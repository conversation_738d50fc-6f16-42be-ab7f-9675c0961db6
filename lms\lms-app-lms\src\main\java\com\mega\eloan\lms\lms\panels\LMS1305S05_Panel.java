package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

public class LMS1305S05_Panel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMS1305S05_Panel(String id) {
		super(id);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new LMS1205S06Panel("lms1205s06panel").processPanelData(model, params);
		new LMSS07Panel02("lmss07panel02").processPanelData(model, params);
		new LMSS07Panel03("lmss07panel03").processPanelData(model, params);
		new LMSS07Panel04("lmss07panel04").processPanelData(model, params);
		new LMSS07Panel05("lmss07panel05").processPanelData(model, params);
		new LMS1305S05Panel01("lms1305s05panel01").processPanelData(model, params);
	}
}
