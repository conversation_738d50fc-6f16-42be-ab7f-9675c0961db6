package com.mega.eloan.lms.base.common;

import java.sql.Types;

public class CMSTypes {

	/**
	 * The type code that identifies the SQL type CHAR.
	 */
	public static final int CHAR = Types.CHAR;

	/**
	 * The type code that identifies the SQL type BOOLEAN.
	 */
	public static final int BOOLEAN = Types.BOOLEAN;

	/**
	 * The type code that identifies the SQL type DATE.
	 */
	public static final int DATE = Types.DATE;

	/**
	 * The type code that identifies the SQL type DECIMAL.
	 */
	public static final int DECIMAL = Types.DECIMAL;

	/**
	 * The type code that identifies the SQL type TIMESTAMP.
	 */
	public static final int TIMESTAMP = Types.TIMESTAMP;

	/**
	 * 日期欄位：格式為 YYYY-MM-DD (西元年)
	 */
	public static final int DATETYPE1 = 910001;

	/**
	 * 日期欄位：格式為 YYYYMMDD (西元年)
	 */
	public static final int DATETYPE2 = 910002;

	/**
	 * 日期欄位：格式為 YYYMMDD (民國年)
	 */
	public static final int DATETYPE3 = 910003;

	/**
	 * 日期欄位：格式為 YYYY/MM/DD (西元年)
	 */
	public static final int DATETYPE4 = 910004;

	/**
	 * 日期欄位：格式為 YYYYMMDDhhmmss
	 */
	public static final int DATETYPE5 = 910005;

	/**
	 * 格式為 YYYY-MM-DD (西元年) 並且不允許null, null轉 0001-01-01
	 */
	public static final int DATETYPE6 = 910006;

	/**
	 * AS400之中文欄位
	 */
	public static final int CHTFIELD = 92000;
}
