

var initDfd = window.initDfd || $.Deferred();
initDfd.done(function(json){
	var ItemGGrid = $("#ItemGGrid").iGrid({
        handler: 'cls1222gridhandler',
        height: 120,
        width: 500,
        autowidth: false,
        action: "queryItemG",
        sortname: "oid",
        sortorder: "asc",
        postData: {
            mainId: responseJSON.mainId
        },
        needPager:false,
        colModel : [ {
			colHeader : i18n.cls1220m04['label.codeValue'],
			name : 'codeValue',
			width : 25,
			sortable : false
        }, {
			colHeader : i18n.cls1220m04['label.codeDesc'],
			name : 'codeDesc',
			width : 100,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['C122M01A.creator'],
			name : 'creator',
			width : 35,
			sortable : false
		}, {
			colHeader : i18n.cls1220m04['C122M01A.createTime'],
			name : 'createTime',
			width : 60,
			sortable : false
		}, {
			name : 'oid',
			hidden : true
		}]				
	});
	
	
	$("input[name=CaseClosedType]").change(function(){	
		var CaseClosedType = $("input[name='CaseClosedType']:radio:checked").val();
		//切換選項時，判斷要不要清空拒絕列表(C122S01G)
		$.ajax({
			type : "POST",
			handler : "cls1220m10formhandler",
			data : {
				formAction : "deleteCaseClosedItemG_fromMainid",
				mainOid : responseJSON.mainOid,
				mainId: responseJSON.mainId,
				CaseClosedType: CaseClosedType
				},
		}).done(function(responseData){
			if(responseData.Success){
				ItemGGrid.trigger("reloadGrid");
			}
		});
		
		//轉換畫面
		if(CaseClosedType == 'G'){ //拒絕
			$("#ItemG").show();
			$("#ItemI").hide();
		}else if(CaseClosedType == 'I'){ //取消
			$("#ItemI").show();
			$("#ItemG").hide();
		}else if(CaseClosedType == 'E'){ //結案
			$("#ItemI").hide();
			$("#ItemG").hide();
		}
	});
	
	$("#addReasonG").click(function(){
		CaseClosedItemG: $('#CaseClosedItemG').val();
		$.ajax({
			type : "POST",
			handler : "cls1220m10formhandler",
			data : {
				formAction : "addCaseClosedItemG",
				mainOid : responseJSON.mainOid,
				mainId: responseJSON.mainId,
				CaseClosedItemG: $('#CaseClosedItemG').val(),
				otherReason: $('#otherReason').val()
				},
			}).done(function(responseData){
				if(responseData.Success){
						ItemGGrid.trigger("reloadGrid");
				//	CommonAPI.triggerOpener("gridview", "reloadGrid");
				//	reLoad(responseData ,json);
					}
			});
	});
	
	$("#deleteReasonG").click(function(){
		var select  = ItemGGrid.getGridParam('selrow');		
		var data = ItemGGrid.getRowData(select);
		
		$.ajax({
			handler : "cls1220m10formhandler",
			type : "POST",
			dataType : "json",
			data : {
				formAction : "deleteCaseClosedItemG",
				oid:data.oid
			},
		}).done(function(obj){
			ItemGGrid.trigger("reloadGrid");
		});
	});
	
	$("select#CaseClosedItemG").change(function(){
		var ItemG = $("select#CaseClosedItemG").val();
		if(ItemG == 'G99'){ //其他
			$('#otherReasonDiv').show();
		}else{
			$('#otherReason').val('');
			$('#otherReasonDiv').hide();
		}
	});
	
	
	//畫面初始化
	$('select#CaseClosedItemG').trigger("change");
	var docStstus = json.mainDocStatus;
	if(docStstus != "C02"){
		$("#CaseClosedTypeE").hide();
	}
	
	
	
	
});

	






