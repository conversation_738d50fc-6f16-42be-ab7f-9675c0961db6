package com.mega.eloan.lms.rpt.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.eloandb.service.LmsCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.rpt.service.CLS180R70Service;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 免計入銀行法72-2限額控管之廠房貸款案件追蹤表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class CLS180R70ServiceImpl extends AbstractCapService implements CLS180R70Service {

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	
	@Resource
	LmsCustdataService lmsCustdataService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Override
	public Map<String, Integer> getTitleMap(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R70.plantLoanTrackingReportOfNotIn722Limitation", 10);//免計入銀行法72-2限額控管之廠房貸款案件追蹤表
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getHeaderMap(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R70.title.01", 10);//分行代碼
		titleMap.put("CLS180R70.title.02", 10);//分行名稱
		titleMap.put("CLS180R70.title.03", 15);//分行承辦人(行員編號)
		titleMap.put("CLS180R70.title.04", 15);//借款人姓名
		titleMap.put("CLS180R70.title.05", 20);//借款人統一編號
		titleMap.put("CLS180R70.title.06", 35);//貸後追蹤管理之最近一期廠房貸款追蹤說明
		titleMap.put("CLS180R70.title.07", 20);//額度序號
		titleMap.put("CLS180R70.title.08", 15);//授信起日
		titleMap.put("CLS180R70.title.09", 15);//授信迄日
		titleMap.put("CLS180R70.title.10", 20);//建築物興建購置情形
		titleMap.put("CLS180R70.title.11", 30);//建築完成年月/所有權取得日
		titleMap.put("CLS180R70.title.13", 30);//預計完工年月/預計取得所有權日
		titleMap.put("CLS180R70.title.12", 15);//建築物座落地
		return titleMap;
	}
	
	@Override
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 20);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String title : titleMap.keySet()){
			this.mergeFieldWithAutoSize(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty(title), cellFormat);
		}
	}
	
	@Override
	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String header : headerMap.keySet()){
			this.mergeFieldAndSetWidth(sheet, colIndex, colIndex++, rowIndex, rowIndex+1, prop.getProperty(header), cellFormat, headerMap.get(header));
		}
	}

	private void mergeFieldAndSetWidth(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat, int width) throws RowsExceededException, WriteException{
		sheet.setColumnView(fromColIndex, width);
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
	
	private void mergeFieldWithAutoSize(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
	
	@Override
	public List<Map<String, Object>> processData(List<Map<String, Object>> elf515List){
		
		Map<String, List<Map<String, Object>>> elf515Map = new LinkedHashMap<String, List<Map<String, Object>>>();
		for(Map<String, Object> m : elf515List){
			
			String cntrNo = String.valueOf(m.get("ELF515_CNTRNO"));
			List<Map<String, Object>> list = elf515Map.get(cntrNo);
			
			if(list == null){
				list = new ArrayList<Map<String, Object>>();
			}
			
			list.add(m);
			elf515Map.put(cntrNo, list);
		}
		
		List<Map<String, Object>> matchDataList = new ArrayList<Map<String, Object>>();
		Date toDate = CapDate.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));
		Map<String, String> areaNameMap = this.eloandbcmsBASEService.getAllCollateralAreaName();
		Map<String, String> estateStateMap = this.codeTypeService.findByCodeType("estateState");
		
		for(String cntrNo : elf515Map.keySet()){
			
			Map<String, Object> m = this.processDataForCollateralCustIdWithBorrowerCustId(elf515Map.get(cntrNo));

			int estateStatus = Integer.parseInt((String)m.get("ELF515_BUILD_STATE"));

			Object objDate = null;
			String bldgfym_or_owndt = null;
			
			// 興建-規劃中、興建-興建中、興建-已完工
			if(estateStatus == 1 || estateStatus == 2 || estateStatus == 3){
				
				// 建築完成年月-年月
				if(m.get("ELF349_BLDGFYM") != null && StringUtils.isNotBlank((String)m.get("ELF349_BLDGFYM"))){
					bldgfym_or_owndt = (String)m.get("ELF349_BLDGFYM") + "01";
				}
			}
			
			// J-112-0415 規劃中、興建中、已完工，案件建築完成年月早於(或稱小於)授信起日之案件，不出表
			if(bldgfym_or_owndt != null){
				Date bldgfym_or_owndt_date = CapDate.parseDate(CapDate.formatDateFromF1ToF2(bldgfym_or_owndt, "YYYMMDD", "yyyy-MM-dd"));
				
				Object lnf022DuringBg = m.get("LNF022_DURING_BG");
				if(lnf022DuringBg == null){
					continue;
				}
				
				Date duringBgDate = (Date)lnf022DuringBg;
				if((bldgfym_or_owndt_date).compareTo(duringBgDate) < 0){
					continue;
				}
			}
			
			// 購置廠房
			if(estateStatus == 4){
				
				//所有權取得日
				if(m.get("ELF349_OWNDT") != null && StringUtils.isNotBlank((String)m.get("ELF349_OWNDT"))){
					bldgfym_or_owndt = (String)m.get("ELF349_OWNDT");
				}
			}
			
			if(bldgfym_or_owndt != null){
				bldgfym_or_owndt = CapDate.formatDateFromF1ToF2(bldgfym_or_owndt, "YYYMMDD", "yyyy-MM-dd");
				objDate = CapDate.parseSQLDate(bldgfym_or_owndt);
			}
			
			// 預計/完工年月日 or 預計取得所有權日
			if(objDate == null){
				objDate = m.get("ELF515_BUILD_DATE");
			}
			
			if(objDate == null){
				matchDataList.add(m);
			}
			else{
				
				Date compareDate = (Date)objDate;
				compareDate = CapDate.addMonth(compareDate, 4);
				if(compareDate.compareTo(toDate) <= 0){
					String custIdDupNo = String.valueOf(m.get("LNF020_CUST_ID"));
					String dupNo = custIdDupNo.substring(10, 11);
					String custId = custIdDupNo.substring(0, 10);
					Map<String, Object> custData = this.lmsCustdataService.findCustDataCname(custId, dupNo);
					String brNo = String.valueOf(m.get("LNF020_LN_BR_NO"));
					String branchName = this.branchService.getBranchName(brNo);
					m.put("BRANCH_NAME", branchName);
					m.put("CUST_NAME", custData != null ? custData.get("CNAME") : "");
					m.put("AREA_NAME", areaNameMap.get(m.get("ELF515_SITE2")));
					m.put("ESTATE_STATE_NAME", estateStateMap.get(m.get("ELF515_BUILD_STATE")));
					m.put("BLDGFYM_OR_OWNDT", bldgfym_or_owndt);
					Map<String, Object> clerkMap = this.misdbBASEService.getLatestChargeClerkOfSigningBook(String.valueOf(m.get("ELF515_CNTRNO")), brNo);
					if(clerkMap != null){
						m.putAll(clerkMap);
					}
					matchDataList.add(m);
				}
			}
		}

		return matchDataList;
	}
	
	@Override
	public void setBodyContent(WritableSheet sheet, List<Map<String, Object>> data, int colIndex, int rowIndex) throws RowsExceededException, WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		WritableCellFormat cellFormat_AlignLeft = new WritableCellFormat(font);
		{
			cellFormat_AlignLeft.setWrap(true);
			cellFormat_AlignLeft.setAlignment(Alignment.LEFT);
			cellFormat_AlignLeft.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat_AlignLeft.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(Map<String, Object> map : data){
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LNF020_LN_BR_NO")), cellFormat));///分行代碼
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRANCH_NAME")), cellFormat));//分行名稱
			String charger = CapString.trimNull(map.get("MISSTAFF_CNAME")) + "(" + CapString.trimNull(map.get("ELF447_EMP_NO")) + ")";
			sheet.addCell(new Label(colIndex++, rowIndex, charger, cellFormat));//分行承辦人
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CUST_NAME")), cellFormat));//借款人姓名
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LNF020_CUST_ID")), cellFormat));//借款人統一編號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF602_FO_MEMO")), cellFormat_AlignLeft));//貸後追蹤管理之最近一期廠房貸款追蹤說明
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF515_CNTRNO")), cellFormat));//額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LNF022_DURING_BG")), cellFormat));//授信起日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("LNF022_DURING_ED")), cellFormat));//授信迄日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ESTATE_STATE_NAME")), cellFormat));//建築物興建購置情形
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BLDGFYM_OR_OWNDT")), cellFormat));//建築完成年月/所有權取得日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("ELF515_BUILD_DATE")), cellFormat));//預計完工年月/預計取得所有權日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("AREA_NAME")), cellFormat));//建築物座落地
			rowIndex++;
			colIndex = 0;
		}
	}
	
	private Map<String, Object> processDataForCollateralCustIdWithBorrowerCustId(List<Map<String, Object>> list){
		
		for(Map<String, Object> map : list){
			
			String lnf020CustId = String.valueOf(map.get("LNF020_CUST_ID"));
			String collCustId = String.valueOf(map.get("ELF346_CUSTID"));
			String collDupNo = String.valueOf(map.get("ELF346_DUPNO"));
			String collCustIdAndDupNo = StringUtils.rightPad(collCustId, 10, " ") + collDupNo;
			
			// 先比對擔保品額度序號檔id = 借款人id
			if(collCustIdAndDupNo.equals(lnf020CustId)){
				return map;
			}
		}
		
		// 如果比對無相同id, 抓建築完成年月最新一筆資料(第一筆資料) ORDER BY value(g.ELF349_BLDGFYM,'0001-01-01') desc, value(g.ELF349_OWNDT,'0001-01-01') desc) RANK
		return list.get(0);
	}
	
}
