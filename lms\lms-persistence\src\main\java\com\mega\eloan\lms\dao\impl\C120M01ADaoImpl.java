/* 
 * C120M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120M01A;

/** 個金徵信借款人主檔 **/
@Repository
public class C120M01ADaoImpl extends LMSJpaDao<C120M01A, String> implements
		C120M01ADao {

	@Override
	public C120M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C120M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C120M01A> findByMainIdForOrder(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("keyMan", true);
		search.addOrderBy("custShowSeqNum", false);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		List<C120M01A> list = createQuery(C120M01A.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<C120M01A> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C120M01A> list = createQuery(C120M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public C120M01A findC120M01A_o_single_Y(String ownBrId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "o_single", "Y");
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}
	
	@Override
	public C120M01A findByUniqueKey(String mainId, String ownBrId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C120M01A> findByIndex01(String mainId, String ownBrId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<C120M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C120M01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<C120M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C120M01A> findByMainIdOrderByCustId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("custShowSeqNum", false);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		List<C120M01A> list = createQuery(C120M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public C120M01A findByUniqueKey(String mainId, String custId, String dupNo) {
		return this.findByUniqueKey(mainId, null, custId, dupNo);
	}

	@Override
	public C120M01A findByMainIdAndKeyMan(String mainId, String keyMan) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyMan", keyMan);
		// 檢查是否有查詢參數

		return findUniqueOrNone(search);
	}

	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120M01A.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
	
	@Override
	public int deleteByMainId(String mainId) {
		Query query = entityManager.createNamedQuery("c120m01a.deleteByMainId");
		query.setParameter(1, mainId);
		return query.executeUpdate();
	}
	
	@Override
	public List<C120M01A> findByMainId_orderBy_keymanCustposCustid(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("keyMan", true);
		search.addOrderBy("custShowSeqNum", false);
		search.addOrderBy("custPos", false);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120M01A> list = createQuery(C120M01A.class, search)
				.getResultList();
		return list;		
	}
	
	@Override
	public List<C120M01A> findByCaseId(String caseId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "caseId", caseId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120M01A> list = createQuery(C120M01A.class, search)
				.getResultList();
		return list;
	}
}