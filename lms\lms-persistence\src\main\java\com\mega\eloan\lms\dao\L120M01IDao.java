/* 
 * L120M01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01I;

/** 簽報書主檔資料檔 **/
public interface L120M01IDao extends IGenericDao<L120M01I> {

	L120M01I findByOid(String oid);
	
	List<L120M01I> findByMainId(String mainId);
	
	L120M01I findByUniqueKey(String mainId);

	List<L120M01I> findByIndex01(String mainId);
}