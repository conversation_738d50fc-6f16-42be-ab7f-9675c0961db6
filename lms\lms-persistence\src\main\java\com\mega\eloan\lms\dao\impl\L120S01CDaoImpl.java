/* 
 * L120S01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S01C;


/** 企金信用評等資料檔 **/
@Repository
public class L120S01CDaoImpl extends LMSJpaDao<L120S01C, String> implements
		L120S01CDao {

	@Override
	public L120S01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S01C> list = createQuery(L120S01C.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L120S01C> findByCustId(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		List<L120S01C> list = createQuery(L120S01C.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public List<L120S01C> findByCustIdOrderByCrdTYear(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addOrderBy("crdTYear", true);
		List<L120S01C> list = createQuery(L120S01C.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L120S01C findByUniqueKey(String mainId, String custId, String dupNo,
			Date crdTYear, String crdTBR, String crdType, String finYear,
			String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "crdTYear", crdTYear);
		search.addSearchModeParameters(SearchMode.EQUALS, "crdTBR", crdTBR);
		search.addSearchModeParameters(SearchMode.EQUALS, "crdType", crdType);
		search.addSearchModeParameters(SearchMode.EQUALS, "finYear", finYear);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S01C> findByIndex01(String mainId, String custId,
			String dupNo, Date crdTYear, String crdTBR, String crdType,
			String finYear, String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L120S01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (crdTYear != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "crdTYear",
					crdTYear);
		if (crdTBR != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "crdTBR", crdTBR);
		if (crdType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "crdType",
					crdType);
		if (finYear != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "finYear",
					finYear);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120S01C.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S01C> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L120S01C> list = createQuery(L120S01C.class, search)
				.getResultList();

		return list;
	}
	@Override
	public List<L120S01C> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S01C> list = createQuery(L120S01C.class,search).getResultList();
		return list;
	}
	@Override
	public int delModel(String mainId) {
		Query query = getEntityManager().createNamedQuery("L120S01C.delModel");
		query.setParameter("MAINID", mainId); // 設置參數
		return query.executeUpdate();
	}
}