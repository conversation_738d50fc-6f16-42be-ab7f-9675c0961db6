package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF506;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import tw.com.jcs.common.Util;

@Service
public class MisELF506ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF506Service {

	@Override
	public ELF506 findByCntrNo(String cntrNo) {
		Map<String, Object> rowData = getByCntrNo(cntrNo);
		if (rowData == null) {
			return null;
		} else {
			ELF506 model = new ELF506();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}

	@Override
	public Map<String, Object> getByCntrNo(String cntrNo) {

		return this.getJdbc().queryForMap("MIS.ELF506.findByCntrNo",
				new String[] { cntrNo });
	}

	@Override
	public List<Map<String, Object>> getByCustId(String custId, String dupNo) {
		return this.getJdbc().queryForListWithMax("MIS.ELF506.findByCustId",
				new String[] { Util.addSpaceWithValue(custId, 10) + dupNo });
	}

	@Override
	public List<Map<String, Object>> getByAdcCaseNo(String adcCaseNo) {
		return this.getJdbc().queryForListWithMax("MIS.ELF506.findByAdcCaseNo",
				new String[] { adcCaseNo });
	}

	@Override
	public List<Map<String, Object>> getExceptIsEmpty(String modifyTime) {
		return this.getJdbc().queryForList("MIS.ELF506.findExceptIsEmpty",
				new String[] { modifyTime }, 0, Integer.MAX_VALUE);
	}

	@Override
	public int deleteByCntrNo(String cntrNo) {
		return this.getJdbc().update("MIS.ELF506.deleteByCntrNo",
				new String[] { cntrNo });
	}

	@Override
	public void insert(String cntrNo, String cnLoanFg, String directFg,
			String stRadeFg, BigDecimal guar1rate, BigDecimal guar2rate,
			BigDecimal guar3rate, BigDecimal coll1rate, BigDecimal coll2rate,
			BigDecimal coll3rate, BigDecimal coll4rate, BigDecimal coll5rate,
			Timestamp modifyTime, Timestamp createTime, String createUnit,
			String modifyUnit, String documentNo, String iGolFlag,
			String cnTMUFg, String cnBusKind, String custId, String is722Flag,
			String modUnit, String docNo, Timestamp modTime, String sDate,
			String isBuy, String exItem, String loanTarget, String isType,
			String grntType, String grntClass, String othCrdType,
			String unionArea3, String nCnSblcFg, String isInstalment,
            String prodKind, String adcCaseNo, String exceptFlag, 
			String exceptFlagQAisY, String exceptFlagQAPlus) {

		this.getJdbc().update(
				"MIS.ELF506.insert",
				new Object[] { cntrNo, cnLoanFg, directFg, stRadeFg, guar1rate,
						guar2rate, guar3rate, coll1rate, coll2rate, coll3rate,
						coll4rate, coll5rate, modifyTime, createTime,
						createUnit, modifyUnit, documentNo, iGolFlag, cnTMUFg,
						cnBusKind, custId, is722Flag, modUnit, docNo, modTime,
						sDate, isBuy, exItem, loanTarget, isType, grntType,
						grntClass, othCrdType, unionArea3, nCnSblcFg,
						isInstalment, prodKind, adcCaseNo, exceptFlag, 
						exceptFlagQAisY, exceptFlagQAPlus });
	}

	@Override
	public void delete(String cntrNo) {
		this.getJdbc().update("delete from mis.elf506 where elf506_cntrno = ?",
				new String[] { cntrNo });
	}

	@Override
	public List<Map<String, Object>> findLoanTargetISEmpty() {
		return this.getJdbc().queryForList("MIS.ELF506.findLoanTargetISEmpty",
				new String[] {}, 0, Integer.MAX_VALUE);
	}

	@Override
	public void updateLoanTargetByCntrNo(String loanTarget, String cntrNo) {
		this.getJdbc().update("ELF506.updateLoanTarget",
				new Object[] { loanTarget, cntrNo });
	}

	@Override
	public void updateCntrNoByCntrNo(String newCntrNo, String oldCntrNo,
			String modifyUnit) {
		this.getJdbc().update("ELF506.updateCntrNoByCntrNo",
				new Object[] { newCntrNo, modifyUnit, oldCntrNo });
	}

	@Override
	public void updateExcpetByCntrNo(String except, String exceptQAIsY,
			String exceptQAPlus, String cntrNo) {
		this.getJdbc().update("MIS.ELF506.updateExcpetByCntrNo",
				new Object[] { except, exceptQAIsY, exceptQAPlus, cntrNo });
	}

	@Override
	public void update72_2markFromDW(String cntrNo, String dw_FLAG_722,
			String dw_IS_BUY_722, String dw_EX_ITEM_722) {
		this.getJdbc().update(
				"ELF506.updateFromDw",
				new Object[] { dw_FLAG_722, dw_IS_BUY_722, dw_EX_ITEM_722,
						cntrNo });

	}

}
