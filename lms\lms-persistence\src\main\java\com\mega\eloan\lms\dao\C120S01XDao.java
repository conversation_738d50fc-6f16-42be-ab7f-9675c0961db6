/* 
 * C120S01XDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01X;

/** 勞工紓困4.0簡易信用評分檔 **/
public interface C120S01XDao extends IGenericDao<C120S01X> {

	C120S01X findByOid(String oid);
	
	List<C120S01X> findByMainId(String mainId);
	
	List<C120S01X> findByIndex01(String mainId, String custId);

	List<C120S01X> findByMainIdCustIdDupNo(String mainId, String custId, String dupNo);
	C120S01X findByUniqueKey(String mainId, String custId, String dupNo);
	
	int deleteByOid(String oid);
}