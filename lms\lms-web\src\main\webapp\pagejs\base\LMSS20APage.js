//J-106-0029-001 新增洗錢防制頁籤
var initS20aJson = {
	handlerName : null,
	lockEdit : false,
	// 設定handler名稱
	setHandler : function(){
		
		this.handlerName = "amlrelateformhandler";
//		if(responseJSON.docURL == "/lms/lms1201m01"){
//			// 授權外企金
//			this.handlerName = "lms1201formhandler";
//		}else if(responseJSON.docURL == "/lms/lms1101m01"){
//			// 授權內企金
//			this.handlerName = "lms1101formhandler";
//		}else if(responseJSON.docURL == "/lms/lms1211m01"){
//			// 授權外個金
//			this.handlerName = "lms1211formhandler";
//		}else if(responseJSON.docURL == "/lms/lms1111m01"){
//			this.handlerName = "lms1111formhandler";
//		}else{
//			this.handlerName = "lms1301formhandler";
//		}		
	},
	// 設定Grid
	blackListGridView : function(fileGridId, fieldId){
		var gridBlackList = $("#gridview_blackList")
			.iGrid({
				needPager: false,
				handler : 'lms1201gridhandler',
				// height: 345, //for 15 筆
				height : "300px", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : {
					formAction : "queryL120s09a",
					mainId : responseJSON.mainid
					//rowNum:30
				},
				//rowNum:30,
				caption: "&nbsp;",
				hiddengrid : false,
				sortname : 'seqNum|checkSeq|custRelation|custId|dupNo|custEName|custName',   //J-107-0070-001  Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
				sortorder:'asc|asc|asc|asc|asc|asc',
				//sortname : 'custRelation',
				multiselect: true,
				//rownumbers : true,
				hideMultiselect:false,
				// autofit: false,
				autowidth : true,
		        loadComplete: function(){
		            //J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
					$.ajax({
			            type: "POST",
			            handler: initS20aJson.handlerName,
						data : {
							formAction : "initForShow",
							mainId : responseJSON.mainid
						},
			            success: function(responseData){
			            	if(responseData.show0024AmlStatus != "Y"){
			            		$("#gridview_blackList").hideCol('cm1AmlStatus');
			            	}
			            }
			        });  
				   
				   //***************** 
		        },
				colModel : [{
					 colHeader: i18n.lmss20a["L120S09a.checkSeq"],//"序號 Check Seq",
		        	 name: 'checkSeq',
		             width: 3,
		             sortable: false,
					 align:"center"
				},{
		        	 
					 colHeader: i18n.lmss20a["L120S09a.blackListCode"],//"查詢結果",
		        	 name: 'blackListCode',
		             width: 5,
		             sortable: false,
					 align:"center"
				},{
					colHeader : i18n.lmss20a["L120S09a.memo"],//"命中代碼", 
					name : 'memo',
					width : 5,
					sortable : false,
					align : 'left'	 
				},{
					colHeader : i18n.lmss20a["L120S09a.cm1AmlStatus"],//"0024註記", 
					name : 'cm1AmlStatus',
					width : 5,
					sortable : false,
					align : 'center'	
				},{
					//J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
					colHeader : i18n.lmss20a["L120S09a.luvRiskLevel"],//"風險等級",   
					name : 'luvRiskLevel',
					width : 4,
					sortable : false,
					align : 'center'		
				
				},{
					colHeader : i18n.lmss20a["L120S09a.custName"],//"戶名",
					width : 13,
					name : 'custName',
					sortable : false,
					align : 'left',
					formatter : 'click',
					onclick : openDocBL
				},{
					colHeader : i18n.lmss20a["L120S09a.custEName"],//"英文戶名",
					width : 13,
					name : 'custEName',
					align : 'left'
				},{
					colHeader : i18n.lmss20a["L120S09a.custRelation"],//"與本案關係",
					name : 'custRelationIndex',
					width : 14,
					sortable : false,
					align : "left"
				},{
					//J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
					colHeader : i18n.lmss20a["L120S09a.country"],//國別
					name : 'country',
					width : 4,
					sortable : false,
					align : "center"		
				},{
					colHeader : i18n.lmss20a["L120S09a.custId"],//"統編", 
					name : 'custId',
					width : 7,
					sortable : false,
					align : 'left'
		        },{
		        	colHeader : i18n.lmss20a["L120S09a.cmfwarnpResult"],//"受告誡處分",
					name : 'cmfwarnpResult',
					width : 4,
					sortable : false,
					align : 'center'
		        },{
                    colHeader : i18n.lmss20a["L120S09a.passPeps"],
                    name : 'passPeps',
                    width : 3,
                    sortable : false,
                    align : 'center',
                    formatter: function(cellvalue, options, rowObject){
                        if(cellvalue == 'Y'){
                            return "*";
                        } else {
                            return "";
                        }
                    }
                },{
					name : 'oid',
					hidden : true
				},{
					name : 'seqNum',
					hidden : true	
				} ],
				ondblClickRow : function(rowid) {
					var data = gridBlackList.getRowData(rowid);
					openDocBL(null, null, data);
				}
			});
	},
	amlStateCheckList : function(){
		var gridAmlStateList = $("#gridview_amlStateCheckList")
			.iGrid({
				needPager: false,
				handler : 'lms1201gridhandler',
				height : "300px", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : {
					formAction : "queryL120s09c",
					mainId : responseJSON.mainid
					//rowNum:30
				},
				caption: "&nbsp;",
				hiddengrid : false,
				multiselect: true,
				hideMultiselect:false,
				autowidth : true,
				colModel : [{
					 colHeader: i18n.lmss20a["L120S09a.stateCustName"],//"客戶名稱",
		        	 name: 'custName',
		             width: 3,
		             sortable: false,
					 align:"center",
					 formatter : 'click',
					 onclick : openDocSC
				},{
					 colHeader: i18n.lmss20a["L120S09a.stateCustId"],//"身分證字號/統一編號",
		        	 name: 'custId',
		             width: 5,
		             sortable: false,
					 align:"center"
				},{
					colHeader : i18n.lmss20a["L120S09a.stateCaseNo"],//"交易編號", 
					name : 'caseNo',
					width : 5,
					sortable : false,
					align : 'left'	 
				},{
					colHeader : i18n.lmss20a["L120S09a.stateCaseDate"],//"交易日期", 
					name : 'caseDate',
					width : 3,
					sortable : false,
					align : 'center'	
				},{
					colHeader : i18n.lmss20a["L120S09a.stateCaseType"],//"檢視時點", 
					name : 'caseType',
					width : 3,
					sortable : false,
					align : 'center'
				},{
					colHeader : i18n.lmss20a["L120S09a.stateVersionDate"],//"版本日期", 
					name : 'versionDate',
					width : 3,
					sortable : false,
					align : 'center'
				},{
					name : 'oid',
					hidden : true	
				} ],
				ondblClickRow : function(rowid) {
					var data = gridAmlStateList.getRowData(rowid);
					openDocSC(null, null, data);
				}
			});
	},
	initForShow : function(){
		//J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」	
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "initForShow",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				if(initS20aJson.checkAmlReadonly()){
					//設定唯讀
					$("#LMS1205S20Form01").readOnlyChilds(true);
					$("#LMS1205S20Form01").find("button").hide();
					$("#LMS1205S20Form02").find("button").hide();// J-107-0226
					$(".oldAmlButtons").hide();
					$(".newAmlButtons").hide();
					if(json.callNewFunc == "Y"){
						$(".newAmlField").show();
					}else{
						$(".newAmlField").hide();
					}
					initS20aJson.lockEdit = true;
				}else{
					if(json.callNewFunc == "Y"){
						$(".newAmlButtons").show();
						$(".newAmlField").show();
						$(".oldAmlButtons").hide();

						if(json.lockEdit=="Y"){
							initS20aJson.lockEdit = true;
							$(".canSendSas").hide();
							$("#blackListDetail").readOnlyChilds(true);
							$("#blackListDetail").find("button").hide();
							$(".notSendSas").show();
						}else{
							initS20aJson.lockEdit = false;
							$(".canSendSas").show();
							$("#blackListDetail").readOnlyChilds(false);
							$("#blackListDetail").find("button").show();
							$(".notSendSas").hide();
						}
	
						if(json.show0024AmlStatus == "Y"){
							$(".show0024AmlStatus").show();
						}else{
							$(".show0024AmlStatus").hide();
						}

						if( json.ncResult || json.uniqueKey ){
							$("#btnCheckAmlResult").show();
						}else{
							//沒有NC_RESULT 或  uniqueKey 代表還沒送呈過(或明細有異動需要重新掃描)，所以按鈕  取得黑名單查詢結果  隱藏
							$("#btnCheckAmlResult").hide();
						}

						$("#LMS1205S20Form01").find("#blackListQDate").val(json.blackListQDate);  //<--callNewFunc = Y 才會塞，callNewFunc = N  從簽報書頁籤的JAVA 來
						$("#LMS1205S20Form01").find("#ncResult").val(json.ncResult);
						$("#LMS1205S20Form01").find("#refNo").val(json.refNo);
						$("#LMS1205S20Form01").find("#uniqueKey").val(json.uniqueKey);
						$("#LMS1205S20Form01").find("#ncCaseId").val(json.ncCaseId);
						$("#LMS1205S20Form01").find("#sCaseBrId").val(json.sCaseBrId);
						$("#LMS1205S20Form01").find("#sQueryUser").val(json.sQueryUser);
						$("#LMS1205S20Form01").find("#ncResultRemark").val(json.ncResultRemark);
						$("#LMS1205S20Form01").find("#highRiskRemark").val(json.highRiskRemark);
						
						if(userInfo.ssoUnitNo == "900"){
							$(".showFor900").show();
						}else{
							$(".showFor900").hide();
						}
						
					}else{
						$(".newAmlButtons").hide();
						$(".newAmlField").hide();
						$(".oldAmlButtons").show();
					}
				}
				
				if(json.showQueryInfo == "Y"){
					$("#LMS1205S20Form01").find("#QueryInfo").show();
				}else{
					$("#LMS1205S20Form01").find("#QueryInfo").hide();
				}

				// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」欄位
				// 把判斷都交給後端決定
				if(json.showNcResultRemark && json.showNcResultRemark == "Y"){
					$("#LMS1205S20Form01").find("#ncResultRemarkDiv").show();
				}else{
					$("#LMS1205S20Form01").find("#ncResultRemarkDiv").hide();
				}
				if(json.showHighRiskRemark && json.showHighRiskRemark == "Y"){
					$("#LMS1205S20Form01").find("#highRiskRemarkDiv").show();
				}else{
					$("#LMS1205S20Form01").find("#highRiskRemarkDiv").hide();
				}
				
				// J-112-0534  因應兆豐金控自113.1.1下架證券違約交割/上市櫃觀察名單，改引入T70
				// 僅限國內企金簽報書有這功能
				if(json.showImportT70 && json.showImportT70 == "Y"){
					$("#importT70Button").show();
				}else{
					$("#importT70Button").hide();
				}
				
				$("#LMS1205S20Form01").find("#gridview_blackList").trigger("reloadGrid");
  
			}
		});				
	},checkAmlReadonly : function(){ 
		var auth = (responseJSON ? responseJSON.Auth : {}); //權限
		var b1 = auth.readOnl;
		var b2= false;
		if (responseJSON.readOnly != undefined &&
	        responseJSON.readOnly != null &&
	        responseJSON.readOnly != '') {
	            if (responseJSON.readOnly.toString() == "true") {
	            	b2 = true;
	            }
        }
		var b3 = ( responseJSON.mainDocStatus != "01O" && responseJSON.mainDocStatus != "07O" && responseJSON.mainDocStatus != "0EO" && responseJSON.mainDocStatus != "01K");
		
		if (b1) {
	    	return true ;
		}
		
		if (b2) {
	    	return true ;
		}
		
		if (b3) {
	    	return true ;
		}
		 
	    return false ;
	},initAmlQueryRole : function(){
		$.ajax({
	        type: "POST",
	        handler: initS20aJson.handlerName,
	        dataType : "json",
	        data: {
	            formAction: "allBranch"
	        },
	        success: function(responseData){
	            var json = {
	                format: "{value} - {key}",
	                item: responseData
	            };
	            $("#caseBrId").setItems(json);

	            $.ajax({
	                type: "POST",
	                handler: initS20aJson.handlerName,
	                dataType : "json",
	                data: {
	                    formAction: "allUserByBranch",
	                    qryBranch: $("#caseBrId").val()
	                },
	                success: function(responseData){
	                    var json = {
	                        format: "{value} - {key}",
	                        item: responseData
	                    };
	                    $("#queryUser").setItems(json);
	                    
	                    $("#caseBrId").val(userInfo.unitNo);
	                    $("#queryUser").val(userInfo.userId);
	                }
	            });
	        }
	    });
	},showAmlStateCheck : function(){
		//J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
		//國內才有這功能，若為海外時需隱藏區塊
		$.ajax({
	        type: "POST",
	        handler: initS20aJson.handlerName,
	        dataType : "json",
	        data: {
	            formAction: "showAmlStateCheck"
	        },
	        success: function(responseData){
	        	if(responseData.showAmlState == true){
	        		$('#amlStateCheckDiv').show();
	        	}else{
	        		$('#amlStateCheckDiv').hide();
	        	}
	        	
	        	// 這邊純粹只控制要不要顯示新增按鈕，和要不要顯示最新版的radio
	        	// 移除新增按鈕
	        	if(responseData.removeAddButton == true){
	        		$('#l120s09cAddButton').hide();
	        	}
	        	
	        	// 關閉舊版11307態樣檢核表
	        	if(responseData.remove11311 == true){
	        		$('#l120s09c_11311_label').hide();
	        	}
	        	
	        	if(responseData.remove11311 != true && responseData.removeAddButton != true){
	        		// 如果11311版本已經可以使用了，也開放新增了，提示訊息就可以刪掉了
	        		$('#l120s09c_11311_text').hide();
	        	}
	        }
	    });
	},showAmlStateChoose : function(){
		//J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	    var L120S01aGrid = $("#amlCheckL120s01agrid").iGrid({ // 選借款人GridView
	        handler: 'lms1201gridhandler',
	        height: 200,
			multiselect: true,
	        postData: {
	            formAction: "showAmlStateChoose"
	        },
	        colModel: [{
	            colHeader: i18n.lmss20a["L120S09a.stateChooseCustId"], // 身分證字號/統一編號
	            align: "left",
	            width: 100, // 設定寬度
	            sortable: false, // 是否允許排序
	            name: 'custId' // col.id
	        }, {
	            colHeader: i18n.lmss20a["L120S09a.stateChooseDupNo"], // 重複序號
	            align: "left",
	            width: 40, // 設定寬度
	            sortable: false, // 是否允許排序
	            name: 'dupNo' // col.id
	        }, {
	            colHeader: i18n.lmss20a["L120S09a.stateChooseCustName"], // 客戶名稱
	            align: "left",
	            width: 100, // 設定寬度
	            sortable: false, // 是否允許排序
	            name: 'custName' // col.id
	        }]
	    });
		
	}
	
};

/**
 * 開起查詢名單以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDocBL(cellvalue, options, rowObject) {
	ilog.debug(rowObject);
	$("#tLMS1205S20Form01").reset();
	//J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能
//	var item = API.loadOrderCombosAsList("BlackListRelation")["BlackListRelation"];
//	$("#custRelation").setItems({
//		size: "2",
//        item: item,
//		clear : true,
//		itemType: 'checkbox' 
//    });
	
	
	$.ajax({
		handler : initS20aJson.handlerName,
		type : "POST",
		dataType : "json",
		action : "queryL120s09a",
		data : {
			oid : rowObject.oid
		},
		success : function(obj) {
			var $tLMS1205S20Form01 = $("#tLMS1205S20Form01");
			for(o in obj.tLMS1205S20Form01.custRelation){
				$tLMS1205S20Form01.find("[name=custRelation]").each(function(i){
					var $this = $(this);
					if($this.val() == obj.tLMS1205S20Form01.custRelation[o]){
						$this.attr("checked",true);
					}
				});
			}

		    var codeTypeMap = API.loadCombos(["BlackListCode"]);
		    var itemObj = codeTypeMap.BlackListCode;
			$tLMS1205S20Form01.find("#blackListCode").setItems({
		        item: itemObj,
		        space: true,
		        format: "{value} - {key}"
		    });		
	
			$tLMS1205S20Form01.setData(obj.tLMS1205S20Form01,false);
			
			// J-112-0534  因應兆豐金控自113.1.1下架證券違約交割/上市櫃觀察名單，改引入T70
			// 僅限國內企金簽報書有這功能
			if(obj.tLMS1205S20Form01.showT70 && obj.tLMS1205S20Form01.showT70=="Y"){
				$tLMS1205S20Form01.find("#t70Detail").show();
				$("#T70Status").attr("disabled","disabled");
				if(obj.tLMS1205S20Form01.T70Docfileoid == undefined || obj.tLMS1205S20Form01.T70Docfileoid==""){
					$("#printT70Button").hide();
				}else{
					$("#printT70Button").show();
				}
			}else{
				$tLMS1205S20Form01.find("#t70Detail").hide();
			}
			
			l120s09aThick(obj.tLMS1205S20Form01.oid);
		}
	});	
}

/**
 * 登錄洗錢防制名單ThickBox
 */
function l120s09aThick(oid) {
	
	//J-106-0238-001
	thickboxOptions.readOnly =initS20aJson.lockEdit;
	$("#blackListCode").attr("disabled","disabled");
	if(oid == ""){//新增時一律隱藏T70區塊
		$("#tLMS1205S20Form01").find("#t70Detail").hide();
	}
	$("#blackListDetail").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss20a["L120S09a.inputBlacklist"],//"登錄洗錢防制名單",
		width : 960,
		height : 500,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"saveData" : function() {
				
				//J-107-0070-001  Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
				//L120S09a.message22=儲存後，所有名單需重新執行「傳送名單掃描」。<br>是否確定執行本作業?
				API.confirmMessage(i18n.lmss20a["L120S09a.message22"],function(b){
					if(b){
						var $tLMS1205S20Form01 = $("#tLMS1205S20Form01");
						var $LMS1205S20Form01 = $("#LMS1205S20Form01");
						
						var list = "";
						var sign = ",";
						if($tLMS1205S20Form01.valid()){
							var checkCou = 0;
							$tLMS1205S20Form01.find("[name=custRelation]:checkbox:checked").each(function(i){
								list += ((list == "") ? "" : sign ) + $(this).val();
								checkCou++;
							});
							if(checkCou == 0){
								//"尚未勾選欄位【與本案關係】內容，請勾選"
								CommonAPI.showMessage(i18n.lmss20a["L120S09a.message01"]);
								return;
							}
							
							$.ajax({		//查詢主要借款人資料
								handler : initS20aJson.handlerName,
								type : "POST",
								dataType : "json",
								action : "saveL120s09a",
								data : {
									tLMS1205S20Form01 : JSON.stringify($tLMS1205S20Form01.serializeData()),
									mainId : responseJSON.mainid,
					  				custId : $("#tLMS1205S20Form01").find("#custId").html(),
					  				dupNo : $("#tLMS1205S20Form01").find("#dupNo").html(),
									oid : oid,
									list : list
								},
								success : function(json) {
									oid = json.newOid;
									
									//J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
									initS20aJson.initForShow();
								}
							});
							//$.thickbox.close();					
						}
					}else{
						//否的function
						 
					}
				})	
				
			},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
					if(res){
						$.thickbox.close();
					}
		         });
			}
		}
	});
}

/**
 * 新增查詢名單
 */
function addData() {
	$("#tLMS1205S20Form01").reset();
	
	var $tLMS1205S20Form01 = $("#tLMS1205S20Form01");
	var $LMS1205S20Form01 = $("#LMS1205S20Form01");
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count == 0){
		//"必須先執行【引進查詢名單】才能執行本功能！"
//		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
//		return;
	}

	l120s09aThick("");
}

/**
 * 刪除名單
 */
function deleteList(){
	var rows = $("#gridview_blackList").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_blackList").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		//"尚未選取資料"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message03"]);
		return;
	}	

    //"是否確定要刪除名單"
	API.confirmMessage(i18n.lmss20a["L120S09a.message04"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : initS20aJson.handlerName,
				type : "POST",
				dataType : "json",
				action : "deleteL120s09a",
				data : {
					listOid : list,
					mainId : responseJSON.mainid
				},
				success : function(json) {
					$("#LMS1205S20Form01").find("#gridview_blackList").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert3"]);
		}
	})	
}

/**
 * 引進戶名
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function applyCustName() {
	var $tLMS1205S20Form01 = $("#tLMS1205S20Form01");
	var list = "";
	var sign = ",";
	var checkCou = 0;
	$tLMS1205S20Form01.find("[name=custRelation]:checkbox:checked").each(function(i){
		list += ((list == "") ? "" : sign ) + $(this).val();
		checkCou++;
	});
	if(checkCou == 0){
		//"尚未勾選欄位【與本案關係】內容，請勾選"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message01"]);
		return;
	}
	$.ajax({
		handler : initS20aJson.handlerName,
		type : "POST",
		dataType : "json",
		action : "queryL120s09aCustName",
		data : {
			tLMS1205S20Form01 : JSON.stringify($tLMS1205S20Form01.serializeData()),
			mainId : responseJSON.mainid,
			custId : $("#tLMS1205S20Form01").find("#custId").html(),
			dupNo : $("#tLMS1205S20Form01").find("#dupNo").html(),
			list : list
		},
		success : function(obj) {
			
			if(obj.newCustName){
				$tLMS1205S20Form01.find("#custName").val(obj.newCustName);
			}
			
			if(obj.newCustEName){
				$tLMS1205S20Form01.find("#custEName").val(obj.newCustEName);
			}
			
			if(obj.newCountry){
				$tLMS1205S20Form01.find("#country").val(obj.newCountry);
			}
			
		}
	});	
}

/**
 * 重新引進查詢名單
 */
function importBlackList(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		//"執行引進後會刪除已存在之查詢名單，是否確定執行？"
		CommonAPI.confirmMessage(i18n.lmss20a["L120S09a.message05"], function(b){
			if (b) {					
				//是的function
				$.ajax({
					handler : initS20aJson.handlerName,
					type : "POST",
					dataType : "json",
					data : {
						formAction : "importBlackList",
						mainId : responseJSON.mainid
					},
					success : function(json) {
						$("#LMS1205S20Form01").find("#gridview_blackList").trigger("reloadGrid");
					}
				});				
			}				
		});		
	}else{
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "importBlackList",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				$("#LMS1205S20Form01").find("#gridview_blackList").trigger("reloadGrid");
			}
		});				
	}
}


/**
 * 黑名單查詢
 */
function queryBlackList(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "queryBlackList",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				$("#LMS1205S20Form01").find("#gridview_blackList").trigger("reloadGrid");
				if(json.blackListQDate){
					$("#LMS1205S20Form01").find("#blackListQDate").val(json.blackListQDate);
				}
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}

/**
 * 匯入EXCEL名單
 */
function importByExl() {
	
	//J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能
	
	var $LMS1205S20Form01 = $("#LMS1205S20Form01");
 
//	var item = API.loadOrderCombosAsList("BlackListRelation")["BlackListRelation"];
//	$("#custRelationExl").setItems({
//		size: "2",
//        item: item,
//		clear : true,
//		itemType: 'checkbox' 
//    });

	$( "[name=custRelationExl]").removeAttr("checked" );
	$("#uploadFileAml").val('');
	
    var fileSize = 5 * 1024 * 1024;
    var s = $.extend({
        handler: 'amlrelatefileuploadhandler',
        fieldId: "uploadFileAml",
        title: i18n && i18n.def.insertfile || "請選擇附加檔案",
        fileCheck: ['xls'],
        successMsg: false,
        success: function(){
        },
        data: {
            fileSize: fileSize,
			mainId : responseJSON.mainId,
			deleteDup : true,
            changeUploadName: "AMLExcelList_01.xls"
        }
    }, s);

	$("#loginImportByExl").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss20a["L120S09a.importBlacklistByExcel"],//匯入EXCEL名單
		width : 960,
		height : 400,
		modal : true,
		i18n:i18n.def,
		buttons: (function(){
            var b = {};
            b[i18n.lmss20a["L120S09a.upLoad"]] = function(){
				
				var checkCou = 0;
				var list = "";
				var sign = ",";
	 
				$("[name=custRelationExl]:checkbox:checked").each(function(i){
					list += ((list == "") ? "" : sign ) + $(this).val();
					checkCou++;
				});
				
				if(checkCou == 0){
					//"尚未勾選欄位【與本案關係】內容，請勾選"
					CommonAPI.showMessage(i18n.lmss20a["L120S09a.message01"]);
					return;
				}
				
//				var f = $("#uploadFileAml");
//				if(f.files.length != 0 ){
//					//"尚未選取檔案
//					CommonAPI.showMessage(i18n.lmss20a["L120S09a.message16"]);
//					return;
//				}
//				
//				if(f.files[0].type.match(/excel.*/)){
//					//"檔案格式錯誤(非EXCEL)
//					CommonAPI.showMessage(i18n.lmss20a["L120S09a.message17"]);
//					return;
//				}
	
                $.capFileUpload({
                    handler: s.handler,
                    fileCheck: s.fileCheck,
                    fileElementId: s.fieldId,
                    successMsg: s.successMsg,
                    data: $.extend({
						fieldId: "uploadFileAml",
	                    custRelation: list
	                }, s.data || {}),
                    success: function(json){
                        $.thickbox.close();
                        API.showPopMessage(i18n.lmss20a["L120S09a.importBlacklistByExcel"]+i18n.lmss20a["L120S09a.success"]);
						$LMS1205S20Form01.find("#gridview_blackList").trigger("reloadGrid");
                    }
                });
            };
            b[i18n && i18n.def.cancel || "取消"] = function(){
                $.thickbox.close();
            };
            return b;
        })()
	});
}

//J-106-0238-001 **************************************************************************************************************************************************************************************

/**
 * J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
 * 重新引進查詢名單
 */
function importBlackListNew(){
	
	// J-107-0070-001 Web e-Loan
	// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	//L120S09a.message21=執行「重新引進查詢名單」後，請先確認名單內容，再執行「傳送名單掃描」。<br>是否確定執行本作業?
	API.confirmMessage(i18n.lmss20a["L120S09a.message21"],function(b){
		if(b){
			var count=$("#gridview_blackList").jqGrid('getGridParam','records');
			if(count > 0){
				//"執行引進後會刪除已存在之查詢名單，是否確定執行？"
				CommonAPI.confirmMessage(i18n.lmss20a["L120S09a.message05"], function(b){
					if (b) {					
						//是的function
						$.ajax({
							handler : initS20aJson.handlerName,
							type : "POST",
							dataType : "json",
							data : {
								formAction : "importBlackListNew",
								mainId : responseJSON.mainid
							},
							success : function(json) {
								
								initS20aJson.initForShow();
								
								$("#LMS1205S20Form01").find("#gridview_blackList").trigger("reloadGrid");
							}
						});				
					}				
				});		
			}else{
				$.ajax({
					handler : initS20aJson.handlerName,
					type : "POST",
					dataType : "json",
					data : {
						formAction : "importBlackListNew",
						mainId : responseJSON.mainid
					},
					success : function(json) {
						
						initS20aJson.initForShow();
						$("#LMS1205S20Form01").find("#gridview_blackList").trigger("reloadGrid");
					}
				});				
			}
		}else{
			//否的function
			 
		}
	})	
	
	
}


/**
 * J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
 * 黑名單查詢
 */
function sendAmlList(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		if(userInfo.unitNo=="940"){
			$("#amlQueryRole").thickbox({ // 使用選取的內容進行彈窗
				title : i18n.lmss20a["L120S09a.inputQueryrole"],//"登錄調查角色",
				width : 520,
				height : 200,
				align : 'center',
				valign : 'bottom',
				modal : false,
				i18n:i18n.def,
				buttons : {
					"sure" : function() {
						if($("#roleForm").valid()){
							if($("#caseBrId").val() == null || $("#caseBrId").val() == ""){
								return CommonAPI.showMessage(i18n.lmss20a["L120S09a.message23"]);	//請選擇案件調查分行
							}
							if($("#queryUser").val() == null || $("#queryUser").val() == ""){
								return CommonAPI.showMessage(i18n.lmss20a["L120S09a.message24"]);		//請選擇案件調查人員
							}
							
							$.ajax({
								handler : initS20aJson.handlerName,
								type : "POST",
								dataType : "json",
								action : "saveL120s09bQuery",
								data : {
									mainId : responseJSON.mainid,
									caseBrId : $("#caseBrId").val(),
									queryUser : $("#queryUser").val()
								},
								success : function(json) {
//									if(json.isOverSea){
//										return CommonAPI.showMessage(i18n.lmss20a["L120S09a.message25"]);		//海外分行暫不開放
//									}
									$.thickbox.close();
									
									if(json.callNewFunc=="Y"){	//newAmlButtons
										//J-107-0070-001  Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
										//L120S09a.message20=傳送名單掃描後，請執行「取得黑名單查詢結果」，若回傳之案件調查結果為有命中疑似名單，須先完成名單調查(國內分行為BTT 0015-10，海外分行為防制洗錢及打擊資恐系統)後，再重新執行「取得黑名單查詢結果」引進調查結果。<br>是否確定執行本作業?
										API.confirmMessage(i18n.lmss20a["L120S09a.message20"],function(b){
											if(b){
												//是的function
												$.ajax({
													handler : initS20aJson.handlerName,
													type : "POST",
													dataType : "json",
													data : {
														formAction : "sendAmlList",
														mainId : responseJSON.mainid
													},
													success : function(json) {
														
														initS20aJson.initForShow();
											
														$("#LMS1205S20Form01").find("#blackListQDate").val(json.blackListQDate);
														$("#LMS1205S20Form01").find("#ncResult").val(json.ncResult);
														$("#LMS1205S20Form01").find("#refNo").val(json.refNo);
														$("#LMS1205S20Form01").find("#uniqueKey").val(json.uniqueKey);
														$("#LMS1205S20Form01").find("#ncCaseId").val(json.ncCaseId);
														$("#LMS1205S20Form01").find("#sCaseBrId").val(json.sCaseBrId);
														$("#LMS1205S20Form01").find("#sQueryUser").val(json.sQueryUser);
														 
													}
												});				
											}else{
												//否的function
												 
											}
										})
									} else {	//oldAmlButtons	黑名單	queryBlackList()
										var count=$("#gridview_blackList").jqGrid('getGridParam','records');
										if(count > 0){
											$.ajax({
												handler : initS20aJson.handlerName,
												type : "POST",
												dataType : "json",
												data : {
													formAction : "insteadQueryBlackList",
													mainId : responseJSON.mainid,
													caseBrId : json.caseBrId,
													queryUser : json.queryUser,
													callNewFunc : json.callNewFunc
												},
												success : function(json) {
													initS20aJson.initForShow();
													
													$("#LMS1205S20Form01").find("#gridview_blackList").trigger("reloadGrid");
													if(json.blackListQDate){
														$("#LMS1205S20Form01").find("#blackListQDate").val(json.blackListQDate);
													}
													$("#LMS1205S20Form01").find("#sCaseBrId").val(json.sCaseBrId);
													$("#LMS1205S20Form01").find("#sQueryUser").val(json.sQueryUser);
												}
											});				
										}else{
											//"必須先執行【引進查詢名單】才能執行本功能！"
											CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
											return;
										}
									}
								}
							});
						}
					},
					"cancel" : function() {
						 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				         });
					}
				}
			});
		} else {		
			//J-107-0070-001  Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			//L120S09a.message20=傳送名單掃描後，請執行「取得黑名單查詢結果」，若回傳之案件調查結果為有命中疑似名單，須先完成名單調查(國內分行為BTT 0015-10，海外分行為防制洗錢及打擊資恐系統)後，再重新執行「取得黑名單查詢結果」引進調查結果。<br>是否確定執行本作業?
			API.confirmMessage(i18n.lmss20a["L120S09a.message20"],function(b){
				if(b){
					//是的function
					$.ajax({
						handler : initS20aJson.handlerName,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "sendAmlList",
							mainId : responseJSON.mainid
						},
						success : function(json) {
							
							initS20aJson.initForShow();
				
							$("#LMS1205S20Form01").find("#blackListQDate").val(json.blackListQDate);
							$("#LMS1205S20Form01").find("#ncResult").val(json.ncResult);
							$("#LMS1205S20Form01").find("#refNo").val(json.refNo);
							$("#LMS1205S20Form01").find("#uniqueKey").val(json.uniqueKey);
							$("#LMS1205S20Form01").find("#ncCaseId").val(json.ncCaseId);
							$("#LMS1205S20Form01").find("#sCaseBrId").val(json.sCaseBrId);
							$("#LMS1205S20Form01").find("#sQueryUser").val(json.sQueryUser);
							 
							if(json.importT70Result && json.importT70Result!=""){
								CommonAPI.showMessage(json.importT70Result);
							}
						}
					});				
				}else{
					//否的function
					 
				}
			})	
		}	
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}

/**
 * J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
 * 黑名單查詢
 */
function checkAmlResult(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "checkAmlResult",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				
				
				
				$("#LMS1205S20Form01").find("#blackListQDate").val(json.blackListQDate);
				$("#LMS1205S20Form01").find("#ncResult").val(json.ncResult);
				$("#LMS1205S20Form01").find("#refNo").val(json.refNo);
				$("#LMS1205S20Form01").find("#uniqueKey").val(json.uniqueKey);
				$("#LMS1205S20Form01").find("#ncCaseId").val(json.ncCaseId);
				$("#LMS1205S20Form01").find("#sCaseBrId").val(json.sCaseBrId);
				$("#LMS1205S20Form01").find("#sQueryUser").val(json.sQueryUser);
				
				initS20aJson.initForShow();

				if(json.resultMsg != ""){
					CommonAPI.showMessage(json.resultMsg);
				}
				
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}


/**
 * J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
 * 黑名單查詢
 */
function applyCm1AmlStatus(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "applyCm1AmlStatus",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				
				
				  
				initS20aJson.initForShow();

				if(json.resultMsg != ""){
					CommonAPI.showMessage(json.resultMsg);
				}
				
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}



/**
 * J-106-0238-004 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
 * 黑名單查詢
 */
function askAmlList(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "askAmlList",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				
				initS20aJson.initForShow();
	
				$("#LMS1205S20Form01").find("#blackListQDate").val(json.blackListQDate);
				$("#LMS1205S20Form01").find("#ncResult").val(json.ncResult);
				$("#LMS1205S20Form01").find("#refNo").val(json.refNo);
				$("#LMS1205S20Form01").find("#uniqueKey").val(json.uniqueKey);
				$("#LMS1205S20Form01").find("#ncCaseId").val(json.ncCaseId);
				$("#LMS1205S20Form01").find("#sCaseBrId").val(json.sCaseBrId);
				$("#LMS1205S20Form01").find("#sQueryUser").val(json.sQueryUser);
				 
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}

/**
 * J-106-0238-004 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
 * 黑名單查詢
 */
function clearNcResult(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "clearNcResult",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				
				initS20aJson.initForShow();
	
				$("#LMS1205S20Form01").find("#blackListQDate").val(json.blackListQDate);
				$("#LMS1205S20Form01").find("#ncResult").val(json.ncResult);
				$("#LMS1205S20Form01").find("#refNo").val(json.refNo);
				$("#LMS1205S20Form01").find("#uniqueKey").val(json.uniqueKey);
				$("#LMS1205S20Form01").find("#ncCaseId").val(json.ncCaseId);
				$("#LMS1205S20Form01").find("#sCaseBrId").val(json.sCaseBrId);
				$("#LMS1205S20Form01").find("#sQueryUser").val(json.sQueryUser);
				 
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}

/**
 * J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
 */
function applyLuvRiskLevel(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "applyLuvRiskLevel",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				
				
				  
				initS20aJson.initForShow();

				if(json.resultMsg != ""){
					CommonAPI.showMessage(json.resultMsg);
				}
				
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}

/**
 * J-112-0534 因應兆豐金控自113.1.1下架證券違約交割上市櫃觀察名單，故調整E-Loan授信管理系統相關欄位資訊
 */
function importT70(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "importT70",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				initS20aJson.initForShow();
				if(json.resultMsg && json.resultMsg != ""){
					CommonAPI.showMessage(json.resultMsg);
				}
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}

function printT70(){
	var t70FileOid=$("#T70Docfileoid").val();
	$.form.submit({
        url: webroot + '/app/lms/lms1200p01',
        target: "_blank",
        data: {
            dataType: "T70",
            fileOid: t70FileOid,
            mainId: responseJSON.mainId
        }
    });
}

/**
 * J-113-0082 配合法務部新規，新增引入「受告誡處分」資訊
 */
function importCmfwarnpResult(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "importCmfwarnp",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				initS20aJson.initForShow();
				if(json.resultMsg && json.resultMsg != ""){
					CommonAPI.showMessage(json.resultMsg);
				}
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}

/**
 * J-107-0176 配合企金處針對AML/CFT查詢作業，企金處於e-Loan授信管理系統中聯貸案簽報(報價)作業，可引進全行營業單位最近期調查結果，修改授信管理系統之AML/CFT查詢作業功能
 */
function changeItem(obj){
    var objValue = $(obj).val();
    var objName = $(obj).attr('name');
    $.ajax({
        type: "POST",
        handler: initS20aJson.handlerName,
        dataType : "json",
        data: {
            formAction: "allUserByBranch",
            qryBranch: objValue
        },
        success: function(responseData){
            var json = {
                format: "{value} - {key}",
                item: responseData
            };
            $("#queryUser").setItems(json);
        }
    });
}
//J-106-0238-001 **************************************************************************************************************************************************************************************


/**
 * I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
 */
function applyCustPanaInfo(){
	var count=$("#gridview_blackList").jqGrid('getGridParam','records');
	if(count > 0){
		$.ajax({
			handler : initS20aJson.handlerName,
			type : "POST",
			dataType : "json",
			data : {
				formAction : "applyCustPanaInfo",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				
				
				  
				initS20aJson.initForShow();

				if(json.resultMsg != ""){
					CommonAPI.showMessage(json.resultMsg);
				}
				
			}
		});				
	}else{
		//"必須先執行【引進查詢名單】才能執行本功能！"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message02"]);
		return;
	}
}

/**
 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
 * 新增態樣檢核表
 */
function addStateCheckList() {
	$("#cLMS1205S20Form02").reset();
	
	// reload借款人grid
	$("#gridview_amlCheckL120s01agrid").trigger("reloadGrid");
	$("#amlCheckStateChoose").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss20a["L120S09a.stateChooseTitle"],//"選擇借款人"
		width : 600,
		height : 400,
		modal : true,
		i18n:i18n.def,
		buttons : {
			"sure" : function(b) {
				if(b){
					var chooseCust= $("#amlCheckL120s01agrid").getGridParam('selarrrow');
					if(chooseCust.length <=0){
						// 請選擇資料
						return CommonAPI.showErrorMessage(i18n.def["grid_selector"]);
					}else {
						$.thickbox.close();
						
						// 組裝客戶custId^dupNo 傳給後端做比對
						var chooseCustArr = [];
                        for (var i in chooseCust) {
			                var row = $("#amlCheckL120s01agrid").getRowData(chooseCust[i]);
			                chooseCustArr.push( row.custId + "^" +row.dupNo);// 放進一個array中
                        }
                        
                        // 畫面選擇的版本日期
                        var versionDate = $("input[name='state_versionDate']:checked").val();
						//預設帶入姓名、統編、案號、職業、交易日期、簽案or動審?
						$.ajax({
							handler : initS20aJson.handlerName,
							type : "POST",
							dataType : "json",
							action : "queryL120s09cInit",
							data : {
								mainId : responseJSON.mainid,
								chooseCustArr : chooseCustArr,
								versionDate : versionDate
							},
							success : function(obj) {
								stateCheckThick(versionDate, obj);
							}
						});	
					}
				}
			},
			"close" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
					if(res){
						$.thickbox.close();
					}
		         });
			}
		}
	});
}

/**
 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
 * 刪除態樣檢核表
 */
function deleteStateCheckList(){
	var rows = $("#gridview_amlStateCheckList").getGridParam('selarrrow');
	var list = "";
	var sign = ",";
	for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
			var data = $("#gridview_amlStateCheckList").getRowData(rows[i]);
			list += ((list == "") ? "" : sign ) + data.oid;
		}
	}
	if (list == "") {
		//"尚未選取資料"
		CommonAPI.showMessage(i18n.lmss20a["L120S09a.message03"]);
		return;
	}	

    //"是否確定要刪除名單"
	API.confirmMessage(i18n.lmss20a["L120S09a.message29"],function(b){
		if(b){
			//是的function
			$.ajax({
				handler : initS20aJson.handlerName,
				type : "POST",
				dataType : "json",
				action : "deleteL120s09c",
				data : {
					listOid : list,
					mainId : responseJSON.mainid
				},
				success : function(json) {
					$("#LMS1205S20Form02").find("#gridview_amlStateCheckList").trigger("reloadGrid");
				}
			});
		}else{
			//否的function
			//CommonAPI.showMessage(i18n.lmss07a["L1205S07.alert3"]);
		}
	})	
}

/**
 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
 * 列印態樣檢核表
 */
function printStateCheckList() {
	$.ajax({
		handler : initS20aJson.handlerName,
		type : "POST",
		dataType : "json",
		action : "checkL120s09cCount",
		data : {
			mainId : responseJSON.mainid
		},
		success : function(json) {
			if(json.l120s09cCount == 0){
				// 無資料
				CommonAPI.showMessage(i18n.lmss20a["L120S09a.message30"]);
			}else{
				// 有資料
				$.form.submit({
				    url: "../../simple/FileProcessingService",
				    target: "_blank",
				    data: {
				        rptOid: "R41" + "^^^^^",
				        mainId: responseJSON.mainId,
				        fileDownloadName: "lms1201r41.pdf",
				        serviceName: "lms1201r01rptservice"
				    }
				});
			}
		}
	});
	
}

/**
 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
 * 態樣檢核表ThickBox
 */
function stateCheckThick(versionDate, obj) {
	// 新增時可以選擇versionDate，修改時是原資料的versionDate
	$("#amlCheckStateDetail").empty();
	
    //J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
	$("#amlCheckStateDetail").load("../../lms/lmsS20APageV"+escape(versionDate),function(){
		var $cLMS1205S20Form02 = $("#cLMS1205S20Form02");
		$cLMS1205S20Form02.setData(obj,false);
		
		$("#amlCheckStateDetail").thickbox({ // 使用選取的內容進行彈窗
			title : i18n.lmss20a["L120S09a.amlStateCheckTable"],//"疑似洗錢或資恐交易態樣檢核表",
			width : 960,
			height : 700,
			modal : true,
			i18n:i18n.def,
			buttons : {
				"saveData" : function(b) {
					if(b){
						var $cLMS1205S20Form02 = $("#cLMS1205S20Form02");
						
						if($cLMS1205S20Form02.valid()){
							$.ajax({		
								handler : initS20aJson.handlerName,
								type : "POST",
								dataType : "json",
								action : "saveL120s09c",
								data: $.extend($cLMS1205S20Form02.serializeData(), {
									mainId: responseJSON.mainid,
									oid: obj.oid ? obj.oid : '',
									versionDate: versionDate
								}),
								success : function(json) {
									$("#LMS1205S20Form02").find("#gridview_amlStateCheckList").trigger("reloadGrid");
								}
							});
							$.thickbox.close();					
						}
					}
				},
				"close" : function() {
					API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
					});
				}
			}
		});
	});
}

/**
 * J-111-0356 疑似洗錢或資恐交易態樣檢核表-綁一些js連動事件
 * 
 */
function bindVersionEvent(versionDate, item){
	if('11307' == versionDate){
		
		// 如果檢視時點選簽報、動審，則動撥的小項目要把radio清掉
		if("caseType" == item){
			$("input[name=state_checkItem5]").attr('checked',false);
		}
		// 反之，如果點選了動撥的小項目，那要把檢視時點選為動撥
		if("checkItem5" == item){
			$("input[name=state_caseType][value='2']").attr('checked',true);
		}
	}else if('11107' == versionDate){
		
		// 如果檢視時點選動審，則動撥的小項目要把radio清掉
		if("caseType" == item){
			$("input[name=state_checkItem5]").attr('checked',false);
		}
	}
}

/**
 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
 * 開起態樣檢核表以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function setStateAutoSetting(versionDate) {
	var form2 = $("#cLMS1205S20Form02");
	if(versionDate == '10807'){
		//345檢核 選不符
		$("input[name=state_checkItem3][value='1']").attr('checked', true);	
		$("input[name=state_checkItem4][value='1']").attr('checked', true);
		$("input[name=state_checkItem5][value='1']").attr('checked', true);
		
		$("input[name=state_isApprove][value='0']").attr('checked', true);//是否申報選承作
		$("input[name=state_isAml][value='1']").attr('checked', true);//是否申報選不申報
		
		//是否申報、是否承做 寫 查未有疑似洗錢或資恐交易情形。
		form2.find("#state_isApproveReason").val(i18n.lmss20a["L120S09a.stateAutoSettingReason"]);
		form2.find("#state_isAmlReason").val(i18n.lmss20a["L120S09a.stateAutoSettingReason"]);
	}else if(versionDate == '11311'){
		// 11311
		$("input[name=state_isApprove][value='0']").attr('checked', true);//是否申報選承作
		$("input[name=state_isAml][value='1']").attr('checked', true);//是否申報選不申報
		
		//是否申報、是否承做 寫 查未有疑似洗錢、資恐及異常交易情形。
		form2.find("#state_isApproveReason").val(i18n.lmss20a["L120S09a.stateAutoSettingReason11311"]);
		form2.find("#state_isAmlReason").val(i18n.lmss20a["L120S09a.stateAutoSettingReason11311"]);
	}else{
		// 11307、11107走一樣的一鍵設定
		$("input[name=state_isApprove][value='0']").attr('checked', true);//是否申報選承作
		$("input[name=state_isAml][value='1']").attr('checked', true);//是否申報選不申報
		
		//是否申報、是否承做 寫 查未有疑似洗錢或資恐交易情形。
		form2.find("#state_isApproveReason").val(i18n.lmss20a["L120S09a.stateAutoSettingReason"]);
		form2.find("#state_isAmlReason").val(i18n.lmss20a["L120S09a.stateAutoSettingReason"]);
	}
}

/**
 * J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
 * 開起態樣檢核表以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDocSC(cellvalue, options, rowObject) {
	$("#cLMS1205S20Form02").reset();

	$.ajax({
		handler : initS20aJson.handlerName,
		type : "POST",
		dataType : "json",
		action : "queryL120s09c",
		data : {
			oid : rowObject.oid
		},
		success : function(obj) {
			stateCheckThick(obj.versionDate, obj);
		}
	});	
}


$(document).ready(function() {
	
	 
	initS20aJson.setHandler();
	initS20aJson.blackListGridView();
	initS20aJson.amlStateCheckList();//J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信->檢核表資料grid
	initS20aJson.showAmlStateChoose();//J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信->選擇借款人grid
	
	//J-106-0238-001 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
	initS20aJson.initForShow();
	
	//J-107-0176 配合企金處針對AML/CFT查詢作業，企金處於e-Loan授信管理系統中聯貸案簽報(報價)作業，可引進全行營業單位最近期調查結果，修改授信管理系統之AML/CFT查詢作業功能
	if(userInfo.unitNo=="940"){initS20aJson.initAmlQueryRole();}

	//J-107-0226_11557_B1001 疑似洗錢或資恐交易態樣檢核表-授信
	//國內才有這功能，若為海外時需隱藏區塊
	initS20aJson.showAmlStateCheck();
	
    var raw = API.loadOrderCombosAsList("BlackListRelation")["BlackListRelation"];
	var parsed = raw.map(function(str) {
		  return JSON.parse(str);
		});  
	
	$("#custRelation").setItems({
		size: "2",
        item: parsed,
		clear : true,
		itemType: 'checkbox' 
    }); 
	
	 
	$("#custRelationExl").setItems({
		size: "2",
        item: parsed,
		clear : true,
		itemType: 'checkbox' 
    }); 
	
	
	var obj = CommonAPI.loadCombos(["SAS_NC_Result"]);
	$("#ncResult").setItems({
        item: obj.SAS_NC_Result,
        format: "{value} - {key}"
    });
	
	
});

