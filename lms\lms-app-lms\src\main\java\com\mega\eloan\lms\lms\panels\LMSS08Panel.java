package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.panels.LMSS08HPanel;
import com.mega.eloan.lms.base.panels.LMSS08IPanel;
import com.mega.eloan.lms.base.panels.LMSS08MPanel;
import com.mega.eloan.lms.base.panels.LMSS08NPanel;
import com.mega.eloan.lms.base.panels.LMSS08OPanel;
import com.mega.eloan.lms.base.panels.LMSS08PPanel;

/**
 * <pre>
 * 相關文件(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS08Panel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS08Panel(String id) {
		super(id);
	}

	public LMSS08Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new LMSS08Panel01("lmss08a_panel").processPanelData(model, params);
		new LMSS08Panel02("lmss08b_panel").processPanelData(model, params);
		new LMSS08DPanel("lmss08d_panel").processPanelData(model, params);
		// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
		new LMSS08HPanel("lmss08h_panel").processPanelData(model, params);
		// J-110-0386_05097_B1001 Web e-Loan國內與海外企金授信「相關文件」頁籤新增「授信額度主要敘做條件彙總表」
		new LMSS08IPanel("lmss08i_panel").processPanelData(model, params);
		// J-112-0357 新增敘做條件異動比較表
		new LMSS08MPanel("lmss08m_panel").processPanelData(model, params);
		// J-112-0357 新增董會討論事項提案檢核表
		new LMSS08NPanel("lmss08n_panel").processPanelData(model, params);
		// J-112-0508_05097_B1002 Web e-Loan為提升授信簽報效率,
		// 三大部及授審處可由eloan授信系統依據授審會及常董會提案稿所需文件之順序產生相關提案稿pdf
		new LMSS08OPanel("lmss08o_panel").processPanelData(model, params);
		new LMSS08PPanel("lmss08p_panel").processPanelData(model, params);
	}
}
