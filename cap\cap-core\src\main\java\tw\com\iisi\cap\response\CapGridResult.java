/*_
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package tw.com.iisi.cap.response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletResponse;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.iisigroup.cap.component.impl.StringResponse;

import tw.com.iisi.cap.enums.IGridEnum;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 設置Grid內容
 * </pre>
 * 
 * @since 2010/11/25
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/11/25,iristu,new
 *          <li>2011/6/21,RodesChen,增加method 供 MGridHandler 使用
 *          <li>2011/10/27,iristu,改implements IGridResult
 *          </ul>
 */
public class CapGridResult implements IGridResult<CapGridResult, GenericBean> {

    private Logger logger = LoggerFactory.getLogger(CapGridResult.class);

    /**
     * Grid資料內容
     */
    protected JSONObject resultMap;

    /**
     * 資料列
     */
    protected List<? extends GenericBean> rowData;

    /**
     * 欄位
     */
    protected String[] columns;

    /**
     * 資料格式化
     */
    protected Map<String, IFormatter> dataReformatter;

    /**
     * 建構子
     */
    public CapGridResult() {
        resultMap = new JSONObject();
    }

    /**
     * 建構子
     * 
     * @param rowData
     *            資料列
     * @param records
     *            資料總筆數
     */
    public CapGridResult(List<? extends GenericBean> rowData, int records) {
        this(rowData, records, null);
    }

    /**
     * 建構子
     * 
     * @param rowData
     *            資料列
     * @param records
     *            資料總筆數
     * @param dataReformatter
     *            資料格式化
     */
    public CapGridResult(List<? extends GenericBean> rowData, int records, Map<String, IFormatter> dataReformatter) {
        resultMap = new JSONObject();
        setRowData(rowData);
        setRecords(records);
        setDataReformatter(dataReformatter);
    }

    /**
     * <pre>
     * 設定頁碼
     * </pre>
     * 
     * @param page
     *            頁碼
     * @return this
     */
    public CapGridResult setPage(int page) {
        resultMap.put(IGridEnum.PAGE.getCode(), page);
        resultMap.put("page", page); // 回傳前端jqgrid仍是讀取「page」的參數
        return this;
    }// ;

    /**
     * <pre>
     * 設定總頁數
     * </pre>
     * 
     * @param rowCount
     *            總筆數
     * @param pageRows
     *            一頁筆數
     * @return this
     */
    public CapGridResult setPageCount(int rowCount, int pageRows) {
        if (pageRows == 0)
            pageRows = 1;
        resultMap.put(IGridEnum.TOTAL.getCode(), rowCount / pageRows + (rowCount % pageRows > 0 ? 1 : 0));
        return this;
    }// ;

    /**
     * <pre>
     * 設定總筆數
     * </pre>
     * 
     * @param rowCount
     *            總筆數
     * @return this
     */
    public CapGridResult setRecords(int rowCount) {
        resultMap.put(IGridEnum.RECORDS.getCode(), rowCount);
        return this;
    }// ;

    /**
     * <pre>
     * 取得總筆數
     * </pre>
     * 
     * @return 總筆數
     */
    public Integer getRecords() {
        Object o = resultMap.get(IGridEnum.RECORDS.getCode());
        return o == null ? 0 : (Integer) o;
    }// ;

    /**
     * <pre>
     * 設定資料行
     * </pre>
     * 
     * @param rowData
     *            資料
     * @return this
     */
    public CapGridResult setRowData(List<? extends GenericBean> rowData) {
        this.rowData = rowData;
        return this;
    }

    /*
     * 取得字串資料列
     * 
     * @see tw.com.iisi.cap.response.IResult#getResult()
     */
    @Override
    public String getResult() {
        resultMap.put(IGridEnum.PAGEROWS.getCode(), getRowDataToJSON());
        return resultMap.toString();
    }

    /*
     * 加入結果資料
     * 
     * @see tw.com.iisi.cap.response.IResult#getLogMessage()
     */
    @Override
    public String getLogMessage() {
        StringBuffer b = new StringBuffer();
        b.append("page=").append(resultMap.get(IGridEnum.PAGE.getCode())).append(",pagerow=").append(resultMap.get(IGridEnum.PAGEROWS.getCode())).append(",rowData=")
                .append(resultMap.get(IGridEnum.PAGEROWS.getCode()));
        return b.toString();
    }

    /*
     * 加入結果資料
     * 
     * @see tw.com.iisi.cap.response.IResult#add(tw.com.iisi.cap.response.IResult)
     */
    @Override
    public void add(IResult result) {
        JSONObject json = JSONObject.fromObject(result);
        resultMap.putAll(json);
    }

    /**
     * 加入Reformat Data
     * 
     * @param key
     *            鍵值
     * @param formatter
     *            格式化方式
     * @return
     * @throws CapException
     */
    protected CapGridResult addReformatData(String key, IFormatter formatter) throws CapException {
        if (dataReformatter == null) {
            dataReformatter = new HashMap<String, IFormatter>();
        }
        dataReformatter.put(key, formatter);
        return this;
    }

    /**
     * 設定欄位
     */
    @Override
    public void setColumns(String[] columns) {
        this.columns = columns;
    }

    /**
     * 取得資料行
     */
    @Override
    public List<? extends GenericBean> getRowData() {
        return this.rowData;
    }

    /**
     * 取得Data Reformatter
     */
    @Override
    public Map<String, IFormatter> getDataReformatter() {
        return this.dataReformatter;
    }

    /**
     * 將資料行轉為JSON array
     * 
     * @return
     */
    protected List<Object> getRowDataToJSON() {
        List<Object> rows = new JSONArray();
        Map<String, Object> row = new HashMap<String, Object>();
        if (rowData != null && !rowData.isEmpty()) {
            for (GenericBean data : rowData) {
                try {
                    row.put(IGridEnum.CELL.getCode(), data.toJSONString(this.columns, dataReformatter));
                } catch (CapException e) {
                    logger.error(e.getMessage(), e);
                    try {
                        row.put(IGridEnum.CELL.getCode(), data.toJSONString(this.columns, null));
                    } catch (CapException e1) {
                        logger.error(e.getMessage(), e1);
                    }
                }
                rows.add(row);
            }
        }
        return rows;
    }

    /**
     * set DataReformatter
     * 
     * @param dataReformatter
     *            Map<String, IFormatter>
     */
    @Override
    public void setDataReformatter(Map<String, IFormatter> dataReformatter) {
        this.dataReformatter = dataReformatter;
    }

    /*
     * 建立字串回應
     * 
     * @see tw.com.iisi.cap.response.IResult#respondResult(javax.servlet.ServletResponse)
     */
    @Override
    public void respondResult(ServletResponse response) throws CapException {
        new StringResponse("text/plain", "utf-8", getResult()).respond(response);
    }

}// ~
