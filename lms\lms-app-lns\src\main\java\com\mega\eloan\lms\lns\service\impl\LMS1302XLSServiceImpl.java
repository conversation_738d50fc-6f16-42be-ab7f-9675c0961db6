package com.mega.eloan.lms.lns.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.service.BranchService;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.PageOrientation;
import jxl.format.PaperSize;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 異常通報產Excel
 * </pre>
 * 
 * @since 2012/12/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/3,Miller
 *          </ul>
 */
@Service("lms1302xlsservice")
public class LMS1302XLSServiceImpl implements FileDownloadService {

	@Resource
	LMS1201Service service1201;

	@Resource
	MisdbBASEService misDBService;

	@Resource
	BranchService branch;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	ICustomerService customerService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	CodeTypeService codetypeService;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1302XLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		String approveUnestablshExlTextarea = Util.trim(params
				.getString("approveUnestablshExlTextarea"));

		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);

		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> listMap = null;
		WritableFont font12 = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12CenterNO = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12LeftNO = null;
		WritableCellFormat format12RightNO = null;
		// L120M01A meta = service1201.findL120m01aByMainId(mainId);

		try {
			baos = new ByteArrayOutputStream();
			WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableSheet sheet = book.createSheet("在途授信", 0);
			SheetSettings settings = sheet.getSettings();
			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			settings.setPaperSize(PaperSize.A4);

			settings.setOrientation(PageOrientation.LANDSCAPE);
			// 縮放比例頁寬
			settings.setFitWidth(1);
			// 縮放比例頁高
			settings.setFitHeight(1);
			// 設定字型與格式
			// other.msg60=新細明體
			font12 = new WritableFont(WritableFont.createFont("新細明體"), 12,
					WritableFont.BOLD);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT);
			format12CenterNO = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE, false, false);
			format12LeftNO = LMSUtil.setCellFormat(format12LeftNO, font12,
					Alignment.LEFT, false, false);
			format12RightNO = LMSUtil.setCellFormat(format12RightNO, font12,
					Alignment.RIGHT, false, false);

			// if (meta != null) {
			// custId = Util.trim(meta.getCustId());
			// dupNo = Util.trim(meta.getDupNo());
			// brno = Util.trim(meta.getCaseBrId());
			// custName = Util.trim(meta.getCustName());
			// listMap = misDBService.selExcel(custId, dupNo, brno);
			// }

			String[] queryIdArr = null;
			if (!Util.isEmpty(approveUnestablshExlTextarea)) {
				queryIdArr = approveUnestablshExlTextarea.split(",");
			} else {
				throw new CapMessageException("查詢ID不得為空白", getClass());
			}

			String[] title = { "簽案分行", "核准日期", "案號", "統編", "重複序號", "戶名",
					"額度序號", "辦理情形", "現請額度幣別", "現請額度金額", "有擔保金額", "無擔保金額",
					"不計入銀行法第33條之3限額註記" };
			int maxX = title.length;

			int y = 0;
			sheet.mergeCells(0, y, maxX - 1, y);
			Label labelT1 = new Label(0, y, "在途授信額度明細表", format12CenterNO);
			sheet.addCell(labelT1);

			y = y + 1;
			sheet.mergeCells(0, y, maxX - 1, y);
			Label labelT3 = new Label(0, y, "查詢統編："
					+ approveUnestablshExlTextarea, format12LeftNO);
			sheet.addCell(labelT3);

			y = y + 1;
			sheet.mergeCells(0, y, maxX - 1, y);
			Label labelT4 = new Label(0, y, "查詢日期："
					+ CapDate.getCurrentDate("YYY/MM/DD"), format12LeftNO);
			sheet.addCell(labelT4);

			// 設定行寬
			int x = 0;
			sheet.setColumnView(x++, 10);
			sheet.setColumnView(x++, 16);
			sheet.setColumnView(x++, 20);
			sheet.setColumnView(x++, 16);
			sheet.setColumnView(x++, 10);
			sheet.setColumnView(x++, 20);
			sheet.setColumnView(x++, 20);
			sheet.setColumnView(x++, 20);
			sheet.setColumnView(x++, 20);
			sheet.setColumnView(x++, 20);
			sheet.setColumnView(x++, 20);
			sheet.setColumnView(x++, 20);
			sheet.setColumnView(x++, 10);

			y = y + 1;
			for (int j = 0; j < maxX; j++) {
				Label labelT5 = new Label(j, y, title[j], format12Center);
				sheet.addCell(labelT5);
			}

			Map<String, String> item_STATUS = codetypeService.findByCodeType(
					"ELF447N_STATUS", "zh_TW");

			for (String queryId : queryIdArr) {
				String custId = Util.getLeftStr(queryId,
						StringUtils.length(queryId) - 1);
				String dupNo = Util.getRightStr(queryId, 1);

				List<String> misList = new ArrayList<String>();
				listMap = misDBService.selElAmt(custId, dupNo);
				if (listMap != null && !listMap.isEmpty()) {
					for (Map<String, Object> elMap : listMap) {
						String misCntrno = MapUtils.getString(elMap,
								"ELF447N_CONTRACT", "");
						if (!misList.contains(misCntrno)) {
							misList.add(misCntrno);
						}
					}
				} else {
					continue;
				}

				List<String> dwContrnoList = dwdbBASEService
						.findDWLnquotovWithoutCntrno(misList);

				if (listMap != null && !listMap.isEmpty()
						&& dwContrnoList != null && !dwContrnoList.isEmpty()) {
					for (Map<String, Object> dataMap : listMap) {

						String ELF447N_UNID = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_UNID"));
						String ELF447N_BRANCH = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_BRANCH"));
						String ELF447N_ENDDATE = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_ENDDATE"));
						String ELF447N_CLASS = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_CLASS"));
						String ELF447N_CUSTID = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_CUSTID"));
						String ELF447N_DUPNO = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_DUPNO"));
						String ELF447N_STATUS = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_STATUS"));
						String ELF447N_STATUS_DT = Util.trim(MapUtils
								.getObject(dataMap, "ELF447N_STATUS_DT"));
						String ELF447N_CONTRACT = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_CONTRACT"));
						BigDecimal ELF447N_CURAMT = Util.equals(
								Util.trim(dataMap.get("ELF447N_CURAMT")), "") ? BigDecimal.ZERO
								: new BigDecimal(Util.trim(dataMap
										.get("ELF447N_CURAMT")));
						String ELF447N_CURR = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_CURR"));
						BigDecimal ELF447N_CURAMT_S = Util.equals(
								Util.trim(dataMap.get("ELF447N_CURAMT_S")), "") ? BigDecimal.ZERO
								: new BigDecimal(Util.trim(dataMap
										.get("ELF447N_CURAMT_S")));
						BigDecimal ELF447N_CURAMT_N = Util.equals(
								Util.trim(dataMap.get("ELF447N_CURAMT_N")), "") ? BigDecimal.ZERO
								: new BigDecimal(Util.trim(dataMap
										.get("ELF447N_CURAMT_N")));
						String ELF447N_LNNOFLA = Util.trim(MapUtils.getObject(
								dataMap, "ELF447N_LNNOFLA"));

						if (!dwContrnoList.contains(ELF447N_CONTRACT)) { // 確認額度序號也不存在於海外
							continue;
						}
						y = y + 1;
						int xx = 0;

						L120M01A l120m01a = l120m01aDao
								.findByMainId(ELF447N_UNID);
						if (l120m01a == null) {
							l120m01a = new L120M01A();
						}

						L140M01A l140m01a = l140m01aDao
								.findByL120m01cMainIdAndcntrNo(ELF447N_UNID,
										ELF447N_CONTRACT,
										UtilConstants.Cntrdoc.ItemType.額度明細表);
						if (l140m01a == null) {
							l140m01a = new L140M01A();
						}

						sheet.addCell(new Label(xx++, y, ELF447N_BRANCH,
								format12Center)); // 簽案分行

						sheet.addCell(new Label(xx++, y, ELF447N_ENDDATE,
								format12Center)); // 核准日期
						sheet.addCell(new Label(xx++, y, l120m01a == null ? ""
								: l120m01a.getCaseNo(), format12Left)); // 案號

						sheet.addCell(new Label(xx++, y, ELF447N_CUSTID,
								format12Left)); // 統編

						sheet.addCell(new Label(xx++, y, ELF447N_DUPNO,
								format12Center)); // 重複序號

						sheet.addCell(new Label(xx++, y,
								l140m01a.getCustName(), format12Left)); // 戶名

						sheet.addCell(new Label(xx++, y, ELF447N_CONTRACT,
								format12Center)); // 額度序號

						sheet.addCell(new Label(xx++, y, MapUtils.getString(
								item_STATUS, ELF447N_STATUS, "N.A"),
								format12Center)); // 辦理情形

						sheet.addCell(new Label(xx++, y, ELF447N_CURR,
								format12Center)); // 請額度幣別

						sheet.addCell(new Label(xx++, y, NumConverter
								.addComma(ELF447N_CURAMT), format12Right)); // 現請額度金額

						sheet.addCell(new Label(xx++, y, NumConverter
								.addComma(ELF447N_CURAMT_S), format12Right)); // 有擔保金額
						sheet.addCell(new Label(xx++, y, NumConverter
								.addComma(ELF447N_CURAMT_N), format12Right)); // 無擔保金額
						sheet.addCell(new Label(xx++, y, ELF447N_LNNOFLA,
								format12Center)); // 不計入同一關係戶註記

					}
				}
			}
			book.write();
			book.close();
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}
}
