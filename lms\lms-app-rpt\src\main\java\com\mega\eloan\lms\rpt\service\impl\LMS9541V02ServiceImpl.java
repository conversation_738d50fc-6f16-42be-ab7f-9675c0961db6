/* 
 *  LMS9515ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.map.LinkedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.dao.L810M01ADao;
import com.mega.eloan.lms.mfaloan.service.impl.AbstractMFAloanJdbc;
import com.mega.eloan.lms.model.L810M01A;
import com.mega.eloan.lms.rpt.service.LMS9541V02Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS9541V02ServiceImpl extends AbstractMFAloanJdbc implements
		LMS9541V02Service {
	@Resource
	BranchService branchservice;

	@Resource
	L810M01ADao l810m01aDao;

	private static final Logger logger = LoggerFactory
			.getLogger(LMS9541V01ServiceImpl.class);

	@Override
	public boolean isRepeat(L810M01A data) {
		ISearch search = l810m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "brno",
				data.getBrno());
		search.addSearchModeParameters(SearchMode.EQUALS, "useType",
				data.getUseType());
		search.addSearchModeParameters(SearchMode.EQUALS, "rptType",
				data.getRptType());
		search.addSearchModeParameters(SearchMode.EQUALS, "endDate",
				data.getEndDate());
		if (!l810m01aDao.find(search).isEmpty())
			return true;
		else
			return false;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof L810M01A) {
					l810m01aDao.save(((L810M01A) model));
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof L810M01A) {
				l810m01aDao.delete(((L810M01A) model));
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L810M01A.class) {
			return l810m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L810M01A.class) {
			L810M01A model = Util.isEmpty(oid) ? null : l810m01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L810M01A.class) {
			List<L810M01A> list = Util.isEmpty(mainId) ? null : l810m01aDao
					.findByMainId(mainId);
			return (list == null ? null : list);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public List<Map<String, Object>> getCityData(String kindNo) {
		// initial
		List<Map<String, Object>> result = null, accept = null, overage = null, newHouse = null, oldHouse = null;
		boolean getAll = ("5".equals(kindNo));
		try {
			accept = getJdbc().queryForList("MIS.BYCITY.ACCEPT", null);
			if (getAll) {
				overage = getJdbc()
						.queryForList("MIS.BYCITY.OVERAGE.ALL", null);
				newHouse = getJdbc().queryForList("MIS.BYCITY.HOUSE.ALL",
						new String[] { "N" });
				oldHouse = getJdbc().queryForList("MIS.BYCITY.HOUSE.ALL",
						new String[] { "O" });
			} else {
				overage = getJdbc().queryForList("MIS.BYCITY.OVERAGE",
						new String[] { kindNo });
				newHouse = getJdbc().queryForList("MIS.BYCITY.HOUSE",
						new String[] { kindNo, "N" });
				oldHouse = getJdbc().queryForList("MIS.BYCITY.HOUSE",
						new String[] { kindNo, "O" });
			}
			result = new LinkedList();
			boolean isOther = true;
			Map<String, Object> other = new LinkedMap(), sumup = new LinkedMap();
			other.put("area", "其他");
			sumup.put("area", "合計");
			BigDecimal[] total = new BigDecimal[11];
			for (int i = 0; i < total.length; i++) {
				total[i] = Util.parseBigDecimal("0");
			}

			for (int i = 0; i < CITYLIST.length; i++) {
				Map<String, Object> pivot = new LinkedMap();
				pivot.put("area", CITYLIST[i]);
				// 已撥發
				Map<String, Object> allocate;
				if (getAll) {
					allocate = getJdbc().queryForMap("MIS.BYCITY.ALLOCATE.ALL",
							new String[] { CITYLIST[i] });
				} else {
					allocate = getJdbc().queryForMap("MIS.BYCITY.ALLOCATE",
							new String[] { CITYLIST[i], kindNo });
				}

				BigDecimal achouses = Util.parseBigDecimal(allocate
						.get("ADOOR"));

				BigDecimal acfavor = Util.parseBigDecimal(allocate.get("APIR"));
				BigDecimal acnormal = Util.parseBigDecimal(allocate
						.get("ADTOL"));

				total[4] = total[4].add(achouses);
				total[5] = total[5].add(acfavor.add(acnormal));
				total[6] = total[6].add(acfavor);
				total[7] = total[7].add(acnormal);

				pivot.put("acHouses", achouses);
				pivot.put("acFavor", acfavor);
				pivot.put("acNormal", acnormal);
				pivot.put("acSumup", acfavor.add(acnormal));

				result.add(pivot);
			}

			Map<String, Object> acother = getJdbc().queryForMap(
					"MIS.BYCITY.ALLOCATE", new String[] { "", kindNo });
			BigDecimal achouses = Util.parseBigDecimal(acother.get("ADOOR"));
			BigDecimal acfavor = Util.parseBigDecimal(acother.get("APIR"));
			BigDecimal acnormal = Util.parseBigDecimal(acother.get("ADTOL"));

			total[4] = total[4].add(achouses);
			total[5] = total[5].add(acfavor.add(acnormal));
			total[6] = total[6].add(acfavor);
			total[7] = total[7].add(acnormal);

			other.put("acHouses", achouses);
			other.put("acFavor", acfavor);
			other.put("acNormal", acnormal);
			other.put("acSumup", acfavor.add(acnormal));

			// 已受理
			for (int i = 0; i < accept.size(); i++) {
				isOther = true;
				Map<String, Object> dataPivot = accept.get(i);
				for (int j = 0; j < result.size(); j++) {
					Map<String, Object> pivot = result.get(j);
					if (pivot.get("area").equals(dataPivot.get("areaNo"))) {
						BigDecimal houses = Util.parseBigDecimal(dataPivot
								.get("ADOOR"));
						BigDecimal favor = Util.parseBigDecimal(dataPivot
								.get("APIR"));
						BigDecimal normal = Util.parseBigDecimal(dataPivot
								.get("ADTOL"));

						total[0] = total[0].add(houses);
						total[1] = total[1].add(favor.add(normal));
						total[2] = total[2].add(favor);
						total[3] = total[3].add(normal);

						pivot.put("houses", houses);
						pivot.put("favor", favor);
						pivot.put("normal", normal);
						pivot.put("sumup", favor.add(normal));

						isOther = false;
						break;// 找到則繼續找下個
					}
				}
				if (isOther) {
					BigDecimal houses = Util.parseBigDecimal(dataPivot
							.get("ADOOR"));
					BigDecimal favor = Util.parseBigDecimal(dataPivot
							.get("APIR"));
					BigDecimal normal = Util.parseBigDecimal(dataPivot
							.get("ADTOL"));

					total[0] = total[0].add(houses);
					total[1] = total[1].add(favor.add(normal));
					total[2] = total[2].add(favor);
					total[3] = total[3].add(normal);

					other.put("houses", houses);
					other.put("favor", favor);
					other.put("normal", normal);
					other.put("sumup", favor.add(normal));
				}
			}
			// 餘額
			for (int i = 0; i < overage.size(); i++) {
				isOther = true;
				Map<String, Object> dataPivot = overage.get(i);
				for (int j = 0; j < result.size(); j++) {
					Map<String, Object> pivot = result.get(j);
					if (pivot.get("area").equals(dataPivot.get("areaNo"))) {
						BigDecimal lnbal = Util.parseBigDecimal(dataPivot
								.get("LNBAL"));
						pivot.put("overage", lnbal);
						total[10] = total[10].add(lnbal);
						isOther = false;
						break;// 找到則繼續找下個
					}
				}
				if (isOther) {
					BigDecimal lnbal = Util.parseBigDecimal(dataPivot
							.get("LNBAL"));
					other.put("overage", lnbal);
					total[10] = total[10].add(lnbal);
				}
			}
			// 新屋戶數
			for (int i = 0; i < newHouse.size(); i++) {
				isOther = true;
				Map<String, Object> dataPivot = newHouse.get(i);
				for (int j = 0; j < result.size(); j++) {
					Map<String, Object> pivot = result.get(j);
					if (pivot.get("area").equals(dataPivot.get("areaNo"))) {
						BigDecimal house = Util.parseBigDecimal(dataPivot
								.get("HOUSE"));
						total[8] = total[8].add(house);
						pivot.put("newHouse", house);
						isOther = false;
						break;// 找到則繼續找下個
					}
				}
				if (isOther) {
					BigDecimal house = Util.parseBigDecimal(dataPivot
							.get("HOUSE"));
					total[8] = total[8].add(house);
					other.put("newHouse", house);
				}
			}
			// 中古屋戶數
			for (int i = 0; i < oldHouse.size(); i++) {
				isOther = true;
				Map<String, Object> dataPivot = oldHouse.get(i);
				for (int j = 0; j < result.size(); j++) {
					Map<String, Object> pivot = result.get(j);
					if (pivot.get("area").equals(dataPivot.get("areaNo"))) {
						BigDecimal house = Util.parseBigDecimal(dataPivot
								.get("HOUSE"));
						total[9] = total[9].add(house);
						pivot.put("oldHouse", house);
						isOther = false;
						break;// 找到則繼續找下個
					}
				}
				if (isOther) {
					BigDecimal house = Util.parseBigDecimal(dataPivot
							.get("HOUSE"));
					total[9] = total[9].add(house);
					other.put("newHouse", house);
				}
			}
			result.add(other);
			for (int i = 0; i < total.length; i++) {
				sumup.put(CITYCOLS[i + 1], total[i]);
			}
			result.add(sumup);
		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return result;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public List<Map<String, Object>> getBrnoData(String kindNo, String endDate) {
		List<Map<String, Object>> result = null;
		try {
			List<Map<String, Object>> accept, allocate;
			result = new LinkedList();
			if ("5".equals(kindNo)) {
				accept = getJdbc().queryForList("MIS.BYBRNO.ACCEPT.ALL", null);
				allocate = getJdbc().queryForList("MIS.BYBRNO.ALLOCATE.ALL",
						new String[] { TWNDate.toAD(new Date()) });
			} else {
				accept = getJdbc().queryForList("MIS.BYBRNO.ACCEPT",
						new String[] { kindNo });
				allocate = getJdbc().queryForList("MIS.BYBRNO.ALLOCATE",
						new String[] { TWNDate.toAD(new Date()), kindNo });
			}
			for (int i = 0; i < accept.size(); i++) {
				Map<String, Object> apPivot = accept.get(i);
				String brno = Util.nullToSpace(apPivot.get("brno"));
				for (int j = 0; j < allocate.size(); j++) {
					Map<String, Object> acPivot = allocate.get(j);
					if (brno.equals(acPivot.get("brno"))) {
						Map<String, Object> record = new LinkedMap();
						BigDecimal favor = Util.parseBigDecimal(apPivot
								.get("APIR"));
						BigDecimal normal = Util.parseBigDecimal(
								apPivot.get("ADTOL")).setScale(scale);
						record.put("brno",
								brno + branchservice.getBranchName(brno));
						record.put("houses", apPivot.get("ADOOR"));
						record.put("sumup", favor.add(normal));
						record.put("favor", favor);
						record.put("normal", normal);
						record.put("favorRate", normal.divide(favor,
								BigDecimal.ROUND_HALF_DOWN));

						favor = Util.parseBigDecimal(acPivot.get("APIR"));
						normal = Util.parseBigDecimal(acPivot.get("ADTOL"))
								.setScale(scale);
						record.put("acHouses", acPivot.get("ADOOR"));
						record.put("acSumup", favor.add(normal));
						record.put("acFavor", favor);
						record.put("acNormal", normal);
						record.put("acFavorRate", normal.divide(favor,
								BigDecimal.ROUND_HALF_DOWN));
						result.add(record);
						break;
					}
				}
			}

		} catch (Exception e) {
			logger.error("[getContent] Exception!!", e.getMessage());
		}
		return result;
	}

	@Override
	public String findElghtappByKindno(String kindNo) {
		String result = null;
		if ("5".equals(kindNo)) {
			BigDecimal count = Util.parseBigDecimal("0");
			for (int i = 1; i < 5; i++) {
				Map<String, Object> data = getJdbc().queryForMap(
						"MIS.ELGHTAPP.getTotapp",
						new String[] { String.valueOf(i) });
				count = count.add(Util.parseBigDecimal(data.get("TOTAPP")));
			}
			result = count.toString();
		} else {
			Map<String, Object> data = getJdbc().queryForMap(
					"MIS.ELGHTAPP.getTotapp", new String[] { kindNo });
			if (data == null || data.size() == 0)
				result = "0";
			else
				result = Util.trim(data.get("TOTAPP"));
		}
		return result;
	}

	@Override
	public Map<String, Object> findTot(String kindNo, String type) {
		Map<String, Object> result = null;
		String query = "MIS." + type + ".MORTGAGE";
		if ("5".equals(kindNo)) {
			query += ".ALL";
			result = getJdbc().queryForMap(query, null);
		} else {
			result = getJdbc().queryForMap(query, new String[] { kindNo });
		}
		return result;
	}
}
