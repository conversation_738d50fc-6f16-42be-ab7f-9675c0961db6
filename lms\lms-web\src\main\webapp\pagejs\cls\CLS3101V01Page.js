var _handler = "cls3101m01formhandler";
var text_grid_height = 120;
$(function(){	
	
	//default div
	var $gridview = $("#gridview").iGrid({
        handler: "cls3101gridhandler",
        height: 350,
        rowNum: 15,
        shrinkToFit: false,
        multiselect: false,  
        sortname: 'approveTime|custId' ,
       	sortorder: 'desc|asc' ,
        postData: {
            formAction: "queryView",
            docStatus : viewstatus	
        },
        colModel: [
            {
            	colHeader: "",name: 'oid', hidden: true
	        }, {
	        	colHeader: "",name: 'mainId', hidden: true
	        }, {
	        	colHeader: "",name: 'ownBrId', hidden: true
	        }, {
	            colHeader: i18n.cls3101v01["C310M01A.custId"], 
	            align: "left", width: 85, sortable: true, name: 'custId',
				onclick : openDoc, formatter : 'click'
	        }, {
	            colHeader: i18n.cls3101v01["C310M01A.custName"], 
	            align: "left", width: 100, sortable: true, name: 'custName'
	        }, {
	            colHeader: i18n.cls3101v01["C900S02E.cyc_mn"], 
	            align: "left", width: 100, sortable: false, name: 'cyc_mn'
	        }, {
	            colHeader: i18n.cls3101v01["C900S02E.text"], 
	            align: "left", width: 160, sortable: false, name: 'text'
	        }, {
	            colHeader: i18n.cls3101v01["C310M01A.chk_result"], 
	            align: "left", width: 80, sortable: true, name: 'chk_result'
	        }, {
	            colHeader: i18n.cls3101v01["C310M01A.chk_memo"], 
	            align: "left", width: 150, sortable: true, name: 'chk_memo'
	        }, {
	        	colHeader: i18n.cls3101v01["C310M01A.updater"], //異動人員
	            align: "left", width: 80, sortable: true, name: 'updater'
	        }, {
	        	colHeader: i18n.cls3101v01["C310M01A.approver"], //核准人員
	            align: "left", width: 80, sortable: true, name: 'approver'
	        }, {
	        	colHeader: i18n.cls3101v01["C310M01A.approveTime"], //核准時間
	            align: "left", width: 80, sortable: true, name: 'approveTime'
	        }
	     ],
		ondblClickRow : function(rowid){
			openDoc(null, null, $gridview.getRowData(rowid));
		}
    });
	
	var TextGrid = $('#TextGrid').iGrid({
        handler: 'cls3101gridhandler', //設定handler
        height: text_grid_height, //設定高度
        width: 500,
    	autowidth: true,
        sortname: 'cyc_mn|rel_flag' ,
       	sortorder: 'desc|asc' ,
        postData: {
            formAction: "queryText"
            
        },
        rowNum: 15,
        rownumbers: true,
        colModel: [{
            colHeader: i18n.cls3101v01["C900S02E.cyc_mn"], // 資料年月
            align: "left",
            width: 80, //設定寬度
            sortable: false, //是否允許排序
            name: 'cyc_mn'
        }, {
            colHeader: i18n.cls3101v01["C900S02E.text"], // 通訊處
            align: "left",
            width: 120, //設定寬度
            sortable: false, //是否允許排序
            name: 'text'
        }, {
            colHeader: i18n.cls3101v01["C900S02E.chk_result"], // 查證結果
            align: "left",
            width: 120, //設定寬度
            sortable: false, //是否允許排序
            name: 'chk_result'
        }, {
            colHeader: i18n.cls3101v01["C900S02E.approveTime"], // 覆核日期
            align: "left",
            width: 120, //設定寬度
            sortable: false, //是否允許排序
            name: 'approveTime'
        }, {
        	name: 'rel_flag',
        	hidden: true
    	}, {
        	name: 'mainId',
        	hidden: true
    	}],
        loadComplete: function () {            	
        	if( TextGrid.getGridParam("records")>0){
        		//
        	}else{
        		var custId = TextGrid.getGridParam("postData")['custId'];
				if(custId && custId.length>1){
//	        		$.thickbox.close();
	        		API.showMessage(i18n.def["grid.emptyrecords"]);
				}else{
					//第一次進入頁面
				}
        	}
        }  
 });
	
	function openDoc(cellvalue, options, rowObject) {
		//ilog.debug(rowObject);		
		$.form.submit({
			url : '../cls/cls3101m01/01',
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus
			},
			target : rowObject.oid
		});					
	};
	
	
    $("#buttonPanel").find("#btnView").click(function(){
    	var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
        	//
        }else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
    	var _id = "_div_cls3101v01_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls3101v01["C310M01A.custId"]+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId'  maxlength='10'>"
					+"</td></tr>");
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			//ui_lms2401.msg02=請選擇不覆審原因
	       title: '篩選',
	       width: 550,
           height: 190,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
                  $.thickbox.close();
                  //=============
                  $gridview.setGridParam({
  	                postData: $.extend(
  	                	{}
  	                	,$("#"+_form).serializeData()
  	                ),
  	                search: true
                  }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
    }).end().find("#btnAdd").click(function(){
    	
    	chose_custId().done(function(resultFrom_chose_custId){
   	 		chose_text(resultFrom_chose_custId).done(function(resultFrom_chose_text){
				$.ajax({
                    handler: _handler,
                    action : 'newC310M01A',
					data : $.extend(resultFrom_chose_text, {})
                    }).done(function(json){
                    	ilog.debug("newC310M01A, return {oid="+json.oid+", mainOid="+json.mainOid+"}");
	        			$.form.submit({
                    		url: '../cls/cls3101m01/01',
                    		data: {
                        		oid: json.mainOid,
                        		mainOid: json.mainOid,
                        		mainDocStatus: viewstatus,
                        		txCode: txCode
                    		},
                    		target: json.mainOid
               	 		});
	    		});
	    	});
	    });
    }).end().find("#btnDelete").click(function(){ //編製中-刪除
    	var row = $gridview.getGridParam('selrow');
		var list = "";
		if(row){
			var data = $gridview.getRowData(row);
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){
					$.ajax({
						handler : _handler,
						type : "POST",
						dataType : "json",
						data :{
							'formAction' : 'delC310M01A',
							'oid' : data.oid,
							'mainOid' : data.oid,
							'mainId' : data.mainId,
							'mainDocStatus' : viewstatus
						}
						}).done(function(obj) {
				        	$gridview.trigger("reloadGrid");
				          	API.showMessage(i18n.def.runSuccess);      
					});
				}
			})				
		}else{
			API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
			return;
		}
    });
    
    
    function chose_custId(){	
		var my_dfd = $.Deferred();
		AddCustAction.open({
	    		handler: 'cls2501m01formhandler',
				action : 'echo_custId',
				data : {
	            },
				callback : function(json){					
	            	// 關掉 AddCustAction 的 
	            	$.thickbox.close();					
					my_dfd.resolve( json );					
				}
			});
		return my_dfd.promise();
	}
    
    function chose_text(resultFrom_chose_custId){
		var my_dfd = $.Deferred();
		
		TextGrid.jqGrid("setGridParam", {
            postData: {
                'custId': resultFrom_chose_custId.custId
				,'dupNo': resultFrom_chose_custId.dupNo
            },
            search: true
        }).trigger("reloadGrid");

		$("#TextThickBox").thickbox({
	       title: (resultFrom_chose_custId.custId + "-" +resultFrom_chose_custId.dupNo), 
	       width: 600,height: (text_grid_height+160),align: "center",valign: "bottom",
           modal: false, i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = TextGrid.getSingleData();
                     if (data) {
						 $.thickbox.close();
						 //---
                    	 var refMainId = data.mainId;
        				 my_dfd.resolve($.extend(resultFrom_chose_custId, {'refMainId':refMainId} ));
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
});
