<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<fieldset>
			<legend><b><th:block th:text="#{'l120s02.tag1'}">基本資料</th:block></b></legend>
			<table width="100%">
			  <tbody>
				<tr>
					<td valign="top">
						<button type="button" onclick="overSeaCLSPage_reg_imp_custData()"><span class="text-only"><th:block th:text="#{'l120s01a.btn7'}">引進借保人資料</th:block></span></button>
					</td>
					<td align="right">
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<span style="color:#000066"><th:block th:text="#{'l120s02.index1'}">申貸人(1)</th:block></span>
					</td>
				</tr>
			  </tbody>
			</table>	
			<!--===============================-->
			<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
			  <tbody>
			  	<tr>
					<td class="hd1"><th:block th:utext="#{'l120s02.index2'}">個　人　授　信　戶&nbsp;&nbsp;<br>負責事業體統一編號&nbsp;&nbsp;</th:block></td>
					<td><input type="text" id="cmpId" name="cmpId" maxlength="10" /></td>
					<td class="hd1"><th:block th:utext="#{'l120s02.index3'}">個&nbsp;&nbsp;人&nbsp;&nbsp;授&nbsp;&nbsp;信&nbsp;&nbsp;戶&nbsp;&nbsp;<br>負責事業體名稱&nbsp;&nbsp;</th:block></td>
					<td><input type="text" id="cmpNm" name="cmpNm" maxlength="120" maxlengthC="40" /></td>
				</tr>
				<tr>
					<td class="hd1" width="30%"><b class="star">＊</b><th:block th:text="#{'l120s01a.typcd'}">DBU／OBU／海外&nbsp;&nbsp;</th:block>&nbsp;&nbsp;</td>
					<td width="25%"> <span id="typCd" ></span></td>
					<td class="hd1" width="17%"><th:block th:text="#{'l120s01a.keyman'}">主要借款人</th:block>&nbsp;&nbsp;</td>
					<td width="28%">
						<th:block th:if="${hs_baseData_Y1}">
							<!--
							以下為 hidden 欄位
							-->		
							<input type='hidden' id='keyMan' name='keyMan' value=''>	
						</th:block>						
						<th:block th:if="${hs_baseData_N1}">
							<!--
							p.s. 企業戶在消金簽報書，不能為主要借款人
							-->
							<label><input class="max" maxlength="1" id="keyMan" name="keyMan" type="checkbox" value='Y' /><th:block th:text="#{'l120s01a.checkbox1'}">是</th:block></label>				
						</th:block>						
						
						
						<!--
						以下為 hidden 欄位
						-->
						<th:block th:if="${hs_baseData_Y}">
							<input type='hidden' id='o_custRlt' name='o_custRlt' value=''>
							<input type='hidden' id='custPos' name='custPos' value=''>				
						</th:block>	
					</td>
				</tr>
				<th:block th:if="${hs_baseData_N}">
					<tr class='hs_by_keyMan'>
						<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s01a.custrlt'}">與主要借款人關係</th:block></td>
						<td>
							<button type="button" onclick="overSeaCLSPage_reg_o_custRlt()"><span class="text-only"><th:block th:text="#{'l120s01a.btn5'}">登錄</th:block></span></button>
							
							<span class="field" id="o_custRlt" name="o_custRlt"></span>&nbsp;
							<span id="o_custRltDesc" name="o_custRltDesc"></span>			
						</td>
						<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s01a.custpos'}">相關身份</th:block>&nbsp;&nbsp;</td>
						<td>
							<select class="max" maxlength="1" id="custPos" name="custPos" codetype='lms1015_custPos' itemStyle="format:{value}-{key};space:true" >
							</select>
						</td>
					</tr>
				</th:block>	
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'label.custId'}">身分證統一編號</th:block>&nbsp;&nbsp;</td>
					<td><span class="required max" maxlength="10" id="custId" name="custId"></span>&nbsp;<th:block th:text="#{'l120s01a.dupno'}"><th:block th:text="#{'l120s02.index5'}">重覆序號</th:block></th:block>： 
					<span class="required max" maxlength="1" id="dupNo" name="dupNo"></span></td>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index6'}">姓　　名</th:block>&nbsp;&nbsp;</td>
					<td>
						<!-- 原本用 span class='field' 來呈現 custName
						但海外客戶若名字有 &，會跑出 amp;
						改用 textarea 來避免此問題  
						-->	
						<textarea id="custName" class="max txt_mult" style="line-height:12px;" readonly="readonly" rows="1" cols="30" name="custName">
						</textarea>
					</td>
				</tr>
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index7'}">出生日期</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="birthday" name="birthday" class="date"  /></td>
					<td class="hd1"><th:block th:text="#{'l120s02.index8'}">客戶編號</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="custNo" name="custNo" class="max obuText upText" maxlength="60" /></td>
				</tr>
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s01b.busCode'}">行業對象別</th:block>&nbsp;&nbsp;</td>
					<td>
						<span id="busCode" name="busCode" class='field'  ></span>
						<span id="ecoNm"   name="ecoNm"   class='field'  ></span>
						<div>
						    <button type="button" onclick="overSeaCLSPage_reg_imp_busCode()">
								<span class="text-only"><th:block th:text="#{'l120s01a.btn4'}">重新引進</th:block></span>
							</button>
						</div>
					</td>
					
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s01b.custClass'}">客戶類別</th:block>&nbsp;&nbsp;</td>
					<td>
					  	<select id="o_custClass" name="o_custClass" codeType="lms1201s02_custClass" itemStyle="format:{value}-{key};space:true;" > 
					  	</select>  
						<!--
						以下為 hidden 欄位
						-->
						<input id="o_crdType" name="o_crdType" type="hidden" value="" />
						<input id="o_grade"   name="o_grade"   type="hidden" value="" />
					</td>
				</tr>				
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index9'}">學　　歷</th:block>&nbsp;&nbsp;</td>
					<td>
						<span class="ps1"><th:block th:text="#{'l120s02.index10'}">※肄業者不計，例如大學肄業請填高中</th:block></span>
						<br />
						<select id="edu" name="edu" codetype='lms1205s01_edu'>
						</select>
					</td>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index11'}">性　　別</th:block>&nbsp;&nbsp;</td>
					<td>
						<label><input id="sex" name="sex" type="radio" value="M" /><th:block th:text="#{'l120s01a.radio10'}">男</th:block></label>
						<label><input id="sex" name="sex" type="radio" value="F" /><th:block th:text="#{'l120s01a.radio11'}">女</th:block></label>
					</td>
				</tr>
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index12'}">婚姻狀況</th:block>&nbsp;&nbsp;</td>
					<td class="">
						<label><input id="marry" name="marry" type="radio" value="1" /><th:block th:text="#{'l120s01a.radio12'}">未婚</th:block></label>
						<label><input id="marry" name="marry" type="radio" value="2" /><th:block th:text="#{'l120s01a.radio13'}">已婚</th:block></label>
						<label><input id="marry" name="marry" type="radio" value="4" /><th:block th:text="#{'l120s01a.radio14'}">離婚</th:block></label>
						<label><input id="marry" name="marry" type="radio" value="5" /><th:block th:text="#{'l120s01a.radio15'}">殁</th:block></label>
						<br />
						，<th:block th:text="#{'l120s02.index13'}">子女數</th:block><input type="text" id="child" name="child" class="max number" maxlength="2" size="5" /><th:block th:text="#{'l120s02.index14'}">人</th:block>
					</td>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index15'}">國　　別</th:block>&nbsp;&nbsp;</td>
					<td>
						<select class="max" maxlength="2" id="ntCode" name="ntCode" codetype='CountryCode' itemStyle="format:{value}-{key}">
						</select>
					</td>
				</tr>
				<tr class="">
					<td class="hd1"><th:block th:text="#{'l120s02.index16'}">通訊電話</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="coTel" name="coTel" class="max phone" maxlength="20" /></td>
					<td class="hd1"><th:block th:text="#{'l120s02.index17'}">戶籍電話</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="fTel" name="fTel" class="max phone" maxlength="20" /></td>
				</tr>
				<tr class="">
					<td class="hd1"><th:block th:text="#{'l120s02.index18'}">行動電話</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="mTel" name="mTel" class="max phone" maxlength="20" /></td>
					<td class="hd1">E-mail&nbsp;&nbsp;</td>
					<td><input type="text" id="email" name="email" class="max email" maxlength="48" /></td>
				</tr>
				<tr class="">
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index19'}">戶籍地址</th:block>&nbsp;&nbsp;</td>
					<td colspan="3"><input type="text" id="fAddr" name="fAddr" class="max" size="90" maxlength="192" maxlengthC="64" /></td>
				</tr>
				<tr class="">
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index20'}">通訊地址</th:block>&nbsp;&nbsp;<br><button type="button" onclick="overSeaCLSPage_coAddr_sameAs_fAddr();"><span class="text-only"><th:block th:text="#{'l120s01a.btn8'}">同戶籍地址</th:block></span></button></td>
					<td colspan="3"><input type="text" id="coAddr" name="coAddr" class="max" size="90" maxlength="192" maxlengthC="64" /></td>
				</tr>
				<tr class="">
					<td class="hd1"><th:block th:text="#{'l120s02.index21'}">現住房屋</th:block>&nbsp;&nbsp;</td>
					<td>
						<select id="houseStatus" name="houseStatus" codetype='lms1205s01_houseStatus'>
						</select>
					</td>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index22'}">不動產狀況</th:block>&nbsp;&nbsp;</td>
					<td>
						<select id="cmsStatus" name="cmsStatus" codetype='lms1205s01_cmsStatus'>
						</select>
					</td>
				</tr>
				<tr class="">
					<td class="hd1"><th:block th:text="#{'l120s02.index23'}">存款往來</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="dpBankName" name="dpBankName" class="max" maxlength="30" maxlengthC="10" /><th:block th:text="#{'l120s02.index24'}">分行</th:block></td>
					<td class="hd1"><th:block th:text="#{'l120s02.index25'}">存款帳戶</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="dpAcct" name="dpAcct" class="max obuText" maxlength="16" /></td>
				</tr>
				<tr>
					<td class="hd1"><th:block th:text="#{'l120s02.index88'}">行員編號</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="megaEmpNo" name="megaEmpNo" class="max obuText" maxlength="6"/></td>
					<td class="hd1"><th:block th:text="#{'C120S01A.newCustFlag'}">新往來客戶註記</th:block>&nbsp;</td>
					<td >
						<label><input type="radio" id="newCustFlag" name="newCustFlag" value="Y" disabled="true"/><th:block th:text="#{'C120S01A.newCustFlag.Y'}">是</th:block></label>
						<label><input type="radio" id="newCustFlag" name="newCustFlag" value="N" disabled="true"/><th:block th:text="#{'C120S01A.newCustFlag.N'}">否</th:block></label>
					</td>
				</tr>
			  </tbody>
			</table>
		</fieldset>
		<!--=====================================================================-->		
		<div id="thickboxCustRlt" style="display:none">
				
			<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td width="30%" class="hd1">
						<th:block th:text="#{'l120s02.other13'}">關係類別</th:block>
					</td>
					<td width="70%">
						<select id="custRlt_main" name="custRlt_main" 
							codetype='lms1205s01_RelClass'  itemStyle="space:false" />
					</td>
				</tr>
				<tr>
					<td  class="hd1">
					&nbsp;
					</td>
					<td>
						<span id="custRlt_main1" class='custRlt_mainV' style="display:none;">
		                	<select name="rationSelect1"  codetype='Relation_type1'  class='custRlt_sel'></select>
		                </span>
		                
		                <span id="custRlt_main2" class='custRlt_mainV' style="display:none;">
		                    <select name="rationSelect2"  codetype='Relation_type2'  class='custRlt_sel'></select>
		                </span>
						
		                <span id="custRlt_main3" class='custRlt_mainV' style="display:none;">                    
		                    <select name="rationSelect31" codetype='Relation_type31' class='custRlt_sel'></select>
		                    <select name="rationSelect32" codetype='Relation_type32' class='custRlt_sel'></select>
		                </span>
									
					</td>
				</tr>
			</table>
		</div>
		<!--=====================================================================-->
		<div id="thickBoxImp_custData" style="display:none">
			<table class="tb2" width="100%">
				<tr id="tr_s_coAddr">
					<td width="40%" class="hd1"><th:block th:text="#{'l120s02.index20'}">通訊地址</th:block>&nbsp;</td>
					<td width="60%"><select id="s_coAddr" name="s_coAddr"></select></td>
				</tr>
				<tr id="tr_s_comTel">
					<td class="hd1">服務單位電話&nbsp;</td>
					<td><select id="s_comTel" name="s_comTel"></select></td>
				</tr>
				<tr id="tr_s_mTel">
					<td class="hd1"><th:block th:text="#{'l120s02.index18'}">行動電話</th:block>&nbsp;</td>
					<td><select id="s_mTel" name="s_mTel"></select></td>
				</tr>
				<tr id="tr_s_email">
					<td class="hd1">E-mail&nbsp;</td>
					<td><select id="s_email" name="s_email"></select></td>
				</tr>
			</table>
		</div>
	</th:block>
</body>
</html>