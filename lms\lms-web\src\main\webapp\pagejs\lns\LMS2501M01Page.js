var initDfd = $.Deferred(), inits = {
    fhandle: "",
    //使用動審表使用的gridhandler
    ghaddle: "lms1601gridhandler"
};
$(document).ready(function(){
    $.form.init({
        formId: "tabForm",
        formHandler: "lms2501m01formhandler",
        formPostData: {
            formAction: "initForm"
        },
		
		//UPGRADE
		
        loadSuccess: function(sJson){
            //非編製中設為唯讀且不套用tempSave
            var tabForm = $("#tabForm");
            
            if (tabForm.find("#mainDocStatus").val() != "01O") {
                setIgnoreTempSave(true);
                
                var auth = (responseJSON ? responseJSON.Auth : {}); //權限
                if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
                    $("form").lockDoc();
                }
            }
            if (tabForm.find("#mainDocStatus").val() == "02O") {
                $("#btnCheck, #btnReturn").show();
            }
			
			const baseJson = sJson;    // page-1 資料先留下來

			            $.ajax({
			                handler: "lms2501m01formhandler",
			                data   : {
			                    formAction: "initForm",
			                    page      : 2,
			                    mainOid   : baseJson.mainOid        // 讓後端知道要撈哪一筆
			                }
			            }).done(function (page2) {

			                /* 後端 listData 可能是字串 → 轉成陣列保險 */
			                if (typeof page2.listData === "string") {
			                    try { page2.listData = JSON.parse(page2.listData); }
			                    catch (e) { page2.listData = []; }
			                }

			                /* 合併兩份資料，只 resolve 一次 */
			                const merged = $.extend({}, baseJson, { listData: page2.listData });
			                initDfd.resolve(merged);

			            }).fail(function () {
			                /* 抓 page-2 失敗就至少把 page-1 資料丟出去，避免永遠卡住 */
			                initDfd.resolve(baseJson);
			            });

        }
    });
    function acceptAction(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            //l230m01a.title22=覆核
            title: i18n.lms2501m01['l250m01a.message.07'],
            width: 100,
            height: 150,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    var val = $("input[name='checkRadio']:checked").val();
                    if (!val) {
                        //l250m01a.message.04=請選擇
                        return CommonAPI.showMessage(i18n.lms2501m01['l250m01a.message.04']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            //一般退回到編製中01O
                            //l230m01a.title23=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms2501m01['l250m01a.message.05'], function(b){
                                if (b) {
                                
                                
                                    $.ajax({
                                        type: "POST",
                                        handler: "lms2501m01formhandler",
                                        data: $.extend({
                                            formAction: "flowAction",
                                            mainOid: responseJSON.mainOid,
                                            txCode: responseJSON.txCode
                                        }, ({
                                            flowAction: "back"
                                        } || {}))
									}).done(function(){
										CommonAPI.triggerOpener("gridview", "reloadGrid");
										API.showPopMessage(i18n.def["runSuccess"], window.close);
									});

                                }
                            });
                            break;
                        case "2":
                            //核定
                            //l250m01a.message.06=該案件是否核准？確定請按【確定】，否則請按【取消】離開
                            CommonAPI.confirmMessage(i18n.lms2501m01["l250m01a.message.06"], function(b){
                                if (b) {
                                    //LMS2305Action.checkDate();
                                    $.ajax({
                                        type: "POST",
                                        handler: "lms2501m01formhandler",
                                        data: $.extend({
                                            formAction: "flowAction",
                                            mainOid: responseJSON.mainOid,
                                            txCode: responseJSON.txCode
                                        }, ({
                                            flowAction: "ok"
                                        } || {})),
									}).done(function(){
										CommonAPI.triggerOpener("gridview", "reloadGrid");
										API.showPopMessage(i18n.def["runSuccess"], window.close);									
                                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };
    
    var saveAction = function(sJson){
    
        var tabForm = $("#tabForm");
        var flag = true;
        
        //flag = saveCheck();
		var ajaxCall = API.flowConfirmAction({
		        message: false,
		        handler: "lms2501m01formhandler",
		        action: "saveDoc",
		        data: $.extend({tabForm:JSON.stringify($("#tabForm").serializeData())},
					$("#tabForm").serializeData(), {
		            listData: responseJSON.page == '02' ? JSON.stringify(prasePage2ListData()) : []
		        })
		    });
		
		if (ajaxCall && $.isFunction(ajaxCall.done)) {
		    // 確認返回 jqXHR / Deferred，才綁 .done()
		    if (sJson?.success instanceof Function) {
		        ajaxCall.done(sJson.success);
		    }
		} else {
		    // flowConfirmAction 已經自行完成 (直接送、直接成功) || 確保.done是callback function，防止jQuery3.6.0報錯
		    if (sJson?.success instanceof Function) {
		        sJson.success();          // 手動觸發 callback
		    }
		}
/*
        flag &&
        API.flowConfirmAction({
            message: false,
            handler: "lms2501m01formhandler",
            action: "saveDoc",
            data: $.extend($("#tabForm").serializeData(), {
                listData: responseJSON.page == '02' ? JSON.stringify(prasePage2ListData()) : []
            })
		}).done(sJson.success);
*/
    }
    
    $("#buttonPanel").find("#btnSave").click(function(){
        var oid = $("#mainOid").val();
        if (oid) {
            saveAction({
                success: function(){
                    API.showMessage(i18n.def.saveSuccess);
                }
            });
        }
        
    }).end().find("#btnCheck").click(function(){
        acceptAction();
    }).end().find("#btnPrint").click(function(){
        var printAction = function(){
            $.form.submit({
                url: "../../simple/FileProcessingService",
                target: "_blank",
                data: {
                    mainId: responseJSON.mainId,
                    mainOid: $("#mainOid").val(),
                    fileDownloadName: "lms2501r01.pdf",
                    serviceName: "lms2501r01rptservice"
                }
            });
        }
        var save = $("#buttonPanel").find("#btnSave:visible");
        if (save.length) {
            saveAction({
                success: function(){
                    API.showMessage(i18n.def.saveSuccess);
                    printAction();
                }
            });
        }
        else {
            printAction();
        }
    }).end().find("#btnExit").click(function(){
        API.triggerOpener();
        window.close();
    }).end().find("#btnSend").click(function(){
        var oid = $("#mainOid").val();
        oid &&
        saveAction({
            success: function(){
                $.ajax({
                    handler: "lms2501m01formhandler",
                    data: {
                        formAction: "checkBeforSend"
                    }
				}).done(function(json){
                        $(".boss").setItems({
                            item: json.bossList
                        });
                        //confirmApply=是否呈主管覆核？
                        CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b){
                            if (b) {
                                $("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
                                    //l250m01a.message.01=呈主管覆核
                                    title: i18n.lms2501m01['l250m01a.message.01'],
                                    width: 500,
                                    height: 180,
                                    modal: true,
                                    valign: "bottom",
                                    align: "center",
                                    readOnly: false,
                                    i18n: i18n.def,
                                    buttons: {
                                        "sure": function(){
                                            var account = $("#accountPerson").val();
                                            //var manager = $("#manager").val();
                                            if (account == "") {
                                                //l230m01a.title11=授信主管
                                                return CommonAPI.showErrorMessage(i18n.lms2501m01['l250m01a.message.02']);
                                            }
                                            //if (manager == "") {
                                            //    //l230m01a.title10=經副襄理
                                            //    return CommonAPI.showErrorMessage(i18n.lms2501m01['l250m01a.message.03']);
                                            //}
                                            
                                            var sendData = {
                                                page: responseJSON.page,
                                                account: account
												//,manager: manager
                                            };
                                            $.ajax({
                                                type: "POST",
                                                handler: "lms2501m01formhandler",
                                                data: $.extend({
                                                    formAction: "flowAction",
                                                    mainOid: responseJSON.mainOid,
                                                    txCode: responseJSON.txCode
                                                }, (sendData || {}))
												
											}).done(function(){
												CommonAPI.triggerOpener("gridview", "reloadGrid");
												API.showPopMessage(i18n.def["runSuccess"], window.close);
											});
                                              
                                            
                                            $.thickbox.close();
                                        },
                                        
                                        "cancel": function(){
                                            $.thickbox.close();
                                        }
                                    }
                                });
                            }
                        });
					
					
                });
            }
        });
        
        
    });
});


/**
 * 將第二2頁的查詢項目格式組成特定的json資料，上傳server
 */
var prasePage2ListData = function(){
	var sendData  = [];

	    /* ① 找所有 group_xxx 的 <span> -------------------------------- */
	    $("#checkListDetail")
	        .find("span.field[name^='group_']")
	        .each(function () {

	        var $groupSpan = $(this);

	        /* === 修改開始：安全取得 name / 文字 ========================= */
	        var nameAttr = $groupSpan.attr("name") || "";      // 沒有 name 就給空字串
	        if (!nameAttr.startsWith("group_")) { return; }    // 防呆，直接跳下一筆

	        var no          = nameAttr.slice(6);               // 去掉 "group_"
	        var groupTitle  = $.trim($groupSpan.text());       // .val() → .text()
	        /* === 修改結束 ============================================= */

	        var js = {
	            group      : no,
	            groupTitle : groupTitle
	        };

	        /* ② 判斷是否顯示標題列 ------------------------------------- */
	        var showHead = $("span.field[name='yesTitle_" + no + "']").length ? "Y" : "N";
	        js.showHead  = showHead;

	        if (showHead === "Y") {
	            js.yesTitle = $.trim($("span.field[name='yesTitle_" + no + "']").text());
	            js.noTitle  = $.trim($("span.field[name='noTitle_" + no + "']").text());
	            js.naTitle  = $.trim($("span.field[name='naTitle_" + no + "']").text());
	        } else {
	            js.yesTitle = js.noTitle = js.naTitle = "";
	        }

	        /* ③ subItems ------------------------------------------------ */
	        var subItems = [];
	        $("#checkListDetail")
	            .find("span[name^='subTitle_" + no + "_']")
	            .each(function () {

	            var $subSpan = $(this);

	            /* === 修改開始：name / 文字防呆 ========================== */
	            var subNameAttr = $subSpan.attr("name") || "";
	            if (!subNameAttr) { return; }

	            var subNo = subNameAttr.replace("subTitle_" + no + "_", "");
	            /* === 修改結束 ========================================== */

	            var selector = "#checkListDetail input[name='sub_" + no + "_" + subNo + "']";

	            var subItem = {
	                subTitle   : $.trim($subSpan.text()),
	                subItem    : subNo,
	                yes        : $(selector + "[value='Y']").length ? "Y" : "N",
	                no         : $(selector + "[value='N']").length ? "Y" : "N",
	                na         : $(selector + "[value='NA']").length ? "Y" : "N",
	                checkValue : $(selector + ":checked").val() || "",
	                subRejectVal : $("#checkListDetail input[name='subRejectVal_" + no + "_" + subNo + "']").val() || ""
	            };
	            subItems.push(subItem);
	        });

	        js.subItems = subItems;
	        sendData.push(js);
	    });

	    ilog.debug(sendData);
	    return sendData;
}

//畫面切換table所需設定之資料如無設定則直接切換
$.extend(window.tempSave, {
    handler: "lms2501m01formhandler",
    action: "tempSave",
    beforeCheck: function(){
        var tabForm = $("#tabForm");
        return true;
    },
    sendData: function(){
		return $.extend({
		    formAction: "tempSave",
		    txCode: responseJSON.txCode,
		    page: responseJSON.page,
		    mainOid: $("#mainOid").val(),
		    docUrl: "/lms/lms2501m01", // 如果後端需要這個欄位
		    tabForm: JSON.stringify($("#tabForm").serializeData()),
		    listData: responseJSON.page == '02' ? JSON.stringify(prasePage2ListData()) : []
		}, $("#tabForm").serializeData());
    }
});
