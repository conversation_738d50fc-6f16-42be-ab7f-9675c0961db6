/* 
 * L140MM6A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 共同行銷維護作業主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140MM6A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140MM6A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 原負責經辦 **/
	@Size(max=6)
	@Column(name="APPRAISER", length=6, columnDefinition="CHAR(6)")
	private String appraiser;

	/** 知會經辦 **/
	@Size(max=6)
	@Column(name="INFOAPPRAISER", length=6, columnDefinition="CHAR(6)")
	private String infoAppraiser;

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得原負責經辦 **/
	public String getAppraiser() {
		return this.appraiser;
	}
	/** 設定原負責經辦 **/
	public void setAppraiser(String value) {
		this.appraiser = value;
	}

	/** 取得知會經辦 **/
	public String getInfoAppraiser() {
		return this.infoAppraiser;
	}
	/** 設定知會經辦 **/
	public void setInfoAppraiser(String value) {
		this.infoAppraiser = value;
	}
}
