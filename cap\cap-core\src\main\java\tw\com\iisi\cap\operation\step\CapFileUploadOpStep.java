/*
 * CapFileUploadOpStep.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.operation.step;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FormHandler;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapMath;

/**
 * <pre>
 * 檔案上傳
 * </pre>
 * 
 * @since 2010/7/23
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/23,iristu,new
 *          <li>2012/2/29,iristu,上傳檔案預設最大size(limitSize)，當接收檔案超出時，則拒絕接收。
 *          </ul>
 */
public class CapFileUploadOpStep extends AbstractCustomizeOpStep {

    protected static Log logger = LogFactory.getLog(CapFileUploadOpStep.class);

    /**
     * {@value #ActionFileUpload}
     */
    static final String ActionFileUpload = "FileUpload";

    /**
     * 檔案解析器
     */
    private CommonsMultipartResolver multipartResolver;

    /**
     * Requset大小上限
     */
    int maxRequestSize = 5 * 1024 * 1024; // 預設為5MB

    /**
     * 超出檔案限制, 錯誤i18n鍵值
     */
    String fileSizeLimitErrorCode;

    /**
     * 設置Requset大小上限
     * 
     * @param maxRequestSize
     *            the maxRequestSize to set
     */
    public void setMaxRequestSize(int maxRequestSize) {
        this.maxRequestSize = maxRequestSize;
    }

    /**
     * 設置超出檔案限制的錯誤i18n鍵值
     * 
     * @param fileSizeLimitErrorCode
     *            the fileSizeLimitErrorCode to set
     */
    public void setFileSizeLimitErrorCode(String fileSizeLimitErrorCode) {
        this.fileSizeLimitErrorCode = fileSizeLimitErrorCode;
    }

    /**
     * 執行 {@linkplain #uploadFile(PageParameters) uploadFile(params)} 後繼續下一步
     * 
     * @return {@value tw.com.iisi.cap.operation.OperationStep#NEXT}
     */
    @Override
    public String execute(PageParameters params, FormHandler handler, IResult result) throws CapException {
        uploadFile(params);
        return NEXT;
    }

    /**
     * 上傳檔案
     * 
     * @param params
     * @return
     * @throws CapException
     */
    protected MultipartHttpServletRequest uploadFile(PageParameters params) throws CapException {
        // [refs#164] 如果前端有傳 limitSize，以前端指定大小檢核，否則使用預設 maxRequestSize(設定在 operation.xml)
        if (params.containsKey("limitSize")) {
            multipartResolver.setMaxUploadSize(params.getAsInteger("limitSize"));
        } else {
            multipartResolver.setMaxUploadSize(maxRequestSize);
        }
        if (params.containsKey("fileEncoding")) {
            multipartResolver.setDefaultEncoding(params.getString("fileEncoding"));
        }

        try {
            return multipartResolver.resolveMultipart((HttpServletRequest) params.getServletRequest());
        } catch (MaxUploadSizeExceededException fe) {
            CapMessageException me = new CapMessageException(fileSizeLimitErrorCode, getClass());
            Map<String, String> extra = new HashMap<>();
            extra.put("fileSize", CapMath.divide(String.valueOf(fe.getMaxUploadSize()), "1048576", 2));
            me.setMessageKey(fileSizeLimitErrorCode);
            me.setExtraInformation(extra);
            throw me;
        } catch (MultipartException e) {
            throw new CapException(e, getClass());
        }

    }

    /**
     * 設置解析器
     * 
     * @param multipartResolver
     *            the multipartResolver to set
     */
    public void setMultipartResolver(CommonsMultipartResolver multipartResolver) {
        this.multipartResolver = multipartResolver;
    }

}// ~
