package tw.com.jcs.auth.model;

import java.io.Serializable;
import java.util.Set;

/**
 * <pre>
 * User
 * </pre>
 * 
 * @since 2022年12月13日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月13日
 *          </ul>
 */
public interface User extends Serializable {

    /**
     * 取得Id
     * 
     * @return
     */
    String getId();

    /**
     * 取得Name
     * 
     * @return
     */
    String getName();

    /**
     * 取得Dept
     * 
     * @return
     */
    Department getDepartment();

    /**
     * 取得Position
     * 
     * @return
     */
    String getPosition();

    /**
     * 取得Roles
     * 
     * @return
     */
    Set<String> getRoles();

    /**
     * 取得LoginTime
     * 
     * @return
     */
    String getLoginTime();
}