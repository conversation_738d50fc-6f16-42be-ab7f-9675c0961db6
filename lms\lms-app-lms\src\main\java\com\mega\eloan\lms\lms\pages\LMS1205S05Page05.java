package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.lms.panels.LMS1205S05Panel02;
import com.mega.eloan.lms.lms.panels.LMS1205S05Panel03;

/**
 * <pre>
 * 說明(企金授權外) - 營運概況
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205S05E")
public class LMS1205S05Page05 extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		new LMS1205S05Panel02("lms1205s05panel02").processPanelData(model, params);
		new LMS1205S05Panel03("lms1205s05panel03").processPanelData(model, params);
		renderJsI18N(LMS1205S05Page05.class);
	}

	@Override
    public String getViewName() {
        return "common/pages/None";
    }


	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
