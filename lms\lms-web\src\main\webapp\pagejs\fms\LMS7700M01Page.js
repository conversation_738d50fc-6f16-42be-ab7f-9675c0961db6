
var initDfd = $.Deferred(), inits = {
    fhandle: "lms7700m01formhandler",
    ghandle: "lms7700gridhandler"
};

//select source
var source = CommonAPI.loadCombos(["lms7700_reason","lms7700_dataFrom","lms7700_sys"]);

var Action = {
	_isLoad: false,
	_initForm: function(){
		$.ajax({
		    handler: inits.fhandle,
		    data: {//把資料轉成json
		        formAction: "queryLMS7700M01",
		        oid: responseJSON.oid,
				mainId: responseJSON.mainId
		    },
		    success: function(obj){
		    	$('body').injectData(obj);
		    	$("#mainId").val(obj.mainId);
		    }
		});
	},
	_initItem: function(){
	    $("[name=reasonSelect]").setItems({
	        item: source.lms7700_reason,
	        format: "{key}"
	    });
	    $("#sys").setItems({
	    	item: source.lms7700_sys,
	    	format: "{key}"//format: "{value} - {key}"
	    });
	},
	_initEvent: function(){
		$("input[name='isDeleteRadio']").change(function(k, v){		
            var value = $(this).val();
			
            if (value == "N") {
            	$("#reasonView").show();
            } else if(value == "Y") {
            	$("#reasonView").hide();
			}
        });
	},
	_initGrid: function(){
		this.listGrid = $("#listGrid").iGrid({
			height: 500,
			handler: inits.ghandle,
			sortname: 'createTime',
			sortorder: 'asc',
			action: "queryL140mm5c",
			postData: {
				mainId: responseJSON.mainId
			},
			multiselect: true, //選項前多checkbox
			colModel: [{
		        colHeader: i18n.lms7700m01["L140MM5A.custId"],
		        align: "left", width: 50, sortable: true, name: 'custId'
		    },{
		        colHeader: i18n.lms7700m01["L140MM5A.dupNo"],
		        align: "left", width: 17, sortable: true, name: 'dupNo'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.custName"],
		        align: "left", width: 50, sortable: true, name: 'custName'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.ces"],
		        align: "center", width: 10, sortable: false, name: 'ces'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.cms"],
		        align: "center", width: 14, sortable: false, name: 'cms'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.col"],
		        align: "center", width: 10, sortable: false, name: 'col'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.lms"],
		        align: "center", width: 10, sortable: false, name: 'lms'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.rps"],
		        align: "center", width: 17, sortable: false, name: 'rps'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.isDelete"],
		        align: "center", width: 20, sortable: false, name: 'isDelete'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.reason"],
		        align: "center", width: 23, sortable: false, name: 'reason'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.dataFrom"],
		        align: "center", width: 16, sortable: false, name: 'dataFrom'
		    }, {
		        colHeader: i18n.lms7700m01["L140MM5A.closeDate"], 
		        align: "center",
		        width: 20, // 設定寬度
		        sortable: false, // 是否允許排序
		        formatter: 'date',
		        formatoptions: {
		            srcformat: 'Y-m-d',
		            newformat: 'Y-m-d'
		        },
		        name: 'closeDate' // col.id
		    }, {
				colHeader: "oid",
				name: 'oid',
				hidden: true
			}]
		});
	},
	_init: function(){
        if (!this._isLoad) {
        	this._initForm();
        	this._initItem();
        	this._initGrid();
        	this._initEvent();
            this._isLoad = true;            
        } else {
            this._reloadGrid();
        }
    },
    getSelectItem: function(){
        var data = [];
        $("#itemSpan_sys").find("[name=sys]:checked").each(function(v, k){
            data.push($(k).val());
        });
        return data.join("|");
    }
}

// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

$(document).ready(function(){
	var tabForm = $("#mainPanel");
	Action._init();
	$("#check1").show();
	
    if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
		_openerLockDoc="1";
    }
    
	// 呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. '
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" />';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }     
    });
	
	var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnSend").click(function(){
        saveData(false, sendBoss);	
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
    });
    
    $("#btnAddRow").click(function(){
    	chose_custId().done(function(result){
    		$.ajax({
    			type: "POST",
    			handler: "lms7700m01formhandler",
    			data: {
                    formAction: "queryElData",
                    mainId: responseJSON.mainId,
                    custId: result.custId,
                    dupNo: result.dupNo,
                    custName: result.custName
                },
                success: function(json){
        	    	$("#addBox").thickbox({
        	            title: i18n.lms7700m01['button.addRow'],
        	            width: 500,
        	            height: 250,
        	            valign: "bottom",
        	            align: "center",
        	            i18n: i18n.def,
        	            open : function() {
        					$(this).find("#addForm").reset();
        					
        					var custId = result.custId;
        		    		var dupNo = result.dupNo;
        		    		var custName = result.custName;
        		    		
        		    		$("#custId").val(custId);
        		    		$("#dupNo").val(dupNo);
        		    		$("#custName").val(custName);
        		    		
        		    		var sysArray = json.sysList.split("|");
        		    		for (var i = 0; i < sysArray.length; i++) {
        						var sys = sysArray[i];
        						$("[name=sys][value=" + sys + "]").attr("checked", true);
        					}
        		    		$("[name=sys]").attr("disabled", true);
        				},
        	            buttons: {
        	                "sure": function(){
        	                	var regEx = /^\d{4}-\d{2}-\d{2}$/;
        	            			
        	                	var custId = $("#custId").val();
        	                	var dupNo = $("#dupNo").val();
        	                	var custName = $("#custName").val();
        	                	var sys = Action.getSelectItem;
        	                	var closeDate = $("#closeDate").val();

        	                	if(!custId){
        	                		return CommonAPI.showErrorMessage(i18n.lms7700m01['L140MM5A.custId'] + 
        	                				i18n.lms7700m01['cantEmpty']);
        	                	} else if(!dupNo){
        	                		return CommonAPI.showErrorMessage(i18n.lms7700m01['L140MM5A.dupNo'] +
        	                				i18n.lms7700m01['cantEmpty']);
        	                	} else if(!custName){
        	                		return CommonAPI.showErrorMessage(i18n.lms7700m01['L140MM5A.custName'] +
        	                				i18n.lms7700m01['cantEmpty']);
        	                	} else if(checkSys()){
        	                		return CommonAPI.showErrorMessage(i18n.lms7700m01['L140MM5A.sys'] +
        	                				i18n.lms7700m01['cantEmpty']);
        	                	} else if(closeDate && !closeDate.match(regEx)){
        	                		return CommonAPI.showErrorMessage(i18n.lms7700m01['L140MM5A.closeDate'] +
        	                				i18n.lms7700m01['notMatchFormat']);
        	                	}
        	                    
        	                	$.ajax({
        	                        handler: "lms7700m01formhandler",
        	                        data: {
        	                            formAction: "addL140mm5c",
        	                            mainId: responseJSON.mainId,
        	                            custId: custId,
        	                            dupNo: dupNo,
        	                            custName: custName,
        	                            sys: Action.getSelectItem,
        	                            closeDate: closeDate
        	                        },
        	                        success: function(obj){
        	                            $("#listGrid").trigger("reloadGrid");
        	                        }
        	                    });
        	                    $.thickbox.close();                    
        	                },
        	                "cancel": function(){
        	                    $.thickbox.close();
        	                }
        	            }
        	        });
                }
    		});
    	});
    });
    
    $("#btnDeleteRow").click(function(){
    	var rows = $("#listGrid").getGridParam('selarrrow');
        var data = [];
        var from = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        for (var i in rows) {
        	var dataFrom = $("#listGrid").getRowData(rows[i]).dataFrom;
            data.push($("#listGrid").getRowData(rows[i]).oid);
            from.push(dataFrom.substring(0, 1));
        }
        if (checkFrom(from)) {
        	return CommonAPI.showMessage(i18n.lms7700m01["message01"]);
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {               
                $.ajax({
                    handler: "lms7700m01formhandler",
                    data: {
                        formAction: "deleteL140mm5c",
                        mainId: responseJSON.mainId,
                        oids: data
                    },
                    success: function(obj){
                        $("#listGrid").trigger("reloadGrid");
                    }
                });
            }
        });

    });
    
    $("#btnMaintain").click(function(){
    	var id = $("#listGrid").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        
        var rows = $("#listGrid").getGridParam('selarrrow');
    	var data = [];
        for (var i in rows) {
            data.push($("#listGrid").getRowData(rows[i]).oid);
        }
        
        $("#maintainBox").thickbox({
            title: i18n.lms7700m01['button.maintain'],
            width: 200,
            height: 170,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            open : function() {
				$(this).find("#maintainForm").reset();
				$("#reasonView").hide();
			},
            buttons: {
                "sure": function(){
                	var val = $("[name=isDeleteRadio]:checked").val();
                    if (!val) {
                        return CommonAPI.showMessage(i18n.lms7700m01['checkSelect']+
                        		i18n.lms7700m01['L140MM5A.isDelete']);
                    }
                    if(val=="N"){
                    	if($("#reasonSelect").val()==""){
                    		return CommonAPI.showMessage(i18n.lms7700m01['checkSelect']+
                            		i18n.lms7700m01['L140MM5A.reason']);
                    	}
                    }
                    
                	$.ajax({
                        handler: "lms7700m01formhandler",
                        data: {
                            formAction: "maintainL140mm5c",
                            oids: data,
                            isDelete: val,
                            reason: $("#reasonSelect").val()
                        },
                        success: function(obj){
                            $("#listGrid").trigger("reloadGrid");
                        }
                    });
                    $.thickbox.close();                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
	
	// 儲存的動作
    function saveData(showMsg, tofn){
		// 為檢查UI的值是否皆無異常
		if ($("#mainPanel").valid() == false) {
			return;
		}

		FormAction.open = true;
		$.ajax({
			handler: inits.fhandle,
			data: {
				formAction: "saveL140mm5a",
				oid: responseJSON.oid,
				page: responseJSON.page,
				txCode: responseJSON.txCode,
				showMsg: showMsg
			},
			success: function(obj){
				if (responseJSON.page == "01") {
					$('body').injectData(obj);
				}		
				CommonAPI.triggerOpener("gridview", "reloadGrid");
				if ($("#mainOid").val()) {
					setRequiredSave(false);
				}
				else {
					setRequiredSave(true);
				}
				
				// 執行列印
				if (!showMsg && tofn) {
					tofn();
				}
			}
		});
    }
	
	var item;
	// 呈主管 - 編製中
    function sendBoss(){
        $.ajax({
            handler: inits.fhandle,
            action: "checkData",
            data: {},
            success: function(json){
                $('#managerItem').empty();
                $('#bossItem').empty();
                item = json.bossList;
                var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
                $('#bossItem').append(bhtml).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
                $('#managerItem').append(html).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                
                // L140MM1B.message27=是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.lms7700m01["L140MM5B.message01"], function(b){
                    if (b) {
                        $("#selectBossBox").thickbox({
                            // L140MM1B.bt14=覆核
                            title: i18n.lms7700m01['approve'],
                            width: 500,
                            height: 300,
                            modal: true,
                            readOnly: false,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                
                                    var selectBoss = $("select[name^=boss]").map(function(){
                                        return $(this).val();
                                    }).toArray();
                                    
                                    for (var i in selectBoss) {
                                        if (selectBoss[i] == "") {
                                            // 請選擇授信主管
                                            return CommonAPI.showErrorMessage(i18n.lms7700m01['checkSelect'] +
                                            i18n.lms7700m01['L140MM5B.bossId']);
                                        }
                                    }
                                    if ($("#manager").val() == "") {
                                        // 請選擇經副襄理
                                        return CommonAPI.showErrorMessage(i18n.lms7700m01['checkSelect'] +
                                        i18n.lms7700m01['L140MM5B.managerId']);
                                    }
                                    // 驗證是否有重複的主管
                                    if (checkArrayRepeat(selectBoss)) {
                                        // 主管人員名單重複請重新選擇
                                        return CommonAPI.showErrorMessage(i18n.lms7700m01['L140MM5B.message02']);
                                    }
                                    
                                    flowAction({
                                        page: responseJSON.page,
                                        saveData: true,
                                        selectBoss: selectBoss,
                                        manager: $("#manager").val()
                                    });
                                    $.thickbox.close();
                                    
                                },
                                
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
            }
        });
    }
	
	// 待覆核 - 覆核
    function openCheck(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            // L140MM1B.bt14=覆核
            title: i18n.lms7700m01['approve'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        // L140MM1B.error2=請選擇
                        return CommonAPI.showMessage(i18n.lms7700m01['checkSelect']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // 該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms7700m01['L140MM5B.message03'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            }); 
                            break;
                        case "3":
                            // 該案件是否確定執行核定作業
                            CommonAPI.confirmMessage(i18n.lms7700m01['L140MM5B.message04'], function(b){
                                if (b) {
				                    flowAction({
				                        flowAction: true,
				                        checkDate: CommonAPI.getToday()//forCheckDate
				                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
	
	function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
				window.close();
            }
        });
    }

	// 列印動作
    function printAction(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: responseJSON.oid,
                fileDownloadName: "lms7700r01.pdf",
                serviceName: "lms7700r01rptservice"
            }
        });
    }
	
	// 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
    
    function checkFrom(from){
        for (var i = from.length; i--;) {
            var val = from[i];
            if (val=="1") {
            	return true;
            }
        }
        return false;
    }
    
    function checkSys(){
    	var cnt=0;
    	$("#itemSpan_sys").find("[name=sys]:checked").each(function(v, k){
    		cnt++;
        });
    	if(cnt>0){
    		return false;
    	} else {
    		return true;
    	}
    }
    
    function chose_custId(){	
		var my_dfd = $.Deferred();
		/*
		AddCustAction.open({
	    		handler: 'lms7700m01formhandler',
				action : 'echo_custId',
				data : {
	            },
				callback : function(json){
	            	// 關掉 AddCustAction 的
	            	$.thickbox.close();
					my_dfd.resolve( json );
				}
			});
        */
        $("#addCustForm").find("#addCustId").val('');
        $("#addCustForm").find("#addDupNo").val('');
        $("#addCustForm").find("#addCustName").val('');
        $("#addCustThickbox").thickbox({
            title : i18n.def["query"],//'查詢',
            width : 500,
            height : 230,
            modal : true,
            align : 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons : {
                'sure' : function(){
                    var $addCustForm = $("#addCustForm");
                    var custId = $addCustForm.find("#addCustId").val();
                    var dupNo = $addCustForm.find("#addDupNo").val();
                    var custName = $addCustForm.find("#addCustName").val();
                    if ($addCustForm.valid()){
                        if(custId == undefined || custId == null || custId == ""){
                            CommonAPI.showErrorMessage(i18n.lms7700m01["L140MM5A.custId"] + i18n.lms7700m01["cantEmpty"]);
                            return;
                        }else{
                            if((custName == undefined || custName == null || custName == "") && (dupNo == undefined || dupNo == null || dupNo == "")){
                                CommonAPI.showErrorMessage(i18n.lms7700m01["L140MM5A.custName"] + i18n.lms7700m01["cantEmpty"]);
                                return;
                            }else{
                                $.thickbox.close();
                                my_dfd.resolve( $.extend({'custId':custId, 'dupNo':dupNo, 'custName':custName}) );
                            }
                        }
                    }
                },
                'cancel' : function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
		return my_dfd.promise();
	}

	$("#getCustData").click(function(){
        var $addCustForm = $("#addCustForm");
        var $custId = $addCustForm.find("#addCustId").val();
        if(($custId == null || $custId == undefined || $custId == '')){
            // 客戶統一編號不得為空白
            CommonAPI.showErrorMessage(i18n.lms7700m01["L140MM5A.custId"] + i18n.lms7700m01["cantEmpty"]);
            return;
        }else{
            var defaultOption = {};
            if($custId != null && $custId != undefined && $custId != ''){
                defaultOption = {
                    defaultValue: $custId //預設值
                };
            }
            //綁入MegaID
            CommonAPI.openQueryBox(
                $.extend({
                    doNewUser: false,
                    defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : "",
                    divId:"addCustForm", //在哪個div 底下
                    autoResponse: { // 是否自動回填資訊
                           id: "addCustId", // 統一編號欄位ID
                           dupno: "addDupNo", // 重覆編號欄位ID
                          name: "addCustName" // 客戶名稱欄位ID
                    },fn:function(obj){
                    }
                },defaultOption)
            );
        }
    });
});