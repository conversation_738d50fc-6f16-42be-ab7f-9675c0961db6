/*
 * AbstractCapPage.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.base.pages;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.ui.ModelMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.utils.CapAppContext;

import tw.com.iisi.cap.base.attr.JavascriptAttr;
import tw.com.iisi.cap.context.ApplicationParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapShortMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.plugin.AjaxHandlerPlugin;
import tw.com.iisi.cap.plugin.CapPluginManager;
import tw.com.iisi.cap.plugin.IFrameAjaxHandlerPlugin;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapErrorResult;
import tw.com.iisi.cap.response.IErrorResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.utils.CapWebUtil;

/**
 * <pre>
 * abstract class AbstractCapPage extends WebPage.
 * 頁面資料處理
 * </pre>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/6,iristu,new
 *          <li>2012/2/23,iristu，加上onRequest供x過期使用
 *          </ul>
 */
public abstract class AbstractCapPage extends AbstractBasePage {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    private Logger capPageLogger = LoggerFactory.getLogger(AbstractCapPage.class);

    /**
     * 判斷資料處理的handler<br>
     * {@value #PARAM_PARSER_KEY}
     */
    public static final String PARAM_PARSER_KEY = "_pa";

    /**
     * 版本<br>
     * {@value #PARAM_VERSION_KEY}
     */
    public static final String PARAM_VERSION_KEY = "_ver";

    /**
     * 預設處理Ajax的handler<br>
     * {@value #AJAX_DEFAULT_PARSER}
     */
    public static final String AJAX_DEFAULT_PARSER = "defaultJsonAjaxHandler";

    /**
     * 插件管理
     */
    @Autowired
    public CapPluginManager plugMgr;

    /**
     * 訊息來源
     */
    @Autowired
    private MessageSource messageSource;

    /**
     * 傳入資料
     */
    @Autowired
    private ApplicationParameter applicationParameter;

    /**
     * The Constant PRE_VALIDATE_MESSAGE.<br>
     * {@value #PRE_VALIDATE_MESSAGE}
     */
    public static final String PRE_VALIDATE_MESSAGE = "PRE_VALIDATE_MESSAGE";

    /**
     * Instantiates a new abstract cap page.
     * 
     * @param parameters
     *            the parameters
     */
    public AbstractCapPage() {

    }

    /**
     * Instantiates a new abstract cap page.
     * 
     * @param parameters
     */
    public AbstractCapPage(PageParameters parameters) {

    }

    /**
     * 改編自 AbstractCapPage, 將 java 程式內動態產生的 javascript 放在 requestScope 變數中, 再由網頁取出
     * 
     * @param content
     * @param id
     */
    protected void addJavascript(String content, String id) {

        @SuppressWarnings("unchecked")
        List<JavascriptAttr> attrList = (List<JavascriptAttr>) getHttpServletAttr().getRequest().getAttribute("PAGE_JS_LIST");
        if (attrList == null) {
            attrList = new ArrayList<JavascriptAttr>();
            getHttpServletAttr().getRequest().setAttribute("PAGE_JS_LIST", attrList);
        }

        attrList.add(new JavascriptAttr("\n" + content + "\n", id));

    }

    /**
     * 處理 Form
     * 
     * @param tabIdx
     * @param model
     * @param map
     * @param req
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(method = { RequestMethod.POST })
    public String processForm(@PathVariable(value = "page", required = false) String tabIdx, ModelMap model, @RequestParam(required = false) MultiValueMap<String, ?> map, HttpServletRequest req,
            HttpServletResponse response) throws IOException {

        // _pa 有帶 handler 時視為 ajax 處理(for $.capFileDownload，不確定是否有其他問題)
        boolean ajaxApi = map != null && StringUtils.isNotBlank((String) map.getFirst("_pa"));

        if (ajaxApi && req.getPathInfo().indexOf("/error/message") < 0) {
            addPageI18N();
            return processAjax(tabIdx, model, map, req, response);
        } else {
            PageParameters params = convertRequest(req);
            params.put("page", tabIdx);
            try {
                if (beforeExecute(model)) {
                    execute(model, params);
                }
                afterExecute(model, params);
                // renderHeadJS();
                if (ajaxApi) {
                    return null;
                } else {
                    return getViewName();
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                model.addAttribute("errorMessage", e.getMessage());
                return applicationParameter.getErrorPageView();
            }
        }
    }

    /**
     * 處理 View
     * 
     * @param type
     * @param model
     * @param req
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    public String processView(@PathVariable(value = "page", required = false) String type, ModelMap model, HttpServletRequest req) {
        if (capPageLogger.isTraceEnabled()) {
            capPageLogger.trace(model.toString());
        }
        try {
            PageParameters params = convertRequest(req); // 使與 form, ajax 一致 --> 將 PageParameter 放入 request scope attribute

            if (beforeExecute(model)) {
                execute(model, params);
            }
            if (StringUtils.isNotBlank(type)) {
                params.put("page", type);
            }
            
            afterExecute(model, params);
            // renderHeadJS();
            // String path = "../../app" + getHttpServletAttr().getRequest().getPathInfo();
            String path = req.getRequestURI().toString();
            addJavascript(new StringBuffer("try{var __ajaxHandler='").append(path).append("';}catch(e){}").toString(), "ajaxHandler");

            return getViewName();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            model.addAttribute("errorMessage", e.getMessage());
            return applicationParameter.getErrorPageView();
        }
    }

    /**
     * 處理 Ajax
     * 
     * @param tabIdx
     * @param model
     * @param map
     * @param req
     * @param response
     * @return
     */
    // @RequestMapping(method = RequestMethod.POST, headers = "x-requested-with=XMLHttpRequest", produces = "application/json; charset=UTF-8")
    // public String processAjax(@PathVariable(value = "page", required = false) String tabIdx, ModelMap model, @RequestBody MultiValueMap<String, ?> map, HttpServletRequest req,
    private String processAjax(String tabIdx, ModelMap model, MultiValueMap<String, ?> map, HttpServletRequest req, HttpServletResponse response) {
        // CapAjaxFormResult result = new CapAjaxFormResult();
        // result.set("a", "123");
        // return result.getResult();
        PageParameters params = convertRequest(req);
        long cur = System.currentTimeMillis();
        if (capPageLogger.isTraceEnabled()) {
            capPageLogger.trace("Request Data: " + params);
        }
        Object locale = req.getSession().getAttribute(CapWebUtil.localeKey);
        if (locale != null) {
            SimpleContextHolder.put(CapWebUtil.localeKey, locale);
        } else {
            SimpleContextHolder.put(CapWebUtil.localeKey, Locale.getDefault());
        }
        String parser = params.getString(PARAM_PARSER_KEY, AJAX_DEFAULT_PARSER);
        String version = params.getString(PARAM_VERSION_KEY, "1.0.0");

        // tableIdx (從 request URL 來的 {page}) 有值才處理
        if (!StringUtils.isBlank(tabIdx)) {
            if (params.containsKey("page")) {
                // request parameter 有 page，可能會跟 URL 來的 page 產生衝突，以 request parameter 為主，tableIdx 放到 key 為 tableIdx 的資料中
                params.put("tabIdx", tabIdx);
            } else {
                // request parameter 沒有 page，tableIdx 放到 key 為 page 的資料中
                params.put("page", tabIdx);
            }
        }
        Logger pluginlogger = logger;
        IResult result = null;
        boolean iframe = false;
        try {
            AjaxHandlerPlugin plugin = plugMgr.getPlugin(parser, version);
            pluginlogger = LoggerFactory.getLogger(plugin.getClass());
            iframe = (plugin instanceof IFrameAjaxHandlerPlugin);
            result = plugin.execute(params);
        } catch (Exception e) {
            logger.error("processAjax execute exception", e);
            IErrorResult errorResult = plugMgr.getDefaultErrorResult();
            if (errorResult == null) {
                result = new CapErrorResult(params, e);
            } else {
                errorResult.putError(params, e);
                result = errorResult;
            }
            if (e instanceof CapShortMessageException) {
                pluginlogger.error(result.getResult());
            } else if (e instanceof CapException && e.getCause() != null) {
                pluginlogger.error(result.getResult(), e.getCause());
            } else {
                pluginlogger.error(result.getResult(), e);
            }
            if (!iframe) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                // 從 AjaxExpiredPage 搬過來
                CapAjaxFormResult ajaxExpired = new CapAjaxFormResult();
                ajaxExpired.set("AJAX_HANDLER_TIMEOUT", "1");
                result.add(ajaxExpired);
            }
        }
        logger.debug("Total cost : " + (System.currentTimeMillis() - cur));
        if (capPageLogger.isTraceEnabled()) {
            capPageLogger.trace("Response Data : " + result.getLogMessage());
        }
        try {
            result.respondResult(response);
            return null;
        } catch (CapException e) {
            logger.error(e.getMessage(), e);
            model.addAttribute("errorMessage", e.getMessage());
            return applicationParameter.getErrorPageView();
        }
    }

    /**
     * 取得 View 的名字
     * 
     * @return
     */
    protected abstract String getViewName();

    /**
     * before Execute
     * 
     * @param model
     * @return {@code true}
     */
    @Override
    protected boolean beforeExecute(ModelMap model) {
        // [refs#206] cache by server startup timestamp 會在 main.js 的 require.config 中使用 jsCache 作為 urlArgs
        model.addAttribute("jsCache", CapAppContext.getJsCacheString());
        return true;
    }// ;

    /**
     * 執行
     * 
     * @param model
     * @param params
     * @throws Exception
     */
    protected abstract void execute(ModelMap model, PageParameters params) throws Exception;

    /**
     * 執行完後動作<br>
     * 呼叫 {@linkplain tw.com.iisi.cap.base.pages.AbstractCapPage#setupRelativePathPrefix() setupRelativePathPrefix();}
     * 
     * @param model
     * @param params
     */
    protected void afterExecute(ModelMap model, PageParameters params) {
        setupRelativePathPrefix();
        // addPageI18N(model, false);
    }

    /**
     * 猜測性的對比至原有 wicket 的 getRelativePathPrefixToWicketHandler
     */
    protected void setupRelativePathPrefix() {

        String contextPath = getHttpServletAttr().getRequest().getContextPath();
        String requestUri = getHttpServletAttr().getRequest().getRequestURI();

        StringBuilder s = new StringBuilder();
        if (requestUri.length() <= contextPath.length())
            s.append("./");
        else {
            int pos = requestUri.indexOf("/", (contextPath + "/app").length() + 1);

            if (pos < 0)
                s.append("./");
            else {
                String apPath = requestUri.substring(pos + 1);
                String[] parts = apPath.split("/");
                for (String part : parts) {
                    if (part.length() > 0)
                        s.append("../");
                }
            }
        }

        addJavascript("var relativePathPrefix='" + s.toString() + "';", "xx");
    }

    /**
     * 增加回傳之JSON.
     * 
     * @param json
     *            JSONString
     */
    protected void addResponseJSON(String json) {
        if (json == null)
            return;
        String rtn = new StringBuffer("try{var responseJSON = ").append(json).append("}catch(e){}").toString();
        addJavascript(rtn, "responseJSON");
    }

    /**
     * 取的i18n的字串
     * 
     * @param key
     *            the i18n key
     * @return String
     */
    protected String getMessage(String key) {
        return getMessage(key, null, null);
    }// ;

    /**
     * 取的i18n的字串
     * 
     * @param key
     *            the i18n key
     * @param params
     *            the parameters
     * @return String
     */
    protected String getMessage(String key, Object[] params, Locale locale) {
        return messageSource.getMessage(key, params, locale);
    }// ;

    /**
     * 解析 I18N
     * 
     * @param componentClazz
     */
    protected void renderJsI18N(Class<?> componentClazz) {

        // locale
        Locale locale = null;
        HttpSession session = getHttpServletAttr().getRequest().getSession(false);
        if (session != null && session.getAttribute("userLocale") != null)
            locale = (Locale) session.getAttribute("userLocale");
        if (locale == null)
            locale = Locale.getDefault();

        // load property file
        String propertyFilePath = "/i18n/" + componentClazz.getName().replaceAll("[.]", "/");
        String pathNoLocal = propertyFilePath;
        if (locale != null)
            propertyFilePath += ("_" + locale.toString());
        propertyFilePath += ".properties";

        // String realPath = getHttpServletAttr().getRequest().getServletContext().getRealPath(propertyFilePath);
        Properties prop = new Properties();
        try (InputStream is = componentClazz.getClassLoader().getResourceAsStream(propertyFilePath)) {
            if (is != null)
                prop.load(is);
            else {
                // 抓取沒有地區後綴的properties
                pathNoLocal += ".properties";
                InputStream isNoLocal = componentClazz.getClassLoader().getResourceAsStream(pathNoLocal);

                if (isNoLocal != null) {
                    prop.load(isNoLocal);
                } else {
                    capPageLogger.warn("i18n file [{}] not found.", propertyFilePath);
                    capPageLogger.warn("i18n file [{}] not found.", pathNoLocal);
                }
            }
            String jsId = "i18n-" + componentClazz.getSimpleName();
            String i18njs = MessageBundleScriptCreator.createScript(componentClazz.getSimpleName());
            addJavascript(i18njs, jsId);
            addI18BasenameKey(componentClazz.getSimpleName());
        } catch (Exception e) {
            capPageLogger.error("load " + propertyFilePath + " error.", e);
            capPageLogger.error("load " + pathNoLocal + " error.", e);
        }

    }

    /**
     * Add I18N Basename Key
     * 
     * @param key
     */
    protected void addI18BasenameKey(String key) {
        MessageBundleScriptCreator.addI18BasenameKey(key);
    }

    /**
     * render js i18n by key:msg
     * 
     * @param msgs
     *            <key,Message>
     */
    protected void renderJsI18N(Map<String, String> msgs) {
        Properties p = new Properties();
        p.putAll(msgs);
        StringBuffer script = new StringBuffer();
        script.append("require(['mega.eloan.properties'], function(){i18n.set(\"msg\",").append(MessageBundleScriptCreator.generateJson(p, null)).append(");});");
        addJavascript(script.toString(), "i18n-msg");
    }

    /**
     * 設定JavaScript變數
     * 
     * @param key
     *            ,value
     */
    protected void setJavaScriptVar(String key, int value) {
        setJavaScriptVar(key, String.valueOf(value));
    }// ;

    /**
     * 設定JavaScript變數
     * 
     * @param key
     *            ,value
     */
    protected void setJavaScriptVar(String key, String value) {
        StringBuffer sb = new StringBuffer("var ");
        sb.append(key).append(" = '").append(value).append("';");
        addJavascript(sb.toString(), key);
    }// ;

    /**
     * render js i18n by key:msg
     * 
     * @param msgs
     *            <key,Message>
     */
    protected void renderJsI18NWithMsgName(String msgName, Map<String, String> msgs) {
        Properties p = new Properties();
        p.putAll(msgs);
        StringBuffer script = new StringBuffer();
        script.append("require(['mega.eloan.properties'], function(){i18n.set(\"").append(msgName).append("\",").append(MessageBundleScriptCreator.generateJson(p, null)).append(");});");
        addJavascript(script.toString(), "i18n-" + msgName);
    }

    /**
     * 加上 controller 同名 i18n
     */
    protected void addPageI18N() {

        Class<?> controllerClass = getClass();

        // 加上 controller 同名 i18n
        if (controllerClass.getName().endsWith("Page")) {
            renderJsI18N(controllerClass);
        }
    }

    /**
     * Single page render指定的i18n資料
     * 
     * @param componentClazz
     *            Class<? extends Component>
     * @param msgKey
     *            卻render的message key
     */
    protected void renderJsI18N(Class<?> componentClazz, String... msgKey) {
        Set<String> key = new HashSet<String>(msgKey.length);
        key.addAll(Arrays.asList(msgKey));
        addJavascript(MessageBundleScriptCreator.createScript(componentClazz.getSimpleName(), key), "i18n-" + componentClazz.getSimpleName());
    }

    /**
     * Single page render指定的i18n資料
     * 
     * @param componentClazz
     *            Class<? extends Component>
     * @param keys
     *            欲render的message key
     * @param msgKey
     *            欲render的message key
     */
    protected void renderJsI18N(Class<?> componentClazz, String[] keys, String... msgKey) {
        Set<String> key = new HashSet<String>(keys.length + msgKey.length);
        key.addAll(Arrays.asList(keys));
        key.addAll(Arrays.asList(msgKey));
        addJavascript(MessageBundleScriptCreator.createScript(componentClazz.getSimpleName(), key), "i18n-" + componentClazz.getSimpleName());
    }
}
