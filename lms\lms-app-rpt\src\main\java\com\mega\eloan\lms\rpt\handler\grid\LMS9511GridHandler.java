/* 
 * LMS9515GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L180R02A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.eloan.lms.rpt.pages.LMS9511V01Page;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 最新管理報表
 * </pre>
 * 
 * @since 2013/1/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/7,Ice,new
 *          <li>2013/01/10,Vector,加入個金報表
 *          </ul>
 */
@Scope("request")
@Controller("lms9511gridhandler")
public class LMS9511GridHandler extends AbstractGridHandler {

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	LMSService lmsService;

	@Resource
	LMS9511Service lms9511service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMSRPTDao lmsRptDao;

	@Resource
	DocFileDao fileDao;

	/**
	 * 取得最新報表資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryLMSRPT(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", "Y");
		String brnachUnitType = UnitTypeEnum.convertToUnitType(
				user.getUnitType()).getCode();
		// String branchId = user.getUnitNo();
		if (user.getUnitNo().equals(UtilConstants.BankNo.國外部)) {
			Object[] rptNos = {
					UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表,

					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報,
					UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單,
					UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料,
					UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,

					UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
					UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
					UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
					UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,

					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,

					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上房貸申貸清單,

					UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表,
					UtilConstants.RPTREPORT.DOCTYPE2.六個月內到期土建融案件月報表,
					UtilConstants.RPTREPORT.DOCTYPE2.整批房貸統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.待售房屋去化落後追蹤表,
					UtilConstants.RPTREPORT.DOCTYPE2.中期循環年度檢視表 };
			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);
		} else if (user.getUnitNo().equals(UtilConstants.BankNo.業管處)) {
			Object[] rptNos = { UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表,

					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報,
					UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單,
					UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料,
					UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表,
					UtilConstants.RPTREPORT.DOCTYPE1.土建融案資料表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,

					UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
					UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
					UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
					UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,

					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,

					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上房貸申貸清單,

					UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表,
					UtilConstants.RPTREPORT.DOCTYPE2.六個月內到期土建融案件月報表,
					UtilConstants.RPTREPORT.DOCTYPE2.整批房貸統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.待售房屋去化落後追蹤表,
					UtilConstants.RPTREPORT.DOCTYPE2.中期循環年度檢視表 };
			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);
		} else if (user.getUnitNo().equals(UtilConstants.BankNo.授信行銷處)) {
			Object[] rptNos = {
					UtilConstants.RPTREPORT.DOCTYPE1.授信案件統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.金融機構辦理振興經濟非中小企業專案貸款,
					UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.本行各營業單位各級授權範圍內承做授信案件統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.分行授信淨增加額度統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報,
					UtilConstants.RPTREPORT.DOCTYPE1.振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表,
					UtilConstants.RPTREPORT.DOCTYPE1.中小企業創新發展專案貸款執行情形統計月報表,

					UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表,

					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金聯貸新作案件統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信案件敘做情形及比較表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金已核准授信額度辦理狀態通報彙總表,
					UtilConstants.RPTREPORT.DOCTYPE1.簽報階段都更危老業務統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.協助工業區及產業園區建廠優惠貸款專案執行情形統計月報表,

					UtilConstants.RPTREPORT.DOCTYPE1.新核准往來客戶及新增放款額度統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.新核准往來客戶明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.國內分行每季新做無擔保中小企業戶授信額度明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.國內分行每季新作副總權限以上授信額度累計金額,
					UtilConstants.RPTREPORT.DOCTYPE1.國內分行新核准往來企金客戶數統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.國內分行新核准往來企金客戶數統計表含舊戶,
					UtilConstants.RPTREPORT.DOCTYPE1.共同行銷擔保品投保未結案明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.愛企貸專案統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形總表,
					UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案分行核准情形總表,
					UtilConstants.RPTREPORT.DOCTYPE1.青年創業及啟動金貸款辦理情形總表,
					UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單,
					UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料,
					UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表,
					UtilConstants.RPTREPORT.DOCTYPE1.國內營業單位海外信保基金基金案件表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,

					UtilConstants.RPTREPORT.DOCTYPE1.企金授權外案件流程進度控管表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件經區域營運中心流程進度控管表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,
					UtilConstants.RPTREPORT.DOCTYPE1.中小企業千億振興融資方案,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件明細檔,
					UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,
                    UtilConstants.RPTREPORT.DOCTYPE1.青創追蹤報表,

					UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
					UtilConstants.RPTREPORT.DOCTYPE2.已核准尚未撥款案件報表,

					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,

					UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.特定金錢信託案件量統計報表,

            };
			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);
		} else if (user.getUnitNo().equals(UtilConstants.BankNo.海業處)) {
			Object[] rptNos = {

					UtilConstants.RPTREPORT.DOCTYPE1.國內營業單位海外信保基金基金案件表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,
					// J-110-0211_11557_B1002
					// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
					// 0A7東京分行、0A8大阪分行要多顯示一張整批貸款明細表
					UtilConstants.RPTREPORT.DOCTYPE2.整批貸款明細表,
					//J-113-0347 海業處增加BIS評估表
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表

			};
			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);
		} else if (user.getUnitNo().equals(UtilConstants.BankNo.消金業務處)) {
			Object[] rptNos = { UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
					UtilConstants.RPTREPORT.DOCTYPE2.已核准尚未撥款案件報表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金處新做案件_當月_當年度累計_統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金整批房貸消貸敘做明細,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上貸款餘額表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金自住型房貸成長方案統計報表,

					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,

					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上房貸申貸清單,
					UtilConstants.RPTREPORT.DOCTYPE2.台電員工貸款明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸ESG明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸KYC分案報表,
					UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸婉拒案件自動發送簡訊失敗顧客清單,
					UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款彙總表,
					UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.六個月內到期土建融案件月報表,
					UtilConstants.RPTREPORT.DOCTYPE2.整批房貸統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.特定金錢信託案件量統計報表,
					UtilConstants.RPTREPORT.DOCTYPE1.待售房屋去化落後追蹤表,
					UtilConstants.RPTREPORT.DOCTYPE2.中期循環年度檢視表,
					UtilConstants.RPTREPORT.DOCTYPE2.客戶訪談紀錄表,
					UtilConstants.RPTREPORT.DOCTYPE2.國內消金貸款地政士引介房貸案發生逾放比例統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.分行承作購置住宅貸款年限逾30年統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.房貸案件明細表,
                    UtilConstants.RPTREPORT.DOCTYPE2.青創追蹤報表};
			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);

		} else if (user.getUnitNo().equals(UtilConstants.BankNo.風控處)) {// Vector
			// 201分行的 brnachUnitType == 5 == UnitTypeEnum.國金部
			Object[] rptNos = { UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,

					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表_不含小規,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,

					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,

					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,

					UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
					UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
					UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
					UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,
					UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表 };
			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);

		} else if (brnachUnitType.equals(UnitTypeEnum.營運中心.getCode())) {
			Object[] rptNos = {
					UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,
					UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
					// J-111-0583_05097_B1001 Web
					// e-Loan企金授信提供各營運中心可自行列印轄下分行於指定期間內所簽報異常通報之「授信異常通報案件報送統計表」
					UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審經理記點統計明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信簽案已核准未能簽約撥貸原因表,
					UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
					UtilConstants.RPTREPORT.DOCTYPE1.營運中心轄下分行往來客戶有全行通報異常情形彙總表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報,
					UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單,
					UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料,
					UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件經區域營運中心流程進度控管表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金授信簽報案件經區域營運中心流程進度控管表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,

					UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表 };
			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);
		} else if (brnachUnitType.equals(UnitTypeEnum.授管處.getCode())
				|| "900".equals(user.getUnitNo())
				|| "916".equals(user.getUnitNo())) {
			Object[] rptNos = {
					UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
					UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信案件統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.金融機構辦理振興經濟非中小企業專案貸款,
					UtilConstants.RPTREPORT.DOCTYPE1.企業自行申請展延案件案件統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表,
					UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表_季報,
					UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.本行各營業單位各級授權範圍內承做授信案件統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.分行授信淨增加額度統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報,
					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.覆審經理記點統計明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.營業單位辦理對私募基金旗下投資事業敘做授信案件清單,
					UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
					UtilConstants.RPTREPORT.DOCTYPE1.中小企業創新發展專案貸款執行情形統計月報表,
					"LMS180R34",
					"LMS180R35",
					UtilConstants.RPTREPORT.DOCTYPE1.協助工業區及產業園區建廠優惠貸款專案執行情形統計月報表,
					UtilConstants.RPTREPORT.DOCTYPE1.對區域營運中心授信覆審作業之管理績效考核表,
					UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表,
					UtilConstants.RPTREPORT.DOCTYPE1.法令遵循自評授信案件明細報表,
					UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人授信異常通報表,
					UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單,
					UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料,
					UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表_不含小規,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授權外案件流程進度控管表,
					UtilConstants.RPTREPORT.DOCTYPE1.土建融案資料表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件經區域營運中心流程進度控管表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金授信簽報案件經區域營運中心流程進度控管表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,
					UtilConstants.RPTREPORT.DOCTYPE1.不動產授信例外管理報表,
					UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,

					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,

					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上房貸申貸清單,

					UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
					UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
					UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
					UtilConstants.RPTREPORT.DOCTYPE2.已核准尚未撥款案件報表,
					UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金處新做案件_當月_當年度累計_統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審名單,
					UtilConstants.RPTREPORT.DOCTYPE2.消金防杜代辦專案覆審件數控管表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金整批房貸消貸敘做明細,
					UtilConstants.RPTREPORT.DOCTYPE2.消金線上貸款餘額表,
					UtilConstants.RPTREPORT.DOCTYPE2.消金自住型房貸成長方案統計報表,
					UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
					UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
					UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.台電員工貸款明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸案件明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸ESG明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款彙總表,
					UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款明細表,
					UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單,
					UtilConstants.RPTREPORT.DOCTYPE2.特定金錢信託案件量統計報表,// 已包含
																	// {900,
																	// 918
					UtilConstants.RPTREPORT.DOCTYPE1.待售房屋去化落後追蹤表,
					UtilConstants.RPTREPORT.DOCTYPE2.客戶訪談紀錄表,
					UtilConstants.RPTREPORT.DOCTYPE2.分行承作購置住宅貸款年限逾30年統計表, 
					UtilConstants.RPTREPORT.DOCTYPE2.房貸案件明細表};

			if (brnachUnitType.equals(UnitTypeEnum.授管處.getCode())
					|| "900".equals(user.getUnitNo())) {
				ArrayList<Object> temp = new ArrayList<Object>(
						Arrays.asList(rptNos));
				temp.add(UtilConstants.RPTREPORT.DOCTYPE1.企業社會責任貸放情形統計表);
				temp.add(UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表);
				temp.add(UtilConstants.RPTREPORT.DOCTYPE1.建案完成未出售房屋融資統計表);
				temp.add(UtilConstants.RPTREPORT.DOCTYPE1.免計入銀行法72_2限額控管之廠房貸款案件追蹤表);
				temp.add(UtilConstants.RPTREPORT.DOCTYPE1.企金授信簽報案件明細檔);
				rptNos = temp.toArray();
			}
			if ("900".equals(user.getUnitNo())) {
				ArrayList<Object> temp = new ArrayList<Object>(
						Arrays.asList(rptNos));
				temp.add(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸KYC分案報表);
				temp.add(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸婉拒案件自動發送簡訊失敗顧客清單);
				rptNos = temp.toArray();
			}
			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);
		} else if (brnachUnitType.equals(UnitTypeEnum.分行.getCode())
				|| brnachUnitType.equals(UnitTypeEnum.國金部.getCode())) {// Vector
			// 201分行的 brnachUnitType == 5 == UnitTypeEnum.國金部
			// 要放在最後面，要不然916 無法呈現授信案件統計表
			// 因為分行類別沒有企劃處，所以分行設定中，916企劃處被設定為一般分行

			// 海外分行
			Object[] rptNos = {};
			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
					.getBranch(user.getUnitNo()).getBrNoFlag());
			if (isOverSea) {

				if (Util.equals(user.getUnitNo(), "0A7")
						|| Util.equals(user.getUnitNo(), "0A8")) {
					Object[] rptNos1 = {
							UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
							UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,

							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,

							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,

							// UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
							UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
							UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
							UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表// TODO
							// 暫時開放功能
							// 正式上線要拿掉
							,
							UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
							// J-109-0115_10702_B1001 Web
							// e-Loan管理報表新增海外分行「授信業務異常通報月報」
							UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報,
							// J-110-0211_11557_B1002
							// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
							// 0A7東京分行、0A8大阪分行要多顯示一張整批貸款明細表
							UtilConstants.RPTREPORT.DOCTYPE2.整批貸款明細表 };
					rptNos = rptNos1;
				} else {
					Object[] rptNos1 = {
							UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
							UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,

							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,

							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,

							// UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
							UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
							UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
							UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表// TODO
							// 暫時開放功能
							// 正式上線要拿掉
							,
							UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
							// J-109-0115_10702_B1001 Web
							// e-Loan管理報表新增海外分行「授信業務異常通報月報」
							UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報 };
					rptNos = rptNos1;
				}
			} else {

				if (brnachUnitType.equals(UnitTypeEnum.國金部.getCode())) {
					Object[] rptNos2 = {
							UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
							UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,

							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報,
							UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表,
							UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表,
							UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單,
							UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料,
							UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,

							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,

							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金線上房貸申貸清單,

							UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
							UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
							UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
							UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表// TODO
																			// 暫時開放功能
																			// 正式上線要拿掉
							,
							UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.待售房屋去化落後追蹤表,
							UtilConstants.RPTREPORT.DOCTYPE2.中期循環年度檢視表,
							UtilConstants.RPTREPORT.DOCTYPE2.特定金錢信託案件量統計報表 }; // UnitType=國金部，包含201
					rptNos = rptNos2;
				} else if (Util.equals(user.getUnitNo(), "243")) { // 投資處
					Object[] rptNos2 = {
							UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
							UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,

							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,

							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,

							UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
							UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
							UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
							UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表// TODO
																			// 暫時開放功能
																			// 正式上線要拿掉
							,
							UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金共同行銷報表 };
					rptNos = rptNos2;
				} else if (Util.equals(user.getUnitNo(),
						UtilConstants.BankNo.海業處)
						|| Util.equals(user.getUnitNo(),
								UtilConstants.BankNo.海管處)) { // 942 海外業務處 937海管處
					// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
					Object[] rptNos2 = {
							UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
							UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,

							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
							UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表,
							UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表,
							UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單,
							UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料,
							UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,

							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,

							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金線上房貸申貸清單,

							UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
							UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
							UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
							UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表// TODO
																			// 暫時開放功能
																			// 正式上線要拿掉
							,
							UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款彙總表,
							UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.六個月內到期土建融案件月報表,
							UtilConstants.RPTREPORT.DOCTYPE2.整批房貸統計表,
							// J-110-0211_11557_B1002
							// 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
							// 942 海外業務處要多顯示一張整批貸款明細表
							UtilConstants.RPTREPORT.DOCTYPE2.整批貸款明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.待售房屋去化落後追蹤表 };
					rptNos = rptNos2;
				} else if (user.getUnitNo()
						.equals(UtilConstants.BankNo.信用卡暨支付處)) {// J-112-0222
																// 因應卡處旗下電銷人員需求，讓該單位能查詢"CLS180R27C 歡喜信貸KYC分案報表"，其餘報表不需要
					Object[] rptNos2 = { UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸KYC分案報表 };
					rptNos = rptNos2;
				} else {
					Object[] rptNos2 = {
							UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單,
							UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表,

							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.企金逾期未覆審名單_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金戶未出現於覆審名單,
							UtilConstants.RPTREPORT.DOCTYPE1.撥貸後半年內辦理覆審檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信覆審明細檢核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表,
							UtilConstants.RPTREPORT.DOCTYPE1.覆審考核表_董事會授權,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表,
							UtilConstants.RPTREPORT.DOCTYPE1.投資台灣三大方案專案貸款執行情形統計表,
							UtilConstants.RPTREPORT.DOCTYPE1.因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表,
							UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人兌付振興三倍券達888張名單,
							UtilConstants.RPTREPORT.DOCTYPE1.額度專案種類明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.案件屬110年行銷名單來源客戶簽案資料,
							UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金綠色授信暨ESG簽報案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE1.企金授信核准案件BIS評估表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信案件曾涉及ESG風險因而有條件通過或未核准之情形表,

							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處未註記清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處已註記清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處全部清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處地址比對清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處電話比對清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金借款人留存同一通訊處EML比對清單,

							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息未註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息已註記明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.疑似代辦案件訊息全部明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金線上信貸申貸清單,
							UtilConstants.RPTREPORT.DOCTYPE2.消金線上房貸申貸清單,

							UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單,
							UtilConstants.RPTREPORT.DOCTYPE2.敘做無自用住宅購屋放款明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報,
							UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表,
							UtilConstants.RPTREPORT.DOCTYPE2.報案考核表被扣分清單,
							UtilConstants.RPTREPORT.DOCTYPE2.審件統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.婉拒案件明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.消金授信已逾期控制表,
							UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表// TODO
																			// 暫時開放功能
																			// 正式上線要拿掉
							,
							UtilConstants.RPTREPORT.DOCTYPE2.價金履保覆審統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.實際覆審戶數及件數統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_至上個月,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_期限自訂,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_從查詢月起,
							UtilConstants.RPTREPORT.DOCTYPE2.未依期限覆審資料_逾覆審期限,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別8_1之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_不動產十足擔保低於門檻抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_專案信貸抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別_95_1抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之進度表,
							UtilConstants.RPTREPORT.DOCTYPE2.覆審類別R14抽樣之明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.個金授信覆審頻率3次含以上明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款彙總表,
							UtilConstants.RPTREPORT.DOCTYPE2.勞工紓困貸款明細表,
							UtilConstants.RPTREPORT.DOCTYPE2.六個月內到期土建融案件月報表,
							UtilConstants.RPTREPORT.DOCTYPE2.整批房貸統計表,
							UtilConstants.RPTREPORT.DOCTYPE2.特定金錢信託案件量統計報表,
							UtilConstants.RPTREPORT.DOCTYPE1.待售房屋去化落後追蹤表,
							UtilConstants.RPTREPORT.DOCTYPE2.中期循環年度檢視表,
							UtilConstants.RPTREPORT.DOCTYPE2.客戶訪談紀錄表 };
					rptNos = rptNos2;
				}

			}

			pageSetting.addSearchModeParameters(SearchMode.IN, "rptNo", rptNos);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branch",
				user.getUnitNo());

		// pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms9511service.findPage(
				LMSRPT.class, pageSetting);

		// List<? extends GenericBean> list = page.getContent();
		// for (GenericBean bean : list) {
		// bean.set("jingBan", bean.get("branch"));
		// }
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得最新報表資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult querySpeReport(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// Date dataDate = null;
		// Date dataEndDate = null;
		String rptNo = Util.trim(params.getString("rptNo"));// 報表
		String nowRpt = Util.trim(params.getString("nowRpt"));// 報表
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptNo", rptNo);
		String brnachUnitType = UnitTypeEnum.convertToUnitType(
				user.getUnitType()).getCode();
		if (brnachUnitType.equals(UnitTypeEnum.分行.getCode())
				|| brnachUnitType.equals(UnitTypeEnum.國金部.getCode())) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branch",
					user.getUnitNo());
		} else if (brnachUnitType.equals(UnitTypeEnum.營運中心.getCode())) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branch",
					user.getUnitNo());
		} else if (brnachUnitType.equals(UnitTypeEnum.授管處.getCode())) {
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"sendTime", null);
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"branch", UtilConstants.BankNo.授管處);
		}

		// pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms9511service.findPage(
				LMSRPT.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("cfrmFlag", new CodeTypeFormatter(codetypeService,
				"Common_YesNo")); // 分行名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 取得918報表所有資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult querySpeLMS180R02AReport(ISearch pageSetting,
			PageParameters params) throws CapException {
		// String rptNo = Util.trim(params.getString("rptNo"));// 報表
		String nowRpt = Util.trim(params.getString("nowRpt"));// 報表
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptNo",
				UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單);
		pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL, "sendTime",
				null);

		// pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms9511service.findPage(
				LMSRPT.class, pageSetting);
		List<? extends GenericBean> list = page.getContent();
		for (GenericBean bean : list) {
			bean.set("jingBan", bean.get("branch"));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("cfrmFlag", new CodeTypeFormatter(codetypeService,
				"Common_YesNo")); // 分行名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 取得營運中心報表所有資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult querySpeLMS180R02AReport2(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));// 報表
		String approver = Util.trim(params.getString("approver"));// 報表
		String[] listBranch = params.getStringArray("listBranch");// 報表
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (Util.isNotEmpty(approver)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "approver",
					approver);
		}
		if (listBranch != null && listBranch.length > 0) {
			if (listBranch.length == 1 && listBranch[0].equals("")) {

			} else {
				pageSetting.addSearchModeParameters(SearchMode.IN, "brno",
						listBranch);
			}
		}
		pageSetting.addOrderBy("caseDate");
		pageSetting.addOrderBy("approver");
		pageSetting.addOrderBy("docKind");
		pageSetting.addOrderBy("docType");
		pageSetting.addOrderBy("brno");

		// pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms9511service.findPage(
				L180R02A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("brno", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("docType", new CodeTypeFormatter(codetypeService,
				"L120M01A_docType"));
		dataReformatter.put("docKind", new CodeTypeFormatter(codetypeService,
				"L120M01A_docKind"));
		dataReformatter.put("audit", new CodeTypeFormatter(codetypeService,
				"Common_YesNo")); // 分行名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 取得918或營運中心報表所有資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult querySpeLMS180R15Report(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brnachUnitType = UnitTypeEnum.convertToUnitType(
				user.getUnitType()).getCode();

		// String rptNo = Util.trim(params.getString("rptNo"));// 報表
		String nowRpt = Util.trim(params.getString("nowRpt"));// 報表
		// String startDate = Util.trim(params.getString("startDate"));// 報表
		// String temp[] = startDate.split("-");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "nowRpt", nowRpt);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptNo",
				UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單);
		if (brnachUnitType.equals(UnitTypeEnum.營運中心.getCode())) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branch",
					user.getUnitNo());
		} else if (brnachUnitType.equals(UnitTypeEnum.授管處.getCode())) {
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"branch", user.getUnitNo());
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"sendTime", null);
		}
		// pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms9511service.findPage(
				LMSRPT.class, pageSetting);
		List<? extends GenericBean> list = page.getContent();
		for (GenericBean bean : list) {
			bean.set("jingBan", bean.get("branch"));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("cfrmFlag", new CodeTypeFormatter(codetypeService,
				"Common_YesNo")); // 分行名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 查詢Grid 資料(L784s01a)
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapGridResult queryL784s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 判定是否已註記被刪除
		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
		// null);
		Page<? extends GenericBean> page = lms9511service.findPage(
				L784S01A.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 取得常董會資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL784s07a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));// 報表代碼-企金
		String year = null;
		// int startYear = params.getInt("startYear", 0);
		// String year = startYear == 0 ? String.valueOf(
		// TWNDate.toTW(CapDate.getCurrentTimestamp())).split("/")[0]
		// : ((startYear - 1911) + "");
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
		// mainId);
		// 判定是否已註記被刪除
		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
		// null);
		// pageSetting.setDistinct(true);
		List<IBranch> branchList = lmsService.getBranchList();
		List<Map<String, Object>> branchMapList = new LinkedList<Map<String, Object>>();
		LMSRPT lmsRpt = lmsRptDao.findByIndex03(mainId);
		if (lmsRpt == null) {
			lmsRpt = new LMSRPT();
		}
		year = lmsRpt.getDataDate() == null ? "" : TWNDate.toAD(
				lmsRpt.getDataDate()).substring(0, 4);
		for (IBranch ibranch : branchList) {
			Map<String, Object> map = new LinkedHashMap<String, Object>();
			map.put("mainId", mainId);
			map.put("apprYY", year);
			map.put("brNo", ibranch.getBrNo());
			map.put("brName", ibranch.getBrNo() + " " + ibranch.getBrName());
			map.put("caseDept", "3");
			branchMapList.add(map);
		}
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(branchMapList,
				pageSetting);

		// 加入格式化
		CapMapGridResult result = new CapMapGridResult(pages.getContent(),
				branchMapList.size());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("caseDept", new I18NFormatter("caseDept."));
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 查詢Grid 資料(L784s07a)
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapGridResult queryL784s07aForTotalMonth(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String brNo = Util.nullToSpace(params.getString("brNo"));

		// 判定是否已註記被刪除
		// pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
		// null);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		// pageSetting.setDistinct(true);
		Page<? extends GenericBean> page = lms9511service.findPage(
				L784S07A.class, pageSetting);
		// 加入格式化
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();

		dataReformatter.put("caseDept", new I18NFormatter("caseDept."));
		dataReformatter.put("updater", new UserNameFormatter(userInfoService)); // codeType格式化
		result.setDataReformatter(dataReformatter);

		return result;
	}

	/**
	 * 查詢 DocFile 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapMapGridResult queryFile(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		String mainId = Util.nullToSpace(params.getString("mainId"));
		if (Util.isNotEmpty(mainId)) {
			boolean useMainId = params.getAsBoolean("useMainId");

			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9511V01Page.class);

			List<? extends GenericBean> list = lms9511service
					.findFileGrid(mainId);
			List<DocFile> fileList;
			if (list != null) {
				for (int i = 0; i < list.size(); i++) {
					LMSRPT pivot = (LMSRPT) list.get(i);
					// 查DOCFILE
					if (useMainId) {
						fileList = fileDao.getDocFilesByIdAndPid(mainId, null);
					} else {
						ISearch search = fileDao.createSearchTemplete();
						search.addSearchModeParameters(SearchMode.EQUALS,
								"oid", pivot.getReportOidFile());
						search.addSearchModeParameters(SearchMode.IS_NULL,
								"deletedTime", null);
						fileList = fileDao.find(search);
					}
					// 通通丟進MAP
					for (int x = 0; x < fileList.size(); x++) {
						DocFile file = fileList.get(x);
						Map<String, Object> record = new LinkedHashMap<String, Object>();
						record.put("oid", file.getOid());
						record.put("mainId", pivot.getMainId());
						record.put("reportOidFile", pivot.getReportOidFile());
						record.put("rptMainId", pivot.getMainId());
						record.put("branch", pivot.getBranch());
						record.put("brName",
								branchService.getBranchName(pivot.getBranch()));
						if (Util.equals(
								UtilConstants.RPTREPORT.DOCTYPE2.已核准尚未撥款案件報表,
								params.getString("rptNo"))) {
							record.put("rptName", file.getSrcFileName());
						} else {
							record.put("rptName", pivot.getRptName());
						}
						record.put("bgnDate", pivot.getBgnDate());
						record.put("endDate", pivot.getEndDate());
						record.put("sendTime", pivot.getSendTime());
						record.put("cfrmTime", pivot.getCfrmTime());
						record.put("cfrmFlag", pivot.getCfrmFlag());
						record.put(
								"cfrmShow",
								pop.getProperty("L180R02A."
										+ pivot.getCfrmFlag()));
						record.put("updateTime", pivot.getUpdateTime());
						record.put("fileDesc", file.getFileDesc());
						result.add(record);
					}
				}
				if (result.size() > 15) {
					int page = Util.parseInt(params.getString("gridPage")) - 1, cols = Util
							.parseInt(params.getString("rows"));
					int begin = page * cols, end = (page + 1) * cols;
					end = (end > result.size()) ? result.size() : end;
					List<Map<String, Object>> data = result;
					if (begin <= end) {
						result = data.subList(begin, end);
					}
					return new CapMapGridResult(result, data.size());
				}
			}
		}
		return new CapMapGridResult(result, result.size());
	}

}
