var dfdPanel1 = $.Deferred(), dfdPanel2 = $.Deferred(), dfdPanel3 = $.Deferred(), dfdPanel4 = $.Deferred();

//J-107-0256_05097_B1001 Web e-Loan企金授信簽報書額度明細表，針對額度性質選列「擔保」者，增加提示不得列擔保科目的擔保品項目
var gSbjProperty = false;
//J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
var lastSel;
//J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
var l140s04aJson;
if (!l140s04aJson) {
	//J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	l140s04aJson = {
		docType : responseJSON.docType,
		docCode : responseJSON.docCode,
		docKind : responseJSON.docKind,
		docStatus : responseJSON.mainDocStatus,
		areaDocstatus : responseJSON.areaDocstatus,
		mainId : $("#tabFormMainId").val(),
		unitNo : userInfo.unitNo,
		page : responseJSON.page,
		typCd : responseJSON.typCd,
		openTGridContentFormLoanPolicyExForReadOnly : false,
		/**
		 *  更新異常通報Grid
		 */	
		rLoanPolicyExGrid : function(){
			$("#loanPolicyExGrid").jqGrid("setGridParam", {
				postData : {
					handler : 'lms1405gridhandler',
					formAction : "queryL140s04a",
					mainId : $("#tabFormMainId").val(),
					itemType : "1",
					rowNum : 10
				},
				search : true
			}).trigger("reloadGrid");		
		},
		/**
		 *  異常通報Grid
		 */	
		loanPolicyExGrid : function(){
			$("#loanPolicyExGrid").iGrid({
				handler : 'lms1405gridhandler',
				height : 175,
				sortname : 'bigKind|seqShow',
				sortorder: 'asc|asc',
				postData : {
					formAction : "queryL140s04a",
					mainId : $("#tabFormMainId").val(),
					itemType : "1"
				},
				caption: "&nbsp;",
				hiddengrid : false,
				//rownumbers : true,
				//rowNum : 10,
				multiselect: true,
				hideMultiselect:false,
				shrinkToFit: false,
				colModel : [ {
					colHeader : i18n.lms1405s02['L140S04a.item'], //項目
					align : "left",
					width : 50, // 設定寬度
					sortable : true, // 是否允許排序
					name : 'seqNo' // col.id
				}, {
					colHeader : i18n.lms1405s02['L140S04a.itemName'] , //項目名稱
					align : "left",
					width : 450, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'seqName' // col.id
				}, {
					colHeader : i18n.lms1405s02['L140S04a.docDscr'] , //說明
					align : "left",
					width : 400, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'docDscr' // col.id
				}, {
					colHeader : "seqShow",
					name : 'seqShow',
					hidden : true
				}, {
					colHeader : "bigKind",
					name : 'bigKind',
					hidden : true
				}, {
					colHeader : "mainId",
					name : 'mainId',
					hidden : true
				}, {
					colHeader : "oid",
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
					var data = $("#loanPolicyExGrid").getRowData(rowid);
					l140s04aJson.thickGridContent(data);
				}
			});		
		},
		/**
		 *  不符合授信政策登錄事項ThickBox
		 */	
		thickLoanPolicyEx : function(isforQuery){
			l140s04aJson.rLoanPolicyExGrid();
			
			//設定開啟F類登錄說明時的唯獨狀態
			$("#tGridContentFormLoanPolicyEx").readOnlyChilds(isforQuery, ".noEdit");
			l140s04aJson.openTGridContentFormLoanPolicyExForReadOnly = isforQuery;
			
			var thickJson = {};
			
			if(isforQuery){
				thickJson = {
					"L140S04a.close" : function(){ //關閉
						$.thickbox.close();
					}
				};	
			}else{ 
				thickJson = {
					"L140S04a.add": function() { // 新增事項
						l140s04aJson.thickAdd();
			        },
					"L140S04a.delete": function() { // 刪除新增事項
						CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
						if (b) {
								var rows = $("#loanPolicyExGrid").getGridParam('selarrrow');
								var list = "";
								var sign = ",";
								for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
									if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
										var data = $("#loanPolicyExGrid").getRowData(rows[i]);
										list += ((list == "") ? "" : sign ) + data.oid;
									}
								}
								if (list == "") {
									CommonAPI.showMessage(i18n.def["grid_selector"]);
									return;
								}													
				          		$.ajax({
					    			type : "POST",
					    			handler : "lms1405m01formhandler",
									action : "deleteL140s04a",
					    			data : 
					    			{
										oids : list,
										unitType : userInfo.unitType,
										mainId : $("#tabFormMainId").val(),
									    itemType : "1"
					    			},
					    			success:function(responseData){
										$("#loanPolicyExGrid").trigger("reloadGrid");
										$("#loanPolicyExDscr").html(responseData.loanPolicyExDscr);
										$.thickbox.close();
						          		// 查詢組合字串
										
					    			}
								});
							}				
						});
		             },
					 "L140S04a.close" : function(){ //關閉
						API.confirmMessage(i18n.def['flow.exit'], function(res){
							if (res) {
								l140s04aJson.buildCombine();
							}
						});
					 }
					 
				};	
					 
			}
			
					
			$("#thickLoanPolicyEx").thickbox({     // 使用選取的內容進行彈窗
			   title : i18n.lms1405s02["L140S04a.loanPolicyEx"], //不符合授信政策登錄
			   width :990,
			   height : 460,
			   modal : true,
			   i18n:i18n.lms1405s02,
			   readOnly: false,
			   buttons: $.extend({},thickJson)
			 });		
		},
		/**
		 *  查詢合併字串
		 */		
		buildCombine : function(responseData,itemType){
			$.ajax({
				type : "POST",
				handler : "lms1405m01formhandler",
				action : "buildL140m01b",
				data : 
				{
					mainId : $("#tabFormMainId").val(),
					itemType : "1"
				},
				success:function(json){
					var unitNo = userInfo.unitNo;
					$("#loanPolicyExDscr").html(json.loanPolicyExDscr);
					$.thickbox.close();
					//CommonAPI.showMessage(json.NOTIFY_MESSAGE);
				}
			});			
		},
		/**
		 *  不符合授信政策新增事項挑選種類ThickBox
		 */	
		thickAdd : function(){
			var itemType = "1";
			 $.ajax({
    			type : "POST",
    			handler : "lms1405m01formhandler",
				action : "getLoanPolicyExBigKind",
    			data : 
    			{
    				mainId : $("#tabFormMainId").val(),
					isOther : true,
					itemType : itemType
    			},				
    			success:function(responseData){
					$("#tAddFormLoanPolicyEx").find("#seqKind").setItems({
						size : 1,
		                item: responseData.seqKind,
		                format: "{value} - {key}"
					});
					$("#thickAddLoanPolicyEx").thickbox({     // 使用選取的內容進行彈窗
					   title : i18n.lms1405s02['L140S04a.loanPolicyEx']+i18n.lms1405s02['L140S04a.add'],  //不符合授信政策登錄+新增事項
					   width :700,
					   height : 250,
					   modal : true,
					   i18n:i18n.def,
					   valign : "bottom",
					   align : "center",
					   readOnly: false,
					   buttons: {
			             "sure": function() {
						 	var bigKind = $("input[name='seqKind']:radio:checked").val();
							$.thickbox.close();
							l140s04aJson.thickAddSeq(bigKind);
			             },
			             "cancel": function() {
			            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
			 					if(res){
			 						$.thickbox.close();
			 					}
			 		        });
			             }
			           }
				    });				
    			}
			});		
			
				
		},
		
		/**
		 *  不符合授信政策新增事項ThickBox
		 */	
		thickAddSeq : function(bigKind){
		  var itemType = "1";
          $.ajax({
    			type : "POST",
    			handler : "lms1405m01formhandler",
				action : "getLoanPolicyExDetail",
    			data : 
    			{
    				mainId : $("#tabFormMainId").val(),
					isOther : true,
					itemType : itemType,
					bigKind : bigKind
    			},				
    			success:function(responseData){
					$("#tAddSeqFormLoanPolicyEx #seq").setItems({
						size : 1,
		                item: responseData.seq,
		                format: "{value} - {key}"
					});
					setTimeout(function(){
					$("#thickAddSeqLoanPolicyEx").thickbox({     // 使用選取的內容進行彈窗
					   title : i18n.lms1405s02['L140S04a.loanPolicyEx'],	//不符合授信政策
					   width :750,
					   height : 400,
					   modal : true,
					   i18n:i18n.def,
					   valign : "bottom",
					   align : "center",	   
					   readOnly: false,
					   buttons: {
					             "sure": function() {
								 	// 將使用者勾選不符合授信政策事項存成陣列
								 	var seqNos = [];
								 	$("#tAddSeqFormLoanPolicyEx").find("[name'seq']:checkbox:checked").each(function(){
										seqNos.push($(this).val());
									});									
					          		$.ajax({
						    			type : "POST",
						    			handler : "lms1405m01formhandler",
										action : "addLoanPolicyEx",
						    			data : 
						    			{
											seqNos : seqNos,
						    				mainId : $("#tabFormMainId").val(),
											unitType : userInfo.unitType,
											itemType : itemType,
					                        bigKind : bigKind
						    			},
						    			success:function(responseData){
											$("#loanPolicyExDscr").html(responseData.loanPolicyExDscr);
											$("#loanPolicyExGrid").trigger("reloadGrid");
											$.thickbox.close();
											$.thickbox.close();
											CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
						    			}
									});
					             },				             
					             "cancel": function() {
					            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
					 					if(res){
					 						$.thickbox.close();
					 					}
					 		        });
					             }
					           }
					    });						
					},300);					
    			}
			});		
		},
		/**
		 *  不符合授信政策明細表ThickBox
		 */	
		thickGridContent : function(data){
			 
			if($.trim(data.bigKind) != "F"){
				//僅「F.其他」類別可登錄說明
				API.showMessage(i18n.lms1405s02['L140S04a.message01']);
				return false;
			}
			
			var seqNoHead = data.seqNo.substring(0,1);
			var isOther = false;
			var itemType = "1";


             var thickJson = {};
			
			if(inits.toreadOnly || l140s04aJson.openTGridContentFormLoanPolicyExForReadOnly ){
				thickJson = {
					"close": function() {
		            	$.thickbox.close();
		             }
				};	
			}else{ 
				thickJson = {
					  "saveData": function() {
					 	// 呼叫檢核方法進行檢核
					 	if(l140s04aJson.checkData()){
							// 檢核不通過就中止
							return;	
						}
						// 開始進行儲存作業
						if($("#tGridContentFormLoanPolicyEx").valid()){
			          		$.ajax({
				    			type : "POST",
				    			handler : "lms1405m01formhandler",
								action : "saveL140s04a",
				    			data : 
				    			{
									oid : data.oid,
									mainId : $("#tabFormMainId").val(),
									itemType: itemType,
				    				tGridContentFormLoanPolicyEx : JSON.stringify($("#tGridContentFormLoanPolicyEx").serializeData())
				    			},
				    			success:function(responseData){
									$("#loanPolicyExDscr").html(responseData.loanPolicyExDscr);
								    $("#loanPolicyExGrid").trigger("reloadGrid");
									//$.thickbox.close();
				    			}
							});										
						}
		             },				             
		             "close": function() {
		            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
		 					if(res){
		 						$.thickbox.close();
		 					}
		 		        });
		             }
					 
				};	
					 
			}
			
			// 開始查詢不符合授信政策明細內容
      		$.ajax({
    			type : "POST",
    			handler : "lms1405m01formhandler",
				action : "queryL140s04a",
    			data : 
    			{
    				oid : data.oid,
					isOther : isOther,
					itemType : itemType
    			},
    			success:function(responseData){					
					$("#tGridContentFormLoanPolicyEx").setData(responseData.tGridContentFormLoanPolicyEx);
					
					// 開啟明細thickBox界面
					$("#thickGridContentLoanPolicyEx").thickbox({     // 使用選取的內容進行彈窗
					   title : i18n.lms1405s02['L140S04a.loanPolicyEx']+ i18n.lms1405s02['L140S04a.detail'],	//不符合授信政策明細+明細
					   width :950,
					   height : 450,
					   modal : true,
					   i18n:i18n.def,
					   //valign : "bottom",
					   //align : "center",	   
					   readOnly: false,
					   buttons: thickJson
					});					
    			}
			});		
		},
		/**
		 *  異常通報明細儲存前的檢核
		 */		
		checkData : function(){
			return false;	
		},
		queryCaseBranchCountry : function(){
			var caseBrIdCountry = "";
			$.ajax({
		        handler: "lms1405m01formhandler",
				async: false ,
		        data: {
		            formAction: "queryCaseBranchData",
					caseMainId: responseJSON.mainId
		        },
		        success: function(obj){
				  caseBrIdCountry = obj.country;
                  
		        }
		    });
			
			 return caseBrIdCountry;
		}
		
		
	};
  
}

var RealEstateAction = {
	    _isLoad: false,
		beforeGrid: null,
		afterGrid: null,
		fileGrid: null,
		isMegaInternalCntrNo: null,
		realEstateFormDetailData: {},
		_initEvent: function(){
			
	        $("#addRealEaste").click(function(){
	        	$("input[name='isBuy'],input[name='isInstalment']").addClass("required");
	        	if ($("input[name='isBuy'],input[name='isInstalment']").valid()) {
	        		RealEstateAction.addRealEstate();
	        	}
	        	$("input[name='isBuy'],input[name='isInstalment']").removeClass("required");
	        })
	        $("#deleteRealEaste").click(function(){
	            RealEstateAction.deleteRealEstate();
	        })
		
			$("input[name='estateSubType']").click(function(){
				RealEstateAction.setEditEstateBoxUI();
				//J-109-0248_05097_B1001 Web e-Loan授信都更之計畫進度，將已送件未核定納入排除72-2項目
				$('#estateStatus').trigger('change');
			})
			
			//J-109-0248_05097_B1001 Web e-Loan授信都更之計畫進度，將已送件未核定納入排除72-2項目
			$('#estateStatus').change(function(){
				var estateSubType = $("input[name='estateSubType']:radio:checked").val();
				var estateStatus = $(this).val() ;
				if(estateSubType == '02' && estateStatus == '2' ){
					$('#showTipsForStatus_2').show();
				}else{
					$('#showTipsForStatus_2').hide();
				}
				//J-110-0054_10702_B1001 Web e-Loan額度明細表不動產暨72-2相關註記修改
				if(estateSubType == '03'){
					$("#estateSubType03").show();
					$("#estateOwner").addClass("required");
				}
				else{
					$("#estateSubType03").hide();
					$("#estateOwner").removeClass("required");
				}
			});
	        
			/*
			$("input[name='isCityRebuild']").click(function(){
				var value = $(this).val();
				if(value == "N"){
					$("input[name='estateSubType'][value='01']").attr("checked", true);
					RealEstateAction._isLoad && RealEstateAction.setEditEstateBoxUI();
				} else {
					$("input[name='estateSubType'][value='01']").attr("checked", false);
					RealEstateAction._isLoad && RealEstateAction.setEditEstateBoxUI();
				}
			})
			*/
			$("input[name='buildWay']").click(function(){
				if($(this).val() != "02" && $(this).val() != "03"){
					$("#landlordNum").val("")
				}
				//J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
				if($(this).val() != "05"){
					$("#otherDesc").val("")
				}
				RealEstateAction._isLoad && RealEstateAction.setEditEstateBoxUI();
			})
			
			$("#mCntrNo").change(function(){
				//$(':input', '#realEstateFormDetail').not('input[name="isCityRebuild"],#mCntrNo,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
				$(':input', '#realEstateFormDetail').not('#mCntrNo,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
				RealEstateAction._isLoad &&  RealEstateAction.setEditEstateBoxUI();
			})
			
			$("input[name='position']").change(function(){
				RealEstateAction._isLoad && RealEstateAction.setEditEstateBoxUI();
			});
				
			$("#estateButton").click(function(){
				//$(':input', '#realEstateFormDetail').not('input[name="isCityRebuild"],#mCntrNo,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
				$(':input', '#realEstateFormDetail').not('#mCntrNo,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
		        $.ajax({
		            handler: "lms1405m01formhandler",
		            action: "queryL140M01TByMCntrNo",
		            data: {
		            	mCntrNo : $("#mCntrNo").val()
		            },
		            success: function(obj){
		            	$("#realEstateFormDetail").injectData(obj);
						RealEstateAction.realEstateFormDetailData = obj;

	               
	             	   $("#realEstateFormDetail").find("select").trigger("change", "init");
						RealEstateAction.setEditEstateBoxUI();
						//J-110-0054_10702_B1001 Web e-Loan額度明細表不動產暨72-2相關註記修改
						var estateSubType=$("input[name='estateSubType']:radio:checked").val();					
						if(estateSubType == '03'){
							$("#estateSubType03").show();
							$("#estateOwner").addClass("required");
						}
						else{
							$("#estateSubType03").hide();
							$("#estateOwner").removeClass("required");
							$("#estateOwner").val("");
						}
		            }
				})			
				
			})
			
			$("#uploadEstateFile").click(function(){
				RealEstateAction.uploadEstateFile();
			});
			
			$("#deleteEstateFile").click(function(){
				RealEstateAction.deleteEstateFile();
			});
			
			//融資業務分類只可選擇一筆
			//全部改成都只能選一筆
			var $a0 = $("input[name='estateType']");
			$a0.click(function(){
				var checkCanClick = true;
				$a0.each(function(k, v){
					var isDiAndCheck = ($(this).prop("checked")) && ($(this).prop("disabled"))				
					if(isDiAndCheck){
						checkCanClick = false;
						return false;
					}
				})
				if(!checkCanClick){
					return checkCanClick;
				} else {
					var value = $(this).val();				
					//$(this).parent("label").siblings("label").find("input").attr("checked", false);
					$("input[name='estateType']").filter("[value !='"+ value + "']").prop("checked", false);
				}			
			})
			
			
			var defaultSelect = "<option value=''>" + i18n.def.comboSpace + "</option>";
	        $("#estateCityId").setItems({
	            item: QueryCityCode.getCode("1", ""),
	            sort: "asc",
	            value: "",
	            fn: function(k, v){
	            	RealEstateAction.changCityValue($("#estateCityId"), $("#estateAreaId"), $("#estateSit3No"), $("#estateSit4No"), defaultSelect, (!v));
	            }
	        });
	        
	        $("#estateAreaId").change(function(k, v){				
				if ($("#estateAreaId").val() != "") {
					RealEstateAction.changAreaValue($("#estateCityId"), $("#estateAreaId"), $("#estateSit3No"), $("#estateSit4No"), defaultSelect, (!v));
				}
	            
	        });
			
			
			$("#estateCityIdXX").setItems({
	            item: QueryCityCode.getCode("1", ""),
	            sort: "asc",
	            value: "",
	            fn: function(k, v){
	            	RealEstateAction.changCityValue($("#estateCityIdXX"), $("#estateAreaIdXX"), $("#estateSit3NoXX"), $("#estateSit4NoXX"), defaultSelect, (!v));
	            }
	        });
			
			$("#estateAreaIdXX").change(function(k, v){
				if ($("#estateAreaIdXX").val() != "") {
					RealEstateAction.changAreaValue($("#estateCityIdXX"), $("#estateAreaIdXX"), $("#estateSit3NoXX"), $("#estateSit4NoXX"), defaultSelect, (!v));
				}
	            
	            
	        });
			
	        $("input[name='sectKind']").change(function(k, v){
				var useItem = "";
	            var value = $(this).val();				
	            if (value == "1") {
	                useItem = CommonAPI.loadCombos(["LandUse21", "cms1010_useKind1"]);
	                $("#useSect").setItems({
	                    item: useItem.LandUse21,
	                    format: "{key}"
	                });
	                $("#useKind").setItems({
	                    item: useItem.cms1010_useKind1,
	                    format: "{key}"
	                });
	            } else if(value == "2"){
					useItem = CommonAPI.loadCombos(["LandUse22", "cms1010_useKind2"]);
	                $("#useSect").setItems({
	                    item: useItem.LandUse22,
	                    format: "{key}"
	                });
	                $("#useKind").setItems({
	                    item: useItem.cms1010_useKind2,
	                    format: "{key}"
	                });
				}
				
				if(RealEstateAction.realEstateFormDetailData.useSect){
					$("#useSect").val(RealEstateAction.realEstateFormDetailData.useSect);
				}
	            if(RealEstateAction.realEstateFormDetailData.useKind){
					$("#useKind").val(RealEstateAction.realEstateFormDetailData.useKind);
				}
	        })
			
			
			
		},
		_reloadBeforeGrid:function(){
	        this.beforeGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
	            postData: {
	                tabFormMainId: $("#tabFormMainId").val(),
	                mainId: $("#mainId").val(),
	                flag: "N"
	            }
	        }).trigger("reloadGrid");
		},
		_reloadAfterGrid:function(){	
	        this.afterGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
	            postData: {
	                tabFormMainId: $("#tabFormMainId").val(),
	                mainId: $("#mainId").val(),
	                flag: "Y"
	            }
	        }).trigger("reloadGrid");	        
		},
		_initGrid: function(){
			this.beforeGrid = $("#realEstateBeforeGrid").iGrid({
				height: 100,
				handler: inits.ghandle,
				sortname: 'createTime',
				sortorder: 'asc',
				action: "queryL140m01t",
				postData: {
					tabFormMainId: $("#tabFormMainId").val(),
					mainId: $("#mainId").val(),
					flag: "N"
				},
				loadComplete: function(){
					$('#realEstateBeforeGrid a').click(function(e) {
						// 避免<a href="#"> go to top
						e.preventDefault();
					});
				},
				colModel: [{
					colHeader: i18n.lms1405s02["L140M01T.view.001"],//類別
					width: 80,
					name: 'estateType',
					sortable: true,
					formatter: 'click',
					onclick: RealEstateAction.openEditEstateBox
				}, {
					colHeader: " ",
					name: 'checkYN',
					align: 'center',
					width: 5
				}, {
					colHeader: "oid",
					name: 'oid',
					hidden: true
				}, {
					colHeader: "flag",
					name: 'flag',
					hidden: true
				}],
				ondblClickRow: function(rowid){
					var data = RealEstateAction.beforeGrid.getRowData(rowid);
					RealEstateAction.openEditEstateBox(null, null, data);
				}
			});
			
			this.afterGrid = $("#realEstateAfterGrid").iGrid({
				height: 100,
				handler: inits.ghandle,
				sortname: 'createTime',
				sortorder: 'asc',
				action: "queryL140m01t",
				postData: {
					tabFormMainId: $("#tabFormMainId").val(),
					mainId: $("#mainId").val(),
					flag: "Y"
				},
				loadComplete : function() {
		
					$('#realEstateAfterGrid a').click(function(e) {
						// 避免<a href="#"> go to top
						e.preventDefault();
					});
				},
				colModel: [{
					colHeader: i18n.lms1405s02["L140M01T.view.001"],//類別
					width: 80,
					name: 'estateType',
					sortable: true,
					formatter: 'click',
					onclick: RealEstateAction.openEditEstateBox
				}, {
					colHeader: " ",
					name: 'checkYN',
					align: 'center',
					width: 5
				}, {
					colHeader: "oid",
					name: 'oid',
					hidden: true
				}, {
					colHeader: "flag",
					name: 'flag',
					hidden: true
				}],
				ondblClickRow: function(rowid){
					var data = RealEstateAction.afterGrid.getRowData(rowid);
					RealEstateAction.openEditEstateBox(null, null, data);
				}
			});
		},
		_init: function(){
	        if (!this._isLoad) {
	            this._initGrid();
	            this._initEvent();
	            this.initFileGrid();
	            this._isLoad = true;            
	        }
	        else {
	            this._reloadGrid();
	        }
	    },
	    initFileGrid: function(){
	    	this.fileGrid = $("#estateFileGrid").iGrid({
				height: 100,
				handler: inits.ghandle,
				sortorder: 'asc',
				action: "queryFile",
				localFirst: true,
				postData: {
					tabFormMainId: $("#tabFormMainId").val(),
					fieldId:"estateType" + $("#_estateType").val()
				},
				loadComplete : function() {
		
					$('#estateFileGrid a').click(function(e) {
						// 避免<a href="#"> go to top
						e.preventDefault();
					});
				},
				colModel: [{
					colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
					width: 80,
					name: 'srcFileName',
					sortable: true,
					formatter: 'click',
					onclick: function (cellvalue, options, rowObject){
					    $.capFileDownload({
					        handler:"simplefiledwnhandler",
					        data : {
					            fileOid:rowObject.oid
					        }
					    });
					}
				}, {
	                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
	                name: 'fileDesc',
	                width: 100,
	                align: "center",
	                sortable: true
	            }, {
	                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
	                name: 'uploadTime',
	                width: 100,
	                align: "center",
	                sortable: true
					},
				{
					colHeader: "oid",
					name: 'oid',
					hidden: true
				}]
			});
	    },
	    _reloadFileGrid:function(){
	        this.fileGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
	            postData: {
	            	tabFormMainId: $("#tabFormMainId").val(),
	            	fieldId:"estateType" + $("#_estateType").val()
	            }
	        }).trigger("reloadGrid");
		},
	    deleteRealEstate: function(){

	        var girdId = this.afterGrid.getGridParam('selrow');
	        var data = this.afterGrid.getRowData(girdId);
	        if (!girdId) {          
	            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	        }
	        
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(result){
                $.ajax({
                    handler: "lms1405m01formhandler",
                    action: "deleteL140m01t",
                    data: {
                        tOid: data.oid,
                        tabFormMainId: $("#tabFormMainId").val()
                    },
                    success: function(obj){
                        RealEstateAction.afterGrid.trigger("reloadGrid");
                        
                        $("input[name='is722Flag']").prop('checked', false);
                        $("input[name='is722Flag'][value='" + obj.is722Flag + "']").prop("checked", true);
                    }
                })
            });
	        
	    
	    },
	    setEditEstateBoxUI: function(){
			
	    	//alert("been called")
	    	$("#reBuildView1").hide();
	    	$("#reBuildView2").hide();
	    	$("#reBuildView3").hide();
	    	$("#reBuildView4").hide();
			$("#landlordView").hide();
			$("#otherDescView").hide(); //J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
	    	$("#_estateSubType04View").hide();
			
			
			$(".estateView").hide();
			
			// 是否為營運周轉金用途
			var isTurnOver = $("input[name='isTurnOver']").val();
	    	    	    	
	    	var value1 = $("#_estateType").val();
					
			if(value1 == "A0#" || value1.substring(0, 1) == "D"){
				$("#" + value1.replace( /(:|\.|\[|\]|,|=|@|#)/g, "\\$1" ) + "_View").show().siblings("div:not(#estateFileGridView)").hide();
				if(value1 != "D01"){
					$("#common_View").show()
				}
				
//				$("input[name='sectKind']").triggerHandler("change")
//				$("#realEstateFormDetail").find("select").trigger("change", "init");
			}
			
			var position = $("input[name='position']:checked").val();
			
			if(position == "0"){
            	$("#commonSiteView").show();
            	$("#forInner").show();
            } else if(position == "1"){
            	$("#commonSiteView").hide();
            	$("#forInner").hide();
            }

			// 建築物在海外時。土地使用分區類別、使用分區、用地類別 隱藏
            if(value1 == "A0#"){
                $("#common_comp1").insertBefore("#forInner");
            } else {
                $("#common_comp1").insertBefore("#common_comp2");
            }
			
	    	if(value1 == "D01"){
	    		$("#reBuildView1").show();
				// 當是都更危老時，如果是海外額度序號，或是營運周轉金的用途，名細都可以編輯
				if(isTurnOver == "Y" || !RealEstateAction.isMegaInternalCntrNo){
					$("#reBuildView4").hide();
					$("#reBuildView1 input").prop("disabled", false).prop("readOnly", false);
					$( "#overDate").datepicker(  );
					
					$("#reBuildView2").find("input, select").each(function(){
						if($(this).hasClass("editable")){
							
						} else {
							$(this).prop("disabled", false);
							$(this).prop("readOnly", false);
						}
					})
				} else {
					$("#reBuildView4").show();
					$("#reBuildView1 input").prop("disabled", true).prop("readOnly", true);
					$( "#overDate").datepicker( "destroy" );
					$("#reBuildView2").find("input, select").each(function(){
						if($(this).hasClass("editable")){
							
						} else {
							$(this).prop("disabled", true);
							$(this).prop("readOnly", true);
						}
					})
				}
	    		
	    	}
	    	if(value1 == "D01"){
				
	    		//var isCityRebuild = $("input[name='isCityRebuild']:checked").val();
				//當是都更危老時，非營運周轉金的用途，國內額度序號才出現引入按鈕
				
	    		if((isTurnOver == "N" && RealEstateAction.isMegaInternalCntrNo) ){
					//母戶額度序號引入
	    			$("#reBuildView3").show();
	    		}
	    		
	    		value2 = $("input[name='estateSubType']:checked").val()
	    		if(value2 == undefined || value2 == "01"){
	    			$("#reBuildView2").hide();
	    		} else {
	    			$("#reBuildView2").show();
	    			    			
//	    			RealEstateAction.fileGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料
//	    	            postData: {
//	    	            	tabFormMainId: $("#tabFormMainId").val(),
//	    	            	fieldId: "estateType" + $("#_estateType").val()
//	    	            },
//	    	            search: true
//	    	        }).trigger("reloadGrid");
	    			
	    		}
	    		if(value2 == "04") {
	    			$("#_estateSubType04View").show();
	    		} else {
	    			$("#_estateSubType04View").hide();
	    		}
				
				var buildWay = $("input[name='buildWay']:checked").val();
				if(buildWay == "02" || buildWay == "03"){
					$("#landlordView").show();
				}
				//J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
				if(buildWay == "05"){
					$("#otherDescView").show();
				}
	    	} else {
	    		$("#reBuildView2").hide();
	    	}
	    	RealEstateAction._reloadFileGrid();
			
	    	
	    },
	    openEditEstateBox: function(cellvalue, options, data){
			
	        var $form = $("#realEstateFormDetail");
	        $form.reset();
			
			//$form.find(":input").filter(".editable").removeAttr("disabled").removeAttr("readonly");
			$form.find(":input").removeAttr("disabled").removeAttr("readonly");
			
			var buttons = {
				"saveData": function(){
		            if ($form.valid()) {
		                $.ajax({
		                    handler: inits.fhandle,
		                    formId: "empty",
		                    action: "saveL140M01T",
		                    data: $.extend($("#realEstateFormDetail").serializeData(), {
		                        tabFormMainId: $("#tabFormMainId").val(),
		                        oid: data.oid
		                    }),
		                    
		                    success: function(obj){
								RealEstateAction.afterGrid.trigger("reloadGrid");
		                    
		                        //saveSuccess=儲存成功
		                        CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
		                            if (b) {
		                                $.thickbox.close();
		                            }
		                        });
								$("input[name='is722Flag']").prop('checked', false);
								$("input[name='is722Flag'][value='"+obj.is722Flag+"']").prop("checked", true);
		                    }
		                });
		            }
		            
		        },
		        "close": function(){
		            $.thickbox.close();
		        }
			}
	 

			
	        $.ajax({
	            handler: inits.fhandle,
	            formId: "empty",
	            action: "queryL140M01T",
	            data: {
	                tOid: data.oid,
					tabFormMainId : $("#tabFormMainId").val()
					
	            },
	            success: function(obj){
	            	RealEstateAction.realEstateFormDetailData = obj;
					RealEstateAction.isMegaInternalCntrNo = obj.isMegaInternalCntrNo;
	                $form.injectData(obj);
					$("input[name='sectKind']:checked").triggerHandler("change")
	                $form.find("select").trigger("change", "init");
	                RealEstateAction.setEditEstateBoxUI();
					
	                var itemType2Special = false;
					
	                //alert(inits.itemType);
					if(inits.itemType == "2"){
						var caseBox16 = $("#caseBox16").prop("checked");
						if(!caseBox16){
							itemType2Special = true;
						}
					}

	                if (data.flag == "N" || itemType2Special || inits.toreadOnly) {
	                    delete buttons.saveData
	                    //$form.find(":input").filter(".editable").attr("disabled", true).attr("readonly", true);
	                    $form.find(":input").prop("disabled", true).prop("readonly", true);
	                    //$("#estateFileGridView").hide();
	                    $form.find("button").hide();
	                }
	                else {
	                    $("#estateFileGridView").show();
	                    $form.find("button").show();
	                }	
					
					
	                $("#realEstateDetailThickbox").thickbox({
	                    title: i18n.lms1405s02["L140M01T.dial.001"], //"不動產暨72-2相關資訊註記",
	                    width: 800,//$("#_estateType").val() == "001" ? 800 : 200,
	                    height: 400,//$("#_estateType").val() == "001" ? 400 : 150,
	                    modal: true,
	                    readOnly: _openerLockDoc == "1" || inits.toreadOnly,
	                    i18n: i18n.def,
	                    buttons: buttons
	                });
	            }
	        });
	    },
	    addRealEstate: function(){
	    	var buttons = {};
	    	buttons[i18n.def.newData] = function(){
	    		$.ajax({
	                handler: "lms1405m01formhandler",
	                action: "quickAddEstateDatas",
	                data: $.extend($("#realEstateForm").serializeData(),{
	                	tabFormMainId : $("#tabFormMainId").val(),
						isInstalment: $("input[name='isInstalment']:checked").val()
	                }),
                    success: function(obj){
                        $.thickbox.close();
                        RealEstateAction.afterGrid.trigger("reloadGrid");
                        $("input[name='is722Flag']").prop('checked', false);
                        $("input[name='is722Flag'][value='" + obj.is722Flag + "']").prop("checked", true);
                        if (obj.warnMessage) {
                            CommonAPI.showMessage(obj.warnMessage);
                        }
                    }
	    		})
	    	};
			buttons[i18n.def.close] = function(){
	    		$.thickbox.close()
	    	};
	    	
	    	$.ajax({
	            handler: "lms1405m01formhandler",
	            action: "getSelectedEstateType",
	            data: {
	            	tabFormMainId : $("#tabFormMainId").val()
	            },
	            success: function(obj){
	            	$("input[name='estateType']").prop("disabled", false).prop("checked", false);
	            	for(var tst in obj.estateType){
	            		$("input[name='estateType'][value='" + obj.estateType[tst] + "']").prop("disabled", true).prop("checked", true);
	            	}
	            }
			}).done(function(){
				$("#realEstateThickbox").thickbox({
					title: i18n.lms1405s02["L140M01T.dial.001"], //"不動產暨72-2相關資訊註記",
		    		modal: false,
		    		width:600,
		    		height:500,
		    		open: function(){
		    			
		    		},
		    		buttons:buttons
		    	})
			})
			
			
	    	
	    },
	     changCityValue: function(tCity, tArea, tSite3, tSite4, defaultSelect, isClear){

	        var value = tCity.val();
	        var obj = QueryCityCode.getCode("2", value);
	        
	        
	        tArea.setItems({
	            item: obj,
	            value: RealEstateAction.realEstateFormDetailData['estateAreaId'] || ""
	        });
	        
	        if (isClear) {
	            tSite3.html(defaultSelect);
	            tSite4.html(defaultSelect);
	            RealEstateAction.realEstateFormDetailData['estateAreaId'] = "";
	            RealEstateAction.realEstateFormDetailData['estateSit3No'] = "";
	            RealEstateAction.realEstateFormDetailData['estateSit4No'] = "";
	        }
	    },
	    changAreaValue : function (tCity, tArea, tSite3, tSite4, defaultSelect, isClear){
	        if (isClear) {
	            
	        }
	        
	        tSite3.setItems({
	            item: RealEstateAction.getSITE3(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
	            value: util.addZeroBefore($.trim(RealEstateAction.realEstateFormDetailData['estateSit3No']).replace(",", "") || "", 4)
	        });
	        
	        tSite4.setItems({
	            item: RealEstateAction.getSITE4(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
	            value:RealEstateAction.realEstateFormDetailData['estateSit4No'] || ""
	        });
	        
	    },
	    getSITE3: function(fCity, fZip, loanBuild){
	        if (!fCity || !fZip) 
	            return {};
	        var SITE3 = null;
	        $.ajax({
	            handler: "lmscommonformhandler",
	            action: "querySIET3",
	            type: 'post',
	            async: false,
	            formId: "empty",
	            data: {
	                fCity: fCity,
	                fZip: fZip
	            },
	            success: function(obj){
	                if (loanBuild == "Y") {
	                    SITE3NO = obj['SITE3NO'];
	                }
	                else {
	                    SITE3 = obj['SITE3'];
	                }
	            }
	        });
	        return SITE3;
	    },getSITE4: function(fCity, fZip, loanBuild){
	        if (!fCity || !fZip) 
	            return {};
	        var SITE4 = null;
	        $.ajax({
	            handler: "lmscommonformhandler",
	            action: "querySIT4NO",
	            type: 'post',
	            async: false,
	            formId: "empty",
	            data: {
	                LOCATE1DESC: fCity,
	                LOCATE2DESC: fZip
	            },
	            success: function(obj){
	                if (loanBuild == "Y") {
	                    SITE4NO = obj['SITE4NO'];
	                }
	                else {
	                    SITE4 = obj['SITE4'];
	                }
	            }
	        });
	        return SITE4;
	    },
	    uploadEstateFile:function(){

			var limitFileSize=3145728;
			MegaApi.uploadDialog({
				fieldId:"estateType" + $("#_estateType").val().replace( /(:|\.|\[|\]|,|=|@|#)/g, "\\$1" ),
	            fieldIdHtml:"size='30'",
	            fileDescId:"fileDesc",
	            fileDescHtml:"size='30' maxlength='30'",
				subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
				limitSize:limitFileSize,
	            width:320,
	            height:190,
				data:{
					mainId:$("#tabFormMainId").val()
				},
				success : function(obj) {
					RealEstateAction.fileGrid.trigger("reloadGrid");
				}
		   });
		
	    },
	    deleteEstateFile:function(){

	    	var select  = this.fileGrid.getGridParam('selrow');		
			// confirmDelete=是否確定刪除?
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){				
					var data = RealEstateAction.fileGrid.getRowData(select);
					if(data.oid == "" || data.oid == undefined || data.oid == null){		
						// TMMDeleteError=請先選擇需修改(刪除)之資料列
						CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
						return;
					}				
					$.ajax({
						handler : "lms1405m01formhandler",
						type : "POST",
						dataType : "json",
						data : {
							formAction : "deleteUploadFile",
							fileOid:data.oid
						},
						success : function(obj) {
							RealEstateAction.fileGrid.trigger("reloadGrid");
						}
					});
				}else{
					return ;
				}
			});
		
	    }
	    
	};

// J-108-0293 Web e-Loan 未依銀行內部規定 internal regulations特別加註"特殊案件"
var IntRegAction = {
    _isLoad: false,
    intRegGrid: null,
    IntRegFormData: {},
    _initEvent: function(){
    
    	//J-112-0284_05097_B1001 Web e-Loan企金授信額度明細表配合修訂後本行「授信利率暨保證及承兌手續費計收標準實施要點」，修改「與本行內部規範不符之註記說明」
    	$.ajax({
            handler: "lms1401m01formhandler",
            action: "initComboListItem",
            data: {
            	tabFormMainId: $("#tabFormMainId").val(),
                mainId: $("#mainId").val()
            }, 
            success: function(obj){
            	var item = API.loadCombos("lms140_intReg")["lms140_intReg"];
            	
            	//J-112-0284_05097_B1001 Web e-Loan企金授信額度明細表配合修訂後本行「授信利率暨保證及承兌手續費計收標準實施要點」，修改「與本行內部規範不符之註記說明」
    			if(obj.stopUseIntRegList){   
    				$.each(JSON.parse(obj.stopUseIntRegList), function(i,v){   
//    			        $.each(item, function(j,k){  
//    						if(k.value==v){
//    							alert("v="+v+"，k.value="+k.value+"，k.desc="+k.desc);
//    							item.splice(i, 1)
//    						}
//    					});
    			        for (var j in item){
    			            if(item[j].value == v){
    			            	 item.splice(j, 1)
    			            }
    			        }
    				});
    			}
    			
    			$("#intRegList").setItems({
		            size: "1",
		            item: item,
		            clear: true,
		            itemType: 'checkbox'
		        });
    			
    			//J-111-0411_05097_B1001 Web e-Loan企金授信新增不動產授信例外管理相關功能 
    	        var lms140_intRegReason = API.loadCombos("lms140_intRegReason")["lms140_intRegReason"];
    	        $("#intRegReason").setItems({
    	            size: "1",
    	            item: lms140_intRegReason,
    	            clear: true,
    	            itemType: 'checkbox'
    	        });
    	        
    	        $("input[name='intRegList']").click(function(){
    	            var value = $(this).val();
    	            if (!$(this).is(':checked')) {
    	                $("input[name='intRegList']").prop('disabled', false);
    	            }
    	            else {
    	                if (value == "N") { // 無需註記 則 清空其他註記且唯讀
    	                    $("input[name='intRegList']").filter("[value !='" + value + "']").prop("checked", false).prop('disabled', true);
    	                }
    	            }
    	        });
 
            }
        });
   
        $("#addIntReg").click(function(){
            IntRegAction.addIntReg();
        });
        $("#deleteIntReg").click(function(){
            IntRegAction.deleteIntReg();
        });
    },
    _initGrid: function(){
        this.intRegGrid = $("#intRegGrid").iGrid({
            height: 100,
            handler: inits.ghandle,
            sortname: 'intReg|createTime',
            sortorder: 'asc|asc',
            action: "queryL140s06aList",
            postData: {
                tabFormMainId: $("#tabFormMainId").val(),
                mainId: $("#mainId").val()
            },
            loadComplete: function(){
                $('#intRegGrid a').click(function(e){
                    // 避免<a href="#"> go to top
                    e.preventDefault();
                });
            },
            colModel: [{
                colHeader: i18n.lms1405s02["L140M01a.reg"],
                width: 80,
                name: 'intReg',
                sortable: true,
                formatter: 'click',
                onclick: IntRegAction.openEditIntRegBox
            }, {
                colHeader: " ",
                name: 'checkYN',
                align: 'center',
                width: 5
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = IntRegAction.intRegGrid.getRowData(rowid);
                IntRegAction.openEditIntRegBox(null, null, data);
            }
        });
    },
    _init: function(){
        if (!this._isLoad) {
            this._initGrid();
            this._initEvent();
            this._isLoad = true;
        }
        else {
            this._reloadGrid();
        }
    },
    openEditIntRegBox: function(cellvalue, options, data){
        var $form = $("#intRegFormDetail");
        $form.reset();
        var buttons = {
            "saveData": function(){
                if ($form.valid()) {
                    var memo = $("#intRegMemo").val();
                    
                    if ($.trim(memo) == "" && $("#intRegCode").val() != "N") {
                        // L140M01a.checkMemo=說明不得為空
                        return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.checkMemo"]);
                    }
                    $.ajax({
                        handler: "lms1405m01formhandler",
                        action: "saveL140S06A",
                        data: $.extend($("#intRegFormDetail").serializeData(), {
                            mainId: responseJSON.mainId,//$("#mainId").val(),
                            oid: data.oid
                        }),
                        
                        success: function(obj){
                            IntRegAction.intRegGrid.trigger("reloadGrid");
                            
                            //saveSuccess=儲存成功
                            CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                                if (b) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    });
                }
            },
            "close": function(){
                $.thickbox.close();
            }
        }
        
        $.ajax({
            handler: "lms1405m01formhandler",
            action: "queryL140S06Adetail",
            data: {
                tOid: data.oid,
                mainId: responseJSON.mainId
            },
            success: function(obj){
            	
            	//J-111-0411_05097_B1001 Web e-Loan企金授信新增不動產授信例外管理相關功能
            	//本項逾規定之類別 ○利率 ○成數 ○申請豁免不利違約事項 ○未檢附興建計畫/營運週轉計畫/房屋銷售計畫/投資理財計畫書 ○其他  ﹙以上可複選﹚
            	if(obj.showRegReason == 'Y'){
            		$("#showIntRegReason").show();
            	}else{
            		$("#showIntRegReason").hide();
            	}
            	
                IntRegAction.IntRegFormData = obj;
                $form.injectData(obj);
				
				var itemType2Special = false;
//				inits.itemType//文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
				if(inits.itemType == "2"){
					var caseBox29 = $("#caseBox29").prop("checked");
					if(!caseBox29){
						itemType2Special = true;
					}
				}
				
				if (itemType2Special || inits.toreadOnly) {
                    delete buttons.saveData
                    $form.find("textarea").prop("disabled", true).prop("readonly", true);
                    $form.find("button").hide();
                } else {
					$form.find("textarea").removeAttr("disabled").removeAttr("readonly");
                    $form.find("button").show();
                }
				
				//J-111-0411_05097_B1001 Web e-Loan企金授信新增不動產授信例外管理相關功能
				$("[name=intRegReason]").removeAttr("disabled").removeAttr("checked");
		        var intRegReason = obj.intRegReason;
		        if (intRegReason != "") {
		        	var vals = intRegReason.split("|");
	                for (var i in vals) {
	                    $("[name=intRegReason][value=" + vals[i] + "]").prop("checked", true);
	                }
		        }
                
                $("#intRegDetailThickbox").thickbox({
                    title: "",
                    width: 900,
                    height: 400,
                    modal: true,
                    readOnly: _openerLockDoc == "1" || inits.toreadOnly, 
                    i18n: i18n.def,
                    buttons: buttons
                });
            }
        });
    },
    addIntReg: function(){
        var buttons = {};
        buttons[i18n.def.newData] = function(){
            $.ajax({
                handler: "lms1405m01formhandler",
                action: "quickAddIntRegList",
                data: $.extend($("#intRegForm").serializeData(), {
                    tabFormMainId: $("#tabFormMainId").val()
                }),
                success: function(obj){
                    $.thickbox.close();
                    IntRegAction.intRegGrid.trigger("reloadGrid");
                }
            })
            
        };
        buttons[i18n.def.close] = function(){
            $.thickbox.close()
        };
        
        $.ajax({
            handler: "lms1405m01formhandler",
            action: "getSelectedIntReg",
            data: {
                tabFormMainId: $("#tabFormMainId").val()
            },
            success: function(obj){
                var checkN = false;
                $("input[name='intRegList']").prop("disabled", false).prop("checked", false);
                for (var tst in obj.intRegList) {
                    $("input[name='intRegList'][value='" + obj.intRegList[tst] + "']").prop("disabled", true).prop("checked", true);
                    if (obj.intRegList[tst] == "N") {
                        checkN = true;
                    }
                }
				
                if (checkN) {
                    $("input[name='intRegList'][value!='N']").prop("disabled", true).prop("checked", false);
                } else if (obj.intRegList.length > 0) {
                    $("input[name='intRegList'][value='N']").prop("disabled", true).prop("checked", false);
                }
            }
        }).done(function(){
            $("#intRegThickbox").thickbox({
                title: "",
                modal: false,
                width: 600,
                height: 500,
                open: function(){
                
                },
                buttons: buttons
            })
        })
    },
    deleteIntReg: function(){
        var girdId = this.intRegGrid.getGridParam('selrow');
        var data = this.intRegGrid.getRowData(girdId);
        if (!girdId) {
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(result){
            if (result) {
                $.ajax({
                    handler: "lms1405m01formhandler",
                    action: "deleteL140s06a",
                    data: {
                        tOid: data.oid,
                        mainId: $("#mainId").val()
                    },
                    success: function(obj){
                        IntRegAction.intRegGrid.trigger("reloadGrid");
                    }
                })
            }
        });
    },
	_reloadIntRegGrid:function(){	
        this.intRegGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
            postData: {
                tabFormMainId: $("#tabFormMainId").val(),
                mainId: $("#mainId").val()
            }
        }).trigger("reloadGrid");	        
	}
};

/**
 * G-113-0145  授信新做額度於eloan簽報核准後，自動傳送AS400執行3X02，以利央行RDT報表傳送。
 */
var thaiContingentType = {
	controlHideShow: function(){
		//報案分行是否為泰國分行
		//20240925 (009677)黃姿尹要求, 新做額度才要顯示
		var property =$.trim($("#proPerty").val()).split("|");
		if(l140s04aJson.queryCaseBranchCountry() == "TH" && $.inArray("1", property) >= 0){
			//申請案件號碼、授信用途科目別
			$(".onlyForThailand").show();
			//init 案件號碼gridview
		}else{
			$(".onlyForThailand").hide();
			$("#loanAndContType").val("");
			$("#appNo").val("");
		}
	},
	init: function(){
		thaiContingentType.controlHideShow();
		//appNoSelect
		//thaiContingentType.initAppNoList();
		//註冊事件
		thaiContingentType.initUiEvent();
	},
	initAppNoList: function(){
		$.ajax({
            handler: inits.fhandle,
            action: "queryELFAPPNOList",
            data: {
            	ownBrId: $("#L140M01AForm1").find("#ownBrId").val(),
 	            custId: $("#L140M01AForm1").find("#custId").val(),
                dupNo: $("#L140M01AForm1").find("#dupNo").val()
            },
            success: function(obj){
            	//console.log(obj);
            	if(!$.isEmptyObject(obj.appNoData)){
            		$("#appNoSelect").setItems({
                        item: obj.appNoData
                    });
            	}
            }
        });

	},
	initUiEvent: function(){
		//授信用途科目別>登陸按鈕
	    $("#loanAndContTypeBT").click(function(){
	    	//取得選單
	    	var item = API.loadCombos("lms_140_loanAndContType")["lms_140_loanAndContType"];
	    	$("#checkbox_loanAndContType").setItems({
		   		 size: "1",
		   	     item: item,
		   		 clear : true,
		   		 itemType: 'checkbox' 
		   	});
	    	//checkbox
	    	$("[name='checkbox_loanAndContType']").removeAttr("disabled").removeAttr("checked");
	    	$("input:checkbox[name='checkbox_loanAndContType']").prop("disabled", false);
	    	$("input:checkbox[name='checkbox_loanAndContType']").show();
	    	//隱藏值
	    	var loanAndContType = $("#loanAndContType").val();
	    	if (loanAndContType != "") {
	    		var vals = loanAndContType.split("|");
	    		for (var i in vals) {
	    	    	$("[name=checkbox_loanAndContType][value='" + DOMPurify.sanitize(vals[i]) + "']").prop("checked", true);
	    	    }
	    	}
	    	$("#loanAndContTypeThickBox").thickbox({
		   	     //L140M01A.loanAndContType=授信科目用途別
		   	     title: i18n.lms1405s02["L140M01A.loanAndContType"],
		   	     width: 450,
		   	     height: 500,
		   	     modal: true,
		   	     align: "center",
		   	     valign: "bottom",
		   	     readOnly: false,
		   	     i18n: i18n.def,
		   	     buttons: {
		   	         "sure": function(){
		   	             $.thickbox.close();
		   	             var allCheacked = [];
		   	             var allCheackedVal = [];
		   	             $.each($("input[type='checkbox'][name='checkbox_loanAndContType']:checked"), function(i, n){
		   	             	allCheacked[i] = i18n.lms_140_loanAndContType[$(n).val()];  
		   	                allCheackedVal[i] = $(n).val();	                  
		   	             });
		   	
		   	             $("#loanAndContTypeShow").val(allCheacked);
		   	             $("#loanAndContType").val(allCheackedVal.join("|"));
		   	         },
		   	         "cancel": function(){
		   	             $.thickbox.close();
		   	         }
		   	     }
	    	});
	    });
	    //申請案件號碼>登陸按鈕
	    $("#appNoBT").click(function(){
	    	thaiContingentType.initAppNoList();
	    	//視窗
		    $("#appNoThickBox").thickbox({
		    	 //L140M01A.loanAndContType=授信科目用途別
		   	     title: i18n.lms1405s02["L140M01A.loanAndContType"],
		   	     width: 300,
		   	     height: 200,
		   	     modal: true,
		   	     align: "center",
		   	     valign: "bottom",
		   	     readOnly: false,
		   	     i18n: i18n.def,
		   	     buttons: {
		   	         "sure": function(){
		   	        	 //選取清單寫到外面text
		   	        	 var appNo = $.trim($("#appNoSelect").val());
		   	        	 if(appNo != ""){
		   	        		$("#appNo").val(DOMPurify.sanitize(appNo));
		   	        	 }
		   	             $.thickbox.close();      
		   	         },
		   	         "cancel": function(){
		   	             $.thickbox.close();
		   	         }
		   	     }
		    });
	    });
	    
	}
};

initAll.done(function(inits){
    //dfdPanel1.done(gridviewNatural, gridviewCorporate);//連保人
	gridviewNatural();
	gridviewCorporate();
	//J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
    gridViewGuarantorSeq();//連保人
    dfdPanel2.done(gridviewitemType);//授信科目grid
    dfdPanel3.done(gridviewConnomOther, gridviewConnomOtherSelect);//共用額度序號gird
    dfdPanel4.done(gridviewCollect);//收付彙計數
    gSbjProperty = false;
    
	//J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	l140s04aJson.mainId =$("#tabFormMainId").val() ;
	l140s04aJson.loanPolicyExGrid();
	
	if(l140s04aJson.queryCaseBranchCountry() == "US"){
		$("#showLoanPolicyEx").show();
	}else{
		$("#showLoanPolicyEx").hide();
	}
    //====================init data=============================
    //N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
    var isLoginDervBT_N1050127_CanShow = false;
    $.ajax({
        async: false,
        handler: "lms1405m01formhandler",
        data: {
            formAction: "chkLoginDervBTN1050127CanShow"
        },
        success: function(obj){
            if (obj.canShow == "Y") {
                isLoginDervBT_N1050127_CanShow = true;
            }
            
            if (isLoginDervBT_N1050127_CanShow == true) {
                $("#showLoginDervBT_N1050127").show();
                $("#showLoginDervBT").hide();
            }
            else {
                $("#showLoginDervBT_N1050127").hide();
                $("#showLoginDervBT").show();
            }
        }
    });
    
    //====================change event =========================
    
    $("#modifyL140M01Q").click(function(){
        //Src = LMSL140M01MPage.js
        LMS140M01MAction.openChinaLoan($("#tabFormMainId").val(), false);
    });
    
    $("#queryL140M01Q").click(function(){
        //Src = LMSL140M01MPage.js
        LMS140M01MAction.openChinaLoan($("#tabFormMainId").val(), true);
    });

    // J-108-0283 變更條件Condition Change
    $("#queryL140S05A").click(function(){//readonly: inits.toreadOnly
        LMS140M01MAction.openCondChg($("#tabFormMainId").val(), inits.toreadOnly);
    });

    //連保人自動帶入使用者姓名
    $("#rId,#rDupNo").blur(function(){
        var custId = $("#rId").val();
        var dupNo = $("#rDupNo").val();
        if ($.trim(custId).length > 0 && $.trim(dupNo).length > 0) {
            $.ajax({
                handler: "lms1405m01formhandler",
                action: "getMisCustData",
                data: {
                    custId: custId,
                    dupNo: dupNo
                },
                success: function(obj){
                    if (!$.isEmptyObject(obj)) {
                        $("#rName").val(obj.custName);
                    }
                }
            });
        }
    });
    
    $("#snoKind").change(function(){
    
        var snoKind = $(this).val();
        
        //J-104-0284-001  額度明細表檢核供應鏈融資賣放限週轉科目
        if (snoKind.substring(0, 1) == "6" || snoKind.substring(0, 1) == "4") {
            $("#showIsEfin").show();
        }
        else {
            $("#showIsEfin").hide();
        }
        
        if (snoKind.substring(0, 1) == "6") {
            var isArCtrlCanShow = false;
            $.ajax({
                async: false,
                handler: "lms1405m01formhandler",
                data: {
                    formAction: "chkArCtrlCanShow"
                },
                success: function(obj){
                    if (obj.canShow == "Y") {
                        isArCtrlCanShow = true;
                    }
                }
            });
            
            if (isArCtrlCanShow == true) {
                $("#arCtrl").show();
                $("#loanPer").show();
            }
            else {
                $("#arCtrl").hide();
                $("#loanPer").hide();
            }
            
        }
        else {
            $("#arCtrl").hide();
            $("#loanPer").hide();
        }
        
        //J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
        $("input[name='isEfin']").trigger("change");
        
    });
    
    
    
    
    //J-110-0485_05097_B1001 於簽報書新增LGD欄位
	$("input[name='unitCase2']").change(function(){
		var unitCase2 = $("input[name='unitCase2']:radio:checked" ).val(); 
		if(unitCase2 =="Y"){
			$(".showSyndInfo").show();
		}else{
			$(".showSyndInfo").hide();
			$("#syndLoanCurr").val('');
			$("#syndLoanTotal").val('');
			$("#syndLoanPart").val('');
		}  
	}) ;	
	
	//J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	$("input[name='isEfin']").change(function(){
		var isEfin = $("input[name='isEfin']:radio:checked" ).val(); 
		var snoKind = $('#snoKind').val();

		if((snoKind == "61" || snoKind == "62" ) && isEfin =="N"){
			
			$.ajax({
                handler: "lms1405m01formhandler",
                data: {
                    formAction: "chkIsNeedARBuyer",
                    tabFormMainId : $("#tabFormMainId").val(),
                    snoKind: snoKind,
                    isEfin: isEfin
                },
                success: function(obj){
                	 
                    if(obj.showArAccManager == "Y"){
                    	$(".showArAccManager").show();
                    	$("#showArAccPercent").show();
                    	$("#arAccManagerType").trigger("change");
            			//$("#arAccManagerType").val('');
                    }else{
                    	$(".showArAccManager").hide();
                    	$("#showArAccPercent").hide();
                    	$("#arAccManagerType").trigger("change");
                    	
                    	
            			$("#arAccManagerType").val('');
            			$("#arAccManagerId").val('');
            			$("#arAccManagerNm").val('');
            			//J-110-0485_05097_B1001 於簽報書新增LGD欄位
            			$("#arAccPercent").val('');
                    }
                   
                }
            });
		}else{
			$(".showArAccManager").hide();
			$("#showArAccPercent").hide();
			$("#arAccManagerType").trigger("change");
			
			$("#arAccManagerType").val('');
			$("#arAccManagerId").val('');
			$("#arAccManagerNm").val('');
			//J-110-0485_05097_B1001 於簽報書新增LGD欄位
			$("#arAccPercent").val('');
			 
		}  	
		
		
	 
		 
	}) ;
 
	
	
	//G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
    $("input[name='isEsgGreenLoan']").change(function(){
        var value = $("input[name=isEsgGreenLoan]:checked").val();
        if (value == "Y") {
            $("#isEsgGreenLoanSpan").show();
        }else {
        	$("#isEsgGreenLoanSpan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#esgGreenSpendType").val('');
        	$("#isEsgGreenLoanSpan").hide();
        	$("#showEsgGreenSpendTypeA").hide();
        	$("#showEsgGreenSpendTypeZ").hide();
        }
        
    });
    
    //G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
    $("input[name='esgSustainLoan']").change(function(){
        var value = $("input[name=esgSustainLoan]:checked").val();
        if (value == "Y") {
            $(".showEsgSustainLoan").show();
        }else {
        	$(".showEsgSustainLoan").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#esgSustainLoanType").val('');
        	$("#showEsgSustainLoanUnReach").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showEsgSustainLoanUnReach").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$(".showEsgSustainLoan").hide();
        }
        
    });
    
    //G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
    $("input[name='esgSustainLoanUnReach']").change(function(){
        var value = $("input[name=esgSustainLoanUnReach]:checked").val();
        if (value == "Y") {
            $("#showEsgSustainLoanUnReach").show();
        }else {
        	$("#showEsgSustainLoanUnReach").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showEsgSustainLoanUnReach").hide();
        }
        
    });
    
    
    //G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
    $("#esgGreenSpendType").change(function(){
    	
    	var value = $(this).val();
    	var allCheacked = value.split("|");

    	if(jQuery.inArray("A", allCheacked) !== -1){ 
        	$("#showEsgGreenSpendTypeA").show();
        }else{
        	$("#showEsgGreenSpendTypeA").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showEsgGreenSpendTypeA").hide();
        }
        
        if(jQuery.inArray("Z", allCheacked) !== -1){ 
        	$("#showEsgGreenSpendTypeZ").show();
        }else{
        	$("#showEsgGreenSpendTypeZ").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showEsgGreenSpendTypeZ").hide();
        }
    });
    
    //J-113-0329 企金授信新增社會責任授信
    $("input[name='socialLoanFlag']").change(function(){
        var value = $("input[name=socialLoanFlag]:checked").val();
        if (value == "Y") {
            $(".showSocialLoan").show();
        }else {
        	$(".showSocialLoan").find(':input').val('');
        	$("#socialKindShowText").val('');
        	$("#socialTaShowText").val('');
        	$("#socialRespShowText").val('');
        	$(".showSocialLoan").hide();
        }
    });
	
	//J-110-0485_05097_B1001 於簽報書新增LGD欄位
	$("#arAccManagerType").change(function(){
		
		var arAccManagerType = $(this).val();
		if(    (arAccManagerType =="2" || arAccManagerType =="3")){
			$("#showArAccPercent").show();
		}else{
			$("#showArAccPercent").hide();
			$("#arAccPercent").val('');
		}  
	}) ;
    
 
	// J-108-0302-001 是否符合出口實績規範
	$("#flaw_fg").change(function(){
        var value = $(this).val();

        if (value == "2") {
            show_flaw._init(true, $("#flaw_fg").prop("disabled"));
            //$("#show_flaw_amt").show();
        } else {
            show_flaw._init(false, $("#flaw_fg").prop("disabled"));
            //$("#show_flaw_amt").hide();
            //$("#flaw_amt").val('');
        }
    });

    var show_flaw = {
    	_init: function(isShow, disabled){
    		if(isShow){
    		    $("#show_flaw_amt").show();
    		} else {
    		    $("#show_flaw_amt").hide();
                $("#flaw_amt").val('');
            }

            if(disabled){
                $("#flaw_amt").prop("readonly", true);
            } else {
                $("#flaw_amt").prop("readonly", false);
            }
    	}
    };
    
    /**
     * 登錄共同借款人
     */
    $("#commonBorrowerBt").click(function(){
        CommonBorrowerAction.openCommonBorrower();
    });
    
    /**
     *登錄分行代號
     */
    $("#selectBranchBt").click(function(){
        CommonAPI.showAllBranch({
            btnAction: function(a, b){
                $("#newFcltNo_No_bankNum").val(b.brNo);
                $.thickbox.close();
            }
        });
    });
    
    /**
     * 顯示變更前內容
     */
    $("#showBeforeBt").click(function(){
        showBefore();
    });
    
    //現請額度與 擔保授信額度調整幣別同步
    $("#currentApplyCurr").change(function(){
        $("#assureTotECurr").val($(this).val());
    });
    
    //當授信科科目內詳其他續做條件被點選就隱藏天數的select
    $("#lmtOther").click(function(){
        $(this).prop("checked") ? $("#hidelimit").hide() : $("#hidelimit").show();
    });
    
    $("#L140M01CForm").find("button#loginGradeBt").click(function(){
        var grid_id = "gridL140S02C_chooseC121MainIdCustIdDup";
        
        var my_post_data = {
            formAction: "queryC120Rating",
            caseId: responseJSON.mainId,
            tabFormId: $("#tabFormId").val()
        };
        var tb_height = 400;
        if ($("#" + grid_id + ".ui-jqgrid-btable").length > 0) {
            $("#" + grid_id).jqGrid("setGridParam", {
                postData: my_post_data,
                search: true
            }).trigger("reloadGrid");
        }
        else {
        	$.ajax({							
        		type : "POST", handler : 'lms1115formhandler',
        		data : { 
        			formAction : "getVarverFormC121M01A", 
        			'mainId' : responseJSON.mainId
        		},
        		success:function(responseData){
        			var _grid_house_fRating = "";
        			var _grid_notHouse_fRating = "";
        			var _grid_varVer = "";
        			var _grid_cmsLocation_hidden = false;
        			var _grid_notHouse_fRating_hidden = false;
        			var val_L120M01A_ratingFlag = _get_val_L120M01A_ratingFlag();
        			var house_fRating_title = i18n.lmss02arating["label.fRating"];
        			
        			if(val_L120M01A_ratingFlag=="JP"){
        				_grid_varVer = "c121m01b.varVer";
        				_grid_cmsLocation_hidden = false;
        				if(responseData.varVer == "2.0"){
        					_grid_house_fRating = "c121m01b.fRating";
        					_grid_notHouse_fRating = "c121m01f.fRating";
        					house_fRating_title = i18n.lmss02arating["label.house.fRating"];
        				}else{
        					_grid_house_fRating = "c121m01b.fRating";
        					_grid_notHouse_fRating_hidden = true;
        				}
        			}else if(val_L120M01A_ratingFlag=="AU"){
        				_grid_varVer = "c121m01c.varVer";
        				_grid_cmsLocation_hidden = true;
        				if(responseData.varVer == "3.0"){
        					_grid_house_fRating = "c121m01c.fRating";
        					_grid_notHouse_fRating = "c121m01g.fRating";
        					house_fRating_title = i18n.lmss02arating["label.house.fRating"];
        				}else{
        					_grid_house_fRating = "c121m01c.fRating";
        					_grid_notHouse_fRating_hidden = true;
        				}	
        			}else if(val_L120M01A_ratingFlag=="TH"){
        				_grid_varVer = "c121m01d.varVer";
        				_grid_cmsLocation_hidden = false;
        				if(responseData.varVer == "2.0"){
        					_grid_house_fRating = "c121m01d.fRating";
        					_grid_notHouse_fRating = "c121m01h.fRating";
        					house_fRating_title = i18n.lmss02arating["label.house.fRating"];
        				}else{
        					_grid_house_fRating = "c121m01d.fRating";
        					_grid_notHouse_fRating_hidden = true;
        				}
        			}
        			
        			
        			$("#" + grid_id).iGrid({
                        handler: 'lms1205gridhandler',
                        height: (tb_height - 140),
                        postData: my_post_data,
                        sortname: 'ratingKind|ratingDesc|mainId|keyMan|custPos|custId',
                        sortorder: 'asc|asc|asc|desc|asc|asc',
                        colModel: [{
                        		colHeader: ' ', width: 15, name: 'keyMan', sortable: true, 'align': 'center'
                        	}, {
                        		colHeader: i18n.lmss02arating["l120s01a.custid"],
                                name: 'custId', align: "left", width: 90, sortable: true
                            }, {
                            	colHeader: ' ',  name: 'dupNo', align: "dupNo", width: 6, sortable: true
                            }, {
                            	colHeader: i18n.lmss02arating["l120s01a.custname"],
                                name: 'custName', align: "left", width: 140, sortable: true
                            }, {
                            	colHeader: i18n.lmss02arating["label.caseNo"],
                                name: 'ratingDesc', align: "left", width: 180, sortable: true
                            }, {
                            	colHeader: house_fRating_title,
                            	name: _grid_house_fRating, align: "left", width: 50, sortable: false
                            }, {
                            	colHeader: i18n.lmss02arating["label.notHouse.fRating"],
                            	name: _grid_notHouse_fRating, align: "left", width: 50, sortable: false,
                            	hidden : _grid_notHouse_fRating_hidden
                            }, {
                            	colHeader: i18n.lmss02arating["label.varVer"],
                            	name: _grid_varVer, align: "left", width: 50, sortable: false
                            }, {
                            	colHeader: i18n.lmss02arating["label.lnPeriod"],
                            	name: 'lnPeriod', align: "left", width: 80, sortable: false
                            }, { 
                            	colHeader: i18n.lms1015v01["label.cmsLocation"],
                                name: 'cmsLocation', align: "left", width: 200, sortable: false,
                                hidden: _grid_cmsLocation_hidden
                            }, {
                            	colHeader: "oid", name: 'oid', hidden: true
                            }, {
                            	colHeader: "mainId", name: 'mainId', hidden: true
                            }, {
                            	colHeader: "ratingKind", name: 'ratingKind', hidden: true
                            }]
                    });

        		}
        	})


        }
        
        $("#divL140S02C_chooseC121MainIdCustIdDup").thickbox({
            title: '',
            width: 800,
            height: tb_height,
            align: 'center',
            valign: 'bottom',
            modal: false,
            buttons: {
                "sure": function(){
                    var $gridview = $("#" + grid_id);
                    var row = $gridview.getGridParam('selrow');
                    if (row) {
                        var data = $gridview.getRowData(row);
                        $.thickbox.close();
                        
                        $.ajax({
                            handler: "lms1405m01formhandler",
                            data: {
                                formAction: "queryC121RelateMsgByC120M01AOid",
                                c120m01a_oid: data.oid,
                                loanTP: $('#loanTP').val()
                            },
                            success: function(json_queryC121RelateMsgByC120M01AOid){
                                if(json_queryC121RelateMsgByC120M01AOid.repaymentSchFmt=="2"){
                                	// class field_l140m01c_lmtDays_lmtOther 包含原本的2個 DOM Element
                                	// 只呈現 lmtDays
                                	// 先隱藏 [ ]詳其他敘做條件 
                                    $(".field_l140m01c_lmtDays_lmtOther").hide();
                                    $(".field_l140m01c_lmtDays").show();
                                    //當清償期限<>授信期間, 隱藏
                                    $(".field_l140m01c_lnYear_lnMonth").hide();
                                }else if(json_queryC121RelateMsgByC120M01AOid.repaymentSchFmt=="1"){
                                    $(".field_l140m01c_lmtDays_lmtOther").hide();
                                    $(".field_l140m01c_lnYear_lnMonth").show();
                                }
                            	//===============
                            	var setFieldJSON = json_queryC121RelateMsgByC120M01AOid;
                                $("#L140M01CForm").injectData(setFieldJSON);
                            }
                        });
                    }
                    else {
                        API.showMessage(i18n.def.grid_selector);//請選擇資料
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        
    }).end().find("button#cleanGradeBt").click(function(){
        var setFieldJSON = {
            "c121MainId": "",
            "c121RatingId": "",
            "c121CustId": "",
            "c121DupNo": "",
            "grade": "",
            "l140m01c_cls_grade": ""
        }
        $("#L140M01CForm").injectData(setFieldJSON);
    });
    
    //擔保授信額度調整說明
    $("#sayBt").click(function(){
        $("#sayBox").thickbox({
            //L140M01a.say=說明
            title: i18n.lms1405s02['L140M01a.say'],
            width: 800,
            height: 150,
            modal: true,
            readOnly: false,
            i18n: i18n.lms1405s02,
            buttons: API.createJSON([{
                key: i18n.def['close'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    });
    
    //選擇額度序號來源
    $("input[name=newFcltNoRadio]").click(function(){
    	if("new" == $(this).val()){//產生新號(適用於「新做」案件)
    		$("#tb_newFcltNo_Yes").show();
            $("#tb_newFcltNo_NO,#originalInput").hide();
    	}
    	else if ("original" == $(this).val()) {//登錄原案額度序號(適用於舊案續約及條件變更)，其他則隱藏
        	$("#tb_newFcltNo_NO,#originalInput").show();
        	$("#tb_newFcltNo_Yes").hide();
        }
        else {
        	$("#tb_newFcltNo_Yes,#originalInput,#tb_newFcltNo_NO").hide();
        }
    });
    //額度序號給號
    $("[name=contentBrankRadio]").change(function(){
        $("input[name=newFcltNoRadio][value=new]").prop("checked", true);
        switch ($(this).val()) {
            case "Yes":
                $('#tr_contentBrankRadio_NO,#originalInput').hide();
                break;
            case "NO":
            	$('#tr_contentBrankRadio_NO,#originalInput').show();
                break;
        }
    });
    //本案未送保原因如果為4 可以自行輸入筆數
    $("#noInsuReason").change(function(v, k){
        var $inputSpan = $("#noInsuReasonOtherSpan");
        var $input = $("#noInsuReasonOther");
        if (!k) {
            $input.val("");
        }
        
        $("#hideMark").hide();
        switch ($(this).val()) {
            case "4":
                $inputSpan.show();
                $("#hideMark").show();
                $input.attr({
                    "maxlength": "8",
                    "class": "number"
                });
                break;
            case "8":
                $inputSpan.show();
                $input.attr({
                    "maxlength": "900",
                    "maxlengthC": "300",
                    "class": ""
                });
                break;
            default:
                $input.val("");
                $inputSpan.hide();
                break;
        }
        
    });
    
    //當性質選擇不變時取消選取其他選項
    $("[name=cb1][value=7]").click(function(){
        if (this.checked) {
            $("[name=cb1][value!=7]").removeAttr("checked").prop("disabled", true);
        }
        else {
            $("[name=cb1][value!=7]").removeAttr("disabled");
        }
    });
    
    
    /** 關係(切換)*/
    $('#relationshipSelect').change(function(){
        switch ($(this).val()) {
            case "1":
                //other.relation1=請選擇企業關係人(含法人、自然人)
                $("#thename").html(i18n.lms1405s02["other.relation1"]);
                $("#the1").show();
                $("#the2,#the3").hide();
                break;
            case "2":
                //other.relation2=請選擇親屬關係人
                $("#thename").html(i18n.lms1405s02["other.relation2"]);
                $("#the2").show();
                $("#the1,#the3").hide();
                break;
                
            case "3":
                //other.relation3=請選擇其他綜合關係
                $("#relationshipName").html(i18n.lms1405s02["other.relation3"]);
                $("#the1,#the2").hide();
                $("#the3").show();
                break;
            default:
                $("#the1,#the2,#the3").hide();
                break
        }
    });
    
    /** 動用期間(切換)*/
    $("#useDeadline").change(function(v, k){
        var $desp1 = $("#desp1");
        if (!k) {
            $desp1.val("");
        }
        $desp1.removeAttr("readonly");
        $desp1.attr({
            size: "20"
        });
        $desp1.addClass("required");
        $desp1.addClass("number");
        $desp1.datepicker('destroy');
        $desp1.hide();
        $desp1.removeClass("caseReadOnly");
        $("#desp2").hide();
        $(".removeTrue").remove();
        $("#moveDurOtfromSpan").hide();
        
        var $moveDur = $("#moveDurOtFrom,#moveDurOtEnd");
        $moveDur.removeClass("date");
        $moveDur.removeClass("required");
        $moveDur.datepicker('destroy');
        
        switch ($(this).val()) {
            case "0":
                //L140M01as02.006=不再動用	
                $desp1.show().val(i18n.lms1405s02["L140M01as02.006"]).attr({
                    maxlength: "20",
                    readonly: true
                });
                $desp1.addClass("caseReadOnly");
                $("#desp1").removeClass("number");
                break;
            case "1":
                $desp1.hide();
                $("#moveDurOtfromSpan").show();//TODO在save的時候把moveDurOtEnd、moveDurOtFrom 組起來放在moveDurOt
                $moveDur.addClass("required").prop("readonly", inits.toreadOnly);
                $moveDur.addClass("date");
                if (!inits.toreadOnly) {
                    $moveDur.datepicker();
                }
                $desp1.removeClass("required");
                break;
            case "2":
                //L140M01as02.001=自核准日起  L140M01as02.002=個月
                if (userInfo.userLocale == "en") {
                    $desp1.show().after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.001"] + "</span>").after(' ').after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.002"] + "</span>").attr({
                        size: "2",
                        maxlength: "2",
                        readonly: inits.toreadOnly
                    });
                }
                else {
                    $desp1.show().before("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.001"] + "</span>").after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.002"] + "</span>").attr({
                        size: "2",
                        maxlength: "2",
                        readonly: inits.toreadOnly
                    });
                }
                
                break;
            case "3":
                //L140M01as02.003=自簽約日起 L140M01as02.002=個月
                if (userInfo.userLocale == "en") {
                    $desp1.show().after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.003"] + "</span>").after(' ').after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.002"] + "</span>").attr({
                        size: "2",
                        maxlength: "2",
                        readonly: inits.toreadOnly
                    });
                }
                else {
                    $desp1.show().before("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.003"] + "</span>").after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.002"] + "</span>").attr({
                        size: "2",
                        maxlength: "2",
                        readonly: inits.toreadOnly
                    });
                }
                
                break;
            case "4":
                // 	L140M01as02.004=自首次動用日起  L140M01as02.002=個月
                if (userInfo.userLocale == "en") {
                    $desp1.show().after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.004"] + "</span>").after(' ').after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.002"] + "</span>").attr({
                        size: "2",
                        maxlength: "2",
                        readonly: inits.toreadOnly
                    });
                }
                else {
                    $desp1.show().before("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.004"] + "</span>").after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.002"] + "</span>").attr({
                        size: "2",
                        maxlength: "2",
                        readonly: inits.toreadOnly
                    });
                }
                
                break;
            case "5":
                // L140M01as02.005=請描述 
                $("#desp2").show().attr({
                    size: "40",
                    maxlength: "900",
                    maxlengthC: "300"
                }).after("<span class='removeTrue'>" + i18n.lms1405s02["L140M01as02.005"] + "</span>")
                break;
            default:
                $(".removeTrue").remove();
                $desp1.hide();
                break;
        }
    });
    
    /**J-104-0219-001 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
     * 額度性質 切換
     */
    var $unsecureFlagSpan = $("#unsecureFlagSpan");
    $("#sbjProperty").change(function(v, k){
        //		if (CntrNoAPI.isNeedUnsecureFlag() && $(this).val() == "N") {
        //			$unsecureFlagSpan.show();
        //        }
        //        else {
        //			 CntrNoAPI.cleanTrHideInput($unsecureFlagSpan);
        //            
        //        }
        
        //J-105-0250-001  Web e-Loan 新增利害關係人檢核
        CntrNoAPI.chkIsNeedUnsecureFlag();
        
      //J-107-0256_05097_B1001 Web e-Loan企金授信簽報書額度明細表，針對額度性質選列「擔保」者，增加提示不得列擔保科目的擔保品項目
        if(gSbjProperty){
       	 if($(this).val()=="S"){
           	 //alert("TEST SSSSSSSSSSSS");
           	 $("#showSbjPropertyChgMsgBox").thickbox({
                    //title.14=本票
                    title: "",
                    width: 500,
                    height: 150,
                    modal: true,
                    readOnly: false,
                    align: "center",
                    i18n: i18n.def,
                    valign: "bottom",
                    buttons: {            
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        }
        
        gSbjProperty = true;
        
    });
    
    
    $("#snoKind").trigger("change");
    $("input[name='isEfin']" ).trigger("change");
     
    //====================change event End=========================
    
    //====================Button event ============================
    
    /** 新增額度序號 */
    $("#newFcltNo").click(function(){
        $.ajax({
            handler: "lms1405m01formhandler",
            action: "queryCntrNoBy442",
            data: {
                tabFormMainId: $("#tabFormMainId").val(),
                proPerty: $("#proPerty").val(),
                riskArea: $("#riskArea").val()
            },
            success: function(obj){
                /** { 
                 * 	count:筆數
                 * 	cntrNos: 額度序號list
                 * 	msg : 顯示限額控管訊息
                 * 	}*/
                if (obj.count > 0) {
                    //L140M01a.error11=是否要引進預約額度檔之額度序號？
                    CommonAPI.confirmMessage(i18n.lms1405s02["L140M01a.error11"], function(b){
                        if (b) {
                            //新增一個預約額度序號的
                            openCntrNoSelectBox(obj.cntrNos);
                        }
                        else {
                            //                            if (obj.msg && obj.msg != "") {
                            //                                return CommonAPI.showErrorMessage(obj.msg);
                            //                            }
                            //                            else {
                            //                                //當不使用預約額度序號
                            //                                openCntrNoBox();
                            //                            }
                            openCntrNoBox();
                        }
                    });
                }
                else {
                    //					if (obj.msg && obj.msg != "") {
                    //                        return CommonAPI.showErrorMessage(obj.msg);
                    //                    }
                    //                    else {
                    //                        //當沒有預約額度序號
                    //                        openCntrNoBox();
                    //                    }
                    openCntrNoBox();
                }
                
            }
        });
    });
    
    /**登錄 授信科目 */
    $("#itemTypeSelect").click(function(){
        ilog.debug("click #<EMAIL>");
        var selectVal = $("#sbjProperty").val();
        if (selectVal == "") {//當登入了額度性質才可以選擇授信科目
            //L140M01a.error03=請選擇額度性質再登入授信科目
            return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error03"]);
        }
        //L140M01a.guarantee=擔保 L140M01a.noGuarantee=無擔保
        if (selectVal == 'S') {
            $("#itemTypename").html(i18n.lms1405s02["L140M01a.guarantee"])
        }
        else {
            $("#itemTypename").html(i18n.lms1405s02["L140M01a.noGuarantee"]);
        }
        
        //在此js 上面有寫到 dfdPanel2.done(...)        
        dfdPanel2.resolve();
        
        $("#gridviewitemType").jqGrid("setGridParam", {//重新設定grid需要查到的資料
            sortname: 'seqNum|createTime|subjSeq',
            sortorder: 'asc|asc|asc',
            postData: {
                formAction: "queryL140m01c", //lms1405gridhandler
                tabFormMainId: $("#tabFormMainId").val()
            },
            search: true
        }).trigger("reloadGrid");
        $("#itemTypeBox").thickbox({
            //'授信科目',
            title: i18n.lms1405s02["L140M01c.item"],
            width: 680,
            height: 400,
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "newData": function(){
                    newItemTypeSelect('new');
                },
                "del": function(){
                    var ids = $("#gridviewitemType").getGridParam('selarrrow');
                    if (ids == "") {
                        //TMMDeleteError=請先選擇需修改(刪除)之資料列
                        return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
                    }
                    
                    // confirmDelete=是否確定刪除?
                    CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                        if (b) {
                            var gridIDList = [];
                            for (var i in ids) {
                                gridIDList[i] = $("#gridviewitemType").getRowData(ids[i]).oid;
                            }
                            $.ajax({
                            
                                handler: "lms1405m01formhandler",
                                data: {//把資料轉成json
                                    formAction: "deleteL140m01c",
                                    tabFormMainId: $("#tabFormMainId").val(),
                                    oidList: gridIDList
                                },
                                success: function(obj){
                                    $("#gridviewitemType").trigger('reloadGrid');
                                    //J-105-0250-001  Web e-Loan 新增利害關係人檢核
                                    CntrNoAPI.chkIsNeedUnsecureFlag();
                                    
                                    //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
				                    CntrNoAPI.chkIsNoneHedge();

				                    CntrNoAPI.chkIsNeedDerivEval();
				                    
                                }//close success
                            }); //close ajax
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                    $.ajax({
                        handler: "lms1405m01formhandler",
                        action: "getItemShow",
                        data: {//把資料轉成json
                            tabFormId: $("#tabFormId").val()
                        },
                        success: function(obj){
                            $("#gridviewC_2").trigger("reloadGrid");
                            //J-105-0250-001  Web e-Loan 新增利害關係人檢核
                            CntrNoAPI.chkIsNeedUnsecureFlag();
                            
                            //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
		                    CntrNoAPI.chkIsNoneHedge();

		                    CntrNoAPI.chkIsNeedDerivEval();
		                    
                            if (!$.isEmptyObject(obj)) {
                                $("#lnSubject").val(obj.lnSubject);
                                $("#payDeadline").val(obj.payDeadline);
                                if (obj.isRating == "Y") {
                                    $("#l140m01a_cls_grade").val(obj.l140m01a_cls_grade);
                                    $("#guarantor").val(obj.guarantor);
                                    $("#l140m01jStr").val(obj.l140m01jStr);
                                    if (true) {
                                        var $formQualitativeFactor = $("#QualitativeFactorForm");
                                        $formQualitativeFactor.reset();
                                        $formQualitativeFactor.injectData(obj.QualitativeFactorForm);
                                    }
                                    
                                }
                            }
                            
                            //J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
                            // J-108-0302 是否符合出口實績規範
                            if (obj && obj.showExperf) {
                                $("#show_Experf").show();
                            } else {
                                $("#show_Experf").hide();
                                $("#show_flaw_amt").hide();
                            }
                            $("input[name=experf_fg]").prop("checked",false);
                            $("#flaw_fg").val('');
                            $("#flaw_amt").val('');
                            
                        }//close success
                    }); //close ajax
                }
            }
        });
    });
    

    /** 登錄連保人  */
    $("#toglePersonBT").click(function(){
    
        //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
        var rType = "G";
        $(".showGuarantorMemo").show(); //顯示備註頁籤
        var orgGuaPercentFg = $("input[name='guaPercentFg']:radio:checked").val();
        $("[name=guaPercentFgTmp][value=" + orgGuaPercentFg + "]:radio").prop("checked", true);
        
        //J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
        var orgGuaNaExposure = $("input[name='guaNaExposure']:radio:checked").val();
        $("[name=guaNaExposureTmp][value=" + orgGuaNaExposure + "]:radio").prop("checked", true);
        
        //$("#guaPercentFgTmp").val();
 	  	 
        $("#burdenCertainPercentage").thickbox({
            //title.09=登錄連保人
            title: i18n.lms1405s02["title.09"],
            width: 600,
            height: 300,
            modal: true,
            i18n: i18n.def,
            readOnly: false,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var orgGuaPercentFgTmp = $("input[name='guaPercentFgTmp']:radio:checked").val();
                    $("[name=guaPercentFg][value=" + orgGuaPercentFgTmp + "]:radio").prop("checked", true);
                    
                    //J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
                    var orgGuaNaExposureTmp = $("input[name='guaNaExposureTmp']:radio:checked").val();
                    $("[name=guaNaExposure][value=" + orgGuaNaExposureTmp + "]:radio").prop("checked", true);
                    
                    
                    $.thickbox.close();
                    //移除lock狀態
                    $("#guarantorMemo").removeAttr("disabled").removeAttr("readonly");
                    $("#relationshipBox").find(".nodisabled").removeAttr("disabled").removeAttr("readonly");
                    //讓每次開起box都是第一頁
                    $("#toglePersonTabs").tabs({
                        selected: 0
                    });

					//dfdPanel1.resolve();		

                    $("#gridviewNatural").jqGrid("setGridParam", {//重新設定grid需要查到的資料
			            sortname: 'createTime',
			            sortorder: 'asc',
			            postData: {
			                formAction: "queryL140m01i",
			                mainId: $("#tabFormMainId").val(),
			                type: "1",
			                rType: rType
			            },
			            search: true,
						loadComplete: function(){
				        	  $("#gridviewCorporate").jqGrid("setGridParam", {//重新設定grid需要查到的資料
					            sortname: 'createTime',
					            sortorder: 'asc',
					            postData: {
					                formAction: "queryL140m01i",
					                mainId: $("#tabFormMainId").val(),
					                type: "2",
					                rType: rType
					            },
					            search: true,
								loadComplete: function(){
									
									$('#gridviewNatural').jqGrid('setGridParam', {
				                        loadComplete: function(){
				                            //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
				                        }
				                    });
									$('#gridviewCorporate').jqGrid('setGridParam', {
				                        loadComplete: function(){
				                            //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
				                        }
				                    });
						
						        	var btnAction = inits.defButton;
				                    if (!inits.toreadOnly) {
				                        btnAction = API.createJSON([{
				                            //引進連保人
				                            key: i18n.lms1405s02['btn.contentPerson'],
				                            value: function(){
				                                $.ajax({
				                                
				                                    handler: "lms1405m01formhandler",
				                                    data: {//把資料轉成json
				                                        formAction: "queryContentData",
				                                        oid: $("#tabFormId").val()
				                                    },
				                                    success: function(responseData){
				                                        $('#gridviewNatural').trigger('reloadGrid');
				                                        $('#gridviewCorporate').trigger('reloadGrid');
				                                    }
				                                });
				                            }
				                            
				                        }, {
				                            key: i18n.def['newData'],
				                            value: function(){
				                                //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
				                                newToglePersonBT(null, null, null, rType);
				                                
				                            }
				                        }, {
				                        	key: i18n.lms1405s02["btn.setGuarantorCreditPriority"],   //"信用品質順序設定",
				                            value: function(){
				                                //J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定 
				                            	$.ajax({ // 查詢主要借款人資料
				                        			handler : "lms1405m01formhandler",
				                        			type : "POST",
				                        			dataType : "json",
				                        			data : {
				                        				formAction : "chkGuarantorPriorityOn",
				                        				rptMainId:responseJSON.mainId,
				                                        tabFormMainId: $("#tabFormMainId").val()
				                        			},
				                        			success : function(json) {
				                        				if(json.guarantorPriorityOn != "Y"){
				                        					if(json.guarantorPriorityOn == "C"){
					                        					return CommonAPI.showErrorMessage("個人戶借款人無須設定");
					                        				}else{
					                        					return CommonAPI.showErrorMessage("此功能尚未開放使用");
					                        				}	
				                        				}else{
				                        					setGuarantorSeq();
				                        				}					
				                        			}
				                        		});
				                            	
				                            }
				                        }, {
				                            key: i18n.def['del'],
				                            value: function(){
				                            
				                                var id1 = $("#gridviewNatural").getGridParam('selarrrow');
				                                var id2 = $("#gridviewCorporate").getGridParam('selarrrow');
				                                var data1 = [];
				                                var data2 = [];
				                                if (id1.length == 0 && id2.length == 0) {
				                                    //TMMDeleteError=請先選擇需修改(刪除)之資料列
				                                    return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
				                                }
				                                
				                                //confirmDelete=是否確定刪除?
				                                CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
				                                    if (b) {
				                                        if (id1.length > 0) {
				                                            for (var i = 0; i < id1.length; i++) {
				                                                data1[i] = $("#gridviewNatural").getRowData(id1[i]).oid;
				                                            }
				                                        }
				                                        
				                                        if (id2.length > 0) {
				                                            for (var i = 0; i < id2.length; i++) {
				                                                data2[i] = $("#gridviewCorporate").getRowData(id2[i]).oid;
				                                            }
				                                        }
				                                        
				                                        $.ajax({
				                                            handler: "lms1405m01formhandler",
				                                            data: {//把資料轉成json
				                                                formAction: "deleteL140m01i",
				                                                oids: data1.concat(data2),
				                                                tabFormMainId: $("#tabFormMainId").val(),
				                                                showMsg: true
				                                            },
				                                            success: function(responseData){
				                                                $('#gridviewNatural').trigger('reloadGrid');
				                                                $('#gridviewCorporate').trigger('reloadGrid');
				                                            }
				                                        });
				                                    }
				                                });
				                            }
				                        }, {
				                            key: i18n.def['close'],
				                            value: function(){
				                            	if ($("#guarantorMemo").val().countLength() > 900) {
				                                    //val.maxlength=最多輸入{0}個字元.
				                                    //L782M01A.disp1=備註說明
				                                    var showMessage = i18n.lms1405s02['L782M01A.disp1'] + i18n.def["val.maxlength"].replace("{0}", 300);
				                                    return CommonAPI.showErrorMessage(showMessage);
				                                }
				                                $("#gridviewC_2").trigger("reloadGrid").delay( 5000 );
				                                //關閉的時候將連保人寫到畫面上 guarantor
				                                var guaPercentFg = $("input[name='guaPercentFg']:radio:checked").val();
				                                //J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
				                                var guaNaExposure = $("input[name='guaNaExposure']:radio:checked").val();
				                                
				                                var corpId = $("#gridviewCorporate").jqGrid('getDataIDs');
				                                var natId = $("#gridviewNatural").jqGrid('getDataIDs');
				                                var data = "";
				                                var sign;
				                                var guaIndex = 0;
				                                var needIndex = false;
				                                if (natId.length + corpId.length > 1) {
				                                    needIndex = true;
				                                }
				                               
				                                var guarantorType = $('#guarantorType').val();              
				                                if(guarantorType == '3'){ //一般保證人/連帶保證人  組字
				                                	
				                                	var JointData = "";
				                                	var OrdinaryData = "";
				                                	var NotSetData = "";
				                                	var JointSign;
				                                	var OrdinarySign;
				                                	var NotSetDataSign;
				                                	for (var i = 0; i < natId.length; i++) {
				                                		(JointData.length > 0) ? JointSign = "、" : JointSign = "";
				                                		(OrdinaryData.length > 0) ? OrdinarySign = "、" : OrdinarySign = "";
				                                		(NotSetData.length > 0) ? NotSetDataSign = "、" : NotSetDataSign = "";
				                                		var guaPercentStr = +(guaPercentFg == "Y") ? "(" + $("#gridviewNatural").getRowData(natId[i]).guaPercentStr + "%)" : "";
				                                		if($("#gridviewNatural").getRowData(natId[i]).guarantorTypeItem == '1'){ //連帶保證人
				                                			JointData = JointData + JointSign + $("#gridviewNatural").getRowData(natId[i]).rName + guaPercentStr;
				                                		}else if($("#gridviewNatural").getRowData(natId[i]).guarantorTypeItem == '2'){ //一般保證人
				                                			OrdinaryData = OrdinaryData + OrdinarySign + $("#gridviewNatural").getRowData(natId[i]).rName + guaPercentStr;
				                                		}else{ //未設定
				                                			NotSetData = NotSetData + NotSetDataSign + $("#gridviewNatural").getRowData(natId[i]).rName + guaPercentStr;
				                                		}
				                                	}
				                                	
				                                	for (var i = 0; i < corpId.length; i++) {
				                                		(JointData.length > 0) ? JointSign = "、" : JointSign = "";
				                                		(OrdinaryData.length > 0) ? OrdinarySign = "、" : OrdinarySign = "";
				                                		(NotSetData.length > 0) ? NotSetDataSign = "、" : NotSetDataSign = "";
				                                		var guaPercentStr = +(guaPercentFg == "Y") ? "(" + $("#gridviewCorporate").getRowData(corpId[i]).guaPercentStr + "%)" : "";
				                                		if($("#gridviewCorporate").getRowData(corpId[i]).guarantorTypeItem == '1'){ //連帶保證人
				                                			JointData = JointData + JointSign + $("#gridviewCorporate").getRowData(corpId[i]).rName + guaPercentStr;
				                                		}else if($("#gridviewCorporate").getRowData(corpId[i]).guarantorTypeItem == '2'){ //一般保證人
				                                			OrdinaryData = OrdinaryData + OrdinarySign + $("#gridviewCorporate").getRowData(corpId[i]).rName + guaPercentStr;
				                                		}else{ //未設定
				                                			NotSetData = NotSetData + NotSetDataSign + $("#gridviewCorporate").getRowData(corpId[i]).rName + guaPercentStr;
				                                		}
				                                		//console.log(JointData);
				                                	}
				                                	
				                                	if(JointData.length > 0){
				                                		data = "1." + $("#guarantorType option:[value=1]").text()+"：" + JointData + ";";
				                                	}
				                                	
				                                	if(OrdinaryData.length > 0){
				                                		data = data + "\n" + "2." + $("#guarantorType option:[value=2]").text()+"：" + OrdinaryData + ";";
				                                	}
				                                	
				                                	if(NotSetData.length > 0){
				                                		data = data + "\n" + "3." + NotSetData+ ";";
				                                	}
				                                	
				                                }else{
				                                	
				                                	for (var i = 0; i < natId.length; i++) {
					                                    (data.length > 0) ? sign = "、" : sign = "";
					                                    guaIndex = guaIndex + 1;
					                                    //J-105-0100-001 Web e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
					                                    var guaPercentStr = +(guaPercentFg == "Y") ? "(" + $("#gridviewNatural").getRowData(natId[i]).guaPercentStr + "%)" : "";
					                                    if (needIndex == true) {
					                                        data = data + sign + guaIndex + "." + $("#gridviewNatural").getRowData(natId[i]).rName + guaPercentStr;
					                                    }
					                                    else {
					                                        data = data + sign + $("#gridviewNatural").getRowData(natId[i]).rName + guaPercentStr;
					                                    }
					                                }
					                                for (var i = 0; i < corpId.length; i++) {
					                                    (data.length > 0) ? sign = "、" : sign = "";
					                                    guaIndex = guaIndex + 1;
					                                    var guaPercentStr = +(guaPercentFg == "Y") ? "(" + $("#gridviewCorporate").getRowData(corpId[i]).guaPercentStr + "%)" : "";
					                                    
					                                    if (needIndex == true) {
					                                        data = data + sign + guaIndex + "." + $("#gridviewCorporate").getRowData(corpId[i]).rName + guaPercentStr;
					                                    }
					                                    else {
					                                        data = data + sign + $("#gridviewCorporate").getRowData(corpId[i]).rName + guaPercentStr;
					                                    }
					                                }
				                                }
				                                
				                                
				                                if ($("#guarantorMemo").val() != "") {
				                                    //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
				                                	data = data + "\n";
				                                	if(guarantorType != '3'){
				                                		data = data + (data.length > 0 ? ";" : "");
				                                	}
				                                    data = data + $("#guarantorMemo").val();
				                                }
				                                
				                                if (!data || $.trim(data) == "") {
				                                    data = i18n.lms1405s02['nohave']
				                                }
				                               
				                                $.ajax({
				                                    handler: "lms1405m01formhandler",
				                                    action: "saveL140m01aGuarantorStr",
				                                    data: {
				                                        tabFormMainId: $("#tabFormMainId").val(),
				                                        guarantor: data,
				                                        rType: rType
				                                    },
				                                    success: function(obj){
				                                    	
				                                        $("#guarantor").val(data);
				                                        $.thickbox.close();
				                                    }
				                                });
				                            }
				                        }]);
				                    }
				                    $("#toglePersonBox").thickbox({ // 使用選取的內容進行彈窗
				                        title: i18n.lms1405s02["title.09"],//'登錄連保人',
				                        width: 700,
				                        height: 410,
				                        readOnly: false,
				                        modal: true,
				                        buttons: btnAction
				                    });
									 	  	
						        }       
					        }).trigger("reloadGrid");     	  	
				        }       
			        }).trigger("reloadGrid");
	
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        
        
    });
    
    //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
    /** 登錄物上保證人  */
    $("#toglePersonBT1").click(function(){
        var rType = "S";
        $(".showGuarantorMemo").hide(); //顯示備註頁籤
        $("#showGuaPercent").hide();
        
        //$("#guarantorMemo").removeAttr("disabled").removeAttr("readonly");
        $("#relationshipBox").find(".nodisabled").removeAttr("disabled").removeAttr("readonly");
        //讓每次開起box都是第一頁
        $("#toglePersonTabs").tabs({
            selected: 0
        });
		
		//dfdPanel1.resolve();
		
        $("#gridviewNatural").jqGrid("setGridParam", {//重新設定grid需要查到的資料
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01i",
                mainId: $("#tabFormMainId").val(),
                type: "1",
                rType: rType
            },
            search: true,
			loadComplete: function(){
	        	   
				$("#gridviewCorporate").jqGrid("setGridParam", {//重新設定grid需要查到的資料
		            sortname: 'createTime',
		            sortorder: 'asc',
		            postData: {
		                formAction: "queryL140m01i",
		                mainId: $("#tabFormMainId").val(),
		                type: "2",
		                rType: rType
		            },
		            search: true,
					loadComplete: function(){
			        	var btnAction = inits.defButton;
		
		                $('#gridviewNatural').jqGrid('setGridParam', {
	                        loadComplete: function(){
	                            //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
	                        }
	                    });
						$('#gridviewCorporate').jqGrid('setGridParam', {
	                        loadComplete: function(){
	                            //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
	                        }
	                    });
						
				        if (!inits.toreadOnly) {
				            btnAction = API.createJSON([{
				                key: i18n.def['newData'],
				                value: function(){
				                    //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
				                    newToglePersonBT(null, null, null, rType);
				                }
				            }, {
				                key: i18n.def['del'],
				                value: function(){
				                
				                    var id1 = $("#gridviewNatural").getGridParam('selarrrow');
				                    var id2 = $("#gridviewCorporate").getGridParam('selarrrow');
				                    var data1 = [];
				                    var data2 = [];
				                    if (id1.length == 0 && id2.length == 0) {
				                        //TMMDeleteError=請先選擇需修改(刪除)之資料列
				                        return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
				                    }
				                    
				                    //confirmDelete=是否確定刪除?
				                    CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
				                        if (b) {
				                            if (id1.length > 0) {
				                                for (var i = 0; i < id1.length; i++) {
				                                    data1[i] = $("#gridviewNatural").getRowData(id1[i]).oid;
				                                }
				                            }
				                            
				                            if (id2.length > 0) {
				                                for (var i = 0; i < id2.length; i++) {
				                                    data2[i] = $("#gridviewCorporate").getRowData(id2[i]).oid;
				                                }
				                            }
				                            
				                            $.ajax({
				                                handler: inits.fhandle,
				                                data: {//把資料轉成json
				                                    formAction: "deleteL140m01i",
				                                    oids: data1.concat(data2),
				                                    tabFormMainId: $("#tabFormMainId").val(),
				                                    showMsg: true
				                                },
				                                success: function(responseData){
				                                    $('#gridviewNatural').trigger('reloadGrid');
				                                    $('#gridviewCorporate').trigger('reloadGrid');
				                                }
				                            });
				                        }
				                    });
				                }
				            }, {
				                key: i18n.def['close'],
				                value: function(){
				                    
				                    $("#gridviewC_2").trigger("reloadGrid");
				                    //關閉的時候將連保人寫到畫面上 guarantor
				                    var guaPercentFg = "N";
				                    var corpId = $("#gridviewCorporate").jqGrid('getDataIDs');
				                    var natId = $("#gridviewNatural").jqGrid('getDataIDs');
				                    var data = "";
				                    var sign;
				                    var guaIndex = 0;
				                    var needIndex = false;
				                    if (natId.length + corpId.length > 1) {
				                        needIndex = true;
				                    }
				                    
				                    for (var i = 0; i < natId.length; i++) {
				                        (data.length > 0) ? sign = "、" : sign = "";
				                        guaIndex = guaIndex + 1;
				                        
				                        //J-105-0100-001 Web e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
				                        //var guaPercentStr = +(guaPercentFg == "Y") ? "(" + $("#gridviewNatural").getRowData(natId[i]).guaPercentStr + "%)" : "";
				                        
				                        if (needIndex == true) {
				                            data = data + sign + guaIndex + "." + $("#gridviewNatural").getRowData(natId[i]).rName ;//+ guaPercentStr;
				                        }
				                        else {
				                            data = data + sign + $("#gridviewNatural").getRowData(natId[i]).rName ;//+ guaPercentStr;
				                        }
				                    }
				                    for (var i = 0; i < corpId.length; i++) {
				                        (data.length > 0) ? sign = "、" : sign = "";
				                        guaIndex = guaIndex + 1;
				                        //var guaPercentStr = +(guaPercentFg == "Y") ? "(" + $("#gridviewCorporate").getRowData(corpId[i]).guaPercentStr + "%)" : "";
				                        
				                        if (needIndex == true) {
				                            data = data + sign + guaIndex + "." + $("#gridviewCorporate").getRowData(corpId[i]).rName ;//+ guaPercentStr;
				                        }
				                        else {
				                            data = data + sign + $("#gridviewCorporate").getRowData(corpId[i]).rName ;//+ guaPercentStr;
				                        }
				                    }
				                    
				                    
				                    //                    if ($("#guarantorMemo").val() != "") {
				                    //                        data = data + (data.length > 0 ? "，" : "") + $("#guarantorMemo").val();
				                    //                    }
				                    
				                    
				                    if (!data || $.trim(data) == "") {
				                        data = i18n.lms1405s02['nohave']
				                    }
				                    
				                    $.ajax({
				                        handler: inits.fhandle,
				                        action: "saveL140m01aGuarantorStr",
				                        data: {
				                            tabFormMainId: $("#tabFormMainId").val(),
				                            guarantor: data,
				                            rType: rType
				                        },
				                        success: function(obj){
				                            $("#guarantor1").val(data);
				                            $.thickbox.close();
				                        }
				                    });
				                }
				            }]);
				        }
						$("#toglePersonBox").thickbox({ // 使用選取的內容進行彈窗
				            title: i18n.lms1405s02["title.22"],//'title.22=登錄物上保證人',
				            width: 700,
				            height: 410,
				            readOnly: false,
				            modal: true,
				            buttons: btnAction
				        });
							     	  	
			        }       
		        }).trigger("reloadGrid"); 	  	
	        }       
        }).trigger("reloadGrid");
       
    });
    
//    /** 登錄連保人  */
//    $("#toglePersonBT").click(function(){
//    
//        //
//        // CommonAPI.confirmMessage(i18n.lms1405s02["L140M01a.error11"], function(b){
//    
//    
//    
//    });
    
    /** 登錄關係 */
    $("#relationshipPeopleBT").click(function(){
        var todo = function(data){
            $('#showName').html(data.context);
            $('#rKindM').val(data.rKindM);
            $('#rKindD').val(data.rKindD);
        };
        relationshipBT(todo);
    });
    
    
    /**  登錄性質  */
    $("#loginTypeBT").click(function(){
        //初始化
        $("[name=cb1]").removeAttr("disabled").removeAttr("checked");
        var proPerty = $("#proPerty").val();
        if (proPerty != "") {
            if (proPerty == "7") {
                $("[name=cb1][value!=7]").removeAttr("checked").prop("disabled", true);
                $("[name=cb1][value=7]").prop("checked", true);
            }
            else {
                var vals = proPerty.split("|");
                for (var i in vals) {
                    $("[name=cb1][value=" + vals[i] + "]").prop("checked", true);
                }
            }
            
        }
        
        $("#loginType").thickbox({
            //L140M01a.select=選取關鍵字
            title: i18n.lms1405s02["L140M01a.select"],
            width: 350,
            height: 260,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    var allCheacked = [];
                    var allCheackedVal = [];
                    var hasCondChg = false;	// J-108-0283 變更條件Condition Change
                    $.each($("#loginType :checkbox[name=cb1]:checked"), function(i, n){
                        allCheacked[i] = i18n.lms1405s02["L140M01a.type" + $(n).val()];
                        allCheackedVal[i] = $(n).val();
                        // J-108-0283 變更條件Condition Change
                        if($(n).val() == "3"){
                            hasCondChg = true;
                        }
                    });
                    $("#proPertyshow").val(allCheacked);
                    $("#proPerty").val(allCheackedVal.join("|"));

                    // J-108-0283 變更條件Condition Change
                    if(hasCondChg){
                        $("#queryL140S05A").show();
                    } else {
                        $("#queryL140S05A").hide();
                    }
                    //G-113-0145 是否顯示泰國相關欄位
                    thaiContingentType.controlHideShow();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    
    /**  登錄衍生性金融商品期數  */
    $("#loginDervBT").click(function(){
        //初始化
        $("[name=dp1]").removeAttr("disabled").removeAttr("checked");
        $('input:checkbox[name="dp1"]').prop('disabled', true);
        $('#loginDervPeriod').find("tr[id^=trDerv]").hide();
        
        var choiceDervPeriod = "";
        var item = API.loadCombos("lms1405s02_AllDervPeriod")["lms1405s02_AllDervPeriod"];
        $("#dp1").setItems({
            size: "1",
            item: item,
            clear: true,
            itemType: 'checkbox'
        })
        
        $("[name=dp1]").removeAttr("disabled").removeAttr("checked");
        $('input:checkbox[name="dp1"]').prop('disabled', true);
        
        $.ajax({
            async: false,
            handler: "lms1405m01formhandler",
            data: {
                formAction: "getCanChoiceDervPeriod",
                tabFormMainId: $("#tabFormMainId").val(),
                cntrNo: $("#cntrNo").val()
            },
            success: function(obj){
                choiceDervPeriod = obj.choiceDervPeriod;
            }
        });
        
        if (choiceDervPeriod != "") {
            $('input:checkbox[name="dp1"]').parent("label").hide();
            var valDp = choiceDervPeriod.split("|");
            for (var d in valDp) {
                $('input:checkbox[name="dp1"][value=' + valDp[d] + ']').prop('disabled', false);
                $('input:checkbox[name="dp1"][value=' + valDp[d] + ']').parent("label").show();
            }
        }
        else {
            $('input:checkbox[name="dp1"]').prop('disabled', false);
            $('input:checkbox[name="dp1"]').show();
        }
        
        var derivatives = $("#derivatives").val();
        if (derivatives != "") {
            var vals = derivatives.split("|");
            var valDp = "";
            if (choiceDervPeriod != "") {
                valDp = choiceDervPeriod.split("|");
            }
            for (var i in vals) {
                if (choiceDervPeriod != "") {
                    for (var d in valDp) {
                        if (vals[i] == valDp[d]) {
                            $("[name=dp1][value=" + vals[i] + "]").prop("checked", true);
                        }
                    }
                }
                else {
                    $("[name=dp1][value=" + vals[i] + "]").prop("checked", true);
                }
                
            }
            
        }
        
        $("#loginDervPeriod").thickbox({
            //L140M01a.select=選取關鍵字
            title: i18n.lms1405s02["L140M01a.select"],
            width: 350,
            height: 300,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    var allCheacked = [];
                    var allCheackedVal = [];
                    $.each($("#loginDervPeriod :checkbox[name=dp1]:checked"), function(i, n){
                        allCheacked[i] = i18n.dervPeriodCodeType[$(n).val()]; //"L140M01a.DERVPERIOD" + 
                        allCheackedVal[i] = $(n).val();
                    });
                    $("#derivativeShow").val(allCheacked);
                    $("#derivatives").val(allCheackedVal.join("|"));
                    
                    $.ajax({
                        async: false,
                        handler: "lms1405m01formhandler",
                        data: {
                            formAction: "clearDerivativesNumDscr",
                            tabFormMainId: $("#tabFormMainId").val(),
                            cntrNo: $("#cntrNo").val()
                        },
                        success: function(obj){
                            //清空信用轉換係數、風險係數
                            $("#derivativesNumDscr").val("");
                            
                            //N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
                            $("#derivativeVersion").val("");
                        }
                    });
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    //N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
    /**  登錄衍生性金融商品期數新  */
    $("#loginDervBT_N1050127").click(function(){
        //初始化
        $.ajax({
            async: false,
            handler: "lms1405m01formhandler",
            data: {
                formAction: "getDervPeriodWithSubject",
                tabFormMainId: $("#tabFormMainId").val(),
                cntrNo: $("#cntrNo").val()
            },
            success: function(obj){
                var $select = $("#maxDervPeriod");
                $("#maxDervNum").val('');
                $select.empty();
                var tempStr = "";
                if (obj.aLoanDervPeriod) {
                    for (var i in obj.aLoanDervPeriod) {
                    
                        var tmp = $("<option></option>");
                        tmp.attr("value", obj.aLoanDervPeriod[i].key);
                        
                        tmp.text(obj.aLoanDervPeriod[i].val);
                        $select.append(tmp);
                    }
                    
                }
                
                $("#loginDervPeriod_N1050127").thickbox({
                    //L140M01a.select=選取關鍵字
                    title: i18n.lms1405s02["L140M01a.select"],
                    width: 520,
                    height: 200,
                    modal: true,
                    align: "center",
                    valign: "bottom",
                    readOnly: false,
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            var $form = $("#loginDervPeriodForm");
                            if (!$form.valid()) {
                                return false
                            }
                            
                            if (!$("#maxDervPeriod").val()) {
                                //衍生性金融商品最長期數
                                return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.maxDervPeriod"] + i18n.def['val.required']);
                            }
                            
                            if (!$("#maxDervNum").val() || $("#maxDervNum").val() <= 0 || $("#maxDervNum").val() > 100) {
                                //最大未來潛在暴險額計算權數必須大於0且小於100%
                                return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.message180"]);
                            }
                            
                            var newDervNum = parseFloat($("#maxDervNum").val());
                            
                            $("#derivativeShow").val($("#maxDervPeriod").find("option:selected").text());
                            $("#derivatives").val($("#maxDervPeriod").val());
                            $("#derivativesNum").val(newDervNum);
                            $("#derivativesNumDscr").val(newDervNum);
                            $("#derivativeVersion").val("N1050127");
                            $.thickbox.close();
                            
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
                
                
                
            }
            
            
            
        });
        
        
    });
    
    
    //N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
    /**  取得高風險組計算權數  */
    $("#applyMaxDervNumBt").click(function(){
        if (!$("#maxDervPeriod").val()) {
            //衍生性金融商品最長期數
            return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.maxDervPeriod"] + i18n.def['val.required']);
        }
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "applyMaxDervNum",
                tabFormMainId: $("#tabFormMainId").val(),
                cntrNo: $("#cntrNo").val(),
                maxDervPeriod: $("#maxDervPeriod").val()
            },
            success: function(obj){
                $("#maxDervNum").val(obj.maxDervNum);
            }//close success
        }); //close ajax
    });
    
    
    /**  登錄共用額度序號  */
    $("#commonNumBt").click(function(){
        dfdPanel3.resolve();
        $("input[name=commonNumRadio][value=commonNow]").prop("checked", true);
        $("#commonNumBox").thickbox({
            title: i18n.lms1405s02["L140M01a.cntrNoCom"],//'額度共用序號',
            width: 550,
            height: 250,
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var checked = $("input[name=commonNumRadio]:checked").val();
                    switch (checked) {
                        case "commonNow":
                            $.thickbox.close();
                            var commonNumSelect = function(data){
                                $("#commSno").val(data.cntrNo);
                            };//本案共用額度序號的新增
                            commonNumOtherSelect($("#mainId").val(), 'queryL140m01aCntrNo', commonNumSelect);
                            break;
                        case "commonOther":
                            $.thickbox.close();
                            commonNumOther();
                            break;
                        case "clean":
                            $.thickbox.close();
                            $("#commSno").val("");
                            break;
                        default:
                            return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error07"]);
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    /** 本票詞庫  */
    $("#localPageBT").click(function(){
        $("#localPageSelect").removeAttr("disabled");
        $("#localPageBox").thickbox({
            //title.14=本票
            title: i18n.lms1405s02["title.14"],
            width: 500,
            height: 150,
            modal: true,
            readOnly: false,
            align: "center",
            i18n: i18n.def,
            valign: "bottom",
            buttons: {
                "sure": function(){
                
                    var seletWord = $("#localPageSelect :selected").text();
                    $("#checkNote").val(seletWord.slice(3));
                    $.thickbox.close();
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    
    /** 分行逾放比  */
    $("#getNPL").click(function(){
        $.ajax({
            handler: "lms1405m01formhandler",
            data: {
                formAction: "queryNPL",
                tabFormMainId: $("#tabFormMainId").val(),
                cntrNo: $("#cntrNo").val()
            },
            success: function(obj){
                $("#npl").val(obj.msg);
                $("#npldate").val(obj.date);
            }//close success
        }); //close ajax
    });
    
    
    /**
     * 收付彙計數 登錄
     */
    $("#collectBT").click(function(){
        if (dfdPanel4.isResolved()) {
            $("#gridviewCollect").jqGrid("setGridParam", {//重新設定grid需要查到的資料
                postData: {
                    tabFormMainId: $("#tabFormMainId").val()
                },
                search: true
            }).trigger("reloadGrid");
        }
        else {
            dfdPanel4.resolve();
        }
        
        CollectAction.openCollectBox();
    });
    
    
    //引進平均動用率(資簡)
    $("#inculeUsePar").click(function(){
        UseParAction.inculeUsePar();
    });
    
    //引進平均動用率(帳務)
    $("#inculeUseParAloan").click(function(){
        UseParAction.inculeUseParAloan();
    });
    
    /**
     * G-104-0286 加強銀行法72-2條之相關控管
     * 引進帳務資訊72-2
     */
    $("#applyOnLine772").click(function(){
        //初始化    		   
        $.ajax({
            async: false,
            handler: "lms1405m01formhandler",
            data: {
                formAction: "queryOnLine772Flag",
                tabFormMainId: $("#tabFormMainId").val(),
                cntrNo: $("#cntrNo").val(),
                custId: $("#L140M01AForm1").find("#custId").val(),
                dupNo: $("#L140M01AForm1").find("#dupNo").val()
            },
            success: function(obj){
            	if (obj.isOldCase && obj.isOldCase == "Y") {
                	$("#is722QDate").val(obj.is722QDate);
                }
                else {
                	if (obj.is722OnFlag == "") {
                        $("[name='is722OnFlag'][value='" + $("[name='is722OnFlag']:radio:checked").val() + "']:radio").prop("checked", false);
                    }
                    else {
                        $("[name='is722OnFlag'][value='" + obj.is722OnFlag + "']:radio").prop("checked", true);
                    }
                    
                    $("#is722CntrNo").val(obj.is722CntrNo);
                    $("#is722QDate").val(obj.is722QDate);
                    
                    if (obj.cntrNoChkExistFlag == "") {
                        $("[name='cntrNoChkExistFlag'][value='" + $("[name='cntrNoChkExistFlag']:radio:checked").val() + "']:radio").prop("checked", false);
                    }
                    else {
                        $("[name='cntrNoChkExistFlag'][value='" + obj.cntrNoChkExistFlag + "']:radio").prop("checked", true);
                    }
                    
                    
                    $("#cntrNoChkExistDate").val(obj.cntrNoChkExistDate);
                    if (obj.isBuyOn == "") {
                        $("input[name='isBuyOn']").prop("checked", false).triggerHandler("change");
                    }
                    else {
                        $("input[name='isBuyOn'][value='" + obj.isBuyOn + "']").prop("checked", true).triggerHandler("change");
                    }
                    /**
                    $("input[name='exItemOn']").attr("checked", false);
                    if (obj.exItemOn) {
                        $.each(obj.exItemOn, function(i, v){
                            $("input[name='exItemOn'][value='" + v + "']").attr("checked", true);
                        })
                    }
                    */
                }
            	RealEstateAction.beforeGrid.trigger("reloadGrid")
            }
        });
        
    });
    
    // 改成change 那originalEvent都會觸發到
    $("input[name='isBuy']").click(function(e){
		var value = $(this).val();
		if(value == "N"){
		    //isInstalment 不清，等到按save才清
//			$("input[name='isInstalment']").attr("checked", false);
			$("input[name='is722Flag'][value='N']").prop("checked", true);
			// 利用trigger方式進來的就不執行
			if(e.originalEvent){
//				$.ajax({
//					handler: "lms1405m01formhandler",
//					action:"deleteCurrentL140m01ts",
//		            data: {
//		                tabFormMainId: $("#tabFormMainId").val()
//		            },
//		            success: function(obj){
//
//					}
//				});
			}
			
		} else {
			$("input[name='is722Flag']").prop("checked", false);
			$("input[name='is722Flag'][value='Y']").prop("checked", true);
			if(e.originalEvent){
				RealEstateAction._reloadAfterGrid();
			}			
		}
		 adjust722UI();
    });
    $("input[name='isBuyOn']").change(function(){
    
        var value = $("input[name='isBuyOn']:checked").val();
        if (value == "Y" || value == "A") {
            $("#722ondiv1").show();
        }
        else {
            $("#722ondiv1").hide();
        }
    });
    
    $("input[name='isInstalment']").change(function(){
		adjust722UI();
	});
    
	$(".isInstalmentClass,input[name='isInstalment']").mouseup(function(event){
		var isDisab = $("input[name='isInstalment']").prop("disabled");
		if(isDisab){
			return ;
		}
		// onmouseup可以取得Before的值
		var checkedCount = $("input[name='isInstalment']:checked").size();
		var gridCount = RealEstateAction.afterGrid.getGridParam("reccount")
		oldValue = $("input[name='isInstalment']:checked").val();
        var $this = $(this);
        var type = $this.attr("type");
        if (type == "radio") {
			//取消事件傳導，不然如果點擊到radio會執行2次
            event.stopPropagation();
        } else {
            $this = $this.children();
        }
                
        if (!$this.is(":checked") && checkedCount && gridCount) {
			//L140M01a.message210=異動此欄位將會刪除「不動產暨72-2相關資訊註記」，是否繼續？
            CommonAPI.confirmMessage(i18n.lms1405s02['L140M01a.message210'], function(r){
                if (!r) {
                    $("input[name='isInstalment'][value=" + oldValue + "]").prop("checked", true)
                } else {
                    $.ajax({
                        handler: "lms1405m01formhandler",
                        action: "deleteCurrentL140m01ts",
                        data: {
                            tabFormMainId: $("#tabFormMainId").val()
                        },
                        success: function(obj){
                            RealEstateAction.afterGrid.trigger("reloadGrid");
                        }
                    });                    
                }
            });
        }        
	});
    $("input[name='exItem']").click(function(){
        adjust722UI();
    });
    
    $("#cancelExItem").click(function(){
        $("input[name='exItem']").prop("checked", false);
        adjust722UI();
    });
    function adjust722UI(){
    
		var isBuyFlag = $("input[name='isBuy']:checked").val();
		if(isBuyFlag == "Y"){
			$("#isInstalmentView").show();
		} else {
			$("#isInstalmentView").hide();
		}
		var isInstalment = $("input[name='isInstalment']:checked").val();
		$("#realEstateAfterGridView")[isBuyFlag == "Y" ? "show":"hide"]();
//		if(isBuyFlag == "Y"){
//			 $("#722div1").show();
//		} else {
//			  $("#722div1").hide();
//			  $("input[name='exItem']").attr("checked", false);
//			  $("input[name='is722Flag'][value='N']").attr("checked", true);
//		}
//		if(isBuyFlag == "Y"){
//			var exItem = $("input[name='exItem']:checked").val();
//			if(exItem){
//				$("input[name='is722Flag'][value='N']").attr("checked", true);
//			} else {
//				$("input[name='is722Flag'][value='Y']").attr("checked", true);
//			}
//		}
        
        
        
        
    }
    
    
    //J-110-0485_05097_B1001 於簽報書新增LGD欄位
    $("#queryDwLnf273ByCntrNo").click(function(){
       //初始化    		   
	   $.ajax({
            async: false,
            handler: "lms1405m01formhandler",
            data: {
                formAction: "queryDwLnf273ByCntrNo",
                tabFormMainId: $("#tabFormMainId").val(),
                cntrNo: $("#cntrNo").val(),
				custId: $("#L140M01AForm1").find("#custId").val(),
				dupNo: $("#L140M01AForm1").find("#dupNo").val()
            },
            success: function(obj){
            	if(obj.syndLoanCurr == ""){
            		return CommonAPI.showMessage(i18n.def["noData"]);
            	}else{
            		$("#L140M01AForm1").find("#syndLoanCurr").val(obj.syndLoanCurr);		   	
                	$("#L140M01AForm1").find("#syndLoanTotal").val(obj.syndLoanTotal);
                	$("#L140M01AForm1").find("#syndLoanPart").val(obj.syndLoanPart);
            	}
            }
        });

    });
    
    
    //產品種類  如為青創，需顯示申請日期
    $("#lnType").change(function(){
        if ($(this).val() == '61') {
            $('#headItem1Tr_4').show();
        }
        else {
            $('#headItem1Tr_4').hide();
            $("#applyDate").val("");
        }   
        
        
        //J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
        if ($(this).val() == '33' || $(this).val() == '34') {
            $('#headItem1Tr_6').show();
        }
        else {
            $('#headItem1Tr_6').hide();
            $("#adcCaseNo").val("");
        }
        
    }).trigger("change");
    
    
    //J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表 
    //J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
    $("#projClass").change(function(){
        //J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
        if ($(this).val() == '05') {
            $('#headItem1Tr_5').show();
        } else {
            $('#headItem1Tr_5').hide();
            $("#itwCode").val("");
        }
        
    }).trigger("change");
    
    //J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
    $("input[name='isCoreBuss']").change(function(){
        var isCoreBuss = $("input[name='isCoreBuss']:radio:checked").val();
    	
        if (isCoreBuss == 'N') {
        	$('#headItem1Tr_7').show();
        	$('#belongItwCodeCoreBuss').val('');
		    $('#belongItwCodeCoreBussShow').val('');
		    $('#belongItwCodeCoreBussShow').hide();
        } else {
            $('#headItem1Tr_7').hide();
            $("#itwCodeCoreBuss").val("");
            $('#belongItwCodeCoreBussShow').show();
        }
        
    }).trigger("change");
    
    
    //J-103-0299 保證人是否按一定比率負担保證責任為Y  且 保證人為企業戶才要維護保證人負担保證責任比率
    $("input[name='toglePersonType']").change(function(){
        //保證人為企業戶
        
        //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
        var rType = $('#L140M01IForm').find("#rType").val();
        if (rType == "G") {
            //J-105-0100-001 Web e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
            var guaPercentFg = $("input[name='guaPercentFg']:radio:checked").val();
            if (guaPercentFg == "Y") {
                $('#showGuaPercent').show();
            }
            else {
                $('#showGuaPercent').hide();
            }
        }
        else {
            $('#showGuaPercent').hide();
        }
        
        
        
        
        //        if ($(this).val() == '2') {
        //            //保 證人是否按一定比率負担保證責任為Y 
        //            var guaPercentFg = $("input[name='guaPercentFg']:radio:checked").val();
        //            if (guaPercentFg == "Y") {
        //                $('#showGuaPercent').show();
        //            }
        //            else {
        //                $('#showGuaPercent').hide();
        //            }
        //        }
        //        else {
        //            $('#showGuaPercent').hide();
        //        }
    
    });
    
    
    
    //J-104-0157-001 Web e-Loan 企金授信於選取授信科目為「買入(出)選擇權」、「換入(出)換利交易」及「換入(出)換匯換利交易」時，增加欄位「交易目的」。
	$("#loanTP").change(function(){
		var $form = $("#L140M01CForm");
        var loanTp = $form.find("#loanTP").val();
        
        //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "chkIsDerivate",
                loanTp:loanTp
            },
            success: function(obj){
                
            	if(obj.isDerivate == "Y"){
            		$("#showWhenDervSubject").show();
            		
            		if (loanTp == "Z09"   ) {
            			//$form.find ("[name='subjPurpose'][value='2']:radio").attr("checked" , "checked" );   //非避險
            			if($form.find ("[name='subjPurpose']:radio:checked").val() == "1"){
           				 	$form.find("input[name='subjPurpose']").trigger('change');
            			}
            		}else if (loanTp == "Z16"  ) {
            			//J-109-0164_05097_B1001 e-Loan企金授信額度明細表新增「賣出選擇權(避險)」科目
                		//$form.find ("[name='subjPurpose'][value='1']:radio").attr("checked" , "checked" );   //避險		
            		}else if (loanTp == "Z10" || loanTp == "Z11") {
            			$form.find ("[name='subjPurpose'][value='1']:radio").prop("checked" , true);   //避險
            		}
            	}else{
            		$("#showWhenDervSubject").hide();
        			$form.find ("[name='subjPurpose'][value='1']:radio").prop("checked" , false);   //避險
        			$form.find ("[name='subjPurpose'][value='2']:radio").prop("checked" , false);   //避險
            	}

            }
        });

        // J-112-0037 經檢討為避免營業單位發生逾越授權情事,eloan企金國內、外授信管理系統增加相關檢核項目
        var lmtDaysText = $("#lmtDaysText");
        lmtDaysText.val("");
        //只有企金要顯示
        if(responseJSON.docType && responseJSON.docType == "1" ){
        	if (loanTp == "111" || loanTp == "211") {
        		// 111 短期週轉放款、211 短期週轉擔保放款
        		lmtDaysText.val(i18n.lms1405s02["L140M01a.lmtDaysText1"]);// 若為供應鏈融資業務者，清償償期應不逾180天
        	}else if (loanTp == "112" || loanTp == "212" || loanTp == "113" || loanTp == "213") {
        		// 112 短期購料放款、212 短期購料擔保放款
        		// 113 短期外銷放款、213 短期外銷擔保放款
        		lmtDaysText.val(i18n.lms1405s02["L140M01a.lmtDaysText2"]);// 清償償期應不逾180天
        	}
        }
	 });
	
	$("input[name='subjPurpose']").change(function(){ 
		var subjPurpose = $(this).val();
		var loanTP = $("#loanTP").val();
		if(loanTP == "Z09" && subjPurpose == "1"){
			CommonAPI.showMessage("買入選擇權(避險)無須簽報!");
		}
	});
	
    $("#residential").change(function(){
    	var value = $(this).val();
    	if(value == "1" || value == "2" || value == "3"){
    		$("#rebuildView").show();
    	} else {
    		$("input[name='rebuild']").prop("checked", false);
    		$("#rebuildView").hide();
    	}
    }).triggerHandler("change")
    
    //J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
    $("#applyIsStartUp").click(function(){
        //判斷借款人行業對項別是否已列於LNF070 
        $.ajax({
            async: false,
            handler: "lms1405m01formhandler",
            data: {
                formAction: "applyIsStartUp",
                rptMainId: responseJSON.mainId,
                tabFormMainId: $("#tabFormMainId").val(),
                cntrNo: $("#cntrNo").val(),
                custId: $("#L140M01AForm1").find("#custId").val(),
                dupNo: $("#L140M01AForm1").find("#dupNo").val()
            },
            success: function(obj){
                if (obj.isStartUp) {
                    if (obj.isStartUp == "Y") {
                        //已經列於LNF070，不用填itwCode
                        $("input[name='isStartUp'][value='Y']:radio").prop("checked", true); //塞值 
                        //J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
					    $('#belongItwCode').val(obj.belongItwCode);
					    $('#belongItwCodeShow').val(obj.belongItwCodeShow);
                        var projClass = $("#projClass").val();
                        if (projClass == '05' || projClass == '20') {
                            $("#projClass").val('00');
                            $("#projClass").trigger("change");
                        }
                    }
                    else {
                        //未列LNF070，要填
                        $("input[name='isStartUp'][value='N']:radio").prop("checked", true); //塞值 
                        //J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
						$('#belongItwCode').val('');
					    $('#belongItwCodeShow').val('');
                    }
                }
                else {
                    //無資料可判斷
                    $("input[name='isStartUp']:radio:checked").prop("checked", false);
                    //J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
					$('#belongItwCode').val('');
				    $('#belongItwCodeShow').val('');
                }
                $("#isStartUp").trigger("change");
                
                
                //J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
				if(obj.isCoreBuss ){
					if (obj.isCoreBuss == "Y") {
						//已經列於LNF070，不用填itwCode
					    $("input[name='isCoreBuss'][value='Y']:radio").prop("checked" , "checked" );   //塞值 
					    //J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
					    $('#belongItwCodeCoreBuss').val(obj.belongItwCodeCoreBuss);
					    $('#belongItwCodeCoreBussShow').val(obj.belongItwCodeCoreBussShow);
					}else{
						//未列LNF070，要填
						$("input[name='isCoreBuss'][value='N']:radio").prop("checked" , "checked" );   //塞值 
						//J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
						$('#belongItwCodeCoreBuss').val('');
					    $('#belongItwCodeCoreBussShow').val('');
					}	
				}else{
					//無資料可判斷
					$("input[name='isCoreBuss']:radio:checked").prop("checked",false);
					//J-107-0027-001 Web e-Loan企金授信額度明細表增列新創重點產業細目選項
					$('#belongItwCodeCoreBuss').val('');
				    $('#belongItwCodeCoreBussShow').val('');
				}
				$("#isCoreBuss").trigger("change");
				
            }
        });
    });
    
    //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
	$("input[name='isNoFactCountry']" ).change(function(){
		var isNoFactCountry= $( "input[name='isNoFactCountry']:radio:checked" ). val();
		if(isNoFactCountry=="Y"){ 
			$(".showNoFactCountry").show();
			$(".badCountryContent").show();
		}else{
			$(".showNoFactCountry").hide();
			$("#noFactCountry").val('');
			$("#noFactCountryShow").val('');
			
			var isFreezeFactCountry= $( "input[name='isFreezeFactCountry']:radio:checked" ). val();
			if(isFreezeFactCountry!="Y"){
				$(".badCountryContent").hide();
			}
		}
	}).trigger("change");
	
	$("input[name='isFreezeFactCountry']" ).change(function(){
		var isFreezeFactCountry= $( "input[name='isFreezeFactCountry']:radio:checked" ). val();
		if(isFreezeFactCountry=="Y"){ 
			$(".showFreezeFactCountry").show();
			$(".badCountryContent").show();
		}else{
			$(".showFreezeFactCountry").hide();
			$("#freezeFactCountry").val('');
			$("#freezeFactCountryShow").val('');
			
			var isNoFactCountry= $( "input[name='isNoFactCountry']:radio:checked" ). val();
			if(isNoFactCountry!="Y"){
				$(".badCountryContent").hide();
			}
		}
	}).trigger("change");
	
	
	//J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
    
    $("input[name='isRevive']").change(function(){
        var value = $("input[name=isRevive]:checked").val();
        if (value == "Y") {
        	$("#showReviveTable").show();  
        	$("input[name='reviveTarget']").trigger('change');
        }else {
        	$("#showReviveTable").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showReviveTable").hide(); 
        }              
    }).trigger("change");
    
   //J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
    $("input[name='isRevive']").click(function(){
        var value = $("input[name=isRevive]:checked").val();
        if (value == "Y") {
        	var reviveTarget = $("input[name=reviveTarget]:checked").val();
        	if (reviveTarget == undefined || reviveTarget == null || reviveTarget == "") {
        		$.ajax({
                    handler: inits.fhandle,
                    action: "queryL140s08a",
                    data: {//把資料轉成json
                    	rptMainId:responseJSON.mainId,
                        tabFormMainId: $("#tabFormMainId").val()
                    },
                    success: function(responseData){
                    	if(responseData.mainItem != undefined && responseData.mainItem != ""){
                    		if(responseData.mainItem == "XX"){
                    			$("input[name='isRevive'][value='N']:radio" ).prop( "checked" , true );   //塞值
                    			$("input[name='isRevive']").trigger('change');
                    		}else{
                    			$("input[name=reviveTarget]:checked").val();
                            	$("input[name='reviveTarget'][value='01']:radio" ).prop( "checked" , true );   //塞值
                            	$("input[name='reviveTarget']").trigger('change');
                            	$("input[name='reviveCoreIndustry'][value='"+responseData.mainItem+"']:radio" ).prop( "checked" , true );   //塞值
                    		}
                    		
                    	}
                    	
                    }
                });
        	}

        }         
    });
    
    $("input[name='reviveTarget']").change(function(){
    	var value = $("input[name=reviveTarget]:checked").val();
        if (value == "01") {
        	$("#showReviveCoreIndustry").show();
        	$("#showReviveChain").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showReviveChain").hide(); 
        }else if (value == "02") {
        	$("#showReviveChain").show();
        	$("#showReviveCoreIndustry").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showReviveCoreIndustry").hide(); 
        }else if (value == "03") {
        	
        	$("#showReviveCoreIndustry").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showReviveCoreIndustry").hide(); 
        	$("#showReviveChain").find(':input').not(':button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
        	$("#showReviveChain").hide(); 
        }          
    }).trigger("change");
    
    //J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
    $("input[name='reviveTarget']").click(function(){
    	var value = $("input[name=reviveTarget]:checked").val();
    	if (value == "01") {
        	 
    		$.ajax({
                handler: inits.fhandle,
                action: "queryL140s08a",
                data: {//把資料轉成json
                	rptMainId:responseJSON.mainId,
                    tabFormMainId: $("#tabFormMainId").val()
                },
                success: function(responseData){
                	if(responseData.mainItem != undefined && responseData.mainItem != ""){
                		if(responseData.mainItem == "XX"){
                			$("input[name='isRevive'][value='N']:radio" ).prop( "checked" , true );   //塞值
                			$("input[name='isRevive']").trigger('change');
                		}else{
                			$("input[name='reviveCoreIndustry'][value='"+responseData.mainItem+"']:radio" ).prop( "checked" , true );   //塞值
                		}
                		
                	}
                	
                }
            });

        }         
    }); 
    //J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
    $("input[name='isOfficialCga']").change(function(){
        var value = $("input[name=isOfficialCga]:checked").val();
        if (value == "Y") {
            $("#isOfficialCgaSpan").show();
            // 限定 國際&長期
            $("#cga_crdArea").val("1").prop('disabled', true);
            $("#cga_crdPred").val("1").prop('disabled', true);
        } else {
            $("span#isOfficialCgaSpan").find("select").val("");
            $("#cga_crdGrade").val("");
            $("#cga_rskRatio").val("");
            $("#isOfficialCgaSpan").hide();
        }
    });//.trigger("change");
    
    //J-109-0239_05097_B1001 Web e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
    $("input[name='isSpecialFinRisk']").change(function(){
    	var value = $("input[name=isSpecialFinRisk]:checked").val();
        if (value == "Y") {
        	$("#isSpecialFinRiskSpan").show();
        } else {
        	$("div#isSpecialFinRiskSpan").find("select").val("");
            $("#isSpecialFinRiskSpan").hide();
        }
    }).trigger("change");
    
    // J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
    $("#specialFinRiskType").change(function(){
        var value = $("#specialFinRiskType").val();
        if (value == "1") {
        	//專案融資
            $("#isProjectFinOperateStagSpan").show();                        
        } else {
        	$("input[name='isProjectFinOperateStag']" ).removeAttr('checked');   //塞值
            $("#isProjectFinOperateStagSpan").hide();
        }
        $("input[name='isProjectFinOperateStag']" ).triggerHandler("change");
    }).trigger("change");
    
    //J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位
    $("input[name='isProjectFinOperateStag']").change(function(){
    	//專案融資是否屬營運階段>選擇是時才顯示"高品質融資"相關選項
    	//只有企金才顯示此欄位
    	if(responseJSON.docType && responseJSON.docType == "1" ){
	    	if( $("input[name='isProjectFinOperateStag']:checked").val() == "Y"){
	    		$("#isHqProjSpan").show();
	    	}else{
	    		$("input[name='isHighQualityProjOpt_1']").removeAttr('checked');//勾選拿掉
	    		$("input[name='isHighQualityProjOpt_2']").removeAttr('checked');//勾選拿掉
	    		$("input[name='isHighQualityProjOpt_3']").removeAttr('checked');//勾選拿掉
	    		$("input[name='isHighQualityProjOpt_4']").removeAttr('checked');//勾選拿掉
	    		$("input[name='isHighQualityProjOpt_5']").removeAttr('checked');//勾選拿掉
	    		$("#isHighQualityProjResult").val("");//最終結果清空
	            $("#isHqProjSpan").hide();       
	    	}
	    	//取得高品質最終結果
	    	getHighQualityResult();
    	}
    });
    
    $("input[name^='isHighQualityProjOpt_']").change(function(){
    	//取得高品質最終結果
    	getHighQualityResult();
    });
    
    $("#applyCountryCrd").click(function(){
        var value = $("select#cga_country option:selected").val();
        if(value != undefined && value != ""){
            // 引進國家信評
            $.ajax({
                handler: inits.fhandle,
                action: "queryCountryCrd",
                data: {
                    country: value
                },
                success: function(responseData){
                    if (responseData.msg && responseData.msg != "") {
                        return API.showErrorMessage(responseData.msg);
                    } else {
                        $("#cga_crdType").val(responseData.cga_crdType);
                        $("#cga_crdGrade").val(responseData.cga_crdGrade);
                        if(responseData.cga_rskRatio != undefined && responseData.cga_rskRatio != ""){
                            $("#cga_rskRatio").val(responseData.cga_rskRatio);
                        }
                    }
                }
            });
        } else {
            return API.showErrorMessage("請選擇主權國家");
        }
    });
    $("#btn_fcrdGrad").click(function(){
        var crdType = $("#cga_crdType :selected").val();
        var crdArea = $("#cga_crdArea :selected").val();
        var crdPred = $("#cga_crdPred :selected").val();
        if(crdType != undefined && crdType != "" && crdArea != undefined
            && crdArea != "" && crdPred != undefined && crdPred != ""){
            // 取得風險等級
            $("#crdGradeGrid").jqGrid("setGridParam", {
                postData : {
                    handler: "lms1810gridhandler",
                    queryType: "L140",
                    fcrdType: crdType,
                    fcrdArea: crdArea,
                    fcrdPred: crdPred,
                    formAction: "query_elfFcrdGrad"
                },
                search: true
            }).trigger("reloadGrid");

            $("#crdGradeThickbox").thickbox({
                title: "",
                width: 400,
                height: 400,
                align: "center",
                valign: "bottom",
                modal: false,
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                         var selrow = $("#crdGradeGrid").getGridParam('selrow');
                         if (selrow) {
                            var data =  $("#crdGradeGrid").getRowData(selrow);
                            $("#cga_crdGrade").val( data.ratingGrad );
                            $("#cga_rskRatio").val("");
                            $.thickbox.close();
                         }
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        } else {
            return API.showErrorMessage("信評相關欄位尚未填妥");
        }
    });
    $("#applyRskRatio").click(function(){
        var crdType = $("#cga_crdType :selected").val();
        var value = $("#cga_crdGrade").val();
        if(value != undefined && value != ""){
            // 計算風險權數
            $.ajax({
                handler: inits.fhandle,
                action: "queryCrdGradeToRskRatio",
                data: {
                    crdType: crdType,
                    crdGrade: value
                },
                success: function(responseData){
                    if (responseData.msg && responseData.msg != "") {
                        return API.showErrorMessage(responseData.msg);
                    } else {
                        $("#cga_rskRatio").val(responseData.cga_rskRatio);
                    }
                }
            });
        } else {
            return API.showErrorMessage("請先取得風險等級");
        }
    });
    $("#cga_country").change(function(){
        $("#cga_crdType").val("");
        $("#cga_crdGrade").val("");
        $("#cga_rskRatio").val("");
    });//.trigger("change");
    $("#cga_crdType").change(function(){
        $("#cga_crdGrade").val("");
        $("#cga_rskRatio").val("");
    });//.trigger("change");
	
	
	$("#selectNoFactCountryBt").click(function(){	
		//J-106-0XXX-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
		selectCountryBt(1);	
	});

	$("#selectFreezeFactCountryBt").click(function(){	
		//J-106-0XXX-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
		selectCountryBt2();		
	});
	

	//J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
	$("input[name=newAdcCaseNoRadio]").click(function(){
        if ("org" == $(this).val()) {
        	$("#originalInputAdc").show();
        } else {
        	$("#originalInputAdc").hide();
        	$("#orgAdcCaseNo").val('');
        }
    });

    $("input[name=queryType]").click(function(){
    	 var queryType = $(this).val();
        $(".queryInput").hide();
        $("#queryInput_" + queryType).show();
    });
    
	$("#newAdcCaseNoBt").click(function(){
        $("#newAdcCaseNoBox").thickbox({
            title: i18n.lms1405s02["L140M01a.adcCaseNo"],
            width: 800,
            height: 300,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var type = $("input[name=newAdcCaseNoRadio]:checked").val();
                    var adcCaseNo = $("#adcCaseNo").val();
                    if ($.trim(adcCaseNo) != "" && type != "del") {
                        return CommonAPI.showErrorMessage(i18n.lms1405s02["L140MM3A.message14"]);
                    }
                    if(type == "new") {
                        $.ajax({
                            handler: inits.fhandle,
                            action: "queryNewAdcCaseNo",
                            data: {
                            	mainId : responseJSON.mainId,
                            	tabFormMainId : $("#tabFormMainId").val()
                            },
                            success: function(obj){
                                $("#adcCaseNo").val(obj.adcCaseNo);
                                $.thickbox.close();
                            }
                        });
                    } else if(type == "org") {
                        var orgAdcCaseNo = $("#orgAdcCaseNo").val();
                        if ($.trim(orgAdcCaseNo) == "") {
                            return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.error18"]);
                        }
                        $.ajax({
                            handler: inits.fhandle,
                            action: "chkAdcCaseNo",
                            data: {
                                adcCaseNo: orgAdcCaseNo,
                                mainId : responseJSON.mainId,
                                tabFormMainId : $("#tabFormMainId").val(),
                                save: true
                            },
                            success: function(obj){
                                if (obj.msg && obj.msg != "") {
                                    return API.showErrorMessage(obj.msg);
                                } else {
                                    $("#adcCaseNo").val(orgAdcCaseNo);
                                    $.thickbox.close();
                                }
                            }
                        });
                    } else if(type == "del") {
                        $("#adcCaseNo").val('');
                        $.thickbox.close();
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });

    $("#queryAdcCaseNoBt").click(function(){
    	 
        $("#queryCustId").val($("#L140M01AForm1").find("#custId").val());
        $("#queryDupNo").val($("#L140M01AForm1").find("#dupNo").val());
        $("#queryCntrNo").val($("#cntrNo").val());

        $("#queryAdcCaseNoBox").thickbox({
            title: i18n.lms1405s02["L140M01a.adcCaseNo"],
            width: 600,
            height: 200,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var queryType = $("input[name=queryType]:checked").val();
                    if(queryType == "1") {
                        var queryCustId = $("#queryCustId").val();
                        var queryDupNo = $("#queryDupNo").val();
                        if ($.trim(queryDupNo) == "") {
                            queryDupNo = "0";
                        }
                        if ($.trim(queryCustId) == "") {
                            return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.error18"]);
                        }
                    } else if(queryType == "2") {
                        var queryCntrNo = $("#queryCntrNo").val();
                        if ($.trim(queryCntrNo) == "") {
                            return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.error18"]);
                        }
                    }
                    $.thickbox.close();
                    adcGrid.jqGrid("setGridParam", {
                        postData: {
                            queryType: queryType,
                            queryCustId: queryCustId,
                            queryDupNo: queryDupNo,
                            queryCntrNo: queryCntrNo
                        },
                        search: true
                    }).trigger("reloadGrid");
                    $("#queryAdcThickBox").thickbox({
                       title: i18n.lms1405s02["L140M01a.error07"]+i18n.lms1405s02["L140M01a.adcCaseNo"],
                       width: 400,
                       height: 450,
                       align: "center",
                       valign: "bottom",
                       modal: false,
                       i18n: i18n.def,
                       buttons: {
                            "sure": function(){
                                var row = adcGrid.getGridParam('selrow');
                                if (!row) {
                                    return CommonAPI.showMessage(i18n.def["grid_selector"]);
                                }
                                if (row.length > 1) {
                                    CommonAPI.showMessage(i18n.lms1405s02["L140MM3A.error1"]);
                                } else {
                                    CommonAPI.confirmMessage(i18n.lms1405s02["L140MM3A.message15"], function(b){
                                        if (b) {
                                            var adcCaseNo = $("#adcCaseNo").val();
                                            if ($.trim(adcCaseNo) != "") {
                                                return CommonAPI.showErrorMessage(i18n.lms1405s02["L140MM3A.message14"]);
                                            }
                                            var data = adcGrid.getRowData(row);
                                            $("#adcCaseNo").val(data.adcCaseNo);
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            },
                            "close": function(){
                                $.thickbox.close();
                            }
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });
	
    //G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
    $("#esgGreenSpendTypeBT").click(function(){	
		esgGreenSpendTypeBT();	
	});
    
    //G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
    $("#esgSustainLoanTypeBT").click(function(){	
    	esgSustainLoanTypeBT();	
	});
    
    //J-113-0329 企金授信新增社會責任授信
    $("#socialKindBT").click(function(){	
    	socialKindBT();	
	});
    $("#socialTaBT").click(function(){	
    	socialTaBT();	
	});
    $("#socialRespBT").click(function(){	
    	socialRespBT();	
	});
    
    //====================Button event End=========================
    
    //====================Grid Code================================
    
    /** 連保人-自然人  */
    function gridviewNatural(){
        $("#gridviewNatural").iGrid({
            height: 200,
            handler: "lms1405gridhandler",
			localFirst: true,
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01i",
                mainId: $("#tabFormMainId").val(),
                type: "1"
            },
            multiselect: true,
            hideMultiselect: false,
            colModel: [{
                colHeader: i18n.lms1405s02["L140M01a.conPersonId"],//"連保人統編(含重覆序號)",
                width: 80,
                name: 'rId',
                sortable: true,
                formatter: 'click',
                onclick: newToglePersonBT
            }, {
                colHeader: i18n.lms1405s02["L140M01a.conPersonName"],//"連保人名稱",
                name: 'rName',
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1405s02["L140M01a.county"],//"國別",
                name: 'rCountry',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.lms1405s02["L140M01a.conPersonRation"],//"與借款人關係",
                name: 'rKindD',
                width: 100,
                sortable: true,
                align: "center"
            }, {
                //J-105-0100-001 Web e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
                colHeader: i18n.lms1405s02["L140M01a.guaPercent"],//"保證人負担保證責任比率",
                name: 'guaPercentStr',
                width: 20,
                sortable: false,
                align: "right",
                formatter: function(data){
                    var guaPercentFg = $("input[name='guaPercentFg']:radio:checked").val();
                    if (guaPercentFg == "Y") {
                        if (data == 'undefined' || data == null) {
                            return "";
                        }
                        else {
                            return data;
                        }
                    }
                    else {
                        return "100";
                    }
                }
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
                colHeader: "rType",
                name: 'rType',
                hidden: true
            }, {
				//J-110-0249
                colHeader: "guarantorTypeItem",
                name: 'guarantorTypeItem',
                hidden: true	
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewNatural").getRowData(rowid);
                newToglePersonBT(null, null, data);
            }
        });
    }
    
    /** 連保人-法人 */
    function gridviewCorporate(){
        $("#gridviewCorporate").iGrid({
            height: 200,
            handler: "lms1405gridhandler",
			localFirst: true,
            sortname: 'createTime',
            sortorder: 'asc',
            postData: {
                formAction: "queryL140m01i",
                mainId: $("#tabFormMainId").val(),
                type: "2"
            },
            multiselect: true,
            hideMultiselect: false,
            colModel: [{
                colHeader: i18n.lms1405s02["L140M01a.conPersonId"],// "連保人統編(含重覆序號)",
                width: 30,
                name: 'rId',
                sortable: true,
                formatter: 'click',
                onclick: newToglePersonBT
            }, {
                colHeader: i18n.lms1405s02["L140M01a.conPersonName"],// "連保人名稱",
                name: 'rName',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.lms1405s02["L140M01a.county"],//"國別",
                name: 'rCountry',
                width: 40,
                sortable: true
            }, {
                colHeader: i18n.lms1405s02["L140M01a.conPersonRation"],//"與借款人關係",
                name: 'rKindD',
                width: 40,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.lms1405s02["L140M01a.guaPercent"],//"保證人負担保證責任比率",
                name: 'guaPercentStr',
                width: 20,
                sortable: false,
                align: "right",
                formatter: function(data){
                    var guaPercentFg = $("input[name='guaPercentFg']:radio:checked").val();
                    if (guaPercentFg == "Y") {
                        if (data == 'undefined' || data == null) {
                            return "";
                        }
                        else {
                            return data;
                        }
                    }
                    else {
                        return "100";
                    }
                }
            }, {
				//J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
                colHeader: i18n.lms1405s02["L140M01a.priority"],//信用品質順序
                name: 'priority',
                align: "center",
                width: 20,
                sortable: false
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
                colHeader: "rType",
                name: 'rType',
                hidden: true
            }, {
				//J-110-0249
                colHeader: "guarantorTypeItem",
                name: 'guarantorTypeItem',
                hidden: true	
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewCorporate").getRowData(rowid);
                newToglePersonBT(null, null, data);
            }
        });
    }
    
    /** 授信科目Grid*/
    function gridviewitemType(){
        var val_L120M01A_ratingFlag = _get_val_L120M01A_ratingFlag();
        var colModel_grade_hidden = !(val_L120M01A_ratingFlag == "JP" || val_L120M01A_ratingFlag == "AU" 
        		|| val_L120M01A_ratingFlag == "TH");
        
        $("#gridviewitemType").iGrid({//授信科目grid
            handler: "lms1405gridhandler",
            height: 200,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'seqNum|createTime|subjSeq',
            sortorder: 'asc|asc|asc',
            postData: {
                formAction: "queryL140m01c",
                tabFormMainId: $("#tabFormMainId").val()
            },
            autowidth: true,
            colModel: [{
                colHeader: i18n.lms1405s02["L140M01c.item"],//"授信科目",
                name: 'loanTP',
                align: "left",
                width: 150,
                sortable: true,
                formatter: 'click',
                onclick: newItemTypeSelect
            }, {
                colHeader: i18n.lms1405s02["L140M01c.subjDscr"],//"科目補充說明",
                name: 'subjDscr',
                align: "left",
                width: 200,
                sortable: true
            }, { //在 formatter 寫rowObject[4]
                //為了避免欄位index順序的問題
                //新的欄位都 往後加
                colHeader: i18n.lms1405s02["L140M01c.lmtDays"],//"清償期限",
                width: 120,
                name: 'lmtDays',
                align: "right",
                sortable: true,
                formatter: itemFormatter2
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'lmtOther',
                hidden: true
            }, {
                //企金的額度明細表，未加入 lms1405s0202
                colHeader: i18n.lms1405s0202 ? i18n.lms1405s0202["L140M01c.grade"] : 'grade', //grade
                width: 26,
                name: 'grade',
                align: "center",
                hidden: colModel_grade_hidden,
                sortable: true
            }, {
                name: 'lnYear',
                hidden: true
            }, {
                name: 'lnMonth',
                hidden: true
            }, {
                name: 'repaymentSchFmt',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewitemType").getRowData(rowid);
                newItemTypeSelect(null, null, data);
            }
        });
    }
    //當其他續做條件被勾選了
    function itemFormatter2(cellvalue, otions, rowObject){
        var itemName = '';
        var val_L120M01A_ratingFlag = _get_val_L120M01A_ratingFlag();
        if ((val_L120M01A_ratingFlag == "JP" || val_L120M01A_ratingFlag == "AU" 
        		|| val_L120M01A_ratingFlag == "TH")) {
            var _lnYear = rowObject[6];
            var _lnMonth = rowObject[7];
            var _repaymentSchFmt = rowObject[8];
            if (!_lnYear) {
                _lnYear = " ";
            }
            if (!_lnMonth) {
                _lnMonth = " ";
            }
            if(_repaymentSchFmt=="2"){
            	itemName = cellvalue;
            }else{
            	itemName = format_lnYear_lnMonth(i18n.lms1405s0202['L140M01c.lnYearMonth'], _lnYear, _lnMonth);	
            }            
        }
        else {
            if (rowObject[4] == '1') {
                //lmtOther=詳其他敘做條件  
                itemName = i18n.lms1405s02['L140M01c.lmtOther2'];
            }
            else {
                itemName = cellvalue;
            }
        }
        return itemName;
    }
    var format_lnYear_lnMonth = function(str, col){
        col = typeof col === 'object' ? col : Array.prototype.slice.call(arguments, 1);
        
        return str.replace(/\{\{|\}\}|\{(\w+)\}/g, function(m, n){
            if (m == "{{") {
                return "{";
            }
            if (m == "}}") {
                return "}";
            }
            return col[n];
        });
    };
    
    /**  共用額度序號簽報書選擇  */
    function gridviewConnomOther(){
        $("#gridviewConnomOther").iGrid({
            handler: 'lms1405gridhandler',
            height: 250,
            width: 600,
            sortname: 'custId',
            postData: {
                formAction: "queryL120m01a",
                rowNum: 10
            },
            colModel: [{
                colHeader: i18n.lms1405s02["L140M01a.caseDate"],//簽案日期,
                name: 'caseDate',
                align: "center",
                width: 80,
                sortable: false
            }, {
                colHeader: i18n.lms1405s02["L140M01a.mainPerson"],//"主要借款人",
                name: 'custName',
                width: 120,
                sortable: false
            }, {
                colHeader: i18n.lms1405s02["L140M01a.caseNum"],//"案號",
                name: 'caseNo',
                width: 160,
                sortable: false
            }, {
                colHeader: i18n.lms1405s02["L140M01a.manger"],//"經辦",
                name: 'updater',
                width: 80,
                sortable: false,
                align: "center"
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                colHeader: "mainId",
                name: 'mainId',
                hidden: true
            }]
        });
    }
    
    /**  共用額度序號 */
    function gridviewConnomOtherSelect(){
        $("#gridviewConnomOtherSelect").iGrid({
            // 使用外層編製中和待補件的grid資料(根據文件狀態抓出這兩個資料)
            handler: "lms1405gridhandler",
            sortname: 'cntrNo',
            sortorder: 'asc',
            colModel: [{
                colHeader: i18n.lms1405s02["L140M01a.cntrNo"],//"額度序號",
                name: 'cntrNo',
                align: "center",
                width: 80,
                sortable: true
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }]
        });
    }
    
    
    /**  收付彙計數Grid */
    function gridviewCollect(){
        $("#gridviewCollect").iGrid({
            handler: "lms1405gridhandler",
            sortorder: 'asc',
            sortname: "createTime",
            height: 235,
            rowNum: 10,
            multiselect: true,
            hideMultiselect: false,
            postData: {
                formAction: "queryCollect",
                tabFormMainId: $("#tabFormMainId").val()
            },
            colModel: [{
                colHeader: i18n.lms1405s02["L782M01A.applyCurr"] + "(" + i18n.lms1405s02["L140M01a.Pay"] + ")", //L782M01A.applyCurr =幣別(付),
                name: 'CPCurr',
                align: "center",
                width: 80,
                sortable: true,
                formatter: 'click',
                onclick: CollectAction.addCollectBox
            
            }, {
                colHeader: i18n.lms1405s02["L140M01h.cp1Fee"] + "(" + i18n.lms1405s02["L140M01a.Pay"] + ")", //L140M01h.cp1Fee=金額(付),
                name: 'CollectPay',
                align: "right",
                formatter: GridFormatter.number['addComma'],
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.lms1405s02["L782M01A.applyCurr"] + "(" + i18n.lms1405s02["L140M01a.Accept"] + ")", //L782M01A.applyCurr =幣別(收)
                name: 'CACurr',
                align: "center",
                width: 80,
                sortable: true,
                formatter: 'click',
                onclick: CollectAction.addCollectBox
            }, {
                colHeader: i18n.lms1405s02["L140M01h.cp1Fee"] + "(" + i18n.lms1405s02["L140M01a.Accept"] + ")", //L140M01h.cp1Fee=金額(收)
                name: 'CollectAccept',
                align: "right",
                formatter: GridFormatter.number['addComma'],
                width: 80,
                sortable: true
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewCollect").getRowData(rowid);
                CollectAction.addCollectBox(null, null, data);
            }
        });
    }
    
    //J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
	var adcGrid = $("#queryAdcGridview").iGrid({
	    handler: inits.ghandle,
        height: 300,
        width: 350,
        autowidth: false,
        action: "queryAdcList",
        postData: {
        },
        rowNum: 15,
        sortname: "adcCaseNo",
        colModel: [{
            colHeader: i18n.lms1405s02["L140M01a.adcCaseNo"],
            align: "center",
            name: 'adcCaseNo'
        }]
    });
	
	//J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	/**  保證人信用品質順序設定grid  */
   
    function gridViewGuarantorSeq(){
        $("#gridViewGuarantorSeq").iGrid({
			needPager: false,
            handler: 'lms1405gridhandler',
            postData: {
                formAction: "queryL140m01i",
                mainId: $("#tabFormMainId").val(),
                type: "2"
            },
            height: 230,
            cellsubmit: 'clientArray',
            autowidth: true,
            sortname: 'priority|createTime',
            sortorder: 'asc|asc',
            colModel: [{
                colHeader: i18n.lms1405s02["L140M01a.conPersonId"],// "連保人統編(含重覆序號)",
                width: 30,
                name: 'rId',
                sortable: false,
                formatter: 'click'
            }, {
                colHeader: i18n.lms1405s02["L140M01a.conPersonName"],// "連保人名稱",
                name: 'rName',
                width: 80,
                sortable: false
            }, {
                colHeader: i18n.lms1405s02["L140M01a.county"],//"國別",
                name: 'rCountry',
                width: 40,
                sortable: false
            }, {
                colHeader: i18n.lms1405s02["L140M01a.conPersonRation"],//"與借款人關係",
                name: 'rKindD',
                width: 30,
                sortable: false,
                align: "center"
			 }, {
				colHeader: i18n.lms1405s02["L140M01a.guaPercent"],//"保證人負担保證責任比率",
				name : 'guaPercentStr',
				width : 20,
				sortable : false,
				align : "right",
				formatter : function(data) {					
					var guaPercentFg = $("input[name='guaPercentFg']:radio:checked").val();
					if(guaPercentFg == "Y"){						
						if(data == 'undefined' || data == null ){
							return "";							
						}else{
                            return data;
						}						
					}else{
						return "100";					
					} 
				}								 
            }, {
                colHeader: i18n.lms1405s02["L140M01a.priority"],//信用品質順序
                name: 'priority',
                align: "center",
                editable: true,
                width: 60,
                sortable: true,
                editrules: {
                    number: true
                }
            }, {
                name: 'oid',
                hidden: true
            }],
            onSelectRow: function(id){
                if (id && id != lastSel) {
                	$("#gridViewGuarantorSeq").saveRow(lastSel, false, 'clientArray');
                    $('#gridViewGuarantorSeq').restoreRow(lastSel);
                    lastSel = id;
                }
                $('#gridViewGuarantorSeq').editRow(id, false);
            }
        });
    }
	
    //========================Grid Code End==================================
    
    //==========================thickbox Code ===============================
    
    function relationshipBT(callback){//關係
        //初始化登錄關係畫面
        $("#relationshipSelect").val("");
        $("[id^=rationSelect]").val("");
        $("#the1,#the2,#the3").hide();
        $("#relationshipBox").thickbox({ // 使用選取的內容進行彈窗
            //title.10=關係
            title: i18n.lms1405s02["title.10"],
            width: 300,
            height: 220,
            modal: true,
            align: "center",
            readOnly: false,
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var context;
                    var rKindD;
                    switch ($('#relationshipSelect').val()) {
                        case "1":
                            if ($("#rationSelect1").val() == "") {
                                //grid.selrow=請先選擇一筆資料。
                                return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                            }
                            context = $("#rationSelect1 :selected").text();
                            rKindD = $("#rationSelect1 :selected").val();
                            break;
                        case "2":
                            if ($("#rationSelect2").val() == "") {
                                //grid.selrow=請先選擇一筆資料。
                                return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                            }
                            context = $("#rationSelect2 :selected").text();
                            rKindD = $("#rationSelect2 :selected").val();
                            break;
                        case "3":
                            if ($("#rationSelect31").val() == "" || $("#rationSelect32").val() == "") {
                                //grid.selrow=請先選擇一筆資料。
                                return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                            }
                            context = $("#rationSelect31 :selected").text().split(" - ")[1] + ' - ' + $("#rationSelect32 :selected").text().split(" - ")[1];
                            rKindD = $("#rationSelect31 :selected").val() + $("#rationSelect32 :selected").val();
                            context = rKindD + "-" + context;
                            break;
                        default:
                            //grid.selrow=請先選擇一筆資料。
                            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                            break;
                    }
                    var obj = {};
                    obj.context = context;
                    obj.rKindM = $('#relationshipSelect').val();
                    obj.rKindD = rKindD;
                    commonNum_One(obj, callback);
                    $.thickbox.close();
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    /**  共用額度序號 */
    function commonNumOther(){
        $("#commonNumOtherBox").thickbox({
            title: i18n.lms1405s02["title.11"],//'簽報書選擇',
            width: 700,
            height: 400,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var girdId = $("#gridviewConnomOther").getGridParam('selrow');
                    var data = $("#gridviewConnomOther").getRowData(girdId);
                    if (!girdId) {
                        //L140M01a.error05=請選擇一份簽報書
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error05"]);
                    }
                    var commonNumSelect = function(data){
                        $("#commSno").val(data.cntrNo);
                    };//本案共用額度序號的新增
                    $.thickbox.close();
                    commonNumOtherSelect(data.mainId, 'queryL140m01aCntrNo', commonNumSelect);
                    //將選到的簽報書取mainId，再去找他現有的額度序號
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    /** 取得選擇簽報書額度序號 以供共用額度序號使用
     *
     * @param {mainId} 文件編號
     * @param {String} method grid查詢的方法
     * @param {function} 程式最後執行的function
     */
    function commonNumOtherSelect(mainId, method, callback){
        $("#gridviewConnomOtherSelect").jqGrid("setGridParam", {//重新設定grid需要查到的資料
            postData: {
                formAction: method,
                mainId: mainId,
                itemType: "1"
            },
            search: true
        }).trigger("reloadGrid");
        
        $("#commonNumOtherSelectBox").thickbox({
            //title.12=選擇額度序號
            title: i18n.lms1405s02["title.12"],
            width: 550,
            height: 300,
            modal: true,
            readOnly: false,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var girdId = $("#gridviewConnomOtherSelect").getGridParam('selrow');
                    var data = $("#gridviewConnomOtherSelect").getRowData(girdId);
                    if (!girdId) {
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error06"]);
                    }
                    commonNum_One(data, callback);
                    $.thickbox.close();
                    //將選到的簽報書取oid，再去找他現有的額度序號
                
                
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    /** 
     * 預約額度序號
     * @param {Object} cntrNos 額度序號物件
     */
    function openCntrNoSelectBox(cntrNos){
        $("#newFcltNoSelect").setItems({
            item: cntrNos
        });
        $("#newFcltNoSelectBox").thickbox({
            //L140M01a.cntrNo=額度序號
            title: i18n.lms1405s02["L140M01a.cntrNo"],
            width: 250,
            height: 150,
            modal: true,
            i18n: i18n.def,
            readOnly: false,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var value = $("#newFcltNoSelect").val();
                    if (value == "") {
                        //grid_selector=請選擇資料
                        return CommonAPI.showErrorMessage(i18n.def['grid_selector']);
                    }
                    var obj = CntrNoAPI.queryOriginalCntrNo(value, "2");
                    if (!$.isEmptyObject(obj) && obj.cntrNo) {
                        $("#cntrNo").val(obj.cntrNo);
                        $("#showCntrNoName").html(obj.ownBrName);
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    /** 額度序號thickbox  */
    function openCntrNoBox(){
        //點開初始化
        $("#newFcltNoBox").find("input[name=contentBrankRadio]").prop("checked", false);
        //請選擇額度序號來源
        $("#newFcltNoBox").find("input[name=newFcltNoRadio]").prop("checked", false);
        $("#newFcltNo_No_bankNum").val("");
        //產生新號區塊
        $("#originalInput,#tb_newFcltNo_NO").hide();
        $("#tr_contentBrankRadio_NO").hide();
        $("#tb_newFcltNo_Yes").hide();
        $("#originalText").val("");
        //英文版不顯示註記區塊
        if(userInfo.userLocale == "en"){
        	$(".desc_zh_only").hide();
        	$(".desc_en_only").show();
        }else{
        	$(".desc_zh_only").show();
        	$(".desc_en_only").hide();
        }

        $("#newFcltNoBox").thickbox({
            //L140M01a.cntrNo=額度序號
            title: i18n.lms1405s02["L140M01a.cntrNo"],
            width: 700,
            height: 400,
            modal: true,
            i18n: i18n.def,
            readOnly: false,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                	//請選擇額度序號來源  new=產生新號(適用於「新做」案件),  original=登錄原案額度序號, clean=清除額度序號
                    var checked = $("input[name=newFcltNoRadio]:checked").val();
                	//本額度之作帳分行(帳務管理行)是否同為簽報分行?
                    //Yes=是, NO=否
                    var checkedtype = $("input[name=contentBrankRadio]:checked").val();
                    //手打輸入分行代號
                    var checkedtypeNO = $("#newFcltNo_No_bankNum").val();
                    //當畫面上已經有額度序號 不能再給號
                    var cntrNo = $("#cntrNo").val();
                    if ($.trim(cntrNo) != "" && checked != "clean") {
                        //L140M01a.error41=額度序號已產生，不得再重新給號！！
                        return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error41"]);
                        
                    }
                    
                    if(checked == "new"){//new=產生新號(適用於「新做」案件)
                    	if (checkedtype == "Yes") {//Yes=是，由簽報行統籌額度管理及帳務還本付息事項        
                    		$("#cntrNo").val(CntrNoAPI.newCntrNo($("#L140M01AForm1").find("#ownBrId").val(), "5"))
                            $.thickbox.close();      		              	
                        }else if (checkedtype == "NO") {//否，請輸入作帳分行代號(三碼)，將另由作帳行統籌額度管理及帳務還本付息事項
	                    	//先檢查分行類別，若類別為國內則需要輸入DBU,OBU
							$.ajax({
								handler: "lms1405m01formhandler",
								action: "queryBankNum",//檢查是否有這個分行代號
								data: {//把資料轉成json
									ownBrId: checkedtypeNO,
									tabFormMainId: $("#tabFormMainId").val()
								},
								success: function(obj){
									$.thickbox.close();
									CommonAPI.confirmMessage(obj.ownBrName, function(b){
										if (b) {
											if (obj.flag) {
												//開啟選擇DBU OBU 視窗
												selectDbuOrObu(checkedtypeNO);
											}
											else {
												$("#showCntrNoName").html(obj.ownBrName);
												$("#cntrNo").val(obj.cntrNo);
											}
										}
									});
									
								}//close success
							}); //close ajax
                        }
                    }else if(checked == "original"){//original=登錄原案額度序號
            			var originalText = $("#originalText").val().toUpperCase();
                        var obj = CntrNoAPI.queryOriginalCntrNo(originalText, "1");
						if (!$.isEmptyObject(obj) && obj.cntrNo) {
							$("#cntrNo").val(obj.cntrNo);
                            $("#showCntrNoName").html(obj.ownBrName);
                            $.thickbox.close();
                        }
                    }else if(checked == "clean"){//額度序號來源, clean=清除額度序號
						$.thickbox.close();
                        //清除分行名稱
                        $("#showCntrNoName").html("");
                        //清除逾放比率
                        $("#npldate").val("");
                        $("#npl").val("");
                        $("#L140M01AForm2 #cntrNo").val("");
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    //收付彙計數相關程式
    var CollectAction = {
        //form 的名稱
        $form: $("#L140M01KForm"),
        //grid 的Id
        gridId: "#gridviewCollect",
        /**
         * 開啟收付彙計數 grid視窗
         */
        openCollectBox: function(){
            $("#collectBox").thickbox({
                //L140M01a.collectPay=借款收付彙計數 
                title: i18n.lms1405s02['L140M01a.collectPay'],
                width: 640,
                height: 370,
                modal: true,
                readOnly: false,
                i18n: i18n.def,
                buttons: {
                    "newData": function(){
                        CollectAction.addCollectBox();
                    },
                    "del": function(){
                        var $gridviewCollect = $(CollectAction.gridId);
                        var ids = $gridviewCollect.getGridParam('selarrrow');
                        if (ids == "") {
                            //TMMDeleteError=請先選擇需修改(刪除)之資料列
                            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
                        }
                        
                        // confirmDelete=是否確定刪除?
                        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                            if (b) {
                                var gridIDList = [];
                                for (var i in ids) {
                                    gridIDList[i] = $gridviewCollect.getRowData(ids[i]).oid;
                                }
                                $.ajax({
                                    handler: inits.fhandle,
                                    action: "delCollect",
                                    data: {//把資料轉成json
                                        tabFormMainId: $("#tabFormMainId").val(),
                                        oidList: gridIDList
                                    },
                                    success: function(data){
                                        CollectAction.getDrc(data);
                                        $gridviewCollect.trigger('reloadGrid');
                                    }//close success
                                }); //close ajax
                            }
                        });
                    },
                    "close": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        /**
         * 新增 收付彙計數視窗
         * @param {Object} callValue grid 顯示
         * @param {Object} setting grid　設定
         * @param {Object} data 欄位資料
         */
        addCollectBox: function(callValue, setting, data){
            var oid = (data && data.oid) || "";
            $.ajax({
                handler: "lms1405m01formhandler",
                action: "queryCollect",
                data: {
                    tabFormMainId: $("#tabFormMainId").val(),
                    oid: oid
                },
                success: function(obj){
                    CollectAction.openAddCollectBox();
                    CollectAction.$form.injectData(obj);
                    //把這份文件的oid放上去
                    CollectAction.$form.attr("oid", oid);
                }
            }); //close ajax
        },
        /**
         * 開啟新增 收付彙計數視窗
         */
        openAddCollectBox: function(){
            $("#addCollectBox").thickbox({
                //L140M01a.collectPay=借款收付彙計數 
                title: i18n.lms1405s02['L140M01a.collectPay'],
                width: 500,
                height: 200,
                modal: true,
                readOnly: false,
                i18n: i18n.def,
                open: function(){
                    CollectAction.$form.find("select,input").val("").removeAttr("disabled").removeAttr("readonly");
                },
                close: function(){
                },
                buttons: {
                    "saveData": function(){
                        var $form = CollectAction.$form;
                        if (!$form.valid()) {
                            return false
                        }
                        var cpCurr = $.trim($("#CPCurr", $form).val());
                        var cpPmt = $.trim($("#CollectPay", $form).val());
                        var caCurr = $.trim($("#CACurr", $form).val());
                        var caAmt = $.trim($("#CollectAccept", $form).val());
                        
                        if (cpCurr == "" && cpPmt == "" && caCurr == "" && caAmt == "") {
                            //L140M01a.error18=請輸入
                            return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error18"]);
                        }
                        
                        if ((cpCurr != "" && cpPmt == "") || (caCurr != "" && caAmt == "")) {
                            //L140M01a.error33=「{0}」幣別已填，金額不得為空白
                            return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error33"].replace("「{0}」", ""));
                        }
                        
                        if ((cpCurr == "" && cpPmt != "") || (caCurr == "" && caAmt != "")) {
                            //L140M01a.error34=「{0}」金額已填，幣別不得為空白
                            return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error34"].replace("「{0}」", ""));
                        }
                        
                        $.ajax({
                            handler: inits.fhandle,
                            action: "saveCollect",
                            data: {
                                tabFormMainId: $("#tabFormMainId").val(),
                                L140M01KForm: JSON.stringify(CollectAction.$form.serializeData()),
                                oid: CollectAction.$form.attr("oid")
                            },
                            success: function(data){
                                CollectAction.getDrc(data);
                                $(CollectAction.gridId).trigger('reloadGrid');
                                $.thickbox.close();
                            }
                        }); //close ajax
                    },
                    "close": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        /**
         * 組畫面上的字串
         * @param {Object} obj 收付彙計數描述
         */
        getDrc: function(obj){
            if (obj.CACURR && obj.CACURR != "") {
                $("#CATable").show();
                $("#CACURR").html(obj.CACURR);
                $("#CAAMT").html(obj.CAAMT);
            }
            else {
                $("#CATable").hide();
            }
            if (obj.CPCURR && obj.CPCURR != "") {
                $("#CPTable").show();
                $("#CPCURR").html(obj.CPCURR);
                $("#CPAMT").html(obj.CPAMT);
            }
            else {
                $("#CPTable").hide();
            }
        }
        
    };
    
    //==========================thickbox Code end============================
    
    function newToglePersonBT(callValue, setting, data, rType){//新增連保人
        //先將表格初始化
        $("#L140M01IForm").reset();
        //$("[name=toglePersonType][value=1]").attr("checked", true);
        
        //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
        var thisRType = "";
        if (data && data.rType) {
			thisRType = data.rType;
        }
        else {
			thisRType = rType;
        }
        
        $.ajax({
            handler: "lms1405m01formhandler",
            data: {//把資料轉成json
                formAction: "queryL140m01i",
                oid: (data && data.oid) || "",
                showMsg: true,
                rType: thisRType
            },
            success: function(obj){
                $('#L140M01IForm').injectData(obj);
                
                //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
                if (!$('#L140M01IForm').find("#rType").val()) {
                    //新增時後端不會傳rType上來，故由前端塞值
                    $('#L140M01IForm').find("#rType").val(thisRType);
                }
                $("input[name='toglePersonType']").trigger('change');
                $("#newToglePersonBox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.lms1405s02["L140M01a.conPersonNew"],
                    width: 490,
                    height: 400,
                    modal: true,
                    align: "center",
                    valign: "bottom",
                    readOnly: false,
                    i18n: i18n.def,
                    open: function(){
                    	var guarantorType = $('#guarantorType').val();
                    	if(guarantorType == "3"){ //連帶保證人/一般保證人
                    		$('#ShoeGuarantorTypeItem').show();
                    	}else{
                    		$('#ShoeGuarantorTypeItem').hide();
                    	}
                    },
                    buttons: {
                        "sure": function(){
                            var $L140M01IForm = $("#L140M01IForm");
                            if (!$L140M01IForm.valid()) {
                                return;
                            }
                            
                            // 2012_05_30_建霖說額度批覆表不檢查連保人國別關係
                            if (inits.itemType == "1") {
                                //國別
                                if ($L140M01IForm.find("#rCountry").val() == "") {
                                    //L140M01a.county=國別
                                    return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.county"] + i18n.def['val.required']);
                                }
                                //關係
                                if ($.trim($L140M01IForm.find("#showName").html()) == "") {
                                    //L140M01a.conPersonRation=與借款人關係
                                    return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.conPersonRation"] + i18n.def['val.required']);
                                }
                            }
    
							//J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
							var toglePersonType = $("[name=toglePersonType]:checked").val();
							if(thisRType=="G"){
								//J-103-0299
								 var guaPercentFg = $("input[name='guaPercentFg']:radio:checked").val();
								 var guaPercent =  $L140M01IForm.find("#guaPercent").val();
								 // 保證人是否按一定比率負担保證責任為Y  且 保證人為企業戶才要維護保證人負担保證責任比率
								 //J-105-0100-001 Web e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
								 if(guaPercentFg == "Y" ){  //&& toglePersonType == "2"
								 	if(guaPercent == ""){
										return CommonAPI.showErrorMessage(i18n.lms1405s02["L140M01a.guaPercent"] + i18n.def['val.required']);
									}
								 	
								 }else{
								 	$L140M01IForm.find("#guaPercent").val(100)  ;
								 }
							}else{
								$L140M01IForm.find("#guaPercent").val(100)  ;
							} 
								
                            
                            //判斷新增的是自然人還是法人，再去分要存到哪個table，並且同時儲存到主檔TABLE裡
                            $.ajax({
                                handler: "lms1405m01formhandler",
                                action: "saveL140m01i",
                                data: {//把資料轉成json
                                    type: $("[name=toglePersonType]:checked").val(),
                                    oid: $("#l140m01iFormOid").val(),
                                    tabFormMainId: $("#tabFormMainId").val(),
                                    rType: thisRType,
                                    L140M01IForm: JSON.stringify($("#L140M01IForm").serializeData())
                                },
                                success: function(responseData){
                                    $.thickbox.close();
                                    $('#gridviewNatural').trigger('reloadGrid');
                                    $('#gridviewCorporate').trigger('reloadGrid');
                                }
                            });
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    }
    
    function commonNum_One(data, callback){
        if (typeof(callback) == 'function') {
            callback(data);
        }
        else {
            //L140M01a.error04=請實作方法
            CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error04"]);
        }
    }
    
    /** 
     * 新增授信科目
     * ● type==new：表示 click button 新增
     * ● type==其它：表示點選 grid 的某一列(修改)
     */
    function newItemTypeSelect(type, options, data){
        var val_L120M01A_ratingFlag = _get_val_L120M01A_ratingFlag();
        var passParam = (type == 'new' ? ("new") : ("l140m01c.oid=" + data.oid));
        ilog.debug("newItemTypeSelect(" + val_L120M01A_ratingFlag + ", " + passParam + ")@LMS1405S02Panel02.js");
        //清空oid
        $("#itemOid,#subjDscr").val("").removeAttr("disabled").removeAttr("readonly");
        $("#lmtOther").removeAttr("checked").removeAttr("disabled");
        $("#hidelimit").show(); //hidelimit 為包含 【lmtDays＋天】的 block element
        $("#lmtDays").removeAttr("disabled");
        
        //J-104-0157-001 Web e-Loan 企金授信於選取授信科目為「買入(出)選擇權」、「換入(出)換利交易」及「換入(出)換匯換利交易」時，增加欄位「交易目的」。
        $("#L140M01CForm").reset();
        
        if (type == 'new') {
            //當為新增授信科目，要判斷是否有已登錄科目。
            $.ajax({
                handler: "lms1405m01formhandler",
                data: {//把資料轉成json
                    formAction: "queryItemSelect",
                    tabFormMainId: $("#tabFormMainId").val(),
                    type: 1
                },
                success: function(obj){
                    /*
                     新增時，若已選擇 102
                     於 ajax 回傳的新增科目，就會排除 102
                     */
                    $("#loanTP").setItems({
                        item: obj,
                        space: false,
                        format: "{value} - {key}"
                    });
                    $("#loanTP").removeAttr("readonly").removeAttr("disabled");
//                    $("#loanTP").find("option[value=7031]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=910]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=912]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=913]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=Z12]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=Z13]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=Z14]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=815]").attr("disabled", "disabled");
                    //J-105-0120-001 Web e-Loan 企金授信管理系統額度明細表「遠期外匯及換匯交易保證金」科目調整
//                    $("#loanTP").find("option[value=8151]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=8141]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=711]").attr("disabled", "disabled");
//                    $("#loanTP").find("option[value=811]").attr("disabled", "disabled");
                    
                    //J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
//                    $("#loanTP").find("option[value=1022]").attr("disabled", "disabled");
                    
                    //J-112-0107_05097_B1001 Web e-Loan企金授信額度明細表排除相關科目選取
					if(obj.stopUseSubject){   
						$.each(obj.stopUseSubject, function(i,v){   
							$("#loanTP").find("option[value="+v+"]").prop("disabled", true);
						});
					}
					
                    //J-104-0157-001 Web e-Loan 企金授信於選取授信科目為「買入(出)選擇權」、「換入(出)換利交易」及「換入(出)換匯換利交易」時，增加欄位「交易目的」。
                    $("#loanTP").trigger("change");
                    
                }//close success
            }); //close ajax
        }
        else {
            //為修改授信科目資料，
            $.ajax({
                handler: "lms1405m01formhandler",
                data: {//把資料轉成json
                    formAction: "queryL140m01cByOid",
                    oid: data.oid
                },
                success: function(obj){
                	 if(obj.repaymentSchFmt=="2"){
                     	// class field_l140m01c_lmtDays_lmtOther 包含原本的2個 DOM Element
                     	// 只呈現 lmtDays
                     	// 先隱藏 [ ]詳其他敘做條件 
                         $(".field_l140m01c_lmtDays_lmtOther").hide();
                         $(".field_l140m01c_lmtDays").show();
                         //當清償期限<>授信期間, 隱藏
                         $(".field_l140m01c_lnYear_lnMonth").hide();
                     }else if(obj.repaymentSchFmt=="1"){
                         $(".field_l140m01c_lmtDays_lmtOther").hide();
                         $(".field_l140m01c_lnYear_lnMonth").show();
                     }
                 	//===============
                	if (obj.lmtOther && obj.lmtOther == "1") {
                        $("#lmtOther").trigger("click");
                        $("#hidelimit").hide();
                    }
                    $("#loanTP").prop("readonly", true);
                    $("#loanTP").setItems({
                        space: false,
                        item: obj.item,
                        format: "{value} - {key}",
                        disabled: true
                    });
                    $("#L140M01C").injectData(obj);
                    
                    //J-104-0157-001 Web e-Loan 企金授信於選取授信科目為「買入(出)選擇權」、「換入(出)換利交易」及「換入(出)換匯換利交易」時，增加欄位「交易目的」。
                    $("#L140M01CForm").find("[name='subjPurpose'][value='" + obj.subjPurpose + "']:radio").prop("checked", true); //塞值
                    $("#loanTP").trigger("change");
                    
                }//close success
            }); //close ajax
        }
        
        var tb_height = 250;
        if (val_L120M01A_ratingFlag == "JP" || val_L120M01A_ratingFlag == "AU" 
        		|| val_L120M01A_ratingFlag == "TH") {
            tb_height += 100;
            $("tr.field_l140m01c_c121Relate").show();
            if (true) {
                var _l140m01a_property_val = $.trim($("#proPerty").val() || '');
                if (_l140m01a_property_val == '' ||
                _l140m01a_property_val == '7' ||
                _l140m01a_property_val == '8') {
                    $("tr.field_l140m01c_c121Relate").find("button#loginGradeBt").hide();
                }
                else {
                    $("tr.field_l140m01c_c121Relate").find("button#loginGradeBt").show();
                }
            }
        }
        else {
            $("tr.field_l140m01c_c121Relate").hide();
        }
        
        if (val_L120M01A_ratingFlag == "JP" || val_L120M01A_ratingFlag == "AU" 
        		|| val_L120M01A_ratingFlag == "TH") {
            $(".field_l140m01c_lmtDays_lmtOther").hide();
            $(".field_l140m01c_lnYear_lnMonth").show();
        }
        else {
            $(".field_l140m01c_lmtDays_lmtOther").show();
            $(".field_l140m01c_lnYear_lnMonth").hide();
        }
        
        $("#newitemTypeBox").thickbox({
            //title.13=新增授信科目
            title: "",
            width: 640,
            height: tb_height,
            modal: true,
            readOnly: false,
            align: "center",
            i18n: i18n.def,
            valign: "bottom",
            buttons: {
                "sure": function(){//在確定的時候儲存gridviewitemType 的授信科目
                    var $form = $("#L140M01CForm");
                    if (!$form.valid()) {
                        return false;
                    }
                    
                    var loanTp = $form.find("#loanTP").val();
                    
                    //J-105-0172-001 Web e-Loan企金授信額度明細表短期授信科目增加檢查清償期限不可超過365日
                    /*
                     * 改於LMS1401M01FormHandler.java檢核
                     if(loanTp){
                     if (loanTp.substring(0, 1) == "1" || loanTp.substring(0, 1) == "2") {
                     var lmtOther = $('input:checkbox:checked[name="lmtOther"]').val();
                     if (lmtOther != "1") {
                     var lmtDays = $("#lmtDays").val();
                     if (lmtDays) {
                     if (lmtDays > 365) {
                     //L140M01a.message176=短期授信科目清償期限不可超過365日。
                     return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.message176"]);
                     }
                     }
                     
                     }
                     }
                     }
                     */
                    $.ajax({
                        handler: "lms1405m01formhandler",
                        data: {//把資料轉成json
                            formAction: "saveL140m01c",
                            tabFormId: $("#tabFormId").val(),
                            oid: $("#itemOid").val(),
                            tabFormMainId: $("#tabFormMainId").val(),
                            L140M01BForm: JSON.stringify($("#L140M01BForm").serializeData()),
                            L140M01CForm: JSON.stringify($("#L140M01CForm").serializeData()),
                            showMsg: true
                        },
                        success: function(data){
                            if (data.isDerivatives) {
                                $("#isDerivatives").val(data.isDerivatives);
                            }
                            $.thickbox.close();
                            $('#gridviewitemType').trigger('reloadGrid');
                            //J-105-0250-001  Web e-Loan 新增利害關係人檢核
                            CntrNoAPI.chkIsNeedUnsecureFlag();
                            
                            //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
		                    CntrNoAPI.chkIsNoneHedge();

		                    CntrNoAPI.chkIsNeedDerivEval();
                        }
                    }); //close ajax
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    /**   
     * 選擇DBU OBU 視窗
     * @param {String } brId 輸入的分行代碼
     */
    function selectDbuOrObu(brId){
        $("[name=DBUorOBURadio][value=1]").prop("checked", true);
        $("#selectDbuOrObuBox").thickbox({
            title: "",
            width: 150,
            height: 100,
            modal: true,
            readOnly: false,
            align: "center",
            i18n: i18n.def,
            valign: "bottom",
            buttons: {
                "sure": function(){
                    //seletValue DBU=1 ,OBU=4
                    var seletValue = $("[name=DBUorOBURadio]:checked").val();
                    if (!seletValue || seletValue == "") {
                        //grid.selrow=請先選擇一筆資料。
                        return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                    }
                    $("#cntrNo").val(CntrNoAPI.newCntrNo(brId, seletValue));
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    /**   
     * 顯示變更前內容
     */
    function showBefore(){
        var $BFcontent = $("#BFcontent");
        $BFcontent.find("span").html("");
        $.ajax({
            handler: "lms1405m01formhandler",
            action: "queryBeforeContent",
            data: {//把資料轉成json
                tabFormMainId: $("#tabFormMainId").val()
            },
            success: function(data){
                $BFcontent.injectData(data);
                if (data.headItem1BF == "Y") {
                    $("#BFheadItem1Tr").show();
                }
                else {
                    $("#BFheadItem1Tr").hide();
                }
                $("#showBeforeBox").thickbox({
                    title: "",
                    width: 600,
                    height: 400,
                    modal: true,
                    readOnly: false,
                    i18n: i18n.def,
                    buttons: {
                        "close": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    }
    /**
     * 平均動用率相關動作
     */
    var UseParAction = {
        grid: null,
        isLoad: false,
        init: function(){
        
            if (this.isLoad) {
                this._reloadGrid();
                return false;
            }
            
            
            this.isLoad = true;
            this.grid = $("#selectUseParGridview").iGrid({
                handler: inits.ghandle,
                height: 175,
                action: "queryCesInfo",
                postData: {
                    tabFormMainId: $("#tabFormMainId").val(),
                    mainId: $("#mainId").val()
                },
                rownumbers: true,
                colModel: [{
                    colHeader: i18n.lms1405s02["L140M01a.message100"],//"最後更新日期"
                    align: "left",
                    width: 40,
                    sortable: false,
                    name: 'DATADATE'
                }, {
                    colHeader: i18n.lms1405s02["L140M01a.message101"],//"資信簡表編號",
                    align: "left",
                    width: 100,
                    sortable: false,
                    name: 'SN'
                }, {
                    colHeader: i18n.lms1405s02["L140M01a.docStatus"],//"文件狀態",
                    align: "left",
                    width: 30,
                    sortable: false,
                    name: 'DOCSTATUS'
                }, {
                    colHeader: i18n.lms1405s02["L140M01a.message97"],//資料基準日
                    align: "left",
                    width: 40,
                    sortable: false,
                    name: 'UPDATETIME'
                }, {
                    colHeader: i18n.lms1405s02["L140M01a.message87"],// "平均動用率",
                    align: "right",
                    width: 30,
                    sortable: false,
                    name: 'RATIO'
                }]
            });
        },
        /**
         *更新grid
         */
        _reloadGrid: function(){
            this.grid.jqGrid("setGridParam", {
                postData: {
                    tabFormMainId: $("#tabFormMainId").val()
                },
                page: 1,
                search: true
            }).trigger("reloadGrid");
        },
        /**
         * 引進平均動用率(資簡)
         */
        inculeUsePar: function(){
            var cntrno = $.trim($("#L140M01AForm2").find("#cntrNo").val());
            if (cntrno == "") {
                //L140M01a.message71=請先登錄現請額度與額度序號並儲存後，在執行此動作。
                return API.showMessage(i18n.lms1405s02["L140M01a.message71"]);
            }
            this.init();
            this.openBox();
            
        },
        /**
         * 引進平均動用率(帳務)
         */
        inculeUseParAloan: function(){
            var cntrno = $.trim($("#L140M01AForm2").find("#cntrNo").val());
            if (cntrno == "") {
                //L140M01a.message71=請先登錄現請額度與額度序號並儲存後，在執行此動作。
                return API.showMessage(i18n.lms1405s02["L140M01a.message71"]);
            }
            
            //K-105-0002  Web e-Loan 額度明細表修改供應鏈融資業務簽案規則
            $("#queryUseParAloanInputCntrNo").val(cntrno);
            
            $("#divQueryUseParAloanInputCntrNo").thickbox({
                title: i18n.lms1405s02["title.12"],
                width: 100,
                height: 100,
                modal: true,
                readOnly: false,
                i18n: i18n.def,
                align: "center",
                valign: "bottom",
                buttons: {
                    "sure": function(){
                        var str = $("#queryUseParAloanInputCntrNo").val();
                        if (str) {
                            if (!str.match(/\w{12}/)) {
                                //L140M01a.message68=額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)
                                return CommonAPI.showMessage(i18n.lms1405s02["L140M01a.message68"]);
                            }
                            cntrno = str;
                            
                            $.ajax({
                                handler: inits.fhandle,
                                action: "queryUseParAloan",
                                data: {
                                    tabFormMainId: $("#tabFormMainId").val(),
                                    cntrno: cntrno
                                },
                                success: function(data){
                                    if (data.hasValue == "Y") {
                                        $("#usePar").val(data.RATIO);
                                        $("#useParDate").val(data.UPDATETIME);
                                    }
                                    
                                }
                            });
                            $.thickbox.close();
                        }
                        else {
                            $.thickbox.close();
                            return;
                        }
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
            
            
        },
        /**
         * 選擇平均動用率視窗
         */
        openBox: function(){
            $("#selectUseParBox").thickbox({
                title: i18n.lms1405s02["L140M01a.message87"],// "平均動用率",
                width: 600,
                height: 350,
                modal: true,
                readOnly: false,
                i18n: i18n.def,
                align: "center",
                valign: "bottom",
                buttons: {
                    "sure": function(){
                        var girdId = UseParAction.grid.getGridParam('selrow');
                        if (!girdId) {
                            //grid_selector=請選擇資料
                            return API.showMessage(i18n.def["grid_selector"]);
                        }
                        var data = UseParAction.grid.getRowData(girdId);
                        $.ajax({
                            handler: inits.fhandle,
                            action: "saveUsePar",
                            data: {
                                tabFormMainId: $("#tabFormMainId").val(),
                                ratio: data.RATIO,
                                updatetime: data.UPDATETIME
                            },
                            success: function(obj){
                                $("#usePar").val(data.RATIO);
                                $("#useParDate").val(data.UPDATETIME);
                                $.thickbox.close();
                            }
                        });
                        
                        
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        }
    };
    
    /**
     * 共同借款人相關動作
     */
    var CommonBorrowerAction = {
        _isLoad: false,
        grid: null,
        formId: "L140M01JForm",
        selectGrid: null,
        getCustId: function(){
            return $("#L140M01AForm1").find("#custId").html()
        },
        getdupNo: function(){
            return $("#L140M01AForm1").find("#dupNo").html()
        },
        /**
         * 重新引進grid
         */
        _initGrid: function(){
            this.grid = $("#commonBorrowerGridVeiw").iGrid({
                height: 200,
                handler: inits.ghandle,
                sortname: 'createTime',
                sortorder: 'asc',
                action: "queryL140M01J",
                postData: {
                    tabFormMainId: $("#tabFormMainId").val(),
                    mainId: $("#mainId").val()
                },
                multiselect: true,
                hideMultiselect: false,
                colModel: [{
                    colHeader: i18n.def["compID"],// 統一編號",
                    width: 80,
                    name: 'custId',
                    sortable: true,
                    formatter: 'click',
                    onclick: CommonBorrowerAction.openEditBorrowerBox
                }, {
                    colHeader: i18n.def["compName"],// 姓名/名稱,
                    name: 'custName',
                    width: 100,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02["L140M01a.county"],//國別,
                    name: 'ntCode',
                    width: 80,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02["L140M01a.conPersonRation"],//與借款人關係,
                    name: 'custRlt',
                    width: 100,
                    sortable: true,
                    align: "center"
                }, {
                    colHeader: "oid",
                    name: 'oid',
                    hidden: true
                }],
                ondblClickRow: function(rowid){
                    var data = CommonBorrowerAction.grid.getRowData(rowid);
                    CommonBorrowerAction.openEditBorrowerBox(null, null, data);
                }
            });
            
            /**
             * L120S01A借款人
             */
            this.selectGrid = $("#selectBorrowerGridVeiw").iGrid({
                height: 200,
                handler: inits.ghandle,
                sortname: 'createTime',
                sortorder: 'asc',
                action: "queryL120S01A",
                postData: {
                    mainId: $("#mainId").val(),
                    custId: CommonBorrowerAction.getCustId(),
                    dupNo: CommonBorrowerAction.getdupNo()
                },
                multiselect: true,
                hideMultiselect: false,
                colModel: [{
                    colHeader: i18n.def["compID"],// 統一編號",
                    width: 80,
                    name: 'custId',
                    sortable: true
                }, {
                    colHeader: i18n.def["compName"],// 姓名/名稱,
                    name: 'custName',
                    width: 100,
                    sortable: true
                }, {
                    colHeader: i18n.lms1405s02["L140M01a.county"],//國別,
                    name: 'custPos',
                    width: 80,
                    sortable: true
                }, {
                    colHeader: "oid",
                    name: 'oid',
                    hidden: true
                }]
            });
        },
        /**
         *更新grid
         */
        _reloadGrid: function(){
            this.grid.jqGrid("setGridParam", {
                postData: {
                    tabFormMainId: $("#tabFormMainId").val()
                },
                page: 1,
                search: true
            }).trigger("reloadGrid");
            
        },
        /**
         *更新查詢簽報書借款人grid
         */
        _reloadSrcGrid: function(){
            this.selectGrid.jqGrid("setGridParam", {
                postData: {
                    mainId: $("#mainId").val(),
                    custId: CommonBorrowerAction.getCustId(),
                    dupNo: CommonBorrowerAction.getdupNo()
                },
                page: 1,
                search: true
            }).trigger("reloadGrid");
            
        },
        _init: function(){
            if (!this._isLoad) {
                this._initGrid();
                this._isLoad = true;
                $("#L140M01JPeopleBT").click(function(){
                    var todo = function(data){
                        var $form = $("#" + CommonBorrowerAction.formId);
                        $form.find('#L140M01J_showName').html(data.context);
                        $form.find('#L140M01J_custRlt').val(data.rKindD);
                    };
                    relationshipBT(todo);
                });
            }
            else {
                this._reloadGrid();
            }
        },
        /**
         * 開啟共同借款人Grid視窗
         */
        openCommonBorrower: function(){
            this._init();
            $("#commonBorrowerBox").thickbox({
                title: i18n.lms1405s02['L140M01a.message86'],
                width: 800,
                height: 350,
                modal: true,
                readOnly: false,
                i18n: i18n.def,
                buttons: {
                    "reQuery": function(){
                        CommonBorrowerAction.openCommonBorrowerBox();
                    },
                    "del": function(){
                        CommonBorrowerAction.deleteCommonBorrower();
                    },
                    "close": function(){
                    
                        var count = CommonBorrowerAction.grid.jqGrid('getGridParam', 'records');
                        
                        if (count == 0) {
                        
                            CommonBorrowerAction.setCommonBorrower(i18n.lms1405s02['nohave']);
                            
                            $.ajax({
                                handler: inits.fhandle,
                                action: "saveL140m01aJstr",
                                data: {
                                    tabFormMainId: $("#tabFormMainId").val(),
                                    l140m01jStr: $("#l140m01jStr").html()
                                },
                                success: function(obj){
                                
                                }
                            });
                        }
                        
                        $.thickbox.close();
                    }
                }
            });
        },
        /**
         *
         維護共用借款人關係
         * @param {Object} cellvalue
         * @param {Object} options
         * @param {Object} data 欄位資料
         */
        openEditBorrowerBox: function(cellvalue, options, data){
            var $form = $("#" + CommonBorrowerAction.formId);
            $form.reset();
            $.ajax({
                handler: inits.fhandle,
                formId: "empty",
                action: "queryL140M01J",
                data: {
                    oid: data.oid
                },
                success: function(obj){
                    $form.injectData(obj);
                    $("#L140M01JPeopleBox").thickbox({
                        title: i18n.lms1405s02['L140M01a.message86'],
                        width: 400,
                        height: 270,
                        modal: true,
                        align: "center",
                        valign: "bottom",
                        readOnly: _openerLockDoc == "1",
                        i18n: i18n.def,
                        buttons: {
                            "sure": function(){
                                $.ajax({
                                    handler: inits.fhandle,
                                    formId: "empty",
                                    action: "saveL140M01J",
                                    data: {
                                        oid: data.oid,
                                        tabFormMainId: $("#tabFormMainId").val(),
                                        custRlt: $form.find("#L140M01J_custRlt").val()
                                    },
                                    success: function(obj){
                                        CommonBorrowerAction._reloadGrid();
                                        //saveSuccess=儲存成功
                                        CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
                                            if (b) {
                                                $.thickbox.close();
                                            }
                                        });
                                    }
                                });
                            },
                            "cancel": function(){
                                $.thickbox.close();
                            }
                        }
                    });
                }
            });
        },
        /**
         * 開啟選擇簽報書借款人視窗
         * @param {Object} callValue grid 顯示
         * @param {Object} setting grid　設定
         * @param {Object} data 欄位資料
         */
        openCommonBorrowerBox: function(callValue, setting, data){
            this._reloadSrcGrid();
            
            $("#selectBorrowerBox").thickbox({
                title: i18n.lms1405s02['title.06'],//title.06=借款人選擇
                width: 600,
                height: 350,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: false,
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var gridIDs = CommonBorrowerAction.selectGrid.getGridParam('selarrrow');
                        if (gridIDs == "") {
                            //TMMDeleteError=請先選擇需修改(刪除)之資料列
                            API.showMessage(i18n.def["action_005"]);
                            return false;
                        }
                        
                        var gridsOid = [];
                        for (var i = 0, total = gridIDs.length; i < total; i++) {
                            gridsOid[i] = CommonBorrowerAction.selectGrid.getRowData(gridIDs[i]).oid;
                        }
                        $.ajax({
                            handler: inits.fhandle,
                            action: "inclueL140M01J",
                            data: {
                                tabFormMainId: $("#tabFormMainId").val(),
                                oids: gridsOid
                            },
                            success: function(obj){
                                CommonBorrowerAction._reloadGrid();
                                CommonBorrowerAction.setCommonBorrower(obj.desc);
                                CommonBorrowerAction.selectGrid.resetSelection();
                                //J-105-0250-001  Web e-Loan 新增利害關係人檢核
                                CntrNoAPI.chkIsNeedUnsecureFlag();
                                $.thickbox.close();
                            }
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        /**
         *刪除共同借款人
         */
        deleteCommonBorrower: function(){
            var gridIDs = CommonBorrowerAction.grid.getGridParam('selarrrow');
            if (gridIDs == "") {
                //TMMDeleteError=請先選擇需修改(刪除)之資料列
                API.showMessage(i18n.def["TMMDeleteError"]);
                return false;
            }
            
            //confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    var gridsOid = [];
                    for (var i = 0, total = gridIDs.length; i < total; i++) {
                        gridsOid[i] = CommonBorrowerAction.grid.getRowData(gridIDs[i]).oid;
                    }
                    $.ajax({
                        handler: inits.fhandle,
                        action: "deleteL140M01J",
                        data: {
                            tabFormMainId: $("#tabFormMainId").val(),
                            oids: gridsOid
                        },
                        success: function(obj){
                            CommonBorrowerAction._reloadGrid();
                            CommonBorrowerAction.setCommonBorrower(obj.desc);
                            //J-105-0250-001  Web e-Loan 新增利害關係人檢核
                            CntrNoAPI.chkIsNeedUnsecureFlag();
                        }
                    });
                }
            });
        },
        /**
         * 設定字串
         * @param {Object} str
         */
        setCommonBorrower: function(str){
            $("#l140m01jStr").html(str);
        }
        
    };
    
    RealEstateAction._init();
	IntRegAction._init();		// J-108-0293
	thaiContingentType.init(); //G-113-0145 是否顯示泰國相關欄位
});


//向上或向下移動
function upDownBox(upDown){
    //$("input[name='upDown']").removeAttr("checked");
    var rows = $("#gridviewitemType").getGridParam('selarrrow');
    if (!rows || rows == "") {
        return CommonAPI.showMessage(i18n.def["grid.selrow"])
    }
    
    if (rows.length > 1) {
        return CommonAPI.showMessage(i18n.def["grid.selrow"])
    }
    
    var list = "";
    var data;
    if (rows != 'undefined' && rows != null) {
        data = $("#gridviewitemType").getRowData(rows);
        list = data.oid;
    }
    
    upOrDownF(data, upDown);
}

function upOrDownF(data, upDown){

    $.ajax({
        type: "POST",
        handler: "lms1405m01formhandler",
        data: {
            formAction: "changeDeSeqNum",
            detailOid: data.oid,
            tabFormMainId: $("#tabFormMainId").val(),
            kind: data.KINDHIDE,
            upOrDown: upDown
        },
        success: function(responseData){
            $('#gridviewitemType').jqGrid('setGridParam', {
                loadComplete: function(){
                    $("#gridviewitemType").setSelection(responseData.newSelect);
                    $('#gridviewitemType').jqGrid('setGridParam', {
                        loadComplete: function(){
                            //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
                        }
                    })
                }
            });
            $("#gridviewitemType").trigger("reloadGrid");//.setSelection(1,true);
            //調整科目順序後要重新組利費率的字 
            $.ajax({
                handler: inits.fhandle,
                data: {//把資料轉成json
                    formAction: "saveDscr2",//儲存所有利費率描述
                    tabFormMainId: $("#tabFormMainId").val(),
                    pageNum: $("#pageNum2").val(),
                    showMsg: true
                },
                success: function(responseData){
                    $("#itemDscr2").val(responseData.str);
                    
                }
            }); //close ajax 
        }
    });
}

//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
/**  登錄衍生性金融商品期數  */
function selectCountryBt(type){
  
	var fieldName = "";
	if(type == 1){
		fieldName = "noFactCountry";
	}else{
		fieldName = "freezeFactCountry";
	}
	
	var item = API.loadCombos("lms120_"+fieldName)["lms120_"+fieldName];
	$("#badCountry").setItems({
		size: "1",
        item: item,
		clear : true,
		itemType: 'checkbox' 
    });
	 
   
   $("[name=badCountry]").removeAttr("disabled").removeAttr("checked");
   //$('input:checkbox[name="_noFactCountry"]').attr('disabled', true);
   

   $('input:checkbox[name="badCountry"]').prop('disabled', false);
   $('input:checkbox[name="badCountry"]').show();

	
	 
    var countryVal = $("#"+fieldName).val();
    if (countryVal != "") {
        var vals = countryVal.split("|");
     
        for (var i in vals) {
           $("[name=badCountry][value=" + vals[i] + "]").prop("checked", true);
        }
    }
    
    $("#loginNoFactCountry").thickbox({
        //L140M01a.select=選取關鍵字
        title: type==1 ? i18n.lms1405s02["l120m01i.noFactCountry"] : i18n.lms1405s02["l120m01i.freezeFactCountry"],
        width: 350,
        height: 300,
        modal: true,
        align: "center",
        valign: "bottom",
        readOnly: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                $.thickbox.close();
                var allCheacked = [];
                var allCheackedVal = [];
                $.each($("#loginNoFactCountry :checkbox[name=badCountry]:checked"), function(i, n){
                	if(type == 1){
                		allCheacked[i] = i18n.lms120_noFactCountry[$(n).val()];  
                	}else{
                		allCheacked[i] = i18n.lms120_freezeFactCountry[$(n).val()];  
                	}
                    
                    allCheackedVal[i] = $(n).val();
                });

                if(type == 1){
                	$("#noFactCountryShow").val(allCheacked);
                    $("#noFactCountry").val(allCheackedVal.join("|"));
                }else{
                	$("#freezeFactCountryShow").val(allCheacked);
                    $("#freezeFactCountry").val(allCheackedVal.join("|"));
                }
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}

//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
/**  登錄是否屬凍結額度國家  */
function selectCountryBt2(){
  
	 var fieldName = "freezeFactCountry";
	 
	
	 $.ajax({
			type : "POST",
			handler : "lms1405m01formhandler",
			action : "getFreezeFactCountry",
			data : 
			{
				mainId : $("#tabFormMainId").val() 
			},				
			success:function(responseData){
				$("#badCountry").setItems({
					size : 1,
	                item: responseData.freezeFactCountry,
	                clear : true,
	                itemType: 'checkbox' 
				});

			   $("[name=badCountry]").removeAttr("disabled").removeAttr("checked");
			   //$('input:checkbox[name="_noFactCountry"]').attr('disabled', true);
			   

			   $('input:checkbox[name="badCountry"]').prop('disabled', false);
			   $('input:checkbox[name="badCountry"]').show();

				
				 
			    var countryVal = $("#"+fieldName).val();
			    if (countryVal != "") {
			        var vals = countryVal.split("|");
			     
			        for (var i in vals) {
			           $("[name=badCountry][value=" + vals[i] + "]").prop("checked", true);
			        }
			    }
			    
			    $("#loginNoFactCountry").thickbox({
			        //L140M01a.select=選取關鍵字
			        title:  i18n.lms1405s02["l120m01i.freezeFactCountry"],
			        width: 350,
			        height: 300,
			        modal: true,
			        align: "center",
			        valign: "bottom",
			        readOnly: false,
			        i18n: i18n.def,
			        buttons: {
			            "sure": function(){
			                $.thickbox.close();
			                var allCheacked = []; 
			                var allCheackedVal = [];
			                $.each($("#loginNoFactCountry :checkbox[name=badCountry]:checked"), function(i, n){
			                	allCheacked[i] = $(this).parent().text() ;  
			                    allCheackedVal[i] = $(n).val();
			                });

			                $("#freezeFactCountryShow").val(allCheacked);
		                    $("#freezeFactCountry").val(allCheackedVal.join("|"));
			            },
			            "cancel": function(){
			                $.thickbox.close();
			            }
			        }
			    });

				
			}
		});		
	 
	 
	
}


//J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
/** 設定信用品質順序設定  */
function setGuarantorSeq(){ 
	 
	//J-110-0007_05097_B1002 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	$("#gridViewGuarantorSeq").jqGrid("setGridParam", {
		postData : {
			handler : 'lms1405gridhandler',
			formAction : "queryL140m01i",
			mainId : $("#tabFormMainId").val(),
            type: "2"
		},
		search : true
	}).trigger("reloadGrid");
	
    $("#setGuarantorSeqThickBox").thickbox({
    	title: i18n.lms1405s02['btn.setGuarantorCreditPriority'],  //信用品質順序設定
        width: 900,
        height: 500,
        modal: true,
        i18n: i18n.lms1405s02,
        buttons: API.createJSON([{
            key: i18n.lms1405s02['btn.writeCase'],
            value: function(){
                var $gridviewprint = $("#gridViewGuarantorSeq");
                //寫回額度明細表
                $gridviewprint.jqGrid('saveRow', lastSel, false, 'clientArray');
                 
                
                var ids = $gridviewprint.jqGrid('getDataIDs');
                //用來放列印順序跟oid
                var json = {};
                var checkArray = $gridviewprint.getCol("priority");
                
                //檢查列印順序值是否重複
                if (checkArrayRepeat(checkArray)) {
                    //L140M01a.message267=順序不可重複
                    return CommonAPI.showMessage(i18n.lms1405s02['L140M01a.message267']);
                }
                
                for (var id in ids) {
                	
                    var data = $gridviewprint.jqGrid('getRowData', ids[id]);
                    json[data.oid] = data.priority;
                    
                }
                FormAction.open = true;
                $.ajax({
                    handler: inits.fhandle,
                    action: "savePriority",
                    data: {
                        mainId: $("#mainId").val(),
                        tabFormMainId: $("#tabFormMainId").val(),
                        data: JSON.stringify(json),
                        guaPercentFg : $("input[name='guaPercentFg']:radio:checked").val()
                    },
                    success: function(obj){
                        FormAction.open = false;
                        $.thickbox.close();
                        $("#gridviewCorporate").trigger('reloadGrid');
                    }
                });
            }
        }, {
            key: i18n.lms1405s02['btn.applyGuarantorCreditOrder'],  //取得保證人信評順序
            value: function(){
            	var $gridviewprint = $("#gridViewGuarantorSeq");
                //取得保證人信評順序
                $gridviewprint.jqGrid('saveRow', lastSel, false, 'clientArray');
 
                var ids = $gridviewprint.jqGrid('getDataIDs');
                //用來放列印順序跟oid
                var json = {};
                
                for (var id in ids) {           	
                    var data = $gridviewprint.jqGrid('getRowData', ids[id]);
                    json[data.oid] = id;
                }
                FormAction.open = true;
                $.ajax({
                    handler: inits.fhandle,
                    action: "getGuarantorCreditPriority",
                    data: {
                        mainId: $("#mainId").val(),
                        tabFormMainId: $("#tabFormMainId").val(),
                        data: JSON.stringify(json)
                    },
                    success: function(obj){
                    	if(obj.NODATA == "Y"){
                    		return CommonAPI.showErrorMessage(i18n.def["noData"]);
                    	}else{
                    		for (var id in ids) {
                            	var x= parseInt(id)+1 ;
                            	$("#gridViewGuarantorSeq").jqGrid('setRowData', x, {priority:obj[id]});     
                        		//$("#gridViewGuarantorSeq").jqGrid('setRowData', 1, {priority:10});
                            }
                    	}
                    	
                        //$gridviewprint.trigger('reloadGrid');
                    }
                });
            }
        }, {
            key: i18n.lms1405s02['btn.applyEllngteePriority'],   //引進主從債務人建檔資料
            value: function(){
            	var $gridviewprint = $("#gridViewGuarantorSeq");
                //取得保證人信評順序
                $gridviewprint.jqGrid('saveRow', lastSel, false, 'clientArray');
 
                var ids = $gridviewprint.jqGrid('getDataIDs');
                //用來放列印順序跟oid
                var json = {};
                
                for (var id in ids) {           	
                    var data = $gridviewprint.jqGrid('getRowData', ids[id]);
                    json[data.oid] = id;
                }
                FormAction.open = true;
                $.ajax({
                    handler: "lms1405m01formhandler",
                    action: "applyEllngteePriority",
                    data: {
                        mainId: $("#mainId").val(),
                        tabFormMainId: $("#tabFormMainId").val(),
                        data: JSON.stringify(json)
                    },
                    success: function(obj){

                    	for (var id in ids) {
                        	//alert("id="+id+"，obj[id]="+obj[id]);
                        	var x= parseInt(id)+1 ;
                        	//alert("x="+x+"，"+"id="+id+"，obj[id]="+obj[id]);
                        	
                    		$("#gridViewGuarantorSeq").jqGrid('setRowData', x, {priority:obj[id]});     

                    		//$("#gridViewGuarantorSeq").jqGrid('setRowData', 1, {priority:10});
                        }

                        //$gridviewprint.trigger('reloadGrid');
                    }
                });
            }
        }, {
            key: i18n.def['close'],
            value: function(){
                $.thickbox.close();
            }
        }])
    });
    
	 

}

/** 檢查陣列內容是否重複 */
function checkArrayRepeat(arrVal){
    var newArray = [];
    for (var i = arrVal.length; i--;) {
        var val = arrVal[i];
        if($.trim(val) != "" ){
        	if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        
    }
    return false;
}

/**
 *是否整批貸款
 */
var $packLoanTb = $("#packLoanTb");
$("input[name=packLoan]").click(function(){
    if ($(this).val() == "Y") {
        $packLoanTb.show();
    }
    else {
        $packLoanTb.hide();
        $packLoanTb.find("span.field").html("");
        $('#parentCntrNo').val("")// 清掉select選項
    }
});

/**
 *取得總戶資訊
 */
$("#parentCntrNo").change(function(){
    var $form = $("#L140M01AForm1");
    var parentCntrNo = $("#parentCntrNo").val();
    if( parentCntrNo ){
    	$.ajax({
    		handler: inits.fhandle,
    		action: "getParentInfo",
    		async:false,
    		data: {
    			parentCntrNo: parentCntrNo,
    			tabFormId: $("#tabFormMainId").val()// 額度明細表的mainId
    		},
    		success: function(obj){
    			if (obj.msg) {
    				API.showMessage(obj.msg);
    			}
    			else {
    				if (obj.size == "1") {
    					$form.injectData(obj);
    				} else {
    					// 沒撈到清空就好
    					$packLoanTb.find("span.field").html("");
    					$packLoanTb.find("input").val("");
    				}
    			}
    		}
    	});
    }else{
    	// 請選擇清空就好
		$packLoanTb.find("span.field").html("");
		$packLoanTb.find("input").val("");
    }
});



//G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
//登錄綠色支出類別  
function esgGreenSpendTypeBT(){

	var item = API.loadCombos("lms140_esgGreenSpendType")["lms140_esgGreenSpendType"];
	$("#_esgGreenSpendType").setItems({
		size: "1",
	 item: item,
		clear : true,
		itemType: 'checkbox' 
	 });
		 
	$("[name=_esgGreenSpendType]").removeAttr("disabled").removeAttr("checked");
	$('input:checkbox[name="_esgGreenSpendType"]').prop('disabled', false);
	$('input:checkbox[name="_esgGreenSpendType"]').show();

	 var esgGreenSpendType = $("#esgGreenSpendType").val();
	 if (esgGreenSpendType != "") {
	     var vals = esgGreenSpendType.split("|");
	  
	     for (var i in vals) {
	        $("[name=_esgGreenSpendType][value=" + vals[i] + "]").prop("checked", true);
	     }
	 }
	 
	  
	 $("#esgGreenSpendTypeThickBox").thickbox({
	     //L140M01a.select=選取關鍵字
	     title: i18n.lms1405s02["L140M01a.esgGreenSpendType"],
	     width: 400,
	     height: 300,
	     modal: true,
	     align: "center",
	     valign: "bottom",
	     readOnly: false,
	     i18n: i18n.def,
	     buttons: {
	         "sure": function(){
	             $.thickbox.close();
	             var allCheacked = [];
	             var allCheackedVal = [];
	             $.each($("#esgGreenSpendTypeThickBox :checkbox[name=_esgGreenSpendType]:checked"), function(i, n){
	             	allCheacked[i] = i18n.lms140_esgGreenSpendType[$(n).val()];  
	                 allCheackedVal[i] = $(n).val();
	                  
	             });
	             
	//             if(jQuery.inArray("A", allCheackedVal) !== -1){ 
	//             	$("#showEsgGreenSpendTypeA").show();
	//             }else{
	//             	$("#showEsgGreenSpendTypeA").hide();
	//             }
	//             
	//             if(jQuery.inArray("Z", allCheackedVal) !== -1){ 
	//             	$("#showEsgGreenSpendTypeZ").show();
	//             }else{
	//             	$("#showEsgGreenSpendTypeZ").hide();
	//             }
	             
	
	             $("#esgGreenSpendTypeShow").val(allCheacked);
	             $("#esgGreenSpendType").val(allCheackedVal.join("|"));
	             
	             $("#esgGreenSpendType").trigger('change');
	         },
	         "cancel": function(){
	             $.thickbox.close();
	         }
	     }
	 });
}

//G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
//登錄綠色支出類別  
function esgSustainLoanTypeBT(){

	var item = API.loadCombos("lms140_esgSustainLoanType")["lms140_esgSustainLoanType"];
	$("#_esgSustainLoanType").setItems({
		 size: "1",
	     item: item,
		 clear : true,
		 itemType: 'checkbox' 
	 });
		 	
	$("[name=_esgSustainLoanType]").removeAttr("disabled").removeAttr("checked");
	$('input:checkbox[name="_esgSustainLoanType"]').prop('disabled', false);
	$('input:checkbox[name="_esgSustainLoanType"]').show();

	 var esgGreenSpendType = $("#esgSustainLoanType").val();
	 if (esgGreenSpendType != "") {
	     var vals = esgGreenSpendType.split("|");
	  
	     for (var i in vals) {
	        $("[name=_esgSustainLoanType][value=" + vals[i] + "]").prop("checked", true);
	     }
	 }
	 
	 $("#esgSustainLoanTypeThickBox").thickbox({
	     //L140M01a.select=選取關鍵字
	     title: i18n.lms1405s02["L140M01a.esgSustainLoanType"],
	     width: 300,
	     height: 200,
	     modal: true,
	     align: "center",
	     valign: "bottom",
	     readOnly: false,
	     i18n: i18n.def,
	     buttons: {
	         "sure": function(){
	             $.thickbox.close();
	             var allCheacked = [];
	             var allCheackedVal = [];
	             $.each($("#esgSustainLoanTypeThickBox :checkbox[name=_esgSustainLoanType]:checked"), function(i, n){
	             	allCheacked[i] = i18n.lms140_esgSustainLoanType[$(n).val()];  
	                 allCheackedVal[i] = $(n).val();	                  
	             });
	
	             $("#esgSustainLoanTypeShow").val(allCheacked);
	             $("#esgSustainLoanType").val(allCheackedVal.join("|"));
	         },
	         "cancel": function(){
	             $.thickbox.close();
	         }
	     }
	 });
}

//J-113-0329 企金授信新增社會責任授信
//登錄社會責任專案類別
function socialKindBT(){

	var item = API.loadCombos("lms140_socialKind")["lms140_socialKind"];
	$("#_socialKind").setItems({
		 size: "1",
	     item: item,
		 clear : true,
		 itemType: 'radio' 
	 });
		 	
	$("[name=_socialKind]").removeAttr("disabled").removeAttr("checked");
	$('input:radio[name="_socialKind"]').prop('disabled', false);
	$('input:radio[name="_socialKind"]').show();

	 var socialKind = $("#socialKind").val();
	 if (socialKind != "") {
	     var vals = socialKind.split("|");
	  
	     for (var i in vals) {
	        $("[name=_socialKind][value=" + vals[i] + "]").prop("checked", true);
	     }
	 }
	 
	 $("#socialKindThickBox").thickbox({
	     title: i18n.lms1405s02["L140M01A.socialKind"],
	     width: 300,
	     height: 200,
	     modal: true,
	     align: "center",
	     valign: "bottom",
	     readOnly: false,
	     i18n: i18n.def,
	     buttons: {
	         "sure": function(){
	             $.thickbox.close();
	             var allCheacked = [];
	             var allCheackedVal = [];
	             $.each($("#socialKindThickBox :radio[name=_socialKind]:checked"), function(i, n){
	             	allCheacked[i] = i18n.lms140_socialKind[$(n).val()];  
	                 allCheackedVal[i] = $(n).val();	                  
	             });
	
	             $("#socialKindShowText").val(allCheacked);
	             $("#socialKind").val(allCheackedVal.join("|"));
	         },
	         "cancel": function(){
	             $.thickbox.close();
	         }
	     }
	 });
	 
}
//登錄社會責任專案目標族群
function socialTaBT(){

	var item = API.loadCombos("lms140_socialTa")["lms140_socialTa"];
	$("#_socialTa").setItems({
		 size: "1",
	     item: item,
		 clear : true,
		 itemType: 'radio' 
	 });
		 	
	$("[name=_socialTa]").removeAttr("disabled").removeAttr("checked");
	$('input:radio[name="_socialTa"]').prop('disabled', false);
	$('input:radio[name="_socialTa"]').show();

	 var socialTa = $("#socialTa").val();
	 if (socialTa != "") {
	     var vals = socialTa.split("|");
	  
	     for (var i in vals) {
	        $("[name=_socialTa][value=" + vals[i] + "]").prop("checked", true);
	     }
	 }
	 
	 $("#socialTaThickBox").thickbox({
	     title: i18n.lms1405s02["L140M01A.socialTa"],
	     width: 400,
	     height: 300,
	     modal: true,
	     align: "center",
	     valign: "bottom",
	     readOnly: false,
	     i18n: i18n.def,
	     buttons: {
	         "sure": function(){
	             $.thickbox.close();
	             var allCheacked = [];
	             var allCheackedVal = [];
	             $.each($("#socialTaThickBox :radio[name=_socialTa]:checked"), function(i, n){
	             	allCheacked[i] = i18n.lms140_socialTa[$(n).val()];  
	                 allCheackedVal[i] = $(n).val();	                  
	             });
	
	             $("#socialTaShowText").val(allCheacked);
	             $("#socialTa").val(allCheackedVal.join("|"));
	         },
	         "cancel": function(){
	             $.thickbox.close();
	         }
	     }
	 });
	 
}
//登錄社會責任專案類別
function socialRespBT(){

	var item = API.loadCombos("lms140_socialResp")["lms140_socialResp"];
	$("#_socialResp").setItems({
		 size: "1",
	     item: item,
		 clear : true,
		 itemType: 'radio' 
	 });
		 	
	$("[name=_socialResp]").removeAttr("disabled").removeAttr("checked");
	$('input:radio[name="_socialResp"]').prop('disabled', false);
	$('input:radio[name="_socialResp"]').show();

	 var socialResp = $("#socialResp").val();
	 if (socialResp != "") {
	     var vals = socialResp.split("|");
	  
	     for (var i in vals) {
	        $("[name=_socialResp][value=" + vals[i] + "]").prop("checked", true);
	     }
	 }
	 
	 $("#socialRespThickBox").thickbox({
	     title: i18n.lms1405s02["L140M01A.socialResp"],
	     width: 300,
	     height: 200,
	     modal: true,
	     align: "center",
	     valign: "bottom",
	     readOnly: false,
	     i18n: i18n.def,
	     buttons: {
	         "sure": function(){
	             $.thickbox.close();
	             var allCheacked = [];
	             var allCheackedVal = [];
	             $.each($("#socialRespThickBox :radio[name=_socialResp]:checked"), function(i, n){
	             	allCheacked[i] = i18n.lms140_socialResp[$(n).val()];  
	                 allCheackedVal[i] = $(n).val();	                  
	             });
	
	             $("#socialRespShowText").val(allCheacked);
	             $("#socialResp").val(allCheackedVal.join("|"));
	         },
	         "cancel": function(){
	             $.thickbox.close();
	         }
	     }
	 });
	 
}

/** J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位 取得高品質最終結果 */
function getHighQualityResult(){
	if($("input[name^='isHighQualityProjOpt_']:checked").length < 5){
		//沒填妥 放空值 
		$("#isHighQualityProjResult").val("");
		$("#div_hqProjResHighQualityProjectFinace").hide();
		$("#div_hqProjResProjectFinace").hide();
	}else if($("input[name^='isHighQualityProjOpt_'][value='Y']:checked").length == 5){
		//高品質融資選項5個都勾 >> 顯示「高品質專案融資」，適用80%風險權數
		$("#isHighQualityProjResult").val("Y");
		$("#div_hqProjResHighQualityProjectFinace").show();
		$("#div_hqProjResProjectFinace").hide();
	}else{//不屬於高品質專案融資，顯示「專案融資」，適用100%風險權數
		$("#isHighQualityProjResult").val("N");
		$("#div_hqProjResHighQualityProjectFinace").hide();
		$("#div_hqProjResProjectFinace").show();		
	}
}