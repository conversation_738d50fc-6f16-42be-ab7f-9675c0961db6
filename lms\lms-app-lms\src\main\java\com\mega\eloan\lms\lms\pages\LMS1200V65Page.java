
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;


/**<pre>
 * 授信簽報書提審計委員會(授管處)
 */
@Controller
@RequestMapping("/lms/lms1200v65")
public class LMS1200V65Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		//設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.授管處_審查中);
		
		if (this.getAuth(AuthType.Modify)) {
			// 經辦權限時要顯示的按鈕...
			// 授審正大科長通知先將「產生」功能拿掉, 等後續再提
			//,CreditButtonEnum.Create
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Login4);
			//,CreditButtonEnum.CreDoc1
		} else {
			// 否則需要顯示的按鈕
			// 加上Button
			addToButtonPanel(model, LmsButtonEnum.View);
		}		
		
//		if (this.getAuth(params, AuthType.Accept)) {
//			// 主管權限時要顯示的按鈕...
//			add(new CreditButtonPanel("_buttonPanel", null,CreditButtonEnum.View));			
//		} else {
//			// 否則需要顯示的按鈕
//			// 加上Button
//			add(new CreditButtonPanel("_buttonPanel", null,
//					CreditButtonEnum.View,CreditButtonEnum.Login3,CreditButtonEnum.Create,CreditButtonEnum.CreDoc1));			
//		}

		//套用哪個i18N檔案
		renderJsI18N(LMS1205V01Page.class);
		renderJsI18N(LMS1200V65Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS1205V01Page');");
	}// ;

	// @Override
	// public String[] getJavascriptPath() {
	// return new String[] { "pagejs/lms/LMS1205V01Page.js" };
	// }
}
