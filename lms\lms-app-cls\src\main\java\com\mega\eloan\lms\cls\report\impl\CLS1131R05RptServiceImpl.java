package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1131R05RptService;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 借保人徵信資料
 * </pre>
 * 
 * @since 2012/11/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/12,Fantasy,new
 *          <li>2013/06/15,調整主借款人和配偶之利害關係人顯示排序問題
 *          <li>2013/07/11,Rex,修改顯示評等
 *          <li>2013/07/17,Rex,修改判斷非兆豐行庫改用判斷其帳戶為14碼的排除加總
 *          </ul>
 */
@Service("cls1131r05rptservice")
public class CLS1131R05RptServiceImpl implements FileDownloadService, CLS1131R05RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1131R05RptServiceImpl.class);

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
//			baos = (ByteArrayOutputStream) this.generateReport(params);
//			return baos.toByteArray();
			// J-109-0148 改為產生JSONObject
			return this.generateJSONObject(params);
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params) throws FileNotFoundException, IOException, Exception {

		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;
		
		try {
			locale = LMSUtil.getLocale();
			rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator generator = new ReportGenerator("report/cls/CLS1131R05_" + locale.toString() + ".rpt");

			List<Map<String, Object>> refuseList = (List<Map<String, Object>>)params.get("refuseList");
			List<Map<String, String>> list = new ArrayList<Map<String, String>>();
			
			for(Map<String, Object> refuseMap : refuseList){
				Map<String, String> map = new HashMap<String, String>();
				map.put("CommonBean1.field01", (String)refuseMap.get("custId"));//統一編號
				map.put("CommonBean1.field02", (String)refuseMap.get("dupNo"));//重覆序號
				map.put("CommonBean1.field03", (String)refuseMap.get("custName"));//客戶名稱
				map.put("CommonBean1.field04", (String)refuseMap.get("type"));//本行/金控
				map.put("CommonBean1.field05", (String)refuseMap.get("regDt"));//登錄日期
				map.put("CommonBean1.field06", (String)refuseMap.get("regBr"));//婉卻分行
				map.put("CommonBean1.field07", (String)refuseMap.get("statusCd"));//婉卻控管種類
				map.put("CommonBean1.field08", (String)refuseMap.get("clsCase"));//本行婉卻業務別/金控卡貸婉卻
				map.put("CommonBean1.field09", (String)refuseMap.get("refuseCd"));//婉卻原因/說明
				list.add(map);
			}
			
			generator.setRowsData(list);
			rptVariableMap.put("queryMan", (String)params.get("queryMan"));
			generator.setVariableData(rptVariableMap);

			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		
		return outputStream;
	}
	
	/**
	 * 建立JSONObject
	 * 
	 * @param params
	 *            params
	 * @return byte[]
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public byte[] generateJSONObject(PageParameters params) throws Exception {

		Map<String, String> rptVariableMap = null;
		JSONObject json = new JSONObject();
		
		try {
			rptVariableMap = new LinkedHashMap<String, String>();

			List<Map<String, Object>> refuseList = (List<Map<String, Object>>)params.get("refuseList");
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			
			for(Map<String, Object> refuseMap : refuseList){
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("td0", CapString.trimNull(refuseMap.get("custId")));//統一編號
				map.put("td1", CapString.trimNull(refuseMap.get("dupNo")));//重覆序號
				map.put("td2", CapString.trimNull(refuseMap.get("custName")));//客戶名稱
				map.put("td3", CapString.trimNull(refuseMap.get("type")));//本行/金控
				map.put("td4", CapString.trimNull(refuseMap.get("regDt")));//登錄日期
				map.put("td5", CapString.trimNull(refuseMap.get("regBr")));//婉卻分行
				map.put("td6", CapString.trimNull(refuseMap.get("statusCd")));//婉卻控管種類
				map.put("td7", CapString.trimNull(refuseMap.get("clsCase")));//本行婉卻業務別/金控卡貸婉卻
				map.put("td8", CapString.trimNull(refuseMap.get("refuseCd")));//婉卻原因/說明
				list.add(map);
			}
			
			rptVariableMap.put("queryMan", (String)params.get("queryMan"));
			json.putAll(rptVariableMap);
			json.put("list",list);
			json.put("date", CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));


		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return json.toString().getBytes("utf-8");
		
	}

}
