/* 
 * ELF447.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 授信授權人員檔 (此檔案只有ELF447一個檔案) **/
public class ELF447 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 員工編號
	 * <p/>
	 * 角色1：C100M01.appraiser<br/>
	 * 角色3：C100M01. approver<br/>
	 * 角色4：C100M01.bossid<br/>
	 * 角色5：C100M01.unitMger1<br/>
	 * C100M01.unitMger2<br/>
	 * <br/>
	 * 若為代鑑價案件則:<br/>
	 * 角色1：C100M01.EntrAppr<br/>
	 * 角色3：C100M01.entrCheck
	 */
	@Column(name = "ELF447_EMP_NO", length = 8, columnDefinition = "CHAR(8)")
	private String elf447_emp_no;

	/**
	 * 覆核日期
	 * <p/>
	 * C100M01.estDate<br/>
	 * CES徵信、CLS消金(個金)、LMS授信(企金)：覆核日期 <br/>
	 * CMS擔保品：鑑估日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF447_CHKDATE", columnDefinition = "DATE")
	private Date elf447_chkdate;

	/**
	 * 角色
	 * <p/>
	 * 1：經辦<br/>
	 * 2：帳戶管理員(經辦選定)<br/>
	 * 3：覆核主管(主管系統簽核)<br/>
	 * 4：授權(單位)主管(經辦選定)<br/>
	 * 5：人事檔的單位主管(系統抓取)<br/>
	 * 6：消金授信主管(經辦選定)
	 */
	@Column(name = "ELF447_ROLE", length = 1, columnDefinition = "CHAR(1)")
	private String elf447_role;

	/**
	 * 授權分行代號
	 * <p/>
	 * C100M01.branch
	 */
	@Column(name = "ELF447_CHKBRANCH", length = 3, columnDefinition = "CHAR(03)")
	private String elf447_chkbranch;

	/**
	 * 借款人統編
	 * <p/>
	 * C100M01.custId
	 */
	@Column(name = "ELF447_CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String elf447_custid;

	/**
	 * 重複序號
	 * <p/>
	 * C100M01.dupNo
	 */
	@Column(name = "ELF447_DUPNO", length = 1, columnDefinition = "CHAR(01)")
	private String elf447_dupno;

	/**
	 * 額度序號
	 * <p/>
	 * C100S03A.cntrNo
	 */
	@Column(name = "ELF447_CONTRACT", length = 12, columnDefinition = "CHAR(12)")
	private String elf447_contract;

	/**
	 * 主辦分行代號
	 * <p/>
	 * C100M01.ownBrId ???
	 */
	@Column(name = "ELF447_BRANCH", length = 3, columnDefinition = "CHAR(03)")
	private String elf447_branch;

	/**
	 * 系統類別
	 * <p/>
	 * ‘CMS’<br/>
	 * CES：徵信<br/>
	 * CLS：消金(個金)<br/>
	 * LMS：授信(企金)<br/>
	 * CMS：擔保品
	 */
	@Column(name = "ELF447_SYSTYPE", length = 3, columnDefinition = "CHAR(03)", unique = true)
	private String elf447_systype;

	/**
	 * 簽報書/擔保品文件編號 UNID
	 * <p/>
	 * CES徵信、CLS消金(個金)、LMS授信(企金)：簽報書文件編號<br/>
	 * CMS擔保品：擔保品文件編號<br/>
	 * C100M01.mainId
	 */
	@Column(name = "ELF447_UNID", length = 50, columnDefinition = "CHAR(50)", unique = true)
	private String elf447_unid;

	/**
	 * 授權等級
	 * <p/>
	 * 1：授權內(一般)<br/>
	 * 2：授權內(其他)<br/>
	 * 3：授權內(陳復案/陳述案)<br/>
	 * 4：授權外(一般)<br/>
	 * 5：授權外(其他)<br/>
	 * 6：授權外(陳復案/陳述案)
	 */
	@Column(name = "ELF447_CASELEVEL", length = 1, columnDefinition = "CHAR(1)")
	private String ELF447_CaseLevel;

	/**
	 * 授信性質別
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13 <br/>
	 * (YYYYYYYYYYYY )
	 */
	@Column(name = "ELF447_PROPERTY", length = 20, columnDefinition = "CHAR(20)")
	private String elf447_property;

	/**
	 * 資料修改日期
	 * <p/>
	 * C100M01.updateTime
	 */
	@Column(name = "ELF447_TMESTAMP", columnDefinition = "TIMESTAMP")
	// @CMSColumn(dateType = CMSTypes.TIMESTAMP)
	private Date elf447_tmestamp;

	/** 消金團貸批號 **/
	@Column(name = "ELF447_PACKNO", length = 16, columnDefinition = "CHAR(16)")
	private String elf447_packno;

	/**
	 * 取得員工編號
	 * <p/>
	 * 角色1：C100M01.appraiser<br/>
	 * 角色3：C100M01. approver<br/>
	 * 角色4：C100M01.bossid<br/>
	 * 角色5：C100M01.unitMger1<br/>
	 * C100M01.unitMger2<br/>
	 * <br/>
	 * 若為代鑑價案件則:<br/>
	 * 角色1：C100M01.EntrAppr<br/>
	 * 角色3：C100M01.entrCheck
	 */
	public String getElf447_emp_no() {
		return this.elf447_emp_no;
	}

	/**
	 * 設定員工編號
	 * <p/>
	 * 角色1：C100M01.appraiser<br/>
	 * 角色3：C100M01. approver<br/>
	 * 角色4：C100M01.bossid<br/>
	 * 角色5：C100M01.unitMger1<br/>
	 * C100M01.unitMger2<br/>
	 * <br/>
	 * 若為代鑑價案件則:<br/>
	 * 角色1：C100M01.EntrAppr<br/>
	 * 角色3：C100M01.entrCheck
	 **/
	public void setElf447_emp_no(String value) {
		this.elf447_emp_no = value;
	}

	/**
	 * 取得覆核日期
	 * <p/>
	 * C100M01.estDate<br/>
	 * CES徵信、CLS消金(個金)、LMS授信(企金)：覆核日期 <br/>
	 * CMS擔保品：鑑估日期
	 */
	public Date getElf447_chkdate() {
		return this.elf447_chkdate;
	}

	/**
	 * 設定覆核日期
	 * <p/>
	 * C100M01.estDate<br/>
	 * CES徵信、CLS消金(個金)、LMS授信(企金)：覆核日期 <br/>
	 * CMS擔保品：鑑估日期
	 **/
	public void setElf447_chkdate(Date value) {
		this.elf447_chkdate = value;
	}

	/**
	 * 取得角色
	 * <p/>
	 * 1：經辦<br/>
	 * 2：帳戶管理員(經辦選定)<br/>
	 * 3：覆核主管(主管系統簽核)<br/>
	 * 4：授權(單位)主管(經辦選定)<br/>
	 * 5：人事檔的單位主管(系統抓取)<br/>
	 * 6：消金授信主管(經辦選定)
	 */
	public String getElf447_role() {
		return this.elf447_role;
	}

	/**
	 * 設定角色
	 * <p/>
	 * 1：經辦<br/>
	 * 2：帳戶管理員(經辦選定)<br/>
	 * 3：覆核主管(主管系統簽核)<br/>
	 * 4：授權(單位)主管(經辦選定)<br/>
	 * 5：人事檔的單位主管(系統抓取)<br/>
	 * 6：消金授信主管(經辦選定)
	 **/
	public void setElf447_role(String value) {
		this.elf447_role = value;
	}

	/**
	 * 取得授權分行代號
	 * <p/>
	 * C100M01.branch
	 */
	public String getElf447_chkbranch() {
		return this.elf447_chkbranch;
	}

	/**
	 * 設定授權分行代號
	 * <p/>
	 * C100M01.branch
	 **/
	public void setElf447_chkbranch(String value) {
		this.elf447_chkbranch = value;
	}

	/**
	 * 取得借款人統編
	 * <p/>
	 * C100M01.custId
	 */
	public String getElf447_custid() {
		return this.elf447_custid;
	}

	/**
	 * 設定借款人統編
	 * <p/>
	 * C100M01.custId
	 **/
	public void setElf447_custid(String value) {
		this.elf447_custid = value;
	}

	/**
	 * 取得重複序號
	 * <p/>
	 * C100M01.dupNo
	 */
	public String getElf447_dupno() {
		return this.elf447_dupno;
	}

	/**
	 * 設定重複序號
	 * <p/>
	 * C100M01.dupNo
	 **/
	public void setElf447_dupno(String value) {
		this.elf447_dupno = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * C100S03A.cntrNo
	 */
	public String getElf447_contract() {
		return this.elf447_contract;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * C100S03A.cntrNo
	 **/
	public void setElf447_contract(String value) {
		this.elf447_contract = value;
	}

	/**
	 * 取得主辦分行代號
	 * <p/>
	 * C100M01.ownBrId ???
	 */
	public String getElf447_branch() {
		return this.elf447_branch;
	}

	/**
	 * 設定主辦分行代號
	 * <p/>
	 * C100M01.ownBrId ???
	 **/
	public void setElf447_branch(String value) {
		this.elf447_branch = value;
	}

	/**
	 * 取得系統類別
	 * <p/>
	 * ‘CMS’<br/>
	 * CES：徵信<br/>
	 * CLS：消金(個金)<br/>
	 * LMS：授信(企金)<br/>
	 * CMS：擔保品
	 */
	public String getElf447_systype() {
		return this.elf447_systype;
	}

	/**
	 * 設定系統類別
	 * <p/>
	 * ‘CMS’<br/>
	 * CES：徵信<br/>
	 * CLS：消金(個金)<br/>
	 * LMS：授信(企金)<br/>
	 * CMS：擔保品
	 **/
	public void setElf447_systype(String value) {
		this.elf447_systype = value;
	}

	/**
	 * 取得簽報書/擔保品文件編號 UNID
	 * <p/>
	 * CES徵信、CLS消金(個金)、LMS授信(企金)：簽報書文件編號<br/>
	 * CMS擔保品：擔保品文件編號<br/>
	 * C100M01.mainId
	 */
	public String getElf447_unid() {
		return this.elf447_unid;
	}

	/**
	 * 設定簽報書/擔保品文件編號 UNID
	 * <p/>
	 * CES徵信、CLS消金(個金)、LMS授信(企金)：簽報書文件編號<br/>
	 * CMS擔保品：擔保品文件編號<br/>
	 * C100M01.mainId
	 **/
	public void setElf447_unid(String value) {
		this.elf447_unid = value;
	}

	/**
	 * 取得授權等級
	 * <p/>
	 * 1：授權內(一般)<br/>
	 * 2：授權內(其他)<br/>
	 * 3：授權內(陳復案/陳述案)<br/>
	 * 4：授權外(一般)<br/>
	 * 5：授權外(其他)<br/>
	 * 6：授權外(陳復案/陳述案)
	 */
	public String getELF447_CaseLevel() {
		return this.ELF447_CaseLevel;
	}

	/**
	 * 設定授權等級
	 * <p/>
	 * 1：授權內(一般)<br/>
	 * 2：授權內(其他)<br/>
	 * 3：授權內(陳復案/陳述案)<br/>
	 * 4：授權外(一般)<br/>
	 * 5：授權外(其他)<br/>
	 * 6：授權外(陳復案/陳述案)
	 **/
	public void setELF447_CaseLevel(String value) {
		this.ELF447_CaseLevel = value;
	}

	/**
	 * 取得授信性質別
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13 <br/>
	 * (YYYYYYYYYYYY )
	 */
	public String getElf447_property() {
		return this.elf447_property;
	}

	/**
	 * 設定授信性質別
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13 <br/>
	 * (YYYYYYYYYYYY )
	 **/
	public void setElf447_property(String value) {
		this.elf447_property = value;
	}

	/**
	 * 取得資料修改日期
	 * <p/>
	 * C100M01.updateTime
	 */
	public Date getElf447_tmestamp() {
		return this.elf447_tmestamp;
	}

	/**
	 * 設定資料修改日期
	 * <p/>
	 * C100M01.updateTime
	 **/
	public void setElf447_tmestamp(Date value) {
		this.elf447_tmestamp = value;
	}

	/** 取得消金團貸批號 **/
	public String getElf447_packno() {
		return this.elf447_packno;
	}

	/** 設定消金團貸批號 **/
	public void setElf447_packno(String value) {
		this.elf447_packno = value;
	}
}
