/* 
 * L999S07A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 授信約定書檔 **/
@Entity
//@EntityListeners({DocumentModifyListener.class})
@Table(name="L999S07A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L999S07A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 帳號 **/
	@Column(name="ACCNO", length=32, columnDefinition="VARCHAR(32)")
	private String accNo;

	/** 到期日_年 **/
	@Column(name="DUESDATEY", length=3, columnDefinition="VARCHAR(3)")
	private String dueSDateY;

	/** 到期日_月 **/
	@Column(name="DUESDATEM", length=2, columnDefinition="VARCHAR(2)")
	private String dueSDateM;

	/** 到期日_日 **/
	@Column(name="DUESDATED", length=2, columnDefinition="VARCHAR(2)")
	private String dueSDateD;
	
	/** 
	 * 幣別<p/>
	 * 美金
	 */
	@Column(name="COVERLOANCURR", length=30, columnDefinition="VARCHAR(30)")
	private String coverLoanCurr;
	/** 
	 * 金額<p/>
	 * 元
	 */
	@Column(name="COVERLOANAMT", length=60, columnDefinition="VARCHAR(60)")
	private String coverLoanAmt;

	/** 動用期間(起)_年 **/
	@Column(name="USESDATEY", length=3, columnDefinition="VARCHAR(3)")
	private String useSDateY;

	/** 動用期間(起)_月 **/
	@Column(name="USESDATEM", length=2, columnDefinition="VARCHAR(2)")
	private String useSDateM;

	/** 動用期間(起)_日 **/
	@Column(name="USESDATED", length=2, columnDefinition="VARCHAR(2)")
	private String useSDateD;

	/** 動用期間(迄)_年 **/
	@Column(name="USEEDATEY", length=3, columnDefinition="VARCHAR(3)")
	private String useEDateY;

	/** 動用期間(迄)_月 **
	 * 	@Size(max=2)
	 */
	@Column(name="USEEDATEM", length=2, columnDefinition="VARCHAR(2)")
	private String useEDateM;

	/** 動用期間(迄) _日 **/
	@Column(name="USEEDATED", length=2, columnDefinition="VARCHAR(2)")
	private String useEDateD;

	/** 
	 * 授信總額度<p/>
	 * 美金元
	 */
	@Column(name="TOTLOANAMT", length=60, columnDefinition="VARCHAR(60)")
	private String totLoanAmt;

	/** 
	 * 提供保證金<p/>
	 * %
	 */
	@Column(name="GUAPERCENT", length=30, columnDefinition="VARCHAR(30)")
	private String guaPercent;

	/** 
	 * 連保人資料保密_是否同意<p/>
	 * ※Y.同意, N.不同意<br/>
	 *  乙方及丙方 同意 不同意 甲方得將乙方及丙方之帳務、信用、投資及保險等資料，在合於營業登記項目或基於業務需要並遵守銀行所屬之金融控股公司及其各子公司
	 */
	@Column(name="DATAUSEFLAGGUA", length=1, columnDefinition="CHAR(1)")
	private String dataUseFlagGua;

	/** 
	 * 連保人資料保密_同意項目<p/>
	 * 以2進位儲存<br/>
	 *  2^0=1, 2^1=2, 2^2=4<br/>
	 *  同意者勾選：<br/>
	 *  1兆豐證券股份有限公司<br/>
	 *  2兆豐產物保險股份有限公司<br/>
	 *  4兆豐票券金融股份有限公司<br/>
	 *  8兆豐人身保險代理人股份有限公司<br/>
	 *  16兆豐國際證券投資信託股份有限公司<br/>
	 *  32兆豐資產管理股份有限公司<br/>
	 *  64兆豐創業投資股份有限公司<br/>
	 *  0.上述所有公司
	 */
	@Column(name="DATAUSEITEMGUA", columnDefinition="DECIMAL(3,0)")
	private Integer dataUseItemGua;

	/** 前次立約日期_年 **/
	@Column(name="PSIGNDATEY", length=3, columnDefinition="VARCHAR(3)")
	private String pSignDateY;

	/** 前次立約日期_月 **/
	@Column(name="PSIGNDATEM", length=2, columnDefinition="VARCHAR(2)")
	private String pSignDateM;

	/** 前次立約日期_日 **/
	@Column(name="PSIGNDATED", length=2, columnDefinition="VARCHAR(2)")
	private String pSignDateD;

	/** 前次約定書字號(字) **/
	@Column(name="PCONTRACTWORD", length=30, columnDefinition="VARCHAR(30)")
	private String pContractWord;

	/** 前次約定書字號(號) **/
	@Column(name="PCONTRACTNO", length=20, columnDefinition="VARCHAR(20)")
	private String pContractNo;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得帳號 **/
	public String getAccNo() {
		return this.accNo;
	}
	/** 設定帳號 **/
	public void setAccNo(String value) {
		this.accNo = value;
	}

	/** 取得到期日_年 **/
	public String getDueSDateY() {
		return this.dueSDateY;
	}
	/** 設定到期日_年 **/
	public void setDueSDateY(String value) {
		this.dueSDateY = value;
	}

	/** 取得到期日_月 **/
	public String getDueSDateM() {
		return this.dueSDateM;
	}
	/** 設定到期日_月 **/
	public void setDueSDateM(String value) {
		this.dueSDateM = value;
	}

	/** 取得到期日_日 **/
	public String getDueSDateD() {
		return this.dueSDateD;
	}
	/** 設定到期日_日 **/
	public void setDueSDateD(String value) {
		this.dueSDateD = value;
	}

	/** 
	 * 取得幣別<p/>
	 * 美金
	 */
	public String getCoverLoanCurr() {
		return this.coverLoanCurr;
	}
	/**
	 *  設定幣別<p/>
	 *  美金
	 **/
	public void setCoverLoanCurr(String value) {
		this.coverLoanCurr = value;
	}
	/** 
	 * 取得金額<p/>
	 * 元
	 */
	public String getCoverLoanAmt() {
		return this.coverLoanAmt;
	}
	/**
	 *  設定金額<p/>
	 *  元
	 **/
	public void setCoverLoanAmt(String value) {
		this.coverLoanAmt = value;
	}

	/** 取得動用期間(起)_年 **/
	public String getUseSDateY() {
		return this.useSDateY;
	}
	/** 設定動用期間(起)_年 **/
	public void setUseSDateY(String value) {
		this.useSDateY = value;
	}

	/** 取得動用期間(起)_月 **/
	public String getUseSDateM() {
		return this.useSDateM;
	}
	/** 設定動用期間(起)_月 **/
	public void setUseSDateM(String value) {
		this.useSDateM = value;
	}

	/** 取得動用期間(起)_日 **/
	public String getUseSDateD() {
		return this.useSDateD;
	}
	/** 設定動用期間(起)_日 **/
	public void setUseSDateD(String value) {
		this.useSDateD = value;
	}

	/** 取得動用期間(迄)_年 **/
	public String getUseEDateY() {
		return this.useEDateY;
	}
	/** 設定動用期間(迄)_年 **/
	public void setUseEDateY(String value) {
		this.useEDateY = value;
	}

	/** 取得動用期間(迄)_月 **/
	public String getUseEDateM() {
		return this.useEDateM;
	}
	/** 設定動用期間(迄)_月 **/
	public void setUseEDateM(String value) {
		this.useEDateM = value;
	}

	/** 取得動用期間(迄) _日 **/
	public String getUseEDateD() {
		return this.useEDateD;
	}
	/** 設定動用期間(迄) _日 **/
	public void setUseEDateD(String value) {
		this.useEDateD = value;
	}

	/** 
	 * 取得授信總額度<p/>
	 * 美金元
	 */
	public String getTotLoanAmt() {
		return this.totLoanAmt;
	}
	/**
	 *  設定授信總額度<p/>
	 *  美金元
	 **/
	public void setTotLoanAmt(String value) {
		this.totLoanAmt = value;
	}

	/** 
	 * 取得提供保證金<p/>
	 * %
	 */
	public String getGuaPercent() {
		return this.guaPercent;
	}
	/**
	 *  設定提供保證金<p/>
	 *  %
	 **/
	public void setGuaPercent(String value) {
		this.guaPercent = value;
	}

	/** 
	 * 取得連保人資料保密_是否同意<p/>
	 * ※Y.同意, N.不同意<br/>
	 *  乙方及丙方 同意 不同意 甲方得將乙方及丙方之帳務、信用、投資及保險等資料，在合於營業登記項目或基於業務需要並遵守銀行所屬之金融控股公司及其各子公司
	 */
	public String getDataUseFlagGua() {
		return this.dataUseFlagGua;
	}
	/**
	 *  設定連保人資料保密_是否同意<p/>
	 *  ※Y.同意, N.不同意<br/>
	 *  乙方及丙方 同意 不同意 甲方得將乙方及丙方之帳務、信用、投資及保險等資料，在合於營業登記項目或基於業務需要並遵守銀行所屬之金融控股公司及其各子公司
	 **/
	public void setDataUseFlagGua(String value) {
		this.dataUseFlagGua = value;
	}

	/** 
	 * 取得連保人資料保密_同意項目<p/>
	 * 以2進位儲存<br/>
	 *  2^0=1, 2^1=2, 2^2=4<br/>
	 *  同意者勾選：<br/>
	 *  1兆豐證券股份有限公司<br/>
	 *  2兆豐產物保險股份有限公司<br/>
	 *  4兆豐票券金融股份有限公司<br/>
	 *  8兆豐人身保險代理人股份有限公司<br/>
	 *  16兆豐國際證券投資信託股份有限公司<br/>
	 *  32兆豐資產管理股份有限公司<br/>
	 *  64兆豐創業投資股份有限公司<br/>
	 *  0.上述所有公司
	 */
	public Integer getDataUseItemGua() {
		return this.dataUseItemGua;
	}
	/**
	 *  設定連保人資料保密_同意項目<p/>
	 *  以2進位儲存<br/>
	 *  2^0=1, 2^1=2, 2^2=4<br/>
	 *  同意者勾選：<br/>
	 *  1兆豐證券股份有限公司<br/>
	 *  2兆豐產物保險股份有限公司<br/>
	 *  4兆豐票券金融股份有限公司<br/>
	 *  8兆豐人身保險代理人股份有限公司<br/>
	 *  16兆豐國際證券投資信託股份有限公司<br/>
	 *  32兆豐資產管理股份有限公司<br/>
	 *  64兆豐創業投資股份有限公司<br/>
	 *  0.上述所有公司
	 **/
	public void setDataUseItemGua(Integer value) {
		this.dataUseItemGua = value;
	}

	/** 取得前次立約日期_年 **/
	public String getPSignDateY() {
		return this.pSignDateY;
	}
	/** 設定前次立約日期_年 **/
	public void setPSignDateY(String value) {
		this.pSignDateY = value;
	}

	/** 取得前次立約日期_月 **/
	public String getPSignDateM() {
		return this.pSignDateM;
	}
	/** 設定前次立約日期_月 **/
	public void setPSignDateM(String value) {
		this.pSignDateM = value;
	}

	/** 取得前次立約日期_日 **/
	public String getPSignDateD() {
		return this.pSignDateD;
	}
	/** 設定前次立約日期_日 **/
	public void setPSignDateD(String value) {
		this.pSignDateD = value;
	}

	/** 取得前次約定書字號(字) **/
	public String getPContractWord() {
		return this.pContractWord;
	}
	/** 設定前次約定書字號(字) **/
	public void setPContractWord(String value) {
		this.pContractWord = value;
	}

	/** 取得前次約定書字號(號) **/
	public String getPContractNo() {
		return this.pContractNo;
	}
	/** 設定前次約定書字號(號) **/
	public void setPContractNo(String value) {
		this.pContractNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
