/* 
 * C122S01YDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.sql.Timestamp;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C122S01Y;

/** 個金進件管理地政士名單 **/
public interface C122S01YDao extends IGenericDao<C122S01Y> {

	C122S01Y findByOid(String oid);
	
	List<C122S01Y> findByMainId(String mainId);

	List<C122S01Y> findByIndex01(String mainId);
	
	public List<C122S01Y> findMatchLaaList(String mainId,String[] laaName_arr);

	// J-112-0006 撈取前一案/後一案之有相同地政士的房貸案件(引介來源為地政士引介才可輸入地政士)
	public C122S01Y findLaaCaseBy_brNo(String ownBrId, String mainId, Timestamp applyTS, String type,String laaName);

}