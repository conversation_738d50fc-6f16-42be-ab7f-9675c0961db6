$(function(){
	
	//驗證readOnly狀態
	function checkReadonly(){
		var auth = (responseJSON ? responseJSON.Auth : {}); //權限
	    if (auth.readOnly || responseJSON.mainDocStatus != "010") {
			return true ;
		}
		return false ;
	}
    var auth = (responseJSON ? responseJSON.Auth : {});
    if (auth.readOnly || responseJSON.mainDocStatus != "010") {
        $('body').lockDoc();
    }
    /*設定關閉此畫面時要詢問*/
    setCloseConfirm(true);
    
    $.form.init({
        formHandler: "lms1815formhandler",
        formPostData: {
            formAction: "queryMain"
        },
        loadSuccess: function(json){
            if (json.page == "01") {
            	if($.trim(json.custInfo) == ""){
                	$("#btnSearch").show();
            	}
                var i = json.uCase;
                if (i && i == "Y") {
                    $('#radio2_chk').show();
                } else {
                    $('#radio2_chk').hide();
                    $('input[name=uCaseRole]').prop('checked','');
                }
		
		        $('#creator').val(json.creator);
                $('#updater').val(json.updater);
            }else if (json.page == "02") {
                $("L180M02BForm").injectData(json.L180M02BForm);
//            	alert(json.result1.elfNCkdFlag);
//            	alert($("input[@name=r8888][value="+ json.result1.elfNCkdFlag +"]").next().html());
//            	alert($("input[@name=r88888][value="+ json.result1.elfNCkdFlag +"]").next().html());
               	
            	if(json.L180M02BForm.elfRCkdLine != ''){
            		$('#elfRCkdLine').val($("input[name=r888][value="+ json.L180M02BForm.elfRCkdLine +"]").next('span').html());
            	}
            	if(json.L180M02BForm.elfMDFlag != ''){
            		$('#elfMDFlag').val($("input[name=r88888][value="+ json.L180M02BForm.elfMDFlag +"]").next('span').html());
            	}
            	if(json.L180M02BForm.elfNCkdFlag != ''){
            		$('#elfNCkdFlag').val($("input[name=r8888][value="+ json.L180M02BForm.elfNCkdFlag +"]").next('span').html());
            	}
            	if(json.result1.elfRCkdLine != ''){
            		$('#elfRCkdLine_1').val($("input[name=r888][value="+ json.result1.elfRCkdLine +"]").next('span').html());
            	}
            	if(json.result1.elfMDFlag != ''){
            		$('#elfMDFlag_1').val($("input[name=r88888][value="+ json.result1.elfMDFlag +"]").next('span').html());
            	}
            	if(json.result1.elfNCkdFlag != ''){
            		$('#elfNCkdFlag_1').val($("input[name=r8888][value="+ json.result1.elfNCkdFlag +"]").next('span').html());
            	}
            	
            	if(json.result1.elfMowType != ''){
            		$('#elfMowType_1').html(DOMPurify.sanitize($('#elfMowType option[value=' + json.result1.elfMowType + ']').text()));
            	}
            	if(json.L180M02BForm.elfMowType != ''){
    				$('#elfMowType').val($('#elfMowType').val());
            	}
             	if(json.result1.elfNewAdd != ''){
             		$('#elfNewAdd_1').html(DOMPurify.sanitize($('#elfNewAdd option[value=' + json.result1.elfNewAdd + ']').text()));
             	}
            	if(json.L180M02BForm.elfNewAdd != ''){
    				$('#elfNewAdd').val($('#elfNewAdd').val());
            	}
//            	if(json.result1.elfMowType != ''){
//    				$('#elfMowType_1').html($('#elfMowType').find("option").find("value="+json.result1.elfMowType).text());
//            	}
//            	if(json.result1.elfNewAdd != ''){
//    				$('#elfNewAdd_1').html($('#elfNewAdd').find("option").find("value="+json.result1.elfNewAdd).text());
//            	}
                if (json.result1.elfUCkdLINE == 'Y') {
                    $("[name='elfUCkdLINE_1'][value='Y']").prop("checked", true);
                } else if (json.result1.elfUCkdLINE == "N") {
                    $("[name='elfUCkdLINE_1'][value='N']").prop("checked", true);
                } else {
                    $("[name='elfUCkdLINE_1'][value='none']").prop("checked", true);
                }
                $('#elfUCkdDt_1').html(json.result1.elfUCkdDt);
                if (json.result1.elfMainCust == "Y") {
                    $("[name=elfMainCust_1][value='Y']").prop("checked", true);
                } else if (json.result1.elfMainCust == "N") {
                    $("[name=elfMainCust_1][value='N']").prop("checked", true);
                }
                $('#elfLRDate_1').html(json.result1.elfLRDate);
                $('#elfCrdTTbl_1').html(json.result1.elfCrdTTbl);
                if (json.result1.elfDBUOBU == "Y") {
                    $("[name=elfDBUOBU_1][value='Y']").prop("checked", true);
                } else if (json.result1.elfDBUOBU == "N") {
                    $("[name=elfDBUOBU_1][value='N']").prop("checked", true);
                }
                $('#elfNextNwDt').val(json.L180M02BForm.elfNextNwDt);
                if($('#elfNextNwDt').val() != ''){
                	$("#nextNwDt").show();
                }
                $('#elfMowTbl1_1').html(json.result1.elfMowTbl1);
                $('#elfMDDt_1').html(json.result1.elfMDDt);
                $('#elfProcess_1').html(json.result1.elfProcess);
                $('#elfNewDate_1').html(json.result1.elfNewDate);
                $('#elfNCkdMemo_1').html(json.result1.elfNCkdMemo);
                $('#elfNextNwDt_1').html(json.result1.elfNextNwDt);
                
                
                $('#elfMemo_1').html(json.result1.elfMemo);
            }
        }
    });
    
    $(".upWord").blur(function(){
        var word = $(this).val().toUpperCase();
        $(this).val(word);
    });
    
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(){
    	saveData(true);
    }).end().find("#btnSend").click(function(){
    	if (checkReadonly()) {
    		btnSend();
        }
        else {
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                 	saveData(false,btnSend);
                }
            });
        }
        
    }).end().find("#btnAccept").click(function(){
    	if (checkReadonly()) {
    		btnAccept();
        }
        else {
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                 	saveData(false,btnAccept);
                }
            });
        }
		}).end().find("#btnSearch").click(function(){
		        var ownbrId = userInfo ? userInfo.unitNo : "";
		        $("#custInfoForm").find("input#branch").val(ownbrId);
		        $("#Search").thickbox({
		            title: i18n.lms1815m01["searchTitle"],
		            width: 400,
		            height: 200,
		            modal: false,
		            align: "center",
		            valign: "bottom",
		            i18n: i18n.def,
		            buttons: {
		                "sure": function(){
		                    if ($("#custInfoForm").valid()) {
		                        $.ajax({
		                            type: "POST",
		                            handler: "lms1815formhandler",
		                            data: {
		                                formAction: "queryCustId",
		                                mainOid : responseJSON.mainOid,
		                                branchId: $("#custInfoForm").find("#branch").val(),
		                                custId: $("#custInfoForm").find("#custid").val(),
		                                dupNo : $("#custInfoForm").find("#dupNo").val()
		                            }
		                        }).done(function(responseData){
		                            
		                                if (responseData.sucess == "N") {
		                                    CommonAPI.confirmMessage(i18n.lms1815m01["lms412NoInfo"], function(b){
		                                        if (b) {
		                                            $.ajax({
		                                                type: "POST",
		                                                handler: "lms1815formhandler",
		                                                data: {
		                                                    formAction: "queryCustData",
		                                                    page: responseJSON.page,
		                                                    txCode: responseJSON.txCode,
		                                                    branchId: $("#custInfoForm").find("#branch").val(),
		                                                    custId: $("#custInfoForm").find("#custid").val(),
		                                                    dupNo : $("#custInfoForm").find("#dupNo").val()
		                                                }
		                                            }).done(function(responseData){
		    //                                                    $("body .reset").html('');
		                                                    $("#elfUpdater").val('');
		                                                    $("#elfUpdDate").val('');
		                                                    $("#elfTmeStamp").val('');
		                                                    $("#cState").val('');
		                                                    $("#elfCState").val('');
		                                                    $("#elfCancelDt").val('');
		                                                    $("#L180M02AForm").setData(responseData.L180M02AForm);
		    //                                                    $("#typCdName").val(responseData.typCdName);
		    //                                                    $("#elfBranch").val(responseData.l180m02aForm.elfBranch);
		    //                                                    $("#custId").val(responseData.l180m02aForm.custId);
		    //                                                    $("#dupNo").val(responseData.l180m02aForm.dupNo);
		                                                    $("#elfBranchName").val(responseData.ElfBranchName);
		                                                });
		                                        }
		                                    })
		                                } else if (responseData.sucess == "Y") {
		                                    $("#L180M02AForm").setData(responseData);
		                                    if ($("input[name=uCase]:checked").val() =='Y'){ 
		                                        $('#radio2_chk').show();
		                                    }else {
		                                        $('#radio2_chk').hide();
		                                        $('input[name=uCaseRole]').prop('checked','');
		                                    }
		                                }
		                        });
		                        $.thickbox.close();
		                    }
		                },
		                "cancel": function(){
		                    $.thickbox.close();
		                }
		            }
		        });
    }).end().find("#btnCauculate").click(function(){
    	if (responseJSON.page == "02") {
            if (!$("#L180M02BForm").valid()) {
                return;
            }
        }
        $.ajax({
            type: "POST",
            handler: "lms1815formhandler",
            data: {
                formAction: "cauNextDate",
                page : responseJSON.page,
                txCode: responseJSON.txCode,
                oid: $("#oid").val()
            }
            }).done(function(responseData){
                var nextDate = responseData.nextDate;
                CommonAPI.showMessage(i18n.lms1815m01["nextCTLDate"] + nextDate);
        });
		}).end().find("#btnState").click(function(){
		        $.ajax({
		            type: "POST",
		            handler: "lms1815formhandler",
		            data: {
		                formAction: "findCstate"
		            }
		        }).done(function(responseData){
		        	$("#elfCState").val(responseData.elfCState);
		        	$("#cState").html(DOMPurify.sanitize(responseData.cState));
		            if (responseData.returnVal) {
		                CommonAPI.showMessage(responseData.returnVal);
		            } else {
		            	CommonAPI.confirmMessage(i18n.lms1815m01['err.noStatus'],function(b){
		            		if(b){
		            			$.ajax({
		            	            type: "POST",
		            	            handler: "lms1815formhandler",
		            	            action : "resetCstatus",
		            	            data: {
		            	                mainOid : responseJSON.mainOid,
		            	                page : responseJSON.page
		            	            }
		            	        }).done(function(obj){
		            	        	if($("#L180M02BForm")){
		                	        	$("#L180M02BForm").setData(obj);
		            	        	}
		            	        });
		            		}
		            	});
		            }
		        });
		    });

    


    function padLeft(str,lenght){
    	str = str + "";
        if(str.length >= lenght)
            return str;
        else
            return padLeft("0" +str,lenght);
    }

    
    function saveData(showMsg,tofn){
    	if (responseJSON.page == "02") {
            if (!$("#L180M02BForm").valid()) {
                return;
            }
            if($("#elfNextNwDt").val() != ''){
            	//判斷起始日期是否大於迄至日期
                var today = new Date();

                var elfNextNwDt = $("#elfNextNwDt").val().split("-");
                var todayM = "" + today.getFullYear() + padLeft((today.getMonth() + 1) + "",2) +  padLeft(today.getDate() + "",2);
                
                if (parseInt(todayM,10)>parseInt("" + elfNextNwDt[0] + elfNextNwDt[1] + elfNextNwDt[2],10)){
                	 return CommonAPI.showMessage(i18n.lms1815m01["err.check11"]);
                }
            }

        }
        $.ajax({
            type: "POST",
            handler: "lms1815formhandler",
            data: {
                formAction: "saveMain",
                page: responseJSON.page,
                txCode: responseJSON.txCode,
                showMsg: showMsg,
                oid: $("#oid").val(),
				L180M02AForm:JSON.stringify($("#L180M02AForm").serializeData())
            }
            }).done(function(responseData){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                if (responseJSON.page == "01") {
                    $('#L180M02AForm').injectData(responseData);
                    if (responseData.uCase && responseData.uCase == 'Y') {
                        $('#radio2_chk').show();
                    } else {
                        $('#radio2_chk').hide();
                        $('input[name=uCaseRole]').prop('checked','');
                    }
                    $('#creator').val(responseData.creator);
                    $('#updater').val(responseData.updater);
                }
                if ($("#mainOid").val()) {
                    setRequiredSave(false);
                }
                else {
                    setRequiredSave(true);
                }
                responseJSON['oid'] = responseData.oid;
                responseJSON['mainOid'] = responseData.oid;
    	        //執行列印
    	        if (!showMsg && tofn) {
    	             tofn();
    	        }
        });
    }
    
    function btnSend(){
    	if (responseJSON.page == "02") {
            if (!$("#L180M02BForm").valid()) {
                return;
            }
        }
        sendBoss();
    }
    
    function btnAccept(){
    	$("#sendBox").thickbox({
            title: i18n.lms1815m01["button.accept"],
            width: 200,
            height: 100,
            modal: false,
            align: "center",
            valign: "bottom",
            i18n: i18n.lms1815m01,
            buttons: {
                "ok": function(){
                    $.thickbox.close();
                    if ($("input[name='send']:checked").val() == 1) {
                        flowAction({
                            flowAction: true
                        });
                    } else if ($("input[name='send']:checked").val() == 2) {
                        flowAction({
                            flowAction: false
                        });
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
});
$.extend(window.tempSave, {
    handler: "lms1815formhandler",
    action: "tempSave",
    beforeCheck : function(){
    	if(responseJSON.page == "01"){
    		if($("#custId").val() == "" || $("#dupNo").val() == ""){
    			CommonAPI.showMessage(i18n.lms1815m01["enterCustId"]);
    			return false;
    		}
    	}
    	return true;
    },
    sendData: function(){
        if (responseJSON.page == "01") {
            return $("#L180M02AForm").serializeData()
        }
    }
});


function flowAction(sendData){
    $.ajax({
        type: "POST",
        handler: "lms1815formhandler",
        data: $.extend({
            formAction: "flowAction",
            txCode: responseJSON.txCode,
            oid: $("#oid").val()
        }, (sendData || {}))
        }).done(function(){
            CommonAPI.triggerOpener("gridview", "reloadGrid");
            setCloseConfirm(false);
            window.close();
    });
}

//=============================================
//呈主管 -  編製中
function sendBoss(){
	$.ajax({
        type: "POST",
        handler: "lms1815formhandler",
        action : "checkMain",
        data: {
            mainOid: responseJSON.mainOid
        }
        }).done(function(obj){
        	if(obj.success){
        	    //confirmApply=是否呈主管覆核？
        	    CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b){
        	        if (b) {
                    	flowAction();
        	        }
        	    });
        }
    });
}
