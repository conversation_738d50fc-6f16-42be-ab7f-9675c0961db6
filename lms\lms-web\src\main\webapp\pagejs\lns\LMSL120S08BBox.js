var init120s08Box = {
	fhandle : "lms1201formhandler",
	ghandle : "lms1201gridhandler",
	fhandle140 : "lms1401m01formhandler",
	ghandle140 : "lms1405gridhandler",
	defButton : {
		"close" : function() {
			$.thickbox.close();
		}
	}
};

var L120s08BoxAPI = {
	mainGridId : "#gridviewPrice",
	/**
	 * 觸發主檔Grid更新
	 */
	_triggerMainGrid : function() {
		$(L120s08BoxAPI.mainGridId).trigger('reloadGrid');
	},
    /** 選取額度序號 */
	gridviewConnomOtherSelect : function() {
		if ($("#gridviewConnomOtherSelect").length) {
			$("#gridviewConnomOtherSelect").iGrid({
				handler : init120s08Box.ghandle140,
				sortname : 'cntrNo',
				sortorder : 'asc',
				colModel : [ {
					colHeader : i18n.lms1401s03["L140M01a.cntrNo"],// "額度序號",
					name : 'cntrNo',
					align : "center",
					width : 80,
					sortable : true
				}, {
					colHeader : "oid",
					name : 'oid',
					hidden : true
				}, {
					colHeader : "mainId",
					name : 'mainId',
					hidden : true
				} ]
			});
		}
	},
    btnApplyRateDscr : function(versionDate) {
		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		if ($("#gridviewConnomOtherSelect").length) {
			$("#gridviewConnomOtherSelect").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
				postData : {
					formAction : "queryL140m01aCntrNoForL120S08",
					mainId : responseJSON.mainId,
					itemType : "1",
					custId : $L120S08Form.find("#custId").val(),
					dupNo : $L120S08Form.find("#dupNo").val()
				},
				search : true
			}).trigger("reloadGrid");	
		}

		var queryMethod = "queryL140m01bItemType2";
		// 2023版拆出自己一個handler method
		if (versionDate == "2023-03-01") {
			queryMethod = "queryL140m01bItemType2_2023";
		}
		 
		$("#commonNumOtherSelectBox").thickbox({
            // L120S08A.title3=選擇額度序號
            title : i18n.lms1401s03["L120S08A.title3"],
            width : 550,
            height : 300,
            modal : true,
            readOnly : false,
            align : "center",
            valign : "bottom",
            i18n : i18n.def,
            buttons : {
                "sure" : function() {
                    var girdId = $("#gridviewConnomOtherSelect").getGridParam('selrow');
                    var data = $("#gridviewConnomOtherSelect").getRowData(girdId);
                    if (!girdId) {
                        // L120S08A.error05=請選擇一個額度序號
                        return CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error05"]);
                    }

                    // 取得選取的額度序號之利費率資料
                    $.ajax({
                        handler : init120s08Box.fhandle,
                        data : {// 把資料轉成json
                            formAction : queryMethod, // 利(費)率
                            mainId : responseJSON.mainId,
                            cntrNo : data.cntrNo,
                            cntrOid : data.oid,
                            curr : $L120S08Form.find("#curr").val(),
                            cntrMainId : data.mainId,
                            versionDate : versionDate,
                            isNoL120s01c : versionDate == "2023-03-01" ? $L120S08Form.find('input[name="isNoL120s01c_dscr"]:checked').val() : ''
                        }
					}).done(function(obj140) {
						$L120S08Form.find("#rateDscr").val(obj140.itemDscr);

						if (versionDate == "2023-03-01") {
							
							// J-110-0498 優化eloan企金授信系統之額度明細表利率合理性分析編輯功能
							if (obj140.baseRate1) {	// 資金成本率
								$L120S08Form.find("#baseRate1").val(obj140.baseRate1);
							}
							if (obj140.baseRate2) { // 營運成本率
								$L120S08Form.find("#baseRate2").val(obj140.baseRate2);
							}
							if (obj140.baseRate3) { // 預期損失率
								$L120S08Form.find("#baseRate3").val(obj140.baseRate3);
							}
							if (obj140.minusBaseRate1) { // -資金成本率(計算區塊的)
								$L120S08Form.find("#minusBaseRate1").val(obj140.minusBaseRate1);
							}
							if (obj140.minusBaseRate2) { // -營運成本率(計算區塊的)
								$L120S08Form.find("#minusBaseRate2").val(obj140.minusBaseRate2);
							}
							
							// 一大段有擔無擔不同情況的判斷.....start
							if ((obj140.hasSShort || obj140.hasSLong) && !(obj140.hasNShort || obj140.hasNLong)){
								// 有擔任一有值且無擔皆沒值
								if (obj140.hasSShort){ // -有擔短期最低利潤率
									$L120S08Form.find("#minRate_short_disYearRateS").val(obj140.minRate_S_short);
									L120s08BoxAPI.insertMinRateRmk(true, false, false, false, obj140, $L120S08Form);
								}
								if (obj140.hasSLong){ // -有擔中長期最低利潤率
									$L120S08Form.find("#minRate_long_disYearRateS").val(obj140.minRate_S_long);
									L120s08BoxAPI.insertMinRateRmk(false, true, false, false, obj140, $L120S08Form);
								}
								CommonAPI.iConfirmDialog({
									message: "是否同時引進無擔最低利潤?",
									buttons: API.createJSON([{
										key: i18n.def.yes,
										value: function(){
											$.thickbox.close();
											if (obj140.hasSShort){ // -因有	有擔短期所以引無擔短期最低利潤率
												$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short);
												L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
											}
											if (obj140.hasSLong){ // -因有	有擔中長期所以引無擔中長期最低利潤率
												$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long);
												L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
											}
										}
									}, {
										key: i18n.def.no,
										value: function(){
											// do nothing
											$.thickbox.close();
										}
									}])
								});
							}else if(!(obj140.hasSShort || obj140.hasSLong) && (obj140.hasNShort || obj140.hasNLong)){
								// 無擔任一有值且有擔皆沒值
								CommonAPI.iConfirmDialog({
									message: "是否有提供本行存款為十足擔保，或依主管機關規定不得帳列擔保科目，但對本行債權具足額保障者?",
									buttons: API.createJSON([{
										key: i18n.def.yes,
										value: function(){
											$.thickbox.close();
											if (obj140.hasNShort){ // -因有	無擔短期所以引有擔短期最低利潤率
												$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short);
												$L120S08Form.find("#minRate_short_disYearRateS").val(obj140.minRate_S_short);
												L120s08BoxAPI.insertMinRateRmk(true, false, true, false, obj140, $L120S08Form);
											}
											if (obj140.hasNLong){ // -因有	無擔中長期所以引有擔中長期最低利潤率
												$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long);
												$L120S08Form.find("#minRate_long_disYearRateS").val(obj140.minRate_S_long);
												L120s08BoxAPI.insertMinRateRmk(false, true, false, true, obj140, $L120S08Form);
											}
										}
									}, {
										key: i18n.def.no,
										value: function(){
											$.thickbox.close();
											if (obj140.hasNShort){ 
												$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short);
												L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
											}
											if (obj140.hasNLong){
												$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long);
												L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
											}
										}
									}])
								});
							}else{
								// 有擔任一有值，無擔任一有值，不做處理
								if (obj140.hasSShort){ // -有擔短期最低利潤率
									$L120S08Form.find("#minRate_short_disYearRateS").val(obj140.minRate_S_short);
									L120s08BoxAPI.insertMinRateRmk(true, false, false, false, obj140, $L120S08Form);
								}
								if (obj140.hasSLong){ // -有擔中長期最低利潤率
									$L120S08Form.find("#minRate_long_disYearRateS").val(obj140.minRate_S_long);
									L120s08BoxAPI.insertMinRateRmk(false, true, false, false, obj140, $L120S08Form);
								}
								if (obj140.hasNShort){ // -無擔短期最低利潤率
									$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short);
									L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
								}
								if (obj140.hasNLong){ // -無擔中長期最低利潤率
									$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long);
									L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
								}
							}
							// 一大段有擔無擔不同情況的判斷.....end
							
							// 2023-03-01版本先照2022跑一次後，在把一些新欄位補上去
							// 2023版本特殊的!!
							$L120S08Form.find("#spanLostDiffRate_dscr").val(obj140.lostDiffRate);
							$L120S08Form.find("#spanPDLostDiffRate_dscr").val(obj140.pdLostRate);
							$L120S08Form.find("#spanLGDLostDiffRate_dscr").val(obj140.lgdLostRate);
							
							$L120S08Form.find("#minRate_short_dscr").val(obj140.minRate_short_dscr);// 最低利潤率 (表訂)的簡要說明
							
							$L120S08Form.find("#lostDiffRate_dscr").val(obj140.lostDiffRate_dscr);
							$L120S08Form.find("#lostDiffRate").val(obj140.lostDiffRate);// 2023版只剩一個欄位，不分有/無擔
							
							$L120S08Form.find("#spanMangerRate_dscr").val(obj140.mangerRate);// 經權不得低於XX%
							$L120S08Form.find("#badDebtLostRate").val(obj140.badDebtLostRate);// 呆帳損失率(平均預期損失率+預期損失差異率)
							
						}else if(versionDate == "2022-02-22"){
							// J-110-0498 優化eloan企金授信系統之額度明細表利率合理性分析編輯功能
							if (obj140.baseRate1) {	// 資金成本率
								$L120S08Form.find("#baseRate1").val(obj140.baseRate1);
							}
							if (obj140.baseRate2) { // 營運成本率
								$L120S08Form.find("#baseRate2").val(obj140.baseRate2);
							}
							if (obj140.baseRate3) { // 預期損失率
								$L120S08Form.find("#baseRate3").val(obj140.baseRate3);
							}
							if (obj140.ftpRate) { // -FTP
							    /*  2021/05/07  陳美雅襄理說 不要帶出數字讓分行自己key
								$L120S08Form.find("#ftpRate_disYearRateS").val(obj140.ftpRate);
								$L120S08Form.find("#ftpRate_disYearRateN").val(obj140.ftpRate);
								*/
							}
							if (obj140.minusBaseRate2) { // -營運成本率
								$L120S08Form.find("#minusBaseRate2").val(obj140.minusBaseRate2);
							}
							
							// J-110-0498 優化eloan企金授信系統之額度明細表利率合理性分析編輯功能
							if (obj140.lostDiffRate) { // -擬制呆帳損失差異率
								$L120S08Form.find("#lostDiffRate_disYearRateS").val(obj140.lostDiffRate);// 有擔
								$L120S08Form.find("#lostDiffRate_disYearRateN").val(obj140.lostDiffRate);// 無擔
							}
							
							// 一大段有擔無擔不同情況的判斷.....start
							if ((obj140.hasSShort || obj140.hasSLong) && !(obj140.hasNShort || obj140.hasNLong)){
								// 有擔任一有值且無擔皆沒值
								if (obj140.hasSShort){ // -有擔短期最低利潤率
									$L120S08Form.find("#minRate_short_disYearRateS").val(obj140.minRate_S_short);
									L120s08BoxAPI.insertMinRateRmk(true, false, false, false, obj140, $L120S08Form);
								}
								if (obj140.hasSLong){ // -有擔中長期最低利潤率
									$L120S08Form.find("#minRate_long_disYearRateS").val(obj140.minRate_S_long);
									L120s08BoxAPI.insertMinRateRmk(false, true, false, false, obj140, $L120S08Form);
								}
								CommonAPI.iConfirmDialog({
									message: "是否同時引進無擔最低利潤?",
									buttons: API.createJSON([{
										key: i18n.def.yes,
										value: function(){
											$.thickbox.close();
											CommonAPI.iConfirmDialog({
							        			message: "LTV(授信總額度/擔保品估值合計數)是否>100%？",
							        			buttons: API.createJSON([{
							        				key: i18n.def.yes,
							        				value: function(){
							        					// LTV > 100%
														if (obj140.hasSShort){ // -因有	有擔短期所以引無擔短期最低利潤率
															$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short_more100);
															L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
														}
														if (obj140.hasSLong){ // -因有	有擔中長期所以引無擔中長期最低利潤率
															$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long_more100);
															L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
														}
							        					$.thickbox.close();
							        				}
							        			}, {
							        				key: i18n.def.no,
							        				value: function(){
							        					// LTV <= 100%
							        					if (obj140.hasSShort){ // -因有	有擔短期所以引無擔短期最低利潤率
															$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short_less100);
															L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
														}
							        					if (obj140.hasSLong){ // -因有	有擔中長期所以引無擔中長期最低利潤率
															$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long_less100);
															L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
														}
							        					$.thickbox.close();
							        				}
							        			}])
							        		});
										}
									}, {
										key: i18n.def.no,
										value: function(){
											// do nothing
											$.thickbox.close();
										}
									}])
								});
							}else if(!(obj140.hasSShort || obj140.hasSLong) && (obj140.hasNShort || obj140.hasNLong)){
								// 無擔任一有值且有擔皆沒值
								CommonAPI.iConfirmDialog({
									message: "是否有提供本行存款為十足擔保，或依主管機關規定不得帳列擔保科目，但對本行債權具足額保障者?",
									buttons: API.createJSON([{
										key: i18n.def.yes,
										value: function(){
											$.thickbox.close();
											CommonAPI.iConfirmDialog({
												message: "LTV(授信總額度/擔保品估值合計數)是否>100%？",
												buttons: API.createJSON([{
													key: i18n.def.yes,
													value: function(){
														// LTV > 100%
														if (obj140.hasNShort){ // -因有	無擔短期所以引有擔短期最低利潤率
															$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short_more100);
															$L120S08Form.find("#minRate_short_disYearRateS").val(obj140.minRate_S_short);
															L120s08BoxAPI.insertMinRateRmk(true, false, true, false, obj140, $L120S08Form);
														}
														if (obj140.hasNLong){ // -因有	無擔中長期所以引有擔中長期最低利潤率
															$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long_more100);
															$L120S08Form.find("#minRate_long_disYearRateS").val(obj140.minRate_S_long);
															L120s08BoxAPI.insertMinRateRmk(false, true, false, true, obj140, $L120S08Form);
														}
														$.thickbox.close();
													}
												}, {
													key: i18n.def.no,
													value: function(){
														// LTV <= 100%
														if (obj140.hasNShort){ // -因有	無擔短期所以引有擔短期最低利潤率
															$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short_less100);
															$L120S08Form.find("#minRate_short_disYearRateS").val(obj140.minRate_S_short);
															L120s08BoxAPI.insertMinRateRmk(true, false, true, false, obj140, $L120S08Form);
														}
														if (obj140.hasNLong){ // -因有	無擔中長期所以引有擔中長期最低利潤率
															$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long_less100);
															$L120S08Form.find("#minRate_long_disYearRateS").val(obj140.minRate_S_long);
															L120s08BoxAPI.insertMinRateRmk(false, true, false, true, obj140, $L120S08Form);
														}
														$.thickbox.close();
													}
												}])
											});
										}
									}, {
										key: i18n.def.no,
										value: function(){
											$.thickbox.close();
											CommonAPI.iConfirmDialog({
												message: "LTV(授信總額度/擔保品估值合計數)是否>100%？",
												buttons: API.createJSON([{
													key: i18n.def.yes,
													value: function(){
														// LTV > 100%
														if (obj140.hasNShort){ 
															$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short_more100);
															L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
														}
														if (obj140.hasNLong){
															$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long_more100);
															L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
														}
														$.thickbox.close();
													}
												}, {
													key: i18n.def.no,
													value: function(){
														// LTV <= 100%
														if (obj140.hasNShort){
															$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short_less100);
															L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
														}
														if (obj140.hasNLong){
															$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long_less100);
															L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
														}
														$.thickbox.close();
													}
												}])
											});
										}
									}])
								});
							}else{
								// 有擔任一有值，無擔任一有值，不做處理
								if (obj140.hasSShort){ // -有擔短期最低利潤率
									$L120S08Form.find("#minRate_short_disYearRateS").val(obj140.minRate_S_short);
									L120s08BoxAPI.insertMinRateRmk(true, false, false, false, obj140, $L120S08Form);
								}
								if (obj140.hasSLong){ // -有擔中長期最低利潤率
									$L120S08Form.find("#minRate_long_disYearRateS").val(obj140.minRate_S_long);
									L120s08BoxAPI.insertMinRateRmk(false, true, false, false, obj140, $L120S08Form);
								}
								CommonAPI.iConfirmDialog({
									message: "LTV(授信總額度/擔保品估值合計數)是否>100%？",
									buttons: API.createJSON([{
										key: i18n.def.yes,
										value: function(){
											// LTV > 100%
											if (obj140.hasNShort){ // -無擔短期最低利潤率
												$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short_more100);
												L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
											}
											if (obj140.hasNLong){ // -無擔中長期最低利潤率
												$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long_more100);
												L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
											}
											$.thickbox.close();
										}
									}, {
										key: i18n.def.no,
										value: function(){
											// LTV <= 100%
											if (obj140.hasNShort){ // -無擔短期最低利潤率
												$L120S08Form.find("#minRate_short_disYearRateN").val(obj140.minRate_N_short_less100);
												L120s08BoxAPI.insertMinRateRmk(false, false, true, false, obj140, $L120S08Form);
											}
											if (obj140.hasNLong){ // -無擔中長期最低利潤率
												$L120S08Form.find("#minRate_long_disYearRateN").val(obj140.minRate_N_long_less100);
												L120s08BoxAPI.insertMinRateRmk(false, false, false, true, obj140, $L120S08Form);
											}
											$.thickbox.close();
										}
									}])
								});
							}
							// 一大段有擔無擔不同情況的判斷.....end
							
						}else if (versionDate == "2021-05-01") {
							// J-105-0311-001 Web
							// e-loan合理性分析表中的基礎放款利率欄位新增引進功能
							if (obj140.baseRate1) {	// 資金成本率
								$L120S08Form.find("#baseRate1").val(obj140.baseRate1);
							}
							if (obj140.baseRate2) { // 營運成本率
								$L120S08Form.find("#baseRate2").val(obj140.baseRate2);
							}
							if (obj140.baseRate3) { // 預期損失率
								$L120S08Form.find("#baseRate3").val(obj140.baseRate3);
							}
							if (obj140.ftpRate) { // -FTP
							    /*  2021/05/07  陳美雅襄理說 不要帶出數字讓分行自己key
								$L120S08Form.find("#ftpRate_disYearRateS").val(obj140.ftpRate);
								$L120S08Form.find("#ftpRate_disYearRateN").val(obj140.ftpRate);
								*/
							}
							if (obj140.minusBaseRate2) { // -營運成本率
								$L120S08Form.find("#minusBaseRate2").val(obj140.minusBaseRate2);
							}
						} else {
							if (obj140.baseRate) {
								$L120S08Form.find("#baseRate").val(obj140.baseRate);
							}
						}
						if (obj140.msg) {
						    return CommonAPI.showMessage(obj140.msg);
						}
                    }); // close ajax

                    $.thickbox.close();
                },
                "cancel" : function() {
                    $.thickbox.close();
                }
            }
        });
	},
	insertMinRateRmk : function(minRateSShort, minRateSLong, minRateNShort, minRateNLong, obj140, $L120S08Form){
		if(minRateSShort){
			if(obj140.needPlusRate){
				$L120S08Form.find("#impMinRate_short_disYearRateS_rmk").val(obj140.needPlusRate);
			}else{
				$L120S08Form.find("#impMinRate_short_disYearRateS_rmk").val('');
			}
		}
		if(minRateSLong){
			if(obj140.needPlusRate){
				$L120S08Form.find("#impMinRate_long_disYearRateS_rmk").val(obj140.needPlusRate);
			}else{
				$L120S08Form.find("#impMinRate_long_disYearRateS_rmk").val('');
			}
		}if(minRateNShort){
			if(obj140.needPlusRate){
				$L120S08Form.find("#impMinRate_short_disYearRateN_rmk").val(obj140.needPlusRate);
			}else{
				$L120S08Form.find("#impMinRate_short_disYearRateN_rmk").val('');
			}
		}if(minRateNLong){
			if(obj140.needPlusRate){
				$L120S08Form.find("#impMinRate_long_disYearRateN_rmk").val(obj140.needPlusRate);
			}else{
				$L120S08Form.find("#impMinRate_long_disYearRateN_rmk").val('');
			}
		}
	},
    btnClear : function(sType, versionDate) {
        var verNo = versionDate.replace(/-/g, "");
        var L120S08FormName = "L120S08Form_"+verNo;
        var $L120S08Form = $("#"+L120S08FormName);

        $("form[id='"+L120S08FormName+"'] input[id*='_disYearRate" + sType + "']").each(function() {
            $(this).val('');
        });
        
        // J-110-0498 優化eloan企金授信系統之額度明細表利率合理性分析編輯功能
        // 開舊版起來因為是跑each，沒有符合的span也不會出錯
        $("form[id='"+L120S08FormName+"'] span[id*='_disYearRate" + sType + "_rmk']").each(function() {
            $(this).val('');
        });
    },
    /** 以「各等級擬制呆帳損失差異率加碼」顯示
     * J-107-0077-001 Web e-Loan 授信案件簽報書之「新臺幣、美元利率合理性分析表」修改
     * */
    btnShowLostDiffRate : function(versionDate) {
        var verNo = versionDate.replace(/-/g, "");
        var L120S08FormName = "L120S08Form_"+verNo;
        var $L120S08Form = $("#"+L120S08FormName);

        $L120S08Form.find("#riskAdd_dscr").val('');
        $L120S08Form.find("#riskAdd_disYearRateS").val('');
        $L120S08Form.find("#riskAdd_disYearRateN").val('');
        $L120S08Form.find("#showRiskAdd").hide();
        $L120S08Form.find("#showLostDiffRate").show();
    }
};