var initDfd = $.Deferred();
var _handler = "cls3801m01formhandler";

$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;
	
	$.form.init({
        formId: "tabForm",
        formHandler: _handler,
        formAction: "queryC103M01A",
        loadSuccess: function(json){
        	if(json.mainDocStatus == '02O' || json.mainDocStatus == '05O') {//待覆核、已覆核
				tabForm.lockDoc();
				initControl_lockDoc = true;
			}
        	
        	tabForm.injectData(json);
        	
        	tabForm.find("input[name='custType'][value='" + encodeURI(json.custType) + "']:visible").prop('checked',true);
        	tabForm.find("input[name='transPotential'][value='" + encodeURI(json.transPotential) + "']:visible").prop('checked',true);
        	
        	tabForm.find("#cityarea").setOptions("counties"+json.city, false);
        	tabForm.find("#cityarea").val(json.cityarea);
        	
        	if(json.oldCustBusiness){
        		$("[name=oldCustBusiness]").val(json.oldCustBusiness.split("|"));
        	}
        	if(json.potentialMain){
        		$("[name=potentialMain]").val(json.potentialMain.split("|"));
        	}
        	if(json.potentialCommon){
        		$("[name=potentialCommon]").val(json.potentialCommon.split("|"));
        	}
        	if(json.insurance){
        		$("[name=insurance]").val(json.insurance.split("|"));
        	}
        	
        	initDfd.resolve(json);
        }
    });
	
	//================================
	var item;
	
	//================================
	//事件
	$("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. ' // +i18n.cls1161m01['manager.L3']
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" />';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }
        
    });
	
	var saveAction = function(opts){
        if (tabForm.valid()) {
        	$("input[type=hidden][name=oldCustBusiness]").remove();
        	$("input[type=hidden][name=potentialMain]").remove();
        	$("input[type=hidden][name=potentialCommon]").remove();
        	$("input[type=hidden][name=insurance]").remove();
        	return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    page: responseJSON.page,
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
					)
				}).done(function(json){
					tabForm.injectData(json);
					//更新 opener 的 Grid
					API.triggerOpener();
				});

        	
        } else {
            return $.Deferred();
        }
    }
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
				)
			}).done(function(json){
				API.triggerOpener();
				window.close();
			});
	}
	
	btnPanel.find("#btnSave").click(function(){
		saveAction({'allowIncomplete':'Y','checkSave':'Y'}).done(function(json){
    		if(json.saveOkFlag){
    			var dyna = [];	
    			if(true){
    				dyna.push(i18n.def.saveSuccess);
    			}
    			if(json.IncompleteMsg){
    				dyna.push(json.IncompleteMsg);
    			}	
    			if(json.alertMsg){
    				dyna.push(json.alertMsg);
    			}
    			API.showMessage(dyna.join("<br/>-------------------<br/>"));
    		}
    	});
	}).end().find("#btnSend").click(function(){
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag && !json_saveAction.alertMsg){
    			API.confirmMessage(i18n.def.confirmApply, function(result){
    	            if (result) {
    	            	$.ajax({
    	                    handler: _handler,
    	                    action: "checkData",
    	                    data: {}
							}).done(function(json){
								$('#bossItem').empty();
    	                        item = json.bossList;
    	                        var bhtml = '<select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
    	                        $('#bossItem').append(bhtml).find('select').each(function(){
    	                            $(this).setItems({
    	                                item: item,
    	                                format: "{value} {key}"
    	                            });
    	                        });
    	                        
    	                        $("#selectBossBox").thickbox({                                    
                                    title: "",
                                    width: 500,
                                    height: 300,
                                    modal: true,
                                    readOnly: false,
                                    valign: "bottom",
                                    align: "center",
                                    i18n: i18n.def,
                                    buttons: {
                                        "sure": function(){
                                        
                                            var selectBoss = $("select[name^=boss]").map(function(){
                                                return $(this).val();
                                            }).toArray();
                                            
                                            for (var i in selectBoss) {
                                                if (selectBoss[i] == "") {
                                                    return CommonAPI.showErrorMessage(i18n.cls3801m01['msg.01'] + i18n.cls3801m01['C103M01E.bossId']);
                                                }
                                            }
                                            // 驗證是否有重複的主管
                                            if (checkArrayRepeat(selectBoss)) {
                                                // 主管人員名單重複請重新選擇
                                                return CommonAPI.showErrorMessage(i18n.cls3801m01['msg.02']);
                                            }
                                            
                                            flowAction( $.extend(
                                            		{'decisionExpr':'呈主管'}
                                            		, {'saveData':'Y', 'selectBoss': selectBoss} //, 'manager':$("#manager").val()
                                            	) 
                                            	);           
                                        },
                                        
                                        "cancel": function(){
                                            $.thickbox.close();
                                        }
                                    }
                                }); // thickbox close
							});
						}
					});
    		}else{
    			if(json_saveAction.alertMsg){
					var dyna = '';
    				dyna = json_saveAction.alertMsg;
    				CommonAPI.showErrorMessage(dyna);
    			}
    		}
    	});
		
	}).end().find("#btnCheck").click(function(){
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");

			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.def['accept']+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.def['return']+"</label></p>");

			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	flowAction({'decisionExpr':'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}).end().find("#btnPrint").click(function(){
	        if (!$("#buttonPanel").find("#btnSave").is("button")) {
	        	printC103M01A();
	        }
	        else {
	            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
	            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
	                if (b) {
	                	saveAction({'allowIncomplete':'Y','checkSave':'Y'}).done(function(json){
        					if(json.alertMsg){
        						var dyna = '';
        						dyna = i18n.def.saveSuccess + "<br/>-------------------<br/>" + json.alertMsg;
        	    				CommonAPI.showPopMessage('', dyna, printC103M01A);
        	    			}else{
        	    				printC103M01A();
        	    			}
        				});
	                }
	            });
	        }
	});
	
	
	tabForm.find("#city").click(function(){
		tabForm.find("#cityarea").setOptions("counties"+$(this).val(),false);
	}).end().find("input[id^=custType_]").change(function(){
		if(tabForm.find("[name='custType']:checked").val() == 'N'){
			tabForm.find("#oldCustBusiness1,#oldCustBusiness2,#oldCustBusiness3,#oldCustBusiness4,#oldCustBusiness5").prop("checked",false);
			tabForm.find("#otherBusiness").val("");
		}
	}).end().find("input[id^=transPotential_]").change(function(){
		if(tabForm.find("[name='transPotential']:checked").val() == 'Y'){
			tabForm.find("#notTransReason").val("");
		}
	});
	
	// 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }
	
    function printC103M01A(){
    	$.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
            	mainOid: $("#mainOid").val(),
            	mainId: $("#mainId").val(),         
                fileDownloadName: "cls3801r01.pdf",
                serviceName: "cls3801r01rptservice"                        
            }
        });
    }

});
///////////////////////

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true	
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});
