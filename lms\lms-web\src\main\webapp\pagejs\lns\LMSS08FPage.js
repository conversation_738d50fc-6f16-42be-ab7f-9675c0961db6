/**
 * 案件報告表共用js
 */
var initS08fJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function() {
		if (responseJSON.docURL == "/lms/lms1201m01") {
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		} else if (responseJSON.docURL == "/lms/lms1101m01") {
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		} else if (responseJSON.docURL == "/lms/lms1211m01") {
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		} else if (responseJSON.docURL == "/lms/lms1111m01") {
			this.handlerName = "lms1111formhandler";
		} else {
			this.handlerName = "lms1301formhandler";
		}
	}
};

initDfd.done(function() {
	// 2012_07_20_rex add 取得sso 連線資訊
	BrowserAction.init();
	setCloseConfirm(true);
	initS08fJson.setHandler();
	
	var thickboxCaseRpt = $("#thickboxCaseRpt");
	if(thickboxOptions.readOnly){
		thickboxCaseRpt.find("#caseRptBtnAdd").hide();
		thickboxCaseRpt.find("#caseRptBtnDel").hide();
	}
	else{
		thickboxCaseRpt.find("#caseRptBtnAdd").show();
		thickboxCaseRpt.find("#caseRptBtnDel").show();	
	}	
	
	//案件報告表清單
	var caseRptGrid = thickboxCaseRpt.find("#caseRptGrid").iGrid({
		handler : 'lms1201gridhandler',
		height : 240,
		needPager : false,
		postData : {
			gridMainId : responseJSON.mainId,
			gridUid : responseJSON.mainId,
			formAction : 'queryCaseInfo'
		},
		caption : i18n.lmss08a['L120S08F.title'],//'案件報告表',
		colModel : [ {
			colHeader : i18n.lmss08a['L120S08.grid17'],// "統一編號",
			name : 'custValue',
			width:50,
			sortable : true
		}, {
			colHeader : i18n.lmss08a['L120S08.grid18'],//"姓名"
			width:140,
			name : 'custName'
		}, {
			colHeader : i18n.lmss08a['L120S08F.grid.grantType'],//'類型'
			width:80,
			name : 'grantType'
		}, {
			colHeader : i18n.lmss08a['L120S08F.grid.caseNo'],// 案件編號,
			name : 'caseNo'
		}, {
			name : 'oid',
			hidden : true
		}, {
			name : 'mainId',
			hidden : true
		}, {
			name : 'custId',
			hidden : true
		}, {
			name : 'pid',
			hidden : true
		} ],
		ondblClickRow : function(rowid) {
			openCaseRpt(caseRptGrid.getRowData(rowid));
		}
	});
	
	var borrowGrid = thickboxCaseRpt.find("#borrowGrid").iGrid({
        handler: 'lms1201gridhandler',
        height: 270,
        postData: {
        	mainId : responseJSON.mainId,
            formAction: "queryBorrower"
        },
        rownumbers: true,
        rowNum: 100,
        colModel: [{
            colHeader: "&nbsp;", // 主要借款人Flag
            align: "center",
            width:10,
            name: 'keyMan' // col.id
        }, {
            colHeader: i18n.lmss08a['L120S08.grid17'] ,// '身分證統編',
            align: "left",
            width:40,
            name: 'custNo' 
        }, {
            colHeader: i18n.lmss08a['L120S08.grid8'],//'借款人名稱',
            align: "left",
            name: 'custName' 
        }, {
            colHeader: "custId",
            name: 'custId',
            hidden: true
        }, {
            colHeader: "dupNo",
            name: 'dupNo',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "caseNo",
            name: 'caseNo',
            hidden: true
        }, {
            colHeader: "caseDate",
            name: 'caseDate',
            hidden: true
        }, {
            colHeader: "colOid",
            name: 'colOid',
            hidden: true
        }
        ],
        ondblClickRow: function(rowid){ 
        }
    });
	
	//畫面按鈕動作
	thickboxCaseRpt.find("#caseRptBtnAdd").click(function(){
		addCaseRpt(responseJSON.mainId);
    });
	
	thickboxCaseRpt.find("#caseRptBtnDel").click(function(){
    	deleteGridRow(caseRptGrid);
    });	
	
	//刪除案件報告表
	function deleteGridRow(igrid){
		var ret = igrid.getSelRowDatas();
	    if (ret) {
			CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
				if (b) {
		    		CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
						if (b) {
							MegaApi.linkToMS({
							   system : "COL",
							   url : "app/snr/snr1010t47/01",
							   mainId : ret.mainId,
							   stxCode : "522901",
							   data :{
							       lms:true,
							       formAction:'deleteT47'
							   }
							});
							$.thickbox.close();	
						}				
		    		});
				}				
			});
	    }else{
	        CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
	    }
	}
	
	//重新載入案件報告表之Grid
	function reloadCaseRptGrid() {
		$("#caseRptGrid").jqGrid("setGridParam", {
			postData : {
				handler : 'lms1201gridhandler',
				gridMainId : responseJSON.mainId,
				gridUid : responseJSON.mainId,
				formAction : 'queryCaseInfo'
			}
		}).trigger("reloadGrid");
	}

	//新增案件報告表
	function addCaseRpt(lmsMainId){
		$("#caseRptFormAddDialog").thickbox({
	       title : i18n.lmss08a['L120S08F.title.add'],// '案件報告表新增',
	       width :850,
	       height : 450,
	       modal : true,
	       valign : "bottom",
		   align : "center",
		   i18n:i18n.def,
		   readOnly: false,
	       buttons: {
             "sure": function() {
            	 	var $gridT=$("#borrowGrid");
					var gridId =$gridT.getGridParam('selrow');
					var gridData = $gridT.getRowData(gridId);
					var grantType=$("#caseRptFormAdd").find("#grantType").val();
					var ownBrId=responseJSON.ownBrId;

					if(grantType == null || grantType==""){
						CommonAPI.showMessage(i18n.lmss08a['L120S08F.error.grantType']);
						return ;
					}
				   	
					if(gridId == null){
						CommonAPI.showMessage(i18n.lmss08a['L120S08.alert1']);
						return ;
					}else{
						$.thickbox.close();
						
						MegaApi.linkToMS({
						   system : "COL",
						   url : "app/snr/snr1010t47/01",
						   mainId :gridData.colOid,   //unique
						   stxCode : "522901",
						   data :{
						       lms:true,
						       pid: lmsMainId,  //記錄用，可帶你原文件的mainId
						       custId:gridData.custId, 
						       dupNo:gridData.dupNo,
						       custName:gridData.custName,
						       lbranchId:ownBrId, //簽案分行
						       lorgBranchId:ownBrId, //原案件分行
						       
						       lcaseNo:gridData.caseNo, //案號
						       lgrantType:grantType,//性質 CodeType中的grantType :01~07
						       lframingDate:gridData.caseDate //簽案日期
						   }
						});						
					}					
					$.thickbox.close();	
             },            
             "cancel": function() {
            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
 					if(res){
 						$.thickbox.close();
 					}
 		        });
             }
           }
	        });		
	}

	//開啟案件報告表
	function openCaseRpt(ret){
	    if (ret) {
			MegaApi.linkToMS({
			   system : "COL",
			   url : "app/snr/snr1010t47/01",
			   mainId : ret.mainId,
			   stxCode : "522901",
			   data :{
				   lockFlag:thickboxOptions.readOnly,
			       lms:true
			   }
			});	
	    }	
	}

});
