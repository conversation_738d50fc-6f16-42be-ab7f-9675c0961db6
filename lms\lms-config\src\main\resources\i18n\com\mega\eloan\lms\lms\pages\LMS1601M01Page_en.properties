#==================================================
# \u52d5\u7528\u5be9\u6838\u8868 Grid
page.title=LMS1605M01 Drawdown Approval Sheet
L160M01A.mainCustId=Principal Borrower's UBN
L160M01A.mainCust=Principal Borrower
L160M01A.caseNo=Case No.
L160M01A.cntrNo=Serial Number of the Drawn Limit
L160M01A.creatorPerson=Branch Officer
L160M01A.allCanPay=Advance Drawdown
L160M01A.willFinishDate=Scheduled Full Documentation Date
L163M01A.finishDate=Completion Date
L160M01A.approveTime=Approval Date
L163M01A.CheckDate=Verification Completed
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 m01
L160M01A.title01=Drawdown Approval Sheet
L160M01A.title02=Related Reports
L160M01A.title03=Advance Drawdown Approval & Control Sheet
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s01
L160M01A.caseDate=Signature Date
L160M01A.caseLvl=Approval Level
L160M01A.cntrNum=Credit Limit Serial Number
L160M01A.bt01=Select A Credit Facility Report
L160M01A.title07=Business Unit
L160M01A.managerId=Manager/Deputy Manager/Junior Manager
L160M01A.bossId=Credit Supervisor
L160M01A.reCheckId=Approver
L160M01A.apprId=Handling Officer
L160M01A.selfCntrCase=Self-produced Credit Facility Report
L160M01A.contentCntrCase=Credit Facility Report From Syndication Loan Arranger
L160M01A.title08=Please select 1 approved Case Report
L160M01A.message2=Please select a Credit Facility Report for this drawdown
L160M01A.commSno=Serial No. Of Shared Limit
L160M01A.moneyAmt=Applied Credit Limit
L160M01A.type=Nature
L140M01a.type1=New
L140M01a.type2=Renewal
L140M01a.type3=Condition Change
L140M01a.type4=Continued
L140M01a.type5=Increase
L140M01a.type6=Decrease
L140M01a.type7=Unchanged
L140M01a.type8=Cancel
L140M01a.type9=Extension
L140M01a.type10=Distress Support
L140M01a.type11=Advance Renewal
L140M01a.type12=Negotiated Settlement
L140M01a.type13=Quotation
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s02
L160M01A.tType1=Short Term
L160M01A.tType2=Medium/Long Term
L160M01A.tType=Credit Agreement
L160M01A.useDate=Drawdown Period
L160M01A.useFromDate=Drawdown Period
L160M01A.useEndDate=Drawdown Period - Due Date
L160M01A.useMonth=XX months since 1st drawdown date
L160M01A.useMonth1=Since 1st Drawdown Date
L160M01A.useMonth2=Months
L160M01A.useOther=Others
L160M01A.lnDate=Credit Tenor
L160M01A.lnFromDate=XX years & XX months since 1st drawdown date
L160M01A.lnEndDate=Loan Tenor
L160M01A.lnYear=Year
L160M01A.lnOther=Credit Tenor - Others
L160M01A.signDate1=Credit Agreement Signing Date
L160M01A.guCurr=Maximum Guarantee
L160M01A.guAmt=dollars only
L160M01A.guFromDate=Tenor
L160M01A.guEndDate=Maximum 5 Years
L160M01A.dMonth1=Penalty Charge Criteria
L160M01A.dRate1=Penalty Charge Criteria - Interest Rate
L160M01A.dMonth2=Pastdue More Than
L160M01A.dRate2=Charge Penalty
L160M01A.dRateAdd=Late Interest Margin
L160M01A.blackDataDate=Audit Items
L160M01A.queryDataDate=Data Inquiry Date
L160M01A.blackListTxtErr=Blacklist Search
L160M01A.blackListTxtOK=Blacklist Search Result
L160M01A.otherCase=Other Remarks
L160M01A.sign=Approval
L160M01A.comm=Review Opinion
L160M01A.allItem=Bank Common Item
L160M01A.localItem=Special Local Rules
L160M01A.selfItem=Manual Input Item
L160M01A.noNeed=Waived
L160M01A.otherfile=File Attachment
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s03
L160M01A.unitLoanCase=Whether the case contains syndicated limits with peer banks
L160M01A.uCMainBranch=Whether the branch is the managing bank
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 s04
L163M01A.waitingItem=Pending Actions
L163M01A.willFinishDate=Scheduled Full Documentation Date
L163M01A.uploadDate=Advance Drawdown Approval Date
L163M01A.itemTrace=Follow-up Progress
L163M01A.managerId=Class A Supervisor
L163M01A.bossId=Class B Supervisor
L163M01A.appraiserId=Handling Officer
L163M01A.bfReCheckDate=Approval Date
L163M01A.check=Signature Column
L163M01A.no=\u3000
L163M01A.site=\u3000th Persons
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94  \u66ab\u653e
L160M01A.branchId=Branch name
L160M01A.branchName=Branch
L160M01A.typCd=Region/Department
L160M01A.custId=Unified Business Number
L160M01A.dupNo=Repeat Serial No.
L160M01A.custName=Borrower's Name
L160M01A.unitType=Handling Unit's Category
L160M01A.ownBrId=Preparing Unit's ID
L160M01A.randomCode=Document Decode Error
L160M01A.caseGist=Agenda
L160M01A.randomcode=Random Report Code
L160M01A.ownbranch=Affiliate Bank
L160M01A.curr=Currency
L160M01A.allMoney=Total Credit Line
L160M01A.money=Dollars
L160M01A.signDate=Contract Date
L160M01A.slBank=Participating Bank/Branch
L160M01A.slBank01=Local Bank
L160M01A.slBank02=Foreign bank's branch in Taiwan
L160M01A.slBank03=Investment Trust Company
L160M01A.slBank06=Property & Life Insurance Company
L160M01A.slBank12=Foreign Bank
L160M01A.slMaster=Joint Lead Arranger
L160M01A.yes=Yes
L160M01A.no=No
L160M01A.slAccNo=Peer Account Number
L160M01A.slAmt=Allocated Loan Quantum
L160M01A.allUse=Fully Drawn
L160M01A.srcFileName=File Name
L160M01A.fileDesc=File Description
L160M01A.uploadTime=Upload Time
L160M01A.selectBoss=Number Of Credit Supervisors
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 \u9801\u7c64\u6a19\u984c
L160M01A.title09=Credit Facility Report
L160M01A.title10=Syndication Loan Allocation Table
L160M01A.title11=Borrower's & Guarantor's Profile Sheet
L160M01A.title12=Input interface for the Advance Drawdown Control Checklist
L160M01A.title13=Advance Drawdown Approval & Control Sheet
L160M01A.title14=Select 1 Credit Facility Report
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 \u6309\u9215
L160M01A.bt02=Inquiry
L160M01A.bt03=All Complete/Received
L160M01A.bt04=Attachment not required for all
L160M01A.bt05=Select Files To Attach
L160M01A.bt06=Delete
L160M01A.bt07=List of New Syndication Loan Allocated Percentages
L160M01A.bt08=Add Borrower's & Guarantor's Profile Sheet
L160M01A.bt09=Originating Branch
L160M01A.bt10=Import Borrower & Guarantor
L160M01A.bt11=Return to handling officer for correction
L160M01A.bt12=Approve Drawdown
L160M01A.bt13=Advance Drawdown Confirmation
L160M01A.bt14=Verifier
L160M01A.bt15=Printer Setting
L160M01A.bt16=Print All
L160M01A.bt17=Generate New Report
L160M01A.bt18=Close
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 error
L160M01A.error1=Multiple selections are not allowed under this function
L160M01A.error2=Please Select
L160M01A.error3=Please import the Credit Facility Report first
L160M01A.error4=The database contains no bank ID:
L160M01A.error5=Total Credit Line can not be
L160M01A.error6=The shared loan quantum can not be
L160M01A.error7=Please input
L160M01A.error8=The shared loan quantum can not exceed total credit limit
L160M01A.error9=Unable to save because the total credit limit does not equal to the shared loan quantum
L160M01A.error10=Please save first
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94 \u8a0a\u606f
L160M01A.message1=Were all limits under this report utilized? If so, press [OK], other wise press [Cancel] to continue selecting the limit utilization report.
L160M01A.message3=Drawdown all limits under the Case Report
L160M01A.message4=Please make sure to input the following fields, as they can be used to calculate penalty charges when the case goes into hardcore collection. This information is not printed on reports.
L160M01A.message5=If the borrower delays principal or interest payments, then penalty charges will accrue from the maturity date (for principal) and the interest payment date (for interest). For pastdue within
L160M01A.message6=months,
L160M01A.message7=interest is charged at 
L160M01A.message8=months, interest is charged at
L160M01A.message9=Please tick "V" if received (completed), tick "X" if not received (incomplete), and tick "Waived" if not required
L160M01A.message10=For approved cases, application documents need to be photocopied and handed to Accounts, Credit Supervisor, and each circulated unit.
L160M01A.message11=Sight LC, usance LC, and export loan limits are circulated to the Foreign Currency Department; clean bill purchases are circulated to the Foreign Exchange Department; overdraft limits are circulated to the Deposits Department
L160M01A.message12=Circulated Personnel & Units: please sign and randomly check the approved document copies
L160M01A.message13=Accounts (file creation) Officer
L160M01A.message14=Foreign Currency
L160M01A.message15=Foreign Exchange
L160M01A.message16=Deposit
L160M01A.message17=Please input names in English to facilitate blacklist checks
L160M01A.message18=Only the relationship field can be left blank, if the borrower's UBN is the same as the guarantor's UBN; no other fields can be left blank
L160M01A.message19=Article 753-1 of the Civil Law states that
L160M01A.message20=persons who offer their personal guarantees for companies in which they serve as directors, supervisors, or other forms of corporate representatives will only be held liable for the liabilities incurred by their respective companies during active duty
L160M01A.message21=Therefore, if the guarantor's relationship is specified as the borrower's
L160M01A.message22=director, supervisor, or other form of corporate representative,
L160M01A.message23=the [Director's/Supervisor's Term Expiry (Expiry Date For Guarantee Obligation)] field needs to key in the year which the director's/supervisor's term ends
L160M01A.message24=Please switch to \u300eAdvance Drawdown Approval & Control Sheet\u300f Tab and Fill in\u300cScheduled Full Documentation Date\u300d Field\u3002
L160M01A.message25=Not Received (Not Completed)
L160M01A.message26=Add Borrower's & Guarantor's Profile will be cleared, whether press [Confirm] or not (Import Borrower's & Guarantor Profile)
L160M01A.message27=Whether to submit for supervisor's approval
L160M01A.message28="Borrower's & Guarantor's Profile Sheet", not entered yet, can not be submitted for supervisor's approval.
L160M01A.message29="Syndication Loan Allocation Table", not entered yet, can not be submitted for supervisor's approval.
L160M01A.message30="Syndication Loan Allocation Table", all the shared loan quantum of total not equal to total credit limit, can not be submitted for supervisor's approval.
L160M01A.message31=Supervisor's name duplicated; please select again
L160M01A.message32=Whether to return the case to the handling officer for amendments; to return, please press [OK], otherwise press [Cancel]
L160M01A.message34=Whether to proceed with approval and disbursement for this case
L160M01A.message35=Whether the "Advance Drawdown Approval & Control Sheet" is confirmed
L160M01A.message36=Whether to return the case to the handling officer for amending "Completion Date & Follow-up Progress"; to return, please press [OK], otherwise press [Cancel]
L160M01A.message37=Approved Drawdown Period
L160M01A.message38=Please input the approval date
L160M01A.message39=Not On Blacklist
L160M01A.message40=English Name Input Error; the input must be made in half-sized characters
L160M01A.message41=Possibly blacklisted
L160M01A.message42=This case is not syndicated, hence can not change the allocated percentage
L160M01A.message43=Failed to upload file
L160M01A.message44=Maybe is Black List
L160M01A.message45=Credit Limit Serial No. for non-Bank's credit Line no longer permitted & utilization
L160M01A.message46=Loans procedures are complete, We intend to Drawdown granted
L160M01A.message47=Phrases
L160M01A.message48=The related identity should be C. Common borrower, if the borrower's UBN is the same as the guarantor's UBN
L160M01A.message49=No Credit Facility Report found
L160M01A.message50=The relationship field should be left blank, if the borrower's UBN is the same as the guarantor's UBN
L160M01A.message51=Borrowers for\u300cBorrower's & Guarantor's Profile Sheet\u300dbelow, data incomplete, can not be submitted for supervisor's approval{0}
L160M01A.message52=The source of the credit limit serial No. is from Inter-Branch Credit Facility Report, can not perform data correction
L160M01A.message53=Fifth\u3001Among other things, the following items are not received (not completed), shall not be pSubmit For Supervisor's Approval:{0}
# \u4e3b\u5f9e\u50b5\u52d9\u4eba\u8cc7\u6599\u8868\u6a94 L162M01A
L162M01A.custId=Borrower's UBN
L162M01A.dupNo=Borrower's UBN Repeat Code
L162M01A.rId=Guarantor's UBN
L162M01A.rDupNo=Guarantor's UBN Repeat Code
L162M01A.rName=Guarantor's Name
L162M01A.rKindM=Relationship
L162M01A.rKindD=Relationship Sub-category
L162M01A.rCountry=Country
L162M01A.rType=Related Identity
L162M01A.dueDate=Director's/Supervisor's Term Expiry
L162M01A.dueDate2=Expiry Date For Guarantee Obligation
L162M01A.breanch=Type Of Financial Institution
L160M01A.signDate2=3~5 years
L162M01A.guaPercent=Guarantor burden the ratio of the guarantee responsibility
#J-110-0040_05097_B1001 Web e-Loan\u589e\u52a0\u300c\u672c\u884c\u570b\u5bb6\u66b4\u96aa\u662f\u5426\u4ee5\u4fdd\u8b49\u4eba\u570b\u5225\u70ba\u8a08\u7b97\u57fa\u6e96(\u53d6\u4ee3\u6700\u7d42\u98a8\u96aa\u570b\u5225)\u300d\u8a3b\u8a18
L162M01A.guaNaExposure=Whether the bank\u2019s country exposure is calculated based on the guarantor\u2019s country (replaces the final risk country)

# \u4ee5\u4e0b\u70ba\u65b0\u589e\u7684
L160M01A.message54=Credit Limit Serial Number {0} had been marked\u3010Not signed - cancellation of quota\u3011 at {1} and shall not be used\uff0cPlease re-select!!
L160M01A.message55=\u3010\u540c\u610f\u3011\u5171\u7528\u59d3\u540d\u3001\u5730\u5740\u57fa\u672c\u8cc7\u6599
L160M01A.message56=\u3010\u4e0d\u540c\u610f\u3011\u5171\u7528\u59d3\u540d\u3001\u5730\u5740\u57fa\u672c\u8cc7\u6599
L160M01A.message57=\u3010\u540c\u610f\u3011\u5171\u7528\u59d3\u540d\u3001\u5730\u5740\u4ee5\u5916\u4e4b\u57fa\u672c\u8cc7\u6599\u53ca\u5e33\u52d9\u8cc7\u6599
L160M01A.message58=\u3010\u4e0d\u540c\u610f\u3011\u5171\u7528\u59d3\u540d\u3001\u5730\u5740\u4ee5\u5916\u4e4b\u57fa\u672c\u8cc7\u6599\u53ca\u5e33\u52d9\u8cc7\u6599
L160M01A.message65=Syndication Loan Allocation Table of the selected contract no. {0} is an unusual amount of the syndicated loan, please re-select
L160M01A.message91=\u806f\u8cb8\u6848\u53c3\u8cb8\u6bd4\u7387\u4e00\u89bd\u8868\u5146\u8c50\u5206\u884c\u6709\u52fe\u9078\u70ba\u5171\u540c\u4e3b\u8fa6\uff0c\u57fa\u672c\u8cc7\u8a0a\u9801\u7c64-\u300c\u6848\u4ef6\u6027\u8cea\u300d\u6b04\u4f4d\u5247\u5fc5\u9808\u70ba\u4e3b\u8fa6
L160M01A.message102=\u57fa\u672c\u8cc7\u8a0a\u9801\u7c64\u4e4b\u6b04\u4f4d\u300c\u5c6c\u632f\u8208\u7d93\u6fdf\u975e\u4e2d\u5c0f\u4f01\u696d\u5c08\u6848\u8cb8\u6b3e\u91d1\u984d\u300d\u4e0d\u5f97\u5927\u65bc\u73fe\u8acb\u984d\u5ea6\u3002
L160M01A.message103=\u984d\u5ea6\u5e8f\u865f\u300c{0}\u300d\u5c6c\u632f\u8208\u7d93\u6fdf\u975e\u4e2d\u5c0f\u4f01\u696d\u5c08\u6848\u8cb8\u6b3e\uff0c\u5176\u4ed6\u4e8b\u9805\u7b2c27\u9805\u300c\u632f\u8208\u7d93\u6fdf\u975e\u4e2d\u5c0f\u4f01\u696d\u5c08\u6848\u8cb8\u6b3e\u5207\u7d50\u66f8\u300d\u5fc5\u9808\u70ba\u300cV\u300d\u3002
#J-106-0082-001 Web e-Loan\u570b\u5167\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\uff0c\u984d\u5ea6\u660e\u7d30\u8868\u65b0\u589e\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e
L160M01A.message104=\u57fa\u672c\u8cc7\u8a0a\u9801\u7c64\u4e4b\u6b04\u4f4d\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e\u300c\u9031\u8f49\u6027\u652f\u51fa\u91d1\u984d+\u8cc7\u672c\u6027\u652f\u51fa\u91d1\u984d\u300d\u4e0d\u5f97\u5927\u65bc\u300c\u73fe\u8acb\u984d\u5ea6\u300d\u3002
L160M01A.message105=\u57fa\u672c\u8cc7\u8a0a\u9801\u7c64\u4e4b\u6b04\u4f4d\u300c\u672c\u6848\u662f\u5426\u70ba\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e\u300d\u8207\u984d\u5ea6\u660e\u7d30\u8868\u4e0d\u540c\u3002
L160M01A.message106=\u984d\u5ea6\u660e\u7d30\u8868->\u7533\u8acb\u5167\u5bb9->\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e\u300c\u9031\u8f49\u6027\u652f\u51fa\u91d1\u984d+\u8cc7\u672c\u6027\u652f\u51fa\u91d1\u984d\u300d\u4e0d\u5f97\u70ba0\u3002
#J-106-0238-001 \u56e0\u61c9\u65bce-Loan\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u4f01\u3001\u500b\u91d1\u5fb5\u3001\u6388\u4fe1\u696d\u52d9\u9632\u5236\u6d17\u9322\u4f5c\u696d\u9801\u7c64\uff0c\u5c0d\u61c90015\u9ed1\u540d\u55ae\u6aa2\u6838\u547d\u4e2d\u5be9\u67e5\u4e4b\u5f8c\u7e8c\u4f5c\u696d\uff0c\u589e\u52a0\u300c\u9ed1\u540d\u55ae/\u9ed1\u570b\u5bb6/\u653f\u6cbb\u654f\u611f\u4eba\u7269\u4ea4\u6613\u5177\u9ad4\u6aa2\u6838\u6a5f\u5236\u300d
L160M01A.message107=Hit Blacklist
L160M01A.message118=contractNo corresponds no Guarantee Collaterals in the Collateral Management System. Please complete filing and approving the Guarantee Collaterals first. 

L162M01A.error01=\u300c{0}\u300dGuarantor burden the ratio of the guarantee responsibility field can not be empty\u3002
L162M01A.error02=\u300c{0}\u300dNatural persons Guarantor burden the ratio of the guarantee responsibility field must be 100
#J-110-0040_05097_B1001 Web e-Loan\u589e\u52a0\u300c\u672c\u884c\u570b\u5bb6\u66b4\u96aa\u662f\u5426\u4ee5\u4fdd\u8b49\u4eba\u570b\u5225\u70ba\u8a08\u7b97\u57fa\u6e96(\u53d6\u4ee3\u6700\u7d42\u98a8\u96aa\u570b\u5225)\u300d\u8a3b\u8a18
L162M01A.error03=\u300c{0}\u300dthe field\u300c{0}\u300dmust not be blank 


L140M01a.isNonSMEProjLoan=\u672c\u6848\u662f\u5426\u70ba\u632f\u8208\u7d93\u6fdf\u975e\u4e2d\u5c0f\u4f01\u696d\u5c08\u6848\u8cb8\u6b3e
L140M01a.nonSMEProjLoanAmt=\u5c6c\u632f\u8208\u7d93\u6fdf\u975e\u4e2d\u5c0f\u4f01\u696d\u5c08\u6848\u8cb8\u6b3e\u91d1\u984d
L140M01a.currSameWithCurrentApply=\u5e63\u5225\u540c\u73fe\u8acb\u984d\u5ea6
#J-106-0082-001 Web e-Loan\u570b\u5167\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\uff0c\u984d\u5ea6\u660e\u7d30\u8868\u65b0\u589e\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e
L140M01a.inSmeFg=\u672c\u6848\u662f\u5426\u70ba\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e
L140M01a.inSmeToAmt=\u5c6c\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e-\u9031\u8f49\u6027\u652f\u51fa\u91d1\u984d
L140M01a.inSmeCaAmt=\u5c6c\u4e2d\u5c0f\u4f01\u696d\u5275\u65b0\u767c\u5c55\u5c08\u6848\u8cb8\u6b3e-\u8cc7\u672c\u6027\u652f\u51fa\u91d1\u984d

#J-106-0029-004  \u6d17\u9322\u9632\u5236-\u52d5\u5be9\u8868\u65b0\u589e\u6d17\u9322\u9632\u5236\u9801\u7c64
l120s01a.chairman=Principal
l120s01a.custid=ID
l120s01a.dupno=Repeat Serial No.
l120s01a.custname=Name
L120S01p.btnApply=Apply
L120S01p.beneficiary=Effective Bene
#J-107-0070-001  Web e-Loan \u570b\u5167\u5fb5\u4fe1\u3001\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u8acb\u5c07\u300c\u9ad8\u968e\u7ba1\u7406\u4eba\u54e1\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
L120S01p.seniorMgr=Senior management
#J-108-0039_05097_B1001 Web e-Loan \u570b\u5167\u4f01\u91d1\u6388\u4fe1\u7cfb\u7d71\u7c3d\u5831\u3001\u52d5\u5be9AML\u9801\u7c64\u5c07\u501f\u6236\u4e4b\u300c\u5177\u63a7\u5236\u6b0a\u4eba\u300d\u7d0d\u5165\u61c9\u67e5\u8a62\u6bd4\u5c0d\u9ed1\u540d\u55ae\u4e4b\u5c0d\u8c61\u3002
L120S01p.ctrlPeo=Controlling Person
L120S01p.type=Data type
L120S01p.type_01=Person
L120S01p.type_02=Company
L120S01p.custIdAndDupNo=ID & Repeat Serial No.
L120S01p.conPersonId=ID
L120S01p.conPersonNew=Company/Person
L120S01p.conPersonName=Name
L120S01p.conPersonEName=English Name
L120S01p.nation=Nation
L120S01p.birthDate=birthday
L120S01p.peps=Politically exposed persons
nohave=No
other.login=Register
L120S01p.message01=Press [Close] and write the data back to the previous page\u3002
L162S02A.grtAmt=Guarantee Amount
L162S02A.localId=Local Id
L162S02A.message01=Guarantee Amount and local ID only need to be filled in by overseas branches
L162S02A.btn01=Excel template download
L162S02A.btn02=Excel Import  Borrower's & Guarantor's Profile
