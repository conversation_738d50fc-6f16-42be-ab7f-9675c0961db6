/* 
 * L999M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金約據書立約人檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L999M01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "type" }))
public class L999M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 立約人統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 立約人重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 立約人種類
	 * <p/>
	 * 1.兆豐國際商業銀行股份有限公司(甲方)<br/>
	 * 2.借款人(乙方)
	 */
	@Column(name = "TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String type;

	/**
	 * 立約人名稱
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * 兆豐國際商業銀行股份有限公司 (XX分行)<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01A.custName
	 */
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 分行代碼
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * COM.BELSBRN.brNo
	 */
	@Column(name = "BRNO", length = 3, columnDefinition = "CHAR(3)")
	private String brNo;

	/**
	 * 分行名稱
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * COM.BELSBRN.brName
	 */
	@Column(name = "BRNAME", length = 63, columnDefinition = "CHAR(63)")
	private String brName;

	/**
	 * 負責人統編
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairmanId
	 */
	@Column(name = "CHAIRMANID", length = 10, columnDefinition = "VARCHAR(10)")
	private String chairmanId;

	/**
	 * 負責人統編重複碼
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairmanDupNo
	 */
	@Column(name = "CHAIRMANDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String chairmanDupNo;

	/**
	 * 負責人姓名
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairman
	 */
	@Column(name = "CHAIRMAN", length = 60, columnDefinition = "VARCHAR(60)")
	private String chairman;

	/**
	 * 郵遞區號
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	@Column(name = "ADDRZIP", columnDefinition = "DECIMAL(5,0)")
	private Integer addrZip;

	/**
	 * 地址(縣市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	@Column(name = "ADDRCITY", length = 12, columnDefinition = "VARCHAR(12)")
	private String addrCity;

	/**
	 * 地址(區鄉鎮市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	@Column(name = "ADDRTOWN", length = 12, columnDefinition = "VARCHAR(12)")
	private String addrTown;

	/**
	 * 地址
	 * <p/>
	 * (含縣市、鄉鎮市區)<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.cmpAddr
	 */
	@Column(name = "ADDR", length = 300, columnDefinition = "VARCHAR(300)")
	private String addr;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得立約人統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定立約人統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得立約人重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定立約人重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得立約人種類
	 * <p/>
	 * 1.兆豐國際商業銀行股份有限公司(甲方)<br/>
	 * 2.借款人(乙方)
	 */
	public String getType() {
		return this.type;
	}

	/**
	 * 設定立約人種類
	 * <p/>
	 * 1.兆豐國際商業銀行股份有限公司(甲方)<br/>
	 * 2.借款人(乙方)
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/**
	 * 取得立約人名稱
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * 兆豐國際商業銀行股份有限公司 (XX分行)<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01A.custName
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定立約人名稱
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * 兆豐國際商業銀行股份有限公司 (XX分行)<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01A.custName
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得分行代碼
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * COM.BELSBRN.brNo
	 */
	public String getBrNo() {
		return this.brNo;
	}

	/**
	 * 設定分行代碼
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * COM.BELSBRN.brNo
	 **/
	public void setBrNo(String value) {
		this.brNo = value;
	}

	/**
	 * 取得分行名稱
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * COM.BELSBRN.brName
	 */
	public String getBrName() {
		return this.brName;
	}

	/**
	 * 設定分行名稱
	 * <p/>
	 * ※type=1，視約據書以分行名義或全行名義<br/>
	 * COM.BELSBRN.brName
	 **/
	public void setBrName(String value) {
		this.brName = value;
	}

	/**
	 * 取得負責人統編
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairmanId
	 */
	public String getChairmanId() {
		return this.chairmanId;
	}

	/**
	 * 設定負責人統編
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairmanId
	 **/
	public void setChairmanId(String value) {
		this.chairmanId = value;
	}

	/**
	 * 取得負責人統編重複碼
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairmanDupNo
	 */
	public String getChairmanDupNo() {
		return this.chairmanDupNo;
	}

	/**
	 * 設定負責人統編重複碼
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairmanDupNo
	 **/
	public void setChairmanDupNo(String value) {
		this.chairmanDupNo = value;
	}

	/**
	 * 取得負責人姓名
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairman
	 */
	public String getChairman() {
		return this.chairman;
	}

	/**
	 * 設定負責人姓名
	 * <p/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.chairman
	 **/
	public void setChairman(String value) {
		this.chairman = value;
	}

	/**
	 * 取得郵遞區號
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	public Integer getAddrZip() {
		return this.addrZip;
	}

	/**
	 * 設定郵遞區號
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 **/
	public void setAddrZip(Integer value) {
		this.addrZip = value;
	}

	/**
	 * 取得地址(縣市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	public String getAddrCity() {
		return this.addrCity;
	}

	/**
	 * 設定地址(縣市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 **/
	public void setAddrCity(String value) {
		this.addrCity = value;
	}

	/**
	 * 取得地址(區鄉鎮市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 */
	public String getAddrTown() {
		return this.addrTown;
	}

	/**
	 * 設定地址(區鄉鎮市)
	 * <p/>
	 * 戶籍地址/公司設立地址<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.無<br/>
	 * ※保留未來若改引自MIS時可用
	 **/
	public void setAddrTown(String value) {
		this.addrTown = value;
	}

	/**
	 * 取得地址
	 * <p/>
	 * (含縣市、鄉鎮市區)<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.cmpAddr
	 */
	public String getAddr() {
		return this.addr;
	}

	/**
	 * 設定地址
	 * <p/>
	 * (含縣市、鄉鎮市區)<br/>
	 * ※type=2<br/>
	 * 資料來源：LMS.L120S01B.cmpAddr
	 **/
	public void setAddr(String value) {
		this.addr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
