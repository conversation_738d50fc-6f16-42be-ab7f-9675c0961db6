/* 
 * L120S10A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 微型企業明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S10A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S10A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 中文戶名 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 與本案關係
	 * <p/>
	 * (多選)<br/>
	 * 借戶 | 1<br/>
	 * 借戶負責人 | 2<br/>
	 * 共同借款人 | 3<br/>
	 * 連保人 | 4<br/>
	 */
	@Size(max = 20)
	@Column(name = "CUSTRELATION", length = 20, columnDefinition = "VARCHAR(20)")
	private String custRelation;
	
	/** 與本案關係Grid 排序(非DB欄位) **/
	@Transient
	private String custRelationStr;
	
	/** 
	 * 查詢日<p/>
	 * YYYY-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="QUERYDATE", columnDefinition="DATE")
	private Date queryDate;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;
	
	/** 異動人員 */
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 */
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * J10資料日<p/>
	 * YYYY-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="J10DATE", columnDefinition="DATE")
	private Date j10Date;

	/** J10信用評分 **/
	@Size(max=5)
	@Column(name="J10SCORE", length=5, columnDefinition="VARCHAR(5)")
	private String j10Score;
	
	/** J10百分位點 **/
	@Size(max=25)
	@Column(name="J10PERCENTILE", length=25, columnDefinition="VARCHAR(25)")
	private String j10Percentile;
	
	/** J10違約率 **/
	@Size(max=15)
	@Column(name="J10BREACH", length=15, columnDefinition="VARCHAR(15)")
	private String j10Breach;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得中文戶名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定中文戶名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得與借款人關係
	 * <p/>
	 * (多選)<br/>
	 * 借戶 | 1<br/>
	 * 借戶負責人 | 2<br/>
	 * 共同借款人 | 3<br/>
	 * 連保人 | 4<br/>
	 */
	public String getCustRelation() {
		return this.custRelation;
	}

	/**
	 * 設定與借款人關係
	 * <p/>
	 * (多選)<br/>
	 * 借戶 | 1<br/>
	 * 借戶負責人 | 2<br/>
	 * 共同借款人 | 3<br/>
	 * 連保人 | 4<br/>
	 **/
	public void setCustRelation(String value) {
		this.custRelation = value;
	}

	/**
	 * 設定與借款人關係Grid 排序
	 * 
	 * @param custRelationIndex
	 */
	public void setCustRelationStr(String value) {
		this.custRelationStr = value;
	}

	/**
	 * 取得與借款人關係Grid 排序
	 * 
	 * @return
	 */
	public String getCustRelationStr() {

		return this.custRelationStr;
	}
	
	/** 
	 * 取得查詢日<p/>
	 * YYYY-MM-dd
	 */
	public Date getQueryDate() {
		return this.queryDate;
	}
	/**
	 *  設定查詢日<p/>
	 *  YYYY-MM-dd
	 **/
	public void setQueryDate(Date value) {
		this.queryDate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}
	
	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得J10資料日<p/>
	 * YYYY-MM-dd
	 */
	public Date getJ10Date() {
		return this.j10Date;
	}
	/**
	 *  設定J10資料日<p/>
	 *  YYYY-MM-dd
	 **/
	public void setJ10Date(Date value) {
		this.j10Date = value;
	}

	/** 取得J10信用評分 **/
	public String getJ10Score() {
		return this.j10Score;
	}
	/** 設定J10信用評分 **/
	public void setJ10Score(String value) {
		this.j10Score = value;
	}
	
	/** 取得J10百分位點 **/
	public String getJ10Percentile() {
		return this.j10Percentile;
	}
	/** 設定J10百分位點 **/
	public void setJ10Percentile(String value) {
		this.j10Percentile = value;
	}

	/** 取得J10違約率 **/
	public String getJ10Breach() {
		return this.j10Breach;
	}
	/** 設定J10違約率 **/
	public void setJ10Breach(String value) {
		this.j10Breach = value;
	}
}
