---------------------------------------------------------
-- LMS.C160S01G 分段利率明細檔
---------------------------------------------------------


---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C160S01G;
CREATE TABLE LMS.C160S01G (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	BGNNUM        DECIMAL(3,0) ,
	ENDNUM        DECIMAL(3,0) ,
	RATETYPE      CHAR(2)      ,
	PMRATE        DECIMAL(6,4) ,
	RATEFLAG      CHAR(1)      ,
	RATECHGWAY    CHAR(1)      ,
	RATECHGWAY2   CHAR(2)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C160S01G PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC160S01G01;
CREATE UNIQUE INDEX LMS.XC160S01G01 ON LMS.C160S01G   (MAINID, SEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C160S01G IS '分段利率明細檔';
COMMENT ON LMS.C160S01G (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	BGNNUM        IS '期別-起', 
	ENDNUM        IS '期別-迄', 
	RATETYPE      IS '利率基礎', 
	PMRATE        IS '加減碼利率', 
	RATEFLAG      IS '利率方式', 
	RATECHGWAY    IS '利率變動方式', 
	RATECHGWAY2   IS '利率變動方式(每『月/三個月/半年/九個月/年』調整乙次)', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
