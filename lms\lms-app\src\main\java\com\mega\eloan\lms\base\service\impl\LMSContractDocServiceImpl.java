package com.mega.eloan.lms.base.service.impl;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.CharUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ContractDocUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S14ADao;
import com.mega.eloan.lms.dao.L120S14BDao;
import com.mega.eloan.lms.dao.L120S14CDao;
import com.mega.eloan.lms.dao.L120S14DDao;
import com.mega.eloan.lms.dao.L120S14FDao;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S14A;
import com.mega.eloan.lms.model.L120S14B;
import com.mega.eloan.lms.model.L120S14C;
import com.mega.eloan.lms.model.L120S14D;
import com.mega.eloan.lms.model.L120S14F;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapCommonUtil;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

@Service("LmsContractDocService")
public class LMSContractDocServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMSContractDocServiceImpl.class);

	@Resource
	BranchService branchService;

	@Resource
	CLSService clsService;

	@Resource
	LMSService lmsService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisMislnratService misMislnratService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120S14ADao l120s14aDao;

	@Resource
	L120S14BDao l120s14bDao;

	@Resource
	L120S14CDao l120s14cDao;

	@Resource
	L120S14DDao l120s14dDao;

    @Resource
    L120S14FDao l120s14fDao;

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {

		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.creatDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				IOUtils.closeQuietly(baos);
			}
		}
		return null;
	}

	private ByteArrayOutputStream creatDoc(PageParameters params)
			throws CapException {
		// ===========================
		String l120m01a_oid = params.getString("oid");
		String l120m01a_mainId = params.getString("mainId");
		String printItem = Util.trim(params.getString("printItem"));
		String splitMark = Util.trim(params.getString("splitMark"));

		boolean isForTest = false;
		// if (params.containsKey("isForTest")) {
		// if (Util.equals(Util.trim(params.getString("isForTest")), "K")) {
		// isForTest = true;
		// }
		// }
		// if (isForTest) {
		// // l120m01a_mainId = "688bcde49427452fa6585945e15c64f5";
		// // l120m01a_mainId = "2f44d1da710143448cdb9d75cd0f557a";
		// l120m01a_mainId = "7e1212c915f54e17bb78bd333bf90e03";
		// }

		L120M01A l120m01a = l120m01aDao.findByMainId(l120m01a_mainId);

		if (l120m01a == null) {
			throw new CapException("[l120m01a_mainId=" + l120m01a_mainId
					+ "] not found", getClass());
		}
		String traceStr = "【" + l120m01a.getCustId() + ", l120m01a.mainId="
				+ l120m01a.getMainId() + "】";
		// _debug(traceStr, "mock_mode="+mock_mode);

		// 單筆用WORD檔案回傳，多筆用ZIP檔案回傳
		String resources[] = StringUtils.split(printItem, splitMark);
		int resLength = resources.length;

		ByteArrayOutputStream rtn_baos = null;
		ByteArrayOutputStream baos1 = null;
		ZipOutputStream out = null;
		InputStream in1 = null;
		BufferedReader bin1 = null;

		try {

			if (resLength > 1) {
				rtn_baos = new ByteArrayOutputStream();
				out = new ZipOutputStream(rtn_baos);
				out.setEncoding("BIG5"); // ISO8859_1 UTF-8
			}

			for (int j = 0; j < resLength; j++) {
                String tempName = "";
                String[] tempSplits = resources[j].split("\\^");
                tempName = (tempSplits.length < 1 ? "" : tempSplits[0]);
				String tempCntrNo = "";
				tempCntrNo = (tempSplits.length < 2 ? "" : tempSplits[1]);
                String tempKey = "";    // 各Table Oid
                tempKey = (tempSplits.length < 3 ? "" : tempSplits[2]);

				if (Util.equals(tempName, "1")) {

					baos1 = gen_l120m01a_contract_typeC(params, l120m01a, "",
							traceStr, tempKey);
					if (resLength == 1) {
						// 單檔列印直接回傳
						baos1.close();
						return baos1;
					} else {
						// 多檔列印塞到ZIP
						int c;
						in1 = new ByteArrayInputStream(baos1.toByteArray());
						bin1 = new BufferedReader(new InputStreamReader(in1,
								"ISO8859_1"));
						out.putNextEntry(new ZipEntry("央行C方案授信合約書-" + tempCntrNo + ".doc")); // 央行C方案授信合約書
						while ((c = bin1.read()) != -1)
							out.write(c);
						bin1.close();
						baos1.close();

						// 以下為判斷OS是AIX 或 WINDOWS程式，因out.setEncoding("BIG5")
						// 已經解決AIX ZIP檔中文亂碼問題，暫時用不到
						// String osName = System.getProperty("os.name");
						// //回傳AIX 或 WINDOWS
						// String filename = "央行C方案授信合約書.doc";
						// if ("AIX".equals(osName.toUpperCase(Locale.ENGLISH)))
						// {
						// filename = new String(filename.getBytes(),
						// "ISO8859_1");
						// } else {
						//
						// }
						// LOGGER.info("XXXXXXXXXXXXXXX osName=" + osName);
						//
						// String enc = System.getProperty("file.encoding");
						// //回傳UTF-8
						// LOGGER.info("XXXXXXXXXXXXXXX enc=" + enc);

					}
				} else if (Util.equals(tempName, "2")) {

					baos1 = gen_l120m01a_note_typeC(params, l120m01a, "",
							traceStr, tempKey);
					if (resLength == 1) {
						baos1.close();
						return baos1;
					} else {
						int c;
						in1 = new ByteArrayInputStream(baos1.toByteArray());
						bin1 = new BufferedReader(new InputStreamReader(in1,
								"ISO8859_1")); // ISO8859_1
						out.putNextEntry(new ZipEntry("本票-" + tempCntrNo + ".doc")); // 本票
						while ((c = bin1.read()) != -1)
							out.write(c);
						bin1.close();
						baos1.close();

					}
				} else if (Util.equals(tempName, "3")) {

					baos1 = gen_l120m01a_noteAuth_typeC(params, l120m01a, "",
							traceStr, tempKey);
					if (resLength == 1) {
						baos1.close();
						return baos1;
					} else {
						int c;
						in1 = new ByteArrayInputStream(baos1.toByteArray());
						bin1 = new BufferedReader(new InputStreamReader(in1,
								"ISO8859_1"));
						out.putNextEntry(new ZipEntry("本票授權書-公司-" + tempCntrNo + ".doc"));
						while ((c = bin1.read()) != -1)
							out.write(c);
						bin1.close();
						baos1.close();
					}
				} else if (Util.equals(tempName, "4")) {

					baos1 = gen_l120m01a_deduction_typeC(params, l120m01a, "",
							traceStr, tempKey);
					if (resLength == 1) {
						baos1.close();
						return baos1;
					} else {
						int c;
						in1 = new ByteArrayInputStream(baos1.toByteArray());
						bin1 = new BufferedReader(new InputStreamReader(in1,
								"ISO8859_1"));
						out.putNextEntry(new ZipEntry("授權扣帳-" + tempCntrNo + ".doc"));
						while ((c = bin1.read()) != -1)
							out.write(c);
						bin1.close();
						baos1.close();
					}
				} else if (Util.equals(tempName, "5")) {

                    baos1 = gen_l120m01a_contractSup_typeC(params, l120m01a, "",
                            traceStr, tempKey);
                    if (resLength == 1) {
                        // 單檔列印直接回傳
                        baos1.close();
                        return baos1;
                    } else {
                        // 多檔列印塞到ZIP
                        int c;
                        in1 = new ByteArrayInputStream(baos1.toByteArray());
                        bin1 = new BufferedReader(new InputStreamReader(in1,
                                "ISO8859_1"));
                        out.putNextEntry(new ZipEntry("央行C方案增補合約書-" + tempCntrNo + ".doc"));
                        while ((c = bin1.read()) != -1)
                            out.write(c);
                        bin1.close();
                        baos1.close();
                    }
                }
			}

			return rtn_baos;

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (in1 != null) {
					in1.close();
				}
				if (bin1 != null) {
					bin1.close();
				}
				if (baos1 != null) {
					baos1.close();
				}

				if (out != null) {
					out.close();
				}
				if (rtn_baos != null) {
					rtn_baos.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		return rtn_baos;
	}

	private ByteArrayOutputStream gen_l120m01a_contract_typeC(
			PageParameters params, L120M01A l120m01a, String mock_mode,
			String traceStr, String tempKey) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		String templateName = "LMS_contract_smallBussC_V202006.xml";
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		String contractNo = "";// 合約編號
		String custName = "";// 客戶名稱
		String contractor = "";// 立合約人
		String inviter = "";// 邀同
		String loanAmt = "";// 授信金額
		String signDate = "";// 簽約日
		String bgnMonth = "";// 起算月
		String useNum = "";// 動用次數
		String fundingWay = "";// 撥款方式
		String currAccount = "";// 活期存款帳號
		String partyPrincipal = "";// 乙方負責人
		String currSavAccount = "";// 活期儲蓄存款帳號
		String lnyear = "";// 授信期間
		String lnBgnDate = "";// 授信起日
		String lnEndDate = "";// 授信迄日
		String repayWay = "";// 償還方式
		String gracePeriod = "";// 寬限期
		String period = "";// 每期
		String periodNum = "";// 期數
		String capitalBgn = "";// 還本期數 - 起
		String capitalEnd = "";// 還本期數 - 迄
		String capitalAmt = "";// 還本金額
		String other = "";// 其他
		String contractDate = "";// 訂約日
		String contractRate = "";// 訂約年利
		String addRate = "";// 加碼年利
		String addRate2 = "";// 第二段加碼年利
		String location = "";// 地方法院
		String origNum = "";// 正本數
		String copyNum = "";// 副本數
		String crossSelling = "";// 共同行銷
		String proxyBrId = "";// 甲方代理分行
		String proxy = "";// 甲方代理人
		String proxyAddr = "";// 甲方代理地址
		String party = "";// 乙方
		String partyAgent = "";// 乙方代理人
		String partyAddr = "";// 乙方地址
		String partyId = "";// 乙方統編
		String party2 = "";// 丙方
		String guarantor = "";// 連保人
		String guarId = "";// 連保人統編
		String guarAddr = "";// 連保人地址

		try {
			// paramMap.put("Text001", "邀同連帶保證人["+"XXXXXXXXXXXX"+"]等（以下簡稱丙方）");

			boolean isForTest = false;
			// if (params.containsKey("isForTest")) {
			// if (Util.equals(Util.trim(params.getString("isForTest")), "Y")) {
			// isForTest = true;
			// }
			// }

			if (isForTest) {
				paramMap = null;
				paramMap = this.forTestParamMap_contract();
			} else {

				L120S14A l120s14a = null;
                l120s14a = l120s14aDao.findByOid(tempKey);
                /*
				List<L120S14A> list = l120s14aDao.findByMainId(l120m01a
						.getMainId());
				if (list != null && !list.isEmpty()) {
					l120s14a = list.get(0);
				}
				*/

				if (l120s14a == null) {
					throw new CapException(
							"請先輸入中央銀行專案融通C方案貸款「授信合約書」資訊後，再執行本列印功能。", getClass());
				}

				contractNo = Util.trim(l120s14a.getContractNo());// 合約編號
				custName = Util.trim(l120s14a.getCustName());// 客戶名稱
				contractor = Util.equals(Util.trim(l120s14a.getContractor()),
						"") ? "　　　　　　　　　　　　　　" : Util.trim(l120s14a
						.getContractor());// 立合約人
				inviter = Util.equals(Util.trim(l120s14a.getInviter()), "") ? ""
						: Util.trim(l120s14a.getInviter());// 邀同
															// //爰經乙方邀同連帶保證人(倘有)等（以下簡稱丙方）

				BigDecimal t_loanAmt = l120s14a.getLoanAmt();
				loanAmt = t_loanAmt == null ? "　　　　" : CapCommonUtil
						.toChineseUpperAmount(LMSUtil.pretty_numStr(t_loanAmt),
								true);// 授信金額

				Date t_signDate = l120s14a.getSignDate();
				signDate = t_signDate == null ? "___年__月__日" : this
						.convertADDatetoTWNDate(t_signDate);// 簽約日

				Integer t_bgnMonth = l120s14a.getBgnMonth();
				bgnMonth = t_bgnMonth == null ? "____" : t_bgnMonth.toString();// 起算月

				Integer t_useNum = l120s14a.getUseNum();
				useNum = t_useNum == null ? "□一次 □分次"
						: (t_useNum == 1 ? "■一次 □分次" : "□一次 ■分次");// 動用次數

				Integer t_fundingWay = l120s14a.getFundingWay();
				fundingWay = t_fundingWay == null ? "　　" : t_fundingWay
						.toString();// 撥款方式

				currAccount = Util.equals(Util.trim(l120s14a.getCurrAccount()),
						"") ? "　　　　　　" : Util.trim(l120s14a.getCurrAccount());// 活期存款帳號

				partyPrincipal = Util.equals(
						Util.trim(l120s14a.getPartyPrincipal()), "") ? "　　　　　"
						: Util.trim(l120s14a.getPartyPrincipal());// 乙方負責人

				currSavAccount = Util.equals(
						Util.trim(l120s14a.getCurrSavAccount()), "") ? "　　　　　　"
						: Util.trim(l120s14a.getCurrSavAccount());// 活期儲蓄存款帳號

				Integer t_lnyear = l120s14a.getLnyear();
				lnyear = t_lnyear == null ? "___" : t_lnyear.toString();// 授信期間

				Date t_lnBgnDate = l120s14a.getLnBgnDate();
				lnBgnDate = t_lnBgnDate == null ? "___年__月__日" : this
						.convertADDatetoTWNDate(t_lnBgnDate);// 授信起日

				Date t_lnEndDate = l120s14a.getLnEndDate();
				lnEndDate = t_lnEndDate == null ? "___年__月__日" : this
						.convertADDatetoTWNDate(t_lnEndDate);// 授信迄日

				Integer t_repayWay = l120s14a.getRepayWay();
				repayWay = t_repayWay == null ? "　　" : t_repayWay.toString();// 償還方式

				Integer t_gracePeriod = l120s14a.getGracePeriod();
				gracePeriod = t_gracePeriod == null ? "     " : t_gracePeriod
						.toString();// 寬限期

				Integer t_period = l120s14a.getPeriod();
				period = t_period == null ? "　　" : t_period.toString();// 每期

				Integer t_periodNum = l120s14a.getPeriodNum();
				periodNum = t_periodNum == null ? "　　　" : t_periodNum
						.toString();// 期數

				Integer t_capitalBgn = l120s14a.getCapitalBgn();
				capitalBgn = t_capitalBgn == null ? "    " : t_capitalBgn
						.toString();// 還本期數 - 起

				Integer t_capitalEnd = l120s14a.getCapitalEnd();
				capitalEnd = t_capitalEnd == null ? "    " : t_capitalEnd
						.toString();// 還本期數 - 迄

				BigDecimal t_capitalAmt = l120s14a.getCapitalAmt();
				capitalAmt = t_capitalAmt == null ? "　　　　" : CapCommonUtil
						.toChineseUpperAmount(
								LMSUtil.pretty_numStr(t_capitalAmt), true);// 還本金額

				other = Util.trim(l120s14a.getOther());// 其他

				Date t_contractDate = l120s14a.getContractDate();
				contractDate = t_contractDate == null ? "___年__月__日" : this
						.convertADDatetoTWNDate(t_contractDate);//

				DecimalFormat rate_df = new DecimalFormat("#0.######");

				BigDecimal t_contractRate = l120s14a.getContractRate();
				contractRate = t_contractRate == null ? "　　　　" : rate_df
						.format(t_contractRate);// 訂約年利

				BigDecimal t_addRate = l120s14a.getAddRate();
				addRate = t_addRate == null ? "　　　　" : rate_df
						.format(t_addRate);// 加碼年利

				BigDecimal t_addRate2 = l120s14a.getAddRate2();
				addRate2 = t_addRate2 == null ? "　　　　" : rate_df
						.format(t_addRate2);// 第二段加碼年利

				location = Util.equals(Util.trim(l120s14a.getLocation()), "") ? "___________"
						: Util.trim(l120s14a.getLocation());// 地方法院

				Integer t_origNum = l120s14a.getOrigNum();
				origNum = t_origNum == null ? "　　　　"
						: CapCommonUtil
								.toChineseUpperAmount(
										LMSUtil.pretty_numStr(new BigDecimal(
												t_origNum)), true);// 正本數

				Integer t_copyNum = l120s14a.getCopyNum();
				copyNum = t_copyNum == null ? "　　　　"
						: CapCommonUtil
								.toChineseUpperAmount(
										LMSUtil.pretty_numStr(new BigDecimal(
												t_copyNum)), true);// 副本數

				String t_crossSelling = Util.trim(l120s14a.getCrossSelling());

				crossSelling = Util.equals(t_crossSelling, "") ? "□同意□不同意"
						: (Util.equals(t_crossSelling, "Y") ? "■同意□不同意"
								: "□同意■不同意");// 共同行銷

				String t_proxyBrId = Util.trim(l120s14a.getProxyBrId());
				if (Util.notEquals(t_proxyBrId, "")) {
					IBranch iBranch = branchService.getBranch(t_proxyBrId);
					proxyBrId = Util.trim(iBranch.getBrName());
				} else {
					proxyBrId = "";// 甲方代理分行
				}

				String t_proxy = Util.trim(l120s14a.getProxy());
				if (Util.notEquals(t_proxy, "")) {
					proxy = Util.trim(userInfoService.getUserName(t_proxy));
				} else {
					proxy = "";// 甲方代理人
				}

				proxyAddr = Util.trim(l120s14a.getProxyAddr());// 甲方代理地址

				party = Util.trim(l120s14a.getParty());// 乙方
				partyAgent = Util.trim(l120s14a.getPartyAgent());// 乙方代理人
				partyAddr = Util.trim(l120s14a.getPartyAddr());// 乙方地址
				partyId = Util.trim(l120s14a.getPartyId());// 乙方統編

				party2 = Util.trim(l120s14a.getParty2());// 丙方
				guarantor = Util.trim(l120s14a.getGuarantor());// 連保人
				guarId = Util.trim(l120s14a.getGuarId());// 連保人統編
				guarAddr = Util.trim(l120s14a.getGuarAddr());// 連保人地址

				paramMap.put("contractNo", contractNo);
				paramMap.put("custName", custName);
				paramMap.put("contractor", contractor);
				paramMap.put("inviter", inviter);
				paramMap.put("loanAmt", loanAmt);
				paramMap.put("signDate", signDate);
				paramMap.put("bgnMonth", bgnMonth);
				paramMap.put("useNum", useNum);
				paramMap.put("fundingWay", fundingWay);
				paramMap.put("currAccount", currAccount);
				paramMap.put("partyPrincipal", partyPrincipal);
				paramMap.put("currSavAccount", currSavAccount);
				paramMap.put("lnyear", lnyear);
				paramMap.put("lnBgnDate", lnBgnDate);
				paramMap.put("lnEndDate", lnEndDate);
				paramMap.put("repayWay", repayWay);
				paramMap.put("gracePeriod", gracePeriod);
				paramMap.put("period", period);
				paramMap.put("periodNum", periodNum);
				paramMap.put("capitalBgn", capitalBgn);
				paramMap.put("capitalEnd", capitalEnd);
				paramMap.put("capitalAmt", capitalAmt);
				paramMap.put("other", other);
				paramMap.put("contractDate", contractDate);
				paramMap.put("contractRate", contractRate);
				paramMap.put("addRate", addRate);
				paramMap.put("addRate2", addRate2);
				paramMap.put("location", location);
				paramMap.put("origNum", origNum);
				paramMap.put("copyNum", copyNum);
				// paramMap.put("crossSelling", crossSelling);
				paramMap.put("proxyBrId", proxyBrId);
				paramMap.put("proxy", proxy);
				paramMap.put("proxyAddr", proxyAddr);
				paramMap.put("party", party);
				paramMap.put("partyAgent", partyAgent);
				paramMap.put("partyAddr", partyAddr);
				paramMap.put("partyId", partyId);
				paramMap.put("party2", party2);
				paramMap.put("guarantor", guarantor);
				paramMap.put("guarId", guarId);
				paramMap.put("guarAddr", guarAddr);
			}

			Map<String, String> convert_paramMap = ContractDocUtil
					.convert_paramValue_for_XML_Predefined_entities(paramMap);
			String templateStr = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);
			String outputStr = join_word_template_param(traceStr, templateStr,
					convert_paramMap);
			baos = this.writeWordContent(outputStr);
		} catch (Exception e) {
			LOGGER.error(traceStr);
			LOGGER.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e.getMessage(), getClass());
		}
		return baos;
	}

	private String join_word_template_param(String traceStr, String raw_srcStr,
			Map<String, String> passed_paramMap) {
		String srcStr = raw_srcStr;

		Pattern pattern_run = Pattern
				.compile("(?s)<w:r\\b[^>]*>(?:(?!<w:r\b).)*?</w:r\\b[^>]*>");

		/*
		 * 增加【Form Fields】判斷 => 插入的 文字控制項，在print時會出現灰底 <w:fldSimple w:instr="">
		 * </w:fldSimple>
		 * 
		 * <w:fldChar w:fldCharType="begin"> <w:ffData> <w:name
		 * w:val="Text999"/> <w:enabled/> <w:calcOnExit w:val="0"/>
		 * <w:textInput/> </w:ffData> </w:fldChar> <w:r><w:fldChar
		 * w:fldCharType="separate"/></w:r> <w:r><w:fldChar
		 * w:fldCharType="end"/></w:r>
		 */
		// if(clsService.is_function_on_codetype("c340m01a_word_FormFields")){
		// pattern_run =
		// Pattern.compile("(?s)<w:r\\b[^>]*>(?:(?!<w:r\b).)*(?:(?!<w:instrText\b).)*(?:(?!<w:fldChar\b).)*?</w:r\\b[^>]*>");
		// }

		Pattern pattern_runTextTagAndContent = Pattern
				.compile("(?s)<w:t\\b[^>]*>(?:(?!<w:t\b).)*?</w:t\\b[^>]*>");
		Pattern pattern_tag_rPr = Pattern
				.compile("(?s)<w:rPr\\b[^>]*>(?:(?!<w:rPr\b).)*?</w:rPr\\b[^>]*>");
		Pattern pattern_tag_w_t = Pattern.compile("<w:t\\b[^>]*>");
		Pattern pattern_tag_w_t_end = Pattern.compile("</w:t\\b[^>]*>");
		// ===========
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> paramMap = new LinkedHashMap<String, String>();
		for (String k : passed_paramMap.keySet()) {
			String injectParamVal = Util.trim(passed_paramMap.get(k));
			if (Util.isEmpty(injectParamVal)) {
				continue;
			}
			paramMap.put(k, injectParamVal);
		}

		if (Util.equals(user.getSsoUnitNo(), "900")) { // 為了 debug
			LOGGER.info("join_word_template_param>>>" + paramMap.toString());
		}

		for (String k : paramMap.keySet()) {
			String injectParamVal = Util.trim(paramMap.get(k));

			String[] arr_bookMark = split_ByBookMark(k, srcStr);
			if (arr_bookMark.length == 3) {
				String befStr_bookmark = arr_bookMark[0];
				String bookmarkTagAndContentStr = arr_bookMark[1];
				String aftStr_bookmark = arr_bookMark[2];
				// =============
				String[] arr_bookmarkTagAndContent = split_ByRunText(
						bookmarkTagAndContentStr, pattern_run);
				if (arr_bookmarkTagAndContent.length == 3) {
					String bookMarkTagBeg = arr_bookmarkTagAndContent[0];
					String runPrAndContentStr = arr_bookmarkTagAndContent[1];
					String bookMarkTagEnd = arr_bookmarkTagAndContent[2];
					// ~~~~~~
					String[] arr_runPrAndContent = ContractDocUtil
							.split_into_pre_match_aft_byFirstFind(
									runPrAndContentStr,
									pattern_runTextTagAndContent);
					if (arr_runPrAndContent.length == 3) {
						String new_runPrAndContent = "";
						String debug_str = "";
						int idx_rPr = arr_runPrAndContent[0].indexOf("<w:rPr");
						int idx_u = arr_runPrAndContent[0].indexOf("<w:u");
						if (idx_rPr > 0 && idx_u > idx_rPr) {
							// 有底線, 例如：帳號_________________
							new_runPrAndContent = addColorAndSetTextWithUnderLine(
									k, arr_runPrAndContent, injectParamVal,
									pattern_tag_w_t, pattern_tag_w_t_end,
									pattern_tag_rPr);
							debug_str = "addColorAndSetTextWithUnderLine";
						} else {
							// 無底線，例如：甲方 ○○○
							new_runPrAndContent = addColorAndSetText(k,
									arr_runPrAndContent, injectParamVal,
									pattern_tag_w_t, pattern_tag_w_t_end,
									pattern_tag_rPr);
							debug_str = "addColorAndSetText";
						}
						String chg_flag = Util.equals(runPrAndContentStr,
								new_runPrAndContent) ? "Eq" : "Diff";
						if (Util.equals(chg_flag, "Eq")) {
							LOGGER.info(traceStr + "[" + k + "][new vs old]["
									+ debug_str + "]=[" + chg_flag + "]");
							LOGGER.info(traceStr + "\t" + runPrAndContentStr);
						} else if (Util.equals(chg_flag, "Diff")) {

						}

						srcStr = befStr_bookmark + bookMarkTagBeg
								+ (new_runPrAndContent) + bookMarkTagEnd
								+ aftStr_bookmark;

					} else {
						LOGGER.error(traceStr + "[" + k
								+ "]arr_runPrAndContent.length="
								+ arr_runPrAndContent.length);
						LOGGER.error(traceStr + runPrAndContentStr);
					}
				} else {
					LOGGER.error(traceStr + "[" + k
							+ "]arr_bookmarkTagAndContent.length="
							+ arr_bookmarkTagAndContent.length);
					LOGGER.error(traceStr + bookmarkTagAndContentStr);
				}
			} else {
				LOGGER.error(traceStr + "[" + k + "]arr_bookMark.length="
						+ arr_bookMark.length);
				LOGGER.error(traceStr + srcStr);
			}
		}
		return srcStr;
	}

	private ByteArrayOutputStream writeWordContent(String content)
			throws CapMessageException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		OutputStreamWriter outWriter = null;
		try {
			outWriter = new OutputStreamWriter(baos, "UTF-8");
			outWriter.write(content);
			outWriter.close();
			return baos;
		} catch (UnsupportedEncodingException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (IOException i) {
			LOGGER.error(i.getMessage());
			throw new CapMessageException(i.getMessage(), getClass());
		}
	}

	private String addColorAndSetTextWithUnderLine(String bookmark_name,
			String[] arr_runPrAndContent, String injectParamVal,
			Pattern pattern_tag_w_t, Pattern pattern_tag_w_t_end,
			Pattern pattern_tag_rPr) {
		String runTagBeg_plus_rPr = arr_runPrAndContent[0]; // <w:r
															// w:rsidRPr="asdf"><w:rPr>...</w:rPr>
		String org_runTagAndContentStr = arr_runPrAndContent[1]; // <w:t> qwert
																	// </w:t>
		String runTagEnd = arr_runPrAndContent[2]; // </w:r>
		// ~~~
		String[] text_arr = ContractDocUtil.split_tag_and_content(
				org_runTagAndContentStr, pattern_tag_w_t, pattern_tag_w_t_end);
		int org_space_cnt = 0;
		if (text_arr.length == 3) {
			org_space_cnt = text_arr[1].length();
		}

		int cnt_empty_bef = 0;
		int cnt_empty_aft = 0;
		int injectStrValCnt = 0;
		String str_empty_bef = "";
		String str_empty_aft = "";
		if (StringUtils.isAsciiPrintable(injectParamVal)) {
			injectStrValCnt = injectParamVal.length();
		} else {
			int sz = injectParamVal.length();
			for (int i = 0; i < sz; i++) {
				if (CharUtils.isAsciiPrintable(injectParamVal.charAt(i))) {
					++injectStrValCnt;
				} else {
					injectStrValCnt = (injectStrValCnt + 2);
				}
			}
		}
		if (true) {
			if (org_space_cnt > injectStrValCnt) {
				int val = (org_space_cnt - injectStrValCnt) / 2;
				cnt_empty_bef = val;
				cnt_empty_aft = val;
			}

			if (cnt_empty_bef == 0) {
				cnt_empty_bef = 1;
			}
			if (cnt_empty_aft == 0) {
				cnt_empty_aft = 1;
			}

			str_empty_bef = StringUtils.repeat(" ", cnt_empty_bef);
			str_empty_aft = StringUtils.repeat(" ", cnt_empty_aft);
		}
		// ====================================
		// 填入資料分為3段
		// 第1段 黑色底線
		// 第2段 藍色底線，且為injectParamVal
		// 第3段 黑色底線
		String p1 = runTagBeg_plus_rPr
				+ (text_arr[0] + str_empty_bef + text_arr[2]) + runTagEnd;
		String p2 = addColorAndSetText(bookmark_name, arr_runPrAndContent,
				injectParamVal, pattern_tag_w_t, pattern_tag_w_t_end,
				pattern_tag_rPr);
		String p3 = runTagBeg_plus_rPr
				+ (text_arr[0] + str_empty_aft + text_arr[2]) + runTagEnd;

		return p1 + p2 + p3;
	}

	private String addColorAndSetText(String bookmark_name,
			String[] arr_runPrAndContent, String injectParamVal,
			Pattern pattern_tag_w_t, Pattern pattern_tag_w_t_end,
			Pattern pattern_tag_rPr) {
		String runTagBeg_plus_rPr = arr_runPrAndContent[0]; // <w:r
															// w:rsidRPr="asdf"><w:rPr>...</w:rPr>
		String org_runTagAndContentStr = arr_runPrAndContent[1]; // <w:t> qwert
																	// </w:t>
		String runTagEnd = arr_runPrAndContent[2]; // </w:r>
		// ~~~
		String[] text_arr = ContractDocUtil.split_tag_and_content(
				org_runTagAndContentStr, pattern_tag_w_t, pattern_tag_w_t_end);
		if (text_arr.length == 3) {
			// 為了能儘快找到填入的字串，增加 <!-- bm -->
			String new_runTagAndContentStr = text_arr[0] + injectParamVal
					+ text_arr[2] + "<!--{" + bookmark_name + "}-->";
			return _add_blueColor_to_rPr(runTagBeg_plus_rPr, pattern_tag_rPr)
					+ (new_runTagAndContentStr) + runTagEnd;
		}

		return StringUtils.join(arr_runPrAndContent, "");
	}

	private String _add_blueColor_to_rPr(String runTagBeg_plus_rPr,
			Pattern pattern_tag_rPr) {
		/*
		 * 第1段 <w:r w:rsidRPr="...">
		 */
		/*
		 * 第2段 <w:rPr><w:rFonts w:ascii="標楷體" w:eastAsia="標楷體" w:hAnsi="標楷體"
		 * w:hint="eastAsia"/><w:b/><w:color w:val="FF0000"/><w:sz
		 * w:val="28"/><w:szCs w:val="28"/><w:u w:val="single"/></w:rPr>
		 */
		/*
		 * 第3段
		 */
		String[] arr = ContractDocUtil.split_into_pre_match_aft_byFirstFind(
				runTagBeg_plus_rPr, pattern_tag_rPr);
		if (arr.length == 3) {
			String rPr = arr[1];
			if (rPr.indexOf("<w:color ") >= 0) {
				int idx = rPr.indexOf("<w:color ");
				String endTag = ">";
				int idx_end = rPr.indexOf(endTag, idx);
				if (idx_end > idx) {
					rPr = rPr.substring(0, idx)
							+ rPr.substring(idx_end + endTag.length());
				}
			}

			if (rPr.indexOf("<w:color ") < 0) { // no <w:color
				int idx = rPr.lastIndexOf("</w:rPr");
				if (idx > 0) {
					return arr[0]
							+ (rPr.substring(0, idx)
									+ " <w:color w:val=\"0000FF\"/>" + rPr
									.substring(idx)) + arr[2];
				}
			}
		}
		return runTagBeg_plus_rPr;
	}

	private String on_when_has_data(String src, String CB_ON_STR) {
		if (Util.isNotEmpty(Util.trim(src))) {
			return CB_ON_STR;
		}
		return "";
	}

	private void add_when_has_data(String src, Set<String> set, String val) {
		if (Util.isNotEmpty(Util.trim(src))) {
			set.add(val);
		}
	}

	private void _injectMap(Map<String, String> map, String k, String v)
			throws CapException {
		if (map.containsKey(k)) {
			throw new CapException("ExistKey[" + k + "]", getClass());
		}
		map.put(k, v);
	}

	/**
	 * <ul>
	 * <li>第1段 beforeStr</li>
	 * <li>第2段 ＜w:bookmarkStart w:id="0" w:name="zxcv"/＞＜w:r
	 * w:rsidRPr="asdf"＞＜w:rPr＞...＜/w:rPr＞＜w:t＞ qwert ＜/w:t＞
	 * ＜/w:r＞＜w:bookmarkEnd w:id="0"/＞</li>
	 * <li>第3段 afterStr</li>
	 * </ul>
	 */
	private String[] split_ByBookMark(String bookMarkName, String srcStr) {
		String[] arr = null;
		String strPattern = "(?s)<w:bookmarkStart\\b[^>]*w:name=\""
				+ bookMarkName + "\"[^>]*>.*<w:bookmarkEnd\\b[^>]*>";
		/*
		 * 參考 https://stackoverflow.com/questions/53646033
		 * 
		 * 若文字為 <w:bookmarkStart w:id="0" w:name="aa1"/><w:bookmarkEnd
		 * w:id="0"/><w:bookmarkStart w:id="1" w:name="aa2"/><w:bookmarkEnd
		 * w:id="1"/> 在加上 (?s) 之後, 若不加上 (?:(?!<w:bookmarkStart\b).)*? 在 parse 時
		 * ● 不會抓到 <w:bookmarkStart w:id="0" w:name="aa1"/><w:bookmarkEnd
		 * w:id="0"/> ● 而會(夾雜另一個tag)抓到 <w:bookmarkStart w:id="0"
		 * w:name="aa1"/><w:bookmarkEnd w:id="0"/><w:bookmarkStart w:id="1"
		 * w:name="aa2"/><w:bookmarkEnd w:id="1"/>
		 */
		strPattern = "(?s)<w:bookmarkStart\\b[^>]*w:name=\""
				+ bookMarkName
				+ "\"[^>]*>(?:(?!<w:bookmarkStart\b).)*?<w:bookmarkEnd\\b[^>]*>";
		// if(srcStr.indexOf(bookMarkName)>0){
		arr = ContractDocUtil.split_into_pre_match_aft_byFirstFind(srcStr,
				Pattern.compile(strPattern));
		// }else{
		// arr = new String[]{srcStr};
		// }

		if (arr != null && arr.length == 3) {
			// ok
		} else {
			LOGGER.error("split_ByBookMark[" + bookMarkName + "]【" + strPattern
					+ "】【" + srcStr + "】");
		}
		return arr;
	}

	/**
	 * <ul>
	 * <li>第A段 ＜w:bookmarkStart w:id="0" w:name="zxcv"/＞</li>
	 * <li>第B~Y段 ＜w:r w:rsidRPr="asdf"＞＜w:rPr＞...＜/w:rPr＞＜w:t＞ qwert ＜/w:t＞
	 * ＜/w:r＞</li>
	 * <li>第Z段 ＜w:bookmarkEnd w:id="0"/＞</li>
	 * </ul>
	 */
	private String[] split_ByRunText(String srcStr, Pattern pattern_run) {
		List<String> list = new ArrayList<String>();
		Matcher matcher = pattern_run.matcher(srcStr);
		int idx_prev_beg = -1;
		int idx_prev_end = -1;
		while (matcher.find()) { // 若一個 bookmark 包含了N個Run
			int idx_beg = matcher.start();
			int idx_end = matcher.end();
			String part = matcher.group();

			if (idx_prev_beg == -1) {
				list.add(srcStr.substring(0, idx_beg)); // 應抓到 <w:bookmarkStart
														// w:id="0"
														// w:name="zxcv"/>
			}
			// ~~~
			idx_prev_beg = idx_beg;
			idx_prev_end = idx_end;
			// ~~~
			list.add(part);
		}

		if (idx_prev_end != -1) {
			list.add(srcStr.substring(idx_prev_end)); // 應抓到 <w:bookmarkEnd
														// w:id="0"/>
		}
		// ============================
		if (list.size() >= 3) {
			String parseStr = StringUtils.join(list, "");
			if (parseStr.length() == srcStr.length()) {
				// ok
				return list.toArray(new String[list.size()]);
			} else {
				LOGGER.error("diff_length[" + parseStr.length() + " vs "
						+ srcStr.length() + "][" + parseStr + "][" + srcStr
						+ "]");
			}
		}
		return new String[] { srcStr };
	}

	private String convertADDatetoTWNDate(Date adDate) {

		if (adDate == null) {
			return "";
		}
		String twnDate = "";
		String yyyy = CapDate.formatDate(adDate, "yyyy");
		String mm = CapDate.formatDate(adDate, "MM");
		String dd = CapDate.formatDate(adDate, "dd");

		String yyy = CapDate.convertDateToTaiwanYear(yyyy);

		twnDate = yyy + "年" + mm + "月" + dd + "日";

		return twnDate;
	}

	private LinkedHashMap<String, String> forTestParamMap_contract() {
		// FOR TEST
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();
		paramMap.put("contractNo", "123456");
		paramMap.put("custName", "邱Ｏ仰");
		paramMap.put("contractor", "程Ｏ介");
		paramMap.put("inviter", "邀同連帶保證人(倘有)等（以下簡稱丙方）");
		paramMap.put("loanAmt", "伍拾萬");
		paramMap.put("signDate", "109年6月3日");
		paramMap.put("bgnMonth", "5");
		paramMap.put("useNum", "■一次 □分次");
		paramMap.put("fundingWay", "2");
		paramMap.put("currAccount", "**************");
		paramMap.put("partyPrincipal", "黃Ｏ達");
		paramMap.put("currSavAccount", "**************");
		paramMap.put("lnyear", "5");
		paramMap.put("lnBgnDate", "109年6月3日");
		paramMap.put("lnEndDate", "111年6月2日");
		paramMap.put("repayWay", "2");
		paramMap.put("gracePeriod", "12");
		paramMap.put("period", "1");
		paramMap.put("periodNum", "48");
		paramMap.put("capitalBgn", "13");
		paramMap.put("capitalEnd", "60");
		paramMap.put("capitalAmt", "壹萬零肆佰壹拾柒");
		paramMap.put("other", "ＯＯＯＯＯＯＯＯＯＯＯＯＯＯＯＯＯＯ");
		paramMap.put("contractDate", "109年6月4日");
		paramMap.put("contractRate", "0.01");
		paramMap.put("addRate", "0.09");
		paramMap.put("addRate2", "1.25");
		paramMap.put("location", "台北");
		paramMap.put("origNum", "壹");
		paramMap.put("copyNum", "貳");
		// paramMap.put("crossSelling", "■同意□不同意");
		paramMap.put("proxyBrId", "忠孝分行");
		paramMap.put("proxy", "林Ｏ青");
		paramMap.put("proxyAddr", "台北市大安區信義路三段182號1樓");
		paramMap.put("party", "測試乙方企業");
		paramMap.put("partyAgent", "甲乙丙");
		paramMap.put("partyAddr", "台北市大安區信義路三段182號2樓");
		paramMap.put("partyId", "12345678");
		paramMap.put("party2", "測試丙方企業");
		paramMap.put("guarantor", "我都保");
		paramMap.put("guarId", "A123456789");
		paramMap.put("guarAddr", "台北市大安區信義路三段182號3樓");

		return paramMap;
	}

	private ByteArrayOutputStream gen_l120m01a_note_typeC(
			PageParameters params, L120M01A l120m01a, String mock_mode,
			String traceStr, String tempKey) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		String templateName = "LMS_note_smallBussC_V202006.xml";
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		// String paymentDate = ""; // 支付日
		// String paymentDateYear = ""; // 支付日-年
		// String paymentDateMonth = ""; // 支付日-月
		// String paymentDateDay = ""; // 支付日-日
		String paymentAmt = ""; // 支付金額
		String paymentBrId = ""; // 付款分行
		String paymentAddr = ""; // 付款分行地址
		// String drawingDate = ""; // 發票日
		// String drawingDateYear = ""; // 發票日-年
		// String drawingDateMonth = ""; // 發票日-月
		// String drawingDateDay = ""; // 發票日-日
		String drawer_1 = ""; // 發票人
		String drawerAddr_1 = ""; // 發票人地址
		String drawer_2 = ""; // 發票人
		String drawerAddr_2 = ""; // 發票人地址
		String drawer_3 = ""; // 發票人
		String drawerAddr_3 = ""; // 發票人地址
		String drawer_4 = ""; // 發票人
		String drawerAddr_4 = ""; // 發票人地址
		String drawer_5 = ""; // 發票人
		String drawerAddr_5 = ""; // 發票人地址
		String drawer_6 = ""; // 發票人
		String drawerAddr_6 = ""; // 發票人地址
		String drawer_7 = ""; // 發票人
		String drawerAddr_7 = ""; // 發票人地址
		String drawer_8 = ""; // 發票人
		String drawerAddr_8 = ""; // 發票人地址
		String drawer_9 = ""; // 發票人
		String drawerAddr_9 = ""; // 發票人地址

		try {
			// paramMap.put("Text001", "邀同連帶保證人["+"XXXXXXXXXXXX"+"]等（以下簡稱丙方）");

			boolean isForTest = false;
			// if (params.containsKey("isForTest")) {
			// if (Util.equals(Util.trim(params.getString("isForTest")), "Y")) {
			// isForTest = true;
			// }
			// }

			if (isForTest) {
				paramMap = null;
				paramMap = this.forTestParamMap_note();
			} else {

				L120S14B l120s14b = null;
                l120s14b = l120s14bDao.findByOid(tempKey);
                /*
				List<L120S14B> list = l120s14bDao.findByMainId(l120m01a
						.getMainId());
				if (list != null && !list.isEmpty()) {
					l120s14b = list.get(0);
				}
				*/

				if (l120s14b == null) {
					throw new CapException(
							"請先輸入中央銀行專案融通C方案貸款「本票」資訊後，再執行本列印功能。", getClass());
				}

				BigDecimal t_paymentAmt = l120s14b.getPaymentAmt();
				paymentAmt = t_paymentAmt == null ? "" : CapCommonUtil
						.toChineseUpperAmount(
								LMSUtil.pretty_numStr(t_paymentAmt), true);// 支付金額

				String t_paymentBrId = Util.trim(l120s14b.getPaymentBrId());
				if (Util.notEquals(t_paymentBrId, "")) {
					IBranch iBranch = branchService.getBranch(t_paymentBrId);
					paymentBrId = Util.trim(iBranch.getBrName());
				} else {
					paymentBrId = "　　　　　　";// 付款分行
				}

				paymentAddr = Util.equals(Util.trim(l120s14b.getPaymentAddr()),
						"") ? "　　　　　　　　　　　　　　　　　　" : Util.trim(l120s14b
						.getPaymentAddr());// 付款分行地址

				// String drawingDate = ""; // 發票日
				// String drawingDateYear = ""; // 發票日-年
				// String drawingDateMonth = ""; // 發票日-月
				// String drawingDateDay = ""; // 發票日-日

				drawer_1 = Util.equals(Util.trim(l120s14b.getDrawer_1()), "") ? ""
						: Util.trim(l120s14b.getDrawer_1()); // 發票人
				drawerAddr_1 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_1()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_1()); // 發票人地址

				drawer_2 = Util.equals(Util.trim(l120s14b.getDrawer_2()), "") ? ""
						: Util.trim(l120s14b.getDrawer_2()); // 發票人
				drawerAddr_2 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_2()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_2()); // 發票人地址
				drawer_3 = Util.equals(Util.trim(l120s14b.getDrawer_3()), "") ? ""
						: Util.trim(l120s14b.getDrawer_3()); // 發票人
				drawerAddr_3 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_3()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_3()); // 發票人地址
				drawer_4 = Util.equals(Util.trim(l120s14b.getDrawer_4()), "") ? ""
						: Util.trim(l120s14b.getDrawer_4()); // 發票人
				drawerAddr_4 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_4()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_4()); // 發票人地址
				drawer_5 = Util.equals(Util.trim(l120s14b.getDrawer_5()), "") ? ""
						: Util.trim(l120s14b.getDrawer_5()); // 發票人
				drawerAddr_5 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_5()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_5()); // 發票人地址
				drawer_6 = Util.equals(Util.trim(l120s14b.getDrawer_6()), "") ? ""
						: Util.trim(l120s14b.getDrawer_6()); // 發票人
				drawerAddr_6 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_6()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_6()); // 發票人地址
				drawer_7 = Util.equals(Util.trim(l120s14b.getDrawer_7()), "") ? ""
						: Util.trim(l120s14b.getDrawer_7()); // 發票人
				drawerAddr_7 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_7()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_7()); // 發票人地址
				drawer_8 = Util.equals(Util.trim(l120s14b.getDrawer_8()), "") ? ""
						: Util.trim(l120s14b.getDrawer_8()); // 發票人
				drawerAddr_8 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_8()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_8()); // 發票人地址
				drawer_9 = Util.equals(Util.trim(l120s14b.getDrawer_9()), "") ? ""
						: Util.trim(l120s14b.getDrawer_9()); // 發票人
				drawerAddr_9 = Util.equals(
						Util.trim(l120s14b.getDrawerAddr_9()), "") ? "" : Util
						.trim(l120s14b.getDrawerAddr_9()); // 發票人地址

				// paramMap.put("paymentDate", paymentDate);// 支付日
				// paramMap.put("paymentDateYear", paymentDateYear);// 支付日-年
				// paramMap.put("paymentDateMonth", paymentDateMonth);// 支付日-月
				// paramMap.put("paymentDateDay", paymentDateDay);// 支付日-日
				paramMap.put("paymentAmt", paymentAmt);// 支付金額
				paramMap.put("paymentBrId", paymentBrId);// 付款分行
				paramMap.put("paymentAddr", paymentAddr);// 付款分行地址
				// paramMap.put("drawingDate", drawingDate);// 發票日
				// paramMap.put("drawingDateYear", drawingDateYear);// 發票日-年
				// paramMap.put("drawingDateMonth", drawingDateMonth);// 發票日-月
				// paramMap.put("drawingDateDay", drawingDateDay);// 發票日-日
				paramMap.put("drawer_1", drawer_1);// 發票人
				paramMap.put("drawerAddr_1", drawerAddr_1);// 發票人地址
				paramMap.put("drawer_2", drawer_2);// 發票人
				paramMap.put("drawerAddr_2", drawerAddr_2);// 發票人地址
				paramMap.put("drawer_3", drawer_3);// 發票人
				paramMap.put("drawerAddr_3", drawerAddr_3);// 發票人地址
				paramMap.put("drawer_4", drawer_4);// 發票人
				paramMap.put("drawerAddr_4", drawerAddr_4);// 發票人地址
				paramMap.put("drawer_5", drawer_5);// 發票人
				paramMap.put("drawerAddr_5", drawerAddr_5);// 發票人地址
				paramMap.put("drawer_6", drawer_6);// 發票人
				paramMap.put("drawerAddr_6", drawerAddr_6);// 發票人地址
				paramMap.put("drawer_7", drawer_7);// 發票人
				paramMap.put("drawerAddr_7", drawerAddr_7);// 發票人地址
				paramMap.put("drawer_8", drawer_8);// 發票人
				paramMap.put("drawerAddr_8", drawerAddr_8);// 發票人地址
				paramMap.put("drawer_9", drawer_9);// 發票人
				paramMap.put("drawerAddr_9", drawerAddr_9);// 發票人地址

			}

			Map<String, String> convert_paramMap = ContractDocUtil
					.convert_paramValue_for_XML_Predefined_entities(paramMap);
			String templateStr = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);
			String outputStr = join_word_template_param(traceStr, templateStr,
					convert_paramMap);
			baos = this.writeWordContent(outputStr);
		} catch (Exception e) {
			LOGGER.error(traceStr);
			LOGGER.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e.getMessage(), getClass());
		}
		return baos;
	}

	private LinkedHashMap<String, String> forTestParamMap_note() {
		// FOR TEST
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();
		// paramMap.put("paymentDate", paymentDate);// 支付日
		// paramMap.put("paymentDateYear", paymentDateYear);// 支付日-年
		// paramMap.put("paymentDateMonth", paymentDateMonth);// 支付日-月
		// paramMap.put("paymentDateDay", paymentDateDay);// 支付日-日
		paramMap.put("paymentAmt", "壹萬零肆佰壹拾柒元");// 支付金額
		paramMap.put("paymentBrId", "忠孝分行");// 付款分行
		paramMap.put("paymentAddr", "台北市大安區信義路三段100巷182號100樓之XXXXXXXXX");// 付款分行地址
		// paramMap.put("drawingDate", drawingDate);// 發票日
		// paramMap.put("drawingDateYear", drawingDateYear);// 發票日-年
		// paramMap.put("drawingDateMonth", drawingDateMonth);// 發票日-月
		// paramMap.put("drawingDateDay", drawingDateDay);// 發票日-日
		paramMap.put("drawer_1", "黃甲甲");// 發票人
		paramMap.put("drawerAddr_1", "台北市大安區信義路三段182號22222222樓");// 發票人地址
		paramMap.put("drawer_2", "黃乙乙");// 發票人
		paramMap.put("drawerAddr_2", "台北市大安區信義路三段182號33333333樓");// 發票人地址
		paramMap.put("drawer_3", "黃餅炳");// 發票人
		paramMap.put("drawerAddr_3", "台北市大安區信義路三段182號44444444樓");// 發票人地址
		paramMap.put("drawer_4", "黃丁丁");// 發票人
		paramMap.put("drawerAddr_4", "台北市大安區信義路三段182號55555555樓");// 發票人地址
		paramMap.put("drawer_5", "黃戊戊");// 發票人
		paramMap.put("drawerAddr_5", "台北市大安區信義路三段182號66666666樓");// 發票人地址
		paramMap.put("drawer_6", "黃己己");// 發票人
		paramMap.put("drawerAddr_6", "台北市大安區信義路三段182號77777777樓");// 發票人地址
		paramMap.put("drawer_7", "黃更更");// 發票人
		paramMap.put("drawerAddr_7", "台北市大安區信義路三段182號88888888樓");// 發票人地址
		paramMap.put("drawer_8", "黃辛辛");// 發票人
		paramMap.put("drawerAddr_8", "台北市大安區信義路三段182號9樓");// 發票人地址
		paramMap.put("drawer_9", "黃ＯＯ");// 發票人
		paramMap.put("drawerAddr_9", "台北市大安區信義路三段182號10樓");// 發票人地址

		return paramMap;
	}

	private ByteArrayOutputStream gen_l120m01a_noteAuth_typeC(
			PageParameters params, L120M01A l120m01a, String mock_mode,
			String traceStr, String tempKey) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		String templateName = "LMS_noteAuth_smallBussC_V202006.xml";
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		// String issueDate =""; // 簽發日
		String issueAmt = ""; // 簽發金額
		String principal_1 = ""; // 立授權書人
		String frontMan = ""; // 負責人
		String principalAddr_1 = ""; // 地址
		String principal_2 = ""; // 立授權書人
		String principalAddr_2 = ""; // 地址
		String principal_3 = ""; // 立授權書人
		String principalAddr_3 = ""; // 地址
		String principal_4 = ""; // 立授權書人
		String principalAddr_4 = ""; // 地址

		try {
			// paramMap.put("Text001", "邀同連帶保證人["+"XXXXXXXXXXXX"+"]等（以下簡稱丙方）");

			boolean isForTest = false;
			// if (params.containsKey("isForTest")) {
			// if (Util.equals(Util.trim(params.getString("isForTest")), "Y")) {
			// isForTest = true;
			// }
			// }

			if (isForTest) {
				paramMap = null;
				paramMap = this.forTestParamMap_noteAuth();
			} else {

				L120S14C l120s14c = null;
                l120s14c = l120s14cDao.findByOid(tempKey);
                /*
				List<L120S14C> list = l120s14cDao.findByMainId(l120m01a
						.getMainId());
				if (list != null && !list.isEmpty()) {
					l120s14c = list.get(0);
				}
				*/

				if (l120s14c == null) {
					throw new CapException(
							"請先輸入中央銀行專案融通C方案貸款「授權書」資訊後，再執行本列印功能。", getClass());
				}

				// String issueDate =""; // 簽發日

				BigDecimal t_issueAmt = l120s14c.getIssueAmt();
				issueAmt = t_issueAmt == null ? "" : CapCommonUtil
						.toChineseUpperAmount(
								LMSUtil.pretty_numStr(t_issueAmt), true);// 簽發金額

				principal_1 = Util.equals(Util.trim(l120s14c.getPrincipal_1()),
						"") ? "" : Util.trim(l120s14c.getPrincipal_1()); // 立授權書人
				frontMan = Util.equals(Util.trim(l120s14c.getFrontMan()), "") ? ""
						: Util.trim(l120s14c.getFrontMan()); // 負責人
				principalAddr_1 = Util.equals(
						Util.trim(l120s14c.getPrincipalAddr_1()), "") ? ""
						: Util.trim(l120s14c.getPrincipalAddr_1()); // 地址
				principal_2 = Util.equals(Util.trim(l120s14c.getPrincipal_2()),
						"") ? "" : Util.trim(l120s14c.getPrincipal_2()); // 立授權書人
				principalAddr_2 = Util.equals(
						Util.trim(l120s14c.getPrincipalAddr_2()), "") ? ""
						: Util.trim(l120s14c.getPrincipalAddr_2()); // 地址
				principal_3 = Util.equals(Util.trim(l120s14c.getPrincipal_3()),
						"") ? "" : Util.trim(l120s14c.getPrincipal_3()); // 立授權書人
				principalAddr_3 = Util.equals(
						Util.trim(l120s14c.getPrincipalAddr_3()), "") ? ""
						: Util.trim(l120s14c.getPrincipalAddr_3()); // 地址
				principal_4 = Util.equals(Util.trim(l120s14c.getPrincipal_4()),
						"") ? "" : Util.trim(l120s14c.getPrincipal_4()); // 立授權書人
				principalAddr_4 = Util.equals(
						Util.trim(l120s14c.getPrincipalAddr_4()), "") ? ""
						: Util.trim(l120s14c.getPrincipalAddr_4()); // 地址

				// paramMap.put("issueDate", issueDate);// 簽發日
				paramMap.put("issueAmt", issueAmt);// 簽發金額
				paramMap.put("principal_1", principal_1);// 立授權書人
				paramMap.put("frontMan", frontMan);// 負責人
				paramMap.put("principalAddr_1", principalAddr_1);// 地址
				paramMap.put("principal_2", principal_2);// 立授權書人
				paramMap.put("principalAddr_2", principalAddr_2);// 地址
				paramMap.put("principal_3", principal_3);// 立授權書人
				paramMap.put("principalAddr_3", principalAddr_3);// 地址
				paramMap.put("principal_4", principal_4);// 立授權書人
				paramMap.put("principalAddr_4", principalAddr_4);// 地址

			}

			Map<String, String> convert_paramMap = ContractDocUtil
					.convert_paramValue_for_XML_Predefined_entities(paramMap);
			String templateStr = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);
			String outputStr = join_word_template_param(traceStr, templateStr,
					convert_paramMap);
			baos = this.writeWordContent(outputStr);
		} catch (Exception e) {
			LOGGER.error(traceStr);
			LOGGER.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e.getMessage(), getClass());
		}
		return baos;
	}

	private LinkedHashMap<String, String> forTestParamMap_noteAuth() {
		// FOR TEST
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		// paramMap.put("issueDate", issueDate);// 簽發日
		paramMap.put("issueAmt", "壹萬零肆佰壹拾柒元");// 簽發金額
		paramMap.put("principal_1", "黃甲甲");// 立授權書人
		paramMap.put("frontMan", "黃負責");// 負責人
		paramMap.put("principalAddr_1", "台北市大安區信義路三段182號22222222樓");// 地址
		paramMap.put("principal_2", "黃乙乙");// 立授權書人
		paramMap.put("principalAddr_2", "台北市大安區信義路三段182號33333333樓");// 地址
		paramMap.put("principal_3", "黃餅炳");// 立授權書人
		paramMap.put("principalAddr_3", "台北市大安區信義路三段182號44444444樓");// 地址
		paramMap.put("principal_4", "黃丁丁");// 立授權書人
		paramMap.put("principalAddr_4", "台北市大安區信義路三段182號55555555樓");// 地址

		return paramMap;
	}

	private ByteArrayOutputStream gen_l120m01a_deduction_typeC(
			PageParameters params, L120M01A l120m01a, String mock_mode,
			String traceStr, String tempKey) throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		String templateName = "LMS_deduction_smallBussC_V202006.xml";
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		String agentBrId = ""; // 被授權行
		String depositType = ""; // 存款種類
		String depositAcct = ""; // 存款帳戶
		String borrower = ""; // 借款人
		String depositor = ""; // 存款人
		String depositorId = ""; // 存款人統一編號
		String depositorAddr = ""; // 存款人地址
		String director = ""; // 負責人
		String directorId = ""; // 負責人統一編號

		try {

			boolean isForTest = false;
			// if (params.containsKey("isForTest")) {
			// if (Util.equals(Util.trim(params.getString("isForTest")), "Y")) {
			// isForTest = true;
			// }
			// }

			if (isForTest) {
				paramMap = null;
				paramMap = this.forTestParamMap_deduction();
			} else {

				L120S14D l120s14d = null;
                l120s14d = l120s14dDao.findByOid(tempKey);
                /*
				List<L120S14D> list = l120s14dDao.findByMainId(l120m01a
						.getMainId());
				if (list != null && !list.isEmpty()) {
					l120s14d = list.get(0);
				}
				*/

				if (l120s14d == null) {
					throw new CapException(
							"請先輸入中央銀行專案融通C方案貸款「授權扣帳」資訊後，再執行本列印功能。", getClass());
				}

				String t_agentBrId = Util.trim(l120s14d.getAgentBrId());
				if (Util.notEquals(t_agentBrId, "")) {
					IBranch iBranch = branchService.getBranch(t_agentBrId);
					agentBrId = Util.trim(iBranch.getBrName());
				} else {
					agentBrId = "　　　　　　";// 被授權行
				}

				depositType = Util.equals(Util.trim(l120s14d.getDepositType()),
						"") ? "　　　　　　" : Util.trim(l120s14d.getDepositType()); // 存款種類
				depositAcct = Util.equals(Util.trim(l120s14d.getDepositAcct()),
						"") ? "　　　　　　　　" : Util.trim(l120s14d.getDepositAcct()); // 存款帳戶
				borrower = Util.equals(Util.trim(l120s14d.getBorrower()), "") ? "　　　　　　"
						: Util.trim(l120s14d.getBorrower()); // 借款人
				depositor = Util.equals(Util.trim(l120s14d.getDepositor()), "") ? "　　　　　　"
						: Util.trim(l120s14d.getDepositor()); // 存款人
				depositorId = Util.equals(Util.trim(l120s14d.getDepositorId()),
						"") ? "　　　　　　" : Util.trim(l120s14d.getDepositorId()); // 存款人統一編號
				depositorAddr = Util.equals(
						Util.trim(l120s14d.getDepositorAddr()), "") ? "　　　　　　"
						: Util.trim(l120s14d.getDepositorAddr()); // 存款人地址
				director = Util.equals(Util.trim(l120s14d.getDirector()), "") ? "　　　　　　"
						: Util.trim(l120s14d.getDirector()); // 負責人
				directorId = Util.equals(Util.trim(l120s14d.getDirectorId()),
						"") ? "　　　　　　" : Util.trim(l120s14d.getDirectorId()); // 負責人統一編號

				paramMap.put("agentBrId", agentBrId);// 被授權行
				paramMap.put("depositType", depositType);// 存款種類
				paramMap.put("depositAcct", depositAcct);// 存款帳戶
				paramMap.put("borrower", borrower);// 借款人
				paramMap.put("depositor", depositor);// 存款人
				paramMap.put("depositorId", depositorId);// 存款人統一編號
				paramMap.put("depositorAddr", depositorAddr);// 存款人地址
				paramMap.put("director", director);// 負責人
				paramMap.put("directorId", directorId);// 負責人統一編號

			}

			Map<String, String> convert_paramMap = ContractDocUtil
					.convert_paramValue_for_XML_Predefined_entities(paramMap);
			String templateStr = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);
			String outputStr = join_word_template_param(traceStr, templateStr,
					convert_paramMap);
			baos = this.writeWordContent(outputStr);
		} catch (Exception e) {
			LOGGER.error(traceStr);
			LOGGER.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e.getMessage(), getClass());
		}
		return baos;
	}

    private ByteArrayOutputStream gen_l120m01a_contractSup_typeC(
            PageParameters params, L120M01A l120m01a, String mock_mode,
            String traceStr, String tempKey) throws CapException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        // 預設為有連保人版本
        String templateName = "LMS_contractSup_smallBussC_G_V202106.xml";
        LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

        String supContractNo = "";// 合約編號
        String frequency = "";// 次數
        String supDate = "";// 簽約日
        String supLnyear = "";// 授信期間
        String supLnBgnDate = "";// 授信起日
        String supLnEndDate = "";// 授信迄日
        String supGracePeriod = "";// 寬限期
        String supPeriod = "";// 每期
        String supPeriodNum = "";// 期數
        String supCapitalBgn = "";// 還本期數 - 起
        String supCapitalEnd = "";// 還本期數 - 迄
        String supCapitalAmt = "";// 還本金額
        String supProxyBrId = "";// 甲方代理分行
        String supProxy = "";// 甲方代理人
        String supProxyAddr = "";// 甲方代理地址
        String supParty = "";// 乙方
        String supPartyAgent = "";// 乙方代理人
        String supPartyAddr = "";// 乙方地址
        String supPartyId = "";// 乙方統編
        String supParty2 = "";// 丙方
        String supGuarantor = "";// 連保人
        String supGuarId = "";// 連保人統編
        String supGuarAddr = "";// 連保人地址

        try {
            boolean isForTest = false;

            if (isForTest) {
                paramMap = null;
                paramMap = this.forTestParamMap_contract();
            } else {

                L120S14F l120s14f = null;
                l120s14f = l120s14fDao.findByOid(tempKey);

                if (l120s14f == null) {
                    throw new CapException(
                            "請先輸入中央銀行專案融通C方案貸款「增補合約」資訊後，再執行本列印功能。", getClass());
                }

                supContractNo = Util.trim(l120s14f.getSupContractNo());// 合約編號

                Integer t_frequency = l120s14f.getFrequency();
                frequency = t_frequency == null ? "" : t_frequency.toString();// 次數

                Date t_supDate = l120s14f.getSupDate();
                supDate = t_supDate == null ? "___年__月__日" : this
                        .convertADDatetoTWNDate(t_supDate);// 簽約日

                Integer t_supLnyear = l120s14f.getSupLnyear();
                supLnyear = t_supLnyear == null ? "___" : t_supLnyear.toString();// 授信期間

                Date t_supLnBgnDate = l120s14f.getSupLnBgnDate();
                supLnBgnDate = t_supLnBgnDate == null ? "___年__月__日" : this
                        .convertADDatetoTWNDate(t_supLnBgnDate);// 授信起日

                Date t_supLnEndDate = l120s14f.getSupLnEndDate();
                supLnEndDate = t_supLnEndDate == null ? "___年__月__日" : this
                        .convertADDatetoTWNDate(t_supLnEndDate);// 授信迄日

                Integer t_supGracePeriod = l120s14f.getSupGracePeriod();
                supGracePeriod = t_supGracePeriod == null ? "     " : t_supGracePeriod
                        .toString();// 寬限期

                Integer t_supPeriod = l120s14f.getSupPeriod();
                supPeriod = t_supPeriod == null ? "　　" : t_supPeriod.toString();// 每期

                Integer t_supPeriodNum = l120s14f.getSupPeriodNum();
                supPeriodNum = t_supPeriodNum == null ? "　　　" : t_supPeriodNum
                        .toString();// 期數

                Integer t_supCapitalBgn = l120s14f.getSupCapitalBgn();
                supCapitalBgn = t_supCapitalBgn == null ? "    " : t_supCapitalBgn
                        .toString();// 還本期數 - 起

                Integer t_supCapitalEnd = l120s14f.getSupCapitalEnd();
                supCapitalEnd = t_supCapitalEnd == null ? "    " : t_supCapitalEnd
                        .toString();// 還本期數 - 迄

                BigDecimal t_supCapitalAmt = l120s14f.getSupCapitalAmt();
                supCapitalAmt = t_supCapitalAmt == null ? "　　　　" : CapCommonUtil
                        .toChineseUpperAmount(
                                LMSUtil.pretty_numStr(t_supCapitalAmt), true);// 還本金額

                String t_supProxyBrId = Util.trim(l120s14f.getSupProxyBrId());
                if (Util.notEquals(t_supProxyBrId, "")) {
                    IBranch iBranch = branchService.getBranch(t_supProxyBrId);
                    supProxyBrId = Util.trim(iBranch.getBrName());
                } else {
                    supProxyBrId = "";// 甲方代理分行
                }

                String t_supProxy = Util.trim(l120s14f.getSupProxy());
                if (Util.notEquals(t_supProxy, "")) {
                    supProxy = Util.trim(userInfoService.getUserName(t_supProxy));
                } else {
                    supProxy = "";// 甲方代理人
                }

                supProxyAddr = Util.trim(l120s14f.getSupProxyAddr());// 甲方代理地址

                supParty = Util.trim(l120s14f.getSupParty());// 乙方
                supPartyAgent = Util.trim(l120s14f.getSupPartyAgent());// 乙方代理人
                supPartyAddr = Util.trim(l120s14f.getSupPartyAddr());// 乙方地址
                supPartyId = Util.trim(l120s14f.getSupPartyId());// 乙方統編

                supParty2 = Util.trim(l120s14f.getSupParty2());// 丙方
                supGuarantor = Util.trim(l120s14f.getSupGuarantor());// 連保人
                supGuarId = Util.trim(l120s14f.getSupGuarId());// 連保人統編
                supGuarAddr = Util.trim(l120s14f.getSupGuarAddr());// 連保人地址

				if(Util.isEmpty(supGuarantor)){	// 換成沒有連保人範本
					templateName = "LMS_contractSup_smallBussC_V202106.xml";
				}

                paramMap.put("supContractNo", supContractNo);
                paramMap.put("frequency", frequency);
                paramMap.put("supParty_1", supParty);
                paramMap.put("supDate", supDate);
                paramMap.put("supContractNo_1", supContractNo);
                paramMap.put("frequency_1", frequency);
                paramMap.put("supLnyear", supLnyear);
                paramMap.put("supLnBgnDate", supLnBgnDate);
                paramMap.put("supLnEndDate", supLnEndDate);
                paramMap.put("supGracePeriod", supGracePeriod);
                paramMap.put("supPeriod", supPeriod);
                paramMap.put("supPeriodNum", supPeriodNum);
                paramMap.put("supCapitalBgn", supCapitalBgn);
                paramMap.put("supCapitalEnd", supCapitalEnd);
                paramMap.put("supCapitalAmt", supCapitalAmt);
                paramMap.put("supProxyBrId", supProxyBrId);
                paramMap.put("supProxy", supProxy);
                paramMap.put("supProxyAddr", supProxyAddr);
                paramMap.put("supParty", supParty);
                paramMap.put("supPartyAgent", supPartyAgent);
                paramMap.put("supPartyAddr", supPartyAddr);
                paramMap.put("supPartyId", supPartyId);
                paramMap.put("supParty2", supParty2);
                paramMap.put("supGuarantor", supGuarantor);
                paramMap.put("supGuarId", supGuarId);
                paramMap.put("supGuarAddr", supGuarAddr);
            }

            Map<String, String> convert_paramMap = ContractDocUtil
                    .convert_paramValue_for_XML_Predefined_entities(paramMap);
            String templateStr = Util.getFileContent(Util.trim(PropUtil
                    .getProperty("loadFile.dir")) + "word/" + templateName);
            String outputStr = join_word_template_param(traceStr, templateStr,
                    convert_paramMap);
            baos = this.writeWordContent(outputStr);
        } catch (Exception e) {
            LOGGER.error(traceStr);
            LOGGER.error(StrUtils.getStackTrace(e));
            throw new CapMessageException(e.getMessage(), getClass());
        }
        return baos;
    }

	private LinkedHashMap<String, String> forTestParamMap_deduction() {
		// FOR TEST
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();

		paramMap.put("agentBrId", "忠孝分行");// 被授權行
		paramMap.put("depositType", "活期儲蓄");// 存款種類
		paramMap.put("depositAcct", "**************");// 存款帳戶
		paramMap.put("borrower", "測試企業");// 借款人
		paramMap.put("depositor", "黃負責人1");// 存款人
		paramMap.put("depositorId", "A123456789");// 存款人統一編號
		paramMap.put("depositorAddr", "台北市大安區信義路三段182號22222222樓");// 存款人地址
		paramMap.put("director", "黃負責人2");// 負責人
		paramMap.put("directorId", "F123456789");// 負責人統一編號

		return paramMap;
	}

}