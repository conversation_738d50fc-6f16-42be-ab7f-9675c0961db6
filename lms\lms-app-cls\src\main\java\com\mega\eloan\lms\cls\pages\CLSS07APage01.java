package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.panels.CLSM01Panel;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 綜合評估/往來彙總  - 綜合評估及敘做理由
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/clss07aA/{page}")
public class CLSS07APage01 extends AbstractEloanForm {
	
	@Autowired
	CLSService clsService;

	@Override
	public void execute(ModelMap model, PageParameters params)
			throws Exception {
		new CLSM01Panel("lmsm01_panel").processPanelData(model, params);
		boolean clsMinor_Y = false;
		String mainId = Util.trim(params.getString("mainId"));
		L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
		L120M01I l120m01i = clsService.syncL120M01I_clsMinor(l120m01a);
		if (l120m01i != null && Util.equals("Y", l120m01i.getClsMinor())) {
			clsMinor_Y = true;
		}
		model.addAttribute("clsMinor_Y", clsMinor_Y);
	}

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(parameters);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// remove("_headerPanel");
		model.addAttribute("showHeader", false); // 不顯示 _headerPanel
	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
