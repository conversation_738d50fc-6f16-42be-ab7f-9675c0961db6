/* 
 * FSSGridService.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service;

import com.mega.eloan.lms.model.F101M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 財報報表之Grid Service
 * </pre>
 * 
 * @since 2011/11/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/17,iristu,new
 *          </ul>
 */
public interface FSSGridService {

	/**
	 * 搜尋一般財務報表。
	 * 
	 * @param search
	 *            提供給grid handler的搜尋條件
	 * @return Page<F101M01A>
	 */
	Page<F101M01A> getF1010V01(ISearch search);

	/**
	 * 搜尋預估財報。
	 * 
	 * @param pageSetting
	 *            搜尋條件
	 * @return Page<F101M01A>
	 */
	Page<F101M01A> getF1020V01(ISearch pageSetting);

	/**
	 * 取得前期財報資料。(for grid)
	 * 
	 * @param search
	 *            提供給grid handler的搜尋條件
	 * @return Page<F101M01A>
	 */
	Page<F101M01A> findPreDocsPages(ISearch search);

}
