package com.mega.eloan.lms.lms.report.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C121M01EDao;
import com.mega.eloan.lms.lms.pages.LMS1015M01Page;
import com.mega.eloan.lms.lms.report.LMS1015R00RptService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01E;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

@Service("lms1015r00rptservice")
public class LMS1015R00RptServiceImpl extends AbstractIISIReportService
		implements LMS1015R00RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1015R00RptServiceImpl.class);
	private Map<String, CapAjaxFormResult> container = new LinkedHashMap<String, CapAjaxFormResult>();

	private Map<String, String> keyMap = new LinkedHashMap<String, String>();
	@Resource
	CLSService clsService;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	C121M01EDao c121m01eDao;

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {
		initCodeType();	

		Properties prop_lms1015m01Page = MessageBundleScriptCreator.getComponentResource(LMS1015M01Page.class);

		String oid = params.getString("oid");
		C120M01A c120m01a = clsService.findC120M01A_oid(oid);
		BigDecimal amtUnit = new BigDecimal("1000");
		DecimalFormat df = new DecimalFormat("###,##0");
		if(c120m01a!=null){

			try {
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				C120S01B c120s01b = clsService.findC120S01B(c120m01a);
				C120S01C c120s01c = clsService.findC120S01C(c120m01a);
				C120S01D c120s01d = clsService.findC120S01D(c120m01a);
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				L120S01M l120s01m = clsService.findL120S01M(c120s01e);

				// 若用 JSONObject.fromObject(...)，遇到有關連的 entity 時，會出錯
				reportData.setAll(DataParse.toJSON(c120m01a));

				Map<String, Object> m = new HashMap<String, Object>();

				IBranch branch = branchService.getBranch(c120m01a.getOwnBrId());
				m.put("BRANCHNAME", OverSeaUtil.getBrNameByLocale(branch, LMSUtil.getLocale()));
				m.put("prtDate", CapDate.getCurrentDate("yyyy-MM-dd"));
				
				String randomCode = "";
				String approver = "";
			    C121M01A c121m01a = clsService.findC121M01A(c120m01a);
			    if(c121m01a!=null){
			     randomCode = Util.trim(c121m01a.getRandomCode());
			     approver = Util.trim(c121m01a.getApprover());
			    }
			    m.put("randomCode", randomCode);
			    m.put("approver", approver);
			    
			    String appr = "";	//經辦
			    String boss = "";	//主管
			    String manager = "";	//經副襄理			    
			    if(c121m01a.getDocStatus().equals(CreditDocStatusEnum.海外_編製中.getCode())){ 
			    	appr = Util.nullToSpace(userInfoService.getUserName(c121m01a.getUpdater()));
					m.put("appr", appr);
			    }else{
			    	List<C121M01E> c121m01eList = null;
				    c121m01eList = c121m01eDao.findByMainId(c121m01a.getMainId());
				    if(c121m01eList!=null){
					    for (C121M01E c121m01e : c121m01eList) {
					    	if ("L5".equals(c121m01e.getStaffJob())){	//經副襄理
					    		manager = Util.nullToSpace(userInfoService.getUserName(c121m01e.getStaffNo()));
					    		m.put("manager", manager);
					    	} else if ("L3".equals(Util.trim(c121m01e.getStaffJob()))) {	//主管
					    		if("".equals(boss)){boss = Util.nullToSpace(userInfoService.getUserName(c121m01e.getStaffNo()));}
					    		else{boss = boss+ "、" +Util.nullToSpace(userInfoService.getUserName(c121m01e.getStaffNo()));}
					    		m.put("boss", boss);
							} else if ("L1".equals(Util.trim(c121m01e.getStaffJob()))) {	//經辦
								appr = Util.nullToSpace(userInfoService.getUserName(c121m01e.getStaffNo()));
								m.put("appr", appr);
							}
					    }
				    }
			    }
			        
				if (l120s01m != null) {					
					m.put("l120s01m_queryDate", CapDate.formatDate(l120s01m.getQueryDate(), "yyyy-MM-dd"));					
				}

				if (c120s01a != null) {
					reportData.setAll(DataParse.toJSON(c120s01a));
					m.put("edu",
							Util.trim(getValue("edu",
									Util.trim(c120s01a.getEdu()))));
					m.put("marry",
							Util.trim(getValue("marry",
									Util.trim(c120s01a.getMarry()))));
				}

				if (c120s01b != null) {
					reportData.setAll(DataParse.toJSON(c120s01b));
					
				
					String value = null;
					String code2 = getJobType2CodeValue(CapString.trimNull(c120s01b
							.getJobType1()));
					
					Map<String, String> map = codeTypeService
							.findByCodeType(code2);

					if (map != null)
						value = map.get(Util.trim(c120s01b.getJobType2()));

					Map<String, String> cls1131m01_othTypemap = codeTypeService
							.findByCodeType("cls1131m01_othType");
					int count = 0;
					String othTypes = "";
					if (cls1131m01_othTypemap != null) {
						String tmp = Util.trim(c120s01c.getOIncome());

						for (String othtype : Util.trim(c120s01c.getOIncome())
								.split(UtilConstants.Mark.SPILT_MARK)) {
							if (count == 0) {
								othTypes = cls1131m01_othTypemap.get(othtype);
								count++;
							} else {
								othTypes = othTypes + "、"
										+ cls1131m01_othTypemap.get(othtype);
							}
						}
					}

					m.put("jobType2", Util.trim(value));
					m.put("oIncome", CapString.trimNull(othTypes)); // 其他收入
					m.put("jobType1",
							Util.trim(getValue("jobType1",
									Util.trim(c120s01b.getJobType1()))));
					m.put("jobTitle",
							Util.trim(getValue("jobTitle",
									Util.trim(c120s01b.getJobTitle()))));
					m.put("inDoc",
							Util.trim(getValue("inDoc",
									Util.trim(c120s01b.getInDoc()))));// yIncomeCert
					m.put("payAmt",c120s01b.getPayAmt()==null?"":df.format(c120s01b.getPayAmt().divide(amtUnit)));

					if(true){
						String seniority_desc = MessageFormat.format(prop_lms1015m01Page.getProperty("report.snrDesc_year"), LMSUtil.pretty_numStr(c120s01b.getSeniority())); //default{若空白}
						if(c120s01b.getSeniority()!=null){
							Integer[] seniorityYM_arr = ClsUtility.seniorityYM_decode(c120s01b.getSeniority());
							if(c120s01b.getSnrM()==null){
								seniority_desc = MessageFormat.format(prop_lms1015m01Page.getProperty("report.snrDesc_year"), String.valueOf(seniorityYM_arr[0]));
							}else{
								seniority_desc = MessageFormat.format(prop_lms1015m01Page.getProperty("report.snrDesc_year_month"), String.valueOf(seniorityYM_arr[0]), String.valueOf(seniorityYM_arr[1]));
							}
						}
						m.put(ClsUtility.SNRDESC, seniority_desc);	//日本報表, 即使是 en.rpt 目前也是「繁中」
					}
				}

				if (c120s01c != null) {
					reportData.setAll(DataParse.toJSON(c120s01c));
					m.put("yIncomeCert",
							Util.trim(getValue("inDoc",
									Util.trim(c120s01c.getYIncomeCert()))));//
					
					m.put("oMoneyAmt",c120s01c.getOMoneyAmt()==null?"":df.format(c120s01c.getOMoneyAmt().divide(amtUnit)));
					m.put("branAmt",c120s01c.getBranAmt()==null?"":df.format(c120s01c.getBranAmt().divide(amtUnit)));
					m.put("invMBalAmt",c120s01c.getInvMBalAmt()==null?"":df.format(c120s01c.getInvMBalAmt().divide(amtUnit)));
					m.put("invOBalAmt",c120s01c.getInvOBalAmt()==null?"":df.format(c120s01c.getInvOBalAmt().divide(amtUnit)));
					m.put("realEstateRentIncomeAmt",c120s01c.getRealEstateRentIncomeAmt()==null?"":df.format(c120s01c.getRealEstateRentIncomeAmt().divide(amtUnit)));
					m.put("yFamAmt",c120s01c.getYFamAmt()==null?"":df.format(c120s01c.getYFamAmt().divide(amtUnit)));
				}

				if (c120s01d != null) {
					// mName'mName
					m.put("mCustId", Util.trim(c120s01d.getMCustId()));
					m.put("mName", Util.trim(c120s01d.getMName()));
				}
				if (c120s01e != null) {
					reportData.setAll(DataParse.toJSON(c120s01e));
				}

				reportData.setAll(m);

			} catch (Exception e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}

		}
		return reportData;
	}

	@Override
	public String getReportDefinition() {
		return "report/lms/LMS1015R00";
	}

	private void initCodeType() {
		if (keyMap == null)
			keyMap = new LinkedHashMap<String, String>();
		keyMap.clear();
		// set key
		keyMap.put("marry", "marry"); // 婚姻狀況
		keyMap.put("edu", "lms1205s01_edu"); // 學歷

		keyMap.put("jobType1", "lms1205s01_jobType1"); // 職業別
		// keyMap.put("jobType2", "jobType"); // 職業別 jobType
		keyMap.put("jobTitle", "lms1205s01_jobTitle"); // 職稱
		keyMap.put("inDoc", "lms1205s01_inDoc"); // 個人所得證明文件
		keyMap.put("oIncome", "cls1131m01_othType"); // 其他收入
		keyMap.put("yIncomeCert", "lms1205s01_inDoc"); // 家庭所得證明文件

		keyMap.put("curr", "Common_Currcy");
		if (container == null)
			container = new LinkedHashMap<String, CapAjaxFormResult>();
		container.clear();

		Collection<String> collection = keyMap.values();
		if (collection != null) {
			String[] keys = collection.toArray(new String[] {});
			container = codeTypeService.findByCodeType(keys);
		}
	}

	private String getJobType2CodeValue(String code) {
		// code = jobType1;
		if (("01").equals(code)) {
			code = "lms1205s01_jobType2a";
		} else if (("02").equals(code)) {
			code = "lms1205s01_jobType2b";
		} else if (("03").equals(code)) {
			code = "lms1205s01_jobType2c";
		} else if (("04").equals(code)) {
			code = "lms1205s01_jobType2d";
		} else if (("05").equals(code)) {
			code = "lms1205s01_jobType2e";
		} else if (("06").equals(code)) {
			code = "lms1205s01_jobType2f";
		} else if (("07").equals(code)) {
			code = "lms1205s01_jobType2g";
		} else if (("08").equals(code)) {
			code = "lms1205s01_jobType2h";
		} else if (("09").equals(code)) {
			code = "lms1205s01_jobType2i";
		} else if (("10").equals(code)) {
			code = "lms1205s01_jobType2j";
		} else if (("11").equals(code)) {
			code = "lms1205s01_jobType2k";
		} else if (("12").equals(code)) {
			code = "lms1205s01_jobType2l";
		} else if (("13").equals(code)) {
			code = "lms1205s01_jobType2m";
		} else if (("14").equals(code)) {
			code = "lms1205s01_jobType2n";
		} else {
			code = "";
		}
		return code;
	}

	private String getValue(String key, String value) {
		Object result = null;
		if (key != null && keyMap != null) {
			String codeTypeKey = keyMap.get(key);
			if (codeTypeKey != null) {
				CapAjaxFormResult cafr = container.get(codeTypeKey);
				if (cafr != null)
					result = cafr.get(value);
			}
		}
		return Util.trim(result != null ? result : value);
	}

	@Override
	public Vector<String> getReportDetailColumns(PageParameters params,
			Engine engine) {
		// 應該搭配 reportData.addDetail
		Vector<String> result = new Vector<String>();
		// ReportBean.column01...........
		for (String s : Util.setColumnMap(30).keySet()) {
			result.add(s);
		}
		return result;
	}

}
