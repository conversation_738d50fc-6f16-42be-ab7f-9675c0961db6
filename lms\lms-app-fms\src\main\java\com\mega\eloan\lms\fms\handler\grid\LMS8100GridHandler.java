package com.mega.eloan.lms.fms.handler.grid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.service.LMS8100Service;
import com.mega.eloan.lms.model.L300M01A;
import com.mega.eloan.lms.model.L300M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 覆審考核表作業
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8100gridhandler")
public class LMS8100GridHandler extends AbstractGridHandler {
	
	@Resource
	BranchService branchService;

	@Resource
	LMS8100Service lms8100Service;

	@SuppressWarnings({ "serial", "unchecked", "rawtypes" })
	public CapGridResult queryGridL300M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String searchType = Util.nullToSpace(params.getString("searchType"));

		if (Util.equals(searchType, "init")) {
			// return new CapGridResult();
		}

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);
		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		
		if(Util.notEquals(user.getUnitNo(), UtilConstants.BankNo.授管處)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, user.getUnitNo());
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		if (Util.equals(searchType, "filter")) {
			String yearFilter = Util.trim(Util.nullToSpace(params.getString("yearFilter")));
			String brIdFilter = Util.trim(Util.nullToSpace(params.getString("brIdFilter")));
			
			if (!Util.isEmpty(yearFilter)) {
				String bgnDate = yearFilter + "-01-01";
				String endDate = yearFilter + "-12-31";
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "bgnDate", 
						new Object[]{CapDate.getDate(bgnDate, "yyyy-MM-dd"), CapDate.getDate(endDate, "yyyy-MM-dd")});
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "endDate", 
						new Object[]{CapDate.getDate(bgnDate, "yyyy-MM-dd"), CapDate.getDate(endDate, "yyyy-MM-dd")});
			}
			if (!Util.isEmpty(brIdFilter)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId", brIdFilter);
			}
		}

//		Page<? extends GenericBean> page = lms8100Service.findPage(
		Page page = lms8100Service.findPage(L300M01A.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter.put("dataDate", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L300M01A meta = (L300M01A) in;
				String dataDate = Util.nullToSpace(TWNDate.toAD(meta.getBgnDate()))
						+ "~" + Util.nullToSpace(TWNDate.toAD(meta.getEndDate()));
				return dataDate;
			}
		});

		List<L300M01A> list = page.getContent();
		for (L300M01A model : list) {
			model.setBranchId(model.getBranchId() + " " 
					+ Util.trim(branchService.getBranchName(model.getBranchId())));
		}
		
		CapGridResult result = new CapGridResult(list, page.getTotalRow(), formatter);

		return result;
	}
	
	@SuppressWarnings({ "serial", "unchecked", "rawtypes" })
	public CapGridResult queryGridL300M01C(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String searchType = Util.nullToSpace(params.getString("searchType"));

		if (Util.equals(searchType, "init")) {
			// return new CapGridResult();
		}
		
		if(Util.notEquals(user.getUnitNo(), UtilConstants.BankNo.授管處)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, user.getUnitNo());
		} else {
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"sendTime", "");
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

//		Page<? extends GenericBean> page = lms8100Service.findPage(
		Page page = lms8100Service.findPage(L300M01C.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter.put("dataDate", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L300M01C meta = (L300M01C) in;
				String dataDate = Util.nullToSpace(TWNDate.toAD(meta.getBgnDate()))
						+ "~" + Util.nullToSpace(TWNDate.toAD(meta.getEndDate()));
				return dataDate;
			}
		});
		
		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow(), formatter);

		return result;
	}

}