/* 
 * L730S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L730S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L730S01A;

/** 授信報案考核表明細檔 **/
@Repository
public class L730S01ADaoImpl extends LMSJpaDao<L730S01A, String>
	implements L730S01ADao {

	@Override
	public L730S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L730S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("itemType");
		search.addOrderBy("itemNo");
		List<L730S01A> list = createQuery(L730S01A.class, search).getResultList();
		return list;
	}
	
	@Override
	public L730S01A findByUniqueKey(String mainId, Date chkYM, String sysType, String itemType, String itemNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "chkYM", chkYM);
		search.addSearchModeParameters(SearchMode.EQUALS, "sysType", sysType);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemNo", itemNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L730S01A> findByIndex01(String mainId, Date chkYM, String sysType, String itemType, String itemNo){
		ISearch search = createSearchTemplete();
		List<L730S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (chkYM != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "chkYM", chkYM);
		if (sysType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "sysType", sysType);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		if (itemNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemNo", itemNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L730S01A.class, search).getResultList();
		}
		return list;
	}
}