<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
            <div id="filterBox" style="display:none">
                <form id="filterForm">
                    <div class="" id="">
                        <table class="tb2" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="doc.custName">姓名/名稱</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" id="custName" name="custName" class="" maxlength="120" size="30" />(<wicket:message key="label.compareLike" />)
                                    </td>
                                </tr>    
                                <tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01H.agentCertYear">證書-年</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" size="10" maxlength="10" id="agentCertYear" name="agentCertYear" />
                                    </td>
                                </tr> 
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01H.agentCertWord">證書-字號</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" id="agentCertWord" name="agentCertWord" class="" maxlength="250" size="30" />(<wicket:message key="label.compareLike" />)
                                    </td>
                                </tr>
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01H.agentCertNo">證書-流水號</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<input type="text" id="agentCertNo" name="agentCertNo" class="" size="30" />
                                    </td>
                                </tr>
								<tr>
                                    <td style="text-align: right;width:30%;" class="hd1">
                                        <wicket:message key="C900M01H.ctlFlag">控制狀態</wicket:message>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                    	<select space="true" combokey="cls260CtlFlagType" combotype="2" id="ctlFlag" name="ctlFlag" style="width:260px">
	                                   	<!--
	                                   	<option></option>
										<option>1.經稽核處查核確定或疑似人頭戶案件</option>
										<option>2.經分行查詢實價登錄價格與買賣契約價格不符之案件</option>
										<option>3.經分行發現疑似製作假文件之案件</option>
										<option>4.同業發生人頭戶房貸案經媒體揭露或受金管會裁罰案例</option>
										-->
	                                   </select>
                                    </td>
                                </tr>                                
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <script>
                var FilterAction = {
                    formId: "#filterForm",
                    gridId: "#gridview",
                    openBox: function(){
                        var $form = $(this.formId).reset();
                        
                        $("#filterBox").thickbox({
                            //query=查詢
                            title: i18n.def["query"],
                            width: 550,
                            height: 320,
                            modal: true,
                            i18n: i18n.def,
                            readOnly: false,
                            align: "center",
                            valign: "bottom",
                            buttons: {
                                "sure": function(){
                                    if (!$form.valid()) {
                                        return false;
                                    }
                                    FilterAction.reloadGrid(JSON.stringify($form.serializeData()));
                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    },
                    /**更新grid
                     *
                     * @param {Object} data 查詢條件
                     */
                    reloadGrid: function(data){
                        $(this.gridId).jqGrid("setGridParam", {
                            postData: {
                                formAction: "queryMain",
                                filetData: data
                            },
                            page: 1,
                            search: true
                        }).trigger("reloadGrid");
                    }
                };
            </script>
        </wicket:panel>
    </body>
</html>
