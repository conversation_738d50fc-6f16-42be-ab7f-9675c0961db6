/* 
 * L120S20ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S20A;

/** LGD額度共用檔 **/
public interface L120S20ADao extends IGenericDao<L120S20A> {

	L120S20A findByOid(String oid);

	List<L120S20A> findByMainId(String mainId);

	List<L120S20A> findByIndex01(String mainId);

	List<L120S20A> findByIndex02(String mainId, String cntrNoCo);

	List<L120S20A> findByIndex03(String mainId, String cntrNoCo, String cntrNo);

	List<Object[]> findMinAllocate(String mainId);

	List<L120S20A> findByCntrNo(String mainId, String cntrNo);
}