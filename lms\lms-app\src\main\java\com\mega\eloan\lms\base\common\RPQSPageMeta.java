package com.mega.eloan.lms.base.common;

public class RPQSPageMeta {
	private String reportId;
	/**
	 * 分行代碼
	 */
	private String brNo = null;
	/**
	 * 分行名稱
	 */
	private String brName = null;
	/**
	 * 頁數
	 */
	private int pageCnt = 0;
	/**
	 * 目前頁內的列數
	 */
	private int rowCnt = 0;

	/**
	 * 執行更新的計數
	 */
	private int updCnt = 0;

	/**
	 * 執行新增的計數
	 */
	private int insCnt = 0;
	/**
	 * 執行刪除的計數
	 */
	private int delCnt = 0;

	/**
	 * 頁面資料內容
	 */
	private String content;

	/**
	 * 頁面內容清空
	 */
	public void resetContent() {
		this.content = "";
	}

	/**
	 * 是否為第2頁
	 * 
	 * @return true:第1頁
	 */
	public boolean isFirstPage() {
		return pageCnt == 1;
	}

	/**
	 * 頁數+11
	 * 
	 * @return 頁數
	 */
	public int incPageCnt() {
		return (this.pageCnt++);
	}

	/**
	 * 列數+1
	 * 
	 * @return 列數
	 */
	public int incRowCnt() {
		return (this.rowCnt++);
	}

	/**
	 * 列數設為0
	 * 
	 * @return
	 */
	public int resetRowCnt() {
		return (this.rowCnt = 0);
	}

	/**
	 * 取得頁數
	 * 
	 * @return 頁數
	 */
	public int getPageCnt() {
		return pageCnt;
	}

	/**
	 * 設定頁數
	 * 
	 * @param pageCnt
	 *            頁數
	 */
	public void setPageCnt(int pageCnt) {
		this.pageCnt = pageCnt;
	}

	/**
	 * 取得列數
	 * 
	 * @return 頁數
	 */
	public int getRowCnt() {
		return rowCnt;
	}

	/**
	 * 設定列數
	 * 
	 * @param rowCnt
	 *            列數
	 */
	public void setRowCnt(int rowCnt) {
		this.rowCnt = rowCnt;
	}

	/**
	 * 取得頁面內容
	 * 
	 * @return 頁面內容
	 */
	public String getContent() {
		return content;
	}

	/**
	 * 取得分行代碼
	 * 
	 * @return 分行代碼
	 */
	public String getBrNo() {
		return brNo;
	}

	/**
	 * 設定分行代碼
	 * 
	 * @param brNo
	 *            分行代碼
	 */
	public void setBrNo(String brNo) {
		this.brNo = brNo;
	}
	
	public String getBrName() {
		return brName;
	}
	public void setBrName(String brName) {
		this.brName = brName;
	}

	public void setContent(String content) {
		this.content = content;
	}

	/**
	 * @return the updCnt
	 */
	public int getUpdCnt() {
		return updCnt;
	}

	/**
	 * @param updCnt
	 *            the updCnt to set
	 */
	public void setUpdCnt(int updCnt) {
		this.updCnt = updCnt;
	}

	/**
	 * @return the insCnt
	 */
	public int getInsCnt() {
		return insCnt;
	}

	/**
	 * @param insCnt
	 *            the insCnt to set
	 */
	public void setInsCnt(int insCnt) {
		this.insCnt = insCnt;
	}

	/**
	 * @return the delCnt
	 */
	public int getDelCnt() {
		return delCnt;
	}

	/**
	 * @param delCnt
	 *            the delCnt to set
	 */
	public void setDelCnt(int delCnt) {
		this.delCnt = delCnt;
	}

	/**
	 * @return the reportId
	 */
	public String getReportId() {
		return reportId;
	}

	/**
	 * @param reportId
	 *            the reportId to set
	 */
	public void setReportId(String reportId) {
		this.reportId = reportId;
	}

}
