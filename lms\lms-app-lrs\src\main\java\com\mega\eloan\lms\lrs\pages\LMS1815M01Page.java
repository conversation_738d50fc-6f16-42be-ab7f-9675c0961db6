/* 
 * LMS1815M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.lrs.panels.LMS1815S01Panel;
import com.mega.eloan.lms.lrs.panels.LMS1815S02Panel;
import com.mega.eloan.lms.model.L181M01A;

/**<pre>
 * 覆審控制檔
 * </pre>
 * @since  2011/9/27
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/27,irene,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/lms1815m01/{page}")
public class LMS1815M01Page extends AbstractEloanForm {

	@Autowired
	CodeItemService cis;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS1815M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		// 依權限設定button

		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.編製中));
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		if(Util.isEmpty(mainOid)){
			addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", AuthType.Accept, params, false));
		}else{
			addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
					AuthType.Accept, RetrialDocStatusEnum.待覆核));
		}
		
		renderJsI18N(LMS1815M01Page.class);

		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2);
		model.addAttribute("tabID", tabID);
		
		Panel panel = getPanel(page, params);
		panel.processPanelData(model, params);
		
	}

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1815S01Panel(TAB_CTX, true);
			renderJsI18N(LMS1815S01Panel.class);
			break;
		case 2:
			panel = new LMS1815S02Panel(TAB_CTX, true);
			renderJsI18N(LMS1815S02Panel.class);
			break;
		default:
			panel = new LMS1815S01Panel(TAB_CTX, true);
			break;
		}
		if (panel == null)
			panel = new Panel(TAB_CTX, true);
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L181M01A.class;
	}

}
