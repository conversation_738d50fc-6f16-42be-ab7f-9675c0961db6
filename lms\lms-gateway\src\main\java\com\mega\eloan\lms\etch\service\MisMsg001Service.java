/* 
 * MisMsg001Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.etch.service;

import java.util.Map;

/**
 * <pre>
 * MIS申貸戶退票、拒往紀錄查詢
 * </pre>
 * 
 * @since 2011年9月30日
 * <AUTHOR>
 * @version <ul>
 *          <li>2011年9月30日,tammyChen,new
 *          </ul>
 */
public interface MisMsg001Service {

	/**
     *<li>00-客戶ID
     *<li>01-客戶ID 重複碼
     *<li>02-存款不足退票金額
	 *<li>03-簽章不符退票張數
	 *<li>04-擅自指定金融業者為本票之擔當付款人退票張數
	 *<li>05-本票提示期限經過前撤銷付款委託退票張數
	 *<li>06-存款不足退票金額
	 *<li>07-簽章不符退票金額
	 *<li>08-擅自指定金融業者為本票之擔當付款人退票金額
	 *<li>09-清償註記存款不足退票張數
	 *<li>10-清償註記簽章不符退票張數
	 *<li>11-清償註記擅自指定金融業者為本票之擔當付款人退票張數
	 *<li>12-清償註記本票提示期限經過前撤銷付款委託退票張數
	 *<li>13-清償註記存款不足退票金額
	 *<li>14-清償註記簽章不符退票金額
	 *<li>15-擅自指定金融業者為本票之擔當付款人退票金額
	 *<li>16-清償註記本票提示期限經過前撤銷付款委託退票金額
	 *<li>17-拒絕往來日期
	 *<li>18-查詢戶名
	 */
	final String[] Msg001Cols = { "CUSTID", "DUPNO", "ADDRNUM", "REFNUM",
			"TXDT", "ADDRKIND", "RJTCD", "ADDRZIP", "CITYR", "TOWNR", "LEER", "ADDRR",
			"UED_DCC","UMS_BCC","SD_BCC","CPE_BCC","UED_BCA","UMS_BCA","SD_BAC","POR_UED_BCC",
			"POR_UMS_BCC","POR_SD_BCC","POR_CPE_BCC","POR_UED_BCA","POR_UMS_BCA","POR_SD_BCA",
			"POR_CPE_BCA","REJECT_DATE","QRY_NAME"};

	/**
	 * 取得申貸戶退票、拒往紀錄
	 * 
	 * @param custId
	 *            統編
	 * @return Map<String, Object>
	 */
	public Map<String, Object> findById(String custId);
	
	/**
	 * 查詢申貸戶是否有退票/拒往資訊
	 * 
	 * @param custId
	 *            統編
	 * @return Map<String, Object>
	 */
	public Map<String, Object> findCntAmtById(String custId);

	/**
	 * 讀取集團轄下公司明細及其聯徵虛擬統編
	 * @param gId
	 * @return
	 */	
	Map<String, Object> findDetailAndBanBygId(String gId);

}
