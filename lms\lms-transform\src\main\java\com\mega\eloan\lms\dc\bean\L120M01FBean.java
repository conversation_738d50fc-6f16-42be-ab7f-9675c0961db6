package com.mega.eloan.lms.dc.bean;

import java.io.Serializable;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * 案件簽報書簽章欄檔
 * 
 * <AUTHOR>
 */
public class L120M01FBean extends BaseBean implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 單位類型 */
	private String branchType = null;
	/** 單位代碼 */
	private String branchId = null;
	/** 行員代碼 */
	private String staffNo = null;
	/** 人員職稱 */
	private String staffJob = null;
	/** 行員姓名 */
	private String staffName = null;
	/** 承作日期 */
	private String cesDate = null;

	/**
	 * @return 單位類型
	 */
	public String getBranchType() {
		return branchType;
	}

	/**
	 * @param branchType
	 *            單位類型
	 */
	public void setBranchType(String branchType) {
		this.branchType = branchType;
	}

	/**
	 * @return 單位代碼
	 */
	public String getBranchId() {
		return branchId;
	}

	/**
	 * @param branchId
	 *            單位代碼
	 */
	public void setBranchId(String branchId) {
		this.branchId = branchId;
	}

	/**
	 * <pre>
	 * 行員代碼不足六碼前補0
	 * (行員代碼不為數值則不補0直接回傳)
	 * </pre>
	 * 
	 * @return 行員代碼
	 */
	public String getStaffNo() {
		String _staffNo = Util.nullToSpace(staffNo);
		try {
			_staffNo = String.format("%06d", Integer.valueOf(_staffNo));
			return _staffNo;
		} catch (Exception e) {
			return staffNo;
		}
	}

	/**
	 * @param staffNo
	 *            行員代碼
	 */
	public void setStaffNo(String staffNo) {
		this.staffNo = staffNo;
	}

	/**
	 * @return 人員職稱
	 */
	public String getStaffJob() {
		return staffJob;
	}

	/**
	 * @param staffJob
	 *            人員職稱
	 */
	public void setStaffJob(String staffJob) {
		this.staffJob = staffJob;
	}

	/**
	 * @return 行員姓名
	 */
	public String getStaffName() {
		return staffName;
	}

	/**
	 * @param staffName
	 *            行員姓名
	 */
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}

	/**
	 * @return 承作日期
	 */
	public String getCesDate() {
		return cesDate;
	}

	/**
	 * @param cesDate
	 *            承作日期
	 */
	public void setCesDate(String cesDate) {
		this.cesDate = cesDate;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();

		// Table 標準 開頭.......................................
		sb.append(Util.nullToSpace(this.getOid()))
				.append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getMainId())).append(
				TextDefine.FILE_DELIM);

		sb.append(Util.nullToSpace(this.getBranchType())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getBranchId())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getStaffNo())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getStaffJob())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getStaffName())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getCesDate())).append(
				TextDefine.FILE_DELIM);

		// Table 標準 節尾.......................................
		sb.append(Util.nullToSpace(this.getCreator())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getCreateTime())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getUpdater())).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getUpdateTime()));

		return sb.toString();
	}
}
