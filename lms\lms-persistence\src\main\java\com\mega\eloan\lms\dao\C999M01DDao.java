/* 
 * C999M01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C999M01D;

/** 個金約據書項目描述檔 **/
public interface C999M01DDao extends IGenericDao<C999M01D> {

	C999M01D findByOid(String oid);

	List<C999M01D> findByMainId(String mainId);

	C999M01D findByUniqueKey(String mainId, String itemType);

	List<C999M01D> findByIndex01(String mainId, String itemType);
}