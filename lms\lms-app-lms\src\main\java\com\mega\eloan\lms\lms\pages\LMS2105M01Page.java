/* 
 * LMS2105M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.panels.LMS2105S01Panel;
import com.mega.eloan.lms.lms.panels.LMS2105S02Panel;
import com.mega.eloan.lms.lms.panels.LMS2105S03Panel;
import com.mega.eloan.lms.lms.panels.LMS2105S04Panel;
import com.mega.eloan.lms.model.L210M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 修改資料特殊流程
 * </pre>
 * 
 * @since 2012/01/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/01/10,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms2105m01/{page}")
public class LMS2105M01Page extends AbstractEloanForm {

	@Autowired
	LMSService lmsService;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待覆核));

		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		Map<String, String> msgs = lmsService.getAllDervPeriod();
		renderJsI18NWithMsgName("dervPeriodCodeType", msgs);   
		renderJsI18N(LMS1405S02Panel.class, new String[] {
				"L140M01e.shareRateError2", "L140M01e.lmterror", "btn.number",
				"title.15", "L140M01a.message68", "L140M01a.message70",
				"btn.changesShareRate2", "L140M01a.message81" });
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}// ;

	// 頁籤
	public Panel getPanel(int index) {
		renderJsI18N(LMS2105M01Page.class);
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS2105S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS2105S02Panel(TAB_CTX, true);
			break;
		case 3:
			renderJsI18N(LMS2105M01Page.class);

			panel = new LMS2105S03Panel(TAB_CTX, true);
			break;
		case 4:
			panel = new LMS2105S04Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS2105S01Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L210M01A.class;

	}

}// ~
