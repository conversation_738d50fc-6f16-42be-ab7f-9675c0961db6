package com.mega.eloan.lms.cls.handler.form;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.gwclient.Brmp005O;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.lngeFlag;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1141M01Page;
import com.mega.eloan.lms.cls.panels.CLS1131S01Panel;
import com.mega.eloan.lms.cls.panels.CLSS02CPanel;
import com.mega.eloan.lms.cls.service.CLS1130Service;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01C;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01F;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01J;
import com.mega.eloan.lms.model.C120S01O;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.C120S01T;
import com.mega.eloan.lms.model.C120S01V;
import com.mega.eloan.lms.model.C120S01W;
import com.mega.eloan.lms.model.C120S01X;
import com.mega.eloan.lms.model.C120S01Z;
import com.mega.eloan.lms.model.C120S02B;
import com.mega.eloan.lms.model.C120S02C;
import com.mega.eloan.lms.model.C900M01J;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L120S18A;
import com.mega.eloan.lms.model.L120S19A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.JsonMapper;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/11/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/19,Fantasy,new
 *          <li>2013/07/04,Rex,清空備註欄位
 *          </ul>
 */
@Scope("request")
@Controller("cls1141formhandler")
public class CLS1141FormHandler extends AbstractFormHandler {

	private static final Logger logger = LoggerFactory
			.getLogger(CLS1141FormHandler.class);

	private static final DateFormat S_FORMAT = new SimpleDateFormat(
			UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);

	@Autowired
	@Qualifier("CLS1130Service")
	CLS1130Service service;

	@Resource
	CLS1131Service cls1131Service;

	@Resource
	CLS1141Service cls1141Service;

	@Resource
	CLS1151Service cls1151Service;

	@Resource
	CLS1220Service cls1220Service;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;
	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	CLSService clsService;

	@Resource
	MisGrpcmpService misGrpcmpService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	MisPTEAMAPPService misPTEAMAPPService;

	@Resource
	MisdbBASEService misBaseService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	ScoreService scoreService;

	@Resource
	SysParameterService sysparamService;

	@Resource
	UserInfoService userInfoService;

	Properties prop_LMSCommomPage = MessageBundleScriptCreator
			.getComponentResource(LMSCommomPage.class);
	Properties prop_CLSS02CPanel = MessageBundleScriptCreator
			.getComponentResource(CLSS02CPanel.class);
	Properties prop_CLS1131S01Panel = MessageBundleScriptCreator
			.getComponentResource(CLS1131S01Panel.class);

	/**
	 * 新增S-擔保品提供人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult addCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String ntCode = clsService.get0024_ntCode(custId, dupNo);
		String c120s01a_oid = "";

		C120M01A c120m01a = service.findModelByKey(C120M01A.class, mainId,
				custId, dupNo);
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainId);
		lmsService.setCaseNo(l120m01a);
		if (c120m01a == null) {
			c120m01a = new C120M01A();
			c120m01a.setMainId(mainId);
			c120m01a.setOwnBrId(MegaSSOSecurityContext.getUnitNo());
			c120m01a.setCustId(custId);
			c120m01a.setDupNo(dupNo);
			c120m01a.setTypCd(this.checkTypCd(custId));
			c120m01a.setCustName(custName);
			c120m01a.setKeyMan("N"); // 簽報書借款人排序時, order by keyMan desc,
										// custShowSeqNum, custId, dupNo
			c120m01a.setMarkModel(UtilConstants.L140S02AModelKind.免辦);
			c120m01a.setRmk(UtilConstants.lngeFlag.擔保品提供人);
			c120m01a.setCustPos(UtilConstants.lngeFlag.擔保品提供人);
			// ~~~~~~
			C120S01A c120s01a = new C120S01A();
			c120s01a.setMainId(c120m01a.getMainId());
			c120s01a.setCustId(c120m01a.getCustId());
			c120s01a.setDupNo(c120m01a.getDupNo());
			c120s01a.setNtCode(ntCode);
			c120s01a.setBusCode(clsService.get0024_busCode(custId, dupNo));
			// ~~~~~~
			C120S01J c120s01j = new C120S01J();
			c120s01j.setMainId(c120m01a.getMainId());
			c120s01j.setCustId(c120m01a.getCustId());
			c120s01j.setDupNo(c120m01a.getDupNo());
			if (true) {
				/*
				 * 比照 CLS1131ServiceImpl :: querySingleData
				 * ClsConstants.C101S01E.黑名單.equals(type)
				 */
				String eName = "";
				if (Util.isEmpty(eName)) {
					List<Map<String, Object>> list = misCustdataService
							.findCustDataCname(custId, dupNo);
					for (Map<String, Object> map : list) {
						eName = Util.trim(map.get("ENAME"));
					}
				}

				c120s01j.setEName(eName);
				if (Util.isNotEmpty(eName)) {
					List<String> blackResult = iCustomerService.findBlackList(
							user.getUnitNo(), eName, "");
					if (blackResult.size() > 0) {
						String blackListCode = blackResult
								.get(ICustomerService.BlackList_ReturnCode);
						c120s01j.setBlackRecQry(CapDate.getCurrentTimestamp()); // 黑名單查詢日期
						c120s01j.setAns1(blackListCode);
					}
				}
			}
			// ~~~~~~
			service.save(c120m01a, l120m01a, c120s01a, c120s01j);
			c120s01a_oid = Util.trim(c120s01a.getOid());
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(custId).append(" ").append(dupNo).append(" ")
					.append(custName);
			Map<String, String> map = new HashMap<String, String>();
			map.put("msg", sb.toString());

			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.資料已存在, map), getClass());
		}

		_inject_ntCode(result, c120s01a_oid, custId, dupNo, ntCode);
		return result;
	}// ;

	private void _inject_ntCode(CapAjaxFormResult result, String c120s01a_oid,
			String custId, String dupNo, String ntCode) {
		result.set("info", "[" + custId + "-" + dupNo + "]ntCode=" + ntCode);
		result.set("has_ntCode", Util.isNotEmpty(ntCode) ? "Y" : "N");
		result.set("c120s01a_oid", c120s01a_oid);
	}

	@DomainAuth(AuthType.Modify)
	public IResult updateNtCode(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String c120s01a_oid = Util.trim(params.getString("c120s01a_oid"));
		String ntCode = Util.trim(params.getString("ntCode"));
		if (Util.isNotEmpty(c120s01a_oid)) {
			C120S01A c120s01a = clsService.findC120S01A_oid(c120s01a_oid);
			if (c120s01a != null) {
				if (Util.isEmpty(ntCode)) {
					throw new CapMessageException("請輸入"
							+ prop_CLSS02CPanel.getProperty("C120S01A.ntCode"),
							getClass());
				} else {
					c120s01a.setNtCode(ntCode);
					// ====
					clsService.save(c120s01a);
				}
			}
		}

		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult queryC120S01A_CustPosS(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);
		Set<String> idDup11Set = new HashSet<String>();
		idDup11Set.add(idDup);

		Map<String, C120S01A> map = clsService.findIdDup_C120S01A(mainId,
				idDup11Set);
		if (map.containsKey(idDup) && map.get(idDup) != null) {
			C120S01A c120s01a = map.get(idDup);
			result.set("custId", c120s01a.getCustId());
			result.set("dupNo", c120s01a.getDupNo());
			String ntCode = Util.trim(c120s01a.getNtCode());
			String ntCode_sep = "";
			String ntCode_desc = "";
			Map<String, String> ntCode_descMap = clsService
					.get_codeTypeWithOrder("CountryCode");
			if (ntCode_descMap.containsKey(ntCode)) {
				ntCode_sep = "-";
				ntCode_desc = Util.trim(ntCode_descMap.get(ntCode));
			}
			result.set("ntCode", ntCode + ntCode_sep + ntCode_desc);
		}
		return result;
	}

	/**
	 * 判斷借款人區部別
	 * 
	 * @param custId
	 *            客戶統編
	 * @return DBU | OBU
	 */
	private String checkTypCd(String custId) {
		boolean isObuId = LMSUtil.isObuId(custId); // 是否為自然人
		String typCd = "";
		if (isObuId) {
			typCd = TypCdEnum.OBU.getCode();
		} else {
			typCd = TypCdEnum.DBU.getCode();
		}
		return typCd;
	}

	/**
	 * 新增團貸母戶借保人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult addParenetCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));

		if ("00000000".equals(custId) || "0000000000".equals(custId)) {
			// CLS.error02=團貸母戶ID不可為0000000或0000000000！！
			Map<String, String> map = new HashMap<String, String>();
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(CLS1141M01Page.class);
			map.put("msg", " " + prop.getProperty("CLS.error02"));
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, map), getClass());
		}
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));

		if (clsService.is_function_on_codetype("pteamapp_dupNo")) {
			List<PTEAMAPP> pteamapp_list = misPTEAMAPPService
					.getPTEAMAPPDataByBean(custId, dupNo);
			if (pteamapp_list.size() > 0) {
				// 之前已簽的舊案
			} else {
				// 新簽報的案件, 至少要有 custId-0 的組合存在
				List<Map<String, Object>> custdata_list = misCustdataService
						.findCustDataCname(custId, "0");
				if (custdata_list.size() == 0) {
					throw new CapMessageException("請先在0024建立 " + custId
							+ "-0 的基本資料，才可在 e-loan 系統新增團貸母戶", getClass());
				}
			}
		}
		String ntCode = clsService.get0024_ntCode(custId, dupNo);
		if (Util.isEmpty(ntCode)) {
			ntCode = clsService.get0024_ntCode(custId, "0"); // 若無, 抓0的國別
		}
		String c120s01a_oid = "";

		C120M01A c120m01a = service.findModelByKey(C120M01A.class, mainId,
				custId, dupNo);
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainId);
		C120M01A keyMan = cls1141Service.findC120M01AByMainIdAndKeyMan(mainId);

		lmsService.setCaseNo(l120m01a);
		if (c120m01a == null) {
			c120m01a = new C120M01A();
			c120m01a.setMainId(mainId);
			c120m01a.setOwnBrId(MegaSSOSecurityContext.getUnitNo());
			c120m01a.setCustId(custId);
			c120m01a.setDupNo(dupNo);
			c120m01a.setCustName(custName);
			c120m01a.setTypCd(this.checkTypCd(custId));
			String keyManMark = "";
			if (keyMan == null) {
				keyManMark = UtilConstants.DEFAULT.是;
				l120m01a.setCustId(custId);
				l120m01a.setDupNo(dupNo);
				l120m01a.setCustName(custName);
				l120m01a.setTypCd(this.checkTypCd(custId));
				result.add(this.setShowTitleCust(c120m01a));
			} else {
				keyManMark = UtilConstants.DEFAULT.否;
			}
			c120m01a.setKeyMan(keyManMark);
			c120m01a.setMarkModel(UtilConstants.L140S02AModelKind.免辦);
			// ~~~~~~

			C120S01A c120s01a = new C120S01A();
			c120s01a.setMainId(c120m01a.getMainId());
			c120s01a.setCustId(c120m01a.getCustId());
			c120s01a.setDupNo(c120m01a.getDupNo());
			c120s01a.setNtCode(ntCode);
			c120s01a.setBusCode(clsService.get0024_busCode(custId, dupNo));
			// ===========
			service.save(c120m01a, c120s01a, l120m01a);
			c120s01a_oid = Util.trim(c120s01a.getOid());
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(custId).append(" ").append(dupNo).append(" ")
					.append(custName);
			Map<String, String> map = new HashMap<String, String>();
			map.put("msg", sb.toString());

			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.資料已存在, map), getClass());
		}

		_inject_ntCode(result, c120s01a_oid, custId, dupNo, ntCode);
		return result;
	}// ;

	/**
	 * 刪除借保人資料
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes" })
	@DomainAuth(AuthType.Modify)
	public IResult deleteCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custOid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<L140M01A> l140m01as = cls1151Service
				.findL140m01aListByL120m01cMainId(mainId, null);
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainId);
		C120M01A c120m01a = this.clsService.getC120M01AByOid(custOid);
		Map<String, Object> docFileMap = this.cls1141Service
				.getDocFileForDeleteBorrower(c120m01a.getCustId(),
						c120m01a.getDupNo(), c120m01a.getOwnBrId());

		// 刪除原因
		String reason = Util.trim(params.getString("reason"));
		String reasonOth = Util.trim(params.getString("reasonOth"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		for (L140M01A l140m01a : l140m01as) {
			if (custId.equals(l140m01a.getCustId())
					&& dupNo.equals(l140m01a.getDupNo())) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(CLS1141M01Page.class);
				Map<String, String> param = new HashMap<String, String>();
				// CLS1141.109=該借款人已為額度明細表主借款人，不得執行此動作！！
				param.put("msg", prop.getProperty("CLS1141.109"));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
		}

		List<String> c120m01a_oids = new ArrayList<String>();
		c120m01a_oids.add(custOid);
		// 刪除借款人時也要同時 上傳DW
		clsService.upDwBydeleCust(l120m01a, c120m01a_oids, reason, reasonOth);

		List<GenericBean> list = new ArrayList<GenericBean>();
		for (Class clazz : LMSUtil.C120Class) {
			List<? extends GenericBean> beans = service.findListByRelationKey(
					clazz, mainId, custId, dupNo);
			for (GenericBean model : beans) {
				list.add(model);
			}
			// ========
			// 簡化簽報
			for (GenericBean model : service.findListByRelationKey(
					C120S01T.class, mainId, custId, dupNo)) {
				list.add(model);
			}
		}
		//檢查雙軌資料
		for (Class clazz : LMSUtil.C101_NClass) {
			List<? extends GenericBean> beans = service.findListByRelationKey(
					clazz, mainId, custId, dupNo);
			for (GenericBean model : beans) {
				list.add(model);
			}
		}
		
		// 當刪除的借款人為目前簽報書上顯示借款人 要清空title
		if (custId.equals(l120m01a.getCustId())
				&& dupNo.equals(l120m01a.getDupNo())) {
			l120m01a.setCustId("");
			l120m01a.setCustName("");
			l120m01a.setDupNo("");
			l120m01a.setTypCd("");
			result.set("cleanTitle", true);
		}
		service.save(l120m01a);
		// 刪除 C120 相關bean
		service.delete(list);

		// 刪除 L140S01A,更改L140M01A,L140S02A的值
		{
			String[] custIdArr = new String[1];
			String[] dupNoArr = new String[1];
			custIdArr[0] = custId;
			dupNoArr[0] = dupNo;
			cls1151Service.saveWhenDelCust(l140m01as, custIdArr, dupNoArr);
		}

		// 刪除 C120S04W, DocFile

		if (docFileMap != null) {
			this.lmsService.deleteDocFileForDeleteBorrower(mainId,
					String.valueOf(docFileMap.get("SRCFILENAME")));
		}

		this.eloandbBASEService.deleteC120S04WByMainIdAndDataCustomerNo(mainId,
				custId);

		return result;
	}// ;

	/**
	 * 讀取借保人資料（CLS1131FormHandler也要一併改）
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes" })
	@DomainAuth(AuthType.Query)
	public IResult loadCust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		Class[] clazzs = { C120M01A.class, C120S01A.class, C120S01B.class,
				C120S01C.class, C120S01D.class, C120S01E.class, C120S01F.class,
				C120S01X.class, C120S01Z.class, C120S02C.class };
		for (Class clazz : clazzs) {
			GenericBean model = service.findModelByKey(clazz, mainId, custId,
					dupNo);
			if (model != null) {
				if (clazz == C120M01A.class
						&& ("Y".equals(((C120M01A) model).getModelTyp()))
						&& Util.isEmpty(((C120M01A) model).getMarkModel())
						&& (((C120M01A) model).getC120s01g() != null)) {
					// 既有的 C120M01A, 有 markModel=免辦的case, EX:擔保品提供人
					// 所以要加判斷 markModel=empty, 才呈現 1-房貸模型
					// 在非房貸模型上線前, 只有房貸模型
					model.set("markModel", "1");
				}

				// 畫面採用C101的部份,所以把120取代為101
				String className = Util.trim(clazz.getSimpleName()).replace(
						"120", "101");
				CapAjaxFormResult formResult = DataParse.toResult(model);
				// ==============================================
				// CLS1131FormHandler vs CLS1141FormHandler
				// 個金簽報書 是 C120S01x 系列
				if (model instanceof C120S01B) {
					formResult.set("juTotalCapital", LMSUtil
							.convert_bigvalue(((C120S01B) model)
									.getJuTotalCapital()));
					formResult.set("juPaidUpCapital", LMSUtil
							.convert_bigvalue(((C120S01B) model)
									.getJuPaidUpCapital()));
					C120S01B c120s01b = (C120S01B) model;
					if (c120s01b.getPtaDataDt() == null) {
						formResult.set("ptaDataDt", "");
					}

					Map<String, String> _CM1_JOB_BUSINESS_CODE_map = codeTypeService
							.findByCodeType("CM1_JOB_BUSINESS_CODE");
					Map<String, String> _CM1_TITLE_CODE_map = codeTypeService
							.findByCodeType("CM1_TITLE_CODE");
					formResult.set("cm1_job_business_InfoStr", ClsUtil
							.getC120S01B_cm1_job_business_InfoStr(c120s01b,
									_CM1_JOB_BUSINESS_CODE_map));
					formResult.set("cm1_job_title_InfoStr", ClsUtil
							.getC120S01B_cm1_job_title_InfoStr(c120s01b,
									_CM1_TITLE_CODE_map));
					if (c120s01b.getCm1_dataDt() == null) {
						formResult.set("cm1_dataDt", "");
					}
					if (true) {
						formResult.set("snrY", ClsUtility
								.get_inject_snrY(c120s01b.getSeniority()));
					}
				}
				if (Util.equals("C101S01E", className)) {
					LMSUtil.setL120M01M(formResult,
							cls1131Service.findL120s01m(mainId, custId, dupNo));

					ClsUtil.setC101S01E_wm_data(formResult,
							DataParse.toJSON(model));

					C120S01J c120s01j = clsService.findC120S01J(mainId, custId,
							dupNo);
					String amlRefNo = "";
					String amlRefOid = "";
					if (c120s01j != null) {
						amlRefNo = Util.trim(c120s01j.getAmlRefNo());
						amlRefOid = Util.trim(c120s01j.getAmlRefOid());
					}
					ClsUtil.set_msg_Laa(formResult, clsService.msg_Laa_html(
							c120s01j, prop_LMSCommomPage));
					String msg_agentPIdCmp = "";
					if (c120s01j != null
							&& Util.isNotEmpty(Util.trim(c120s01j
									.getAgentPIdCmp()))) {
						C900M01J c900m01j = clsService
								.findC900M01J_mainId(c120s01j.getAgentPIdCmp());
						String c101s01e_agentPId = Util
								.trim(c900m01j == null ? "" : c900m01j
										.getCustId());
						msg_agentPIdCmp = ClsUtil.msg_agentPIdCmp(
								c101s01e_agentPId, c900m01j,
								clsService.get_C900M01J_output_memo(c900m01j),
								prop_CLS1131S01Panel);
					}
					ClsUtil.set_msg_agentPIdCmp(formResult, msg_agentPIdCmp);
					// for amlDiv
					L120S09B l120s09b = clsService.findL120S09B_refNo_or_oid(
							amlRefNo, amlRefOid);
					L120S09A l120s09a = clsService
							.findL120S09A_cls1131(l120s09b);
					Map<String, String> map_ncResult = clsService
							.get_codeTypeWithOrder("SAS_NC_Result");
					ClsUtil.set_msg_L120S09B(formResult, l120s09b, l120s09a,
							map_ncResult);

					// 加上大數據風險資料
					C120S02B c120s02b = cls1131Service.findModelByKey(
							C120S02B.class, mainId, custId, dupNo);
					if (c120s02b != null) {
						CapAjaxFormResult c120s02bData = DataParse
								.toResult(c120s02b);
						formResult.putAll(c120s02bData);
					}
				}

				if (model instanceof C120S01Z) {
					if (Util.isNotEmpty(model.get("kycUpdater"))) {
						formResult.set("kycUpdaterName", Util
								.trim(userInfoService.getUserName(model.get(
										"kycUpdater").toString())));
					}
					if (Util.isNotEmpty(model.get("kycUpdateTime"))) {
						formResult.set("kycUpdateTime",
								S_FORMAT.format(model.get("kycUpdateTime")));
					}
					if (Util.isNotEmpty(model.get("kycApprover"))) {
						formResult.set("kycApproverName", Util
								.trim(userInfoService.getUserName(model.get(
										"kycApprover").toString())));
					}
					if (Util.isNotEmpty(model.get("kycApprTime"))) {
						formResult.set("kycApprTime",
								S_FORMAT.format(model.get("kycApprTime")));
					}
				}

				result.set(className + "Form", formResult);
			}
		}

		// 取得評等資訊
		JSONObject C101M01AForm = (JSONObject) result.get("C101M01AForm");
		if (C101M01AForm != null) {
			C120S01A model_s01a = service.findModelByKey(C120S01A.class,
					mainId, custId, dupNo);
			C120S01G model_g = service.findModelByKey(C120S01G.class, mainId,
					custId, dupNo);
			C120S01Q model_q = service.findModelByKey(C120S01Q.class, mainId,
					custId, dupNo);
			C120S01R model_r = service.findModelByKey(C120S01R.class, mainId,
					custId, dupNo);

			L120M01I l120m01i = clsService.findL120M01I_mainId(mainId);
			String bailout_flag = OverSeaUtil
					.build_l120m01i_bailout_flag(l120m01i);
			// ========================
			String ntCode = "";
			if (model_s01a != null) {
				ntCode = Util.trim(model_s01a.getNtCode());
			}
			ClsUtil.set_ntCode(C101M01AForm, ntCode);
			// ========================
			if (model_g != null) {
				String checkItemRange = clsService.getCheckItemRange(model_g);
				C101M01AForm.putAll(ClsUtil.procMarkModel_G(
						LMSUtil.copy_to_C101S01G(model_g), bailout_flag,
						checkItemRange));
				C101M01AForm.putAll(ClsUtil.procMarkModel_0(
						LMSUtil.copy_to_C101S01G(model_g), bailout_flag,
						checkItemRange));
			}
			// ========================
			Date date_jcicFlg_V_NN = null;
			if (model_q != null) {
				String checkItemRange = clsService.getCheckItemRange(model_q);
				C101M01AForm.putAll(ClsUtil.procMarkModel_Q(
						LMSUtil.copy_to_C101S01Q(model_q), bailout_flag,
						checkItemRange));
				date_jcicFlg_V_NN = model_q.getJcicQDate();
			}
			ClsUtil.set_date_jcicFlg_V_NN(C101M01AForm, date_jcicFlg_V_NN);

			if (model_r != null) {
				String checkItemRange = clsService.getCheckItemRange(model_r);
				C101M01AForm.putAll(ClsUtil.procMarkModel_R(
						LMSUtil.copy_to_C101S01R(model_r), bailout_flag,
						checkItemRange));
			} else {
				String c101s01r_varVer = "";
				if (model_q != null) { // 目前的專案信貸(非團體), 是[非房貸]+[遮罩]去產出, 在簽報書,
										// 優先帶出[非房貸]模型版本
					c101s01r_varVer = Util.trim(model_q.getVarVer());
				}
				if (Util.isEmpty(c101s01r_varVer)) {
					c101s01r_varVer = scoreService.get_Version_CardLoan();
				}
				C101M01AForm.putAll(ClsUtil
						.procMarkModel_R_default(c101s01r_varVer));
			}
			
			//增加雙軌處理
			boolean showSDT_G = false;
			boolean showSDT_Q = false;
			boolean showSDT_R = false;
			boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
			if(scoreDoubleTrack){ //雙軌處理
				C101S01G_N model_GN = cls1131Service.findModelByKey(C101S01G_N.class,
						mainId, custId, dupNo);
				C101S01Q_N model_QN = cls1131Service.findModelByKey(C101S01Q_N.class,
						mainId, custId, dupNo);
				C101S01R_N model_RN = cls1131Service.findModelByKey(C101S01R_N.class,
						mainId, custId, dupNo);
				
				if (model_GN != null) {
					C101M01AForm.putAll(ClsUtil.procMarkModel_G_N(model_GN));
					showSDT_G = true;
				}
				if (model_QN != null) {
					C101M01AForm.putAll(ClsUtil.procMarkModel_Q_N(model_QN));
					showSDT_Q = true;
				}
				if (model_RN != null) {
					C101M01AForm.putAll(ClsUtil.procMarkModel_R_N(model_RN));
					showSDT_R = true;
				}
			}
			result.set("showSDT_G", showSDT_G);
			result.set("showSDT_Q", showSDT_Q);
			result.set("showSDT_R", showSDT_R);
			
			

			boolean naturalFlag = LMSUtil.check2(custId); // 是否為自然人
			C101M01AForm.put("naturalFlag",
					naturalFlag ? UtilConstants.DEFAULT.是
							: UtilConstants.DEFAULT.否); // 是否為自然人

			// J-108-0143_10702_B1001 新增新往來客戶註記並於額度明細表新做加註
			JSONObject C101S01AForm = (JSONObject) result.get("C101S01AForm");
			if (C101S01AForm != null
					&& C101S01AForm.toString().contains("newCustFlag")) {
				C101M01AForm.put("newCustFlag",
						C101S01AForm.getString("newCustFlag"));
			} else {
				C101M01AForm.put("newCustFlag", "");
			}
		}

		if (true) {
			String active_SAS_AML = "0";
			L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
			if (clsService.active_SAS_AML(l120m01a)) {
				active_SAS_AML = "1";
				// 比照 CLS1201S20Panel 的參數定義{1:SAS_AML且未掃描/已結束,
				// 2:SAS_AML且掃描中（LOCKEDIT）}
			}
			result.set("active_SAS_AML", active_SAS_AML);
		}

		// J-108-0277 介接系統資料查詢-資料建檔記錄查詢
		// List<Map<String, Object>> mixPdfRecordList =
		// cls1141Service.getDataArchivalRecordData(mainId, custId, dupNo);
		// result.set("dataArchivalRecordData",
		// JSONArray.fromObject(mixPdfRecordList));

		// J-109-0178_10702_B1004 Web e-Loan 簽報書借款人顯示申請資料核對表
		CapAjaxFormResult c120s01vForm = new CapAjaxFormResult();
		List<C120S01V> c120s01v_list = clsService.findC120S01VByMainid(mainId,
				custId, dupNo);
		if (c120s01v_list.size() > 0) {
			for (C120S01V c120s01v : c120s01v_list) {
				c120s01vForm.set(c120s01v.getItems_Name(),
						c120s01v.getItems_Value());
			}
		}
		result.set("C101S01VForm", c120s01vForm);
		//J-113-0199 行員自動過件
		if (clsService.is_function_on_codetype("autoCheckFlag")) {
			result.set("autoCheck_showPage", clsService.is_function_on_codetype("autoCheck_showPage"));
			result.set("autoCheck_bankManFlag", misBaseService.isBankMan_on_the_job(custId) || clsService.is_function_on_codetype("autoCheck_bankManFlag"));

			L120S19A l120s19a_latestOutput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_autoCheck_output);
			JSONObject outJs = new JSONObject();
			L120S19A l120s19aOutJs = null;
			ObjectMapper objectMapper = new ObjectMapper();
			if (Util.isNotEmpty(l120s19a_latestOutput)) {
				outJs = JSONObject.fromObject(l120s19a_latestOutput.getJsonData());
				l120s19aOutJs = l120s19a_latestOutput;
				Brmp005O brmp005o_obj = new Brmp005O();
				try {
					brmp005o_obj = objectMapper.readValue(outJs.toString(), Brmp005O.class);
				} catch (IOException e) {
					e.printStackTrace();
				}
				CapAjaxFormResult rtn_json = new CapAjaxFormResult(cls1131Service.getAutoCheck(brmp005o_obj));
				result.set("rtn_json", rtn_json);
				//案件狀態
				C120S02C c120s02c = cls1131Service.findC120S02C(mainId);
				if (c120s02c!=null) {
					String docstatus = Util.trim(c120s02c.getDocStatus());
					if (Util.isNotEmpty(docstatus)) {
						Map<String, String> _DocStatusNewDescMap = cls1220Service.get_DocStatusNewDescMap();
						rtn_json.set("autoCheckResultDesc", _DocStatusNewDescMap.get(docstatus));
						result.set("rtn_json",rtn_json);
					}
				}
			}
		}

		return result;
	}

	/**
	 * 讀取評等資料
	 * <ul>
	 * <li>個金簽報書 > 借保人 > 開啟等級評分表
	 * </ul>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult loadScore(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C120S01G model_g = null;
		C120S01Q model_q = null;
		C120S01R model_r = null;
		String markModel = Util.trim(params.getString("markModel",
				UtilConstants.L140S02AModelKind.房貸));

		if (Util.equals(markModel, UtilConstants.L140S02AModelKind.房貸)) {
			model_g = service.findModelByKey(C120S01G.class, mainId, custId,
					dupNo);
		} else if (Util.equals(markModel, UtilConstants.L140S02AModelKind.非房貸)) {
			model_q = service.findModelByKey(C120S01Q.class, mainId, custId,
					dupNo);
		} else if (Util.equals(markModel, UtilConstants.L140S02AModelKind.卡友貸)) {
			model_r = service.findModelByKey(C120S01R.class, mainId, custId,
					dupNo);
		}

		if (model_g != null) {
			CapAjaxFormResult formResult = cls1131Service.loadScore_G(LMSUtil
					.copy_to_C101S01G(model_g));
			// J-111-0271 消金房貸3.0,計算[與最高分這差距]、[影響性]
			String varVer = Util.trim(model_g.getVarVer());
			if (Util.equals(varVer, ClsScoreUtil.V3_0_HOUSE_LOAN)) {
				// 與最高分差距
				// Step1. 定義9個因子分數欄位
				String[] scoreArr = ClsScoreUtil.scoreArr_G_3_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_G_3_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_G_3_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_G_3_0;
				// Step2、Step3、Step4放到共用function執行
				cls1131Service.newScoreModel_01(formResult, scoreArr, HighGapArr,
						InfluenceArr,HighScoreArr);
			}
			if(scoreDoubleTrack){ //雙軌資料
				cls1131Service.loadScoreSDT(formResult, markModel, mainId, custId, dupNo);
			}
			result.set("C101S01GForm", formResult);
		}

		if (model_q != null) {
			C101S01Q c101s01q = LMSUtil.copy_to_C101S01Q(model_q);
			CapAjaxFormResult formResult = cls1131Service.loadScore_Q(c101s01q);
			ClsUtil.set_Q_chkItem(formResult, c101s01q);
			// J-111-0373消金非房貸4.0,計算[與最高分這差距]、[影響性]
			String varVer = Util.trim(model_q.getVarVer());
			if (Util.equals(varVer, ClsScoreUtil.V4_0_NOT_HOUSE_LOAN)) { // 非房貸4.0
				// 與最高分差距
				// Step1. 定義5個因子分數欄位
				String[] scoreArr = ClsScoreUtil.scoreArr_Q_4_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_Q_4_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_Q_4_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_Q_4_0;
				// Step2、Step3、Step4放到共用function執行
				cls1131Service.newScoreModel_01(formResult, scoreArr, HighGapArr,
						InfluenceArr,HighScoreArr);
			}
			if(scoreDoubleTrack){ //雙軌資料
				cls1131Service.loadScoreSDT(formResult, markModel, mainId, custId, dupNo);
			}
			result.set("C101S01QForm", formResult);
		}
		if (model_r != null) {
			L120M01I l120m01i = clsService.findL120M01I_mainId(mainId);
			String bailout_flag = OverSeaUtil
					.build_l120m01i_bailout_flag(l120m01i);
			// ~~~~~~
			C101S01R c101s01r = LMSUtil.copy_to_C101S01R(model_r);

			CapAjaxFormResult formResult = cls1131Service.loadScore_R(c101s01r);
			// 供「總處單位」檢視「評分表內容」 => [1]針對J10分數加工, [2]初始評等在不同子頁籤出現
			String checkItemRange = clsService.getCheckItemRange(model_r);
			formResult.putAll(ClsUtil.procMarkModel_R(model_r, bailout_flag,
					checkItemRange));
			ClsUtil.set_R_chkItem(formResult, c101s01r);
			String varVer = Util.trim(model_r.getVarVer());
			if (Util.equals(varVer, ClsScoreUtil.V4_0_CARD_LOAN)) { // 非房貸4.0
				// 與最高分差距
				// Step1. 定義5個因子分數欄位
				String[] scoreArr = ClsScoreUtil.scoreArr_R_4_0;
				String[] HighGapArr = ClsScoreUtil.HighGapArr_R_4_0;
				String[] InfluenceArr = ClsScoreUtil.InfluenceArr_R_4_0;
				String[] HighScoreArr = ClsScoreUtil.HighScoreArr_R_4_0;
				// Step2、Step3、Step4放到共用function執行
				cls1131Service.newScoreModel_01(formResult, scoreArr, HighGapArr,
						InfluenceArr,HighScoreArr);
			}
			if(scoreDoubleTrack){ //雙軌資料
				cls1131Service.loadScoreSDT(formResult, markModel, mainId, custId, dupNo);
			}
			result.set("C101S01RForm", formResult);
		}
		return result;
	}// ;

	@DomainAuth(AuthType.Query)
	public IResult loadAdjust(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String markModel = Util.trim(params.getString("markModel"));

		if (Util.equals(UtilConstants.L140S02AModelKind.房貸, markModel)) {
			C120S01G c120s01g = service.findModelByKey(C120S01G.class, mainId,
					custId, dupNo);

			// C120M01A c120m01a = service.findModelByKey(C120M01A.class,
			// mainId,
			// custId, dupNo);

			if (c120s01g != null) {
				result.set("adjustForm", DataParse.toResult(c120s01g));
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸, markModel)) {

			C120S01Q c120s01q = service.findModelByKey(C120S01Q.class, mainId,
					custId, dupNo);
			if (c120s01q != null) {
				result.set("adjustNotHouseLoanForm",
						DataParse.toResult(c120s01q));
			}
		} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸, markModel)) {

			C120S01R c120s01r = service.findModelByKey(C120S01R.class, mainId,
					custId, dupNo);
			if (c120s01r != null) {
				result.set("adjustCardLoanForm", DataParse.toResult(c120s01r));
			}
		}
		result.set("fromC120M01A", "Y");
		return result;
	}

	/**
	 * 引進借保人
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult importLendCollateral(PageParameters params)
			throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String[] rows = params.getStringArray("rows");
		HashSet<String> bfRejCaseSet = new HashSet<String>();
		List<String> c900m01MsgList = new ArrayList<String>();

		cls1141Service.importLendCollateral(mainId, rows, user.getUnitNo());

		List<C101M01A> c101m01as = cls1141Service.findC101M01AByMaindIds(rows);
		for (C101M01A c101m01a : c101m01as) {
			if (UtilConstants.Casedoc.rejtCase.警示不控管.equals(c101m01a
					.getBfRejCase())) {
				bfRejCaseSet.add(c101m01a.getCustId() + " "
						+ c101m01a.getDupNo() + " " + c101m01a.getCustName());
			}
			C101S01E c101s01e = c101m01a.getC101s01e();
			if (c101s01e != null) {
				if (UtilConstants.haveNo.有.equals(c101s01e.getIsQdata18())) {
					C101S01J c101s01j = service.findModelByKey(C101S01J.class,
							c101m01a.getMainId(), c101m01a.getCustId(),
							c101m01a.getDupNo());
					if (c101s01j != null) {
						c900m01MsgList.add(ClsUtil.getC900M01EMsg(
								Util.trim(c101m01a.getCustName()), c101s01j));
					}
				}
			}
		}
		if (true) {
			// 是否需要指定 主要借款人
			C120M01A c120m01a = cls1141Service
					.findC120M01AByMainIdAndKeyMan(mainId);
			if (c120m01a == null) {
				result.set("needMainCust", true);
			}
		}

		if (CollectionUtils.isNotEmpty(bfRejCaseSet)) {
			// CLS1141.106=目前屬婉卻警示戶。
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(CLS1141M01Page.class);

			String bfRejCaseMsg = StringUtils.join(bfRejCaseSet, "、")
					+ prop.getProperty("CLS1141.106");

			result.set("bfRejCaseMsg", Util.trim(bfRejCaseMsg));
		}
		if (CollectionUtils.isNotEmpty(c900m01MsgList)) {
			// 提示 偽造證件或財力證明 的人員
			result.set("c900m01Msg",
					StringUtils.join(c900m01MsgList, "<br/><br/>"));
		}

		return result;
	}

	/**
	 * 查詢借款人
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult queryAllCust(PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<C120M01A> c120m01as = (List<C120M01A>) service.findListByMainId(
				C120M01A.class, mainId);
		HashMap<String, String> custMap = new HashMap<String, String>();
		for (C120M01A c120m01a : c120m01as) {
			if (UtilConstants.lngeFlag.擔保品提供人.equals(c120m01a.getCustPos())) {
				continue;
			}
			custMap.put(c120m01a.getOid(), c120m01a.getCustId() + " "
					+ c120m01a.getDupNo() + " " + c120m01a.getCustName());
		}
		return new CapAjaxFormResult(custMap);
	}

	/**
	 * 設定主要借款人
	 * 
	 * @param params
	 *            <pre>
	 * custOid: c120m01a Oid
	 * </pre>
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult setMainCust(PageParameters params) throws CapException {
		String custOid = Util.trim(params.getString("custOid"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainId);
		C120M01A c120m01a = null;
		if (Util.isNotEmpty(custOid)) {
			// 有 c120m01a_oid 則以傳入參數為主
			c120m01a = service.findModelByOid(C120M01A.class, custOid);
		} else {
			List<C120M01A> c120m01a_list = clsService
					.findC120M01A_mainId_orderBy_keymanCustposCustid(mainId);
			if (c120m01a_list.size() == 1) {
				c120m01a = c120m01a_list.get(0);
			} else {
				logger.error("setMainCust{mainId=" + mainId + ", custOid="
						+ custOid + "}");
				throw new CapMessageException("no custOid", getClass());
			}
		}
		CapBeanUtil.copyBean(c120m01a, l120m01a, new String[] { "custId",
				"dupNo", "typCd", "custName" });
		c120m01a.setKeyMan(UtilConstants.DEFAULT.是);
		cls1141Service.save(l120m01a);
		CapAjaxFormResult result = this.setShowTitleCust(c120m01a);

		if (true) {
			CapAjaxFormResult chose_c120m01a = new CapAjaxFormResult();
			chose_c120m01a.set("c120m01a_oid", c120m01a.getOid());
			result.set("chose_c120m01a", chose_c120m01a);
		}
		return result;
	}

	/**
	 * 設定上方顯示借款人資訊
	 * 
	 * @param c120m01a
	 * @return
	 */
	private CapAjaxFormResult setShowTitleCust(C120M01A c120m01a) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("showTypCd", this.getMessage("typCd." + c120m01a.getTypCd()));
		result.set("showCustId", StrUtils.concat(c120m01a.getCustId()
				.toUpperCase(), " ", c120m01a.getDupNo().toUpperCase(), " ",
				c120m01a.getCustName()));
		result.set("mainCustId", Util.trim(c120m01a.getCustId()).toUpperCase());
		result.set("mainDupNo", Util.trim(c120m01a.getDupNo()).toUpperCase());
		return result;
	}

	/**
	 * 設定主要借款人
	 * 
	 * @param params
	 *            <pre>
	 * custOid: c120m01a Oid
	 * </pre>
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult setKeyMan(PageParameters params) throws CapException {
		String custOid = Util.trim(params.getString("custOid"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainId);

		List<C120M01A> c120m01as = (List<C120M01A>) service.findListByMainId(
				C120M01A.class, mainId);
		CapAjaxFormResult result = new CapAjaxFormResult();
		for (C120M01A c120m01a : c120m01as) {
			if (c120m01a.getOid().equals(custOid)) {
				if (lngeFlag.擔保品提供人.equals(c120m01a.getCustPos())) {
					// CLS1141.096=擔保品提供人不可設為主要借款人
					Properties prop = MessageBundleScriptCreator
							.getComponentResource(CLS1141M01Page.class);
					Map<String, String> param = new HashMap<String, String>();
					param.put("msg", prop.getProperty("CLS1141.096"));
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, param),
							getClass());
				}
				CapBeanUtil.copyBean(c120m01a, l120m01a, new String[] {
						"custId", "dupNo", "typCd", "custName" });
				c120m01a.setKeyMan(UtilConstants.DEFAULT.是);

				result.add(this.setShowTitleCust(c120m01a));
				// 印出儲存成功訊息!
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
				c120m01a.setKeyMan(UtilConstants.DEFAULT.是);
			} else {
				c120m01a.setKeyMan(UtilConstants.DEFAULT.否);
			}
		}
		cls1141Service.saveC120M01As(c120m01as, l120m01a);
		return result;
	}

	/**
	 * 讀取關聯戶資料表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Query)
	public IResult loadRelation(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<C120S01O> list = (List<C120S01O>) service.findListByRelationKey(
				C120S01O.class, mainId, custId, dupNo);
		String jsonStr = JsonMapper.toJSON(list);
		result.set("detials", jsonStr);

		for (C120S01O model : list) {
			JSONObject json = DataParse.toJSON(model);
			result.set("queryDate", json.getString("queryDate"));
		}
		return result;
	}// ;

	/**
	 * 修改婉卻記錄
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult editReject(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		StringBuilder rejectMemo = new StringBuilder();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String editReject = Util.trim(params.getString("editReject"));
		C120M01A c120m01a = cls1141Service.findModelByOid(C120M01A.class, oid);
		if (c120m01a != null) {
			rejectMemo
					.append(user.getUserCName())
					.append("(")
					.append(CapDate.getDateTimeFormat(CapDate
							.getCurrentTimestamp())).append(")");
			c120m01a.setRejectCase(editReject);
			c120m01a.setRejectMemo(rejectMemo.toString());
			cls1141Service.save(c120m01a);
		}
		result.set("rejectMemo", rejectMemo.toString());
		return result;
	}// ;

	/**
	 * 載入custShowSeqNum
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult load_custShowSeqNum(PageParameters params)
			throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		List<C120M01A> c120m01a_list = cls1141Service
				.findC120M01AByMainIdForOrder(mainId);

		HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
		{
			JSONArray jsonArray = new JSONArray();
			for (C120M01A model : c120m01a_list) {

				JSONObject o = new JSONObject();
				o.put("oid", model.getOid());
				o.put("custShowSeqNum", Util.trim(model.getCustShowSeqNum()));
				o.put("custId", Util.trim(model.getCustId()));
				o.put("dupNo", Util.trim(model.getDupNo()));
				o.put("custName", Util.trim(model.getCustName()));

				jsonArray.add(o);
			}

			map.put("arr", jsonArray);
		}
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("c120m01a_list", new CapAjaxFormResult(map));
		result.set("c120m01a_cnt", c120m01a_list.size());
		return result;
	}

	/**
	 * 寫入custShowSeqNum
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult save_custShowSeqNum(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String[] oidArr = params.getStringArray("c120cust_oid");
		String[] uiSeqArr = params.getStringArray("c120cust_uiSeq");
		int oid_size = (oidArr == null ? 0 : oidArr.length);
		int uiSeq_size = (uiSeqArr == null ? 0 : uiSeqArr.length);
		if (oid_size == uiSeq_size) {

			for (int i = 0; i < oid_size; i++) {
				C120M01A c120m01a = clsService.findC120M01A_oid(oidArr[i]);
				c120m01a.setCustShowSeqNum(Util.parseInt(uiSeqArr[i]));
				clsService.save(c120m01a);
			}
		}
		return result;
	}

	/**
	 * 查詢是否有評等被引用
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Query)
	public IResult queryDelectCase(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean havaGrade = false;
		Set<String> cntrNoSet = eloandbBASEService
				.queryL140S02AGradCustByL120M01AMainId(mainId);
		if (Util.isEmpty(custId)) {
			// 整份簽報書刪除
			havaGrade = !cntrNoSet.isEmpty();
		} else {
			// 單一借款人刪除
			havaGrade = cntrNoSet.contains(custId + dupNo);
		}
		result.set("havaGrade", havaGrade);
		return result;
	}

	@DomainAuth(AuthType.Query)
	public IResult getAbnormalDocParam(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		result.set("findDoc", "N");
		C120M01A model = clsService.findC120M01A_mainId_idDup(mainId, custId,
				dupNo);
		if (model != null) {
			L120M01A l120m01a = clsService.findL120M01A_mainId(model
					.getAbnormalMainId());
			if (l120m01a != null) {
				result.set("findDoc", "Y");
				result.set("open_docURL", Util.trim(l120m01a.getDocURL()));
				if (true) {
					cls1131Service.injectAbnormalDocParam(result, l120m01a,
							"url_data");
				}
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeDetail(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C120S01B c120s01b = cls1131Service.findModelByKey(C120S01B.class,
				mainId, custId, dupNo);

		if (c120s01b != null) {
			result.add(new CapAjaxFormResult(c120s01b.toJSONObject(
					cls1131Service.getPersonalIncomeDetailColumns(), null)));
		}
		return result;
	}

	/**
	 * 取得個人收入明細版本
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeVersion(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		Boolean isC120M01A = params.getBoolean("isC120M01A");

		int systemVersion = Util.parseInt(sysparamService
				.getParamValue("C101S01B_incomeDetailVer"));
		C120S01B c120s01b = cls1131Service.findModelByKey(C120S01B.class,
				mainId, custId, dupNo);

		if (isC120M01A) {
			result.set("incomeVersion",
					c120s01b.getIncomeDetailVer() == null ? 0 : c120s01b
							.getIncomeDetailVer().intValue());
			result.set("needReset", false);
		} else {
			boolean needReset = false;

			BigDecimal incomeDetailVersion = c120s01b.getIncomeDetailVer();
			int cVersion = incomeDetailVersion == null ? 0
					: incomeDetailVersion.intValue();
			if (cVersion == systemVersion) {

			} else {
				needReset = true;
			}
			result.set("incomeVersion", systemVersion);
			result.set("needReset", needReset);

		}
		return result;
	}

	/**
	 * 取得新版個人收入明細資料，使用column-table的方式儲存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeDetailWithVersion(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		List<C120S01W> c120s01ws = clsService.findC120S01W(mainId, custId,
				dupNo);
		if (CollectionUtils.isNotEmpty(c120s01ws)) {
			for (C120S01W w : c120s01ws) {
				String keyString = Util.trim(w.getKeyString());
				String valueString = Util.trim(w.getValueString());
				result.set(keyString, valueString);
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeByFormula(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String sOid = Util.trim(params.getString("sOid"));

		C120S01W c120s01w = cls1131Service.findModelByOid(C120S01W.class, sOid);

		String[] arrs = new String[] { "value01", "value02", "value03",
				"value04", "value05", "value06", "value07", "value08",
				"value09", "value10", "value11", "value12", "bonus",
				"inProfit", "disRate", "holding", "valueYear" };

		String formulaType = c120s01w.getFormulaType();
		JSONObject jsonObject = DataParse.toJSON(c120s01w);
		for (String arr : arrs) {
			jsonObject.put("formula_" + formulaType + "_" + arr,
					jsonObject.opt(arr));
		}

		result.set("c101s01w", new CapAjaxFormResult(jsonObject));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeFormula(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String incomeItem = Util.trim(params.getString("incomeItem"));
		String actuallyIncomeSum = Util.trim(params.getString("actuallyIncomeSum"));

		CodeType c101s01w_incomeItem = codeTypeService.findByCodeTypeAndCodeValue("c101s01w_incomeItem", incomeItem+actuallyIncomeSum, "zh_TW");

		if (c101s01w_incomeItem != null) {
			String formula = Util.trim(c101s01w_incomeItem.getCodeDesc2());
			result.set("formula",formula);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getPersonalIncomeDetailWithVersionExt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		C120S01B c120s01b = cls1131Service.findModelByKey(C120S01B.class,
				mainId, custId, dupNo);
		if (c120s01b != null) {
			result.set("positionType", Util.trim(c120s01b.getPositionType()));
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importRelatedEconomic(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<L120S18A> l120s18a_list = clsService
				.findL120S18A_byMainId_orderBySeq(mainId);
		if (l120s18a_list.size() > 0) {
			clsService.delL120S18A(mainId, l120s18a_list);
		}

		clsService.cls_importRelatedEconomic(mainId);

		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult updateCreditCondition(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String remark = Util.trim(params.getString("remark"));
		String type = Util.trim(params.getString("type"));
		String content = Util.trim(params.getString("content"));
		try {
			this.cls1141Service.createCreditConditionLog(mainId, content, type,
					remark);
		} catch (Exception e) {
			// J-111-0343_05097_B1004 Web e-Loan修改企金額度明細表合計之功能
			throw new CapMessageException(e.toString(), getClass());
		}
		if (UtilConstants.PaperlessActingType.FEE.equals(type)) {
			Map<String, String> m = this.cls1141Service
					.formatDisplayDisplayStringForL120S19C(mainId,
							this.clsService
									.getActingRoleForPaperlessSigning(mainId));
			content = m.get("CONTENT");
			remark = m.get("REMARK");
		} else {
			content = this.cls1141Service.formatContentString(type, "TWD",
					content);
		}

		result.set("content", content);
		result.set("remark", remark);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult mappingClsJob(PageParameters params) throws CapException {
		//get value from req
		String clsJobType1Val = Util.trim(params.getString("clsJobType1"));
		String clsJobType2Val = Util.trim(params.getString("clsJobType2"));
		String clsJobTitleVal = Util.trim(params.getString("clsJobTitle"));
		String capital = Util.trim(params.getString("capital"));
		boolean isNPO = params.getBoolean("isNPO");
	
		return cls1131Service.mappingClsJob(clsJobType1Val, clsJobType2Val, clsJobTitleVal, capital,isNPO);

	}
	
	/**
	 * 確定引入借保人之評等因子、分數皆有正確計算
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult checkClsRating(PageParameters params)
			throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] rows = params.getStringArray("rows");
		List<C101M01A> c101m01as = cls1141Service.findC101M01AByMaindIds(rows);
		List<String> errorCust = new ArrayList<String>();
		//為安全起見還是還是做一個可以關閉檢核的機制
		if(clsService.is_function_on_codetype("J-112-0239")){
			//可能引入多個借款人
			for (C101M01A c101m01a : c101m01as) {
				boolean noChange_G = false;
				boolean noChange_Q = false;
				boolean noChange_R = false;
				C101S01A c101s01a = clsService.findC101S01A(c101m01a);
				C101S01B c101s01b = clsService.findC101S01B(c101m01a);
				C101S01C c101s01c = clsService.findC101S01C(c101m01a);
				C101S01G c101s01g = c101m01a.getC101s01g();
				C101S01Q c101s01q = c101m01a.getC101s01q();
				C101S01R c101s01r = c101m01a.getC101s01r();
				
				//檢查房貸評等(G)
				if(c101s01g != null){
					noChange_G = unChg_C101S01G_factor_vs_C101S01X(c101s01a, c101s01b, c101s01c, c101s01g);	
				}
				//檢查非房貸評等(Q)
				if(c101s01q != null){
					noChange_Q = unChg_C101S01Q_factor_vs_C101S01X(c101s01a, c101s01b, c101s01c, c101s01q);
				}
				//檢查專案信貸(非團體)評等(R)
				if(c101s01r != null){
					noChange_R = unChg_C101S01R_factor_vs_C101S01X(c101s01a, c101s01b, c101s01c, c101s01r);
				}
				
				if(noChange_G || noChange_Q || noChange_R){
					//有參數與檢核不同
					errorCust.add(Util.trim(c101m01a.getCustId())+" "+Util.trim(c101m01a.getDupNo()+" "+Util.trim(c101m01a.getCustName())));
				}
			}
		}
		StringBuffer changeMsg = new StringBuffer();
		if(errorCust != null && errorCust.size()>0){
			for(int i=0;i<errorCust.size();i++){
				changeMsg.append(errorCust.get(i));
				changeMsg.append("<br/>");
			}
			changeMsg.append(prop_CLSS02CPanel.getProperty("markModel.changeMessage"));
			result.set("notSame", true);
			result.set("changeMsg", changeMsg.toString());
		}
		return result;
	}
	
	private boolean unChg_C101S01G_factor_vs_C101S01X(C101S01A c101s01a, C101S01B c101s01b, C101S01C c101s01c, C101S01G c101s01g){
		String var_G = c101s01g.getVarVer();
		if(Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, var_G) || Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, var_G)){
			//夫妻年收入
			if(diff_parseBigDecimal_val(c101s01g.getYFamAmt(), c101s01c.getYFamAmt())){
				return true;
			}
			//年資
			if(diff_parseBigDecimal_val(c101s01g.getSeniority(), c101s01b.getSeniority())){
				return true;
			}
			//個人年所得
			BigDecimal payamt = CrsUtil.parseBigDecimal(c101s01b.getPayAmt());
			BigDecimal othamt = CrsUtil.parseBigDecimal(c101s01b.getOthAmt());
			BigDecimal pIncome_from_C101S01B = payamt.add(othamt);
			if(diff_parseBigDecimal_val(c101s01g.getPIncome(), pIncome_from_C101S01B)){
				return true;
			}
			//職稱
			if(Util.notEquals(c101s01g.getJobTitle(), c101s01b.getJobTitle())){
				return true;
			}
		}else if(Util.equals(ClsScoreUtil.V3_0_HOUSE_LOAN, var_G)){
			
		}
		return false;
	}
	private boolean unChg_C101S01Q_factor_vs_C101S01X(C101S01A c101s01a, C101S01B c101s01b, C101S01C c101s01c, C101S01Q c101s01q){
		String var_Q = c101s01q.getVarVer();
		if(Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, var_Q) || Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, var_Q)){
			//個人年所得
			BigDecimal payamt = CrsUtil.parseBigDecimal(c101s01b.getPayAmt());
			BigDecimal othamt = CrsUtil.parseBigDecimal(c101s01b.getOthAmt());
			BigDecimal pIncome_from_C101S01B = payamt.add(othamt);
			if(diff_parseBigDecimal_val(c101s01q.getPIncome(), pIncome_from_C101S01B)){
				return true;
			}
			//職稱
			if(Util.notEquals(c101s01q.getJobTitle(), c101s01b.getJobTitle())){
				return true;
			}
			//個人負債比
			if(diff_parseBigDecimal_val(c101s01q.getNochkItemDrate(), c101s01c.getDRate())){
				return true;
			}
		}else if(Util.equals(ClsScoreUtil.V4_0_NOT_HOUSE_LOAN, var_Q)){
			
		}
		return false;
	}
	private boolean unChg_C101S01R_factor_vs_C101S01X(C101S01A c101s01a, C101S01B c101s01b, C101S01C c101s01c, C101S01R c101s01r){
		String var_R = c101s01r.getVarVer();
		if(Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, var_R) || Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, var_R)){
			//個人年所得
			BigDecimal payamt = CrsUtil.parseBigDecimal(c101s01b.getPayAmt());
			BigDecimal othamt = CrsUtil.parseBigDecimal(c101s01b.getOthAmt());
			BigDecimal pIncome_from_C101S01B = payamt.add(othamt);
			if(diff_parseBigDecimal_val(c101s01r.getPIncome(), pIncome_from_C101S01B)){
				return true;
			}
			//職稱
			if(Util.notEquals(c101s01r.getJobTitle(), c101s01b.getJobTitle())){
				return true;
			}
			//個人負債比
			if(diff_parseBigDecimal_val(c101s01r.getNochkItemDrate(), c101s01c.getDRate())){
				return true;
			}
		}else if(Util.equals(ClsScoreUtil.V4_0_CARD_LOAN, var_R)){
			
		}
		return false;
	}
	
	private boolean diff_parseBigDecimal_val(BigDecimal value_GQR ,BigDecimal value_Detial) {
		boolean nullJsonValA = false;
		if (value_GQR == null) {
			nullJsonValA = true;
		} else {
			nullJsonValA = false;
		}
		// ===============================================
		boolean is_diff = true;
		if (value_Detial == null) {
			if (nullJsonValA) {
				is_diff = false; // 兩者都是 null ---> diff為false
			} else {
				is_diff = true;
			}
		} else {
			if (nullJsonValA) {
				is_diff = true;
			} else {
				// 兩者都非null
				is_diff = (value_GQR.compareTo(value_Detial) != 0);
			}
		}
		return is_diff;
	}

}
