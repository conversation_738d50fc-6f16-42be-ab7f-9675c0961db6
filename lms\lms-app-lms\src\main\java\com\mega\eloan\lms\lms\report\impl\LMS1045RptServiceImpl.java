package com.mega.eloan.lms.lms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.dao.C123M01ADao;
import com.mega.eloan.lms.lms.report.LMS1045RptService;
import com.mega.eloan.lms.model.C123M01A;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

@Service("lms1045rptservice")
public class LMS1045RptServiceImpl extends AbstractIISIReportService implements FileDownloadService, LMS1045RptService {
	
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1045RptServiceImpl.class);
	@Resource
	CLSService clsService;
	
	@Resource
	C123M01ADao c123m01aDao;
	
	@Resource
	BranchService branchService;
	
	@Override
	public String getReportDefinition() {
		return "report/lms/LMS1045R00";
	}

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {
		
		String oid = params.getString("mainOid");

		C123M01A c123m01a = null;
		c123m01a = c123m01aDao.findByOid(oid);

		if (c123m01a != null) {

			try {
				// 若用 JSONObject.fromObject(...)，遇到有關連的 entity 時，會出錯
				reportData.setAll(DataParse.toJSON(c123m01a));

				Map<String, Object> m = new HashMap<String, Object>();

				IBranch branch = branchService.getBranch(c123m01a.getOwnBrId());
				m.put("BRANCHNAME", OverSeaUtil.getBrNameByLocale(branch, LMSUtil.getLocale()));
				m.put("prtDate", CapDate.getCurrentDate("MM-yyyy"));
				
				int TotalScore = Integer.parseInt(Util.trim(c123m01a.getBeaconScore()))+Integer.parseInt(Util.trim(c123m01a.getDebtRatio()))+
								Integer.parseInt(Util.trim(c123m01a.getLtvRatio()))+Integer.parseInt(Util.trim(c123m01a.getEmployYear()))+
								Integer.parseInt(Util.trim(c123m01a.getIncome()))+Integer.parseInt(Util.trim(c123m01a.getPropertyType()))+
								Integer.parseInt(Util.trim(c123m01a.getAssessment()));
				
				String RiskRating = "";
				if(TotalScore < 50){
					RiskRating = "10";
				} else if(TotalScore >= 50 && TotalScore <= 54){
					RiskRating = "9";
				} else if(TotalScore >= 55 && TotalScore <= 59){
					RiskRating = "8";
				} else if(TotalScore >= 60 && TotalScore <= 64){
					RiskRating = "7";
				} else if(TotalScore >= 65 && TotalScore <= 69){
					RiskRating = "6";
				} else if(TotalScore >= 70 && TotalScore <= 74){
					RiskRating = "5";
				} else if(TotalScore >= 75 && TotalScore <= 79){
					RiskRating = "4";
				} else if(TotalScore >= 80 && TotalScore <= 84){
					RiskRating = "3";
				} else if(TotalScore >= 85 && TotalScore <= 89){
					RiskRating = "2";
				} else if(TotalScore >= 90){
					RiskRating = "1";
				}
				
				String EvaluationResults = "";
				if(Util.equals(RiskRating, "10")){
					EvaluationResults = "Loss";
				} else if(Util.equals(RiskRating, "9")){
					EvaluationResults = "Doubtful";
				} else if(Util.equals(RiskRating, "8") || Util.equals(RiskRating, "7")){
					EvaluationResults = "Substandard";
				} else if(Util.equals(RiskRating, "6") || Util.equals(RiskRating, "5")){
					EvaluationResults = "Especially Mentioned";
				} else {
					EvaluationResults = "Satisfactory";
				}
				
				String FrequencyOfCreditReview = "";
				if(Util.equals(EvaluationResults, "Loss")){
					FrequencyOfCreditReview = "--";
				} else if(Util.equals(EvaluationResults, "Doubtful") || Util.equals(EvaluationResults, "Substandard")){
					FrequencyOfCreditReview = "Quarterly";
				} else if(Util.equals(EvaluationResults, "Especially Mentioned")){
					FrequencyOfCreditReview = "Semi-Annually";
				} else if(Util.equals(EvaluationResults, "Satisfactory")){
					FrequencyOfCreditReview = "Annually";
				}
				
				m.put("TotalScore", TotalScore);
				m.put("RiskRating", "Grade "+RiskRating);
				m.put("EvaluationResults", EvaluationResults);
				m.put("FrequencyOfCreditReview", FrequencyOfCreditReview);
				
				reportData.setAll(m);

			} catch (Exception e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}
		}
		return reportData;
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) super.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}
}
