package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;

import com.mega.eloan.lms.base.enums.RateTypeEnum;

/**
 * <pre>
 * 利率資料
 * </pre>
 * 
 * @since 2012/5/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/5/9,<PERSON>,new
 *          </ul>
 */
public class Rate {

	/** 幣別 */
	private String curr = null;
	/** 匯率 */
	private BigDecimal rate = null;
	/** 類別 */
	private RateTypeEnum type = null;

	/**
	 * 匯率建構子
	 * 
	 * @param curr
	 *            幣別
	 * @param rate
	 *            匯率
	 * @param type
	 *            類別
	 */
	public Rate(String curr, BigDecimal rate, RateTypeEnum type) {
		this.curr = curr;
		this.rate = rate;
		this.type = type;
	}

	/**
	 * 取得幣別
	 * 
	 * @return
	 */
	public String getCurr() {
		return curr;
	}

	/**
	 * 設定幣別
	 * 
	 * @param curr
	 *            幣別
	 */
	public void setCurr(String curr) {
		this.curr = curr;
	}

	/**
	 * 取得匯率
	 * 
	 * @return
	 */
	public BigDecimal getRate() {
		return rate;
	}

	/**
	 * 設定匯率
	 * 
	 * @param rate
	 *            匯率
	 */
	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	/**
	 * 取得計算類別
	 * 
	 * @return RateTypeEnum 計算類別
	 */
	public RateTypeEnum getType() {
		return type;
	}

	/**
	 * 設定計算類別
	 * 
	 * @param type
	 *            RateTypeEnum 計算類別
	 */
	public void setType(RateTypeEnum type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return this.getCurr() + "," + this.getRate().toString() + ","
				+ this.getType().getCode();
	}
}
