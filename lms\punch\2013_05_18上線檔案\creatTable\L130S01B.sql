---------------------------------------------------------
-- LMS.L130S01B 異常通報表參貸行檔
---------------------------------------------------------
--DROP TABLE LMS.L130S01B;
CREATE TABLE LMS.L130S01B (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	BRANCHIDS     CHAR(3)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L130S01B PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL130S01B01;
--CREATE UNIQUE INDEX LMS.XL130S01B01 ON LMS.L130S01B   (OID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L130S01B IS '異常通報表參貸行檔';
COMMENT ON LMS.L130S01B (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	BRANCHIDS     IS '參貸行代碼', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
