/* 
 * L185M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L185M01A;


/** 覆審管理報表檔 **/
public interface L185M01ADao extends IGenericDao<L185M01A> {

	L185M01A findByOid(String oid);

	L185M01A findByMainId(String mainId);

	List<L185M01A> findByIndex01(Date dataDate, Date dataEDate, String branchId, int i);

	List<L185M01A> findByCustIdDupId(String custId,String DupNo);
}