/* 
 * LPDFA01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授信 PDF 舊案授權檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="LPDFA01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class LPDFA01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	@SuppressWarnings("unused")
	@ToStringExclude
	@OneToMany(mappedBy="lpdfa01a", fetch = FetchType.LAZY)
	private Set<LPDFM01A> lpdfm01a;
	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 記錄主文件串連資料的UNID
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * pid<p/>
	 * ''
	 */
	@Size(max=32)
	@Column(name="PID", length=32, columnDefinition="CHAR(32)")
	private String pid;

	/** 
	 * 授權單位<p/>
	 * 918授管處<br/>
	 *  93x營運中心<br/>
	 *  xxx分行
	 */
	@Size(max=3)
	@Column(name="OWNUNIT", length=3, columnDefinition="CHAR(3)")
	private String ownUnit;

	/** 
	 * 授權人員<p/>
	 * ''
	 */
	@Size(max=6)
	@Column(name="OWNER", length=6, columnDefinition="CHAR(6)")
	private String owner;

	/** 
	 * 授權日期<p/>
	 * current timestamp
	 */
	@Column(name="AUTHTIME", columnDefinition="TIMESTAMP")
	private Timestamp authTime;

	/** 
	 * 授權類別<p/>
	 * 4.傳送(授權)
	 */
	@Size(max=1)
	@Column(name="AUTHTYPE", length=1, columnDefinition="CHAR(1)")
	private String authType;

	/** 
	 * 被授權單位<p/>
	 * 918授管處<br/>
	 *  93x營運中心<br/>
	 *  xxx分行
	 */
	@Size(max=3)
	@Column(name="AUTHUNIT", length=3, columnDefinition="CHAR(3)")
	private String authUnit;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 記錄主文件串連資料的UNID
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  記錄主文件串連資料的UNID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得pid<p/>
	 * ''
	 */
	public String getPid() {
		return this.pid;
	}
	/**
	 *  設定pid<p/>
	 *  ''
	 **/
	public void setPid(String value) {
		this.pid = value;
	}

	/** 
	 * 取得授權單位<p/>
	 * 918授管處<br/>
	 *  93x營運中心<br/>
	 *  xxx分行
	 */
	public String getOwnUnit() {
		return this.ownUnit;
	}
	/**
	 *  設定授權單位<p/>
	 *  918授管處<br/>
	 *  93x營運中心<br/>
	 *  xxx分行
	 **/
	public void setOwnUnit(String value) {
		this.ownUnit = value;
	}

	/** 
	 * 取得授權人員<p/>
	 * ''
	 */
	public String getOwner() {
		return this.owner;
	}
	/**
	 *  設定授權人員<p/>
	 *  ''
	 **/
	public void setOwner(String value) {
		this.owner = value;
	}

	/** 
	 * 取得授權日期<p/>
	 * current timestamp
	 */
	public Timestamp getAuthTime() {
		return this.authTime;
	}
	/**
	 *  設定授權日期<p/>
	 *  current timestamp
	 **/
	public void setAuthTime(Timestamp value) {
		this.authTime = value;
	}

	/** 
	 * 取得授權類別<p/>
	 * 4.傳送(授權)
	 */
	public String getAuthType() {
		return this.authType;
	}
	/**
	 *  設定授權類別<p/>
	 *  4.傳送(授權)
	 **/
	public void setAuthType(String value) {
		this.authType = value;
	}

	/** 
	 * 取得被授權單位<p/>
	 * 918授管處<br/>
	 *  93x營運中心<br/>
	 *  xxx分行
	 */
	public String getAuthUnit() {
		return this.authUnit;
	}
	/**
	 *  設定被授權單位<p/>
	 *  918授管處<br/>
	 *  93x營運中心<br/>
	 *  xxx分行
	 **/
	public void setAuthUnit(String value) {
		this.authUnit = value;
	}
}
