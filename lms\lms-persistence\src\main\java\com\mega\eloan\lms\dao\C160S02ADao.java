package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S02A;

/** 動審額度明細表聯徵查詢結果 **/
public interface C160S02ADao extends IGenericDao<C160S02A> {

	C160S02A findByOid(String oid);

    List<C160S02A> findByList(String mainId, String custId, String dupNo);

    C160S02A findByItem(String mainId, String custId, String dupNo, String item);

	List<C160S02A> findByCustIdDupId(String custId, String DupNo);

	List<C160S02A> findByMainId(String mainId);

	public int deleteByOid(String oid);
}