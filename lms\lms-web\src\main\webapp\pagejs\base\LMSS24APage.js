
var initS24aJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){

		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	}
	
};

/**
 * 引進國家風險限額說明
 * J-109-0369_05097_B1001 e-Loan企金授權外簽報書引進國家風險限額資料
 */
function applyCountryRiskLimit(){
	$.ajax({
		handler : initS24aJson.handlerName,
		type : "POST",
		dataType : "json",
		data : {
			formAction : "applyCountryRiskLimit",
			mainId : responseJSON.mainid
		},
		success : function(json) {
			if(json.showMsg != "" ){
				CommonAPI.showMessage(json.showMsg);
			}
			$("#LMS1205S24Form01").find("#itemDscrS").val(json.dscr);
		}
	});
}

function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    // 海外_編製中("01O"),       海外_待補件("07O"),       會簽後修改編製中("01K")
    if (auth.readOnly || (responseJSON.mainDocStatus != "01O" && responseJSON.mainDocStatus != "07O" && responseJSON.mainDocStatus != "01K")) {
        return true;
    }
    return false;
}

$(document).ready(function() {
	initS24aJson.setHandler();

    if (checkReadonly()) {
        $("form").lockDoc();
        var $LMS1205S24Form01 = $("#LMS1205S24Form01");
        $LMS1205S24Form01.readOnlyChilds(true);
        $LMS1205S24Form01.find("button").hide();
    }

	//J-106-0XXX-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
//	$("input[name='isNoFactCountry']" ).change(function(){
//		var isNoFactCountry= $( "input[name='isNoFactCountry']:radio:checked" ). val();
//		if(isNoFactCountry=="Y"){ 
//			$(".showNoFactCountry").show();
//			$(".badCountryContent").show();
//		}else{
//			$(".showNoFactCountry").hide();
//			$("#noFactCountry").val('');
//			$("#noFactCountryShow").val('');
//			
//			var isFreezeFactCountry= $( "input[name='isFreezeFactCountry']:radio:checked" ). val();
//			if(isFreezeFactCountry!="Y"){
//				$(".badCountryContent").hide();
//			}
//		}
//	}).trigger("change");
//	
//	$("input[name='isFreezeFactCountry']" ).change(function(){
//		var isFreezeFactCountry= $( "input[name='isFreezeFactCountry']:radio:checked" ). val();
//		if(isFreezeFactCountry=="Y"){ 
//			$(".showFreezeFactCountry").show();
//			$(".badCountryContent").show();
//		}else{
//			$(".showFreezeFactCountry").hide();
//			$("#freezeFactCountry").val('');
//			$("#freezeFactCountryShow").val('');
//			
//			var isNoFactCountry= $( "input[name='isNoFactCountry']:radio:checked" ). val();
//			if(isNoFactCountry!="Y"){
//				$(".badCountryContent").hide();
//			}
//		}
//	}).trigger("change");
//	
//	
//	$("#selectNoFactCountryBt").click(function(){	
//		//J-106-0XXX-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
//		selectCountryBt(1);	
//	});
//
//	$("#selectFreezeFactCountryBt").click(function(){	
//		//J-106-0XXX-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
//		selectCountryBt(2);		
//	});
//	
//	
//	//J-106-0XXX-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
//	$("input[name='isNoFactCountry']").trigger('change');
//	$("input[name='isFreezeFactCountry']").trigger('change'); 
	
 
});





