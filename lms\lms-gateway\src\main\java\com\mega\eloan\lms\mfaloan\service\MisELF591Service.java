package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;


/**
 * <pre>
 * 國內消金、現金存入警示戶明細檔 
 * </pre>
 * 
 * @since 2020/12/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/12/01,EL08034,new
 *          </ul>
 */
public interface MisELF591Service {
	public List<Map<String, Object>> sel_info_in_ELF591H(String idDup, String elf591h_beg_date);
	
	public List<Map<String, Object>> sel_never_retrialData_in_ELF591H(String brNo, String idDup);
	
	public List<Map<String, Object>> sel_not_in_ELF491_list(String brNo, String assign_crDate);
}
