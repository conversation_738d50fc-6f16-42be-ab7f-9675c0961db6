/* 
 * C120M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 個金徵信借款人主檔
* 
* C101M01A 有 2 個 UniqueConstraint
*     U【+MAINID】
*     U【+OWNBRID+CUSTID+DUPNO】
*     
* 而 C120M01A 的 UniqueConstraint
*     U【+MAINID+OWNBRID+CUSTID+DUPNO 】
*     
* → 因004,083轉移案件，所以轉檔上來的 Notes 舊資料，同一 mainId 會有不同的ownBrId
* 
*     
* 來源
* 	● 簽報書       L120M01A
*  	● 海外(日本/澳洲)模型  C121M01A
*  	● 線上增貸  C122M01A
*  
<pre>
※在海外簽報書的 grid
  select * from lms.c120m01a where caseId=? //列出全部的 Rating Doc
   
input  ─ 簽報書 引入 海外模型
● select * from lms.c120m01a where                                  caseId='responseJSON.mainId' and ratingKind='1' 
      甲	評等001號 	mainId='c121m01a_mainId_001' 	caseId='l120m01a_mainId' 	ratingKind='1' 
      乙	評等001號 	mainId='c121m01a_mainId_001' 	caseId='l120m01a_mainId' 	ratingKind='1'
  ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─      
      乙	評等002號 	mainId='c121m01a_mainId_002' 	caseId='l120m01a_mainId' 	ratingKind='1'

input  ─ 簽報書 新增
● select * from lms.c120m01a where mainId='responseJSON.mainId' and caseId=mainId                and ratingKind='2' 
      丙	        	mainId='l120m01a_mainId'       	caseId='l120m01a_mainId' 	ratingKind='2' 

output ─ 合併
● select * from lms.c120m01a where mainId='responseJSON.mainId' and caseId=''                    and ratingKind='3' 
      甲	        	mainId='l120m01a_mainId' 	    caseId='' 	                ratingKind='3' 
      乙	        	mainId='l120m01a_mainId' 	    caseId='' 	                ratingKind='3' 
</pre>
*/
@NamedEntityGraph(name = "C120M01A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("c121m01c"),
		@NamedAttributeNode("c120s01g"),
		@NamedAttributeNode("c120s01q"),
		@NamedAttributeNode("c120s01r"),
		@NamedAttributeNode("c121m01b"),
		@NamedAttributeNode("c121m01f"),
		@NamedAttributeNode("c121m01g"),
		@NamedAttributeNode("c121m01d"),
		@NamedAttributeNode("c121m01h") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C120M01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "ownBrId", "custId", "dupNo" }))
public class C120M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 編製單位代號
	 * <p/>
	 * 單位代碼
	 */
	@Size(max = 3)
	@Column(name = "OWNBRID", length = 3, columnDefinition = "CHAR(3)")
	private String ownBrId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Size(max = 1)
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	@Size(max = 60)
	@Column(name = "CUSTNO", length = 60, columnDefinition = "VARCHAR(60)")
	private String custNo;
	
	/**
	 * 職工編號
	 * <p/>
	 * 非必要輸入
	 */
	@Size(max = 20)
	@Column(name = "STAFFNO", length = 20, columnDefinition = "VARCHAR(20)")
	private String staffNo;

	/** 負責事業體統一編號 **/
	@Size(max = 10)
	@Column(name = "CMPID", length = 10, columnDefinition = "VARCHAR(10)")
	private String cmpId;

	/** 負責事業體名稱 **/
	@Size(max = 120)
	@Column(name = "CMPNM", length = 120, columnDefinition = "VARCHAR(120)")
	private String cmpNm;

	/**
	 * 前婉卻分行
	 * <p/>
	 * C101S01E.isQdata1<br/>
	 * (MIS.LNUNID.REGBR)
	 */
	@Size(max = 3)
	@Column(name = "BFREJBRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String bfRejBranch;

	/**
	 * 前婉卻原因
	 * <p/>
	 * C101S01E.isQdata1<br/>
	 * (MIS.LNUNID.REFUSEDS)<br/>
	 * 128個全型字
	 */
	@Size(max = 384)
	@Column(name = "BFREJREASON", length = 384, columnDefinition = "VARCHAR(384)")
	private String bfRejReason;

	/**
	 * 前婉卻日期
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.REGDT)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "BFREJDATE", columnDefinition = "DATE")
	private Date bfRejDate;

	/**
	 * 前婉卻狀態
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * 婉卻狀態：1(控管);2(警示);D(刪除)
	 */
	@Size(max = 1)
	@Column(name = "BFREJCASE", length = 1, columnDefinition = "CHAR(1)")
	private String bfRejCase;

	/**
	 * 現婉卻狀態
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * 婉卻狀態：1(控管);2(警示);D(刪除)
	 */
	@Size(max = 1)
	@Column(name = "REJECTCASE", length = 1, columnDefinition = "CHAR(1)")
	private String rejectCase;

	/**
	 * 現婉卻記錄
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * (組字)128個全型字<br/>
	 * ※ 1(控管)：本案主要借款人 ○○○ 曾於 YYYY-MM-DD HH:MM:SS.sssss 在 999 ＸＸ分行
	 * 有過婉卻紀錄，婉卻原因：ＸＸＸＸＸ...，現婉卻狀態仍屬控管層級，如欲承做需報授權外核准。<br/>
	 * ※ 2(警示)：本案主要借款人 ○○○ 曾於 YYYY-MM-DD HH:MM:SS.sssss 在 999 ＸＸ分行
	 * 有過婉卻紀錄，婉卻原因：ＸＸＸＸＸ...，現婉卻狀態已屬警示層級，如欲承做可於授權內承做。
	 */
	@Size(max = 384)
	@Column(name = "REJECTMEMO", length = 384, columnDefinition = "VARCHAR(384)")
	private String rejectMemo;

	/**
	 * 評等類型
	 * <p/>
	 * 0.免辦 1.房貸個人評等模型 2.非房貸個人評等模型
	 */
	@Size(max = 5)
	@Column(name = "MARKMODEL", length = 5, columnDefinition = "CHAR(5)")
	private String markModel;

	/**
	 * 是否為自然人
	 * <p/>
	 * Y.是 N.否
	 */
	@Size(max = 1)
	@Column(name = "NATURALFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String naturalFlag;

	/**
	 * 是否可引進
	 * <p/>
	 * Y.可 N.不可
	 */
	@Size(max = 1)
	@Column(name = "IMPORTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String importFlag;

	/** 備註 **/
	@Size(max = 30)
	@Column(name = "RMK", length = 30, columnDefinition = "VARCHAR(30)")
	private String rmk;

	/**
	 * 主要借款人註記
	 * <p/>
	 * ※2013-01-29新增欄位<br/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "KEYMAN", length = 1, columnDefinition = "CHAR(1)")
	private String keyMan;

	/**
	 * 相關身份
	 * <p/>
	 * ※2013-01-29新增欄位<br/>
	 * C.共同借款人<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 */
	@Size(max = 1)
	@Column(name = "CUSTPOS", length = 1, columnDefinition = "CHAR(1)")
	private String custPos;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 非房貸模型上線前舊案(若為 Y 時，表示只有 房貸模型，非房貸模型還不能用)
	 */
	@Size(max = 1)
	@Column(name = "MODELTYP", length = 1, columnDefinition = "CHAR(1)")
	private String modelTyp;
	
	/** 第1碼krm040，第2碼bam095，第3碼KRS008(值 和C101S01G.cardFlag 未持有信用卡 相反) **/
	@Column(name = "JCICFLG", length = 3, columnDefinition = "VARCHAR(3)")
	private String jcicFlg;
	
	/** 行員編號 **/
	@Column(name = "MEGAEMPNO", length = 6, columnDefinition = "VARCHAR(6)")
	private String megaEmpNo;

	/** (海外)當地識別ID **/
	@Column(name = "O_LOCALCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String o_localCustId;

	/** (海外)與主要借款人關係 **/
	@Column(name = "O_CUSTRLT", length = 2, columnDefinition = "VARCHAR(2)")
	private String o_custRlt;

	/** 輸入資料檢誤完成(Y/N) **/
	@Column(name = "O_CHKYN", length = 1, columnDefinition = "VARCHAR(1)")
	private String o_chkYN;
	
	/** 海外基本資料 **/
	@Column(name = "O_SINGLE", length = 1, columnDefinition = "VARCHAR(1)")
	private String o_single;
	
	/** 串連ID **/
	@Column(name = "CASEID", length = 32, columnDefinition = "VARCHAR(32)")
	private String caseId;
	
	/** 評等文件 **/
	@Size(max=62)
	@Column(name="RATINGDESC", length=62, columnDefinition="VARCHAR(62)")
	private String ratingDesc;
	
	/** 評等種類 1.從評等copy;2:簽報書新增;3:由評等合併 **/
	@Size(max=1)
	@Column(name="RATINGKIND", length=1, columnDefinition="VARCHAR(1)")
	private String ratingKind;
	
	/** 附加檔案｛日本模型放jcicFile, 澳洲模型放vedaFile｝ **/
	@Column(name = "ATTCHFILEOID", length = 32, columnDefinition = "VARCHAR(32)")
	private String attchFileOid;
	
	/** 附加檔案2｛日本模型放 主觀更新>開示文件｝ **/
	@Column(name = "ATTCHFILEOID2", length = 32, columnDefinition = "VARCHAR(32)")
	private String attchFileOid2;
	
	/** 顯示順序 **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "CUSTSHOWSEQNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer custShowSeqNum;
	
	/** 查詢異常通報紀錄日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ABNORMALREADDATE", columnDefinition = "DATE")
	private Date abnormalReadDate;
	
	/** 通報分行 **/
	@Size(max = 3)
	@Column(name = "ABNORMALBRNO", length = 3, columnDefinition = "VARCHAR(3)")
	private String abnormalBrNo;
	
	/** 通報日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ABNORMALDATE", columnDefinition = "DATE")
	private Date abnormalDate;
	
	/** 目前異常通報狀態{Y:已解除,N:未解除} **/
	@Size(max = 1)
	@Column(name = "ABNORMALSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String abnormalStatus;

	/** 異常通報文件id **/
	@Size(max = 32)
	@Column(name = "ABNORMALMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String abnormalMainId;	

	/** 信用卡正卡張數(不含停用) **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "PRIMARY_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal primary_card;
	
	/** 信用卡附卡張數(不含停用) **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "ADDITIONAL_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal additional_card;
	
	/** 信用卡商務/採購卡張數(不含停用) **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "BUSINESS_OR_P_CARD", columnDefinition = "DEC(3,0)")
	private BigDecimal business_or_p_card;
	
	/** 借款人提供之資料與聯徵中心或與本行內部留存資料不相符 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISIMPORTDATAMATCH", length = 1, columnDefinition = "CHAR(1)")
	private String isImportDataMatch;
	
	/** 借款人或保證人提供申請資料及證明文件過於完整 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISFULLAPPLYDOCUMENT", length = 1, columnDefinition = "CHAR(1)")
	private String isFullApplyDocument;
	
	/** 照會過程難以直接聯繫借款人本人，需由第三人居中聯繫 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISNOTEBORROWER", length = 1, columnDefinition = "CHAR(1)")
	private String isNoteBorrower;
	
	/** 借款人提供經變造之申貸文件 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISCHECKORIGINALDOCUMENT", length = 1, columnDefinition = "CHAR(1)")
	private String isCheckOriginalDocument;
	
	/** 符合信貸減碼 */
	@Column(name = "CREDITLOANREDUCTFG", length = 1, columnDefinition = "CHAR(1)")
	private String creditLoanReductFg;
	
	/** 本行房貸戶 */
	@Column(name = "HASHOUSELOAN", length = 1, columnDefinition = "CHAR(1)")
	private String hasHouseLoan;

	/** 近1年AUM(單位:萬) */
	@Column(name = "WM_12M", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal wm_12m;

	/** 本行信用卡最早持卡日 */
	@Temporal(TemporalType.DATE)
	@Column(name = "HOLDMEGACARDDT", columnDefinition = "DATE")
	private Date holdMegaCardDt;
	
	/** 申貸證明文件(如:身分證影本, 買賣合約書影本...等)應已加蓋「與正本相符」及「限辦理本行授信業務使用」之樣章 **/
	@Size(max = 1)
	@Column(name = "LOANDOCCHECKFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String loanDocCheckFlag;
	
	/** 是否為勞工紓困4.0 **/
	@Size(max = 1)
	@Column(name = "ISBAILOUT4", length = 1, columnDefinition = "CHAR(1)")
	private String isBailout4;
	
	/** 買賣契約書(如有)與借款契約、借款申請書簽名不一致 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISDOCSIGNATURENOTMATCH", length = 1, columnDefinition = "CHAR(1)")
	private String isDocSignatureNotMatch;
	
	/** 借款人、擔保品所有權人與房屋契約書之買方不同人 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISOWNERBUYERNOTSAMEPERSON", length = 1, columnDefinition = "CHAR(1)")
	private String isOwnerBuyerNotSamePerson;
	
	/** 由非親屬之第三人陪同申辦貸款 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISWITHNONRELATIVES", length = 1, columnDefinition = "CHAR(1)")
	private String isWithNonRelatives;
	
	/** 指定撥款日期及時間，且無法提出合理解釋 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISPOINTAPPROPRIATIONDATE", length = 1, columnDefinition = "CHAR(1)")
	private String isPointAppropriationDate;
	
	/** 對自己從事之行業或職業性質與內容、購屋目的不瞭解或毫無概念 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISDONTKNOWOWNAFFAIRS", length = 1, columnDefinition = "CHAR(1)")
	private String isDontKnowOwnAffairs;
	
	/** 於聯徵具被查詢紀錄，卻無法說明原因 {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISDONTEXPLAINEJCICRECORD", length = 1, columnDefinition = "CHAR(1)")
	private String isDontExplainEjcicRecord;
	
	/** 支付銀行收取開辦手續費以外之費用以完成貸款目的(確認有無代辦業者收費) {Y:有, N:無} **/
	@Size(max = 1)
	@Column(name = "ISPAYOTHERFEE", length = 1, columnDefinition = "CHAR(1)")
	private String isPayOtherFee;
	
	/** 信貸集中徵信註記 **/
	@Size(max = 1)
	@Column(name = "CONCENTRATECREDIT", length = 1, columnDefinition = "CHAR(1)")
	private String concentrateCredit;
	
	/** 借戶所得來自建築業者、代銷、仲介時，或其身分屬前述對象之關係戶(股東、員工)時，是否由其交易資料確認購屋價金來自第三人，且無法佐證其與第三人之關係。 {Y:是, N:	否/不適用} **/
	@Size(max = 1)
	@Column(name = "ISCASHFROMOTHERS", length = 1, columnDefinition = "CHAR(1)")
	private String isCashFromOthers;

	/** 產品初評註記 **/
	@Size(max = 1)
	@Column(name = "PRODKINDFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String prodKindFlag;
	
	/** 年輕族群客戶加強關懷結果:申請目的 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT1", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult1;
	
	/** 年輕族群客戶加強關懷結果:產品認知_第一項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT2_1", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult2_1;
	
	/** 年輕族群客戶加強關懷結果:產品認知_第二項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT2_2", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult2_2;
	
	/** 年輕族群客戶加強關懷結果:償債能力_第一項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT3_1", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult3_1;
	
	/** 年輕族群客戶加強關懷結果:償債能力_第二項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT3_2", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult3_2;
	
	/** 年輕族群客戶加強關懷結果:案件來源_第一項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT4_1", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult4_1;
	
	/** 年輕族群客戶加強關懷結果:案件來源_第二項 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT4_2", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult4_2;
	
	/** 年輕族群客戶加強關懷結果:告知與揭露 **/
	@Size(max = 1)
	@Column(name = "YOUNGCARERESULT5", length = 1, columnDefinition = "CHAR(1)")
	private String youngCareResult5;
	
	/** 年輕族群客戶加強關懷備註 **/
	@Size(max = 900)
	@Column(name = "YOUNGCAREMEMO", length = 900, columnDefinition = "VARCHAR(900)")
	private String youngCareMemo;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得編製單位代號
	 * <p/>
	 * 單位代碼
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}

	/**
	 * 設定編製單位代號
	 * <p/>
	 * 單位代碼
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定客戶型態 (區部別)
	 * <p/>
	 * 0.無、1.DBU、2.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public String getCustNo() {
		return this.custNo;
	}

	/**
	 * 設定客戶編號
	 * <p/>
	 * 非必要輸入
	 **/
	public void setCustNo(String value) {
		this.custNo = value;
	}

	/** 取得負責事業體統一編號 **/
	public String getCmpId() {
		return this.cmpId;
	}

	/** 設定負責事業體統一編號 **/
	public void setCmpId(String value) {
		this.cmpId = value;
	}

	/** 取得負責事業體名稱 **/
	public String getCmpNm() {
		return this.cmpNm;
	}

	/** 設定負責事業體名稱 **/
	public void setCmpNm(String value) {
		this.cmpNm = value;
	}

	/**
	 * 取得前婉卻分行
	 * <p/>
	 * C101S01E.isQdata1<br/>
	 * (MIS.LNUNID.REGBR)
	 */
	public String getBfRejBranch() {
		return this.bfRejBranch;
	}

	/**
	 * 設定前婉卻分行
	 * <p/>
	 * C101S01E.isQdata1<br/>
	 * (MIS.LNUNID.REGBR)
	 **/
	public void setBfRejBranch(String value) {
		this.bfRejBranch = value;
	}

	/**
	 * 取得前婉卻原因
	 * <p/>
	 * C101S01E.isQdata1<br/>
	 * (MIS.LNUNID.REFUSEDS)<br/>
	 * 128個全型字
	 */
	public String getBfRejReason() {
		return this.bfRejReason;
	}

	/**
	 * 設定前婉卻原因
	 * <p/>
	 * C101S01E.isQdata1<br/>
	 * (MIS.LNUNID.REFUSEDS)<br/>
	 * 128個全型字
	 **/
	public void setBfRejReason(String value) {
		this.bfRejReason = value;
	}

	/**
	 * 取得前婉卻日期
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.REGDT)
	 */
	public Date getBfRejDate() {
		return this.bfRejDate;
	}

	/**
	 * 設定前婉卻日期
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.REGDT)
	 **/
	public void setBfRejDate(Date value) {
		this.bfRejDate = value;
	}

	/**
	 * 取得前婉卻狀態
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * 婉卻狀態：1(控管);2(警示);D(刪除)
	 */
	public String getBfRejCase() {
		return this.bfRejCase;
	}

	/**
	 * 設定前婉卻狀態
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * 婉卻狀態：1(控管);2(警示);D(刪除)
	 **/
	public void setBfRejCase(String value) {
		this.bfRejCase = value;
	}

	/**
	 * 取得現婉卻狀態
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * 婉卻狀態：1(控管);2(警示);D(刪除)
	 */
	public String getRejectCase() {
		return this.rejectCase;
	}

	/**
	 * 設定現婉卻狀態
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * 婉卻狀態：1(控管);2(警示);D(刪除)
	 **/
	public void setRejectCase(String value) {
		this.rejectCase = value;
	}

	/**
	 * 取得現婉卻記錄
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * (組字)128個全型字<br/>
	 * ※ 1(控管)：本案主要借款人 ○○○ 曾於 YYYY-MM-DD HH:MM:SS.sssss 在 999 ＸＸ分行
	 * 有過婉卻紀錄，婉卻原因：ＸＸＸＸＸ...，現婉卻狀態仍屬控管層級，如欲承做需報授權外核准。<br/>
	 * ※ 2(警示)：本案主要借款人 ○○○ 曾於 YYYY-MM-DD HH:MM:SS.sssss 在 999 ＸＸ分行
	 * 有過婉卻紀錄，婉卻原因：ＸＸＸＸＸ...，現婉卻狀態已屬警示層級，如欲承做可於授權內承做。
	 */
	public String getRejectMemo() {
		return this.rejectMemo;
	}

	/**
	 * 設定現婉卻記錄
	 * <p/>
	 * ※配合婉卻控管機制增列<br/>
	 * (MIS.LNUNID.STATUSCD)<br/>
	 * (組字)128個全型字<br/>
	 * ※ 1(控管)：本案主要借款人 ○○○ 曾於 YYYY-MM-DD HH:MM:SS.sssss 在 999 ＸＸ分行
	 * 有過婉卻紀錄，婉卻原因：ＸＸＸＸＸ...，現婉卻狀態仍屬控管層級，如欲承做需報授權外核准。<br/>
	 * ※ 2(警示)：本案主要借款人 ○○○ 曾於 YYYY-MM-DD HH:MM:SS.sssss 在 999 ＸＸ分行
	 * 有過婉卻紀錄，婉卻原因：ＸＸＸＸＸ...，現婉卻狀態已屬警示層級，如欲承做可於授權內承做。
	 **/
	public void setRejectMemo(String value) {
		this.rejectMemo = value;
	}

	/**
	 * 取得評等類型
	 * <p/>
	 * 0.免辦 1.房貸個人評等模型 2.非房貸個人評等模型
	 */
	public String getMarkModel() {
		return this.markModel;
	}

	/**
	 * 設定評等類型
	 * <p/>
	 * 0.免辦 1.房貸個人評等模型 2.非房貸個人評等模型
	 **/
	public void setMarkModel(String value) {
		this.markModel = value;
	}

	/**
	 * 取得是否為自然人
	 * <p/>
	 * Y.是 N.否
	 */
	public String getNaturalFlag() {
		return this.naturalFlag;
	}

	/**
	 * 設定是否為自然人
	 * <p/>
	 * Y.是 N.否
	 **/
	public void setNaturalFlag(String value) {
		this.naturalFlag = value;
	}
	
	/**
	 * 取得是否可引進
	 * <p/>
	 * Y.可 N.不可
	 */
	public String getImportFlag() {
		return this.importFlag;
	}

	/**
	 * 設定是否可引進
	 * <p/>
	 * Y.可 N.不可
	 **/
	public void setImportFlag(String value) {
		this.importFlag = value;
	}

	/** 取得備註 **/
	public String getRmk() {
		return this.rmk;
	}

	/** 設定備註 **/
	public void setRmk(String value) {
		this.rmk = value;
	}

	/**
	 * 取得主要借款人註記
	 * <p/>
	 * ※2013-01-29新增欄位<br/>
	 * Y/N
	 */
	public String getKeyMan() {
		return this.keyMan;
	}

	/**
	 * 設定主要借款人註記
	 * <p/>
	 * ※2013-01-29新增欄位<br/>
	 * Y/N
	 **/
	public void setKeyMan(String value) {
		this.keyMan = value;
	}

	/**
	 * 取得相關身份
	 * <p/>
	 * ※2013-01-29新增欄位<br/>
	 * C.共同借款人<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 */
	public String getCustPos() {
		return this.custPos;
	}

	/**
	 * 設定相關身份
	 * <p/>
	 * ※2013-01-29新增欄位<br/>
	 * C.共同借款人<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 **/
	public void setCustPos(String value) {
		this.custPos = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得非房貸模型上線前舊案(若為 Y 時，表示只有 房貸模型，非房貸模型還不能用) **/
	public String getModelTyp() {
		return this.modelTyp;
	}

	/** 設定非房貸模型上線前舊案(若為 Y 時，表示只有 房貸模型，非房貸模型還不能用) **/
	public void setModelTyp(String value) {
		this.modelTyp = value;
	}
	
	/**
	 * join 房貸
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C120S01G c120s01g;

	public void setC120s01g(C120S01G c120s01g) {
		this.c120s01g = c120s01g;
	}

	public C120S01G getC120s01g() {
		return c120s01g;
	}
	
	/**
	 * join 非房貸
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C120S01Q c120s01q;

	public void setC120s01q(C120S01Q c120s01q) {
		this.c120s01q = c120s01q;
	}

	public C120S01Q getC120s01q() {
		return c120s01q;
	}
	
	/**
	 * join 卡友貸
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C120S01R c120s01r;

	public void setC120s01r(C120S01R c120s01r) {
		this.c120s01r = c120s01r;
	}

	public C120S01R getC120s01r() {
		return c120s01r;
	}
	
	
	/**
	 * join 日本消金評等模型(1.0=TTL、2.0=房貸)
	 */
	@OneToOne(cascade = { CascadeType.PERSIST, CascadeType.REMOVE }, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C121M01B c121m01b;

	public void setC121m01b(C121M01B c121m01b) {
		this.c121m01b = c121m01b;
	}

	public C121M01B getC121m01b() {
		return c121m01b;
	}
	
	/**
	 * join 日本消金評等模型(2.0=非房貸)
	 */
	@OneToOne(cascade = { CascadeType.PERSIST, CascadeType.REMOVE }, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C121M01F c121m01f;
	public void setC121m01f(C121M01F c121m01f) {
		this.c121m01f = c121m01f;
	}

	public C121M01F getC121m01f() {
		return c121m01f;
	}

	/**
	 * join 澳洲消金評等表
	 */
	
	@OneToOne(cascade = { CascadeType.PERSIST, CascadeType.REMOVE }, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false,insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false,insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false,insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false,insertable = false, updatable = false) })
	private C121M01C c121m01c;

	public void setC121m01c(C121M01C c121m01c) {
		this.c121m01c = c121m01c;
	}

	public C121M01C getC121m01c() {
		return c121m01c;
	}
	
	/**
	 * join 澳洲消金評等表 (非房貸)
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C121M01G c121m01g;

	public void setC121m01g(C121M01G c121m01g) {
		this.c121m01g = c121m01g;
	}

	public C121M01G getC121m01g() {
		return c121m01g;
	}

	/**
	 * join 泰國消金評等表
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C121M01D c121m01d;

	public void setC121m01d(C121M01D c121m01d) {
		this.c121m01d = c121m01d;
	}

	public C121M01D getC121m01d() {
		return c121m01d;
	}
	
	/**
	 * join 泰國消金評等表(非房貸)
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C121M01H c121m01h;

	public void setC121m01h(C121M01H c121m01h) {
		this.c121m01h = c121m01h;
	}

	public C121M01H getC121m01h() {
		return c121m01h;
	}
	
	/** XXX 顯示用欄位 */
	@Transient
	private Date ncbQDate;

	public void setNcbQDate(Date value) {
		this.ncbQDate = value;
	}

	public Date getNcbQDate() {
		return ncbQDate;
	}
	
	/** XXX 顯示用欄位 */
	@Transient
	private Date eJcicQDate;

	public void setEJcicQDate(Date value) {
		this.eJcicQDate = value;
	}

	public Date getEJcicQDate() {
		return eJcicQDate;
	}
	
	/** XXX 顯示用欄位 */
	@Transient
	private Date eChkQDate;

	public void setEChkQDate(Date value) {
		this.eChkQDate = value;
	}

	public Date getEChkQDate() {
		return eChkQDate;
	}
	
	/** XXX 顯示用欄位 */
	@Transient
	private String lnPeriod;

	public void setLnPeriod(String value) {
		this.lnPeriod = value;
	}

	public String getLnPeriod() {
		return lnPeriod;
	}
	
	/** XXX 顯示用欄位 */
	@Transient
	private String cmsLocation;

	public void setCmsLocation(String value) {
		this.cmsLocation = value;
	}

	public String getCmsLocation() {
		return cmsLocation;
	}
	
	/**
	 * 顯示用欄位
	 */
	/** 統編+重覆序號 **/
	@Transient
	private String custNumber;

	public void setCustNumber(String value) {
		this.custNumber = value;
	}

	public String getTest() {
		return custNumber;
	}	

	/**
	 * 設定客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public void setStaffNo(String value) {
		this.staffNo = value;
	}
	/**
	 * 取得客戶編號
	 * <p/>
	 * 非必要輸入
	 */
	public String getStaffNo() {
		return this.staffNo;
	}
	
	/** 取得第1碼krm040，第2碼bam095，第3碼KRS008(值 和C101S01G.cardFlag 未持有信用卡 相反) **/
	public String getJcicFlg() {
		return this.jcicFlg;
	}
	/** 設定第1碼krm040，第2碼bam095，第3碼KRS008(值 和C101S01G.cardFlag 未持有信用卡 相反) **/
	public void setJcicFlg(String value) {
		this.jcicFlg = value;
	}

	/** 取得行員編號 **/
	public String getMegaEmpNo() {
		return megaEmpNo;
	}
	/** 設定行員編號 **/
	public void setMegaEmpNo(String megaEmpNo) {
		this.megaEmpNo = megaEmpNo;
	}

	/** 取得(海外)當地識別ID **/
	public String getO_localCustId() {
		return o_localCustId;
	}
	/** 設定(海外)當地識別ID **/
	public void setO_localCustId(String o_localCustId) {
		this.o_localCustId = o_localCustId;
	}

	/** 取得(海外)與主要借款人關係 **/
	public String getO_custRlt() {
		return o_custRlt;
	}
	/** 設定(海外)與主要借款人關係 **/
	public void setO_custRlt(String o_custRlt) {
		this.o_custRlt = o_custRlt;
	}

	/** 取得輸入資料檢誤完成(Y/N) **/
	public String getO_chkYN() {
		return o_chkYN;
	}
	/** 設定輸入資料檢誤完成(Y/N) **/
	public void setO_chkYN(String o_chkYN) {
		this.o_chkYN = o_chkYN;
	}
	
	/** 取得海外基本資料 **/
	public String getO_single() {
		return o_single;
	}
	/** 設定海外基本資料 **/
	public void setO_single(String o_single) {
		this.o_single = o_single;
	}
	
	/** 取得串連ID **/
	public String getCaseId() {
		return caseId;
	}
	/** 設定串連ID **/
	public void setCaseId(String s) {
		this.caseId = s;
	}
	
	/** 取得評等文件 **/
	public String getRatingDesc() {
		return this.ratingDesc;
	}
	/** 設定評等文件 **/
	public void setRatingDesc(String value) {
		this.ratingDesc = value;
	}
	
	/** 取得評等種類 **/
	public String getRatingKind() {
		return this.ratingKind;
	}
	/** 設定評等種類 **/
	public void setRatingKind(String value) {
		this.ratingKind = value;
	}
	
	/** 取得附加檔案 **/
	public String getAttchFileOid() {
		return this.attchFileOid;
	}
	/** 設定附加檔案 **/
	public void setAttchFileOid(String value) {
		this.attchFileOid = value;
	}
	
	/** 取得附加檔案2 **/
	public String getAttchFileOid2() {
		return this.attchFileOid2;
	}
	/** 設定附加檔案2 **/
	public void setAttchFileOid2(String value) {
		this.attchFileOid2 = value;
	}

	/** 取得顯示順序 **/
	public Integer getCustShowSeqNum() {
		return custShowSeqNum;
	}
	/** 設定顯示順序 **/
	public void setCustShowSeqNum(Integer custShowSeqNum) {		
		this.custShowSeqNum = custShowSeqNum;
	}
	
	/** 取得查詢異常通報紀錄日期 **/
	public Date getAbnormalReadDate() {
		return abnormalReadDate;
	}
	/** 設定查詢異常通報紀錄日期 **/
	public void setAbnormalReadDate(Date abnormalReadDate) {
		this.abnormalReadDate = abnormalReadDate;
	}

	/** 取得通報分行 **/
	public String getAbnormalBrNo() {
		return abnormalBrNo;
	}
	/** 設定通報分行 **/
	public void setAbnormalBrNo(String abnormalBrNo) {
		this.abnormalBrNo = abnormalBrNo;
	}

	/** 取得通報日期 **/
	public Date getAbnormalDate() {
		return abnormalDate;
	}
	/** 設定通報日期 **/
	public void setAbnormalDate(Date abnormalDate) {
		this.abnormalDate = abnormalDate;
	}

	/** 取得目前異常通報狀態{Y:已解除,N:未解除} **/
	public String getAbnormalStatus() {
		return abnormalStatus;
	}
	/** 設定目前異常通報狀態{Y:已解除,N:未解除} **/
	public void setAbnormalStatus(String abnormalStatus) {
		this.abnormalStatus = abnormalStatus;
	}

	/** 取得異常通報文件id **/
	public String getAbnormalMainId() {
		return abnormalMainId;
	}
	/** 設定異常通報文件id **/
	public void setAbnormalMainId(String abnormalMainId) {
		this.abnormalMainId = abnormalMainId;
	}

	/** 取得信用卡正卡張數(不含停用) **/
	public BigDecimal getPrimary_card() {
		return primary_card;
	}
	/** 設定信用卡正卡張數(不含停用) **/
	public void setPrimary_card(BigDecimal primary_card) {
		this.primary_card = primary_card;
	}
	
	/** 取得信用卡附卡張數(不含停用) **/
	public BigDecimal getAdditional_card() {
		return additional_card;
	}
	/** 設定信用卡附卡張數(不含停用) **/
	public void setAdditional_card(BigDecimal additional_card) {
		this.additional_card = additional_card;
	}

	/** 取得信用卡商務/採購卡張數(不含停用) **/
	public BigDecimal getBusiness_or_p_card() {
		return business_or_p_card;
	}
	/** 設定信用卡商務/採購卡張數(不含停用) **/
	public void setBusiness_or_p_card(BigDecimal business_or_p_card) {
		this.business_or_p_card = business_or_p_card;
	}

	public String getIsImportDataMatch() {
		return isImportDataMatch;
	}

	public void setIsImportDataMatch(String isImportDataMatch) {
		this.isImportDataMatch = isImportDataMatch;
	}

	public String getIsFullApplyDocument() {
		return isFullApplyDocument;
	}

	public void setIsFullApplyDocument(String isFullApplyDocument) {
		this.isFullApplyDocument = isFullApplyDocument;
	}

	public String getIsNoteBorrower() {
		return isNoteBorrower;
	}

	public void setIsNoteBorrower(String isNoteBorrower) {
		this.isNoteBorrower = isNoteBorrower;
	}

	public String getIsCheckOriginalDocument() {
		return isCheckOriginalDocument;
	}

	public void setIsCheckOriginalDocument(String isCheckOriginalDocument) {
		this.isCheckOriginalDocument = isCheckOriginalDocument;
	}

	public String getCreditLoanReductFg() {
		return creditLoanReductFg;
	}
	public void setCreditLoanReductFg(String creditLoanReductFg) {
		this.creditLoanReductFg = creditLoanReductFg;
	}

	public String getHasHouseLoan() {
		return hasHouseLoan;
	}
	public void setHasHouseLoan(String hasHouseLoan) {
		this.hasHouseLoan = hasHouseLoan;
	}
	
	public BigDecimal getWm_12m() {
		return wm_12m;
	}
	public void setWm_12m(BigDecimal wm_12m) {
		this.wm_12m = wm_12m;
	}

	public Date getHoldMegaCardDt() {
		return holdMegaCardDt;
	}
	public void setHoldMegaCardDt(Date holdMegaCardDt) {
		this.holdMegaCardDt = holdMegaCardDt;
	}

	public String getLoanDocCheckFlag() {
		return loanDocCheckFlag;
	}

	public void setLoanDocCheckFlag(String loanDocCheckFlag) {
		this.loanDocCheckFlag = loanDocCheckFlag;
	}

	public String getIsBailout4() {
		return isBailout4;
	}

	public void setIsBailout4(String isBailout4) {
		this.isBailout4 = isBailout4;
	}

	public String getIsDocSignatureNotMatch() {
		return isDocSignatureNotMatch;
	}

	public void setIsDocSignatureNotMatch(String isDocSignatureNotMatch) {
		this.isDocSignatureNotMatch = isDocSignatureNotMatch;
	}

	public String getIsOwnerBuyerNotSamePerson() {
		return isOwnerBuyerNotSamePerson;
	}

	public void setIsOwnerBuyerNotSamePerson(String isOwnerBuyerNotSamePerson) {
		this.isOwnerBuyerNotSamePerson = isOwnerBuyerNotSamePerson;
	}

	public String getIsWithNonRelatives() {
		return isWithNonRelatives;
	}

	public void setIsWithNonRelatives(String isWithNonRelatives) {
		this.isWithNonRelatives = isWithNonRelatives;
	}

	public String getIsPointAppropriationDate() {
		return isPointAppropriationDate;
	}

	public void setIsPointAppropriationDate(String isPointAppropriationDate) {
		this.isPointAppropriationDate = isPointAppropriationDate;
	}

	public String getIsDontKnowOwnAffairs() {
		return isDontKnowOwnAffairs;
	}

	public void setIsDontKnowOwnAffairs(String isDontKnowOwnAffairs) {
		this.isDontKnowOwnAffairs = isDontKnowOwnAffairs;
	}

	public String getIsDontExplainEjcicRecord() {
		return isDontExplainEjcicRecord;
	}

	public void setIsDontExplainEjcicRecord(String isDontExplainEjcicRecord) {
		this.isDontExplainEjcicRecord = isDontExplainEjcicRecord;
	}

	public String getIsPayOtherFee() {
		return isPayOtherFee;
	}

	public void setIsPayOtherFee(String isPayOtherFee) {
		this.isPayOtherFee = isPayOtherFee;
	}

	public String getConcentrateCredit() {
		return concentrateCredit;
	}

	public void setConcentrateCredit(String concentrateCredit) {
		this.concentrateCredit = concentrateCredit;
	}

	public String getIsCashFromOthers() {
		return isCashFromOthers;
	}

	public void setIsCashFromOthers(String isCashFromOthers) {
		this.isCashFromOthers = isCashFromOthers;
	}

	public String getProdKindFlag() {
		return prodKindFlag;
	}

	public void setProdKindFlag(String prodKindFlag) {
		this.prodKindFlag = prodKindFlag;
	}

	public String getYoungCareResult1() {
		return youngCareResult1;
	}

	public void setYoungCareResult1(String youngCareResult1) {
		this.youngCareResult1 = youngCareResult1;
	}

	public String getYoungCareResult2_1() {
		return youngCareResult2_1;
	}

	public void setYoungCareResult2_1(String youngCareResult2_1) {
		this.youngCareResult2_1 = youngCareResult2_1;
	}

	public String getYoungCareResult2_2() {
		return youngCareResult2_2;
	}

	public void setYoungCareResult2_2(String youngCareResult2_2) {
		this.youngCareResult2_2 = youngCareResult2_2;
	}

	public String getYoungCareResult3_1() {
		return youngCareResult3_1;
	}

	public void setYoungCareResult3_1(String youngCareResult3_1) {
		this.youngCareResult3_1 = youngCareResult3_1;
	}

	public String getYoungCareResult3_2() {
		return youngCareResult3_2;
	}

	public void setYoungCareResult3_2(String youngCareResult3_2) {
		this.youngCareResult3_2 = youngCareResult3_2;
	}

	public String getYoungCareResult4_1() {
		return youngCareResult4_1;
	}

	public void setYoungCareResult4_1(String youngCareResult4_1) {
		this.youngCareResult4_1 = youngCareResult4_1;
	}

	public String getYoungCareResult4_2() {
		return youngCareResult4_2;
	}

	public void setYoungCareResult4_2(String youngCareResult4_2) {
		this.youngCareResult4_2 = youngCareResult4_2;
	}

	public String getYoungCareResult5() {
		return youngCareResult5;
	}

	public void setYoungCareResult5(String youngCareResult5) {
		this.youngCareResult5 = youngCareResult5;
	}

	public String getYoungCareMemo() {
		return youngCareMemo;
	}

	public void setYoungCareMemo(String youngCareMemo) {
		this.youngCareMemo = youngCareMemo;
	}
	
}
