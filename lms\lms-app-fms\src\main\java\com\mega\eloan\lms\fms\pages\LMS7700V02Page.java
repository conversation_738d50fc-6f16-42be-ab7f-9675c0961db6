package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;


/**
 * <pre>
 * 電子文件維護作業 - 待覆核
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller
@RequestMapping(path = "/fms/lms7700v02")
public class LMS7700V02Page extends AbstractEloanInnerView {

	public LMS7700V02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核,
				CreditDocStatusEnum.先行動用_待覆核);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View);
		renderJsI18N(LMS7700M01Page.class);
		renderJsI18N(LMS7700V01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS7700V02Page.js" };
	}
}
