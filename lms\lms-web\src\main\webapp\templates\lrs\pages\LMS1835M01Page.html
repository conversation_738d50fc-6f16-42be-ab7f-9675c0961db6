<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:extend>
		<div class="button-menu funcContainer" id="buttonPanel">
			<!-- <wicket:enclosure>
				<span wicket:id="_btnDOC_EDITING" />
			</wicket:enclosure> -->
		
			<button id="btnSave">
				<span class="ui-icon ui-icon-jcs-04" />
				<wicket:message key="button.save">儲存</wicket:message>
			</button>
			<button id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<wicket:message key="button.exit">離開</wicket:message>
			</button>
		</div>
		<div class="tit2 color-black">
			<wicket:message key="doc.title"></wicket:message>
		</div>
		<form id="L181M01AForm">
		<table class="tb2" border="0" cellspacing="0" cellpadding="0"
			width="100%">
			<tr>
				<td class="hd1" width="20%" colspan="2"><wicket:message key="L181M01A.genDate">指定執行整批產生名單時間</wicket:message>&nbsp;&nbsp;</td>
				<td colspan="4" width="80%">
					<input type="text" class="date" size="10" id="genDate" name="genDate" />
					<input type="text" size="5" id="time" name="time" value=":" />(HH:mm)
				</td>
			</tr>
			<tr>
				<td class="hd1" colspan="2"><wicket:message key="L181M01A.baseDate">下次覆審日期年月</wicket:message>&nbsp;&nbsp;</td>
				<td colspan="4"><input type="text" size="7" maxlength="7" minlength="7" id="baseDate" />(YYYY-MM)</td>
			</tr>
			<tr>
				<td class="hd1" colspan="2"><wicket:message key="L181M01A.docStatus">文件狀態</wicket:message>&nbsp;&nbsp;</td>
				<td colspan="4"><span id="docStatus"></span></td>
			</tr>
			<tr>
				<td class="hd1" colspan="2"><wicket:message key="L181M01A.creator">建立人員</wicket:message>&nbsp;&nbsp;</td>
				<td colspan="4"><span id="creator"></span>(<span id="createTime"></span>)</td>
			</tr>
			<tr>
				<td class="hd1" colspan="2"><wicket:message key="L181M01A.updater">異動人員</wicket:message>&nbsp;&nbsp;</td>
				<td colspan="4"><span id="updater"></span>(<span id="updateTime"></span>)</td>
			</tr>
			<tr>
				<td class="hd1" rowspan="28" width="2%"><wicket:message key="L181M01A.branchList">分行名單</wicket:message></td>
				<td><button id="allCheck">
						<span class="text-only"><wicket:message key="chooseAll">選擇全部</wicket:message></span>
					</button></td>
				<td colspan="4"><button id="clean">
						<span class="text-only"><wicket:message key="clearAll">全部清空</wicket:message></span>
					</button></td>
			</tr>
			<tr>
				<td colspan="5"><input id="order" type="checkbox"
					name="branchList" /></td>
			</tr>
		</table>
		</form>
		<wicket:message key="memo"></wicket:message>
		<script type="text/javascript" src="pagejs/lrs/LMS1835M01Page.js"></script>
	</wicket:extend>
</body>
</html>
