/* 
 * C250M01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C250M01E;

/** 可疑代辦案件註記作業簽章欄檔 **/
public interface C250M01EDao extends IGenericDao<C250M01E> {

	C250M01E findByOid(String oid);
	
	List<C250M01E> findByMainId(String mainId);
	
	C250M01E findByUniqueKey(String mainId, String branchType, String branchId, String staffNo, String staffJob);

	List<C250M01E> findByIndex01(String mainId, String branchType, String branchId, String staffNo, String staffJob);
}