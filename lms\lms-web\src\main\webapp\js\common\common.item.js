/* 專案JS設定檔-common item    */
$.extend(Properties, {
    commonItem: {
        numeric: {
            "class": "numeric",
            positiveonly: true,
			size:16,
			maxlength:15,
            integer: 13,
            fraction: 0
        },
        percent: {
            "class": "numeric",
            positiveonly: true,
			size:7,
			maxlength:6,
            integer: 3,
            fraction: 2
        },
        date: {
            "class": "date",
            size: 12,
			maxlength:10
        }
    }
});


/* 專案JS設定檔-欄位初始設定   */
$.extend(Properties, {
    itemMaskRule: {
        "[class*=numeric]": function(val){
   /* 2012-03-12 for subfix */
   var $this = $(this),val = $this.val() + "", _sf = $this.attr("subfix") || "";
            /* 20130108 不可輸入SPACE */
            val = $.trim($this.val());
            if (val && /[0-9,.]+/.test(val)) {
                var re = new RegExp(_sf,"g");
    val = val.replace(/,/g,"").replace(re,"");
    $this.data("realValue", val);
    // 2011-10-20 update for negative number by Sunkist
                return val.replace(/^(-?)(\d+)((\.\d+)?)$/, function(s, s0, s1, s2){
                    return s0 + s1.replace(/\d{1,3}(?=(\d{3})+$)/g, "$&,") + s2+ _sf;
                });
            }
            return val;
        },
        "[class*=fullText]": function(val){//轉全形
        	var $this = $(this),val = $this.val() + "";
        	var fVal = val.toFull();
        	$this.data("realValue", fVal);
        	return fVal;
        },
		"[class*=halfText]": function(val){//轉半形
        	var $this = $(this),val = $this.val() + "";
        	var fVal = val.toHalf();
        	$this.data("realValue", fVal);
        	return fVal;
        },
        "[class*=upText]": function(val){//轉大寫
        	var $this = $(this),val = $this.val() + "";
        	var uVal = val.toUpperCase() ;
        	$this.data("realValue", uVal);
        	return uVal;
        },
        "[class*=trim]": function(val){//去除多餘空白
        	var $this = $(this);
        	var uVal = $.trim($this.val());
        	$this.data("realValue", uVal);
        	return uVal;
        }
    }
});

