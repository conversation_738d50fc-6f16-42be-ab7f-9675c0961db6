var initDfd = initDfd || $.Deferred()
//J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
var lastSel;
var guarantorPriorityOn = "";

initDfd.done(function(json, auth){

    //    if (!$.isEmptyObject(json.cntSelect)) {
    //        if ($("#L161M01AForm").find("#cntrNo").length) {
    //            $("#L161M01AForm").find("#cntrNo").setItems({
    //                item: json.cntSelect,
    //                space: false,
    //                value: json.cntrNo || ""
    //            });
    //        }
    //        
    //    }
    //舊案用
	
	$.ajax({
        handler: "lms1605m01formhandler",
        data: {
            formAction: "queryInitData"
        }
    }).done(
        function(initObj){
        	 
        	//J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
        	guarantorPriorityOn = initObj.guarantorPriorityOn;
        	
        	if(guarantorPriorityOn == "Y"){
        		$("#setGuarantorCreditPriority").show();
        		$("#btnEntireApply").show();
        	}else{
        		$("#setGuarantorCreditPriority").hide();
        		$("#btnEntireApply").hide();
        	}

        }
    );
	
    var gridviewBranchOld = $("#gridviewBranchOld").iGrid({
        localFirst: true,
        needPager: false,
        handler: inits.ghaddle,
        height: "190px",
        width: "100%",
        multiselect: true,
        sortname: 'slBank|slBranch',
        sortorder: 'asc|asc',
        postData: {
            formAction: "queryBranch"
        },
        colModel: [{
            colHeader: i18n.lms1605m01['L160M01A.slBank'],// "參貸行庫/分行",
            name: 'slBankCN',
            width: '200px',
            sortable: true,
            align: "left",
            formatter: 'click',
            onclick: branchBox
        }, {
            colHeader: i18n.lms1605m01['L160M01A.slMaster'],//"共同主辦行",
            name: 'slMaster',
            width: '80px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.slAccNo'],// "同業帳號",
            name: 'slAccNo',
            width: '100px',
            sortable: true,
            align: "right"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.slAmt'],// "參貸金額",
            name: 'slAmt',
            width: '100px',
            sortable: true,
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2//小數點到第幾位
            }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'pid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = gridviewBranchOld.getRowData(rowid);
            branchBox(null, null, data);
            
        }
    });
    
    //新案用L161S01A
    var gridviewBranch = $("#gridviewBranch").iGrid({
        needPager: false,
        localFirst: true,
        handler: inits.ghaddle,
        height: "190px",
        width: "100%",
        multiselect: true,
        sortname: 'seq',
        sortorder: 'asc',
        postData: {
            formAction: "queryBranch"
        },
        colModel: [{
            colHeader: i18n.lms1605m01['L160M01A.slBank'],// "參貸行庫/分行",
            name: 'slBankCN',
            width: '200px',
            sortable: true,
            align: "left",
            formatter: 'click',
            onclick: branchBox
        }, {
            colHeader: i18n.lms1605m01['L160M01A.slMaster'],//"共同主辦行",
            name: 'slMaster',
            width: '80px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.slAccNo'],// "同業帳號",
            name: 'slAccNo',
            width: '100px',
            sortable: true,
            align: "right"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.slAmt'],// "參貸金額",
            name: 'slAmt',
            width: '100px',
            sortable: true,
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2//小數點到第幾位
            }
        }, {
            name: 'seq',
            hidden: true
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'pid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = gridviewBranch.getRowData(rowid);
            branchBox(null, null, data);
            
        }
    });
    
 // G-113-0036 簽案額度明細聯行攤貸比例
	var gridviewL140M01E_AF = $(BranchAcitonAF.gridId).iGrid({
	    handler: inits.ghaddle,
	    height: 170,
	    rowNum: 10,
	    rownumbers: true,
	    multiselect: true,
	    hideMultiselect: false,
	    sortname: 'createTime',
	    sortorder: 'asc',
	    postData: {
	        formAction: "queryL140m01e_af",
	        cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
			noOpenDoc: true
	    },
	    autowidth: true,
	    colModel: [{
	        name: 'shareRate2',
	        hidden: true
	    }, {
	        colHeader: i18n.lms1605m01["L140M01e.shareBrId"],//"攤貸分行",
	        name: 'shareBrId',
	        align: "left",
	        width: 110,
	        sortable: true,
	        formatter: 'click',
	        onclick: BranchAcitonAF.query
	    }, {
	        colHeader: i18n.lms1605m01["L140M01e.shareAmt"],//"攤貸金額",
	        name: 'shareAmt',
	        width: 160,
	        sortable: true,
	        align: "right",
	        formatter: 'currency',
	        formatoptions: {
	            thousandsSeparator: ",",
				removeTrailingZero: true,
	            decimalPlaces: 2//小數點到第幾位
	        }
	    }, {
	        colHeader: i18n.lms1605m01["L140M01e.shareRate1"],//"攤貸比例",
	        width: 140,
	        name: 'showRate',
	        align: "right",
	        sortable: true
	    }, {
	        colHeader: i18n.lms1605m01["L140M01e.shareNo"],//"額度序號",
	        width: 140,
	        name: 'shareNo',
	        sortable: true
	    }, {
	        name: 'oid',
	        hidden: true
	    }],
	    ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能           	
	        var data = $(BranchAcitonAF.gridId).getRowData(rowid);
	        BranchAcitonAF.query(null, null, data);
	    }
	});
	//簽案額度明細聯行攤貸比例調整
	$(BranchAcitonAF.amtgrId).iGrid({
		handler: inits.ghaddle,
        rowNum: 10,
        postData: {
        	 formAction: "queryL140m01e_af",
		     cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
        },
        rowNum: 10,
        autowidth: true,
        colModel: [{
            name: 'shareRate2',
            hidden: true
        }, {
            colHeader: i18n.lms1605m01["L140M01e.shareBrId"],//"攤貸分行",
            name: 'shareBrId',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01["L140M01e.shareAmt"],//"攤貸金額",
            name: 'shareAmt',
            width: 160,
            sortable: true,
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2
            }
        }, {
            colHeader: i18n.lms1605m01["L140M01e.shareRate1"],//"攤貸比例",
            width: 140,
            name: 'showRate',
            align: "right",
            sortable: true
        }, {
            colHeader: i18n.lms1605m01["L140M01e.shareNo"],//"額度序號",
            width: 140,
            name: 'shareNo',
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }]
    });
    
    $("#gridviewPeople").iGrid({
        handler: 'lms1605gridhandler',
        width: "100%",
        height: "250px",
        sortname: 'cntrNo|rType|rKindD|rId',
        sortorder: 'asc|asc|asc|asc',
        multiselect: true,
        postData: {
            formAction: "queryPeople"
        },
        colModel: [{
            colHeader: i18n.lms1605m01['L162M01A.custId'],//"主債務人統編",
            name: 'custId',
            width: '60px',
            sortable: true,
            formatter: 'click',
            onclick: peopleBox
        }, {
            colHeader: i18n.lms1605m01['L160M01A.cntrNum'],// "額度序號",
            name: 'cntrNo',
            width: '70px',
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rId'],//"從債務人統編",
            name: 'rId',
            width: '60px',
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rName'],// "從債務人名稱",
            name: 'rName',
            width: '100px',
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rKindM'],// "關係",
            name: 'rKindD',
            width: '80px',
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rCountry'],// "國別",
            name: 'rCountry',
            width: '50px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rType'],// "相關身份",
            name: 'rType',
            width: '100px',
            sortable: true
        
		}, {
            colHeader: i18n.lms1605m01['L162M01A.guaPercent'],// "保證人負担保證責任比率",
            name: 'guaPercent',
            width: '30px',
            sortable: false	
		 }, {
			 //J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
             colHeader: i18n.lms1605m01["L162M01A.priority"],//信用品質順序
             name: 'priority',
             align: "center",
             width: 20,
             sortable: false   
		 }, {
			 //J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
             colHeader: i18n.lms1605m01["L162M01A.guaNaExposure"],//本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
             name: 'guaNaExposure',
             align: "center",
             width: 20,
             sortable: false        
		}, {
            colHeader: i18n.lms1605m01['L162M01A.dueDate'] + "<br/>" + i18n.lms1605m01['L162M01A.dueDate2'],//"董監事任期止日<br />(保證人保證迄日)",
            name: 'dueDate',
            width: '70px',
            sortable: true     
        }, {
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = $("#gridviewPeople").getRowData(rowid);
            peopleBox(null, null, data);
        }
    });
    
    
  //J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	/**  保證人信用品質順序設定grid  */
    $("#gridViewGuarantorSeq").iGrid({
		needPager: false,
        handler: 'lms1601gridhandler',
        postData: {
            formAction: "queryPeopleForSetPriority"
        },
        height: 230,
        cellsubmit: 'clientArray',
        autowidth: true,
        sortname: 'priority|createTime',
        sortorder: 'asc|asc',
        localFirst: true,
        colModel: [{
            colHeader: i18n.lms1605m01['L162M01A.custId'],//"主債務人統編",
            name: 'custId',
            width: '80px',
            sortable: true,
            formatter: 'click',
            onclick: peopleBox
        }, {
            colHeader: i18n.lms1605m01['L160M01A.cntrNum'],// "額度序號",
            name: 'cntrNo',
            width: '100px',
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rId'],//"從債務人統編",
            name: 'rId',
            width: '100px',
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rName'],// "從債務人名稱",
            name: 'rName',
            width: '100px',
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rKindM'],// "關係",
            name: 'rKindD',
            width: '120px',
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rCountry'],// "國別",
            name: 'rCountry',
            width: '50px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L162M01A.rType'],// "相關身份",
            name: 'rType',
            width: '100px',
            sortable: true
		}, {
            colHeader: i18n.lms1605m01['L162M01A.guaPercent'],// "保證人負担保證責任比率",
            name: 'guaPercent',
            width: '40px',
            sortable: false	
		}, {
            colHeader: i18n.lms1605m01["L162M01A.priority"],//信用品質順序
            name: 'priority',
            align: "center",
            editable: true,
            width: 60,
            sortable: true,
            editrules: {
                number: true
            }
        }, {
            name: 'oid',
            hidden: true
        }],
        onSelectRow: function(id){
            if (id && id != lastSel) {
            	$("#gridViewGuarantorSeq").saveRow(lastSel, false, 'clientArray');
                $('#gridViewGuarantorSeq').restoreRow(lastSel);
                lastSel = id;
            }
            $('#gridViewGuarantorSeq').editRow(id, false);
        }
    });
     
    
    var showVersion = json.showVersion;
    
    if (showVersion == "0") {
        $("#showOldL161M01AForm").show();
        $("#showL161M01CForm").hide();
        if (json.unitLoanCaseOld == "Y") {
            $("#lms160s0301").show();
        }
        var cntrNo = $("#showOldL161M01AForm").find("#cntrNoOld");
        $("#gridviewBranchOld").jqGrid("setGridParam", {
            postData: {
                formAction: "queryBranch",
                cntrNo: cntrNo,
                uid: json.uidOld
            }
        }).trigger("reloadGrid");
        
    }
    else {
        $("#lms160s0301").show();
        $("#showOldL161M01AForm").hide();
        $("#showL161M01CForm").show();
    }
    
    
    var custDataInfo = json.custIdSelect;
    if (!$.isEmptyObject(custDataInfo)) {
        var temp = "<option value=''>" + i18n.def.comboSpace + "</option>";
        $("#L162M01AForm").find("#cntrNo").html(temp);
        //先帶出custId
        for (var key in custDataInfo) {
            temp += "<option value=" + key + ">" + key.substring(0, key.length - 1) + " " + key.substring(key.length - 1, key.length) + "</option>";
        }
        $("#custIdSelect").html(temp);
        
    }
    $("#custIdSelect").change(function(){
        var value = $(this).val();
        var temp = "";
        if (value) {
            for (var key in custDataInfo[value]) {
                //先帶出custId 
                temp += "<option value=" + custDataInfo[value][key] + ">" + custDataInfo[value][key] + "</option>";
            }
        }
        else {
            temp = "<option value=''>" + i18n.def.comboSpace + "</option>";
        }
        $("#L162M01AForm").find("#cntrNo").html(temp);
    });
    
	//G-113-0036 額度動用資訊>聯貸案參貸比率一覽>聯貸案參貸比率資訊> 給號新號，舊號的隱藏條件
    $("input[name=numberType]").change(function(){
        var $cntrNoForm = $("#cntrNoBoxforItem3Form");
        var type = $cntrNoForm.find("#cntrNoType").val(type);
        $cntrNoForm.find(".ForOriginal,.ForInSide").hide();
        //海外不行選輸入分行 和 是否遠匯 
        var value = $(this).val();
        switch (value) {
            case "1":
                if (type != "5") {
                    $cntrNoForm.find(".ForInSide").show();
                }
                $("#showBrNoTr").show();
                break;
            case "2":
                $cntrNoForm.find(".ForOriginal").show();
                break;
        }
    });
   
   
    /**取得聯貸案已編碼國外銀行的清單
     *
     * @param {String } value 分行種類 12-國外分行 99 -其他(由國外部徵信系統金融機構資料維護)
     */
    function getBranch(value){
        $.ajax({
            handler: "lms1605m01formhandler",
            data: {
                formAction: "queryforeignBranch",
                type: value
            }
        }).done(
            function(obj){
                if (!$.isEmptyObject(obj.foreignBranch)) {
                    //新增空白選項
                    $("#selBank" + value).setItems({
                        item: obj.foreignBranch,
                        format: "{value} - {key}",
                        space: false
                    });
                }
            }
        );
    }
    //判斷是否顯示參貸行
    // $("#lms160s0301").show()
    //    if (json.unitLoanCase == "Y") {
    //        $("#lms160s0301").show()
    //    }
    //    else {
    //        $("#lms160s0302").click();
    //    }
    
    $("#lms160s0302").click();
    
    //關係切換
    $('#rKindM').change(function(){
        var value = $(this).val();
        switch (value) {
            case "1":
                $("#the1").show().siblings("#the2,#the3").hide();
                break;
            case "2":
                $("#the2").show().siblings("#the1,#the3").hide();
                break;
            case "3":
                $("#the3").show().siblings("#the1,#the2").hide();
                break;
            default:
                $("#the1,#the2,#the3").hide();
                $("[id^=rationSelect]").val("");
                break;
        }
    });
    
	//相關身份
    $("#L162M01AForm").find('#rType').change(function(){
        var value = $(this).val();
		if(value == "C" || value == "S" ){
			$(".showGuaPercent").hide();
            $("#guaPercent").val(null);
            $("[name=guaNaExposure]:radio").prop("checked", false);//移除隱藏選項的radio checked ;
		} else{
			$(".showGuaPercent").show();
            $("#guaPercent").val(100);
		}
		
		//J-106-0029-004  洗錢防制-動審表新增洗錢防制頁籤
		if(value == "C"){
			$("#L162M01AForm").find(".hBorrowData").show();
		}else{
			$("#L162M01AForm").find(".hBorrowData").hide();
		}

    });
	
    //銀行種類切換
    $('#selBank').change(function(){
        var value = DOMPurify.sanitize($(this).val());
        
        if ($.trim(value) != "") {
        
            $("#selBank" + value).show().siblings("[id^=selBank]").hide();
        }
        else {
        
            $("#tdSelBank").find("[id^=selBank]").hide();
        }
        //當沒有下拉選單時才去抓		
        if (value == "12" && $("#selBank12 option").length == 0) {
        
            getBranch(value);
        }
        else 
            if (value == "99" && $("#selBank99 option").length == 0) {
            
                getBranch(value);
            }
        
    });
    
    //動態切換載入銀行
    $('#selBank01,#selBank02').change(function(){
        var value = $(this).val();
        var id = DOMPurify.sanitize($(this).attr("id").slice(7));//$(this).attr("id").slice(7) = 01 or 02
        $("#selBankOther" + id).show();
        if ($.trim(value) == "") {
            $("#selBankOther" + id).hide();
            return false;
        }
        
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryBranch",
                mainBranch: value
            }
        }).done(
            function(obj){
                if (!$.isEmptyObject(obj.brankList)) {
                    $("#selBankOther" + id).setItems({
                        item: obj.brankList,
                        format: "{value} {key}",
                        space: false
                    });
                }
                else {
                    $("#selBankOther" + id).hide().html("");
                }
            }
        );
    });
    
    
    
    //新增聯貸案參貸比率一覽表
    $("#openBranchBox").click(function(){
        var amt = $.trim($("#quotaAmt").val());
        if (!$("#L161M01AForm").valid()) {
            return;
        }
        //        //檢驗規則
        //        if (amt == 0 || amt == '') {
        //            //L160M01A.error5=總額度不得為
        //            return CommonAPI.showErrorMessage(i18n.lms1605m01["L160M01A.error5"] + "0");
        //        }
        
        branchBox();
    });
    
    //複製同業參貸比率
    $("#copyBranchBox").click(function(){
        copyBranchBox();
    });
    
    //引進案由
    $("#applyGist").click(function(){
        applyGist();
    });
    
    
    //刪除聯貸案參貸比率一覽表
    $("#deleteBranch").click(function(){
    
        var select = gridviewBranch.getGridParam('selarrrow');
        if (select == "") {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = [];
                for (var i in select) {
                    data.push(gridviewBranch.getRowData(select[i]).oid);
                }
                
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL161M01B",
                        oids: data
                    }
                }).done(
                    function(obj){
                        gridviewBranch.trigger("reloadGrid");
                    }
                );
            }
            else {
                return;
            }
        });
    });
    
    //列印聯貸案參貸比率一覽表(舊案用)
    $("#printBranchOld").click(function(){
        var count = $("#gridviewBranchOld").jqGrid('getGridParam', 'records');
        var uids = responseJSON.mainId;
        
        //先檢查是否有資料
        if (count == 0) {
            //EFD0002=INFO|報表無資料|
            return CommonAPI.showErrorMessage(i18n.msg["EFD0002"]);
        }
        
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                type: "R02",
                mainId: responseJSON.mainId,
                mainOid: $("#mainOid").val(),
                fileDownloadName: "lms1605r02.pdf",
                serviceName: "lms1605r02rptservice",
				mode:"OLD",
                uids: uids
            }
        });
    });
    
    //列印聯貸案參貸比率一覽表(新案用L161S01A模式)
    $("#printBranch").click(function(){
        var count = $("#gridviewBranch").jqGrid('getGridParam', 'records');
        var uids = $("#L161M01AForm").find("#uid").val();
        
        //先檢查是否有資料
        if (count == 0) {
            //EFD0002=INFO|報表無資料|
            return CommonAPI.showErrorMessage(i18n.msg["EFD0002"]);
        }
        
        //先確認整批勾選列印的額度動用資訊是否都有聯貸參貸比率
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "chkAllPrintHasL161S01B",
                mainId: responseJSON.mainId,
                mainOid: $("#mainOid").val(),
                uids: uids
            }
        }).done(
            function(obj){
                $.form.submit({
                    url: "../../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        type: "R02",
                        mainId: responseJSON.mainId,
                        mainOid: $("#mainOid").val(),
                        fileDownloadName: "lms1605r02.pdf",
                        serviceName: "lms1605r02rptservice",
                        uids: uids
                    }
                });
            }
        );
        
        
    });
    
    //整批列印聯貸案參貸比率一覽表
    $("#printBranchAll").click(function(){
        var $gridviewCntrnoInfo = $("#gridviewCntrnoInfo");
        var count = $gridviewCntrnoInfo.jqGrid('getGridParam', 'records');
        
        //先檢查是否有資料
        if (count == 0) {
            //EFD0002=INFO|報表無資料|
            return CommonAPI.showErrorMessage(i18n.msg["EFD0002"]);
        }
        
        var ids = $gridviewCntrnoInfo.getGridParam('selarrrow');
        var uids = "";
        if (ids == "") {
            //action_005=請先選取一筆以上之資料列
            return CommonAPI.showErrorMessage(i18n.def['action_005']);
        }
        for (var i in ids) {
            if (uids == "") {
                uids = $gridviewCntrnoInfo.getRowData(ids[i]).uid;
            }
            else {
                uids = uids + "," + $gridviewCntrnoInfo.getRowData(ids[i]).uid;
            }
            
        }
        
        
        //先確認整批勾選列印的額度動用資訊是否都有聯貸參貸比率
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "chkAllPrintHasL161S01B",
                mainId: responseJSON.mainId,
                mainOid: $("#mainOid").val(),
                uids: uids
            }
        }).done(
            function(obj){
                $.form.submit({
                    url: "../../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        type: "R02",
                        mainId: responseJSON.mainId,
                        mainOid: $("#mainOid").val(),
                        fileDownloadName: "lms1605r02.pdf",
                        serviceName: "lms1605r02rptservice",
                        uids: uids
                    }
                });
            }
        );
        
        
    });
    
    
    //新增聯貸案參貸比率一覽表
    $("#openCntrnoBox").click(function(){
        var id = $("#gridviewCntrnoInfo").getGridParam('selrow');
        if (!id) {
        
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
            
        }
        var result = $("#gridviewCntrnoInfo").getRowData(id);
        cntrnoInfoBox(null, null, result);
    });
    
    //登錄銀行
    $("#includeBranch").click(function(){
        //初始化選項
        $("#includeBranchBox [id^=selBank]").hide();
        $("#selBank").show();
        $("#includeBranchForm").reset();
        
        $("#includeBranchBox").thickbox({
            //L160M01A.slBank=參貸行庫/分行
            title: i18n.lms1605m01['L160M01A.slBank'],
            width: 650,
            height: 200,
            modal: true,
            align: "center",
            i18n: i18n.def,
            valign: "bottom",
            buttons: {
                "sure": function(){
                
                    //初始化畫面欄位
                    $("#slBankType,#slBranchCN,#slBranch,#slBank,#slBankCN").val("");
                    $("#showBranch,#showBranchCn").html("");
                    
                    //參貸行總類
                    var value = DOMPurify.sanitize($("#selBank").val());
                    //參貸銀行
                    var number = DOMPurify.sanitize($("#selBank" + value).val());
                    
                    var name = DOMPurify.sanitize($.trim($("#selBank" + value + " :selected").text().slice(5)));//取得參貸行名稱, ex:'321 - 日商三井住友銀行' >> '日商三井住友銀行'
                    
                    
                    if ($.trim(number) == "" || $.trim(value) == "") {
                        //grid.selrow=請先選擇一筆資料。
                        return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                    }
                    $("#slBank").val(number);
                    $("#slBankCN").val(name);
                    $("#showBranch").html(number + " " + name);
                    $("#slBankType").val(value);
                    switch (value) {
                        case "01":
                        case "02":
                            var numberCn = DOMPurify.sanitize($("#selBankOther" + value).val());
                            if ($.trim(numberCn) == "" && !$("#selBankOther" + value).is(":hidden")) {
                                //grid.selrow=請先選擇一筆資料。
                                return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                            }
                            var numberCnName = DOMPurify.sanitize($("#selBankOther" + value + " :selected").text());
                            $("#slBranch").val(numberCn);
                            if ("017" == number) {
                                $("#slBranchCN").val(numberCnName.slice(4));
                                $("#showBranchCn").html(numberCnName);
                            }
                            else {
                                if ($("#selBankOther" + value).is(":hidden")) {
                                    $("#showBranchCn").html("");
                                }
                                else {
                                    $("#showBranchCn").html(numberCn + " " + numberCnName.slice(8));
                                    $("#slBranchCN").val(numberCnName.slice(8));
                                }
                                
                            }
                            break;
                        case "03":
                        case "04":
                        case "05":
                        case "06":
                        case "07":
                        case "08":
                        case "09":
                        case "10":
                        case "11":
                            break;
                        case "12":
                        case "99":
                            $("#slBankCN").val($("#selBank" + value + " :selected").text().split(" - ")[1]);
                            $("#showBranch").html(DOMPurify.sanitize($("#selBank" + value + " :selected").text()));
                            break;
                        default:
                            //grid.selrow=請先選擇一筆資料。
                            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                            break;
                    }
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    
    function branchBox(cellvalue, options, rowData){
        var $L161M01BForm = $("#L161M01BForm");
        var $L161M01AForm = $("#L161M01AForm");
       
	   
        //初始化表單物件
        $L161M01BForm.reset().attr("formOid", "");
        $L161M01BForm.find("#showBranch,#showBranchCn").html("");
        
        var cntrNo = $L161M01AForm.find("#cntrNo").val();
        var uid = $L161M01AForm.find("#uid").val();
        
        if (rowData) {
            $.ajax({
                handler: "lms1605m01formhandler",
                data: {
                    formAction: "queryL161s01b",
                    oid: rowData.oid
                }
            }).done(
                function(obj){
                    $L161M01BForm.setData(obj);
                    $("#showBranch").html(DOMPurify.sanitize(obj.slBank + " " + obj.slBankCN));
                    $("#showBranchCn").html(DOMPurify.sanitize(obj.slBranch + " " + obj.slBranchCN));
                }
            );
        }
        $("#branchBox").thickbox({
        
            //L160M01A.title10=聯貸案參貸比率一覽表
            title: i18n.lms1605m01['L160M01A.title10'],
            width: 620,
            height: 280,
            modal: true,
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                    if (!$L161M01BForm.valid()) {
                        return;
                    }
                    var amt = $.trim($("#slAmt").val());
                    
                    
                    if ($.trim($("#slBankType").val()) == "") {
                    
                        //L160M01A.slBank=參貸行庫/分行  L160M01A.error7=請登錄
                        return CommonAPI.showErrorMessage(i18n.lms1605m01["L160M01A.error7"] + i18n.lms1605m01["L160M01A.slBank"]);
                    }
                    FormAction.open = true;
                    $.ajax({
                        handler: "lms1605m01formhandler",
                        data: {
                            formAction: "saveL161m01b",
							L161M01AForm:JSON.stringify($("#L161M01AForm").serializeData()),
							L161M01BForm:JSON.stringify($("#L161M01BForm").serializeData()),
                            oid: $L161M01BForm.attr("formOid"),
                            slCurr: $("#quotaCurr").val(),
                            cntrNo: cntrNo,
                            uid: uid,
                            totalAmt: $("#quotaAmt").val().replace(/,/g, "")
                        }
                    }).done(
                        function(obj){
                            FormAction.open = false;
                            $.thickbox.close();
                            $("#gridviewBranch").trigger('reloadGrid');
                        }
                    );
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
        
        
    }
     
    //引進連保人
    $("#includePeople").click(function(){
    
        //新增的主從債務人資料將被清空，是否繼續？【確定】(引進連保人資料)
        CommonAPI.confirmMessage(i18n.lms1605m01["L160M01A.message26"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1605m01formhandler",
                    data: {
                        formAction: "includeL140m01I"
                    }
                }).done(
                    function(obj){
                        $("#gridviewPeople").trigger('reloadGrid');
                    }
                );
            }
        });
    });
    
    //新增主從債務人資料表
    $("#openPeopleBox").click(function(){
        peopleBox();
    });
    
    //J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
    //信用品質順序設定
    $("#setGuarantorCreditPriority").click(function(){
    	setGuarantorSeq();
    });
    
    //刪除主從債務人資料表
    $("#deletePeople").click(function(){
        var $gridviewPeople = $("#gridviewPeople");
        var select = $gridviewPeople.jqGrid('getGridParam', 'selarrrow');
        if (select == "") {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = [];
                for (var i in select) {
                    data.push($gridviewPeople.getRowData(select[i]).oid);
                }
                
                $.ajax({
                    handler: "lms1605m01formhandler",
                    data: {
                        formAction: "deleteL162M01A",
                        oids: data
                    }
                }).done(
                    function(obj){
                        $gridviewPeople.trigger("reloadGrid");
                    }
                );
            }
            else {
                return;
            }
        });
    });
    
    
  //J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
    //整批引進最新資料
    $("#btnEntireApply").click(function(){
       //海外:lms1405s02_EntireApplyNew
		//國內:lms1401s02_EntireApplyNew
        var item = API.loadOrderCombosAsList("lms1601s03_EntireApplyNew")["lms1601s03_EntireApplyNew"];
		$("#entireApply").setItems({
			size: "1",
            item: convertItems(item),
			clear : true,
			itemType: 'checkbox' 
        })
 
        $("[name=entireApply]").prop("disabled", false).prop("checked", false);
		
		$("input[name='entireApply']").change(function(){
			
			var entireApply = $('input:checkbox:checked[name="entireApply"]').val();
			if(entireApply =="A01"){
				$("#showGuaNaExposure").show();
			}else{
				$("#showGuaNaExposure").hide();
			}  
		});	
		
		$("input[name='entireApply']").trigger("change");
 
        var $gridviewC_2 = $("#gridviewPeople");
        var ids = $gridviewC_2.getGridParam('selarrrow');
        var oids = [];
        if (ids == "") {
            //action_005=請先選取一筆以上之資料列
            return CommonAPI.showErrorMessage(i18n.def['action_005']);
        }

		
		$("#choiceEntireApply").thickbox({
		        //L162M01A.guaNaExposure=本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
		        title: i18n.lms1605m01["L162M01A.guaNaExposure"],
		        width: 600,
		        height: 200,
		        modal: true,
		        align: "center",
		        valign: "bottom",
		        readOnly: false,
		        i18n: i18n.def,
		        buttons: {
		            "sure": function(){
		            	
		            	var entireApply = $('input:checkbox:checked[name="entireApply"]').val();
				        var guaNaExposure = "";
				        if(entireApply == "A01"){
				       	 guaNaExposure = $("input[name='guaNaExposureTmp']:radio:checked" ).val(); 
				       	  
				   		 if ($.trim(guaNaExposure) == "") {
				                
				                //L162M01A.guaNaExposure=本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)  L160M01A.error7=請登錄
				                return CommonAPI.showErrorMessage(i18n.lms1605m01["L160M01A.error7"] + i18n.lms1605m01["L162M01A.guaNaExposure"]);
				            }
				       	  
				        }
					        
		            	$.thickbox.close();
		            	
		                 var allCheackedVal = [];
		                $.each($("#choiceEntireApply :checkbox[name=entireApply]:checked"), function(i, n){
		                    allCheackedVal[i] = $(n).val();
		                });
		                 
				        for (var i in ids) {
				            oids.push($gridviewC_2.getRowData(ids[i]).oid);
				        } 
	
						$.ajax({
				            async: false,
				            handler: "lms1605m01formhandler",
				            data: {
				                formAction: "entireApplyNew",
				                allCheackedVal: allCheackedVal.join("|"),
								oids: oids,
								guaNaExposure : guaNaExposure
				            }
				        }).done(
                            function(obj){
				                //runSuccess=執行成功
								$gridviewC_2.trigger("reloadGrid");
								CommonAPI.showMessage(i18n.def["runSuccess"]);
				            }
                        );
		            },
		            "cancel": function(){
		                $.thickbox.close();
		            }
		        }
		 });
    });
    
    //列印新增主從債務人資料表
    $("#printPeople").click(function(){
        var count = $("#gridviewPeople").jqGrid('getGridParam', 'records');
        
        //先檢查是否有資料
        if (count == 0) {
            //EFD0002=INFO|報表無資料|
            return CommonAPI.showErrorMessage(i18n.msg["EFD0002"]);
        }
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: $("#mainOid").val(),
                fileDownloadName: "lms1605r03.pdf",
                serviceName: "lms1605r03rptservice",
                type: "R03"
            }
        });
    });
    
	//G-113-0036 主從債務人
	 $("#ExcelDownload").click(function () {// 下載範本
		 $.form.submit({
		 	url: webroot + '/img/lms/L162S01AChkList.xls',
		    target: "_blank"
		 });
	 });
	 $("#ExcelImport").click(function () {// excel匯入
		 	$("#uploadDialog").thickbox({
		    	modal: false,
		        height: 200,
		        width: 555,
		        valign: 'bottom',
		        align: 'center',
		        buttons: API.createJSON([{
		            key: i18n.def.sure,
		            value: function () {
		               if ($("#filterForm").valid()) {
		                   var fileSize = 10 * 1024 * 1024;
		                   // 10MB
		                   $.capFileUpload({
		                	   handler: 'lms1601fileuploadhandler',
		                       fileElementId: "LMS1602S01",
		                       fileCheck: ['xls'],
		                       timeout: 5 * 60 * 1000,
		                       successMsg: false,
		                       data: {
		                    	   fileSize: fileSize,
								   fieldId: 'LMS1602S01',
		                           fileDesc: "",
		                           mainId: responseJSON.mainId
		                       },
		                       success: function (json) {
		                       		API.showPopMessage('', i18n.def['fileUploadSuccess'], function () {
		                       			$.thickbox.close();
		                       		});
		                       		$("#gridviewPeople").trigger("reloadGrid");
		                       }
		                	});
		                }
		            }
		        }, {
		        	key: i18n.def.cancel,
		            value: function () {
		            	$.thickbox.close()
		            }
		        }])
		 	});
		});
	 	//新增額度明細表聯行攤貸比率
	    $("#newItemChildren3Bt").click(function(){
	    	BranchAcitonAF.query();
	    });
	 	//刪除額度明細表聯行攤貸比率
	    $("#removeGridviewitemChildren3").click(function(){
	    	BranchAcitonAF.remove();
	    });
	    //變更分母
	    $("#changesShareRate2").click(function(){
	    	BranchAcitonAF.changesShareRate();
	    });
	    //變更攤貸行計算方式
	    $("[name=shareFlag]").click(function(){
	    	BranchAcitonAF.controlShow();
	    });
    //主從債務人視窗
    function peopleBox(cellvalue, options, rowData){
        var $L162M01AForm = $("#L162M01AForm");
        
        //初始化
        $L162M01AForm.reset().attr("formOid", "").find("#custId,#dupNo").html("");
        //初始化關係
        $("#the1,#the2,#the3").hide();
        $("[id^=rationSelect]").val("");
        $.ajax({
            handler: "lms1605m01formhandler",
            data: {
                formAction: "queryL162m01a",
                oid: rowData ? rowData.oid : "",
                mainId: responseJSON.mainId
            }
        }).done(
            function(obj){
                $("#rKindM").val('');
                $L162M01AForm.setData(obj);
                //設定額度序號是否可選 引進的不可選、新增的可
                if (obj.dataSrc == "1") {
                    $L162M01AForm.find("#cntrNo").prop("disabled", true).html("<option value=" + DOMPurify.sanitize(obj.cntrNo) + ">" + DOMPurify.sanitize(obj.cntrNo) + "</option>");
                    $L162M01AForm.find("#custIdSelect").prop("disabled", true);
                }
                else {
                    $L162M01AForm.find("#cntrNo").prop("disabled", false).html("<option value=''>" + DOMPurify.sanitize(i18n.def.comboSpace) + "</option>");
                    $L162M01AForm.find("#custIdSelect").prop("disabled", false);
                }
                
				//if($L162M01AForm.find("#rId").val() == ""){
					//J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
				$L162M01AForm.find("#rType").change();
				//}
				
                $("#custIdSelect").change();
                $("#rKindM").change();
                
                $("#cntrNo").children().each(function(){
                    //alert("cntrno="+$(this).text());
                    if ($(this).text() == obj.cntrNo) {
                        //jQuery給法
                        $(this).prop("selected", true); //或是給"selected"也可
                        //javascript給法
                        //this.selected = true;
                    
                    }
                });
                
                
                $("#rKindM").children().each(function(){
                    if (obj.rKindM == undefined || obj.rKindM == "") {
                    
                        if ($(this).val() == '') {
                            $(this).prop("selected", true); //或是給"selected"也可
                            $("#rKindM").change();
                        }
                    }
                    
                });
                
                
                //設定關係細項的值
                switch (obj.rKindM) {
                    case "1":
                        $("#rationSelect1").val(obj.rKindD);
                        $("#the1").show().siblings("#the2,#the3").hide();
                        break;
                    case "2":
                        $("#rationSelect2").val(obj.rKindD);
                        $("#the2").show().siblings("#the1,#the3").hide();
                        break;
                    case "3":
                        $("#rationSelect31").val(obj.rKindD.charAt(0));
                        $("#rationSelect32").val(obj.rKindD.charAt(1));
                        $("#the1,#the2").hide().siblings("#the3").show();
                        break;
                }
                
                //J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
                //$("#rType").change();
                
				//J-106-0029-004  洗錢防制-動審表新增洗錢防制頁籤
				if (responseJSON.readOnly == "true") {		            
					$L162M01AForm.find(".noHideBt").show();
		        }
				//G-113-0036 新增 保證金額上限、當地客戶識別ID，海外分行才能填
				if(!$.isEmptyObject(obj.banktype)){
	            	if(obj.banktype == "5"){
	        			$(".trGrtAmt").show();
	        		}else{
	        			$(".trGrtAmt").hide();
	        			$("#grtAmt").val("");
	        			$("#localId").val("");
	        		}
				}
                toPeopleBox();
                
                
            }
        );
    }//close 開啟主從債務人資料表畫面
    function toPeopleBox(){
        var $L162M01AForm = $("#L162M01AForm");
        $("#peopleBox").thickbox({
            //L160M01A.title11=主從債務人資料表
            title: i18n.lms1605m01['L160M01A.title11'],
            width: 960,
            height: 500,   //J-106-0029
            modal: true,
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                	var mainCustId = $L162M01AForm.find("#custIdSelect").val();
                    $L162M01AForm.find("#custId").val(mainCustId.substring(0, mainCustId.length - 1));
                    $L162M01AForm.find("#dupNo").val(mainCustId.substring(mainCustId.length - 1, mainCustId.length));
                    if (!$L162M01AForm.valid()) {
                        return;
                    }
                    
                    var rKindD = "";
                    var rKindM = $("#rKindM").val();
                    //針對關係細項作處理
                    switch (rKindM) {
                        case "1":
                            rKindD = $("#rationSelect1").val();
                            break;
                        case "2":
                            rKindD = $("#rationSelect2").val();
                            break;
                        case "3":
                            //選擇關係兩個都要選
                            if ($("#rationSelect31").val() == "" || $("#rationSelect32").val() == "") {
                                return CommonAPI.showErrorMessage(i18n.lms1605m01["L160M01A.error2"] + i18n.lms1605m01['L162M01A.rKindM']);
                            }
                            rKindD = $("#rationSelect31").val() + $("#rationSelect32").val();
                            break;
                    }
                    
                    if ($.trim(rKindD) == "" && rKindM != "") {
                        return CommonAPI.showErrorMessage(i18n.lms1605m01["L160M01A.error2"] + i18n.lms1605m01['L162M01A.rKindM']);
                    }
                    
                    //檢查國別不可以為空	
                    if ($("#rCountry").val() == "") {
                    
                        return CommonAPI.showErrorMessage(i18n.lms1605m01["L162M01A.rCountry"] + i18n.def['val.required']);
                    }
                    
                    var custId = $L162M01AForm.find("#custId").val(), dupNo = $L162M01AForm.find("#dupNo").val(), rId = $L162M01AForm.find("#rId").val(), rDupNo = $L162M01AForm.find("#rDupNo").val();
                    //當只有主債務人統編與從債務人統編相同者，其關係可為空白，其餘欄位皆不可空白	
                    if (!(custId == rId && dupNo == rDupNo)) {
                        if ($.trim(rKindD) == "" && $.trim(rKindM) == "") {
                            return CommonAPI.showErrorMessage(i18n.lms1605m01['L162M01A.rKindM'] + i18n.def['val.required']);
                        }
                    }
                    else {
                        if ($L162M01AForm.find("#rType").val() != "C") {
                            //L160M01A.message48=主債務人統編與從債務人統編相同者，其相關身份應為 C、共同借款人	
                            return CommonAPI.showErrorMessage(i18n.lms1605m01['L160M01A.message48']);
                        }
                        
                        if ($.trim(rKindD) != "" || $.trim(rKindM) != "") {
                            //L160M01A.message50=主債務人統編與從債務人統編相同者，其關係應為空白
                            return CommonAPI.showErrorMessage(i18n.lms1605m01['L160M01A.message50']);
                        }
                        
                    }
                    
                    $.ajax({
                        handler: "lms1605m01formhandler",
                        data: {
                            formAction: "saveL162m01a",
                            oid: $L162M01AForm.attr("formOid"),
                            rKindD: rKindD,
                            L162M01AForm: JSON.stringify($L162M01AForm.serializeData())
                        }
                    }).done(
                        function(obj){
                        
                        	if($L162M01AForm.attr("formOid") == ""){
                        		if(obj.formOid){
                        			$L162M01AForm.attr("formOid",obj.formOid);
                        		}
                        		
                        	}
                        	
                            var rType = $L162M01AForm.find("#rType").val();
                            var dueDate = $L162M01AForm.find("#dueDate").val();
                            if ((rType == "N" || rType == "G") && dueDate == "") {
                                //L160M01A.message62=本訊息僅提醒:配合民法第753條之1條訂，保證人若為「董事、監察人或其他有代表權之人而為該法人擔任保證人」者，必須輸入【董監事任期止日 西元年YYYY-MM-DD
                                API.showMessage(i18n.lms1605m01["L160M01A.message62"]);
                            }
                            
                            //saveSuccess=儲存成功
                            API.showMessage(i18n.def["saveSuccess"]);
                           
                            $("#gridviewPeople").trigger('reloadGrid');
                            
                        }
                    );
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        if (auth.readOnly) {
            $("button#saveData").remove();
        }
    }
    
    
    
    var gridviewCntrnoInfo = $("#gridviewCntrnoInfo").iGrid({
        needPager: false,
        handler: 'lms1605gridhandler',
        height: "190px",
        width: "100%",
        multiselect: true,
        sortname: 'printSeq|custId|cntrNo',
        sortorder: 'asc|asc|asc',
        postData: {
            formAction: "queryCntrnoInfo"
        },
        colModel: [{
            colHeader: i18n.lms1605m01['L160M01A.custId'],// "借戶統編",
            name: 'custId',
            width: '80px',
            sortable: true,
            align: "left",
            formatter: 'click',
            onclick: cntrnoInfoBox
        }, {
            colHeader: i18n.lms1605m01['L160M01A.cntrNo'],// "額度序號",
            name: 'cntrNo',
            width: '80px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.currentApplyCurr'],// "幣別",
            name: 'currentApplyCurr',
            width: '40px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.currentApplyAmt'],// "現請額度（元）",
            name: 'currentApplyAmt',
            width: '80px',
            sortable: true,
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2//小數點到第幾位
            }
        }, {
            colHeader: i18n.lms1605m01['L160M01A.propertyDscr'],// "額度性質",
            name: 'propertyDscr',
            width: '100px',
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.snoKindDscr'],// "額度控管種類",
            name: 'snoKindDscr',
            width: '100px',
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.caseTypeDscr'],// "案件性質",
            name: 'caseTypeDscr',
            width: '100px',
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.quotaCurr'],// "聯貸幣別",
            name: 'quotaCurr',
            width: '60px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.quotaAmtDscr'],// "聯貸總額度（元）",
            name: 'quotaAmtDscr',
            width: '80px',
            sortable: true,
            align: "left"
        }, {
            colHeader: "&nbsp",//"檢核欄位",
            name: 'chkYN',
            width: 20,
            sortable: true,
            align: "center"
        }, {
            name: 'printSeq',
            hidden: true
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'uid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = gridviewCntrnoInfo.getRowData(rowid);
            cntrnoInfoBox(null, null, data);
        }
    });
    
    function cntrnoInfoBox(cellvalue, options, rowData){
        var $L161M01AForm = $("#L161M01AForm");
        
        //初始化表單物件
        $L161M01AForm.reset();
        
        if (rowData) {
            $.ajax({
                handler: "lms1605m01formhandler",
                data: {
                    formAction: "queryL161s01a",
                    oid: rowData.oid
                }
            }).done(
                function(obj){
                    $L161M01AForm.setData(obj);
                    $L161M01AForm.find("input[name=unitCase]:checked").trigger("click");
                    $L161M01AForm.find("input[name=unitMega]:checked").trigger("click");
                    $L161M01AForm.find("#coKind").trigger("change");
                    
					//J-103-0202-005 
					//遠匯換匯比照選擇權，以交易額度(名目本金*風險係數)來簽案
					var showDervApplyAmtType = obj.isDerivatives;
				    if (showDervApplyAmtType == "Y") {
				       $L161M01AForm.find("#showDervApplyAmtType").show();
				    }
				    else {
				       $L161M01AForm.find("#showDervApplyAmtType").hide();
				    }
					
                    var cntrNo = $L161M01AForm.find("#cntrNo").val();
                    var uid = $L161M01AForm.find("#uid").val();
                    $("#gridviewBranch").jqGrid("setGridParam", {
                        postData: {
                            formAction: "queryBranch",
                            cntrNo: cntrNo,
                            uid: uid
                        }
                    }).trigger("reloadGrid");
                    
                    // G-113-0036 簽案額度明細聯行攤貸比例
					$(BranchAcitonAF.gridId).jqGrid("setGridParam",{
						postData : {
							formAction : "queryL140m01e_af",
							cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
							cntrNo : $("#L161M01AForm").find("#cntrNo").val()
						}
					}).trigger("reloadGrid");
					$(BranchAcitonAF.amtgrId).jqGrid("setGridParam",{
						postData : {
							formAction : "queryL140m01e_af",
							cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
							cntrNo : $("#L161M01AForm").find("#cntrNo").val()
						}
					}).trigger("reloadGrid");
                    
                    var docCode = $("#docCode").val();
                    //當分行代號為025時，要出現#branchShow025_1 #branchShow025_2的li
                    var brNo = userInfo ? userInfo.unitNo : "";
                    if (brNo == "025" || brNo == "900") {
                        $L161M01AForm.find("#branchShow025_1,#branchShow025_2").show();
                    }
                    $("#cntrnoInfoBox").thickbox({
                        //L160M01A.title10=額度動用資訊一覽表
                        title: i18n.lms1605m01['L160M01A.title10'],
                        width: 800,
                        height: 500,
                        modal: true,
                        i18n: i18n.def,
                        buttons: {
                            "saveData": function(){
                                if (!$L161M01AForm.valid()) {
                                
                                    $("input.data-error,select.data-error").eq(0).focus();
                                    
                                    return;
                                }
                                
                                var cntrNo = $L161M01AForm.find("#cntrNo").val();
                                var uid = $L161M01AForm.find("#uid").val();
                                FormAction.open = true;
                                $.ajax({
                                    handler: "lms1605m01formhandler",
                                    data: {
                                        formAction: "saveL161s01a",
                                        mainId: responseJSON.mainId,
                                        cntrNo: cntrNo,
                                        uid: uid
                                    }
                                }).done(
                                    function(obj){
                                        FormAction.open = false;
                                        if (obj.l140m01eAmt != undefined && obj.l140m01eAmt != 0) {
						                	//當攤貸時還有餘額，要跳出餘額修改視窗
						                    if (obj.l140m01eAmt != 0) {
						                       	BranchAcitonAF.l140m01eAmtBox(obj.l140m01eAmt);
						                    }
						                }
                                        if (obj.errorMsg != "") {
                                            CommonAPI.showErrorMessage(obj.errorMsg);
                                        }
                                        if (obj.suggestMsg != "") {
                                            CommonAPI.showErrorMessage(obj.suggestMsg);
                                        }

                                        $("#gridviewCntrnoInfo").trigger("reloadGrid");
                                        
                                        if (obj.errorMsg == "") {
                                            API.showMessage(i18n.def["saveSuccess"]);
                                        }
                                    }
                                );
                            },
                            "close": function(){
                                $.thickbox.close();
                            }
                        }
                    });
                }
            );
        }
        else {
            CommonAPI.showErrorMessage(i18n.lms1605m01["L160M01A.message64"]);
            
        }
        
        
        
        
    }
    
    var copyL161S01BGrid = $("#copyL161S01BGrid").iGrid({
        needPager: false,
        localFirst: true,
        handler: 'lms1605gridhandler',
        height: "190px",
        width: "100%",
        multiselect: false,
        sortname: 'printSeq|custId|cntrNo',
        sortorder: 'asc|asc|asc',
        postData: {
            formAction: "queryCntrnoInfo"
        },
        colModel: [{
            colHeader: i18n.lms1605m01['L160M01A.custId'],// "借戶統編",
            name: 'custId',
            width: '80px',
            sortable: true,
            align: "left",
            formatter: 'click'
        }, {
            colHeader: i18n.lms1605m01['L160M01A.cntrNo'],// "額度序號",
            name: 'cntrNo',
            width: '80px',
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.snoKindDscr'],// "額度控管種類",
            name: 'snoKindDscr',
            width: '150px',
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.caseTypeDscr'],// "案件性質",
            name: 'caseTypeDscr',
            width: '150px',
            sortable: true,
            align: "left"
        }, {
            colHeader: "&nbsp",//"檢核欄位",
            name: 'chkYN',
            width: 20,
            sortable: true,
            align: "center"
        }, {
            name: 'printSeq',
            hidden: true
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'uid',
            hidden: true
        }]
    });
    
    function copyBranchBox(){
        //TODO
        
        var $copyL161S01BGrid = $("#copyL161S01BGrid");
        var $L161M01AForm = $("#L161M01AForm");
        $copyL161S01BGrid.trigger("reloadGrid");
        
        $("#selectCntrnoInfoBox").thickbox({
            //L160M01A.title10=額度動用資訊一覽表
            title: i18n.lms1605m01['L160M01A.title10'],
            width: 600,
            height: 400,
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var row1 = $copyL161S01BGrid.getGridParam('selrow');
                    
                    if (row1 == "") {
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showErrorMessage(i18n.def['action_005']);
                    }
                    
                    var data1 = $copyL161S01BGrid.getRowData(row1);
                    var copyFromUid = data1.uid;
                    
                    var copyToUid = $L161M01AForm.find("#uid").val();
                    
                    FormAction.open = true;
                    $.ajax({
                        handler: "lms1605m01formhandler",
                        data: {
                            formAction: "copyL161s01b",
                            mainId: responseJSON.mainId,
                            copyToUid: copyToUid,
                            copyFromUid: copyFromUid
                        }
                    }).done(
                        function(obj){
                            FormAction.open = false;
                            $("#gridviewBranch").trigger("reloadGrid");
                            $.thickbox.close();
                        }
                    );
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    //J-106-0029-003  洗錢防制-新增實質受益人
	/** 關係人-自然人  */
	$("#gridviewNatural").iGrid({
        height: 200,
        handler: "lms1201gridhandler",
        localFirst: true,
        sortname: 'seqNum|createTime',
        sortorder: 'asc|asc',
        postData: {
            formAction: "queryL120s01p",
            mainId: responseJSON.mainId,
            type: "1"
        },
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lms1605m01["L120S01p.custIdAndDupNo"],//"統編(含重覆序號)"
            width: 80,
            name: 'rId',
            sortable: true,
            formatter: 'click',
            onclick: newToglePersonBT
        }, {
            colHeader: i18n.lms1605m01["L120S01p.conPersonName"],//"名稱",
            name: 'rName',
            width: 100,
            sortable: false
        }, {
            colHeader: i18n.lms1605m01["L120S01p.conPersonEName"],//"英文名稱",
            name: 'rEName',
            width: 100,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "rType",
            name: 'rType',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewNatural").getRowData(rowid);
            newToglePersonBT(null, null, data);
        }
    });


	//J-106-0029-003  洗錢防制-新增實質受益人 
	/** 關係人-法人 */
	$("#gridviewCorporate").iGrid({
        height: 200,
        handler: "lms1201gridhandler",
        localFirst: true,
        sortname: 'seqNum|createTime',
        sortorder: 'asc|asc',
        postData: {
            formAction: "queryL120s01p",
            mainId: responseJSON.mainId,
            type: "2"
        },
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lms1605m01["L120S01p.custIdAndDupNo"],//"統編(含重覆序號)"
            width: 80,
            name: 'rId',
            sortable: true,
            formatter: 'click',
            onclick: newToglePersonBT
        }, {
            colHeader: i18n.lms1605m01["L120S01p.conPersonName"],//"名稱",
            name: 'rName',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01["L120S01p.conPersonEName"],//"英文名稱",
            name: 'rEName',
            width: 100,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "rType",
            name: 'rType',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewCorporate").getRowData(rowid);
            newToglePersonBT(null, null, data);
        }
    });

    //J-106-0029-003  洗錢防制-新增實質受益人
    //自動帶入使用者姓名
    $("#L120S01PForm").find("#rId,#rDupNo").blur(function(){
        var custId = $("#L120S01PForm").find("#rId").val();
        var dupNo = $("#L120S01PForm").find("#rDupNo").val();
        if ($.trim(custId).length > 0 && $.trim(dupNo).length > 0) {
            $.ajax({
                handler: "amlrelateformhandler",
                action: "getMisCustData",
                data: {
                    custId: custId,
                    dupNo: dupNo
                }
            }).done(
                function(obj){
                    if (!$.isEmptyObject(obj)) {
                        $("#L120S01PForm").find("#rName").val(obj.custName);
						$("#L120S01PForm").find("#rEName").val(obj.custEName);
                    }
                }
            );
        }
    });
    
	//J-106-0029-003  洗錢防制-新增實質受益人
	/** 登錄實質受益人  */
	$("#L162M01AForm").find("#toglePersonBT").click(function(){
	    var rType = "7";  //實質受益人
	    var $L162M01AForm = $("#L162M01AForm");
	    
		if (!$L162M01AForm.valid()) {
            return;
        }
		
	    //讓每次開起box都是第一頁
	    $("#toglePersonTabs").tabs({
	        selected: 0
	    });
	    
	    $("#gridviewNatural").jqGrid("setGridParam", {//重新設定grid需要查到的資料
	        postData: {
	            formAction: "queryL120s01p",
	            mainId: responseJSON.mainId,
	            type: "1",
	            custId: $L162M01AForm.find("#rId").val(),
	            dupNo: $L162M01AForm.find("#rDupNo").val(),
	            rType: rType
	        },
	        search: true,
	        loadComplete: function(){
	        
	            $("#gridviewCorporate").jqGrid("setGridParam", {//重新設定grid需要查到的資料
	                postData: {
	                    formAction: "queryL120s01p",
	                    mainId: responseJSON.mainId,
	                    type: "2",
	                    custId: $L162M01AForm.find("#rId").val(),
	            		dupNo: $L162M01AForm.find("#rDupNo").val(),
	                    rType: rType
	                },
	                search: true,
	                loadComplete: function(){
	                
	                    $('#gridviewNatural').jqGrid('setGridParam', {
	                        loadComplete: function(){
	                            //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
	                        }
	                    });
	                    $('#gridviewCorporate').jqGrid('setGridParam', {
	                        loadComplete: function(){
	                            //執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
	                        }
	                    });
	                    
						 
	                    var btnAction = API.createJSON([{
							key: i18n.def['close'],
                            value: function(){
                                $.thickbox.close();
                            }
					    }]);
	                     
						
	                    if (responseJSON.readOnly != "true") {
							
							if(userInfo.ssoUnitNo=="900"){
								btnAction = API.createJSON([{
		                            //引進
		                            key: i18n.lms1605m01['L120S01p.btnApply'],
		                            value: function(){
										
										var countN=$("#gridviewNatural").jqGrid('getGridParam','records');
										var countC=$("#gridviewCorporate").jqGrid('getGridParam','records');
		                                if (countN+countC  > 0) {
										    //confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
											CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
												if (b) {					
													//是的function
													$.ajax({
					                                    handler: "amlrelateformhandler",
					                                    data: {//把資料轉成json
					                                        formAction: "applyBeneficiaryData",
					                                        mainId: responseJSON.mainId,
					                                        custId: $L162M01AForm.find("#rId").val(),
	            											dupNo: $L162M01AForm.find("#rDupNo").val()
					                                    }
					                                }).done(
                                                        function(responseData){
															$('#gridviewNatural').trigger('reloadGrid');
					                                        $('#gridviewCorporate').trigger('reloadGrid');
															if(responseData.errMsg){
														        return CommonAPI.showMessage(responseData.errMsg);
													        }
					                                    }
                                                    );	
												}				
											});		
										}else{
											$.ajax({
			                                    handler: "amlrelateformhandler",
			                                    data: {//把資料轉成json
			                                        formAction: "applyBeneficiaryData",
			                                        mainId: responseJSON.mainId,
			                                        custId: $L162M01AForm.find("#rId").val(),
	            									dupNo: $L162M01AForm.find("#rDupNo").val()
			                                    }
			                                }).done(
                                                function(responseData){
													$('#gridviewNatural').trigger('reloadGrid');
			                                        $('#gridviewCorporate').trigger('reloadGrid');
													if(responseData.errMsg){
												        return CommonAPI.showMessage(responseData.errMsg);
											        }
			                                    }
                                            );
										}
		                                
		                            }
									//只能引進0024
		                        }, {
		                            key: i18n.def['newData'],
		                            value: function(){
		                                newToglePersonBT(null, null, null, rType);
		                            }
		                        }, {
		                            key: i18n.def['del'],
		                            value: function(){
		                            
		                                var id1 = $("#gridviewNatural").getGridParam('selarrrow');
		                                var id2 = $("#gridviewCorporate").getGridParam('selarrrow');
		                                var data1 = [];
		                                var data2 = [];
		                                if (id1.length == 0 && id2.length == 0) {
		                                    //TMMDeleteError=請先選擇需修改(刪除)之資料列
		                                    return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
		                                }
		                                
		                                //confirmDelete=是否確定刪除?
		                                CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
		                                    if (b) {
		                                        if (id1.length > 0) {
		                                            for (var i = 0; i < id1.length; i++) {
		                                                data1[i] = $("#gridviewNatural").getRowData(id1[i]).oid;
		                                            }
		                                        }
		                                        
		                                        if (id2.length > 0) {
		                                            for (var i = 0; i < id2.length; i++) {
		                                                data2[i] = $("#gridviewCorporate").getRowData(id2[i]).oid;
		                                            }
		                                        }
		                                        
		                                        $.ajax({
		                                            handler: "amlrelateformhandler",
		                                            data: {//把資料轉成json
		                                                formAction: "deleteL120s01p",
		                                                oids: data1.concat(data2),
		                                                mainId: responseJSON.mainId,
		                                                showMsg: true
		                                            }
		                                        }).done(
                                                    function(responseData){
		                                                $('#gridviewNatural').trigger('reloadGrid');
		                                                $('#gridviewCorporate').trigger('reloadGrid');
		                                            }
                                                );
		                                    }
		                                });
		                            }
		                        }, {
		                            key: i18n.def['close'],
		                            value: function(){
		                            
		                            
		                                //關閉的時候將相關人寫到畫面上 guarantor
		                                
		                                var corpId = $("#gridviewCorporate").jqGrid('getDataIDs');
		                                var natId = $("#gridviewNatural").jqGrid('getDataIDs');
		                                var data = "";
		                                var sign;
		                                var guaIndex = 0;
		                                var needIndex = false;
		                                if (natId.length + corpId.length > 1) {
		                                    needIndex = true;
		                                }
		                                
		                                for (var i = 0; i < natId.length; i++) {
		                                    (data.length > 0) ? sign = "、" : sign = "";
		                                    guaIndex = guaIndex + 1;
		                                    
		                                    if (needIndex == true) {
		                                        data = data + sign + guaIndex + "." + $("#gridviewNatural").getRowData(natId[i]).rName;//+ guaPercentStr;
		                                    }
		                                    else {
		                                        data = data + sign + $("#gridviewNatural").getRowData(natId[i]).rName;//+ guaPercentStr;
		                                    }
		                                }
		                                for (var i = 0; i < corpId.length; i++) {
		                                    (data.length > 0) ? sign = "、" : sign = "";
		                                    guaIndex = guaIndex + 1;
		                                    
		                                    if (needIndex == true) {
		                                        data = data + sign + guaIndex + "." + $("#gridviewCorporate").getRowData(corpId[i]).rName;//+ guaPercentStr;
		                                    }
		                                    else {
		                                        data = data + sign + $("#gridviewCorporate").getRowData(corpId[i]).rName;//+ guaPercentStr;
		                                    }
		                                }
		                                
		                                //J-109-0067_05097_B1001 Web e-Loan 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
//		                                if (!data || $.trim(data) == "") {
//		                                    data = i18n.lms1605m01['nohave']
//		                                }
		                                
		                                $.ajax({
		                                    handler: "amlrelateformhandler",
		                                    action: "saveL120s01pBeneficiaryStr",
		                                    data: {
		                                        mainId: responseJSON.mainId,
		                                       	custId: $L162M01AForm.find("#rId").val(),
	            								dupNo: $L162M01AForm.find("#rDupNo").val(),
		                                        beneficiary: data,
		                                        rType: rType,
												callFrom:"2" //動審表
		                                    }
		                                }).done(
                                            function(obj){
		                                    	//J-109-0067_05097_B1001 Web e-Loan 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
		                                        //$("#beneficiary").val(data);
		                                    	$("#beneficiary").val(obj.beneficiary);
		                                        $.thickbox.close();
		                                    }
                                        );
		                            }
		                        }]);
							}else{
								//一般分行只能引進0024
								btnAction = API.createJSON([{
		                            //引進
		                            key: i18n.lms1605m01['L120S01p.btnApply'],
		                            value: function(){
										
										var countN=$("#gridviewNatural").jqGrid('getGridParam','records');
										var countC=$("#gridviewCorporate").jqGrid('getGridParam','records');
		                                if (countN+countC  > 0) {
										    //confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
											CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
												if (b) {					
													//是的function
													$.ajax({
					                                    handler: "amlrelateformhandler",
					                                    data: {//把資料轉成json
					                                        formAction: "applyBeneficiaryData",
					                                        mainId: responseJSON.mainId,
					                                        custId: $L162M01AForm.find("#rId").val(),
	            											dupNo: $L162M01AForm.find("#rDupNo").val()
					                                    }
					                                }).done(
                                                        function(responseData){
															$('#gridviewNatural').trigger('reloadGrid');
					                                        $('#gridviewCorporate').trigger('reloadGrid');
															if(responseData.errMsg){
														        return CommonAPI.showMessage(responseData.errMsg);
													        }
					                                    }
                                                    );	
												}				
											});		
										}else{
											$.ajax({
			                                    handler: "amlrelateformhandler",
			                                    data: {//把資料轉成json
			                                        formAction: "applyBeneficiaryData",
			                                        mainId: responseJSON.mainId,
			                                        custId: $L162M01AForm.find("#rId").val(),
	            									dupNo: $L162M01AForm.find("#rDupNo").val()
			                                    }
			                                }).done(
                                                function(responseData){
													$('#gridviewNatural').trigger('reloadGrid');
			                                        $('#gridviewCorporate').trigger('reloadGrid');
													if(responseData.errMsg){
												        return CommonAPI.showMessage(responseData.errMsg);
											        }
			                                    }
                                            );
										}
		                                
		                            }
									
		                        }, {
		                            key: i18n.def['close'],
		                            value: function(){
		                            
		                            
		                                //關閉的時候將相關人寫到畫面上 guarantor
		                                
		                                var corpId = $("#gridviewCorporate").jqGrid('getDataIDs');
		                                var natId = $("#gridviewNatural").jqGrid('getDataIDs');
		                                var data = "";
		                                var sign;
		                                var guaIndex = 0;
		                                var needIndex = false;
		                                if (natId.length + corpId.length > 1) {
		                                    needIndex = true;
		                                }
		                                
		                                for (var i = 0; i < natId.length; i++) {
		                                    (data.length > 0) ? sign = "、" : sign = "";
		                                    guaIndex = guaIndex + 1;
		                                    
		                                    if (needIndex == true) {
		                                        data = data + sign + guaIndex + "." + $("#gridviewNatural").getRowData(natId[i]).rName;//+ guaPercentStr;
		                                    }
		                                    else {
		                                        data = data + sign + $("#gridviewNatural").getRowData(natId[i]).rName;//+ guaPercentStr;
		                                    }
		                                }
		                                for (var i = 0; i < corpId.length; i++) {
		                                    (data.length > 0) ? sign = "、" : sign = "";
		                                    guaIndex = guaIndex + 1;
		                                    
		                                    if (needIndex == true) {
		                                        data = data + sign + guaIndex + "." + $("#gridviewCorporate").getRowData(corpId[i]).rName;//+ guaPercentStr;
		                                    }
		                                    else {
		                                        data = data + sign + $("#gridviewCorporate").getRowData(corpId[i]).rName;//+ guaPercentStr;
		                                    }
		                                }
		                                
		                                //J-109-0067_05097_B1001 Web e-Loan 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
//		                                if (!data || $.trim(data) == "") {
//		                                    data = i18n.lms1605m01['nohave']
//		                                }
		                                
		                                $.ajax({
		                                    handler: "amlrelateformhandler",
		                                    action: "saveL120s01pBeneficiaryStr",
		                                    data: {
		                                        mainId: responseJSON.mainId,
		                                        custId: $L162M01AForm.find("#rId").val(),
	            								dupNo: $L162M01AForm.find("#rDupNo").val(),
		                                        beneficiary: data,
		                                        rType: rType,
												callFrom:"2" //動審表
		                                    }
	                                    }).done(
                                            function(obj){
		                                        //J-109-0067_05097_B1001 Web e-Loan 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
		                                    	//$("#beneficiary").val(data);
		                                    	$("#beneficiary").val(obj.beneficiary);
		                                        $.thickbox.close();
		                                    }
                                        );
	                            }
	                        }]);
							}
							
	                    }
						
						 
	                    $("#toglePersonBox").thickbox({ // 使用選取的內容進行彈窗
	                        title: i18n.lms1605m01["other.login"]+i18n.lms1605m01["L120S01p.beneficiary"],//'other.login=登錄 ',  L120S01p.conPersonNew
	                        width: 700,
	                        height: 410,
	                        readOnly: false,
	                        modal: true,
	                        buttons: btnAction
	                    });
	                    
	                }
	            }).trigger("reloadGrid");
	        }
	    }).trigger("reloadGrid");
	    
	});
    
});


function changeSyndicationLoan(obj){

    var objValue1 = $('input[name=unitCase]:checked').val();
    var objValue2 = $('input[name=unitMega]:checked').val();
    
    if (objValue1 == "Y") { //|| objValue2 == "Y"
        if (objValue1 == "Y") {
            $('#SyndicationLoan1').show();
            $('#unitCase2Div').show();
            $('#SyndicationLoanAddDate').show();
            $('#SyndicationLoanAddDate1').show();
        }
        else {
            $('#SyndicationLoan1').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
            $('#unitCase2Div').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
        }
        $('#SyndicationLoan2').show();
        $('#SyndicationLoanAddDate').show();
        $('#SyndicationLoanAddDate1').show();
    }
    else {
        if (objValue1 == "N") { //&& objValue2 == "N"
            //$('#SyndicationLoan1').hide();
            //$('#SyndicationLoan2').hide();
            $('#SyndicationLoan1').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked 
            $('#SyndicationLoan2').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
            $('#SyndicationLoanAddDate').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
            $('#SyndicationLoanAddDate1').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked
        }
    }
    
    if (objValue1 == "Y" || objValue2 == "Y") {
        $('#lms160s0301Tab02').show();
        $('#lms160s0301B').show();
    }
    else {
        $('#lms160s0301Tab02').hide();
        $('#lms160s0301B').hide();
    }
    
    
    //
    var objValue = $(obj).val();
    var objName = $(obj).attr('name');
    var uCntBranch = $('input[name=uCntBranch]:checked').val();
    var unitCase = $('input[name=unitCase]:checked').val();
    
    //if (objName == "uCntBranch" || objName == "unitCase") {
    
    if (uCntBranch == "Y" && unitCase == "Y") {
        $('#SyndicationLoan2_mgr').show();
    }
    else {
        $('#SyndicationLoan2_mgr').hide();
    }
    //}
}

function changeItem(obj){
    var objValue = $(obj).val();
    var objName = $(obj).attr('name');
    if (objName == "mCntrt") {
        if (objValue == "Y") {
            $('#coKind_parent').hide().find("[name=sCntrt]:radio").prop("checked", false);//移除隱藏選項的radio checked ;
            $('#coKind_son').hide().find(":text").val("");//清除隱藏選項的內容;
        }
        else {
            $('#coKind_parent').show();
        }
    }
    
    if (objName == "sCntrt") {
        if (objValue == "Y") {
            $('#coKind_son').show();
        }
        else {
            $('#coKind_son').hide().find(":text").val("");//清除隱藏選項的內容;
        }
    }
    if (objName == "coKind" && objValue == "0") {
        $('#coKindSpan').hide().find(":radio").prop("checked", false);//移除隱藏選項的radio checked ;;
    }
    else {
        if (objName == "coKind") {
            $('#coKindSpan').show();
            $('#coKind_1,#coKind_2,#coKind_3,#coKind_4').html(DOMPurify.sanitize($("[name=coKind]").find(":selected").text()));//合作業務種類
        }
    }
    
    
    
}

function applyGist(){

    var $L161M01AForm = $("#L161M01AForm");
    var cntrNo = $L161M01AForm.find("#cntrNo").val();
    var uid = $L161M01AForm.find("#uid").val();
    $.ajax({
        handler: "lms1605m01formhandler",
        data: {
            formAction: "applyGist",
            mainId: responseJSON.mainId,
            cntrNo: cntrNo,
            uid: uid
        }
    }).done(
        function(obj){
            $L161M01AForm.find("#gist").val(obj.gist);
        }
    );
}


//J-106-0029-003  洗錢防制-新增實質受益人
function newToglePersonBT(callValue, setting, data, rType){//新增連保人
    //先將表格初始化
    $("#L120S01PForm").reset();
    
    //J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
    var thisRType = "";
    if (data && data.rType) {
        thisRType = data.rType;
    }
    else {
        thisRType = rType;
    }
    
    var $L162M01AForm = $("#L162M01AForm");
    
    
    $.ajax({
        handler: "amlrelateformhandler",
        data: {//把資料轉成json
            formAction: "queryL120s01p",
            oid: (data && data.oid) || "",
            showMsg: true,
            rType: thisRType
        }
    }).done(
        function(obj){
            $('#L120S01PForm').injectData(obj);
            
            if (!$('#L120S01PForm').find("#rType").val()) {
                //新增時後端不會傳rType上來，故由前端塞值
                $('#L120S01PForm').find("#rType").val(thisRType);
            }
            $("input[name='toglePersonType']").trigger('change');
			
			var btnAction = API.createJSON([{
				key: i18n.def['close'],
                value: function(){
                    $.thickbox.close();
                }
		    }]);
			
			if (userInfo.ssoUnitNo == "900") {
			   btnAction = API.createJSON([{
					key: i18n.def['sure'],
	                value: function(){
	                    var $L120S01PForm = $("#L120S01PForm");
                        if (!$L120S01PForm.valid()) {
                            return;
                        }
                        
                        var toglePersonType = $("[name=toglePersonType]:checked").val();
                        
                        
                        //判斷新增的是自然人還是法人，再去分要存到哪個table，並且同時儲存到主檔TABLE裡
                        $.ajax({
                            handler: "amlrelateformhandler",
                            action: "saveL120s01p",
                            data: {//把資料轉成json
                                type: $("[name=toglePersonType]:checked").val(),
                                oid: $("#l120s01pFormOid").val(),
                                mainId: responseJSON.mainId,
                                custId: $L162M01AForm.find("#rId").val(),
                                dupNo: $L162M01AForm.find("#rDupNo").val(),
                                rType: thisRType,
                                L120S01PForm: JSON.stringify($("#L120S01PForm").serializeData())
                            }
                        }).done(
                            function(responseData){
                                $.thickbox.close();
                                $('#gridviewNatural').trigger('reloadGrid');
                                $('#gridviewCorporate').trigger('reloadGrid');
                            }
                        );
	                }
				}, {
					key: i18n.def['close'],
	                value: function(){
	                    $.thickbox.close();
	                }	
			    }]);
			} 
			
			
            $("#newToglePersonBox").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.lms1605m01["L120S01p.beneficiary"],
                width: 490,
                height: 400,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: false,
                i18n: i18n.def,
                buttons: btnAction
            });
        }
    );
}


//J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
//信用品質順序設定
function setGuarantorSeq(cellvalue, options, rowData){
	
	 var $gridviewPeople = $("#gridviewPeople");
   var select = $gridviewPeople.jqGrid('getGridParam', 'selarrrow');
   if (select == "") {
  	//grid.selrow=請先選擇一筆資料。
       return CommonAPI.showMessage(i18n.def["grid.selrow"]);
   }
   
   var data = [];
   var count = 0;
   var cntrNo = "";
   var l162s01aOid = "";
   for (var i in select) {
  	 count= count+1;
       data.push($gridviewPeople.getRowData(select[i]).oid);
       cntrNo = $gridviewPeople.getRowData(select[i]).cntrNo ;
       l162s01aOid = $gridviewPeople.getRowData(select[i]).oid;
   }
   if(count > 1){
  	 //grid.selrow=請先選擇一筆資料。
       return CommonAPI.showMessage(i18n.def["grid.selrow"]);
   }
   
   $("#gridViewGuarantorSeq").jqGrid("setGridParam", {
       postData: {
           formAction: "queryPeopleForSetPriority",
           oids: data
       },
       search: true
   }).trigger("reloadGrid");
   
   
   //檢查主從債務人都有在0024建檔，否則沒辦法判斷是不是法人
   $.ajax({
       handler: "lms1605m01formhandler",
       action: "chkAllGuarantorHas0024",
       data: {
           mainId: responseJSON.mainId,
           cntrNo : cntrNo
       }
   }).done(
    function(obj){
            
        $("#gridViewGuarantorSeq").trigger("reloadGrid");
        $("#setGuarantorSeqThickBox").thickbox({
            title: i18n.lms1605m01['btn.setGuarantorCreditPriority'],  //信用品質順序設定
            width: 900,
            height: 500,
            modal: true,
            i18n: i18n.lms1605m01,
            buttons: API.createJSON([{
                key: i18n.lms1605m01['btn.writeGuarantor'],
                value: function(){
                    var $gridviewprint = $("#gridViewGuarantorSeq");
                    //寫回主從債務人資料表
                    $gridviewprint.jqGrid('saveRow', lastSel, false, 'clientArray');
                     
                    
                    var ids = $gridviewprint.jqGrid('getDataIDs');
                    //用來放列印順序跟oid
                    var json = {};
                    var checkArray = $gridviewprint.getCol("priority");
                    
                    //檢查列印順序值是否重複
                    if (checkArrayRepeat(checkArray)) {
                        //L162M01A.message9=列印順序不可重複
                        return CommonAPI.showMessage(i18n.lms1605m01['L162M01A.message9']);
                    }
                    
                    for (var id in ids) {
                        
                        var data = $gridviewprint.jqGrid('getRowData', ids[id]);
                        json[data.oid] = data.priority;
                        
                    }
                    FormAction.open = true;
                    $.ajax({
                        handler: "lms1605m01formhandler",
                        action: "savePriority",
                        data: {
                            mainId: $("#mainId").val(),
                            cntrNo : cntrNo,
                            l162s01aOid: l162s01aOid,
                            data: JSON.stringify(json)
                        }
                    }).done(
                        function(obj){
                            FormAction.open = false;
                            $.thickbox.close();
                            $("#gridviewPeople").trigger('reloadGrid');
                        }
                    );
                }
            }, {
                key: i18n.lms1605m01['btn.applyGuarantorCreditOrder'],  //取得保證人信評順序
                value: function(){
                    var $gridviewprint = $("#gridViewGuarantorSeq");
                    //取得保證人信評順序
                    $gridviewprint.jqGrid('saveRow', lastSel, false, 'clientArray');
     
                    var ids = $gridviewprint.jqGrid('getDataIDs');
                    //用來放列印順序跟oid
                    var json = {};
                 
                    for (var id in ids) {           	
                        var data = $gridviewprint.jqGrid('getRowData', ids[id]);
                        json[data.oid] = id;
                    }
                    FormAction.open = true;
                    $.ajax({
                        handler: "lms1605m01formhandler",
                        action: "getGuarantorCreditPriority",
                        data: {
                            mainId: $("#mainId").val(),
                            cntrNo : cntrNo,
                            l162s01aOid: l162s01aOid,
                            data: JSON.stringify(json)
                        }
                    }).done(
                        function(obj){
                            for (var id in ids) {
                                var x= parseInt(id)+1 ;
                                $("#gridViewGuarantorSeq").jqGrid('setRowData', x, {priority:obj[id]});     
                                //$("#gridViewGuarantorSeq").jqGrid('setRowData', 1, {priority:10});
                            }
                            //$gridviewprint.trigger('reloadGrid');
                        }
                    );
                }
            }, {
                key: i18n.lms1605m01['btn.applyEllngteePriority'],   //引進主從債務人建檔資料
                value: function(){
                    var $gridviewprint = $("#gridViewGuarantorSeq");
                    //取得保證人信評順序
                    $gridviewprint.jqGrid('saveRow', lastSel, false, 'clientArray');
     
                    var ids = $gridviewprint.jqGrid('getDataIDs');
                    //用來放列印順序跟oid
                    var json = {};
                    
                    for (var id in ids) {           	
                        var data = $gridviewprint.jqGrid('getRowData', ids[id]);
                        json[data.oid] = id;
                    }
                    FormAction.open = true;
                    $.ajax({
                        handler: "lms1605m01formhandler",
                        action: "applyEllngteePriority",
                        data: {
                            mainId: $("#mainId").val(),
                            cntrNo : cntrNo,
                            l162s01aOid: l162s01aOid,
                            data: JSON.stringify(json)
                        }
                    }).done(
                        function(obj){

                            for (var id in ids) {
                                //alert("id="+id+"，obj[id]="+obj[id]);
                                var x= parseInt(id)+1 ;
                                //alert("x="+x+"，"+"id="+id+"，obj[id]="+obj[id]);
                                
                                $("#gridViewGuarantorSeq").jqGrid('setRowData', x, {priority:obj[id]});     

                                //$("#gridViewGuarantorSeq").jqGrid('setRowData', 1, {priority:10});
                            }

                            //$gridviewprint.trigger('reloadGrid');
                        }
                    );
                }
            }, {
                key: i18n.def['close'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });  
        
        
        
        
    }
   );
   

   
}//close 開啟主從債務人資料表畫面

/** 檢查陣列內容是否重複 */
function checkArrayRepeat(arrVal){
  var newArray = [];
  for (var i = arrVal.length; i--;) {
      var val = arrVal[i];
      if($.trim(val) != "" ){
      	if ($.inArray(val, newArray) == -1) {
              newArray.push(val);
          }
          else {
              return true;
          }
      }
      
  }
  return false;
}


//參考額度明細表內程式，舊案額度序號檢核
var CntrNoAPI = {
		/**
	     *	查詢原案額度序號
	     * @param {String } originalText 原案額度序號
	     * return {Object}cntrNo 額度序號",ownBrName 額度序號前三碼分行名稱
	     */
	    queryOriginalCntrNo: function(originalText){
	        //驗證舊有額度序號規則
	        if (!originalText.match(/\w{12}/)) {
	            //L140M01a.message68=額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)
	            return CommonAPI.showMessage(i18n.lms1601m05["L140M01a.message68"]);
	        }
	        var queryObj = {};
	        $.ajax({
	            handler: inits.fhandle,
	            async: false,
	            action: "checkCntrno",
	            data: {
	                cntrNo: originalText,
	                snoKind: $("#L161M01AForm").find("#snoKind").val(),
	                cntrMainId: $("#L161M01AForm").find("#cntrMainId").val()
	            }
	        }).done(
                function(obj){
	                queryObj.cntrNo = originalText.toUpperCase();
	                queryObj.ownBrName = obj.ownBrName;
	                //當錯誤碼有值則不帶額度序號資料
	                if (obj.error) {
	                    queryObj.cntrNo = "";
	                    queryObj.ownBrName = "";
	                    queryObj.error = obj.error;
	                    //原始額度控管種類
	                    queryObj.snoKindOld = obj.snoKindOld;
	                    queryObj.snoKindOldShow = obj.snoKindOldShow;
	                    //原始額度序號 
	                    queryObj.snoOld = obj.snoOld;
	                    
	                }
	            }
            );
	        return queryObj;
	    }
}
//登錄聯行攤貸比例
var BranchAcitonAF = {
    $form: $('#L140M01E_AFForm'),
    gridId: "#gridviewL140M01E_AF",
    amtgrId: "#gridviewL140m01eAmt",
    /**
     * 查詢
     * @param {Object} cellvalue 欄位顯示值
     * @param {Object} type  欄位選項
     * @param {Object} data  欄位資料
     */
    query: function(cellvalue, type, data){
        if (!data) {
            data = {
                oid: ""
            };
        }
        util.init(BranchAcitonAF.$form);
        BranchAcitonAF.$form.reset();
        
        $.ajax({
            handler: inits.fhandle,
            data: {//把資料轉成json
                formAction: "queryL140m01e_af",
                cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                oid: data.oid,
				noOpenDoc: true
            }
        }).done(
            function(obj){
                $("#shareBrId").setItems({
                    item: obj.item,
                    format: "{value} {key}",
                    space: false
                });
                BranchAcitonAF.$form.injectData(obj.formData);
                $("#totalAmt").val(DOMPurify.sanitize(util.addComma(obj.totalAmt)));
                if (data.oid != "") {
                    $("#shareBrId").prop("disabled", true);
                } else {
                    $("#shareBrId").prop("disabled", false);
                }
                $("#shareAmt,#shareRate1,#shareRate2").prop("readonly", true);
                $("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
                if (obj.role != "0") {
                    $("#shareRate2").val(obj.role);
                    $("#shareRate2").prop("readonly", true);
                    $("[name=shareFlag]").prop("disabled", true);
                    //UPGRADE
					//$("[name=shareFlag][value=" + obj.shareFlag + "]").prop("checked", true);
					$("[name=shareFlag][value=\"" + obj.shareFlag + "\"]").prop("checked", true);
                } else {
                    $("[name=shareFlag]").prop("disabled", false);
                }//close if
                BranchAcitonAF.controlShow();
                BranchAcitonAF.openBox();
            }//close success
        ); //close ajax  
    },
    openBox: function(){
        $("#newItemChildrenBox3").thickbox({
            //title.15=登錄聯行攤貸比例
            title: i18n.lms1605m01["L140M01e.title01"],
            width: 600,
            height: 300,
            modal: true,
            //readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            open: function(){
                if (_openerLockDoc == "1") {
                    //鎖定box
                    BranchAcitonAF.$form.readOnlyChilds(_openerLockDoc == "1", "#totalAmt,#shareBrId");
                }
            },
            buttons: {
                "saveData": function(){
                    if ($("[name=shareFlag]:checked").val() == "1") {
                        BranchAcitonAF.$form.find("#shareRate1,#shareRate2").removeClass("required");
                    } else {
                        BranchAcitonAF.countByAMT();
                        BranchAcitonAF.$form.find("#shareRate1,#shareRate2").addClass("required");
                        if ($("#shareRate2").val() == 0) {
                            //L140M01a.message81=分母不可為0
                            return CommonAPI.showMessage(i18n.lms1605m01["L140M01a.message81"]);
                        }
                    }
                    
                    if (!BranchAcitonAF.$form.valid()) {
                        return false;
                    }
                    var shareRate1 = parseInt($("#shareRate1").val(), 10), shareRate2 = parseInt($("#shareRate2").val(), 10);
                    if (shareRate1 > shareRate2) {
                        //L140M01e.lmterror=分子總和大於分母無法儲存
                        return CommonAPI.showMessage(i18n.lms1605m01["L140M01e.lmterror"]);
                    }
                    var shareBrId = $("#shareBrId").val();
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "queryShareBrIdType",//查詢目前分行為海外還是國內，若為海外第四碼為5，若為國內判斷
                            shareBrId: shareBrId,
                            cntrMainId: $("#L161M01AForm").find("#cntrMainId").val()
                        }
                    }).done(
                        function(obj){
                            //先檢查該間分行是否存在，若存在只做save的動作
                            if (obj.have) {
                                //若該分行已存在只執行儲存
                                BranchAcitonAF.save("");
                            } else {
                                //新增案件開起給號視窗
                                BranchAcitonAF.getCntrNo(obj.type, shareBrId);
                            }
                        }//close success
                    );
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**儲存
     *
     * @param {Object} type 1.DBU,4.OBU,5.海外
     */
    save: function(type){
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "saveL140m01e_af",
                cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                cntrNo : $("#L161M01AForm").find("#cntrNo").val(),
                L140M01E_AFForm: JSON.stringify(BranchAcitonAF.$form.serializeData()),
                type: type
            }
        }).done(
            function(obj){
                if (obj && obj.drc) {
                    $("#itemDscr1").val(obj.drc);
                }
                $.thickbox.close();
                $(BranchAcitonAF.gridId).trigger("reloadGrid");
            }
        );
    },
    
    /**
     * 給號畫面
     * @param {String} type 5.海外
     * @param {String} brId 分行代號
     */
    getCntrNo: function(type, brId){
        var $cntrNoForm = $("#cntrNoBoxforItem3Form");
        $("#cntrNoBoxforItem3").thickbox({
            //btn.number=給號
            title: i18n.lms1605m01["btn.number"],
            width: 640,
            height: 320,
            modal: true,
            align: "center",
            //readOnly: _openerLockDoc == "1",
            valign: "bottom",
            i18n: i18n.def,
            open: function(){
                //初始化
                $cntrNoForm.reset();
                //帶入分行預設值
                $cntrNoForm.find("#branchNoItem3").val(brId);
                $cntrNoForm.find("#cntrNoType").val(type);
                $cntrNoForm.find(".ForOriginal,.ForInSide").hide();
            },
            buttons: {
                "sure": function(){
                    if (!$cntrNoForm.valid()) {
                        return false;
                    }
                    var numberType = $cntrNoForm.find("input[name=numberType]:checked").val();
                    var originalCntrNo = $cntrNoForm.find("#originalCntrNo").val();
                    brId = $cntrNoForm.find("#branchNoItem3").val();
                    //給新號
                    if (numberType == "1") {
                        if (type != "5") {
                            //國內的可以自己選分行和DBU or OBU
                            type = $cntrNoForm.find("input[name=typeCd]:checked").val();
                        }
                    } else {
                        //舊號
                        var cntrno = CntrNoAPI.queryOriginalCntrNo(originalCntrNo, "3");
                        if ($.isEmptyObject(cntrno) || !cntrno.cntrNo) {
                            return false;
                        }
                    }
                    $.ajax({
                        handler: inits.fhandle,
                        action: "saveL140m01e_af",
                        data: {
                        	L140M01E_AFForm: JSON.stringify(BranchAcitonAF.$form.serializeData()),
                            cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                            cntrNo : $("#L161M01AForm").find("#cntrNo").val(),
                            numberType: numberType,
                            originalCntrNo: originalCntrNo,
                            type: type,
                            classCD: "0",
                            selectBrNo: brId
                        }
                    }).done(
                        function(obj){
                            if (obj && obj.drc) {
                                $("#itemDscr1").val(obj.drc);
                            }
                            $.thickbox.close();
                            $.thickbox.close();
                            $(BranchAcitonAF.gridId).trigger("reloadGrid");
                        }
                    );
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 刪除
     */
    remove: function(){
        var gridData = $(BranchAcitonAF.gridId);
        var gridID = gridData.getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = gridData.getRowData(gridID[i]).oid;
                }
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140m01e_af",
                        cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                        cntrNo : $("#L161M01AForm").find("#cntrNo").val(),
                        Idlist: gridIDList
                    }
                }).done(
                    function(obj){
                        if (obj && obj.drc) {
                            $("#itemDscr1").val(obj.drc);
                        }
                        gridData.trigger("reloadGrid");
                    }
                );
            }
        });
    },
    /**
     * 控制計算方式顯示
     */
    controlShow: function(){
        var value = $("[name=shareFlag]:checked").val();
        $("#shareAmt,#shareRate1,#shareRate2").prop("readonly", true);
        $("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
        //顯示隱藏
        switch (value) {
            case "1":
                // 1.依攤貸金額
                $("#shareMoneyCount2").show().parents(".fg-buttonset").show();
                $("#shareAmt").prop("readonly",false);
                $("#shareRate1,#shareRate2").val("");
                break;
            case "2":
                //2.依攤貸比例          
                $("#shareMoneyCount1").show().parents(".fg-buttonset").show();
                if (!$("[name=shareFlag]:checked").prop("disabled")) {
                    $("#shareAmt").val("");
                }
                $("#shareRate1").prop("readonly",false);
                var countGrid = $(BranchAcitonAF.gridId).jqGrid('getGridParam', 'records');
                if (countGrid == 0) {
                    $("#shareRate2").prop("readonly",false);
                }
                break;
            default:
                break;
        }
    },
    /**
     *以比例計算金額
     */
    countByAMT: function(){
        var totel = $("#totalAmt").val().replace(/,/g, ""), value1 = $("#shareRate1").val(), value2 = $("#shareRate2").val(), shareRate1 = parseInt(value1, 10), shareRate2 = parseInt(value2, 10), end = (totel * shareRate1) / shareRate2;
        if (!BranchAcitonAF.isNumber(end)) {
            end = "";
        } else {
            end = util.addComma(parseInt(end, 10));
        }
        $("#shareAmt").val(end);
    },
    /**
     *變更分母
     */
    changesShareRate: function(){
        $("#newSharteNew").val("");
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryChangesShareRate",
                cntrMainId: $("#L161M01AForm").find("#cntrMainId").val()
            }
        }).done(
            function(obj){
                $("#newSharteNewBox").thickbox({
                    title: i18n.lms1605m01["btn.changesShareRate2"],//變更分母
                    width: 200,
                    height: 100,
                    //readOnly: _openerLockDoc == "1",
                    i18n: i18n.def,
                    modal: true,
                    align: "center",
                    valign: "bottom",
                    open: function(){
                    },
                    buttons: {
                        "sure": function(){
                            if ($("#newSharteNew").val() == 0) {
                                //L140M01a.message81=分母不可為0
                                return CommonAPI.showMessage(i18n.lms1605m01["L140M01e.message03"]);
                            }
                            if (!$("#newSharteNewForm").valid()) {
                                return false;
                            }
                            $.ajax({
                                handler: inits.fhandle,
                                data: {
                                    formAction: "saveChangesShareRate",
                                    cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                                    cntrNo : $("#L161M01AForm").find("#cntrNo").val(),
                                    shareRate: $("#newSharteNew").val()
                                }
                            }).done(
                                function(obj){
                                    if (obj && obj.drc) {
                                        $("#itemDscr1").val(obj.drc);
                                    }
                                    $(BranchAcitonAF.gridId).trigger("reloadGrid");
                                    $.thickbox.close();
                                }
                            );
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        );
    },
    //驗證是不是數字   
    isNumber: function(val){
        return /\d/.test(val);
    },
    //當現請額度大於攤貸總金額時 會出現 grid選擇  將餘額加至哪筆 
    l140m01eAmtBox: function(amt){
        //L140M01a.message70=聯行攤貸金額尚餘 {0}元，請選擇要將餘額加進何筆資料!
        $("#l140m01eAmtMsg").html(i18n.lms1605m01['L140M01e.message06'].replace("{0}", amt))
        $(BranchAcitonAF.amtgrId).setGridParam({//重新設定grid需要查到的資料
			postData: {
				cntrMainId: $("#L161M01AForm").find("#cntrMainId").val()
            },
            search: true
        }).trigger("reloadGrid");
        
        $("#l140m01eAmtBox").thickbox({
            title: "",
            width: 600,
            height: 350,
            align: "center",
            valign: "bottom",
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $(BranchAcitonAF.amtgrId).getGridParam('selrow');
                    if (id == null || !id) {
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showErrorMessage(i18n.def['action_005']);
                    }
                    var oid = $(BranchAcitonAF.amtgrId).getRowData(id).oid;
                    $.thickbox.close();
                    $.ajax({
                        handler: inits.fhandle,
                        action: "saveL140m01e_afAmt",
                        data: {
                            oid: oid,
                            amt: util.delComma(amt),
                            cntrNo : $("#L161M01AForm").find("#cntrNo").val()
                        }
                    }).done(
                        function(responseData){
                        	$(BranchAcitonAF.gridId).trigger("reloadGrid");
                        }
                    );
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
};