/*
 * CapMath.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,
		Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.util;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Arrays;

import tw.com.iisi.cap.constant.CapConstants;

/**
 * <p>
 * 數字格式處理.
 * </p>
 * 
 * <AUTHOR>
 * 
 */
public class CapMath implements CapConstants {

    /**
     * 預設捨去模式 陣列
     */
    final static int[] DEFAULT_DIVIDE_ROUNDING_MODE = { BigDecimal.ROUND_HALF_UP, BigDecimal.ROUND_HALF_UP, BigDecimal.ROUND_HALF_UP };

    /**
     * 預設捨去模式
     */
    final static int DEFAULT_ROUNDING_MODE = BigDecimal.ROUND_HALF_UP;

    /**
     * 轉為純字串
     */
    private static final Method toPlainString;
    static {
        Method m;
        try {
            m = BigDecimal.class.getMethod("toPlainString", (Class[]) null);
        } catch (NoSuchMethodException e) {
            m = null;
        }
        toPlainString = m;
    }

    /**
     * Set number's scale.
     * 
     * @param first
     *            the number
     * @param scale
     *            scale of the BigDecimal value to be returned.
     * @return the result data
     * @see java.math.BigDecimal#ROUND_HALF_UP
     */
    public static String setScale(String first, int scale) {
        return setScale(first, scale, DEFAULT_ROUNDING_MODE);
    }

    /**
     * Set number's scale.
     * 
     * @param first
     *            the number
     * @param scale
     *            scale of the BigDecimal value to be returned.
     * @param roundingMode
     *            The rounding mode to apply.
     * @return the result data
     * @see java.math.BigDecimal#ROUND_UP
     * @see java.math.BigDecimal#ROUND_DOWN
     * @see java.math.BigDecimal#ROUND_CEILING
     * @see java.math.BigDecimal#ROUND_FLOOR
     * @see java.math.BigDecimal#ROUND_HALF_UP
     * @see java.math.BigDecimal#ROUND_HALF_DOWN
     * @see java.math.BigDecimal#ROUND_HALF_EVEN
     * @see java.math.BigDecimal#ROUND_UNNECESSARY
     */
    public static String setScale(String first, int scale, int roundingMode) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        BigDecimal result = getBigDecimal(first).setScale(scale, roundingMode);
        return bigDecimalToString(result);
    }

    /**
     * add two Number. use ROUND_HALF_UP for rounding mode.
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @return String the result number
     * @see java.math.BigDecimal#ROUND_HALF_UP
     */
    public static String add(String first, String second) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        second = second == null || second.length() == 0 ? S0 : second.trim();

        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        int fs = f.scale();
        int ss = s.scale();
        int max = fs > ss ? fs : ss;
        BigDecimal result = f.add(s).setScale(max, DEFAULT_ROUNDING_MODE);
        return bigDecimalToString(result);
    }

    /**
     * add two Number with scale. use ROUND_HALF_UP for rounding mode.
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @param scale
     *            scale of the result value to be returned.
     * @return String the result number
     */
    public static String add(String first, String second, int scale) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        second = second == null || second.length() == 0 ? S0 : second.trim();

        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        BigDecimal result = f.add(s).setScale(scale, DEFAULT_ROUNDING_MODE);
        return bigDecimalToString(result);
    }

    /**
     * add more than one Number
     * 
     * @param numArray
     *            numArray
     * @return String
     */
    public static String add(String[] numArray) {
        String result = S0;
        for (int i = 0; i < numArray.length; i++) {
            result = add(result, numArray[i]);
        }
        return result;
    }

    /**
     * subtract two number.
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @return String the result number
     */
    public static String subtract(String first, String second) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        second = second == null || second.length() == 0 ? S0 : second.trim();

        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        int fs = f.scale();
        int ss = s.scale();
        int max = fs > ss ? fs : ss;
        BigDecimal result = f.subtract(s).setScale(max, DEFAULT_ROUNDING_MODE);
        return bigDecimalToString(result);
    }

    /**
     * subtract two number with scale.
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @param scale
     *            scale of the result value to be returned.
     * @return String the result number
     */
    public static String subtract(String first, String second, int scale) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        second = second == null || second.length() == 0 ? S0 : second.trim();

        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        BigDecimal result = f.subtract(s).setScale(scale, DEFAULT_ROUNDING_MODE);
        return bigDecimalToString(result);
    }

    /**
     * subtract more than one Number
     * 
     * @param numArray
     *            numArray
     * @return String
     */
    public static String subtract(String[] numArray) {
        String result = numArray[0];
        for (int i = 1; i < numArray.length; i++) {
            result = subtract(result, numArray[i]);
        }
        return result;
    }

    /**
     * multiply two number.
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @return String the result number
     */
    public static String multiply(String first, String second) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        second = second == null || second.length() == 0 ? S0 : second.trim();

        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        int fs = f.scale();
        int ss = s.scale();
        int max = fs > ss ? fs : ss;
        BigDecimal result = f.multiply(s).setScale(max, DEFAULT_ROUNDING_MODE);
        return bigDecimalToString(result);
    }

    /**
     * multiply two number with scale.
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @param scale
     *            scale of the result value to be returned.
     * @return String the result number
     */
    public static String multiply(String first, String second, int scale) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        second = second == null || second.length() == 0 ? S0 : second.trim();

        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        BigDecimal result = f.multiply(s).setScale(scale, DEFAULT_ROUNDING_MODE);
        return bigDecimalToString(result);
    }

    /**
     * divide two Number.
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @return String the result number
     */
    public static String divide(String first, String second) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        second = second == null || second.length() == 0 ? S0 : second.trim();
        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        if (s.compareTo(B0) == 0)
            return "0";
        int fs = f.scale();
        int ss = s.scale();
        int max = fs > ss ? fs : ss;
        BigDecimal result = f.setScale(max, DEFAULT_DIVIDE_ROUNDING_MODE[0]).divide(s, DEFAULT_DIVIDE_ROUNDING_MODE[1]).setScale(max, DEFAULT_DIVIDE_ROUNDING_MODE[2]);

        return bigDecimalToString(result);
    }

    /**
     * divide two Number with scale.
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @param scale
     *            scale of the result value to be returned.
     * @return String the result number
     */
    public static String divide(String first, String second, int scale) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        second = second == null || second.length() == 0 ? S0 : second.trim();

        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        BigDecimal result = f.setScale(scale, DEFAULT_DIVIDE_ROUNDING_MODE[0]).divide(s, DEFAULT_DIVIDE_ROUNDING_MODE[1]).setScale(scale, DEFAULT_DIVIDE_ROUNDING_MODE[2]);
        return bigDecimalToString(result);
    }

    /**
     * compare two Number
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @return -1, 0 or 1 as this BigDecimal is numerically less than, equal to, or greater than <tt>val</tt>.
     */
    public static int compare(BigDecimal first, BigDecimal second) {
        if (first == null)
            first = B0;
        if (second == null)
            second = B0;
        return first.compareTo(second);
    }

    /**
     * compare two Number
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @return -1, 0 or 1 as this BigDecimal is numerically less than, equal to, or greater than <tt>val</tt>.
     */
    public static int compare(BigDecimal first, String second) {
        second = second == null || second.length() == 0 ? S0 : second.trim();
        BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
        return first.compareTo(s);
    }

    /**
     * compare two Number
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @return -1, 0 or 1 as this BigDecimal is numerically less than, equal to, or greater than <tt>val</tt>.
     */
    public static int compare(String first, BigDecimal second) {
        first = first == null || first.length() == 0 ? S0 : first.trim();
        BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
        return f.compareTo(second);
    }

    /**
     * compare two Number
     * 
     * @param first
     *            the first number
     * @param second
     *            the second number
     * @return -1, 0 or 1 as this BigDecimal is numerically less than, equal to, or greater than <tt>val</tt>.
     */
    public static int compare(String first, String second) {
        try {
            first = first == null || first.length() == 0 ? S0 : first.trim();
            second = second == null || second.length() == 0 ? S0 : second.trim();

            BigDecimal f = getBigDecimal(first);// new BigDecimal(first);
            BigDecimal s = getBigDecimal(second);// new BigDecimal(second);
            return f.compareTo(s);
        } catch (Exception ex) {
            return 0;
        }
    }

    /**
     * round Number
     * 
     * @param num
     *            num
     * @param roundIndex
     *            roundIndex
     * @return String
     */
    public static String round(String num, int roundIndex) {
        String result = S0;
        String multiple = S1;
        if (roundIndex < 0) {
            for (int i = roundIndex; i < 0; i++) {
                multiple = multiple + S0;
            }
            String tempNum = divide(num, multiple);
            tempNum = setScale(tempNum, 0, BigDecimal.ROUND_HALF_UP);
            result = multiply(tempNum, multiple);
        } else {
            result = setScale(num, roundIndex, BigDecimal.ROUND_HALF_UP);
        }

        return result;
    }

    /**
     * toString.
     * 
     * @param b
     *            the BigDecimal
     * @return String
     */
    public static String bigDecimalToString(BigDecimal b) {
        String out = null;
        if (b != null) {
            if (toPlainString == null) {
                out = b.toString();
            } else {
                // jdk 1.5 (5.0) bigdecimal's toString() change
                try {
                    out = (String) toPlainString.invoke(b, (Object[]) null);
                } catch (Exception ex) {
                    return null;
                }
            }
        }
        return out;
    }

    /**
     * 將字串轉為BigDecimal格式
     * 
     * @param in
     *            the input
     * @return BigDecimal
     */
    public static BigDecimal getBigDecimal(String in) {
        BigDecimal out = B0;
        if (in != null) {
            try {
                out = new BigDecimal(in);
            } catch (Exception ex) {
                char[] ca = in.toCharArray();
                StringBuffer sb = new StringBuffer();
                for (int i = 0; i < ca.length; i++) {
                    switch (ca[i]) {
                    case '-':
                    case '+':
                    case '0':
                    case '1':
                    case '2':
                    case '3':
                    case '4':
                    case '5':
                    case '6':
                    case '7':
                    case '8':
                    case '9':
                    case '.':
                        sb.append(ca[i]);
                    }
                }
                try {
                    out = new BigDecimal(sb.toString());
                } catch (Exception ex2) {
                    return out;
                }
            }
        }
        return out;
    }

    /**
     * 取得 Normal(sampleMean, sampleSTD) 的值對應到 Normal(mean, std)的轉換值
     * 
     * @param sampleValue
     * @param sampleMean
     * @param sampleSTD
     * @param mean
     * @param std
     * @return
     */
    public static BigDecimal normalization(BigDecimal sampleValue, BigDecimal sampleMean, BigDecimal sampleSTD, BigDecimal mean, BigDecimal std, int scale, RoundingMode rm) {

        if (sampleValue == null || sampleMean == null || sampleSTD == null || mean == null || std == null) {
            return null;
        }
        BigDecimal tmpValue = sampleSTD;
        if (tmpValue.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        tmpValue = sampleValue.subtract(sampleMean).divide(tmpValue, scale, rm).multiply(std).add(mean);
        return tmpValue;
    }

    /**
     * 取得 Normal(sampleMean, sampleSTD) 的值對應到 Normal(mean, std)的轉換值
     * 
     * @param sampleValue
     * @param sampleMean
     * @param sampleSTD
     * @param mean
     * @param std
     * @return
     */
    public static BigDecimal normalization(BigDecimal sampleValue, BigDecimal sampleMean, BigDecimal sampleSTD, BigDecimal mean, BigDecimal std, MathContext mc) {

        if (sampleValue == null || sampleMean == null || sampleSTD == null || mean == null || std == null) {
            return null;
        }
        BigDecimal tmpValue = sampleSTD;
        if (tmpValue.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        tmpValue = sampleValue.subtract(sampleMean).divide(tmpValue, mc).multiply(std).add(mean);
        return tmpValue;
    }

    /**
     * 當BigDecimal為null時回傳零
     * 
     * @param in
     * @return
     */
    public static BigDecimal bigDecimalIsNullToZero(BigDecimal in) {
        return in == null ? BigDecimal.ZERO : in;
    }

    /**
     * 傳回範圍中第 K 個百分位數的值。
     *
     * @param arr
     *            必要。 用以定義相對位置的陣列或資料範圍。
     * @param percent
     *            必要。 在 0 到 1 範圍內 (包括 0 與 1) 的百分位數。
     * @return 傳回範圍中第 K 個百分位數的值。
     */
    public static BigDecimal percentile(double arr[], double percent) {
        // 例子 陣列=[10,20,30,40,50,60,70,80], 取0.9(90%)的值
        // N = 8, (8-1)*0.9 = 6.3 -> 整數為6，小數為0.3
        // 陣列[6]=70, 陣列[6+1]=80
        // 公式為70*(1-0.3)+80*(0.3) = 73

        // percent 在 0 到 1 範圍內
        if (arr == null || arr.length == 0 || percent < 0 || percent > 1) {
            return null;
        }
        if (arr.length == 1) {
            return BigDecimal.valueOf(arr[0]);
        }
        // 先排序，由小排到大
        Arrays.sort(arr);
        if (percent == 0) {
            return BigDecimal.valueOf(arr[0]);
        }
        if (percent == 1) {
            return BigDecimal.valueOf(arr[arr.length - 1]);
        }
        int n = arr.length;
        BigDecimal base = BigDecimal.valueOf(percent).multiply(BigDecimal.valueOf(n - 1));
        // 取整數
        int a = base.intValue();
        // 取小數。remainder->切成?份，剩下來的數。這邊用切成1份後，剩下來的就是小數的部份
        BigDecimal b = base.remainder(BigDecimal.ONE);

        // 使用內插法來計算
        // value = arr[a]*(1-b) + arr[a+1]*b
        return BigDecimal.valueOf(arr[a]).multiply(BigDecimal.ONE.subtract(b)).add(BigDecimal.valueOf(arr[a + 1]).multiply(b));
    }

    /**
     * 將 BigDecimal 小數的尾數0給去掉
     *
     * @param num
     * @return
     */
    public static String stripTrailingZerosString(BigDecimal num) {
        if (num != null) {
            return num.stripTrailingZeros().toPlainString();
        }
        return "";
    }
}
