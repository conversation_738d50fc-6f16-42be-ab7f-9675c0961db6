/* 
 * CapMvcParameters.java
 * 
 * Copyright (c) 2021 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.iisigroup.cap.component.impl;

import java.lang.reflect.Array;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.util.StringChecker;

/**
 * <pre>
 * 取得Resquest傳入的參數
 * </pre>
 * 
 * @since 2021年11月8日
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2021年11月8日,1104263,new
 *          </ul>
 */
@Scope("prototype")
@Component
public class CapMvcParameters extends HashMap<String, Object> implements PageParameters {

    private static final long serialVersionUID = 1L;

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private HttpServletRequest request;

    /**
     * 設置HttpServletRequest物件
     * 
     * @see com.iisigroup.cap.component.PageParameters#setRequestObject(java.lang.Object)
     */
    @Override
    public void setRequestObject(Object obj) {
        this.request = (HttpServletRequest) obj;
        super.putAll(getReqParameter(request));
    }

    /**
     * <pre>
     * 取得form傳過來的field名稱和值。
     * </pre>
     * 
     * @param req
     *            HttpServletRequest
     * @return Map 前頁傳過來的參數值
     */
    public Map<String, Object> getReqParameter(ServletRequest req) {
        Enumeration<String> fids = req.getParameterNames();
        HashMap<String, Object> hm = new HashMap<String, Object>();
        while (fids.hasMoreElements()) {
            String field = (String) fids.nextElement();
            String[] value = req.getParameterValues(field);
            if (value.length == 1 || "txCode".equalsIgnoreCase(field)) {
                hm.put(field, value[0]);
                continue;
            }
            hm.put(field, value);
        }
        return hm;
    }

    /*
     * 取得處理過危害字元的字串
     * 
     * @see com.iisigroup.cap.component.PageParameters#getString(java.lang.String)
     */
    @Override
    public String getString(String key) {
        return getString(key, null);
    }

    /*
     * 取得處理過危害字元的字串, 設置未找到指定key時的預設返回值
     * 
     * @see com.iisigroup.cap.component.PageParameters#getString(java.lang.String, java.lang.String)
     */
    @Override
    public String getString(String key, String defaultValue) {
        return getString(key, defaultValue, true);
    }

    /*
     * 取得不限制長度之字串,對其進行有害字元檢查, 並設置未找到指定key時的預設返回值
     * 
     * @see com.iisigroup.cap.component.PageParameters#getStrickString(java.lang.String, java.lang.String)
     */
    @Override
    public String getStrickString(String key, String defaultValue) {

        return getStrickString(key, defaultValue, 0);
    }

    /*
     * 取得字串,對其進行有害字元檢查和長度限制, 並設置未找到指定key時的預設返回值
     * 
     * @see com.iisigroup.cap.component.PageParameters#getStrickString(java.lang.String, java.lang.String, int)
     */
    @Override
    public String getStrickString(String key, String defaultValue, int maxLength) {

        String s1 = getString(key, defaultValue, false);
        String s2 = StringChecker.checkXssString(s1, maxLength, false);
        return s2;
    }

    /*
     * 取得不限制長度之字串,對其進行有害字元檢查
     * 
     * @see com.iisigroup.cap.component.PageParameters#getEscapString(java.lang.String)
     */
    @Override
    public String getEscapString(String key) {
        return getEscapString(key, null);
    }

    /*
     * 取得不限制長度之字串,對其進行有害字元檢查, 並設置未找到指定key時的預設返回值
     * 
     * @see com.iisigroup.cap.component.PageParameters#getEscapString(java.lang.String, java.lang.String)
     */
    @Override
    public String getEscapString(String key, String defaultValue) {
        Object value = null;
        value = super.containsKey(key) ? super.get(key) : request.getParameter(key);
        if (value != null) {
            // String s = ((String[]) value)[0];
            // return StringEscapeUtils.escapeHtml(s);
            return getStrickString(((String[]) value)[0], defaultValue);
        }
        logger.trace("can't find request parameter :" + key);
        return defaultValue;
    }

    /**
     * 判斷傳入值是否為空值, 並呼叫 {@linkplain com.iisigroup.cap.component.impl.CapMvcParameters#getStrickString(String, String) getStrickString} 方法
     * 
     * @param value
     *            需經過判斷的字串
     * @return value !null : {@code getStrickString(value, "")} , <br>
     *         otherwise : {@code null}
     */
    public String getEscapeStringFromValue(String value) {
        if (value != null) {
            return getStrickString(value, "");
        }
        logger.trace("can't get value of string. ");
        return null;
    }

    /*
     * 對HashMap物件設置參數
     * 
     * @see com.iisigroup.cap.component.PageParameters#setParameter(java.lang.String, java.lang.Object)
     */
    @Override
    public void setParameter(String key, Object value) {
        super.put(key, value);
    }

    /*
     * 返回HttpServletRequest
     * 
     * @see com.iisigroup.cap.component.PageParameters#getServletRequest()
     */
    @SuppressWarnings("unchecked")
    @Override
    public ServletRequest getServletRequest() {
        return request;
    }

    /*
     * 檢查Key是否存在
     * 
     * @see com.iisigroup.cap.component.PageParameters#containsParamsKey(java.lang.String)
     */
    @Override
    public boolean containsParamsKey(String key) {
        return request.getParameter(key) != null || super.containsKey(key);
    }

    /*
     * 判斷取出的值是否為Int, 不是的話返回預設值0
     * 
     * @see com.iisigroup.cap.component.PageParameters#getAsInteger(java.lang.String)
     */
    @Override
    public int getAsInteger(String key) {
        return getAsInteger(key, 0);
    }

    /*
     * 判斷取出的值是否為Int並返回, 不是的話返回自訂預設值
     * 
     * @see com.iisigroup.cap.component.PageParameters#getAsInteger(java.lang.String, int)
     */
    @Override
    public int getAsInteger(String key, int defaultValue) {
        String s = getString(key, String.valueOf(defaultValue));
        return NumberUtils.isParsable(s) ? Integer.valueOf(s) : defaultValue;
    }

    /*
     * 將HashMap以String形式返回
     * 
     * @see java.util.AbstractMap#toString()
     */
    @Override
    public String toString() {
        return super.toString();
    }

    /*
     * 判斷以key取出的值, 並返回對應字串陣列
     * 
     * @see com.iisigroup.cap.component.PageParameters#getStringArray(java.lang.String)
     */
    @SuppressWarnings("rawtypes")
    @Override
    public String[] getStringArray(String key) {
        Object values = super.get(key);
        if (values instanceof String[]) {
            return (String[]) values;
        } else if (values instanceof List) {
            List list = (List) values;
            Object[] o = list.toArray();
            String[] s = new String[o.length];
            for (int i = 0; i < o.length; i++) {
                s[i] = String.valueOf(o[i]);
            }
            return s;
        } else if (values != null) {
            return new String[] { String.valueOf(values) };
        } else {
            return new String[0];
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.iisigroup.cap.component.PageParameters#getArrayAsString(java.lang.String)
     */
    @Override
    public String getArrayAsString(String key) {
        String[] ary = getStringArray(key);
        return CapString.array2String(ary);
    }

    /*
     * 以key值取出對應物件
     * 
     * @see com.iisigroup.cap.component.PageParameters#getObject(java.lang.String)
     */
    @Override
    public Object getObject(String key) {
        Object value = null;
        value = super.containsKey(key) ? super.get(key) : request.getParameter(key);

        if (value == null) {
            logger.debug("can't find request parameter :" + key);
            return null;
        } else {
            if (value instanceof String[] && ((String[]) value).length > 0) {
                return ((Object[]) value)[0];
            }
            return value;
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.iisigroup.cap.component.PageParameters#getFile(java.lang.String)
     */
    @SuppressWarnings("unchecked")
    @Override
    public <T> T getFile(String key) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        return (T) multipartRequest.getFile(key);
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.iisigroup.cap.component.PageParameters#getFiles(java.lang.String)
     */
    @SuppressWarnings("unchecked")
    @Override
    public <T> List<T> getFiles(String key) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        return (List<T>) multipartRequest.getFiles(key);
    }

    /*
     * 從HashMap或是Request中取值, 非JSON物件可設置是否要處理有害字元, 處理完後返回字串
     * 
     * @see com.iisigroup.cap.component.PageParameters#getString(java.lang.String, java.lang.String, boolean)
     */
    @SuppressWarnings("rawtypes")
    @Override
    public String getString(String key, String defaultValue, boolean escape) {
        Object value = null;
        value = super.containsKey(key) ? super.get(key) : request.getParameter(key);

        if (value == null) {
            logger.trace("can't find request parameter :" + key);
            return defaultValue;
        } else {
            String val;
            if (value instanceof String[] && ((String[]) value).length > 0) {
                val = ((String[]) value)[0].trim();
            } else if (value instanceof List && ((List) value).size() > 0) {
                val = ((String) ((List) value).get(0)).trim();
            } else {
                val = String.valueOf(value).trim();
            }
            // 修正Parameter_Tampering
            // val = StringChecker.checkXssString(val); 此寫法會造成傳入是TAG字串時會發生問題
            return isJsonObject(val) ? val : (escape ? getStrickString(key, defaultValue) : val);
        }
    }

    /**
     * 判斷字串是否為JSON物件
     * 
     * @param val
     *            需判斷的String
     * @return boolean
     */
    private boolean isJsonObject(String val) {
        return (val.startsWith("{") && val.endsWith("}")) || (val.startsWith("[") && val.endsWith("]"));
    }

    /*
     * 比對取出的值, 依結果回傳布林值, 預設返回false
     * 
     * @see com.iisigroup.cap.component.PageParameters#getAsBoolean(java.lang.String)
     */
    @Override
    public Boolean getAsBoolean(String key) {
        return getAsBoolean(key, false);
    }

    /*
     * 比對取出的值, 依結果回傳布林值, 可設置預設返回值
     * 
     * @see com.iisigroup.cap.component.PageParameters#getAsBoolean(java.lang.String, boolean)
     */
    @Override
    public Boolean getAsBoolean(String key, boolean defaultValue) {
        return isTrue(getString(key, String.valueOf(defaultValue)));
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.iisigroup.cap.component.PageParameters#getBoolean(java.lang.String)
     */
    @Override
    public boolean getBoolean(String key) {
        return getAsBoolean(key, false);
    }

    /*
     * 將取出的字串轉換成Long形式
     * 
     * @see com.iisigroup.cap.component.PageParameters#getLong(java.lang.String, long)
     */
    @Override
    public long getLong(String key, long defaultValue) {
        return Long.parseLong(getString(key, String.valueOf(defaultValue)));
    }

    /*
     * 判斷取出的值是否為Int並返回, 不是的話返回自訂預設值
     * 
     * @see com.iisigroup.cap.component.PageParameters#getInt(java.lang.String, int)
     */
    @Override
    public int getInt(String key, int defaultValue) {
        return getAsInteger(key, defaultValue);
    }

    /**
     * 比對傳入的字串, 依結果回傳布林值
     * 
     * @param s
     *            需比對的字串
     * @return boolean
     */
    public static boolean isTrue(final String s) {
        if (s != null) {
            if (s.equalsIgnoreCase("true")) {
                return true;
            }
            if (s.equalsIgnoreCase("false")) {
                return false;
            }
            if (s.equalsIgnoreCase("on") || s.equalsIgnoreCase("yes") || s.equalsIgnoreCase("y") || s.equalsIgnoreCase("1")) {
                return true;
            }
            if (s.equalsIgnoreCase("off") || s.equalsIgnoreCase("no") || s.equalsIgnoreCase("n") || s.equalsIgnoreCase("0")) {
                return false;
            }
            return false;
        }
        return false;
    }

    /*
     * 判斷取出的值是否為Int並返回, 不是的話返回預設值0
     * 
     * @see com.iisigroup.cap.component.PageParameters#getInt(java.lang.String)
     */
    @Override
    public int getInt(String key) {
        return getInt(key, 0);
    }

    /*
     * 取值後轉成Double, 若取出值非數字形式則返還自訂預設值
     * 
     * @see com.iisigroup.cap.component.PageParameters#getAsDouble(java.lang.String, double)
     */
    @Override
    public double getAsDouble(String key, double defaultValue) {
        String s = getString(key, String.valueOf(defaultValue));
        return NumberUtils.isCreatable(s) ? Double.valueOf(s) : defaultValue;
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.iisigroup.cap.component.PageParameters#getDouble(java.lang.String)
     */
    @Override
    public Double getDouble(String key) {
        String s = getString(key);
        return Double.valueOf(s);
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.iisigroup.cap.component.PageParameters#getKey(java.lang.String)
     */
    @Override
    public String getKey(String key) {
        for (String keyValue : keySet()) {
            if (key.equalsIgnoreCase(keyValue)) {
                return keyValue;
            }
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.iisigroup.cap.component.PageParameters#add(java.lang.String, java.lang.String)
     */
    @Override
    public void add(String key, String value) {
        final Object o = get(key);
        if (o == null) {
            put(key, value);
        } else if (o.getClass().isArray()) {
            int length = Array.getLength(o);
            String destArray[] = new String[length + 1];
            for (int i = 0; i < length; i++) {
                final Object arrayValue = Array.get(o, i);
                if (arrayValue != null) {
                    destArray[i] = arrayValue.toString();
                }
            }
            destArray[length] = value;
            put(key, destArray);
        } else {
            put(key, new String[] { o.toString(), value });
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.iisigroup.cap.component.PageParameters#getAsDouble(java.lang.String)
     */
    @Override
    public Double getAsDouble(String key) {
        String s = getString(key, String.valueOf(0));
        return NumberUtils.isCreatable(s) ? Double.valueOf(s) : 0;
    }

}
