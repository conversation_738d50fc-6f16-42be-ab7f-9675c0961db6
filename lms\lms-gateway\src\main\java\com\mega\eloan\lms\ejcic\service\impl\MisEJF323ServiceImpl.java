/* 
 * MisEJF323ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.ejcic.service.MisEJF323Service;

/**
 * <pre>
 * MIS.KRS001>>MIS.EJV32301>>MIS.EJF323信用卡是否持卡及強停
 * </pre>
 * @since 2012/03/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/23,TimChiang,new
 *          </ul>
 */
@Service
public class MisEJF323ServiceImpl extends AbstractEjcicJdbc implements MisEJF323Service {

	@Override
	public List<Map<String, Object>> findCreditCardDisbRecord(String id) {
		return getJdbc().queryForList("KRS001.findCreditCardDisbRecord", new String[]{id});
	}

}
