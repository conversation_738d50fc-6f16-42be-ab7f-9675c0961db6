package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C900S03CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S03C;

/** DW_LNCUSTREL檔  **/
@Repository
public class C900S03CDaoImpl extends LMSJpaDao<C900S03C, String>
	implements C900S03CDao {

	@Override
	public C900S03C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public int countByCYC_MN(String CYC_MN){
		Query query = getEntityManager().createNamedQuery("C900S03C.countByCYC_MN");
		// 設置參數
		query.setParameter(1, CYC_MN);
		Integer count = (Integer) query.getSingleResult();
		return count.intValue();
	}
	
}