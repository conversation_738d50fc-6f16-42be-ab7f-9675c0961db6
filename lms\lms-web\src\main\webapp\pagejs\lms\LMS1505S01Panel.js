/*  pagejs/lms/LMS1505S01Panel.js  ── 簡化版  */
var initDfd = window.initDfd || $.Deferred();

initDfd.done(function (json) {

    /* === 下拉動態顯示 “自行輸入” === */
    const $selectTo = $(".selectTo");

    $selectTo.append(
        "<option value='0'>" + i18n.lms1505m01["L150M01a.useSelf"] + "</option>"
    ).change(function () {
        $(this).val() === "0"
            ? $(this).next(".theother").show()
            : $(this).next(".theother").hide();
    }).trigger("change");          // 頁面載入立即套用

    /* === 權限 / 人員清單 === */
    $("#chairMan,#lawsBoss").setItems({ item: json.mainPeopleMap });
    $("#accounting,#recorder").setItems({ item: json.allPeopleMap });

    $("#chairMan").val($.trim(json.chairMan));
    $("#lawsBoss") .val($.trim(json.lawsBoss));
    $("#accounting").val($.trim(json.accounting));
    $("#recorder") .val($.trim(json.recorder));

    /* === 主管單位 918 顯示控制 === */
    if (userInfo.unitNo === "918") {
        $(".hideFor918").show();   // 現在規格：永遠顯示
    } else {
        $(".hideFor918").show();
    }

    /* === 出席人員 thickbox（保留） === */
    $("#selectPeople").click(function () {

        // 帶入舊值
        $("#allPeopleShow").html(DOMPurify.sanitize($("#present").val()));

        $("#selectPeopleView").thickbox({
            title : i18n.lms1505m01["L150M01a.present"],
            width : 400,
            height: 400,
            align : "center",
            valign: "bottom",
            i18n  : i18n.def,
            modal : false,
            buttons : {
                "sure"  : function () {                      // 直接取 textarea 內容
                    $("#present").val($("#allPeopleShow").text());
                    $.thickbox.close();
                },
                "cancel": function () { $.thickbox.close(); }
            }
        });
    });
});
