package com.mega.eloan.lms.lms.flow;

import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L700M01ADao;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L700M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 案件簽報書 - 泰國提會流程
 * </pre>
 * 
 * @since 2011/11/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/2,REX,new
 *          </ul>
 */
@Component
public class LMS1205TH02Flow extends AbstractFlowHandler {

	public static final String FLOW_CODE = "LMS1205TH02Flow";

	@Resource
	CommonMetaDao metaDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L700M01ADao l700m01aDao;

	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	DocLogService docLogService;

	@Resource
	LMSService lmsService;

	// 判斷
	@Transition(node = "exclusive1", value = "to退回編製中")
	public void back(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		String nextOwnBrId = UtilConstants.BankNo.授管處;
		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())) {
			nextOwnBrId = UtilConstants.BankNo.授管處;
			instance.setAttribute("flowCode", LMS1205HeadFlow.FLOW_CODE);
		} else {
			nextOwnBrId = meta.getAreaBrId();
			instance.setAttribute("flowCode", LMS1205AreaFlow.FLOW_CODE);
		}
		// 下一個編製單位

		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnBrId,
				meta.getCaseBrId());
		meta.setOwnBrId(nextOwnBrId);
		if (l700m01a != null) {
			meta.setHqAppraiser(l700m01a.getUserNo());
		}

		meta.setHqMeetFlag(null);
		// 先判斷是否已經有這筆授權檔
		lmsService.saveL12A01A(meta, nextOwnBrId,
				DocAuthTypeEnum.VIEW_TRANSFER.getCode());
		// 新增一筆傳送紀錄以供近期已收案件用
		lmsService.saveL000M01A(meta, nextOwnBrId);

	}

	// 判斷核定還是婉卻
	@Transition(node = "提會待覆核", value = "to泰行提會")
	public void toCheck2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);

		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(meta.getMainId(),
						UtilConstants.Cntrdoc.ItemType.泰國額度批覆表,
						FlowDocStatusEnum.已核准.getCode());
		String result = (String) instance.getAttribute("result");
		if ("to核定".equals(result)) {
			// 檢查主管與經辦是否為同一人
			lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
					UtilConstants.STAFFJOB.提會登錄經辦L7);
			// 當無已核准額度明細表且 該案件非陳覆陳述案 為婉卻
			if (l140m01as.isEmpty()
					&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
							.getDocCode())) {
				instance.setAttribute("result", "to已婉卻");
			} else {
				instance.setAttribute("result", "to核定");

			}
		}

	}

	@Transition(node = "exclusive1", value = "to提會待覆核")
	public void accept(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.提會登錄經辦L7);
		// 對泰國而言文件狀態為母行法人提案意見，為自己的批覆書
		// 檢查是否批覆
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(meta.getMainId(),
						UtilConstants.Cntrdoc.ItemType.泰國額度批覆表, null);
		if (l140m01as.isEmpty()) {
			// EFD3015=WARN|尚未產生額度批覆表不得呈主管覆核！！|
			throw new FlowMessageException("EFD3015");
		}

		// 檢查額度明細表 的額度序號批附表是否都產生
		HashMap<String, String> checkCntrNo = new HashMap<String, String>();
		for (L140M01A l140m01a : l140m01as) {
			checkCntrNo.put(l140m01a.getCntrNo(), "");
			if (FlowDocStatusEnum.編製中.getCode().equals(l140m01a.getDocStatus())) {
				// EFD3012=WARN|所有額度批覆表需由經辦執行【批覆】後才能呈主管覆核|
				throw new FlowMessageException("EFD3012");
			}

			if (!UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				// EFD3024=WARN|額度批覆表有異動，請執行【計算授信額度合計】以確保額度批覆表之「授信額度合計」為正確數值。|
				throw new FlowMessageException("EFD3024");
			}
		}

		List<L140M01A> l140m01as1 = l140m01aDao
				.findL140m01aListByL120m01cMainIdOrderByCust(meta.getMainId(),
						UtilConstants.Cntrdoc.ItemType.額度明細表);
		StringBuffer cntrnoMsg = new StringBuffer();
		for (L140M01A l140m01a : l140m01as1) {
			// 當不存在把這額度序號記錄下來
			if (!checkCntrNo.containsKey(l140m01a.getCntrNo())) {
				cntrnoMsg.append(cntrnoMsg.length() > 0 ? "、" : "");
				cntrnoMsg.append(l140m01a.getCntrNo());
			}
		}
		// 要出現的額度序號訊息
		if (cntrnoMsg.length() > 0) {
			// EFD3036=ERROR|尚未產生額度序號$\{cntrNo\}額度批覆表不得呈主管覆核！|
			HashMap<String, String> extraMessage = new HashMap<String, String>();
			extraMessage.put("cntrNo", " " + cntrnoMsg.toString() + " ");
			FlowMessageException msg = new FlowMessageException("EFD3036");
			msg.setExtraMessage(extraMessage);
			throw msg;
		}
	}

	// 核定
	@Transition(node = "泰國提會判斷", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.提會放行主管L8);
	}

	// 婉卻
	@Transition(node = "泰國提會判斷", value = "to已婉卻")
	public void reject(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.提會放行主管L8);
	}

	/**
	 * 當案件結束時做的動作
	 * 
	 * @param instance
	 *            flow 流程檔
	 * @param docstatus
	 *            文件狀態
	 * @param staffjob
	 *            職稱欄
	 * @throws CapException
	 * @throws FlowException
	 */
	private void toEndAction(FlowInstance instance, String docstatus,
			String staffjob) throws FlowException, CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		String docRslt = "";
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			docRslt = UtilConstants.Casedoc.DocRslt.承做;
		} else {
			docRslt = UtilConstants.Casedoc.DocRslt.婉卻;
		}
		lmsService.uploadELLNSEEK(meta);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(meta.getMainId(),
						UtilConstants.Cntrdoc.ItemType.泰國額度批覆表, null);
		for (L140M01A l140m01a : l140m01as) {
			l140m01a.setApprover(user.getUserId());
			l140m01a.setApproveTime(CapDate.getCurrentTimestamp());
		}
		meta.setDocRslt(docRslt);
		meta.setOwnBrId(meta.getCaseBrId());
		l140m01aDao.save(l140m01as);
		l120m01aDao.save(meta);
		// 要去除多餘的營運中心授權檔，不然在已核准或已婉卻會多出現
		lmsService.checkL120A01A(meta);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行, staffjob);
		// 更新預約額度檔
		lmsService.gfnDB2SetELF442_CNTRNO(meta,
				UtilConstants.Cntrdoc.ACTION.覆核, docstatus);
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta,
				UtilConstants.Cntrdoc.ItemType.泰國額度批覆表, docstatus,
				meta.getCaseBrId());
		lmsService.upLoadMIS(meta);
		lmsService.upLnunid(meta);
		
		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			if (!LMSUtil.isClsCase(meta)) {
				lmsService.uploadL902Data(meta, docstatus);
			}
		}
		
		
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}