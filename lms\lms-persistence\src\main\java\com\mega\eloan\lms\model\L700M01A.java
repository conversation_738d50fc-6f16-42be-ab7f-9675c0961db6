/* 
 * L700M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

//import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 案件分案對照表檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L700M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"ownBrId","branchId","subject"}))
public class L700M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 建檔單位<p/>
	 * 2011/08/23新增：<br/>
	 *  營運中心與授管處皆有自動派案功能
	 */
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 分行別代碼 **/
	@Column(name="BRANCHID", length=3, columnDefinition="CHAR(3)")
	private String branchId;

	/** 
	 * 科(組)別<p/>
	 * ※僅007國外部才需勾選<br/>
	 *  1.第一科<br/>
	 *  2.第二科<br/>
	 *  3.第三科
	 */
	@Column(name="SUBJECT", length=1, columnDefinition="VARCHAR(1)")
	private String subject;

	/** 隸屬區域營運中心代碼 **/
	@Column(name="GROUPID", length=3, columnDefinition="VARCHAR(3)")
	private String groupId;

	/** 負責經辦人員號碼 **/
	@Column(name="USERNO", length=6, columnDefinition="CHAR(6)")
	private String userNo;

	/** 
	 * 負責主管人員號碼<p/>
	 * ※此欄主要供總行產生議程用
	 */
	@Column(name="RECHECK", length=6, columnDefinition="CHAR(6)")
	private String reCheck;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得建檔單位<p/>
	 * 2011/08/23新增：<br/>
	 *  營運中心與授管處皆有自動派案功能
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/**
	 *  設定建檔單位<p/>
	 *  2011/08/23新增：<br/>
	 *  營運中心與授管處皆有自動派案功能
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}
	
	/** 取得分行別代碼 **/
	public String getBranchId() {
		return this.branchId;
	}
	/** 設定分行別代碼 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 
	 * 取得科(組)別<p/>
	 * ※僅007國外部才需勾選<br/>
	 *  1.第一科<br/>
	 *  2.第二科<br/>
	 *  3.第三科
	 */
	public String getSubject() {
		return this.subject;
	}
	/**
	 *  設定科(組)別<p/>
	 *  ※僅007國外部才需勾選<br/>
	 *  1.第一科<br/>
	 *  2.第二科<br/>
	 *  3.第三科
	 **/
	public void setSubject(String value) {
		this.subject = value;
	}

	/** 取得隸屬區域營運中心代碼 **/
	public String getGroupId() {
		return this.groupId;
	}
	/** 設定隸屬區域營運中心代碼 **/
	public void setGroupId(String value) {
		this.groupId = value;
	}

	/** 取得負責經辦人員號碼 **/
	public String getUserNo() {
		return this.userNo;
	}
	/** 設定負責經辦人員號碼 **/
	public void setUserNo(String value) {
		this.userNo = value;
	}

	/** 
	 * 取得負責主管人員號碼<p/>
	 * ※此欄主要供總行產生議程用
	 */
	public String getReCheck() {
		return this.reCheck;
	}
	/**
	 *  設定負責主管人員號碼<p/>
	 *  ※此欄主要供總行產生議程用
	 **/
	public void setReCheck(String value) {
		this.reCheck = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	/* (non-Javadoc)
	 * @see com.mega.eloan.common.model.IDocObject#getMainId()
	 */
	@Override
	public String getMainId() {
		// TODO Auto-generated method stub
		return null;
	}
}
