/* 
 * L140S02J.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 外勞貸款檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S02J", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","seq"}))
public class L140S02J extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="SEQ", columnDefinition="DECIMAL(5,0)")
	private Integer seq;

	/** 仲介公司統一編號 **/
	@Size(max=10)
	@Column(name="INTROID", length=10, columnDefinition="VARCHAR(10)")
	private String introId;

	/** 仲介公司重覆序號 **/
	@Size(max=1)
	@Column(name="INTRODUPNO", length=1, columnDefinition="CHAR(1)")
	private String introDupNo;

	/** 隸屬之仲介公司 **/
	@Size(max=120)
	@Column(name="INTRONAME", length=120, columnDefinition="VARCHAR(120)")
	private String introName;

	/** 
	 * 仲介獎金比率<p/>
	 * ％
	 */
	@Digits(integer=3, fraction=2, groups = Check.class)
	@Column(name="INTRORATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal introRate;

	/** 居留證核發日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="HABITDATE", columnDefinition="DATE")
	private Date habitDate;

	/** 居留證字號 **/
	@Size(max=20)
	@Column(name="HABITID", length=20, columnDefinition="VARCHAR(20)")
	private String habitId;

	/** 
	 * 償還辦理方式<p/>
	 * 複選：(eg.1|2|3)<br/>
	 *  1.借款人於本行開立A/C存款帳戶，授權本行至該帳戶扣期付金，且雇主/仲介公司承諾按月將借款人薪資存入該帳戶。<br/>
	 *  2.借款人之雇主出具承諾書代扣應攤還本息。<br/>
	 *  3.借款人之仲介公司出具承諾書代扣應攤還本息。
	 */
	@Size(max=5)
	@Column(name="SUBPAYWAY", length=5, columnDefinition="VARCHAR(5)")
	private String subPayWay;

	/** 
	 * A/C存款帳戶<p/>
	 * ※可自系統選取存款帳號或自行輸出
	 */
	@Size(max=14)
	@Column(name="ACCNO", length=14, columnDefinition="VARCHAR(14)")
	private String accNo;

	/** 
	 * 承諾存款<p/>
	 * 單選：<br/>
	 *  1.雇主<br/>
	 *  2.仲介公司
	 */
	@Size(max=1)
	@Column(name="AGENCY", length=1, columnDefinition="CHAR(1)")
	private String agency;

	/** 
	 * 保證金<p/>
	 * ※每一仲介公司應於初次撥貸時，依撥貸總額提存10％現金，以「存入保證金－其他」科目出帳，作為案下擔保品，之後再有新撥貸案件，於撥貸時，即以該仲介公司引介外勞貸款。<br/>
	 *  複選：(eg.1|2|3)<br/>
	 *  1.前一日餘額加計新撥貸金額為基準，核算調整存入保證金應有金額。<br/>
	 *  2.前一日已撥貸總金額加計新撥貸金額再扣除已結清之個案金額為基準，核算調整存入保證金應有金額。<br/>
	 *  3.每筆外勞貸款保留10%當做存入保證金，於該筆貸款還清時退還外勞。
	 */
	@Size(max=5)
	@Column(name="GUATYPE", length=5, columnDefinition="VARCHAR(5)")
	private String guaType;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}
	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得仲介公司統一編號 **/
	public String getIntroId() {
		return this.introId;
	}
	/** 設定仲介公司統一編號 **/
	public void setIntroId(String value) {
		this.introId = value;
	}

	/** 取得仲介公司重覆序號 **/
	public String getIntroDupNo() {
		return this.introDupNo;
	}
	/** 設定仲介公司重覆序號 **/
	public void setIntroDupNo(String value) {
		this.introDupNo = value;
	}

	/** 取得隸屬之仲介公司 **/
	public String getIntroName() {
		return this.introName;
	}
	/** 設定隸屬之仲介公司 **/
	public void setIntroName(String value) {
		this.introName = value;
	}

	/** 
	 * 取得仲介獎金比率<p/>
	 * ％
	 */
	public BigDecimal getIntroRate() {
		return this.introRate;
	}
	/**
	 *  設定仲介獎金比率<p/>
	 *  ％
	 **/
	public void setIntroRate(BigDecimal value) {
		this.introRate = value;
	}

	/** 取得居留證核發日期 **/
	public Date getHabitDate() {
		return this.habitDate;
	}
	/** 設定居留證核發日期 **/
	public void setHabitDate(Date value) {
		this.habitDate = value;
	}

	/** 取得居留證字號 **/
	public String getHabitId() {
		return this.habitId;
	}
	/** 設定居留證字號 **/
	public void setHabitId(String value) {
		this.habitId = value;
	}

	/** 
	 * 取得償還辦理方式<p/>
	 * 複選：(eg.1|2|3)<br/>
	 *  1.借款人於本行開立A/C存款帳戶，授權本行至該帳戶扣期付金，且雇主/仲介公司承諾按月將借款人薪資存入該帳戶。<br/>
	 *  2.借款人之雇主出具承諾書代扣應攤還本息。<br/>
	 *  3.借款人之仲介公司出具承諾書代扣應攤還本息。
	 */
	public String getSubPayWay() {
		return this.subPayWay;
	}
	/**
	 *  設定償還辦理方式<p/>
	 *  複選：(eg.1|2|3)<br/>
	 *  1.借款人於本行開立A/C存款帳戶，授權本行至該帳戶扣期付金，且雇主/仲介公司承諾按月將借款人薪資存入該帳戶。<br/>
	 *  2.借款人之雇主出具承諾書代扣應攤還本息。<br/>
	 *  3.借款人之仲介公司出具承諾書代扣應攤還本息。
	 **/
	public void setSubPayWay(String value) {
		this.subPayWay = value;
	}

	/** 
	 * 取得A/C存款帳戶<p/>
	 * ※可自系統選取存款帳號或自行輸出
	 */
	public String getAccNo() {
		return this.accNo;
	}
	/**
	 *  設定A/C存款帳戶<p/>
	 *  ※可自系統選取存款帳號或自行輸出
	 **/
	public void setAccNo(String value) {
		this.accNo = value;
	}

	/** 
	 * 取得承諾存款<p/>
	 * 單選：<br/>
	 *  1.雇主<br/>
	 *  2.仲介公司
	 */
	public String getAgency() {
		return this.agency;
	}
	/**
	 *  設定承諾存款<p/>
	 *  單選：<br/>
	 *  1.雇主<br/>
	 *  2.仲介公司
	 **/
	public void setAgency(String value) {
		this.agency = value;
	}

	/** 
	 * 取得保證金<p/>
	 * ※每一仲介公司應於初次撥貸時，依撥貸總額提存10％現金，以「存入保證金－其他」科目出帳，作為案下擔保品，之後再有新撥貸案件，於撥貸時，即以該仲介公司引介外勞貸款。<br/>
	 *  複選：(eg.1|2|3)<br/>
	 *  1.前一日餘額加計新撥貸金額為基準，核算調整存入保證金應有金額。<br/>
	 *  2.前一日已撥貸總金額加計新撥貸金額再扣除已結清之個案金額為基準，核算調整存入保證金應有金額。<br/>
	 *  3.每筆外勞貸款保留10%當做存入保證金，於該筆貸款還清時退還外勞。
	 */
	public String getGuaType() {
		return this.guaType;
	}
	/**
	 *  設定保證金<p/>
	 *  ※每一仲介公司應於初次撥貸時，依撥貸總額提存10％現金，以「存入保證金－其他」科目出帳，作為案下擔保品，之後再有新撥貸案件，於撥貸時，即以該仲介公司引介外勞貸款。<br/>
	 *  複選：(eg.1|2|3)<br/>
	 *  1.前一日餘額加計新撥貸金額為基準，核算調整存入保證金應有金額。<br/>
	 *  2.前一日已撥貸總金額加計新撥貸金額再扣除已結清之個案金額為基準，核算調整存入保證金應有金額。<br/>
	 *  3.每筆外勞貸款保留10%當做存入保證金，於該筆貸款還清時退還外勞。
	 **/
	public void setGuaType(String value) {
		this.guaType = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
