package com.mega.eloan.lms.batch.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ScoreServiceJP;
import com.mega.eloan.lms.dao.C121M01ADao;
import com.mega.eloan.lms.lms.service.LMS1015Service;
import com.mega.eloan.lms.lms.service.LMS1025Service;
import com.mega.eloan.lms.lms.service.LMS1035Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L140M01C;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'mockOverSeaRatingServiceImpl','request':{'step':'1'}}
 * http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'mockOverSeaRatingServiceImpl','request':{'step':'2'}}
 * http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'mockOverSeaRatingServiceImpl','request':{'step':'3'}}
 */
@Service("mockOverSeaRatingServiceImpl")
public class MockOverSeaRatingServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(MockOverSeaRatingServiceImpl.class);
	private static final String MOCK_DOC_STATUS = "TTT";
	private static final String MY_TS = "2023-01-01 00:00:00";
	/*
	 delete from lms.c120m01a where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 delete from lms.c120s01a where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 delete from lms.c120s01b where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 delete from lms.c120s01c where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 delete from lms.c120s01d where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 delete from lms.c120s01e where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 
	 delete from lms.c121s01a where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 delete from lms.c121m01b where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 delete from lms.c121m01c where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 
	 delete from lms.c121m01a where mainid in (select mainid from lms.c121m01a where docStatus='TTT');
	 */
	/*
	 delete from dwadm.OTS_RKSCOREOVS     where custId like 'ZZ%';
	 delete from dwadm.OTS_RKCREDITOVS    where custId like 'ZZ%';
	 delete from dwadm.OTS_RKJCICOVS      where custId like 'ZZ%';
	 delete from dwadm.OTS_RKAPPLICANTOVS where custId like 'ZZ%';	 
	 */
	/*
	 delete from dwadm.OTS_RKSCOREOVS     ;
	 delete from dwadm.OTS_RKCREDITOVS    ;
	 delete from dwadm.OTS_RKJCICOVS      ;
	 delete from dwadm.OTS_RKAPPLICANTOVS ;	 
	 delete from dwadm.OTS_RKPROJECTOVS   ;
	 delete from dwadm.OTS_RKADJUSTOVS    ;
	 delete from dwadm.OTS_RKCNTRNOOVS    ;
	 delete from dwadm.OTS_RKCOLLOVS      ;
	 */
	@Resource
	C121M01ADao c121m01aDao;
	
	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	LMS1015Service lms1015Service;
	
	@Resource
	LMS1025Service lms1025Service;
	
	@Resource
	LMS1035Service lms1035Service;
	
	@Resource
	ScoreServiceJP scoreServiceJP;
	
	@Override
	public JSONObject execute(JSONObject json) {
		
		JSONObject result = new JSONObject();
		JSONObject rq = json.getJSONObject("request");
		
		String step = rq.optString("step");	
		LOGGER.info("passed_param["+step+"]");
		try {
			if (Util.equals("1", step)){
				result.putAll(step1());
			}else if (Util.equals("2", step)){
				result.putAll(step2());
			}else if (Util.equals("3", step)){
				result.putAll(step3());
			}
		} catch (Exception ex) {
			result.put("success", false);
			LOGGER.error("[execute] Exception!!", ex);
		} finally {
			result.put("success", true);
		}

		LOGGER.info("end");
		return result;
	}
	
	/**
	 * 準備基本檔
	 */
	private JSONObject step1()throws CapException{
		
		JSONObject result = new JSONObject();
		
		String ownBrId = "0A7";
//		ownBrId = "0B9";
//		ownBrId = "Y01";
		List<String> id_list = new ArrayList<String>();
		String mowType = "";
		if(LMSUtil.get_AU_BRNO_SET().contains(ownBrId)){
			mowType = OverSeaUtil.C121M01A_MOW_TYPE_澳洲;  
			id_list.addAll(_TesAUId());
		}else if(LMSUtil.get_JP_BRNO_SET().contains(ownBrId)){
			mowType = OverSeaUtil.C121M01A_MOW_TYPE_日本;
			id_list.addAll(_TesJPId());
		}else if(LMSUtil.get_TH_BRNO_SET().contains(ownBrId)){
			mowType = OverSeaUtil.C121M01A_MOW_TYPE_泰國;
			id_list.addAll(_TesTHId());
		}
				
		String dupNo = "0"; 
		BigDecimal houseArea = new BigDecimal("100");
		Timestamp createTime = CapDate.getCurrentTimestamp();		
		int size = id_list.size();
		int cnt = 0;
		for(int i=0;i<size;i++){
			String custId = id_list.get(i);
			String uuid = IDGenerator.getUUID();
			//---------
			C121M01A c121m01a = new C121M01A();			
			c121m01a.setMainId(uuid);
			c121m01a.setCustId(custId);
			c121m01a.setDupNo(dupNo);
			c121m01a.setOwnBrId(ownBrId);
			c121m01a.setCaseBrId(ownBrId);
			c121m01a.setRatingId(uuid);
			c121m01a.setDocStatus(MOCK_DOC_STATUS);
			c121m01a.setCreateTime(createTime);
			c121m01a.setMowType(mowType);
			//---------
			C121S01A c121s01a = new C121S01A();			
			c121s01a.setMainId(uuid);
			c121s01a.setRatingId(uuid);
			c121s01a.setCmsType("1");
			c121s01a.setHouseAge("2");
			c121s01a.setHouseArea(houseArea);
			//---------
			C120M01A c120m01a = new C120M01A();
			c120m01a.setMainId(uuid);
			c120m01a.setCustId(custId);
			c120m01a.setDupNo(dupNo);
			c120m01a.setOwnBrId(ownBrId);
			c120m01a.setKeyMan("Y");
			//---------
			C120S01A c120s01a = new C120S01A();
			c120s01a.setMainId(c120m01a.getMainId());
			c120s01a.setCustId(c120m01a.getCustId());
			c120s01a.setDupNo(c120m01a.getDupNo());
			c120s01a.setBusCode("060000");
			//---------
			C120S01B c120s01b = new C120S01B();
			c120s01b.setMainId(c120m01a.getMainId());
			c120s01b.setCustId(c120m01a.getCustId());
			c120s01b.setDupNo(c120m01a.getDupNo());
			//---------
			C120S01C c120s01c = new C120S01C();
			c120s01c.setMainId(c120m01a.getMainId());
			c120s01c.setCustId(c120m01a.getCustId());
			c120s01c.setDupNo(c120m01a.getDupNo());
			//---------
			C120S01E c120s01e = new C120S01E();
			c120s01e.setMainId(c120m01a.getMainId());
			c120s01e.setCustId(c120m01a.getCustId());
			c120s01e.setDupNo(c120m01a.getDupNo());
			//---------
			chgFactor(c121m01a, c121s01a, c120m01a, c120s01a, c120s01b, c120s01c, c120s01e);
			
			clsService.save(c121m01a, c121s01a, c120m01a, c120s01a, c120s01b, c120s01c, c120s01e);
			cnt++;
		}
		LOGGER.info("procCnt:"+cnt);
		
		return result;
	}
		
	/**
	 * 計算分數
	 */
	private JSONObject step2() throws Exception{
		JSONObject result = new JSONObject();
				
		ISearch search = c121m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", MY_TS);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", "05O");
		search.addSearchModeParameters(SearchMode.EQUALS, "mowType", OverSeaUtil.C121M01A_MOW_TYPE_日本);	
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "custId", "");
		search.setMaxResults(Integer.MAX_VALUE);
		String varVerNow = scoreServiceJP.get_Version_JP();
		List<C121M01A> list = c121m01aDao.find(search);
		int cnt = 0;
		for(C121M01A c121m01a : list){
			if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())){
				if(varVerNow.equals(OverSeaUtil.V2_0_LOAN_JP)){
					lms1015Service.calc_C121_score_v2_0(c121m01a);
				}else{
					lms1015Service.calc_C121_score(c121m01a); 		
				}
			}else if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲, c121m01a.getMowType())){
				lms1025Service.calc_C121_score(c121m01a);
			}else if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國, c121m01a.getMowType())){
				lms1035Service.calc_C121_score(c121m01a);
			}else{
				continue;
			}			
			cnt++;
		}
		LOGGER.info("procCnt:"+cnt);
		return result;
	}
	
	/**
	 * 上傳DW
	 */
	private JSONObject step3() throws Exception{
		JSONObject result = new JSONObject();
		

		ISearch search = c121m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", MY_TS);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", "05O");
		search.addSearchModeParameters(SearchMode.EQUALS, "mowType", OverSeaUtil.C121M01A_MOW_TYPE_日本);	
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "custId", "");
		search.setMaxResults(Integer.MAX_VALUE);
		List<C121M01A> list = c121m01aDao.find(search);
		int cnt = 0;
		for(C121M01A c121m01a : list){
			int upCnt = 0;
			C121S01A c121s01a = clsService.findC121S01A(c121m01a);
			
			for(C120M01A c120m01a: clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(c121m01a)){
				GenericBean c121m01_grade = null;
				if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())){
					//房貸
					c121m01_grade = clsService.findC121M01B_byC120M01A(c120m01a); 			
					//非房貸
//					c121m01_grade = clsService.findC121M01F_byC120M01A(c120m01a); 		
				}else if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲, c121m01a.getMowType())){
					c121m01_grade = clsService.findC121M01C_byC120M01A(c120m01a);
				}else if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國, c121m01a.getMowType())){
					c121m01_grade = clsService.findC121M01D_byC120M01A(c120m01a);
				}
				if(c121m01_grade==null){
					continue;
				}
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				C120S01B c120s01b = clsService.findC120S01B(c120m01a);
				C120S01C c120s01c = clsService.findC120S01C(c120m01a);
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				L140M01C l140m01c = new L140M01C();
				if (true) {
					l140m01c.setLoanTP("202");
					l140m01c.setC121CustId(c121m01a.getCustId());
					l140m01c.setC121DupNo(c121m01a.getDupNo());
					l140m01c.setModelType("M"); //房貸
//					l140m01c.setModelType("N"); //非房貸
				}
				clsService.verify_C121M01A(c121m01a, c121s01a, c120m01a, 
						c120s01a, c120s01b, c120s01c, c120s01e, c121m01_grade, l140m01c);
				upCnt++;		
			}
			LOGGER.info("procCnt["+cnt+"]"+upCnt+(upCnt==0?"--- zzz_fail":""));
			cnt++;
		}
		LOGGER.info("procCnt:"+cnt);	
		return result;
	}

	private List<String> _TesAUId(){
		List<String> r = new ArrayList<String>();
		r.add("ZZAU101101");
		r.add("ZZAU211212");
		r.add("ZZAU312313");
		r.add("ZZAU402424");
		r.add("ZZAU503531");
		r.add("ZZAU613602");
		return r;
	}
	
	private void chgFactor(C121M01A c121m01a, C121S01A c121s01a, C120M01A c120m01a, C120S01A c120s01a, 
			C120S01B c120s01b,
			C120S01C c120s01c,
			C120S01E c120s01e){
		if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_日本, c121m01a.getMowType())){
			chgFactorJP(c121m01a, c121s01a, c120m01a, c120s01a, c120s01b, c120s01c, c120s01e);
		}else if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_澳洲, c121m01a.getMowType())){
			chgFactorAU(c121m01a, c121s01a, c120m01a, c120s01a, c120s01b, c120s01c, c120s01e);
		}else if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國, c121m01a.getMowType())){
			chgFactorTH(c121m01a, c121s01a, c120m01a, c120s01a, c120s01b, c120s01c, c120s01e);
		}
	}
	private void chgFactorAU(C121M01A c121m01a, C121S01A c121s01a, C120M01A c120m01a, C120S01A c120s01a, 
			C120S01B c120s01b,
			C120S01C c120s01c,
			C120S01E c120s01e){
		/*
		M1_age|M5_occupation|M7_seniority|D1_ICR|P3_hincome|A5_loan_period|O1_vedascore|Z1|Z2	
		*/
		String custId = c120m01a.getCustId();
		String custId5 = custId.substring(4, 5);
		String custId6 = custId.substring(5, 6);
		String custId7 = custId.substring(6, 7);
		String custId8 = custId.substring(7, 8);
		String custId9 = custId.substring(8, 9);
		String custId10 = custId.substring(9, 10);
		//M1
		if(true){
			int v = Util.parseInt(custId5)%4;
			String date = "0001-01-01";
			if(v==0){
				date = "1992-03-08";
			}else if(v==1){
				date = "1979-06-12";
			}else if(v==2){
				date = "1965-09-22";
			}else if(v==3){
				date = "1952-10-06";
			}
			c120s01a.setBirthday(CapDate.parseDate(date));
		}
		//M5
		if(true){
			int v = Util.parseInt(custId6)%2;
			String jobType1 = "01";
			if(v==0){
				jobType1 = "05";
			}else if(v==1){
				jobType1 = "11";
			}
			c120s01b.setJobType1(jobType1);
		}
		//M7
		if(true){
			int v = Util.parseInt(custId7)%2;
			int seniority = 7;
			if(v==0){
				seniority = 7;
			}else if(v==1){
				seniority = 20;
			}
			c120s01b.setSeniority(new BigDecimal(seniority));
		}
		//D1
		if(true){
			int v = Util.parseInt(custId8)%6;
						
			String issueLC = "";
			String icrFlag = "";
			String icr = "";
			
			if(v==0){
				issueLC = "N";
				icrFlag = "Y";
				icr = "0.89";				
			}else if(v==1){
				issueLC = "N";
				icrFlag = "Y";
				icr = "1.23";
			}else if(v==2){
				issueLC = "N";
				icrFlag = "Y";
				icr = "2.34";
			}else if(v==3){
				issueLC = "N";
				icrFlag = "Y";
				icr = "3.17";
			}else if(v==4){
				issueLC = "Y";
				icrFlag = "N";
				icr = null;
			}else if(v==5){
				issueLC = "N";
				icrFlag = "N";
				icr = null;
			}
			c120s01c.setIssueLC(issueLC);
			c120s01c.setICRFlag(icrFlag);
			c120s01c.setICR(Util.isEmpty(icr)?null:new BigDecimal(icr));			
		}
		//P3_hincome
		if(true){
			int v = Util.parseInt(custId9)%4;
			String curr = "AUD";
			String amt = "";
			if(v==0){
				amt = "56999";
			}else if(v==1){
				amt = "62130";
			}else if(v==2){
				amt = "75000";
			}else if(v==3){
				amt = null;
			}
			c120s01c.setYFamCurr(curr);
			c120s01c.setYFamAmt(Util.isEmpty(amt)?null:new BigDecimal(amt));	
		}
		//A5_loan_period
		if(true){
			int v = Util.parseInt(custId8)%6;
			int lnYear = 0;
			int lnMonth = 0;
			if(v==0 || v==1){
				lnYear = 1;
				lnMonth = 0;
			}else if(v==2 || v==3){
				lnYear = 4;
				lnMonth = 6;
			}else if(v==4 || v==5){
				lnYear = 12;
				lnMonth = 0;
			}
			c121m01a.setLnYear(lnYear);
			c121m01a.setLnMonth(lnMonth);
		}
		//O1_vedascore
		if(true){
			int v = Util.parseInt(custId10)%4;
			String vedaRecord = "Y";
			Date vedaQDate = CapDate.getCurrentTimestamp();
			String vedaScoreFlag = "1";
			Integer vedaScore = null;
			String vedaAdverseFile = "2";
			String vedaDefaultAmt = "2";
			String vedaJudgement = "2";
			String vedaEnquiriesFlag = "2";
			Integer vedaEnquiriesTimes = null;
			String vedaProvider = "2";
			String vedaFileAgeFlag = "1";
			int vedaFileAge = 5;
			if(v==0){
				vedaScore = 680;
				
				vedaEnquiriesFlag = "1";
				vedaEnquiriesTimes = 6;
			}else if(v==1){
				vedaScore = 770;				
				
			}else if(v==2){
				vedaScore = 900;
				
				vedaFileAge = 9;
			}else if(v==3){
				vedaRecord = "N";
				vedaQDate = null;
				vedaScoreFlag = "3";
				vedaScore = null;
			}
			c120s01e.setVedaQDate(vedaQDate);
			c120s01e.setVedaRecord(vedaRecord);
			c120s01e.setVedaScoreFlag(vedaScoreFlag);
			c120s01e.setVedaScore(vedaScore);
			
			c120s01e.setVedaAdverseFile(vedaAdverseFile);
			c120s01e.setVedaDefaultAmt(vedaDefaultAmt);
			c120s01e.setVedaJudgement(vedaJudgement);
			
			c120s01e.setVedaEnquiriesFlag(vedaEnquiriesFlag);
			c120s01e.setVedaEnquiriesTimes(vedaEnquiriesTimes);
			c120s01e.setVedaProvider(vedaProvider);
			
			c120s01e.setVedaFileAgeFlag(vedaFileAgeFlag);
			c120s01e.setVedaFileAge(vedaFileAge);
		}
		//Z1
		if(true){
			int v = Util.parseInt(custId8)%6;
			int factor1 = 0;
			if(v==0 || v==1){
				factor1 = 5;
			}else if(v==2){
				factor1 = 4;
			}else if(v==3){
				factor1 = 3;
			}else if(v==4){
				factor1 = 2;
			}else if(v==5){
				factor1 = 1;
			}
			c121s01a.setFactor1(factor1);
		}
		//Z2
		if(true){
			int v = Util.parseInt(custId8)%6;
			int factor2 = 0;
			if(v==0 || v==1){
				factor2 = 4;
			}else if(v==2 || v==3){
				factor2 = 3;
			}else if(v==4){
				factor2 = 2;
			}else if(v==5){
				factor2 = 1;
			}
			c121s01a.setFactor2(factor2);
		}
		if(true){
			//負面資訊
			//寫在 O1_vedascore
		}
	}
	
	private List<String> _TesJPId(){
		List<String> r = new ArrayList<String>();
		r.add("ZZJP101161");
		r.add("ZZJP211252");
		r.add("ZZJP312313");
		r.add("ZZJP402424");
		r.add("ZZJP503531");
		r.add("ZZJP613642");
		r.add("ZZJP611702");
		r.add("ZZJP311841");
		r.add("ZZJP312941");
		return r;
	}
	private void chgFactorJP(C121M01A c121m01a, C121S01A c121s01a, C120M01A c120m01a, C120S01A c120s01a, 
			C120S01B c120s01b,
			C120S01C c120s01c,
			C120S01E c120s01e){
		
		String custId = c120m01a.getCustId();
		String custId5 = custId.substring(4, 5);
		String custId6 = custId.substring(5, 6);
		String custId7 = custId.substring(6, 7);
		String custId8 = custId.substring(7, 8);
		String custId9 = custId.substring(8, 9);
		String custId10 = custId.substring(9, 10);
		//M1
		if(true){
			int v = Util.parseInt(custId5)%3;
			String date = "0001-01-01";
			if(v==0){
				date = "1975-02-23";
			}else if(v==1){
				date = "1965-03-30";
			}else if(v==2){
				date = "1955-04-25";
			}
			c120s01a.setBirthday(CapDate.parseDate(date));
		}
		//M5
		if(true){
			int v = Util.parseInt(custId6)%2;
			String jobType1 = "01";
			if(v==0){
				jobType1 = "05";
			}else if(v==1){
				jobType1 = "11";
			}
			c120s01b.setJobType1(jobType1);
		}
		//M7
		if(true){
			int v = Util.parseInt(custId7)%3;
			int seniority = 7;
			if(v==0){
				seniority = 5;
			}else if(v==1){
				seniority = 16;
			}else if(v==2){
				seniority = 22;
			}
			c120s01b.setSeniority(new BigDecimal(seniority));
		}
		
		//P2
		if(true){
			int v = Util.parseInt(custId8)%4;
			String curr = "JPY";
			String amt = "";
			if(v==0){
				amt = "2499000";
			}else if(v==1){
				amt = "6543000";
			}else if(v==2){
				amt = "9876000";
			}else if(v==3){
				amt = "12345000";
			}
			c120s01b.setPayCurr(curr);
			c120s01b.setPayAmt(Util.isEmpty(amt)?null:new BigDecimal(amt));	
			
			c120s01c.setOMoneyCurr(curr);
			c120s01c.setOMoneyAmt(null);
		}
		//A5_loan_period
		if(true){
			int v = Util.parseInt(custId9)%6;
			int lnYear = 0;
			int lnMonth = 0;
			if(v==0 || v==1){
				lnYear = 8;
				lnMonth = 0;
			}else if(v==2 || v==3){
				lnYear = 12;
				lnMonth = 0;
			}else if(v==4 || v==5){
				lnYear = 16;
				lnMonth = 0;
			}
			c121m01a.setLnYear(lnYear);
			c121m01a.setLnMonth(lnMonth);
		}

		//Z1
		if(true){
			int v = Util.parseInt(custId9)%6;
			c121s01a.setFactor1(v+1);
		}
		//Z2
		if(true){
			int v = Util.parseInt(custId10)%4;
			c121s01a.setFactor2(v+1);
		}
		if(true){
			//負面資訊
			c120s01e.setIsQdata9("2");//退票
			c120s01e.setIsQdata10("2");//拒往
			c120s01e.setIsQdata13("2");//信用卡強停
			c120s01e.setIsQdata11("2");//催收呆帳
			
			c120s01e.setIsQdata19("2");
			
			c120s01e.setIsQdata21("2");
			c120s01e.setCntQdata21(null);
		
			c120s01e.setIsQdata22("2");
			c120s01e.setCntQdata22(null);
		
			c120s01e.setIsQdata24("2");
			c120s01e.setCntQdata24(null);
		
			c120s01e.setIsQdata20("2");
		
			c120s01e.setIsQdata23("2");
			c120s01e.setCntQdata23(null);
		
			c120s01e.setIsQdata25("2");
			
			if(true){
				c120s01e.setIsQdata26("2");
				c120s01e.setBalQdata26(null);
				
				c120s01e.setIsQdata28("2");
				c120s01e.setBalQdata28(null);
				//bal26 - bal28
			}
		
			c120s01e.setIsQdata27("2");
			c120s01e.setCntQdata27(null);
			
			//若為C-DF. D-降1等
			c120s01e.setJ10_score_flag("A");
			c120s01e.setJ10_score(823);
			//===============
			if(Util.equals(custId8, "0")||Util.equals(custId8, "1")){
				c120s01e.setIsQdata13("1");//信用卡強停
				
			}else if(Util.equals(custId8, "2") || Util.equals(custId8, "3")){
				
							
			}else if(Util.equals(custId8, "4")){
				c120s01e.setJ10_score(453);
				
				//風險2點,降1等
				c120s01e.setIsQdata21("1");
				c120s01e.setCntQdata21(3);
			}else if(Util.equals(custId8, "5")){
				c120s01e.setJ10_score(600);
				
				//風險0點
				c120s01e.setIsQdata24("1");
				c120s01e.setCntQdata24(1);
				
			}else if(Util.equals(custId8, "6")){
				c120s01e.setJ10_score_flag("C");
				c120s01e.setJ10_score(null);
				
			}else if(Util.equals(custId8, "7")){
				c120s01e.setJ10_score_flag("D");
				c120s01e.setJ10_score(null);
				
			}else if(Util.equals(custId8, "8")){
				c120s01e.setJ10_score(480);
				
				//申貸查詢時無擔保授信(扣除學生助學貸款)餘額超過新台幣1,000仟元 , 1點
				c120s01e.setIsQdata26("1");
				c120s01e.setBalQdata26(new BigDecimal("1200"));
				
				c120s01e.setIsQdata28("1");
				c120s01e.setBalQdata28(new BigDecimal("90"));
			}else if(Util.equals(custId8, "9")){
				
				c120s01e.setJ10_score(900);	
				//風險4點
				c120s01e.setIsQdata19("1");
				//風險3點
				c120s01e.setIsQdata24("1");
				c120s01e.setCntQdata24(3);
				//合計7點,降3等
			}
		}
		
	}
	
	private List<String> _TesTHId(){
		List<String> r = new ArrayList<String>();
		
		return r;
	}
	private void chgFactorTH(C121M01A c121m01a, C121S01A c121s01a, C120M01A c120m01a, C120S01A c120s01a, 
			C120S01B c120s01b,
			C120S01C c120s01c,
			C120S01E c120s01e){
		
	}
}
