<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.baseInfo'}"><!--基本資訊--></th:block></b>
                    </legend>
                    <table class="tb2 alignTopTab " id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
							<tr>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C340M01A.ownBrId'}">分行名稱</th:block>&nbsp;&nbsp;
							</td>
							<td width="35%" >
								<span id="ownBrId" class="field"></span>&nbsp;<span id="ownBrIdName" class="field" ></span>
							</td>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C340M01A.docStatus'}">文件狀態</th:block>&nbsp;&nbsp;
							</td>
							<td width="35%" ><b class="text-red"><span id="docStatus"></span>&nbsp;</b>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.ctrType'}">契約書種類</th:block>&nbsp;&nbsp;
							</td>
							<td ><span id="ctrTypeMapDesc" ></span>
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.rptId'}">契約書版本</th:block>&nbsp;&nbsp;
							</td>
							<td ><span id="rptId_desc" ></span>  <!-- <span id="rptId" /> -->
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.caseNo'}">案號</th:block>&nbsp;&nbsp;</td>
							<td colspan='3'><span id="caseNo" ></span>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.custId'}">主要借款人</th:block>&nbsp;&nbsp;
							</td>
							<td><span id="custId" class="field"-></span><span id="dupNo" class="field" />&nbsp;&nbsp;<span id="custName"></span>
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01B.cntrNo'}">額度序號</th:block>&nbsp;
							</td>
							<td><span id="cntrNo" ></span>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.contrNumber'}">契約書編號</th:block>&nbsp;
							</td>
							<td colspan=''><input type='text' id='contrNumber' name='contrNumber' maxlength='60' size='30'>
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.randomCode'}">報表亂碼</th:block>&nbsp;
							</td>
							<td colspan=''><span id="randomCode" class="field" ></span>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.contrPartyNm'}">契約書客戶名稱</th:block>&nbsp;
							</td>
							<td colspan='3'><input type='text' id='contrPartyNm' name='contrPartyNm' maxlengthC='50' size='60'>
							</td>
						</tr>
                        </tbody>
                    </table>
                </fieldset>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}"><!-- 文件異動紀錄 --></th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer">
                        <!-- 文件異動紀錄 --><div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
                        </div>
                    </div>
                    <table class="tb2" id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="15%" class="hd1">
                                    <th:block th:text="#{'doc.creator'}"><!--  文件建立者--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="35%">
                                    <span id='creator'/>(<span id='createTime'/>)
                                </td>
                                <td width="15%" class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}"><!--  最後異動者--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="35%">
                                    <span id='updater'/>(<span id='updateTime'/>)
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
               
           
        </th:block>
    </body>
</html>