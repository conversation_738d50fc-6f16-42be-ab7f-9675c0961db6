/* 
 * CLS1131S03Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.service.CLSService;

/**
 * <pre>
 * 評分調整表
 * </pre>
 * 
 * @since 2012/12/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/7,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131s03")
public class CLS1131S03Page extends AbstractOutputPage {

	@Autowired
	CLSService clsService;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {

		setNeedHtml(true); // need html

		setJavascript(new String[] { "pagejs/cls/CLS1131S03Page.js" });
		
		boolean c101s01g_adjustFlag_4 = true;
		model.addAttribute("c101s01g_adjustFlag_4", c101s01g_adjustFlag_4);
		
		return "&nbsp;";
	}

	// UPGRADE: 待確認是否需要ViewName
	@Override
	protected String getViewName() {
		return null;
	}

}
