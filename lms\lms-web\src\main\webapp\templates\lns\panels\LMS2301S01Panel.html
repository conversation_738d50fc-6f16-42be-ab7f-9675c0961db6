<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
    <body> 
        <th:block th:fragment="panelFragmentBody">
			<form id="L230M01AForm" name="L230M01AForm">
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.baseInfo'}"><!--基本資訊--></th:block></b>
                    </legend>
                    <table class="tb2" id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="35%" class="hd1">
                                    <th:block th:text="#{'doc.branchName'}"><!--  分行名稱--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id="ownBrId" name="ownBrId" ></span>
                                </td>
                                <td width="30%" class="hd1">
                                    <th:block th:text="#{'doc.caseNo'}"><!--  案號--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <input type="text" id="caseNo" name="caseNo" size="40" maxlength="62" maxlengthC="20">
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.docStatus'}"><!--文件狀態--></th:block>&nbsp;&nbsp
                                </td>
                                <td>
                                    <b><span class="color-red" id="docStatus" name="docStatus" ></span></b>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'l230m01a.title06'}"><!--簽案日期--></th:block>&nbsp;&nbsp
                                </td>
                                <td>
                                    <input type="text" id="caseDate" name="caseDate" class="date" />
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'l230m01a.title07'}"><!--主要借款人--></th:block>&nbsp;&nbsp
                                </td>
                                <td>
                                    <span id="custId" class="field"></span>&nbsp; <th:block th:text="#{'doc.idDup'}"><!--重覆序號--></th:block>：<span id="dupNo" class="field"></span>
                                    <br>
                                    (<span id="typCd" class="text-red"></span>)<span id="custName" ></span>
                                </td>
                                <td class="hd1">
                                </td>
                                <td>
                                    <button id="selectCaseTableBt" type="button">
                                        <span class="text-only"><th:block th:text="#{'l230m01a.bt01'}"><!-- 重新選擇簽報書--></th:block></span>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}"><!-- 文件異動紀錄 --></th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer">
                            <!-- 文件異動紀錄-->
                            <div id="_docLog" class="forview" th:insert="~{common/panels/DocLogPanel :: DocLogPanel}"></div>
                        </div>
                    </div>
                    <table class="tb2" id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="35%" class="hd1">
                                    <th:block th:text="#{'doc.creator'}"><!--  文件建立者--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id='creator'></span>(<span id='createTime'></span>)
                                </td>
                                <td width="30%" class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}"><!--  最後異動者--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <span id='updater'></span>(<span id='updateTime'></span>)
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                </td>
                                <td>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.docCode'}"><!--報表亂碼--></th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="randomCode" ></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
                <fieldset>
                    <legend>
                        <th:block th:text="#{'l230m01a.title08'}"><!--簽章欄--></th:block>&nbsp;&nbsp;
                    </legend>
                    <div id="tabs-appr" class="tabs">
                        <ul>
                            <li>
                                <a href="#tabs-appr01"><b><th:block th:text="#{'l230m01a.title09'}"><!--營業單位--></th:block>&nbsp;&nbsp;</b></a>
                            </li>
                        </ul>
                        <div class="tabCtx-warp">
                            <div id="tabs-appr01" class="content">
                                <p>
                                    <table width="100%">
                                        <tr>
                                            <td width="25%">
                                                <b class="text-red"><th:block th:text="#{'l230m01a.title10'}"><!--經副襄理--></th:block>&nbsp;&nbsp;：</b>
                                                <span id="managerId" name="managerId"></span>
                                            </td>
                                            <td width="25%">
                                                <b class="text-red"><th:block th:text="#{'l230m01a.title11'}"><!--授信主管--></th:block>&nbsp;&nbsp;：</b>
                                                <span id="bossId" name="bossId"></span>
                                            </td>
                                            <td width="25%">
                                                <b class="text-red"><th:block th:text="#{'l230m01a.title04'}"><!--覆核--></th:block>&nbsp;&nbsp;：</b>
                                                <span id="reCheckId" name="reCheckId"></span>
                                            </td>
                                            <td width="25%">
                                                <b class="text-red"><th:block th:text="#{'l230m01a.title12'}"><!--經辦--></th:block>&nbsp;&nbsp;：</b>
                                                <span id="apprId" name="apprId"></span>
                                            </td>
                                        </tr>
                                    </table>
                                </p>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </form>
            <div id="signReport" class="content" style="display: none;">
                <table class="tb1" width="100%" border="0" cellspacing="0"cellpadding="0">
                    <div id="signReportGrid" ></div>
                </table>
            </div>
		</th:block>
    </body>
</html>