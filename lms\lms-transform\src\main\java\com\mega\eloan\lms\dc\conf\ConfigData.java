package com.mega.eloan.lms.dc.conf;

import java.io.File;
import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.util.Util;

public class ConfigData implements Serializable {
	private static Logger logger = LoggerFactory.getLogger(ConfigData.class);

	private static final long serialVersionUID = -959618283626229084L;

	// private String userPath = "";// User當前工作目錄
	private String userPassword = "";
	private String ip = "";// 轉檔時使用的ip
	// private String port = "";// 轉檔時使用的port
	// private String host = "";// 轉檔時使用的host(ip+port)
	// private String LMSMainForm = "";// 企金會用到的所有formName
	// private String CLSMainForm = "";// 企金會用到的所有formName
	// private String RichTextColName = "";// form裡RichText 的 Column Name
	private String homePath = "";// 根目錄路徑
	// private String lmsDxlDirRootPath = "";// LMS的.dxl檔根目錄路徑
	// private String clsDxlDirRootPath = "";// CLS的.dxl檔根目錄路徑
	private String lmsXmlRootPath = "";// LMS的db2 xml檔根目錄路徑
	private String clsXmlRootPath = "";// CLS的db2 xml檔根目錄路徑
	// private String lmsLogsDirPath = "";// 企金各程序的log檔主目錄所在位置路徑
	// private String clsLogsDirPath = "";// 個金各程序的log檔主目錄所在位置路徑
	// private String lmsloadDB2DirPath = "";// 企金準備存入db2相關檔案放根目錄所在位置路徑
	// private String clsloadDB2DirPath = "";// 個金準備存入db2相關檔案放根目錄所在位置路徑
	private String richTextDirPath = "";// richText目錄所在位置路徑

	// private String homeName;
	// private String transKey; // 對應至config.properties 內today欄位

	// *****************
	// 原MainConfig內設定
	// *****************

	private String HOME_NAME;
	private String TODAY;
	private String usr_id;
	private String usr_psw;
	private String port;
	private String richTxtColName;
	private String dominoServerIp;

	private String viewListSize;
	private String XML_LMS;
	private String XML_CLS;
	private String LMSMainForm;
	private String CLSMainForm;// 2013-01-28 Add By Bang

	private String DC_ROOT;
	private String DC_ONLINE_FILE_ROOT;
	private String CONF_PATH;
	private String htmlPath;
	private String imagesPath;
	private String filesPath;
	private String textPath;
	private String rejectPath;// 2013-01-30 Add By Bang
	private String dataPath;// 2013-02-01 Add By Bang
	private String clobPath;// 2013-02-01 Add By Bang

	private String LMSViewName;// 2013-03-25 Add By Bang
	private String CLSViewName;// 2013-03-25 Add By Bang
	private String LMSViewListName;
	private String CLSViewListName;
	private String viewListExt;

	public void setOnlineData() {
		this.viewListSize = "0";
		this.init();
	}

	public void setOnlineData(String ip, String mainId, String schema,
			List<String> viewList) {
		this.dominoServerIp = ip;
		this.TODAY = mainId;
		if (schema.equalsIgnoreCase("LMS")) {
			System.out.println("LMSViewName=" + this.LMSViewName);
			@SuppressWarnings("unused")
			String[] tmp = this.LMSViewName.split(";");
			// this.LMSViewName = viewList.get(0);
			this.LMSViewName = "";
			/*
			 * for(int i =1;i<tmp.length;i++){//要換掉第1個view this.LMSViewName
			 * =this.LMSViewName +";"+tmp[i]; }
			 */
			for (String x : viewList) {// 要換掉第1個view
				this.LMSViewName += x + ";";
			}
		} else if (schema.equalsIgnoreCase("CLS")) {
			System.out.println("CLSViewName=" + this.CLSViewName);
			String[] tmp = this.LMSViewName.split(";");
			this.CLSViewName = viewList.get(0) + ";" + viewList.get(1);
			for (int i = 2; i < tmp.length; i++) {// 要換掉前2個view
				this.CLSViewName = this.CLSViewName + ";" + tmp[i];
			}
		}
		this.setOnlineData();
	}

	public String getUserPath() {
		return this.DC_ROOT;
	}

	public String getUserId() {
		return this.usr_id;
	}

	public String getUserPassword() {
		return userPassword;
	}

	public void setUserPassword(String userPassword) {
		this.userPassword = userPassword;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getPort() {
		return port;
	}

	public void setPort(String port) {
		this.port = port;
	}

	public String getHost() {
		return this.getIp() + ":" + port;
	}

	public String getLMSMainForm() {
		return LMSMainForm;
	}

	public void setLMSMainForm(String lMSMainForm) {
		LMSMainForm = lMSMainForm;
	}

	public String getCLSMainForm() {
		return CLSMainForm;
	}

	public void setCLSMainForm(String cLSMainForm) {
		CLSMainForm = cLSMainForm;
	}

	/**
	 * 回傳程式產出物主要根目錄
	 * 
	 * @return homePath String
	 * @Todo User當前工作目錄\DXL_Home\LMS
	 */
	public String getHomePath() {
		return homePath;
	}

	public void setHomePath(String homePath) {
		this.homePath = homePath;
	}

	/**
	 * 回傳LMS dxl主要根目錄
	 * 
	 * @return lmsDxlDirRootPath String
	 * @Todo User當前工作目錄\DXL_Home\轉檔當日日期\LMS
	 */
	public String getLmsDxlDirRootPath() {
		String userPath = this.getUserPath();
		if (this.isOnlineMode()) {
			userPath = this.getDC_ONLINE_FILE_ROOT();
		}
		return userPath + this.getHomeName() + File.separator
				+ this.getTransKey() + File.separator + "LMS";
	}

	/**
	 * 回傳CLS dxl主要根目錄
	 * 
	 * @return clsDxlDirRootPath String
	 * @Todo User當前工作目錄\DXL_Home\轉檔當日日期\CLS
	 */
	public String getClsDxlDirRootPath() {
		String userPath = this.getUserPath();
		if (this.isOnlineMode()) {
			userPath = this.getDC_ONLINE_FILE_ROOT();
		}
		return userPath + this.getHomeName() + File.separator
				+ this.getTransKey() + File.separator + "CLS";
	}

	/**
	 * LMS的db2 xml檔根目錄路徑
	 * 
	 * @return lmsXmlRootPath
	 */
	public String getLmsXmlRootPath() {
		return lmsXmlRootPath;
	}

	public void setLmsXmlRootPath(String lmsXmlRootPath) {
		this.lmsXmlRootPath = lmsXmlRootPath;
	}

	/**
	 * CLS的db2 xml檔根目錄路徑
	 * 
	 * @return clsXmlRootPath
	 */
	public String getClsXmlRootPath() {
		return clsXmlRootPath;
	}

	public void setClsXmlRootPath(String clsXmlRootPath) {
		this.clsXmlRootPath = clsXmlRootPath;
	}

	/**
	 * 企金各程序的log檔主目錄所在位置路徑
	 * 
	 * @return lmsLogsDirPath String
	 */
	public String getLmsLogsDirPath() {
		String userPath = this.getUserPath();
		if (this.isOnlineMode()) {
			userPath = this.getDC_ONLINE_FILE_ROOT();
		}
		return userPath + File.separator + "log" + File.separator + "logs"
				+ File.separator + this.getTransKey() + File.separator  + "LMS";
	}

	/**
	 * 個金各程序的log檔主目錄所在位置路徑
	 * 
	 * @return clsLogsDirPath String
	 */
	public String getClsLogsDirPath() {
		String userPath = this.getUserPath();
		if (this.isOnlineMode()) {
			userPath = this.getDC_ONLINE_FILE_ROOT();
		}
		return userPath + File.separator + "log" + File.separator + "logs"
				+ File.separator + this.getTransKey() + File.separator + "CLS";
	}

	/**
	 * 企金準備存入db2相關檔案放目錄所在位置路徑
	 * 
	 * @return lmsloadDB2DirPath String
	 */
	public String getLmsloadDB2DirPath() {
		String userPath = this.getUserPath();
		if (this.isOnlineMode()) {
			userPath = this.getDC_ONLINE_FILE_ROOT();
		}
		return userPath + File.separator + "load_db2" + File.separator
				+ this.getTransKey() + File.separator + "LMS";

	}

	/**
	 * 個金準備存入db2相關檔案放目錄所在位置路徑
	 * 
	 * @return clsloadDB2DirPath String
	 */
	public String getClsloadDB2DirPath() {
		String userPath = this.getUserPath();
		if (this.isOnlineMode()) {
			userPath = this.getDC_ONLINE_FILE_ROOT();
		}
		return userPath + File.separator + "load_db2" + File.separator
				+ this.getTransKey() + File.separator + "CLS";
	}

	public String getRichTextDirPath() {
		return richTextDirPath;
	}

	public void setRichTextDirPath(String richTextDirPath) {
		this.richTextDirPath = richTextDirPath;
	}

	/**
	 * get the htmlPath
	 * 
	 * @return the htmlPath
	 */
	public String getHtmlPath() {
		return htmlPath;
	}

	/**
	 * set the htmlPath
	 * 
	 * @param htmlPath
	 *            the htmlPath to set
	 */
	public void setHtmlPath(String htmlPath) {
		this.htmlPath = htmlPath;
	}

	/**
	 * get the imagesPath
	 * 
	 * @return the imagesPath
	 */
	public String getImagesPath() {
		return imagesPath;
	}

	/**
	 * set the imagesPath
	 * 
	 * @param imagesPath
	 *            the imagesPath to set
	 */
	public void setImagesPath(String imagesPath) {
		this.imagesPath = imagesPath;
	}

	/**
	 * get the filesPath
	 * 
	 * @return the filesPath
	 */
	public String getFilesPath() {
		return filesPath;
	}

	/**
	 * set the filesPath
	 * 
	 * @param filesPath
	 *            the filesPath to set
	 */
	public void setFilesPath(String filesPath) {
		this.filesPath = filesPath;
	}

	/**
	 * get the textPath
	 * 
	 * @return the textPath
	 */
	public String getTextPath() {
		return textPath;
	}

	/**
	 * set the textPath
	 * 
	 * @param textPath
	 *            the textPath to set
	 */
	public void setTextPath(String textPath) {
		this.textPath = textPath;
	}

	/**
	 * get the rejectPath
	 * 
	 * @return the rejectPath
	 */
	public String getRejectPath() {
		return rejectPath;
	}

	/**
	 * set the rejectPath
	 * 
	 * @param rejectPath
	 *            the rejectPath to set
	 */
	public void setRejectPath(String rejectPath) {
		this.rejectPath = rejectPath;
	}

	/**
	 * get the dataPath
	 * 
	 * @return the dataPath
	 */
	public String getDataPath() {
		return dataPath;
	}

	/**
	 * set the dataPath
	 * 
	 * @param dataPath
	 *            the dataPath to set
	 */
	public void setDataPath(String dataPath) {
		this.dataPath = dataPath;
	}

	/**
	 * get the clobPath
	 * 
	 * @return the clobPath
	 */
	public String getClobPath() {
		return clobPath;
	}

	/**
	 * set the clobPath
	 * 
	 * @param clobPath
	 *            the clobPath to set
	 */
	public void setClobPath(String clobPath) {
		this.clobPath = clobPath;
	}

	/**
	 * get the transKey
	 * 
	 * @return the transKey
	 */
	public String getTransKey() {
		return this.TODAY;
	}

	/**
	 * get the homeName
	 * 
	 * @return the homeName
	 */
	public String getHomeName() {
		return this.HOME_NAME;
	}

	/**
	 * get the hOME_NAME
	 * 
	 * @return the hOME_NAME
	 */
	public String getHOME_NAME() {
		return HOME_NAME;
	}

	/**
	 * set the hOME_NAME
	 * 
	 * @param hOME_NAME
	 *            the hOME_NAME to set
	 */
	public void setHOME_NAME(String hOME_NAME) {
		HOME_NAME = hOME_NAME;
	}

	/**
	 * get the tODAY
	 * 
	 * @return the tODAY
	 */
	public String getTODAY() {
		return TODAY;
	}

	/**
	 * set the tODAY
	 * 
	 * @param tODAY
	 *            the tODAY to set
	 */
	public void setTODAY(String tODAY) {
		TODAY = tODAY;
	}

	/**
	 * get the usr_id
	 * 
	 * @return the usr_id
	 */
	public String getUsr_id() {
		return usr_id;
	}

	/**
	 * set the usr_id
	 * 
	 * @param usr_id
	 *            the usr_id to set
	 */
	public void setUsr_id(String usr_id) {
		this.usr_id = usr_id;
	}

	/**
	 * get the usr_psw
	 * 
	 * @return the usr_psw
	 */
	public String getUsr_psw() {
		return usr_psw;
	}

	/**
	 * set the usr_psw
	 * 
	 * @param usr_psw
	 *            the usr_psw to set
	 */
	public void setUsr_psw(String usr_psw) {
		this.usr_psw = usr_psw;
	}

	/**
	 * get the richTxtColName
	 * 
	 * @return the richTxtColName
	 */
	public String getRichTxtColName() {
		return richTxtColName;
	}

	/**
	 * set the richTxtColName
	 * 
	 * @param richTxtColName
	 *            the richTxtColName to set
	 */
	public void setRichTxtColName(String richTxtColName) {
		this.richTxtColName = richTxtColName;
	}

	/**
	 * get the dominoServerIp
	 * 
	 * @return the dominoServerIp
	 */
	public String getDominoServerIp() {
		return dominoServerIp;
	}

	/**
	 * set the dominoServerIp
	 * 
	 * @param dominoServerIp
	 *            the dominoServerIp to set
	 */
	public void setDominoServerIp(String dominoServerIp) {
		this.dominoServerIp = dominoServerIp;
	}

	/**
	 * get the viewListSize
	 * 
	 * @return the viewListSize
	 */
	public String getViewListSize() {
		return viewListSize;
	}

	/**
	 * set the viewListSize
	 * 
	 * @param viewListSize
	 *            the viewListSize to set
	 */
	public void setViewListSize(String viewListSize) {
		this.viewListSize = viewListSize;
	}

	/**
	 * get the xML_LMS
	 * 
	 * @return the xML_LMS
	 */
	public String getXML_LMS() {
		return XML_LMS;
	}

	/**
	 * set the xML_LMS
	 * 
	 * @param xML_LMS
	 *            the xML_LMS to set
	 */
	public void setXML_LMS(String xML_LMS) {
		XML_LMS = xML_LMS;
	}

	/**
	 * get the xML_CLS
	 * 
	 * @return the xML_CLS
	 */
	public String getXML_CLS() {
		return XML_CLS;
	}

	/**
	 * set the xML_CLS
	 * 
	 * @param xML_CLS
	 *            the xML_CLS to set
	 */
	public void setXML_CLS(String xML_CLS) {
		XML_CLS = xML_CLS;
	}

	/**
	 * get the dC_ROOT
	 * 
	 * @return the dC_ROOT
	 */
	public String getDC_ROOT() {
		return DC_ROOT;
	}

	/**
	 * set the dC_ROOT
	 * 
	 * @param dC_ROOT
	 *            the dC_ROOT to set
	 */
	public void setDC_ROOT(String dC_ROOT) {
		DC_ROOT = dC_ROOT;
	}

	/**
	 * get the cONF_PATH
	 * 
	 * @return the cONF_PATH
	 */
	public String getCONF_PATH() {
		return CONF_PATH;
	}

	/**
	 * set the cONF_PATH
	 * 
	 * @param cONF_PATH
	 *            the cONF_PATH to set
	 */
	public void setCONF_PATH(String cONF_PATH) {
		CONF_PATH = cONF_PATH;
	}

	/**
	 * get the LMSViewName
	 * 
	 * @return the lMSViewName
	 */
	public String getLMSViewName() {
		return LMSViewName;
	}

	/**
	 * set the lMSViewName
	 * 
	 * @param lMSViewName
	 *            the lMSViewName to set
	 */
	public void setLMSViewName(String lMSViewName) {
		LMSViewName = lMSViewName;
	}

	/**
	 * get the CLSViewName
	 * 
	 * @return the CLSViewName
	 */
	public String getCLSViewName() {
		return CLSViewName;
	}

	/**
	 * set the cLSViewName
	 * 
	 * @param cLSViewName
	 *            the cLSViewName to set
	 */
	public void setCLSViewName(String cLSViewName) {
		CLSViewName = cLSViewName;
	}

	/**
	 * get the lMSViewListName
	 * 
	 * @return the lMSViewListName
	 */
	public String getLMSViewListName() {
		return LMSViewListName;
	}

	/**
	 * set the lMSViewListName
	 * 
	 * @param lMSViewListName
	 *            the lMSViewListName to set
	 */
	public void setLMSViewListName(String lMSViewListName) {
		LMSViewListName = lMSViewListName;
	}

	/**
	 * get the cLSViewListName
	 * 
	 * @return the cLSViewListName
	 */
	public String getCLSViewListName() {
		return CLSViewListName;
	}

	/**
	 * set the cLSViewListName
	 * 
	 * @param cLSViewListName
	 *            the cLSViewListName to set
	 */
	public void setCLSViewListName(String cLSViewListName) {
		CLSViewListName = cLSViewListName;
	}

	/**
	 * get the viewListExt
	 * 
	 * @return the viewListExt
	 */
	public String getViewListExt() {
		return viewListExt;
	}

	/**
	 * set the viewListExt
	 * 
	 * @param viewListExt
	 *            the viewListExt to set
	 */
	public void setViewListExt(String viewListExt) {
		this.viewListExt = viewListExt;
	}

	public String getDC_ONLINE_FILE_ROOT() {
		return DC_ONLINE_FILE_ROOT;
	}

	public void setDC_ONLINE_FILE_ROOT(String dC_ONLINE_FILE_ROOT) {
		DC_ONLINE_FILE_ROOT = dC_ONLINE_FILE_ROOT;
	}

	/**
	 * 創建轉檔過程中所需之各項基本目錄: Ex-->DXL_HOME/LMS/今天日期 及設定userId,password,port,RichText
	 * ColumnName等相關資訊
	 * 
	 * @param userPath
	 *            String:User當前工作目錄
	 */
	public void init() {
		if (logger.isInfoEnabled()) {
			if (StringUtils.isEmpty(this.DC_ROOT)) {
				logger.warn("!!!!! config.properties內未設定DC_ROOT值，目前使用系統變數(user.dir)值！");
			}
		}
		this.DC_ROOT = this.DC_ROOT == null ? System.getProperty("user.dir")
				: DC_ROOT;

		String userPath = this.DC_ROOT;

		// 設定userPassword
		this.setUserPassword(this.usr_psw);
		// 設定ip
		String ip = StringUtils.isEmpty(dominoServerIp) ? Util.getLocalIp()
				: dominoServerIp;

		this.setIp(ip);

		// this.setHomeName(this.HOME_NAME);

		// 設定USER_PATH
		// this.setUserPath(userPath);
		// 設定HOME_NAME
		if (this.isOnlineMode()) {
			this.setHomePath(this.DC_ONLINE_FILE_ROOT + this.HOME_NAME);
		} else {
			this.setHomePath(userPath + this.HOME_NAME);
		}

		// 設定企金xmlPath
		String LMSXmlPath = userPath + this.XML_LMS;
		this.setLmsXmlRootPath(LMSXmlPath);

		// 設定個金xmlPath
		String CLSXmlPath = userPath + this.XML_CLS;
		this.setClsXmlRootPath(CLSXmlPath);

		// // 設定LMS所有formName
		// this.setLMSMainForm(this.LMSMainForm);
		// // 設定CLS所有formName
		// this.setCLSMainForm(this.CLSMainForm);
		// // 設定RichTextColName
		// this.setRichTextColName(this.richTxtColName);
		//
		// this.setHtmlPath(this.htmlPath);
		// this.setImagesPath(this.imagesPath);
		// this.setFilesPath(this.filesPath);
		// this.setTextPath(this.textPath);
		// this.setRejectPath(this.rejectPath);
		// this.setDataPath(this.dataPath);
		// this.setClobPath(this.clobPath);

		this.checkPath();

		logger.info("##### ConfigDataBean==>>\n" + this.toString());
	}

	public boolean isOnlineMode() {
		int viewSize = NumberUtils.toInt(this.viewListSize, 0);
		return viewSize <= 0;
	}

	public void checkPath() {
		Util.checkDirExist(this.homePath);

		// 設定企金 dxlPath
		Util.checkDirExist(this.getLmsDxlDirRootPath());

		// 設定個金 dxlPath
		Util.checkDirExist(this.getClsDxlDirRootPath());

		// 設定企金xmlPath
		Util.checkDirExist(this.getLmsXmlRootPath());

		// 設定個金xmlPath
		Util.checkDirExist(this.getClsXmlRootPath());

		// 設定企金各程序的log檔存放目錄
		Util.checkDirExist(this.getLmsLogsDirPath());

		// 設定個金各程序的log檔存放目錄
		Util.checkDirExist(this.getClsLogsDirPath());

		// 設定企金準備存入db2相關檔案放目錄
		Util.checkDirExist(this.getLmsloadDB2DirPath());

		// 設定個金準備存入db2相關檔案放目錄
		Util.checkDirExist(this.getClsloadDB2DirPath());
	}

	@Override
	public String toString() {
		StringBuffer str = new StringBuffer();
		str.append(
				StringUtils.replace(ToStringBuilder.reflectionToString(this),
						",", "\n")).append("\n");

		str.append("getUserId=").append(this.getUserId()).append("\n");
		str.append("getUserPath=").append(this.getUserPath()).append("\n");
		str.append("getHomeName=").append(this.getHomeName()).append("\n");

		str.append("getHost=").append(this.getHost()).append("\n");
		str.append("getLmsDxlDirRootPath=").append(this.getLmsDxlDirRootPath())
				.append("\n");
		str.append("getClsDxlDirRootPath=").append(this.getClsDxlDirRootPath())
				.append("\n");
		str.append("getLmsLogsDirPath=").append(this.getLmsLogsDirPath())
				.append("\n");
		str.append("getClsLogsDirPath=").append(this.getClsLogsDirPath())
				.append("\n");
		str.append("getLmsloadDB2DirPath=").append(this.getLmsloadDB2DirPath())
				.append("\n");
		str.append("getClsloadDB2DirPath=").append(this.getClsloadDB2DirPath())
				.append("\n");
		return str.toString();
	}
}
