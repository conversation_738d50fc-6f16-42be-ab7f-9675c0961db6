/* 
 * LMS9515M01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.Rate;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.lns.panels.LMS1401S03Panel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.bean.LNF07A;
import com.mega.eloan.lms.mfaloan.service.LNLNF07AService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;
import com.mega.eloan.lms.model.L120S04E;
import com.mega.eloan.lms.model.L260M01D;
import com.mega.eloan.lms.obsdb.service.MisELF001Service;
import com.mega.eloan.lms.rpt.panels.LMSS07APanel;
import com.mega.eloan.lms.rpt.report.impl.LMS9535R01RptServiceImpl;
import com.mega.eloan.lms.rpt.service.LMS9535Service;
import com.mega.eloan.lms.rpt.service.LMS9535XLSService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.write.WriteException;
import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 管理報表往來彙總查詢
 * </pre>
 * 
 * @since 2013/1/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/2,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9535formhandler")
public class LMS9535Formhandler extends AbstractFormHandler {

	@Resource
	LMS9535Service lms9535Service;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	MisELF001Service elf001Srv; // 海外存款

	@Resource
	LMSService lmsService;

	@Resource
	ICustomerService icustSrv;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisGrpcmpService misGrpcmpService;

	@Resource
	DocFileService docFileService;

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	@Resource
	LMS9535R01RptServiceImpl lms9535r01rptservice;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	LMS9535XLSService lms9535xlsService;
	
	@Resource
	LMS1201Service lms1201Service;
	
	@Resource
	SysParameterService sysParameterService;
	
	@Resource
	LNLNF07AService lnLnf07aService;

	/**
	 * 新增往來彙總
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult addL120s04a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custIdAll = Util.trim(params.getString("custIdAll"));
		boolean isEmpty = Util.isEmpty(custIdAll);
		String custName = (isEmpty) ? Util.trim(params.getString("custName"))
				: (Util.trim(params.getString("custName")).length() < custIdAll
						.length() + 3) ? Util
						.trim(params.getString("custName")) : Util.trim(
						params.getString("custName")).substring(
						custIdAll.length() + 3);
		String custId = (isEmpty) ? UtilConstants.Mark.SPACE : custIdAll
				.substring(0, custIdAll.length() - 1);
		String dupNo = (isEmpty) ? UtilConstants.Mark.SPACE : custIdAll
				.substring(custIdAll.length() - 1);
		String mainId = params.getString(EloanConstants.MAIN_ID);

		List<L120S04A> list = lms9535Service.findL120s04aByMainId(mainId);
		if (!list.isEmpty()) {
			for (L120S04A model : list) {
				// 欲新增的資料已存在，無法新增！
				if (custId.equals(Util.trim(model.getCustId()))
						&& dupNo.equals(Util.trim(model.getDupNo()))) {
					throw new CapMessageException(
							pop.getProperty("L1205S07.alert8"), getClass());
				}
			}
		}

		// 取得使用者選擇之查詢日期起訖日
		String queryDateS = params.getString("queryDateS");
		String queryDateE = params.getString("queryDateE");
		CapAjaxFormResult myForm2Result = new CapAjaxFormResult();
		// 開始設定一些初始化內容
		myForm2Result.set("custId", custId);
		myForm2Result.set("dupNo", dupNo);
		myForm2Result.set("custName", custName);
		myForm2Result.set("prtFlag", UtilConstants.Casedoc.L120s04aPrtFlag.要列印);
		myForm2Result.set("prtFlag2", pop.getProperty("L1205S07.prtFlag1"));
		myForm2Result.set("createBY",
				UtilConstants.Casedoc.L120s04aCreateBY.人工產生);
		if (Util.isEmpty(queryDateS) && Util.isEmpty(queryDateE)) {
			// 如果找不到使用者所輸入的查詢日期起訖日(即尚未引進往來彙總)
			// 設定為空
			myForm2Result.set("queryDateS_", UtilConstants.Mark.SPACE);
			myForm2Result.set("queryDateE_", UtilConstants.Mark.SPACE);
		} else {
			// 找到資料就設定
			myForm2Result.set("queryDateS_",
					(queryDateS.length() < 7) ? UtilConstants.Mark.SPACE
							: queryDateS.substring(0, 7));
			myForm2Result.set("queryDateE_",
					(queryDateE.length() < 7) ? UtilConstants.Mark.SPACE
							: queryDateE.substring(0, 7));
		}
		result.set("tLMS1205S07Form03", myForm2Result);
		return result;
	}

	/**
	 * 儲存往來彙總
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s04a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// String queryDateS = params.getString("queryDateS");
		// String queryDateE = params.getString("queryDateE");
		String queryDateS = UtilConstants.Mark.SPACE;
		String queryDateE = UtilConstants.Mark.SPACE;
		String custRelation = Util.trim(params.getString("list"));
		String[] strs = custRelation.split(",");
		// 對陣列進行排序
		if (strs.length > 0) {
			Arrays.sort(strs);
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			for (String str : strs) {
				sb.append((sb.length() > 0) ? "," : UtilConstants.Mark.SPACE)
						.append(str);
			}
			custRelation = sb.toString();
		}
		L120S04A l120s04a = lms9535Service.findL120s04aByOid(oid);
		List<L120S04A> list = lms9535Service.findL120s04aByMainId(mainId);
		if (!list.isEmpty()) {
			for (L120S04A model : list) {
				if (Util.isNotEmpty(Util.trim(queryDateS))
						&& Util.isNotEmpty(Util.trim(queryDateE))) {
					break;
				}
				String tempDateS = CapDate.formatDate(model.getQueryDateS(),
						UtilConstants.DateFormat.YYYY_MM_DD);
				String tempDateE = CapDate.formatDate(model.getQueryDateE(),
						UtilConstants.DateFormat.YYYY_MM_DD);
				if (Util.isNotEmpty(Util.trim(tempDateS))) {
					queryDateS = Util.trim(tempDateS);
				}
				if (Util.isNotEmpty(Util.trim(tempDateE))) {
					queryDateE = Util.trim(tempDateE);
				}
			}
		}
		String formL120s04a = params.getString("tLMS1205S07Form03");
		if (l120s04a != null) {
			// 有資料(更新儲存)
			DataParse.toBean(formL120s04a, l120s04a);
		} else {
			// 如無資料則新建立
			l120s04a = new L120S04A();
			DataParse.toBean(formL120s04a, l120s04a);
			// 設定一些初始化內容
			l120s04a.setMainId(mainId);
			l120s04a.setCreateTime(CapDate.getCurrentTimestamp());
			l120s04a.setCreator(user.getUserId());
			// l120s04a.setQueryDateS(Util.parseDate(Util.trim(queryDateS +
			// "-01")));
			// l120s04a.setQueryDateE(Util.parseDate(Util.trim(queryDateE +
			// "-01")));
			l120s04a.setQueryDateS(Util.parseDate(Util.trim(queryDateS)));
			l120s04a.setQueryDateE(Util.parseDate(Util.trim(queryDateE)));
		}
		if (custRelation != null) {
			l120s04a.setCustRelation(custRelation);
		} else {
			l120s04a.setCustRelation(UtilConstants.Mark.SPACE);
		}
		// 進行檢核的判斷寫在此
		l120s04a.setChkYN(UtilConstants.DEFAULT.是);
		// 儲存
		// L120M01A model = l120m01aDao.findByMainId(mainId);
		try {
			lms9535Service.save(l120s04a);
		} catch (Exception e) {
			logger.error("[saveL120s04a] service1201.save EXCEPTION!!", e);
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		result.set("newOid", l120s04a.getOid());
		return result;
	}// ;

	/**
	 * J-112-0307
	 * 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	 * 
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult autoProLMS9535V01(PageParameters params)
			throws FileNotFoundException, IOException, Exception, CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();		

		CapAjaxFormResult result = new CapAjaxFormResult();
		
		CapAjaxFormResult result1 = (CapAjaxFormResult) getAdvanceData(params);
		JSONObject tLMS1205S07Form03bResult = (JSONObject) result1
				.get("tLMS1205S07Form03b");
		String chairmanId = Util.trim(tLMS1205S07Form03bResult
				.get("chairmanId"));
		String chairmanDupNo = Util.trim(tLMS1205S07Form03bResult
				.get("chairmanDupNo"));
		String chairman = Util.trim(tLMS1205S07Form03bResult.get("chairman"));
		String custName = Util.trim(tLMS1205S07Form03bResult.get("custName"));
		String groupNo = Util.trim(tLMS1205S07Form03bResult.get("groupNo"));
		String groupName = Util.trim(tLMS1205S07Form03bResult.get("groupName"));
		String groupBadFlag = Util.trim(tLMS1205S07Form03bResult
				.get("groupBadFlag"));

		// 取得集團列管註記
		CodeType tCodeType = codeTypeService.findByCodeTypeAndCodeValue(
				"l1205s01_groupBadFlag", groupBadFlag);
		String codeValue = "";
		String groupBadFlagText = "";
		if (Util.isNotEmpty(tCodeType)) {

			codeValue = Util.trim(tCodeType.getCodeValue());
			groupBadFlagText = "";

			if (Util.notEquals(codeValue, "") && Util.notEquals(codeValue, "0")) {
				groupBadFlagText = Util.trim(tCodeType.getCodeDesc());
			}

		}

		params.add("custName", custName);
		CapAjaxFormResult result2 = (CapAjaxFormResult) saveL120s04a2(params);

		String queryDateS0 = params.getString("queryDateS0");
		String queryDateS1 = params.getString("queryDateS1");
		String queryDateE0 = params.getString("queryDateE0");
		String queryDateE1 = params.getString("queryDateE1");
		StringBuilder strbS = new StringBuilder();
		StringBuilder strbE = new StringBuilder();

		strbS.append(checkLength(queryDateS0)).append("-")
				.append(checkLength(queryDateS1));
		String queryDateS = strbS.toString();
		strbE.append(checkLength(queryDateE0)).append("-")
				.append(checkLength(queryDateE1));
		String queryDateE = strbE.toString();

		params.add("queryDateS", queryDateS);
		params.add("queryDateE", queryDateE);
		saveTotal(params);

		
		String unitMgrName_S01D = params.getString("unitMgrName_S01D");
		String accountMgrName_S01D = params.getString("accountMgrName_S01D");
		
		String mainId = Util.trim(result2.get(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		PageParameters reportParams = new CapMvcParameters();
		reportParams.add("mainId", mainId);
		reportParams.add("custId", custId);
		reportParams.add("dupNo", dupNo);
		reportParams.add("custName", custName);
		reportParams.add("chairmanId", chairmanId);
		reportParams.add("chairmanDupNo", chairmanDupNo);
		reportParams.add("chairman", chairman);
		reportParams.add("groupNo", groupNo);
		reportParams.add("groupName", groupName);
		reportParams.add("groupBadFlag", groupBadFlag);
		reportParams.add("groupBadFlagText", groupBadFlagText);
		reportParams.add("rptOid", "R14" + "^" + "");
		reportParams.add("fileDownloadName", "l120r01.pdf");
		
		reportParams.add("fromPostLoan", "Y");
		reportParams.add("unitMgrName_S01D", unitMgrName_S01D);
		reportParams.add("accountMgrName_S01D", accountMgrName_S01D);

		OutputStream out = lms9535r01rptservice.generateReport(reportParams);

		L260M01D l260m01d = lms9535Service.findModelByOid(L260M01D.class,
				mainId);
		if (Util.isNotEmpty(l260m01d)) {
			ByteArrayOutputStream baos = null;
			baos = (ByteArrayOutputStream) out;

			DocFile file = new DocFile();

			final String fileTitle = "借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表";

			file.setMainId(l260m01d.getMainId()); // L260M01D 的 mainId = L260M01A 的 MAINID
			file.setPid(mainId); // L260M01D 的 oid
			file.setData(baos != null ? baos.toByteArray() : null);
			file.setFileDesc(fileTitle);
			file.setCrYear(CapDate.getCurrentDate("yyyy-MM"));
			file.setFieldId("postLoanCertified");
			file.setSrcFileName(fileTitle + ".pdf");
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setBranchId(user.getUnitNo());
			file.setContentType("application/pdf");
			file.setSysId("LMS");
			docFileService.save(file);
							
		}

		return result;
	}
	
	/**
	 * 引進各關係戶往來彙總
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s04a2(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("mainId", "");
		mainId = mainId.length() == 32 ? mainId : IDGenerator.getUUID();
		// L120M01A model = l120m01aDao.findByMainId(mainId);
		List<L120S04A> list = lms9535Service.findL120s04aByMainId(mainId);
		String form = params.getString("LMS1205S07Form03");
		// 取得使用者所輸入的查詢日期起訖日
		String queryDateS0 = params.getString("queryDateS0");
		String queryDateS1 = params.getString("queryDateS1");
		String queryDateE0 = params.getString("queryDateE0");
		String queryDateE1 = params.getString("queryDateE1");
		StringBuilder strbS = new StringBuilder();
		StringBuilder strbE = new StringBuilder();
		// 轉換日期成YYYY-MM-01格式
		strbS.append(checkLength(queryDateS0)).append("-")
				.append(checkLength(queryDateS1)).append("-").append("01");
		String queryDateS = strbS.toString();
		strbE.append(checkLength(queryDateE0)).append("-")
				.append(checkLength(queryDateE1)).append("-").append("01");
		String queryDateE = strbE.toString();

		JSONObject formJson;
		if (form != null) {
			formJson = JSONObject.fromObject(form);
		} else {
			formJson = new JSONObject();
		}

		CapAjaxFormResult LMS1205S07Form03 = new CapAjaxFormResult();
		if (list != null) {
			// 如果有資料就刪除再引進
			lms9535Service.deleteListL120s04a(list);
		}
		// 設定日期
		formJson.put("queryDateS0", queryDateS);
		formJson.put("queryDateE0", queryDateE);
		LMS1205S07Form03.set("queryDateS", (queryDateS.length() < 7) ? null
				: queryDateS.substring(0, 7));
		LMS1205S07Form03.set("queryDateE", (queryDateE.length() < 7) ? null
				: queryDateE.substring(0, 7));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));

		// 開始連結DB取值並丟給List
		int getKind = lms9535Service.checkDate(queryDateS, queryDateE, formJson);
		StringBuilder sbMsg = new StringBuilder();
		String popMsg1 = UtilConstants.Mark.SPACE;
		String popMsg2 = UtilConstants.Mark.SPACE;
		String popMsg3 = "L1205S07.error7";
		StringBuilder sbDate = new StringBuilder();
		StringBuilder sbDbDate = new StringBuilder();
		if (getKind == 0) {
			try {
				list = lms9535Service.findL120s04a(mainId, custId, dupNo,
						custName, queryDateS, queryDateE);
			} catch (DataAccessResourceFailureException e) {
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0048"), getClass());
			}
		} else if (getKind == 1) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			sbDbDate.append(
					getYMDofDate(Util.trim(formJson.get("MIN_CYC_MN")),
							UtilConstants.DEFAULT.是))
					.append("/")
					.append(getYMDofDate(Util.trim(formJson.get("MIN_CYC_MN")),
							"M"));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error8";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 2) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			sbDbDate.append(
					getYMDofDate(Util.trim(formJson.get("MIN_CYC_MN")),
							UtilConstants.DEFAULT.是))
					.append("/")
					.append(getYMDofDate(Util.trim(formJson.get("MIN_CYC_MN")),
							"M"));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error4";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 3) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error3";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 4) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			sbDbDate.append(
					getYMDofDate(Util.trim(formJson.get("MAX_CYC_MN")),
							UtilConstants.DEFAULT.是))
					.append("/")
					.append(getYMDofDate(Util.trim(formJson.get("MAX_CYC_MN")),
							"M"));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error6";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 5) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			sbDbDate.append(
					getYMDofDate(Util.trim(formJson.get("MAX_CYC_MN")),
							UtilConstants.DEFAULT.是))
					.append("/")
					.append(getYMDofDate(Util.trim(formJson.get("MAX_CYC_MN")),
							"M"));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error10";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error5";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		}
		// 設定文件檢核狀態
		for (L120S04A l120s04a : list) {
			l120s04a.setChkYN(UtilConstants.DEFAULT.是);
		}
		// 儲存
		lms9535Service.saveL120s04aList(list);
		if (list.isEmpty()) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		} else {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}
		LMS1205S07Form03.set("custId", custId);
		LMS1205S07Form03.set("dupNo", dupNo);
		LMS1205S07Form03.set("custName", Util.truncateString(custName, 120));
		result.set("LMS1205S07Form03", LMS1205S07Form03);
		result.set(EloanConstants.MAIN_ID, mainId);
		return result;
	}// ;

	/**
	 * 取得負責人、借款人名稱、隸屬集團等資訊並設定到畫面上
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getAdvanceData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult tLMS1205S07Form03b = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mainId = IDGenerator.getUUID();
		// 紀錄負責人資訊
		List<Map<String, Object>> rows1 = new ArrayList<Map<String, Object>>();
		// 紀錄借款人名稱
		List<Map<String, Object>> rows2 = new ArrayList<Map<String, Object>>();
		// 紀錄隸屬集團
		List<Map<String, Object>> rows3 = new ArrayList<Map<String, Object>>();
		try {
			// 透過MIS JDBC取得負責人資訊
			rows1 = this.misCustdataService.findCustdataForList(custId, dupNo);
			// 透過MIS JDBC取得借款人名稱
			rows2 = this.misCustdataService.findCustDataCname(custId, dupNo);
			// 透過MIS JDBC取得隸屬集團資訊
			rows3 = this.misGrpcmpService.findGrpcmpSelGrpdtl(custId, dupNo);
		} catch (DataAccessResourceFailureException e) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0048"), getClass());
		}
		// 設定負責人統編、重覆序號與負責人名稱
		for (Map<String, Object> map1 : rows1) {
			// 負責人統編
			if (Util.isNotEmpty(map1.get("SUP1ID"))) {
				tLMS1205S07Form03b.set("chairmanId",
						Util.trim(map1.get("SUP1ID")));
				tLMS1205S07Form03b.set("chairmanDupNo",
						Util.isEmpty(Util.trim(map1.get("SUP1DUPNO"))) ? "0"
								: Util.trim(map1.get("SUP1DUPNO")));
			}
			if (Util.isNotEmpty(Util.trim(map1.get("SUP1CNM")))) {
				tLMS1205S07Form03b.set("chairman",
						Util.trim(map1.get("SUP1CNM")));
			} else if (Util.isNotEmpty(Util.trim(map1.get("SUP3CNM")))) {
				tLMS1205S07Form03b.set("chairman",
						Util.trim(map1.get("SUP3CNM")));
			}
			break;
		}
		// 設定借款人中文名稱
		for (Map<String, Object> map2 : rows2) {
			if (Util.isNotEmpty(map2.get("CNAME"))) {
				// 取得借款人中文名稱
				tLMS1205S07Form03b
						.set("custName", Util.trim(map2.get("CNAME")));
			}
			break;
		}
		// 設定隸屬集團代碼與名稱
		for (Map<String, Object> map3 : rows3) {
			tLMS1205S07Form03b.set("groupNo",
					Util.trim(Util.trim(map3.get("GRPID"))));
			tLMS1205S07Form03b.set("groupName",
					Util.trim(Util.trim(map3.get("GRPNM"))));
			tLMS1205S07Form03b.set("groupBadFlag",
					Util.trim(Util.trim(map3.get("BADFLAG"))));
			break;
		}
		tLMS1205S07Form03b.set("custId", custId);
		tLMS1205S07Form03b.set("dupNo", dupNo);
		result.set("tLMS1205S07Form03b", tLMS1205S07Form03b);
		result.set(EloanConstants.MAIN_ID, mainId);
		return result;
	}// ;

	/**
	 * <pre>
	 * 處理月份長度為1碼時自動在前面加上0	Ex:1->01,3->03...etc
	 * @param str String
	 * @return String
	 * </pre>
	 */
	private String checkLength(String str) {
		if (Util.trim(str).length() == 1) {
			str = "0" + str;
		}
		return Util.trim(str);
	}

	/**
	 * <pre>
	 * 將DB欄位分割送到相對應前端欄位2
	 * @param date String
	 * @param dateFormate String
	 * @return String
	 * </pre>
	 */
	private String getYMDofDate(String date, String dateFormate) {
		String dateArray[] = date.split("-");
		dateFormate = Util.trim(dateFormate).toUpperCase();
		if (dateArray.length >= 3) {
			if ("Y".equals(dateFormate)) {
				// Year
				return dateArray[0];
			} else if ("M".equals(dateFormate)) {
				// Month
				return dateArray[1];
			} else if ("D".equals(dateFormate)) {
				// Day
				return dateArray[2];
			}
		}
		return UtilConstants.Mark.SPACE;
	}

	/**
	 * 顯示往來彙總日期範圍錯誤訊息
	 * 
	 * @param sbMsg
	 *            日期範圍錯誤訊息
	 * @param popMsg1
	 *            i18n property檔1
	 * @param popMsg2
	 *            i18n property檔2
	 * @param popMsg3
	 *            i18n property檔3
	 * @param sbDate
	 *            使用者輸入日期範圍
	 * @param sbDbDate
	 *            DB查詢日期範圍
	 * @throws CapException
	 */
	private void showError(StringBuilder sbMsg, String popMsg1, String popMsg2,
			String popMsg3, StringBuilder sbDate, StringBuilder sbDbDate) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		sbMsg.append(pop.getProperty(popMsg1)).append(sbDate.toString())
				.append(pop.getProperty(popMsg2)).append(sbDbDate.toString())
				.append("\t").append(pop.getProperty(popMsg3));
		
		String hostAddr = sysParameterService
			.getParamValue(SysParamConstants.MAIL_ADDRESS_HOST);
		boolean isTestEmail = true; // 是否為測試信件
		
		if (Util.equals(Util.trim(hostAddr), "")) {
			isTestEmail = "true".equals(PropUtil.getProperty("isTestEmail")) ? true
					: false; // 是否為測試信件
		} else {
			if (StringUtils.indexOf(hostAddr, "@notes.") >= 0) { // Production
				isTestEmail = false;
			} else {
				isTestEmail = true;
			}
		}
		
		if (isTestEmail) {
			// 測試環境不檔
			logger.error(UtilConstants.AJAX_RSP_MSG.注意, sbMsg.toString());
		} else {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.注意, sbMsg.toString()), getClass());
		}
	}

	/**
	 * 計算貢獻度(集團)、放款額度(集團)、放款餘額(集團)、活期存款(集團)、 貢獻度(關係)、放款額度(關係)、放款餘額(關係)、活期存款(關係)
	 * 之後儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveTotal(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String LMS1205S07Form03 = params.getString("LMS1205S07Form03");
		JSONObject json = JSONObject.fromObject(LMS1205S07Form03);
		String queryDateS = params.getString("queryDateS");
		String queryDateE = params.getString("queryDateE");
		CapAjaxFormResult form;
		if (json != null && !json.isEmpty()) {
			form = DataParse.toResult(json);
		} else {
			form = new CapAjaxFormResult();
			json = new JSONObject();
		}
		json.put("queryDateS", queryDateS);
		json.put("queryDateE", queryDateE);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, Long> map = new HashMap<String, Long>();
		// 開始進行計算
		lms9535Service.setTotal(mainId, map, json);
		// 將計算後的結果設定到前端欄位
		form.set("profit01", Util.trim(map.get("profit01")));
		form.set("loanQuota01", Util.trim(map.get("loanQuota01")));
		form.set("loanAvgBal01", Util.trim(map.get("loanAvgBal01")));
		form.set("depTime01", Util.trim(map.get("depTime01")));
		form.set("profit02", Util.trim(map.get("profit02")));
		form.set("loanQuota02", Util.trim(map.get("loanQuota02")));
		form.set("loanAvgBal02", Util.trim(map.get("loanAvgBal02")));
		form.set("depTime02", Util.trim(map.get("depTime02")));
		if (params.getAsBoolean("showMsg", true)) {
			// 印出計算完畢訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0021"));
		}
		result.set("LMS1205S07Form03", form);
		return result;
	}// ;

	/**
	 * 查詢往來彙總
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s04a(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L120S04A l120s04a = lms9535Service.findL120s04aByOid(oid);
		if (l120s04a == null) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		CapAjaxFormResult myForm2Result = DataParse.toResult(l120s04a);
		String[] strs = l120s04a.getCustRelation().split(",");
		List<String> list = new ArrayList<String>();
		for (int i = 0; i < strs.length; i++) {
			list.add(strs[i]);
		}

		StringBuilder relDateS = new StringBuilder();
		StringBuilder relDateE = new StringBuilder();
		StringBuilder depMemo = new StringBuilder();
		StringBuilder loanQMemo = new StringBuilder();
		StringBuilder loanABMemo = new StringBuilder();
		StringBuilder exchgMemo = new StringBuilder();
		StringBuilder derMemo = new StringBuilder();
		StringBuilder trustMemo = new StringBuilder();
		StringBuilder wealthMemo = new StringBuilder();
		StringBuilder salaryMemo = new StringBuilder();
		StringBuilder cardComMemo = new StringBuilder();
		StringBuilder cardBrnMemo = new StringBuilder();
		StringBuilder GEBMemo = new StringBuilder();
		StringBuilder GEBLCMemo = new StringBuilder();
		StringBuilder profitMemo = new StringBuilder();

		if (Util.isEmpty(l120s04a.getQueryDateS())) {
			relDateS.append(UtilConstants.Mark.SPACE);
		} else {
			relDateS.append(Util.isEmpty(CapDate.formatDate(
					l120s04a.getQueryDateS(),
					UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
					: CapDate.formatDate(l120s04a.getQueryDateS(),
							UtilConstants.DateFormat.YYYY_MM_DD)
							.substring(0, 7));
		}
		if (Util.isEmpty(l120s04a.getQueryDateE())) {
			relDateE.append(UtilConstants.Mark.SPACE);
		} else {
			relDateE.append(Util.isEmpty(CapDate.formatDate(
					l120s04a.getQueryDateE(),
					UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
					: CapDate.formatDate(l120s04a.getQueryDateE(),
							UtilConstants.DateFormat.YYYY_MM_DD)
							.substring(0, 7));
		}

		// 開始設定值到前端欄位
		if (Util.isNotEmpty(l120s04a.getQueryDateS())
				&& Util.isNotEmpty(l120s04a.getQueryDateS())) {
			myForm2Result
					.set("queryDateS_",
							Util.isEmpty(CapDate.formatDate(
									l120s04a.getQueryDateS(),
									UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
									: CapDate
											.formatDate(
													l120s04a.getQueryDateS(),
													UtilConstants.DateFormat.YYYY_MM_DD)
											.substring(0, 7));
			myForm2Result
					.set("queryDateE_",
							Util.isEmpty(CapDate.formatDate(
									l120s04a.getQueryDateE(),
									UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
									: CapDate
											.formatDate(
													l120s04a.getQueryDateE(),
													UtilConstants.DateFormat.YYYY_MM_DD)
											.substring(0, 7));

			depMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form1"));
			loanQMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form2"));
			loanABMemo.append(relDateS).append("~").append(relDateE);
			exchgMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form3"));
			derMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form3"));
			trustMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form4"));
			wealthMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form5"));
			salaryMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form6"));
			cardComMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form7"));
			cardBrnMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form8"));
			GEBMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form9"));
			GEBLCMemo.append(relDateS).append("~").append(relDateE);
			profitMemo.append(relDateS).append("~").append(relDateE);

			// 設定資料基期
			myForm2Result.set("depMemo", depMemo.toString());
			myForm2Result.set("loanQMemo", loanQMemo.toString());
			myForm2Result.set("loanABMemo", loanABMemo.toString());
			myForm2Result.set("exchgMemo", exchgMemo.toString());
			myForm2Result.set("derMemo", derMemo.toString());
			myForm2Result.set("trustMemo", trustMemo.toString());
			myForm2Result.set("wealthMemo", wealthMemo.toString());
			myForm2Result.set("salaryMemo", salaryMemo.toString());
			myForm2Result.set("cardComMemo", cardComMemo.toString());
			myForm2Result.set("cardComMemo1", cardComMemo.toString());
			myForm2Result.set("cardBrnMemo", cardBrnMemo.toString());
			myForm2Result.set("GEBMemo", GEBMemo.toString());
			myForm2Result.set("GEBLCMemo", GEBLCMemo.toString());
			myForm2Result.set("profitMemo", profitMemo.toString());
		}
		myForm2Result.set("createBY2",
				UtilConstants.Casedoc.L120s04aCreateBY.系統產生.equals(l120s04a
						.getCreateBY()) ? pop.getProperty("L1205S07.createBY1")
						: pop.getProperty("L1205S07.createBY2"));
		myForm2Result.set("prtFlag2",
				UtilConstants.Casedoc.L120s04aPrtFlag.要列印.equals(l120s04a
						.getPrtFlag()) ? pop.getProperty("L1205S07.prtFlag1")
						: pop.getProperty("L1205S07.prtFlag2"));
		myForm2Result.set("custRelation", list);
		JSONObject json = new JSONObject();
		if (Util.isNotEmpty(l120s04a.getQueryDateS())) {
			json.put(
					"queryDateS",
					Util.isEmpty(CapDate.formatDate(l120s04a.getQueryDateS(),
							UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
							: CapDate.formatDate(l120s04a.getQueryDateS(),
									UtilConstants.DateFormat.YYYY_MM_DD)
									.substring(0, 7));
		}
		if (Util.isNotEmpty(l120s04a.getQueryDateE())) {
			json.put(
					"queryDateE",
					Util.isEmpty(CapDate.formatDate(l120s04a.getQueryDateE(),
							UtilConstants.DateFormat.YYYY_MM_DD)) ? UtilConstants.Mark.SPACE
							: CapDate.formatDate(l120s04a.getQueryDateE(),
									UtilConstants.DateFormat.YYYY_MM_DD)
									.substring(0, 7));
		}
		result.set("LMS1205S07Form03", json.toString());
		result.set("tLMS1205S07Form03", myForm2Result);
		return result;
	}

	/**
	 * 取得完整Id(統編加重覆序號)-- 重覆序號為零會自動改成空白
	 * 
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	private String getAllCust(String custId, String dupNo) {
		StringBuilder strb = new StringBuilder();
		if ("0".equals(dupNo)) {
			dupNo = UtilConstants.Mark.SPACE;
		}
		return strb.append(CapString.fillString(custId, 10, false, ' '))
				.append(dupNo).toString();
	}

	/**
	 * 重新引進往來彙總
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unused", "rawtypes" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult rQueryL120s04a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String queryDateS = Util.trim(params.getString("queryDateS"));
		String queryDateE = Util.trim(params.getString("queryDateE"));

		// 當YYYY-MM格式時才需要加上-01
		if (queryDateS.length() == 7) {
			queryDateS += "-01";
		}
		if (queryDateE.length() == 7) {
			queryDateE += "-01";
		}
		L120M01A meta = l120m01aDao.findByMainId(mainId);
		L120S04A l120s04a = lms9535Service.findL120s04aByOid(oid);
		if (l120s04a == null) {
			l120s04a = new L120S04A();
			l120s04a.setMainId(mainId);
			l120s04a.setCustId(custId);
			l120s04a.setDupNo(dupNo);
			l120s04a.setCustName(custName);
			l120s04a.setPrtFlag(UtilConstants.Casedoc.L120s04aPrtFlag.要列印);
			l120s04a.setCreator(user.getUserId());
			l120s04a.setCreateTime(CapDate.getCurrentTimestamp());
			l120s04a.setCreateBY(UtilConstants.Casedoc.L120s04aCreateBY.人工產生);
			// // 請先儲存此文件!
			// throw new CapMessageException(RespMsgHelper.getMessage(parent,
			// "EFD0040"), getClass());
		}
		// L120M01A l120m01a = service1201.findL120m01aByMainId(l120s04a
		// .getMainId());
		List<L120S04A> l120s04as = new ArrayList<L120S04A>();
		// 活期存款
		long depTime = 0;
		// 定期存款
		long depFixed = 0;
		// 額度
		long loanQuota = 0;
		// 平均餘額
		long loanAvgBal = 0;
		// 平均動用率
		BigDecimal loanAvgRate = new BigDecimal("0");
		// 進口筆數
		long exchgImpRec = 0;
		// 進口金額
		long exchgImpAmt = 0;
		// 出口筆數
		long exchgExpRec = 0;
		// 出口金額
		long exchgExpAmt = 0;
		// 匯出筆數
		long exchgOutRec = 0;
		// 匯出金額
		long exchgOutAmt = 0;
		// 匯入筆數
		long exchgInRec = 0;
		// 匯入金額
		long exchgInAmt = 0;
		// 選擇權
		long derOption = 0;
		// 利率交換
		long derRateExchg = 0;
		// 換匯換利
		long derCCS = 0;
		// 遠匯
		// String derDraft = "0";
		// 遠匯(含SWAP)
		long derSWAP = 0;
		// 國內外基金債券
		long trustBond = 0;
		// 基金保管
		long trustFund = 0;
		// 集管
		long trustSetAcct = 0;
		// 有價證券信託
		// String trustSecurities = "0";
		// 不動產信託
		// String trustREITs = "0";
		// 福儲信託
		// String trustWelDep = "0";
		// 其他信託
		long trustOther = 0;
		// 信託
		long wealthTrust = 0;
		// 保險佣金
		long wealthInsCom = 0;
		// 雙元投資
		long wealthInvest = 0;
		// 薪轉戶數
		long salaryRec = 0;
		// 定期定額戶數
		long salaryFixed = 0;
		// 房貸戶數
		long salaryMortgage = 0;
		// 消貸戶數
		long salaryConsumption = 0;
		// 信用卡持卡人數
		long salaryCard = 0;
		// 個人網銀戶數
		long salaryNetwork = 0;
		// 商務卡
		long cardCommercial = 0;
		// 非商務卡
		long cardNoneCommercial = 0;
		// 聯名卡
		String cardCoBranded = "N";
		// 台幣交易筆數
		long GEBTWDRec = 0;
		// 外幣交易筆數
		long GEBOTHRec = 0;
		// 信用狀交易筆數
		long GEBLCRec = 0;
		// 利潤貢獻度
		// String profit = "0";
		// 活期存款
		String depTime1 = "0";
		// 定期存款
		String depFixed1 = "0";
		// 額度
		String loanQuota1 = "0";
		// 平均餘額
		String loanAvgBal1 = "0";
		// 平均動用率
		String loanAvgRate1 = "0";
		// 進口筆數
		String exchgImpRec1 = "0";
		// 進口金額
		String exchgImpAmt1 = "0";
		// 出口筆數
		String exchgExpRec1 = "0";
		// 出口金額
		String exchgExpAmt1 = "0";
		// 匯出筆數
		String exchgOutRec1 = "0";
		// 匯出金額
		String exchgOutAmt1 = "0";
		// 匯入筆數
		String exchgInRec1 = "0";
		// 匯入金額
		String exchgInAmt1 = "0";
		// 選擇權
		String derOption1 = "0";
		// 利率交換
		String derRateExchg1 = "0";
		// 換匯換利
		String derCCS1 = "0";
		// 遠匯
		// String derDraft1 = "0";
		// 遠匯(含SWAP)
		String derSWAP1 = "0";
		// 國內外基金債券
		String trustBond1 = "0";
		// 基金保管
		String trustFund1 = "0";
		// 集管
		String trustSetAcct1 = "0";
		// 有價證券信託
		// String trustSecurities1 = "0";
		// 不動產信託
		// String trustREITs1 = "0";
		// 福儲信託
		// String trustWelDep1 = "0";
		// 其他信託
		String trustOther1 = "0";
		// 信託
		String wealthTrust1 = "0";
		// 保險佣金
		String wealthInsCom1 = "0";
		// 雙元投資
		String wealthInvest1 = "0";
		// 薪轉戶數
		String salaryRec1 = "0";
		// 定期定額戶數
		String salaryFixed1 = "0";
		// 房貸戶數
		String salaryMortgage1 = "0";
		// 消貸戶數
		String salaryConsumption1 = "0";
		// 信用卡持卡人數
		String salaryCard1 = "0";
		// 個人網銀戶數
		String salaryNetwork1 = "0";
		// 商務卡
		String cardCommercial1 = "0";
		// 非商務卡
		String cardNoneCommercial1 = "0";
		// 聯名卡
		String cardCoBranded1 = "N";
		// 台幣交易筆數
		String GEBTWDRec1 = "0";
		// 外幣交易筆數
		String GEBOTHRec1 = "0";
		// 信用狀交易筆數
		String GEBLCRec1 = "0";
		// 利潤貢獻度
		// String profit1 = "0";

		// M-104-0172-001 二維表收信新增AR買方額度餘額
		String IN_LN_FA_B = "0";
		String IN_LN_FA_S = "0";
		long in_ln_fa_b = 0;
		long in_ln_fa_s = 0;
		String IN_LN_FACT_AMT_FA_S = "0";
		String IN_LN_FACT_AMT_FA_B = "0";
		long in_ln_fact_amt_fa_b = 0;
		long in_ln_fact_amt_fa_s = 0;

		String IN_CC_CC_ACT = UtilConstants.Mark.SPACE;
		String BR_CD = UtilConstants.Mark.SPACE;
		// 依目前簽案行做計算幣別
		BranchRate branchRate = lmsService.getBranchRate(user.getUnitNo());

		try {
			String manageId = l120s04a.getCustId();
			String manageDup = l120s04a.getDupNo();
			List<?> rows6 = this.dwdbService.findDW_MD_CUPFM_OTS_selCYC_MN(
					manageId, manageDup, queryDateS, queryDateE);
			Iterator<?> it6 = rows6.iterator();
			while (it6.hasNext()) {
				Map<?, ?> dataMap6 = (Map<?, ?>) it6.next();
				String date = CapDate.formatDate(CapDate.parseDate(String
						.valueOf(dataMap6.get("CYC_MN"))), "yyyy-MM-dd");
				dataMap6.get("CUST_KEY");
				BR_CD = Util.trim(dataMap6.get("BR_CD")); // 999代表全行
				depTime1 = Util.trim(dataMap6.get("IN_DP"));
				dataMap6.get("IN_DP_G");
				depFixed1 = Util.trim(dataMap6.get("IN_CT"));
				loanAvgBal1 = Util.trim(dataMap6.get("IN_LN_USE")); // 餘額
				loanAvgRate1 = Util.trim(dataMap6.get("IN_LN_AVGRT")); // 平均動用率
				loanQuota1 = Util.trim(dataMap6.get("IN_LN_FACT_AMT")); // 額度
				exchgImpAmt1 = Util.trim(dataMap6.get("IN_IM"));
				exchgExpAmt1 = Util.trim(dataMap6.get("IN_EX_BP"));
				exchgImpRec1 = Util.trim(dataMap6.get("IN_IM_TXN"));
				exchgExpRec1 = Util.trim(dataMap6.get("IN_EX_TXN"));
				exchgOutAmt1 = Util.trim(dataMap6.get("IN_OR"));
				exchgInAmt1 = Util.trim(dataMap6.get("IN_IR"));
				exchgOutRec1 = Util.trim(dataMap6.get("IN_OR_TXN"));
				exchgInRec1 = Util.trim(dataMap6.get("IN_IR_TXN"));
				derOption1 = Util.trim(dataMap6.get("IN_DV_OP"));
				derRateExchg1 = Util.trim(dataMap6.get("IN_DV_RE"));
				derCCS1 = Util.trim(dataMap6.get("IN_DV_ER"));
				derSWAP1 = Util.trim(dataMap6.get("IN_DV_FR"));
				wealthTrust1 = Util.trim(dataMap6.get("IN_WM_F_FEE"));
				wealthInsCom1 = Util.trim(dataMap6.get("IN_WM_I_FEE"));
				wealthInvest1 = Util.trim(dataMap6.get("IN_WM_S_FEE"));
				trustFund1 = Util.trim(dataMap6.get("IN_TR_FU"));
				trustSetAcct1 = Util.trim(dataMap6.get("IN_TR_CF"));
				trustBond1 = Util.trim(dataMap6.get("IN_TR_SC"));
				trustOther1 = Util.trim(dataMap6.get("IN_TR_OTS"));
				cardCommercial1 = Util.trim(dataMap6.get("IN_CC_CC"));
				cardNoneCommercial1 = Util
						.nullToSpace(dataMap6.get("IN_CC_IV"));
				IN_CC_CC_ACT = Util.trim(dataMap6.get("IN_CC_CC_ACT"));
				cardCoBranded1 = Util.trim(dataMap6.get("IN_CC_JC_ACT"));
				salaryRec1 = Util.trim(dataMap6.get("IN_ST"));
				salaryFixed1 = Util.trim(dataMap6.get("IN_ST_FD"));
				salaryMortgage1 = Util.trim(dataMap6.get("IN_ST_LN_1"));
				salaryConsumption1 = Util.trim(dataMap6.get("IN_ST_LN_2"));
				salaryCard1 = Util.trim(dataMap6.get("IN_ST_CC"));
				salaryNetwork1 = Util.trim(dataMap6.get("IN_ST_NB"));
				GEBTWDRec1 = Util.trim(dataMap6.get("IN_GEB_NTD_TXN"));
				GEBOTHRec1 = Util.trim(dataMap6.get("IN_GEB_NTD_N_TXN"));
				GEBLCRec1 = Util.trim(dataMap6.get("IN_GEB_LC_TXN"));
				dataMap6.get("DW_CR_DT");
				dataMap6.get("DW_LST_MNT_DT");

				// M-104-0172-001 二維表收信新增AR買方額度餘額
				// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
				IN_LN_FA_B = Util.nullToSpace(dataMap6.get("IN_LN_FA_B"));
				// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
				IN_LN_FA_S = Util.nullToSpace(dataMap6.get("IN_LN_FA_S"));
				// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
				IN_LN_FACT_AMT_FA_B = Util.nullToSpace(dataMap6
						.get("IN_LN_FACT_AMT_FA_B"));
				// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
				IN_LN_FACT_AMT_FA_S = Util.nullToSpace(dataMap6
						.get("IN_LN_FACT_AMT_FA_S"));

				if (date.equals(queryDateE)) {
					loanQuota += Util.parseLong(loanQuota1);
					trustFund += Util.parseLong(trustFund1);
					trustSetAcct += Util.parseLong(trustSetAcct1);
					trustBond += Util.parseLong(trustBond1);
					trustOther += Util.parseLong(trustOther1);

					salaryRec += Util.parseLong(salaryRec1);
					salaryFixed += Util.parseLong(salaryFixed1);
					salaryMortgage += Util.parseLong(salaryMortgage1);
					salaryConsumption += Util.parseLong(salaryConsumption1);
					salaryCard += Util.parseLong(salaryCard1);
					salaryNetwork += Util.parseLong(salaryNetwork1);
					if (UtilConstants.DEFAULT.是.equals(cardCoBranded1)) {
						cardCoBranded = UtilConstants.DEFAULT.是;
					} else {
						cardCoBranded = UtilConstants.DEFAULT.否;
					}

					// M-104-0172-001 二維表收信新增AR買方額度餘額
					// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
					in_ln_fact_amt_fa_b += Util.parseLong(IN_LN_FACT_AMT_FA_B);
					// 授信-應收帳款賣方有效額度(等值台幣)(資料來源LNF02P)
					in_ln_fact_amt_fa_s += Util.parseLong(IN_LN_FACT_AMT_FA_S);

				}
				if (UtilConstants.Casedoc.往來彙總用全行.equals(BR_CD)) {
					// 全行、用來計算平均動用率
					loanAvgRate = loanAvgRate.add(new BigDecimal(loanAvgRate1));
				}
				depTime += Util.parseLong(depTime1);
				depFixed += Util.parseLong(depFixed1);
				loanAvgBal += Util.parseLong(loanAvgBal1);
				exchgImpAmt += Util.parseLong(exchgImpAmt1);
				exchgExpAmt += Util.parseLong(exchgExpAmt1);
				exchgImpRec += Util.parseLong(exchgImpRec1);
				exchgExpRec += Util.parseLong(exchgExpRec1);
				exchgOutAmt += Util.parseLong(exchgOutAmt1);
				exchgInAmt += Util.parseLong(exchgInAmt1);
				exchgOutRec += Util.parseLong(exchgOutRec1);
				exchgInRec += Util.parseLong(exchgInRec1);
				derOption += Util.parseLong(derOption1);
				derRateExchg += Util.parseLong(derRateExchg1);
				derCCS += Util.parseLong(derCCS1);
				derSWAP += Util.parseLong(derSWAP1);
				wealthTrust += Util.parseLong(wealthTrust1);
				wealthInsCom += Util.parseLong(wealthInsCom1);
				wealthInvest += Util.parseLong(wealthInvest1);
				cardCommercial += Util.parseLong(cardCommercial1);
				cardNoneCommercial += Util.parseLong(cardNoneCommercial1);
				GEBTWDRec += Util.parseLong(GEBTWDRec1);
				GEBOTHRec += Util.parseLong(GEBOTHRec1);
				GEBLCRec += Util.parseLong(GEBLCRec1);

				// M-104-0172-001 二維表收信新增AR買方額度餘額
				// 應收帳款無追索買方承購平均餘額(IN_LN_FA_B)
				in_ln_fa_b += Util.parseLong(IN_LN_FA_B);
				// 應收帳款無追索權賣方融資平均餘額(IN_LN_FA_S)
				in_ln_fa_s += Util.parseLong(IN_LN_FA_S);

			}
			// 算起迄年月的差異月數
			int monDiff = (Util.parseInt((queryDateE.length() < 4) ? null
					: queryDateE.substring(0, 4)) * 12 + Util
					.parseInt((queryDateE.length() < 7) ? null : queryDateE
							.substring(5, 7)))
					- (Util.parseInt((queryDateS.length() < 4) ? null
							: queryDateS.substring(0, 4)) * 12 + Util
							.parseInt((queryDateS.length() < 7) ? null
									: queryDateS.substring(5, 7))) + 1;
			// 換算仟元
			l120s04a.setLoanQuota((long) Math.round((double) loanQuota / 1000));
			l120s04a.setTrustFund((long) Math.round((double) trustFund / 1000));
			l120s04a.setTrustSetAcct((long) Math
					.round((double) trustSetAcct / 1000));
			l120s04a.setTrustBond((long) Math.round((double) trustBond / 1000));
			l120s04a.setTrustOther((long) Math
					.round((double) trustOther / 1000));
			l120s04a.setExchgImpAmt((long) Math
					.round((double) exchgImpAmt / 1000));
			l120s04a.setExchgExpAmt((long) Math
					.round((double) exchgExpAmt / 1000));
			l120s04a.setExchgOutAmt((long) Math
					.round((double) exchgOutAmt / 1000));
			l120s04a.setExchgInAmt((long) Math
					.round((double) exchgInAmt / 1000));
			l120s04a.setDerOption((long) Math.round((double) derOption / 1000));
			l120s04a.setDerRateExchg((long) Math
					.round((double) derRateExchg / 1000));
			l120s04a.setDerCCS((long) Math.round((double) derCCS / 1000));
			l120s04a.setDerSWAP((long) Math.round((double) derSWAP / 1000));
			l120s04a.setWealthTrust((long) Math
					.round((double) wealthTrust / 1000));
			l120s04a.setWealthInsCom((long) Math
					.round((double) wealthInsCom / 1000));
			l120s04a.setWealthInvest((long) Math
					.round((double) wealthInvest / 1000));
			l120s04a.setCardCommercial((long) Math
					.round((double) cardCommercial / 1000));
			l120s04a.setCardNoneCommercial((long) Math
					.round((double) cardNoneCommercial / 1000));
			// 交易筆數不須處理
			l120s04a.setExchgImpRec((int) exchgImpRec);
			l120s04a.setExchgExpRec((int) exchgExpRec);
			l120s04a.setExchgOutRec((int) exchgOutRec);
			l120s04a.setExchgInRec((int) exchgInRec);
			l120s04a.setSalaryRec(salaryRec);
			l120s04a.setSalaryFixed(salaryFixed);
			l120s04a.setSalaryMortgage(salaryMortgage);
			l120s04a.setSalaryConsumption(salaryConsumption);
			l120s04a.setSalaryCard(salaryCard);
			l120s04a.setSalaryNetwork(salaryNetwork);
			l120s04a.setGEBTWDRec(GEBTWDRec);
			l120s04a.setGEBOTHRec(GEBOTHRec);
			l120s04a.setGEBLCRec(GEBLCRec);
			l120s04a.setCardCoBranded(cardCoBranded);
			// 算月平均【不】換算仟元
			l120s04a.setLoanAvgRate(loanAvgRate.divide(
					BigDecimal.valueOf(monDiff), 0, BigDecimal.ROUND_HALF_UP)
					.doubleValue());
			// 算月平均並換算仟元
			l120s04a.setDepTime((long) Math
					.round((depTime / (double) monDiff) / 1000));
			l120s04a.setDepFixed((long) Math
					.round((depFixed / (double) monDiff) / 1000));
			l120s04a.setLoanAvgBal((long) Math
					.round((loanAvgBal / (double) monDiff) / 1000));
			// 設定其他欄位
			// 有價證券信託
			l120s04a.setTrustSecurities((long) 0);
			// 不動產信託
			l120s04a.setTrustREITs((long) 0);
			// 福儲信託
			l120s04a.setTrustWelDep((long) 0);

			// M-104-0172-001 二維表收信新增AR買方額度餘額
			// 授信-應收帳款買方有效額度(等值台幣)(資料來源LNF02P)
			l120s04a.setRcvBuyFactAmt((long) Math
					.round((double) in_ln_fact_amt_fa_b / 1000));
			// 授信-應收帳款無追索權買方承購月平均餘額(等值台幣)(資料來源LNF150)
			l120s04a.setRcvBuyAvgBal((long) Math
					.round((in_ln_fa_b / (double) monDiff) / 1000));

			// 讀取並計算貢獻度
			List<?> rows7 = this.dwdbService.findDW_DM_CUBCPCM_TOTAL_ATTRIBUTE(
					manageId, manageDup, queryDateS, queryDateE);
			List<?> rows8 = this.dwdbService
					.findDW_DM_CUBCPCMOVS_TOTAL_ATTRIBUTE(manageId, manageDup,
							queryDateS, queryDateE);
			Iterator<?> it7 = rows7.iterator();
			Iterator<?> it8 = rows8.iterator();
			long conTri = 0;
			long conSalaryTri = 0;
			long conTrustFdtaTri = 0;

			// 國內貢獻度(存款,非存款)
			if (it7.hasNext()) {
				Map<?, ?> dataMap7 = (Map<?, ?>) it7.next();
				conTri += Util.parseLong(Util.trim(dataMap7
						.get("TOTAL_ATTRIBUTE")));
				conSalaryTri += Util.parseLong(Util.trim(dataMap7
						.get("SLDP_TOTAL")));
				conTrustFdtaTri += Util.parseLong(Util.trim(dataMap7
						.get("FDTA_T_TOTAL")));
			}
			// 海外貢獻度(非存款) Miller added at 2012/07/27
			if (it8.hasNext()) {
				Map<?, ?> dataMap8 = (Map<?, ?>) it8.next();
				double seaBal = Util.parseDouble(Util.trim(dataMap8
						.get("TOTAL_ATTRIBUTE")));
				double seaSalaryBal = Util.parseDouble(Util.trim(dataMap8
						.get("SLDP_TOTAL")));
				double seaTrustFdtaBal = Util.parseDouble(Util.trim(dataMap8
						.get("FDTA_T_TOTAL")));
				String curr = Util.trim(dataMap8.get("CURR"));
				if (seaBal != 0) {
					seaBal = branchRate.toTWDAmt(
							(Util.isEmpty(curr)) ? "TWD" : curr,
							LMSUtil.toBigDecimal(seaBal)).doubleValue();
				}
				if (seaSalaryBal != 0) {
					seaSalaryBal = branchRate.toTWDAmt(
							(Util.isEmpty(curr)) ? "TWD" : curr,
							LMSUtil.toBigDecimal(seaSalaryBal)).doubleValue();
				}
				if (seaTrustFdtaBal != 0) {
					seaTrustFdtaBal = branchRate.toTWDAmt(
							(Util.isEmpty(curr)) ? "TWD" : curr,
							LMSUtil.toBigDecimal(seaTrustFdtaBal))
							.doubleValue();
				}
				conTri += seaBal;
				conSalaryTri += seaSalaryBal;
				conTrustFdtaTri += seaTrustFdtaBal;
			}

			// 開始透過AS400 取得海外存款貢獻度 Miller added at 2012/07/20
			Map mapAs400 = null;
			if (meta != null) {
				String typCd = Util.trim(meta.getTypCd());
				if (UtilConstants.Casedoc.typCd.海外.equals(typCd)) {
					try {
						Date dQueryDateS = CapDate.getDate(queryDateS,
								UtilConstants.DateFormat.YYYY_MM_DD);
						Date dQueryDateE = CapDate.getDate(queryDateE,
								UtilConstants.DateFormat.YYYY_MM_DD);
						String elf003SDate = CapDate.formatDate(dQueryDateS,
								"yyyyMM");
						String elf003EDate = CapDate.formatDate(dQueryDateE,
								"yyyyMM");
						mapAs400 = elf001Srv
								.findELF003ProfitContributeByIdDate(
										Util.trim(manageId),
										Util.trim(manageDup),
										Util.trim(meta.getCaseBrId()),
										elf003SDate, elf003EDate);
					} catch (GWException gw) {
						throw gw;
					} catch (Exception e) {
						logger.error(e.getMessage());
					}
				}
			}

			BigDecimal exchangeRate = null;

			if (!CollectionUtils.isEmpty(mapAs400)) {
				String tmpStr = MapUtils.getString(mapAs400, "ELF003_LOC_CURR");
				if (!CapString.isEmpty(tmpStr)) {
					exchangeRate = branchRate.toTWDRate(tmpStr);
				} else {
					exchangeRate = BigDecimal.ONE;
				}
				tmpStr = MapUtils.getString(mapAs400, "ELF003_AMT", "0");
				BigDecimal tmpBD = new BigDecimal(tmpStr);
				conTri += tmpBD.multiply(exchangeRate).longValue();
			}

			conTri = (long) Math.round(conTri / 1000);
			l120s04a.setProfit(conTri);

			conSalaryTri = LMSUtil.toBigDecimal(conSalaryTri)
					.divide(new BigDecimal("1000"), BigDecimal.ROUND_HALF_UP)
					.longValue();
			l120s04a.setProfitSalary(conSalaryTri);

			conTrustFdtaTri = LMSUtil.toBigDecimal(conTrustFdtaTri)
					.divide(new BigDecimal("1000"), BigDecimal.ROUND_HALF_UP)
					.longValue();
			l120s04a.setProfitTrustFdta(conTrustFdtaTri);

			// 起始日(民國年)
			StringBuilder relDateS = new StringBuilder();
			String dateS = Util.addZeroWithValue(CapDate
					.convertDateToTaiwanYear((queryDateS.length() < 4) ? null
							: queryDateS.substring(0, 4)), 3);
			String dateE = Util.addZeroWithValue(CapDate
					.convertDateToTaiwanYear((queryDateE.length() < 4) ? null
							: queryDateE.substring(0, 4)), 3);
			relDateS.append(dateS)
					.append("/")
					.append((queryDateS.length() < 7) ? UtilConstants.Mark.SPACE
							: queryDateS.substring(5, 7));
			// 迄日(民國年)
			StringBuilder relDateE = new StringBuilder();
			relDateE.append(dateE)
					.append("/")
					.append((queryDateE.length() < 7) ? UtilConstants.Mark.SPACE
							: queryDateE.substring(5, 7));

			l120s04as.add(l120s04a);
			// l120s04as = service1201.findL120s04a(l120s04a.getMainId(),
			// l120m01a.getCustId(), l120m01a.getDupNo(),
			// l120m01a.getCustName(), queryDateS, queryDateE);
		} catch (DataAccessResourceFailureException e) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0048"), getClass());
		}
		CapAjaxFormResult myForm2Result = new CapAjaxFormResult();
		if (!l120s04as.isEmpty()) {
			// String[] delColumns =
			// {"createBY","custId","dupNo","custName","typCd","custRelation","prtFlag","queryDateS","queryDateE"};
			for (L120S04A model : l120s04as) {
				if (l120s04a.getCustId().equals(model.getCustId())
						&& l120s04a.getDupNo().equals(model.getDupNo())
						&& l120s04a.getCustName().equals(model.getCustName())) {
					// 有找到符合的資料
					myForm2Result = DataParse.toResult(model);
					break;
				}
			}
		}
		if (Util.isEmpty(Util.trim(myForm2Result.getResult()))) {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		List<String> list = new ArrayList<String>();
		if (Util.isNotEmpty(Util.trim(l120s04a.getCustRelation()))) {
			String[] strs = l120s04a.getCustRelation().split(",");
			for (int i = 0; i < strs.length; i++) {
				list.add(strs[i]);
			}
		}

		StringBuilder relDateS = new StringBuilder();
		StringBuilder relDateE = new StringBuilder();
		StringBuilder depMemo = new StringBuilder();
		StringBuilder loanQMemo = new StringBuilder();
		StringBuilder loanABMemo = new StringBuilder();
		StringBuilder exchgMemo = new StringBuilder();
		StringBuilder derMemo = new StringBuilder();
		StringBuilder trustMemo = new StringBuilder();
		StringBuilder wealthMemo = new StringBuilder();
		StringBuilder salaryMemo = new StringBuilder();
		StringBuilder cardComMemo = new StringBuilder();
		StringBuilder cardBrnMemo = new StringBuilder();
		StringBuilder GEBMemo = new StringBuilder();
		StringBuilder GEBLCMemo = new StringBuilder();
		StringBuilder profitMemo = new StringBuilder();

		if (Util.isEmpty(queryDateS)) {
			relDateS.append(UtilConstants.Mark.SPACE);
		} else {
			relDateS.append((queryDateS.length() < 7) ? UtilConstants.Mark.SPACE
					: queryDateS.substring(0, 7));
		}
		if (Util.isEmpty(queryDateE)) {
			relDateE.append(UtilConstants.Mark.SPACE);
		} else {
			relDateE.append((queryDateE.length() < 7) ? UtilConstants.Mark.SPACE
					: queryDateE.substring(0, 7));
		}

		// 開始設定值到前端欄位
		if (Util.isNotEmpty(queryDateS) && Util.isNotEmpty(queryDateE)) {
			myForm2Result.set("queryDateS_",
					(queryDateS.length() < 7) ? UtilConstants.Mark.SPACE
							: queryDateS.substring(0, 7));
			myForm2Result.set("queryDateE_",
					(queryDateE.length() < 7) ? UtilConstants.Mark.SPACE
							: queryDateE.substring(0, 7));

			depMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form1"));
			loanQMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form2"));
			loanABMemo.append(relDateS).append("~").append(relDateE);
			exchgMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form3"));
			derMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form3"));
			trustMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form4"));
			wealthMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form5"));
			salaryMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form6"));
			cardComMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form7"));
			cardBrnMemo.append(relDateE).append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form8"));
			GEBMemo.append(relDateS).append("~").append(relDateE)
					.append(UtilConstants.Mark.HTMLSPACE)
					.append(pop.getProperty("L1205S07.form9"));
			GEBLCMemo.append(relDateS).append("~").append(relDateE);
			profitMemo.append(relDateS).append("~").append(relDateE);

			// 設定資料基期
			myForm2Result.set("depMemo", depMemo.toString());
			myForm2Result.set("loanQMemo", loanQMemo.toString());
			myForm2Result.set("loanABMemo", loanABMemo.toString());
			myForm2Result.set("exchgMemo", exchgMemo.toString());
			myForm2Result.set("derMemo", derMemo.toString());
			myForm2Result.set("trustMemo", trustMemo.toString());
			myForm2Result.set("wealthMemo", wealthMemo.toString());
			myForm2Result.set("salaryMemo", salaryMemo.toString());
			myForm2Result.set("cardComMemo", cardComMemo.toString());
			myForm2Result.set("cardBrnMemo", cardBrnMemo.toString());
			myForm2Result.set("GEBMemo", GEBMemo.toString());
			myForm2Result.set("GEBLCMemo", GEBLCMemo.toString());
			myForm2Result.set("profitMemo", profitMemo.toString());
		}
		myForm2Result.set(
				"createBY2",
				UtilConstants.Casedoc.L120s04aCreateBY.系統產生.equals(Util
						.nullToSpace(myForm2Result.get("createBY"))) ? pop
						.getProperty("L1205S07.createBY1") : pop
						.getProperty("L1205S07.createBY2"));
		myForm2Result.set(
				"prtFlag2",
				UtilConstants.Casedoc.L120s04aPrtFlag.要列印.equals(Util
						.nullToSpace(myForm2Result.get("prtFlag"))) ? pop
						.getProperty("L1205S07.prtFlag1") : pop
						.getProperty("L1205S07.prtFlag2"));
		myForm2Result.set("custRelation", list);
		JSONObject json = new JSONObject();
		json.put("queryDateS",
				(queryDateS.length() < 7) ? null : queryDateS.substring(0, 7));
		json.put("queryDateE",
				(queryDateE.length() < 7) ? null : queryDateE.substring(0, 7));
		result.set("LMS1205S07Form03", json.toString());
		result.set("tLMS1205S07Form03", myForm2Result);
		return result;
	}

	/**
	 * 取消列印關係戶名單
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult cancelPrint(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		// String mainId = params.getString(EloanConstants.MAIN_ID);
		List<L120S04A> list = new ArrayList<L120S04A>();
		List<L120S04A> listDel = new ArrayList<L120S04A>();
		for (String oid : oidArray) {
			L120S04A l120s04a = lms9535Service.findL120s04aByOid(oid);
			if (l120s04a != null) {
				// 有資料(更新儲存)
				if (UtilConstants.Casedoc.L120s04aCreateBY.系統產生.equals(Util
						.trim(l120s04a.getCreateBY()))) {
					l120s04a.setPrtFlag(UtilConstants.Casedoc.L120s04aPrtFlag.不列印);
					list.add(l120s04a);
				} else if (UtilConstants.Casedoc.L120s04aCreateBY.人工產生
						.equals(Util.trim(l120s04a.getCreateBY()))) {
					listDel.add(l120s04a);
				}
			}
		}
		// 儲存
		// L120M01A model = lms9535Service.findL120m01aByMainId(mainId);
		try {
			lms9535Service.saveL120s04aList(list);
			// 刪除人工資料
			if (!listDel.isEmpty()) {
				lms9535Service.deleteListL120s04a(listDel);
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}
		return result;
	}// ;

	/**
	 * 恢復已取消列印關係戶名單
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult undoPrint(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = ",";
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		// String mainId = params.getString(EloanConstants.MAIN_ID);
		List<L120S04A> list = new ArrayList<L120S04A>();
		for (String oid : oidArray) {
			L120S04A l120s04a = lms9535Service.findL120s04aByOid(oid);
			if (l120s04a != null) {
				// 有資料(更新儲存)
				l120s04a.setPrtFlag(UtilConstants.Casedoc.L120s04aPrtFlag.要列印);
				list.add(l120s04a);
			}
		}
		// 儲存
		// L120M01A model = lms9535Service.findL120m01aByMainId(mainId);
		try {
			// lms9535Service.saveCancelPrint(model, list);
			lms9535Service.saveL120s04aList(list);
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出執行成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}
		return result;
	}// ;

	/**
	 * 刪除往來彙總
	 * 
	 * @param params
	 *            PageParameter
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteL120s04a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		List<L120S04A> list1 = lms9535Service.findL120s04aByMainId(mainId);
		List<L120S04B> list2 = lms9535Service.findL120s04bByMainId(mainId);
		List<L120S04C> list3 = lms9535Service.findL120s04cByMainId(mainId);
		List<DocFile> docFiles = docFileService.findByIDAndName(mainId,
				"RPTNoList", "LMS1205R24A.xls");
		JSONObject json = new JSONObject();
		// if (list != null) {
		// // 有資料就刪除
		// lms9535Service.deleteListL120s04a(list);
		// }
		lms9535Service.deleteAllWanLai(list1, list2, list3, docFiles);
		// 初始化集團合計和關係合計
		json.put("profit01", UtilConstants.Mark.SPACE);
		json.put("loanQuota01", UtilConstants.Mark.SPACE);
		json.put("loanAvgBal01", UtilConstants.Mark.SPACE);
		json.put("depTime01", UtilConstants.Mark.SPACE);
		json.put("profit02", UtilConstants.Mark.SPACE);
		json.put("loanQuota02", UtilConstants.Mark.SPACE);
		json.put("loanAvgBal02", UtilConstants.Mark.SPACE);
		json.put("depTime02", UtilConstants.Mark.SPACE);
		json.put("queryDateS", UtilConstants.Mark.SPACE);
		json.put("queryDateE", UtilConstants.Mark.SPACE);
		result.set("LMS1205S07Form03", json.toString());
		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}// ;

	/**
	 * 查詢往來彙總與實績彙總表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult queryL120s04b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formL120s04b = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custName = Util.trim(params.getString("custName"));
		L120S04B l120s04b = lms9535Service.findL120s04bByOid(oid);
		// L120M01A meta = l120m01aDao.findByMainId(mainId);

        // J-111-0052 修改借戶暨關係戶與本行往來實績彙總表
        formL120s04b.set("showHins", "N");  // 預設值
		if (l120s04b != null) {
			formL120s04b.add(DataParse.toResult(l120s04b));
			formL120s04b.set("grpYear", NumConverter.delCommaString(Util
					.trim(formL120s04b.get("grpYear"))));
			formL120s04b
					.set("mainGrpAvgRate",
							(Util.isEmpty(Util.trim(l120s04b
									.getMainGrpAvgRate()))) ? "N.A."
									: NumConverter.addComma(l120s04b
											.getMainGrpAvgRate()));
			formL120s04b
					.set("demandAvgRate",
							(Util.isEmpty(Util.trim(l120s04b.getDemandAvgRate()))) ? "N.A."
									: NumConverter.addComma(l120s04b
											.getDemandAvgRate()));

			formL120s04b.set("mainGrpAvgRateM", (Util.isEmpty(Util
					.trim(l120s04b.getMainGrpAvgRateM()))) ? "N.A."
					: NumConverter.addComma(l120s04b.getMainGrpAvgRateM()));

			formL120s04b.set("mainGrpAvgRateR", (Util.isEmpty(Util
					.trim(l120s04b.getMainGrpAvgRateR()))) ? "N.A."
					: NumConverter.addComma(l120s04b.getMainGrpAvgRateR()));

			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			boolean newGrpGrade = lmsService.isNewGrpGrade(
					Util.trim(l120s04b.getGrpYear()), true);
			String defultNoGrade = lmsService.getGrpNoGrade(newGrpGrade);

			if (newGrpGrade) {
				// (大A)、(中B)....
				formL120s04b.set(
						"grpSizeLvlShow",
						lmsService.getGrpSizeLvlShow(
								Util.trim(l120s04b.getGrpSize()),
								Util.trim(l120s04b.getGrpLevel())));
			} else {
				formL120s04b.set("grpSizeLvlShow", "");
			}

            // J-111-0052 修改借戶暨關係戶與本行往來實績彙總表
			boolean showHins = lmsService.isHinsCust(
					Util.addSpaceWithValue(Util.trim(l120s04b.getKeyCustId()), 10)
							+ Util.trim(l120s04b.getKeyDupNo()));
            formL120s04b.set("showHins", (showHins ? "Y" : "N"));
            if(showHins) {
				DecimalFormat dfRate = new DecimalFormat("###,###,##0.########");
				// 因為 DataParse.toResult( 小數點後取五位   資料算到第六位
				formL120s04b.set("hinsRate", (l120s04b.getHinsRate() == null ? "0" :
						dfRate.format(l120s04b.getHinsRate())));
				formL120s04b.set("hinsAvgRate", (l120s04b.getHinsAvgRate() == null ? "0" :
						dfRate.format(l120s04b.getHinsAvgRate())));
			}
		}
		List<L120S04C> list = lms9535Service.findL120s04cByMainId(mainId);
		// 借款人與本行往來實績彙總表list
		List<L120S04C> listKind1 = new ArrayList<L120S04C>();
		// 借款人與本行往來實績彙總表資料年月(起)list
		List<String> dateKind1 = new ArrayList<String>();
		// 借款人暨關係戶與本行往來實績彙總表list
		List<L120S04C> listKind2 = new ArrayList<L120S04C>();
		// 借款人暨關係戶與本行往來實績彙總表資料年月(起)list
		List<String> dateKind2 = new ArrayList<String>();
		if (!list.isEmpty()) {
			for (L120S04C l120s04c : list) {
				if ("1".equals(Util.trim(l120s04c.getDocKind()))) {
					// 借款人與本行往來實績彙總表
					listKind1.add(l120s04c);
					String docDate = Util.trim(l120s04c.getDocDate());
					if (Util.isNotEmpty(docDate)) {
						dateKind1.add(docDate);
					}
				} else if ("2".equals(Util.trim(l120s04c.getDocKind()))) {
					// 借款人暨關係戶與本行往來實績彙總表
					listKind2.add(l120s04c);
					String docDate = l120s04c.getDocDate();
					if (Util.isNotEmpty(docDate)) {
						dateKind2.add(docDate);
					}
				}
			}
		}

		formL120s04b.set("custName04C", Util.toSemiCharString(custName));
		// 進行排序
		Collections.sort(dateKind1);
		Collections.sort(dateKind2);
		formL120s04b.add(queryL120S04c(listKind1, dateKind1, "1"));
		formL120s04b.add(queryL120S04c(listKind2, dateKind2, "2"));

		// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
		List<L120S04E> listL120s04e = lms9535Service.findL120s04eByMainId(mainId);
		List<L120S04E> listL120s04e1 = new ArrayList<L120S04E>();
		List<String> s04eDate1 = new ArrayList<String>();
		if (!listL120s04e.isEmpty()) {
			for (L120S04E l120s04e : listL120s04e) {
				listL120s04e1.add(l120s04e);
				String docDate = Util.trim(l120s04e.getDocDate());
				if (Util.isNotEmpty(docDate)) {
					s04eDate1.add(docDate);
				}
			}
		}
		Collections.sort(s04eDate1);
		formL120s04b.add(queryL120S04e(listL120s04e1, s04eDate1, "1"));

		result.set("formL120s04b", formL120s04b);
		return result;
	}

	/**
	 * 將關係戶於本行往來實績彙總表明細檔設值到前端
	 * 
	 * @param listKind
	 * @param dateKind
	 * @param type
	 * @return
	 * @throws CapException
	 */
	private IResult queryL120S04c(List<L120S04C> listKind,
			List<String> dateKind, String type) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] l120S04cCol = new String[] { "docDate", "docDateE",
				"avgDepositAmt", "avgLoanAmt", "rcvBuyAvgAmt", "rcvSellAvgAmt",
				"exportAmt", "importAmt", "profitAmt", "profitSalaryAmt",
				"profitTrustFdtaAmt", "profitRate", "oid" };
		int index = 0;
		if (!dateKind.isEmpty() && !listKind.isEmpty()) {
			if ("1".equals(type)) {
				// 借款人與本行往來實績彙總表
				index = 1;
			} else if ("2".equals(type)) {
				// 借款人暨關係戶與本行往來實績彙總表
				index = 4;
			}
			for (String docDateS : dateKind) {
				for (L120S04C l120s04c : listKind) {
					JSONObject json = DataParse.toJSON(l120s04c);
					if (docDateS.equals(Util.trim(l120s04c.getDocDate()))) {
						for (String l120s04cCol : l120S04cCol) {
							result.set(l120s04cCol + index,
									setDocDate(l120s04cCol, index, json)
							// ("docDateS3".equals(l120s04cCol + index) ||
							// "docDateS6"
							// .equals(l120s04cCol + index)) ?
							// CapDate.formatDateFormatToyyyyMMdd(Util
							// .trim(json.get(l120s04cCol)), "yyyy-MM")
							// + " ~ "
							// +
							// CapDate.formatDateFormatToyyyyMMdd(Util.trim(json.get("docDateE")),"yyyy-MM")
							// : Util.trim(json.get(l120s04cCol))
							);
						}
						index++;
						break;
					}
				}
			}
		}
		return result;
	}

	private IResult queryL120S04e(List<L120S04E> listKind,
								 List<String> dateKind, String type) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// J-112-0147 配合企金處，「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度欄位，增列「其他(信託等)」。
		String[] l120S04eCols = new String[] { "docDate", "loanPcAmt", "depFxPcAmt", "wmPcAmt",
				"dervPcAmt", "salaryPcAmt", "cardPcAmt", "otherPcAmt", "totalPcAmt", "oid" };

		int dateSize = 2;
		if (dateKind != null && !dateKind.isEmpty()){
			dateSize = dateKind.size();
		}
		int index = 0;
		if (!dateKind.isEmpty() && !listKind.isEmpty()) {
			for (String docDateS : dateKind) {
				for (L120S04E l120s04e : listKind) {
					JSONObject json = DataParse.toJSON(l120s04e);
					if (docDateS.equals(Util.trim(l120s04e.getDocDate()))
							&& Util.equals(Util.nullToSpace(l120s04e.getDocKind()), type)) {
						for (String s04eCol : l120S04eCols) {
							String rLabel = ("s04e_" + s04eCol + "_" + (dateSize-index-1) + "_" + type);
							result.set(rLabel, (json.get(s04eCol) == null ? ""
									: Util.nullToSpace(json.get(s04eCol))));
						}
						index++;
						break;
					}
				}
			}
		}
		return result;
	}

	/**
	 * 針對第一欄日期格式化
	 * 
	 * @param l120s04cCol
	 *            欄位key
	 * @param index
	 *            欄位指標
	 * @param json
	 *            Json物件
	 * @return 格式化後的日期
	 */
	private String setDocDate(String l120s04cCol, int index, JSONObject json) {
		// 第一欄、第二欄日期格式為yyyy顯示，第三欄日期格式為yyyy-MM顯示
		if ("docDate1".equals(l120s04cCol + index)
				|| "docDate2".equals(l120s04cCol + index)
				|| "docDate4".equals(l120s04cCol + index)
				|| "docDate5".equals(l120s04cCol + index)
				|| "docDate3".equals(l120s04cCol + index)
				|| "docDate6".equals(l120s04cCol + index)) {
			return Util.trim(json.get(l120s04cCol));
		}
		// else
		// // 第三欄日期格式為yyyy-MM顯示
		// if ("docDateS3".equals(l120s04cCol + index)
		// || "docDateS6".equals(l120s04cCol + index)) {
		// return Util.trim(json.get(l120s04cCol));
		// }
		else {
			return getProfitRate(l120s04cCol, index, json);
		}
	}

	/**
	 * 取得報酬率
	 * 
	 * @param l120s04cCol
	 *            欄位key
	 * @param index
	 *            欄位指標
	 * @param json
	 *            Json物件
	 * @return 報酬率
	 */
	private String getProfitRate(String l120s04cCol, int index, JSONObject json) {
		if ("profitRate".equals(l120s04cCol)) {
			// 若A-B+C = 0，則報酬率分母為零傳回N.A.
			if (LMSUtil
					.toBigDecimal(
							NumConverter.delCommaString(Util.trim(json
									.get("avgLoanAmt"))))
					.subtract(
							LMSUtil.toBigDecimal(NumConverter
									.delCommaString(Util.trim(json
											.get("rcvBuyAvgAmt")))))
					.add(LMSUtil.toBigDecimal(NumConverter.delCommaString(Util
							.trim(json.get("rcvSellAvgAmt")))))
					.compareTo(BigDecimal.ZERO) == 0) {
				return "N.A.";
			} else {
				return Util.trim(json.get(l120s04cCol));
			}
		} else {
			return Util.trim(json.get(l120s04cCol));
		}
	}

	/**
	 * 儲存往來彙總與實績彙總表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s04b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String formL120s04b = Util.trim(params.getString("formL120s04b"));
		JSONObject jsonForm = Util.isNotEmpty(formL120s04b) ? JSONObject
				.fromObject(formL120s04b) : new JSONObject();
		L120S04B l120s04b = lms9535Service.findL120s04bByOid(oid);
		if (l120s04b != null) {
			DataParse.toBean(jsonForm, l120s04b);
			l120s04b.setDemandAvgRate("N.A.".equals(jsonForm
					.getString("demandAvgRate")) ? null : LMSUtil
					.toBigDecimal(jsonForm.get("demandAvgRate")));
			l120s04b.setMainGrpAvgRate("N.A.".equals(jsonForm
					.getString("mainGrpAvgRate")) ? null : LMSUtil
					.toBigDecimal(jsonForm.get("mainGrpAvgRate")));
			l120s04b.setMainGrpAvgRateM("N.A.".equals(jsonForm
					.getString("mainGrpAvgRateM")) ? null : LMSUtil
					.toBigDecimal(jsonForm.get("mainGrpAvgRateM")));
			l120s04b.setMainGrpAvgRateR("N.A.".equals(jsonForm
					.getString("mainGrpAvgRateR")) ? null : LMSUtil
					.toBigDecimal(jsonForm.get("mainGrpAvgRateR")));

			// 往來彙總實績明細表
			List<L120S04C> listL120s04c = lms9535Service
					.findL120s04cByMainId(Util.trim(l120s04b.getMainId()));
			if (!listL120s04c.isEmpty()) {
				// 開始將前端值塞入明細檔群組內...
				listL120s04c = setL120S04Cs(jsonForm);
			}
			lms9535Service.saveL120s04bc(listL120s04c, l120s04b);

			// J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。
			List<L120S04E> listL120s04e = lms9535Service.findL120s04eByMainId(Util.trim(l120s04b.getMainId()));
			if (!listL120s04e.isEmpty()) {
				// 開始將前端值塞入明細檔群組內...
				listL120s04e = setL120S04Es(jsonForm);
			}
			lms9535Service.saveL120s04eList(listL120s04e);
		}
		// 印出儲存成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		return result;
	}// ;

	/**
	 * 將前端值塞入明細檔群組Model內
	 * 
	 * @param jsonForm
	 * @return
	 * @throws CapException
	 */
	private List<L120S04C> setL120S04Cs(JSONObject jsonForm)
			throws CapException {
		List<L120S04C> list = new ArrayList<L120S04C>();
		String[] l120S04cCol = new String[] { "docDate", "docDateE",
				"avgDepositAmt", "avgLoanAmt", "rcvBuyAvgAmt", "rcvSellAvgAmt",
				"exportAmt", "importAmt", "profitAmt", "profitSalaryAmt",
				"profitTrustFdtaAmt", "profitRate" };
		for (int index = 1; index <= 6; index++) {
			// 找出哪個Model
			if (Util.isNotEmpty(jsonForm.get("oid" + index))) {
				L120S04C model = lms9535Service.findL120s04cByOid(Util
						.trim(jsonForm.get("oid" + index)));
				if (model != null) {
					// 開始設定明細內容
					for (String l120s04cCol : l120S04cCol) {
						if (jsonForm.containsKey(l120s04cCol + index)) {
							if (l120s04cCol.equals("docDateS")
									|| l120s04cCol.equals("docDateE")) {
								// 日期格式化後儲存寫在這裡
							} else {
								// 將前端值設定到後端Model
								if ("profitRate".equals(l120s04cCol)) {
									// 報酬率
									CapBeanUtil
											.setField(
													model,
													l120s04cCol,
													(Util.trim(jsonForm
															.get(l120s04cCol
																	+ index))
															.equals("N.A.")) ? "0"
															: jsonForm
																	.get(l120s04cCol
																			+ index));
								} else {
									// 其他欄位
									CapBeanUtil.setField(model, l120s04cCol,
											jsonForm.get(l120s04cCol + index));
								}

							}
						}
					}
					list.add(model);
				}
			}
		}
		return list;
	}

	private List<L120S04E> setL120S04Es(JSONObject jsonForm)
			throws CapException {
		List<L120S04E> list = new ArrayList<L120S04E>();
		// J-112-0147 配合企金處，「借戶暨關係戶與本行往來實績彙總表」下方各業務別利潤貢獻度欄位，增列「其他(信託等)」。
		String[] l120S04eCols = new String[] { "docDate", "loanPcAmt", "depFxPcAmt", "wmPcAmt",
				"dervPcAmt", "salaryPcAmt", "cardPcAmt", "otherPcAmt", "totalPcAmt" };
		for (int index = 0; index <= 1; index++) {
			String oid = "";
			if (jsonForm.containsKey("s04e_oid_" + index + "_1")) {
				oid = Util.nullToSpace(jsonForm.get("s04e_oid_" + index + "_1"));
			}
			// 找出哪個Model
			if (Util.isNotEmpty(oid)) {
				L120S04E model = lms9535Service.findL120s04eByOid(oid);
				if (model != null) {
					// 開始設定明細內容
					for (String s04eCol : l120S04eCols) {
						String rLabel = ("s04e_" + s04eCol + "_" + index + "_1");
						if (jsonForm.containsKey(rLabel)) {
							CapBeanUtil.setField(model, s04eCol, jsonForm.get(rLabel));
						}
					}
					list.add(model);
				}
			}
		}
		return list;
	}

	/**
	 * 刪除往來彙總與實績彙總表
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL120s04b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<L120S04B> listS04b = lms9535Service.findL120s04bByMainId(mainId);
		List<L120S04C> listS04c = lms9535Service.findL120s04cByMainId(mainId);
        List<L120S04E> listS04e = lms9535Service.findL120s04eByMainId(mainId);
		if (listS04b != null || listS04c != null || listS04e != null) {
			// 有資料就刪除
			lms9535Service.deleteListL120s04(listS04b, listS04c, listS04e);
		}
		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
				RespMsgHelper.getMainMessage("EFD0019"));
		return result;
	}// ;

	/**
	 * 產生往來實績彙總表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult importL120s04b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("mainId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String queryDateS = Util.trim(params.getString("queryDateS"));
		String queryDateE = Util.trim(params.getString("queryDateE"));
		if (Util.isEmpty(queryDateS) || Util.isEmpty(queryDateE)) {
			// L1205S07.error19=資料查詢期間不得為空白！
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSS07APanel.class);
			throw new CapMessageException(RespMsgHelper.getMessage(
					"EFD0015", pop.getProperty("L1205S07.error19")), getClass());
		}
		lms9535Service.importL120s04b(mainId, custId, dupNo,
				queryDateS, queryDateE);
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}// ;

	/**
	 * 產生主要關係戶與本行授信往來比較表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws WriteException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult creExcel(PageParameters params)
			throws CapException, WriteException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		lms9535xlsService.getContent(params);
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}// ;

	/**
	 * 透過MIS.CUSDATA取得借款人客戶名稱
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			// 轉大寫
			custId = custId.toUpperCase();
		}
		List<Map<String, Object>> custData = icustSrv.findByIdBy0024(custId);
		Map<String, String> map = new TreeMap<String, String>();
		if (custData != null) {
			StringBuilder sbVal = new StringBuilder();
			for (Map<String, Object> mCust : custData) {
				sbVal.setLength(0);
				sbVal.append(Util.trim(mCust.get("CUSTID"))).append(
						Util.trim(mCust.get("DUPNO")));
				map.put(sbVal.toString(), Util.trim(mCust.get("CNAME")));
			}
		} else {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		if (!map.isEmpty()) {
			CapAjaxFormResult selCus = new CapAjaxFormResult(map);
			result.set("selCus", selCus);
		}
		return result;
	}
	
	public IResult calcBisEstimatedReturn(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		BigDecimal bisNoneLoanProfit = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisNoneLoanProfit")));

		BigDecimal bisLoanBal = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisLoanBal")));

		BigDecimal bisFactAmtIncrease = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisFactAmtIncrease")));

		BigDecimal bisEstimatedReturn = BigDecimal.ZERO;
		if ((bisLoanBal.add(bisFactAmtIncrease)).compareTo(BigDecimal.ZERO) > 0) {
			bisEstimatedReturn = bisNoneLoanProfit
					.divide(bisLoanBal.add(bisFactAmtIncrease), 6,
							BigDecimal.ROUND_HALF_UP)
					.multiply(new BigDecimal(100))
					.setScale(2, BigDecimal.ROUND_HALF_UP);
		}

		result.set("bisEstimatedReturn", bisEstimatedReturn.stripTrailingZeros().toPlainString());

		BigDecimal bisNoneLoanProfit_1 = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisNoneLoanProfit_1")));

		BigDecimal bisLoanBal_1 = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisLoanBal_1")));

		BigDecimal bisFactAmtIncrease_1 = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisFactAmtIncrease_1")));

		BigDecimal bisEstimatedReturn_1 = BigDecimal.ZERO;
		if ((bisLoanBal_1.add(bisFactAmtIncrease_1)).compareTo(BigDecimal.ZERO) > 0) {
			bisEstimatedReturn_1 = bisNoneLoanProfit_1
					.divide(bisLoanBal_1.add(bisFactAmtIncrease_1), 6,
							BigDecimal.ROUND_HALF_UP)
					.multiply(new BigDecimal(100))
					.setScale(2, BigDecimal.ROUND_HALF_UP);
		}

		result.set("bisEstimatedReturn_1", bisEstimatedReturn_1.stripTrailingZeros().toPlainString());

		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}
	
	//J-113-0183
	public IResult impBisEstimatedReturn(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S03Panel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String queryDateE0 = params.getString("queryDateE0");
		String queryDateE1 = params.getString("queryDateE1");
		
		if (Util.isEmpty(queryDateE0) || Util.isEmpty(queryDateE1)) {
			// L120S08A.error10=資料查詢期間不得為空白！
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", prop.getProperty("L120S08A.error10")),
					getClass());
		}
		
		// 1:借款人
		// 2:關係企業
		// 3.關係企業+借款人
		String kind = Util.trim(params.getString("kind"));
		String qCustId = Util.trim(params.getString("custId"));
		String qDupNo = Util.trim(params.getString("dupNo"));
		String mainId = Util.trim(params.getString("mainId"));
		BigDecimal newApplyAmt = !Util.trim(params.getString("newApplyAmt")).isEmpty() ? 
				new BigDecimal(Util.trim(params.getString("newApplyAmt"))) : BigDecimal.ZERO;
		BigDecimal bisTwdRate = !Util.trim(params.getString("bisTwdRate")).isEmpty() ? 
				new BigDecimal(Util.trim(params.getString("bisTwdRate"))) : BigDecimal.ZERO;
		
		//新做增額額度 = 使用者輸入的增額額度 * 幣別折台幣匯率
		BigDecimal factAmtIncrease = newApplyAmt.multiply(bisTwdRate).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
		
		BranchRate branchRate = lmsService.getBranchRate(user.getUnitNo());
		JSONObject jsonData = new JSONObject();
		
		StringBuilder strbE = new StringBuilder();
		// 轉換日期成YYYY-MM-01格式
		
		strbE.append(checkLength(queryDateE0)).append("-")
				.append(checkLength(queryDateE1)).append("-").append("01");
		String queryDateE = strbE.toString();
		
		if (Util.isEmpty(queryDateE)) {
			// L120S08A.error10=資料查詢期間不得為空白！
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", prop.getProperty("L120S08A.error10")),
					getClass());
		}
		
		// 檢查查詢起迄************************************
		JSONObject formJson = new JSONObject();
		StringBuilder sbMsg = new StringBuilder();
		String popMsg1 = UtilConstants.Mark.SPACE;
		String popMsg2 = UtilConstants.Mark.SPACE;
		String popMsg3 = "L1205S07.error7";
		StringBuilder sbDate = new StringBuilder();
		StringBuilder sbDbDate = new StringBuilder();
		
		// 查詢起日-往前推半年
		Date dQueryDate6S = CapDate.addMonth(Util.parseDate(queryDateE), -5);
		// 查詢起日-往前推一年
		Date dQueryDate12S = CapDate.addMonth(Util.parseDate(queryDateE), -11);
		// 查詢迄日
		Date dQueryDateE = Util.parseDate(queryDateE);
		
		String queryDateS0 = CapDate.formatDate(dQueryDate12S, "yyyy");
		String queryDateS1 = CapDate.formatDate(dQueryDate12S, "MM");
		
		int getKind = 0;
		getKind = lms1201Service.checkDate(
				CapDate.formatDate(dQueryDate12S, "yyyy-MM-dd"),
				CapDate.formatDate(dQueryDateE, "yyyy-MM-dd"), formJson);
		if (getKind == 0) {
			if (Util.equals(kind, "A")) {
				// A.個體+集團　　2023/01/18 風控處改個體(借款人)；集團(借款人+集團企業)　kind固定為A
				lms9535Service
						.impBisEstimatedReturn("1", mainId, queryDateE,
								jsonData, branchRate, qCustId, qDupNo, true, factAmtIncrease);
				lms9535Service
						.impBisEstimatedReturn("3", mainId, queryDateE,
								jsonData, branchRate, qCustId, qDupNo, true, factAmtIncrease);
			} else {
				lms9535Service.impBisEstimatedReturn(kind, mainId, queryDateE, 
								jsonData, branchRate, qCustId, qDupNo, false, factAmtIncrease);
			}
		} else if (getKind == 1) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			sbDbDate.append(
					getYMDofDate(Util.nullToSpace(formJson.get("MIN_CYC_MN")),
							"Y"))
					.append("/")
					.append(getYMDofDate(
							Util.nullToSpace(formJson.get("MIN_CYC_MN")), "M"));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error8";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 2) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			sbDbDate.append(
					getYMDofDate(Util.nullToSpace(formJson.get("MIN_CYC_MN")),
							"Y"))
					.append("/")
					.append(getYMDofDate(
							Util.nullToSpace(formJson.get("MIN_CYC_MN")), "M"));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error4";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 3) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateS0)).append("/")
					.append(checkLength(queryDateS1));
			popMsg1 = "L1205S07.error1";
			popMsg2 = "L1205S07.error3";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 4) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			sbDbDate.append(
					getYMDofDate(Util.nullToSpace(formJson.get("MAX_CYC_MN")),
							"Y"))
					.append("/")
					.append(getYMDofDate(
							Util.nullToSpace(formJson.get("MAX_CYC_MN")), "M"));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error6";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else if (getKind == 5) {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			sbDbDate.append(
					getYMDofDate(Util.nullToSpace(formJson.get("MAX_CYC_MN")),
							"Y"))
					.append("/")
					.append(getYMDofDate(
							Util.nullToSpace(formJson.get("MAX_CYC_MN")), "M"));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error10";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		} else {
			sbMsg.setLength(0);
			sbDate.setLength(0);
			sbDbDate.setLength(0);
			sbDate.append(checkLength(queryDateE0)).append("/")
					.append(checkLength(queryDateE1));
			popMsg1 = "L1205S07.error2";
			popMsg2 = "L1205S07.error5";
			showError(sbMsg, popMsg1, popMsg2, popMsg3, sbDate, sbDbDate);
		}
		// **************************************************
		
		result.set("bisNoneLoanProfit",
				jsonData.optString("bisNoneLoanProfit", "0"));
		result.set("bisLoanBal", jsonData.optString("bisLoanBal", "0"));
		//result.set("bisFactAmtIncrease", jsonData.optString("bisFactAmtIncrease", "0"));
		result.set("bisFactAmtIncrease", factAmtIncrease.toPlainString());
		
		BigDecimal bisNoneLoanProfit = Util.parseBigDecimal(jsonData.optString("bisNoneLoanProfit", "0"));
		BigDecimal bisLoanBal = Util.parseBigDecimal(jsonData.optString("bisLoanBal", "0"));
		//BigDecimal bisFactAmtIncrease = Util.parseBigDecimal(jsonData.optString("bisFactAmtIncrease", "0"));
		
		BigDecimal bisEstimatedReturn = BigDecimal.ZERO;
		if ((bisLoanBal.add(factAmtIncrease)).compareTo(BigDecimal.ZERO) > 0) {
			bisEstimatedReturn = bisNoneLoanProfit
					.divide(bisLoanBal.add(factAmtIncrease), 6,
							BigDecimal.ROUND_HALF_UP)
					.multiply(new BigDecimal(100))
					.setScale(2, BigDecimal.ROUND_HALF_UP);
		}
		
		result.set("bisEstimatedReturn", bisEstimatedReturn.stripTrailingZeros().toPlainString());
		
		if (Util.equals(kind, "A")) {
			result.set("bisNoneLoanProfit_1", jsonData.optString("bisNoneLoanProfit_1", "0"));
			result.set("bisLoanBal_1", jsonData.optString("bisLoanBal_1", "0"));
			//result.set("bisFactAmtIncrease_1", jsonData.optString("bisFactAmtIncrease_1", "0"));
			result.set("bisFactAmtIncrease_1", factAmtIncrease.toPlainString());
		
			BigDecimal bisNoneLoanProfit_1 = Util.parseBigDecimal(jsonData.optString("bisNoneLoanProfit_1", "0"));
			BigDecimal bisLoanBal_1 = Util.parseBigDecimal(jsonData.optString("bisLoanBal_1", "0"));
			//BigDecimal bisFactAmtIncrease_1 = Util.parseBigDecimal(jsonData.optString("bisFactAmtIncrease_1", "0"));
		
			BigDecimal bisEstimatedReturn_1 = BigDecimal.ZERO;
			if ((bisLoanBal_1.add(factAmtIncrease))
					.compareTo(BigDecimal.ZERO) > 0) {
				bisEstimatedReturn_1 = bisNoneLoanProfit_1
						.divide(bisLoanBal_1.add(factAmtIncrease), 6,
								BigDecimal.ROUND_HALF_UP)
						.multiply(new BigDecimal(100))
						.setScale(2, BigDecimal.ROUND_HALF_UP);
			}
		
			result.set("bisEstimatedReturn_1", bisEstimatedReturn_1.stripTrailingZeros().toPlainString());
		} else {
			result.set("bisNoneLoanProfit_1", "0");
			result.set("bisLoanBal_1", "0");
			result.set("bisFactAmtIncrease_1", "0");
			result.set("bisEstimatedReturn_1", "0");
		}
		
		// 印出執行成功訊息!
		// e-Loan簽報書BIS評估表調整海外分行存款貢獻度之年化處理及非授信貢獻度為負時應顯示之警語
		String negativeAttrbute = jsonData.optString("negativeAttrbute", "");
		if (Util.notEquals(negativeAttrbute, "")) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					"檢核下列統編之非授信貢獻度為負，請確認是否已於AS400 SCR：2DR3進行Earning Rate之維護交易；如已執行，請確認交易是否正確："
							+ "<br>" + negativeAttrbute);
		} else {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}
		
		return result;
	}
	
	//J-113-0183 額度幣別折台幣匯率
	public IResult impBisTwdRate(PageParameters params) throws CapMessageException{
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String curr = Util.trim(params.getString("curr"));
		if(curr.isEmpty()){
			result.set("bisTwdRate", "");
			result.set("rateDate", "");
			return result;
		}
		
		BranchRate branchRate = lmsService.getBranchRate(user.getUnitNo());
		HashMap<String, Rate> misRateMap = branchRate.getMisRateMap();
		Rate rate = misRateMap.get(curr);
		BigDecimal toTwRate = rate.getRate().stripTrailingZeros();
		result.set("bisTwdRate", toTwRate);
		result.set("rateDate", CapDate.formatDate(CapDate.getCurrentTimestamp(), UtilConstants.DateFormat.YYYY_MM_DD));
		
		return result;
	}
	
	//J-113-0183 RORWA計算結果區塊內容之計算
	public IResult calcResult(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		// 營運成本率
		BigDecimal bisBankWorkCost = BigDecimal.ZERO;
		String bankWorkCost = "";
		List<LNF07A> list2 = lnLnf07aService
				.sel_by_key1_orderBykey3Desc("BANK WORK COST");
		if (list2.size() > 0) {
			LNF07A lnf07a2 = list2.get(0);
			// 9(2)V9(5) 整數兩位 小數5位
			// 007600 => 0.76
			bankWorkCost = Util.trim(lnf07a2.getLnf07a_key_4());
			StringBuffer sb2 = new StringBuffer();
			sb2.append(NumberUtils.toInt(bankWorkCost.substring(0, 2)))
					.append(".").append(bankWorkCost.substring(2, 7));
			bankWorkCost = sb2.toString();
		}
		if (Util.equals(bankWorkCost, "")) {
			throw new CapMessageException("無法引進「營運成本」", getClass());
		} else {
			bisBankWorkCost = new BigDecimal(bankWorkCost).setScale(3, BigDecimal.ROUND_HALF_UP);
		}
		result.set("bisBankWorkCost", bisBankWorkCost.stripTrailingZeros().toPlainString());

		// 預期損失率=風險成本(全行平均)
		BigDecimal bisRiskCost = BigDecimal.ZERO;
		String lostRate = "";
		List<LNF07A> list3 = lnLnf07aService
				.sel_by_key1_orderBykey3Desc("LOST RATE");
		if (list3.size() > 0) {
			LNF07A lnf07a3 = list3.get(0);
			// 9(2)V9(5) 整數兩位 小數5位
			// 007600 => 0.76
			lostRate = Util.trim(lnf07a3.getLnf07a_key_4());
			StringBuffer sb3 = new StringBuffer();
			sb3.append(NumberUtils.toInt(lostRate.substring(0, 2)))
					.append(".").append(lostRate.substring(2, 7));
			lostRate = sb3.toString();
		}
		if (Util.equals(lostRate, "")) {
			throw new CapMessageException("無法引進「風險成本(全行平均)」", getClass());
		} else {
			bisRiskCost = new BigDecimal(lostRate).setScale(3, BigDecimal.ROUND_HALF_UP);;
		}
		result.set("bisRiskCost", bisRiskCost.stripTrailingZeros().toPlainString());
		

		BigDecimal bisIncomeRate = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisIncomeRate")));//授信收益率

		BigDecimal bisFtpRate = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisFtpRate")));//資金成本
		
		BigDecimal bisEstimatedReturn = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisEstimatedReturn")));//預估非授信收益率(個體)
		
		BigDecimal bisEstimatedReturn_1 = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("bisEstimatedReturn_1")));//預估非授信收益率(集團)
		
		BigDecimal newApplyAmt = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("newApplyAmt")));//新作增額額度
		
		BigDecimal newCreditLimitAmt = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("newCreditLimitAmt")));//考慮表外信用轉換係數之新作增額額度(表內+表外X信用轉換係數)
		
		BigDecimal riskOffInsAmt = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("riskOffInsAmt")));//風險抵減後暴險額
		
		BigDecimal riskWeight_standard = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("riskWeight_standard")));//風險權數(標準法)
		
		BigDecimal riskWeight_IRB = Util.parseBigDecimal(NumConverter
				.delCommaString(params.getString("riskWeight_IRB")));//風險權數(內評法)
		
		//風險調整後收益(個體)
		BigDecimal bisRiskAdjReturn = bisIncomeRate.subtract(bisFtpRate).subtract(bisBankWorkCost)
										.subtract(bisRiskCost).add(bisEstimatedReturn);
		result.set("bisRiskAdjReturn", bisRiskAdjReturn.stripTrailingZeros().toPlainString());
		
		//風險調整後收益(集團)
		BigDecimal bisRiskAdjReturn_1 = bisIncomeRate.subtract(bisFtpRate).subtract(bisBankWorkCost)
											.subtract(bisRiskCost).add(bisEstimatedReturn_1);
		result.set("bisRiskAdjReturn_1", bisRiskAdjReturn_1.stripTrailingZeros().toPlainString());
		
		//抵減後風險權數(標準法)
		BigDecimal bisRItemD_standard = BigDecimal.ZERO;
		if(newApplyAmt.compareTo(BigDecimal.ZERO) != 0){
			bisRItemD_standard = riskOffInsAmt.multiply(riskWeight_standard).divide(newApplyAmt, 2, BigDecimal.ROUND_HALF_UP);
		}
		result.set("bisRItemD_standard", bisRItemD_standard.stripTrailingZeros().toPlainString());
		
		
		if(bisRItemD_standard.compareTo(BigDecimal.ZERO) != 0){
			//RORWA(標準法,個體)
			BigDecimal bisRorwa_standard = bisRiskAdjReturn.multiply(new BigDecimal(100))
												.divide(bisRItemD_standard, 5, BigDecimal.ROUND_HALF_UP);
			result.set("bisRorwa_standard", bisRorwa_standard.stripTrailingZeros().toPlainString());
			
			//RORWA(標準法,集團)
			BigDecimal bisRorwa_1_standard = bisRiskAdjReturn_1.multiply(new BigDecimal(100))
												.divide(bisRItemD_standard, 5, BigDecimal.ROUND_HALF_UP);
			result.set("bisRorwa_1_standard", bisRorwa_1_standard.stripTrailingZeros().toPlainString());
		} else {
			result.set("bisRorwa_standard", "N.A.");
			result.set("bisRorwa_1_standard", "N.A.");
		}
		
		//不適用內評法的內評法風險權數會留空白，若為此情況將內評法相關計算結果放N/A
		if(Util.trim(params.getString("riskWeight_IRB")).isEmpty()){
			result.set("bisRItemD_IRB", "N.A.");
			result.set("bisRorwa_IRB", "N.A.");
			result.set("bisRorwa_1_IRB", "N.A.");
		} else {
			//抵減後風險權數(內評法)
			BigDecimal bisRItemD_IRB = BigDecimal.ZERO;
			if(newApplyAmt.compareTo(BigDecimal.ZERO) != 0){
				bisRItemD_IRB = newCreditLimitAmt.multiply(riskWeight_IRB).divide(newApplyAmt, 2, BigDecimal.ROUND_HALF_UP);
			}
			result.set("bisRItemD_IRB", bisRItemD_IRB.stripTrailingZeros().toPlainString());
			
			
			if(bisRItemD_IRB.compareTo(BigDecimal.ZERO) != 0){
				//RORWA(內評法,個體)
				BigDecimal bisRorwa_IRB = bisRiskAdjReturn.multiply(new BigDecimal(100))
												.divide(bisRItemD_IRB, 5, BigDecimal.ROUND_HALF_UP);
				result.set("bisRorwa_IRB", bisRorwa_IRB.stripTrailingZeros().toPlainString());
				
				//RORWA(內評法,集團)
				BigDecimal bisRorwa_1_IRB = bisRiskAdjReturn_1.multiply(new BigDecimal(100))
												.divide(bisRItemD_IRB, 5, BigDecimal.ROUND_HALF_UP);
				result.set("bisRorwa_1_IRB", bisRorwa_1_IRB.stripTrailingZeros().toPlainString());
			} else {
				result.set("bisRorwa_IRB", "N.A.");
				result.set("bisRorwa_1_IRB", "N.A.");
			}
		}

		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String fid = params.getString("fileOid");
		if (docFileService.clean(fid)) {
			// EFD0019=INFO|刪除成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		}
		return result;
	}

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 產生集團／關係企業與本行授信往來條件比較表
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 * @throws IOException
	 * @throws WriteException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult creExcel2(PageParameters params)
			throws CapException, WriteException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		lms9535xlsService.getContent(params);
		// 印出執行成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		return result;
	}// ;

}
