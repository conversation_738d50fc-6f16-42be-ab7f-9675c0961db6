/* 
 * AbstractTEJJdbc.java
 */
package com.mega.eloan.lms.tej.service.impl;

import javax.annotation.Resource;
import javax.sql.DataSource;

import tw.com.iisi.cap.context.CapParameter;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;

/**
 * <pre>
 * 企業情報資料庫系統
 * </pre>
 * 
 * @since 2018/6/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2018/6/22,007623,new
 *          </ul>
 */
public class AbstractTEJJdbc {

	EloanJdbcTemplate jdbc;

	@Resource(name = "tejSqlStatement")
	CapParameter sqlp;

	@Resource(name = "tej-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_TEJDB);
		jdbc.setSqlProvider(sqlp);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * getJdbc
	 * 
	 * @return EloanJdbcTemplate
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}

	/**
	 * 取得Sql
	 * 
	 * @param sqlId
	 *            sqlId
	 * @return sqlString
	 */
	public String getSqlBySqlId(String sqlId) {
		return sqlp.getValue(sqlId);
	}

}
