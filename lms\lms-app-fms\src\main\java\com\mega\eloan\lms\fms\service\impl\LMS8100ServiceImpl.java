package com.mega.eloan.lms.fms.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.DocLog;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L300M01ADao;
import com.mega.eloan.lms.dao.L300M01BDao;
import com.mega.eloan.lms.dao.L300M01CDao;
import com.mega.eloan.lms.dao.L300S01ADao;
import com.mega.eloan.lms.fms.service.LMS8100Service;
import com.mega.eloan.lms.model.L300M01A;
import com.mega.eloan.lms.model.L300M01B;
import com.mega.eloan.lms.model.L300M01C;
import com.mega.eloan.lms.model.L300S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 覆審考核表作業
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service("LMS8100Service")
public class LMS8100ServiceImpl extends AbstractCapService implements
		LMS8100Service {
	
	@Resource
	FlowService flowService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	L300M01ADao l300m01aDao;

	@Resource
	L300M01BDao l300m01bDao;
	
	@Resource
	L300M01CDao l300m01cDao;
	
	@Resource
	L300S01ADao l300s01aDao;

	@Resource
	RetrialService retrialService;
	
	@Resource 
	SysParameterService sysParameterService;

	private static Logger logger = LoggerFactory.getLogger(LMS8100ServiceImpl.class);

	/**
	 * J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計。 <br/>
	 * 查詢統計週期 <br/>
	 * 
	 * 每半年 HY <br/>
	 * 每季 S <br/>
	 * 
	 * @param dt
	 * @return
	 */
	@Override
	public String findProduceInterval(String dt) {

		String onStartDate = sysParameterService
				.getParamValue("LMS_J1120461_PRODUCEBYSEASON");
		int days = CapDate.calculateDays(Util.parseDate(dt),
				Util.parseDate(onStartDate));
		String produceInterval = "HY";
		if (days >= 0) {
			produceInterval = "S";
		}
		return produceInterval;
	}
		
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L300M01A) {
					if (Util.isEmpty(((L300M01A) model).getOid())) {
						((L300M01A) model).setCreator(user.getUserId());
						((L300M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l300m01aDao.save((L300M01A) model);

						flowService.start("LMS8100Flow",
								((L300M01A) model).getOid(), user.getUserId(),
								user.getUnitNo());
						docLogService.record(((L300M01A) model).getOid(),
								DocLogEnum.CREATE);
					} else {
						((L300M01A) model).setUpdater(user.getUserId());
						((L300M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l300m01aDao.save((L300M01A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L300M01A) model)
									.getMainId());
							docLogService.record(((L300M01A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof L300M01B) {
					((L300M01B) model).setUpdater(user.getUserId());
					((L300M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l300m01bDao.save((L300M01B) model);
				} else if (model instanceof L300M01C) {
					((L300M01C) model).setUpdater(user.getUserId());
					((L300M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l300m01cDao.save((L300M01C) model);
				} else if (model instanceof L300S01A) {
					((L300S01A) model).setUpdater(user.getUserId());
					((L300S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l300s01aDao.save((L300S01A) model);
				}
			}
		}
	}
	
	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L300M01A.class) {
			return l300m01aDao.findPage(search);
		} else if(clazz == L300M01B.class){
			return l300m01bDao.findPage(search);
		} else if(clazz == L300M01C.class){
			return l300m01cDao.findPage(search);
		} else if(clazz == L300S01A.class){
			return l300s01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByMainId(Class clazz, String mainId) {
		if (clazz == L300M01A.class) {
			return (T) l300m01aDao.findByMainId(mainId);
		} else if (clazz == L300M01B.class) {
			return (T) l300m01bDao.findByMainId(mainId);
		} else if (clazz == L300M01C.class) {
			return (T) l300m01cDao.findByMainId(mainId);
		} else if (clazz == L300S01A.class) {
			return (T) l300s01aDao.findByMainId(mainId);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L300M01A.class) {
			return (T) l300m01aDao.findByOid(oid);
		} else if(clazz == L300M01B.class){
			return (T) l300m01bDao.findByOid(oid);
		} else if(clazz == L300M01C.class){
			return (T) l300m01cDao.findByOid(oid);
		} else if(clazz == L300S01A.class){
			return (T) l300s01aDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz, String mainId) {
		if (clazz == L300M01A.class) {
			return l300m01aDao.findByIndex02(mainId);
		} else if (clazz == L300M01B.class) {
			return l300m01bDao.findByMainId(mainId);
		} else if (clazz == L300M01C.class) {
			return l300m01cDao.findByMainId(mainId);
		} else if (clazz == L300S01A.class) {
			return l300s01aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void flowAction(String mainOid, L300M01A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		try {
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS8100Flow",
						((L300M01A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					if (upMis) {
						L300M01A l300m01a = (L300M01A) findModelByOid(
								L300M01A.class, mainOid);
						// 簽章欄檔取得人員職稱
						List<L300M01B> l300m01blist = l300m01bDao
								.findByMainId(l300m01a.getMainId());
						String apprId = "";
						String reCheckId = "";

						for (L300M01B l300m01b : l300m01blist) {
							String StaffJob = Util.trim(l300m01b.getStaffJob());// 取得人員職稱
							String StaffNo = Util.trim(l300m01b.getStaffNo());// 取得行員代碼
							if (Util.equals(StaffJob, UtilConstants.STAFFJOB.經辦L1)) {// 分行經辦
								apprId = StaffNo;
							} else if (Util.equals(StaffJob, UtilConstants.STAFFJOB.執行覆核主管L4)) {// 分行覆核主管
								reCheckId = StaffNo;
							}
						}
						// 若人員職稱為空值改取m01a上的人員資料
						if (Util.isEmpty(apprId)) {
							apprId = l300m01a.getUpdater();
						}
						if (Util.isEmpty(reCheckId)) {
							reCheckId = l300m01a.getApprover();
						}
					}
				}
			}
			inst.next();
			logger.info("[flowAction]");
		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}		
	}
	
	@Override
	public Map<String, String> getItemTypeMap(String verStr) {
		Map<String, String> typeMap = new LinkedHashMap<String, String>();

		String[] m01jCol = retrialService.getPaCol(verStr, "1");
		String[] m01jColYn = retrialService.getPaCol(verStr, "2");
		String[] m01jColCnt = retrialService.getPaCol(verStr, "3");

		for (String fieldName : m01jCol) {
			if (ArrayUtils.contains(m01jColYn, fieldName)) {
				typeMap.put(fieldName, "YN");
			} else if (ArrayUtils.contains(m01jColCnt, fieldName)) {
				typeMap.put(fieldName, "CNT");
			} else {
				typeMap.put(fieldName, "");
			}
		}

		return typeMap;
	}
	
	@Override
	public Map<String, BigDecimal> getItemScoreMap(String verStr) {
		Map<String, BigDecimal> scoreMap = new LinkedHashMap<String, BigDecimal>();

		if(Util.equals(verStr, LrsUtil.PAVER_20230701)){
			scoreMap.put("paItem01", BigDecimal.valueOf(-0.5));
			scoreMap.put("paItem02", BigDecimal.valueOf(-1.5));
			scoreMap.put("paItem03", BigDecimal.valueOf(-0.5));
			scoreMap.put("paItem04", BigDecimal.valueOf(-1));
			scoreMap.put("paItem05", BigDecimal.valueOf(-1.5));
			scoreMap.put("paItem06", BigDecimal.valueOf(-0.5));
			scoreMap.put("paItem07", BigDecimal.valueOf(-1));
			scoreMap.put("paItem08", BigDecimal.valueOf(-2));
			scoreMap.put("paItem09", BigDecimal.valueOf(-1));
		} else {
			scoreMap.put("paItem01", BigDecimal.valueOf(-0.5));
			scoreMap.put("paItem02", BigDecimal.valueOf(-1));
			scoreMap.put("paItem03", BigDecimal.valueOf(-0.5));
			scoreMap.put("paItem04", BigDecimal.valueOf(-1));
			scoreMap.put("paItem05", BigDecimal.valueOf(-0.5));
			scoreMap.put("paItem06", BigDecimal.valueOf(-0.5));
			scoreMap.put("paItem07", BigDecimal.valueOf(-1));
			scoreMap.put("paItem08", BigDecimal.valueOf(-1));
		}

		return scoreMap;
	}

	@Override
	public String buildPaFormHtml(L300M01A l300m01a, Properties prop) {
		DecimalFormat df = new DecimalFormat("#####0.###");
		StringBuffer paFormHtml = new StringBuffer("");

		if (l300m01a != null) {

		} else {
			l300m01a = new L300M01A();
		}

		String paVer = Util.nullToSpace(l300m01a.getPaVer());
		String propVerStr = (Util.isNotEmpty(paVer) ? ("_" + paVer) : "");
		String[] m01jCol = retrialService.getPaCol(paVer, "1");
		String[] m01jColYn = retrialService.getPaCol(paVer, "2");
		String[] m01jColCnt = retrialService.getPaCol(paVer, "3");

		List<L300S01A> l300s01aList = this.findL300s01aList(l300m01a.getMainId());
		if (l300s01aList != null && !l300s01aList.isEmpty()) {
			paFormHtml.append("<span style='display: inline-block; width: 100%;'>")
					.append(paVer).append(Util.isEmpty(paVer) ? "" : "版").append("</span>");
			paFormHtml.append("<table class='tb2' width='100%' border='0' cellspacing='0' cellpadding='0'>");
			paFormHtml.append("<tr class='hd2'>");
			paFormHtml.append("<td width='53%'>")
					.append(prop.getProperty("L300S01A.itemName"))
					.append("</td>");
			paFormHtml.append("<td width='10%'>")
					.append(prop.getProperty("L300S01A.itemCnt")).append("\r\n").append("(1)")
					.append("</td>");
			paFormHtml.append("<td width='7%'>")
					.append(prop.getProperty("L300S01A.itemScore")).append("\r\n").append("(2)")
					.append("</td>");
			paFormHtml.append("<td width='10%'>")
					.append(prop.getProperty("L300S01A.itemAll")).append("\r\n").append("(3)=(1)×(2)")
					.append("</td>");
			paFormHtml.append("<td width='30%'>")
					.append(prop.getProperty("L300S01A.itemDscr"))
					.append("</td>");
			paFormHtml.append("</tr>");

			for (String fieldName : m01jCol) {	// 為了按照順序
				for (L300S01A l300s01a : l300s01aList) {
					String itemName = Util.nullToSpace(l300s01a.getItemName());
					if(Util.notEquals(fieldName, itemName)){
						continue;
					}

					/*
					l300s01aMap.put(itemName + "_itemType",
							Util.nullToSpace(l300s01a.getItemType()));
					l300s01aMap.put(itemName + "_itemCnt", df.format(l300s01a
							.getItemCnt() == null ? BigDecimal.ZERO : Util
							.parseBigDecimal(l300s01a.getItemCnt())));
					l300s01aMap.put(itemName + "_itemScore", df.format(l300s01a
							.getItemScore() == null ? BigDecimal.ZERO : Util
							.parseBigDecimal(l300s01a.getItemScore())));
					l300s01aMap.put(itemName + "_itemAll", df.format(l300s01a
							.getItemAll() == null ? BigDecimal.ZERO : Util
							.parseBigDecimal(l300s01a.getItemAll())));
					l300s01aMap.put(itemName + "_itemDscr", l300s01a.getItemDscr());
					*/
					String itemType = "";
					String inputStr = "";
					if (ArrayUtils.contains(m01jColYn, itemName)) {
						itemType = "YN";
						inputStr = "戶";
					} else if (ArrayUtils.contains(m01jColCnt, itemName)) {
						itemType = "CNT";
						inputStr = "項";
					} else {
					}

					paFormHtml.append("<tr>");

					paFormHtml.append("<td>")
							.append(prop.getProperty("L300S01A." + itemName + propVerStr))
							.append("</td>");
					paFormHtml.append("<td>");
					paFormHtml.append("<input type='text' id='").append(itemName)
							.append("_itemType' name='").append(itemName)
							.append("_itemType' value='").append(itemType)
							.append("' style='display:none'/>");
					paFormHtml.append("<input type='text' id='").append(itemName)
							.append("_itemCnt' name='").append(itemName)
							.append("_itemCnt' value='")
							.append(df.format(l300s01a.getItemCnt() == null ?
									BigDecimal.ZERO : Util.parseBigDecimal(l300s01a.getItemCnt())))
							.append("' class='max mathit numeric' positiveonly='true' size='3' integer='3' fraction='0' value='0'/>")
							.append(inputStr);
					paFormHtml.append("</td>");
					paFormHtml.append("<td>");
					paFormHtml.append("<input type='text' id='").append(itemName)
							.append("_itemScore' name='").append(itemName)
							.append("_itemScore' value='")
							.append(df.format(l300s01a.getItemScore() == null ?
									BigDecimal.ZERO : Util.parseBigDecimal(l300s01a.getItemScore())))
							.append("' readonly='true' size='1'/>分");
					paFormHtml.append("</td>");
					paFormHtml.append("<td>");
					paFormHtml.append("<input type='text' id='").append(itemName)
							.append("_itemAll' name='").append(itemName)
							.append("_itemAll' value='")
							.append(df.format(l300s01a.getItemAll() == null ?
									BigDecimal.ZERO : Util.parseBigDecimal(l300s01a.getItemAll())))
							.append("' class='totalit' readonly='true' size='1'/>分");
					paFormHtml.append("</td>");
					paFormHtml.append("<td>");
					paFormHtml.append("<textarea id='").append(itemName)
							.append("_itemDscr' name='").append(itemName)
							.append("_itemDscr' cols='25' rows='2' class='max txt_mult' maxlengthC='3400'>")
							.append(l300s01a.getItemDscr()).append("</textarea>");
					paFormHtml.append("</td>");

					paFormHtml.append("</tr>");
				}
			}
			/*
			paFormHtml.append("<tr>");
			paFormHtml.append("<td class='hd1' colspan='3'>");
			paFormHtml.append("<button type='button' id='reCalc'>")
					.append(prop.getProperty("btn.reCalc")).append("</button>")
					.append(prop.getProperty("L300M01A.subTotal")).append("（A）");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>");
			paFormHtml.append("<input type='text' name='subTotal' id='subTotal' readonly='true' size='3'/>分");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>").append("</td>");
			paFormHtml.append("</tr>");

			paFormHtml.append("<tr>");
			paFormHtml.append("<td class='hd1' colspan='3'>");
			paFormHtml.append(prop.getProperty("L300M01A.rsNum")).append("（B）");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>");
			paFormHtml.append("<input type='text' name='rsNum' id='rsNum' readonly='true' size='3'/>件");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>").append("</td>");
			paFormHtml.append("</tr>");

			paFormHtml.append("<tr>");
			paFormHtml.append("<td class='hd1' colspan='3'>");
			paFormHtml.append(prop.getProperty("L300M01A.avgScore")).append("（A/B）（取至小數第三位）");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>");
			paFormHtml.append("<input type='text' name='avgScore' id='avgScore' readonly='true' size='3'/>");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>").append("</td>");
			paFormHtml.append("</tr>");

			paFormHtml.append("<tr class='afterCalc'>");
			paFormHtml.append("<td class='hd1' colspan='3'>");
			paFormHtml.append(prop.getProperty("L300M01A.realScore")).append("（※詳說明二、換算公式）");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>");
			paFormHtml.append("<input type='text' name='realScore' id='realScore' readonly='true' size='3'/>分");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>").append("</td>");
			paFormHtml.append("</tr>");

			// 最後再加上獨立項目：加分、減分
			L300S01A l300s01aP = this.findL300s01a(l300m01a.getMainId(),
					null, "plusItem");
			L300S01A l300s01aM = this.findL300s01a(l300m01a.getMainId(),
					null, "minusItem");
			if (l300s01aP != null) {
				String itemName = Util.nullToSpace(l300s01aP.getItemName());
				paFormHtml.append("<tr>");
				paFormHtml.append("<td colspan='3'>");
				paFormHtml.append(prop.getProperty("L300S01A.plusItem")).append("（※詳說明三、）");
				paFormHtml.append("</td>");
				paFormHtml.append("<td>");
				paFormHtml.append("<input type='text' id='").append(itemName)
						.append("_itemAll' name='").append(itemName)
						.append("_itemAll' class='max numeric' positiveonly='true' size='6' integer='3' fraction='2' value='0' max='10' min='0'/>分");
				paFormHtml.append("</td>");
				paFormHtml.append("<td>");
				paFormHtml.append("<textarea id='").append(itemName)
						.append("_itemDscr' name='").append(itemName)
						.append("_itemDscr' cols='25' rows='2' class='max txt_mult' maxlengthC='3400'></textarea>");
				paFormHtml.append("</td>");
			}
			if (l300s01aM != null) {
				String itemName = Util.nullToSpace(l300s01aM.getItemName());
				paFormHtml.append("<tr>");
				paFormHtml.append("<td colspan='3'>");
				paFormHtml.append(prop.getProperty("L300S01A.minusItem")).append("（※詳說明三、）");
				paFormHtml.append("</td>");
				paFormHtml.append("<td>");
				paFormHtml.append("<input type='text' id='").append(itemName)
						.append("_itemAll' name='").append(itemName)
						.append("_itemAll' class='max numeric' positiveonly='false' size='6' integer='3' fraction='2' value='0' max='0' min='-10'/>分");
				paFormHtml.append("</td>");
				paFormHtml.append("<td>");
				paFormHtml.append("<textarea id='").append(itemName)
						.append("_itemDscr' name='").append(itemName)
						.append("_itemDscr' cols='25' rows='2' class='max txt_mult' maxlengthC='3400'></textarea>");
				paFormHtml.append("</td>");
			}

			paFormHtml.append("<tr class='afterCalc'>");
			paFormHtml.append("<td class='hd1' colspan='3'>");
			paFormHtml.append(prop.getProperty("L300M01A.totalScore"));
			paFormHtml.append("</td>");
			paFormHtml.append("<td>");
			paFormHtml.append("<input type='text' name='totalScore' id='totalScore' readonly='true' size='3'/>分");
			paFormHtml.append("</td>");
			paFormHtml.append("<td>").append("</td>");
			paFormHtml.append("</tr>");
			*/
			paFormHtml.append("</table>");
		}

		return paFormHtml.toString();
	}
	
	@Override
	public List<L300M01A> findL300m01aList(String ownBrId, String branchId, String bgnDate, String endDate) {
		return l300m01aDao.findByIndex01(ownBrId, branchId, 
				CapDate.getDate(bgnDate, "yyyy-MM-dd"), CapDate.getDate(endDate, "yyyy-MM-dd"));
	}
	
	@Override
	public L300M01A findL300m01a(String ownBrId, String branchId, String bgnDate, String endDate) {
		List<L300M01A> r = l300m01aDao.findByIndex01(ownBrId, branchId, 
				CapDate.getDate(bgnDate, "yyyy-MM-dd"), CapDate.getDate(endDate, "yyyy-MM-dd"));
		if (r != null && r.size() > 0) {
			return r.get(0);
		}
		return null;
	}
	
	/**
	 * J-112-0461 授信覆審考核表-編製中-下拉選項統計由每半年改為每季統計
	 */
	@Override
	public L300M01A findL300m01aExist(String ownBrId, String branchId,
			String bgnDate, String endDate) {
		List<L300M01A> r = l300m01aDao.findL300m01a(ownBrId, branchId,
				CapDate.getDate(bgnDate, "yyyy-MM-dd"),
				CapDate.getDate(endDate, "yyyy-MM-dd"));
		if (r != null && r.size() > 0) {
			return r.get(0);
		}
		return null;
	}
	
	@Override
	public void saveL300m01aList(List<L300M01A> list, boolean isNew) {
		/*
		 * 呼叫時機：
		 * 1. 起案newL300m01a	
		 * 		isNew=true
		 * 2. 產製排名表genRbPdf => calcRealScore
		 * 		isNew=false
		 **/
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L300M01A l300m01a : list) {
			l300m01a.setUpdater(user.getUserId());
			l300m01a.setUpdateTime(CapDate.getCurrentTimestamp());
			if(isNew){
				// 呼叫此function才會有 起案紀錄 docLogService."建檔"
				this.save(l300m01a);
			} else {
				// 產排名表時有更新 l300m01a 欄位 
				docLogService.record(l300m01a.getOid(), DocLogEnum.OTHER);
			}
		}
		if(!isNew){
			l300m01aDao.save(list);
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public boolean deleteL300m01as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L300M01A> l300m01as = new ArrayList<L300M01A>();
		List<L300S01A> l300s01as = new ArrayList<L300S01A>();
		
		for (int i = 0, size = oids.length; i < size; i++) {
			L300M01A l300m01a = (L300M01A) findModelByOid(L300M01A.class, oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l300m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l300m01a.setUpdater(user.getUserId());
			l300m01as.add(l300m01a);
			docLogService.record(l300m01a.getOid(), DocLogEnum.DELETE);
			
			String mainId = l300m01a.getMainId();
			List<L300S01A> l300s01aList = (List<L300S01A>) findListByMainId(
					L300S01A.class, mainId);
			if (!Util.isEmpty(l300s01aList)){
				for (L300S01A l300s01a : l300s01aList) {
					// 直接刪除 
					l300s01as.add(l300s01a);
				}
			}
			
			// 刪除附件	.clean ==> 直接刪除		.delete ==> setDeletedTime
			List<DocFile> docFiles = docFileService.findByIDAndName(
					l300m01a.getMainId(), "paFormChkList", "");
			if (docFiles != null && !docFiles.isEmpty()) {
				for (DocFile file : docFiles) {
					docFileService.clean(file.getOid());
				}
			}
		}
		if (!l300m01as.isEmpty()) {
			l300m01aDao.save(l300m01as);
			flag = true;
		}
		if (!l300s01as.isEmpty()) {
			l300s01aDao.delete(l300s01as);
		}
		return flag;
	}
	
	@Override
	public L300M01B findL300m01b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l300m01bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}
	
	@Override
	public void saveL300m01bList(List<L300M01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L300M01B l300m01b : list) {
			l300m01b.setUpdater(user.getUserId());
			l300m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l300m01bDao.save(list);
	}
	
	public void deleteL300m01bs(List<L300M01B> l300m01bs) {
		l300m01bDao.delete(l300m01bs);
	}
	
	@Override
	public List<L300S01A> findL300s01aList(String mainId) {
		return l300s01aDao.findByMainId(mainId);
	}
	
	@Override
	public L300S01A findL300s01a(String mainId, String itemType, String itemName) {
		List<L300S01A> r = l300s01aDao.findByIndex01(mainId, itemType, itemName);
		if (r != null && r.size() > 0) {
			return r.get(0);
		}
		return null;
	}
	
	@Override
	public void saveL300s01aList(List<L300S01A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L300S01A l300s01a : list) {
			l300s01a.setUpdater(user.getUserId());
			l300s01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l300s01aDao.save(list);
	}
	
	@Override
	public void deleteL300s01as(List<L300S01A> list) {
		l300s01aDao.delete(list);
	}
	
	@Override
	public List<L300M01A> findL300m01aProduceList(String ownBrId, String docStatus, 
			String bgnDate, String endDate, String produceFlag) {
		return l300m01aDao.findByIndex03(ownBrId, docStatus, 
				CapDate.getDate(bgnDate, "yyyy-MM-dd"), 
				CapDate.getDate(endDate, "yyyy-MM-dd"), produceFlag);
	}
	
	@Override
	public L300M01C findL300m01c(String ownBrId, String bgnDate, String endDate) {
		List<L300M01C> r = l300m01cDao.findByIndex02(ownBrId, null,  
				CapDate.getDate(bgnDate, "yyyy-MM-dd"), CapDate.getDate(endDate, "yyyy-MM-dd"));
		if (r != null && r.size() > 0) {
			return r.get(0);
		}
		return null;
	}
	
	@Override
	public void setBackActLog(Meta meta) {
		// 因為退回用 .flowStart() 會有一筆CREATE
		// 將該筆紀錄改為退回
		DocLog docLog = docLogService.getLastDocLog(meta.getOid());
		if (docLog != null
				&& Util.equals(DocLogEnum.CREATE.getCode(),
						docLog.getActCode())) {
			docLog.setActCode(DocLogEnum.BACK.getCode());
			docLogService.save(docLog);
		}
	}
}
