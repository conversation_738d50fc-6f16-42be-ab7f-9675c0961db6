/* 
 * C160S01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S01D;

/** 動審表匯入明細檔 **/
public interface C160S01DDao extends IGenericDao<C160S01D> {

	C160S01D findByOid(String oid);
	
	C160S01D findByMainIdCustId(String mainId,String custId);
	
	List<C160S01D> findByMainId(String mainId);
	
	C160S01D findByUniqueKey(String mainId, String staffNo, String custId);

	List<C160S01D> findByIndex01(String mainId, String staffNo, String custId);
}