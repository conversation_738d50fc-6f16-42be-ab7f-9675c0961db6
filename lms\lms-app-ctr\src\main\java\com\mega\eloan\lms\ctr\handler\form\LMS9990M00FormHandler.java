/* 
 * LMS9990FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.handler.form;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.ctr.service.LMS9990Service;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L999M01A;

/**
 * <pre>
 * 約據書
 * </pre>
 * 
 * @since 2012/2/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/12,Ice,new
 *          </ul>
 */
@Scope("request")
@DomainClass(L999M01A.class)
public class LMS9990M00FormHandler extends AbstractFormHandler {

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	LMS9990Service lms9990Service;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	CodeTypeService codeTypeService;

	/**
	 * 查詢使用者名稱
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData(PageParameters params)
			throws CapException {
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		String typCd = params.getString("typCd");
		Map<String, String> typCdMap = null;
		Map<String, Object> custdata = misCustdataService.findCustdataSelCname(
				custId, dupNo);
		CapAjaxFormResult result = null;
		CapAjaxFormResult myFormResult = null;
		try {
			if (custdata == null) {
				custdata = new LinkedHashMap<String, Object>();
			}
			typCdMap = codeTypeService.findByCodeType("TypCd");
			if (typCdMap == null)
				typCdMap = new LinkedHashMap<String, String>();
			result = new CapAjaxFormResult();
			myFormResult = new CapAjaxFormResult();
			myFormResult.set("custName",
					CapString.trimNull(custdata.get("CNAME")));
			myFormResult.set("custId", custId);
			myFormResult.set("dupNo", dupNo);
			myFormResult.set("typCdName",
					CapString.trimNull(typCdMap.get(typCd)));
			result.set("CustDataForm", myFormResult);
		} catch (Exception e) {
			logger.error("[LMS9990M00FormHandler] getCustData  EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007"), getClass());
		} finally {
			if (custdata != null) {
				custdata.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}
		}

		return result;
	}

	/**
	 * 查詢使用者資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryCustData(PageParameters params)
			throws CapException {
		Map<String, Object> custdata = null;
		CapAjaxFormResult result = null;
		String srcMainId = params.getString("srcMainId");
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		String txCode = params.getString(EloanConstants.TRANSACTION_CODE);
		try {
			result = new CapAjaxFormResult();
			custdata = misCustdataService.findCustdataSelCname(custId, dupNo);
			if (custdata == null)
				custdata = new LinkedHashMap<String, Object>();
			L120M01A l120m01a = lms1205Service.findL120m01aByMainId(srcMainId);
			if (l120m01a == null)
				l120m01a = new L120M01A();
			result.set("srcMainId", srcMainId);
			result.set(EloanConstants.MAIN_ID,
					CapString.trimNull(l120m01a.getMainId()));
			result.set("custName", CapString.trimNull(custdata.get("CNAME")));
			result.set("custId", custId);
			result.set("dupNo", dupNo);
			result.set(
					"custData",
					custId + " " + dupNo + " "
							+ CapString.trimNull(custdata.get("CNAME")));
			result.set("caseNo", CapString.trimNull(l120m01a.getCaseNo()));
			result.set(EloanConstants.TRANSACTION_CODE,
					CapString.trimNull(txCode));
		} catch (Exception e) {
			logger.error("[LMS9990M00FormHandler] queryCustData  EXCEPTION!!",
					e);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007"), getClass());
		} finally {
			if (custdata != null) {
				custdata.clear();
			}
		}

		return result;

	}

	/**
	 * 取得亂數
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getUUID(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("uuid", IDGenerator.getUUID());
		return result;

	}

	/**
	 * 處理數值是否為空值
	 * 
	 * @param value
	 *            數值
	 * @return BigDecimal
	 */
	protected BigDecimal getDecimal(String value) {
		if (!"".equals(CapString.trimNull(value))) {
			return new BigDecimal(NumConverter.delCommaString(value));
		} else {
			return null;
		}
	}

	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			lms9990Service.deleteUploadFile(oids);
		}
		return result;
	}

	/**
	 * 刪除L999M01A
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL999M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString("oid"));
		L999M01A l999m01a = lms9990Service.findModelByOid(L999M01A.class, oid);
		if (l999m01a != null) {
			lms9990Service.delete(l999m01a);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		}
		return result;
	}
}
