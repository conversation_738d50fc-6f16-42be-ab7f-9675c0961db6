<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
	<th:block th:fragment="innerPageBody">
	
           <script>
         		loadScript('[[@{/pagejs/lrs/LMS1825M01Page.js}]]'); 
           </script> 

		<div class="button-menu funcContainer" id="buttonPanel">
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
			</th:block>
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}">離開</th:block>
			</button>
		</div>
		<div class="tit2 color-black">
			<th:block th:text="#{'doc.title'}"></th:block>
		</div>
		<form id="L182M01AForm">
		<table class="tb2" border="0" cellspacing="0" cellpadding="0" width="100%">
			<tr>
				<td class="hd1" colspan="2">
					<th:block th:text="#{'L181M01A.genDate'}">指定執行整批產生名單時間</th:block>&nbsp;&nbsp;
				</td>
				<td colspan="4" width="70%">
					<input type="text" class="date required" size="10" id="genDate1" name="genDate1" />
					<input id="meetingTime" name="meetingTime" type="text" class="required time" size="5" maxlength="5"/>
				</td>
			</tr>
			
			<tr>
				<td class="hd1" colspan="2"><th:block th:text="#{'L181M01A.baseDate'}">下次覆審日期年月</th:block>&nbsp;&nbsp;</td>
				<td colspan="4"><input class="required" type="text" size="7" maxlength="7" minlength="7" id="baseDate2" />(YYYY-MM)</td>
			</tr>
			<tr>
				<td class="hd1" colspan="2"><th:block th:text="#{'L181M01A.docStatus'}">文件狀態</th:block>&nbsp;&nbsp;</td>
				<td colspan="4"><span id="docStatus"></span></td>
			</tr>
			<tr>
				<td class="hd1" colspan="2"><th:block th:text="#{'doc.creator'}">建立人員</th:block>&nbsp;&nbsp;</td>
				<td colspan="4"><span id="creator"></span>(<span id="createTime"></span>)</td>
			</tr>
			<tr>
				<td class="hd1" colspan="2"><th:block th:text="#{'doc.lastUpdater'}">異動人員</th:block>&nbsp;&nbsp;</td>
				<td colspan="4"><span id="updater"></span>(<span id="updateTime"></span>)</td>
			</tr>
			<tr>
				<td class="hd1" rowspan="28"><th:block th:text="#{'L181M01A.branchList'}">分行名單</th:block>&nbsp;&nbsp;</td>
				<td>
					<th:block th:if="${_btnAll_visible}">
						<button type="button" id="allCheck">
							<span class="text-only"><th:block th:text="#{'chooseAll'}">選擇全部</th:block></span>
						</button>
					</th:block>
				</td>
				<td colspan="4">
				
					<th:block th:if="${_btnAllclean_visible}">
						<button type="button" id="clean">
							<span class="text-only"><th:block th:text="#{'clearAll'}">全部清空</th:block></span>
						</button>
					</th:block>
				</td>
			</tr>
			<tr>
				<td colspan="5"><div id="order"></div></td>
			</tr>
		</table>
		</form>
		<th:block th:text="#{'memo'}"></th:block>
	</th:block>
</body>
</html>
