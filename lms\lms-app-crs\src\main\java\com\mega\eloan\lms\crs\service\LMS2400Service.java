package com.mega.eloan.lms.crs.service;
import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.crs.common.CrsRuleVO;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01Z;
import com.mega.eloan.lms.model.C242M01A;

public interface LMS2400Service extends ICapService {
	
	/**
	 * 檢查是否已有相同名單
	 * 
	 * @param branch
	 * @param dateYM
	 * @return
	 */
	public Map<String, Object> checkAlreadyHave(String branch, String dateYM);
			
	/**
	 * 修改預計覆審日
	 * @param mainId
	 */
	public void c240m01a_ExpectedRetrialDate(C240M01A c240m01a, Date defaultDate) throws CapException;
	
	/**
	 * 刪除(註記)不覆審客戶
	 */ 
	public void saveNoCTL(C240M01A c240m01a, List<String> oidArr, String nCkdFlag, String nCkdDetail, List<C241M01A> skipArr_upload, List<C241M01A> skipArr_R98)throws CapException;
	
	public void saveNoCTL_O(C240M01A meta, C240M01A cmpMeta)throws CapException;
	/**
	 * 還原不覆審客戶
	 */ 
	public void saveReCTL(C240M01A c240m01a, List<String> oidArr)throws CapException;

	/**
	 * 產生 工作底稿 EXCEL
	 * 
	 * @param c240m01a
	 * @return
	 * @throws Exception 
	 */
	public boolean produce_attch(C240M01A c240m01a, String fieldId) throws Exception;
	/**
	 * 產生 Ejcic整批上傳名單
	 * 
	 * @param c240m01a
	 * @return
	 * @throws CapException 
	 */
	public boolean produceEjcicExcel(C240M01A c240m01a) throws Exception;
	
	public boolean produce_grpDetail_attch(C241M01A c241m01a, String fieldId) throws Exception;
	
	public String c241m01a_docStatusDesc(C241M01A c241m01a);
	
	public String c242m01a_docStatusDesc(C242M01A c242m01a);

	public String checkIncompleteMsg(C241M01A meta);
	
	/**
	 * 在 lms2401s02 呈現時，為呈現[覆審組編製中, 覆審組已維護覆審事項]
	 * 會去判斷資料是否輸入完整
	 * 只有要任1項不完整，即可 return
	 */
	public String checkIncompleteMsg_fast(C241M01A meta);

	public void batch_toEnd(String userId, String unitNo);
	
	public void update490_491abnormal(List<String> branchList, Date sysMonth_1st);
	
	public boolean produce(String branch, String dateYM, String userId, String unitNo, String unitType, List<String> existBrSkipList, String latest__aprdcdate) throws CapException;
	 
	public boolean produce8_1(C240M01A c240m01a)throws CapException;
	
	public boolean produceNew(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException;
	public boolean produceNewGrpDetail(C240M01A c240m01a, C241M01A c241m01a_grpMain, String custId, String dupNo, String cName)throws CapException;
	public boolean produce95_1(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException;
	public boolean produce96(C240M01A c240m01a, String custId, String dupNo, String cName, String pa_ym, String pa_trg)throws CapException;
	public boolean produce97(C240M01A c240m01a, List<Map<String, Object>> list, Map<String, C241M01A> existMap
			, String lnf660_m_contract, String lnf660_loan_class, String comId, String comDupNo, String comName)throws CapException;
	
	public boolean produce_R1R2S(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException;
	public boolean is_only_projectCreditLoan(String brNo, String custId, String dupNo);
	public boolean produce_projectCreditLoan(C240M01A c240m01a, String custId, String dupNo, String cName, boolean is_only_projectCreditLoan)throws CapException;
	public void deriveFrom99(C240M01A c240m01a, List<C241M01A> srclist, String userId)throws CapException;
	
	public void update_ptMgrId(C240M01A c240m01a, List<String> oid_list, String ptMgrId);
	
	public int update_docFmt(C240M01A c240m01a, List<String> oid_list);
	
	public List<Map<String, Object>> escrowList(String type, String lnf660_loan_class
			, String comId, String comName
			, String contract_M, String lnf034_CP_BANK_CD, String sDate);
	
	public List<Map<String, Object>> escrowList_lcNo(String lnf660_loan_class
			, String comId, String comName
			, String contract_M, String lnf034_CP_BANK_CD, String lcNo);
	
	public void daoSave(C241M01A c241m01a);
	
	public void deleteC240M01ZByPk(Date d, String brNo);
	
	public boolean isC240M01AInCompiling(String ownBrId, String branchId, Date dataEndDate);
	
	/**
	 * ref : gfnReLoadReviewDoc
	 */
	public void replaceWithBef(C241M01A meta, C241M01A befMeta);
	
	/**
	 * ref : 918SRV 的 ACLS0240
	 */
	public boolean mail_processingOverDays(int overDays, boolean skipBrT1);
	
	public boolean mail_crs_notify(String version, String data_type, String data_ym);
	
	public void deleteC241M01Z(C241M01Z o);
	public List<C241M01Z> findUnProc(int size);
	public List<C241M01Z> findProc(int size);
	public void saveUnmatchR1R2R4(ELF491 elf491, C241M01Z c241m01z, CrsRuleVO crsRuleVO);
	public void test_calc(String flag, CrsRuleVO crsRuleVO, String rule_no_new, String rule_no);

	public void upd_c241m01c_latestVer(C241M01A c241m01a);
	
	// J-108-0268 逾期情形
	public boolean chkOverDue(C241M01A meta,List<C241M01C> c241m01c_list);

	/**
	 * 產生抽樣單一授信額度在新臺幣一千萬元以下，且為十足擔保授信或經信用保證基金保證成數七成以上者
	 * @param c240m01a
	 * @param custId
	 * @param dupNo
	 * @param cName
	 * @return
	 * @throws CapException
	 */
	boolean produce_R14(C240M01A c240m01a, String custId,
			String dupNo, String cName) throws CapException;

	/**
	 * 刪除考評表資料
	 * @param c241m01a
	 */
	void deleteC241M01GSByMeta(C241M01A c241m01a);
}

