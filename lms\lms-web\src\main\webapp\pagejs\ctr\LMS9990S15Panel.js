var initDfd = initDfd || $.Deferred();
initDfd.done(function(json){
    $("#btnDelete").click(function(){
        var rows = $("#showGrid").getGridParam('selarrrow');
		var list = "";
		var sign = ",";
		for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
			if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
				var data = $("#showGrid").getRowData(rows[i]);
				list += ((list == "") ? "" : sign ) + data.oid;
			}
		}
        if (list == undefined) {
			// includeId.selData=請選擇一筆資料！！
            CommonAPI.showMessage(i18n.def["includeId.selData"]);
            return;
        }
        // confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
		if (b) {					
		        $.ajax({
		            type: "POST",
		            handler: "lms9990m01formhandler",
		            data: {
		                formAction: "deleteL999M01CList",
		                list: list
		            },
		        }).done(function(esponseData){
					$("#showGrid").trigger("reloadGrid");// 更新Grid內容
					
				});
			}				
		});		
    });
    
    var gridPrint = $("#showGrid").iGrid({
        handler: 'lms9990gridhandler',
        height: 200,
        sortname: 'addr',
        multiselect: true,
        hideMultiselect: false,
        hiddengrid: false,
        rowNum: 15,
        postData: {
            formAction: "queryL999M01CList"
        },
        colModel: [{
            colHeader: i18n.lms9990m03['L999M01AS15.grid01'],// "連保人",
            name: 'custName',
            width: 40,
            sortable: true
        }, {
            colHeader: i18n.lms9990m03['L999M01AS15.grid02'],// "地址",
            name: 'addr',
            align: "center",
            width: 120,
            sortable: true
        }, {
            colHeader: i18n.lms9990m03['L999M01AS15.grid03'],// "身分證統一編號",
            name: 'custId',
            width: 70,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }]
    });
});
