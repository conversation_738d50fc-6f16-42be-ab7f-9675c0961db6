/* 
 * LNF130.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 利率檔 **/
public class LNF130 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 分行別
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 3)
	@Column(name = "LNF130_BR_NO", length = 3, columnDefinition = "CHAR(03)",unique = true)
	private String lnf130_br_no;

	/**
	 * 客戶統編
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 11)
	@Column(name = "LNF130_CUST_ID", length = 11, columnDefinition = "CHAR(11)")
	private String lnf130_cust_id;

	/**
	 * 額度序號
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 12)
	@Column(name = "LNF130_CONTRACT", length = 12, columnDefinition = "CHAR(12)",unique = true)
	private String lnf130_contract;

	/**
	 * 放款帳號
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 14)
	@Column(name = "LNF130_LOAN_NO", length = 14, columnDefinition = "CHAR(14)",unique = true)
	private String lnf130_loan_no;

	/**
	 * 起期
	 * <p/>
	 * NOT NULL WITH DEFAULT L
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF130_BEG_TERM", columnDefinition = "DECIMAL(03,0)",unique = true)
	private Integer lnf130_beg_term;

	/**
	 * 迄期
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF130_END_TERM", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf130_end_term;

	/**
	 * 利率代碼
	 * <p/>
	 * NOT NULL
	 */
	@Size(max = 2)
	@Column(name = "LNF130_INT_CODE", length = 2, columnDefinition = "CHAR(02)  ")
	private String lnf130_int_code;

	/**
	 * 利率加減碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Digits(integer = 2, fraction = 6, groups = Check.class)
	@Column(name = "LNF130_INT_SPRD", columnDefinition = "DECIMAL(08,6)")
	private BigDecimal lnf130_int_sprd;

	/**
	 * 利率方式
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF130_INT_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf130_int_type;

	/**
	 * 利率變動方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF130_INTCHG_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf130_intchg_type;

	/**
	 * 利率變動週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF130_INTCHG_CYCL", length = 1, columnDefinition = "CHAR(01)")
	private String lnf130_intchg_cycl;

	/**
	 * 資料來源
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF130_SCTYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf130_sctype;

	/**
	 * 文件ID
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 32)
	@Column(name = "LNF130_UNDOCID", length = 32, columnDefinition = "CHAR(32)")
	private String lnf130_undocid;

	/**
	 * 產品種類
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 2)
	@Column(name = "LNF130_LOAN_CLASS", length = 2, columnDefinition = "CHAR(02)")
	private String lnf130_loan_class;

	/**
	 * 資料修改日期
	 * <p/>
	 * NOTNULL
	 */
	@Column(name = "LNF130_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp lnf130_tmestamp;

	/**
	 * 省息遞減註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF130_DEC_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf130_dec_flag;

	/**
	 * 遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 6, groups = Check.class)
	@Column(name = "LNF130_DEC_SPRD", columnDefinition = "DEC(8,6)")
	private BigDecimal lnf130_dec_sprd;

	/** PR自訂利率參考值 **/
	@Size(max = 3)
	@Column(name = "LNF130_INT_01_PTR", length = 3, columnDefinition = "CHAR(03)")
	private String lnf130_int_01_ptr;

	/**
	 * 取得分行別
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf130_br_no() {
		return this.lnf130_br_no;
	}

	/**
	 * 設定分行別
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf130_br_no(String value) {
		this.lnf130_br_no = value;
	}

	/**
	 * 取得客戶統編
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf130_cust_id() {
		return this.lnf130_cust_id;
	}

	/**
	 * 設定客戶統編
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf130_cust_id(String value) {
		this.lnf130_cust_id = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf130_contract() {
		return this.lnf130_contract;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf130_contract(String value) {
		this.lnf130_contract = value;
	}

	/**
	 * 取得放款帳號
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf130_loan_no() {
		return this.lnf130_loan_no;
	}

	/**
	 * 設定放款帳號
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf130_loan_no(String value) {
		this.lnf130_loan_no = value;
	}

	/**
	 * 取得起期
	 * <p/>
	 * NOT NULL WITH DEFAULT L
	 */
	public Integer getLnf130_beg_term() {
		return this.lnf130_beg_term;
	}

	/**
	 * 設定起期
	 * <p/>
	 * NOT NULL WITH DEFAULT L
	 **/
	public void setLnf130_beg_term(Integer value) {
		this.lnf130_beg_term = value;
	}

	/**
	 * 取得迄期
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public Integer getLnf130_end_term() {
		return this.lnf130_end_term;
	}

	/**
	 * 設定迄期
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf130_end_term(Integer value) {
		this.lnf130_end_term = value;
	}

	/**
	 * 取得利率代碼
	 * <p/>
	 * NOT NULL
	 */
	public String getLnf130_int_code() {
		return this.lnf130_int_code;
	}

	/**
	 * 設定利率代碼
	 * <p/>
	 * NOT NULL
	 **/
	public void setLnf130_int_code(String value) {
		this.lnf130_int_code = value;
	}

	/**
	 * 取得利率加減碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 */
	public BigDecimal getLnf130_int_sprd() {
		return this.lnf130_int_sprd;
	}

	/**
	 * 設定利率加減碼
	 * <p/>
	 * NOT NULL WITH DEFAULT
	 **/
	public void setLnf130_int_sprd(BigDecimal value) {
		this.lnf130_int_sprd = value;
	}

	/**
	 * 取得利率方式
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf130_int_type() {
		return this.lnf130_int_type;
	}

	/**
	 * 設定利率方式
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf130_int_type(String value) {
		this.lnf130_int_type = value;
	}

	/**
	 * 取得利率變動方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf130_intchg_type() {
		return this.lnf130_intchg_type;
	}

	/**
	 * 設定利率變動方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf130_intchg_type(String value) {
		this.lnf130_intchg_type = value;
	}

	/**
	 * 取得利率變動週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf130_intchg_cycl() {
		return this.lnf130_intchg_cycl;
	}

	/**
	 * 設定利率變動週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf130_intchg_cycl(String value) {
		this.lnf130_intchg_cycl = value;
	}

	/**
	 * 取得資料來源
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf130_sctype() {
		return this.lnf130_sctype;
	}

	/**
	 * 設定資料來源
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf130_sctype(String value) {
		this.lnf130_sctype = value;
	}

	/**
	 * 取得文件ID
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf130_undocid() {
		return this.lnf130_undocid;
	}

	/**
	 * 設定文件ID
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf130_undocid(String value) {
		this.lnf130_undocid = value;
	}

	/**
	 * 取得產品種類
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf130_loan_class() {
		return this.lnf130_loan_class;
	}

	/**
	 * 設定產品種類
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf130_loan_class(String value) {
		this.lnf130_loan_class = value;
	}

	/**
	 * 取得資料修改日期
	 * <p/>
	 * NOTNULL
	 */
	public Timestamp getLnf130_tmestamp() {
		return this.lnf130_tmestamp;
	}

	/**
	 * 設定資料修改日期
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf130_tmestamp(Timestamp value) {
		this.lnf130_tmestamp = value;
	}

	/**
	 * 取得省息遞減註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf130_dec_flag() {
		return this.lnf130_dec_flag;
	}

	/**
	 * 設定省息遞減註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf130_dec_flag(String value) {
		this.lnf130_dec_flag = value;
	}

	/**
	 * 取得遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf130_dec_sprd() {
		return this.lnf130_dec_sprd;
	}

	/**
	 * 設定遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf130_dec_sprd(BigDecimal value) {
		this.lnf130_dec_sprd = value;
	}

	/** 取得PR自訂利率參考值 **/
	public String getLnf130_int_01_ptr() {
		return this.lnf130_int_01_ptr;
	}

	/** 設定PR自訂利率參考值 **/
	public void setLnf130_int_01_ptr(String value) {
		this.lnf130_int_01_ptr = value;
	}
}
