package com.mega.eloan.lms.mfaloan.bean;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;
import org.springframework.jdbc.core.RowMapper;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.utils.CapBeanUtil;

/** 空地貸款控制檔 **/
public class ELF600 extends GenericBean {

	private static final long serialVersionUID = 1L;
	
	/** 客戶統編 **/
	@Column(name = "ELF600_CUST_ID", length = 10, columnDefinition = "CHAR(10)")
	private String elf600_cust_id;
	
	/** 重覆序號 **/
	@Column(name = "ELF600_CUST_DUP", length = 1, columnDefinition = "CHAR(1)")
	private String elf600_cust_dup;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "ELF600_CONTRACT", length = 12, columnDefinition = "CHAR(12)", nullable=false, unique = true)
	private String elf600_contract;
	
	/** 首次動用日 */
	@Column(name = "ELF600_USE_DATE", columnDefinition = "DATE")
	Date elf600_use_date;
	
	/** 使用分區類別 **/
	@Column(name = "ELF600_USECD", length = 1, columnDefinition = "CHAR(1)")
	private String elf600_usecd;
	
	/** 使用分區 **/
	@Column(name = "ELF600_USETYPE", length = 2, columnDefinition = "CHAR(2)")
	private String elf600_usetype;
	
	/** 用地類別 **/
	@Column(name = "ELF600_LANDTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String elf600_landtype;
	
	/** 土地性質 **/
	@Column(name = "ELF600_LANDKIND", length = 2, columnDefinition = "CHAR(2)")
	private String elf600_landkind;
	
	/** 聯徵B50閒置工業用地 **/
	@Column(name = "ELF600_IDLELAND", length = 1, columnDefinition = "CHAR(1)")
	private String elf600_idleland;
	
	/** 控管類別 **/
	@Column(name = "ELF600_CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf600_ctltype;
	
	/** 初次核定預計動工日  **/
	@Column(name = "ELF600_FSTDATE", columnDefinition = "DATE")
	Date elf600_fstdate;
	
	/** 最新核定(動審)預計動工日  **/
	@Column(name = "ELF600_LSTDATE", columnDefinition = "DATE")
	Date elf600_lstdate;
	
	/** 解除控管日  **/
	@Column(name = "ELF600_RRMOVEDT", columnDefinition = "DATE")
	Date elf600_rrmovedt;
	
	/** 變更預計動工日簽報書狀態 **/
	@Column(name = "ELF600_ELFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elf600_elflag;
	
	/** 變更預計動工日最新簽報日期  **/
	@Column(name = "ELF600_ENDDATE", columnDefinition = "DATE")
	Date elf600_enddate;
	
	/** 變更預計動工日簽報書編號 **/
	@Column(name = "ELF600_DOCUMENTNO", length = 20, columnDefinition = "CHAR(20)")
	private String elf600_documentno;
	
	/** 變更預計動工日  **/
	@Column(name = "ELF600_CSTDATE", columnDefinition = "DATE")
	Date elf600_cstdate;
	
	/** 採行措施 **/
	@Column(name = "ELF600_ADOPTFG", length = 30, columnDefinition = "CHAR(30)")
	private String elf600_adoptfg;
	
	/** 利率再加碼幅度 **/
	@Digits(integer = 4, fraction = 5, groups = Check.class)
	@Column(name = "ELF600_RATEADD", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal elf600_rateadd;
	
	/** 借款人ROA **/
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF600_CUSTROA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf600_custroa;
	
	/** 關係人ROA **/
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF600_RELROA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf600_relroa;
	
	/** 變更預計動工日原因 **/
	@Column(name = "ELF600_CSTREASON", length = 2, columnDefinition = "CHAR(2)")
	private String elf600_cstreason;
	
	/** 是否符合本行規定 **/
	@Column(name = "ELF600_ISLEGAL", length = 1, columnDefinition = "CHAR(1)")
	private String elf600_islegal;
	
	/** 資料修改人（行員代號） **/
	@Column(name = "ELF600_UPDATER", length = 6, columnDefinition = "VARCHAR(6)")
	private String elf600_updater;
	
	/** 資料修改日期 **/
	@Column(name = "ELF600_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf600_tmestamp;
	
	/** 最後異動文件MAINID **/
	@Column(name = "ELF600_UNIVERSAL_ID", length = 32, columnDefinition = "CHAR(32)")
	private String elf600_universal_id;
	
	/** 最後異動來源 **/
	@Column(name = "ELF600_UPDFROM", length = 3, columnDefinition = "CHAR(3)")
	private String elf600_updfrom;
	
	/** 放款餘額 **/
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF600_LOAN_BAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf600_loan_bal;
	
	/** 實際動工日  **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF600_ACT_ST_DATE", columnDefinition = "DATE")
	Date elf600_act_st_date;
	
	/** 
	 * 央行控管類別1/2/3/4<p/>
	 * from LNF087_CONTROL_CD
	 */
	@Column(name="ELF600_CONTROL_CD", length=1, columnDefinition="CHAR(1)")
	private String elf600_control_cd;
	
	
	/** 取得客戶統編  **/
	public String getElf600_cust_id() {
		return this.elf600_cust_id;
	}

	/** 設定客戶統編  **/
	public void setElf600_cust_id(String value) {
		this.elf600_cust_id = value;
	}
	
	/** 取得重覆序號  **/
	public String getElf600_cust_dup() {
		return this.elf600_cust_dup;
	}

	/** 設定重覆序號 **/
	public void setElf600_cust_dup(String value) {
		this.elf600_cust_dup = value;
	}
	
	/** 取得額度序號 **/
	public String getElf600_contract() {
		return this.elf600_contract;
	}

	/** 設定額度序號 **/
	public void setElf600_contract(String value) {
		this.elf600_contract = value;
	}
	
	/** 取得首次動用日 **/
	public Date getElf600_use_date() {
		return this.elf600_use_date;
	}

	/** 設定首次動用日 **/
	public void setElf600_use_date(Date value) {
		this.elf600_use_date = value;
	}
	
	/** 取得使用分區類別 **/
	public String getElf600_usecd() {
		return this.elf600_usecd;
	}

	/** 設定使用分區類別 **/
	public void setElf600_usecd(String value) {
		this.elf600_usecd = value;
	}
	
	/** 取得使用分區 **/
	public String getElf600_usetype() {
		return this.elf600_usetype;
	}

	/** 設定使用分區 **/
	public void setElf600_usetype(String value) {
		this.elf600_usetype = value;
	}
	
	/** 取得用地類別 **/
	public String getElf600_landtype() {
		return this.elf600_landtype;
	}

	/** 設定用地類別 **/
	public void setElf600_landtype(String value) {
		this.elf600_landtype = value;
	}

	/** 取得土地性質 **/
	public String getElf600_landkind() {
		return this.elf600_landkind;
	}

	/** 設定土地性質 **/
	public void setElf600_landkind(String value) {
		this.elf600_landkind = value;
	}

	/** 取得聯徵B50閒置工業用地 **/
	public String getElf600_idleland() {
		return this.elf600_idleland;
	}

	/** 設定聯徵B50閒置工業用地 **/
	public void setElf600_idleland(String value) {
		this.elf600_idleland = value;
	}

	/** 取得控管類別 **/
	public String getElf600_ctltype() {
		return this.elf600_ctltype;
	}

	/** 設定控管類別 **/
	public void setElf600_ctltype(String value) {
		this.elf600_ctltype = value;
	}

	/** 取得初次核定預計動工日 **/
	public Date getElf600_fstdate() {
		return this.elf600_fstdate;
	}

	/** 設定初次核定預計動工日 **/
	public void setElf600_fstdate(Date value) {
		this.elf600_fstdate = value;
	}

	/** 取得最新核定(動審)預計動工日 **/
	public Date getElf600_lstdate() {
		return this.elf600_lstdate;
	}

	/** 設定最新核定(動審)預計動工日**/
	public void setElf600_lstdate(Date value) {
		this.elf600_lstdate = value;
	}

	/** 取得解除控管日 **/
	public Date getElf600_rrmovedt() {
		return this.elf600_rrmovedt;
	}

	/** 設定解除控管日 **/
	public void setElf600_rrmovedt(Date value) {
		this.elf600_rrmovedt = value;
	}

	/** 取得變更預計動工日簽報書狀態 **/
	public String getElf600_elflag() {
		return this.elf600_elflag;
	}

	/** 設定變更預計動工日簽報書狀態 **/
	public void setElf600_elflag(String value) {
		this.elf600_elflag = value;
	}

	/** 取得變更預計動工日最新簽報日期 **/
	public Date getElf600_enddate() {
		return this.elf600_enddate;
	}

	/** 設定變更預計動工日最新簽報日期 **/
	public void setElf600_enddate(Date value) {
		this.elf600_enddate = value;
	}

	/** 取得變更預計動工日簽報書編號 **/
	public String getElf600_documentno() {
		return this.elf600_documentno;
	}

	/** 設定變更預計動工日簽報書編號 **/
	public void setElf600_documentno(String value) {
		this.elf600_documentno = value;
	}

	/** 取得變更預計動工日 **/
	public Date getElf600_cstdate() {
		return this.elf600_cstdate;
	}

	/** 設定變更預計動工日 **/
	public void setElf600_cstdate(Date value) {
		this.elf600_cstdate = value;
	}

	/** 取得採行措施 **/
	public String getElf600_adoptfg() {
		return this.elf600_adoptfg;
	}

	/** 設定採行措施 **/
	public void setElf600_adoptfg(String value) {
		this.elf600_adoptfg = value;
	}

	/** 取得利率再加碼幅度 **/
	public BigDecimal getElf600_rateadd() {
		return this.elf600_rateadd;
	}

	/** 設定利率再加碼幅度 **/
	public void setElf600_rateadd(BigDecimal value) {
		this.elf600_rateadd = value;
	}

	/** 取得借款人ROA **/
	public BigDecimal getElf600_custroa() {
		return this.elf600_custroa;
	}

	/** 設定借款人ROA **/
	public void setElf600_custroa(BigDecimal value) {
		this.elf600_custroa = value;
	}

	/** 取得關係人ROA **/
	public BigDecimal getElf600_relroa() {
		return this.elf600_relroa;
	}

	/** 設定關係人ROA **/
	public void setElf600_relroa(BigDecimal value) {
		this.elf600_relroa = value;
	}

	/** 取得變更預計動工日原因 **/
	public String getElf600_cstreason() {
		return this.elf600_cstreason;
	}

	/** 設定變更預計動工日原因 **/
	public void setElf600_cstreason(String value) {
		this.elf600_cstreason = value;
	}

	/** 取得是否符合本行規定 **/
	public String getElf600_islegal() {
		return this.elf600_islegal;
	}

	/** 設定是否符合本行規定 **/
	public void setElf600_islegal(String value) {
		this.elf600_islegal = value;
	}

	/** 取得資料修改人（行員代號） **/
	public String getElf600_updater() {
		return this.elf600_updater;
	}

	/** 設定資料修改人（行員代號） **/
	public void setElf600_updater(String value) {
		this.elf600_updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getElf600_tmestamp() {
		return this.elf600_tmestamp;
	}

	/** 設定資料修改日期 **/
	public void setElf600_tmestamp(Timestamp value) {
		this.elf600_tmestamp = value;
	}

	/** 取得最後異動文件MAINID **/
	public String getElf600_universal_id() {
		return this.elf600_universal_id;
	}

	/** 設定最後異動文件MAINID **/
	public void setElf600_universal_id(String value) {
		this.elf600_universal_id = value;
	}

	/** 取得最後異動來源 **/
	public String getElf600_updfrom() {
		return this.elf600_updfrom;
	}

	/** 設定最後異動來源 **/
	public void setElf600_updfrom(String value) {
		this.elf600_updfrom = value;
	}
	
	/** 取得放款餘額 **/
	public BigDecimal getElf600_loan_bal() {
		return this.elf600_loan_bal;
	}

	/** 設定放款餘額 **/
	public void setElf600_loan_bal(BigDecimal value) {
		this.elf600_loan_bal = value;
	}

	public class ELF600RM implements RowMapper<ELF600> {
		public ELF600 mapRow(ResultSet rs, int rowNum) throws SQLException {

			Field[] cols = CapBeanUtil.getField(ELF600.class, false);
			ELF600 elf600 = new ELF600();

			for (Field field : cols) {
				try {
					elf600.set(field.getName(), rs.getObject(field.getName()));
				} catch (CapException e) {
					e.printStackTrace();
				}
			}

			return elf600;
		}
	}

	public Date getElf600_act_st_date() {
		return elf600_act_st_date;
	}

	public void setElf600_act_st_date(Date elf600_act_st_date) {
		this.elf600_act_st_date = elf600_act_st_date;
	}

	public String getElf600_control_cd() {
		return elf600_control_cd;
	}

	public void setElf600_control_cd(String elf600_control_cd) {
		this.elf600_control_cd = elf600_control_cd;
	}

}
