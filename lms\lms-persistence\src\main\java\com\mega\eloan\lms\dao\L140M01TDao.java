package com.mega.eloan.lms.dao;

import java.util.LinkedHashMap;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01T;


public interface L140M01TDao extends IGenericDao<L140M01T> {

	List<L140M01T> findByMainId(String mainId);

	L140M01T findByOid(String oid);

	List<L140M01T> findCurrentByMainId(String mainId);

	List<L140M01T> findLastByMainId(String mainId);

	L140M01T findByMainIdEstateType(String mainId, String estateType);

	L140M01T findByMainIdFlagEstateTypeEstateSubType(String mainId, String flag, String estateType, String estateSubType);

	List<L140M01T> findByMainId_orderBy(String mainId, LinkedHashMap<String, Boolean> orderByMap);
}