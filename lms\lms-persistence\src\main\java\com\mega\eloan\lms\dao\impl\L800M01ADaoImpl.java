/* 
 * L800M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L800M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L800M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/** 常用主管資料檔 **/
@Repository
public class L800M01ADaoImpl extends LMSJpaDao<L800M01A, String>
	implements L800M01ADao {

	@Override
	public L800M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L800M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L800M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L800M01A findByUniqueKey(String brno, String zhuGuan){
		ISearch search = createSearchTemplete();
		if (brno != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "brno", brno);
		if (zhuGuan != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "zhuGuan", zhuGuan);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L800M01A> findByIndex01(String brno, String zhuGuan){
		ISearch search = createSearchTemplete();
		List<L800M01A> list = null;
		if (brno != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "brno", brno);
		if (zhuGuan != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "zhuGuan", zhuGuan);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L800M01A> findByBrno() {
		ISearch search = createSearchTemplete();
		MegaSSOUserDetails userId = MegaSSOSecurityContext.getUserDetails();
		search.addSearchModeParameters(SearchMode.EQUALS, "brno", userId.getUnitNo());
		return find(search);
	}
}