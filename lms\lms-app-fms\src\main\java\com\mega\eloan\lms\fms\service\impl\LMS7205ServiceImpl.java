/* 
 * LMS1205ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dao.L720M01ADao;
import com.mega.eloan.lms.fms.service.LMS7205Service;
import com.mega.eloan.lms.model.L720M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 使用者自定表格範本Service Impl
 * <AUTHOR> Lin
 * </pre>
 */
@Service
public class LMS7205ServiceImpl extends AbstractCapService implements
		LMS7205Service {
	@Resource
	L720M01ADao l720m01adao;

	@Override
	public L720M01A findL720m01aByOid(String oid) {
		return l720m01adao.findByOid(oid);
	}

	@Override
	public void deleteListL720m01a(String[] oidArray) {
		// 刪除多筆資料
		for (int i = 0; i < oidArray.length; i++) {
			L720M01A model = findL720m01aByOid(oidArray[i]);
			l720m01adao.delete(model);
		}
	}

	@Override
	public L720M01A findL720m01aByUniqueKey(String patternNm) {
		return l720m01adao.findByUniqueKey(patternNm);
	}

	@Override
	public List<L720M01A> findL720m01aList(ISearch search) {
		return l720m01adao.find(search);
	}

	@Override
	public List<L720M01A> findL720m01aList() {
		ISearch search = l720m01adao.createSearchTemplete();
		return l720m01adao.find(search);
	}
	
	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L720M01A) {
					((L720M01A) model).setUpdater(user.getUserId());
					((L720M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l720m01adao.save((L720M01A) model);
					// 記錄文件異動記錄
					// docLogService.record(model.getOid(),DocLogEnum.SAVE);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		// 進行無限多筆刪除
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L720M01A) {
					l720m01adao.delete((L720M01A) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L720M01A.class) {
			return l720m01adao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}
}
