package com.mega.eloan.lms.dc.thread;

import com.mega.eloan.lms.dc.action.CopyAllTextFile;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;

/**
 * <pre>
 * CopyTextThread
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>2013/01/29 Modify By Bang:加入個金判斷 (schema)
 *          </ul>
 */
public class CopyTextThread extends Thread {

	private String schema = "";
	private ConfigData config = null;

	public CopyTextThread() {
		super();
	}

	/**
	 * Constructor
	 * 
	 * @param schema
	 *            String:目前執行的系統名稱
	 * @param pps
	 *            :Properties
	 */
	public CopyTextThread(String schema) {
		this.schema = schema;
	}

	public void run() {
		CopyAllTextFile catf = new CopyAllTextFile();
		catf.setConfigData(config);
		catf.doCopyAllText(this.schema);
	}

	public void copyAllText(ConfigData config) {
		try {
			this.setConfig(config);
			this.run();
		} catch (DCException e) {
			throw new DCException("CopyAllTextFile 時產生錯誤...", e);
		}
	}

	/**
	 * set the config
	 * 
	 * @param config
	 *            the config to set
	 */
	public void setConfig(ConfigData config) {
		if (config != null) {
			this.config = config;
		}
	}

}
