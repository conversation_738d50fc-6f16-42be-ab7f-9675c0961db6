$(document).ready(function(){	

  //J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
  //因為要改用LOAD，所有JS移到 LMS1201S05Page.js 
	$("#partyPrincipal").change(function(){
        var value = $(this).val();
        if (value) {
            if($("#s14cForm").find("#frontMan").val() == ""){
                $("#s14cForm").find("#frontMan").val(value);
            }
            if($("#s14cForm").find("#principal_2").val() == ""){
                $("#s14cForm").find("#principal_2").val(value);
            }
        }
    });
    $("#partyAddr").change(function(){
        var value = $(this).val();
        if (value) {
            if($("#s14bForm").find("#drawerAddr_1").val() == ""){
                $("#s14bForm").find("#drawerAddr_1").val(value);
            }
            if($("#s14cForm").find("#principalAddr_1").val() == ""){
                $("#s14cForm").find("#principalAddr_1").val(value);
            }
        }
    });
    $("input[name='isSole']").change(function(){
        var isSole = $("input[name='isSole']:radio:checked" ).val();
        if(isSole =="Y"){
            $(".showSole").show();
        }else{
            $(".showSole").hide().find(":radio").removeAttr("checked");
        }
    });

    var BorrowersGrid = $('#BorrowersGrid').iGrid({
        localFirst: true,
        handler: 'lms1201gridhandler',
        height: 200, // 設定高度
        action: 'BorrowersQuery', // 執行的Method
        rowNum: 15,
        rownumbers: true,
        postData: {
            mainid: responseJSON.mainId,
            chairman : 'N'
        },
        colModel: [{
            colHeader: "統一編號",
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'borrowId' // col.id
        }, {
            colHeader: "重覆序號",
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'borrowDupNo' // col.id
        }, {
            colHeader: "客戶名稱",
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'borrowName' // col.id
        }, {
            colHeader: "type",      // 借款人M or 負責人3
            name: 'type',
            hidden: true
        }]
    });

    var AccountGrid = $("#AccountGrid").iGrid({
        localFirst: true,
        handler: "cls1161gridhandler", // 設定handler
        height: 200, // 設定高度
        action: 'AccountQuery', // 執行的Method
        rowNum: 15,
        rownumbers: true,
        colModel: [{
            colHeader: "帳號", // 帳號
            align: "center",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'Account'
        }]
    });

    var gridPrintContract = $("#printContractGrid").iGrid({
        needPager: false,
        localFirst: true,
        handler: "lms1201gridhandler",
        height: 100,
        rownumbers:true,
        multiselect: true,
        hideMultiselect:false,
        caption: "&nbsp;",
        hiddengrid : false,
        needPager: false,
        sortname: "printItem|printCntrno",
        sortorder: 'asc|asc',
        postData: {
            formAction: "queryContractPrintList_lnType61",
            mainid: responseJSON.mainId
        },
        colModel: [ {
            colHeader: "檔案名稱",
            name: 'docName',
            align: "left",
            width: 50,
            sortable: false
        }, {
            colHeader : "額度序號",
            name : 'printCntrno',
            align: "center",
            width: 50,
            sortable: false
        }, {
            colHeader : "printItem",
            name : 'printItem',
            hidden : true
        }, {
            colHeader : "printOid",
            name : 'printOid',
            hidden : true
        }]
    });
    
    

    $("#acctNoPullin").click(function(){
        $('#BorrowersGrid').trigger("reloadGrid");

        $("#BorrowersThickBox").thickbox({
            title: "請選擇存款人",
            width: 450,
            height: 350,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var data = BorrowersGrid.getSingleData();
                    if (data) {
                        AccountGrid.jqGrid("setGridParam", {
                            postData: {
                                custId: data.borrowId,
                                dupNo: data.borrowDupNo,
                                chairman : 'N'
                            },
                            search: true
                        }).trigger("reloadGrid");

                        $("#AccountThickBox").thickbox({
                            title: "請選擇帳號",
                            width: 450,
                            height: 350,
                            align: 'center',
                            valign: 'bottom',
                            buttons: {
                                'sure': function(){
                                    var dataAcc = AccountGrid.getSingleData();
                                    if (dataAcc) {
                                        $.ajax({
                                            type: "POST",
                                            handler: "lms1201formhandler",
                                            data: {
                                                formAction: "getDepositTypeName",
                                                acctNo: dataAcc.Account,
                                                custId: data.borrowId,
                                                dupNo: data.borrowDupNo
                                            },
                                            success: function(json){
                                                $("#s14dForm").find("#depositType").val(json.depositType);
                                                $("#s14dForm").find("#depositAcct").val(json.depositAcct);
                                                $("#s14dForm").find("#depositor").val(data.borrowName);
                                                $("#s14dForm").find("#depositorId").val(data.borrowId);
                                                $("#s14dForm").find("#depositorAddr").val(json.depositorAddr);
                                                if(data.type == "3"){
                                                    // 當存款帳號選擇為負責人的帳號時，負責人資訊清空
                                                    $("#s14dForm").find("#director").val("");
                                                    $("#s14dForm").find("#directorId").val("");
                                                }
                                                $.thickbox.close();
                                                $.thickbox.close();
                                            }
                                        });
                                    }
                                },
                                'cancel': function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
    });

    // 預設開啟 第一個頁面
    $("#formTab").tabs({
        selected: 0,
        select: function(event, ui){
            // 從 目前頁面 pageIndex 跳到 目的頁面 ui.index
            var pageIndex = $(this).find("li.ui-tabs-selected").index();
            var formId = DOMPurify.sanitize($(DOMPurify.sanitize($("#formTab").find("li.ui-tabs-selected").find("a").attr("href"))).find("form").attr("id"));
//            ilog.debug(formId);
            if (!$("#" + DOMPurify.sanitize(formId)).valid()) {
                //common.001=欄位檢核未完成，請填妥後再送出
                API.showMessage(i18n.def["common.001"]);
                event.preventDefault();
                return false;
            }

//            switch (pageIndex) {
//                case 0:
//                    if (!$("#contractForm").valid()) {
//                        //common.001=欄位檢核未完成，請填妥後再送出
//                        API.showMessage(i18n.def["common.001"]);
//                        event.preventDefault();
//                        return false;
//                    }
//                    break;
//                case 1:
//                    if (!$("#s14bForm").valid()) {
//                        //common.001=欄位檢核未完成，請填妥後再送出
//                        API.showMessage(i18n.def["common.001"]);
//                        event.preventDefault();
//                        return false;
//                    }
//                    break;
//                case 2:
//                    if (!$("#s14cForm").valid()) {
//                        //common.001=欄位檢核未完成，請填妥後再送出
//                        API.showMessage(i18n.def["common.001"]);
//                        event.preventDefault();
//                        return false;
//                    }
//                    break;
//                case 3:
//                    if (!$("#s14dForm").valid()) {
//                        //common.001=欄位檢核未完成，請填妥後再送出
//                        API.showMessage(i18n.def["common.001"]);
//                        event.preventDefault();
//                        return false;
//                    }
//                    break;
//            }
            if(pageIndex == 0){
                // 跳離合約書時檢查
                var hasRegis = $("input[name='hasRegis']:radio:checked").val();
                if(hasRegis == "Y" && $("#contractForm").find("#fundingWay").val() == "2"){
                    CommonAPI.showErrorMessage("撥款方式：第"+$("#contractForm").find("#fundingWay").val()+"款限乙方無辦理商業登記");
                    return false;
                }
            }
            if(ui.index == 1 || ui.index == 2){
                // 1-本票     2-本票授權書
                // soleType     01-獨資    02-合夥
                var soleType = $("input[name='soleType']:radio:checked").val();
                var partyPrincipal = $("#contractForm").find("#partyPrincipal").val();    // 乙方負責人
                var party = $("#contractForm").find("#party").val();    // 乙方
                var partyAgent = $("#contractForm").find("#partyAgent").val();    // 乙方代表人
                if(soleType == "01"){
                    // 本票第一位發票人
                    if($("#s14bForm").find("#drawer_1").val() == ""){
                        $("#s14bForm").find("#drawer_1").val(partyPrincipal+"即"+party);
                    }
                    // 本票授權書第一位立授權書人
                    if($("#s14cForm").find("#principal_1").val() == ""){
                        $("#s14cForm").find("#principal_1").val(partyPrincipal+"即"+party);
                    }
                } else if(soleType == "02"){
                    // 本票第一位發票人
                    if($("#s14bForm").find("#drawer_1").val() == ""){
                        $("#s14bForm").find("#drawer_1").val(party+"，代表人："+partyAgent);
                    }
                    // 本票授權書第一位立授權書人
                    if($("#s14cForm").find("#principal_1").val() == ""){
                        $("#s14cForm").find("#principal_1").val(party+"，代表人："+partyAgent);
                    }
                }
            }
            return true;
        }
    });
	
	 
	
	
	
});

