/* 
 * C100M01Dao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C100M01;

/** 擔保品主檔 **/
public interface C100M01Dao extends IGenericDao<C100M01> {

	C100M01 findByOid(String oid);

	List<C100M01> findByOids(String[] oids);

	C100M01 findByMainId(String mainId);

	List<C100M01> findByDocStatus(String docStatus);

	List<C100M01> findByIndex01(String mainId);

	List<C100M01> findByCustId(String custId);

	List<C100M01> findByCustName(String custName);

	List<C100M01> findByIndex02(String Branch, String custid, String collno);

	List<C100M01> findByIndex03(String custid, String dupno, String collno);

	List<C100M01> findByIndex04(String Branch, String pndFlag, String docStatus);

	Long findC100m01Count();

	Long findC100m01Count2();

	List<C100M01> findByDocStatusAndBranch(String docStatus, String branch);
}