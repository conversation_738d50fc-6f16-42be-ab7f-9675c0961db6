/* 
 * L782M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.Meta;

/** 額度特殊案件登記表 **/
@Entity
@Table(name = "L782M01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "loanTP" }))
public class L782M01A extends Meta implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L782A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l782m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L782A01A> l782a01a;

	public Set<L782A01A> getL782a01a() {
		return l782a01a;
	}

	public void setL782a01a(Set<L782A01A> l782a01a) {
		this.l782a01a = l782a01a;
	}

	/**
	 * 科(子)目-簡碼
	 * <p/>
	 * 100/09/06調整<br/>
	 * 配合Notes舊案科目有些為4碼<br/>
	 * 100/12/07調整<br/>
	 * 配合codeType改為VARCHAR
	 */
	@Column(name = "LOANTP", length = 4, columnDefinition = "VARCHAR(04)")
	private String loanTP;

	/** 科(子)目 **/
	@Column(name = "SUBJECT", length = 60, columnDefinition = "VARCHAR(60)")
	private String subject;

	/** 發文日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DISPATCHDATE", columnDefinition = "DATE")
	private Date dispatchDate;

	/** 額度(幣別) **/
	@Column(name = "APPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String applyCurr;

	/** 額度(金額) **/
	@Column(name = "APPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal applyAmt;

	/**
	 * 歸類
	 * <p/>
	 * 1短期放款<br/>
	 * 2中長期放款<br/>
	 * 3團體消貸<br/>
	 * 4房貸<br/>
	 * 5行家理財<br/>
	 * 6建築融資<br/>
	 * 7購置企業建築物貸款<br/>
	 * 8政策性優惠貸款<br/>
	 * 9公司債保證<br/>
	 * 10存單質借<br/>
	 * 11NIF融資<br/>
	 * 12對投資公司融資<br/>
	 * 13展期還款<br/>
	 * 14低利融資方案<br/>
	 * 15船舶貸款<br/>
	 * 16逾放和解案<br/>
	 * 17本票、保人、押品、動用期限、清償期限等<br/>
	 * 18對證証券商融資<br/>
	 * 19不宜再增額敘作之集團
	 */
	@Column(name = "CASETYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String caseType;

	/**
	 * 利費率/其他
	 * <p/>
	 * 64個全型字<br/>
	 * 102.02.18 欄位擴大 192 -> 900
	 */
	@Column(name = "INTERATE", length = 900, columnDefinition = "VARCHAR(900)")
	private String inteRate;

	/**
	 * 備註說明
	 * <p/>
	 * 64個全型字<br/>
	 * 102.02.18 欄位擴大 192 -> 900
	 */
	@Column(name = "DISP1", length = 900, columnDefinition = "VARCHAR(900)")
	private String disp1;
	
	
	/** 案件單位代號 */
	@Column(name = "CASEBRID",length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 取得科(子)目-簡碼
	 * <p/>
	 * 100/09/06調整<br/>
	 * 配合Notes舊案科目有些為4碼<br/>
	 * 100/12/07調整<br/>
	 * 配合codeType改為VARCHAR
	 */
	public String getLoanTP() {
		return this.loanTP;
	}

	/**
	 * 設定科(子)目-簡碼
	 * <p/>
	 * 100/09/06調整<br/>
	 * 配合Notes舊案科目有些為4碼<br/>
	 * 100/12/07調整<br/>
	 * 配合codeType改為VARCHAR
	 **/
	public void setLoanTP(String value) {
		this.loanTP = value;
	}

	/** 取得科(子)目 **/
	public String getSubject() {
		return this.subject;
	}

	/** 設定科(子)目 **/
	public void setSubject(String value) {
		this.subject = value;
	}

	/** 取得發文日期 **/
	public Date getDispatchDate() {
		return this.dispatchDate;
	}

	/** 設定發文日期 **/
	public void setDispatchDate(Date value) {
		this.dispatchDate = value;
	}

	/** 取得額度(幣別) **/
	public String getApplyCurr() {
		return this.applyCurr;
	}

	/** 設定額度(幣別) **/
	public void setApplyCurr(String value) {
		this.applyCurr = value;
	}

	/** 取得額度(金額) **/
	public BigDecimal getApplyAmt() {
		return this.applyAmt;
	}

	/** 設定額度(金額) **/
	public void setApplyAmt(BigDecimal value) {
		this.applyAmt = value;
	}

	/**
	 * 取得歸類
	 * <p/>
	 * 1短期放款<br/>
	 * 2中長期放款<br/>
	 * 3團體消貸<br/>
	 * 4房貸<br/>
	 * 5行家理財<br/>
	 * 6建築融資<br/>
	 * 7購置企業建築物貸款<br/>
	 * 8政策性優惠貸款<br/>
	 * 9公司債保證<br/>
	 * 10存單質借<br/>
	 * 11NIF融資<br/>
	 * 12對投資公司融資<br/>
	 * 13展期還款<br/>
	 * 14低利融資方案<br/>
	 * 15船舶貸款<br/>
	 * 16逾放和解案<br/>
	 * 17本票、保人、押品、動用期限、清償期限等<br/>
	 * 18對證証券商融資<br/>
	 * 19不宜再增額敘作之集團
	 */
	public String getCaseType() {
		return this.caseType;
	}

	/**
	 * 設定歸類
	 * <p/>
	 * 1短期放款<br/>
	 * 2中長期放款<br/>
	 * 3團體消貸<br/>
	 * 4房貸<br/>
	 * 5行家理財<br/>
	 * 6建築融資<br/>
	 * 7購置企業建築物貸款<br/>
	 * 8政策性優惠貸款<br/>
	 * 9公司債保證<br/>
	 * 10存單質借<br/>
	 * 11NIF融資<br/>
	 * 12對投資公司融資<br/>
	 * 13展期還款<br/>
	 * 14低利融資方案<br/>
	 * 15船舶貸款<br/>
	 * 16逾放和解案<br/>
	 * 17本票、保人、押品、動用期限、清償期限等<br/>
	 * 18對證証券商融資<br/>
	 * 19不宜再增額敘作之集團
	 **/
	public void setCaseType(String value) {
		this.caseType = value;
	}

	/**
	 * 取得利費率/其他
	 * <p/>
	 * 64個全型字
	 */
	public String getInteRate() {
		return this.inteRate;
	}

	/**
	 * 設定利費率/其他
	 * <p/>
	 * 64個全型字
	 **/
	public void setInteRate(String value) {
		this.inteRate = value;
	}

	/**
	 * 取得備註說明
	 * <p/>
	 * 64個全型字
	 */
	public String getDisp1() {
		return this.disp1;
	}

	/**
	 * 設定備註說明
	 * <p/>
	 * 64個全型字
	 **/
	public void setDisp1(String value) {
		this.disp1 = value;
	}
	
	/**
	 * 取得案件單位代號
	 */
	public void setCaseBrId(String caseBrId) {
		this.caseBrId = caseBrId;
	}

	/**
	 * 設定案件單位代號
	 **/
	public String getCaseBrId() {
		return caseBrId;
	}
}
