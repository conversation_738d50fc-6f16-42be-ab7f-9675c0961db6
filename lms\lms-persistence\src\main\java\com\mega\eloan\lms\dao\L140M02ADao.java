/* 
 * L140M02ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M02A;

/** 額度批覆表修改註記檔 **/
public interface L140M02ADao extends IGenericDao<L140M02A> {

	L140M02A findByOid(String oid);
	
	List<L140M02A> findByMainId(String mainId);
	
	L140M02A findByUniqueKey(String mainId);

	List<L140M02A> findByIndex01(String mainId);
}