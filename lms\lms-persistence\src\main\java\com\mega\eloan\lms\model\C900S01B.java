/* 
 * C900S01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 檢附資訊檔(產出至lms.c160m01c) **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C900S01B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C900S01B extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 檢附資訊編號 **/
	@Size(max = 10)
	@Column(name = "ITEMCODE", length = 10, columnDefinition = "VARCHAR(10)")
	private String itemCode;

	/** 顯示順序 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "ITEMSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer itemSeq;

	/**
	 * 查核項目類別
	 * <p/>
	 * 1共用項目<br/>
	 * 2依產品自訂<br/>
	 * 3自行輸入項目<br/>
	 * D已刪除項目
	 */
	@Size(max = 1)
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/**
	 * 訊息內容(同c160m01c)
	 */
	@Size(max = 420)
	@Column(name = "ITEMCONTENT", length = 420, columnDefinition = "VARCHAR(420)")
	private String itemContent;

	/**
	 * 欄位顯示格式
	 * <p/>
	 * JSON格式<br/>
	 * [<br/>
	 * {<br/>
	 * titleName,(格式如:式、份)<br/>
	 * value,(數量)<br/>
	 * },<br/>
	 * {<br/>
	 * titleName,(格式如:式、份)<br/>
	 * value,(數量)<br/>
	 * },<br/>
	 * ]<br/>
	 * 當此欄位為空表示無，可填欄位內容。
	 */
	@Size(max = 300)
	@Column(name = "ITEMFORMAT", length = 300, columnDefinition = "VARCHAR(300)")
	private String itemFormat;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得檢附資訊編號 **/
	public String getItemCode() {
		return this.itemCode;
	}

	/** 設定檢附資訊編號 **/
	public void setItemCode(String value) {
		this.itemCode = value;
	}

	/** 取得顯示順序 **/
	public Integer getItemSeq() {
		return this.itemSeq;
	}

	/** 設定顯示順序 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/**
	 * 查核項目類別
	 * <p/>
	 * 1共用項目<br/>
	 * 2依產品自訂<br/>
	 * 3自行輸入項目
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 查核項目類別
	 * <p/>
	 * 1共用項目<br/>
	 * 2依產品自訂<br/>
	 * 3自行輸入項目
	 */
	public void setItemType(String value) {
		this.itemType = value;
	}

	/**
	 * 取得訊息內容(同c160m01c)
	 */
	public String getItemContent() {
		return this.itemContent;
	}

	/**
	 * 設定訊息內容(同c160m01c)
	 **/
	public void setItemContent(String value) {
		this.itemContent = value;
	}

	/**
	 * 取得欄位顯示格式
	 * <p/>
	 * JSON格式<br/>
	 * [<br/>
	 * {<br/>
	 * titleName,(格式如:式、份)<br/>
	 * value,(數量)<br/>
	 * },<br/>
	 * {<br/>
	 * titleName,(格式如:式、份)<br/>
	 * value,(數量)<br/>
	 * },<br/>
	 * ]<br/>
	 * 當此欄位為空表示無，可填欄位內容。
	 */
	public String getItemFormat() {
		return this.itemFormat;
	}

	/**
	 * 設定欄位顯示格式
	 * <p/>
	 * JSON格式<br/>
	 * [<br/>
	 * {<br/>
	 * titleName,(格式如:式、份)<br/>
	 * value,(數量)<br/>
	 * },<br/>
	 * {<br/>
	 * titleName,(格式如:式、份)<br/>
	 * value,(數量)<br/>
	 * },<br/>
	 * ]<br/>
	 * 當此欄位為空表示無，可填欄位內容。
	 **/
	public void setItemFormat(String value) {
		this.itemFormat = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
