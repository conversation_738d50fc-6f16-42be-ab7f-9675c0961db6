$(document).ready(function() {
	setCloseConfirm(true);
	gridOther();

});

function cesGridOther(){
	   $("#gridOther").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIds2",
			rowNum:10
		},
		search: true
	   }).trigger("reloadGrid");
	}

function gridOther(){
    var gridbox1 = $("#gridOther").iGrid({
		handler : 'lms1205gridhandler',
		height : 175,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIds2",
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		rowNum:10,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lms1205s06["L120M01D.grid2"], //建立日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'createTime' //col.id
		}, {
			colHeader : i18n.lms1205s06["L120M01D.grid4"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		}, {
			colHeader : i18n.lms1205s06["L120M01D.grid3"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		}, {
			colHeader : i18n.lms1205s06["L120M01D.grid1"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}

function openOther(){
	cesGridOther();
	$("#openOther").thickbox({     // 使用選取的內容進行彈窗
		title : i18n.lms1205s06["L120M01D.thickbox1"],
		width : 640,
		height : 350,
		align : 'center',
		valign: 'bottom',
		modal : true,
		i18n:i18n.def,
		buttons: {
			"sure": function() {
				var row = $("#gridOther").getGridParam('selrow'); 
				var list = "";
				var data = $("#gridOther").getRowData(row);
				list = data.mainId;
				list = (list == undefined ? "" : list);
				if(list != ""){
					$.ajax({ // 查詢主要借款人資料
						handler : "lms1205formhandler",
						type : "POST",
						dataType : "json",
						data : {
							formAction : "findOther",
							mainId : responseJSON.mainId,
							cesMainId : list,
							custName : data.custName
						},
						success : function(json) {
							// alert(JSON.stringify(json));
							setCkeditor2("itemDscr03",json.itemDscr03);
							//$("#itemDscr03").val(json.itemDscr03);
						}
					});
				}
				$.thickbox.close();
			},            
			"close": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

function keyinHKMAText(){	
	$("#L120M01D_hkmadiv").thickbox({
		title : '',
		width : 800,
		height : 450,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.def,
		buttons : {
			 "sure": function() {				                    		
				 $.thickbox.close();
             },				                     
             "cancel": function() {
             	
             	$.ajax({		
             		handler : 'lms1305formhandler',
             		type : "POST",
             		dataType : "json",
             		action : "getHKMAText",
             		data : { 
             			mainId : responseJSON.mainid
             		},
             		success : function(json) {
             			$F = $("#L120M01D_hkmaForm");
 						$F.setData(json.L120M01D_hkmaForm);
 						$.thickbox.close();
             		}
             	});
             	
              }
		}
	});
}

function importHKMAText(){
	$F = $("#L120M01D_hkmaForm");
	
	$.ajax({		
		handler : 'lms1305formhandler',
		type : "POST",
		dataType : "json",
		action : "importHKMAText",
		data : { 
			"L120M01D_hkmaForm": JSON.stringify($F.serializeData()) 
		},
		success : function(json) {
			var newstr = [];
			newstr.push("<p><table border='1' cellpadding='0' cellspacing='0'>");
			for(var i=1;i<=7;i++){
				newstr.push("<tr>");
				{
					newstr.push("<td style='vertical-align:top;width: 165px'>");
					newstr.push("<p style='margin:0px;padding:0px;font-family: 新細明體;font-size: 16px;'>");
					newstr.push($F.find("#label_hkmaText"+i).html());
					newstr.push("</p>");
					newstr.push("</td>");
				}
				{
					newstr.push("<td style='width: 430px'>");
					newstr.push("<p style='margin:0px;padding:0px;font-family: 新細明體;font-size: 16px;'>");
					newstr.push(json["hkmaText"+i]);
					newstr.push("</p>");
					newstr.push("</td>");
				}	
				newstr.push("</tr>");
			}
			newstr.push("</table></p>");
			setCkeditor2("itemDscr03",$("#itemDscr03").val()+newstr.join(""));
		}
	});
	

}


function applyEquatorPrinciples(){

    var $LMS1205S07Form05 = $("#LMS1205S07Form05");
	
    $.ajax({ // 查詢主要借款人資料
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "applyEquatorPrinciples",
			mainId : responseJSON.mainId,
			LMS1205S07Form05: JSON.stringify($LMS1205S07Form05.serializeData())
		},
		success : function(json) {
			var newstr = json.equatorPrinciples;
			setCkeditor2("itemDscr03",newstr+$("#itemDscr03").val());
		}
	});
}
	
