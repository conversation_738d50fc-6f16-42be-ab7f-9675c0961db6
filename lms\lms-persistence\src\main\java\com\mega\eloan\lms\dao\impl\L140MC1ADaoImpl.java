/* 
 * L140MC1ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140MC1ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140MC1A;

/** 疑似人頭戶檢核明細紀錄檔 **/
@Repository
public class L140MC1ADaoImpl extends LMSJpaDao<L140MC1A, String>
	implements L140MC1ADao {

	@Override
	public L140MC1A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140MC1A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140MC1A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140MC1A findByUniqueKey(String mainId, String itemCode){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemCode", itemCode);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}
	
	@Override
	public void saveAll(List<L140MC1A> list){
		for(L140MC1A entity : list){
			this.save(entity);
		}
	}
	
	@Override
	public void deleteAll(List<L140MC1A> list){
		for(L140MC1A entity : list){
			this.delete(entity);
		}
	}
}