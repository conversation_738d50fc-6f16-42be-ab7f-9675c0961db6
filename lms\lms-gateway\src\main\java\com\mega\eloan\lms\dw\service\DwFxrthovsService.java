/* 
 * DwFxrthovsService.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2011 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dw.service;

import java.math.BigDecimal;
import java.util.List;

import com.mega.sso.model.IBranch;

/**
 * <pre>
 * DwdbBASEService
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,UFO,new
 *          </ul>
 */

public interface DwFxrthovsService {

	/**
	 * 找海外的利率
	 * 
	 * @param brNo
	 *            分行代號
	 * @return CURR 幣別 RATE 匯率
	 */
	public List<?> findDW_DWFXRTHOVS_RATE(String brNo);
	
	/**
	 * 取得最新結帳匯率
	 * @param brNo 分行代號
	 * @return AGNT_TWD_RT 本位幣對臺幣
	 */
	public List<?> findDW_DWFXRTHOVS_RATE2(String brNo);

	/**
	 * @param brNo
	 *            分行代號
	 * @return CURR 該分行主要計價幣別
	 */
	public List<?> findDW_DWFXRTHOVS_MainCurr(String brNo);
	
	List<?> findDW_DWFXRTHOVSSelAll(String branch);
	
	List<?> findDW_DWFXRTHOVSSelAllMaxDate(String branch);
	
	BigDecimal getCur1ToCur2Rate(String date, IBranch branch,
			String sourceCurr, String destCur);

}
