/* 
 * L141M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L141M01B;

/** 聯行額度明細關聯檔 **/
public interface L141M01BDao extends IGenericDao<L141M01B> {

	L141M01B findByOid(String oid);

	List<L141M01B> findByMainId(String mainId);

	L141M01B findByUniqueKey(String mainId, String itemType, String refMainId);

	List<L141M01B> findByIndex01(String mainId, String itemType,
			String refMainId);

	public List<L141M01B> findByRefMainId(String refMainId);

}