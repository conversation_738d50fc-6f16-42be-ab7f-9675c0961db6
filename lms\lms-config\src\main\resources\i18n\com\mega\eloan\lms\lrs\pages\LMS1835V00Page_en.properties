#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae-\u5916Grid
#==================================================
L1835M01a.dataDate=Date of date
L1835M01a.branch=Branch name
L1835M01a.reportDate=Report Generation Date

#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae--thickbox
#==================================================
L1835M01a.title1=Generate New Corporate Account/Limit Increase List
L1835M01a.title2=New Corporate Account/Limit Increase List Query
L1835M01a.date=Please input the year & month of scheduled credit review
L1835M01a.brNo=Please select the branch ID for list  generation
L1835M01a.dataDateForFilter=Data Date
L1835M01a.dataDateForFilter2=Date Range For Data
L1835M01a.searchNew=Latest Data
L1835M01a.searchOld=Historical Data
L1835v00.startDate=Start Date
L1835v00.endDate=End Date
#==================================================
# \u7522\u751f\u4f01\u91d1\u6236\u65b0\u589e/\u589e\u984d\u540d\u55ae--ERROR
#==================================================
L1835v00.error=The start date cannot be later than the end date
