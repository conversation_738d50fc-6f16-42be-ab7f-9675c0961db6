---------------------------------------------------------
-- LMS.L130M01B 異常通報表事項檔
---------------------------------------------------------
--DROP TABLE LMS.L130M01B;
CREATE TABLE LMS.L130M01B (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	BRANCHKIND    CHAR(1)       not null,
	SEQ<PERSON><PERSON>       VARCHAR(3072),
	CREATOR       CHAR(6)      ,
	CREATETI<PERSON>    TIMESTAMP    ,
	UPDATE<PERSON>       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L130M01B PRIMARY KEY(OID)
) in EL_DATA_4KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL130M01B01;
CREATE UNIQUE INDEX LMS.XL130M01B01 ON LMS.L130M01B   (MAINID, BRANCHKIND);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L130M01B IS '異常通報表事項檔';
COMMENT ON LMS.L130M01B (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	BRANCHKIND    IS '單位種類', 
	SEQDSCR       IS '事項內容', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
