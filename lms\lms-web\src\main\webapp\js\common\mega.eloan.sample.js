//==================================================================
// 共用common開始...........
//==================================================================

//復原變更集291709
(function(window, $){

    if (window.util) 
        return;
    
    window.util = {
        /** 對選擇的區域，進行初始化動作 */
        init: function(elem){
            var root = elem || document;
            
            // 初始化所有貨幣欄位
            var currBlur = function(){
                var s;
                // class屬性如為negative可放負數
                if ($(this).attr("class").indexOf("negative") == -1) {
                    s = this.value.replace(/[^\d\.]/g, '');
                }
                else {
                    s = this.value.replace(/[^-\d\.]/g, '');
                }
                var m = /^-?\d+(\.\d+)?$/.exec(s);
                if (m) {
                    this.value = util.addComma("" + (m[1] ? parseFloat(s, 10) : parseInt(s, 10)));
                }
                else {
                    this.value = "";
                }
            };
            $(".number", root).css("text-align", "right").css("padding-right", "1px").focus(function(){
                util.delComma(this);
                this.select();
            }).blur(currBlur).each(function(){
                currBlur.apply(this);
            });
            var obuTextBlur = function() {
               // 僅能輸入 ascii code
    			this.value = this.value.replace(/[^\u0000-\u007F]/g, '');
    		};
    		$(".obuText", root)
    		.blur(obuTextBlur)
    		.each(function(){
    			obuTextBlur.apply(this);
    		});
        },
        /**
         * 加上千分位符號
         * 如elem為欄位，則直接作用於欄位上
         * 如elem為字串，則作用在回傳值
         * 如elem為數字，則回傳處理後的字串
         */
        addComma: function(elem){
            var isInput = (typeof elem !== "string" && typeof elem !== "number");
            var value = (isInput ? elem.value : elem) + "";
            if (!/^-?\d+(\.\d+)?$/.test(value)) {
                return value;
            }
            var pos = value.indexOf(".");
            if (pos == -1) {
                pos = value.length;
            }
            value = value.substring(0, pos).replace(/(\d{1,3})(?=(\d{3})+(?:$|\D))/g, "$1,") + value.substring(pos);
            if (isInput) {
                elem.value = value;
            }
            return value;
        },
        
        /**
         * 去掉千分位符號
         * 如elem為欄位，則直接作用於欄位上
         * 如elem為字串，則作用在回傳值
         */
        delComma: function(elem){
            var isInput = (typeof elem !== "string");
            var value = (isInput ? elem.value : elem) + "";
            if (!/^-?(\d+,)*\d{1,3}(\.\d+)?$/.test(value)) {
                return value;
            }
            if (value.length) {
                value = parseFloat(value.replace(/,/g, ""), 10);
            }
            if (isInput) {
                elem.value = value;
            }
            return value;
        },
		/**
		 *  數字前面補0
		 * @param {Object} str 
		 * @param {Object} num 要補的位數
		 */
		addZeroBefore:function(str, num){
			if(!str || !/\d/.test(str)){
				return str;
			}
			if(str.length < num ){
				for(var i = 0,tal = num - str.length ; i < tal ; i++){
					str = "0" + str;
				}
			}
			return str;
		},
        /**
         * 判斷是否為總部分行
         * util.isSpectialBank("007")
         * @param {Object} brid 分行代號
         */
        isSpectialBank: function(brid){
            if (brid) {
                if (brid == "007" || brid == "brid" || brid == "025" ||
                brid == "011" ||
                brid == "201" ||
                brid == "940" ||
                brid == "943" ||
                brid == "149") {
                    return true;
                }else if(brid == "918"){
					if (userInfo){
						if (userInfo.unitType != "4"){
						   return true;
						}
					}
				}
            } else {
				alert("util.isSpectialBank() error");
			}
            return false;
        },
        getToday : function(){
        	 var result = '';
        	 try {
	             var d = new Date();
	             result = d.getFullYear() + '-' + util.addZeroBefore((d.getMonth() + 1) + '', 2) +
	             				'-' + util.addZeroBefore(d.getDate() + '', 2);
	         } 
	         catch (e) {
	         }
           return result;
        }
    }; // end of util definition
    // 預設的初始化動作
    $(function(){
        util.init();
    });
    
})(window, jQuery);
//==================================================================
// 共用commonr結束....................
//==================================================================

/* 2025/05/16 這段移到common.js
jQuery.fn.extend({ 
    setValue: function(source, reset, formPrx, sourcePrx){
    	var $form = $(this);
    	if ((reset === undefined || reset) && $form.is('form')) {
    		$form.attr('formOid', '');
    		$form.reset();
    		$form.find('span.comboSpace').html(i18n.def['comboSpace'] || '--請選擇--');
    	}
    	
    	if (source){
    		formPrx = (formPrx || '');
    		sourcePrx = (sourcePrx || '');
    		var data = $.extend({},source);
    		//$(["input","select","textarea","span[id]","form[id]"]).each(function(i,v){
	    	$(["input","select","textarea","span[id]"]).each(function(i,v){
	    		$form.find(v).each(function(){
					var $item = $(this);
					var id = $item.attr('id') || $item.attr('name');
					if (formPrx) id = (id || '').replace(formPrx, '');
                    // 20240206,007623,因應 Checkmarx ,Client DOM Stored XSS 測試 DOMPurify
                    var value = data[DOMPurify.sanitize(sourcePrx+id)];
					//if (value){
			    	if (value || value === ''){
			    		var tagName = ($item.prop('tagName') || '').toLowerCase();
				    	switch (tagName){
			    		case 'input' :
			    			var type = ($item.attr('type') || '').toLowerCase();
							switch (type){
							case 'radio':
								$form.find("input[name='"+id+"']").prop('checked', false).filter("[value='"+value+"']").trigger('click').prop("checked", true);
								//原寫法，未把舊值清空 $form.find("input[name='"+id+"'][value='"+value+"']").attr("checked", true).trigger('click');
								break;
							case 'checkbox':
								if (!$.isArray(value)) value = (value+'').split("|");
								//原寫法，未把舊值清空
								$form.find("input[name='"+id+"']").prop('checked', false);
				    			var size = value.length;
				    			if (size > 0){
				    				var $$item = $form.find("input[name='"+id+"']");
					    			for (var i=0;i<size;i++){
		  		    					$$item.filter("[value='"+value[i]+"']").attr("checked", true).trigger('click').attr("checked", true);
					    			}
				    			}
								break;
							default:
								 $item.val(value);
								 break;
							}
			    			break;
			    		case 'select' :
			    			$item.val(value).trigger('change');
			    			break;
			    		case 'span' :
			    		case 'textarea' :
				    		if ($item.is(".ickeditor")){
				    			setTimeout(function(){
		                    		try{
		                				var name = $item.attr('name');
			                    		var ckEditor = CKEDITOR.instances[name];
			                    		if (ckEditor) ckEditor.setData(value);
			                		}catch(e){
			                			$item.html(value);
			                		}
				    			}, 1);
			              	}else{
			              		$item.html(value);
			              	}
			    			break;
			    		case 'form' :
			    			//$item.setValue(value);
			    			break;
				    	}
				    }
			    	delete data[id];
			    }); //end $form.find(v).each
		    }); //end $(["input",
	    	//set oid
	    	var oid = data['oid'];
	    	if (oid) {
	    		$form.attr('formOid', oid);
	    		delete data['oid'];
	    	}
    	} //if (source)
    },
	//form reset
    reset: function(){
        $(this).find(".data-error").removeClass("data-error").end().find("input:text,.field").data("realValue","").filter(".field").val("").end().end().each(function(){
          this.reset();
        });
				$(this).find("div.preview").empty();
				return this;
    },
    buildItem : function(){
    	//var objs = $(this).find('.setItem');
    	var objs = $(this).find('input:checkbox,input:radio,select').filter('[codeType]');
    	var res = [];
    	objs.each(function(){
    		var value = $(this).attr('codeType') || '';
    		if ($.inArray(value, res) == -1) res.push(value);
    	});
    	var items = CommonAPI.loadCombos(res);
    	objs.each(function(){
    		var $obj = $(this);
    		var codeType = $obj.attr('codeType');
    		if (codeType){
    			var itemJson = {};
	    		var styleArray = ($obj.attr('itemStyle') || '').split(';');
	    		for (var o in styleArray){
	    			var arr = (styleArray[o] || '').split(':');
	    			if (arr.length >=2) {
	    				var value = arr[1];
	    				if (value === 'true') value = true;
	    				if (value === 'false') value = false;
	    				itemJson[arr[0]] = value; 
	    			}
	    		}
	    		$obj.setItems($.extend(itemJson, {
	    			item : items[codeType] || {}
	    		}));
    		}
    	});
    }
});*/

/* 專案JS設定檔   如需使用i18n 請放此*/
/* 這段移到mega.eloan.lms.js
$.extend(Properties, {
    constants: {
        MAIN_DOC_STATUS: "mainDocStatus",
        MAIN_OID: "mainOid",
        MAIN_ID: "mainId",
        MAIN_UID: "uid",
        //文件狀態
        docStatus: {
            "01O": "01O",//海外_編製中
            "02O": "02O",//海外_待覆核
            "05O": "05O",//海外_已核准
            "06O": "06O",//海外_婉卻
            "07O": "07O",//海外_待補件
            "0EO": "0EO",//海外_待撤件
            "03B": "03B",//海外_提會待登錄
            "04B": "04B",//海外_提會待覆核
            "0DO": "0DO",//海外_總行待覆核
            "L1C": "L1C",//營運中心_審查中
            "L2C": "L2C",//營運中心_待放行
            "L10": "L10",//授管處_已收案件
            "L0H": "L0H",//授管處_待收案件
            "L1H": "L1H",//授管處_審查中
            "L6H": "L6H",//授管處_待放行
            "L8H": "L8H"//授管處_待更正
        }
    }

});
var Constants = Properties.constants;
*/

/**
 Ex:
 BrowserAction.submit({
 system : "cms", //系統別
 url    : "../c01/cms1010m01/02", //文件URL
 mainId : "3f7ea07403814e36abb3f8e060a04d2b", //mainId
 txCode : "131003",
 data   : { //其它參數
 oid : "1C1A53605C7211E1AC291273C0A83D4A",
 mainOid : "1C1A53605C7211E1AC291273C0A83D4A",
 mainDocStatus : "11B",
 rptType : "A1",
 oidpndFlag : "N"
 }
 });
 */
//for LMS add by fantasy 2012/04/06
var BrowserAction = {
	data : {
		ces : 'http://192.168.53.85:9080/ces-web/app/ssoverify',
		lms : 'http://192.168.53.85:9081/lms-web/app/ssoverify',
		cms : 'http://192.168.53.85:9082/cms-web/app/ssoverify'
	},
	init : function(){
		$.ajax({
			type : "POST",
			handler : "browseractionformhandler",
			action : "queryData",
			success:function(responseData){
				//$.extend(BrowserAction.data, responseData.data || {});
				if (responseData.data)
					$.extend(BrowserAction.data, responseData.data);
			}
		});
	},
    submit: function(options, ssoverify){
    	if (!ssoverify)
    		ssoverify = BrowserAction.data[options.system];

        if (userInfo && ssoverify) {
            $.form.submit({
                url: ssoverify,
                data: {
                    lightID: (userInfo.lightId || ''),
                    loginUnit: (userInfo.unitNo || ''),
                    sendData: JSON.stringify(options),
					forceChangeLanguage : (options.forceChangeLanguage || ''),
					mainLocale : (options.userLocale||userInfo.userLocale || '')
                },
                encode: true,
                target: options.mainId
            });
        }
    },
    send : function(options){
 		$.ajax({
			type : "POST",
			handler : "browseractionformhandler",
			action : "querySystemData",
			data : {
				system : options.system
			},
			success:function(responseData){
				BrowserAction.submit(options, responseData.ssoverify);
			}
		});
    }
};

/**
 * TODO TEST 這段移到common.js並參考CES版本，同CES作法
 * thickbox ckeditor
 * class set tckeditor
 * <AUTHOR>
 * @version 2012/05/02,Fantasy,new
 */
/*
var EditorAction = {
	formId : null,
	id : null,
	ckeditor : null,
	readOnly : false,
	name : 'editorAction',
	tckeditor : 'tckeditor_',
	thickboxId : 'tckeditor_thickbox',
	preview : 'tckeditor_preview_',
	parsePreview : function(str){
		var result = {};
		if (str != null){
			var jsonStr = '';
			var strArray = str.split(';');
			for (var s in strArray){
				var chrArray = strArray[s].split(':');
				var len = chrArray.length;
				if (jsonStr != '') jsonStr += ',';
				if (len >= 2){
					jsonStr += '"'+chrArray[0]+'":"'+chrArray[1]+'"';
				}else if (len == 1){
					jsonStr += '"'+chrArray[0]+'":"true"';
				}
			}
			result = $.parseJSON('{'+jsonStr+'}');
		}	
		return result;
	},
	transform : function($obj){
		if ($obj){
			$obj.hide();
			var showType = $obj.attr('showType') || '';
			var id = $obj.attr('id') || $obj.attr('name');
			var msg = $obj.attr('displayMessage') || '';
			var $form = $obj.closest('form');
			var formId = $form.attr('id') || '';
			
			var preview = $obj.attr("preview");
			var previewObj = EditorAction.parsePreview(preview);
			if (preview != null){
				$obj.after('<span class="color-red">' + i18n.def.defaultFontSize + '</span><div id="'+EditorAction.preview+id+'" class="preview" '+(preview ? 'preview="'+preview+'"' : '')
						+ (previewObj.disabled ? '' : ' ondblclick="EditorAction.parse(\''+formId+'\',\''+id+'\');" ')
						+' ></div>');
				 //ie use propertychange
				if ($.browser.msie){
					$obj.bind('propertychange', function(){
						var $this = $(this);
						var $div = $('#'+EditorAction.preview+$this.attr('id'));
						if ($div.size()>0) $div.html($this.val());
					});
				}else{
					$obj.watch('value', function(prop, oldval, newval){
						var $this = $(this);
						var $div = $('#'+EditorAction.preview+$this.attr('id'));
						if ($div.size()>0) $div.html(newval);
						return newval;
					});
				}
				EditorAction.setPrevew($('#'+EditorAction.preview+id));
			}
			
			if (!previewObj.disabled){
				//have msg create a link
				var $span = $obj.next('#'+EditorAction.tckeditor+id);
				if ($span.size() == 0){
					$obj.after('<span id="'+EditorAction.tckeditor+id+'" ></span>');
				}

				if(showType=="b"){
					$obj.after('<button type="button" onClick="EditorAction.parse(\''+formId+'\',\''+id+'\');" >'+(i18n.def['openTckeditBoxmsg'].replace("{0}",EditorAction.display(msg)))+'</button>');   				
					$obj.next('button').prepend('<span class="ui-icon ui-icon-jcs-07" />')
  					.button().addClass('fg-button').wrap('<span class="fg-buttonset" />').wrap('<span class="fg-child" />');
					
				}else if(showType=="h"){
					//不產按鈕及連結(即為隱藏) Miller add 2012/07/17
				}else{
					$obj.next('#'+EditorAction.tckeditor+id).html(EditorAction.display(msg))
					.wrap('<a href="#" onClick="EditorAction.parse(\''+formId+'\',\''+id+'\');" ></a>');
				}
			}
		}
	},
	setPrevew : function($div){
		var view = $div.attr("preview");
		var preview = EditorAction.parsePreview(view);
		//attr
		$div.parent('td,div,form').each(function(){
			var $parent = $(this);
			if (!preview['height'] && $parent.height() != 0) preview['height'] = $parent.height();
			if (!preview['width'] && $parent.width() != 0) preview['width'] = $parent.width();
		});
		$div.css(preview);
	},
	parse : function(formId, id){
		EditorAction.formId = formId;
		EditorAction.id = id;
		
		
		var $tb = $('#'+EditorAction.thickboxId);
		if ($tb.size() == 0){
			$('body').append('<div id="'+EditorAction.thickboxId+'" style="display:none;"><div><span id="showMsg1"/>'
					+'<textarea name="'+EditorAction.name+'" class="ickeditor" ></textarea>'
					+'<span id="showMsg2"/></div></div>');
			$('#'+EditorAction.thickboxId).find('.ickeditor').each(function(){
				$(this).ckeditor();
			});
		}
		
		var $obj = $(EditorAction.objID());
		//2013-05-17,Rex,增加t_width, t_height 設定ckeditor大小
		var t_width = $obj.attr('t_width') || 800;
		var t_height = $obj.attr('t_height') || 450;
		var title = $obj.attr('displayMessage') || '';
		var isReadOnly = $(EditorAction.objID()).attr('readOnly');
		var buttonObj  ={
				"sure" : function() {
					if (EditorAction.sure()) $.thickbox.close();
					//2013-08-20,Rex,解決選擇原始碼問題，當確定或取消非wysiwyg模式要還原回來
					if (EditorAction.ckeditor.mode != 'wysiwyg' ) {
						EditorAction.ckeditor.setMode('wysiwyg');
						EditorAction.ckeditor = CKEDITOR.instances[EditorAction.name];
					}
				},
				"cancel" : function() {
					//2013-08-20,Rex,解決選擇原始碼問題，當確定或取消非wysiwyg模式要還原回來
					if(EditorAction.ckeditor.mode != 'wysiwyg' ){
						EditorAction.ckeditor.setMode('wysiwyg');
						EditorAction.ckeditor = CKEDITOR.instances[EditorAction.name];
					}
					$.thickbox.close();
				}
		};
		//Modify by ICE
		$('#showMsg1,#showMsg2').empty();
		//showNewLineMessage : 顯示註1:建議字型16 註2:|←字型16時建議換行
		var showNewLineMessage = $obj.attr('showNewLineMessage') || '';
		//需要距離多少距離
		var distanceWord = $obj.attr('distanceWord') || '0';
		if(showNewLineMessage == 'Y'){
			var msg1 = '<b class="star">' + i18n.def['lms.ckeditRemark1'] + '</b><br/>' + 
						'<b class="star">' + i18n.def['lms.ckeditRemark2'] + '</b><br/>';
			var space = '';
			var distance = parseInt(distanceWord, 10);
			for(var i=0;i<distance;i++) space += '　';
			$('#showMsg1').html(msg1 + space + '|←');
			$('#showMsg2').html(space + '|←');
		}
		
		if(isReadOnly){
			delete buttonObj["sure"];
		}
		
		$('#'+EditorAction.thickboxId).thickbox({ // 使用選取的內容進行彈窗
			title : title,
			width :  parseInt(t_width, 10) + 100,
			height : parseInt(t_height, 10) + 100,
			valign : "bottom",
			align : "center",
			buttons : buttonObj
		});
	
		//set readonly
		setTimeout(function(){
			EditorAction.setReadOnly(isReadOnly, EditorAction.objID(), t_width, t_height);
		}, 300);
		
		$obj = null;
	},
	sure : function(){
		var id = EditorAction.objID();
		if (id){
			if (!EditorAction.ckeditor)
				EditorAction.ckeditor = CKEDITOR.instances[EditorAction.name];
			
			if (EditorAction.ckeditor){
				var data = '';
				try{
					data = EditorAction.ckeditor.getData() || '';
					//data = EditorAction.ckeditor.document.getBody().getHtml() || '';
					EditorAction.ckeditor.setData(''); //clear
				}catch(e){
					alert('get data error:'+e);
				}
			  //fix get value add by fantasy 2013/05/16
				if (!data){
				//if (jQuery.browser.msie){
					try{
						data = EditorAction.ckeditor.document.getBody().$.innerHTML;
						EditorAction.ckeditor.document.getBody().$.innerHTML = ''; //clear
					
						if (data != '<br/>' || data != '<p><br/></p>');
						
						var $temp = $('<div/>').append(data);
						$temp.find('p').each(function(){
							if (!$(this).attr('style'))
								$(this).attr('style', 'margin:0px;padding:0px;font-family: 新細明體;font-size: 16px;');
						});
						data = $temp.html();
						
						//fix clear to empty add by fantasy 2013/06/14
						if ($temp.find('p').size() == 1){
							var pv = ($temp.find('p').html() || '').toLowerCase();
							if (pv === '&nbsp;' || pv === '<br>') data = '';
						}else{
							if (data === '&nbsp;' || data === '<br>') data = '';
						}
					}catch(e){}
				}
				$(id).val(data);
				//$(id).show(); //html debug
			}
		}
		return true;
	},
	setReadOnly : function(sure, id, t_width, t_height){
		if (!EditorAction.ckeditor)
			EditorAction.ckeditor = CKEDITOR.instances[EditorAction.name];

		if (EditorAction.ckeditor){
			//set value add by fantasy 2013/05/16
			if (id) {
				//fix ie set value add by fantasy 2013/05/24
				try{
					if (jQuery.browser.msie){
						EditorAction.ckeditor.document.getBody().$.innerHTML = $(id).val();
					}else{
						EditorAction.ckeditor.setData($(id).val());
					}
				}catch(e){
					alert('set data error:'+e);
				}
			}
			
			try{
				var _ickeditor = EditorAction.ckeditor.document.$;
	            _ickeditor.body.contentEditable= (sure?'false':'true');
	            $("#cke_top_" + EditorAction.name)[sure?'hide':'show']();
				//2013-05-17,Rex,設定ckeditor大小
				EditorAction.ckeditor.resize(t_width,t_height);	
			}catch(e){}
		}
	},
	objID : function(){
		return (EditorAction.formId && EditorAction.formId != '' 
			? '#'+EditorAction.formId+' ' : '')+'#'+EditorAction.id;
	},
	display : function(msg){
		var result = (msg ? msg : (i18n ? i18n.def['look'] : 'look' ));
		return result;
	}
};
*/

/**
 * 外部連結
 * EX: 
 * ExternalAction.open({
 *   action : ejcic, //ejcic,etch
 *   sysId : 'CLS', //LMS,CLS..
 *   prodId : 'P7', //ejcic P7, etch 4111,4114..
 *   queryId : 'A123456789' //
 * });
 */
var ExternalAction = {
	handler : 'externalactionformhandler',
	open : function(options){
		var data = $.extend({
			sysId : 'CLS' //個金
		}, options);
		
		if (data.queryType){
			$.ajax({
				handler : ExternalAction.handler,
				action : data.queryType+'Url',
				formId : 'ExternalForm',
				data : data,
				success : function(response) {
					if (response.url){
						$.form.submit({
							url : response.url,
							target : data.queryType
				        });
					}
				}
			});
		}
	},
	submit : function(options){
		var data = $.extend({
			sysId : 'CLS' //個金
		}, options);
		
		if (data.queryType){
			$.form.submit({
				url : webroot+"/app/external",
				data : data,
				target : data.queryType
	        });
		}
	}
};

/**
 * 新增借款人
 */
/* 2025/05/16 這段移到common.js
var AddCustAction = {
	handler : null,
	action : null,
	data : null,
	doNewUser:false,
	dupNoInput:false,
	fn:function(){
		
	},
	open : function(options){
		
		$.extend(AddCustAction, options);
		AddCustAction.dupNoInput = !!options.dupNoInput; 
		if (AddCustAction.handler && AddCustAction.action){
			var $tb = $('#addCustThickBox');
			if ($tb.length == 0){
				var html = '';
				html += '<div id="addCustThickBox" style="display:none;">';
				html += ' <div id="addCustDiv" ><form id="addCustForm" >';
				html += '  <table class="tb2" width="100%">';
				html += '	<tr>';
				html += '    <td width="30%" class="hd2" align="right" >'+(i18n.def['compID'] || '統一編號')+'&nbsp;&nbsp;</td>';
				html += '    <td>';
				html += '     <input type="text" id="add_custId" name="add_custId" class="checkID required max" maxlength="10" size="10"/>';
				//多加一個隱藏欄位以免按下enter時自動 Submit
				html += '     <input type="text"  style="display:none"/>';
				html += '     &nbsp;'+(i18n.def['dupNo'] || '重覆序號')+'&nbsp;'
				html += '<input id="add_dupNo" name="add_dupNo" size="1" class="required" maxlength="1" />';
				//html += '<span id="add_dupNo" class="field" />';	
				html += '     &nbsp;<button type="button" onClick="AddCustAction.importData();" ><span class="ui-button-text" ><span class="text-only">'+(i18n.def['query'] || '查詢')+'</span></span></button>';
				html += '    </td>';
				html += '	</tr>';
				html += '   <tr>';
				html += '    <td width="30%" class="hd2" align="right" >'+(i18n.def['name'] || '姓名/名稱')+'&nbsp;&nbsp;</td>';
				html += '    <td ><span id="add_custName" class="field" /></td>';
				html += '   </tr>';
				html += '  </table>';
				html += ' </form></div>';
				html += '</div>';
				$('body').append(html);
				//將統編轉大寫 add by fantasy 2013/04/09
				$('#addCustForm').find('#add_custId,#add_dupNo').blur(function(){
					var value = ($(this).val() || '').toUpperCase();
					$(this).val(value);
				});
				$('#addCustThickBox').find('button').button().addClass('fg-button').wrap('<span class="fg-buttonset" />').wrap('<span class="fg-child" />');
			}
			var $form = $('#addCustForm');
			if (AddCustAction.dupNoInput){
				$form.find("#add_dupNo").removeAttr("disabled");
			} else {
				$form.find("#add_dupNo").attr("disabled","disabled");
			}
			$form.reset();
			$('#addCustThickBox').thickbox({
				title : i18n.def['newData'] || '',
				width : 450,
				height : 180,   
				align : 'center',
				valign: 'bottom',
				buttons : {
					'sure' : function(){
						if ($form.valid()){
							//var custIdValue = $form.find('#add_custId').val();
							//var dupNoValue = $form.find('#add_dupNo').val();
							var custNameValue = $form.find('#add_custName').val();
							if (!custNameValue){
								var msg = i18n.def['name']+' '+i18n.def['val.required'];
								MegaApi.showErrorMessage(i18n.def["confirmTitle"], msg);
								return;
							}
							$.ajax({
								handler : AddCustAction.handler,
								action : AddCustAction.action,
								formId : 'addCustForm',
								data : $.extend(AddCustAction.data, {
									custId : $form.find('#add_custId').val(),
									dupNo : $form.find('#add_dupNo').val(),
									custName : $form.find('#add_custName').html()
								}),
								success : function(response) {
									if ($.isFunction(AddCustAction.callback)){
										AddCustAction.callback(response);
									}
									if(AddCustAction.fn){
										AddCustAction.fn(response);
									}
								}
							});
						}
					},
					'cancel' : function(){	
						$.thickbox.close();
					}
				}
			});
		}
	},
	callback : function(response){
		$.thickbox.close();
		MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["runSuccess"]);
		var $grid = $('#gridview');
		if ($grid.size() > 0){
			$grid.trigger('reloadGrid');
		}else{
			CommonAPI.triggerOpener("gridview", "reloadGrid");
		}
	},
	importData : function(options){
		var $form = $('#addCustForm');
		if ($form.valid()){
			//綁入MegaID
			CommonAPI.openQueryBox({
	            defaultValue: $form.find('#add_custId').val(),//指定時會自動查詢 
				defaultCustType : '1', //2.英文名
	            divId : 'addCustDiv', //在哪個div 底下 
	            doNewUser: AddCustAction.doNewUser, //是否允許新客戶   (false:不出現新客戶選單)   
	            autoResponse: { // 是否自動回填資訊 
	               id   : 'add_custId', // 統一編號欄位ID 
	               dupno: 'add_dupNo', // 重覆編號欄位ID 
	               name : 'add_custName' // 客戶名稱欄位ID 
	            }
				//,
	            //fn : function(response){
					//alert(response.custid);
					//alert(response.dupno);
					//alert(response.name);
	            //}
	            					
			});
		}
	}
};*/

/**
 * 附加檔案
 * EX:FilesAction.open({
 *   deleteDup : true, //刪除名稱重覆之檔案 defalut false
 *	 fileCheck: ['.xls','.doc], //限制副檔名 default false
 *	 limitSize : 9999999, //限制檔大小 default 3145728
 *	 mainId : data.mainId, //主文件mainId default responseJSON.mainId
 *	 pid : data.oid, //子文件ID default ''
 * });
 */
FilesAction = {
		handler : 'docfileformhandler',
		setting : {},
		grid : null,
		build : function(){
			if ($('#_docFilesThickBox').size() == 0){
				$('body').append('<div id="_docFilesThickBox" style="display:none;">'
						+'<div id="_docFilesGrid" /></div>');
				
				FilesAction.grid = $('#_docFilesGrid').iGrid({
					localFirst: true,
					handler : 'docfilegridhandler', //設定handler
					height : 250, //設定高度
					action : 'query', //執行的Method
					rowNum : 100,
					rownumbers : true,
					multiselect : true, //是否開啟多選
					colModel : [{
							name : 'oid',
							hidden : true //是否隱藏
						},{
							name : 'mainId',
							hidden : true //是否隱藏
						},{
							colHeader : i18n.def['uploadFile.srcFileName'], //檔案名稱
							align : "left",
							width : 100, //設定寬度
							sortable : true, //是否允許排序
							name : 'srcFileName',
							formatter: 'click',
							onclick : function(cellvalue, options, rowObject){
								FilesAction.download(rowObject);
							}
						},{
							colHeader : i18n.def['uploadFile.srcFileDesc'], //檔案說明
							align : "left",
							width : 100, //設定寬度
							sortable : true, //是否允許排序
							name : 'fileDesc'
						},{
							colHeader : i18n.def["uploadFile.uploadTime"], //上傳時間
							align : "left",
							width : 100, //設定寬度
							sortable : true, //是否允許排序
							name : 'uploadTime'
						}
					],
					ondblClickRow : function(rowid) {
						var data = FilesAction.grid.getRowData(rowid);
						FilesAction.download(data);
					}
				});
			} // end if
			return true;
		},
		open : function(setting){
			//set default config
			FilesAction.setting = $.extend({
				deleteDup : false,
				fileCheck: false,
				limitSize : 3145728,
				mainId : (typeof responseJSON != 'undefined' ? (responseJSON.mainId || '') : ''),
				pid : '', //(typeof responseJSON != 'undefined' ? (responseJSON.oid || '') : '')
				uploadConfig : {}
			}, setting || {});
			//build
			if (FilesAction.build()){
				$('#_docFilesThickBox').thickbox({
					title : i18n.def['attachfile'],
					width : 800,
					height : 400,
					buttons : {
						'newData' : function() {
							FilesAction.upload();
						},
						'look' : function(){
							var data = FilesAction.grid.getSingleData();
							if (data) FilesAction.download(data);
						},
						'del' : function() {
							var args = FilesAction.grid.getSelectData('oid');
							if (args) FilesAction.remove(args);
						},
						'close' : function() {
							$.thickbox.close();
						}
					}
				});
				//load data
				FilesAction.grid.reload(FilesAction.setting);
			} // end if
		},
		/**
		 * 上傳檔案
		 */
		upload : function(){
			MegaApi.uploadDialog($.extend({
				fieldId : 'uploadFileId',
				fileDescId : 'uploadFileDescId',
        width: 320,
        height: 200,
        subTitle: i18n.def('insertfileSize',{
        	'fileSize':(FilesAction.setting.limitSize/1048576).toFixed(2)
        }),
		    limitSize: FilesAction.setting.limitSize,
        fileCheck: FilesAction.setting.fileCheck,
        data: {
        	 deleteDup : FilesAction.setting.deleteDup,
        	 mainId : FilesAction.setting.mainId,
        	 uid : FilesAction.setting.pid
        },
        success: function(response){
        	MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['addSuccess']);
        	FilesAction.grid.reload();
        }
			}, FilesAction.setting.uploadConfig || {}));
		},
		/**
		 * 下載檔案
		 */
		download : function(data){
			$.capFileDownload({
				data : {
					fileOid : data.oid
				}
			});
		},
		/**
		 * 移除
		 */
		remove : function(args){
			MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action) {
				if (action) {
					$.ajax({
						handler : FilesAction.handler,
						action : 'remove',
						formId : 'empty',
						data : {keys : args},
						success : function(response) {
							MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['confirmDeleteSuccess']);
							FilesAction.grid.reload();
						}
					});
				}
			});
		}
};

/**
 * 黑名單查詢
 * EX: BlackNameAction.query({
 *   cust : {'1': {custid:'xx',dupNo:'x'}, '2' : {custId:'yy',dupNo:'y'},
 *   custId : 'xxx',
 *   dupNo : '0',
 *   callback : function(response){
 *    alert(response.message) //query message
 *   }
 * });
 */
BlackNameAction = {
		cust : {},
		option : {},
		handler : 'lmscommonformhandler',
		query : function(option){
			BlackNameAction.option = $.extend({}, option || {});
			
			$.ajax({
				handler : BlackNameAction.handler,
				action : 'queryBlackName',
				formId : 'empty',
				data : {
					cust : JSON.stringify(BlackNameAction.option.cust || {}),
					custId : BlackNameAction.option.custId || '',
					dupNo : BlackNameAction.option.dupNo || ''
			  },
				success : function(response) {
					BlackNameAction.cust = $.extend({}, response.cust);
					
					var ciMsg = BlackNameAction.parseMessage('ci0024');
					if (ciMsg){
						MegaApi.showErrorMessage(i18n.def["confirmTitle"], ciMsg);
						return;
					}
					
					var message = BlackNameAction.parseMessage('eName');
					if (message){
						var msg = (message || '') + '<br/>';
						msg += i18n.def['queryByEnglish'] + '<br/>';
						msg += i18n.def['actoin_001'] + '<br/>';
						MegaApi.confirmMessage(msg, function(action) {
							if (action) {
								BlackNameAction.open();
							}else{
								BlackNameAction.option.callback(response);
							}
						});
					}else{
						if ($.isFunction(BlackNameAction.option.callback)){
							BlackNameAction.option.callback(response);
						}else{
							CommonAPI.showMessage(BlackNameAction.parseMessage() || i18n.def['runSuccess']);
							//MegaApi.showPopMessage(i18n.def["confirmTitle"], BlackNameAction.parseMessage() || i18n.def['runSuccess']);
						}
					}
				}
			});
		},
		open : function(){
			if ($('#blackNameThickBox').size() == 0){
				var html = '<div id="blackNameThickBox" style="display:none;" >';
				html += '<form id="blackNameForm" >';
				html += ' <table id="blackNameTable" class="tb2" width="100%">';
				html += '  <tr id="blackNameTr" >';
				html += '   <td width="15%" class="hd2" align="center" >'+(i18n.def['compID'] || '統一編號')+'</td>';
				html += '   <td width="30%" class="hd2" align="center" >'+(i18n.def['creatCust.ENAME'] || '英文戶名')+'</td>';
				html += '  </tr>';
				html += ' </table>';
				html += ' <input type="text" style="display:none;" />';
				html += '</form>';
				html += '</div>';
				$('body').append(html);
			}
			
			var height = 130;
			if (BlackNameAction.cust){
				var $tr = $('#blackNameTr');
				$tr.nextAll('tr').remove();
				
				for (var o in BlackNameAction.cust){
					var json = BlackNameAction.cust[o];
					if (json.eName === ''){
						var html = '<tr><td>'+(json.custId || '') +' '+(json.dupNo || '')+'</td>'
							       + ' <td><input class="required enText2" value="'+(json.eName || '')+'" onblur="BlackNameAction.eNameBlur(this,\''+o+'\');"; /></td>'
							       + '</tr>';
						height += 30;
						$tr.after(html);
					}
				}
			}
			
			$('#blackNameThickBox').thickbox({
				title : i18n.def['val.enText'] || '',
				width : 400,
				height : (height > 300 ? 300 : height),
				align : 'center',
				valign: 'bottom',
				buttons : {
					'sure' : function() {
						if($('#blackNameForm').valid()){
							BlackNameAction.queryEname();
						}
					},
					'close' : function() {
						$.thickbox.close();
					}
				}
			});
		},
		queryEname : function(){
			$.ajax({
				handler : BlackNameAction.handler,
				action : 'queryBlackName' ,  //'queryEname' MQ,
				formId : 'empty',
				data : {
					cust : JSON.stringify(BlackNameAction.cust || {}),
					custId : BlackNameAction.option.custId || '',
					dupNo : BlackNameAction.option.dupNo || ''
			  },
				success : function(response) {
					$.thickbox.close();
					BlackNameAction.cust = $.extend({}, response.cust);
					if ($.isFunction(BlackNameAction.option.callback)){
						BlackNameAction.option.callback(response);
					}else{
						CommonAPI.showMessage(BlackNameAction.parseMessage() || i18n.def['runSuccess']);
					}
				}
			});
		},
		parseMessage : function(type){
			var result = '';
			if (BlackNameAction.cust){
				for (var o in BlackNameAction.cust){
					var json = BlackNameAction.cust[o];
					var chk = true;
					switch (type){
						case 'eName': chk = !json.eName; break;
						case 'ci0024': chk = !json.ci0024; break;
					}
					if (chk) result += (result.length > 0 ? '<br/>' : '') + (json.message || '');
				}
			}
			return result;
		},
		eNameBlur : function(input, key){
			var value = (input.value || '').toUpperCase();
			$(input).val(value);
			if (BlackNameAction.cust){
				var json = BlackNameAction.cust[key];
				if (json) json['eName'] = value;
			}
		}
};

/**
 * GridFormatter
 * <AUTHOR>
 * @version 2012/05/14,Fantasy,new
 */
var GridFormatter = {
	number : {
		addComma : function(cellvalue, options, rowObject){
			return util.addComma(cellvalue || '');
		},
		delComma : function(cellvalue, options, rowObject){
			return util.delComma(cellvalue || '');
		} 
	},
	date : {
		'yyyy-mm' : function(cellvalue, options, rowObject)  {  
		   if (cellvalue && cellvalue.length >= 10){
			   return cellvalue.substr(0,7);
		   }
		   return cellvalue || ''; 
		},
		'yyyy-mm-dd' : function(cellvalue, options, rowObject)  {  
		   if (cellvalue && cellvalue.length >= 10){
			   return cellvalue.substr(0,10);
		   }
		   return cellvalue || ''; 
		}
	}
};

/** 用來存放基本參數 */
/*	2025/05/20 這段移到common.js
var LMSUtil ={
	fhandler :"lmscommonformhandler",
	ghandler :"lmscommongridhandler"
};*/
/**
 * 查詢縣市代碼
 */
/*	2025/05/20 這段移到common.js
var QueryCityCode = {
    tempCode: {},
    getCode: function(queryType, key){
        var CodeKey = "counties";
        if (queryType != "1") {
            CodeKey += key
        }
        if (QueryCityCode.tempCode[CodeKey]) {
            return QueryCityCode.tempCode[CodeKey];
        } else {
            $.ajax({
                handler: LMSUtil.fhandler,
                async: false,
                action: 'queryCityCode',
                formId: "empty",
                data: {
					noOpenDoc: true,
                    queryType: queryType,
                    key: key
                },
                success: function(obj){
                    QueryCityCode.tempCode[CodeKey] = obj;
                }
            });
            return QueryCityCode.tempCode[CodeKey];
        }
    }
};*/

/** 
 * <pre>
 * 選擇銀行 
 * @param {Object} s
 * ex :
 * 	 QueryBranch.open({
 * 	 removeKey: ["01",XX] | "01",//刪除不需要的選項
 *		fn: function(data){
 * 			data.bankCode:銀行代碼
 * 			data.bankCodeCn:銀行名稱
 *			data.branchCode:分行代碼
 * 			data.branchName:分行名稱
 *			//TODO some thing .....
 *		}
 *	});
 *	</pre>
 */
/*  2025/05/16 這段移到common.js
var QueryBranch = {
		banks : {},
		brnos : {},
		open : function(options){
			var def = {
		        fn: $.noop,
		        removeKey: ''
	    	}
			QueryBranch.build($.extend(def, options || {}));
		},
		build : function(s){
			if ($('#qBranchThickBox').length == 0){
				$('body').append('<div id="qBranchDiv" />');
				$('#qBranchDiv').load(webroot + '/app/lms/commompage', function(){
					$('#qBranchForm').find('#qBranch').change(function(){
						var $fm = $('#qBranchForm');
						$fm.find('#qBank,#qBrno').hide();
						var value = ''+$(this).val();
  					if (value){
  						$fm.find('#qBank').setItems({
  							item: QueryBranch.getBank(value) || {},
  										format: '{value} - {key}',
		  		          	space: true
		  	          	}).show();
  					}
					})
					.end().find('#qBank').change(function(){
						var value = ''+$(this).val();
						var $fm = $('#qBranchForm');
						$fm.find('#qBrno').hide();
						if (value){
							var qBranchValue = $fm.find('#qBranch').val();
							if (qBranchValue === '01' || qBranchValue === '02'){
								var items = QueryBranch.getBrno(qBranchValue, value);
								if (!$.isEmptyObject(items)){
									$fm.find('#qBrno').setItems({
					  				  item: items || {},
					  				  		format: '{value} - {key}',
					  		          space: true
					  	          	}).show();
								}
							}
						}
					})
		      .end().find('#qBrno').hide();
					QueryBranch.init(s);
				});
			} else {
				QueryBranch.init(s);
			}
		},	
		init : function(s){
			var item = QueryBranch.getCodeType('lms1605s03_slBankType');
			//removeKey
      if (s.removeKey) {
          for (var key in item) {
              if ((s.removeKey + '').indexOf(key) != -1) 
                  delete item[key];
          }
      }
      var $form = $('#qBranchForm');
      $form.find('#qBrno,#qBank').hide();
      $form.reset();
      $form.find('#qBranch').setItems({
          item: item,
          format: '{value} - {key}',
          space: true
      });
      //open thickbox
      $('#qBranchThickBox').thickbox({
	        title: i18n.def['grid.showAllBranch'] || '',
	        width: 550,
	        height: 200,
	        modal: true,
	        readOnly: false,
	        align: 'center',
	        i18n: i18n.def,
	        valign: 'bottom',
	        buttons: {
	            'sure': function(){
	                if ($('#qBranchForm').valid()) {
	                    s.fn(QueryBranch.getData());
	                    $.thickbox.close();
	                }
	            },
	            'cancel': function(){
	            	$.thickbox.close();
	            }
	        }
	    });
		},
		
     //取得codeType值
     //@param {Object} key
     
    getCodeType: function(key){
        return API.loadCombos(key)[key];
    },
    //取得BANK
    getBank: function(value){
        var result = $.extend({}, QueryBranch.banks[value] || {});
        if ($.isEmptyObject(result)) {
            if (value === '12' || value === '99') {
                $.ajax({
                    handler: LMSUtil.fhandler,
                    async: false,
                    action: 'queryForeignBranch',
                    formId: 'qBranchForm',
                    data: {
                        type: value
                    },
                    success: function(response){
                        $.extend(result, response.foreignBranch || {})
                    }
                });
            } else {
                $.extend(result, QueryBranch.getCodeType('BankCode' + value) || {});
            }
            QueryBranch.banks[value] = result;
        }
        return result;
    },
    //取得BRNO
    getBrno: function(branch, value){
    	var result = $.extend({}, QueryBranch.brnos[branch+value] || {});
			if ($.isEmptyObject(result)){
		    	if (branch != '12' && branch != '99'){
			      $.ajax({
				        handler: LMSUtil.fhandler,
				        async: false,
				        action: 'queryBranch',
				        formId: 'qBranchForm',
				        data: {mainBranch: value},
				        success: function(response){
				        	$.extend(result, response.brankList || {});
				        	QueryBranch.brnos[branch+value] = result;
				        }
				    });
		    	}
			}
    	return result;
    },
    //取得要回傳的值
    getData: function(){
    	  var $fm = $('#qBranchForm');
    	  var qBranchValue = ($fm.find('#qBranch').val() || '')+'';
    	  var qBankValue = ($fm.find('#qBank').val() || '')+'';
    	  var qBrnoValue =  ($fm.find('#qBrno').val() || '')+'';
    	  
    	  //var qBranchText = $fm.find('#qBranch option:selected').text() || '';
    	  var qBranchText = QueryBranch.getCodeType('lms1605s03_slBankType')[qBranchValue] || '';
    	  //var qBankText = $fm.find('#qBank option:selected').text() || '';
    	  var qBankText = (QueryBranch.banks[qBranchValue] || {})[qBankValue] || '';
    	  //var qBrnoText =  $fm.find('#qBrno option:selected').text() || '';
    	  var qBrnoText = (QueryBranch.brnos[qBranchValue+qBankValue] || {})[qBrnoValue] || '';

        var result = {
        		bankCode : qBranchValue ? (qBrnoValue ? qBankValue : qBranchValue) : '',
        		bankCodeCn : qBranchValue ? (qBrnoValue ? qBankText : qBranchText) : '',
        		branchCode : qBranchValue ? (qBrnoValue ? qBrnoValue : qBankValue) : '',
        		branchName : qBranchValue ? (qBrnoValue ? qBrnoText : qBankText) : ''
        };
        return result;
    }
};*/

/**
 * 常用主管
 * ******** TODO TEST : 將此段移到common.js，同CES
 */
// var CommonManager = {
// 	managers : {},
// 	init : function(id){
// 		var $obj = $('#'+id);
// 		var itemJson = {};
// 		var styleArray = ($obj.attr('CommonManager') || '').split(';');
// 		for (var o in styleArray){
// 			var arr = (styleArray[o] || '').split(':');
// 			if (arr.length >=2) {
// 				var value = arr[1];
// 				itemJson[arr[0]] = arr[1]; 
// 			}
// 		}
// 		var options = $.extend({
// 					id : id,
// 					value : $obj.val() || ''
// 		}, itemJson);
// 		CommonManager.open(options);
// 	},
// 	load : function(){
// 			$.ajax({
// 	      handler: 'lmscommonformhandler',
// 	      async: false,
// 	      action: 'queryCommonManager',
// 	      //formId : 'empty',
// 	      data: {},
// 	      success: function(response){
// 	      	 $.extend(CommonManager.managers, response.managers);
// 	      }
// 	  });
// 	},
// 	// 查詢常用主管名單
// 	open : function(options){
// 		// kind 1.企金 2.個金 3.通用
// 		// type 1.帳戶管理員 2.授信主管 3.授權主管 4.單位副主管 5.單位主管
// 		var setting = $.extend({
// 			kind : '3',
// 			type : '2',
// 			callback : $.noop
// 		}, options);
// 		//
// 		if ($.isEmptyObject(CommonManager.managers))
// 			CommonManager.load();
		
// 		var $CommonManagerThickBox = $('#CommonManagerThickBox');
// 		if ($CommonManagerThickBox.length == 0){
// 			var html = '<div id="CommonManagerThickBox" style="display:none;" >';
// 			html += '<form id="CommonManagerForm" >';
// 			html += ' <table class="tb2" width="100%">';
// 			html += '  <tr>';
// 			html += '   <td width="30%" class="hd2" align="right" >'+(i18n.def['err.chooseBoss'] || '')+'</td>';
// 			html += '   <td><select id="selectCommonManager" name="selectCommonManager" class="required" ></select></td>';
// 			html += '  </tr>';
// 			html += ' </table>';
// 			html += ' <input style="display:none;" >';
// 			html += '</form>';
// 			html += '</div>';
// 			$('body').append(html);
			
// 			$CommonManagerThickBox = $('#CommonManagerThickBox');
// 		}
		
// 		var items = $.extend({},
// 				CommonManager.managers[setting.kind+''+setting.type] || {},
// 				CommonManager.managers['3'+setting.type] || {}
// 		);
//         $CommonManagerThickBox.find('#selectCommonManager').setItems({
//             item: items,
//             format: '{value} - {key}',
//             value: setting.value,
//             space: true
//         });
	
// 		$CommonManagerThickBox.thickbox({
// 	      title: (i18n.def['common.004'] || '常用主管'),
// 	      width: 300,
// 	      height: 130,
// 	      readOnly: false,
// 	      align: 'center',
// 	      valign: 'bottom',
// 	      buttons: {
// 	          'sure': function(){
//                   var $form = $('#CommonManagerForm');
//                   if ($form.valid()) {
//                       var value = $form.find('#selectCommonManager').val() || '';
//                       if (setting.id) 
//                           $('#' + setting.id).val(value);
//                       setting.callback(value);
//                       $.thickbox.close();
//                   }
// 	          },
// 	          'cancel': function(){
// 	          	$.thickbox.close();
// 	          }
// 	      }
// 	  });
// 	}
// };

/**
 * 查詢LNF070中，被[額度、統編、集團控管的設定]
 */
var CntrNo_LN_CONTROL_Action = {	
	runQuery : function(){
		var _id = "_div_CntrNo_LN_CONTROL_filter";
		var _form = _id+"_form";
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.def.all+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.def.compID+"</label>");
			dyna.push("		</p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));
		    //----------		    			
		}
		//clear data		
		$("#"+_form).reset();

		//預設-全部
		$("#"+_form).find("[name='decisionExpr'][value=1]").attr("checked", "checked").trigger('change');
		
		$("#"+_id).thickbox({ title: i18n.def['query'], width: 400, height: 185, modal: true,
	        valign: "bottom", align: "center", i18n: i18n.def,
	        buttons: {
	            "sure": function(){            	
	            	if (true){
	            		var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
	            		
	            		if(val=="1" || val=="2" ){
	            			$.thickbox.close();
	            			
	            			if(val=="1"){
		            			CntrNo_LN_CONTROL_Action._runQueryWithParam("", "");
		            		}else if(val=="2"){
		            			AddCustAction.open({ handler: 'lms140mm01formhandler', action : 'echo_custId',
		            				data : { }, callback : function(json){
		            	            	$.thickbox.close();					
		            	            	CntrNo_LN_CONTROL_Action._runQueryWithParam(json.custId, json.dupNo);					
		            				}
		            			});		            			
		            		}
	            		}												
					}
	            },
	            "cancel": function(){
	            	 $.thickbox.close();
	            }
	        }
	    });
	}, 
	_runQueryWithParam : function(custId, dupNo){
		var _id = "_div_CntrNo_LN_CONTROL";
		
		if ($("#"+_id).size() == 0){
		     $('body').append("<div id='"+_id+"' style='display:none;'></div>");
		}else{
			$("#"+_id).empty();
		}
		
		$.ajax({
			handler : 'lmscommonformhandler',
			action : 'queryLnf07aCntrNoControl',
			data : {
				'custId':custId,'dupNo':dupNo
			},
			success : function(json) {
				var dyna = [];
				dyna.push("<div style='overflow:auto'>");
				var str_ctlItem = json.str_ctlItem;
				var str_startDate = json.str_startDate;
				var str_endDate = json.str_endDate;
				
				CntrNo_LN_CONTROL_Action._build_tbl(dyna, str_ctlItem, str_startDate, str_endDate
						, json.contract_title, json.contract_size, json.contract_data.arr
						, json.contract_strCnt);
				CntrNo_LN_CONTROL_Action._build_tbl(dyna, str_ctlItem, str_startDate, str_endDate
						, json.id_title, json.id_size, json.id_data.arr
						, json.id_strCnt);
				CntrNo_LN_CONTROL_Action._build_tbl(dyna, str_ctlItem, str_startDate, str_endDate
						, json.group_title, json.group_size, json.group_data.arr
						, json.group_strCnt);
				
				dyna.push("</div>");
				//===
				$("#"+_id).html(dyna.join("\n"));
				$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			        title: (custId==""?i18n.def.all:custId), width: 740, height: 450, align: "center", valign: "bottom",
			        modal: false, i18n: i18n.def,
			        buttons: {
			            "close": function(){
			                $.thickbox.close();
			            }
			        }
			    });        
			}
		});
	},
	_build_tbl : function(dyna, str_ctlItem, str_startDate, str_endDate, _title, _size, _arr, _strCnt){		
		dyna.push("<p>"+_title);
		dyna.push("<div style='margin-left:20px'>");
		if(_size > 0 ){
			dyna.push("<table border='1' width='100%'>");
			//header
			dyna.push("<tr>");
			if (true) {														
				dyna.push("<td width='40%'>&nbsp;&nbsp;"+str_ctlItem+"</td>");
				dyna.push("<td width='20%'>&nbsp;&nbsp;"+str_endDate+"</td>");
				dyna.push("<td width='20%'>&nbsp;&nbsp;"+"</td>");
				dyna.push("<td width='20%'>&nbsp;&nbsp;"+str_startDate+"</td>");				
			}							
			dyna.push("</tr>");
			$.each( _arr, function(idx, item){						
				dyna.push("<tr>");						
				if (true) {														
					dyna.push("<td>&nbsp;&nbsp;"+item.ctlItem+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+item.ctlEDate+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+item.memo+"</td>");
					dyna.push("<td>&nbsp;&nbsp;"+item.ctlSDate+"</td>");					
				}							
				dyna.push("</tr>");
			});
			dyna.push("</table>");
		}	
		dyna.push(_strCnt);//查到{0}筆資料
		dyna.push("</div>");
		dyna.push("</p>");
	}
};

var TEST = {
    inTest: false,
    addSpace: function(value, num){
        if (!value) {
            return "";
        }
        var length = value.length;
        if (num > 0 && length < num) {
            for (var times = num - length, i = 0; i < times; i++) {
                value += " ";
            }
        }
        return value;
    },
    start: function(name){
        if ((name && name.length) > this.maxlength) {
            this.maxlength = name.length;
        }
        this.startObj[name] = new Date().getTime();
    },
    end: function(name){
        this.endObj[name] = new Date().getTime();
    },
    startObj: {},
    endObj: {},
    maxlength: 0,
    clean: function(){
        this.startObj = {};
        this.endObj = {};
        this.maxlength = 0;
    },
    log: function(){
        if (TEST.inTest && this.startObj) {
            var result = "";
            for (var name in this.startObj) {
                var end = this.endObj[name] || new Date().getTime();
                result += " \n" + TEST.addSpace(name, TEST.maxlength) + ":" + ((end - this.startObj[name]) / 1000 + " sec");
            }
            try {
                API.showMessage(result.replace(/\n/g, "<br/>"));
            } 
            catch (e) {
                try {
                    console.log(result);
                } 
                catch (e) {
                    alert(result);
                }
            }
            this.clean();
        }
    }
};

//process memory
$(function(){
	//個金授信-SSO單位開頭非9 隱藏 個金徵信作業之相關查詢暨評等模型 add by fantasy 2013/06/15
	try{
		if (/welcome2/.test(window.location)){
			if (userInfo && userInfo.ssoUnitNo){
				if (!userInfo.ssoUnitNo.match('^9')){
					$('#menu2').find('a[href$=338032]').closest('li').hide();
				}
			}
		}
	}catch(e){}

	if(true){
		//在 EloanHome 當第一層無 subMenu，加上 class=noChilds_
		$("div#header_menu dl#menu dd.noChilds_311003").hide();	
	}	
	
	//20250312 先註解 TODO
	//ie 7 process menory
	/*
	if ($.browser.msie && $.browser.version <= 7){
		$(window).unload(function(){
			try{
				if (Constants) Constants = null;
				if (Properties) Properties = null;
				if (BrowserAction) BrowserAction = null;
				if (EditorAction) EditorAction = null;
				if (GridFormatter) GridFormatter = null;
				//未公開的函數CollectGarbage，這個函數是用來進行內存釋放的
				CollectGarbage();
			}catch(e){
				alert(e);
			}
		});
	}
	*/
});

(function($){
	$.extend({"log":function(){ 
		if(arguments.length > 0) {
			
			// join for graceful degregation
			var args = (arguments.length > 1) ? Array.prototype.join.call(arguments, " ") : arguments[0];
			
			// this is the standard; firebug and newer webkit browsers support this
			try { 
				console.log(args);
				return true;
			} catch(e) {		
				// newer opera browsers support posting erros to their consoles
				try { 
					opera.postError(args); 
					return true;
				} catch(e) { }
			}
			
			// catch all; a good old alert box
			//alert(args);
			return false;
		}
	}});
})(jQuery);
