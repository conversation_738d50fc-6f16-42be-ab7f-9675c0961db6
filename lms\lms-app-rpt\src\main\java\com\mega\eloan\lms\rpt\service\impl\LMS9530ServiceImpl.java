/* 
 *  LMS9530ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.LPDFA01ADao;
import com.mega.eloan.lms.dao.LPDFD01ADao;
import com.mega.eloan.lms.dao.LPDFM01ADao;
import com.mega.eloan.lms.dao.LPDFS01ADao;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.LPDFA01A;
import com.mega.eloan.lms.model.LPDFD01A;
import com.mega.eloan.lms.model.LPDFM01A;
import com.mega.eloan.lms.model.LPDFS01A;
import com.mega.eloan.lms.rpt.service.LMS9530Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS9530ServiceImpl extends AbstractCapService implements
		LMS9530Service {
	@Resource
	LPDFA01ADao lpdfa01aDao;

	@Resource
	LPDFM01ADao lpdfm01aDao;

	@Resource
	LPDFS01ADao lpdfs01aDao;
	
	@Resource
	LPDFD01ADao lpdfd01aDao;

	@Resource
	L120M01ADao l120m01aDao;
	
	private static final Logger logger = LoggerFactory
			.getLogger(LMS9530ServiceImpl.class);

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof LPDFA01A) {
					lpdfa01aDao.save(((LPDFA01A) model));
				} else if (model instanceof LPDFM01A) {
					lpdfm01aDao.save(((LPDFM01A) model));
				} else if (model instanceof LPDFS01A) {
					lpdfs01aDao.save(((LPDFS01A) model));
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof LPDFA01A) {
				lpdfa01aDao.delete(((LPDFA01A) model));
			} else if (model instanceof LPDFM01A) {
				lpdfm01aDao.delete(((LPDFM01A) model));
			} else if (model instanceof LPDFS01A) {
				//先存到LPDFD01A
				LPDFS01A waitToDel = (LPDFS01A)model;
				LPDFD01A record = new LPDFD01A();
				record.setMainId(waitToDel.getMainId());
				record.setRptUNID(waitToDel.getRptUNID());
				record.setRandomCode(waitToDel.getRandomCode());
				record.setCntrNo(waitToDel.getCntrNo());
				record.setCreator(waitToDel.getCreator());
				record.setCreateTime(waitToDel.getCreateTime());
				record.setUpdater(waitToDel.getUpdater());
				record.setUpdateTime(waitToDel.getUpdateTime());
				record.setRptDisp(waitToDel.getRptDisp());
				record.setRptSeq(waitToDel.getRptSeq());
				record.setRptFile(waitToDel.getRptFile());
				record.setRptName(waitToDel.getRptName());
				record.setRptType(waitToDel.getRptType());
				record.setDeleteTime(CapDate.getCurrentTimestamp());
				if(lpdfd01aDao.find(record)==null){
					lpdfd01aDao.save(record);
				}
				//正式刪除
				lpdfs01aDao.delete(waitToDel);
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (clazz == LPDFA01A.class) {
			return lpdfa01aDao.findPage(search);
		} else if (clazz == LPDFM01A.class) {
			search.addSearchModeParameters(SearchMode.EQUALS, "lpdfa01a.authUnit", user.getUnitNo());
			return lpdfm01aDao.findPage(search);
		} else if (clazz == LPDFS01A.class) {
			search.addSearchModeParameters(SearchMode.EQUALS, "lpdfa01a.authUnit", user.getUnitNo());
			return lpdfs01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == LPDFA01A.class) {
			LPDFA01A model = Util.isEmpty(oid) ? null : lpdfa01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		} else if (clazz == LPDFM01A.class) {
			LPDFM01A model = Util.isEmpty(oid) ? null : lpdfm01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		} else if (clazz == LPDFS01A.class) {
			LPDFS01A model = Util.isEmpty(oid) ? null : lpdfs01aDao
					.findByOid(oid);
			return (T) (model == null ? null : model);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == LPDFA01A.class) {
			List<LPDFA01A> list = Util.isEmpty(mainId) ? null : lpdfa01aDao
					.findByMainId(mainId);
			return (list == null ? null : list);
		} else if (clazz == LPDFM01A.class) {
			List<LPDFM01A> list = Util.isEmpty(mainId) ? null : lpdfm01aDao
					.findByMainId(mainId);
			return (list == null ? null : list);
		} else if (clazz == LPDFS01A.class) {
			List<LPDFS01A> list = Util.isEmpty(mainId) ? null : lpdfs01aDao
					.findByMainId(mainId);
			return (list == null ? null : list);
		}
		return null;
	}
	
	@Override
	public boolean inPremition(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ISearch search = lpdfa01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "authUnit",
				user.getUnitNo());
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		lpdfa01aDao.flush();
		List<LPDFA01A> data = lpdfa01aDao.find(search);
		if (data.size()>0)
			return true;
		else
			return false;
	}
	
	@Override
	public List<L120M01A> useL120M01A(ISearch search) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		search.addSearchModeParameters(SearchMode.EQUALS, "caseBrId", user.getUnitNo());
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "notesUp", null);
		return l120m01aDao.find(search);
	}
}
