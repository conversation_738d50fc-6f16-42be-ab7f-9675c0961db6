/* 
 * DW_RKADJUST.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 個人信用評等調整 **/

public class DW_RKADJUST extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 
	 * 分行別<p/>
	 * 　
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** 
	 * NOTES文件編號<p/>
	 * 　
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 
	 * 客戶統一編號<p/>
	 * 　
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 
	 * 重複序號<p/>
	 * 　
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 
	 * 評等模型類別<p/>
	 * 　
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;

	/** 
	 * 模型版本-大版<p/>
	 * 　
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER1", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 
	 * 模型版本-小版<p/>
	 * 　
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER2", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** 
	 * JCIC查詢日期 YYYY-MM-DD<p/>
	 * 　
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Temporal(TemporalType.DATE)
	@Column(name="JCIC_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date jcic_date;

	/**  **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="ACCT_KEY", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String acct_key;

	
	/** 
	 * 主借款人統一編號(CUSTKEY)<p/>
	 * 　
	 */
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)")
	private String cust_key;

	/** 
	 * 相關身分(LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 
	 * 勾選升等主要理由<p/>
	 * 1.淨資產高、2.職業佳、3.其他
	 */
	@Column(name="UPGRADE_REASON_FLAG", length=2, columnDefinition="CHAR(2)")
	private String upgrade_reason_flag;

	/** 
	 * 詳敘升等理由<p/>
	 * 文字敘述
	 */
	@Column(name="UPGRADE_REASON_TEXT", length=200, columnDefinition="CHAR(200)")
	private String upgrade_reason_text;

	/** 
	 * 詳敘降等理由<p/>
	 * 文字敘述
	 */
	@Column(name="DOWNGRADE_REASON_TEXT", length=200, columnDefinition="CHAR(200)")
	private String downgrade_reason_text;

	/** 
	 * 升等數<p/>
	 * 等數
	 */
	@Column(name="UPGRADE_LEVEL", columnDefinition="DECIMAL(2,0)")
	private Integer upgrade_level;

	/** 
	 * 降等數<p/>
	 * 等數
	 */
	@Column(name="DOWNGRADE_LEVEL", columnDefinition="DECIMAL(2,0)")
	private Integer downgrade_level;

	/** 
	 * 文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 
	 * 上傳資料日期<p/>
	 * 　
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;

	/** 卡友貸旗標 */
	@Column(name="C_FLAG", length=1, columnDefinition="CHAR(1)")
	private String c_flag;
	
	/** 
	 * 取得分行別<p/>
	 * 　
	 */
	public String getBr_cd() {
		return this.br_cd;
	}
	/**
	 *  設定分行別<p/>
	 *  　
	 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 
	 * 取得NOTES文件編號<p/>
	 * 　
	 */
	public String getNoteid() {
		return this.noteid;
	}
	/**
	 *  設定NOTES文件編號<p/>
	 *  　
	 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 
	 * 取得客戶統一編號<p/>
	 * 　
	 */
	public String getCustid() {
		return this.custid;
	}
	/**
	 *  設定客戶統一編號<p/>
	 *  　
	 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 
	 * 取得重複序號<p/>
	 * 　
	 */
	public String getDupno() {
		return this.dupno;
	}
	/**
	 *  設定重複序號<p/>
	 *  　
	 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 
	 * 取得評等模型類別<p/>
	 * 　
	 */
	public String getMowtype() {
		return this.mowtype;
	}
	/**
	 *  設定評等模型類別<p/>
	 *  　
	 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}

	/** 
	 * 取得模型版本-大版<p/>
	 * 　
	 */
	public Integer getMowver1() {
		return this.mowver1;
	}
	/**
	 *  設定模型版本-大版<p/>
	 *  　
	 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 
	 * 取得模型版本-小版<p/>
	 * 　
	 */
	public Integer getMowver2() {
		return this.mowver2;
	}
	/**
	 *  設定模型版本-小版<p/>
	 *  　
	 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 
	 * 取得JCIC查詢日期 YYYY-MM-DD<p/>
	 * 　
	 */
	public Date getJcic_date() {
		return this.jcic_date;
	}
	/**
	 *  設定JCIC查詢日期 YYYY-MM-DD<p/>
	 *  　
	 **/
	public void setJcic_date(Date value) {
		this.jcic_date = value;
	}



	/** 取得 **/
	public String getAcct_key() {
		return this.acct_key;
	}

	/** 設定 **/
	public void setAcct_key(String value) {
		this.acct_key = value;
	}
	
	/** 
	 * 取得主借款人統一編號(CUSTKEY)<p/>
	 * 　
	 */
	public String getCust_key() {
		return this.cust_key;
	}
	/**
	 *  設定主借款人統一編號(CUSTKEY)<p/>
	 *  　
	 **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得相關身分(LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	public String getLngeflag() {
		return this.lngeflag;
	}
	/**
	 *  設定相關身分(LNGEFLAG)<p/>
	 *  M: 主借款人  C: 共同借款人  G: 連帶保證人
	 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 
	 * 取得勾選升等主要理由<p/>
	 * 1.淨資產高、2.職業佳、3.其他
	 */
	public String getUpgrade_reason_flag() {
		return this.upgrade_reason_flag;
	}
	/**
	 *  設定勾選升等主要理由<p/>
	 *  1.淨資產高、2.職業佳、3.其他
	 **/
	public void setUpgrade_reason_flag(String value) {
		this.upgrade_reason_flag = value;
	}

	/** 
	 * 取得詳敘升等理由<p/>
	 * 文字敘述
	 */
	public String getUpgrade_reason_text() {
		return this.upgrade_reason_text;
	}
	/**
	 *  設定詳敘升等理由<p/>
	 *  文字敘述
	 **/
	public void setUpgrade_reason_text(String value) {
		this.upgrade_reason_text = value;
	}

	/** 
	 * 取得詳敘降等理由<p/>
	 * 文字敘述
	 */
	public String getDowngrade_reason_text() {
		return this.downgrade_reason_text;
	}
	/**
	 *  設定詳敘降等理由<p/>
	 *  文字敘述
	 **/
	public void setDowngrade_reason_text(String value) {
		this.downgrade_reason_text = value;
	}

	/** 
	 * 取得升等數<p/>
	 * 等數
	 */
	public Integer getUpgrade_level() {
		return this.upgrade_level;
	}
	/**
	 *  設定升等數<p/>
	 *  等數
	 **/
	public void setUpgrade_level(Integer value) {
		this.upgrade_level = value;
	}

	/** 
	 * 取得降等數<p/>
	 * 等數
	 */
	public Integer getDowngrade_level() {
		return this.downgrade_level;
	}
	/**
	 *  設定降等數<p/>
	 *  等數
	 **/
	public void setDowngrade_level(Integer value) {
		this.downgrade_level = value;
	}

	/** 
	 * 取得文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	public String getDocstatus() {
		return this.docstatus;
	}
	/**
	 *  設定文件狀態<p/>
	 *  編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 
	 * 取得上傳資料日期<p/>
	 * 　
	 */
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/**
	 *  設定上傳資料日期<p/>
	 *  　
	 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}

	/** 取得卡友貸旗標 */
	public String getC_flag() {
		return c_flag;
	}
	/** 設定卡友貸旗標 */
	public void setC_flag(String c_flag) {
		this.c_flag = c_flag;
	}
}
