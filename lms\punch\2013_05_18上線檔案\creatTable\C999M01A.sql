---------------------------------------------------------
-- LMS.C999M01A 個金約據書主檔
---------------------------------------------------------
--DROP TABLE LMS.C999M01A;
CREATE TABLE LMS.C999M01A (
	OID           CHAR(32)      not null,
	UID           CHAR(32)     ,
	MAINID        CHAR(32)     ,
	TYPCD         CHAR(1)      ,
	CUSTID        VARCHAR(10)  ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)      ,
	<PERSON><PERSON><PERSON>NA<PERSON>      VARCHAR(120) ,
	<PERSON><PERSON>TYPE      CHAR(1)      ,
	OWNBRID       CHAR(3)      ,
	DOCSTATUS     VARCHAR(3)   ,
	<PERSON><PERSON>OMCO<PERSON>    CHAR(32)     ,
	DOCURL        VARCHAR(40)  ,
	<PERSON>CO<PERSON>        CHAR(6)      ,
	CREATO<PERSON>       CHAR(6)      ,
	CREA<PERSON><PERSON><PERSON>    TIMESTAMP    ,
	<PERSON><PERSON><PERSON>R       CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>    TIMESTAMP    ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   TIMESTAMP    ,
	<PERSON><PERSON><PERSON><PERSON>      CHAR(1)      ,
	<PERSON><PERSON>TEDTIME   TIMESTAMP    ,
	SRCMAINID     CHAR(32)     ,
	CASEYEAR      DECIMAL(4,0) ,
	CASEBRID      CHAR(3)      ,
	CASESEQ       DECIMAL(5,0) ,
	CASENO        VARCHAR(62)  ,
	CASEDATE      DATE         ,
	CONTRACTTYPE  CHAR(1)      ,
	CONTRACTKIND  CHAR(1)      ,
	CONTRACTTYPE2 CHAR(3)      ,
	CONTRACTWORD  VARCHAR(30)  ,
	CONTRACTNO    VARCHAR(20)  ,
	CONTRACTRATE  DECIMAL(7,4) ,
	DATAUSEFLAG   CHAR(1)      ,
	DATAUSEITEM   DECIMAL(3,0) ,
	COURTCODE     CHAR(3)      ,
	ADDRZIP       DECIMAL(5,0) ,
	ADDRCITY      VARCHAR(12)  ,
	ADDRTOWN      VARCHAR(12)  ,
	ADDR          VARCHAR(300) ,
	AMONTH        DECIMAL(2,0) ,
	ARATE         DECIMAL(2,0) ,
	BMONTH        DECIMAL(2,0) ,
	BRATE         DECIMAL(2,0) ,
	CRATE         DECIMAL(7,4) ,
	RPTID         VARCHAR(32)  ,

	constraint P_C999M01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C999M01A IS '個金約據書主檔';
COMMENT ON LMS.C999M01A (
	OID           IS 'oid', 
	UID           IS 'uid', 
	MAINID        IS '文件編號', 
	TYPCD         IS '區部別', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTNAME      IS '客戶名稱', 
	UNITTYPE      IS '辦理單位類別', 
	OWNBRID       IS '編製單位代號', 
	DOCSTATUS     IS '目前文件狀態', 
	RANDOMCODE    IS '文件亂碼', 
	DOCURL        IS '文件URL', 
	TXCODE        IS '交易代碼', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期', 
	APPROVER      IS '核准人員號碼', 
	APPROVETIME   IS '核准日期', 
	ISCLOSED      IS '是否結案', 
	DELETEDTIME   IS '刪除註記', 
	SRCMAINID     IS '來源文件編號', 
	CASEYEAR      IS '案件號碼-年度', 
	CASEBRID      IS '案件號碼-分行', 
	CASESEQ       IS '案件號碼-流水號', 
	CASENO        IS '案件號碼', 
	CASEDATE      IS '簽案日期', 
	CONTRACTTYPE  IS '約據書種類', 
	CONTRACTKIND  IS '約據書類型', 
	CONTRACTTYPE2 IS '約據書表單', 
	CONTRACTWORD  IS '連保書字號(字)', 
	CONTRACTNO    IS '約據書編號/連保書字號(號)', 
	CONTRACTRATE  IS '基準利率及調整', 
	DATAUSEFLAG   IS '資料保密_是否同意', 
	DATAUSEITEM   IS '資料保密_同意項目', 
	COURTCODE     IS '管轄法院', 
	ADDRZIP       IS 'MIS郵遞區號', 
	ADDRCITY      IS 'MIS地址(縣市)', 
	ADDRTOWN      IS 'MIS地址(區鄉鎮市)', 
	ADDR          IS 'MIS地址', 
	AMONTH        IS '逾期在x個月以內', 
	ARATE         IS '百分之x計付違約金', 
	BMONTH        IS '逾期超過x個月部份', 
	BRATE         IS '百分之x計付違約金', 
	CRATE         IS '計付遲延利息', 
	RPTID         IS 'RPTID'
);
