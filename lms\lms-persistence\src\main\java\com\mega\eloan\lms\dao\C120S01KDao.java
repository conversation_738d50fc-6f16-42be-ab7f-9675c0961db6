/* 
 * C120S01KDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01K;


/** 個金相關查詢主從債務人檔 **/
public interface C120S01KDao extends IGenericDao<C120S01K> {

	C120S01K findByOid(String oid);

	List<C120S01K> findByMainId(String mainId);

	C120S01K findByUniqueKey(String mainId, String custId, String dupNo,
			String lnGeId, String lnGeDupNo);

	List<C120S01K> findByIndex01(String mainId, String custId, String dupNo,
			String lnGeId, String lnGeDupNo);
	
	List<C120S01K> findByCustIdDupId(String custId,String DupNo);

	int deleteByOid(String oid);
}