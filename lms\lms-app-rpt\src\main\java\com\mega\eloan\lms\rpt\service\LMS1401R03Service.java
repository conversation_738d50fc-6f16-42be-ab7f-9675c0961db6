package com.mega.eloan.lms.rpt.service;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;




/**
 * <pre>
 * 案完成未出售房屋融資統計表
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS1401R03Service {
	
	public Map<String, Integer> getTitleMap();
	
	public Map<String, Integer> getHeaderMap();
	
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException;
	
	public int setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException;
	
	public List<Map<String, Object>> getStatisticsData(String startDate, String endDate);
	
	public void setBodyContent(WritableSheet sheet, List<Map<String, Object>> elf517List, int colIndex, int rowIndex) throws RowsExceededException, WriteException;
}
