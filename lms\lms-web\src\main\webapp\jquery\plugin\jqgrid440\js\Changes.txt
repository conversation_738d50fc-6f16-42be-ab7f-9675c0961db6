3.6 version
===========
Bug Fixes
---------
   * Fix problem with getAbsoluteIndex on IE8
   * Fix in triggerReload grid to reset the savedRow array.
   * Fix in setGridWidth  method when try to resize a grid a and there is no data.
   * Fix header aligment in ie8
   * Fix in createEl function to check for val and not for html when select
   * making getChangedCells method compatible with unformat function if defined
   * Fixed record count when scroll true
   * Fix in setSelection method to accept numbers
   * Fix IE header checkbox when multiselect is true
   * Fix for searching DOM elements with id containing special characters
   * Fix a checkbox checking in form editing module.
   * Fix getCol to use unformat function
   * Better looking buttons in form editing
   * Fix searchFilter in IE7 and IE6 not to return undefined value when more 
than one field is searched.
   * Some fixes for datepicker UI in inline and cell edit (when present)
   * Fix in setLabel method not to clear the sort icons when we set new label
   * Fix in form edit when a formatter select is used
   * Fix the id pollution when multiselect is enabled. Now the id of every 
common multiselect is combination of cb_ + id of the grid 
   * Fixed bug when deselectAfterSort false and we are in multiselect mode
   * Fix in unformat number and currency
   * Fixed bug when creating a select with dataUrl and select is set to multiple.
   * Fix a bug when creating select with multiple option. Also the attribute 
should be set immediatley after the element is created and not after.
   * Fix a bug in form edit. The comapared values should be trimed since of 
split(",") which cause the elements to begin with space.
   * Fix a jqGrid loader (development version) for IE browsers
   * Fix for form icons in Opera
   * Fix getGridParam to return values 0 or false 
   * Avoiding the overhead of local functions and maintenance issues when the 
parameters change
   * Opera fix for cursor col-resize
   * Fix in editGridRow when a save key is defined and the key is Eneter we 
should not do save when we are in teaxtarea element
   * Fix in formatter checkbox when the value is undefined. We also add a 
default format for the checkbox too
   * Fix addRowData to use key:true (keyIndex) when rowid is not defined
   * Fix formatter bug for the select to use the defaultFormat
   * Adding a 3 parameter to the unformat function - the cellobject.
   * Fix alternate rows when we sort data locally.
   * Fix in addRowData for alternate rows when we add item as last
   * Fix collapseRow in treegrid preventing infinite recursion
   * Fix in stripHtml function to parse numeric values
   * Fix in delGridRow to get "fresh" Data from delData object
   * Fix problems with width option if set as string.
   * Fixing $jgrid.format function in base module to accept undefined parameter.
   * Fix for saveRow method in aftersave function. Also we pass the request and 
not the responseText. Also both aftersave and  success functions have the 
same type parameter - the response
   * Fix for serchhidden in searchGrid method
   * Fix for setGridWidth method
   * Fix  setColWidth function
   * Fix for topinfo and bottom info in formedit to appear separatley in edit and add dialogs
   * Fix searchFilter css to work when called from subgrid 
   * Fix celledit to work better when datepicker is attached
   * Fix in viewGrid row to vizualize only the columns that only not hidden.

   
Additions and changes
---------------------
   * New API
   * RTL Support
   * Column reordering
   * Added life scrolling of data
   * Added gridResize method which can resize the grid. Also use a jQuery UI 
resizable and can use any options of this widget
   * Added sortableRows method which allow us to sort rows with drag and drop
   * Added gridDnD method. The method connect two or more grids so that drag and drop rows
between these is possible
   * Add columnChooser method which allows choosing/sorting columns at the same 
time. This method requiere multiselect plugin
   * Now getRowData can return all rows when rowid is not set.
   * getCol now can return summary, average and count of the selected row. The 
3 parameter determines this (valid are sum, avg, count). If this parameter 
is set and is valid, the returned value is a scalar.If the param is not 
valid the returned value is empty array. 
   * To setRowData we add a 3 parameter. If the parameter is string we use 
addClass to add classes. If the parameter is object we use css to add css 
properties. Note that we can set properties and classes without data, in 
this case we should set data to false 
   * Adding a event object to ondblClickRow and onRightClickRow events as 4 parameter
   * Add a ant based build system which allow us to minify the JS code
   * Added custom validation property in editing modules
   * Added possibility to create custom element in editing modules
   * Adding support to configure the ajax request in a way that user want. This
is done in all modules where ajax is used.
   * Added class in cell edit and inline edit so that the cell or row can not
be edited - the corresponding classes are 'not-editable-cell' and
'not-editable-row'.
   * Added Hebrew Translation
   * Added a beforeCheckValues event in editGridRow method. The execution is 
before checking the values. Should return object of the posted data.
   * Added Romanian translation 
   * Added a property classes in colModel where we can attach classes to column.
   * info_dialog function can be used as separate modal dialog
   * Added a class ui-ellipsis in grid css file. Also work in FireFox too.
   * Changind the order of calling the events beforeSearch and beforeClear in 
filterToolbar method. Also the events are called immediatley after triggering the grid
   * To onCellSelect we replace the 4 parameter with the more natuaral event instead of e.target - 
sorry if this will bring to some confusions.
   * Added searchoptions value parameter in order to separate the editing module and search module.
We first look for such option in searchoptions and if not found we use the
editoptions value
   * Added fixed property in colModel which allow us not to change the  
column width if shrinkToFit is true and we set new width of the grid.
   * Added buildSelect function to deserialize data for the select. This is option
can be added in editoptions or search options

   
Please read The Installation instructions first in install.txt
================================================
3.5 beta (buld6- build11)

New Properties and methods
==========================

- Added new grid parameter hoverrows. When set to false the mouse hovering is
disabled in the grid data rows.

- Added rowid as parameter to error function in saveRow method - inline 
editing module

- treeGrid to be initially work with datatype local 

- Added new method updateGridRows (grid.custom.js)
This method update the existing data in the grid by given id rowname.

Syntax: updateGridRows (data, rowidname, jsonreader)

data is a array of data in format
[{name:value,name1:value1...},{name:value,name2:value2...}]
where the name is the name from colModel and value is the actuall value.
It is not neccessary that all columns are present in the data item object
(the same as setRowData method)

rowidname (string) - is the name of the row which should acts as id.
If not set the name "id" is used.

jsonreader (boolean) default false. If set to true a jsonReader definition
is used to set the data. Note that this work only of the item in jsonReader
object 'repeatitems' is set to true.

- Switch to namedItems JS function. This improve the speed. Previously we
walk throught the entry rowset to find the rowIndex of the given rowid.
Now we use the build in javaScript method namedItems which do the same.

- multiselect is enabled in inline edit module.

- To the  importComplete event in jqGridImport method we pass the response.

- Added support for alternate rows. Also added new jqGrid option altclass.
The default value is 'ui-priority-secondary' THis way we can develop our own
alternate class.

- Added ShrinkToFit option in setColumns method which fit columns to the grid 
width. Default value is false.

- Extend tableToGrid method to use custom options when building the grid.
The call to this function is
tableToGrid(selector, options)
where options is the object with jqGrid options.

- Adding 4px right to the view records text for better view 

- Add a onClose event to all modals in form editing (except alert dialog)
The default value is null. This event can be passed in editGridRow, delGridRow,
viewGridRow, and setColumns methods. The event raised when a close icon is clicked,
a cancel button is clicked or ESC key is pressed (if this is enabled).

- Significant speed improvents in tree Grid when a relative large data is loaded.

- Added dataheight parameter to all modal windows, where we can control the
scrolling content. Intitially this parameter is auto.
To summarize - we have two parameters for controlling the height of the modal.
height and dataheight.
The height parameter control the height of the entry modal, dataheight parameter
control the height of the scrolling content.  The buttons of the modals are
allways visible at the bottom of the modal.Also some improvments in jqDnR.

- In setColumns method we add colnameview and closeAfterSubmit parameters. The
colnameview (default true). If set to false the names from colModel are not
visible. closeAfterSubmit if defined cleses the dialog after the submit
button is clicked.

- Added parameter viewsortcols in jqGrid parameters. The purpose of this
parameter is to define different look and behavior of sorting icons that
appear near the header. This parameter is array with the following default
options
viewsortcols : [false,'vertical',true]

The first parameter determines if all icons should be viewed at the same time
when all columns  have sort property set to true.
The default of false determines that only the icons of the current sorting column
should be viewed.
Setting this parameter to true causes all icons in all sortable columns to be viewed.

The second parameter determines how icons should be placed - vertical means that
the sorting icons are one under another. 'horizontal' means that the icons should
be one near other.

The third parameter determines the click functionality. If set to true the columns are
sorted if the header is clicked. If set to false the columns are sorted only when
the icons are clicked.

Important note: When set a third parameter to false be a sure that the first parameter
is set to true, otherwise you will lost the sorting.


Fixes
=====
- Fixed bug in filterToolbar. We search now by index and when not found by 
name. 

- Fix in searchGrid method. We search now by index and when not found by 
name. 

- Fixed bug in IE when resizing columns. The function getOffset is changed
so that the resizing speed is the same as in Safari and FF.

- Fix multiple select bug in createEl function. Now we set the options before
checking for multiple:true

- Fixed bug in treegrid. The strict comparation in getNodeParent method
causes bad results.

- Fix in setGridWidth method. We do not initialize correct the new width which
causes after the first set of the width to not work in other grid in the same
page.

- Fixed bug in GridExport. We delete the columns that are generated 
dynamically - multiselect, subgrid, treegrid or rownumbers. This way the
import is without errors 

- Fixed a bug in addRowData method to add data with id = 0 (number)

- Fixed a forever loading bug in IE.

- Fixed altRows bug in delRowData method

- navGrid method fix for warning modal and IE6

- Fix setColumns method to the common apprach of forms and modals

- Fix for IE8 in getAbsoluteIndex method. IE8 seems to work as other browsers

- Fixed bug in delRowData - the grid does not resize when first row is 
deleted.

- Fixed bug when rownumbers and subgrid are used together 

- Fixed bug in row editing when the column name contain dot.

- Fixing bug in viewGridRow - also the id manes of the form are changed in order
to be not equal of those in editGridRow



3.5 beta (buld5)

FormEditing
===========
- added options checkOnUpdate (default false) in editGridRow method.
Also available in add and edit mode.
When this option is set to true the behaviour as follow:
When something is changed in the form and the user click on
Cancel button, navigator buttons, close button (on upper right corner of the
form), in overlay (if available) or press Esc key (if set) a message box apper
asking the user to save the changes, not to save the changes and go back in the
form or cancel all changes (this will close the modal form)
Note that checkOnSubmit option will work only when a submit button is clicked.
When nothing is changed we contionue with the action.

- added option -  rowabove (default false) in colModel formoptions
When set to true a new empty row is added above this row in form editing.
- added option - rowcontent (default empty) in colModel formoptions.
This option have sense only when rowabove option is set to true.
With this option is possible to set a content to the new added empty row in
the form. In order to style easy these rows a class 'contentinfo' is set.

- added new items in the language files to support the checkOnUpdate option

- added class 'delmsg' in the delGridRow method. This allow us to change the
message dynamically using a beforeShow or afterSow eventrs. The class is added
to a element td.

Fixes
=====
- fix in Bulgarian translations files.
- fix in searchGrid method. Now we search by index (from colModel) and only if
it is not available we search by name.
- Fix for date parser in the base module. Now we can sort locally on
dates that contain month names
- Fixed bug in searchGrid. The translation messages for the operations does not 
apper when not set in colModel
- removed some not needed events  from base grid. Also return false do these
by default;
- Fix bug to check if datepicker is available in searchGrid.
- Fix a bug in searchGrid in IE6, where the data value is not set for elements
after the first 
- Fixed bug when calling of jqModal multiple times on the same element. We 
need the initialization only once and not every time. This cause a memory 
leaks
- Fixed bug in formatter where the data containing ' causes break in IE 
- Fixed bug in base module when adding data where the data contain a ' char.
This breaks in IE browsers.
- Fixed bug in checkboxes. We add the value setted in the editoptions. Also 
converting to lower case make the option checkOnUpdate to be true every time. 
- We replace white-space nowrap with pre in td element in the CSS file. This fixes
the bug in IE where the first row does not wrap when the text is longer than
the column width. Also this requiere DOCTYPE declaration in the document.
- Fixed bug in viewGridRow method where when resizing the modal causes the data
to apper bad.


3.5 beta (buld4)
Base
====
- added option footerrow - default false. If set to true this will place a
footer table with one row below the gird records and above the pager.
The number of columns equal of these from colModel

-added option userDataOnFooter(default false) which directly place the user data
array at footer. The rules are as follow: If the userData array contain name
which is equal to those of colModel then the value is placed in that column.
If there are no such values nothing is palced. Note that if this option
is used we use the current formatter options (if available) for that column.

-Added method getCol(colname). Colname can be either number which represents
the index of the column or name from colModel. The method return array with
the values of the coulmn.

- Added method footerData ( action, data, format). This method get or set data
on footer.

action - can be 'get' or 'set'. If not set default is get.
When set to 'get' - the result is object of type name:value, where the name is
a name from colModel. This will return a data from the footer. The other two
options  does not have effect in this case.

When set to 'set' a data array (object) should be set. This will place
the values in the footer.

format - default is true. This instruct the method to use the formmater
(if set in colModel) when a new values are set.
Setting to false will disable the using of formatter.

Related css items in the ui.jqgrid.css
.ui-jqgrid-sdiv - is a div container for the footer
.ui-jqgrid-ftable - is the class for the table
tr.footrow - is the class for the footer row
tr.footrow td is the item for the cell formating

In order to format every cell in a way that you want
maybe you can use

jQuery("tr.footrow td:eq(2)","#gbox_mygrid").css(...)
where the td:eq(2)  point to the third column
and mygrid is the id of the grid. The id of the global grid container
is constructed via "gbox_"+the id of the grid.

There are no methods to show and hide the footer row dynamically, but
following the above example we can do

jQuery(".ui-jqgrid-sdiv","#gbox_mygrid").hide() - this will hide the footer row.

jQuery(".ui-jqgrid-sdiv","#gbox_mygrid").show() - this will show the footer row.


Form Editing
============
aded option in editGridRow - checkOnSubmit (default false)
This option only work in editing mode and not in add.
When set to true - the method looks if any data is changed in the form and
if this is a case a dilog message appear where the user is asked to confirm the
changes or cancel it. Pressing cancel will return to the form, but does not
set the values to its original state.

Note that this causes a some changes in the language file.

Fixes
=====

- Removed imgpath from cell edit and inline edit modules and common module.
This parameter is no longer valid in 3.5
- Replace $ with jQuery in grid.common.js which causes problems when used with
other librariers like prototype.
- removed a checking e.srcElement from base module. jQuery allready fix this.
- fixing the size option in select when using ajax to load selects.
- Fixed bug in IE browsers, when clicked on href tag with child nodes,
IE incorrect report in all cases that this is a href element. This couses
onSelectCell not to behave correct.
- Fixed bug in searchGrid - group options are not passed to serchFilter in 
order get them from Language file.
- Fix in bulgarian trnslations.
- Fix on sorting column. The initial sort check first for index and then for 
column name. Before the checking for the index was missed.
Sortorder converted to lower case in order to avoid some cofusions.
- Fix in set columns module. The modal does not appear correctly due to 
missed parameter 



3.5 beta (buld3)

Base
====
- To the loading message is added ui-state-active class from Theme roller.
This is more natural, indicating that the grid is active when data is loaded.

Form Editing
============
- Added keyboard navigation, which allow us to navigate throught the records while
in form editing pressing certain keys. This option is available in editGridRow
and viewGridRow methods. The default state is disabled. To enable this you
should set the options in the edit/add parameters:

navkeys: [true,38,40]
The first item enables the navigation.
The second item corresponds to reccord up and by default is the  the key code
for Up key
The third item corresponds to reccord down and by default is the  key code for
Down key
Example
$("#mygrid").navGrid("#pager",{options},{id:'myeditid',navkeys: [true,38,40]}..)
Also this option does not work in add mode.

Note: Instead that this does not have sense, if you plan to navigate with keys
it is recommended to add this option in add mode. This should be done
because the form for add and edit is the same and it is unknown which action
will be lunched first.

- Added posibility to save the form pressing certain key - add/edit mode only
By default this option is disabled.
To enable
savekey: [true,13]
The first item enables saving with pressing certain key.
The second item corresponds to key code for saving. Default Enter (key code 13)

Note that this binding should be used for both adding and editing a row.
Since the binding is for the form, there is no possibility to have one
key in add and another in edit mode.

- Added icons for the buttons in the form. The corresponding class in the css
is "fm-button"
By default all icons are enabled.
For add/edit method editGridRow
The save button is defined with
saveicon : [true,"left","ui-icon-disk"]
The close button is defined with
closeicon: [true,"left","ui-icon-close"]

The first item enables the icon
The second item tells where to pu icon left or right
The third item corresponds to valid ui icon from theme roller

For viewGrid Row
closeicon : [true,"left","ui-icon-close"]

For Deleting
delicon: [true,"left","ui-icon-scissors"]
cancelicon: [true,"left","ui-icon-cancel"]

Fixes
======
-Fixed bug when getting the index of the row. Strict compare should be removed.
-Fixed bug when datatype is function - we need to exit when call it and not to 
continue.
-Fixed bug in form editing - the buttons do not align properly.

3.5 beta (build2)

Navigator
=========
This was missed in the first build
In the navGrid we can pass uniquie id's of the buttons in order to manipulate
them easy. If no id is set we create one which is combination of:
for add button "add_"+ the id of the grid
for edit button "edit_"+ the id of the grid
for view button "view_" + the id of the grid
for delete button "del_" + the id of the grid
for search button "search_" + the id of the grid
for refresh button "refresh_" + the id of the grid
Example:
$("#mygrid").navGrid("#pager",{options}, {id:'myeditid'},{id:'myaddid'}...)

Base Grid
=========
- Record number information now can format the values according to the 
formatter setting (if available) getting information from language file.

Fixes
=====

- Fixed bug when multi select with json. We pass wrong parameter to addMulti 
function 
- Spelling error in all language files, which cause not to set the default 
value correct
- Fixed bug when creating warning message. Fixed bug when determining the 
number column element in the form
- Fixed bug - clientSide datatype should be converted to lower case
according to the new improvements 
- Fixed bug in IE6/7 when try to select a option in the select generated via 
ajax 

===============================
Upgrade from 3.4.x to 3.5 beta.
===============================


1. Installation
--------------
See install.txt

2. Code changes
---------------
2.1 The following grid options are deprecated - i.e. you can remove them from
the grid options. They are not needed in 3.5 version

imgpath
sortascimg
sortdescimg
firstimg
previmg
nextimg
lastimg
sortclass
resizeclass

2.2 Second change is related to custom formatters. Starting with version 3.5,
the custom formatter is passed the following parameters:

formatter : function ( cellvalue, options, rowObject )
{
// format the cellvalue to new format
return new_formated_cellvalue;
}

Note the return in the function. This function should always return a value
in order to work correctly. The parameters are

cellvalue - is the value to be formatted
options - is an object containing the following element

options :  { rowId: rid, colModel: cm}
where rid - is the id of the row
colModel is the object of the properties for this column getted from colModel
array of jqGrid

rowObject - is a row data represented in the format determined from datatype
option.
If we have datatype: xml/xmlstring - the rowObject is xml node,provided
according to the rules from xmlReader
If we have datatype: json/jsonstring - the rowObject is array, provided
according to the rules from jsonReader

Example:
Code in 3.4.x variant

formatter : function ( elem, cellvalue, options)
{
var newval = "<strong>"+cellvalue+"</strong>";
jQuery(elem).html(newval);
}


Code in 3.5

formatter : function ( cellvalue, options, rowObject )
{
return "<strong>"+cellvalue+"</strong>";
}


Base Module
============
Bug fixes
---------
- fixed bug in onCellSelect event when used in IE browsers - now the rowIndex
passed to this event is calculated correctly regardless of hidden columns.

- fixed width bug when multiselect is used. Previously the width of this column
is recalculated according to table width and when shrinkToFit is set to true.
Now the column has fixed width and is no longer recalculated when the grid
width is changed. See documentation for the new options

- fixed width bug when subgrid is used. Previously the width of this column is
recalculated according to table width and when shrinkToFit is set to true. Now
the column has fixed width and is no longer recalculated when the grid width
is changed. See documentation for the new options

- fixed bug in addRowData method when the grid is empty and we try to add one
or more columns. The width of the data was not being recalculated correctly,
according to the header elements.

- fixed bug when we use xmlstring and jsonstring to add data. Previously all
the data from the string is loaded without reference to the rowNum parameter.
Also this behaviour is changed for xml and json data. In other words, we now
load only rowNum number of rows even when the request consists of more rows.

- fixed bug when posting values of input of type select set to multiple:true.
Now in all editing modules the posted values in for multiselect are separated
with comma 
 
- fixed bug in the size option of multi select. If the size option is not set
for multiselect the default value is 3

Deprecated options
----------------
imgpath: "", in all modules
sortascimg: "sort_asc.gif",
sortdescimg: "sort_desc.gif",
firstimg: "first.gif",
previmg: "prev.gif",
nextimg: "next.gif",
lastimg: "last.gif",
sortclass: "grid_sort",
resizeclass: "grid_resize",

Note that imgpath is not used anymore in all modules. 
The altRows option is still here, but will have no effect until the jQuery
UI Theme roller supports alternative rows.


Note that the table element where we define the grid can not have a class
scroll - i.e. class scroll in the table definition can be omitted, but this
is NOT true for the pager element.

New options
-----------
datatype can now set as 'script'. In other words, we now support script
as datatype.

reccount: numeric, Default 0. Determines the exact number of available
grid rows. Can be <= from rowNum parameter

autowidth: boolean, Default false. When set to true, the grid width is
recalculated automatically to the width of the parent element. This is done
only initially when the grid is created. In order to resize the grid when
the parent element changes width you should apply custom code and use a
setGridWidth method for this purpose.

scrollOffset: numeric, Default 18. Determines the width of the vertical
scrollbar. Since different browsers interpret this width differently (and it
is difficult to calculate it in all  browsers) this can be changed.

cellLayout: numeric, Default 5. This option determines the padding + border
width of the cell. Usually this should not be changed, but if custom changes
to td element are made in the grid css file this will need to be changed.

subGridWidth: numeric, Default 20. Determines the width of the subgrid column
if the subGrid option is set to true.

multiselectWidth: numeric, Default 20. Determines the width of the multiselect
column if multiselect is set to true.

rownumbers: boolean, Default false. If this option is set to true, a new column
at left of the grid is added. The purpose of this column is to count the number
of available rows, beginning from 1. In this case colModel is extended
automatically with new element with name - 'rn'. Also, be careful not to use
the name 'rn' in colModel

rownumWidth: numeric, Default 25. Determines the width of the row number column
if rownumbers option is set to true.

pagerpos: string, Default 'center'. Determines the position of the pager in the
grid. By default the pager element when created is divided in 3 parts (one part
for pager, one part for navigator buttons and one part for record information)

recordpos: string, Default 'right'. Determines the position of the record
information in the pager.

toolbar now accepts another option: "both". When we set toolbar: [true,"both"]
two toolbars are created -- one on the top of table data and one of the bottom
of the table data. When we have two toolbars then we create two elements (div).
The id of the top bar is constructed like "t_"+id of the grid and the bottom
toolbar the id is "tb_"+id of the grid. In case when only one toolbar is
created we have the id as "t_" + id of the grid, independent of where this
toolbar is created (top or bottom)

gridview: boolean, Default value false. In the previous versions of jqGrid,
including 3.5 alfa, reading a relatively big data sets (Rows >=100 ) caused
speed problems. The reason for this was that as every cell was inserted into
the grid we applied about 5-6 jQuery calls to it. Now this problem is resolved;
we now  insert the entry row at once with a jQuery append. The result is
impressive - about 3-5 times faster. What will be the result if we insert all
the data at once? Yes, this can be done with a help of gridview option when set
to true. The result is a grid that is 5 to 10 times faster. Of course when this
option is set to true we have some limitations. If set to true we can not use
- treeGrid
- subGrid, or 
- afterInsertRow event.
If you do not use these three options in the grid you can set this option to
true and enjoy the speed.

addRowData now is 1-2 times faster because we are using the concept to insert
the row at once

Added jQuery function parse (removed from json2.js module):
jQuery.parse(jsonstring)
To prevent JavaScript hijacking attacks, web application authors are encouraged
to use this function. This returns a JSON representation of the variable. 

Cyclic structures are detected and safely interrupted, but could not be further
restored.
1. The while(1); construct, located at the beginning of JSON text,
2. Comments at the beginning and end of the text.
JSON data providers are encouraged to use one or both of these methods to
prevent data execution. Such JSON response may then look like this: 
while(1);/*{[
    {"name":"safe value 1"},
    {"name":"safe value 2"},
   ...
]}*/
jqGrid uses this function when obtaining a data with datatype:'json', also used
in subGrid.

Added jQuery function stringToDoc: jQuery.stringToDoc(xmlstring). This returns
the xml document of xml string

Added new method: jQuery("#mygrid").updateColumns(); this synchronizes the
width of the headers with the data. Useful when used with table drag and drop.


colModel changes and additions
------------------------------
searchhidden is removed from editrules object

added searchoptions object to define properties, events and other settings when
a search is performed
searchoptions : {
    dataUrl : value,
    dataInit: function,
    dataEvents : array,
    defaultValue: value,
    attr: object,
    searchhidden: boolean,
    sopt: array
}
To summarize - when we want to deal with search we should use the following
options in colModel

search : boolean , // true/false
stype: value, // text/select
searchoptions : object // see above

Note: when the dataUrl is not used for the search type select, the definitions
for the select are taken from editoptions value property - i.e
editoptions:{value:"1:one;2:two",...}. See below for how to use these options in
different search methods.


Added new object formoptions to define various settings in form editing. Also
valid only in this module.

formoptions : {
    elmprefix:value,
    elmsuffix:value,
    rowpos: number,
    colpos:number,
    label: value
}
See below for how to use

The editoptions are extended with the following properties. Valid in all editing
modules editoptions : {
    dataUrl : value, dataInit: function, dataEvents : array, defaultValue:
    value/function,
} See below


Changes in all editing modules
==============================

edittype property is extended with the support of the following input elements:
button, image and file

As described above in all editing modules we can add additional properties and
events to the editing element:

editoptions : {
    dataUrl : value, dataInit: function, dataEvents : array, defaultValue:
    value/function
}
-------------------------------------------------------------------------------
dataUrl option is valid only for the elements of type select - i.e.,
edittype:select

When this option is set, the element will be filled with values from the ajax
request. The data should be a valid html select element with the desired
options. <select>
    <option value="1">One</option> <option value="2">Two</option>
</select>

In form editing for add/edit this is called only once. In order to call it every
time the form is launched you should set the option recreateForm:true or use
other events like beforeShowForm (with another custom ajax call) to fill the
desired element.

In inline edit and cell edit this is fired every time you edit the row or cell
element.
--------------------------------------------------------------------------------
dataInit : function (elem) {
// do something
}

If set, this function is called only once ? when the element is created. The
function is passed the specified element. This option is ideal for attaching
date picker, time picker, etc. Example:

dataInit : function (elem) {
    $(elem).datepicker();
}
--------------------------------------------------------------------------------
dataEvents: list of events to apply to the data element; uses
$("#id").bind(type, [data], fn) to bind events to data element

Should be described like this dataEvents: [
    { type: 'click', data: { i: 7 }, fn: function(e) { console.log(e.data.i); }
    }, { type: 'keypress', fn: function(e) { console.log('keypress'); } }
]

The event(s) are called only once when the element is created
-------------------------------------------------------------------------------
defaultValue: 'value' or function This option is valid only in Form Editing
module when used with editGridRow method in add mode.If set this the input
element is set with this value if only element is empty. If used in selects the
text should be provided and not the key. Also when a function is used the
function should return value.

Now we can validate a url. The corresponding settings is in colModel
editrules:{required:true, url:true}


FormEditing
===========
In all methods that deal with the modals are added the following options (thanks
to Faserline Team: www.faserline.com)

jqModal: true. The default value is true. How it works. If set to true, first
jqGrid checks if the jqModal plugin is available. If this plugin is available we
create the modal according to the jqModal plugin. If this plugin is not
available jqGrid create its own modal. The difference between the jqModal window
and jqGrid modal window is that the overlay covers only the grid, but not the
entry page as jqModal does. If this option is set to false, jqGrid will create
its own modal as described above even if jqModal is available.

The only exception here is searchGrid method created with the new searchFilter
plugin, where this option is not available. In this method a jqGrid modal window
is used.

closeOnEscape : false. Default false. When set to true the modal window can be
closed with ESC key.



=======searchGrid==================
From now on jqGrid use a new search engine
thanks to wonderful plugin provided from Kasey Speakman (<EMAIL>).
This plugin can also be used separately. Detailed documentation is provided in
the jquery.searchFilter.js which is provided with jqGrid package.

These options are not used anymore in the searchGrid method
----------------------------------------------------------
top : 0,
left: 0,
width: 360,
height: 80,
modal: false,
closeicon: 'ico-close.gif',
dirty: false,
processData: "",

Added the following options
---------------------------

recreateFilter: false, This option construct the filter every time a search
button is clicked.

multipleSearch : false, When set to false the behaviour of the search is the
same as those in versions before 3.5 When set to true: 1. We have a possibility
to add/delete as many conditions as we want 2. The posted data to the server is
string (actually a json array converted to string)

sFilter: 'filters', This option has an effect only when the multipleSearch is
set to true; it describes the name of the posted string. By default this, i.e.
the posted data, can look like this

filters ={
 "groupOp":"AND","rules":[{"field":"id","op":"gt","data":"1"},
{"field":"name","op":"bw","data":"Client"}]
}

groupOp determines the logical operator that can be used in the where statement
of the select. The value can be AND or OR. This can be changed used a select box
which is created at bottom of the search filter.

The rules array describes actually which fields and how they are included in the
where clause The field element describes the name of the field The op element
describes the operator that should be used in the search The data element is the
data that we want to search.

The above example (if getted and constructed correctly) should generate the
following where clause

SELECT .... FROM table,... WHERE id > 1 AND name LIKE 'Client%'
-------------------------------------------------------------------------------
As described above the search is constructed according to the searchoptions in
colModel

search : boolean , // true/false stype: value, // text/select

searchoptions : {
    dataUrl : value,
    dataInit: function,
    dataEvents : array,
    attr: object,
    searchhidden: boolean,
    sopt: array
}
Note that here is missing the defaultValue. The option is not implemented in
this method.


stype determines the search type - can be select or text. If not set a text is
used

searchoptions
-------------------------------------------------------------------------------
dataUrl option is valid only for the elements of type select, i.e.
edittype:select

When this option is set the element will be filled with values from the ajax
request. The data should be a valid html select element with the desired
options. <select>
    <option value="1">One</option> <option value="2">Two</option>
</select>

This is called only once.

-------------------------------------------------------------------------------
dataInit : function (elem) {
// do something
}

If set this function is called only once when the element is created. To the
function is passed the element. Also this option is ideal for attaching date
picker, time picker and etc. Example

dataInit : function (elem) {
    $(elem).datepicker();
}
--------------------------------------------------------------------------------
dataEvents: list of events to apply to the data element; uses
$("#id").bind(type, [data], fn) to bind events to data element

Should be described like this
dataEvents: [
{ type: 'click', data: { i: 7 }, fn: function(e) { console.log(e.data.i); }},
{ type: 'keypress', fn: function(e) { console.log('keypress'); } }
]

The event(s) are called only once when the element is created
--------------------------------------------------------------------------------
attr is object where we can set valid attributes to the created element

attr : { title: "Some title" }
------------------------------------------------------------------------------
searchhidden : if set to true, the hidden element will appear in the search.
-------------------------------------------------------------------------------
array that correspond to the op element in the posted data and determines the
operation. If not set all the available options will be used; if set only the
set values will appear as selectboxes.

All available option are sopt:
['eq','ne','lt','le','gt','ge','bw','bn','in','ni','ew','en','cn','nc']

The corresponding texts are in language file and mean the following ['equal',
'not equal', 'less', 'less or equal','greater','greater or equal', 'begins
with','does not begin with','is in','is not in','ends with','does not end
with','contains','does not contain'],

Note that the elements in sopt array can be mixed in any order.


=========add/edit form==========
In the editGridRow method the following options are no longer used

closeicon: 'ico-close.gif',
imgpath: ''

Added new property formoptions (in colModel); thanks to Faserline Team
(www.faserline.com)

The purpose of these options is to reorder the elements in the form and to add
some information before and after the editing element.

formoptions : {
    elmprefix:'',
    elmsuffix:'',
    rowpos:number,
    colpos:number,
    label: value
}

If you go to use this object in collModel it is recommended that all editing
fields use this property and with at least the rowpos and colpos set.

Elemprefix: if set, a text or html content appears before the input element

Elemsuffix: if set, a text or html content appears after the input element

Label: if set, this replace the name from colNames array that appears as label
in the form.

rowpos : number determines the row position of the element (again with the
text-label) in the form; the count begins from 1

colpos : number determines the column position of the element (again with the
label) in the form beginning from 1

Two elements can have equal row position, but different column position. This
will place the two elements in one row on the form.

=========== delete=================================
In the delGridRow method the following options are no longer used

closeicon: 'ico-close.gif',
imgpath: '',



============= view =============================
Added viewGridRow method; thanks to Faserline Team (www.faserline.com)

This method is similar to the editGridRow method except that the fields are not
editable and we do not have any events attached to the form. Here are the
options viewGridRow (options) where
options = {
    top : 0,
    left: 0,
    width: 0,
    height: 0,
    modal: false,
    drag: true,
    closeOnEscape : false,
    labelswidth: '30%'
}


All the options have the same thing as these in editGridRow except the option
labelswidth which default value is 30%. Since we construct the view with table
element it is difficult to calculate, in this case, how much width is needed for
the labels. Depending on the needs this value can be increased or decreased

Custom method
=============
In the custom module is added new method filterToolbar.

This method is the same as filterGrid, except that the search input elements are
placed in the grid just below the header elements. When the header elements are
resized the input search elements are also resized according to the new width.
Another difference to the filterGrid is that the filter toolbar uses definitions
from colModel just like the searchGrid

Should be called like this

jQuery("#mygrid").filterToolbar(options)

where options are

options = {
    autosearch: true,
    beforeSearch: null,
    afterSearch: null,
    beforeClear: null,
    afterClear: null,
    searchurl : ''
}

autosearch: true Search is performed according to the following rules For text
element when a Enter key is pressed while inputting values a search is
performed. For select element when the value changes.

beforeSearch - event which fires before a search

afterSearch - event which fires after a search

beforeClear - event which fires before clearing entered values (i.e.,
clearToolbar is activated)

afterClear - event which fires after clearing entered values (i.e., clearToolbar
is activated)

The rules of creating toolbar search input elements are the same as searchGrid
using the colModel
searchoptions : {
    dataUrl : value,
    dataInit: function,
    dataEvents : array,
    defaultValue: value,
    attr: object
}

Note the missing sopt element, but addition of defaultValue property

This method has 3 new additional methods that can be used:

triggerToolbar - when called perform a search dynamically

clearToolbar - when called clear the search values and set the default one if
available

toggleToolbar - toggles the searchbar

A typical call to these methods

var sgrid = $("#mygrid")[0] sgrid.triggerToolbar();

will trigger the search of mygrid.


Import module
=============
added option importComplete event which fires after constructing the grid.


Inline edit
===========
added afterrestorefunc as 9th parameter. The event fires after the row is
restored

treeGrid
========
the tree icons can be changed using the following grid parameter treeIcons. The
default values are.
treeIcons ={
    plus:'ui-icon-triangle-1-e',
    minus:'ui-icon-triangle-1-s',
    leaf:'ui-icon-radio-off'
};
Note that icons from UI Theme should be used.

formatter
=========

- the source format for date formatter (srcformat) now can accept month names
- it is possible to enable checkboxes as editable when created with formatter In
  the format options we should in this case checkbox : {disabled:false} The
  default is false. To enable this option globally you can change this value in
  the language file

- the showLink now can accept custom name for the id The parameter is idName :
  'id' The default is id. Can be changed in formatoptions or in the language
  file.

