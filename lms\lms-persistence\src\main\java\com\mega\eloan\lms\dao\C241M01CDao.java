/* 
 * C241M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C241M01C;

/** 一般/團貸覆審項目檔 **/
public interface C241M01CDao extends IGenericDao<C241M01C> {

	C241M01C findByOid(String oid);

	List<C241M01C> findByMainId(String mainId);

	C241M01C findByUniqueKey(String mainId, String custId, String dupNo,
			String itemNo);

	List<C241M01C> findByIndex01(String mainId, String custId, String dupNo,
			String itemNo);

	C241M01C findByIndex02(String mainId, String custId, String dupNo,
			int itemSeq);
	List<C241M01C> findByCustIdDupId(String custId,String DupNo);
	
}