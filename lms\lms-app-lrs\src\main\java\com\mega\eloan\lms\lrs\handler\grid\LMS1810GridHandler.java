package com.mega.eloan.lms.lrs.handler.grid;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lrs.service.LMS1815Service;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 覆審控制檔維護交易
 * </pre>
 * 
 * @since 2011/9/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/21,irene,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1810gridhandler")
public class LMS1810GridHandler extends AbstractGridHandler {

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branch;

	@Resource
	RetrialService retrialService;

	@Resource
	LMS1815Service service;

	@Resource
	EloandbBASEService eloandbBASEService;

	/**
	 * 查詢Grid 覆審名單檔 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapMapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {

		// 建立主要Search 條件
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String custId = Util.nullToSpace(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l181a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (RetrialDocStatusEnum.已核准.getCode().equals(docStatus)) {
			String branch = Util.trim(params.getString("branch"));
			Date beforeDate = TWNDate.valueOf(Util.nullToSpace(params
					.getString("beforeDate", "0001-01-01")));
			Date afterDate = TWNDate.valueOf(params.getString("afterDate",
					TWNDate.toFullAD(new Date())));
			//新增日期轉型
			Date startTime = CapDate.parseDate(TWNDate.toAD(beforeDate) + " 00:00:00");
			Date endTime = CapDate.parseDate(TWNDate.toAD(afterDate) + " 23:59:59");
			Object[] reason = { startTime, endTime };
			if (Util.isNotEmpty(branch)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"elfBranch", branch);
			}
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", reason);
		}
		pageSetting.addOrderBy("elfBranch");
		pageSetting.addOrderBy("approveTime");
		pageSetting.addOrderBy("custId");
		pageSetting.addOrderBy("dupNo");

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Page<? extends GenericBean> page = service.findPage(L181M01A.class,
				pageSetting);
		List<L181M01A> src_list = (List<L181M01A>) page.getContent();
		for (L181M01A l181m01a : src_list) {
			Map<String, Object> row = new HashMap<String, Object>();
			// ---
			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			LMSUtil.meta_to_map(row, l181m01a, new String[] { "elfBranch",
					"custName", "oid", "mainId", "ctlType" });

			IBranch ibranch = branch.getBranch(l181m01a.getElfBranch());
			if (ibranch != null) {
				row.put("elfBranch",
						l181m01a.getElfBranch() + " " + ibranch.getBrName());
			}

			row.put("custId",
					Util.trim(l181m01a.getCustId()) + " "
							+ Util.trim(l181m01a.getDupNo()));
			row.put("updater",
					Util.trim(userservice.getUserName(l181m01a.getUpdater())));
			// ---
			String elfRCkdLine = "";
			String elfLRDate = "";
			String elfNCkdMemo = "";
			String ndDate = "";
			L181M01B af = retrialService.findL181M01B_mainid_Bf(l181m01a
					.getMainId());
			if (af != null) {
				elfRCkdLine = Util.trim(af.getElfRCkdLine());
				elfLRDate = Util.trim(TWNDate.toAD(af.getElfLRDate()));
				elfNCkdMemo = Util.trim(af.getElfNCkdMemo());
				ndDate = Util.trim(TWNDate.toAD(af.getNdDate()));
			}
			row.put("elfRCkdLine", elfRCkdLine);
			row.put("elfLRDate", elfLRDate);
			row.put("elfNCkdMemo", elfNCkdMemo);
			row.put("ndDate", ndDate);
			// ---
			list.add(row);
		}

		// Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
		// pageSetting);
		// return new CapMapGridResult(page.getContent(), page.getTotalRow());
		// src_list.size()
		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, page.getTotalRow(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());

	}

	public CapMapGridResult query_elfFcrdGrad(ISearch pageSetting,
			PageParameters params) throws CapException {
		String fcrdType = Util.trim(params.getString("fcrdType"));
		String fcrdArea = Util.trim(params.getString("fcrdArea"));
		String fcrdPred = Util.trim(params.getString("fcrdPred"));
		String queryType = Util.trim(params.getString("queryType"));
		boolean show = true;
		if (Util.equals(queryType, "L140") || Util.equals(queryType, "L140S08")) {
			show = false; // 額度明細表來查不要有後面的數字

			// J-114-XXX1 LGD合格保證人納入境外保證人
			if (Util.equals(queryType, "L140S08")) {
				show = true;
			}

			// 轉換成一碼的 cardType
			if (Util.equals(fcrdType, UtilConstants.Casedoc.CrdType.SAndP)) {
				fcrdType = "1";
			} else if (Util.equals(fcrdType,
					UtilConstants.Casedoc.CrdType.MOODY)) {
				fcrdType = "2";
			} else if (Util.equals(fcrdType,
					UtilConstants.Casedoc.CrdType.Fitch)) {
				fcrdType = "3";
			} else if (Util
					.equals(fcrdType, UtilConstants.Casedoc.CrdType.中華信評)) {
				fcrdType = "4";
			} else if (Util.equals(fcrdType,
					UtilConstants.Casedoc.CrdType.FitchTW)) {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				fcrdType = "5";
			} else if (Util
					.equals(fcrdType, UtilConstants.Casedoc.CrdType.KBRA)) {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				fcrdType = "6";
			}
		}

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		String chooseKey = retrialService.get_lrs_FcrdGrad_key(fcrdType,
				fcrdArea, fcrdPred);
		Map<String, String> elfFcrdGradMap = retrialService
				.get_codeTypeWithOrder(chooseKey, "zh_TW");
		for (String k : elfFcrdGradMap.keySet()) {
			String v = elfFcrdGradMap.get(k);
			Map<String, Object> row = new HashMap<String, Object>();
			// ---
			row.put("ratingGrad", k + (show ? ("|" + v) : ""));
			// ---
			list.add(row);
		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryL120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));
		String oid = Util.trim(params.getString("oid"));
		L181M01A l181m01a = retrialService.findL181M01A_oid(oid);

		String custId = l181m01a.getCustId();
		String dupNo = l181m01a.getDupNo();
		String brId = l181m01a.getElfBranch();

		Page<Map<String, Object>> page = null;

		List<Map<String, Object>> beanList = eloandbBASEService
				.findLMSL120M01A(brId, custId, dupNo);

		int start = pageSetting.getFirstResult();
		int pagNumber = pageSetting.getMaxResults();
		int end = start + pagNumber > beanList.size() ? start
				+ (beanList.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				beanListnew, beanList.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

}
