package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;


/**
 * <pre>
 * 決策平台 電文回傳
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2021/7/2,007625,new
 * </ul>
 * @since 2021/7/2
 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S19A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S19A extends GenericBean implements IDataObject, IDocObject {
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 文件編號
	 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 項目類別
	 **/
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/**
	 * 對應到的額度明細表mainId
	 */
	@Column(name = "REF140MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String ref140MainId;

	/**
	 * 內容
	 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "JSONDATA", columnDefinition = "CLOB")
	private String jsonData;


	@Digits(integer = 5, fraction = 0)
	@Column(name = "ITEMVERSION", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal itemVersion;
	
	/**
	 * AO修改額度
	 **/
	@Column(name = "AOUPDAMOUNT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal aoUpdAmount;
	
	/**
	 * AO修改額度備註
	 **/
	@Column(name = "AOUPDAMOUNTREMARK", columnDefinition = "VARCHAR(300)")
	private String aoUpdAmountRemark;
	
	/**
	 * 審查修改額度
	 **/
	@Column(name = "RVUPDAMOUNT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal rvUpdAmount;
	
	/**
	 * 審查修改額度備註
	 **/
	@Column(name = "RVUPDAMOUNTREMARK", columnDefinition = "VARCHAR(300)")
	private String rvUpdAmountRemark;
	
	
	/**
	 * AO修改期間-年
	 **/
	@Column(name = "AOUPDYEAR", columnDefinition = "DECIMAL(3,0)")
	private Integer aoUpdYear;
	
	/**
	 * AO修改期間-月
	 **/
	@Column(name = "AOUPDMONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer aoUpdMonth;

	/**
	 * AO修改期間備註
	 **/
	@Column(name = "AOUPDPERIODREMARK", columnDefinition = "VARCHAR(300)")
	private String aoUpdPeriodRemark;
	
	/**
	 * 審查修改期間-年
	 **/
	@Column(name = "RVUPDYEAR", columnDefinition = "DECIMAL(3,0)")
	private Integer rvUpdYear;
	
	/**
	 * 審查修改期間-月
	 **/
	@Column(name = "RVUPDMONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer rvUpdMonth;
	
	/**
	 * 審查修改期間備註
	 **/
	@Column(name = "RVUPDPERIODREMARK", columnDefinition = "VARCHAR(300)")
	private String rvUpdPeriodRemark;
	
	/**
	 * AO修改利率
	 **/
	@Column(name = "AOUPDRATE", columnDefinition = "VARCHAR(3000)")
	private String aoUpdRate;
	
	/**
	 * AO修改利率備註
	 **/
	@Column(name = "AOUPDRATEREMARK", columnDefinition = "VARCHAR(300)")
	private String aoUpdRateRemark;
	
	/**
	 * 審查修改利率
	 **/
	@Column(name = "RVUPDRATE", columnDefinition = "VARCHAR(3000)")
	private String rvUpdRate;
	
	/**
	 * 審查修改利率備註
	 **/
	@Column(name = "RVUPDRATEREMARK", columnDefinition = "VARCHAR(300)")
	private String rvUpdRateRemark;
	
	/**
	 * AO修改額度幣別
	 **/
	@Column(name = "AOUPDAMOUNTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String aoUpdAmountCurr;
	
	/**
	 * 審查修改額度幣別
	 **/
	@Column(name = "RVUPDAMOUNTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String rvUpdAmountCurr;
	
	/**
	 * 建立人員號碼
	 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/**
	 * 建立日期
	 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/**
	 * 異動人員號碼
	 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/**
	 * 異動日期
	 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;


	public L120S19A(String mainId, String itemType, String ref140MainId, String jsonData, BigDecimal itemVersion, String creator) {
		this.mainId = mainId;
		this.itemType = itemType;
		this.ref140MainId = ref140MainId;
		this.jsonData = jsonData;
		this.itemVersion = itemVersion;
		this.creator = creator;
		this.createTime = new Timestamp(System.currentTimeMillis());
	}

	public L120S19A() {

	}

	@Override
	public String getOid() {
		return oid;
	}

	@Override
	public void setOid(String oid) {
		this.oid = oid;
	}

	@Override
	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getItemType() {
		return itemType;
	}

	public void setItemType(String itemType) {
		this.itemType = itemType;
	}

	public String getJsonData() {
		return jsonData;
	}

	public void setJsonData(String jsonData) {
		this.jsonData = jsonData;
	}

	public BigDecimal getItemVersion() {
		return itemVersion;
	}

	public void setItemVersion(BigDecimal itemVersion) {
		this.itemVersion = itemVersion;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getRef140MainId() {
		return ref140MainId;
	}

	public void setRef140MainId(String ref140MainId) {
		this.ref140MainId = ref140MainId;
	}

	public BigDecimal getAoUpdAmount() {
		return aoUpdAmount;
	}

	public void setAoUpdAmount(BigDecimal aoUpdAmount) {
		this.aoUpdAmount = aoUpdAmount;
	}

	public String getAoUpdAmountRemark() {
		return aoUpdAmountRemark;
	}

	public void setAoUpdAmountRemark(String aoUpdAmountRemark) {
		this.aoUpdAmountRemark = aoUpdAmountRemark;
	}

	public BigDecimal getRvUpdAmount() {
		return rvUpdAmount;
	}

	public void setRvUpdAmount(BigDecimal rvUpdAmount) {
		this.rvUpdAmount = rvUpdAmount;
	}

	public String getRvUpdAmountRemark() {
		return rvUpdAmountRemark;
	}

	public void setRvUpdAmountRemark(String rvUpdAmountRemark) {
		this.rvUpdAmountRemark = rvUpdAmountRemark;
	}

	public String getAoUpdPeriodRemark() {
		return aoUpdPeriodRemark;
	}

	public void setAoUpdPeriodRemark(String aoUpdPeriodRemark) {
		this.aoUpdPeriodRemark = aoUpdPeriodRemark;
	}

	public String getRvUpdPeriodRemark() {
		return rvUpdPeriodRemark;
	}

	public void setRvUpdPeriodRemark(String rvUpdPeriodRemark) {
		this.rvUpdPeriodRemark = rvUpdPeriodRemark;
	}

	public String getAoUpdRate() {
		return aoUpdRate;
	}

	public void setAoUpdRate(String aoUpdRate) {
		this.aoUpdRate = aoUpdRate;
	}

	public String getAoUpdRateRemark() {
		return aoUpdRateRemark;
	}

	public void setAoUpdRateRemark(String aoUpdRateRemark) {
		this.aoUpdRateRemark = aoUpdRateRemark;
	}

	public String getRvUpdRate() {
		return rvUpdRate;
	}

	public void setRvUpdRate(String rvUpdRate) {
		this.rvUpdRate = rvUpdRate;
	}

	public String getRvUpdRateRemark() {
		return rvUpdRateRemark;
	}

	public void setRvUpdRateRemark(String rvUpdRateRemark) {
		this.rvUpdRateRemark = rvUpdRateRemark;
	}

	public String getAoUpdAmountCurr() {
		return aoUpdAmountCurr;
	}

	public void setAoUpdAmountCurr(String aoUpdAmountCurr) {
		this.aoUpdAmountCurr = aoUpdAmountCurr;
	}

	public String getRvUpdAmountCurr() {
		return rvUpdAmountCurr;
	}

	public void setRvUpdAmountCurr(String rvUpdAmountCurr) {
		this.rvUpdAmountCurr = rvUpdAmountCurr;
	}

	public Integer getAoUpdYear() {
		return aoUpdYear;
	}

	public void setAoUpdYear(Integer aoUpdYear) {
		this.aoUpdYear = aoUpdYear;
	}

	public Integer getAoUpdMonth() {
		return aoUpdMonth;
	}

	public void setAoUpdMonth(Integer aoUpdMonth) {
		this.aoUpdMonth = aoUpdMonth;
	}

	public Integer getRvUpdYear() {
		return rvUpdYear;
	}

	public void setRvUpdYear(Integer rvUpdYear) {
		this.rvUpdYear = rvUpdYear;
	}

	public Integer getRvUpdMonth() {
		return rvUpdMonth;
	}

	public void setRvUpdMonth(Integer rvUpdMonth) {
		this.rvUpdMonth = rvUpdMonth;
	}
}
