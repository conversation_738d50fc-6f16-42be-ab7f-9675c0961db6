package com.mega.eloan.lms.las.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import com.iisigroup.cap.component.PageParameters;

import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.LasDocStatusEnum;
import com.mega.eloan.lms.las.panels.LASCOMMON01Panel;

/**
 * 分行 稽核工作底稿 已覆核
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1945v04")
public class LMS1945V04Page extends AbstractEloanInnerView {

	public LMS1945V04Page() {
		super();
	}


	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(LasDocStatusEnum.分行_已核准);
		// 加上Button
//		UPGRADE：
//		TRANSACTION_CODE在已註解的原LasButtonPanel中，雖引入但並未影響邏輯
//		下列model.put()僅留著以免日後需要TRANSACTION_CODE做邏輯調整用
//		model.put("transactionCode", params.getString(EloanConstants.TRANSACTION_CODE));
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.PrintAllAudit, LmsButtonEnum.PrintAllBill);

		renderJsI18N(LMS1945V01Page.class);
		renderJsI18N(LMS1935V01Page.class, "lms1935.016");
		
		setupIPanel(new LASCOMMON01Panel(PANEL_ID), model, params);

	}
//UPGRADE(Scott)
//	@Override
//	public String[] getJavascriptPath() {
//		return new String[] { "pagejs/las/LMS1945V04.js",
//				"pagejs/las/LASCOMMON.js" };
//	}

}
