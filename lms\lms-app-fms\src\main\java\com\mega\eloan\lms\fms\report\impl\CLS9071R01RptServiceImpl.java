package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.fms.report.CLS9071R01RptService;
import com.mega.eloan.lms.fms.service.CLS9071Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

@Service("cls9071r01rptservice")
public class CLS9071R01RptServiceImpl implements
	FileDownloadService, CLS9071R01RptService {

	@Resource
	CLS9071Service cls9071Service;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String grpcntrno = Util.trim(params.getString("grpcntrno"));
			if(true){
				baos = (ByteArrayOutputStream) this.generateXls(grpcntrno);	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}			
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	private ByteArrayOutputStream generateXls(String grpcntrno) throws IOException, Exception {	
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		if(true){
			cls9071Service.genExcel(outputStream, grpcntrno);	
		}		
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
	
}
