package com.mega.eloan.lms.mfaloan.service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;

public interface MisGrpcmpService {
	public List<?> findGrpcmpForL120S05A1(String grpNo);

	public List<?> findGrpcmpForL120S05A2(String grpNo);

	public List<?> findGrpcmpForL120S05B1(String grpNo);

	public List<?> findGrpcmpForLNF022V15(String grpNo);

	public List<?> findGrpcmpForLNF022V11(String grpNo);

	public List<?> findGrpcmpForGrpid(String id, String dupno);

	public List<?> findGrpcmpForCmpnm(String grpNo);
	
	public List<Map<String, Object>> findGrpcmpForCmpnm1(String grpNo);

	public List<?> findGrpcmpForDW_ROCLIST_ECUS_Main1(String val);

	/**
	 * 集團代號查詢轄下公司資料
	 * 
	 * @param grpId
	 *            集團代號
	 * @return 轄下公司資列表
	 */
	List<Map<String, Object>> findAllByGrpId(String grpId);

	/**
	 * 集團代號查詢轄下公司資料
	 * 
	 * @param grpId
	 *            集團代號
	 * @return 轄下公司資列表(BAN, DUPNO ,TYPCD, CMPNM )
	 */
	List<Map<String, Object>> findAllByGrpId2(String grpId);

	/**
	 * 統一編號查詢轄下公司資料
	 * 
	 * @param custId
	 *            集團轄下公司之統一編號
	 * @param dupNo
	 *            重複序號
	 * @return 轄下公司資料
	 */
	Map<String, Object> findByCustIdAndDupNo(String custId, String dupNo);

	/**
	 * 取得敘做營業單位
	 * 
	 * @return 集團基本資料文字檔資料
	 * @throws CapException
	 */
	List<Map<String, Object>> findGrpbrnBranch() throws CapException;

	/**
	 * 依統一編號、重覆序號取得敘做營業單位
	 * 
	 * @param ban
	 *            統一編號
	 * @param dupNo
	 *            重覆序號
	 * @return 敘做營業單位
	 */
	List<Map<String, Object>> findGrpbrnBranchByBan(String ban, String dupNo);

	/**
	 * 上傳更新集團轄下公司基本資料檔
	 * 
	 * @param grpid
	 *            集團代號
	 * @param ben
	 *            轄下公司（個人戶）統一編號
	 * @param dupno
	 *            轄下公司重覆序號
	 * @param cmpnm
	 *            轄下公司（個人戶）名稱
	 * @param kind
	 *            自然人或法人
	 * @param poscd
	 *            職稱
	 * @param typecd
	 *            區部別
	 * @param gbanyn
	 *            是否編製三書表
	 * @param gban
	 *            核心公司統一編號
	 * @param branch
	 *            指定資料編製分行
	 * @param overseas
	 *            國內海外集團
	 * @param updater
	 *            資料修改人
	 * @param updateTime
	 *            資料修改日期
	 * @return 成功筆數
	 */
	int addGrpcmp(String grpid, String ben, String dupno, String cmpnm,
			String kind, String poscd, String typecd, String gbanyn,
			String gban, String branch, String overseas, String updater,
			Timestamp updateTime);

	/**
	 * 刪除集團轄下公司基本資料檔
	 * 
	 * @param grpid
	 *            集團代號
	 * @param ben
	 *            轄下公司（個人戶）統一編號
	 * @param dupno
	 *            轄下公司重覆序號
	 * @return 成功筆數
	 */
	int delGrpcmp(String grpid, String ben, String dupno);

	/**
	 * 刪除集團轄下公司
	 * 
	 * @param grpid
	 *            集團代號
	 * @return 成功筆數
	 */
	int delGrpcmpByGrpid(String grpid);

	/**
	 * 取得隸屬集團(借款人企金用)
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            借款人重覆序號
	 * @return 集團資料{ GRPID 集團代號 GRPNM 名稱}
	 */
	List<Map<String, Object>> findGrpcmpSelGrpdtl(String custId, String dupNo);

	/**
	 * 取得集團評等
	 * 
	 * @param grpId
	 * @param grpYY
	 * @return
	 */
	List<Map<String, Object>> findGrpcmpSelGrpGrade(String grpId, String grpYY);

	/**
	 * 取得集團資料
	 * 
	 * @param grpId
	 * @return
	 */
	public List<Map<String, Object>> findByGrpIDOnly100Record(String grpId);
}
