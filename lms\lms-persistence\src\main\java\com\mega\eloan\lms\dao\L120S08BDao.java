/* 
 * L120S08BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S08B;

/** 利率定價核理性分析表明細檔 **/
public interface L120S08BDao extends IGenericDao<L120S08B> {

	L120S08B findByOid(String oid);

	List<L120S08B> findByMainId(String mainId);

	List<L120S08B> findByIndex01(String mainId);

	List<L120S08B> findByIndex02(String mainId, String curr);

	List<L120S08B> findByMainIdCurrSeqNo(String mainId, String curr, BigDecimal seqNo);
	
	L120S08B findByMainIdCurrSeqNoItemName(String mainId, String curr, BigDecimal seqNo, String itemName);
}