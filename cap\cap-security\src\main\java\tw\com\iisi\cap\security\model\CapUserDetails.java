/*
 * CapUserDetails.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.security.model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;

import org.kordamp.json.JSONArray;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * <p>
 * 使用者資料.
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/2,iristu,new
 *          </ul>
 */
@SuppressWarnings("serial")
public class CapUserDetails implements UserDetails, IUser {

    /**
     * 使用者Id
     */
    String userId;
    /**
     * 使用者名稱
     */
    String userName;
    /**
     * 使用者所屬單位代碼
     */
    String unitNo;
    /**
     * 角色
     */
    Map<String, String> roles;
    /**
     * 目錄
     */
    JSONArray menu;

    /**
     * 驗證
     */
    private Collection<GrantedAuthority> authorities;

    /**
     * 建構子
     */
    public CapUserDetails() {
    }

    /**
     * 建立使用者詳細資訊
     * 
     * @param user
     * @param roles
     */
    public CapUserDetails(IUser user, Map<String, String> roles) {
        this.userId = user.getUserId();
        this.userName = user.getUserName();
        this.unitNo = user.getUnitNo();
        this.roles = new LinkedHashMap<String, String>();
        this.roles.putAll(roles);
        setAuthorities(roles);
    }

    /**
     * 設置權限
     * 
     * @param roles
     */
    protected void setAuthorities(Map<String, String> roles) {
        authorities = new ArrayList<GrantedAuthority>();
        for (String roleOid : roles.keySet()) {
            authorities.add(new SimpleGrantedAuthority(roleOid));
        }
    }

    /**
     * 取得權限
     */
    public Collection<GrantedAuthority> getAuthorities() {
        return this.authorities;
    }

    /**
     * 取得密碼
     * 
     * @return {@code ""}
     */
    public String getPassword() {
        return "";
    }

    /**
     * 取得使用者Id
     * 
     * @return {@code userId}
     */
    @Override
    public String getUsername() {
        return userId;
    }

    /**
     * 帳戶未過期
     * 
     * @return {@code true}
     */
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 憑證未過期
     * 
     * @return {@code true}
     */
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 啟用
     * 
     * @return {@code true}
     */
    public boolean isEnabled() {
        return true;
    }

    /**
     * 帳戶未鎖定
     * 
     * @return {@code true}
     */
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 取得 UnitNo
     */
    public String getUnitNo() {
        return unitNo;
    }

    /**
     * 取得使用者 ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 取得使用者名稱
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 取得 menu
     * 
     * @return
     */
    public JSONArray getMenu() {
        return menu;
    }

    /**
     * 設置 menu
     * 
     * @param menu
     */
    public void setMenu(JSONArray menu) {
        this.menu = menu;
    }

    /**
     * 取得角色
     */
    public Map<String, String> getRoles() {
        return roles;
    }

    /**
     * 設置角色
     * 
     * @param roles
     */
    public void setRoles(Map<String, String> roles) {
        this.roles = roles;
    }

}
