package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 價金履約保證覆審資料 **/
public class ELF486 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/**
	 * 分行代號
	 */
	@Column(name = "ELF486_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable=false,unique = true)
	private String elf486_branch;
	
	/**
	 * 保證編號
	 */
	@Column(name = "ELF486_LC_NO", length = 20, columnDefinition = "CHAR(20)", nullable=false,unique = true)
	private String elf486_lc_no;
	
	/**
	 * 額度序號
	 */
	@Column(name = "ELF486_CNTRNO", length = 12, columnDefinition = "CHAR(12)", nullable=false,unique = true)	
	private String elf486_cntrno;
		
	/**
	 * 保證起日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF486_S_DATE", columnDefinition = "DATE")
	private Date elf486_s_date;
	
	/**
	 * 保證迄日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF486_E_DATE", columnDefinition = "DATE")
	private Date elf486_e_date;
		
	/**
	 * 保證餘額
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF486_BAL_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf486_bal_amt;

	/**
	 * 逾期狀態
	 */
	@Column(name = "ELF486_OVERDUE", length = 2, columnDefinition = "CHAR(2)", nullable=false,unique = true)	
	private String elf486_overdue;
	
	/**
	 * 覆審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF486_ICMTYPE", columnDefinition = "DATE")
	private Date elf486_icmtype;
	
	/**
	 * 仲介商ID
	 */
	@Column(name = "ELF486_COM_ID", length = 10, columnDefinition = "CHAR(10)")
	private String elf486_com_id;
	
	/**
	 * 仲介商重覆碼
	 */
	@Column(name = "ELF486_COM_DUP", length = 1, columnDefinition = "CHAR(1)")
	private String elf486_com_dup;
	
	/**
	 * 仲介商姓名
	 */
	@Column(name = "ELF486_COM_NAME", length = 40, columnDefinition = "CHAR(40)")
	private String elf486_com_name;
	
	/**
	 * 賣方ID
	 */
	@Column(name = "ELF486_SELLER_ID", length = 10, columnDefinition = "CHAR(10)")
	private String elf486_seller_id;
	
	/**
	 * 賣方重覆碼
	 */
	@Column(name = "ELF486_SELLER_DUP", length = 1, columnDefinition = "CHAR(1)")
	private String elf486_seller_dup;
	
	/**
	 * 賣方姓名
	 */
	@Column(name = "ELF486_SELLER_NAME", length = 40, columnDefinition = "CHAR(40)")
	private String elf486_seller_name;
	
	/**
	 * 買方ID
	 */
	@Column(name = "ELF486_BUYER_ID", length = 10, columnDefinition = "CHAR(10)")
	private String elf486_buyer_id;
	
	/**
	 * 買方重覆碼
	 */
	@Column(name = "ELF486_BUYER_DUP", length = 1, columnDefinition = "CHAR(1)")
	private String elf486_buyer_dup;
	
	/**
	 * 買方姓名
	 */
	@Column(name = "ELF486_BUYER_NAME", length = 40, columnDefinition = "CHAR(40)")
	private String elf486_buyer_name;
	
	/**
	 * 資料修改人
	 */
	@Column(name = "ELF486_UPDATER", length = 8, columnDefinition = "CHAR(8)")
	private String elf486_updater;
	
	/** 
	 * 資料更新日
	 */
	@Column(name = "ELF486_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf486_tmestamp;

	public String getElf486_branch() {
		return elf486_branch;
	}

	public void setElf486_branch(String elf486_branch) {
		this.elf486_branch = elf486_branch;
	}

	public String getElf486_lc_no() {
		return elf486_lc_no;
	}

	public void setElf486_lc_no(String elf486_lc_no) {
		this.elf486_lc_no = elf486_lc_no;
	}

	public String getElf486_cntrno() {
		return elf486_cntrno;
	}

	public void setElf486_cntrno(String elf486_cntrno) {
		this.elf486_cntrno = elf486_cntrno;
	}

	public Date getElf486_s_date() {
		return elf486_s_date;
	}

	public void setElf486_s_date(Date elf486_s_date) {
		this.elf486_s_date = elf486_s_date;
	}

	public Date getElf486_e_date() {
		return elf486_e_date;
	}

	public void setElf486_e_date(Date elf486_e_date) {
		this.elf486_e_date = elf486_e_date;
	}

	public BigDecimal getElf486_bal_amt() {
		return elf486_bal_amt;
	}

	public void setElf486_bal_amt(BigDecimal elf486_bal_amt) {
		this.elf486_bal_amt = elf486_bal_amt;
	}

	public String getElf486_overdue() {
		return elf486_overdue;
	}

	public void setElf486_overdue(String elf486_overdue) {
		this.elf486_overdue = elf486_overdue;
	}

	public Date getElf486_icmtype() {
		return elf486_icmtype;
	}

	public void setElf486_icmtype(Date elf486_icmtype) {
		this.elf486_icmtype = elf486_icmtype;
	}

	public String getElf486_com_id() {
		return elf486_com_id;
	}

	public void setElf486_com_id(String elf486_com_id) {
		this.elf486_com_id = elf486_com_id;
	}

	public String getElf486_com_dup() {
		return elf486_com_dup;
	}

	public void setElf486_com_dup(String elf486_com_dup) {
		this.elf486_com_dup = elf486_com_dup;
	}

	public String getElf486_com_name() {
		return elf486_com_name;
	}

	public void setElf486_com_name(String elf486_com_name) {
		this.elf486_com_name = elf486_com_name;
	}

	public String getElf486_seller_id() {
		return elf486_seller_id;
	}

	public void setElf486_seller_id(String elf486_seller_id) {
		this.elf486_seller_id = elf486_seller_id;
	}

	public String getElf486_seller_dup() {
		return elf486_seller_dup;
	}

	public void setElf486_seller_dup(String elf486_seller_dup) {
		this.elf486_seller_dup = elf486_seller_dup;
	}

	public String getElf486_seller_name() {
		return elf486_seller_name;
	}

	public void setElf486_seller_name(String elf486_seller_name) {
		this.elf486_seller_name = elf486_seller_name;
	}

	public String getElf486_buyer_id() {
		return elf486_buyer_id;
	}

	public void setElf486_buyer_id(String elf486_buyer_id) {
		this.elf486_buyer_id = elf486_buyer_id;
	}

	public String getElf486_buyer_dup() {
		return elf486_buyer_dup;
	}

	public void setElf486_buyer_dup(String elf486_buyer_dup) {
		this.elf486_buyer_dup = elf486_buyer_dup;
	}

	public String getElf486_buyer_name() {
		return elf486_buyer_name;
	}

	public void setElf486_buyer_name(String elf486_buyer_name) {
		this.elf486_buyer_name = elf486_buyer_name;
	}

	public String getElf486_updater() {
		return elf486_updater;
	}

	public void setElf486_updater(String elf486_updater) {
		this.elf486_updater = elf486_updater;
	}

	public Timestamp getElf486_tmestamp() {
		return elf486_tmestamp;
	}

	public void setElf486_tmestamp(Timestamp elf486_tmestamp) {
		this.elf486_tmestamp = elf486_tmestamp;
	}
}
