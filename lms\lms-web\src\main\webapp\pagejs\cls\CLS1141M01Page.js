var initDfd = new $.Deferred();
//var initDfdPanel27 = new $.Deferred();

/*
	function setReadOnly1() {...} @ CLSM01APage.js
	
	var CLSAction = {...} is defined @CLS1141M01Page.js
*/

//登錄國金部-Thickbox
function signThickBox(){
    $.ajax({
        type: "POST",
        handler: CLSAction.fhandle,
        action: "querySignContent",
        data: {
        
            mainId: responseJSON.mainId
        },
        success: function(responseData){
            $("#tItemDscr09").val(responseData.formSea.itemDscr09);
            $("#_tItemDscr09").val($("#tItemDscr09").val());
            $("#signThick").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.cls1141m01['l120m01a.login'],
                width: 450,
                height: 250,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                readOnly: false,
                buttons: {
                    "sure": function(){
                        var value = $("input[name='sign']:checked").val();
                        if (value == 999) {
                            // 登錄簽章欄人員
                            signContent();
                        }
                        else {
                            $.ajax({
                                type: "POST",
                                handler: CLSAction.fhandle,
                                action: "saveSignContent",
                                data: {
                                
                                    itemDscr09: $("#tItemDscr09").val(),
                                    sSeaManager: $("#sSeaManager").val(),
                                    sSeaBoss: $("#sSeaBoss").val(),
                                    sSeaAoName: $("#sSeaAoName").val(),
                                    sSeaAppraiserCN: $("#sSeaAppraiserCN").val(),
                                    mainid: responseJSON.mainId
                                },
                                success: function(responseData){
                                
                                    setCkeditor2("itemDscr09", responseData.L120M01aForm14.itemDscr09);
                                }
                            });
                        }
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
        }
    });
}

//登錄授管處-Thickbox
function signThickBox2(){
    $("#_tItemDscr0A").val($("#tItemDscr0A").val());
    $("#_tItemDscr0B").val($("#tItemDscr0B").val());
    $("#signThick2").find("input[name='login']").readOnly(false);
    if (responseJSON.hqMeetFlag == "1" ||
    responseJSON.hqMeetFlag == "2" ||
    responseJSON.hqMeetFlag == "3") {
        $("#signThick2").find(".hide").show();
    }
    else {
        $("#signThick2").find(".hide").hide();
    }
    lmsM01Json.checkSpectialHead();
    $("#signThick2").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.cls1141m01['l120m01a.login'],
        width: 450,
        height: 250,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                var value = $("input[name='login']:checked").val();
                $.thickbox.close();
                if (value == 1) {
                    //案件審核層級
                    var obj = API.loadCombos(["lms1205m01_caseLvl"]);
                    $("#formCaseLvl").find("#caseLvl").setItems({
                        item: obj.lms1205m01_caseLvl,
                        format: "{value} - {key}",
                        space: false
                    });
                    signCaseLvl();
                }
                else 
                    if (value == 2) {
                        //補充意見及審查意見
                        //signContent2();
                        $.ajax({
                            type: "POST",
                            handler: CLSAction.fhandle,
                            action: "saveSignContent2",
                            data: {
                            
                                itemDscr0A: $("#tItemDscr0A").val(),
                                itemDscr0B: $("#tItemDscr0B").val(),
                                sHeadLeader: $("#sHeadLeader").val(),
                                sHeadSubLeader: $("#sHeadSubLeader").val(),
                                sHeadReCheck: $("#sHeadReCheck").val(),
                                sHeadAppraiser: $("#sHeadAppraiser").val(),
                                mainid: responseJSON.mainId
                            },
                            success: function(responseData){
                            
                                setCkeditor2("itemDscr0A", responseData.L120M01aForm13.itemDscr0A);
                                setCkeditor2("itemDscr0B", responseData.L120M01aForm13.itemDscr0B);
                                
                            }
                        });
                    }
                    else 
                        if (value == 998) {
                            // 授管處會簽意見(國內授信專用)
                            $.ajax({
                                type: "POST",
                                handler: CLSAction.fhandle,
                                data: {
                                    formAction: "saveSignContent2a",
                                    htItemDscr0C: $("#htItemDscr0C").val(),
                                    sHeadLeader: $("#sHeadLeader").val(),
                                    sHeadSubLeader: $("#sHeadSubLeader").val(),
                                    sHeadReCheck: $("#sHeadReCheck").val(),
                                    sHeadAppraiser: $("#sHeadAppraiser").val(),
                                    mainid: responseJSON.mainId
                                },
                                success: function(responseData){
                                    //$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                    setCkeditor2("itemDscr0C", responseData.L120M01aForm12.itemDscr0C);
                                //$("#L120M01aForm13").find("#itemDscr0A").val(responseData.L120M01aForm13.itemDscr0A);
                                //$("#L120M01aForm13").find("#itemDscr0B").val(responseData.L120M01aForm13.itemDscr0B);
                                }
                            });
                        }
                        else 
                            if (value == 3) {
                                // 會議決議
                                $.ajax({
                                    type: "POST",
                                    handler: CLSAction.fhandle,
                                    action: "querySignContent0",
                                    data: {
                                    
                                        mainId: responseJSON.mainId,
                                        txCode: responseJSON.txCode
                                    },
                                    success: function(responseData){
                                    
                                        $("#formL120m01h").reset();
                                        $("#formL120m01h").setData(responseData.formL120m01h, false);
                                        signContent0();
                                    }
                                });
                            }
                            else 
                                if (value == 4) {
                                    lmsM02Json.thickOpenUnNormal(false);
                                }
                                else 
                                    if (value == 5) {
                                        lmsM02Json.thickOpenDecide(false);
                                    } else if (value == 991) {
			                        	//#J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表
			                            //案件收件日期
			                        	signCaseReceivedDate();   
									}
                                    else {
                                        // 登錄簽章欄人員
                                        signContent2();
                                    }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function login1(oid, isArea){
    //$("#LMS1200V62Form1").reset();
    $("#LMS1200V62Form1").find("#rptTitle1e").html($("body").find(".system_info span").html());
    $("#LMS1200V62Thickbox1").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.cls1141m01["l120v01.thickbox7"],
        width: 500,
        height: 200,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                var $LMS1200V62Form1 = $("#LMS1200V62Form1");
                if ($LMS1200V62Form1.valid()) {
                    if ($LMS1200V62Form1.find("#rptTitle1b").val() < 1 ||
                    $LMS1200V62Form1.find("#rptTitle1b").val() > 12) {
                        API.showMessage(i18n.cls1141m01["l120v01.error3"]);
                        return;
                    }
                    else 
                        if ($LMS1200V62Form1.find("#rptTitle1c").val() < 1 ||
                        $LMS1200V62Form1.find("#rptTitle1c").val() > 31) {
                            API.showMessage(i18n.cls1141m01["l120v01.error4"]);
                            return;
                        }
                        else 
                            if ($LMS1200V62Form1.find("#rptTitle1d").val() <=
                            0) {
                                API.showMessage(i18n.cls1141m01["l120v01.error6"]);
                                return;
                            }
                            else 
                                if ($LMS1200V62Form1.find("#rptTitle1a").val() <= 0) {
                                    API.showMessage(i18n.cls1141m01["l120v01.error8"]);
                                    return;
                                }
                                else {
                                    $.ajax({
                                        type: "POST",
                                        handler: CLSAction.fhandle,
                                        action: "login1",
                                        data: {
                                            LMS1200V62Form1: JSON.stringify($LMS1200V62Form1.serializeData()),
                                            oid: oid,
                                            isArea: isArea,
											isDelete : false,
                                            caseName: $LMS1200V62Form1.find("b").text()
                                        },
                                        success: function(responseData){
                                            // 顯示營運中心授審會會期
                                            var $form = $("#" + CLSAction.mainFormId);
                                            $form.find(".areaTitle").show();
                                            $form.find("#rptTitleArea1").html(DOMPurify.sanitize(responseData.rptTitleArea1));
                                        }
                                    });
                                }
                    $.thickbox.close();
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            },
			"del" : function() {
				$.thickbox.close();
				var $LMS1200V62Form1 = $("#LMS1200V62Form1");
				 API.confirmMessage(i18n.def['confirmDelete'], function(res){
						if(res){
							$.ajax({
                                type: "POST",
                                handler: CLSAction.fhandle,
                                action: "login1",
                                data: {
                                    LMS1200V62Form1: JSON.stringify($LMS1200V62Form1.serializeData()),
                                    oid: oid,
                                    isArea: isArea,
									isDelete : true,
                                    caseName: $LMS1200V62Form1.find("b").text()
                                },
                                success: function(responseData){
                                    // 顯示營運中心授審會會期
                                    var $form = $("#" + CLSAction.mainFormId);
                                    $form.find(".areaTitle").show();
                                    $form.find("#rptTitleArea1").html(DOMPurify.sanitize(responseData.rptTitleArea1));
                                }
                            });
							
						}
						
						$.thickbox.close();
			        });
			}
        }
    });
}

//登錄營運中心-Thickbox
function signThickBox3(){
    // 控制案件審核層級顯示條件
    if (responseJSON.docKind == "2") {
        $("#sCaseLvl").show();
    }
    $("#_tItemDscr07").val($("#tItemDscr07").val());
    $("#_tItemDscr08").val($("#tItemDscr08").val());
    $("#signThick3").find("input[name='login2']").readOnly(false);
    $("#signThick3").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.cls1141m01['l120m01a.login'],
        width: 450,
        height: 250,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                var value = $("input[name='login2']:checked").val();
                $.thickbox.close();
                if (value == 1) {
                    //營運中心授審會會期
                    $.ajax({
                        type: "POST",
                        handler: CLSAction.fhandle,
                        action: "queryLogin1",
                        data: {
                        
                            isArea: true,
                            mainId: responseJSON.mainId
                        },
                        success: function(responseData){
                            $("#LMS1200V62Form1").setData(responseData);
                            login1(responseData.oid, true);
                        }
                    });
                }
                else 
                    if (value == 2) {
                        //營運中心說明及意見
                        //signContent3();
                        $.ajax({
                            type: "POST",
                            handler: CLSAction.fhandle,
                            action: "saveSignContent3",
                            data: {
                            
                                itemDscr07: $("#tItemDscr07").val(),
                                itemDscr08: $("#tItemDscr08").val(),
                                sAreaLeader: $("#sAreaLeader").val(),
                                sAreaSubLeader: $("#sAreaSubLeader").val(),
                                sAreaManager: $("#sAreaManager").val(),
                                sAreaAppraiser: $("#sAreaAppraiser").val(),
                                itemTitle: $("#itemTitle").val(),
                                mainid: responseJSON.mainId
                            },
                            success: function(responseData){
                                setCkeditor2("itemDscr07", responseData.L120M01aForm15.itemDscr07);
                                setCkeditor2("itemDscr08", responseData.L120M01aForm15.itemDscr08);
                            }
                        });
                    }
                    else 
                        if (value == 3) {
                            // 會議決議
                            $.ajax({
                                type: "POST",
                                handler: CLSAction.fhandle,
                                data: {
                                    formAction: "querySignContent0",
                                    isArea: true,
                                    mainId: responseJSON.mainId,
                                    txCode: responseJSON.txCode
                                },
                                success: function(responseData){
                                    $("#formL120m01h").reset();
                                    $("#formL120m01h").setData(responseData.formL120m01h, false);
                                    signContent0();
                                }
                            });
                        }
                        else 
                            if (value == 4) {
                                lmsM02Json.thickOpenUnNormal();
                            }
                            else 
                                if (value == 998) {
                                    //案件審核層級
                                    var obj = CommonAPI.loadCombos(["lms1205m01_caseLvl"]);
                                    $("#formCaseLvl").find("#caseLvl").setItems({
                                        item: obj.lms1205m01_caseLvl,
                                        format: "{value} - {key}",
                                        space: false
                                    });
                                    signCaseLvl();
                                } else if (value == 991) {
		                        	//#J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表
		                            //案件收件日期
		                        	signCaseReceivedDate();   
								}	
                                else {
                                    signContent3(); //此處呼叫的 signContent3() 寫在 CLSM01APage.js
                                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//查詢會簽意見內容(營運中心)-Thickbox
function rSignContent(){
    $("#signContent").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.cls1141m01['l120m01a.looksay2'],
        width: 640,
        height: 350,
        modal: true,
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
    $.ajax({
        type: "POST",
        handler: CLSAction.fhandle,
        action: "querySignContent",
        data: {
        
            mainId: responseJSON.mainId
        },
        success: function(responseData){
        
            $("#tItemDscr09").val(responseData.formSea.itemDscr09);
        }
    });
}

function lms7205Grid(){
    var grid = $("#lms7205Grid").iGrid({
        localFirst: true,
        handler: CLSAction.ghandle,
        height: 350,
        sortname: 'patternNM',
        action: "queryL720m01a",
        postData: {
            docStatus: responseJSON.mainDocStatus,
            
            rowNum: 15
        },
        rowNum: 15,
        //multiselect : true,
        colModel: [{
            colHeader: i18n.cls1141m01['L720M01A.patternNM'],//"範本名稱",
            name: 'patternNM',
            align: "left",
            width: 200,
            sortable: true
        }, {
            colHeader: i18n.cls1141m01['L720M01A.updater'],//"最後異動人員",
            name: 'updater',
            width: 200,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        }
    });
}

function uThickLms7205Grid(){
    $("#lms7205Grid").jqGrid("setGridParam", {
        postData: {
            docStatus: responseJSON.mainDocStatus,
            formAction: "queryL720m01a",
            rowNum: 15
        },
        search: true
    }).trigger("reloadGrid");
}

function thickLms7205Grid(){
    uThickLms7205Grid();
    $("#lms7205Grid").resetSelection();
    var openThickbox = $("#thickLms7205Grid").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.cls1141m01['l120m01a.bt05'],
        width: 640,
        height: 470,
        modal: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(showMsg){
                var row1 = $("#lms7205Grid").getGridParam('selrow');
                var list1 = "";
                var data1 = $("#lms7205Grid").getRowData(row1);
                list1 = data1.oid;
                list1 = (list1 == undefined ? "" : list1);
                if (list1 != "") {
                    $.ajax({
                        type: "POST",
                        handler: CLSAction.fhandle,
                        data: {
                            formAction: "queryL720m01a",
                            mainId: responseJSON.mainId,
                            oid: list1
                        },
                        success: function(responseData){
                            //alert(JSON.stringify(responseData));
                            $.thickbox.close();
                            $.thickbox.close();
                            $("#meetingNote").val(responseData.meetingNote);
                            //setCkeditor2("meetingNote",responseData.meetingNote);
                            CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                        }
                    });
                }
                else {
                    CommonAPI.showMessage(i18n.cls1141m01["l120m01a.error1"]);
                }
            },
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//催收會會議決議(產Word)
function printLmsDoc80(){
    var $formL120m01h = $("#formL120m01h");
    CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
        if (b) {
            // 儲存後列印
            $.ajax({
                type: "POST",
                handler: CLSAction.fhandle,
                data: $.extend({
                    formAction: "saveSignContent0",
                    formL120m01h: JSON.stringify($formL120m01h.serializeData()),
                    mainid: responseJSON.mainId,
                    txCode: responseJSON.txCode
                }, (userInfo.unitNo == "920" ||
                userInfo.unitNo == "922" ||
                userInfo.unitNo == "931" ||
                userInfo.unitNo == "932" ||
                userInfo.unitNo == "933" ||
                userInfo.unitNo == "934" ||
                userInfo.unitNo == "935") ? {
                    isArea: true
                } : {}),
                success: function(responseData){
                    if (responseData.rptTitle1 == "") {
                        // other.msg146=催收會會期為空，無法執行本功能，請先登錄會期！
                        return CommonAPI.showMessage(i18n.lmscommom["other.msg146"]);
                    }
                    $.form.submit({
                        url: "../../simple/FileProcessingService",
                        target: "_blank",
                        data: {
                            fileName: "LMSDoc8.htm",
                            mainId: responseJSON.mainId,
                            docTempType: "LMSDoc80",
                            fileDownloadName: "LMSDoc80.doc",
                            serviceName: "lms1201docservice"
                        }
                    });
                }
            });
        }
    });
}


//會議決議(產報表)
function printR20(){
    var $formL120m01h = $("#formL120m01h");
    CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
        if (b) {
            // 儲存後列印
            $.ajax({
                type: "POST",
                handler: CLSAction.fhandle,
                data: $.extend({
                    formAction: "saveSignContent0",
                    formL120m01h: JSON.stringify($formL120m01h.serializeData()),
                    mainid: responseJSON.mainId,
                    txCode: responseJSON.txCode
                }, (userInfo.unitNo == "920" ||
                userInfo.unitNo == "922" ||
                userInfo.unitNo == "931" ||
                userInfo.unitNo == "932" ||
                userInfo.unitNo == "933" ||
                userInfo.unitNo == "934" ||
                userInfo.unitNo == "935") ? {
                    isArea: true
                } : {}),
                success: function(responseData){
                    var itemDscr08 = responseData.itemDscr08;
                    var itemDscr0D = responseData.itemDscr0D;
                    var page = responseJSON.page;
                    if (itemDscr08 != undefined && itemDscr08 != null) {
                        if (page == "15") {
                            setCkeditor2("itemDscr08", itemDscr08);
                        }
                    }
                    if (itemDscr0D != undefined && itemDscr0D != null) {
                        if (page == "12") {
                            setCkeditor2("itemDscr0D", itemDscr0D);
                        }
                    }
                    $.form.submit({
                        url: "../../simple/FileProcessingService",
                        target: "_blank",
                        data: {
                            mainId: responseJSON.mainId,
                            rptOid: "R20" + "^" + "",
                            otherData: (responseData.meetType != undefined &&
                            responseData.meetType != null &&
                            responseData.meetType != "") ? responseData.meetType : "",
                            fileDownloadName: "l120r01.pdf",
                            serviceName: "lms1201r01rptservice"
                        }
                    });
                }
            });
        }
    });
}

function signContent0(){
    var $formL120m01h = $("#formL120m01h");
    $("#meetingNote").readOnly(false);
    $formL120m01h.find("#itemDscrC").readOnly(true);
    if (responseJSON.hqMeetFlag == "B" || responseJSON.hqMeetFlag == "2") {
        $formL120m01h.find("#btnLmsDoc80").show();
    }
    $("#signContent0").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.cls1141m01['L120M01H.meetingNote'],
        width: 900,
        height: 480,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                var $formL120m01h = $("#formL120m01h");
                $.ajax({
                    type: "POST",
                    handler: CLSAction.fhandle,
                    action: "saveSignContent0",
                    data: $.extend({
                    
                        formL120m01h: JSON.stringify($formL120m01h.serializeData()),
                        mainid: responseJSON.mainId,
                        txCode: responseJSON.txCode
                    }, (userInfo.unitNo == "920" ||
                    userInfo.unitNo == "922" ||
                    userInfo.unitNo == "931" ||
                    userInfo.unitNo == "932" ||
                    userInfo.unitNo == "933" ||
                    userInfo.unitNo == "934" ||
                    userInfo.unitNo == "935") ? {
                        isArea: true
                    } : {}),
                    success: function(responseData){
                        var itemDscr08 = responseData.itemDscr08;
                        var itemDscr0D = responseData.itemDscr0D;
                        var page = responseJSON.page;
                        if (itemDscr08 != undefined && itemDscr08 != null) {
                            if (page == "15") {
                                setCkeditor2("itemDscr08", itemDscr08);
                            }
                        }
                        if (itemDscr0D != undefined && itemDscr0D != null) {
                            if (page == "12") {
                                setCkeditor2("itemDscr0D", itemDscr0D);
                            }
                        }
                    }
                });
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//登錄案件審核層級
function signCaseLvl(){
    $("#signCaseLvl").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.cls1141m01['l120m01a.caselevel'],
        width: 320,
        height: 200,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                $.ajax({
                    type: "POST",
                    handler: CLSAction.fhandle,
                    action: "setCaseLvl",
                    data: {
                    
                        caseLvl: $("#formCaseLvl").find("#caseLvl option:selected").val(),
                        mainid: responseJSON.mainId
                    },
                    success: function(responseData){
                        $("#caseLvl").val(responseData.caseLvl);
                        $.thickbox.close();
                        $.thickbox.close();
                        API.showMessage(responseData.NOTIFY_MESSAGE);
                    }
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//合併多個serializeData JSON 成一個serializeData JSON
function mergeJSON(json, array){
    for (var data in array) {
        json[data] = array[data];
    }
}

//檢查陣列內容是否重複
function checkArrayRepeat(arrVal){
    var newArray = [];
    for (var i = arrVal.length; i--;) {
        var val = arrVal[i];
        if ($.inArray(val, newArray) == -1) {
            newArray.push(val);
        }
        else {
            return true;
        }
    }
    return false;
}

var CLSAction = {
    mainFormId: "CLSForm",
    fhandle: "cls1141m01formhandler",
    ghandle: "cls1141gridhandler",
    grid: null,
    init: function(){
    
    },
    isByHeadAndArea: function(){
        var bird = userInfo ? userInfo.unitNo : ""
        var unitType = userInfo ? userInfo.unitType : "";
        if (unitType) {
            //unitType 為2或4即為授管處或營運中心
            if ("4" == unitType || "2" == unitType) {
                return true;
            }
        }
        return false
    },
    /**
     * 畫面的顯示
     * @param {Object} jsonInit
     */
    setQueryData: function(jsonInit){
        responseJSON.docStatus = jsonInit.docStatus;
        if ($("#authLvl").length == 0) {
            $("body").append("<input type='hidden'  id='authLvl' name='authLvl' value='" + DOMPurify.sanitize(jsonInit.authLvl) + "'/>");
        }
        if (responseJSON.page == "01") {
            //塞入簽章欄
            // var L120M01FData = jsonInit.L120M01FData;
            //for (var id in L120M01FData) {
            //$("#" + id).html(L120M01FData[id])
            //}
            $("#" + CLSAction.mainFormId).injectData(jsonInit);
        }
        if (responseJSON.page == "08") {
            if (jsonInit.formL120m01e.cesCase == "Y") {
                $("#formL120m01e").find("#cesCase:checkbox").attr("checked", true);
            }
            else {
                $("#formL120m01e").find("#cesCase:checkbox").attr("checked", false);
            }
            $("#formL120m01e").find("#docDscr1").html(DOMPurify.sanitize(jsonInit.formL120m01e.docDscr1));
            if (jsonInit.formL120m01e.docDscr1 != "" &&
            jsonInit.formL120m01e.docDscr1 != undefined &&
            jsonInit.formL120m01e.docDscr1 != null) {
                $("#docDscr1 a").attr({
                    "href": "#"
                });
            }
            else {
                $("#docDscr1").html("");
                $("#docDscr1").val("");
            }
            $("#formL120m01e").find("#docDscr2").html(DOMPurify.sanitize(jsonInit.formL120m01e.docDscr2));
            if (jsonInit.formL120m01e.docDscr2 != "" &&
            jsonInit.formL120m01e.docDscr2 != undefined &&
            jsonInit.formL120m01e.docDscr2 != null) {
                $("#docDscr2 a").attr({
                    "href": "#"
                });
            }
            else {
                $("#docDscr2").html("");
                $("#docDscr2").val("");
            }
            /**
             * <pre>
             *2013_04_08,edit,個金擔保品有所差異所已做更改
             * initS08dJson.afterQuery(jsonInit);
             *  $("#formL120m01e").find("#docDscr3").html(jsonInit.formL120m01e.docDscr3);
             *$("#formL120m01e").find("#docDscr4").html(jsonInit.formL120m01e.docDscr4);
             *$("#formL120m01e").find("#docDscr5").html(jsonInit.formL120m01e.docDscr5);
             *if (jsonInit.formL120m01e.docDscr5 != "" &&
             *jsonInit.formL120m01e.docDscr5 != undefined &&
             *jsonInit.formL120m01e.docDscr5 != null) {
             *$("#docDscr5 a").attr({
             *"href": "#"
             *});
             *} else {
             *var $docDscr5 = $("#docDscr5");
             *$docDscr5.html("");
             *$docDscr5.val("");
             }
             * </pre>
             */
            $("#formL120m01e").find("#docDscr6").html(DOMPurify.sanitize(jsonInit.formL120m01e.docDscr6));
            if (jsonInit.formL120m01e.docDscr6 != "" &&
            jsonInit.formL120m01e.docDscr6 != undefined &&
            jsonInit.formL120m01e.docDscr6 != null) {
                $("#docDscr6 a").attr({
                    "href": "#"
                });
            }
            else {
                var $docDscr6 = $("#docDscr6");
                $docDscr6.html("");
                $docDscr6.val("");
            }
            $("#formL120m01e").find("#docDscr7").html(DOMPurify.sanitize(jsonInit.formL120m01e.docDscr7));
            if (responseJSON.readOnly != undefined &&
            responseJSON.readOnly != null &&
            responseJSON.readOnly != '') {
                if (responseJSON.readOnly.toString() == "true") {
                    setReadOnly1();
                }
            }
        } else if (responseJSON.page == "10") {
                var $formfile = $("#formfile");
                if (responseJSON.readOnly != undefined &&
                responseJSON.readOnly != null &&
                responseJSON.readOnly != '') {
                    if (responseJSON.readOnly.toString() == "true") {
                        setReadOnly1();
                    }
                }
                if (jsonInit.formfile.sfilehide1a) {
                    $formfile.find("#filehide1a").hide();
                }
                else {
                    $formfile.find("#filehide1a").show();
                }
                if (jsonInit.formfile.sfilehide1b) {
                    $formfile.find("#filehide1b").hide();
                }
                else {
                    $formfile.find("#filehide1b").show();
                    if (auth.Modify) {
                        $formfile.find("#filehide1b button").show();
                    }
                }
                if (jsonInit.formfile.sfilehide1a && jsonInit.formfile.sfilehide1b) {
                    $formfile.find("#filehide1").hide();
                }
                if (jsonInit.formfile.sfilehide2a) {
                    $formfile.find("#filehide2a").hide();
                }
                else {
                    $formfile.find("#filehide2a").show();
                }
                if (jsonInit.formfile.sfilehide2b) {
                    $formfile.find("#filehide2b").hide();
                }
                else {
                    $formfile.find("#filehide2b").show();
                    if (auth.Modify) {
                        $formfile.find("#filehide2b button").show();
                    }
                }
                if (jsonInit.formfile.sfilehide2a && jsonInit.formfile.sfilehide2b) {
                    $formfile.find("#filehide2").hide();
                }
        } else if (responseJSON.page == "12") {
                    //setCkeditor2("itemDscr0C", jsonInit.L120M01aForm12.itemDscr0C);
                    if (jsonInit._mainDocStatus == "01O" ||
                    jsonInit._mainDocStatus == "02O" ||
                    !(jsonInit.areaChk == "2" || jsonInit.areaChk == "4" || jsonInit.areaChk == "5" || jsonInit.areaChk == "6")) {
                        $("#L120M01aForm12").find("#hItemDscr0C").hide();
                    }
                    else {
                        setTimeout(function(){
                            if (responseJSON.hqMeetFlag == "A" ||
                            responseJSON.hqMeetFlag == "B" ||
                            responseJSON.hqMeetFlag == "C" ||
                            responseJSON.hqMeetFlag == "D") {
                            	//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
                                // 總處分行會簽後，提授審會等會議，附加檔案的功能請開放，但是請控管，025不能刪除非025上傳的檔案
                                $("#L120M01aForm12").find(":button").show();
                            }
                        }, 500);
                        $("#L120M01aForm12").find("#hItemDscr0C").show();
                    }
                    if (!jsonInit.hideDispWord) {
                        $("#L120M01aForm12").find("#hDispWord").show();
                    }
                    else {
                        $("#L120M01aForm12").find("#hDispWord").hide();
                    }
        } else if (responseJSON.page == "14") {
                        setCkeditor2("itemDscr09", jsonInit.L120M01aForm14.itemDscr09);
        } else if (responseJSON.page == "15") {
                            setCkeditor2("itemDscr07", jsonInit.L120M01aForm15.itemDscr07);
                            setCkeditor2("itemDscr08", jsonInit.L120M01aForm15.itemDscr08);
                            $("#L120M01aForm15").find("#willIdeaB").html(DOMPurify.sanitize(jsonInit.L120M01aForm15.willIdeaB));
        } else if (responseJSON.page == "17") {
                                if (jsonInit.L120M01aForm17.showBtn) {
                                    $("#L120M01aForm17").find("#showBtnA").show();
                                }
                                else {
                                    $("#L120M01aForm17").find("#showBtnA").hide();
                                }
        } else if (responseJSON.page == "19") {
                                    if ((userInfo.unitNo == "920" || userInfo.unitNo == "931" ||
                                    userInfo.unitNo == "922" ||
                                    userInfo.unitNo == "932" ||
                                    userInfo.unitNo == "933" ||
                                    userInfo.unitNo == "934" ||
                                    userInfo.unitNo == "935") ||
                                    userInfo.unitNo == "918") {
                                        // 營運中心、授管處則隱藏「登錄擬(已)辦事項」按鈕
                                        $("#unNormalForm #hideNotSub").hide();
                                    }
                                    else {
                                        $("#unNormalForm #hideNotSub").show();
                                    }
                                    // 異常通報表查詢
                                    var $unNormalForm = $("#unNormalForm");
                                    $unNormalForm.setData(jsonInit.unNormalForm);
                                    // 設定是否為參貸行
                                    $unNormalForm.find("input[name='hasBrid']:radio").each(function(i){
                                        var $this = $(this);
                                        if (jsonInit.unNormalForm.haseBrid == $this.val()) {
                                            $this.trigger("click");
                                            return false;
                                        }
                                    });
                                    // 設定參貸行 checkbox
                                    if (jsonInit.listBranch != undefined && jsonInit.listBranch != null && jsonInit.listBranch != "") {
                                        for (o in jsonInit.listBranch) {
                                            $unNormalForm.find("input[name='branchIds']").each(function(i){
                                                var $this = $(this);
                                                if (jsonInit.listBranch[o] == $this.val()) {
                                                    $this.attr("checked", true);
                                                    // each 中的 break用法
                                                    return false;
                                                }
                                            });
                                        }
                                    }
                                    // 查詢異常通報合併字串
                                    $.ajax({
                                        type: "POST",
                                        handler: CLSAction.fhandle,
                                        action: "queryL130m01b",
                                        data: {
                                            // 固定查詢分行合併字串
                                            branchKind: "1",
                                            mainId: responseJSON.mainId
                                        },
                                        success: function(json){
                                            $("#unNormalGrid").trigger("reloadGrid");
                                            $("#unNormalForm #willIdea").html(DOMPurify.sanitize(json.willIdea));
                                            if (jsonInit.headSay != "") {
                                                $.ajax({
                                                    type: "POST",
                                                    handler: "lms1301formhandler",
                                                    action: "queryL130m01b",
                                                    data: {
                                                        // 查詢授管處核定字串
                                                        branchKind: "3",
                                                        mainId: responseJSON.mainId
                                                    },
                                                    success: function(json2){
                                                        $("#unNormalForm #headWillIdea").html(DOMPurify.sanitize(json2.willIdea));
                                                        $("#unNormalForm #headSay").html(DOMPurify.sanitize(jsonInit.headSay));
                                                        $("#unNormalForm #headApprove").html(DOMPurify.sanitize(jsonInit.headApprove));
                                                        $("#unNormalForm #headUser").html(DOMPurify.sanitize(jsonInit.headUser));
                                                        $unNormalForm.find(".headShow").show();
                                                    }
                                                });
                                            }
                                        }
                                    });
                                    // 於畫面上渲染關係戶清單下載用tag
                                    lmsM02Json.genLms1201r49("N");// genAction = 'N'
        } else if (responseJSON.page == "20") {
        	$("#CLS1201S20Form #blackListQDate").val(jsonInit.blackListQDate||'');
        	
        	// J-111-0141 針對國內企金、消金及海外授信簽案系統之AML頁籤，增加「調查結果說明」
        	if(jsonInit.showNcResultRemark && jsonInit.showNcResultRemark == "Y"){
        		$("#CLS1201S20Form").find("#ncResultRemarkDiv").show();
        	}else{
        		$("#CLS1201S20Form").find("#ncResultRemarkDiv").hide();
        	}
        	if(jsonInit.showHighRiskRemark && jsonInit.showHighRiskRemark == "Y"){
        		$("#CLS1201S20Form").find("#highRiskRemarkDiv").show();
        	}else{
        		$("#CLS1201S20Form").find("#highRiskRemarkDiv").hide();
        	}
        } else if (responseJSON.page == "24") {
        	$("#CLS1201S24Form").injectData(jsonInit);
        }
        CLSAction.setReadOnlyShow();
    },
    //依照不同系統控制顯示頁籤
    controlBook: function(){
        $.ajax({
            action: "checkBookmark",
            handler: CLSAction.fhandle,
            data: {
                mainOid: responseJSON.mainOid,
                mainId: responseJSON.mainId,
                docKind: responseJSON.docKind,
                docCode: responseJSON.docCode,
                docStatus: responseJSON.mainDocStatus
            },
            success: function(responseData){
				if (responseData.simplifyFlag == 'E') {//無紙化簽報
					$(".ordinary").hide();
					$(".paperless").show();
				}
				else {
					$(".paperless").hide();
					for (o in responseData.hideBook) {
						$(DOMPurify.sanitize(responseData.hideBook[o])).hide();
					}
				}
                $(".tabs-warp").scrollToTab();
            }
        });
    },
    /**
     * 初始化grid
     *
     */
    initGrid: function(){
    
    },
    /**
     * 初始化事件
     *
     */
    initEven: function(){
    
    },
    /**
     * 重新整理Grid
     *
     */
    reloadGrid: function(){
        this.grid.trigger('reloadGrid');
    },
    /**
     * 重新整理Grid
     * @param {Object} data 設定資料
     *
     */
    setGrid: function(data){
        this.grid.jqGrid("setGridParam", {
            sortname: "",
            sortorder: "",
            postData: $.extend({}, data || {}),
            page: 1,
            search: true
        }).trigger("reloadGrid");
    },
    /**
     * 控制畫面的隱藏顯示
     */
    setReadOnlyShow: function(){
    	 
        var doLock = true;
        //page=12【會簽/會議決議】page=13【授管處意見】page=15【營運中心意見】
        if (responseJSON.page == "12" || responseJSON.page == "13" || responseJSON.page == "15") {
            setReadOnly1();
        }
        else {
            if (CLSAction.isReadOnly()) {
                if (responseJSON.page == "11") {//page=11【額度批覆表】
                    if (responseJSON.docStatus == "L1H" || responseJSON.docStatus == "L1C") {
                        doLock = false;
                    }else if (responseJSON.docStatus == "01K" ||
                    		responseJSON.docStatus == "03K" ||
                    		responseJSON.docStatus == "05K"||
                    		responseJSON.docStatus == "06K"||
                    		responseJSON.docStatus == "07K"){
                    	//J-108-0316_10702_B1001 Web e-Loan調整國外部、國際金融業務分行與金控總部分行等原總處營業單位會簽流程
                    	doLock = false;
                    	$("#checkBt").hide();
                    }
                }else if (responseJSON.page == "17") {//page=17【常董稿附加檔案】
                	if (responseJSON.docStatus == "L1H") {
                        doLock = false;
                    }
                }
              
                
                if (doLock) {
                    if (responseJSON.page == "05") {//page=05【借款用途/還款財源/各項費用】
//                        alert("aa");
                        if (responseJSON.docStatus == "L1H" || responseJSON.docStatus == "L1C") {
                            if (CLSAction.isByHeadAndArea()) {
                                $("#PageContext").lockDoc(false);
                                $("#special  button").show();
								$("#charge input,#charge textarea,#charge select").attr("readOnly", false);
								$("#charge select").attr("disabled", false);
                            } else {
								$("#PageContext").lockDoc();
							}
                        }
                        else {
                            $("#PageContext").lockDoc();
                        }
                    }else  if (responseJSON.page == "27" && responseJSON.docStatus == "02O") {
						setIgnoreTempSave(true);
					}
					else {
						$("#PageContext").lockDoc();
					}
                    
                }
            }
            else {
            	if (responseJSON.page == "11") {//page=11【額度批覆表】
            		if (responseJSON.docStatus == "01K" ||
                    		responseJSON.docStatus == "02K" ||
                    		responseJSON.docStatus == "03K" ||
                    		responseJSON.docStatus == "05K"||
                    		responseJSON.docStatus == "06K"||
                    		responseJSON.docStatus == "07K"){
                    	//J-108-0316_10702_B1001 Web e-Loan調整國外部、國際金融業務分行與金控總部分行等原總處營業單位會簽流程
                    	doLock = false;
                    	$("#checkBt").hide();
                    }
                }
            }
        }
        
        
        
    },
    /**
     * 依照權限隱藏特定物件
     */
    controlDisplayObject: function(){
        $.ajax({
            action: "check_only_expermission",
            handler: CLSAction.fhandle,
            success: function(responseData){
            	if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
            		$(".only-ex-permission").hide();
            	}
            }
        });
    },
    /**  
     *檢驗列印順序
     *@param {function } 按下確定後執行的動作
     */
    queryPrintSeq: function(afterAction){
        $.ajax({
            async: false,
            handler: "cls1151m01formhandler",
            action: "queryPrintSeq",
            data: {
                mainId: $("#mainId").val()
            },
            success: function(obj){
                if (obj.printSeqMsg && obj.printSeqMsg != "") {
                    API.confirmMessage(obj.printSeqMsg, function(b){
                        if (b) {
                            afterAction();
                        }
                    });
                }
                else {
                    afterAction();
                }
            }
        });
    },
    /**
     *flow相關動作
     * @param {Object} sendData
     */
    flowAction: function(sendData){
        $.ajax({
            handler: CLSAction.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(){
                API.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
            }
        });
    },
    /**
     * 處理全部的儲存
     * @param {Object} afterFn 儲存後要執行的動作
     * @param {Object} isSendBoss 是否為程主管
     */
    saveAll: function(afterFn, isSendBoss){
        // 異常通報參貸行前端js設定
        var extData = {
            unNormalForm: JSON.stringify($("#unNormalForm").serializeData()),
            haseBrid: $("#unNormalForm").find("input[name='hasBrid']:radio:checked").val()
        };
        
        var $LMS1205S01Form = $("#LMS1205S01Form");
        var $L120M01BForm = $("#L120M01BForm");
        var $LMS1205S04Form = $("#LMS1205S04Form");
        var $LMS1205S05Form01 = $("#LMS1205S05Form01");
        var $LMS1205S05Form04 = $("#LMS1205S05Form04");
        var $LMS1205S05Form06 = $("#LMS1205S05Form06");
        var $LMS1205S05Form07 = $("#LMS1205S05Form07");
        var $CLS1205S05Form = $("#CLS1205S05Form");
        var $L120M01DForm03 = $("#L120M01DForm03");
        var $L120M01dForm04 = $("#L120M01dForm04");
        var $formL120m01e = $("#formL120m01e");
        var $L120M01DForm05 = $("#L120M01DForm05");
        // 額外參數設定	
        var sendData = {
            L120M01BForm: JSON.stringify($L120M01BForm.serializeData()),
            LMS1205S04Form: JSON.stringify($LMS1205S04Form.serializeData()),
            LMS1205S05Form01: JSON.stringify($LMS1205S05Form01.serializeData()),
            LMS1205S05Form04: JSON.stringify($LMS1205S05Form04.serializeData()),
            longCaseFlag: $LMS1205S05Form04.find("[name='longCaseFlag']:radio:checked").val(),
            LMS1205S05Form06: JSON.stringify($LMS1205S05Form06.serializeData()),
            LMS1205S05Form07: JSON.stringify($LMS1205S05Form07.serializeData()),
            CLS1205S05Form: JSON.stringify($CLS1205S05Form.serializeData()),
            L120M01DForm03: JSON.stringify($L120M01DForm03.serializeData()),
            L120M01dForm04: JSON.stringify($L120M01dForm04.serializeData()),
            formL120m01e: JSON.stringify($formL120m01e.serializeData()),
            L120M01DForm05: JSON.stringify($L120M01DForm05.serializeData()),
            itemDscr03: getCkeditor("itemDscr03"),
            itemDscr05: getCkeditor("itemDscr05"),
            itemDscr06: $("#L120M01dForm06").find("#itemDscr06").val(),
            itemDscr01: $("#LMS1205S05Form01").find("#itemDscr01").val(),//getCkeditor("itemDscr01"),
            itemDscr02: $("#LMS1205S05Form04").find("#itemDscr02").val(),//getCkeditor("itemDscr02"),
            itemDscr07: getCkeditor("itemDscr07"),
            itemDscr09: getCkeditor("itemDscr09"),
            itemDscr0A: getCkeditor("itemDscr0A"),
            itemDscr0C: getCkeditor("itemDscr0C"),
            longCaseFlag: $("#LMS1205S05Form04").find("input[name='longCaseFlag']:radio:checked").val(),
            longCaseDscr: $("#LMS1205S05Form04").find("#longCaseDscr option:selected").val(),
            ffbody: getCkeditor("ffbody"),
            clsMinorPurpose: getCkeditor("clsMinorPurpose"),
            clsMinorLegality: getCkeditor("clsMinorLegality"),
            docCode: responseJSON.docCode,
            docType: responseJSON.docType,
            s05page: responseJSON.s05page,
            s07page: responseJSON.s07page,
            grpFlag: $("#LMS1205S05Form06").find("input[name='grpFlag']:radio:checked").val(),
            rltFlag: $("#LMS1205S05Form07").find("input[name='rltFlag']:radio:checked").val()
        };
        var $form = $("#" + CLSAction.mainFormId);
        if ($form.length && !$form.valid()) {
			// 為跳至未輸入選項上
			if(responseJSON.page=="03"){
				$("input.data-error").eq(0).focus();
			}
            //當畫面檢核沒過
            return false;
        }
        if ($form.length && $form.valid()) {
        	ilog.debug("<EMAIL>");
            $.ajax({
                handler: CLSAction.fhandle,
                formId: CLSAction.mainFormId,
                action: "saveAll",
                data: $.extend({
                    page: responseJSON.page,
                    mainId: responseJSON.mainId
                }, sendData),
                success: function(obj){
                    // 設定是否加送會審單位
                    responseJSON["_areaChk"] = obj.areaChk;
                    if (obj.noKeyMan) {
                        return API.showMessage(obj.noKeyMan);
                    }
                    CLSAction.setQueryData(obj);
                    API.triggerOpener("gridview", "reloadGrid");
                    if (afterFn) {
                        if (isSendBoss && responseJSON.docStatus == "01K") {
                        	
                        	if(obj.areaChk == "5" || obj.areaChk == "6"){
                        		$("#spectialSendTo").find("#spectialSendToShowForSign").hide();
                        	}
                        	
                            // 特殊分行會簽後呈主管覆核選擇界面
                            $("#spectialSendTo").thickbox({ // 使用選取的內容進行彈窗
                                //l120m01a.error1=請選擇
                                title: i18n.cls1141m01['l120m01a.error1'],
                                width: 300,
                                height: 100,
                                modal: true,
                                valign: "bottom",
                                align: "center",
                                i18n: i18n.def,
                                readOnly: false,
                                buttons: {
                                    "sure": function(){
                                        var spectialFlag = $("[name='spectialFlag']:radio:checked").val();
                                        afterFn(spectialFlag);
                                        $.thickbox.close();
                                    },
                                    "cancel": function(){
                                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                                            if (res) {
                                                $.thickbox.close();
                                            }
                                        });
                                    }
                                }
                            });
                        }
                        else {
                        	// 如果saveAll檢核訊息有值，則跳出儲存成功+檢核訊息視窗
        					if(!isSendBoss && obj.hasSaveAllCheckMsg == true){
        						API.showMessage(i18n.def['saveSuccess'] + obj.saveAllCheckMsg);
        					}
                            afterFn();
                        }
                    }
                    else {
                        //saveSuccess=儲存成功
                        API.showMessage(i18n.def['saveSuccess'] + obj.saveAllCheckMsg);
                    }
                    /*
                     if (afterFn) {
                     afterFn();
                     } else {
                     //saveSuccess=儲存成功
                     API.showMessage(i18n.def['saveSuccess']);
                     }
                     */
                }
            });
        }
        else 
            if (responseJSON.page == "19" && $("#unNormalForm").valid()) {
                // 異常通報儲存
                $.ajax({
                    type: "POST",
                    handler: CLSAction.fhandle,
                    data: $.extend({
                        formAction: "saveAll",
                        page: responseJSON.page,
                        mainId: responseJSON.mainId,
                        docCode: responseJSON.docCode,
                        docType: responseJSON.docType
                    }, (responseJSON.docCode == '4') ? extData : {}),
                    success: function(responseData){
                        API.triggerOpener("gridview", "reloadGrid");
                        if (afterFn) {
                            afterFn();
                        }
                        else {
                            //saveSuccess=儲存成功
                            API.showMessage(i18n.def['saveSuccess']);
                        }
                    }
                });
            } else if(responseJSON.page == "24" && !$("#CLS1201S24Form").valid()){
            	$("input.data-error").eq(0).focus();
            }
            else {
                $.ajax({
                    type: "POST",
                    handler: CLSAction.fhandle,
                    data: $.extend({
                        formAction: "saveAll",
                        page: responseJSON.page,
                        mainId: responseJSON.mainId
                    }, sendData),
                    success: function(responseData){
                        // 設定是否加送會審單位
                        responseJSON["_areaChk"] = responseData.areaChk;
                        if (responseData.noKeyMan) {
                            return API.showMessage(responseData.noKeyMan);
                        }
                        API.triggerOpener("gridview", "reloadGrid");
                        if (afterFn) {
                            if (isSendBoss && responseJSON.docStatus == "01K") {
                            	
                            	if(responseData.areaChk == "5" || responseData.areaChk == "6"){
                            		$("#spectialSendTo").find("#spectialSendToShowForSign").hide();
                            	}
                            	
                                // 特殊分行會簽後呈主管覆核選擇界面
                                $("#spectialSendTo").thickbox({ // 使用選取的內容進行彈窗
                                    //l120m01a.error1=請選擇
                                    title: i18n.cls1141m01['l120m01a.error1'],
                                    width: 300,
                                    height: 100,
                                    modal: true,
                                    valign: "bottom",
                                    align: "center",
                                    i18n: i18n.def,
                                    readOnly: false,
                                    buttons: {
                                        "sure": function(){
                                            var spectialFlag = $("[name='spectialFlag']:radio:checked").val();
                                            afterFn(spectialFlag);
                                            $.thickbox.close();
                                        },
                                        "cancel": function(){
                                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                                if (res) {
                                                    $.thickbox.close();
                                                }
                                            });
                                        }
                                    }
                                });
                            }
                            else {
                            	// 如果saveAll檢核訊息有值，則跳出儲存成功+檢核訊息視窗
            					if(!isSendBoss && responseData.hasSaveAllCheckMsg == true){
            						API.showMessage(i18n.def['saveSuccess'] + responseData.saveAllCheckMsg);
            					}
                                afterFn();
                            }
                        }
                        else {
                            API.showMessage(i18n.def['saveSuccess'] + responseData.saveAllCheckMsg);
                        }
                    }
                });
            }
        
    },
    /**
     * 檢查文件狀態是否鎖定
     */
    isReadOnly: function(){
        var result = false;
        var auth = (responseJSON ? responseJSON.Auth : {});
        //先檢查文件狀態是否可編輯
        //不等於待補件和編至中
        if (responseJSON.docStatus != "01O" && responseJSON.docStatus != "07O" && responseJSON.docStatus != "01K") {
            result = true;
        }
        //檢查是否可以編輯文件
        if (!auth.Modify) {
            result = true;
        }
        if (_openerLockDoc == "1") {
            result = true;
        }
        var unitType = userInfo.unitType || "";
        if (unitType == "2" || unitType == "4") {
            result = true;
        }
        return result;
        
    },
    printAction: function(){
        printAction();
    },
    sendBoss: function(arg){
        var spectialFlag = (arg != undefined && arg != null && arg != "") ? arg : "";
        var showMsg;
        $.ajax({
            handler: CLSAction.fhandle,
            action: "setBoss",
            data: {},
            success: function(json){
            	//J-110-0233 為讓 勞工紓困 RPA 能輸入簽章欄人員，格式改成 {6碼行編+" "+姓名}
            	$(".boss").setItems({
                    item: json.bossList,
                    space: true,format: "{value} {key}"
                });
                if (!json.noBossList2) {
                    $(".boss2").setItems({
                        item: json.bossList2,
                        space: true,format: "{value} {key}"
                    });
                }
                $("#sUnitManager option:eq(1)").attr("selected", true);
                $("#sUnitManager").attr("disabled", true);
                if (spectialFlag == "B") {
                    // other.msg147=是否呈授管處審核批覆？
                    showMsg = i18n.lmscommom["other.msg147"];
                }
                else {
                    //l120m01a.message01=是否呈主管覆核？
                    showMsg = i18n.cls1141m01["l120m01a.message01"];
                }
                CommonAPI.confirmMessage(lmsM01Json.checkConMsg(showMsg), function(b){
                    if (b) {
                        // 檢核簽報書底下異常通報明細是否有被停權
                        $.ajax({
                            handler: CLSAction.fhandle,
                            data: {
                                formAction: "checkIsStop",
                                docKind: responseJSON.docKind,
                                mainId: responseJSON.mainId,
                                isNew: false
                            },
                            success: function(obj){
                                //必要欄位檢核
                                $.ajax({
                                    handler: CLSAction.fhandle,
                                    action: "checkSend",
                                    data: {
                                    
                                        mainId: responseJSON.mainId,
                                        page: responseJSON.page,
                                        docCode: responseJSON.docCode,
                                        docType: responseJSON.docType,
                                        spectialFlag: spectialFlag
                                    },
                                    success: function(responseData){
                                    	ilog.debug("logic_1_after "+CLSAction.fhandle+" ::checkSend @CLS1141M01Page.js");
                                        var docStatus = responseData.docStatus;
                                        if (responseData.canSend) {
                                        	//經辦【呈主管】，抓 checkSend 回傳的 rejtMsgData
                                        	//主管【覆核】時，抓 CLSM01APage.js :: getRejtMsgData() 的 rejtMsgData
                                        	//J-109-0273_10702_B1001 Web e-Loan 調整申貸資料核對表內容
                                    		var promptMsg = getCheckListPromptMsg();
                                    		
                                    		//J-113-0349 新增綠色授信、社會責任授信提示訊息
                                            var esgMsg = getEsgMsg();
                                            if(esgMsg != ""){
                                            	promptMsg += esgMsg;
                                            }
                                            
                                        	if(promptMsg!="" && (docStatus=="01O" || docStatus=="02O" ||docStatus=="07O")){
                                        		cfm_ok(promptMsg).done(function(){
                                        			cfm_ok(responseData.rejtMsgData).done(function(){
                                        				sendBoss(responseData,docStatus,json);
                                                	});                            			
                                        		});
                                        	}	
                                        	else{
                                        		cfm_ok(responseData.rejtMsgData).done(function(){
                                    				sendBoss(responseData,docStatus,json);
                                            	});  
                                        	}
                                        	
                                        } else {
                                            //檢核不通過	            					
                                            return API.showErrorMessage(responseData.errorMsg);
                                        }
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });
    },
    /**
     選擇主管視窗
     **/
    selectBossBox: function(){
        //檢核通過
        $("#selectBossBox").thickbox({
            //l120m01a.btnSendBoss=呈主管覆核
            title: i18n.cls1141m01['l120m01a.btnSendBoss'],
            width: 500,
            height: 300,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            readOnly: false,
            buttons: {
                "sure": function(){
                    var selectBoss = $("select[name^=boss]").map(function(){
                        return $(this).val();
                    }).toArray();
                    
                    // 驗證主管是否都有選擇到
                    for (var i in selectBoss) {
                        if (selectBoss[i] == "") {
                            //l120m01a.error1=請選擇+ l120m01a.bossId=授信主管
                            return API.showErrorMessage(i18n.cls1141m01['l120m01a.error1'] + i18n.cls1141m01['l120m01a.bossId']);
                        }
                    }
                    // 驗證單位授權主管是否有選擇到
                    if ($("#sManager option:selected").val() == "") {
                        //l120m01a.error1=請選擇+ l120m01a.managerId=單位/授權主管
                        return API.showErrorMessage(i18n.cls1141m01['l120m01a.error1'] + i18n.cls1141m01['l120m01a.managerId']);
                    }
                    //驗證是否有重複的主管
                    if (checkArrayRepeat(selectBoss)) {
                        //主管人員名單重複請重新選擇
                        return API.showErrorMessage(i18n.cls1141m01['l120m01a.message02']);
                    }
                    // 驗證單位主管是否有選擇到
                    if ($("#sUnitManager option:selected").val() == "") {
                        //l120m01a.error1=請選擇+ l120m01a.managerId2=單位主管
                        return CommonAPI.showErrorMessage(i18n.cls1141m01['l120m01a.error1'] + i18n.cls1141m01['l120m01a.managerId2']);
                    }
                    //建立簽章欄
                    $.ajax({
                        type: "POST",
                        handler: CLSAction.fhandle,
                        action: "saveL120m01f",
                        data: {
                            mainId: responseJSON.mainId,
                            selectBoss: selectBoss,
                            manager: $("#sManager option:selected").val(),
                            AOPerson: $("#AOPerson option:selected").val(),
                            sUnitManager: $("#sUnitManager option:selected").val()
                        },
                        success: function(responseData){
                        	//20240312 調整產生異常通報表時機，避免簽章欄為不完整
		                    if (responseJSON.docCode == "4") {
		                        //分行端異常動報呈主管覆核要產生報表														
		                        $.ajax({
		                            handler: "lms1301formhandler",
		                            data: {
		                                formAction: "genlms1201r26"
		                            },
		                            success: function(json){
		                            
		                            }
		                        });
		                    }
		                    
                            if (responseData.docStatus == "01K") {
                                // 會簽後修改待覆核(02K)
                                flowAction({
                                    flowAction: "sendAWait"
                                });
                            }
                            else {
                                flowAction({
                                    flowAction: "waitCheck"
                                });
                            }                            
                        }
                    });
                    $.thickbox.close();
                },
                
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    },
    reloadRandomCodeSbr : function (){
        $("#showRandomCodeSbr").hide();
        $.ajax({
            handler: CLSAction.fhandle,
            action: "getRandomCodeSbr",
            data: {
                mainId: responseJSON.mainId
            },
            success: function(obj){
            	if(obj.showRandomCodeSbr =="Y"){
            		$("#showRandomCodeSbr").show();
                	$("#randomCodeSbr").val(obj.randomCodeSbr);
            	}else{
            		$("#showRandomCodeSbr").hide();
            	}
            }
        });
        
	},
	refreshPage: function(){
        location.reload();
    },
    initItem: function(){
        ilog.debug("@_M > initItem");
        //產生下拉選單
        var $div = $("#createCntrNo_brmp_creditCheckForm").find("[itemType]");
        var allKey = [];
        $div.each(function(){
            allKey.push($(this).attr("itemType"));
        });
        var item = API.loadCombos(allKey);
        $div.each(function(){
            var $obj = $(this);
            var itemType = $obj.attr("itemType");
            if (itemType) {
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: item[itemType],
                    format: "{value} - {key}",
                    size: $obj.attr("itemSize")
                });
            }
        });
    },
    /**
     * 取得該客戶最後一筆決策客群
     */
    getlast_brmp_termGroupRule: function(custId, dupNo, termGroupObj, termGroupSubObj, termGroupRuleResultTextObj){ 	
    	if(custId != "" && dupNo != ""){
    		$.ajax({
    	        type: "POST",
    	        handler: "cls1141m01formhandler",
    	        data: {formAction: "show_last_brmp_termGroupRule", "custId": custId, "dupNo": dupNo},
    	        success: function(jsonparm){
    	        	//console.log(jsonparm);
    	        	var resultmsg = "";
    	        	if(jsonparm.hasbrmp004){//有結果
    	                if(jsonparm.brmp004data.result.termGroup != null && jsonparm.brmp004data.result.termGroup != ""){//最終客群分類 "X":不承作 , "S":小額 , "N":普惠 , "G":優質, "E":行員
    	                	termGroupObj.val(jsonparm.brmp004data.result.termGroup);
    	                	resultmsg += termGroupObj.find(":selected").text();
    	                	if(jsonparm.brmp004data.result.applyDBRType != null){
        	                	termGroupSubObj.val(jsonparm.brmp004data.result.applyDBRType);	
        	                	if(jsonparm.brmp004data.result.applyDBRType == "A"){
                            		resultmsg += " (DBR上限15倍)";
                            	}
        	                }
        	                termGroupRuleResultTextObj.text(resultmsg);
    	                }else{
        	            	//沒有新的客群資訊, 看看原始的有沒有存, 有就帶回來
        	                if(termGroupObj.find("option:selected").val() != ""){
        	                	termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
        	                }else{//都沒有才跳提示
        	                	CommonAPI.showMessage(i18n.cls1141m01['L140S02A.chkTermGroup']);
        	                }
    	                	
    	                }   
    	                
    	            }else{
    	            	termGroupObj.val("");
    	            	termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
    	            	CommonAPI.showMessage(i18n.cls1141m01['L140S02A.chkTermGroup']);
    	            }
    	        }
    	    });
    	}else{
    		termGroupObj.val("");
        	termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
    	}
    }
};

$(document).ready(function(){
    CLSAction.initItem();
    CLSAction.controlDisplayObject();
    //lms7205Grid();
    // 控制異常通報產Excel按鈕
    if (responseJSON.docCode == "4") {
        $("#showExl").show();
        $(".hideUnNormal").show();
    }
    else {
        $("#showExl").hide();
        $(".hideUnNormal").hide();
    }
    //控制特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)按鈕顯示/隱藏 
    lmsM01Json.checkSpecitalBtn();
    //控制異常通報唯讀狀態
    if (userInfo.unitNo == "918" || userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
    userInfo.unitNo == "932" ||
    userInfo.unitNo == "933" ||
    userInfo.unitNo == "934" ||
    userInfo.unitNo == "935") {
        //當登入分行為授管處或營運中心時設定唯讀
        if (lmsM01Json.docStatus == "010" ||
        lmsM01Json.docStatus == "01O" ||
        lmsM01Json.docStatus == "01K" ||
        lmsM01Json.docStatus == "07O|0EO" ||
        lmsM01Json.docStatus == "07O") {
        }
        else {
            var $unNormalForm = $("#unNormalForm");
            $unNormalForm.readOnlyChilds(true);
            $unNormalForm.find("button").hide();
            thickboxOptions.readOnly = true;
        }
    }
    //依照不同系統控制顯示頁籤
    CLSAction.controlBook();
    CLSAction.isReadOnly();

    ilog.debug("<EMAIL>{"+"mainId= " +(responseJSON.mainId||'')+" }");    
    $.form.init({
        formHandler: CLSAction.fhandle,
        formAction: "queryL120M01A",
        formId: CLSAction.mainFormId,
        formPostData: {
            oid: responseJSON.oid,
            page: responseJSON.page,
            mainId: responseJSON.mainId
        },
        loadSuccess: function(jsonInit){          
            responseJSON["brnGroup"] = jsonInit.brnGroup;
            responseJSON["hqMeetFlag"] = jsonInit.hqMeetFlag;
            responseJSON["docCode"] = jsonInit.docCode;
            initDfd.resolve(jsonInit);
            
            CLSAction.setQueryData(jsonInit);
			
//			initDfdPanel27.resolve(jsonInit);
			
            //~~~
            check_clsRatingModel();
            if (CLSAction.isReadOnly()) {
                $("#buttonPanel").find("#btnReSend").hide();
            }
            else{
                $("#buttonPanel").find("#btnReSend").show();
            }
        }
    });
    
    
    //呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        var $newBossSpan = $("#newBossSpan");
        //清空原本的
        $newBossSpan.empty();
        var newdiv = "";
        var val = parseInt($(this).val(), 10);
        if (val > 1) {
            for (var i = 2, count = val + 1; i < count; i++) {
                newdiv += "<div>" + i18n.cls1141m01['l120m01a.no'] + i +
                i18n.cls1141m01['l120m01a.site'] +
                "&nbsp;" +
                i18n.cls1141m01['l120m01a.bossId'] +
                "&nbsp;&nbsp;&nbsp;<select id='boss" +
                i +
                "' name=boss" +
                i +
                " class='boss'/>&nbsp;" +
                "<span class='fg-buttonset'>" +
                "<span class='fg-child'>" +
                "<button b-id='boss" +
                i +
                "' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button' onclick='lmsM03Json.beforeOpen($(this).attr(\"b-id\"))' type='button' role='button' aria-disabled='false'>" +
                "<span class='ui-button-text'>" +
                "<span class='text-only'>常用主管</span>" +
                "</span></button></span></span></div>"
            }
            $newBossSpan.append(newdiv);
            var copyOption = $("#mainBoss").html();
            $("[name^=boss]").html(copyOption);
        }
    });
    
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(event, json){
		if (json) {
			CLSAction.saveAll(json.success, false);
		} else {
			CLSAction.saveAll();
		}
    }).end().find("#btnAccept").click(function(){
    }).end().find("#btnSLogIN").click(function(){
        //案件審核層級
        var obj = CommonAPI.loadCombos(["lms1205m01_caseLvl"]);
        $("#formCaseLvl").find("#caseLvl").setItems({
            item: obj.lms1205m01_caseLvl,
            format: "{value} - {key}",
            space: false
        });
        signCaseLvl();
    }).end().find("#btnSendCase").click(function(){
        var $sendTo = $("#sendTo");
        if (responseJSON.hqMeetFlag == "1") {
            $sendTo.find("#sendTo1").hide();
            $sendTo.find("#sendTo2").show();
            $sendTo.find("#sendTo3").show();
            //J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
            $sendTo.find("#sendTo4").show();
        }
        else 
            if (responseJSON.hqMeetFlag == "2") {
                $sendTo.find("#sendTo1").show();
                $sendTo.find("#sendTo2").hide();
                $sendTo.find("#sendTo3").show();
                //J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
                $sendTo.find("#sendTo4").show();
            }
            else 
                if (responseJSON.hqMeetFlag == "3") {
                    $sendTo.find("#sendTo1").show();
                    $sendTo.find("#sendTo2").show();
                    $sendTo.find("#sendTo3").hide();
                    //J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
                    $sendTo.find("#sendTo4").show();
                } else if(responseJSON.hqMeetFlag == "4"){
                	//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
                	$sendTo.find("#sendTo1").show();
                    $sendTo.find("#sendTo2").show();
                    $sendTo.find("#sendTo3").show();
                    $sendTo.find("#sendTo4").hide();
        		}
        $sendTo.thickbox({
            //l120m01a.btnSendTo=提會
            title: i18n.cls1141m01['l120m01a.btnSendTo'],
            width: 250,
            height: 150,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            readOnly: false,
            buttons: {
                "sure": function(){
                    if ($("input[name='hqMeetFlag']:radio:checked").val() == undefined) {
                        //l120m01a.error1=請選擇
                        return API.showErrorMessage(i18n.cls1141m01['l120m01a.error1']);
                    }
                    else {
                        $.ajax({
                            type: "POST",
                            handler: CLSAction.fhandle,
                            action: "sendTo",
                            data: {
                            
                                mainId: responseJSON.mainId,
                                hqMeetFlag: $("input[name='hqMeetFlag']:radio:checked").val()
                            },
                            success: function(responseData){
                                API.triggerOpener("gridview", "reloadGrid");
                                API.showPopMessage(i18n.def["runSuccess"], window.close);
                            }
                        });
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }).end().find("#btnLogeIN").click(function(){
        if (responseJSON.docStatus == "03K" || responseJSON.docStatus == "01K") {
            // 特殊分行已會簽
            signThickBox2();
            $(".sSpectial").show();
            $(".hSpectial").hide();
            //J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
            if (responseJSON.hqMeetFlag == "A" || responseJSON.hqMeetFlag == "B" || responseJSON.hqMeetFlag == "C" || responseJSON.hqMeetFlag == "D") {
                $("#spectialId").show();
            }
        }
        else 
            if (responseJSON.areaDocstatus == "LWC" && userInfo.unitNo == "025") {
                // 國金部會簽
                signThickBox();
            }
            else 
                if (responseJSON.areaDocstatus == "LWC" &&
                (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
                userInfo.unitNo == "932" ||
                userInfo.unitNo == "933" ||
                userInfo.unitNo == "934" ||
                userInfo.unitNo == "935")) {
                    // 營運中心會簽
                    signThickBox();
                }
                else 
                    if ((responseJSON.mainDocStatus == "L1H" ||
                    responseJSON.mainDocStatus == "L2H" ||
                    responseJSON.mainDocStatus == "L3H" ||
                    responseJSON.mainDocStatus == "L4H" ||
                    responseJSON.mainDocStatus == "L5H") &&
                    userInfo.unitNo == "918") {
                        $.ajax({
                            type: "POST",
                            handler: CLSAction.fhandle,
                            action: "querySignContent2",
                            data: {
                            
                                mainId: responseJSON.mainId
                            },
                            success: function(responseData){
                                $("#tItemDscr0A").val(responseData.L120M01aForm13.tItemDscr0A);
                                $("#tItemDscr0B").val(responseData.L120M01aForm13.tItemDscr0B);
                                $("#_tItemDscr0A").val(responseData.L120M01aForm13.tItemDscr0A);
                                $("#_tItemDscr0B").val(responseData.L120M01aForm13.tItemDscr0B);
                                $.ajax({
                                    type: "POST",
                                    handler: CLSAction.fhandle,
                                    data: {
                                        formAction: "querySignContent2a",
                                        mainId: responseJSON.mainId
                                    },
                                    success: function(responseData){
                                        //alert(JSON.stringify(responseData));
                                        $("#htItemDscr0C").val(responseData.L120M01aForm12.htItemDscr0C);
                                        $("#_htItemDscr0C").val(responseData.L120M01aForm12.htItemDscr0C);
                                        signThickBox2();
                                    }
                                });
                            }
                        });
                    }
                    else 
                        if (responseJSON.mainDocStatus == "L1C" &&
                        (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
                        userInfo.unitNo == "932" ||
                        userInfo.unitNo == "933" ||
                        userInfo.unitNo == "934" ||
                        userInfo.unitNo == "935")) {
                            $.ajax({ //查詢會簽內容{L120m01d.ItemType=7:營運中心說明及意見},{L120m01d.ItemType=8:會議決議}
                                type: "POST",
                                handler: CLSAction.fhandle,
                                action: "querySignContent3",
                                data: {
                                
                                    mainId: responseJSON.mainId
                                },
                                success: function(responseData){
                                    $("#tItemDscr07").val(responseData.L120M01aForm15.tItemDscr07);
                                    $("#tItemDscr08").val(responseData.L120M01aForm15.tItemDscr08);
                                    $("#_tItemDscr07").val(responseData.L120M01aForm15.tItemDscr07);
                                    $("#_tItemDscr08").val(responseData.L120M01aForm15.tItemDscr08);
                                    signThickBox3();
                                }
                            });
                        }
        //signContent2
    }).end().find("#btnView").click(function(){
        //調閱
        rSignContent();
    }).end().find("#btnCheckArea").click(function(){
    
        //區域中心覆核
        btnCheckActionArea();
    }).end().find("#btnCheck").click(function(){
        //覆核 :提供退回或呈下一關
        btnCheckAction();
    }).end().find("#btnAfterSign").click(function(){
        // 會簽後修改
        btnAfterSign();
    }).end().find("#btnAfterSign2").click(function(){
        // 由提會退回會簽後修改
        // other.msg121=是否確定執行此功能？
        CommonAPI.confirmMessage(i18n.lmscommom["other.msg121"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: CLSAction.fhandle,
                    data: {
                        formAction: "sendTo",
                        mainId: responseJSON.mainId,
                        hqMeetFlag: "",
                        backAfterSign: true
                    },
                    success: function(responseData){
                        //alert(JSON.stringify(responseData));
                        CommonAPI.triggerOpener("gridview", "reloadGrid");
                        API.showPopMessage(i18n.def["runSuccess"], window.close);
                    }
                });
            }
        });
    }).end().find("#btnCheckSea").click(function(){
        btnCheckSea();
    }).end().find("#btnCaseToChange").click(function(){
        // 案件改分派
        $.ajax({
            handler: CLSAction.fhandle,
            action: "setBoss",
            data: {
            
                changePeople: true
            },
            success: function(json){
                $("#hqAppraiser").setItems({
                    item: json.hqAppraiser,
                    space: false
                });
                $("#selectHqAppraiser").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.cls1141m01['l120m01a.error1a'],
                    width: 200,
                    height: 100,
                    modal: true,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.def,
                    readOnly: false,
                    buttons: {
                        "sure": function(){
                            $.ajax({
                                type: "POST",
                                handler: CLSAction.fhandle,
                                action: "saveHqAppraiser",
                                data: {
                                
                                    hqAppraiser: $("#hqAppraiser").val(),
                                    mainid: responseJSON.mainId
                                },
                                success: function(responseData){
                                    $("#LMS1205S01Form").find("#headAppraiser").val(DOMPurify.sanitize(responseData.headAppraiser));
                                    $("#LMS1205S01Form").find("#areaAppraiser").val(DOMPurify.sanitize(responseData.areaAppraiser));
                                    // 更新授信簽報書Grid內容
                                    API.triggerOpener("gridview", "reloadGrid");
                                    $.thickbox.close();
                                    API.showMessage(DOMPurify.sanitize(responseData.runSuccess));
                                }
                            });
                        },
                        "cancel": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            }
        });
    }).end().find("#btnSend").click(function(showMsg){
        if (CLSAction.isReadOnly()) {
            CLSAction.sendBoss(null);
        }
        else {
            //saveBeforeSend=執行將自動儲存資料，是否繼續此動作? 
            API.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {
                    CLSAction.saveAll(CLSAction.sendBoss, true);
                }
            });
            
        }
    }).end().find("#btnSend3").click(function(){
        API.confirmMessage(i18n.cls1141m01["l120m01a.message01"], function(b){
            if (b) {
                $.ajax({
                    handler: CLSAction.fhandle,
                    action: "sendBossSea",
                    data: {
                    
                        mainId: responseJSON.mainId
                    },
                    success: function(json){
                        // 更新授信簽報書Grid內容
                        API.triggerOpener("gridview", "reloadGrid");
                        API.showPopMessage(i18n.def["runSuccess"], window.close);
                    }
                });
            }
        });
    }).end().find("#btnBackUnit").click(function(){
    
        //退回分行更正:退分行待補件
        enterBackReason("6");
        //flowAction({flowAction:"waitCase"});
    }).end().find("#btnBackInHead").click(function(){
        //授管處_退回更正
        btnHeadBackAction();
    }).end().find("#btnBackSea").click(function(){
        //授管處_退回會簽意見
        API.confirmMessage(i18n.cls1141m01["L1205m01a.confirm1"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    action: "backSea",
                    data: {
                    
                        mainId: responseJSON.mainId
                    },
                    success: function(responseData){
                        // 更新授信簽報書Grid內容
                        API.triggerOpener("gridview", "reloadGrid");
                        API.showPopMessage(i18n.def["runSuccess"], window.close);
                    }
                });
            }
            else {
                $.thickbox.close();
            }
        });
    }).end().find("#btnCheckHead").click(function(){
    
        //授管處覆核
        btnChecActionkHead();
    }).end().find("#btnBackCase").click(function(){
    
        //撤件/陳復
        btnBackCaseBox();
    }).end().find("#btnBackCase2").click(function(){	
    	//J-109-0092_10702_B1001 Web e-Loan新增營運中心簽報書(審查中)加撤件功能
		//撤件
		API.confirmMessage(i18n.def['flow.confirmReturn'], function(res){
			if(res){
				signCaseCancelDate(function(){
					flowAction({
                        flowAction: "cancelCase"
                    });
					$.thickbox.close();
				});
			}
        });
		
	}).end().find("#btnPrint").click(function(showMsg){
        if (CLSAction.isReadOnly()) {
            CLSAction.printAction();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            API.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    CLSAction.saveAll(CLSAction.printAction);
                }
            });
        }
        
        
    }).end().find("#btnPrint").one("click", function(){
        var gridPrint = $("#printGrid").iGrid({
            handler: CLSAction.ghandle,
            height: 270,
            rowNum: 500,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            caption: "&nbsp;",
            hiddengrid: false,
            postData: {
                formAction: "",
                mainId: responseJSON.mainId
            },
            colModel: [{
                colHeader: i18n.cls1141m01['print.custName'],// "借款人名稱",
                name: 'custName',
                width: 120,
                sortable: false
            }, {
                colHeader: i18n.cls1141m01['print.rptNo'],// "報表編號",
                name: 'rptNo',
                align: "center",
                width: 40,
                sortable: false
            }, {
                colHeader: i18n.cls1141m01['print.rptName'],// "報表名稱",
                name: 'rptName',
                width: 70,
                sortable: false
            }, {
                colHeader: i18n.cls1141m01['print.cntrNo'],// "額度序號",
                name: 'cntrNo',
                align: "center",
                width: 50,
                sortable: false
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                colHeader: "rpt",
                name: 'rpt',
                hidden: true
            }, {
                colHeader: "custId",
                name: 'custId',
                hidden: true
            }, {
                colHeader: "dupNo",
                name: 'dupNo',
                hidden: true
            }, {
                colHeader: "refMainId",
                name: 'refMainId',
                hidden: true
            }, {
                colHeader: "keyCustId",
                name: 'keyCustId',
                hidden: true
            }, {
                colHeader: "keyDupNo",
                name: 'keyDupNo',
                hidden: true
            }]
        });
        
        var gridPrint2 = $("#printGrid2").iGrid({
            handler: CLSAction.ghandle,
            height: 270,
            rowNum: 500,
            rownumbers: true,
            multiselect: false,
            hideMultiselect: false,
            caption: "&nbsp;",
            hiddengrid: false,
            sortname: 'rptNo',
            postData: {
                formAction: "",
                mainId: responseJSON.mainId
            },
            colModel: [{
                colHeader: i18n.cls1141m01['print.custName'],// "借款人名稱",
                name: 'custName',
                width: 120,
                sortable: false
            }, {
                colHeader: i18n.cls1141m01['print.rptNo'],// "報表編號",
                name: 'rptNo',
                align: "center",
                width: 40,
                sortable: false
            }, {
                colHeader: i18n.cls1141m01['print.rptName'],// "報表名稱",
                name: 'rptName',
                width: 100,
                sortable: false
            }, {
                colHeader: "rptHandle", //組合方式
                name: 'rptHandle',
                hidden: true
            }, {
                colHeader: "cntrNo",
                name: 'cntrNo',
                hidden: true
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                colHeader: "rpt",
                name: 'rpt',
                hidden: true
            }, {
                colHeader: "custId",
                name: 'custId',
                hidden: true
            }, {
                colHeader: "dupNo",
                name: 'dupNo',
                hidden: true
            }, {
                colHeader: "refMainId",
                name: 'refMainId',
                hidden: true
            }, {
                colHeader: "rptHandle",
                name: 'rptHandle',
                hidden: true
            }, {
                colHeader: "meetingType",
                name: 'meetingType',
                hidden: true
            }, {
                colHeader: "branchType",
                name: 'branchType',
                hidden: true
            }]
        });
    }).end().find("#btnOpenLmsCase").click(function(){
        if ($("#lmss7305_panel").attr("open") == "true") {
            $("#lmss7305_panel").load("../../lms/lms7301m01", function(){
                queryL730m01a();
                $("#lmss7305_panel").attr("open", false);
            });
        }
        else {
            queryL730m01a();
        }
    }).end().find("#btnSendWaitLogin").click(function(){
        //總行提會待登錄
        btnSendWaitLogin();
    }).end().find("#btnCheckWaitApprove").click(function(){
        //總行提會待覆核
        btnCheckWaitApprove();
    }).end().find("#backToHeadFirst").click(function(){
        //待陳復退回審核中
        backToHeadFirst();
    })    /*
     .end().find("#btnCreateExl").click(function(){
     //異常通報產Excel
     $.form.submit({
     url: "../../simple/FileProcessingService",
     target: "_blank",
     data: {
     mainId: responseJSON.mainId,
     fileDownloadName: "LMS1301M01A.xls",
     serviceName: "lms1301xlsservice"
     }
     });
     })
     */
    .end().find("#btnExit").click(function(){
        setCloseConfirm(false);
    }).end().find("#btnNoCheck").click(function(){
        $.ajax({
            handler: CLSAction.fhandle,
            data: {
                formAction: "setBoss"
            },
            success: function(json){
                $(".boss").setItems({
                    item: json.bossList,
                    space: true
                });
                if (!json.noBossList2) {
                    $(".boss2").setItems({
                        item: json.bossList2,
                        space: true
                    });
                }
                $("#sUnitManager option:eq(1)").attr("selected", true);
                $("#sUnitManager").attr("disabled", true);
                //button.noCheckmsg=是否執行免批覆?
                CommonAPI.confirmMessage(i18n.cls1141m01["button.noCheckmsg"], function(b){
                    if (b) {
                        //必要欄位檢核
                        $.ajax({
                            type: "POST",
                            handler: CLSAction.fhandle,
                            data: {
                                formAction: "checkSend",
                                mainId: responseJSON.mainId,
                                page: responseJSON.page,
                                docCode: responseJSON.docCode,
                                docType: responseJSON.docType
                            },
                            success: function(responseData){
                            	ilog.debug("logic_2_after "+CLSAction.fhandle+" ::checkSend @CLS1141M01Page.js");
                            	if (responseData.canSend) {
                                    checkDate();
                                }
                                else {
                                    //檢核不通過
                                    return API.showErrorMessage(responseData.errorMsg);
                                }
                            }
                        });
                    }
                });
            }
        });
    }).end().find("#btnFindL140M01ACase").click(function(){
        $.ajax({
            handler: CLSAction.fhandle,
            data: {
                formAction: "findL140M01ACase"
            },
            success: function(json){
                if (json.msg) {
                    API.showMessage(json.msg);
                }
                else {
                    //CLS1141.112=額度明細表皆已產生
                    API.showMessage(i18n.cls1141m01["CLS1141.112"]);
                }
            }
        });
    }).end().find("#btnReSend").click(function(){
      $.thickbox.close();

      var _form = "createCntrNo_brmp_creditCheckForm";
      var $form = $('#'+_form);
      $("#"+_form).reset();
      if(true){
        $("#"+_form).find("#createCntrNo_brmp_creditCheck_introduceSrc").removeOptions(["2","4","8"]);
      }
      $("#"+_form).find(".brmp_creditCheck_custPos_rKindD").hide();
      $("#"+_form).find(".brmp_creditCheck_custPos_isLiveWithBorrowerSpan").hide();
      $("#"+_form).find("#tr_EsgGtype_brmp").hide();
      $("#"+_form).injectData({'createCntrNo_brmp_creditCheck_dupNo':'0'
        , 'createCntrNo_brmp_creditCheck_otherAdjustAmt':'0'
        , 'createCntrNo_brmp_creditCheck_compensationAmt':'0'
        , 'createCntrNo_brmp_creditCheck_esggnLoanFg':'N'    //在 reset 後，綠色支出類型 會被清空
        });
      $("#"+_form).find("#div_createCntrNo_brmp_creditCheck_induceFlagOrNot").hide();
      $("#"+_form).find("#tr_createCntrNo_brmp_creditCheck_induceFlagXV").hide();

      //先帶入預設值
      $.ajax({
      	type : "POST",
      	handler : CLSAction.fhandle,
      	data : {
      		formAction : "brmp_reSend_formInit"
      	},
      	success : function(responseData) {
      		$form.injectData({'createCntrNo_brmp_creditCheck_termGroup':DOMPurify.sanitize(responseData.termGroup) });
      		$form.injectData({'createCntrNo_brmp_creditCheck_creditLoanPurpose':DOMPurify.sanitize(responseData.creditLoanPurpose) });
      		$form.injectData({'createCntrNo_brmp_creditCheck_introduceSrc':DOMPurify.sanitize(responseData.introduceSrc) });
      		$form.injectData({'createCntrNo_brmp_creditCheck_pConBegEnd_fg':DOMPurify.sanitize(responseData.pConBegEnd_fg) });
      		IntroductionSource_PaperlessSigning.change();
      		if(responseData.introduceSrc=="3"){
      		    IntroductionSource_PaperlessSigning.loadMegaSubUnitNo(DOMPurify.sanitize(responseData.megaCode));
      		}
      		$form.injectData({'megaEmpNo':DOMPurify.sanitize(responseData.megaEmpNo) });
      		$form.injectData({'megaCode':DOMPurify.sanitize(responseData.megaCode) });
      		$form.injectData({'subUnitNo':DOMPurify.sanitize(responseData.subUnitNo) });
      		$form.injectData({'subEmpNo':DOMPurify.sanitize(responseData.subEmpNo) });
      		$form.injectData({'subEmpNm':DOMPurify.sanitize(responseData.subEmpNm) });
      		$form.injectData({'introCustId':DOMPurify.sanitize(responseData.introCustId) });
            $form.injectData({'introDupNo':DOMPurify.sanitize(responseData.introDupNo) });
            $form.injectData({'introCustName':DOMPurify.sanitize(responseData.introCustName) });

            $("#createCntrNo_brmp_creditCheckDiv").thickbox({
              title: '重新申貸資料', width: 860, height: 450, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
              buttons: {
                  "sure": function(){
                    if (!$("#"+_form).valid()) {
                          return;
                      }
                    var frmJSON = $("#"+_form).serializeData();

                    var createCntrNo_brmp_creditCheck_termGroup = frmJSON["createCntrNo_brmp_creditCheck_termGroup"];
                    var createCntrNo_brmp_creditCheck_creditLoanPurpose = frmJSON["createCntrNo_brmp_creditCheck_creditLoanPurpose"];
                    var createCntrNo_brmp_creditCheck_introduceSrc = frmJSON["createCntrNo_brmp_creditCheck_lnYear"];

                    brmp_creditCheck_existL120M01A(responseJSON.mainId, _form ).done(function(json_71_gen_tabDoc){
                        brmp_upd_caseDoc___tabDoc_cntrNo_npl(responseJSON.mainId, json_71_gen_tabDoc.tabMainId).done(function(){
                           dfd_import_simplifyFlag_D___L120S15A_content(responseJSON.mainId).done(function(){
                              dfd_import_property7_cntrNo(responseJSON.mainId, json_71_gen_tabDoc.c120m01a_oid , "Y" ).done(function(){
                                 dfd_fast_checkSend_CLS_L120M01A_countValue(responseJSON.mainId).done(function(){
                                    dfd_fastCntrNo_CLS_L120M01A_sendAML(responseJSON.mainId).done(function(json_sendAML){
                                       dfd_fastCntrNo_CLS_L120M01A_getAML(responseJSON.mainId, json_sendAML).done(function(){
                                         dfd_unlockDoc(responseJSON.mainId).done(function(){
                                             //=======================
                                             $.thickbox.close();
                                             //=======================
                                             //在 Grid 出現 新增的簽報書
                                             $("#gridview").trigger("reloadGrid");
                                             if(json_71_gen_tabDoc.rejectDesc && json_71_gen_tabDoc.rejectDesc!=''){
                                                API.showErrorMessage( json_71_gen_tabDoc.rejectDesc );
                                             }else{
                                                if(json_71_gen_tabDoc.statusMissing && json_71_gen_tabDoc.statusMissing!=''){
                                                    API.showErrorMessage( json_71_gen_tabDoc.statusMissing );
                                                }
                                             }
                                             //重新載入，更新頁籤
                                             API.showMessage("執行成功");
                                             if($("#TB_window1").length > 0 || $("#TB_window").length > 0){
                                                 $("#關閉").click(function(){
                                                     CLSAction.refreshPage();
                                                 });
                                             }
                                         });
                                       });
                                    });
                                  });
                              });
                           });
                        });
                    });
                  },
                  "cancel": function(){
                    $.thickbox.close();
                  }
              }
            });
        	//快速審核(信貸)>> 簽報書 >> 額度明細表 >> 新增歡喜信貸額度 
        	CLSAction.getlast_brmp_termGroupRule($("#mainCustId").val(), $("#mainDupNo").val(), 
        			$("select#createCntrNo_brmp_creditCheck_termGroup"), $("#createCntrNo_brmp_creditCheck_termGroupSub"), 
        			$("#span_createCntrNo_brmp_creditCheck_termGroupRuleResultText"));
      	}
      });
    }).end().find("#btnApproveUnestablshExl").click(function(){	
		//J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
		approveUnestablshExl();  
    });
    
    var tScore = 0;
    //計算授信報案考核表扣分與總扣分
    $("#formopenLmsCase").find(".mathit").blur(function(i){
        var mul1 = $(this).parent().parent().find("td").next().html();
        var mul2 = $(this).val();
        $(this).parent().next().html(mul1 * mul2);
        
        //總扣分初始化
        tScore = 0;
        $("#formopenLmsCase").find(".totalit").each(function(j){
            if ($(this).html().length == 0) {
                tScore += 0;
            }
            else {
                tScore += parseInt($(this).html(), 10);
            }
        });
        //將計算後的總扣分結果設定到畫面上
        $("#formopenLmsCase").find("#tmGrade").html(tScore);
    });
    //J-108-0316_10702_B1001 Web e-Loan調整國外部、國際金融業務分行與金控總部分行等原總處營業單位會簽流程
    if(responseJSON.page=="11"){
    	CLSAction.reloadRandomCodeSbr();
    }

    $("#createCntrNo_brmp_creditCheck_introduceSrc").change(function(){
        IntroductionSource_PaperlessSigning.change();
    });

    $("#megaCode").change(function(){
        IntroductionSource_PaperlessSigning.loadMegaSubUnitNo($(this).val());
    });

    $("#importCustOrComButton").click(function(){
        IntroductionSource_PaperlessSigning.importCustomerOrCompanyInfo();
    })

});

function check_clsRatingModel(){
//	if(responseJSON.page=="02"){
	if(responseJSON.page=="02_MODEL"){
		$.ajax({
	        type: "POST", handler: "cls1141m01formhandler",
	        action: "check_clsRatingModel", async: false,//同步
	        data: {
	            mainOid: responseJSON.mainOid
		    },
		    success: function(json){
		    	var divId = "div_check_clsRatingModel";
		    	var $msg = $("#"+divId);
		    	
		    	var lockClass = "z_check_clsRatingModel";
		    	if(json.errMsgClsRatingModel){
		        	if($msg.length>0){
		        	}else{
		        		$(".doc-tabs").before("<div id="+divId+" class='tit2'><span class='color-red'><b><span id='span_"+divId+"'/></b></span></div>");
		        	}
		        	$("#span_"+divId).val(json.errMsgClsRatingModel);
		        	$msg.show();
		        	
		        	//鎖住、不能切換 tab
		        	$(".doc-tabs").find("li:not(.ui-state-disabled)").addClass(lockClass);
		        	$("."+lockClass).addClass(" ui-state-disabled ");
		        }else{
		        	if($msg.length>0){
		        		$msg.hide();
		        		$("."+lockClass).removeClass(" ui-state-disabled ");
		        		$("."+lockClass).removeClass(lockClass);
		        	}
		        }
		    }
		});
	}     
}


//畫面切換table 所需設定之資料 如無設定 則直接切換
if(Boolean($("#btnSave").first())){
$.extend(window.tempSave,{
    handler: CLSAction.fhandle,
    action: "tempSave",
    beforeCheck: function(){
        var $form = $("#" + CLSAction.mainFormId);
        switch (responseJSON.page + "") {
            case "02":
                var count = $("#gridview").jqGrid('getGridParam', 'records');
                if (count == 0) {
                    API.showErrorMessage(i18n.cls1141m01('l120m01a.error24', {
                        'colName': ''
                    }));
                    return false;
                }
                else {
                    return true;
                }
                break;
            case "04":
            case "05":
			case "22":
                return $form.valid();
                break;
            default:
                return true
        }
        
    },
    sendData: function(){
        switch (responseJSON.page + "") {
            case "07":
                //綜合評估/往來彙總
                return $.extend({"ffbody_fixme":"Y", "ffbody_keepval": getCkeditor("ffbody")}, $("#L120M01dForm04").serializeData());
                break;
            case "08":
                //相關文件
                return $("#formL120m01e").serializeData();
                break;
            case "09":
                //補充說明
                return $("#L120M01dForm05").serializeData();
                break;
            case "18":
                //主管批示
                var extData = {
                    itemDscr06: $("#itemDscr06").val()
                };
                return extData;
                break;
            case "19":
                //異常通報表
                var extData = {
                    unNormalForm: JSON.stringify($("#unNormalForm").serializeData()),
                    haseBrid: $("#unNormalForm").find("input[name='hasBrid']:radio:checked").val()
                };
                return extData;
                break;
            default:
                var $form = $(CLSAction.mainFormId);
                if ($form) {
                    return $form.serializeData();
                }
                else {
                    return {};
                }
        }
    }
});
}else{
	setIgnoreTempSave(true);	
}

function cfm_ok(msg){
	var my_dfd = $.Deferred();
	if(msg && msg!=''){
		API.confirmMessage(msg, function(b){
            if (b) {
        		my_dfd.resolve();
            }else{
            	my_dfd.reject();
            }            	
        });
	}else{
		my_dfd.resolve();
	}
	return my_dfd.promise();
}
function sendBoss(responseData,docStatus,json){
	// 授管處和營運中心的呈主管不需額外挑主管與相關負責人員。
	// !(docStatus == "03K" || docStatus == "01K"   非「會簽」相關
    if (responseData.unitType != "S" && responseData.unitType != "A" &&
    	!(docStatus == "03K" || docStatus == "01K" ||
    	docStatus == "05K" ||docStatus == "06K" ||docStatus == "07K")){
        // 國內加上查詢AO人員並設定程式碼
        if (json.AOVal != undefined && json.AOVal != null && json.AOVal != "") {
            //CLS1141.069=系統搜尋到該客戶之帳戶管理員為
            //CLS1141.070=，是否需要變更？
            API.confirmMessage(i18n.cls1141m01["CLS1141.069"] + json.AOName + i18n.cls1141m01["CLS1141.070"], function(b){
                if (b) {
                    $("#bAOPerson").show();
                    CLSAction.queryPrintSeq(CLSAction.selectBossBox);
                }
                else {
                	var from = $("#selectBossForm");
                    $("#bAOPerson").hide();
                    from.find("#AOPerson").setItems({
                        item: json.bossListAO,
                        space: false
                    });
                    CLSAction.queryPrintSeq(CLSAction.selectBossBox);
                }
            });
        } else {
            CLSAction.queryPrintSeq(CLSAction.selectBossBox);
        }
        
        //20240312 調整產生異常通報表時機，避免簽章欄為不完整
//        if (responseJSON.docCode == "4") {
//            //分行端異常動報呈主管覆核要產生報表														
//            $.ajax({
//                handler: "lms1301formhandler",
//                data: {
//                    formAction: "genlms1201r26"
//                },
//                success: function(json){
//                
//                }
//            });
//        }
        
    } else {
        // 直接跑flow，簽章欄不用建，之後於"登錄說明及意見"時再建立
        if (docStatus == "03K") {
            // 會簽待覆核(04K)
            lmsM01Json.selectBossbox2(flowAction, {
                flowAction: "sendHWait"
            });
        } else if (docStatus == "01K") {
        		var from = $("#selectBossForm");
                // 國內加上查詢AO人員並設定程式碼
                if (json.AOVal != undefined && json.AOVal != null && json.AOVal != "") {
                    //CLS1141.069=系統搜尋到該客戶之帳戶管理員為
                    //CLS1141.070=，是否需要變更？
                    API.confirmMessage(i18n.cls1141m01["CLS1141.069"] + json.AOName + i18n.cls1141m01["CLS1141.070"], function(b){
                        if (b) {
                            $("#bAOPerson").show();
                            lmsM01Json.selectBossbox2(flowAction, {
                                flowAction: "sendAWait"
                            });                                                                        
                        } else {
                            $("#bAOPerson").hide();
                            from.find("#AOPerson").setItems({
                                item: json.bossListAO,
                                space: false
                            });
                            lmsM01Json.selectBossbox2(flowAction, {
                                flowAction: "sendAWait"
                            });                                                                   
                        }
                    });
                }else {
                    lmsM01Json.selectBossbox2(flowAction, {
                        flowAction: "sendAWait"
                    });

                }
                
        } else {
            flowAction({
            	flowAction: "waitCheck"
            });
        }
    }	
}

//J-110-0374 Web e-Loan 為加強區域營運中心授信案件之審查效率, 增加企/消金授信簽報案件經區域營運中心流程進度控管表
//登錄案件收件日期
function signCaseReceivedDate(){
	
	$("#formCaseReceivedDate").find("#caseReceivedDate").val(CommonAPI.getToday());
    $("#signCaseReceivedDate").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.cls1141m01['l120s17a.caseReceivedDate'],   //案件收件日期
        width: 400,
        height: 150,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                $.ajax({
                    type: "POST",
                    handler: "lms1201formhandler",
                    data: {
                        formAction: "setCaseReceivedDate",
                        caseReceivedDate: $("#formCaseReceivedDate").find("#caseReceivedDate").val(),
                        mainid: responseJSON.mainId
                    },
                    success: function(responseData){
                        
                        $.thickbox.close();
                        $.thickbox.close();
                        CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                    }
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function brmp_creditCheck_existL120M01A(caseMainId, param_formId){
	var my_dfd = $.Deferred();
	var frmJSON = $("#"+param_formId).serializeData();
	$.ajax({
        handler: "cls1141m01formhandler",
		formId: 'empty',
        action: "brmp_creditCheck_existL120M01A",
        data: {'caseMainId': caseMainId
			, 'introduceSrc': frmJSON["createCntrNo_brmp_creditCheck_introduceSrc"]
			, 'creditLoanPurpose': frmJSON["createCntrNo_brmp_creditCheck_creditLoanPurpose"]
			, 'termGroup': frmJSON["createCntrNo_brmp_creditCheck_termGroup"]
			, 'termGroupSub': frmJSON["createCntrNo_brmp_creditCheck_termGroupSub"]
			, 'megaEmpNo': frmJSON["megaEmpNo"]
            , 'megaCode': frmJSON["megaCode"]
            , 'subUnitNo': frmJSON["subUnitNo"]
            , 'subEmpNo': frmJSON["subEmpNo"]
            , 'subEmpNm': frmJSON["subEmpNm"]
            , 'introCustId': frmJSON["introCustId"]
            , 'introDupNo': frmJSON["introDupNo"]
            , 'introCustName': frmJSON["introCustName"]
            , 'pConBegEnd_fg': frmJSON["createCntrNo_brmp_creditCheck_pConBegEnd_fg"]
        },
        success: function(obj){
        	ilog.debug("brmp_creditCheck_existL120M01A {tabMainId="+(obj.tabMainId||'')+",rejectDesc="+(obj.rejectDesc||'')+",statusMissing="+(obj.statusMissing||'')+"}");

        	if(obj.tabMainId && obj.tabMainId != ""){
        		my_dfd.resolve({'tabMainId':(obj.tabMainId||'')
        			, 'rejectDesc': (obj.rejectDesc||'')
        			, 'statusMissing': (obj.statusMissing||'')
        			, 'c120m01a_oid': (obj.c120m01a_oid||'')
        			});
            	/*  之後的 CLS1141S03Action.getContractNo(...) , CLS1141S03Action.getBranchNplRatiosInfo(...) , updateCreditCardMembersLoanData(...)
                 	在 dfd_71_upd_caseDoc___tabDoc_cntrNo_npl(...) 去做
            	*/
        	}else{
        		ilog.debug("brmp_creditCheck_new {no} l140m01a_MainId");
        		//若無報價
        		my_dfd.reject();
        		//=======================

        		$.thickbox.close();
        	}
        },
        error: function(obj){
        	ilog.debug("brmp_creditCheck_new {fail}");
        	//若 BRMP 執行失敗，讓  user 也能看到已產出的簽報書，免得一直 click
    		my_dfd.reject();
    		//=======================
    		$.thickbox.close();
        },
        complete:function(result){
//            //重新載入，更新頁籤
//            API.showMessage("執行成功");
//            if($("#TB_window1").length > 0 || $("#TB_window").length > 0){
//                $("#關閉").click(function(){
//                    CLSAction.refreshPage();
//                });
//            }
        }
    });

	return my_dfd.promise();
}
function prepare_gen_71_brmp_caseDoc_tabDoc(custId, dupNo, termGroup, creditLoanPurpose, onlineCaseNo, custPos_custId, custPos_dupNo, custPos_rKindM, custPos_rKindD, lnYear, lnMonth
		, esggnLoanFg
		, esggtype, esggtypeZMemo, houseLoanFlag){
	var my_dfd = $.Deferred(); //為了檢核 決策平台 連 EJCIC狀況，在 server 端就先 run API：headAccountValidation
	$.ajax({
        type: "POST",
        handler: "cls1141m01formhandler",
        data: {formAction: "prepare_gen_71_brmp_caseDoc_tabDoc", "custId":custId, "dupNo":dupNo
        	, 'termGroup': termGroup, 'creditLoanPurpose':creditLoanPurpose, 'onlineCaseNo': onlineCaseNo
        	, "custPos_custId": custPos_custId, "custPos_dupNo":custPos_dupNo
        	, "custPos_rKindM": custPos_rKindM, "custPos_rKindD":custPos_rKindD
        	, "lnYear":lnYear, "lnMonth":lnMonth
        	, "esggnLoanFg":esggnLoanFg
        	, "esggtype":esggtype, "esggtypeZMemo":esggtypeZMemo,"houseLoanFlag":houseLoanFlag
        	},
        success: function(json){
        	my_dfd.resolve( json );
        }
    });
	return my_dfd.promise();
}
var IntroductionSource_PaperlessSigning = {

	show: function(introduceSrc){

		switch (introduceSrc) {
		    case '1':
		        $("#employeeDiv").show();
		        break;
		    case '3':
		        $("#megaSubCompanyDiv").show();
		        break;
		    case '5':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
		        break;
		    case '6':
		        $("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
		}
	},

	change: function(){

		$("#employeeDiv").hide();
		$("#megaSubCompanyDiv").hide();
		$("#customerOrCompanyDiv").hide();

		//行員引介
		$("#employeeDiv").find("input:text").val("");
		//金控子公司員工引介
		$("#megaSubCompanyDiv").find("select").val("");
		$("#megaSubCompanyDiv").find("input:text").val("");
		//往來企金戶所屬員工, 本行客戶引介
		$("#customerOrCompanyDiv").find("input:text").val("");

		IntroductionSource_PaperlessSigning.show($("#createCntrNo_brmp_creditCheck_introduceSrc").val());
	},

	openBox: function(){

		var buttons = {};
		buttons[i18n.def.close] = function(){
			$.thickbox.close();
        };

       	$("#openBox_realEstateIntroduction").thickbox({
            title: i18n.cls1151s01['page01.title.introductionRealEstateAgentInfo'],
            width: 550,
            height: 250,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: buttons
        });
	},

	importCustomerOrCompanyInfo: function(){

		AddCustAction.open({
    		handler: 'cls1151m01formhandler',
			action : 'importCustomerOrCompanyInfo',
			data : {
            },
			callback : function(json){
            	// 關掉 AddCustAction 的
            	$.thickbox.close();
				$("#introCustId").val(json.introCustId);
				$("#introDupNo").val(json.introDupNo);
				$("#introCustName").val(json.introCustName);
			}
		});
	},

	//帶入引介子公司分支代號
	loadMegaSubUnitNo: function(megaCode){

		if (megaCode == '') {
			$("#subUnitNo").setItems({});
			$("#subEmpNo").val("");
			$("#subEmpNm").val("");
		}
		else {
			var key_sub_unitNo = ('LNF13E_SUB_UNITNO_' + megaCode);
			var item = CommonAPI.loadCombos(key_sub_unitNo);
			$("#subUnitNo").setItems({
				item: item[key_sub_unitNo],
				format: "{value} - {key}"
			});
		}
	}
}

function brmp_upd_caseDoc___tabDoc_cntrNo_npl(caseMainId, tabMainId){
	//之後在 L120M01A 會增加 simplifyFlag
	return $.ajax({handler: "cls1141m01formhandler", action: "upd_caseDoc___tabDoc_cntrNo_npl", formId: 'empty',
		 data:{'caseMainId': caseMainId, 'simplifyFlag' : ''
			 , 'tabMainId': tabMainId, 'tab_printSeq' : 1
		 },success: function(json){
			 ilog.debug("{done}json_resp_upd_caseDoc___tabDoc_cntrNo_npl , {cntrNo="+(json.cntrNo||'')+"}");
		 }
	});
}

function dfd_import_simplifyFlag_D___L120S15A_content(caseMainId){
	return $.ajax({handler: "cls1141m01formhandler", action: "import_simplifyFlag_D___L120S15A_content", formId: 'empty',
		 data:{'mainId': caseMainId
		 },success: function(json){
			 ilog.debug("{done}import_simplifyFlag_D___L120S15A_content");
		 }
	});
}

function dfd_import_property7_cntrNo(caseMainId, custOid, importBisFlag ){
	var my_dfd = $.Deferred();
	$.ajax({handler: "cls1151m01formhandler", action: "addL140m01a", formId: 'empty',
		 data:{'mainId': caseMainId, 'custOid': custOid
		 },success: function(json_addL140m01a){
			 ilog.debug("{done}dfd_import_property7_cntrNo___{addL140m01a}");
			 if(importBisFlag =="Y"){
				 $.ajax({handler: "cls1141m01formhandler", action: "getBis", formId: 'empty',
					 data:{'mainId': caseMainId
						 , 'refresh' : false
						 , 'trigger_in_dfd_chain' : true
					 },success: function(json_getBis){
						 //因在 server 端寫 result.set(CapConstants.AJAX_NOTIFY_MESSAGE, ...)
						 $.thickbox.close();
						 ilog.debug("{done}dfd_import_property7_cntrNo___{getBis}");
						 //~~~
						 my_dfd.resolve();
					 }
			 	});
			}else{
				my_dfd.resolve();
			}
		 }
	});
	return my_dfd.promise();
}

function dfd_fast_checkSend_CLS_L120M01A_countValue(caseMainId){
	var my_dfd = $.Deferred();
	$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_checkSend_CLS_L120M01A_countValue", formId: 'empty',
		 data:{'mainId': caseMainId
		 },success: function(json){
			 ilog.debug("{done_dfd_fast_checkSend_CLS_L120M01A_countValue}");
			 if(json.msg){
				 ilog.debug("msg="+json.msg);
			 }
			 my_dfd.resolve();
		 }
	});
	return my_dfd.promise();
}

function dfd_fastCntrNo_CLS_L120M01A_sendAML(caseMainId){
	var my_dfd = $.Deferred();
	$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_CLS_L120M01A_sendAML", formId: 'empty',
		 data:{'mainId': caseMainId
		 },success: function(json){
			 ilog.debug("{done_dfd_fastCntrNo_CLS_L120M01A_sendAML}=>need_getAML="+(json.need_getAML||""));
			 if(json.msg){
				 ilog.debug("msg="+json.msg);
			 }
			 my_dfd.resolve(json);
		 }
	});
	return my_dfd.promise();
}

function dfd_fastCntrNo_CLS_L120M01A_getAML(caseMainId, json_sendAML){
	var my_dfd = $.Deferred();
	if(json_sendAML.need_getAML=="Y"){
		$.ajax({handler: "cls1141m01formhandler", action: "fastCntrNo_CLS_L120M01A_getAML", formId: 'empty',
			 data:{'mainId': caseMainId
			 },success: function(json){
				 ilog.debug("{done_dfd_fastCntrNo_CLS_L120M01A_getAML}");
				 if(json.msg){
					 ilog.debug("msg="+json.msg);
				 }
				 my_dfd.resolve();
			 }
		});
	}else{
		my_dfd.resolve();
	}
	return my_dfd.promise();
}

function dfd_unlockDoc(lockMainId){
	//需有 AuthType.Accept  return $.ajax({handler: 'unlockdocformhandler', action: "unlockDoc", formId: 'empty',
	return $.ajax({handler: 'cls1141m01formhandler', action: "unlockDoc_aft_genL120M01A", formId: 'empty',
        data: { 'lockMainId': lockMainId
        },
        success: function(responseData){
        	ilog.debug("{done}dfd_unlockDoc");
        }
    });
}