
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF491B;
import com.mega.eloan.lms.mfaloan.service.MisELF491BService;

/**
 * <pre>
 * 防杜代辦覆審控制檔
 * </pre>
 * 
 * @since 2019/4/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/4/17,EL08034,new
 *          </ul>
 */
@Service
public class MisELF491BServiceImpl extends AbstractMFAloanJdbc implements
MisELF491BService {

	@Override
	public List<ELF491B> findByBrNoIdDup(String brNo, String custId, String dupNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF491B.findByBrNoIdDup", new String[]{brNo, custId , dupNo});
		return toELF491B(rowData);
	}


	@Override
	public List<ELF491B> findByArea_PA_YM(String brno_area, String pa_ym){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF491B.findByArea_PA_YM", new String[]{brno_area , pa_ym});
		return toELF491B(rowData);
	}
	
	
	private List<ELF491B> toELF491B(List<Map<String, Object>> rowData){
		List<ELF491B> list = new ArrayList<ELF491B>();
		for (Map<String, Object> row : rowData) {
			ELF491B model = new ELF491B();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
