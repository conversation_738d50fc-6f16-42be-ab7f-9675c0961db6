/*
 * DWCommonService.java
 *
 * IBM Confidential
 * GBS Source Materials
 *
 * Copyright (c) 2011 IBM Corp.
 * All Rights Reserved.
 */
package com.mega.eloan.lms.eloandb.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.model.BELDFM02;

/**
 * <pre>
 * EloandbBASEService
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,UFO,new
 *          </ul>
 *          s
 */

public interface EloandbBASEService {

	public int update(String sql);

	public int update(String sql, Object... objects);

	public List<Map<String, Object>> findL120M01A_MaxCaseSeq(String arg);

	/**
	 * 取得授信科目最大seq
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @return
	 */
	public String findL140M01C_MaxSeq(String mainId);

	/**
	 * [個金]簽報書檢查chYN
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @return
	 */
	public List<Map<String, Object>> findCLSChkyn(String mainId);

	/**
	 * 6. 營運中心每日授權外授信案件清單
	 * 
	 * @param baseDate
	 * @param caseDept
	 * @param ctype
	 * @param brNo
	 * @param date
	 * @return
	 */
	public List<Map<String, Object>> findL120M01AJoinL120A01A_selAreaSendInfo(
			String date, String brNo);

	/**
	 * 取得徵信報告書 該Id 下的 連保人-自然人資料
	 * 
	 * @param custId
	 *            借款人資料
	 * @param dupNo
	 *            重覆序號
	 * @return 連保人-自然人資料
	 */
	public List<Map<String, Object>> findC140M04A_Natural(String custId,
			String dupNo);

	/**
	 * 取得徵信報告書 該Id 下的 連保人-法人資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 連保人-法人資料
	 */
	public List<Map<String, Object>> findC140M04B_Corporate(String custId,
			String dupNo);

	/**
	 * 取得徵信報告書 該Id 下的 連保人-自然人資料
	 * 
	 * @param custId
	 *            借款人資料
	 * @param dupNo
	 *            重覆序號
	 * @param mainId
	 *            徵新報告書mianid
	 * @return 連保人-自然人資料
	 */
	public List<Map<String, Object>> findC140M04A_Natural(String custId,
			String dupNo, String mainId);

	/**
	 * 取得徵信報告書 該Id 下的 連保人-法人資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param mainId
	 *            徵新報告書mianid
	 * @return 連保人-法人資料
	 */
	/**
	 * @param custId
	 * @param dupNo
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC140M04B_Corporate(String custId,
			String dupNo, String mainId);

	/**
	 * 取得徵信報告書 所需上傳資料
	 * 
	 * @param cesMainIds
	 *            徵信文件編號群組
	 * @return
	 */
	public List<Map<String, Object>> findC140M01A_selUpload(
			String... cesMainIds);

	/**
	 * 取得資信簡表 所需上傳資料
	 * 
	 * @param cesMainIds
	 *            徵信文件編號群組
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selUpload(
			String... cesMainIds);

	/**
	 * 取得徵信報告書 借款人基本資料1
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC140M01A_selCustData1(String mainId);

	/**
	 * 取得徵信報告書 借款人基本資料2
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC140JSON_selCustData2(String mainId);

	/**
	 * 取得徵信資信簡表 借款人基本資料3
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selCustData3(String mainId);

	/**
	 * 取得徵信報告書 使用者輸入之Id 下的 文件編號(徵信報告用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainId(String caseBrId,
			String custId);

	/**
	 * 取得徵信報告書 主要借款人Id 下的 文件編號(資信簡表用)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainId1(String caseBrId,
			String custId, String dupNo);

	/**
	 * 取得徵信報告書 主要借款人Id 下的 文件編號(徵信報告用)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainId2(String caseBrId,
			String custId, String dupNo);

	/**
	 * 取得徵信 主要借款人Id群組 下的 文件編號(範圍)(徵信報告用)
	 * 
	 * @param caseBrId
	 * @param mainId1
	 * @param mainId2
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainId2s(String caseBrId,
			String mainId1, String mainId2);

	/**
	 * 取得徵信 主要借款人Id群組 下的 文件編號(範圍)(徵信報告用)降冪排列
	 * 
	 * @param caseBrId
	 * @param mainId1
	 * @param mainId2
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainId2sd(String caseBrId,
			String mainId1, String mainId2);

	/**
	 * 取得徵信 主要借款人Id群組 下的 文件編號(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param caseBrId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainId2ss(String caseBrId);

	/**
	 * 取得徵信報告書 主要借款人Id 下的 文件編號(資信簡表用)
	 * 
	 * @param caseBrId
	 * @param mainId1
	 * @param mainId2
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainIda(String caseBrId,
			String mainId1, String mainId2);

	/**
	 * 取得徵信報告書 使用者輸入之Id 下的 文件編號(資信簡表用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainIdb(String caseBrId,
			String custId);

	/**
	 * 取得徵信 主要借款人Id群組 下的 文件編號(範圍)(資信簡表用，不限制文件狀態)
	 * 
	 * @param caseBrId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainIdc(String caseBrId);

	/**
	 * 取得擔保品文件編號 (by 分行)
	 * 
	 * @param branchId
	 * @return
	 */
	public List<Map<String, Object>> findCms_selMainId(String branchId);

	/**
	 * 取得擔保品 簽報書主要借款人Id群組 下的 文件編號
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findCms_selMainId2(String mainId);

	/**
	 * 取得擔保品 大類 下的 文件編號
	 * 
	 * @param collTyp1
	 * @return
	 */
	public List<Map<String, Object>> findCms_selMainId3(String collTyp1);

	/**
	 * 取得徵信報告書 文件編號下的 營運概況
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01C_selL120s01e1(String mainId);

	/**
	 * 取得徵信報告書 文件編號下的 營運概況報表年度
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01C_selYear1(String mainId);

	/**
	 * 取得徵信報告書 文件編號下的 財務狀況
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01C_selL120s01e2(String mainId);

	/**
	 * 取得徵信報告書 文件編號下的 財務狀況報表年度
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01C_selYear2(String mainId);

	/**
	 * 取得資信簡表 文件編號下的 存放款及外匯往來情形
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120JSON_selL120s01f1(String mainId);

	/**
	 * 取得資信簡表 文件編號下的集團代碼
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120JSON_selL120s05a(String mainId);

	/**
	 * 取得徵信報告書 綜合評估及敘做理由
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC140SFFF_selFfbody(String mainId);

	/**
	 * 取得徵信報告書 文件編號下的 營運概況及財務狀況分析與評估
	 * 
	 * @param mainId
	 * @return
	 */

	public List<Map<String, Object>> findC140M01A_selL120s01g(String mainId);

	/**
	 * (個金)查詢不重覆額度序號的餘額
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findLc241m01bQuotaAmtBymainId(String mainId);

	/**
	 * (企金)查詢不重覆額度序號的餘額
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL170BquotaAmtBymainId(String mainId);

	/**
	 * (企金)找CES.F101A01A , CES.F101M01A 資料
	 * 
	 * @param mainId
	 * @return
	 */

	public List<Map<String, Object>> findCESF101A01AJoinCESF101M01A(
			String custId, String dupNo, String brno);

	/**
	 * (企金)找 CES.F101S01A BymainId (營業收入,營業利益,稅前損益)
	 * 
	 * @param mainId
	 * @return
	 */

	public List<Map<String, Object>> CESF101S01ABymaiinId(String mainId);

	public List<Map<String, Object>> CESF101S01AByMainIdSubNo(String mainId,
			String subNo1, String subNo2, String subNo3);

	/**
	 * (企金)找CESF101S01BBymainIdAndratioNo (XX比率)
	 * 
	 * @param mainId
	 * @param ratioNoObject
	 * @return
	 */

	public Map<String, Object> CESF101S01BBymainIdAndratioNo(String mainId,
			String ratioNo);

	/**
	 * 取得 日期不小於條件的L170M01A的retrialDate
	 * 
	 * @param mainId
	 * @param ratioNoObject
	 * @return
	 */

	public Map<String, Object> getL170M01AEData(String custId, String dupNo,
			Date retrialDate);

	List<Map<String, Object>> LMS491selByBranch(String branch);

	/**
	 * 取得徵信報告書 主要營業項目
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return List
	 */
	@SuppressWarnings("rawtypes")
	public List findBusi(String mainId);

	/**
	 * 取得徵信報告書 其他內容(連保人)
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return List
	 */
	@SuppressWarnings("rawtypes")
	public List selPctitle(String mainId);

	/**
	 * 取得徵信報告書 其他內容(大陸投資)
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return List
	 */
	@SuppressWarnings("rawtypes")
	public List selCh6_lstock_inv(String mainId);

	/**
	 * 取得徵信報告書 基金及投資(GAAP) / 採用權益法之投資(IFRS) 結構化表格
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return List
	 */
	@SuppressWarnings("rawtypes")
	public List selCh6_lstock_inv_JSON(String mainId);

	/**
	 * 取得徵信報告書 基金及投資(GAAP) / 採用權益法之投資(IFRS) 結構化表格：財簽公司之選項
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return List
	 */
	@SuppressWarnings("rawtypes")
	public List selCh6_lstock_inv_VAL(String mainId);

	/**
	 * 國內授信取得大陸投資內容
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return
	 */
	public List<Map<String, Object>> selCh6_LLand_inv(String mainId);

	/**
	 * 取得資料簡表最新一筆已覆核資料
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findC120M01A_selOrderByUpdateTime(
			String caseBrId, String custId, String dupNo);

	/**
	 * 取得徵信報告書 相關文件之徵信報告書
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return List
	 */
	@SuppressWarnings("rawtypes")
	public List C140M01A_selCustname(String mainId);

	/**
	 * 取得徵信報告書隸屬資信簡表
	 * 
	 * @param mainId
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List C140M01A_selCustname2(String mainId);

	/**
	 * 取得徵信報告書 相關文件之資信簡表
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List C120M01A_selCustname(String mainId);

	/**
	 * 取得擔保品系統資料 相關文件之擔保品
	 * 
	 * @param mainId
	 *            徵信文件編號
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List C100M01_selCustname(String mainId);

	/**
	 * 覆審管理報表(TYPE4)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	public List<Map<String, Object>> findlrDatellrDate(String custId,
			String dupNo, String brNo);

	/**
	 * 覆審管理報表(TYPE3)---411ovs Find L170M01B And L180M01B By 額度序號
	 * 
	 * @param custId411ovs
	 * @param dupNo411ovs
	 * @param cntrno411ovs
	 * @param cntrno411ovs
	 * @param cycmn411ovs
	 * @return
	 */

	public List<Map<String, Object>> find170And180ByCntrno411ovs(
			String custId411ovs, String dupNo411ovs, String cntrno411ovs,
			Date cycmn411ovs);

	/**
	 * 覆審管理報表(TYPE3)---411ovs Find L170M01B And L180M01B By 統一編號
	 * 
	 * @param custId411ovs
	 * @param dupNo411ovs
	 * @param cycmn411ovs
	 * @return
	 */
	public List<Map<String, Object>> find170And180ByCustId411ovs(
			String custId411ovs, String dupNo411ovs, Date cycmn411ovs);

	/**
	 * 取得輸入資料檢誤完成判斷
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findChkyn(String mainId);

	/**
	 * 依照徵信mainId查詢徵信Oid
	 * 
	 * @param cesMainId
	 * @return
	 */
	public String C140M01A_selCesOid(String cesMainId);

	/**
	 * 複製徵信報告書主表
	 * 
	 * @param oid
	 */
	public void C140M01A_copy(String mainId);

	/**
	 * 複製徵信報告第四章登錄主要負責人連保人資信狀況資料主檔
	 * 
	 * @param mainId
	 */
	public void C140M04A_copy(String mainId, String cesMainId);

	/**
	 * 複製徵信報告第四章 登錄保證公司/母公司營運及財務概況
	 * 
	 * @param mainId
	 */
	public void C140M04B_copy(String mainId, String cesMainId);

	/**
	 * 取得徵信財務預估表一覽(徵信報告用)
	 * 
	 * @param caseBrId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> F101M01A_selFss(String caseBrId,
			String custId, String dupNo);

	/**
	 * 複製徵信報告第柒章主檔(財務分析)
	 * 
	 * @param mainId
	 */
	public void C140M07A_copy(String mainId, String cesMainId, String tab);

	/**
	 * 複製徵信報告第柒章 財務分析(子)
	 * 
	 * @param mainId
	 */
	public void C140S07A_copy(String mainId, String cesMainId);

	/**
	 * 複製徵信報告JSON內容
	 * 
	 * @param mainId
	 */
	public void C140JSON_copy(String mainId, String cesMainId, String tab);

	/**
	 * 複製徵信報告註記說明內容
	 * 
	 * @param mainId
	 */
	public void C140SDSC_copy(String mainId, String cesMainId, String tab);

	/**
	 * 複製徵信報告檔案
	 * 
	 * @param mainId
	 * @param cesMainId
	 */
	public void BDOCFILE_copy(String mainId, String cesMainId);

	/**
	 * 複製徵信報告自由格式
	 * 
	 * @param mainId
	 */
	public void C140SFFF_copy(String mainId, String cesMainId, String tab);

	/**
	 * 複製徵信報告引用文件儲存
	 * 
	 * @param mainId
	 */
	public void BRELATED_copy(String mainId);

	/**
	 * 刪除徵信報告書主表
	 * 
	 * @param mainId
	 */
	public void C140M01A_deleteA1(String mainId);

	/**
	 * 刪除徵信報告書主表
	 * 
	 * @param mainId
	 */
	public void C140M01A_delete61(String mainId);

	/**
	 * 刪除徵信報告第四章登錄主要負責人連保人資信狀況資料主檔
	 * 
	 * @param mainId
	 */
	public void C140M04A_delete(String mainId);

	/**
	 * 刪除徵信報告第四章 登錄保證公司/母公司營運及財務概況
	 * 
	 * @param mainId
	 */
	public void C140M04B_delete(String mainId);

	/**
	 * 刪除徵信報告第柒章主檔(財務分析)
	 * 
	 * @param mainId
	 */
	public void C140M07A_delete(String mainId);

	/**
	 * 刪除徵信報告第柒章 財務分析(子)
	 * 
	 * @param mainId
	 */
	public void C140S07A_delete(String mainId);

	/**
	 * 刪除徵信報告JSON內容
	 * 
	 * @param mainId
	 */
	public void C140JSON_delete(String mainId, String tab);

	/**
	 * 刪除徵信報告註記說明內容
	 * 
	 * @param mainId
	 */
	public void C140SDSC_delete(String mainId, String tab);

	/**
	 * 刪除徵信報告自由格式
	 * 
	 * @param mainId
	 */
	public void C140SFFF_delete(String mainId, String tab);

	/**
	 * 更新徵信報告書主表(中長期)
	 * 
	 * @param cesOid
	 * @param mainId
	 */
	public void C140M01A_updateA1(String cesOid, String mainId);

	/**
	 * 更新徵信報告書主表(產銷)
	 * 
	 * @param cesOid
	 * @param mainId
	 */
	public void C140M01A_update61(String cesOid, String mainId);

	/**
	 * 更新徵信報告書主表(產業概況)
	 * 
	 * @param cesOid
	 * @param mainId
	 */
	public void C140M01A_update51(String cesOid, String mainId);

	/**
	 * 刪除-常董會及申報案件明細檔(刪除全部資料)
	 */
	public void deleteL784s07(String year, String month, String mainId);

	/**
	 * 查詢-l784m01a資料
	 */
	public Map<String, Object> selL784m01abyRPTTYPEYear(String rpttype,
			String year);

	/**
	 * 查詢-常董會及申報案件明細檔 by yy mm
	 */
	public List<Map<String, Object>> selL784s07byBrnoYearMonthBrNo(String brNo,
			String year, String month, String mainId);

	/**
	 * 查詢-常董會及申報案件明細檔 by brno yy mm
	 */
	public List<Map<String, Object>> selL784s07byBrnoYearMonth(String year,
			String month, String mainId);

	/**
	 * (管理報表)2. 已敘做授信案件清單
	 * 
	 * @param ovUnitNo
	 * @param ovUnitNo2
	 * @param docStatusCode
	 * @param docKind
	 * @param startDate
	 * @param endDate
	 * @param docStatusCode2
	 * @param typeCd1
	 * @param typeCd2
	 * @return
	 */
	public List<Map<String, Object>> findforNewReportType2ByBrNo(
			String ovUnitNo2, String docStatusCode, String docKind,
			String startDate, String endDate, String docStatusCode2,
			String typeCd1, String typeCd2);

	/**
	 * (管理報表)2. 已敘做授信案件清單 海外總行泰國0E3、加拿大0E1
	 * 
	 * @param ovUnitNo
	 * @param ovUnitNo2
	 * @param docStatusCode
	 * @param docKind
	 * @param startDate
	 * @param endDate
	 * @param docStatusCode2
	 * @param typeCd1
	 * @param typeCd2
	 * @return
	 */
	public List<Map<String, Object>> findforNewReportType2ByAccGroup(
			String ovUnitNo2, String docStatusCode, String docKind,
			String startDate, String endDate, String docStatusCode2,
			String typeCd1, String typeCd2);

	/**
	 * J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
	 * 
	 * @param docStatusCode
	 * @param country
	 * @param ovUnitNo2
	 * @param startDate
	 * @param endDate
	 * @param docStatusCode2
	 * @param typeCd1
	 * @param typeCd2
	 * @return
	 */
	public List<Map<String, Object>> findforNewReportType2ByCountryHead(
			String docStatusCode, String country, String ovUnitNo2,
			String startDate, String endDate, String docStatusCode2,
			String typeCd1, String typeCd2);

	/**
	 * (管理報表)2. 已敘做授信案件清單 營運中心
	 * 
	 * @param ovUnitNoName
	 * @param ovUnitNo
	 * @param baseDateStr
	 * @param date
	 * @param caseDept
	 * @param caseDept2
	 * @param caseDept2
	 * @return
	 */
	public List<Map<String, Object>> findforNewReportType2ByArea(
			String ovUnitNo2, String docStatusCode, String docStatusCode2,
			String startDate, String endDate);

	// /**
	// * 複製徵信財務預估表
	// *
	// * @param mainIds
	// * @return
	// */
	// public int F101M01A_copy(String mainIds, String contents);

	// /**
	// * 刪除徵信財務預估表
	// *
	// * @param oids
	// * @return
	// */
	// public int F101M01A_delete(String oids, String contents);

	// /**
	// * 複製徵信財務預估表子表1
	// *
	// * @param mainIds
	// * @return
	// */
	// public int F101S01A_copy(String mainIds, String contents);

	// /**
	// * 刪除徵信財務預估表子表1
	// *
	// * @param oids
	// * @return
	// */
	// public int F101S01A_delete(String oids, String contents);

	// /**
	// * 複製徵信財務預估表子表2
	// *
	// * @param mainIds
	// * @return
	// */
	// public int F101S01B_copy(String mainIds, String contents);

	// /**
	// * 刪除徵信財務預估表子表2
	// *
	// * @param oids
	// * @return
	// */
	// public int F101S01B_delete(String oids, String contents);

	// /**
	// * 刪除企金信評
	// *
	// * @param mainId
	// * @param custId
	// * @param dupNo
	// */
	// public void deleteTrust(String mainId, String custId, String dupNo);

	/**
	 * 依照MainId,CustId,DupNo 刪除所有符合的L120S01E
	 * 
	 * @param mainId
	 *            mainId
	 * @param custId
	 *            custId
	 * @param dupNo
	 *            dupNo
	 */
	public void L120S01E_delByMainIdCustData(String mainId, String custId,
			String dupNo);

	/**
	 * 查詢利害關係人授信條件對照表
	 * 
	 * @param custId
	 * @param dupNo
	 * @param caseBrid
	 * @return
	 */
	public List<Map<String, Object>> L140M01A_selLihai(String custId,
			String dupNo, String caseBrid);

	/**
	 * 取得動審表內的額度明細表借款人
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<Map<String, Object>> 借款人名稱
	 */
	public List<Map<String, Object>> findL140M01AByMainId(String mainId);

	/**
	 * 取得動審表內的額度明細表借款人資訊
	 * 
	 * @param mainId
	 *            mainId
	 * @return List<Map<String, Object>> 借款人名稱、統編...
	 */
	public List<Map<String, Object>> findL140M01AInfoByMainId(String mainId);

	/**
	 * 用簽報書MAINID取得該所屬的額度明細表資料
	 * 
	 * @param mainId
	 * @return
	 */

	public List<Map<String, Object>> findC140M01AByC120M01C(String mainId);

	/**
	 * C241M01B 前日結欠餘額合計
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findLc241m01bQuotaAmtBymainId2(
			String mainId);

	/**
	 * L170M01B 前日結欠餘額合計
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL170BquotaAmtBymainId2(String mainId);

	/**
	 * 刪除L170M01E 外部信評
	 * 
	 * @param mainId
	 */
	public void deleteL170M01EByTypeCN(String mainId);

	/**
	 * 依前次已傳送覆審名單改列本次為暫不覆審
	 * 
	 * @param newNCkdMemo
	 * @param mainIdA
	 * @param mainIdB
	 */
	void updateForNck(String newNCkdMemo, Date newNextnwdt, String mainIdA,
			String mainIdB);

	/**
	 * 1.取得流程主檔、歷史檔的資料移至 現況檔
	 * 
	 * @param instid
	 *            流程編號
	 */
	void insertFlowWinst(String instid);

	/**
	 * 2.取得流程明細、歷史檔的資料移至 現況檔
	 * 
	 * @param instid
	 *            流程編號
	 */
	void insertFlowSeq(String instid);

	/**
	 * 3.移除流程主檔歷史檔中的記錄
	 * 
	 * @param instid
	 *            流程編號
	 */
	void delFlowWinst(String instid);

	/**
	 * 4.移除流程明細歷史檔中的記錄
	 * 
	 * @param instid
	 *            流程編號
	 */
	void delFlowSeq(String instid);

	/**
	 * 5.新增一筆流程明細檔
	 * 
	 * @param instid
	 *            流程編號
	 */
	void insertFolwNewSeq(String instid);

	/**
	 * 5.新增一筆流程明細檔For180M01A
	 * 
	 * @param instid
	 *            流程編號
	 */
	void insertFolwNewSeqFor180M01A(String instid);

	/**
	 * 移除流程主檔現況檔中的記錄
	 * 
	 * @param instid
	 *            流程編號
	 */
	void delNowFlowWinst(String instid);

	/**
	 * 移除流程明細現況檔中的記錄
	 * 
	 * @param instid
	 *            流程編號
	 */
	void delNowFlowSeq(String instid);

	/**
	 * 查詢流程主檔現況檔中的記錄
	 * 
	 * @param instid
	 *            流程編號
	 */
	Map<String, Object> selNowFlowWinst(String instid);

	/**
	 * <!-- 查詢是否存在該額度序號-->
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return 筆數
	 */
	int findL140M01EByCustIdAndDupNoAndCntrno(String custId, String dupNo,
			String cntrNo);

	// /**
	// * 查詢該ID不重覆的 聯行額度明細表
	// *
	// * @param custId
	// * 客戶統編
	// * @param dupNo
	// * 重覆序號
	// * @param ownBrid
	// * 分行代號
	// * @return 資料
	// */
	// public List<Map<String, Object>> findL141M01ADistinctByCustIdAndDupNo(
	// String custId, String dupNo, String ownBrid);

	/**
	 * oin L170A101A ,L170M01F
	 * 
	 * @param docStatus
	 *            文件狀態
	 * @param brNo
	 *            分行代號
	 * @param retrialDate1
	 *            起日
	 * @param retrialDate2
	 *            迄日
	 * @param custId
	 *            客戶統編
	 * @param search
	 *            order by 條件
	 * @return
	 */
	List<Map<String, Object>> findL170M10AJoin(String docStatus, String brNo,
			String retrialDate1, String retrialDate2, String custId,
			String custName, ISearch search);

	/**
	 * 找出大於今天幾天內的 mainId
	 * 
	 * @param tableName
	 *            表格名稱
	 * @param days
	 *            天數
	 * @return mainId List
	 * */
	public List<Map<String, Object>> findDeleteTime(String tableName, int days);

	/**
	 * 刪除byMainId
	 * 
	 * @param table
	 *            參數
	 */
	public int deleteByMainId(String table, String... mainIds);

	/**
	 * 刪除BRelated
	 * 
	 * @param table
	 *            參數
	 */
	public int deleteBRelatedByMainId1(String table, String... mainIds);

	/**
	 * 查找出的 retrialkind 可能是 4;98(09)，還需要再「細分」
	 */
	public List<Map<String, Object>> findRetrialKind_in_c240m01a_raw(
			String c240m01a_mainId, String ruleNo);

	/**
	 * 取得C240M01B的資料 排序使用C241M01A的序號
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findC240M01BOrderByC241M01ASEQ(
			String mainId);

	/**
	 * 查詢最新額度資訊檔
	 * 
	 * @param srcMainId
	 *            額度明細表 mainId
	 * @param docstatus
	 *            主檔文件狀態
	 * @return 額度資訊檔
	 */
	public Map<String, Object> findL230S01AMaxDATADATE(String srcMainId,
			String docstatus);

	/**
	 * 取明細最大號
	 * 
	 * @param mainId
	 * @return
	 */
	Map<String, Object> findMaxProjectNo(String mainId);

	/**
	 * 修改預計覆審日=>修改實際覆審日
	 * 
	 * @param retrialdate
	 * @param mainId
	 */
	void updateC241m01a(Date retrialdate, String mainId);

	/**
	 * 刪除借款人時同步刪除額度明細表共同借款人
	 * 
	 * @param mainId
	 *            簽報書mainID
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 */
	void deleteL140M01JByL120S01(String mainId, String custId, String dupNo);

	/**
	 * 查詢額度明細表平均動用率引進
	 * 
	 * @param search
	 *            查詢條件
	 * @param custId
	 *            簽報書mainId
	 * @param mainId
	 *            額度明細表mainId
	 * @return pageData
	 */
	Page<Map<String, Object>> findCESL120S01A(ISearch search, String brId,
			String custId, String dupNo, String cntrNo);

	/**
	 * 查詢相關文件下非本案簽報書夾帶個金簽報書
	 * 
	 * @param docOids
	 *            所選夾帶個金簽報書Oid
	 * @param mainId
	 *            本案簽報書文件編號
	 * @return
	 */
	public List<Map<String, Object>> selL120m01eCls(String[] docOids,
			String mainId);

	/**
	 * 查詢 敘做無自用住宅購屋放款明細表
	 * 
	 * @param brno
	 *            分行別
	 * @param dateType
	 *            日期類別
	 * @param date
	 *            查詢日期
	 * @return
	 */
	List<Map<String, Object>> queryLoanList(String brno, String dateType,
			String date);

	/**
	 * 查詢 授權內分行敘做房屋貸款月報
	 * 
	 * @param brno
	 *            分行別
	 * @param dateType
	 *            日期類別
	 * @param date
	 *            查詢日期
	 * @return
	 */
	List<Map<String, Object>> queryBrnoAmt(String brno, String dateType,
			String date);

	/**
	 * 查詢 本月團貸案動用統計
	 * 
	 * @param brno
	 *            分行別
	 * @param dateType
	 *            日期類別
	 * @param date
	 *            查詢日期
	 * @return
	 */
	public Map<String, Object> monthTotal(String brno, String dateType,
			String date);

	/**
	 * 查詢 授權內分行敘做房屋貸款月報
	 * 
	 * @param brno
	 *            分行別
	 * @param dateType
	 *            日期類別
	 * @param date
	 *            查詢日期
	 * @return
	 */
	List<Map<String, Object>> queryConsumerFinance(String brno,
			String dateType, String date);

	/**
	 * 查詢 主借款人資訊
	 * 
	 * @param mainId
	 *            mainId
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	List<Map<String, Object>> findBorrower(String mainId, String cntrNo);

	/**
	 * 查詢借保人資訊
	 * 
	 * @param mainId
	 *            mainId
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	List<Map<String, Object>> findGuarantor(String mainId, String cntrNo);

	/**
	 * 查詢 婉拒案件明細表
	 * 
	 * @param brno
	 *            分行代碼
	 * @param bgnDate
	 *            資料起日
	 * @param endDate
	 *            資料迄日
	 * @return
	 */
	List<Map<String, Object>> queryReject(String brno, String bgnDate,
			String endDate);

	/**
	 * 查詢主要關係戶與本行授信往來比較表部份內容
	 * 
	 * @param custId
	 *            往來彙總明細統編
	 * @param dupNo
	 *            往來彙總明細重覆序號
	 * @return
	 */
	public List<Map<String, Object>> selExcel(String custId, String dupNo);

	/**
	 * J-113-0069_05097_B1001 Web
	 * e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
	 * 
	 * @param custId
	 * @param dupNo
	 * @param caseMainId
	 * @param itemType
	 * @return
	 */
	public List<Map<String, Object>> selExcel_forThisReport(String custId,
			String dupNo, String caseMainId, String itemType);

	/**
	 * 刪除LMSBATCH的資料
	 * 
	 */
	void delBatchTblNowRpts(String mainId);

	/**
	 * LMSBATCH複製到LMSRPT
	 * 
	 */
	void batchTblCopyToRptTbl(String branchId);

	/**
	 * LMSRPT將最新的資料變成歷史資料
	 * 
	 */
	void updateNowTyperptTbl(String rptNo, String branch);

	/**
	 * 查詢個金舊案資訊 order by endate desc
	 * 
	 * @param docType
	 *            企/個金案件
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	public List<Map<String, Object>> findOldCaseByCutId(String custId,
			String dupNo, String docType);

	/**
	 * J-112-0462 「對大陸地區授信業務控管註記」新增三提問 以額度序號取得已核准之註記
	 * 
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> findLastByCntrNo(String cntrNo);

	/**
	 * 管理報表-「營運中心授權內外已核准/已婉卻授信案件」
	 * 
	 * @param startDate
	 *            開始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	public List<Map<String, Object>> queryLMS180R02AData(String startDate,
			String endDate, String branchId);

	/**
	 * 查詢審件統計表資料
	 * 
	 * @param prodKind
	 *            產品種類
	 * @param docStatus
	 *            文件狀態
	 * @param bgnDate
	 *            起始日
	 * @param endDate
	 *            結束日
	 * @return
	 */
	public Map<String, Object> queryC180R06Data(String prodKind,
			String[] docStatus, String bgnDate, String endDate);

	/**
	 * 管理報表-「金融機構辦理振興經濟非中小企業專案貸款」案件統計表
	 * 
	 * @param startDate
	 *            開始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	public List<Map<String, Object>> queryLMS180R12Data(String startDate,
			String endDate);

	/**
	 * 管理報表-「企業自行申請展延案件」案件統計表
	 * 
	 * @param startDate
	 *            開始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	public List<Map<String, Object>> queryLMS180R13Data(String startDate,
			String endDate);

	/**
	 * 管理報表-「授信契約產生主辦聯貸案一覽表」案件統計表
	 * 
	 * @param startDate
	 *            開始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	public List<Map<String, Object>> queryLMS161T02Data01(String startDate,
			String endDate, String branchId);

	/**
	 * 管理報表-「授信契約產生主辦聯貸案一覽表」主辦行
	 * 
	 * @param startDate
	 *            開始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	public List<Map<String, Object>> queryLMS161T02Data02(String startDate,
			String endDate, String branchId);

	/**
	 * 查詢借保人資料 不重覆的
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @return CUSTID ,DUPNO,CUSTPOS
	 */
	public List<Map<String, Object>> queryL140S01ADistinctByKey(String mainId);

	/**
	 * 取得BSYSPARAM裡面的PARAM參數內容
	 * 
	 * @param param
	 * @return
	 */
	public Map<String, Object> getSysParamData(String param);

	/**
	 * <pre>
	 * [個金] 取得此簽報書不重覆的產品與科目
	 * 性質 只查詢 新作 或續約
	 *  當為續約 三碼科目為  1 全取 三碼科目為
	 *                    2 則為 是否換約為是 或 續約次數大於6次
	 * </pre>
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @return PRODKIND ,SUBJCODE
	 */
	public List<Map<String, Object>> findL140S02ADistinctByKey(String mainId);

	/**
	 * [個金] 取得徵信報告書 主要借款人Id 下的 文件編號(資信簡表用)
	 * 
	 * @param caseBrId
	 * @param mainId1
	 *            簽報書mainId
	 * @param mainId2
	 *            簽報書mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainIdaForCls(
			String caseBrId, String mainId1, String mainId2);

	/**
	 * 取得徵信 主要借款人Id群組 下的 文件編號(範圍)(徵信報告用)
	 * 
	 * @param caseBrId
	 * @param mainId1
	 *            簽報書mainId
	 * @param mainId2
	 *            簽報書mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainId2sForCls(
			String caseBrId, String mainId1, String mainId2);

	/**
	 * 取得徵信 主要借款人Id群組 下的 文件編號(範圍)(徵信報告用)降冪排列
	 * 
	 * @param caseBrId
	 * @param mainId1
	 *            簽報書mainId
	 * @param mainId2
	 *            簽報書mainId
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainId2sdForCls(
			String caseBrId, String mainId1, String mainId2);

	/**
	 * 取得企金覆審 - 資信簡表行業別
	 * 
	 * @param brNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findC120M01A_bizModeForLrs(String brNo,
			String custId, String dupNo);

	/**
	 * 取得 優惠貸款控制數的全行編制中總額度
	 * 
	 * @param kindNo
	 *            貸款種類
	 * @return
	 */
	public Map<String, Object> findTotPreparation(String kindNo);

	/**
	 * 取得營運中心的L180R02A的授管處MAINID
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public Map<String, Object> getL180R02A918LMSRPT(String bgnDate,
			String endDate);

	/**
	 * [個金]查詢個金簽報書是否已被企金簽報書夾帶-
	 * 
	 * @param oid
	 *            個金簽報書oid
	 * @return 夾帶此案的企金案號
	 */
	public String queryL120M01EByOid(String oid);

	/**
	 * 退回流程
	 * 
	 * @param oid
	 *            文件oid
	 * @param isSpecail
	 *            是否為特殊案件
	 * @param histroyFlow
	 *            流程歷史檔
	 * 
	 */
	public void doWorkUnapp(String oid, boolean isSpecail,
			List<FlowInstance> histroyFlow);

	/**
	 * 退回流程 到任意文件狀態
	 * 
	 * @param oid
	 *            文件oid
	 * @param docstatus
	 *            文件狀態
	 * @param histroyFlow
	 *            流程歷史檔
	 */
	public void doWorkUnappByAnyDocstatus(String oid, String docstatus,
			List<FlowInstance> histroyFlow, String ownBrId);

	/**
	 * 取得此份額度明細表 借保人與主借人此份簽報書 評等資料 grid用
	 * 
	 * @param search
	 *            查詢條件
	 * @param caseMainId
	 *            簽報書mainId
	 * @param mainId
	 *            額度明細表mainId
	 * @return
	 */
	Page<Map<String, Object>> queryL140S01AForGrade_G(ISearch search,
			String caseMainId, String mainId);

	Page<Map<String, Object>> queryL140S01AForGrade_notHouseLoan(
			ISearch search, String caseMainId, String mainId);

	Page<Map<String, Object>> queryL140S01AForGrade_cardLoan(ISearch search,
			String caseMainId, String mainId);

	/**
	 * 取得此份額度明細表 借保人與主借人此份簽報書 評等資料 grid用
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @param mainId
	 *            額度明細表mainId
	 * @return
	 */
	List<Map<String, Object>> queryL140S01AForGrade_G(String caseMainId,
			String mainId);

	List<Map<String, Object>> queryL140S01AForGrade_notHouseLoan(
			String caseMainId, String mainId);

	List<Map<String, Object>> queryL140S01AForGrade_cardLoan(String caseMainId,
			String mainId);

	/**
	 * 個金額度明細表 查詢舊案資訊L140M01P 敘作條件異動用]
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	Map<String, Object> findCLSCaseByCutIdAndCntrNoOrdByUpdateTime(
			String custId, String dupNo, String cntrNo);

	/**
	 * 查詢徵信報告書
	 * 
	 * @param search
	 * @param brId
	 *            分行代號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	Page<Map<String, Object>> findCESL140M01A(ISearch search, String brId,
			String custId, String dupNo);

	/**
	 * 取得徵信報告-一般概況
	 * 
	 * @param cesMainId
	 * @return
	 */
	Map<String, Object> getC140M01A_Overview(String cesMainId);

	/**
	 * 取得徵信報告-資本變動情況
	 * 
	 * @param cesMainId
	 * @return
	 */
	Map<String, Object> getC140M01A_ChangeInShareCapital(String cesMainId);

	/**
	 * 取得徵信報告-沿革
	 * 
	 * @param cesMainId
	 * @return
	 */
	Map<String, Object> getC140M01A_History(String cesMainId);

	/**
	 * 取得徵字資信簡表上的成立(改組)日期
	 * 
	 * @param cesMainId
	 * @return
	 */
	Map<String, Object> getC140C120M01A_EstaDate(String cesMainId);

	/**
	 * 取得徵信報告-有無大陸投資
	 * 
	 * @param cesMainId
	 * @return
	 */
	Map<String, Object> getC140M01A_Ch6_LLand_inv(String cesMainId);

	/**
	 * 查詢額度明細表所有引進的擔保品
	 * 
	 * @param search
	 *            查詢條件
	 * @param caseMainId
	 *            簽報書mainId
	 * @return pageData
	 */
	Page<Map<String, Object>> findL140M01OByMainId(ISearch search,
			String caseMainId);

	/**
	 * 尋找管理報表L180R02A的經辦
	 * 
	 * @param custId411ovs
	 * @param dupNo411ovs
	 * @param cntrno411ovs
	 * @param cntrno411ovs
	 * @param cycmn411ovs
	 * @return
	 */

	public List<Map<String, Object>> findApproverForL180R02A(String mainId);

	/**
	 * 取得R6上的舊案
	 * 
	 * @param custIdSet
	 * @param docType
	 *            案件種類 1.企金2.個金
	 * @return
	 */
	HashMap<String, HashSet<String>> findDISTINCTCntrNoByCustId(
			HashSet<String> custIdSet, String docType);

	/**
	 * 個金約據書依照多筆mainId(串好的)查詢額度明細表不重覆契約人相關資料
	 * 
	 * @param mainIds
	 * @return
	 */
	public List<Map<String, Object>> selToC999m01c(String... mainIds);

	/**
	 * 取得最新的一筆產品種類資料
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public Map<String, Object> getL140S02AByCntrNo(String cntrNo);

	/**
	 * 取得擔保品資訊
	 * 
	 * @param mainId
	 *            動審表mainId
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public List<Map<String, Object>> findProducts(String mainId, String cntrNo);

	/**
	 * 簽報書mainid 取得 該購置房屋擔保放款風險權數檢核表所有mainid
	 * 
	 * @param mainId
	 *            簽報書mainid
	 * 
	 * @return
	 */
	public List<String> findC102M01AByMainId(String mainId);

	/**
	 * 取得加總的優惠房貸額度
	 * 
	 * @param itemType
	 *            額度明細表種類
	 * @param caseMainid
	 *            簽報書種類
	 * @return <pre>
	 * {
	 * key :28,38,56,59
	 * value :總金額
	 * }
	 * </pre>
	 * 
	 */
	public Map<String, BigDecimal> getSUMByPRODKIND(String itemType,
			String caseMainid);

	/**
	 * 使用行員優惠房貸利率者的檢核 加總本簽報書 利率代碼為N2,M2,7C,7R,7D
	 * 
	 * @param itemType
	 *            額度明細表種類
	 * @param caseMainid
	 *            簽報書種類
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 總金額
	 * 
	 * 
	 */
	public BigDecimal getSUMByRATE_N2(String itemType, String caseMainid,
			String custId, String dupNo);

	public BigDecimal getSUMByRATE_N2_V2(String itemType, String caseMainid,
			String custId, String dupNo);

	/**
	 * 行員優惠消貸利率者 加總本簽報書 利率代碼為M3,MD
	 * 
	 * @param itemType
	 *            額度明細表種類
	 * @param caseMainid
	 *            簽報書種類
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 總金額
	 * 
	 * 
	 */
	public BigDecimal getSUMByRATE_M3(String itemType, String caseMainid,
			String custId, String dupNo);

	/**
	 * 使用行員優惠房貸利率者的檢核 加總本簽報書 利率代碼為N2,M2,7C,7R,7D ByL140S02H
	 * 
	 * @param itemType
	 *            額度明細表種類
	 * @param caseMainid
	 *            簽報書種類
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 總金額
	 * 
	 * 
	 */
	public BigDecimal getSUMByRATE_N2ByL140S02H(String itemType,
			String caseMainid, String custId, String dupNo);

	/**
	 * 行員優惠消貸利率者 加總本簽報書 利率代碼為M3,MD ByL140S02H
	 * 
	 * @param itemType
	 *            額度明細表種類
	 * @param caseMainid
	 *            簽報書種類
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 總金額
	 * 
	 * 
	 */
	public BigDecimal getSUMByRATE_M3ByL140S02H(String itemType,
			String caseMainid, String custId, String dupNo);

	/**
	 * 查詢個金簽報書非已完結的簽報書
	 * 
	 * @return
	 */
	public List<String> selByCLSUPDW();

	/**
	 * 用簽報書mainId 取得底下所有描述檔
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @param itemType
	 * @return
	 */
	/**
	 * 用簽報書mainId 取得底下所有描述檔
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @param L120M01C_itemType
	 *            額度明細表種類
	 * @param L140M01B_itemType
	 *            描述檔種類
	 * @return
	 */
	public Map<String, String> findL140M01BByL120M01AMainId(String mainId,
			String L120M01C_itemType, String L140M01B_itemType);

	/**
	 * 查詢該簽報書底下 所有 有評等的 借款人
	 * 
	 * @param mainId
	 *            簽報書mainid
	 * @return 客戶統編 +重覆序號
	 */
	Set<String> queryL140S02AGradCustByL120M01AMainId(String mainId);

	/**
	 * 取得【59-青年安心成家優惠貸款】e-Loan已核准未撥款
	 * 
	 * @param kindNo
	 *            貸款種類
	 * @return
	 */
	public List<Map<String, Object>> findMailServiceProd59_part2_list(
			String kindNo, String version);

	/**
	 * 取得【59-青年安心成家優惠貸款】e-Loan簽核中
	 * 
	 * @param kindNo
	 *            貸款種類
	 * @return
	 */
	public Map<String, Object> findMailServiceProd59_part3(String kindNo);

	public List<Map<String, Object>> findMailService_l140s02f_ratePlan20();

	/**
	 * 由資信簡表MAINID取得徵信報告書MAINID
	 * 
	 * @param ces120MainID
	 *            資信簡表MAINID
	 * @return
	 */
	public Map<String, Object> selCES140ByCES120MainId(String ces120MainID);

	/**
	 * 取得徵信報告書第九章集團企業有無財務資訊
	 * 
	 * @param ces140MainID
	 *            徵信報告MAINID
	 * @param schemaKind
	 *            LMS OR CES
	 * @return
	 */
	public Map<String, Object> selCES140Ch9FinInfor1(String ces140MainID,
			String schemaKind);

	/**
	 * 取得徵信報告書第九章集團企業財務資訊年度
	 * 
	 * @param ces140MainID
	 *            徵信報告MAINID
	 * @param schemaKind
	 *            LMS OR CES
	 * @return
	 */
	public Map<String, Object> selCES140Ch9FinYear(String ces140MainID,
			String schemaKind);

	/**
	 * 由資信簡表MAINID取得徵信報告書第九章集團企業淨值與營收
	 * 
	 * @param ces140MainID
	 *            徵信報告MAINID
	 * @param schemaKind
	 *            LMS OR CES
	 * @return
	 */
	public List<Map<String, Object>> selCES140Ch9FINData(String ces140MainID,
			String schemaKind);

	public List<Map<String, Object>> findC101M01A_queryScoreModel(
			String custId, String dupNo, String ownBrId, ISearch search);

	/**
	 * 取得管理報表-分行授信淨增加額度統計表
	 * 
	 * @param bgnDate
	 *            起始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	List<Map<String, Object>> listLMS180R18(String bgnDate, String endDate,
			String otherCondition, String areaNo);

	/**
	 * 取得最新的一筆異動的額度明細表資料
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public Map<String, Object> findL140M01AMAXUPDATE(String cntrNo);

	/**
	 * 從簽報書MAINID取得所有的各項費用明細合計數
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL140M01RByL120M01A(String mainId);

	/**
	 * 取得尚未FTP之異常通報資料
	 * 
	 * @param
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findL130M01A_NeedSend();

	/**
	 * 從動審表MAINID取得所有的簽報書案號
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL120M01AByC160M01A(String mainId);

	/**
	 * 從簽報書MAINID及案號取得所有的各項費用明細合計數(排除feeSrc=3)
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL140M01RByL120M01AcaseNo(
			String mainId, String caseNo);

	/**
	 * 從簽報書MAINID取得所有的各項費用明細合計數(feeSrc=3)
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL140M01RByL120M01AInfeeSrc3(
			String mainId);

	public List<Map<String, Object>> findGridListByC240M01AMainid(
			String c240m01a_mainId);

	/**
	 * 從動審表MAINID取得所有的額度序號
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL140M01AByC160M01A(String mainId);

	public List<Map<String, Object>> findL140M01A_J_107_0327(String custId,
			String cntrNo);

	public Map<String, Object> findLatestGrpCntrNo(String brId, String grpCntrNo);

	/**
	 * 取得消金覆審逾N天的分行
	 */
	public Set<String> findCrsMailBr(String retrialDate, boolean isArea);

	/**
	 * 取得消金覆審逾N天的分行
	 */
	public Set<String> findLrsMailBr(String retrialDate, boolean isArea);

	/**
	 * 透過某一筆額度明細表mainid(=L120M01C.REFMAINID)找出同一份簽報書項下的所有額度序號
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findAllCNTRNOByL140M01AMainid(String mainId);

	/**
	 * 取得授信科目 maxSeqNum
	 * 
	 * @param mainId
	 * @return
	 */
	String findL140M01C_MaxSeqNum(String mainId);

	// /**
	// * 取得授信科目 SeqNum為空
	// *
	// * @param mainId
	// * @return
	// */
	// List<Map<String, Object>> findL140M01C_SeqNumIsEmpty(String mainId);

	/**
	 * 用ID找出LMS取消的額度明細表最後異動資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> findAllL140M01ACancelCntrnoDataByID(
			String custId, String dupNo);

	/**
	 * 用cntrno找出LMS取消的額度明細表最後異動資料
	 * 
	 * @param cntrNo
	 * @return
	 */
	List<Map<String, Object>> findAllL140M01ACancelCntrnoDataByCntrNo(
			String cntrNo);

	List<Map<String, Object>> findL180M01B(String mainId, String type);

	List<Map<String, Object>> findisSmallBuss_lrs(String mainId);

	List<Map<String, Object>> findL180M01BByL170M01A(String mainId);

	List<Map<String, Object>> findL120M01A_lrs(String brNo,
			List<String> custId_list);

	List<Map<String, Object>> findL120M01A_lrs_ctlType_B(String brNo,
			List<String> custId_list);

	/**
	 * 動審表引進額度明細表選取簽報書項下額度明細表
	 * 
	 * @param caseMainId
	 * @param useBrId
	 * @param itemType
	 * @return
	 */
	List<Map<String, Object>> get_cntrDoc_from_VLUSEDOC01(String caseMainId,
			String useBrId, String itemType);

	BigDecimal findAllC900M01GAndStatus01(String grpCntrNo);

	BigDecimal findAllC900M01GAndStatus02(String grpCntrNo);

	BigDecimal findAllC900M01GAndStatus03(String grpCntrNo);

	/**
	 * 由動審表中所對應的額度序號引入個人清冊
	 */
	List<Map<String, Object>> findCntrnoByL161S01A(String mainId);

	List<Map<String, Object>> findL140InfoSinceWebApplyTS(String brNo,
			String custId, String dupNo, Timestamp applyTS);

	public void c121m01aclearUnUsedDoc();

	/**
	 * 取得管理報表-授信業務異常通報月報
	 * 
	 * @param bgnDate
	 *            起始時間
	 * @param endDate
	 *            結束時間
	 * @param areaNo
	 *            分行或營運中心代號
	 * @param sqlMode
	 *            呼叫SQL類別
	 * @return
	 */
	List<Map<String, Object>> listLMS180R25(String bgnDate, String endDate,
			String[] areaNo, String sqlMode);

	/**
	 * 檢查額度序號是否有於簽報書額度明細表/聯行攤貸比例建檔(國內/海外簽報書檢核用)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	int checkCntrnoExistEloan(String mainId, String custId, String dupNo,
			String cntrNo);

	/**
	 * J-104-0279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 轉檔用-轉eLoan loanTarget
	 * 
	 * @return
	 */
	List<Map<String, Object>> find140QByLoanTargetIsNull();

	/**
	 * J-104-0279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 轉檔用-轉MIS.ELF506與AS400
	 * loanTarget
	 * 
	 * @return
	 */
	List<Map<String, Object>> find140QByAllCnLoanFgIsY();

	/**
	 * J-104-0279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 轉檔用-轉eLoan loanTarget
	 * 
	 * @return
	 */
	List<Map<String, Object>> find140AFrom140QLoanTargetIsNull();

	/**
	 * J-104-0279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 轉檔用-更新eLoan
	 * loanTarget
	 * 
	 * @param loanTarget
	 * @param mainId
	 */
	void updateL140M01QLoanTargetByMainId(String mainId, String loanTarget,
			String bloanTarget, String charCd);

	/**
	 * J-104-0270-002 Web
	 * e-Loan國內授信管理系統OBU戶檢核要有聯徵虛擬統編才能送呈主管，但排除遠匯、衍生性金融商品與其他非授信業務
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL140M01C_findNeedChkObuJcicId(
			String mainId);

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 清除信用風險遵循本案異動額度部分
	 * 
	 * @param mainId
	 */
	public void setCaculate_L120S01M(String isCaculate, String mainId);

	public void cleanLoacal_L120S01N(String mainId);

	public void cleanLoacal_L120S01O(String mainId);

	public void setLocalCurrentAdjVal_L120S01O(String mainId, String custId,
			String dupNo, BigDecimal netValue);

	public List<Map<String, Object>> findAllL120S01NSumByMainId(String mainId);

	public void setLoacal_L120S01N(String mainId, String custId, String dupNo,
			String dataKind, BigDecimal localCurrentAdjValT,
			BigDecimal localCurrentTotal, BigDecimal shareOfNet);

	/**
	 * ELF506 Y01轉Y04
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @return
	 */
	public List<Map<String, Object>> findLms140QByComBrToBr01_Elf506Y01ToY04(
			String exDate, String fBranch, String tBranch);

	/**
	 * 取得管理報表-振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表
	 * 
	 * @param bgnDate
	 *            起始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	List<Map<String, Object>> listLMS180R26(String bgnDate, String endDate);

	/**
	 * 取得管理報表-授信異常通報案件報送統計表 J-111-0583_05097_B1001 Web
	 * e-Loan企金授信提供各營運中心可自行列印轄下分行於指定期間內所簽報異常通報之「授信異常通報案件報送統計表」
	 * 
	 * @param bgnDate
	 *            起始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	List<Map<String, Object>> listLMS180R27(List<String> custQuery,
			List<String> custParam);

	public List<Map<String, Object>> listLMS1205(String[] brNoArrs);

	public List<Map<String, Object>> listLMS180R28(String brNo,
			String retrialDateBeg, String retrialDateEnd);

	/**
	 * J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等 引進個金評等-房貸
	 * 
	 * @param custId
	 * @param dupNo
	 * @param ownBrId
	 * @param search
	 * @return
	 */
	public List<Map<String, Object>> findC101M01A_queryScoreModel_C1(
			String custId, String dupNo, String ownBrId);

	/**
	 * J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等 引進個金評等-非房貸
	 * 
	 * @param custId
	 * @param dupNo
	 * @param ownBrId
	 * @param search
	 * @return
	 */
	public List<Map<String, Object>> findC101M01A_queryScoreModel_C2(
			String custId, String dupNo, String ownBrId);

	/**
	 * J-105-0167-001 Web e-Loan 企金授信案件簽報書第八之3增列集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」。
	 * 取得集團財務警訊(依年度由新至舊排序)
	 * 
	 * @param grpId
	 * @return
	 */
	public List<Map<String, Object>> findG117M01A_FinancialAlert(String grpId);

	/**
	 * 刪除利率檔案特定日期所有資料 J-105-0185-001
	 * 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
	 * 
	 * @param mainId
	 */
	public void RATETBL_deleteByDateYmd(String dateYmd);

	/**
	 * 新增利率資料 J-105-0185-001 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
	 * 
	 * @param mainId
	 */
	public void RATETBL_insert(String curr, String dataYmd, BigDecimal endRate,
			String rateYmd);

	/**
	 * 取得e-Loan額度 J-105-0228-002 Web
	 * e-Loan企金授信簽報書新增私募基金報表新增列示近半年已核准簽報書尚未於a-Loan建檔之額度資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findCntrDocByCustIdForPEFund(
			String custId, String dupNo, String endDate);

	/**
	 * 取得已核准未簽約報送資料 J-105-0214-001 Web e-Loan 管理報表新增授信簽案已核准未能簽約撥貸原因表。
	 * 
	 * @param custId
	 * @param dupNo
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL230ByDataDateAndBrNo(String bgnDate,
			String endDate, String brNo);

	public List<Map<String, Object>> findDeletedMetaByMainId(String mainId);

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 * 
	 * @param startDate
	 * @param endDate
	 * @param otherCondition
	 * @return
	 */
	public List<Map<String, Object>> queryLMS180R31Data(String startDate,
			String endDate, String otherCondition, String otherParam1);

	/**
	 * 依額度明細表MAINID取得最新一筆報送 J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	public Map<String, Object> findL230S01LastBySrcMainId(String srcMainId,
			String docstatus);

	/**
	 * 依額度明細表MAINID取得最新一筆已簽約報送(為了抓簽約日期) J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	public Map<String, Object> findL230S01LastBySrcMainIdAndNuseMemo(
			String srcMainId, String docstatus, String nuseMemo);

	/**
	 * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findZ15CntrDoc();

	public List<Map<String, Object>> findCntrNoByC120S01B_comname(String comname);

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 */
	public List<Map<String, Object>> findC120S01D_selectGroupByCustId(
			List<String> cesMainIds);

	/**
	 * J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 */
	public List<Map<String, Object>> findC120S01D_selectGroupByCustName(
			List<String> cesMainIds);

	/**
	 * 抓取ELOAN所有715科目資料
	 * 
	 * @param is715
	 * @return
	 */
	List<Map<String, Object>> find715_CntrDoc(boolean is715);

	/**
	 * 更新ELOAN科目代號
	 * 
	 * @param mainId
	 * @param origLoanTp
	 * @param newLoanTp
	 */
	void updateL140m01cLoanTp(String mainId, String origLoanTp, String newLoanTp);

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 */
	public List<Map<String, Object>> listLMS180R33(String bgnDate,
			String endDate);

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	public List<Map<String, Object>> findLMSL120M01A(String brId,
			String custId, String dupNo);

	public Map<String, Object> findC900S02A_groupBy_ratePlan_companyId(
			String begDate, String endDate, String ratePlan, String companyId);

	public List<Map<String, Object>> findC900S02A_groupBy_brNo(String begDate,
			String endDate, String companyId5);

	public List<Map<String, Object>> findLaaData(String approveDateBeg,
			String caseBrId);

	public List<Map<String, Object>> findLaaData_by_L120M01A(
			String l120m01a_mainId);

	// J-107-0045-001 Web e-Loan企金授信簽報書配合海外啟用IFRS徵信報告調整財報引進相關功能。
	public void C140JSON_copy1(String mainId, String cesMainId, String tab);

	public void C140SDSC_copy1(String mainId, String cesMainId, String tab);

	public void C140SFFF_copy1(String mainId, String cesMainId, String tab);

	/**
	 * J-107-0184_05097_B1001 Web e-loan企金授信簽報時提供以借款人查詢應簽報的額度明細表及該客戶所有的往來分行(
	 * 包含前次簽報書所簽報額度明細表所屬分行及現有有效額度的往來分行)等資訊,並於送呈前進行差異比對, 就存在差異時提供警示訊息,
	 * 以避免錯選授信案件授權層級情事。
	 */
	public List<Map<String, Object>> findL140M01_byLastRptWithCustid(
			String custId, String dupNo, String caseBrId);

	/**
	 * J-107-0196_05097_B1001 Web
	 * e-Loan企金授信系統管理報表新增以各營運中心之所載聯貸案企金案件，並按期產製報表，以供後續追蹤聯貸案件進度及收益。
	 * (1)營運中心之企金授信，案件簽報書屬呈總處。
	 */
	public List<Map<String, Object>> listLMS180R36_1();

	/**
	 * J-107-0196_05097_B1001 Web
	 * e-Loan企金授信系統管理報表新增以各營運中心之所載聯貸案企金案件，並按期產製報表，以供後續追蹤聯貸案件進度及收益。
	 * (2)營運中心權限內已核准新做聯貸案件
	 */
	public List<Map<String, Object>> listLMS180R36_2(String startDate,
			String endDate);

	/**
	 * J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。
	 */
	public List<Map<String, Object>> findL120S08APrintGroupOfMainIdCustIdPrintGroup(
			String mainId, String versionDate);

	/**
	 * J-111-0501 依據授信簽報有關「利率訂價合理性及收益分析表」內容濃縮產生「授信成本收益概算表」
	 */
	public List<Map<String, Object>> findL120S08APrintGroupByOneCustId(
			String mainId, String versionDate, String curr, String custId,
			String dupNo);

	/**
	 * J-107-0224_05097_B1001 Web e-Loan企金處新增企金授信案件敘做情形及比較表
	 * 
	 * 金授信案件敘做情形及比較表
	 * 
	 */
	public List<Map<String, Object>> listLMS180R37(String startDate,
			String endDate);

	/**
	 * J-107-0224_05097_B1001 Web e-Loan企金處新增企金授信案件敘做情形及比較表
	 * 
	 * 金授信案件敘做情形及比較表
	 * 
	 */
	public List<Map<String, Object>> listLMS180R37_AreaBranch(String brNo,
			String areaId);

	/**
	 * LMS180R38 企金已核准授信額度辦理狀態通報彙總表
	 * 
	 * @param startDate
	 * @param endDate
	 * @param otherCondition
	 * @return
	 */
	public List<Map<String, Object>> queryLMS180R38Data(String startDate,
			String endDate, String otherCondition, String otherParam1);

	public List<Map<String, Object>> findC900S02C_fillData(String year_1st,
			String data_ym_1st, String data_ym_last);

	public List<Map<String, Object>> findC900S02C_fillData_exclude940(
			String year_1st, String data_ym_1st, String data_ym_last);

	public List<Map<String, Object>> findC900S02C_fetch1(String flag,
			String data_ym, String beg, String end);

	public List<Map<String, Object>> findC900S02C_fetch2(String flag,
			String data_ym, String beg, String end);

	public void batchInsert_C900S02C(List<Object[]> batchValues);

	public void batchInsert_C900M03A_dtl(String fn_D, List<Object[]> batchValues);

	public void batchInsert_C900S03C(List<Object[]> batchValues);

	public void batchInsert_C900S03D(List<Object[]> batchValues);

	public void batchInsert_C900S03E(List<Object[]> batchValues);

	public void batchInsert_C900S02E_C900S02F(List<Object[]> batchValues_s02e,
			List<Object[]> batchValues_s02f);

	public void delete_C900M03A_dtl(String fn_D, String genDate, String genTime);

	public void delete_C900S03C(String cyc_mn);

	public void delete_C900S03D(String cyc_mn);

	public void delete_C900S03E(String cyc_mn);

	/**
	 * 首次時, rpt_brNo = 全部的授信分行 <br/>
	 * 第2次以後, rpt_brNo 有2種作法【A分行客戶新作, 與B分行既有客戶(不變)的通訊處相同】 (1)只包含 "當月新做"
	 * 的授信分行(scope 較小)(假設, 只有新作的分行 A 要調查, 不變的分行B 不用) (2)A及B都要調查
	 */
	public List<Map<String, Object>> prep_text_from_C900S03C(
			boolean isDwCustRelFirstTotalRun, String cyc_mn, String rel_flag,
			String brNo);

	public List<Map<String, Object>> prep_custKey_brNo_from_C900S03C_text(
			String cyc_mn, String rel_flag, String text);

	/**
	 * J-107-0245_09301_B1001 Web e-Loan企金授信系統覆審報告中增列上次覆審日當時之「信用評等及信用風險內部評等」資訊。
	 * 另授信「額度」欄位，針對中長期不循環授信額度者，請修改以「有效額度」列示。
	 */
	public String findL170M01A_exMainid(String mainId, String custId,
			String dupNo, String ownBrId);

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表 取得最新額度明細表
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findL140M01A_lastByCustIdAndCntrNo(
			String custId, String dupNo, String cntrNo);

    public Map<String, Object> findL140M01ALastDerivEvalByCustId(
            String custId, String dupNo);

	/**
	 * J-113-0069_05097_B1001 Web
	 * e-Loan企金授信「主要關係戶與本行授信往來比較表」，借戶及其相關關係企業於本次有簽報者，引進本次簽報內容
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param caseMainId
	 * @param itemType
	 * @return
	 */
	public Map<String, Object> findL140M01A_lastByCustIdAndCntrNo_forThisReport(
			String custId, String dupNo, String cntrNo, String caseMainId,
			String itemType);

	// LGD
	public List<Map<String, Object>> findL140m01aLastByCntrNo(String cntrNo);

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param docType
	 * @param bgnDate
	 * @param endDate
	 * @param ctlType
	 * @param brNo
	 * @return
	 */
	public Map<String, Object> findL180r19hHasDone(String docType,
			String bgnDate, String endDate, String ctlType, String brNo);

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param docType
	 * @param bgnDate
	 * @param endDate
	 * @param ctlType
	 * @param brNo
	 */
	public void L180R19H_deleteExistData(String docType, String bgnDate,
			String endDate, String ctlType, String brNo);

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @param brNo
	 */
	public void LELF412A_deleteExistData(String bgnDate, String endDate,
			String brNo);

	/**
	 * J-107-0342_05097_B1001 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @param brNo
	 */
	public void LELF412B_deleteExistData(String bgnDate, String endDate,
			String brNo);

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @param brNo
	 */
	public void LELF412C_deleteExistData(String bgnDate, String endDate,
			String brNo);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @return
	 */
	public List<Map<String, Object>> findBrToBr01List(String exDate,
			String fBranch, String tBranch);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	public void insertL120A01AFromBRTOBR01(String exDate, String fBranch,
			String tBranch, String version);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	public void updateL120M01ACaseBrIdFromBRTOBR01(String exDate,
			String fBranch, String tBranch, String version);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	public void updateL140m01aFromBRTOBR01(String exDate, String fBranch,
			String tBranch, String version);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	public void updateL140m01eFromBRTOBR01(String exDate, String fBranch,
			String tBranch, String version);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	public void deleteBrToBr01ByExDateAndFBranch(String exDate, String fbranch);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param custId
	 * @param dupNo
	 * @param exDate
	 * @param fbranch
	 * @param fcustId
	 * @param fcustNo
	 * @param tbranch
	 * @param tcustId
	 * @param tcustNo
	 * @param cname
	 */
	public void insertBrToBr01(String custId, String dupNo, String exDate,
			String fbranch, BigDecimal fcustId, BigDecimal fcustNo,
			String tbranch, BigDecimal tcustId, BigDecimal tcustNo,
			String cname, String chgCustId, String chgDupNo);

	public List<Map<String, Object>> findClsApplyCntrDataForElf459(
			String approveTimeBeg, String approveTimeEnd, String cntrNo);

	/**
	 * J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
	 * 
	 * @param caseBrId
	 * @param mainId1
	 * @param mainId2
	 * @return
	 */
	public List<Map<String, Object>> findC120M01A_selMainIdd(String caseBrId,
			String mainId1, String mainId2);

	/**
	 * 
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> selDistinctCntrnoByCustidDupno(
			String custId, String dupNo);

	public Map<String, Object> get_CLS180R19_data_by_grpCntrNo(String grpCntrNo);

	/**
	 * LMS180R40 簽報階段都更危老業務統計表
	 */
	public List<Map<String, Object>> queryLMS180R40DataByCntrno(String cntrno,
			String bgnDate, String endDate, String custId, String filterValue);

	/**
	 * J-107-0357_05097_B1001 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
	 */
	public List<Map<String, Object>> listLMS180R41(String bgnDate,
			String endDate);

	/**
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findAllNewCustFromL140m01a(String bgnDate,
			String endDate);

	/**
	 * 108年度新核准往來客戶及新增放款額度統計表 J-108-0040_05097_B1001 Web
	 * e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	public List<Map<String, Object>> listLMS180R42_01(String allBgnDate,
			String allEndDate, String thisMonthBgnDate,
			String thisMonthEndDate, String thisYearBgnDate,
			String thisYearEndDate, String lastYearBgnDate,
			String lastYearEndDate);

	/**
	 * 本月新增核准額度前五大客戶
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	public List<Map<String, Object>> listLMS180R42_02(String thisMonthBgnDate,
			String thisMonthEndDate);

	/**
	 * 本月全行新增核准中小企業戶數最多之前五名分行
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	public List<Map<String, Object>> listLMS180R42_03(String thisMonthBgnDate,
			String thisMonthEndDate);

	/**
	 * 累計全行新增核准中小企業戶數最多之前五名分行
	 * 
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	public List<Map<String, Object>> listLMS180R42_04(String thisYearBgnDate,
			String thisYearEndDate);

	/**
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 */
	public List<Map<String, Object>> listLMS180R42T(String bgnDate,
			String endDate);

	/**
	 * 國內分行每季新做無擔保中小企業戶授信額度明細表 LMS180R43
	 * 
	 * 
	 * M-108-0066_05097_B1001 Web e-Loan企金授信因應稽核處風險導向內部稽核制度填報風險監控指標需要新增企金處報表
	 */
	public List<Map<String, Object>> listLMS180R43(String bgnDate,
			String endDate);

	/**
	 * 國內分行每季新作副總權限以上授信額度累計金額 LMS180R44
	 * 
	 * 
	 * M-108-0066_05097_B1001 Web e-Loan企金授信因應稽核處風險導向內部稽核制度填報風險監控指標需要新增企金處報表
	 */
	public List<Map<String, Object>> listLMS180R44(String bgnDate,
			String endDate);

	/**
	 * 報表管理 -> 海外專用 918可全部海外分行 pdf10 10. 過去半年內董事會（或常董會）權限核定之企業戶授信案件名單 LMS9515R10
	 */
	public List<Map<String, Object>> queryPdf10Data(String ovUnitNo,
			String bgnDate, String endDate);

	/**
	 * 依額度序號取得最新一筆報送J-108-0396-001
	 */
	public Map<String, Object> findL230S01LastByCntrno(String cntrNo,
			String docstatus);

	// public List<Map<String, Object>> get_LMS_OTS_CSPERSCC_list(int
	// maxRecords);

	public List<Map<String, Object>> getC101M01AByOwnBrid(String fristWord,
			String ownbrid);

	// public List<Map<String, Object>> get_OTS_CSPERSCC_NOT_RUN(int maxRecords,
	// String targetBrNo, String idPrefix);

	/**
	 * 房屋貸款擔保品用途聲明切結事項的地址資料
	 * 
	 * @param mainId
	 * @param itemType
	 * @return
	 */
	List<String> getL140s02fCMSid(String mainId, String itemType);

	/**
	 * LMS180R45 國內分行新核准往來企金客戶數統計表
	 * 
	 * Sheet1_分行彙總
	 * 
	 * J-108-0107_05097_B1001 國內分行新核准往來企金客戶數統計表(按分行列表)
	 */
	public List<Map<String, Object>> listLMS180R45_1(String thisMonthBgnDate,
			String thisMonthEndDate, String thisYearBgnDate,
			String thisYearEndDate);

	/**
	 * LMS180R45 國內分行新核准往來企金客戶數統計表
	 * 
	 * Sheet2_分行明細
	 * 
	 * J-108-0107_05097_B1001 國內分行新核准往來企金客戶數統計表(按分行列表)
	 */
	public List<Map<String, Object>> listLMS180R45_2(String bgnDate,
			String endDate);

	/**
	 * 根據CUSTID,DUPNO,OWNID 設定刪除狀態及時間並保留原狀態 J-108-0086_05097_B1001
	 * e-Loan電子文件清理機制
	 * 
	 * @param dfm02
	 * @throws Exception
	 */
	public void setDocStatusToDelByCustIdDupNo(BELDFM02 dfm02) throws Exception;

	/**
	 * 根據CUSTID,DUPNO,OWNID 設定刪除狀態及時間並保留原狀態 J-108-0086_05097_B1001
	 * e-Loan電子文件清理機制
	 * 
	 * @param dfm02
	 * @throws Exception
	 */
	public void setDocStatusDelToDeleteTime(String currentTime)
			throws Exception;

	/**
	 * J-108-0086_05097_B1001 e-Loan電子文件清理機制
	 * 
	 * @param deletedTime
	 * @param brNo
	 * @param createTime
	 * @throws Exception
	 */
	public void setDeleteTimeForReportL784m01a(String deletedTime, String brNo,
			String createTime) throws Exception;

	/**
	 * J-108-0086_05097_B1001 e-Loan電子文件清理機制
	 * 
	 * @param deletedTime
	 * @param brNo
	 * @param createTime
	 * @throws Exception
	 */
	public void setDeleteTimeForReportLmsrpt(String deletedTime, String brNo,
			String createTime) throws Exception;

	public List<Map<String, Object>> doLmsBatch0019(String custId,
			String dupNo, String brId);

	/**
	 * J-108-0116 共同行銷擔保品投保未結案明細表
	 */
	public List<Map<String, Object>> listLMS180R46();

	public List<Map<String, Object>> notifyMail_LMS180R46(String step,
			String user);

	/**
	 * J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL120m01aByEndDate(String bgnDate,
			String endDate);

	/**
	 * J-112-0456_05097_B1001 Web e-Loan授信系統增加簽報中屬不變的額度序號之簽報明細上送DW
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL120m01aByEndDateOnlyMainId(
			String bgnDate, String endDate);


	/**
	 * J-113-0059 額度明細表約定融資註記欄位，配合於簽報書核准時寫入ELF506，回補舊案資料
	 * 
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findLatestExceptFlagByCntrNo(String cntrNo);
	
	/**
	 * J-113-0417 配合修改LLMLN998消金授信案件例外管理報表，ELF500新增寫入家庭所得，回補舊案資料
	 * 
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findLatestFincomeByCntrNo(String cntrNo, String custId, String dupNo);

	int updateBySQL(String sql, Object[] args) throws GWException;

	int updateByCustParam(String sqlId, Object[] msgFmtParam, Object[] args)
			throws GWException;

	/**
	 * J-108-0166 企業社會責任貸放情形統計表 LMS180R47
	 */
	public Map<String, Object> listLMS180R47_1(String bgn, String end);

	public List<Map<String, Object>> listLMS180R47_2(String bgn, String end);

	public List<Map<String, Object>> listLMS180R47_2out(String bgn, String end);

	public List<Map<String, Object>> listLMS180R47_2kind(String mainId,
			String L140A_custId, String L140A_custDupNo);

	public List<Map<String, Object>> listLMS180R47_2third(String bgn,
			String end, String L140A_custId, String L140A_custDupNo);

	public Map<String, Object> listLMS180R47_3(String bgn, String end);

	public List<Map<String, Object>> listLMS180R47_4(String bgn, String end);

	public List<Map<String, Object>> listLMS180R47_4factor(String bgn,
			String end, String custId, String dupNo);

	public List<Map<String, Object>> listLMS180R47_detail(String bgn, String end);

	/**
	 * J-108-0243 微型企業
	 */
	public List<Map<String, Object>> getJ10DefaultRateByType(String type);

	/**
	 * J-108-0242_05097_B1001 Web e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
	 * 
	 * @param cntrNo
	 * @param caseBrId
	 * @param startDate
	 * @param endDate
	 * @param docKind
	 * @param docType
	 * @param caseSeq
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0024_01(String cntrNo,
			String caseBrId, String startDate, String endDate, String docKind,
			String docType, BigDecimal caseSeq);

	/**
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * 對區域營運中心授信覆審作業之管理績效考核表(附表一)_合計
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> listLMS180R48_total(String bgnDate,
			String endDate);

	/**
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * 對區域營運中心授信覆審作業之管理績效考核表(附表一)_合計
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> listLMS180R48_detail(String mdFlag,
			String bgnDate, String endDate);

	/**
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @param baseDate
	 * @return
	 */
	public Map<String, Object> findC241m01aClosestReChkRpt(String brno,
			String custId, String dupNo, String baseDate);

	public List<Map<String, Object>> findCLS180R23_ratePlan20(
			String l120m01a_docStatus, String cntrNo);

	public List<Map<String, Object>> findCLS180R25(String applyStatus,
			String brNo, String applyBegDate, String applyEndDate);

	/**
	 * LMS180R49 國內分行新核准往來企金客戶數統計表(含舊戶)
	 * 
	 * Sheet1_營運中心彙總
	 * 
	 * J-108-0272_10702_B1001 Web e-Loan 新增「國內分行新核准往來企金客戶數統計表(含舊戶)」報表
	 */
	public List<Map<String, Object>> listLMS180R49_1(String thisMonthBgnDate,
			String thisMonthEndDate, String thisYearBgnDate,
			String thisYearEndDate, String lastYearBgnDate,
			String lastYearEndDate, String lastMonthBgnDate,
			String lastMonthEndDate);

	/**
	 * LMS180R49 國內分行新核准往來企金客戶數統計表(含舊戶)
	 * 
	 * Sheet2_組別彙總
	 * 
	 * J-108-0272_10702_B1001 Web e-Loan 新增「國內分行新核准往來企金客戶數統計表(含舊戶)」報表
	 */
	public List<Map<String, Object>> listLMS180R49_2(String thisMonthBgnDate,
			String thisMonthEndDate, String thisYearBgnDate,
			String thisYearEndDate, String lastYearBgnDate,
			String lastYearEndDate, String lastMonthBgnDate,
			String lastMonthEndDate);

	/**
	 * LMS180R49 國內分行新核准往來企金客戶數統計表(含舊戶)
	 * 
	 * Sheet3_分行明細
	 * 
	 * J-108-0272_10702_B1001 Web e-Loan 新增「國內分行新核准往來企金客戶數統計表(含舊戶)」報表
	 */
	public List<Map<String, Object>> listLMS180R49_3(String bgnDate,
			String endDate);

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * 授信審查(企、消金)
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0025_01(String startDate,
			String endDate);

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * 授信覆審(企金)
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0025_02(String startDate,
			String endDate);

	/**
	 * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * 授信覆審(消金)
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0025_03(String startDate,
			String endDate);

	/**
	 * M-108-0296_05097_B1003 Web e-Loan配合總處經費分攤提供所需資料
	 * 
	 * 授信審查(企、消金)
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0025_04(String startDate,
			String endDate);

	/**
	 * J-108-0303 連鎖店Chain store 取得主事業體額度序號
	 */
	public Map<String, Object> findL140m01vById(String custId, String dupNo);

	/**
	 * J-108-0303 連鎖店Chain store 檢核
	 */
	public Map<String, Object> chkMainBiz(String mCntrNo, String custId,
			String dupNo, String type);

	public String chkChainStore(String cntrNo);

	/**
	 * J-108-0304 投資台灣三大方案專案貸款執行情形統計表 LMS180R50
	 */
	public Map<String, Object> listLMS180R50_1(String bnDate, String endDate,
			String projClass, String brType, String branch);

	public List<Map<String, Object>> listLMS180R50_2(String bnDate,
			String endDate, String projClass, String brType, String branch);

	public List<Map<String, Object>> listCntr_c340m01a_ctrType_1_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo);

	public List<Map<String, Object>> listCntr_c340m01a_ctrType_2_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo);

	public List<Map<String, Object>> listCntr_c340m01a_ctrType_3_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo);

	public List<Map<String, Object>> listCntr_c340m01a_ctrType_A_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo);

	public List<Map<String, Object>> listCntr_c340m01a_ctrType_B_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo);

	public List<Map<String, Object>> listCntr_c340m01a_ctrType_S_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo);

	public List<Map<String, Object>> listCntr_c340m01a_ctrType_L_match(
			String l120m01a_docstatus, String brNo, String custId, String dupNo);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @return
	 */
	public List<Map<String, Object>> findBrToBr01ListByExDate(String exDate);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param exDate
	 * @param fBranch
	 * @param tBranch
	 * @param version
	 */
	public void deleteBrToBr01ByExDate(String exDate);

	/**
	 * J-109-0025 愛企貸專案統計表 LMS180R51
	 */
	public List<Map<String, Object>> listLMS180R51(String bgnDate,
			String endDate, String projClass);

	/**
	 * 本月全行新增核准企業戶數最多之前五名分行
	 * 
	 * J-109-0054_05097_B1001
	 * 國內分行新核准往來企金客戶數(LMS180R45)、新核准往來客戶及新增放款額度統計表(LMS180R42)內容修改
	 */
	public List<Map<String, Object>> listLMS180R42_05(String thisMonthBgnDate,
			String thisMonthEndDate);

	/**
	 * 累計全行新增核准企業戶數最多之前五名分行
	 * 
	 * J-109-0054_05097_B1001
	 * 國內分行新核准往來企金客戶數(LMS180R45)、新核准往來客戶及新增放款額度統計表(LMS180R42)內容修改
	 */
	public List<Map<String, Object>> listLMS180R42_06(String thisYearBgnDate,
			String thisYearEndDate);

	public Map<String, Object> findVariousTotalFee(String mainId);

	/**
	 * 因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表--彙總
	 * 
	 * J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 */
	public List<Map<String, Object>> listLMS180R52_01(String brNos,
			String condition);

	/**
	 * 因應嚴重特殊傳染性肺炎影響事業資金紓困方貸款統計表--明細
	 * 
	 * J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 */
	public List<Map<String, Object>> listLMS180R52_02(String brNos,
			String condition);

	public Page<Map<String, Object>> findCesWithBankSimpleCreditRatio(
			ISearch search, String brId, String custId, String dupNo);

	public Page<Map<String, Object>> findCesWithBankSimpleCreditRatio(
			ISearch search, String cesMainId);

	public Map<String, Object> findLastPrint_L140M01A(String cntrNo);

	/**
	 * J-110-0005 其他敘做條件to貸後管理
	 */
	public List<Map<String, Object>> findL140S09BtoPostLoanByL140M01A(String oid);

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	public Map<String, Object> findLatestL260S01D(String l260m01dOid,
			String custId, String dupNo);

	/**
	 * J-113-0035 為利ESG案件之貸後管控,
	 * ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
	 * @param cntrNo
	 * @param from602SUid
	 * @param from602SApptime
	 * @param from602SSeqno
	 * @return
	 */
	public List<Map<String, Object>> findLatestL260S01F(String cntrNo, String from602SUid,
			Date from602SApptime,BigDecimal from602SSeqno);
			
	// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
	public Map<String, Object> getL161S01A_IsRescue(String cntrNo);

	/**
	 * 法令遵循自評授信案件明細報表-簽報書
	 * 
	 * J-109-0132_05097_B1001 e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表
	 */
	public List<Map<String, Object>> listLMS180R53_01(String bgnDate,
			String endDate);

	/**
	 * 引進當日已完成掃描/調查名單
	 * 
	 * @param custId
	 * @param dupNo
	 * @param qDate
	 * @param caseBrId
	 * @param mainId
	 * @param ncResultStr
	 * @return
	 */
	public List<Map<String, Object>> findL120s09bNcResultDoneByMainIdAndCustId(
			String custId, String dupNo, String qDateBgn, String qDateEnd,
			String caseBrId, String mainId, String[] ncResultStr);

	/**
	 * 取得勞工紓困資料傳送notes
	 * 
	 * @return
	 * @param laborVerDate
	 */
	List<Map<String, Object>> getLaborReliefPackage(Date laborVerDate);

	/**
	 * 取得線上申請勞工紓困資料
	 */
	Map<String, Object> getCustDataFormOnline(String custId, String dupNo);

	/**
	 * 取得個人徵信作業基本資料
	 */
	Map<String, Object> getCustDataFormBase(String custId, String dupNo);

	/**
	 * 取得申請書鍵入者資料
	 */
	Map<String, Object> getLastImporterData(String importerNo);

	List<Map<String, Object>> genLaborTrustData(int laborVer, Date laborVerDate);

	/**
	 * 信保整批貸款申請書回饋檔查詢
	 */
	public List<Map<String, Object>> findC124m01aByCustIdBatchDate(
			String ownBrId, String custId, String bgnDate, String endDate);

	/**
	 * 信保整批貸款通知單回饋檔查詢
	 * 
	 * @param ownBrId
	 * @param custId
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findC125m01aByCustIdBatchDate(
			String ownBrId, String custId, String bgnDate, String endDate);

	/**
	 * 「法令遵循自評授信案件明細報表」
	 * 
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> findLms180r53IndustryLandByCntrNo(
			String cntrNo);

	/** J-109-0140_10702_B1001 增加地政士黑名單控管邏輯 **/
	public List<Map<String, Object>> findActiveMajorByCertNo(String year,
			String word, String no);

	public List<Map<String, Object>> findCLS1220R07_1(String ownBrId);

	public List<Map<String, Object>> findCLS1220R07_2(String ownBrId,
			String createTimeSince);

	/**
	 * J-110-0373 中鋼消貸進件 excel資料
	 * 
	 * @param ownBrId
	 * @param ploanPlan
	 * @param csc_applyTS_beg
	 * @param csc_applyTS_end
	 * @return
	 */
	public List<Map<String, Object>> findCLS1220R08(String ownBrId,
			String ploanPlan, String csc_applyTS_beg, String csc_applyTS_end);

	/**
	 * J-110-0373 中鋼消貸控制檔 excel資料
	 * 
	 * @param ownBrId
	 * @param grpCntrNo
	 * @return
	 */
	public List<Map<String, Object>> findCLS1220R12(String ownBrId,
			String grpCntrNo);

	/**
	 * J-110-0373 中鋼消貸撈團貸母戶編號 透過ownBrId查C122M01E中的資料
	 * 
	 * @param brno
	 * @return
	 */
	public Map<String, String> getGrpCntrNoList(String brno);

	public List<Map<String, Object>> query_cntrNo_for_C122M01A_ApplyKindPE(
			String c122_mainId);

	public List<Map<String, Object>> find_com_bgwdata_by_logsno(String logsno);

	public List<Map<String, Object>> find_com_bgwdata_by_data(String data);

	public List<Map<String, Object>> find_com_bgwdata_serviceId_txId_between_reqtime(
			String serviceId, String txId, String reqtimeBeg,
			String reqtimeEnd, String sno);

	public List<Map<String, Object>> getAccumulatedMortgageAppropriationAmount(
			String[] groupLoanMasterNoArr);

	public Map<String, Object> getSmallBussInfo(String mainId);

	public Map<String, Object> getSmallBussInfoByL140m01aOid(String mainId,
			String cntrNoOid);

	/**
	 * 取得資信簡表J10資料
	 * 
	 * @param cesMainIds
	 * @return
	 */
	public List<Map<String, Object>> findCesJ10(String... cesMainIds);

	/**
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findAllReviveFromL140m01a(String bgnDate,
			String endDate);

	/**
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> listLMS180R54T(String bgnDate,
			String endDate);

	/**
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> findSysColumnsRemarks(String tableName);

	/**
	 * J-109-0235_05097_B1004 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param propertyStr
	 * @return
	 */
	public List<Map<String, Object>> findL140m01aHasReviveByCntrNo(
			String cntrNo, String propertyStr);

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> listLMS180R54_01(String effectDate,
			String bgnDate, String endDate);

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> listLMS180R54_02(String effectDate,
			String bgnDate, String endDate);

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> listLMS180R54_03_1(String effectDate,
			String bgnDate, String endDate);

	/**
	 * 兆元振興融資方案辦理情形統計表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> listLMS180R54_03_2(String effectDate,
			String bgnDate, String endDate);

	/**
	 * J-109-0247 最新簽報書之擔保品
	 */
	public List<Map<String, Object>> getLatestCol(String custId,
			String bgnDate, String endDate);

	/**
	 * 兆元振興融資方案辦理情形預估報表(編製中)
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> listLMS180R55_01();

	/**
	 * 兆元振興融資方案辦理情形預估報表(已核准)
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> listLMS180R55_02(String effectDate);

	/**
	 * 兆元振興融資方案分行核准情形總表
	 * 
	 * J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
	 */
	public List<Map<String, Object>> listLMS180R56_01(String effectDate,
			String bgnDate, String endDate);

	/**
	 * J-108-0345 貸後管理 最新一筆維護資料之附件檔案
	 */
	public List<Map<String, Object>> findL260M01DLatest();

	/**
	 * 定期檢視 BDOCFILE By L260M01D
	 */
	public List<Map<String, Object>> findBDocFileByL260M01D();

	/**
	 * 小規模營業人授信異常通報表
	 * 
	 * J-109-0315_05097_B1001 Web e-loan企金授信新增小規模營業人央行C方案授信案件之異常通報上一個月獲核定之異動名單
	 */
	public List<Map<String, Object>> listLMS180R58_01(String bgnDate,
			String endDate);

	/**
	 * 小規模營業人額度明細表，有關總處單位引介資訊，預設引進徵信資信簡表介接官網進件之資料。
	 * 
	 * @param cesMainId
	 * @return
	 */
	public Map<String, Object> findC240m01aIntrByMainId(String cesMainId);

	/**
	 * J-109-0280 敘做條件
	 */
	public List<Map<String, Object>> queryBizCatList(String mainId);

	public List<Map<String, Object>> findL140S09AGroupByBizCat(String mainId);

	// public List<Map<String, Object>>
	// getGroupLoanBuildCaseMasterDataOfApprovedAndNotCancelled(String
	// grpCntrnoString);
	//
	// public List<Map<String, Object>>
	// getGroupLoanBuildCaseMasterDataOfEditingOrUnderReview(String
	// grpCntrnoString);

	public List<Map<String, Object>> findLaaData2(String startDate,
			String endDate);

	/**
	 * J-109-0362 青年創業及啟動金貸款辦理情形總表 LMS180R59
	 */
	public List<Map<String, Object>> listLMS180R59(String brNos, String condtion);

	public List<Map<String, Object>> findListbyL120S01Aseq(String tableName,
			String mainId);

	/**
	 * J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程
	 */
	public Map<String, Object> getLnType61Info(String mainId, String bgnDate);

	public Map<String, Object> getLnType61InfoByL140m01aOid(String mainId,
			String bgnDate, String l140m01aOid);

	/**
	 * 以OBU MEGA ID 查詢股票代號(F股，資料由徵信處維護)
	 * 
	 * @param custid
	 * @param dupno
	 * @return
	 */
	public Map<String, Object> findCesF106m01aByCustIdInMega(String custid);

	/**
	 * J-109-0351_05097_B1002 e-Loan企金「青年創業及啟動金貸款」簽報書修改
	 */
	public List<Map<String, Object>> findClsLnType61ByChairmanId(
			String chairmanId, String bgnDate);

	/**
	 * J-109-0351_10702_B1002 e-Loan青創判斷消金借款人id相同且與企金申貸之青創事業體不同
	 */
	public List<Map<String, Object>> getLnType61DocType1ByCustId(String custId,
			String bgnDate);

	/**
	 * 不符合中小企業定義(A01~A03)及貸款期間(A02:3年、A03:5年)，則系統(eLoan 與
	 * aLoan)不能讓該授信案成為經濟部紓困案件(A01~A03)
	 * 
	 * @param cesMainId
	 * @return
	 */
	public Map<String, Object> getC140M01A_TotEmp(String cesMainId);

	public List<Map<String, Object>> getCheckResultForIsSuspectedHeadAccount(
			String l140m01a_mainId);

	public List<Map<String, Object>> findC126m01aByAgntNo(String ownBrId,
			String agntNo, String applyTS_beg, String applyTS_end,
			String docStatus);

	public List<Map<String, Object>> getApprovedGroupLoanBuildCaseMasterData(
			String grpCntrnoString);

	public List<Map<String, Object>> getGroupLoanBuildCaseMasterDataOfUnderReview(
			String endOfMonth);

	public List<Map<String, Object>> getCesName(String mainId, String custId,
			String dupNo);

	/**
	 * 小規模營業人兌付振興三倍券達888張名單--明細
	 * 
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 */
	public List<Map<String, Object>> listLMS180R60_01(String brNos,
			String condtion);

	/**
	 * 額度專案種類明細表--明細
	 * 
	 * J-110-0018_05097_B1001 Web
	 * e-Loan簽報書額度明細表中增列「兆豐百億挺你專案」及「協助農地工廠合法化融資貸款」兩項專案，並產生統計報表
	 */
	public List<Map<String, Object>> listLMS180R61_01(String brNos,
			String projClass, String condition);

	public List<Map<String, Object>> findL140m01aAdcCaseNo(String type,
			String custId, String dupNo, String cntrNo, String adcCaseNo);

	public List<Map<String, Object>> checkMatchL140S02M_LandNo(String landNo,
			String address);

	public List<Map<String, Object>> getC126M01AByBranchNoAndCustId(
			String custId, String dupNo, String branchNo);

	public List<Map<String, Object>> getC126M01AByBranchNoAndCustIdOnly(
			String custId, String branchNo);

	/**
	 * 案件屬110年行銷名單來源客戶簽案資料--明細
	 * 
	 * J-110-0038_05097_B1001 Web e-Loan企金額度明細表新增「本案是否屬110年行銷名單來源客戶」並產生統計報表
	 */
	public List<Map<String, Object>> listLMS180R62_01(String brNos,
			String condtion);

	public List<Map<String, Object>> find_ploan_no_ptaData(int shiftdays);

	/**
	 * J-110-0373 中鋼消貸 撈出中鋼消貸進件資料中，尚未查詢利害關係人的資料
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findPloanCSCNoStkhData(String... ploanPlan);

	/**
	 * J-112-0390 中鋼集團消貸程式優化(e-Loan) 取得中鋼徵信整批匯入名單需要發查外部系統的清單
	 * 
	 * @param startDate
	 * @param dataCount
	 * @return
	 */
	public List<Map<String, Object>> findCSGNeedSendOuterSysData(
			String startDate, int dataCount);

	/**
	 * I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
	 * 
	 * @param caseMainId
	 * @param useBrId
	 * @param itemType
	 * @return
	 */
	public List<Map<String, Object>> get_cntrDoc_from_VLUSEDOC01_multiBr(
			String caseMainId, String[] useBrId, String itemType);

	public List<Map<String, Object>> getLatestGroupLoanBuildCaseMasterDataWithin2Years(
			String startDate, String endDate);

	public List<Map<String, Object>> getLatestGroupLoanBuildCaseMasterDataWithin2YearsByBranch(
			String startDate, String endDate, String branchNo);

	public List<Map<String, Object>> getSubAppropriationDataOfGroupLoanBuildCase(
			Set<String> grpCntrnoSet, String m_startDate, String m_endDate,
			String y_startDate, String y_endDate);

	public List<Map<String, Object>> listCLS180R52();

	/**
	 * 取得管理報表-授信異常通報案件報送統計表(不含小規模營業人貸款) J-111-0583_05097_B1001 Web
	 * e-Loan企金授信提供各營運中心可自行列印轄下分行於指定期間內所簽報異常通報之「授信異常通報案件報送統計表」
	 * 
	 * @param bgnDate
	 *            起始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	List<Map<String, Object>> listLMS180R64(List<String> custQuery,
			List<String> custParam);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * @param mainId
	 */
	public void LNF078T_deleteByExDate(String exDate);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * @param mainId
	 */
	public void LNF078T_insert(String CONTRACT_O, String LOAN_NO_O,
			String CUST_ID, String CONTRACT, String LOAN_NO, String CUSTID,
			String DUPNO, String BRANCH_O, String BRANCH, String EXDATE);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改額度序號
	 * 
	 * @param table
	 * @param column
	 * @param exDate
	 * @return
	 */
	public int updateCntrNoByLnf078t(String table, String column, String exDate);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改帳號
	 * 
	 * @param table
	 * @param column
	 * @param exDate
	 * @return
	 */
	public int updateLoanNoByLnf078t(String table, String column, String exDate);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 增加授權
	 * 
	 * @param authTable
	 * @param mainTable
	 * @param docStatusArr
	 * @param fromBranch
	 * @param toBranch
	 * @param exDate
	 * @return
	 */
	public int addAuthUnitByLnf078t(String authTable, String mainTable,
			String[] docStatusArr, String fromBranch, String toBranch,
			String exDate);

	public int addRetrialAuthUnitByLnf078T(String authTable, String mainTable,
			String[] docStatusArr, String fromBranch, String toBranch,
			String toBranchRetrialUnit, String exDate);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改CASEBRID
	 * 
	 * @param docStatusArr
	 * @param fromBranch
	 * @param toBranch
	 * @param exDate
	 * @return
	 */
	public int chgCaseBrIdByLnf078t(String[] docStatusArr, String fromBranch,
			String toBranch, String exDate);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * @param exDate
	 * @return
	 */
	public List<Map<String, Object>> LNF078T_selAllByExDate(String exDate);

	/**
	 * 
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改C801M01A(財產清冊) LOANNO
	 * 
	 * @param newLoanNo
	 * @param oldLoanNo
	 * @return
	 */
	public int updateC801m01aLoanNoByLnf078t(String newLoanNo, String oldLoanNo);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * 修改OWNBRID
	 * 
	 * @param table
	 * @param column
	 * @param docStatusArr
	 * @param exDate
	 * @return
	 */
	public int chgOwnBrIdByLnf078t(String table, String column,
			String[] docStatusArr, String exDate);

	/**
	 * J-109-0479_05097_B1004 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 * J-111-0600_05097_B1002 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
	 * 
	 * @param bgnDate
	 *            起始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	List<Map<String, Object>> listLMS180R65(List<String> custQuery,
			List<String> custParam);

	public Map<String, Object> findDocFileByMainIdAndBranchId(String mainId,
			String branchId);

	/**
	 * 取得最新一筆借款人小規模額度明細表之簽報書
	 * 
	 * @param custId
	 * @param caseBrId
	 * @return
	 */
	public List<Map<String, Object>> findL120m01aHaveSmallBussC(String custId,
			String caseBrId);

	public Map<String, Object> findCesC120s01kByMainId(String mainId);

	/**
	 * 資信簡表BY MAINID
	 */
	public Map<String, Object> findCesC120m01aByMainId(String mainId);

	/**
	 * 取得資信簡表對應之線上進件
	 */
	public Map<String, Object> findCesC240m01aByOid(String oid);

	/**
	 * 更新徵信線上進件文件狀態(簽報書核准時)
	 * 
	 * Z01:不承做 03:已核貸
	 * 
	 * @param docStatus
	 * @param oid
	 */
	public void updateC240m01aStatusByOid(String docStatus, String oid);

	/**
	 * 更新徵信線上進件分行顯示狀態 RPA簽報書處理中:B01 RPA簽報書完成:B02 RPA簽報書執行異常: B03 RPA動審表處理中:B04
	 * RPA動審表完成:B05 RPA動審表執行異常:B06 RPA信保查詢失敗:B07 RPA信保查詢完成:B08 簽報書已核准:B09
	 * 簽報書已婉卻:B10 動審表已核准:B11
	 * 
	 * @param status4Br
	 * @param oid
	 */
	public void updateC240m01aStatus4BrByOid(String status4Br, String oid);

	/**
	 * 取得小規模線上進件最新一筆
	 */
	public Map<String, Object> findCesC240m01aLastByOwnBrIdAndCustId(
			String brNo, String custId);

	public List<Map<String, Object>> findC120M01A_selMainIdaByCustId(
			String caseBrId, String custId, String dupNo);

	public int deleteC120S04WByMainIdAndDataCustomerNo(String mainId,
			String dataCustomerNo);

	public Map<String, Object> findC122m01aAgreeQueryEJIpCount(
			String agreeQueryEJIp, String applyTs);

	public Map<String, Object> findC122m01aAgreeQueryEJMtelCount(
			String agreeQueryEJMtel, String applyTs);

	/**
	 * 取得最新一筆借款人簽報書
	 * 
	 * @param custId
	 * @param caseBrId
	 * @return
	 */
	public List<Map<String, Object>> findL120m01aLastByCustId(String custId,
			String caseBrId);

	/**
	 * 取得最新一筆借款人紓困動審表明細(篩選紓困類別)
	 * 
	 * @param cntrNo
	 * @param custId
	 * @param dupNo
	 * @param rescueItem
	 * @return
	 */
	public List<Map<String, Object>> findL161s01aLastRescueDataWithRescueItem(
			String cntrNo, String custId, String dupNo, String rescueItem);

	/**
	 * 取得最新一筆借款人紓困動審表明細(不篩選紓困類別)
	 * 
	 * @param cntrNo
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findL161s01aLastRescueDataWithoutRescueItem(
			String cntrNo, String custId, String dupNo);

	public List<Map<String, Object>> getCntrNoByTable(String tableName);

	/**
	 * 取得最新一筆借款人紓困動審表明細(篩選掛件文號)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param rescueNo
	 * @return
	 */
	public List<Map<String, Object>> findL161s01aLastRescueDataWithRescueNo(
			String custId, String dupNo, String rescueNo);

	public List<Map<String, Object>> findL140M01_byLastSmallBussCWithCustid(
			String custId, boolean onlyApprove);

	/**
	 * 從額度表MAINID取得所有的簽報書案號
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> findL120M01AByL140M01A(String mainId);

	/**
	 * 檢查該簽報書是否可刪除
	 * 
	 * @param mainId
	 * @return
	 */
	public List<Map<String, Object>> L120M01ACanDeleteOrNot(String mainId);

	/**
	 * 檢查該簽報書之客戶上有幾筆未註銷之簽報書
	 * 
	 * @param mainId
	 * @return
	 */
	public String FindAmountbyMainid(String mainId);

	/**
	 * 動審表-勞工紓困簽報書資料
	 */
	public List<Map<String, Object>> getEloanCollateralFrom(String custid,
			String ownbrid);

	public List<Map<String, Object>> getOnlineApplicationDateForLaborReliefLoanDetailReportData(
			String applyDate, String custId);

	public List<Map<String, Object>> getEloanDataForLaborReliefLoanDetailReport(
			String applyDate, String custId);

	public List<Map<String, Object>> getEloanDataForLaborReliefLoanSummaryReport(
			String applyDate);

	/**
	 * J-110-0142 特定金錢信託案件量統計報表
	 */
	public List<Map<String, Object>> listCLS180R42_01(String brNo);

	/**
	 * J-110-0211_11557_B1002 配合海外東、阪行信義房屋專案，e-Loan授信管理系統新增控管措施，並開啟海外業務處即時查詢功能
	 * 
	 * @param startDate
	 * @param endDate
	 * @param otherCondition
	 * @return
	 */
	public List<Map<String, Object>> queryCLS180R53Data(String parentId,
			String brNo, String startDate, String endDate);

	/**
	 * J-111-0602 歡喜信貸eLoan流程精進修改-徵審分案流程變動 刪除該分行下，不是傳入assignEmp的剩餘資料
	 * 
	 * @param brNo
	 * @param assignEmpNos
	 * @return
	 */
	public int deleteC900m01nByNotInAssignEmpNo(String brNo,
			String[] assignEmpNos);

	/**
	 * 查出該分行下，傳入assignEmp的資料，用最後派案時間 lastAssignTime(null first)、派案順序
	 * assignOrder做排序
	 * 
	 * @param brNo
	 * @param assignEmpNos
	 * @return
	 */
	public List<Map<String, Object>> findC900m01nByBrNoAndEmpNoOrderByTimeAndOrder(
			String brNo, String[] assignEmpNos);

	/**
	 * 依借款人查詢項下已動審之特定紓困代碼類別
	 * 
	 * J-110-0288_05097_B1001 Web e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
	 * 
	 * @param custId
	 * @param dupNo
	 * @param rescueItem
	 * @return
	 */
	public List<Map<String, Object>> findL161s01aAllRescueDataWithRescueItemByCustId(
			String custId, String dupNo, String[] rescueItem);

	/**
	 * 依借款人查詢同一動審表項下已動審之特定紓困代碼類別
	 * 
	 * J-110-0288_05097_B1001 Web e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
	 * 
	 * @param custId
	 * @param dupNo
	 * @param rescueItem
	 * @return
	 */
	public List<Map<String, Object>> findL161s01aAllRescueDataWithRescueItemByCustIdAndMainId(
			String mainId, String custId, String dupNo, String[] rescueItem);

	public List<Map<String, Object>> findC140M01A_selMainIdaByCustId(
			String caseBrId, String custId, String dupNo);

	/**
	 * 徵信報告BY MAINID
	 */
	public Map<String, Object> findCesC140m01aByMainId(String mainId);

	public List<Map<String, Object>> findL140M01_byLastStartUpReliefWithCustid(
			String custId, boolean onlyApprove, String[] rescueItem,
			String[] rescueItemSub);

	public List<Map<String, Object>> getC120s01aDataSameWithLenderOrGuarantorInfo(
			String l120m01a_mainId, String custId, String coTel, String fTel,
			String mTel, String fTarGet, String CoTarGet);

	public List<Map<String, Object>> getC101s01aDataSameWithLenderOrGuarantorInfo(
			String c101s01a_mainId, String custId, String coTel, String fTel,
			String mTel, String fTarGet, String CoTarGet);

	/**
	 * 保證人資料BY MAINID
	 */
	public List<Map<String, Object>> findL140m01iOrderBy(String mainId,
			String rtype);

	/**
	 * Web e-Loan配合紐行Oracle系統建置，修改AML相關功能。 整批重送紐行ORACLE掃描
	 * 
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0047(String bgnDate,
			String endDate);

	/**
	 * 產品資訊資料BY MAINID
	 */
	public List<Map<String, Object>> findL140s02aByc340m01b(String mainId);

	/**
	 * J-110-0371 新版簽報書_個人 取得消金徵信相關資料
	 */
	public Map<String, Object> getCLSInfo(String brNo, String custId,
			String dupNo, boolean isOverSea);

	/**
	 * J-110-0363 貸後管理 取得ID階層資料
	 */
	public List<Map<String, Object>> findL260MById(String clazz,
			String ownBrId, String custId, String dupNo, String[] docStatusArray);

	/**
	 * J-110-0326_05097_B1001 Web e-Loan 授信案件之簽報增加企業淨值為負的授權檢核提示訊息
	 */
	public List<Map<String, Object>> findCesF101m01aByCustIdForNegativeNetWorth(
			String custId, String dupNo, String brNo);

	public List<Map<String, Object>> getSameScrivenerIntroductionInfo(
			String fromCaseDate, String toCaseDate, String l140m01a_mainId,
			String laaName, String laaNo);

	public List<Map<String, Object>> getSameFinancialHoldingSubsidiaryIntroductionInto(
			String fromCaseDate, String toCaseDate, String l140m01a_mainId,
			String laaName, String laaNo);

	public List<Map<String, Object>> getSameMegaBankCustomersIntroductionInto(
			String fromCaseDate, String toCaseDate, String l140m01a_mainId,
			String introCustId);

	public List<Map<String, Object>> getContractNoBySameWithGuarantorIdsInOtherCase(
			String l140m01a_mainId, List<String> idList);

	public List<Map<String, Object>> findCmsC100m01ByCntrNoForLgd(
			List<String> cntrNos);

	public List<Map<String, Object>> getCntrnoInfoOfSameAsGuarantorIdCase(
			List<String> idList);

	/**
	 * 查詢 集團企業授信條件比較表(含簽註意見)
	 * 
	 * @param custId
	 * @param brId
	 * @return
	 */
	public List<Map<String, Object>> findG120M01A_selByCustIdAndBrId(
			String custId, String brId);

	/**
	 * 新增集團企業授信條件比較表(含簽註意見)
	 * 
	 * @param g120m01aMap
	 */
	public void g120M01A_createNew(Map<String, Object> g120m01aMap);

	/**
	 * 新增集團企業授信條件比較表(含簽註意見)A01A
	 * 
	 * @param mainId
	 * @param uid
	 * @param ownBrid
	 */
	public void g120A01A_createNew(String mainId, String uid, String ownBrid);

	/**
	 * 新增集團企業授信條件比較表(含簽註意見)A01B
	 * 
	 * @param mainId
	 * @param uid
	 * @param ownBrid
	 */
	public void g120A01B_createNew(String mainId, String uid, String ownBrid);

	/**
	 * 新增文件記錄BDocLog
	 * 
	 * @param metaOid
	 */
	public void bDocLog_insert(String metaOid);

	/**
	 * 新增額度序號資料到集團企業授信條件比較表(含簽註意見)
	 * 
	 * @param cntrnoData
	 */
	public void g120S03A_createNew(Map<String, Object> cntrnoData);

	/**
	 * 新增流程主檔
	 * 
	 * @param bFlowInstMap
	 */
	public void bFlowInst_insert(Map<String, Object> bFlowInstMap);

	/**
	 * 新增流程紀錄檔
	 * 
	 * @param bFlowInstMap
	 */
	public void bFlowSeq_insert(Map<String, Object> bFlowSeqMap);

	/**
	 * 國內營業單位海外信保基金基金案件表 LMS180R67
	 */
	List<Map<String, Object>> listLMS180R67(String bgnDate, String endDate);

	List<Map<String, Object>> findC120jsonByMainIdAndTab(String mainId,
			String tab, String subTab);

	public void batchInsert_L290M01A(List<Object[]> batchValues);

	public List<Map<String, Object>> findCLS180R26(String applyStatus,
			String brNo, String applyBegDate, String applyEndDate);

	/**
	 * 歡喜信貸案件明細表 CLS180R27
	 */
	public List<Map<String, Object>> findCLS180R27(String brNo,
			String applyBegDate, String applyEndDate);

	/**
	 * 歡喜信貸ESG明細表 CLS180R27B
	 */
	public List<Map<String, Object>> findCLS180R27B(String brNo,
			String applyBegDate, String applyEndDate);

	/**
	 * 歡喜信貸KYC分案報表 CLS180R27C
	 * 
	 * @param starTime
	 *            起始時間(分案開始)
	 * @param endTime
	 *            結束時間(分案截止)
	 */
	public List<Map<String, Object>> findCLS180R27C(String starTime,
			String endTime);

	/**
	 * 歡喜信貸婉拒案件自動發送簡訊失敗顧客清單 CLS180R27D
	 * 
	 * @param starDate
	 *            進件日期
	 * @param endDate
	 *            進件日期
	 */
	public List<Map<String, Object>> findCLS180R27D(String starDate,
			String endDate);

	/**
	 * J-109-0479_05097_B1004 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 * J-111-0600_05097_B1001 Web e-Loan授信系統管理報表新增「授信簽報案件經區域營運中心接案進度控管表」
	 * 
	 * @param bgnDate
	 *            起始時間
	 * @param endDate
	 *            結束時間
	 * @param docType
	 *            1.企金 2.個金
	 * @return
	 */
	public List<Map<String, Object>> listLMS180R68(String docType,
			List<String> custQuery, List<String> custParam);

	/**
	 * 取得內部評等模型主檔MAINID
	 */
	public Map<String, Object> findCesM100m01aByOid(String oid);

	public Map<String, Object> findLastTimeCaseByCutIdAndCntrNoOrdByUpdateTime(
			String custId, String dupNo, String cntrNo, String docType,
			String mainId);

	public List<Map<String, Object>> findCms_selByOid(String cmsOid);

	public List<Map<String, Object>> findCLS722NonAppropriation(String begDate,
			String endDate);

	/*
	 * J-110-0308 覆審考核表_企金名單 MainIdListForL300S01A、ListForL300S01A兩個SQL base
	 * WHERE條件要一樣
	 */
	public List<Map<String, Object>> getLrsMainIdListForL300S01A(
			String authUnit, String branchId, String bgnDate, String endDate);

	public List<Map<String, Object>> getLrsListForL300S01A(String authUnit,
			String branchId, String bgnDate, String endDate);

	/*
	 * J-110-0308 覆審考核表_消金名單
	 */
	public List<Map<String, Object>> getCrsMainIdListForL300S01A(
			String authUnit, String branchId, String bgnDate, String endDate);

	public List<Map<String, Object>> getCrsListForL300S01A(String authUnit,
			String branchId, String bgnDate, String endDate);

	/*
	 * J-110-0308 覆審考核表_取得排名
	 */
	public List<Map<String, Object>> getRankingBoard(String authUnit,
			String bgnDate, String endDate, String[] SpecialBranchList);

	public List<Map<String, Object>> findL120s21aByMainId(String mainId);

	public List<Map<String, Object>> findL120s21bByMainId(String mainId);

	public List<Map<String, Object>> findL120s21cByMainId(String mainId);

	public List<Map<String, Object>> find_J_111_0022_CLSA(String param, int cnt);

	public void batchUpdate_J_111_0022_CLSA(List<Object[]> batchValues);

	public List<Map<String, Object>> find_J_111_0022_CLSB(String param, int cnt);

	public void batchUpdate_J_111_0022_CLSB(List<Object[]> batchValues);

	/**
	 * LMS180R69 企金綠色授信暨ESG簽報案件明細表
	 * 
	 * Sheet1_分行明細
	 * 
	 * Web e-Loan企金授信新增綠色支出、永續績效連結授信ESG
	 */
	public List<Map<String, Object>> listLMS180R69_1(String bgnDate,
			String endDate, String brType, String brNo);

	/**
	 * LMS180R76 授信案件曾涉及ESG風險因而有條件通過或未核准之情形表
	 * 
	 * J-112-0426_05097_B1002 為正確統計涉及ESG風險授信案件之審查結果新增按月產生報表。
	 * 
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> listLMS180R76_1(String bgnDate,
			String endDate, String brType, String brNo);

	// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
	public List<Map<String, Object>> findCmsCollType06ByCntrNo(String cntrNo);

	/**
	 * J-110-0505_05097_B1001 Web
	 * e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	 * 
	 * @param cntrNo_list
	 * @return
	 */
	public List<Map<String, Object>> findLastL140m01AByCntrNos(
			Set<String> cntrNo_list);

	/**
	 * J-111-0011 新增於eloan授信管理系統(906稽核處)稽核工作底稿，增列「已核准額度明細表及案件簽報書」功能鍵 撈出額度明細的資料用
	 * 
	 * @param oid
	 * @return
	 */
	public List<Map<String, Object>> findPrintL140M01AByOidForLMS1935(String oid);

	/**
	 * J-111-0011 新增於eloan授信管理系統(906稽核處)稽核工作底稿，增列「已核准額度明細表及案件簽報書」功能鍵
	 * 判斷該OID下的L192S01A是否有額度序號
	 * 
	 * @param oid
	 * @return
	 */
	public List<Map<String, Object>> checkL192S01AHavaCntrNoForLMS1935(
			String oid);

	/**
	 * J-111-0011 新增於eloan授信管理系統(906稽核處)稽核工作底稿，增列「已核准額度明細表及案件簽報書」功能鍵
	 * 撈出額度明細的資料用，不串L192S01A的額度明細!!
	 * 
	 * @param oid
	 * @return
	 */
	public List<Map<String, Object>> findPrintL140M01AByOidForLMS1935WithoutCntrNo(
			String oid);

	/**
	 * J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。 抓出需要寫入ELF601貸後管理的先行動用案件
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL160m01ANeedElf601(String startDate,
			String endDate);

	public List<Map<String, Object>> findL120m01aWithNoL120m01l(String startDate,
			String endDate);
	/**
	 * J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
	 * 抓出當日補全資料，需要將ELF601、ELF602壓成完成
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findL160m01AElf601Complete(
			String startDate, String endDate);

	/**
	 * J-110-0505_05097_B1001 Web
	 * e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	 * 
	 * @param cntrNo_list
	 * @return
	 */
	public List<Map<String, Object>> findLastL140m01AByCustId(String custId,
			String dupNo, Set<String> cntrNo_list);

	/**
	 * J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
	 * 
	 * @param stkNo
	 * @return
	 */
	public Map<String, Object> findL290m01aByStkNo(String stkNo);

	/**
	 * J-112-0337 配合授審處，在簽報書及常董會提案稿，社會責任與環境風險評估大項中，增加本行ESG風險評級結果
	 */
	public Map<String, Object> findLastC290m01aByCustId(String custId);

	public Map<String, Object> findLastC280m01aByCustId(String custId);

	public Map<String, Object> findLastC280m01aHadSbtiByCustId(String custId);

	public List<Map<String, Object>> findLastC300s01a(String custId);

	/**
	 * J-111-0107_05097_B1001 Web e-Loan企金增加借戶ESG外部綜合評分資料相關資料。
	 * 
	 * @return
	 */
	public Map<String, Object> findL290m01aMaxDate();

	public Map<String, Map<String, Object>> getCaseNoByCntrnoApprovedData(
			List<String> cntrNoList);

	/**
	 * J-110-0548 擔保品謄本 追蹤日(追蹤事項通知日期)前最新一筆擔保品
	 */
	public Map<String, Object> findLastC101m01(String brNo, String cntrNo,
			String elf601_unid, String followDate);

	/**
	 * J-110-0548 擔保品謄本 依擔保品申請編號取得謄本資料
	 */
	public Map<String, Object> findC101m01ByApplyNo(String applyNo);

	/**
	 * J-110-0548 擔保品謄本 特定時間內的謄本
	 */
	public List<Map<String, Object>> findC101m01List(String brNo,
			String cntrNo, String elf601_unid, String bgnDate, String endDate);

	/**
	 * J-110-0548 擔保品謄本 謄本附件
	 */
	public List<Map<String, Object>> findC100s02aList(String c101m01MainId);

	public Map<String, Object> findC100s02aByOid(String c100s02aOid);

	/**
	 * J-111-0025 實價登錄 特定時間內的實登
	 */
	public List<Map<String, Object>> findC101m29List(String brNo,
			String cntrNo, String elf601_unid, String bgnDate, String endDate);

	public Map<String, Object> findC101m29ByOid(String c101m29Oid);

	public Map<String, Object> findC101m29Cnt(String brNo, String cntrNo,
			String elf601_unid, String bgnDate, String endDate);

	/**
	 * J-111-0423_05097_B1001 Web
	 * e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
	 * 
	 */
	public void updateEsgDataFromLms2105v01ServiceImpl(String cntrNo,
			String esgSustainLoan, String esgSustainLoanType,
			String esgSustainLoanUnReach);

	public List<Map<String, Object>> cls_call_center_amort_cnt(String amort_ym);

	public int fill_call_center_amort_column(String amort_ym, Timestamp begTS);

	/**
	 * LMS180R72 企金授信核准案件BIS評估表
	 * 
	 * J-111-0443_05097_B1001 Web e-Loan企金授信開發授信BIS評估表
	 */
	public List<Map<String, Object>> listLMS180R72_01(List<String> custQuery,
			List<String> custParam);
	
	/**
	 * J-113-0328 企金授信核准案件BIS評估表欄位調整
	 * 
	 * LMS180R72 企金授信核准案件BIS評估表，分為依額度明細or依簽報書
	 * 
	 */
	public List<Map<String, Object>> listLMS180R72_02(List<String> custQuery,
			List<String> custParam, String caseOutputType);

	/**
	 * J-111-0411_05097_B1002 Web e-Loan企金授信新增不動產授信例外管理相關功能
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> listLMS180R73_01(List<String> custQuery,
			List<String> custParam);

	/**
	 * J-111-0615_05097_B1001 Web e-Loan授信系統管理報表不動產授信例外管理報表修改
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> listLMS180R73_02(List<String> custQuery,
			List<String> custParam);

	/**
	 * J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表 」建置，於簽報書增設相對應欄位
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> findCesEsgRejectByCustId(String custId,
			String dupNo);

	public Map<String, String> getC122m01aIntroductionBranchNoByApprovedSigningBook();

	/**
	 * J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * @param cntrNo
	 * @param prodKind
	 * @param adcCaseNo
	 */
	public void updateAdcInfo(String cntrNo, String prodKind, String adcCaseNo);

	/**
	 * J-112-0021_05097_B1001 Web e-Loan企金授信異常通報案件結果副知徵信單位
	 * 
	 * 全行最新資信簡表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findC120M01A_CompleteLast(String custId,
			String dupNo);

	/**
	 * J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
	 * 
	 * @param codeType
	 * @param codeValue
	 * @param locale
	 * @return
	 */
	public List<Map<String, Object>> findCodeTypeOrderByCodeValueAsc(
			String codeType, String codeValue, String locale);

	/**
	 * J-111-0461 取得各授信業務授權額參數檔版本
	 * 
	 * @param codeType
	 * @param codeValue
	 * @param locale
	 * @return
	 */
	public List<Map<String, Object>> getCaseLvlParamVersion(String codeType,
			String codeValue, String codeDesc, String locale);

	// J-110-0330_11565_B1001 e-Loan AO帳戶管理員
	public List<Map<String, Object>> findL140M01A_GroupByCust(String mainId);

	/**
	 * 經濟部協助中小型事業疫後振興專案貸款 & 經濟部協助中小企業轉型發展專案貸款
	 * 
	 * @param custQuery
	 * @param custParam
	 * @return
	 */
	public List<Map<String, Object>> findL140M01_byLastCntrNoWithCustidRescueItem(
			List<String> custQuery, List<String> custParam);

	/**
	 * 中小企業千億振興融資方案」統計報表
	 * 
	 * 表1-「中小企業千億振興融資方案」資金用途及各月辦理情形統計
	 * 
	 * J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	 */
	public List<Map<String, Object>> listLMS180R74_01(String effectDate,
			String bgnDate, String endDate);

	/**
	 * J-113-0125_05097_B1001
	 * 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	 */
	public List<Map<String, Object>> listLMS180R74_a_01(List<String> custQuery,
			List<String> custParam);

	/**
	 * 中小企業千億振興融資方案」統計報表
	 * 
	 * 表2-「中小企業千億振興融資方案」承作類別及金額累計統計表
	 * 
	 * J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	 */
	public List<Map<String, Object>> listLMS180R74_02(String effectDate,
			String bgnDate, String endDate);

	/**
	 * J-113-0125_05097_B1001
	 * 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	 */
	public List<Map<String, Object>> listLMS180R74_a_02(List<String> custQuery,
			List<String> custParam);

	/**
	 * 中小企業千億振興融資方案」統計報表
	 * 
	 * 表3-「中小企業千億振興融資方案」核准明細表
	 * 
	 * J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	 */
	public List<Map<String, Object>> listLMS180R74_03(String effectDate,
			String bgnDate, String endDate);

	/**
	 * J-113-0125_05097_B1001
	 * 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	 */
	public List<Map<String, Object>> listLMS180R74_a_03(List<String> custQuery,
			List<String> custParam);

	/**
	 * 中小企業千億振興融資方案」統計報表
	 * 
	 * 表4-「中小企業千億振興融資方案」簽案明細表(未核准暨已核准)
	 * 
	 * J-112-0200_05097_B1001 Web e-Loan系統新增「中小企業千億振興融資方案」統計報表
	 */
	public List<Map<String, Object>> listLMS180R74_04(String effectDate,
			String bgnDate, String endDate);

	/**
	 * J-113-0125_05097_B1001
	 * 中小企業千億振興融資方案核准明細表篩選額度增列額度性質為「續約」之額度(包含變更條件、續約及增減額、續約)等
	 */
	public List<Map<String, Object>> listLMS180R74_a_04(List<String> custQuery,
			List<String> custParam);

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	public List<Map<String, Object>> listLMS180R75(String mainId);

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	public void batchInsert_LXLSR75A(List<Object[]> valList);

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	public void LXLSR75A_deleteByDocFileOid(String docFileOid);

	/**
	 * J-112-0357 新增敘做條件異動比較表
	 */
	public List<Map<String, Object>> listLMSDoc11A(String mainId);

	public Page<Map<String, Object>> queryOtherL140S11AByL140M01A(
			ISearch search, String caseMainId, String itemType, String cntrNo);

	/**
	 * J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
	 * 
	 * @param cntrNo_list
	 * @return
	 */
	public List<Map<String, Object>> findCMS_C100m01byCntrno(
			Set<String> cntrNo_list);

	public List<Map<String, Object>> findCMS_C100m01byMainId(
			Set<String> mainId_Set);

	/**
	 * J-112-0196 動審表送呈檢查是否有已覆核登記之保證擔保品
	 */
	public List<Map<String, Object>> findCmsCollType05ByCntrNo(String cntrNo);

	/**
	 * J-112-0534 因應兆豐金控自113.1.1下架證券違約交割/上市櫃觀察名單，改引入T70 取得徵信系統的查詢結果
	 */
	public List<Map<String, Object>> findC140M01A_selectT70Result(
			String cistId, String dupNo, List<String> cesMainIds);

	/**
	 * J-112-0534 因應兆豐金控自113.1.1下架證券違約交割/上市櫃觀察名單，改引入T70 取得徵信系統的查詢結果檔案
	 */
	public Map<String, Object> findC140M01A_selectT70Html(String mainId,
			String custId);

	/**
	 * J-112-0329 撈取信貸資料(進件管理為主)
	 * 
	 * @param isFirstTime
	 *            Y-每週上傳所有文件狀態之資料；N-每日上傳非已結案之資料
	 */
	public List<Map<String, Object>> findHPCLApplyDataForDW(String isFirstTime);

	/**
	 * J-112-0329 更新信貸資料之上傳DW FLAG(進件管理為主)
	 */
	public void updateHPCLApplyDataFlagForDW(String[] oidArry);

	/**
	 * J-112-0329 撈取信貸資料(簽報書為主)
	 * 
	 * @param isFirstTime
	 *            Y-每週上傳所有文件狀態之資料；N-每日上傳非已結案之資料；D-於DW刪除簽報書為已刪除的資料
	 */
	public List<Map<String, Object>> findHPCLCaseReportDataForDW(
			String isFirstTime);

	/**
	 * J-112-0329 更新信貸資料之上傳DW FLAG(簽報書為主)
	 */
	public void updateHPCLCaseReportDataFlagForDW(String[] oidArry);

	/**
	 * J-112-0399 分行承作購置住宅貸款年限40年統計表 CLS180R60
	 */
	public List<Map<String, Object>> findCLS180R60(Date applyBegDate,
			Date applyEndDate);

	/**
	 * J-112-0568 取得 舊同一額度屬[專案種類為22-辦理企業戶購置廠辦整批分戶貸款]之筆數
	 */
	public int queryProjClass22Count(String cntrNo);

	/**
	 * J-113-0044_12473_B1001 取得內部評等試行新模型主檔
	 */
	public List<Map<String, Object>> findTmpNewCesM100m01(String custId,
			String dupNo, String[] tmpNewMow);

	/**
	 * J-113-0009房貸案件明細表 CLS180R61
	 */
	public List<Map<String, Object>> findCLS180R61(Date applyBegDate,
			Date applyEndDate);

	public Map<String, Object> findOldestL140m01aByCntrNo(String cntrNo);

	/**
	 * J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
	 * 
	 * @param custId
	 * @return
	 */
	public Map<String, Object> findC120M01A_selectGrarantorStkCatNm(
			String custId);

	/**
	 * J-113-0442 企金簽報書「社會責任與環境風險評估」新增內容
	 *
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findC260M01AByCustIdDupNo(String custId, String dupNo);

    List<Object[]> findSMEAList(Date startDate, Date endDate);

	public List<Map<String, Object>> findL140mc1bTotalBalanceOrCurrentApplyAmt(String l120m01a_mainId);
	
	public List<Map<String, Object>> findProdKind59NewCase(String custId, String dupNo);

	public List<Map<String, Object>> findRpsO140m01aByCustIdandCntrNo(String custId, String dupNo, String cntrNo);
	public void updateRpsO140S01A(String OverdueRec,String OverdueReason, String mainId);
    List<Map<String, Object>> findL260M01A_L260M01DByFilter(StringBuilder condition, List<Object> paramValues);
}
