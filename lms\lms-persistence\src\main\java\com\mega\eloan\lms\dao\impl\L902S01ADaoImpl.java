/* 
 * L902S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L902S01ADao;
import com.mega.eloan.lms.model.L902S01A;

/** 私募基金旗下事業明細檔 **/
@Repository
public class L902S01ADaoImpl extends LMSJpaDao<L902S01A, String> implements
		L902S01ADao {

	@Override
	public L902S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L902S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L902S01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L902S01A> findByMainIdIgnoreDelete(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L902S01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L902S01A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L902S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L902S01A> findByPeNo(String peNo) {
		ISearch search = createSearchTemplete();
		List<L902S01A> list = null;
		if (peNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "peNo", peNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addOrderBy("custId", false);
		search.addOrderBy("dupNo", false);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L902S01A> findByCustId(String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L902S01A> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L902S01A> findByRptMainIdAndCustId(String rptMainId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L902S01A> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "rptMainId",
				rptMainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	/**
	 * 恢復為未刪除用
	 */
	@Override
	public List<L902S01A> findByCustIdAndPeNo(String custId, String dupNo,
			String peNo) {
		ISearch search = createSearchTemplete();
		List<L902S01A> list = null;

		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "peNo", peNo);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

}