/* 
 * L180R42ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180R42A;

/** 企金新核准往來客戶月檔 **/
public interface L180R42ADao extends IGenericDao<L180R42A> {

	L180R42A findByOid(String oid);

	List<L180R42A> findByMainId(String mainId);

	List<L180R42A> findByIndex01(String mainId);

	List<L180R42A> findByIndex02(Date endDate);

	List<L180R42A> findByIndex03(String caseBrId, Date endDate);

	List<L180R42A> findByIndex04(String cntrBrId, Date endDate);

	List<L180R42A> findByIndex05(String custId, String dupNo);

	List<L180R42A> findByIndex06(String cntrMainId);

	L180R42A findByMainIdAndCntrMainId(String mainId, String cntrMainId);

	L180R42A findByCustIdExcludeSelfRptMainId(String rptMainId, String custId,
			String dupNo, String bgnDate, String endDate);

}