/* 
 * L810M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L810M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L810M01A;

/** 優惠貸款相關控制表 **/
@Repository
public class L810M01ADaoImpl extends LMSJpaDao<L810M01A, String>
	implements L810M01ADao {

	@Override
	public L810M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L810M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L810M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L810M01A> findByIndex01(String brno, String useType, String rptType){
		ISearch search = createSearchTemplete();
		List<L810M01A> list = null;
		if (brno != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "brno", brno);
		if (useType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "useType", useType);
		if (rptType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptType", rptType);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}