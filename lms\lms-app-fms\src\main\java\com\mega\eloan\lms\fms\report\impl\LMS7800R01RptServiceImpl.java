package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.LMS7800M01Page;
import com.mega.eloan.lms.fms.report.LMS7800R01RptService;
import com.mega.eloan.lms.fms.service.LMS7800Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140MM6A;
import com.mega.eloan.lms.model.L140MM6B;
import com.mega.eloan.lms.model.L140MM6C;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

@Service("lms7800r01rptservice")
public class LMS7800R01RptServiceImpl implements LMS7800R01RptService, FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS7800R01RptServiceImpl.class);
	
	@Resource
	BranchService branch;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	LMS7800Service lms7800Service;
	
	@Resource
	CodeTypeService codetypeservice;
	
	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params)  
			throws FileNotFoundException,IOException, Exception{
		LOGGER.info("into setReportData");
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS7800R01_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		OutputStream outputStream = null;
		
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L140MM6A l140mm6a = null;
		List<L140MM6B> l140mm6blist = null;

		String branchName = null;
		try {
			l140mm6a = lms7800Service.findModelByOid(L140MM6A.class, mainOid);
			if (l140mm6a == null) {
				l140mm6a = new L140MM6A();
			}

			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			l140mm6blist = (List<L140MM6B>)lms7800Service.findListByMainId(L140MM6B.class, mainId);
			if (!Util.isEmpty(l140mm6blist) && Util.notEquals(l140mm6a.getDocStatus(),CreditDocStatusEnum.海外_編製中)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM6B l140mm6b : l140mm6blist) {
					// 要加上人員代碼
					String type = Util.trim(l140mm6b.getStaffJob());
					String userId = Util.trim(l140mm6b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {	//L1. 分行經辦
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) {	//L3. 分行授信主管 
						bossId.append(bossId.length() > 0 ? "\r\n" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {	//L4. 分行覆核主管
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) {	//L5. 經副襄理
						managerid = userId + " " + value;
					}
				}
				bossid = bossId.toString();
			}
			String updater = Util.trim(l140mm6a.getUpdater()) + " " + 
							Util.trim(lmsService.getUserName(l140mm6a.getUpdater()));
			rptVariableMap.put("L140MM6A.UPDATER", updater);
			rptVariableMap.put("L140MM6B.RECHECKID", recheckid);
			rptVariableMap.put("L140MM6B.BOSSID", bossid);
			rptVariableMap.put("L140MM6B.MANAGERID", managerid);
			rptVariableMap.put("L140MM6B.APPRID", apprid);
			
			branchName = Util.nullToSpace(branch.getBranchName(Util.nullToSpace(l140mm6a.getOwnBrId())));
			rptVariableMap.put("BRANCHNAME", branchName);
			
			String cust = Util.trim(l140mm6a.getCustId()) + " " +
							Util.trim(l140mm6a.getDupNo())  + " " +
							Util.trim(l140mm6a.getCustName());
			rptVariableMap.put("CUST", cust);
			
			String appraiserNo = Util.trim(l140mm6a.getAppraiser());
			String appraiserName = Util.trim(lmsService.getUserName(appraiserNo));
			rptVariableMap.put("APPRAISER", appraiserNo + " " + appraiserName);
			String iAppraiserNo = Util.trim(l140mm6a.getInfoAppraiser());
			String iAppraiserName = Util.trim(lmsService.getUserName(iAppraiserNo));
			rptVariableMap.put("INFOAPPRAISER", iAppraiserNo + " " + iAppraiserName);
			
			rptVariableMap.put("CNTRNO", l140mm6a.getCntrNo());
			
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS7800M01Page.class);

			Map<String, CapAjaxFormResult> codeTypes = codetypeservice
								.findByCodeType(new String[] { "lms7800_resultA",
										"lms7800_resultB","lms7800_resultC"});
			
			Map<String, String> select_AList = new TreeMap<String, String>();
			select_AList = lmsService.querySynBankOfCsTypes("1B", "800");
			Map<String, String> select_BList = new TreeMap<String, String>();
			select_BList = lmsService.querySynBankOfCsTypes("06", "060");
			Map<String, String> select_CList = new TreeMap<String, String>();
			select_CList = lmsService.querySynBankOfCsTypes("1A", "700");
			Map<String, String> selectList = new TreeMap<String, String>();
			
			String[] typeArr = {"A","B","C"};
			for(String type : typeArr){
				if(Util.equals("A", type)){
					selectList = select_AList;
				} else if(Util.equals("B", type)){
					selectList = select_BList;
				} else if(Util.equals("C", type)){
					selectList = select_CList;
				} 
				
				titleRows = this.setL140mm6cData(titleRows, l140mm6a, type,
						prop, codeTypes, selectList);
				
			}
			
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}
	
	private List<Map<String, String>> setL140mm6cData(
			List<Map<String, String>> titleRows, L140MM6A l140mm6a, String type,
			Properties prop, Map<String, CapAjaxFormResult> codeTypes,
			Map<String, String> selectList) {

		Map<String, String> map = Util.setColumnMap();
		L140MM6C l140mm6c = lms7800Service.findL140mm6c(
											l140mm6a.getMainId(), type);
		if (l140mm6c != null && Util.isNotEmpty(l140mm6c)) {
			String checkYN = Util.trim(l140mm6c.getCheckYN());
			String result = Util.trim(l140mm6c.getResult());
			String contact = Util.trim(l140mm6c.getContact());
			if(Util.equals("X11", contact)){
				contact = "財務處";
			} else {
				contact = selectList.get(contact);
			}
			String memo = l140mm6c.getMemo();
			
			map.put("ReportBean.column01", prop.getProperty("L140MM6A.type"+type));
			map.put("ReportBean.column02", prop.getProperty(checkYN));
			map.put("ReportBean.column03", Util.isNotEmpty(result) ? codeTypes.get("lms7800_result"+type)
												.get(result).toString() : "");
			map.put("ReportBean.column04", Util.isNotEmpty(contact) ? contact+"　"+memo : memo);
			titleRows.add(map);
		}

		return titleRows;
	}
}
