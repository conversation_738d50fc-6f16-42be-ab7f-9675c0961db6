/* 
 * C900M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 產品種類對應表（LN.LNF113） **/
@NamedEntityGraph(name = "C900M01B-entity-graph", attributeNodes = { 
		@NamedAttributeNode("c900m01a"),
		@NamedAttributeNode("c900m01d")
		})
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C900M01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"prodKind", "subjCode" }))
public class C900M01B extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODKIND", referencedColumnName = "PRODKIND", nullable = false, insertable = false, updatable = false)
	private C900M01A c900m01a;
	
	public C900M01A getC900m01a() {
		return c900m01a;
	}

	public void setC900m01a(C900M01A c900m01a) {
		this.c900m01a = c900m01a;
	}

	/**
	 * JOIN條件與關聯檔By會計科子目名稱檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBJCODE", referencedColumnName = "SUBJCODE", insertable = false, updatable = false)
	private C900M01D c900m01d;

	public C900M01D getC900M01D() {
		return c900m01d;
	}

	/**
	 * JOIN條件 產品種類關聯檔 (C900M01C)
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "c900m01b", fetch = FetchType.LAZY)
	private Set<C900M01C> c900m01c;

	public Set<C900M01C> getC900M01C() {
		return c900m01c;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	/** 產品代號 **/
	@Size(max = 2)
	@Column(name = "PRODKIND", length = 2, columnDefinition = "VARCHAR(2)", nullable = false)
	private String prodKind;

	/** 會計科子細目 **/
	@Size(max = 8)
	@Column(name = "SUBJCODE", length = 8, columnDefinition = "VARCHAR(8)", nullable = false)
	private String subjCode;

	/**
	 * 計息方式(多筆) {1:按月收息, 2:期付金, P:透支end, Q:透支top}<br/>
	 * Ex:1|2
	 */
	@Size(max = 36)
	@Column(name = "RINTWAY", length = 36, columnDefinition = "VARCHAR(36)")
	private String rIntWay;

	/**
	 * 是否取消
	 * <p/>
	 * Y|是<br/>
	 * N|否<br/>
	 * ※當為Y時，新作時不提供選擇
	 */
	@Size(max = 1)
	@Column(name = "ISCANCEL", length = 1, columnDefinition = "CHAR(1)")
	private String isCancel;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得產品代號 **/
	public String getProdKind() {
		return this.prodKind;
	}

	/** 設定產品代號 **/
	public void setProdKind(String value) {
		this.prodKind = value;
	}

	/** 取得會計科子細目 **/
	public String getSubjCode() {
		return this.subjCode;
	}

	/** 設定會計科子細目 **/
	public void setSubjCode(String value) {
		this.subjCode = value;
	}

	/**
	 * 取得計息方式(多筆) {1:按月收息, 2:期付金, P:透支end, Q:透支top}<br/>
	 * Ex:1|2
	 */
	public String getRIntWay() {
		return this.rIntWay;
	}

	/**
	 * 設定計息方式(多筆) {1:按月收息, 2:期付金, P:透支end, Q:透支top}<br/>
	 * Ex:1|2
	 **/
	public void setRIntWay(String value) {
		this.rIntWay = value;
	}

	/**
	 * 取得是否取消
	 * <p/>
	 * Y|是<br/>
	 * N|否<br/>
	 * ※當為Y時，新作時不提供選擇
	 */
	public String getIsCancel() {
		return this.isCancel;
	}

	/**
	 * 設定是否取消
	 * <p/>
	 * Y|是<br/>
	 * N|否<br/>
	 * ※當為Y時，新作時不提供選擇
	 **/
	public void setIsCancel(String value) {
		this.isCancel = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
