var initDfd = initDfd || new $.Deferred();
initDfd.done(function(json){
	build_c241m01c(json);
	//在 build 完頁面後,若 M01 是 lockDoc, 也跟著 lock
	if(json['initControl_lockDoc']){
		$("#tabForm").lockDoc();
	}
	//===================================================
	$("#btn_c241m01c_latestVer").click(function(){
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){	
				$.ajax({
		           type: "POST",
		           handler: _handler,
		           data: {
		               formAction: "c241m01c_latestVer",
		               mainOid: $("#mainOid").val()
		           }
		           }).done(function(responseData){
		        	   	var page = responseJSON.page;
	        	   		var tData = {'mainDocStatus': $("#mainDocStatus").val()
   		        	   		, 'mainId': $("#mainId").val()
   		        	   		, 'mainOid': $("#mainOid").val()
		        	   	};
		        	   	$.form.submit({ url: page , data: tData });
		       });
			}
        });
	});
	//===================================================
	$("#btn_c241m01c_defaultVal").click(function(){
		$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "c241m01c_defaultVal",
               mainOid: $("#mainOid").val()
           }
           }).done(function(responseData){
           	var tabForm = $("#tabForm");
           	
           	var map = responseData.defVal;
           	$.each(map, function(def_key, def_val) {
           		$("[name=_chkResult_"+def_key+"][value="+def_val+"]").attr("checked", "checked");
           	});
           				
       });
	});	
	//===================================================
	// J-111-0560 配合授信審查處，Web-eloan授信管理系統，覆審作業聯徵資料PPA已查詢部份,增加一鍵查詢功能，自動比對債票信及卡信資料
	$("#btn_getEjcicReusltRecord").click(function(){
		$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "getEjcicReusltRecord",
               mainOid: $("#mainOid").val()
           }
           }).done(function(responseData){
           	var tabForm = $("#tabForm");
           	tabForm.injectData(responseData);
       });
	});	
	//===================================================
	function build_c241m01c(json){
		{
			/*
			  A_徵信事項			B_債權確保			C_其他
			  D_開辦徵信事項		E_專戶款項撥付作業	F_糾紛案件處理	*DEF是 價金履保
			  Y_項目附屬選項
			  Z_電腦建檔資料
			 */
			var $c241m01c_content = $("#c241m01c_content");
			$c241m01c_content.find("tr.tr_itemType").css("background-color","lightgray");
			
			$.each(['A', 'B', 'C', 'D', 'E', 'F', 'Y'], function(idx_itemType, itemType) {
				var itemTypeDesc = json.c241m01c_title[itemType];
				var arr = json.c241m01c_list[itemType];
				var c241m01c_chkText_maxlength = json.c241m01c_chkText_maxlength; 
				var	c241m01c_chkText_maxlengthC = json.c241m01c_chkText_maxlengthC;
				if(arr.length>0){
					$.each(arr, function(idx, jsonItem) {
						//LMS2411M01Formhandler :: setC241M01C
						//每一個 itemType 包含的項目
					
						var $tr = $c241m01c_content.find("tr#tr_"+jsonItem.itemNo);
						//填入 tr 的 attr
						if(true){
							$tr.attr("c241m01c_oid", "c241m01c_"+jsonItem.oid);
							$tr.attr("c241m01c_itemSeq", jsonItem.itemSeq);
						}
						
						//覆審號碼
						if(true){
							$tr.find("td.td_itemSeq").append("&nbsp;&nbsp;"+jsonItem.itemSeq+"&nbsp;&nbsp;"
									+"<div class='item' style='display:none'>"+jsonItem.itemNo+":"+jsonItem.chkResult+"</div>");	
						}
						
						//覆審條文
						if(true){
							$tr.find("td.td_chkItem").append(jsonItem.chkItem);
							if(jsonItem.ptItem=='Y'){
								var dyna = [];
								dyna.push("<div style='margin-top:12px;'>");
								
								dyna.push(((json['initControl_lockDoc'])?"<span>應負責經理：</span>":"<input type='button' value='應負責經理：' class='btnUpdateItemNoPtMgrId'>")
									+"<span class='color-blue ptMgrIdName' id='ptMgrIdName_"+(jsonItem.itemNo)+"'>"
									+jsonItem.ptMgrId+" "+jsonItem._ptMgrName
									+"</span>"
								);	
								
								dyna.push("</div>");
								//~~~~~~
								$tr.find("td.td_chkItem").append(dyna.join(""));
							}
							// J-108-0268 逾期情形
//							if(jsonItem._overDue=='Y'){
//								var dyna = [];
//								dyna.push("<div style='margin-top:12px;'>");
//								dyna.push(((json['initControl_lockDoc'])?"<span>逾期情形：</span>":"<input type='button' value='查詢逾期情形' class='btnOverDue'>")
//									+"<span class='overDueText' id='overDueText'>"+jsonItem.overDueText+"</span>");
//								dyna.push("</div>");
//								$tr.find("td.td_chkItem").append(dyna.join(""));
//							}
						}							
						
						//覆審結果 	
						//[]是 []否 [] 一
						if(true){
							var _name_chkResult = "_chkResult_"+(jsonItem.itemNo);
							var fmt = jsonItem._chkResult_fmt;
							var dyna = []
							build_radio(dyna, _name_chkResult, fmt, jsonItem.chkResult);	
							$tr.find("td.td_chkResult").append(dyna.join(""));				
							
						}
						
						//文字欄位
						if(true){
						
							if(jsonItem._chkPreReview_fmt){//處理chkPreReview
								var _chkPreReview_fmt = jsonItem._chkPreReview_fmt;
								var _name_chkPreReview = "_chkPreReview_"+(jsonItem.itemNo);
								var dyna = []
								dyna.push("<div style='white-space: nowrap;'>");								
								build_radio(dyna, _name_chkPreReview, _chkPreReview_fmt, jsonItem.chkPreReview);
								dyna.push("</div>");
								$tr.find("td.td_memo").append(dyna.join(""));
							}
							
							var _name_chkText = "_chkText_"+(jsonItem.itemNo);								
							$tr.find("td.td_memo").append("<textarea name='"+_name_chkText+"' id='"+_name_chkText+"' "
									+"maxlength='"+c241m01c_chkText_maxlength+"' maxlengthC='"+c241m01c_chkText_maxlengthC+"' "
									+"class='my_taClass' rows='1' ></textarea>");
								
						}
						
					});	
				}else{
					//itemType 下的項目若為0,不顯示
				}
			});

			$.each(['A', 'B', 'C', 'D', 'E', 'F', 'Y'], function(idx_itemType, itemType) {				
				var arr = json.c241m01c_list[itemType];
				if(arr.length>0){
					$.each(arr, function(idx, jsonItem) {
						var _name_chkText = "_chkText_"+(jsonItem.itemNo);
						$("textarea.my_taClass[name="+_name_chkText+"]").val(jsonItem.chkText);
					});
				}
			});
			
			$(".btnUpdateItemNoPtMgrId").click(function(){
				//為讓 ajax 能存取
				var $tr = $(this).closest("tr");
				
				var c241m01c_oid = $tr.attr("c241m01c_oid");
				var c241m01c_itemSeq = $tr.attr("c241m01c_itemSeq");
				var exist_val = $tr.find(".ptMgrIdName").val();
				//======
				var options = {'title':"第"+c241m01c_itemSeq+"項" ,'exist_val': exist_val};
				
				var my_dfd = $.Deferred();				
				my_dfd.done(function(json){					
					$.ajax({type: "POST", handler: _handler,
						data: { formAction: "update_itemNo_ptMgrId"
	        	            , 'model_oid':c241m01c_oid
	        	            , 'ptMgrId':json.ptMgrId
	        	        }
	        	        }).done(function(json){
	        	        	$tr.find(".ptMgrIdName").val(json.id+" "+json.name);
	        		});
		    	});    		
				
				RetrialPtMgrIdPanelAction.open(options, my_dfd);
						
			});
			
			// 逾期情形
			$(".btnOverDue").click(function(){
				$.ajax({
					type: "POST",
		            handler: _handler,	//LMS2411M01Formhandler
		            data: {
						formAction: "getOverDueData",
						mainOid: $("#mainOid").val()
					}
		            }).done(function(json){
						$(".overDueText").val(json.overDueText);
		        });		
			});
			
		}
		
		//=====================
		{//Z類			
			$.each(['Z'], function(idx_itemType, itemType) {
				var itemTypeDesc = json.c241m01c_title[itemType];
				var arr = json.c241m01c_list[itemType];
				
				if(arr.length>0){
					//每一個 itemType 包含的項目
					$.each(arr, function(idx, jsonItem) {
						
						var _name_chkResult = "_chkResult_"+(jsonItem.itemNo);
						var radioName = _name_chkResult;
						var chooseVal = jsonItem.chkResult;
						var fmt = jsonItem._chkResult_fmt;
						
						var $tr = $("tr#tr_"+jsonItem.itemNo);
						if(fmt.length==0){
							//純文字顯示
						}else{
							if(true){
								$tr.find("td.td_chkItem").append(jsonItem.chkItem);	// chkItem 是覆審條款文字。例如：借戶或保證人是否無退票記錄？							
								$.each(fmt.split("|"), function(fmt_idx, fmt_val) { //fmt 可能是 "空白"(titleRow) 或是   用|隔開的"Y|N|K"
									//maybe Y3
									var radioVal = fmt_val.substring(0, 1);									
									//~~~~~~
									var _td_class = ".td_chkResult_radioNotFound";
									if(fmt_idx==0){
										_td_class = ".td_chkResult_1st"; 
									}else if(fmt_idx==1){
										_td_class = ".td_chkResult_2nd";
									}else if(fmt_idx==2){
										_td_class = ".td_chkResult_3rd";
									}									
									//~~~~~~
									/*
									        在 html 裡已寫 
									   <td class='td_chkResult_1st' nowrap></td>
									   <td class='td_chkResult_2nd' nowrap></td>
									   <td class='td_chkResult_3rd' nowrap></td>
									   
									        再於 js 用
									   $tr.find("td.td_chkResult_1st").append("<label> </label>");
									   
									       去填入 radioButton 
									*/   
									var attr = (chooseVal==radioVal)?" checked ":"";
									$tr.find("td"+_td_class).append("<label><input name='"+radioName+"' "
											+" id='"+(radioName+"_v_"+radioVal)+"' type='radio' "
											+" value='"+radioVal +"' "+attr+">"+i18n.lms2411m01[("label."+fmt_val)]+"</label>");
								});
							}
						}
					});	
				}
			});
			
			if(true){
				var $tr = $("#tr_NY20");
				$tr.find("td.td_chkItem").append("<input type='button' id='btn_NY20_Y' style='margin-left:15px;' value='全部為是' />");		
			}
			if(true){
				$("#btn_all_z_y").click(function(){
					$(".z_chkItem").find("input[type=radio][value=Y]").attr("checked", "checked");
				});
				
				$("#btn_NY20_Y").click(function(){
					$("tr#tr_NY20").find("input[type=radio][value=Y]").attr("checked", "checked");
					$("tr#tr_NY2A").find("input[type=radio][value=Y]").attr("checked", "checked");
					$("tr#tr_NY2B").find("input[type=radio][value=Y]").attr("checked", "checked");
					$("tr#tr_NY2C").find("input[type=radio][value=Y]").attr("checked", "checked");
					$("tr#tr_NY2D").find("input[type=radio][value=Y]").attr("checked", "checked");
				});
				//---
				//案下授信額度  []有   [] 無   擔保品，若.......
				$("input[name=_chkResult_ZB1A]:radio").change(function(){
					if($(this).val()=="N"){
						/*
						 * 使用分區
						 * 用地類別
						 * 土地性質
						 */
						$("input[name=_chkResult_ZB11][value=K]:radio").attr("checked", "checked");
						$("input[name=_chkResult_ZB12][value=K]:radio").attr("checked", "checked");
						$("input[name=_chkResult_ZB13][value=K]:radio").attr("checked", "checked");
					}
				});
				
				//---
				//授信戶案下 □有  □無 額度之融資業務分類A-LOAN註記為「#」或海外AS-400註記為「A0#」，若勾「有」則應檢視下列事項
				$("input[name=_chkResult_ZB2A]:radio").change(function(){
					if($(this).val()=="N"){
						/*
						 * 不動產暨72-2相關資訊註記欄位
						 */
						$("input[name=_chkResult_ZB21][value=K]:radio").attr("checked", "checked");
					}
				});

				
				
				//---
				// 	借戶是否依照約定條件履行？借戶若有違反承諾或約定事項是否依核定條件處置？
				$("input[name=_chkResult_NY10]:radio").change(function(){
					if($(this).val()=="N"){
						$("input[name=_chkResult_NY1A][value=K]:radio").attr("checked", "checked");
						$("input[name=_chkResult_NY1B][value=K]:radio").attr("checked", "checked");
						$("textarea.my_taClass[name=_chkText_NY1A]").val('');
						$("textarea.my_taClass[name=_chkText_NY1B]").val('');
					}
				});
			}			
		}
		
		//=====================
		{//特別處理 textarea 的高度
			$("textarea.my_taClass").css('overflow-y','hidden').css('width', '150px').bind("keyup focus", expandText );
	        //在初始化時,若 textarea 有N列,展開
	        $.each( $("textarea.my_taClass"), function (idx, element) {
	            if ( $(element).val().length > 0) {                
	                $(element).trigger('focus');            
	            }            
	        });	
		}
	}	
	
	function build_radio(dyna, radioName, srcStr, chooseVal){
		$.each(srcStr.split("|"), function(idx, val_item) {
			var radioVal = val_item.substring(0, 1);
			var attr = (chooseVal==radioVal)?" checked ":"";
			dyna.push("<label><input name='"+radioName+"' id='"+(radioName+"_v_"+radioVal)+"' type='radio' value='"+radioVal +"' "+attr+">"+i18n.lms2411m01[("label."+val_item)]+"</label>");
		});
	}

});

//http://perplexed.co.uk/596_expanding_textarea_as_you_type.htm
var expandText = function(){    
    var el = this;
    //if(el.tagName!=="textarea"){return;}
    // has the scroll height changed?, we do this because we can successfully change the height
    var prvLen = el.preValueLength;
    el.preValueLength = el.value.length;
    if(el.scrollHeight===el.prvScrollHeight&&el.prvOffsetHeight===el.offsetHeight&&el.value.length>=prvLen){    	
        return;
    }
    while(el.rows>1 && el.scrollHeight<el.offsetHeight){
        el.rows--;
    }
    var h=0;
    while(el.scrollHeight > el.offsetHeight && h!==el.offsetHeight && (h=el.offsetHeight) ){
        el.rows++;
    }    
    el.rows++;    
    el.prvScrollHeight = el.scrollHeight;
    el.prvOffsetHeight = el.offsetHeight;     
};

function showItemNo(){
	$(".item").show().css("color","blue");
}