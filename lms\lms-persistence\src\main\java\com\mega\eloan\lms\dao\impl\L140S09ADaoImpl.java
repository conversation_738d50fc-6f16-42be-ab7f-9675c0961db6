/* 
 * L140S09ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140S09ADao;
import com.mega.eloan.lms.model.L140S09A;
import tw.com.jcs.common.Util;

/** 其他敘作條件資訊檔 **/
@Repository
public class L140S09ADaoImpl extends LMSJpaDao<L140S09A, String>
	implements L140S09ADao {

	@Override
	public L140S09A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S09A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seqNum", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S09A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140S09A findMaxSeqNumByMainId(String mainId, String bizCat, Integer bizCatId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(Util.isNotEmpty(bizCat)) {
			search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
			search.addSearchModeParameters(SearchMode.EQUALS, "bizCatId", bizCatId);
		}
		search.addOrderBy("seqNum", true);
		return findUniqueOrNone(search);
	}

	@Override
	public L140S09A findMaxBizCatSeqNumByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("bizCatSeqNum", true);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S09A> findExist(String mainId, String loanTPs, String bizCat, String bizItem) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(Util.isNotEmpty(Util.trim(loanTPs))) {
			search.addSearchModeParameters(SearchMode.EQUALS, "loanTPs", loanTPs);
		}
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		if(Util.isNotEmpty(Util.trim(bizItem))) {
			search.addSearchModeParameters(SearchMode.EQUALS, "bizItem", bizItem);
		}
		search.addOrderBy("seqNum", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S09A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S09A> findByLoanTPs(String mainId, String loanTPs) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanTPs", loanTPs);
//		search.addOrderBy("bizCat", false);
		search.addOrderBy("seqNum", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S09A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S09A> findByBizCat(String mainId, String bizCat) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addOrderBy("seqNum", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S09A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140S09A findMaxBizCatIdByMainIdAndBizCat(String mainId, String bizCat) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(bizCat != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		}
		search.addOrderBy("bizCatId", true);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S09A> findByBizCatAndId(String mainId, String bizCat, Integer bizCatId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCatId", bizCatId);
		search.addOrderBy("seqNum", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S09A> list = createQuery(search).getResultList();
		return list;
	}
}