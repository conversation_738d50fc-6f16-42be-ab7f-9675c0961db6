/* 
 *MisQuotapprServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisQuotapprService;

/**
 * <pre>
 * 授信額度檔 QUOTAPPR (MIS.ELV38301)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
@Service
public class MisQuotapprServiceImpl extends AbstractMFAloanJdbc implements
		MisQuotapprService {

	@Override
	public void updateByUniqueKey(BigDecimal unionAmt, String updater,
			BigDecimal curAmt, String caseType, String custId, String dupNo,
			String cntrNo) {
		this.getJdbc().update(
				"QUOTAPPR.update",
				new Object[] { unionAmt, updater, curAmt, caseType, custId,
						dupNo, cntrNo });
	}

	@Override
	public void delByUniqueKey(String custId, String dupNo, String cntrNo,
			String sDate) {
		this.getJdbc().update("QUOTAPPR.delByUniqueKey",
				new Object[] { custId, dupNo, cntrNo, sDate });

	}

	@Override
	public void delByUniqueKeyWithoutONLNTIME(String custId, String dupNo,
			String cntrNo, String sDate) {
		this.getJdbc().update("QUOTAPPR.delByUniqueKeyWithoutONLNTIME",
				new Object[] { custId, dupNo, cntrNo, sDate });

	}

	// 海外動審表覆核時寫回QUOTAPPR用
	// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
	@Override
	public void insert(String custid, String dupno, String cntrno,
			String sdate, String casetype, String lnflag, BigDecimal oldamt,
			BigDecimal curamt, String oldcurr, String curcurr, String lnqtflag,
			String reclflag, String sguflag, String lrptype, String llnno,
			String llnfdate, String llnedate, int llnmon, String lnuseno,
			String usefmdt, String useendt, int useftmn, String memo,
			String grantno, String commborw, String updater, String reclchg,
			String sguchg, String gutflag, BigDecimal gutper, String llneflag,
			String useeflag, String lnnoflag, String unichgflag, String reflag,
			BigDecimal unionamt, BigDecimal shareamt, String permittype,
			String hideunion, String setdate, String unionarea,
			String unionrole, String riskarea, String existdate,
			String feedate, String countrydt, String crdttbl, String mowtype,
			String mowtbl1, String syndipfd, String cokind, String cntrnom,
			String rclno, String documentno, String crdtymd, String crdtbr,
			String mowymd, String mowbr, String controlcd, String duringflag,
			BigDecimal ltvrate, String locationcd, String jcicmark,
			String promise, String factType, String commsno,
			BigDecimal riskFactors, BigDecimal riskActAmt, String reViewDate,
			BigDecimal reViewChg1, String unsecureFlag, String isEfin,
			String prodKind, String isNonSMEProjLoan,
			BigDecimal nonSMEProjLoanAmt, String exceptFlag, String itwCode,
			String inSmeFg, BigDecimal inSmeToAmt, BigDecimal inSmeCaAmt,
			String isHedge, BigDecimal enhanceAmt, String netSwft,
			BigDecimal netAmt, BigDecimal netAmtUnit, BigDecimal cgfRate,
			String cgfDate, String projClass, String isRevive,
			String revTarget, String revSubItem, String revPurpose,
			String itwCodeCoreBuss, String esggFg, String esggType,
			String esgsFg, String esgsType, String esgsUnre,
			BigDecimal cntrLgd, String stdAuth, String depositFg,
			String experf_fg, String flaw_fg, BigDecimal flaw_amt,
			String socialFlag, String socialKind, String socialTa, String socialResp) {
		this.getJdbc().update(
				"QUOTAPPR.insert",
				new Object[] { custid, dupno, cntrno, sdate, casetype, lnflag,
						oldamt, curamt, oldcurr, curcurr, lnqtflag, reclflag,
						sguflag, lrptype, llnno, llnfdate, llnedate, llnmon,
						lnuseno, usefmdt, useendt, useftmn, memo, grantno,
						commborw, updater, reclchg, sguchg, gutflag, gutper,
						llneflag, useeflag, lnnoflag, unichgflag, reflag,
						unionamt, shareamt, permittype, hideunion, setdate,
						unionarea, unionrole, riskarea, existdate, feedate,
						countrydt, crdttbl, mowtype, mowtbl1, syndipfd, cokind,
						cntrnom, rclno, documentno, crdtymd, crdtbr, mowymd,
						mowbr, controlcd, duringflag, ltvrate, locationcd,
						jcicmark, promise, factType, commsno, riskFactors,
						riskActAmt, reViewDate, reViewChg1, unsecureFlag,
						isEfin, prodKind, isNonSMEProjLoan, nonSMEProjLoanAmt,
						exceptFlag, itwCode, inSmeFg, inSmeToAmt, inSmeCaAmt,
						isHedge, enhanceAmt, netSwft, netAmt, netAmtUnit,
						cgfRate, cgfDate, projClass, isRevive, revTarget,
						revSubItem, revPurpose, itwCodeCoreBuss, esggFg,
						esggType, esgsFg, esgsType, esgsUnre, cntrLgd, stdAuth,
						depositFg, experf_fg, flaw_fg, flaw_amt,
						socialFlag, socialKind, socialTa, socialResp});

	}

	// 國內動審表覆核用
	@Override
	public void insertForInside(String custId, String dupNo, String cntrNo,
			String sDate, String caseType, String lnFlag, BigDecimal oldAmt,
			BigDecimal curAmt, String oldCurr, String curCurr, String lnqtFlag,
			String reclFlag, String sguFlag, String lrpType, String llnno,
			String llnfDate, String llnedate, int llnmon, String lnuseno,
			String usefmdt, String useendt, int useftmn, String memo,
			String grantno, String commborw, String updater, String reclchg,
			String sguchg, String gutflag, BigDecimal gutper, String llneflag,
			String useeflag, String lnnoflag, String unichgflag, String reflag,
			BigDecimal unionamt, BigDecimal shareamt, String permittype,
			String hideunion, String setdate, String unionarea,
			String unionrole, String riskarea, String existdate,
			String feedate, String countrydt, String crdttbl, String mowtype,
			String mowtbl1, String syndipfd, String cokind, String cntrnom,
			String rclno, String documentno, String crdtYmd, String crdtBr,
			String mowYmd, String mowbr, String controlcd, String duringFlag,
			BigDecimal ltvRate, String locationcd, String jcicMark,
			String promise, String factType, String commsno, String chinaivt,
			String chinacur, BigDecimal chinaamt, BigDecimal signamt,
			String noisurea, BigDecimal noisuort, String noisudesp,
			String plusreason, String residential, String buildYN,
			BigDecimal nowAMT, BigDecimal valueAMT, String commonYN,
			String shareCollYN, BigDecimal shareCollAmt, String isLimitCust,
			String isHighHouse, String houseYN, String houseType,
			String purposeType, String cmsType, String keepYN,
			String plusReasonMeMo, String sit3No, String sit4No,
			String cmsOther, BigDecimal loanPer, String cbHlChk,
			BigDecimal appAmt, String applyDate, BigDecimal riskFactors,
			BigDecimal riskActAmt, String reViewDate, BigDecimal reViewChg1,
			String unsecureFlag, String isEfin, String prodKind,
			String isNonSMEProjLoan, BigDecimal nonSMEProjLoanAmt,
			String exceptFlag, String itwCode, String inSmeFg,
			BigDecimal inSmeToAmt, BigDecimal inSmeCaAmt, String isHedge,
			BigDecimal enhanceAmt, String netSwft, BigDecimal netAmt,
			BigDecimal netAmtUnit, BigDecimal cgfRate, String cgfDate,
			String projClass, String experf_fg, String flaw_fg,
			BigDecimal flaw_amt, String isRescue, String rescueItem,
			BigDecimal rescueRate, String rescueIbDate, String isCbRefin,
			String rescueItemSub, String rescueNo, BigDecimal empCount,
			String isSmallBuss, String isSole, String soleType,
			String hasRegis, String isRevive, String revTarget,
			String revSubItem, String revPurpose, String cbRefinDt,
			String rescueIndustry, String rescueCity, String version,
			String realEstateLoanLimitReason, String isRescue_el,
			String rescue110, String rescueItemN, BigDecimal rescueRateN,
			String rescueItem_el, String rescueIbDateN, String rescueNoN,
			BigDecimal rescueNdfGutPercent, String isTurnoverDecreased,
			String rescueSn, String esggFg, String esggType, String esgsFg,
			String esgsType, String esgsUnre, String hLoanLimit_2,
			Date endDate, String itwCodeCoreBuss, BigDecimal cntrLgd,
			String stdAuth, String depositFg, String RESCUE_C_FG,
			Date RESCUE_C_SD, BigDecimal RESCUE_C_RT, Date RESCUE_C_ED,
			String isRenew, String isPayOldQuota, BigDecimal oldQuota,
			BigDecimal payOldAmt, String payOldAmtItem,
			String isMatchUnsoldHouseItem, String isSaleCase, Date lstDate,
			BigDecimal timeVal,
			String socialFlag, String socialKind, String socialTa, String socialResp) {

		this.getJdbc().update(
				"QUOTAPPR.insertForInside",
				new Object[] { custId, dupNo, cntrNo, sDate, caseType, lnFlag,
						oldAmt, curAmt, oldCurr, curCurr, lnqtFlag, reclFlag,
						sguFlag, lrpType, llnno, llnfDate, llnedate, llnmon,
						lnuseno, usefmdt, useendt, useftmn, memo, grantno,
						commborw, updater, reclchg, sguchg, gutflag, gutper,
						llneflag, useeflag, lnnoflag, unichgflag, reflag,
						unionamt, shareamt, permittype, hideunion, setdate,
						unionarea, unionrole, riskarea, existdate, feedate,
						countrydt, crdttbl, mowtype, mowtbl1, syndipfd, cokind,
						cntrnom, rclno, documentno, crdtYmd, crdtBr, mowYmd,
						mowbr, controlcd, duringFlag, ltvRate, locationcd,
						jcicMark, promise, factType, commsno, chinaivt,
						chinacur, chinaamt, signamt, noisurea, noisuort,
						noisudesp, plusreason, residential, buildYN, nowAMT,
						valueAMT, commonYN, shareCollYN, shareCollAmt,
						isLimitCust, isHighHouse, houseYN, houseType,
						purposeType, cmsType, keepYN, plusReasonMeMo, sit3No,
						sit4No, cmsOther, loanPer, cbHlChk, appAmt, applyDate,
						riskFactors, riskActAmt, reViewDate, reViewChg1,
						unsecureFlag, isEfin, prodKind, isNonSMEProjLoan,
						nonSMEProjLoanAmt, exceptFlag, itwCode, inSmeFg,
						inSmeToAmt, inSmeCaAmt, isHedge, enhanceAmt, netSwft,
						netAmt, netAmtUnit, cgfRate, cgfDate, projClass,
						experf_fg, flaw_fg, flaw_amt, isRescue, rescueItem,
						rescueRate, rescueIbDate, isCbRefin, rescueItemSub,
						rescueNo, empCount, isSmallBuss, isSole, soleType,
						hasRegis, isRevive, revTarget, revSubItem, revPurpose,
						cbRefinDt, rescueIndustry, rescueCity, version,
						realEstateLoanLimitReason, isRescue_el, rescue110,
						rescueItemN, rescueRateN, rescueItem_el, rescueIbDateN,
						rescueNoN, rescueNdfGutPercent, isTurnoverDecreased,
						rescueSn, esggFg, esggType, esgsFg, esgsType, esgsUnre,
						hLoanLimit_2, endDate, itwCodeCoreBuss, cntrLgd,
						stdAuth, depositFg, RESCUE_C_FG, RESCUE_C_SD,
						RESCUE_C_RT, RESCUE_C_ED, isRenew, isPayOldQuota,
						oldQuota, payOldAmt, payOldAmtItem,
						isMatchUnsoldHouseItem, isSaleCase, lstDate, timeVal,
						socialFlag, socialKind, socialTa, socialResp});

	}

	@Override
	public Map<String, Object> findByKey(String custId, String dupNo,
			String cntrNo, String sDate) {
		return this.getJdbc().queryForMap("QUOTAPPR.selByKey",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	@Override
	public Map<String, Object> findByKey2(String custId, String dupNo,
			String cntrNo) {
		return this.getJdbc().queryForMap("QUOTAPPR.selByKey2",
				new Object[] { custId, dupNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findBy2105(String custId, String dupNo,
			String cntrNo) {
		return this.getJdbc().queryForList("QUOTAPPR.selFor2105",
				new Object[] { custId, dupNo, cntrNo });
	}

	@Override
	public void insertBy2105(String custId, String dupNo, String cntrNo,
			String sdate, BigDecimal unionamt, String updater,
			BigDecimal curAmt, String documentNo, String caseType,
			String permitType, String hideunion, String setDate, String UArea,
			String unionRole, String riskArea) {
		this.getJdbc().update(
				"QUOTAPPR.insertFor2105",
				new Object[] { custId, dupNo, cntrNo, sdate, unionamt, updater,
						curAmt, documentNo, caseType, permitType, hideunion,
						setDate, UArea, unionRole, riskArea });

	}

	@Override
	public List<Map<String, Object>> findBy2105OnlyU(String custId,
			String dupNo, String cntrNo, String sDate) {
		return this.getJdbc().queryForList("QUOTAPPR.selFor2105OnlyU",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	@Override
	public void delBy2105OnlyU(String custId, String dupNo, String cntrNo,
			String sDate) {
		this.getJdbc().update("QUOTAPPR.delFor2105OnlyU",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	@Override
	public List<Map<String, Object>> findBy2105IsExist(String custId,
			String dupNo, String cntrNo, String sDate) {
		return this.getJdbc().queryForList("QUOTAPPR.selFor2105IsExist",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	/**
	 * J-109-0077_05097_B1013 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isSmallBusArr
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findHasOtherIsSmallBus(String custId,
			String dupNo, String cntrNo, String[] isSmallBusArr) {
		String isSmallBusParam = Util.genSqlParam(isSmallBusArr);
		List<Object> params = new ArrayList<Object>();
		params.add(custId);
		params.add(dupNo);
		params.add(cntrNo);
		params.addAll(Arrays.asList(isSmallBusArr));
		return this.getJdbc()
				.queryForAllListByCustParam(
						"MIS.QUOTAPPR.findHasOtherIsSmallBus",
						new String[] { isSmallBusParam },
						params.toArray(new Object[0]));
	}

	/**
	 * J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findLastIsSoleByCustId(String custId,
			String dupNo) {
		return this.getJdbc().queryForMap(
				"mis.quotappr.selectLastIsSoleByCustId",
				new Object[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> findByCustIdCntrNo(String custId,
			String dupNo, String cntrNo) {
		return this.getJdbc().queryForListWithMax(
				"mis.quotappr.selectByCustId",
				new String[] { custId, dupNo, cntrNo });
	}

	@Override
	public int updateSole(String hasRegis, String custId, String dupNo,
			String cntrNo) {
		String updHasRegis = "";
		if (Util.equals(hasRegis, "Y")) {
			updHasRegis = "N";
		} else if (Util.equals(hasRegis, "N")) {
			updHasRegis = "Y";
		}
		return this.getJdbc().update("mis.quotappr.updateSole",
				new Object[] { updHasRegis, custId, dupNo, cntrNo });
	}

	/**
	 * J-110-0182_05097_B1001 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isRescue
	 * @param rescueItem
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findByLastRescue(String custId,
			String dupNo, String cntrNo, String isRescue, String rescueItem) {

		return this.getJdbc().queryForListWithMax(
				"mis.quotappr.selectLastRescue",
				new String[] { custId, dupNo, cntrNo, isRescue, rescueItem });

	}

	/**
	 * // 經濟部
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isRescue
	 * @return
	 */
	@Override
	public Map<String, Object> findByLastIsRescue(String custId, String dupNo,
			String cntrNo, String isRescue) {
		return this.getJdbc().queryForMap("mis.quotappr.selectLastIsRescue",
				new Object[] { custId, dupNo, cntrNo, isRescue });
	}

	/**
	 * J-110-0182_05097_B1001 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
	 * 
	 * @return
	 */
	@Override
	public int updateIsRescue_elByIsRescue() {

		return this.getJdbc().update(
				"mis.quotappr.updateIsRescue_elByIsRescue", new Object[] {});
	}

	/**
	 * J-110-0182_05097_B1001 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
	 * 
	 * @return
	 */
	@Override
	public int updateRescueItem_elByRescueItem() {

		return this.getJdbc().update(
				"mis.quotappr.updateRescueItem_el_By_rescueItem",
				new Object[] {});
	}

	/**
	 * J-110-0209紓困掛件文號新舊案不能相同
	 * 
	 * @param custId
	 * @param dupNo
	 * @param rescueNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findRescueByCsutIdAndRescueNo(
			String custId, String dupNo, String rescueNo) {

		return this.getJdbc().queryForListWithMax(
				"mis.quotappr.selectRescueByCsutIdAndRescueNo",
				new String[] { custId, dupNo, rescueNo });

	}

}
