/* 
 * L120S06B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
//import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 利害關係人授信條件對照表明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S06B", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo","cntrNo","type","itemType"}))
public class L120S06B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 額度明細文件編號<p/>
	 * 101/02/21 Miller新增<br/>
	 *  L140M01A_mainId
	 */
	@Column(name="REFMAINID", length=32, columnDefinition="CHAR(32)")
	private String refMainId;

	/**
	
	/** 
	 * 本案授信戶統編<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * 本案授信戶重複序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)")
	private String dupNo;

	/** 
	 * 本案授信戶額度序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 類別<p/>
	 * 1.本案授信戶<br/>
	 *  2.對照授信戶
	 */
	@Column(name="TYPE", length=1, columnDefinition="CHAR(01)")
	private String type;

	/** 
	 * 項目類別<p/>
	 * 2利(費)率<br/>
	 *  3擔保品<br/>
	 *  4其他敘做條件
	 */
	@Column(name="ITEMTYPE", length=1, columnDefinition="CHAR(01)")
	private String itemType;

	/** 項目說明 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="ITEMDSCR", columnDefinition="CLOB")
	private String itemDscr;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得額度明細文件編號<p/>
	 * 101/02/21 Miller新增<br/>
	 *  L140M01A_mainId
	 */
	public String getRefMainId() {
		return this.refMainId;
	}
	/**
	 *  設定額度明細文件編號<p/>
	 *  101/02/21 Miller新增<br/>
	 *  L140M01A_mainId
	 **/
	public void setRefMainId(String value) {
		this.refMainId = value;
	}
	
	/** 
	 * 取得本案授信戶統編<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定本案授信戶統編<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得本案授信戶重複序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定本案授信戶重複序號<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得本案授信戶額度序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}
	/**
	 *  設定本案授信戶額度序號<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得類別<p/>
	 * 1.本案授信戶<br/>
	 *  2.對照授信戶
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定類別<p/>
	 *  1.本案授信戶<br/>
	 *  2.對照授信戶
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 
	 * 取得項目類別<p/>
	 * 2利(費)率<br/>
	 *  3擔保品<br/>
	 *  4其他敘做條件
	 */
	public String getItemType() {
		return this.itemType;
	}
	/**
	 *  設定項目類別<p/>
	 *  2利(費)率<br/>
	 *  3擔保品<br/>
	 *  4其他敘做條件
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 取得項目說明 **/
	public String getItemDscr() {
		return this.itemDscr;
	}
	/** 設定項目說明 **/
	public void setItemDscr(String value) {
		this.itemDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
