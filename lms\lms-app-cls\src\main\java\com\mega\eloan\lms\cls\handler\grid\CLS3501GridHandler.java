package com.mega.eloan.lms.cls.handler.grid;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.cls.pages.CLS3501M01Page;
import com.mega.eloan.lms.cls.service.CLS3501Service;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;


/**
 * <pre>
 * 中小信保整批申請
 * </pre>
 * 
 * @since 2020/05/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/05/16,EL09301,new
 *          </ul>
 */
@Scope("request")
@Controller("cls3501gridhandler")
public class CLS3501GridHandler extends AbstractGridHandler {

    @Resource
    CodeTypeService codeTypeService;

    @Resource
    CLS3501Service cls3501Service;

    MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
    Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3501M01Page.class);

    @SuppressWarnings("unchecked")
	public CapGridResult queryView(ISearch pageSetting, PageParameters params)
			throws CapException {
        String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
        MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);
        pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
        if (true) { //篩選
            String custId = Util.trim(params.getString("custId"));
            if (Util.isNotEmpty(custId)) {
                pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
            }
            String applyTS_beg = Util.trim(params.getString("applyLoanDay_beg"));
            String applyTS_end = Util.trim(params.getString("applyLoanDay_end"));
            if(Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end) ){
                pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
                        "applyLoanDay", new Object[] { Util.parseDate(applyTS_beg),
                                Util.parseDate(applyTS_end + " 23:59:59") });
            }
        }

        Page<? extends GenericBean> page = cls3501Service.findPage(C124M01A.class, pageSetting);

        Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

        formatter.put("dataStatus", new CodeTypeFormatter(codeTypeService,
                "cls3501_dataStatus", CodeTypeFormatter.ShowTypeEnum.Desc));

        return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);
    }
}
