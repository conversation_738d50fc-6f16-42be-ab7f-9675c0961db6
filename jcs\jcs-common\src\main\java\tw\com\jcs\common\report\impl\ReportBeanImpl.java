package tw.com.jcs.common.report.impl;

import java.util.List;

import com.inet.report.database.beans.BeanDataSourceManager;
import com.inet.report.database.beans.TransferServletException;

/**
 * <pre>
 * ReportBeanImpl
 * </pre>
 * 
 * @since 2022年12月13日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月13日
 *          </ul>
 */
public class ReportBeanImpl implements BeanDataSourceManager {

    private static final String BEAN_REPORTBEAN = "tw.com.jcs.common.report.ReportBean";

    /**
     * 結束時該作的動作
     */
    @Override
    public void destroy() {

    }

    /**
     * 取得全部beanclassnames
     * 
     * @param user
     *            帳號
     * @param password
     *            密碼
     * @return String[] 回傳陣列beanclassnames
     */
    @Override
    public String[] getAllBeanClassNames(String user, String password) throws TransferServletException {
        // return new String[]{BEAN_L120Report};
        return new String[] { BEAN_REPORTBEAN };
    }

    /**
     * 取得beandata
     * 
     * @param beanClassName
     *            beanClassName
     * @param user
     *            帳號
     * @param password
     *            密碼
     * @return List 回傳list的beandata
     */
    @SuppressWarnings("rawtypes")
    @Override
    public List getBeanData(String beanClassName, String user, String password) throws TransferServletException {
        return getBeanData(beanClassName, null, user, password);
    }

    /**
     * 取得beandata
     * 
     * @param beanClassName
     *            beanClassName
     * @param param
     *            參數
     * @param user
     *            帳號
     * @param password
     *            密碼
     * @return List 回傳list的BeanData
     */
    @SuppressWarnings("rawtypes")
    @Override
    public List getBeanData(String beanClassName, String param, String user, String password) throws TransferServletException {
        return getData(user, password);
    }

    /**
     * 取得Data
     * 
     * @param user
     *            帳號
     * @param param
     *            參數
     * @return List 回傳list的Data
     */
    @SuppressWarnings("rawtypes")
    private List getData(String user, String param) {
        return null;
    }

}
