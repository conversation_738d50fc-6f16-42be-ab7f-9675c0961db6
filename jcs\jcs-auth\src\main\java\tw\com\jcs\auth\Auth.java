package tw.com.jcs.auth;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * <h1>權限設定</h1>
 * 
 * <AUTHOR> Software Inc.
 */
@Target(TYPE)
@Retention(RUNTIME)
public @interface Auth {

    /**
     * 權限代碼
     * 
     * @return 各系統設定的權限代碼常數
     */
    int code();

    /**
     * 權限類型
     * 
     * @see AuthType
     * @return AuthType
     */
    int type() default AuthType.None;

    /**
     * 檢驗規則
     * 
     * @see AuthRule
     * @return AuthRule
     */
    int rule() default AuthRule.Any;

}
