/* 
 * L162S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L162S01A;

/** 主從債務人資料表檔 **/
public interface L162S01ADao extends IGenericDao<L162S01A> {

	L162S01A findByOid(String oid);

	List<L162S01A> findByOid(String[] oids);

	List<L162S01A> findByMainId(String mainId);

	/**
	 * 排序方式比照grid<br/>
	 * (客戶統編ASC) + 額度序號 ASC + 相關身分+ASC
	 * 
	 * @param mainId
	 * @return
	 */
	List<L162S01A> findByMainIdOrderByGrid(String mainId);

	L162S01A findByUniqueKey(String mainId, String custId, String dupNo,
			String cntrNo, String rId, String rDupNo);
	
	L162S01A findS01AByCustIdCntrNo(String custId, String dupNo, String cntrNo);

	List<L162S01A> findByIndex01(String mainId, String custId, String dupNo,
			String cntrNo, String rId, String rDupNo);

	List<L162S01A> findByCntrNo(String CntrNo);

	List<L162S01A> findByCustIdDupId(String custId, String DupNo);

	List<L162S01A> findByMainIdCntrNo(String mainId, String cntrNo);

	List<L162S01A> findByOids(String[] oids);
}