$(function() {
	
});
var L120s01rGrid = $("#l120s01rGrid").iGrid({
    handler: 'cls1141gridhandler',
    height: 350,
    postData: {
        formAction: "queryL120s01rList"
    },
    rownumbers: true,
    multiselect : true,
    needPager: false,
    colModel: [{
        colHeader: i18n.clss07a06["L120S01R.grid.custId"],
        align: "left",
        width: 60, // 設定寬度
        sortable: true, // 是否允許排序
        formatter: 'click',
        onclick: loadS01rDoc,
        name: 'custId'
    }, {
        colHeader: i18n.clss07a06["L120S01R.grid.dupNo"],
        align: "left",
        width: 10, // 設定寬度
        name: 'dupNo'
    }, {
        colHeader: i18n.clss07a06["L120S01R.grid.custName"],
        align: "left",
        width: 60, // 設定寬度
        name: 'custName'
    }, {
        colHeader: i18n.clss07a06["L120S01R.grid.result"],
        name: 'result',
        align: 'center',
        width: 30,
        formatter: function(value){
            if(value == "0"){
                return "O";
            }else if(value == "1"){
                return "X";
            }else{
                return ""; //value;
            }
        }
    }, {
        colHeader: "oid",
        name: 'oid',
        hidden: true
    }],
    ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        var data = $("#l120s01rGrid").getRowData(rowid);
        loadS01rDoc(null, null, data);
    }
}).trigger("reloadGrid");

function loadS01rDoc(cellvalue, options, data){
    $.ajax({
        handler: 'cls1141m01formhandler',
        action: "queryL120s01r",
        data: {
            mainId: responseJSON.mainId,
            oid: data.oid
        },
        success: function(data){
        	openS01rDoc(data);
        }
    });
}


/**
 * J-111-0220 選擇相關保證人->確定後產生檢核表主檔
 */
function genL120S01R() {
	// 傳給後端建立主檔
	$.ajax({
		handler : 'cls1141m01formhandler',
		type : "POST",
		dataType : "json",
		action : "genL120s01r",
		data : {
			mainId : responseJSON.mainid
		},
		success : function(obj) {
			L120s01rGrid.trigger("reloadGrid");
		}
	});	
}

function addL120S01R(){
	openS01rDoc();
}

function openS01rDoc(data){
	$("#form06detailDiv").empty();
	// data是空的代表是新增add來的
	var versionDate = "20220706";
	if(data){
		versionDate = DOMPurify.sanitize(data.versionDate);
	}
	$("#form06detailDiv").load("../../cls/clsS07APage06V"+escape(versionDate),function(){
		if(data){
			$("#LMS1205S07Form06").setData(data);
		}
		thickboxOptions.customButton = [i18n.clss07a06["L1205S07Page06.close"]];
		// 消金在非編制中時，不可以去儲存這個表
		// thickboxOptions.customButton = [i18n.clss07a06["L1205S07Page06.saveData"], i18n.clss07a06["L1205S07Page06.close"]];
		$("#form06detailDiv").thickbox({
			title: "",
			width: 1000,
			height: 700,
			modal : true,
			buttons:  API.createJSON([{
				// 儲存
				key: i18n.clss07a06["L1205S07Page06.saveData"],
				value: function(){
					if(!$("#LMS1205S07Form06").valid()){
						return false;
					}
					$.ajax({
						handler: 'cls1141m01formhandler',
						type: "POST",
						action : "saveL120s01r",
						dataType: "json",
						data:{
							mainId: responseJSON.mainId,
							oid: data ? data.oid : '',
							versionDate:versionDate,
							LMS1205S07Form06: JSON.stringify($("#LMS1205S07Form06").serializeData())
						},
						success : function(json) {
							$.thickbox.close();
							API.showMessage(i18n.def['saveSuccess']);
							L120s01rGrid.trigger("reloadGrid");
						}
					});
				}
			}, {
				// 離開
				key: i18n.clss07a06["L1205S07Page06.close"],
				value: function(){
					$.thickbox.close();
				}
			}])
		});
	});
}

function deleteL120S01R(){
	var rows = $("#l120s01rGrid").getGridParam('selarrrow');
	var data = [];
   
	if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
		return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	}
	
	// confirmDelete=是否確定刪除?
	CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
		if (b) {
			for (var i in rows) {
				data.push($("#l120s01rGrid").getRowData(rows[i]).oid);
			}
			
			$.ajax({
				handler: 'cls1141m01formhandler',
				type: "POST",
				action : "deleteL120s01rs",
				data: {
					oids: data
				},
				success: function(obj){
					$("#l120s01rGrid").trigger("reloadGrid");
				}
			});
		}
	});
}
