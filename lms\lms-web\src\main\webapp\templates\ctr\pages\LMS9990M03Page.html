<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			 <script type="text/javascript">loadScript('pagejs/ctr/LMS9990Common');</script>
			  <script type="text/javascript">loadScript('pagejs/ctr/LMS9990M03Page');</script>
			<div class="button-menu funcContainer" id="buttonPanel">	
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}"><!--儲存--></th:block>
				</button>
				<button type="button" id="btnPrint">
					<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}">列印</th:block>
				</button>
				<button id="btnQuery"  class="forview">
                	<span class="ui-icon ui-icon-jcs-102"></span>
					<th:block th:text="#{'button.queryByRate'}"><!--查詢利率--></th:block>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}"><!--離開--></th:block>
				</button>
				
            </div>
			<form id="ActionMForm">
				<div id="showTitle">
					    <div class=" tit2 color-black" >
						<th:block th:text="#{'L999M01AM03.showTitle01'}"><!--種類--></th:block>：<span name="contractType" id="contractType" class="color-blue"></span>
					    <br/>
						<th:block th:text="#{'L999M01AM03.showTitle02'}"><!--客戶名稱--></th:block>：<span name="custData" id="custData" class="color-blue"></span>
					    <br/>
						<th:block th:text="#{'L999M01AM03.showTitle03'}"><!--字      號--></th:block>：
						（<input name="contractWord" id="contractWord" type="text" size="15" maxlength="30"/>）
						<th:block th:text="#{'L999M01AM03.showTitle0301'}"><!--字第 --></th:block>
						<input name="contractNo" id="contractNo" type="text" size="20" class="alphanum" maxlength="20"/>
						<th:block th:text="#{'L999M01AM03.showTitle0302'}"><!--號 --></th:block>
					    <br/>
						<th:block th:text="#{'L999M01AM03.showTitle04'}"><!--主債務人--></th:block>：<span name="custData" id="custData" class="color-blue"></span>
					    <br/>
						<th:block th:text="#{'L999M01AM03.showTitle05'}"><!--保證債務--></th:block>：
						<th:block th:text="#{'L999M01AM03.showTitle05'}"><!--本金 --></th:block>
						<input name="guaAmt" id="guaAmt" type="text" size="19" maxlength="19" integer="15"  class="numeric"/>
						<th:block th:text="#{'L999M01ASCOMMON.unit'}"><!--元 --></th:block>
					    </div>
				</div>
			</form>
			<div class="tabs doc-tabs">
                <ul>
                	<li id="tabs_1" > <a href="#tab-01" goto="01"><b><th:block th:text="#{'L999M01AM03.title01'}"><!--  一般條款--></th:block></b></a></li>
                	<li id="tabs_2" > <a href="#tab-02" goto="02"><b><th:block th:text="#{'L999M01AM03.title02'}"><!--  特別條款--></th:block></b></a></li>
                	<li id="tabs_3" > <a href="#tab-03" goto="03"><b><th:block th:text="#{'L999M01AM03.title03'}"><!--  連保人清單--></th:block></b></a></li>
                </ul>
                <div class="tabCtx-warp">
                	<form id="ActionSForm" name="ActionSForm" >
                		<div id="tabs-00" th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
					</form>
				</div>
			</div>
		</th:block>
    </body>
</html>
