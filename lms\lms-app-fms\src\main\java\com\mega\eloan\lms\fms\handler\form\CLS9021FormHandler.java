/* 
 * CLS9021FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.Set;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.service.CLS9021Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 優惠房貸額度維護 - 總額度維護作業
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
@Scope("request")
@Controller("cls9021formhandler")
public class CLS9021FormHandler extends AbstractFormHandler {

	@Resource
	CLS9021Service service;

	@Resource
	UserInfoService userinfoservice;

	/**
	 * 取得額度資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult obtainAmount(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean canEdit = false;
		Set<String> canEdit_unit = new HashSet<String>();
		if(true){
			//J-111-0290 e-Loan授信系統>建檔維護>優惠房貸額度維護>總額度維護作業，調整為只有 主政單位消金處 可編修，授審處僅顯示。
			//canEdit_unit.add(UtilConstants.BankNo.授管處);
			canEdit_unit.add(UtilConstants.BankNo.消金業務處);
		}
		canEdit = canEdit_unit.contains(user.getUnitNo());
		result.set("canEdit", canEdit);
		
		// 接收AJAX
		String type = Util.trim(params.getString("type"));
		result.set("kindNo", type);

		result.set("totapp",
				NumConverter.addComma(service.findElghtappByKindno(type)));

		String favloan = service.findFavloan(type, "N");
		BigDecimal base = Util.parseBigDecimal("10000");// 萬元
		BigDecimal changeBase = Util.parseBigDecimal(favloan);
		result.set(
				"favloan",
				NumConverter.addComma(changeBase.divide(base).setScale(0,
						RoundingMode.HALF_UP)));

		String rfavloan = service.findRfavloan(type);
		changeBase = Util.parseBigDecimal(rfavloan);
		result.set(
				"rfavloan",
				NumConverter.addComma(changeBase.divide(base).setScale(0,
						RoundingMode.HALF_UP)));
		return result;
	}

	/**
	 * 上傳資料至主機
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveTotapp(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 接收AJAX
		String form = Util.trim(params.getString("resultForm"));
		JSONObject formJson = DataParse.toJSON(form);
		if (service.saveElghtapp(formJson.getString("kindNo"),
				formJson.getString("sumup"))) {
			/**
			 * 2013/08/12 add by EL08034:
			 * 詢問後,授管處 做 優惠房貸額度維護  不用 sendMail
			 */
//			service.sendMail(formJson.getString("kindNo"));
			// show message 儲存成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		} else {
			throw new CapMessageException(RespMsgHelper.getMessage("Error", "",
					"儲存失敗", "請稍後在試一次"), getClass());
		}
		return result;
	}
}
