package com.mega.eloan.lms.fms.flow;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L140MM2ADao;
import com.mega.eloan.lms.dao.L140S02LDao;
import com.mega.eloan.lms.mfaloan.bean.ELF508;
import com.mega.eloan.lms.mfaloan.service.MisELF508Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140MM2A;
import com.mega.eloan.lms.model.L140S02L;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;


@Component
public class CLS2801Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "CLS2801Flow";


	@Resource
	L140S02LDao l140s02lDao;
	
	@Resource
	L140MM2ADao l140mm2aDao;
	
	@Resource
	MisELF508Service misELF508Service;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Transition(node = "開始", value = "起案")
	public void init_flow(FlowInstance instance) {		
		String oid = Util.trim(instance.getId());
		
		L140MM2A meta = l140mm2aDao.findByOid(oid);
		{
			meta.setApprover(null);
			meta.setApproveTime(null);	
		}		
		l140mm2aDao.save(meta);
	}
	
	
	@Transition(node = "確認", value = "核定")
	public void apply(FlowInstance instance) {
		String oid = Util.trim(instance.getId());
		
		L140MM2A meta = l140mm2aDao.findByOid(oid);
		{
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			meta.setApprover(user.getUserId());
			meta.setApproveTime(CapDate.getCurrentTimestamp());	
		}
		l140mm2aDao.save(meta);

		List<ELF508> elf508Data_list = misELF508Service.findByCntrNo(meta.getCntrNo());
		ELF508 elf508Data = elf508Data_list.get(0);
		
		L140S02L l140s02l = l140s02lDao.findByUniqueKey(meta.getMainId(), meta.getSeq());
		
		String elf508_brno = elf508Data.getElf508_brno();	
		String elf508_cust_id = elf508Data.getElf508_cust_id();			//Key
		String elf508_disas_type = elf508Data.getElf508_disas_type();	//Key
		String elf508_hold_no = elf508Data.getElf508_hold_no();			//Key
		String elf508_owner_id = elf508Data.getElf508_owner_id();		//Key
		String elf508_cntrno = elf508Data.getElf508_cntrno();
		String elf508_loan_type = elf508Data.getElf508_loan_type(); 
		Date elf508_app_date = CapDate.parseDate(CapDate.formatDate(l140s02l.getApp_date(), UtilConstants.DateFormat.YYYY_MM_DD));
		BigDecimal elf508_app_amt = elf508Data.getElf508_app_amt();
		String elf508_house_adr = l140s02l.getHouse_adr();
		String elf508_owner_nm = l140s02l.getOwner_nm();
		String elf508_ownsp_id = l140s02l.getOwnsp_id();
		String elf508_ownsp_nm = l140s02l.getOwnsp_nm();
		String elf508_spouse_id = l140s02l.getSpouse_id();
		String elf508_spouse_nm = l140s02l.getSpouse_nm();
		String elf508_coll_ln = l140s02l.getColl_ln();
		String elf508_coll_bn = l140s02l.getColl_bn();
		String elf508_coll_addr = l140s02l.getColl_addr();
		String elf508_set_hold = l140s02l.getSet_hold();
		Date elf508_cancel_date = CapDate.parseDate(CapDate.formatDate(elf508Data.getElf508_cancel_date(), UtilConstants.DateFormat.YYYY_MM_DD));
		Timestamp elf508_eloan_date = meta.getApproveTime();
		Timestamp elf508_aloan_date = (Timestamp)elf508Data.getElf508_aloan_date();

		// Update -> delete insert
		misELF508Service.deleteELF508ByPk(elf508_cust_id, elf508_disas_type, elf508_hold_no, elf508_owner_id);
		misELF508Service.insertELF508(elf508_brno, elf508_cust_id, elf508_disas_type, elf508_hold_no, elf508_owner_id, 
				elf508_cntrno, elf508_loan_type, elf508_app_date, elf508_app_amt, elf508_house_adr, 
				elf508_owner_nm, elf508_ownsp_id, elf508_ownsp_nm, elf508_spouse_id, elf508_spouse_nm, 
				elf508_coll_ln, elf508_coll_bn, elf508_coll_addr, elf508_set_hold, elf508_cancel_date, 
				elf508_eloan_date, elf508_aloan_date);	
	}
	
	
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return L140MM2A.class;
	}


	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return FlowDocStatusEnum.class;
	}
}