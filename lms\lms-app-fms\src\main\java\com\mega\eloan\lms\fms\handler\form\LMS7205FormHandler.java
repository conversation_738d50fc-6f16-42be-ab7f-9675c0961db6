/* 
 * LMS7205FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.fms.service.LMS7205Service;
import com.mega.eloan.lms.model.L720M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 使用者自定表格範本檔
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/9/29,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7205formhandler")
public class LMS7205FormHandler extends AbstractFormHandler {

	@Resource
	LMS7205Service service;
	@Resource
	UserInfoService userSrv;
	
	/**
	 * 檢查(範本名稱有無重複)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult checkL720m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String patternNM = params.getString("patternNM");
		boolean exist = params.getBoolean("exist");
		List<L720M01A> listL720m01a = service.findL720m01aList();
		for (L720M01A model : listL720m01a) {
			if (patternNM.equals(model.getPatternNM())) {
				exist = true;
			}
		}
		result.set("exist", exist);
		return result;
	}// ;

	/**
	 * 查詢
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL720m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("oid");
		L720M01A l720m01a = service.findL720m01aByOid(oid);
		CapAjaxFormResult formL720m01a = DataParse.toResult(l720m01a);
		StringBuilder fullUp = new StringBuilder();
		fullUp.append(getPerName(l720m01a.getUpdater()))
		.append(" (").append(TWNDate.toFullTW(l720m01a.getUpdateTime())).append(")");
		formL720m01a.set("cupdater", fullUp.toString());
		formL720m01a.set("pattern", Util.trim(l720m01a.getPattern()));
		if (l720m01a != null) {
			result.set("L720M01AForm", formL720m01a);
		}
		return result;
	}// ;

	/**
	 * 儲存(含修改)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveL720m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String formL720m01a = params.getString("L720M01AForm");
		String pattern = Util.trim(params.getString("pattern"));
		String oid = params.getString("oid");
		L720M01A l720m01a;
		if(Util.isEmpty(oid)){
			l720m01a = new L720M01A();
			l720m01a.setCreator(user.getUserId());
			l720m01a.setCreateTime(CapDate.getCurrentTimestamp());			
		}else{
			l720m01a = service.findL720m01aByOid(oid);
			if (l720m01a == null) {
				l720m01a = new L720M01A();
				l720m01a.setCreator(user.getUserId());
				l720m01a.setCreateTime(CapDate.getCurrentTimestamp());
			}
		}
		DataParse.toBean(formL720m01a, l720m01a);
		l720m01a.setPattern(pattern);
		service.save(l720m01a);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage("EFD0017"));
		}
		return result;
	}// ;

	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteL720m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		service.deleteListL720m01a(oidArray);
		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
				RespMsgHelper.getMainMessage("EFD0019"));
		return result;
	}// ;
	
	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * @param id 使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id){
		return (!Util.isEmpty(userSrv.getUserName(id))
		 ? userSrv.getUserName(id): id);
	}
	
	/**
	 * 新增時取得最後異動人員(含時間)
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getUpdater(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		StringBuilder fullUp = new StringBuilder();
		fullUp.append(getPerName(user.getUserId()))
		.append(" (").append(TWNDate.toFullTW(CapDate.getCurrentTimestamp())).append(")");
		result.set("cupdater", fullUp.toString());
		result.set("updater", user.getUserId());
		return result;
	}// ;
}
