/* 
 * OdsdbBASEService.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2011 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.ods.service;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

public interface OdsdbBASEService {

    public Map<String, Object> getODS_Status();
    public List<Map<String, Object>> findODS_0320_ById(String custId, String dupNo);
    public Page<Map<String, Object>> findODS_0320_ById_forPage(ISearch search, String custId, String dupNo);
    public List<Map<String, Object>> findODS_0060_TXN(String realActNo);
    public List<Map<String, Object>> findODS_0060_HIST(String realActNo, String currCode, String bgnDate, String endDate);
    public List<Map<String, Object>> findODS_8250(String brNo, String ioFlag, String func, String remitType, String bgnDate, String endDate,
            String custId, String dupNo, String begAmt, String endAmt, String bankId, String ractNo);
    public List<Map<String, Object>> findODS_8410_ByAccNo(String loanNo, String brNo);
    public List<Map<String, Object>> findODS_CMSTKTBL();
    public Map<String, Object> findODS_CMSTKTBL_SINGLE(String CMSTK_CODE);
    public List<Map<String, Object>> findODS_CMMEMTBN();
    public Map<String, Object> findODS_CMFWARNP(String idNo,String date);
    public Map<String, Object> findODS_CMFAUDAC_ByAcc(String brNo,String acc);
}
