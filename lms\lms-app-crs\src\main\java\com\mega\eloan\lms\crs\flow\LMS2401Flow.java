package com.mega.eloan.lms.crs.flow;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.dao.C241A01ADao;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241A01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Component
public class LMS2401Flow extends AbstractFlowHandler{
			
	@Resource
	RetrialService retrialService;
	
	@Resource
	C240M01ADao c240m01aDao;
	
	@Resource
	C241A01ADao c241a01aDao;
	
	@Transition(node = "區中心_編製中", value = "呈主管")
	public void send(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C240M01A c240m01a = retrialService.findC240M01A_oid(Util.trim(instance.getId()));
		c240m01a.setHqAppraiserId(user.getUserId());
		c240m01aDao.save(c240m01a);
	}
	
	@Transition(node = "確認", value = "核定")
	public void apply(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C240M01A c240m01a = retrialService.findC240M01A_oid(Util.trim(instance.getId()));
		c240m01a.setApprover(user.getUserId());
		c240m01a.setApproveTime(CapDate.getCurrentTimestamp());
		c240m01a.setReManagerId(user.getUserId());
		c240m01a.setReCheckTime(CapDate.getCurrentTimestamp());
		c240m01aDao.save(c240m01a);
	}
	
	@Transition(node = "海外_已核准", value = "傳送")
	public void sendToBranch(FlowInstance instance) {
		C240M01A c240m01a = retrialService.findC240M01A_oid(Util.trim(instance.getId()));
		try{
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();	
						
			List<C241A01A> c241a01as = retrialService.auth_C241A01A_ToBr(c240m01a.getOwnBrId()
					, user.getUserId() , retrialService.findC241M01A_C240M01A(c240m01a.getMainId()));
			
			if(CollectionUtils.isNotEmpty(c241a01as)){
				c241a01aDao.save(c241a01as);	
			}
			//傳送 Btt
			retrialService.sendBtt(c240m01a);
		}catch(Exception e){
			
		}
	}
			
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C240M01A.class;
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}
