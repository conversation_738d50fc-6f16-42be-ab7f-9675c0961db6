/* 
 * C140S04CDaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C140S04CDao;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C140S04C;

/**
 * <pre>
 * 徵信調查報告書第四章 建物
 * </pre>
 * 
 * @since 2011/9/23
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140S04CDaoImpl extends LMSJpaDao<C140S04C, String> implements
		C140S04CDao {

	@Override
	public int deleteByMeta(C140M04A meta) {
		Query query = entityManager
				.createNamedQuery("ces140s04c.deleteByMainIdAndPid");

		query.setParameter("mainId", meta.getMainId());
		query.setParameter("pid", meta.getUid());
		return query.executeUpdate();
	}
}// ;
