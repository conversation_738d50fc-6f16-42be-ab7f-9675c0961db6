var l120s24aGrid;
//LMS1401S10PageV20250101、LMS1401S10PageV20220812.js，L140S10Page物件，移到這邊定義，html就不用先require這2隻JS
var L140S10Page = {
		initL120s24aPage : function(){
			// H.本額度有提供不動產擔保品會影響的欄位都先隱藏，等user選了"是"or"否"再show/hide
			$("#l120S24ADetailForm").find(".hasEstate_Y").hide();
			// P.本額度是否有合格金融擔保品，合格金融擔保品按鈕+grid區塊
			// $("#l120S24ADetailForm").find("#hasQuaColl_div").hide();
			// 四、保證資訊整個區塊先隱藏
			// $("#l120S24ADetailForm").find("#gutArea4Div").hide();
			// R-1是否有下列類型保證，四、保證資訊下的欄位都先隱藏
			$("#l120S24ADetailForm").find(".hasGut_Y").hide();
			// 保證類別_其他說明欄位先隱藏
			$("#l120S24ADetailForm").find("#gutClass_other").hide();
			// V.有徵提評等較佳之連帶保證人，連帶保證人下的欄位都先隱藏
			$("#l120S24ADetailForm").find(".hasGuar_Y").hide();

			// 因為codetype好像沒有讓選項是value - key的方式，只好抄別隻的塞法，要在setData前做好
			var obj = CommonAPI.loadCombos(["CountryCode", "Common_Currcy"]);
			//國別
			$(".country_s24a").setItems({
				item: obj.CountryCode,
				format: "{value} - {key}"
			});
			//幣別
			$(".money_s24a").setItems({
				item: obj.Common_Currcy,
				format: "{value} - {key}"
			});
			// 從js塞，因為key是數字的情況如果用combotype設定，整個順序會被打亂
			//UPGRADETODO 補入下拉選單item
			var itema = API.loadOrderCombosAsList("gutClass_s24a")["gutClass_s24a"];
			$("#gutClass_s24a").setItems({ size: "1", item: convertItems(itema), clear: true});
			var itemas = CommonAPI.loadCombos(["bowrrowerClass_s24a", "ccf_s24a", "LTVClass_s24a_Y", "LTVClass_s24a_N"
				, "isFarmWood_s24a", "quaColl_s24b"]);
			$("#bowrrowerClass_s24a").setItems({ size: "1", item: itemas.bowrrowerClass_s24a, clear: true});
			$("#ccf_s24a").setItems({ size: "1", item: itemas.ccf_s24a, clear: true});
			$("#LTVClass_s24a_Y").setItems({ size: "1", item: itemas.LTVClass_s24a_Y, clear: true});
			$("#LTVClass_s24a_N").setItems({ size: "1", item: itemas.LTVClass_s24a_N, clear: true});
			$("#isFarmWood_s24a").setItems({ size: "1", item: itemas.isFarmWood_s24a, clear: true});
			$("#quaColl_s24b").setItems({ size: "1", item: itemas.quaColl_s24b, clear: true});
		},
		triggerSelect : function(){
			$("#l120S24ADetailForm").find('#bowrrowerClass_s24a').trigger('change');
			// 讓是否有ccf、ccf值、現請額度都放好後，才進行change再重算一次$calCcfAmt_s24a
			$("#l120S24ADetailForm").find('#ccf_s24a').trigger('change');
			
			// 為了在所有值都寫入後去觸發area2ShowHide()
			// 雖然最早的setData的時候會不斷的觸發area2ShowHide()
			// 但是那個時候的各欄位的值都有點不太齊全
			$("#l120S24ADetailForm").find('#LTVClass_s24a_N').trigger('change');
			
			$("#l120S24ADetailForm").find('#gutClass_s24a').trigger('change');
		},
		setHTMLData : function(data){
			// 畫面顯示的部分，會用到data裡來判斷
			// 連帶保證人風險權數旁邊的"計算"是否要出現，算是補救用的button
			if(data.l120s24a_show_guarrw_cal && data.l120s24a_show_guarrw_cal == 'Y'){
				$("#l120S24ADetailForm").find("#guarRW_s24a_cal_div").show();
			}

			if(data.lms_rw_show_special_branch_div){
				if(jQuery.inArray(userInfo.ssoUnitNo, data.lms_rw_show_special_branch_div) !== -1){
					$("#l120S24ADetailForm").find("#special_branch_s24a_div").show();
				}	
			}
			
			// 補上畫面幾處要塞幣別的地方
			if(data.currentApplyCurr_s24a){
				$("#l120S24ADetailForm").find('.currentApplyCurr_s24a_unit').val(data.currentApplyCurr_s24a);
				
				// 如果原幣別不為TWD則要多出現折合台幣的tr  還需要嗎??
				var currentApplyAmt_TWDArea = $("#l120S24ADetailForm").find('#currentApplyAmt_TWDArea');
				if(data.currentApplyCurr_s24a != 'TWD'){
					currentApplyAmt_TWDArea.show();
				}else{
					currentApplyAmt_TWDArea.hide();
				}
			}
			
			// 這兩個說明欄位特別要用.html來塞，因為裡面有斷行的一些排版 
			if(data.l120s24a_bowrrowerClassTop){
				$("#l120s24a_bowrrowerClassTop").html(DOMPurify.sanitize(data.l120s24a_bowrrowerClassTop));
			}
			if(data.l120s24a_bowrrowerClassBottom){
				$("#l120s24a_bowrrowerClassBottom").html(DOMPurify.sanitize(data.l120s24a_bowrrowerClassBottom));
			}
			
			// 補資料的部分，怕有些setData完之後，因為trigger change事件而導致畫面欄位被清掉
			if(data.ccfAmt_s24a){
				$("#l120S24ADetailForm").find('#ccfAmt_s24a').val(data.ccfAmt_s24a);
			}
			// .clear1會清的欄位
			if(data.beforeDeductRW_s24a){
				$("#l120S24ADetailForm").find('#beforeDeductRW_s24a').val(data.beforeDeductRW_s24a);
			}
			// .clear2會清的欄位
			if(data.gutDeptName_s24a){
				$("#l120S24ADetailForm").find("#gutDeptName_s24a").val(data.gutDeptName_s24a);
			}
			if(data.gutDeptRW_s24a){
				$("#l120S24ADetailForm").find("#gutDeptRW_s24a").val(data.gutDeptRW_s24a);
			}
			// .clear3會清的欄位
			if(data.guarName_s24a){
				$("#l120S24ADetailForm").find("#guarName_s24a").val(data.guarName_s24a);
			}
			if(data.guarCrdGrade_s24a){
				$("#l120S24ADetailForm").find("#guarCrdGrade_s24a").val(data.guarCrdGrade_s24a);
			}
			if(data.guarCrdText_s24a){
				$("#l120S24ADetailForm").find("#guarCrdText_s24a").html(data.guarCrdText_s24a);
			}
			if(data.guarRW_s24a){
				$("#l120S24ADetailForm").find("#guarRW_s24a").val(data.guarRW_s24a);
			}
			
			// .clear0 .clear1、.clear2、.clear3會清的欄位-計算區塊的所有欄位
			if(data.calCurrentApplyAmt_s24a){
				$("#l120S24ADetailForm").find("#calCurrentApplyAmt_s24a").val(data.calCurrentApplyAmt_s24a);
			}
			if(data.calCcf_s24a){
				$("#l120S24ADetailForm").find("#calCcf_s24a").val(data.calCcf_s24a);
			}
			if(data.calCcfAmt_s24a){
				$("#l120S24ADetailForm").find("#calCcfAmt_s24a").val(data.calCcfAmt_s24a);
			}
			if(data.calBeforeDeductRW_s24a){
				$("#l120S24ADetailForm").find("#calBeforeDeductRW_s24a").val(data.calBeforeDeductRW_s24a);
			}
			if(data.calDisCollAmt_s24a){
				$("#l120S24ADetailForm").find("#calDisCollAmt_s24a").val(data.calDisCollAmt_s24a);
			}
			if(data.calDisCollExposureAmt_s24a){
				$("#l120S24ADetailForm").find("#calDisCollExposureAmt_s24a").val(data.calDisCollExposureAmt_s24a);
			}
			if(data.calGutDeptRW_s24a){
				$("#l120S24ADetailForm").find("#calGutDeptRW_s24a").val(data.calGutDeptRW_s24a);
			}
			if(data.calGutPercent_s24a){
				$("#l120S24ADetailForm").find("#calGutPercent_s24a").val(data.calGutPercent_s24a);
			}
			if(data.calHasGutPartAmt_s24a){
				$("#l120S24ADetailForm").find("#calHasGutPartAmt_s24a").val(data.calHasGutPartAmt_s24a);
			}
			if(data.calHasGutDeptRWA_s24a){
				$("#l120S24ADetailForm").find("#calHasGutDeptRWA_s24a").val(data.calHasGutDeptRWA_s24a);
			}
			if(data.calGuarRW_s24a){
				$("#l120S24ADetailForm").find("#calGuarRW_s24a").val(data.calGuarRW_s24a);
			}
			if(data.calNoGutPartAmt_s24a){
				$("#l120S24ADetailForm").find("#calNoGutPartAmt_s24a").val(data.calNoGutPartAmt_s24a);
			}
			if(data.calNoGutPartRW_s24a){
				$("#l120S24ADetailForm").find("#calNoGutPartRW_s24a").val(data.calNoGutPartRW_s24a);
			}
			if(data.calNoGutRWA_s24a){
				$("#l120S24ADetailForm").find("#calNoGutRWA_s24a").val(data.calNoGutRWA_s24a);
			}
			if(data.calDeductRWA_s24a){
				$("#l120S24ADetailForm").find("#calDeductRWA_s24a").val(data.calDeductRWA_s24a);
			}
			if(data.calDeductRW_s24a){
				$("#l120S24ADetailForm").find("#calDeductRW_s24a").val(data.calDeductRW_s24a);
			}
		},
		readOnlyL120s24aPage : function(data){
			if (checkReadonly_s24a() || thickboxOptions.readOnly) {
				// 畫面唯讀
		        $("#l120S24ADetailForm").readOnlyChilds(true);
		        $("#l120S24BDetailForm").readOnlyChilds(true);
		        // 按鈕隱藏
		        $("#l120S24ADetailForm").find("button:not(.forview)").hide();
		    }
		}
}

pageJsInit(function() {
$(function() {
	lms1401s10PanelInit();

initDfd.done(function(auth) {
	if (checkReadonly_s24a() || thickboxOptions.readOnly) {
        $("#LMS1401S10Form01").find("button:not(.forview)").hide();
    }
});
});
});

function lms1401s10PanelInit(){
	l120s24aGrid = $("#l120s24aGrid").iGrid({
		handler: 'lms1201gridhandler',
		height: 350,
		postData: {
			formAction: "queryL120s24aList"
		},
		rownumbers: true,
		multiselect : true,
		needPager: false,
		colModel: [{
			colHeader: i18n.lms1401s10["L120S24A.grid.cntrNo_s24a"],// 額度序號
			align: "center",
			width: 45, // 設定寬度
			name: 'cntrNo_s24a',
			formatter: 'click',
			onclick: loadS24ADoc
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.currentApplyCurr_s24a"],// 幣別
			align: "center",
			width: 20, // 設定寬度
			name: 'currentApplyCurr_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.currentApplyAmt_s24a"],// 現請額度(A)
			width: 25, // 設定寬度
			name: 'currentApplyAmt_s24a',
			sortable: false,
			align:"right"
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.ccf_s24a"],// CCF
			align: "left",
			width: 15, // 設定寬度
			name: 'ccf_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.ccfAmt_s24a"],// 純表外之信用相當額(B)=(A)xCCF
			align: "right",
			width: 25, // 設定寬度
			name: 'ccfAmt_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.specialFinRiskType_s24a"],// 特殊融資
			align: "center",
			width: 10, // 設定寬度
			name: 'specialFinRiskType_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.hasEstate_s24a"],// LTV法
			align: "center",
			width: 10, // 設定寬度
			name: 'hasEstate_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.isCBControl_s24a"],// 央行管制
			align: "center",
			width: 10, // 設定寬度
			name: 'isCBControl_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.beforeDeductRW_s24a"],// 抵減前風險權數
			align: "right",
			width: 15, // 設定寬度
			name: 'beforeDeductRW_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.totalDisCollAmt_s24a"],// 合格擔保品抵減金額(C)
			align: "right",
			width: 25, // 設定寬度
			name: 'totalDisCollAmt_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.calDisCollExposureAmt_s24a"],// 合格擔保品抵減後暴險額(D)=(A)-(C) 或 (B)-(C)
			align: "right",
			width: 28, // 設定寬度
			name: 'calDisCollExposureAmt_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.hasGutClass_s24a"],// 信保機構
			align: "center",
			width: 7, // 設定寬度
			name: 'hasGutClass_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.calHasGutDeptRWA_s24a"],// 信保部位風險性資產(E)
			align: "right",
			width: 25, // 設定寬度
			name: 'calHasGutDeptRWA_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.calNoGutRWA_s24a"],// 非信保部位風性資產(F)
			align: "right",
			width: 25, // 設定寬度
			name: 'calNoGutRWA_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.calDeductRWA_s24a"],// 抵減後風險性資產(G)=(E)+(F)
			align: "right",
			width: 25, // 設定寬度
			name: 'calDeductRWA_s24a'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.calDeductRW_s24a"],// 抵減後風險權數=(G)/(A)
			align: "right",
			width: 25, // 設定寬度
			name: 'calDeductRW_s24a'
		}, {
			colHeader: "oid",
			name: 'oid',
			hidden: true
		}, {
			colHeader: "versionDate_s24a",
			name: 'versionDate_s24a',
			hidden: true
		}, {
			// 當前最新的版本
			colHeader: "newestVersionDate",
			name: 'newestVersionDate',
			hidden: true
		}],
		ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = $("#l120s24aGrid").getRowData(rowid);
			loadS24ADoc(null, null, data);
		},
		loadComplete : function() {
			// 預設顯示新版本欄位
			var gridNowVersion = '20250101';
			var newestVersionDate = '20250101';
			// 從第一筆來判斷versionDate_s24a
			var row1Data = $("#l120s24aGrid").getRowData(1);
			if(row1Data != undefined && row1Data != null){
				// 第一筆有內容才判斷
				if(Object.keys(row1Data).length > 0){
					gridNowVersion = row1Data.versionDate_s24a;
					newestVersionDate = row1Data.newestVersionDate;
				}
			}
			// 這樣判斷是因為每次reload的時候一直show/hide會造成grid越長越胖
			if(gridNowVersion == '20250101'){
				$("#l120s24aGrid").showCol('specialFinRiskType_s24a');
				$("#L120S24AHint1Span").hide();
			}else if(gridNowVersion == '20220812'){
				// 如果版本是20220812，就不要顯示這個新欄位
				$("#l120s24aGrid").hideCol('specialFinRiskType_s24a');
				
				// 另外在畫面上出現紅字
				if(responseJSON.mainDocStatus =="01O" || responseJSON.mainDocStatus =="07O"  || 
						responseJSON.mainDocStatus =="01K" || responseJSON.mainDocStatus == "0EO" ){
					if(gridNowVersion != newestVersionDate){
						// 是在經辦可編輯的情況下，再顯示建議他換新版的字眼
						$("#L120S24AHint1Span").show();
					}else{
						$("#L120S24AHint1Span").hide();
					}
			    }
			}
			// 如果L120S24A沒資料，就什麼都不做
		}
		
	}).trigger("reloadGrid");
}


function loadS24ADoc(cellvalue, options, data){
	$.ajax({
		handler: 'lms1401s10formhandler',
		action: "queryL120s24a",
		data: {
			mainId: responseJSON.mainId,
			oid: data.oid
		}
	}).done(function(data){
		openS24ADoc(data);
	});
}

function openS24ADoc(data){
	$("#l120S24ADetailThickbox").empty();
	// 放在load之前才不會造成lms1401S10PageVXXXXXX讀不到
	// 將L120S24A.oid存進responseJSON讓後面存L120S24B.refOid_s24b時可以用
	responseJSON.openL120s24aOid = data.oid;
	$("#l120S24ADetailThickbox").load("../../lms/lms1401S10PageV"+escape(DOMPurify.sanitize(data.versionDate_s24a)),function(){
		//UPGRADETODO 再執行一次pageInit，numeric檢核欄位才會生效
		pageInit.call($("#l120S24ADetailThickbox"), true);
		setTimeout(function() {
			if(data){
				L140S10Page.initL120s24aPage();// 先init初始化畫面
				$("#l120S24ADetailForm").setData(data);
				L140S10Page.triggerSelect();// select選項得自己觸發change
				L140S10Page.setHTMLData(data);// 等setData完才可以補一些畫面的值
				L140S10Page.readOnlyL120s24aPage();// 如果是整個畫面要readOnly，要等上面畫面都畫完在跑
			}
		}, 500);
		
		//UPGRADETODO TEST 不懂為何require(['validate']要註解才會生效...
		jQuery.validator.injectFieldVal();
		$(this).find('form').each(function() {
			$(this).validate({
				ignore: ".ck"
			});
		});
		
		// 這個超重要喔，沒有這個唯讀的時候會無法關閉thickbox
		thickboxOptions.customButton = [i18n.lms1401s10["LMS1401S10Form01.close"]];
		$("#l120S24ADetailThickbox").thickbox({
			title: i18n.lms1401s10["L120S24A.head1"] + ' ' + data.cntrNo_s24a + 
				' ' + i18n.lms1401s10["L120S24A.head2"],// 額度序號046111100082風險權數試算
			width: 900,
			height: 800,
			modal : true,
			buttons:  API.createJSON([{
				// 儲存
				key: i18n.lms1401s10["LMS1401S10Form01.saveData"],
				value: function(){
					if(!$("#l120S24ADetailForm").valid()){
						return false;
					}
					$.ajax({
						handler: 'lms1401s10formhandler',
						type: "POST",
						action : "saveL120s24a",
						dataType: "json",
						data:{
							mainId: responseJSON.mainId,
							oid: data.oid,
							l120S24ADetailForm: JSON.stringify($("#l120S24ADetailForm").serializeData())
						}
					}).done(function(json){
						$.thickbox.close();
						API.showMessage(i18n.def['saveSuccess'] + "<BR/>" + json.hint1);
						l120s24aGrid.trigger("reloadGrid");
					});
				}
			}, {
				// 離開
				key: i18n.lms1401s10["LMS1401S10Form01.close"],
				value: function(){
					$.thickbox.close();
					l120s24aGrid.trigger("reloadGrid");
				}
			}])
		});
	});
}

/**
 * 產生風險權數主檔
 */
function genL120S24A() {
	// 傳給後端建立主檔
	$.ajax({
		handler : 'lms1401s10formhandler',
		type : "POST",
		dataType : "json",
		action : "genL120s24a",
		data : {
			mainId : responseJSON.mainid
		}
	}).done(function(obj){
		l120s24aGrid.trigger("reloadGrid");
	});	
}

/**
 * 寫回額度明細表
 */
function writeBackToL140m01a_s24a(){
	var rows = $("#l120s24aGrid").getGridParam('selarrrow');
	var data = [];
	
	if (rows == "") {// button.L120S24A.writeNonSelect=請先選擇需寫回之資料列
		return CommonAPI.showMessage(i18n.lms1401s10["button.L120S24A.writeNonSelect"]);
	}
	
	// button.L120S24A.writeConfirm=是否要寫回額度明細表風險權數欄位?
	CommonAPI.confirmMessage(i18n.lms1401s10["button.L120S24A.writeConfirm"], function(b){
		if (b) {
			for (var i in rows) {
				data.push($("#l120s24aGrid").getRowData(rows[i]).oid);
			}
			
			$.ajax({
				handler: 'lms1401s10formhandler',
				type: "POST",
				action : "writeBackToL140m01a",
				data: {
					mainId: responseJSON.mainId,
					oids: data
				}
			}).done(function(obj){
				$("#l120s24aGrid").trigger("reloadGrid");
			});
		}
	});
}

/**
 * 列印此頁
 */
function printPage_s24a(){
	$.form.submit({
		url: "../../simple/FileProcessingService",
		target: "_blank",
		data: {
			rptOid: "R47_simple" + "^^^^^",
			mainId: responseJSON.mainId,
			fileDownloadName: "lms1201r47_simple.pdf",
			serviceName: "lms1201r01rptservice"
		}
	});
}

/**
 * 刪除
 * @returns
 */
function delete_s24a(){
	var rows = $("#l120s24aGrid").getGridParam('selarrrow');
	var data = [];
	
	if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
		return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	}
	
	// confirmDelete=是否確定刪除?
	CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
		if (b) {
			for (var i in rows) {
				data.push($("#l120s24aGrid").getRowData(rows[i]).oid);
			}
			
			$.ajax({
				handler: 'lms1401s10formhandler',
				type: "POST",
				action : "deleteL120s24as",
				data: {
					oids: data
				}
			}).done(function(obj){
				$("#l120s24aGrid").trigger("reloadGrid");
			});
		}
	});
}

/**
 * 列印試算明細
 */
function printL120S24A_s24a(){
	$.form.submit({
		url: "../../simple/FileProcessingService",
		target: "_blank",
		data: {
			rptOid: "R47" + "^^^^^",
			mainId: responseJSON.mainId,
			fileDownloadName: "lms1201r47_simple.pdf",
			serviceName: "lms1201r01rptservice"
		}
	});
}


function checkReadonly_s24a(){
	var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || _openerLockDoc == "1") {
        return true;
    }
    return false;
}
