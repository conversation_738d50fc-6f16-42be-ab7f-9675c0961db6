/* 
 *MisELF502Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;


/**<pre>
 * 停權明細檔MIS.ELF511
 * </pre>
 * @since  2013/1/22
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/1/22,<PERSON>,new
 *          </ul>
 */
public interface MisELF511Service {

	/**
	 * 依照統一編號查詢停權明細
	 * @param custId 統一編號
	 * @return
	 */
	public List<Map<String, Object>> selStop1(String custId);
	
	/**
	 * 依照分行代碼查詢停權明細
	 * @param brno 分行代碼
	 * @return
	 */
	public List<Map<String, Object>> selStop2(String brno);
	
	/**
	 * 依照分行代碼與統一編號查詢停權明細
	 * @param custId 統一編號(含重覆序號)
	 * @param brno 分行代碼
	 * @return
	 */
	public List<Map<String, Object>> selStop3(String custId, String brno);
}
