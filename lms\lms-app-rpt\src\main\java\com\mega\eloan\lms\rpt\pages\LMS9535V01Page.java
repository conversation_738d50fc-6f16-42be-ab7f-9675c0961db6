package com.mega.eloan.lms.rpt.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.rpt.panels.LMSS07APanel;

@Controller
@RequestMapping(path = "/rpt/lms9535v01")
public class LMS9535V01Page extends AbstractEloanInnerView {

	public LMS9535V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		
		// UPGRADE: 後續沒有用到就可以刪掉
		//add(new SimpleButtonPanel("_buttonPanel", null, new ArrayList<Object>()));

		renderJsI18N(LMS9535V01Page.class);
		renderJsI18N(LMSS07APanel.class);
		renderJsI18N(LMSCommomPage.class);
		renderRespMsgJsI18N(UtilConstants.AJAX_RSP_MSG.ERR_MSG);
		model.addAttribute("loadScript", "loadScript('pagejs/rpt/LMS9535V01Page');");
	}
}
