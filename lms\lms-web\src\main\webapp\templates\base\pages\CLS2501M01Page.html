<html xmlns="http://www.w3.org/1999/xhtml"  xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<script>
				loadScript('pagejs/base/CLS2501M01Page');
			</script>
			<!--<script type="text/javascript" src="pagejs/base/LMSL140M01MPanel.js"></script>-->
			
			<div class="button-menu funcContainer" id="buttonPanel">
				
				<!--編製中 -->
				<th:block th:if="${_btnDOC_EDITING_visible}">
	        		<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04"></span>
	        			<th:block th:text="#{'button.save'}"><!--儲存--></th:block>
	        		</button>
					<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02"></span>
	        			<th:block th:text="#{'button.send'}" ><!--呈主管覆核--></th:block>
	        		</button>
		        </th:block>		
				
				<!--待覆核 -->
				<th:block th:if="${_btnWAIT_APPROVE_visible}">
	        		<button id="btnCheck" >
	        			<span class="ui-icon ui-icon-jcs-106"></span>
	        			<th:block th:text="#{'button.check'}" ><!--覆核--></th:block>
	        		</button>
		        </th:block>				
		        
                <button id="btnPrint" class="forview">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}"><!--列印--></th:block>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}"><!--離開--></th:block>
				</button>
				
            </div>
			<div class="tit2 color-black">
				<th:block th:text="#{'C250M01A.title01'}"><!-- 可疑代辦案件註記維護--></th:block>：
				(<span id="showTypCd" class="text-red"></span>)<span id="showCustId" class="color-blue"></span>
			</div>
			
			<div class="tit2 color-black">
				<th:block th:text="#{'C250M01A.cntrNo'}"><!-- 額度序號--></th:block>：
				<span id="cntrNo" class="color-blue"></span>
			</div>	
						
			
			<div id="openCheckBox" style="display:none"> 
				<div>
				 <span id="check1" style="display:none">
				 	<label><input name="checkRadio" type="radio" value="3"><th:block th:text="#{'C250M01E.bt12'}"><!--  核准--></th:block></label><br/>
					<label><input name="checkRadio" type="radio" value="1"><th:block th:text="#{'C250M01E.bt11'}"><!--  退回經辦修改--></th:block></label>
				</span>
				</div>
			</div>
			
			<div id="openChecDatekBox" style="display:none"> 
				<div>
					<input id="forCheckDate" type="text" size="10" maxlength="10" class="date">	
				</div>
			</div>
			
			<form id="CLS2501M01Form">
			<fieldset>
                    <legend>
                        <b><th:block th:text="#{'C250M01A.title01'}"><!-- 可疑代辦案件註記維護 --></th:block></b>
                    </legend>
					
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tbody>
							<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.ownBrId'}">分行名稱</th:block>&nbsp;&nbsp;</td>
							<td >
								<span id="ownBrId" class="field"></span>&nbsp;<span id="ownBrIdName" class="field"></span>
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.docStatus'}">目前文件狀態</th:block>&nbsp;&nbsp;</td>
							<td><b class="text-red"><span id="docStatus"></span>&nbsp;</b></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.custId'}">統一編號</th:block>&nbsp;&nbsp;</td>
							<td><span id="custId" class="field"></span>&nbsp;<span id="dupNo" class="field"></span></td>
							<td class="hd2" align="right"></td>
							<td></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.yyyymm'}">資料年月</th:block>&nbsp;&nbsp;</td>
							<td><span id="yyyymm"></span></td>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.status'}">性質</th:block>&nbsp;&nbsp;</td>
							<td><span id="status"></span></td>
						</tr>						
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.loanNo'}">帳號</th:block>&nbsp;&nbsp;</td>
							<td>
								<span id="loanNo" class="field"></span>
							</td>
							<td class="hd2" align="right">聯徵B29查詢資料&nbsp;&nbsp;</td>
							<td>
								<a href="#" id="showB29Ejcic" class="readOnly">開啟</a>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.LNFLAG'}">疑似代辦案件訊息</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" >
								<input type="radio" id="lnflag" name="lnflag" value="1" itemType="C250M01A_lnFlag" itemSize="1" itemFormat="{key}" class="required" />
								<!--<input type="textarea" name="othermemo" id="othermemo" rows="10" cols="30" size="100" maxlength="300" maxlengthC="100" class="required" style="display:none"/>-->
								<textarea cols="100" rows="3" id="othermemo" name="othermemo" maxlength="300" maxlengthC="100" class="required" style="display:none"></textarea>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.otherDesc'}">其他情形</th:block>&nbsp;&nbsp;</td>
							<td colspan="3" >
								<span id="otherDesc" class="field"></span>
							</td>
						</tr>	
						</tbody>
					</table>
					<table id="BranchCommTable" class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0" style="display:none">
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C250M01A.BranchComm'}">查證結果</th:block>&nbsp;&nbsp;</td>							
							<td>
								<b class="text-red">
								<th:block th:text="#{'C250M01A.MEMO04'}">※疑似代辦案件訊息非屬　{經查並無仲介代辦情況出現}　時，需登錄查證結果。</th:block><br/>
								</b>
								<textarea cols="100" rows="3" id="branchComm" name="branchComm" maxlength="300" maxlengthC="100" class="required"></textarea>
							</td>
						</tr>	
					</table>	
					<b class="text-red">	
						<th:block th:text="#{'C250M01A.MEMO01'}">1.查核目標範圍為個人無擔保放款，排除員工消貸、同一機關團體消貸、移送信保保證案件(例如：教育部留貸、青年創業及啟動金貸款)</th:block>
						<br/>
						<th:block th:text="#{'C250M01A.MEMO02'}">2.營業單位應就上述查核範圍內之新做或增額案件，於撥款日後3個月內，查詢聯徵是否有其他金融機構於短期內接續撥款，並於E-loan系統註記是否有疑似代辦案件之情形。</th:block>
						<br/>
						<th:block th:text="#{'C250M01A.MEMO03'}">3.營業單位應於每月10日前自行列印本報表，由授信主管檢視是否仍有撥款日後超過3個月以上尚未註記之案件，並儘速完成註記。</th:block>
					</b>
					<br/>
			</fieldset>		
			<fieldset>
                    <legend>
                        <b><th:block th:text="#{'C250M01A.overduePaymentRecord'}"><!--滯延繳款紀錄--></th:block></b>
                    </legend>
					<div id="overdueRecordGrid"></div>
			</fieldset>
			    <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}"><!-- 文件異動紀錄 --></th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer"><!-- 文件異動紀錄--> <div id="_docLog" class="forview" th:insert="~{common/panels/DocLogPanel :: DocLogPanel}"></div></div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="35%" class="hd1">
                                    <th:block th:text="#{'doc.creator'}">
                                        <!--  文件建立者-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id='creator'></span>(<span id='createTime'></span>)
                                </td>
                                <td width="30%" class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}">
                                        <!--  最後異動者-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <span id='updater'></span>(<span id='updateTime'></span>)
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                </td>
                                <td>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.docCode'}">
                                        <!--文件亂碼-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="randomCode"></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
				
				<fieldset>
					<!--class="tabs"-->
	                <div id="tabs-appr" class="tabs" style='width:99%;'>
	                    <ul>
	                        <li>
	                            <a href="#tabs-appr01"><b>
	                                    <th:block th:text="#{'C250M01E.title01'}">
	                                        
	                                    </th:block>
	                                </b></a>
	                        </li>
	                    </ul>
						<div class="tabCtx-warp">
	                        <div id="tabs-appr01" class="content">
 							<table width="100%">
                                <tr>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'C250M01E.managerId'}">
                                                <!--經副襄理-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="managerId"></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'C250M01E.bossId'}">
                                                <!-- 授信主管-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="bossId"></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'C250M01E.reCheckId'}">
                                                <!--覆核主管-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="reCheckId"></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'C250M01E.apprId'}">
                                                <!--  經辦-->
                                            </th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="showApprId"></span>
                                    </td>
                                </tr>
                            </table>
							</div>
	                    </div>

	               </div>				   
	            </fieldset>
			</form>	
			<!--===============================================-->	
			  <div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr>
	            		<td class="hd1" width="60%"><th:block th:text="#{'C250M01E.selectBoss'}"><!--  授信主管人數--></th:block>&nbsp;&nbsp;</td>
	                    <td width="40%"><select id="numPerson" name="numPerson">
	                    		<option value="1">1</option>
	                    		<option value="2">2</option>
	                            <option value="3">3</option>
								<option value="4">4</option>
	                    		<option value="5">5</option>
	                            <option value="6">6</option>
								<option value="7">7</option>
	                    		<option value="8">8</option>
	                            <option value="9">9</option>
								<option value="10">10</option>
	                    	</select>
							</td>
	                 </tr>
	                 <tr >
	                 	<td class="hd1" ><th:block th:text="#{'C250M01E.bossId'}"><!--  授信主管--></th:block>&nbsp;&nbsp;</td>
	            		<td >
	            			<div id="bossItem"></div>
	                 	</td>
	                 </tr>
	                 <tr >
	            		<td class="hd1"><th:block th:text="#{'C250M01E.managerId'}"><!--經副襄理--></th:block>&nbsp;&nbsp;</td>
	                    <td><div id="managerItem"></div></td>
	                 </tr>
	           	 </table>
				</form>
  			</div>
		</th:block>
    </body>
</html>
