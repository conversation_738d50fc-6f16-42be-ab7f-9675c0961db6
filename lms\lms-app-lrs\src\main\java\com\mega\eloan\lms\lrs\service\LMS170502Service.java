/* 
 *  LMS1705Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service;
 
import java.util.List;
import java.util.Map;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01I;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.ICapService;

public interface LMS170502Service extends ICapService {

	/** 企金複審報告empSave 切換頁籤自動儲存
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapAjaxFormResult tempSave(PageParameters params)
	throws CapException;

	/** 查詢  一般/團貸覆審項目檔 (查詢覆審項目是否有變更)
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapAjaxFormResult queryL170m01d(PageParameters params)
	throws CapException;
	
	/**
	 * 更新覆審控制檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public CapAjaxFormResult updateElf412(PageParameters params)
	throws CapException;
	
	/**
	 * <pre>
	 * 產生所有授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public CapAjaxFormResult addCredit(PageParameters params)
	throws CapException;
	
	/**
	 * <pre>
	 * 刪除所有授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public CapAjaxFormResult deleteCredit(PageParameters params)
			throws CapException;
	
	/**
	 * <pre>
	 * 刪除選取的授信資料(不包含外部引進的)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public CapAjaxFormResult deleteChkCredit(PageParameters params)
			throws CapException;
	
	/**
	 * <pre>
	 * 新增 覆審報告表
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public CapAjaxFormResult addL170m01a(PageParameters params)
			throws CapException;
	
	/**
	 * <pre>
	 * 儲存(單筆)一般授信資料
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public CapAjaxFormResult saveL170m01b(PageParameters params)
			throws CapException;
	
	/**
	 * <pre>
	 * 儲存(全部標籤內容)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * </pre>
	 */
	public CapAjaxFormResult saveAll(PageParameters params)
			throws CapException;

	/** 設定L170M01C基本資料
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param curr
	 * @param ratioNo1
	 * @param ratioNo2
	 * @param ratioNo3
	 * @param ratioNo4
	 * @return L170M01C
	 */
	public L170M01C setC170M01CDefault(String mainId,String custId,String dupNo,String curr,String ratioNo1,String ratioNo2,String ratioNo3,String ratioNo4);

	/**
	 * 查詢DWADM.DW_ASLNDAVGOVS及DWADM.DW_LNQUOTOV資料儲存於L170M01B
	 * 
	 * @param brNo brNo
	 * @param custId custId
	 * @return Map<String,BigDecimal> 回傳產生的總和
	 */
	Map<String,String> saveL170m01bByBrNoCustId(String brNo, String custId, String mainId,
			String dupNo,L170M01A l170m01a) throws CapException;

	/** 把L170M01B作加總存入L170M01A
	 * @param brNo 分行
	 * @param mainId mainId
	 * @param l170m01a l170m01a ( 若不需要則傳Null)
	 * @param l170m01bList 更新後的l170m01b所有內容
	 * @param lnDataDateResult 判斷是否要對l170m01a的lnDataDate做儲存  1=要儲存currecttime 2=要儲存null
	 * @return
	 * @throws CapException
	 */
	public Map<String, String> sumL170M01BAllAndSaveL170M01A(String brNo,
			String mainId,L170M01A l170m01a,List<L170M01B> l170m01bList,String lnDataDateResult) throws CapException;

	public String checkS04Data(List<L170M01D> l170m01dList) throws CapException;
	
	public void initl170m01e_oV(L170M01E l170m01e, L170M01A meta, String userId, String timeFlag);

    public CapAjaxFormResult saveL170m01i(PageParameters params, L170M01A l170m01a)
            throws CapException;

    public String chkL170m01i(L170M01I l170m01i);
}
