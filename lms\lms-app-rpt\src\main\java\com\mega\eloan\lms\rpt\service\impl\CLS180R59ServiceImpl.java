package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.NumConverter;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.rpt.service.CLS180R59Service;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 待售房屋(餘屋)去化落後追蹤表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class CLS180R59ServiceImpl extends AbstractCapService implements CLS180R59Service {

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	
	@Resource
	L140M01ADao l140m01adao;
	
	@Resource
	CLSService clsService;

	@Override
	public Map<String, Integer> getTitleMap1(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.summaryListofOverdueMortgageCases", 10);//房貸逾催案件總表-地政士
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getTitleMapOfDataPeriod(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.CLS180R59.dataPeriod", 10);//撥款後1～3年房貸案件—期間：{0}年{1}月{2}日 ～{3}年{4}月{5}日
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getTitleMap2(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.detailListofOverdueMortgageCases", 10);//房貸逾催案件明細-地政士
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getTitleMap3(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.landsmenRankingList", 10);//地政士排名(按件數由高至低)
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getTitleMap4(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.landsmenRankingList", 10);//地政士敘做總件數表(由高至低排序，僅列示前三大分行)
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getTitleMap5(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.top3BranchRankingList", 10);//分行排名(按件數由高至低，取前3家)
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getTitleMap6(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.normalLandsmenDetailList", 10);//正常地政士引介案件明細
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getOverdueCaseSummaryListTitle(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.mortgageOverdueCaseSummaryList", 10);//房貸逾催案件總表-地政士
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getOverdueCaseDetailListTitle(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.mortgageOverdueCaseDetailList", 10);//房貸逾催案件明細-地政士
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getHeaderMapForSummaryOverdueCase(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.name", 15); //地政士姓名
		titleMap.put("CLS180R59.number.overdueLoan.allbranch", 20); //屬該地政士引介之逾催件(A)
		titleMap.put("CLS180R59.number.recommended.allbranch", 15); //該地政士引介總件數(B)
		titleMap.put("CLS180R59.shareA_B", 10); //占比
		titleMap.put("CLS180R59.amount.overdueLoan.allbranch", 20); //屬該地政士引介之逾催金額(C)
		titleMap.put("CLS180R59.amount.recommended.allbranch", 20); //該地政士引介總金額(D)
		titleMap.put("CLS180R59.shareC_D", 10); //占比
		titleMap.put("CLS180R59.branchCode", 10);//承辦行
		titleMap.put("CLS180R59.number.recommended.branch", 20); //承辦行由該地政士引介件數(F)
		titleMap.put("CLS180R59.amount.recommended.branch", 20); //承辦行由該地政士引介金額(H)
		titleMap.put("CLS180R59.total.number.recommended.branch", 20); //承辦行地政士引介總件數(I)
		titleMap.put("CLS180R59.shareF_I", 10); //占比
		return titleMap;
	}
	
	@Override
	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String header : headerMap.keySet()){
			this.mergeFieldAndSetWidth(sheet, colIndex, colIndex++, rowIndex, rowIndex+2, prop.getProperty(header), cellFormat, headerMap.get(header));
		}
	}

	@Override
	public Map<String, List<String>> setBodyContentForSummaryOverdueCase(WritableSheet sheet, int colIndex, int rowIndex, Properties prop, 
								Map<String, TreeSet<String>> allManAndBranchMap, 
								Map<String, String> keyNameMap,
								Map<String, Integer> numOverDueCaseAllBranch_A, 
								Map<String, Integer> numTotalIntroCaseAllBranch_B, 
								Map<String, BigDecimal> amtOverDueAllBranch_C,
								Map<String, BigDecimal> amtTotalIntroAllBranch_D, 
								Map<String, Integer> numOverDueCaseBranch_E, 
								Map<String, Integer> numIntroCaseBranch_F,
								Map<String, BigDecimal> amtOverDueBranch_G, 
								Map<String, BigDecimal> amtIntroBranch_H, 
								Map<String, Integer> numTotalIntroCaseBranch_I) throws RowsExceededException, WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		WritableFont fontBold = new WritableFont(WritableFont.createFont("標楷體"), 12, WritableFont.BOLD);
		WritableCellFormat cellFormatNoBorder = new WritableCellFormat(fontBold);
		{
			cellFormatNoBorder.setWrap(true);
			cellFormatNoBorder.setAlignment(Alignment.CENTRE);
			cellFormatNoBorder.setVerticalAlignment(VerticalAlignment.CENTRE);
		}
		
		BigDecimal total_A = BigDecimal.ZERO;
		BigDecimal total_B = BigDecimal.ZERO;
		BigDecimal total_C = BigDecimal.ZERO;
		BigDecimal total_D = BigDecimal.ZERO;
		
		Map<String, List<String>> rtnMap = new HashMap<String, List<String>>();
		
		for(String keyByMan : allManAndBranchMap.keySet()){
			
			sheet.addCell(new Label(colIndex++, rowIndex, keyNameMap.get(keyByMan), cellFormat));//地政士姓名
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numOverDueCaseAllBranch_A, keyByMan), cellFormat));   //全行逾放件數(A)
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numTotalIntroCaseAllBranch_B, keyByMan), cellFormat));//全行總引介件數 (B)

			BigDecimal A = LMSUtil.nullToZeroBigDecimal(numOverDueCaseAllBranch_A.get(keyByMan));
			BigDecimal B = LMSUtil.nullToZeroBigDecimal(numTotalIntroCaseAllBranch_B.get(keyByMan));
			sheet.addCell(new Label(colIndex++, rowIndex, this.getDividedValueByHalfUp(A, B).toString() + "%", cellFormat));//占比
			
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(amtOverDueAllBranch_C, keyByMan), cellFormat));//全行逾放金額 (C)
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(amtTotalIntroAllBranch_D, keyByMan), cellFormat));//全行總引介金額 (D)
			BigDecimal C = LMSUtil.nullToZeroBigDecimal(amtOverDueAllBranch_C.get(keyByMan));
			BigDecimal D = LMSUtil.nullToZeroBigDecimal(amtTotalIntroAllBranch_D.get(keyByMan));
			sheet.addCell(new Label(colIndex++, rowIndex, this.getDividedValueByHalfUp(C, D).toString() + "%", cellFormat));//占比
			
			Map<BigDecimal, String> compareMap = new TreeMap<BigDecimal, String>(Collections.reverseOrder());
			for(String brNo : allManAndBranchMap.get(keyByMan)){
				
				String keyByBranch = keyByMan + brNo;
				sheet.addCell(new Label(colIndex++, rowIndex, brNo, cellFormat));//分行
//				sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numOverDueCaseBranch_E, keyByBranch), cellFormat));//分行逾放件數 (E)
				sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numIntroCaseBranch_F, keyByBranch), cellFormat));//分行引介件數 (F)
//				BigDecimal E = LMSUtil.nullToZeroBigDecimal(numOverDueCaseBranch_E.get(keyByBranch));
				BigDecimal F = LMSUtil.nullToZeroBigDecimal(numIntroCaseBranch_F.get(keyByBranch));
//				sheet.addCell(new Label(colIndex++, rowIndex, this.getDividedValueByHalfUp(E, F).toString() + "%", cellFormat));//占比
//				sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(amtOverDueBranch_G, keyByBranch), cellFormat));//分行逾放金額 (G)
				sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(amtIntroBranch_H, keyByBranch), cellFormat));//分行引介金額 (H)
//				BigDecimal H = LMSUtil.nullToZeroBigDecimal(amtIntroBranch_H.get(keyByBranch));
				sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numTotalIntroCaseBranch_I, brNo), cellFormat));//分行總引介件數 (I)
				BigDecimal I = LMSUtil.nullToZeroBigDecimal(numTotalIntroCaseBranch_I.get(brNo));
				sheet.addCell(new Label(colIndex++, rowIndex, this.getDividedValueByHalfUp(F, I).toString() + "%", cellFormat));//占比
				
				colIndex = 7;
				rowIndex++;
				
				compareMap.put(this.getDividedValueByHalfUp(F, I), brNo);
			}
			
			int i = 1;
			List<String> brNoList = new ArrayList<String>();
			for(BigDecimal key : compareMap.keySet()) {
				String brNo = compareMap.get(key);
				brNoList.add(brNo);
				
				if(i == 3){
					break;
				}
				
				i++;
			}
			
			rtnMap.put(keyByMan, brNoList);
			
			total_A = total_A.add(A);
			total_B = total_B.add(B);
			total_C = total_C.add(C);
			total_D = total_D.add(D);
			
			colIndex = 0;
		}

		rowIndex++;
		sheet.addCell(new Label(colIndex++, rowIndex, prop.getProperty("CLS180R59.total"), cellFormatNoBorder));//合計
		sheet.addCell(new Label(colIndex++, rowIndex, NumConverter.addComma(total_A.toPlainString(), "#,###,###,###,##0") + "件", cellFormatNoBorder));
		sheet.addCell(new Label(colIndex++, rowIndex, NumConverter.addComma(total_B.toPlainString(), "#,###,###,###,##0") + "件", cellFormatNoBorder));
		colIndex++;
		sheet.addCell(new Label(colIndex++, rowIndex, NumConverter.addComma(total_C.toPlainString(), "#,###,###,###,##0") + "元", cellFormatNoBorder));
		sheet.addCell(new Label(colIndex++, rowIndex, NumConverter.addComma(total_D.toPlainString(), "#,###,###,###,##0") + "元", cellFormatNoBorder));
		
		return rtnMap;
	}
	
	@Override
	public Map<String, Integer> getHeaderMapForDetailOverdueCase(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.name", 15); //地政士姓名
		titleMap.put("CLS180R59.certificateNo", 15); //地政士證書字號
		titleMap.put("CLS180R59.officeName", 15); //地政士事務所名稱
		titleMap.put("CLS180R59.branchCode", 10);//分行
		titleMap.put("CLS180R59.custName", 15); //借款人姓名
		titleMap.put("CLS180R59.custId", 10); //ID
		titleMap.put("CLS180R59.cntrNo", 10); //額度序號
		titleMap.put("CLS180R59.accountNo", 15); //帳號
		titleMap.put("CLS180R59.approvedQuota", 15); //核准額度
		titleMap.put("CLS180R59.balace", 15); //餘額
		titleMap.put("CLS180R59.firstGrantDate", 10); //首次撥款日
		titleMap.put("CLS180R59.overdueDate", 10); //逾放日
		titleMap.put("CLS180R59.overdueAmt", 15); //逾催金額
		titleMap.put("CLS180R59.accountStatus", 10); //戶況
		titleMap.put("CLS180R59.overdueCode", 10); //逾期代碼
		titleMap.put("CLS180R59.abnormalNotification", 25); //異常通報表之異常帳況
		return titleMap;
	}
	
	@Override
	public void setBodyContentForDetailOverdueCase(WritableSheet sheet, int colIndex, int rowIndex, Properties prop, List<Map<String, Object>> overdueList) throws RowsExceededException, WriteException{
		
		Map<String, String> accStatusMap = this.codeTypeService.findByCodeType("actDtlStat");
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.LEFT);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		for(Map<String, Object> map : overdueList){
			boolean isOverdue = map.get("ELF464_KIND_CODE") != null || StringUtils.isNotBlank((String)map.get("ELF464_KIND_CODE")) ? true : false;
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "ELF457_LANAME", ""), cellFormat));//地政士姓名
			String certificateNo = map.get("ELF457_LAYEAR") + "年" + map.get("ELF457_LAWORD") + map.get("ELF457_LANO") + "號";
			sheet.addCell(new Label(colIndex++, rowIndex, certificateNo, cellFormat));//地政士證書字號
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "ELF457_LONAME", ""), cellFormat));//地政士事務所名稱
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_BR_NO", ""), cellFormat));//分行
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "CNAME", ""), cellFormat));//借款人姓名
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_CUST_ID", ""), cellFormat));//CUSTID
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_CONTRACT", ""), cellFormat));//額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_LOAN_NO", ""), cellFormat));//帳號
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "LNF155_FACT_AMT"), cellFormat));//核准額度
			double balance = MapUtils.getDoubleValue(map, "LNF155_LOAN_BAL_TW");
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, balance, cellFormat));//餘額
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_USE_DATE", ""), cellFormat));//首次撥款日
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "COVHAPNDT", ""), cellFormat));//逾放日
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, isOverdue ? balance : new Double("0"), cellFormat));//逾催金額
			String accStatus = map.get("CSTAT") != null && StringUtils.isNotBlank((String)map.get("CSTAT")) ? accStatusMap.get("CSTAT") : "";
			sheet.addCell(new Label(colIndex++, rowIndex, accStatus, cellFormat));//戶況
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_LOAN_OVER", ""), cellFormat));//逾期代碼
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNFE0851_PROCESS", ""), cellFormat));//異常通報表之異常帳況
			rowIndex++;
			colIndex = 0;
		}
	}
	
	@Override
	public Map<String, Integer> getHeaderMapForLandsmenRankingList(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.name", 15); //地政士姓名
		titleMap.put("CLS180R59.certificateNo", 25); //地政士證書字號
		titleMap.put("CLS180R59.total.number.recommended.branch", 25); //承辦行地政士引介總件數(I)
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getHeaderMapForBranchRankingByLandsmenList(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.name", 15); //地政士姓名
		titleMap.put("CLS180R59.certificateNo", 15); //地政士證書字號
		titleMap.put("CLS180R59.number.recommended.allbranch", 15);//該地政士引介總件數(B)
		
		titleMap.put("CLS180R59.branch.top1", 15); //分行
		titleMap.put("CLS180R59.number.recommended.branch.top1", 25); //承辦行由該地政士引介件數(F)
		titleMap.put("CLS180R59.total.number.recommended.branch.top1", 25); //承辦行地政士引介總件數(I)
		titleMap.put("CLS180R59.share.top1", 10); //占比
		
		titleMap.put("CLS180R59.branch.top2", 15); //分行
		titleMap.put("CLS180R59.number.recommended.branch.top2", 25); //承辦行由該地政士引介件數(F)
		titleMap.put("CLS180R59.total.number.recommended.branch.top2", 25); //承辦行地政士引介總件數(I)
		titleMap.put("CLS180R59.share.top2", 10); //占比
		
		titleMap.put("CLS180R59.branch.top3", 15); //分行
		titleMap.put("CLS180R59.number.recommended.branch.top3", 25); //承辦行由該地政士引介件數(F)
		titleMap.put("CLS180R59.total.number.recommended.branch.top3", 25); //承辦行地政士引介總件數(I)
		titleMap.put("CLS180R59.share.top3", 10); //占比
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getHeaderMapForTop3BranchRankingList() {
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.branchCode", 15); //分行
		titleMap.put("CLS180R59.total.number.recommended.branch", 25); //承辦行地政士引介總件數(I)
		titleMap.put("CLS180R59.total.number.recommended.allbranch", 25); //全國地政士總引介件數(J)
		titleMap.put("CLS180R59.share1", 10); //占比
		return titleMap;
	}
	
	@Override
	public Map<String, Integer> getHeaderMapForNormalLandsmenDetailList(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R59.name", 15); //地政士姓名
		titleMap.put("CLS180R59.certificateNo", 15); //地政士證書字號
		titleMap.put("CLS180R59.officeName", 15); //地政士事務所名稱
		titleMap.put("CLS180R59.branchCode", 10);//分行
		titleMap.put("CLS180R59.custName", 15); //借款人姓名
		titleMap.put("CLS180R59.custId", 10); //ID
		titleMap.put("CLS180R59.cntrNo", 10); //額度序號
		titleMap.put("CLS180R59.accountNo", 15); //帳號
		titleMap.put("CLS180R59.approvedQuota", 15); //核准額度
		titleMap.put("CLS180R59.balace", 15); //餘額
		titleMap.put("CLS180R59.firstGrantDate", 10); //首次撥款日
		return titleMap;
	}
	
	@Override
	public void setBodyContentForLandsmenRankingList(WritableSheet sheet, int colIndex, int rowIndex, Properties prop,
														List<Map<String, Object>> overdueList,
														Map<String, Integer> numTotalIntroCaseAllBranch_B) throws WriteException {
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.LEFT);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		Set<String> keyByNameSet = new HashSet<String>();
		
		for(Map<String, Object> map : overdueList){
			
			String laYear = (String)map.get("ELF457_LAYEAR");
			String laWord = (String)map.get("ELF457_LAWORD");
			String laNo = (String)map.get("ELF457_LANO");
			String keyByMan = laYear + laWord + laNo;
			
			if(!keyByNameSet.contains(keyByMan)){
				sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "ELF457_LANAME", ""), cellFormat));//地政士姓名
				String certificateNo = map.get("ELF457_LAYEAR") + "年" + map.get("ELF457_LAWORD") + map.get("ELF457_LANO") + "號";
				sheet.addCell(new Label(colIndex++, rowIndex, certificateNo, cellFormat));//地政士證書字號
				sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numTotalIntroCaseAllBranch_B, keyByMan), cellFormat));//AA地政士 全行總引介件數 (B)
				keyByNameSet.add(keyByMan);
				rowIndex++;
				colIndex = 0;
			}
		}
	}
	
	@Override
	public void setBodyContentForBranchRankingByLandsmenList(WritableSheet sheet, int colIndex, int rowIndex, Properties prop,
																Map<String, List<String>> sortedTop3BrNoMap,
																Map<String, String> keyNameMap,
																Map<String, Integer> numIntroCaseBranch_F, Map<String, Integer> numTotalIntroCaseBranch_I,
																Map<String, String> landsmanNoMap,
																Map<String, Integer> numTotalIntroCaseAllBranch_B
																) throws WriteException {
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.LEFT);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		for(String keyByMan : sortedTop3BrNoMap.keySet()){
			
			List<String> brNoList = sortedTop3BrNoMap.get(keyByMan);
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(keyNameMap, keyByMan, ""), cellFormat));//地政士姓名
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(landsmanNoMap, keyByMan, ""), cellFormat));//地政士證書字號
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numTotalIntroCaseAllBranch_B, keyByMan), cellFormat));//該地政士引介總件數(B)

			for(String brNo : brNoList){
				String keyByBranch = keyByMan + brNo;
				sheet.addCell(new Label(colIndex++, rowIndex, brNo, cellFormat));//分行
				sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numIntroCaseBranch_F, keyByBranch), cellFormat));//承辦行由該地政士引介件數(F)
				sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(numTotalIntroCaseBranch_I, brNo), cellFormat));//承辦行地政士引介總件數(I)
				BigDecimal F = LMSUtil.nullToZeroBigDecimal(numIntroCaseBranch_F.get(keyByBranch));
				BigDecimal I = LMSUtil.nullToZeroBigDecimal(numTotalIntroCaseBranch_I.get(brNo));
				sheet.addCell(new Label(colIndex++, rowIndex, this.getDividedValueByHalfUp(F, I).toString() + "%", cellFormat));//占比
			}
			
			rowIndex++;
			colIndex = 0;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public void setBodyContentForsetBodyContentForTop3BranchRankingList(WritableSheet sheet, int colIndex, int rowIndex, Properties prop,
																	Map<String, Integer> numTotalIntroCaseBranch_I
																	) throws WriteException {
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.LEFT);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		int total = 0;
		for(Integer i : numTotalIntroCaseBranch_I.values()){
			total += i;
		}
		
		Map<Integer, String> sortMap = new TreeMap<Integer, String>(Collections.reverseOrder());
		sortMap.putAll(MapUtils.invertMap(numTotalIntroCaseBranch_I));

		for(Integer number : sortMap.keySet()){
			sheet.addCell(new Label(colIndex++, rowIndex, sortMap.get(number), cellFormat));//分行
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, number, cellFormat));//承辦行地政士引介總件數(I)
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, total, cellFormat));//承辦行地政士引介總件數(I)
			BigDecimal I = LMSUtil.nullToZeroBigDecimal(number);
			sheet.addCell(new Label(colIndex++, rowIndex, this.getDividedValueByHalfUp(I, new BigDecimal(total)).toString() + "%", cellFormat));//占比
			
			rowIndex++;
			colIndex = 0;
		}
		
	}
	
	@Override
	public void setBodyContentForNormalLandsmenDetailList(WritableSheet sheet, int colIndex, int rowIndex, Properties prop, List<Map<String, Object>> overdueList
																) throws WriteException {
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.LEFT);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		for(Map<String, Object> map : overdueList){
			boolean isOverdue = map.get("ELF464_KIND_CODE") != null || StringUtils.isNotBlank((String)map.get("ELF464_KIND_CODE")) ? true : false;
			if(!isOverdue){
				
			}
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "ELF457_LANAME", ""), cellFormat));//地政士姓名
			String certificateNo = map.get("ELF457_LAYEAR") + "年" + map.get("ELF457_LAWORD") + map.get("ELF457_LANO") + "號";
			sheet.addCell(new Label(colIndex++, rowIndex, certificateNo, cellFormat));//地政士證書字號
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "ELF457_LONAME", ""), cellFormat));//地政士事務所名稱
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_BR_NO", ""), cellFormat));//分行
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "CNAME", ""), cellFormat));//借款人姓名
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_CUST_ID", ""), cellFormat));//CUSTID
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_CONTRACT", ""), cellFormat));//額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_LOAN_NO", ""), cellFormat));//帳號
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "LNF155_FACT_AMT"), cellFormat));//核准額度
			double balance = MapUtils.getDoubleValue(map, "LNF155_LOAN_BAL_TW");
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, balance, cellFormat));//餘額
			sheet.addCell(new Label(colIndex++, rowIndex, MapUtils.getString(map, "LNF155_USE_DATE", ""), cellFormat));//首次撥款日
			rowIndex++;
			colIndex = 0;
		}
	}
	
	@Override
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 20);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.LEFT, BorderLineStyle.THIN);
			cellFormat.setBorder(Border.TOP, BorderLineStyle.THIN);
			cellFormat.setLocked(false);
		}

		for(String title : titleMap.keySet()){
			this.mergeFieldWithAutoSize(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty(title), cellFormat);
		}
	}
	
	@Override
	public void setTitleContentOfUnit(WritableSheet sheet, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 15);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.RIGHT);
			cellFormat.setVerticalAlignment(VerticalAlignment.BOTTOM);
			cellFormat.setBorder(Border.RIGHT, BorderLineStyle.THIN);
			cellFormat.setLocked(false);
		}

		this.mergeFieldWithAutoSize(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty("CLS180R59.unit"), cellFormat);
	}
	
	private void mergeFieldAndSetWidth(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat, int width) throws RowsExceededException, WriteException{
		sheet.setColumnView(fromColIndex, width);
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
	
	private void mergeFieldWithAutoSize(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}

	private BigDecimal getDividedValueByHalfUp(BigDecimal a, BigDecimal b){
		
		if(b.compareTo(BigDecimal.ZERO) == 0){
			return BigDecimal.ZERO;
		}
		
		return a.divide(b, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2);
	}
	
	@Override
	public void setTitleContentOfDataPeriod(WritableSheet sheet, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, String fromDate, String toDate) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.LEFT, BorderLineStyle.THIN);
			cellFormat.setBorder(Border.BOTTOM, BorderLineStyle.THIN);
			cellFormat.setLocked(false);
		}

		this.mergeFieldWithAutoSize(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty("CLS180R59.dataPeriod") + fromDate + "～" + toDate, cellFormat);
	}
	
	@Override
	public int getCount(Map<String, Integer> map, String key){
		Integer count = map.get(key);
		
		if(count == null){
			count = 0;
		}
		
		return ++count;
	}
}
