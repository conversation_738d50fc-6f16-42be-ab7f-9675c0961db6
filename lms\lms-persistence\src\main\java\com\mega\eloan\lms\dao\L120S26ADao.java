/* 
 * L120S26ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S26A;

/** 企金T70明細檔 **/
public interface L120S26ADao extends IGenericDao<L120S26A> {

	L120S26A findByOid(String oid);

	List<L120S26A> findByMainId(String mainId);

	L120S26A findByMainIdAndCustId(String mainId, String custId);

	List<L120S26A> findByIndex01(String mainId);

	List<L120S26A> findByMainIdAndCustList(String mainId, String[] custList,
			boolean negCase);
}