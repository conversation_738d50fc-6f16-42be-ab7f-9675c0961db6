/* 
 * RATETBLDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.RATETBL;

/** 額度信用評等資料檔 **/
public interface RATETBLDao extends IGenericDao<RATETBL> {

	RATETBL findByOid(String oid);
	
	List<RATETBL> findByMainId(String mainId);
	
	RATETBL findByUniqueKey(String curr, String dataYmd);

	List<RATETBL> findByIndex01(String curr, String dataYmd);

	List<RATETBL> findByIndex02(String dataYmd);
}