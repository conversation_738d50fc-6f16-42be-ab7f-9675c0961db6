package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.panels.LMSM01Panel;
import com.mega.eloan.lms.lms.panels.LMSS02Panel;
import com.mega.eloan.lms.lms.panels.LMSS02Panel01;
import com.mega.eloan.lms.lms.panels.LMSS02Panel02;
import com.mega.eloan.lms.lms.panels.LMSS02Panel03;
import com.mega.eloan.lms.lms.panels.LMSS02Panel04;

/**
 * <pre>
 * 借款人企金-分頁
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lmss02")
public class LMSS02Page extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.afterExecute(model, params);
//		model.addAttribute("_lmss02panel01_visible", true);
		new LMSS02Panel01("lmss02panel01").processPanelData(model, params);

//		model.addAttribute("_lmss02panel02_visible", true);
		new LMSS02Panel02("lmss02panel02").processPanelData(model, params);

//		model.addAttribute("_lmss02panel03_visible", true);
		new LMSS02Panel03("lmss02panel03").processPanelData(model, params);

//		model.addAttribute("_lmss02panel04_visible", true);
		new LMSS02Panel04("lmss02panel04").processPanelData(model, params);
        
		// 共用借款人引進界面
		Panel panel = new LMSM01Panel("lmsm01_panel");
		renderJsI18N(LMSM01Panel.class);
		panel.processPanelData(model, params);
		//因同名的關係將導致i18n render會被覆蓋 故將LMSS02Page.properties合併至LMSS02Panel.properties
		renderJsI18N(LMSS02Panel.class);
	}

	@Override
    public String getViewName() {
        // 不要headerarea
        return "common/pages/None";
    }

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
