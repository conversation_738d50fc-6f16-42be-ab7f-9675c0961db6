package tw.com.jcs.flow;

import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.jcs.flow.node.FlowNode;

/**
 * <pre>
 * <h1>流程執行實體</h1> 流程的實體，紀錄流程目前的執行狀態與資料，並實際負責流程的執行
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> Software Inc.
 * @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public interface FlowInstance {

    String CREATE = "__create__";
    String CANCEL = "__cancel__";

    /**
     * 取得對應的流程定義
     * 
     * @return
     */
    FlowDefinition getDefinition();

    /**
     * 取得流程的ID
     * 
     * @return
     */
    Object getId();

    /**
     * 取得流程目前的執行序列
     * 
     * @return
     */
    int getSeq();

    /**
     * 取得流程目前所在的狀態(節點名)
     * 
     * @return
     */
    String getState();

    /**
     * 取得流程目前所屬的組別
     * 
     * @return
     */
    String getRoleId();

    /**
     * 取得流程目前所屬的成員
     * 
     * @return
     */
    String getUserId();

    /**
     * 取得流程目前所屬的部門
     * 
     * @return
     */
    String getDeptId();

    /**
     * 取得流程目前執行序列的開始時間
     * 
     * @return
     */
    Date getBeginTime();

    /**
     * 取得流程目前執行序列的結束時間
     * 
     * @return
     */
    Date getEndTime();

    /**
     * 取得父流程編號(無父流程時為-1)
     * 
     * @return
     */
    Object getParentInstanceId();

    /**
     * 取得父流程建立目前流程時的執行狀態
     * 
     * @return
     */
    String getParentInstanceState();

    /**
     * 取得所有子流程的ID<br/>
     * Key為建立子流程時的狀態
     * 
     * @return
     */
    Map<String, List<Object>> getSubInstanceList();

    /**
     * 取得流程變數
     * 
     * @param name
     * @return
     */
    Object getAttribute(String name);

    /**
     * 設定流程變數
     * 
     * @param name
     * @param value
     */
    void setAttribute(String name, Object value);

    /**
     * 移除流程變數
     * 
     * @param name
     */
    void removeAttribute(String name);

    /**
     * 執行下一流程
     * 
     */
    void next();

    /**
     * 指定成員
     * 
     * @param userId
     */
    void setUserId(String userId);

    /**
     * 指定部門
     * 
     * @param deptId
     */
    void setDeptId(String deptId);

    /**
     * 指定組別
     * 
     * @param roleId
     */
    void setRoleId(String roleId);

    /**
     * 指定下一狀態節點的可執行成員
     * 
     * @param userId
     *            成員ID
     */
    void setNextUser(String userId);

    /**
     * 指定下一狀態節點的可執行部門
     * 
     * @param deptId
     *            部門ID
     */
    void setNextDept(String deptId);

    /**
     * 取得節點<br/>
     * 如回傳null，則代表找不到，或節點無法判斷
     * 
     * @return
     */
    public FlowNode getNode();

    /**
     * 取得狀態<br/>
     * 如回傳null，則代表找不到，或狀態無法判斷
     * 
     * @return
     */
    public String getStatus();

    /**
     * 取得LOG<br/>
     * 如回傳null，則代表找不到，或LOG無法判斷
     * 
     * @return
     */
    public String getLog();

    /**
     * 取得屬性<br/>
     * 
     * @return
     */
    public Map<String, String> getAttr();

    /**
     * 取得下一個節點<br/>
     * 如回傳null，則代表找不到，或下一節點無法判斷
     * 
     * @return
     */
    public FlowNode getNextNode();

    /**
     * 取得下一個節點名稱
     * 
     * @return
     */
    public String getNextNodeName();

    /**
     * 取得下一個節點狀態<br/>
     * 如回傳null，則代表找不到，或下一個節點狀態無法判斷
     * 
     * @return
     */
    public String getNextStatus();

    /**
     * 取得下一個節點LOG<br/>
     * 如回傳null，則代表找不到，或LOG無法判斷
     * 
     * @return
     */
    public String getNextLog();

    /**
     * 取得下一個節點屬性<br/>
     * 
     * @return
     */
    public Map<String, String> getNextAttr();

    /**
     * 取得節點名稱(TAG)<br/>
     * 如回傳null，則代表找不到，或狀態無法判斷
     * 
     * @return
     */
    public String getTagName();

    /**
     * 取得下一個節點名稱(TAG)<br/>
     * 如回傳null，則代表找不到，或狀態無法判斷
     * 
     * @return
     */
    public String getNextTagName();

    /**
     * 取得Transition屬性<br/>
     * 如回傳null，則代表找不到，或狀態無法判斷
     * 
     * @return
     */
    public Map<String, String> getTransitionAttr();

}
