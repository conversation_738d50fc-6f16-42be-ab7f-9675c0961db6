/* 
 * LMS1101M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.lms.model.L120M01A;

/**<pre>
 * 授信簽報書(企金授權內) FormHandler
 * </pre>
 * @since  2011/8/6
 * <AUTHOR>
 * @version <ul>
 * 			 <li>2012/8/16,贊介 負責人資料改為可編輯欄位，解決輸入負責人姓名空白問題
 *           <li>2011/8/6,<PERSON>,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1101formhandler")
@DomainClass(L120M01A.class)
public class LMS1101M01FormHandler extends LMSM01CFormHandler {	
}