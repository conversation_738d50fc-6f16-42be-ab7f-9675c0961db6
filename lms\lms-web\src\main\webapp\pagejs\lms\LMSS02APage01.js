/** 個金借款人js  */

var _handler = "";
initDfd.done(function() {
	setCloseConfirm(true);
	if(responseJSON.docURL == "/lms/lms1215m01"){
		// 授權外個金
		_handler = "lms1215formhandler";
	}else{
		//授權內個金
		_handler = "lms1115formhandler";
	}
	
	$("#getCustData").click(function(){
		var $addborrow = $("#addborrow");
		var $custId = $addborrow.find("#custId").val();
		var $custName = $addborrow.find("#custName").val();
		if(($custId != null && $custId != undefined && $custId != '')
		&& ($custName != null && $custName != undefined && $custName != '')){
			// 統一編號、名稱擇一輸入引進即可
			CommonAPI.showErrorMessage(i18n.lmss02a["l120s02.alert26"]);
		}else if(($custId == null || $custId == undefined || $custId == '')
		&& ($custName == null || $custName == undefined || $custName == '')){
			// 請輸入統一編號或名稱
			CommonAPI.showErrorMessage(i18n.lmss02a["l120s02.alert27"]);
		}else{
		    var defaultOption = {};
			if($custId != null && $custId != undefined && $custId != ''){
				defaultOption = {
					defaultValue: $custId //預設值 
				};
			}else{
				defaultOption = {
					defaultName : $custName
				};				
			}			
			//綁入MegaID
			CommonAPI.openQueryBox(
				$.extend({
/*
	                defaultValue: $custId, //預設值 
	                defaultName : $custName,
*/
					defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : ($custName != null && $custName != undefined && $custName != '') ? "2" : "",
	                divId:"addborrow", //在哪個div 底下
	                isInSide:false, 
	                autoResponse: { // 是否自動回填資訊 
	                           id: "custId", // 統一編號欄位ID 
	                       dupno: "dupNo", // 重覆編號欄位ID 
	                      name: "custName" // 客戶名稱欄位ID 
	                },fn:function(obj){
						//alert(JSON.stringify(obj));
	                   /*{
	                    * custid 統一編號,
	                    dupno 重覆序號,
	                    name 中文姓名,
	                    lname 英文姓名,
	                    buscd 行業別}*/						
						$addborrow.find("#buscd").val(obj.buscd);						
						if($("#addborrow").valid()){
							var exist = false;
							// 個金
							$.ajax({								
								//驗證前端資料有無和後端資料重複
								type : "POST",
								handler : _handler,
								data : {
									formAction : "checkAddBorrow",
									mainId : responseJSON.mainId,
									custId : obj.custid,
									dupNo : obj.dupno,
									checkdata : exist
								},
								success:function(responseData){
									$.thickbox.close();
									exist = responseData.checkdata;
									if(exist==true){
										CommonAPI.showMessage(i18n.lmss02a["l120s02.alert8"]);
									}else{
										//沒有任何主要借款人
										if(!responseData.haseCust){
											var buscd = obj.buscd;
											if (buscd != "130300" && buscd != "060000") {
												// 法人
												addNewBor(obj, false);
											}else {
												// 自然人
												CommonAPI.confirmMessage(i18n.lmss02a["l120s02.confirm4"], function(b){
													if (b) {
														addNewBor(obj, true);
													}
													else {
														addNewBor(obj, false);
													}
												});
											}																				
										}else{
											addNewBor(obj,false);
										}
										//$.thickbox.close();
									}
								}
							});
						}						
						
					}
				},defaultOption)
			);			
		}
	});	
	var L120S01aGrid = $("#l120s01agrid").iGrid({		//借款人基本資料GridView
		handler : 'lms1205gridhandler',
		height : 350,
		sortname : 'keyMan',
		postData : {
			formAction : "queryC120M01A",
			rowNum:10
		},
		rownumbers:true,
		rowNum:10,
		//multiselect : true,
		colModel : [ {
			colHeader : "&nbsp;", // 主要借款人Flag
			align : "center",
			width : 10, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'keyMan' // col.id
		},{
			colHeader : i18n.lmss02a["l120s01a.custid"], //身分證統編
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			formatter : 'click',
			onclick : openDoc,
			name : 'custId' //col.id
		}, {
			colHeader : i18n.lmss02a["l120s01a.custname2"], //借款人名稱
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		}, {
			colHeader : i18n.lmss02a["l120s01a.custrlt"], //與主要借款人關係
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'o_custRlt' //col.id
		}, {
			colHeader : i18n.lmss02a["l120s01a.custpos"], //相關身份
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custPos' //col.id
		}, {
	       	 colHeader: "&nbsp",//"檢核欄位",
	         name: 'o_chkYN',
	         width: 20,
	         sortable: true,
			 align:"center"
	    }, {
			colHeader : "docType",
			name : 'docType',
			hidden : true
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = L120S01aGrid.getRowData(rowid);
			openDoc(null, null, data);
		}
	});	
});

function getCustData3(){
	$.ajax({
		type : "POST",
		handler : _handler,
		data : 
		{
			formAction : "getCustData3",
			oid : thisOid
		},
		success:function(responseData){
			//alert(JSON.stringify(responseData));
			var $CLS1205S02aForm = $("#CLS1205S02aForm");
			var $CLS1205S02bForm = $("#CLS1205S02bForm");
			var $CLS1205S02dForm = $("#CLS1205S02dForm");
			var $CLS1205S02eForm = $("#CLS1205S02eForm");
			var $formSel = $("#formSel");
			$formSel.find("#empty1").show();
			$formSel.find("#empty2").show();
			$formSel.find("#empty3").show();
			$formSel.find("#empty4").show();

			$CLS1205S02aForm.setData(responseData.CLS1205S02aForm, false);
			$CLS1205S02bForm.setData(responseData.CLS1205S02bForm, false);
			$CLS1205S02dForm.setData(responseData.CLS1205S02dForm, false);
			$CLS1205S02eForm.setData(responseData.CLS1205S02eForm, false);
			$("#showBorrowData").setData(responseData.showBorrowData, false);

			if(responseData.empty1){
				$formSel.find("#empty1").hide();
			}
			if(responseData.empty2){
				$formSel.find("#empty2").hide();
			}
			if(responseData.empty3){
				$formSel.find("#empty3").hide();
			}
			if(responseData.empty4){
				$formSel.find("#empty4").hide();
			}
			//手動設定動態下拉式選單
		      var json1 = {
		       		item : responseData.sCoAddr,
		       		format : "{key}",
		       		space: false
		       	};
		      var json2 = {
			       		item : responseData.sMComTel,
			       		format : "{key}",
			       		space: false
			       	};
		      var json3 = {
			       		item : responseData.sMTel,
			       		format : "{key}",
			       		space: false
			       	};
		      var json4 = {
			       		item : responseData.sEmail,
			       		format : "{key}",
			       		space: false
			       	};
		      $formSel.find("#sCoAddr").setItems(json1);
		      $formSel.find("#sMComTel").setItems(json2);
		      $formSel.find("#sMTel").setItems(json3);
		      $formSel.find("#sEmail").setItems(json4);			  
		      if(responseData.empty1 && responseData.empty2 && 
		    		  responseData.empty3 && responseData.empty4){
		    	   //下拉式選單全部找不到，顯示執行成功
		    	  CommonAPI.showMessage(i18n.def["runSuccess"]);
		      }else{
		    	  thickBoxSel();
		      }		
		}
	}).done(function(){
		var CLS1205S02aForm = $("#CLS1205S02aForm");		
		 $.ajax({
	        type: "POST",
	        handler: "customerformhandler",
	        data: {
	            formAction: "custQueryBy0024ByIdDupNo",
	            sendId: CLS1205S02aForm.find("#custId").val(),
	            dupNo: CLS1205S02aForm.find("#dupNo").val()
	        },
	        success: function(responseData){
	            CLS1205S02aForm.find("#custName").val(responseData.cname);
	        }
		});
	});	
}		

function queryBorrow(rowObject){
	//每次開啟都是第一頁
	  $("#tabs-x").tabs({ selected: 0 }); 

	//讀取已有的資料以進行修改
	ilog.debug(rowObject);
	//個金
	$.ajax({		//查詢主要借款人資料
		handler : _handler,
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "queryBorrow2",
			mainId : responseJSON.mainId,
			oid : rowObject.oid,
			theOid : rowObject.oid,
			isOther : false
		},
		success : function(obj2) {	
			//alert(JSON.stringify(obj2));
			var $CLS1205S02aForm = $("#CLS1205S02aForm");
			var $CLS1205S02bForm = $("#CLS1205S02bForm");
			var $CLS1205S02cForm = $("#CLS1205S02cForm");
			var $CLS1205S02dForm = $("#CLS1205S02dForm");
			var $CLS1205S02eForm = $("#CLS1205S02eForm");
			
			var $hJobType2a = $("#hJobType2a");
			var $hJobType2b = $("#hJobType2b");
			var $hJobType2c = $("#hJobType2c");
			var $hJobType2d = $("#hJobType2d");
			var $hJobType2e = $("#hJobType2e");
			var $hJobType2f = $("#hJobType2f");
			var $hJobType2g = $("#hJobType2g");
			var $hJobType2h = $("#hJobType2h");
			var $hJobType2i = $("#hJobType2i");
			var $hJobType2j = $("#hJobType2j");
			var $hJobType2k = $("#hJobType2k");
			var $hJobType2l = $("#hJobType2l");
			var $hJobType2m = $("#hJobType2m");
			var $hJobType2n = $("#hJobType2n");						
			//利用CodeType取得國別及幣別
		      var obj= CommonAPI.loadCombos(["CountryCode","Common_Currcy","lms1205s01_RelClass",
		                                     "Relation_type31","Relation_type32","Relation_type1",
		                                     "Relation_type2","lms1205s01_CustPos","lms1205s01_edu",
		                                     "lms1205s01_houseStatus","lms1205s01_cmsStatus",
		                                     "lms1205s01_jobType1","lms1205s01_jobType2",
		                                     "lms1205s01_jobType2a","lms1205s01_jobType2b",
		                                     "lms1205s01_jobType2c","lms1205s01_jobType2d",
		                                     "lms1205s01_jobType2e","lms1205s01_jobType2f",
		                                     "lms1205s01_jobType2g","lms1205s01_jobType2h",
		                                     "lms1205s01_jobType2i","lms1205s01_jobType2j",
		                                     "lms1205s01_jobType2k","lms1205s01_jobType2l",
		                                     "lms1205s01_jobType2m","lms1205s01_jobType2n",
		                                     "lms1205s01_jobTitle","lms1205s01_inDoc","lms1205s01_oIncome",
		                                     "lms1205s01_yIncomeCert","lms1205s01_mJobKind","lms1201s02_custClass"]);
		        //幣別	
		      $CLS1205S02bForm.find("#payCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
		      	});
		      $CLS1205S02cForm.find("#yFamCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
		     	});
		      $CLS1205S02cForm.find("#oMoneyCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      $CLS1205S02cForm.find("#invMBalCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      $CLS1205S02cForm.find("#invOBalCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      $CLS1205S02cForm.find("#branCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      $CLS1205S02dForm.find("#mPayCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		     	//國別
		      $CLS1205S02aForm.find("#ntCode").setItems({
		     		item:obj.CountryCode,
		     		format : "{value} - {key}",
		     		space: true
		     	});
		      	//關係類別
		      $CLS1205S02aForm.find("#custRlt_main").setItems({
			  		item:obj.lms1205s01_RelClass,
			  		format : "{key}",
			  		space: false
			  	});
		      	//與主要借款人關係1
		      $CLS1205S02aForm.find("#custRlt_content1").setItems({
			  		item:obj.Relation_type1,
			  		format : "{key}",
			  		space: false
			  	});
		      	//與主要借款人關係2
		      $CLS1205S02aForm.find("#custRlt_content2").setItems({
			  		item:obj.Relation_type2,
			  		format : "{key}",
			  		space: false
			  	});
		      	//與主要借款人關係3
		      $CLS1205S02aForm.find("#custRlt_content3").setItems({
			  		item:obj.Relation_type31,	
			  		format : "{key}",
			  		space: false
			  	});
		      	//與主要借款人關係4
		      $CLS1205S02aForm.find("#custRlt_content4").setItems({
			  		item:obj.Relation_type32,	  			
			  		format : "{key}",
			  		space: false
			  	});
		      	//相關身份
		      $CLS1205S02aForm.find("#custPos").setItems({
			  		item:obj.lms1205s01_CustPos,
			  		format : "{key}",
			  		space: false
			  	});
		      	//學歷
		      $CLS1205S02aForm.find("#edu").setItems({
			  		item:obj.lms1205s01_edu,
			  		format : "{key}",
			  		space: true
			  	});
		      	//現住房屋
		      $CLS1205S02aForm.find("#houseStatus").setItems({
			  		item:obj.lms1205s01_houseStatus,
			  		format : "{key}",
			  		space: true
			  	});
		      	//不動產狀況
		      $CLS1205S02aForm.find("#cmsStatus").setItems({
			  		item:obj.lms1205s01_cmsStatus,
			  		format : "{key}",
			  		space: true
			  	});
				//客戶類別
				$CLS1205S02aForm.find("#custClass").setItems({
		     		item:obj.lms1201s02_custClass,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      	//職業別大類
		      $CLS1205S02bForm.find("#jobType1").setItems({
			  		item:obj.lms1205s01_jobType1,
			  		format : "{key}",
					fn : function(){
						var $hJobType2a = $("#hJobType2a");
						var $hJobType2b = $("#hJobType2b");
						var $hJobType2c = $("#hJobType2c");
						var $hJobType2d = $("#hJobType2d");
						var $hJobType2e = $("#hJobType2e");
						var $hJobType2f = $("#hJobType2f");
						var $hJobType2g = $("#hJobType2g");
						var $hJobType2h = $("#hJobType2h");
						var $hJobType2i = $("#hJobType2i");
						var $hJobType2j = $("#hJobType2j");
						var $hJobType2k = $("#hJobType2k");
						var $hJobType2l = $("#hJobType2l");
						var $hJobType2m = $("#hJobType2m");
						var $hJobType2n = $("#hJobType2n");						
						$hJobType2a.hide();
						$hJobType2b.hide();
						$hJobType2c.hide();
						$hJobType2d.hide();
						$hJobType2e.hide();
						$hJobType2f.hide();
						$hJobType2g.hide();
						$hJobType2h.hide();
						$hJobType2i.hide();
						$hJobType2j.hide();
						$hJobType2k.hide();
						$hJobType2l.hide();
						$hJobType2m.hide();
						$hJobType2n.hide();						
						if($(this).find("option:selected").val()== "01"){
							$hJobType2a.show();
						}else if($(this).find("option:selected").val()== "02"){
							$hJobType2b.show();										
						}else if($(this).find("option:selected").val()== "03"){
							$hJobType2c.show();		
						}else if($(this).find("option:selected").val()== "04"){
							$hJobType2d.show();										
						}else if($(this).find("option:selected").val()== "05"){
							$hJobType2e.show();									
						}else if($(this).find("option:selected").val()== "06"){
							$hJobType2f.show();										
						}else if($(this).find("option:selected").val()== "07"){
							$hJobType2g.show();									
						}else if($(this).find("option:selected").val()== "08"){
							$hJobType2h.show();									
						}else if($(this).find("option:selected").val()== "09"){
							$hJobType2i.show();										
						}else if($(this).find("option:selected").val()== "10"){
							$hJobType2j.show();										
						}else if($(this).find("option:selected").val()== "11"){
							$hJobType2k.show();									
						}else if($(this).find("option:selected").val()== "12"){
							$hJobType2l.show();										
						}else if($(this).find("option:selected").val()== "13"){
							$hJobType2m.show();										
						}else if($(this).find("option:selected").val()== "14"){
							$hJobType2n.show();			
						}						
					},
			  		space: true
			  	});
		      	//職業別細項
		      $CLS1205S02bForm.find("#jobType2a").setItems({
			  		item:obj.lms1205s01_jobType2a,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2b").setItems({
			  		item:obj.lms1205s01_jobType2b,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2c").setItems({
			  		item:obj.lms1205s01_jobType2c,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2d").setItems({
			  		item:obj.lms1205s01_jobType2d,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2e").setItems({
			  		item:obj.lms1205s01_jobType2e,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2f").setItems({
			  		item:obj.lms1205s01_jobType2f,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2g").setItems({
			  		item:obj.lms1205s01_jobType2g,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2h").setItems({
			  		item:obj.lms1205s01_jobType2h,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2i").setItems({
			  		item:obj.lms1205s01_jobType2i,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2j").setItems({
			  		item:obj.lms1205s01_jobType2j,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2k").setItems({
			  		item:obj.lms1205s01_jobType2k,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2l").setItems({
			  		item:obj.lms1205s01_jobType2l,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2m").setItems({
			  		item:obj.lms1205s01_jobType2m,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2n").setItems({
			  		item:obj.lms1205s01_jobType2n,
			  		format : "{key}",
			  		space: false
			  	});		      
		      	//職稱
		      $CLS1205S02bForm.find("#jobTitle").setItems({
			  		item:obj.lms1205s01_jobTitle,
			  		format : "{key}",
			  		space: true
			  	});
		      	//個人所得證明文件
		      $CLS1205S02bForm.find("#inDoc").setItems({
			  		item:obj.lms1205s01_inDoc,
			  		format : "{key}",
			  		space: true
			  	});
		      	//其他收入
		      $CLS1205S02cForm.find("#oIncome").setItems({
			  		item:obj.lms1205s01_oIncome,
			  		format : "{key}",
			  		space: true
			  	});
		      	//所得證明文件
		      $CLS1205S02cForm.find("#yIncomeCert").setItems({
			  		item:obj.lms1205s01_yIncomeCert,
			  		format : "{key}",
			  		space: true
			  	});
				
		      	//行業別
		      $CLS1205S02dForm.find("#mJobKind").setItems({
			  		item:obj.lms1205s01_mJobKind,
			  		format : "{key}",
					fn : function(){
						if($(this).val() == "99"){
							$("#mJobOther").show();
						}else{
							$("#mJobOther").hide();
							$("#mJobOther").val("");
						}
					},
			  		space: true
			  	});
				// 控制CodeType下拉式選單是否唯讀
				if(responseJSON.readOnly.toString() == "true"){
					$("#CLS1205S02aForm select").attr("disabled",true);
					$("#CLS1205S02bForm select").attr("disabled",true);
					$("#CLS1205S02cForm select").attr("disabled",true);
					$("#CLS1205S02dForm select").attr("disabled",true);
				}
				//將讀取好的資料存進前端網頁中
				//初始化Form
				 $CLS1205S02aForm.reset();
				 $CLS1205S02bForm.reset();
				 $CLS1205S02cForm.reset();
				 $CLS1205S02dForm.reset();
				 $CLS1205S02eForm.reset();
			    //設定值到前端
			    $CLS1205S02aForm.setData(obj2.CLS1205S02aForm,false);
			    $CLS1205S02aForm.find("#typCd").html(DOMPurify.sanitize(obj2.CLS1205S02aForm.typCd));
			    $CLS1205S02bForm.setData(obj2.CLS1205S02bForm,false);
			    $CLS1205S02cForm.setData(obj2.CLS1205S02cForm,false);
			    if(obj2.CLS1205S02cForm.credit1 == "A"){
			    	$CLS1205S02cForm.find("#credit1").attr("checked",true);
			    }else{
			    	$CLS1205S02cForm.find("#credit1").attr("checked",false);
			    }
			    if(obj2.CLS1205S02cForm.credit2 == "B"){
			    	$CLS1205S02cForm.find("#credit2").attr("checked",true);
			    }else{
			    	$CLS1205S02cForm.find("#credit2").attr("checked",false);
			    }			    
			    $CLS1205S02dForm.setData(obj2.CLS1205S02dForm,false);			
				if(obj2.CLS1205S02dForm.rDataNameC == "2"){
					$CLS1205S02dForm.find("input[name='rDataName']:radio:eq(2)").attr("checked",true);					
					$CLS1205S02dForm.find("#sMCustId").html(DOMPurify.sanitize(obj2.CLS1205S02dForm.sMCustId));
					$CLS1205S02dForm.find("#sMName").html(DOMPurify.sanitize(obj2.CLS1205S02dForm.sMName));
					$CLS1205S02dForm.find("#rDataName3").show();
/*
					if(obj2.sMName != undefined && obj2.sMName != null && obj2.sMName != ""){
						$CLS1205S02dForm.find("#rDataName3").show();	
					}
*/						
				}
			    $CLS1205S02eForm.setData(obj2.CLS1205S02eForm,false);			    								
				if(obj2.CLS1205S02aForm.keyMan=="Y"){	
					//為主要借款人
					$CLS1205S02aForm.find("#keyMan").attr("checked",true);
					$CLS1205S02aForm.find("#keyMan").val("Y");
					$CLS1205S02aForm.find("#custRltShow").val("");
					$CLS1205S02aForm.find("#custRlt").html("");
					$CLS1205S02aForm.find("#custPos option:eq(0)").attr("selected",true);
					$("#chk_radio1").hide();				
				}
				else{	
					//為非主要借款人
					$CLS1205S02aForm.find("#keyMan").attr("checked",false);
					$CLS1205S02aForm.find("#keyMan").val("N");
					$("#custRltShow").val("");
					if(obj2.CLS1205S02aForm.custRlt != "" && obj2.CLS1205S02aForm.custPos != ""){
						if(obj2.CLS1205S02aForm.isOther){
							//其他綜合關係
							$("#custRlt").html(DOMPurify.sanitize(obj2.CLS1205S02aForm.custRlt));
							$("#custRlt_content3 option").each(function(i){					
								if($(this).val() == obj2.CLS1205S02aForm.custRlt1){							
									$("#custRltShow").val(obj2.CLS1205S02aForm.custRlt1 
											+ obj2.CLS1205S02aForm.custRlt2 + " " + $(this).text()+"-");
								}
							});
							$("#custRlt_content4 option").each(function(i){					
								if($(this).val() == obj2.CLS1205S02aForm.custRlt2){
									$("#custRltShow").val($("#custRltShow").val() + " " + $(this).text());
								}
							});
						}else{
							$("[name='custRlt_content'] option").each(function(i){					
								if($(this).val() == obj2.CLS1205S02aForm.custRlt){
									$("#custRlt").html(DOMPurify.sanitize(obj2.CLS1205S02aForm.custRlt));
									$("#custRltShow").val(obj2.CLS1205S02aForm.custRlt + " " + $(this).text());
								}
							});
						}
						$("#custPos option").each(function(j){					
							if($(this).val() == obj2.CLS1205S02aForm.custPos){
								$(this).attr("selected",true);
							}
						});
						$("#chk_radio1").show();
					}else{
						$("#custRlt").html("");
						$("#custPos option:eq(0)").attr("selected",true);
						$("#chk_radio1").show();
					}
				}	
					// 初始化職業別				
					$hJobType2a.hide();
					$hJobType2b.hide();
					$hJobType2c.hide();
					$hJobType2d.hide();
					$hJobType2e.hide();
					$hJobType2f.hide();
					$hJobType2g.hide();
					$hJobType2h.hide();
					$hJobType2i.hide();
					$hJobType2j.hide();
					$hJobType2k.hide();
					$hJobType2l.hide();
					$hJobType2m.hide();
					$hJobType2n.hide();																		
					if(obj2.CLS1205S02bForm.jobType1 == "01"){
						$hJobType2a.show();
					}else if(obj2.CLS1205S02bForm.jobType1 == "02"){
						$hJobType2b.show();			
					}else if(obj2.CLS1205S02bForm.jobType1 == "03"){
						$hJobType2c.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "04"){
						$hJobType2d.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "05"){
						$hJobType2e.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "06"){
						$hJobType2f.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "07"){
						$hJobType2g.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "08"){
						$hJobType2h.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "09"){
						$hJobType2i.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "10"){
						$hJobType2j.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "11"){
						$hJobType2k.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "12"){
						$hJobType2l.show();									
					}else if(obj2.CLS1205S02bForm.jobType1 == "13"){
						$hJobType2m.show();								
					}else if(obj2.CLS1205S02bForm.jobType1 == "14"){
						$hJobType2n.show();									
					}				
			$("#showBorrowData").find("#custId").val(obj2.custId);
			$("#showBorrowData").find("#dupNo").val(obj2.dupNo);
			if(obj2.CLS1205S02aForm.needDisable){
				$CLS1205S02aForm.find("#keyMan").attr("checked",false);
				$CLS1205S02aForm.find("#keyMan").attr("disabled",true);
			}else{
				$CLS1205S02aForm.find("#keyMan").attr("disabled",false);
			}
				
			if(obj2.CLS1205S02aForm._renCd == "C"){
				// 法人
				$(".cbuscd").hide();
				// 個金簽報書借款人為法人時，不可以為主要借款人
				$CLS1205S02aForm.find("#keyMan").attr("checked",false);
				$CLS1205S02aForm.find("#keyMan").attr("disabled",true);
			}else{
				// 自然人
				$(".cbuscd").show();
				$CLS1205S02aForm.find("#keyMan").attr("disabled",false);		
			}			
													
			openDocAddBorrow(obj2.oid);
		}
	});	
}

/**
 * 讀取已有的資料以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc(cellvalue, options, rowObject) {		
	var val_L120M01A_ratingFlag = $("#val_L120M01A_ratingFlag").val()||'';
	if(val_L120M01A_ratingFlag==""){
		if($("#lmss02a_panel").attr("open") == "true"){
			$("#lmss02a_panel").load("../../lms/lmss02a",function(){
				var $CLS1205S02aForm = $("#CLS1205S02aForm");
				var $CLS1205S02bForm = $("#CLS1205S02bForm");
				var $CLS1205S02cForm = $("#CLS1205S02cForm");
				var $CLS1205S02dForm = $("#CLS1205S02dForm");
				var $CLS1205S02eForm = $("#CLS1205S02eForm");
				
				util.init($CLS1205S02aForm);
				util.init($CLS1205S02bForm);
				util.init($CLS1205S02cForm);
				util.init($CLS1205S02dForm);
				util.init($CLS1205S02eForm);
				if(true){
					//清除 span 欄位
					$("#l120s01m_queryDate").html('');	
				}
				
				queryBorrow(rowObject);
				$("#lmss02a_panel").attr("open",false);
				// 控制分頁頁籤內容唯讀(不包括下拉式選單)
				if(responseJSON.readOnly == "true"){		
					$CLS1205S02aForm.readOnlyChilds(true);
					$CLS1205S02aForm.find("button").hide();
					$CLS1205S02bForm.readOnlyChilds(true);
					$CLS1205S02bForm.find("button").hide();
					$CLS1205S02cForm.readOnlyChilds(true);
					$CLS1205S02cForm.find("button").hide();
					$CLS1205S02dForm.readOnlyChilds(true);
					$CLS1205S02dForm.find("button").hide();
					$CLS1205S02eForm.readOnlyChilds(true);
					$CLS1205S02eForm.find("button").hide();
				}
			});
		}else{
			queryBorrow(rowObject);
		}	
	}else{
		call_z_grid_openDoc(rowObject.oid, false, true, val_L120M01A_ratingFlag);					
	}	
};

function queryC120BusCd(c120m01a_oid){
	return $.ajax({							
		type : "POST", handler : 'lms1015m01formhandler',
		data : { formAction : "queryC120BusCd", 'c120m01a_oid':c120m01a_oid
		},
		success:function(json_queryC120BusCd){
			
		}
	});
}
/**
 * 新增借款人所開啟的ThickBox內容
 * @param value
 * @param titleName
 * @param tWidth
 * @param tHeight
 */
function openList(value, titleName, tWidth, tHeight){
	titleName = i18n.lmss02a["l120s01a.btn1"];
	$("#addborrow").reset();
	$.ajax({									
		//根據外層授信簽報書企金/個金案件自動替User勾選並改成唯讀狀態
		handler : _handler,
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "checkDocType",
			mainId : responseJSON.mainId,
			oid : responseJSON.oid
		},
		success : function(json) {
			$("#addborrow").find("[name='docType']").each(function(i){
				if($(this).attr("value")==json.docType){
					$(this).attr("checked",true);
				}
				$(this).attr("disabled",true);
			});
			
			$("#"+value).thickbox({	
				// 使用選取的內容進行彈窗
				title : titleName,
				width : 800,
				height : 380,
				modal : true,
/*
				valign : "bottom",
				align : "center",
*/
				i18n:i18n.def,
				buttons : {
/*
					"l120s02.thickbox1" : function(showMsg){
						$.thickbox.close();
						if($("#addborrow").valid()){
							var exist = false;
							// 個金
							$.ajax({								
								//驗證前端資料有無和後端資料重複
								type : "POST",
								handler : _handler,
								data : {
									formAction : "checkAddBorrow",
									mainId : responseJSON.mainId,
									custId : $("#addborrow").find("#custId").val(),
									dupNo : $("#addborrow").find("#dupNo").val(),
									checkdata : exist
								},
								success:function(responseData){
									exist = responseData.checkdata;
									if(exist==true){
										CommonAPI.showMessage(i18n.lmss02a["l120s02.alert8"]);
									}else{
										//沒有任何主要借款人
										if(!responseData.haseCust){
											CommonAPI.confirmMessage(i18n.lmss02a["l120s02.confirm4"],function(b){
												if(b){
													addNewBor(showMsg,true);
												}else{
													addNewBor(showMsg,false);
												}
											});									
										}else{
											addNewBor(showMsg,false);
										}
										//$.thickbox.close();
									}
								}
							});
						}
					},
*/
					"close" : function(){
						 API.confirmMessage(i18n.def['flow.exit'], function(res){
								if(res){
									$.thickbox.close();
								}
					        });
					}
				}
			});
		}
	});	
}

function addBorrowMain2(showMsg, check){
/*
	一、	自然人、法人之區分
	自然人：行業別為130300、060000。
	法  人：行業別自然人以外的全部為法人。
*/
	var buscd = $("#addborrow").find("#buscd").val();
	var renCd = "";
	var $CLS1205S02aForm = $("#CLS1205S02aForm");	
	if(buscd != "130300" && buscd != "060000" 
	&& buscd != "130300" && buscd != "60000"
	){
		// 法人
		$(".cbuscd").hide();
		// 記錄法人或自然人
		renCd = "C";
		// 個金簽報書借款人為法人時，不可以為主要借款人
		$CLS1205S02aForm.find("#keyMan").attr("checked",false);
		$CLS1205S02aForm.find("#keyMan").attr("disabled",true);
	}else{
		// 自然人
		$(".cbuscd").show();
		renCd = "N";
		$CLS1205S02aForm.find("#keyMan").attr("disabled",false);		
	}
	$.ajax({
		type : "POST",
		handler : _handler,
		data : {
			formAction : "addBorrowMain2",
			mainId : responseJSON.mainId,
			docType : $("#addborrow").find("input[name='docType']:checked").val(),
			typCd : $("#addborrow").find("input[name='typCd']:checked").val(),
			custId : $("#addborrow").find("#custId").val(),
			dupNo : $("#addborrow").find("#dupNo").val(),
			custName : $("#addborrow").find("#custName").val(),
			buscd : buscd,
			renCd : renCd,
			check : check,
			showMsg : showMsg
		},
		success:function(responseData){
			var val_L120M01A_ratingFlag = $("#val_L120M01A_ratingFlag").val()||'';
			ilog.debug("after addBorrowMain2, val_L120M01A_ratingFlag=["+val_L120M01A_ratingFlag+"]");
			if(val_L120M01A_ratingFlag==""){
				
			var $CLS1205S02aForm = $("#CLS1205S02aForm");
			var $CLS1205S02bForm = $("#CLS1205S02bForm");
			var $CLS1205S02cForm = $("#CLS1205S02cForm");
			var $CLS1205S02dForm = $("#CLS1205S02dForm");
			var $CLS1205S02eForm = $("#CLS1205S02eForm");
			var $hJobType2a = $("#hJobType2a");
			var $hJobType2b = $("#hJobType2b");
			var $hJobType2c = $("#hJobType2c");
			var $hJobType2d = $("#hJobType2d");
			var $hJobType2e = $("#hJobType2e");
			var $hJobType2f = $("#hJobType2f");
			var $hJobType2g = $("#hJobType2g");
			var $hJobType2h = $("#hJobType2h");
			var $hJobType2i = $("#hJobType2i");
			var $hJobType2j = $("#hJobType2j");
			var $hJobType2k = $("#hJobType2k");
			var $hJobType2l = $("#hJobType2l");
			var $hJobType2m = $("#hJobType2m");
			var $hJobType2n = $("#hJobType2n");			
			$hJobType2a.hide();
			$hJobType2b.hide();
			$hJobType2c.hide();
			$hJobType2d.hide();
			$hJobType2e.hide();
			$hJobType2f.hide();
			$hJobType2g.hide();
			$hJobType2h.hide();
			$hJobType2i.hide();
			$hJobType2j.hide();
			$hJobType2k.hide();
			$hJobType2l.hide();
			$hJobType2m.hide();
			$hJobType2n.hide();			
		      var obj= CommonAPI.loadCombos(["CountryCode","Common_Currcy","lms1205s01_RelClass",
		                                     "Relation_type31","Relation_type32","Relation_type1",
		                                     "Relation_type2","lms1205s01_CustPos","lms1205s01_edu",
		                                     "lms1205s01_houseStatus","lms1205s01_cmsStatus",
		                                     "lms1205s01_jobType1","lms1205s01_jobType2",
		                                     "lms1205s01_jobType2a","lms1205s01_jobType2b",
		                                     "lms1205s01_jobType2c","lms1205s01_jobType2d",
		                                     "lms1205s01_jobType2e","lms1205s01_jobType2f",
		                                     "lms1205s01_jobType2g","lms1205s01_jobType2h",
		                                     "lms1205s01_jobType2i","lms1205s01_jobType2j",
		                                     "lms1205s01_jobType2k","lms1205s01_jobType2l",
		                                     "lms1205s01_jobType2m","lms1205s01_jobType2n",
		                                     "lms1205s01_jobTitle","lms1205s01_inDoc","lms1205s01_oIncome",
		                                     "lms1205s01_yIncomeCert","lms1205s01_mJobKind","lms1201s02_custClass"]);
		        //幣別	
		      $CLS1205S02bForm.find("#payCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
		      	});
		      $CLS1205S02cForm.find("#yFamCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
		     	});
		      $CLS1205S02cForm.find("#oMoneyCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      $CLS1205S02cForm.find("#invMBalCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      $CLS1205S02cForm.find("#invOBalCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      $CLS1205S02cForm.find("#branCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		      $CLS1205S02dForm.find("#mPayCurr").setItems({
		     		item:obj.Common_Currcy,
		     		format : "{value} - {key}",
		     		space: true
			    });
		     	//國別
		      $CLS1205S02aForm.find("#ntCode").setItems({
		     		item:obj.CountryCode,
		     		format : "{value} - {key}",
		     		space: true
		     	});
		      	//關係類別
		      $CLS1205S02aForm.find("#custRlt_main").setItems({
			  		item:obj.lms1205s01_RelClass,
			  		format : "{key}",
			  		space: false
			  	});
		      	//與主要借款人關係1
		      $CLS1205S02aForm.find("#custRlt_content1").setItems({
			  		item:obj.Relation_type1,
			  		format : "{key}",
			  		space: false
			  	});
		      	//與主要借款人關係2
		      $CLS1205S02aForm.find("#custRlt_content2").setItems({
			  		item:obj.Relation_type2,
			  		format : "{key}",
			  		space: false
			  	});
		      	//與主要借款人關係3
		      $CLS1205S02aForm.find("#custRlt_content3").setItems({
			  		item:obj.Relation_type31,	
			  		format : "{key}",
			  		space: false
			  	});
		      	//與主要借款人關係4
		      $CLS1205S02aForm.find("#custRlt_content4").setItems({
			  		item:obj.Relation_type32,	  			
			  		format : "{key}",
			  		space: false
			  	});
		      	//相關身份
		      $CLS1205S02aForm.find("#custPos").setItems({
			  		item:obj.lms1205s01_CustPos,
			  		format : "{key}",
			  		space: false
			  	});
		      	//學歷
		      $CLS1205S02aForm.find("#edu").setItems({
			  		item:obj.lms1205s01_edu,
			  		format : "{key}",
			  		space: true
			  	});
		      	//現住房屋
		      $CLS1205S02aForm.find("#houseStatus").setItems({
			  		item:obj.lms1205s01_houseStatus,
			  		format : "{key}",
			  		space: true
			  	});
		      	//不動產狀況
		      $CLS1205S02aForm.find("#cmsStatus").setItems({
			  		item:obj.lms1205s01_cmsStatus,
			  		format : "{key}",
			  		space: true
			  	});
				
				//客戶類別
		      $CLS1205S02aForm.find("#custClass").setItems({
		     		item:obj.lms1201s02_custClass,
		     		format : "{value} - {key}",
		     		space: true
		     	});
				
		      	//職業別大類
		      $CLS1205S02bForm.find("#jobType1").setItems({
			  		item:obj.lms1205s01_jobType1,
			  		format : "{key}",
					fn : function(){
						var $hJobType2a = $("#hJobType2a");
						var $hJobType2b = $("#hJobType2b");
						var $hJobType2c = $("#hJobType2c");
						var $hJobType2d = $("#hJobType2d");
						var $hJobType2e = $("#hJobType2e");
						var $hJobType2f = $("#hJobType2f");
						var $hJobType2g = $("#hJobType2g");
						var $hJobType2h = $("#hJobType2h");
						var $hJobType2i = $("#hJobType2i");
						var $hJobType2j = $("#hJobType2j");
						var $hJobType2k = $("#hJobType2k");
						var $hJobType2l = $("#hJobType2l");
						var $hJobType2m = $("#hJobType2m");
						var $hJobType2n = $("#hJobType2n");						
						$hJobType2a.hide();
						$hJobType2b.hide();
						$hJobType2c.hide();
						$hJobType2d.hide();
						$hJobType2e.hide();
						$hJobType2f.hide();
						$hJobType2g.hide();
						$hJobType2h.hide();
						$hJobType2i.hide();
						$hJobType2j.hide();
						$hJobType2k.hide();
						$hJobType2l.hide();
						$hJobType2m.hide();
						$hJobType2n.hide();						
						if($(this).find("option:selected").val()== "01"){
							$hJobType2a.show();
						}else if($(this).find("option:selected").val()== "02"){
							$hJobType2b.show();										
						}else if($(this).find("option:selected").val()== "03"){
							$hJobType2c.show();		
						}else if($(this).find("option:selected").val()== "04"){
							$hJobType2d.show();										
						}else if($(this).find("option:selected").val()== "05"){
							$hJobType2e.show();									
						}else if($(this).find("option:selected").val()== "06"){
							$hJobType2f.show();										
						}else if($(this).find("option:selected").val()== "07"){
							$hJobType2g.show();									
						}else if($(this).find("option:selected").val()== "08"){
							$hJobType2h.show();									
						}else if($(this).find("option:selected").val()== "09"){
							$hJobType2i.show();										
						}else if($(this).find("option:selected").val()== "10"){
							$hJobType2j.show();										
						}else if($(this).find("option:selected").val()== "11"){
							$hJobType2k.show();									
						}else if($(this).find("option:selected").val()== "12"){
							$hJobType2l.show();										
						}else if($(this).find("option:selected").val()== "13"){
							$hJobType2m.show();										
						}else if($(this).find("option:selected").val()== "14"){
							$hJobType2n.show();			
						}						
					},					
			  		space: true
			  	});
		      	//職業別細項
		      $CLS1205S02bForm.find("#jobType2a").setItems({
			  		item:obj.lms1205s01_jobType2a,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2b").setItems({
			  		item:obj.lms1205s01_jobType2b,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2c").setItems({
			  		item:obj.lms1205s01_jobType2c,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2d").setItems({
			  		item:obj.lms1205s01_jobType2d,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2e").setItems({
			  		item:obj.lms1205s01_jobType2e,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2f").setItems({
			  		item:obj.lms1205s01_jobType2f,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2g").setItems({
			  		item:obj.lms1205s01_jobType2g,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2h").setItems({
			  		item:obj.lms1205s01_jobType2h,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2i").setItems({
			  		item:obj.lms1205s01_jobType2i,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2j").setItems({
			  		item:obj.lms1205s01_jobType2j,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2k").setItems({
			  		item:obj.lms1205s01_jobType2k,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2l").setItems({
			  		item:obj.lms1205s01_jobType2l,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2m").setItems({
			  		item:obj.lms1205s01_jobType2m,
			  		format : "{key}",
			  		space: false
			  	});
		      $CLS1205S02bForm.find("#jobType2n").setItems({
			  		item:obj.lms1205s01_jobType2n,
			  		format : "{key}",
			  		space: false
			  	});		      
		      	//職稱
		      $CLS1205S02bForm.find("#jobTitle").setItems({
			  		item:obj.lms1205s01_jobTitle,
			  		format : "{key}",
			  		space: true
			  	});
		      	//個人所得證明文件
		      $CLS1205S02bForm.find("#inDoc").setItems({
			  		item:obj.lms1205s01_inDoc,
			  		format : "{key}",
			  		space: true
			  	});
		      	//其他收入
		      $CLS1205S02cForm.find("#oIncome").setItems({
			  		item:obj.lms1205s01_oIncome,
			  		format : "{key}",
			  		space: true
			  	});
		      	//所得證明文件
		      $CLS1205S02cForm.find("#yIncomeCert").setItems({
			  		item:obj.lms1205s01_yIncomeCert,
			  		format : "{key}",
			  		space: true
			  	});
		      	//行業別
		      $CLS1205S02dForm.find("#mJobKind").setItems({
			  		item:obj.lms1205s01_mJobKind,
			  		format : "{key}",
			  		space: true
			  	});								      
			//初始化前端form
		    //$("#showBorrowData").reset();
			//第一頁籤
			$CLS1205S02aForm.reset();
			//第二頁籤
			$CLS1205S02bForm.reset();
			//第三頁籤
			$CLS1205S02cForm.reset();
			//第四頁籤
			$CLS1205S02dForm.reset();
			//第五頁籤
			$CLS1205S02eForm.reset();
			$CLS1205S02aForm.setData(responseData.CLS1205S02aForm,false);
			$CLS1205S02aForm.find("#typCd").html(DOMPurify.sanitize(responseData.CLS1205S02aForm.typCd));
			//開始設定資料到前端...
			//$("#showBorrowData").setData(responseData.showBorrowData,false);
			$("#custRlt").html("");
			$("#custPos option:eq(0)").attr("selected",true);
			$("#chk_radio1").show();
			$CLS1205S02aForm.find("#custRltShow").val("");

			if(check){
				$CLS1205S02aForm.find("#keyMan:checkbox").attr("checked",true);
				$CLS1205S02aForm.find("#keyMan").val("Y");
				$("#custRltShow").val("");
				$("#custRlt").html("");
				$("#custPos option:eq(0)").attr("selected",true);
				$("#chk_radio1").hide();
				CommonAPI.triggerOpener("gridview","reloadGrid");
			}else{
				$CLS1205S02aForm.find("#keyMan:checkbox").attr("checked",false);
				$CLS1205S02aForm.find("#keyMan").val("N");	//初始化主要借款人
				if($("#custRlt").html()!="" && $("#custPos option:selected").val()!=""){
					$("[name='custRlt_content'] option").each(function(i){					
						if($(this).val()==$("#custRlt").html()){
							$("#custRltShow").val($("#custRlt").html() + " " + $(this).text());
						}
					});
					$("#chk_radio1").show();
				}else{
					$("#custRlt").html("");
					$("#custPos option:eq(0)").attr("selected",true);
					$("#chk_radio1").show();
				}
			}
			$("#l120s01agrid").trigger("reloadGrid");	//更新Grid內容											      	
			openDocAddBorrow(responseData.oid); //開啟ThickBox
			setTimeout(function(){
				// 自動點選重新引進按鈕
				getCustData3();
			},50);	
			
			}else{
				$("#l120s01agrid").trigger("reloadGrid");	//更新Grid內容
				//在 addBorrowMain2 之後
				//第1次自動開啟頁面，執行引入基本資料
				//類似 getCustData3() 
				call_z_grid_openDoc(responseData.oid, true, true, val_L120M01A_ratingFlag);
			}			
		}
	});	
}

function call_z_grid_openDoc(c120m01a_oid, doOpenDoc, showModifyBtn, val_L120M01A_ratingFlag){ //在 openDoc(...) 已限定 val_L120M01A_ratingFlag 需有值
	
	if(true){
		/*
		 * 在簽報書異常通報中，原本的LMSS02APanel01.html 就有包含一些 html element
		 * 為免 id 重複，造成inject A，但 show B，做移除
		 * EX:與主要借款人關係 click 登錄 btn 開啟的tb
		 */
		$("#CLS1205S02aForm").empty(); //仍留著 CLS1205S02aForm	
		if(true){
			//信用風險管理遵循 Panel 在 LMSS02APanel01 的 divId 和 L120S01MPanel 相同
			//等一下在 load [lms1015s02c] 時，也會再 load 一次  L120S01MPanel
			//所以先把重複的 id 移除
			$("#tL120s01m").remove(); //連 tL120s01m 都砍掉
			$("#tL120s01o").remove();				
		}		
	}
	
	queryC120BusCd(c120m01a_oid).done(function(json_queryC120BusCd){
		if(json_queryC120BusCd.busCode){
			var loadUrl = "";
			var formAttrTxFlag = "";
			//無額度明細表的文件 EX: 異常通報
			var isCls = (json_queryC120BusCd.busCode=="060000" || json_queryC120BusCd.busCode=="130300");
			if(val_L120M01A_ratingFlag=="JP"){
				if(isCls){
					loadUrl = "../../lms/lms1015s02c";	
				}else{
					loadUrl = "../../lms/lms1015s02b";
				}					
				formAttrTxFlag = "lms1015s02cb";
			}else if(val_L120M01A_ratingFlag=="AU"){
				if(isCls){
					loadUrl = "../../lms/lms1025s02c";	
				}else{
					loadUrl = "../../lms/lms1025s02b";
				}
				formAttrTxFlag = "lms1025s02cb";
			}else if(val_L120M01A_ratingFlag=="TH"){
				if(isCls){
					loadUrl = "../../lms/lms1035s02c";	
				}else{
					loadUrl = "../../lms/lms1035s02b";
				}
				formAttrTxFlag = "lms1035s02cb";
			}else{
				if(val_L120M01A_ratingFlag=="00"){
					
				}else{
					
				}
				
				//default
				if(isCls){
					loadUrl = "../../lms/lms1115s02c";
				}else{
					loadUrl = "../../lms/lms1115s02b";
				}				
				formAttrTxFlag = "lms1115s02cb";
			}
			
			ilog.debug("noCntrNo@divOverSeaCLSPage_content will load:"+loadUrl+"[formAttrTxFlag="+formAttrTxFlag+"]");
			$("#divOverSeaCLSPage_content").load(loadUrl,function(){	
				
				$.ajax({type : "POST", handler : 'lms1015m01formhandler',
					data : $.extend(
						{
							'formAction':'overSeaCLS_query','mainId':responseJSON.mainId
							,'c120m01a_oid' : c120m01a_oid
							,'formAttrTxFlag' :formAttrTxFlag
						}
						, {}
					),success:function(json_overSeaCLS_query){
						var $form = overSeaCLSPage_getForm();
						$form.buildItem();
						overSeaCLSPage_buildItem();
						if($("#oIncome").length>0){
							var item = API.loadOrderCombosAsList("cls1131m01_othType")["cls1131m01_othType"];
					        $("#oIncome").setItems({ size: "1", item: item, clear: true, itemType: 'checkbox' })
						}
						$form.injectData(json_overSeaCLS_query);
						if(true){
							overSeaCLSPage_initEvent(json_overSeaCLS_query);
							overSeaCLSPage_2ndInjectData_afterInitEvent(json_overSeaCLS_query);							 
						}
						//~~~~~~~~~~~~
						var tb_btnArr = {};
						overSeaCLSPage_saveAjaxParam(
								{'mainId':responseJSON.mainId
									,'c120m01a_oid' : c120m01a_oid
								}
						);
						if( $("#buttonPanel").find("#btnSave").is("button")){
							tb_btnArr["saveData"] = function(){
								overSeaCLSPage_runSaveAjax().done(function(json_overSeaCLS_save){
									$("#l120s01agrid").trigger("reloadGrid");
									if(json_overSeaCLS_save.l120m01a_custId){//更改主借人
										$("form#showBorrowData").find("#custId").val(json_overSeaCLS_save.l120m01a_custId);
										$("form#showBorrowData").find("#dupNo").val(json_overSeaCLS_save.l120m01a_dupNo);
										$("form#showBorrowData").find("#custName").val(json_overSeaCLS_save.l120m01a_custName);
									}
									CommonAPI.triggerOpener("gridview", "reloadGrid");
									
									if(json_overSeaCLS_save.errMsg){
										CommonAPI.showErrorMessage(json_overSeaCLS_save.errMsg);	
									}else{
										CommonAPI.showMessage(i18n.def.saveSuccess);
									}
						        });					
							};
							
							tb_btnArr["del"] = function(){
								API.confirmMessage(i18n.def['confirmDelete'], function(res){
									if(res){							
										$.ajax({							
											type : "POST", handler : 'lms1015m01formhandler',
											data : $.extend(
												{
													'formAction':'overSeaCLS_del'
													,'mainId':responseJSON.mainId
													,'c120m01a_oid' : c120m01a_oid
													,'formAttrTxFlag':overSeaCLSPage_getForm().attr(FORM_ATTR_TX_FLAG)||''
												}
												, {}
											),
											success:function(json_delCust){
												$("#l120s01agrid").trigger("reloadGrid");	//更新Grid內容
												$.thickbox.close();
											}
										});						
									}
							     });
							};
						}else{
							//當無 saveBtn，以唯讀開啟 thickbox
							thickboxOptions.lockDoc = true;
							thickboxOptions.readOnly = true;
						}
						tb_btnArr["close"] = function(){
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
								if(res){
									$.thickbox.close();
								}
						     });
						};
						//~~~~~~~~~~~~
						$("#divOverSeaCLSPage").thickbox({		
							title : (json_overSeaCLS_query.custId||'')
									+'-'+(json_overSeaCLS_query.dupNo||'')
									+' '+(json_overSeaCLS_query.custName||'')
							, width : 950, height : 580, modal : true, i18n:i18n.def,						
							buttons : tb_btnArr
						});
						
						$form.closest('div.tabs')[0].scrollIntoView();

					    if (doOpenDoc) {
					    	overSeaCLSPage_reg_imp_custData();
					    }
					}
				});
			});
		}
	});
}
function addNewBor(showMsg, check){
	if($("#lmss02a_panel").attr("open") == "true"){
		$("#lmss02a_panel").load("../../lms/lmss02a", function(){
			addBorrowMain2(showMsg, check);
		});
		$("#lmss02a_panel").attr("open",false);
	}else{
		addBorrowMain2(showMsg, check);
	}
}