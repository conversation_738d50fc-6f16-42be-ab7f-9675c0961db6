/**
 * @license Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */

// The editor creator to use.
import ClassicEditorBase from '@ckeditor/ckeditor5-editor-classic/src/classiceditor';

// 資拓新增的Plugin	start
import Alignment from '@ckeditor/ckeditor5-alignment/src/alignment';
import FontBackgroundColor from '@ckeditor/ckeditor5-font/src/fontbackgroundcolor';
import FontColor from '@ckeditor/ckeditor5-font/src/fontcolor';
import FontSize from '@ckeditor/ckeditor5-font/src/fontsize';
import ImageBlock from '@ckeditor/ckeditor5-image/src/imageblock';
import ImageInline from '@ckeditor/ckeditor5-image/src/imageinline';
import ImageInsert from '@ckeditor/ckeditor5-image/src/imageinsert';
import ImageResize from '@ckeditor/ckeditor5-image/src/imageresize';
import IndentBlock from '@ckeditor/ckeditor5-indent/src/indentblock';
import LinkImage from '@ckeditor/ckeditor5-link/src/linkimage';
import SourceEditing from '@ckeditor/ckeditor5-source-editing/src/sourceediting';
import Strikethrough from '@ckeditor/ckeditor5-basic-styles/src/strikethrough';
import TableCaption from '@ckeditor/ckeditor5-table/src/tablecaption';
import TableCellProperties from '@ckeditor/ckeditor5-table/src/tablecellproperties';
import TableProperties from '@ckeditor/ckeditor5-table/src/tableproperties';
import Underline from '@ckeditor/ckeditor5-basic-styles/src/underline';
// 資拓新增的Plugin	end

// Cyber新增的Plugin	start
import GeneralHtmlSupport from '@ckeditor/ckeditor5-html-support/src/generalhtmlsupport';
// Cyber新增的Plugin	end

// 原始既有的Plugin	start
import Essentials from '@ckeditor/ckeditor5-essentials/src/essentials';
import UploadAdapter from '@ckeditor/ckeditor5-adapter-ckfinder/src/uploadadapter';
import Autoformat from '@ckeditor/ckeditor5-autoformat/src/autoformat';
import Bold from '@ckeditor/ckeditor5-basic-styles/src/bold';
import Italic from '@ckeditor/ckeditor5-basic-styles/src/italic';
import BlockQuote from '@ckeditor/ckeditor5-block-quote/src/blockquote';
import CKBox from '@ckeditor/ckeditor5-ckbox/src/ckbox';
import CKFinder from '@ckeditor/ckeditor5-ckfinder/src/ckfinder';
import EasyImage from '@ckeditor/ckeditor5-easy-image/src/easyimage';
import Heading from '@ckeditor/ckeditor5-heading/src/heading';
import Image from '@ckeditor/ckeditor5-image/src/image';
import ImageCaption from '@ckeditor/ckeditor5-image/src/imagecaption';
import ImageStyle from '@ckeditor/ckeditor5-image/src/imagestyle';
import ImageToolbar from '@ckeditor/ckeditor5-image/src/imagetoolbar';
import ImageUpload from '@ckeditor/ckeditor5-image/src/imageupload';
import Indent from '@ckeditor/ckeditor5-indent/src/indent';
import Link from '@ckeditor/ckeditor5-link/src/link';
import List from '@ckeditor/ckeditor5-list/src/list';
import MediaEmbed from '@ckeditor/ckeditor5-media-embed/src/mediaembed';
import Paragraph from '@ckeditor/ckeditor5-paragraph/src/paragraph';
import PasteFromOffice from '@ckeditor/ckeditor5-paste-from-office/src/pastefromoffice';
import PictureEditing from '@ckeditor/ckeditor5-image/src/pictureediting';
import Table from '@ckeditor/ckeditor5-table/src/table';
import TableToolbar from '@ckeditor/ckeditor5-table/src/tabletoolbar';
import TextTransformation from '@ckeditor/ckeditor5-typing/src/texttransformation';
import CloudServices from '@ckeditor/ckeditor5-cloud-services/src/cloudservices';
// 原始既有的Plugin	end

export default class ClassicEditor extends ClassicEditorBase {}

// Plugins to include in the build.
ClassicEditor.builtinPlugins = [
	Alignment,
	FontBackgroundColor,
	FontColor,
	FontSize,
	ImageBlock,
	ImageInline,
	ImageInsert,
	ImageResize,
	IndentBlock,
	LinkImage,
	SourceEditing,
	Strikethrough,
	TableCaption,
	TableCellProperties,
	TableProperties,
	Underline,
	GeneralHtmlSupport,

	Essentials,
	UploadAdapter,
	Autoformat,
	Bold,
	Italic,
	BlockQuote,
	CKBox,
	CKFinder,
	CloudServices,
	EasyImage,
	Heading,
	Image,
	ImageCaption,
	ImageStyle,
	ImageToolbar,
	ImageUpload,
	Indent,
	Link,
	List,
	MediaEmbed,
	Paragraph,
	PasteFromOffice,
	PictureEditing,
	Table,
	TableToolbar,
	TextTransformation
];

// Editor configuration.
ClassicEditor.defaultConfig = {
	toolbar: {
		items: [
			"insertTable", "|",
			"bold", "italic", "underline", "strikethrough", "|",
			"fontsize", "|",
			"fontColor", "fontBackgroundColor", "|",
			"outdent", "indent", "|",
			"numberedList", "bulletedList", "|",
			"alignment", "|",
			"link", "uploadImage", "|",
			"sourceEditing"
		]
	},
	image: {
		resizeUnit: "%",
		resizeOptions: [ {
			name: "resizeImage:original",
			value: null
		}, {
			name: "resizeImage:25",
			value: "25"
		}, {
			name: "resizeImage:50",
			value: "50"
		}, {
			name: "resizeImage:75",
			value: "75"
		}, {
			name: "resizeImage:100",
			value: "100"
		} ],
		toolbar: [
			"toggleImageCaption", "imageTextAlternative", "|",
			"imageStyle:wrapText", "imageStyle:breakText", "|",
			"resizeImage", "linkImage"
		]
	},
	fontSize: {
		options: [ 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 ],
		supportAllValues: !0
	},
	fontColor: {
		colors: [ {
			color: "#000000",
			label: "Black",
			hasBorder: !0
		}, {
			color: "#800000",
			label: "Maroon",
			hasBorder: !0
		}, {
			color: "#8B4513",
			label: "Saddle Brown",
			hasBorder: !0
		}, {
			color: "#305050",
			label: "Dark Slate Gray",
			hasBorder: !0
		}, {
			color: "#007F80",
			label: "Teal",
			hasBorder: !0
		}, {
			color: "#6A5BCD",
			label: "Navy",
			hasBorder: !0
		}, {
			color: "#4A0080",
			label: "Indigo",
			hasBorder: !0
		}, {
			color: "#696969",
			label: "Dark Gray",
			hasBorder: !0
		}, {
			color: "#B42222",
			label: "Fire Brick",
			hasBorder: !0
		}, {
			color: "#A62B2B",
			label: "Brown",
			hasBorder: !0
		}, {
			color: "#D9A520",
			label: "Golden Rod",
			hasBorder: !0
		}, {
			color: "#006600",
			label: "Dark Green",
			hasBorder: !0
		}, {
			color: "#3EE0CF",
			label: "Turquoise",
			hasBorder: !0
		}, {
			color: "#0000CC",
			label: "Medium Blue",
			hasBorder: !0
		}, {
			color: "#80007F",
			label: "Purple",
			hasBorder: !0
		}, {
			color: "#808080",
			label: "Gray",
			hasBorder: !0
		}, {
			color: "#FF0000",
			label: "Red",
			hasBorder: !0
		}, {
			color: "#FF8C00",
			label: "Dark Orange",
			hasBorder: !0
		}, {
			color: "#FFD900",
			label: "Gold",
			hasBorder: !0
		}, {
			color: "#00FF00",
			label: "Green",
			hasBorder: !0
		}, {
			color: "#00FFFF",
			label: "Cyan",
			hasBorder: !0
		}, {
			color: "#0000FF",
			label: "Blue",
			hasBorder: !0
		}, {
			color: "#EE81EE",
			label: "Violet",
			hasBorder: !0
		}, {
			color: "#A8A8A8",
			label: "Dim Gray",
			hasBorder: !0
		}, {
			color: "#FFA07A",
			label: "Light Salmon",
			hasBorder: !0
		}, {
			color: "#FFA600",
			label: "Orange",
			hasBorder: !0
		}, {
			color: "#FFFF00",
			label: "Yellow",
			hasBorder: !0
		}, {
			color: "#BFFF00",
			label: "Lime",
			hasBorder: !0
		}, {
			color: "#AFEEEE",
			label: "Pale Turquoise",
			hasBorder: !0
		}, {
			color: "#ADD8E6",
			label: "Light Blue",
			hasBorder: !0
		}, {
			color: "#DDA1DD",
			label: "Plum",
			hasBorder: !0
		}, {
			color: "#D4D4D4",
			label: "Light Gray",
			hasBorder: !0
		}, {
			color: "#FFF0F5",
			label: "Lavender Blush",
			hasBorder: !0
		}, {
			color: "#FAEAD6",
			label: "Antique White",
			hasBorder: !0
		}, {
			color: "#FFFFEB",
			label: "Light Yellow",
			hasBorder: !0
		}, {
			color: "#F0FFF0",
			label: "Honeydew",
			hasBorder: !0
		}, {
			color: "#F0FFFF",
			label: "Azure",
			hasBorder: !0
		}, {
			color: "#F0F8FF",
			label: "Alice Blue",
			hasBorder: !0
		}, {
			color: "#E5E5FA",
			label: "Lavender",
			hasBorder: !0
		}, {
			color: "#FFFFFF",
			label: "White",
			hasBorder: !0
		} ],
		columns: 8,
		documentColors: 40
	},
	fontBackgroundColor: {
		colors: [ {
			color: "#000000",
			label: "Black",
			hasBorder: !0
		}, {
			color: "#800000",
			label: "Maroon",
			hasBorder: !0
		}, {
			color: "#8B4513",
			label: "Saddle Brown",
			hasBorder: !0
		}, {
			color: "#305050",
			label: "Dark Slate Gray",
			hasBorder: !0
		}, {
			color: "#007F80",
			label: "Teal",
			hasBorder: !0
		}, {
			color: "#6A5BCD",
			label: "Navy",
			hasBorder: !0
		}, {
			color: "#4A0080",
			label: "Indigo",
			hasBorder: !0
		}, {
			color: "#696969",
			label: "Dark Gray",
			hasBorder: !0
		}, {
			color: "#B42222",
			label: "Fire Brick",
			hasBorder: !0
		}, {
			color: "#A62B2B",
			label: "Brown",
			hasBorder: !0
		}, {
			color: "#D9A520",
			label: "Golden Rod",
			hasBorder: !0
		}, {
			color: "#006600",
			label: "Dark Green",
			hasBorder: !0
		}, {
			color: "#3EE0CF",
			label: "Turquoise",
			hasBorder: !0
		}, {
			color: "#0000CC",
			label: "Medium Blue",
			hasBorder: !0
		}, {
			color: "#80007F",
			label: "Purple",
			hasBorder: !0
		}, {
			color: "#808080",
			label: "Gray",
			hasBorder: !0
		}, {
			color: "#FF0000",
			label: "Red",
			hasBorder: !0
		}, {
			color: "#FF8C00",
			label: "Dark Orange",
			hasBorder: !0
		}, {
			color: "#FFD900",
			label: "Gold",
			hasBorder: !0
		}, {
			color: "#00FF00",
			label: "Green",
			hasBorder: !0
		}, {
			color: "#00FFFF",
			label: "Cyan",
			hasBorder: !0
		}, {
			color: "#0000FF",
			label: "Blue",
			hasBorder: !0
		}, {
			color: "#EE81EE",
			label: "Violet",
			hasBorder: !0
		}, {
			color: "#A8A8A8",
			label: "Dim Gray",
			hasBorder: !0
		}, {
			color: "#FFA07A",
			label: "Light Salmon",
			hasBorder: !0
		}, {
			color: "#FFA600",
			label: "Orange",
			hasBorder: !0
		}, {
			color: "#FFFF00",
			label: "Yellow",
			hasBorder: !0
		}, {
			color: "#BFFF00",
			label: "Lime",
			hasBorder: !0
		}, {
			color: "#AFEEEE",
			label: "Pale Turquoise",
			hasBorder: !0
		}, {
			color: "#ADD8E6",
			label: "Light Blue",
			hasBorder: !0
		}, {
			color: "#DDA1DD",
			label: "Plum",
			hasBorder: !0
		}, {
			color: "#D4D4D4",
			label: "Light Gray",
			hasBorder: !0
		}, {
			color: "#FFF0F5",
			label: "Lavender Blush",
			hasBorder: !0
		}, {
			color: "#FAEAD6",
			label: "Antique White",
			hasBorder: !0
		}, {
			color: "#FFFFEB",
			label: "Light Yellow",
			hasBorder: !0
		}, {
			color: "#F0FFF0",
			label: "Honeydew",
			hasBorder: !0
		}, {
			color: "#F0FFFF",
			label: "Azure",
			hasBorder: !0
		}, {
			color: "#F0F8FF",
			label: "Alice Blue",
			hasBorder: !0
		}, {
			color: "#E5E5FA",
			label: "Lavender",
			hasBorder: !0
		}, {
			color: "#FFFFFF",
			label: "White",
			hasBorder: !0
		} ],
		columns: 8,
		documentColors: 40
	},
	table: {
		tableProperties: {
			borderColors: [ {
				color: "#000000",
				label: "Black",
				hasBorder: !0
			}, {
				color: "#4D4D4D",
				label: "Dim grey",
				hasBorder: !0
			}, {
				color: "#999999",
				label: "Grey",
				hasBorder: !0
			}, {
				color: "#E6E6E6",
				label: "Light grey",
				hasBorder: !0
			}, {
				color: "#FFFFFF",
				label: "White",
				hasBorder: !0
			}, {
				color: "#F44034",
				label: "Red",
				hasBorder: !0
			}, {
				color: "#E6994C",
				label: "Orange",
				hasBorder: !0
			}, {
				color: "#E6E64C",
				label: "Yellow",
				hasBorder: !0
			}, {
				color: "#99E64C",
				label: "Light green",
				hasBorder: !0
			}, {
				color: "#4CE64C",
				label: "Green",
				hasBorder: !0
			}, {
				color: "#4CE699",
				label: "Aquamarine",
				hasBorder: !0
			}, {
				color: "#4CE6E6",
				label: "Turquoise",
				hasBorder: !0
			}, {
				color: "#4C99E6",
				label: "Light blue",
				hasBorder: !0
			}, {
				color: "#4C4CE6",
				label: "Blue",
				hasBorder: !0
			}, {
				color: "#994CE6",
				label: "Purple",
				hasBorder: !0
			} ]
		},
		tableCellProperties: {
			borderColors: [ {
				color: "#000000",
				label: "Black",
				hasBorder: !0
			}, {
				color: "#4D4D4D",
				label: "Dim grey",
				hasBorder: !0
			}, {
				color: "#999999",
				label: "Grey",
				hasBorder: !0
			}, {
				color: "#E6E6E6",
				label: "Light grey",
				hasBorder: !0
			}, {
				color: "#FFFFFF",
				label: "White",
				hasBorder: !0
			}, {
				color: "#F44034",
				label: "Red",
				hasBorder: !0
			}, {
				color: "#E6994C",
				label: "Orange",
				hasBorder: !0
			}, {
				color: "#E6E64C",
				label: "Yellow",
				hasBorder: !0
			}, {
				color: "#99E64C",
				label: "Light green",
				hasBorder: !0
			}, {
				color: "#4CE64C",
				label: "Green",
				hasBorder: !0
			}, {
				color: "#4CE699",
				label: "Aquamarine",
				hasBorder: !0
			}, {
				color: "#4CE6E6",
				label: "Turquoise",
				hasBorder: !0
			}, {
				color: "#4C99E6",
				label: "Light blue",
				hasBorder: !0
			}, {
				color: "#4C4CE6",
				label: "Blue",
				hasBorder: !0
			}, {
				color: "#994CE6",
				label: "Purple",
				hasBorder: !0
			} ]
		},
		contentToolbar: [ "tableColumn", "tableRow", "mergeTableCells", "tableProperties", "tableCellProperties" ]
	},
	// This value must be kept in sync with the language defined in webpack.config.js.
	language: "zh"
};
