package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.mega.eloan.lms.base.pages.AbstractFileDownloadPage;

@Controller@RequestMapping(path = "/fms/lms9101r01.xls")
public class LMS9101V00Report extends AbstractFileDownloadPage {

	public LMS9101V00Report() {
		super();
	}

	@Override
	public String getDownloadFileName() {
		return "LMS9101PreList.xls";
	}

	@Override
	public String getFileDownloadServiceName() {
		return "lms9101xlsservice";
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
