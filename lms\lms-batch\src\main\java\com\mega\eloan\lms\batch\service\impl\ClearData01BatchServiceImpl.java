package com.mega.eloan.lms.batch.service.impl;

import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.model.BELDFM02;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ElDeleteFileService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * batch 刪除天數
 * </pre>
 * 
 * @since 2012/7/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/26,REX,new
 *          </ul>
 */
@Service("clearData01BatchServiceImpl")
public class ClearData01BatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(DeleteBatchServiceImpl.class);

	private static final long serialVersionUID = 1L;

	@Resource
	EloandbBASEService r6dbService;

	@Resource
	DocFileService docFileService;

	@Resource
	FlowService flowService;
	@Resource
	DocLogService docLogService;
	@Resource
	ElDeleteFileService elDeleteFileService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		// 可以從json內取得參數
		long t1 = System.currentTimeMillis();
		LOGGER.info("傳入參數==>[{}]", json.toString());

		int totalCount = 0;
		JSONObject request = json.getJSONObject("request");

		// deleteTable.properties 記錄需要刪除的table
		Properties properties = new Properties();
		JSONObject result = new JSONObject();

		List<BELDFM02> list = elDeleteFileService.getM02List("LMS");
		String custId = "", dupNo = "";

		try {

			for (BELDFM02 dfm02 : list) {
				custId = dfm02.getCustId();
				dupNo = dfm02.getDupNo();
				r6dbService.setDocStatusToDelByCustIdDupNo(dfm02);
			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"ClearData01BatchServiceImpl執行成功！");

		} catch (Exception ex) {
			LOGGER.error("[execute] Exception!!", ex);
			result = WebBatchCode.RC_ERROR;
			if (CapString.isEmpty(custId)) {
				result.element(WebBatchCode.P_RC_MSG, ex.toString()
						+ "==> 刪除文件  ERROR!!");
			} else {
				result.element(WebBatchCode.P_RESPONSE, this.getClass()
						.getName()
						+ "執行失敗！==>"
						+ "custId: "
						+ custId
						+ dupNo
						+ " - " + ex.getLocalizedMessage());
			}
		}

		return result;
	}

}
