package com.mega.eloan.lms.dc.conf;

import java.io.File;
import java.io.IOException;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.filefilter.FileFilterUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;

/**
 * <pre>
 * ViewListConfig
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,UFO,new
 *          <li>2013/3/7,UFO,增加判斷當ViewSize=0時，直接讀檔
 *          </ul>
 */
public class ViewListConfig {
	private static Logger logger = LoggerFactory.getLogger(ViewListConfig.class);

	private static Map<String, List<String>> map = new LinkedHashMap<String, List<String>>();

	private static ViewListConfig config = new ViewListConfig();

	public static ViewListConfig getInstance() {
		return config;
	}

	public List<String> getViewList(String viewListName) {
		if (!MainConfig.getInstance().isOnlineMode()) {
			return map.get(viewListName);
		} else {
			try {
				logger.info("直接讀檔：" + viewListName);
				return FileUtils.readLines(new File(viewListName));
			} catch (IOException ex) {
				throw new DCException(ex);
			}
		}
	}

	private ViewListConfig() {
		try {
			this.load();
		} catch (Exception ex) {
			throw new DCException("讀取ViewListConfig設定檔錯誤！", ex);
		}
	}

	// 2013-01-28 Modify By Bang:區分企金與個金
	private synchronized void load() throws Exception {
		ConfigData databean = MainConfig.getInstance().getConfig();

		String path = databean.getDC_ROOT() + databean.getCONF_PATH();
		map.clear();

		String lmsViewName = databean.getLMSViewListName();
		if (StringUtils.isEmpty(lmsViewName)) {
			throw new DCException("請設定LMSViewListName值在config.properties！");
		} else {
			this.loadViewList(map, path, lmsViewName);
		}
		String clsViewName = databean.getCLSViewListName();
		if (StringUtils.isEmpty(clsViewName)) {
			throw new DCException("請設定CLSViewListName值在config.properties！");
		} else {
			this.loadViewList(map, path, clsViewName);
		}
	}

	private void loadViewList(Map<String, List<String>> map, String path,
			String viewName) throws Exception {
		Collection<File> files = FileUtils.listFiles(new File(path),
				FileFilterUtils.prefixFileFilter(viewName, IOCase.INSENSITIVE),
				FileFilterUtils.falseFileFilter());

		if (logger.isDebugEnabled()) {
			logger.debug("----------------------------------------------------");
			logger.debug(" path=" + path);
		}

		final String EXT = MainConfig.getInstance().getConfig()
				.getViewListExt();
		for (File file : files) {
			if (EXT.equalsIgnoreCase(FilenameUtils.getExtension(file.getPath()))) {
				List<String> value = FileUtils.readLines(file);
				String key = FilenameUtils.getName(file.getPath());
				map.put(key, value);

				if (logger.isDebugEnabled()) {
					logger.debug("key=" + key + ",value=" + value);
				}
			}
		}
		if (logger.isDebugEnabled()) {
			logger.debug("----------------------------------------------------");
		}
	}

	@Override
	public String toString() {
		StringBuffer str = new StringBuffer();
		for (Map.Entry<String, List<String>> entry : map.entrySet()) {
			str.append(entry.getKey())
					.append("=")
					.append(ToStringBuilder.reflectionToString(entry.getValue()))
					.append("\n");
		}
		return str.toString();
	}
}
