/* 
 *CLS9021ServiceImp.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C340M01ADao;
import com.mega.eloan.lms.dao.C900S02ADao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.fms.pages.CLS9021V00Page;
import com.mega.eloan.lms.fms.service.CLS9021Service;
import com.mega.eloan.lms.mfaloan.bean.MISLN20;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.mfaloan.service.impl.AbstractMFAloanJdbc;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C900S02A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 優惠房貸額度維護 - 總額度維護作業
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          <li>2013/08/05,Vector Lo,更正mail function與site
 *          </ul>
 */
/* Use MIS-RDB */
@Service
public class CLS9021ServiceImpl extends AbstractMFAloanJdbc implements
		CLS9021Service {
	private static final Logger logger = LoggerFactory
			.getLogger(CLS9021ServiceImpl.class);
	
	private static final DateFormat CUSTOM_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm");
	
	@Resource
	BranchService branchService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	CodeTypeService codetypeService;

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	MisMISLN20Service misMISLN20Service;
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	@Resource
	EmailClient email;

	@Resource
	L140M01ODao l140m01oDao;
	
	@Resource
	C900S02ADao c900s02aDao;
	
	@Resource
	C122M01ADao c122m01aDao;
	
	@Resource
	C340M01ADao c340m01aDao;
	
	@Override
	public String findRfavloan(String kindNo) {
		Map<String, Object> result = getJdbc().queryForMap(
				"MIS.ELAPPCON.getRfavloanSum", new String[] { kindNo });

		if (result == null || result.size() == 0)
			return "0";
		else
			return result.get("RFAV_LOAN").toString();
	}

	@Override
	public String findFavloan(String kindNo, String orctNo2) {
		Map<String, Object> result = getJdbc().queryForMap(
				"MIS.ELAPPCON.getFavloanSum", new String[] { kindNo, orctNo2 });

		if (result == null || result.size() == 0)
			return "0";
		else
			return result.get("FAV_LOAN").toString();
	}

	@Override
	public boolean saveElghtapp(String kindNo, String totapp) {
		if (Util.isEmpty(findElghtappByKindno(kindNo))) {// 沒資料=>insert
			getJdbc().queryForMap("MIS.ELGHTAPP.insert",
					new String[] { kindNo, totapp });
		} else {// 有資料=>update
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			getJdbc().update(
					"MIS.ELGHTAPP.update",
					new String[] { totapp, user.getUserId(),
							TWNDate.toFullAD(new Date()), kindNo });
		}
		return true;
	}

	@Override
	public String findElghtappByKindno(String kindNo) {
		Map<String, Object> result = getJdbc().queryForMap(
				"MIS.ELGHTAPP.getTotapp", new String[] { kindNo });

		if (result == null || result.size() == 0)
			return "0";
		else
			return result.get("TOTAPP").toString();
	}

	@Override
	public Map<String, Object> findTot(String kindNo, String type) {
		Map<String, Object> result = null;
		String query = "MIS." + type + ".MORTGAGE";
		if ("5".equals(kindNo)) {
			query += ".ALL";
			result = getJdbc().queryForMap(query, null);
		} else {
			result = getJdbc().queryForMap(query, new String[] { kindNo });
		}
		return result;
	}
	
	private Map<String, Object> findMailServicePart1(String kindNo) {
		Map<String, Object> result = getJdbc().queryForMap("MIS.MAILSERVICE.part1", new String[] { kindNo });
		return result;
	}

	private Map<String, Object> findMailServicePart2(String kindNo, String version) {		
		Map<String, Object> r = new HashMap<String, Object>();
		
		BigDecimal num = new BigDecimal("0");
		BigDecimal amt = new BigDecimal("0");
		
		for(Map<String, Object> row: eloandbBaseService.findMailServiceProd59_part2_list(kindNo, version)){
			String cntrNo = Util.trim(row.get("CNTRNO"));
			BigDecimal loanAmt = Util.parseBigDecimal(row.get("LOANAMT"));
			if(Util.isEmpty(cntrNo)){
				continue;
			}			
			List<Map<String, Object>> lnf030List = this.getJdbc().queryForList(
						"MIS.MAILSERVICE.part2_LNF030",
						new Object[] { cntrNo, kindNo});
			if(lnf030List.size()==0){//排除已撥款
				num = num.add(BigDecimal.ONE);
				amt = amt.add(loanAmt);
			}
		}
		r.put("NUM", num);
		r.put("AMT", amt);
		return r;
	}
	
	private List<Map<String, Object>> find_l140s02f_ratePlan20_aloan() {		
		//ratePlan20_aloan 在 2019-07-26 開辦
		return getJdbc().queryForListWithMax("MIS.Mail_RatePlan_20_summary", new String[] { "2019-07-26", "9999-12-31" });
	}
	
	
	/** 可參考 青安產品59 分為[已撥款、已核准未撥款、簽案中]	 	
	 	但做法與 青安產品59  不同
	 	~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		若僅用 L140M03A.isUse!='Y', 若客戶已動審, 但拖N個月才撥款的話 => 青安產品59 不會呈現這部分的數字(有缺漏)
		 
 		但 J-108-0208 自住型房貸成長方案 
 		因為要綁一個總戶序號, 可以去抓 C900M01G.lnf_loan_date
	 * @return
	 */
	private List<Map<String, Object>> find_l140s02f_ratePlan20_eloan() {		
		return eloandbBaseService.findMailService_l140s02f_ratePlan20();
	}
	
	@Override
	public boolean sendMail(String kind, String version, Map<String, String> paramMap) {
		Date sysDate = CapDate.getCurrentTimestamp();		
		boolean changeToLNF13E = true;
//		logger.debug("sendMail[kind="+kind+"][version="+version+"]"+paramMap);
		
		if(Util.equals(kind, "l140s02f_ratePlan20")){
			boolean can_skip = false;
			if(Util.equals("Y", MapUtils.getString(paramMap, "forceRun")) || Util.equals("01", Util.getRightStr(TWNDate.toAD(sysDate), 2))){ //每月1日
				
			}else{
				//營業日才跑
				boolean is_bussDate = MapUtils.isNotEmpty(misdbBASEService.get_LNF320(TWNDate.toAD(sysDate)));
				if(!is_bussDate){
					can_skip = true;
				}
			}
			
			if(can_skip){
				return true;
			}
		}
		
		/*
		select * from com.bcodetype where codetype in (
			'ratePlan16_mailSites' ,
			'importId55555_mailSites' ,
			'lms9541v02_mailSites' )
			
		【SLMS-00046】 http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'MailServiceImpl','request':{kindNo:'l140s02a_importId11111_22222','version':'@1'}}
		
		【SLMS-00049】 http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'MailServiceImpl','request':{kindNo:'l140s02a_importId55555','version':'@1'}}
		
		【SLMS-00066】 http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'MailServiceImpl','request':{kindNo:'l140s02a_importId66666_77777','version':'@1'}}
		*/
		if(Util.equals(kind, "l140s02a_importId11111_22222")){
			
			Date dataEndDate = CapDate.shiftDays(sysDate, -1);
			String str_dataEndDate = TWNDate.toAD(dataEndDate);
			//================================
			//把資料由 a-Loan 抓到 e-Loan	
			if(true){
				Date beginDate = null;
				if(true){
					Date existEloanDate = c900s02aDao.findMaxTxnDate("11111");
					if(existEloanDate==null){
						beginDate = CapDate.parseDate("2017-08-15");	
					}else{				
						int add_days = 1;
						beginDate = CapDate.shiftDays(existEloanDate, add_days);
					}	
				}
				String str_dataBegDate = TWNDate.toAD(beginDate);
				write_to_lms_C900S02A(sysDate, version, str_dataBegDate, str_dataEndDate, "11111", "16", true, changeToLNF13E);
				write_to_lms_C900S02A(sysDate, version, str_dataBegDate, str_dataEndDate, "11111", "16", false, changeToLNF13E);	
			}
			
			if(true){
				Date beginDate = null;
				if(true){
					Date existEloanDate = c900s02aDao.findMaxTxnDate("22222");
					if(existEloanDate==null){
						beginDate = CapDate.parseDate("2017-08-15");	
					}else{				
						int add_days = 1;
						beginDate = CapDate.shiftDays(existEloanDate, add_days);
					}	
				}
				String str_dataBegDate = TWNDate.toAD(beginDate);
				write_to_lms_C900S02A(sysDate, version, str_dataBegDate, str_dataEndDate, "22222", "16", true, changeToLNF13E);
				write_to_lms_C900S02A(sysDate, version, str_dataBegDate, str_dataEndDate, "22222", "16", false, changeToLNF13E);
			}
			return sendMail_ratePlan16(kind, version, str_dataEndDate);
		}else if(Util.equals(kind, "l140s02a_importId55555")){
			Date dataEndDate = CapDate.shiftDays(sysDate, -1);
			String str_dataEndDate = TWNDate.toAD(dataEndDate);
			//================================
			//把資料由 a-Loan 抓到 e-Loan	
			if(true){
				Date beginDate = null;
				if(true){
					Date existEloanDate = c900s02aDao.findMaxTxnDate("55555");
					if(existEloanDate==null){
						beginDate = CapDate.parseDate("2017-10-06");	
					}else{				
						int add_days = 1;
						beginDate = CapDate.shiftDays(existEloanDate, add_days);
					}	
				}
				String str_dataBegDate = TWNDate.toAD(beginDate);
				write_to_lms_C900S02A(sysDate, version, str_dataBegDate, str_dataEndDate, "55555", "17", true, changeToLNF13E);
				write_to_lms_C900S02A(sysDate, version, str_dataBegDate, str_dataEndDate, "55555", "17", false, changeToLNF13E);
			}
			
			return sendMail_importId55555(kind, version, str_dataEndDate);
		}else if(Util.equals(kind, "l140s02a_importId66666_77777")){

			Date dataEndDate = CapDate.shiftDays(sysDate, -1);
			String str_dataEndDate = TWNDate.toAD(dataEndDate);
			//================================
			//把資料由 a-Loan 抓到 e-Loan
			String[] companyId_arr = {"66666", "77777", "80001", "80002", "80003", "80004", "80005", "80006"};			
			for(String companyId: companyId_arr){
				Date beginDate = null;
				if(true){
					Date existEloanDate = c900s02aDao.findMaxTxnDate(companyId);
					if(existEloanDate==null){
						beginDate = CapDate.parseDate("2019-09-23");	
					}else{				
						int add_days = 1;
						beginDate = CapDate.shiftDays(existEloanDate, add_days);
					}	
				}
				String str_dataBegDate = TWNDate.toAD(beginDate);
				write_to_lms_C900S02A(sysDate, version, str_dataBegDate, str_dataEndDate, companyId, "22", true, changeToLNF13E);
				write_to_lms_C900S02A(sysDate, version, str_dataBegDate, str_dataEndDate, companyId, "22", false, changeToLNF13E);	
			}
			
			return true;
		}else if(kind.startsWith("c122m01a_notifyT1")){
			int time_cnt = -1;
			String[] param_arr = StringUtils.split(kind, "-");
			if(param_arr!=null && param_arr.length>1){
				time_cnt = Util.parseInt(param_arr[1]);
			}
			if(time_cnt<0){
				time_cnt = 0;
			}
			return sendMail_c122m01a_notifyT1(version, time_cnt); 
		}else if(kind.startsWith("ploan005_notify_IT")){
				int time_cnt = -1;
				String[] param_arr = StringUtils.split(kind, "-");
				if(param_arr!=null && param_arr.length>1){
					time_cnt = Util.parseInt(param_arr[1]);
				}
				if(time_cnt<0){
					time_cnt = 0;
				}
				return sendMail_ploan005_notify_IT(version, time_cnt); 
		}else if(Util.equals(kind, "l140s02f_ratePlan20")){
			Date dataEndDate = CapDate.shiftDays(sysDate, -1);
			String str_dataEndDate = TWNDate.toAD(dataEndDate);
			
			return sendMail_ratePlan20(kind, version, str_dataEndDate);
		}else if(kind.startsWith("ploan015_notify_IT")){
			int time_cnt = -1;
			String[] param_arr = StringUtils.split(kind, "-");
			if(param_arr!=null && param_arr.length>1){
				time_cnt = Util.parseInt(param_arr[1]);
			}
			if(time_cnt<0){
				time_cnt = 0;
			}
			return sendMail_ploan015_notify_IT(version, time_cnt); 
	}else{
			return sendMail_elf431_relate_prodKind(kind, version);	
		}		
	}

	private boolean is_test(String version){
		return version.startsWith("@");
	}

	/**
		本來 cateKey 是【抓data】、【統計】的主要依據
		但 隨著業務邏輯的變更｛房仲代號55555 對應利率方案由17→16｝, 此欄位只剩下【抓data】的作用, 已失去【統計】作用
		● 【抓data】用於去抓出同一份簽案中，承做 本行的房仲利率方案{16或17}時，也一併承做的 {06青年安家購屋優惠貸款 , 09一價到底, ...} 資料
					=> 這類資料, 可能分行不會輸入房仲代號, 但主政單位也要看【來源為 房仲引介 的總金額】
		● 【統計】要改用【companyId5 + ratePlan】去計算		
	*/
	private String c900s02a_cateKey(String companyId5, String m_rateKind
			, boolean isEqRatePlan, String flag){
		if(Util.equals("11111", companyId5)){
			//組出類別 [1-16M , 1-16S , 1-OTH]
			if(isEqRatePlan){
				return ("1-"+m_rateKind+(flag));
			}else{
				return "1-OTH";
			}
		}else if(Util.equals("22222", companyId5)){
			//組出類別 [2-16M , 2-16S , 2-OTH]
			if(isEqRatePlan){
				return ("2-"+m_rateKind+(flag));
			}else{
				return "2-OTH";
			}
		}else if(Util.equals("55555", companyId5)){
			//組出類別 [5-17M , 5-17S , 5-OTH]
			if(isEqRatePlan){
				return ("5-"+m_rateKind+(flag));
			}else{
				return "5-OTH";
			}
		}else if(Util.equals("66666", companyId5)){
			//組出類別 [6-22M , 6-22S , 6-OTH]
			if(isEqRatePlan){
				return ("6-"+m_rateKind+(flag));
			}else{
				return "6-OTH";
			}
		}else if(Util.equals("77777", companyId5)){
			//組出類別 [7-22M , 7-22S , 7-OTH]
			if(isEqRatePlan){
				return ("7-"+m_rateKind+(flag));
			}else{
				return "7-OTH";
			}
		}else if(Util.equals("80001", companyId5)){
			//組出類別 [8122M , 8122S , 81OTH]
			if(isEqRatePlan){
				return ("81"+m_rateKind+(flag));
			}else{
				return "81OTH";
			}
		}else if(Util.equals("80002", companyId5)){
			if(isEqRatePlan){
				return ("82"+m_rateKind+(flag));
			}else{
				return "82OTH";
			}
		}else if(Util.equals("80003", companyId5)){
			if(isEqRatePlan){
				return ("83"+m_rateKind+(flag));
			}else{
				return "83OTH";
			}
		}else if(Util.equals("80004", companyId5)){
			if(isEqRatePlan){
				return ("84"+m_rateKind+(flag));
			}else{
				return "84OTH";
			}
		}else if(Util.equals("80005", companyId5)){
			if(isEqRatePlan){
				return ("85"+m_rateKind+(flag));
			}else{
				return "85OTH";
			}
		}else if(Util.equals("80006", companyId5)){
			if(isEqRatePlan){
				return ("86"+m_rateKind+(flag));
			}else{
				return "86OTH";
			}
		}else{
			return "";
		}
	} 
	
	private String fetch_l140m01o_locationCd(String custId, String dupNo, String cntrNo){
		//用 l140m01o 去補
		Map<String, Object> l140map = eloandbBaseService.findCLSCaseByCutIdAndCntrNoOrdByUpdateTime(custId, dupNo, cntrNo);
		String l140m01a_mainId = Util.trim(MapUtils.getString(l140map, "MAINID"));
		if(Util.isNotEmpty(l140m01a_mainId)){
			for(L140M01O l140m01o : l140m01oDao.findByMainIdOrderByCcollTyp1(l140m01a_mainId)){
				String cms_oid = l140m01o.getCmsOid();
				if(Util.isNotEmpty(cms_oid)){
					Map<String, Object> c100map = eloandbcmsBASEService.getC100M01ByOid(cms_oid);
					String cms_mainId = Util.trim(MapUtils.getString(c100map, "MAINID"));
					if(Util.isNotEmpty(cms_mainId)){
						for(Map<String, Object> c101m04 : eloandbcmsBASEService.getBuildingByMainId(cms_mainId)){
							String AREAID = Util.trim(MapUtils.getString(c101m04, "AREAID"));
							if(Util.isNotEmpty(AREAID)){
								return AREAID;
							}
						}
					}	
				}
			}					
		}
		return "";
	}
	
	/**
	TODO 以[房仲代號]為劃分的依據	
	stepA 先去抓[房仲代號=OOO][RatePlan=△△△] 的data
	stepB 用 stepA 抓出的 LNF020_DOCUMENT_NO 去找出同一份簽報書下的其它房貸帳號【例如：同時簽2個利率方案, 1個利率方案是{16或17}, 另1個利率方案=06青年安家購屋優惠貸款】
	stepC 再去抓[房仲代號=OOO][RatePlan!=△△△] 的data
		● 若資料已  exist in C900S02A => 表示在 stepB 就先抓出, 不重複寫入 C900S02A
		● 若資料 not exist in C900S02A => 有透過「房仲進件」但沒有做  本行的房仲利率方案{16或17} , 就單純只有承做 {06青年安家購屋優惠貸款 , 09一價到底, ...} 或 {科目=503的房貸副擔保}
	~~~~~~~~~~~~~
	最原始的需求, 出表的 data 要分三類
	●stepA 對應的 cateKey=M
	●stepB 對應的 cateKey=S
	●stepC 對應的 cateKey=OTH
	~~~~~~~~~~~~~	
	但在房貸利率方案整併之後, 停用17, 原本的住商引介55555, 改成輸入16 => 依上述的 logic, 住商引介的case 都會分類到  "5-OT"
	隨著業務邏輯的變更｛房仲代號55555 對應利率方案由17→16｝, 出表統計的條件, 已經沒有用到 C900S02A.cateKey
	========================================================================================	
				 先跑 func_J_106_0170_sql_1A_eq_ratePlan(...)												AND LNF033_COMPANY_ID  in (?) AND (LNF033_RATE_PLAN like '16%' and LNF033_RATE_PLAN !='')
				 ● 寫入 cateKey=M 利率方案={16或17}
				 ● 其 mainId like 'XXXXX-1'  
				 
				 再以 LNF020_DOCUMENT_NO 去查出「同一簽報書」項下的所有額度 
				 針對上述步驟抓出的所有額度, 去跑 func_J_106_0170_sql_1B(beginDate, endDate, cntrNo)
				。若[loanNo, txnDate, solarDate, txnTime]的組合, 之前已被寫入C900S02A(cateKey=M)則略過
				。其它的 loanNo 寫入：cateKey=S 【例如：利率方案=06青年安家購屋優惠貸款】
					● 例如1額度2個帳號, 其房貸利率方案分別是 16及06 => 利率方案=06 的cateKey會被寫入S
					● 簽報書下另外的房貸額度, 也寫入S
					● 其 mainId like 'XXXXX-2'
				。當 isEqRatePlan==true  時, c900s02a_cateKey(String companyId5, String m_rateKind, boolean isEqRatePlan, String flag) 寫入的 cateKey 共有 M, S 這兩類
				======================
				。當 isEqRatePlan==false 時, c900s02a_cateKey(String companyId5, String m_rateKind, boolean isEqRatePlan, String flag) 寫入的 cateKey 只有 OTH 這一類
				　⇒  表示房仲引介的案件, 完全沒有承做利率方案={16或17} , 就單純只有承做 {06青年安家購屋優惠貸款 , 09一價到底, ...} 而已
				          但還是要顯示, 透過「房仲進件」的資料
				          	
				 執行 func_J_106_0170_sql_1A_ne_ratePlan(...)												AND LNF033_COMPANY_ID  in (?) AND (LNF033_RATE_PLAN like '%' and LNF033_RATE_PLAN !='16')
				 ● 寫入：cateKey=OTH{房仲引介,但利率方案非16,17}
				 ● 其 mainId like 'XXXXX-1'
				 再跑 func_J_106_0170_sql_1B(beginDate, endDate, cntrNo), 抓同一 docNo 下的額度
				 ● 寫入：cateKey=OTH
				 ● 其 mainId like 'XXXXX-2' ========⇒  select * from LMS.C900S02A where cateKey like '%OTH%' and mainid like 'CLS%ne%2%'
				 ● 例如：房貸不夠的部分, 另申請一個額度, 科目=長放
				 
				 原本一開始, 要呈現 M+S, OTH 各有多少筆
				 執行 J_106_0170_sql_3 [1-16M, 1-16S, 1-OTH]把資料分成 [1-16, 1-OT]
				 但是, 後來在 sendMail_xx(...) 只有呈現  MS+O 的合計後結果
	======================================================================================== 
	 */
	private void write_to_lms_C900S02A(Date sysDate, String version
			, String str_dataBegDate, String str_dataEndDate
			, String companyId5, String m_rateKind, boolean isEqRatePlan, boolean changeToLNF13E){
		
		String mainId_prefix = "CLS_"+TWNDate.toAD(sysDate)+"_"+companyId5+"_"+(isEqRatePlan?"eq":"ne")+"_"+m_rateKind;
		List<C900S02A> added_list = new ArrayList<C900S02A>();
		if(true){
			String mainId = mainId_prefix+"_1";
		
			List<Map<String, Object>> mis_list = new ArrayList<Map<String, Object>>();
			if(isEqRatePlan){
				mis_list = misdbBASEService.func_J_106_0170_sql_1A_eq_ratePlan(
						str_dataBegDate, str_dataEndDate, companyId5, m_rateKind, changeToLNF13E);
			}else{
				mis_list = misdbBASEService.func_J_106_0170_sql_1A_ne_ratePlan(
						str_dataBegDate, str_dataEndDate, companyId5, m_rateKind, changeToLNF13E);
			}
			
			for(Map<String, Object> m : mis_list){				
	//			String brNo = Util.trim(m.get("LNF033_BR_NO")); 
				String loanNo = Util.trim(m.get("LNF033_LOAN_NO")); 
				String ratePlan = Util.trim(m.get("LNF033_RATE_PLAN")); 
				String prodPlan = Util.trim(m.get("LNF033_PROD_PLAN"));			
				String prodKind = Util.trim(m.get("LNF030_LOAN_CLASS")); 
				Date txnDate = (Date)m.get("LNF090_TXN_DATE"); 
				String txnTime = Util.trim(m.get("LNF090_TXN_TIME"));	
				String idDup = Util.trim(m.get("LNF090_CUST_ID"));						 
				String swft = Util.trim(m.get("LNF090_SWFT"));
				BigDecimal txnAmt = (BigDecimal)m.get("LNF090_TXN_AMT");    
				String cntrNo = Util.trim(m.get("LNF090_CONTRACT")); 
				Date solarDate = (Date)m.get("LNF090_SOLAR_DATE");
				String locationCd = Util.trim(m.get("LNF087_LOCATION_CD"));
				 
				String brNo = Util.trim(StringUtils.substring(loanNo, 0, 3));
				String lnap3 = Util.trim(StringUtils.substring(loanNo, 4, 7));
				String custId = Util.trim(StringUtils.substring(idDup, 0, 10));
				String dupNo = Util.trim(StringUtils.substring(idDup, 10, 11));
				/*
				 * P-RATE-AMT16
				 * P-RATE-AMT16-TOT
				 */
				String S136_O_HOUSE_FLAG = LNSR136(prodKind, lnap3);
				/*
				 * 仿 LNBM169 PERFORM  7600-CALL-LNSR136 
				 */
				if(Util.equals("N", S136_O_HOUSE_FLAG)){
					continue;
				}
	
				C900S02A existModel = c900s02aDao.findLNF090_UK(loanNo, txnDate, solarDate, txnTime);
				if(existModel!=null){
					logger.debug("repeat_UK_c900s02a{"+loanNo
							+","+ TWNDate.toAD(txnDate)
							+","+ TWNDate.toAD(solarDate)
							+","+ txnTime+"}");
					continue;
				}
	
				String docNo = "";
				Map<String, Object> lnf020_map = misMISLN20Service.findByCntrNo(cntrNo);
				if(true){
					docNo = Util.trim(lnf020_map.get("LNF020_DOCUMENT_NO")); 
				}
				
				if(Util.isEmpty(locationCd) || Util.equals("00", locationCd)){
					//用 l140m01o 去補
					locationCd = fetch_l140m01o_locationCd(custId, dupNo, cntrNo);
				}
				
				C900S02A model = new C900S02A();
				model.setMainId(mainId);
				model.setBrNo(brNo);
				model.setCntrNo(cntrNo);
				model.setProdKind(prodKind);
				model.setLoanNo(loanNo);
				model.setRatePlan(ratePlan);
				model.setProdPlan(prodPlan);
				model.setCompanyId5(companyId5);
				model.setCateKey(c900s02a_cateKey(companyId5, m_rateKind, isEqRatePlan, "M"));
				model.setTxnDate(txnDate);
				model.setTxnTime(txnTime);
				model.setSolarDate(solarDate);
				model.setSwft(swft);
				model.setTxnAmt(txnAmt);
				model.setLocationCd(locationCd);
				model.setDocNo(docNo);
				model.setCustId(custId);
				model.setDupNo(dupNo);
				//========
				added_list.add(model);				
			}
			c900s02aDao.save(added_list);
		}
		
		if(true){
			String mainId = mainId_prefix+"_2";
			
			Set<String> docNo_set = new HashSet<String>();
			for(C900S02A c900s02a: added_list){
				String docNo = Util.trim(c900s02a.getDocNo());
				if(Util.isNotEmpty(docNo)){
					docNo_set.add(docNo);
				}
			}
			
			for(String docNo : docNo_set){
				Set<String> all_cntrNo_set = new HashSet<String>();
				for(MISLN20 misln20 : misMISLN20Service.query_LNF020_DOCUMENT_NO(docNo)){
					all_cntrNo_set.add(Util.trim(misln20.getLnf020_contract()));
				}
				
				for(String q_cntrNo : all_cntrNo_set){
					for(Map<String, Object> m :misdbBASEService.func_J_106_0170_sql_1B(str_dataBegDate, str_dataEndDate, q_cntrNo)){
//						String brNo = Util.trim(m.get("LNF033_BR_NO")); 
						String loanNo = Util.trim(m.get("LNF033_LOAN_NO")); 
						String ratePlan = Util.trim(m.get("LNF033_RATE_PLAN")); 
						String prodPlan = Util.trim(m.get("LNF033_PROD_PLAN"));			
						String prodKind = Util.trim(m.get("LNF030_LOAN_CLASS")); 
						Date txnDate = (Date)m.get("LNF090_TXN_DATE"); 
						String txnTime = Util.trim(m.get("LNF090_TXN_TIME"));	
						String idDup = Util.trim(m.get("LNF090_CUST_ID"));						 
						String swft = Util.trim(m.get("LNF090_SWFT"));
						BigDecimal txnAmt = (BigDecimal)m.get("LNF090_TXN_AMT");    
						String cntrNo = Util.trim(m.get("LNF090_CONTRACT")); 
						Date solarDate = (Date)m.get("LNF090_SOLAR_DATE");
						String locationCd = Util.trim(m.get("LNF087_LOCATION_CD"));
						 
						String brNo = Util.trim(StringUtils.substring(loanNo, 0, 3));
//						String lnap3 = Util.trim(StringUtils.substring(loanNo, 4, 7));
						String custId = Util.trim(StringUtils.substring(idDup, 0, 10));
						String dupNo = Util.trim(StringUtils.substring(idDup, 10, 11));
						
						C900S02A existModel = c900s02aDao.findLNF090_UK(loanNo, txnDate, solarDate, txnTime);
						if(existModel!=null){
							logger.debug("repeat_UK_c900s02a{"+loanNo
									+","+ TWNDate.toAD(txnDate)
									+","+ TWNDate.toAD(solarDate)
									+","+ txnTime+"}");
							continue;
						}

						if(Util.isEmpty(locationCd) || Util.equals("00", locationCd)){
							//用 l140m01o 去補
							locationCd = fetch_l140m01o_locationCd(custId, dupNo, cntrNo);
						}
						
						C900S02A model = new C900S02A();
						model.setMainId(mainId);
						model.setBrNo(brNo);
						model.setCntrNo(cntrNo);
						model.setProdKind(prodKind);
						model.setLoanNo(loanNo);
						model.setRatePlan(ratePlan);
						model.setProdPlan(prodPlan);
						model.setCompanyId5(companyId5);							
						model.setCateKey(c900s02a_cateKey(companyId5, m_rateKind, isEqRatePlan, "S"));
						model.setTxnDate(txnDate);
						model.setTxnTime(txnTime);
						model.setSolarDate(solarDate);
						model.setSwft(swft);
						model.setTxnAmt(txnAmt);
						model.setLocationCd(locationCd);
						model.setDocNo(docNo);
						model.setCustId(custId);
						model.setDupNo(dupNo);
						//======
						c900s02aDao.save(model);					
					}				
				}
			}	
		}			
	}
	
	private void _add_sum_area_companyId__BigDecimal(Map<String,BigDecimal> map, String key, BigDecimal val){
		if(map.containsKey(key)){
			map.put(key, map.get(key).add(val));
		}else{
			map.put(key, val);
		}
	}
	
	private boolean sendMail_ratePlan16(String kind, String version, String str_dataEndDate) {
		try {
			
			/** 組Html */
			List<CodeType> mailList = codetypeService
					.findByCodeTypeList("ratePlan16_mailSites");
			String[] site = new String[mailList.size()];
			for (int i = 0; i < mailList.size(); i++) {
				CodeType sendData = mailList.get(i);
				//20130805修正 只用codeDesc放Email(codeDesc2放姓名)
				site[i] = sendData.getCodeDesc();
			}
			
			String str_dataEndDate_yyyyMM = StringUtils.substring(str_dataEndDate, 0, 7);
			
			//================================
			
			Map<String, String> import_companId_map = new HashMap<String, String>();
			if(true){
				import_companId_map.put("11111", "11111-永慶房屋");
				import_companId_map.put("22222", "22222-信義房屋");
			}			
			
			Map<String, String> brSetNo_map = new HashMap<String, String>();			
			if(true){
				brSetNo_map.put("931", "北區");
				brSetNo_map.put("932", "北二區");
				brSetNo_map.put("933", "桃竹苗");
				brSetNo_map.put("934", "中　區");
				brSetNo_map.put("935", "南　區");
				brSetNo_map.put("007", "國外部");
				brSetNo_map.put("201", "金控總部");
			}
			//================================
			
			Properties prop = MessageBundleScriptCreator
									.getComponentResource(CLS9021V00Page.class);
			
			StringBuffer strBuf = new StringBuffer();
			String env = "";
			if(is_test(version)){
				env = "【測試】";
			}
			String subject = env + prop.getProperty("subject_ratePlan16");
			
			//title
			strBuf.append(MessageFormat.format(prop.getProperty("title_ratePlan16"), subject));
			
			//截至 yyyy-MM-dd 止
			
			strBuf.append(MessageFormat.format(prop.getProperty("title_ratePlan16")
					, "截至 "+str_dataEndDate+" 止"));
			
			

			//============================
			//part 1
			if(true){	
				//單位：新台幣仟元
				strBuf.append(prop.getProperty("unit_ratePlan16"));
				
				//J-107-0119 永慶信義100億專案 變更 為永慶信義200億專案
				String[] th = {"16-優質房仲方案"
						, str_dataEndDate_yyyyMM+"筆數"
						, str_dataEndDate_yyyyMM+"金額"
						, "累計筆數"
						, "累計金額"};
				
				
				strBuf.append(prop.getProperty("table_beg_ratePlan16"));
				
				strBuf.append(MessageFormat.format(prop.getProperty("table_th_ratePlan16_part1")
						, th[0], th[1], th[2], th[3], th[4]));
				
				//~~~
				if(true){
					String[] companyId5_arr = {"11111", "22222"};
					String td_pattern = prop.getProperty("table_td_ratePlan16_part1");
					
					BigDecimal sum_mm_cnt = BigDecimal.ZERO;
					BigDecimal sum_mm_amt = BigDecimal.ZERO;
					BigDecimal sum_time_cnt = BigDecimal.ZERO;
					BigDecimal sum_time_amt = BigDecimal.ZERO;
					BigDecimal unit = BigDecimal.ONE; //new BigDecimal("1000");
					int scale = 0;
					
					for(String companyId : companyId5_arr){
						Map<String, Object> map_mm = eloandbBaseService.findC900S02A_groupBy_ratePlan_companyId(
								str_dataEndDate_yyyyMM+"-01", str_dataEndDate, "16", companyId);
						Map<String, Object> map_time = eloandbBaseService.findC900S02A_groupBy_ratePlan_companyId(
								"1911-01-01", "9999-12-31", "16", companyId);
						
						//月初, 當月可能尚無資料 => CrsUtil.parseBigDecimal(...)
						BigDecimal mm_cnt = CrsUtil.parseBigDecimal( MapUtils.getString(map_mm, "CNT"));
						BigDecimal mm_amt = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_mm, "AMT"));
						
						BigDecimal time_cnt = CrsUtil.parseBigDecimal( MapUtils.getString(map_time, "CNT"));
						BigDecimal time_amt = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_time, "AMT"));
						
						if(true){
							//轉換為千元單位
							mm_amt = Arithmetic.div(mm_amt, unit, scale);
							time_amt = Arithmetic.div(time_amt, unit, scale);	
						}
						if(true){
							sum_mm_cnt = sum_mm_cnt.add(mm_cnt);
							sum_mm_amt = sum_mm_amt.add(mm_amt);
							sum_time_cnt = sum_time_cnt.add(time_cnt);
							sum_time_amt = sum_time_amt.add(time_amt);
						}
						//===========
						strBuf.append(MessageFormat.format(td_pattern 
								, "合計 "+ LMSUtil.getDesc(import_companId_map, companyId)
								, NumConverter.addComma(mm_cnt), NumConverter.addComma(mm_amt)
								, NumConverter.addComma(time_cnt), NumConverter.addComma(time_amt)));
					}			
					
					strBuf.append(MessageFormat.format(prop.getProperty("table_tdSum_ratePlan16_part1") 
							, "總計 "
							, NumConverter.addComma(sum_mm_cnt), NumConverter.addComma(sum_mm_amt)
							, NumConverter.addComma(sum_time_cnt), NumConverter.addComma(sum_time_amt)));
				}
				
				strBuf.append(prop.getProperty("table_end_ratePlan16"));
			
			}
			strBuf.append("&nbsp;<br/>&nbsp;<br/>");
			//============================
			//part 2
			if(true){	
				//單位：新台幣仟元
				strBuf.append(prop.getProperty("unit_ratePlan16"));
				
				String[] th = {"歸屬單位"
						, "分行別"
						,"仲介公司"			
						, str_dataEndDate_yyyyMM+"筆數"
						, str_dataEndDate_yyyyMM+"金額"
						, "累計筆數"
						, "累計金額"};
				
				
				strBuf.append(prop.getProperty("table_beg_ratePlan16"));
				
				strBuf.append(MessageFormat.format(prop.getProperty("table_th_ratePlan16_part2")
						, th[0], th[1], th[2], th[3], th[4], th[5], th[6]
						));
				
				//~~~
				if(true){
					String[] companyId5_arr = {"11111", "22222"};
					String td_pattern = prop.getProperty("table_td_ratePlan16_part2");
					
					LinkedHashMap<String,BigDecimal> sum_area_companyId__mm_cnt_MS = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__mm_amt_MS = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__mm_cnt_O = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__mm_amt_O = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__time_cnt_MS = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__time_amt_MS = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__time_cnt_O = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__time_amt_O = new LinkedHashMap<String,BigDecimal>();
					BigDecimal unit = BigDecimal.ONE; //new BigDecimal("1000");
					int scale = 0;
					
					LinkedHashMap<String, Map<String, Object>> list_mm = new LinkedHashMap<String, Map<String, Object>>();
					LinkedHashMap<String, Map<String, Object>> list_time = new LinkedHashMap<String, Map<String, Object>>();
					for(String companyId : companyId5_arr){
												
						for(Map<String, Object> row_mm : eloandbBaseService.findC900S02A_groupBy_brNo(
								str_dataEndDate_yyyyMM+"-01", str_dataEndDate, companyId)){
							String key = Util.trim(row_mm.get("BRSETNO"))+"_"
												+Util.trim(row_mm.get("BRNO"))+"_"
												+Util.trim(row_mm.get("COMPANYID5"));												
							list_mm.put(key, row_mm);
						}
						
						for(Map<String, Object> row_time : eloandbBaseService.findC900S02A_groupBy_brNo(
								"1911-01-01", "9999-12-31", companyId)){
							String key = Util.trim(row_time.get("BRSETNO"))+"_"
											+Util.trim(row_time.get("BRNO"))+"_"
											+Util.trim(row_time.get("COMPANYID5"));
							list_time.put(key, row_time);
						}
					}	
					/*
					 依[11111,22222]的順序去抓資料
					 005BR 11111
					 229BR 11111
					 
					 005BR 22222
					 
					 => 調整呈現的 順序
					 	005BR 11111
					 	005BR 22222
					 	229BR 11111
					 
					 */
					TreeSet<String> define_key_set = new TreeSet<String>(list_time.keySet());					
					for(String area_companyId_brNo_key: define_key_set){
						Map<String, Object> map_mm = list_mm.get(area_companyId_brNo_key);
						Map<String, Object> map_time = list_time.get(area_companyId_brNo_key);
						
						String BRSETNO = Util.trim(MapUtils.getString(map_time, "BRSETNO")); 
						String BRNAME = Util.trim(MapUtils.getString(map_time, "BRNAME"));                                                          
						String BRNO = Util.trim(MapUtils.getString(map_time, "BRNO"));
						String COMPANYID5 = Util.trim(MapUtils.getString(map_time, "COMPANYID5")); 
						BigDecimal time_CNT_MS = CrsUtil.parseBigDecimal( MapUtils.getString(map_time, "CNT_MS"));      
						BigDecimal time_AMT_MS = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_time, "AMT_MS"));                             
						BigDecimal time_CNT_O = CrsUtil.parseBigDecimal( MapUtils.getString(map_time, "CNT_O"));       
						BigDecimal time_AMT_O = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_time, "AMT_O"));
						BigDecimal time_CNT_MSO = time_CNT_MS.add(time_CNT_O);
						BigDecimal time_AMT_MSO = null;
						//月初, 當月可能尚無資料 => CrsUtil.parseBigDecimal(...)
						BigDecimal mm_CNT_MS = CrsUtil.parseBigDecimal( MapUtils.getString(map_mm, "CNT_MS"));       
						BigDecimal mm_AMT_MS = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_mm, "AMT_MS"));
						BigDecimal mm_CNT_O = CrsUtil.parseBigDecimal( MapUtils.getString(map_mm, "CNT_O"));
						BigDecimal mm_AMT_O = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_mm, "AMT_O"));
						BigDecimal mm_CNT_MSO = mm_CNT_MS.add(mm_CNT_O);
						BigDecimal mm_AMT_MSO = null; 
						if(true){
							//轉換為千元單位
							time_AMT_MS = Arithmetic.div(time_AMT_MS, unit, scale);
							time_AMT_O = Arithmetic.div(time_AMT_O, unit, scale);	
							
							mm_AMT_MS = Arithmetic.div(mm_AMT_MS, unit, scale);
							mm_AMT_O = Arithmetic.div(mm_AMT_O, unit, scale);
							
							time_AMT_MSO = time_AMT_MS.add(time_AMT_O);
							mm_AMT_MSO = mm_AMT_MS.add(mm_AMT_O); 
						}
						if(true){
							String key = BRSETNO+"_"+COMPANYID5; 
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__mm_cnt_MS, key, mm_CNT_MS);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__mm_amt_MS, key, mm_AMT_MS);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__mm_cnt_O, key, mm_CNT_O);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__mm_amt_O, key, mm_AMT_O);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__time_cnt_MS, key, time_CNT_MS);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__time_amt_MS, key, time_AMT_MS);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__time_cnt_O, key, time_CNT_O);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__time_amt_O, key, time_AMT_O);
						}
						//===========
						strBuf.append(MessageFormat.format(td_pattern 
								, LMSUtil.getDesc(brSetNo_map, BRSETNO)
								, BRNO+"-"+BRNAME
								, LMSUtil.getDesc(import_companId_map, COMPANYID5)
								, NumConverter.addComma(mm_CNT_MSO), NumConverter.addComma(mm_AMT_MSO)
								, NumConverter.addComma(time_CNT_MSO), NumConverter.addComma(time_AMT_MSO)
								));
					}
					
					if(true){
						String tdSum_pattern = prop.getProperty("table_tdSum_ratePlan16_part2");
						/*
						 * 
						 */
						TreeSet<String> ts = new TreeSet<String>(sum_area_companyId__time_cnt_MS.keySet());
						
						//處理依[營運中心、companyId]合計的資料列
						if(true){
							for(String key_cols: ts){
								String[] keyArr = StringUtils.split(key_cols, "_");
								String BRSETNO = keyArr[0];
								String COMPANYID5 = keyArr[1];
								
								BigDecimal time_CNT_MS = sum_area_companyId__time_cnt_MS.get(key_cols);    
								BigDecimal time_AMT_MS = sum_area_companyId__time_amt_MS.get(key_cols);             
								BigDecimal time_CNT_O = sum_area_companyId__time_cnt_O.get(key_cols);
								BigDecimal time_AMT_O = sum_area_companyId__time_amt_O.get(key_cols);
								//當月資料
								BigDecimal mm_CNT_MS = sum_area_companyId__mm_cnt_MS.get(key_cols);     
								BigDecimal mm_AMT_MS = sum_area_companyId__mm_amt_MS.get(key_cols);
								BigDecimal mm_CNT_O = sum_area_companyId__mm_cnt_O.get(key_cols);
								BigDecimal mm_AMT_O = sum_area_companyId__mm_amt_O.get(key_cols);
								
								BigDecimal time_CNT_MSO = time_CNT_MS.add(time_CNT_O);
								BigDecimal mm_CNT_MSO = mm_CNT_MS.add(mm_CNT_O);
								
								BigDecimal time_AMT_MSO = time_AMT_MS.add(time_AMT_O);
								BigDecimal mm_AMT_MSO = mm_AMT_MS.add(mm_AMT_O); 
								
								strBuf.append(MessageFormat.format(tdSum_pattern 
										, LMSUtil.getDesc(brSetNo_map, BRSETNO)
										, ""
										, LMSUtil.getDesc(brSetNo_map, BRSETNO)+"合計"+LMSUtil.getDesc(import_companId_map, COMPANYID5)
										, NumConverter.addComma(mm_CNT_MSO), NumConverter.addComma(mm_AMT_MSO)
										, NumConverter.addComma(time_CNT_MSO), NumConverter.addComma(time_AMT_MSO)
										));
							}
						}
						
						//處理依[]合計的資料列
						if(true){
							
							BigDecimal time_CNT_MS = BigDecimal.ZERO;      
							BigDecimal time_AMT_MS = BigDecimal.ZERO;             
							BigDecimal time_CNT_O = BigDecimal.ZERO;
							BigDecimal time_AMT_O = BigDecimal.ZERO;
							//當月資料
							BigDecimal mm_CNT_MS = BigDecimal.ZERO;     
							BigDecimal mm_AMT_MS = BigDecimal.ZERO;
							BigDecimal mm_CNT_O = BigDecimal.ZERO;
							BigDecimal mm_AMT_O = BigDecimal.ZERO;
							
							for(String key_cols: ts){
								time_CNT_MS = time_CNT_MS.add( sum_area_companyId__time_cnt_MS.get(key_cols) );      
								time_AMT_MS = time_AMT_MS.add( sum_area_companyId__time_amt_MS.get(key_cols) );             
								time_CNT_O = time_CNT_O.add( sum_area_companyId__time_cnt_O.get(key_cols) );
								time_AMT_O = time_AMT_O.add( sum_area_companyId__time_amt_O.get(key_cols) );
								//當月資料
								mm_CNT_MS = mm_CNT_MS.add( sum_area_companyId__mm_cnt_MS.get(key_cols) );     
								mm_AMT_MS = mm_AMT_MS.add( sum_area_companyId__mm_amt_MS.get(key_cols) );
								mm_CNT_O = mm_CNT_O.add( sum_area_companyId__mm_cnt_O.get(key_cols) );
								mm_AMT_O = mm_AMT_O.add( sum_area_companyId__mm_amt_O.get(key_cols) );							
							}
							
							BigDecimal time_CNT_MSO = time_CNT_MS.add(time_CNT_O);
							BigDecimal mm_CNT_MSO = mm_CNT_MS.add(mm_CNT_O);
							
							BigDecimal time_AMT_MSO = time_AMT_MS.add(time_AMT_O);
							BigDecimal mm_AMT_MSO = mm_AMT_MS.add(mm_AMT_O); 
							
							strBuf.append(MessageFormat.format(tdSum_pattern 
									, ""
									, ""
									, "總計"
									, NumConverter.addComma(mm_CNT_MSO), NumConverter.addComma(mm_AMT_MSO)
									, NumConverter.addComma(time_CNT_MSO), NumConverter.addComma(time_AMT_MSO)));
						}
						
					}
				}
				
				strBuf.append(prop.getProperty("table_end_ratePlan16"));
			
			}
			/** 發送 */
			
			logger.info("mail_html_format[\n==============\n"+strBuf.toString()+"\n==============]");
			
			//20130805修正寄出仍為Text格式
			email.send(false, null, site, subject, strBuf.toString());
			return true;

		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
			return false;
		}
	}

	private boolean sendMail_ratePlan20(String kind, String version, String str_dataEndDate) {
		try {
			Properties prop_CLS9021V00Page = MessageBundleScriptCreator.getComponentResource(CLS9021V00Page.class);
				
			StringBuffer strBuf = new StringBuffer();
			String env = "";
			if(is_test(version)){
				env = "【測試】";
			}
			String subject = env + prop_CLS9021V00Page.getProperty("subject_l140s02f_ratePlan20");
			
			//title
			strBuf.append(MessageFormat.format(prop_CLS9021V00Page.getProperty("title_l140s02f_ratePlan20"), subject));
			
			//截至 yyyy-MM-dd 止			
			strBuf.append(MessageFormat.format(prop_CLS9021V00Page.getProperty("title_l140s02f_ratePlan20"), "截至 "+str_dataEndDate+" 止"));
			strBuf.append(prop_CLS9021V00Page.getProperty("unit_l140s02f_ratePlan20"));
			BigDecimal base_unit = Util.parseBigDecimal("100000000");// 億元
			int scaleNum = 4;
			//===============
			List<String> mailList = new ArrayList<String>(_mailList("ratePlan20_mailSites"));		
			String[] site = mailList.toArray(new String[mailList.size()]);
			//===============			
			strBuf.append(prop_CLS9021V00Page.getProperty("table_beg_l140s02f_ratePlan20"));
			if(true){
				strBuf.append(MessageFormat.format(prop_CLS9021V00Page.getProperty("table_th_l140s02f_ratePlan20"),"母戶戶號", "分項"
						, "動用期限"	, "申請總額度", "佔自住型房貸成長方案", "預約期限"
						, prop_CLS9021V00Page.getProperty("labe_ll140s02f_ratePlan20_tot")
						, prop_CLS9021V00Page.getProperty("labe_ll140s02f_ratePlan20_aloan"), prop_CLS9021V00Page.getProperty("labe_ll140s02f_ratePlan20_eloan_approved"), prop_CLS9021V00Page.getProperty("labe_ll140s02f_ratePlan20_eloan_edit")));
				BigDecimal sum_tot = BigDecimal.ZERO;
				BigDecimal sum_aloan = BigDecimal.ZERO;
				BigDecimal sum_eloan_approved = BigDecimal.ZERO;
				BigDecimal sum_eloan_edit = BigDecimal.ZERO;
				if(true){
					Map<String, BigDecimal> map_aloan = new HashMap<String, BigDecimal>();
					Map<String, BigDecimal> map_eloan_approved = new HashMap<String, BigDecimal>();
					Map<String, BigDecimal> map_eloan_edit = new HashMap<String, BigDecimal>();
					Map<String, CodeType> map_codeType = new HashMap<String, CodeType>();
					if(true){					
						/*
						  	問題處理經驗
						  		額度序號 235111100124之前已撥款,分行於2022-06-07變更 房貸利率方案 由23優利房貸方案 -> 20自住型
						  		但未做L505-30團貸總戶額度序號
								導致SQL抓到的 LNF020_GRP_CNTRNO=空白
						  		=> 請分行執行 L505-30 後，批次即可正常執行
						*/
						List<Map<String, Object>> list_aloan = find_l140s02f_ratePlan20_aloan();	// LNF020_GRP_CNTRNO , LNF090_SWFT  , SUM_AMT				
						List<Map<String, Object>> list_eloan = find_l140s02f_ratePlan20_eloan();	// GRPCNTRNO , STAGE , SUM_AMT
						
						for(Map<String, Object> map: list_aloan){
							map_aloan.put(StringUtils.trim(MapUtils.getString(map, "LNF020_GRP_CNTRNO")), CrsUtil.parseBigDecimal(MapUtils.getObject(map, "SUM_AMT")));
						}
						for(Map<String, Object> map: list_eloan){
							String grpCntrNo = StringUtils.trim(MapUtils.getString(map, "GRPCNTRNO"));
							String stage = StringUtils.trim(MapUtils.getString(map, "STAGE"));
							BigDecimal sum_amt = CrsUtil.parseBigDecimal(MapUtils.getObject(map, "SUM_AMT"));
							if(Util.equals("S1", stage)){
								map_eloan_edit.put(grpCntrNo, sum_amt);
							}else if(Util.equals("S2", stage)){
								map_eloan_approved.put(grpCntrNo, sum_amt);
							}
						}
						

						List<CodeType> list_codeType = codetypeService.findByCodeTypeList("ratePlan20_grpCntrNo");
						for(CodeType codeType: list_codeType){
							String grpCntrNo = Util.trim(codeType.getCodeValue());
							map_codeType.put(grpCntrNo, codeType);
						}
					}
					
					TreeSet<String> ts_grpCntrNo = new TreeSet<String>(); 
					if(true){
						ts_grpCntrNo.addAll(map_aloan.keySet());
						ts_grpCntrNo.addAll(map_eloan_approved.keySet());
						ts_grpCntrNo.addAll(map_eloan_edit.keySet());
						ts_grpCntrNo.addAll(map_codeType.keySet());
					}
					//===========================================
					//918110899995 自住型房貸成長方案  排在第1個
					LinkedHashMap<String, PTEAMAPP> map_basic = new LinkedHashMap<String, PTEAMAPP>();
					if(true){
						if(true){
							String grpCntrNo = "918110899995";
							map_basic.put(grpCntrNo, misPTEAMAPPService.getDataByLnf020GrpCntrNo(grpCntrNo));
						}
						for(String grpCntrNo : ts_grpCntrNo){
							if(map_basic.containsKey(grpCntrNo)){
								continue;
							}
							map_basic.put(grpCntrNo, misPTEAMAPPService.getDataByLnf020GrpCntrNo(grpCntrNo));
						}
					}
					
					for(String grpCntrNo : map_basic.keySet()){
						PTEAMAPP pteamapp = map_basic.get(grpCntrNo);
						BigDecimal occupy_amt = null;
						Date occupy_date = null;
						if(Util.equals(grpCntrNo, "918110899995")){
							occupy_amt = _convert_amt_by_basicUnit(pteamapp.getTotamt(), base_unit, scaleNum);
							occupy_date = pteamapp.getEffend();
						}else{
							CodeType codeType =  map_codeType.get(grpCntrNo);
							if(codeType!=null){
								String codeDesc = codeType.getCodeDesc(); //借用此欄位當金額
//								String codeDesc2 = codeType.getCodeDesc2(); //借用此欄位當	預約期限(起)
								String codeDesc3 = codeType.getCodeDesc3(); //借用此欄位當	預約期限(迄)
								if(Util.isNotEmpty(Util.trim(codeDesc))){
									BigDecimal _amt = CrsUtil.parseBigDecimal(codeDesc);
									if(_amt!=null && _amt.compareTo(BigDecimal.ZERO)!=0){
										occupy_amt = _convert_amt_by_basicUnit(_amt, base_unit, scaleNum);
									}
								}
								if(Util.isNotEmpty(Util.trim(codeDesc3)) && CapDate.parseDate(codeDesc3)!=null){
									occupy_date = CapDate.parseDate(codeDesc3);
								}
							}
						}
						//~~~~~~~~~
						BigDecimal pteamapp_totamt = _convert_amt_by_basicUnit(pteamapp.getTotamt(), base_unit, scaleNum);
						BigDecimal _aloan = _convert_amt_by_basicUnit(map_aloan.containsKey(grpCntrNo)?map_aloan.get(grpCntrNo):BigDecimal.ZERO, base_unit, scaleNum);						
						BigDecimal _eloan_approved = _convert_amt_by_basicUnit(map_eloan_approved.containsKey(grpCntrNo)?map_eloan_approved.get(grpCntrNo):BigDecimal.ZERO, base_unit, scaleNum);
						BigDecimal _eloan_edit = _convert_amt_by_basicUnit(map_eloan_edit.containsKey(grpCntrNo)?map_eloan_edit.get(grpCntrNo):BigDecimal.ZERO, base_unit, scaleNum);						
						//~~~~~~~~~
						BigDecimal _tot = _aloan.add(_eloan_approved).add(_eloan_edit);
						sum_tot = sum_tot.add(_tot);
						sum_aloan = sum_aloan.add(_aloan);
						sum_eloan_approved = sum_eloan_approved.add(_eloan_approved);
						sum_eloan_edit = sum_eloan_edit.add(_eloan_edit);
						//~~~~~~~~~
						strBuf.append(MessageFormat.format(prop_CLS9021V00Page.getProperty("table_td_l140s02f_ratePlan20"), grpCntrNo, pteamapp.getProjectnm()
								, TWNDate.toAD(pteamapp.getEfffrom())+"~"+TWNDate.toAD(pteamapp.getEffend())
								, pretty_numStr_with_scaleNum(pteamapp_totamt)
								, pretty_numStr_with_scaleNum(occupy_amt)
								, (occupy_date==null?"-":TWNDate.toAD(occupy_date))
								, pretty_numStr_with_scaleNum(_tot)
								, pretty_numStr_with_scaleNum(_aloan), pretty_numStr_with_scaleNum(_eloan_approved), pretty_numStr_with_scaleNum(_eloan_edit)));
					}
				}
				if(true){ // 最後的合計欄
					strBuf.append(MessageFormat.format(prop_CLS9021V00Page.getProperty("table_td_l140s02f_ratePlan20"),"", "合計欄"
							, "" 	//期限 
							, "&nbsp;"	//申請總額度
							, "&nbsp;"	//佔自住型房貸成長方案
							, "-"	//預約期限
							, pretty_numStr_with_scaleNum(sum_tot)
							, pretty_numStr_with_scaleNum(sum_aloan), pretty_numStr_with_scaleNum(sum_eloan_approved), pretty_numStr_with_scaleNum(sum_eloan_edit)));	
				}
				
			}					
			strBuf.append(prop_CLS9021V00Page.getProperty("table_end_l140s02f_ratePlan20"));
			
			logger.info("mail_html_format[\n==============\n"+strBuf.toString()+"\n==============]");
			
			email.send(false, null, site, subject, strBuf.toString());
			//===============
			return true;

		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
			return false;
		}		
	}
	
	private BigDecimal _convert_amt_by_basicUnit(BigDecimal val, BigDecimal base_unit, int scaleNum){
		return val.divide(base_unit).setScale(scaleNum, RoundingMode.HALF_UP);
	}
	
	private String pretty_numStr_with_scaleNum(BigDecimal val, String defaultStr){
		if(val==null){
			return defaultStr;
		}
		return val.toPlainString();
	}
	private String pretty_numStr_with_scaleNum(BigDecimal val){
		return pretty_numStr_with_scaleNum(val, "&nbsp;");
	}
	
	private boolean sendMail_importId55555(String kind, String version, String str_dataEndDate) {
		try {
			
			/** 組Html */
			List<CodeType> mailList = codetypeService
					.findByCodeTypeList("importId55555_mailSites");
			String[] site = new String[mailList.size()];
			for (int i = 0; i < mailList.size(); i++) {
				CodeType sendData = mailList.get(i);
				//20130805修正 只用codeDesc放Email(codeDesc2放姓名)
				site[i] = sendData.getCodeDesc();
			}
			
			String str_dataEndDate_yyyyMM = StringUtils.substring(str_dataEndDate, 0, 7);
			
			//================================
			
			Map<String, String> import_companId_map = new HashMap<String, String>();
			if(true){
				String k = "55555";
				String desc = "住商公司專案";
				import_companId_map.put(k, k+"-"+desc);
			}
			
			
			Map<String, String> brSetNo_map = new HashMap<String, String>();			
			if(true){
				brSetNo_map.put("931", "北區");
				brSetNo_map.put("932", "北二區");
				brSetNo_map.put("933", "桃竹苗");
				brSetNo_map.put("934", "中　區");
				brSetNo_map.put("935", "南　區");
				brSetNo_map.put("007", "國外部");
				brSetNo_map.put("201", "金控總部");
			}
			//================================
			
			Properties prop = MessageBundleScriptCreator
									.getComponentResource(CLS9021V00Page.class);
			
			StringBuffer strBuf = new StringBuffer();
			String env = "";
			if(is_test(version)){
				env = "【測試】";
			}
			String subject = env + prop.getProperty("subject_importId55555");
			
			//title
			strBuf.append(MessageFormat.format(prop.getProperty("title_importId55555"), subject));
			
			//截至 yyyy-MM-dd 止
			
			strBuf.append(MessageFormat.format(prop.getProperty("title_importId55555")
					, "截至 "+str_dataEndDate+" 止"));
			
			//============================
			//part 1
			if(true){	
				//單位：新台幣仟元
				strBuf.append(prop.getProperty("unit_importId55555"));
				
				String[] th = {"歸屬單位"
						, "分行別"
						,"仲介公司"			
						, str_dataEndDate_yyyyMM+"筆數"
						, str_dataEndDate_yyyyMM+"金額"
						, "累計筆數"
						, "累計金額"};
				
				
				strBuf.append(prop.getProperty("table_beg_importId55555"));
				
				strBuf.append(MessageFormat.format(prop.getProperty("table_th_importId55555_part1")
						, th[0], th[1], th[2], th[3], th[4], th[5], th[6]
						));
				
				//~~~
				if(true){
					String[] companyId5_arr = {"55555"};
					String td_pattern = prop.getProperty("table_td_importId55555_part1");
					
					LinkedHashMap<String,BigDecimal> sum_area_companyId__mm_cnt_MS = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__mm_amt_MS = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__mm_cnt_O = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__mm_amt_O = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__time_cnt_MS = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__time_amt_MS = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__time_cnt_O = new LinkedHashMap<String,BigDecimal>();
					LinkedHashMap<String,BigDecimal> sum_area_companyId__time_amt_O = new LinkedHashMap<String,BigDecimal>();
					BigDecimal unit = BigDecimal.ONE; //new BigDecimal("1000");
					int scale = 0;
					
					LinkedHashMap<String, Map<String, Object>> list_mm = new LinkedHashMap<String, Map<String, Object>>();
					LinkedHashMap<String, Map<String, Object>> list_time = new LinkedHashMap<String, Map<String, Object>>();
					for(String companyId : companyId5_arr){
												
						for(Map<String, Object> row_mm : eloandbBaseService.findC900S02A_groupBy_brNo(
								str_dataEndDate_yyyyMM+"-01", str_dataEndDate, companyId)){
							String key = Util.trim(row_mm.get("BRSETNO"))+"_"
												+Util.trim(row_mm.get("BRNO"))+"_"
												+Util.trim(row_mm.get("COMPANYID5"));												
							list_mm.put(key, row_mm);
						}
						
						for(Map<String, Object> row_time : eloandbBaseService.findC900S02A_groupBy_brNo(
								"1911-01-01", "9999-12-31", companyId)){
							String key = Util.trim(row_time.get("BRSETNO"))+"_"
											+Util.trim(row_time.get("BRNO"))+"_"
											+Util.trim(row_time.get("COMPANYID5"));
							list_time.put(key, row_time);
						}
					}	
					/*
					 依[11111,22222]的順序去抓資料
					 005BR 11111
					 229BR 11111
					 
					 005BR 22222
					 
					 => 調整呈現的 順序
					 	005BR 11111
					 	005BR 22222
					 	229BR 11111
					 
					 */
					TreeSet<String> define_key_set = new TreeSet<String>(list_time.keySet());					
					for(String area_companyId_brNo_key: define_key_set){
						Map<String, Object> map_mm = list_mm.get(area_companyId_brNo_key);
						Map<String, Object> map_time = list_time.get(area_companyId_brNo_key);
						
						String BRSETNO = Util.trim(MapUtils.getString(map_time, "BRSETNO")); 
						String BRNAME = Util.trim(MapUtils.getString(map_time, "BRNAME"));                                                          
						String BRNO = Util.trim(MapUtils.getString(map_time, "BRNO"));
						String COMPANYID5 = Util.trim(MapUtils.getString(map_time, "COMPANYID5")); 
						BigDecimal time_CNT_MS = CrsUtil.parseBigDecimal( MapUtils.getString(map_time, "CNT_MS"));      
						BigDecimal time_AMT_MS = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_time, "AMT_MS"));                             
						BigDecimal time_CNT_O = CrsUtil.parseBigDecimal( MapUtils.getString(map_time, "CNT_O"));       
						BigDecimal time_AMT_O = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_time, "AMT_O"));
						BigDecimal time_CNT_MSO = time_CNT_MS.add(time_CNT_O);
						BigDecimal time_AMT_MSO = null;
						//月初, 當月可能尚無資料 => CrsUtil.parseBigDecimal(...)
						BigDecimal mm_CNT_MS = CrsUtil.parseBigDecimal( MapUtils.getString(map_mm, "CNT_MS"));       
						BigDecimal mm_AMT_MS = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_mm, "AMT_MS"));
						BigDecimal mm_CNT_O = CrsUtil.parseBigDecimal( MapUtils.getString(map_mm, "CNT_O"));
						BigDecimal mm_AMT_O = CrsUtil.parseBigDecimal((BigDecimal) MapUtils.getObject(map_mm, "AMT_O"));
						BigDecimal mm_CNT_MSO = mm_CNT_MS.add(mm_CNT_O);
						BigDecimal mm_AMT_MSO = null; 
						if(true){
							//轉換為千元單位
							time_AMT_MS = Arithmetic.div(time_AMT_MS, unit, scale);
							time_AMT_O = Arithmetic.div(time_AMT_O, unit, scale);	
							
							mm_AMT_MS = Arithmetic.div(mm_AMT_MS, unit, scale);
							mm_AMT_O = Arithmetic.div(mm_AMT_O, unit, scale);
							
							time_AMT_MSO = time_AMT_MS.add(time_AMT_O);
							mm_AMT_MSO = mm_AMT_MS.add(mm_AMT_O); 
						}
						if(true){
							String key = BRSETNO+"_"+COMPANYID5; 
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__mm_cnt_MS, key, mm_CNT_MS);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__mm_amt_MS, key, mm_AMT_MS);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__mm_cnt_O, key, mm_CNT_O);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__mm_amt_O, key, mm_AMT_O);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__time_cnt_MS, key, time_CNT_MS);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__time_amt_MS, key, time_AMT_MS);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__time_cnt_O, key, time_CNT_O);
							_add_sum_area_companyId__BigDecimal(sum_area_companyId__time_amt_O, key, time_AMT_O);
						}
						//===========
						strBuf.append(MessageFormat.format(td_pattern 
								, LMSUtil.getDesc(brSetNo_map, BRSETNO)
								, BRNO+"-"+BRNAME
								, LMSUtil.getDesc(import_companId_map, COMPANYID5)
								, NumConverter.addComma(mm_CNT_MSO), NumConverter.addComma(mm_AMT_MSO)
								, NumConverter.addComma(time_CNT_MSO), NumConverter.addComma(time_AMT_MSO)
								));
					}
					
					if(true){
						String tdSum_pattern = prop.getProperty("table_tdSum_importId55555_part1");
						/*
						 * 
						 */
						TreeSet<String> ts = new TreeSet<String>(sum_area_companyId__time_cnt_MS.keySet());
						
						//處理依[營運中心、companyId]合計的資料列
						if(true){
							for(String key_cols: ts){
								String[] keyArr = StringUtils.split(key_cols, "_");
								String BRSETNO = keyArr[0];
								String COMPANYID5 = keyArr[1];
								
								BigDecimal time_CNT_MS = sum_area_companyId__time_cnt_MS.get(key_cols);    
								BigDecimal time_AMT_MS = sum_area_companyId__time_amt_MS.get(key_cols);             
								BigDecimal time_CNT_O = sum_area_companyId__time_cnt_O.get(key_cols);
								BigDecimal time_AMT_O = sum_area_companyId__time_amt_O.get(key_cols);
								//當月資料
								BigDecimal mm_CNT_MS = sum_area_companyId__mm_cnt_MS.get(key_cols);     
								BigDecimal mm_AMT_MS = sum_area_companyId__mm_amt_MS.get(key_cols);
								BigDecimal mm_CNT_O = sum_area_companyId__mm_cnt_O.get(key_cols);
								BigDecimal mm_AMT_O = sum_area_companyId__mm_amt_O.get(key_cols);
								
								BigDecimal time_CNT_MSO = time_CNT_MS.add(time_CNT_O);
								BigDecimal mm_CNT_MSO = mm_CNT_MS.add(mm_CNT_O);
								
								BigDecimal time_AMT_MSO = time_AMT_MS.add(time_AMT_O);
								BigDecimal mm_AMT_MSO = mm_AMT_MS.add(mm_AMT_O); 
								
								strBuf.append(MessageFormat.format(tdSum_pattern 
										, LMSUtil.getDesc(brSetNo_map, BRSETNO)
										, ""
										, LMSUtil.getDesc(brSetNo_map, BRSETNO)+"合計"+LMSUtil.getDesc(import_companId_map, COMPANYID5)
										, NumConverter.addComma(mm_CNT_MSO), NumConverter.addComma(mm_AMT_MSO)
										, NumConverter.addComma(time_CNT_MSO), NumConverter.addComma(time_AMT_MSO)
										));
							}
						}
						
						//處理依[]合計的資料列
						if(true){
							
							BigDecimal time_CNT_MS = BigDecimal.ZERO;      
							BigDecimal time_AMT_MS = BigDecimal.ZERO;             
							BigDecimal time_CNT_O = BigDecimal.ZERO;
							BigDecimal time_AMT_O = BigDecimal.ZERO;
							//當月資料
							BigDecimal mm_CNT_MS = BigDecimal.ZERO;     
							BigDecimal mm_AMT_MS = BigDecimal.ZERO;
							BigDecimal mm_CNT_O = BigDecimal.ZERO;
							BigDecimal mm_AMT_O = BigDecimal.ZERO;
							
							for(String key_cols: ts){
								time_CNT_MS = time_CNT_MS.add( sum_area_companyId__time_cnt_MS.get(key_cols) );      
								time_AMT_MS = time_AMT_MS.add( sum_area_companyId__time_amt_MS.get(key_cols) );             
								time_CNT_O = time_CNT_O.add( sum_area_companyId__time_cnt_O.get(key_cols) );
								time_AMT_O = time_AMT_O.add( sum_area_companyId__time_amt_O.get(key_cols) );
								//當月資料
								mm_CNT_MS = mm_CNT_MS.add( sum_area_companyId__mm_cnt_MS.get(key_cols) );     
								mm_AMT_MS = mm_AMT_MS.add( sum_area_companyId__mm_amt_MS.get(key_cols) );
								mm_CNT_O = mm_CNT_O.add( sum_area_companyId__mm_cnt_O.get(key_cols) );
								mm_AMT_O = mm_AMT_O.add( sum_area_companyId__mm_amt_O.get(key_cols) );							
							}
							
							BigDecimal time_CNT_MSO = time_CNT_MS.add(time_CNT_O);
							BigDecimal mm_CNT_MSO = mm_CNT_MS.add(mm_CNT_O);
							
							BigDecimal time_AMT_MSO = time_AMT_MS.add(time_AMT_O);
							BigDecimal mm_AMT_MSO = mm_AMT_MS.add(mm_AMT_O); 
							
							strBuf.append(MessageFormat.format(tdSum_pattern 
									, ""
									, ""
									, "總計"
									, NumConverter.addComma(mm_CNT_MSO), NumConverter.addComma(mm_AMT_MSO)
									, NumConverter.addComma(time_CNT_MSO), NumConverter.addComma(time_AMT_MSO)));
						}
						
					}
				}
				
				strBuf.append(prop.getProperty("table_end_importId55555"));
			
			}
			/** 發送 */
			
			logger.info("mail_html_format[\n==============\n"+strBuf.toString()+"\n==============]");
			
			//20130805修正寄出仍為Text格式
			email.send(false, null, site, subject, strBuf.toString());
			return true;

		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
			return false;
		}
	}
	
	/**
	 * 回傳值 S136-O-HOUSE-FLAG(1: 購置 2: 房屋修繕 3: 行家理財 N: 非房貸戶) <br/>
	 * 仿 LNSR136
	 * @return
	 */
	private String LNSR136(String prodKind, String lnap3){
		String r = LNSR136_3100_CHECK_HOUSE(prodKind, lnap3);
		if(CrsUtil.inCollection(lnap3, new String[]{"403", "603"}) && Util.equals(prodKind, "03")){
			r = "1";
		}
		return r;
	}
	
	private String LNSR136_3100_CHECK_HOUSE(String prodKind, String lnap3){
		String r = "N";
		
		if(StringUtils.substring(lnap3, 0, 1).compareTo("6")>0){
			
		}else{
			//01 購屋儲蓄貸款 (473,673)&(11)
			if(CrsUtil.inCollection(lnap3, new String[]{"473", "673"}) && Util.equals(prodKind, "11")){
				r = "1";
			}else
				//02 購置住宅貸款 273,(473,673)&( 非 11) 
				if(CrsUtil.inCollection(lnap3, new String[]{"273", "473", "673"}) 
						&& Util.notEquals(prodKind, "11")){
					r = "1";
				}else
					//03 房屋修繕貸款 (474,674,503(22,23,24)) 
					if(CrsUtil.inCollection(lnap3, new String[]{"474", "674", "503"}) 
							&& CrsUtil.inCollection(prodKind, new String[]{"22", "23", "24"})){
						r = "2";
					}else
						//04 行家理財中長期擔保貸款 - 房貸 (603,403)&(30,31)&
						if(CrsUtil.inCollection(lnap3, new String[]{"403", "603"}) 
								&& CrsUtil.inCollection(prodKind, new String[]{"30", "31"})){
							r = "3";
						}
		}
		return r;
	}
	
	private boolean sendMail_elf431_relate_prodKind(String kind, String version) {
		try {
			/** 撈資料 */
			Map<String, Object> totap = findTot(kind, "ACCEPT");
			BigDecimal base = Util.parseBigDecimal("10000");// 萬元

			BigDecimal allNum = Util.parseBigDecimal(totap.get("COUNT"));
			BigDecimal allAmt = Util.parseBigDecimal(totap.get("LOAN"))
					.divide(base).setScale(0, RoundingMode.HALF_UP);

			Map<String, Object> totac = findTot(kind, "ALLOCATE");
			BigDecimal acNum = Util.parseBigDecimal(totac.get("COUNT"));
			BigDecimal acAmt = Util.parseBigDecimal(totac.get("LOAN"))
					.divide(base).setScale(0, RoundingMode.HALF_UP);

			BigDecimal unAcNum = Util.parseBigDecimal(allNum).subtract(
					Util.parseBigDecimal(acNum));
			BigDecimal unAcAmt = Util.parseBigDecimal(allAmt).subtract(
					Util.parseBigDecimal(acAmt));

			Map<String, Object> prepare = eloandbBaseService
					.findTotPreparation(kind);
			String prepareNum = Util.trim(prepare.get("num"));
			String prepareAmt = (prepare.get("amt") == null) ? "0" : Util
					.trim(prepare.get("amt"));
			
			String desc = "全部優惠房貸";
			if("59".equals(kind)){
				desc = "青年安心成家優惠貸款";
				if(Util.isNotEmpty(version)){
					desc += version;
				}
				//part1 自 LN.LNF116 抓取
				Map<String, Object> part1 = findMailServicePart1(kind);
				
				//part2【step 1】e-Loan已核准未撥款(l120m01a.docstatus='05O' 且 LMS.L140M03A.isUse!='Y') 
				//part2【step 2】加強判斷, 以 step1 的 cntrNo 去查 lnf030, 若無 lnf030 才列入  part2
				Map<String, Object> part2 = findMailServicePart2(kind, version);
				
				//part3 抓 L140M01A WHERE approveTime IS NULL AND DELETEDTIME IS NULL 且 L140S02A.property='1' 
				Map<String, Object> part3 = eloandbBaseService.findMailServiceProd59_part3(kind);
				
				acNum = Util.parseBigDecimal(part1.get("NUM"));
				acAmt = Util.parseBigDecimal(part1.get("AMT")).divide(base).setScale(0, RoundingMode.HALF_UP);
								
				unAcNum = Util.parseBigDecimal(part2.get("NUM"));
				unAcAmt = Util.parseBigDecimal(part2.get("AMT")).divide(base).setScale(0, RoundingMode.HALF_UP);
				
				allNum = acNum.add(unAcNum);
				allAmt = acAmt.add(unAcAmt);
				
				prepareNum = CapMath.bigDecimalToString(Util.parseBigDecimal(part3.get("NUM")));
				prepareAmt = CapMath.bigDecimalToString(Util.parseBigDecimal(part3.get("AMT")).divide(base).setScale(0, RoundingMode.HALF_UP));
			}		

			/** 組Html */
			List<CodeType> mailList = codetypeService
					.findByCodeTypeList("lms9541v02_mailSites");
			String[] site = new String[mailList.size()];
			for (int i = 0; i < mailList.size(); i++) {
				CodeType sendData = mailList.get(i);
				//20130805修正 只用codeDesc放Email(codeDesc2放姓名)
				site[i] = sendData.getCodeDesc();
			}

			Properties pop = MessageBundleScriptCreator
					.getComponentResource(CLS9021V00Page.class);
			StringBuffer strBuf = new StringBuffer();

			// XXX承做數
			strBuf.append(pop.getProperty("title_b1") + desc
					+ pop.getProperty("title_b2"));

			// 截至_______為止
			Calendar calendar = Calendar.getInstance();
			strBuf.append(pop.getProperty("date_b1")
					+ CapDate.shiftDaysString(Util.getDate(calendar.getTime()), "yyyy-MM-dd", -1) 
					+ pop.getProperty("date_b2"));

			// 單位:新台幣萬元
			strBuf.append(pop.getProperty("unit_b1"));

			// 開始畫table
			// 筆數 金額
			strBuf.append(pop.getProperty("table_r1_b1"));

			// a-Loan已撥款
			strBuf.append(pop.getProperty("table_r2_b1")
					+ NumConverter.addComma(acNum)
					+ pop.getProperty("table_r_mid")
					+ NumConverter.addComma(acAmt)
					+ pop.getProperty("table_r_end"));

			// e-Loan已核准未撥款
			strBuf.append(pop.getProperty("table_r3_b1")
					+ NumConverter.addComma(unAcNum)
					+ pop.getProperty("table_r_mid")
					+ NumConverter.addComma(unAcAmt)
					+ pop.getProperty("table_r_end"));

			// e-Loan簽核中
			strBuf.append(pop.getProperty("table_r4_b1")
					+ NumConverter.addComma(prepareNum)
					+ pop.getProperty("table_r_mid")
					+ NumConverter.addComma(prepareAmt)
					+ pop.getProperty("table_r_end"));

			// 合計
			strBuf.append(pop.getProperty("table_r5_b1")
					+ NumConverter.addComma(allNum.add(Util
							.parseBigDecimal(prepareNum)))
					+ pop.getProperty("table_r_mid")
					+ NumConverter.addComma(allAmt.add(Util
							.parseBigDecimal(prepareAmt)))
					+ pop.getProperty("table_r_end")
					+ pop.getProperty("table_end"));
			/** 發送 */
			
			logger.info("mail_html_format["+strBuf.toString()+"]");
			
			//20130805修正寄出仍為Text格式
			email.send(false, null, site,
					desc + pop.getProperty("subject"), strBuf.toString());
			return true;

		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
			return false;
		}
	}

	private Set<String> _mailList(String codeType){
		Set<String> r = new HashSet<String>();
		for(CodeType obj : codetypeService.findByCodeTypeList(codeType)){
			String target = Util.trim(obj.getCodeDesc());
			if(Util.isNotEmpty(target)){
				r.add(target);
			}
		}
		return r;
	}
	
	private boolean sendMail_c122m01a_notifyT1(String version, int time_cnt) {
		boolean skipBrT1 = is_test(version)?true:false;
		List<C122M01A> raw_list = c122m01aDao.find_0A0_C122M01A(new String[]{UtilConstants.C122_ApplyKind.H, UtilConstants.C122_ApplyKind.C});
		if(raw_list.size()==0){
			return true;
		}
		
				
		Map<String, List<C122M01A>> map = new TreeMap<String, List<C122M01A>>();
		Date sysDate = CapDate.getCurrentTimestamp();
		for(C122M01A c122m01a : raw_list){
			String brNo = c122m01a.getOwnBrId();
			
			if(c122m01a.getNotifyT1TS()==null){
				//notify
			}else{
				if(LMSUtil.cmpDate(sysDate, "==", c122m01a.getNotifyT1TS())){
					continue;
				}else if(LMSUtil.cmpDate(sysDate, ">", c122m01a.getNotifyT1TS())){
					if(c122m01a.getApplyTS()!=null && LMSUtil.cmpDate(sysDate, ">", CapDate.shiftDays(c122m01a.getApplyTS(), time_cnt))){
						continue;
					}
				}
			}
			if(!map.containsKey(brNo)){
				map.put(brNo, new  ArrayList<C122M01A>());
			}
			map.get(brNo).add(c122m01a);
		}
				
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS9021V00Page.class);
		Set<String> mail_area_list = _mailList("c122m01a_notifyT1");
		String env = "";
		if(is_test(version)){
			env = "【測試】";
		}
//			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//			Properties prop_cls1220m01 = MessageBundleScriptCreator.getComponentResource(CLS1220M01Page.class);
			
		try {	
			for(String brNo : map.keySet()){
				List<C122M01A> list = map.get(brNo);
				if(list.size()==0){
					continue;
				}
				//---				
				List<String> toAddr = new ArrayList<String>();
				if(skipBrT1==false){
					toAddr.add(brNo+"<EMAIL>");				
				}
				toAddr.addAll(mail_area_list);
				//---				
				String brName = branchService.getBranchName(brNo);
				String subject = env+"【"+brNo +" " + brName + "】"+prop.getProperty("subject_c122m01a_notifyT1");
				List<String> body_list = new ArrayList<String>();
				body_list.add(MessageFormat.format(prop.getProperty("msg01_c122m01a_notifyT1"), (brNo +" " + brName), list.size()) );
				body_list.add(prop.getProperty("table_beg_c122m01a_notifyT1"));
				if(true){
					body_list.add("<tr>");
					body_list.add("<td style='padding-right:12px;'>"+"身分證統編"+"</td>");
					body_list.add("<td style='padding-right:12px;'>"+"借款人姓名"+"</td>");
					body_list.add("<td style='padding-right:12px;'>"+"線上進件日期"+"</td>");
					body_list.add("<td style='padding-right:12px;'>"+"申請金額"+"</td>");
					body_list.add("</tr>");
					
					for(C122M01A c122m01a : list){
						body_list.add("<tr>");
						body_list.add("<td style='padding-right:12px;'>"+Util.trim(c122m01a.getCustId())+"</td>");
						body_list.add("<td style='padding-right:12px;'>"+Util.trim(c122m01a.getCustName())+"</td>");
						body_list.add("<td style='padding-right:12px;'>"+Util.trim(TWNDate.toAD(c122m01a.getApplyTS()))+"</td>");
						body_list.add("<td style='padding-right:12px;'>&nbsp;&nbsp;&nbsp;"+LMSUtil.pretty_numStr(c122m01a.getApplyAmt())+"萬"+"</td>");
						body_list.add("</tr>");
					}
				}	
				body_list.add(prop.getProperty("table_end_c122m01a_notifyT1"));
				body_list.add(prop.getProperty("msg02_c122m01a_notifyT1"));
				
				email.send(false, null, toAddr.toArray(new String[toAddr.size()]), subject, StringUtils.join(body_list, ""));
				for(C122M01A c122m01a : list){
					c122m01a.setNotifyT1TS(CapDate.getCurrentTimestamp());
					//------------------------
					c122m01aDao.save(c122m01a);
				}
			}		
			
			return true;

		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
			return false;
		}
	}

	private boolean sendMail_ploan005_notify_IT(String version, int time_cnt) {
		String[] ctrType_arr = new String[]{ContractDocConstants.C340M01A_CtrType.Type_A, ContractDocConstants.C340M01A_CtrType.Type_B};
		List<C340M01A> raw_list = c340m01aDao.findByNotifyT1TS(ctrType_arr);
		if(raw_list.size()==0){
			return true;
		}
		
		Set<String> mail_IT_list = _mailList("ploan005_notify_IT");
		String env = "";
		if(is_test(version)){
			env = "【測試】";
		}

		try {	
			Properties prop_CLS9021V00Page = MessageBundleScriptCreator.getComponentResource(CLS9021V00Page.class);
			String subject_ploan005_notify_IT = prop_CLS9021V00Page.getProperty("subject_ploan005_notify_IT");
			
			for(C340M01A c340m01a : raw_list){
				String brNo = Util.trim(c340m01a.getOwnBrId());
				//---				
				List<String> toAddr = new ArrayList<String>();
				if(is_test(version)==false){
					toAddr.add(brNo+"<EMAIL>");
					Set<String> branchStaff = new HashSet<String>();
					branchStaff.add(Util.trim(c340m01a.getUpdater()));
					branchStaff.add(Util.trim(c340m01a.getApprover()));
					branchStaff.remove("");
					//J-111-0206 對保完成通知檢核對保契約書建立人員為system，新增通知簽報書經辦
					if(Util.equals(Util.trim(c340m01a.getUpdater()),Util.trim(c340m01a.getApprover()))
						&& Util.equals("system",Util.trim(c340m01a.getCreator()))){
						String caseMainId = c340m01a.getCaseMainId();
						L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
						if(Util.isNotEmpty(l120m01a)){
							branchStaff.add(Util.trim(l120m01a.getUpdater()));
						}
					}
					for(String staffNo : branchStaff){
						toAddr.add(staffNo+"@notes.megabank.com.tw");	
					}
				}
				String houseLoan = "";
				if (Util.equals(c340m01a.getCtrType(),ContractDocConstants.C340M01A_CtrType.Type_B)) {
					houseLoan = "房貸";
				}

				toAddr.addAll(mail_IT_list);
				//---				
				String brName = branchService.getBranchName(brNo);
				String subject = env
					+ houseLoan
					+ MessageFormat.format(subject_ploan005_notify_IT
					,"【"+brNo +" " + brName + "】"+Util.trim(c340m01a.getCustId())+" "+Util.trim(c340m01a.getCustName())
					, (c340m01a.getPloanCtrSignTimeM()==null?"":CUSTOM_TIME_FORMAT.format(c340m01a.getPloanCtrSignTimeM()))
					, Util.trim(TWNDate.toAD(c340m01a.getPloanCtrBegDate()))
					, Util.trim(TWNDate.toAD(c340m01a.getPloanCtrEndDate()))
					);
				logger.debug("sendMail[mailList="+toAddr.toString()+"]");
				email.send(toAddr.toArray(new String[toAddr.size()]), subject, "");
				
				if(true){
					c340m01a.setPloanNotifyT1TS(CapDate.getCurrentTimestamp());
					//------------------------
					c340m01aDao.save(c340m01a);
				}
			}		
			
			return true;

		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
			return false;
		}
	}
	private boolean sendMail_ploan015_notify_IT(String version, int time_cnt) {
		
		String[] ctrType_arr = new String[]{ContractDocConstants.C340M01A_CtrType.Type_L};
		List<C340M01A> raw_list = c340m01aDao.findByNotifyT1TS(ctrType_arr);
		if(raw_list.size()==0){
			return true;
		}
		
		Set<String> mail_IT_list = _mailList("ploan015_notify_IT");
		String env = "";
		if(is_test(version)){
			env = "【測試】";
		}

		try {	
			Properties prop_CLS9021V00Page = MessageBundleScriptCreator.getComponentResource(CLS9021V00Page.class);
			String subject_ploan005_notify_IT = prop_CLS9021V00Page.getProperty("subject_ploan015_notify_IT");
			String subject_ploan015_notify_Content = prop_CLS9021V00Page.getProperty("subject_ploan015_notify_Content");
			
			List<Ploan015> lists= new ArrayList<Ploan015>();
			StringBuilder brNos = new StringBuilder();
			for(C340M01A c340m01a : raw_list){
				String brNo = Util.trim(c340m01a.getOwnBrId());
				
				if(lists!=null && brNos.toString().contains(brNo)){
					for(Ploan015 ploan015:lists){
						if(Util.equals(ploan015.getBrNo(), brNo)){
							ploan015.setCount(ploan015.getCount()+1);
							ploan015.setCustNames(ploan015.getCustNames()+","+c340m01a.getCustId()+" "+c340m01a.getCustName());
							break;
						}
					}
				}
				else{
					brNos = brNos.append(","+brNo);;
					Ploan015 ploan015=new Ploan015();
					ploan015.setBrNo(brNo);
					ploan015.setCustNames(c340m01a.getCustId()+" " + c340m01a.getCustName());
					ploan015.setCount(1);
					lists.add(ploan015);
				}
				
				if(true){
					c340m01a.setPloanNotifyT1TS(CapDate.getCurrentTimestamp());
					c340m01aDao.save(c340m01a);
				}
			}		
			
			if(lists!=null){
				for(Ploan015 ploan015:lists){
					List<String> toAddr = new ArrayList<String>();
					if(is_test(version)==false){
						toAddr.add(ploan015.getBrNo()+"<EMAIL>");				
					}
					toAddr.addAll(mail_IT_list);
					//---				
					String brName = branchService.getBranchName(ploan015.getBrNo());
					String subject = subject_ploan005_notify_IT+"【"+ploan015.getBrNo() +" " + brName + "】";		
					String content= MessageFormat.format(subject_ploan015_notify_Content
							,ploan015.getCount()
							,ploan015.getCustNames()
							);		
					email.send(toAddr.toArray(new String[toAddr.size()]), subject, content);
				}
			}
			return true;

		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
			return false;
		}
	}
	public class Ploan015{
		private String brNo;
		private String custNames;
		private int count;
		
		public String getBrNo() {
			return brNo;
		}

		public void setBrNo(String brNo) {
			this.brNo = brNo;
		}
		
		public String getCustNames() {
			return custNames;
		}

		public void setCustNames(String custNames) {
			this.custNames = custNames;
		}
		
		public Integer getCount() {
			return count;
		}

		public void setCount(Integer count) {
			this.count = count;
		}

	}
}
