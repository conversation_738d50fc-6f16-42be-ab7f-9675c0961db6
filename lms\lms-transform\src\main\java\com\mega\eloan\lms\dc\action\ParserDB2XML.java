package com.mega.eloan.lms.dc.action;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.Attributes;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

public class ParserDB2XML extends DefaultHandler {

	private static Logger logger = LoggerFactory.getLogger(ParserDB2XML.class);

	private int occursTimes = 1;
	private String occursFields = "";
	private String occursTimesField = "";
	private String occursKeys = "";
	private String occursValues = "";
	private String selectKeys = "";
	private String selectValues = "";
	private String formName = "";
	private String outFile = "";
	private String currfield = "";// form對應DB2的xml檔中的各個ItemName Ex:<oid
	private String fieldName = "";// ItemName中的各屬性欄位名稱 Ex:name,null,type....
	private boolean bfname = false;

	private List<String> myList = new ArrayList<String>();
	private HashMap<String, String> nameMap = new HashMap<String, String>();
	private HashMap<String, String> nullMap = new HashMap<String, String>();
	private HashMap<String, String> typeMap = new HashMap<String, String>();
	private HashMap<String, String> defaultMap = new HashMap<String, String>();
	private HashMap<String, String> descMap = new HashMap<String, String>();
	private HashMap<String, String> numMap = new HashMap<String, String>();

	// 測試用
	public static void main(String argv[]) {
		ParserDB2XML ap = new ParserDB2XML();
		// bang
		ap.readDB2XML("D:/WORK/workspace/lms-transfer/xml/FLMS110M01/FLMS110M01_L120M01A.xml");
		System.exit(0);
	}

	/**
	 * 讀取form對應的DB2xml檔
	 * 
	 * @param xmlNamePath
	 *            String :當前notesForm對應之DB2Xml所在目錄位置下之xml檔
	 */
	public void readDB2XML(String xmlNamePath) {
		try {
			initMap();
			SAXParserFactory factory = SAXParserFactory.newInstance();
			SAXParser saxParser = factory.newSAXParser();
			saxParser.parse(xmlNamePath, this);
		} catch (Throwable t) {
			logger.error("讀取XML檔時產生錯誤 ,目前正在讀取的XML檔名稱 :" + xmlNamePath, t);
		}
	}

	public void initMap() {
		this.bfname = false;
		this.myList.clear();
		this.nameMap.clear();
		this.nullMap.clear();
		this.typeMap.clear();
		this.defaultMap.clear();
		this.descMap.clear();
		this.numMap.clear();
	}

	// ===========================================================
	// SAX DocumentHandler methods
	// ===========================================================

	public void startDocument() throws SAXException {
		this.occursTimes = 1;
		this.occursFields = "";
		this.occursKeys = "";
		this.occursValues = "";
		this.selectKeys = "";
		this.selectValues = "";
		this.formName = "";
		this.outFile = "";
		this.currfield = "";
		this.fieldName = "";
		this.bfname = false;
	}

	public void endDocument() throws SAXException {
	}

	@SuppressWarnings("unused")
	public void startElement(String uri, String localName, String elementName,
			Attributes attributes) throws SAXException {
		this.currfield = elementName;
		this.bfname = true;
		this.fieldName = elementName;

		// 列印出屬性訊息
		if (attributes != null) {
			for (int i = 0; i < attributes.getLength(); i++) {
				boolean bChkAttrValue = true;
				String aName = attributes.getLocalName(i); // Attr name
				if ("".equals(aName)) {
					aName = attributes.getQName(i);
				}
				if (!"".equals(aName)) {
					getItemValue(aName, attributes.getValue(i));
					bChkAttrValue = false;
				}
			}
		}

	}

	public void endElement(String uri, String localName, String qName)
			throws SAXException {
		this.currfield = "";
		this.bfname = false;
	}

	public void characters(char ch[], int start, int length)
			throws SAXException {
		if (this.bfname) {
			getItemValue(this.fieldName, new String(ch, start, length));
		}

	}

	/**
	 * 設定DB2Xml之formName及輸出的名稱並將欄位屬性值轉入對應的Map
	 * 
	 * @param fldName
	 *            String :(屬性)欄位名稱
	 * @param fldValue
	 *            String :(屬性)值
	 */
	private void getItemValue(String fldName, String fldValue) {
		if ("Form".equals(fldName)) {
			this.formName = fldValue;
		}
		if ("OutFile".equalsIgnoreCase(fldName)) {
			this.outFile = fldValue;
		}

		if ("name".equals(fldName)) {
			this.nameMap.put(this.currfield, fldValue);
			this.myList.add(this.currfield);
		}
		if ("type".equals(fldName)) {
			this.typeMap.put(this.currfield, fldValue);
		}
		if ("null".equals(fldName)) {
			this.nullMap.put(this.currfield, fldValue);
		}
		if ("default".equals(fldName)) {
			this.defaultMap.put(this.currfield, fldValue);
		}
		if ("desc".equals(fldName)) {
			this.descMap.put(this.currfield, fldValue);
		}
		if ("num".equals(fldName)) {
			this.numMap.put(this.currfield, fldValue);
		}
		if ("OccursTimes".equals(fldName)) {
			this.occursTimes = Integer.parseInt(fldValue);
		}
		if ("OccursTimesField".equals(fldName)) {
			this.occursTimesField = fldValue;
		}
		if ("OccursFields".equals(fldName)) {
			this.occursFields = fldValue;
		}
		if ("OccursKeys".equals(fldName)) {
			this.occursKeys = fldValue;
		}
		if ("OccursValues".equals(fldName)) {
			this.occursValues = fldValue;
		}
		if ("SelectKeys".equals(fldName)) {
			this.selectKeys = fldValue;
		}
		if ("SelectValues".equals(fldName)) {
			this.selectValues = fldValue;
		}
	}

	@SuppressWarnings("unused")
	private boolean chkWantField(String fldName) {
		// return myList.contains(fldName);
		return true;
	}

	public String getFormName() {
		return this.formName;
	}

	public String getOutFile() {
		return this.outFile;
	}

	public List<String> getFieldList() {
		return this.myList;
	}

	public HashMap<String, String> getNameMap() {
		return this.nameMap;
	}

	public HashMap<String, String> getNullMap() {
		return this.nullMap;
	}

	public HashMap<String, String> getTypeMap() {
		return this.typeMap;
	}

	public HashMap<String, String> getDefaultMap() {
		return this.defaultMap;
	}

	public HashMap<String, String> getDescMap() {
		return this.descMap;
	}

	public HashMap<String, String> getNumMap() {
		return this.numMap;
	}

	public int getOccursTimes() {
		return this.occursTimes;
	}

	public String getOccursFields() {
		return this.occursFields;
	}

	public String getOccursKeys() {
		return this.occursKeys;
	}

	public String getOccursValues() {
		return occursValues;
	}

	public String getSelectKeys() {
		return this.selectKeys;
	}

	public String getSelectValues() {
		return this.selectValues;
	}

	public String getOccursTimesField() {
		return this.occursTimesField;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
