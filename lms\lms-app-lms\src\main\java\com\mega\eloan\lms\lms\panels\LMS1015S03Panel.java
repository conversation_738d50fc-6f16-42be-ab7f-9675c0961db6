package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 消金信用評等模型(擔保品)
 * 此 Panel 含 JavaScript
 * 再另外 include 純 html 的 LMS1015S03PanelA
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
public class LMS1015S03Panel extends Panel {
	private static final long serialVersionUID = 1L;

	public LMS1015S03Panel(String id) {
		super(id);
	}

	public LMS1015S03Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		renderJsI18N(LMS1015S03PanelA.class);
		
		new LMS1015S03PanelA("includePanel").processPanelData(model, params);
	}
}
