/* Site -------------------------------- */
/*
 body {
 font-family: arial, helvetica, sans-serif;
 font-size: 1em;
 }
 */
body {
    FONT: 13px Arial, Helvetica, sans-serif, "微軟正黑體", "新細明體";
    /*font-family: Arial, "微軟正黑體", "新細明體", sans-serif, helvetica;
     font-size: 13px;*/
}

*:focus {
  outline: none;
}

#capbody {
    width: 964px;
    margin-left: auto;
    margin-right: auto;
}

.clear {
    float: none;
    clear: both;
}

.floatLeft {
    float: left;
}

.floatRight {
    float: right;
}

.pghead {
    text-align: right;
    width: 100%;
}

.pgfoot {
    text-align: center;
    width: 100%;
}

.sysinfo {
    font-size: .7em;
    font-weight: bold;
}

.hide {
    display: none;
}

/* jstee ----------------------------------- */
.jstree input {
    font-size: .8em;
}

.ui-widget {
    font-size: 1em !important;
}

/*.ui-jqgrid-pager{font-size:.7em;}*/
/* jqgrid ----------------------------------*/
.ui-jqgrid .ui-jqgrid-htable th div {
    vertical-align: middle;
    height: auto !important;
}

/*.doc-tabs {font-size:.8em !important;}*/
.ui-state-highlight {
    background: #FFEB86 none repeat-x scroll 50% top !important; /*FAFAAA*/
}

/* uniform ----------------------------------*/
div.uploader span.filename, div.selector span {
    color: #000 !important;
}

/* validation -------------------------------*/
/* .data-error {border: 1px solid red !important;padding: 2px 2px 2px 2px;} */
.data-error {
    background: #fff url(../img/error/invalid_line.gif) repeat-x bottom;
    border: 1px solid #dd7870;
}

/* tab */
.tabs .tabs-warp {
    position: relative;
}

.tabs .tabCtx-warp {
    padding: 1em 1em;
}

.tabs .scroll-icon {
    position: absolute;
    width: 100%
}

.tabs .tabs-scroll-warp {
    margin: 0 40px 0 0;
}

.tabs .tab-prev {
    float: right;
    margin-top: -20px;
    margin-right: 20px;
    *margin-right: 6px;
    background-color:
    #FFFFFF;
}

.tabs .tab-next {
    float: right;
    margin-top: -20px;
    background-color: #FFFFFF;
}

.tabs .tab-next span, .tabs .tab-prev span {
    cursor: pointer;
}

/**.ui-tabs-nav li{ max-width:105px;}*/
.ui-tabs-nav li a {
    padding: 0.4em 0.6em 0.2em 0.6em !important; /* max-width:88px;*/
    padding-right: 2px;
    overflow: hidden;
}

.tabBorderColor {
    background-color: red;
}

.ui-tabs .ui-tabs-panel {
    padding: 0px;
}

/* dalog */
.ui-dialog .ui-dialog-titlebar {
    font-size: 14px;
    padding: .2em 1em;
}

/* ckeditor */
.cke_skin_kama .cke_button_InsertImageCmd .cke_icon {
    background-position: 0 -576px !important;
}

.cke_skin_kama .cke_button_LocalSave .cke_icon {
    background-position: 0 -64px !important;
}

/* thickbox */
.log-error-message {
    border-color: #C24D58
}

.log-error-message * {
    vertical-align: middle;
}

.log-error-message .TB_title, .log-error-message .TB_b2f, .log-error-message .TB_b3f, .log-error-message .TB_b4f {
    background-color: #B84E4D;
    border-color: #C24D58;
}

.log-error-message .TB_b1f, .log-error-message .TB_buttonBar_bottom {
    background-color: #E3A0Ab;
    border-color: #B84E4D;
}

/*		fieldset  */
fieldset {
    clear: both;
    border: 2px solid #3B7092;
    margin: 1em 0;
    /*padding: 0.5em;*/
    padding: 0.1em;
}

/** pop css **/
.popup_tit1 {
    font-size: 13px;
    color: #a0360f;
    padding: 0 0 0 20px;
    margin: 0 0 10px;
    font-weight: bold;
}

span.text-red, .text-red, .red {
    color: #990000;
}

.tb1 td, .tb1 th {
    line-height: 1.5em;
    padding: 3px 3px 2px;
    border: 1px #e0e0e0 solid;
    vertical-align: text-top;
}

base.css .tit2.color-black, .color-black, tbody td {
    color: #000000;
}

.popup_cont hr.hr1 {
    border: 1px dashed #cccccc;
}

.star {
    color: #AA0000;
}
#includeIdForm input{
	font-weight:bolder;
}

.includeList {
	max-height: 200px;
}
.includeList hr{
	color:#6699CC;
	border:1px solid #6699CC;
	margin:5px 0;
}
.inlcudeCustList b{
	display:inline-block;
	width:20px;
	text-align:center;
	margin-right:5px;
}
.inlcudeCustList a:hover{
	display:block;
    background: #88B1D2;
}

.selectItem {
    background-color: #FBEC88;
    border: 1px solid #FAD42E;
	font-weight:bolder;
}

ul.lt {
    line-height: 1.5em;
}
ul.lt li {
    margin-left: 25px;
    list-style-position: outside;
}

ul.nm li {
    list-style-type: decimal;
}

ul.dc li {
    list-style-type: disc;
}

input.date {
    width: 6em;
}
.ps1{
	color:#900;
}

/* tckeditor preview css add by fantasy 2012/07/02 */
div.preview {
	border:3px double #ccc;
	overflow: auto;
	width: 500px;
	height: 150px;
	background-color:#f5f5f5;
}
div.preview:hover{
	border:3px double black;
	cursor:pointer;
}
div.preview ol li{
    list-style:decimal inside;
}
div.preview ul li{
    list-style:disc inside;
}

.TB_window {
    font-size: 1em;
    color: #333333;
    min-width: 249px;
}

.TB_secondLine {
    font-size: 0.9em;
    color: #666666;
}

.TB_overlay {
    position: fixed;
    z-index: 100;
    top: 0px;
    left: 0px;
    height: 100%;
    width: 100%;
}

.TB_overlayMacFFBGHack {
    background: url(macFFBgHack.png) repeat;
}

.TB_overlayBG {
    background-color: #F5EEFC;
    filter: alpha(opacity=75);
    -moz-opacity: 0.75;
    opacity: 0.75;
}

* html .TB_overlay {
    position: absolute;
	//TODO TEST  --> mega.eloan.properties.js 產生對話框func 未來會升版，有機會移除?
	//這是 IE6 專屬 的 CSS Hack，但已經被淘汰，現代瀏覽器不支援這種寫法
    height: expression(document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + 'px');	
}

.TB_window {
    position: fixed;
    background: #ffffff;
    z-index: 102;
    color: #000000;
    display: none;
    text-align: left;
    top: 46.5%;
    left: 50%
}

* html .TB_window {
    position: absolute;
	//TODO TEST  --> mega.eloan.properties.js 產生對話框func 未來會升版，有機會移除?
	//thickbox.js 之後改寫fancybox移除?
    margin-top: expression(0 - parseInt(this.offsetHeight / 2) + (TBWindowMargin = document.documentElement && document.documentElement.scrollTop || document.body.scrollTop) + 'px');
}

.TB_ajaxContent {
    position: relative;
}

.TB_window img.TB_Image {
    display: block;
    margin: 15px 0 0 15px;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    border-top: 1px solid #666;
    border-left: 1px solid #666;
}

.TB_caption {
    height: 25px;
    padding: 7px 30px 10px 25px;
    float: left;
}

.TB_closeWindow {
    height: 25px;
    padding: 11px 25px 10px 0;
    float: right;
}

.TB_closeAjaxWindow {
    padding: 0 5px 0 0;
    text-align: right;
    float: right;
}

.TB_closeAjaxWindowFixIE {
    padding: 0 5px 0 0;
    margin: -5pt 0 0 0;
    text-align: right;
    float: right;
}

.TB_ajaxWindowTitle {
    float: left;
    padding: 0 0 0 10px;
}

.TB_buttonBar {
    background-color: #EBEBFF;
    padding: 3px 10px 3px 10px;
    border-bottom: 1px solid #6699cc;
    border-left: 1px solid #6699cc;
    border-right: 1px solid #6699cc;
}

.TB_buttonBar_bottom {
    background-color: #EBEBFF;
    padding: 3px 10px 3px 10px;
    border-top: 1px solid #6699cc;
    border-left: 1px solid #6699cc;
    border-right: 1px solid #6699cc;
    position: relative;
}

.TB_buttonBar button img {
    margin-right: 2px;
}

.TB_buttons {
    font-size: 1em;
    color: #333333;
}

.TB_title {
    font-weight: bold;
    color: #FFFFFF;
    background-color: #6699cc;
    border-top: 1px solid #6699cc;
    border-bottom: 1px solid #6699cc;
    border-left: 1px solid #6699cc;
    border-right: 1px solid #6699cc;
    height: 27px;
    line-height: 27px;
    vertical-align: middle;
}

.TB_ajaxContent {
    min-height: 50px;
    clear: both;
    padding: 5px 10px 5px 10px;
    overflow: auto;
    text-align: left;
    line-height: 1.4em;
    border-left: 1px solid #6699cc;
    border-right: 1px solid #6699cc;
}

.TB_ajaxContent p {
    padding: 5px 0px 5px 0px;
}

.TB_load {
    position: fixed;
    display: none;
    height: 13px;
    width: 208px;
    z-index: 103;
    top: 50%;
    left: 50%;
    margin: -6px 0 0 -104px;
}

* html .TB_load {/* ie6 hack */
    position: absolute;
    margin-top: expression(0 - parseInt(this.offsetHeight / 2) + (TBWindowMargin = document.documentElement && document.documentElement.scrollTop || document.body.scrollTop) + 'px');
}

.TB_HideSelect {
    z-index: 99;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #fff;
    border: none;
    filter: alpha(opacity=0);
    -moz-opacity: 0;
    opacity: 0;
    height: 100%;
    width: 100%;
}

* html .TB_HideSelect {/* ie6 hack */
    position: absolute;
    height: expression(document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + 'px');
}

.TB_iframeContent {
    clear: both;
    border: none;
    margin-bottom: -3px;
    border-left: 1px solid #6699cc;
    border-right: 1px solid #6699cc;
}

.TB_closeWindowButton,.TB_closeWindowButton:active,.TB_closeWindowButton:visited,.TB_closeWindowButton:hover {
    text-decoration: none;
}

.TB_b1f,.TB_b2f,.TB_b3f,.TB_b4f {
    font-size: 1px;
    overflow: hidden;
    display: block;
    background: #EBEBFF;
    border-right: 1px solid #6699cc;
    border-left: 1px solid #6699cc;
}

.TB_b1f {
    height: 1px;
    margin: 0 5px;
    background: #6699cc;
}

.TB_b2f {
    height: 1px;
    margin: 0 3px;
    border-right-width: 2px;
    border-left-width: 2px;
}

.TB_b3f {
    height: 1px;
    margin: 0 2px;
}

.TB_b4f {
    height: 2px;
    margin: 0 1px;
}

.TB_head {
    background: #6699cc;
}

.TB_content {
    background: #6699cc;
    padding-top: 5px;
}
