/* 
 * C125M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 勞工紓困信保整批貸款申請書 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C125M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class C125M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * 刪除註記
	 * <p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name = "DELETEDTIME", columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;

	/** 檔案產生日 **/
	@Column(name = "BATCHDATE", columnDefinition = "TIMESTAMP")
	private Timestamp batchDate;

	/** 銀行代碼 **/
	@Size(max = 7)
	@Column(name = "BANKNO", length = 7, columnDefinition = "CHAR(7)")
	private String bankNo;

	/** 案件編號 **/
	@Size(max = 50)
	@Column(name = "BANKKEYNO", length = 50, columnDefinition = "VARCHAR (50)")
	private String bankKeyNo;

	/** 保證案號 **/
	@Digits(integer = 10, fraction = 0, groups = Check.class)
	@Column(name = "GRNTPAPER", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal grntPaper;

	/** 貴單位核准本案授信之文件編號 **/
	@Size(max = 50)
	@Column(name = "APPROVENO", length = 50, columnDefinition = "VARCHAR (50)")
	private String approveNo;

	/** 批覆書額度核准日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "APPROVEDAY", columnDefinition = "DATE")
	private Date approveDay;

	/** 授信單位依授權層級核准之額度 **/
	@Digits(integer = 14, fraction = 0, groups = Check.class)
	@Column(name = "LOANLIMIT", columnDefinition = "DECIMAL(14,0)")
	private BigDecimal loanLimit;

	/** 授信起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BEGDAY", columnDefinition = "DATE")
	private Date begDay;

	/** 授信迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDAY", columnDefinition = "DATE")
	private Date endDay;

	/** 授信金額 **/
	@Digits(integer = 16, fraction = 2, groups = Check.class)
	@Column(name = "LOAN", columnDefinition = "DECIMAL(16,2)")
	private BigDecimal loan;

	/**
	 * 還款方式
	 * <p/>
	 * 固定2-本息平均
	 */
	@Digits(integer = 1, fraction = 0, groups = Check.class)
	@Column(name = "RETMETHOD", columnDefinition = "DECIMAL(1,0)")
	private Integer retMethod;

	/**
	 * 手續費收取期別
	 * <p/>
	 * 固定1-按月
	 */
	@Digits(integer = 1, fraction = 0, groups = Check.class)
	@Column(name = "FEETERM", columnDefinition = "DECIMAL(1,0)")
	private Integer feeTerm;

	/**
	 * 寬限期月數
	 * <p/>
	 * 不得超過6
	 */
	@Digits(integer = 1, fraction = 0, groups = Check.class)
	@Column(name = "GRACEPERIOD", columnDefinition = "DECIMAL(1,0)")
	private Integer gracePeriod;

	/** 年利率 **/
	@Digits(integer = 10, fraction = 6, groups = Check.class)
	@Column(name = "LOANRATE", columnDefinition = "DECIMAL(10,6)")
	private BigDecimal loanRate;

	/**
	 * 手續費收取方式
	 * <p/>
	 * 固定 1-一次
	 */
	@Digits(integer = 1, fraction = 0, groups = Check.class)
	@Column(name = "FEEMETHOD", columnDefinition = "DECIMAL(1,0)")
	private Integer feeMethod;

	/**
	 * 手續費率
	 * <p/>
	 * 固定為0.1
	 */
	@Digits(integer = 10, fraction = 6, groups = Check.class)
	@Column(name = "RATE", columnDefinition = "DECIMAL(10,6)")
	private BigDecimal rate;

	/**
	 * 票據繳納記號
	 * <p/>
	 * 1:票據繳納<br/>
	 * 固定為1
	 */
	@Size(max = 1)
	@Column(name = "TAXFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String taxFlag;

	/**
	 * 帳戶管理費用註記
	 * <p/>
	 * 1:有 0:無
	 */
	@Size(max = 1)
	@Column(name = "ACNTFEEFLG", length = 1, columnDefinition = "VARCHAR(1)")
	private String acntFeeFlg;

	/**
	 * 帳戶管理費收取方式
	 * <p/>
	 * 1.額度一次收取2.逐筆收取+
	 */
	@Size(max = 1)
	@Column(name = "ACNTFEEFLG2", length = 1, columnDefinition = "VARCHAR(1)")
	private String acntFeeFlg2;

	/** 帳戶管理費用 **/
	@Digits(integer = 14, fraction = 0, groups = Check.class)
	@Column(name = "ACNTFEE", columnDefinition = "DECIMAL(14,0)")
	private BigDecimal acntFee;

	/**
	 * 另收取保證費註記
	 * <p/>
	 * 1:有 0:無
	 */
	@Size(max = 1)
	@Column(name = "OTHERFEEFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String otherFeeFlag;

	/**
	 * 保證費收取方式
	 * <p/>
	 * 1.額度一次收取2.逐筆收取
	 */
	@Size(max = 1)
	@Column(name = "OTHERFEEFLAG2", length = 1, columnDefinition = "VARCHAR(1)")
	private String otherFeeFlag2;

	/** 另收取保證費 **/
	@Digits(integer = 14, fraction = 0, groups = Check.class)
	@Column(name = "OTHERFEE", columnDefinition = "DECIMAL(14,0)")
	private BigDecimal otherFee;

	/**
	 * 放款帳號
	 * <p/>
	 * 非必填
	 */
	@Size(max = 26)
	@Column(name = "LOANACTNO", length = 26, columnDefinition = "VARCHAR(26)")
	private String loanActNo;

	/** 銀行經辦姓名 **/
	@Size(max = 84)
	@Column(name = "BANKUSERNAME", length = 84, columnDefinition = "VARCHAR(84)")
	private String bankUserName;

	/** 銀行經辦電話區碼 **/
	@Size(max = 2)
	@Column(name = "STAFFTEL1", length = 2, columnDefinition = "VARCHAR(2)")
	private String staffTel1;

	/** 銀行經辦電話號碼 **/
	@Size(max = 8)
	@Column(name = "STAFFTEL2", length = 8, columnDefinition = "VARCHAR(8)")
	private String staffTel2;

	/** 銀行經辦電話分機 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "STAFFTELEXT", columnDefinition = "DECIMAL(5,0)")
	private Integer staffTelExt;

	/** 回傳的通知單號 **/
	@Digits(integer = 10, fraction = 0, groups = Check.class)
	@Column(name = "SRLNO", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal srlNo;

	/** 回覆日 **/
	@Column(name = "RECEIVEDAY", columnDefinition = "TIMESTAMP")
	private Timestamp receiveDay;

	/** 回覆結果 **/
	@Size(max = 3)
	@Column(name = "DATASTATUS", length = 3, columnDefinition = "VARCHAR(3)")
	private String dataStatus;

	/** 處理說明 **/
	@Size(max = 600)
	@Column(name = "DESCRIPTION", length = 600, columnDefinition = "VARCHAR(600)")
	private String description;

	/** 版本 **/
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "VERSION", columnDefinition = "DECIMAL(2,0)")
	private Integer version;

	/**
	 * 取得刪除註記
	 * <p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}

	/**
	 * 設定刪除註記
	 * <p/>
	 * 文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得檔案產生日 **/
	public Timestamp getBatchDate() {
		return this.batchDate;
	}

	/** 設定檔案產生日 **/
	public void setBatchDate(Timestamp value) {
		this.batchDate = value;
	}

	/** 取得銀行代碼 **/
	public String getBankNo() {
		return this.bankNo;
	}

	/** 設定銀行代碼 **/
	public void setBankNo(String value) {
		this.bankNo = value;
	}

	/** 取得案件編號 **/
	public String getBankKeyNo() {
		return this.bankKeyNo;
	}

	/** 設定案件編號 **/
	public void setBankKeyNo(String value) {
		this.bankKeyNo = value;
	}

	/** 取得保證案號 **/
	public BigDecimal getGrntPaper() {
		return this.grntPaper;
	}

	/** 設定保證案號 **/
	public void setGrntPaper(BigDecimal value) {
		this.grntPaper = value;
	}

	/** 取得貴單位核准本案授信之文件編號 **/
	public String getApproveNo() {
		return this.approveNo;
	}

	/** 設定貴單位核准本案授信之文件編號 **/
	public void setApproveNo(String value) {
		this.approveNo = value;
	}

	/** 取得批覆書額度核准日 **/
	public Date getApproveDay() {
		return this.approveDay;
	}

	/** 設定批覆書額度核准日 **/
	public void setApproveDay(Date value) {
		this.approveDay = value;
	}

	/** 取得授信單位依授權層級核准之額度 **/
	public BigDecimal getLoanLimit() {
		return this.loanLimit;
	}

	/** 設定授信單位依授權層級核准之額度 **/
	public void setLoanLimit(BigDecimal value) {
		this.loanLimit = value;
	}

	/** 取得授信起日 **/
	public Date getBegDay() {
		return this.begDay;
	}

	/** 設定授信起日 **/
	public void setBegDay(Date value) {
		this.begDay = value;
	}

	/** 取得授信迄日 **/
	public Date getEndDay() {
		return this.endDay;
	}

	/** 設定授信迄日 **/
	public void setEndDay(Date value) {
		this.endDay = value;
	}

	/** 取得授信金額 **/
	public BigDecimal getLoan() {
		return this.loan;
	}

	/** 設定授信金額 **/
	public void setLoan(BigDecimal value) {
		this.loan = value;
	}

	/**
	 * 取得還款方式
	 * <p/>
	 * 固定2-本息平均
	 */
	public Integer getRetMethod() {
		return this.retMethod;
	}

	/**
	 * 設定還款方式
	 * <p/>
	 * 固定2-本息平均
	 **/
	public void setRetMethod(Integer value) {
		this.retMethod = value;
	}

	/**
	 * 取得手續費收取期別
	 * <p/>
	 * 固定1-按月
	 */
	public Integer getFeeTerm() {
		return this.feeTerm;
	}

	/**
	 * 設定手續費收取期別
	 * <p/>
	 * 固定1-按月
	 **/
	public void setFeeTerm(Integer value) {
		this.feeTerm = value;
	}

	/**
	 * 取得寬限期月數
	 * <p/>
	 * 不得超過6
	 */
	public Integer getGracePeriod() {
		return this.gracePeriod;
	}

	/**
	 * 設定寬限期月數
	 * <p/>
	 * 不得超過6
	 **/
	public void setGracePeriod(Integer value) {
		this.gracePeriod = value;
	}

	/** 取得年利率 **/
	public BigDecimal getLoanRate() {
		return this.loanRate;
	}

	/** 設定年利率 **/
	public void setLoanRate(BigDecimal value) {
		this.loanRate = value;
	}

	/**
	 * 取得手續費收取方式
	 * <p/>
	 * 固定 1-一次
	 */
	public Integer getFeeMethod() {
		return this.feeMethod;
	}

	/**
	 * 設定手續費收取方式
	 * <p/>
	 * 固定 1-一次
	 **/
	public void setFeeMethod(Integer value) {
		this.feeMethod = value;
	}

	/**
	 * 取得手續費率
	 * <p/>
	 * 固定為0.1
	 */
	public BigDecimal getRate() {
		return this.rate;
	}

	/**
	 * 設定手續費率
	 * <p/>
	 * 固定為0.1
	 **/
	public void setRate(BigDecimal value) {
		this.rate = value;
	}

	/**
	 * 取得票據繳納記號
	 * <p/>
	 * 1:票據繳納<br/>
	 * 固定為1
	 */
	public String getTaxFlag() {
		return this.taxFlag;
	}

	/**
	 * 設定票據繳納記號
	 * <p/>
	 * 1:票據繳納<br/>
	 * 固定為1
	 **/
	public void setTaxFlag(String value) {
		this.taxFlag = value;
	}

	/**
	 * 取得帳戶管理費用註記
	 * <p/>
	 * 1:有 0:無
	 */
	public String getAcntFeeFlg() {
		return this.acntFeeFlg;
	}

	/**
	 * 設定帳戶管理費用註記
	 * <p/>
	 * 1:有 0:無
	 **/
	public void setAcntFeeFlg(String value) {
		this.acntFeeFlg = value;
	}

	/**
	 * 取得帳戶管理費收取方式
	 * <p/>
	 * 1.額度一次收取2.逐筆收取+
	 */
	public String getAcntFeeFlg2() {
		return this.acntFeeFlg2;
	}

	/**
	 * 設定帳戶管理費收取方式
	 * <p/>
	 * 1.額度一次收取2.逐筆收取+
	 **/
	public void setAcntFeeFlg2(String value) {
		this.acntFeeFlg2 = value;
	}

	/** 取得帳戶管理費用 **/
	public BigDecimal getAcntFee() {
		return this.acntFee;
	}

	/** 設定帳戶管理費用 **/
	public void setAcntFee(BigDecimal value) {
		this.acntFee = value;
	}

	/**
	 * 取得另收取保證費註記
	 * <p/>
	 * 1:有 0:無
	 */
	public String getOtherFeeFlag() {
		return this.otherFeeFlag;
	}

	/**
	 * 設定另收取保證費註記
	 * <p/>
	 * 1:有 0:無
	 **/
	public void setOtherFeeFlag(String value) {
		this.otherFeeFlag = value;
	}

	/**
	 * 取得保證費收取方式
	 * <p/>
	 * 1.額度一次收取2.逐筆收取
	 */
	public String getOtherFeeFlag2() {
		return this.otherFeeFlag2;
	}

	/**
	 * 設定保證費收取方式
	 * <p/>
	 * 1.額度一次收取2.逐筆收取
	 **/
	public void setOtherFeeFlag2(String value) {
		this.otherFeeFlag2 = value;
	}

	/** 取得另收取保證費 **/
	public BigDecimal getOtherFee() {
		return this.otherFee;
	}

	/** 設定另收取保證費 **/
	public void setOtherFee(BigDecimal value) {
		this.otherFee = value;
	}

	/**
	 * 取得放款帳號
	 * <p/>
	 * 非必填
	 */
	public String getLoanActNo() {
		return this.loanActNo;
	}

	/**
	 * 設定放款帳號
	 * <p/>
	 * 非必填
	 **/
	public void setLoanActNo(String value) {
		this.loanActNo = value;
	}

	/** 取得銀行經辦姓名 **/
	public String getBankUserName() {
		return this.bankUserName;
	}

	/** 設定銀行經辦姓名 **/
	public void setBankUserName(String value) {
		this.bankUserName = value;
	}

	/** 取得銀行經辦電話區碼 **/
	public String getStaffTel1() {
		return this.staffTel1;
	}

	/** 設定銀行經辦電話區碼 **/
	public void setStaffTel1(String value) {
		this.staffTel1 = value;
	}

	/** 取得銀行經辦電話號碼 **/
	public String getStaffTel2() {
		return this.staffTel2;
	}

	/** 設定銀行經辦電話號碼 **/
	public void setStaffTel2(String value) {
		this.staffTel2 = value;
	}

	/** 取得銀行經辦電話分機 **/
	public Integer getStaffTelExt() {
		return this.staffTelExt;
	}

	/** 設定銀行經辦電話分機 **/
	public void setStaffTelExt(Integer value) {
		this.staffTelExt = value;
	}

	/** 取得回傳的通知單號 **/
	public BigDecimal getSrlNo() {
		return this.srlNo;
	}

	/** 設定回傳的通知單號 **/
	public void setSrlNo(BigDecimal value) {
		this.srlNo = value;
	}

	/** 取得回覆日 **/
	public Timestamp getReceiveDay() {
		return this.receiveDay;
	}

	/** 設定回覆日 **/
	public void setReceiveDay(Timestamp value) {
		this.receiveDay = value;
	}

	/** 取得回覆結果 **/
	public String getDataStatus() {
		return this.dataStatus;
	}

	/** 設定回覆結果 **/
	public void setDataStatus(String value) {
		this.dataStatus = value;
	}

	/** 取得處理說明 **/
	public String getDescription() {
		return this.description;
	}

	/** 設定處理說明 **/
	public void setDescription(String value) {
		this.description = value;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public Integer getVersion() {
		return version;
	}
}
