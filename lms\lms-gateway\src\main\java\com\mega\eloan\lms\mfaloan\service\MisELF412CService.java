package com.mega.eloan.lms.mfaloan.service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF412C;

public interface MisELF412CService {
	/**
	 * 依分行別取得覆審名單資料
	 * 
	 * @param branch
	 * @return
	 */
	List<Map<String, Object>> getByKeyWithBasicData(String branch);

	Map<String, Object> getByKeyWithBasicData(String branch, String custId,
			String dupNo);

	Map<String, Object> getDataWithPEO(String branch, String custId,
			String dupNo);

	public ELF412C findByPk(String branch, String custId, String dupNo);

	public List<ELF412C> findByBranch(String branch);

	public int updateELF412CNckdFlag(Date ELF412C_LRDATE,
			String ELF412C_NEWADD, String ELF412C_NEWDATE,
			String ELF412C_NCKDFLAG, Date ELF412C_NCKDDATE,
			String ELF412C_NCKDMEMO, Date ELF412C_NEXTNWDT,
			Date ELF412C_NEXTLTDT, Timestamp ELF412C_TMESTAMP,
			Date ELF412C_UPDDATE, String ELF412C_UPDATER,
			String ELF412C_BRANCH, String ELF412C_CUSTID, String ELF412C_DUPNO);

}
