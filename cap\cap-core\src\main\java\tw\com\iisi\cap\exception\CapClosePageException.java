/**
 * CapClosePageException.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.exception;

import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 錯誤產生時，通知前端關閉畫面
 * extends CapException
 * </pre>
 * 
 * @since 2011/1/4
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/1/4,RodesChen,new
 *          </ul>
 */
@SuppressWarnings({ "serial" })
public class CapClosePageException extends CapException implements CapShortMessageException {

    /**
     * i18nKey
     */
    String i18nKey;

    /**
     * Instantiates a new cap exception.
     */
    public CapClosePageException() {
        super();
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param causeClass
     *            the cause class
     */
    @SuppressWarnings("rawtypes")
    public CapClosePageException(Class causeClass) {
        super();
        super.setCauseSource(causeClass);
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param message
     *            the message
     * @param causeClass
     *            the cause class
     */
    @SuppressWarnings("rawtypes")
    public CapClosePageException(String message, Class causeClass) {
        super(message, causeClass);
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param cause
     *            the throwable
     * @param causeClass
     *            the cause class
     */
    @SuppressWarnings("rawtypes")
    public CapClosePageException(Throwable cause, Class causeClass) {
        super(cause, causeClass);
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param message
     *            the message
     * @param cause
     *            the cause
     * @param causeClass
     *            the cause class
     */
    @SuppressWarnings("rawtypes")
    public CapClosePageException(String message, Throwable cause, Class causeClass) {
        super(message, cause, causeClass);
    }

    /*
     * (non-Javadoc)
     * 
     * @see java.lang.Throwable#getMessage()
     */
    @Override
    public String getMessage() {
        return CapString.isEmpty(i18nKey) ? super.getMessage() : i18nKey;
    }

    /**
     * set i18n key
     * 
     * @param i18nKey
     *            the i18n key
     * @return CapMessageException
     */
    public CapClosePageException setMessageKey(String i18nKey) {
        this.i18nKey = i18nKey;
        return this;
    }

    /**
     * get i18n key
     * 
     * @return String
     */
    public String getMessageKey() {
        return i18nKey;
    }

}
