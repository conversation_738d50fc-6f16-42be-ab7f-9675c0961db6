package tw.com.jcs.auth.model;

/**
 * <pre>
 * 權限物件
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public interface CodeItem {

    /**
     * 取得代碼
     * 
     * @return
     */
    int getCode();

    /**
     * 取得階層
     * 
     * @return
     */
    int getStep();

    /**
     * 取得排列順序
     * 
     * @return
     */
    int getSeq();

    /**
     * 取得上層代碼
     * 
     * @return
     */
    int getParent();

    /**
     * 取得代碼名稱
     * 
     * @return
     */
    String getName();

    /**
     * 取得URL位置
     * 
     * @return
     */
    String getPath();

    /**
     * 取得 Desc
     * 
     * @return
     */
    String getDesc();

    /**
     * 取得 文件ID
     * 
     * @return
     */
    String getDocid();
}