$(function(){
    // alert(viewstatus);
    var grid = $("#gridview").iGrid({
        // rownumbers : true,
        handler: 'lms1705gridhandler',
        height: 350, // 設定高度
        sortname: 'retrialDate|projectNo',
        width: 785,
        autowidth: false,
        sortorder: 'desc|asc',
        // multiselect : true,
        postData: {
            formAction: "queryL170m01a",
            docStatus: viewstatus
        },
        colModel: [{
            colHeader: "",
            name: 'oid',
            hidden: true
            // 是否隱藏
        }, {
            colHeader: "", // uid
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'uid', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: "", // 文件編號
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'mainId', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: "",
            name: 'dupNo',
            width: 100, // 設定寬度
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms1705v01["L170M01a.retrialDate"], // 覆審日期
            align: "center",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'retrialDate' // col.id
        }, {
            colHeader: i18n.lms1705v01["L170M01a.projectNo"], // 覆審案號
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'projectNo' // col.id
        }, {
            colHeader: i18n.lms1705v01["L170M01a.custId"], // 統一編號
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: BOM,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1705v01["L170M01a.custName"], // 主要借款人
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'custName' // col.id
        }, {
            // J-111-0326 海外覆審作業系統改良第一階段：
            // 4-2. 新增 符合授信額度標準
            // 原 mLoanPerson主要戶 改 mLoanPerson符合授信額度標準、mLoanPersonA主要戶
            colHeader: i18n.lms1705v01["L170M01a.mLoanPerson"], // 符合授信額度標準
            align: "center",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'mLoanPerson' // col.id
        }, {
            colHeader: i18n.lms1705v01["L170M01a.lastRetrialDate"], // 上次覆審日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'lastRetrialDate' // col.id
        }, {
            colHeader: i18n.lms1705v01["L170M01a.approver"], // 覆審人員
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'updater' // col.id
        }, {
            colHeader: i18n.lms1705v01["L170M01a.complete"], // 完成
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'update' // col.id
        }, {
            colHeader: i18n.lms1705v01["L170M01a.conFlag"], // 免覆審註記
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'nCkdFlag' // col.id
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            BOM(null, null, data);
        }
    
    });
    
    /*===========================================================================================================*/
    $("#buttonPanel").find("#btnDelete").click(function(){
        var $gridview = $("#gridview");
        var ids = $gridview.getGridParam('selrow');
        
        if (!ids) {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        var oids = [];
        for (var i in ids) {
            oids.push($gridview.getRowData(ids).oid);
        }
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1705m01formhandler",
                    action: "deleteListL170m01a",
                    data: {
                        //  formAction: "deleteListL170m01a",
                        oids: oids
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
        
    }).end().find("#btnFilter").click(function(){
        thickBoxFilter();
    }).end().find("#btnAdd").click(function(){
        // 新增的功能
        thickBoxOpenADD();
        
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        BOM(null, null, result);
        //openDoc(null, null, result);
    
    });
    
    /*=====================================================================================================*/
    
    function thickBoxFilter(){
        $("#filterForm").reset();
        var test2 = $("#lms170Filter").thickbox({ // '新增覆審報告表',
            title: i18n.lms1705v01['L170M01a.fileter'],
            width: 450,
            height: 200,
            align: 'center',
            valign: 'bottom',
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($("#filterForm").valid()) {
                        $("#gridview").jqGrid("setGridParam", {
                            postData: {
                                retrialDate1: $("#retrialDateFilter1").val(),
                                retrialDate2: $("#retrialDateFilter2").val(),
                                custId: $("#custIdFilter").val(),
                                custName: $("#custName").val()
                            },
                			page : 1,
                			//gridPage : 1,
                            search: true
                        }).trigger("reloadGrid");
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    function thickBoxOpenADD(cellvalue, options, rowObject, showMsg){
        $("#tabForm").reset();
        var test2 = $("#lms170new").thickbox({ // '新增覆審報告表',
            title: i18n.lms1705v01['L170M01a.insertData'],
            width: 450,
            height: 60,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(cellvalue, options, rowObject, showMsg, oid, id){
                
                    // 驗證
                    if ($("#custId").val() == "" ||
                    $("#duoNo").val() == "") {
                        // val.required=此為必填欄位.
                        return CommonAPI.showMessage(i18n.def["val.required"]);
                    }
                    $.ajax({
                        handler: "lms1705m01formhandler",
                        type: "POST",
                        dataType: "json",
                        action: "addL170m01a",
                        data: $.extend($("#tabForm").serializeData(), {
                            //  formAction: "addL170m01a",
                            mainOid: oid,
                            showMsg: true,
							tabForm: JSON.stringify($("#tabForm").serializeData())
                        }),
					}).done(function(responseData){
						$('#tabForm').injectData(responseData);
						// CommonAPI.triggerOpener("gridview", "reloadGrid");
						//  $("#gridview").trigger("reloadGrid");
						$('#id').val("");
						$.form.submit({
						    url: '../lrs/LMS1705M01Page/01',
						    data: {
						        formAction: "queryL170m01a",
						        mainId: responseData.mainId,
						        mainDocStatus: viewstatus,
						        mainOid: responseData.oid,
						        custId: $("#custId").val(),
						        duoNo: $("#dupNo").val()
						    
						    },
						    target: "_blank"
						});
						if (responseData.ERRORCODE != '') {
						    $.thickbox.close();
						}
					 })
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };
    });
/*===================================================================================================*/
function BOM(cellvalue, options, rowObject){
    ilog.debug(rowObject);
    $.form.submit({
        url: '../lrs/LMS1705M01Page/01',
        data: {
            formAction: "queryL170m01a",
            mainId: rowObject.mainId,
            mainDocStatus: viewstatus,
            mainOid: rowObject.oid // mainOid 驗證文件狀態 ,權限按鈕顯現
        },
        target: rowObject.oid
    });
    
}
