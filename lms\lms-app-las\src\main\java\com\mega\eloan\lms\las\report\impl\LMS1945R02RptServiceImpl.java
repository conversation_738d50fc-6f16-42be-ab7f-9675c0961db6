package com.mega.eloan.lms.las.report.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.report.ReportGenerator;

import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.las.report.AbstractLasReportMergeService;
import com.mega.eloan.lms.las.report.LMS1905R02RptService;
import com.mega.eloan.lms.las.report.LMS1915R02RptService;
import com.mega.eloan.lms.las.report.LMS1925R02RptService;
import com.mega.eloan.lms.las.report.LMS1945R02RptService;
import com.mega.eloan.lms.las.service.LMS1905Service;
import com.mega.eloan.lms.model.L192M01A;

/**
 * 合併列印工作底稿對帳單內容
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1945r02rptservice")
public class LMS1945R02RptServiceImpl extends AbstractLasReportMergeService
		implements LMS1945R02RptService {

	@Resource
	LMS1905Service lms1905Service;

	@Resource
	LMS1905R02RptService lms1905r02RptService;
	
	@Resource
	LMS1915R02RptService lms1915r02RptService;

	@Resource
	LMS1925R02RptService lms1925r02RptService;

	@Override
	public boolean showPaginate() {
		return false;
	}

	@Override
	public void setLasReportData(ReportGenerator rptGenerator, String mainOid,
			String shtType) {

		if (UtilConstants.ShtType.房貸業務工作底稿.equals(shtType)) {
			rptGenerator.setReportFile(((AbstractReportService)lms1905r02RptService).getReportTemplateFileName());
			lms1905r02RptService.setReport001(rptGenerator, mainOid);
		} else if (UtilConstants.ShtType.授信業務工作底稿.equals(shtType)) {
			rptGenerator.setReportFile(((AbstractReportService)lms1925r02RptService).getReportTemplateFileName());
			lms1925r02RptService.setReport001(rptGenerator, mainOid);
		} else if (UtilConstants.ShtType.團體消貸工作底稿.equals(shtType)) {
			rptGenerator.setReportFile(((AbstractReportService)lms1915r02RptService).getReportTemplateFileName());
			lms1915r02RptService.setReport001(rptGenerator, mainOid);
		}
	}

	@Override
	public L192M01A getLasData(String oid) {
		return lms1905Service.getL192M01A(oid);
	}

}
