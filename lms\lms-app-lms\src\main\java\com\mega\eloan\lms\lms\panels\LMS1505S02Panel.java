/* 
 * LMS1505S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import java.util.LinkedHashSet;
import java.util.Properties;

import org.springframework.ui.ModelMap;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lms.pages.LMS1505M01Page;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;

/**
 * <pre>
 * 小放會會議記錄 - 小放會會議記錄
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
public class LMS1505S02Panel extends Panel {

	public LMS1505S02Panel(String id) {
		super(id);
	}

	public LMS1505S02Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		//UPGRADE
		LinkedHashSet<String> keys = new LinkedHashSet<>();
		keys.add("AbstractEloan");
		keys.add("LMS1505M01");
		RequestContextHolder.getRequestAttributes().setAttribute("basenameKey", keys, RequestAttributes.SCOPE_REQUEST);



		// J-112-0057_05097_B1001 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// eloan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// 1.「帳戶管理員」改「覆核」
		// 2.「紀錄」改「經辦」
		// 3.取消「遵守法令主管」
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1505M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (user.getUnitNo().equals(UtilConstants.BankNo.授管處)) {
			model.addAttribute("L150M01a.accounting", prop.getProperty("L150M01a.approver")); // 覆核
			model.addAttribute("L150M01a.recorder", prop.getProperty("L150M01a.appraiser")); // 經辦
		} else {
			model.addAttribute("L150M01a.accounting", prop.getProperty("L150M01a.accounting")); // 帳戶管理員
			model.addAttribute("L150M01a.recorder", prop.getProperty("L150M01a.recorder")); // 紀錄
		}
	}

	/**/
	private static final long serialVersionUID = 1L;

}
