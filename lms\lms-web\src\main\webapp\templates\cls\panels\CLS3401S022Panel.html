<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">
					loadScript('pagejs/cls/CLS3401S021Panel');
			</script>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.baseInfo'}"><!--基本資訊--></th:block></b>
                    </legend>
                    <table class="tb2 alignTopTab " id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
							<tr>
							<td width="20%" class="hd2" align="right"><th:block th:text="#{'C340M01A.ownBrId'}">分行名稱</th:block>&nbsp;&nbsp;
							</td>
							<td width="30%" >
								<span id="ownBrId" class="field" ></span>&nbsp;<span id="ownBrIdName" class="field" ></span>
							</td>
							<td width="18%" class="hd2" align="right"><th:block th:text="#{'C340M01A.docStatus'}">文件狀態</th:block>&nbsp;&nbsp;
							</td>
							<td width="32%" ><b class="text-red"><span id="docStatus" ></span>&nbsp;</b>
							</td>
						</tr>
						<!--
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.ctrType'}">契約書種類</th:block>&nbsp;&nbsp;
							</td>
							<td ><span id="ctrTypeMapDesc" />
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.contrNumber'}">契約書編號</th:block>&nbsp;
							</td>
							<td><input type='text' id='contrNumber' name='contrNumber' maxlength='60' size='20'  readonly="readonly">
							</td>
						</tr>
						-->
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.custId'}">主要借款人</th:block>&nbsp;&nbsp;
							</td>
							<td colspan='3'><span id="custId" class="field" ></span>-<span id="dupNo" class="field" ></span>&nbsp;&nbsp;<span id="custName"></span>
							</td>
							<!--
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.contrPartyNm'}">契約書客戶名稱</th:block>&nbsp;
							</td>
							<td><input type='text' id='contrPartyNm' name='contrPartyNm' maxlengthC='50' size='30'  readonly="readonly">
							</td>
							-->
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.rptId'}">契約書版本</th:block>&nbsp;&nbsp;
							</td>
							<td colspan='3'><span id="rptId_desc" ></span> 
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C340M01A.ploanCtrNo'}">線上對保契約編號</th:block>&nbsp;
							</td>
							<td><span id='ploanCtrNo' />&nbsp;
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.ploanCtrStatus'}">線上對保契約狀態</th:block>&nbsp;&nbsp;
							</td>
							<td ><b class="text-red"><span id="ploanCtrStatus" /></b>  <!-- <span id="rptId" /> -->
							</td>
						</tr>
						<th:block th:if="${showPloanColumn_visible}">
							<!--
							<th:block th:text="#{'label.ploanDataArea'}">線上對保契約</th:block>
							-->							
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'C340M01A.ploanCtrExprDate'}">線上對保契約期限</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrExprDate' ></span>&nbsp;
							</td>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'C340M01A.ploanCtrSignTimeM'}">線上對保借款人完成時間</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrSignTimeM' ></span>&nbsp;
								<div>
									<span id='ploanBorrowerIPAddr' ></span>&nbsp;
								</div>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'C340M01A.ploanCtrDcTime'}">線上對保契約作廢時間</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrDcTime' />&nbsp;(<span id='ploanCtrDcUser' />)
							</td>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'C340M01A.ploanCtrSignTime1'}">線上對保保證人完成時間</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrSignTime1' />&nbsp;
								<div>
									<span id='ploanStakeholderIPAddr' ></span>&nbsp;
								</div>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'label.ploanCtrBegDateToEndDate'}">線上對保契約起迄日</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanCtrBegDateToEndDate' ></span>&nbsp;
							</td>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'label.ploanStakeholder'}">利害關係人</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='ploanStakeholder' ></span>&nbsp;
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'label.eddaResult'}">eDDA核印結果</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='eddaResult' ></span>&nbsp;
							</td>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'label.eddaResultTime'}">eDDA核印時間</th:block>&nbsp;
							</td>
							<td style='background:#BBCCFF;'><span id='eddaResultTime' ></span>&nbsp;
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" style='background:#BBCCFF;'><th:block th:text="#{'label.ploanUploadGrid'}">線上對保檔案</th:block>&nbsp;
							</td>
							<td colspan='3'>
									<div style="max-width:600px;">
									<div id='ploanUploadGrid'></div>
									</div>
							</td>
						</tr>
						</th:block>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01A.caseNo'}">案號</th:block>&nbsp;&nbsp;</td>
							<td><span id="caseNo" ></span>
								<!-- 
								可能在006號簽報書的性質=新做
								但在008號簽報書的性質=變更條件
								此欄位需要出現在前端，以供釐清是否選錯簽報書 
								-->
							</td>
							<td class="hd2" align="right"><th:block th:text="#{'C340M01B.cntrNo'}">額度序號</th:block>&nbsp;
							</td>
							<td><span id="cntrNo" ></span>
							</td>
						</tr>

							<tr>
								<td class="hd2" align="right">生日</td>
								<td><input type="text" id="borrowerBirthDate" name="borrowerBirthDate" readonly="readonly"></td>
								<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'borrowerMobileNumber'}">手機</th:block></td>
								<td><input type="text" id="borrowerMobileNumber" name="borrowerMobileNumber" ></td>
							</tr>

							<tr>
								<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'borrowerEmail'}">信箱</th:block></td>
								<td><input type="text" id="borrowerEmail" name="borrowerEmail" size='45' ></td>
                                <td class="hd2" align="right">存款帳號</td>
                                <td><input type="text" id="ui_loanAcct" name="ui_loanAcct" readonly="readonly" size='40' >
									<!---
									loanAcct : dwdbBASEService.getGuarOnLineAccount(l140m01a.getCustId())
									-->
									<div>
										<span id="ui_loanAcct_chose" class="text-red"></span><br>
										<span id="ui_loanAcct_chose2" class="text-red"></span>
									</div>
								</td>
							</tr>


							<tr>
								<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">借款條件</td>
							</tr>
						<tr>
							<td class="hd2" align="right">借款金額</td>
							<td><input type="text" id="loanAmt" name="loanAmt" class="numeric" size="4">萬元</td>
							<td class="hd2" align="right">借款期間(月)</td>
							<td><input type="text" id="loanPeriod" name="loanPeriod" class="numeric" readonly="readonly" size="2">月</td>
						</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'preliminaryFee'}">開辦手續費</th:block></td>
								<td><input type="text" id="preliminaryFee" name="preliminaryFee" class="numeric" size="3">元</td>
								<td class="hd2" align="right"><th:block th:text="#{'creditCheckFee'}">信用查詢費</th:block></td>
								<td><input type="text" id="creditCheckFee" name="creditCheckFee" class="numeric" size="3">元</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'loanPurpose'}">借款用途</th:block></td>
								<td colspan='3'>
									<label style="display:none"><input type='checkbox' id='loanPurpose_C' name='loanPurpose_C' value='Y'><th:block th:text="#{'loanPurpose.C'}">週轉</th:block></label>
									<label style="display:none">&nbsp;&nbsp;<input type='checkbox' id='loanPurpose_H' name='loanPurpose_H' value='Y'><th:block th:text="#{'loanPurpose.H'}">購置耐久性消費品</th:block></label>
									<label style="display:none">&nbsp;&nbsp;<input type='checkbox' id='loanPurpose_J' name='loanPurpose_J' value='Y'><th:block th:text="#{'loanPurpose.J'}">子女教育</th:block></label>
									<label style="display:none">&nbsp;&nbsp;<input type='checkbox' id='loanPurpose_K' name='loanPurpose_K' value='Y'><th:block th:text="#{'loanPurpose.K'}">繳稅</th:block></label>
									<label style="display:none">&nbsp;&nbsp;<input type='checkbox' id='loanPurpose_F' name='loanPurpose_F' value='Y'><th:block th:text="#{'loanPurpose.F'}">留學/遊學</th:block></label>

									<label><input type='checkbox' id='loanPurpose_N' name='loanPurpose_N' value='Y'><th:block th:text="#{'loanPurpose.N'}">個人週轉金</th:block></label>
									<label>&nbsp;&nbsp;<input type='checkbox' id='loanPurpose_O' name='loanPurpose_O' value='Y'><th:block th:text="#{'loanPurpose.O'}">個人消費性用途</th:block></label>
									<label>&nbsp;&nbsp;<input type='checkbox' id='loanPurpose_G' name='loanPurpose_G' value='Y'><th:block th:text="#{'loanPurpose.G'}">投資理財</th:block></label>
									<label>&nbsp;&nbsp;<input type='checkbox' id='loanPurpose_P' name='loanPurpose_P' value='Y'><th:block th:text="#{'loanPurpose.P'}">企業員工認購股票</th:block></label>
									<div>
										<label><th:block th:text="#{'loanPurpose.otherDesc'}">其他</th:block>：</label>
										<input type="text" id="loanPurpose_otherDesc" name="loanPurpose_otherDesc" size="60">
									</div>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><th:block th:text="#{'rawDownType'}">動用方式</th:block></td>
								<td><input type="text" id="drawDownType" name="drawDownType" readonly="readonly"></td>
								<td class="hd2" align="right"><th:block th:text="#{'repaymentMethod'}">還款方式</th:block></td>
								<td><input type="text" id="repaymentMethod" name="repaymentMethod" readonly="readonly"></td>
							</tr>
							<tr>
								<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">借款方案：<span id="lendingPlanInfo_showOption" name="lendingPlanInfo_showOption" class="field"></span></td>
							</tr>
							<tr>	
								<td class="hd2" align="right"><span id="rateTitle" name="rateTitle" class="field">利率</span></td>
								<td colspan="3">
									<!--<textarea name="" id="" cols="30" rows="5">等消金處給format，原則上由額度名細表帶入，可修改，但數字不得優於額度明細表</textarea>-->
									<div id="rateRange1View">
										<input type="hidden" id="rateRange1" name="rateRange1">
										<input type="hidden" id="rateRange1Oid" name="rateRange1Oid">
										<div id="rateRange1View_default">
										<span>第<span id="rateRange1Param01" class="field"></span>期至第<span id="rateRange1Param02" class="field"></span>期，</span><span id="rateRange1Type01View"><span>固定年利率<span id="rateRange1Param03" class="field"></span>%。</span></span>
										<!--<br>-->
										<span id="rateRange1TypeOtherView">依撥款日乙方公告之<span id="rateRange1Param04" class="field">消費金融放款指標利率</span><span id="rateRange1Param05" class="field"></span>%加計年利率
											<input type="text" id="rateRange1Param06" name="rateRange1Param06" class="numeric" size="3" integer="3" fraction="3"/>
											%浮動計息，目前合計為年利率<span id="rateRange1Param07" class="field"></span>%。</span>
										</div>
										<div id="rateRange1View_M3" style="display:none">
											<span id="rateRange1ParamM3" ><!-- M3 是機動計息 --></span>
										</div>
									</div>
									<div id="rateRange2View" style="display:none">
										<input type="hidden" id="rateRange2" name="rateRange2">
										<input type="hidden" id="rateRange2Oid" name="rateRange2Oid">
										<span>第<span id="rateRange2Param01" class="field"></span>期至第<span id="rateRange2Param02" class="field"></span>期，</span><span id="rateRange2Type01View"><span>固定年利率<span id="rateRange2Param03" class="field"></span>%。</span></span>
										<!--<br>-->
										<span id="rateRange2TypeOtherView">依撥款日乙方公告之<span id="rateRange2Param04" class="field">消費金融放款指標利率</span><span id="rateRange2Param05" class="field"></span>%加計年利率
											<input type="text" id="rateRange2Param06" name="rateRange2Param06" class="numeric" size="3" integer="3" fraction="3"/>
											%浮動計息，目前合計為年利率<span id="rateRange2Param07" class="field"></span>%。</span>
									</div>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right"><span id="redemptionTitle" name="redemptionTitle" class="field"></span></td>
								<td colspan="3"><textarea name="redemptionDesc" id="redemptionDesc" cols="100" rows="4" readonly="readonly"></textarea></td>
							</tr>
							<th:block th:if="${showOtherInfoTitle_visible}">
							<tr>
								<td class="hd2" align="right"><span id='otherInfoTitle' />&nbsp;
								</td>
								<td colspan='3'><textarea name="otherInfoDesc" id="otherInfoDesc" cols="80" rows="4" readonly="readonly"></textarea>&nbsp;
								</td>
							</tr>
							</th:block>
							<tr>
								<td class="hd2" align="right">總費用年百分率</td>
								<td colspan="3">
									<input type="text" name="annualPercentageRate" id="annualPercentageRate" class="numeric" integer="3" fraction="2" size="2">%
									<button id="btnCalculateRate" type="button" onclick="queryCalculateRate()">重新計算</button>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right">管轄法院</td>
								<td colspan="3"><th:block th:text="#{'label.countName.prefix'}">本契約涉訟時，除法律規定具管轄權之法院外，甲乙雙方及保證人同意以臺灣臺北地方法院或</th:block>
												<input type="text" name="courtName" id="courtName" size="10">
												<th:block th:text="#{'label.countName.postfix'}">法院為第一審管轄法院。但法律有專屬管轄法院之特別規定者，從其規定。</th:block>
								</td>
							</tr>
						<!--<tr>-->
							<!--<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">保證人資料</td>-->
							<!--&lt;!&ndash;<td></td>&ndash;&gt;-->
							<!--&lt;!&ndash;<td></td>&ndash;&gt;-->
							<!--&lt;!&ndash;<td></td>&ndash;&gt;-->
						<!--</tr>-->
						<!--<tr>-->
							<!--<td class="hd2" align="right">身分證字號</td>-->
							<!--<td><input type="text" id="relatedPersonId" name="relatedPersonId" readonly="readonly"></td>-->
							<!--<td class="hd2" align="right">姓名</td>-->
							<!--<td><input type="text" id="relatedPersonName" name="relatedPersonName" readonly="readonly"></td>-->
						<!--</tr>-->
						<!--<tr>-->
							<!--<td class="hd2" align="right">生日</td>-->
							<!--<td><input type="text" id="relatedPersonBirthDate" name="relatedPersonBirthDate" readonly="readonly"></td>-->
							<!--<td class="hd2" align="right">手機</td>-->
							<!--<td><input type="text" id="relatedPersonMobileNumber" name="relatedPersonMobileNumber" readonly="readonly"></td>-->
						<!--</tr>-->
						<!--<tr>-->
							<!--<td class="hd2" align="right">信箱</td>-->
							<!--<td><input type="text" id="relatedPersonEmail" name="relatedPersonEmail" readonly="readonly" size='45'></td>-->
							<!--<td class="hd2" align="right">類型</td>-->
							<!--<td><input type="hidden" id="relatedPersonType" name="relatedPersonType">-->
								<!--<input type="text" id="relatedPersonTypeDesc" name="relatedPersonTypeDesc" readonly="readonly">-->
							<!--</td>-->
						<!--</tr>-->
						<!--<tr>-->
							<!--<td class="hd2" align="right">保證條款</td>-->
							<!--<td colspan='3'><span id="guaranteePlan" name="guaranteePlan" class="field"></span>-->
							<!--</td>-->
						<!--</tr>-->
						<!--<tr>-->
							<!--<td class="hd2" align="right">主債務</td>-->
							<!--<td colspan='3'><input type="text" id="guaranteeAmt" name="guaranteeAmt" class="numeric" size="4" ><span id="guaranteeAmtDollarUnit">萬元</span>-->
							<!--</td>-->
						<!--</tr>-->
						<!--&lt;!&ndash; ****** C101 開始 ***** &ndash;&gt;-->
						<!--<tr class="dataForLoanPlan_C101" style="display:none;">-->
							<!--<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">認購股票款項支取/匯款授權書</td>-->
						<!--</tr>-->
						<!--<tr class="dataForLoanPlan_C101" style="display:none;">-->
							<!--<td class="hd2" align="right"><th:block th:text="#{'payeeBankCode'}">指定帳戶銀行</th:block></td>-->
							<!--<td><input type="text" name="payeeBankCode" id="payeeBankCode" size="7" maxlength="3">-->
							<!--</td>-->
							<!--<td class="hd2" align="right"><th:block th:text="#{'payeeBankAccountNo'}">指定帳戶帳號</th:block></td>-->
							<!--<td><input type="text" name="payeeBankAccountNo" id="payeeBankAccountNo" size="16" maxlength="16" >-->
							<!--</td>-->
						<!--</tr>-->
						<!--<tr class="dataForLoanPlan_C101" style="display:none;">-->
							<!--<td class="hd2" align="right"><th:block th:text="#{'payeeBankAccountName'}">指定帳戶戶名</th:block></td>-->
							<!--<td><input type="text" name="payeeBankAccountName" id="payeeBankAccountName" size="30">-->
							<!--</td>-->
							<!--<td class="hd2" align="right"><th:block th:text="#{'payeeTotalAmt'}">金額(含匯款手續費)</th:block></td>-->
							<!--<td><input type="text" name="payeeTotalAmt" id="payeeTotalAmt" size="14" class="numeric" >元-->
								<!--<div>-->
									<!--認股金額：<input id="subscribeAmt" name="subscribeAmt" class="numeric" readonly="readonly" size="8">元-->
								<!--</div>	-->
							<!--</td>-->
						<!--</tr>-->
						<!--<tr class="dataForLoanPlan_C101" style="display:none;">-->
							<!--<td class="hd2" align="right"><th:block th:text="#{'payeeSelfProvide'}">自備款</th:block></td>-->
							<!--<td><input type="text" name="payeeSelfProvide" id="payeeSelfProvide" size="14" class="numeric" >元-->
							<!--</td>-->
							<!--<td class="hd2" align="right"><th:block th:text="#{'payeeRemittance'}">匯款手續費</th:block></td>-->
							<!--<td><input type="text" name="payeeRemittance" id="payeeRemittance" size="14" class="numeric" >元-->
							<!--</td>-->
						<!--</tr>-->
						<!-- ****** C101 結尾 ***** -->
                        </tbody>
                    </table>
					<table class="tb2 alignTopTab " id="repaymentForm" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">代償資訊</td>
							<!--<td></td>-->
							<!--<td></td>-->
							<!--<td></td>-->
						</tr>
						<tr>
							<td class="hd2" align="center">
								金融機構
							</td>
							<td class="hd2"  align="center">
								代償產品別
							</td>
							<td class="hd2"  align="center" colspan="2">
								聯徵原始額度
							</td>
						</tr>
						<tbody id="tb4">
						</tbody>
					</table>
					<table class="tb2 alignTopTab " id="top_part2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tbody>
							<tr>
								<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">保證人資料</td>
								<!--<td></td>-->
								<!--<td></td>-->
								<!--<td></td>-->
							</tr>
							<tr>
								<td class="hd2" align="right">身分證字號</td>
								<td><input type="text" id="relatedPersonId" name="relatedPersonId" readonly="readonly"></td>
								<td class="hd2" align="right">姓名</td>
								<td><input type="text" id="relatedPersonName" name="relatedPersonName" readonly="readonly"></td>
							</tr>
							<tr>
								<td class="hd2" align="right">生日</td>
								<td><input type="text" id="relatedPersonBirthDate" name="relatedPersonBirthDate" readonly="readonly"></td>
								<td class="hd2" align="right">手機</td>
								<td><input type="text" id="relatedPersonMobileNumber" name="relatedPersonMobileNumber" readonly="readonly"></td>
							</tr>
							<tr>
								<td class="hd2" align="right">信箱</td>
								<td><input type="text" id="relatedPersonEmail" name="relatedPersonEmail" readonly="readonly" size='45'></td>
								<td class="hd2" align="right">類型</td>
								<td><input type="hidden" id="relatedPersonType" name="relatedPersonType">
									<input type="text" id="relatedPersonTypeDesc" name="relatedPersonTypeDesc" readonly="readonly">
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right">保證條款</td>
								<td colspan='3'><span id="guaranteePlan" name="guaranteePlan" class="field"></span>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="right">主債務</td>
								<td colspan='3'><input type="text" id="guaranteeAmt" name="guaranteeAmt" class="numeric" size="4" ><span id="guaranteeAmtDollarUnit">萬元</span>
								</td>
							</tr>
							<!-- ****** C101 開始 ***** -->
							<tr class="dataForLoanPlan_C101" style="display:none;">
								<td class="ct hd2" colspan="4" style="background-color:rgb(186, 214, 255)">認購股票款項支取/匯款授權書</td>
							</tr>
							<tr class="dataForLoanPlan_C101" style="display:none;">
								<td class="hd2" align="right"><th:block th:text="#{'payeeBankCode'}">指定帳戶銀行</th:block></td>
								<td><input type="text" name="payeeBankCode" id="payeeBankCode" size="7" maxlength="3">
								</td>
								<td class="hd2" align="right"><th:block th:text="#{'payeeBankAccountNo'}">指定帳戶帳號</th:block></td>
								<td><input type="text" name="payeeBankAccountNo" id="payeeBankAccountNo" size="16" maxlength="16" >
								</td>
							</tr>
							<tr class="dataForLoanPlan_C101" style="display:none;">
								<td class="hd2" align="right"><th:block th:text="#{'payeeBankAccountName'}">指定帳戶戶名</th:block></td>
								<td><input type="text" name="payeeBankAccountName" id="payeeBankAccountName" size="30">
								</td>
								<td class="hd2" align="right"><th:block th:text="#{'payeeTotalAmt'}">金額(含匯款手續費)</th:block></td>
								<td><input type="text" name="payeeTotalAmt" id="payeeTotalAmt" size="14" class="numeric" >元
									<div>
										認股金額：<input id="subscribeAmt" name="subscribeAmt" class="numeric" readonly="readonly" size="8">元
									</div>
								</td>
							</tr>
							<tr class="dataForLoanPlan_C101" style="display:none;">
								<td class="hd2" align="right"><th:block th:text="#{'payeeSelfProvide'}">自備款</th:block></td>
								<td><input type="text" name="payeeSelfProvide" id="payeeSelfProvide" size="14" class="numeric" >元
								</td>
								<td class="hd2" align="right"><th:block th:text="#{'payeeRemittance'}">匯款手續費</th:block></td>
								<td><input type="text" name="payeeRemittance" id="payeeRemittance" size="14" class="numeric" >元
								</td>
							</tr>
							<!-- ****** C101 結尾 ***** -->
						</tbody>
					</table>
                </fieldset>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}"><!-- 文件異動紀錄 --></th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer">
                            <!-- 文件異動紀錄 --><div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
                        </div>
                    </div>
                    <table class="tb2" id="top_part" width="100%" border="0" cellspacing="0" cellpadding="0" style="display:none">
                        <tbody>
                            <tr>
                                <td width="15%" class="hd1">
                                    <th:block th:text="#{'doc.creator'}"><!--  文件建立者--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="35%">
                                    <span id='creator'></span>(<span id='createTime'></span>)
                                </td>
                                <td width="15%" class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}"><!--  最後異動者--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="35%">
                                    <span id='updater'></span>(<span id='updateTime'></span>)
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
               
           
        </th:block>
    </body>
</html>