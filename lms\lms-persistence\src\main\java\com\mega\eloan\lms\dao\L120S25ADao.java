/* 
 * L120S25ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S25A;

/** BIS評估表 **/
public interface L120S25ADao extends IGenericDao<L120S25A> {

	L120S25A findByOid(String oid);

	List<L120S25A> findByMainId(String mainId);

	List<L120S25A> findByIndex01(String mainId);

	List<L120S25A> findByIndex02(String mainId, String bisCntrNo_s25a);

	List<L120S25A> findByMainIdAndCustId(String mainId, String bisCustId_s25a,
			String bisDupNo_s25a);
}