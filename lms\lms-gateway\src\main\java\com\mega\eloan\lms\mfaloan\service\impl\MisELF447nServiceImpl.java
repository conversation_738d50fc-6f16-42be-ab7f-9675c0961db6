package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisELF447nService;

/**
 * <pre>
 * 核准額度資料檔  MIS.ELF447n
 * </pre>
 * 
 * @since 2012/6/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/6/8,REX,new
 *          </ul>
 */
@Service
public class MisELF447nServiceImpl extends AbstractMFAloanJdbc implements
		MisELF447nService {

	@Override
	public void delByCustKey(String custId, String dupNo, String cntrNo) {
		this.getJdbc().update("MISELF447N.delByCsutKey",
				new Object[] { custId, dupNo, cntrNo });
	}

	@Override
	public void deleteByunId(String oid) {
		this.getJdbc().update("MISELF447N.delByOid", new Object[] { oid });

	}

	// J-111-0283 E-LOAN簽報，關係企業額度已逾分行權限，需覆核主管覆核，並產生檢核報表
	@Override
	public void insert(String ELF447N_UNID, String ELF447N_PROJECT_NO,
			String ELF447N_CLASS, String ELF447N_CUSTID, String ELF447N_DUPNO,
			String ELF447N_STATUS, String ELF447N_STATUS_DT,
			String ELF447N_CONTRACT, String ELF447N_BRANCH,
			String ELF447N_PROCESS_BR, String ELF447N_FACT_TYPE,
			String ELF447N_SYSTYPE, String ELF447N_CASELEVEL,
			String ELF447N_PROPERTY, BigDecimal ELF447N_CURAMT,
			String ELF447N_CURR, BigDecimal ELF447N_OLDAMT,
			String ELF447N_OLDCURR, String ELF447N_GRPNO,
			String ELF447N_RISK_CNTRY, String ELF447N_RISK_AREA,
			String ELF447N_BUS_CD, String ELF447N_BUS_SUB_CD,
			String ELF447N_BUILD_NAME, String ELF447N_SITE1,
			String ELF447N_SITE2, String ELF447N_SITE3, String ELF447N_SITE4,
			String ELF447N_NUSEDATE, String ELF447N_NUSEMEMO,
			Timestamp ELF447N_TMESTAMP, String ELF447N_INT_MEMO,
			String ELF447N_RESIDENce, String ELF447N_PROD_CLASS,
			BigDecimal ELF447N_LAND_AREA, Date ELF447N_BUILD_DATE,
			BigDecimal ELF447N_WAIT_MONTH, String ELF447N_LOCATE_CD,
			BigDecimal ELF447N_SITE3NO, String ELF447N_SITE4NO,
			String ELF447N_LAND_TYPE, String ELF447N_PROPERTIES,
			String ELF447N_REVIEWBR, String ELF447N_ISHEDGE,
			BigDecimal ELF447N_ENHANCEAMT, String ELF447N_PROJ_CLASS,
			String ELF447N_ENDDATE, String ELF447N_RGTCURR,
			BigDecimal ELF447N_RGTAMT, BigDecimal ELF447N_RGTUNIT,
			String ELF447N_COND_CHG, String ELF447N_INTREG,
			String ELF447N_ISRESCUE, String ELF447N_RESCUEITEM,
			BigDecimal ELF447N_RESCUERATE, String ELF447N_ACT_CODE,
			String ELF447N_OVAUTH_LN, String ELF447N_OVAUTH_EX,
			String ELF447N_OVAUTH_AL, String ELF447N_LNNOFLAG,
			BigDecimal ELF447N_CURAMT_S, BigDecimal ELF447N_CURAMT_N) {
		this.getJdbc().update(
				"MISELF447N.insert",
				new Object[] { ELF447N_UNID, ELF447N_PROJECT_NO, ELF447N_CLASS,
						ELF447N_CUSTID, ELF447N_DUPNO, ELF447N_STATUS,
						ELF447N_STATUS_DT, ELF447N_CONTRACT, ELF447N_BRANCH,
						ELF447N_PROCESS_BR, ELF447N_FACT_TYPE, ELF447N_SYSTYPE,
						ELF447N_CASELEVEL, ELF447N_PROPERTY, ELF447N_CURAMT,
						ELF447N_CURR, ELF447N_OLDAMT, ELF447N_OLDCURR,
						ELF447N_GRPNO, ELF447N_RISK_CNTRY, ELF447N_RISK_AREA,
						ELF447N_BUS_CD, ELF447N_BUS_SUB_CD, ELF447N_BUILD_NAME,
						ELF447N_SITE1, ELF447N_SITE2, ELF447N_SITE3,
						ELF447N_SITE4, ELF447N_NUSEDATE, ELF447N_NUSEMEMO,
						ELF447N_TMESTAMP, ELF447N_INT_MEMO, ELF447N_RESIDENce,
						ELF447N_PROD_CLASS, ELF447N_LAND_AREA,
						ELF447N_BUILD_DATE, ELF447N_WAIT_MONTH,
						ELF447N_LOCATE_CD, ELF447N_SITE3NO, ELF447N_SITE4NO,
						ELF447N_LAND_TYPE, ELF447N_PROPERTIES,
						ELF447N_REVIEWBR, ELF447N_ISHEDGE, ELF447N_ENHANCEAMT,
						ELF447N_PROJ_CLASS, ELF447N_ENDDATE, ELF447N_RGTCURR,
						ELF447N_RGTAMT, ELF447N_RGTUNIT, ELF447N_COND_CHG,
						ELF447N_INTREG, ELF447N_ISRESCUE, ELF447N_RESCUEITEM,
						ELF447N_RESCUERATE, ELF447N_ACT_CODE,
						ELF447N_OVAUTH_LN, ELF447N_OVAUTH_EX,
						ELF447N_OVAUTH_AL, ELF447N_LNNOFLAG, ELF447N_CURAMT_S,
						ELF447N_CURAMT_N });

	}

	/**
	 * J-111-0551_05097_B1003 Web
	 * e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
	 */
	@Override
	public int updateByUnidAndContract(String ELF447N_STATUS,
			String ELF447N_STATUS_DT, String ELF447N_NUSEDATE,
			String ELF447N_NUSEMEMO, String ELF447N_NSIGN_CODE,
			String ELF447N_NSIGN_MEMO, String unid, String cntrNo,
			BigDecimal ELF447N_CURAMT_S, BigDecimal ELF447N_CURAMT_N) {
		int count = this.getJdbc().update(
				"MISELF447N.updateByUnidAndContract",
				new Object[] { ELF447N_STATUS, ELF447N_STATUS_DT,
						ELF447N_NUSEDATE,
						Util.trimSizeInOS390(ELF447N_NUSEMEMO, 800),
						ELF447N_NSIGN_CODE, ELF447N_NSIGN_MEMO,
						ELF447N_CURAMT_S, ELF447N_CURAMT_N, unid, cntrNo });
		return count;
	}

	@Override
	public void insertByCls(String ELF447N_UNID, String ELF447N_PROJECT_NO,
			String ELF447N_CLASS, String ELF447N_CUSTID, String ELF447N_DUPNO,
			String ELF447N_STATUS, String ELF447N_STATUS_DT,
			String ELF447N_CONTRACT, String ELF447N_BRANCH,
			String ELF447N_PROCESS_BR, String ELF447N_FACT_TYPE,
			String ELF447N_SYSTYPE, String ELF447N_CASELEVEL,
			String ELF447N_PROPERTY, BigDecimal ELF447N_CURAMT,
			String ELF447N_CURR, BigDecimal ELF447N_OLDAMT,
			String ELF447N_OLDCURR, String ELF447N_GRPNO,
			String ELF447N_RISK_CNTRY, String ELF447N_RISK_AREA,
			String ELF447N_BUS_CD, String ELF447N_BUS_SUB_CD,
			String ELF447N_BUILD_NAME, String ELF447N_SITE1,
			String ELF447N_SITE2, String ELF447N_SITE3, String ELF447N_SITE4,
			String ELF447N_NUSEDATE, String ELF447N_NUSEMEMO,
			Timestamp ELF447N_TMESTAMP, String ELF447N_INT_MEMO,
			String ELF447N_RESIDENce, BigDecimal ELF447N_INT_Rate,
			String ELF447N_PROD_CLASS, String ELF447N_ACT_CODE,
			String ELF447N_PURPOSE, BigDecimal ELF447N_LAND_AREA,
			Date ELF447N_BUILD_DATE, BigDecimal ELF447N_WAIT_MONTH,
			String ELF447N_LOCATE_CD, BigDecimal ELF447N_SITE3NO,
			String ELF447N_SITE4NO, String ELF447N_LAND_TYPE,
			String ELF447N_PROPERTIES, String ELF447N_ENDDATE,
			String ELF447N_RGTCURR, BigDecimal ELF447N_RGTAMT,
			BigDecimal ELF447N_RGTUNIT, String ELF447N_COND_CHG,
			String ELF447N_INTREG, String ELF447N_LNNOFLAG,
			BigDecimal ELF447N_CURAMT_S, BigDecimal ELF447N_CURAMT_N) {
		this.getJdbc().update(
				"MISELF447N.insertByCls",
				new Object[] { ELF447N_UNID, ELF447N_PROJECT_NO, ELF447N_CLASS,
						ELF447N_CUSTID, ELF447N_DUPNO, ELF447N_STATUS,
						ELF447N_STATUS_DT, ELF447N_CONTRACT, ELF447N_BRANCH,
						ELF447N_PROCESS_BR, ELF447N_FACT_TYPE, ELF447N_SYSTYPE,
						ELF447N_CASELEVEL, ELF447N_PROPERTY, ELF447N_CURAMT,
						ELF447N_CURR, ELF447N_OLDAMT, ELF447N_OLDCURR,
						ELF447N_GRPNO, ELF447N_RISK_CNTRY, ELF447N_RISK_AREA,
						ELF447N_BUS_CD, ELF447N_BUS_SUB_CD, ELF447N_BUILD_NAME,
						ELF447N_SITE1, ELF447N_SITE2, ELF447N_SITE3,
						ELF447N_SITE4, ELF447N_NUSEDATE, ELF447N_NUSEMEMO,
						ELF447N_TMESTAMP, ELF447N_INT_MEMO, ELF447N_RESIDENce,
						ELF447N_INT_Rate, ELF447N_PROD_CLASS, ELF447N_ACT_CODE,
						ELF447N_PURPOSE, ELF447N_LAND_AREA, ELF447N_BUILD_DATE,
						ELF447N_WAIT_MONTH, ELF447N_LOCATE_CD, ELF447N_SITE3NO,
						ELF447N_SITE4NO, ELF447N_LAND_TYPE, ELF447N_PROPERTIES,
						ELF447N_ENDDATE, ELF447N_RGTCURR, ELF447N_RGTAMT,
						ELF447N_RGTUNIT, ELF447N_COND_CHG, ELF447N_INTREG,
						ELF447N_LNNOFLAG, ELF447N_CURAMT_S, ELF447N_CURAMT_N });

	}

	@Override
	public void insertByL2305FLow(String ELF447N_UNID,
			String ELF447N_PROJECT_NO, String ELF447N_CLASS,
			String ELF447N_CUSTID, String ELF447N_DUPNO, String ELF447N_STATUS,
			String ELF447N_STATUS_DT, String ELF447N_CONTRACT,
			String ELF447N_BRANCH, String ELF447N_PROCESS_BR,
			String ELF447N_FACT_TYPE, String ELF447N_SYSTYPE,
			String ELF447N_CASELEVEL, String ELF447N_PROPERTY,
			BigDecimal ELF447N_CURAMT, String ELF447N_CURR,
			BigDecimal ELF447N_OLDAMT, String ELF447N_OLDCURR,
			String ELF447N_GRPNO, String ELF447N_RISK_CNTRY,
			String ELF447N_RISK_AREA, String ELF447N_BUS_CD,
			String ELF447N_BUS_SUB_CD, String ELF447N_BUILD_NAME,
			String ELF447N_SITE1, String ELF447N_SITE2, String ELF447N_SITE3,
			String ELF447N_SITE4, String ELF447N_NUSEDATE,
			String ELF447N_NUSEMEMO, Timestamp ELF447N_TMESTAMP,
			String ELF447N_INT_MEMO, String ELF447N_RESIDENce,
			String ELF447N_NSIGN_CODE, String ELF447N_NSIGN_MEMO,
			String ELF447N_PROD_CLASS, BigDecimal ELF447N_LAND_AREA,
			Date ELF447N_BUILD_DATE, BigDecimal ELF447N_WAIT_MONTH,
			String ELF447N_LOCATE_CD, BigDecimal ELF447N_SITE3NO,
			String ELF447N_SITE4NO, String ELF447N_LAND_TYPE,
			String ELF447N_PROPERTIES, String ELF447N_REVIEWBR,
			String ELF447N_ISHEDGE, BigDecimal ELF447N_ENHANCEAMT,
			String ELF447N_PROJ_CLASS, String ELF447N_ENDDATE,
			String ELF447N_RGTCURR, BigDecimal ELF447N_RGTAMT,
			BigDecimal ELF447N_RGTUNIT, BigDecimal ELF447N_CURAMT_S,
			BigDecimal ELF447N_CURAMT_N) {
		this.getJdbc().update(
				"MISELF447N.insertByL2305FLow",
				new Object[] { ELF447N_UNID, ELF447N_PROJECT_NO, ELF447N_CLASS,
						ELF447N_CUSTID, ELF447N_DUPNO, ELF447N_STATUS,
						ELF447N_STATUS_DT, ELF447N_CONTRACT, ELF447N_BRANCH,
						ELF447N_PROCESS_BR, ELF447N_FACT_TYPE, ELF447N_SYSTYPE,
						ELF447N_CASELEVEL, ELF447N_PROPERTY, ELF447N_CURAMT,
						ELF447N_CURR, ELF447N_OLDAMT, ELF447N_OLDCURR,
						ELF447N_GRPNO, ELF447N_RISK_CNTRY, ELF447N_RISK_AREA,
						ELF447N_BUS_CD, ELF447N_BUS_SUB_CD, ELF447N_BUILD_NAME,
						ELF447N_SITE1, ELF447N_SITE2, ELF447N_SITE3,
						ELF447N_SITE4, ELF447N_NUSEDATE, ELF447N_NUSEMEMO,
						ELF447N_TMESTAMP, ELF447N_INT_MEMO, ELF447N_RESIDENce,
						ELF447N_NSIGN_CODE, ELF447N_NSIGN_MEMO,
						ELF447N_PROD_CLASS, ELF447N_LAND_AREA,
						ELF447N_BUILD_DATE, ELF447N_WAIT_MONTH,
						ELF447N_LOCATE_CD, ELF447N_SITE3NO, ELF447N_SITE4NO,
						ELF447N_LAND_TYPE, ELF447N_PROPERTIES,
						ELF447N_REVIEWBR, ELF447N_ISHEDGE, ELF447N_ENHANCEAMT,
						ELF447N_PROJ_CLASS, ELF447N_ENDDATE, ELF447N_RGTCURR,
						ELF447N_RGTAMT, ELF447N_RGTUNIT, ELF447N_CURAMT_S,
						ELF447N_CURAMT_N });

	}

	/**
	 * J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 */
	@Override
	public Map<String, Object> findByMaxChkDate(String custId, String dupNo,
			String cntrNo) {
		return this.getJdbc().queryForMap("MIS.ELF447N_byMaxChkDate",
				new Object[] { custId, dupNo, cntrNo });
	}

	/**
	 * J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
	 * 
	 * @param ELF447N_UNID
	 * @param ELF447N_CUSTID
	 * @param ELF447N_DUPNO
	 * @param ELF447N_ENDDATE
	 * @param ELF447N_RGTCURR
	 * @param ELF447N_RGTAMT
	 * @param ELF447N_RGTUNIT
	 * @return
	 */
	@Override
	public int updateByUnidAndCustId(String ELF447N_UNID,
			String ELF447N_CUSTID, String ELF447N_DUPNO,
			String ELF447N_ENDDATE, String ELF447N_RGTCURR,
			BigDecimal ELF447N_RGTAMT, BigDecimal ELF447N_RGTUNIT) {

		int count = 0;

		if (Util.equals(ELF447N_CUSTID, "") && Util.equals(ELF447N_DUPNO, "")) {
			// 消金
			count = this.getJdbc().update(
					"MISELF447N.updateEndDateByUnidAndCustId_cls",
					new Object[] { ELF447N_ENDDATE, ELF447N_RGTCURR,
							ELF447N_RGTAMT, ELF447N_RGTUNIT, ELF447N_UNID });
		} else {
			count = this.getJdbc().update(
					"MISELF447N.updateEndDateByUnidAndCustId",
					new Object[] { ELF447N_ENDDATE, ELF447N_RGTCURR,
							ELF447N_RGTAMT, ELF447N_RGTUNIT, ELF447N_UNID,
							ELF447N_CUSTID, ELF447N_DUPNO });
		}

		return count;
	}

	/**
	 * J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
	 * 
	 * @param ELF447N_UNID
	 * @param ELF447N_CUSTID
	 * @param ELF447N_DUPNO
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findByUnidAndCustId(String ELF447N_UNID,
			String ELF447N_CUSTID, String ELF447N_DUPNO) {

		if (Util.equals(ELF447N_CUSTID, "") && Util.equals(ELF447N_DUPNO, "")) {
			// 消金
			return this.getJdbc().queryForListWithMax(
					"MISELF447N.selByUnidAndCustId_cls",
					new Object[] { ELF447N_UNID });
		} else {
			return this.getJdbc()
					.queryForListWithMax(
							"MISELF447N.selByUnidAndCustId",
							new Object[] { ELF447N_UNID, ELF447N_CUSTID,
									ELF447N_DUPNO });
		}

	}

	@Override
	public void deleteByunIdAndCntrNo(String oid, String cntrNo) {
		this.getJdbc().update("MISELF447N.delByOidAndCntrNo",
				new Object[] { oid, cntrNo });

	}

	@Override
	public int updateRescueByUnidAndContract(String ELF447N_ISRESCUE,
			String ELF447N_RESCUEITEM, BigDecimal ELF447N_RESCUERATE,
			String unid, String cntrNo) {
		int count = this.getJdbc().update(
				"MISELF447N.updateRescueByUnidAndContract",
				new Object[] { ELF447N_ISRESCUE, ELF447N_RESCUEITEM,
						ELF447N_RESCUERATE, unid, cntrNo });
		return count;
	}

	@Override
	public List<Map<String, Object>> findByUnid(String ELF447N_UNID) {

		return this.getJdbc().queryForListWithMax("MIS.ELF447N_findByUnid",
				new Object[] { ELF447N_UNID });

	}

	@Override
	public Map<String, Object> findByUnidAndCntrNo(String ELF447N_UNID,
			String ELF447N_CONTRACT) {

		return this.getJdbc().queryForMap("MIS.ELF447N_findByUnidAndCntrNo",
				new Object[] { ELF447N_UNID, ELF447N_CONTRACT });

	}

	/**
	 * M-109-0210_05097_B1001 Web e-Loan企金授信配合DW產生額度未引入帳務系統之報表，上傳授信科目到ELF447N
	 * 
	 * @param ELF447N_UNID
	 * @param ELF447N_CUSTID
	 * @param ELF447N_DUPNO
	 * @param ELF447N_CONTRACT
	 * @param ELF447N_ACT_CODE
	 * @return
	 */
	@Override
	public int updateActCodeByUnidCustIdCntrNo(String ELF447N_UNID,
			String ELF447N_CUSTID, String ELF447N_DUPNO,
			String ELF447N_CONTRACT, String ELF447N_ACT_CODE) {

		int count = 0;

		count = this.getJdbc().update(
				"MISELF447N.updateActCodeByUnidCustIdCntrNo",
				new Object[] { ELF447N_ACT_CODE, ELF447N_UNID, ELF447N_CUSTID,
						ELF447N_DUPNO, ELF447N_CONTRACT });

		return count;
	}

	@Override
	public List<Map<String, Object>> getByCntrNoOrderByEndDateDesc(
			String ELF447N_CONTRACT) {
		return this.getJdbc().queryForListWithMax("MIS.ELF447N_findByCntrNo",
				new Object[] { ELF447N_CONTRACT });
	}
	
	@Override
	public List<Map<String, Object>> getByIdAndStatus(String brid,String custid,String dupno) {
		return this.getJdbc().queryForListWithMax("MIS.ELF447N.getLnf447nByIdAndStatus",
				new Object[] { brid,custid,dupno });
	}

	/**
	 * J-111-0515_05097_B1001 Web
	 * e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
	 */
	@Override
	public int updateCurrAmtSNByUnidAndContract(String unid, String cntrNo,
			String ELF447N_CLASS, String ELF447N_LNNOFLAG,
			BigDecimal ELF447N_CURAMT_S, BigDecimal ELF447N_CURAMT_N) {
		int count = this.getJdbc().update(
				"MIS.ELF447N.updateCurrAmtSNByUnidAndContract",
				new Object[] { ELF447N_CLASS, ELF447N_LNNOFLAG,
						ELF447N_CURAMT_S, ELF447N_CURAMT_N, unid, cntrNo });
		return count;
	}

}
