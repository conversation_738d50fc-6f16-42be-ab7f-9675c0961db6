/* 
 *ObsdbELF164Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 授信消金利率檔  ELF164
 * </pre>
 * 
 * @since 2012/1/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/3,REX,new
 *          </ul>
 */
public interface ObsdbELF164Service {
	/**
	 * 查詢
	 * 
	 * @param BRNID
	 *            上傳資料庫
	 * @param brNo
	 *            分行別
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> findByBrNo(final String BRNID, String brNo);

	/**
	 * 新增
	 * 
	 * @param BRNID
	 *            上傳銀行號碼
	 * @param dataList
	 *            上傳資料list
	 * 
	 *            <pre>
	 * Object[]
	 *  
	 *  BR_NO  分行別
	 *  CONTRACT 額度序號
	 *  CUST_ID 客戶編號
	 *  KIND 契約種類
	 *  SWFT 放款幣別
	 *  LNAP_CODE 放款科目
	 *  INTRT_TYPE  收息方式
	 *  INT_KIND 利率條件是否字述
	 *  INT_BASE 加碼基礎
	 *  INT_SPREAD  加減碼 BigDecimal
	 *  INT_TYPE 利率方式
	 *  INTCHG_TYPE 利率變動方式
	 *  INT_MEMO 利率條件中文敘述
	 * </pre>
	 */

	public void insert(final String BRNID, List<Object[]> dataList);

	/**
	 * 新增
	 * 
	 * @param BRNID
	 *            上傳銀行號碼
	 * @param dataList
	 *            上傳資料list
	 * 
	 *            <pre>
	 * Object[]
	 *  
	 *  BR_NO  分行別
	 *  CONTRACT 額度序號
	 *  CUST_ID 客戶編號
	 *  KIND 契約種類
	 *  SWFT 放款幣別
	 *  LNAP_CODE 放款科目
	 *  INTRT_TYPE  收息方式
	 *  INT_KIND 利率條件是否字述
	 *  INT_BASE 加碼基礎
	 *  INT_SPREAD  加減碼 BigDecimal
	 *  INT_TYPE 利率方式
	 *  INTCHG_TYPE 利率變動方式
	 *  INT_MEMO 利率條件中文敘述
	 * </pre>
	 */

	public int insert(final String BRNID, String BR_NO, String CONTRACT,
			String CUST_ID, String KIND, String SWFT, String LNAP_CODE,
			String INTRT_TYPE, String INT_KIND, String INT_BASE,
			Double INT_SPREAD, String INT_TYPE, String INTCHG_TYPE,
			String INT_MEMO);
}
