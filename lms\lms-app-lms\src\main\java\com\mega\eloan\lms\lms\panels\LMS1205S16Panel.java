/* 
 * LMS1205S16Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.model.L120M01A;


/**<pre>
 * 額度批覆表/批覆書
 * </pre>
 * @since  2011/11/4
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/11/4,<PERSON>,new
 *          </ul>
 */
public class LMS1205S16Panel extends Panel {

	private L120M01A l120m01a;

	public LMS1205S16Panel(String id, boolean updatePanelName, L120M01A l120m01a) {
		super(id, updatePanelName);
		this.l120m01a = l120m01a;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new LMS7415S01Panel("_lms7415s01panel", l120m01a).processPanelData(model, params);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
