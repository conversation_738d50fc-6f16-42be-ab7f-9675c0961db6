/* 
 * C004S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C004S01A;

/** 政策性留學生貸款報送資料Q檔 **/
public interface C004S01ADao extends IGenericDao<C004S01A> {

	C004S01A findByOid(String oid);
	
	List<C004S01A> findByMainId(String mainId);

	List<C004S01A> findByIndex01(String mainId);
}