package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** <pre>同一通訊指標控制檔
 * </pre>
 * 
 * @since 2019/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/02, J-107-0129 , 在 SLMS-00074 先把 DW、MIS的資料抄寫到ELOANDB後，結合 C900S03C、C900S03D、C900S03E 這三個Table，所產出的控制檔 <br/>
 *          	因為不能跨DB去join資料 , 所以把資料先下到 ELOANDB。在ELOAN 去 join 之後，把結果（亦即，需註記的資料）留存在 ELOAN
 *          </li>
 *          <li>查證結果（chk_result） 
 *          	<ul>
 *          	<li>一開始是空的。
 *          	</li>
 *          	<li>分行在檢視報表「消金借款人留存同一通訊處未註記清單」之後，會跑  LMS.C310M01A 的簽核流程，把 查證結果 回寫到控制檔（LMS.C900S02E）
 *          	</li>
 *          	</ul>　 
 *          </li>
 *          </ul>
 */
/*
delete from lms.c900s02e where DELETEDTIME IS NOT NULL AND (DAYS(CURRENT DATE ) - DAYS(DATE(DELETEDTIME)))  >= 10
*/
@Entity
@Table(name="C900S02E", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S02E extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	/** 文件編號 */
	@Column(length = 32, columnDefinition = "CHAR(32)")
	private String mainId;
	
	@Temporal(TemporalType.DATE)
	@Column(name = "CYC_MN", columnDefinition = "DATE")
	private Date cyc_mn;               
	
	@Column(name="BRNO", length=3, columnDefinition="CHAR(3)")
	private String brNo;
		
	/** 關聯戶FLAG{1:地址, 2:電話, 3:Mail} */
	@Column(name="REL_FLAG", length=1, columnDefinition="CHAR(1)")
	private String rel_flag;
	
	@Column(name="TEXT", length=120, columnDefinition="CHAR(120)")
	private String text;
	
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)")
	private String custId;
	
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;
	
	@Column(name="CNAME", length=171, columnDefinition="CHAR(171)")
	private String cname;
	
	@Column(name="FLAG", length=1, columnDefinition="CHAR(1)")
	private String flag;
	
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="GEN_TIME", columnDefinition="TIMESTAMP")
	private Timestamp gen_time;

//	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="DELETEDTIME", columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;

	/** 查證結果{Y:正常, N異常} **/
	@Column(name="CHK_RESULT", length=1, columnDefinition="CHAR(1)")
	private String chk_result;
	
	@Column(name="CHK_MEMO", length=300, columnDefinition="CHAR(300)")
	private String chk_memo;

	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;
	
	@Column(name="APPROVER", length=6, columnDefinition="CHAR(6)")
	private String approver;
	
	/** 覆核日期 */
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp approveTime;
	
	public String getOid() {
		return this.oid;
	}
	
	public void setOid(String value) {
		this.oid = value;
	}
	
	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public Date getCyc_mn() {
		return cyc_mn;
	}

	public void setCyc_mn(Date cyc_mn) {
		this.cyc_mn = cyc_mn;
	}

	public String getBrNo() {
		return brNo;
	}

	public void setBrNo(String brNo) {
		this.brNo = brNo;
	}

	public String getRel_flag() {
		return rel_flag;
	}

	public void setRel_flag(String rel_flag) {
		this.rel_flag = rel_flag;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String s) {
		this.custId = s;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public String getCname() {
		return cname;
	}

	public void setCname(String cname) {
		this.cname = cname;
	}
	
	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public Timestamp getGen_time() {
		return gen_time;
	}

	public void setGen_time(Timestamp gen_time) {
		this.gen_time = gen_time;
	}

	public Timestamp getDeletedTime() {
		return deletedTime;
	}

	public void setDeletedTime(Timestamp deletedTime) {
		this.deletedTime = deletedTime;
	}

	public String getChk_result() {
		return chk_result;
	}

	public void setChk_result(String chk_result) {
		this.chk_result = chk_result;
	}

	public String getChk_memo() {
		return chk_memo;
	}

	public void setChk_memo(String chk_memo) {
		this.chk_memo = chk_memo;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public String getApprover() {
		return approver;
	}

	public void setApprover(String approver) {
		this.approver = approver;
	}

	public Timestamp getApproveTime() {
		return approveTime;
	}

	public void setApproveTime(Timestamp approveTime) {
		this.approveTime = approveTime;
	}	
}