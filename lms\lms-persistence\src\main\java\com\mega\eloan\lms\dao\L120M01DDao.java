package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01D;

/** 簽報書敘述說明檔 **/
public interface L120M01DDao extends IGenericDao<L120M01D> {

	L120M01D findByOid(String oid);

	List<L120M01D> findByMainId(String mainId);

	L120M01D findByUniqueKey(String mainId, String itemType);

	int delModel(String mainId);

	/**
	 * 查詢oid陣列
	 * 
	 * @param oids oid 
	 * @return List<L120M01D>
	 */
	List<L120M01D> findByOids(String[] oids);
}