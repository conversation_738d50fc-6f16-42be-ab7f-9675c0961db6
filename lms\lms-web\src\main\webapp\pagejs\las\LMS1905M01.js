var initDfd = $.Deferred();

//	alert(responseJSON.mainOid);
//	alert(responseJSON.mainId);


$(document).ready(function(){
    //setRequiredSave(true);
    //setCloseConfirm(true);
    $.form.init({
        formHandler: "lms1905m01formhandler",
        formAction: "query",
        loadSuccess: function(json){
            var innerAudit = $('#innerAudit').val();
            if (innerAudit == "Y") {
                $("#innerAudit_info").show();
            }
            var docStatus = $("#mainDocStatus").val();
            switch (docStatus) {
                case '30G':
                    break;
                case '31G':
                    //假如主管沒有編輯權限，則打開該筆文件就要設成唯讀模式
                    if (!responseJSON.Auth.Modify) {
                        $("#tabForm").readOnlyChilds();
                        setIgnoreTempSave(true);
                        $("#reInclude,#addNewColl,#deleteColl,#cancel,#finish,#phrase").remove();
                        json.editable = "N";
                    }
                    break;
                case '31B':
                    //假如主管沒有編輯權限，則打開該筆文件就要設成唯讀模式
                    if (!responseJSON.Auth.Modify) {
                        $("#tabForm").readOnlyChilds();
                        setIgnoreTempSave(true);
                        $("#reInclude,#addNewColl,#deleteColl,#cancel,#finish,#phrase").remove();
                        json.editable = "N";
                    }
                    break;
                default:
                    $("#tabForm").readOnlyChilds();
                    setIgnoreTempSave(true);
                    $("#reInclude,#addNewColl,#deleteColl,#cancel,#finish,#phrase").remove();
                    json.editable = "N";
            }
            initDfd.resolve(json);
        }
    });
    
    $("#btnSave").click(function(){
        saveAction().done(function(json){
            API.showMessage(i18n.def.saveSuccess);
        });
    });
    
    $("#btnSendNextG").click(function(){
        API.flowConfirmAction({
            message: i18n.def["flow.confirmSend"],
            handler: "lms1905m01formhandler",
            action: "sendNextG",
            formId: "tabForm",
            success: function(){
                API.showPopMessage(i18n.def["flow.sent"], window.close);
            }
        });
    });
    
    $("#btnSendG").click(function(){
        API.flowConfirmAction({
            message: i18n.def["flow.confirmSend2"],
            handler: "lms1905m01formhandler",
            action: "sendG",
            formId: "tabForm",
            success: function(){
                API.showPopMessage(i18n.def["flow.sent2"], window.close);
            }
        });
    });
    
    $("#btnSend").click(function(){
        API.flowConfirmAction({
            message: i18n.def["flow.confirmSend2"],
            handler: "lms1905m01formhandler",
            action: "send",
            formId: "tabForm",
            success: function(){
                API.showPopMessage(i18n.def["flow.sent2"], window.close);
            }
        });
    });
    
    $("#btnAcceptG").click(function(){
        API.flowConfirmAction({
            message: i18n.def["confirmApprove"],
            handler: "lms1905m01formhandler",
            action: "acceptG",
            formId: "tabForm",
            success: function(){
                API.showPopMessage(i18n.def["confirmApplySuccess"], window.close);
            }
        });
    });
    
    $("#btnAccept").click(function(){
        API.flowConfirmAction({
            message: i18n.def["confirmApprove"],
            handler: "lms1905m01formhandler",
            action: "accept",
            formId: "tabForm",
            success: function(){
                API.showPopMessage(i18n.def["confirmApplySuccess"], window.close);
            }
        });
    });
    
    $("#btnReturnG").click(function(){
        API.flowConfirmAction({
            message: i18n.def["flow.confirmReturn"],
            handler: "lms1905m01formhandler",
            action: "returnG",
            formId: "tabForm",
            success: function(){
                API.showPopMessage(i18n.def["confirmReturnSuccess"], window.close);
            }
        });
    });
    
    $("#btnReturn").click(function(){
        API.flowConfirmAction({
            message: i18n.def["flow.confirmReturn"],
            handler: "lms1905m01formhandler",
            action: "returnB",
            formId: "tabForm",
            success: function(){
                API.showPopMessage(i18n.def["confirmReturnSuccess"], window.close);
            }
        });
    });
    
    $("#btnPrint2").click(function(){
        if ($("#btnSave").length) {
            saveAction().done(function(json){
                $.form.submit({
                    url: "../../las/lms1905r01",
                    target: "_blank",
                    data: {
                        mainOid: $("#mainOid").val()
                    }
                });
            });
        }
        else {
            $.form.submit({
                url: "../../las/lms1905r01",
                target: "_blank",
                data: {
                    mainOid: $("#mainOid").val()
                }
            });
        }
        
    });
    
    $("#btnPrint3").click(function(){
    	var showMessage = "";
        if ($("#btnSave").length) {
            saveAction().done(function(json){
                $.ajax({
                    handler: "lms1905m01formhandler",
                    action: "checkPrint",
                    data: {
                        mainOid: $("#mainOid").val()
                    },
                    success: function(responseData){
                        if (responseData.L192S01A_PRINT_MARK == "Y") {
                        	
                        } else {
                        	showMessage = showMessage + responseData.NO_PRINT_DETAIL;
                            showMessage = showMessage + "<br>";
                        }
                        if (responseData.L192S01A_PRINT_MARK == "Y" || responseData.L192S01A_PRINT_MARK == "P") {
                            API.confirmMessage(showMessage.length == 0 ? i18n.def["actoin_001"] : showMessage, function(result){
                                if (result) {
                                    $.form.submit({
                                        url: "../../las/lms1905r02",
                                        target: "_blank",
                                        data: {
                                            mainOid: $("#mainOid").val()
                                        }
                                    });
                                    //設定2秒reoload，但如果report印得太慢的話，那麼寫回createBill的速度會比較慢，那可能沒效果
                                    setTimeout(function(){
                                    	API.triggerOpener()
                                    }, 2000);
                                }
                            });
                        } else if (showMessage.length > 0) {
                        	API.triggerOpener();
                            API.showMessage(showMessage);
                        }
                    }
                });
            })
        }
        else {
            $.ajax({
                handler: "lms1905m01formhandler",
                action: "checkPrint",
                data: {
                    mainOid: $("#mainOid").val()
                },
                success: function(responseData){
                    if (responseData.L192S01A_PRINT_MARK == "Y") {
                    	
                    } else {
                    	showMessage = showMessage + responseData.NO_PRINT_DETAIL;
                        showMessage = showMessage + "<br>";
                    }
                    if (responseData.L192S01A_PRINT_MARK == "Y" || responseData.L192S01A_PRINT_MARK == "P") {
                        API.confirmMessage(showMessage.length == 0 ? i18n.def["actoin_001"] : showMessage, function(result){
                            if (result) {
                                $.form.submit({
                                    url: "../../las/lms1905r02",
                                    target: "_blank",
                                    data: {
                                        mainOid: $("#mainOid").val()
                                    }
                                });
                                //設定2秒reoload，但如果report印得太慢的話，那麼寫回createBill的速度會比較慢，那可能沒效果
                                setTimeout(function(){
                                	API.triggerOpener()
                                }, 2000);
                            }
                        });
                    } else if (showMessage.length > 0) {
                    	API.triggerOpener();
                        API.showMessage(showMessage);
                    }
                }
            });
        }
        
        
    });
    
    function saveAction(json){
        if ($("#tabForm").valid()) {
            return $.ajax($.extend({
                handler: "lms1905m01formhandler",
                action: "save",
                data: $.extend($("#tabForm").serializeData(), {}),
                success: function(json){
                    $("#tabForm").injectData(json);
                    window.name = json.mainOid;
                    API.triggerOpener();
                    setRequiredSave(false);
                    setCloseConfirm(false);
                }
            }, json || {}));
        }
        else {
            return $.Deferred();
        }
    }
    
});


//畫面切換table 所需設定之資料 如無設定 則直接切換
$.extend(window.tempSave, {
    handler: "lms1905m01formhandler",
    action: "tempSave",
    beforeCheck: function(){
        return $("#tabForm").valid();
    },
    sendData: function(){
        return $("#tabForm").serializeData();
    }
});
