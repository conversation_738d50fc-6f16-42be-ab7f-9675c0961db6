/* 
 * L180M02B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 覆審控制維護明細檔 **/
@NamedEntityGraph(name = "L181M01B-entity-graph", attributeNodes = { @NamedAttributeNode("l181m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L181M01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "type" }))
public class L181M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件與關聯檔By聯行額度明細表
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)
	private L181M01A l181m01a;

	public void setL181m01a(L181M01A l181m01a) {
		this.l181m01a = l181m01a;
	}

	public L181M01A getL181m01a() {
		return l181m01a;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 類別
	 * <p/>
	 * 1.原始資料<br/>
	 * 2.異動資料
	 */
	@Column(name = "TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String type;

	/** 主要授信戶 **/
	@Column(name = "ELFMAINCUST", length = 1, columnDefinition = "CHAR(1)")
	private String elfMainCust;

	/** 資信評等類別 **/
	@Column(name = "ELFCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elfCrdType;

	/** 資信評等 **/
	@Column(name = "ELFCRDTTBL", length = 2, columnDefinition = "CHAR(2)")
	private String elfCrdTTbl;

	/** 信用模型評等類別 **/
	@Column(name = "ELFMOWTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elfMowType;

	/** 信用模型評等 **/
	@Column(name = "ELFMOWTBL1", length = 2, columnDefinition = "CHAR(2)")
	private String elfMowTbl1;

	/** 上次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFLRDATE", columnDefinition = "DATE")
	private Date elfLRDate;

	/** 覆審週期 **/
	@Column(name = "ELFRCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elfRCkdLine;

	/** 主管機關指定覆審案件 **/
	@Column(name = "ELFUCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elfUCkdLINE;

	/** 主管機關通知日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFUCKDDT", columnDefinition = "DATE")
	private Date elfUCkdDt;

	/** 異常通報代碼 **/
	@Column(name = "ELFMDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elfMDFlag;

	/** 異常通報日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFMDDT", columnDefinition = "DATE")
	private Date elfMDDt;

	/** 異常通報情形 **/
	@Column(name = "ELFPROCESS", length = 300, columnDefinition = "VARCHAR(300)")
	private String elfProcess;

	/** 新作/增額/逾放轉正 **/
	@Column(name = "ELFNEWADD", length = 1, columnDefinition = "CHAR(1)")
	private String elfNewAdd;

	/** 新作/增額/逾放轉正年月 **/
	@Column(name = "ELFNEWDATE", length = 6, columnDefinition = "CHAR(6)")
	private String elfNewDate;

	/** 不覆審代碼 **/
	@Column(name = "ELFNCKDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elfNCkdFlag;

	/** 不覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFNCKDDATE", columnDefinition = "DATE")
	private Date elfNCkdDate;

	/** 不覆審備註 **/
	@Column(name = "ELFNCKDMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String elfNCkdMemo;

	/** 下次恢復覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFNEXTNWDT", columnDefinition = "DATE")
	private Date elfNextNwDt;

	/** DBUOBU是否有共管 **/
	@Column(name = "ELFDBUOBU", length = 1, columnDefinition = "CHAR(1)")
	private String elfDBUOBU;

	/** 其他備註 **/
	@Column(name = "ELFMEMO", length = 300, columnDefinition = "VARCHAR(300)")
	private String elfMemo;

	/**
	 * 外部評等類別 <br/>
	 * 標準普爾 | 1 <br/>
	 * 穆迪信評 | 2 <br/>
	 * 惠譽信評 | 3 <br/>
	 * 中華信評 | 4
	 */
	@Column(name = "ELFFCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elfFcrdType;

	/**
	 * 外部評等地區別 <br/>
	 * 國際 | 1 <br/>
	 * 本國 | 2
	 */
	@Column(name = "ELFFCRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String elfFcrdArea;

	/**
	 * 外部評等期間別 <br/>
	 * 長期 | 1 <br/>
	 * 短期 | 2
	 */
	@Column(name = "ELFFCRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String elfFcrdPred;

	/** 外部評等等級 **/
	@Column(name = "ELFFCRDGRAD", length = 30, columnDefinition = "VARCHAR(30)")
	private String elfFcrdGrad;

	/** 下次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "NDDATE", columnDefinition = "DATE")
	private Date ndDate;

	/**
	 * 是否為實地覆審 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 */
	@Column(name = "ELFREALCKFG", length = 1, columnDefinition = "CHAR(1)")
	private String elfRealCkFg;

	/**
	 * 最近一次實地覆審時間 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFREALDT", columnDefinition = "DATE")
	private Date elfRealDt;

	/**
	 * 舊簽報書MAINID
	 */
	@Column(name = "ELFOLDRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String elfOldRptId;

	/**
	 * 舊簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFOLDRPTDT", columnDefinition = "DATE")
	private Date elfOldRptDt;

	/**
	 * 新簽報書MAINID
	 */
	@Column(name = "ELFNEWRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String elfNewRptId;

	/**
	 * 新簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELFNEWRPTDT", columnDefinition = "DATE")
	private Date elfNewRptDt;

	/**
	 * J-108-0078_05097_B1001
	 * 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
	 * 
	 * 
	 * 首次往來之新貸戶
	 */
	@Column(name = "ELFISALLNEW", length = 1, columnDefinition = "CHAR(1)")
	private String elfIsAllNew;

    /**
     * 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
     * 純紓困戶
     */
    @Column(name = "ELFISRESCUE", length = 1, columnDefinition = "CHAR(1)")
    private String elfIsRescue;

    /** 信保擔保註記
     * 2020/04 設定為8成 **/
    @Column(name = "ELFGUARFLAG", length = 1, columnDefinition = "CHAR(1)")
    private String elfGuarFlag;

	/** 新作紓困註記 **/
	@Column(name = "ELFNEWRESCUE", length = 1, columnDefinition = "CHAR(1)")
	private String elfNewRescue;

	/** 新作紓困資料年月 **/
	@Column(name="ELFNEWRESCUEYM", length=6, columnDefinition="CHAR(6)")
	private String elfNewRescueYM;

    /** 抽樣類別 **/
    @Column(name = "ELFRANDOMTYPE", length = 1, columnDefinition = "VARCHAR(1)")
    private String elfRandomType;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得類別
	 * <p/>
	 * 1.原始資料<br/>
	 * 2.異動資料
	 */
	public String getType() {
		return this.type;
	}

	/**
	 * 設定類別
	 * <p/>
	 * 1.原始資料<br/>
	 * 2.異動資料
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得主要授信戶 **/
	public String getElfMainCust() {
		return this.elfMainCust;
	}

	/** 設定主要授信戶 **/
	public void setElfMainCust(String value) {
		this.elfMainCust = value;
	}

	/** 取得資信評等類別 **/
	public String getElfCrdType() {
		return this.elfCrdType;
	}

	/** 設定資信評等類別 **/
	public void setElfCrdType(String value) {
		this.elfCrdType = value;
	}

	/** 取得資信評等 **/
	public String getElfCrdTTbl() {
		return this.elfCrdTTbl;
	}

	/** 設定資信評等 **/
	public void setElfCrdTTbl(String value) {
		this.elfCrdTTbl = value;
	}

	/** 取得信用模型評等類別 **/
	public String getElfMowType() {
		return this.elfMowType;
	}

	/** 設定信用模型評等類別 **/
	public void setElfMowType(String value) {
		this.elfMowType = value;
	}

	/** 取得信用模型評等 **/
	public String getElfMowTbl1() {
		return this.elfMowTbl1;
	}

	/** 設定信用模型評等 **/
	public void setElfMowTbl1(String value) {
		this.elfMowTbl1 = value;
	}

	/** 取得上次覆審日 **/
	public Date getElfLRDate() {
		return this.elfLRDate;
	}

	/** 設定上次覆審日 **/
	public void setElfLRDate(Date value) {
		this.elfLRDate = value;
	}

	/** 取得覆審週期 **/
	public String getElfRCkdLine() {
		return this.elfRCkdLine;
	}

	/** 設定覆審週期 **/
	public void setElfRCkdLine(String value) {
		this.elfRCkdLine = value;
	}

	/** 取得主管機關指定覆審案件 **/
	public String getElfUCkdLINE() {
		return this.elfUCkdLINE;
	}

	/** 設定主管機關指定覆審案件 **/
	public void setElfUCkdLINE(String value) {
		this.elfUCkdLINE = value;
	}

	/** 取得主管機關通知日期 **/
	public Date getElfUCkdDt() {
		return this.elfUCkdDt;
	}

	/** 設定主管機關通知日期 **/
	public void setElfUCkdDt(Date value) {
		this.elfUCkdDt = value;
	}

	/** 取得異常通報代碼 **/
	public String getElfMDFlag() {
		return this.elfMDFlag;
	}

	/** 設定異常通報代碼 **/
	public void setElfMDFlag(String value) {
		this.elfMDFlag = value;
	}

	/** 取得異常通報日期 **/
	public Date getElfMDDt() {
		return this.elfMDDt;
	}

	/** 設定異常通報日期 **/
	public void setElfMDDt(Date value) {
		this.elfMDDt = value;
	}

	/** 取得異常通報情形 **/
	public String getElfProcess() {
		return this.elfProcess;
	}

	/** 設定異常通報情形 **/
	public void setElfProcess(String value) {
		this.elfProcess = value;
	}

	/** 取得新作/增額/逾放轉正 **/
	public String getElfNewAdd() {
		return this.elfNewAdd;
	}

	/** 設定新作/增額/逾放轉正 **/
	public void setElfNewAdd(String value) {
		this.elfNewAdd = value;
	}

	/** 取得新作/增額/逾放轉正年月 **/
	public String getElfNewDate() {
		return this.elfNewDate;
	}

	/** 設定新作/增額/逾放轉正年月 **/
	public void setElfNewDate(String value) {
		this.elfNewDate = value;
	}

	/** 取得不覆審代碼 **/
	public String getElfNCkdFlag() {
		return this.elfNCkdFlag;
	}

	/** 設定不覆審代碼 **/
	public void setElfNCkdFlag(String value) {
		this.elfNCkdFlag = value;
	}

	/** 取得不覆審日期 **/
	public Date getElfNCkdDate() {
		return this.elfNCkdDate;
	}

	/** 設定不覆審日期 **/
	public void setElfNCkdDate(Date value) {
		this.elfNCkdDate = value;
	}

	/** 取得不覆審備註 **/
	public String getElfNCkdMemo() {
		return this.elfNCkdMemo;
	}

	/** 設定不覆審備註 **/
	public void setElfNCkdMemo(String value) {
		this.elfNCkdMemo = value;
	}

	/** 取得下次恢復覆審日期 **/
	public Date getElfNextNwDt() {
		return this.elfNextNwDt;
	}

	/** 設定下次恢復覆審日期 **/
	public void setElfNextNwDt(Date value) {
		this.elfNextNwDt = value;
	}

	/** 取得DBUOBU是否有共管 **/
	public String getElfDBUOBU() {
		return this.elfDBUOBU;
	}

	/** 設定DBUOBU是否有共管 **/
	public void setElfDBUOBU(String value) {
		this.elfDBUOBU = value;
	}

	/** 取得其他備註 **/
	public String getElfMemo() {
		return this.elfMemo;
	}

	/** 設定其他備註 **/
	public void setElfMemo(String value) {
		this.elfMemo = value;
	}

	/** 取得外部評等類別 **/
	public String getElfFcrdType() {
		return elfFcrdType;
	}

	/** 設定外部評等類別 **/
	public void setElfFcrdType(String elfFcrdType) {
		this.elfFcrdType = elfFcrdType;
	}

	/** 取得外部評等地區別 **/
	public String getElfFcrdArea() {
		return elfFcrdArea;
	}

	/** 設定外部評等地區別 **/
	public void setElfFcrdArea(String elfFcrdArea) {
		this.elfFcrdArea = elfFcrdArea;
	}

	/** 取得外部評等期間別 **/
	public String getElfFcrdPred() {
		return elfFcrdPred;
	}

	/** 設定外部評等期間別 **/
	public void setElfFcrdPred(String elfFcrdPred) {
		this.elfFcrdPred = elfFcrdPred;
	}

	/** 取得外部評等等級 **/
	public String getElfFcrdGrad() {
		return elfFcrdGrad;
	}

	/** 設定外部評等等級 **/
	public void setElfFcrdGrad(String elfFcrdGrad) {
		this.elfFcrdGrad = elfFcrdGrad;
	}

	/** 取得下次覆審日 **/
	public Date getNdDate() {
		return ndDate;
	}

	/** 設定下次覆審日 **/
	public void setNdDate(Date ndDate) {
		this.ndDate = ndDate;
	}

	/** 設定是否為實地覆審 **/
	public void setElfRealCkFg(String elfRealCkFg) {
		this.elfRealCkFg = elfRealCkFg;
	}

	/** 取得是否為實地覆審 **/
	public String getElfRealCkFg() {
		return elfRealCkFg;
	}

	/** 設定最近一次實地覆審時間 **/
	public void setElfRealDt(Date elfRealDt) {
		this.elfRealDt = elfRealDt;
	}

	/** 取得最近一次實地覆審時間 **/
	public Date getElfRealDt() {
		return elfRealDt;
	}

	/** 設定舊簽報書MAINID **/
	public void setElfOldRptId(String elfOldRptId) {
		this.elfOldRptId = elfOldRptId;
	}

	/** 取得舊簽報書MAINID **/
	public String getElfOldRptId() {
		return elfOldRptId;
	}

	/** 設定舊簽報書核准日期 **/
	public void setElfOldRptDt(Date elfOldRptDt) {
		this.elfOldRptDt = elfOldRptDt;
	}

	/** 取得舊簽報書核准日期 **/
	public Date getElfOldRptDt() {
		return elfOldRptDt;
	}

	/** 設定新簽報書MAINID **/
	public void setElfNewRptId(String elfNewRptId) {
		this.elfNewRptId = elfNewRptId;
	}

	/** 取得新簽報書MAINID **/
	public String getElfNewRptId() {
		return elfNewRptId;
	}

	/** 設定新簽報書核准日期 **/
	public void setElfNewRptDt(Date elfNewRptDt) {
		this.elfNewRptDt = elfNewRptDt;
	}

	/** 取得新簽報書核准日期 **/
	public Date getElfNewRptDt() {
		return elfNewRptDt;
	}

	/** 設定首次往來之新授信戶(下稱純新貸戶) **/
	public void setElfIsAllNew(String elfIsAllNew) {
		this.elfIsAllNew = elfIsAllNew;
	}

	/** 取得首次往來之新授信戶(下稱純新貸戶) **/
	public String getElfIsAllNew() {
		return elfIsAllNew;
	}

    /** 設定純紓困戶 **/
    public void setElfIsRescue(String elfIsRescue) {
        this.elfIsRescue = elfIsRescue;
    }

    /** 取得純紓困戶 **/
    public String getElfIsRescue() {
        return elfIsRescue;
    }

    /** 設定信保擔保註記(2020/04 設定為8成) **/
    public void setElfGuarFlag(String elfGuarFlag) {
        this.elfGuarFlag = elfGuarFlag;
    }

    /** 取得信保擔保註記(2020/04 設定為8成) **/
    public String getElfGuarFlag() {
        return elfGuarFlag;
    }

	/** 設定新作紓困註記 **/
	public void setElfNewRescue(String elfNewRescue) {
		this.elfNewRescue = elfNewRescue;
	}

	/** 取得新作紓困註記 **/
	public String getElfNewRescue() {
		return elfNewRescue;
	}

	/**
	 * 設定新作紓困資料年月
	 */
	public void setElfNewRescueYM(String elfNewRescueYM) {
		this.elfNewRescueYM = elfNewRescueYM;
	}

	/**
	 * 取得新作紓困資料年月
	 */
	public String getElfNewRescueYM() {
		return elfNewRescueYM;
	}

    /**
     * 設定抽樣類別
     */
    public void setElfRandomType(String elfRandomType) {
        this.elfRandomType = elfRandomType;
    }

    /**
     * 取得抽樣類別
     */
    public String getElfRandomType() {
        return elfRandomType;
    }
}
