package com.mega.eloan.lms.cls.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.model.C126M01A;
import com.mega.eloan.common.service.AbstractService;

public interface CLS3701Service extends AbstractService{
//	public void flowAction(String mainOid, C126M01A model, boolean setResult,
//			   boolean resultType, boolean upMis) throws Throwable;
//	public void save(C126M01A entity);
//	public void delete(C126M01A entity);
//	public Page<C126M01A> findPage(ISearch search);
	public boolean deleteC126m01as(String[] oids);
	public List<C126M01A> findAgntNo(String ownBrId,String agntNo,String applyTS_beg,String applyTS_end,String docStatus);
	public void backApproveSave(C126M01A c126m01a);
	public boolean checkSpecialBank(String ownBrId);
	public List<C126M01A> findDuplicateAgntCase(String mainId,String custId,String dupNo,String agntNo,String agntChain,String contractNo,String ownBrId);
	public void saveELF604(List<Map<String, Object>> datas);
}
