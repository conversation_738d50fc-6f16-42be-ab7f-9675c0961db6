package com.mega.eloan.lms.mfaloan.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.mega.eloan.lms.mfaloan.bean.ELF491;

/**
 * <pre>
 * 覆審控制檔
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,EL08034,new
 *          </ul>
 */
public interface MisELF491Service {
	public ELF491 findByPk(String elf491_branch, String elf491_custid,String elf491_dupno);
			
	/*
	  編製完成
	Retrial_Kind==99, SpecifyCycle{0-免再審;1-1個月;A-12個月}
	  
	If Trim(doc.DocKind(0))<>"G" Then		
		If Not  gfnDB2UpdateMISELF491(doc, tFinalData) Then Msgbox "更新覆審控制檔失敗，請洽資訊處":Exit Sub
		//---
		//同時處理ELF491, ELF492(只上傳C241M01A.retrialYN=='Y')
		//ELF491_LRDATE = Retrial_Date
		//另再 insert 8_1 的 EFL491: custId||dupNo='XXXXXXXXXX'||'X', updater=LNBD9455
		//---		  
		If Not  gfnDB2UpdataMISELF498(doc) Then Msgbox "更新覆審稽核控制檔失敗，請洽資訊處":Exit Sub
	Elseif Trim(doc.DocKind(0))="G" Then 
		If Not  fnGrpUpdaateELF492Data  Then Msgbox "更新覆審控制檔失敗，請洽資訊處":Exit Sub
	 */
	
	public List<Map<String, Object>> selNewCaseForELF498(String elf490_data_ym_beg,String elf490_data_ym_end,String brNo);
	public List<ELF491> selByBranch_activeCrDate(String brNo);
	
	/**
	 * remomo 的 1~13 碼 :plotsOfReview 覆審類別8-1總筆數
	 * remomo 的14~25 碼 :samplingCount 覆審類別8-1已抽樣筆數
	 * 
	 * 可能 2013 應抽總筆數為 15 筆; 而到 2014  應抽總筆數變 20 筆 ===> 由 主機 的排程控制
	 * 消金覆審只 異動 remomo 的後 13 碼(覆審類別8-1已抽樣筆數)
	 */
	public ELF491 selWithCustIdXXXX_8_1(String brno);
	public ELF491 selWithCustId_AAAAAAAAAA___dupNo_A(String brno);
	public ELF491 selWithCustId_DDDDDDDDDD___dupNo_D(String brno);
	public int selC240M01A_EffectiveCount(String brno);
	public Set<String> sel_custIdSet_hasLN(String brNo);
	public List<Map<String, Object>> sel_whenProduce(String brNo, Date dataEndDate, String elf491_newflag);
	public List<Map<String, Object>> selRuleNo8_1(String brNo);	
	public List<ELF491> selByR98_activeCrDate();
	public List<ELF491> selByCustIdDupNo(String custId, String dupNo);
	public List<Map<String, Object>> sel_gfnGetWillOverReviewData(String elf491_branch, String elf491_crDate_s, String elf491_crDate_e);
	public List<ELF491> chkR1R2R4(String elf491_branch, String elf491_custid, String elf491_lrDate);
	
	public List<Map<String, Object>> sel_gfnGenerateCTL_FLMS180R15(String date_s, String date_e);
	public List<Map<String, Object>> proc_crossMonth_loanData(String prevRocDataYM, String baseRocDataYM, String strCmpCrDate);

	public int update_remom_from_8_2_to_8_1();
	
	public List<ELF491> selByBranch_activeCrDate(String[] branchs, String sDate, String eDate);

	/**
	 * 消金覆審R14單一授信額度新臺幣一千萬元以下且為十足擔保授信或經信用保證基金保證成數七成以上
	 * 搬移資料
	 * @param custId
	 * @param dupNo
	 * @param newCustId
	 * @param newDupNo
	 * @return
	 */
	int update_R14(String custId, String dupNo, String newCustId,
			String newDupNo);

	/**
	 * 消金覆審R14刪除資料
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	int delete_R14(String custId, String dupNo);
}
