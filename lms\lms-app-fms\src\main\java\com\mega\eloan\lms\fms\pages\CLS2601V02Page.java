package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.fms.panels.CLS2601FilterPanel;

@Controller
@RequestMapping(path = "/fms/cls2601v02")
public class CLS2601V02Page extends AbstractEloanInnerView {

	public CLS2601V02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.待覆核);
		setJavaScriptVar("noOpenDoc", "N");
		//---
		addToButtonPanel(model, LmsButtonEnum.Filter);
		
		renderJsI18N(CLS2601V01Page.class);
		setupIPanel(new CLS2601FilterPanel(PANEL_ID), model, params);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS2601V01Page.js" };
	}
}
