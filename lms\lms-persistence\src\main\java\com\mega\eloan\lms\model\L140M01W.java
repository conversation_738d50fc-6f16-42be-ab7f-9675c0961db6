/*
 * L140M01W.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 專案種類細項資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140M01W", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class L140M01W extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 連鎖店_類別<p/>
	 * 1：主事業體<br/>
	 *  2：加盟
	 */
	@Size(max=1)
	@Column(name="CHAINSTORE", length=1, columnDefinition="CHAR(1)")
	private String chainStore;

	/** 連鎖店_主事業體統一編號 **/
	@Size(max=10)
	@Column(name="MAINBIZID", length=10, columnDefinition="VARCHAR(10)")
	private String mainBizId;

	/** 連鎖店_主事業體重覆序號 **/
	@Size(max=1)
	@Column(name="MAINBIZDUPNO", length=1, columnDefinition="CHAR(1)")
	private String mainBizDupNo;

	/** 連鎖店_主事業體客戶名稱 **/
	@Size(max=120)
	@Column(name="MAINBIZNAME", length=120, columnDefinition="VARCHAR(120)")
	private String mainBizName;

	/** 連鎖店_加盟戶數 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="JOINNUM", columnDefinition="DECIMAL(5,0)")
	private Integer joinNum;

	/** 連鎖店_主事業體額度序號 **/
	@Size(max=12)
	@Column(name="MAINBIZCNTRNO", length=12, columnDefinition="CHAR(12)")
	private String mainBizCntrNo;

	/** 三大方案_核發日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ISSUEDT", columnDefinition="DATE")
	private Date issueDT;

	/** 三大方案_應完成投資日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="IVDT", columnDefinition="DATE")
	private Date ivDT;

	/**
	 * 三大方案_投資計畫總金額<p/>
	 * TWD
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="IVTTLAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal ivTtlAmt;

	/**
	 * 三大方案_投資金額(購置土地-非屬專案貸款範圍)<p/>
	 * TWD
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="IVLANDAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal ivLandAmt;

	/**
	 * 三大方案_投資金額(新(擴)建廠房)<p/>
	 * TWD
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="IVFTYAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal ivFtyAmt;

	/**
	 * 三大方案_投資金額(購置機器設備)<p/>
	 * TWD
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="IVMACHAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal ivMachAmt;

	/**
	 * 三大方案_投資金額(營運週轉金)<p/>
	 * TWD
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="IVCASHAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal ivCashAmt;

	/**
	 * 三大方案_申請用途<p/>
	 * 1：新(擴)建廠房<br/>
	 *  2：購置機器設備<br/>
	 *  3：營運週轉金<br/>
     *  Z：舊案(多選)
	 */
	@Size(max=1)
	@Column(name="PROJTW_USE", length=1, columnDefinition="CHAR(1)")
	private String projTW_use;

	/**
	 * 三大方案_申請金額<p/>
	 * TWD
	 */
	@Digits(integer=15, fraction=0, groups = Check.class)
	@Column(name="PROJTW_USEAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal projTW_useAmt;

    /**
     * 三大方案_舊案_申請用途_多選
     */
    @Size(max=25)
    @Column(name="PROJTW_MPLUSE", length=25, columnDefinition="VARCHAR(25)")
    private String projTW_mplUse;

	/**
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 取得連鎖店_類別<p/>
	 * 1：主事業體<br/>
	 *  2：加盟
	 */
	public String getChainStore() {
		return this.chainStore;
	}
	/**
	 *  設定連鎖店_類別<p/>
	 *  1：主事業體<br/>
	 *  2：加盟
	 **/
	public void setChainStore(String value) {
		this.chainStore = value;
	}

	/** 取得連鎖店_主事業體統一編號 **/
	public String getMainBizId() {
		return this.mainBizId;
	}
	/** 設定連鎖店_主事業體統一編號 **/
	public void setMainBizId(String value) {
		this.mainBizId = value;
	}

	/** 取得連鎖店_主事業體重覆序號 **/
	public String getMainBizDupNo() {
		return this.mainBizDupNo;
	}
	/** 設定連鎖店_主事業體重覆序號 **/
	public void setMainBizDupNo(String value) {
		this.mainBizDupNo = value;
	}

	/** 取得連鎖店_主事業體客戶名稱 **/
	public String getMainBizName() {
		return this.mainBizName;
	}
	/** 設定連鎖店_主事業體客戶名稱 **/
	public void setMainBizName(String value) {
		this.mainBizName = value;
	}

	/** 取得連鎖店_加盟戶數 **/
	public Integer getJoinNum() {
		return this.joinNum;
	}
	/** 設定連鎖店_加盟戶數 **/
	public void setJoinNum(Integer value) {
		this.joinNum = value;
	}

	/** 取得連鎖店_主事業體額度序號 **/
	public String getMainBizCntrNo() {
		return this.mainBizCntrNo;
	}
	/** 設定連鎖店_主事業體額度序號 **/
	public void setMainBizCntrNo(String value) {
		this.mainBizCntrNo = value;
	}

	/** 取得三大方案_核發日期 **/
	public Date getIssueDT() {
		return this.issueDT;
	}
	/** 設定三大方案_核發日期 **/
	public void setIssueDT(Date value) {
		this.issueDT = value;
	}

	/** 取得三大方案_應完成投資日期 **/
	public Date getIvDT() {
		return this.ivDT;
	}
	/** 設定三大方案_應完成投資日期 **/
	public void setIvDT(Date value) {
		this.ivDT = value;
	}

	/**
	 * 取得三大方案_投資計畫總金額<p/>
	 * TWD
	 */
	public BigDecimal getIvTtlAmt() {
		return this.ivTtlAmt;
	}
	/**
	 *  設定三大方案_投資計畫總金額<p/>
	 *  TWD
	 **/
	public void setIvTtlAmt(BigDecimal value) {
		this.ivTtlAmt = value;
	}

	/**
	 * 取得三大方案_投資金額(購置土地-非屬專案貸款範圍)<p/>
	 * TWD
	 */
	public BigDecimal getIvLandAmt() {
		return this.ivLandAmt;
	}
	/**
	 *  設定三大方案_投資金額(購置土地-非屬專案貸款範圍)<p/>
	 *  TWD
	 **/
	public void setIvLandAmt(BigDecimal value) {
		this.ivLandAmt = value;
	}

	/**
	 * 取得三大方案_投資金額(新(擴)建廠房)<p/>
	 * TWD
	 */
	public BigDecimal getIvFtyAmt() {
		return this.ivFtyAmt;
	}
	/**
	 *  設定三大方案_投資金額(新(擴)建廠房)<p/>
	 *  TWD
	 **/
	public void setIvFtyAmt(BigDecimal value) {
		this.ivFtyAmt = value;
	}

	/**
	 * 取得三大方案_投資金額(購置機器設備)<p/>
	 * TWD
	 */
	public BigDecimal getIvMachAmt() {
		return this.ivMachAmt;
	}
	/**
	 *  設定三大方案_投資金額(購置機器設備)<p/>
	 *  TWD
	 **/
	public void setIvMachAmt(BigDecimal value) {
		this.ivMachAmt = value;
	}

	/**
	 * 取得三大方案_投資金額(營運週轉金)<p/>
	 * TWD
	 */
	public BigDecimal getIvCashAmt() {
		return this.ivCashAmt;
	}
	/**
	 *  設定三大方案_投資金額(營運週轉金)<p/>
	 *  TWD
	 **/
	public void setIvCashAmt(BigDecimal value) {
		this.ivCashAmt = value;
	}

	/**
	 * 取得三大方案_申請用途<p/>
	 * 1：新(擴)建廠房<br/>
	 *  2：購置機器設備<br/>
	 *  3：營運週轉金<br/>
     *  Z：舊案(多選)
	 */
	public String getProjTW_use() {
		return this.projTW_use;
	}
	/**
	 *  設定三大方案_申請用途<p/>
	 *  1：新(擴)建廠房<br/>
	 *  2：購置機器設備<br/>
	 *  3：營運週轉金<br/>
     *  Z：舊案(多選)
	 **/
	public void setProjTW_use(String value) {
		this.projTW_use = value;
	}

	/**
	 * 取得三大方案_申請金額<p/>
	 * TWD
	 */
	public BigDecimal getProjTW_useAmt() {
		return this.projTW_useAmt;
	}
	/**
	 *  設定三大方案_申請金額<p/>
	 *  TWD
	 **/
	public void setProjTW_useAmt(BigDecimal value) {
		this.projTW_useAmt = value;
	}

    /**
     * 取得三大方案_舊案_申請用途_多選
     */
    public String getProjTW_mplUse() {
        return this.projTW_mplUse;
    }
    /**
     *  設定三大方案_舊案_申請用途_多選
     **/
    public void setProjTW_mplUse(String value) {
        this.projTW_mplUse = value;
    }
}
