package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 覆審(帳號層)
 * </pre>
 * 
 * @since 2020/12/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/12/01,EL08034,new
 *          </ul>
 */
public interface MisELF487Service {
	public List<Map<String, Object>> sel_by_brNo_idDup(String brNo, String custId, String dupNo);
	public List<String> sel_never_retrialData_only_Rule1(String brNo, String custId, String dupNo);
	public List<String> sel_never_retrialData_match___HOUSE_or_RMBINS_or_HS_CRE_FG(String brNo, String custId, String dupNo);
	
}
