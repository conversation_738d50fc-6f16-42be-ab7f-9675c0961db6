package com.mega.eloan.lms.mfaloan.service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF412;

public interface MisELF412Service {
	/**
	 * 依分行別取得覆審名單資料
	 * 
	 * @param branch
	 * @return
	 */
	List<Map<String, Object>> getByKeyWithBasicData(String branch);

	Map<String, Object> getByKeyWithBasicData(String branch, String custId,
			String dupNo);

	Map<String, Object> getDataWithPEO(String branch, String custId,
			String dupNo);

	public ELF412 findByPk(String branch, String custId, String dupNo);

	public List<ELF412> findByBranch(String branch);

	public List<Map<String, Object>> sel_gfnGenerateCTL_FLMS180R14(
			String date_s, String date_e);

	public int updateELF412NckdFlag(Date ELF412_LRDATE, String ELF412_NEWADD,
			String ELF412_NEWDATE, String ELF412_NCKDFLAG,
			Date ELF412_NCKDDATE, String ELF412_NCKDMEMO, Date ELF412_NEXTNWDT,
			Date ELF412_NEXTLTDT, Timestamp ELF412_TMESTAMP,
			Date ELF412_UPDDATE, String ELF412_UPDATER, String ELF412_BRANCH,
			String ELF412_CUSTID, String ELF412_DUPNO, String ELF412_ISRESCUE,
            String ELF412_GUARFLAG, String ELF412_NEWRESCUE, String ELF412_NEWRESCUEYM,
            String ELF412_RANDOMTYPE);
}
