$(document).ready(function(){
    $("#buttonPanel").before('<div class=" tit2 color-black" id="searchActionName" name="searchActionName"></div>');
    $("#gridview").remove();
    hideBtn();
    showReport("searchAll");
    function hideBtn(){
        $("#btnInsertExcelData").hide();
        $("#btnInsertInExcelData").hide();
        $("#btnInsertUnExcelData").hide();
        $("#btnInsertLateExcelData").hide();
        $("#btnInsertRecExcelData").hide();
        $("#btnView").hide();
        $("#btnFilter").hide();
        $("#btnDelete").hide();
        
    }
    
    function padLeft(str, lenght){
    	str = "" + str;
        if (str.length >= lenght) 
            return str;
        else             
            return padLeft("0" + str, lenght);
    }
    
    function showReport(queryAction){
        brank();
        var showHeight = 100;
        if (queryAction == 'searchAll') {
            $("#report").show();
            $("#createMonth").hide();
            showHeight = 205;
        } else if (queryAction == 'searchReport') {
            $("#report").hide();
            $("#createMonth").show();
            showHeight = 165;
            if($("#searchAction").val() == '3'){
            	$("#searchDataDateCondition1").hide();
            	$("#searchDataDateCondition2").show();
            }else{
            	$("#searchDataDateCondition1").show();
            	$("#searchDataDateCondition2").hide();
            }
        }
        if ($("#searchActionName").val() == '') {
            $("#searchStartCreateDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1], 10), 2));
            $("#searchEndCreateDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1], 10), 2));
        }
        if ($("#searchActionName").val() == '') {
            $("#searchDataDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1], 10) - 1, 2));
        }
        
        //        if($('select#searchAction option:selected').val() == '3'){
        //    		$("#starDate1").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1],10) - 1,2));
        //    		$("#endDate1").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1],10) - 1,2));
        //    		$('#show1').show(); 
        //    		$('#show2').hide();
        //    	}else{
        //    		$("#searchDataDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1],10) - 1,2));
        //    		$("#show1").hide();
        //            $("#show2").show();
        //    	}
        $("#lms1855new").thickbox({
            title: i18n.lms1855v01['newInfo'],
            width: 500,
            height: showHeight,
            modal: false,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var action = $('select#searchAction option:selected').val();
                    refactorGrid();
                    //YYYY-MM 格式驗證
                    var searchDate = $("#searchDataDate").val();
                    var searchStartCreateDate = $("#searchStartCreateDate").val();
                    var searchEndCreateDate = $("#searchEndCreateDate").val();
                    if (queryAction == 'searchReport') {
                    	if(searchAction == '3'){
                        	if (searchStartCreateDate == "") {
                                //val.ineldate=請輸入年月
                                return CommonAPI.showMessage(i18n.lms1855v01["L1855v01.startDate"] + i18n.def["val.ineldate"]);
                            }
                        	if (searchEndCreateDate == "") {
                                //val.ineldate=請輸入年月
                                return CommonAPI.showMessage(i18n.lms1855v01["L1855v01.endDate"] + i18n.def["val.ineldate"]);
                            }
                            if (!searchStartCreateDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                                //val.date2=日期格式錯誤(YYYY-MM)
                                return CommonAPI.showMessage(i18n.lms1855v01["L1855v01.startDate"] + i18n.def["val.date2"]);
                            }
                            if (!searchEndCreateDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                                //val.date2=日期格式錯誤(YYYY-MM)
                                return CommonAPI.showMessage(i18n.lms1855v01["L1855v01.endDate"] + i18n.def["val.date2"]);
                            }
                        }else{
                        	if (searchDate == "") {
                                //val.ineldate=請輸入年月
                                return CommonAPI.showMessage(i18n.def["val.ineldate"]);
                            }
                            if (!searchDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                                //val.date2=日期格式錯誤(YYYY-MM)
                                return CommonAPI.showMessage(i18n.def["val.date2"]);
                            }
                        }
                    }
                    
                    
                    
                    var msg = "";
                    switch (action) {
                        case '1':
                            msg = type1();
                            break;
                        case '2':
                            msg = type2();
                            break;
                        case '3':
                            msg = type3();
                            break;
                        case '4':
                            msg = type4();
                            break;
                    }
                    if (msg == '' || !msg) {

                        var searchAction = $("#searchAction").val();
                    	if(searchAction == '3'){
                    		$("#searchActionName").val($('select#searchAction option:selected').text() + "(" + searchStartCreateDate + "~" + searchEndCreateDate + ")");
                    	}else{
                    		$("#searchActionName").val($('select#searchAction option:selected').text() + "(" + searchDate + ")");
                    	}
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    //==============================================
    function refactorGrid(){
        //alert('refactorGrid');
        $("#gridview").remove();
        //        $("#gridDiv").empty();
        $("#gridDiv").html('<div id="gridview" />');
    }
    // ================================================================================================================
    
    function brank(){
        $.ajax({
            type: "POST",
            handler: "lms1855m01formhandler",
            data: {
                formAction: "queryBranch"
            },
            success: function(obj){
                //alert(JSON.stringify(obj));
                var data = {
                    border: "none",
                    size: 3,
                    item: obj.item
                };
                $("#order1").setItems(data);
                $("#order2").setItems(data);
                $("#order3").setItems(data);
                $("#order4").setItems(data);
                $("#order5").setItems(data);
                //                $("#order1").setItems({
                //                    // i18n : i18n.samplehome,
                //                    item: obj.item,
                //                    format: "{value} {key}", // ,
                //                    space: false
                //                    // value :
                //                });
                //                $("#order2").setItems({
                //                    // i18n : i18n.samplehome,
                //                    item: obj.item,
                //                    format: "{value} {key}",
                //                    space: false
                //                    // value :
                //                });
                //                $("#order3").setItems({
                //                    // i18n : i18n.samplehome,
                //                    item: obj.item,
                //                    format: "{value} {key}", // ,
                //                    space: false
                //                    // value :
                //                });
                //                $("#order4").setItems({
                //                    // i18n : i18n.samplehome,
                //                    item: obj.item,
                //                    format: "{value} {key}", // ,
                //                    space: false
                //                    // value :
                //                });
                //                $("#order5").setItems({
                //                    // i18n : i18n.samplehome,
                //                    item: obj.item,
                //                    format: "{value} {key}", // ,
                //                    space: false
                //                    // value :
                //                });
            
            }
        });
    }
    // ================================================================================================================
    $("#buttonPanel").find("#btnDelete").click(function(){
        var $gridview = $("#gridview");
        var id = $gridview.getGridParam('selarrrow');
        var content = [];
        for (var i = 0; i < id.length; i++) {
            if (id[i] != "") {
                var datas = $gridview.getRowData(id[i]);
                content.push(datas.mainId);
            }
        }
        if (content.length == 0) {
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        } else {
        	CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
                if (b) {
                    $.ajax({
                        type: "POST",
                        handler: "lms1855m01formhandler",
                        data: {
                            formAction: "deleteListL185m01a",
                            mainId: content,
                            searchAction: $('select#searchAction option:selected').val()
                        },
                        success: function(responseData){
                            $("#gridview").trigger("reloadGrid");
                        }
                    });
                } else {
                }
            })
        }
        
        
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        download(null, null, result);
    }).end().find("#btnInsertInExcelData").click(function(){
        //**產生企金戶未列於覆審名單 */
        thickBoxOpenADD();
    }).end().find("#btnInsertUnExcelData").click(function(){
        //** 產生未於規定期限辦理覆審之企金戶名單 */
        thickBoxOpenADD1();
    }).end().find("#btnInsertLateExcelData").click(function(){
        //** 產生新作增額逾期檢核表 */
        thickBoxOpenADD2();
    }).end().find("#btnInsertRecExcelData").click(function(){
        //** 產生辦理最近授信檢核表 */
        thickBoxOpenADD3();
    }).end().find("#btnFilter").click(function(){
    	showReport("searchReport");
    });
    // =======================================================================================================
    function thickBoxOpenADD1(){
        //資料基準年月日
        //登入所在分行
        brank();
        $("#lms1855Create").thickbox({
            title: i18n.lms1855v01['title2'],
            width: 600,
            height: 250,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(cellvalue, showMsg, oid, id){
                    //YYYY-MM 格式驗證
                    
                    var searchDate = $("#dataDate").val();
                    
                    if (searchDate == "") {
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(i18n.def["val.ineldate"]);
                    }
                    
                    if (!searchDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
                    var choiceBrNos = getBrNos("order5");
                    if (choiceBrNos == "") {
                        //val.inelbranch=請選分行
                        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
                    }
                    
                    $.ajax({
                        handler: "lms1855m01formhandler",
                        type: "POST",
                        dataType: "json",
                        data: $.extend($("#tabForm").serializeData(), {
                            formAction: "transportExcel",
                            mainOid: oid,
                            dataDate: searchDate,
                            brNos: choiceBrNos,
                            searchAction: $("#searchAction").val(),
                            // listName:listName,
                            showMsg: true
                        }),
                        success: function(responseData){
                            $("#gridview").jqGrid("setGridParam", {//重新設定grid需要查到的資料
                                postData: {
                                    searchDataDate: $("#dataDate").val()
                                },
                    			page : 1,
                    			//gridPage : 1,
                                search: true
                            }).trigger("reloadGrid");
                            $("#searchActionName").val($('select#searchAction option:selected').text() + "(" + $("#dataDate").val() + ")");
                        }
                    });
                    
                    $.thickbox.close();
                    
                },
                "cancel": function(){
                
                    $.thickbox.close();
                }
            }
        });
    };
    // =======================================================================================================
    function thickBoxOpenADD(){
    
        //產生企金戶未列於覆審名單
        title = 'title3';
        
        //登入所在分行
        brank();
        $("#lms1855Create1").thickbox({
            title: i18n.lms1855v01[title],
            width: 800,
            height: 250,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(cellvalue, showMsg, oid, id){
                	$("#dataDate1").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1], 10),2));
                    //YYYY-MM 格式驗證
                    var searchDate = $("#dataDate1").val();
                    if (searchDate == "") {
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(i18n.def["val.ineldate"]);
                    }
                    
                    if (!searchDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
                    var choiceBrNos = getBrNos("order2");
                    if (choiceBrNos == "") {
                        //val.inelbranch=請選分行
                        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
                    }
                    
                    $.ajax({
                        handler: "lms1855m01formhandler",
                        type: "POST",
                        dataType: "json",
                        data: $.extend($("#tabForm").serializeData(), {
                            formAction: "transportExcel",
                            mainOid: oid,
                            dataDate: searchDate,
                            brNos: choiceBrNos,
                            searchAction: $("#searchAction").val(),
                            // listName:listName,
                            showMsg: true
                        }),
                        success: function(responseData){
                            $("#gridview").jqGrid("setGridParam", {//重新設定grid需要查到的資料
                                postData: {
                                    searchDataDate: $("#dataDate1").val()
                                },
                                search: true
                            }).trigger("reloadGrid");
                            $("#searchActionName").val($('select#searchAction option:selected').text() + "(" + $("#dataDate1").val() + ")");
                        }
                    });
                    
                    $.thickbox.close();
                    
                },
                "cancel": function(){
                
                    $.thickbox.close();
                }
            }
        });
    };
    //});
    // =======================================================================================================
    function thickBoxOpenADD2(){
        //title3=產生企金戶未列於覆審名單
        //登入所在分行
        brank();
        $("#lms1855Create2").thickbox({
            title: i18n.lms1855v01["title4"],
            width: 800,
            height: 250,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(cellvalue, showMsg, oid, id){
                    //YYYY-MM 格式驗證
                    //起始日期
                    var starDate = $("#starDate").val();
                    //迄至日期
                    var endDate = $("#endDate").val();
                    var str = "";
                    
                    if (starDate == "") {
                        //L1855v01.startDate=起始日期
                        str = i18n.lms1855v01["L1855v01.startDate"]
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
                    }
                    if (endDate == "") {
                        //L1855v01.endDate=迄至日期
                        str = i18n.lms1855v01["L1855v01.endDate"]
                        //val.ineldate=請輸入年月
                        return CommonAPI.showMessage(str + i18n.def["val.ineldate"]);
                    }
                    if (!starDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
                    if (!endDate.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                        //val.date2=日期格式錯誤(YYYY-MM)
                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                    }
                    
                    var choiceBrNos = getBrNos("order3");
                    if (choiceBrNos == "") {
                        //val.inelbranch=請選分行
                        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
                    }
                    $.ajax({
                        handler: "lms1855m01formhandler",
                        type: "POST",
                        dataType: "json",
                        data: $.extend($("#tabForm").serializeData(), {
                            formAction: "transportExcel",
                            mainOid: oid,
                            starDate: starDate,
                            endDate: endDate,
                            brNos: choiceBrNos,
                            searchAction: $("#searchAction").val(),
                            // listName:listName,
                            showMsg: true
                        }),
                        success: function(responseData){
                            $("#gridview").trigger("reloadGrid");
                            $("#searchActionName").val($('select#searchAction option:selected').text() + "(" + $("#starDate").val() + "~" + $("#endDate").val() + ")");
                        }
                    });
                    $.thickbox.close();
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };
    //=======================================================================================================
    function thickBoxOpenADD3(){
        //title4=產生新作增額逾期檢核表
        //登入所在分行
        brank();
        $("#lms1855Create3").thickbox({
            title: i18n.lms1855v01["title5"],
            width: 600,
            height: 250,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(cellvalue, showMsg, oid, id){
                
                    var dataDate = $("#dataDate").val()
                    
                    var choiceBrNos = getBrNos("order4");
                    if (choiceBrNos == "") {
                        //val.inelbranch=請選分行
                        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
                    }
                    $.ajax({
                        handler: "lms1855m01formhandler",
                        type: "POST",
                        dataType: "json",
                        data: $.extend($("#tabForm").serializeData(), {
                            formAction: "transportExcel",
                            mainOid: oid,
                            brNos: choiceBrNos,
                            searchAction: $("#searchAction").val(),
                            // listName:listName,
                            showMsg: true
                        }),
                        success: function(responseData){
                            $("#gridview").jqGrid("setGridParam", {//重新設定grid需要查到的資料
                                postData: {
                                    searchDataDate: $("#searchDataDate").val()
                                },
                                search: true
                            }).trigger("reloadGrid");
                            //                        	$("#searchActionName").val($('select#searchAction option:selected').text() + "(" + $("#dataDate").val() + ")");
                        }
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };
    });//最外層
function getBrNos(idName){
    var choiceBrNos = "";
    $('input:checkbox:checked[id="' + idName + '"]').each(function(i){
        if (choiceBrNos != '') {
            choiceBrNos = choiceBrNos + "^";
        }
        choiceBrNos = choiceBrNos + this.value;
    });
    return choiceBrNos;
}

// ======================================================================================================
function type1(){
    //button.InsertUnExcelData
    var choiceBrNos = getBrNos("order1");
    if (choiceBrNos == "") {
        //val.inelbranch=請選分行
        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
    }
    $("#btnInsertUnExcelData").show();
    $("#btnView").show();
    $("#btnFilter").show();
    $("#btnDelete").show();
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms1855gridhandler',
        height: 350, // 設定高度
        width: 785,
        autowidth: false,
        multiselect : true,
        postData: {
            formAction: "queryL185M01a",
            searchAction: $("#searchAction").val(),
            searchDataDate: $("#searchDataDate").val(),
            brNos: choiceBrNos
            //	docStatus : viewstatus
        },
        colModel: [{
            name: 'oid',
            hidden: true
        }, {
            name: 'uid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'listName',
            hidden: true
        }, {
            name: 'reportFile',
            hidden: true
        }, {
            colHeader: i18n.lms1855v01["L185M01a.ownBrId"], // 單位別
            align: "center",
            width: 100,
            sortable: true,
            name: 'ownBrId'
        }, {
            colHeader: i18n.lms1855v01["L185M01a.dataDate"], // 資料基準日
            align: "center",
            width: 100,
            sortable: true,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            },
            name: 'dataDate'
        }, {
            colHeader: i18n.lms1855v01["L185M01a.conut"], // 資料筆數
            align: "right",
            width: 100,
            sortable: true,
            name: 'recCount',
            formatter: GridFormatter.number['addComma']
        }, {
            colHeader: i18n.lms1855v01["L185M01a.createTime"], // 產生日期
            align: "center",
            width: 100,
            sortable: true,
            name: 'createTime'
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            download(null, null, data);
        }
        
    });
}

// ======================================================================================================
function type2(){
    var choiceBrNos = getBrNos("order1");
    if (choiceBrNos == "") {
        //val.inelbranch=請選分行
        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
    }
    $("#btnInsertInExcelData").show();
    $("#btnView").show();
    $("#btnFilter").show();
    $("#btnDelete").show();
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms1855gridhandler',
        height: 350, // 設定高度
        width: 785,
        autowidth: false,
        multiselect : true,
        postData: {
            formAction: "queryL185M01a",
            searchAction: $("#searchAction").val(),
            searchDataDate: $("#searchDataDate").val(),
            brNos: choiceBrNos
        },
        colModel: [{
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            name: 'uid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'reportFile',
            hidden: true
        }, {
            colHeader: i18n.lms1855v01["L185M01a.ownBrId"], // 單位別
            align: "left",
            width: 100,
            sortable: true,
            name: 'ownBrId'
        }, {
            colHeader: i18n.lms1855v01["L185M01a.dataDate"], //資料基準日
            align: "center",
            width: 100,
            sortable: true,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            },
            name: 'dataDate'
        }, {
            colHeader: i18n.lms1855v01["L185M01a.acctCount"], //戶數
            align: "right",
            width: 100,
            sortable: true,
            name: 'acctCount'
        }, {
            colHeader: i18n.lms1855v01["L185M01a.uLoanYCount"], //聯貸筆數
            align: "right",
            width: 100,
            sortable: true,
            name: 'uLoanYCount'
        }, {
            colHeader: i18n.lms1855v01["L185M01a.uLoanNCount"], //非聯貸筆數
            align: "right",
            width: 100,
            sortable: true,
            name: 'uLoanNCount'
        }, {
            colHeader: i18n.lms1855v01["L185M01a.createTime"], // 產生日期
            align: "center",
            width: 100,
            sortable: true,
            name: 'createTime'
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            download(null, null, data);
        }
        
    });
}

//======================================================================================================
function type3(){
    var choiceBrNos = getBrNos("order1");
    if (choiceBrNos == "") {
        //val.inelbranch=請選分行
        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
    }
    $("#btnInsertLateExcelData").show();
    $("#btnView").show();
    $("#btnFilter").show();
    $("#btnDelete").show();
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms1855gridhandler',
        height: 350, // 設定高度
        width: 785,
        autowidth: false,
        multiselect : true,
        postData: {
            formAction: "queryL185M01a",
            searchAction: $("#searchAction").val(),
            searchStartCreateDate: $("#searchStartCreateDate").val(),
            searchEndCreateDate: $("#searchEndCreateDate").val(),
            brNos: choiceBrNos
            //            searchDataDate: $("#starDate1").val(),
            //            searchDataEDate: $("#endDate1").val()
            //docStatus : viewstatus
        },
        colModel: [{
            colHeader: "oid",
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'uid', //col.id,
            hidden: true //是否隱藏
        }, {
            name: 'mainId', //col.id
            hidden: true //是否隱藏
        }, {
            name: 'rptType', //col.id,
            hidden: true //是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms1855v01["L185M01a.ownBrId"], // 單位別
            align: "left",
            width: 100,
            sortable: true,
            name: 'ownBrId'
        }, {
            colHeader: i18n.lms1855v01["L185M01a.sDate"], //資料基準日(起)
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'dataDate' //col.id
        }, {
            colHeader: i18n.lms1855v01["L185M01a.eDate"], //資料基準日(迄)
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'dataEDate' //col.id
        }, {
            colHeader: i18n.lms1855v01["L185M01a.delayCount"], //逾期筆數
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'delayCount' //col.id
        }, {
            colHeader: i18n.lms1855v01["L185M01a.acctCount"], //戶數合計
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'acctCount' //col.id
        }, {
            colHeader: i18n.lms1855v01["L185M01a.createTime"], // 產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            download(null, null, data);
        }
    });
}

//======================================================================================================
function type4(){

    var choiceBrNos = getBrNos("order1");
    if (choiceBrNos == "") {
        //val.inelbranch=請選分行
        return CommonAPI.showMessage(i18n.def["val.inelbranch"]);
    }
    $("#btnInsertRecExcelData").show();
    $("#btnView").show();
    $("#btnFilter").show();
    $("#btnDelete").show();
    var grid = $("#gridview").iGrid({
        rownumbers: true,
        handler: 'lms1855gridhandler',
        width: 785,
        autowidth: false,
        height: 350, // 設定高度
        multiselect : true,
        postData: {
            formAction: "queryL185M01a",
            searchAction: $("#searchAction").val(),
            searchDataDate: $("#searchDataDate").val(),
            brNos: choiceBrNos
            //docStatus : viewstatus
        },
        colModel: [{
            colHeader: "oid",
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'uid', //col.id,
            hidden: true //是否隱藏
        }, {
            name: 'mainId', //col.id
            hidden: true //是否隱藏
        }, {
            name: 'reportFile', // col.id
            hidden: true
            // 是否隱藏
        }, {
            colHeader: i18n.lms1855v01["L185M01a.ownBrId"], // 單位別
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'branchId' //col.id
        }, {
            colHeader: i18n.lms1855v01["L185M01a.delayCount"], //逾期筆數
            align: "right",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'delayCount' //col.id
        }, {
            colHeader: i18n.lms1855v01["L185M01a.conut"], // 資料筆數
            align: "right",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'recCount' // col.id
        }, {
            colHeader: i18n.lms1855v01["L185M01a.createTime"], // 產生日期
            align: "center",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            download(null, null, data);
        }
        
    });
}

//======================================================================================================
//下載EXCEL
function download(cellvalue, options, data){
    //alert(data.rptType);
    var rptTypeOid = data.reportFile;
    
    $.capFileDownload({
        //  handler: "simplefiledwnhandler",
        data: {
            fileOid: rptTypeOid
        }
    });
    
}

function padLeft(str, lenght){
	str = "" + str;
    if (str.length >= lenght) 
        return str;
    else         
        return padLeft("0" + str, lenght);
}

function changeReport(){
    $("#searchDataDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1], 10) - 1, 2));
    //	if($('select#searchAction option:selected').val() == '3'){
    //		$("#starDate1").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1],10) - 1,2));
    //		$("#endDate1").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1],10) - 1,2));
    //		$('#show1').show(); 
    //		$('#show2').hide();
    //	}else{
    //		$("#searchDataDate").val(CommonAPI.getToday().split("-")[0] + "-" + padLeft(parseInt(CommonAPI.getToday().split("-")[1],10) - 1,2));
    //		$('#show1').hide(); 
    //		$('#show2').show();
    //	}
}
