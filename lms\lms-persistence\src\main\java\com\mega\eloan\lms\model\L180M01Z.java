/* 
 * L180M01Z.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 覆審名單主檔 **/
@Entity
@Table(name="L180M01Z", uniqueConstraints = @UniqueConstraint(columnNames = {"dataDate","branchId"}))
public class L180M01Z extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 資料日期<p/>
	 * ELF411_DATAYM<br/>
	 *  YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DATADATE", columnDefinition="DATE")
	private Date dataDate;

	/** 
	 * 分行別<p/>
	 * ELF411_BRNO
	 */
	@Column(name="BRANCHID", length=3, columnDefinition="CHAR(3)")
	private String branchId;

	/** 更新完成時間 **/
	@Column(name="FINISHTIME", columnDefinition="TIMESTAMP")
	private Date finishTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得資料日期<p/>
	 * ELF411_DATAYM<br/>
	 *  YYYY-MM-01
	 */
	public Date getDataDate() {
		return this.dataDate;
	}
	/**
	 *  設定資料日期<p/>
	 *  ELF411_DATAYM<br/>
	 *  YYYY-MM-01
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 
	 * 取得分行別<p/>
	 * ELF411_BRNO
	 */
	public String getBranchId() {
		return this.branchId;
	}
	/**
	 *  設定分行別<p/>
	 *  ELF411_BRNO
	 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 取得更新完成時間 **/
	public Date getFinishTime() {
		return this.finishTime;
	}
	/** 設定更新完成時間 **/
	public void setFinishTime(Date value) {
		this.finishTime = value;
	}
}
