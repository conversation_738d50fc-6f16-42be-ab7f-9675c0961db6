package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上勞工紓困貸款
 * </pre>
 * 
 * @since 2020/5/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/5/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1220v07")
public class CLS1220V07Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CLSDocStatusEnum.編製中);
		
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		list.add(LmsButtonEnum.Filter);
		list.add(LmsButtonEnum.QueryCustLoanRecord);
		list.add(LmsButtonEnum.View);
		list.add(LmsButtonEnum.CreateExcel);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ssoUnitNo = user.getSsoUnitNo();
		if(Util.equals("900", ssoUnitNo)||Util.equals("943", ssoUnitNo)){
			list.add(LmsButtonEnum.CaseToChange);
		}
		// 加上Button
		addToButtonPanel(model, list);
		// build i18n
		renderJsI18N(CLS1220M03Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1220V07Page');");
	}

}
