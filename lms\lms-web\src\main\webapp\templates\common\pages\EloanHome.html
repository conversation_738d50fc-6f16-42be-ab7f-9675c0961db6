<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<meta name="_csrf" th:content="${_csrf.token}" />
		<meta name="_csrf_header" th:content="${_csrf.headerName}" />
		<link rel="stylesheet" th:href="@{/css/base_css.css}" />
		<link rel="stylesheet" th:href="@{/css/core_css.css}" />
		<link rel="stylesheet" th:href="@{/css/menu_css.css}" />
		<script type="text/javascript">
	        var webroot = window.location.pathname.substr(0, window.location.pathname.indexOf("/app"));
	        var jsCache = null;
	        var baseUrl = '../../';
	        var __ajaxHandler='';
	        
	        function validPath(inputPath){
	            if (/-web/.test(inputPath)) {
	                return inputPath;
	            }
	            return '';
	        }
		</script>
		<link rel="icon" href="img/c/mega_logo.gif" type="image/x-icon" th:href="@{/img/c/mega_logo.gif}" />
		<link rel="shortcut icon" href="img/c/mega_logo.gif" type="image/x-icon" th:href="@{/img/c/mega_logo.gif}" />
		<script type="text/javascript" th:src="@{/js/common/first_check.js}"></script>
		<script type="text/javascript" th:src="@{/js/lib/purify/3.0.6/purify.js}"></script>
		<script type="text/javascript" th:src="@{/js/lib/requirejs/2.3.6/require.min.js}"></script>
		<script type="text/javascript" th:src="@{/js/build-main.js}"></script>
        <title>[[#{EloanHome.page.title}]]</title>
    </head>
	<body>
		<script>
			loadScript('pagejs/base/EloanHome');
		</script>

	    <!-- 將 java 程式寫的 javascript 列出 -->
	    <th:block th:each="jsAttr : ${PAGE_JS_LIST}">
	        <script type="text/javascript" th:utext="${jsAttr.content}" th:id="${jsAttr.id}">
			</script>
	    </th:block>
	    
		<div class="body">
			<div id="headerarea">
				<div class="header_wrap" th:insert="~{common/panels/EloanHeaderPanel :: header}">
		            user information
		        </div>
	        </div>
			<div id="header_menu" >
				<div th:utext="${htmlTopMenu}" th:remove="tag">
                	<dl id="menu" class="clear block" >
                    	<dd><a href="../main.htm" class="menu0"><b>回首頁</b></a></dd>
                    	<dd><a href="./ces_main.htm" class="menu0"><b>徵信管理</b></a></dd>
                	</dl>
				</div>
			</div>
			<div class="clear" >
				<table class="bottomFrame">
					<tr>
						<td id="leftFrame">
							<div class="menu2top"></div>
<!-- 							tree menu -->
							<div id="mLeft" class="left-nav" th:utext="${htmlLeftMenu}">
								<ul id="menu2" style="width:100%">   
								 	<li><a herf="#" target="mainCtx"><span class="menu-icon icon-1"></span>待辦案件</a></li>
			                        <li><a herf="#"><span class="menu-icon icon-2"></span>關係人資料登錄作業</a>
										<ul class="menu_sub">
											<li><a href="app/person/SAP110V01" target="mainCtx">．編制中</a></li>
											<li><a href="app/person/SAP110V02" target="mainCtx">．待覆核</a></li>
											<li><a href="app/person/SAP110V03" target="mainCtx">．已核定</a></li>
										</ul>
			                        </li>
			                    </ul>
							</div>
							<div id="goMainHome" class="block">
                                <span id="gohome"></span>
                            </div>
							<div class="menu2bottom"></div>
						</td>
						<td id="mainFrame">
							<div id="mainCtx">
							<!--<div id="demo">這裡是放內容的區塊</div>-->
							</div>
						</td>
					</tr>
				</table>
			</div>
		</div>
    </body>
</html>
