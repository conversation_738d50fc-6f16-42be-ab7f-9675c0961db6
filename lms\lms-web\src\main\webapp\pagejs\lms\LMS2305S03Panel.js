var LMS230S03Action = {
    formId: "#CntrDocForm",
    gridId: "#CntrDocGridView",
    
    /**
     * 開啟修改視窗
     * @param {Object} cellvalue 欄位顯示
     * @param {Object} options   欄位設定
     * @param {Object} rowObject 欄位值
     */
    openBox: function(cellvalue, options, data){
        var $form = $(LMS230S03Action.formId);
        $form.reset();
        $.ajax({
            handler: LMS2305Action.fhandle,//lms2305m01formhandler
            action: "queryCntrDoc",
            data: {
                oid: data.oid
				}
            }).done(function(json){
            
                $form.injectData(json);
                $("#nuseMemo").trigger("change");
                LMS230S03Action.setReason(json.reason);
                
                $("#openCnrtDocBox").thickbox({
                    //doc.cntrDoc=額度明細表資訊
                    title: i18n.lms2305m01['doc.cntrDoc'],
                    width: 650,
                    height: 450,
                    modal: true,
                    open: function(){
                        if (LMS2305Action.checkReadonly()) {
                            $form.lockDoc();
                        }
                    },
                    i18n: i18n.def,
                    buttons: {
                        "saveData": function(){
                            if ($form.valid()) {
                                $.ajax({
                                    handler: LMS2305Action.fhandle,//lms2305m01formhandler
                                    action: "saveCntrDoc",
                                    data: {
                                        oid: data.oid,
                                        CntrDocForm: JSON.stringify($form.serializeData()),
                                        reason: LMS230S03Action.getReason()
										}
                                    }).done(function(){
                                        $.thickbox.close();
                                        $.thickbox.close();
                                        LMS230S03Action.reloadGrid();
                                });
                            }
                            
                        },
                        "close": function(){
                            $.thickbox.close();
                        }
                    }
                });
        });
    },
    /**重新引進*/
    pullinAgain: function(){
        //actoin_001=是否執行此動作?
        API.confirmMessage(i18n.def['actoin_001'], function(res){
            if (res) {
                $.ajax({
                    handler: LMS2305Action.fhandle,//lms2305m01formhandler
                    action: "pullinAgain",
                    data: {
                        mainOid: responseJSON.mainOid
						}
                    }).done(function(){
                        LMS230S03Action.reloadGrid();
                });
            }
        });
        
        
    },
    /**重新整理grid*/
    reloadGrid: function(){
        $(this.gridId).trigger('reloadGrid');
    },
    /**
     * 取得註銷原因
     * return 01|02
     */
    getReason: function(){
        var data = [];
        $("[name=reason]:checked").each(function(v, k){
            data.push($(k).val());
        });
        return data.join("|")
    },
    /**
     * 設定註銷原因
     * @param {Object} data 01|02
     */
    setReason: function(data){
        data = $.trim(data).split("|");
        $("[name=reason]").val(data);
        //if (data.join("").indexOf("99") != -1) {
        $("#reasonDrcSapn").show();
        //}
    }
};

$(function(){
    /**重新引進*/
    $("#pullinAgain").click(function(){
        LMS230S03Action.pullinAgain();
    });
    
    $("#reason").setItems({
        space: false,
        item: API.loadCombos("lms2305s01_reason").lms2305s01_reason,
        format: "{key}",
        size: "1",
        fn: function(){
            var $reasonDrcSapn = $("#reasonDrcSapn");
            $reasonDrcSapn.hide();
            var data = LMS230S03Action.getReason();
            //if (data.indexOf("99") != -1) {
            $reasonDrcSapn.show();
            //}
            $("[name=reason][value=10]").prop("disabled", true);
        }
    });
    
    $("#nuseMemo").change(function(even, type){
        var value = $(this).val();
        var $signDateTr = $("#signDateTr");
        var $resonTr = $("#resonTr");
        var $reasonDrcSapn = $("#reasonDrcSapn");
        //J-111-0551_05097_B1003 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
        var $signAmtTr = $("#signAmtTr");
        $("#reasonDrcSapn").hide();
        $signDateTr.hide();
        $resonTr.hide();
        //J-111-0551_05097_B1003 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
        $signAmtTr.hide();
        if (value == "3" || value == "Y") {
            $signDateTr.show();
        } else if (value == "D" ||  value == "H" || value == "I" ) {
            $resonTr.show();
            $reasonDrcSapn.show();
           
        }
        
        //J-111-0551_05097_B1003 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
        if (value == "3"){
        	$signAmtTr.show();
        	$("input[name='isSignAmtLowerApplyAmt']").trigger("change");
        }else{
        	$('#signAmt_S').val('');
		    $('#signAmt_N').val('');
        }
        
        $("[name=reason][value=10]").prop("disabled", true);
    });
    
    //J-111-0551_05097_B1003 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
    $("input[name='isSignAmtLowerApplyAmt']").change(function(){
        var isSignAmtLowerApplyAmt = $("input[name='isSignAmtLowerApplyAmt']:radio:checked").val();
        if (isSignAmtLowerApplyAmt == 'Y') {
        	$('#showSignAmt').show();
        } else {
        	$('#signAmt_S').val('');
		    $('#signAmt_N').val('');
		    $('#showSignAmt').hide();
        }
    });
    
    // 額度明細表選擇Grid
    var $CntrDocGrid = $(LMS230S03Action.gridId).iGrid({
        handler: LMS2305Action.ghandle,//"lms2305gridhandler"
        height: "240px",
        width: "100%",
        sortname: 'cntrNo',
        postData: {
            formAction: "queryL230S01A",
            mainId: responseJSON.mainId
        },
        shrinkToFit: false,
        rowNum: 10,
        autowidth: true,
        colModel: [{
            colHeader: i18n.lms2305m01['l230m01a.custName'],// 借款人名稱
            name: 'custName',
            width: 150,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms2305m01['l230m01a.cntrNo'],//額度序號
            name: 'cntrNo',
            width: 100,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2305m01['l230m01a.lnSubject'],//科目
            name: 'lnSubject',
            width: 150,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2305m01["l230m01a.proPerty"],//性質
            name: 'proPerty',
            width: 150,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2305m01["l230m01a.currentApplyCurr"],//額度幣別
            name: 'currentApplyCurr',
            width: 30,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2305m01["l230m01a.currentApplyAmt"],//額度金額
            name: 'currentApplyAmt',
            width: 100,
            sortable: true,
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 2//小數點到第幾位
            }
        }, {
            colHeader: i18n.lms2305m01["l230m01a.status"],// 狀態註記(新案才需要維護)
            name: 'nuseMemo',
            width: 100,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms2305m01['l230m01a.reason'],// 不簽約原因
            name: 'reason',
            width: 100,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.lms2305m01['l230m01a.signingDay'],// 已簽約日
            name: 'signDate',
            width: 100,
            sortable: true,
            align: "center"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = $(LMS230S03Action.gridId).getRowData(rowid);
            LMS230S03Action.openBox(null, null, data);
        }
    });
});
