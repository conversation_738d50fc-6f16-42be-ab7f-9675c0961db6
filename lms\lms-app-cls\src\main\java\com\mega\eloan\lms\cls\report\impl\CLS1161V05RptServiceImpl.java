package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1161V05RptService;
import com.mega.eloan.lms.cls.service.CLS1161Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service("cls1161v05rptservcie")
public class CLS1161V05RptServiceImpl implements FileDownloadService, CLS1161V05RptService{
	
	@Resource
	CLS1161Service cls1161Service;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String fmtType = Util.trim(params.getString("fmtType"));
			if(Util.equals("1A", fmtType)){
				baos = (ByteArrayOutputStream) this.gen(params, "LNF916S", "LNDLNS16");
			}else if(Util.equals("1B", fmtType)){
				baos = (ByteArrayOutputStream) this.gen(params, "LNF916S", "LNDLNS26");
			}else if(Util.equals("2A", fmtType)){
				baos = (ByteArrayOutputStream) this.gen(params, "LNF919S", "LSDLNS09");	
			}else if(Util.equals("2B", fmtType)){
				baos = (ByteArrayOutputStream) this.gen(params, "LNF919S", "LSDLNS19");
			}else if(Util.equals("2C", fmtType)){
				baos = (ByteArrayOutputStream) this.gen(params, "LNF919S", "LSDLNS29");
			}else if(Util.equals("2D", fmtType)){
				baos = (ByteArrayOutputStream) this.gen(params, "LNF919S", "LSDLNS39");
			}else if(Util.equals("2E", fmtType)){
				baos = (ByteArrayOutputStream) this.gen(params, "LNF919S", "LSDLNS49");
			}else if(Util.equals("3", fmtType)){
				baos = (ByteArrayOutputStream) this.gen(params, "LNF917S", "");	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}			
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	private ByteArrayOutputStream gen(PageParameters params, String tableNm, String formId) throws IOException, Exception {
		
		String custId = Util.trim(params.getString("custId"));
		Date procDateB = TWNDate.valueOf(Util.trim(params.getString("procDateB")));
		Date procDateE = TWNDate.valueOf(Util.trim(params.getString("procDateE")));
		
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		
		cls1161Service.export_Superficies(outputStream, custId, procDateB, procDateE, tableNm, formId);
		
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
	
	
}
