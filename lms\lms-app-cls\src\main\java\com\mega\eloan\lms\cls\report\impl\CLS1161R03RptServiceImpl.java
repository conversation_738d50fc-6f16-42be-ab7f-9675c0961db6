package com.mega.eloan.lms.cls.report.impl;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.report.CLS1161R03RptService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01Q;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service("cls1161r03rptservcie")
public class CLS1161R03RptServiceImpl implements
	FileDownloadService, CLS1161R03RptService {

	@Resource
	CLSService clsService;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			String mode = Util.trim(params.getString("mode"));
			if(Util.equals("A", mode)){
				baos = (ByteArrayOutputStream) this.generateXls(params);	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}			
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	@SuppressWarnings("unchecked")
	private ByteArrayOutputStream generateXls(PageParameters params) throws IOException, Exception {
		
		String mainId = Util.trim(params.getString("mainId"));
		
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		if(true){
			ISearch search = clsService.getMetaSearch();
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId",
					mainId);
			search.addOrderBy("custNo");
			search.setMaxResults(Integer.MAX_VALUE);
			Page<? extends GenericBean> page = clsService.findPage(
					C120M01A.class, search);
						
			proc_xls(outputStream, (List<C120M01A>)page.getContent());
		}
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}	

	private void proc_xls(
			ByteArrayOutputStream outputStream,
			List<C120M01A> meta_list) throws IOException, WriteException {
		
		WritableWorkbook workbook = null;
		WritableSheet sheet1 = null;
		if (true) {
			// ---
			workbook = Workbook.createWorkbook(outputStream);
			sheet1 = workbook.createSheet("Sheet1", 0);
			// ======
			WritableFont headFont = new WritableFont(WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatR = new WritableCellFormat(headFont);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatL_Border = new WritableCellFormat(
					cellFormatL);
			{
				cellFormatL_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}

			WritableCellFormat cellFormatR_Border = new WritableCellFormat(
					cellFormatR);
			{
				cellFormatR_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			// ======
			
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("員工編號", 12);
			headerMap.put("借款人身分證字號", 15);
			headerMap.put("借款人姓名", 15);
			headerMap.put("年薪(新台幣萬元)", 10);
			headerMap.put("其他所得(新台幣萬元)", 10);
			headerMap.put("年資(年)", 10);
			headerMap.put("非房貸模型初始評等", 10);
			headerMap.put("升降等", 6);
			headerMap.put("調整理由", 12);
			headerMap.put("非房貸模型最終評等", 10);
			headerMap.put("票交所與聯徵負面資訊", 15);
			headerMap.put("聯徵查詢日期", 14);
			headerMap.put("票信查詢日期", 14);


			int totalColSize = headerMap.size();

			List<String[]> rows = new ArrayList<String[]>();
			for (C120M01A meta : meta_list) {
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				C120S01B c120s01b = clsService.findC120S01B(meta);
				C120S01Q c120s01q = meta.getC120s01q();
				
				if(c120s01b==null){
					c120s01b = new C120S01B();
				}
				if(c120s01q==null){
					c120s01q = new C120S01Q();
				}
				arr[0] = Util.trim(meta.getStaffNo());
				arr[1] = Util.trim(meta.getCustId())+Util.trim(meta.getDupNo());
				arr[2] = Util.trim(meta.getCustName());				
				arr[3] = Util.trim(c120s01b.getPayAmt());
				arr[4] = Util.trim(c120s01b.getOthAmt());
				arr[5] = LMSUtil.pretty_numStr(c120s01b.getSeniority());
				arr[6] = Util.trim(c120s01q.getGrade1());
				String s7 = "";
				String s8 = "";
				if(true){
					if (Util.equals(c120s01q.getAdjustStatus(), "1")) {
						s7 = "升"+c120s01q.getGrade2()+"等";
						s8 = Util.trim(c120s01q.getAdjustReason());
					} else if (Util.equals(c120s01q.getAdjustStatus(), "2")) {
						s7 = "降"+c120s01q.getGrade2()+"等";
						s8 = Util.trim(c120s01q.getAdjustReason());
					}	
				}
				arr[7] = s7;
				arr[8] = s8;
				arr[9] = Util.trim(c120s01q.getGrade3());
				arr[10] = Util.trim(ClsUtil.c120s01q_negativeInfo(c120s01q));
				arr[11] = Util.trim(TWNDate.toAD(c120s01q.getJcicQDate()));
				arr[12] = Util.trim(TWNDate.toAD(c120s01q.getEtchQDate()));
				// ---
				rows.add(arr);
			}

			// ==============================
			int rowIdx = 0;			
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label(colIdx, rowIdx, h, cellFormatL_Border));
				// ---
				colIdx++;
			}
			// ==============================

			rowIdx = 1;
			int i = 0;
			for (String[] arr : rows) {
				int colLen = arr.length;
				for (int i_col = 0; i_col < colLen; i_col++) {
					sheet1.addCell(new Label(i_col, rowIdx + i, arr[i_col],
							i_col >=3 && i_col<= 5 ? cellFormatR_Border
									: cellFormatL_Border));
				}
				// ---
				i++;
			}

			workbook.write();
			workbook.close();
		}
	}
}
