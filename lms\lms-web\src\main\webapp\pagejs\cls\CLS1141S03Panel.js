var lastSel;

function dfd_upd_caseDoc___tabDoc_cntrNo_npl(tabMainId){
	return $.ajax({handler: "cls1141m01formhandler", action: "upd_caseDoc___tabDoc_cntrNo_npl", formId: 'empty', 
		 data:{'caseMainId': '', 'simplifyFlag' : ''
			 , 'tabMainId': tabMainId, 'tab_printSeq' : 1
		 },success: function(json){
			 ilog.debug("{done}json_resp_upd_caseDoc___tabDoc_cntrNo_npl , {cntrNo="+json.cntrNo+"}");
		 }
	});
}
function dfd_import_property7_cntrNo(caseMainId, custOid, importBisFlag ){
	var my_dfd = $.Deferred();  
	$.ajax({handler: "cls1151m01formhandler", action: "addL140m01a", formId: 'empty', 
		 data:{'mainId': caseMainId, 'custOid': custOid
		 },success: function(json_addL140m01a){
			 ilog.debug("{done}dfd_import_property7_cntrNo___{addL140m01a}");
			 if(importBisFlag =="Y"){
				 $.ajax({handler: "cls1141m01formhandler", action: "getBis", formId: 'empty', 
					 data:{'mainId': caseMainId
						 , 'refresh' : false
						 , 'trigger_in_dfd_chain' : true
					 },success: function(json_getBis){
						 //因在 server 端寫 result.set(CapConstants.AJAX_NOTIFY_MESSAGE, ...)
						 $.thickbox.close();
						 ilog.debug("{done}dfd_import_property7_cntrNo___{getBis}");
						 //~~~
						 my_dfd.resolve();
					 }
				});	 
			 }else{
				 my_dfd.resolve();				 
			 }
		 }
	});
	return my_dfd.promise();
}

var CLS1141S03Action = {
    itemType: "1",
    isInit: false,
    ghandle: "cls1151gridhandler",
    fhandle: "cls1151m01formhandler",
    formId: "#editBoxForm",
    grid: null,
    height_pteamappGrid:150 ,
    /**  
     * 判斷是否已有額度明細表
     * return  true ,false
     */
    isCheckGrid: function(){
        var countGrid = this.grid.jqGrid('getGridParam', 'records');
        if (countGrid == 0) {
            //  CLS1141.045=尚未登錄額度明細表，無法執行此動作
            API.showMessage(i18n.cls1141m01['CLS1141.045']);
            return false;
        }
        return true;
    },
    /**
     * 設定此份文件為何種案件 1額度明細表、2額度批覆表、3母行法人提案意見
     */
    setItemType: function(){
        switch (responseJSON.page + "") {
            case "03"://額度明細表
                CLS1141S03Action.itemType = "1";
                break;
            case "11"://額度批覆表 
                CLS1141S03Action.itemType = "2";
                break;
        }
    },
    initEvent: function(){
        /**
         * 產生額度明細表
         */
        $("#openNewList").click(function(){
            CLS1141S03Action.openCreateBox();
        });
        /**
         * 複製額度明細表
         */
        $("#copyCntrNoDoc").click(function(){
            CLS1141S03Action.copyCntrNoDoc();
        });
        /**
         * 計算合計
         */
        $("#countValue").click(function(){
            if (CLS1141S03Action.isCheckGrid()) {        	
            	$.when( CLS1141S03Action.checkC120S01P() ).done(function(){
            		CLS1141S03Action.checkSend_CLS_L120M01A().done(function(json){
            			CLS1141S03Action.countValue().done(function(json_countValue){
							CLS1141S03Action.processMortgageRatioValidation().done(function(){
	            				CLS1141S03Action.do_chkTotalNeedChange().done(function(json_do_chkTotalNeedChange){
	            					if(json_do_chkTotalNeedChange.val=="ReCount") { //--必須重新計算
	            						//詢問是否調整「性質」的順序
	                        			CLS1141S03Action.ask_after_countValue(json_countValue.printSeqMsg).done(function(json_ask_after_countValue){
	                        				if (json_countValue.theCase) {
	                                            //幣別
	                                            $("#countEditBoxCurrSelect").setItems({
	                                                item: json_countValue.curr,
	                                                format: "{value} - {key}"
	                                            });
	                                            //請選擇計算幣別
	                                            CLS1141S03Action.selecCountMoney();
	                                        } else {
	                                            //調整視窗
	                                            CLS1141S03Action.editCount(false, json_ask_after_countValue.show_cfm_msg=="Y"?true:false);
	                                        } 
	                            		}); 
	            					}else if(json_do_chkTotalNeedChange.val=="UsePrevious") {    
	            						$.ajax({
	            				            handler: CLS1141S03Action.fhandle,
	            				            action: "setCntrCompleteCaculateTotal",
	            				            formId: 'empty',
	            				            data: {
	            				                itemType: CLS1141S03Action.itemType,
	            				                mainId: $("#mainId").val()
	            				            },
	    									success: function(obj){
	    										//runSuccess=執行成功
	    										CLS1141S03Action.reloadGrid();
	    										CommonAPI.showMessage(i18n.def["runSuccess"]);
	    									},
	    									complete:function(result){    
	    										CLS1141S03Action.refreshPage();
	    						            }
	    								});
	            					}
	            					
	            				});
            				});
                		});            		
            		});            		
                });       	
            }
        });
        /**
         * 設定列印順序
         */
        $("#printviewBt").click(function(){
            if (CLS1141S03Action.isCheckGrid()) {
                CLS1141S03Action.printviewBt();
            }
        });
        /**
         * 查詢非本案額度明細表
         */
        $("#queryDiffCntrNoDoc").click(function(){
            CLS1141S03Action.queryDiffCntrNoDoc();
        });
        
        /**
         * 查詢非本案額度明細表內容查詢
         */
        $("#queryDiffCntrNoDocBt").click(function(){
            CLS1141S03Action.queryDiffCntrNoDocBt();
        });
        
        /**
         * 重新引進戶名
         */
        $("#getCustNewName").click(function(){
            $.ajax({
                handler: CLS1141S03Action.fhandle,
                formId: $("#mainId").val(),
                action: "getNewCustName",
                data: {},
                success: function(obj){
                    CLS1141S03Action.reloadGrid();
                }
            });
        });
        
        
        /**
         *產生查核事項
         */
        $("#creatL140M04A").click(function(){
            CLS1141S03Action.creatL140M04A();
        });
        
        /**
         * 查詢 查核事項頁籤
         */
        $("#cls1141s03").click(function(){
            $.ajax({
                handler: CLS1141S03Action.fhandle,
                formId: $("#mainId").val(),
                action: "queryL140M04A",
                data: {},
                success: function(obj){
                    CLS1141S03Action.creatL140M04ATable(obj.htmlCode);
                    $("#L140M04A_createTime").html(DOMPurify.sanitize(obj.L140M04A_createTime));
                }
            });
        });
        
        
        /**
         * 產品描述視窗
         */
        $("#prodkindDrc").click(function(){
            var $this = $(this);
            if ($this.attr("open") != "Y") {
                $.ajax({
                    handler: CLS1141S03Action.fhandle,
                    formId: "empty",
                    async: false,
                    action: "getC900M01A",
                    success: function(obj){
                        var temp = '';
                        for (var key in obj) {
                            temp += key + ' ' + obj[key];
                            temp += '<br/>';
                        }
                        $this.attr("open", "Y");
                        $("#prodkindDrcShow").html(DOMPurify.sanitize(temp));
                    }
                });
            }
            
            $("#prodkindDrcBox").thickbox({
                title: i18n.cls1141m01['CLS1141.053'],//產品種類代碼說明
                width: 400,
                height: 400,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: _openerLockDoc == "1",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                    
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
        
        /**
         * 產生額度批覆表
         */
        $("#newCheckPageBt").click(function(){
            $.ajax({
                handler: CLS1141S03Action.fhandle,
                action: "produceCase",
                data: {},
                success: function(obj){
                    CLS1141S03Action.reloadGrid();
                }
            });
        });
        /**
         * 批覆bt
         */
        $("#checkBt").click(function(){
            CLS1141S03Action.toDoCheck();
        });
        
        //J-111-0343_05097_B1001 Web e-Loan修改企金額度明細表合計之功能
        /** 查詢LGD明細  */
        $("#printLgdDetail").click(function(){
        	CLS1141S03Action.print_Lgd();
        });
        
        /**
         * 刪除全部
         */
        $("#removeAll").click(function(){
            if (!CLS1141S03Action.isCheckGrid()) {
                return false;
            };
            var $gridviewC_2 = CLS1141S03Action.grid;
            var ids = $gridviewC_2.getGridParam('selarrrow');
            var oids = [];
            if (ids == "") {
                //action_005=請先選取一筆以上之資料列
                return API.showErrorMessage(i18n.def['action_005']);
            }
            for (var i in ids) {
                oids.push($gridviewC_2.getRowData(ids[i]).oid);
            }
            API.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    $.ajax({
                        handler: CLS1141S03Action.fhandle,
                        action: "removeAll",
                        data: {
                            noOpenDoc: true,
                            mainId: $("#mainId").val(),
                            caseType: CLS1141S03Action.itemType,
                            oids: oids
                        },
                        success: function(obj){
                            CLS1141S03Action.reloadGrid();
                        }
                    });
                }
            });
        });
		
		
		$("input[name=cancelCntrNoDoc]").change(function(){
			var val = $(this).val();
			if( val=="Y"){
				$("#showCancelCntrNoGrid").show();
			}else if( val=="N"){
				$("#showCancelCntrNoGrid").hide();
			}else{
			}
        });
		
		
		$("#addOrMinus, #rate").change(function(){
			CLS1141S03Action.computeRate();
		});
		
		/**
         * 卡友信貸-新增案件簽報書
         */
        $("#createCaseSigningBookForCreditCardMembersLoan").click(function(){
			CLS1141S03Action.openCreditCardMemberLoanBox();
        });

        //勞工紓困額度
        $("#createCntrNo_fast_prodKind69").click(function(){
			CLS1141S03Action.createCntrNo_fast_prodKind69();
        });
        
        $("#createCntrNo_fast_prodKind71").click(function(){
			CLS1141S03Action.createCntrNo_fast_prodKind71();
        });
    
        $("input[type='radio'][name='createCntrNo_fast_prodKind71_esggnLoanFg']").change(function(){
        	if($(this).val()=="Y"){
        		$("#tr_EsgGtype_fast_prodKind71").show();		
        	}else if($(this).val()=="N"){
        		$("#createCntrNo_fast_prodKind71Form").injectData({'createCntrNo_fast_prodKind71_esggtype':'', 'createCntrNo_fast_prodKind71_esggtypeZMemo':''});
        		$("#tr_EsgGtype_fast_prodKind71").hide();
        	}
        });
        
        $("#EsgGtypeClsDescPdf_fast_prodKind71").click(function(){
        	$.form.submit({
                url: webroot + "/app/simple/FileProcessingService",
                target: "_blank",
                data: {
                    fileId: "EsgGtypeClsDesc.pdf",
                    fileDownloadName: "EsgGtypeClsDesc.pdf",
                    serviceName: "lmsfiledownloadservice"
                }
            });
        });
        
        $("#createCntrNo_fast_prodKind71_esggtype").change(function(v, k){
            var value = $(this).val();
			
            if (value=="Z") {
            	$("#div_createCntrNo_fast_prodKind71_esggtypeZ").show();
			}else{
				$("#div_createCntrNo_fast_prodKind71_esggtypeZ").find(':input').not(':button, :submit, :reset, :hidden').not(':checkbox, :radio').val('');
				$("#div_createCntrNo_fast_prodKind71_esggtypeZ").hide();
			}
		});
        
        $("#importRelatedEconomic").click(function(){
        	//參考 企金 lms1201formhandler extends { LMSM02FormHandler.java } :: applyRelatedCompany(...)
        	CLS1141S03Action.prompt_befImport_L120S18A().done(function(json){
    			$.ajax({
        			handler : 'cls1141formhandler', type : "POST", dataType : "json", action : "importRelatedEconomic",
        			data : {
        				mainId : responseJSON.mainId
        			},
        			success : function(obj) {
        				$("#gridviewRelatedEconomic").trigger("reloadGrid");
        			}
        		});	
    		});
        });
    	//額度明細表 >> 新增歡喜信貸額度 >> 借款人
    	$("#createCntrNo_fast_prodKind71Form").find("#debtor_createCntrNo_fast_prodKind71").change(function(){
    		CLS1141S03Action.getlast_brmp_termGroupRule($(this).val(), "", "", 
    				$("select#createCntrNo_fast_prodKind71_termGroup"), $("#createCntrNo_fast_prodKind71_termGroupSub"),
    				$("#span_createCntrNo_fast_prodKind71_termGroupRuleResultText"));
    	});
		
		$("#updateOldCaseLtvValueButton").click(function(){
			CommonAPI.triggerOpener($("#notChangedCaseLtvGrid"), "reloadGrid");
			$("#notChangedCaseLtvBox").thickbox({
	            title: i18n.cls1141m01["L140M01A.title.notChangedCaseLTV"], //不變案件核貸成數
	            width: 350,
	            height: 370,
	            align: "center",
	            valign: "bottom",
	            i18n: i18n.def,
	            buttons: {
	                "close": function(){
	                    $.thickbox.close();
	                }
	            }
	        });
	    });

		CLS1141S03Action.loadNotChangedLtvGridInfo();

        //房貸成數檢核明細頁籤顯示判斷
        $("#cls1141s05Li").hide();
        if($('#housePlan_A').val() || $('#housePlan_B').val()|| $('#housePlan_C').val()|| $('#housePlan_D').val()|| $('#housePlan_E').val()|| $('#housePlan_F').val()){
            $("#cls1141s05Li").show();

            $(".housePlan_A").hide();
            $(".housePlan_B").hide();
            $(".housePlan_C").hide();
            $(".housePlan_D").hide();
            $(".housePlan_E").hide();
            $(".housePlan_F").hide();
            if($('#housePlan_A').val()){
                $(".housePlan_A").show();
            };
            if($('#housePlan_B').val()){
                $(".housePlan_B").show();
            };
            if($('#housePlan_C').val()){
                $(".housePlan_C").show();
            };
            if($('#housePlan_D').val()){
                $(".housePlan_D").show();
            };
            if($('#housePlan_E').val()){
                $(".housePlan_E").show();
            };
            if($('#housePlan_F').val()){
                $(".housePlan_F").show();
            };

            //判斷房貸成數檢核明細頁籤>說明文字是否顯示
            $("#housePlanDesc > tr > td > #tipDesc > p > span").each(function( index ) {
                //console.log( index + ": " + $( this ).text() );
                $( this ).parent().parent().hide();
                if($( this ).text().trim()!=''){
                    $( this ).parent().parent().show();
                }
            });
        };
    },
	
	computeRate: function(){
		var addOrMinus = $("#addOrMinus").val();//加減種類
		var rate = $("#rate").val();//加減利率
		rate = rate 
				? parseFloat(rate, 10) 
				: 0;
		
		var baseRate = $("#baseRate").val();
		baseRate = baseRate 
				? parseFloat(baseRate, 10) 
				: 0;
				
        var sumRate = addOrMinus == "P" 
				? baseRate + rate 
				: baseRate - rate;
		sumRate = isNaN(sumRate) 
				? "" 
				: (Math.round(parseFloat(sumRate, 10).toFixed(4) * 10000) / 10000) + '%';
		$("#sumRate").val(sumRate);
	},
	
    checkGrid: null,
    /**  擬核准用gird */
    beforeCheckGridview: function(){
        if (this.checkGrid) {
            return true;
        }
        this.checkGrid = $("#beforeCheckGridview").iGrid({
            handler: CLS1141S03Action.ghandle,
            height: 200,
            rownumbers: true,
            sortname: 'printSeq|custId|cntrNo',
            sortorder: 'asc|asc|asc',
            multiselect: true,
            postData: {
                formAction: "queryL140m01a",
                itemType: CLS1141S03Action.itemType
            },
            colModel: [{
                colHeader: i18n.cls1141m01["CLS.custName"],//借款人名稱
                width: 140,
                name: 'custName',
                sortable: true
            }, {
                colHeader: i18n.cls1141m01["CLS.cntrno"],//"額度序號",
                name: 'cntrNo',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls1141m01["CLS.cntrNoCom"],//"共用額度序號",
                name: 'commSno',
                width: 80,
                sortable: true
            }, {
                colHeader: "&nbsp;",
                name: 'currentApplyCurr',
                width: 25,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.moneyAmt"],//現請額度,
                name: 'currentApplyAmt',
                width: 100,
                sortable: true,
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0//小數點到第幾位
                }
            }, {
                colHeader: i18n.cls1141m01["CLS.proPerty"],//"性質"
                name: 'proPerty',
                width: 70,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.docStatus"], //"文件狀態",
                name: 'docStatus',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.branchId"],//"分行別",
                name: 'ownBrId',
                width: 80,
                sortable: true,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'printSeq',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }]
        });
    },
    /** 批覆和擬批覆  */
    toDoCheck: function(){
        if (!CLS1141S03Action.isCheckGrid()) {
            return false;
        }
        CLS1141S03Action.beforeCheckGridview();
        $("#beforeCheckGridview").trigger("reloadGrid");
        $("[name=checkBtRadio]").removeAttr("disabled");
        $("[name=checkBtRadio][value=1]").attr("checked", true);
        $("#checkBtBox").thickbox({
            //CLS1141.073=整批批覆作業
            title: i18n.cls1141m01['CLS1141.073'],
            width: 830,
            height: 400,
            align: "center",
            valign: "bottom",
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var checked = $("[name=checkBtRadio]:checked").val();
                    var ids = $("#beforeCheckGridview").getGridParam('selarrrow');
                    
                    var oids = [];
                    for (var i in ids) {
                        oids.push($("#beforeCheckGridview").getRowData(ids[i]).oid);
                    }
                    if (ids == "") {
                        //action_005=請先選取一筆以上之資料列
                        return API.showErrorMessage(i18n.def['action_005']);
                    }
                    switch (checked) {
                        case "1":
                        case "3":
                            $.thickbox.close();
                            $.ajax({
                                handler: CLS1141S03Action.fhandle,
                                action: "checkAction",
                                data: {
                                    oids: oids,
                                    doAction: checked,
                                    itemType: CLS1141S03Action.itemType
                                },
                                success: function(obj){
                                    CLS1141S03Action.reloadGrid();
                                }
                            });
                            break;
                        case "2":
                            $.thickbox.close();
                            //打開婉卻輸入欄 
                            CLS1141S03Action.rejectCauseBox(oids, checked);
                            break;
                        default:
                            //l120m01a.error1=請選擇
                            return API.showErrorMessage(i18n.def['grid.selrow']);
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    print_Lgd: function(){
        //J-111-0343_05097_B1004 Web e-Loan修改企金額度明細表合計之功能
        var pdfName = "CLS1201R44.pdf";
        var rType = "R44"
        var content = ""; //R12^10C4E73B266C11E3B4D7126EC0A83B82^73251209^0^006109290001^
        content = rType + "^" + "" + "^" + "" + "^" + "" + "^" + "" + "^" + "" + "^";
        pdfName = pdfName;
        
        //$.thickbox.close();
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                caseType : CLS1141S03Action.itemType, //文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
                serviceName: "cls1141r01rptservice"
            }
        });
        
        
    },
    changeSuggestMode: function(suggestMode){
        // J-111-0461 建議審核層級
    	// 2023.09.28 在計算額度合計時，可以選擇by借款人還是合併借款人
    	// 純粹塞值給後續ajax to 後端時使用
    	responseJSON.suggestMode = suggestMode;
    },
    /** 登錄婉卻原因
     *
     * @param {Array} oids 所選擇的額度明細表oid
     * @param {String } checked 執行動作
     */
    rejectCauseBox: function(oids, checked){
        //初始化
        $("#cesRjtReasonEnt").val("");
        $("#rejectCauseBox").thickbox({
            //CLS1141.074=登錄婉卻原因
            title: i18n.cls1141m01['CLS1141.074'],
            width: 600,
            height: 300,
            align: "center",
            valign: "bottom",
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#rejectCauseForm").valid()) {
                        return false;
                    }
                    $.thickbox.close();
                    $.ajax({
                        handler: CLS1141S03Action.fhandle,
                        action: "checkAction",
                        data: {
                            oids: oids,
                            doAction: checked,
                            itemType: CLS1141S03Action.itemType,
                            Cause: $("#cesRjtCauseEnt").val(),
                            Reason: $("#cesRjtReasonEnt").val()
                        },
                        success: function(obj){
                            CLS1141S03Action.reloadGrid();
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    printGrid: null,
    initGrid: function(){
        this.grid = $("#gridveiwCntrNoDoc").iGrid({
            handler: CLS1141S03Action.ghandle,
            height: 200,
            rownumbers: true,
            sortname: 'printSeq|custId|cntrNo',
            sortorder: 'asc|asc|asc',
            multiselect: true,
            rowNum: 10,
            postData: {
                formAction: "queryL140m01a",
                itemType: CLS1141S03Action.itemType
            },
            colModel: [{
                colHeader: i18n.cls1141m01["CLS.custName"],//借款人名稱
                width: 140,
                name: 'custName',
                sortable: true,
                formatter: 'click',
                onclick: CLS1141S03Action.openCntrNoDoc
            }, {
                colHeader: i18n.cls1141m01["CLS.cntrno"],//"額度序號",
                name: 'cntrNo',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls1141m01["CLS.cntrNoCom"],//"共用額度序號",
                name: 'commSno',
                width: 80,
                sortable: true
            }, {
                colHeader: "&nbsp;",
                name: 'currentApplyCurr',
                width: 25,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.moneyAmt"],//現請額度,
                name: 'currentApplyAmt',
                width: 100,
                sortable: true,
                align: "right",
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0//小數點到第幾位
                }
            }, {
                colHeader: i18n.cls1141m01["CLS.proPerty"],//"性質"
                name: 'proPerty',
                width: 70,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.docStatus"], //"文件狀態",
                name: 'docStatus',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.branchId"],//"分行別",
                name: 'ownBrId',
                width: 80,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls1141m01["CLS.dataDrc"],//來源
                name: 'dataSrc',
                width: 50,
                sortable: true,
                align: "center"
            }, {
                colHeader: "&nbsp",//"檢核欄位",
                name: 'chkYN',
                width: 15,
                sortable: true,
                align: "center"
            },{
				colHeader: i18n.cls1141m01["CLS.parentCntrNo"],//CLS.parentCntrNo=團貸
				name: 'l120m01g.parentCntrNo',
                width: 25,
				sortable: false,
				align: "center",
				formatter: function(data){
					if (data == "" || data == null) {
						return "";
					}
					else {
						return "Y";
					}
				}
			}, {
                name: 'oid',
                hidden: true
            }, {
                name: 'printSeq',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = CLS1141S03Action.grid.getRowData(rowid);
                CLS1141S03Action.openCntrNoDoc(null, null, data);
            }
        });
        
        if($("#pteamappGrid").length>0){
        	        
        	$("#selectCust").change(function(){
   
        		$("#pteamappGrid").setGridParam({
                    postData: {
                    	custOid: $("#selectCust").val()
                    },
                    search: true
                }).trigger("reloadGrid");
            });
            
        	$("#pteamappGrid").iGrid({
                handler: CLS1141S03Action.ghandle,                
                height: CLS1141S03Action.height_pteamappGrid+30,                
                shrinkToFit: false,
    	        multiselect: true, 
    	        needPager: false,
                postData: {
                    'formAction': 'genL140M01A_pteamappGrid'
                    ,'custOid': $("#selectCust").val()
                },
                colModel: [{                   
                    colHeader: i18n.cls1141m01["PTEAMAPP.YEAR"], //年度
                    name: 'year',
                    align: "left",
                    width: 30,
                    sortable: false
                }, {
                	 colHeader: i18n.cls1141m01["PTEAMAPP.PROJECTNM"], //團貸戶戶名
                     name: 'projectnm',
                     align: "left",
                     width: 120,
                     hidden: true,
                     sortable: false
                }, {
                    colHeader: i18n.cls1141m01["PTEAMAPP.GRPCNTRNO"], //團貸編號
                    align: "left",
                    width: 100,
                    name: 'grpcntrno',
                    sortable: true
                }, {
                    colHeader: i18n.cls1141m01["CLS1141.038"],//"簽案行",
                    name: 'issuebrno_name',
                    width: 100,
                    sortable: false,
                    align: "left"
                }, {
                    colHeader: i18n.cls1141m01["PTEAMAPP.EFFFROM"], //總額度有效起日
                    align: "center",
                    width: 100, //設定寬度
                    sortable: false, //是否允許排序
                    name: 'efffrom' //col.id
                }, {
                    colHeader: i18n.cls1141m01["PTEAMAPP.EFFEND"], //總額度有效迄日
                    align: "center",
                    width: 100, //設定寬度
                    sortable: false, //是否允許排序
                    name: 'effend' //col.id
                }, {
                    colHeader: i18n.cls1141m01["PTEAMAPP.OVERAMT"], //剩餘額度 
                    align: "right",
                    width: 100, //設定寬度
                    sortable: false, //是否允許排序
                    formatter: GridFormatter.number.addComma,
                    name: 'overamt' //col.id
                }],
                ondblClickRow: function(rowid){
                }
        	});    
        }
		
        if( $("#cls1141s04Li").is(":visible")){
        	if($("#gridviewRelatedEconomic").length>0){
        		ilog.debug("gridviewRelatedEconomic => init grid");	
        		//參考 企金  第1層grid = lms1201gridhandler :: queryL120s11aMainCust(...)
            	$("#gridviewRelatedEconomic").iGrid({
                    handler: 'cls1141gridhandler',                
                    height: 180,                
                    shrinkToFit: false,
        	        needPager: false,
                    postData: {
                        'formAction': 'queryRelatedEconomicMainCust'
                        ,'mainId': responseJSON.mainId
                    },
                    colModel : [{
            			colHeader : i18n.cls1141m01["L120S18A.custId"], //統一編號
            			align : "left",
            			width : 100, //設定寬度
            			sortable : true, //是否允許排序
            			formatter : 'click',
            			onclick : CLS1141S03Action.openL120S18A,
            			name : 'custId' //col.id
            		},{
            			colHeader : i18n.cls1141m01["L120S18A.dupNo"], //重覆序號
            			align : "center",
            			width : 30, //設定寬度
            			sortable : true, //是否允許排序
            			//formatter : 'click',
            			//onclick : function,
            			name : 'dupNo' //col.id
            		},{
            			colHeader : i18n.cls1141m01["L120S18A.custName"], //申貸戶客戶名稱
            			align : "left",
            			width : 180, //設定寬度
            			sortable : true, //是否允許排序
            			//formatter : 'click',
            			//onclick : function,
            			name : 'custName' //col.id
            		},{
            			colHeader : i18n.cls1141m01["L120S18A.factAmt"]+"(TWD)", //授信額度金額
            			width : 180, //設定寬度
            			sortable : true, //是否允許排序
            			align: "right",
            			formatter: 'currency',
                        formatoptions: {
                            thousandsSeparator: ",",
            				removeTrailingZero: true,
                            decimalPlaces: 0//小數點到第幾位
                        },
            			name : 'factAmt' //col.id		            		
            		},{
            			colHeader : i18n.cls1141m01["L120S18A.dataDate"], //資料日期
            			align : "left",
            			width : 90, //設定寬度
            			//sortable : true, //是否允許排序
            			//formatter : 'click',
            			name : 'dataDate' //col.id		
            		},{
            			colHeader : "printSeq", name : 'printSeq' , hidden : true
            		},{
            			colHeader : "oid", name : 'oid' , hidden : true
            		}],
                    ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                    	var data = $("#gridviewRelatedEconomic").getRowData(rowid);
                    	CLS1141S03Action.openL120S18A(null, null, data);                   
                    }
            	});    
        	}else{
        		ilog.debug("gridviewRelatedEconomic => elm.length==0");	
        	}

        	if($("#gridviewRelatedEconomicDetail").length>0){
        		//參考 企金  第2層grid = 	lms1201gridhandler :: queryL120s11aByCustId(...)
            	$("#gridviewRelatedEconomicDetail").iGrid({
                    handler: 'cls1141gridhandler',                
                    height: 200,                
                    shrinkToFit: false,
        	        needPager: false,
                    postData: {
                        'formAction': 'queryRelatedEconomicDetail'
                        ,'mainId': responseJSON.mainId
                        ,'custId': ''
                        ,'dupNo': ''
                    },
                    colModel : [{
        				colHeader : i18n.cls1141m01["L120S18A.itemSeq"], //序號
        				align : "center",
        				width : 50, //設定寬度
        				sortable : true, //是否允許排序
        				name : 'itemSeq' //col.id
        			},{
        				colHeader : i18n.cls1141m01["L120S18A.custId22"], //
        				align : "left",
        				width : 100, //設定寬度
        				sortable : true, //是否允許排序
        				name : 'custId2' //col.id
        			},{
        				colHeader : i18n.cls1141m01["L120S18A.dupNo22"], //重覆序號
        				align : "center",
        				width : 40, //設定寬度
        				sortable : true, //是否允許排序
        				name : 'dupNo2' //col.id
        			},{
        				colHeader : i18n.cls1141m01["L120S18A.custName22"], //名稱
        				align : "left",
        				width : 180, //設定寬度
        				sortable : true, //是否允許排序
        				name : 'custName2' //col.id
        			},{
        				colHeader : i18n.cls1141m01["L120S18A.appt"], //稱謂
        				align : "left",
        				width : 100, //設定寬度
        				sortable : true, //是否允許排序
        				name : 'appt' //col.id		
        			},{
        				colHeader : i18n.cls1141m01["L120S18A.factAmt"]+"(TWD)", //授信
        				width : 180, //設定寬度
        				sortable : true, //是否允許排序
        				align: "right",
        				formatter: 'currency',
        	            formatoptions: {
        	                thousandsSeparator: ",",
        					removeTrailingZero: true,
        	                decimalPlaces: 0//小數點到第幾位
        	            },
        				name : 'factAmt' //col.id		
        			},{
        				colHeader : "oid", name : 'oid' , hidden : true
        			}],
                    ondblClickRow: function(rowid){ 
                    }
            	});
        	}
        }else{
        	ilog.debug("cls1141s04Li => not show");
        }
        
		if($("#cancelCntrNoGrid").length>0){
        	        
        	$("#selectCust").change(function(){
   				$("input[name=cancelCntrNoDoc][value=N]").attr("checked", true).trigger('change');
				
        		$("#cancelCntrNoGrid").setGridParam({
                    postData: {
                    	custOid: $("#selectCust").val()
                    },
                    search: true
                }).trigger("reloadGrid");
            });
            
        	$("#cancelCntrNoGrid").iGrid({
                handler: CLS1141S03Action.ghandle,                
                height: CLS1141S03Action.height_pteamappGrid+30,                
                shrinkToFit: false,
    	        multiselect: true, 
    	        needPager: false,
                postData: {
                    'formAction': 'genCancelCntrNoGrid'
                    ,'custOid': $("#selectCust").val()
                },
                colModel: [{ 
                    colHeader: i18n.cls1141m01["cancelcntrno.cntrno"], // 額度序號
                    name: 'LNF020_CONTRACT',
                    align: "left",
                    width: 150,
                    sortable: true
                }, {
                	 colHeader: i18n.cls1141m01["cancelcntrno.swft"], // 幣別
                     name: 'LNF020_SWFT',
                     align: "left",
                     width: 30,
                     sortable: false
                }, {
                    colHeader: i18n.cls1141m01["cancelcntrno.factamt"], // 額度金額
                    align: "right",
                    width: 150,
                    name: 'LNF020_FACT_AMT',
                    sortable: true
                }, {
                    colHeader: i18n.cls1141m01["cancelcntrno.canceldate"],// 銷戶日期
                    name: 'LNF020_CANCEL_DATE',
                    width: 100,
                    align: "center",
                    sortable: true
				}, {
                    colHeader: i18n.cls1141m01["cancelcntrno.datasrc"],// 來源
                    name: 'DATA_SRC',
                    width: 100,
                    sortable: false,
                    align: "center"
                }],
                ondblClickRow: function(rowid){
                }
        	});    
        }

        
        /**  設定列印順序grid  */
        
        this.printGrid = $("#gridviewprint").iGrid({
            handler: CLS1141S03Action.ghandle,
            postData: {
                formAction: "queryL140m01a",
                mainId: $("#mainId").val(),
                itemType: CLS1141S03Action.itemType
            },
            height: 230,
            cellsubmit: 'clientArray',
            autowidth: true,
            sortname: 'printSeq|custId|cntrNo',
            sortorder: 'asc|asc|asc',
            colModel: [{
                colHeader: i18n.cls1141m01["CLS.custName"],//借款人名稱
                name: 'custName',
                align: "left",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1141m01["CLS.cntrno"],//"額度序號",
                name: 'cntrNo',
                align: "left",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1141m01["CLS1141.065"],//"科目",
                name: 'lnSubject',
                align: "left",
                width: 200,
                sortable: true
            }, {
                colHeader: i18n.cls1141m01["CLS.proPerty"],//"性質"
                name: 'proPerty',
                align: "center",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.cls1141m01["CLS1141.066"],//"列印順序",
                name: 'printSeq',
                align: "center",
                editable: true,
                width: 60,
                sortable: true,
                editrules: {
                    number: true
                },
                formatter: printSeqformatter
            }, {
                name: 'oid',
                hidden: true
            }],
            onSelectRow: function(id){
                if (id && id != lastSel) {
                    $("#gridviewprint").saveRow(lastSel, false, 'clientArray');
                    $('#gridviewprint').restoreRow(lastSel);
                    lastSel = id;
                }
                $('#gridviewprint').editRow(id, false);
            }
        });
        
        /**  列印格式化  */
        function printSeqformatter(cellvalue, otions, rowObject){
            var seq = 0;
            if (!cellvalue) {
                cellvalue = ++seq;
            }
            seq = 0;
            return cellvalue;
        }
    },
    /**
     *初始化
     */
    init: function(){
        if (!this.isInit) {
            this.setItemType();
            this.initGrid();
            this.initEvent();
            this.isInit = true;
        }
    },
    start: function(obj){
        this.init();
        if (CLS1141S03Action.itemType == "1") {
            //判斷團貸案隱藏查核事項頁籤
            if (obj.docCode == "5") {
                $("#cls1141s03Li").hide();
                $("#cls1141s04Li").hide();
                
                // 一般案引進已銷戶額度明細表選項
				$("#showCancelCntrNoBt").hide();
            }
			
			//if (cancelCntrNoDoc)
			
            this.setCustSelect();
            var $beforeCheckSpan = $("#beforeCheckSpan");
            if (obj.docKind == "1") {
                switch (obj.authLvl + "") {
                    case "1":
                    case "2":
                        //授權內要顯示擬核准按鈕
                        $beforeCheckSpan.show();
                        break;
                    default:
                        //授權內要顯示擬核准按鈕
                        $beforeCheckSpan.hide();
                        break;
                }
            } else {
                // 授權外 特殊分行(國外部、財富管理處、國金部、財務部、金控總部分行)
                // 無條件顯示擬核准按鈕
                var unitNo = userInfo.unitNo;
                if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
                unitNo == "011" ||
                unitNo == "201" ||
                unitNo == "149") {
                    //J-108-0316_10702_B1001 Web e-Loan調整國外部、國際金融業務分行與金控總部分行等原總處營業單位會簽流程
                	//$beforeCheckSpan.show();
                	$beforeCheckSpan.hide();
                } else {
                    $beforeCheckSpan.hide();
                }
            }
        }
    },
    /**
     * page 要載入的頁面
     * 2.資本適足率引響數頁面
     * 3.查核事項頁面
     */
    loadPage: function(page){
        var $tabs = $("#tabs0" + page);
        if ($tabs.attr("isOpen") == "N") {
            $tabs.attr("isOpen", "Y");
        }
    },
    /**
     * 是否已經載入額度明細表
     */
    isLoadCntrNoDoc: false,
    /**
     * 開啟非本案額度明細表
     * @param {Object} column
     * @param {Object} oid
     * @param {Object} data
     */
    openCntrNoLockDoc: function(column, oid, data){
        data.justQurry = true;
        CLS1141S03Action.openCntrNoDoc(column, oid, data);
    },
    /**
     * 開啟額度明細表
     *
     */
    openCntrNoDoc: function(column, oid, data){
		data.custId = $("#mainCustId").val();
		data.dupNo = $("#mainDupNo").val();
        data.itemType = CLS1141S03Action.itemType;
        data.CaseMainId = $("#mainId").val();
        //把文件已開啟的狀態傳進來
        data._openerLockDoc = responseJSON._openerLockDoc;
        data.CLSAction_isReadOnly = CLSAction.isReadOnly() ? "Y" : "N";
        $.form.submit({
            url: "../cls1151s01",
            data: data,
            target: data.oid ? data.oid : "new"
        });
        
        /*
         if (!CLS1141S03Action.isLoadCntrNoDoc) {
         $('#loadPage1').load(webroot + '/app/cls/cls1151s01', function(){
         CLS1141S03Action.isLoadCntrNoDoc = true;
         //CLS1151S01Page.js
         CLS115Action.openBox(data, {
         itemType: CLS1141S03Action.itemType
         });
         });
         } else {
         CLS115Action.openBox(data, {
         itemType: CLS1141S03Action.itemType
         });
         }
         */
    },
    
    /**
     * 重新整理Grid
     *
     */
    reloadGrid: function(){
        this.grid.trigger('reloadGrid');
    
        if($("#gridviewRelatedEconomic").length>0){
        	$("#gridviewRelatedEconomic").trigger("reloadGrid");        	
        }
    },
    
    /**
     * 設定借款人
     */
    setCustSelect: function(){
        $.ajax({
            handler: CLSAction.fhandle,
            async: false,
            action: "queryC120M01A",
            data: {},
            success: function(obj){
                if ($.isEmptyObject(obj.item)) {
                    //CLS.error01=請先登錄借款人資料
                    return API.showMessage(i18n.cls1141m01['CLS.error01']);
                } else {
                    $("#selectCust,#copyCntrNoDoc_CustId,#debtor,#debtor_createCntrNo_fast_prodKind69,#debtor_createCntrNo_fast_prodKind71").setItems({
                        space: true,
                        item: obj.item,
                        format: "{key}"
                    });
                }
                
            }
        });
    },
    
    
    /**
     *開起新增額度明細表視窗
     */
    openCreateBox: function(){
		$("#showCancelCntrNoGrid").hide();
		
        var $form = $("#ctreatDocForm");		
        $form.reset();        
        //---
        var box_height = 350;
        if($("#pteamappGrid").length>0){
        	box_height += CLS1141S03Action.height_pteamappGrid;
        	
        	//上面的 $form.reset() 已把 $("#selectCust") 給清空
        	//所以也清空 grid 內容
        	$form.find("#selectCust").trigger('change');
        } 
		
		if($("#cancelCntrNoGrid").length>0){
        	$form.find("#selectCust").trigger('change');
        }
		
        $("#ctreatDocBox").thickbox({
            title: i18n.cls1141m01["CLS1141.011"],
            width: 650,
            height: box_height,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                    	var param = {};
						var cancel = {};
						// 勾選團貸的額度序號
                    	if($("#pteamappGrid").length>0){                    		
                        	var grpCntrNoArr = [];                        	
                        	var rowId_arr = $("#pteamappGrid").getGridParam('selarrrow');                          	
                          	for (var i = 0; i < rowId_arr.length; i++) {
                          		var data = $("#pteamappGrid").getRowData(rowId_arr[i]);
                          		grpCntrNoArr.push(data.grpcntrno);
                            }                          	
                          	if(grpCntrNoArr.length==0 && ('N'==$('input[name=newCntrNoDoc]:checked').val() )){
                          		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
                          		return;
                          	}                          	
                          	param['grpCntrNoArr'] = grpCntrNoArr.join("|");
                        }
						// 勾選取消的額度序號
						if( $("#cancelCntrNoGrid:visible").length>0){
							var cancelCntrNoArr = [];
							var ids = $("#cancelCntrNoGrid").getGridParam('selarrrow');                            
                            if (ids.length == 0) {
                                API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
                          		return;
                            }
                            for (var i in ids) {
				                var row = $("#cancelCntrNoGrid").getRowData(ids[i]);
                                    cancelCntrNoArr.push( row.LNF020_CONTRACT + "^" +row.DATA_SRC);
                            }
							cancel['cancelCntrNoArr'] = cancelCntrNoArr.join("|");
						}
						ilog.debug("@ajax > "+CLS1141S03Action.fhandle+"::addL140m01a");
						$.ajax({
                            handler: CLS1141S03Action.fhandle,
                            formId: "ctreatDocForm",//CLSForm
                            action: "addL140m01a",
                            data: $.extend({custOid: $("#selectCust").val()}, param, cancel),
                            success: function(obj){
                                CLS1141S03Action.reloadGrid();
                                $.thickbox.close();
                            }
                        });
						
						ilog.debug("@ajax > newCntrNoDoc61="+$form.find("[name=newCntrNoDoc61]:checked").val());
						if($form.find("[name=newCntrNoDoc61]:checked").val()=="Y"){
							CLS1141S03Action.createCntrNo_fast_prodKind61();
						}					
                    }
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
	
	/**
     * 開啟新增卡友信貸視窗
     */
    openCreditCardMemberLoanBox: function(){
        var $form = $("#creditCardMemberLoanForm");		
        $form.reset();        
		//get 指標利率
		$.ajax({
            handler: CLS1141S03Action.fhandle,
            formId: "creditCardMemberLoanForm",
            action: "getBaseRate",
            data: {},
            success: function(obj){
                $("#baseRate").val(obj.rate['6R']);
				CLS1141S03Action.computeRate();
            }
        });
		
		//卡友信貸-新增案件簽報書 利率-下拉選單(P-加, M-減)
		var objs = API.loadCombos(["L140S02D_pmFlag"]);
        $("#addOrMinus").setItems({
            space: false,
            item: objs.L140S02D_pmFlag,
            format: "{value} - {key}"
        });

		var box_height = 340;
        $("#creditCardMemberLoanDiv").thickbox({
            title: i18n.cls1141m01["CLS1141.011"],
            width: 650,
            height: box_height,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
			async: false,
            buttons: {
                "sure": function(){
                    if (!$form.valid()) {
                        return;
					}

                    var l140m01a_MainId = CLS1141S03Action.createCreditCardMembersLoanData();
					var cntrNo = CLS1141S03Action.getContractNo(l140m01a_MainId);
					var nplInfo = CLS1141S03Action.getBranchNplRatiosInfo(cntrNo);
					CLS1141S03Action.updateCreditCardMembersLoanData(l140m01a_MainId, cntrNo, nplInfo.npl, nplInfo.nplDate);
					CLS1141S03Action.reloadGrid();
                  	$.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    createCntrNo_fast_prodKind61: function(){
    	ilog.debug("exec createCntrNo_fast_prodKind61, c120_oid="+($("#ctreatDocForm #selectCust").val()||''));
    	var $form = $("#createCntrNo_fast_prodKind61Form");
    	$form.reset();        
        $form.injectData({'debtor_createCntrNo_fast_prodKind61': $("#ctreatDocForm #selectCust").val()||''
        });
    	
        CLS1141S03Action.createCntrNo_fast_prodKind_byParam('61', 'createCntrNo_fast_prodKind61Form').done(function(json_cntrNo_fast){
        	$.ajax({handler: "cls1141m01formhandler", action: "upd_caseDoc___tabDoc_cntrNo_npl", formId: 'empty', data:{'tabMainId': json_cntrNo_fast.l140m01a_MainId},success: function(json){}});                    	
        		//var cntrNo = CLS1141S03Action.getContractNo(json_cntrNo_fast.l140m01a_MainId);
				//var nplInfo = CLS1141S03Action.getBranchNplRatiosInfo(cntrNo);
				//CLS1141S03Action.updateCreditCardMembersLoanData(json_cntrNo_fast.l140m01a_MainId, cntrNo, nplInfo.npl, nplInfo.nplDate);
				CLS1141S03Action.reloadGrid();
           	$.thickbox.close();	
        });			
    },
    /**
     * 開啟新增勞工紓困視窗
     */
    createCntrNo_fast_prodKind69: function(){
        var $form = $("#createCntrNo_fast_prodKind69Form");		
        $form.reset();        
        $.ajax({handler: "cls1141m01formhandler", action: "prepare_gen_69_tabDoc", formId: 'empty', data:{'mainId': responseJSON.mainId}
        	,success: function(json){
        		if(json.prepareData){
        			$form.injectData( json.prepareData );			
        		}else{
        			$form.injectData({'loanAmount_createCntrNo_fast_prodKind69':10
                		, 'year_createCntrNo_fast_prodKind69':3
                		, 'month_createCntrNo_fast_prodKind69':0
                		, 'sendSMS_createCntrNo_fast_prodKind69': 'Y'
        			});
        		}
        		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        		var box_height = 340;
                $("#createCntrNo_fast_prodKind69Div").thickbox({
                    title: i18n.cls1141m01["CLS1141.011"],
                    width: 700,
                    height: box_height,
                    modal: true,
                    align: "center",
                    valign: "bottom",
                    readOnly: _openerLockDoc == "1",
                    i18n: i18n.def,
        			async: false,
                    buttons: {
                        "sure": function(){
                            if (!$form.valid()) {
                                return;
        					}

                            CLS1141S03Action.createCntrNo_fast_prodKind_byParam('69', 'createCntrNo_fast_prodKind69Form').done(function(json_cntrNo_fast){
                            	dfd_upd_caseDoc___tabDoc_cntrNo_npl(json_cntrNo_fast.l140m01a_MainId).done(function(){
                            		
                            		dfd_import_property7_cntrNo(responseJSON.mainId
                            				, $("#createCntrNo_fast_prodKind69Form").find("#debtor_createCntrNo_fast_prodKind69").val() 
                            				, "N" ).done(function(){
                            			CLS1141S03Action.reloadGrid();
                            			$.thickbox.close();	
             						});                    	
         						});                    	
                            });					
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
        }});
        
        
    },
    createCntrNo_fast_prodKind71: function(){
    	var $form = $("#createCntrNo_fast_prodKind71Form");		
    	$form.find("#tr_EsgGtype_fast_prodKind71").hide();
    	$form.reset();        
        $form.injectData({
			'createCntrNo_fast_prodKind71_esggnLoanFg': 'N'  //在 reset 後，綠色支出類型 會被清空
		});

		$("#createCntrNo_fast_prodKind71_termGroup option[value='GA']").remove();
		$("#createCntrNo_fast_prodKind71_termGroup option[value='GB']").remove();
        
		var box_height = 430;
        $("#createCntrNo_fast_prodKind71Div").thickbox({
            title: i18n.cls1141m01["CLS1141.011"],
            width: 700,
            height: box_height,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
			async: false,
            buttons: {
                "sure": function(){
                    if (!$form.valid()) {
                        return;
					}

                    CLS1141S03Action.createCntrNo_fast_prodKind_byParam('71', 'createCntrNo_fast_prodKind71Form').done(function(json_cntrNo_fast){
                    	ilog.debug("createCntrNo_fast_prodKind71Form {tabMainId="+json_cntrNo_fast.l140m01a_MainId+" }{clsTerm:"+(json_cntrNo_fast.clsTerm?json_cntrNo_fast.clsTerm.infoStr:'')+"}");
                    	$.ajax({handler: "cls1141m01formhandler", action: "upd_caseDoc___tabDoc_cntrNo_npl", formId: 'empty', data:{'tabMainId': json_cntrNo_fast.l140m01a_MainId},success: function(json){}});                    	
                    	//var cntrNo = CLS1141S03Action.getContractNo(json_cntrNo_fast.l140m01a_MainId);
                    	//var nplInfo = CLS1141S03Action.getBranchNplRatiosInfo(cntrNo);
                    	//CLS1141S03Action.updateCreditCardMembersLoanData(json_cntrNo_fast.l140m01a_MainId, cntrNo, nplInfo.npl, nplInfo.nplDate);
    					CLS1141S03Action.reloadGrid();
                      	$.thickbox.close();	
                    });					
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    
	updateCreditCardMembersLoanData : function(l140m01a_MainId, cntrNo, npl, nplDate){
		
		$.ajax({
            handler: CLS1141S03Action.fhandle,
			formId: "creditCardMemberLoanForm",
            action: "updateCreditCardMembersLoanData",
            data: $.extend({
				mainId: l140m01a_MainId,
				cntrNo: cntrNo,
				npl: npl,
				nplDate: nplDate
			}, {}, {}),
            success: function(obj){
            }
        });
	},
	
	//分行逾放比資訊npl, nplDate;
	getBranchNplRatiosInfo : function(cntrNo){
		var nplInfo;
		$.ajax({
	        handler: CLS1141S03Action.fhandle,
	        async: false,
	        action: "queryNPL",
	        data: {cntrNo: cntrNo},
	        success: function(obj){
				nplInfo = {npl: obj.msg, nplDate: obj.date}
	        }
	    });
		
		return nplInfo;
	},
	
	getContractNo : function(mainId){
		
		var cntrNo;
		$.ajax({
    		handler: CLS1141S03Action.fhandle,
    		async: false,
    		action: "queryNewCntrNo",
    		data: $.extend({'doSave': 'true', 'tabFormMainId':mainId, 'ownBrId':userInfo.unitNo}, {}),
   			success: function(obj){
        		cntrNo = obj.cntrNo;
				if(cntrNo == null || cntrNo == ""){
					//無法產生 額度序號！
					API.showMessage(i18n.cls1141m01['CLS1141.128.errorMessage']);
					return;
				}
    		}
		});
		
		return cntrNo;
	},
	
	createCreditCardMembersLoanData : function(){
		
		var mainId, param = {}, cancel = {};
		$.ajax({
            handler: CLS1141S03Action.fhandle,
			async: false,
			formId: "creditCardMemberLoanForm",
            action: "createCreditCardMembersLoanData",
            data: $.extend({
				debtorOid: $("#debtor").val(),
				isSubj303: $("input[name='isSubj303']:checked").val(),
				loanAmount: $("#loanAmount").val(),
				year: $("#year").val(),
				month: $("#month").val(),
				rate: $("#rate").val(),
				addOrMinus: $("#addOrMinus").val(),
				baseRate: $("#baseRate").val(),
				rateType: $("#rateType").html()
			}, param, cancel),
            success: function(obj){
				mainId = obj.l140m01a_MainId;
            }
        });
		
		return mainId;
	},
	
	createCntrNo_fast_prodKind_byParam : function(param_prodKind, param_formId){		
		return $.ajax({
            handler: CLS1141S03Action.fhandle,
			async: false,
			formId: 'empty',
            action: "createCntrNo_fast_prodKind_byParam",
            data: $.extend( {'prodKind':param_prodKind}, $("#"+param_formId).serializeData()),
            success: function(obj){
				
            }
        });
	},
	
    /**
     * 複製額度明細表
     */
    copyCntrNoDoc: function(){
        var $form = $("#copyCntrNoDocForm");
        $form.reset();
        $("#copyCntrNoDocBox").thickbox({
            //CLS1141.012=複製額度明細表
            title: i18n.cls1141m01['CLS1141.012'],
            width: 600,
            height: 300,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                        $.thickbox.close();
                        CLS1141S03Action.showCopyCntrNoDocGridBox($form.serializeData());
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        
    },
    copyCntrNoDocGrid: null,
    
    //呈現要複製的額度明細表
    showCopyCntrNoDocGridBox: function(setData){
        if (!CLS1141S03Action.copyCntrNoDocGrid) {
            CLS1141S03Action.copyCntrNoDocGrid = $("#showCopyCntrNoDocGrid").iGrid({
                handler: CLS1141S03Action.ghandle,
                height: 230,
                rownumbers: true,
                multiselect: true,
                hideMultiselect: false,
                rowNum: 10,
                postData: $.extend({
                    formAction: "queryCopyFitlePeole"
                }, setData || {}),
                colModel: [{
                    colHeader: i18n.cls1141m01["CLS.custName"],//借款人名稱
                    name: 'custName',
                    align: "left",
                    width: 100,
                    sortable: true
                }, {
                    colHeader: i18n.cls1141m01["CLS.cntrno"],//"額度序號",
                    name: 'cntrNo',
                    align: "left",
                    width: 100,
                    sortable: true
                }, {
                    colHeader: i18n.cls1141m01["CLS.proPerty"],//"性質"
                    name: 'proPerty',
                    align: "center",
                    width: 100,
                    sortable: true
                }, {
                    colHeader: i18n.cls1141m01["CLS1141.107"],//CLS1141.107=額/批
                    name: 'l120m01c.itemType',
                    align: "center",
                    width: 50,
                    sortable: true,
                    formatter: CLS1141S03Action.itemTypeFormatter
                }, {
                    colHeader: i18n.cls1141m01["l120m01a.caseno"],//案   號,
                    name: 'caseNo',
                    align: "center",
                    editable: true,
                    width: 100,
                    sortable: true
                }, {
                    name: 'oid',
                    hidden: true
                }],
                ondblClickRow: function(rowid){
                
                }
            });
        } else {
            CLS1141S03Action.copyCntrNoDocGrid.reload(setData)
        }
        
        $("#showCopyCntrNoDocGridBox").thickbox({
            title: i18n.cls1141m01["CLS1141.012"],//複製額度明細表
            width: 800,
            height: 400,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = CLS1141S03Action.copyCntrNoDocGrid;
                    //多筆
                    var rowData = $grid.getSelectData("oid");
                    if (rowData) {
                        $.thickbox.close();
                        
                        ilog.debug("@ajax > "+CLS1141S03Action.fhandle+"::copyL140m01a");
                        $.ajax({
                            handler: CLS1141S03Action.fhandle,
                            action: "copyL140m01a",
                            formId: 'empty',
                            data: $.extend({
                                itemType: CLS1141S03Action.itemType,
                                mainId: $("#mainId").val(),
                                oids: rowData,
                                cust_Oid: setData.copyCntrNoDoc_CustId,
                                noOpenDoc: true
                            }, setData || {}),
                            success: function(obj){
                                CLS1141S03Action.reloadGrid();
                            }
                        });
                    }
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    itemTypeFormatter: function(cellvalue, otions, rowObject){
        //CLS1141.itemType1=額
        //CLS1141.itemType2=批
        var itemName = '';
        if (cellvalue) {
            itemName = i18n.cls1141m01["CLS1141.itemType" + cellvalue];
        }
        return itemName;
    },
    checkSend_CLS_L120M01A: function(){
    	var my_dfd = $.Deferred(); 
        $.ajax({
            handler: CLSAction.fhandle,
            action: "checkSend_CLS_L120M01A",
            formId: 'empty',
            data: {
                mainId: $("#mainId").val()
            },
            success: function(json){
            	// J-111-0461 建議核定層級，可以合併全案計算or不計算
	        	if(json.showSuggestModeItem){
	        		responseJSON.showSuggestModeItem = encodeURI(json.showSuggestModeItem);
	        	}else{
	        		responseJSON.showSuggestModeItem = '';
	        	}
	        	
            	if(json.cfmMsg && json.cfmMsg!=""){
    				API.confirmMessage(json.cfmMsg, function(b){
    	                if (b) {
    	                	my_dfd.resolve();
    	                }
    	            });
    			}else{
    				my_dfd.resolve();
    			}
            },
            complete:function(result){       	
            	//J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
            	//重新載入，更新頁籤
            	if($("#TB_window1").length > 0 || $("#TB_window").length > 0){
            		$("#關閉").click(function(){
            			CLS1141S03Action.refreshPage();
    		        });
            	}
            }
        });
        
        return my_dfd.promise();
    },
    ask_after_countValue: function(cfm_msg){
    	var my_dfd = $.Deferred(); 
        if(cfm_msg && cfm_msg!=""){
        	CommonAPI.confirmMessage(cfm_msg, function(b){
	            if (b) {
	            	my_dfd.resolve({'show_cfm_msg':'Y'});    	
	            }else{
	            	my_dfd.reject();
	            }
	        });
        }else{
        	my_dfd.resolve({'show_cfm_msg':'N'}); 
        }
        return my_dfd.promise();
    },
    do_chkTotalNeedChange: function(){
    	var my_dfd = $.Deferred(); 
    	$.ajax({
            handler: CLS1141S03Action.fhandle,
            action: "chkTotalNeedChange_clsVer",
            formId: 'empty',
            data: {
                itemType: CLS1141S03Action.itemType,
                mainId: $("#mainId").val()
            },
			success: function(objChg){
				ilog.debug("@ajax > "+CLS1141S03Action.fhandle+"::chkTotalNeedChange_clsVer[totalNeedChange="+(objChg.totalNeedChange||"")+"]");
				if (objChg.totalNeedChange == "N") {
					//合計可以不用重新計算
					var orgReadOnlyStatus = thickboxOptions.readOnly;
					thickboxOptions.readOnly = false;
					
					CommonAPI.iConfirmDialog({
						message: i18n.cls1141m01["CLS1141.134"], //"是否需重新計算?(選則否沿用上次計算結果)
						buttons: API.createJSON([{
							key: i18n.def.yes,
							value: function(){
								thickboxOptions.readOnly = orgReadOnlyStatus;
								$.thickbox.close();
								my_dfd.resolve({'val':'ReCount'}); 
							}
						}, {
							key: i18n.def.no,
							value: function(){
								//否--不用重新計算
								//更新額度明細表/批覆書 check Flag為V	
								thickboxOptions.readOnly = orgReadOnlyStatus;
								$.thickbox.close();
								my_dfd.resolve({'val':'UsePrevious'}); 
							}
//						}, {
//							key: i18n.def.cancel,
//							value: function(){
//								thickboxOptions.readOnly = orgReadOnlyStatus;
//								$.thickbox.close();
//							}
						}])
					});
					
					
				
				}else{
					my_dfd.resolve({'val':'ReCount'}); 
				}
			}
    	});
        return my_dfd.promise();
    },
    /**
     * 計算合計
     */
    countValue: function(){
        /* $.ajax({
            handler: CLS1141S03Action.fhandle,
            action: "queryCountCurr",
            formId: 'empty',
            data: {
                itemType: CLS1141S03Action.itemType,
                mainId: $("#mainId").val(),
                noOpenDoc: true
            },
            success: function(obj){ //obj.theCase 表示「多幣別」
                if (!obj.printSeqMsg || obj.printSeqMsg == "") {
                    if (obj.theCase) {
                        //幣別
                        $("#countEditBoxCurrSelect").setItems({
                            item: obj.curr,
                            format: "{value} - {key}"
                        });
                        //請選擇計算幣別
                        CLS1141S03Action.selecCountMoney();
                    } else {
                        //調整視窗
                        CLS1141S03Action.editCount(false, false);
                    }
                } else {
                    API.confirmMessage(obj.printSeqMsg, function(b){
                        if (b) {
                            //當同一借款人的額度明細表有兩種以上幣別彈出詢問視窗
                            if (obj.theCase) {
                                //幣別
                                $("#countEditBoxCurrSelect").setItems({
                                    item: obj.curr,
                                    format: "{value} - {key}"
                                });
                                //請選擇計算幣別
                                CLS1141S03Action.selecCountMoney();
                            } else {
                                //調整視窗
                                CLS1141S03Action.editCount(false, true);
                            }
                        }
                    });
                }
            }
        }); */    	
    	return $.ajax({
            handler: CLS1141S03Action.fhandle,
            action: "queryCountCurr",
            formId: 'empty',
            data: {
                itemType: CLS1141S03Action.itemType,
                mainId: $("#mainId").val(),
                noOpenDoc: true
            }
    	});
    },
    /** 
     * 打開修改視窗
     * @param {String} curr 多幣別計算時計算結果的幣別
     * @param {String} showCurr 是否顯示多幣別描述
     */
    editCount: function(curr, showCurr){
    	
    	// 先預設成mode 1
    	CLS1141S03Action.changeSuggestMode(1);
    	var suggestModeText = '<BR/><BR/>';
    	if(responseJSON.showSuggestModeItem){
    		suggestModeText += '授信業務建議授權層級計算方式:<br/>';
    		var modeItemArr = responseJSON.showSuggestModeItem.split(",");

    		for (var i in modeItemArr) {
            	 	if(modeItemArr[i] == 1){
            	 		suggestModeText += '<label class="suggestModeLabel" id="modeLabe1">' + 
    					'<input name="suggestMode" type="radio" onclick="CLS1141S03Action.changeSuggestMode(1);"/> 1.個別借款人歸戶授權</label><br/>';
					}else if( modeItemArr[i] == 2 ){
						suggestModeText += '<label class="suggestModeLabel" id="modeLabe2">' + 
						'<input name="suggestMode" type="radio" onclick="CLS1141S03Action.changeSuggestMode(2);"/> 2.全案所有借款人授權</label><br/>';
					}else if( modeItemArr[i] == 3 ){
						suggestModeText += '<label class="suggestModeLabel" id="modeLabe3">' +
						'<input name="suggestMode" type="radio" onclick="CLS1141S03Action.changeSuggestMode(3);"/> 3.部分借款人或額度合併授權(不提供建議授權層級)</label>';
					}
             }
    	}
    	
    	ilog.debug("editCount(curr="+curr+", showCurr="+showCurr+")");
    	//CLS1141.046 = 是否需調整計算後之合計值？ \r若需調整請按【確定】，系統會開啟調整畫面；\r若不調整請按【取消】，則以系統算出的金額填入額度
        API.confirmMessage(i18n.cls1141m01['CLS1141.046'] + suggestModeText, function(b){
            if (b) {
                var td = "";
                $.ajax({
                    handler: CLS1141S03Action.fhandle,
                    async: false,
                    action: "queryL140m01aCountToEdit",
                    data: {
                        noOpenDoc: true,
                        mainId: $("#mainId").val(),
                        CaseType: CLS1141S03Action.itemType,
                        curr: curr || "",
                        showCurr: showCurr || false
                    },
                    success: function(obj){

                    	//J-111-0343_05097_B1004 Web e-Loan修改企金額度明細表合計之功能
                    	if(obj.showLgdTotAmt == "Y"){
                    		for (var i = 1; i <= obj.lmsLgdCountTotal; i++) {
                        		if(obj["showLgdTotAmt_"+i] == "Y"){
                            		$(".showLgdTotAmt_"+i).show();
                				}else{
                					$(".showLgdTotAmt_"+i).hide();
                				}
                        	}		
        				}else{
        					for (var i = 1; i <= obj.lmsLgdCountTotal; i++) {
                        		$(".showLgdTotAmt_"+i).hide();
                        	}	
        				}
                    	
                    	//J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
                    	$("#countEditTitle").empty();
                    	var td = "";
                    	var title = obj.item[0].title;
                    	for (var colName in title) {
                    		var lgdPrintBtn = "";
                    		if(colName == "lgdTotAmt_1"){
                    			lgdPrintBtn += "<br><button type='button' id='printLgdDetail' class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button' onclick= 'CLS1141S03Action.print_Lgd();' ><span class='ui-button-text'><span class='text-only'>查詢</span></span></button>";
                    		}
                    		td += "<td class='hd2 ct' ><span>"+DOMPurify.sanitize(title[DOMPurify.sanitize(colName)]) +"</span>"+lgdPrintBtn+"</td>"; 
                        }
                    	//$("#countEditTitle").html("<tr>" + td + "</tr>");
                    	var countEditTitle = $("#countEditTitle");
                    	//countEditTitle.html("<tr>" + td + "</tr>");
                    	var titleStr = "<tr>" + td + "</tr>";
                    	//countEditTitle.injectData({'countEditTitle':titleStr},false);
                    	countEditTitle.html(titleStr);
                    	
                        td = "";
                        
                        $("#countEditBody").empty();
                        for (var i = 0; i < obj.size; i++) {
                            td += "<td ><input value='" + obj.item[i].name + "' disabled/><input name='custId" + i + "' value='" + obj.item[i].id + "' style='display:none' /></td>";
                            td += "<td><select id='curr" + i + "'name='countCurr" + i + "'class='curr reuqired'/></td>";
                            var counts = obj.item[i].count;
                            for (var colName in counts) {
                            	//J-112-0037_05097_B1005 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
                            	if(counts[colName] == "readOnly"){
                            		td += "<td><input id='" + DOMPurify.sanitize(colName) + i + "' name='" + DOMPurify.sanitize(colName) + i + "' value='' size='19'  maxlength='22'  readonly=readonly disabled=disabled /></td>";
                            	}else{
                            		td += "<td><input id='" + DOMPurify.sanitize(colName) + i + "' name='" + DOMPurify.sanitize(colName) + i + "' value='" + DOMPurify.sanitize(counts[colName]) + "'size='19' positiveonly='true' integer='14' fraction='0' class='numeric  required' maxlength='19' /></td>";
                            	}
                            }
                            
                            var drcs = obj.item[i].drc;
                            if (!$.isEmptyObject(drcs)) {
                                for (var colName in drcs) {
                                	if(drcs[colName] == "readOnly"){
                                		td += "<td><textarea id='" + DOMPurify.sanitize(colName) + i + "' name='" + DOMPurify.sanitize(colName) + i + "'rows='5' maxlengthC='100' readonly=readonly disabled=disabled ></textarea></td>";
                                	}else{
                                		td += "<td><textarea id='" + DOMPurify.sanitize(colName) + i + "' name='" + DOMPurify.sanitize(colName) + i + "'rows='5' maxlengthC='100' >" + DOMPurify.sanitize(drcs[colName]) + "</textarea></td>";
                                	}
                                    
                                }
                            } else {
                                //無多幣別敘述開空欄位
                                //td += "<td><textarea id='MultiAmt" + i + "' name='MultiAmt" + i + "' rows='5' maxlengthC='100' readonly=readonly disabled=disabled ></textarea></td>";
                                //td += "<td><textarea id='MultiAssureAmt" + i + "' name='MultiAssureAmt" + i + "' rows='5' maxlengthC='100' readonly=readonly disabled=disabled ></textarea></td>";
                            }
                            $("#countEditBody").append("<tr>" + td + "</tr>");
                            td = "";
                        }
                        //修改視窗
                        var objs = API.loadCombos(["Common_Currcy"]);
                        $("select.curr").setItems({
                            space: false,
                            item: objs.Common_Currcy,
                            value: "TWD",
                            format: "{value} - {key}"
                        });
                        //把相對應的幣別放入
                        for (var i = 0; i < obj.size; i++) {
                            $("[name=countCurr" + i + "]").val(obj.item[i].curr);
                        }
                        CLS1141S03Action.countEditBox(obj.size, curr);
                    }
                });
            } else {
                //直接做計算
                CLS1141S03Action.justOnlyCount(curr);
            }
        });
    },
    /** 
     *開啟額度明細表授信合計調整
     * @param {Integer}size 筆數
     * @param {String } curr 主要計價幣別
     */
    countEditBox: function(size, curr){
        $("#countEditBox").thickbox({
            //CLS1141.047=額度明細表授信合計調整
            title: i18n.cls1141m01['CLS1141.047'],
            width: 1000,
            height: 500,
            modal: true,
            i18n: i18n.def,
            align: "center",
            readOnly: false,
            valign: "bottom",
            buttons: {
                "sure": function(){
                    if ($("#countEditForm").valid()) {
                        $.ajax({
                            handler: CLS1141S03Action.fhandle,
                            action: "saveCountEditForm",
                            formId: "empty",
                            data: {
                                noOpenDoc: true,
                                mainId: $("#mainId").val(),
                                caseType: CLS1141S03Action.itemType,
                                countEditForm: JSON.stringify($("#countEditForm").serializeData()),
                                //調整筆數
                                size: size
                            },
                            success: function(obj){
                                CLS1141S03Action.reloadGrid();
                                $.thickbox.close();
                                CLS1141S03Action.setL120M01ALongCaseDscr(obj);
                            }
                        });
                    }
                },
                "cancel": function(){
                    //直接計算不做修改
                    CLS1141S03Action.justOnlyCount(curr);
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 選擇計算幣別
     
     */
    selecCountMoney: function(){
        $("#countEditBoxCurr").thickbox({
            //CLS1141.048=請選擇計算幣別
            title: i18n.cls1141m01['CLS1141.048'],
            width: 300,
            height: 100,
            modal: true,
            i18n: i18n.def,
            readOnly: false,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var curr = $("#countEditBoxCurrSelect").val();
                    if (curr == "") {
                        //CLS1141.048=請選擇計算幣別
                        return API.confirmMessage(i18n.cls1141m01['CLS1141.048']);
                    }
                    $.thickbox.close();
                    //CLS1141.049=同一份額度明細表有二種以上幣別者, 是否要增列按各幣別合計顯示？
                    API.confirmMessage(i18n.cls1141m01['CLS1141.049'], function(b){
                        if (b) {
                            CLS1141S03Action.editCount(curr, true);
                        } else {
                            CLS1141S03Action.editCount(curr, false);
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 直接做計算
     * @param {Object} curr 多幣別時的計算幣別
     */
    justOnlyCount: function(curr){
        $.ajax({
            handler: CLS1141S03Action.fhandle,
            action: (curr) ? "queryL140m01aCountToTwoCurr" : "queryL140m01aCount",
            data: {
                noOpenDoc: true,
                mainId: $("#mainId").val(),
                CaseType: CLS1141S03Action.itemType,
                curr: curr
            },
            success: function(obj){
                CLS1141S03Action.setL120M01ALongCaseDscr(obj);
                CLS1141S03Action.reloadGrid();
            }
        });
    },
    /**
     * 設定是否已至資料建檔建置同一關係人表
     */
    setL120M01ALongCaseDscr: function(obj){
        //當兩項皆不存在則不需顯示
        if (!obj.checkBy3000 && !obj.checkCustPos) {
        	CLS1141S03Action.refreshPage();
            return false;
        }
        var $form = $("#setCustPosAnd3000BoxForm");
        $form.reset();
        // LongCaseFlag 個金用來 顯示是否已徵提保證人同意書及宣告書
        // longCaseDscr 個金用來 顯示 是否已至資料建檔建置同一關係人表
        if (obj.checkCustPos) {
            $form.find("#longCaseFlagTr").show();
        } else {
            $form.find("#longCaseFlagTr").hide();
        }
        if (obj.checkBy3000) {
            $form.find("#longCaseDscrTr").show();
        } else {
            $form.find("#longCaseDscrTr").hide();
        }
        
        $("#setCustPosAnd3000Box").thickbox({
            //selectOption=選項
            title: i18n.def['selectOption'],
            width: 500,
            height: 180,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                        $.ajax({
                            handler: CLS1141S03Action.fhandle,
                            action: "setL120M01ALongCaseDscr",
                            formId: "setCustPosAnd3000BoxForm",
                            data: {
                                noOpenDoc: true,
                                mainId: $("#mainId").val()
                            },
                            success: function(obj){
                                $.thickbox.close();                  
                            },
                            complete:function(result){       	
                            	CLS1141S03Action.refreshPage();
                            }
                        });
                    }
                }
            }
        });
    },
    /**
     * 設定列印順序
     */
    printviewBt: function(){
        $("#gridviewprint").trigger("reloadGrid");
        $("#printview").thickbox({
            //CLS1141.062=額度明細表列印順序設定
            title: i18n.cls1141m01['CLS1141.062'],
            width: 900,
            height: 500,
            modal: true,
            i18n: i18n.cls1141m01,
            readOnly: _openerLockDoc == "1",
            buttons: API.createJSON([{
                //CLS1141.064=寫回額度明細表
                key: i18n.cls1141m01['CLS1141.064'],
                value: function(){
                    var $gridviewprint = $("#gridviewprint");
                    //寫回額度明細表
                    $gridviewprint.jqGrid('saveRow', lastSel, false, 'clientArray');
                    var ids = $gridviewprint.jqGrid('getDataIDs');
                    //用來放列印順序跟oid
                    var json = {};
                    var checkArray = $gridviewprint.getCol("printSeq");
                    
                    //檢查列印順序值是否重複
                    if (CLS1141S03Action.checkArrayRepeat(checkArray)) {
                        //L140M01a.error24=列印順序不可重複
                        return API.showMessage(i18n.cls1141m01['CLS1141.063']);
                    }
                    
                    for (var id in ids) {
                        var data = $gridviewprint.jqGrid('getRowData', ids[id]);
                        json[data.oid] = data.printSeq;
                    }
                    $.ajax({
                        handler: CLS1141S03Action.fhandle,
                        action: "savePrintSeq",
                        formId: "empty",
                        data: {
                            mainId: $("#mainId").val(),
                            noOpenDoc: true,
                            caseType: CLS1141S03Action.itemType,
                            data: JSON.stringify(json)
                        },
                        success: function(obj){
                            $.thickbox.close();
                            CLS1141S03Action.reloadGrid();
                        }
                    });
                }
            }, {
                key: i18n.def['close'],
                value: function(){
                    $.thickbox.close();
                }
            }])
        });
    },
    
    
    
    /**
     * 查詢非本案額度明細表
     */
    queryDiffCntrNoDoc: function(){
        var $form = $("#queryDiffCntrNoDocForm");
        $form.reset();
        $("#queryDiffCntrNoDocBox").thickbox({
            title: i18n.cls1141m01['CLS1141.016'],//CLS1141.016=查詢非本案額度明細表
            width: 800,
            height: 450,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        
    },
    
    queryDiffCntrNoDocGrid: null,
    /**
     * 查詢非本案額度明細表 內容查詢
     */
    queryDiffCntrNoDocBt: function(){
        var $form = $("#queryDiffCntrNoDocForm");
        if ($form.valid()) {
            var setData = $form.serializeData();
            if (!CLS1141S03Action.queryDiffCntrNoDocGrid) {
                CLS1141S03Action.queryDiffCntrNoDocGrid = $("#queryDiffCntrNoDocGrid").iGrid({
                    handler: CLS1141S03Action.ghandle,
                    height: 230,
                    rownumbers: true,
                    rowNum: 10,
                    postData: $.extend({
                        formAction: "queryDiffCntrNoDoc"
                    }, setData || {}),
                    colModel: [{
                        colHeader: i18n.cls1141m01["CLS.custName"],//借款人名稱
                        name: 'custName',
                        align: "left",
                        width: 100,
                        sortable: true,
                        formatter: 'click',
                        onclick: CLS1141S03Action.openCntrNoLockDoc
                    }, {
                        colHeader: i18n.cls1141m01["CLS.cntrno"],//"額度序號",
                        name: 'cntrNo',
                        align: "left",
                        width: 100,
                        sortable: true
                    }, {
                        colHeader: i18n.cls1141m01["CLS.proPerty"],//"性質"
                        name: 'proPerty',
                        align: "center",
                        width: 100,
                        sortable: true
                    }, {
                        colHeader: i18n.cls1141m01["CLS1141.107"],//CLS1141.107=額/批
                        name: 'l120m01c.itemType',
                        align: "center",
                        width: 50,
                        sortable: true,
                        formatter: CLS1141S03Action.itemTypeFormatter
                    }, {
                        colHeader: i18n.cls1141m01["l120m01a.caseno"],//案   號,
                        name: 'caseNo',
                        align: "center",
                        editable: true,
                        width: 100,
                        sortable: true
                    }, {
                        name: 'oid',
                        hidden: true
                    }, {
                        name: 'mainId',
                        hidden: true
                    }],
                    ondblClickRow: function(rowid){
                        var data = CLS1141S03Action.queryDiffCntrNoDocGrid.getRowData(rowid);
                        CLS1141S03Action.openCntrNoLockDoc(null, null, data);
                    }
                });
            } else {
                CLS1141S03Action.queryDiffCntrNoDocGrid.reload(setData)
            }
        }
    },
    /** 檢查陣列內容是否重複 */
    checkArrayRepeat: function(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            } else {
                return true;
            }
        }
        return false;
    },
    /**
     *產生查核事項
     */
    creatL140M04A: function(){
        $.ajax({
            handler: CLS1141S03Action.fhandle,
            formId: 'empty',
            action: "creatL140M04A",
            success: function(obj){
                if (obj.htmlCode) {
                    CLS1141S03Action.creatL140M04ATable(obj.htmlCode);
                    $("#L140M04A_createTime").html(DOMPurify.sanitize(obj.L140M04A_createTime));
                } 

                if(obj.show_CLS1141_117=="Y"){
                    //CLS1141.117=新做/短放續約/短擔已續約超過6次/換約才需做查核事項！！
                    API.showMessage(i18n.cls1141m01['CLS1141.117']);
                }
            }
        });
    },
    /**
     *產生查核事項 呈現畫面
     * @param {Object} htmlCode 需產生的資料
     */
    creatL140M04ATable: function(htmlCode){
        var $target = $("#L140M04Acontent");
        $target.empty();
        $target.html(htmlCode);
        if (CLSAction.isReadOnly()) {
            $target.readOnlyChilds();
        }
    },
    /**
     *是否須向個人借戶徵提「同一關係人」資料表。
     */
    checkC120S01P: function(json){
    	var my_dfd = $.Deferred();
    	if(CLS1141S03Action.itemType == "1"){
    		var oids = [];
        	
    		var ids = CLS1141S03Action.grid.jqGrid('getDataIDs');
        	for (var i = 0; i < ids.length; i++) 
        	{
        	    var rowData = CLS1141S03Action.grid.jqGrid ('getRowData', ids[i]);
        	    oids.push(rowData.oid);
        	}
            
        	$.ajax($.extend({
                handler: CLS1141S03Action.fhandle,
                action: "checkC120S01P",
                data: {oids:oids},
                success: function(json){
                	if ($.isEmptyObject(json.result)){
                		my_dfd.resolve();
                	}else{
                		var target = 0;
                		var cnt = 0;
                		$.each( json.result, function( k, v ) {
                			target++;
                		});
                		//------
                		var showMsg = [];
                		$.each( json.result, function( custName, c120s01p ) {
                			var msg = [];
                			msg.push(custName);
                			msg.push(" ");
                			msg.push(i18n.cls1141m01['CLS1141.121']);
                			msg.push("<ul>");
                			for(var i=0;i<c120s01p.length;i++){
                				msg.push("<li>‧"+ c120s01p[i].comCustId+":"+ c120s01p[i].comName+"</li>");
                			}
                			msg.push("</ul>");
                			msg.push(i18n.cls1141m01['CLS1141.122'].replace("{0}", custName));
                			
                			showMsg.push(msg.join(""));
            			});  
                		//------
                		var theQueue = $({});
                		$.each(showMsg,function(k, v) {
                			theQueue.queue('myqueue', function(next) { 
                				API.showPopMessage("", v,function(){
                					cnt++;
                  					if(cnt==target){
                      					my_dfd.resolve();	
                      				}
                  					//---
                  					//把 next() 寫在 close 的 callback 裡
                  					//才會 1個 dialog 關掉後,再顯示下1個
                  					//不然會1次跳 N 個出來
                  					next();
                  				});
                			}); 
                		});
                		theQueue.dequeue('myqueue');
                	}
                }
            }, json || {})); 
    	}else{
    		my_dfd.resolve();
    	}
    	return my_dfd.promise();
    },
    openL120S18A: function(column, oid, data){

		var buttons = {};
		
		var custId = data.custId;
		var dupNo = data.dupNo;
		var custName = data.custName;
		
		
		$("#gridviewRelatedEconomicDetail").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
			postData : {
				'custId' : custId
                ,'dupNo' : dupNo
			},
			search : true
		}).trigger("reloadGrid");

		buttons["cancel"] = function() {
			$.thickbox.close();
		};

		
		$("#detailL120s18aBox").thickbox({
			title : custId+dupNo+" "+custName,
			width : 760,
			height : 370,
			modal : true,
			readOnly : thickboxOptions.readOnly,
			align : "center",
			i18n : i18n.def,
			valign : "bottom",
			buttons : buttons
		});
		
    },
    prompt_befImport_L120S18A : function() {		
		var my_dfd = $.Deferred(); 
		if( $("#gridviewRelatedEconomic").jqGrid('getGridParam','records')> 0){
			//L120S18A.message001=執行引進後會刪除已存在之名單，是否確定執行？
    		CommonAPI.confirmMessage(i18n.cls1141m01["L120S18A.message001"], function(b){
				if (b) {					
					my_dfd.resolve();
				}				
			});		
		}else{
			my_dfd.resolve();	
		}
		return my_dfd.promise();
	},
    /**
     *完成授信合計重新整理頁面，以顯示申請資料核對表頁籤。
     */
    refreshPage: function(){
    	
    	//J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
    	//重新載入，更新頁籤
    	/*
    	  //在額度明細表 , 執行 $("#docStatus").val() 有抓到值
    	  //但在批覆書 , 執行 $("#docStatus").val() 是  undefined  => 造成 DocCheckServiceImpl.checkDocStatus(...)會丟出 Exception
    	$.form.submit({
            url: location.href,
            target: $("#mainOid").val(),
            data: {
                txCode: responseJSON.txCode,
                mainId: $("#mainId").val(),
                mainOid: $("#mainOid").val(),
                mainDocStatus: $("#docStatus").val()
            }
        }); 
        */ 
        var refreshPage_txCode = {txCode: responseJSON.txCode};
        $.ajax({ handler: 'cls1141m01formhandler', data: {formAction: "openL120M01AParam", 'mainOid':$("#mainOid").val()},
            success: function(json_openPageParam){
            	var _cls1141_run_refresh  = false;
            	if(json_openPageParam.checkListFlag=="Y"){
            		if( $("#tabs-lms").find("a[goto=24]").length==0){
            			_cls1141_run_refresh  = true;	
            		}	
            	}else{
            		if( $("#tabs-lms").find("a[goto=24]").length>0){
            			_cls1141_run_refresh  = true;	
            		}
            	}
            	if(_cls1141_run_refresh){
	            	var postData = {
	            		'mainOid': json_openPageParam.mainOid, 
	            		'mainId': json_openPageParam.mainId,
	            		'mainDocStatus': json_openPageParam.mainDocStatus
	            	}
	
	            	$.form.submit({
	                    url: location.href,
	                    target: $("#mainOid").val(),
	                    data: $.extend(refreshPage_txCode, postData)                    
	                });            	
            	}
            }
        });
        
    },
    /**
     * 取得該客戶最後一筆決策客群
     */
    getlast_brmp_termGroupRule: function(c120m01a_oid, custid, dupno, termGroupObj, termGroupSubObj, termGroupRuleResultTextObj){ 	
    	if(c120m01a_oid != ""){
    		$.ajax({
    	        type: "POST",
    	        handler: "cls1141m01formhandler",
    	        data: {formAction: "show_last_brmp_termGroupRule", "c120m01a_oid":c120m01a_oid},
    	        success: function(jsonparm){
    	        	//console.log(jsonparm);
    	        	var resultmsg = "";
    	        	if(jsonparm.hasbrmp004){//有結果
    	                if(jsonparm.brmp004data.result.termGroup != null && jsonparm.brmp004data.result.termGroup != ""){//最終客群分類 "X":不承作 , "S":小額 , "N":普惠 , "G":優質, "E":行員
    	                	termGroupObj.val(jsonparm.brmp004data.result.termGroup);
    	                	resultmsg += termGroupObj.find(":selected").text();
    	                	if(jsonparm.brmp004data.result.applyDBRType != null){
        	                	termGroupSubObj.val(jsonparm.brmp004data.result.applyDBRType);	
        	                	if(jsonparm.brmp004data.result.applyDBRType == "A"){
                            		resultmsg += " (DBR上限15倍)";
                            	}
        	                }
        	                termGroupRuleResultTextObj.text(resultmsg);
    	                }
    	            }else{
    	            	termGroupObj.val("");
    	            	termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
    	            	CommonAPI.showMessage(i18n.cls1141m01['L140S02A.chkTermGroup']);
    	            }
    	        	
    	        }
    	    });
    	}else{
    		termGroupObj.val("");
    		termGroupRuleResultTextObj.text(termGroupObj.find("option:selected").text());
    	}
    },
	
	loadNotChangedLtvGridInfo: function(){
		
		$("#notChangedCaseLtvGrid").iGrid({
			handler: 'cls1151gridhandler',
			height: 170,
			width: 280,
			rownumbers: true,
			action: "queryNotChangedL140m01aCase",
			postData: {
				tabFormMainId: responseJSON.mainId,
				custId: $("#mainCustId").val(),
				dupNo: $("#mainDupNo").val()
			},
			colModel: [{
				colHeader: i18n.cls1141m01["CLS.cntrno"], //額度序號
				name: 'cntrNo',
				align: "left",
				width: 20,
				sortable: true,
				formatter: 'click',
                onclick: CLS1141S03Action.openEditLtvBox
			}, {
				colHeader: i18n.cls1141m01["L140M01A.realEstateLtv"],//(不動產)核貸成數
				name: 'approvedPercent',
				align: "right",
				width: 20,
				formatter:'number',
				editable:true
			}, {
                name: 'mainId',
                hidden: true
            }]
		});
	},
	
	openEditLtvBox: function(cellvalue, type, data){
		$('#cntrNo').html(data.cntrNo);
		$('#approvedPercent').val(data.approvedPercent == null ? '' : data.approvedPercent);
		
		$("#editLtvValueDiv").thickbox({
            title: i18n.cls1141m01["L140M01A.title.editLtv"], //編輯核貸成數
            width: 350,
            height: 200,
            modal: true,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
				"saveData": function(){
					
					var $form = $("#editNotChangedCaseLtvForm");
					if ($form.valid()) {
						
						var approvedPercent = $('#approvedPercent').val();
						if(approvedPercent <= 0){
							// 核貸成數需大於0
							return API.showErrorMessage(i18n.cls1141m01['L140M01A.msg.error.approvedPercentMustMoreThan0']);
						}
						
						$.ajax({
				            handler: CLS1141S03Action.fhandle,
				            formId: 'empty',
				            action: "saveNotChangedCaseLtvValue",
							type: "POST",
	    	        		data: {
								mainId: data.mainId,
								ltv: approvedPercent
							},
				            success: function(obj){
								$("#notChangedCaseLtvGrid").trigger("reloadGrid");
								CommonAPI.showMessage(i18n.def["saveSuccess"]);
				            }
				        });
					}
					
					$.thickbox.close();
				},
				
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
	},
	
	processMortgageRatioValidation: function(){
		var my_dfd = $.Deferred();
		$.ajax({
            handler: CLS1141S03Action.fhandle,
            action: "processMortgageRatioValidation",
            formId: 'empty',
            data: {
                mainId: $("#mainId").val()
            },
			success: function(obj){

				if(obj.tipsMsg != ''){
					CommonAPI.confirmMessage(obj.tipsMsg, function(b){
						if (b) {
							my_dfd.resolve();
						}
					});
				}
				else{
					my_dfd.resolve();
				}
			}
    	});
		
		return my_dfd.promise();
	}
};

initDfd.done(function(obj){
    CLS1141S03Action.start(obj);
});
