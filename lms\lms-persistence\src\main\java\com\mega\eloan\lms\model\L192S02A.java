package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/**
 * 擔保品資料檔
 */
@NamedEntityGraph(name = "L192S02A-entity-graph", attributeNodes = { @NamedAttributeNode("l192m01a") })
@Entity
public class L192S02A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 6246410363516496317L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	@Column(length = 32, nullable = false, columnDefinition = "CHAR(32)")
	private String mainId;

	/**擔保品名稱*/
	@Column(length = 192, columnDefinition = "VARCHAR(192)")
	private String gteName;

	/**估　　值(幣別)*/
	@Column(length = 3, columnDefinition = "VARCHAR(3)")
	private String estCurr;

	/**估　　值(金額)*/
	@Column(columnDefinition = "DECIMAL(13,0)")
	private BigDecimal estAmt;

	/**押　　值(幣別)*/
	@Column(length = 3, columnDefinition = "VARCHAR(3)")
	private String loanCurr;

	/**押　　值(金額)*/
	@Column(columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/**設定金額(幣別)*/
	@Column(length = 3, columnDefinition = "VARCHAR(3)")
	private String setCurr;

	/**設定金額(金額)*/
	@Column(columnDefinition = "DECIMAL(13,0)")
	private BigDecimal setAmt;

	/**鑑價日期*/
	@Temporal(TemporalType.DATE)
	private Date estDate;

	/**所有權人*/
	@Column(length = 192, columnDefinition = "VARCHAR(192)")
	private String owner;

	/**設定日期*/
	@Temporal(TemporalType.DATE)
	private Date setDate;

	/**設定期限(起)*/
	@Temporal(TemporalType.DATE)
	private Date setDateFrom;

	/**設定期限(迄)*/
	@Temporal(TemporalType.DATE)
	private Date setDateEnd;

	/**設定順位*/
	@Column(columnDefinition = "DECIMAL(2,0)")
	private Integer setPosition;

	/**險種/金額*/
	@Column(length = 384, columnDefinition = "VARCHAR(384)")
	private String insurance;

	/**保險期限(起)*/
	@Temporal(TemporalType.DATE)
	private Date insDateFrom;

	/**保險期限(迄)*/
	@Temporal(TemporalType.DATE)
	private Date insDateEnd;

	/**保費收據*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String insPaper;
	
	/**建立人員號碼*/
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/**建立日期*/
	@Temporal(TemporalType.TIMESTAMP)
	private Date createTime;

	/**異動人員號碼*/
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/**異動日期*/
	@Temporal(TemporalType.TIMESTAMP)
	private Date updateTime;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", insertable = false, updatable = false) })
	private L192M01A l192m01a;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/** 擔保品名稱 */
	public String getGteName() {
		return gteName;
	}

	/** 擔保品名稱 */
	public void setGteName(String gteName) {
		this.gteName = gteName;
	}

	/** 估　　值(幣別) */
	public String getEstCurr() {
		return estCurr;
	}

	/** 估　　值(幣別) */
	public void setEstCurr(String estCurr) {
		this.estCurr = estCurr;
	}

	/** 估　　值(金額) */
	public BigDecimal getEstAmt() {
		return estAmt;
	}

	/** 估　　值(金額) */
	public void setEstAmt(BigDecimal estAmt) {
		this.estAmt = estAmt;
	}

	/** 押　　值(幣別) */
	public String getLoanCurr() {
		return loanCurr;
	}

	/** 押　　值(幣別) */
	public void setLoanCurr(String loanCurr) {
		this.loanCurr = loanCurr;
	}

	/** 押　　值(金額) */
	public BigDecimal getLoanAmt() {
		return loanAmt;
	}

	/** 押　　值(金額) */
	public void setLoanAmt(BigDecimal loanAmt) {
		this.loanAmt = loanAmt;
	}

	/** 設定金額(幣別) */
	public String getSetCurr() {
		return setCurr;
	}

	/** 設定金額(幣別) */
	public void setSetCurr(String setCurr) {
		this.setCurr = setCurr;
	}

	/** 設定金額(金額) */
	public BigDecimal getSetAmt() {
		return setAmt;
	}

	/** 設定金額(金額) */
	public void setSetAmt(BigDecimal setAmt) {
		this.setAmt = setAmt;
	}

	/** 鑑價日期 */
	public Date getEstDate() {
		return estDate;
	}

	/** 鑑價日期 */
	public void setEstDate(Date estDate) {
		this.estDate = estDate;
	}

	/** 所有權人 */
	public String getOwner() {
		return owner;
	}

	/** 所有權人 */
	public void setOwner(String owner) {
		this.owner = owner;
	}

	/** 設定日期 */
	public Date getSetDate() {
		return setDate;
	}

	/** 設定日期 */
	public void setSetDate(Date setDate) {
		this.setDate = setDate;
	}

	/** 設定期限(起) */
	public Date getSetDateFrom() {
		return setDateFrom;
	}

	/** 設定期限(起) */
	public void setSetDateFrom(Date setDateFrom) {
		this.setDateFrom = setDateFrom;
	}

	/** 設定期限(迄) */
	public Date getSetDateEnd() {
		return setDateEnd;
	}

	/** 設定期限(迄) */
	public void setSetDateEnd(Date setDateEnd) {
		this.setDateEnd = setDateEnd;
	}

	/** 設定順位 */
	public Integer getSetPosition() {
		return setPosition;
	}

	/** 設定順位 */
	public void setSetPosition(Integer setPosition) {
		this.setPosition = setPosition;
	}

	/** 險種/金額 */
	public String getInsurance() {
		return insurance;
	}

	/** 險種/金額 */
	public void setInsurance(String insurance) {
		this.insurance = insurance;
	}

	/** 保險期限(起) */
	public Date getInsDateFrom() {
		return insDateFrom;
	}

	/** 保險期限(起) */
	public void setInsDateFrom(Date insDateFrom) {
		this.insDateFrom = insDateFrom;
	}

	/** 保險期限(迄) */
	public Date getInsDateEnd() {
		return insDateEnd;
	}

	/** 保險期限(迄) */
	public void setInsDateEnd(Date insDateEnd) {
		this.insDateEnd = insDateEnd;
	}

	/** 保費收據 */
	public String getInsPaper() {
		return insPaper;
	}

	/** 保費收據 */
	public void setInsPaper(String insPaper) {
		this.insPaper = insPaper;
	}

	/** 建立人員號碼 */
	public String getCreator() {
		return creator;
	}

	/** 建立人員號碼 */
	public void setCreator(String creator) {
		this.creator = creator;
	}

	/** 建立日期 */
	public Date getCreateTime() {
		return createTime;
	}

	/** 建立日期 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/** 異動人員號碼 */
	public String getUpdater() {
		return updater;
	}

	/** 異動人員號碼 */
	public void setUpdater(String updater) {
		this.updater = updater;
	}

	/** 異動日期 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/** 異動日期 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public L192M01A getL192m01a() {
		return l192m01a;
	}

	public void setL192m01a(L192M01A l192m01a) {
		this.l192m01a = l192m01a;
	}

}
