
var initDfd = $.Deferred(), inits = {
	fhandle: "lms8100m01formhandler",
    ghandle: "lms8100gridhandler"
};

// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || responseJSON.mainDocStatus != "01O" || _openerLockDoc == "1") {
        return true;
    }
    return false;
}

$(document).ready(function(){
	
	init();
	
    $("#check1").show();
    
    if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
        _openerLockDoc = "1";
    }

    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        saveData(true);
    }).end().find("#btnSend").click(function(){
//        saveData(false, sendNoBoss);
        saveData(false, sendBoss);
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                	saveData(false, printAction);
                }
            });
        }
    });
    
    // 初始
    function init(){
    	$.form.init({
            formHandler: inits.fhandle,
            formAction: 'queryL300m01a',
            loadSuccess: function(json){
                $('body').injectData(json);
                
                // 組附件連結
                build_attchLink(json);
                
                if(json.produceFlag && json.produceFlag == "Y"){
                	$(".afterCalc").show();
                } else {
                	$(".afterCalc").hide();
                }
                
                // J-112-0461 授信貸後管理缺失考評表之說明一、考評期間：「分為上半年、下半年，各六個月為一期。」更改為「每三個月為一期，按季考評一年分四期。」
                if(json.produceInterval === "S" ){
                	var memo1 = $('#memo1');
                	memo1.html('&nbsp;&nbsp;一、	考評期間：每三個月為一期，按季考評一年分四期。');                	                	                	
                }

                if (checkReadonly()) {
                    $(".readOnlyhide").hide();
                    $("form").lockDoc();
                    _openerLockDoc = "1";
                }
            }
        });
    	
    	$("#reImpl").click(function(){
    		CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
                if (b) {
                	$.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "reImplL300S01A",
                            oid: responseJSON.oid
                        },
                        success: function(obj){
                        	$('body').injectData(obj);
                        	
                        	// 組附件連結
                            build_attchLink(obj);
                            
                            if(obj.produceFlag && obj.produceFlag == "Y"){
                            	$(".afterCalc").show();
                            } else {
                            	$(".afterCalc").hide();
                            }
                        }
                    });
                }
    		});
    	});
    	
    	$("#reCalc").click(function(){
    		// 計算每項扣分
    		$("#mainPanel").find(".mathit").each(function(i){
    	        var $this = $(this);
    	        var cnt = $this.val();
    	        // $this.parent().next() 隔壁 <td>
    	        var score = $this.parent().next().find("input").val();
    	        // $this.parent().next().next() 隔壁的隔壁 <td>
    	        $this.parent().next().next().find("input").val(cnt * score);
    	    });
    		// 扣分小計（A）
    		var subTotal = 0;
    		$("#mainPanel").find(".totalit").each(function(j){
    			var $this = $(this);
    			subTotal += parseFloat($this.val());
            });
    		$("#mainPanel").find("#subTotal").val(subTotal);
    		
    		// 平均扣分值（A/B）（取至小數第三位）
    		ilog.debug("rsNum===" + $("#mainPanel").find("#rsNum").val() + "==subTotal====" + subTotal + "==avgScore===" + $("#mainPanel").find("#avgScore").val());
    		var avgScore = 0;
    		var rsNum = $("#mainPanel").find("#rsNum").val();
    		avgScore = subTotal / rsNum;
    		avgScore = (isNaN(avgScore) ? 0 : avgScore.toFixed(3));
    		$("#mainPanel").find("#avgScore").val(avgScore);
        });
    }
    
	// 儲存的動作
    function saveData(showMsg, tofn){
    	// 為檢查UI的值是否皆無異常
        if ($("#mainPanel").valid() == false) {
            return;
        }
        
        $("#reCalc").trigger("click");
        
        $.ajax({
            handler: inits.fhandle,
            data: {// 把資料轉成json
                formAction: "saveL300M01A",
                oid: responseJSON.oid,
                showMsg: showMsg
            },
            success: function(obj){
                $('body').injectData(obj);
                
                // 執行列印
                if (!showMsg && tofn) {
                    tofn();
                }
            }
        });
    }
    
    // 呈主管 - 編製中
    var item;
    function sendBoss(){
        var selectBossForm = $("#selectBossForm");
        var selectBossBox = $("#selectBossBox");
    	$.ajax({
            handler: inits.fhandle,
            action: "getBossList",
            data: {},
            success: function(json){
                // 掃到高風險 $(".boss").setItems({
            	selectBossForm.find(".boss").setItems({
                    item: json.bossList
                });
            	//confirmApply=是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b){
                    if (b) {
                        // 掃到高風險 $("#selectBossBox").thickbox({
                    	selectBossBox.thickbox({ // 使用選取的內容進行彈窗
                    		// button.send=呈主管覆核
                    		title: i18n.abstracteloan['button.send'],
	                        width: 300,
	                        height: 180,
	                        modal: true,
	                        valign: "bottom",
	                        align: "center",
	                        readOnly: false,
	                        i18n: i18n.def,
	                        buttons: {
	                            "sure": function(){
                                    // 請選擇主管
	                            	if ($("#staffL3Item").val() == "") {
                                        return CommonAPI.showErrorMessage(i18n.def['err.chooseBoss']);
                                    }
	                            	if ($("#staffL5Item").val() == "") {
                                        return CommonAPI.showErrorMessage(i18n.def['err.chooseBoss']);
                                    }
	                            	if ($("#staffL6Item").val() == "") {
                                        return CommonAPI.showErrorMessage(i18n.def['err.chooseBoss']);
                                    }
	                            	
	                            	flowAction({
                                        saveData: true,
                                        staffL3Item: $("#staffL3Item").val(),
                                        staffL5Item: $("#staffL5Item").val(),
                                        staffL6Item: $("#staffL6Item").val()
                                    });
	                            	$.thickbox.close();
	                            },	                            
	                            "cancel": function(){
	                                $.thickbox.close();
	                            }
	                        }
                    	});
                    }
                });
            }
        });
    }
    
    // 呈主管(不選簽章欄主管)
    function sendNoBoss(){
        // confirmApply=是否呈主管覆核？
        CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b){
            if (b) {
//                flowAction({
//                    page: responseJSON.page,
//                    saveData: true,
//                    sendBoss: true
//                });
            }
        });
    }
    
    // 待覆核 - 覆核
    function openCheck(){
    	$("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.abstracteloan['button.check'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        return CommonAPI.showMessage(i18n.abstracteloan['plsSel']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // 該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms8100m01['message01'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            }); 
                            break;
                        case "3":
                            // 該案件是否確定執行核定作業
                            CommonAPI.confirmMessage(i18n.lms8100m01['message02'], function(b){
                                if (b) {
				                    flowAction({
				                        flowAction: true,
				                        checkDate: CommonAPI.getToday()//forCheckDate
				                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    function flowAction(sendData){
    	$.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: responseJSON.oid
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
				window.close();
            }
        });
    }
    
    // 列印動作
    function printAction(){
    	$.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
//                mainId: $("#mainId").val(),//responseJSON.mainId,
//                mainOid: responseJSON.oid,
            	oids: responseJSON.oid,
                type: "R01",
                fileDownloadName: "lms8100r01.pdf",
                serviceName: "lms8100r01rptservice"
            }
        });
    }

    /*
    function build_paForm(jsonParam){
        var paForm = $('#mainPanel').find("#paForm");
        $.each(jsonParam.l300s01aResult, function(itemKey, itemVal){
            $('#mainPanel').find("#paForm").find("#" + itemKey).val(itemVal);
        });
        paForm.injectData(jsonParam);
    }
    */

    function build_attchLink(passParam){
    	$.each(passParam.attchResult, function(divId, arr){
    		var dyna = [];
    		{
    			$.each(arr, function(idx, jsonItem) {
    				var inProcess = false;
    				if(jsonItem.flag!==null && Boolean(jsonItem.flag)){
    					if(jsonItem.flag=="P"){
    						inProcess = true;
    					}
    				}				
    				dyna.push("<span class='"+(inProcess?"color-red":"linkDocFile")+"' oid='"+jsonItem.oid+"'>"
    						+(inProcess?"產生中...請等候":jsonItem.srcFileName)+"</span>"
    						+ "&nbsp;&nbsp;&nbsp;&nbsp;("+jsonItem.uploadTime+")");					
    			});	
    		}					
    		$("#"+divId).html(dyna.join("<br/>"));
    		$("#"+divId).find('span.linkDocFile').click(function(){
    			var oid = jQuery(this).attr("oid");
    			
    			$.capFileDownload({
    		        handler:"simplefiledwnhandler",
    		        data : {
    		            fileOid:oid
    		        }
    			});				
    		});
    	});
    }
});
