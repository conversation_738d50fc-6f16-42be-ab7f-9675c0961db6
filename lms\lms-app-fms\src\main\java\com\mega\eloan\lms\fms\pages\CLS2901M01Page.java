package com.mega.eloan.lms.fms.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.model.C900M01J;

import tw.com.jcs.auth.AuthType;


@Controller
@RequestMapping(path = "/fms/cls2901m01/{page}")
public class CLS2901M01Page extends AbstractEloanForm {

	@Autowired
	CLSService service;
	
	public CLS2901M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {	
		new DocLogPanel("_docLog").processPanelData(model, params);
		
		if(true){
			StringBuilder lnflagSB = new StringBuilder();
			
			Map<String, String> map = service.get_codeTypeWithOrder("lnFlag_extend_C250M01A_C900M01H");
			lnflagSB.append("<table border='0'>");
			for(String k: map.keySet()){
				String v = map.get(k);
			
		    	lnflagSB.append("<tr><td style='padding-bottom:8px; border:0; '>")
		                .append("<label><input type='radio' name='lnflag' id='lnflag' value='")
		                .append(k).append("' />")
		                .append(v).append("</label>")
		                .append("</td></tr>");
		    }
			lnflagSB.append("</table>");
			// UPGRADE: 前端須配合改為 <span th:utext......>（類似於 setEscapeModelStrings(false)，不轉義 HTML，直接解析）
		    // Label _C900M01J_lnFlag_Label = new Label("C900M01J_lnflag_Label", lnflagSB.toString());
		    // _C900M01J_lnFlag_Label.setEscapeModelStrings(false);
		    // add(_C900M01J_lnFlag_Label);	
			model.addAttribute("C900M01J_lnflag_Label", lnflagSB.toString());
		}
		
		
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = params.getString(EloanConstants.MAIN_DOC_STATUS);
		
		
		addAclLabel(model, new AclLabel("_btnSave", params, getDomainClass(),
				AuthType.Modify	, FlowDocStatusEnum.編製中));
		
		if (docStatus.equals("030|0C0") ){
			//黑名單查詢一律不顯示
			model.addAttribute("_btnWAIT_APPROVE", false );
			model.addAttribute("_btnWAIT_REMOVE", false );
			model.addAttribute("_btnRemove", false );
		}else{
			addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
					AuthType.Accept, false, FlowDocStatusEnum.待覆核));
			addAclLabel(model, new AclLabel("_btnWAIT_REMOVE", params, getDomainClass(),
					AuthType.Accept, false,FlowDocStatusEnum.待解除));
			addAclLabel(model, new AclLabel("_btnRemove", params, getDomainClass(),
					AuthType.Modify, false, FlowDocStatusEnum.已核准));
		}
		
		renderJsI18N(CLS2901M01Page.class);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {		
		return C900M01J.class;
	}
}
