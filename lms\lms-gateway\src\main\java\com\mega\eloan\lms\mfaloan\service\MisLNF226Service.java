/* 
 *MisLNF226Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 授信逾放比例檔
 * </pre>
 * 
 * @since 2012/2/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/6,REX,new
 *          </ul>
 */
public interface MisLNF226Service {
	/**
	 * 額度明細表--取得分行逾放比率
	 * 
	 * @param ownBrIds
	 *            陣列銀行代碼
	 * @return 分行逾放比率值
	 */
	public List<Map<String, Object>> findLNF226ForRate(String[] ownBrIds);
}
