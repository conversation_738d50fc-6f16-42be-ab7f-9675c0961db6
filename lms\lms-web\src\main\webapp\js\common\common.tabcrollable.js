var tabBorderColor = "#AAA";
//overwrite tabs
;(function($){
    if ($.fn.tabs) {
        var pageIcon = "<div class=\"scroll-icon\"><div class=\"tab-next\"><span class=\"ui-icon ui-icon-circle-triangle-e\"></span></div><div class=\"tab-prev\"><span class=\"ui-icon ui-icon-circle-triangle-w\"></span></div></div>";
        var _tabs = $.fn.tabs;
        !$(".tabBorderColor").length && $("<div class=\"tabBorderColor\" />").appendTo("body");
        $.fn.tabs = function(){
            var args = arguments;
            this.each(function(){
                var $this = $(this);
                if (!$this.find("ul:first").is(".ui-tabs-nav")) {
                
                    _tabs.apply($this.find("ul:first").css({
					 width: $.browser.msie ? 9999 :$(this).width() //$(this).outerWidth() || 600//,"padding-left":20,"padding-right":20
                    }).wrap("<div class='tabs-warp'/>").wrap("<div class=\"tabs-scroll-warp\" style=\"overflow: hidden; position: relative;\" />").end(), args);
                    
                    $this.bind("fitsize.tab", fn.tabFitSize).trigger("fitsize.tab");
                    
                    // add title
                    $this.find("ul:first > li > a ").each(function(){
                        var $$this = $(this);
                        !$$this.attr("title") && $$this.attr("title", $.trim($$this.text()));
                        $$this = null;
                    });
                    
                    
                    //change css
                    $this.removeClass("ui-widget-content ui-corner-all").find("ul:first").removeClass("ui-widget-content ui-widget-header ui-corner-all").end().find(".tabCtx-warp").addClass("ui-widget-content");
                    //corner css
                    $this.find(".ui-corner-top").corner("cc:#FFF top 6px");
                }
                else {
                    _tabs.apply($this, args);
                    if (args[0] && args[0] == 'select') {
                        fn.scrollToTab.call($this);
                    }
                }
            });
            return this;
        }
        
        var fn = {
            tabFitSize: function(){
                if ($(this).is(":visible")) {
					
                    var $this = $(this), $div;
                    if ($this.is(".tabs")) {
                        $div = $this;
                        $this = $div.find("ul:first");
                    }
                    else if ($this.is("ul")) {
                        $div = $this.closest(".tabs");
                    }
                    else {
                        return;
                    }
                    // fix firefox fitwidth
                    var blockWidth = $this.parent().css("marginRight",0).width();
                    var a = 0;
                    $this.removeClass("ui-tab-fit").children("li").each(function(){
                        a += ($(this).width() + 6);
                    });
                    $this.width(a < blockWidth ? blockWidth : a);
                    $this.parent().width(blockWidth);
                    if (a > blockWidth && !$div.find(".scroll-icon").length) {
                        $div.find(".tabs-scroll-warp:first").css({
                            "marginRight": 40,
                            width: blockWidth - 40
                        }).end().find(".tabs-warp:first").append(pageIcon).children(".scroll-icon").find(".tab-next span").click($.proxy(fn.nextTab, $div.find(".tabs-scroll-warp:first"))).end().find(".tab-prev span").click($.proxy(fn.prevTab, $div.find(".tabs-scroll-warp:first")));
                    }
                }
                return $(this);
            },
            changeTab: function(prev){
                prev = !!prev;
                try {
                    var index = this.find('li.ui-tabs-selected').prevAll().length + ((prev) ? -1 : 1);
                    if (prev && index >= 0 || !prev) {
                        var li = this.find('li').eq(index);
                        if (li.length) {
                            li.find("a[goto]").length && li.click() || this.tabs('select', index);
                        }
                    }
                } 
                catch (e) {
                    ilog.debug(e);
                }
            },
            nextTab: function(){
                var left = this.attr("scrollLeft") + 500;
                this.scrollTo(left, 100, {
                    'axis': 'x',
                    'margin': true,
                    'easing': 'swing'
                });
                //changeTab.call(this, false)
            },
            prevTab: function(){
                var left = this.attr("scrollLeft") - 500;
                this.scrollTo(left < 0 ? 0 : left, 100, {
                    'axis': 'x',
                    'margin': true,
                    'easing': 'swing'
                });
                //changeTab.call(this, true)
            },
            scrollToTab: function(){
                var obj = this.find(".tabs-scroll-warp");
                obj.scrollTo(this.find('li.ui-tabs-selected'), 500, {
                    'axis': 'x',
                    'margin': true,
                    'easing': 'swing',
                    offset: {
                        left: (this.width() - this.find('li.ui-tabs-selected').width() - 100) * -1
                    }
                });
            }
        };
        
    }
	
	$.fn.scrollToTab = function(){
		this.each(function(){
			fn.scrollToTab.call($(this));
		});
	}
})(jQuery);

