package com.mega.eloan.lms.lns.service;

/* 
 * LMS0011Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.model.L001M01A;
import com.mega.eloan.lms.model.VL015M01A01;

/**
 * <pre>
 * 待辦案件
 * </pre>
 * 
 * @since 2012/1/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/20,REX,new
 *          </ul>
 */
public interface LMS0011Service {
	/**
	 * 查詢待辦案件grid
	 * 
	 * @param search
	 *            設定查詢條件
	 * @return Page
	 */
	public Page<VL015M01A01> findPage(ISearch search);

	/**
	 * 查詢篩選條件
	 * 
	 * @param userId
	 *            使用者ID
	 * @return 待辦事項篩選條件檔
	 */
	public L001M01A findL001m01a(String userId);

//	/**
//	 * 儲存 篩選條件
//	 * 
//	 * @param l001m01a
//	 *            待辦事項篩選條件檔
//	 */
//	public void saveL001m01a(L001M01A l001m01a);

	/**
	 * 根據model選擇查詢的oid
	 * 
	 * @param <T>
	 *            GenericBean
	 * @param clazz
	 *            model
	 * @param oid
	 *            文件編號
	 * @return GenericBean model
	 */
	public <T extends GenericBean> T findModelByOid(Class<?> clazz, String oid);

	/**
	 * 儲存model
	 * 
	 * @param entity
	 *            model
	 */
	public void save(GenericBean... entity);

}