/* 
 *ObsdbELFAPPNOService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 配合泰行所需APP NO，AS400這邊會上傳ELFAPPNO
 * </pre>
 */
public interface ObsdbELFAPPNOService {

	/** 取得ELFAPPNO的資料(利用branchId, custId, dupNo)
	 * @param branchId 銀行代碼
	 * @param custId 身份證字號
	 * @param dupNo 重複序號
	 */
	List<Map<String, Object>> listAppNoDataByCustIdDupNoBranch(String branchId, String custId, String dupNo);

}
