---------------------------------------------------------
-- LMS.L140M03A 個金額度明細表主檔補充資料檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M03A;
CREATE TABLE LMS.L140M03A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	IDENTITYN<PERSON>    VARCHAR(1)   ,
	NOTICETYPE    VARCHAR(1)   ,
	YOUNCREATY<PERSON>   VARCHAR(1)   ,
	CAPITALAMT    DECIMAL(13,0),
	<PERSON><PERSON>ISTERDATE  DATE         ,
	ASSISTYPE     VARCHAR(1)   ,
	ASSISTYPEYN   VARCHAR(1)   ,
	COMPANYCITY   VARCHAR(1)   ,
	COMPANYAREA   VARCHAR(3)   ,
	COMPANYID     VARCHAR(10)  ,
	COMPANYDUPNO  VARCHAR(1)   ,
	COMPANYNAME   VARCHAR(120) ,
	ISUSE         CHAR (1)     ,
	ISUSEDATE     DATE         ,
	GRPCNTRNO     CHAR(12)     ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140M03A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M03A01;
CREATE UNIQUE INDEX LMS.XL140M03A01 ON LMS.L140M03A   (MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M03A IS '個金額度明細表主檔補充資料檔';
COMMENT ON LMS.L140M03A (
	OID           IS 'oid', 
	MAINID        IS 'mainId', 
	IDENTITYNO    IS '不計入同一關係戶代號', 
	NOTICETYPE    IS '單據寄發方式', 
	YOUNCREATYN   IS '是否屬於青創', 
	CAPITALAMT    IS '登記出租額', 
	REGISTERDATE  IS '設立登記日期', 
	ASSISTYPE     IS '屬優予核貸對象/育成中心輔導', 
	ASSISTYPEYN   IS '是否屬優予核貸對象/是否為育成中心輔導是否', 
	COMPANYCITY   IS '企業所在縣市', 
	COMPANYAREA   IS '企業所在鄉鎮市區', 
	COMPANYID     IS '同一事業體資訊(ID)', 
	COMPANYDUPNO  IS '同一事業體資訊(dupNo)', 
	COMPANYNAME   IS '同一事業體資訊(名稱)', 
	ISUSE         IS '是否動用', 
	ISUSEDATE     IS '動用日期', 
	GRPCNTRNO     IS '團貸編號', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
