/* 
 * L140MC1BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MC1B;

/** 房貸利率成數條件檢核明細紀錄檔 **/
public interface L140MC1BDao extends IGenericDao<L140MC1B> {

	L140MC1B findByOid(String oid);
	
	List<L140MC1B> findByMainId(String mainId);
	
	L140MC1B findByUniqueKey(String mainId);

	List<L140MC1B> findByIndex01(String mainId);

	List<L140MC1B> findByIndex02(String mainId, String cntrno);
	
	List<L140MC1B> findByCaseMainid(String srcMainId);

	List<L140MC1B> findByCmsOidsAndSrcMainId(String[] cmsoids, String srcMainId);

	List<L140MC1B> findExcludeMortgageInsuranceAndY1Y2Y3By(String cmsOid, String caseMainId);
}