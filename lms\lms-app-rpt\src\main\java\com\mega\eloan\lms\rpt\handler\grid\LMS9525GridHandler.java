/* 
 * LMS9525GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.model.L784M01A;
import com.mega.eloan.lms.rpt.service.LMS9515Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 歷史資料管理報表
 * </pre>
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9525gridhandler")
public class LMS9525GridHandler extends AbstractGridHandler {

	@Resource
	LMS9515Service service;

	@Resource
	BranchService branch;

	@Resource
	CodeTypeService codetypeService;

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings({ "rawtypes", "unchecked"})
	public CapGridResult queryL951m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String searchAction = Util
				.nullToSpace(params.getString("searchAction"));
		String startDate = Util
		.nullToSpace(params.getString("startDate"));
		String endDate = Util
		.nullToSpace(params.getString("endDate"));
		startDate = startDate + "-01";
		endDate = endDate + "-" + CapDate.getDayOfMonth(endDate.split("-")[0], endDate.split("-")[1]);
		
		int actionCode = Util.parseInt(searchAction);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l784a01a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptType",
				String.valueOf(actionCode));
		pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS, "dataDate",
				endDate);
		pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS, "dataDate",
				startDate);
		// pageSetting.addOrderBy("createTime");
		Page page = service.findPage(L784M01A.class, pageSetting);
		List<L784M01A> list = page.getContent();
		for (L784M01A model : list) {
			model.setOwnBrId(model.getOwnBrId()
					+ " "
					+ Util.nullToSpace(branch.getBranchName(model.getOwnBrId())));
			//不清楚為何createTime會格式化成yyyy:MM:DD 而不顯示時間 改用uid
			model.setUid(TWNDate.toFullAD(model.getCreateTime()));
			//不清楚為何SendFirstTime會格式化成yyyy:MM:DD 而不顯示時間 改用DocStatus
			model.setDocStatus(TWNDate.toFullAD(model.getSendFirstTime()));
			model.setBranchId(model.getBranchId() + Util.trim(branch.getBranchName(model.getBranchId())));
		}
		return new CapGridResult(list, page.getTotalRow());

	}
	

	/**
	 * 查詢 選中列的DocFile資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	public CapGridResult queryFile(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		List<DocFile> data = service.findDocFile(mainId, "rpt");
		return new CapGridResult(data, data.size());

	}
	
}
