var _handler = "cls2601formhandler";

$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");

	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){
			
			// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button")) {
				tabForm.lockDoc();				
			}
			
			tabForm.injectData(json);
			
			$("#ctlFlag").change(function() {
				if($("#ctlFlag option:selected").val()=="5"){
					$("#estateAgentFlag").attr("disabled", false);
					$("#record17Flag").attr("disabled", false);
				}else{
					$("#estateAgentFlag").attr("disabled", true);
					$("#record17Flag").attr("disabled", true);
				}
		    });
			if($("#ctlFlag option:selected").val()=="5"){
				$("#estateAgentFlag").attr("disabled", false);
				$("#record17Flag").attr("disabled", false);
			}else{
				$("#estateAgentFlag").attr("disabled", true);
				$("#record17Flag").attr("disabled", true);
			}
		}
	});

	btnPanel.find("#btnSave").click(function(){
		if(!tabForm.valid()){
			return;
		}
		
		saveAction().done(function(json){
			if(json.saveOkFlag){
				API.showMessage(i18n.def.saveSuccess);
			}
        });
	}).end().find("#btnSend,#btnRemove").click(function(){
		if(!tabForm.valid()){
			return;
		}
		
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			API.confirmMessage(i18n.def.confirmApply, function(result){
    	            if (result) {
    	            	flowAction({'decisionExpr':'呈主管'});    	
    	        	}
    	    	});
    		}
    	});	
	}).end().find("#btnAccept,#btnAcceptRemove").click(function(){	
		//分行[受檢單位]主管-覆核
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		var btnid = this.id;
		var decisionExprExtend = "";
		if(btnid == "btnAcceptRemove"){
			decisionExprExtend = "解除";
    	}
    	
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			//dyna.push("	<table><tr><td>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />核定</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />退回</label></p>");
			//dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	
                    	flowAction({'decisionExpr': decisionExprExtend +'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr': decisionExprExtend + '退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});
	
	//證書號前補0
	$("#agentCertNo").change(function(){
		var value = $(this).val();
		$(this).val(util.addZeroBefore(value, 6));		
	});
	
	var saveAction = function(opts){		
		if(tabForm.valid()){			
			return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    mainOid: $("#mainOid").val() || responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                ),                
                success: function(json){
                	responseJSON.mainOid = json.mainOid;
                	$("#mainOid").val(json.mainOid);
                	tabForm.injectData(json);
                	//更新 opener 的 Grid
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                }
            });
		}else{
			return $.Deferred();
		}
	}
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            ),                
            success: function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
            }
        });
	}
	
	$('#href_LaaNoticeItem').click(function(){
        $.form.submit({
            url: webroot + '/app/simple/FileProcessingService',
            target: "_blank",
            data: {
                markModel: "L",
                fileDownloadName: "LaaNoticeItem.pdf",
                serviceName: "cls1131s01pdfservice"
            }
        });
    });
	
});

