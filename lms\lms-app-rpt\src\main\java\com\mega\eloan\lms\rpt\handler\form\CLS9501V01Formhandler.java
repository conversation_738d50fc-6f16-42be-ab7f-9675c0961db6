/* 
 * LMS9541V01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.lms.model.C820M01A;
import com.mega.eloan.lms.rpt.service.CLS9501V01Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 收集批覆書額度資訊
 * </pre>
 * 
 * @since 2013/01/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/23,Vector,new
 *          </ul>
 */

@Scope("request")
@Controller("cls9501v01formhandler")
public class CLS9501V01Formhandler extends AbstractFormHandler {

	@Resource
	CLS9501V01Service service;

	@Resource
	BranchService branchService;

	/**
	 * 更改日期
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult changeDataYM(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		this.changeChks(params);

		String mainId = params.getString("mainId");
		String dataYM = params.getString("dataYM");

		List<C820M01A> data = (List<C820M01A>) service.getAllSelects(mainId);
		for (int i = 0; i < data.size(); i++) {
			C820M01A record = data.get(i);
			record.setDataYM(dataYM);
			service.save(record);
		}
		return result;
	}

	/**
	 * 更改勾選狀態
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public IResult changeChks(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");// 需更改日期項目
		String[] selects = params.getStringArray("selects");// 已選取項目，包含需更改
		List<C820M01A> allData = (List<C820M01A>) service.findListByMainId(
				C820M01A.class, params.getString("mainId"));
		if (Util.isNotEmpty(selects)) {
			for (int i = 0; i < allData.size(); i++) {
				C820M01A record = allData.get(i);
				for (int j = 0; j < oids.length; j++) {
					if (record.getOid().equals(oids[j])) {
						String selected = "N";
						for (int x = 0; x < selects.length; x++) {
							if (record.getOid().equals(selects[x])) {
								selected = "Y";
								break;
							}
						}
						if (!selected.equals(record.getIsSelected())) {
							record.setIsSelected(selected);
							service.save(record);
						}
					}
				}
			}
		}
		return result;
	}
}
