/* 
 *MisLNF226Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.Map;

/**
 * <pre>
 * 
 * </pre>
 * 
 */
public interface MisLNF251Service {
	
	public Map<String, Object> findEarliestOverdueDate(String custId, String dupNo);
}
