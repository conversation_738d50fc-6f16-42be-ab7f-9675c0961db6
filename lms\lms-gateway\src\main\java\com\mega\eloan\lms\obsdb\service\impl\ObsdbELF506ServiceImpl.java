package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF506Service;
import tw.com.jcs.common.Util;

@Service
public class ObsdbELF506ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF506Service {

	@Override
	public Map<String, Object> getByCntrNo(String BRNID, String cntrNo) {

		return this.getJdbc(BRNID).queryForMap("ELF506.findByCntrNo",
				new String[] { cntrNo });
	}

    @Override
    public List<Map<String, Object>> getByCustId(String BRNID, String custId, String dupNo) {
        return this.getJdbc(BRNID).queryForList("ELF506.findByCustId",
                new String[] { Util.addSpaceWithValue(custId, 10) + dupNo });
    }

	@Override
	public List<Map<String, Object>> getByAdcCaseNo(String BRNID, String adcCaseNo) {
		return this.getJdbc(BRNID).queryForList("ELF506.findByAdcCaseNo",
				new String[] { adcCaseNo });
	}

	@Override
	public int deleteByCntrNo(String BRNID, String cntrNo) {
		return this.getJdbc(BRNID).update("ELF506.deleteByCntrNo",
				new String[] { cntrNo });
	}

	@Override
	public void insert(String BRNID, String cntrNo, String cnLoanFg,
			String directFg, String stRadeFg, BigDecimal guar1rate,
			BigDecimal guar2rate, BigDecimal guar3rate, BigDecimal coll1rate,
			BigDecimal coll2rate, BigDecimal coll3rate, BigDecimal coll4rate,
			BigDecimal coll5rate, BigDecimal modifyTime, BigDecimal createTime,
			String createUnit, String modifyUnit, String documentNo,
			String iGolFlag, String cnTMUFg, String cnBusKind, String custId,
			String is722Flag, String modUnit, String docNo, BigDecimal modTime,
			BigDecimal sDate, String isBuy, String exItem, String loanTarget,
			String isType, String grntType, String grntClass,
			String othCrdType, String unionArea3, String nCnSblcFg, String nCnInstallMent,
			String prodKind, String adcCaseNo, String exceptFlag, String exceptFlagQAisY, 
			String exceptFlagQAPlus) {

		this.getJdbc(BRNID).update(
				"ELF506.insert",
				new Object[] { cntrNo, cnLoanFg, directFg, stRadeFg, guar1rate,
						guar2rate, guar3rate, coll1rate, coll2rate, coll3rate,
						coll4rate, coll5rate, modifyTime, createTime,
						createUnit, modifyUnit, documentNo, iGolFlag, cnTMUFg,
						cnBusKind, custId, is722Flag, modUnit, docNo, modTime,
						sDate, isBuy, exItem, loanTarget, isType, grntType,
						grntClass, othCrdType, unionArea3, nCnSblcFg, nCnInstallMent,
						prodKind, adcCaseNo, exceptFlag, exceptFlagQAisY, 
						exceptFlagQAPlus});
	}

	@Override
	public void updateLoanTargetByCntrNo(String BRNID, String loanTarget,
			String cntrNo) {
		this.getJdbc(BRNID).update("ELF506.updateLoanTarget",
				new Object[] { loanTarget, cntrNo });
	}
	
	@Override
	public void updateExcpetByCntrNo(String BRNID, String except, String exceptQAIsY,
			String exceptQAPlus, String cntrNo){
		this.getJdbc(BRNID).update("MIS.ELF506.updateExcpetByCntrNo",
				new Object[] { except, exceptQAIsY, exceptQAPlus, cntrNo });
	}

	@Override
	public void updateCntrNoByCntrNo(String BRNID, String newCntrNo,
			String oldCntrNo, String modifyUnit) {
		this.getJdbc(BRNID).update("ELF506.updateCntrNoByCntrNo",
				new Object[] { newCntrNo, modifyUnit, oldCntrNo });
	}

}
