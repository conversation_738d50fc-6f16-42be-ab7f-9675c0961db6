package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * LN.LNBADCR 信用負面訊息檔
 * </pre>
 * 
 * @version <ul>
 *          <li>2019/10/9,new
 *          </ul>
 */
public interface LNLNBadCRService {

	/**
	 * 查詢信用負面訊息
	 * 
	 * @param custId
	 * @return
	 */
	public List<Map<String, Object>> getNegativeCreditInfo(String custId);
	
	/**
	 * 查詢本行逾催呆資料
	 * 
	 * @return
	 */
	public List<Map<String, Object>> getOverdueAndBadDebtInfo(List<String> custIdList, List<String> dupNoList);

}
