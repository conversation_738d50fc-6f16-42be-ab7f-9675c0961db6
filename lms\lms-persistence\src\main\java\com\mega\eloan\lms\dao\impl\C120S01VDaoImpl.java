/* 
 * L120S15ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120S01VDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01V;

/** 申請資料核對表檔 **/
@Repository
public class C120S01VDaoImpl extends LMSJpaDao<C120S01V, String>
	implements C120S01VDao {

	@Override
	public C120S01V findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01V> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01V> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C120S01V> findByMainIdandItemsName(String mainId,String CustID,String Dupno,String items_Name) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "CUSTID", CustID);
		search.addSearchModeParameters(SearchMode.EQUALS, "DUPNO", Dupno);
		search.addSearchModeParameters(SearchMode.EQUALS, "items_Name", items_Name);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01V> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C120S01V> findByMainIdandCustID(String mainId,String custID,String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custID);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01V> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C120S01V> findByUniqueKey(String mainId, String ownBrId,String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return createQuery(search).getResultList();
		}
		return null;
	}

	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120S01V.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}