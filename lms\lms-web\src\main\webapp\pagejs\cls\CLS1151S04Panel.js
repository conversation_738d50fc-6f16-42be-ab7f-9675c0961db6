var PanelAction04 = {
    isInit: false,
    /**
     *頁面初始化的動作
     * */
    initAction: function(){
        _M.initItem("04");
        this.initEvent();
        this.initGrid();
    },
    /**
     *載入頁面後的動作(主要用來更新grid)
     * */
    afterAction: function(){
    
    },
    /**
     *設定完值後載入頁面後的動作
     * */
    afterSetFormDataAction: function(){
        var $boxContext = $(_M.boxContextId);
        var $CATable = $boxContext.find("#CATable");
        var $CPTable = $boxContext.find("#CPTable");
        $CATable.hide();
        $CPTable.hide();
        
        var page04Data = _M.AllFormData["04"];
        if (page04Data.CACURR && page04Data.CACURR != "") {
            $CATable.show();
            $boxContext.find("#CACURR").html(page04Data.CACURR);
            $boxContext.find("#CAAMT").html(page04Data.CAAMT);
        } else {
            $boxContext.find("#CACURR").html("");
            $boxContext.find("#CAAMT").html("");
        }
        if (page04Data.CPCURR && page04Data.CPCURR != "") {
            $CPTable.show();
            $boxContext.find("#CPCURR").html(page04Data.CPCURR);
            $boxContext.find("#CPAMT").html(page04Data.CPAMT);
        } else {
            $boxContext.find("#CPCURR").html("");
            $boxContext.find("#CPAMT").html("");
        }
        //是否顯示團貸        
        var $parentCaseNoTr = $("#parentCaseNoTr");
        $parentCaseNoTr.hide();
        //若為舊案引入擇額度種類與額度序號不可異動
        var $cls_newFcltNo = $("#cls_newFcltNo");
        var $snoKind = $("#snoKind");
        $cls_newFcltNo.show();
        var page01Data = _M.AllFormData["01"];
        //當為團貸案件其額度控管種類固定為10
        if (_M.AllFormData["docCode"] == "5") {
			$("#desc_BLAmt").show();
            $snoKind.val("10");
            $snoKind.attr("disabled", "disabled");
            $parentCaseNoTr.show();
        } else {
            if (page04Data.dataSrc != "1" && page04Data.dataSrc != "2") {
                $cls_newFcltNo.hide();
                $snoKind.attr("disabled", "disabled");
//            } else if (page01Data.younCreatYN == "Y" && page01Data.assisTypeYN == "Y") {
//                //額度明細表中[是否屬於青創]的CASE如果[青年貸款種類]為[是]時，則額度控管種類需鎖在20-信保的模式。
//                $snoKind.val("20");
//                $snoKind.attr("disabled", "disabled");
                // 青創案件已開放非信保亦可承做。
            } else if (page01Data.younCreatYN == "Y") {
				if (page01Data.gutPercent > 0 ) {					
                $snoKind.val("20");
				}
				

			} else {
                if (!_M.isReadOnly) {
                    $snoKind.removeAttr("disabled");
                }
            }
        }
        if (_M.isByHeadAndArea()) {
            $cls_newFcltNo.hide();
            $snoKind.attr("disabled", "disabled");
        }
        
        if (_M.isReadOnly) {
            $cls_newFcltNo.hide();
        }
    },
    initGrid: function(){
        //共用額度序號
        $("#gridviewConnomOther").iGrid({
            handler: _M.ghandle,
            height: 250,
            width: 600,
            sortname: 'custId',
            postData: {
                formAction: "queryL120m01a",
                rowNum: 10
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L140M01A.caseDate"],//簽案日期,
                name: 'caseDate',
                align: "center",
                width: 80,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01["L140M01A.mainPerson"],//"主要借款人",
                name: 'custName',
                width: 120,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01["page4.026"],//"案號",
                name: 'caseNo',
                width: 160,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01["L140M01A.manger"],//"經辦",
                name: 'updater',
                width: 80,
                sortable: false,
                align: "center"
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }, {
                colHeader: "mainId",
                name: 'mainId',
                hidden: true
            }]
        });
        //共用額度序號
        $("#gridviewConnomOtherSelect").iGrid({
            // 使用外層編製中和待補件的grid資料(根據文件狀態抓出這兩個資料)
            handler: _M.ghandle,
            sortname: 'cntrNo',
            sortorder: 'asc',
            colModel: [{
                colHeader: i18n.cls1151s01["L140M01A.cntrNo"],//"額度序號",
                name: 'cntrNo',
                align: "center",
                width: 80,
                sortable: true
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }]
        });
    },
    initEvent: function(){
        var $boxContext = $(_M.boxContextId);
        
        var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
        /**
         * 選擇額度序號來源
         */
        $("input[name=newFcltNoRadio]").click(function(){
        	if("new" == $(this).val()){//產生新號(適用於「新做」案件)
        		$("#tb_newFcltNo_Yes").show();
                $("#tb_newFcltNo_NO,#originalInput").hide();
        	}
        	else if ("original" == $(this).val()) {//登錄原案額度序號(適用於舊案續約及條件變更)，其他則隱藏
            	$("#tb_newFcltNo_NO,#originalInput").show();
            	$("#tb_newFcltNo_Yes").hide();
            }
            else {
            	$("#tb_newFcltNo_Yes,#originalInput,#tb_newFcltNo_NO").hide();
            }
        });
        /**
         * 額度序號給號
         */
        $("[name=contentBrankRadio]").change(function(){
            $("input[name=newFcltNoRadio][value=new]").attr("checked", true);
            switch ($(this).val()) {
                case "Yes":
                    $('#tr_contentBrankRadio_NO,#originalInput').hide();
                    break;
                case "NO":
                	$('#tr_contentBrankRadio_NO,#originalInput').show();
                    break;
            }
        });
        /**
         * 顯示變更前內容
         */
        $boxContext.find("#cls_showBeforeBt").click(function(){
        
        });
        
        /**
         * 登錄-團貸總戶案號
         */
        $boxContext.find("#cls_loginParentCaseNoBt").click(function(){
            PanelAction04.loginParentCaseNo();
        });
        
        /**
         * 查詢-團貸總戶案號
         */
        $boxContext.find("#cls_queryParentCaseNoBt").click(function(){
            _M.doAjax({
                action: "queryMisPteamapp",
                success: function(obj){
                    API.confirmMessage(obj.msg);
                }
            });
            
        });
        
        /**
         *   登錄-總額度有效期限
         */
        $boxContext.find("#L140M01LExpBDateBt").click(function(){
            var $form = $("#loginExpMonthsForm");
            $form.reset();
            $("#loginExpMonthsBox").thickbox({
                title: i18n.def.confirmTitle,
                width: 300,
                height: 200,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: _openerLockDoc == "1",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        if ($form.valid()) {
                            var value = $form.find("#expMonths").val();
                            $boxContext.find("#showExpMonths").html(i18n.cls1151s01["page4.019"].replace("{0}", value));
                            _M.AllFormData["04"]["expMonths"] = value;
                            $.thickbox.close();
                        }
                        
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
        
        
        /**
         * 額度序號 給號按鈕
         */
        $boxContext.find("#cls_newFcltNo").click(function(){
            if ($CLS1151Form04.find("#snoKind").val() == "") {
                //page4.030=額度控管種類不得為空
                return API.showMessage(i18n.cls1151s01["page4.030"]);
            }
          
          _M.verifyCntrNoAction().done(function(){
        	
            //當為團貸案直接用918給號
            if (_M.AllFormData.docCode == "5") {
                /**
                 *簽報書為團貸母戶案，額度序號給號部份
                 (1)新案直接由918給出序號
                 (2)舊案部份要讓執行下列SQL產生出名單供user勾選……..
                 */
                if (_M.AllFormData["04"]["cntrNo"]) {
                    //L140M01A.error42=額度序號已產生，是否清除額度序號？
                    API.confirmMessage(i18n.cls1151s01["L140M01A.error42"], function(b){
                        if (b) {
                            var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
                            //清除分行名稱
                            $CLS1151Form04.find("#showCntrNoName").html("");
                            //清除逾放比率
                            $CLS1151Form04.find("#npldate").val("");
                            $CLS1151Form04.find("#npl").val("");
                            $CLS1151Form04.find("#cntrNo").val("");
                            _M.AllFormData["04"]["cntrNo"] = "";
                            _M.AllFormData["04"]["npldate"] = "";
                            _M.AllFormData["04"]["cntrNo"] = "";
                        }
                    });
                } else {
                    var page04Data = _M.AllFormData["04"];
                    if (page04Data.dataSrc == "1") {
                        var cntrNo = PanelAction04.newCntrNo("918");
                        $CLS1151Form04.find("#cntrNo").val(cntrNo);
                        _M.AllFormData["04"]["cntrNo"] = cntrNo;
                    } else {
                        _M.doAjax({
                            action: "getPTEAMAPPCntrNo",
                            success: function(obj){
                                if (obj.cntrNos) {
                                    if ($.isEmptyObject(obj.cntrNos)) {
                                        //page4.031=查無額度序號！！
                                        return API.showMessage(i18n.cls1151s01["page4.031"]);
                                    }
                                    var $oldGrpcntrno = $("#selecOldCntrNoBoxDiv").find("#oldGrpcntrno");
                                    $oldGrpcntrno.setItems({
                                        item: obj.cntrNos,
                                        space: false
                                    });
                                    $("#selecOldCntrNoBox").thickbox({
                                        //L140M01A.msg036=選擇額度序號
                                        title: i18n.cls1151s01["L140M01A.msg036"],
                                        width: 200,
                                        height: 100,
                                        modal: true,
                                        readOnly: false,
                                        i18n: i18n.def,
                                        align: "center",
                                        valign: "bottom",
                                        buttons: {
                                            "sure": function(){
                                                var cntrNo = $oldGrpcntrno.val();
                                                var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
                                                if (cntrNo != "") {
                                                    _M.doAjax({
                                                        action: "saveCntrNo",
                                                        data: {
                                                            cntrNo: cntrNo
                                                        },
                                                        success: function(obj){
                                                            _M._triggerMainGrid();
                                                            $CLS1151Form04.find("#cntrNo").val(cntrNo);
                                                            _M.AllFormData["04"]["cntrNo"] = cntrNo;
                                                            $.thickbox.close();
                                                        }
                                                    });
                                                } else {
                                                    API.showMessage(i18n.cls1151s01["L140M01A.error07"]);
                                                }
                                            },
                                            "cancel": function(){
                                                $.thickbox.close();
                                            }
                                        }
                                    });
                                }
                            }
                        });
                    }
                    
                }
                
            } else {
                PanelAction04.getNewFcltNo();
            }
            
          });
            
            
        });
        
        /**
         * 共用額度序號 給號按鈕
         */
        $boxContext.find("#cls_commonNumBt").click(function(){
            $("input[name=commonNumRadio][value=commonNow]").attr("checked", true);
            $("#cls_commonNumBox").thickbox({
                title: i18n.cls1151s01["L140M01A.cntrNoCom"],//'額度共用序號',
                width: 550,
                height: 250,
                modal: true,
                readOnly: false,
                i18n: i18n.def,
                align: "center",
                valign: "bottom",
                buttons: {
                    "sure": function(){
                        var checked = $("input[name=commonNumRadio]:checked").val();
                        switch (checked) {
                            case "commonNow":
                                $.thickbox.close();
                                var commonNumSelect = function(data){
                                    _M.AllFormData["04"]["commSno"] = data.cntrNo;
                                    $("#commSno").val(data.cntrNo);
                                };//本案共用額度序號的新增
                                PanelAction04.commonNumOtherSelect( _M.CaseMainId, 'queryL140m01aCntrNo', commonNumSelect);
                                break;
                            case "commonOther":
                                $.thickbox.close();
                                PanelAction04.commonNumOther();
                                break;
                            case "clean":
                                $.thickbox.close();
                                _M.AllFormData["04"]["commSno"] = "";
                                $("#commSno").val("");
                                break;
                            default:
                                return API.showMessage(i18n.cls1151s01["L140M01A.error07"]);
                                break;
                        }
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
        
        /**
         * 登錄-借款收付彙計數
         */
        $boxContext.find("#cls_collectBT").click(function(){
            CLS_CollectAction.openCollectBox();
        });
        
        
        /**
         * 引進上月底逾期放款分析表
         */
        $boxContext.find("#cls_getNPL").click(function(){
            _M.doAjax({
                action: "queryNPL",
                data: {
                    cntrNo: $CLS1151Form04.find("#cntrNo").val()
                },
                success: function(obj){
                    $boxContext.find("#npl").val(obj.msg);
                    $boxContext.find("#npldate").val(obj.date);
                }
            });
        });
        
        /**
         * 說明-擔保授信額度調整
         */
        $boxContext.find("#cls_sayBt").click(function(){
            $("#cls_sayBox").thickbox({
                //L140M01A.say=說明
                title: i18n.cls1151s01['L140M01A.say'],
                width: 800,
                height: 150,
                modal: true,
                readOnly: false,
                buttons: API.createJSON([{
                    key: i18n.def['close'],
                    value: function(){
                        $.thickbox.close();
                    }
                }])
            });
        });
        /**
         * 登錄 -請輸入欲產生額度序號之作帳行分行代碼(三碼)
         */
        $boxContext.find("#cls_selectBranchBt").click(function(){
            API.showAllBranch({
                btnAction: function(a, b){
                    $("#newFcltNo_No_bankNum").val(b.brNo);
                    $.thickbox.close();
                }
            });
        });
        /**
         * 引進平均動用率
         */
        $boxContext.find("#cls_inculeUsePar").click(function(){
			var cntrno = $CLS1151Form04.find("#cntrNo").val();
        	if (cntrno == "") {
            	// page6.007=請先登錄現請額度與額度序號並儲存後，在執行此動作。
            return API.showMessage(i18n.cls1151s01["page6.007"]);
        	} else {
			    _M.doAjax({
                action: "inculeUsePar",
                data: {
                    cntrNo: $CLS1151Form04.find("#cntrNo").val()
                },
                success: function(obj){
                    $boxContext.find("#useParDate").val(obj.useParDate);
                    $boxContext.find("#usePar").val(obj.usePar);
                }
            });
			}
        });
        /**
         * 額度控管種類非聯貸時不顯示聯貸條件
         */
        $boxContext.find("#snoKind").change(function(){
            _M.setTab06($(this).val());
        });
        var $desp1 = $boxContext.find("#desp1");
        /** 動用期間(切換)*/
        $boxContext.find("#useDeadline").change(function(v, k){
            $desp1.val(_M.AllFormData["04"]["desp1"]);
            $desp1.removeAttr("readonly");
            $desp1.attr({
                size: "20"
            });
            if (!k) {
                $desp1.val("");
            }
            $desp1.addClass("required");
            $desp1.addClass("number");
            $desp1.datepicker('destroy');
            $desp1.hide();
            $desp1.removeClass("caseReadOnly");
            $("#desp2").hide();
            $(".removeTrue").remove();
            $("#moveDurOtfromSpan").hide();
            $("#moveDurOtApprEndSpan").hide();
            $("#useDeadline_not3_and_reUse_1").hide();
            
            var $moveDur = $("#moveDurOtFrom,#moveDurOtEnd");
            $moveDur.removeClass("date");
            $moveDur.removeClass("required");
            $moveDur.datepicker('destroy');
            
            // 選項8自核准日起~YYYY-MM-DD 使用
            var $moveDurOtApprEnd = $("#moveDurOtApprEnd");
            $moveDurOtApprEnd.removeClass("date");
            $moveDurOtApprEnd.removeClass("required");
            $moveDurOtApprEnd.datepicker('destroy');
            
            /*
             	原本長度是 2
             	但若是 "以房養老"，可能長度會是 30年*12月=360個月(長度3)
             	
             	將會上傳 MIS.ELLNSEEK, 欄位 ELF461_USEFTMN 為 Dec(3,0)
            */
            var attr_disp1 = {
                    size: "3",
                    maxlength: "3",
                    readonly: _M.isReadOnly
            };
            
            switch ($(this).val()) {
                case "0":
                    //page4.003=不再動用	
                    $desp1.show().val(i18n.cls1151s01["page4.003"]).attr({
                        maxlength: "20",
                        readonly: true
                    });
                    $desp1.addClass("caseReadOnly");
                    $desp1.removeClass("number");
                    break;
                case "1":
                    $desp1.hide();
                    $("#moveDurOtfromSpan").show();//TODO在save的時候把moveDurOtEnd、moveDurOtFrom 組起來放在moveDurOt
                    $moveDur.addClass("required").attr("readonly", _M.isReadOnly);
                    $moveDur.addClass("date");
                    if (_M.isReadOnly) {
                        $moveDur.datepicker('destroy')
                    } else {
                        $moveDur.datepicker();
                    }
                    $desp1.removeClass("required");
                    break;
                case "2":
                    //page4.004=自核准日起  page4.005=個月
                    $desp1.show().before("<span class='removeTrue'>" + i18n.cls1151s01["page4.004"] + "</span>").after("<span class='removeTrue'>" + i18n.cls1151s01["page4.005"] + "</span>").attr(attr_disp1);
                    break;
                case "3":
                    //page4.006=自簽約日起  page4.005=個月
                    $desp1.show().before("<span class='removeTrue'>" + i18n.cls1151s01["page4.006"] + "</span>").after("<span class='removeTrue'>" + i18n.cls1151s01["page4.005"] + "</span>").attr(attr_disp1);
                    break;
                case "4":
                    // 	page4.007=自首次動用日起   page4.005=個月
                    $desp1.show().before("<span class='removeTrue'>" + i18n.cls1151s01["page4.007"] + "</span>").after("<span class='removeTrue'>" + i18n.cls1151s01["page4.005"] + "</span>").attr(attr_disp1);
                    break;
                case "5":
                    //page4.008=請描述 
                    $("#desp2").show().attr({
                        size: "40",
                        maxlength: "100"
                    }).after("<span class='removeTrue'>" + i18n.cls1151s01["page4.008"] + "</span>")
                    break;
                case "6":
                    //page4.004=自核准日起  page4.032=個月內首次動撥
                    $desp1.show().before("<span class='removeTrue'>" + i18n.cls1151s01["page4.004"] + "</span>").after("<span class='removeTrue'>" + i18n.cls1151s01["page4.032"] + "</span>").attr(attr_disp1);
                    break;
                case "7":
                    //page4.006=自簽約日起  page4.032=個月內首次動撥
                    $desp1.show().before("<span class='removeTrue'>" + i18n.cls1151s01["page4.006"] + "</span>").after("<span class='removeTrue'>" + i18n.cls1151s01["page4.032"] + "</span>").attr(attr_disp1);
                    break;
                case "8":
                	//page4.004=自核准日起
                	$desp1.hide();
                	$("#moveDurOtApprEndSpan").show();//TODO在save的時候把~yyyy-MM-DD 組起來放在$desp1的val中
                	$moveDurOtApprEnd.addClass("required").attr("readonly", _M.isReadOnly);
                	$moveDurOtApprEnd.addClass("date");
                	if (_M.isReadOnly) {
                		$moveDurOtApprEnd.datepicker('destroy')
                	} else {
                		$moveDurOtApprEnd.datepicker();
                	}
                	$desp1.removeClass("required");
                	$desp1.before("<span class='removeTrue'>" + i18n.cls1151s01["page4.004"] + "</span>").attr(attr_disp1);
                	break;
                default:
                    $(".removeTrue").remove();
                    $desp1.hide();
                    break;
            }
            switch ($(this).val()) {
                case "0":
                case "1":
                case "2":
                case "4":
                case "5":
                case "6":
                case "7":
                default:
                    if($("#reUse").val() == "1"){
                         $("#useDeadline_not3_and_reUse_1").show();
                    }
                    break;
                case "3":
                    break;
            }
        });
        
        //本案未送保原因如果為4 可以自行輸入筆數
        $("#noInsuReason").change(function(v, k){
            var $inputSpan = $("#noInsuReasonOtherSpan");
            var $input = $("#noInsuReasonOther");
            $input.val(_M.AllFormData["04"]["noInsuReasonOther"]);
            //            if (!k) {
            //                $input.val(_M.AllFormData["04"]["noInsuReasonOther"]);
            //            }
            
            $("#hideMark").hide();
            switch ($(this).val()) {
                case "4":
                    $inputSpan.show();
                    $("#hideMark").show();
                    $input.attr({
                        "size": "3",
                        "maxlength": "3",
                        "class": "number"
                    });
                    break;
                case "8":
                    $inputSpan.show();
                    $input.attr({
                        "size": "20",
                        "maxlength": "100",
                        "class": ""
                    });
                    break;
                default:
                    $input.val("");
                    $inputSpan.hide();
                    break;
            }
        });

        $boxContext.find("#memo_L140M01A_printDetails").click(function(){
        	var msg0 = "第1個[]現請額度&nbsp;&nbsp;第2個[]評等資訊<br/>"
        		+"● 只有在包含2個以上的產品，才會影響列印「額度明細表」時呈現的資料。<br/>"
        		+"● 若只有1個產品，不論是否勾選，不影響呈現的資料";
        	var msg1 = "[&nbsp;]現請額度"+"<br/>"   
        		+"　　"+"現請額度金額 TWD 1,000,000元 "+"<br/>"   
        		+"[V]現請額度 => 勾選後，增加顯示產品的分項金額"+"<br/>"
        		+"　　"+"現請額度金額 TWD 1,000,000元 ( A. 700,000元 B. 400,000元)";  
        	var msg2 = "[&nbsp;]評等資訊"+"<br/>"   
    			+"　　"+"A.本案最終評等－採用OOO"+"<br/>"        		
    			+"　　"+"B.本案最終評等－採用OOO"+"<br/>"
        		+"[V]評等資訊 => 勾選後，改為印於產品資訊附表"+"<br/>"
        		+"　　"+"印於產品資訊附表";
			API.showMessage(msg0+"<br/>&nbsp;<br/>"+msg1+"<br/>&nbsp;<br/>"+msg2);
        });
    },
    /**  共用額度序號 */
    commonNumOther: function(){
        $("#commonNumOtherBox").thickbox({
            //page4.024=簽報書選擇
            title: i18n.cls1151s01["page4.024"],//'簽報書選擇',
            width: 700,
            height: 400,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var girdId = $("#gridviewConnomOther").getGridParam('selrow');
                    var data = $("#gridviewConnomOther").getRowData(girdId);
                    if (!girdId) {
                        //page4.025=請選擇一份簽報書
                        return API.showMessage(i18n.cls1151s01["page4.025"]);
                    }
                    var commonNumSelect = function(data){
                        _M.AllFormData["04"]["commSno"] = data.cntrNo;
                        $("#commSno").val(data.cntrNo);
                    };//本案共用額度序號的新增
                    $.thickbox.close();
                    PanelAction04.commonNumOtherSelect(data.mainId, 'queryL140m01aCntrNo', commonNumSelect);
                    //將選到的簽報書取mainId，再去找他現有的額度序號
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /** 
     *取得選擇簽報書額度序號 以供共用額度序號使用
     * @param {mainId} 文件編號
     * @param {String} method grid查詢的方法
     * @param {function} 程式最後執行的function
     */
    commonNumOtherSelect: function(mainId, method, callback){
        $("#gridviewConnomOtherSelect").jqGrid("setGridParam", {//重新設定grid需要查到的資料
            postData: {
                formAction: method,
                mainId: mainId,
                itemType: "1",
				cntrNo:$("#cntrNo").val()
            },
            search: true
        }).trigger("reloadGrid");
        
        $("#commonNumOtherSelectBox").thickbox({
            //L140M01A.msg036=選擇額度序號
            title: i18n.cls1151s01["L140M01A.msg036"],
            width: 550,
            height: 300,
            modal: true,
            readOnly: false,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var girdId = $("#gridviewConnomOtherSelect").getGridParam('selrow');
                    var data = $("#gridviewConnomOtherSelect").getRowData(girdId);
                    if (!girdId) {
                        return API.showMessage(i18n.cls1151s01["L140M01A.error06"]);
                    }
                    PanelAction04.commonNum_One(data, callback);
                    $.thickbox.close();
                    //將選到的簽報書取oid，再去找他現有的額度序號
                
                
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 共用額度序號共用
     * @param {Object} data
     * @param {Object} callback
     */
    commonNum_One: function(data, callback){
        if (typeof(callback) == 'function') {
            callback(data);
        } else {
            //L140M01a.error04=請實作方法
            CommonAPI.showMessage(i18n.lms1405s02["L140M01a.error04"]);
        }
    },
    /**
     * 登錄-團貸總戶案號
     */
    loginParentCaseNo: function(){
        var $form = $("#loginParentCaseNoForm");
        $form.reset();
        var parentCaseNoObj = $form.find("#parentCaseNo");
        parentCaseNoObj.hide();
        $form.find("[name=loginParentCaseNoType]").change(function(){
            if ($(this).val() == "1") {
                parentCaseNoObj.show();
            } else {
                parentCaseNoObj.hide();
            }
        })
        $("#loginParentCaseNoBox").thickbox({
            title: i18n.def.confirmTitle,
            width: 300,
            height: 200,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                        $.thickbox.close();
                        var value = $form.find("[name=loginParentCaseNoType]:checked").val();
                        if (value == "1") {
                            _M.doAjax({
                                action: "queryMisPteamappIsExist",
                                data: {
                                    loanMasterNo: parentCaseNoObj.val()
                                },
                                success: function(obj){
                                    if (obj.msg) {
                                        return API.showErrorMessage(obj.msg);
                                    } else {
                                        $("#loanMasterNo").html(DOMPurify.sanitize(parentCaseNoObj.val()));
                                        _M.AllFormData["04"]["loanMasterNo"] == parentCaseNoObj.val();
                                        _M.AllFormData["04"]["cntrNo"] == parentCaseNoObj.val();
                                        $("#cntrNo").val(parentCaseNoObj.val());
                                    }
                                }
                            });
                        } else {
                            var loanMasterNo = PanelAction04.newCntrNo("918", "1", true);
                            $("#loanMasterNo").html(loanMasterNo);
                            _M.AllFormData["04"]["loanMasterNo"] == loanMasterNo;
                            _M.AllFormData["04"]["cntrNo"] == loanMasterNo;
                            $("#cntrNo").val(loanMasterNo);
                        }
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 額度序號給號
     */
    getNewFcltNo: function(){
        PanelAction04.openCntrNoBox();
    },
    /** 
     * 預約額度序號
     * @param {Object} cntrNos 額度序號物件
     */
    openCntrNoSelectBox: function(cntrNos){
        var $boxContext = $(_M.boxContextId);
        $boxContext.find("#newFcltNoSelect").setItems({
            item: cntrNos
        });
        $boxContext.find("#newFcltNoSelectBox").thickbox({
            //L140M01A.cntrNo=額度序號
            title: i18n.cls1151s01["L140M01A.cntrNo"],
            width: 250,
            height: 150,
            modal: true,
            i18n: i18n.def,
            readOnly: false,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                
                    var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
                    var value = $("#newFcltNoSelect").val();
                    if (value == "") {
                        //grid_selector=請選擇資料
                        return API.showErrorMessage(i18n.def['grid_selector']);
                    }
					var obj = PanelAction04.queryOriginalCntrNo(value, "2");
                    if (!$.isEmptyObject(obj) && obj.cntrNo) {
                        $CLS1151Form04.find("#cntrNo").val(obj.cntrNo);
                        _M.AllFormData["04"]["cntrNo"] = obj.cntrNo;
                        $boxContext.find("#showCntrNoName").html(obj.ownBrName);
                        $.thickbox.close();
						
						$.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    
    /** 額度序號thickbox  */
    openCntrNoBox: function(){
        var $boxContext = $(_M.boxContextId);        
        //點開初始化 2024/1/22 授審黃啟瑋科長移除預設值
        $boxContext.find("#newFcltNoBox").find("input[name=contentBrankRadio]").attr("checked", false);
        //請選擇額度序號來源
        $boxContext.find("#newFcltNoBox").find("input[name=newFcltNoRadio]").attr("checked", false);
        $boxContext.find("#newFcltNo_No_bankNum").val("");
        //產生新號區塊
        $boxContext.find("#originalInput,#tb_newFcltNo_NO").hide();
        $boxContext.find("#tr_contentBrankRadio_NO").hide();
        $boxContext.find("#tb_newFcltNo_Yes").hide();
        $boxContext.find("#originalText").val("");
        
        $boxContext.find("#newFcltNoBox").thickbox({
            //L140M01A.cntrNo=額度序號
            title: i18n.cls1151s01["L140M01A.cntrNo"],
            width: 600,
            height: 300,
            modal: true,
            i18n: i18n.def,
            readOnly: false,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                	var $div = $("#newFcltNoBoxDiv");
                	//請選擇額度序號來源  new=產生新號(適用於「新做」案件),  original=登錄原案額度序號, clean=清除額度序號
                    var checked = $div.find("input[name=newFcltNoRadio]:checked").val();
                	//本額度之作帳分行(帳務管理行)是否同為簽報分行? //Yes=是, NO=否
                    var checkedtype = $div.find("input[name=contentBrankRadio]:checked").val();
                    //手打輸入分行代號
                    var checkedtypeNO = $div.find("#newFcltNo_No_bankNum").val();
                    
                    var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
                    //當畫面上已經有額度序號 不能再給號
                    var cntrNo = $CLS1151Form04.find("#cntrNo").val();
                    if ($.trim(cntrNo) != "" && checked != "clean") {
                        //L140M01A.error41=額度序號已產生，不得再重新給號！！
                        return API.showMessage(i18n.cls1151s01["L140M01A.error41"]);
                    }
                    if(checked == "new"){//new=產生新號(適用於「新做」案件)
                    	if (checkedtype == "Yes") {//Yes=是，由簽報行統籌額度管理及帳務還本付息事項        
							//當為給新號需先檢查預約額度檔中是否已有，預約額度序號
                            _M.doAjax({
                                action: "queryCntrNoBy442",
                                data: {
                                    proPerty: $("#proPerty").val(),
                                    riskArea: _M.AllFormData["01"]["riskArea"] || ""
                                },
                                success: function(obj){
                                    var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
                                    /** { 
                                 * 	count:筆數
                                 * 	cntrNos: 額度序號list
                                 * 	msg : 顯示限額控管訊息
                                 * 	}*/
                                    if (obj.count > 0) {
                                        //L140M01A.error11=是否要引進預約額度檔之額度序號？
                                        API.confirmMessage(i18n.cls1151s01["L140M01A.error11"], function(b){
                                            if (b) {
                                                //新增一個預約額度序號的
                                                PanelAction04.openCntrNoSelectBox(obj.cntrNos);
                                            } else {
                                                if (obj.msg && obj.msg != "") {
                                                    return API.showErrorMessage(obj.msg);
                                                } else {
                                                    $.thickbox.close();
                                                    //當不使用預約額度序號
                                                    //當沒有預約額度序號
                                                    var cntrNo = PanelAction04.newCntrNo(_M.AllFormData["01"]["ownBrId"], "5");
                                                    
                                                    $CLS1151Form04.find("#cntrNo").val(cntrNo);
                                                    _M.AllFormData["04"]["cntrNo"] = cntrNo;
                                                    
                                                }
                                            }
                                        });
                                    } else if (obj.msg && obj.msg != "") {
                                        return API.showErrorMessage(obj.msg);
                                    } else {
                                        $.thickbox.close();
                                        //當沒有預約額度序號
                                        var cntrNo = PanelAction04.newCntrNo(_M.AllFormData["01"]["ownBrId"], "5");
                                        $CLS1151Form04.find("#cntrNo").val(cntrNo);
                                        _M.AllFormData["04"]["cntrNo"] = cntrNo;
                                        
                                    }
                                }
                            });
                        }else if (checkedtype == "NO") {//否，請輸入作帳分行代號(三碼)，將另由作帳行統籌額度管理及帳務還本付息事項
                        	$.thickbox.close();
                        	//先檢查分行類別，若類別為國內則需要輸入DBU,OBU
							_M.doAjax({
								action: "queryBankNum",//檢查是否有這個分行代號
								data: {//把資料轉成json
									ownBrId: checkedtypeNO
								},
								success: function(obj){
									API.confirmMessage(obj.ownBrName, function(b){
										if (b) {
											_M.doAjax({
												action: "queryCntrNoBy442",
												data: {
													proPerty: $("#proPerty").val(),
													riskArea: _M.AllFormData["01"]["riskArea"] || ""
												},
												success: function(obj){
													var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
													/** { 
													 * 	count:筆數
													 * 	cntrNos: 額度序號list
													 * 	msg : 顯示限額控管訊息
													 * 	}*/
													if (obj.count > 0) {
														//L140M01A.error11=是否要引進預約額度檔之額度序號？
														API.confirmMessage(i18n.cls1151s01["L140M01A.error11"], function(b){
															if (b) {
																//新增一個預約額度序號的
																PanelAction04.openCntrNoSelectBox(obj.cntrNos);
															} else {
																if (obj.msg && obj.msg != "") {
																	return API.showErrorMessage(obj.msg);
																} else {
																	$.thickbox.close();
																	//當不使用預約額度序號
																	//當沒有預約額度序號
																	var cntrNo = PanelAction04.newCntrNo(checkedtypeNO, "5");
																	
																	$CLS1151Form04.find("#cntrNo").val(cntrNo);
																	_M.AllFormData["04"]["cntrNo"] = cntrNo;
																	
																}
															}
														});
													} else if (obj.msg && obj.msg != "") {
														return API.showErrorMessage(obj.msg);
													} else {
														//當沒有預約額度序號
														var cntrNo = PanelAction04.newCntrNo(checkedtypeNO, "5");
														$CLS1151Form04.find("#cntrNo").val(cntrNo);
														_M.AllFormData["04"]["cntrNo"] = cntrNo;
													}
												}
											});
										}
									});
								}
							});
                        }
                    }else if(checked == "original"){//original=登錄原案額度序號
						var originalText = $div.find("#originalText").val().toUpperCase();
						var obj = PanelAction04.queryOriginalCntrNo(originalText, "1");
                        if (!$.isEmptyObject(obj) && obj.cntrNo) {
                            var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
                            $CLS1151Form04.find("#cntrNo").val(obj.cntrNo);
                            _M.AllFormData["04"]["cntrNo"] = obj.cntrNo;
                            $CLS1151Form04.find("#showCntrNoName").html(obj.ownBrName);
                            //當舊案輸入完成則額度性質不提供修改
                            _M.AllFormData["04"].dataSrc = "6";
                            $CLS1151Form04.find("#dataSrc").val("6");
                            $CLS1151Form04.find("#cls_newFcltNo").hide();
                            $CLS1151Form04.find("#snoKind").attr("disabled", "disabled");
                            _M.btnInculeAccount();
                            $.thickbox.close();
                        }
                    }else if(checked == "clean"){//額度序號來源, clean=清除額度序號
						$.thickbox.close();
                        var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
                        //清除分行名稱
                        $CLS1151Form04.find("#showCntrNoName").html("");
                        //清除逾放比率
                        $CLS1151Form04.find("#npldate").val("");
                        $CLS1151Form04.find("#npl").val("");
                        $CLS1151Form04.find("#cntrNo").val("");
                        _M.AllFormData["04"]["cntrNo"] = "";
                        _M.AllFormData["04"]["npldate"] = "";
                        _M.AllFormData["04"]["cntrNo"] = "";
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**   
     * 選擇DBU OBU 視窗
     * @param {String } brId 輸入的分行代碼
     */
    selectDbuOrObu: function(brId){
        var $boxContext = $(_M.boxContextId);
        $boxContext.find("[name=DBUorOBURadio][value=1]").attr("checked", true);
        $boxContext.find("#selectDbuOrObuBox").thickbox({
            title: "",
            width: 150,
            height: 100,
            modal: true,
            readOnly: false,
            align: "center",
            i18n: i18n.def,
            valign: "bottom",
            buttons: {
                "sure": function(){
                    //seletValue DBU=1 ,OBU=4
                    var seletValue = $boxContext.find("[name=DBUorOBURadio]:checked").val();
                    if (!seletValue || seletValue == "") {
                        //grid.selrow=請先選擇一筆資料。
                        return API.showMessage(i18n.def["grid.selrow"]);
                    }
                    var cntrno = PanelAction04.newCntrNo(brId, seletValue);
                    var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
                    $CLS1151Form04.find("#cntrNo").val(cntrno);
                    _M.AllFormData["04"]["cntrNo"] = cntrno;
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     *	查詢原案額度序號
     * @param {String } originalText 原案額度序號
     * @param {String } justSave 1.為查詢舊額度序號2.儲存預約額度序號 3,聯行攤貸比例
     * return {Object}cntrNo 額度序號",ownBrName 額度序號前三碼分行名稱
     */
    queryOriginalCntrNo: function(originalText, justSave){
        //驗證舊有額度序號規則
        ///\w{3}\d{4}[0-9|X]\d{4}/		
        if (!originalText.match(/\w{12}/)) {
            //L140M01A.message68=額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)
            return API.showMessage(i18n.cls1151s01["L140M01A.message68"]);
        }
        var $CLS1151Form04 = $("#" + _M.formPrefix + "04");
        var queryObj = {};
        _M.doAjax({
            async: false,
            action: "checkCntrno",
            data: {
                cntrNo: originalText,
                justSave: justSave || "1",
                snoKind: $CLS1151Form04.find("#snoKind").val()
            },
            success: function(obj){
                queryObj.cntrNo = originalText.toUpperCase();
                queryObj.ownBrName = obj.ownBrName;
                if (justSave != "3") {
                    _M._triggerMainGrid();
                }
            }
        });
        return queryObj;
    },
    /**  額度序號給號 
     *
     * @param {String} ownBrId 分行號碼
     * @param {String} unitCode 區部別 DBU=1 OBU=4 海外=5
     * @param {boolean} doSave 是否儲存
     * return 額度序號
     */
    newCntrNo: function(ownBrId, unitCode, doSave){
        if (doSave == undefined) {
            doSave = true;
        }
        var number;
        _M.doAjax({
            async: false,
            action: "queryNewCntrNo",
            data: {
                ownBrId: ownBrId,
                unitCode: unitCode,
                classCD: "0",
                doSave: doSave
            },
            success: function(obj){
                number = obj.cntrNo;
                if (doSave) {
                    _M._triggerMainGrid();
                    $("#showCntrNoName").html(obj.branchName);
                }
                
            }//close success
        });
        return number;
    }
};


//收付彙計數相關程式
var CLS_CollectAction = {
    //form 的名稱
    $form: $("#CLS_L140M01KForm"),
    isInit: false,
    init: function(){
        if (!this.isInit) {
            this.initGrid();
            this.initItem();
            this.isInit = true;
        } else {
            this.grid.jqGrid("setGridParam", {
                postData: {
                    tabFormMainId: _M.tabMainId
                },
                search: true
            }).trigger("reloadGrid");
        }
    },
    initItem: function(){
        var $div = CLS_CollectAction.$form.find("[itemType]");
        var allKey = [];
        $div.each(function(){
            allKey.push($(this).attr("itemType"));
        });
        _M.codetypeItem = API.loadCombos(allKey);
        $div.each(function(){
            var $obj = $(this);
            var itemType = $obj.attr("itemType");
            if (itemType) {
                var format = $obj.attr("itemFormat") || "{value} - {key}";
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: _M.codetypeItem[itemType],
                    format: format,
                    sort: $obj.attr("itemSort") || "asc",
                    size: $obj.attr("itemSize")
                });
            }
        });
    },
    /**
     * 開啟收付彙計數 grid視窗
     */
    openCollectBox: function(){
        this.init();
        $("#collectBox").thickbox({
            //L140M01A.collectPay=借款收付彙計數 
            title: i18n.cls1151s01['L140M01A.collectPay'],
            width: 640,
            height: 370,
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "newData": function(){
                    CLS_CollectAction.addCollectBox();
                },
                "del": function(){
                    var $gridviewCollect = CLS_CollectAction.grid;
                    var ids = $gridviewCollect.getGridParam('selarrrow');
                    if (ids == "") {
                        //TMMDeleteError=請先選擇需修改(刪除)之資料列
                        return API.showMessage(i18n.def["TMMDeleteError"]);
                    }
                    
                    // confirmDelete=是否確定刪除?
                    API.confirmMessage(i18n.def["confirmDelete"], function(b){
                        if (b) {
                            var gridIDList = [];
                            for (var i in ids) {
                                gridIDList[i] = $gridviewCollect.getRowData(ids[i]).oid;
                            }
                            _M.doAjax({
                                action: "delCollect",
                                data: {//把資料轉成json
                                    oidList: gridIDList
                                },
                                success: function(data){
                                    CLS_CollectAction.getDrc(data);
                                    $gridviewCollect.trigger('reloadGrid');
                                }//close success
                            });
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 新增 收付彙計數視窗
     * @param {Object} callValue grid 顯示
     * @param {Object} setting grid　設定
     * @param {Object} data 欄位資料
     */
    addCollectBox: function(callValue, setting, data){
        var oid = (data && data.oid) || "";
        _M.doAjax({
            action: "queryCollect",
            data: {
                oid: oid
            },
            success: function(obj){
                CLS_CollectAction.openAddCollectBox();
                CLS_CollectAction.$form.injectData(obj);
                //把這份文件的oid放上去
                CLS_CollectAction.$form.attr("oid", oid);
            }
        }); //close ajax
    },
    /**
     * 開啟新增 收付彙計數視窗
     */
    openAddCollectBox: function(){
        $("#CLS_addCollectBox").thickbox({
            //L140M01A.collectPay=借款收付彙計數 
            title: i18n.cls1151s01['L140M01A.collectPay'],
            width: 500,
            height: 200,
            modal: true,
            readOnly: false,
            i18n: i18n.def,
            open: function(){
                CLS_CollectAction.$form.find("select,input").val("");
            },
            close: function(){
            },
            buttons: {
                "saveData": function(){
                    var $form = CLS_CollectAction.$form;
                    if (!$form.valid()) {
                        return false
                    }
                    var cpCurr = $.trim($("#CPCurr", $form).val());
                    var cpPmt = $.trim($("#CollectPay", $form).val());
                    var caCurr = $.trim($("#CACurr", $form).val());
                    var caAmt = $.trim($("#CollectAccept", $form).val());
                    
                    if (cpCurr == "" && cpPmt == "" && caCurr == "" && caAmt == "") {
                        //page4.023=請輸入
                        return API.showMessage(i18n.cls1151s01["page4.023"]);
                    }
                    
                    if ((cpCurr != "" && cpPmt == "") || (caCurr != "" && caAmt == "")) {
                        //L140M01A.error33=「{0}」幣別已填，金額不得為空白
                        return API.showMessage(i18n.cls1151s01["L140M01A.error33"].replace("「{0}」", ""));
                    }
                    
                    if ((cpCurr == "" && cpPmt != "") || (caCurr == "" && caAmt != "")) {
                        //L140M01A.error34=「{0}」金額已填，幣別不得為空白
                        return API.showMessage(i18n.cls1151s01["L140M01A.error34"].replace("「{0}」", ""));
                    }
                    _M.doAjax({
                        action: "saveCollect",
                        formId: CLS_CollectAction.$form.attr("id"),
                        data: {
                            //L140M01KForm: JSON.stringify(CLS_CollectAction.$form.serializeData()),
                            oid: CLS_CollectAction.$form.attr("oid")
                        },
                        success: function(data){
                            CLS_CollectAction.getDrc(data);
                            CLS_CollectAction.grid.trigger('reloadGrid');
                            $.thickbox.close();
                        }
                    }); //close ajax
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 組畫面上的字串
     * @param {Object} obj 收付彙計數描述
     */
    getDrc: function(obj){
        var $boxContext = $(_M.boxContextId);
        if (obj.CACURR && obj.CACURR != "") {
            $boxContext.find("#CATable").show();
            $boxContext.find("#CACURR").html(obj.CACURR);
            $boxContext.find("#CAAMT").html(obj.CAAMT);
        } else {
            $boxContext.find("#CATable").hide();
        }
        if (obj.CPCURR && obj.CPCURR != "") {
            $boxContext.find("#CPTable").show();
            $boxContext.find("#CPCURR").html(obj.CPCURR);
            $boxContext.find("#CPAMT").html(obj.CPAMT);
        } else {
            $boxContext.find("#CPTable").hide();
        }
    },
    //收付彙計數grid
    grid: null,
    initGrid: function(){
        this.grid = $("#cls_gridviewCollect").iGrid({
            handler: _M.ghandle,
            sortorder: 'asc',
            sortname: "createTime",
            height: 235,
            rowNum: 10,
            multiselect: true,
            hideMultiselect: false,
            postData: {
                formAction: "queryCollect",
                tabFormMainId: _M.tabMainId
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L782M01A.applyCurr"] + "(" + i18n.cls1151s01["L140M01A.Pay"] + ")", //L782M01A.applyCurr =幣別(付),
                name: 'CPCurr',
                align: "center",
                width: 80,
                sortable: true,
                formatter: 'click',
                onclick: CLS_CollectAction.addCollectBox
            
            }, {
                colHeader: i18n.cls1151s01["L140M01h.cp1Fee"] + "(" + i18n.cls1151s01["L140M01A.Pay"] + ")", //L140M01h.cp1Fee=金額(付),
                name: 'CollectPay',
                align: "right",
                formatter: GridFormatter.number['addComma'],
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L782M01A.applyCurr"] + "(" + i18n.cls1151s01["L140M01A.Accept"] + ")", //L782M01A.applyCurr =幣別(收)
                name: 'CACurr',
                align: "center",
                width: 80,
                sortable: true,
                formatter: 'click',
                onclick: CLS_CollectAction.addCollectBox
            }, {
                colHeader: i18n.cls1151s01["L140M01h.cp1Fee"] + "(" + i18n.cls1151s01["L140M01A.Accept"] + ")", //L140M01h.cp1Fee=金額(收)
                name: 'CollectAccept',
                align: "right",
                formatter: GridFormatter.number['addComma'],
                width: 80,
                sortable: true
            }, {
                colHeader: "oid",
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = CLS_CollectAction.grid.getRowData(rowid);
                CLS_CollectAction.addCollectBox(null, null, data);
            }
        });
    }
    
    
};

_M.pageInitAcion["04"] = PanelAction04;
