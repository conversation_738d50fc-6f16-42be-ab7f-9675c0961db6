/*
 *JC Software.
 * copyright @jcs.com.tw 2003~2006. all right reserved.
 */
package tw.com.jcs.common;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * 這個程式是用來做阿拉伯數字和中文數字之間的轉換.
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR>
 * @version 1.0
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public class NumConverter {

    private static final Logger logger = LoggerFactory.getLogger(JsonMapper.class);

    /**
     * 不能產生instance
     */
    private NumConverter() {
    }

    private interface 面積比率 {
        public final static String M2to坪 = "0.3025";
        public final static String M2to英畝 = "0.000247";
        public final static String M2to平方英呎 = "10.7639";
        public final static String 坪toM2 = "3.3058";
        public final static String 坪to英畝 = "0.00082";
        public final static String 坪to平方英呎 = "35.565";
        public final static String 平方英呎toM2 = "0.092899";
        public final static String 平方英呎to坪 = "0.0281";
        public final static String 平方英呎to英畝 = "0.000022";
        public final static String 英畝toM2 = "4046.87";
        public final static String 英畝to坪 = "1224.12";
        public final static String 英畝to平方英呎 = "43560";
    }

    private class 面積單位 {
        public static final int 坪 = 1;
        public static final int 平方公尺 = 2;
        public static final int 平方英呎 = 3;
        public static final int 英畝 = 4;
    }

    /**
     * 阿拉伯數字轉中文大寫金錢數字 123 → 壹佰貳拾參；壹貳參肆伍陸柒捌玖零拾佰仟萬億
     * 
     * @param Number
     *            要轉換的阿拉伯數字整數
     */
    public static String toChineseNumberFull(Object Number) {
        return toChineseNumberFull(String.valueOf(Number));
    }

    /**
     * 阿拉伯數字轉中文大寫金錢數字 123 → 壹佰貳拾參；壹貳參肆伍陸柒捌玖零拾佰仟萬億
     * 
     * @param Number
     *            要轉換的阿拉伯數字整數
     */
    public static String toChineseNumberFull(int Number) {
        return toChineseNumberFull(String.valueOf(Number));
    }

    /**
     * 阿拉伯數字轉中文大寫金錢數字 123 → 壹佰貳拾參；壹貳參肆伍陸柒捌玖零拾佰仟萬億
     * 
     * @param Number
     *            要轉換的阿拉伯數字整數
     */
    public static String toChineseNumberFull(double Number) {
        return toChineseNumberFull(String.valueOf(Number));
    }

    /**
     * 阿拉伯數字轉中文大寫金錢數字 123 → 壹佰貳拾參；壹貳參肆伍陸柒捌玖零拾佰仟萬億
     * 
     * @param Number
     *            要轉換的阿拉伯數字字串
     */
    public static String toChineseNumberFull(String Number) {
        Number = numberTrim(Number);
        if (isNumeric(Number)) {
            String result = "";
            for (int loop_index = 0; loop_index < Number.length(); loop_index++) {
                String number = Number.substring(Number.length() - loop_index - 1, Number.length() - loop_index);
                switch (loop_index) {
                case 0:
                    if (number.equals("0")) {
                        result = result + "";
                    } else {
                        result = numberToChineseFull(number) + result;
                    }
                    break;
                case 1:
                    if (number.equals("0")) {
                        if (!result.equals("")) {
                            result = "零" + result;
                        }
                    } else {
                        result = numberToChineseFull(number) + "拾" + result;
                    }
                    break;
                case 2:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChineseFull(number) + "佰" + result;
                    }
                    break;
                case 3:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChineseFull(number) + "仟" + result;
                    }
                    break;
                case 4:
                    if (number.equals("0")) {
                        result = "萬" + result;
                    } else {
                        result = numberToChineseFull(number) + "萬" + result;
                    }
                    break;
                case 5:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChineseFull(number) + "拾" + result;
                    }
                    break;
                case 6:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零") && !result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChineseFull(number) + "佰" + result;
                    }
                    break;
                case 7:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零") && !result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChineseFull(number) + "仟" + result;
                    }
                    break;
                case 8:
                    if (result.substring(0, 1).equals("萬")) {
                        result = result.substring(1);
                        if (result.length() > 0 && !result.substring(0, 1).equals("零")) {
                            result = "零" + result;
                        }
                    }
                    if (number.equals("0")) {
                        result = "億" + result;
                    } else {
                        result = numberToChineseFull(number) + "億" + result;
                    }
                    break;
                case 9:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("億")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChineseFull(number) + "拾" + result;
                    }
                    break;
                case 10:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零") && !result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChineseFull(number) + "佰" + result;
                    }
                    break;
                case 11:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零") && !result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChineseFull(number) + "仟" + result;
                    }
                    break;
                default:
                    break;
                }
            }
            if (result.length() >= 2 && result.substring(0, 2).equals("壹拾")) {
                // result = result.substring(1);
            }
            return result;
        } else
            return null;
    }

    /**
     * 阿拉伯數字轉中文金錢數字 123 → 一百二十三；一二三四五六七八九零十百千萬億
     * 
     * @param Number
     *            要轉換的阿拉伯數字整數
     */
    public static String toChineseNumber(Object Number) {
        return toChineseNumber(String.valueOf(Number));
    }

    /**
     * 阿拉伯數字轉中文金錢數字 123 → 一百二十三；一二三四五六七八九零十百千萬億
     * 
     * @param Number
     *            要轉換的阿拉伯數字整數
     */
    public static String toChineseNumber(int Number) {
        return toChineseNumber(String.valueOf(Number));
    }

    /**
     * 阿拉伯數字轉中文金錢數字 123 → 一百二十三；一二三四五六七八九零十百千萬億
     * 
     * @param Number
     *            要轉換的阿拉伯數字字串
     */
    public static String toChineseNumber(String Number) {
        Number = numberTrim(Number);
        String zeroBack = "";
        if (Number.indexOf(".") != -1) {
            String[] temp = Number.split("\\.");
            Number = temp[0];
            boolean result = false;
            String numberTemp[] = new String[temp[1].length()];
            for (int i = temp[1].length(); i >= 1; i--) {
                if (result) {
                    numberTemp[i - 1] = temp[1].substring(i - 1, i);
                } else {
                    if (temp[1].subSequence(i - 1, i).equals("0")) {
                        numberTemp[i - 1] = "";
                    } else {
                        numberTemp[i - 1] = temp[1].substring(i - 1, i);
                        result = true;
                    }
                }
            }
            for (String temp2 : numberTemp) {
                zeroBack = zeroBack + temp2;
            }
        }
        if (isNumeric(Number)) {
            String result = "";
            for (int loop_index = 0; loop_index < Number.length(); loop_index++) {
                String number = Number.substring(Number.length() - loop_index - 1, Number.length() - loop_index);
                switch (loop_index) {
                case 0:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.equals("")) {
                                // 第二以上位數為零者,例:02,102...
                                result = result + "零"; // modified by Eva on
                                                       // 2005/05/23 : 預設為零
                            }
                        }
                    } else {
                        result = numberToChinese(number) + result;
                    }
                    break;
                case 1:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.equals("")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "十" + result;
                    }
                    break;
                case 2:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "百" + result;
                    }
                    break;
                case 3:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "千" + result;
                    }
                    break;
                case 4:
                    if (number.equals("0")) {
                        result = "萬" + result;
                    } else {
                        result = numberToChinese(number) + "萬" + result;
                    }
                    break;
                case 5:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "十" + result;
                    }
                    break;
                case 6:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零") && !result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "百" + result;
                    }
                    break;
                case 7:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零") && !result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "千" + result;
                    }
                    break;
                case 8:
                    if (result.substring(0, 1).equals("萬")) {
                        result = result.substring(1);
                        if (result.length() > 0 && !result.substring(0, 1).equals("零")) {
                            result = "零" + result;
                        }
                    }
                    if (number.equals("0")) {
                        result = "億" + result;
                    } else {
                        result = numberToChinese(number) + "億" + result;
                    }
                    break;
                case 9:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("億")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "十" + result;
                    }
                    break;
                case 10:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零") && !result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "百" + result;
                    }
                    break;
                case 11:
                    if (number.equals("0")) {
                        if (result.length() > 0) {
                            if (!result.substring(0, 1).equals("零") && !result.substring(0, 1).equals("萬")) {
                                result = "零" + result;
                            }
                        }
                    } else {
                        result = numberToChinese(number) + "千" + result;
                    }
                    break;
                default:
                    break;
                }
            }
            if (result.length() >= 2 && result.substring(0, 2).equals("一十")) {
                result = result.substring(1);
            }
            if (zeroBack != null && zeroBack.length() > 0) {
                result = result + "點";
                int len = zeroBack.length();
                for (int i = 0; i < len; i++) {
                    if (zeroBack.substring(i, i + 1).equals("0")) {
                        result = result + "零";
                    } else {
                        result = result + numberToChinese(zeroBack.substring(i, i + 1));
                    }
                }
            }
            return result;
        } else
            return null;
    }

    /**
     * 將阿拉伯數字轉成大寫國字數字字串 1234567890 → 壹貳參肆伍陸柒捌玖零
     * 
     * @param Number
     *            要轉換的阿拉伯數字字串
     */
    public static String numberToChineseFull(String Number) {
        if (isNumeric(Number)) {
            return numberToChineseFullLoop(Number);
        } else {
            return null;
        }
    }

    /**
     * 將阿拉伯數字轉成大寫國字數字字串 1234567890 → 壹貳參肆伍陸柒捌玖零
     * 
     * @param Number
     *            要轉換的阿拉伯數字字串
     */
    public static String numberToChineseFull(int Number) {
        String num = String.valueOf(Number);
        if (isNumeric(num)) {
            return numberToChineseFullLoop(num);
        } else {
            return null;
        }
    }

    /**
     * 將阿拉伯數字轉換成小寫國字數字字串 1234567890 → 一二三四五六七八九０
     * 
     * @param Number
     *            要轉換的阿拉伯數字字串
     */
    public static String numberToChinese(String Number) {
        if (isNumeric(Number)) {
            return numberToChineseLoop(Number);
        } else {
            return null;
        }
    }

    /**
     * 將阿拉伯數字轉換成小寫國字數字字串 1234567890 → 一二三四五六七八九０
     * 
     * @param Number
     *            要轉換的阿拉伯數字
     */
    public static String numberToChinese(int Number) {
        String num = String.valueOf(Number);
        if (isNumeric(num)) {
            return numberToChineseLoop(num);
        } else {
            return null;
        }
    }

    /**
     * 將大寫國字數字轉換成阿拉伯數字一二三四五六七八九０ → 1234567890 只能處理整數而且傳回值是double
     * 
     * @param CFullNumber
     *            要轉換的大寫國字數字字串
     */
    public static double chineseFullToNumber(String CFullNumber) {
        double result = 0;
        int sign = 1;

        if (CFullNumber.equals(null) || CFullNumber.length() <= 0 || !isCNumeric(CFullNumber)) {
            return result;
        } else if (CFullNumber.substring(0, 1).equals("負")) {
            sign = -1;
            CFullNumber = CFullNumber.substring(1);
        }
        for (int loop_index = 0; loop_index < CFullNumber.length(); loop_index++) {
            if (CFullNumber.substring(loop_index, loop_index + 1).equals("壹"))
                result = result * 10 + 1;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("貳"))
                result = result * 10 + 2;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("參"))
                result = result * 10 + 3;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("肆"))
                result = result * 10 + 4;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("伍"))
                result = result * 10 + 5;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("陸"))
                result = result * 10 + 6;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("柒"))
                result = result * 10 + 7;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("捌"))
                result = result * 10 + 8;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("玖"))
                result = result * 10 + 9;
            else if (CFullNumber.substring(loop_index, loop_index + 1).equals("拾"))
                result = result * 10;
        }
        result = result * sign;
        return result;
    }

    /**
     * 將小寫國字數字轉換成阿拉伯數字一二三四五六七八九０ → 1234567890 只能處理整數而且傳回值是double
     * 
     * @param CNumber
     *            要轉換的小寫國字數字字串
     */
    public static double chineseToNumber(String CNumber) {
        double result = 0;
        int sign = 1;

        if (CNumber.equals(null) || CNumber.length() <= 0 || !isCNumeric(CNumber)) {
            return result;
        } else if (CNumber.substring(0, 1).equals("負")) {
            sign = -1;
            CNumber = CNumber.substring(1);
        }
        for (int i = 0; i < CNumber.length(); i++) {
            String ch = CNumber.substring(i, i + 1);
            if (ch.equals("一"))
                result = result * 10 + 1;
            else if (ch.equals("二"))
                result = result * 10 + 2;
            else if (ch.equals("三"))
                result = result * 10 + 3;
            else if (ch.equals("四"))
                result = result * 10 + 4;
            else if (ch.equals("五"))
                result = result * 10 + 5;
            else if (ch.equals("六"))
                result = result * 10 + 6;
            else if (ch.equals("七"))
                result = result * 10 + 7;
            else if (ch.equals("八"))
                result = result * 10 + 8;
            else if (ch.equals("九"))
                result = result * 10 + 9;
            else if (ch.equals("０") || ch.equals("○"))
                result = result * 10 + 0;
            else if (ch.equals("十") && CNumber.length() == 1)
                result = 10;
            else if (ch.equals("十") && CNumber.length() == 2)
                result = result * 10 + 0;
        }
        result = result * sign;
        return result;
    }

    /**
     * 將所有要轉換成小寫國字數字的數字先轉成英文字元在丟進迴圈運算
     * 
     * @param Number
     *            要轉換的阿拉伯數字字串
     */
    private static String numberToChineseLoop(String Number) {
        String result = "";
        for (int loop_index = 0; loop_index < Number.length(); loop_index++) {
            switch (Number.charAt(loop_index)) {
            case 48:
                result += "０";
                break;
            case 45:
                result += "負";
                break;
            case 49:
                result += "一";
                break;
            case 50:
                result += "二";
                break;
            case 51:
                result += "三";
                break;
            case 52:
                result += "四";
                break;
            case 53:
                result += "五";
                break;
            case 54:
                result += "六";
                break;
            case 55:
                result += "七";
                break;
            case 56:
                result += "八";
                break;
            case 57:
                result += "九";
                break;
            }
        }
        return result;
    }

    /**
     * 檢查字串是不是文數字，如果是就傳回true，如果不是就傳回false
     * 
     * @param number
     *            要檢查的數字字串
     */
    public static boolean isNumeric(String number) {
        if (number == null)
            return false;
        try {
            Double.parseDouble(number);
            return true;
        } catch (NumberFormatException nfe) {
            return false;
        }
    }

    /**
     * 檢查字串是不是中文數字，如果是就傳回true，如果不是就傳回false
     * 
     * @param Number
     *            要檢查的中文數字字串
     */
    public static boolean isCNumeric(String Number) {
        boolean checked = false;
        try {
            for (int i = 0; i < Number.length(); i++) {
                String CNumber = Number.substring(i, i + 1);
                String[] CNumberSet = { "一", "二", "三", "四", "五", "六", "七", "八", "九", "０", "○", "十" };
                for (int j = 0; j < CNumberSet.length; j++) {
                    if (CNumber.equals(CNumberSet[j])) {
                        checked = true;
                        break;
                    } else
                        checked = false;
                }
            }
            if (checked == false) {
                throw new NumberFormatException();
            } else {
                return true;
            }
        } catch (NumberFormatException nfe) {
            return false;
        }
    }

    /**
     * trim掉0
     * 
     * @param Number
     *            要轉換的阿拉伯數字字串
     * @return
     */
    private static String numberTrim(String Number) {
        if (!"0".equals(Number)) {
            while (Number.charAt(0) == 48) {
                Number = Number.substring(1);
            }
        }
        return Number;
    }

    /**
     * 將所有要轉換大寫國字數字的數字字串先轉成英文字元在丟進迴圈運算
     * 
     * @param Number
     *            要轉換的阿拉伯數字字串
     */
    private static String numberToChineseFullLoop(String Number) {
        String result = "";
        for (int loop_index = 0; loop_index < Number.length(); loop_index++) {
            switch (Number.charAt(loop_index)) {
            case 48:
                result += "零";
                break;
            case 45:
                result += "負";
                break;
            case 49:
                result += "壹";
                break;
            case 50:
                result += "貳";
                break;
            case 51:
                result += "參";
                break;
            case 52:
                result += "肆";
                break;
            case 53:
                result += "伍";
                break;
            case 54:
                result += "陸";
                break;
            case 55:
                result += "柒";
                break;
            case 56:
                result += "捌";
                break;
            case 57:
                result += "玖";
                break;
            }
        }
        return result;
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(BigDecimal input) {
        if (input == null) {
            return "";
            // input = BigDecimal.ZERO;
        } else {
            return addComma(String.valueOf(input));
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(BigDecimal input, String type) {
        if (input == null) {
            return "";
            // input = BigDecimal.ZERO;
        } else {
            return addComma(String.valueOf(input), type);
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(Object input, String type) {
        if (input == null) {
            return "";
            // input = BigDecimal.ZERO;
        } else {
            return addComma(String.valueOf(input), type);
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(Object input) {
        if (input == null) {
            return "";
            // input = BigDecimal.ZERO;
        } else {
            return addComma(String.valueOf(input));
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(int input) {
        return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(Integer input) {
        if (input == null) {
            return "";
            // input = 0;
        } else {
            return addComma(String.valueOf(input));
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(float input) {
        return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(Float input) {
        if (input == null) {
            return "";
            // input = new Float(0.0);
        } else {
            return addComma(String.valueOf(input));
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(long input) {
        return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(Long input) {
        if (input == null) {
            return "";
            // input = new Long(0);
        } else {
            return addComma(String.valueOf(input));
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(double input) {
        return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(Double input) {
        if (input == null) {
            return "";
            // input = new Double(0.0);
        } else {
            return addComma(String.valueOf(input));
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45 971225 Add by Crash
     */
    public static String addComma(double input, String type) {
        return addComma(String.valueOf(input), type);
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。. EX : 4121324.45 => 4,121,324.45 971225 Add by Crash
     */
    public static String addComma(Double input, String type) {
        if (input == null) {
            return "";
            // input = new Double(0.0);
        } else {
            return addComma(String.valueOf(input), type);
        }
        // return addComma(String.valueOf(input));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。 傳入參數：該欄位。 輸出數值：會直接將該欄位的?，加上comma。 EX : 4121324.45 => 4,121,324.45
     */
    public static String addComma(String input) {
        return addComma(input, null);
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。 傳入參數：該欄位。 傳入參數：格式。 ex ,##0.######0 輸出數值：會直接將該欄位的?，加上comma。 EX : 4121324.45 => 4,121,324.45 970923 增加先把 comma 取代避免重複加到
     */
    public static String addComma(String input, String type) {
        if (input == null || input.length() == 0) {
            return "";
        } else {
            input = input.replaceAll(",", "");
        }

        if (type == null || type.length() == 0) {
            type = ",##0.#####";
        }

        try {
            DecimalFormat dollarFormat = new DecimalFormat(type);
            double a = Double.parseDouble(input);
            String resultStr = dollarFormat.format(a);

            return resultStr;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    public static void main(String args[]) {
        // System.out.println(NumConverter.addComma("1234567.00", 13, 2));
    }

    /**
     * 功能說明：將數字欄位加上Comma(即,)。 傳入參數：該欄位。 傳入參數：格式。 ex ,##0.######0 輸出數值：會直接將該欄位的?，加上comma。 EX : 4121324.45 => 4,121,324.45 970923 增加先把 comma 取代避免重複加到
     */
    public static String addComma(String input, int integer, int fraction) {
        if (fraction == 0) {
            return addComma(input, null);
        } else {
            StringBuffer sb = new StringBuffer(",##0.");
            for (int i = 0; i < fraction; i++) {
                sb.append("0");
            }
            return addComma(input, sb.toString());
        }
    }

    /**
     * 傳入帶有comma的文字，去掉Comma，並傳回浮點數的數字型態.
     * 
     * @param number
     *            String 帶有comma的字串。
     * @return double 傳出double型態的數字。
     */
    public static double delCommaDouble(String number) {
        if (number == null || number.length() == 0)
            return 0.d;
        String num = number.replaceAll(",", "");
        try {
            double dnum = Double.parseDouble(num);
            return dnum;
        } catch (NumberFormatException nfe) {
            return 0.d;
        }
    }

    /**
     * 傳入帶有comma的文字，去掉Comma，並傳回浮點數的數字型態.
     * 
     * @param number
     *            String 帶有comma的字串。
     * @return float 傳出浮點數型態的數字(小數點2位，四捨五入)。
     */
    public static float delCommaFloat(String number) {
        if (number == null || number.length() == 0)
            return 0;
        String num = number.replaceAll(",", "");
        try {
            float fnum = Float.parseFloat(num);
            return fnum;
        } catch (NumberFormatException nfe) {
            return 0;
        }
    }

    /**
     * 傳入帶有comma的文字，去掉Comma，並傳回長整數的數字型態.
     * 
     * @param number
     *            String 帶有comma的字串。
     * @return long 傳出長整數型態的數字。
     */
    public static long delComma(String number) {
        if (number == null || number.length() == 0)
            return 0;
        String num = number.replaceAll(",", "");
        try {
            long fnum = Long.parseLong(num);
            return fnum;
        } catch (NumberFormatException nfe) {
            return 0;
        }
    }

    /**
     * 傳入帶有comma的文字，去掉Comma，並傳回整數的數字型態.
     * 
     * @param number
     *            String 帶有comma的字串。
     * @return int 傳出整數型態的數字。
     */
    public static int delCommaInteger(String number) {
        if (number == null || number.length() == 0)
            return 0;
        String num = number.replaceAll(",", "");
        try {
            int fnum = Integer.parseInt(num);
            return fnum;
        } catch (NumberFormatException nfe) {
            return 0;
        }
    }

    /**
     * 傳入帶有comma的文字，去掉Comma，並傳回文字型態.
     * 
     * @param number
     *            String 帶有comma的字串。
     * @return String 傳字串型態的數字。
     */
    public static String delCommaString(String number) {
        if (number == null || number.length() == 0)
            return null;
        String num = number.replaceAll(",", "");
        return num;
    }

    /**
     * 轉換主機數字欄位為指定的數字格式,並轉換非數字字元為0.
     * 
     * ex:9(7)V99 如: 123456700 >>1234567.00
     * 
     * @param num
     *            要轉換的數字字串
     * @param d1
     *            數字格式[小數點前的位數]
     * @param d2
     *            數字格式[小數點後的位數]
     * @return String 格式好的數字
     */
    public static String convertToDecimalFormat(String num, int d1, int d2) {
        if (num == null)
            return null;
        if (num.trim().length() == 0)
            return "0";
        if (num.length() < (d1 + d2))
            return "0";

        boolean bNegative = false;

        // 已知有些數字中有"{" 字元
        // num = Util.replaceAll(num, "{", "0");

        char c = num.charAt(num.length() - 1);
        if (isNumeric(String.valueOf(c)) == false) {
            num = num.substring(0, num.length() - 1) + mappingNumericChar(c);
        }
        // 負數 :最末碼 = JKLMNOPQR.................................
        if ((c >= 74 && c <= 82) || c == '}') {
            bNegative = true;
        }

        StringBuffer sb = new StringBuffer();
        sb.append(num.substring(0, d1));
        sb.append(".");
        sb.append(num.substring(d1, d1 + d2));
        return (bNegative ? "-" : "") + sb.toString();
    }

    /** 將利率 S99V9(07) 的數字格式化 pattern=00.0000000 */
    public static String formatInterest(String num) {
        DecimalFormat interestFormat = new DecimalFormat("00.0000000");
        float f = Float.parseFloat(num);
        return interestFormat.format(f);
    }

    // 轉換主機碼:(含有正負符號)
    private static String mappingNumericChar(char c) {
        // ==主機碼:正數==================================
        // {ABCDEFGHI
        // 0123456789
        // ==主機碼:負數==================================
        // JKLMNOPQR}
        // 1234567890
        // ===========================================
        if (c >= 48 && c <= 57) {
            // 數字..............................
            return String.valueOf(c);
        }
        String num = "0";
        switch (c) {
        case '{':
        case '}':
            num = "0";
            break;
        case 'A':
        case 'J':
            num = "1";
            break;
        case 'B':
        case 'K':
            num = "2";
            break;
        case 'C':
        case 'L':
            num = "3";
            break;
        case 'D':
        case 'M':
            num = "4";
            break;
        case 'E':
        case 'N':
            num = "5";
            break;
        case 'F':
        case 'O':
            num = "6";
            break;
        case 'G':
        case 'P':
            num = "7";
            break;
        case 'H':
        case 'Q':
            num = "8";
            break;
        case 'I':
        case 'R':
            num = "9";
            break;
        }
        return num;
    }

    /**
     * 將百分比數字轉為利率 S99V9(07) 的格式[上傳電文用]. <br/>
     * pattern=[第一碼正負號]000000000 <br/>
     * ex: 3.52 ->[ 035200000] ,-1.84 ->[-184000000]
     */
    public static String to444InterestRate(String s) {
        return to444InterestRate(Double.parseDouble(s));
    }

    /**
     * 將百分比數字轉為利率 S99V9(07) 的格式[上傳電文用]. <br/>
     * pattern=[第一碼正負號]000000000 <br/>
     * ex: 3.52 ->[ 035200000] ,-1.84 ->[-184000000]
     */
    public static String to444InterestRate(double d) {
        if (Math.abs(d) >= 100)
            return " 000000000";
        DecimalFormat interestFormat = new DecimalFormat("000000000");
        return ((d < 0) ? "-" : " ") + interestFormat.format(Math.abs(d) * 10000000);
    }

    /**
     * 面積換算.
     * 
     * @param area
     *            轉換的面積
     * @param before
     *            原單位別[1:坪,2:平方公尺,3:平方英呎,4:英畝]
     * @param after
     *            要轉換的單位別[1:坪,2:平方公尺,3:平方英呎,4:英畝]
     * @return double 轉換後的面積
     */
    public static BigDecimal areaConvert(BigDecimal area, int before, int after) {
        return areaConvert(area, before, after, false);
    }

    /**
     * 面積換算.
     * 
     * @param area
     *            轉換的面積
     * @param before
     *            原單位別[1:坪,2:平方公尺,3:平方英呎,4:英畝]
     * @param after
     *            要轉換的單位別[1:坪,2:平方公尺,3:平方英呎,4:英畝]
     * @param format
     *            是否要小數點以下2位:四捨五入
     * @return double 轉換後的面積
     */
    public static double areaConvert(double area, int before, int after, boolean format) {
        return areaConvert(new BigDecimal(Double.toString(area)), before, after, format).doubleValue();
    }

    /**
     * 面積換算.
     * 
     * @param area
     *            轉換的面積
     * @param before
     *            原單位別[1:坪,2:平方公尺,3:平方英呎,4:英畝]
     * @param after
     *            要轉換的單位別[1:坪,2:平方公尺,3:平方英呎,4:英畝]
     * @param format
     *            是否要小數點以下2位:四捨五入
     * @return double 轉換後的面積
     */
    public static BigDecimal areaConvert(BigDecimal area, int before, int after, boolean format) {
        if (before < 面積單位.坪 || before > 面積單位.英畝) {
            logger.error("NumConverter warnning->參數錯誤:請指定原單位別!");
        }
        if (after < 面積單位.坪 || after > 面積單位.英畝) {
            logger.error("NumConverter warnning->參數錯誤:請指定要轉換的單位別!");
        }
        BigDecimal _area = BigDecimal.ZERO;
        switch (before) {
        case 面積單位.坪:
            switch (after) {
            case 面積單位.坪:
                _area = area;
                break;
            case 面積單位.平方公尺:
                _area = area.multiply(new BigDecimal(面積比率.坪toM2));
                break;
            case 面積單位.平方英呎:
                _area = area.multiply(new BigDecimal(面積比率.坪to平方英呎));
                break;
            case 面積單位.英畝:
                _area = area.multiply(new BigDecimal(面積比率.坪to英畝));
                break;
            default:
                break;
            }
            break;
        case 面積單位.平方公尺:
            switch (after) {
            case 面積單位.坪:
                _area = area.multiply(new BigDecimal(面積比率.M2to坪));
                break;
            case 面積單位.平方公尺:
                _area = area;
                break;
            case 面積單位.平方英呎:
                _area = area.multiply(new BigDecimal(面積比率.M2to平方英呎));
                break;
            case 面積單位.英畝:
                _area = area.multiply(new BigDecimal(面積比率.M2to英畝));
                break;
            default:
                break;
            }
            break;
        case 面積單位.平方英呎:
            switch (after) {
            case 面積單位.坪:
                _area = area.multiply(new BigDecimal(面積比率.平方英呎to坪));
                break;
            case 面積單位.平方公尺:
                _area = area.multiply(new BigDecimal(面積比率.平方英呎toM2));
                break;
            case 面積單位.平方英呎:
                _area = area;
                break;
            case 面積單位.英畝:
                _area = area.multiply(new BigDecimal(面積比率.平方英呎to英畝));
                break;
            default:
                break;
            }
            break;
        case 面積單位.英畝:
            switch (after) {
            case 面積單位.坪:
                _area = area.multiply(new BigDecimal(面積比率.英畝to坪));
                break;
            case 面積單位.平方公尺:
                _area = area.multiply(new BigDecimal(面積比率.英畝toM2));
                break;
            case 面積單位.平方英呎:
                _area = area.multiply(new BigDecimal(面積比率.英畝to平方英呎));
                break;
            case 面積單位.英畝:
                _area = area;
                break;
            default:
                break;
            }
            break;
        default:
            break;
        }

        if (format) {
            _area = _area.setScale(2, RoundingMode.HALF_UP);
        }
        return _area;
    }
}
