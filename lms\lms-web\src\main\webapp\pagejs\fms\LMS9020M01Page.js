var pageAction = {
    handler: 'lms9020formhandler',
    grid: null,
    build: function(){
        pageAction.grid = $("#gridStopDetail").iGrid({
            // localFirst: true,
            handler: 'lms9020gridhandler',
            height: 400,
            action: "queryL902s01a",
            sortname: 'custId',
            sortorder: 'asc',
            postData: {
                mainId: responseJSON.mainId
            },
            rowNum: 15,
            rownumbers: true,
            multiselect: true,
            colModel: [{
                colHeader: "oid",
                name: 'oid',
                hidden: true
                // 是否隱藏
            }, {
                colHeader: "mainId", // mainId
                hidden: true,// 是否隱藏
                name: 'mainId' // col.id
            }, {
                colHeader: i18n.lms9020m01["subGrid.index1"], // 客戶統編
                align: "left",
                width: 50,
                sortable: true,
                name: 'custId' // col.id
            }, {
                colHeader: i18n.lms9020m01["subGrid.index2"], // 重覆序號
                align: "center",
                width: 10,
                sortable: true,
                name: 'dupNo' // col.id
            }, {
                colHeader: i18n.lms9020m01["subGrid.index3"], // 客戶名稱
                align: "left",
                width: 150,
                sortable: true,
                name: 'custName' // col.id
             }, {
                colHeader: i18n.lms9020m01["subGrid.index7"], // 修改案號
                align: "left",
                width: 70,
                sortable: true,
                name: 'documentNo' // col.id   
             }, {
                colHeader: i18n.lms9020m01["subGrid.index4"], //修改人員
                align: "left",
                width: 50,
                sortable: true,
                name: 'updater' // col.id
            }, {
                colHeader: i18n.lms9020m01["subGrid.index5"], // 修改時間
                align: "left",
                width: 90,
                sortable: true,
                name: 'updateTime' // col.id        
            }, {
                colHeader: i18n.lms9020m01["subGrid.index6"], // 刪除時間
                align: "left",
                width: 90,
                sortable: true,
                name: 'deletedTime' // col.id
            }],
            ondblClickRow: function(rowid){
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openDetail(data);
            }
        });
        //build buttons
        //儲存
        $("#buttonPanel").find("#btnSave").click(function(){
            // confirmRun=是否確定執行此功能?
            // 停權待覆核					
            pageAction.saveMain();
        });
        
         
        
    },
    /**
     * 查詢detail
     */
    init: function(){
        $.form.init({
            formHandler: pageAction.handler,
            formPostData: {
                formAction: "queryL902m01a",
                mainId: responseJSON.mainId
            },
            loadSuccess: function(respons){
                $("#formStopDetail1").setData(respons.formStopDetail1);
				
				if(!$("#formStopDetail1").find("#deletedTime").val()){
					$("#showClearDeletedTime").hide();
				}else{
					$("#showClearDeletedTime").show();
				}
				
				
            }
        });
    },
    /**
     * 開啟detail
     */
    openDetail: function(data){
        var $formStopDetail2 = $("#formStopDetail2");
        
        if (data) {
            // btn.index1=新增
            if (data.statusFlag != i18n.lms9020m01["btn.index1"]) {
                $formStopDetail2.find(".addNeed").attr("disabled", true);
            }
        }
        else {
            $formStopDetail2.find(".addNeed").attr("disabled", false);
            $formStopDetail2.find("#modlifyMons").attr("disabled", false);
        }
        // 開始進行查詢明細
        $.ajax({
            handler: pageAction.handler,
            action: 'queryL902s01a',
            data: {
                oid: (data) ? data.oid : ""
            },
            success: function(result){
                $formStopDetail2.setData(result.formStopDetail2, true);
                
                // html.index15=停權明細
                //開視窗
                $("#stopDetail2").thickbox({
                    title: i18n.lms9020m01["html.index15"],
                    width: 800,
                    height: 200,
                    modal: true,
                    //align : 'center',
                    //valign: 'bottom',
                    i18n: i18n.def,
                    buttons: {
                        'saveData': function(){
                            if ($formStopDetail2.valid()) {
                                pageAction.saveDetail(data);
                            }
                        },
                        'del': function(){
                            if (data) {
                                pageAction.delDetail(data.oid);
								
                            }
                            else {
                                // msg.alert3=資料尚未儲存，請執行「儲存」後再執行本功能。
                                CommonAPI.showMessage(i18n.lms9020m01["msg.alert3"]);
                                return;
                            }
                        },
                        'close': function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            }
        });
    },
	/**
     * 儲存detail
     */
    saveMain: function(data){
        $.ajax({
            handler: pageAction.handler,
            action: 'saveL902m01a',
            data: {
                oid: responseJSON.oid,
                mainId: responseJSON.mainId,
                formStopDetail1: JSON.stringify($("#formStopDetail1").serializeData())
            },
            success: function(result){
                API.triggerOpener();
                
            }
        });
    },
    /**
     * 儲存detail
     */
    saveDetail: function(data){
        $.ajax({
            handler: pageAction.handler,
            action: 'saveL902s01a',
            data: {
                oid: (data) ? data.oid : "",
                mainId: responseJSON.mainId,
                formStopDetail2: JSON.stringify($("#formStopDetail2").serializeData())
            },
            success: function(result){
                pageAction.reloadGrid();
                $.thickbox.close();
                $.thickbox.close();
                CommonAPI.showMessage(result.NOTIFY_MESSAGE);
            }
        });
    },
    /**
     * 新增detail
     */
    addDetail: function(){
        pageAction.openDetail();
    },
    /**
     * 刪除detail(從Grid刪除)
     */
    delGridDetail: function(){
        var list = pageAction.getRowData();
        if (list == "") {
            // msg.alert1=尚未選取資料!
            CommonAPI.showMessage(i18n.lms9020m01["msg.alert1"]);
            return;
        }
		//msg.alert5=(欄位「修改案號」非空白之明細只能標記刪除，不會真正刪除)
		CommonAPI.confirmMessage(i18n.def["confirmDelete"]+i18n.lms9020m01["msg.alert5"], function(b){
            if (b) {
                $.ajax({
		            handler: pageAction.handler,
		            action: 'delL902s01a',
		            data: {
		                list: list
		            },
		            success: function(result){
		                pageAction.reloadGrid();
		            }
		        });
            }
        });
        
    },
    /**
     * 刪除detail
     */
    delDetail: function(oid){
        var list = oid;
        if (list == "") {
            // msg.alert1=尚未選取資料!
            CommonAPI.showMessage(i18n.lms9020m01["msg.alert1"]);
            return;
        }
		alert("A");
		// confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
		            handler: pageAction.handler,
		            action: 'delL902s01a',
		            data: {
		                list: list
		            },
		            success: function(result){
		                pageAction.reloadGrid();
		                $.thickbox.close();
						$.thickbox.close();
		                CommonAPI.showMessage(result.NOTIFY_MESSAGE);
		            }
		        });
            }
        });
				
        
    },
    /**
     * 取消刪除detail
     */
    undoGridDetail: function(){
		var $formStopDetail1 = $("#formStopDetail1"); 
        var list = pageAction.getRowData();
        if (list == "") {
            // msg.alert1=尚未選取資料!
            CommonAPI.showMessage(i18n.lms9020m01["msg.alert1"]);
            return;
        }
        $.ajax({
            handler: pageAction.handler,
            action: 'undoDelL902s01a',
            data: {
				oid: responseJSON.oid,
                mainId: responseJSON.mainId,
				peNo : $formStopDetail1.find('#peNo').val(),
                list: list
            },
            success: function(result){
                pageAction.reloadGrid();
            }
        });
    },
	/**
     * 清除刪除時間(取消集團刪除)
     */
    clearDeletedTime: function(){
        var $formStopDetail1 = $("#formStopDetail1"); 
		$formStopDetail1.find('#deletedTime').val('');
    },
	/**
     * 引進戶名
     */
    applyCustName: function(){
        var $formStopDetail2 = $("#formStopDetail2"); 
		var custId =  $formStopDetail2.find("#custId").val();
		var dupNo =  $formStopDetail2.find("#dupNo").val();
		if(!custId || !dupNo){
			// msg.alert2=客戶統編或重覆序號不得空白
            CommonAPI.showMessage(i18n.lms9020m01["msg.alert2"]);
            return;
		}
        $.ajax({
            handler: pageAction.handler,
            action: 'applyCustName',
            data: {
                custId: custId,
				dupNo:dupNo
            },
            success: function(result){
				if(result.noData =="Y"){
					// msg.alert4=0024客戶檔無此客戶統編資料
		            CommonAPI.showMessage(i18n.lms9020m01["msg.alert4"]);
		            return;
				}else{
					$formStopDetail2.find("#custName").val(result.custName);
				}
                
            }
        });
    },
    //依照不同文件狀態控制唯讀
    setReadOnly: function(auth){
        //停權編製中可編輯，其他都不可編輯
        if (responseJSON.mainDocStatus == "LEH") {
            //編製中且沒被鎖定
            if (auth.Modify && !thickboxOptions.readOnly) {
                responseJSON["readOnly"] = false;
                $(this).find("button").show();
            }
            else {
                responseJSON["readOnly"] = true;
                var $formStopDetail2 = $("#formStopDetail2");
                $formStopDetail2.readOnlyChilds(true);
                $("#formStopBtn").find("button").hide();
                thickboxOptions.readOnly = true;
                //顯示上方主要標題按鈕
                $("#buttonPanel :button").show();
                $("#buttonPanel").find("#btnSend").hide();
            }
        }
        else {
            //非編製中
            responseJSON["readOnly"] = true;
            var $formStopDetail2 = $("#formStopDetail2");
            $formStopDetail2.readOnlyChilds(true);
            $("#formStopBtn").find("button").hide();
            thickboxOptions.readOnly = true;
            //顯示上方主要標題按鈕
            $("#buttonPanel :button").show();
            $("#buttonPanel").find("#btnSend").hide();
        }
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var rows = pageAction.grid.getGridParam('selarrrow');
        var list = "";
        var sign = ",";
        for (var i = 0; i < rows.length; i++) {
            //將所有已選擇的資料存進變數list裡面
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = pageAction.grid.getRowData(rows[i]);
                list += ((list == "") ? "" : sign) + data.oid;
            }
        }
        return list;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        }
        else {
            pageAction.grid.trigger('reloadGrid');
        }
    }
}
$(document).ready(function(){
    var auth = (responseJSON ? responseJSON.Auth : {}); //權限
    pageAction.build();
    pageAction.init();
    pageAction.setReadOnly(auth);
});
