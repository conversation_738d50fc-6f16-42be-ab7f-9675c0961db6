package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisELCUS21Service {

	/**
	 * 取得客戶地址(for 稽核工作底稿，授信額度檔(昨日)MIS.MISLN20 cancel date is null)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> getCustAddressForLas(String custId,
			String dupNo);
	
	/**
     *<li>00-客戶統一編號
     *<li>01-重複序號
     *<li>02-通訊地址(縣市)
     *<li>03-通訊地址(區鄉鎮市)
     *<li>04-通訊地址(村里)
     *<li>05-通訊地址(其餘部分)
     *<li>06-最新異動日期(西元年 YYYY-MM-DD)
	 */
	final String[] ElCus21Cols = { "CUSTID", "DUPNO", "CITYR", "TOWNR",
			"LEER", "ADDRR", "TXDT" };

	/**
	 * 取得客戶通訊地址資料
	 * 
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @return List<Map<String, Object>>
	 */
	public Map<String, Object> findByIdDupNo(String custId, String dupNo);
	
	/**
	 * 取得地址組合
	 * 
	 * @param data
	 * 			客戶資料
	 * @return String
	 */
	public String getAddr(Map<String, Object> data);
}
