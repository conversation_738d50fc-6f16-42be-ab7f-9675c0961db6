package tw.com.jcs.auth.impl;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.sql.DataSource;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import tw.com.jcs.auth.AuthException;
import tw.com.jcs.auth.AuthQueryFactory;
import tw.com.jcs.auth.util.StringUtil;

/**
 * <pre>
 * AuthQueryFactoryImpl
 * </pre>
 * 
 * @since 2022年12月21日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月21日
 *          <li>2024年10月21日 修正CHECKMARX
 *          </ul>
 */
public class AuthQueryFactoryImpl implements AuthQueryFactory {

    private JdbcTemplate jdbc;

    private Document xml;
    private Map<String, String> context;
    private String userQuery;
    private String deptQuery;
    private String roleAuthQuery;
    private String userRoleQuery;
    private String codeQuery;
    private String branchQuery;
    private String roleQuery; // add by fantasy 2012/02/29
    private String docAuthQuery; // add by fantasy 2012/04/13

    /**
     * 設定DataSource
     * 
     * @param dataSource
     */
    @Resource
    public void setDataSource(DataSource dataSource) {
        jdbc = new JdbcTemplate(dataSource);
    }

    /**
     * 設定config檔
     * 
     * @param configLocation
     */
    public void setConfigLocation(String configLocation) {
        try {
            if (configLocation.startsWith("/")) {
                configLocation = configLocation.substring(1);
            }
            InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(configLocation);
            // 修正Improper_Restriction_of_Stored_XXE_Ref
            // 針對XML的Factory class設定禁用DTD
            DocumentBuilderFactory dfactory = DocumentBuilderFactory.newInstance();
            dfactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            dfactory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            dfactory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            xml = dfactory.newDocumentBuilder().parse(is);
            is.close();

            init();
        } catch (Exception e) {
            throw new AuthException("Can't read config file : " + e, e);
        }
    }

    public AuthQueryFactoryImpl() {
        super();
    }

    /**
     * 初始化參數
     * 
     * @throws XPathExpressionException
     */

    void init() throws XPathExpressionException {
        context = new HashMap<String, String>();
        XPathFactory factory = XPathFactory.newInstance();
        XPath path = factory.newXPath();
        context.put("system", path.evaluate("//system", xml));

        NodeList list = (NodeList) path.evaluate("//table/*", xml, XPathConstants.NODESET);
        for (int i = 0; i < list.getLength(); ++i) {
            Node node = list.item(i);
            context.put("table." + node.getNodeName(), StringUtil.trim(node.getTextContent()));
        }

        Node node = (Node) path.evaluate("//query", xml, XPathConstants.NODE);

        userQuery = StringUtil.trim(path.evaluate("./user", node));
        deptQuery = StringUtil.trim(path.evaluate("./department", node));
        userRoleQuery = StringUtil.trim(path.evaluate("./user-role", node));
        roleAuthQuery = StringUtil.trim(path.evaluate("./role-auth", node));
        codeQuery = StringUtil.trim(path.evaluate("./code", node));
        branchQuery = StringUtil.trim(path.evaluate("./branch", node));
        roleQuery = StringUtil.trim(path.evaluate("./role", node)); // add by fantasy 2012/02/29
        docAuthQuery = StringUtil.trim(path.evaluate("./doc", node)); // add by fantasy 2012/02/29
    }

    private static final Pattern pattern = Pattern.compile("\\$\\{([\\S]+)\\}");

    /**
     * 組SQL字串
     * 
     * @param sql
     *            sql字串
     * @param evalContext
     * @return
     */
    private String eval(String sql, Map<String, String> evalContext) {
        StringBuilder buf = new StringBuilder();
        Matcher m = pattern.matcher(sql);
        int p = 0;
        while (m.find(p)) {
            buf.append(sql.substring(p, m.start()));
            String key = m.group(1);
            String value = evalContext.get(key);
            if (value == null) {
                buf.append(m.group());
            } else {
                buf.append(value);
            }
            p = m.end();
        }
        buf.append(sql.substring(p));
        return buf.toString();
    }

    /**
     * 執行query
     * 
     * @param query
     * @param handler
     *            {@link org.springframework.jdbc.core.RowCallbackHandler}
     */
    private void exec(String query, RowCallbackHandler handler) {
        jdbc.query(query, handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#execUserQuery(org.springframework.jdbc.core.RowCallbackHandler)
     */
    @Override
    public void execUserQuery(RowCallbackHandler handler) {
        exec(eval(userQuery, context), handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#execDeptQuery(org.springframework.jdbc.core.RowCallbackHandler)
     */
    @Override
    public void execDeptQuery(RowCallbackHandler handler) {
        exec(eval(deptQuery, context), handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#execRoleAuthQuery(org.springframework.jdbc.core.RowCallbackHandler)
     */
    @Override
    public void execRoleAuthQuery(RowCallbackHandler handler) {
        exec(eval(roleAuthQuery, context), handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#execUserRoleQuery(org.springframework.jdbc.core.RowCallbackHandler)
     */
    @Override
    public void execUserRoleQuery(RowCallbackHandler handler) {
        exec(eval(userRoleQuery, context), handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#execCodeQuery(org.springframework.jdbc.core.RowCallbackHandler)
     */
    @Override
    public void execCodeQuery(RowCallbackHandler handler) {
        exec(eval(codeQuery, context), handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#execBranchQuery(org.springframework.jdbc.core.RowCallbackHandler)
     */
    @Override
    public void execBranchQuery(RowCallbackHandler handler) {
        exec(eval(branchQuery, context), handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#execRoleQuery(org.springframework.jdbc.core.RowCallbackHandler)
     */
    // add by fantasy 2012/02/29
    @Override
    public void execRoleQuery(RowCallbackHandler handler) {
        exec(eval(roleQuery, context), handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#execDocAuthQuery(org.springframework.jdbc.core.RowCallbackHandler)
     */
    // add by fantasy 2012/04/13
    @Override
    public void execDocAuthQuery(RowCallbackHandler handler) {
        exec(eval(docAuthQuery, context), handler);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthQueryFactory#getSystemType()
     */
    @Override
    public String getSystemType() {
        return StringUtil.trim((String) context.get("system"));
    }
}
