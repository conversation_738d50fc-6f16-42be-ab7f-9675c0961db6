/* 
 * C160S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 擔保品資料明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C160S01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seqNo", "refmainId" }))
public class C160S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 額度明細表來源mainId **/
	@Size(max = 32)
	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refmainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQNO", columnDefinition = "DECIMAL(5,0)")
	private Integer seqNo;

	/**
	 * 分行代碼
	 * <p/>
	 * 擔保品所屬分行
	 */
	@Size(max = 3)
	@Column(name = "BRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String branch;

	/** 鑑價分行 **/
	@Size(max = 3)
	@Column(name = "ESTBRN", length = 3, columnDefinition = "CHAR(3)")
	private String estBrn;

	/** 鑑估日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ESTDATE", columnDefinition = "DATE")
	private Date estDate;

	/** 統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 擔保品編號 **/
	@Size(max = 9)
	@Column(name = "COLLNO", length = 9, columnDefinition = "VARCHAR(09)")
	private String collNo;

	/** 擔保品大類 **/
	@Size(max = 2)
	@Column(name = "COLLTYP1", length = 2, columnDefinition = "CHAR(2)")
	private String collTyp1;

	/** 擔保品小類 **/
	@Size(max = 2)
	@Column(name = "COLLTYP2", length = 2, columnDefinition = "CHAR(2)")
	private String collTyp2;

	/** 擔保品文件狀態 **/
	@Size(max = 3)
	@Column(name = "DOCSTATUS", length = 3, columnDefinition = "VARCHAR(03)")
	private String docStatus;

	/**
	 * 擔保品名稱
	 * <p/>
	 * 50個中文字
	 */
	@Size(max = 150)
	@Column(name = "COLLNAME", length = 150, columnDefinition = "VARCHAR(150)")
	private String collName;

	/**
	 * 放款值（TWD）
	 * <p/>
	 * 合計放款值
	 * 單位:元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "LOANTWD", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal loanTwd;

	/** 已敘做放款總戶數 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "TTLNUM", columnDefinition = "DECIMAL(5,0)")
	private Integer ttlNum;

	/** 已敘做放款總餘額 
	 * 單位:元
	 * **/
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "TTLBAL", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal ttlBal;

	/**
	 * 土地及建物擬/已設定
	 * <p/>
	 * 擬<br/>
	 * 已
	 */
	@Size(max = 1)
	@Column(name = "SET1", length = 1, columnDefinition = "CHAR(1)")
	private String set1;

	/** 順位 **/
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "SETODR", columnDefinition = "DECIMAL(2,0)")
	private Integer setOdr;

	/**
	 * 抵押金額
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "SETAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal setAmt;

	/**
	 * 前順位抵押金額
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "PRIAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal priAmt;

	/**
	 * 押值
	 * <p/>
	 * 單位:元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "TOTALLOANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal totalLoanAmt;

	/**
	 * 建物擬/已投保
	 * <p/>
	 * 擬<br/>
	 * 已<br/>
	 * 應
	 */
	@Size(max = 1)
	@Column(name = "INS1", length = 1, columnDefinition = "CHAR(1)")
	private String ins1;

	/** 火險金額 
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 * **/
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "INSAMT1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal insAmt1;

	/** 地震險金額 
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 * **/
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "INSAMT2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal insAmt2;

	/**
	 * 保險公司
	 * <p/>
	 * Codetype=BankCode06
	 */
	@Size(max = 10)
	@Column(name = "INSID", length = 10, columnDefinition = "VARCHAR(10)")
	private String insId;

	/**
	 * 火險金額計算方式
	 * <p/>
	 * (可複選)<br/>
	 * 保險金額>=（借款金額－土地放款值）萬元<br/>
	 * 以重置成本計算火險金額<br/>
	 * 以貸款金額額作為投保金額，但不得超逾建物重置成本。<br/>
	 * <br/>
	 * 1|2|3
	 */
	@Size(max = 5)
	@Column(name = "FIREINS", length = 5, columnDefinition = "VARCHAR(05)")
	private String fireIns;

	/**
	 * 借款金額
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "INAPPMONEY", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal inAppMoney;

	/**
	 * 土地放款值
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "ASSTOT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal assTot;

	/**
	 * 重置成本
	 * <p/>
	 * 單位:元
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "REBUILDCOST", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal rebuildCost;

	/**
	 * 擔保品係
	 * <p/>
	 * 自住|1<br/>
	 * 出租|2<br/>
	 * 自住並出租 |4<br/>
	 * 投資或空屋|3切結同意書之擔保品係xxx＜已計算完值＞<br/>
	 * Codetype=cms1010_useCondition
	 */
	@Size(max = 1)
	@Column(name = "TERM1", length = 1, columnDefinition = "CHAR(1)")
	private String term1;

	/**
	 * 有無加建未辦保存登記之建物
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	@Size(max = 1)
	@Column(name = "TERM2", length = 1, columnDefinition = "CHAR(1)")
	private String term2;

	/**
	 * 面積
	 * <p/>
	 * 切結同意書之 面積坪數
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "ADDRAREANUM", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal addrAreaNum;

	/**
	 * 建物地址
	 * <p/>
	 * 包含建號<br/>
	 * 個金 :建物地址<br/>
	 * JSONArray物件<br/>
	 * [{<br/>
	 * target:建物地址<br/>
	 * bldno:建號<br/>
	 * },....]<br/>
	 * 企金 :組成供L140M01Bt呈現字串
	 */
	@Size(max = 4096)
	@Column(name = "BUILD", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String build;

	/**
	 * 土地地段
	 * <p/>
	 * JSONArray物件<br/>
	 * [{<br/>
	 * target:土地地址 <br/>
	 * landNo:地號 <br/>
	 * ttlNum:已敘做總戶數<br/>
	 * ttlBal:已敘做總金額<br/>
	 * },<br/>
	 * ....]
	 */
	@Size(max = 2048)
	@Column(name = "AREADETAIL", length = 2048, columnDefinition = "VARCHAR(2048)")
	private String areaDetail;

	/** 地號 **/
	@Size(max = 1024)
	@Column(name = "LNNO", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String lnNo;

	/**
	 * 稅籍編號
	 * <p/>
	 * 估價表第一筆主建物<br/>
	 * 2013-04-17_修改欄位大小<br/>
	 * VARCHAR(12) - > VARCHAR(60)
	 */
	@Size(max = 60)
	@Column(name = "TAXNO", length = 60, columnDefinition = "VARCHAR(60)")
	private String taxNo;

	/**
	 * 稅籍地址
	 * <p/>
	 * 估價表第一筆主建物門牌地址<br/>
	 * 102/04/08 修改欄位大小<br/>
	 * VARCHAR(150)->VARCHAR(900)
	 */
	@Size(max = 900)
	@Column(name = "TAXADDR", length = 900, columnDefinition = "VARCHAR(900)")
	private String taxAddr;

	/** 核貸成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "PAYPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal payPercent;

	/** 擔保品內容 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "CMSDESC", columnDefinition = "CLOB")
	private String cmsDesc;

	/**
	 * 購價或時價
	 * <p/>
	 * ※2013/1/11_新增
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "INAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal inAmt;

	/**
	 * 屋齡
	 * <p/>
	 * ※2013/1/11_新增<br/>
	 * c101m04.bldYears-c101m04.useYears<br/>
	 * if小於0then等於0
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "HOUSEYEAR", columnDefinition = "DECIMAL(3,0)")
	private Integer houseYear;

	/**
	 * 擔保品Oid
	 * <p/>
	 * ※2013/1/11_新增擔保品文件編號
	 */
	@Size(max = 32)
	@Column(name = "CMSOID", length = 32, columnDefinition = "CHAR(32)")
	private String cmsOid;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 設定順位文字
	 * <p/>
	 * ※2013/3/14_新增設定順位文字
	 */
	@Size(max = 120)
	@Column(name = "SETORDSTR", length = 120, columnDefinition = "VARCHAR(120)")
	private String setordStr;
	
	/**
	 * 金額單位異動
	 */
	@Size(max = 1)
	@Column(name = "UNITCHG", length = 1, columnDefinition = "CHAR(1)")
	private String unitChg;
	
	/**
	 * 押值(折算後)
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "TOTLNAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal totLnAmt;
	
	/**
	 * 重置成本(折算後)
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "REBUILDAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal rebuildAmt;

	/** 過戶日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CTRTXDATE", columnDefinition = "DATE")
	private Date ctrTxDate;
	
	/** 近3年內移轉成交價格 **/ 
	@Column(name = "CTRTXAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal ctrTxAmt;
	
	/** 土地明細筆數 **/ 
	@Column(name = "LNDNUM", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal lndNum;
	
	/** 建物明細筆數 **/ 
	@Column(name = "BLDNUM", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal bldNum;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得額度明細表來源mainId **/
	public String getRefmainId() {
		return this.refmainId;
	}

	/** 設定額度明細表來源mainId **/
	public void setRefmainId(String value) {
		this.refmainId = value;
	}

	/** 取得序號 **/
	public Integer getSeqNo() {
		return this.seqNo;
	}

	/** 設定序號 **/
	public void setSeqNo(Integer value) {
		this.seqNo = value;
	}

	/**
	 * 取得分行代碼
	 * <p/>
	 * 擔保品所屬分行
	 */
	public String getBranch() {
		return this.branch;
	}

	/**
	 * 設定分行代碼
	 * <p/>
	 * 擔保品所屬分行
	 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 取得鑑價分行 **/
	public String getEstBrn() {
		return this.estBrn;
	}

	/** 設定鑑價分行 **/
	public void setEstBrn(String value) {
		this.estBrn = value;
	}

	/** 取得鑑估日期 **/
	public Date getEstDate() {
		return this.estDate;
	}

	/** 設定鑑估日期 **/
	public void setEstDate(Date value) {
		this.estDate = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得擔保品編號 **/
	public String getCollNo() {
		return this.collNo;
	}

	/** 設定擔保品編號 **/
	public void setCollNo(String value) {
		this.collNo = value;
	}

	/** 取得擔保品大類 **/
	public String getCollTyp1() {
		return this.collTyp1;
	}

	/** 設定擔保品大類 **/
	public void setCollTyp1(String value) {
		this.collTyp1 = value;
	}

	/** 取得擔保品小類 **/
	public String getCollTyp2() {
		return this.collTyp2;
	}

	/** 設定擔保品小類 **/
	public void setCollTyp2(String value) {
		this.collTyp2 = value;
	}

	/** 取得擔保品文件狀態 **/
	public String getDocStatus() {
		return this.docStatus;
	}

	/** 設定擔保品文件狀態 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/**
	 * 取得擔保品名稱
	 * <p/>
	 * 50個中文字
	 */
	public String getCollName() {
		return this.collName;
	}

	/**
	 * 設定擔保品名稱
	 * <p/>
	 * 50個中文字
	 **/
	public void setCollName(String value) {
		this.collName = value;
	}

	/**
	 * 取得放款值（TWD）
	 * <p/>
	 * 合計放款值
	 */
	public BigDecimal getLoanTwd() {
		return this.loanTwd;
	}

	/**
	 * 設定放款值（TWD）
	 * <p/>
	 * 合計放款值
	 **/
	public void setLoanTwd(BigDecimal value) {
		this.loanTwd = value;
	}

	/** 取得已敘做放款總戶數 **/
	public Integer getTtlNum() {
		return this.ttlNum;
	}

	/** 設定已敘做放款總戶數 **/
	public void setTtlNum(Integer value) {
		this.ttlNum = value;
	}

	/** 取得已敘做放款總餘額 **/
	public BigDecimal getTtlBal() {
		return this.ttlBal;
	}

	/** 設定已敘做放款總餘額 **/
	public void setTtlBal(BigDecimal value) {
		this.ttlBal = value;
	}

	/**
	 * 取得土地及建物擬/已設定
	 * <p/>
	 * 擬<br/>
	 * 已
	 */
	public String getSet1() {
		return this.set1;
	}

	/**
	 * 設定土地及建物擬/已設定
	 * <p/>
	 * 擬<br/>
	 * 已
	 **/
	public void setSet1(String value) {
		this.set1 = value;
	}

	/** 取得順位 **/
	public Integer getSetOdr() {
		return this.setOdr;
	}

	/** 設定順位 **/
	public void setSetOdr(Integer value) {
		this.setOdr = value;
	}

	/**
	 * 取得抵押金額
	 * <p/>
	 * 新台幣萬元
	 */
	public BigDecimal getSetAmt() {
		return this.setAmt;
	}

	/**
	 * 設定抵押金額
	 * <p/>
	 * 新台幣萬元
	 **/
	public void setSetAmt(BigDecimal value) {
		this.setAmt = value;
	}

	/**
	 * 取得前順位抵押金額
	 * <p/>
	 * 新台幣萬元
	 */
	public BigDecimal getPriAmt() {
		return this.priAmt;
	}

	/**
	 * 設定前順位抵押金額
	 * <p/>
	 * 新台幣萬元
	 **/
	public void setPriAmt(BigDecimal value) {
		this.priAmt = value;
	}

	/**
	 * 取得押值
	 * <p/>
	 * 元
	 */
	public BigDecimal getTotalLoanAmt() {
		return this.totalLoanAmt;
	}

	/**
	 * 設定押值
	 * <p/>
	 * 元
	 **/
	public void setTotalLoanAmt(BigDecimal value) {
		this.totalLoanAmt = value;
	}

	/**
	 * 取得建物擬/已投保
	 * <p/>
	 * 擬<br/>
	 * 已<br/>
	 * 應
	 */
	public String getIns1() {
		return this.ins1;
	}

	/**
	 * 設定建物擬/已投保
	 * <p/>
	 * 擬<br/>
	 * 已<br/>
	 * 應
	 **/
	public void setIns1(String value) {
		this.ins1 = value;
	}

	/** 取得火險金額 **/
	public BigDecimal getInsAmt1() {
		return this.insAmt1;
	}

	/** 設定火險金額 **/
	public void setInsAmt1(BigDecimal value) {
		this.insAmt1 = value;
	}

	/** 取得地震險金額 **/
	public BigDecimal getInsAmt2() {
		return this.insAmt2;
	}

	/** 設定地震險金額 **/
	public void setInsAmt2(BigDecimal value) {
		this.insAmt2 = value;
	}

	/**
	 * 取得保險公司
	 * <p/>
	 * Codetype=BankCode06
	 */
	public String getInsId() {
		return this.insId;
	}

	/**
	 * 設定保險公司
	 * <p/>
	 * Codetype=BankCode06
	 **/
	public void setInsId(String value) {
		this.insId = value;
	}

	/**
	 * 取得火險金額計算方式
	 * <p/>
	 * (可複選)<br/>
	 * 保險金額>=（借款金額－土地放款值）萬元<br/>
	 * 以重置成本計算火險金額<br/>
	 * 以貸款金額額作為投保金額，但不得超逾建物重置成本。<br/>
	 * <br/>
	 * 1|2|3
	 */
	public String getFireIns() {
		return this.fireIns;
	}

	/**
	 * 設定火險金額計算方式
	 * <p/>
	 * (可複選)<br/>
	 * 保險金額>=（借款金額－土地放款值）萬元<br/>
	 * 以重置成本計算火險金額<br/>
	 * 以貸款金額額作為投保金額，但不得超逾建物重置成本。<br/>
	 * <br/>
	 * 1|2|3
	 **/
	public void setFireIns(String value) {
		this.fireIns = value;
	}

	/**
	 * 取得借款金額
	 * <p/>
	 * 萬元
	 */
	public BigDecimal getInAppMoney() {
		return this.inAppMoney;
	}

	/**
	 * 設定借款金額
	 * <p/>
	 * 萬元
	 **/
	public void setInAppMoney(BigDecimal value) {
		this.inAppMoney = value;
	}

	/**
	 * 取得土地放款值
	 * <p/>
	 * 萬元
	 */
	public BigDecimal getAssTot() {
		return this.assTot;
	}

	/**
	 * 設定土地放款值
	 * <p/>
	 * 萬元
	 **/
	public void setAssTot(BigDecimal value) {
		this.assTot = value;
	}

	/**
	 * 取得重置成本
	 * <p/>
	 * 元
	 */
	public BigDecimal getRebuildCost() {
		return this.rebuildCost;
	}

	/**
	 * 設定重置成本
	 * <p/>
	 * 元
	 **/
	public void setRebuildCost(BigDecimal value) {
		this.rebuildCost = value;
	}

	/**
	 * 取得擔保品係
	 * <p/>
	 * 自住|1<br/>
	 * 出租|2<br/>
	 * 自住並出租 |4<br/>
	 * 投資或空屋|3切結同意書之擔保品係xxx＜已計算完值＞<br/>
	 * Codetype=cms1010_useCondition
	 */
	public String getTerm1() {
		return this.term1;
	}

	/**
	 * 設定擔保品係
	 * <p/>
	 * 自住|1<br/>
	 * 出租|2<br/>
	 * 自住並出租 |4<br/>
	 * 投資或空屋|3切結同意書之擔保品係xxx＜已計算完值＞<br/>
	 * Codetype=cms1010_useCondition
	 **/
	public void setTerm1(String value) {
		this.term1 = value;
	}

	/**
	 * 取得有無加建未辦保存登記之建物
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	public String getTerm2() {
		return this.term2;
	}

	/**
	 * 設定有無加建未辦保存登記之建物
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 **/
	public void setTerm2(String value) {
		this.term2 = value;
	}

	/**
	 * 取得面積
	 * <p/>
	 * 切結同意書之 面積坪數
	 */
	public BigDecimal getAddrAreaNum() {
		return this.addrAreaNum;
	}

	/**
	 * 設定面積
	 * <p/>
	 * 切結同意書之 面積坪數
	 **/
	public void setAddrAreaNum(BigDecimal value) {
		this.addrAreaNum = value;
	}

	/**
	 * 取得建物地址
	 * <p/>
	 * 包含建號<br/>
	 * 個金 :建物地址<br/>
	 * JSONArray物件<br/>
	 * [{<br/>
	 * target:建物地址<br/>
	 * bldno:建號<br/>
	 * },....]<br/>
	 * 企金 :組成供L140M01Bt呈現字串
	 */
	public String getBuild() {
		return this.build;
	}

	/**
	 * 設定建物地址
	 * <p/>
	 * 包含建號<br/>
	 * 個金 :建物地址<br/>
	 * JSONArray物件<br/>
	 * [{<br/>
	 * target:建物地址<br/>
	 * bldno:建號<br/>
	 * },....]<br/>
	 * 企金 :組成供L140M01Bt呈現字串
	 **/
	public void setBuild(String value) {
		this.build = value;
	}

	/**
	 * 取得土地地段
	 * <p/>
	 * JSONArray物件<br/>
	 * [{<br/>
	 * target:土地地址 <br/>
	 * landNo:地號 <br/>
	 * ttlNum:已敘做總戶數<br/>
	 * ttlBal:已敘做總金額<br/>
	 * },<br/>
	 * ....]
	 */
	public String getAreaDetail() {
		return this.areaDetail;
	}

	/**
	 * 設定土地地段
	 * <p/>
	 * JSONArray物件<br/>
	 * [{<br/>
	 * target:土地地址 <br/>
	 * landNo:地號 <br/>
	 * ttlNum:已敘做總戶數<br/>
	 * ttlBal:已敘做總金額<br/>
	 * },<br/>
	 * ....]
	 **/
	public void setAreaDetail(String value) {
		this.areaDetail = value;
	}

	/** 取得地號 **/
	public String getLnNo() {
		return this.lnNo;
	}

	/** 設定地號 **/
	public void setLnNo(String value) {
		this.lnNo = value;
	}

	/**
	 * 取得稅籍編號
	 * <p/>
	 * 估價表第一筆主建物<br/>
	 * 2013-04-17_修改欄位大小<br/>
	 * VARCHAR(12) - > VARCHAR(60)
	 */
	public String getTaxNo() {
		return this.taxNo;
	}

	/**
	 * 設定稅籍編號
	 * <p/>
	 * 估價表第一筆主建物<br/>
	 * 2013-04-17_修改欄位大小<br/>
	 * VARCHAR(12) - > VARCHAR(60)
	 **/
	public void setTaxNo(String value) {
		this.taxNo = value;
	}

	/**
	 * 取得稅籍地址
	 * <p/>
	 * 估價表第一筆主建物門牌地址<br/>
	 * 102/04/08 修改欄位大小<br/>
	 * VARCHAR(150)->VARCHAR(900)
	 */
	public String getTaxAddr() {
		return this.taxAddr;
	}

	/**
	 * 設定稅籍地址
	 * <p/>
	 * 估價表第一筆主建物門牌地址<br/>
	 * 102/04/08 修改欄位大小<br/>
	 * VARCHAR(150)->VARCHAR(900)
	 **/
	public void setTaxAddr(String value) {
		this.taxAddr = value;
	}

	/** 取得核貸成數 **/
	public BigDecimal getPayPercent() {
		return this.payPercent;
	}

	/** 設定核貸成數 **/
	public void setPayPercent(BigDecimal value) {
		this.payPercent = value;
	}

	/** 取得擔保品內容 **/
	public String getCmsDesc() {
		return this.cmsDesc;
	}

	/** 設定擔保品內容 **/
	public void setCmsDesc(String value) {
		this.cmsDesc = value;
	}

	/**
	 * 取得購價或時價
	 * <p/>
	 * ※2013/1/11_新增
	 */
	public BigDecimal getInAmt() {
		return this.inAmt;
	}

	/**
	 * 設定購價或時價
	 * <p/>
	 * ※2013/1/11_新增
	 **/
	public void setInAmt(BigDecimal value) {
		this.inAmt = value;
	}

	/**
	 * 取得屋齡
	 * <p/>
	 * ※2013/1/11_新增<br/>
	 * c101m04.bldYears-c101m04.useYears<br/>
	 * if小於0then等於0
	 */
	public Integer getHouseYear() {
		return this.houseYear;
	}

	/**
	 * 設定屋齡
	 * <p/>
	 * ※2013/1/11_新增<br/>
	 * c101m04.bldYears-c101m04.useYears<br/>
	 * if小於0then等於0
	 **/
	public void setHouseYear(Integer value) {
		this.houseYear = value;
	}

	/**
	 * 取得擔保品Oid
	 * <p/>
	 * ※2013/1/11_新增擔保品文件編號
	 */
	public String getCmsOid() {
		return this.cmsOid;
	}

	/**
	 * 設定擔保品Oid
	 * <p/>
	 * ※2013/1/11_新增擔保品文件編號
	 **/
	public void setCmsOid(String value) {
		this.cmsOid = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 取得設定順位文字
	 * <p/>
	 * ※2013/3/14_新增設定順位文字
	 */
	public String getSetordStr() {
		return this.setordStr;
	}

	/**
	 * 設定設定順位文字
	 * <p/>
	 * ※2013/3/14_新增設定順位文字
	 **/
	public void setSetordStr(String value) {
		this.setordStr = value;
	}

	/**
	 * 設定金額單位異動
	 */
	public void setUnitChg(String value) {
		this.unitChg = value;
	}

	/**
	 * 取得金額單位異動
	 */
	public String getUnitChg() {
		return this.unitChg;
	}

	/**
	 * 設定押值(折算後)
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	public void setTotLnAmt(BigDecimal value) {
		this.totLnAmt = value;
	}

	/**
	 * 取得押值(折算後)
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	public BigDecimal getTotLnAmt() {
		return this.totLnAmt;
	}

	/**
	 * 設定重置成本(折算後)
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	public void setRebuildAmt(BigDecimal value) {
		this.rebuildAmt = value;
	}

	/**
	 * 取得重置成本(折算後)
	 * <p/>
	 * 單位:萬元(假如unitChg=’Y’則算至仟元)
	 */
	public BigDecimal getRebuildAmt() {
		return this.rebuildAmt;
	}

	/** 取得過戶日期 **/
	public Date getCtrTxDate() {
		return ctrTxDate;
	}
	/** 設定過戶日期 **/
	public void setCtrTxDate(Date ctrTxDate) {
		this.ctrTxDate = ctrTxDate;
	}

	/** 取得近3年內移轉成交價格 **/ 
	public BigDecimal getCtrTxAmt() {
		return ctrTxAmt;
	}
	/** 設定近3年內移轉成交價格 **/ 
	public void setCtrTxAmt(BigDecimal ctrTxAmt) {
		this.ctrTxAmt = ctrTxAmt;
	}

	public BigDecimal getLndNum() {
		return lndNum;
	}

	public void setLndNum(BigDecimal lndNum) {
		this.lndNum = lndNum;
	}

	public BigDecimal getBldNum() {
		return bldNum;
	}

	public void setBldNum(BigDecimal bldNum) {
		this.bldNum = bldNum;
	}

}
