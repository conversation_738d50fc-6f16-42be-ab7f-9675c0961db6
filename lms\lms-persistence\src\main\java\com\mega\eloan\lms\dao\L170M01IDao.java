/* 
 * L170M01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L170M01I;

/** 海外覆審檢視表檔 **/
public interface L170M01IDao extends IGenericDao<L170M01I> {

	L170M01I findByOid(String oid);
	
	// findByIndex01
	List<L170M01I> findByIndex01(String mainId);
	L170M01I findByMainId(String oid);
}