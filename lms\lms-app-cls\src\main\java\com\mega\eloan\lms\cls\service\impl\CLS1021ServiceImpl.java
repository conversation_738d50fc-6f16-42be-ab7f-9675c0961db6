/* 
 * CLS1021ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringEscapeUtils;
import org.omg.Messaging.SYNC_WITH_TRANSPORT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;

import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.gwclient.OBSMqGwClient;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.handler.form.CLS1021M01FormHandler;
import com.mega.eloan.lms.cls.pages.CLS1021M01Page;
import com.mega.eloan.lms.cls.service.CLS1021Service;
import com.mega.eloan.lms.dao.C102A01ADao;
import com.mega.eloan.lms.dao.C102M01ADao;
import com.mega.eloan.lms.dao.C102M01BDao;


import com.mega.eloan.lms.mfaloan.service.MisELF448Service;
import com.mega.eloan.lms.model.C102A01A;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C102M01B;


import com.mega.sso.context.MegaSSOSecurityContext;

import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,GaryChang,new
 *          </ul>
 */
@Service
public class CLS1021ServiceImpl extends AbstractCapService implements
		CLS1021Service {

	private static final Logger LOGGER = LoggerFactory.getLogger(CLS1021ServiceImpl.class);
	
	@Resource
	FlowNameService flowNameService;

	@Resource
	TempDataService tempDataService;

	@Resource
	OBSMqGwClient obsMqGwClient;

	@Resource
	FlowService flowService;

	@Resource
	C102A01ADao c102a01aDao;

	@Resource
	C102M01ADao c102m01aDao;

	@Resource
	C102M01BDao c102m01bDao;

	@Resource
	DocFileDao docFileDao;

	@Resource
	DocLogService docLogService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMSService lmsService;

	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	MisELF448Service miself448service;
	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == C102M01A.class) {
			return c102m01aDao.findByMainId(mainId);
		} else if (clazz == C102M01B.class) {
			return c102m01bDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C102M01A) {
					if (Util.isEmpty(((C102M01A) model).getOid())) {
						((C102M01A) model).setCreator(user.getUserId());
						((C102M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						c102m01aDao.save((C102M01A) model);

						flowService.start("CLS1021Flow",
								((C102M01A) model).getOid(), user.getUserId(),
								user.getUnitNo());
						// 新增授權檔
						C102A01A c102a01a = new C102A01A();
						c102a01a.setAuthTime(CapDate.getCurrentTimestamp());
						c102a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
						c102a01a.setAuthUnit(user.getUnitNo());
						c102a01a.setMainId(((C102M01A) model).getMainId());
						c102a01a.setOwner(user.getUserId());
						c102a01a.setOwnUnit(user.getUnitNo());
						c102a01aDao.save(c102a01a);

					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						((C102M01A) model).setUpdater(user.getUserId());
						((C102M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						c102m01aDao.save((C102M01A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((C102M01A) model)
									.getMainId());
							docLogService.record(((C102M01A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof C102M01B) {
					((C102M01B) model).setUpdater(user.getUserId());
					((C102M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					c102m01bDao.save((C102M01B) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C102M01A.class) {
			return c102m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C102M01A.class) {
			return (T) c102m01aDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public boolean deleteC102m01as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C102M01A> c102m01as = new ArrayList<C102M01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			C102M01A c102m01a = (C102M01A) findModelByOid(C102M01A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			c102m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			c102m01a.setUpdater(user.getUserId());
			c102m01as.add(c102m01a);
			docLogService.record(c102m01a.getOid(), DocLogEnum.DELETE);
		}
		if (!c102m01as.isEmpty()) {
			c102m01aDao.save(c102m01as);
			flag = true;
		}
		return flag;
	}

	@Override
	public void saveC102m01bList(List<C102M01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (C102M01B c102m01b : list) {
			c102m01b.setUpdater(user.getUserId());
			c102m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		c102m01bDao.save(list);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#deleteUploadFile(java.lang
	 * .String[])
	 */
	@Override
	public void deleteUploadFile(String[] oids) {
		List<DocFile> docfiles = docFileDao.findAllByOid(oids);
		for (DocFile docfile : docfiles) {
			docfile.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		docFileDao.save(docfiles);
	}

	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType, boolean upMis)
			throws Throwable {
		mainOid = StringEscapeUtils.escapeSql(mainOid);
		if (model instanceof C102M01A) {
			save((C102M01A) model);
			if(upMis){
				C102M01A c102m01a=(C102M01A)model;
				List<Map<String, Object>> elf448data=miself448service.findBycntrNodupNocustId(c102m01a.getCustId(),c102m01a.getDupNo(),c102m01a.getCntrNo());
				String ELF501_RISK_RATING = Util.trim(LMSUtil.build_ELF501_RISK_RATING(c102m01a
						, c102m01a.getSelfChk(), c102m01a.getALoanPurpose()));
				//雖然這裡的變數是寫 elf448
				//但真正的存取table 是 elf501
				if(elf448data.size()==0){
					miself448service.insertHOUSE(c102m01a.getCustId(),c102m01a.getDupNo(),c102m01a.getCntrNo(), c102m01a.getSelfChk(), ELF501_RISK_RATING, c102m01a.getALoanAC());
				}else{
					miself448service.updateHOUSE(c102m01a.getCustId(),c102m01a.getDupNo(),c102m01a.getCntrNo(), c102m01a.getSelfChk(), ELF501_RISK_RATING, c102m01a.getALoanAC());
				}
			}
		}
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("CLS1021Flow",
						((C102M01A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// inst.setNextDept("200");
				// inst.setNextUser("nextTest")
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
			}
			inst.next();
			
				
			
		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public C102M01B findC102m01b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return c102m01bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}

	@Override
	public void delete(GenericBean... entity) {

	}

	@Override
	public void deleteC102m01bs(List<C102M01B> c102m01bs, boolean isAll) {
		if (isAll) {
			c102m01bDao.delete(c102m01bs);
		} else {
			List<C102M01B> C102M01BsOld = new ArrayList<C102M01B>();
			for (C102M01B c102m1b : c102m01bs) {
				String staffJob = c102m1b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					C102M01BsOld.add(c102m1b);
				}
			}
			c102m01bDao.delete(C102M01BsOld);
		}

	}
}
