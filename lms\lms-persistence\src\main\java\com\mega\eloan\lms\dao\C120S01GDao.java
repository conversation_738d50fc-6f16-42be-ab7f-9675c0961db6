/* 
 * C120S01GDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01G;


/** 個金房貸評等表 **/
public interface C120S01GDao extends IGenericDao<C120S01G> {

	C120S01G findByOid(String oid);

	List<C120S01G> findByMainId(String mainId);

	C120S01G findByUniqueKey(String mainId, String ownBrId, String custId,
			String dupNo);

	List<C120S01G> findByIndex01(String mainId, String ownBrId, String custId,
			String dupNo);
	
	List<C120S01G> findByCustIdDupId(String custId,String DupNo);
	
	int deleteByOid(String oid);
}