/* 
 * LMS9131M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.fms.panels.LMS9131S01Panel;
import com.mega.eloan.lms.model.C102M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書風險權數整批修改
 * </pre>
 * 
 * @since 2012/11/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/21,GaryChang,new
 *          </ul>
 */
@Controller@RequestMapping(path = "/lms/lms9131m01/{page}")
public class LMS9131M01Page extends AbstractEloanForm {
	@Autowired
	DocCheckService docCheckService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS9131M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);

		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, CreditDocStatusEnum.海外_待覆核,
				CreditDocStatusEnum.先行動用_待覆核));
		renderJsI18N(LMS9131M01Page.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		renderJsI18N(LMS9131M01Page.class);
		Panel panel = getPanel(page, params);
		model.addAttribute("tabID", tabID);
		panel.processPanelData(model, params);
	}// ;

	// 頁籤
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		// switch (index) {
		// case 1:

		panel = new LMS9131S01Panel(TAB_CTX);
		// break;
		// case 2:
		// renderRespMsgJsI18N("EFD3026"); // 多render一個msgi18n
		// // renderJsI18N(AbstractEloanPage.class, "EFD3026");
		// panel = new LMS1601S02Panel(TAB_CTX);
		// break;
		// case 3:
		// renderRespMsgJsI18N("EFD0002"); // 多render一個msgi18n
		// // renderJsI18N(AbstractEloanPage.class, "EFD0002");
		// panel = new LMS1605S03Panel(TAB_CTX, params);
		// break;
		// case 4:
		// panel = new LMS1605S04Panel(TAB_CTX);
		// break;
		// default:
		// panel = new LMS1605S01Panel(TAB_CTX);
		// break;
		// }
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C102M01A.class;
	}
}
