/* 
 *ObsdbELF384ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF025Service;

/**
 * <pre>
 * 科(子)目及其限額檔 ELF025
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
@Service
public class ObsdbELF025ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF025Service {

	@Override
	public void insert(String BRNID, String ELF025_SDATE, String ELF025_MAINID,
			String ELF025_BRANCH, String ELF025_CASEDATE,
			String ELF025_DOCUMENT_NO, String ELF025_CONTRACT_CO,
			String ELF025_CONTRACT, String ELF025_SWFT,
			BigDecimal ELF025_FACT_AMT, String ELF025_UPDATER,
			BigDecimal ELF025_TMESTAMP) {

		this.getJdbc(BRNID).update(
				"ELF025.insert",
				new Object[] { ELF025_SDATE, ELF025_MAINID, ELF025_BRANCH,
						ELF025_CASEDATE, ELF025_DOCUMENT_NO,
						ELF025_CONTRACT_CO, ELF025_CONTRACT, ELF025_SWFT,
						ELF025_FACT_AMT, ELF025_UPDATER, ELF025_TMESTAMP });

	}

	@Override
	public void deleteByMainId(String BRNID, String mainId) {
		this.getJdbc(BRNID).update("ELF025.delByMainId", new Object[] {mainId});
	}

}
