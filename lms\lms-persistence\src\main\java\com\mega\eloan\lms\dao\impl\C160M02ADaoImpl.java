/* 
 * C160M02ADaoImpl.java
 */

package com.mega.eloan.lms.dao.impl;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C160M02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160M02A;

/**
 * C160M02A 中鋼整批評等檔
 * 
 * <AUTHOR>
 * 
 */
@Repository
public class C160M02ADaoImpl extends LMSJpaDao<C160M02A, String> implements
		C160M02ADao {

	@Override
	public C160M02A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C160M02A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
}