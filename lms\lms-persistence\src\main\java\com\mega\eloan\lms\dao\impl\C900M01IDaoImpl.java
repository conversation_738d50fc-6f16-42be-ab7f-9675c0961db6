/* 
 * C900M01IDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C900M01IDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01I;

/** ID重配號記錄檔 **/
@Repository
public class C900M01IDaoImpl extends LMSJpaDao<C900M01I, String>
	implements C900M01IDao {

	@Override
	public C900M01I findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C900M01I findByMainId(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C900M01I findByUniqueKey(String custId, String dupNo, String ownBrId, String orgCustId, String orgDupNo){
		ISearch search = createSearchTemplete();
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (ownBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if (orgCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "orgCustId", orgCustId);
		if (orgDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "orgDupNo", orgDupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	
}