package com.mega.eloan.lms.batch.service.impl;

import java.io.IOException;
import java.security.KeyStore;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.gwclient.EJCICGwReqMessage;
import com.mega.eloan.common.gwclient.GWLogger;
import com.mega.eloan.common.gwclient.GWType;
import com.mega.eloan.common.gwclient.RPAHttpSSLSocketFactory;
import com.mega.eloan.common.service.GWLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.dc.util.Base64;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * RPA 回傳勞工紓困貸款案件狀態
 * </pre>
 * <p>
 * LOCAL Test URL example ： http://localhost/ces-web/app/schedulerRPA
 * <p>
 * Post Request : {"serviceId":"getSmallBusStatus", "vaildIP":"N",
 * "request":{"responseCode":"1","custId":"13724746","brNo":"007",
 * "rpaUserId","078001"}}
 * <p>
 * SIT http://*************/ces-web/app/schedulerRPA
 * 
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/2,EL07623,new
 *          </ul>
 * @since 2021/6/2
 */
@Service("queryEjcicOutputHtmlService")
public class RPAQueryEjcicOutputHtmlServiceImpl extends AbstractCapService
		implements WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(RPAQueryEjcicOutputHtmlServiceImpl.class);

	@Resource
	LMS1201Service service1201;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	GWLogService gwLogService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	EJCICGwClient ejcicClient;

	@Resource
	EjcicService ejcicService;

	@Value("${systemId}")
	private String sysId;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 * 
	 * REQUEST: http://localhost:9081/lms-web/app/schedulerRPA
	 * {"serviceId":"queryEjcicService"
	 * ,"vaildIP":"N","request":{"custId":"10101013"
	 * ,"dupNo":"0","brNo":"010","rpaUserId":"007623"}}
	 * 
	 * RESPONSE: {"rc":0,"rcmsg":"SUCCESS","message":"執行成功","rcUrl":
	 * "http://***************/ejcic/combination/JCIC0444.jsp?deptid=010&branchnm=%C4%F5%B6%AE%A4%C0%A6%E6&prodid=P1&queryid=10101013&empname=%B6%C0_007623&empid=007623&apid=EL&pur=B4A&purpose=1&cbdeptid=0103&key=ELKEY17925320881792"
	 * }
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag;
		logger.info("queryEjcicOutputHtmlService 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		GWLogger gwlogger = new GWLogger(GWType.GWTYPE_RPA, gwLogService,
				sysParameterService);

		JSONObject req = json.getJSONObject("request");
		String errorMsg = "";
		String custId = req.optString("custId", "");
		String dupNo = req.optString("dupNo", "");
		String brNo = req.optString("brNo", "");
		String prodId = req.optString("prodId", "");
		String rpaUserId = req.optString("rpaUserId", "");
		String retrialDate = req.optString("retrialDate", "");
		String diffDay = req.optString("diffDay", "0");
		String purpose_for_PACK = req.optString("purpose", ""); // 查詢目的{1:企業授信,
																// 2:房屋貸款,3:消費性貸款,
																// 4:留學生貸款}
		
		//J-111-0184_05097_B1001 Web e-Loan覆審發查聯徵, 針對企業負責人未擔任案下保證人者, 查詢理由要改為XGA
		String pur = req.optString("pur", "B4A"); // 查詢理由:B4A=> 第一層 B.原業務往來
													// 第二層 4放款業務(c) 第三層
													// A.取得當事人書面同意、
													// XGA=>第一層
													// X.其他,第二層G.其他(e),第三層A.當事人同意
		if (Util.isEmpty(pur)) {
			pur = "B4A";
		}

		gwlogger.logBegin(sysId, custId, "queryEjcicOutputHtmlService",
				req.toString(), System.currentTimeMillis());

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = rpaUserId;

		String empname = userInfoService.getUserName(userId);
		String deptid = brNo;
		String cbdeptid = "";
		String deptnm = "";
		IBranch iBranch = branchService.getBranch(brNo);
		if (iBranch != null) {
			cbdeptid = iBranch.getBrNo() + iBranch.getChkNo();
			deptnm = iBranch.getBrName();
		}

		// [下午 02:00] 蕭瑄(資訊處,專員)
		//
		// PUR詳細說明:
		// 查詢理由第一碼
		// A 新業務申請 B 原業務往來 H 原業務往來資料尚未報送 C 帳務管理
		// D 公開(公示)資訊 E 統計類資訊 F 消金債務協商 I 業務照會資訊
		// X 其他

		// 查詢理由第二碼
		// a.信用卡業務
		// b.現金卡業務
		// c.放款業務
		// d.其他授信業務
		// x.存款業務(限單選)
		// y.物件查詢
		// w.無追索權應收帳款承購業務
		// e.其他
		// z.衍生性金融商品業務(限單選)
		// A_PUR2["a"]='1';
		// A_PUR2["b"]='2';
		// A_PUR2["ab"]='3';
		// A_PUR2["c"]='4';
		// A_PUR2["ac"]='5';
		// A_PUR2["bc"]='6';
		// A_PUR2["abc"]='7';
		// A_PUR2["d"]='8';
		// A_PUR2["ad"]='9';
		// A_PUR2["bd"]='A';
		// A_PUR2["abd"]='B';
		// A_PUR2["cd"]='C';
		// A_PUR2["acd"]='D';
		// A_PUR2["bcd"]='E';
		// A_PUR2["abcd"]='F';
		// A_PUR2["e"]='G';
		// A_PUR2["ae"]='H';
		// A_PUR2["be"]='I';
		// A_PUR2["abe"]='J';
		// A_PUR2["ce"]='K';
		// A_PUR2["ace"]='L';
		// A_PUR2["bce"]='M';
		// A_PUR2["abce"]='N';
		// A_PUR2["de"]='O';
		// A_PUR2["ade"]='P';
		// A_PUR2["bde"]='Q';
		// A_PUR2["abde"]='R';
		// A_PUR2["cde"]='S';
		// A_PUR2["acde"]='T';
		// A_PUR2["bcde"]='U';
		// A_PUR2["abcde"]='V';
		// A_PUR2["x"]='X';
		// A_PUR2["y"]='Y';
		// A_PUR2["w"]='W';
		// A_PUR2["z"]='Z';

		// 查詢理由第三碼
		// A 取得當事人書面同意
		// B 依法令規定
		// G 取得當事人同意(或與當事人有契約或類似契約關係)-辦理勞工紓困貸款
		// H 取得當事人同意(或與當事人有契約或類似契約關係)-辦理央行對中小企業專案融通貸款C方案(限小規模營業人)
		// I 取得當事人同意(或與當事人有契約或類似契約關係)-辦理央行對中小企業專案融通貸款A方案
		// J 取得當事人同意(或與當事人有契約或類似契約關係)-辦理央行對中小企業專案融通貸款B方案
		// K 取得當事人同意(或與當事人有契約或類似契約關係)-其他因疫情影響之政府專案紓困貸款
		//
		
		
		//J-111-0184_05097_B1001 Web e-Loan覆審發查聯徵, 針對企業負責人未擔任案下保證人者, 查詢理由要改為XGA
		// [下午 02:00] 蕭瑄(資訊處,專員)
		// 其他其他當事人同意, 應該是XGA吧

		// String pur = "C4B"; // 查詢理由選項123
		// String pur = "B4A"; // 查詢理由選項123 第一層 B.原業務往來 第二層 4放款業務(c) 第三層
		// A.取得當事人書面同意

		// [下午 01:47] 黃建霖(資訊處,高級專員)
		// 他說:覆審針對企業負責人沒有擔任連保人者, 查詢理由必須用其他,其他,當事人同意

		String callAPI_URL_ejcic_PACK = "";
		String resp_url = "";

		EJCICGwReqMessage ejcicReq_PACK = new EJCICGwReqMessage();
		ejcicReq_PACK.setSysId("LMS");
		ejcicReq_PACK.setMsgId(IDGenerator.getUUID());
		ejcicReq_PACK.setQueryid(custId);
		ejcicReq_PACK.setEmpid(userId);
		ejcicReq_PACK.setEmpname(empname);
		ejcicReq_PACK.setDeptid(deptid);
		ejcicReq_PACK.setCbdeptid(cbdeptid);
		ejcicReq_PACK.setBranchnm(deptnm);
		ejcicReq_PACK.setPur(pur);

		ejcicReq_PACK.setProdid(prodId);
		ejcicReq_PACK.setPurpose(purpose_for_PACK);

		String respHtml = "";
		String respHtml64 = "";
		boolean hasOld = false;
		int tDiffDay = Util.parseInt(diffDay);
		Date toDate = Util.parseDate(CapDate.getCurrentDate("yyyy-MM-dd"));
		Date calcDate = CapDate.shiftDays(toDate, tDiffDay);
		String gStrQDate = "";
		try {

			// 先抓EJCIC DB

			List<Map<String, Object>> datedate_list = ejcicService
					.get_mis_datadate_records(custId, prodId);

			for (Map<String, Object> map : datedate_list) {
				String str_qDate = MapUtils.getString(map, "QDATE");
				TWNDate qDate = TWNDate.valueOf(str_qDate);
				// 同一日(或?日內之資料可以使用)
				if (qDate != null) {
					if (LMSUtil.cmpDate(qDate, ">=", calcDate)) {
						gStrQDate = str_qDate;
						// 最新一筆查詢日期
						break;
					}
				}

			}

			for (Map<String, Object> map : datedate_list) {
				String str_qDate = MapUtils.getString(map, "QDATE");
				TWNDate qDate = TWNDate.valueOf(str_qDate);

				if (Util.equals(str_qDate, gStrQDate)) {

					String tProdId = MapUtils.getString(map, "PRODID");

					try {
						StringBuilder htmlData = new StringBuilder();
						List<Map<String, Object>> list = ejcicService
								.getCPXQueryLogHtml(custId, prodId, str_qDate);
						for (Map<String, Object> querylog : list) {
							String txId = MapUtils.getString(querylog, "TXID");
							String html = MapUtils.getString(querylog,
									"HTMLDATA");
							logger.debug("TXID:{},HTML:{}", txId, html);
							htmlData.append(html);
						}
						if (Util.notEquals(htmlData.toString(), "")) {
							hasOld = true;
							respHtml64 = Base64.encodeBytes(htmlData.toString()
									.getBytes("BIG5"));
						}

					} catch (Exception e) {
						errorMsg = "EJCIC OLD Exception:"
								+ StrUtils.getStackTrace(e);
						logger.error(StrUtils.getStackTrace(e));

					}

				}
			}

			if (!hasOld) {

				// 抓EJCIC
				callAPI_URL_ejcic_PACK = Util.trim(ejcicClient
						.get_callAPI_URL(ejcicReq_PACK));
				resp_url = callAPI_URL_ejcic_PACK;
				logger.info("queryEjcicOutputHtmlService resp_url=" + resp_url);
				try {

					int sec = 300;
					KeyStore trustStore = KeyStore.getInstance(KeyStore
							.getDefaultType());
					trustStore.load(null, null);

					SSLSocketFactory sf = new RPAHttpSSLSocketFactory(
							trustStore);
					final HttpParams params = new BasicHttpParams();
					HttpConnectionParams.setStaleCheckingEnabled(params, false);
					HttpConnectionParams.setConnectionTimeout(params,
							sec * 1000);
					HttpConnectionParams.setSoTimeout(params, sec * 1000);
					HttpConnectionParams.setSocketBufferSize(params, 8192 * 5);
					SchemeRegistry registry = new SchemeRegistry();
					registry.register(new Scheme("http", PlainSocketFactory
							.getSocketFactory(), 80));
					registry.register(new Scheme("https", sf, 443));
					ClientConnectionManager ccm = new ThreadSafeClientConnManager(
							params, registry);

					HttpClient httpclient = new DefaultHttpClient(ccm, params);

					HttpGet httpGet = new HttpGet(resp_url);
					HttpResponse response = null;
					response = httpclient.execute(httpGet);

					if (response.getStatusLine().getStatusCode() == 200) {
						// 回傳內容
						String content = EntityUtils.toString(
								response.getEntity(), "big5");// UTF-8 big5
						// 內容寫如檔案
						// FileUtils
						// .writeByteArrayToFile(
						// new File(
						// "/elnfs/LMS/931/2021/858e8062cdcd43879d812f6ae1d07136/lrs/cccccc.html"),
						// content.getBytes("BIG5"));

						respHtml64 = Base64.encodeBytes(content
								.getBytes("BIG5"));

					} else {
						errorMsg = "HTTP ERROR:"
								+ response.getStatusLine().getStatusCode();
					}

				} catch (IOException ioe) {
					errorMsg = "EJCIC NEW resp_url=" + resp_url + " Exception:"
							+ StrUtils.getStackTrace(ioe);
					logger.error("url={}", resp_url);
					logger.error(StrUtils.getStackTrace(ioe));
				} catch (Exception e) {
					errorMsg = "EJCIC NEW resp_url=" + resp_url + " Exception:"
							+ StrUtils.getStackTrace(e);
					logger.error("url={}", resp_url);
					logger.error(StrUtils.getStackTrace(e));
				} finally {

				}
			}

		} catch (Exception e) {
			errorMsg = e.toString();
		}

		logger.info("queryEjcicOutputHtmlService 結束========================");

		GWException gwException = null;
		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
			gwException = new GWException(errorMsg, getClass(),
					GWException.GWTYPE_RPA);
		}

		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
		} else {
			logger.info("執行成功");
		}

		if (!CapString.isEmpty(errorMsg)) {
			// mag = JSONObject
			// .fromObject("{\"rc\": 0, \"rcmsg\": \"FAIL\", \"message\":\" "
			// + errorMsg + "\"}");

			JSONObject jsonVal = new JSONObject();
			jsonVal.put("rc", "1");
			jsonVal.put("rcmsg", "FAIL");
			jsonVal.put("message", errorMsg);
			mag = jsonVal;
		} else {

			JSONObject jsonVal = new JSONObject();
			jsonVal.put("rc", "0");
			jsonVal.put("rcmsg", "SUCCESS");
			jsonVal.put("message", "執行成功");
			jsonVal.put("rcType", hasOld ? "O" : "N"); // O:OLD N:NEW
			jsonVal.put("rcHtml", respHtml64);

			mag = jsonVal;
		}

		gwlogger.logEnd(mag.toString(), gwException, "0");
		return mag;
	}
}
