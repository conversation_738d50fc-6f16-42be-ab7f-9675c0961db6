package com.mega.eloan.lms.base.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S04C;

import jxl.write.WriteException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

public interface RelatedAccountService extends AbstractService {
	Page<? extends GenericBean> queryL120s01aById(ISearch pageSetting);

	Page<? extends GenericBean> queryL120s04a(ISearch pageSetting);

	Page<? extends GenericBean> queryL120s04b(ISearch pageSetting);

	Page<DocFile> queryfile(ISearch pageSetting, boolean needCngName,
			boolean needBranch);

	boolean delfile(String fileOid);

	void genfile(String mainId, String fieldId, String keyCustId,
			String keyDupNo) throws CapException, WriteException, IOException;

	String checkDWDate(String queryDateS, String queryDateE,
			String str_LMS1205S07Form03);

	List<L120S04A> saveL120s04a2(String mainId, String queryDateS,
			String queryDateE, String keyCustId, String keyDupNo,
			String keyCustName);

	List<Map<String, String>> getKeyCustId(String l120m01a_mainId);

	String getKeyCustName(String l120m01a_mainId, String choose_keyCustId,
			String choose_keyDupNo);

	L120S04A findL120s04aByOid(String oid);

	L120S04B findL120s04bByOid(String oid);

	L120S04C findL120s04cByOid(String oid);

	CapAjaxFormResult queryL120s04a_detail(L120S04A l120s04a)
			throws CapException;

	CapAjaxFormResult queryL120s04b_detail(L120S04B l120s04b, String keyCustName)
			throws CapException;

	L120S04A rQueryL120s04a(L120S04A l120s04a);

	List<L120S04A> findL120s04aByMainIdKeyCustIdDupNodPrtFlag(String mainId,
			String keyCustId, String keyDupNo, String prtFlag);

	List<L120S04A> findL120s04aByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo);

	L120M01A findL120m01aByMainId(String mainId);

	void setTotal(String mainId, String keyCustId, String keyDupNo,
			Map<String, Long> map, String queryDateS, String queryDateE);

	void deleteL120S04_A(String mainId, String keyCustId, String keyDupNo);

	void deleteL120S04_BC(String mainId, String keyCustId, String keyDupNo);

	String importL120s04b(String mainId, String keyCustId, String keyDupNo,
			String queryDateS, String queryDateE);

	void cancelPrint(String mainId, String[] oidArray);

	void undoPrint(String mainId, String[] oidArray);

	void saveL120s04bc(L120S04B model, List<L120S04C> list);

	public Map<String, Object> getMainGrpData(String gpId);

	String importL120s04b_Risk_weighted_Assets(String mainId, String keyCustId,
			String keyDupNo, String queryDateS, String queryDateE,
			JSONObject jsonData, BranchRate branchRate);
}
