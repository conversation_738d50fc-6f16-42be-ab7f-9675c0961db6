/* 
 * L230M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 簽約未動用授信案件報送檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L230M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L230M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	/**
	 * JOIN條件 L230A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l230m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L230A01A> l230a01a;

	public Set<L230A01A> getL230a01a() {
		return l230a01a;
	}

	public void setL230a01a(Set<L230A01A> l230a01a) {
		this.l230a01a = l230a01a;
	}

	/**
	 * JOIN條件 L230S01A．簽約未動用額度資訊檔
	 * 
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l230m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L230S01A> l230s01a;

	public Set<L230S01A> getL230s01a() {
		return l230s01a;
	}

	public void setL230s01a(Set<L230S01A> l230s01a) {
		this.l230s01a = l230s01a;
	}

	/**
	 * 案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/** 授信額度合計(幣別) **/
	@Column(name = "LTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String ltCurr;

	/**
	 * 授信額度合計(金額)
	 * <p/>
	 * 101/03/01調整<br/>
	 * DECIMAL(13,0)(DECIMAL(15,0)<br/>
	 * 若是引進自簽報書，則為已核准額度明細表/批覆書上顯示之授信額度合計
	 */
	@Column(name = "LTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal ltAmt;

	/**
	 * 原申請案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(768)(VARCHAR(4096)
	 */
	@Column(name = "GIST", length = 4096, columnDefinition = "VARCHAR(4096)")
	private String gist;

	/**
	 * 未簽約、動用原因
	 * <p/>
	 * 256個全型字 <br/>
	 * 102.02.18 欄位擴大 768 -> 1200
	 */
	@Column(name = "REASION", length = 1200, columnDefinition = "VARCHAR(1200)")
	private String reasion;

	/** 經辦 **/
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;

	/** 覆核主管 **/
	@Column(name = "RECHECKID", length = 6, columnDefinition = "CHAR(6)")
	private String reCheckId;

	/** 授信主管 **/
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossId;

	/** 經副襄理 **/
	@Column(name = "MANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String managerId;

	/**
	 * RPTID
	 * <p/>
	 * 2012-11-29 增加註解<br/>
	 * CLS 個金<br/>
	 * LMS企金<br/>
	 * 空白 海外<br/>
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/**
	 * 取得案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/**
	 * 設定案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 取得授信額度合計(幣別) **/
	public String getLtCurr() {
		return this.ltCurr;
	}

	/** 設定授信額度合計(幣別) **/
	public void setLtCurr(String value) {
		this.ltCurr = value;
	}

	/**
	 * 取得授信額度合計(金額)
	 * <p/>
	 * 101/03/01調整<br/>
	 * DECIMAL(13,0)(DECIMAL(15,0)<br/>
	 * 若是引進自簽報書，則為已核准額度明細表/批覆書上顯示之授信額度合計
	 */
	public BigDecimal getLtAmt() {
		return this.ltAmt;
	}

	/**
	 * 設定授信額度合計(金額)
	 * <p/>
	 * 101/03/01調整<br/>
	 * DECIMAL(13,0)(DECIMAL(15,0)<br/>
	 * 若是引進自簽報書，則為已核准額度明細表/批覆書上顯示之授信額度合計
	 **/
	public void setLtAmt(BigDecimal value) {
		this.ltAmt = value;
	}

	/**
	 * 取得原申請案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(768)(VARCHAR(4096)
	 */
	public String getGist() {
		return this.gist;
	}

	/**
	 * 設定原申請案由
	 * <p/>
	 * 101/06/25調整<br/>
	 * VARCHAR(768)(VARCHAR(4096)
	 **/
	public void setGist(String value) {
		this.gist = value;
	}

	/**
	 * 取得未簽約、動用原因
	 * <p/>
	 * 256個全型字
	 */
	public String getReasion() {
		return this.reasion;
	}

	/**
	 * 設定未簽約、動用原因
	 * <p/>
	 * 256個全型字
	 **/
	public void setReasion(String value) {
		this.reasion = value;
	}

	/** 取得經辦 **/
	public String getApprId() {
		return this.apprId;
	}

	/** 設定經辦 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得覆核主管 **/
	public String getReCheckId() {
		return this.reCheckId;
	}

	/** 設定覆核主管 **/
	public void setReCheckId(String value) {
		this.reCheckId = value;
	}

	/** 取得授信主管 **/
	public String getBossId() {
		return this.bossId;
	}

	/** 設定授信主管 **/
	public void setBossId(String value) {
		this.bossId = value;
	}

	/** 取得經副襄理 **/
	public String getManagerId() {
		return this.managerId;
	}

	/** 設定經副襄理 **/
	public void setManagerId(String value) {
		this.managerId = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 2012-11-29 增加註解<br/>
	 * CLS 個金<br/>
	 * LMS企金<br/>
	 * 空白 海外<br/>
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 2012-11-29 增加註解<br/>
	 * CLS 個金<br/>
	 * LMS企金<br/>
	 * 空白 海外<br/>
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}
}
