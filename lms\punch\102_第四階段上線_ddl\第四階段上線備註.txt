﻿1.國內授信系統上線時要把config.properties裡的isTestEmail=true改成isTestEmail=false
*2.需新增 簽報書給號檔 LMS.NUMBER資料(建霖提說新上線的給號從50000開始給)
*3.creatTable 為 國內上線新增table ,editTable 為以上線 table 修改,paramater為 第四階段相關參數
4.要取得所有子母行的對應表
5.需新增以下內容lms-config/spring/getway.xml
	<bean id="rServiceClient" class="com.mega.eloan.common.gwclient.RServiceClient">
		<constructor-arg index="0" type="int" value="65" />
	</bean>

	<bean id="eloanBatchClient" class="com.mega.eloan.common.gwclient.EloanBatchClient">
		<!-- 系統代碼 -->
		<constructor-arg index="0" type="java.lang.String" value="${systemId}" />
		<constructor-arg index="1" type="int" value="65" />
	</bean>
6.需新增以下內容lms-config/spring/dao-lms.xml
	
	<bean id="schMainDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchMainDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>

	<bean id="schJobDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchJobDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>

	<bean id="schRelDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchRelDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>

	<bean id="schLogDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchLogDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>

	<bean id="schQueueDaoImpl" class="com.mega.eloan.common.batch.dao.impl.SchQueueDaoImpl">
		<property name="entityManager" ref="comEntityManager" />
	</bean>
7. online轉檔，需增加一個第三方jar檔: commons-configuration-1.9.jar
8. online轉檔 dc-config.properties需修改

