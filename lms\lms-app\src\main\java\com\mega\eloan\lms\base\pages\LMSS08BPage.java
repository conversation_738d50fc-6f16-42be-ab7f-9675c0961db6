package com.mega.eloan.lms.base.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.acl.EjcicAcl;
import com.mega.eloan.common.acl.EtchAcl;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.panels.LMSS08COMPanel;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 相關文件-最新集團分頁
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/baselmss08b/{page}")
public class LMSS08BPage extends AbstractOutputPage {

	@Autowired
	L120M01ADao l120m01aDao;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		renderJsI18N(LMSS08COMPanel.class);
		EjcicAcl ejcicAcl = new EjcicAcl();
		model.addAttribute("_s91t1f1btnEjcic_visible", ejcicAcl.isVisible());
		model.addAttribute("_s91t1f2btnQryEjcic_visible", ejcicAcl.isVisible());
		
		EtchAcl etchAcl = new EtchAcl();
		model.addAttribute("_s91t1f2btnQryEtch_visible", etchAcl.isVisible());
		String docType = Util.trim(params.getString("docType"));
		setNeedHtml(true);		// need html
		if(Util.isEmpty(docType)){
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			L120M01A meta = l120m01aDao.findByMainId(mainId);
			if(meta == null){
				meta = new L120M01A();
			}
			docType = Util.trim(meta.getDocType());
		}
		
//		if(UtilConstants.Casedoc.DocType.個金.equals(docType)){
//			setJavascript(new String[] { "pagejs/cls/CLSS08APage02.js" });
//		}else{
//			setJavascript(new String[] { "pagejs/lns/LMSS08APage02.js" });
//		}
		setJavascript(new String[] { "pagejs/base/LMSS08APage02.js" });
		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		// UPGRADE: 待確認是否一樣換成Thymeleaf的None.html頁面
		return "common/pages/None";
	}
}
