/* 
 * L120S04B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 關係戶於本行往來實績彙總表主檔 **/
@Entity
// @EntityListeners({DocumentModifyListener.class})
@Table(name = "L120S04B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S04B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 隸屬集團代號 **/
	@Column(name = "GRPNO", length = 4, columnDefinition = "CHAR(4)")
	private String grpNo;

	/** 隸屬集團 **/
	@Column(name = "GRPNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String grpName;

	/**
	 * 集團評等年度
	 * <p/>
	 * 西元年
	 */
	@Column(name = "GRPYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer grpYear;

	/**
	 * 集團評等
	 * <p/>
	 * 1級|1<br/>
	 * 2級|2<br/>
	 * 3級|3<br/>
	 * 4級|4<br/>
	 * 5級|5<br/>
	 * 未評等 | 6<br/>
	 * 問題集團 | 7
	 */
	@Column(name = "GRPGRRD", length = 2, columnDefinition = "CHAR(2)")
	private String grpGrrd;

	/**
	 * 主要集團企業最近一年起日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	@Column(name = "MAINGRPDATES", length = 20, columnDefinition = "VARCHAR(20)")
	private String mainGrpDateS;

	/**
	 * 主要集團企業最近一年迄日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	@Column(name = "MAINGRPDATEE", length = 20, columnDefinition = "VARCHAR(20)")
	private String mainGrpDateE;

	/**
	 * 主要集團企業-平均餘額報酬率
	 * <p/>
	 * %
	 */
	@Column(name = "MAINGRPAVGRATE", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal mainGrpAvgRate;

	/**
	 * 借戶暨關係戶近半年起日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	@Column(name = "DEPOSITDATES", length = 20, columnDefinition = "VARCHAR(20)")
	private String depositDateS;

	/**
	 * 借戶暨關係戶近半年迄日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	@Column(name = "DEPOSITDATEE", length = 20, columnDefinition = "VARCHAR(20)")
	private String depositDateE;

	/**
	 * 本行平均存款合計－金額
	 * <p/>
	 * TWD仟元
	 */
	@Column(name = "MEGAAVGAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal megaAvgAmt;

	/**
	 * 活期性存款－金額
	 * <p/>
	 * TWD仟元
	 */
	@Column(name = "DEMANDAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal demandAmt;

	/**
	 * 活期性存款所占比率
	 * <p/>
	 * %
	 */
	@Column(name = "DEMANDAVGRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal demandAvgRate;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/** 分項統一編號 **/
	@Column(name = "KEYCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String keyCustId;

	/** 分項重覆序號 **/
	@Column(name = "KEYDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String keyDupNo;

	/**
	 * 彙總表類別 空白：關係戶 A：共借戶
	 */
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String docKind;

	/** 與借款人關係Grid 排序(非DB欄位) **/
	@Transient
	private String rptName;

	/**
	 * 主要借款人-平均餘額報酬率最近一年起日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	@Column(name = "MAINGRPDATEMS", length = 20, columnDefinition = "VARCHAR(20)")
	private String mainGrpDateMS;

	/**
	 * 主要借款人-平均餘額報酬率最近一年迄日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	@Column(name = "MAINGRPDATEME", length = 20, columnDefinition = "VARCHAR(20)")
	private String mainGrpDateME;

	/**
	 * 主要借款人-平均餘額報酬率
	 * <p/>
	 * %
	 */
	@Column(name = "MAINGRPAVGRATEM", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal mainGrpAvgRateM;

	/**
	 * 主要借款人暨關係戶-平均餘額報酬率最近一年起日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	@Column(name = "MAINGRPDATERS", length = 20, columnDefinition = "VARCHAR(20)")
	private String mainGrpDateRS;

	/**
	 * 主要借款人暨關係戶-平均餘額報酬率最近一年迄日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	@Column(name = "MAINGRPDATERE", length = 20, columnDefinition = "VARCHAR(20)")
	private String mainGrpDateRE;

	/**
	 * 主要借款人暨關係戶-平均餘額報酬率
	 * <p/>
	 * %
	 */
	@Column(name = "MAINGRPAVGRATER", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal mainGrpAvgRateR;

	/** 集團企業規模 **/
	@Column(name = "GRPSIZE", length = 1, columnDefinition = "CHAR(01)")
	private String grpSize;

	/** 集團企業規模級別 **/
	@Column(name = "GRPLEVEL", length = 1, columnDefinition = "CHAR(01)")
	private String grpLevel;

	/** 備註 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "MEMOADD", columnDefinition = "CLOB")
	private String memoAdd;

    /** 高利拆單_截至日期 **/
    @Column(name = "HINSDATE", length = 20, columnDefinition = "VARCHAR(20)")
    private String hinsDate;

	/** 高利拆單_共計金額(TWD千元) **/
	@Column(name = "HINSAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal hinsAmt;

	/** 高利拆單_平均利率(%) **/
	@Column(name = "HINSRATE", columnDefinition = "DECIMAL(12,6)")
	private BigDecimal hinsRate;

	/** 高利拆單_近半年平均金額(TWD千元) **/
	@Column(name = "HINSAVGAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal hinsAvgAmt;

	/** 高利拆單_近半年平均利率(%) **/
	@Column(name = "HINSAVGRATE", columnDefinition = "DECIMAL(12,6)")
	private BigDecimal hinsAvgRate;

    /** 前一年底薪轉戶數 **/
    @Column(name="SALARYACCT", columnDefinition="DECIMAL(13,0)")
    private Long salaryAcct;

    /** 前一年底薪轉戶-信用卡持卡人數 **/
    @Column(name="SALARYCARDACCT", columnDefinition="DECIMAL(13,0)")
    private Long salaryCardAcct;

    /**
     * 薪轉信用卡貢獻度起日
     * <p/>
     * (年月) YYYY/MM
     */
    @Column(name = "SALARYCARDDATES", length = 20, columnDefinition = "VARCHAR(20)")
    private String salaryCardDateS;

    /**
     * 薪轉信用卡貢獻度迄日
     * <p/>
     * (年月) YYYY/MM
     */
    @Column(name = "SALARYCARDDATEE", length = 20, columnDefinition = "VARCHAR(20)")
    private String salaryCardDateE;

	/**
	 * 薪轉信用卡_利潤貢獻<p/>
	 * TWD仟元
	 */
	@Column(name="SALARYCARDPCAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal salaryCardPcAmt;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得隸屬集團代號 **/
	public String getGrpNo() {
		return this.grpNo;
	}

	/** 設定隸屬集團代號 **/
	public void setGrpNo(String value) {
		this.grpNo = value;
	}

	/** 取得隸屬集團 **/
	public String getGrpName() {
		return this.grpName;
	}

	/** 設定隸屬集團 **/
	public void setGrpName(String value) {
		this.grpName = value;
	}

	/**
	 * 取得集團評等年度
	 * <p/>
	 * 西元年
	 */
	public Integer getGrpYear() {
		return this.grpYear;
	}

	/**
	 * 設定集團評等年度
	 * <p/>
	 * 西元年
	 **/
	public void setGrpYear(Integer value) {
		this.grpYear = value;
	}

	/**
	 * 取得集團評等
	 * <p/>
	 * 1級|1<br/>
	 * 2級|2<br/>
	 * 3級|3<br/>
	 * 4級|4<br/>
	 * 5級|5<br/>
	 * 未評等 | 6<br/>
	 * 問題集團 | 7
	 */
	public String getGrpGrrd() {
		return this.grpGrrd;
	}

	/**
	 * 設定集團評等
	 * <p/>
	 * 1級|1<br/>
	 * 2級|2<br/>
	 * 3級|3<br/>
	 * 4級|4<br/>
	 * 5級|5<br/>
	 * 未評等 | 6<br/>
	 * 問題集團 | 7
	 **/
	public void setGrpGrrd(String value) {
		this.grpGrrd = value;
	}

	/**
	 * 取得主要集團企業最近一年起日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	public String getMainGrpDateS() {
		return this.mainGrpDateS;
	}

	/**
	 * 設定主要集團企業最近一年起日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 **/
	public void setMainGrpDateS(String value) {
		this.mainGrpDateS = value;
	}

	/**
	 * 取得主要集團企業最近一年迄日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	public String getMainGrpDateE() {
		return this.mainGrpDateE;
	}

	/**
	 * 設定主要集團企業最近一年迄日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 **/
	public void setMainGrpDateE(String value) {
		this.mainGrpDateE = value;
	}

	/**
	 * 取得主要集團企業-平均餘額報酬率
	 * <p/>
	 * %
	 */
	public BigDecimal getMainGrpAvgRate() {
		return this.mainGrpAvgRate;
	}

	/**
	 * 設定主要集團企業-平均餘額報酬率
	 * <p/>
	 * %
	 **/
	public void setMainGrpAvgRate(BigDecimal value) {
		this.mainGrpAvgRate = value;
	}

	/**
	 * 取得借戶暨關係戶近半年起日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	public String getDepositDateS() {
		return this.depositDateS;
	}

	/**
	 * 設定借戶暨關係戶近半年起日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 **/
	public void setDepositDateS(String value) {
		this.depositDateS = value;
	}

	/**
	 * 取得借戶暨關係戶近半年迄日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 */
	public String getDepositDateE() {
		return this.depositDateE;
	}

	/**
	 * 設定借戶暨關係戶近半年迄日
	 * <p/>
	 * (年月) YYYY/MM<br/>
	 * Miller edited at <br/>
	 * 2012/11/21
	 **/
	public void setDepositDateE(String value) {
		this.depositDateE = value;
	}

	/**
	 * 取得本行平均存款合計－金額
	 * <p/>
	 * TWD仟元
	 */
	public BigDecimal getMegaAvgAmt() {
		return this.megaAvgAmt;
	}

	/**
	 * 設定本行平均存款合計－金額
	 * <p/>
	 * TWD仟元
	 **/
	public void setMegaAvgAmt(BigDecimal value) {
		this.megaAvgAmt = value;
	}

	/**
	 * 取得活期性存款－金額
	 * <p/>
	 * TWD仟元
	 */
	public BigDecimal getDemandAmt() {
		return this.demandAmt;
	}

	/**
	 * 設定活期性存款－金額
	 * <p/>
	 * TWD仟元
	 **/
	public void setDemandAmt(BigDecimal value) {
		this.demandAmt = value;
	}

	/**
	 * 取得活期性存款所占比率
	 * <p/>
	 * %
	 */
	public BigDecimal getDemandAvgRate() {
		return this.demandAvgRate;
	}

	/**
	 * 設定活期性存款所占比率
	 * <p/>
	 * %
	 **/
	public void setDemandAvgRate(BigDecimal value) {
		this.demandAvgRate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 取得分項統一編號 **/
	public String getKeyCustId() {
		return keyCustId;
	}

	/** 設定分項統一編號 **/
	public void setKeyCustId(String keyCustId) {
		this.keyCustId = keyCustId;
	}

	/** 取得分項重覆序號 **/
	public String getKeyDupNo() {
		return keyDupNo;
	}

	/** 設定分項重覆序號 **/
	public void setKeyDupNo(String keyDupNo) {
		this.keyDupNo = keyDupNo;
	}

	/** 設定彙總表類別 **/
	public void setDocKind(String docKind) {
		this.docKind = docKind;
	}

	/** 取得彙總表類別 **/
	public String getDocKind() {
		return docKind;
	}

	public void setRptName(String rptName) {
		this.rptName = rptName;
	}

	public String getRptName() {
		return rptName;
	}

	public void setMainGrpDateMS(String mainGrpDateMS) {
		this.mainGrpDateMS = mainGrpDateMS;
	}

	public String getMainGrpDateMS() {
		return mainGrpDateMS;
	}

	public void setMainGrpDateME(String mainGrpDateME) {
		this.mainGrpDateME = mainGrpDateME;
	}

	public String getMainGrpDateME() {
		return mainGrpDateME;
	}

	public void setMainGrpAvgRateM(BigDecimal mainGrpAvgRateM) {
		this.mainGrpAvgRateM = mainGrpAvgRateM;
	}

	public BigDecimal getMainGrpAvgRateM() {
		return mainGrpAvgRateM;
	}

	public void setMainGrpDateRS(String mainGrpDateRS) {
		this.mainGrpDateRS = mainGrpDateRS;
	}

	public String getMainGrpDateRS() {
		return mainGrpDateRS;
	}

	public void setMainGrpDateRE(String mainGrpDateRE) {
		this.mainGrpDateRE = mainGrpDateRE;
	}

	public String getMainGrpDateRE() {
		return mainGrpDateRE;
	}

	public void setMainGrpAvgRateR(BigDecimal mainGrpAvgRateR) {
		this.mainGrpAvgRateR = mainGrpAvgRateR;
	}

	public BigDecimal getMainGrpAvgRateR() {
		return mainGrpAvgRateR;
	}

	public void setGrpSize(String grpSize) {
		this.grpSize = grpSize;
	}

	public String getGrpSize() {
		return grpSize;
	}

	public void setGrpLevel(String grpLevel) {
		this.grpLevel = grpLevel;
	}

	public String getGrpLevel() {
		return grpLevel;
	}

	public void setMemoAdd(String memoAdd) {
		this.memoAdd = memoAdd;
	}

	public String getMemoAdd() {
		return memoAdd;
	}

	/**
	 * 取得高利拆單_截至日期
	 * <p/>
	 * (年月) YYYY/MM
	 */
	public String getHinsDate() {
		return this.hinsDate;
	}

	/**
	 * 設定高利拆單_截至日期
	 * <p/>
	 * (年月) YYYY/MM
	 **/
	public void setHinsDate(String value) {
		this.hinsDate = value;
	}

	/**
	 * 取得高利拆單_共計金額(TWD千元)
	 */
	public BigDecimal getHinsAmt() {
		return this.hinsAmt;
	}

	/**
	 * 設定高利拆單_共計金額(TWD千元)
	 **/
	public void setHinsAmt(BigDecimal value) {
		this.hinsAmt = value;
	}

	/**
	 * 取得高利拆單_平均利率(%)
	 */
	public BigDecimal getHinsRate() {
		return this.hinsRate;
	}

	/**
	 * 設定高利拆單_平均利率(%)
	 **/
	public void setHinsRate(BigDecimal value) {
		this.hinsRate = value;
	}

	/**
	 * 取得高利拆單_近半年平均金額(TWD千元)
	 */
	public BigDecimal getHinsAvgAmt() {
		return this.hinsAvgAmt;
	}

	/**
	 * 設定高利拆單_近半年平均金額(TWD千元)
	 **/
	public void setHinsAvgAmt(BigDecimal value) {
		this.hinsAvgAmt = value;
	}

	/**
	 * 取得高利拆單_近半年平均利率(%)
	 */
	public BigDecimal getHinsAvgRate() {
		return this.hinsAvgRate;
	}

	/**
	 * 設定高利拆單_近半年平均利率(%)
	 **/
	public void setHinsAvgRate(BigDecimal value) {
		this.hinsAvgRate = value;
	}

	/** 取得前一年底薪轉戶數 **/
	public Long getSalaryAcct() {
		return this.salaryAcct;
	}
	/** 設定前一年底薪轉戶數 **/
	public void setSalaryAcct(Long value) {
		this.salaryAcct = value;
	}

	/** 取得前一年底薪轉戶-信用卡持卡人數 **/
	public Long getSalaryCardAcct() {
		return this.salaryCardAcct;
	}
	/** 設定前一年底薪轉戶-信用卡持卡人數 **/
	public void setSalaryCardAcct(Long value) {
		this.salaryCardAcct = value;
	}

	/**
	 * 取得薪轉信用卡貢獻度起日
	 * <p/>
	 * (年月) YYYY/MM
	 */
	public String getSalaryCardDateS() {
		return this.salaryCardDateS;
	}

	/**
	 * 設定薪轉信用卡貢獻度起日
	 * <p/>
	 * (年月) YYYY/MM
	 **/
	public void setSalaryCardDateS(String value) {
		this.salaryCardDateS = value;
	}

	/**
	 * 取得薪轉信用卡貢獻度迄日
	 * <p/>
	 * (年月) YYYY/MM
	 */
	public String getSalaryCardDateE() {
		return this.salaryCardDateE;
	}

	/**
	 * 設定薪轉信用卡貢獻度迄日
	 * <p/>
	 * (年月) YYYY/MM
	 **/
	public void setSalaryCardDateE(String value) {
		this.salaryCardDateE = value;
	}

	/**
	 * 取得薪轉信用卡_利潤貢獻<p/>
	 * TWD仟元
	 */
	public BigDecimal getSalaryCardPcAmt() {
		return this.salaryCardPcAmt;
	}
	/**
	 *  設定薪轉信用卡_利潤貢獻<p/>
	 *  TWD仟元
	 **/
	public void setSalaryCardPcAmt(BigDecimal value) {
		this.salaryCardPcAmt = value;
	}
}
