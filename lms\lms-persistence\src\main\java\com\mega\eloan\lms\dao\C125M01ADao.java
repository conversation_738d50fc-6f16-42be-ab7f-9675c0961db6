/* 
 * C125M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C125M01A;

/** 勞工紓困信保整批貸款申請書 **/
public interface C125M01ADao extends IGenericDao<C125M01A> {

	C125M01A findByOid(String oid);

	C125M01A findByMainId(String mainId);

	List<C125M01A> findByOwnBrIdCustIdAndDupNo(String ownBrId, String custId,
			String dupNo);

	List<C125M01A> findByOwnBrIdAndBatchDate(String ownBrId, String bgnDate,
			String endDate);

}