initDfd.done(function() {
	gridborrowC();	
	var s41 = $("#thickboxPeo"), s41dialog = $("#s41"), form = $("#tabForm_1"), s41t1t2form = $("#s41t2form"), s41t3form = $("#s41t3form"), s41t4form = $("#s41t4form");
	
    var s41grid, s41t2grid, s41t3grid, s41t4grid;
    var subOid = form.find("#subOid"), subMainId = form.find("#subMainId"), subUid = form.find("#subUid");
	
    s41grid = s41.find("#s41grid").iGrid({
        handler:'lms1201gridhandler',height:180,needPager:false,
        postData:{gridMainId: responseJSON.mainId,gridUid: responseJSON.mainId, formAction:'queryViewA'},
        caption: i18n.lmss08com['ces1405.4101'],//'主要負責人及連保人資信狀況',
        colModel: [{colHeader: i18n.lmss08com['L120S08.grid20'],//"借款人統編",
            name: 'l120m01e.docCustId', sortable: true
        }, {colHeader: i18n.lmss08com['L120S08.grid18'],//"借款人姓名",
            name: 'l120m01e.docDupNo', sortable: true
        }, {colHeader: i18n.lmss08com['ces1405.4102'],//"身分證號碼",
            name: 'pcId', sortable: true
        }, {colHeader: i18n.lmss08com['ces1405.4103'],//"姓名",
            name: 'pcName'
        }, {colHeader: i18n.lmss08com['ces1405.4104'],//"負責人／連保人",
            name: 'pcType'
        }, {colHeader: i18n.lmss08com['ces1405.4105'],//"職稱",
            name: 'pcTitle'
        }, {colHeader: i18n.lmss08com['ces1405.4106'],//"性別",
            name: 'pcSex'
        }, {name: 'oid', hidden: true}, {name: 'mainId', hidden: true}, {name: 'uid', hidden: true}],
        ondblClickRow: function(rowid){
        	var ret = s41grid.getRowData(rowid);
        	openS41Dialog({subOid:ret.oid,subMainId:ret.mainId,subUid:ret.uid});
        }
    });

    //每次開啟都是第一頁
    $("#tabs-ces").tabs({
        selected: 0,
    	select:function(event, ui){
    		if (ui.index != 0) {
				// 進行檢查是否借款人代碼有輸入
				var pcId = strTrim($("#tabForm_1").find("#pcId").val());
				if(pcId == undefined || pcId == null || pcId == ""){
					// L120S08.alert4=請先儲存資料
					CommonAPI.showMessage(i18n.lmss08com['L120S08.alert4']);
					return false;
				}   			
    		} 
			return true;
    	}		
    });

    s41.find("#s41btnAdd").click(function(){//主要-新增
    	openpersonC();
    }).end().find("#s41btnDel").click(function(){//主要-刪除
    	deleteGridRow(s41grid,'41');
    });
	
	// 去頭尾空白
	function strTrim(str) {
	  var start = -1,
	  end = str.length;
	  while (str.charCodeAt(--end) < 33);
	  while (str.charCodeAt(++start) < 33);
	  return str.slice(start, end + 1);
	};	
	
	function deleteGridRow(igrid,ipage){/*刪除選取的grid資料*/
    	var ret = igrid.getSelRowDatas();
        if (ret) {
			CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
			if (b) {					
		        	$.ajax({
		        		handler: 'lms1201formhandler',message: i18n.def.action_003,
			            data: {page:ipage,deleteMainOid:ret.oid,formAction: "delete"},
			            success: function(json){
			            	$("#formL120m01e").find("#docDscr3").html(DOMPurify.sanitize(json.formL120m01e.docDscr3));
							CommonAPI.showPopMessage(i18n.def.confirmDeleteSuccess);
			            	igrid.trigger("reloadGrid");
			            }
			         });
				}				
			});
        }else{
            CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
        }
    }

	function openpersonC(){
		$.ajax({
			handler : "lms1201formhandler",
			type : "POST",
			dataType : "json",
			data : 
			{
				formAction : "checkBorrow",
				mainId : responseJSON.mainId
			},
			success : function(json) {
				uGridborrowC();
				$("#openpersonC").thickbox({     // 使用選取的內容進行彈窗
					title : i18n.lmss08com["L120S08.thickbox15"],
					width : 600,
					height : 500,
					modal : true,
					valign: "bottom",
					align: "center",
					i18n:i18n.def,
					buttons: {             
						"sure": function() {
							var gridId = $("#gridborrowC").getGridParam('selrow');
							var gridData = $("#gridborrowC").getRowData(gridId);
							if(gridId == null){
								CommonAPI.showMessage(i18n.lmss08com['L120S08.alert1']);
							}else{
								$.thickbox.close();
								openS41Dialog({});
								$("#tabForm_1").find("#lienCustId").val(gridData.custId);
								$("#tabForm_1").find("#lienDupNo").val(gridData.dupNo);
							}			    
						},
						"cancel": function() {					 
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}			
				});
			}
		});		
	}
	
	    //登錄主要負責人連保人------------------
    var s41dialogDiv = $("#thickboxPeo"),tabForm = $("#tabForm_1");
    function openS41Dialog(data){
	    //每次開啟都是第一頁
	    $("#tabs-ces").tabs({
	        selected: 0		
	    });		
		var dialogOption = {
			modal: true,
			height: 480,
			width: 940,
			open: function(){
				var cs = tabForm.find("input[name=isRejt]");
				cs.readOnly(true).trigger("click");
				tabForm.find("#currCR,#currDE,#currGU,#mycurr,#Curr,#landCurr,#buCurr").val("TWD").end().find("#amtUnitDE,#amtUnitCR,#amtUnitGU,#myamtUnit,#amtUnit,#landAmtUnit,#buAmtUnit").val("1000");
				
				// Mike 2013.03.04 Add 新增時，持有ROC身份證預設為是
				!data.subOid && tabForm.find("[name='pcCountry'][value='Y']").attr('checked', true) && tabForm.find("#idVerifiedTR").show();				
				
				data.subOid &&
				$.ajax({
					handler: 'lms1201formhandler',
					data: {
						page: '41',
						subOid: data.subOid,
						formAction: "query"
					},
					success: function(json){
						tabForm.injectData(json);
						var claz = cs.attr("class"), readonly = json[claz + "Flag"] != '2';
						cs.readOnly(readonly).end().find("tbody[id^=" + claz + "]").readOnlyChilds(readonly);
						
	                	// Mike 2013.03.04 Add
	                	json["pcCountry"] == 'Y' ? tabForm.find("#idVerifiedTR").show() : tabForm.find("#idVerifiedTR").hide();						
						
						tabForm.find("#s41t2grid,#s41t3grid,#s41t4grid").each(function(){
							$(this).jqGrid('setGridParam', {
								postData: {
									gridMainId: data.subMainId,
									gridUid: data.subUid
								}
							}).trigger("reloadGrid");
						});						
	                	//#1809 (#443)********** 補已刪除資料 ********** start
            			tabForm.find("#s41t7btnReQry,#s41t7btnSave").show();
            			if(tabForm.find("#s41t7btnReQry").length){
            				tabForm.find("#crdD3,#crdLyear,#refTick2,#qryDefault,#defaultRec").readOnly(false);
            			}
	                	//#1809 (#443)********** 補已刪除資料 ********** end						
					}
				});
	        	//if(!data.subOid){
	        		$("#dataSrc").val(i18n.lmss08com["ces1401.msg038"]);
	        	//}				
			},
			close: function(){
				tabForm.reset();
			},
			buttons: API.createJSON([{
				key: i18n.def.saveData,
				value: function(){
					if (tabForm.find("input[name=pcType]").is(":checked")) {
						if (tabForm.valid()) {
							$.ajax({
								handler: 'lms1201formhandler',
								data: $.extend(tabForm.serializeData(), {
									mainOid: s41.find("#mainOid").val(),
									mainId: responseJSON.mainId,
									uid: s41.find("#uid").val(),
									subOid: data.subOid,
									page : "41",
									lienCustId : $("#tabForm_1").find("#lienCustId").val(),
									lienDupNo : $("#tabForm_1").find("#lienDupNo").val(),
									formAction: "tempSave2"
								}),
								success: function(json){
									tabForm.injectData(json);
									s41grid.trigger("reloadGrid");
				                	tabForm.find("#s41t2grid,#s41t3grid,#s41t4grid").each(function(){
										$(this).jqGrid('setGridParam', {
				                        	postData: { gridMainId: json.subMainId, gridUid: json.subUid }
					                    });
									});
		 							 var ids = new Array();
									 ids = $(s41grid).jqGrid('getDataIDs');
									 var list = "";
									 var sign = ",";
									 var count = 0;
									 for ( var id in ids) {								 
								 		 var rows = $(s41grid).jqGrid('getRowData',	ids[id]);					 		 
										 if (rows.pcName != 'undefined'&& rows.pcName != null&& rows.pcName != 0) {
											 list += ((list == "") ? "": sign)+ rows.pcName;
										 }
										 count++;
									 }									
				                      $.ajax({
				                          handler: 'lms1201formhandler',
				                          data: $.extend(form.serializeData(), {
				                          	formAction : "saveAndSetGua",
											  formL120m01e : JSON.stringify($("#formL120m01e").serializeData()),
				                              mainOid: $("#mainOid").val(),
				                              mainId: responseJSON.mainId,
				                              pcNames : list
				                          }),
				                          success: function(json){
				                              //$("#formL120m01e").setData(json.formL120m01e,false);
											  $("#formL120m01e").find("#docDscr3").html(DOMPurify.sanitize(json.formL120m01e.docDscr3));
				                              API.showMessage(i18n.def.saveSuccess);
				                          }
				                      });		                              		                              
		                              //$.thickbox.close();
								}
							});
						}
					}
					else {
						CommonAPI.showErrorMessage(i18n.lmss08com["ces1405.4104"] + i18n.def["val.required"]);
					}
				}
			}, {
				key: i18n.def.close,
				value: function(){
					$.thickbox.close();
				}
			}])
		};
		$("#tabForm_1").reset();
		$("#s41dialog").thickbox(dialogOption);
	}
	
	// 以下開始實作連保人細部功能
	tabForm.bind('reset',function(){
    	tabForm.find("#s41t2grid,#s41t3grid,#s41t4grid").clearGridData();
    });

    //本人及配偶資料----------------
    var s41t1 = tabForm.find("#s41t1");
    s41t1.find("input[name=isRejt]").click(function(){
    	var cs = $(this), claz=cs.attr("class");
    	s41t1.find("#" + claz + cs.val()).show().siblings("[id^=" + claz + "]").hide();
    }).end().find("#pcId").blur(function(){//統一編號
    	if ($(this).valid()) {
    		s41t1.find("input[name=pcSex]").attr('checked', false).filter("[value='"+$(this).val().substr(1, 1) +"']").trigger('click').attr("checked", true);
        } else {
        	s41t1.find("input[name=pcSex]").attr('checked', false);
        }
    }).end().find("#s41t1btnPullin").click(function(){//引進負責人/連保人基本資料
    	API.includeIdCes({
            autoResponse: { "id": "pcId", "dupno": "pcDupNo", "name": "pcName"},
			defaultValue:s41t1.find("#pcId").val(),
            btnAction: function(id, dupno, name){
                $.ajax({
                    handler: 'lms1201formhandler',
                    data: { pcId: id, dupNo: dupno, formAction: "getCustInfo" },
                    success: function(json){
                    	s41t1.injectData(json);
                		var cs = s41t1.find("input[name=isRejt]"), claz=cs.attr("class"), readonly = json[claz+"Flag"]!='2';
                		cs.readOnly(readonly).end().find("tbody[id^="+claz+"]").readOnlyChilds(readonly);
                    }
                });
            }
        });
    }).end().find("#pcCountry").change(function(){ // 國民身份證資料是否與內政部戶政司檔存資料相符 Mike 2013.03.04 Add
    	if ($(this).val() == 'Y') {
    		tabForm.find("#idVerifiedTR").show();
    	} else {
    		tabForm.find("#idVerifiedTR").hide();
    		tabForm.find("#idVerified").attr("checked", false);
    		tabForm.find("#idVerifiedDate").val("");
    	}
    });
	
    //經營事業----------------
    var s41t2 = tabForm.find("#s41t2") , s41t2form = $("#s41t2form") ;
    s41t2.find("#s41t2btnAdd").click(function(){ //新增
    	var records = s41t2grid.getGridParam("records");
        if(records>=5){
        	API.showMessage(i18n.msg('EFD2039',{'item':'','maxRows':'5'}));//$\{item\}明細資料最多$\{maxRows\}筆
        	return;
        }
        openSubDetail(s41t2form,'s41t2dialog',300,600,s41t2grid,{
        	qryAction:'query41S',saveAction:'save41S',type:'A',subOidS:'',mainId: responseJSON.mainId,subOid:tabForm.find("#subOid").val()
			, subMainId:tabForm.find("#subMainId").val(), subUid:tabForm.find("#subUid").val()
    	});
    }).end().find("#s41t2btnDel").click(function(){//刪除
    	deleteSubDetail(s41t2grid,'A');
    }).end().find("#s41t2f1btn").click(function(){	//引進經營事業資料
		$.ajax({
			handler:"lms1201formhandler", action:"getAcm009s",
			data:{subOid:tabForm.find("#subOid").val()},
			success:function(json){
				s41t2grid.trigger("reloadGrid");
			}
		});
	}).end().find("#s41t2btnQryEjcic").click(function(){
		var custIds = [];
		custIds = [s41t1.find("#pcId").val()]
		$.form.submit({
			url : '../../base/ejcicm01/01',
			data : {qryCustIds:custIds},
			target : '1401s41'+custIds[0]
		});
	});
    //本人之土地-----------
    var s41t3=tabForm.find("#s41t3"),s41t3form = $("#s41t3form");
    s41t3.find("#s41t3btnAdd").click(function(){//新增
    	var records = s41t3grid.getGridParam("records");
        if(records>=3){////$\{item\}明細資料最多$\{maxRows\}筆
        	API.showMessage(i18n.msg('EFD2039',{'item':'','maxRows':'3'}));
        	return;
        }
        openSubDetail(s41t3form,'s41t3dialog',580,750,s41t3grid,{
        	qryAction:'query41S',saveAction:'save41S',type:'B',subOidS:'',mainId: responseJSON.mainId,subOid:tabForm.find("#subOid").val()
			, subMainId:tabForm.find("#subMainId").val(), subUid:tabForm.find("#subUid").val()
    	});
		// TODO
		if(tabForm.find("#cmsCode").val()){
			tabForm.find("#cmsKind").setOptions("cmsKind"+tabForm.find("#cmsCode").val(),false);
			json['cmsKind'] && tabForm.find("#cmsKind").val(json['cmsKind']);
		}		
    }).end().find("#s41t3btnDel").click(function(){
    	deleteSubDetail(s41t3grid,'B');
    });
    s41t3form.find("#city").change(function(){ //座落所在區段-縣市-鄉鎮
    	s41t3form.find("#zone").setOptions("counties" + $(this).val(), false);
    }).end().find("#landUse1").change(function(){ //用途-使用分區-種類
    	s41t3form.find("#landUse2").setOptions("LandUse2" + $(this).val(), false);
    }).end().find("#landLevel").change(function(){//地目等則
    	($(this).val() == '99') ? s41t3form.find("#landCust").show() : s41t3form.find("#landCust").hide().val('');
    }).end().find("#landBp").blur(function(){ //(坪)
    	numConP2M(s41t3form,'landBp', 'landBm', 'landUnit1');
    }).end().find("#landAp").blur(function(){ //(坪)
    	numConP2M(s41t3form,'landAp', 'landAm', 'landUnit2');
    }).end().find("#landBm").blur(function(){
    	numConM2P(s41t3form,'landBp', 'landBm', 'landUnit1');
    }).end().find("#landAm").blur(function(){
    	numConM2P(s41t3form,'landAp', 'landAm', 'landUnit2');
    }).end().find("#landUnit1").change(function(){
    	s41t3form.find("#landBp").trigger('blur');
    }).end().find("#landUnit2").change(function(){
    	s41t3form.find("#landAp").trigger('blur');
    }).end().find("#s41t3btnCnt").click(function(){//持分面積-計算
    	var _landRateC = s41t3form.find("#landRateC").val(),_landRateD = s41t3form.find("#landRateD").val();
    	if (_landRateC == '' || _landRateD == '') {
            CommonAPI.showErrorMessage(i18n.msg["EFD2047"]);
        } else {
        	var q = /[,]/g;
        	var _landBp = s41t3form.find("#landBp").val(),_landBm = s41t3form.find("#landBm").val();
        	_landRateC = parseFloat(String(_landRateC).replace(q,""));
        	_landRateD = parseFloat(String(_landRateD).replace(q,""));
        	_landBp = parseFloat(String(_landBp).replace(q,""));
        	_landBm = parseFloat(String(_landBm).replace(q,""));
        	var landAp = Math.floor((_landRateC/_landRateD)*_landBp*100)/100;
        	var landAm = Math.floor((_landRateC/_landRateD)*_landBm*100)/100;
            s41t3form.find("#landAp").val(isNaN(landAp)?"":landAp);
            s41t3form.find("#landAm").val(isNaN(landAm)?"":landAm);
            s41t3form.find("#landUnit2").val(s41t3form.find("#landUnit1").val());
        }
    });
	s41t3form.find(".landAddr").blur(function(){
		var city = s41t3form.find("#city option:selected");
		var zone = s41t3form.find("#zone option:selected");
		var section = s41t3form.find("#section");
		var sSection = s41t3form.find("#sSection");
		//#1774(#436)
		var addr = (city.val()?city.attr("showvalue"):"")
			+(zone.val()?zone.attr("showvalue"):"")
			+(section.val()?section.val()+i18n.lmss08com['ces1401.41139']:"")
			+(sSection.val()?sSection.val()+i18n.lmss08com['ces1401.41140']:"");
		s41t3form.find("#landAddr").val(addr);
	});	
    //本人之建物--------------
    var s41t4=tabForm.find("#s41t4"),s41t4form = $("#s41t4form");
    s41t4.find("#s41t4btnAdd").click(function(){
    	var records = s41t4grid.getGridParam("records");
        if(records>=3){
			API.showMessage(i18n.msg('EFD2039').replace(/\${item}/,'').replace(/\${maxRows}/,'3'));
			//API.showMessage(i18n.msg('EFD2039',{'item':'','maxRows':'3'}));
        	return;
        }
        openSubDetail(s41t4form,'s41t4dialog',500,700,s41t4grid,{
        	qryAction:'query41S',saveAction:'save41S',type:'C',subOidS:'',mainId: responseJSON.mainId,subOid:tabForm.find("#subOid").val()
			, subMainId:tabForm.find("#subMainId").val(), subUid:tabForm.find("#subUid").val()
    	});
    }).end().find("#s41t4btnDel").click(function(){
    	deleteSubDetail(s41t4grid,'C');
    });
    s41t4form.find("#buP").blur(function(){//(坪)
    	numConP2M(s41t4form,'buP', 'buBuM', 'buUnit');
    }).end().find("#buBuM").blur(function(){
    	numConM2P(s41t4form,'buP', 'buBuM', 'buUnit');
    }).end().find("#buUnit").change(function(){
    	s41t4form.find("#buP").trigger('blur');
    });
    //經濟狀況-------------
    var s41t5 = tabForm.find("#s41t5");
	var bnksTbs = false;
    s41t5.find("#s41t5btn").click(function(){//引進本行金額
        $.ajax({
            handler: 'lms1201formhandler',
            data: { pcId: s41t1.find("#pcId").val(),pcDupNo: s41t1.find("#pcDupNo").val(),formAction: "getEldpf" },
            success: function(json){ s41t5.injectData(json); }
        });
    }).end().find("#s41t5btnCountTot").click(function(){//計算合計
    	$.ajax({
            handler: "lms1201formhandler",
            data: {
                AMT: [s41t5.find("#depAmnt1").val(), s41t5.find("#depAmnt2").val(), s41t5.find("#depAmnt3").val()],
				formAction: "countTot"
            },
            success: function(json){ s41t5.find("#dep_Tot_amnt").val(json.result); }
        });
    }).end().find("#s41t5btn2CountTot").click(function(){//計算合計
    	$.ajax({
            handler: "lms1201formhandler", action: "countTot",
            data: {
                AMT: [s41t5.find("#borAmnt1").val(), s41t5.find("#borAmnt2").val(), s41t5.find("#borAmnt3").val()]
            },
            success: function(json){ s41t5.find("#bor_Tot_amnt").val(json.result); }
        });
    }).end().find("#currDE").change(function(){
		s41t5.find("select[class^=cCur]").val($(this).val());
	}).end().find("#amtUnitDE").change(function(){
		s41t5.find("select[class^=cUnit]").val($(this).val());
	}).end().find('input[name^="depNo"]').each(function(){
		$(this).blur(function(){				
				$.ajax({
					handler: "lms1201formhandler",
					global:false,
					data: {bid_1_1: $(this).val(),formAction: "s71queryBs1_1Nm", id:"depName"+$(this).attr("id").substring(5)},
					success: function(d){
						d.colNm && $("#"+d.colNm).val(d.bs1_1);
					}
				});
			});
	}).end().find("#s41t5BnkNmbtn").click(function(){
		//銀行代號名稱對照表
		var bnksDiv = s41t5.find("#s41t5BnkNmtb");
		function openbnksDiv(){
			bnksDiv.thickbox({model:false,height:640,width:780,open:function(){},
				buttons:API.createJSON([{key:i18n.def.close, value:function(){$.thickbox.close();}}])
			});
		}
		if (!bnksTbs) {
			$.ajax({
				handler: "lms1201formhandler",
				action: "getBnkNmtb",
				success: function(json){
					if (json.bnks) {
						var bnks = json.bnks;
						var body = "<table id='bnksTable' class='tb2' width='100%' border='0' cellspacing='0' cellpadding='0'><tr>";
						var n = 0;
						for (var i = 1; i <= 4; i++) {
							body += "<td width='25%'><table class='tb2' width='100%' border='0' cellspacing='0' cellpadding='0'>";
							for (var j = 1; j <= 25; j++) {
								var bnk = DOMPurify.sanitize(bnks[n++]);
								body += ("<tr><td style='height:28px'><span>" + (bnk===undefined?"":bnk) + "</span></td></tr>");
							}
							body += "</table></td>";
						}
						body += "</tr></table>";
						bnksDiv.append(body);
						bnksTbs = true;openbnksDiv();
					}
				}
			});
		}else openbnksDiv();
	}).end().find("#s41t5f3btn").click(function(){
		var getELaList = function(json){
			$.ajax({
	            handler: "lms1201formhandler",
	            action: "getLoanAndGuarantee",
				data: {
					pcId: tabForm.find("#pcId").val(),
					DD1:json.DD1
				},
				timeout:60 * 1000 * 10,
	            success: function(d){
					s41t5.injectData(d);
					d.proNote1 && s41t5.find("#proNote1").html(d.proNote1);
	            }
	        });
		}
		$.ajax({
            handler: "lms1201formhandler",
            action: "checkcpLoanDate",
			data: s41t5.serializeData(),
            success: function(d){
				s41t5.injectData(d);
				if(d.confirmMsg){
					API.confirmMessage(d.confirmMsg, function(result){
			            if(result){
							getELaList(d);
						}
			        });
				}else{
					getELaList(d);
				}
            }
        });
	}).end().find("#s41t5f3btnQryEjcic").click(function(){	//聯徵查詢
		var custIds = [];
		custIds = [s41t1.find("#pcId").val()]
		$.form.submit({
			url : '../../base/ejcicm01/01',
			data : {qryCustIds:custIds},
			target : '1401s41'+custIds[0]
		});
	});
    //信用情形及商場風評-------------
    var s41t7 = tabForm.find("#s41t7");
	
    //#1809 (#443)********** 補已刪除資料 ********** start
    s41t7.find("#s41t7btnReQry").click(function(){/*re查詢負責人(連保人)信用情形*/
    	s41t7.find("#s41t7btnQry").trigger('click');
    }).end().find("#s41t7btnSave").click(function(){/*僅儲存查詢負責人(連保人)信用情形*/
    	$.ajax({
            handler: "lms1201formhandler", action: "tempSave41_8",
            data: $.extend(s41t7.serializeData(), {
                subOid: tabForm.find("#subOid").val()
            }),
            success: function(json){
                API.showMessage(i18n.def.saveSuccess);
            }
        });
    });
    //#1809 (#443)********** 補已刪除資料 ********** end	
	
    s41t7.find("#isCredit").click(function(){/*信用情形*/
    	$(this).val($(this).is(":checked") ? "0" : "1");
        s41t7.find("#Div" + $(this).attr("name"))[$(this).is(":checked") ? "hide" : "show"]();
    }).end().find("#s41t7btnQry").click(function(){//查詢負責人(連保人)信用情形
    	var custId = s41t1.find("#pcId").val();
		if(custId) custId = $.trim(custId);
		function getCreditStat(custId) {
			//取得信用情形
	        $.ajax({
	            handler: "lms1201formhandler",action: "getCreditStatus",
	            data: { ID2: custId},
	            success:function(json){ 
	            	s41t7.injectData(json); 
	            	(json.msg) && CommonAPI.showErrorMessage(json.msg);
	            }
	        });
		};
		ilog.debug("custId ==> "+ custId);
		if(custId  && ((custId.length==10 && (/^[a-zA-Z]{2}/i.test(custId)) || /([0-9][0-9][Z])/.test(custId)))){
			CommonAPI.confirmMessage(i18n.lmss08com('ces1401.41153', {'ID':custId}), function(result){
	            if (result) {
			        $.ajax({
			            handler: "lms1201formhandler",action: "getEjcicId",
			            data: { custId: s41t1.find("#pcId").val()},
			            success:function(json){
							custId = json.ID2;	//s41t7.injectData(json);
//							ilog.debug(json.ID2);
			            	(json.msg) && CommonAPI.confirmMessage(json.msg, function(result){
								result && getCreditStat(custId);
							});
			            }
			        });
				}else{
					getCreditStat(custId);
//					API.showMessage(i18n.lmss08com['ces1401.41155']);
				}
			});
		}else
			getCreditStat(custId);
    }).end().find("#s41t7btnQryEjcic").click(function(){	//聯徵查詢
		var custIds = [];
		custIds = [s41t1.find("#pcId").val()]
		$.form.submit({
			url : '../../base/ejcicm01/01',
			data : {qryCustIds:custIds},
			target : '1401s41'+custIds[0]
		});
	}).end().find("#s41t7btnQryEtch").click(function(){	//票信查詢
		var custId = s41t1.find("#pcId").val(),custName = s41t1.find("#pcName").val();
		$.form.submit({
			url : '../../base/etchm01/01',
			data : {custId:custId, custName:custName},
			target : '1401s41'+custId
		});
	}); 
    //主債務、共同債務及保證債務情形--------------------
    var s41t9 = tabForm.find("#s41t9");
    s41t9.find("#s41t9btnImLoan").click(function(){
    	$.ajax({
            handler: "lms1201formhandler",
            data: {custId: s41t1.find("#pcId").val(),dupNo: s41t1.find("#pcDupNo").val(),formAction: "s07ImLoanData"},
            success: function(json){s41t9.injectData(json);}
        });
    });
    
    var s41t2grid = tabForm.find("#s41t2grid").iGrid({/*經營事業*/
        handler:'lms1201gridhandler',height: 120,needPager: false,localFirst: true,
        postData: {gridMainId: '',gridUid: '', formAction: 'queryView41SA'},
        colModel: [{colHeader: i18n.lmss08com['ces1405.4107'],//"其他投資事業名稱",
            name: 'invBusna1',width: 100
        }, {colHeader: i18n.lmss08com['ces1405.4108'],//"擔任職務",
            name: 'invKind1',align: 'center',width: 100
        }, {colHeader: i18n.lmss08com['ces1405.4109'],//"幣別",
            name: 'invCap11',width: 100, align: 'center'
        }, {colHeader: i18n.lmss08com['ces1405.4110'],//"實收資本額",
            name: 'invUnit11',width: 100,align: 'right',
            formatter: 'number',formatoptions: {decimalSeparator: ",",thousandsSeparator: ",",decimalPlaces: 0}
        }, {colHeader: i18n.lmss08com['ces1405.4111'],//"單位",
            width: 50, align: 'center',name: 'invCap21'
        }, {colHeader: i18n.lmss08com['ces1405.4112'],// "實有股份/出資額",
            width: 100,align: 'right',name: 'invUnit21',
            formatter: 'number',formatoptions: {decimalSeparator: ",",thousandsSeparator: ",",decimalPlaces: 0}
        }, {colHeader: i18n.lmss08com['ces1405.4113'],//"單位",
            width: 50,align: 'center',name: 'amtUnitST', formatter: function(data){
				if(data == ""){
					return i18n.lmss08com['ces1405.41127'];
				}else{
					return data;
				}
			}
        }, {name: 'oid',hidden: true},{name: 'mainId', hidden: true},{name: 'pid', hidden: true}],
        ondblClickRow: function(rowid){
        	var ret = s41t2grid.getRowData(rowid);
        	openSubDetail(s41t2form,'s41t2dialog',300,600,s41t2grid,{
        		qryAction:'query41S',saveAction:'save41S',type:'A',mainId: responseJSON.mainId,subOidS:ret.oid,
        		subOid:tabForm.find("#subOid").val(),subUid:ret.pid,subMainId:ret.mainId
        	});
        }
    });
    
    var s41t3grid = tabForm.find("#s41t3grid").iGrid({/*土地*/
        handler: 'lms1201gridhandler',height: 75,needPager: false,localFirst: true,
        postData: {gridMainId: '',gridUid: '',formAction: 'queryView41SB'},
        colModel: [{colHeader: i18n.lmss08com['ces1405.4114'],//"座落地址",
            name: 'landAddr',width: 180
        }, {colHeader: i18n.lmss08com['ces1405.4115'],//"用途",
            name: 'landUse',width: 100
        }, {colHeader: i18n.lmss08com['ces1405.4116'],//"地目等則",
            name: 'landLevel',width: 70,align: 'center'
        }, {colHeader: i18n.lmss08com['ces1405.4117'],//"地號",
            name: 'landNum',width: 50,align: 'center'
        }, {colHeader: i18n.lmss08com['ces1405.4118'],//"持分",
            name: 'landRate',width: 50,align: 'center'
        }, {colHeader: i18n.lmss08com['ces1405.4119'],//"總面積",
            name: 'landBp',width: 80,align: 'center',
            formatter: function(val){
                return val == null ? '' : util.addComma(val) + i18n.lmss08com['ces1405.4125'];
            }
        }, {colHeader: i18n.lmss08com['ces1405.4120'],//"持分面積",
            name: 'landAp',width: 80,align: 'center',
            formatter: function(val){
                return val == null ? '' : util.addComma(val) + i18n.lmss08com['ces1405.4125'];
            }
        }, {colHeader: i18n.lmss08com['ces1405.4121'],//"他項權利",
            name: 'landMp',width: 250
        }, {name: 'oid',hidden: true},{name: 'mainId', hidden: true},{name: 'pid', hidden: true}],
        ondblClickRow: function(rowid){
        	var ret = s41t3grid.getRowData(rowid);
        	openSubDetail(s41t3form,'s41t3dialog',580,750,s41t3grid,{
        		qryAction:'query41S',saveAction:'save41S',type:'B',mainId: responseJSON.mainId,subOidS:ret.oid,
        		subOid:tabForm.find("#subOid").val(),subUid:ret.pid,subMainId:ret.mainId
        	});
        }
    });
    
    var s41t4grid = tabForm.find("#s41t4grid").iGrid({/*建物*/
        handler: 'lms1201gridhandler',height:75,needPager:false,localFirst:true,
        postData: {gridMainId: '',gridUid: '',formAction:'queryView41SC'},
        colModel: [{colHeader: i18n.lmss08com['ces1405.4114'],//"座落地址",
            name: 'buAddr',width: 200
        }, {colHeader: i18n.lmss08com['ces1405.4115'],//"用途",
            name: 'buUse',width: 120
        }, {colHeader: i18n.lmss08com['ces1405.4122'],//"建號",
            name: 'buNum',width: 120,align: 'center'
        }, {colHeader: i18n.lmss08com['ces1405.4123'],//"構造",
            name: 'buStru',width: 120,align: 'center'
        }, {colHeader: i18n.lmss08com['ces1405.4124'],//"面積",
            name: 'buP',width: 150,align: 'center',
            formatter: function(val){return val == null ? '' : util.addComma(val) + i18n.lmss08com['ces1405.4125'];}
        }, {colHeader: i18n.lmss08com['ces1405.4121'],//"他項權利",
            name: 'buMp',width: 250
        }, {name: 'oid',hidden: true},{name: 'mainId', hidden: true},{name: 'pid', hidden: true}],
        ondblClickRow: function(rowid){
        	var ret = s41t4grid.getRowData(rowid);
        	openSubDetail(s41t4form,'s41t4dialog',500,700,s41t4grid,{
        		qryAction:'query41S',saveAction:'save41S',type:'C',mainId: responseJSON.mainId,subOidS:ret.oid,
        		subOid:tabForm.find("#subOid").val(),subUid:ret.pid,subMainId:ret.mainId
        	});
        }
    });
    
    function numConP2M(form,p, m, unit){
        if (!form.find("#" + p).valid() || form.find("#" + p).val() == '') {return;}
        $.ajax({
            handler: "lms1201formhandler",
            data: {p2m: form.find("#" + p).val(),unit: form.find("#" + unit).val(),formAction: "numConvert"},
            success: function(json){form.find("#" + m).val(json.area);}
        });
    }
    
    function numConM2P(form,p, m, unit){
        if (!form.find("#" + m).valid() || form.find("#" + m).val() == '') {return;}
        $.ajax({
            handler: "lms1201formhandler",
            data: {m2p: form.find("#" + m).val(),unit: form.find("#" + unit).val(),formAction: "numConvert"},
            success: function(json){form.find("#" + p).val(json.area);}
        });
    }
	
});

function saveAction(json){
    return $.ajax($.extend({
        handler: "lms1201formhandler",formAction: "save",
        success: function(json){
            window.name = json.mainOid;
            setRequiredSave(false);
        }
    }, json || {}));
}

function openSubDetail(subForm,dialog,_height,_width,grid,data){
    if (!data.subOid) {CommonAPI.showErrorMessage(i18n.def["requireSave"]);return;}
    subForm.find("#subOid"+data.type).val(data.subOidS);
    $("#"+dialog).thickbox({
        modal: true,height:_height,width:_width,
        open: function(){
			subForm.find("#invCap11,#landCurr,#buCurr").val("TWD").end().find("#invCap21,#landAmtUnit,#buAmtUnit").val("1000");
        	data.subOidS &&
            $.ajax({
                handler: 'lms1201formhandler',
                data: {type: data.type,subOidS: data.subOidS,formAction: data.qryAction},
                success: function(json){
                	subForm.injectData(json);
                	if(data.type=='B'){
                		subForm.find("#landLevel, #landUse1").change().end().find("#landUse2").val(json.landUse2);
						//#815
						subForm.find("#city").focusout();
                		subForm.find("#city, #zone").change().end().find("#zone").val(json.zone);
                	}
                }
            });
        	data.type == 'A'&&subForm.find("select[class^=spAmtUnit]").setOptions({0: i18n.lmss08com['ces1405.41127']}, true);
        },
        close:function(){
			subForm.find("select[class^=spAmtUnit]").removeOptions(['0']);
        	subForm.reset();
        },
        buttons: API.createJSON([{
            key: i18n.def.saveData,
            value: function(){
                if (subForm.valid()) {
                    $.ajax({
                        handler: 'lms1201formhandler',
                        data: $.extend(subForm.serializeData(), {
                            page:data.type,subOidS:data.subOidS,
                            subOid:data.subOid,mainOid:$("#thickboxPeo").find("#mainOid").val(),
							formAction: data.saveAction
                        }),
                        success: function(json){
                            grid.jqGrid('setGridParam', {
                                postData: {gridMainId:data.subMainId,gridUid: data.subUid}
                            }).trigger("reloadGrid");
                            $.thickbox.close();
                        }
                    });
                }
            }
        }, {key: i18n.def.close,
            value: function(){$.thickbox.close();subForm.reset();}
        }])
    });
}

function deleteSubDetail(grid,type){
	var ret = grid.getSelRowDatas();
    if (ret) {
		API.flowConfirmAction({
			message: i18n.def["action_003"],
			handler: 'lms1201formhandler',
			data: {
				type: type,
				deleteSubOid: ret.oid,
				formAction: "deleteS"
			},
			success: function(){
				CommonAPI.showPopMessage(i18n.def["confirmDeleteSuccess"]);
				grid.trigger("reloadGrid");
			}
		});
    } else {
        CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
    }
}

function uGridborrowC(){
    $("#gridborrowC").jqGrid("setGridParam", {
        postData: {
            formAction: "queryL120s01aToGetData",
            rowNum: 10
        },
        search: true
    }).trigger("reloadGrid");
}

function gridborrowC(){
	$("#gridborrowC").iGrid({
		handler: 'lms1201gridhandler',
		//width: "100px",
		height: 350,
		sortname : 'custId',
		postData : {
			formAction : "queryL120s01aToGetData",
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		colModel: [{
			colHeader: i18n.lmss08com["L120S08.grid17"],//"身分證統編"
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'custId' //col.id
		},{
			colHeader : i18n.lmss08com["L120S08.grid18"],//"借款人姓名"
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		},{
			colHeader: i18n.lmss08com["L120S08.grid19"],//"最後異動日期"
			align : "center",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'updateTime' //col.id
		}, {
			colHeader : "dupNo",
			name : 'dupNo',
			hidden : true
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}],
		ondblClickRow: function(rowid){
		}
	});
}