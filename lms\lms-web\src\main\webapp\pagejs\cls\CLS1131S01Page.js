//set String default
//trim
String.prototype.trim = function(){
    return (this.replace(/^[\s\xA0]+/, '').replace(/[\s\xA0]+$/, ''))
}
// startsWith
String.prototype.startsWith = function(str){
    return (this.match('^' + str) == str)
}
// endsWith
String.prototype.endsWith = function(str){
    return (this.match(str + '$') == str)
}

var CLS1131S01 = {
    //是否為C120M01A
    isC120M01A: false,
    isC122M01A: false,
    autoCheck_showPage: false,
    autoCheck_bankManFlag: false,
    handler: 'cls1131formhandler',
    readOnly: false,
    ready: false,
    manual: false,
    c122m01a_mainId: '',
    c122m01a_mainOid: '',
    c122m01a_oid: '',
    applyKind:'',
    c122m01a_mainId_applyKindB: '',	
    c122m01a_mainId_applyKind_PLOAN: '',
    //isPopupResultWindow:'N',//設定一鍵查詢是否跳出查詢視窗初值 => 不使用 CLS1131S01.isPopupResultWindow 而是用 CLS1131S01.data.isPopupResultWindow
    defined_etch_timeout : 800,
    defined_ejcic_timeout : 850,
    forms: ['C101M01AForm', 'C101S01AForm', 'C101S01BForm', 'C101S01CForm', 'C101S01DForm', 'C101S01EForm', 'C101S01FForm', 'C101S01VForm', 'C101S01ZForm', 'C101S02CForm'],
    /**
     * 資料
     */
    data: {},
    /**
     * 項目
     */
    items: {},
    /**
     * 初始化
     */
    init: function(){
        CLS1131S01.manual = false;
        if($('#C101S01AForm').find("#checkListFlag:checked").val()=="Y"){
			$("#liCLS1131S01V").attr("style","display:block;");
		}else{
			$("#liCLS1131S01V").attr("style","display:none;");
		}
		
		isShowRelatedDataItem31to34();
		
		this.getProdSelect();
    },
    /**
     * 建構
     */
    build: function(){
    	ilog.debug("@CLS1131S01Page.js > build");
        //取得帳號權限組
        $.ajax({
            handler: 'cls1131formhandler',
            action: 'check_only_expermission',
            success: function(responseData){
            	if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
                	$("#"+i18n.def['print']).hide();
            	}
            }
        });
        // 取得相關之codeType
        CLS1131S01.items = CommonAPI.loadCombos(['CountryCode', 'Common_Currcy', 'lms1205s01_RelClass', 'Relation_type31', 'Relation_type32', 'Relation_type1', 'Relation_type2', 'lms1205s01_CustPos', 'lms1205s01_edu', 'lms1205s01_houseStatus', 'lms1205s01_cmsStatus', 'jobType', 'cls_mateFlag', 'Common_YesNo', 'Common_YesNo2', 'cls_credit', 'lms1205s01_jobTitle', 'lms1205s01_inDoc', 'lms1205s01_oIncome', 'lms1205s01_yIncomeCert', 'lms1205s01_mJobKind', 'Education', 'sex', 'marry', 'YesNo', 'YesNoNa', 'HaveNoNa', 'counties', 'lms_rejCase', 'C101S02B_HML', 'C101S02B_LAA']);
        
        $('#CLS1131S01ThickBox').buildItem();
        //============================
    	// 處理下拉選單排序
    	if(true){
    		var juType_v = $("select#juType").attr("data-codetype") ||'';		
    		if(juType_v){
    			$.ajax({
    				type : "POST", handler : "lms1015m01formhandler", async: false,//用「同步」的方式
    				data : {
    					formAction : "codeTypeWithOrder" ,
    					key : juType_v
    				},
    				success:function(obj){
    					var chooseItem1 = $("select#juType");
    					var _addSpace = false;
    					if(chooseItem1.attr("space")=="true"){
    						_addSpace = true;	
    					}
    					
    					$.each(obj.itemOrder, function(idx, c_val) {
    						var currobj = {};
    						var c_desc = obj.item[c_val];
    						currobj[c_val] = c_desc;
    				
    						//select
    						chooseItem1.setItems({ item: currobj, format: "{key}", clear:false, space: (_addSpace?(idx==0):false) });
    					});
    				}
    			});
    		}
    	}
    	
    	if (true) {
			var juType_v = $("select#clsJobTitle").attr("data-codetype") || '';
			if (juType_v) {
				$.ajax({
					type : "POST",
					handler : "lms1015m01formhandler",
					async : false,// 用「同步」的方式
					data : {
						formAction : "codeTypeWithOrder",
						key : juType_v
					},
					success : function(obj) {
						var chooseItem1 = $("select#clsJobTitle");
						var _addSpace = false;
						if (chooseItem1.attr("space") == "true") {
							_addSpace = true;
						}

						$.each(obj.itemOrder, function(idx, c_val) {
							var currobj = {};
							var c_desc = obj.item[c_val];
							currobj[c_val] = c_desc;

							// select
							chooseItem1.setItems({
								item : currobj,
								format : "{key}",
								clear : false,
								space : (_addSpace ? (idx == 0) : false)
							});
						});
					}
				});
			}
		}
		if (true) {
			var juType_v = $("select#clsJobType1").attr("data-codetype") || '';
			if (juType_v) {
				$.ajax({
					type : "POST",
					handler : "lms1015m01formhandler",
					async : false,// 用「同步」的方式
					data : {
						formAction : "codeTypeWithOrder",
						key : juType_v
					},
					success : function(obj) {
						var chooseItem1 = $("select#clsJobType1");
						var _addSpace = false;
						if (chooseItem1.attr("space") == "true") {
							_addSpace = true;
						}

						$.each(obj.itemOrder, function(idx, c_val) {
							var currobj = {};
							var c_desc = obj.item[c_val];
							currobj[c_val] = c_desc;

							// select
							chooseItem1.setItems({
								item : currobj,
								format : "{key}",
								clear : false,
								space : (_addSpace ? (idx == 0) : false)
							});
						});
					}
				});
			}
		}
    	
//    	if(true){
//    		var item = API.loadOrderCombosAsList("cls1131m01_othType")["cls1131m01_othType"];
//        	$("#othType").setItems({ size: "1", item: item, clear: true, itemType: 'checkbox' })
//    	}
    	 
        // 建構Form
        CLS1131S01.Master_build(); // 基本資訊
        CLS1131S01.A_build(); // 基本資料
        CLS1131S01.B_build(); // 服務單位
        CLS1131S01.C_build(); // 債償能力
        CLS1131S01.D_build(); // 配偶資料
        CLS1131S01.E_build(); // 相關資料/信用情形查詢
        CLS1131S01.F_build(); // 個人放款信用評分表
        CLS1131S01.V_build(); // 申貸資料核對表
        CLS1131S01.X_build(); // 勞工紓困4.0
        CLS1131S01.T_build(); // 系統初評
        CLS1131S01.Z_build(); // 信貸集中徵信
        return true;
    },
    /**
     * 基本資訊 ======================================================================
     */
    Master_build: function(){
        var $C101M01AForm = $('#C101M01AForm');
        $C101M01AForm.find('#btViewAbnormalDoc').click(function(){
        	
        	//在一份簽報書有多個借款人, 要多加上 idDup
        	$.ajax({
                handler: CLS1131S01.handler,
                /*
				可能 handler 是 cls1131formhandler(C101M01A) 或 cls1141formhandler(C120M01A)
				所以兩邊都要加上 action method
                */
                action: 'getAbnormalDocParam',
                data: { 'mainId':CLS1131S01.data.mainId
                	, 'custId':CLS1131S01.data.custId
                	, 'dupNo':CLS1131S01.data.dupNo 
                },
                success: function(json_param){
                	if(json_param.findDoc=="Y"){
                		var open_docURL_prefix = "";
                    	if(CLS1131S01.handler=="cls1131formhandler"){
                    		open_docURL_prefix = "..";
                    	}else if(CLS1131S01.handler=="cls1141formhandler"){
                    		open_docURL_prefix = "../..";
                    	}
                    	
                    	var new_url = open_docURL_prefix + (json_param.open_docURL||"")+"/02";
                    	var new_data = $.extend({'noOpenDoc':true}, json_param.url_data||{});
                    	var new_target = new_data.mainOid||"_blank";
                		$.form.submit({ url: new_url, data:new_data, target:new_target});
                	}else{
                		API.showErrorMessage(i18n.def.noData);                	
                	}                	
                }
            });
        });
        $C101M01AForm.find('#btImportCust').click(function(){
            CustAction.load();
        });
        $C101M01AForm.find('#btImportWebBankApply').click(function(){
        	//引進線上申貸資料時不檢查歡喜信貸欄位
    		CLS1131S01.data['isNeedCheckCLSJobInput'] = 'N';
        	CustAction.importWebBankApply();
        });
        $C101M01AForm.find('#btImportWebProd69').click(function(){
        	//引進線上勞工紓困資料時不檢查歡喜信貸欄位
    		CLS1131S01.data['isNeedCheckCLSJobInput'] = 'N';
        	CustAction.importWebProd69();
        });
        $C101M01AForm.find('#btImportWebPLOAN').click(function(){
         	//引進線上貸款資料時不檢查歡喜信貸欄位
     		CLS1131S01.data['isNeedCheckCLSJobInput'] = 'N';
        	CustAction.importWebPLOAN();           
        });
        $C101M01AForm.find("#href_HouseLoanNoticeItem").click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                    markModel: "1",
                    varVer: $("#varVer_markModel_1").val(),
                    fileDownloadName: "HouseLoanNoticeItem.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
        $C101M01AForm.find("#href_NotHouseLoanNoticeItem").click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                    markModel: "2",
                    varVer: $("#varVer_markModel_2").val(),
                    fileDownloadName: "NotHouseLoanNoticeItem.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
        $C101M01AForm.find("#href_CardLoanNoticeItem").click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                    markModel: "3",
                    varVer: $("#varVer_markModel_3").val(),
                    fileDownloadName: "CardLoanNoticeItem.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
        $C101M01AForm.find("#href_TW_J10_REASON").click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                    markModel: "TW_J10_REASON",
                    varVer: $("#varVer_markModel_3").val(),
                    fileDownloadName: "TW_J10_REASON.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
        $C101M01AForm.find("#href_DifficultCharSoln").click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                    markModel: "DifficultCharSoln",
                    fileDownloadName: "DifficultCharSoln.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
        $C101M01AForm.find("[name=markModel][value=0]").click(function(){
            if (this.checked) {
                $C101M01AForm.find("[name=markModel][value!=0]").prop("checked", false).prop("disabled", true);
                $C101M01AForm.find("#fieldset_markModel_0").show();
                $C101M01AForm.find("#fieldset_markModel_1").hide();
                $C101M01AForm.find("#fieldset_markModel_2").hide();
                $C101M01AForm.find("#fieldset_markModel_3").hide();
            }
            else {
                $C101M01AForm.find("[name=markModel]").prop("disabled", false);
                $C101M01AForm.find("#fieldset_markModel_0").hide();
            }
        });
        
        $C101M01AForm.find("[name=markModel][value=1]").click(function(){
            var fieldset = $C101M01AForm.find("#fieldset_markModel_1");
            if (this.checked) {
                fieldset.show();
            }
            else {
                fieldset.hide();
            }
            
        });
        $C101M01AForm.find("[name=markModel][value=2]").click(function(){
            var fieldset = $C101M01AForm.find("#fieldset_markModel_2");
            if (this.checked) {
                fieldset.show();
            }
            else {
                fieldset.hide();
            }
        });
        $C101M01AForm
		.find("[name=markModel][value=3]")
		.click(function() {
					var fieldset = $C101M01AForm
							.find("#fieldset_markModel_3");
					var clsJobTypeField = document
							.getElementById("cls_job_type_fieldset");
					
					if (this.checked) {
						/*
						 * 會進到此 function 有2種情況 (1)經辦人工勾選
						 * (2)之前已勾選專案信貸(非團體)過，之後開啟頁面
						 */
						// if($C101M01AForm.find('#grade3_markModel_3').val()==""){
						if ((CLS1131S01.data.grade3_markModel_3 || '') == "") {
							API
									.showMessage("請注意:"
											+ "<br/>"
											+ "1.如敘做「專案信貸(非團體)」，請務必於e-JCIC查詢系統中，查詢「組合查詢產品七（個人信用狀況）」時，\"同一天\"再加查「標準查詢-J10個人信用評分」。"
											+ "<br/>"
											+ "2.如未敘做「專案信貸(非團體)」，請不要勾選「非房貸申請信用評等:專案信貸(非團體)」。"
											+ "<br/>");
						} else {
							// 若已有「專案信貸(非團體)」的最終評等
						}

						fieldset.show();
						// 勾選後的動作
						markModelCheckAction();
						//增加檢查判斷
						CLS1131S01.data['isNeedCheckCLSJobInput'] = 'Y';

					} else {
						fieldset.hide();
						markModelUncheckAction(true);
						//增加檢查判斷
						CLS1131S01.data['isNeedCheckCLSJobInput'] = 'N';

					}
					//J-113-0208未勾選「非房貸申請信用評等:專案信貸(非團體)」、不顯示歡喜信貸客群欄位
					TermGroupAction.markModelCheckChange();
				});
        
        
        // 開啟 房貸評分表
        $C101M01AForm.find('#btScoreInfo_markModel_1').click(function(){
        	//因為有不同的版本，改成每次
        	$('#scoreSheet').empty();
        	
        	var param = {'mainId':CLS1131S01.data.mainId, 'custId':CLS1131S01.data.custId, 'dupNo':CLS1131S01.data.dupNo
					, 'use_handler':CLS1131S01.handler, 'noOpenDoc':true};
			if(CLS1131S01.isC120M01A){
            	param['isC120M01A'] = 'Y';
        	}
			//在CLS1131,CLS1141時，path有差別
    		$("#scoreSheet").load(webroot + '/app/cls/cls1131s02', param, function(){          
    			open_G_page(param);
            });	
    		
        });
        // 調整
        $C101M01AForm.find('#btAdjust_markModel_1').click(function(){
        	//在簽報書開啟，會是 readonly 狀態
        	$('#adjustSheet').empty();
        	$('#adjustSheet').load(webroot + '/app/cls/cls1131s03', function(){
                AdjustAction.handler = CLS1131S01.handler;
                AdjustAction.open(CLS1131S01.data);
            });
        	/*if ($('#adjustThickBox').size() == 0) {
                $('#adjustSheet').load(webroot + '/app/cls/cls1131s03', function(){
                    AdjustAction.handler = CLS1131S01.handler;
                    AdjustAction.open(CLS1131S01.data);
                });
            }
            else {
                AdjustAction.open(CLS1131S01.data);
            }*/
        });
        // 開啟 非房貸評分表
        $C101M01AForm.find('#btScoreInfo_markModel_2').click(function(){
        	//因為有不同的版本，改成每次
        	$('#scoreNotHouseLoanSheet').empty();
			
			var param = {'mainId':CLS1131S01.data.mainId, 'custId':CLS1131S01.data.custId, 'dupNo':CLS1131S01.data.dupNo
					, 'use_handler':CLS1131S01.handler, 'noOpenDoc':true};
			if(CLS1131S01.isC120M01A){
            	param['isC120M01A'] = 'Y';
        	}
			//在CLS1131,CLS1141時，path有差別
    		$("#scoreNotHouseLoanSheet").load(webroot + '/app/cls/cls1131s05', param, function(){          
    			open_Q_page(param);
            });			            
        });
        //調整  非房貸評分表
        $C101M01AForm.find('#btAdjust_markModel_2').click(function(){
        	//在簽報書開啟，會是 readonly 狀態
        	$('#adjustNotHouseLoanSheet').empty();
        	$('#adjustNotHouseLoanSheet').load(webroot + '/app/cls/cls1131s06', function(){
                AdjustNotHouseLoanAction.handler = CLS1131S01.handler;
                AdjustNotHouseLoanAction.open(CLS1131S01.data);
            });
        	/*if ($('#adjustNotHouseLoanThickBox').size() == 0) {
                $('#adjustNotHouseLoanSheet').load(webroot + '/app/cls/cls1131s06', function(){
                    AdjustNotHouseLoanAction.handler = CLS1131S01.handler;
                    AdjustNotHouseLoanAction.open(CLS1131S01.data);
                });
            }
            else {
                AdjustNotHouseLoanAction.open(CLS1131S01.data);
            }*/
        });
        
     // 開啟 專案信貸(非團體)評分表
        $C101M01AForm.find('#btScoreInfo_markModel_3').click(function(){
        	if($C101M01AForm.find('#grade3_markModel_3').val()==""){
        		//沒有查 J10, 不會產出「專案信貸(非團體)」的評等
        		API.showMessage("尚未產生「非房屋貸款申請信用評等模型:專案信貸(非團體)」評等");
        	}else{
            	//因為有不同的版本，改成每次
            	$('#scoreCardLoanSheet').empty();
    			
    			var param = {'mainId':CLS1131S01.data.mainId, 'custId':CLS1131S01.data.custId, 'dupNo':CLS1131S01.data.dupNo
    					, 'use_handler':CLS1131S01.handler, 'noOpenDoc':true};
    			if(CLS1131S01.isC120M01A){
                	param['isC120M01A'] = 'Y';
            	}
    			//在CLS1131,CLS1141時，path有差別
        		$("#scoreCardLoanSheet").load(webroot + '/app/cls/cls1131s07', param, function(){          
        			open_R_page(param);
                });		
        		
        	}	            
        });
        //調整  專案信貸(非團體)評分表
        $C101M01AForm.find('#btAdjust_markModel_3').click(function(){
        	if($C101M01AForm.find('#grade3_markModel_3').val()==""){
        		//沒有查 J10, 不會產出「專案信貸(非團體)」的評等
        		//ilog.debug("no markModel_3");
        	}else{
            	//在簽報書開啟，會是 readonly 狀態
            	$('#adjustCardLoanSheet').empty();
            	$('#adjustCardLoanSheet').load(webroot + '/app/cls/cls1131s08', function(){
                    AdjustCardLoanAction.handler = CLS1131S01.handler;
                    AdjustCardLoanAction.open(CLS1131S01.data);
                });
        	}	        	
        });
        
        //2013-06-20,Rex,add,婉卻按鈕功能
        // 修改婉卻狀態
        $C101M01AForm.find('#btEditReject').click(function(){
            var lms_rejCase = CommonAPI.loadCombos(['lms_rejCase']).lms_rejCase;
            var $BoxDiv = $('#openEditRejectSBoxDiv');
            $BoxDiv.find("[name=editRejCase]").prop("disabled", false);
            if ($BoxDiv.find("[name=editRejCase]").length != 3) {
                $BoxDiv.find("#editRejCase").setItems({
                    item: lms_rejCase,
                    format: '{key}',
                    size: 1
                });
            }
            var rejectCase = $C101M01AForm.find("[name=rejectCase]:checked").val();
            $BoxDiv.find("[name=editRejCase][value=" + rejectCase + "]").prop("checked", true);
            $('#openEditRejectSBox').thickbox({
                title: '', // i18n.cms1400v01["title"],
                width: 400,
                height: 200,
                align: 'center',
                valign: 'bottom',
                buttons: {
                    'sure': function(){
                        var result = $BoxDiv.find("[name=editRejCase]:checked").val();
                        $.ajax({
                            handler: CLS1131S01.handler,
                            action: 'editReject',
                            data: $.extend(CLS1131S01.data, {
                                editReject: result
                            }),
                            success: function(response){
                                $C101M01AForm.find("[name=rejectCase][value=" + result + "]").attr("checked", "checked");
                                $C101M01AForm.find("#rejectMemo").val(response.rejectMemo);
                                $.thickbox.close();
                            }
                        });
                    },
                    'cancel': function(){
                        $.thickbox.close();
                    }
                }
            });
        });
        
        $C101M01AForm.find('#btnImpCustName').click(function(){
        	$.ajax({
                handler: CLS1131S01.handler,
                action: 'impCustName',
                data: { 'mainId':CLS1131S01.data.mainId
                	, 'custId':CLS1131S01.data.custId
                	, 'dupNo':CLS1131S01.data.dupNo 
                },
                success: function(json){
                    $C101M01AForm.injectData(json);
                }
            });
        });
        
        $('#btnImpCm1DataBlock').click(function(){
        	$.ajax({
                handler: CLS1131S01.handler,
                action: 'impCm1DataBlock',
                data: { 'mainId':CLS1131S01.data.mainId
                	, 'custId':CLS1131S01.data.custId
                	, 'dupNo':CLS1131S01.data.dupNo 
                },
                success: function(json){
                    $("#C101S01BForm").injectData(json);
                }
            });
        });
        
        $('#btnImpPtaFlag').click(function(){
        	$.ajax({
                handler: CLS1131S01.handler,
                action: 'impPtaFlag',
                data: { 'mainId':CLS1131S01.data.mainId
                	, 'custId':CLS1131S01.data.custId
                	, 'dupNo':CLS1131S01.data.dupNo 
                },
                success: function(json){
                    $("#C101S01BForm").injectData(json);
                }
            });
        });
    
        if(CLS1131S01.isC120M01A){
        	$('#href_DifficultCharSoln').hide();
        }else{
        	
        };

		$C101M01AForm.find("#noteItem").click(function(){
			$('#noteItemThickBox').thickbox({
            title: '', // i18n.cms1400v01["title"],
            width: 500,
            height: 200,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'close': function(){
                    $.thickbox.close();
                }
            }
        	});
        });
	
		$C101M01AForm.find("[name=isBailout4][value='Y']").click(function(){
			LabourBailout4_0.isShowTag(this.checked);
        });
		
		$C101M01AForm.find("#batchSelectNoDoubtItem").change(function() {
			BatchSelectHeadAccountCheckItem.isSelectAllItem($(this).is(':checked'));
		});
		
		$C101M01AForm.find("[name=concentrateCredit][value='Y']").click(function(){
			concentrateCredit.isShowTag(this.checked);
        });

        //J-111-0269 Ltv查詢視窗相關
        $C101M01AForm.find('#btLtvInfo').click(function(){
        	// 開啟LTV查詢視窗
        	LtvAction.open();
        });
    },
    /**
     * 基本資料 ======================================================================
     */
    A_build: function(){
        var $C101S01AForm = $('#C101S01AForm');
        // 同戶籍地址
        $C101S01AForm.find('#btSameAddr').click(function(){
            var $form = $('#C101S01AForm');
            $form.find('#coCity').val($form.find('#fCity').val());
            $form.find('#coZip').val($form.find('#fZip').val());
            $form.find('#coAddr').val($form.find('#fAddr').val());
            $form.find('#coTarget').html($form.find('#fTarget').html());
        });
        // 登錄
        $C101S01AForm.find('#dpBankNameLink').click(function(){
            QueryBranch.open({
                //removeKey : [ '03', '06', '99' ],// 刪除不需要的選項
                fn: function(data){
                    var $form = $('#C101S01AForm');
                    $form.find('#dpBank').val(data.bankCode || '');
                    $form.find('#dpBrno').val(data.branchCode || '');
                    $form.find('#dpBankName').html(data.branchName || i18n.def['comboSpace'] || '--請選擇--');
                    // 查詢存款帳戶
                    //CLS1131S01.queryAccount('' + data.bankCode);
                    CLS1131S01.queryAccount('' + data.bankCode, '' + data.branchCode);
                }
            });
        });
        // 戶籍地址
        $C101S01AForm.find('#fTargetLink').click(function(){
            AddrAction.open({
                formId: 'C101S01AForm',
                signify: 'f'
            });
        });
        // 通訊地址
        $C101S01AForm.find('#coTargetLink').click(function(){
            AddrAction.open({
                formId: 'C101S01AForm',
                signify: 'co'
            });
        });
        // 存款帳戶
        $C101S01AForm.find('#dpAcct').change(function(){
            CLS1131S01.checkAccount();
        });
        //預設本分行
        $C101S01AForm.find('#btDpBankAsUserInfoUnitNo').click(function(){
        	var $form = $('#C101S01AForm');
            $form.find('#dpBank').val('017');
            $form.find('#dpBrno').val(userInfo.unitNo);
            $form.find('#dpBankName').html(userInfo.unitCName);
            // 查詢存款帳戶
            CLS1131S01.queryAccount('' +  $form.find('#dpBank').val(), '' + $form.find('#dpBrno').val());
        });
        // remove required
        $C101S01AForm.find('input,select').removeClass('required');
        
        $C101S01AForm.find("#checkListFlag").change(function() {
        	if($('#C101S01AForm').find("#checkListFlag[value='Y']").prop('checked')){
    			$("#liCLS1131S01V").attr("style","display:block;");
    		}else{
    			$("#liCLS1131S01V").attr("style","display:none;");
    		}
    	});
    },
    /**
     * 服務單位 ======================================================================
     */
    B_build: function(){
    	var clsJobType1 = '';
    	var clsJobType2 = '';
    	var clsJobTitle = '0101'; //預設值
    	var capital = '';
    	var isNPO = false;
    	
        var $C101S01BForm = $('#C101S01BForm');
        // 職業別
        $C101S01BForm.find('#jobType1').change(function(){
            var $form = $('#C101S01BForm');
            var code = $form.find('#jobType1').val();
            
            if (code) {
                var item = CommonAPI.loadCombos('jobType' + code);

				if(!CLS1131S01.isC120M01A && item.jobType02 != undefined){
					delete item.jobType02.A;
				}
				
                $form.find('#jobType2').setItems({
                    item: item['jobType' + code],
                    format: '{key}'
                });
            }
        });
        //配合消金新增的行業
        $C101S01BForm.find('#clsJobType1').change(function(){
            var $form = $('#C101S01BForm');
            var code = $form.find('#clsJobType1').val();
            if (code) {
                var item = CommonAPI.loadCombos('clsJobType' + code);
                $form.find('#clsJobType2').setItems({
                    item: item['clsJobType' + code],
                    format: '{key}'
                });
            }
            //職業mapping
            clsJobType1 = code;
            mappingClsJob(clsJobType1,clsJobType2,clsJobTitle,capital,isNPO);
            
        });
   
        $C101S01BForm.find('#juPaidUpCapital').change(function(){
        	capital = $C101S01BForm.find('#juPaidUpCapital').val();
        	mappingClsJob(clsJobType1,clsJobType2,clsJobTitle,capital,isNPO);
        	
        });
        $C101S01BForm.find('#clsJobType2').change(function(){
        	clsJobType2 = $C101S01BForm.find('#clsJobType2').val();
        	mappingClsJob(clsJobType1,clsJobType2,clsJobTitle,capital,isNPO);
        	
        });
        $C101S01BForm.find('#clsJobTitle').change(function(){
        	clsJobTitle = $C101S01BForm.find('#clsJobTitle').val();
        	mappingClsJob(clsJobType1,clsJobType2,clsJobTitle,capital,isNPO);
        	
        });
        
        $C101S01BForm.find('#isNPO').change(function(){
        	//檢查有沒有勾選"專案信貸"
    		if(!$('#C101M01AForm').find("[name=markModel][value=3]").is(":checked")){
    			$("#juPaidUpCapital").val("");
        		capital = '';
        		enableWhite("juPaidUpCapital");
    		}else{
    			//有勾選的狀況。
            	isNPO = $C101S01BForm.find("#isNPO").is(":checked");
            	if(isNPO){
            		//非營利單位打勾，實收資本額給0反灰、統編變成非必填。
            		$("#juPaidUpCapital").prop("required", false);
            		//檢查是否有錯誤提示
            		removeDataErrorStyle("juId");
            		removeDataErrorStyle("juPaidUpCapital");
            		$("#juPaidUpCapital").val("0");
            		capital = "0";
            		$("#juPaidUpCapital").trigger("change");
            		
            		disableGray("juPaidUpCapital");
            		
            		//統一編號變成非必填
            		$("#juId").prop("required", false);
            		$("#taxIDStart").html("");
            	}
            	else{   		
            		$("#taxIDStart").html("＊");
            		$("#juId").prop("required", true);           		
            		capital = '';
            		//服務單位實收資本額(新台幣元)   勾"無"
        			if($("input[name='hasJuTotalCapital']:checked").length == 0){//J-113-0208 服務單位實收資本額(新台幣元)為無 沒勾才開
        				//非營利單位不勾。實收單位資本額必填、
                		$("#juPaidUpCapital").prop("required", true);
                		$("#juPaidUpCapital").val("");
        				enableWhite("juPaidUpCapital");
        			}
            		
            	}
            	mappingClsJob(clsJobType1,clsJobType2,clsJobTitle,capital,isNPO);
    		}
        });
        
        // 服務單位地址
        $C101S01BForm.find('#comTargetLink').click(function(){
            AddrAction.open({
                formId: 'C101S01BForm',
                signify: 'com'
            });
        });
        // remove required
        $C101S01BForm.find('input,select').removeClass('required');
        
//        $C101S01BForm.find('#experience').blur(function(){
//            var $this = $(this);
//            var len = parseInt($this.attr('maxlength') || $this.attr('maxLength') || '0');
//            var value = $this.val() || '';
//			var len = 60;
//            if (value.length > len) 
//                $this.val(value.substring(0, len));
//        });
        $C101S01BForm.find('input[name=ynJuId]').click(function(){
        	$C101S01BForm.injectData({'juId':''});
        });
        $C101S01BForm.find('input[name=ynJuTotalCapital]').click(function(){
        	$C101S01BForm.injectData({'juTotalCapital':''});
        });
        $C101S01BForm.find('input[name=ynJuPaidUpCapital]').click(function(){
        	$C101S01BForm.injectData({'juPaidUpCapital':''});
        });
        $C101S01BForm.find("#openIncomeView").click(function(){

            $.ajax({
                handler: CLS1131S01.handler,
                action: 'getPersonalIncomeVersion',
                data:  {
                    custId:CLS1131S01.data.custId,
                    dupNo:CLS1131S01.data.dupNo,
                    mainId:CLS1131S01.data.mainId,
                    isC120M01A: CLS1131S01.isC120M01A
                },
                success: function(response){
                    var incomeVersion = response.incomeVersion;
                    needReset = response.needReset;
                    if(incomeVersion!=0){
                        //目前是v1版，之後如果有v2，v3版，則再新增cls1131s01bincomev?.html,CLS1131S01BIncomeV?Page.js相關檔案
                        //會這樣做是消金處希望可以保留每一版本要呈相的畫面和資料
                        //如果版本不合，則needReset代表清空畫面，讓user重填
                        var url = CLS1131S01.isC120M01A ? "../../cls/cls1131s01bincomev" + incomeVersion+"?needReset=" + needReset:"../cls/cls1131s01bincomev" + incomeVersion+"?needReset=" + needReset
                        if(CLS1131S01.isC122M01A){ //從進件管理開檔
                        	//進件管理無特殊步驟，基本上都走個金徵信的模組，但這邊URL路徑上由進件開檔需要多一組../，因此這邊另外拆分
                        	url = "../../cls/cls1131s01bincomev" + incomeVersion+"?needReset=" + needReset
                        }
                        $("#personalIncomeDetailViewVer").load(url,function(){

                        });
                    } else {
                        // 這是舊版的收入明細表
                        $.ajax({
                            handler: CLS1131S01.handler,
                            action: 'getPersonalIncomeDetail',
                            data:  {
                                custId:CLS1131S01.data.custId,
                                dupNo:CLS1131S01.data.dupNo,
                                mainId:CLS1131S01.data.mainId
                            },
                            success: function(response){
                                var pidForm = $("#personalIncomeDetailForm");

                                resetPersonalIncomeDetailForm();
                                pidForm.injectData(response);

                                if(CLS1131S01.readOnly || CLS1131S01.isC120M01A){
                                    pidForm.lockDoc()
                                }
                                var buttons = {
                                    "saveData":function(){
                                        saveData();
                                    },
                                    "cancelSelect": function(){
                                        resetPersonalIncomeDetailForm();
                                    },
                                    "print": function(){
                                        saveData().done(function(){
                                            $.form.submit({
                                                url: webroot + '/app/simple/FileProcessingService',
                                                target: "_blank",
                                                data: $.extend(CLS1131S01.data, {
                                                    fileDownloadName: 'cls1131r08.pdf',
                                                    serviceName: 'cls1131r08rptservice',
                                                    isC120M01A: CLS1131S01.isC120M01A
                                                })
                                            });
                                        })
                                    },
                                    "close": function(){
                                        $.thickbox.close();
                                    }
                                }
                                i18n.def['cancelSelect'] = "清除重填"
                                if(CLS1131S01.readOnly || CLS1131S01.isC120M01A){
                                    delete buttons["saveData"];
                                    delete buttons["cancelSelect"];
                                }
                                $("#personalIncomeDetailView").thickbox({
                                    width:950,
                                    height:600,
                                    modal: true,
                                    i18n: i18n.def,
                                    buttons: buttons
                                })
                            }
                        });
                    }
                }
            });

        });
        function mappingClsJob(clsJobType1,clsJobType2,clsJobTitle,capital,isNPO){
        	if(!(checkStringIsNotEmpty(clsJobType1) && checkStringIsNotEmpty(clsJobType2) && checkStringIsNotEmpty(clsJobTitle) && checkStringIsNotEmpty(capital))){
        		return false;
        	}
        	//不為空時，把資料丟到後端。
         	$.ajax({
                handler: CLS1131S01.handler,
                action: 'mappingClsJob',
                data: { 
                	'clsJobType1':clsJobType1,
                	'clsJobType2':clsJobType2,
                	'clsJobTitle':clsJobTitle,
                	'capital':capital,
                	'isNPO':isNPO 
                },
                success: function(json){
                	$("#jobType1").val(json.jobType1);
            		$("#jobType1").trigger("change");
            		
                	$("#jobType2").val(json.jobType2);
                	$("#jobType2").trigger("change");
                	$("#jobTitle").val(json.jobTitle);
					$("#jobTitle").trigger("change");					
                }
            });
        	
        	
        }
        function checkStringIsNotEmpty(para){
        	if(typeof (para) != "string"){
        		return false;
        	}
        	if(para === null || para === ''){
        	    return false;
        	}else{
        		return true;
        	}
        }

        function resetPersonalIncomeDetailForm(){
            var pidForm = $("#personalIncomeDetailForm");
            pidForm.reset();
            pidForm.find(".personalIncomeDetail").hide();
            pidForm.find("input").filter(":not(.readonly,.editable)").prop("disabled", true);
            pidForm.find("input").filter(":not(.readonly,.editable)").prop("readOnly", true);
            pidForm.find("input").filter(":not(.readonly,.editable)").css("background-color", "#E0E0E0");
            pidForm.find("input[name='otherC1']").prop("disabled", false);
        }

        var saveData = function(){
            var deferred = $.Deferred();

            if(CLS1131S01.readOnly || CLS1131S01.isC120M01A){
                deferred.resolve();
            } else {
                var pidForm = $("#personalIncomeDetailForm");
                var checkSize = $("input[name='mainIncomeType']:checked").length + $("input[name='otherC1']:checked,input[name='otherC2']:checked,input[name='otherC3']:checked").length;

                if(pidForm.valid() && checkSize > 0){
                     $.ajax({
                        handler: CLS1131S01.handler,
                        action: 'savePersonalIncomeDetail',
                        data: $.extend({
                            custId:CLS1131S01.data.custId,
                            dupNo:CLS1131S01.data.dupNo,
                            mainId:CLS1131S01.data.mainId

                        },pidForm.serializeData()),
                        success: function(json){
                            pidForm.injectData(json);
                            API.showPopMessage(i18n.def.saveSuccess);
                            deferred.resolve();
                        }
                    });
                } else {
                    if(checkSize == 0){
                        API.showErrorMessage("A~B4(單選)，C1~C3(複選)選項至少需填列一項");
                    }
                    $("input.data-error").eq(0).focus();
                }
            }

            return deferred.promise();
        }

        function resetPersonalIncomeDetailView(){
            var pidForm = $("#personalIncomeDetailForm");
            resetPersonalIncomeDetailForm();
            pidForm.find("input[name='positionType']").click(function(){

                pidForm.find(".personalIncomeDetail").show();
                var value = $(this).val();

                // 3.業務職(底薪+獎金者)，不能勾選A及B2
                if(value == "3"){
                     pidForm.find("input[name='mainIncomeType'][value='A'],input[name='mainIncomeType'][value='B2']").prop("checked", false);
                     pidForm.find("input[name='mainIncomeType'][value='A'],input[name='mainIncomeType'][value='B2']").prop("disabled", true);
                     pidForm.find("input[name='otherC1']").prop("disabled", false);
                     pidForm.find(".incomeTypeA input,.incomeTypeB2 input").filter(":not(.readonly,.editable)").prop("disabled", true);
                     pidForm.find(".incomeTypeA input,.incomeTypeB2 input").filter(":not(.readonly,.editable)").prop("readOnly", true);

                     //當為業務職時，薪轉存摺/薪資單為六個月資料
                     pidForm.find(".forPositionType3").show();

                } else {
                    pidForm.find("input[name='mainIncomeType'][value='A'],input[name='mainIncomeType'][value='B2']").prop("disabled", false);
                    pidForm.find(".forPositionType3").hide();
                }


            })



           pidForm.find("input[name='mainIncomeType']").click(function(){
                var mainValue = $(this).val();
//                alert(mainValue)
                pidForm.find("input[name='mainIncomeType']").each(function(i, $value){
                    var value = $($value).val();

                    pidForm.find(".incomeType" + value + " input").filter(":not(.readonly,.editable)").prop("disabled", true);
                    pidForm.find(".incomeType" + value + " input").filter(":not(.readonly,.editable)").prop("readOnly", true);
                    pidForm.find(".incomeType" + value + " input").filter(":not(.readonly,.editable)").css("background-color", "#E0E0E0")

                    if(mainValue == value){

                        pidForm.find(".incomeType" + value + " input").filter(":not(.readonly,.editable)").prop("disabled", false);
                        pidForm.find(".incomeType" + value + " input").filter(":not(.readonly,.editable)").prop("readOnly", false);
                        pidForm.find(".incomeType" + value + " input").filter(":not(.readonly,.editable)").css("background-color", "white")
                    } else {


                    }
                });
                // 當勾選所得清單/扣繳憑單時，則不能選取定存利息
                if(mainValue == "A"){

                    pidForm.find("input[name='otherC1']").prop("checked", false);
                    pidForm.find("input[name='otherC1']").prop("disabled", true);
                } else {
                    pidForm.find("input[name='otherC1']").prop("disabled", false);
                }
                pidForm.find("input[name='otherC1']").triggerHandler("click");
            })



            $(["C1","C2","C3","D4","D5","D6","D7","D8","D9"]).each(function(i, val){
                var input = val;
                pidForm.find("input[name='other" + input + "']").click(function(){

                    pidForm.find(".otherType" + input + " input").filter(":not(.readonly,.editable)").prop("disabled", true);
                    pidForm.find(".otherType" + input + " input").filter(":not(.readonly,.editable)").prop("readOnly", true);
                    pidForm.find(".otherType" + input + " input").filter(":not(.readonly,.editable)").css("background-color", "#E0E0E0")

                    if($(this).is(":checked")){
                        pidForm.find(".otherType" + input + " input").filter(":not(.readonly,.editable)").prop("disabled", false);
                        pidForm.find(".otherType" + input + " input").filter(":not(.readonly,.editable)").prop("readOnly", false);
                        pidForm.find(".otherType" + input + " input").filter(":not(.readonly,.editable)").css("background-color", "white")
                    } else {}
                });
                pidForm.find("input[name='other" + input + "']").triggerHandler("click")
            })

//            //401報表輸3期，403/405輸6期
//            pidForm.find("input[name='itemB3ReportType']").click(function(){
//                if($(this).val() == "A") {
//                    pidForm.find(".forItemB3ReportTypeB").hide();
//                } else {
//                    pidForm.find(".forItemB3ReportTypeB").show();
//                }
//            })
        }
        resetPersonalIncomeDetailView();
		
		//ESG分數
		$C101S01BForm.find('input[name="isHasEsgScore"]').change(function(){
			var isHasEsgScore = $(this).val();
			controlEsgScoreDisplay(isHasEsgScore);
        });
    },
    /**
     * 債償能力 =====================================================================
     */
    C_build: function(){
        var $C101S01CForm = $('#C101S01CForm');
        // remove required
        $C101S01CForm.find('input,select').removeClass('required');
        
        $C101S01CForm.find('#yFamAmt').blur(function(){
            var rawValue = $(this).val();
            var value = util.delComma(rawValue);
            
            if (!isNaN(value)) {
                if (parseInt(value, 10) > 1000) {
                    MegaApi.confirmMessage(i18n.cls1131s01['C101S01C.yFamAmt'] + " 輸入之金額為" + rawValue + "萬元，是否正確?", function(r){
                        if (r) {
                            //ok
							//判斷是否有夫妻收入有異動，若是，c101s01c.reCalFlg = N
							$.ajax({
			                    handler: CLS1131S01.handler,
			                    action: 'checkYFamAmtisChange',
			                    data: {
									custId:CLS1131S01.data.custId,
			                        dupNo:CLS1131S01.data.dupNo,
			                        mainId:CLS1131S01.data.mainId,
									yFamAmt:value
								},
			                    success: function(json){
									
			                    }
			                });
                        }
                        else {
                            $C101S01CForm.find('#yFamAmt').val("0");
                        }
                    });
                } else {
					//判斷是否有夫妻收入有異動，若是，c101s01c.reCalFlg = N
					$.ajax({
	                    handler: CLS1131S01.handler,
	                    action: 'checkYFamAmtisChange',
	                    data: {
							custId:CLS1131S01.data.custId,
	                        dupNo:CLS1131S01.data.dupNo,
	                        mainId:CLS1131S01.data.mainId,
							yFamAmt:value
						},
	                    success: function(json){
							
	                    }
	                });
				}
            }
        });
        
		var dRateClickTime = 0;
	    // 點10下讓負債比可以編輯
	    $("#dRate").click(function() {
	        dRateClickTime = dRateClickTime + 1;
	        if (dRateClickTime > 10) {
	            $(this).prop("readonly", false);
	            dRateClickTime = 0;
	        }
	    })
		
		var yRateClickTime = 0;
	    // 點10下讓編號可以編輯
	    $("#yRate").click(function() {
	        yRateClickTime = yRateClickTime + 1;
	        if (yRateClickTime > 10) {
	            $(this).prop("readonly", false);
	            yRateClickTime = 0;
	        }
	    })
		
		//計算負債比  J-110-0073 Web eloan授信系統因應負債比計算系統化 By 2021/3/9  johnny
		$C101S01CForm.find('#btRate').click(function(){
//			//檢核夫妻年收入 
//			if ($('#yFamAmt').val() == "" || $('#yFamAmt').val() == "0") {
//				CommonAPI.showErrorMessage("夫妻年收入 不得為0或空值！");
//				return false;
//			}
			
        	$("#rateBox").thickbox({
				width: 980,
				height: 600,
				//align: "center",
				//valign: "bottom",
				i18n: i18n.def,
				open: function(){
					 $.ajax({
							type : "POST",
							handler : "cls1131formhandler",
							action: "queryC120S01C",
	        				data: $.extend(CLS1131S01.data, {'isC120M01A': CLS1131S01.isC120M01A}),
							success:function(responseData){
								$('#rateForm').reset();
								
								if (responseData.c101s01c) {
									$('#rateForm').find("#tb3").empty();
									if (responseData.c101s01c.rateData == undefined
										|| responseData.c101s01c.rateData.jcicCount == undefined  
										|| responseData.c101s01c.rateData.jcicCount == ""
										|| responseData.c101s01c.rateData.jcicCount == 0) {
										
										$('#rateForm').find("#tb3").append('<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>');
									} else {
										var jcicCount = parseInt(responseData.c101s01c.rateData.jcicCount);
										
										//動態append html
										for(i = 1; i <= jcicCount; i++ ) {
											//debugger;
											 $('#rateForm').find("#tb3").append('<tr><td><input type="text" id="jcic_' + i + '" name="jcic_' + i + '" class="numeric" style="text-align: center;" maxlength="13" size="1" disabled="true"/> ' +
												'</td><td><input type="text" id="jcic_bankname_' + i + '" name="jcic_bankname_' + i + '" class="" maxlength="13" size="24" disabled="true"/> <input type="hidden" id="jcic_bankcode_' + i + '" name="jcic_bankcode_' + i + '" class="jcic_bankcode" maxlength="13" size="24"/>' +
												'</td><td align="center"><input type="text" id="jcic_item_' + i + '" name="jcic_item_' + i + '" class="" maxlength="13" size="1" disabled="true" style="text-align: center;"/> ' +
												'</td><td><input type="jcic_itemname_' + i + '" id="jcic_itemname_' + i + '" name="jcic_itemname_' + i + '" class="" maxlength="13" size="16" disabled="true"/> ' +
												'</td><td><input type="text" id="jcic_loan_' + i + '" name="jcic_loan_' + i + '" class="numeric" maxlength="13" size="6" disabled="true"/>' +
                                                '</td><td><input type="text" id="jcic_loanamt_' + i + '" name="jcic_loanamt_' + i + '" class="numeric" maxlength="13" size="6" disabled="true"/>' +
												'</td><td><input type="text" id="jcic_period_' + i + '" name="jcic_period_' + i + '" class="numeric jcic_period" maxlength="13" size="8" fraction="2" disabled="true"/> ' +
												'</td><td><input type="text" id="jcic_period_ch_' + i + '" name="jcic_period_ch_' + i + '" class="numeric jcic_period_ch" maxlength="13" size="8" fraction="2"/> ' +
												'</td><td align="center"><input type="checkbox" id="jcic_period_needmegapay_' + i + '" name="jcic_period_needmegapay_' + i + '" class="checkbox jcic_period_needmegapay_" maxlength="1" size="1" fraction="2" value="Y"/> ' +
												'</td></tr> ');
										}
									}
									$('#rateForm').find("#tb4").empty();
                                    if (responseData.c101s01c.rateData == undefined
                                        || responseData.c101s01c.rateData.jcicCreditCount == undefined
                                        || responseData.c101s01c.rateData.jcicCreditCount == ""
                                        || responseData.c101s01c.rateData.jcicCreditCount == 0) {

                                        $('#rateForm').find("#tb4").append('<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>');
                                    } else {
                                        var jcicCount = parseInt(responseData.c101s01c.rateData.jcicCount);
                                        var jcicCreditCount = parseInt(responseData.c101s01c.rateData.jcicCreditCount);

                                        //動態append html
                                        for(i = 1; i <= jcicCreditCount; i++ ) {
                                            //debugger;
                                             $('#rateForm').find("#tb4").append('<tr><td><input type="text" id="jcic_credit_' + i + '" name="jcic_credit_' + i + '" class="numeric" style="text-align: center;" maxlength="13" size="1" disabled="true"/> ' +
                                                '</td><td><input type="text" id="jcic_credit_bankname_' + i + '" name="jcic_credit_bankname_' + i + '" class="" maxlength="13" size="24" disabled="true"/> <input type="hidden" id="jcic_credit_bankcode_' + i + '" name="jcic_credit_bankcode_' + i + '" class="jcic_credit_bankcode" maxlength="13" size="24"/>' +
                                                '</td><td><input type="text" id="jcic_credit_revol_' + i + '" name="jcic_credit_revol_' + i + '" class="numeric jcic_credit_revol" maxlength="13" size="8" disabled="true"/> ' +
                                                '</td><td><input type="text" id="jcic_credit_revol_ch_' + i + '" name="jcic_credit_revol_ch_' + i + '" class="numeric jcic_credit_revol_ch" maxlength="13" size="8" fraction="2"/> ' +
                                                '</td><td align="center"><input type="checkbox" id="jcic_credit_revol_needmegapay_' + i + '" name="jcic_credit_revol_needmegapay_' + i + '" class="checkbox jcic_credit_revol_needmegapay" maxlength="1" size="1" fraction="2" value="Y"/> ' +
                                                '</td><td><input type="text" id="jcic_credit_pre_' + i + '" name="jcic_credit_pre_' + i + '" class="numeric jcic_credit_pre" maxlength="13" size="8" disabled="true"/> ' +
                                                '</td><td><input type="text" id="jcic_credit_pre_ch_' + i + '" name="jcic_credit_pre_ch_' + i + '" class="numeric jcic_credit_pre_ch" maxlength="13" size="8" fraction="2"/> ' +
                                                '</td><td align="center"><input type="checkbox" id="jcic_credit_pre_needmegapay_' + i + '" name="jcic_credit_pre_needmegapay_' + i + '" class="checkbox jcic_credit_pre_needmegapay" maxlength="1" size="1" fraction="2" value="Y"/> ' +
                                                '</td></tr> ')
                                        }
                                    }
									
									if (responseData.c101s01c.rateData != undefined) {
										$('#rateForm').injectData(responseData.c101s01c.rateData);
										
										$('#rateForm').find("#tb3").find(".numeric").each(function() {
											$(this).val(util.addComma($(this).val()));
									    }).change(function() {
											$(this).val(util.addComma($(this).val()));
									    });

										$('#rateForm').find("#tb4").find(".numeric").each(function() {
                                            $(this).val(util.addComma($(this).val()));
                                        }).change(function() {
                                            $(this).val(util.addComma($(this).val()));
                                        });

                                        $('#rateForm').find("#tb3").find(".checkbox").each(function() {
                                            if($(this).is(":checked")){
                                                $(this).parent().prev().children().val("0");
                                                $(this).parent().prev().children().readOnly(true);
                                            }
                                            else{
                                                $(this).parent().prev().children().readOnly(false);
                                            }
                                        }).change(function() {
                                            if($(this).is(":checked")){
                                                $(this).parent().prev().children().val("0");
                                                $(this).parent().prev().children().readOnly(true);
                                            }
                                            else{
                                                $(this).parent().prev().children().val("");
                                                $(this).parent().prev().children().readOnly(false);
                                            }
                                        });

                                        $('#rateForm').find("#tb4").find(".checkbox").each(function() {
                                            if($(this).is(":checked")){
                                                $(this).parent().prev().children().val("0");
                                                $(this).parent().prev().children().readOnly(true);
                                            }
                                            else{
                                                $(this).parent().prev().children().readOnly(false);
                                            }
                                        }).change(function() {
                                            if($(this).is(":checked")){
                                                $(this).parent().prev().children().val("0");
                                                $(this).parent().prev().children().readOnly(true);
                                            }
                                            else{
                                                $(this).parent().prev().children().val("");
                                                $(this).parent().prev().children().readOnly(false);
                                            }
                                        });

                                         $('#rateForm').find("#tb4").find(".checkbox").each(function() {
                                             if( $(this).parent().prev().prev().prev().children().val()=='017'
                                             || $(this).parent().prev().prev().prev().prev().prev().prev().children().val()=='017'){
                                                 $(this).readOnly(true);
                                             }
                                             else{
                                                 $(this).readOnly(false);
                                             }
                                         });
									}
								}
								
								//帶入外層 夫妻年收入 //消金處說不用了，改分行自行Key到元
								//$('#rateForm').find('#yFamAmtR').val($C101S01CForm.find('#yFamAmt').val());
								
								//個人收入
								$('#rateForm').find('#pAllAmt').val(responseData.pAllAmt);
								
								//基本資料中未婚/離婚/歿，則夫妻年收入自動帶入個人年收入金額
								if ($('#rateForm').find('#yFamAmtR').val() == "" 
									&& ($('#C101S01AForm').find("input[name=marry]:checked").val() == "1"
										|| $('#C101S01AForm').find("input[name=marry]:checked").val() == "4"
										|| $('#C101S01AForm').find("input[name=marry]:checked").val() == "5")) {
									$('#rateForm').find('#yFamAmtR').val(responseData.pAllAmt);
								}
							}
						});
				},
				buttons: {
					"calculate": function () {
						if (!$('#rateForm').valid()) {
                            return false;
                        }
						
						saveRateFormData();
					},
					"print": function(){
                        saveRateFormData().done(function(){
                            $.form.submit({
                                url: webroot + '/app/simple/FileProcessingService',
                                target: "_blank",
                                data: $.extend(CLS1131S01.data, {
                                    fileDownloadName: 'cls1131r09.pdf',
                                    serviceName: 'cls1131r09rptservice',
                                    isC120M01A: CLS1131S01.isC120M01A
                                })
                            });
                        })
                    },
					"close": function () {
						$.thickbox.close();
					}
				}
			});
        });
		
		//簽報書檢視
		$C101S01CForm.find('#btRateView').click(function(){
        	$("#rateBox").thickbox({
				width: 980,
				height: 600,
				//align: "center",
				//valign: "bottom",
				i18n: i18n.def,
				open: function(){
					 $.ajax({
							type : "POST",
							handler : "cls1131formhandler",
							action: "queryC120S01C",
	        				data: $.extend(CLS1131S01.data, {'isC120M01A': CLS1131S01.isC120M01A}),
							success:function(responseData){
								$('#rateForm').reset();
								
								if (responseData.c101s01c) {
									$('#rateForm').find("#tb3").empty();
									if (responseData.c101s01c.rateData == undefined
										|| responseData.c101s01c.rateData.jcicCount == undefined  
										|| responseData.c101s01c.rateData.jcicCount == ""
										|| responseData.c101s01c.rateData.jcicCount == 0) {
										
										$('#rateForm').find("#tb3").append('<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>');
									} else {
										var jcicCount = parseInt(responseData.c101s01c.rateData.jcicCount);
										
										//動態append html
										for(i = 1; i <= jcicCount; i++ ) {
											//debugger;
											 $('#rateForm').find("#tb3").append('<tr><td><input type="text" id="jcic_' + i + '" name="jcic_' + i + '" class="numeric" style="text-align: center;" maxlength="13" size="1" disabled="true"/> ' +
												'</td><td><input type="text" id="jcic_bankname_' + i + '" name="jcic_bankname_' + i + '" class="" maxlength="13" size="24" disabled="true"/> <input type="hidden" id="jcic_bankcode_' + i + '" name="jcic_bankcode_' + i + '" class="jcic_bankcode" maxlength="13" size="24"/>' +
												'</td><td><input type="text" id="jcic_item_' + i + '" name="jcic_item_' + i + '" class="" maxlength="13" size="1" disabled="true" style="text-align: center;"/> ' +
												'</td><td><input type="jcic_itemname_' + i + '" id="jcic_itemname_' + i + '" name="jcic_itemname_' + i + '" class="" maxlength="13" size="16" disabled="true"/> ' +
												'</td><td><input type="text" id="jcic_loan_' + i + '" name="jcic_loan_' + i + '" class="numeric" maxlength="13" size="6" disabled="true"/>' +
                                                '</td><td><input type="text" id="jcic_loanamt_' + i + '" name="jcic_loanamt_' + i + '" class="numeric" maxlength="13" size="6" disabled="true"/>' +
												'</td><td><input type="text" id="jcic_period_' + i + '" name="jcic_period_' + i + '" class="numeric jcic_period" maxlength="13" size="8" fraction="2" disabled="true"/> ' +
												'</td><td><input type="text" id="jcic_period_ch_' + i + '" name="jcic_period_ch_' + i + '" class="numeric jcic_period_ch" maxlength="13" size="8" fraction="2" disabled="true"/> ' +
												'</td><td align="center"><input type="checkbox" id="jcic_period_needmegapay_' + i + '" name="jcic_period_needmegapay_' + i + '" class="checkbox jcic_period_needmegapay_" maxlength="1" size="1" fraction="2" value="Y"  disabled="true"/> ' +
												'</td></tr> ')
										}
									}
									$('#rateForm').find("#tb4").empty();
                                    if (responseData.c101s01c.rateData == undefined
                                        || responseData.c101s01c.rateData.jcicCreditCount == undefined
                                        || responseData.c101s01c.rateData.jcicCreditCount == ""
                                        || responseData.c101s01c.rateData.jcicCreditCount == 0) {

                                        $('#rateForm').find("#tb4").append('<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>');
                                    } else {
                                        var jcicCount = parseInt(responseData.c101s01c.rateData.jcicCount);
                                        var jcicCreditCount = parseInt(responseData.c101s01c.rateData.jcicCreditCount);

                                        //動態append html
                                        for(i = 1; i <= jcicCreditCount; i++ ) {
                                            //debugger;
                                             $('#rateForm').find("#tb4").append('<tr><td><input type="text" id="jcic_credit_' + i + '" name="jcic_credit_' + i + '" class="numeric" style="text-align: center;" maxlength="13" size="1" disabled="true"/> ' +
                                                '</td><td><input type="text" id="jcic_credit_bankname_' + i + '" name="jcic_credit_bankname_' + i + '" class="" maxlength="13" size="24" disabled="true"/> <input type="hidden" id="jcic_credit_bankcode_' + i + '" name="jcic_credit_bankcode_' + i + '" class="jcic_credit_bankcode" maxlength="13" size="24"/>' +
                                                '</td><td><input type="text" id="jcic_credit_revol_' + i + '" name="jcic_credit_revol_' + i + '" class="numeric jcic_credit_revol" maxlength="13" size="8" disabled="true"/> ' +
                                                '</td><td><input type="text" id="jcic_credit_revol_ch_' + i + '" name="jcic_credit_revol_ch_' + i + '" class="numeric jcic_credit_revol_ch" maxlength="13" size="8" fraction="2"  disabled="true"/> ' +
                                                '</td><td align="center"><input type="checkbox" id="jcic_credit_revol_needmegapay_' + i + '" name="jcic_credit_revol_needmegapay_' + i + '" class="checkbox jcic_credit_revol_needmegapay" maxlength="1" size="1" fraction="2" value="Y"  disabled="true"/> ' +
                                                '</td><td><input type="text" id="jcic_credit_pre_' + i + '" name="jcic_credit_pre_' + i + '" class="numeric jcic_credit_pre" maxlength="13" size="8" disabled="true"/> ' +
                                                '</td><td><input type="text" id="jcic_credit_pre_ch_' + i + '" name="jcic_credit_pre_ch_' + i + '" class="numeric jcic_credit_pre_ch" maxlength="13" size="8" fraction="2"  disabled="true"/> ' +
                                                '</td><td align="center"><input type="checkbox" id="jcic_credit_pre_needmegapay_' + i + '" name="jcic_credit_pre_needmegapay_' + i + '" class="checkbox jcic_credit_pre_needmegapay" maxlength="1" size="1" fraction="2" value="Y"  disabled="true"/> ' +
                                                '</td></tr> ')
                                        }
                                     }
									
									if (responseData.c101s01c.rateData != undefined) {
                                        $('#rateForm').injectData(responseData.c101s01c.rateData);
										
										$('#rateForm').find("#tb3").find(".numeric").each(function() {
											$(this).val(util.addComma($(this).val()));
									    }).change(function() {
											$(this).val(util.addComma($(this).val()));
									    });
										$('#rateForm').find("#tb4").find(".numeric").each(function() {
                                            $(this).val(util.addComma($(this).val()));
                                        }).change(function() {
                                            $(this).val(util.addComma($(this).val()));
                                        });

                                        $('#rateForm').find("#tb3").find(".checkbox").each(function() {
                                            if($(this).is(":checked")){
                                                $(this).parent().prev().children().val("0");
                                                $(this).parent().prev().children().readOnly(true);
                                            }
                                            else{
                                                $(this).parent().prev().children().readOnly(false);
                                            }
                                        }).change(function() {
                                            if($(this).is(":checked")){
                                                $(this).parent().prev().children().val("0");
                                                $(this).parent().prev().children().readOnly(true);
                                            }
                                            else{
                                                $(this).parent().prev().children().val("");
                                                $(this).parent().prev().children().readOnly(false);
                                            }
                                        });

                                        $('#rateForm').find("#tb4").find(".checkbox").each(function() {
                                            if($(this).is(":checked")){
                                                $(this).parent().prev().children().val("0");
                                                $(this).parent().prev().children().readOnly(true);
                                            }
                                            else{
                                                $(this).parent().prev().children().readOnly(false);
                                            }
                                        }).change(function() {
                                            if($(this).is(":checked")){
                                                $(this).parent().prev().children().val("0");
                                                $(this).parent().prev().children().readOnly(true);
                                            }
                                            else{
                                                $(this).parent().prev().children().val("");
                                                $(this).parent().prev().children().readOnly(false);
                                            }
                                        });
									}
								}
								
								//帶入外層 夫妻年收入 //消金處說不用了，改分行自行Key到元
								//$('#rateForm').find('#yFamAmtR').val($C101S01CForm.find('#yFamAmt').val());
								//個人收入
								$('#rateForm').find('#pAllAmt').val(responseData.pAllAmt);
							}
						});
				},
				buttons: {
					"print": function(){
                            saveRateFormData().done(function(){
                                $.form.submit({
                                    url: webroot + '/app/simple/FileProcessingService',
                                    target: "_blank",
                                    data: $.extend(CLS1131S01.data, {
                                        fileDownloadName: 'cls1131r09.pdf',
                                        serviceName: 'cls1131r09rptservice',
                                        isC120M01A: CLS1131S01.isC120M01A
                                    })
                                });
                            })
                        },
					"close": function () {
						$.thickbox.close();
					}
				}
			});
        });
		
		//清除重填
		$('#rateForm').find('#btRateReset').click(function(){
			//$('#rateForm').reset();
			$("#rateForm #tb1 input[type=text]").val("");
        	$("#rateForm #tb1 input[type=checkbox]").prop('checked', false);
        	$("#rateForm #tb1 input[type=radio]").prop('checked', false);
		});
		
		$('#rateForm').find('#btImportJCIC').click(function(){
			//引進聯徵
			$.ajax({
				type : "POST",
				handler : "cls1131formhandler",
				action: "queryC120S01C_JCIC",
				data: CLS1131S01.data,
				success:function(responseData){
					$('#rateForm').find("#tb3").empty();
					if (responseData.jcicCount == 0) {
						$('#rateForm').find("#tb3").append('<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>');
					} else {
						//動態append html
						for(i = 1; i <= responseData.jcicCount; i++ ) {
							//debugger;
							 $('#rateForm').find("#tb3").append('<tr><td><input type="text" id="jcic_' + i + '" name="jcic_' + i + '" class="numeric" style="text-align: center;" maxlength="13" size="1" disabled="true"/> ' +
								'</td><td><input type="text" id="jcic_bankname_' + i + '" name="jcic_bankname_' + i + '" class="" maxlength="13" size="24" disabled="true"/> <input type="hidden" id="jcic_bankcode_' + i + '" name="jcic_bankcode_' + i + '" class="jcic_bankcode" maxlength="13" size="24"/>' +
								'</td><td><input type="text" id="jcic_item_' + i + '" name="jcic_item_' + i + '" class="" maxlength="13" size="1" disabled="true" style="text-align: center;"/> ' +
								'</td><td><input type="jcic_itemname_' + i + '" id="jcic_itemname_' + i + '" name="jcic_itemname_' + i + '" class="" maxlength="13" size="16" disabled="true"/> ' +
								'</td><td><input type="text" id="jcic_loan_' + i + '" name="jcic_loan_' + i + '" class="numeric" maxlength="13" size="6" disabled="true"/>' +
								'</td><td><input type="text" id="jcic_loanamt_' + i + '" name="jcic_loanamt_' + i + '" class="numeric" maxlength="13" size="6" disabled="true"/>' +
								'</td><td><input type="text" id="jcic_period_' + i + '" name="jcic_period_' + i + '" class="numeric jcic_period" maxlength="13" size="8" fraction="2" disabled="true"/> ' +
								'</td><td><input type="text" id="jcic_period_ch_' + i + '" name="jcic_period_ch_' + i + '" class="numeric jcic_period_ch" maxlength="13" size="8" fraction="2"/> ' +
								'</td><td align="center"><input type="checkbox" id="jcic_period_needmegapay_' + i + '" name="jcic_period_needmegapay_' + i + '" class="checkbox jcic_period_needmegapay_" maxlength="1" size="1" fraction="2" value="Y"/> ' +
								'</td></tr> ')
						}

						$('#rateForm').find("#tb3").find(".numeric").each(function() {
							$(this).val(util.addComma($(this).val()));
					    }).change(function() {
							$(this).val(util.addComma($(this).val()));
					    });
                        $('#rateForm').find("#tb3").find(".checkbox").each(function() {
                            if($(this).is(":checked")){
                                $(this).parent().prev().children().val("0");
                                $(this).parent().prev().children().readOnly(true);
                            }
                            else{
                                $(this).parent().prev().children().val("");
                                $(this).parent().prev().children().readOnly(false);
                            }
                        }).change(function() {
                            if($(this).is(":checked")){
                                $(this).parent().prev().children().val("0");
                                $(this).parent().prev().children().readOnly(true);
                            }
                            else{
                                $(this).parent().prev().children().val("");
                                $(this).parent().prev().children().readOnly(false);
                            }
                        });
					}

                    $('#rateForm').find("#tb4").empty();
					if (responseData.jcicCreditCount == 0) {
                        $('#rateForm').find("#tb4").append('<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>');
                        $('#rateForm').injectData(responseData);
                    } else {
                        //動態append html
                        for(i = 1; i <= responseData.jcicCreditCount; i++ ) {
                            //debugger;
                             $('#rateForm').find("#tb4").append('<tr><td><input type="text" id="jcic_credit_' + i + '" name="jcic_credit_' + i + '" class="numeric" style="text-align: center;" maxlength="13" size="1" disabled="true"/> ' +
                                '</td><td><input type="text" id="jcic_credit_bankname_' + i + '" name="jcic_credit_bankname_' + i + '" class="" maxlength="13" size="24" disabled="true"/> <input type="hidden" id="jcic_credit_bankcode_' + i + '" name="jcic_credit_bankcode_' + i + '" class="jcic_credit_bankcode" maxlength="13" size="24"/>' +
                                '</td><td><input type="text" id="jcic_credit_revol_' + i + '" name="jcic_credit_revol_' + i + '" class="numeric jcic_credit_revol" maxlength="13" size="8" disabled="true"/> ' +
                                '</td><td><input type="text" id="jcic_credit_revol_ch_' + i + '" name="jcic_credit_revol_ch_' + i + '" class="numeric jcic_credit_revol_ch" maxlength="13" size="8" fraction="2"/> ' +
                                '</td><td align="center"><input type="checkbox" id="jcic_credit_revol_needmegapay_' + i + '" name="jcic_credit_revol_needmegapay_' + i + '" class="checkbox jcic_credit_revol_needmegapay" maxlength="1" size="1" fraction="2" value="Y"/> ' +
                                '</td><td><input type="text" id="jcic_credit_pre_' + i + '" name="jcic_credit_pre_' + i + '" class="numeric jcic_credit_pre" maxlength="13" size="8" disabled="true"/> ' +
                                '</td><td><input type="text" id="jcic_credit_pre_ch_' + i + '" name="jcic_credit_pre_ch_' + i + '" class="numeric jcic_credit_pre_ch" maxlength="13" size="8" fraction="2"/> ' +
                                '</td><td align="center"><input type="checkbox" id="jcic_credit_pre_needmegapay_' + i + '" name="jcic_credit_pre_needmegapay_' + i + '" class="checkbox jcic_credit_pre_needmegapay" maxlength="1" size="1" fraction="2" value="Y"/> ' +
                                '</td></tr> ')
                        }
                        $('#rateForm').injectData(responseData);

                        $('#rateForm').find("#tb3").find(".numeric").each(function() {
                            $(this).val(util.addComma($(this).val()));
                        }).change(function() {
                            $(this).val(util.addComma($(this).val()));
                        });
                        $('#rateForm').find("#tb3").find(".checkbox").each(function() {
                            if($(this).is(":checked")){
                                $(this).parent().prev().children().val("0");
                                $(this).parent().prev().children().readOnly(true);
                            }
                            else{
                                $(this).parent().prev().children().val("");
                                $(this).parent().prev().children().readOnly(false);
                            }
                        }).change(function() {
                            if($(this).is(":checked")){
                                $(this).parent().prev().children().val("0");
                                $(this).parent().prev().children().readOnly(true);
                            }
                            else{
                                $(this).parent().prev().children().val("");
                                $(this).parent().prev().children().readOnly(false);
                            }
                        });

                        $('#rateForm').find("#tb4").find(".numeric").each(function() {
                            $(this).val(util.addComma($(this).val()));
                        }).change(function() {
                            $(this).val(util.addComma($(this).val()));
                        });

                        $('#rateForm').find("#tb4").find(".checkbox").each(function() {
                            if($(this).is(":checked")){
                                $(this).parent().prev().children().val("0");
                                $(this).parent().prev().children().readOnly(true);
                            }
                            else{
                                $(this).parent().prev().children().val("");
                                $(this).parent().prev().children().readOnly(false);
                            }
                        }).change(function() {
                            if($(this).is(":checked")){
                                $(this).parent().prev().children().val("0");
                                $(this).parent().prev().children().readOnly(true);
                            }
                            else{
                                $(this).parent().prev().children().val("");
                                $(this).parent().prev().children().readOnly(false);
                            }
                        });

                        $('#rateForm').find("#tb4").find(".checkbox").each(function() {
                             if( $(this).parent().prev().prev().prev().children().val()=='017'
                             || $(this).parent().prev().prev().prev().prev().prev().prev().children().val()=='017'){
                                 $(this).readOnly(true);
                             }
                             else{
                                 $(this).readOnly(false);
                             }
                        });
                    }
				}
			});
		});
		
		//計算 onchange
        $('#rateForm').find('.mode,.loan,.period,.extPeriod,.rate').change(function(){
			var selectTr = $(this).closest('tr');
			if (CLS1131S01.readOnly || CLS1131S01.isC120M01A) {
				//簽報唯獨，不觸發change 事件
			} else {
				if (selectTr.find("input[class=mode]:checked").val() !== undefined) {
					if (selectTr.find("input[class=mode]:checked").val() == "2") {
						//期數清空，不需輸入
						selectTr.find(".period,.extPeriod").val("");
						selectTr.find(".period,.extPeriod").readOnly(true);
					} else {
						selectTr.find(".period,.extPeriod").readOnly(false);
					}
				}
			}
			
			if (selectTr.find("input[class=mode]:checked").val() !== undefined && selectTr.find(".loan").val() != "" 
					 && selectTr.find(".rate").val() != "") {
				var mPay = 0;
				if (selectTr.find("input[class=mode]:checked").val() == "1" 
						&& selectTr.find(".period").val() != "" 
						&& selectTr.find(".extPeriod").val() != "") {
							
					if (selectTr.find(".rate").val() == "0") {
						//利率為0 => 期付金=本金/(期數-寬限期)
						mPay = ((parseFloat(selectTr.find(".loan").val()) * 1000) 
									/ (parseInt(selectTr.find(".period").val()) - parseInt(selectTr.find(".extPeriod").val())));
					} else {
						//期付金= ROUND((額度仟元*1000)*(利率%/12)/(1-(1/(1+利率%/12))^(月數-寬限期)),0)
						mPay = (parseFloat(selectTr.find(".loan").val()) * 1000) * (parseFloat(selectTr.find(".rate").val()) / 100 / 12)
									/ (1-Math.pow(1/(1+(parseFloat(selectTr.find(".rate").val()) / 100 ) / 12),
									 (parseInt(selectTr.find(".period").val()) - parseInt(selectTr.find(".extPeriod").val()))
									 ));
					}
					selectTr.find(".mPay").val(util.addComma(Math.round(mPay))); //四捨五入
				} else if (selectTr.find("input[class=mode]:checked").val() == "2") {
					//按月計息=ROUND((額度仟元*1000)*利率%/12,0)
					mPay = ((parseFloat(selectTr.find(".loan").val()) * 1000) * (parseFloat(selectTr.find(".rate").val()) / 100 ) / 12) ;
					selectTr.find(".mPay").val(util.addComma(Math.floor(mPay))); //無條件捨去
				} else {
					selectTr.find(".mPay").val("");
				}
			} else {
				selectTr.find(".mPay").val("");
			}
		});
		
					
        $C101S01CForm.find('#btDataQueryC1_C2_C3_C4').click(function(){
        	cls1131_import_c101s01c("C2").done(function(){
    			cls1131_import_c101s01c("C3").done(function(){
    				cls1131_import_c101s01c("C4").done(function(){
    					cls1131_import_c101s01c("C1").done(function(){
    		        		//C2, C3, C4 是引入「行內資料庫」
    						//C1需先查詢EJCIC，所以把 C1 放最後
    		        	});
    	        	});
            	});
        	});
        	
        });
        
        $C101S01CForm.find('#btDataQueryC1').click(function(){
        	cls1131_import_c101s01c("C1");
        });
        $C101S01CForm.find('#btDataQueryC2').click(function(){
        	cls1131_import_c101s01c("C2");
        });
        $C101S01CForm.find('#btDataQueryC3').click(function(){
        	cls1131_import_c101s01c("C3");
        });
        $C101S01CForm.find('#btDataQueryC4').click(function(){
        	cls1131_import_c101s01c("C4");
        });
        $C101S01CForm.find('#btLoanBalSByid').click(function(){
        	cls1131_import_c101s01c("LoanBalS");
        });
        $C101S01CForm.find('#btLoanBalNByid').click(function(){
        	cls1131_import_c101s01c("LoanBalN");
        });
        
        if(CLS1131S01.isC120M01A){
        	$C101S01CForm.find('#DataQueryC_memo').hide();
        }else{
        	$C101S01CForm.find('#DataQueryC_memo').show();
			$C101S01CForm.find('#btRateView').hide();
        }
        
        $C101S01CForm.find('#href_DRateExplain').click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                	markModel: "D",
                    fileDownloadName: "DRateRegulations.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
		
		var saveRateFormData = function(){
            var deferred = $.Deferred();
			
            if(CLS1131S01.readOnly || CLS1131S01.isC120M01A){
                deferred.resolve();
            } else {		
				//勾選本案為保證人，不用檢核
				if ($("#guarantorFlag:checked").length == 0) {
					//檢核至少一筆月付金
					if ($('#mPay_1').val() == "" && $('#mPay_2').val() == "" &&
							$('#mPay_3').val() == "" && $('#mPay_4').val() == "" &&
							$('#mPay_5').val() == "") {
						CommonAPI.showErrorMessage("【本案資訊】至少有輸入一筆貸款之資訊！<br/><br/>若本案為保證人無須輸入貸款資訊，請勾選本案為保證人");
						return false;
					}
				}
				
				
				if ($('#yFamAmtR').val() == "" ) {
					//夫妻年收入必填
					CommonAPI.showErrorMessage("夫妻年收入必填");
					return false;
				} else {
					//檢核夫妻年收入跟外層夫妻年收不可相差超過一萬
					if (Math.abs(parseInt($('#yFamAmtR').val()) - (parseInt($C101S01CForm.find('#yFamAmt').val()) * 10000)) >= 10000) {
						CommonAPI.showErrorMessage("夫妻年收入不一致，請確認");
						return false;
					}
				}
				
				if ($('#yPeriod').val() == "" ) {
					//配偶貸款期付金 必填
					CommonAPI.showErrorMessage("配偶貸款期付金必填");
					return false;
				}
				
				if ($('#yCycle').val() == "" ) {
					//配偶信用卡循環 必填
					CommonAPI.showErrorMessage("配偶信用卡循環必填");
					return false;
				}
				
				if ($('#yPeriodUnpay').val() == "" ) {
					//配偶分期未償還金額 必填
					CommonAPI.showErrorMessage("配偶分期未償還金額必填");
					return false;
				}
				
				
				//家庭資訊-必填欄位檢核
				if ($('#fFamAmtR').val() == "" ) {
					//夫妻年收入必填
					CommonAPI.showErrorMessage("借款人及家庭年收入必填");
					return false;
				} else {
					//檢核家庭年收入跟外層家庭年收入不可相差超過一萬
					if (Math.abs(parseInt($('#fFamAmtR').val()) - (parseInt($C101S01CForm.find('#fincome').val()) * 10000)) >= 10000) {
						CommonAPI.showErrorMessage("家庭年收入不一致，請確認");
						return false;
					}
				}
				if ($('#fPeriod').val() == "" ) {
					//家庭貸款期付金 必填
					CommonAPI.showErrorMessage("家庭貸款期付金必填");
					return false;
				}
				if ($('#fCycle').val() == "" ) {
					//配偶信用卡循環 必填
					CommonAPI.showErrorMessage("配偶信用卡循環必填");
					return false;
				}
				if ($('#fPeriodUnpay').val() == "" ) {
					//家庭分期未償還金額 必填
					CommonAPI.showErrorMessage("家庭分期未償還金額必填");
					return false;
				}
				
                //計算個人負債總額 
				var pDebtAmt = 0;
				
				//本案資訊A
				var mPay = 0;
				$('#rateForm').find(".mPay").each(function(i) {
					if ($(this).val() != "") {
						var selectTr = $(this).closest('tr');
						
						if (selectTr.find(".mPayCh").val() != "") {
							//若有填入調整後期付金，已填入為主
							mPay = mPay + (parseInt(selectTr.find(".mPayCh").val()) * 12 );
						} else {
							mPay = mPay + (parseInt($(this).val()) * 12 );
						}
					}
				});
				
				// 聯徵資訊 (D或E)*12 *注意因為動態長要replace(',','')
				var jcic_period = 0;
				$('#rateForm').find(".jcic_period").each(function(i) {
					//原則上以jcic_period計算，惟分行有在jcic_period_ch填列金額時，則以該欄位計算
					var selectTr = $(this).closest('tr');
					selectTr.find(".jcic_period_ch")
					if (selectTr.find(".jcic_period_ch").val() != "") {
						jcic_period = jcic_period + (parseInt(util.delComma(selectTr.find(".jcic_period_ch").val())) * 12);
					} else if ($(this).val() != ""){
						jcic_period = jcic_period + (parseInt(util.delComma($(this).val())) * 12);
					}
				});

				var jcic_credit_revol_017 = 0;
				var jcic_credit_revol = 0;
				$('#rateForm').find(".jcic_credit_revol").each(function(i) {
                    //原則上以jcic_period計算，惟分行有在jcic_period_ch填列金額時，則以該欄位計算
                    var selectTr = $(this).closest('tr');
                    selectTr.find(".jcic_credit_revol_ch")
                    if (selectTr.find(".jcic_credit_revol_ch").val() != "") {
                        if (selectTr.find(".jcic_credit_bankcode").val() == "017"){
                            jcic_credit_revol_017 = jcic_credit_revol_017 + (parseInt(util.delComma(selectTr.find(".jcic_credit_revol_ch").val())));
                        }
                        else{
                            jcic_credit_revol = jcic_credit_revol + (parseInt(util.delComma(selectTr.find(".jcic_credit_revol_ch").val())));
                        }
                    } else if ($(this).val() != ""){
                        if (selectTr.find(".jcic_credit_bankcode").val() == "017"){
                            jcic_credit_revol_017 = jcic_credit_revol_017 + (parseInt(util.delComma($(this).val())));
                        }
                        else{
                            jcic_credit_revol = jcic_credit_revol + (parseInt(util.delComma($(this).val())));
                        }
                    }
                });
                $("#jcic_credit_ch_017").val(jcic_credit_revol_017);
                $("#jcic_credit_not_ch_017").val(jcic_credit_revol);

                var jcic_credit_pre = 0;
                $('#rateForm').find(".jcic_credit_pre").each(function(i) {
                    //原則上以jcic_period計算，惟分行有在jcic_period_ch填列金額時，則以該欄位計算
                    var selectTr = $(this).closest('tr');
                    selectTr.find(".jcic_credit_pre_ch")
                    if (selectTr.find(".jcic_credit_pre_ch").val() != "") {
                        jcic_credit_pre = jcic_credit_pre + (parseInt(util.delComma(selectTr.find(".jcic_credit_pre_ch").val())));
                    } else if ($(this).val() != ""){
                        jcic_credit_pre = jcic_credit_pre + (parseInt(util.delComma($(this).val())));
                    }
                });
                $("#jcic_credit_ch_unpay").val(jcic_credit_pre);
				
				//信用卡F
				var credit = 0;
				if ($("#jcic_credit_017").val() != "" || $("#jcic_credit_ch_017").val() != "") {
					if ($("#jcic_credit_ch_017").val() != "") {
						credit = credit + parseInt($("#jcic_credit_ch_017").val());
					} else {
						credit = credit + parseInt($("#jcic_credit_017").val());
					}
				}
				if ($("#jcic_credit_not_017").val() != "" || $("#jcic_credit_not_ch_017").val() != "") {
					if ($("#jcic_credit_not_ch_017").val() != "") {
						credit = credit + parseInt($("#jcic_credit_not_ch_017").val());
					} else {
						credit = credit + parseInt($("#jcic_credit_not_017").val());
					}
				}
				if ($("#jcic_credit_unpay").val() != "" || $("#jcic_credit_ch_unpay").val() != "") {
					if ($("#jcic_credit_ch_unpay").val() != "") {
						credit = credit + parseInt($("#jcic_credit_ch_unpay").val());
					} else {
						credit = credit + parseInt($("#jcic_credit_unpay").val());
					}
				}
				
				//個人負債總額  = (本案資訊月付金+聯徵資訊月付金)*12 + 信用卡
				pDebtAmt = mPay + jcic_period + credit;
				$("#pDebtAmt").val(Math.round(pDebtAmt)); //四捨五入到整數
				
				//計算個人負債比率=個人負債總額 / 個人收入
				var dRateR = 0;
				if ($("#pAllAmt").val() != "") {
					//如果收入為0的=>1.有負債的一律為999%  2.沒負債的一律為0%
					if ($("#pAllAmt").val() == "0") {
						if (pDebtAmt == 0) {
							dRateR = "0";
						} else {
							dRateR = "999";
						}
					} else {
						dRateR = parseFloat(pDebtAmt) / (parseFloat($("#pAllAmt").val())) * 100;
					}
				}
//				$("#dRateR").val(Math.round(dRateR)); //四捨五入到整數
				dRateR = Math.floor(dRateR); //四捨五入到整數
				if (dRateR < 1000) {
					$("#dRateR").val(dRateR);
				} else {
					$("#dRateR").val("999");
				}
				
				//計算夫妻負債比率=(個人負債總額+配偶期付金*12+配偶信用卡+配偶未償還) / 夫妻年收入(萬元)
				var yRateR = 0;
				if ($("#yFamAmtR").val() != "") {
					if ($("#yFamAmtR").val() == "0") {
						if (pDebtAmt == 0) {
							yRateR = "0";
						} else {
							yRateR = "999";
						}
					} else {
						var totalDebt = parseFloat(pDebtAmt) + (parseFloat($("#yPeriod").val()) * 12) +
									parseFloat($("#yCycle").val()) + parseFloat($("#yPeriodUnpay").val());
						yRateR = totalDebt / (parseFloat($("#yFamAmtR").val())) * 100;
					}
				}
//				$("#yRateR").val(Math.round(yRateR)); //四捨五入到整數
				yRateR = Math.floor(yRateR); //四捨五入到整數
				if (yRateR < 1000) {
					$("#yRateR").val(yRateR);
				} else {
					$("#yRateR").val("999");
				}
				
				//計算家庭負債比率=(家庭貸款期附金*12+家庭信用卡循環+家庭分期未償還金額 + (試算月附金 or 調整後月附金)*12+信用卡金額 or 信用卡調整後金額) / 借款人及家庭收入(萬元)
				//信用卡金額=信用卡循環(本行)+信用卡循環(他行)+分期未償還金額
				var fRateR = 0;
				if ($("#fFamAmtR").val() != "") {
					if ($("#fFamAmtR").val() == "0") {
						if (pDebtAmt == 0) {
							fRateR = "0";
						} else {
							fRateR = "999";
						}
					} else {						var fFamAmtR = $('#fFamAmtR').val();  //借款人及家庭年收入
						var fPeriod = $('#fPeriod').val();  //家庭貸款期付金
						var fCycle = $('#fCycle').val();  //家庭信用卡循環
						var fPeriodUnpay = $('#fPeriodUnpay').val();  //家庭分期未償還金額
						
						var totalDebt = parseFloat(pDebtAmt) + (parseFloat(fPeriod) * 12) + 
										parseFloat(fCycle) + parseFloat(fPeriodUnpay);
						
						fRateR = totalDebt / (parseFloat(fFamAmtR)) * 100;
					}
				}
//				$("#fRateR").val(Math.round(fRateR)); //四捨五入到整數
				fRateR = Math.floor(fRateR); //四捨五入到整數
				if (fRateR < 1000) {
					$("#fRateR").val(fRateR);
				} else {
					$("#fRateR").val("999");
				}
				
				$.ajax({
					type : "POST",
					handler : "cls1131formhandler",
					action: "saveC120S01CrateData",
					data: CLS1131S01.data,
					success:function(responseData){
						//帶回到上一層 個人負債比率 , 夫妻負債比率
						if (dRateR < 1000) {
							$("#dRate").val($("#dRateR").val());
						} else {
							$("#dRate").val("999");
						}
						
						if (yRateR < 1000) {
							$("#yRate").val($("#yRateR").val());
						} else {
							$("#yRate").val("999");
						}
						if (fRateR < 1000) {
							$("#fRate").val($("#fRateR").val());
						} else {
							$("#fRate").val("999");
						}
						deferred.resolve();
					}
				});
            }
            return deferred.promise();
        }
		
		$C101S01CForm.find('#href_DRatControlRuleDesc').click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                	markModel: "DRatControlRuleDesc",
                    fileDownloadName: "DRatControlRuleDesc.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
    },
    /**
     * 配偶資料 =====================================================================
     */
    D_build: function(){
        var $C101S01DForm = $('#C101S01DForm');
        CommonAPI.loadCombos('cls_mateFlag');
        // 條件下移除C.同本案借款人
        var mateFlagItems = $.extend({}, CLS1131S01.items['cls_mateFlag']);
        delete mateFlagItems['C'];
        $C101S01DForm.find('#mateFlag').setItems({
            item: mateFlagItems,
            format: '{key}',
            value: 'A',
            fn: function(){
            	hs_mateFlag();                
            }
        });
        // 引進配偶資料
        $C101S01DForm.find('#btImportMate').click(function(){
            CustAction.importMate(CLS1131S01.data);
        });
        // 服務單位地址
        $C101S01DForm.find('#mComTargetLink').click(function(){
            AddrAction.open({
                formId: 'C101S01DForm',
                signify: 'mCom'
            });
        });
    },
    /**
     * 相關資料/信用情形查詢 ==========================================================
     */
    E_build: function(){
        var $C101S01EForm = $('#C101S01EForm');
        //無法提供票信電子資料
        $C101S01EForm.find('input[name=isFromOld]').click(function(){
            if (!CLS1131S01.readOnly) {
                var $fm = $('#C101S01EForm');
                $fm.find('input[name=eChkFlag]').readOnly(true);
                $fm.find('#eChkQDate').readOnly(true);
                $fm.find('#eChkDDate').readOnly(true);
                if ($(this).val() == 'Y' || CLS1131S01.data.naturalFlag == 'N') {
                    $fm.find('input[name=eChkFlag]').readOnly(false);
                    $fm.find('#eChkQDate').readOnly(false);
                    $fm.find('#eChkDDate').readOnly(false);
                }
            }
        });
        // 相關資料查詢
        $C101S01EForm.find('#btDataQuery').click(function(){
            MegaApi.confirmMessage(i18n.cls1131s01["message.longTimeConfirm"], function(action){
                if (action){
					
					CLS1131S01.checkIsQueryEjcicS11().done(function(rtnObj){
						ilog.debug(rtnObj.chekcMsg);
						if(rtnObj.chekcMsg == ''){
							CLS1131S01.proc_EJ_ST_query("S11", "Y");						
						}
					});
					
                    CLS1131S01.queryRelatedData();
                }
            });
        });
        // EJCIC聯徵查詢列印
        $C101S01EForm.find('#btEjcicQueryPrint').click(function(){
            CLS1131S01.print('ejcic');
        });
        // ETCH票信查詢列印
        $C101S01EForm.find('#btEtchQueryPrint').click(function(){
            CLS1131S01.print('etch');
        });
        
        $C101S01EForm.find('#btSendAmlList').click(function(){
        	CLS1131S01.sendAmlList(0);
        });
        $C101S01EForm.find('#btCheckAmlResult').click(function(){
        	CLS1131S01.checkAmlResult();
        });
		//J-108-0277 介接系統資料查詢-資料建檔記錄查詢
        $C101S01EForm.find("#href_OneBtnQuery").click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                    markModel: "OneBtnQuery",
                    fileDownloadName: "OneBtnQuery.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
        
        InterfaceSystemDataInquiry.init();
        // 開啟關聯戶貸款查詢明細
        $C101S01EForm.find('#btOpenDetial').click(function(){
            if ($('#C101S01OThickBox').length == 0) {
                $('#relationSheet').load(webroot + '/app/cls/cls1131s04', function(){
                    C101S01OAction.open(CLS1131S01.data);
                });
            }
            else {
                C101S01OAction.open(CLS1131S01.data);
            }
        });
        /*
	     	在 mega.eloan.sample.js 定義 ExternalAction 
	     	有EJ, ETCH的授權，才會得到一個【附加上KEY】之後的URL
	    */
        // EJCIC聯徵查詢網址
        $C101S01EForm.find('#ejcicLink').click(function(){
            ExternalAction.submit({
                queryType: 'ejcic', // ejcic,etch
                queryId: CLS1131S01.data.custId
            });
        });
        // ETCH票信查詢網址
        $C101S01EForm.find('#etchLink').click(function(){
            ExternalAction.submit({
                queryType: 'etch', // ejcic,etch
                queryId: CLS1131S01.data.custId
            });
        });
        $C101S01EForm.find('#btnSendOneButtonQuery_P9').click(function(){
        	CLS1131S01.runOneBtnQuery('P9', '3'); //{1企業授信_2房屋貸款_3消費性貸款_4留學生貸款}
        });
        $C101S01EForm.find('#btnSendOneButtonQuery_P7').click(function(){
        	CLS1131S01.runOneBtnQuery('P7', '2'); //{1企業授信_2房屋貸款_3消費性貸款_4留學生貸款}
        });
        $C101S01EForm.find('#btnSendOneButtonQuery_Prod69').click(function(){
        	CLS1131S01.runOneBtnQuery_Prod69('P7_Prod69', '3');
        });
        $C101S01EForm.find('#btGetOneBtnQuery').click(function(){
        	CLS1131S01.getOneBtnQuery();
			// J-110-0527個金聯徵查詢結果資料紀錄
			CLS1131S01.recordEjcicResultData();
        });
        $C101S01EForm.find('#btGetOneBtnPrintPDF').click(function(){
        	CLS1131S01.getOneBtnPrintPDF(); 
        });
        $C101S01EForm.find('#btGetOneBtnPrintHTML').click(function(){
        	CLS1131S01.getOneBtnPrintHTML(); 
        });
        $C101S01EForm.find('#btn_qry_C101S01S').click(function(){
        	MegaApi.confirmMessage("是否查詢資料建檔系統？", function(action){
                if (action){ 
                	build_C101S01S_RPS(CLS1131S01.data.mainId).done(function(){
                		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                	}); 
                }
            });
        });
        $C101S01EForm.find('#btn_qry_Ejcic_T70').click(function(){
        	MegaApi.confirmMessage("是否查詢T70證券暨期貨違約交割？", function(action){
                if (action){ 
                	build_C101S01S_EJCIC_T70(CLS1131S01.data.mainId).done(function(){
                		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");	
                	});
                }
            });
        });
		$C101S01EForm.find('#btn_qry_EAI_CURIQ01').click(function(){
        	MegaApi.confirmMessage("是否查詢金控證券暨期貨違約交割？", function(action){
                if (action){ 
                	build_C101S01S_CURIQ01(CLS1131S01.data.mainId).done(function(){
                		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");	
                	});
                }
            });
        });
        $C101S01EForm.find('#btn_qry_EJ_ST_Z21').click(function(){
        	MegaApi.confirmMessage("是否查詢 Z21？", function(action){
                if (action){ 
                	CLS1131S01.proc_EJ_ST_query("Z21", "Y").done(function(){
                		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");        		
                	});
                }
            });        	
        });
        $C101S01EForm.find('#btn_qry_EJ_ST_Z13').click(function(){
        	MegaApi.confirmMessage("是否查詢 Z13？", function(action){
                if (action){ 
                	CLS1131S01.proc_EJ_ST_query("Z13", "Y").done(function(){      
                		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                	});
                }
            });        	 
        });
        if(true){
            $C101S01EForm.find('#btn_qry_EJ_ST_Z21_Prod69').click(function(){
            	MegaApi.confirmMessage("是否查詢 Z21（查詢理由：辦理勞工紓困貸款）？", function(action){
                    if (action){ 
                    	CLS1131S01.proc_EJ_ST_query("Z21_Prod69", "Y").done(function(){
                    		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");        		
                    	});
                    }
                });        	
            });
            $C101S01EForm.find('#btn_qry_EJ_ST_Z13_Prod69').click(function(){
            	MegaApi.confirmMessage("是否查詢 Z13（查詢理由：辦理勞工紓困貸款）？", function(action){
                    if (action){ 
                    	CLS1131S01.proc_EJ_ST_query("Z13_Prod69", "Y").done(function(){      
                    		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                    	});
                    }
                });        	 
            });
            $C101S01EForm.find('#btn_qry_EJ_ST_D10').click(function(){
            	MegaApi.confirmMessage("是否查詢 D10？", function(action){
                    if (action){ 
                    	CLS1131S01.proc_EJ_ST_query("D10", "Y").done(function(){      
                    		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                    	});
                    }
                });        	 
            });
            $C101S01EForm.find('#btn_qry_EJ_ST_R20').click(function(){
            	MegaApi.confirmMessage("是否查詢 R20？", function(action){
                    if (action){ 
                    	CLS1131S01.proc_EJ_ST_query("R20", "Y").done(function(){      
                    		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                    	});
                    }
                });        	 
            });
			
			$C101S01EForm.find('#btn_qry_API_C101S01S').click(function(){
            	MegaApi.confirmMessage("是否 查詢身分證領換補？", function(action){
                    if (action){ 
                    	CLS1131S01.proc_EJ_ST_query("ID_CARD_CHECK", "Y").done(function(){      
                    		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                    	});
                    }
                });        	 
            });
			
			$C101S01EForm.find('#btn_qry_RPA_C101S04W').click(function(){
            	MegaApi.confirmMessage("是否 查詢受監護/輔助？", function(action){
                    if (action){ 
                    	CLS1131S01.proc_EJ_ST_query("FA_QUERY", "Y").done(function(){      
                    		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                    	});
                    }
                });        	 
            });

        	$C101S01EForm.find('#btn_qry_WiseNews').click(function(){
                MegaApi.confirmMessage("是否 查詢負面新聞資料庫？", function(action){
                    if (action){
                        CLS1131S01.proc_EJ_ST_query("WiseNews_QUERY", "Y").done(function(){
                            InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
                        });
                    }
                });
            });
			
			$C101S01EForm.find('#btn_qry_Ejcic_B98_B95').click(function(){
        		MegaApi.confirmMessage("是否查詢聯徵B95與B98？", function(action){
                	if (action){ 
                		CLS1131S01.proc_EJ_ST_query("B95", "Y");
						CLS1131S01.proc_EJ_ST_query("B98", "Y");
                	}
            	});
			});
			
        }
        $C101S01EForm.find('a.showDetial').click(function(){
            if (FormAction.check(CLS1131S01.forms) || CLS1131S01.readOnly) {
                CLS1131S01.showDetial();
            }
            else {
                MegaApi.confirmMessage(i18n.def["saveBeforeSend"], function(action){
                    if (action) 
                        CLS1131S01.save(CLS1131S01.showDetial);
                });
            }
        });
        $C101S01EForm.find('a.query').click(function(){
        	var _queryType = $(this).attr('queryType');
        	if(_queryType=="isQdata7"){
        		if($(this).attr('data-activeSAS')=="Y"){
        			CLS1131S01.checkAmlResult();		
        		}else{
        			CLS1131S01.queryData(_queryType);	
        		}
        	}else{
        		CLS1131S01.queryData(_queryType);	
        	}
        });
        $C101S01EForm.find('span.class_l120s01m_queryDate').click(function(){
            var val = $(this).val();
            if (val && val.length == 10) {
                $.ajax({ //查詢主要借款人資料
                    handler: 'cls1131formhandler',
                    type: "POST",
                    dataType: "json",
                    action: "queryL120s01m",
                    data: {
                        'custId': CLS1131S01.data.custId,
                        'dupNo': CLS1131S01.data.dupNo,
                        'mainId': CLS1131S01.data.mainId
                    },
                    success: function(json){
                        var $formL120s01m = $("#formL120s01m");
                        
                       //J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
            			var grpYear = json.formL120s01m.grpYear;
            			var grpGrrd = json.formL120s01m.grpGrade;
            			if(grpYear){
            				//判斷2017以後為新版，之前為舊版
            				if(parseInt(grpYear, 10) >= 2017){
            					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
            			        //評等等級
            			        $("#grpGrade").setItems({
            			            item: obj.GroupGrade2017,
            			            format: "{key}"
            			        });
            	
            				}else{
            					var obj = CommonAPI.loadCombos(["GroupGrade"]);
            			        //評等等級
            			        $("#grpGrade").setItems({
            			            item: obj.GroupGrade,
            			            format: "{key}"
            			        });
            				}
            	
            			}else{
            				var obj = CommonAPI.loadCombos(["GroupGrade"]);
            		        
            		        //評等等級
            		        $("#grpGrade").setItems({
            		            item: obj.GroupGrade,
            		            format: "{key}"
            		        });

            			}
            			
                        $formL120s01m.setData(json.formL120s01m);
                        $formL120s01m.readOnlyChilds(true);
                        $formL120s01m.find(".noHideBt").show();
                        
                        if (json.formL120s01m.dataNotShow_020 == "Y") {
                            $formL120s01m.find("#data_020").hide();
                        }
                        else {
                            $formL120s01m.find("#data_020").show();
                        }
                        
                        if (json.formL120s01m.dataNotShow_080 == "Y") {
                            $formL120s01m.find("#data_080").hide();
                        }
                        else {
                            $formL120s01m.find("#data_080").show();
                        }
                        
                        if (json.formL120s01m.dataNotShow_030 == "Y") {
                            $formL120s01m.find("#data_030").hide();
                        }
                        else {
                            $formL120s01m.find("#data_030").show();
                        }
                        
                        if (json.formL120s01m.grpNo == "") {
                            $formL120s01m.find("#grpYear").hide();
                            $formL120s01m.find("#grpGrade").hide();
                        }
                        else {
                            $formL120s01m.find("#grpYear").show();
                            $formL120s01m.find("#grpGrade").show();
                        }
                        
                        if (json.formL120s01m.mbRlt == "1") {
                            $formL120s01m.find("#dataDate_060").show();
                            $formL120s01m.find("#dataDate_070").hide();
                        }
                        else {
                            $formL120s01m.find("#dataDate_060").hide();
                            $formL120s01m.find("#dataDate_070").show();
                        }
                        
                        $("#tL120s01m").thickbox({ // 使用選取的內容進行彈窗
                            title: i18n.cls1131s01["l120s01m.item26"], // 「授信信用風險管理」遵循檢核
                            width: 965,
                            height: 480,
                            modal: true,
                            i18n: i18n.def,
                            buttons: {
                            
                                "close": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
            }
        });
        $C101S01EForm.find("input[name='mbRlt33']").click(function(){
            var $desc = $("#mbRltDscr33");
            if ($(this).val() == "2" || $(this).val() == "3") {
                $desc.hide();
                $desc.val("");
            }
            else {
                $desc.show();
            }
        });
        
        $C101S01EForm.find("input[name='caseSrcFlag']").click(function(){
            
            var $div_O = $("#div_caseSrcFlag_O");
            if ($(this).val() == "L" || $(this).val() == "A") {
                $div_O.hide();
                $("#Word").val("台內地登");
            } else if ($(this).val() == "P" || $(this).val() == "C") {
            	$div_O.hide();
            } else if ($(this).val() == "O" || $(this).val() == "B") {
                $div_O.show();
            } else {
                
            }
        });

        $C101S01EForm.find("input[name='laaNo']").change(function(){        	
        	var value = $(this).val();
        	$(this).val(util.addZeroBefore(value, 6));
        });
        
        //黑名單 英文名
        $C101S01EForm.find('#eNameLink').click(function(){
            CLS1131S01.openEName();
        });

        
        $C101S01EForm.find('#href_LaaNoticeItem').click(function(){
            $.form.submit({
                url: webroot + '/app/simple/FileProcessingService',
                target: "_blank",
                data: {
                    markModel: "L",
                    fileDownloadName: "LaaNoticeItem.pdf",
                    serviceName: "cls1131s01pdfservice"
                }
            });
        });
        $C101S01EForm.find("input[name='amlBadNews']").click(function(){
            var value = $(this).val();
            var checked = $(this).is(":checked");

            if(checked){
                if(value == "0"){
                    // 若勾選了無，則其它勾選資料取消掉
                    $C101S01EForm.find("input[name='amlBadNews'][value!='0']").prop("checked", false);
                } else if(value != "0"){
                    // 若勾選了非無的其它值，則無要取消掉
                    $C101S01EForm.find("input[name='amlBadNews'][value='0']").prop("checked", false);
                }
            }

        });
        
        // 因常有難字要改DB故直接提供USER自行異動功能。		
        $("#custName").dblclick(function(){
            $("#custName").prop("readonly",false).addClass("required");
        });
		
		$("#creditShow").dblclick(function(){
            $("#btScoreInfo_markModel_1").show();
            $("#btScoreInfo_markModel_2").show();
            $("#btScoreInfo_markModel_3").show();
        });
 
        $('#chgNameLink').click(function(){
			$("#chgCustName").prop("readonly",false).addClass("required");
             CLS1131S01.openChgName();
        });
		
		//J-109-0451_08831_B1001 web eloan授信系統個金徵信作業，新增RPA查詢地政士開業資料 By 20210225
	    $("#queryRPA").click(function(){
			$("#rpaTypeBox").thickbox({
				width: 300,
				height: 90,
				align: "center",
				valign: "bottom",
				i18n: i18n.def,
				open: function(){
					 //do something....
				},
				buttons: {
					"sure": function () {
						if ($('#queryLaaName').val() == "") {
							CommonAPI.showErrorMessage("查詢地政士姓名必填！");
							return false;
						}
						
						$.ajax({
							type : "POST",
							handler : "cls1131formhandler",
							data : {
								formAction : "queryRpaQueryLaaName",
								oid : CLS1131S01.data.mainId.oid,
								mainId: CLS1131S01.data.mainId,
								queryLaaName : $('#queryLaaName').val()
							},
							success:function(responseData){
								
							}
						});
					},
					"cancel": function () {
						$.thickbox.close();
					}
				}
			});
	    });
		
		$("#queryRPAResult").click(function(){
			$("#RpaGridDetail").jqGrid("setGridParam", {
							postData : {
								mainId: CLS1131S01.data.mainId
							}
						}).trigger("reloadGrid");
						
			var openBts = {};
			openBts[i18n.def['grid.refresh']] = function(){
				$("#RpaGridDetail").trigger("reloadGrid");
		    };	
			openBts[i18n.def['close']] = function(){
				$.thickbox.close();
		    };	
			
			$("#RpaResultDetailBox").thickbox({
				title : 'RPA查詢明細',
				width : 850,
				height : 450,
				align: 'center',
		        valign: 'bottom',
				model : true,
				buttons : openBts
			});
	    });
		
		$("#RpaGridDetail").iGrid({
    		handler : 'cls1131gridhandler',
			height : 300,
    		sortname : 'type|rpaQueryReason1',
			localFirst: true,
    		postData : {
    			mainId : CLS1131S01.data.mainId,
    			formAction : "queryRpaResultDetail"
    		},
    		colModel : [ {
				colHeader : '檢視附件一',
				name : 'docfileoid',
	            align: 'center',
	            width: 100,
	            formatter: function enterNumer (cellvalue , options, rowObject){
					if (rowObject[12] == 'A02') { //查詢成功
						return '<button type=\"button\" class=\"forview\"  onclick=\"CLS1131S01.openRpafile(\'' + cellvalue + '\')\"><span class=\"text-only\"> 檢視結果 </span></button>';
					} else {
						return '';
					}
            	}
        	}, {
				colHeader : ' ',  //引進
				name : 'oid',
	            align: 'center',
	            width: 50,
	            formatter: function enterNumer (cellvalue , options, rowObject){
					if (rowObject[12] == 'A02') {
						return '<button type=\"button\" onclick=\"CLS1131S01.importRpaDetail(\'' + cellvalue + '\')\"><span class=\"text-only\"> 引進 </span></button>';
					} else {
						return '';
					}
            	}
        	},{
    			colHeader : i18n.cls1131s01['L161S01D.rpaType'],//"發查類別"
    			name : 'type',
				width: 100,
    			align : "left",
    			sortable : false
    		}, {
    			colHeader : i18n.cls1131s01['L161S01D.agentName'],//"地政士姓名"
    			width: 100,
    			name : 'rpaQueryReason1',
    			sortable : false,
    			align : "left"
    		}, {
    			colHeader : i18n.cls1131s01['L161S01D.status'],//處理狀態
    			width: 150,
    			name : 'statusDesc',
    			sortable : false,
    			align : "center"
    		}, {
    			colHeader : i18n.cls1131s01['L161S01D.reason'],//"回傳結果"
    			width: 200,
    			name : 'reason',
    			align : "left",
    			sortable : false
    		}, {
    			colHeader : i18n.cls1131s01['L161S01D.updateTime'],//"更新時間"
    			width: 135,
    			name : 'updateTime',
    			align : "left",
    			sortable : false
    		}, {
    			name : 'oid',
    			sortable : false,
    			hidden : true
    		},{
                name : 'pid',
    			sortable : false,
    			hidden : true
            },{
                name : 'refoid',
    			sortable : false,
    			hidden : true
            },{
                name : 'docfileoid',
    			sortable : false,
    			hidden : true
            },{
                name : 'docfileoid2',
    			sortable : false,
    			hidden : true
            },{
                name : 'status',
    			sortable : false,
    			hidden : true
            }],
			loadComplete : function () {
				
			}
    	});
		//地政士
		$("#laaGrid").iGrid({
			handler: 'cls1131gridhandler',
			height: 100,
			autowidth: false,
			needPager: false,
			divWidth:0,
			colModel : [ {
				colHeader : '黑名單',
				name : 'laaCtlFlagType',
				width : 10,
				sortable : false,
				align : "center"
			}, {
				colHeader : '地政士姓名',
				name : 'laaName',
				width : 60,
				sortable : false,
				formatter : 'click',
				onclick : openLaa,
				align : "left"
			}, {
				colHeader : '地政士證書字號',
				name : '',
				width : 120,
				sortable : false,
				align : "left",
				formatter: function enterNumer (cellvalue , options, rowObject) {
	                return '<span>' 
							+ '(' + rowObject[6] + ')'
							+ rowObject[7] + '字'
							+ '第' + rowObject[8] + '號'
							+ '<span/>';
	            }
			}, {
				colHeader : '地政士事務所統編',
				name : 'laaOfficeId',
				width : 60,
				sortable : false,
				align : "left"
			}, {
				colHeader : '地政士事務所名稱',
				name : 'laaOffice',
				width : 90,
				sortable : false,
				align : "left"
			}, {
				colHeader : '地政士黑名單<br/>警示名單敘述',
				name : 'laaDesc',
				width : 90,
				sortable : false,
				align : "left"
			}, {
				colHeader : "laaYear",
				name : 'laaYear',
				hidden : true
			}, {
				colHeader : "laaWord",
				name : 'laaWord',
				hidden : true
			}, {
				colHeader : "laaNo",
				name : 'laaNo',
				hidden : true
			}, {
				colHeader : "laaCtlFlag",
				name : 'laaCtlFlag',
				hidden : true
			}, {
				colHeader : "laaMatchRuleFlag",
				name : 'laaMatchRuleFlag',
				hidden : true
			}, {
				colHeader : "laaQueryDate",
				name : 'laaQueryDate',
				hidden : true
			}, {
				colHeader : "oid",
				name : 'oid',
				hidden : true
			}]
		});
		
		$('#C101S01YAdd').click(function(){
        	openLaa();
        });
		
		$('#C101S01YDelete').click(function(){
        	var rows = $("#laaGrid").getGridParam('selrow');
			var sOid = "";
			if (rows != 'undefined' && rows != null && rows != 0) {
				var data = $("#laaGrid").getRowData(rows);
				sOid = data.oid;
			}
			if (sOid == "") {
				CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
				return;
			}
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){
					$.ajax({
						type : "POST",
						handler: "cls1131formhandler",
						data : {
							formAction : "deleteC101S01Y",
							sOid : sOid,
							mainId: CLS1131S01.data.mainId,
							isC120M01A: CLS1131S01.isC120M01A
						},
						success:function(responseData){
							$("#laaGrid").trigger("reloadGrid");
							//執行成功
							CommonAPI.showPopMessage(i18n.def["runSuccess"], function(){
							});
							$("#msg_Laa").html(DOMPurify.sanitize(responseData.msg_Laa));
						}
					});
				}
			});
        });
		
		function openLaa(cellvalue, options, rowObject){
			var openBts;
			openBts = API.createJSON([{
				key: i18n.def['sure'],
				value: function(){
					var C101S01YForm = $("#C101S01YForm");
					if (C101S01YForm.find("#laaOfficeId").val().trim() !== "") {
						C101S01YForm.find("#laaOffice").prop('required',true);
					} else {
						C101S01YForm.find("#laaOffice").prop('required',false);
					}
					if (C101S01YForm.valid()) {
						$.ajax({
							type : "POST",
				            handler: "cls1131formhandler",
				            action: 'checkC101S01Y',
				            data: C101S01YForm.serializeData(),
				            success: function(responseData){
				                if (responseData.msg) {
									API.showPopMessage(responseData.msg);
									return;
								} else {
									$.ajax({
										type : "POST",
							            handler: "cls1131formhandler",
							            action: 'saveC101S01Y',
							            data: $.extend(C101S01YForm.serializeData(), {
						                    sOid: C101S01YForm.find("#C101S01YOid").val(),
											mainId: CLS1131S01.data.mainId,
											caseSrcFlag: $C101S01EForm.find("input[name='caseSrcFlag']:checked").val(),
											isC120M01A: CLS1131S01.isC120M01A
						                }),
							            success: function(responseData){
							            	if (!responseData.NOTIFY_MESSAGE) {
							            		$("#laaGrid").trigger("reloadGrid");
							            		//執行成功
							            		CommonAPI.showPopMessage(i18n.def["runSuccess"], function(){
							            			$.thickbox.close();
							            		});
							            	}
							            	$("#msg_Laa").html(DOMPurify.sanitize(responseData.msg_Laa));

							            }
							        });
								}
				            }
				        });
					}
				}
			}, {
				key: i18n.def['cancel'],
				value: function(){
					$("#C101S01YForm").reset();
					$("#C101S01YOid").val('');
					$.thickbox.close();
				}
			}]);
			$("#laaDetail").thickbox({
				title : '地政士維護',
				height : 300,
				align : "center",
				valign : "bottom",
				model : true,
				i18n : i18n.def,
				open: function(){
					$("#C101S01YForm").reset();
					$("#C101S01YOid").val('');
					$.ajax({
						type : "POST",
						handler: "cls1131formhandler",
						data : {
							formAction : "getC101S01Y",
							sOid : rowObject.oid,
							isC120M01A: CLS1131S01.isC120M01A
						},
						success:function(responseData){
							$("#C101S01YOid").val(responseData.oid);
							$("#C101S01YForm").injectData(responseData);
						}
					});
	            },
				buttons : openBts
			});
		}
		
		$("a[href='#CLS1131S01_E']").click(function(){
			$("#laaGrid").jqGrid('setGridParam', {
				postData: {
					formAction: "queryC101S01Y",
					mainId : CLS1131S01.data.mainId,
					custId : CLS1131S01.data.custId,
					dupNo : CLS1131S01.data.dupNo,
					isC120M01A: CLS1131S01.isC120M01A
		        }
			});
			$("#laaGrid").trigger("reloadGrid"); 
		});

		$("input[id='cmfWarnpResult'][value='3']").parent().attr('style', 'display:none');
    },
    /**
     * 個人放款信用評分表 ==============================================================
     */
    F_build: function(){
        var $C101S01FForm = $('#C101S01FForm');
        // 是否填列
        $C101S01FForm.find('#chkFlag').setItems({
            item: CLS1131S01.items['Common_YesNo'],
            format: '{key}',
            sort: 'desc',
            value: 'N',
            fn: function(){
                var $form = $('#C101S01FForm');
                $form.find('#chkFlagDiv').hide();
                $form.find('input[name=chkFlag]:checked').each(function(){
                    if ($(this).val() == 'Y') {
                        $form.find('#chkFlagDiv').show();
                    }
                });
            }
        });
        // 引進資料
        $C101S01FForm.find('#btImportData').click(function(){
            CLS1131S01.importData();
        });
        // 計算
        $C101S01FForm.find('#btCalculate').click(function(){
            CLS1131S01.calculate();
        });
        //合計
        $C101S01FForm.find('.number').change(function(){
            CLS1131S01.calculate();
        });
    },
    /**
     * 申貸資料核對表 ======================================================================
     */
    V_build: function(){
    	//J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
        var $C101S01VForm = $('#C101S01VForm');
        CLS1131S01VPageSetting();
    },
	
	/**
     * 勞工紓困4.0 init 程式======================================================================
     */
	X_build: function(){
		var $C101S01XForm = $('#C101S01XForm');
		//LabourBailout4_0.initC101S01X();
        $C101S01XForm.find('#computeScore').click(function(){
        	LabourBailout4_0.computeScore();
        });
    },

	/**
     * 系統初評 ==============================================================
     */
    T_build: function(){
        var $C101S02CForm = $('#C101S02CForm');
        $C101S02CForm.buildItem();
    },

    /**
     * 信貸集中徵信 ==============================================================
     */
    Z_build: function(){
        var $C101S01ZForm = $('#C101S01ZForm');
        $C101S01ZForm.find('#btnImpKYCUpdaterData').click(function(){
        	$.ajax({
                handler: CLS1131S01.handler,
                action: 'impKYCData',
                success: function(response){
                    $('#C101S01ZForm').find("[name=kycUpdaterName]").val(response.userId);
                    $('#C101S01ZForm').find("[name=kycUpdater]").val(response.userName);
                    $('#C101S01ZForm').find("[name=kycUpdateTime]").val(response.dataTime);
                }
            });
        })
        
        $C101S01ZForm.find('#btnImpKYCApproverData').click(function(){
        	$.ajax({
                handler: CLS1131S01.handler,
                action: 'impKYCData',
                success: function(response){
                    $('#C101S01ZForm').find("[name=kycApprover]").val(response.userName);
                    $('#C101S01ZForm').find("[name=kycApprTime]").val(response.dataTime);
                    $('#C101S01ZForm').find("[name=kycApproverName]").val(response.userId);
                }
            });
        })
    },
    
    /**
     * 開啟文件
     */
    open: function(parm){
    	ilog.debug("@CLS1131S01Page.js > open[parm.isC120M01A="+(parm.isC120M01A||"")+"][parm.mainId="+(parm.mainId||"")+"]");  
        if (parm.isC120M01A) {
            CLS1131S01.isC120M01A = true;
        }
        if (parm.isC122M01A) {
            CLS1131S01.isC122M01A = true;
            CLS1131S01.c122m01a_mainId = parm.c122m01a_mainId;
            CLS1131S01.c122m01a_mainOid = parm.c122m01a_mainOid;
            CLS1131S01.c122m01a_oid = parm.c122m01a_oid;
            CLS1131S01.applyKind = parm.applyKind;
        }
        //init
        CLS1131S01.init();

        // set thickbox button
        var thickBoxButtons = {};
        
        thickBoxButtons[i18n.def['saveData']] = function(){
            var check = true;
            for (var index in CLS1131S01.forms) {
                var formName = CLS1131S01.forms[index];
                if (check) {
                    check = $('#' + formName).valid();
                }
            }
            
            if (check) {
            	//檢查是否為歡喜信貸專案
            	CLS1131S01.data['isNeedCheckCLSJobInput'] = ($('#C101M01AForm').find("[name=markModel][value=3]").is(":checked")) ? 'Y' : 'N';
                $.when(CLS1131S01.checkMarkModel()).done(function(){
                    //檢核通過才執行儲存
                    CLS1131S01.save();
                }).fail(function(){
                    //check = false;
                });
            }
        }
        thickBoxButtons[i18n.def['del']] = function(){
            MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                if (action) {
                    //2013-06-26,Rex,add,當有評等資料且為簽報書，要打刪除原因
                    if (CLS1131S01.isC120M01A && CLS1131S01.queryDelectCase()) {
                        var $deleteReasonForm = $("#deleteReasonForm");
                        var item = CommonAPI.loadCombos(["CLSDeleteReason"]);
                        $deleteReasonForm.find("#deleteReason").hide();
                        if ($deleteReasonForm.find("#deleteReasonSelect option").length == 0) {
                            $deleteReasonForm.find("#deleteReasonSelect").setItems({
                                item: item.CLSDeleteReason,
                                format: ' {value}-{key}',
                                fn: function(){
                                    if ($(this).val() == "A4") {
                                        $deleteReasonForm.find("#deleteReason").show();
                                    }
                                    else {
                                        $deleteReasonForm.find("#deleteReason").hide();
                                        $deleteReasonForm.find("#deleteReason").val("");
                                    }
                                }
                            });
                        }
                        $deleteReasonForm.reset();
                        $deleteReasonForm.find("#deleteReason").prop("readonly",false);
                        $deleteReasonForm.find("#deleteReasonSelect").prop("disabled",false);
                        $('#openDeleteReasonBox').thickbox({
                            //C101M01A.deleteReason=刪除原因
                            title: i18n.cls1131s01['C101M01A.deleteReason'],
                            width: 500,
                            height: 200,
                            align: 'center',
                            valign: 'bottom',
                            buttons: {
                                'sure': function(){
                                    if ($deleteReasonForm.valid()) {
                                        $.ajax({
                                            handler: CLS1131S01.handler,
                                            action: 'deleteCust',
                                            data: $.extend(CLS1131S01.data, {
                                                reason: $deleteReasonForm.find("#deleteReasonSelect").val(),
                                                reasonOth: $deleteReasonForm.find("#deleteReason").val()
                                            }),
                                            success: function(response){
                                                CLS1131S01.reloadGrid();
                                                $.thickbox.close();
                                                if (response.cleanTitle) {
                                                    $("#mainDupNo").html("");
                                                    $("#showTypCd").html("");
                                                    $("#showCustId").html("");
                                                    $("3mainDupNo").html("");
                                                }
                                                if(CLS1131S01.isC120M01A){
                                                	check_clsRatingModel();// in CLS1141M01Page.js
                                            	}
                                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                                            }
                                        });
                                    }
                                },
                                'cancel': function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                    else {
                        $.ajax({
                            handler: CLS1131S01.handler,
                            action: 'deleteCust',
                            data: CLS1131S01.data,
                            success: function(response){
                                CLS1131S01.reloadGrid();
                                if (response.cleanTitle) {
                                    $("#mainDupNo").html("");
                                    $("#showTypCd").html("");
                                    $("#showCustId").html("");
                                    $("3mainDupNo").html("");
                                }
                                if(CLS1131S01.isC120M01A){
                                	check_clsRatingModel();// in CLS1141M01Page.js
                            	}
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                            }
                        });
                    }
                }
            });
        }
        thickBoxButtons[i18n.def['print']] = function(){
            if (FormAction.check(CLS1131S01.forms) || CLS1131S01.readOnly) {
                CLS1131S01.showDetial();
            }
            else {
            	if(TermGroupAction.isMarkModel3()){
            		TermGroupAction.callTermGroupRuleQuery(false);
            	}
                MegaApi.confirmMessage(i18n.def["saveBeforeSend"], function(action){
                    if (action) 
                        CLS1131S01.save(CLS1131S01.showDetial);
                });
            }
        }
        thickBoxButtons[i18n.def['close']] = function(){
//            //J-113-0199 判斷若為進件管理開啟，refresh進件管理頁面
//            if(CLS1131S01.isC122M01A){
//                ilog.debug("離開按鈕");
//            }
            if (CLS1131S01.readOnly) {
                $.thickbox.close();
            }
            else {
                FormAction.check(CLS1131S01.forms, function(){
                    CLS1131S01.reloadGrid();
    		        
                    $.ajax({ //仿 common.properties.js 的 onunload
    		            handler: 'checkOpenerhandler', global:false, async: false, type: 'post',
    		            data: {
    		            	checkOpenerAction: 'ClosePage',
    		                mainId: CLS1131S01.data.mainId
    		            }
    		        });
                });
            }
        }

        if (CLS1131S01.readOnly) {
            delete thickBoxButtons['saveData']; // remove save button
            delete thickBoxButtons[i18n.def['saveData']];
        }
        
        //操作者只有 EL00 時
        if (CLS1131S01.readOnly) {
        	if (CLS1131S01.isC120M01A) {
                
            }else{
            	delete thickBoxButtons[i18n.def['del']];
            }	
        }
        
        if (CLS1131S01.isC120M01A) {
        
        }
        else {
            delete thickBoxButtons[i18n.cls1131s01['btn.C120M01A.markModel']];
        }
        
		
        // ready build
        if (!CLS1131S01.ready) {
            CLS1131S01.ready = CLS1131S01.build();
            // set readOnly
            var $div = $('#CLS1131S01Div');
            $div.find('.readonly').readOnly();
            if (CLS1131S01.readOnly) {
                $div.readOnlyChilds();
                $div.find('a').filter('.readOnly').unbind('click').css({
                    'text-decoration': 'none',
                    'cursor': 'default'
                });
                $div.find('a').filter('.query').hide();
                // remove button
                var btns = $div.find("button").not(".forview");
                btns && btns.hide();
            }
        }
        // 總處才可編輯婉卻資訊
        if (userInfo && !(userInfo.ssoUnitNo || '').startsWith('9')) {
            $("#btEditReject").hide();
            $('#rejectInfo').readOnlyChilds();
        }
        
        // 總處才可 開啟等級評分表 button
        if (userInfo && !(userInfo.ssoUnitNo || '').startsWith('9')) {
            $('#btScoreInfo_markModel_1').hide();
            $('#btScoreInfo_markModel_2').hide();
            $('#btScoreInfo_markModel_3').hide();
        }
        //J-111-0285_10702_B1001 Web e-Loan開放大安、板南跟南東編輯徵信>信貸集中審核頁籤
        if (userInfo && (!(userInfo.ssoUnitNo || '').startsWith('9') && !((userInfo.ssoUnitNo || '')=='027' || (userInfo.ssoUnitNo || '')=='070'  || (userInfo.ssoUnitNo || '')=='229'))) {
        	$('#C101S01ZForm').readOnlyChilds();
        	$('#btnImpKYCUpdaterData').hide();
        	$('#btnImpKYCApproverData').hide();
        }
        
        //開給 test user 
        //$('#btScoreInfo_markModel_1').show();
        //$('#btScoreInfo_markModel_2').show();	
        
        // form clear
        for (var index in CLS1131S01.forms) {
            var formName = CLS1131S01.forms[index];
            $('#' + formName).setValue();
            if(formName=="C101S01ZForm"){
            	$("#kycUpdaterMemo").empty();
            	$("#kycApproverMemo").empty();
            }
        }
        if(true){
        	$("#l120s01m_queryDate").html('');
        	$("#c101s01e_wm_data").val('');
        	$("#wm_qDate").val('');
        }
        
        // open thickbox
        $("#CLS1131S01ThickBox").thickbox({
            title: (i18n.cls1131s01['title.master'] || '')+parm.custId+"-"+parm.dupNo+" "+parm.custName, //借保人資料
            width: 950,
            height: 550,
            buttons: thickBoxButtons
        });
        // default tab
        $('#CLS1131S01Div').tabs({
            selected: 0
        });
        $('#CLS1131S01Div').find('.startHide').hide();
		
        if (CLS1131S01.isC120M01A) {
            
        } else {
        	if(true){
        		var curr_param = {'memo_twdcurr_s01b':'payCurr', 'memo_twdcurr_s01c':'yFamCurr'};
        		for(var memo_id in curr_param){
        			if($("#"+memo_id).length>0){}else{
                		$("#"+curr_param[memo_id]).closest("table")
                		.before("<span id='"+memo_id+"' class='color-red' style='margin-left:15px;'><b>※國內消金評等之計價幣別為台幣</b></span>");
                	}		
        		}
        	}
        	
            $(['payCurr','othCurr','yFamCurr','invMBalCurr','invOBalCurr','branCurr','fincomeCurr', 'indAssetTotAmtCurr', 'indDebtBalanceCurr', 'fAssetTotAmtCurr', 'fDebtBalanceCurr']).each(function(idx, fid){
            	$("#"+fid).find("option:not([value=TWD])").remove();
            });
            $("#mPayCurr").find("option:not([value=TWD],[value=''])").remove();        
        }
        
        if(true){
        	$("tr.hs_jcicFlg").hide();
        }
        
        if(true){
        	$("#btImportWebBankApply").hide();
        	$("#btImportWebProd69").hide();
        	$("#btImportWebPLOAN").hide();
        }
        
		$("#dRate").prop("readonly", true);
		$("#yRate").prop("readonly", true);
		
        // 讀取資料
        CLS1131S01.load(parm);
    },
    /**
     * 讀取資料
     */
    load: function(data){
        //清空前筆資料
        var $C101M01AForm = $('#C101M01AForm');
        
        $C101M01AForm.find("[name=markModel]").prop("checked", false);
        if (CLS1131S01.isC120M01A) {
            //C120M01A default 不開放更改 markModel
        }
        else {
            $C101M01AForm.find("[name=markModel]").prop("disabled",false);
        }
        
        $C101M01AForm.find("#fieldset_markModel_0").hide();
        $C101M01AForm.find("#fieldset_markModel_1").hide();
        $C101M01AForm.find("#fieldset_markModel_2").hide();
        $C101M01AForm.find("#fieldset_markModel_3").hide();
        //雙軌
        $C101M01AForm.find("#scoreDoubleTrack_G_notes").hide();
    	$C101M01AForm.find("#scoreDoubleTrack_G_info").hide();
    	$C101M01AForm.find("#scoreDoubleTrack_Q_notes").hide();
    	$C101M01AForm.find("#scoreDoubleTrack_Q_info").hide();
    	$C101M01AForm.find("#scoreDoubleTrack_R_notes").hide();
    	$C101M01AForm.find("#scoreDoubleTrack_R_info").hide();

        //---
        $("#tab_LS1131S01_F").hide()
        
        ilog.debug("@CLS1131S01Page.js > load【ajax:loadCust】begin{custId="+(data.custId||'')+", mainId="+(data.mainId||'')+"}");
        
        // load data
        $.ajax({
            handler: CLS1131S01.handler,
            action: 'loadCust',
            data: $.extend(data, {
                noOpenDoc: true
            }),
            success: function(response){
                autoCheckBrmp.init(response.rtn_json);
                // set sendData
                CLS1131S01.data = $.extend({
                    noOpenDoc: true
                }, response.C101M01AForm);
                
                //雙軌模型
                if(response.showSDT_G == true){
                	$C101M01AForm.find("#scoreDoubleTrack_G_notes").show();
                	$C101M01AForm.find("#scoreDoubleTrack_G_info").show();
                }
                if(response.showSDT_Q == true){
                	$C101M01AForm.find("#scoreDoubleTrack_Q_notes").show();
                	$C101M01AForm.find("#scoreDoubleTrack_Q_info").show();
                }
                if(response.showSDT_R == true){
                	$C101M01AForm.find("#scoreDoubleTrack_R_notes").show();
                	$C101M01AForm.find("#scoreDoubleTrack_R_info").show();
                }
                
                
                if(response.c122m01a_mainId && response.c122m01a_mainId!=''){
                	CLS1131S01.c122m01a_mainId = (response.c122m01a_mainId||'');
                	$("#btImportWebBankApply").show();
                }                
                if(response.c122m01a_mainId_applyKindB && response.c122m01a_mainId_applyKindB!=''){
                	CLS1131S01.c122m01a_mainId_applyKindB = (response.c122m01a_mainId_applyKindB||'');
                	$("#btImportWebProd69").show();
                }     
                if(response.c122m01a_mainId_applyKind_PLOAN && response.c122m01a_mainId_applyKind_PLOAN!=''){
                	CLS1131S01.c122m01a_mainId_applyKind_PLOAN = (response.c122m01a_mainId_applyKind_PLOAN||'');
                	$("#btImportWebPLOAN").show();
                }
				
				//地政士查詢
				if(response.param_oneBtnQuery && response.param_oneBtnQuery.RPA_AGENT_SHOW_FUNC != 'Y'){
            		$('#queryRPA ,#queryRPAResult').hide();
            	}
					
                if(true){                	
                    if(response.active_SAS_AML=="1"){
                    	if (CLS1131S01.readOnly) {
                    		
                    	}else{
                    		$("#C101S01EForm #btSendAmlList").show();
                    		$("#C101S01EForm #btCheckAmlResult").show();
                    	}
                    	$("#C101S01EForm #amlDiv").show();
                    	$("#C101S01EForm #amlDiv2").show();
                    	//另加入 disabled 控制
                    	$("a.query[queryType=isQdata7]").attr("data-activeSAS", "Y");
                    }else if(response.active_SAS_AML=="2"){
                    	if (CLS1131S01.readOnly) {
                    		
                    	}else{
                    		$("#C101S01EForm #btSendAmlList").show();
                    		$("#C101S01EForm #btCheckAmlResult").show();
                    	}
                    	$("#C101S01EForm #amlDiv").show();
                    	$("#C101S01EForm #amlDiv2").show();
                    	// ===
                    	$("a.query[queryType=isQdata7]").attr("data-activeSAS", "Y");
                    }else{
                    	$("#C101S01EForm #btSendAmlList").hide();
                    	$("#C101S01EForm #btCheckAmlResult").hide();
                    	$("a.query[queryType=isQdata7]").attr("data-activeSAS", "N");
                    }
                }
                
                // 判斷是否有自然人
                var $div = $('#CLS1131S01Div');
                var $fm = $('#C101S01EForm');
                $fm.find('.ABCD').readOnly();
                if (CLS1131S01.data.naturalFlag == 'N') {
                    $div.find('.naturalClass').hide();
                    $fm.find('.CD').readOnly(CLS1131S01.readOnly);
                }
                else {
                    $div.find('.naturalClass').show();
                    $fm.find('.AB').readOnly(CLS1131S01.readOnly);
                }
                //set value
                for (var index in CLS1131S01.forms) {
                    var formName = CLS1131S01.forms[index];
                    $div.find('#' + formName).setValue(response[formName], false);
                    
                    if (response.C101S01FForm && formName == "C101S01FForm" && CLS1131S01.isC120M01A) {
                        $("#tab_LS1131S01_F").show();
                        
                        var c120s01f_chkFlag = $div.find('#' + formName).find('input[name=chkFlag]:checked').val(); 
                        if (c120s01f_chkFlag == 'Y') {
                        	$div.find('#' + formName).find('#chkFlagDiv').show();
                        }
                    }
                }
                hs_rejectInfo();
                hs_abnormalInfo();
                hs_jcicFlg(response.C101M01AForm);
                hs_mateFlag();                
                // init check 
                //FormAction 定義在  mega.eloan.properties.js
                FormAction.init(CLS1131S01.forms);
                // set cust info
                CLS1131S01.setCustInfo();
                // 是否為手動
                CLS1131S01.manual = true;
                
                //2013-06-20,Rex,add,當為簽報書要判斷修改婉卻按鈕是否要出現
                if (CLS1131S01.isC120M01A) {
                    var $erjButton = $("#erjButton");
                    $erjButton.hide();
                    var unitType = userInfo.unitType;
                    if ((unitType == "4" && lmsM01Json.docStatus == "L1H") || ((unitType == "2") && lmsM01Json.docStatus == "L1C")) {
                        $erjButton.show();
                        //總處才可編輯婉卻資訊
                        if (userInfo){
                        	if(!(userInfo.ssoUnitNo || '').startsWith('9')) {
                            	$("#btEditReject").hide();
                            }else{
                            	$("#btEditReject").show();
                            }
                        }
                    }                    
                }
                //===
                if (CLS1131S01.isC120M01A) {
                    var unitType = userInfo.unitType;
//                  if (unitType == "1" && lmsM01Json.docStatus == "01O") { 原寫法unitType == "1"，若遇到 201（unitType = "5"）會 return false => 無法勾選
                    if (lmsM01Json.docStatus == "01O") {
                        $C101M01AForm.find("[name=markModel]").prop("disabled",false);
                    }
                }
                //===				
				if(CLS1131S01.isC120M01A && responseJSON.mainDocStatus=='01O' && CLS1131S01.data.naturalFlag == 'N'){
					$("#chgNameLink").show();
				}
				//===
                if (CLS1131S01.isC120M01A) {
                	
                }else{
                	if(response.C101S01BForm && response.C101S01BForm.ptaFlag=='Y'){
                		var ptaTaxNo = response.C101S01BForm.ptaTaxNo || "";
                		var ptaGrade = response.C101S01BForm.ptaGrade || "";
                		var ptaGradeMsg = "";
                		if(ptaGrade=="" || ptaGrade==" "){
                			
                		}else{
                			ptaGradeMsg = i18n.cls1131s01["C101S01B.ptaGrade.descBf"]+ptaGrade+i18n.cls1131s01["C101S01B.ptaGrade.descAf"]; 
                		}
                		//配合勞工紓困，將會有 RPA 介入  =>  不跳出 dialog 的訊息 
                		ilog.debug(i18n.cls1131s01["C101S01B.ptaFlag.msg1"]+"<br/>"
                				+i18n.cls1131s01["C101S01B.ptaTaxNo.label"]+"："+ptaTaxNo+ptaGradeMsg+"<br/>"
                				+i18n.cls1131s01["C101S01B.ptaFlag.msg2"]);
                	}

                	if (CLS1131S01.data.naturalFlag == 'N') {
                		
                	}else{
                		var cls1131_sex_val = $('#C101S01AForm').find("input[name=sex]:checked").val()||'';
                		if(cls1131_sex_val=='M'||cls1131_sex_val=='F'){                    	
                        	//已有性別
                        }else{
                        	var custId1st_ascii = CLS1131S01.data.custId.charCodeAt(0);
                        	if(custId1st_ascii>=65 && custId1st_ascii<=90){
                        		var custId2nd_ascii = CLS1131S01.data.custId.charCodeAt(1);
                        		if(custId2nd_ascii==49){
                        			$('#C101S01AForm').find("input[name=sex][value=M]").prop("checked", "checked");
                        		}else if(custId2nd_ascii==50){
                        			$('#C101S01AForm').find("input[name=sex][value=F]").prop("checked", "checked");
                        		}
                        	}
                        }
                		//~~~~~~~~~~~~~~~~~~~~~~~~~~
                		var cls1131_idCardPhoto_val = $('#C101S01AForm').find("#idCardPhoto").val()||'';
                		if(cls1131_idCardPhoto_val==''){
                        	$('#C101S01AForm').find("#idCardPhoto").val("Y");
                        }
                	}
                }
				//J-108-0277 個金查詢客戶報表綜合資料
//				InterfaceSystemDataInquiry.loadInterfaceSystemData(response);
                //剛開啟頁面時，[custId 及 isC120M01A]無資料，在[loadCust]之後，需做 setGridParam
                InterfaceSystemDataInquiry.grid1.jqGrid("setGridParam", {
                    postData: {
                    	  'mainId' : CLS1131S01.data.mainId
                    	, 'custId': CLS1131S01.data.custId
            			, 'dupNo': CLS1131S01.data.dupNo
            			, 'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N")
						, 'ownBrId': CLS1131S01.data.ownBrId
                    },
                    search: true
                }).trigger("reloadGrid");   
                
                if (CLS1131S01.isC120M01A) {
                	
					if(response.C101S01EForm != undefined){
						
						if(response.C101S01EForm.version == '1.0' || response.C101S01EForm.version == undefined){
							$('#C101S01EForm').find("#isQdata8Tr").show();
							$('#C101S01EForm').find("#isQdata30Tr").hide();
						}
						else{
							$('#C101S01EForm').find("#isQdata30Tr").show();
							$('#C101S01EForm').find("#isQdata8Tr").hide();
						}
					}
					
                }else{
                	$('#C101S01EForm').find('button.btnSendOneButtonQuery').removeClass("ui-state-disabled").prop("disabled",false);
                }
                //=================
                if(CLS1131S01.data.naturalFlag == 'Y'){
                	if (CLS1131S01.isC120M01A) {
                    	$(".hideWhenC120M01A").hide(); //包含 click button 的區塊
                    }else{
                    	$(".hideWhenC120M01A").show();
                    }	
                	if (CLS1131S01.readOnly) {
                    	$(".hideWhenCLS1131S01_readOnly").hide(); //包含 click button 的區塊
                    }else{
                    	$(".hideWhenCLS1131S01_readOnly").show();
                    }
                }else{
                	// 一鍵查詢裡的「證券暨期貨違約交割紀錄」，個人戶有顯示「非上市櫃公司觀察名單」
                	// 當公司戶的ID，就隱藏整個區塊
                	$("#interfaceSystemDataInquiryFieldSet").hide();
                }
                if($('#C101S01AForm').find("#checkListFlag:checked").val()=="Y"){
        			$("#liCLS1131S01V").attr("style","display:block;");
        		}else{
        			$("#liCLS1131S01V").attr("style","display:none;");
        		}
            
                if(response.param_oneBtnQuery){
                	if(response.param_oneBtnQuery.openWay=="A"){ //設定 CLS1131S01.data.isPopupResultWindow
                		CLS1131S01.data['isPopupResultWindow'] = "Y";
                	}else{
                		CLS1131S01.data['isPopupResultWindow'] = "N";
                	}
                	//~~~~~~
                	if(response.param_oneBtnQuery.defined_etch_timeout){
                		CLS1131S01.defined_etch_timeout = response.param_oneBtnQuery.defined_etch_timeout; 
                	}
                	if(response.param_oneBtnQuery.defined_ejcic_timeout){
                		CLS1131S01.defined_ejcic_timeout = response.param_oneBtnQuery.defined_ejcic_timeout; 
                	}
                }
                if(response.chk_c120s01v){
                	$("#CheckListFlag").attr("style","");
        		}else{
        			$("#CheckListFlag").attr("style","display:none;");
        		}
        		//J-113-0199 非進件管理隱藏相關自動過件功能
        		if (response.autoCheck_showPage) {
                    CLS1131S01.autoCheck_showPage = true;
                }
                ilog.debug("autoCheck_bankManFlag:"+ response.autoCheck_bankManFlag);
                if (response.autoCheck_bankManFlag) {
                    CLS1131S01.autoCheck_bankManFlag = true;
                }
				LabourBailout4_0.isShowTag(CLS1131S01.data.isBailout4 == 'Y' ? true : false);
				
				if(response.C101S01XForm != undefined && response.C101S01XForm != null){
					LabourBailout4_0.initC101S01X(response.C101S01XForm)
				}
				
				concentrateCredit.isShowTag(CLS1131S01.data.concentrateCredit == 'Y' ? true : false);

				BatchSelectHeadAccountCheckItem.init();
				if(response.C101S01BForm != undefined && response.C101S01BForm != null){
					controlEsgScoreDisplay(response.C101S01BForm.isHasEsgScore);
					if(($.inArray("3",response.C101M01AForm.markModel) != -1)){
						//有打勾專案信貸
						CLS1131S01.data['isNeedCheckCLSJobInput'] = 'Y';
						if(response.C101S01BForm.isNPO=='Y'){
							isNPO = true;
							$('#isNPO').trigger('change');						
							//統一編號變成非必填。
							$("#juId").prop("required",false);
						}else{
							isNPO = false;
						}
						controlIsNPODisplay(isNPO);
					}else{
						//檢查markModel 3 是不是沒有被打勾
						markModelUncheckAction(false);
						CLS1131S01.data['isNeedCheckCLSJobInput'] = 'N';
					}
				}
            }
        }).done(function(){
        	//取得上次決策api 房貸利率查詢結果
    		LtvAction.init(data.mainId);
    		//客群查詢
    		TermGroupAction.init(data.mainId,data.custId,data.dupNo);
    		
    		//J-113-0341 個金徵信作業新增「年輕族群客戶加強關懷提問單」
            youngCareListShowHide();
        });
    },
    /**
     * set cust info
     */
    setCustInfo: function(){
        var $form = $('#C101M01AForm');
        var $div = $('#topDiv');
        $div.find('#topCustId').html($form.find('#custId').html() || '');
        $div.find('#topDupNo').html($form.find('#dupNo').html() || '');
        $div.find('#topCustName').html($form.find('#custName').html() || '');
    },
    /**
     * 儲存
     */
    save: function(callback){
        //檢查票信和聯徵日期
        if (!CLS1131S01.checkDate()) { 
            return;
        }
        //========================
        $.ajax({
            handler: 'cls1131formhandler',
            action: 'calc_seniority_from_snrY_snrM', async: false,//用「同步」的方式
            formId: 'C101S01BForm',
            success: function(json_calc_seniority){
                //ilog.debug('[calc_seniority_from_snrY_snrM]:'+json_calc_seniority.seniority||'');
                //J-110-0298 , (110)第(2133)號 , 年資 seniority 由 DEC(2,0)變成 DEC(4,2) ===> call ajax 用 {snrY、snrM}去算出 seniority，塞入form後，送 server
            	$("#C101S01BForm").injectData(json_calc_seniority);
                //========================
                $.ajax({
                    handler: CLS1131S01.handler,
                    action: 'saveCust',
                    //formId: CLS1131S01.forms,
                    formId: (CLS1131S01.forms).concat(['C101S01XForm']),
                    data: CLS1131S01.data,
                    success: function(response){
                        $("#custName").prop("readonly", true).addClass("required");
                        
                        for (var index in CLS1131S01.forms) {
                            var formName = CLS1131S01.forms[index];
                            $('#' + formName).setValue(response[formName], false);
                        }
                        hs_mateFlag();
                        //評分結果
                        if (response.C101S01GForm) {
                            $('#gradeDiv_markModel_1').setValue(response.C101S01GForm);
                        }
                        if (response.C101S01QForm) {
                            $('#gradeDiv_markModel_2').setValue(response.C101S01QForm);
                        }
                        if (response.C101S01RForm) {
                            $('#gradeDiv_markModel_3').setValue(response.C101S01RForm);
                        }
                        if (response.C101S01XForm) {
                        	$('#C101S01XForm').injectData(response.C101S01XForm);
                        }
                        //增加雙軌處理(原本無雙軌資料，進行評分後有雙軌，需要開啟雙軌欄位)
                        if(response.showSDT_G){ //有雙軌
                        	if (response.C101S01G_NForm) {
                        		$("#scoreDoubleTrack_G_notes").show();
                            	$("#scoreDoubleTrack_G_info").show();
                                $('#gradeDiv_markModel_1').setValue(response.C101S01G_NForm);
                            }
                        }
                        if(response.showSDT_Q){ //有雙軌
                        	if (response.C101S01Q_NForm) {
                        		$("#scoreDoubleTrack_Q_notes").show();
                            	$("#scoreDoubleTrack_Q_info").show();
                                $('#gradeDiv_markModel_2').setValue(response.C101S01Q_NForm);
                            }
                        }
                        if(response.showSDT_R){ //有雙軌
                        	if (response.C101S01R_NForm) {
                        		$("#scoreDoubleTrack_R_notes").show();
                            	$("#scoreDoubleTrack_R_info").show();
                                $('#gradeDiv_markModel_3').setValue(response.C101S01R_NForm);
                            }
                        }
                        
                        //重新取得[房貸利率查詢結果]
                        LtvAction.init(response.mainId);
                        
                        
                        if(response.C101M01AForm != undefined){
                        	if(($.inArray("3",response.C101M01AForm.markModel) != -1)){
                        		//確認markmodel 3有打勾
                        		if(response.C101S01BForm.isNPO){
                        			controlIsNPODisplay(response.C101S01BForm.isNPO);
                        			//優化弱掃
                        			checkIsNPO(); 
                        		}
                        	}
                        }
                        //J-113-0208 未勾選「非房貸申請信用評等:專案信貸(非團體)」不發查客群決策
                        if(TermGroupAction.isMarkModel3()){
                        	//J-112-0467 歡喜信貸客群查詢, 
                            TermGroupAction.callTermGroupRuleQuery(true);
                        }

                        //兩個開關
                        //1.限制只有進件管理開啟才能看系統初審頁籤
                        //2.只有行員件才查autoCheck
                        if(autoCheckBrmp.showAutoCheckPage()){
                            if (CLS1131S01.autoCheck_bankManFlag && (CLS1131S01.isC122M01A || CLS1131S01.autoCheck_showPage)) {
                                //信貸才能查系統初評，
                                if(CLS1131S01.applyKind=='P'){
                                    autoCheckBrmp.brmp_autoCheck_new(CLS1131S01.data.mainId, $("#C101S01TForm") );
                                }
                            }
                        }
                        // init check 
                        FormAction.init(CLS1131S01.forms);
                        //save success callback
                        if ($.isFunction(callback))
                            callback();
                    }
                });
            }
        });
    },
    /**
     * 檢查票信和聯徵日期
     */
    checkDate: function(){
        var msg = [];
        var $form = $('#C101S01EForm');
        var eChkQDate = $form.find('#eChkQDate').val() || ''; //票信查詢日期
        var eChkDDate = $form.find('#eChkDDate').val() || ''; //票信資料截止日
        var eJcicQDate = $form.find('#eJcicQDate').val() || ''; //聯徵查詢日期
        var eJcicDDate = $form.find('#eJcicDDate').val() || ''; //聯徵資料日期
        var d = new Date();
        var today = d.getFullYear() + '-' +
        util.addZeroBefore((d.getMonth() + 1) + '', 2) +
        '-' +
        util.addZeroBefore(d.getDate() + '', 2);
        
        if (eChkQDate > today) 
            msg.push('票信查詢日期[' + eChkQDate + ']');
        if (eChkDDate > today) 
            msg.push('票信資料截止日[' + eChkDDate + ']');
        if (eJcicQDate > today) 
            msg.push('聯徵查詢日期[' + eChkQDate + ']');
        if (eJcicDDate > today) 
            msg.push('聯徵資料日期[' + eChkDDate + ']');
        
        if (msg.length > 0) {
            msg.push('不可大於系統日期[' + today + ']');
        }
        else {
            if (eChkDDate && eChkQDate) {
                if (eChkDDate > eChkQDate) 
                    msg.push('票信資料截止日[' + eChkDDate + ']不能大於票信查詢日期[' + eChkQDate + ']');
            }
            if (eJcicDDate && eJcicQDate) {
                if (eJcicDDate > eJcicQDate) 
                    msg.push('聯徵資料日期[' + eJcicDDate + ']不能大於聯徵查詢日期[' + eJcicQDate + ']');
            }
        }
        
        if (msg.length > 0) {
            MegaApi.showErrorMessage(i18n.def['confirmTitle'], '<b>相關查詢資料</b><br/>' + msg.join("<br/>"));
            return false;
        }
        return true;
    },
    /**
     * 查詢本行存款帳戶
     */
    queryAccount: function(bank, brno){
        // 選取存款往來銀行，若選取的存款往來銀行為本行(017兆豐)，則再根據客戶統一編號查詢存款帳號。
        if (bank === '017') {
            $.ajax({
                handler: CLS1131S01.handler,
                action: 'queryAccount',
                formId: 'C101M01AForm', //
                data: {
                    brno: brno,
                    custId: CLS1131S01.data.custId,
                    dupNo: CLS1131S01.data.dupNo
                },
                success: function(response){
                    CLS1131S01.selectAccount(response.account);
                }
            });
        }
    },
    /**
     * 選擇帳戶
     */
    selectAccount: function(data){
        if (data) {
            var list = data.list;
            if (list && list.length > 1) {
                var items = {};
                for (var index in list) {
                    var item = list[index] || {};
                    var account = item.Account || '';
                    items[account] = account;
                }
                $('#AccountDiv').find('#_dpAcct').setItems({
                    item: items,
                    format: '{key}'
                });
                
                $('#AccountThickBox').thickbox({
                    title: '', // i18n.cms1400v01["title"],
                    width: 300,
                    height: 150,
                    align: 'center',
                    valign: 'bottom',
                    buttons: {
                        'sure': function(){
                            $('#dpAcct').val($('#_dpAcct').val());
                            $.thickbox.close();
                        },
                        'close': function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
            else {
                var item = list[0] || {};
                var account = item.Account || '';
                if (account) 
                    $('#dpAcct').val(account);
            }
        }
    },
    /**
     * 檢查本行存款帳戶
     */
    checkAccount: function(){
        $.ajax({
            handler: CLS1131S01.handler,
            action: 'checkAccount',
            formId: 'C101S01AForm',
            data: {},
            success: function(response){
                if (response.alert) {
                    MegaApi.showPopMessage(i18n.def["confirmTitle"], response.alert);
                }
            }
        });
    },
	/**
     * RPA地政士調閱附件
     */
    openRpafile: function(fileOid){
        $.capFileDownload({
           handler:"simplefiledwnhandler",
           data : {
               fileOid : fileOid
           }
		});
    },
	/**
     * RPA地政士引進
     */
    importRpaDetail: function(oid){
        $.ajax({
			type : "POST",
			handler : "cls1131formhandler",
			data : {
				formAction : "importRpaDetail",
				mainId : CLS1131S01.data.mainId,
				oid : oid
			},
			success:function(responseData){
				$("#laaGrid").trigger("reloadGrid");
				if (responseData.returnObj && responseData.returnObj.size == 1) {
					$("#laaName").val(DOMPurify.sanitize(responseData.returnObj.laaName));
					$("#laaYear").val(DOMPurify.sanitize(responseData.returnObj.laaYear));
					$("#laaWord").val(DOMPurify.sanitize(responseData.returnObj.laaWord));
					$("#laaNo").val(DOMPurify.sanitize(responseData.returnObj.laaNo));
					$("#laaOffice").val(DOMPurify.sanitize(responseData.returnObj.laaOffice));
					
					//查詢黑名單
					$("#queryLasBlack").click();
					$("#msg_Laa").html(DOMPurify.sanitize(responseData.msg_Laa));
					
					//執行成功
					CommonAPI.showMessage(i18n.def["runSuccess"]);
				} else if (responseData.returnObj && responseData.returnObj.size > 1) {
					//多筆供經辦挑選
					//console.log(responseData.returnObj.agentAry);		
					var agantAry = responseData.returnObj.agentAry;
					
					$("#RpaMultResultBox").thickbox({
						title : '地政士查詢結果',
						width : 700,
						height : 300,
						align: 'center',
				        valign: 'bottom',
						model : true,
						open : function(){
							$("#tbRpaMultResult").empty();
							$("#tbRpaMultResult").append('<tr><td class="hd2" width="3%"></td><td class="hd2" width="20%">地政士姓名 </td><td class="hd2" width="17%">地政士證書年度</td><td class="hd2" width="20%">地政士證書字</td><td class="hd2" width="20%">地政士證書號</td><td class="hd2" width="20%">地政士事務所名稱</td></tr>');
							//動態append html
							for(i = 0; i < agantAry.length; i++ ) {
								//debugger;
								 $("#tbRpaMultResult").append('<tr><td><input type="radio" name="agent" value="' + i +'" />' +
								 	 '</td><td>' + DOMPurify.sanitize(agantAry[i].agentName) +
									 '</td><td>' + DOMPurify.sanitize(agantAry[i].agentCertYear) +
									 '</td><td>' + DOMPurify.sanitize(agantAry[i].agentCertWord) +
									 '</td><td>' + DOMPurify.sanitize(agantAry[i].agentCertNo) +
									 '</td><td>' + DOMPurify.sanitize(agantAry[i].landOffice) + '</td></tr>');
							}
						},
						buttons: {
							"sure": function () {
								if ($('input[name=agent]:checked').length == 0) {
									CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
									return false;
								}
								var selectTr = $('input[name=agent]:checked').closest('tr');
								//alert(selectTr.find('td')[4].innerText);
								
								$.ajax({
									type : "POST",
									handler : "cls1131formhandler",
									data : {
										formAction : "importRpaDetailByAgent",
										mainId : CLS1131S01.data.mainId,
										oid : oid,
										laaName : selectTr.find('td')[1].innerText,
										laaYear : selectTr.find('td')[2].innerText,
										laaWord : selectTr.find('td')[3].innerText,
										laaNo : selectTr.find('td')[4].innerText,
										laaOffice : selectTr.find('td')[5].innerText
									},
									success:function(responseData){
										$("#laaGrid").trigger("reloadGrid");
										$("#msg_Laa").html(DOMPurify.sanitize(responseData.msg_Laa));
										//執行成功
										CommonAPI.showPopMessage(i18n.def["runSuccess"], function(){
											$.thickbox.close();
										});
									}
								});
							},
							"cancel": function () {
								$.thickbox.close();
							}
						}
					});
				}
			}
		});
    },
    /**
     * 相關資料查詢
     */
    queryRelatedData: function(){
        $.ajax({
            handler: CLS1131S01.handler,
            action: 'queryRelatedData',
            formId: CLS1131S01.forms,
            data: CLS1131S01.data,
            success: function(response){
                // 評等訊息
            	if (response.gradeDiv_markModel_0) {
                    $('#gradeDiv_markModel_0').setValue(response.gradeDiv_markModel_0);
                }
            	if (response.gradeDiv_markModel_1) {
                    $('#gradeDiv_markModel_1').setValue(response.gradeDiv_markModel_1);
                }
                if (response.gradeDiv_markModel_2) {
                    $('#gradeDiv_markModel_2').setValue(response.gradeDiv_markModel_2);
                }
                if (response.gradeDiv_markModel_3) {
                    $('#gradeDiv_markModel_3').setValue(response.gradeDiv_markModel_3);
                }
                if (response.C101S01EForm) {
                    $('#C101S01EForm').setValue(response.C101S01EForm, false);
                    // 查詢(全部資料)
                    //CLS1131S01.queryData();
                }
                if(true){
                	CLS1131S01.sync_ej_data_to_C101S01E();                	
                }
                CLS1131S01.queryData();
            }
        });
    },
    /**
     * 查詢, 若無聯徵/票信, server 傳回結果會是 ajax fail => 導致未跳出 "接下來的 eName dialog"
     */
    queryData: function(type){
        var data = $.extend(CLS1131S01.data, {
            type: type || ''
        });
        $.ajax({
            handler: CLS1131S01.handler,
            action: 'queryData',
            formId: CLS1131S01.forms,
            data: data,
            success: function(response){
                var $eForm = $('#C101S01EForm');
                $eForm.setValue(response.C101S01EForm, false);
                
                if(true){
                	var _type = "ukwn";
                	if(response.param_map && response.param_map.type){
                		_type = response.param_map.type;
                	}
                	ilog.debug("@CLS1131S01Page.js :: queryData type=["+_type+"][done]");	
                }
                
                if (response.C101M01AForm) {
                    $('#C101M01AForm').setValue(response.C101M01AForm, false);
                    hs_rejectInfo();
                    hs_abnormalInfo();
                    hs_jcicFlg(response.C101M01AForm);
                    hs_mateFlag();
                }
                // 點擊 黑名單 這一列最右邊的 '查詢' 且缺少 eName => 打開輸入英文名視窗
                if (type == 'isQdata7' && $eForm.find('#eName').val() == '') {
                	if($("a.query[queryType=isQdata7]").attr('data-activeSAS')=="Y"){
                		//...		
                	}else{
                		CLS1131S01.openEName();	
                	}
                }
                else {
                	//當未指定 inputParam: type 時，會呼叫 CLS1131S01.openEName()
                    if (!type) {
                        //if (!type && $eForm.find('#eName').val() == ''){
                    	//J-109-0190_05097_B1001 為加速勞工紓困貸款之案件簽報，簡化e-Loan洗錢防制流程
                    	CLS1131S01.openEName();
                    }
                    else {
                        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["runSuccess"]);
                        if(response.alterMsg){
                           MegaApi.showPopMessage(response.alterMsg);
                        }
                        // 相關查詢後執行 form init
                        if (!type) 
                            FormAction.init(CLS1131S01.forms);
                    }
                }
            }
        });
    },
    decide_cls1131_amlflag: function(){
    	var my_dfd = $.Deferred();    	
    	 
    	//不要用 visible 來判斷 => 在簽報書時, button 都會被隱藏
        //if( $("#C101S01EForm #btSendAmlList").is(":visible") ){
    	$.ajax({
			type : "POST", handler : "cls1131formhandler", 
			data : {
				formAction : "decide_cls1131_amlflag" ,
				mainId : CLS1131S01.data.mainId,
				custId : CLS1131S01.data.custId, 
				dupNo : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N")
			},
			success:function(json){
				if(json.flag){
					my_dfd.resolve({'flag':json.flag, 'eName0024':json.eName0024});			
				}
			}
		});        	
        
        return my_dfd.promise();
    },
    /**
     * 黑名單英文名
     */
    openEName: function(){
        $('#eNameThickBox').find('#eName').val($('#C101S01EForm').find('#eName').html());
        //J-109-0190_05097_B1001 為加速勞工紓困貸款之案件簽報，簡化e-Loan洗錢防制流程
        //J-109-0426_05097_B1001 為加速青創貸款之案件簽報，擬比照勞工紓困貸款方式，申請簡化洗錢防制流程
        CommonAPI.iConfirmDialog({
			message: "勞工紓困貸款案件(產品種類：69)及青年創業及啟動金貸款案件(產品種類：61)，e-Loan系統中之制裁/管制名單掃瞄，僅須於「動審」階段執行。<BR>●若按「是」，則繼續執行名單掃瞄作業。<BR>●若按「否」，則本案屬紓困案或青年創業及啟動金貸款案件，可不執行名單掃描，系統清除黑名單查詢資料並將查詢結果設定為N.A.。<BR>●若按「取消」，取消本次作業。",
			buttons: API.createJSON([{
				key: i18n.def.yes,
				value: function(){
					CLS1131S01.decide_cls1131_amlflag().done(function(json_decide){
	                	var amlflag = json_decide.flag||'';
	                	$('#eName0024').val(json_decide.eName0024||'');
	                	//=========
	                	
	                	var btnArr = {};
	                    var tb_height = 150;
	                    if(CLS1131S01.isC120M01A){
	                    	//only show close button
	                    }else{
	                    	if(amlflag=="0"){
	                        	btnArr["sure"] = function(){
	                                $.thickbox.close();
	                                $('#C101S01EForm').find('#eName').html(DOMPurify.sanitize($('#eNameThickBox').find('#eName').val()));
	                                CLS1131S01.queryData('isQdata7');
	                            };	
	                        }else{
	                        	btnArr["tbSendAmlList"] = function(){     
	                        		$('#C101S01EForm').find('#eName').html(DOMPurify.sanitize($('#eNameThickBox').find('#eName').val()));
	                        		//===============
	                                CLS1131S01.sendAmlList(1).done(function(){
	                                	$.thickbox.close();
	                                	CLS1131S01.checkAmlResult();
	                                });                	
	                            };	
//	                          btnArr["tbCheckAmlResult"] = function(){
//	                              CLS1131S01.checkAmlResult(1).done(function(){
//	                               	$.thickbox.close();
//	                              });                
//	                          };
	                        }                
	                    }
	                    if(amlflag=="0"){
	                    	//舊的黑名單查詢功能
	                    	$("#eNameThickBox").find("tr.hs_SAS_AML").hide();
	                    }else{
	                    	$("#eNameThickBox").find("tr.hs_SAS_AML").show();
	                    	tb_height = 350;
	                    }
	                    
	                    btnArr["close"] = function(){
	                        $.thickbox.close();
	                    };
	                    //==============================
	                    if(amlflag=="1"){
	                    	get_tb_ename_elm().prop("disabled",false);
	                	}else if(amlflag=="2" ){
	                		get_tb_ename_elm().prop("disabled", true);
	                	}
	                    
	                    $('#eNameThickBox').thickbox({
	                        title: i18n.cls1131s01['C101S01E.isQdata7'] + i18n.cls1131s01['C101S01E.eName'],
	                        width: 550,
	                        height: tb_height,
	                        i18n : $.extend(i18n.def, 
	                        	{'tbSendAmlList':i18n.cls1131s01["C101S01E.btSendAmlList"], 
	                        	 'tbCheckAmlResult':i18n.cls1131s01["C101S01E.btCheckAmlResult"]
	                        	}),
	                        align: 'center',
	                        valign: 'bottom',
	                        buttons: btnArr
	                    });
	                    //==============================
	                    if(true){
	                    	if(amlflag=="1"){
	                    		//為讓 firefox 開啟 thickbox 能 disabled button => 使用 class ui-state-disabled
	                    		$("#tbSendAmlList.TB_buttons").removeClass(" ui-state-disabled ").prop("disabled",false);
//	                    		$("#tbCheckAmlResult.TB_buttons").addClass(" ui-state-disabled ").attr("disabled", "true");
	                    	}else if(amlflag=="2" ){
	                    		$("#tbSendAmlList.TB_buttons").addClass(" ui-state-disabled ").prop("disabled", true);
//	                    		$("#tbCheckAmlResult.TB_buttons").removeClass(" ui-state-disabled ").removeAttr("disabled");
	                    	}	
	                    }            
	                });
					$.thickbox.close();
				}
			}, {
				key: i18n.def.no,
				value: function(){
					$.ajax({ 
	                    handler: 'cls1131formhandler',
	                    type: "POST",
	                    dataType: "json",
	                    action: "clearAmlRelateDataForProd69",
	                    data: {
	                        custId: CLS1131S01.data.custId,
	                        dupNo: CLS1131S01.data.dupNo,
	                        mainId: CLS1131S01.data.mainId
	                    },
	                    success: function(){
	                    	//清除名單掃描結果並將結果設為N.A.
	                    	$( "#C101S01EForm" ).find ("[name='isQdata7'][value='3']:radio").prop("checked" , true );   //塞值 
	                    	$( "#C101S01EForm" ).find("#l120s09b_uniqueKey").val('');
	                    	$( "#C101S01EForm" ).find("#l120s09b_queryDateS").val('');
	                    	$( "#C101S01EForm" ).find("#l120s09b_ncResult").val('');
	                    	//$("a.query[queryType=isQdata7]").attr("data-activeSAS", "N");
	                    	$("#tbSendAmlList.TB_buttons").removeClass(" ui-state-disabled ").prop("disabled",false);
	                    	MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["runSuccess"]);
	                    }
	                });
					$.thickbox.close();
				}
			}, {
				key: i18n.def.cancel,
				value: function(){
					$.thickbox.close();
				}
			}])
		});
        
        
        
        
         
        
    },
    /**
     * 更改借款人姓名
     */
    openChgName: function(){
        $('#chgCustName').val( $('#C101M01AForm').find('#custName').val() );
        
        $('#chgNameThickBox').thickbox({
            title: i18n.cls1131s01['C101M01A.chgName'],
            width: 350,
            height: 150,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
					var newName = $('#chgCustName').val();
                    $('#C101M01AForm').find('#custName').val( newName );
					
    $.ajax({ 
        handler: 'cls1131formhandler',
        type: "POST",
        dataType: "json",
        action: "chgCustName",
        data: {
            custId: CLS1131S01.data.custId,
            dupNo: CLS1131S01.data.dupNo,
            custName : newName,
            mainId: CLS1131S01.data.mainId
        },
        success: function(){
			CLS1131S01.reloadGrid();
			var mainCustId = $('#mainCustId').val();
			var mainDupNo = $('#mainDupNo').val();
			
			if (mainCustId == CLS1131S01.data.custId && mainDupNo ==CLS1131S01.data.dupNo ){
				$('#showCustId').val( newName );
			}			
            $.thickbox.close();
        }
    });
	
					
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 引進資料
     */
    importData: function(){
        $.ajax({
            handler: CLS1131S01.handler,
            action: 'importData',
            formId: CLS1131S01.forms,
            data: {},
            success: function(response){
                $('#C101S01FForm').setValue(response.C101S01FForm);
                CLS1131S01.calculate(); // 重新計算分數
            }
        });
    },
    /**
     * 計算
     */
    calculate: function(){
        var result = 0;
        $('#C101S01FForm').find(".number").each(function(){
            try {
                result += parseInt($(this).val() || '0');
            } 
            catch (e) {
            }
        });
        $('#C101S01FForm').find('#ttlScore').html(result);
    },
    /**
     * 列印
     */
    print: function(queryType){
        $.form.submit({
            url: webroot + '/app/cls/cls1131p01',
            target: queryType,
            data: $.extend({
                queryType: queryType,
                isC120M01A: CLS1131S01.isC120M01A
            }, CLS1131S01.data)
        });
    },
    print_EJ_ST: function(txId){
    	if(txId=="Z13" || txId=="Z21"
    		|| txId=="B36" || txId=="D10" || txId=="R20"){
            $.form.submit({
                url: webroot + '/app/cls/cls1131p02',
                target: CLS1131S01.data.mainId+"_"+txId,
                data: $.extend({
                    'txId': txId,
                    isC120M01A: CLS1131S01.isC120M01A
                }, CLS1131S01.data)
            });
    	}else{
    		API.showMessage("無法開啟["+txId+"]");
    	}
    },
    /**
     * 重新整理grid
     */
    reloadGrid: function(){
        $.thickbox.close();
        try {
            pageAction.reloadGrid();
        } 
        catch (e) {
            CommonAPI.triggerOpener("gridview", "reloadGrid");
        }
    },
    /**
     * 顯示明細
     */
    showDetial: function(){
        if (CLS1131S01.data.naturalFlag == 'N') {
            //公司戶
            MegaApi.showMessage('非自然人');
        }
        else {
            //個人戶-可選擇房貸、非房貸評等
            if ($('#C101M01AForm').find('#grade1_markModel_1').html() ||
            $('#C101M01AForm').find('#grade1_markModel_2').html() ||
            $('#C101M01AForm').find('#grade1_markModel_3').html()) {
            	//print
                $.form.submit({
                    url: webroot + '/app/simple/FileProcessingService',
                    target: "_blank",
                    data: $.extend(CLS1131S01.data, {
                        isC120M01A: CLS1131S01.isC120M01A,
                        fileDownloadName: 'cls1131r01.pdf',
                        serviceName: 'cls1131r01rptservice'
                    })
                });
            }
            else {
                MegaApi.showErrorMessage(i18n.def['confirmTitle'], '請先執行 <b>相關資料查詢</b>');
            }
        }
    },
    /**
     * 查詢是否評等已被引用
     */
    queryDelectCase: function(){
        var reslut = false;
        if (CLS1131S01.data) {
            $.ajax({
                handler: CLS1131S01.handler,
                async: false,
                action: 'queryDelectCase',
                //formId: 'empty',
                data: CLS1131S01.data,
                success: function(response){
                    reslut = response.havaGrade;
                }
            });
        }
        return reslut;
    },
    sendAmlList: function(param){
    	
    	var my_dfd = $.Deferred();
    	CLS1131S01.decide_cls1131_amlflag().done(function(json_decide){
    		var amlflag = json_decide.flag||'';
    		        	
        	if(amlflag=="1"){
        		if(param=="0"){
        			my_dfd.resolve();
        			//------------------
        			//套 eName 的畫面, 會走 param=="1" 的程式去掃
            		CLS1131S01.openEName();
            	}else if(param=="1"){
            		CommonAPI.confirmMessage("若命中疑似名單，需至 BTT 0015 輸入調查結果。"
            				+"請確認資料正確，再執行下一步。<br/>"
            				+"是否繼續執行？", function(b){
        	            if (b) {
        	            	var _eName = get_tb_ename_elm().val()||'';
        	            	var _birthday = $('#C101S01AForm').find("#birthday").val()||'';
        	            	var _sex = $('#C101S01AForm').find("[name=sex]:checked").val()||'';
        	            	var _ntCode = $('#C101M01AForm').find("#ntCode").val()||'';
        	            	
        	            	ilog.debug("@CLS1131S01Page.js【send_cls1131_AmlList】ajax:begin");
        	            	$.ajax({
                                handler: 'cls1131formhandler', async: false,
                                action: 'send_cls1131_AmlList',
                                formId: 'empty',
                                data: $.extend(CLS1131S01.data, 
                                	{'eName':_eName, 
                                	'birthday': _birthday,
                                	'sex': _sex, 
                                	'ntCode': _ntCode
                                	}
                                ),
                                success: function(json){
                                	$('#C101S01EForm').find('#eName').html(DOMPurify.sanitize(_eName));
                                	findODS_CMFWARNP('cmfWarnp');
                                	my_dfd.resolve();
                                }
                            });        	                
        	            }else{
        	            	my_dfd.reject();
        	            } 
        	        });
            	}        		
        	}else if(amlflag=="2"){
        		my_dfd.reject();
                API.showMessage("已送出查詢，請先取得黑名單查詢結果");
        	}
    	});
    	    
        return my_dfd.promise();
    },
    checkAmlResult: function(){
    	var my_dfd = $.Deferred(); 
    	
    	ilog.debug("@CLS1131S01Page.js【checkAmlResult】begin");
    	
    	CLS1131S01.queryData('isQdata7');
		my_dfd.resolve();
        
        return my_dfd.promise();
    },
    proc_EJ_ST_query: function(txId, do_query){
    	if(do_query=="Y"){
    		return $.ajax({
    			type : "POST", handler : "cls1131formhandler",
				//formId: 'empty',
    			data : {
    				'formAction' : "keep_EJ_ST_queryOutput" , async: false,//用「同步」的方式				
    				'mainId' : CLS1131S01.data.mainId,
    				'custId' : CLS1131S01.data.custId, 
    				'dupNo' : CLS1131S01.data.dupNo, 
    				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") ,
    				'txId' : txId 
    			},
    			success:function(json_rtn){
    				ilog.debug("cls1131s01_proc_EJ_ST_query["+txId+"] done");
    				if(CLS1131S01.data.isPopupResultWindow == 'Y' && json_rtn.isDone=="Y"){
            			CLS1131S01.print_EJ_ST(json_rtn.txId);	
            		}			
    			}
    		});	
    	}else{
    		var my_dfd = $.Deferred();
    		my_dfd.resolve();	
    		return my_dfd.promise();
    	}
    },    
    runOneBtnQuery_Prod69: function(prodId, purpose_for_PACK){
    	var sysType_EJ = "sysType_EJ";
    	var sysType_ETCH = "sysType_ETCH";
//    	var do_query_Z21 = $('#C101S01EForm').find("[name=ej_ST_Query_Z21]:checked").val()||"";
//    	var do_query_Z13 = $('#C101S01EForm').find("[name=ej_ST_Query_Z13]:checked").val()||"";
//    	if(true){
//	    	if(do_query_Z21==""){
//				API.showMessage("請勾選是否加查 Z21");
//				return;
//			}
//			if(do_query_Z13==""){
//				API.showMessage("請勾選是否加查 Z13");
//				return;
//			}
//    	}
    	//=========================================
    	CLS1131S01.checkRecentEjQuery_Prod69().done(function(){
	    	CLS1131S01.preActionOneBtnQuery().done(function(json_preActionOneBtnQuery){
	        	if(true){
	        		$('#C101S01EForm').find('button.btnSendOneButtonQuery').addClass(" ui-state-disabled ").prop("disabled", true);
	        	}
	        	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	        	CLS1131S01.proc_EJ_ST_query("ID_CARD_CHECK", "Y").done(function(){	
//	        		CLS1131S01.proc_EJ_ST_query("FA_QUERY", "Y").done(function(){
	        			CLS1131S01.proc_EJ_ST_query("D10", "Y").done(function(){	
	        				CLS1131S01.proc_EJ_ST_query("R20", "Y").done(function(){	
	        					CLS1131S01.proc_EJ_ST_query("B36", "Y").done(function(){	
	        					
	        					/*if(true){ //etch
	    	        				CLS1131S01.get_callAPI_URL(sysType_ETCH, "", "", "").done(function(json_etch){
	    	        					if(json_etch.resp_url){
											
											if (CLS1131S01.data.isPopupResultWindow == 'Y') {
												$.form.submit({ url: json_etch.resp_url, data:{}, target:CLS1131S01.data.mainId+"_"+prodId+'json_etch'});
											}
											else{
												//此變數命名, 由 var queryResult → var queryResult_etch , 避免和 window.open(json_ej_resp_url) 的變數相同
												var queryResult_etch = window.open(json_etch.resp_url, "etch", '_blank', 'toolbar=0,location=0,menubar=0');
												setTimeout(function(){
													if(queryResult_etch){
														queryResult_etch.close();	
													}else{
														ilog.debug("[window_69]queryResult_etch is undefined");
													}													
												}, CLS1131S01.defined_etch_timeout);
											}
	    	        	    			}		
	    	        	    		});		
	    	        			}*/
	        					
	        					if(true){ //ejcic
	    	        				CLS1131S01.get_callAPI_URL(sysType_EJ, prodId, "", purpose_for_PACK).done(function(json_ej_pack){
										if(json_ej_pack.resp_url){
											
											if (CLS1131S01.data.isPopupResultWindow == 'Y') {
												$.form.submit({ url: json_ej_pack.resp_url, data:{}, target:CLS1131S01.data.mainId+"_"+prodId+"json_ej_"+prodId});
											}
	    	        	    				else{
												var queryResult_ejcic = window.open(json_ej_pack.resp_url, "ejcic", '_blank', 'toolbar=0,location=0,menubar=0');												
												if(queryResult_ejcic){
													queryResult_ejcic.close();	
												}else{
													ilog.debug("[window_69]queryResult_ejcic is undefined");
												}												
											}
	    	        	    			}    			
	    	        	    		});	
	    	        			}	
	    	        			if(true){ //RPS
	    	        				build_C101S01S_RPS(CLS1131S01.data.mainId).done(function(){
	    	        					//build_C101S01S_EJCIC_T70(CLS1131S01.data.mainId).done(function(){
	    	        					//});
	    	                    	});
	    	        			}	
	    	        			
	    	        			if(true){ 
	    	        				$.ajax({type : "POST", handler : "cls1131formhandler",
	    	            				data : {formAction : "sync_C122M01A_when_cls1131" ,mainId : CLS1131S01.data.mainId},
	    	            				success:function(obj){}
	    	            			});
	    	        			}
	    	        			
	        					});
	        				});
	        			});	        				
//	    			});
				}).fail(function(){    
					//當必填欄位沒有填，可讓 btn 由 disabled 恢復
					$('#C101S01EForm').find('button.btnSendOneButtonQuery').removeClass("ui-state-disabled").prop("disabled",false);
				}); 
	    	});	    		
    	});
    },
    runOneBtnQuery: function(prodId, purpose_for_PACK){
    	var sysType_EJ = "sysType_EJ";
    	var sysType_ETCH = "sysType_ETCH";
    	/*
    	 (1)因P7、P9較花時間，把組合查詢放在最後。
    	 (2)在送出 url 查詢之後，可能會出現﹝接收逾期 or 無此權限﹞的回應
    	
    	 window.open(json_ej_pack.resp_url, '_blank', 'scrollbars=no,location=no,menubar=no');
    	 */
//    	var do_query_Z21 = $('#C101S01EForm').find("[name=ej_ST_Query_Z21]:checked").val()||"";
//    	var do_query_Z13 = $('#C101S01EForm').find("[name=ej_ST_Query_Z13]:checked").val()||""
//    	if(true){    		
//    		if(prodId=="P9"){
//    			do_query_Z13 = "N"; //P9 已內含 Z13	
//    		}
    		ilog.debug("runOneBtnQuery[mainId="+CLS1131S01.data.mainId+"][custId="+CLS1131S01.data.custId+"]");
//    		if(do_query_Z21==""){
//    			API.showMessage("請勾選是否加查 Z21");
//    			return;
//    		}
//    		if(do_query_Z13==""){
//    			API.showMessage("請勾選是否加查 Z13");
//    			return;
//    		}
//    	}
    	//=========================================
    		CLS1131S01.checkRecentEjQuery(prodId).done(function(InquireType){
	    	CLS1131S01.preActionOneBtnQuery().done(function(json_preActionOneBtnQuery){
	        	if(prodId=="P7"){
	        		$('#C101S01EForm').find('button.btnSendOneButtonQuery').addClass(" ui-state-disabled ").prop("disabled", true);
	        	}else if(prodId=="P9"){
	        		$('#C101S01EForm').find('button.btnSendOneButtonQuery').addClass(" ui-state-disabled ").prop("disabled", true);
	        	}
	        	ilog.debug("[window_param][CLS1131S01.data.isPopupResultWindow="+(CLS1131S01.data.isPopupResultWindow||'')+"][CLS1131S01.defined_etch_timeout="+(CLS1131S01.defined_etch_timeout||'')+"]");
	        	
				//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

				CLS1131S01.proc_EJ_ST_query("ID_CARD_CHECK", "Y").done(function(){
					CLS1131S01.proc_EJ_ST_query("FA_QUERY", "Y").done(function(){
					    CLS1131S01.proc_EJ_ST_query("WiseNews_QUERY", "Y").done(function(){
                            if(true){ //etch
                                CLS1131S01.get_callAPI_URL(sysType_ETCH, "", "", "").done(function(json_etch){
                                    if(json_etch.resp_url){
                                        if(CLS1131S01.data.isPopupResultWindow == 'Y'){
                                            $.form.submit({ url: json_etch.resp_url, data:{}, target:CLS1131S01.data.mainId+"_"+prodId+'json_etch'});
                                        }
                                        else{
                                            //此變數命名, 由 var queryResult → var queryResult_etch , 避免和 window.open(json_ej_resp_url) 的變數相同
                                            var queryResult_etch = window.open(json_etch.resp_url, "etch", '_blank', 'toolbar=0,location=0,menubar=0');
                                            setTimeout(function(){
                                                if(queryResult_etch){
                                                    queryResult_etch.close();
                                                }else{
                                                    ilog.debug("[window_1]queryResult_etch is undefined");
                                                }
                                            }, CLS1131S01.defined_etch_timeout);
                                        }
                                    }
                                });
                            }
                            if(true){ //ejcic
                                if(InquireType != '2'){ //2 = 不重查聯徵
                                    CLS1131S01.get_callAPI_URL(sysType_EJ, prodId, "", purpose_for_PACK).done(function(json_ej_pack){
                                        if(json_ej_pack.resp_url){

                                            if (CLS1131S01.data.isPopupResultWindow == 'Y') {
                                                $.form.submit({ url: json_ej_pack.resp_url, data:{}, target:CLS1131S01.data.mainId+"_"+prodId+"json_ej_"+prodId});
                                            }
                                            else{
                                                var queryResult_ejcic = window.open(json_ej_pack.resp_url, "ejcic", '_blank', 'toolbar=0,location=0,menubar=0');
                                                setTimeout(function(){
                                                    if(queryResult_ejcic){
                                                        queryResult_ejcic.close();
                                                    }else{
                                                        ilog.debug("[window_1]queryResult_ejcic is undefined");
                                                    }
                                                }, CLS1131S01.defined_ejcic_timeout);

                                            }
                                        }
                                    });
                                }
                            }
                            if(true){ //RPS
                                build_C101S01S_RPS(CLS1131S01.data.mainId).done(function(){
                                    //build_C101S01S_EJCIC_T70(CLS1131S01.data.mainId).done(function(){
                                    //});
                                });
                            }
	        			});
	    			});
				}).fail(function(){    
					//當必填欄位沒有填，可讓 btn 由 disabled 恢復
					$('#C101S01EForm').find('button.btnSendOneButtonQuery').removeClass("ui-state-disabled").prop("disabled",false);
				}); 
	    	});	    		
    	});
    },
    sync_ej_data_to_C101S01E: function(param){
    	$.ajax({
			type : "POST", handler : "cls1131formhandler", 
			//formId: 'empty', 
			data : $.extend(param||{}, 
				{
				'formAction' : "sync_ej_data_to_C101S01E" , 
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
				}
			),
			success:function(json){
				if(json.sync_result=="Y"){
					InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
				} 
			}
		});    	
    },
    getOneBtnQuery: function(param){
    	$.ajax({
			type : "POST", handler : "cls1131formhandler", 
			//formId: 'empty', 
			data : $.extend(param||{}, 
				{
				'formAction' : "getOneBtnQuery" , 
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
				}
			),
			success:function(json){
				$('#C101S01EForm').find('button.btnSendOneButtonQuery').removeClass("ui-state-disabled").prop("disabled",false);
//		    	setTimeout(function(){ 
		    		InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
//		    	}, 1000);
		    	API.showMessage(i18n.def.runSuccess);
			}
		});    	
    },
	recordEjcicResultData: function(param){
    	$.ajax({
			type : "POST", handler : "cls1131formhandler", 
			data : $.extend(param||{}, 
				{
				'formAction' : "recordEjcicResultData" , 
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
				}
			),
			success:function(json){
			}
		});    	
    },
    /**
     * 一鍵列印PDF
     */
    getOneBtnPrintPDF: function(queryType){
    	var data = $('#C101S01EForm').find("#gridview1").getRowData();
    	var isAllJSON = true;
    	// 判斷所有RPS檔案是否為JSON格式
		$(data).each(function() {
			if ($(this)[0].dataSrcMemo.match("C101S01S|C120S01S") && "J" != $(this)[0].reportFileType) {
				isAllJSON = false;
			}
		});
		if(isAllJSON) {
			// JSON格式，訊息提示點選一鍵列印HTML
			API.showMessage("無PDF資料，請點選一鍵列印HTML");
		} else {
			// 非JSON格式，用PDF呈現
			$.form.submit({
				url: webroot + '/app/simple/FileProcessingService',
				target: "_blank",
				data: $.extend(CLS1131S01.data, {
					isC120M01A: CLS1131S01.isC120M01A,
					mainId : CLS1131S01.data.mainId,
					custId : CLS1131S01.data.custId, 
					dupNo : CLS1131S01.data.dupNo,
					fileDownloadName: 'cls1131r07.pdf',
					serviceName: 'cls1131r07rptservice'
				})
			});
		}
    },
    /**
     * 一鍵列印HTML
     */
    getOneBtnPrintHTML: function(queryType){
        $.form.submit({
            url: webroot + '/app/cls/cls1131p03',
            target: "_blank",
            data: $.extend({
                queryType: queryType,
                isC120M01A: CLS1131S01.isC120M01A
            }, CLS1131S01.data)
        });
    },
    checkRecentEjQuery_Prod69: function(){ //若近期已查過Ejcic 出提示，避免用「新業務」查太多次
    	var my_dfd = $.Deferred();
    	$.ajax({
			type : "POST", handler : "cls1131formhandler", 
			//formId: 'empty',
			data : {
				'formAction' : "checkRecentEjQuery_Prod69" , 		
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo 
			},
			success:function(obj){				
				if(obj.msg){
					MegaApi.confirmMessage(obj.msg, function(r){
                        if (r) {
                        	my_dfd.resolve();
                        }else {
                        	my_dfd.reject();
                        }
                    });					
				}else{
					my_dfd.resolve();
				}				
			}
		});
    	return my_dfd.promise();
    },
    checkRecentEjQuery: function(prodId){ //若近期已查過Ejcic 出提示，避免用「新業務」查太多次
    	var my_dfd = $.Deferred();
    	$.ajax({
			type : "POST", handler : "cls1131formhandler", 
			//formId: 'empty',
			data : {
				'formAction' : "checkRecentEjQuery" , 		
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'prodId' : prodId
			},
			success:function(obj){	
				if(obj.msg){
					if(obj.newMessageType){ //有三個選項的訊息框
						CommonAPI.iConfirmDialog({
							message: obj.msg, //回傳之message 30日內已查詢過P7、J10，是否P7及J10仍要以﹝查詢理由：新業務申請﹞重新發送聯徵查詢？
							buttons: API.createJSON([{
								key: i18n.cls1131s01["ejcic.lack.Inquire"],
								value: function(){
									$.thickbox.close();
									my_dfd.resolve("2");
								}
							}, {
								key: i18n.cls1131s01["ejcic.all.reInquire"],
								value: function(){
									$.thickbox.close();
									my_dfd.resolve("1");
								}
							}, {
								key: i18n.def.cancel,
								value: function(){
									$.thickbox.close();
									my_dfd.reject();
								}
							}])
						});
					}else{		
						CommonAPI.iConfirmDialog({
							message: obj.msg, //回傳之message 30日內已查詢過P7、J10，是否P7及J10仍要以﹝查詢理由：新業務申請﹞重新發送聯徵查詢？
							buttons: API.createJSON([{
								key: i18n.cls1131s01["ejcic.all.reInquire"],
								value: function(){
									$.thickbox.close();
									my_dfd.resolve("1");
								}
							}, {
								key: i18n.def.cancel,
								value: function(){
									$.thickbox.close();
									my_dfd.reject();
								}
							}])
						});
					}
				}else{
					my_dfd.reject();
				}				
			}
		});
    	return my_dfd.promise();
    },
    preActionOneBtnQuery: function(){
    	return $.ajax({
			type : "POST", handler : "cls1131formhandler", 
			//formId: 'empty',
			data : $.extend($("#C101S01AForm").serializeData()
					, {
						'formAction' : "preActionOneBtnQuery" , 		
						'mainId' : CLS1131S01.data.mainId,
						'custId' : CLS1131S01.data.custId, 
						'dupNo' : CLS1131S01.data.dupNo, 
						'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
					}
			),
			success:function(obj){
				
			}
		});
    },
    get_callAPI_URL: function(sysType, prodId, txId, purpose_for_PACK){
    	/* test
    	  	CLS1131S01.get_callAPI_URL("sysType_EJ", "P7", "", "2").done(function(json_ej_pack){
    			if(json_ej_pack.resp_url){
	    				$.form.submit({ url: json_ej_pack.resp_url, data:{}, target:'json_ej_pack'});
	    		}
    		});
    	*/
    	return $.ajax({
			type : "POST", handler : "cls1131formhandler", 
			//formId: 'empty', 
			data : {
				'formAction' : "get_callAPI_URL" , async: false,//用「同步」的方式
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") ,
				'sysType' : sysType,
				'prodId' : prodId , 
				'txId' : txId ,  
				'purpose_for_PACK' : purpose_for_PACK
			},
			success:function(obj){
				
			}
		});
    },
    /**
     * 提示 markModel 是否為免辦
     */
    checkMarkModel: function(){
        var my_dfd = $.Deferred();
        
        var $C101M01AForm = $('#C101M01AForm');
        //---
        if ($C101M01AForm.find("[name=markModel]").is(":visible")) {
            if ($C101M01AForm.find("[name=markModel][value=0]").is(':checked')) {
                //適用之模型：免辦評等  
                MegaApi.confirmMessage("適用之模型：免辦評等。是否繼續儲存？", function(action){
                    if (action) {
                        my_dfd.resolve();
                    }
                    else {
                        my_dfd.reject();
                    }
                });
            }
            else 
                if ($C101M01AForm.find("[name=markModel][value=1]").is(':checked') ||
                $C101M01AForm.find("[name=markModel][value=2]").is(':checked') ||
                $C101M01AForm.find("[name=markModel][value=3]").is(':checked')) {
                    my_dfd.resolve();
                }
                else {
                    //未勾選適用之模型 
                    MegaApi.confirmMessage("未勾選適用之模型 。是否繼續儲存？", function(action){
                        if (action) {
                            my_dfd.resolve();
                        }
                        else {
                            my_dfd.reject();
                        }
                    });
                }
        }
        else {
            //企金戶, 海外ID
            my_dfd.resolve();
        }
        
        return my_dfd.promise();
    },
	checkIsQueryEjcicS11: function(){
		return $.ajax({
			type : "POST", handler : "cls1131formhandler", 
			//formId: 'empty', 
			data : {
				formAction : "checkIsQueryEjcicS11", 
				async: false, //用「同步」的方式
				mainId : CLS1131S01.data.mainId,
				custId : CLS1131S01.data.custId, 
				dupNo : CLS1131S01.data.dupNo
			},
			success:function(obj){
				
			}
		});
	},
	/**
    *產品的相關選單
    */
    getProdSelect: function(){
        ilog.debug("@ajax > getProdSelect");
        var $prodKind = $('#C101S02CForm').find("#prodKind");
        $.ajax({
            handler: "cls1151m01formhandler",
            action: "getProdSelect",
            async: false,
            success: function(objs){
                var temp = "<option value=''>" + i18n.def.comboSpace + "</option>";
                for (var i in objs["obj"]) {
                    var value = objs["obj"][i];
                    var disabled = value["isCanCel"] ? "disabled=disabled isCanCel='Y'" : "";
                    temp += "<option value='" + DOMPurify.sanitize(value["key"]) + "' subjectData='" + DOMPurify.sanitize(JSON.stringify(value["subjectData"])) + "' " + disabled + " >" + DOMPurify.sanitize(value["key"]) + "-" + DOMPurify.sanitize(value["name"]) + "</option>";
                }

                $prodKind.html(temp);
            }
        });
    }
};

/**
 * 地址
 */
var AddrAction = {
    formId: '',
    signify: '',
    open: function(options){
        AddrAction.formId = options.formId;
        AddrAction.signify = options.signify;
        
        var $form = $('#' + AddrAction.formId);
        var cityCode = $form.find('#' + AddrAction.signify + 'City').val();
        var combos = CommonAPI.loadCombos(['counties', 'counties' + cityCode]);
        
        var $addrForm = $('#AddrForm');
        $addrForm.setValue(); // $addrForm reset
        // 縣市
        $addrForm.find('#AddrCity').setItems({
            item: combos['counties'],
            format: '{key}',
            value: cityCode,
            fn: function(){
                var $addrForm = $('#AddrForm');
                var cityCode = $addrForm.find('#AddrCity').val();
                var combos = CommonAPI.loadCombos('counties' + cityCode);
                $addrForm.find('#AddrZip').setItems({
                    item: combos['counties' + cityCode],
                    format: '{key}'
                });
            }
        });
        // 鄉鎮市區
        $addrForm.find('#AddrZip').setItems({
            item: combos['counties' + cityCode],
            format: '{key}',
            value: $form.find('#' + AddrAction.signify + 'Zip').val()
        });
        // 地址
        $addrForm.find('#AddrAddr').val($form.find('#' + AddrAction.signify + 'Addr').val() || '');
        
        $('#AddrThickBox').thickbox({
            title: '', // i18n.cms1400v01["title"],
            width: 500,
            height: 200,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var $addrForm = $('#AddrForm');
                    if ($addrForm.valid()) {
                        var $form = $('#' + AddrAction.formId);
                        $form.find('#' + AddrAction.signify + 'City').val($addrForm.find('#AddrCity').val());
                        $form.find('#' + AddrAction.signify + 'Zip').val($addrForm.find('#AddrZip').val());
                        $form.find('#' + AddrAction.signify + 'Addr').val($addrForm.find('#AddrAddr').val());
                        var addr = DOMPurify.sanitize($addrForm.find('#AddrCity :selected').text()) +
                                   DOMPurify.sanitize($addrForm.find('#AddrZip :selected').text()) +
                                   DOMPurify.sanitize($addrForm.find('#AddrAddr').val());
                        $form.find('#' + AddrAction.signify + 'Target').html(addr);
                        $.thickbox.close();
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    }
};

/**
 * 借款人資料
 */
var CustAction = {
    ready: false,
    data: {},
    addrs: {},
    init: function(){
        for (var key in CustAction.data) {
            delete CustAction.data[key];
        }
        for (var key in CustAction.addrs) {
            delete CustAction.addrs[key];
        }
    },
    build: function(){
        $('#CustForm').buildItem();
        //職業別
        $('#CustForm').find('#_jobType1').change(function(){
            var $form = $('#CustForm');
            var code = $form.find('#_jobType1').val();
            if (code) {
                var item = CommonAPI.loadCombos('jobType' + code);
                $form.find('#_jobType2').setItems({
                    item: item['jobType' + code],
                    format: '{key}'
                });
            }
        });
        return true;
    },
    load: function(){
        var $form = $('#C101S01AForm');
        $.ajax({
            handler: CLS1131S01.handler,
            action: 'importCust',
            formId: 'C101M01AForm', //
            data: CLS1131S01.data,
            success: function(response){
                CustAction.open(response.CustForm);
            }
        });
    },
    open: function(data){
        if (!CustAction.ready) {
            CustAction.ready = CustAction.build();
        }
        CustAction.init();
        var $form = $('#CustForm');
        $form.setValue(data, true, '_');
        
        // 申貸戶通訊地址 addrs
        $form.find('#_coAddr').setItems({
            space: false,
            item: CustAction.parseAddr(data.addrs),
            format: '{key}'
        });
        // 服務單位電話 tels
        $form.find('#_comTel').setItems({
            space: false,
            item: CustAction.parseTel(data.tels),
            format: '{key}'
        });
        // 申貸戶行動電話 phones
        $form.find('#_mTel').setItems({
            space: false,
            item: CustAction.parsePhone(data.phones),
            format: '{key}'
        });
        // 申貸戶電子郵件地址 mails
        $form.find('#_email').setItems({
            space: false,
            item: CustAction.parseMail(data.mails),
            format: '{key}'
        });
        
        $('#CustThickBox').thickbox({
            title: i18n.cls1131s01['title.custInfo'] || '',
            width: 700,
            height: 430,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    //if (true){
                    if ($('#C101M01AForm').find('#custName').val() != $('#CustForm').find('#_custName').html()) {
                        var msg = '<b>引進姓名</b>與<b>原始姓名</b>不合，引進後需重新執行<b>相關資料查詢</b>。' +
                        '<br/>確定引進資料嗎?';
                        MegaApi.confirmMessage(msg, function(action){
                            if (action) 
                                CustAction.sure();
                        });
                    }
                    else {
                        CustAction.sure();
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 確定引進資料
     */
    sure: function(){
        var data = $('#CustForm').serializeData();
        // 通訊地址
        var json = CustAction.addrs[data['_coAddr']] || {};
        data['_coCity'] = json.cityCode || '';
        data['_coZip'] = json.zipCode || '';
        data['_coAddr'] = json.addr || '';
        data['_coTarget'] = json.target || '';
        
        for (var index in CLS1131S01.forms) {
            var formName = CLS1131S01.forms[index];
            $('#' + formName).setValue(data, false, null, '_');
        }
        // set cust info
        CLS1131S01.setCustInfo();
        
        //J-113-0341 個金徵信作業新增「年輕族群客戶加強關懷提問單」
        youngCareListShowHide();
        
        $.thickbox.close();
    },
    /**
     * 解析申貸戶通訊地址 addrs
     */
    parseAddr: function(array){
        var result = {};
        if (array) {
            for (var index in array) {
                var json = array[index];
                json['addr'] = (json.LEER || '') + (json.ADDRR || '');
                json['target'] = (json.CITYR || '') + (json.TOWNR || '') + json['addr'];
                result[json.target] = json.target;
                
                CustAction.addrs[json.target] = json;
            }
        }
        return result;
    },
    /**
     * 解析服務單位電話 tels
     */
    parseTel: function(array){
        var result = {};
        if (array) {
            for (var index in array) {
                var json = array[index];
                var value = '';
                if (json.AREANO) 
                    value = '(' + json.AREANO + ')';
                if (json.TELNO) 
                    value += json.TELNO;
                if (json.EXTNO) 
                    value += (json.TELNO ? '-' : '') + json.EXTNO;
                result[value] = value;
            }
        }
        return result;
    },
    /**
     * 解析申貸戶行動電話 phones
     */
    parsePhone: function(array){
        var result = {};
        if (array) {
            for (var index in array) {
                var json = array[index];
                var value = json.MPNO || '';
                result[value] = value;
            }
        }
        return result;
    },
    /**
     * 解析申貸戶電子郵件地址 mails
     */
    parseMail: function(array){
        var result = {};
        if (array) {
            for (var index in array) {
                var json = array[index];
                var value = json.MAILADDR || '';
                result[value] = value;
            }
        }
        return result;
    },
    /**
     * 引進配偶
     */
    importMate: function(data){
        $.ajax({
            handler: CLS1131S01.handler,
            action: 'importMate',
            formId: 'C101S01DForm', //
            data: data,
            success: function(response){
                //clear
            	clear_DForm_input();
                
                $('#C101S01DForm').setValue(response.C101S01DForm, false);
            }
        });
    },
    importWebBankApply: function(){
    	isChgC122M01A_applyStatus().done(function(resp){
			if(resp.c122_applyStatus && resp.c122_applyStatus!=''){
				var msg = "";
				if(resp.c122_applyStatus=="0A0" && resp.c122_docStatus==""){//受理中
					 msg = "是否將線上申貸的內容，帶入至消金徵信；並將狀態由 受理中 → 審核中？"
				}else{
					msg = "是否將線上申貸的內容，帶入至消金徵信？"
				}
				MegaApi.confirmMessage(msg, function(r2){
					if (r2) {
						fetchC122M01A_to_C101();			
					}
				});
			}    				
		});
    },
    importWebProd69: function(){
    	MegaApi.confirmMessage("是否將線上勞工紓困資料的內容，帶入至消金徵信？", function(r2){
		if (r2) {
			fetchC122M01A_to_C101_Prod69();			
		}
    	});
    },
    importWebPLOAN: function(){
    	MegaApi.confirmMessage("是否將線上貸款資料的內容，帶入至消金徵信？", function(r2){
		if (r2) {
			fetchC122M01A_to_C101_PLOAN();			
		}
    	});
    }
};

function isChgC122M01A_applyStatus(){
	return $.ajax({
        handler: CLS1131S01.handler,
        action: 'isChgC122M01A_applyStatus',
        data: {
            'c122m01a_mainId': CLS1131S01.c122m01a_mainId
        },
        success: function(response){        	
        }
    }); 
}

function fetchC122M01A_to_C101(){
	return $.ajax({
        handler: CLS1131S01.handler,
        action: 'fetchC122M01A_to_C101',
        data: {
            'c122m01a_mainId': CLS1131S01.c122m01a_mainId,
            'mainId':CLS1131S01.data.mainId
        },
        success: function(response){       
        	for (var index in CLS1131S01.forms) {
                var formName = CLS1131S01.forms[index];
                if(formName=="C101S01AForm"){
                	$('#C101S01AForm').setValue(response.C101S01AForm, false);
                }else if(formName=="C101S01BForm"){
                	$('#C101S01BForm').setValue(response.C101S01BForm, false);                	
                }else if(formName=="C101S01CForm"){	
                	$('#C101S01CForm').setValue(response.C101S01CForm, false);
                }else{
                	continue;
                }
            }   
        	CLS1131S01.data['triggerByFetchC122M01A'] = 'Y';
        	CLS1131S01.save(function(){ 
        		CLS1131S01.data['triggerByFetchC122M01A'] = 'N';        		
        	});
        }
    }); 
}

function fetchC122M01A_to_C101_Prod69(){
	return $.ajax({
        handler: CLS1131S01.handler,
        action: 'fetchC122M01A_to_C101_Prod69',
        data: {
            'c122m01a_mainId': CLS1131S01.c122m01a_mainId_applyKindB,
            'mainId':CLS1131S01.data.mainId
        },
        success: function(response){
        	ilog.debug("fetchC122M01A_to_C101_Prod69[c122m01a_mainId="+CLS1131S01.c122m01a_mainId_applyKindB+"][cls1131_mainId="+CLS1131S01.data.mainId+"]");
        	for (var index in CLS1131S01.forms) {
                var formName = CLS1131S01.forms[index];
                if(formName=="C101M01AForm"){
                	$('#C101M01AForm').injectData(response.C101M01AForm);
                }else if(formName=="C101S01AForm"){
                	//$('#C101S01AForm').setValue(response.C101S01AForm, false);
                	$('#C101S01AForm').injectData(response.C101S01AForm);
                }else if(formName=="C101S01BForm"){
                	/* 
                	原寫法  $('#C101S01BForm').setValue(response.C101S01BForm, false);
                	
                	但遇到 jsonResponse 有回傳 othAmt=0, 但 html UI 未更新(可能因為 othAmt 已變成 readonly) 
                	*/
                	$('#C101S01BForm').injectData(response.C101S01BForm);
                }else if(formName=="C101S01CForm"){
                	$('#C101S01CForm').injectData(response.C101S01CForm);
                }else if(formName=="C101S01EForm"){
                	$('#C101S01EForm').injectData(response.C101S01EForm);
                }else{
                	continue;
                }
            }   
        	
        	if(response.C101S01XForm){
        		$('#C101S01XForm').injectData(response.C101S01XForm);
        	}
        	
        	CLS1131S01.data['triggerByFetchC122M01A'] = 'Y';
        	CLS1131S01.save(function(){ 
        		CLS1131S01.data['triggerByFetchC122M01A'] = 'N';        		
        	});
        }
    }); 
}


function fetchC122M01A_to_C101_PLOAN(){
	return $.ajax({
        handler: CLS1131S01.handler,
        action: 'fetchC122M01A_to_C101_PLOAN',
        data: {
            'c122m01a_mainId': CLS1131S01.c122m01a_mainId_applyKind_PLOAN,
            'mainId':CLS1131S01.data.mainId
        },
        success: function(response){       
        	for (var index in CLS1131S01.forms) {
                var formName = CLS1131S01.forms[index];
                if(formName=="C101S01AForm"){
                	$('#C101S01AForm').setValue(response.C101S01AForm, false);
                }else if(formName=="C101S01BForm"){
                	$('#C101S01BForm').setValue(response.C101S01BForm, false);                	
                }else{
                	continue;
                }
            }   
        	CLS1131S01.data['triggerByFetchC122M01A'] = 'Y';
        	CLS1131S01.save(function(){ 
        		CLS1131S01.data['triggerByFetchC122M01A'] = 'N';        		
        	});
        }
    }); 
}
function showDetail(relType){

    var custId = CLS1131S01.data.custId;
    var dupNo = CLS1131S01.data.dupNo;
    
    // 進行查詢 
    $.ajax({ //查詢主要借款人資料
        handler: 'cls1131formhandler',
        type: "POST",
        dataType: "json",
        action: "queryL120s01o",
        data: {
            custId: custId,
            dupNo: dupNo,
            relType: relType,
            mainId: CLS1131S01.data.mainId
        },
        success: function(json){
            var $formL120s01o = $("#formL120s01o");
            $formL120s01o.find("#showDetailHtml").html(DOMPurify.sanitize(json.showDetailResult));
            alert(json.showDetailResult);
            
            $("#tL120s01o").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.cls1131s01["l120s01m.item26"], // 「授信信用風險管理」遵循檢核
                width: 965,
                height: 480,
                modal: true,
                i18n: i18n.def,
                buttons: {
                
                    "close": function(){
                        $.thickbox.close();
                    }
                }
            });
        }
    });
    
}

function clear_DForm_input(){
	var $div = $('#C101S01DForm').find('#mateDiv');
	$div.find('input:text,select').val('');
	$div.find('input:radio,input:checkbox').prop('checked', false);
	$div.find('span.comboSpace').html(i18n.def['comboSpace'] || '--請選擇--');
}
function hs_mateFlag(){
	var $form = $('#C101S01DForm');
    var val = $form.find('input[name=mateFlag]:checked').val();    
    if(val=='B'){
		$form.find('#mateDiv').show();
	}else{
		clear_DForm_input();
		$form.find('#mateDiv').hide();	
	}
}
function hs_rejectInfo(){
	if( $('#C101S01EForm').find('input[name=isQdata1][value=1]:checked').length>0 ){
		
		$('#C101M01AForm').find('#rejectInfo').show();
	}else{
		$('#C101M01AForm').find('#rejectInfo').hide();
	}
}
function markModelCheckAction() {
	var clsJobTypeField = document.getElementById("cls_job_type_fieldset");
	clsJobTypeField.style.display = "table-row";
	disableGray('jobType1');
	disableGray('jobType2');
	disableGray('jobTitle');

	//檢查是否為非營利。
	if(!$("#isNPO").is(":checked")){
		$("#taxIDStart").html("＊");
		$("#juId").prop("required", true);
			
	}else{
		$("#taxIDStart").html("");
		$("#juId").prop("required",false);
	}
	$("#capitalStart").html("＊");
	$("#juPaidUpCapital").prop("required", true);
	cleanClsJobValue();

	$("#clsJobType1").prop("required", true);
	$("#clsJobType2").prop("required", true);
	$("#clsJobTitle").prop("required", true);
}

function markModelUncheckAction(needClean) {
	var clsJobTypeField = document.getElementById("cls_job_type_fieldset");
	clsJobTypeField.style.display = "none";
	enableWhite("jobType1");
	enableWhite("jobType2");
	enableWhite("jobTitle");

	$("#taxIDStart").html("");
	$("#juId").prop("required",false);
	$("#capitalStart").html("");
	$("#juPaidUpCapital").prop("required",false);

	if(needClean){
		cleanClsJobValue();	
	}

	//新職業表非必填
	$("#clsJobType1").prop("required",false);
	$("#clsJobType2").prop("required",false);
	$("#clsJobTitle").prop("required",false);
}
function disableGray(elementName){
	$('#'+elementName).prop("disabled", true);
	$('#'+elementName).css("background-color", "#ccc");
}
function enableWhite(elementName){
	$('#'+elementName).prop("disabled",false);
	$('#'+elementName).css("background-color", "#FFF");
}
function removeDataErrorStyle(elementName){
	removeClass(elementName,"data-error");
	removeArr(elementName,"errormsg");
}

function removeClass(elementName,className){
	if($("#"+elementName).hasClass(className)){
		$("#"+elementName).removeClass(className);
	}
}
function removeArr(elementName,AttrName){
	if($("#"+elementName).attr(AttrName)){
		$("#"+elementName).removeAttr(AttrName);
	}
}
function checkIsNPO(){
	//非營利單位打勾，實收資本額給0反灰、統編變成非必填。
	$("#juPaidUpCapital").prop("required",false);
	//檢查是否有錯誤提示
	removeDataErrorStyle("juId");
	removeDataErrorStyle("juPaidUpCapital");
	$("#juPaidUpCapital").val("0");
	capital = "0";
	$("#juPaidUpCapital").trigger("change");
	
	disableGray("juPaidUpCapital");
	
	//統一編號變成非必填
	$("#juId").prop("required",false);
	$("#taxIDStart").html("");
}

function cleanClsJobValue(){
	$("#clsJobTitle").val('0101').trigger("change");
	$("#clsJobType1").val('').trigger("change");
	$("#clsJobType2").val('').trigger("change");
	$("#isNPO").checked = false;

	$("#isNPO").trigger("change");
	
	$("#jobType1").val('');
	$("#jobType2").val('');
	$("#jobTitle").val('');
}

function hs_abnormalInfo(){
	if( $('#C101S01EForm').find('input[name=isQdata29][value=1]:checked').length>0){
		//有異常通報{包含 已解除, 未解除}
		$('#C101M01AForm').injectData({'isAbnormal':'Y'});
		
		$('#C101M01AForm').find('.isAbnormal_Y').show();
		$('#C101M01AForm').find('#abnormalInfo').show();
	}else if( $('#C101S01EForm').find('input[name=isQdata29][value=2]:checked').length>0){
		//無異常通報
		$('#C101M01AForm').injectData({'isAbnormal':'N'});
		
		$('#C101M01AForm').find('.isAbnormal_Y').hide();
		$('#C101M01AForm').find('#abnormalInfo').show();
	}else{
		$('#C101M01AForm').find('#abnormalInfo').hide();
	}
}
function hs_jcicFlg(m01aForm){	
	if(m01aForm && m01aForm.naturalFlag && m01aForm.jcicFlg && m01aForm.naturalFlag == 'Y'){
		var s_jcicFlg = m01aForm.jcicFlg+"      ";
		var jcicFlg_first2chars = s_jcicFlg.substring(0, 2);
		/*
		 * 在頁面上，有[房貸、非房貸]共2個區塊
		 */
		if(jcicFlg_first2chars=="NN"){
			$("span.date_jcicFlg_V_NN").val(m01aForm.date_jcicFlg_V_NN||'');
			$("tr.hs_jcicFlg_VNN_").show();
		}
	}			
}


function alwaysConfirmAdjReason(cnt, obj){
	var my_dfd = $.Deferred();
	if(cnt=="0"){
		my_dfd.resolve();
	}else{	
		if(true){
			$("#adjustReasonAlwaysCfmMsg").html(obj.alwaysCfmStr);
		}
		$("#divAdjustReasonAlwaysCfmMsg").thickbox({
	        title: "", width: 550, height: 180,
	        align: "center", valign: "bottom", modal: false,
	        i18n: (obj || i18n.def),
	        buttons: {
	        	"alwaysCfmN": function(){
	                $.thickbox.close();
	                my_dfd.reject();
	            },
	            "alwaysCfmY": function(){
	            	//=============
	                $.thickbox.close();
	            	my_dfd.resolve();
	            }
	        }
	    });	
	}		
	return my_dfd.promise(); 
}
function procCfmMsg(obj){
	var my_dfd = $.Deferred();
	
	if((obj.cfmStr||"")==""){
		my_dfd.resolve();
	}else{		
		if(true){
			$("#adjustReasonCfmMsg").html(obj.cfmStr);
		}
		$("#divAdjustReasonCfmMsg").thickbox({
	        title: "", width: 600, height: 200,
            align: "center", valign: "bottom", modal: false,
            i18n: (obj || i18n.def),
            buttons: {
            	"cfmN": function(){
                    $.thickbox.close();
                    my_dfd.reject();
                },
                "cfmY": function(){
                	//=============
                    $.thickbox.close();
                	my_dfd.resolve();
                }
            }
	    });	
		
	}
	return my_dfd.promise(); 
}

function gridOneBtnQueryClickLink(cellvalue, options, rowObject){	
	var _oid = rowObject.oid;
	var _dataSrcMemo = rowObject.dataSrcMemo;
	var _dataType = rowObject.dataType;
	var _reportFileType = rowObject.reportFileType;
	
	ilog.debug("gridOneBtnQueryClickLink[_oid="+_oid+"][dataSrcMemo="+_dataSrcMemo+"][dataType="+_dataType+"][reportFileType="+_reportFileType+"]");
	if(_dataSrcMemo=="C101S01S" || _dataSrcMemo=="C120S01S" ){ //當來源是 RPS 相關的 data
		// JSON格式
		if(_reportFileType == "J"){
			$.form.submit({
	            url: webroot + '/app/cls/cls1131p03',
	            target: "_blank",
	            data: $.extend({
	            	dataType: _dataType,
	                isC120M01A: (CLS1131S01.isC120M01A?"Y":"N")
	            }, CLS1131S01.data)
	        });
		} else {
			$.form.submit({
				url: webroot + '/app/simple/FileProcessingService',
				target: "_blank",
				data: {
					oid: _oid,
					serviceName: _dataSrcMemo=="C101S01S" ? 'cls1131MixPdfservice' : 'cls1141MixPdfservice',
							fileDownloadName: "xxx.pdf"
				}
			});
		}
	}else if(_dataSrcMemo=="C101S01E" || _dataSrcMemo=="C120S01E" ){
		CLS1131S01.print_EJ_ST(_dataType);
	}else if(_dataSrcMemo=="C101S01H" || _dataSrcMemo=="C120S01H" ){
		CLS1131S01.print('ejcic');
	}else if(_dataSrcMemo=="C101S01I" || _dataSrcMemo=="C120S01I" ){
        CLS1131S01.print('etch');
	}else if(_dataSrcMemo=="C101S01U" || _dataSrcMemo=="C120S01U" ){
		CLS1131S01.print_EJ_ST(_dataType);
	}else if(_dataSrcMemo=="C101S04W" || _dataSrcMemo=="C120S04W"){
		
		if(rowObject.dataStatusCode != 'A02'){
			API.showMessage(rowObject.dataStatus);
			return;
		}
		
		$.form.submit({
				url: webroot + '/app/simple/FileProcessingPage',
				target: "_blank",
				data: {
					oid: rowObject.oid,
					mainId: rowObject.mainId,
					serviceName: 'cls1131RpaFileService',
					fileDownloadName: "xxx.jpg",
					dataSource: _dataSrcMemo
				}
			});
			
	}else if(_dataSrcMemo=="C101S02S" || _dataSrcMemo=="C120S02S"){
		
		if(rowObject.dataStatusCode != 'A3'){
			API.showMessage(rowObject.remark);
			return;
		}
		
		CLS1131S01.print('ejcic');
	}else if(_dataSrcMemo=="WISENEWS"){
        $.form.submit({
            url: webroot + '/app/simple/FileProcessingPage',
            target: "_blank",
            data: {
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                serviceName: 'cls1131r10rptservice',
                fileDownloadName: 'cls1131r10.pdf',
                dataSource: _dataSrcMemo
            }
        });
    }else{
		ilog.debug("oid["+rowObject.oid+"], unKnown dataSrcMemo["+rowObject.dataSrcMemo+"]");
		API.showMessage("無法開啟未定義的連結");
	}
}
//J-108-0277 介接系統資料查詢-資料建檔記錄查詢
var InterfaceSystemDataInquiry = {
	
	init: function (){
		$('#C101S01EForm').find('#queryDataArchivalRecord').click(function(){
			build_C101S01S_RPS(CLS1131S01.data.mainId).done(function(){
				InterfaceSystemDataInquiry.grid1.jqGrid("setGridParam", {
                    postData: {
                    	  'mainId' : CLS1131S01.data.mainId
                    	, 'custId': CLS1131S01.data.custId
            			, 'dupNo': CLS1131S01.data.dupNo
            			, 'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N")
                    },
                    search: true
                }).trigger("reloadGrid");
        	});
			
			$("#interfaceSystemDataInquiryFieldSet").show();
			$("#dataArchivalRecordData").show();
			
        });
		
		InterfaceSystemDataInquiry.isCloseFinHoldingDefaultDeliveryFunction();
	},
	downloadDataArchivalRecordFile: function (dataType, fileSeq){
		//formhandler 可能為 cls1131formhandler or cls1141formhandler
		$.form.submit({
		    url: webroot + '/app/simple/FileProcessingService',
		    target: "_blank",
		    data: {
				mainId:CLS1131S01.data.mainId,
				custId: CLS1131S01.data.custId,
				dupNo: CLS1131S01.data.dupNo,
				dataType: dataType,
				fileSeq: fileSeq,
		        serviceName: CLS1131S01.handler == 'cls1131formhandler' 
												? 'cls1131MixPdfservice' : 'cls1141MixPdfservice',
				fileDownloadName: "xxx.pdf"
		    }
		});
	},
//	loadInterfaceSystemData: function(response){
//		
//		$("#interfaceSystemDataInquiryFieldSet").show();
//		$("#dataArchivalRecordData").hide();
//
//		//資料建檔記錄查詢
//		var dataArchivalArray = response.dataArchivalRecordData;
//
//		if(dataArchivalArray.length > 0){
//			InterfaceSystemDataInquiry.grid1.jqGrid('clearGridData', true);
//	    	for(var i=0;i<=dataArchivalArray.length;i++){
//	    		InterfaceSystemDataInquiry.grid1.jqGrid('addRowData', i+1, dataArchivalArray[i]);
//	    	}
//			
//			$("#dataArchivalRecordData").show();
//			$("#interfaceSystemDataInquiryFieldSet").show();
//		}
//	},
	//資料建檔記錄查詢
	grid1 : $('#C101S01EForm').find("#gridview1").iGrid({
		handler: 'cls1131gridhandler',
		height: 190,
		width: 880,
		autowidth: false,
//		async:false,
		needPager: false,
		postData: {
			formAction: "queryDataArchivalRecordData"
			, 'mainId':CLS1131S01.data.mainId
			, 'custId': CLS1131S01.data.custId
			, 'dupNo': CLS1131S01.data.dupNo
			, 'ownBrId': CLS1131S01.data.ownBrId
        }, 
		colModel: [{
		    colHeader: '資料類別',
		    name: 'dataTypeDesc',
		    width: 150,
		    align: "left",
		    sortable: false
		}, 
		{
		    colHeader: '查詢時間',
		    name: 'queryDate',
		    width: 150,
			align: 'center',
		    sortable: false
		}, 
		{
		    colHeader: '報表檔案',
		    name: 'link',
			align: 'center',
		    width: 80,
		    sortable: false,
		    formatter: 'click',
			onclick: gridOneBtnQueryClickLink
		},
		{
		    colHeader: '資料狀態',
		    name: 'dataStatus',
		    width: 80,
			align: 'center',
		    sortable: false
		},
		{
		    colHeader: '備註',
		    name: 'remark',
		    width: 150,
			align: 'left',
		    sortable: false
		},
		{
            name: 'fileSeq',
            hidden: true
        },
		 {  name: 'dataSrcMemo',hidden: true}
        ,{  name: 'oid', hidden: true }
        ,{  name: 'mainId', hidden: true}
        ,{  name: 'dataType', hidden: true}
        ,{  name: 'reportFileType', hidden: true}
		,{  name: 'dataStatusCode', hidden: true}
		]
	}),
	
	isCloseFinHoldingDefaultDeliveryFunction: function(){
		return $.ajax({
		    handler: CLS1131S01.handler, 
		    action: 'isCloseFinHoldingDefaultDeliveryFunction',
		    //formId: 'empty',
		    data: {
		    },
		    success: function(json){
				
				$('#C101S01EForm').find('#finHoldingSystem').show();
				$('#C101S01EForm').find('#queryFinDefaultDeli').show();
		    	if(json.isCloseFunc == 'Y'){
					$('#C101S01EForm').find('#finHoldingSystem').hide();
					$('#C101S01EForm').find('#queryFinDefaultDeli').hide();
				}
		    }
		}); 
	}
}

// 原查詢證券暨期貨違約交割記錄
function build_C101S01S_CURIQ01(param_mainId){
	return $.ajax({
	    handler: CLS1131S01.handler, 
	    action: 'build_C101S01S_CURIQ01',
	    //formId: 'empty',
	    data: { 'mainId':param_mainId
	    },
	    success: function(json){
	    	
	    }
	}); 
}

function build_C101S01S_RPS(param_mainId){
	var dfd_build_C101S01S_RPS = $.Deferred();    	
	build_C101S01S_RPS_by_type(param_mainId, "1").done(function(){
		build_C101S01S_RPS_by_type(param_mainId, "2").done(function(){
			build_C101S01S_RPS_by_type(param_mainId, "3").done(function(){
				dfd_build_C101S01S_RPS.resolve();
			});
		});
	});
	return dfd_build_C101S01S_RPS.promise();
}

function build_C101S01S_RPS_by_type(param_mainId, param_type){
	return $.ajax({
        handler: CLS1131S01.handler, 
        action: 'build_C101S01S_RPS_by_type',
        //formId: 'empty',
        data: { 'mainId':param_mainId
        	, 'type' : param_type
        },
        success: function(json){
        	
        }
    });
}

function get_tb_ename_elm(){
	/*
	 在同一頁面, 有2個id都叫 eName
	當用 IE 開啟, 若寫  $("input#eName.atTB") 會抓到非 thickBox 的那一個elm
	*/
	return $("input[name=eName].atTB");	
}

function cls1131_import_c101s01c(type){
	if(type=="C1"){
		return $.ajax({ //判斷本次聯徵查詢結果的 KRM040, BAM095 
			type : "POST", handler : "cls1131formhandler", 
			data : {
				'formAction' : "c_query_credit" ,
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
			},
			success:function(json){
				if(json.injectVal=="Y"){
					// $("#C101S01CForm").find("[name=credit]").val(credit_val);
					var credit_val = json.newVal.split("|");
					$('#C101S01CForm').find("[name=credit]").val(credit_val);
				}
			}
		});			
	}else if(type=="C2"){
		return $.ajax({
			type : "POST", handler : "cls1131formhandler", 
			data : {
				'formAction' : "c_query_isPeriodFund",
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
			},
			success:function(json){
				if(json.injectVal=="Y"){
					$('#C101S01CForm').injectData({'isPeriodFund':json.newVal});
				}
			}
		});
	}else if(type=="C3"){
		return $.ajax({
			type : "POST", handler : "cls1131formhandler", 
			data : {
				'formAction' : "c_query_busi" ,
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N")
			},
			success:function(json){
				if(json.injectVal=="Y"){
					$('#C101S01CForm').injectData({'busi':json.newVal});
				}
			}
		});
	}else if(type=="C4"){
		return $.ajax({
			type : "POST", handler : "cls1131formhandler", 
			data : {
				'formAction' : "c_query_invMBalAmt",
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
			},
			success:function(json){
				if(json.injectVal=="Y"){
					$('#C101S01CForm').injectData({'invMBalAmt':json.newVal});
				}
			}
		});
	}else if(type=="LoanBalS"){
		return $.ajax({
			type : "POST", handler : "cls1131formhandler", 
			data : {
				'formAction' : "query_loanBalSByid",
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
			},
			success:function(json){
				$('#C101S01CForm').injectData({'loanBalSByid':json.loanBalSByid});
				$('#C101S01CForm').injectData({'loanBalSByidShow':json.loanBalSByidShow});
				$('#C101S01CForm').injectData({'loanBalSTime':json.loanBalSTime});
			}
		});
	}else if(type=="LoanBalN"){
		return $.ajax({
			type : "POST", handler : "cls1131formhandler", 
			data : {
				'formAction' : "query_loanBalNByid",
				'mainId' : CLS1131S01.data.mainId,
				'custId' : CLS1131S01.data.custId, 
				'dupNo' : CLS1131S01.data.dupNo, 
				'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
			},
			success:function(json){
				$('#C101S01CForm').injectData({'loanBalNByid':json.loanBalNByid});
				$('#C101S01CForm').injectData({'loanBalNByidShow':json.loanBalNByidShow});
				$('#C101S01CForm').injectData({'loanBalNTime':json.loanBalNTime});
				
			}
		});
}else{
		var my_dfd = $.Deferred();  
		return  my_dfd.promise();
	}
}
$(function(){
    util.init();
});

function CLS1131S01VPageSetting(){
	$("#C101S01VForm input[type='radio']").change(function() {
		var r=$(this);
		var text=r.parent().children('input[type=text]');
		r.addClass("required");
		text.prop("disabled", false);
		
		//前後欄位清空
		r.parent().parent().next().children().find('input[type=text]').val("");
		r.parent().parent().prev().children().find('input[type=text]').val("");
		
		//前後欄位required移除
		r.parent().parent().next().children().find('input[type=text]').removeClass("required");
		r.parent().parent().next().children().find('input[type=text]').removeClass("data-error");
		r.parent().parent().prev().children().find('input[type=text]').removeClass("required");
		r.parent().parent().prev().children().find('input[type=text]').removeClass("data-error");
		if(text.get(0) !== undefined){
			r.parent().parent().parent().prev().children().find('input[type=text]').removeClass("required");
			r.parent().parent().parent().prev().children().find('input[type=text]').removeClass("data-error");
		}
		r.parent().find('input[type=text]').prop("disabled", false);
		r.parent().find('input[type=text]').addClass("required");
		
		//前後欄位disabled
		r.parent().parent().next().children().find('input[type=text]').prop("disabled", true);
		r.parent().parent().prev().children().find('input[type=text]').prop("disabled", true);
		if(text.get(0) !== undefined){
			r.parent().parent().parent().next().children().find('input[type=text]').val("");
			r.parent().parent().parent().prev().children().find('input[type=text]').val("");
			
			r.parent().parent().parent().next().children().find('input[type=text]').prop("disabled", true);
			r.parent().parent().parent().prev().children().find('input[type=text]').prop("disabled", true);
		}
		if($("input[name='item2_5']:checked").val()==3){
			$("input[name='item2_5']:checked").parent().parent().parent().prev().children().find('input[type=text]').val("");
			$("input[name='item2_5']:checked").parent().parent().parent().prev().children().find('input[type=text]').removeClass("required");
			$("input[name='item2_5']:checked").parent().parent().parent().prev().children().find('input[type=text]').removeClass("data-error");
			$("input[name='item2_5']:checked").parent().parent().parent().prev().children().find('input[type=text]').prop("disabled", true);
		}
    });
	$("input[name='item2_1']").change(function() {
		item2_1Change();
    });
	$("input[name='item4_1']").change(function() {
		item4Change();
    });
}
function item2_1Change(){
	if ($("input[name='item2_1']:checked").val() == '3') {
		$("input[name='item2_1_sub1']").prop("checked", false);
    	$("input[name='item2_1_sub2']").prop("checked", false);
    	$("input[name='item2_1_sub3']").prop("checked", false);
    	
    	$("input[name='item2_1_sub1']").prop("disabled", true);
    	$("input[name='item2_1_sub2']").prop("disabled", true);
    	$("input[name='item2_1_sub3']").prop("disabled", true);
    	
    	$("input[name='item2_1_sub1']").removeClass("required");
    	$("input[name='item2_1_sub2']").removeClass("required");
    	$("input[name='item2_1_sub3']").removeClass("required");
    }
	else{
		$("input[name='item2_1_sub1']").prop("disabled", false);
    	$("input[name='item2_1_sub2']").prop("disabled", false);
    	$("input[name='item2_1_sub3']").prop("disabled", false);
    	
    	$("input[name='item2_1_sub1']").addClass("required");
    	$("input[name='item2_1_sub2']").addClass("required");
    	$("input[name='item2_1_sub3']").addClass("required");
	}
}
function item4Change(){
	if ($("input[name='item4_1']:checked").val() == '2') {
		$("input[name='item4_1_sub1']").prop("disabled", true);
    	$("input[name='item4_1_sub2']").prop("disabled", true);
    	$("input[name='item4_1_sub3']").prop("disabled", true);
    	$("input[name='item4_1_sub4']").prop("disabled", true);
    	$("input[name='item4_1_sub5']").prop("disabled", true);
    	$("input[name='item4_1_sub1']").prop("checked", false);
    	$("input[name='item4_1_sub2']").prop("checked", false);
    	$("input[name='item4_1_sub3']").prop("checked", false);
    	$("input[name='item4_1_sub4']").prop("checked", false);
    	$("input[name='item4_1_sub5']").prop("checked", false);
    	$("#item4_1_sub1_2_NeedReason1").val("");
    	$("#item4_1_sub3_2_NeedReason1").val("");
    	$("#item4_1_sub3_2_NeedReason2").val("");
    	$("#item4_1_sub4_2_NeedReason1").val("");
    	$("#item4_1_sub1_2_NeedReason1").prop("disabled", true);
    	$("#item4_1_sub3_2_NeedReason1").prop("disabled", true);
    	$("#item4_1_sub3_2_NeedReason2").prop("disabled", true);
    	$("#item4_1_sub4_2_NeedReason1").prop("disabled", true);
    }
	else{
		$("input[name='item4_1_sub1']").prop("disabled", false);
    	$("input[name='item4_1_sub2']").prop("disabled", false);
    	$("input[name='item4_1_sub3']").prop("disabled", false);
    	$("input[name='item4_1_sub4']").prop("disabled", false);
    	$("input[name='item4_1_sub5']").prop("disabled", false);
	}
}

function roundX(val, precision){
    return Math.round(Math.round(val * Math.pow(10, (precision || 0) + 1)) / 10) / Math.pow(10, (precision || 0));
}

var LabourBailout4_0 = {
	
	computeScore: function() {

		if($('#C101S01XForm').valid()){
			return $.ajax({
			type : "POST", handler : "cls1131formhandler",
			data : $.extend($('#C101S01XForm').serializeData()
					, {
						'formAction' : "executeLaborBailoutSimpleCreditScoreTalbe" , 		
						'mainId' : CLS1131S01.data.mainId,
						'custId' : CLS1131S01.data.custId,
						'dupNo':CLS1131S01.data.dupNo 
					}
				),
				success:function(obj){
					LabourBailout4_0.setC101s01xValue(obj);
					CommonAPI.showMessage("計算成功 !");
				}
			});
		}
	},
	
	isShowTag: function(isShow){
		if(isShow){
			$("#tab_LS1131S01_X").show();
		}
		else{
			$("#tab_LS1131S01_X").hide();
		}
	},

	initC101S01X: function(){
		return $.ajax({
			type : "POST", 
			handler : "cls1131formhandler",
			data : 
			$.extend($('#C101S01XForm').serializeData()
				, {
					'formAction' : "getC101S01X" , 		
					'mainId' : CLS1131S01.data.mainId,
					'custId' : CLS1131S01.data.custId,
					'dupNo':CLS1131S01.data.dupNo , 
					'isC120M01A': (CLS1131S01.isC120M01A?"Y":"N") 
				}
			),
			success:function(obj){
				LabourBailout4_0.setC101s01xValue(obj);
			}
		});
	},
	
	setC101s01xValue: function(obj){
		$('#C101S01XForm').injectData(obj);
		$('#C101S01XForm').find("#isApproved").val(obj.isApproved)
		$('#C101S01XForm').find('input[name="notAllowA"]').filter("[value='" + obj.notAllowA + "']").attr('checked', true);
		$('#C101S01XForm').find('input[name="notAllowB"]').filter("[value='" + obj.notAllowB + "']").attr('checked', true);
		$('#C101S01XForm').find('input[name="notAllowC"]').filter("[value='" + obj.notAllowC + "']").attr('checked', true);
		$('#C101S01XForm').find('input[name="notAllowD"]').filter("[value='" + obj.notAllowD + "']").attr('checked', true);
		$('#C101S01XForm').find('input[name="notAllowE"]').filter("[value='" + obj.notAllowE + "']").attr('checked', true);
	}
}

var BatchSelectHeadAccountCheckItem = {
	
	init: function(){

		var str = 
			$("input[name='isCheckOriginalDocument']:checked").val() +
			$("input[name=isNoteBorrower]:checked").val() +
			$("input[name=isImportDataMatch]:checked").val() +
			$("input[name=isFullApplyDocument]:checked").val() +
			$("input[name=isDocSignatureNotMatch]:checked").val() +
			$("input[name=isOwnerBuyerNotSamePerson]:checked").val() +
			$("input[name=isWithNonRelatives]:checked").val() +
			$("input[name=isPointAppropriationDate]:checked").val() +
			$("input[name=isDontKnowOwnAffairs]:checked").val() +
			$("input[name=isDontExplainEjcicRecord]:checked").val() +
			$("input[name=isPayOtherFee]:checked").val() + 
			$("input[name=isCashFromOthers]:checked").val();
			
		if(str == 'YYYNNNNNNNNN'){
			$('input[name="batchSelectNoDoubtItem"]').filter("[value='Y']").click();
		}
	},
	
	isSelectAllItem : function(isSelect){
		
		if(isSelect){
			$('input[name="isCheckOriginalDocument"]').filter("[value='Y']").click();
			$('input[name="isNoteBorrower"]').filter("[value='Y']").click();
			$('input[name="isImportDataMatch"]').filter("[value='Y']").click();
			$('input[name="isFullApplyDocument"]').filter("[value='N']").click();
			$('input[name="isDocSignatureNotMatch"]').filter("[value='N']").click();
			$('input[name="isOwnerBuyerNotSamePerson"]').filter("[value='N']").click();
			$('input[name="isWithNonRelatives"]').filter("[value='N']").click();
			$('input[name="isPointAppropriationDate"]').filter("[value='N']").click();
			$('input[name="isDontKnowOwnAffairs"]').filter("[value='N']").click();
			$('input[name="isDontExplainEjcicRecord"]').filter("[value='N']").click();
			$('input[name="isPayOtherFee"]').filter("[value='N']").click();
			$('input[name="isCashFromOthers"]').filter("[value='N']").click();
		}
	}
}

function showCLS1131S01_ReadOnlyMessage(){
	MegaApi.showMessage("目前狀態僅供「顯示」，不可異動內容");
}
function controlEsgScoreDisplay(isHasEsgScore){
	
	var esgScoreObj = $("#esgScore");
	if(isHasEsgScore == 'Y'){
		esgScoreObj.prop("disabled", false);
	}
	else{
		esgScoreObj.prop("disabled", true);
		esgScoreObj.val("");
	}
}

function controlIsNPODisplay(isNPO){
	if(isNPO){
		$("#juPaidUpCapital").val('0');
		$("#juPaidUpCapital").trigger('change');
		disableGray("juPaidUpCapital");

	}
	else{
		capital = '';
		$('#juPaidUpCapital').trigger('change');
		enableWhite("juPaidUpCapital");
	}
	
}

var concentrateCredit = {
	isShowTag: function(isShow){
		if(isShow){
			$("#tab_LS1131S01_Z").show();
		}
		else{
			$("#tab_LS1131S01_Z").hide();
		}
	}
}

/**
 * J-111-0269 引進Ltv
 */
var LtvAction = {
	ready: false,
	init: function(mainid){
		var $LtvForm = $("#LtvForm");
		//取得前一次執行結果
		$.ajax({
            handler: 'cls1131formhandler',
            action: 'show_last_brmp_homeloanrule',
            data: { 'mainId':mainid },
            success: function(jsonparm){
            	first = jsonparm;
            	if(jsonparm.hasbrmp003){//有結果
                	//取得條件
            		$LtvForm.find("span[id='Ralevel']").val(jsonparm.brmp003Input.ralevel);
            		$LtvForm.find("input[type='radio'][name='fntype'][value='"+jsonparm.brmp003Input.fntype+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='ltvcheck'][value='"+jsonparm.brmp003Input.ltv+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='cd01'][value='"+jsonparm.brmp003Input.cd01+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='cd02'][value='"+jsonparm.brmp003Input.cd02+"']").prop("checked",true);
            		$LtvForm.find("#disoption").val(jsonparm.brmp003Input.disoption);
            		$LtvForm.find("input[type='text'][name='cd03']").val(Math.abs(jsonparm.brmp003Input.cd03));
            		$LtvForm.find("input[type='radio'][name='cd04'][value='"+jsonparm.brmp003Input.cd04+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='ce01'][value='"+jsonparm.brmp003Input.ce01+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='ce02'][value='"+jsonparm.brmp003Input.ce02+"']").prop("checked",true);
            		$LtvForm.find("#ex01").val(jsonparm.brmp003Input.ex01);
            		$LtvForm.find("input[type='radio'][name='ex02'][value='"+jsonparm.brmp003Input.ex02+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='sp01'][value='"+jsonparm.brmp003Input.sp01+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='sp02'][value='"+jsonparm.brmp003Input.sp02+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='sp03'][value='"+jsonparm.brmp003Input.sp03+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='sp04'][value='"+jsonparm.brmp003Input.sp04+"']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][name='sp05'][value='"+jsonparm.brmp003Input.sp05+"']").prop("checked",true);

                    if(jsonparm.brmp003Output.result.errorMsg != null && jsonparm.brmp003Output.result.errorMsg != ""){//show錯誤訊息
                    	$("#span_HouseErrorText").val(jsonparm.brmp003Output.result.errorMsg);
                    	$("#span_HouseErrorText").show();
                    }
                    else{
                    	$("#span_HouseErrorText").hide();
                    }
                    if(jsonparm.brmp003Output.result.resultMsg != null && jsonparm.brmp003Output.result.resultMsg != ""){//show提示訊息
                    	$("#span_HouseResultMsg").val(jsonparm.brmp003Output.result.resultMsg);
                    	$("#span_HouseResultMsg").show();
                    }
                    else{
                    	$("#span_HouseResultMsg").hide();
                    }
            		var finalRate = (jsonparm.brmp003Output.result.finalRate == "" || jsonparm.brmp003Output.result.finalRate == "null" || jsonparm.brmp003Output.result.finalRate == null)
            			? "0" : jsonparm.brmp003Output.result.finalRate;	
//            		$("#span_HouseRate").val(finalRate);
//                	if(finalRate != 0){
//                		$("#span_HouseInfoText").show();
//                	}else{
//                		$("#span_HouseInfoText").hide();
//                	}
            	}else{//無結果，帶預設值
            		$LtvForm.find("input[type='radio'][name='fntype'][value='1']").prop("checked",true);
            		$LtvForm.find("input[type='radio'][value='N']").prop("checked",true);
            		$LtvForm.find("input[type='text']").val("");
            		$("#disoption").val("2");
            		$("#span_HouseErrorText").hide();
            		$("#span_HouseErrorText").val("");
            		$("#span_HouseResultMsg").hide();
            		$("#span_HouseResultMsg").val("");
            	}
            }
        });
	},
	build: function(){
		var $LtvForm = $("#LtvForm");
		$LtvForm.find("input[name='fntype']").change(function(){
			var fntype = $LtvForm.find("input[name='fntype']:checked").val();
			var lbl_cd03 = $LtvForm.find("#lbl_cd03");
			var cd01_obj = $LtvForm.find("input[name='cd01']");//搭配青年安心成家
			var cd02_obj = $LtvForm.find("input[name='cd02']");//是否符合銀行法12條之1自用住宅貸款
			var cd04_obj = $LtvForm.find("input[name='cd04']");//是否為首購(含搭配房貸壽險保費融資額度)
			var ce01_obj = $LtvForm.find("input[name='ce01']");//是否為投資型
			var ce02_obj = $LtvForm.find("input[name='ce02']");//是否為無限制清償期間且不收取提前還款違約金
			switch (fntype){
				case "1":
//					lbl_cd03.text(i18n.cls1131s01["LTV.cd01"] + " " + i18n.cls1131s01["LTV.or"] + " " + i18n.cls1131s01["LTV.cd03"]);
					lbl_cd03.text(i18n.cls1131s01["LTV.cd03"]);
					//搭配青年安心成家、是否符合銀行法、是否為首購、是否為投資型、是否為無限制清償期  可選擇(顯示)
					cd01_obj.parent().parent().hide();
					cd01_obj.filter("[value='N']").prop("checked",true);//隱藏預設否 ， 暫無作用
					cd04_obj.parent().parent().show();
					cd04_obj.filter("[value='N']").prop("checked",true);//之後重發都預設N
					ce01_obj.parent().parent().show();
					ce02_obj.parent().parent().show();
					//是否符合銀行法12條之1自用住宅貸款，預設否 (隱藏)
					cd02_obj.filter("[value='N']").prop("checked",true);
					cd02_obj.parent().parent().hide();
					break;
				case "2":
					//搭配青年安心成家、是否符合銀行法、是否為首購、是否為投資型、是否為無限制清償期，預設否(隱藏)
					lbl_cd03.text(i18n.cls1131s01["LTV.cd03"]);
					cd01_obj.filter("[value='N']").prop("checked",true);//隱藏預設否 ， 暫無作用
					cd01_obj.parent().parent().hide();
					cd04_obj.filter("[value='N']").prop("checked",true);
					cd04_obj.parent().parent().show();
					ce01_obj.filter("[value='N']").prop("checked",true);
					ce01_obj.parent().parent().hide();
					ce02_obj.filter("[value='N']").prop("checked",true);
					ce02_obj.parent().parent().hide();
					//是否符合銀行法12條之1自用住宅貸款 可選擇(顯示)
					//cd02_obj.parent().parent().show();
					//J-113-0326 隱藏cd02
					cd02_obj.parent().parent().hide();
					break;
			}
        })
        $($LtvForm.find("input[name='fntype']")).trigger("change");
				
        return true;
	},
	open: function(){
		//開啟條件框
		var $LtvForm = $("#LtvForm");
        if (!LtvAction.ready) {	
        	LtvAction.ready = LtvAction.build();
        }

		var grade3_markModel_1 = $("#grade3_markModel_1").val() ||"";
		if(grade3_markModel_1 === ""){
			MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.cls1131s01["LTV.check_raLevel"]);
			return;
		}
		//模型最終評等欄位從外層直接帶入
		$("#ralevel").val(grade3_markModel_1);
		 //模型最終評等4,5時 才顯示 "個人負債比"、"是否有保證人或共同借款人" 欄位
		var ex01_obj = $LtvForm.find("#ex01");
		var ex02_obj = $LtvForm.find("input[name='ex02']");
		if($("#ralevel").val() == "4" || $("#ralevel").val() == "5"){
			ex01_obj.parent().parent().show();
			ex02_obj.parent().parent().show();
		}else{
			ex01_obj.parent().parent().hide();
			ex01_obj.val("");
			ex02_obj.parent().parent().hide();
			ex02_obj.filter("[value='N']").prop("checked",true);
		}
		$('#LtvThickBox').thickbox({
            title: i18n.cls1131s01["LTV.title"] || "",
            width: 700,
            height: 430,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                	var $LtvForm = $("#LtvForm");               	
                    //call 決策api /api/eloan/homeLoanRule
                    $.ajax({
                        handler: 'cls1131formhandler',
                        action: 'brmp_homeloanrule',
                        data: { 'mainId':CLS1131S01.data.mainId
                        	, 'custId':CLS1131S01.data.custId
                        	, 'fntype':$LtvForm.find("input[name='fntype']:checked").val()
                        	, 'ralevel':$LtvForm.find("#ralevel").val()
                        	, 'ltv':$LtvForm.find("input[name='ltvcheck']:checked").val()
                        	, 'cd01':$LtvForm.find("input[name='cd01']:checked").val()
                        	, 'cd02':$LtvForm.find("input[name='cd02']:checked").val()
                        	, 'disoption':$LtvForm.find("#disoption").val()
                        	, 'cd03':$LtvForm.find("#cd03").val()
                        	, 'cd04':$LtvForm.find("input[name='cd04']:checked").val()
                        	, 'ce01':$LtvForm.find("input[name='ce01']:checked").val()
                        	, 'ce02':$LtvForm.find("input[name='ce02']:checked").val()
                        	, 'ex01':$LtvForm.find("#ex01").val()
                        	, 'ex02':$LtvForm.find("input[name='ex02']:checked").val()
                        	, 'sp01':$LtvForm.find("input[name='sp01']:checked").val()
                        	, 'sp02':$LtvForm.find("input[name='sp02']:checked").val()
                        	, 'sp03':$LtvForm.find("input[name='sp03']:checked").val()
                        	, 'sp04':$LtvForm.find("input[name='sp04']:checked").val()
                        	, 'sp05':$LtvForm.find("input[name='sp05']:checked").val()

                        },
                        success: function(jsonparm){
                            if(jsonparm.brmp003data.result.errorMsg != null && jsonparm.brmp003data.result.errorMsg != ""){//show錯誤訊息
                            	$("#span_HouseErrorText").val(jsonparm.brmp003data.result.errorMsg);
                            	$("#span_HouseErrorText").show();
                            }
                            else{
                            	$("#span_HouseErrorText").hide();
                            }
                            if(jsonparm.brmp003data.result.resultMsg != null && jsonparm.brmp003data.result.resultMsg != ""){//show提示訊息
                            	$("#span_HouseResultMsg").val(jsonparm.brmp003data.result.resultMsg);
                            	$("#span_HouseResultMsg").show();
                            }
                            else{
                            	$("#span_HouseResultMsg").hide();
                            }
                            //$("#span_HouseInfoText").hide();
                            var finalRate = (jsonparm.brmp003data.result.finalRate == "" || jsonparm.brmp003data.result.finalRate == "null" || jsonparm.brmp003data.result.finalRate == null)
                            	? "0" :jsonparm.brmp003data.result.finalRate;		
//                            $("#span_HouseRate").val(finalRate);
//                            if(finalRate != 0){
//                            	$("#span_HouseInfoText").show();
//                            }else{
//                        		$("#span_HouseInfoText").hide();
//                        	}
                            $.thickbox.close();
                        }
                    });
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
	}
}
/**
 * J-112-0467 歡喜信貸客群查詢
 */
var TermGroupAction = {
	termdata: "",
	callTermGroupRuleQuery: function(popflag){
		var $TermGroupForm = $("form#C101S01BForm");
		var payAmt = "0";
		if(isNaN($TermGroupForm.find("#payAmt").val().replaceAll(",", ""))){//年收萬元
			payAmt = "0";
		}else{
			payAmt = $TermGroupForm.find("#payAmt").val().replaceAll(",", "") * 10000;
		}
		//非營利事業未勾選、 服務單位實收資本總額(新台幣元) 未勾無 時，檢核"服務單位實收資本額" 需>0
		if(!$TermGroupForm.find("#isNPO").is(":checked") && $TermGroupForm.find("#juPaidUpCapital").val() == "0" && !$("#hasJuTotalCapital").is(":checked")){
			$("#span_termGroupRuleResultText").text(i18n.cls1131s01["termGroupRule.juTotalCapitalNeedMoreThenZero"]);
			CommonAPI.showMessage(i18n.cls1131s01["C101S01B.termGroup"] + ": " + i18n.cls1131s01["termGroupRule.juTotalCapitalNeedMoreThenZero"]);
			return;
		}
		var hasJuTotalCapital = "Y";
		if($TermGroupForm.find("input[name='hasJuTotalCapital']:checked").length > 0){
			hasJuTotalCapital = "N";
		}	
        //call 決策api /api/eloan/termGroupRule
        $.ajax({
        	handler: "cls1131formhandler",
            action: "brmp_termGroupRule",
            data: { "mainId": CLS1131S01.data.mainId//個金徵信mainid
              	, "custId": CLS1131S01.data.custId//客戶ID
              	, "dupNo": CLS1131S01.data.dupNo //重複碼
               	, "lnClass": "71"//預設歡喜信貸
               	, "clsJobType2": $TermGroupForm.find("#clsJobType2").val()//行業代碼
               	, "positionCode": $TermGroupForm.find("#clsJobTitle").val()//職位代碼
               	, "juPaidUpCapital": $TermGroupForm.find("#juPaidUpCapital").val()//資本額 (若非公司請帶0)
               	, "hasJuTotalCapital": hasJuTotalCapital
               	, "payAmt": payAmt//年收入(元)
            },
            success: function(jsonparm){
            	//console.log(jsonparm);
            	if(jsonparm.brmp004data.result.errorMsg != null && jsonparm.brmp004data.result.errorMsg != ""){//show提示訊息
            		$("select#termGroup").val("");
            		$("#span_termGroupRuleResultText").text(jsonparm.brmp004data.result.errorMsg);
            	}else{
	                if(jsonparm.brmp004data.result.termGroup != null && jsonparm.brmp004data.result.termGroup != ""){//最終客群分類 "X":不承作 , "S":小額 , "N":普惠 , "G":優質, "E":行員
	                	var msgdialog = $(".msgContent");
	                    var finalmsg = ""; 
	                    var resultmsg = "";
	                    $("select#termGroup").val(jsonparm.brmp004data.result.termGroup);
	                    resultmsg += $("select#termGroup option:selected").text();
	                    if(jsonparm.brmp004data.result.applyDBRType == "A"){
	                    	resultmsg += i18n.cls1131s01["C101S01B.DBR15"];//" (DBR上限15倍)";
	                    }
	                    $("#span_termGroupRuleResultText").text(resultmsg);
	                    if(popflag){
	                    	CommonAPI.showMessage(i18n.cls1131s01["C101S01B.termGroup"] +": " + resultmsg);
	                    }	                    
	                    //msgdialog.html(finalmsg);
	            	}else{
	            		$("select#termGroup").val("");
	            		$("#span_termGroupRuleResultText").text($("select#termGroup option:selected").text());
	            	}
                } 	
            }       
        });
	},
	init: function(mainid,custId,dupNo){
		var $TermGroupForm = $("form#C101S01BForm");
		TermGroupAction.markModelCheckChange();
		TermGroupAction.initUI();
		if(TermGroupAction.isMarkModel3()){
			$.ajax({
				handler: 'cls1131formhandler',
		        action: 'show_last_brmp_termGroupRule',
		        data: { 'mainId': mainid,
		        		'custId': custId,
		        		'dupNo': dupNo},
		        success: function(jsonparm){
		        	TermGroupAction.termdata = jsonparm;
		            if(jsonparm.hasbrmp004){//有結果
		            	//if(jsonparm.brmp004data.result.errorMsg != null && jsonparm.brmp004data.result.errorMsg != ""){//show錯誤訊息
		            	if(jsonparm.brmpErrorMsg && jsonparm.brmpErrorMsg != ""){//show錯誤訊息
		                   	$("#span_termGroupRuleResultText").text(jsonparm.brmpErrorMsg);
		                }
		                //if(jsonparm.brmp004data.result.resultMsg != null && jsonparm.brmp004data.result.resultMsg != ""){//show提示訊息);
		                  	if(jsonparm.brmpTermGroup && jsonparm.brmpTermGroup != ""){//最終客群分類 "X":不承作 , "S":小額 , "N":普惠 , "G":優質, "E":行員
		                  		var resultmsg = "";
		                   		$("select#termGroup").val(jsonparm.brmpTermGroup);
		                   		resultmsg += $("select#termGroup option:selected").text();
		                   		if(jsonparm.brmpApplyDBRType && jsonparm.brmpApplyDBRType == "A"){
	                    			resultmsg += i18n.cls1131s01["C101S01B.DBR15"];//" (DBR上限15倍)";
	                    		}
		                   		$("#span_termGroupRuleResultText").text(resultmsg);
		                   	}
		               // }
		            }else{
		            	if(!isNaN(Date.parse(jsonparm.checkDate)) && !isNaN(Date.parse(CLS1131S01.data.updateTime))){
							if(Date.parse(CLS1131S01.data.updateTime) > Date.parse(jsonparm.checkDate)){
								//上線後舊案引進簽案當時無紀錄or決策掛掉會導致儲存無資料(個金徵信約一個月維護一次)之前沒記到的先重查補上
								//簽報書進來的不要查
								if(!CLS1131S01.isC120M01A){
									TermGroupAction.callTermGroupRuleQuery(false);	
								}
							}
		            	}else{
		            		$("select#termGroup").val("");
		            		$("#span_termGroupRuleResultText").text("");
		            	}
	            	}
		        }
		    });
		}
	},
	markModelCheckChange: function(){
		//未勾選「非房貸申請信用評等:專案信貸(非團體)」、不顯示歡喜信貸客群欄位
		if(TermGroupAction.isMarkModel3()){
			$(".tr_isMarkModel3").show();
		}else{
			$(".tr_isMarkModel3").hide();
		}
	},
	isMarkModel3: function(){
		if($("input[name='markModel'][value='3']:checked").length > 0){
			return true;
		}else{
			return false;
		}
		
	},
	initUI: function(){
		var $TermGroupForm = $("form#C101S01BForm");
		$TermGroupForm.find("input[name='hasJuTotalCapital']").change(function(){
			//服務單位實收資本額(新台幣元)   勾"無"
			if($TermGroupForm.find("input[name='hasJuTotalCapital']:checked").length > 0){//有勾
				$("#juPaidUpCapital").prop("required",false);
				$("#juPaidUpCapital").val("0");
				$("#juPaidUpCapital").trigger("change");
				removeDataErrorStyle("juPaidUpCapital");
				disableGray("juPaidUpCapital");
			}else{//沒勾
            	if($TermGroupForm.find("#isNPO").is(":checked")){//非營利單位打勾, 還是一樣要鎖起來
            		
            	}else{//沒勾打開
            		$("#juPaidUpCapital").prop("required", true);
    				$("#juPaidUpCapital").val("");
    				enableWhite("juPaidUpCapital");
            	}
				
			}
		});	
	}
}
/**
 * J-113-0199 web eLoan行員自動過件
 */
var autoCheckBrmp = {
	termdata: "",
	init: function(rtn_json){
	    $("#autoCheck").hide();
        $("#tab_LS1131S02_C").hide();
        $("[name=prodKindFlag][value='Y']").click(function(){
            if(autoCheckBrmp.showAutoCheckPage()){
                $("#autoCheck").show();
                $("#tab_LS1131S02_C").show();
            }
            else{
                $("#autoCheck").hide();
                $("#tab_LS1131S02_C").hide();
            }
        });
		if (rtn_json) {
            var autoCheckTBody = $("#autoCheckTb").find("#autoCheckTBody");
            var tmpJsonObj=JSON.parse(JSON.stringify(rtn_json));
            ilog.debug("決策policyResult.Length:"+ tmpJsonObj.result.policyResult.length);
            ilog.debug("決策packageVersion:"+ tmpJsonObj.packageVersion);
            autoCheckTBody.find("tr").remove()
            var tr = "";
            for (const policyResult of tmpJsonObj.result.policyResult) {
                //ilog.debug("決策policyCode:"+ policyResult.policyCode);
                tr = "";
                tr +="<tr>"
                tr += ("<td><span>" + (policyResult===undefined?"":DOMPurify.sanitize(policyResult.policyCode)) + "</span></td>");
                tr += ("<td><span>" + (policyResult===undefined?"":DOMPurify.sanitize(policyResult.policyDescription)) + "</span></td>");
                if(policyResult.actionType=='不通過'){
                    tr += ("<td><span class='red'>" + (policyResult===undefined?"":DOMPurify.sanitize(policyResult.actionType)) + "</span></td>");
                }
                else if(policyResult.actionType=='人工審核'){
                    tr += ("<td><span style='color:burlywood'>" + (policyResult===undefined?"":DOMPurify.sanitize(policyResult.actionType)) + "</span></td>");
                }
                else{
                    tr += ("<td><span>" + (policyResult===undefined?"":DOMPurify.sanitize(policyResult.actionType)) + "</span></td>");
                }
                tr += ("<td><span>" + (policyResult===undefined?"":DOMPurify.sanitize(policyResult.showWord)) + "</span></td>");
                tr +="</tr>"

                autoCheckTBody.append(tr);
            }
            $("#C101S02CForm").injectData(rtn_json);
            if(autoCheckBrmp.showAutoCheckPage()){
                $("#autoCheck").show();
                $("#tab_LS1131S02_C").show();
            }
            else{
                $("#autoCheck").hide();
                $("#tab_LS1131S02_C").hide();
            }
    //        for (var j = 0; j < tmpJsonObj.result.policyResult.length; j++) {
    //            tr = "";
    //            var autoCheckRow = tmpJsonObj.result.policyResult;
    //            tr +="<tr>"
    //            tr += ("<td><span>" + (autoCheckRow===undefined?"":DOMPurify.sanitize(tmpJsonObj.result.policyResult.policyCode)) + "</span></td>");
    //            tr += ("<td><span>" + (autoCheckRow===undefined?"":DOMPurify.sanitize(tmpJsonObj.result.policyResult.showWord)) + "</span></td>");
    //            tr += ("<td><span>" + (autoCheckRow===undefined?"":DOMPurify.sanitize(tmpJsonObj.result.policyResult.actionType)) + "</span></td>");
    //            tr +="</tr>"
    //
    //            autoCheckTBody.append(tr);
    //        }
        }
	},
	brmp_autoCheck_new: function(caseMainId, param_formId){
		var my_dfd = $.Deferred();
        //var frmJSON = $("#"+param_formId).serializeData();
        $.ajax({
            handler: CLS1131S01.handler,
            //formId: 'empty',
            action: "brmp_autoCheck_new",
            data:{ 'mainId':CLS1131S01.c122m01a_mainId
                 , 'custId':CLS1131S01.data.custId
                 , 'dupNo':CLS1131S01.data.dupNo
                 , 'loanAmt':$("#loanAmt").val()
                 , 'lnYear':$("#lnYear").val()
                 , 'lnMonth':$("#lnMonth").val()
                 , 'installmentPay':$("#installmentPay").val()
                 , 'onlineCaseNo':$("#ploanCaseNo").val()
//                 , 'pConBegEnd_fg':12
//                 , 'creditLoanPurpose':'B'
//                 , 'termGroup':'E'
                 , 'prodKind':$("#prodKind").val()
            },
            success: function(response){
                var data = new Object();
                autoCheckBrmp.init(response.rtn_json);
                data.mainId = CLS1131S01.c122m01a_mainId;
                data.mainOid = CLS1131S01.c122m01a_mainOid;
                data.oid = CLS1131S01.c122m01a_oid;
            },
            error: function(response){
                ilog.debug("brmp_creditCheck_new {fail}");
                //若 BRMP 執行失敗，讓  user 也能看到已產出的簽報書，免得一直 click
                my_dfd.reject();
                //=======================
                $.thickbox.close();
                $("#gridview").trigger("reloadGrid");
            }
        });

        return my_dfd.promise();
	},
	isAutoCheckMarkModel: function(){
		if($("input[name='markModel'][value='2']:checked").length > 0){
			return true;
		}else{
			return false;
		}

	},
    showAutoCheckPage: function(){
        if($("[name=prodKindFlag][value='Y']:checked").length > 0){
            return true;
        }else{
            return false;
        }

    }
}

function build_C101S01S_EJCIC_T70(param_mainId){
	return $.ajax({
		handler: CLS1131S01.handler,
		action: 'build_C101S01S_EJCIC_T70',
		//formId: 'empty',
		data: {
			'mainId': param_mainId
		},
		success: function(json){
			InterfaceSystemDataInquiry.grid1.trigger("reloadGrid");
		}
	});
}

function findODS_CMFWARNP(type){
    var data = $.extend(CLS1131S01.data, {
        type: type || ''
    });
	return $.ajax({
       handler: CLS1131S01.handler,
       action: 'queryData',
       formId: CLS1131S01.forms,
       data: data,
       success: function(response){
           ilog.debug("@CLS1131S01Page.js :: queryData type=["+type+"][done]");
           var $eForm = $('#C101S01EForm');
           $eForm.setValue(response.C101S01EForm, false);
           if(response.alterMsg){
             MegaApi.showPopMessage(response.alterMsg);
           }
       }
   });
}

//J-113-0341 控制年輕族群客戶加強關懷提問單,借款人年紀為18~22歲(不含)才顯示
function youngCareListShowHide(){
	var birthday = DOMPurify.sanitize($('#C101S01AForm').find("#birthday").val());
	$.ajax({
       handler: 'cls1131formhandler',
       action: 'youngCareListShowHide',
       data: {},
       success: function(res){
    	   var isShow = DOMPurify.sanitize(res.isShow)
    	   if(isShow){
    		   $("#fieldset_youngCareList").show();
    	   }else{
    		   $("#fieldset_youngCareList").hide();
    		   $("input[name^='youngCareResult']").prop("checked", false)
    		   $("#youngCareMemo").val("");
    	   }
       }
   });
}

//J-113-0341 個金徵信作業新增「年輕族群客戶加強關懷提問單」
$('#C101S01AForm').find("#birthday").change(function(){
    youngCareListShowHide();
});

function isShowRelatedDataItem31to34(){
	
	return $.ajax({
       handler: 'cls1131formhandler',
       action: 'isOpenMortgageRatioCheck',
       //formId: 'empty',
       data: {},
       success: function(response){
	   	
	   	  $(".mortChk").hide();
		  if(response.isOpen == 'Y'){
		  	$(".mortChk").show();
		  }
       }
   });
}
