
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF486;
import com.mega.eloan.lms.mfaloan.service.MisELF486Service;

/**
 * <pre>
 * 價金履約保證覆審資料
 * </pre>
 * 
 * @since 2014/4/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2014/4/24,EL08034,new
 *          </ul>
 */
@Service
public class MisELF486ServiceImpl extends AbstractMFAloanJdbc implements
MisELF486Service {

	@Override
	public ELF486 findByPk(String elf486_branch, String elf486_lc_no
			, String elf486_cntrno, String elf486_overdue) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList("ELF486.selByPk"
				, new String[]{elf486_branch, elf486_lc_no, elf486_cntrno, elf486_overdue});
		List<ELF486> list = toELF486(rowData);
		if(list.size()==1){
			return list.get(0);
		}else{
			return null;
		}		
	}
		
	@Override
	public List<Map<String, Object>> sel_already_retrial_cnt(String elf486_branch
			, String elf486_cntrno, String elf486_s_date_s, String elf486_s_date_e){
		return this.getJdbc().queryForList("ELF486.sel_already_retrial_cnt"
				, new String[]{elf486_branch, elf486_cntrno, elf486_s_date_s, elf486_s_date_e});
	}
	private List<ELF486> toELF486(List<Map<String, Object>> rowData){
		List<ELF486> list = new ArrayList<ELF486>();
		for (Map<String, Object> row : rowData) {
			ELF486 model = new ELF486();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
