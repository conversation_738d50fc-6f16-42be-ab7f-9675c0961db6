/* 
 * ScoreServiceJPImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;
import javax.script.ScriptEngineManager;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreAU;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.base.service.ScoreServiceAU;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.Util;
/**
 * <pre>
 * 評分 ServiceImpl
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
@Service("ScoreServiceAU")
public class ScoreServiceAUImpl extends AbstractEloandbJdbc implements
		ScoreServiceAU {

	
	private static final Logger logger = LoggerFactory.getLogger(ScoreServiceJPImpl.class);
	private static final String JSON_NULL = "";
	private HashMap<String, JSONObject> container_houseLoan = new LinkedHashMap<String, JSONObject>();
	private ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

	private static final int OVERSEA_SCALE = 5;

	@Resource
	EjcicService ejcicService;

	@Resource
	EtchService etchService;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	MisdbBASEService misdbBaseService;
	
	@Resource
	ScoreService scoreService;
	
	@Resource
	CodeTypeService codeTypeService;

	
	@Override
	public JSONObject scoreAU(String type, JSONObject data, String varVer, String mowType2) {
		
		//預設使用1.0模型，因1.0、2.0沒有區分房貸非房貸，所以其實[設定檔_House_AU]跟[設定檔_notHouse_AU]是一樣的 (只是強迫症想分一下，但他們都是指向同一之檔案)
		JSONObject result = new JSONObject();
		String fileLoc = ScoreAU.設定檔_House_AU_V1_0;	
		
		if(mowType2.equals(OverSeaUtil.海外評等_房貸)){
			fileLoc = ScoreAU.設定檔_House_AU_V1_0;	
			if(Util.equals(OverSeaUtil.V2_0_LOAN_AU, varVer)){
				fileLoc = ScoreAU.設定檔_House_AU_V2_0;				
			}else if(Util.equals(OverSeaUtil.V3_0_LOAN_AU, varVer)){
				fileLoc = ScoreAU.設定檔_House_AU_V3_0;				
			}
		}else{ //非房貸
			fileLoc = ScoreAU.設定檔_notHouse_AU_V2_0;	
			if(Util.equals(OverSeaUtil.V2_0_LOAN_AU, varVer)){
				fileLoc = ScoreAU.設定檔_notHouse_AU_V2_0;				
			}else if(Util.equals(OverSeaUtil.V3_0_LOAN_AU, varVer)){
				fileLoc = ScoreAU.設定檔_notHouse_AU_V3_0;				
			}
			
		}
		
		JSONObject items = null;
		/*
		 * 邏輯應該是
		 * <1>先有 base基本 → 再算出「初始」評等
		 * <2>依評等 → 對應 DR (若有人工調整評等，要以調整後的評等，去對應)
		 */
		if(Util.equals(type, ScoreAU.type.澳洲消金模型基本)){
			if(Util.equals(OverSeaUtil.V3_0_LOAN_AU, varVer)){//3.0模型，因子有調整
				items = scoreService.parse(ScoreAU.auBase_V3_0.class, fileLoc);
				//因夫妻年收入因子，在各國的單位不一樣，因此須分別設定
				if(Util.equals(data.get("item_p3_curr"), "AUD")){ //澳洲
					JSONObject items_p3_au = scoreService.parse(ScoreAU.auBase_V3_0_P3_AU.class, fileLoc);
					items.putAll(items_p3_au);
				}else if(Util.equals(data.get("item_p3_curr"), "EUR")){ //法國
					JSONObject items_p3_fr = scoreService.parse(ScoreAU.auBase_V3_0_P3_FR.class, fileLoc);
					items.putAll(items_p3_fr);
				}else if(Util.equals(data.get("item_p3_curr"), "CAD")){ //加拿大
					JSONObject items_p3_ca = scoreService.parse(ScoreAU.auBase_V3_0_P3_CA.class, fileLoc);
					items.putAll(items_p3_ca);
				}else{
					
				}
			}else{
				items = scoreService.parse(ScoreAU.auBase.class, fileLoc);		
			}	
		}else if(Util.equals(type, ScoreAU.type.澳洲消金模型評等)){
			items = scoreService.parse(ScoreAU.auGrade.class, fileLoc);
		}else if(Util.equals(type, ScoreAU.type.澳洲消金模型違約機率)){
			if(Util.equals(OverSeaUtil.V3_0_LOAN_AU, varVer)){//3.0模型，因子有調整
				items = scoreService.parse(ScoreAU.auDR_V3_0.class, fileLoc);		
			}else{
				items = scoreService.parse(ScoreAU.auDR.class, fileLoc);
			}
		}
		Properties prop = getProperty(fileLoc);
		
		if (items != null) {
			_debug("[scoreAU]input_param:"+data);
			if(Util.equals(OverSeaUtil.V3_0_LOAN_AU, varVer)){
				get_Score_V3_0(items, result, data, prop);
			}else {
				get_Score(items, result, data, prop);
			}	
		}

		if(Util.equals(type, ScoreAU.type.澳洲消金模型基本)){
			process_AU(data, result, prop, varVer, mowType2);//裡面 call 計算[評等、違約機率]			
		}
		return result;
	}
	
	
	private void get_Score(JSONObject items, JSONObject result, JSONObject data, Properties prop){
		@SuppressWarnings("unchecked")
		Set<String> set = (Set<String>) items.keySet();
		BigDecimal scr_core = BigDecimal.ZERO;
		BigDecimal val_100 = new BigDecimal("100");
		for (String key : set) {
			JSONObject json = (JSONObject) items.get(key);
			Object determine_val = scoreService.determine(key, json, data);
			_debug("[scoreAU]---af["+key+"]"+determine_val);
			result.put(key, determine_val);
							
			String weightKeyName = "";
			String stdKeyName = "";
			if(Util.equals(key, ScoreAU.auBase.年齡_M1)){
				weightKeyName = ScoreAU.column.加權M1;
				stdKeyName = ScoreAU.column.標準化M1;
			}else if(Util.equals(key, ScoreAU.auBase.職業_M5)){
				weightKeyName = ScoreAU.column.加權M5;
				stdKeyName = ScoreAU.column.標準化M5;
			}else if(Util.equals(key, ScoreAU.auBase.年資_M7)){
				weightKeyName = ScoreAU.column.加權M7;
				stdKeyName = ScoreAU.column.標準化M7;
			}else if(Util.equals(key, ScoreAU.auBase.ICR_D1)){
				weightKeyName = ScoreAU.column.加權D1;
				stdKeyName = ScoreAU.column.標準化D1;
			}else if(Util.equals(key, ScoreAU.auBase.夫妻年收入_P3)){
				weightKeyName = ScoreAU.column.加權P3;
				stdKeyName = ScoreAU.column.標準化P3;
			}else if(Util.equals(key, ScoreAU.auBase.契約年限_A5)){
				weightKeyName = ScoreAU.column.加權A5;
				stdKeyName = ScoreAU.column.標準化A5;
			}else if(Util.equals(key, ScoreAU.auBase.VEDASCORE_O1)){
				weightKeyName = ScoreAU.column.加權O1;
				stdKeyName = ScoreAU.column.標準化O1;
			}else if(Util.equals(key, ScoreAU.auBase.擔保品地點及種類_Z1)){
				weightKeyName = ScoreAU.column.加權Z1;
				stdKeyName = ScoreAU.column.標準化Z1;
			}else if(Util.equals(key, ScoreAU.auBase.市場環境及變現性_Z2)){
				weightKeyName = ScoreAU.column.加權Z2;
				stdKeyName = ScoreAU.column.標準化Z2;
			}else{
				
			}
			BigDecimal stdItemVal = auItemStdVal(prop, key, CrsUtil.parseBigDecimal(determine_val));
			BigDecimal weightItemVal = CrsUtil.parseBigDecimal(prop.getProperty(weightKeyName));
			result.put(stdKeyName, stdItemVal);
			result.put(weightKeyName, weightItemVal);
			
			scr_core = scr_core.add(stdItemVal.multiply(weightItemVal).divide(val_100));
		}
		scr_core = Arithmetic.round(scr_core, OVERSEA_SCALE);
		
		result.put(ScoreAU.column.合計WeightedScore, scr_core);
		result.put(ScoreAU.column.核心模型分數, auItemStdVal(prop, ScoreAU.column.合計WeightedScore, scr_core));
	}
	
	
	
	private void get_Score_V3_0(JSONObject items, JSONObject result, JSONObject data, Properties prop){
		@SuppressWarnings("unchecked")
		Set<String> set = (Set<String>) items.keySet();
		BigDecimal scr_core = BigDecimal.ZERO;
		BigDecimal val_100 = new BigDecimal("100");
		for (String key : set) {
			JSONObject json = (JSONObject) items.get(key);
			Object determine_val = scoreService.determine(key, json, data);
			_debug("[scoreAU]---af["+key+"]"+determine_val);
			result.put(key, determine_val);
							
			String weightKeyName = "";
			String weightScrName = "";
			
			if(Util.equals(key, ScoreAU.auBase_V3_0.年齡_M1)){
				weightKeyName = ScoreAU.column.加權M1;
				weightScrName = ScoreAU.column.權重分數M1;
			}else if(Util.equals(key, ScoreAU.auBase_V3_0.職業_M5)){
				weightKeyName = ScoreAU.column.加權M5;
				weightScrName = ScoreAU.column.權重分數M5;
			}else if(Util.equals(key, ScoreAU.auBase_V3_0.年資_M7)){
				weightKeyName = ScoreAU.column.加權M7;
				weightScrName = ScoreAU.column.權重分數M7;
			}else if(Util.equals(key, ScoreAU.auBase_V3_0_P3_AU.夫妻年收入_P3_AU)){ //scr_p3
				weightKeyName = ScoreAU.column.加權P3;
				weightScrName = ScoreAU.column.權重分數P3;
			}else if(Util.equals(key, ScoreAU.auBase_V3_0.契約年限_A5)){
				weightKeyName = ScoreAU.column.加權A5;
				weightScrName = ScoreAU.column.權重分數A5;
			}else if(Util.equals(key, ScoreAU.auBase_V3_0.擔保品地點及種類_Z1)){
				weightKeyName = ScoreAU.column.加權Z1;
				weightScrName = ScoreAU.column.權重分數Z1;
			}else if(Util.equals(key, ScoreAU.auBase_V3_0.市場環境及變現性_Z2)){
				weightKeyName = ScoreAU.column.加權Z2;
				weightScrName = ScoreAU.column.權重分數Z2;
			}else if(Util.equals(key, ScoreAU.auBase_V3_0.教育程度)){
				weightKeyName = ScoreAU.column.加權edu;
				weightScrName = ScoreAU.column.權重分數edu;
			}else if(Util.equals(key, ScoreAU.auBase_V3_0.個人負債比率)){
				weightKeyName = ScoreAU.column.加權drate;
				weightScrName = ScoreAU.column.權重分數drate;
			}else {
				
			}
			//3.0版本不用算標準化(STD)
			//weightItemVal = 權重
			BigDecimal weightItemVal = CrsUtil.parseBigDecimal(prop.getProperty(weightKeyName));
			result.put(weightKeyName, weightItemVal);
			
			BigDecimal scr_decimal = CrsUtil.parseBigDecimal(determine_val);
			//scr_weight = 分數*權重
			BigDecimal scr_weight = scr_decimal.multiply(weightItemVal).divide(val_100);
			result.put(weightScrName, scr_weight);
			scr_core = scr_core.add(scr_weight);
		}
		scr_core = Arithmetic.round(scr_core, OVERSEA_SCALE);
		
		result.put(ScoreAU.column.合計WeightedScore, scr_core); // Σ分數*權重
		result.put(ScoreAU.column.核心模型分數, null);
		
		BigDecimal slope = Util.parseBigDecimal(prop.getProperty(ScoreAU.column.斜率));
		BigDecimal interCept = Util.parseBigDecimal(prop.getProperty(ScoreAU.column.截距));
		result.put(ScoreAU.column.截距, interCept);
		result.put(ScoreAU.column.斜率, slope);

		
		double pd = (1 / (1 + Math.exp( interCept.add(slope.multiply(scr_core)).doubleValue())));			
		result.put(ScoreAU.column.預測壞率, Util.Overflow(pd, 4));
		
	}
	
	
	private static Properties getProperty(String name) {
		Properties prop = new Properties();
		InputStream stream = Thread.currentThread().getContextClassLoader()
				.getResourceAsStream(name);
		try {
			prop.load(stream);
		} catch (IOException e) {
			logger.error("[Properties.load]",e);
		} finally {
			try {
				if (stream != null)
					stream.close();
			} catch (IOException e) {
				logger.error("[InputStream.close]",e);
			}
		}
		return prop;
	}
	
	
	private String get_AU_Activedate(String varVer){
		CodeType activeDate = codeTypeService.findByCodeTypeAndCodeValue("ActiveDate_AU", varVer, "zh_TW");
		String activeDate_AU = "";
		if(activeDate != null){
			activeDate_AU = activeDate.getCodeDesc2();
		}
		
		return activeDate_AU;
	}
	
	@Override
	public String get_Version_AU(){
		if(LMSUtil.cmpDate(new Date(), ">=", CapDate.parseDate(get_AU_Activedate(OverSeaUtil.V3_0_LOAN_AU)))){
			return OverSeaUtil.V3_0_LOAN_AU;
		}else if(LMSUtil.cmpDate(new Date(), ">=", CapDate.parseDate(OverSeaUtil.V2_0_AU_ACTIVEDATE))){
			return OverSeaUtil.V2_0_LOAN_AU;
		}else{
			return OverSeaUtil.V1_0_LOAN_AU;
		}
		
	}
	
	private void _debug(String s){
		
	}
	
	private BigDecimal auItemStdVal(Properties prop, String key, BigDecimal factorVal){	
		BigDecimal devSampleMean = BigDecimal.ZERO;
		BigDecimal devSampleSTD = BigDecimal.ONE;
		BigDecimal A = BigDecimal.ZERO;
		BigDecimal B = BigDecimal.ZERO;
		if(Util.equals(key, ScoreAU.column.合計WeightedScore)){
			devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreDevSampleMean"));
			devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreDevSampleSTD"));
			A = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreA"));
			B = CrsUtil.parseBigDecimal(prop.getProperty("stdCoreScoreB"));
		}else{
			A = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreA"));
			B = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreB"));
			
			if(Util.equals(key, ScoreAU.auBase.年齡_M1)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_m1"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_m1"));				
			}else if(Util.equals(key, ScoreAU.auBase.職業_M5)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_m5"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_m5"));				
			}else if(Util.equals(key, ScoreAU.auBase.年資_M7)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_m7"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_m7"));				
			}else if(Util.equals(key, ScoreAU.auBase.ICR_D1)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_d1"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_d1"));
			}else if(Util.equals(key, ScoreAU.auBase.夫妻年收入_P3)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_p3"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_p3"));				
			}else if(Util.equals(key, ScoreAU.auBase.契約年限_A5)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_a5"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_a5"));				
			}else if(Util.equals(key, ScoreAU.auBase.VEDASCORE_O1)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_o1"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_o1"));
			}else if(Util.equals(key, ScoreAU.auBase.擔保品地點及種類_Z1)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_z1"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_z1"));				
			}else if(Util.equals(key, ScoreAU.auBase.市場環境及變現性_Z2)){
				devSampleMean = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleMean_z2"));
				devSampleSTD = CrsUtil.parseBigDecimal(prop.getProperty("stdItemScoreDevSampleSTD_z2"));					
			}	
		}
		BigDecimal val = CapMath.normalization(factorVal, devSampleMean, devSampleSTD, B, A, MathContext.DECIMAL64);
		return Arithmetic.round(val, OVERSEA_SCALE);	
	}
	
	private void process_AU(JSONObject data, JSONObject result, Properties prop, String varVer, String mowType2) {
		if (prop != null) {
			
			result.put(ScoreAU.column.評等建立日期, CapDate.getCurrentDate(DataParse.DateFormat));
			result.put(ScoreAU.column.模型版本, varVer);
						
			if(true){//評等
				JSONObject level = scoreAU(ScoreAU.type.澳洲消金模型評等, result, varVer, mowType2);
				String pRating = Util.trim(level.get(ScoreAU.auGrade.等級));
				result.put(ScoreAU.column.初始評等, pRating);
				//================					
				Integer sumRiskPt = process_AU_sumRiskPt(data, varVer);
				Integer adj_pts = get_AU_adj_pts_g(sumRiskPt);
				Integer adj_sw = get_AU_adj_sw(data, varVer);
				Integer adj_oi = get_AU_adj_oi(data, varVer);
				Integer sRating = null;	
				if(Util.isNotEmpty(pRating) && Util.isInteger(pRating)){
					//依「累加風險點數」降等
					int raw_sRating = Util.parseInt(pRating) - adj_pts - adj_sw - adj_oi;
					if(Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)){
						sRating = OverSeaUtil.rating_min1_max15(raw_sRating);
					}else{
						sRating = OverSeaUtil.rating_min1_max10(raw_sRating);
					}
				}
				//================
				if(true){
					result.put(ScoreAU.column.累加風險點數, Util.trim(sumRiskPt));
					result.put(ScoreAU.column.升降等_依風險點數, Util.trim(adj_pts));
					result.put(ScoreAU.column.升降等_依特殊警訊, Util.trim(adj_sw));
					result.put(ScoreAU.column.升降等_依其他資訊, Util.trim(adj_oi));
				}				
				result.put(ScoreAU.column.獨立評等, Util.trim(sRating));
				
				result.put(ScoreAU.column.支援評等, Util.trim(sRating));
				result.put(ScoreAU.column.調整評等, "");
				result.put(ScoreAU.column.原始最終評等, Util.trim(sRating));
				result.put(ScoreAU.column.最終評等, Util.trim(sRating));
	
				result.put(ScoreAU.column.評等調整日期, "");
				result.put(ScoreAU.column.完成最終評等日期, "");
				result.put(ScoreAU.column.註記不調整, "");
				result.put(ScoreAU.column.調整狀態, "");
				result.put(ScoreAU.column.調整註記, "");
				result.put(ScoreAU.column.調整理由, "");
			}
			if(true){//違約機率
				JSONObject auDR = scoreAU(ScoreAU.type.澳洲消金模型違約機率, result, varVer, mowType2);
				setAUDR(auDR, result);
			}
			// clear
			prop.clear();
			prop = null;
		}
	}
	
	private Integer process_AU_sumRiskPt(JSONObject data, String varVer){
		String have = UtilConstants.haveNo.有;
		
		int sumRiskPt = 0;
		//和 UI 的順序相同，以方便比對 chkItem 對應的點數
		if(Util.equals(Util.trim(data.get(ScoreAU.column.AU一般警訊1)), have)){
			sumRiskPt += 3;
		}
		if(Util.equals(Util.trim(data.get(ScoreAU.column.AU一般警訊2)), have)){
			sumRiskPt += 2;
		}
		if(Util.equals(Util.trim(data.get(ScoreAU.column.AU一般警訊3)), have)){
			sumRiskPt += 1;
		}
		return sumRiskPt;
	}
	/**	
	降1等	1<=points<3
	降2等	3<=points<5
	降3等	points>=5
	*/
	private int get_AU_adj_pts_g(Integer sumRiskPt){
		int adj = 0;
		if(sumRiskPt==null){
			
		}else if(sumRiskPt>=1 && sumRiskPt<3){
			adj = -1;
		}else if(sumRiskPt>=3 && sumRiskPt<5){
			adj = -2;
		}else if(sumRiskPt>=5){
			adj = -3;
		}
		return adj;
	}
	
	private int get_AU_adj_sw(JSONObject data, String varVer){		
		String have = UtilConstants.haveNo.有;
		if(Util.equals(Util.trim(data.get(ScoreAU.column.AU特殊警訊1)), have)
				|| Util.equals(Util.trim(data.get(ScoreAU.column.AU特殊警訊2)), have)){
			return -3;
		}
		return 0;
	}	
	
	private int get_AU_adj_oi(JSONObject data, String varVer){		
		String have = UtilConstants.haveNo.有;
		if(Util.equals(Util.trim(data.get(ScoreAU.column.AU其他資訊1)), have)){
			return 1;
		}
		return 0;
	}
	
	@Override
	public void setAUDR(JSONObject auDR, JSONObject target){
		String[] keyArr = {ScoreAU.auDR.違約機率_預估3年期, ScoreAU.auDR.違約機率_預估1年期};
		for(String key: keyArr){
			target.put(key, auDR.get(key));	
		}
	}
	

	public boolean getModelType_3_0(String loanTP) {
		/*Step1.判斷要引入的模型為房貸 or 非房貸
		 * 若會計科目為[1260-5000_273短期房屋購置擔保放款、1350-6200_473中期房屋購置擔保放款、1350-6300_474中期房屋修繕擔保放款、
		 * 1450-1500_673長期房屋購置擔保放款、1450-2000_674長期房屋修繕擔保放款、1260-6000_???房屋修繕住宅貸款(這個科目在系統及科目表都找不到，先不管)]
		 * 其餘引用[非房貸模型]
		*/
		//loanTP=會計科目
		Map<String, String> modelTypeMap = codeTypeService.findByCodeType("modelType_AU_M");
		if(modelTypeMap.get(loanTP) != null ){
			return true;
		}
			return false;
	}
	
}