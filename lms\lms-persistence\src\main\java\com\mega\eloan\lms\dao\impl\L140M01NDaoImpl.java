/* 
 * L140M01NDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140M01NDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01N;

/** 額度利率結構利率化明細檔 **/
@Repository
public class L140M01NDaoImpl extends LMSJpaDao<L140M01N, String> implements
		L140M01NDao {

	@Override
	public L140M01N findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01N> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140M01N> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140M01N findByUniqueKey(String mainId, Integer rateSeq,
			String rateType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rateSeq", rateSeq);
		search.addSearchModeParameters(SearchMode.EQUALS, "rateType", rateType);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01N> findByIndex01(String mainId, Integer rateSeq,
			String rateType) {
		ISearch search = createSearchTemplete();
		List<L140M01N> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (rateSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rateSeq",
					rateSeq);
		if (rateType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rateType",
					rateType);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01N> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		return createQuery(search).getResultList();
	}

	@Override
	public List<L140M01N> findByMainIdAndRateSeq(String mainId, Integer rateSeq) {
		ISearch search = createSearchTemplete();
		List<L140M01N> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (rateSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rateSeq",
					rateSeq);
		search.addOrderBy("rateType");
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M01N> findByMainIdAndRateSeqOrderByWithGrid(String mainId,
			Integer rateSeq) {
		ISearch search = createSearchTemplete();
		List<L140M01N> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (rateSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rateSeq",
					rateSeq);
		// BY t0.RATETYPE ASC, t0.SECNO ASC, t0.SECNOOP ASC, t0.RATEDSCR ASC

		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("rateType", false);
		printSeqMap.put("secNo", false);
		printSeqMap.put("secNoOp", false);
		printSeqMap.put("createTime", false);
		printSeqMap.put("rateDscr", false);
		search.setOrderBy(printSeqMap);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}