package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140S04C model.
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S04C-entity-graph", attributeNodes = { @NamedAttributeNode("c140m04a") })
@Entity
@Table(name="C140S04C", uniqueConstraints = @UniqueConstraint(columnNames ={"oid"}))
public class C140S04C extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(precision=12)
	private BigDecimal buAmtUnit;

	@Column(length=120)
	private String buAddr;

	@Column(precision = 11, scale = 2)
	private BigDecimal buBuM;

	@Column(precision=12)
	private BigDecimal buMm;

	@Column(length=60)
	private String buMp;

	@Column(length=20)
	private String buNum;

	@Column(precision = 11, scale = 2)
	private BigDecimal buP;

	@Column(length=2)
	private String buStru;

	@Column(length=1)
	private String buUnit;

	@Column(length=1)
	private String buUse;

	@Column(length=3)
	private String buCurr;
	
	@Column(precision = 11, scale = 2)
	private BigDecimal buVal;

	@Column(length = 3)
	private String curr;
	
	@Column(precision = 12, scale = 0)
	private BigDecimal amtUnit;
	
	//bi-directional many-to-one association to C140M04A
    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
		@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false)
		})
	private C140M04A c140m04a;

	public BigDecimal getBuAmtUnit() {
		return this.buAmtUnit;
	}

	public void setBuAmtUnit(BigDecimal buAmtUnit) {
		this.buAmtUnit = buAmtUnit;
	}

	public String getBuAddr() {
		return this.buAddr;
	}

	public void setBuAddr(String buAddr) {
		this.buAddr = buAddr;
	}

	public BigDecimal getBuBuM() {
		return this.buBuM;
	}

	public void setBuBuM(BigDecimal buBuM) {
		this.buBuM = buBuM;
	}

	public BigDecimal getBuMm() {
		return this.buMm;
	}

	public void setBuMm(BigDecimal buMm) {
		this.buMm = buMm;
	}

	public String getBuMp() {
		return this.buMp;
	}

	public void setBuMp(String buMp) {
		this.buMp = buMp;
	}

	public String getBuNum() {
		return this.buNum;
	}

	public void setBuNum(String buNum) {
		this.buNum = buNum;
	}

	public BigDecimal getBuP() {
		return this.buP;
	}

	public void setBuP(BigDecimal buP) {
		this.buP = buP;
	}

	public String getBuStru() {
		return this.buStru;
	}

	public void setBuStru(String buStru) {
		this.buStru = buStru;
	}

	public String getBuUnit() {
		return this.buUnit;
	}

	public void setBuUnit(String buUnit) {
		this.buUnit = buUnit;
	}

	public String getBuUse() {
		return this.buUse;
	}

	public void setBuUse(String buUse) {
		this.buUse = buUse;
	}

	public String getBuCurr() {
		return this.buCurr;
	}

	public void setBuCurr(String buCurr) {
		this.buCurr = buCurr;
	}

	public C140M04A getC140m04a() {
		return this.c140m04a;
	}

	public void setC140m04a(C140M04A c140m04a) {
		this.c140m04a = c140m04a;
	}

	public BigDecimal getBuVal() {
		return buVal;
	}

	public void setBuVal(BigDecimal buVal) {
		this.buVal = buVal;
	}

	public String getCurr() {
		return curr;
	}

	public void setCurr(String curr) {
		this.curr = curr;
	}

	public BigDecimal getAmtUnit() {
		return amtUnit;
	}

	public void setAmtUnit(BigDecimal amtUnit) {
		this.amtUnit = amtUnit;
	}	
}