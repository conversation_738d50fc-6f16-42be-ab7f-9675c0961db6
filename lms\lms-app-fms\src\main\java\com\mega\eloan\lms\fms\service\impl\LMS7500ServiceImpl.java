package com.mega.eloan.lms.fms.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L140MM3ADao;
import com.mega.eloan.lms.dao.L140MM3BDao;
import com.mega.eloan.lms.dao.L140MM3CDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS7500M01Page;
import com.mega.eloan.lms.fms.service.LMS7500Service;
import com.mega.eloan.lms.mfaloan.bean.ELF506;
import com.mega.eloan.lms.mfaloan.bean.ELF515;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF515Service;
import com.mega.eloan.lms.mfaloan.service.MisEllnseekservice;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140MM3A;
import com.mega.eloan.lms.model.L140MM3B;
import com.mega.eloan.lms.model.L140MM3C;
import com.mega.eloan.lms.obsdb.service.ObsdbBASEService;
import com.mega.eloan.lms.obsdb.service.ObsdbELF461Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF506Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF515Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 都更危老註記維護作業
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS7500ServiceImpl extends AbstractCapService implements
		LMS7500Service {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS7500ServiceImpl.class);

	@Resource
	L140MM3CDao l140mm3cDao;

	@Resource
	L140MM3ADao l140mm3aDao;

	@Resource
	L140MM3BDao l140mm3bDao;

	@Resource
	FlowService flowService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	MisELF515Service misELF515Service;

	@Resource
	MisELF447nService misELF447nService;

	@Resource
	ObsdbELF506Service obsdbELF506Service;

	@Resource
	ObsdbELF515Service obsdbELF515Service;

	@Resource
	DocFileService docFileService;

	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	@Resource
	MisEllnseekservice misEllnseekservice;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	ObsdbELF461Service obsdbELF461Service;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	ObsdbBASEService obsDBService;
	
	@Resource
	L120M01ADao l120m01aDao;
	
	@Resource
	LMSLgdService lmsLgdService;
	
	@Resource
	CLSService clsService;

	@Override
	public L140MM3A findL140mm3aByUniqueKey(String mainId) {
		return l140mm3aDao.findByUniqueKey(mainId);
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L140MM3A.class) {
			return l140mm3aDao.findByMainId(mainId);
		} else if (clazz == L140MM3B.class) {
			return l140mm3bDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L140MM3A) {
					if (Util.isEmpty(((L140MM3A) model).getOid())) {
						((L140MM3A) model).setCreator(user.getUserId());
						((L140MM3A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l140mm3aDao.save((L140MM3A) model);

						flowService.start("LMS7500Flow",
								((L140MM3A) model).getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						((L140MM3A) model).setUpdater(user.getUserId());
						((L140MM3A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l140mm3aDao.save((L140MM3A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L140MM3A) model)
									.getMainId());
							docLogService.record(((L140MM3A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof L140MM3B) {
					((L140MM3B) model).setUpdater(user.getUserId());
					((L140MM3B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm3bDao.save((L140MM3B) model);
				} else if (model instanceof L140MM3C) {
					((L140MM3C) model).setUpdater(user.getUserId());
					((L140MM3C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140mm3cDao.save((L140MM3C) model);
				}
			}
		}
	}

	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L140MM3A.class) {
			return l140mm3aDao.findPage(search);
		} else if (clazz == L140MM3C.class) {
			return l140mm3cDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L140MM3A.class) {
			return (T) l140mm3aDao.findByOid(oid);
		} else if (clazz == L140MM3B.class) {
			return (T) l140mm3bDao.findByOid(oid);
		} else if (clazz == L140MM3C.class) {
			return (T) l140mm3cDao.findByOid(oid);
		}
		return null;
	}

	// 參考 LMSServiceImpl.java queryOnLine772FlagByCntrnoSecond
	public Map<String, String> getElf515Data(L140MM3A l140mm3a)
			throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String toDayStr = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		String mainId = l140mm3a.getMainId();
		String cntrNo = l140mm3a.getCntrNo();
		String custId = l140mm3a.getCustId();
		String dupNo = l140mm3a.getDupNo();
		returnMap.put("mainId", mainId);

		if (Util.equals(cntrNo, "")) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			// lmsL120M01A.error006=額度明細表額度序號不得為空白
			throw new CapMessageException(
					pop.getProperty("lmsL120M01A.error006"), getClass());

		}

		returnMap.put("cntrNoChkExistFlag", "");
		returnMap.put("cntrNoChkExistDate", "");
		returnMap.put("is722OnFlag", "");
		returnMap.put("is722CntrNo", "");
		returnMap.put("is722QDate", "");
		returnMap.put("exceptFlagOn", "");
		returnMap.put("exceptFlagQAisYOn", "");
		returnMap.put("exceptFlagQAPlusOn", "");

		Map<String, Object> dataMap = null;
		int count = 0;
		String cntrBranch = cntrNo.substring(0, 3);
		// 2.檢核額度序號是否存在
		if (UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(cntrBranch).getBrNoFlag())
				|| UtilConstants.BrNoType.子銀行.equals(branchService.getBranch(
						cntrBranch).getBrNoFlag())) {
			// 海外分行
			dataMap = dwdbBASEService.findDWCntrnoByCntrNo(custId, dupNo,
					cntrNo);
			if (dataMap != null && !dataMap.isEmpty()) {
				count = 1;
			} else {
				count = 0;
			}

		} else {
			// 國內分行
			// X為 遠匯
			if ("X".equals(cntrNo.substring(7, 8))) {
				count = misdbBASEService.findLNF197BycntrNoAndCustId(cntrNo,
						custId, dupNo);
			} else {
				count = misdbBASEService.findMISLN20BycntrNoAndCustId(cntrNo,
						custId, dupNo);
			}
		}

		if (count == 0) {
			returnMap.put("cntrNoChkExistFlag", "N");
		} else {
			returnMap.put("cntrNoChkExistFlag", "Y");
		}

		// 1.取得前次ELOAN 註記資料
		Map<String, Object> elf506DataMap = misELF506Service
				.getByCntrNo(cntrNo);
		String preBuyFlag = "";
		String isInstalmentOn = "";
		String isFinancingNotesAgreedBefore = "";
		// String prodKind = ""; // 產品種類 33.土地融資 34.建築融資
		// String adcCaseNo = ""; // ADC案件編號
		if (elf506DataMap != null && !elf506DataMap.isEmpty()) {
			preBuyFlag = Util.trim(elf506DataMap.get("ELF506_722_IS_BUY"));
			isInstalmentOn = Util.trim(elf506DataMap.get("ELF506_INSTALMENT"));
			// J-111-0633_05097_B1001 Web
			// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			// prodKind = Util.trim(elf506DataMap.get("ELF506_PROD_KIND"));
			// adcCaseNo = Util.trim(elf506DataMap.get("ELF506_ADC_CASENO"));

			// 國內_企金_授信核准時，ELF506_722_IS_BUY無值時會塞X給a-Loan判斷不檢核
			if (Util.equals(preBuyFlag, "X")) {
				preBuyFlag = "";
			}
			
			// 前次約定融資額度註記
			if(!Util.isNotEmpty(elf506DataMap.get("ELF506_EX_QA_Y"))){
				returnMap.put("exceptFlagOn", Util.trim(elf506DataMap.get("ELF506_EXCEPT")));
				returnMap.put("exceptFlagQAisYOn", Util.trim(elf506DataMap.get("ELF506_EX_QA_Y")));
				returnMap.put("exceptFlagQAPlusOn", Util.trim(elf506DataMap.get("ELF506_EX_QA_PLUS")));
			}
		}

		// J-111-0633_05097_B1001 Web
		// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
		// ELF506 無資料 再去撈 ELF447N
		// if (Util.isEmpty(prodKind)) {
		// List<Map<String, Object>> elf447nList = misELF447nService
		// .getByCntrNoOrderByEndDateDesc(cntrNo);
		// if (elf447nList != null && !elf447nList.isEmpty()
		// && elf447nList.size() > 0) {
		// Map<String, Object> elf447nDataMap = elf447nList.get(0);
		// prodKind = Util.trim(elf447nDataMap.get("ELF447N_PROD_CLASS"));
		// }
		// }

		returnMap.put("cntrNoChkExistDate", toDayStr);

		returnMap.put("is722CntrNo", cntrNo);
		returnMap.put("is722QDate", toDayStr);
		returnMap.put("isBuyOn", preBuyFlag);
		returnMap.put("isInstalmentOn", isInstalmentOn);
		// J-111-0633_05097_B1001 Web
		// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
		// returnMap.put("prodKind", prodKind);
		// returnMap.put("adcCaseNo", adcCaseNo);

		List<ELF515> elf515s = misELF515Service.getCurrentDataByCntrNo(cntrNo);
		List<L140MM3C> l140mm3cs = new ArrayList<L140MM3C>();
		if (elf515s != null && elf515s.size() > 0) {
			for (ELF515 elf515 : elf515s) {
				String elf515_type = Util.trim(elf515.getElf515_type());
				String elf515_sub_type1 = Util.trim(elf515
						.getElf515_sub_type1());

				L140MM3C l140mm3c = new L140MM3C();
				l140mm3c.setMainId(mainId);
				l140mm3c.setIsTurnOver(Util.trim(elf515.getElf515_instalment()));
				l140mm3c.setEstateType(elf515_type);

				if (UtilConstants.L140M01T_estatType.都更危老.equals(elf515_type)) {

					l140mm3c.setEstateSubType(elf515_sub_type1);
					if (UtilConstants.L140M01T_estatSubType.都更
							.equals(elf515_sub_type1)
							|| UtilConstants.L140M01T_estatSubType.危老
									.equals(elf515_sub_type1)
							|| UtilConstants.L140M01T_estatSubType.其它都更危老
									.equals(elf515_sub_type1)) {

						l140mm3c.setEstateStatus(elf515.getElf515_sub_type2());
						l140mm3c.setOverDate(CapDate.parseDate(elf515
								.getElf515_sub_type3()));

						l140mm3c.setEstateCityId(Util.trim(elf515
								.getElf515_site1()));
						l140mm3c.setEstateAreaId(Util.trim(elf515
								.getElf515_site2()));
						l140mm3c.setEstateSit3No(Util.trim(elf515
								.getElf515_site3()));
						l140mm3c.setEstateSit4No(Util.trim(elf515
								.getElf515_site4()));
						l140mm3c.setBuildWay(Util.trim(elf515
								.getElf515_sub_type4()));
						l140mm3c.setmCntrNo(Util.trim(elf515
								.getElf515_cntrno_m()));
						l140mm3c.setLandlordNum(CapMath.getBigDecimal(elf515
								.getElf515_data3()));
						l140mm3c.setSubTypeNote(Util.trim(elf515
								.getElf515_data1()));
						l140mm3c.setEstateNote(Util.trim(elf515
								.getElf515_data2()));
						l140mm3c.setOtherDesc(Util.trim(elf515
								.getElf515_data4()));//J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
					}

				} else if (UtilConstants.L140M01T_estatType.公私立各級學校
						.equals(elf515_type)) {
					l140mm3c.setPosition(Util.trim(elf515
							.getElf515_inter_outer()));
					l140mm3c.setEstateStatus(elf515.getElf515_build_state());
					l140mm3c.setOverDate(elf515.getElf515_build_date());

					l140mm3c.setSubject(elf515.getElf515_data1());
					l140mm3c.setEstateCityId(Util.trim(elf515.getElf515_site1()));
					l140mm3c.setEstateAreaId(Util.trim(elf515.getElf515_site2()));
					l140mm3c.setEstateSit3No(Util.trim(elf515.getElf515_site3()));
					l140mm3c.setEstateSit4No(Util.trim(elf515.getElf515_site4()));
					l140mm3c.setSiteNote(Util.trim(elf515.getElf515_address()));
				} else if (UtilConstants.L140M01T_estatType.醫療機構
						.equals(elf515_type)) {
					l140mm3c.setPosition(Util.trim(elf515
							.getElf515_inter_outer()));
					l140mm3c.setEstateStatus(elf515.getElf515_build_state());
					l140mm3c.setOverDate(elf515.getElf515_build_date());

					l140mm3c.setSubject(elf515.getElf515_data1());
					l140mm3c.setSubjectCode(Util.trim(elf515.getElf515_data2()));
					l140mm3c.setEstateCityId(Util.trim(elf515.getElf515_site1()));
					l140mm3c.setEstateAreaId(Util.trim(elf515.getElf515_site2()));
					l140mm3c.setEstateSit3No(Util.trim(elf515.getElf515_site3()));
					l140mm3c.setEstateSit4No(Util.trim(elf515.getElf515_site4()));
					l140mm3c.setSiteNote(Util.trim(elf515.getElf515_address()));
				} else if (UtilConstants.L140M01T_estatType.政府廳舍
						.equals(elf515_type)) {
					l140mm3c.setPosition(Util.trim(elf515
							.getElf515_inter_outer()));
					l140mm3c.setEstateStatus(elf515.getElf515_build_state());
					l140mm3c.setOverDate(elf515.getElf515_build_date());

					l140mm3c.setSubject(elf515.getElf515_data1());
					l140mm3c.setSubjectKind(Util.trim(elf515.getElf515_data2()));
					l140mm3c.setEstateCityId(Util.trim(elf515.getElf515_site1()));
					l140mm3c.setEstateAreaId(Util.trim(elf515.getElf515_site2()));
					l140mm3c.setEstateSit3No(Util.trim(elf515.getElf515_site3()));
					l140mm3c.setEstateSit4No(Util.trim(elf515.getElf515_site4()));
					l140mm3c.setSiteNote(Util.trim(elf515.getElf515_address()));
				} else if (UtilConstants.L140M01T_estatType.長照服務機構
						.equals(elf515_type)) {
					l140mm3c.setPosition(Util.trim(elf515
							.getElf515_inter_outer()));
					l140mm3c.setEstateStatus(elf515.getElf515_build_state());
					l140mm3c.setOverDate(elf515.getElf515_build_date());

					l140mm3c.setSubject(elf515.getElf515_data1());
					l140mm3c.setSubjectKind(Util.trim(elf515.getElf515_data2()));
					l140mm3c.setEstateCityId(Util.trim(elf515.getElf515_site1()));
					l140mm3c.setEstateAreaId(Util.trim(elf515.getElf515_site2()));
					l140mm3c.setEstateSit3No(Util.trim(elf515.getElf515_site3()));
					l140mm3c.setEstateSit4No(Util.trim(elf515.getElf515_site4()));
					l140mm3c.setSiteNote(Util.trim(elf515.getElf515_address()));
				} else if (UtilConstants.L140M01T_estatType.社會住宅
						.equals(elf515_type)) {
					l140mm3c.setPosition(Util.trim(elf515
							.getElf515_inter_outer()));
					l140mm3c.setEstateStatus(elf515.getElf515_build_state());
					l140mm3c.setOverDate(elf515.getElf515_build_date());

					l140mm3c.setSubject(elf515.getElf515_data1());
					l140mm3c.setEstateCityId(Util.trim(elf515.getElf515_site1()));
					l140mm3c.setEstateAreaId(Util.trim(elf515.getElf515_site2()));
					l140mm3c.setEstateSit3No(Util.trim(elf515.getElf515_site3()));
					l140mm3c.setEstateSit4No(Util.trim(elf515.getElf515_site4()));
					l140mm3c.setSiteNote(Util.trim(elf515.getElf515_address()));
				} else if (UtilConstants.L140M01T_estatType.A0井
						.equals(elf515_type)) {
					l140mm3c.setPosition(Util.trim(elf515
							.getElf515_inter_outer()));
					l140mm3c.setEstateStatus(elf515.getElf515_build_state());
					l140mm3c.setOverDate(elf515.getElf515_build_date());

					l140mm3c.setSubjectCode(Util.trim(elf515.getElf515_data1()));
					l140mm3c.setEstateCityId(Util.trim(elf515.getElf515_site1()));
					l140mm3c.setEstateAreaId(Util.trim(elf515.getElf515_site2()));
					l140mm3c.setEstateSit3No(Util.trim(elf515.getElf515_site3()));
					l140mm3c.setEstateSit4No(Util.trim(elf515.getElf515_site4()));
					l140mm3c.setSiteNote(Util.trim(elf515.getElf515_address()));
					l140mm3c.setSectKind(Util.trim(elf515.getElf515_sub_type2()));
					l140mm3c.setUseSect(Util.trim(elf515.getElf515_sub_type3()));
					l140mm3c.setUseKind(Util.trim(elf515.getElf515_sub_type4()));
				} else {

				}

				l140mm3c.setFlag("N");
				l140mm3c.setCheckYN("Y");
				l140mm3c.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm3c.setCreator(user.getUserId());
				l140mm3cs.add(l140mm3c);
			}
		}
		List<L140MM3C> lastData = l140mm3cDao.findByMainId(mainId);
		l140mm3cDao.delete(lastData);
		l140mm3cDao.save(l140mm3cs);

		// is722OnFlag 本來都是抓506資料，但這欄位廢掉，改用判斷的方式
		l140mm3a.setIsBuyOn(preBuyFlag);
		String checkCaseIs72_2 = this.checkCaseIs72_2(l140mm3a, true);
		l140mm3a.setIs722OnFlag(checkCaseIs72_2);
		l140mm3a.setIs722QDate(CapDate.parseDate(toDayStr));
		l140mm3a.setCntrNoChkExistFlag(returnMap.get("cntrNoChkExistFlag"));
		l140mm3a.setIsBuyOn(returnMap.get("isBuyOn"));
		l140mm3a.setIsInstalmentOn(returnMap.get("isInstalmentOn"));
		// 約定融資額度註記, 抓ELF506資料
		l140mm3a.setExceptFlagOn(returnMap.get("exceptFlagOn"));
		l140mm3a.setExceptFlagQAisYOn(returnMap.get("exceptFlagQAisYOn"));
		l140mm3a.setExceptFlagQAPlusOn(returnMap.get("exceptFlagQAPlusOn"));

		// J-111-0633_05097_B1001 Web
		// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
		// l140mm3a.setLnType(returnMap.get("prodKind"));
		// l140mm3a.setAdcCaseNo(returnMap.get("adcCaseNo"));
		l140mm3aDao.save(l140mm3a);

		returnMap.put("is722OnFlag", checkCaseIs72_2);
		returnMap.put("is722CntrNo", cntrNo);
		returnMap.put("is722QDate", toDayStr);

		return returnMap;
	}

	/*
	 * LMSServiceImpl.checkCaseIs72_2()也要改
	 */
	@Override
	public String checkCaseIs72_2(L140MM3A meta, boolean isPrevious) {
		String is722 = "";
		String isBuy = "";

		if (isPrevious) {
			isBuy = meta.getIsBuyOn();
		} else {
			isBuy = meta.getIsBuy();
			meta.setExItem("");
		}

		if ("Y".equals(isBuy)) {
			List<L140MM3C> datas = null;
			if (isPrevious) {
				datas = l140mm3cDao.findLastByMainId(meta.getMainId());
			} else {
				datas = l140mm3cDao.findCurrentByMainId(meta.getMainId());
			}

			if (datas != null && datas.size() > 0) {
				for (L140MM3C data : datas) {
					if (!isPrevious) {
						meta.setExItem(data.getEstateType());
					}

					String checkYN = data.getCheckYN();
					// 資料如果輸入未完整，就不做檢核
					if ("N".equals(checkYN)) {
						return "";
					}
				}

				for (L140MM3C data : datas) {
					if (UtilConstants.L140M01T_estatType.都更危老.equals(data
							.getEstateType())) {
						// 重建類型001
						// 只要不是都更，危老，都屬72-2
						if (UtilConstants.L140M01T_estatSubType.一般.equals(data
								.getEstateSubType())
								|| UtilConstants.L140M01T_estatSubType.其它都更危老
										.equals(data.getEstateSubType())) {
							is722 = "Y";
						} else if (UtilConstants.L140M01T_estatSubType.危老
								.equals(data.getEstateSubType())) {
							// 2.已送件未核定 3.已核定 4.已取得建照
							String estateStatus = data.getEstateStatus();
							// J-110-0054_10702_B1001 Web
							// e-Loan額度明細表不動產暨72-2相關註記修改
							String estateOwner = Util.trim(data
									.getEstateOwner());
							if (Util.equals(estateOwner,
									UtilConstants.DEFAULT.否)) {
								is722 = "Y";
							} else {
								if (("3".equals(estateStatus) || "4"
										.equals(estateStatus))
										&& Util.equals(estateOwner,
												UtilConstants.DEFAULT.是)) {
									is722 = "N";
								} else if ("3".equals(estateStatus)
										|| "4".equals(estateStatus)) {

									is722 = "N";
								} else {
									is722 = "Y";
								}
							}
						} else if (UtilConstants.L140M01T_estatSubType.都更
								.equals(data.getEstateSubType())) {
							// 3.已核定 4.已取得建照
							String estateStatus = data.getEstateStatus();
							// J-109-0248_05097_B1001 Web
							// e-Loan授信都更之計畫進度，將已送件未核定納入排除72-2項目
							if ("2".equals(estateStatus)
									|| "3".equals(estateStatus)
									|| "4".equals(estateStatus)) {

								is722 = "N";
							} else {
								is722 = "Y";
							}
						}
					} else {
						// 其它項都不屬72-2
						// 全部資料只要有一項不屬722，則全部都不是722
						// 所以就不需再判斷其它資料了
						is722 = "N";
						break;
					}
				}
			} else {
				is722 = "Y";
			}

		} else if ("N".equals(isBuy)) {
			is722 = "N";
		} else {
			is722 = "";
		}
		return is722;
	}

	@Override
	public void deleteCurrentL140mm3cs(String mainId) {
		List<L140MM3C> datas = l140mm3cDao.findCurrentByMainId(mainId);
		if (datas != null && datas.size() > 0) {
			for (L140MM3C data : datas) {
				this.deleteL140mm3cAndFile(data.getOid());
			}
		}
	}

	@Override
	public void deleteL140mm3cAndFile(String oid) {
		L140MM3C data = l140mm3cDao.find(oid);
		if (data != null) {
			String estateType = "estateType" + Util.trim(data.getEstateType());

			List<DocFile> docs = docFileService.findByIDAndName(
					data.getMainId(), estateType, "");

			if (docs != null && docs.size() > 0) {
				for (DocFile doc : docs) {
					docFileService.clean(doc.getOid());
				}
			}
			l140mm3cDao.delete(data);
		}
	}

	@Override
	public List<L140MM3C> findCurrentL140mm3cs(String mainId) {
		return l140mm3cDao.findCurrentByMainId(mainId);
	}

	@Override
	public List<L140MM3C> findLastL140mm3cs(String mainId) {
		return l140mm3cDao.findLastByMainId(mainId);
	}

	@Override
	public void saveL140mm3cList(List<L140MM3C> list) {
		l140mm3cDao.save(list);
	}

	@Override
	public List<L140MM3C> findL140mm3csByMainId(String mainId) {
		return l140mm3cDao.findByMainId(mainId);
	}

	@Override
	public Map<String, String> getData(L140MM3A l140mm3a) throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String toDayStr = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		String mainId = l140mm3a.getMainId();
		String cntrNo = l140mm3a.getCntrNo();
		returnMap.put("mainId", mainId);

		if (Util.equals(cntrNo, "")) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			// lmsL120M01A.error006=額度明細表額度序號不得為空白
			throw new CapMessageException(
					pop.getProperty("lmsL120M01A.error006"), getClass());

		}

		returnMap.put("cntrNoChkExistFlag", l140mm3a.getCntrNoChkExistFlag());
		returnMap
				.put("cntrNoChkExistDate",
						(Util.equals(Util.nullToSpace(l140mm3a
								.getCntrNoChkExistDate()), "") ? "" : CapDate
								.formatDate(l140mm3a.getCntrNoChkExistDate(),
										UtilConstants.DateFormat.YYYY_MM_DD)));
		returnMap.put("is722OnFlag", l140mm3a.getIs722OnFlag());
		returnMap.put("is722CntrNo", l140mm3a.getCntrNo());
		returnMap
				.put("is722QDate",
						(Util.equals(
								Util.nullToSpace(l140mm3a.getIs722QDate()), "") ? ""
								: CapDate.formatDate(l140mm3a.getIs722QDate(),
										UtilConstants.DateFormat.YYYY_MM_DD)));
		returnMap.put("isBuyOn", l140mm3a.getIsBuyOn());
		returnMap.put("isInstalmentOn", l140mm3a.getIsInstalmentOn());

		returnMap.put("is722Flag", l140mm3a.getIs722Flag());
		returnMap.put("isBuy", l140mm3a.getIsBuy());
		returnMap.put("isInstalment", l140mm3a.getIsInstalment());

		return returnMap;
	}

	@Override
	public boolean deleteL140mm3as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140MM3A> l140mm3as = new ArrayList<L140MM3A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L140MM3A l140mm3a = (L140MM3A) findModelByOid(L140MM3A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l140mm3a.setDeletedTime(CapDate.getCurrentTimestamp());
			l140mm3a.setUpdater(user.getUserId());
			l140mm3as.add(l140mm3a);
			docLogService.record(l140mm3a.getOid(), DocLogEnum.DELETE);
		}
		if (!l140mm3as.isEmpty()) {
			l140mm3aDao.save(l140mm3as);
			flag = true;
		}
		return flag;
	}

	@Override
	public void deleteL140mm3bs(List<L140MM3B> l140mm3bs, boolean isAll) {
		if (isAll) {
			l140mm3bDao.delete(l140mm3bs);
		} else {
			List<L140MM3B> L140MM3BsOld = new ArrayList<L140MM3B>();
			for (L140MM3B l140mm3b : l140mm3bs) {
				String staffJob = l140mm3b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L140MM3BsOld.add(l140mm3b);
				}
			}
			l140mm3bDao.delete(L140MM3BsOld);
		}

	}

	@Override
	public void saveL140mm3bList(List<L140MM3B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L140MM3B l140mm3b : list) {
			l140mm3b.setUpdater(user.getUserId());
			l140mm3b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l140mm3bDao.save(list);
	}

	@Override
	public L140MM3B findL140mm3b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l140mm3bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}

	@Override
	public void flowAction(String mainOid, L140MM3A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable {
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS7500Flow",
						((L140MM3A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					// save((L140MM3A) model);
					if (upMis) {

						L140MM3A l140mm3a = (L140MM3A) findModelByOid(
								L140MM3A.class, mainOid);
						if (Util.equals(l140mm3a.getUpdateItem2(), "Y") || Util.equals(l140mm3a.getUpdateItem5(), "Y")) {
							this.upElf506(l140mm3a);
						}
						if (Util.equals(l140mm3a.getUpdateItem3(), "Y")) {
							this.upEllnseek(l140mm3a);
						}

						// J-111-0633_05097_B1001 Web
						// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
						if (Util.equals(l140mm3a.getUpdateItem4(), "Y")) {
							this.upAdcInfo(l140mm3a);
						}

					} // upMis
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public L140MM3C getBuildInfoByMcntrNo(String mCntrNo) {
		ELF515 elf515 = misELF515Service.findMcntrNo(mCntrNo);
		L140MM3C l140mm3c = null;
		if (elf515 != null) {
			l140mm3c = new L140MM3C();
			l140mm3c.setEstateType(elf515.getElf515_type());
			l140mm3c.setEstateSubType(elf515.getElf515_sub_type1());
			l140mm3c.setEstateStatus(elf515.getElf515_sub_type2());
			l140mm3c.setEstateCityId(elf515.getElf515_site1());
			l140mm3c.setEstateAreaId(elf515.getElf515_site2());
			l140mm3c.setEstateSit3No(elf515.getElf515_site3());
			l140mm3c.setEstateSit4No(elf515.getElf515_site4());
			l140mm3c.setBuildWay(elf515.getElf515_sub_type4());
			l140mm3c.setmCntrNo(mCntrNo);
			l140mm3c.setSubTypeNote(elf515.getElf515_data1());
			l140mm3c.setEstateNote(elf515.getElf515_data2());
			l140mm3c.setLandlordNum(NumberUtils.isNumber(elf515
					.getElf515_data3()) ? CapMath.getBigDecimal(elf515
					.getElf515_data3()) : null);
			l140mm3c.setOverDate(CapDate.parseDate(elf515.getElf515_sub_type3()));
			l140mm3c.setOtherDesc(elf515.getElf515_data4());//J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
		}

		return l140mm3c;
	}

	private ELF515 convertELF515(L140MM3C l140mm3c, String cntrNo,
			MegaSSOUserDetails user) {

		String estateType = l140mm3c.getEstateType();
		String isTurnOver = l140mm3c.getIsTurnOver();

		ELF515 elf515 = new ELF515();
		elf515.setElf515_cntrno(cntrNo);
		elf515.setElf515_type(estateType);
		elf515.setElf515_instalment(isTurnOver);

		if (UtilConstants.L140M01T_estatType.都更危老.equals(estateType)) {
			String estateSubType = Util.trim(l140mm3c.getEstateSubType());

			elf515.setElf515_sub_type1(estateSubType);

			if (UtilConstants.L140M01T_estatSubType.其它都更危老
					.equals(estateSubType)
					|| UtilConstants.L140M01T_estatSubType.危老
							.equals(estateSubType)
					|| UtilConstants.L140M01T_estatSubType.都更
							.equals(estateSubType)) {
				elf515.setElf515_site1(l140mm3c.getEstateCityId());
				elf515.setElf515_site2(l140mm3c.getEstateAreaId());
				elf515.setElf515_site3(l140mm3c.getEstateSit3No());
				elf515.setElf515_site4(l140mm3c.getEstateSit4No());
				elf515.setElf515_cntrno_m(l140mm3c.getmCntrNo());
				elf515.setElf515_sub_type2(l140mm3c.getEstateStatus());
				elf515.setElf515_sub_type3(CapDate.formatDate(
						l140mm3c.getOverDate(), "yyyy-MM-dd"));
				elf515.setElf515_sub_type4(l140mm3c.getBuildWay());
				elf515.setElf515_sub_type5(l140mm3c.getEstateOwner());
				elf515.setElf515_data1(l140mm3c.getSubTypeNote());
				elf515.setElf515_data2(l140mm3c.getEstateNote());
				elf515.setElf515_data3(Util.trim(l140mm3c.getLandlordNum()));
				elf515.setElf515_data4(Util.trim(l140mm3c.getOtherDesc()));//J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
			}
		} else if (UtilConstants.L140M01T_estatType.公私立各級學校.equals(estateType)) {

			elf515.setElf515_inter_outer(l140mm3c.getPosition());
			elf515.setElf515_data1(l140mm3c.getSubject());
			elf515.setElf515_site1(l140mm3c.getEstateCityId());
			elf515.setElf515_site2(l140mm3c.getEstateAreaId());
			elf515.setElf515_site3(l140mm3c.getEstateSit3No());
			elf515.setElf515_site4(l140mm3c.getEstateSit4No());
			elf515.setElf515_address(l140mm3c.getSiteNote());

			elf515.setElf515_build_state(l140mm3c.getEstateStatus());
			elf515.setElf515_build_date(new java.sql.Date(l140mm3c
					.getOverDate().getTime()));

		} else if (UtilConstants.L140M01T_estatType.醫療機構.equals(estateType)) {

			elf515.setElf515_inter_outer(l140mm3c.getPosition());
			elf515.setElf515_data1(l140mm3c.getSubject());
			elf515.setElf515_data2(l140mm3c.getSubjectCode());
			elf515.setElf515_site1(l140mm3c.getEstateCityId());
			elf515.setElf515_site2(l140mm3c.getEstateAreaId());
			elf515.setElf515_site3(l140mm3c.getEstateSit3No());
			elf515.setElf515_site4(l140mm3c.getEstateSit4No());
			elf515.setElf515_address(l140mm3c.getSiteNote());

			elf515.setElf515_build_state(l140mm3c.getEstateStatus());
			elf515.setElf515_build_date(new java.sql.Date(l140mm3c
					.getOverDate().getTime()));

		} else if (UtilConstants.L140M01T_estatType.政府廳舍.equals(estateType)) {

			elf515.setElf515_inter_outer(l140mm3c.getPosition());
			elf515.setElf515_data1(l140mm3c.getSubject());
			elf515.setElf515_data2(l140mm3c.getSubjectKind());
			elf515.setElf515_site1(l140mm3c.getEstateCityId());
			elf515.setElf515_site2(l140mm3c.getEstateAreaId());
			elf515.setElf515_site3(l140mm3c.getEstateSit3No());
			elf515.setElf515_site4(l140mm3c.getEstateSit4No());
			elf515.setElf515_address(l140mm3c.getSiteNote());

			elf515.setElf515_build_state(l140mm3c.getEstateStatus());
			elf515.setElf515_build_date(new java.sql.Date(l140mm3c
					.getOverDate().getTime()));

		} else if (UtilConstants.L140M01T_estatType.長照服務機構.equals(estateType)) {

			elf515.setElf515_inter_outer(l140mm3c.getPosition());
			elf515.setElf515_data1(l140mm3c.getSubject());
			elf515.setElf515_data2(l140mm3c.getSubjectKind());
			elf515.setElf515_site1(l140mm3c.getEstateCityId());
			elf515.setElf515_site2(l140mm3c.getEstateAreaId());
			elf515.setElf515_site3(l140mm3c.getEstateSit3No());
			elf515.setElf515_site4(l140mm3c.getEstateSit4No());
			elf515.setElf515_address(l140mm3c.getSiteNote());

			elf515.setElf515_build_state(l140mm3c.getEstateStatus());
			elf515.setElf515_build_date(new java.sql.Date(l140mm3c
					.getOverDate().getTime()));

		} else if (UtilConstants.L140M01T_estatType.社會住宅.equals(estateType)) {

			elf515.setElf515_inter_outer(l140mm3c.getPosition());
			elf515.setElf515_data1(l140mm3c.getSubject());
			elf515.setElf515_site1(l140mm3c.getEstateCityId());
			elf515.setElf515_site2(l140mm3c.getEstateAreaId());
			elf515.setElf515_site3(l140mm3c.getEstateSit3No());
			elf515.setElf515_site4(l140mm3c.getEstateSit4No());
			elf515.setElf515_address(l140mm3c.getSiteNote());

			elf515.setElf515_build_state(l140mm3c.getEstateStatus());
			elf515.setElf515_build_date(new java.sql.Date(l140mm3c
					.getOverDate().getTime()));

		} else if (UtilConstants.L140M01T_estatType.A0井.equals(estateType)) {

			elf515.setElf515_inter_outer(l140mm3c.getPosition());
			elf515.setElf515_build_state(l140mm3c.getEstateStatus());
			elf515.setElf515_data1(l140mm3c.getSubjectCode());
			elf515.setElf515_site1(l140mm3c.getEstateCityId());
			elf515.setElf515_site2(l140mm3c.getEstateAreaId());
			elf515.setElf515_site3(l140mm3c.getEstateSit3No());
			elf515.setElf515_site4(l140mm3c.getEstateSit4No());
			elf515.setElf515_address(l140mm3c.getSiteNote());
			elf515.setElf515_sub_type2(l140mm3c.getSectKind());
			elf515.setElf515_sub_type3(l140mm3c.getUseSect());
			elf515.setElf515_sub_type4(l140mm3c.getUseKind());

			elf515.setElf515_build_state(l140mm3c.getEstateStatus());
			elf515.setElf515_build_date(new java.sql.Date(l140mm3c
					.getOverDate().getTime()));

		}

		elf515.setElf515_empl_no(l140mm3c.getUpdater());
		elf515.setElf515_supv_no(user.getUserId());
		elf515.setElf515_createUnit("FMS");
		elf515.setElf515_modifyUnit("FMS");
		elf515.setElf515_createTime(CapDate.getCurrentTimestamp());
		elf515.setElf515_modifyTime(CapDate.getCurrentTimestamp());

		return elf515;
	}

	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}

	/**
	 * J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
	 * 
	 * 暴險註記
	 * 
	 * @param l140mm3a
	 * @return
	 * @throws CapException
	 */
	@Override
	public Map<String, String> getEllnseekData(L140MM3A l140mm3a,
			boolean needSave) throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String toDayStr = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		String mainId = l140mm3a.getMainId();
		String cntrNo = l140mm3a.getCntrNo();
		String custId = l140mm3a.getCustId();
		String dupNo = l140mm3a.getDupNo();
		returnMap.put("mainId", mainId);

		if (Util.equals(cntrNo, "")) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			// lmsL120M01A.error006=額度明細表額度序號不得為空白
			throw new CapMessageException(
					pop.getProperty("lmsL120M01A.error006"), getClass());

		}

		// 1.取得前次ELOAN 註記資料

		List<Map<String, Object>> ellnseeks = misEllnseekservice.findByKey(
				custId, dupNo, cntrNo);
		if (ellnseeks != null && !ellnseeks.isEmpty()) {
			for (Map<String, Object> ellnseek : ellnseeks) {
				returnMap.put("isSpecialFinRisk",
						Util.trim(MapUtils.getString(ellnseek, "ISSPEFIN")));
				returnMap.put("specialFinRiskType",
						Util.trim(MapUtils.getString(ellnseek, "SPEFINTYPE")));
				returnMap.put("isCmsAdcRisk",
						Util.trim(MapUtils.getString(ellnseek, "ISADC")));
				returnMap.put("isProjectFinOperateStag",
						Util.trim(MapUtils.getString(ellnseek, "ISPROJOP")));
				returnMap.put("isSpecialFinRiskOn",
						Util.trim(MapUtils.getString(ellnseek, "ISSPEFIN")));
				returnMap.put("specialFinRiskTypeOn",
						Util.trim(MapUtils.getString(ellnseek, "SPEFINTYPE")));
				returnMap.put("isCmsAdcRiskOn",
						Util.trim(MapUtils.getString(ellnseek, "ISADC")));
				returnMap.put("isProjectFinOperateStagOn",
						Util.trim(MapUtils.getString(ellnseek, "ISPROJOP")));
				//J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位
				returnMap.put("isHighQualityProjOpt_1On", Util.trim(MapUtils.getString(ellnseek, "HQPROJ_OPT1")));
				returnMap.put("isHighQualityProjOpt_2On", Util.trim(MapUtils.getString(ellnseek, "HQPROJ_OPT2")));
				returnMap.put("isHighQualityProjOpt_3On", Util.trim(MapUtils.getString(ellnseek, "HQPROJ_OPT3")));
				returnMap.put("isHighQualityProjOpt_4On", Util.trim(MapUtils.getString(ellnseek, "HQPROJ_OPT4")));
				returnMap.put("isHighQualityProjOpt_5On", Util.trim(MapUtils.getString(ellnseek, "HQPROJ_OPT5")));
				returnMap.put("isHighQualityProjResultOn", Util.trim(MapUtils.getString(ellnseek, "HQPROJ_RES")));
				
				break;
			}
		}

		if (needSave) {
			l140mm3a.setIsSpecialFinRisk(returnMap.get("isSpecialFinRisk"));
			l140mm3a.setSpecialFinRiskType(returnMap.get("specialFinRiskType"));
			l140mm3a.setIsCmsAdcRisk(returnMap.get("isCmsAdcRisk"));
			l140mm3a.setIsProjectFinOperateStag(returnMap
					.get("isProjectFinOperateStag"));
			l140mm3a.setIsSpecialFinRiskOn(returnMap.get("isSpecialFinRiskOn"));
			l140mm3a.setSpecialFinRiskTypeOn(returnMap
					.get("specialFinRiskTypeOn"));
			l140mm3a.setIsCmsAdcRiskOn(returnMap.get("isCmsAdcRiskOn"));
			l140mm3a.setIsProjectFinOperateStagOn(returnMap
					.get("isProjectFinOperateStagOn"));
			//J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位
			l140mm3a.setIsHighQualityProjOpt_1(returnMap.get("isHighQualityProjOpt_1"));
			l140mm3a.setIsHighQualityProjOpt_2(returnMap.get("isHighQualityProjOpt_2"));
			l140mm3a.setIsHighQualityProjOpt_3(returnMap.get("isHighQualityProjOpt_3"));
			l140mm3a.setIsHighQualityProjOpt_4(returnMap.get("isHighQualityProjOpt_4"));
			l140mm3a.setIsHighQualityProjOpt_5(returnMap.get("isHighQualityProjOpt_5"));
			l140mm3a.setIsHighQualityProjResult(returnMap.get("isHighQualityProjResult"));
			//前案
			l140mm3a.setIsHighQualityProjOpt_1On(returnMap.get("isHighQualityProjOpt_1On"));
			l140mm3a.setIsHighQualityProjOpt_2On(returnMap.get("isHighQualityProjOpt_2On"));
			l140mm3a.setIsHighQualityProjOpt_3On(returnMap.get("isHighQualityProjOpt_3On"));
			l140mm3a.setIsHighQualityProjOpt_4On(returnMap.get("isHighQualityProjOpt_4On"));
			l140mm3a.setIsHighQualityProjOpt_5On(returnMap.get("isHighQualityProjOpt_5On"));
			l140mm3a.setIsHighQualityProjResultOn(returnMap.get("isHighQualityProjResultOn"));
			
			l140mm3aDao.save(l140mm3a);
		}

		return returnMap;
	}

	/**
	 * J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * ADC號碼補建或變更
	 * 
	 * @param l140mm3a
	 * @return
	 * @throws CapException
	 */
	@Override
	public Map<String, String> getAdcInfo(L140MM3A l140mm3a, boolean needSave)
			throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String toDayStr = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		String mainId = l140mm3a.getMainId();
		String cntrNo = l140mm3a.getCntrNo();
		String custId = l140mm3a.getCustId();
		String dupNo = l140mm3a.getDupNo();
		returnMap.put("mainId", mainId);

		if (Util.equals(cntrNo, "")) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			// lmsL120M01A.error006=額度明細表額度序號不得為空白
			throw new CapMessageException(
					pop.getProperty("lmsL120M01A.error006"), getClass());

		}

		String prodKind = ""; // 產品種類 33.土地融資 34.建築融資
		String adcCaseNo = ""; // ADC案件編號
		
		String exceptFlagOn = "";
		String exceptFlagQAisYOn = "";
		String exceptFlagQAPlusOn = "";

		// 1.取得前次ELOAN 註記資料
		Map<String, Object> elf506Map = misELF506Service.getByCntrNo(cntrNo);
		if (elf506Map != null && !elf506Map.isEmpty()) {

			prodKind = Util.trim(elf506Map.get("ELF506_PROD_KIND"));
			adcCaseNo = Util.trim(elf506Map.get("ELF506_ADC_CASENO"));
			exceptFlagOn = Util.trim(elf506Map.get("ELF506_EXCEPT"));
			exceptFlagQAisYOn = Util.trim(elf506Map.get("ELF506_EX_QA_Y"));
			exceptFlagQAPlusOn = Util.trim(elf506Map.get("ELF506_EX_QA_PLUS"));
			
			returnMap.put("lnType", prodKind);
			returnMap.put("adcCaseNo", adcCaseNo);
			returnMap.put("lnTypeOn", prodKind);
			returnMap.put("adcCaseNoOn", adcCaseNo);
			
			returnMap.put("exceptFlagOn", exceptFlagOn);
			returnMap.put("getExceptFlagQAisYOn", exceptFlagQAisYOn);
			returnMap.put("exceptFlagQAPlusOn", exceptFlagQAPlusOn);
		}

		// ELF506 無資料 再去撈 ELF447N
		if (Util.isEmpty(prodKind)) {
			List<Map<String, Object>> elf447nList = misELF447nService
					.getByCntrNoOrderByEndDateDesc(cntrNo);
			if (elf447nList != null && !elf447nList.isEmpty()
					&& elf447nList.size() > 0) {
				Map<String, Object> elf447nDataMap = elf447nList.get(0);
				prodKind = Util.trim(elf447nDataMap.get("ELF447N_PROD_CLASS"));
				returnMap.put("lnType", prodKind);
				returnMap.put("lnTypeOn", prodKind);
			}
		}

		if (needSave) {
			l140mm3a.setLnType(prodKind);
			l140mm3a.setAdcCaseNo(adcCaseNo);
			l140mm3a.setLnTypeOn(prodKind);
			l140mm3a.setAdcCaseNoOn(adcCaseNo);
			l140mm3a.setExceptFlagOn(exceptFlagOn);
			l140mm3a.setExceptFlagQAisYOn(exceptFlagQAisYOn);
			l140mm3a.setExceptFlagQAPlusOn(exceptFlagQAPlusOn);
			l140mm3aDao.save(l140mm3a);
		}

		return returnMap;
	}

	/**
	 * J-109-0239_05097_B1001 Web
	 * e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
	 * 
	 * 判斷是否要顯示本案是否屬特殊融資暴險
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @return
	 * @throws CapException
	 */
	@Override
	public boolean needShowIsSpecialFinRisk(L140MM3A l140mm3a)
			throws CapException {
		boolean needFinRisk = false;
		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		Map<String, Object> busCDMap = misCustdataService
				.findBUSCDByCustIdANdDupNo(l140mm3a.getCustId(),
						l140mm3a.getDupNo());
		String buscd = "";
		if (busCDMap != null) {
			// 行業別代碼
			buscd = Util.trim(busCDMap.get("BUSCD"));
		}

		if (Util.equals(buscd, "")) {
			throw new CapMessageException("0024無該借款人行業對象別資訊", getClass());
		}

		if (Util.equals(buscd, "060000") || Util.equals(buscd, "130300")) {
			needFinRisk = false;
		} else {
			// 本案是否屬特殊融資暴險
			needFinRisk = true;
		}

		return needFinRisk;

	}

	public void upElf506(L140MM3A l140mm3a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 動審表簽章欄檔取得人員職稱
		List<L140MM3B> l140mm3blist = l140mm3bDao.findByMainId(l140mm3a
				.getMainId());
		String apprId = "";
		String reCheckId = "";

		for (L140MM3B l140mm3b : l140mm3blist) {
			String StaffJob = Util.trim(l140mm3b.getStaffJob());// 取得人員職稱
			String StaffNo = Util.trim(l140mm3b.getStaffNo());// 取得行員代碼
			if (Util.equals(StaffJob, "L1")) {// 分行經辦
				apprId = StaffNo;
			} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
				reCheckId = StaffNo;
			}
		}
		// 若人員職稱為空值改取c160m01a上的人員資料
		if (Util.isEmpty(apprId)) {
			apprId = l140mm3a.getUpdater();
		}
		if (Util.isEmpty(reCheckId)) {
			reCheckId = l140mm3a.getApprover();
		}

		Map<String, String> createUnitMap = new HashMap<String, String>();
		Map<String, Timestamp> createTimeMap = new HashMap<String, Timestamp>();

		String mainId = l140mm3a.getMainId();
		String brnId = Util.getLeftStr(l140mm3a.getCntrNo(), 3);
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brnId).getBrNoFlag());
		String cntrNo = l140mm3a.getCntrNo();
		String is722Flag = l140mm3a.getIs722Flag();
		String exceptFlag = Util.trim(l140mm3a.getExceptFlag());
		exceptFlag = "_".equals(exceptFlag) ? "" : exceptFlag;
		String exceptFlagQAisY = Util.trim(l140mm3a.getExceptFlagQAisY());
		String exceptFlagQAPlus = Util.trim(l140mm3a.getExceptFlagQAPlus());
		boolean isUpdate722Data = "Y".equals(l140mm3a.getUpdateItem2()) ? true : false;
		boolean isUpdateFinNotesAgreed = "Y".equals(l140mm3a.getUpdateItem5()) ? true : false;
		
		String modUnit = "";
		String isBuy = "";
		String isInstalment = "";
		String exItem = "";
		String prodKind = "";
		String adcCaseNo = "";
		String custIdDupNo = (l140mm3a.getCustId() + "          ").substring(0, 10) + l140mm3a.getDupNo();
		
		if (isUpdate722Data && Util.notEquals(is722Flag, "")) {
			
			modUnit = "ELOAN";
			isBuy = Util.trim(l140mm3a.getIsBuy());
			isInstalment = Util.trim(l140mm3a.getIsInstalment());
			exItem = Util.trim(l140mm3a.getExItem());
			// J-111-0633_05097_B1001 Web
			// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			// adcCaseNo = Util.nullToSpace(l140mm3a.getAdcCaseNo());
			// prodKind = Util.nullToSpace(l140mm3a.getLnType());
		}

		String busCode = lmsService.get_0024_busCd(
				Util.trim(StringUtils.substring(custIdDupNo, 0, 10)),
				Util.trim(StringUtils.substring(custIdDupNo, 10, 11)));
		if (LMSUtil.isBusCode_060000_130300(busCode) && !isOverSea) {
			isBuy = ""; // 個人國內 放空白 除外Y/N
		}

		Timestamp modTime = new Timestamp(System.currentTimeMillis());
		String sDate = CapDate.formatDate(l140mm3a.getApproveTime(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		Map<String, Object> elf506 = misELF506Service.getByCntrNo(cntrNo);
		if (elf506 != null && !elf506.isEmpty()) {
			
			// 本次異動為空白，取ELF506前一次的資料，再寫回去
			if(!isUpdateFinNotesAgreed){
				exceptFlag = Util.trim(elf506.get("ELF506_EXCEPT"));
				exceptFlagQAisY = Util.trim(elf506.get("ELF506_EX_QA_Y"));
				exceptFlagQAPlus = Util.trim(elf506.get("ELF506_EX_QA_PLUS"));
			}
			
			// update
			if (!isUpdate722Data) {
				// 若本次簽案為空白，ELF506可能有前次報案資料
				// 要用之前的ELF506來取代
				// 先把72-2相關資料讀出來再塞回去
				custIdDupNo = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_CUST_ID"));
				is722Flag = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_722_FLAG"));
				modUnit = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_722_MODUNIT"));
				modTime = (Timestamp) MapUtils.getObject(elf506,
						"ELF506_722_MODTIME");
				sDate = CapDate.formatDate(
						(Date) MapUtils.getObject(elf506, "ELF506_722_SDATE"),
						UtilConstants.DateFormat.YYYY_MM_DD);
				isBuy = Util.trim(MapUtils.getString(elf506,
						"ELF506_722_IS_BUY"));
				exItem = Util.trim(MapUtils.getString(elf506,
						"ELF506_722_EX_ITEM"));
				isInstalment = Util.trim(MapUtils.getString(elf506, "ELF506_INSTALMENT"));
			}

			// J-111-0633_05097_B1001 Web
			// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			// 33 34 再回壓 空白就不用了(保持原資料)
			prodKind = Util
					.trim(MapUtils.getString(elf506, "ELF506_PROD_KIND"));
			adcCaseNo = Util.trim(MapUtils.getString(elf506,
					"ELF506_ADC_CASENO"));

			String createUnit = MapUtils.getString(elf506, "ELF506_CREATEUNIT");
			Timestamp createTime = (Timestamp) MapUtils.getObject(elf506,
					"ELF506_CREATETIME");

			createUnitMap.put(cntrNo, createUnit);
			createTimeMap.put(cntrNo, createTime);

			String cnLoanFg = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_CN_LOAN_FG"));
			String directFg = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_DIRECT_FG"));
			String stRadeFg = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_S_TRADE_FG"));
			BigDecimal guar1Rate = (BigDecimal) MapUtils.getObject(elf506,
					"ELF506_GUAR1_RATE", BigDecimal.ZERO);
			BigDecimal guar2Rate = (BigDecimal) MapUtils.getObject(elf506,
					"ELF506_GUAR2_RATE", BigDecimal.ZERO);
			BigDecimal guar3Rate = (BigDecimal) MapUtils.getObject(elf506,
					"ELF506_GUAR3_RATE", BigDecimal.ZERO);
			BigDecimal coll1Rate = (BigDecimal) MapUtils.getObject(elf506,
					"ELF506_COLL1_RATE", BigDecimal.ZERO);
			BigDecimal coll2Rate = (BigDecimal) MapUtils.getObject(elf506,
					"ELF506_COLL2_RATE", BigDecimal.ZERO);
			BigDecimal coll3Rate = (BigDecimal) MapUtils.getObject(elf506,
					"ELF506_COLL3_RATE", BigDecimal.ZERO);
			BigDecimal coll4Rate = (BigDecimal) MapUtils.getObject(elf506,
					"ELF506_COLL4_RATE", BigDecimal.ZERO);
			BigDecimal coll5Rate = (BigDecimal) MapUtils.getObject(elf506,
					"ELF506_COLL5_RATE", BigDecimal.ZERO);
			String documentNo = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_DOCUMENT_NO"));
			// 如果是 企金簽案的個人額度序號 isBuy不清
			if (StringUtils.indexOf(documentNo, "CLS") >= 0) {
				// 消金
			} else {
				if (isUpdate722Data && Util.notEquals(is722Flag, "")) {
					isBuy = Util.trim(l140mm3a.getIsBuy());
				}
			}
			String iGolFlag = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_IGOL_FLAG"));
			String cnTMUFg = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_CN_TMU_FG"));
			String cnBusKind = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_CN_BUS_KIND"));
			String docNo = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_722_DOC_NO"));
			String loanTarget = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_LOAN_TARGET"));
			String isType = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_IS_TYPE"));
			String grntType = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_GRNT_TYPE"));
			String grntClass = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_GRNT_CLASS"));
			String othCrdType = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_OTHCRD_TYPE"));
			String unionArea3 = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_UNION_AREA3"));
			String nCnSblcFg = (String) Util.trim(MapUtils.getObject(elf506,
					"ELF506_NCN_SBLC_FG"));

			misELF506Service.delete(cntrNo);

			misELF506Service.insert(cntrNo, cnLoanFg, directFg, stRadeFg,
					guar1Rate, guar2Rate, guar3Rate, coll1Rate, coll2Rate,
					coll3Rate, coll4Rate, coll5Rate,
					new Timestamp(System.currentTimeMillis()), createTime,
					createUnit, "ELOAN", documentNo, iGolFlag, cnTMUFg,
					cnBusKind, custIdDupNo, is722Flag, modUnit, docNo, modTime,
					sDate, isBuy, exItem, loanTarget, isType, grntType,
					grntClass, othCrdType, unionArea3, nCnSblcFg, isInstalment,
					prodKind, adcCaseNo, exceptFlag, exceptFlagQAisY, exceptFlagQAPlus);
		} else {
			// insert
			misELF506Service.insert(cntrNo, "", "", "", BigDecimal.ZERO,
					BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
					BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
					BigDecimal.ZERO, new Timestamp(System.currentTimeMillis()),
					new Timestamp(System.currentTimeMillis()), "ELOAN",
					"ELOAN", "", "", "", "", custIdDupNo, is722Flag, modUnit,
					"", modTime, sDate, isBuy, exItem, "", "", "", "", "", "",
					"", isInstalment, prodKind, adcCaseNo, exceptFlag, exceptFlagQAisY, 
					exceptFlagQAPlus);
		}

		// 72-2值有填才上傳
		if (isUpdate722Data && Util.isNotEmpty(is722Flag)) {
			List<L140MM3C> l140mm3cs = l140mm3cDao.findCurrentByMainId(mainId);
			// 都delete 再insert
			misELF515Service.delete(l140mm3a.getCntrNo());
			if (l140mm3cs != null && l140mm3cs.size() > 0) {
				for (L140MM3C l140mm3c : l140mm3cs) {
					ELF515 elf515 = this.convertELF515(l140mm3c, cntrNo, user);
					misELF515Service.insert(elf515);
				}
			}
		}

		// 上傳AS400*************************************
		// 額度序號前三碼為海外分行時
		if (isOverSea) {

			String createUnit = null;
			BigDecimal createTime = null;
			if (createUnitMap.containsKey(cntrNo)) {
				createUnit = createUnitMap.get(cntrNo);
			} else {
				createUnit = "ELOAN";
			}

			if (createTimeMap.containsKey(cntrNo)) {
				createTime = LMSUtil.covertAs400Time((Timestamp) createTimeMap
						.get(cntrNo));
			} else {
				createTime = LMSUtil.covertAs400Time(new Timestamp(System
						.currentTimeMillis()));
			}

			// ELF506
			Map<String, Object> elf506As400 = obsdbELF506Service.getByCntrNo(
					brnId, cntrNo);

			String exceptFlagOVS = Util.trim(l140mm3a.getExceptFlag());
			String exceptFlagQAisYOVS = Util.trim(l140mm3a.getExceptFlagQAisY());
			exceptFlagQAisYOVS = StringUtils.isNotBlank(exceptFlagQAisYOVS) ? StringUtils.leftPad(exceptFlagQAisYOVS, 2, "0") : exceptFlagQAisYOVS ;
			String exceptFlagQAPlusOVS = Util.trim(l140mm3a.getExceptFlagQAPlus());
			
			if (elf506As400 != null && !elf506As400.isEmpty()) {
				// update
				String custIdDupNoOVS = "";
				String is722FlagOVS = "";
				String modUnitOVS = "";
				String docNoOVS = "";
				BigDecimal modTimeOVS = BigDecimal.ZERO;
				BigDecimal sDateOVS = BigDecimal.ZERO;
				String isBuyOVS = "";
				String exItemOVS = "";

				String loanTarget = Util.trim(MapUtils.getString(elf506As400,
						"ELF506_LOAN_TARGET"));
				String isType = Util.trim(MapUtils.getString(elf506As400,
						"ELF506_IS_TYPE"));
				String grntType = Util.trim(MapUtils.getString(elf506As400,
						"ELF506_GRNT_TYPE"));
				String grntClass = Util.trim(MapUtils.getString(elf506As400,
						"ELF506_GRNT_CLASS"));
				String othCrdType = Util.trim(MapUtils.getString(elf506As400,
						"ELF506_OTHCRD_TYPE"));
				// String dfOthCrdTypeStr = "";
				// if (Util.equals(grntClass, "2")) {
				// dfOthCrdTypeStr = "NNNNNNNNNNNNNNNNNNNN";
				// } else {
				// dfOthCrdTypeStr = "                    ";
				// }
				// StringBuffer othCrdTypebuff = new
				// StringBuffer(
				// dfOthCrdTypeStr);
				// String othCrdType = dfOthCrdTypeStr;
				// if (Util.equals(grntClass, "2")) {
				// if (Util.notEquals(Util.trim(tOthCrdType),
				// "")) {
				// String[] othCrdTypeArr = tOthCrdType
				// .split("\\|");
				// for (String strOthCrdType : othCrdTypeArr) {
				// int index = Integer
				// .valueOf(strOthCrdType);
				// othCrdTypebuff.replace(index - 1,
				// index, "Y");
				// }
				// othCrdType = othCrdTypebuff.toString();
				// }
				// }
				String unionArea3 = Util.trim(MapUtils.getString(elf506As400,
						"ELF506_UNION_AREA3"));
				String cnLoanFg = Util.trim(MapUtils.getString(elf506As400,
						"ELF506_CN_LOAN_FG"));
				String directFg = (String) Util.trim(MapUtils.getObject(
						elf506As400, "ELF506_DIRECT_FG"));
				String stRadeFg = (String) Util.trim(MapUtils.getObject(
						elf506As400, "ELF506_S_TRADE_FG"));
				BigDecimal guar1Rate = (BigDecimal) MapUtils.getObject(
						elf506As400, "ELF506_GUAR1_RATE", BigDecimal.ZERO);
				BigDecimal guar2Rate = (BigDecimal) MapUtils.getObject(
						elf506As400, "ELF506_GUAR2_RATE", BigDecimal.ZERO);
				BigDecimal guar3Rate = (BigDecimal) MapUtils.getObject(
						elf506As400, "ELF506_GUAR3_RATE", BigDecimal.ZERO);
				BigDecimal coll1Rate = (BigDecimal) MapUtils.getObject(
						elf506As400, "ELF506_COLL1_RATE", BigDecimal.ZERO);
				BigDecimal coll2Rate = (BigDecimal) MapUtils.getObject(
						elf506As400, "ELF506_COLL2_RATE", BigDecimal.ZERO);
				BigDecimal coll3Rate = (BigDecimal) MapUtils.getObject(
						elf506As400, "ELF506_COLL3_RATE", BigDecimal.ZERO);
				BigDecimal coll4Rate = (BigDecimal) MapUtils.getObject(
						elf506As400, "ELF506_COLL4_RATE", BigDecimal.ZERO);
				BigDecimal coll5Rate = (BigDecimal) MapUtils.getObject(
						elf506As400, "ELF506_COLL5_RATE", BigDecimal.ZERO);
				String documentNo = (String) Util.trim(MapUtils.getObject(
						elf506As400, "ELF506_DOCUMENT_NO"));
				String iGolFlag = (String) Util.trim(MapUtils.getObject(
						elf506As400, "ELF506_IGOL_FLAG"));
				String cnTMUFg = (String) Util.trim(MapUtils.getObject(
						elf506As400, "ELF506_CN_TMU_FG"));
				String cnBusKind = (String) Util.trim(MapUtils.getObject(
						elf506As400, "ELF506_CN_BUS_KIND"));
				String docNo = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_722_DOC_NO"));
				String nCnSblcFg = (String) Util.trim(MapUtils.getObject(
						elf506, "ELF506_NCN_SBLC_FG"));

				// J-111-0633_05097_B1001 Web
				// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
				// 33 34 再回壓 空白就不用了(保持原資料)
				prodKind = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_PROD_KIND"));
				adcCaseNo = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_ADC_CASENO"));
				
				// 本次異動為空白，取ELF506前一次的資料，再寫回去
				if(!isUpdateFinNotesAgreed){
					exceptFlagOVS = Util.trim(elf506As400.get("ELF506_EXCEPT"));
					exceptFlagQAisYOVS = Util.trim(elf506As400.get("ELF506_EX_QA_Y"));
					exceptFlagQAPlusOVS = Util.trim(elf506As400.get("ELF506_EX_QA_PLUS"));
				}
				
				obsdbELF506Service.deleteByCntrNo(brnId, cntrNo);

				if (!isUpdate722Data) {
					custIdDupNoOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_CUST_ID"));
					is722FlagOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_FLAG"));
					modUnitOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_MODUNIT"));
					docNoOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_DOC_NO"));
					modTimeOVS = (BigDecimal) MapUtils.getObject(elf506As400,
							"ELF506_722_MODTIME");
					sDateOVS = (BigDecimal) MapUtils.getObject(elf506As400,
							"ELF506_722_SDATE");
					isBuyOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_IS_BUY"));
					exItemOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_EX_ITEM"));

					obsdbELF506Service.insert(brnId, cntrNo, cnLoanFg,
							directFg, stRadeFg, guar1Rate, guar2Rate,
							guar3Rate, coll1Rate, coll2Rate, coll3Rate,
							coll4Rate, coll5Rate, LMSUtil
									.covertAs400Time(new Timestamp(System
											.currentTimeMillis())), createTime,
							createUnit, "ELOAN", documentNo, iGolFlag, cnTMUFg,
							cnBusKind, custIdDupNoOVS, is722FlagOVS,
							modUnitOVS, docNoOVS, modTimeOVS, sDateOVS,
							isBuyOVS, exItemOVS, loanTarget, isType, grntType,
							grntClass, othCrdType, unionArea3, nCnSblcFg,
							isInstalment, prodKind, adcCaseNo, exceptFlagOVS, 
							exceptFlagQAisYOVS, exceptFlagQAPlusOVS);
				} else {
					obsdbELF506Service.insert(brnId, cntrNo, cnLoanFg,
							directFg, stRadeFg, guar1Rate, guar2Rate,
							guar3Rate, coll1Rate, coll2Rate, coll3Rate,
							coll4Rate, coll5Rate, LMSUtil
									.covertAs400Time(new Timestamp(System
											.currentTimeMillis())), createTime,
							"ELOAN", "ELOAN", documentNo, iGolFlag, cnTMUFg,
							cnBusKind, custIdDupNo, is722Flag, modUnit, docNo,
							LMSUtil.covertAs400Time(modTime), Util
									.parseBigDecimal(sDate
											.replaceAll("\\D", "")), isBuy,
							exItem, loanTarget, isType, grntType, grntClass,
							othCrdType, unionArea3, nCnSblcFg, isInstalment,
							prodKind, adcCaseNo, exceptFlagOVS, exceptFlagQAisYOVS, 
							exceptFlagQAPlusOVS);
				}

			} else {
				// insert
				obsdbELF506Service.insert(brnId, cntrNo, "", "", "",
						BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
						BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
						BigDecimal.ZERO, BigDecimal.ZERO, LMSUtil
								.covertAs400Time(new Timestamp(System
										.currentTimeMillis())), createTime,
						"ELOAN", "ELOAN", "", "", "", "", custIdDupNo,
						is722Flag, modUnit, "", LMSUtil
								.covertAs400Time(modTime), Util
								.parseBigDecimal(sDate.replaceAll("\\D", "")),
						isBuy, exItem, "", "", "", "", "", "", "",
						isInstalment, prodKind, adcCaseNo, exceptFlagOVS, exceptFlagQAisYOVS, 
						exceptFlagQAPlusOVS);
			}

			// 72-2值有填才上傳
			if (isUpdate722Data && Util.isNotEmpty(is722Flag)) {
				List<L140MM3C> l140mm3cs = l140mm3cDao
						.findCurrentByMainId(mainId);
				// 都delete 再insert
				obsdbELF515Service.delete(brnId, cntrNo);
				if (l140mm3cs != null && l140mm3cs.size() > 0) {
					for (L140MM3C l140mm3c : l140mm3cs) {
						ELF515 elf515 = this.convertELF515(l140mm3c, cntrNo,
								user);
						obsdbELF515Service.insert(brnId, elf515);
					}
				}
			}
		}
	}

	public void upEllnseek(L140MM3A l140mm3a) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 動審表簽章欄檔取得人員職稱
		List<L140MM3B> l140mm3blist = l140mm3bDao.findByMainId(l140mm3a
				.getMainId());
		String apprId = "";
		String reCheckId = "";

		for (L140MM3B l140mm3b : l140mm3blist) {
			String StaffJob = Util.trim(l140mm3b.getStaffJob());// 取得人員職稱
			String StaffNo = Util.trim(l140mm3b.getStaffNo());// 取得行員代碼
			if (Util.equals(StaffJob, "L1")) {// 分行經辦
				apprId = StaffNo;
			} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
				reCheckId = StaffNo;
			}
		}
		// 若人員職稱為空值改取c160m01a上的人員資料
		if (Util.isEmpty(apprId)) {
			apprId = l140mm3a.getUpdater();
		}
		if (Util.isEmpty(reCheckId)) {
			reCheckId = l140mm3a.getApprover();
		}

		Map<String, String> createUnitMap = new HashMap<String, String>();
		Map<String, Timestamp> createTimeMap = new HashMap<String, Timestamp>();

		String mainId = l140mm3a.getMainId();
		String brnId = Util.getLeftStr(l140mm3a.getCntrNo(), 3);
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brnId).getBrNoFlag());
		String cntrNo = l140mm3a.getCntrNo();
		String custId = l140mm3a.getCustId();
		String dupNo = l140mm3a.getDupNo();

		String isSpecialFinRisk = "";
		String specialFinRiskType = "";
		String isCmsAdcRisk = Util.trim(l140mm3a.getIsCmsAdcRisk());
		// J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
		String isProjectFinOperateStag = "";
		//J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位
		String isHighQualityProjOpt_1 = "";
		String isHighQualityProjOpt_2 = "";
		String isHighQualityProjOpt_3 = "";
		String isHighQualityProjOpt_4 = "";
		String isHighQualityProjOpt_5 = "";
		String isHighQualityProjResult = "";
		if (this.needShowIsSpecialFinRisk(l140mm3a)) {
			isSpecialFinRisk = Util.trim(l140mm3a.getIsSpecialFinRisk());
			if (Util.equals(isSpecialFinRisk, "Y")) {
				specialFinRiskType = Util
						.trim(l140mm3a.getSpecialFinRiskType());
				if (Util.equals(specialFinRiskType, "1")) {
					// 專案融資
					isProjectFinOperateStag = Util.trim(l140mm3a
							.getIsProjectFinOperateStag());
					isHighQualityProjOpt_1 = Util.trim(l140mm3a.getIsHighQualityProjOpt_1());
					isHighQualityProjOpt_2 = Util.trim(l140mm3a.getIsHighQualityProjOpt_2());
					isHighQualityProjOpt_3 = Util.trim(l140mm3a.getIsHighQualityProjOpt_3());
					isHighQualityProjOpt_4 = Util.trim(l140mm3a.getIsHighQualityProjOpt_4());
					isHighQualityProjOpt_5 = Util.trim(l140mm3a.getIsHighQualityProjOpt_5());
					isHighQualityProjResult = Util.trim(l140mm3a.getIsHighQualityProjResult());
				}
			}

		}

		List<Map<String, Object>> ellnseeks = misEllnseekservice.findByKey(
				custId, dupNo, cntrNo);

		if (ellnseeks.isEmpty()) {

			String apprYY = CapDate.getCurrentDate("yyyy");// 年份
			String apprMM = CapDate.getCurrentDate("MM");// 月份

			misEllnseekservice.insertOnlySpecialFinRisk(custId, dupNo, cntrNo,
					user.getUnitNo(), "9", apprYY, apprMM, reCheckId,
					isSpecialFinRisk, specialFinRiskType, isCmsAdcRisk,
					isProjectFinOperateStag, 
					isHighQualityProjOpt_1, isHighQualityProjOpt_2, isHighQualityProjOpt_3,
					isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult);
		} else {
			for (Map<String, Object> ellnseek : ellnseeks) {
				String timeStamp = MapUtils.getString(ellnseek, "TMESTAMP");
				misEllnseekservice.updateOnlySpecialFinRisk(custId, dupNo,
						cntrNo, timeStamp, isSpecialFinRisk,
						specialFinRiskType, isCmsAdcRisk,
						isProjectFinOperateStag,
						isHighQualityProjOpt_1, isHighQualityProjOpt_2, isHighQualityProjOpt_3,
						isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult);
				break;
			}

		}

		String cntrBrNo = Util.getLeftStr(cntrNo, 3);
		if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(cntrBrNo)
				.getBrNoFlag())) {

			List<Map<String, Object>> elf461s = obsdbELF461Service.findByKey(
					cntrBrNo, custId, dupNo, cntrNo);

			if (elf461s.isEmpty()) {
				BigDecimal now = LMSUtil.covertAs400Time(CapDate
						.getCurrentTimestamp());
				String apprYY = CapDate.getCurrentDate("yyyy");// 年份
				String apprMM = CapDate.getCurrentDate("MM");// 月份
				obsdbELF461Service.insertOnlySpecialFinRisk(cntrBrNo, custId,
						dupNo, cntrNo, cntrBrNo, "9", apprYY, apprMM,
						reCheckId, now, isSpecialFinRisk, specialFinRiskType,
						isCmsAdcRisk, isProjectFinOperateStag,
						isHighQualityProjOpt_1, isHighQualityProjOpt_2, isHighQualityProjOpt_3,
						isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult);
			} else {

				for (Map<String, Object> elf461 : elf461s) {
					BigDecimal timeStamp = Util.parseBigDecimal(MapUtils
							.getString(elf461, "ELF461_TMESTAMP"));
					obsdbELF461Service.updateOnlySpecialFinRisk(cntrBrNo,
							custId, dupNo, cntrNo, timeStamp, isSpecialFinRisk,
							specialFinRiskType, isCmsAdcRisk,
							isProjectFinOperateStag,
							isHighQualityProjOpt_1, isHighQualityProjOpt_2, isHighQualityProjOpt_3,
							isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult);
					break;
				}

			}
		}

	}

	/**
	 * J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * @param l140mm3a
	 * @throws CapException
	 */
	public void upAdcInfo(L140MM3A l140mm3a) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 動審表簽章欄檔取得人員職稱
		List<L140MM3B> l140mm3blist = l140mm3bDao.findByMainId(l140mm3a
				.getMainId());
		String apprId = "";
		String reCheckId = "";

		for (L140MM3B l140mm3b : l140mm3blist) {
			String StaffJob = Util.trim(l140mm3b.getStaffJob());// 取得人員職稱
			String StaffNo = Util.trim(l140mm3b.getStaffNo());// 取得行員代碼
			if (Util.equals(StaffJob, "L1")) {// 分行經辦
				apprId = StaffNo;
			} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
				reCheckId = StaffNo;
			}
		}
		// 若人員職稱為空值改取c160m01a上的人員資料
		if (Util.isEmpty(apprId)) {
			apprId = l140mm3a.getUpdater();
		}
		if (Util.isEmpty(reCheckId)) {
			reCheckId = l140mm3a.getApprover();
		}

		Map<String, String> createUnitMap = new HashMap<String, String>();
		Map<String, Timestamp> createTimeMap = new HashMap<String, Timestamp>();

		String mainId = l140mm3a.getMainId();
		String brnId = Util.getLeftStr(l140mm3a.getCntrNo(), 3);
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brnId).getBrNoFlag());
		String cntrNo = l140mm3a.getCntrNo();
		String custId = l140mm3a.getCustId();
		String dupNo = l140mm3a.getDupNo();

		String prodKind = "";
		String adcCaseNo = "";
		adcCaseNo = Util.nullToSpace(l140mm3a.getAdcCaseNo());
		prodKind = Util.nullToSpace(l140mm3a.getLnType());

		// ELF506_PROD_KIND
		// ELF447N_PROD_CLASS
		// ELF383_PROD_KIND
		//
		// L140M01A lnType 、L140M01A

		// --MIS
		// update mis.elf506 set ELF506_PROD_KIND = '34' WHERE ELF506_CNTRNO IN
		// ('0C2502050090');
		// update mis.elf447n set ELF447N_PROD_CLASS = '34' WHERE
		// ELF447N_CONTRACT IN ('0C2502050090');
		// update mis.quotappr set PROD_KIND = '34' WHERE cntrno IN
		// ('0C2502050090');
		// --ELOAN
		// update lms.l140m01a set lnType = '34' where docstatus = '030' and
		// deletedTime is null and cntrno = '0C2502050090';

		// 更新MIS
		misdbBASEService.updateAdcInfo(cntrNo, prodKind, adcCaseNo);
		eloandbBASEService.updateAdcInfo(cntrNo, prodKind, adcCaseNo);

		// 更新海外
		if (isOverSea) {
			obsDBService.updateAdcInfo(brnId, cntrNo, prodKind, adcCaseNo);
		}

	}
	
	@Override
	public L140MM3C findLastestL140mm3cByMainIdEstateType(String mainId) {
		
		List<L140MM3C> list = l140mm3cDao.findLastestByMainIdEstateType(mainId);
		
		if(!list.isEmpty()){
			return list.get(0);
		}
		
		return null;
	}
	
	@Override
	public ELF506 findELF506ByCntrNo(String cntrNo){
		return this.misELF506Service.findByCntrNo(cntrNo);
	}
	
	@Override
	public L120M01A findL120m01aByMainId(String mainId) {
		return l120m01aDao.findByMainId(mainId);
	}
	
	@Override
	public String checkFinancingNotesAgreed(L140MM3A l140mm3a)
		throws CapException {
		
		if (!"Y".equals(Util.trim(l140mm3a.getUpdateItem5())) || "918".equals(l140mm3a.getCntrNo().substring(0, 3))){ // 團貸建案母戶
			return "";
		}
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS7500M01Page.class);
		
		String msg = "";
		boolean isLmsCase = this.isShowQ2Q7AttachedItem(l140mm3a.getCntrNo());

		String exceptFlagQAisY = Util.trim(l140mm3a.getExceptFlagQAisY());
		
		if (Util.isEmpty(l140mm3a.getExceptFlag())) {
			// (若本案額度非屬廣義授信業務範圍(如：衍生性金融商品)時，本額度是否「非屬於廣義授信業務範圍」請勾選為
			msg = "(請確認「約定融資額度註記(異動後)」是否勾選正確)";
		}
		
		if(isLmsCase){
			
			if (Util.isEmpty(l140mm3a.getExceptFlag())) {

				if ("2".equals(exceptFlagQAisY) || "7".equals(exceptFlagQAisY)) {
					msg = "(請勾選欄位是否符合(a)(b)(c)全部條件)";
				}
				
				msg = prop.getProperty("L140M01a.exceptFlag.afterUpdate") + msg;
				
			} else if (("2".equals(exceptFlagQAisY) || "7".equals(exceptFlagQAisY)) && Util.isEmpty(l140mm3a.getExceptFlagQAPlus())) {
				// J-112-0566 配合金管會修訂「銀行自有資本與風險性資產計算方法說明及表格修正重點」，新增勾選項目
				msg = prop.getProperty("L140M01a.exceptFlag.afterUpdate") + "(請勾選欄位是否符合(a)(b)(c)全部條件)";
			}
		}

		return msg;
	}
	
	@Override
	public boolean isShowQ2Q7AttachedItem(String cntrNo){
		
		L140M01A l140m01a = this.lmsService.getL140m01aByCntrNoForNew(cntrNo);
		
		String busCode = "";
		if(l140m01a == null){
			
			Map<String, Object> dwMap = this.dwdbBASEService.findDwLncntrovsByCntrNo(cntrNo);
			
			if(dwMap != null){
				
				String custKey = (String)dwMap.get("CUST_KEY");
				String custId = custKey;
				String dupNo = "0";
				
				if(custKey.length() > 10){
					custId = custKey.substring(0, 10);
					dupNo = custKey.substring(10, 11);
				}
				
				Map<String, Object> custDataMap = this.misCustdataService.findBUSCDByCustIdANdDupNo(custId, dupNo);
				busCode = String.valueOf(custDataMap.get("BUSCD"));
			}
		}
		
		if(l140m01a != null){
			busCode = this.lmsLgdService.getCustBusCodeByL140m01a(l140m01a);
		}
		
		if (LMSUtil.isBusCode_060000_130300(busCode)) {
			return false;
		}
		else{
			return true;
		}
	}
	
	@Override
	public boolean processIsDerivatives(String l140m01a_mainId){
		
		List<L140M01C> l140m01cs = this.clsService.findL140m01cListByMainId(l140m01a_mainId);
	
		if (!l140m01cs.isEmpty()) {
			// 用來存放衍生性金融商品授信科目代號
			ArrayList<String> items = new ArrayList<String>();
			ArrayList<String> itemsAll = new ArrayList<String>();
	
			for (L140M01C l140m01c : l140m01cs) {
				itemsAll.add(l140m01c.getLoanTP());
				if (lmsService.isGenDerivativesNumItem(l140m01c.getLoanTP())) {
					items.add(l140m01c.getLoanTP());
				}
			}

			Boolean hasDerivateSubjectFlag = false;
		
			// 判斷衍生性科目是否要產生風險係數
			hasDerivateSubjectFlag = lmsService.hasDerivateSubject(itemsAll.toArray(new String[itemsAll.size()]));
	
			// J-105-0155-001 Web
			// e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
			// 衍生性商品不算約定融資額度
			if (!itemsAll.isEmpty()) {
				if (hasDerivateSubjectFlag) {
					return true;
				} 
				else {
					return false;
				}
			}
		}
		
		return false;
	}
	
}
