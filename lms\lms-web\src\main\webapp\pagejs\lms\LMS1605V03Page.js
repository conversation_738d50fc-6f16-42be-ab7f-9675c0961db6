$(function(){

    //判斷查詢為何種形式
    $("[name=queryData]").click(function(){
        $(this).val() == '1' ? $("#queryDataTr1").show().siblings("#queryDataTr2").hide() : $("#queryDataTr2").show().siblings("#queryDataTr1").hide();
        
    });
    
    $("#managerId").change(function(){
        if ($(this).val() == "0") {
            $("#managerNm").show();
        } else {
            $("#managerNm").hide();
        }
    });
    
    openFilterBox();
    
    
    var grid = $("#gridview").iGrid({
        handler: 'lms1605gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        rowNum: 15,
        sortname: 'createTime|custId',
        sortorder: 'desc|asc',
        multiselect: false,
        colModel: [{
            colHeader: i18n.lms1605m01['L160M01A.approveTime'],// "核定日期",
            name: 'approveTime',
            width: 70,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d'
            },
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.mainCustId'],// "主要借款人統編",
            name: 'custId',
            width: 80,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms1605m01['L160M01A.mainCust'],// "主要借款人",
            name: 'custName',
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.caseNo'],// "案號",
            name: 'caseNo',
            width: 150,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.cntrNo'],// "動用額度序號",
            name: 'allCanPay',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.creatorPerson'],// "分行經辦",
            name: 'apprId',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.allCanPay'],// "先行動用",
            name: 'useType',
            width: 40,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L163M01A.finishDate'],// "辦妥日期",
            name: 'blackDataDate',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L163M01A.CheckDate'],// "辦妥覆核",
            name: 'blackListTxtErr',
            width: 40,
            sortable: true,
            align: "center"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
/*    
    var gridviewUseFirst = $("#gridviewUseFirst").iGrid({
        handler: 'lms1605gridhandler',
        height: 220,
        rowNum: 10,
        postData: {
            formAction: "queryUseFirst",
            docStatus: viewstatus
        },
        //multiselect: true,
        colModel: [{
            colHeader: i18n.lms1605m01['L163M01A.uploadDate'],// " 先行動用覆核日",
            name: 'approveTime',
            width: 120,
            align: "left",
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d'
            },
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L163M01A.willFinishDate'],// " 預定補全日期",
            name: 'caseDate',
            width: 120,
            align: "left",
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d'
            },
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.mainCustId'],// "主要借款人統編",
            name: 'custId',
            width: 120,
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.mainCust'],// "主要借款人",
            name: 'custName',
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L163M01A.waitingItem'],// "待辦事項",
            name: 'caseNo',
            width: 140,
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }]
    });
*/    
    
    
    //先行動用待辦事項控制表登錄介面
    function openUseFirstTable(){
        $("#UseFirstBox").thickbox({
            //L160M01A.title12=先行動用待辦事項控制表登錄介面
            title: i18n.lms1605m01['L160M01A.title12'],
            width: 720,
            height: 400,
            modal: true,
            i18n: i18n.lms1605m01,
            buttons: {
                "L160M01A.bt16": function(){
                    $.form.submit({
                        url: "../simple/FileProcessingService",
                        target: "_blank",
                        data: {
                            fileDownloadName: "lms1605r04.pdf",
                            serviceName: "lms1605r04rptservice"
                        }
                    });
                },
                "L160M01A.bt18": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    //先行動用呈核及控制表
    function openLogeIN(){
    
        //檢查選擇的是否有未辦妥
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
        
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        
        var result = $("#gridview").getRowData(id);
        if (result.useType != "V") {
        
            //L160M01A.message03=此案件並未先行動用，故不需登錄辦妥日期
            return CommonAPI.showMessage(i18n.lms1605v03["L160M01A.message03"]);
        }
        
        if ($.trim(result.blackListTxtErr) != "") {
            //L160M01A.message04=此先行動用案件已辦妥且已呈主管覆核，不得再異動
            return CommonAPI.showMessage(i18n.lms1605v03["L160M01A.message04"]);
        }
        
        
        $("#logeINBox").thickbox({
            //L160M01A.title13=先行動用呈核及控制表
            title: i18n.lms1605m01['L160M01A.title13'],
            width: 650,
            height: 420,
            modal: true,
            valign: "bottom",
            align: "center",
            open: function(){
                //初始化
                $("#L160M01AForm").reset();
                
                $.ajax({
                    handler: "lms1605m01formhandler",
                    data: {//把資料轉成json
                        formAction: "queryLogeIN",
                        oid: result.oid
                    
                    },
                    success: function(obj){
                        $("#managerNm").hide();
                        $("#managerId").empty();
                        //該分行甲級主管名單
                        $("#managerId").setItems({
                            item: obj.bossList
                        });
                        //L160M01A.message08=自行輸入
                        $("#managerId").append($("<option/>").attr("value", "0"));
                        $("#managerId").find("option[value='0']").text(DOMPurify.sanitize(i18n.lms1605v03['L160M01A.message08']));
                        $("#L160M01AForm").setData(obj);
                        
                    }
                });
                
            },
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#L160M01AForm").valid()) {
                        return;
                    }
                    if (($("#managerId").val() == "0" && $.trim($("#managerNm").val()) == "") || $("#managerId").val() == "") {
                    
                        //L160M01A.message10=請輸入甲級主管姓名
                        return CommonAPI.showErrorMessage(i18n.lms1605v03["L160M01A.message10"]);
                    }
                    $.ajax({
                        handler: "lms1605m01formhandler",
                        data: {
                            formAction: "saveLogeIn",
                            oid: $("#L160M01AForm").attr("formOid")
                        },
                        success: function(obj){
                            $.thickbox.close();
                            $("#gridview").trigger('reloadGrid');
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    //篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        //初始化
        $filterForm.reset();
        $("[name=queryData][value=1]").prop("checked", true);
        $("#queryDataTr1").show().siblings("#queryDataTr2").hide();
        $("#fromDate").val(CommonAPI.getToday());
        $("#endDate").val(CommonAPI.getToday());
        
        $("#filterBox").thickbox({
            // L160M01A.title01=動用審核表
            title: i18n.lms1605m01['L160M01A.title01'],
            width: 500,
            height: 150,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$filterForm.valid()) {
                        return false;
                    }
                    if ($("[name=queryData]:checked").val() == '1') {
                        var cust = $filterForm.find("#custId").val();
                        
                        if ($.trim(cust) == "") {
                        
                            //L160M01A.message07=借款人統編尚未輸入
                            return CommonAPI.showErrorMessage(i18n.lms1605v03["L160M01A.message07"]);
                        }
                        
                        filterGrid({
                            custId: cust.slice(0, cust.length - 1),
                            dupNo: cust.slice(cust.length - 1)
                        });
                    } else {
                    
                        if ($.trim($("#endDate").val()) == "" || $.trim($("#fromDate").val()) == "") {
                            //請輸入日期
                            return CommonAPI.showErrorMessage(i18n.lms1605v03["L160M01A.message09"]);
                        }
                        
                        var end = $("#endDate").val().split("-");
                        var from = $("#fromDate").val().split("-");
                        var endData = new Date(end[0], end[1], end[2]);
                        var fromData = new Date(from[0], from[1], from[2]);
                        
                        if (fromData > endData) {
                        
                            //L160M01A.error11=起始日期不能大於結束日期
                            return CommonAPI.showErrorMessage(i18n.lms1605v03["L160M01A.message06"]);
                        }
                        filterGrid({
                            endDate: $("#endDate").val(),
                            fromDate: $("#fromDate").val()
                        });
                    }
                    
                    $.thickbox.close();
                },
                "cancel": function(){
                    filterGrid({
                        type: ""
                    });
                    $.thickbox.close();
                }
            }
        });
    }
    
    //資料修正選擇額度明細表grid
 /*   
    var gridviewCase = $("#gridviewCase").iGrid({
        handler: 'lms1605gridhandler',
        height: 225,
        rowNum: 10,
        postData: {
            formAction: ""
        },
        multiselect: false,
        colModel: [{
            colHeader: i18n.lms1605m01['L160M01A.mainCustId'],// "統編",
            name: 'custId',
            width: 120,
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.mainCust'],// "主要借款人",
            name: 'custName',
            width: 140,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.cntrNum'],// "額度序號",
            name: 'cntrNo',
            width: 140,
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }]
    });
*/    
    
    function openCntrCaseBox(oid){
        $("#gridviewCase").jqGrid("setGridParam", {
            postData: {
                formAction: "queryCntrCase",
                oid: oid
            },
            search: true
        }).trigger("reloadGrid");
        $("#cntrCaseBox").thickbox({
            // L160M01A.title14=選擇一份額度明細表
            title: i18n.lms1605m01['L160M01A.title14'],
            width: 600,
            height: 350,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $("#gridviewCase").getGridParam('selrow');
                    if (!id) {
                        // action_004=請先選擇需「調閱」之資料列
                        return CommonAPI.showMessage(i18n.def["action_004"]);
                    }
                    
                    var result = $("#gridviewCase").getRowData(id);
                    
                    $.ajax({
                        handler: "lms1605m01formhandler",
                        data: {
                            formAction: "queryDateFixBase",
                            l140m01aOid: result.oid,
                            l160m01aOid: oid,
                            txCode: window.txCode
                        },
                        success: function(obj){
                        
                        }
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    
    //grid資料篩選
    function filterGrid(sendData){
        $("#gridview").jqGrid("setGridParam", {
            postData: $.extend({
                formAction: "queryL160m01a3",
                docStatus: viewstatus,
                type: $("[name=queryData]:checked").val()
            }, sendData || {}),
            search: true
        }).trigger("reloadGrid");
    }
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',//'../lms/lms1605m01/01',
            data: {
                formAction: "queryL160m01a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
                $.ajax({
                    handler: "lms1605m01formhandler",
                    data: {
                        formAction: "deleteL160m01a",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
        
        
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        
        if (!id) {
        
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            CommonAPI.showMessage(i18n.lms1605m01["L160M01a.error1"]);
        } else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    }).end().find("#btnUseFirstTable").click(function(){
        openUseFirstTable();
    }).end().find("#btnLogeIN").click(function(){
        openLogeIN();
    }).end().find("#btnDataFix").click(function(){
    
        var id = $("#gridview").getGridParam('selrow');
        
        if (!id) {
        
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        
        openCntrCaseBox(result.oid);
        
    });
});
