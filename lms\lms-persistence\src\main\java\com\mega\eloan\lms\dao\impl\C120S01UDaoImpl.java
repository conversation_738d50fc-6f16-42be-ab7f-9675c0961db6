
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C120S01UDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01U;

/** 個金EJ標準查詢檔 **/
@Repository
public class C120S01UDaoImpl extends LMSJpaDao<C120S01U, String>
	implements C120S01UDao {

	@Override
	public C120S01U findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01U> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01U> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C120S01U> findByMainIdCustIdDupNo(String mainId, String custId, String dupNo){
		
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01U> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C120S01U> findByMainIdCustIdDupNoTxid(String mainId, String custId, String dupNo, String txid){
		
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "txid", txid);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01U> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120S01U.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}