/* 
 * L120S14F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 增補合約資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S14F", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S14F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 新產生時：getUUID()
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 合約編號 **/
	@Size(max=66)
	@Column(name="SUPCONTRACTNO", length=66, columnDefinition="VARCHAR(66)")
	private String supContractNo;

	/** 次數 **/
	private Integer frequency;

	/** 
	 * 增補簽約日<p/>
	 * yyyy-MM-dd<br/>
	 *  sup: Supplementary
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="SUPDATE", columnDefinition="DATE")
	private Date supDate;

	/** 
	 * 增補授信期間<p/>
	 * 年
	 */
	private Integer supLnyear;

	/** 
	 * 增補授信起日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="SUPLNBGNDATE", columnDefinition="DATE")
	private Date supLnBgnDate;

	/** 
	 * 增補授信迄日<p/>
	 * yyyy-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="SUPLNENDDATE", columnDefinition="DATE")
	private Date supLnEndDate;

	/** 
	 * 寬限期<p/>
	 * 幾個月、償還方式=2
	 */
	private Integer supGracePeriod;

	/** 
	 * 每期<p/>
	 * 每期幾個月、償還方式=2
	 */
	private Integer supPeriod;

	/** 
	 * 期數<p/>
	 * 分幾期、償還方式=2
	 */
	private Integer supPeriodNum;

	/** 
	 * 還本期數 - 起<p/>
	 * 第幾期、償還方式=2
	 */
	private Integer supCapitalBgn;

	/** 
	 * 還本期數 - 迄<p/>
	 * 第幾期、償還方式=2
	 */
	private Integer supCapitalEnd;

	/** 
	 * 還本金額<p/>
	 * 償還方式=2
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="SUPCAPITALAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal supCapitalAmt;

	/** 
	 * 甲方代理分行<p/>
	 * 分行ID
	 */
	@Size(max=3)
	@Column(name="SUPPROXYBRID", length=3, columnDefinition="CHAR(3)")
	private String supProxyBrId;

	/** 
	 * 甲方代理人<p/>
	 * 分行經理ID
	 */
	@Size(max=6)
	@Column(name="SUPPROXY", length=6, columnDefinition="CHAR(6)")
	private String supProxy;

	/** 
	 * 甲方代理地址<p/>
	 * 分行地址
	 */
	@Size(max=192)
	@Column(name="SUPPROXYADDR", length=192, columnDefinition="VARCHAR(192)")
	private String supProxyAddr;

	/** 乙方 **/
	@Size(max=150)
	@Column(name="SUPPARTY", length=150, columnDefinition="VARCHAR(150)")
	private String supParty;

	/** 乙方代理人 **/
	@Size(max=150)
	@Column(name="SUPPARTYAGENT", length=150, columnDefinition="VARCHAR(150)")
	private String supPartyAgent;

	/** 乙方地址 **/
	@Size(max=192)
	@Column(name="SUPPARTYADDR", length=192, columnDefinition="VARCHAR(192)")
	private String supPartyAddr;

	/** 乙方統編 **/
	@Size(max=10)
	@Column(name="SUPPARTYID", length=10, columnDefinition="VARCHAR(10)")
	private String supPartyId;

	/** 丙方 **/
	@Size(max=150)
	@Column(name="SUPPARTY2", length=150, columnDefinition="VARCHAR(150)")
	private String supParty2;

	/** 連保人 **/
	@Size(max=150)
	@Column(name="SUPGUARANTOR", length=150, columnDefinition="VARCHAR(150)")
	private String supGuarantor;

	/** 連保人統編 **/
	@Size(max=10)
	@Column(name="SUPGUARID", length=10, columnDefinition="VARCHAR(10)")
	private String supGuarId;

	/** 連保人地址 **/
	@Size(max=192)
	@Column(name="SUPGUARADDR", length=192, columnDefinition="VARCHAR(192)")
	private String supGuarAddr;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 新產生時：getUUID()
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  新產生時：getUUID()
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  每次儲存：getRandomCode()
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得合約編號 **/
	public String getSupContractNo() {
		return this.supContractNo;
	}
	/** 設定合約編號 **/
	public void setSupContractNo(String value) {
		this.supContractNo = value;
	}

	/** 取得次數 **/
	public Integer getFrequency() {
		return this.frequency;
	}
	/** 設定次數 **/
	public void setFrequency(Integer value) {
		this.frequency = value;
	}

	/** 
	 * 取得增補簽約日<p/>
	 * yyyy-MM-dd<br/>
	 *  sup: Supplementary
	 */
	public Date getSupDate() {
		return this.supDate;
	}
	/**
	 *  設定增補簽約日<p/>
	 *  yyyy-MM-dd<br/>
	 *  sup: Supplementary
	 **/
	public void setSupDate(Date value) {
		this.supDate = value;
	}

	/** 
	 * 取得增補授信期間<p/>
	 * 年
	 */
	public Integer getSupLnyear() {
		return this.supLnyear;
	}
	/**
	 *  設定增補授信期間<p/>
	 *  年
	 **/
	public void setSupLnyear(Integer value) {
		this.supLnyear = value;
	}

	/** 
	 * 取得增補授信起日<p/>
	 * yyyy-MM-dd
	 */
	public Date getSupLnBgnDate() {
		return this.supLnBgnDate;
	}
	/**
	 *  設定增補授信起日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setSupLnBgnDate(Date value) {
		this.supLnBgnDate = value;
	}

	/** 
	 * 取得增補授信迄日<p/>
	 * yyyy-MM-dd
	 */
	public Date getSupLnEndDate() {
		return this.supLnEndDate;
	}
	/**
	 *  設定增補授信迄日<p/>
	 *  yyyy-MM-dd
	 **/
	public void setSupLnEndDate(Date value) {
		this.supLnEndDate = value;
	}

	/** 
	 * 取得寬限期<p/>
	 * 幾個月、償還方式=2
	 */
	public Integer getSupGracePeriod() {
		return this.supGracePeriod;
	}
	/**
	 *  設定寬限期<p/>
	 *  幾個月、償還方式=2
	 **/
	public void setSupGracePeriod(Integer value) {
		this.supGracePeriod = value;
	}

	/** 
	 * 取得每期<p/>
	 * 每期幾個月、償還方式=2
	 */
	public Integer getSupPeriod() {
		return this.supPeriod;
	}
	/**
	 *  設定每期<p/>
	 *  每期幾個月、償還方式=2
	 **/
	public void setSupPeriod(Integer value) {
		this.supPeriod = value;
	}

	/** 
	 * 取得期數<p/>
	 * 分幾期、償還方式=2
	 */
	public Integer getSupPeriodNum() {
		return this.supPeriodNum;
	}
	/**
	 *  設定期數<p/>
	 *  分幾期、償還方式=2
	 **/
	public void setSupPeriodNum(Integer value) {
		this.supPeriodNum = value;
	}

	/** 
	 * 取得還本期數 - 起<p/>
	 * 第幾期、償還方式=2
	 */
	public Integer getSupCapitalBgn() {
		return this.supCapitalBgn;
	}
	/**
	 *  設定還本期數 - 起<p/>
	 *  第幾期、償還方式=2
	 **/
	public void setSupCapitalBgn(Integer value) {
		this.supCapitalBgn = value;
	}

	/** 
	 * 取得還本期數 - 迄<p/>
	 * 第幾期、償還方式=2
	 */
	public Integer getSupCapitalEnd() {
		return this.supCapitalEnd;
	}
	/**
	 *  設定還本期數 - 迄<p/>
	 *  第幾期、償還方式=2
	 **/
	public void setSupCapitalEnd(Integer value) {
		this.supCapitalEnd = value;
	}

	/** 
	 * 取得還本金額<p/>
	 * 償還方式=2
	 */
	public BigDecimal getSupCapitalAmt() {
		return this.supCapitalAmt;
	}
	/**
	 *  設定還本金額<p/>
	 *  償還方式=2
	 **/
	public void setSupCapitalAmt(BigDecimal value) {
		this.supCapitalAmt = value;
	}

	/** 
	 * 取得甲方代理分行<p/>
	 * 分行ID
	 */
	public String getSupProxyBrId() {
		return this.supProxyBrId;
	}
	/**
	 *  設定甲方代理分行<p/>
	 *  分行ID
	 **/
	public void setSupProxyBrId(String value) {
		this.supProxyBrId = value;
	}

	/** 
	 * 取得甲方代理人<p/>
	 * 分行經理ID
	 */
	public String getSupProxy() {
		return this.supProxy;
	}
	/**
	 *  設定甲方代理人<p/>
	 *  分行經理ID
	 **/
	public void setSupProxy(String value) {
		this.supProxy = value;
	}

	/** 
	 * 取得甲方代理地址<p/>
	 * 分行地址
	 */
	public String getSupProxyAddr() {
		return this.supProxyAddr;
	}
	/**
	 *  設定甲方代理地址<p/>
	 *  分行地址
	 **/
	public void setSupProxyAddr(String value) {
		this.supProxyAddr = value;
	}

	/** 取得乙方 **/
	public String getSupParty() {
		return this.supParty;
	}
	/** 設定乙方 **/
	public void setSupParty(String value) {
		this.supParty = value;
	}

	/** 取得乙方代理人 **/
	public String getSupPartyAgent() {
		return this.supPartyAgent;
	}
	/** 設定乙方代理人 **/
	public void setSupPartyAgent(String value) {
		this.supPartyAgent = value;
	}

	/** 取得乙方地址 **/
	public String getSupPartyAddr() {
		return this.supPartyAddr;
	}
	/** 設定乙方地址 **/
	public void setSupPartyAddr(String value) {
		this.supPartyAddr = value;
	}

	/** 取得乙方統編 **/
	public String getSupPartyId() {
		return this.supPartyId;
	}
	/** 設定乙方統編 **/
	public void setSupPartyId(String value) {
		this.supPartyId = value;
	}

	/** 取得丙方 **/
	public String getSupParty2() {
		return this.supParty2;
	}
	/** 設定丙方 **/
	public void setSupParty2(String value) {
		this.supParty2 = value;
	}

	/** 取得連保人 **/
	public String getSupGuarantor() {
		return this.supGuarantor;
	}
	/** 設定連保人 **/
	public void setSupGuarantor(String value) {
		this.supGuarantor = value;
	}

	/** 取得連保人統編 **/
	public String getSupGuarId() {
		return this.supGuarId;
	}
	/** 設定連保人統編 **/
	public void setSupGuarId(String value) {
		this.supGuarId = value;
	}

	/** 取得連保人地址 **/
	public String getSupGuarAddr() {
		return this.supGuarAddr;
	}
	/** 設定連保人地址 **/
	public void setSupGuarAddr(String value) {
		this.supGuarAddr = value;
	}
}
