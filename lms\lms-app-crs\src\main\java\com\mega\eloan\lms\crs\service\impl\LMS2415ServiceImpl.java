/* 
 * LMS2415ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.crs.report.impl.LMS2415R01RptServiceImpl;
import com.mega.eloan.lms.crs.service.LMS2405Service;
import com.mega.eloan.lms.crs.service.LMS2415Service;
import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.dao.C241A01ADao;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01BDao;
import com.mega.eloan.lms.dao.C241M01CDao;
import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms491Service;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C240M01B;
import com.mega.eloan.lms.model.C241A01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS2415ServiceImpl extends AbstractCapService implements
		LMS2415Service {

	private static Logger logger = LoggerFactory
			.getLogger(LMS2415ServiceImpl.class);
	@Resource
	TempDataService tempDataService;

	@Resource
	FlowService flowService;

	@Resource
	BranchService branchService;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	LMSService lmsService;

	@Resource
	MisRatetblService misRateblService;

	@Resource
	C240M01ADao c240m01aDao;

	@Resource
	C241M01ADao c241m01aDao;
	
	@Resource
	C241A01ADao c241a01aDao;

	@Resource
	C241M01BDao c241m01bDao;

	@Resource
	C241M01CDao c241m01cDao;

	@Resource
	C241M01EDao c241m01eDao;

	@Resource
	DocLogService docLogService;

	@Resource
	EloandbBASEService eloandbbaseService;

	@Resource
	Lms491Service lms491Service;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	LMS2405Service lms2405Service;
	
	@Resource
	DocFileDao docFileDao;
	
	@Resource
	SysParameterService sysParameterService;

	@Override
	public void deleteC241M01A(String oid) {
		C241M01A c241m01a = c241m01aDao.findByOid(oid);
		if (c241m01a != null) {
			List<C241M01B> c241m01bs = c241m01bDao.findByMainId(c241m01a
					.getMainId());
			List<C241M01C> c241m01cs = c241m01cDao.findByMainId(c241m01a
					.getMainId());
			c241m01bDao.delete(c241m01bs);
			c241m01cDao.delete(c241m01cs);
			c241m01aDao.delete(c241m01a);
		}
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C241M01A.class) {
			return (T) c241m01aDao.find(oid);
		} else if (clazz == C241M01B.class) {
			return (T) c241m01bDao.find(oid);
		} else if (clazz == C241M01C.class) {
			return (T) c241m01cDao.find(oid);
		} else if (clazz == C240M01A.class) {
			return (T) c240m01aDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId) {
		if (clazz == C241M01A.class) {
			return (T) c241m01aDao.findByMainId(mainId);
		} else if (clazz == C241M01B.class) {
			return (T) c241m01bDao.findByMainId(mainId);
		} else if (clazz == C241M01C.class) {
			return (T) c241m01cDao.findByMainId(mainId);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C241M01A.class) {
			return c241m01aDao.findPage(search);
		} else if (clazz == C241M01B.class) {
			return c241m01bDao.findPage(search);
		} else if (clazz == C241M01C.class) {
			return c241m01cDao.findPage(search);
		} else if (clazz == DocFile.class) {
			return docFileDao.findPage(search);
		}
		return null;
	}

	@Override
	public C241M01C findC241m01cByMainId(String mainId, String custId,
			String dupNo, int itemSeq) {
		return c241m01cDao.findByIndex02(mainId, custId, dupNo, itemSeq);
	}
	
	@Override
	public C241M01C findC241m01cByMainId(String mainId, String custId,
			String dupNo, String itemNo) {
		return c241m01cDao.findByUniqueKey(mainId, custId, dupNo, itemNo);
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C241M01A) {
					((C241M01A) model).setUpdater(user.getUserId());
					((C241M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C241M01A) model).setRandomCode(IDGenerator.getUUID());
					c241m01aDao.save(((C241M01A) model));
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C241M01A) model)
								.getMainId());
						// 記錄文件異動記錄
						docLogService.record(((C241M01A) model).getOid(),
								DocLogEnum.SAVE);
					}
				} else if (model instanceof C241M01B) {
					((C241M01B) model).setUpdater(user.getUserId());
					((C241M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());

					c241m01bDao.save(((C241M01B) model));

				} else if (model instanceof C241M01C) {
					((C241M01C) model).setUpdater(user.getUserId());
					((C241M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C241M01C) model)
								.getMainId());
						c241m01cDao.save(((C241M01C) model));

					}
				}
			}
		}

	}
	
	@Override
	public void saveNoChangUpdate(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C241M01A) {
					c241m01aDao.save(((C241M01A) model));
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C241M01A) model)
								.getMainId());
						// 記錄文件異動記錄
						docLogService.record(((C241M01A) model).getOid(),
								DocLogEnum.SAVE);
					}
				} else if (model instanceof C241M01B) {
					c241m01bDao.save(((C241M01B) model));

				} else if (model instanceof C241M01C) {
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C241M01C) model)
								.getMainId());
						c241m01cDao.save(((C241M01C) model));

					}
				}
			}
		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	public void delete(Class clazz, String oid) {
		if (clazz == C241M01A.class) {
			C241M01A c241m01a = c241m01aDao.findByOid(oid);
			c241m01aDao.delete(c241m01a);
			docLogService.record(c241m01a.getOid(), DocLogEnum.DELETE);
		} else if (clazz == C241M01B.class) {
			C241M01B c241m01b = c241m01bDao.findByOid(oid);
			c241m01bDao.delete(c241m01b);
			docLogService.record(c241m01b.getOid(), DocLogEnum.DELETE);
		} else if (clazz == C241M01C.class) {
			C241M01C c241m01c = c241m01cDao.findByOid(oid);
			c241m01cDao.delete(c241m01c);
			docLogService.record(c241m01c.getOid(), DocLogEnum.DELETE);
		}
	}

	@Override
	public void deleteC241m01bByMainId(String mainId) {
		List<C241M01B> c241m01bList = c241m01bDao
				.findByMainIdByYLnDataDate(mainId);
		c241m01bDao.delete(c241m01bList);

	}

	@Override
	public void deleteC241m01cByMainId(String mainId) {
		List<C241M01C> c241m01cList = c241m01cDao.findByMainId(mainId);
		c241m01cDao.delete(c241m01cList);
	}

	@Override
	public Map<String, Object> saveC241m01bByCustIdData(String brNo,
			String custId, String mainId, String dupNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean result = false;
		// 查子額度(排除30(母額度))
		List<Map<String, Object>> rows = dwdbService
				.findDW_ASLNDAVGOVSJOIN_ByCustIdData(brNo, custId, dupNo);
		// 會計科目對應回授信科目(代碼)
		Map<String, String> subItemToAccountingMap = null;
		Map<String, String> vauleToKeyMap = new LinkedHashMap<String, String>();
		// 會計科目對應回授信科目
		Map<String, String> subItmeMap = null;
		subItemToAccountingMap = codetypeService
				.findByCodeType("lms1405m01_SubItemToAccounting");
		// 會有值重複問題,JASON說蓋掉就好
		for (String key : subItemToAccountingMap.keySet()) {
			vauleToKeyMap.put(subItemToAccountingMap.get(key), key);
		}
		subItmeMap = codetypeService.findByCodeType("lms1705m01_SubItme");
		List<C241M01B> c241m01bList = new ArrayList<C241M01B>();
		C241M01A c241m01a = this.findModelByMainId(C241M01A.class, mainId);
		for (Map<String, Object> dataMap : rows) {
			C241M01B c241m01b = new C241M01B();
			c241m01b.setMainId(mainId);
			c241m01b.setCustId(custId);
			c241m01b.setDupNo(dupNo);
			// 額度控管種類
			String factType = Util.nullToSpace(dataMap.get("FACT_TYPE"));
			// c241m01b.setLnType(factType);
			String dwbrNo = Util.nullToSpace(dataMap.get("BR_NO"));
			// 額度序號
			String contract = Util.nullToSpace(dataMap.get("CONTRACT"));
			c241m01b.setQuotaNo(contract);
			// DW 會計科目
			String actcd = Util.nullToSpace((dataMap.get("GL_AC_KEY")));
			// 授信科目(代碼)
			String loanTP = Util.nullToSpace(vauleToKeyMap.get(actcd));
			// 授信科目
			String subject = Util.nullToSpace(subItmeMap.get(actcd));
			// 放款帳號
			String loanNo = Util.nullToSpace(dataMap.get("ACCT_KEY"));
			// 前日結欠餘額(幣別)
			String balCurr = Util.nullToSpace(dataMap.get("CUR_CD"));
			// 前日結欠餘額(金額)
			BigDecimal balAmt = LMSUtil.toBigDecimal(dataMap.get("LN_BAL"));
			// 額度(幣別)
			String quotaCurr = Util.nullToSpace(dataMap.get("FACT_SWFT"));
			// 額度(金額)
			BigDecimal quotaAmt = LMSUtil.toBigDecimal(dataMap.get("FACT_AMT"));

			c241m01b.setActcd(actcd);
			c241m01b.setSubjectName(subject);
			c241m01b.setSubjectNo(loanTP);
			c241m01b.setQuotaNo(contract);
			c241m01b.setLoanNo(loanNo);
			c241m01b.setUpdater(user.getUserId());
			c241m01b.setCreator(user.getUserId());
			c241m01b.setUpdateTime(CapDate.getCurrentTimestamp());
			c241m01b.setCreateTime(CapDate.getCurrentTimestamp());
			// 30已在DWSQL被排除
			if (("31".equals(factType)) && (!dwbrNo.equals(brNo))) {
				c241m01b.setQuotaCurr(quotaCurr);
				c241m01b.setQuotaAmt(quotaAmt);
				c241m01b.setBalCurr(balCurr);
				c241m01b.setBalAmt(balAmt);
				c241m01bList.add(c241m01b);
			} else {
				if ("31".equals(factType) && dwbrNo.equals(brNo)) {
					// 如果符合以下條件,則有母戶
					// 子戶額度/餘額
					c241m01b.setSQuotaCurr(quotaCurr);
					c241m01b.setSQuotaAmt(quotaAmt);
					c241m01b.setBalCurr(balCurr);
					c241m01b.setSBalAmt(LMSUtil.toBigDecimal(balAmt));
					// 下SQL搜尋母戶
					c241m01b = this.findDwAslndavgovsjoinDwLnquotovSecond(
							custId, contract, c241m01b);
				} else {
					c241m01b.setQuotaCurr(quotaCurr);
					c241m01b.setQuotaAmt(quotaAmt);
					c241m01b.setBalCurr(balCurr);
					c241m01b.setBalAmt(balAmt);
				}
				// 額度建立日期
				c241m01b.setLNF020CrtDate((Date) dataMap.get("CREATE_DT"));
				// 帳務建立日期
				c241m01b.setLNF030CrtDate((Date) (dataMap.get("DW_DATA_SRC_DT")));
				// 授管處增加要求如果 帳務產生日期(DW_DATA_SRC_DT JASON說的) 大於 覆審名單產生日期(今天)
				// 時(因為覆審資料只會捉至名單產生時減1個月的資料)，
				// 預設值先設定為本次該筆帳務沒覆審

				// 帳務產生日期
				String ynReview = "N";
				Date openDataDate = (Date) (dataMap.get("BEG_DATE"));
				Date checkDataDate = Util.isEmpty(c241m01a) ? CapDate
						.getCurrentTimestamp() : c241m01a.getCreateTime();
				if (openDataDate != null && checkDataDate != null) {
					if (Integer.parseInt(TWNDate.toAD(openDataDate).replace(
							"-", "")) >= Integer.parseInt(TWNDate.toAD(
							checkDataDate).replace("-", ""))) {
						ynReview = "N";
					} else {
						ynReview = "Y";
					}
				} else {
					ynReview = "Y";
				}
				c241m01b.setLoanFDate((Date) (dataMap.get("BEG_DATE")));
				c241m01b.setLoanEDate((Date) (dataMap.get("END_DATE")));
				c241m01b.setUseFDate((Date) (dataMap.get("DURING_BG")));
				c241m01b.setUseEDate((Date) (dataMap.get("DURING_ED")));
				c241m01b.setYnReview(ynReview);
				c241m01b.setLnDataDate(CapDate.getCurrentTimestamp());
				// 循環註記
				c241m01b.setReVolve(Util.nullToSpace(dataMap.get("REVOVLE")));
				c241m01bList.add(c241m01b);
			}
		}
		if (!c241m01bList.isEmpty()) {
			// 一次儲存
			c241m01bDao.save(c241m01bList);
			result = true;
		}
		Map<String, Object> reMap = new HashMap<String, Object>();
		reMap.put("success", result);
		reMap.put("c241m01bList", c241m01bList);
		return reMap;

	}

	private C241M01B findDwAslndavgovsjoinDwLnquotovSecond(String custId,
			String contract, C241M01B c241m01b) {
		// 撈母額度
		Map<String, Object> dataMap = dwdbService
				.findDW_ASLNDAVGOVSJOIN_ByContractData(custId, contract);
		if (!dataMap.isEmpty()) {
			// 母戶額度/餘額
			c241m01b.setQuotaCurr(Util.nullToSpace(dataMap.get("FACT_SWFT")));
			c241m01b.setQuotaAmt(LMSUtil.toBigDecimal(dataMap.get("FACT_AMT")));
			c241m01b.setBalCurr(Util.nullToSpace(dataMap.get("CUR_CD")));
			c241m01b.setBalAmt(LMSUtil.toBigDecimal(dataMap.get("LN_BAL")));
		}
		return c241m01b;
	}

	@SuppressWarnings({ "unused", "rawtypes" })
	@Override
	public boolean updateElf491(String mainId, String custId, String dupNo,
			String branch, String doFix, String specifyCycle)
			throws ParseException {

		boolean check = false;

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		C241M01A c241m01a = findModelByMainId(C241M01A.class, mainId);
		if (c241m01a == null) {
			c241m01a = new C241M01A();
		}

		String tFlag1 = "";
		String tFlag2 = "";
		// int uCount = 0;
		String tuCount = "";
		String maincus = "";

		Date dateBaseStr = formatter.parse("0001-01-01");

		// 實際覆審日
		Date retrialDate = c241m01a.getRetrialDate();
		if (retrialDate == null) {
			// 實際覆審日="0001-01-01"
			retrialDate = dateBaseStr;
		}

		// 覆審類別
		String retrialKind = c241m01a.getRetrialKind();

		// 上次覆審日
		Date lastRetrialDate = c241m01a.getLastRetrialDate();

		if (lastRetrialDate == null) {
			// 上次覆審日="0001-01-01"
			lastRetrialDate = dateBaseStr;
		}
		// 下次覆審日
		Date shouldReviewDate = null;
		Map<String, Object> finalMap = new LinkedHashMap<String, Object>();
		finalMap.put("RULE_NO", c241m01a.getRetrialKind());
		finalMap.put("CUST_ID", c241m01a.getCustId());
		finalMap.put("DUPNO", c241m01a.getDupNo());
		finalMap.put("BR_NO", c241m01a.getOwnBrId());
		finalMap.put("CYC_MN", c241m01a.getRetrialDate());

		if (Util.isNotEmpty(retrialKind) && retrialKind.indexOf("99") > -1 && Util.isNotEmpty(specifyCycle)) {
			if ("12".equals(specifyCycle)) {
				maincus = "A";
			} else {
				maincus = specifyCycle;
			}
		}else{
			maincus = "";
		}
		finalMap.put("MAINCUST", maincus);
		Map<String, Object> map = null;
		map = lms2405Service.cauFinalDate(finalMap);
		// if ("N".equals(c241m01a.getNewCase())) {
		// map = lms2405Service.cauFinalDate(finalMap);
		// } else {
		// map = lms2405Service.cauFinalDateNew(finalMap);
		// }
		if (map != null) {
			shouldReviewDate = (Date) map.get("finalDate");
			
//			System.out.println(TWNDate.toAD(c241m01a.getRetrialDate()));
//			System.out.println(TWNDate.toAD(shouldReviewDate));
		}

		// 99類覆審週期 specifyCycle
		String specifyCycle99 = c241m01a.getSpecifyCycle();

		// updater
		String updater = c241m01a.getUpdater();

		// 本年度8-1已覆審註記
		List<?> rowFirstsel = this.lms491Service.find491BycustIdData(custId,
				dupNo, branch);

		int rowFirstselSize = rowFirstsel.size();

		if (rowFirstselSize == 0) {
			// 如覆審控制檔中無此筆資料則INSERT一筆
			boolean st = this.gfnDB2ELF492_R(branch, c241m01a);
			if (st) {
				check = true;
			}
		} else {
			Map flagdataMap = (Map) rowFirstsel.get(0);
			// 如有8-1規則，控制檔需有custid='XXXXXXXXXX',dupno='X'資料
			if (!Util.isEmpty(retrialKind) && retrialKind.indexOf("8-1") > -1) {
				tFlag1 = "Y";
				Map<String, Object> rowRemomo = this.lms491Service
						.findSelRemomo(branch);

				if (rowRemomo == null) {
					// 至491新增一筆覆審報告表資料
					this.lms491Service.insertNew491Data(branch);
					return true;
				}
				tuCount = ((String) rowRemomo.get("REMOMO")).substring(0, 12);
			} else {
				tFlag1 = "";
			}

			// 本年度8-1已覆審註記
			// String flag = (String) flagdataMap.get("FLAG_8_1");
			// if (flag != null) {
			// tFlag2 = "Y";
			// } else {
			// tFlag2 = "";
			// }
			tFlag2 = "";
			// 只會有一筆，如有多筆需刪除
			if("0".equals(maincus)){
				shouldReviewDate = null;
			}
			if (rowFirstselSize > 1) {
				String NCKDFLAG = Util.trim(flagdataMap.get("NCKDFLAG"));
				Date NCKDDATE = (Date) flagdataMap.get("NCKDDATE");
				String NCKDMEMO = Util.trim(flagdataMap.get("NCKDMEMO"));
				Date CANCELDT = (Date) flagdataMap.get("CANCELDT");
				String UCKDLINE = Util.trim(flagdataMap.get("UCKDLINE"));
				Date UCKDDT = (Date) flagdataMap.get("UCKDDT");
				String NEWFLAG = Util.trim(flagdataMap.get("NEWFLAG"));
				String FLAG_N_8_1 = Util.trim(flagdataMap.get("FLAG_N_8_1"));
				Date AGNT_DT = (Date) flagdataMap.get("AGNT_DT");
				BigDecimal AGNT_TWD_RT = (BigDecimal) flagdataMap
						.get("AGNT_TWD_RT");
				String AGNT_LOC_RT = (String) flagdataMap.get("AGNT_LOC_RT");
				this.lms491Service.deleteLMS491(branch, custId, dupNo);
				this.lms491Service.insertLMS491(branch, custId, dupNo,
						maincus, lastRetrialDate, retrialDate,
						shouldReviewDate, "", null, "",
						CANCELDT, updater, UCKDLINE, UCKDDT, Util.trim(c241m01a.getDocKind()),
						retrialKind, "", tFlag1, tFlag2, FLAG_N_8_1,
						AGNT_DT, AGNT_TWD_RT, AGNT_LOC_RT);
			} else {
				this.lms491Service.update491ByRetrialKind99(maincus,
						lastRetrialDate, retrialDate, Util.trim(c241m01a.getDocKind()), retrialKind,
						shouldReviewDate, tFlag1, tFlag2, updater, custId,
						dupNo, branch);
				// 更新491
//				if ("Y".equals(doFix)) {
//					this.lms491Service.update491ByRetrialKind99(specifyCycle,
//							lastRetrialDate, retrialDate, maincus, retrialKind,
//							shouldReviewDate, tFlag1, tFlag2, updater, custId,
//							dupNo, branch);
//				} else {
//					Map<String, Object> map491 = lms491Service.findNewSelAll(
//							branch, custId, dupNo);
//					if (map491 == null) {
//						map491 = new LinkedHashMap<String, Object>();
//					}
//					this.lms491Service.update491ByRetrialKind99(specifyCycle,
//							lastRetrialDate, retrialDate,
//							Util.trim(map491.get("MAINCUST")), retrialKind,
//							shouldReviewDate, tFlag1, tFlag2, updater, custId,
//							dupNo, branch);
//				}

			}
			if (Util.isNotEmpty(retrialKind) && retrialKind.indexOf("8-1") > -1
					&& !("Y".equals(specifyCycle))) {
				this.lms491Service.update491ByRetrialKind81(tuCount, updater,
						branch);
			}
			// 執行成功
			check = true;
		}// While END
			// Find 本年度8-1已覆審註記 End
		return check;

	}

	private boolean gfnDB2ELF492_R(String branch, C241M01A c241m01a)
			throws ParseException {

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		boolean st = false;
		// 新增覆審控制檔的欄位

		// 不覆審原因代碼
		// 不覆審日期
		// 不覆審備註
		// 不覆審註記
		// 上次覆審日
		// 下次覆審日
		// 資料修改人

		Date dateBaseStr = formatter.parse("0001-01-01");
		String custId = c241m01a.getCustId();
		String dupNo = c241m01a.getDupNo();

		// 上次覆審日
		Date lastRetrialDate = c241m01a.getLastRetrialDate();

		if (lastRetrialDate == null) {
			// 上次覆審日="0001-01-01"
			lastRetrialDate = dateBaseStr;
		}

		// 下次覆審日
		Date shouldReviewDate = c241m01a.getShouldReviewDate();
		// updater
		String updater = c241m01a.getUpdater();
		// 不覆審備註
		String nCkdMemo = c241m01a.getNCkdMemo();
		// 不覆審原因代碼
		String nCkdFlag = c241m01a.getNCkdFlag();
		// 不覆審註記
		String retrialYN = c241m01a.getRetrialYN();
		// 不覆審日期
		Date nCkdDate = c241m01a.getNCkdDate();

		if (nCkdDate == null) {
			// 不覆審日期="0001-01-01"
			nCkdDate = dateBaseStr;
		}

		// if ("Y".equals(c241m01a.getRetrialYN())) {
		this.lms491Service.insertNew491(custId, dupNo, branch, lastRetrialDate,
				shouldReviewDate, nCkdMemo, nCkdFlag, retrialYN, nCkdDate,
				updater);

		st = true;
		// }

		return st;
	}

	@Override
	public Page<Map<String, Object>> getBorrows(String mainId, ISearch search)
			throws CapException {
		Properties rptProperties = MessageBundleScriptCreator
				.getComponentResource(LMS2415R01RptServiceImpl.class);
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		C241M01A c241m01a = this.findModelByMainId(C241M01A.class, mainId);
		List<C241M01B> c241m01bList = this.findC241M01bList(mainId);

		data = new HashMap<String, Object>();
		data.put(
				"custName",
				Util.nullToSpace(c241m01a.getCustName()) + " "
						+ Util.nullToSpace(c241m01a.getCustId()) + " "
						+ Util.nullToSpace(c241m01a.getDupNo()));
		data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME1"));
		data.put("rptNo", "LMS2415R01");
		data.put("rpt", "R01");
		data.put("oid", Util.nullToSpace(c241m01a.getOid()));
		beanList.add(data);

		if (c241m01bList.size() > 3) {
			data = new HashMap<String, Object>();
			data.put(
					"custName",
					Util.nullToSpace(c241m01a.getCustName()) + " "
							+ Util.nullToSpace(c241m01a.getCustId()) + " "
							+ Util.nullToSpace(c241m01a.getDupNo()));
			data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME2"));
			data.put("rptNo", "LMS2415R02");
			data.put("rpt", "R02");
			data.put("oid", Util.nullToSpace(c241m01a.getOid()));
			beanList.add(data);
		}

		return new Page<Map<String, Object>>(beanList, beanList.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public List<C241M01C> findC241m01cByMainId(String mainId) {

		return c241m01cDao.findByMainId(mainId);
	}

	@Override
	public List<C241M01B> findC241m01bByMainId(String mainId) {

		return c241m01bDao.findByMainId(mainId);
	}

	@Override
	public List<C241M01E> findC241m01eByMainId(String mainId) {

		return c241m01eDao.findByMainId(mainId);
	}

	@Override
	public void save(List<C241M01C> list) {
		c241m01cDao.save(list);

	}

	@Override
	public List<C241M01B> findC241M01bList(String mainId) {
		return c241m01bDao.findByMainId(mainId);
	}

	/**
	 * 啟動flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */

	@Override
	public void startFlow(String oid, String flow_code) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		FlowInstance inst = flowService.createQuery().id(oid).query();

		if ("".equals(Util.nullToSpace(flow_code))) {
			flow_code = "LMS2415Flow";
		}
		if (inst == null) {
			logger.debug("流程啟動：流程代號[{}]OID[{}]", flow_code, oid);
			flowService.start(flow_code, oid, user.getUserId(),
					user.getUnitNo());
		} else {
			if (flow_code.equals(inst.getDefinition().getName())) {
				logger.warn("欲啟動之流程已存在：流程名稱[{}]OID[{}]", inst.getDefinition()
						.getName(), oid);
			} else {
				logger.error("尚未結束原流程：流程名稱[{}]OID[{}]", inst.getDefinition()
						.getName(), oid);
				throw new FlowException("FlowError: oid[" + oid + "]name["
						+ inst.getDefinition().getName() + "]");
			}
		}
	}

	/**
	 * 跑flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String oid, MegaSSOUserDetails user,
			HashMap<String, Object> data) throws CapException {
		FlowInstance inst = flowService.createQuery().id(oid).query();
		if (inst == null) {
			throw new RuntimeException("FlowInstance is null : OID[" + oid
					+ "]");
		}

		for (String key : data.keySet()) {
			inst.setAttribute(key, data.get(key));
		}
		inst.next();
	}

	@Override
	public void saveC241m01dList2(List<C241M01C> models) {
		if (models != null) {
			c241m01cDao.save(models);
		}
	}

	@Override
	public void deleteFromL241M01B(String[] oids) {
		for (String oid : oids) {
			this.delete(C241M01B.class, oid);
		}
	}

	@Override
	public void deleteCredit(String mainId) throws CapException {
		List<C241M01B> c241m01bList = this.findC241M01bList(mainId);
		for (C241M01B c241m01b : c241m01bList) {
			if (Util.isNotEmpty(c241m01b.getLnDataDate())) {
				this.delete(C241M01B.class, c241m01b.getOid());
			}
		}

	}

	@Override
	public Map<String, String> sumC241M01BAllAndSaveC241M01A(String brNo,
			String mainId, C241M01A c241m01a, List<C241M01B> c241m01bList,
			String lnDataDateResult) throws CapException {
		return this.sumC241M01BAllAndSaveC241M01A(brNo, mainId, c241m01a,
				c241m01bList, lnDataDateResult, true);
	}

	@Override
	public Map<String, String> sumC241M01BAllAndSaveC241M01A(String brNo,
			String mainId, C241M01A c241m01a, List<C241M01B> c241m01bList,
			String lnDataDateResult, boolean saveResult) throws CapException {
		Map<String, String> map = new LinkedHashMap<String, String>();
		// 匯率轉換處理
		BranchRate branchRate = lmsService.getBranchRate(brNo);
		BigDecimal totQuota = new BigDecimal(0);
		BigDecimal totBal = new BigDecimal(0);
		String totQuotaCurr = null;
		String totBalCurr = null;
		List<Map<String, Object>> currList = null;
		boolean brFlag = false;
		if (c241m01a != null) {
			totQuotaCurr = Util.nullToSpace(c241m01a.getTotQuotaCurr());
			totBalCurr = Util.nullToSpace(c241m01a.getTotBalCurr());
			// 國外分行匯率 2=國外
			if ("2".equals(branchService.getBranch(brNo).getBrNoFlag())) {
				brFlag = false;
			} else {
				currList = misRateblService.listNewest();
				brFlag = true;
			}
		} else {
		}
		if ("".equals(Util.trim(totQuotaCurr))) {
			totQuotaCurr = Util.nullToSpace(branchRate.getMCurr());
		}
		if ("".equals(Util.trim(totBalCurr))) {
			totBalCurr = Util.nullToSpace(branchRate.getMCurr());
		}
		HashSet<String> set = new HashSet<String>();
		for (C241M01B c241m01b : c241m01bList) {
			if (!set.contains(c241m01b.getQuotaNo())) {
				set.add(c241m01b.getQuotaNo());
				if (c241m01a != null) {
					// 台幣
					if (!brFlag) {
						if (c241m01b.getQuotaAmt() != null) {
							try {
								totQuota = totQuota.add(branchRate.toOtherAmt(
										c241m01b.getQuotaCurr(), totQuotaCurr,
										c241m01b.getQuotaAmt()));
							} catch (Exception e) {
								map = this.setNoRateCurrMap(map,
										c241m01b.getQuotaCurr());
							}
						}
					} else {
						if (currList == null) {
							currList = new ArrayList<Map<String, Object>>();
						}
						for (Map<String, Object> currMap : currList) {
							if (currMap.get("CURR").equals(
									c241m01b.getQuotaCurr())) {
								if (c241m01b.getQuotaAmt() != null) {
									try {
										totQuota = totQuota
												.add(c241m01b
														.getQuotaAmt()
														.multiply(
																new BigDecimal(
																		Util.nullToSpace(currMap
																				.get("ENDRATE")))));
									} catch (Exception e) {
										map = this.setNoRateCurrMap(map,
												c241m01b.getQuotaCurr());
									}
								}
							}
						}
					}
				}
			}
			if (c241m01a != null) {
				// 台幣
				if (!brFlag) {
					if (c241m01b.getBalAmt() != null) {
						try {
							totBal = totBal.add(branchRate.toOtherAmt(
									c241m01b.getBalCurr(), totBalCurr,
									c241m01b.getBalAmt()));
						} catch (Exception e) {
							map = this.setNoRateCurrMap(map,
									c241m01b.getBalCurr());
						}
					}

				} else {
					if (currList == null) {
						currList = new ArrayList<Map<String, Object>>();
					}
					for (Map<String, Object> currMap : currList) {
						if (currMap.get("CURR").equals(
								c241m01b.getQuotaCurr())) {
							if (c241m01b.getQuotaAmt() != null) {
								try {
									totQuota = totQuota
											.add(c241m01b
													.getQuotaAmt()
													.multiply(
															new BigDecimal(
																	Util.nullToSpace(currMap
																			.get("ENDRATE")))));
								} catch (Exception e) {
									map = this.setNoRateCurrMap(map,
											c241m01b.getQuotaCurr());
								}
							}
						}
					}
				}
			}
		}
		if (c241m01a != null) {
			c241m01a.setTotBal(totBal);
			c241m01a.setTotQuota(totQuota);
			if ("1".equals(lnDataDateResult)) {
				c241m01a.setLnDataDate(CapDate.getCurrentTimestamp());
			} else if ("2".equals(lnDataDateResult)) {
				c241m01a.setLnDataDate(null);
			}
			if (saveResult) {
				if("010".equals(c241m01a.getDocStatus())){
					this.save(c241m01a);
				}else{
					this.saveNoChangUpdate(c241m01a);
				}
			}
		}
		map.put("totBal",
				String.valueOf(totBal.setScale(2, BigDecimal.ROUND_HALF_UP)));
		map.put("totQuota",
				String.valueOf(totQuota.setScale(2, BigDecimal.ROUND_HALF_UP)));
		map.put("totBalCurr", Util.trim(c241m01a.getTotBalCurr()));
		map.put("totQuotaCurr", Util.trim(c241m01a.getTotQuotaCurr()));

		return map;
	}

	/**
	 * 將無法轉換匯率的幣別記錄到map
	 * 
	 * @param map
	 *            map
	 * @param currTotalMap
	 *            所有幣別
	 * @param l170m01b
	 *            l170m01b
	 * @return
	 */
	private Map<String, String> setNoRateCurrMap(Map<String, String> map,
			String curr) {
		if (map.get("noRateCurr") == null) {
			map.put("noRateCurr", curr);
		} else {
			if (!map.get("noRateCurr").contains(curr)) {
				map.put("noRateCurr", map.get("noRateCurr") + "、" + curr);// currTotalMap.get(curr)
			}

		}
		return map;
	}
	
	@SuppressWarnings({ "rawtypes" })
	@Override
	public C241M01A produceNewC241M01A(String custId, String dupNo, String custName) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C241M01A c241m01a = new C241M01A();
		String mainId = IDGenerator.getUUID();
		// 明細
		Map<String, Object> map = lms491Service.findNewSelAll(
				user.getUnitNo(), custId, dupNo);
		if (map != null) {
			c241m01a.setLastRetrialDate(Util.isEmpty(map.get("LRDATE")) ? null
					: CapDate.parseDate(Util.nullToSpace(map.get("LRDATE"))));
			String crDate = Util.nullToSpace(map.get("CRDATE"));
			if (crDate == null || crDate.isEmpty()
					|| "0001-01-01".equals(crDate)) {
				c241m01a.setShouldReviewDate(null);
			} else {
				c241m01a.setShouldReviewDate(CapDate.parseDate(crDate));
			}
		}
		c241m01a.setMainId(mainId);
		c241m01a.setTypCd(TypCdEnum.海外.getCode());
		c241m01a.setCustId(custId);
		c241m01a.setDupNo(dupNo);
		c241m01a.setCustName(custName);
		c241m01a.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
		c241m01a.setOwnBrId(user.getUnitNo());
		c241m01a.setCreator(user.getUserId());
		c241m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c241m01a.setUpdater(user.getUserId());
		c241m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		c241m01a.setRptId(getOVSLastC241M01ARptId());	//J-107-0128海外改格式
		c241m01a.setDocStatus(RetrialDocStatusEnum.編製中);
		c241m01a.setDocFmt(CrsUtil.DOCFMT_一般);

		if (lms2405Service.findstaff(custId)) {
			c241m01a.setStaff("Y");
		} else {
			c241m01a.setStaff("N");
		}
		String remomo = "99";
		// 規則為空塞99
		if (map != null) {
			remomo = Util.trim(map.get("REMOMO"));
		}
		c241m01a.setRetrialKind(remomo);
		c241m01a.setNCreatData(CrsUtil.C241M01A_NCREATEDATA_USR);
		String reportkind = "N";
		if (map != null) {
			reportkind = Util.nullToSpace(map.get("REPORTKIND"));
		}
		if ("N".equals(reportkind)) {
			c241m01a.setDocKind("N");
			c241m01a.setNewCase("Y");
		} else if ("O".equals(reportkind)) {
			c241m01a.setDocKind("N");
			c241m01a.setNewCase("N");
		} else if ("G".equals(reportkind)) {
			c241m01a.setDocKind("G");
			c241m01a.setNewCase("Y");
		} else if ("H".equals(reportkind)) {
			c241m01a.setDocKind("G");
			c241m01a.setNewCase("N");
		}
		c241m01a.setRetrialYN("Y");
		c241m01a.setTotBalCurr(this.branchService.getBranch(
				Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
		c241m01a.setTotQuotaCurr(this.branchService.getBranch(
				Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
		// 授權檔
		C241A01A c241a01a = new C241A01A();
		c241a01a.setMainId(c241m01a.getMainId());
		// 1.編製/移送
		c241a01a.setOwnUnit(user.getUnitNo());
		c241a01a.setAuthType("1");
		c241a01a.setAuthUnit(user.getUnitNo());
		c241a01aDao.save(c241a01a);
		save(c241m01a);
		return c241m01a;
	}
	
	@Override
	public void deleteC241M01AMainMark(String oid) {
		C241M01A c241m01a = c241m01aDao.findByOid(oid);
		c241m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		c241m01aDao.save(c241m01a);
	}

	@Override
	public void saveLinkToC240M01B(String c240MainId, String c241MainId) {
		C240M01B newC240m01b = new C240M01B();
		newC240m01b.setMainId(c240MainId);
		newC240m01b.setRefMainId(c241MainId);
		lms2405Service.save(newC240m01b);
	}

	@Override
	public String getOVSLastC241M01ARptId() {
		// return CrsUtil.V_201809	//009301海外改格式
		return CrsUtil.V_202209;	// J-111-0405,009763海外改格式
	}

}

 