/*
 * SearchSetting.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.dao.utils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <p>
 * SearchSetting .
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/9,iristu,new
 *          <li>2011/03/21,iristu,配合JPA修改SearchModeParameter中key的型態
 *          <li>2011/6/10,<PERSON><PERSON><PERSON><PERSON>, add method
 *          <li>2015/10/6,<PERSON>, 增加distinct設定欄位
 *          </ul>
 */
public abstract class AbstractSearchSetting implements ISearch {

    private static final long serialVersionUID = 1L;

    /**
     * Distinct欄位
     */
    private String[] distinctColumns;

    /**
     * Constructs a new search template and initializes.
     */
    public AbstractSearchSetting() {
    }

    /**
     * Constructs a new search template and initializes it with the values of the passed search template.
     * 
     * @param searchSetting
     *            searchSetting
     */
    public AbstractSearchSetting(AbstractSearchSetting searchSetting) {
        setOrderBy(searchSetting.getOrderBy());
        setFirstResult(searchSetting.getFirstResult());
        setMaxResults(searchSetting.getMaxResults());
    }

    /**
     * 實體名稱
     */
    String entityName;

    /*
     * 取得實體名
     */
    @Override
    public String getEntityName() {
        return entityName;
    }

    /*
     * 設置實體名
     */
    @Override
    public AbstractSearchSetting setEntityName(String entityName) {
        this.entityName = entityName;
        return this;
    }

    /**
     * 是否不重複, boolean
     */
    private boolean isDistinct;

    /**
     * <pre>
     * 是否distinct result
     * </pre>
     * 
     * @param isDistinct
     *            true/false
     */
    @Override
    public void setDistinct(boolean isDistinct) {
        this.isDistinct = isDistinct;
    }

    /**
     * <pre>
     * 是否distinct result
     * </pre>
     * 
     * @return boolean
     */
    @Override
    public boolean isDistinct() {
        return isDistinct;
    }

    /**
     * Order by support
     */
    private Map<String, Boolean> orderBy;

    /*
     * 是否有設置排序形式
     */
    @Override
    public boolean hasOrderBy() {
        return !(orderBy == null || orderBy.isEmpty());
    }

    /**
     * Specify that results must be ordered by the passed column Null by default. 預設為升羃排序
     * 
     * @param orderBy
     *            the order by
     * @return SearchSetting
     */
    @Override
    public AbstractSearchSetting addOrderBy(String orderBy) {
        if (this.orderBy == null) {
            this.orderBy = new LinkedHashMap<String, Boolean>();
        }
        this.orderBy.put(orderBy, false);
        return this;
    }

    /**
     * Specify that results must be ordered by the passed column Null by default.
     * 
     * @param orderBy
     *            orderBy
     * @param orderDesc
     *            是否要降羃排序
     * @return SearchSetting
     */
    @Override
    public AbstractSearchSetting addOrderBy(String orderBy, boolean orderDesc) {
        if (this.orderBy == null) {
            this.orderBy = new LinkedHashMap<String, Boolean>();
        }
        this.orderBy.put(orderBy, orderDesc);
        return this;
    }

    /*
     * 設置排序形式
     */
    @Override
    public AbstractSearchSetting setOrderBy(Map<String, Boolean> orderBy) {
        this.orderBy = orderBy;
        return this;
    }

    /*
     * 取得排序形式
     */
    @Override
    public Map<String, Boolean> getOrderBy() {
        return this.orderBy;
    }

    /**
     * Pagination support
     */
    private int maxResults = 100;

    /**
     * 設定查詢的筆數
     * 
     * @param maxResults
     * @return
     */
    public AbstractSearchSetting setMaxResults(int maxResults) {
        Validate.isTrue(maxResults > 0, "maxResults must be > 0");
        // this.maxResults = Math.min(maxResults, maxResultsLimit);
        this.maxResults = maxResults;
        return this;
    }

    /*
     * 取得查詢的筆數
     */
    @Override
    public int getMaxResults() {
        return maxResults;
    }

    /**
     * the first result
     */
    private int firstResult = 0;

    /**
     * 設定查詢的頁碼
     * 
     * @param firstResult
     * @return
     */
    @Override
    public AbstractSearchSetting setFirstResult(int firstResult) {
        Validate.isTrue(firstResult >= 0, "maxResults must be >= 0");
        this.firstResult = firstResult;
        return this;
    }

    /*
     * 取得查詢的頁碼
     */
    @Override
    public int getFirstResult() {
        return this.firstResult;
    }

    /**
     * Search mode support
     */
    private List<SearchModeParameter> searchModeParameters = new ArrayList<SearchModeParameter>();

    /*
     * 取得已設置查詢模式
     * 
     * @see tw.com.iisi.cap.dao.utils.ISearch#getSearchModeParameters()
     */
    @Override
    public List<SearchModeParameter> getSearchModeParameters() {
        return this.searchModeParameters;
    }

    /*
     * 設定查詢模式
     */
    @Override
    public AbstractSearchSetting addSearchModeParameters(SearchMode searchMode, Object key, Object value) {
        Validate.notNull(searchMode, "search mode must not be null");
        searchModeParameters.add(new SearchModeParameter(searchMode, key, value));
        return this;
    }

    /*
     * 設定查詢模式
     * 
     * @see tw.com.iisi.cap.dao.utils.ISearch#addSearchModeParameters(tw.com.iisi.cap.dao.utils.ISearch)
     */
    @Override
    public AbstractSearchSetting addSearchModeParameters(ISearch search) {
        this.searchModeParameters.addAll(search.getSearchModeParameters());
        return this;
    }

    /*
     * 設定Distinct欄位
     * 
     * @see tw.com.iisi.cap.dao.utils.ISearch#setDistinctColumn(java.lang.String[])
     */
    @Override
    public ISearch setDistinctColumn(String[] distinctColumns) {
        this.distinctColumns = distinctColumns;
        return this;
    }

    /*
     * 取得已設定的Distinct欄位
     * 
     * @see tw.com.iisi.cap.dao.utils.ISearch#getDistinctColumn()
     */
    @Override
    public String[] getDistinctColumn() {
        return this.distinctColumns;
    }

    /*
     * 轉換為字串
     * 
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

}
