
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 消金契約書 - 已核准
 * </pre>
 */
@Controller
@RequestMapping("/cls/cls3401v03")
public class CLS3401V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕
		list.add(LmsButtonEnum.Filter);
		list.add(LmsButtonEnum.View);
		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {

		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			
		}
		addToButtonPanel(model, list);

		// 加上Button
		renderJsI18N(CLS3401V01Page.class);
		renderJsI18N(CLS3401M01Page.class);
		renderJsI18N(CLS3401V03Page.class);

		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS3401V03Page');");
	}

}
