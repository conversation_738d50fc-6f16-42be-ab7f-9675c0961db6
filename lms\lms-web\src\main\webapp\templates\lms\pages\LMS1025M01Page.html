<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		<script type="text/javascript">
 			loadScript('pagejs/lms/LMS1025M01Page');
		</script>
		<div class="button-menu funcContainer" id="buttonPanel">
			
			<!--海外分行 編製中-->
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" ></span>
					<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
				</button>
			</th:block>
		
			<!--海外分行 待覆核-->
			<th:block th:if="${_btnWAIT_APPROVE_visible}">			
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" ></span>
					<th:block th:text="#{'button.check'}">覆核</th:block>
				</button>	
	        </th:block>
			
			<!--海外分行 已覆核-->
			<th:block th:if="${_btn_APPROVED_visible}">
				
				<button type="button" id="btnReturnToCompiling">
					<th:block th:text="#{'button.return'}">退回</th:block>		
					<th:block th:text="#{'docStatus.010'}">編製中</th:block>
				</button>
	        </th:block>
			
			<button type="button" id="btnPrint" class="forview">
				<span class="ui-icon ui-icon-jcs-03"></span>
				<th:block th:text="#{'button.print'}">列印</th:block>
			</button>						
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}">離開</th:block>
			</button>						
		</div>	
			<th:block th:if="${title_1_2_0}">
			 	<div class="tit2 color-black">
                	<th:block th:text="#{'doc.title01'}">消金信用評等表</th:block>：
                	(<span id="showTypCd" class="text-red"><th:block th:text="#{'doc.title02'}">澳洲</th:block></SPAN>) <span id="titleInfo" class="color-blue" ></span>
           		</div>
			</th:block>
			<th:block th:if="${title_3_0}">
				<div class="tit2 color-black">
                	<th:block th:text="#{'doc.title01'}">消金信用評等表</th:block>：
					(<span id="showTypCd" class="text-red"><th:block th:text="#{'doc.title03'}">其他地區</th:block></SPAN>) <span id="titleInfo" class="color-blue" ></span>
           		</div>
			</th:block>
			
		<div class="tabs doc-tabs">
            <ul>
                <li><a href="#tab-01" goto="01"><b><th:block th:text="#{'tab.01'}">文件資訊</th:block></b></a></li>
                <li><a href="#tab-02" goto="02"><b><th:block th:text="#{'tab.02'}">本案關係人基本資料</th:block></b></a></li>					
				<li><a href="#tab-03" goto="03"><b><th:block th:text="#{'tab.03'}">擔保品資料</th:block></b></a></li>
				<th:block th:if="${tab_VedaReport}">
					<li><a href="#tab-04" goto="04"><b><th:block th:text="#{'tab.04'}">Veda Report資訊</th:block></b></a></li>
				</th:block>
				<th:block th:if="${tab_Adjustment}">
            		<li><a href="#tab-05" goto="05"><b><th:block th:text="#{'tab.05'}">調整評等</th:block></b></a></li>
				</th:block>
				<li><a href="#tab-06" goto="06"><b><th:block th:text="#{'tab.06'}">評等等級</th:block></b></a></li>
            </ul>
            <div class="tabCtx-warp">
                <form id="tabForm">
					<div th:id="${tabIdx}" th:insert="${panelName} :: ${panelFragmentName}"></div>                
                </form>
            </div>
        </div>
		
		<div id="thickboxaddborrow" style="display: none;">
			<form id="addborrowForm">				
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrowA" checked="checked" value="1" onclick="$('._rborrowa').attr('disabled',false);$('._rborrowb').val('').attr('disabled',true);"/>
							<th:block th:text="#{'l120s01a.custid'}">身分證統編</th:block>&nbsp;&nbsp;						
						</td>
						<td width="80%">
							<input type="text" id="addborrowForm_custId" name="addborrowForm_custId" class="max upText _rborrowa" size="10" maxlength="10" />
							&nbsp;&nbsp; 
							<input type="hidden" id="addborrowForm_dupNo" name="addborrowForm_dupNo" value="">
							<button type="button" id="getCustData" name="getCustData">
								<span class="text-only"><th:block th:text="#{'tab02.btnImport'}">引進</th:block></span>
							</button>
						</td>
					</tr>
					<tr>
						<td width="20%" class="hd1">
							<input type="radio" name="rborrowA" value="2" onclick="$('._rborrowa').val('').attr('disabled',true);$('._rborrowb').attr('disabled',false);"/>
							<th:block th:text="#{'l120s01a.custname'}">借款人姓名</th:block>&nbsp;&nbsp;
						</td>
						<td width="80%">
							<input type="text" id="addborrowForm_custName" name="addborrowForm_custName" class="max _rborrowb" maxlength="120" disabled="true" /> &nbsp;&nbsp;							
						</td>
					</tr>
					<tr>
						<td colspan="2" class="text-red">
							<th:block th:text="#{'l120s01a.other16'}">說明...</th:block>							
						</td>
					</tr>
				</table>
			</form>
		</div>
		<div id='divPrint' style='display:none'>		
			<div id="printGrid">
			</div>
		</div>
		<div id='divAdjustReasonCfmMsg' style='display:none'>		
			<div id="adjustReasonCfmMsg">
			</div>
		</div>
		<div id='divAdjustReasonAlwaysCfmMsg' style='display:none'>		
			<div id="adjustReasonAlwaysCfmMsg">
			</div>
		</div>
		<div id='divEnterAdjustReason' style='display:none'>
			<form id="tmp_adjustReasonForm">
			<textarea id="tmp_adjustReason" name="tmp_adjustReason" cols="80" Rows="6" class=""  maxlength="300" maxlengthC="100"  >										
			</textarea>	
			</form>
		</div>
		<div id='div_chooseC120M01ACustPos' style='display:none'>		
			<form id='chooseC120M01ACustPosForm'>
				<input type='radio' id='chooseC120M01ACustPos' name='chooseC120M01ACustPos' codetype='lms1015_custPos' class="required"  itemStyle="size:1" >
				<br>&nbsp;
				<div id='div_choose_o_custRlt'><!-- 與主借人關係 -->
						<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" class="hd1">
								<th:block th:text="#{'l120s02.other13'}">關係類別</th:block>
							</td>
							<td width="70%">
								<select id="lms1015m01_custRlt_main" name="lms1015m01_custRlt_main" 
									codetype='lms1205s01_RelClass'  itemStyle="space:false" />
							</td>
						</tr>
						<tr>
							<td  class="hd1">
							&nbsp;
							</td>
							<td>
								<span id="lms1015m01_custRlt_main1" class='lms1015m01_custRlt_mainV' style="display:none;">
				                	<select name="lms1015m01_rationSelect1"  codetype='Relation_type1'  class='lms1015m01_custRlt_sel'></select>
				                </span>
				                
				                <span id="lms1015m01_custRlt_main2" class='lms1015m01_custRlt_mainV' style="display:none;">
				                    <select name="lms1015m01_rationSelect2"  codetype='Relation_type2'  class='lms1015m01_custRlt_sel'></select>
				                </span>
								
				                <span id="lms1015m01_custRlt_main3" class='lms1015m01_custRlt_mainV' style="display:none;">                    
				                    <select name="lms1015m01_rationSelect31" codetype='Relation_type31' class='lms1015m01_custRlt_sel'></select>
				                    <select name="lms1015m01_rationSelect32" codetype='Relation_type32' class='lms1015m01_custRlt_sel'></select>
				                </span>
											
							</td>
						</tr>
					</table>
				</div>			  
			</form>
		</div>
		
		<!-- include Panel -->
		<div th:include="base/panels/OverSeaCLSOuterPanel :: panelFragmentBody"></div>
		
		<!-- 呈主管覆核 -->
		<div id="selectBossBox" style="display:none;">
            <form id="selectBossForm">
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="hd1" width="60%">
                            <th:block th:text="#{'c121m01e.selectBoss'}"><!--  授信主管人數--></th:block>&nbsp;&nbsp;
                        </td>
                        <td width="40%">
                            <select id="numPerson" name="numPerson">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <th:block th:text="#{'c121m01e.bossId'}"><!--  授信主管--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <div>
                                <th:block th:text="#{'c121m01e.no'}"><!-- 第--></th:block>1<th:block th:text="#{'c121m01e.site'}"><!--  位--></th:block>
                                <th:block th:text="#{'c121m01e.bossId'}"><!--  授信主管--></th:block>&nbsp;&nbsp;
                                <select id="mainBoss" name="boss1" class="boss"></select>
                                <span id="newBossSpan" ></span>                                
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <th:block th:text="#{'c121m01e.managerId'}"><!--  經副襄理--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <select id="sManager" name="sManager" class="boss"></select>&nbsp;						
                        </td>
                    </tr>
                </table>
            </form>
        </div>
	</th:block>
</body>
</html>
