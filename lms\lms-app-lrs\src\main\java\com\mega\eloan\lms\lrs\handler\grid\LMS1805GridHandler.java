/* 
 * LMS1805GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.grid;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.Branch;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lrs.pages.LMS1805M01Page;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 覆審交易
 * </pre>
 * 
 * @since 2011/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,irene
 *          </ul>
 */
@Scope("request")
@Controller("lms1805gridhandler")
public class LMS1805GridHandler extends AbstractGridHandler {

	@Resource
	UserInfoService userservice;

	@Resource
	LMS1805Service service;

	@Resource
	BranchService branch;

	@Resource
	LMS1205Service service1205;

	Properties lms1805s02 = MessageBundleScriptCreator
			.getComponentResource(LMS1805M01Page.class);

	/**
	 * 查詢Grid 覆審名單檔 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 * @throws ParseException 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult query(ISearch pageSetting, PageParameters params) throws CapException, ParseException {
		
		String dataDate = Util.nullToSpace(params.getString("dataDate"));
		
		if (Util.isNotEmpty(dataDate)) {
			Date dataDateObj = new SimpleDateFormat("yyyy-MM-dd").parse(dataDate + "-01");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDateObj);
		}
		String brId = Util.nullToSpace(params.getString("brId"));
		if (Util.isNotEmpty(brId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					brId);
		}

		// 建立主要Search 條件
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l180a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		// if(!"".equals(custId)){
		// search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		// }

		Page page = service.findPage(L180M01A.class, pageSetting);
		List<L180M01A> list = page.getContent();
		for (L180M01A model : list) {
			// PEO 人工新增 SYS 系統新增
			if ("2".equals(model.getCreateBy())) {
				model.setCreateBy(lms1805s02.getProperty("createByPeople"));
			} else if ("1".equals(model.getCreateBy())) {
				model.setCreateBy(lms1805s02.getProperty("createByAuto"));
			}
			if (!Util.isEmpty(model.getUpdater())) {
				model.setUpdater(!Util.isEmpty(userservice.getUserName(model
						.getUpdater())) ? userservice.getUserName(model
						.getUpdater()) : model.getUpdater());
			}
			if (!Util.isEmpty(model.getCreator())) {
				model.setCreator(!Util.isEmpty(userservice.getUserName(model
						.getCreator())) ? userservice.getUserName(model
						.getCreator()) : model.getCreator());
			}
			model.setBranchId(model.getBranchId()
					+ " "
					+ Util.nullToSpace(branch.getBranchName(model.getBranchId())));
		}

		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢Grid 覆審名單明細 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryList(ISearch pageSetting, PageParameters params) throws CapException {

		// 建立主要Search 條件
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);

		pageSetting.addSearchModeParameters(search);

		// 加入換頁條件
		Page page = service.findPage(L180M01B.class, pageSetting);
		List<L180M01B> list = page.getContent();
		for (L180M01B model : list) {
			 if ("1".equals(model.getDocStatus1())
					 || "3".equals(model.getDocStatus1())) {
				 model.setDocStatus1("Y");
			 } else {
				 model.setDocStatus1("N");
			 }
			// PEO 人工新增 SYS 系統新增
			if ("PEO".equals(model.getCreateBY())) {
				model.setCreateBY(lms1805s02.getProperty("peopleNew"));
			} else {
				model.setCreateBY("--");
			}
			if ("0001-01-01".equals(TWNDate.toAD(model.getElfLRDate()))) {
				model.setElfLRDate(null);
			}
			// DBUOBU
			if (!UtilConstants.DEFAULT.是.equals(model.getElfDBUOBU())) {
				model.setElfDBUOBU(null);
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 搜尋分行
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult queryBank(ISearch pageSetting, PageParameters params) throws CapException {

		List<IBranch> bank = branch.getBranchByUnitType(BranchTypeEnum.海外分行
				.getCode());
		Page page = new Page(bank, 0, 0, 0);

		List<Branch> list = page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryL170M01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		// 建立主要Search 條件
		ISearch search = createSearchTemplete();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));
		L180M01A l180m01a = service.findModelByOid(L180M01A.class, oid);
		String branch = l180m01a.getBranchId();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branch);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				RetrialDocStatusEnum.已產生覆審名單報告檔.getCode());

		pageSetting.addSearchModeParameters(search);

		// 加入換頁條件
		Page page = service.findPage(L180M01A.class, pageSetting);
		List<L180M01A> list = page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 搜尋附加檔案
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryDocFile(ISearch pageSetting,
			PageParameters params) throws CapException {

		// 建立主要Search 條件
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);

		pageSetting.addSearchModeParameters(search);

		// 加入換頁條件
		Page page = service.findPage(DocFile.class, pageSetting);
		List<DocFile> list = page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 搜尋簽報書---列印批覆表用
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryL120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = params.getString("custId", "");
		// 建立主要Search 條件
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "docKind",
				UtilConstants.Casedoc.DocKind.授權外);
		search.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, CreditDocStatusEnum.海外_已核准.getCode());

		pageSetting.addSearchModeParameters(search);

		// 加入換頁條件
		Page page = service.findPage(L120M01A.class, pageSetting);
		List<L120M01A> list = page.getContent();
		Properties abstractPage = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		for (L120M01A l120m01a : list) {
			l120m01a.setTypCd(abstractPage.getProperty("typCd."
					+ l120m01a.getTypCd()));
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢需列印的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryPrint(ISearch pageSetting,
			PageParameters params) throws CapException {
		String printCondition = Util.nullToSpace(params
				.getString("printCondition"));
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = service1205.getBorrows(mainId,
				printCondition, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

}
