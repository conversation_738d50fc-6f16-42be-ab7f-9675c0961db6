<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<bean id="entityManagerFactory"
		class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
		<property name="dataSource" ref="dsELOANDB_COM" />

		<property name="persistenceXmlLocation" value="classpath:META-INF/persistence-common.xml" />
		<property name="jpaVendorAdapter">
			<bean class="org.springframework.orm.jpa.vendor.OpenJpaVendorAdapter">
				<property name="generateDdl" value="${jpa.ddl}" />
			</bean>
		</property>
		<property name="jpaDialect">
			<bean class="org.springframework.orm.jpa.vendor.OpenJpaDialect" />
		</property>
		<property name="jpaPropertyMap">
			<map>
				<entry key="openjpa.jdbc.Schema" value="${jpa.schema}" />
				<entry key="openjpa.jdbc.DBDictionary" value="${jpa.platform}" />
				<entry key="openjpa.RuntimeUnenhancedClasses" value="${jpa.runtimeEnhance}" />
				<entry key="openjpa.DynamicEnhancementAgent" value="${jpa.agent}" />
				<entry key="openjpa.Log" value="${jpa.log}" />
				<entry key="openjpa.ConnectionFactoryProperties" value="${jpa.log.properties}" />
				<entry key="openjpa.DataCache" value="${jpa.cache}" />
				<entry key="openjpa.QueryCache" value="${jpa.cache.query}" />
				<entry key="openjpa.RemoteCommitProvider" value="${jpa.cache.provider}" />
			</map>
		</property>
	</bean>
   <!-- <bean id="icbcrdbTxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dsICBCRDB" />
	</bean>
	-->
	<bean id="entityManager"
		class="org.springframework.orm.jpa.support.SharedEntityManagerBean">
		<property name="entityManagerFactory" ref="entityManagerFactory" />
	</bean>

	<bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
		<property name="entityManagerFactory" ref="entityManagerFactory" />
	</bean>

	<alias name="transactionManager" alias="txManager" />

	<tx:advice id="txAdvice" transaction-manager="txManager">
		<!-- the transactional semantics... -->
		<tx:attributes>
			<!-- all methods below are read-only -->
			<tx:method name="list*" timeout="45" read-only="true" />
			<tx:method name="find*" timeout="45" read-only="true" />
			<tx:method name="get*" timeout="45" read-only="true" />

			<!-- other methods use the default transaction settings (see below) -->
			<tx:method name="*" timeout="45" rollback-for="Throwable"
				propagation="REQUIRED" />

			<!-- timeout in seconds -->
		</tx:attributes>
	</tx:advice>

	<aop:aspectj-autoproxy proxy-target-class="true" />

	<!-- AOP Config -->
	<aop:config proxy-target-class="true">
		<aop:pointcut id="iisiServiceOperation"
			expression="execution(* com.mega.eloan..service.*.*(..))" />

		<aop:advisor advice-ref="txAdvice" pointcut-ref="iisiServiceOperation" />
	</aop:config>

</beans>