/* 
 * L140S10CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140S10CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S10C;

@Repository
public class L140S10CDaoImpl extends LMSJpaDao<L140S10C, String>
	implements L140S10CDao {

	@Override
	public L140S10C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S10C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140S10C> findByMainIdAndBizCat(String mainId, String bizCat) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140S10C> findByMainIdAndBizCatAndBizItem(String mainId, String bizCat, String bizItem) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizItem", bizItem);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140S10C> findByMainIdAndBizCatAndBizItemAndSequence(String mainId, String bizCat, String bizItem, int[] seqArray) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizItem", bizItem);
		search.addSearchModeParameters(SearchMode.IN, "seq", seqArray);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10C> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140S10C findMaxBizContentBy(String mainId, String bizCat, String bizItem) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizItem", bizItem);
		search.addOrderBy("contNo", true);
		return findUniqueOrNone(search);
	}
	
}