package com.mega.eloan.lms.base.constants;

public interface ContractDocConstants {
	interface WordML {
		static final String 換行符號 = "<w:br/>";
	}
	interface C340M01A_RptId {
		static final String V202003 = "V202003"; //J-109-0058購屋貸款
		static final String V202008 = "V202008";
		static final String V202206 = "V202206"; //J-111-0270 購屋貸款 
		static final String V202212 = "V202212";
		static final String V202304 = "V202304"; //J-112-0152 購屋貸款
		static final String V202401 = "V202401"; //J-113-0012
		static final String CtrType2_V202009 = "CtrType2_V202009"; //J-109-0366 信用貸款
		static final String CtrType2_V202010 = "CtrType2_V202010";
		static final String CtrType2_V202206 = "CtrType2_V202206"; //J-111-0270 信用貸款
		static final String CtrType2_V202212 = "CtrType2_V202212";
		static final String CtrType2_V202304 = "CtrType2_V202304"; //J-112-0152 信用貸款
		static final String CtrType2_V202401 = "CtrType2_V202401"; //J-113-0012 信用貸款
		static final String CtrType2_V202409 = "CtrType2_V202409"; //J-113-0296 信用貸款
		static final String CtrType3_V202008 = "CtrType3_V202008"; //J-109-0282 其他貸款(RptId 可能房貸、信貸都有V2020版，為了更細的區分)
		static final String CtrType3_V202206 = "CtrType3_V202206"; //J-111-0270 其他貸款
		static final String CtrType3_V202210 = "CtrType3_V202210"; //J-111-0502 其他貸款(111.10版)
		static final String CtrType3_V202212 = "CtrType3_V202212";
		static final String CtrType3_V202304 = "CtrType3_V202304"; //J-112-0152 其他貸款
		static final String CtrType3_V202401 = "CtrType3_V202401"; //J-113-0012 其他貸款
		static final String CtrTypeA_V202008 = "CtrTypeA_V202008";
		static final String CtrTypeA_V202011 = "CtrTypeA_V202011";
		static final String CtrTypeA_V202206 = "CtrTypeA_V202206";
        static final String CtrTypeA_V202209 = "CtrTypeA_V202209";
        static final String CtrTypeA_V202212 = "CtrTypeA_V202212";
        static final String CtrTypeA_V202304 = "CtrTypeA_V202304"; //J-112-0152 線上對保契約書
		static final String CtrTypeA_V202307 = "CtrTypeA_V202307"; //J-112-0205
		static final String CtrTypeA_V202401 = "CtrTypeA_V202401";
		static final String CtrTypeB_V202309 = "CtrTypeB_V202309";
		static final String CtrTypeB_V202401 = "CtrTypeB_V202401";
		static final String CtrTypeL_V202106 = "CtrTypeA_V202106";
	}
	interface C340M01A_CtrType {
		static final String Type_1 = "1"; //1-購屋契約						CLS3401M01Page
		static final String Type_2 = "2"; //2-信用貸款(DBR22)				CLS3401M04Page
		static final String Type_3 = "3"; //3-其他類契約						CLS3401M03Page
		static final String Type_4 = "4"; //4-歡喜樂活貸款契約書(以房養老)
		static final String Type_A = "A"; //A-線上對保契約書					CLS3401M02Page
		static final String Type_B = "B"; //B-線上房貸增貸對保契約書					CLS3401M08Page
		static final String Type_C = "C"; //C-中鋼集團消貸線上契約 			CLS3401M07Page
		static final String Type_S = "S"; //S-線上對保擔保品提供人契約		CLS3401M05Page
		static final String Type_L = "L"; //L-線上對保契約書勞工紓困			CLS3401M06Page
	}
	interface C340M01C_ItemType { // 在儲存時，是否依不同的 itemType 區分成不同的 JSONObject
		static final String TYPE_0 = "0"; //PLOAN線上對保 init
		static final String TYPE_1 = "1"; //PLOAN線上對保 Save原始資料
		static final String TYPE_9 = "9"; //PLOAN對保後回傳
	}
	
	interface CtrType1 { //排除 <w:bookmarkStart w:id="..." w:name="_GoBack"/>
		static final String ELOAN_P1_CONTR_NO = "eloan_p1_contr_no";
		static final String ELOAN_P1_CONTR_CNAME = "eloan_p1_contr_cname";
		static final String ELOAN_P1_CONTR_NAME_M = "eloan_p1_contr_name_m";
		static final String ELOAN_P1_CONTR_NAME_N = "eloan_p1_contr_name_n";
		static final String ELOAN_P1_CONTR_NAME_G = "eloan_p1_contr_name_g";
		static final String ELOAN_P1_CONTR_AMT = "eloan_p1_contr_amt";
		
		static final String ELOAN_PA_DELIV_A_CB = "eloan_pa_deliv_A_cb";
		static final String ELOAN_PA_DELIV_A_T1 = "eloan_pa_deliv_A_t1";
		static final String ELOAN_PA_DELIV_A_T2 = "eloan_pa_deliv_A_t2";
		static final String ELOAN_PA_DELIV_B_CB = "eloan_pa_deliv_B_cb";
		static final String ELOAN_PA_DELIV_B_T1 = "eloan_pa_deliv_B_t1";
		static final String ELOAN_PA_DELIV_B_T2 = "eloan_pa_deliv_B_t2";
		static final String ELOAN_PA_DELIV_B_T3 = "eloan_pa_deliv_B_t3";
		static final String ELOAN_PA_DELIV_C_CB = "eloan_pa_deliv_C_cb";
		static final String ELOAN_PA_DELIV_D_CB = "eloan_pa_deliv_D_cb";
		static final String ELOAN_PA_DELIV_D_T1 = "eloan_pa_deliv_D_t1";
		static final String ELOAN_PA_DELIV_D_T2 = "eloan_pa_deliv_D_t2";
		static final String ELOAN_PA_DELIV_D1_T1  = "eloan_pa_deliv_D1_t1"; //地址分成多段，前端UI只有1行
		static final String ELOAN_PA_DELIV_D1_T1A = "eloan_pa_deliv_D1_t1A";
		static final String ELOAN_PA_DELIV_D1_T1B = "eloan_pa_deliv_D1_t1B";
		static final String ELOAN_PA_DELIV_D1_T2  = "eloan_pa_deliv_D1_t2";
		static final String ELOAN_PA_DELIV_D1_T3  = "eloan_pa_deliv_D1_t3";
		static final String ELOAN_PA_DELIV_D1_T4  = "eloan_pa_deliv_D1_t4";
		static final String ELOAN_PA_DELIV_D1_T5  = "eloan_pa_deliv_D1_t5";
		static final String ELOAN_PA_DELIV_D1_T6  = "eloan_pa_deliv_D1_t6";
		static final String ELOAN_PA_DELIV_D1_T7  = "eloan_pa_deliv_D1_t7";
		static final String ELOAN_PA_DELIV_D2_1CB = "eloan_pa_deliv_D2_1cb";
		static final String ELOAN_PA_DELIV_D2_2CB = "eloan_pa_deliv_D2_2cb";
		static final String ELOAN_PA_DELIV_D3_T1 = "eloan_pa_deliv_D3_t1";
		static final String ELOAN_PA_DELIV_E_CB = "eloan_pa_deliv_E_cb";
		static final String ELOAN_PA_DELIV_E_T1 = "eloan_pa_deliv_E_t1";
		static final String ELOAN_PA_USE_A_CB = "eloan_pa_use_A_cb";
		static final String ELOAN_PA_USE_A_T1 = "eloan_pa_use_A_t1";
		static final String ELOAN_PA_USE_A_T2 = "eloan_pa_use_A_t2";
		static final String ELOAN_PA_USE_B_CB = "eloan_pa_use_B_cb";
		static final String ELOAN_PA_USE_B_T1 = "eloan_pa_use_B_t1";
		static final String ELOAN_PA_USE_Y = "eloan_pa_use_y";
		static final String ELOAN_PA_USE_M = "eloan_pa_use_m";
		static final String ELOAN_PA_USE_BEGY = "eloan_pa_use_begY";
		static final String ELOAN_PA_USE_BEGM = "eloan_pa_use_begM";
		static final String ELOAN_PA_USE_BEGD = "eloan_pa_use_begD";
		static final String ELOAN_PA_USE_ENDY = "eloan_pa_use_endY";
		static final String ELOAN_PA_USE_ENDM = "eloan_pa_use_endM";
		static final String ELOAN_PA_USE_ENDD = "eloan_pa_use_endD";
		static final String ELOAN_PA_REPAY_A_CB = "eloan_pa_repay_A_cb";
		static final String ELOAN_PA_REPAY_B_CB = "eloan_pa_repay_B_cb";
		static final String ELOAN_PA_REPAY_C_CB = "eloan_pa_repay_C_cb";
		static final String ELOAN_PA_REPAY_C_T1 = "eloan_pa_repay_C_t1";
		static final String ELOAN_PA_REPAY_C_T2 = "eloan_pa_repay_C_t2";
		static final String ELOAN_PA_REPAY_C_T3 = "eloan_pa_repay_C_t3";
		static final String ELOAN_PA_REPAY_C_T4 = "eloan_pa_repay_C_t4";
		static final String ELOAN_PA_REPAY_D_CB = "eloan_pa_repay_D_cb";
		static final String ELOAN_PA_REPAY_D_T1 = "eloan_pa_repay_D_t1";
		static final String ELOAN_PA_REPAY_D_T2 = "eloan_pa_repay_D_t2";
		static final String ELOAN_PA_REPAY_D_T3 = "eloan_pa_repay_D_t3";
		static final String ELOAN_PA_REPAY_D_T4 = "eloan_pa_repay_D_t4";
		static final String ELOAN_PA_REPAY_E_CB = "eloan_pa_repay_E_cb";
		static final String ELOAN_PA_REPAY_E_T1 = "eloan_pa_repay_E_t1";
		static final String ELOAN_PA_REPAY_E_T2 = "eloan_pa_repay_E_t2";
		static final String ELOAN_PA_REPAY_F_CB = "eloan_pa_repay_F_cb";
		static final String ELOAN_PA_REPAY_F_T1 = "eloan_pa_repay_F_t1";
		static final String ELOAN_PA_REPAY_NOPPP_CB = "eloan_pa_repay_noPPP_cb";
		static final String ELOAN_PA_REPAY_WITHPPP_CB = "eloan_pa_repay_withPPP_cb";
		static final String ELOAN_PA_REPAY_ACTNO = "eloan_pa_repay_actNo";
		static final String ELOAN_PA_REPAY_FEENO01 = "eloan_pa_repay_feeNo01";
		static final String ELOAN_PA_REPAY_FEENO02 = "eloan_pa_repay_feeNo02";
		static final String ELOAN_PA_REPAY_FEENO03 = "eloan_pa_repay_feeNo03";
		static final String ELOAN_PA_REPAY_FEENO04 = "eloan_pa_repay_feeNo04";
		static final String ELOAN_PA_REPAY_FEENO06 = "eloan_pa_repay_feeNo06";
		static final String ELOAN_PA_REPAY_FEENO07 = "eloan_pa_repay_feeNo07";
		static final String ELOAN_PA_INTR_NOPPP_CB = "eloan_pa_intr_noPPP_cb";
		static final String ELOAN_PA_INTR_NOPPP_BASERATE = "eloan_pa_intr_noPPP_baseRate";
		static final String ELOAN_PA_INTR_NOPPP_WAY = "eloan_pa_intr_noPPP_way";
		static final String ELOAN_PA_INTR_NOPPP_1T1 = "eloan_pa_intr_noPPP_1t1";
		static final String ELOAN_PA_INTR_NOPPP_1T2 = "eloan_pa_intr_noPPP_1t2";
		static final String ELOAN_PA_INTR_NOPPP_2T1 = "eloan_pa_intr_noPPP_2t1";		
		static final String ELOAN_PA_INTR_NOPPP_3T1 = "eloan_pa_intr_noPPP_3t1";
		static final String ELOAN_PA_INTR_WITHPPP_CB = "eloan_pa_intr_withPPP_cb";
		static final String ELOAN_PA_INTR_WITHPPP_BASERATE = "eloan_pa_intr_withPPP_baseRate";
		static final String ELOAN_PA_INTR_WITHPPP_WAY = "eloan_pa_intr_withPPP_way";
		static final String ELOAN_PA_INTR_WITHPPP_1X1 = "eloan_pa_intr_withPPP_1x1";
		static final String ELOAN_PA_INTR_WITHPPP_1X2 = "eloan_pa_intr_withPPP_1x2";
		static final String ELOAN_PA_INTR_WITHPPP_1X3 = "eloan_pa_intr_withPPP_1x3";
		static final String ELOAN_PA_INTR_WITHPPP_1Y1 = "eloan_pa_intr_withPPP_1y1";
		static final String ELOAN_PA_INTR_WITHPPP_1Y2 = "eloan_pa_intr_withPPP_1y2";
		static final String ELOAN_PA_INTR_WITHPPP_1Y3 = "eloan_pa_intr_withPPP_1y3";
		static final String ELOAN_PA_INTR_WITHPPP_1Y4 = "eloan_pa_intr_withPPP_1y4";
		static final String ELOAN_PA_INTR_WITHPPP_1Y5 = "eloan_pa_intr_withPPP_1y5";
		static final String ELOAN_PA_INTR_WITHPPP_1Y6 = "eloan_pa_intr_withPPP_1y6";
		static final String ELOAN_PA_INTR_WITHPPP_1Y7 = "eloan_pa_intr_withPPP_1y7";
		static final String ELOAN_PA_INTR_WITHPPP_1Y8 = "eloan_pa_intr_withPPP_1y8";		
		static final String ELOAN_PA_INTR_WITHPPP_2T1 = "eloan_pa_intr_withPPP_2t1";
		static final String ELOAN_PA_INTR_OTHER_CB = "eloan_pa_intr_other_cb";
		static final String ELOAN_PA_INTR_OTHER_T1 = "eloan_pa_intr_other_t1";
		
		static final String ELOAN_PB_GNTEEG_CB = "eloan_pb_gnteeG_cb";
		static final String ELOAN_PB_GNTEEG_B_CB = "eloan_pb_gnteeG_B_cb"; //B:非自用住宅放款(不循環動用)
		static final String ELOAN_PB_GNTEEG_B_T1 = "eloan_pb_gnteeG_B_t1";
		static final String ELOAN_PB_GNTEEG_C_CB = "eloan_pb_gnteeG_C_cb"; //C:歡喜理財家或綜合理財房屋貸款（循環及不循環動用）
		static final String ELOAN_PB_GNTEEG_C_T1 = "eloan_pb_gnteeG_C_t1";
		static final String ELOAN_PB_GNTEEN_CB = "eloan_pb_gnteeN_cb";
		static final String ELOAN_PB_GNTEEN_A_CB = "eloan_pb_gnteeN_A_cb"; //A:自用住宅放款（不循環動用）
		static final String ELOAN_PB_GNTEEN_B_CB = "eloan_pb_gnteeN_B_cb";
		static final String ELOAN_PB_GNTEEN_B_T1 = "eloan_pb_gnteeN_B_t1";
		static final String ELOAN_PB_GNTEEN_C_CB = "eloan_pb_gnteeN_C_cb";
		static final String ELOAN_PB_GNTEEN_C_T1 = "eloan_pb_gnteeN_C_t1";
		
		static final String ELOAN_PC_SERV_TEL_CB = "eloan_pc_serv_tel_cb";
		static final String ELOAN_PC_SERV_TEL_T1 = "eloan_pc_serv_tel_t1";
		static final String ELOAN_PC_SERV_FAX_CB = "eloan_pc_serv_fax_cb";
		static final String ELOAN_PC_SERV_FAX_T1 = "eloan_pc_serv_fax_t1";
		static final String ELOAN_PC_SERV_MAIL_CB = "eloan_pc_serv_mail_cb";
		static final String ELOAN_PC_SERV_MAIL_T1 = "eloan_pc_serv_mail_t1";
		static final String ELOAN_PC_SERV_URL_CB = "eloan_pc_serv_url_cb";
		static final String ELOAN_PC_SERV_CALL_CB = "eloan_pc_serv_call_cb";
		static final String ELOAN_PC_SERV_OTHER_CB = "eloan_pc_serv_other_cb";
		static final String ELOAN_PC_SERV_OTHER_T1 = "eloan_pc_serv_other_t1";
		static final String ELOAN_PC_COURT_LOC = "eloan_pc_court_loc";
		static final String ELOAN_PC_COPY_CNT = "eloan_pc_copy_cnt";
		static final String ELOAN_PC_SPTERM_YRATE = "eloan_pc_spTerm_yRate";
		static final String ELOAN_PC_SPTERM_NOTE = "eloan_pc_spTerm_note";
		static final String ELOAN_PC_GNTEE_CB = "eloan_pc_gntee_cb";
		
		static final String ELOAN_P9_SIG_M_NAME = "eloan_p9_sig_m_name";
		static final String ELOAN_P9_SIG_M_CUSTID = "eloan_p9_sig_m_custId";
		static final String ELOAN_P9_SIG_M_ADDR = "eloan_p9_sig_m_addr";
		static final String ELOAN_P9_SIG_1N_CB = "eloan_p9_sig_1N_cb";
		static final String ELOAN_P9_SIG_1N_NAME = "eloan_p9_sig_1N_name";
		static final String ELOAN_P9_SIG_1G_CB = "eloan_p9_sig_1G_cb";
		static final String ELOAN_P9_SIG_1G_NAME = "eloan_p9_sig_1G_name";
		static final String ELOAN_P9_SIG_1_CUSTID = "eloan_p9_sig_1_custId";
		static final String ELOAN_P9_SIG_1_ADDR = "eloan_p9_sig_1_addr";
		static final String ELOAN_P9_SIG_2N_CB = "eloan_p9_sig_2N_cb";
		static final String ELOAN_P9_SIG_2N_NAME = "eloan_p9_sig_2N_name";
		static final String ELOAN_P9_SIG_2G_CB = "eloan_p9_sig_2G_cb";
		static final String ELOAN_P9_SIG_2G_NAME = "eloan_p9_sig_2G_name";
		static final String ELOAN_P9_SIG_2_CUSTID = "eloan_p9_sig_2_custId";
		static final String ELOAN_P9_SIG_2_ADDR = "eloan_p9_sig_2_addr";
		static final String ELOAN_P9_SIG_PARTYB_AGENT = "eloan_p9_sig_partyB_agent";
		static final String ELOAN_P9_SIG_PARTYB_ADDR = "eloan_p9_sig_partyB_addr";
		static final String ELOAN_P9_RECPT_M_NAME = "eloan_p9_recpt_m_name";
		static final String ELOAN_P9_RECPT_S_NAME = "eloan_p9_recpt_s_name";

		static final String ELOAN_P1_RPRINTMODE2_R1 = "eloan_p1_printMode2_r1";
		static final String ELOAN_P1_RPRINTMODE2_R2 = "eloan_p1_printMode2_r2";
		static final String ELOAN_P1_RPRINTMODE2_R3 = "eloan_p1_printMode2_r3";
		static final String ELOAN_P1_RPRINTMODE2_R4 = "eloan_p1_printMode2_r4";
		static final String ELOAN_P1_RPRINTMODE2_R5 = "eloan_p1_printMode2_r5";

		static final String ELOAN_P8_expense_year = "eloan_p8_expense_year";
		static final String ELOAN_P8_expense_mth = "eloan_p8_expense_mth";
		static final String ELOAN_P8_expense_day = "eloan_p8_expense_day";
	}

	interface CtrType2 {
		static final String ELOAN_P1_CONTR_NO = "eloan_p1_contr_no";
		static final String ELOAN_P1_CONTR_CNAME = "eloan_p1_contr_cname";
		static final String ELOAN_P1_CONTR_NAME_M = "eloan_p1_contr_name_m";
		static final String ELOAN_P1_CONTR_NAME_N = "eloan_p1_contr_name_n";
		static final String ELOAN_P1_CONTR_NAME_G = "eloan_p1_contr_name_g";
		static final String ELOAN_P1_CONTR_AMT = "eloan_p1_contr_amt";
		
		static final String ELOAN_PA_DELIV_A_CB = "eloan_pa_deliv_A_cb";
		static final String ELOAN_PA_DELIV_A_T1 = "eloan_pa_deliv_A_t1";
		static final String ELOAN_PA_DELIV_A_T2 = "eloan_pa_deliv_A_t2";
		static final String ELOAN_PA_DELIV_B_CB = "eloan_pa_deliv_B_cb";
		static final String ELOAN_PA_DELIV_C_CB = "eloan_pa_deliv_C_cb";
		static final String ELOAN_PA_DELIV_C_T1 = "eloan_pa_deliv_C_t1";
		static final String ELOAN_PA_DELIV_D_CB = "eloan_pa_deliv_D_cb";
		static final String ELOAN_PA_DELIV_D_T1 = "eloan_pa_deliv_D_t1";
		
		static final String ELOAN_PA_PURPOSE_WAY = "eloan_pa_purpose_way"; // L120M01A.purpose // where codeType='cls1141_purpose'
		static final String ELOAN_PA_PURPOSE_E_T1 = "eloan_pa_purpose_E_t1"; // L120M01A.purposeOth
		
		static final String ELOAN_PA_USE_WAY = "eloan_pa_use_way";
		static final String ELOAN_PA_USE_A_T1 = "eloan_pa_use_A_t1"; //一次撥付型
		static final String ELOAN_PA_USE_A_T2 = "eloan_pa_use_A_t2";		
		static final String ELOAN_PA_USE_A_T3 = "eloan_pa_use_A_t3";
		static final String ELOAN_PA_USE_A_T4 = "eloan_pa_use_A_t4";
		static final String ELOAN_PA_USE_A_T5 = "eloan_pa_use_A_t5";		
		static final String ELOAN_PA_USE_A_T6 = "eloan_pa_use_A_t6";
		
		static final String ELOAN_PA_USE_B_T1 = "eloan_pa_use_B_t1"; //循環動用型(憑借款支用書)
		static final String ELOAN_PA_USE_B_T2 = "eloan_pa_use_B_t2";
		static final String ELOAN_PA_USE_B_T3 = "eloan_pa_use_B_t3";
		static final String ELOAN_PA_USE_B_T4 = "eloan_pa_use_B_t4";
		static final String ELOAN_PA_USE_B_T5 = "eloan_pa_use_B_t5";
		static final String ELOAN_PA_USE_B_T6 = "eloan_pa_use_B_t6";
		static final String ELOAN_PA_USE_B_T7 = "eloan_pa_use_B_t7";
		static final String ELOAN_PA_USE_B_T8 = "eloan_pa_use_B_t8";
		
		static final String ELOAN_PA_USE_C_T1 = "eloan_pa_use_C_t1"; //循環動用型(金融卡、網路銀行或存摺與取款憑條)
		static final String ELOAN_PA_USE_C_T2 = "eloan_pa_use_C_t2";
		static final String ELOAN_PA_USE_C_T3 = "eloan_pa_use_C_t3";
		static final String ELOAN_PA_USE_C_T4 = "eloan_pa_use_C_t4";
		static final String ELOAN_PA_USE_C_T5 = "eloan_pa_use_C_t5";
		static final String ELOAN_PA_USE_C_T6 = "eloan_pa_use_C_t6";
		static final String ELOAN_PA_USE_C_T7 = "eloan_pa_use_C_t7";
		static final String ELOAN_PA_USE_C_T8 = "eloan_pa_use_C_t8";
		static final String ELOAN_PA_USE_C_T9 = "eloan_pa_use_C_t9";

		static final String ELOAN_PA_USE_D_T1 = "eloan_pa_use_D_t1"; //循環動用型(第xyz號存款帳戶之支票委託乙方付款)
		static final String ELOAN_PA_USE_D_T2 = "eloan_pa_use_D_t2";
		static final String ELOAN_PA_USE_D_T3 = "eloan_pa_use_D_t3";
		static final String ELOAN_PA_USE_D_T4 = "eloan_pa_use_D_t4";
		static final String ELOAN_PA_USE_D_T5 = "eloan_pa_use_D_t5";
		static final String ELOAN_PA_USE_D_T6 = "eloan_pa_use_D_t6";
		static final String ELOAN_PA_USE_D_T7 = "eloan_pa_use_D_t7";
		static final String ELOAN_PA_USE_D_T8 = "eloan_pa_use_D_t8";
		static final String ELOAN_PA_USE_D_T9 = "eloan_pa_use_D_t9";
		
		static final String ELOAN_PA_USE_E_T1 = "eloan_pa_use_E_t1"; 
		//~~~~~~
		static final String ELOAN_PA_REPAY_WAY = "eloan_pa_repay_way";
		static final String ELOAN_PA_REPAY_A_T1 = "eloan_pa_repay_A_t1";
		static final String ELOAN_PA_REPAY_A_T2 = "eloan_pa_repay_A_t2";
		static final String ELOAN_PA_REPAY_A_T3 = "eloan_pa_repay_A_t3";
		static final String ELOAN_PA_REPAY_C_T1 = "eloan_pa_repay_C_t1";
		static final String ELOAN_PA_REPAY_C_T2 = "eloan_pa_repay_C_t2";
		static final String ELOAN_PA_REPAY_C_T3 = "eloan_pa_repay_C_t3";
		static final String ELOAN_PA_REPAY_C_T4 = "eloan_pa_repay_C_t4";
		static final String ELOAN_PA_REPAY_D_T1 = "eloan_pa_repay_D_t1";
		
		static final String ELOAN_PA_REPAY_NOPPP_CB = "eloan_pa_repay_noPPP_cb";
		static final String ELOAN_PA_REPAY_WITHPPP_CB = "eloan_pa_repay_withPPP_cb";
		static final String ELOAN_PA_REPAY_WITHPPP_TERM = "eloan_pa_repay_withPPP_term";
		static final String ELOAN_PA_REPAY_ACTNO = "eloan_pa_repay_actNo";
		static final String ELOAN_PA_REPAY_FEENO01 = "eloan_pa_repay_feeNo01";
		static final String ELOAN_PA_REPAY_FEENO02 = "eloan_pa_repay_feeNo02";
		static final String ELOAN_PA_REPAY_FEENO03 = "eloan_pa_repay_feeNo03";
		static final String ELOAN_PA_REPAY_FEENO04 = "eloan_pa_repay_feeNo04";
		static final String ELOAN_PA_REPAY_FEENO06 = "eloan_pa_repay_feeNo06";
		//信貸契約 無｛補發抵押權塗銷同意書｝及｛補發費用｝
		
		static final String ELOAN_PA_INTR_WITHPPP_CB = "eloan_pa_intr_withPPP_cb";
		static final String ELOAN_PA_INTR_WITHPPP_BASERATE = "eloan_pa_intr_withPPP_baseRate";
		static final String ELOAN_PA_INTR_WITHPPP_WAY = "eloan_pa_intr_withPPP_way";
		static final String ELOAN_PA_INTR_WITHPPP_1X1 = "eloan_pa_intr_withPPP_1x1";
		static final String ELOAN_PA_INTR_WITHPPP_1X2 = "eloan_pa_intr_withPPP_1x2";
		static final String ELOAN_PA_INTR_WITHPPP_1X3 = "eloan_pa_intr_withPPP_1x3";
		static final String ELOAN_PA_INTR_WITHPPP_1Y1 = "eloan_pa_intr_withPPP_1y1";
		static final String ELOAN_PA_INTR_WITHPPP_1Y2 = "eloan_pa_intr_withPPP_1y2";
		static final String ELOAN_PA_INTR_WITHPPP_1Y3 = "eloan_pa_intr_withPPP_1y3";
		// static final String ELOAN_PA_INTR_WITHPPP_1Y4 = "eloan_pa_intr_withPPP_1y4";
		static final String ELOAN_PA_INTR_WITHPPP_1Y5 = "eloan_pa_intr_withPPP_1y5";
		static final String ELOAN_PA_INTR_WITHPPP_1Y6 = "eloan_pa_intr_withPPP_1y6";
		static final String ELOAN_PA_INTR_WITHPPP_1Y7 = "eloan_pa_intr_withPPP_1y7";
		// static final String ELOAN_PA_INTR_WITHPPP_1Y8 = "eloan_pa_intr_withPPP_1y8";		
		static final String ELOAN_PA_INTR_WITHPPP_1Y9 = "eloan_pa_intr_withPPP_1y9";
		static final String ELOAN_PA_INTR_WITHPPP_1Y10 = "eloan_pa_intr_withPPP_1y10";
		static final String ELOAN_PA_INTR_WITHPPP_1Y11 = "eloan_pa_intr_withPPP_1y11";
		// static final String ELOAN_PA_INTR_WITHPPP_1Y12 = "eloan_pa_intr_withPPP_1y12";		
		static final String ELOAN_PA_INTR_WITHPPP_2T1 = "eloan_pa_intr_withPPP_2t1";
		
		static final String ELOAN_PA_INTR_NOPPP_CB = "eloan_pa_intr_noPPP_cb";
		static final String ELOAN_PA_INTR_NOPPP_BASERATE = "eloan_pa_intr_noPPP_baseRate";
		static final String ELOAN_PA_INTR_NOPPP_WAY = "eloan_pa_intr_noPPP_way";
		static final String ELOAN_PA_INTR_NOPPP_1T1 = "eloan_pa_intr_noPPP_1t1";
		static final String ELOAN_PA_INTR_NOPPP_2T1 = "eloan_pa_intr_noPPP_2t1";		
		static final String ELOAN_PA_INTR_NOPPP_3T1 = "eloan_pa_intr_noPPP_3t1";		
		static final String ELOAN_PA_INTR_NOPPP_4T1 = "eloan_pa_intr_noPPP_4t1";
		static final String ELOAN_PA_INTR_NOPPP_4T2 = "eloan_pa_intr_noPPP_4t2";
		static final String ELOAN_PA_INTR_NOPPP_5T1 = "eloan_pa_intr_noPPP_5t1";
		
		static final String ELOAN_PA_INTR_OTHER_CB = "eloan_pa_intr_other_cb";
		static final String ELOAN_PA_INTR_OTHER_T1 = "eloan_pa_intr_other_t1";
		
		static final String ELOAN_PB_GNTEEG_CB = "eloan_pb_gnteeG_cb";
		static final String ELOAN_PB_GNTEEG_E_CB = "eloan_pb_gnteeG_E_cb"; //E:理財型貸款(不循環動用)
		static final String ELOAN_PB_GNTEEG_E_T1 = "eloan_pb_gnteeG_E_t1";		
		static final String ELOAN_PB_GNTEEG_D_CB = "eloan_pb_gnteeG_D_cb"; //D:理財型貸款(循環動用)
		static final String ELOAN_PB_GNTEEG_D_T1 = "eloan_pb_gnteeG_D_t1";
		//~~~~~~
		static final String ELOAN_PB_GNTEEN_CB = "eloan_pb_gnteeN_cb";
		static final String ELOAN_PB_GNTEEN_F_CB = "eloan_pb_gnteeN_F_cb"; //F:消費性放款（不循環動用）
		static final String ELOAN_PB_GNTEEN_F_T1 = "eloan_pb_gnteeN_F_t1";
		static final String ELOAN_PB_GNTEEN_G_CB = "eloan_pb_gnteeN_G_cb"; //G:理財型貸款或消費性放款（循環動用）
		static final String ELOAN_PB_GNTEEN_G_T1 = "eloan_pb_gnteeN_G_t1";
		
		static final String ELOAN_PC_SERV_TEL_CB = "eloan_pc_serv_tel_cb";
		static final String ELOAN_PC_SERV_TEL_T1 = "eloan_pc_serv_tel_t1";
		static final String ELOAN_PC_SERV_FAX_CB = "eloan_pc_serv_fax_cb";
		static final String ELOAN_PC_SERV_FAX_T1 = "eloan_pc_serv_fax_t1";
		static final String ELOAN_PC_SERV_MAIL_CB = "eloan_pc_serv_mail_cb";
		static final String ELOAN_PC_SERV_MAIL_T1 = "eloan_pc_serv_mail_t1";
		static final String ELOAN_PC_SERV_URL_CB = "eloan_pc_serv_url_cb";
		static final String ELOAN_PC_SERV_CALL_CB = "eloan_pc_serv_call_cb";
		static final String ELOAN_PC_SERV_OTHER_CB = "eloan_pc_serv_other_cb";
		static final String ELOAN_PC_SERV_OTHER_T1 = "eloan_pc_serv_other_t1";
		static final String ELOAN_PC_COURT_LOC = "eloan_pc_court_loc";
		static final String ELOAN_PC_COPY_CNT = "eloan_pc_copy_cnt";
		static final String ELOAN_PC_SPTERM_YRATE = "eloan_pc_spTerm_yRate";
		static final String ELOAN_PC_SPTERM_NOTE = "eloan_pc_spTerm_note";
		static final String ELOAN_PC_GNTEE_CB = "eloan_pc_gntee_cb";
		
		static final String ELOAN_P9_SIG_M_NAME = "eloan_p9_sig_m_name";
		static final String ELOAN_P9_SIG_M_CUSTID = "eloan_p9_sig_m_custId";
		static final String ELOAN_P9_SIG_M_ADDR = "eloan_p9_sig_m_addr";
		static final String ELOAN_P9_SIG_1N_CB = "eloan_p9_sig_1N_cb";
		static final String ELOAN_P9_SIG_1N_NAME = "eloan_p9_sig_1N_name";
		static final String ELOAN_P9_SIG_1G_CB = "eloan_p9_sig_1G_cb";
		static final String ELOAN_P9_SIG_1G_NAME = "eloan_p9_sig_1G_name";
		static final String ELOAN_P9_SIG_1_CUSTID = "eloan_p9_sig_1_custId";
		static final String ELOAN_P9_SIG_1_ADDR = "eloan_p9_sig_1_addr";
		static final String ELOAN_P9_SIG_2N_CB = "eloan_p9_sig_2N_cb";
		static final String ELOAN_P9_SIG_2N_NAME = "eloan_p9_sig_2N_name";
		static final String ELOAN_P9_SIG_2G_CB = "eloan_p9_sig_2G_cb";
		static final String ELOAN_P9_SIG_2G_NAME = "eloan_p9_sig_2G_name";
		static final String ELOAN_P9_SIG_2_CUSTID = "eloan_p9_sig_2_custId";
		static final String ELOAN_P9_SIG_2_ADDR = "eloan_p9_sig_2_addr";
		static final String ELOAN_P9_SIG_PARTYB_AGENT = "eloan_p9_sig_partyB_agent";
		static final String ELOAN_P9_SIG_PARTYB_ADDR = "eloan_p9_sig_partyB_addr";
		static final String ELOAN_P9_RECPT_M_NAME = "eloan_p9_recpt_m_name";
		static final String ELOAN_P9_RECPT_S_NAME = "eloan_p9_recpt_s_name";

		static final String ELOAN_P1_RPRINTMODE2_R1 = "eloan_p1_printMode2_r1";
		static final String ELOAN_P1_RPRINTMODE2_R2 = "eloan_p1_printMode2_r2";
		static final String ELOAN_P1_RPRINTMODE2_R3 = "eloan_p1_printMode2_r3";
		static final String ELOAN_P1_RPRINTMODE2_R4 = "eloan_p1_printMode2_r4";
		static final String ELOAN_P1_RPRINTMODE2_R5 = "eloan_p1_printMode2_r5";
	}
	
	interface CtrType3 { 
		static final String ELOAN_P1_CONTR_NO = "eloan_p1_contr_no";
		static final String ELOAN_P1_CONTR_CNAME = "eloan_p1_contr_cname";
		static final String ELOAN_P1_CONTR_NAME_M = "eloan_p1_contr_name_m";
		static final String ELOAN_P1_CONTR_NAME_N = "eloan_p1_contr_name_n";
		static final String ELOAN_P1_CONTR_NAME_G = "eloan_p1_contr_name_g";
		static final String ELOAN_P1_CONTR_AMT = "eloan_p1_contr_amt";
		
		static final String ELOAN_PA_DELIV_A_CB = "eloan_pa_deliv_A_cb";
		static final String ELOAN_PA_DELIV_A_T1 = "eloan_pa_deliv_A_t1";
		static final String ELOAN_PA_DELIV_A_T2 = "eloan_pa_deliv_A_t2";
		static final String ELOAN_PA_DELIV_B_CB = "eloan_pa_deliv_B_cb";		
		static final String ELOAN_PA_DELIV_C_CB = "eloan_pa_deliv_C_cb";
		static final String ELOAN_PA_DELIV_C_T1 = "eloan_pa_deliv_C_t1";
		static final String ELOAN_PA_DELIV_D_CB = "eloan_pa_deliv_D_cb";
		static final String ELOAN_PA_DELIV_D_T1 = "eloan_pa_deliv_D_t1";
		static final String ELOAN_PA_DELIV_D_T2 = "eloan_pa_deliv_D_t2";

		static final String ELOAN_PA_DELIV_D1_T1  = "eloan_pa_deliv_D1_t1"; //地址分成多段，前端UI只有1行
		static final String ELOAN_PA_DELIV_D1_T1A = "eloan_pa_deliv_D1_t1A";
		static final String ELOAN_PA_DELIV_D1_T1B = "eloan_pa_deliv_D1_t1B";
		static final String ELOAN_PA_DELIV_D1_T2  = "eloan_pa_deliv_D1_t2";
		static final String ELOAN_PA_DELIV_D1_T3  = "eloan_pa_deliv_D1_t3";
		static final String ELOAN_PA_DELIV_D1_T4  = "eloan_pa_deliv_D1_t4";
		static final String ELOAN_PA_DELIV_D1_T5  = "eloan_pa_deliv_D1_t5";
		static final String ELOAN_PA_DELIV_D1_T6  = "eloan_pa_deliv_D1_t6";
		static final String ELOAN_PA_DELIV_D1_T7  = "eloan_pa_deliv_D1_t7";
		static final String ELOAN_PA_DELIV_D2_1CB = "eloan_pa_deliv_D2_1cb";
		static final String ELOAN_PA_DELIV_D2_2CB = "eloan_pa_deliv_D2_2cb";
		static final String ELOAN_PA_DELIV_D3_T1 = "eloan_pa_deliv_D3_t1";
		
		static final String ELOAN_PA_DELIV_E_CB = "eloan_pa_deliv_E_cb";
		static final String ELOAN_PA_DELIV_E_T1 = "eloan_pa_deliv_E_t1";
		
		static final String ELOAN_PA_PURPOSE_WAY = "eloan_pa_purpose_way"; // L120M01A.purpose // where codeType='cls1141_purpose'
		static final String ELOAN_PA_PURPOSE_G_T1 = "eloan_pa_purpose_G_t1"; // L120M01A.purposeOth
		
		static final String ELOAN_PA_USE_WAY = "eloan_pa_use_way";
		static final String ELOAN_PA_USE_A_T1 = "eloan_pa_use_A_t1";
		static final String ELOAN_PA_USE_A_T2 = "eloan_pa_use_A_t2";		
		static final String ELOAN_PA_USE_A_T3 = "eloan_pa_use_A_t3";
		static final String ELOAN_PA_USE_A_T4 = "eloan_pa_use_A_t4";
		static final String ELOAN_PA_USE_A_T5 = "eloan_pa_use_A_t5";		
		static final String ELOAN_PA_USE_A_T6 = "eloan_pa_use_A_t6";
		static final String ELOAN_PA_USE_A_T7 = "eloan_pa_use_A_t7";
		static final String ELOAN_PA_USE_A_T8 = "eloan_pa_use_A_t8";
		static final String ELOAN_PA_USE_B_T1 = "eloan_pa_use_B_t1";
		static final String ELOAN_PA_USE_B_T2 = "eloan_pa_use_B_t2";
		static final String ELOAN_PA_USE_B_T3 = "eloan_pa_use_B_t3";
		static final String ELOAN_PA_USE_B_T4 = "eloan_pa_use_B_t4";
		static final String ELOAN_PA_USE_B_T5 = "eloan_pa_use_B_t5";
		static final String ELOAN_PA_USE_B_T6 = "eloan_pa_use_B_t6";
		static final String ELOAN_PA_USE_B_T7 = "eloan_pa_use_B_t7";
		static final String ELOAN_PA_USE_B_T8 = "eloan_pa_use_B_t8";
		static final String ELOAN_PA_USE_B_T9 = "eloan_pa_use_B_t9";
		static final String ELOAN_PA_USE_C_T1 = "eloan_pa_use_C_t1";
		static final String ELOAN_PA_USE_C_T2 = "eloan_pa_use_C_t2";
		static final String ELOAN_PA_USE_C_T3 = "eloan_pa_use_C_t3";
		static final String ELOAN_PA_USE_C_T4 = "eloan_pa_use_C_t4";
		static final String ELOAN_PA_USE_C_T5 = "eloan_pa_use_C_t5";
		static final String ELOAN_PA_USE_C_T6 = "eloan_pa_use_C_t6";
		static final String ELOAN_PA_USE_C_T7 = "eloan_pa_use_C_t7";
		static final String ELOAN_PA_USE_C_T8 = "eloan_pa_use_C_t8";
		static final String ELOAN_PA_USE_C_T9 = "eloan_pa_use_C_t9";
		static final String ELOAN_PA_USE_D_T1 = "eloan_pa_use_D_t1";
		static final String ELOAN_PA_USE_D_T2 = "eloan_pa_use_D_t2";
		static final String ELOAN_PA_USE_D_T3 = "eloan_pa_use_D_t3";
		static final String ELOAN_PA_USE_D_T4 = "eloan_pa_use_D_t4";
		static final String ELOAN_PA_USE_D_T5 = "eloan_pa_use_D_t5";
		static final String ELOAN_PA_USE_D_T6 = "eloan_pa_use_D_t6";
		static final String ELOAN_PA_USE_E_T1 = "eloan_pa_use_E_t1";
		
		static final String ELOAN_PA_REPAY_WAY = "eloan_pa_repay_way";
		static final String ELOAN_PA_REPAY_D_T1 = "eloan_pa_repay_D_t1";
		static final String ELOAN_PA_REPAY_D_T2 = "eloan_pa_repay_D_t2";
		static final String ELOAN_PA_REPAY_D_T3 = "eloan_pa_repay_D_t3";
		static final String ELOAN_PA_REPAY_D_T4 = "eloan_pa_repay_D_t4";
		static final String ELOAN_PA_REPAY_E_T1 = "eloan_pa_repay_E_t1";
		static final String ELOAN_PA_REPAY_E_T2 = "eloan_pa_repay_E_t2";
		static final String ELOAN_PA_REPAY_E_T3 = "eloan_pa_repay_E_t3";
		static final String ELOAN_PA_REPAY_E_T4 = "eloan_pa_repay_E_t4";
		static final String ELOAN_PA_REPAY_F_T1 = "eloan_pa_repay_F_t1";
		static final String ELOAN_PA_REPAY_F_T2 = "eloan_pa_repay_F_t2";
		static final String ELOAN_PA_REPAY_G_T1 = "eloan_pa_repay_G_t1";
		static final String ELOAN_PA_REPAY_NOPPP_CB = "eloan_pa_repay_noPPP_cb";
		static final String ELOAN_PA_REPAY_WITHPPP_CB = "eloan_pa_repay_withPPP_cb";
		static final String ELOAN_PA_REPAY_WITHPPP_TERM = "eloan_pa_repay_withPPP_term";
		static final String ELOAN_PA_REPAY_ACTNO = "eloan_pa_repay_actNo"; //TODO 在 <w:p>... </w:p> 裡，把 <w:jc w:val="both"/> 移掉 => 不要分散對齊
		static final String ELOAN_PA_REPAY_FEENO01 = "eloan_pa_repay_feeNo01";
		static final String ELOAN_PA_REPAY_FEENO02 = "eloan_pa_repay_feeNo02";
		static final String ELOAN_PA_REPAY_FEENO03 = "eloan_pa_repay_feeNo03";
		static final String ELOAN_PA_REPAY_FEENO04 = "eloan_pa_repay_feeNo04";
		static final String ELOAN_PA_REPAY_FEENO06 = "eloan_pa_repay_feeNo06";
		static final String ELOAN_PA_REPAY_FEENO07 = "eloan_pa_repay_feeNo07";

		static final String ELOAN_PA_INTR_WITHPPP_CB = "eloan_pa_intr_withPPP_cb";
		static final String ELOAN_PA_INTR_WITHPPP_BASERATE = "eloan_pa_intr_withPPP_baseRate";
		static final String ELOAN_PA_INTR_WITHPPP_WAY = "eloan_pa_intr_withPPP_way";
		static final String ELOAN_PA_INTR_WITHPPP_1X1 = "eloan_pa_intr_withPPP_1x1";
		static final String ELOAN_PA_INTR_WITHPPP_1X2 = "eloan_pa_intr_withPPP_1x2";
		static final String ELOAN_PA_INTR_WITHPPP_1X3 = "eloan_pa_intr_withPPP_1x3";
		static final String ELOAN_PA_INTR_WITHPPP_1Y1 = "eloan_pa_intr_withPPP_1y1";
		static final String ELOAN_PA_INTR_WITHPPP_1Y2 = "eloan_pa_intr_withPPP_1y2";
		static final String ELOAN_PA_INTR_WITHPPP_1Y3 = "eloan_pa_intr_withPPP_1y3";
		// static final String ELOAN_PA_INTR_WITHPPP_1Y4 = "eloan_pa_intr_withPPP_1y4";
		static final String ELOAN_PA_INTR_WITHPPP_1Y5 = "eloan_pa_intr_withPPP_1y5";
		static final String ELOAN_PA_INTR_WITHPPP_1Y6 = "eloan_pa_intr_withPPP_1y6";
		static final String ELOAN_PA_INTR_WITHPPP_1Y7 = "eloan_pa_intr_withPPP_1y7";
		// static final String ELOAN_PA_INTR_WITHPPP_1Y8 = "eloan_pa_intr_withPPP_1y8";		
		static final String ELOAN_PA_INTR_WITHPPP_2T1 = "eloan_pa_intr_withPPP_2t1";
		
		static final String ELOAN_PA_INTR_NOPPP_CB = "eloan_pa_intr_noPPP_cb";
		static final String ELOAN_PA_INTR_NOPPP_BASERATE = "eloan_pa_intr_noPPP_baseRate";
		static final String ELOAN_PA_INTR_NOPPP_WAY = "eloan_pa_intr_noPPP_way";
		static final String ELOAN_PA_INTR_NOPPP_1T1 = "eloan_pa_intr_noPPP_1t1";
		static final String ELOAN_PA_INTR_NOPPP_2T1 = "eloan_pa_intr_noPPP_2t1";		
		static final String ELOAN_PA_INTR_NOPPP_3T1 = "eloan_pa_intr_noPPP_3t1";		
		static final String ELOAN_PA_INTR_NOPPP_4T1 = "eloan_pa_intr_noPPP_4t1";
		
		static final String ELOAN_PA_INTR_OTHER_CB = "eloan_pa_intr_other_cb";
		static final String ELOAN_PA_INTR_OTHER_T1 = "eloan_pa_intr_other_t1";
		
		static final String ELOAN_PB_GNTEEG_CB = "eloan_pb_gnteeG_cb";
		static final String ELOAN_PB_GNTEEG_B_CB = "eloan_pb_gnteeG_B_cb"; //B:非自用住宅放款(不循環動用)
		static final String ELOAN_PB_GNTEEG_B_T1 = "eloan_pb_gnteeG_B_t1";
		static final String ELOAN_PB_GNTEEG_C_CB = "eloan_pb_gnteeG_C_cb"; //C:歡喜理財家或綜合理財房屋貸款（循環及不循環動用）
		static final String ELOAN_PB_GNTEEG_C_T1 = "eloan_pb_gnteeG_C_t1";
		static final String ELOAN_PB_GNTEEG_D_CB = "eloan_pb_gnteeG_D_cb"; //D:理財型貸款(循環動用)
		//~~~~~~
		static final String ELOAN_PB_GNTEEN_CB = "eloan_pb_gnteeN_cb";
		//在 房貸契約書，選項A指 自用住宅放款 static final String ELOAN_PB_GNTEEN_A_CB = "eloan_pb_gnteeN_A_cb";
		static final String ELOAN_PB_GNTEEN_B_CB = "eloan_pb_gnteeN_B_cb"; //B:非自用住宅放款(不循環動用)
		static final String ELOAN_PB_GNTEEN_B_T1 = "eloan_pb_gnteeN_B_t1";
		static final String ELOAN_PB_GNTEEN_C_CB = "eloan_pb_gnteeN_C_cb"; //C:歡喜理財家或綜合理財房屋貸款（循環及不循環動用）
		static final String ELOAN_PB_GNTEEN_C_T1 = "eloan_pb_gnteeN_C_t1";
		static final String ELOAN_PB_GNTEEN_D_CB = "eloan_pb_gnteeN_D_cb"; //D:理財型貸款(循環動用)
		
		static final String ELOAN_PC_SERV_TEL_CB = "eloan_pc_serv_tel_cb";
		static final String ELOAN_PC_SERV_TEL_T1 = "eloan_pc_serv_tel_t1";
		static final String ELOAN_PC_SERV_FAX_CB = "eloan_pc_serv_fax_cb";
		static final String ELOAN_PC_SERV_FAX_T1 = "eloan_pc_serv_fax_t1";
		static final String ELOAN_PC_SERV_MAIL_CB = "eloan_pc_serv_mail_cb";
		static final String ELOAN_PC_SERV_MAIL_T1 = "eloan_pc_serv_mail_t1";
		static final String ELOAN_PC_SERV_URL_CB = "eloan_pc_serv_url_cb";
		static final String ELOAN_PC_SERV_CALL_CB = "eloan_pc_serv_call_cb";
		static final String ELOAN_PC_SERV_OTHER_CB = "eloan_pc_serv_other_cb";
		static final String ELOAN_PC_SERV_OTHER_T1 = "eloan_pc_serv_other_t1";
		static final String ELOAN_PC_COURT_LOC = "eloan_pc_court_loc";
		static final String ELOAN_PC_COPY_CNT = "eloan_pc_copy_cnt";
		static final String ELOAN_PC_SPTERM_YRATE = "eloan_pc_spTerm_yRate";
		static final String ELOAN_PC_SPTERM_NOTE = "eloan_pc_spTerm_note";
		static final String ELOAN_PC_GNTEE_CB = "eloan_pc_gntee_cb";
		
		static final String ELOAN_P9_SIG_M_NAME = "eloan_p9_sig_m_name";
		static final String ELOAN_P9_SIG_M_CUSTID = "eloan_p9_sig_m_custId";
		static final String ELOAN_P9_SIG_M_ADDR = "eloan_p9_sig_m_addr";
		static final String ELOAN_P9_SIG_1N_CB = "eloan_p9_sig_1N_cb";
		static final String ELOAN_P9_SIG_1N_NAME = "eloan_p9_sig_1N_name";
		static final String ELOAN_P9_SIG_1G_CB = "eloan_p9_sig_1G_cb";
		static final String ELOAN_P9_SIG_1G_NAME = "eloan_p9_sig_1G_name";
		static final String ELOAN_P9_SIG_1_CUSTID = "eloan_p9_sig_1_custId";
		static final String ELOAN_P9_SIG_1_ADDR = "eloan_p9_sig_1_addr";
		static final String ELOAN_P9_SIG_2N_CB = "eloan_p9_sig_2N_cb";
		static final String ELOAN_P9_SIG_2N_NAME = "eloan_p9_sig_2N_name";
		static final String ELOAN_P9_SIG_2G_CB = "eloan_p9_sig_2G_cb";
		static final String ELOAN_P9_SIG_2G_NAME = "eloan_p9_sig_2G_name";
		static final String ELOAN_P9_SIG_2_CUSTID = "eloan_p9_sig_2_custId";
		static final String ELOAN_P9_SIG_2_ADDR = "eloan_p9_sig_2_addr";
		static final String ELOAN_P9_SIG_PARTYB_AGENT = "eloan_p9_sig_partyB_agent";
		static final String ELOAN_P9_SIG_PARTYB_ADDR = "eloan_p9_sig_partyB_addr";
		static final String ELOAN_P9_RECPT_M_NAME = "eloan_p9_recpt_m_name";
		static final String ELOAN_P9_RECPT_S_NAME = "eloan_p9_recpt_s_name";

		static final String ELOAN_P1_RPRINTMODE2_R1 = "eloan_p1_printMode2_r1";
		static final String ELOAN_P1_RPRINTMODE2_R2 = "eloan_p1_printMode2_r2";
		static final String ELOAN_P1_RPRINTMODE2_R3 = "eloan_p1_printMode2_r3";
		static final String ELOAN_P1_RPRINTMODE2_R4 = "eloan_p1_printMode2_r4";
		static final String ELOAN_P1_RPRINTMODE2_R5 = "eloan_p1_printMode2_r5";
	}
	
	interface CtrTypeL {
		static final String PLOAN_ACCTNO_FLAG_LIST = "loanAcct_and_Flag" ;
	}
	
	interface CtrTypeA {
		static final String PLOAN_ACCTNO_LIST = "loanAcct";
		static final String PLOAN_LENDING_PLAN_OPTION = "lendingPlanInfo_showOption";
		static final String PLOAN_RATE_RANGE1 = "rateRange1";
		static final String PLOAN_RATE_RANGE2 = "rateRange2";
		static final String PLOAN_RATE_RANGE1_TYPE01 = "rateRange1Type01";  //一開始只有「浮動」, 後來增加了「固定」
		static final String PLOAN_RATE_RANGE2_TYPE01 = "rateRange2Type01";
		static final String PLOAN_REPAYMENT_LIST = "repaymentList";
		static final String PLOAN_PAYMENTINFO_LIST = "paymentInfoList";
		/*
			rateRange?Param01~rateRange?Param02 第X期~第Y期
			
			rateRange?Param03(固定利率的利率值)
			rateRange?Param04(利率的中文描述)
			rateRange?Param05(baseRate) + rateRange?Param06(plusRate) = rateRange?Param07(finalRate) 
		 */
		static final String PLOAN_RATE_RANGE1_FIXED_RATE = "rateRange1Param03";
		static final String PLOAN_RATE_RANGE2_FIXED_RATE = "rateRange2Param03";
		static final String PLOAN_RATE_RANGE1_RATEDESC_M3 = "rateRange1ParamM3"; //M3 是「機動計息」
		static final String PLOAN_RATE_RANGE1_PLUSRATE = "rateRange1Param06";
		static final String PLOAN_RATE_RANGE2_PLUSRATE = "rateRange2Param06";
		static final String PLOAN_RATE_RANGE1_RESULTRATE = "rateRange1Param07";
		static final String PLOAN_RATE_RANGE2_RESULTRATE = "rateRange2Param07";
		static final String PLOAN_RATE_ADVANCED_REDEMPT_TITLE = "advancedRedemptionTitle";
		static final String PLOAN_RATE_ADVANCED_REDEMPT_DESC = "advancedRedemptionDesc";
		static final String PLOAN_RATE_LIMITED_REDEMPT_TITLE = "limitedRedemptionTitle";
		static final String PLOAN_RATE_LIMITED_REDEMPT_DESC = "limitedRedemptionDesc";
		static final String PLOAN_OTHERINFODESC = "otherInfoDesc";
		static final String PLOAN_GUARANTEEAMT = "guaranteeAmt";
		static final String PLOAN_COURT_NAME = "courtName";
		static final String PLOAN_BORROWERMOBILENUMBER = "borrowerMobileNumber";
		static final String PLOAN_BORROWEREMAIL = "borrowerEmail";


		static final String PLOAN_LOANPURPOSE_C = "loanPurpose_C";
		static final String PLOAN_LOANPURPOSE_G = "loanPurpose_G";
		static final String PLOAN_LOANPURPOSE_H = "loanPurpose_H";
		static final String PLOAN_LOANPURPOSE_J = "loanPurpose_J";
		static final String PLOAN_LOANPURPOSE_K = "loanPurpose_K";
		static final String PLOAN_LOANPURPOSE_F = "loanPurpose_F";
		static final String PLOAN_LOANPURPOSE_N = "loanPurpose_N";
		static final String PLOAN_LOANPURPOSE_O = "loanPurpose_O";
		static final String PLOAN_LOANPURPOSE_P = "loanPurpose_P";
		static final String PLOAN_LOANPURPOSE_OTHERDESC = "loanPurpose_otherDesc";
		
		static final String PLOAN_IS_CNTRNO_BELONG_CO70647919_C101 = "is_cntrNo_belong_co70647919_c101";
	}

	interface CtrTypeB {
		static final String PLOAN_ACCTNO_LIST = "loanAcct";
		static final String PLOAN_LENDING_PLAN_OPTION = "lendingPlanInfo_showOption";
		static final String PLOAN_RATE_RANGE1 = "rateRange1";
		static final String PLOAN_RATE_RANGE2 = "rateRange2";
		static final String PLOAN_RATE_RANGE1_TYPE01 = "rateRange1Type01";  //一開始只有「浮動」, 後來增加了「固定」
		static final String PLOAN_RATE_RANGE2_TYPE01 = "rateRange2Type01";
		static final String PLOAN_REPAYMENT_LIST = "repaymentList";
		static final String PLOAN_PAYMENTINFO_LIST = "paymentInfoList";
		/*
			rateRange?Param01~rateRange?Param02 第X期~第Y期

			rateRange?Param03(固定利率的利率值)
			rateRange?Param04(利率的中文描述)
			rateRange?Param05(baseRate) + rateRange?Param06(plusRate) = rateRange?Param07(finalRate)
		 */
		static final String PLOAN_RATE_RANGE1_FIXED_RATE = "rateRange1Param03";
		static final String PLOAN_RATE_RANGE2_FIXED_RATE = "rateRange2Param03";
		static final String PLOAN_RATE_RANGE1_RATEDESC = "rateRange1ParamDesc"; //M3 是「機動計息」
		static final String PLOAN_RATE_RANGE1_PLUSRATE = "rateRange1Param06";
		static final String PLOAN_RATE_RANGE2_PLUSRATE = "rateRange2Param06";
		static final String PLOAN_RATE_RANGE1_RESULTRATE = "rateRange1Param07";
		static final String PLOAN_RATE_RANGE2_RESULTRATE = "rateRange2Param07";
		static final String PLOAN_RATE_ADVANCED_REDEMPT_TITLE = "advancedRedemptionTitle";
		static final String PLOAN_RATE_ADVANCED_REDEMPT_DESC = "advancedRedemptionDesc";
		static final String PLOAN_RATE_LIMITED_REDEMPT_TITLE = "limitedRedemptionTitle";
		static final String PLOAN_RATE_LIMITED_REDEMPT_DESC = "limitedRedemptionDesc";
		static final String PLOAN_OTHERINFODESC = "otherInfoDesc";
		static final String PLOAN_GUARANTEEAMT = "guaranteeAmt";
		static final String PLOAN_COURT_NAME = "courtName";
		static final String PLOAN_BORROWERMOBILENUMBER = "borrowerMobileNumber";
		static final String PLOAN_BORROWEREMAIL = "borrowerEmail";


		static final String PLOAN_LOANPURPOSE_A = "loanPurpose_A";
		static final String PLOAN_LOANPURPOSE_B = "loanPurpose_B";
		static final String PLOAN_LOANPURPOSE_C = "loanPurpose_C";
		static final String PLOAN_LOANPURPOSE_D = "loanPurpose_D";
		static final String PLOAN_LOANPURPOSE_G = "loanPurpose_G";
		static final String PLOAN_LOANPURPOSE_H = "loanPurpose_H";
		static final String PLOAN_LOANPURPOSE_J = "loanPurpose_J";
		static final String PLOAN_LOANPURPOSE_K = "loanPurpose_K";
		static final String PLOAN_LOANPURPOSE_F = "loanPurpose_F";
		static final String PLOAN_LOANPURPOSE_N = "loanPurpose_N";
		static final String PLOAN_LOANPURPOSE_O = "loanPurpose_O";
		static final String PLOAN_LOANPURPOSE_P = "loanPurpose_P";
		static final String PLOAN_LOANPURPOSE_OTHERDESC = "loanPurpose_otherDesc";

		static final String preliminaryFee = "preliminaryFee";
		static final String creditCheckFee = "creditCheckFee";
		static final String renewFee = "renewFee";
		static final String changeFee = "changeFee";
		static final String certFee = "certFee";
		static final String reissueFee = "reissueFee";

		static final String witness = "witness";

		static final String ELOAN_P1_CONTR_NO = "eloan_p1_contr_no";
		static final String ELOAN_P1_CONTR_CNAME = "eloan_p1_contr_cname";
		static final String ELOAN_P1_CONTR_NAME_M = "eloan_p1_contr_name_m";
		static final String ELOAN_P1_CONTR_NAME_N = "eloan_p1_contr_name_n";
		static final String ELOAN_P1_CONTR_NAME_G = "eloan_p1_contr_name_g";
		static final String ELOAN_P1_CONTR_AMT = "eloan_p1_contr_amt";

		static final String ELOAN_PA_DELIV_A_CB = "eloan_pa_deliv_A_cb";
		static final String ELOAN_PA_DELIV_A_T1 = "eloan_pa_deliv_A_t1";
		static final String ELOAN_PA_DELIV_A_T2 = "eloan_pa_deliv_A_t2";
		static final String ELOAN_PA_DELIV_B_CB = "eloan_pa_deliv_B_cb";
		static final String ELOAN_PA_DELIV_C_CB = "eloan_pa_deliv_C_cb";
		static final String ELOAN_PA_DELIV_C_T1 = "eloan_pa_deliv_C_t1";
		static final String ELOAN_PA_DELIV_D_CB = "eloan_pa_deliv_D_cb";
		static final String ELOAN_PA_DELIV_D_T1 = "eloan_pa_deliv_D_t1";
		static final String ELOAN_PA_DELIV_D_T2 = "eloan_pa_deliv_D_t2";

		static final String ELOAN_PA_DELIV_D1_T1  = "eloan_pa_deliv_D1_t1"; //地址分成多段，前端UI只有1行
		static final String ELOAN_PA_DELIV_D1_T1A = "eloan_pa_deliv_D1_t1A";
		static final String ELOAN_PA_DELIV_D1_T1B = "eloan_pa_deliv_D1_t1B";
		static final String ELOAN_PA_DELIV_D1_T2  = "eloan_pa_deliv_D1_t2";
		static final String ELOAN_PA_DELIV_D1_T3  = "eloan_pa_deliv_D1_t3";
		static final String ELOAN_PA_DELIV_D1_T4  = "eloan_pa_deliv_D1_t4";
		static final String ELOAN_PA_DELIV_D1_T5  = "eloan_pa_deliv_D1_t5";
		static final String ELOAN_PA_DELIV_D1_T6  = "eloan_pa_deliv_D1_t6";
		static final String ELOAN_PA_DELIV_D1_T7  = "eloan_pa_deliv_D1_t7";
		static final String ELOAN_PA_DELIV_D2_1CB = "eloan_pa_deliv_D2_1cb";
		static final String ELOAN_PA_DELIV_D2_2CB = "eloan_pa_deliv_D2_2cb";
		static final String ELOAN_PA_DELIV_D3_T1 = "eloan_pa_deliv_D3_t1";

		static final String ELOAN_PA_DELIV_E_CB = "eloan_pa_deliv_E_cb";
		static final String ELOAN_PA_DELIV_E_T1 = "eloan_pa_deliv_E_t1";

		static final String ELOAN_PA_PURPOSE_WAY = "eloan_pa_purpose_way"; // L120M01A.purpose // where codeType='cls1141_purpose'
		static final String ELOAN_PA_PURPOSE_G_T1 = "eloan_pa_purpose_G_t1"; // L120M01A.purposeOth

		static final String ELOAN_PA_USE_WAY = "eloan_pa_use_way";
		static final String ELOAN_PA_USE_A = "eloan_pa_use_A";
		static final String ELOAN_PA_USE_A_T1 = "eloan_pa_use_A_t1";
		static final String ELOAN_PA_USE_A_T2 = "eloan_pa_use_A_t2";
		static final String ELOAN_PA_USE_A_T3 = "eloan_pa_use_A_t3";
		static final String ELOAN_PA_USE_A_T4 = "eloan_pa_use_A_t4";
		static final String ELOAN_PA_USE_A_T5 = "eloan_pa_use_A_t5";
		static final String ELOAN_PA_USE_A_T6 = "eloan_pa_use_A_t6";
		static final String ELOAN_PA_USE_A_T7 = "eloan_pa_use_A_t7";
		static final String ELOAN_PA_USE_A_T8 = "eloan_pa_use_A_t8";
		static final String ELOAN_PA_USE_B = "eloan_pa_use_B";
		static final String ELOAN_PA_USE_B_T1 = "eloan_pa_use_B_t1";
		static final String ELOAN_PA_USE_B_T2 = "eloan_pa_use_B_t2";
		static final String ELOAN_PA_USE_B_T3 = "eloan_pa_use_B_t3";
		static final String ELOAN_PA_USE_B_T4 = "eloan_pa_use_B_t4";
		static final String ELOAN_PA_USE_B_T5 = "eloan_pa_use_B_t5";
		static final String ELOAN_PA_USE_B_T6 = "eloan_pa_use_B_t6";
		static final String ELOAN_PA_USE_B_T7 = "eloan_pa_use_B_t7";
		static final String ELOAN_PA_USE_B_T8 = "eloan_pa_use_B_t8";
		static final String ELOAN_PA_USE_B_T9 = "eloan_pa_use_B_t9";
		static final String ELOAN_PA_USE_C = "eloan_pa_use_C";
		static final String ELOAN_PA_USE_C_T1 = "eloan_pa_use_C_t1";
		static final String ELOAN_PA_USE_C_T2 = "eloan_pa_use_C_t2";
		static final String ELOAN_PA_USE_C_T3 = "eloan_pa_use_C_t3";
		static final String ELOAN_PA_USE_C_T4 = "eloan_pa_use_C_t4";
		static final String ELOAN_PA_USE_C_T5 = "eloan_pa_use_C_t5";
		static final String ELOAN_PA_USE_C_T6 = "eloan_pa_use_C_t6";
		static final String ELOAN_PA_USE_C_T7 = "eloan_pa_use_C_t7";
		static final String ELOAN_PA_USE_C_T8 = "eloan_pa_use_C_t8";
		static final String ELOAN_PA_USE_C_T9 = "eloan_pa_use_C_t9";
		static final String ELOAN_PA_USE_D = "eloan_pa_use_D";
		static final String ELOAN_PA_USE_D_T1 = "eloan_pa_use_D_t1";
		static final String ELOAN_PA_USE_D_T2 = "eloan_pa_use_D_t2";
		static final String ELOAN_PA_USE_D_T3 = "eloan_pa_use_D_t3";
		static final String ELOAN_PA_USE_D_T4 = "eloan_pa_use_D_t4";
		static final String ELOAN_PA_USE_D_T5 = "eloan_pa_use_D_t5";
		static final String ELOAN_PA_USE_D_T6 = "eloan_pa_use_D_t6";
		static final String ELOAN_PA_USE_E = "eloan_pa_use_E";
		static final String ELOAN_PA_USE_E_T1 = "eloan_pa_use_E_t1";

		static final String ELOAN_PA_REPAY_WAY = "eloan_pa_repay_way";
		static final String ELOAN_PA_REPAY_A = "eloan_pa_repay_A";
		static final String ELOAN_PA_REPAY_B = "eloan_pa_repay_B";
		static final String ELOAN_PA_REPAY_C = "eloan_pa_repay_C";
		static final String ELOAN_PA_REPAY_D = "eloan_pa_repay_D";
		static final String ELOAN_PA_REPAY_D_T1 = "eloan_pa_repay_D_t1";
		static final String ELOAN_PA_REPAY_D_T2 = "eloan_pa_repay_D_t2";
		static final String ELOAN_PA_REPAY_D_T3 = "eloan_pa_repay_D_t3";
		static final String ELOAN_PA_REPAY_D_T4 = "eloan_pa_repay_D_t4";
		static final String ELOAN_PA_REPAY_E = "eloan_pa_repay_E";
		static final String ELOAN_PA_REPAY_E_T1 = "eloan_pa_repay_E_t1";
		static final String ELOAN_PA_REPAY_E_T2 = "eloan_pa_repay_E_t2";
		static final String ELOAN_PA_REPAY_E_T3 = "eloan_pa_repay_E_t3";
		static final String ELOAN_PA_REPAY_E_T4 = "eloan_pa_repay_E_t4";
		static final String ELOAN_PA_REPAY_F= "eloan_pa_repay_F";
		static final String ELOAN_PA_REPAY_F_T1 = "eloan_pa_repay_F_t1";
		static final String ELOAN_PA_REPAY_F_T2 = "eloan_pa_repay_F_t2";
		static final String ELOAN_PA_REPAY_G = "eloan_pa_repay_G";
		static final String ELOAN_PA_REPAY_G_T1 = "eloan_pa_repay_G_t1";
		static final String ELOAN_PA_REPAY_NOPPP_CB = "eloan_pa_repay_noPPP_cb";
		static final String ELOAN_PA_REPAY_WITHPPP_CB = "eloan_pa_repay_withPPP_cb";
		static final String ELOAN_PA_REPAY_WITHPPP_TERM = "eloan_pa_repay_withPPP_term";
		static final String ELOAN_PA_REPAY_ACTNO = "eloan_pa_repay_actNo"; //TODO 在 <w:p>... </w:p> 裡，把 <w:jc w:val="both"/> 移掉 => 不要分散對齊

		static final String ELOAN_PA_INTR_WITHPPP_CB = "eloan_pa_intr_withPPP_cb";
		static final String ELOAN_PA_INTR_WITHPPP_BASERATE = "eloan_pa_intr_withPPP_baseRate";
		static final String ELOAN_PA_INTR_WITHPPP_WAY = "eloan_pa_intr_withPPP_way";
		static final String ELOAN_PA_INTR_WITHPPP_1X1 = "eloan_pa_intr_withPPP_1x1";
		static final String ELOAN_PA_INTR_WITHPPP_1X2 = "eloan_pa_intr_withPPP_1x2";
		static final String ELOAN_PA_INTR_WITHPPP_1X3 = "eloan_pa_intr_withPPP_1x3";
		static final String ELOAN_PA_INTR_WITHPPP_1Y1 = "eloan_pa_intr_withPPP_1y1";
		static final String ELOAN_PA_INTR_WITHPPP_1Y2 = "eloan_pa_intr_withPPP_1y2";
		static final String ELOAN_PA_INTR_WITHPPP_1Y3 = "eloan_pa_intr_withPPP_1y3";
		// static final String ELOAN_PA_INTR_WITHPPP_1Y4 = "eloan_pa_intr_withPPP_1y4";
		static final String ELOAN_PA_INTR_WITHPPP_1Y5 = "eloan_pa_intr_withPPP_1y5";
		static final String ELOAN_PA_INTR_WITHPPP_1Y6 = "eloan_pa_intr_withPPP_1y6";
		static final String ELOAN_PA_INTR_WITHPPP_1Y7 = "eloan_pa_intr_withPPP_1y7";
		// static final String ELOAN_PA_INTR_WITHPPP_1Y8 = "eloan_pa_intr_withPPP_1y8";
		static final String ELOAN_PA_INTR_WITHPPP_2T1 = "eloan_pa_intr_withPPP_2t1";

		static final String ELOAN_PA_INTR_NOPPP_CB = "eloan_pa_intr_noPPP_cb";
		static final String ELOAN_PA_INTR_NOPPP_BASERATE = "eloan_pa_intr_noPPP_baseRate";
		static final String ELOAN_PA_INTR_NOPPP_WAY = "eloan_pa_intr_noPPP_way";
		static final String ELOAN_PA_INTR_NOPPP_1T1 = "eloan_pa_intr_noPPP_1t1";
		static final String ELOAN_PA_INTR_NOPPP_2T1 = "eloan_pa_intr_noPPP_2t1";
		static final String ELOAN_PA_INTR_NOPPP_3T1 = "eloan_pa_intr_noPPP_3t1";
		static final String ELOAN_PA_INTR_NOPPP_4T1 = "eloan_pa_intr_noPPP_4t1";

		static final String ELOAN_PA_INTR_OTHER_CB = "eloan_pa_intr_other_cb";
		static final String ELOAN_PA_INTR_OTHER_T1 = "eloan_pa_intr_other_t1";

		static final String ELOAN_PB_GNTEEG_CB = "eloan_pb_gnteeG_cb";
		static final String ELOAN_PB_GNTEEG_B_CB = "eloan_pb_gnteeG_B_cb"; //B:非自用住宅放款(不循環動用)
		static final String ELOAN_PB_GNTEEG_B_T1 = "eloan_pb_gnteeG_B_t1";
		static final String ELOAN_PB_GNTEEG_C_CB = "eloan_pb_gnteeG_C_cb"; //C:歡喜理財家或綜合理財房屋貸款（循環及不循環動用）
		static final String ELOAN_PB_GNTEEG_C_T1 = "eloan_pb_gnteeG_C_t1";
		static final String ELOAN_PB_GNTEEG_D_CB = "eloan_pb_gnteeG_D_cb"; //D:理財型貸款(循環動用)
		//~~~~~~
		static final String ELOAN_PB_GNTEEN_CB = "eloan_pb_gnteeN_cb";
		//在 房貸契約書，選項A指 自用住宅放款 static final String ELOAN_PB_GNTEEN_A_CB = "eloan_pb_gnteeN_A_cb";
		static final String ELOAN_PB_GNTEEN_B_CB = "eloan_pb_gnteeN_B_cb"; //B:非自用住宅放款(不循環動用)
		static final String ELOAN_PB_GNTEEN_B_T1 = "eloan_pb_gnteeN_B_t1";
		static final String ELOAN_PB_GNTEEN_C_CB = "eloan_pb_gnteeN_C_cb"; //C:歡喜理財家或綜合理財房屋貸款（循環及不循環動用）
		static final String ELOAN_PB_GNTEEN_C_T1 = "eloan_pb_gnteeN_C_t1";
		static final String ELOAN_PB_GNTEEN_D_CB = "eloan_pb_gnteeN_D_cb"; //D:理財型貸款(循環動用)

		static final String ELOAN_PC_SERV_TEL_CB = "eloan_pc_serv_tel_cb";
		static final String ELOAN_PC_SERV_TEL_T1 = "eloan_pc_serv_tel_t1";
		static final String ELOAN_PC_SERV_FAX_CB = "eloan_pc_serv_fax_cb";
		static final String ELOAN_PC_SERV_FAX_T1 = "eloan_pc_serv_fax_t1";
		static final String ELOAN_PC_SERV_MAIL_CB = "eloan_pc_serv_mail_cb";
		static final String ELOAN_PC_SERV_MAIL_T1 = "eloan_pc_serv_mail_t1";
		static final String ELOAN_PC_SERV_URL_CB = "eloan_pc_serv_url_cb";
		static final String ELOAN_PC_SERV_CALL_CB = "eloan_pc_serv_call_cb";
		static final String ELOAN_PC_SERV_OTHER_CB = "eloan_pc_serv_other_cb";
		static final String ELOAN_PC_SERV_OTHER_T1 = "eloan_pc_serv_other_t1";
		static final String ELOAN_PC_COURT_LOC = "eloan_pc_court_loc";
		static final String ELOAN_PC_COPY_CNT = "eloan_pc_copy_cnt";
		static final String ELOAN_PC_SPTERM_YRATE = "eloan_pc_spTerm_yRate";
		static final String ELOAN_PC_SPTERM_NOTE = "eloan_pc_spTerm_note";

		static final String ELOAN_P9_SIG_M_NAME = "eloan_p9_sig_m_name";
		static final String ELOAN_P9_SIG_M_CUSTID = "eloan_p9_sig_m_custId";
		static final String ELOAN_P9_SIG_M_ADDR = "eloan_p9_sig_m_addr";
		static final String ELOAN_P9_SIG_1N_CB = "eloan_p9_sig_1N_cb";
		static final String ELOAN_P9_SIG_1N_NAME = "eloan_p9_sig_1N_name";
		static final String ELOAN_P9_SIG_1G_CB = "eloan_p9_sig_1G_cb";
		static final String ELOAN_P9_SIG_1G_NAME = "eloan_p9_sig_1G_name";
		static final String ELOAN_P9_SIG_1_CUSTID = "eloan_p9_sig_1_custId";
		static final String ELOAN_P9_SIG_1_ADDR = "eloan_p9_sig_1_addr";
		static final String ELOAN_P9_SIG_2N_CB = "eloan_p9_sig_2N_cb";
		static final String ELOAN_P9_SIG_2N_NAME = "eloan_p9_sig_2N_name";
		static final String ELOAN_P9_SIG_2G_CB = "eloan_p9_sig_2G_cb";
		static final String ELOAN_P9_SIG_2G_NAME = "eloan_p9_sig_2G_name";
		static final String ELOAN_P9_SIG_2_CUSTID = "eloan_p9_sig_2_custId";
		static final String ELOAN_P9_SIG_2_ADDR = "eloan_p9_sig_2_addr";
		static final String ELOAN_P9_SIG_PARTYB_AGENT = "eloan_p9_sig_partyB_agent";
		static final String ELOAN_P9_SIG_PARTYB_ADDR = "eloan_p9_sig_partyB_addr";
		static final String ELOAN_P9_RECPT_M_NAME = "eloan_p9_recpt_m_name";
		static final String ELOAN_P9_RECPT_S_NAME = "eloan_p9_recpt_s_name";

		static final String ELOAN_P1_RPRINTMODE2_R1 = "eloan_p1_printMode2_r1";
		static final String ELOAN_P1_RPRINTMODE2_R2 = "eloan_p1_printMode2_r2";
		static final String ELOAN_P1_RPRINTMODE2_R3 = "eloan_p1_printMode2_r3";
		static final String ELOAN_P1_RPRINTMODE2_R4 = "eloan_p1_printMode2_r4";
		static final String ELOAN_P1_RPRINTMODE2_R5 = "eloan_p1_printMode2_r5";
		
		//J-113-0050 線上房貸增貸對保契約書-約據及央行切結書資料
		static final String CONSENTVER = "consentVer";
		static final String COLLATERALBUILDINGADDR_1 = "collateralBuildingAddr_1";
		static final String MORTGAGEMAXAMT_1 = "mortgageMaxAmt_1";
		static final String FIRSTLOANDATE_YEAR = "firstLoanDate_year";
		static final String FIRSTLOANDATE_MTH = "firstLoanDate_mth";
		static final String FIRSTLOANDATE_DAY = "firstLoanDate_day";
		static final String FIRSTLOANAMT_1 = "firstLoanAmt_1";
		static final String COLLATERALBUILDINGADDR_2 = "collateralBuildingAddr_2";
		static final String MORTGAGEMAXAMT_2 = "mortgageMaxAmt_2";
		static final String FIRSTLOANAMT_2 = "firstLoanAmt_2";
		static final String HOUSELOANCONTRACTNO ="houseLoanContractNo";
		static final String COLLATERALCONTRACTTERMS = "collateralContractTerms";
		static final String UNREGISTEREDBUILDINGDESC ="unregisteredBuildingDesc";
		static final String COLLATERALCONTRACTTERMS_5_DESC ="collateralContractTerms_5_Desc";
		static final String CBAFFTTERMS ="cbAfftTerms";
		static final String CBAFFT1_1 = "cbAfft1_1";
		static final String CBAFFT1_2 = "cbAfft1_2";
		static final String CBAFFT1_3_YEAR = "cbAfft1_3_year";
		static final String CBAFFT1_3_MTH = "cbAfft1_3_mth";
		static final String CBAFFT1_3_DAY = "cbAfft1_3_day";
		static final String CBAFFT1_4 = "cbAfft1_4";
		static final String CBAFFT1_5 = "cbAfft1_5";
		static final String CBAFFT1_6 = "cbAfft1_6";
		static final String CBAFFT1_7 = "cbAfft1_7";
		static final String CBAFFT1_8 = "cbAfft1_8";
		static final String CBAFFT1_9_YEAR = "cbAfft1_9_year";
		static final String CBAFFT1_9_MTH = "cbAfft1_9_mth";
		static final String CBAFFT1_9_DAY = "cbAfft1_9_day";
		static final String CBAFFT1_10_YEAR = "cbAfft1_10_year";
		static final String CBAFFT1_10_MTH = "cbAfft1_10_mth";
		static final String CBAFFT1_10_DAY = "cbAfft1_10_day";
		static final String CBAFFT1_10_NO = "cbAfft1_10_no";
		static final String CBAFFT1_11 = "cbAfft1_11";
		static final String CBAFFT1_12 = "cbAfft1_12";
		static final String CBAFFT1_13 = "cbAfft1_13";
		static final String CBAFFT1_14 = "cbAfft1_14";
		static final String CBAFFT1_15 = "cbAfft1_15";
		static final String CBAFFT1_16 = "cbAfft1_16";
		static final String CBAFFT2_1 ="cbAfft2_1";
		static final String CBAFFT2_2 ="cbAfft2_2";
		static final String CBAFFT2_3_YEAR ="cbAfft2_3_year";
		static final String CBAFFT2_3_MTH ="cbAfft2_3_mth";
		static final String CBAFFT2_3_DAY ="cbAfft2_3_day";
		static final String CBAFFT2_4 ="cbAfft2_4";
		static final String CBAFFT2_5 ="cbAfft2_5";
		static final String CBAFFT2_6 ="cbAfft2_6";
		static final String CBAFFT2_7 ="cbAfft2_7";
		static final String CBAFFT2_8 ="cbAfft2_8";
		static final String CBAFFT2_9 ="cbAfft2_9";
		static final String CBAFFT2_10 ="cbAfft2_10";
		static final String CBAFFT3_1 ="cbAfft3_1";
		static final String CBAFFT3_2 ="cbAfft3_2";
		static final String CBAFFT3_3_YEAR ="cbAfft3_3_year";
		static final String CBAFFT3_3_MTH ="cbAfft3_3_mth";
		static final String CBAFFT3_3_DAY ="cbAfft3_3_day";
		static final String CBAFFT3_4 ="cbAfft3_4";
		static final String CBAFFT3_5 ="cbAfft3_5";
		static final String CBAFFT4_1 = "cbAfft4_1";
		static final String CBAFFT4_2_YEAR = "cbAfft4_2_year";
		static final String CBAFFT4_2_MTH = "cbAfft4_2_mth";
		static final String CBAFFT4_2_DAY = "cbAfft4_2_day";
		static final String CBAFFT4_3 = "cbAfft4_3";
		static final String CBAFFT4_4 = "cbAfft4_4";
		static final String CBAFFT4_5 = "cbAfft4_5";
		static final String CBAFFT4_6 = "cbAfft4_6";
		static final String CBAFFT4_7 = "cbAfft4_7";
		static final String CBAFFT5_1 = "cbAfft5_1";
		static final String CBAFFT5_2 = "cbAfft5_2";
		static final String CBAFFT5_3_YEAR = "cbAfft5_3_year";
		static final String CBAFFT5_3_MTH = "cbAfft5_3_mth";
		static final String CBAFFT5_3_DAY = "cbAfft5_3_day";
		static final String CBAFFT5_4 = "cbAfft5_4";
		static final String CBAFFT5_5 = "cbAfft5_5";
		static final String CBAFFT5_6 = "cbAfft5_6";
		static final String CBAFFT5_7 = "cbAfft5_7";
		static final String CBAFFT5_8 = "cbAfft5_8";
		static final String CBAFFT5_9 = "cbAfft5_9";
		static final String CBAFFT5_10 = "cbAfft5_10";
		static final String CBAFFT5_11 = "cbAfft5_11";
		static final String CBAFFT5_12 = "cbAfft5_12";
		static final String CBAFFT5_13 = "cbAfft5_13";
		static final String CBAFFT5_14 = "cbAfft5_14";
		static final String CBAFFT5_15 = "cbAfft5_15";
	}
}
