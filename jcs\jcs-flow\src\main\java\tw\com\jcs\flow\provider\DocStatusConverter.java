/**
 * 
 */
package tw.com.jcs.flow.provider;

import tw.com.jcs.flow.FlowInstance;

/**
 * <pre>
 * DocStatusFormatter
 * </pre>
 * 
 * @since 2012/3/19
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2012/3/19,UFO,new
 *          </ul>
 */
public interface DocStatusConverter {

    /**
     * 取得文件狀態
     * 
     * @param instance
     * @return
     */
    public String getDocStatus(FlowInstance instance);
}
