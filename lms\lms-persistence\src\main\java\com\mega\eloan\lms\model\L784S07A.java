/* 
 * L784S07A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;

/** 常董會及申報案件明細檔 **/
@Entity
@Table(name = "L784S07A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "brNo", "apprYY", "apprMM", "caseDept" }))
public class L784S07A extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 單位別
	 * <p/>
	 * MIS.ELCSECNT.BRNO
	 */
	@Column(name = "BRNO", length = 3, columnDefinition = "CHAR(03)")
	private String brNo;

	/**
	 * 案件核准年度 (民國年)
	 * <p/>
	 * MIS.ELCSECNT.APPRYY
	 */
	@Column(name = "APPRYY", length = 4, columnDefinition = "CHAR(04)")
	private String apprYY;

	/**
	 * 案件核准月份
	 * <p/>
	 * MIS.ELCSECNT.APPRMM
	 */
	@Column(name = "APPRMM", length = 2, columnDefinition = "CHAR(02)")
	private String apprMM;

	/**
	 * 案件隸屬部門
	 * <p/>
	 * MIS.ELCSECNT.CASEDEPT<br/>
	 * 企金部<br/>
	 * 個金部<br/>
	 * 企金+個金<br/>
	 * ※目前固定為(企金+個金)
	 */
	@Column(name = "CASEDEPT", length = 1, columnDefinition = "CHAR(01)")
	private String caseDept;

	/**
	 * 新做(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM1)
	 */
	@Column(name = "CITEM1REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem1Rec;

	/**
	 * 新做(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM1AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem1Amt;

	/**
	 * 續約(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM2)
	 */
	@Column(name = "CITEM2REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem2Rec;

	/**
	 * 續約(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM2AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem2Amt;

	/**
	 * 變更條件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM3)
	 */
	@Column(name = "CITEM3REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem3Rec;

	/**
	 * 變更條件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM3AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem3Amt;

	/**
	 * 無擔保授信(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM4)
	 */
	@Column(name = "CITEM4REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem4Rec;

	/**
	 * 無擔保授信(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM4AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem4Amt;

	/**
	 * 擔保授信(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM5)
	 */
	@Column(name = "CITEM5REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem5Rec;

	/**
	 * 擔保授信(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM5AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem5Amt;

	/**
	 * 申報案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM6)
	 */
	@Column(name = "CITEM6REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem6Rec;

	/**
	 * 申報案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM6AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem6Amt;

	/**
	 * 核准案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM7)
	 */
	@Column(name = "CITEM7REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem7Rec;

	/**
	 * 核准案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM7AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem7Amt;

	/**
	 * 授權內案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM8)
	 */
	@Column(name = "CITEM8REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem8Rec;

	/**
	 * 授權內案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM8AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem8Amt;

	/**
	 * 提會案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM9)
	 */
	@Column(name = "CITEM9REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem9Rec;

	/**
	 * 提會案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM9AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem9Amt;

	/**
	 * 常董會案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM10)
	 */
	@Column(name = "CITEM10REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem10Rec;

	/**
	 * 常董會案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM10AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem10Amt;

	/**
	 * 覆審案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM11)
	 */
	@Column(name = "CITEM11REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem11Rec;

	/**
	 * 覆審案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM11AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem11Amt;

	/**
	 * 逾放展期、轉正常(筆數)
	 * <p/>
	 * ※本欄位於資料產生後自行輸入
	 */
	@Column(name = "CITEM12REC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem12Rec;

	/**
	 * 逾放展期、轉正常(金額)
	 * <p/>
	 * ※本欄位於資料產生後自行輸入
	 */
	@Column(name = "CITEM12AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem12Amt;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 新做新客戶(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM1)
	 */
	@Column(name = "CITEM1NEWREC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem1NewRec;

	/**
	 * 新做新客戶(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM1NEWAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem1NewAmt;

	/**
	 * 新做舊客戶(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM1)
	 */
	@Column(name = "CITEM1OLDREC", columnDefinition = "DECIMAL(5,0)")
	private Integer cItem1OldRec;

	/**
	 * 新做舊客戶(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	@Column(name = "CITEM1OLDAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cItem1OldAmt;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得單位別
	 * <p/>
	 * MIS.ELCSECNT.BRNO
	 */
	public String getBrNo() {
		return this.brNo;
	}

	/**
	 * 設定單位別
	 * <p/>
	 * MIS.ELCSECNT.BRNO
	 **/
	public void setBrNo(String value) {
		this.brNo = value;
	}

	/**
	 * 取得案件核准年度 (民國年)
	 * <p/>
	 * MIS.ELCSECNT.APPRYY
	 */
	public String getApprYY() {
		return this.apprYY;
	}

	/**
	 * 設定案件核准年度 (民國年)
	 * <p/>
	 * MIS.ELCSECNT.APPRYY
	 **/
	public void setApprYY(String value) {
		this.apprYY = value;
	}

	/**
	 * 設定案件核准月份
	 * <p/>
	 * MIS.ELCSECNT.APPRMM
	 **/
	public void setApprMM(String value) {
		this.apprMM = value;
	}

	/**
	 * 取得案件核准月份
	 * <p/>
	 * MIS.ELCSECNT.APPRMM
	 **/
	public String getApprMM() {
		return this.apprMM;
	}

	/**
	 * 取得案件隸屬部門
	 * <p/>
	 * MIS.ELCSECNT.CASEDEPT<br/>
	 * 企金部<br/>
	 * 個金部<br/>
	 * 企金+個金<br/>
	 * ※目前固定為(企金+個金)
	 */
	public String getCaseDept() {
		return this.caseDept;
	}

	/**
	 * 設定案件隸屬部門
	 * <p/>
	 * MIS.ELCSECNT.CASEDEPT<br/>
	 * 企金部<br/>
	 * 個金部<br/>
	 * 企金+個金<br/>
	 * ※目前固定為(企金+個金)
	 **/
	public void setCaseDept(String value) {
		this.caseDept = value;
	}

	/**
	 * 取得新做(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM1)
	 */
	public Integer getCItem1Rec() {
		return this.cItem1Rec;
	}

	/**
	 * 設定新做(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM1)
	 **/
	public void setCItem1Rec(Integer value) {
		this.cItem1Rec = value;
	}

	/**
	 * 取得新做(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem1Amt() {
		return this.cItem1Amt;
	}

	/**
	 * 設定新做(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem1Amt(BigDecimal value) {
		this.cItem1Amt = value;
	}

	/**
	 * 取得續約(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM2)
	 */
	public Integer getCItem2Rec() {
		return this.cItem2Rec;
	}

	/**
	 * 設定續約(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM2)
	 **/
	public void setCItem2Rec(Integer value) {
		this.cItem2Rec = value;
	}

	/**
	 * 取得續約(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem2Amt() {
		return this.cItem2Amt;
	}

	/**
	 * 設定續約(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem2Amt(BigDecimal value) {
		this.cItem2Amt = value;
	}

	/**
	 * 取得變更條件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM3)
	 */
	public Integer getCItem3Rec() {
		return this.cItem3Rec;
	}

	/**
	 * 設定變更條件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM3)
	 **/
	public void setCItem3Rec(Integer value) {
		this.cItem3Rec = value;
	}

	/**
	 * 取得變更條件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem3Amt() {
		return this.cItem3Amt;
	}

	/**
	 * 設定變更條件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem3Amt(BigDecimal value) {
		this.cItem3Amt = value;
	}

	/**
	 * 取得無擔保授信(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM4)
	 */
	public Integer getCItem4Rec() {
		return this.cItem4Rec;
	}

	/**
	 * 設定無擔保授信(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM4)
	 **/
	public void setCItem4Rec(Integer value) {
		this.cItem4Rec = value;
	}

	/**
	 * 取得無擔保授信(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem4Amt() {
		return this.cItem4Amt;
	}

	/**
	 * 設定無擔保授信(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem4Amt(BigDecimal value) {
		this.cItem4Amt = value;
	}

	/**
	 * 取得擔保授信(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM5)
	 */
	public Integer getCItem5Rec() {
		return this.cItem5Rec;
	}

	/**
	 * 設定擔保授信(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM5)
	 **/
	public void setCItem5Rec(Integer value) {
		this.cItem5Rec = value;
	}

	/**
	 * 取得擔保授信(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem5Amt() {
		return this.cItem5Amt;
	}

	/**
	 * 設定擔保授信(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem5Amt(BigDecimal value) {
		this.cItem5Amt = value;
	}

	/**
	 * 取得申報案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM6)
	 */
	public Integer getCItem6Rec() {
		return this.cItem6Rec;
	}

	/**
	 * 設定申報案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM6)
	 **/
	public void setCItem6Rec(Integer value) {
		this.cItem6Rec = value;
	}

	/**
	 * 取得申報案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem6Amt() {
		return this.cItem6Amt;
	}

	/**
	 * 設定申報案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem6Amt(BigDecimal value) {
		this.cItem6Amt = value;
	}

	/**
	 * 取得核准案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM7)
	 */
	public Integer getCItem7Rec() {
		return this.cItem7Rec;
	}

	/**
	 * 設定核准案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM7)
	 **/
	public void setCItem7Rec(Integer value) {
		this.cItem7Rec = value;
	}

	/**
	 * 取得核准案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem7Amt() {
		return this.cItem7Amt;
	}

	/**
	 * 設定核准案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem7Amt(BigDecimal value) {
		this.cItem7Amt = value;
	}

	/**
	 * 取得授權內案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM8)
	 */
	public Integer getCItem8Rec() {
		return this.cItem8Rec;
	}

	/**
	 * 設定授權內案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM8)
	 **/
	public void setCItem8Rec(Integer value) {
		this.cItem8Rec = value;
	}

	/**
	 * 取得授權內案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem8Amt() {
		return this.cItem8Amt;
	}

	/**
	 * 設定授權內案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem8Amt(BigDecimal value) {
		this.cItem8Amt = value;
	}

	/**
	 * 取得提會案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM9)
	 */
	public Integer getCItem9Rec() {
		return this.cItem9Rec;
	}

	/**
	 * 設定提會案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM9)
	 **/
	public void setCItem9Rec(Integer value) {
		this.cItem9Rec = value;
	}

	/**
	 * 取得提會案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem9Amt() {
		return this.cItem9Amt;
	}

	/**
	 * 設定提會案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem9Amt(BigDecimal value) {
		this.cItem9Amt = value;
	}

	/**
	 * 取得常董會案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM10)
	 */
	public Integer getCItem10Rec() {
		return this.cItem10Rec;
	}

	/**
	 * 設定常董會案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM10)
	 **/
	public void setCItem10Rec(Integer value) {
		this.cItem10Rec = value;
	}

	/**
	 * 取得常董會案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem10Amt() {
		return this.cItem10Amt;
	}

	/**
	 * 設定常董會案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem10Amt(BigDecimal value) {
		this.cItem10Amt = value;
	}

	/**
	 * 取得覆審案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM11)
	 */
	public Integer getCItem11Rec() {
		return this.cItem11Rec;
	}

	/**
	 * 設定覆審案件(筆數)
	 * <p/>
	 * sum(MIS.ELCSECNT.CITEM11)
	 **/
	public void setCItem11Rec(Integer value) {
		this.cItem11Rec = value;
	}

	/**
	 * 取得覆審案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 */
	public BigDecimal getCItem11Amt() {
		return this.cItem11Amt;
	}

	/**
	 * 設定覆審案件(金額)
	 * <p/>
	 * sum(MIS.ELCSECNT.APPRAMT)
	 **/
	public void setCItem11Amt(BigDecimal value) {
		this.cItem11Amt = value;
	}

	/**
	 * 取得逾放展期、轉正常(筆數)
	 * <p/>
	 * ※本欄位於資料產生後自行輸入
	 */
	public Integer getCItem12Rec() {
		return this.cItem12Rec;
	}

	/**
	 * 設定逾放展期、轉正常(筆數)
	 * <p/>
	 * ※本欄位於資料產生後自行輸入
	 **/
	public void setCItem12Rec(Integer value) {
		this.cItem12Rec = value;
	}

	/**
	 * 取得逾放展期、轉正常(金額)
	 * <p/>
	 * ※本欄位於資料產生後自行輸入
	 */
	public BigDecimal getCItem12Amt() {
		return this.cItem12Amt;
	}

	/**
	 * 設定逾放展期、轉正常(金額)
	 * <p/>
	 * ※本欄位於資料產生後自行輸入
	 **/
	public void setCItem12Amt(BigDecimal value) {
		this.cItem12Amt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 設定新做新客戶(筆數) **/
	public void setCItem1NewRec(Integer cItem1NewRec) {
		this.cItem1NewRec = cItem1NewRec;
	}

	/** 取得新做新客戶(筆數) **/
	public Integer getCItem1NewRec() {
		return cItem1NewRec;
	}

	/** 設定新做新客戶(金額) **/
	public void setCItem1NewAmt(BigDecimal cItem1NewAmt) {
		this.cItem1NewAmt = cItem1NewAmt;
	}

	/** 取得新做新客戶(金額) **/
	public BigDecimal getCItem1NewAmt() {
		return cItem1NewAmt;
	}

	/** 設定新做舊客戶(筆數) **/
	public void setCItem1OldRec(Integer cItem1OldRec) {
		this.cItem1OldRec = cItem1OldRec;
	}

	/** 取得新做舊客戶(筆數) **/
	public Integer getCItem1OldRec() {
		return cItem1OldRec;
	}

	/** 設定新做舊客戶(金額) **/
	public void setCItem1OldAmt(BigDecimal cItem1OldAmt) {
		this.cItem1OldAmt = cItem1OldAmt;
	}

	/** 取得新做舊客戶(金額) **/
	public BigDecimal getCItem1OldAmt() {
		return cItem1OldAmt;
	}
}
