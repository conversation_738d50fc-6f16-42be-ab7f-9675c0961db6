var _iHandler = "";
var _handler = "";
$(document).ready(function() {
	//2012_07_20_rex add 取得sso 連線資訊 
	BrowserAction.init();
	setCloseConfirm(true);
	_handler = "cls1141m01formhandler";	
	$("#tformL120m01e1").find("input[name='radioKind1']").click(function(i) {
		$("#tformL120m01e1").find("#other").hide();
		if ($(this).val() == "2") {
			$("#tformL120m01e1").find("#other").show();
		} else {
			$("#tformL120m01e1").find("#other").hide();
		}
	});
	$("#bDocDscr1").one("click",function(){
		gridCesDbu1("");
		gridCesDbu2("");
		gridCesDbu3("");	
	});
	$("#bDocDscr2").one("click",function(){
		var gridCesDbu4 = $("#gridCesDbu4").iGrid({		//借款人基本資料GridView
			handler : 'cls1141gridhandler',
			height : 175,
			sortname : 'createTime',
			postData : {
				formAction : "queryCesMainIda",
				rowNum:5
			},
			rownumbers:true,
			rowNum:5,
			multiselect: true,
			hideMultiselect:false,
			caption: "&nbsp;",
			hiddengrid : false,
			//multiselect : true,
			colModel : [ {
				colHeader : i18n.clss08a["L120S08.grid13"], //建立日期
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				name : 'createTime' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid15"], //核准日期
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'approveTime' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid14"], //文件狀態
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'docStatus' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid12"], //主要借款人
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custName' //col.id
			}, {
				colHeader : "mainId",
				name : 'mainId',
				hidden : true
			}],
			ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			}
		});
		
		var gridCesDbu5 = $("#gridCesDbu5").iGrid({		//借款人基本資料GridView
			handler : 'cls1141gridhandler',
			height : 175,
			multiselect: true,
			hideMultiselect:false,
			caption: "&nbsp;",
			hiddengrid : false,
			sortname : 'createTime',
			postData : {
				formAction : "queryCesMainIdb",
				rowNum:5
			},
			rownumbers:true,
			rowNum:5,
			//multiselect : true,
			colModel : [ {
				colHeader : i18n.clss08a["L120S08.grid13"], //建立日期
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				name : 'createTime' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid15"], //核准日期
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'approveTime' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid14"], //文件狀態
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'docStatus' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid12"], //主要借款人
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custName' //col.id
			}, {
				colHeader : "mainId",
				name : 'mainId',
				hidden : true
			}],
			ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			}
		});
		
		var gridCesDbu6 = $("#gridCesDbu6").iGrid({		//借款人基本資料GridView
			handler : 'cls1141gridhandler',
			height : 175,
			multiselect: true,
			hideMultiselect:false,
			caption: "&nbsp;",
			hiddengrid : false,
			sortname : 'createTime',
			postData : {
				formAction : "queryCesMainIdc",
				rowNum:5
			},
			rownumbers:true,
			rowNum:5,
			//multiselect : true,
			colModel : [ {
				colHeader : i18n.clss08a["L120S08.grid13"], //建立日期
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				name : 'createTime' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid15"], //核准日期
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'approveTime' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid14"], //文件狀態
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'docStatus' //col.id
			}, {
				colHeader : i18n.clss08a["L120S08.grid12"], //主要借款人
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custName' //col.id
			}, {
				colHeader : "mainId",
				name : 'mainId',
				hidden : true
			}],
			ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			}
		});	
	});	
	
	
	//J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
	$("#docDscr1 a").live( "click", function() {
		//徵信報告書
	    getMain(DOMPurify.sanitize(this.title));
	});
	
	$("#docDscr2 a").live( "click", function() {
		//資信簡表
	    getCes(DOMPurify.sanitize(this.title));
	});
	
});

function clearContent(docType){
	$.ajax({ // 查詢主要借款人資料
		handler : _handler,
		type : "POST",
		dataType : "json",
		data : {
			formAction : "clearContent",
			mainId : responseJSON.mainId,
			docType : docType
		},
		success : function(json) {
			if(docType == "1"){
				$("#docDscr1").html("");
				$("#docDscr1").val("");	
			}else if(docType == "2"){
				$("#docDscr2").html("");
				$("#docDscr2").val("");		
			}
		}
	});	
}

function getMain(ces){
	$.ajax({ // 查詢主要借款人資料
		handler : _handler,
		type : "POST",
		dataType : "json",
		data : {
			formAction : "getRelate1",
			mainId : responseJSON.mainId,
			cesMainId : ces
		},
		success : function(json) {
			// 徵信報告
			BrowserAction.submit({
				system : "ces",
				url    : json.url,
				mainId : json.mainId,
				mainOid : json.mainOid,
				txCode : json.txCode,
				gaapFlag : json.gaapFlag,
				data   : { //其它參數
					fromView: true,
					uid : json.uid,
					mainDocStatus : json.mainDocStatus,
					oid : json.mainOid,
					mainOid : json.mainOid
				}
			});	
		}
	});	
}

function getCes(ces){
	$.ajax({ // 查詢主要借款人資料
		handler : _handler,
		type : "POST",
		dataType : "json",
		data : {
			formAction : "getRelate2",
			mainId : responseJSON.mainId,
			cesMainId : ces
		},
		success : function(json) {
			// 資信簡表
			BrowserAction.submit({
				system : "ces",
				url    : json.url,
				mainId : json.mainId,
				mainOid : json.mainOid,
				txCode : json.txCode,
				gaapFlag : json.gaapFlag,
				data   : { //其它參數
					uid : json.uid,
					mainDocStatus : json.mainDocStatus,
					oid : json.mainOid,
					mainOid : json.mainOid
				}
			});			
		}
	});	
}

function cesGridDbu1(custId) {
	$("#gridCesDbu1").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIds2",
			custId : custId,
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu2(custId) {
	$("#gridCesDbu2").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIds",
			custId : custId,
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu3() {
	$("#gridCesDbu3").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIdss2",
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu4(custId) {
	$("#gridCesDbu4").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIda",
			custId : custId,
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu5(custId) {
	$("#gridCesDbu5").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIdb",
			custId : custId,
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu6() {
	$("#gridCesDbu6").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIdc",
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function gridCesDbu1(custId) {
	var gridCesDbu1 = $("#gridCesDbu1").iGrid({
		handler : 'cls1141gridhandler',
		height : 175,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIds2",
			custId : custId,
			rowNum : 10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers : true,
		rowNum : 10,
		multiselect: true,
		hideMultiselect:false,
		colModel : [ {
			colHeader : i18n.clss08a["L120S08.grid13"], // 建立日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'createTime' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid15"], // 核准日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'approveTime' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid14"], // 文件狀態
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'docStatus' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid12"], // 主要借款人
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'custName' // col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		} ],
		ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
}

function gridCesDbu2(custId) {
	var gridCesDbu2 = $("#gridCesDbu2").iGrid({
		handler : 'cls1141gridhandler',
		height : 175,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIds",
			custId : custId,
			rowNum : 10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers : true,
		rowNum : 10,
		multiselect: true,
		hideMultiselect:false,
		colModel : [ {
			colHeader : i18n.clss08a["L120S08.grid13"], // 建立日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'createTime' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid15"], // 核准日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'approveTime' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid14"], // 文件狀態
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'docStatus' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid12"], // 主要借款人
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'custName' // col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		} ],
		ondblClickRow : function(rowid) {
			var data = gridCesDbu.getRowData(rowid);
		}
	});
}

function gridCesDbu3(custId) {
	var gridCesDbu3 = $("#gridCesDbu3").iGrid({
		handler : 'cls1141gridhandler',
		height : 175,
		sortname : 'custName',
		postData : {
			formAction : "queryCesMainIdss2",
			rowNum : 10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers : true,
		rowNum : 10,
		multiselect: true,
		hideMultiselect:false,
		colModel : [ {
			colHeader : i18n.clss08a["L120S08.grid12"], // 主要借款人
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'custName' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid15"], // 核准日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'approveTime' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid14"], // 文件狀態
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'docStatus' // col.id
		}, {
			colHeader : i18n.clss08a["ces1405.0203"], // 徵信報告編號
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'cesId' // col.id
		}, {
			colHeader : i18n.clss08a["L120S08.grid16"], // 文件建立者
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'creator' // col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		} ],
		ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
}
//徵信報告
function openCesDbu(value, custId) {
	//初始化Grid選項(將Grid選項清空)
	$("#gridCesDbu1").resetSelection();
	$("#gridCesDbu2").resetSelection();
	$("#gridCesDbu3").resetSelection();
	if (value == 1) {
		cesGridDbu1(custId);
	} else if (value == 2) {
		cesGridDbu2(custId);
	} else {
		cesGridDbu3();
	}
	$("#openCesDbu").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.clss08a["L120S08.thickbox6"],
		width : 640,
		height : 400,
		modal : true,
		align : "center",
		valign : "bottom",
		i18n : i18n.clss08a,
		buttons : {
			"L120S08.thickbox1" : function() {
				var $formL120m01e = $("#formL120m01e");
				//取得使用者選擇依借款人Grid資料
				var rows1 = $("#gridCesDbu1").getGridParam('selarrrow');
				var list1 = "";
				var sign1 = ",";
				for (var i=0;i<rows1.length;i++){	//將所有已選擇的資料存進變數list裡面
					if (rows1[i] != 'undefined' && rows1[i] != null && rows1[i] != 0){
						var data1 = $("#gridCesDbu1").getRowData(rows1[i]);
						if(data1.docStatus == i18n.abstracteloan["docStatus.230"]){
							list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
						}else{
							list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
							 CommonAPI.showErrorMessage(i18n.clss08a('L120S08.alert3', {
						          'custname': data1.custName,
								  'ceskind' : i18n.clss08a["L120S08.index4"]
						    }));
							 return;
						}						
					}
				}								
				
				//取得使用者選擇依統編Grid資料
				var rows2 = $("#gridCesDbu2").getGridParam('selarrrow');
				var list2 = "";
				var sign2 = ",";
				for (var j=0;j<rows2.length;j++){	//將所有已選擇的資料存進變數list裡面
					if (rows2[j] != 'undefined' && rows2[j] != null && rows2[j] != 0){
						var data2 = $("#gridCesDbu2").getRowData(rows2[j]);
						if(data2.docStatus == i18n.abstracteloan["docStatus.230"]){
							list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
						}else{
							list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
							 CommonAPI.showErrorMessage(i18n.clss08a('L120S08.alert3', {
						          'custname': data2.custName,
								  'ceskind' : i18n.clss08a["L120S08.index4"]
						    }));
							 return;
						}						
					}
				}				

				//取得使用者選擇所有徵信Grid資料
				var rows3 = $("#gridCesDbu3").getGridParam('selarrrow');
				var list3 = "";
				var sign3 = ",";
				for (var k=0;k<rows3.length;k++){	//將所有已選擇的資料存進變數list裡面
					if (rows3[k] != 'undefined' && rows3[k] != null && rows3[k] != 0){
						var data3 = $("#gridCesDbu3").getRowData(rows3[k]);
						if(data3.docStatus == i18n.abstracteloan["docStatus.230"]){
							list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
						}else{
							list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
							 CommonAPI.showErrorMessage(i18n.clss08a('L120S08.alert3', {
						          'custname': data3.custName,
								  'ceskind' : i18n.clss08a["L120S08.index4"]
						    }));
							 return;
						}						
					}
				}

				if (list1 != "" || list2 != "" || list3 != "") {
					//如果三個Grid資料其中一筆有被選擇
					var list = "";
					var data = "";
					//取得確切選擇的那筆資料文件編號(MainId)
					if (list1 != "") {
						list = list1;
						data = data1;
					} else if (list2 != "") {
						list = list2;
						data = data2;
					} else {
						list = list3;
						data = data3;
					} 
					$.ajax({ // 查詢主要借款人資料
						handler : _handler,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "findRelate1",
							mainId : responseJSON.mainId,
							formL120m01e : JSON.stringify($formL120m01e.serializeData()),
							cesMainId : list
						},
						success : function(json) {
							//alert(JSON.stringify(json));
							//J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
							$("#docDscr1").html(DOMPurify.sanitize(json.docDscr1));
							if(json.docDscr1 != "" && json.docDscr1 != undefined && json.docDscr1 != null){
								$("#docDscr1 a").attr({"href":"#"});	
							}							
						}
					});
					$.thickbox.close();
					$.thickbox.close();
				}else{
					//並未選擇任何資料
					CommonAPI.showMessage(i18n.clss08a["L120S08.alert1"]);
				}				
			},
			"L120S08.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

//資信簡表
function openCesDbu2(value, custId) {
	//初始化Grid選項(將Grid選項清空)
	$("#gridCesDbu4").resetSelection();
	$("#gridCesDbu5").resetSelection();
	$("#gridCesDbu6").resetSelection();
	if (value == 1) {
		cesGridDbu4(custId);
	} else if (value == 2) {
		cesGridDbu5(custId);
	} else {
		cesGridDbu6();
	}
	$("#openCesDbu2").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.clss08a["L120S08.thickbox6"],
		width : 640,
		height : 400,
		modal : true,
		align : "center",
		valign : "bottom",
		i18n : i18n.clss08a,
		buttons : {
			"L120S08.thickbox1" : function() {
				var $formL120m01e = $("#formL120m01e");
				//取得使用者選擇依借款人Grid資料
				var rows1 = $("#gridCesDbu4").getGridParam('selarrrow');
				var list1 = "";
				var sign1 = ",";
				for (var i=0;i<rows1.length;i++){	//將所有已選擇的資料存進變數list裡面
					if (rows1[i] != 'undefined' && rows1[i] != null && rows1[i] != 0){
						var data1 = $("#gridCesDbu4").getRowData(rows1[i]);
						if(data1.docStatus == i18n.abstracteloan["docStatus.230"]){
							list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
						}else{
							list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
							 CommonAPI.showErrorMessage(i18n.clss08a('L120S08.alert3', {
						          'custname': data1.custName,
								  'ceskind' : i18n.clss08a["L120S08.index5"]
						    }));
							 return;
						}						
						//list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
					}
				}								
				
				//取得使用者選擇依統編Grid資料
				var rows2 = $("#gridCesDbu5").getGridParam('selarrrow');
				var list2 = "";
				var sign2 = ",";
				for (var j=0;j<rows2.length;j++){	//將所有已選擇的資料存進變數list裡面
					if (rows2[j] != 'undefined' && rows2[j] != null && rows2[j] != 0){
						var data2 = $("#gridCesDbu5").getRowData(rows2[j]);
						if(data2.docStatus == i18n.abstracteloan["docStatus.230"]){
							list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
						}else{
							list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
							 CommonAPI.showErrorMessage(i18n.clss08a('L120S08.alert3', {
						          'custname': data2.custName,
								  'ceskind' : i18n.clss08a["L120S08.index5"]
						    }));
							 return;
						}						
						//list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
					}
				}				

				//取得使用者選擇所有徵信Grid資料
				var rows3 = $("#gridCesDbu6").getGridParam('selarrrow');
				var list3 = "";
				var sign3 = ",";
				for (var k=0;k<rows3.length;k++){	//將所有已選擇的資料存進變數list裡面
					if (rows3[k] != 'undefined' && rows3[k] != null && rows3[k] != 0){
						var data3 = $("#gridCesDbu6").getRowData(rows3[k]);
						if(data3.docStatus == i18n.abstracteloan["docStatus.230"]){
							list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
						}else{
							list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
							 CommonAPI.showErrorMessage(i18n.clss08a('L120S08.alert3', {
						          'custname': data3.custName,
								  'ceskind' : i18n.clss08a["L120S08.index5"]
						    }));
							 return;
						}						
						//list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
					}
				}

				if (list1 != "" || list2 != "" || list3 != "") {
					//如果三個Grid資料其中一筆有被選擇
					var list = "";
					var data = "";
					//取得確切選擇的那筆資料文件編號(MainId)
					if (list1 != "") {
						list = list1;
						data = data1;
					} else if (list2 != "") {
						list = list2;
						data = data2;
					} else {
						list = list3;
						data = data3;
					} 
					$.ajax({ // 查詢主要借款人資料
						handler : _handler,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "findRelate2",
							formL120m01e : JSON.stringify($formL120m01e.serializeData()),
							mainId : responseJSON.mainId,
							cesMainId : list
						},
						success : function(json) {
							//alert(JSON.stringify(json));
							//J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
							$("#docDscr2").html(DOMPurify.sanitize(json.docDscr2));
							if(json.docDscr2 != "" && json.docDscr2 != undefined && json.docDscr2 != null){
								$("#docDscr2 a").attr({"href":"#"});
							}							
						}
					});
					$.thickbox.close();
					$.thickbox.close();
				}else{
					//並未選擇任何資料
					CommonAPI.showMessage(i18n.clss08a["L120S08.alert1"]);
				}				
			},
			"L120S08.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}


function seachKind1() {
	$("#seachKind1").find("#ceskind1").show();
	$("#seachKind1").find("#ceskind2").hide();
	$("#seachKind1")
			.thickbox(
					{ // 使用選取的內容進行彈窗
						title : i18n.clss08a["L120S08.thickbox7"],
						width : 350,
						height : 200,
						modal : true,
						valign : "bottom",
						align : "center",
						i18n : i18n.clss08a,
						buttons : {
							"L120S08.thickbox1" : function() {
								$(function() {
									$("#tformL120m01e0 .cesGrid").hide();
									var value = $(
											"#tformL120m01e1 input[name='radioKind1']:checked")
											.val();
									if (value == 1) {
										$("#tformL120m01e0").find(
												"#hideCesGrid1").show();
										openCesDbu(value, "");
									} else if (value == 2) {
										$("#tformL120m01e0").find(
												"#hideCesGrid2").show();
										var other = $("#tformL120m01e1").find(
												"#other").val();										
										if(other != ""){
											openCesDbu(value, other);
										} else{
											//尚未輸入統編
											CommonAPI.showMessage(i18n.clss08a["L120S08.alert2"]);
										}
									} else {
										$("#tformL120m01e0").find(
												"#hideCesGrid3").show();
										openCesDbu(value, "");
									}
								});
							},
							"L120S08.thickbox2" : function() {
								 API.confirmMessage(i18n.def['flow.exit'], function(res){
										if(res){
											$.thickbox.close();
										}
							        });
							}
						}
					});
}

function seachKind2() {
	$("#seachKind1").find("#ceskind2").show();
	$("#seachKind1").find("#ceskind1").hide();
	$("#seachKind1")
			.thickbox(
					{ // 使用選取的內容進行彈窗
						title : i18n.clss08a["L120S08.thickbox14"],
						width : 350,
						height : 200,
						modal : true,
						valign : "bottom",
						align : "center",
						i18n : i18n.clss08a,
						buttons : {
							"L120S08.thickbox1" : function() {
								$(function() {
									$("#tformL120m01e0a .cesGrid2").hide();
									var value = $(
											"#tformL120m01e1 input[name='radioKind1']:checked")
											.val();
									if (value == 1) {
										$("#tformL120m01e0a").find(
												"#hideCesGrid4").show();
										openCesDbu2(value, "");
									} else if (value == 2) {
										$("#tformL120m01e0a").find(
												"#hideCesGrid5").show();
										var other = $("#tformL120m01e1").find(
												"#other").val();										
										if(other != ""){
											openCesDbu2(value, other);
										} else{
											//尚未輸入統編
											CommonAPI.showMessage(i18n.clss08a["L120S08.alert2"]);
										}
									} else {
										$("#tformL120m01e0a").find(
												"#hideCesGrid6").show();
										openCesDbu2(value, "");
									}
								});
							},
							"L120S08.thickbox2" : function() {
								 API.confirmMessage(i18n.def['flow.exit'], function(res){
										if(res){
											$.thickbox.close();
										}
							        });
							}
						}
					});
}