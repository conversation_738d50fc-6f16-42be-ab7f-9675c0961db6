/* 
 * CLS1131S01XPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2021/06/03
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/06/03,Benrison,new
 *          </ul>
 */
public class CLS1131S01XPanel extends Panel {

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS1131S01XPanel(String id) {
		super(id);
	}
}
