var _handler = "cls3701m01formhandler";
$(function(){	
	var hidden05O = true;
    if(viewstatus == "05O"){
        hidden05O = false;
    }
	var grid = $("#gridview").iGrid({
        handler: 'cls3701gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        postData: {
            formAction: "queryView",
            docStatus : viewstatus
        },
        rowNum: 15,
//        sortname: "createTime|custId",
//        sortorder: "desc|asc|desc",//desc
        multiselect: true,
        colModel: [{
        	colHeader: i18n.cls3701m01["C126M01A.ownBrId"],
            align: "left",
            width: 100, // 設定寬
            name: 'ownBrId'
        }, {
            colHeader: i18n.cls3701m01["C126M01A.custId"],
            align: "left", width: 90, sortable: true, name: 'custId',
            formatter: 'click', onclick: openDoc
        }, {
            colHeader: i18n.cls3701m01["C126M01A.dupNo"],
            align: "left", width: 10, sortable: true, name: 'dupNo'
        }, {
            colHeader: i18n.cls3701m01["C126M01A.custName"],
            align: "left", width: 120, sortable: true, name: 'custName'
        }, {
            colHeader: i18n.cls3701m01["C126M01A.agntNo"], //房仲代號
            align: "left",
            width: 100, // 設定寬
            name: 'agntNo'
        }, {
            colHeader: i18n.cls3701m01["C126M01A.statFlag"], //承作情形
            align: "left",
            width: 100, // 設定寬
            name: 'statFlag'
        }, {
            colHeader: i18n.cls3701m01['C126M01A.updater'],//經辦
            name: 'updater',
            width: 80,
            align: "center"
        }, {
            colHeader: i18n.cls3701m01["C126M01A.createTime"], //建立日期
            align: "center",
            width: 80, // 設定寬
            name: 'createTime',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d H:i'
            }
        }, {
            colHeader: i18n.cls3701m01['C126M01A.approver'],//覆核人員
            name: 'approver',
            width: 80,
            align: "center"
        }, {
            colHeader: i18n.cls3701m01['C126M01A.approveTime'],//覆核日期
            name: 'approveTime',
            width: 80,
            align: "center"
        },{
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        },
        codetypeItem: {}
    });
	
	function openDoc(cellvalue, options, rowObject) {
		$.form.submit({
			url : '../cls/cls3701m01/01',
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus
			},
			target : rowObject.oid
		});					
	};
	
	
    $("#buttonPanel").find("#btnView").click(function(){
    	var id = $("#gridview").getGridParam('selarrrow');
        if (id.length==0) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
        	// includeId.selData=請選擇一筆資料!!
        	return CommonAPI.showMessage(i18n.def["includeId.selData"]);
        } else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
    	openFilterBox();
    }).end().find("#btnAdd").click(function(){
    	chose_custId().done(function(resultFrom_chose_custId){
    		$.ajax({
                handler: _handler,
                action : 'newC126M01A',
                data : {
                    custId: resultFrom_chose_custId.custId,
                    dupNo: resultFrom_chose_custId.dupNo,
                    custName: resultFrom_chose_custId.custName
                },
                success: function(obj){
                	if(obj.oid!=null){
	                    $.form.submit({
	                        url: '../cls/cls3701m01/01',
	                        data: {
	                        	oid: obj.oid,
                                mainOid: obj.oid,
                                mainDocStatus: viewstatus,
                                txCode: txCode,
                                custId: resultFrom_chose_custId.custId,
                                dupNo: resultFrom_chose_custId.dupNo,
                                custName: resultFrom_chose_custId.custName,
                                mainId: obj.mainId
	                        },
	                        target: obj.oid
	                    });
	                    $("#gridview").trigger("reloadGrid");
                	}
                	else{
                		
                		return CommonAPI.showMessage(i18n.cls3701m01["cls3701.err1"]);
                	}
                }
            });
	    });
    }).end().find("#btnDelete").click(function(){ //編製中-刪除
    	var rows = $("#gridview").getGridParam('selarrrow');
		var data = [];
		if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
		    // action_005=請先選取一筆以上之資料列
            return CommonAPI.showMessage(i18n.def["action_005"]);
        }

		if(rows){
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){
                    for (var i in rows) {
                        data.push($("#gridview").getRowData(rows[i]).oid);
                    }
					$.ajax({
						handler : _handler,
						type : "POST",
						dataType : "json",
						data :{
							'formAction' : 'deleteC126M01A',
							oids: data
						},
						success : function(obj) {
				        	$("#gridview").trigger("reloadGrid");
						}
					});
				}
			});
		}
    });
    
    // 篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        
        $.ajax({
    		type : "POST",
    		handler : "codetypehandler",
    		data : {
    			formAction : "allBranchByUnitType"
    		},
    		success : function(responseData) {
    			var json = {
    				format : "{value} - {key}",
    				item : responseData
    			};

    			$("#caseBrId").setItems(json);

    		}
    	});
        if(true){
        	//第一次開啟box
            //產生下拉選單
            var $div = $("#filterForm").find("[itemType]");
            var allKey = [];
            $div.each(function(){
                allKey.push($(this).attr("itemType"));
            });
            grid.codetypeItem = API.loadCombos(allKey);
            $div.each(function(){
                var $obj = $(this);
                var itemType = $obj.attr("itemType");
                if (itemType) {
                    var format = $obj.attr("itemFormat") || "{value} - {key}";
                    $obj.setItems({
                        space: $obj.attr("space") || true,
                        item: grid.codetypeItem[itemType],
                        format: format,
                        sort: $obj.attr("itemSort") || "asc",
                        size: $obj.attr("itemSize")
                    });
                }
            });
        }
        
        $("#filterBox").thickbox({
            // filter=請輸入欲查詢項目：
            title: i18n.cls3701m01["filter"],
            width: 450,
            height: 210,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#filterForm").valid()) {
                        return;
                    }
//                    grid();

                    grid.jqGrid("setGridParam", {
                        postData: $.extend({ docStatus: viewstatus}, $filterForm.serializeData() ),
                        search: true
                    }).trigger("reloadGrid");

                    $.thickbox.close();
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
        });
    }

    function chose_custId(){
        var my_dfd = $.Deferred();
        AddCustAction.open({
                handler: _handler,
                action : 'echo_custId',
                data : {
                },
                callback : function(json){
                    // 關掉 AddCustAction 的
                    $.thickbox.close();
                    my_dfd.resolve( json );
                }
            });
        return my_dfd.promise();
	}

	function build_submenu(dyna, rdoName, submenu){
    	$.each(submenu, function(k, v) {
    		dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>");
        });
    }
});
