/* 
 * MisEJF334ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanNamedJdbcTemplate;
import com.mega.eloan.common.service.EjcicLogService;
import com.mega.eloan.lms.ejcic.service.MisEJF334Service;

/**
 * <pre>
 * AAS103->EJV33401->EJF334法人戶戶名,身分證補發,通報,補充註記
 * </pre>
 * 
 * @since 2012/3/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/3/22,<PERSON>,new
 *          </ul>
 */
@Service
public class MisEJF334ServiceImpl extends AbstractEjcicJdbc implements
		MisEJF334Service {

	EloanNamedJdbcTemplate njdbc;
	
	@Resource
	EjcicLogService ejcicLogService;

	@Resource(name = "ejcic-db")
	public void setNJdbc(DataSource dataSource) {
		njdbc = new EloanNamedJdbcTemplate(dataSource, GWException.GWTYPE_EJCIC, ejcicLogService);
		njdbc.setSqlProvider(super.sqlp);
		njdbc.setCauseClass(this.getClass());
	}

	public EloanNamedJdbcTemplate getNamedJdbc() {
		return njdbc;
	}

	@Override
	public Map<String, Object> findCNameById(String custId) {
		return getJdbc()
				.queryForMap("AAS103.findById", new String[] { custId });
	}

	@Override
	public Map<String, Object> findPNameByBan(String readId) {
		return getJdbc().queryForMap("AAS103.findByBan",
				new String[] { readId });
	}

	public List<Map<String, Object>> findPNameByBans(List<String> readIds) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("BANs", readIds);
		return getNamedJdbc().queryForList("AAS103.findByBans", map);
	}

}
