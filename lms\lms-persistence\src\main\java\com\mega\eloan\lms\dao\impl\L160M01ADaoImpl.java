/* 
 * L160M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L160M01A;

/** 動用審核表主檔 **/
@Repository
public class L160M01ADaoImpl extends LMSJpaDao<L160M01A, String> implements
		L160M01ADao {

	@Override
	public L160M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L160M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L160M01A> findByDocStatus(String docStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		List<L160M01A> list = createQuery(L160M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L160M01A> findByMainIds(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L160M01A> list = createQuery(L160M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L160M01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L160M01A> list = createQuery(L160M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L160M01A> findByFitstUse(String[] docStatus, String ownBrId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "docStatus", docStatus);
		search.addSearchModeParameters(SearchMode.EQUALS, "l160a01a.authUnit",
				ownBrId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL,
				"l163s01a.waitingItem", "");
		search.addSearchModeParameters(SearchMode.IS_NULL,
				"l163s01a.finishDate", "");
		search.addSearchModeParameters(SearchMode.EQUALS, "useType", "Y");
		return createQuery(L160M01A.class, search).getResultList();
	}
}