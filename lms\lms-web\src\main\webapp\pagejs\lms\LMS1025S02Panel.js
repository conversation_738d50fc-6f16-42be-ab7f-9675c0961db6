var initDfd = initDfd || $.Deferred();
initDfd.done(function(json){
	var initControl_lockDoc = json['initControl_lockDoc'];

	var c120m01agrid = $("#c120m01agrid").iGrid({
        handler: 'lms1025gridhandler',        
        height: 250,
        rowNum: 15,
        postData: {
        	mainOid: json.mainOid,
            formAction: "queryC120M01A"
        },
        sortname: "keyMan|custPos|o_custRlt|custId",
        sortorder: 'desc|asc|asc|asc',
        rownumbers:true,
        needPager: false,        
        shrinkToFit: false,       
        colModel: [
         {colHeader : ' ', width : 15, name : 'keyMan', sortable : true, 'align':'center'}
        ,{colHeader : i18n.def["megaID"], width:90, name:'custId',formatter:'click', onclick : openDoc }
        ,{colHeader : ' ', width : 10, name : 'dupNo', sortable : true}
        ,{colHeader : i18n.def["compName"], width : 160, name : 'custName', sortable : true}
        ,{colHeader : i18n.lms1025m01["l120s01a.custrlt"], width : 140, name : 'o_custRlt', sortable : true}
        ,{colHeader : i18n.lms1025m01["l120s01a.custpos"], width : 140, name : 'custPos', sortable : true}
        ,{colHeader : ' ', width : 15, name : 'o_chkYN', sortable : true}
        ,{ name: 'oid', hidden: true }
        ,{ name: 'mainId', hidden: true }
        ,{ name: 'markModel', hidden: true }
        ],        
        ondblClickRow: function(rowid){
        	
        }        
    });		
	function queryC120BusCd(c120m01a_oid){
		return $.ajax({							
			type : "POST", handler : 'lms1015m01formhandler',
			data : { formAction : "queryC120BusCd", 'c120m01a_oid':c120m01a_oid
			},
			success:function(json_queryC120BusCd){
				
			}
		});
	}
	
 
	
	function openDoc(cellvalue, options, rowObject){
	    
		 openWindowDoc(true, rowObject.oid);
        
    };
    
    function openWindowDoc(lockDoc, oid, isNew){
        $.form.submit({
            url: '../lms1025open/01',
            data: {
                oid: oid,
                isNew: isNew,
                lockDoc:lockDoc
            },
            target: oid
        });
    }
	
 
	/**
	 * 參考 LMSS02APanel.html 裡的 onclick="openList('thickboxaddborrow','新增借款人'
	 */
	$("#btn_add_c120m01a").click(function(){
		var formId = "addborrowForm";
		var $addborrow = $("#"+formId);
		$addborrow.reset();
		$addborrow.find("input[name=rborrowA]:checked").trigger('click');
		/*
		
		 在 thickbox 的 div 內，包含 addborrowForm
		
		     addborrowForm 內的  btn 引進，參考 $("#getCustData").click(function(){
		
		*/		
		$("#thickboxaddborrow").thickbox({		
			title : '', width : 800, height : 380, modal : true, i18n:i18n.def,
			buttons : {				
				"close" : function(){
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
				     });
				}
			}
		});		
	});
	
	$("#btn_imp_basicData").click(function(){
		imp_queryFromBaseData();
	});
	
	$("#btn_delRatingDocCust").click(function(){		
    	var row = c120m01agrid.getGridParam('selrow');
    	if(row){
    		var data = c120m01agrid.getRowData(row);
    		ajax_delCust(data.oid);
			            		
    	}else{
    		API.showMessage(i18n.def.action_005);//請先選取一筆以上之資料列
    	}    	
	});
	$("#btn_setCustPosCustRlt").click(function(){		
    	var row = c120m01agrid.getGridParam('selrow');
    	if(row){
    		var data = c120m01agrid.getRowData(row);
    		queryC120BusCd(data.oid).done(function(json_queryC120BusCd){
    			if(json_queryC120BusCd.busCode){
    				chose_custPosCustRlt(json_queryC120BusCd.busCode
    									, data.custId+"-"+data.dupNo
    									, true).done(function(dfd_output){	            			
						$.ajax({							
							type : "POST", handler : _handler,
							data : $.extend(
									{ formAction : "setC120CustPosCustRlt"
										, mainOid : responseJSON.mainOid
										, 'c120m01a_oid': data.oid
									}
									,dfd_output||{}
							),
							success:function(responseData){
								c120m01agrid.trigger("reloadGrid");
								CommonAPI.triggerOpener("gridview", "reloadGrid");								
							}
						});
					});		
    			}
    		});
			            		
    	}else{
    		API.showMessage(i18n.def.grid_selector);//請選擇資料
    	}    	
	});
	
	function ajax_delCust(c120m01a_oid){
		API.confirmMessage(i18n.def['confirmDelete'], function(res){
			if(res){
				$.ajax({							
					type : "POST", handler : _handler,
					data : { formAction : "delRatingDocCust"
						, 'c120m01a_oid':c120m01a_oid
						, 'c121m01a_oid': responseJSON.mainOid													
					},
					success:function(){
						c120m01agrid.trigger("reloadGrid");
						$.thickbox.close();
					}
				});
			}
	    });
	}
	
	/*
	 * btn 引進 
	 * 		參考 $("#getCustData").click(function(){
	 */
	$("#getCustData").click(function(){
		var formId = "addborrowForm";
		var $addborrow = $("#"+formId);
		
		var $custId = $addborrow.find("[name=addborrowForm_custId]").val();
		var $custName = $addborrow.find("[name=addborrowForm_custName]").val();
		if(($custId != null && $custId != undefined && $custId != '')
		&& ($custName != null && $custName != undefined && $custName != '')){
			// 統一編號、名稱擇一輸入引進即可
			CommonAPI.showErrorMessage(i18n.lms1025m01["l120s02.alert26"]);
		}else if(($custId == null || $custId == undefined || $custId == '')
		&& ($custName == null || $custName == undefined || $custName == '')){
			// 請輸入統一編號或名稱
			CommonAPI.showErrorMessage(i18n.lms1025m01["l120s02.alert27"]);
		}else{
		    var defaultOption = {};
			if($custId != null && $custId != undefined && $custId != ''){
				defaultOption = {
					defaultValue: $custId //預設值 
				};
			}else{
				defaultOption = {
					defaultName : $custName
				};				
			}			
			//綁入MegaID
			CommonAPI.openQueryBox(
				$.extend({
					defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : ($custName != null && $custName != undefined && $custName != '') ? "2" : "",
	                divId: formId, //在哪個div 底下
	                isInSide:false, 
	                autoResponse: { // 是否自動回填資訊 
	                       id: "addborrowForm_custId", // 統一編號欄位ID 
	                       dupno: "addborrowForm_dupNo", // 重覆編號欄位ID 
	                       name: "addborrowForm_custName" // 客戶名稱欄位ID 
	                },fn:function(obj){	
	                	/*
						obj.custid "A123456789"
						obj.dupno "0"
						obj.name "TESTT"
						obj.buscd 	"060000"
	                	*/
						if( $addborrow.valid()){
							chose_custPosCustRlt(obj.buscd
												, obj.custid+"-"+obj.dupno
												, false).done(function(dfd_output){
								$.ajax({							
									type : "POST", handler : _handler,
									data : $.extend(
											{ formAction : "addRatingDocCust", mainOid : responseJSON.mainOid,
												custId: obj.custid, dupNo: obj.dupno, custName: obj.name
											}
											,dfd_output||{}
									),
									success:function(responseData){
										c120m01agrid.trigger("reloadGrid");
										CommonAPI.triggerOpener("gridview", "reloadGrid");
										$.thickbox.close();
										
										call_z_grid_openDoc(responseData.c120m01a_oid, true, true);
									}
								});
							});							
						}
					}
				},defaultOption)
			);			
		}
	});	
	/*
	 引入時，可用 custId 來篩選
	 */
	$("#findBaseDataIdBt").click(function(){	
		var search_custId = $("#findBaseDataId").val();
		var grid_id = "grid_fromBaseData";
		
		var my_post_data = {
			formAction : "queryFromBaseData"
			, search_custId: search_custId
		};
		
		
		$("#"+grid_id).jqGrid("setGridParam", {
			postData : my_post_data,
			search : true
		}).trigger("reloadGrid");			
	});
	
	function imp_queryFromBaseData(){		
		var grid_id = "grid_fromBaseData";
			
		var my_post_data = {
			formAction : "queryFromBaseData"
		};
		
		if($("#"+grid_id+".ui-jqgrid-btable").length >0){
			$("#"+grid_id).jqGrid("setGridParam", {
				postData : my_post_data,
				search : true
			}).trigger("reloadGrid");	        		
		}else{
			$("#"+grid_id).iGrid({
				handler : 'lms1015gridhandler',
				height : 280,
				postData : my_post_data,		
				//因為要指定 相關身分，所以先用單選  multiselect: false, 
				sortname: 'o_chkYN|updateTime', 
		        sortorder: 'desc|desc',
				colModel : [{ name : 'oid', hidden : true}
				,{name : 'mainId',hidden : true}
				,{colHeader : i18n.def["megaID"], width:90, name:'custId' }
				,{colHeader : ' ', width : 10, name : 'dupNo', sortable : true}
				,{colHeader : i18n.def["compName"], width : 140, name : 'custName', sortable : true}		
				,{
					colHeader : i18n.lms1025v01["C101S01G.jcicQDate"], //聯徵查詢日期
					align : "center", width : 60, sortable : false,
					name : 'eJcicQDate' //借欄位 modelTyp as eJcicQDate
				},{
					colHeader : i18n.lms1025v01["C101S01G.etchQDate"], //票信查詢日期
					align : "center", width : 60, sortable : false,
					name : 'eChkQDate' //借欄位 jcicFlg as eChkQDate
				}		
				,{colHeader : i18n.def["lastUpdater"], width : 110, name : 'updater', sortable : true}
				,{colHeader : i18n.def["lastUpdateTime"], width : 100, name : 'updateTime', sortable : true}
				,{colHeader : ' ', width : 10, name : 'o_chkYN', sortable : true}
				]
			});
		}    
	            
		$("#thickBoxFromBaseData").thickbox({
	        title: i18n.abstractoverseacls["l120s02.thickbox10"],
	        width: 940, height: 440, align: 'center', valign: 'bottom', modal: true,
	        buttons: {
	            "sure": function(){            	
	            	var $gridview = $("#"+grid_id);
	            	var row = $gridview.getGridParam('selrow');
	            	if(row){
	            		var data = $gridview.getRowData(row);
	            		$.thickbox.close();
	            		
	            		queryC120BusCd(data.oid).done(function(json_queryC120BusCd){
	            			if(json_queryC120BusCd.busCode){
	            				chose_custPosCustRlt(json_queryC120BusCd.busCode
	            									, data.custId+"-"+data.dupNo
	            									, false).done(function(dfd_output){	            			
	    							$.ajax({							
	    								type : "POST", handler : _handler,
	    								data : $.extend(
	    										{ formAction : "impRatingDocCust", mainOid : responseJSON.mainOid
	    											, 'c120m01a_oid': data.oid
	    										}
	    										,dfd_output||{}
	    								),
	    								success:function(responseData){
	    									c120m01agrid.trigger("reloadGrid");
	    									CommonAPI.triggerOpener("gridview", "reloadGrid");
	    									$.thickbox.close();
	    									
	    									//call_z_grid_openDoc(responseData.c120m01a_oid, false, false);
	    								}
	    							});
	    						});		
	            			}
	            		});
	            		            		
	            	}else{
	            		API.showMessage(i18n.def.action_005);//請先選取一筆以上之資料列
	            	}
	            },
	            "cancel": function(){
	               $.thickbox.close();
	            }
	        }
	    });
	}
	
	if(true){
		$("#chooseC120M01ACustPosForm [name=chooseC120M01ACustPos]").click(function(){
	        var custPos = $(this).val();        
	        if(custPos=="M"){
	        	$("#chooseC120M01ACustPosForm #div_choose_o_custRlt").hide();
	        }else{
	        	$("#chooseC120M01ACustPosForm #div_choose_o_custRlt").show();
	        }
	    });
		
		$("#chooseC120M01ACustPosForm #lms1015m01_custRlt_main").change(function(){
			$("span.lms1015m01_custRlt_mainV").hide();
			
	        switch ($(this).val()) {        
	            case "1":
	                //請選擇企業關係人(含法人、自然人)
	                $("span#lms1015m01_custRlt_main1").show();
	                break;
	            case "2":
	                //請選擇親屬關係人
	            	$("span#lms1015m01_custRlt_main2").show();
	                break;
	                
	            case "3":
	                //請選擇其他綜合關係
	            	$("span#lms1015m01_custRlt_main3").show();
	                break;
	            default:
	                break;
	        }
	    });
		
	}
});


function chose_custPosCustRlt(busCode, title, showCancelBtn){
	var my_dfd = $.Deferred();
	var $custPosForm = $("#chooseC120M01ACustPosForm"); 
	
	if(busCode=="060000" || busCode=="130300"){
		$custPosForm.find("[name=chooseC120M01ACustPos][value=M]").closest("tr").show();
	}else{
		$custPosForm.find("[name=chooseC120M01ACustPos][value=M]").closest("tr").hide();
	}
	//澳洲評等表 G 不列入評等
	$custPosForm.find("[name=chooseC120M01ACustPos][value=G]").closest("tr").hide();
	$custPosForm.find("[name=chooseC120M01ACustPos][value=N]").closest("tr").hide();
	$custPosForm.find("[name=chooseC120M01ACustPos][value=S]").closest("tr").hide();

	if(true){
		$("#lms1015m01_custRlt_main").val("");
        $("span.lms1015m01_custRlt_mainV").hide();
        $("select.lms1015m01_custRlt_sel").val("");
	}
	
	var tb_btnArr = {};
	if(true){
		tb_btnArr["sure"] = function(){
			if($custPosForm.valid()){
				
				var o_custRlt = ""; 
				var custPos = $custPosForm.find("[name=chooseC120M01ACustPos]:checked").val();
				if(custPos=="M"){
					o_custRlt = "";
				}else{
					var context;
                    var rKindM = $('#lms1015m01_custRlt_main').find(":selected").val();
                    var rKindD;
                    if(rKindM=="1"){
                    	var $elm = $("select.lms1015m01_custRlt_sel[name=lms1015m01_rationSelect1]").find(":selected");
                        if ($elm.val() == "") {
                            //grid.selrow=請先選擇一筆資料。
                            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                        }
                        context = $elm.text();
                        rKindD = $elm.val();
                    }else if(rKindM=="2"){
                    	var $elm = $("select.lms1015m01_custRlt_sel[name=lms1015m01_rationSelect2]").find(":selected");
                    	if ($elm.val() == "") {
                            //grid.selrow=請先選擇一筆資料。
                            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                        }
                        context = $elm.text();
                        rKindD = $elm.val();
                    }else if(rKindM=="3"){
                    	var $elmA = $("select.lms1015m01_custRlt_sel[name=lms1015m01_rationSelect31]").find(":selected");
                    	var $elmB = $("select.lms1015m01_custRlt_sel[name=lms1015m01_rationSelect32]").find(":selected");
                    	if ($elmA.val() == "" || $elmB.val() == "") {
                            //grid.selrow=請先選擇一筆資料。
                            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                        }
                        context = $elmA.text() + '-' + $elmB.text();
                        rKindD = $elmA.val() + $elmB.val();
                    }else{
                    	return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                    }  
                    //====
                    o_custRlt = rKindD;
				}
				$.thickbox.close();
				
				
				//---------
				my_dfd.resolve(
					{
						'custPos':custPos
						, 'o_custRlt':o_custRlt  
					}
				);
			}
		};	
		
		if(showCancelBtn){
			tb_btnArr["cancel"] = function(){
				$.thickbox.close();
				my_dfd.reject();
			};	
		}
	}
	
	//英文 width 要650
	$("#div_chooseC120M01ACustPos").thickbox({		
		title : title, width : 650, height : 320, modal : true, i18n:i18n.def,
		buttons : tb_btnArr
	});	
	return my_dfd.promise();	
}
