package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.service.LMS7600Service;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.model.L140MM4A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 空地貸款維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms7600gridhandler")
public class LMS7600GridHandler extends AbstractGridHandler {

	@Resource
	LMS7600Service lms7600Service;
	
	@Resource
	MisELF600Service misELF600Service;
	
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140mm4a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, 
				UtilConstants.Field.目前編製行, user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms7600Service.findPage(
				L140MM4A.class, pageSetting);

		List<L140MM4A> l140mm4alist = (List<L140MM4A>) page.getContent();

		return new CapGridResult(l140mm4alist, page.getTotalRow());

	}
	
	/**
	 * 查詢額度序號
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */	
	public CapMapGridResult queryGetCntrno(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<String> cntrnoList = new ArrayList<String>();
		
		if(!custId.isEmpty() && !dupNo.isEmpty()){
			
			//查詢ELF600 空地貸款
			List<Map<String, Object>> elf600List = misELF600Service.selCntrnoByCustidDupno(custId, dupNo);
			for (Map<String, Object> elf600 : elf600List) {
				Map<String, Object> row = new HashMap<String, Object>();
				
				String cntrNo = Util.trim(elf600.get("ELF600_CONTRACT"));
				if(!cntrNo.isEmpty()){
					if(cntrnoList != null && cntrnoList.contains(cntrNo)){
						//排除重複
					} else {
						cntrnoList.add(cntrNo);	
						row.put("cntrNo", cntrNo);
						list.add(row);
					}
				}
			}
			
			//排序
			Collections.sort(list, new Comparator<Map<String, Object>>() {
				public int compare(Map<String, Object> o1, Map<String, Object> o2) {
					String name1 = o1.get("cntrNo").toString(); 
					String name2 = o2.get("cntrNo").toString();
	                return name1.compareTo(name2);
				}
			});
		}
		
		return new CapMapGridResult(list, list.size());
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public CapGridResult filterL140mm4aByEditCompletedCase(ISearch pageSetting, PageParameters params) throws CapException {
		
    	MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String cntrNo = Util.trim(params.getString("cntrNo"));
		String custId = Util.trim(params.getString("custId"));
		
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus", docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, UtilConstants.Field.目前編製行, user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, UtilConstants.Field.刪除時間, "");
		
		if(StringUtils.isNotBlank(cntrNo)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		}
		
		if(StringUtils.isNotBlank(custId)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, UtilConstants.Field.CUSTID, custId);
		}
		
		Page<? extends GenericBean> page = lms7600Service.findPage(L140MM4A.class, pageSetting);

		@SuppressWarnings("unchecked")
		List<L140MM4A> l140mm4alist = (List<L140MM4A>) page.getContent();

		return new CapGridResult(l140mm4alist, page.getTotalRow());
	}
}