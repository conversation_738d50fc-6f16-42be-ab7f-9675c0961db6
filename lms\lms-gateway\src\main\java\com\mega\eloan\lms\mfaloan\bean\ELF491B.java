package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 防杜代辦消金覆審控制檔 **/
public class ELF491B extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 文件編號UNID */
	@Column(name = "ELF491B_UNID", length = 32, columnDefinition = "CHAR(32)", nullable=false,unique = true)
	private String elf491b_unid;
	
	/** 分行代號 */
	@Column(name = "ELF491B_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable=false)
	private String elf491b_branch;
	
	/** 借款人統一編號 */
	@Column(name = "ELF491B_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable=false)
	private String elf491b_custid;
	
	/** 重複序號 */
	@Column(name = "ELF491B_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable=false)
	private String elf491b_dupno;
		
	/** 覆審日期*/
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491B_LRDATE", columnDefinition = "DATE")
	private Date elf491b_lrdate;
	
	/** 防杜代辦覆審_基準期間 */
	@Column(name = "ELF491B_PA_YM", length = 7, columnDefinition = "CHAR(7)", nullable=false)
	private String elf491b_pa_ym;
	
	/** 防杜代辦覆審_對象類別 */
	@Column(name = "ELF491B_PA_TRG", length = 2, columnDefinition = "CHAR(2)", nullable=false)
	private String elf491b_pa_trg;

	/** 查核結果{1:正常, 2:異常} */
	@Column(name = "ELF491B_CONFLAG", length = 1, columnDefinition = "CHAR(1)", nullable=false)
	private String elf491b_conflag;
	
	/** 資料修改人  */
	@Column(name = "ELF491B_UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String elf491b_updater;
	
	/**  資料更新日 */
	@Column(name = "ELF491B_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf491b_tmestamp;

	public String getElf491b_unid() {
		return elf491b_unid;
	}

	public void setElf491b_unid(String elf491b_unid) {
		this.elf491b_unid = elf491b_unid;
	}

	public String getElf491b_branch() {
		return elf491b_branch;
	}

	public void setElf491b_branch(String elf491b_branch) {
		this.elf491b_branch = elf491b_branch;
	}

	public String getElf491b_custid() {
		return elf491b_custid;
	}

	public void setElf491b_custid(String elf491b_custid) {
		this.elf491b_custid = elf491b_custid;
	}

	public String getElf491b_dupno() {
		return elf491b_dupno;
	}

	public void setElf491b_dupno(String elf491b_dupno) {
		this.elf491b_dupno = elf491b_dupno;
	}

	public Date getElf491b_lrdate() {
		return elf491b_lrdate;
	}

	public void setElf491b_lrdate(Date elf491b_lrdate) {
		this.elf491b_lrdate = elf491b_lrdate;
	}

	public String getElf491b_pa_ym() {
		return elf491b_pa_ym;
	}

	public void setElf491b_pa_ym(String elf491b_pa_ym) {
		this.elf491b_pa_ym = elf491b_pa_ym;
	}

	public String getElf491b_pa_trg() {
		return elf491b_pa_trg;
	}

	public void setElf491b_pa_trg(String elf491b_pa_trg) {
		this.elf491b_pa_trg = elf491b_pa_trg;
	}

	public String getElf491b_conflag() {
		return elf491b_conflag;
	}

	public void setElf491b_conflag(String elf491b_conflag) {
		this.elf491b_conflag = elf491b_conflag;
	}

	public String getElf491b_updater() {
		return elf491b_updater;
	}

	public void setElf491b_updater(String elf491b_updater) {
		this.elf491b_updater = elf491b_updater;
	}

	public Timestamp getElf491b_tmestamp() {
		return elf491b_tmestamp;
	}

	public void setElf491b_tmestamp(Timestamp elf491b_tmestamp) {
		this.elf491b_tmestamp = elf491b_tmestamp;
	}

}
