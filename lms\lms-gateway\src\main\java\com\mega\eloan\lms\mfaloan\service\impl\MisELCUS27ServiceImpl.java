package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELCUS27Service;

@Service
public class MisELCUS27ServiceImpl extends AbstractMFAloanJdbc implements
		MisELCUS27Service {

	@Override
	public List<Map<String, Object>> getCustPhone(String custId, String dupNo) {
		return getJdbc().queryForList("ELCUS27.getCustPhone",
				new String[] { custId, dupNo });
	}

}
