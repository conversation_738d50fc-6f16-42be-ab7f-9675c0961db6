---------------------------------------------------------
-- LMS.C900S01A 查核事項檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C900S01A;
CREATE TABLE LMS.C900S01A (
	OID           CHAR(32)      not null,
	CHECKCODE     VARCHAR(10)   not null,
	CHECKSEQ      DECIMAL(5,0) ,
	CHECKTY<PERSON><PERSON>     VARCHAR(2)   ,
	CHECKCONTENT  VARCHAR(1024),
	PRODTY<PERSON><PERSON>      VARCHAR(256) ,
	CHECKRMK      VARCHAR(1024),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C900S01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900S01A01;
CREATE UNIQUE INDEX LMS.XC900S01A01 ON LMS.C900S01A   (CHECKCODE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900S01A IS '查核事項檔';
COMMENT ON LMS.C900S01A (
	OID           IS 'oid', 
	CHECKCODE     IS '查核事項編號', 
	CHECKSEQ      IS '顯示順序', 
	CHECKTYPE     IS '查核事項項目', 
	CHECKCONTENT  IS '查核內容', 
	PRODTYPE      IS '適用產品', 
	CHECKRMK      IS '備註', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
