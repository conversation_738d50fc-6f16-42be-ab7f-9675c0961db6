/* 
 * L810M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 優惠貸款相關控制表 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L810M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L810M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 分行別 **/
	@Size(max=3)
	@Column(name="BRNO", length=3, columnDefinition="CHAR(3)")
	private String brno;

	/** 
	 * 統計方式<p/>
	 * 1.各類優惠房貸統計表(BY分行)<br/>
	 *  2.各類優惠房貸統計表(BY縣市)<br/>
	 *  3.各類優惠房貸統計數<br/>
	 *  4.各類優惠房貸明細表<br/>
	 *  5.週報表<br/>
	 *  6.月報表<br/>
	 *  7.總表彙報
	 */
	@Size(max=1)
	@Column(name="USETYPE", length=1, columnDefinition="CHAR(1)")
	private String useType;

	/** 
	 * 報表類型<p/>
	 * 1. 八仟億優惠房貸額度控制表、<br/>
	 *  2. 三千億優惠房貸額度控制表、<br/>
	 *  3. 九十七年度二千億優惠房貸額度控制表、<br/>
	 *  4. 青年安心成家優惠貸款-購置住宅、<br/>
	 *  5. 全部優惠房貸<br/>
	 *  6. 100億房貸專案週報表
	 */
	@Size(max=1)
	@Column(name="RPTTYPE", length=1, columnDefinition="CHAR(1)")
	private String rptType;

	/** 報表名稱 **/
	@Size(max=120)
	@Column(name="RPTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String rptName;

	/** 
	 * 傳送至授管處<p/>
	 * 100億房貸專案週報表是否發送過mail
	 */
	@Size(max=1)
	@Column(name="SENDED", length=1, columnDefinition="CHAR(1)")
	private String sended;

	/** 開始日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="BGNDATE", columnDefinition="DATE")
	private Date bgnDate;

	/** 截止日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 
	 * 報表oid<p/>
	 * 報表檔的BDOCFILE.oid
	 */
	@Size(max=32)
	@Column(name="RPTOID", length=32, columnDefinition="CHAR(32)")
	private String rptOid;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得分行別 **/
	public String getBrno() {
		return this.brno;
	}
	/** 設定分行別 **/
	public void setBrno(String value) {
		this.brno = value;
	}

	/** 
	 * 取得統計方式<p/>
	 * 1.各類優惠房貸統計表(BY分行)<br/>
	 *  2.各類優惠房貸統計表(BY縣市)<br/>
	 *  3.各類優惠房貸統計數<br/>
	 *  4.各類優惠房貸明細表<br/>
	 *  5.週報表<br/>
	 *  6.月報表<br/>
	 *  7.總表彙報
	 */
	public String getUseType() {
		return this.useType;
	}
	/**
	 *  設定統計方式<p/>
	 *  1.各類優惠房貸統計表(BY分行)<br/>
	 *  2.各類優惠房貸統計表(BY縣市)<br/>
	 *  3.各類優惠房貸統計數<br/>
	 *  4.各類優惠房貸明細表<br/>
	 *  5.週報表<br/>
	 *  6.月報表<br/>
	 *  7.總表彙報
	 **/
	public void setUseType(String value) {
		this.useType = value;
	}

	/** 
	 * 取得報表類型<p/>
	 * 1. 八仟億優惠房貸額度控制表、<br/>
	 *  2. 三千億優惠房貸額度控制表、<br/>
	 *  3. 九十七年度二千億優惠房貸額度控制表、<br/>
	 *  4. 青年安心成家優惠貸款-購置住宅、<br/>
	 *  5. 全部優惠房貸<br/>
	 *  6. 100億房貸專案週報表
	 */
	public String getRptType() {
		return this.rptType;
	}
	/**
	 *  設定報表類型<p/>
	 *  1. 八仟億優惠房貸額度控制表、<br/>
	 *  2. 三千億優惠房貸額度控制表、<br/>
	 *  3. 九十七年度二千億優惠房貸額度控制表、<br/>
	 *  4. 青年安心成家優惠貸款-購置住宅、<br/>
	 *  5. 全部優惠房貸<br/>
	 *  6. 100億房貸專案週報表
	 **/
	public void setRptType(String value) {
		this.rptType = value;
	}

	/** 取得報表名稱 **/
	public String getRptName() {
		return this.rptName;
	}
	/** 設定報表名稱 **/
	public void setRptName(String value) {
		this.rptName = value;
	}

	/** 
	 * 取得傳送至授管處<p/>
	 * 100億房貸專案週報表是否發送過mail
	 */
	public String getSended() {
		return this.sended;
	}
	/**
	 *  設定傳送至授管處<p/>
	 *  100億房貸專案週報表是否發送過mail
	 **/
	public void setSended(String value) {
		this.sended = value;
	}

	/** 取得開始日期 **/
	public Date getBgnDate() {
		return this.bgnDate;
	}
	/** 設定開始日期 **/
	public void setBgnDate(Date value) {
		this.bgnDate = value;
	}

	/** 取得截止日期 **/
	public Date getEndDate() {
		return this.endDate;
	}
	/** 設定截止日期 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 
	 * 取得報表oid<p/>
	 * 報表檔的BDOCFILE.oid
	 */
	public String getRptOid() {
		return this.rptOid;
	}
	/**
	 *  設定報表oid<p/>
	 *  報表檔的BDOCFILE.oid
	 **/
	public void setRptOid(String value) {
		this.rptOid = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	@Override
	public String getMainId() {
		return null;
	}
}
