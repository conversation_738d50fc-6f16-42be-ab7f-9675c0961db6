/* 
 * LMS1601V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;


/**
 * <pre>
 * 購置房屋擔保放款風險權數檢核表 - 已核准
 * </pre>
 * 
 * @since 2013/01/03
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/03,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1021v03")
public class CLS1021V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		
		// 加上Button
		// 主管跟經辦都會出現的按鈕
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.View);

		renderJsI18N(CLS1021M01Page.class);
		renderJsI18N(CLS1021V03Page.class);
		
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1021V03Page')");
	}

}
