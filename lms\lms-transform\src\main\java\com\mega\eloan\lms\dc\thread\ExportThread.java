package com.mega.eloan.lms.dc.thread;

import com.mega.eloan.lms.dc.action.DXLExport;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;

/**
 * <pre>
 * ExportThread
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          </ul>
 */
public class ExportThread extends Thread {

	private String viewname = "";
	private String schema = "";
	private ConfigData config=null;

	public ExportThread() {
		super();
	}

	/**
	 * Constructor
	 * 
	 * @param schema
	 *            String:目前執行的系統名稱
	 * @param vn
	 *            String :目前讀取的viewListName
	 * @param pps
	 *            :Properties 2013-01-28 Modify By Bang:加入個金判斷 (schema)
	 */
	public ExportThread(String schema, String vn) {
		this.schema = schema;
		this.viewname = vn;
	}

	public void run() {
		DXLExport de = new DXLExport();
		de.setConfigData(config);
		de.doExport(this.schema, this.viewname);
	}

	public void export(ConfigData config) {
		try {
			this.setConfig(config);
			this.run();
		} catch (Exception e) {
			throw new DCException("Export 時產生錯誤...", e);
		}
	}

	/**
	 * set the config
	 * 
	 * @param config
	 *            the config to set
	 */
	public void setConfig(ConfigData config) {
		if (config != null) {
			this.config = config;
		}
	}

}
