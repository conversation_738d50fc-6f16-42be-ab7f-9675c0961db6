/* 
 *ObsdbELFAPPNOServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELFAPPNOService;

/**
 * <pre>
 * 配合泰行所需APP NO，AS400這邊會上傳ELFAPPNO
 * </pre>
 */
@Service
public class ObsdbELFAPPNOServiceImpl extends AbstractOBSDBJdbcFactory implements
	ObsdbELFAPPNOService {

	@Override
	public List<Map<String, Object>> listAppNoDataByCustIdDupNoBranch(
			String branchId, String custId, String dupNo) {
		Object[] obj = {branchId, custId, dupNo};
		return this.getJdbc(branchId).queryForList("ELFAPPNO.listAppNoData", obj);
	}

}