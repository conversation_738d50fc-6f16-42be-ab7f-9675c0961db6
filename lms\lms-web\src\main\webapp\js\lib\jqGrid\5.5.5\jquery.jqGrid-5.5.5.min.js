/* 
* jqGrid  5.5.5-Trial  
* Copyright (c) 2008, <PERSON>, <EMAIL> 
*  License: http://guriddo.net/?page_id=103334 
*
* Modules: grid.base.js; jquery.fmatter.js; grid.common.js; grid.formedit.js; grid.filter.js; grid.inlinedit.js; grid.celledit.js; jqModal.js; jqDnR.js; grid.subgrid.js; grid.grouping.js; grid.treegrid.js; grid.pivot.js; grid.import.js; grid.export.js; grid.utils.js; grid.aria.js; grid.jqueryui.js; jquery.sortable.js;
*/
!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery"],e):e(jQuery)}(function($){"use strict";$.jgrid=$.jgrid||{},$.jgrid.hasOwnProperty("defaults")||($.jgrid.defaults={}),$.extend($.jgrid,{version:"5.5.5",trim:function(e){return"string"==typeof
e?e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""):e},isFunction:function(e){return"function"==typeof
e},type:function(e,t){if(t)return null===e?"[object Null]":Object.prototype.toString.call(e);if(null==e)return(e+"").toLowerCase();t=Object.prototype.toString.call(e).slice(8,-1).toLowerCase();return"generatorfunction"===t?"function":t.match(/^(array|bigint|date|error|function|generator|regexp|symbol)$/)?t:"object"==typeof
e||"function"==typeof
e?"object":typeof
e},floatNum:function(e,t){return void
0===t&&(t=0),e=parseFloat(e),isNaN(e)?t:e},htmlDecode:function(e){return e&&("&nbsp;"===e||"&#160;"===e||1===e.length&&160===e.charCodeAt(0))?"":e&&String(e).replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/&quot;/g,'"').replace(/&amp;/g,"&")},htmlEncode:function(e){return e&&String(e).replace(/&/g,"&amp;").replace(/\"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},template:function(e){var
o,n=$.makeArray(arguments).slice(1),a=n.length;return null==e&&(e=""),e.replace(/\{([\w\-]+)(?:\:([\w\.]*)(?:\((.*?)?\))?)?\}/g,function(e,t){if(!isNaN(parseInt(t,10)))return n[parseInt(t,10)];for(o=0;o<a;o++)if(Array.isArray(n[o]))for(var
i=n[o],r=i.length;r--;)if(t===i[r].nm)return i[r].v})},msie:function(){return 0<$.jgrid.msiever()},msiever:function(){var
e=0,t=window.navigator.userAgent,i=t.indexOf("MSIE");return 0<i?e=parseInt(t.substring(i+5,t.indexOf(".",i))):navigator.userAgent.match(/Trident\/7\./)&&(e=11),e},getCellIndex:function(e){e=$(e);return e.is("tr")?-1:(e=(e.is("td")||e.is("th")?e:e.closest("td,th"))[0],$.jgrid.msie()?$.inArray(e,e.parentNode.cells):e.cellIndex)},stripHtml:function(e){return(e=String(e))?(e=e.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi,""))&&"&nbsp;"!==e&&"&#160;"!==e?e.replace(/\"/g,"'"):"":e},stripPref:function(e,t){var
i=$.jgrid.type(e);return"string"!==i&&"number"!==i||(t=""!==(e=String(e))?String(t).replace(String(e),""):t),t},useJSON:!0,parse:function(jsonString){var
js=jsonString;return"while(1);"===js.substr(0,9)&&(js=js.substr(9)),"/*"===js.substr(0,2)&&(js=js.substr(2,js.length-4)),js=js||"{}",!0===$.jgrid.useJSON&&"object"==typeof
JSON&&"function"==typeof
JSON.parse?JSON.parse(js):eval("("+js+")")},dateToOADate:function(e){var
t=new
Date(e);return Math.round((t.setHours(0,0,0,0)-new
Date(1899,11,30))/864e5)+(Math.abs((e-t)%864e5)/864e5).toFixed(10).substr(1)},parseDate:function(e,t,i,r){function
o(e,t){for(e=String(e),t=parseInt(t,10)||2;e.length<t;)e="0"+e;return e}function
n(e,t){return 0===e?12===t&&(t=0):12!==t&&(t+=12),t}var
a,s,l,d=new
RegExp("^/Date\\((([-+])?[0-9]+)(([-+])([0-9]{2})([0-9]{2}))?\\)/$"),p="string"==typeof
t?t.match(d):null,c={m:1,d:1,y:1970,h:0,i:0,s:0,u:0},u=0,g=0;if(void
0===r&&(r=$.jgrid.getRegional(this,"formatter.date")),void
0===r&&(r={}),void
0===r.parseRe&&(r.parseRe=/[#%\\\/:_;.,\t\s-]/),void
0===r.AmPm&&(r.AmPm=["am","pm","AM","PM"]),r.masks&&r.masks.hasOwnProperty(e)&&(e=r.masks[e]),t&&null!=t)if(isNaN(+t)||"u"!==String(e).toLowerCase())if(t.constructor===Date)u=t,r.validate=!1;else
if(null!==p)u=new
Date(parseInt(p[1],10)),p[3]&&(g=60*Number(p[5])+Number(p[6]),g*="-"===p[4]?1:-1,g-=u.getTimezoneOffset(),u.setTime(Number(Number(u)+60*g*1e3))),r.validate=!1;else{for("ISO8601Long"===r.srcformat&&"Z"===t.charAt(t.length-1)&&(g-=(new
Date).getTimezoneOffset()),t=String(t).replace(/\T/g,"#").replace(/\t/,"%").split(r.parseRe),s=0,l=(e=e.replace(/\T/g,"#").replace(/\t/,"%").split(r.parseRe)).length;s<l;s++){switch(e[s]){case"M":-1!==(a=$.inArray(t[s],r.monthNames))&&a<12&&(t[s]=a+1,c.m=t[s]);break;case"F":-1!==(a=$.inArray(t[s],r.monthNames,12))&&11<a&&(t[s]=a+1-12,c.m=t[s]);break;case"n":e[s]="m";break;case"j":e[s]="d";break;case"a":-1!==(a=$.inArray(t[s],r.AmPm))&&a<2&&t[s]===r.AmPm[a]&&(t[s]=a,c.h=n(t[s],c.h));break;case"A":-1!==(a=$.inArray(t[s],r.AmPm))&&1<a&&t[s]===r.AmPm[a]&&(t[s]=a-2,c.h=n(t[s],c.h));break;case"g":c.h=parseInt(t[s],10)}void
0!==t[s]&&(c[e[s].toLowerCase()]=parseInt(t[s],10))}if(c.f&&(c.m=c.f),0===c.m&&0===c.y&&0===c.d)return"&#160;";c.m=parseInt(c.m,10)-1;var
h=c.y;70<=h&&h<=99?c.y=1900+c.y:0<=h&&h<=69&&(c.y=2e3+c.y),u=new
Date(c.y,c.m,c.d,c.h,c.i,c.s,c.u),0!==g&&u.setTime(Number(Number(u)+60*g*1e3))}else
u=new
Date(1e3*parseFloat(t)),r.validate=!1;else
u=new
Date(c.y,c.m,c.d,c.h,c.i,c.s,c.u);if(r&&!0===r.validate){const
C=new
Date(c.y,+c.m,c.d,c.h,c.i);return Boolean(+C)&&C.getDate()===c.d&&C.getHours()===c.h&&C.getMinutes()===c.i}if(r.userLocalTime&&0===g&&0!==(g-=(new
Date).getTimezoneOffset())&&u.setTime(Number(Number(u)+60*g*1e3)),void
0===i)return u;i=r.masks&&r.masks.hasOwnProperty(i)?r.masks[i]:i||"Y-m-d";var
f,m,v,w=u.getHours(),j=u.getMinutes(),b=u.getDate(),_=u.getMonth()+1,y=u.getTimezoneOffset(),D=u.getSeconds(),x=u.getMilliseconds(),d=u.getDay(),p=u.getFullYear(),h=(d+6)%7+1,g=(new
Date(p,_-1,b)-new
Date(p,0,1))/864e5,q={d:o(b),D:r.dayNames[d],j:b,l:r.dayNames[d+7],N:h,S:r.S(b),w:d,z:g,W:h<5?Math.floor((g+h-1)/7)+1:Math.floor((g+h-1)/7)||((new
Date(p-1,0,1).getDay()+6)%7<4?53:52),F:r.monthNames[_-1+12],m:o(_),M:r.monthNames[_-1],n:_,t:"?",L:"?",o:"?",Y:p,y:String(p).substring(2),a:w<12?r.AmPm[0]:r.AmPm[1],A:w<12?r.AmPm[2]:r.AmPm[3],B:"?",g:w%12||12,G:w,h:o(w%12||12),H:o(w),i:o(j),s:o(D),u:x,e:"?",I:"?",O:(0<y?"-":"+")+o(100*Math.floor(Math.abs(y)/60)+Math.abs(y)%60,4),P:(0<y?"-":"+")+(f=o(100*Math.floor(Math.abs(y)/60)+Math.abs(y)%60,4),m=-2,v=":",(f=String(f)).slice(0,m)+v+f.slice(m)),T:(String(u).match(/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g)||[""]).pop().replace(/[^-+\dA-Z]/g,""),Z:"?",c:"?",r:"?",U:Math.floor(u/1e3)};return i.replace(/\\.|[dDjlNSwzWFmMntLoYyaABgGhHisueIOPTZcrU]/g,function(e){return q.hasOwnProperty(e)?q[e]:e.substring(1)})},jqID:function(e){return String(e).replace(/[!"#$%&'()*+,.\/:; <=>?@\[\\\]\^`{|}~]/g,"\\$&")},guid:1,uidPref:"jqg",randId:function(e){return(e||$.jgrid.uidPref)+$.jgrid.guid++},getAccessor:function(e,t){var
i,r,o=[];if("function"==typeof
t)return t(e);if(void
0===(i=e[t]))try{if("string"==typeof
t&&(o=t.split(".")),r=o.length)for(i=e;i&&r--;)i=i[o.shift()]}catch(e){}return i},getXmlData:function(e,t,i){var
r="string"==typeof
t?t.match(/^(.*)\[(\w+)\]$/):null;return"function"==typeof
t?t(e):r&&r[2]?(r[1]?$(r[1],e):$(e)).attr(r[2]):(e=$(t,e),i?e:0<e.length?$(e).text():void
0)},cellWidth:function(){var
e=$("<div class='ui-jqgrid' style='left:10000px'><table class='ui-jqgrid-btable ui-common-table' style='width:5px;'><tr class='jqgrow'><td style='width:5px;display:block;'></td></tr></table></div>"),t=e.appendTo("body").find("td").width();return e.remove(),.1<Math.abs(t-5)},isLocalStorage:function(){try{return"localStorage"in
window&&null!==window.localStorage}catch(e){return!1}},getRegional:function(e,t,i){var
r;return void
0!==i?i:(e.p&&e.p.regional&&$.jgrid.regional&&(r=$.jgrid.getAccessor($.jgrid.regional[e.p.regional]||{},t)),void
0===r&&(r=$.jgrid.getAccessor($.jgrid,t)),r)},isMobile:function(){try{return/Android|webOS|iPhone|iPad|iPod|pocket|psp|kindle|avantgo|blazer|midori|Tablet|Palm|maemo|plucker|phone|BlackBerry|symbian|IEMobile|mobile|ZuneWP7|Windows Phone|Opera Mini/i.test(navigator.userAgent)?!0:!1}catch(e){return!1}},cell_width:!0,scrollbarWidth:function(){var
e=$('<div style="width:50px;height:50px;overflow:hidden;position:absolute;top:-200px;left:-200px;"><div style="height:100px;"></div>');$("body").append(e);var
t=$("div",e).innerWidth();e.css("overflow-y","scroll");var
i=$("div",e).innerWidth();return $(e).remove(),t-i<0?18:t-i},ajaxOptions:{},from:function(source){var
$t=this,QueryObject=function(d,q){"string"==typeof
d&&(d=$.data(d));var
self=this,_data=d,_usecase=!0,_trim=!1,_query=q,_stripNum=/[\$,%]/g,_lastCommand=null,_lastField=null,_orDepth=0,_negate=!1,_queuedOperator="",_sorting=[],_useProperties=!0;if("object"!=typeof
d||!d.push)throw"data provides is not an array";return 0<d.length&&(_useProperties="object"==typeof
d[0]),this._hasData=function(){return null!==_data&&0!==_data.length},this._getStr=function(e){var
t=[];return _trim&&t.push("jQuery.trim("),t.push("String("+e+")"),_trim&&t.push(")"),_usecase||t.push(".toLowerCase()"),t.join("")},this._strComp=function(e){return"string"==typeof
e?".toString()":""},this._group=function(e,t){return{field:e.toString(),unique:t,items:[]}},this._toStr=function(e){return _trim&&(e=$.jgrid.trim(e)),e=e.toString().replace(/\\/g,"\\\\").replace(/\"/g,'\\"'),_usecase?e:e.toLowerCase()},this._funcLoop=function(i){var
r=[];return $.each(_data,function(e,t){r.push(i(t))}),r},this._append=function(e){var
t;for(null===_query?_query="":_query+=""===_queuedOperator?" && ":_queuedOperator,t=0;t<_orDepth;t++)_query+="(";_negate&&(_query+="!"),_query+="("+e+")",_negate=!1,_queuedOperator="",_orDepth=0},this._setCommand=function(e,t){_lastCommand=e,_lastField=t},this._resetNegate=function(){_negate=!1},this._repeatCommand=function(e,t){return null===_lastCommand?self:null!==e&&null!==t?_lastCommand(e,t):null!==_lastField&&_useProperties?_lastCommand(_lastField,e):_lastCommand(e)},this._equals=function(e,t){return 0===self._compare(e,t,1)},this._compare=function(e,t,i){var
r=Object.prototype.toString;if(void
0===i&&(i=1),void
0===e&&(e=null),void
0===t&&(t=null),null===e&&null===t)return 0;if(null===e&&null!==t)return 1;if(null!==e&&null===t)return-1;if("[object Date]"===r.call(e)&&"[object Date]"===r.call(t)||"number"==typeof
e&&"number"==typeof
t)return t<e?i:e<t?-i:0;t=String(e).localeCompare(String(t));return t<0?-i:0<t?i:0},this._performSort=function(){0!==_sorting.length&&(_data=self._doSort(_data,0))},this._doSort=function(e,t){var
i=_sorting[t].by,r=_sorting[t].dir,o=_sorting[t].type,n=_sorting[t].datefmt,a=_sorting[t].sfunc;if(t===_sorting.length-1)return self._getOrder(e,i,r,o,n,a);t++;for(var
s,l,d=self._getGroup(e,i,r,o,n),p=[],c=0;c<d.length;c++)for(l=self._doSort(d[c].items,t),s=0;s<l.length;s++)p.push(l[s]);return p},this._getOrder=function(e,i,t,r,o,n){var
a,s,l,d,p=[],c=[],u="a"===t?1:-1;void
0===r&&(r="text"),d="float"===r||"number"===r||"currency"===r||"numeric"===r?function(e){e=parseFloat(String(e).replace(_stripNum,""));return isNaN(e)?Number.NEGATIVE_INFINITY:e}:"int"===r||"integer"===r?function(e){return e?parseFloat(String(e).replace(_stripNum,"")):Number.NEGATIVE_INFINITY}:"date"===r||"datetime"===r?function(e){return $.jgrid.parseDate.call($t,o,e).getTime()}:$.jgrid.isFunction(r)?r:function(e){return e=e?$.jgrid.trim(String(e)):"",_usecase?e:e.toLowerCase()},$.each(e,function(e,t){void
0===(s=""!==i?$.jgrid.getAccessor(t,i):t)&&(s=""),s=d(s,t),c.push({vSort:s,index:e})}),$.jgrid.isFunction(n)?c.sort(function(e,t){return n.call(this,e.vSort,t.vSort,u,e,t)}):c.sort(function(e,t){return self._compare(e.vSort,t.vSort,u)}),l=0;for(var
g=e.length;l<g;)a=c[l].index,p.push(e[a]),l++;return p},this._getGroup=function(e,i,t,r,o){var
n,a=[],s=null,l=null;return $.each(self._getOrder(e,i,t,r,o),function(e,t){null==(n=$.jgrid.getAccessor(t,i))&&(n=""),self._equals(l,n)||(l=n,null!==s&&a.push(s),s=self._group(i,n)),s.items.push(t)}),null!==s&&a.push(s),a},this.ignoreCase=function(){return _usecase=!1,self},this.useCase=function(){return _usecase=!0,self},this.trim=function(){return _trim=!0,self},this.noTrim=function(){return _trim=!1,self},this.execute=function(){var
match=_query,results=[];return null===match?self:($.each(_data,function(){eval(match)&&results.push(this)}),_data=results,self)},this.data=function(){return _data},this.select=function(i){if(self._performSort(),!self._hasData())return[];if(self.execute(),$.jgrid.isFunction(i)){var
r=[];return $.each(_data,function(e,t){r.push(i(t))}),r}return _data},this.hasMatch=function(){return!!self._hasData()&&(self.execute(),0<_data.length)},this.andNot=function(e,t,i){return _negate=!_negate,self.and(e,t,i)},this.orNot=function(e,t,i){return _negate=!_negate,self.or(e,t,i)},this.not=function(e,t,i){return self.andNot(e,t,i)},this.and=function(e,t,i){return _queuedOperator=" && ",void
0===e?self:self._repeatCommand(e,t,i)},this.or=function(e,t,i){return _queuedOperator=" || ",void
0===e?self:self._repeatCommand(e,t,i)},this.orBegin=function(){return _orDepth++,self},this.orEnd=function(){return null!==_query&&(_query+=")"),self},this.isNot=function(e){return _negate=!_negate,self.is(e)},this.is=function(e){return self._append("this."+e),self._resetNegate(),self},this._compareValues=function(e,t,i,r,o){var
n=_useProperties?"jQuery.jgrid.getAccessor(this,'"+t+"')":"this";void
0===i&&(i=null);var
a=i,s=void
0===o.stype?"text":o.stype;if(null!==i)switch(s){case"int":case"integer":n="parseInt("+n+",10)",a="parseInt("+(a=isNaN(Number(a))||""===a?Number.NEGATIVE_INFINITY:a)+",10)";break;case"float":case"number":case"numeric":a=String(a).replace(_stripNum,""),n="parseFloat("+n+")",a="parseFloat("+(a=isNaN(Number(a))||""===a?Number.NEGATIVE_INFINITY:Number(a))+")";break;case"date":case"datetime":a=String($.jgrid.parseDate.call($t,o.srcfmt||"Y-m-d",a).getTime()),n='jQuery.jgrid.parseDate.call(jQuery("#'+$.jgrid.jqID($t.p.id)+'")[0],"'+o.srcfmt+'",'+n+").getTime()";break;default:n=self._getStr(n),a=self._getStr('"'+self._toStr(a)+'"')}return self._append(n+" "+r+" "+a),self._setCommand(e,t),self._resetNegate(),self},this.equals=function(e,t,i){return self._compareValues(self.equals,e,t,"==",i)},this.notEquals=function(e,t,i){return self._compareValues(self.equals,e,t,"!==",i)},this.isNull=function(e,t,i){return self._compareValues(self.equals,e,null,"===",i)},this.greater=function(e,t,i){return self._compareValues(self.greater,e,t,">",i)},this.less=function(e,t,i){return self._compareValues(self.less,e,t,"<",i)},this.greaterOrEquals=function(e,t,i){return self._compareValues(self.greaterOrEquals,e,t,">=",i)},this.lessOrEquals=function(e,t,i){return self._compareValues(self.lessOrEquals,e,t,"<=",i)},this.startsWith=function(e,t){var
i=null==t?e:t,i=(_trim?$.jgrid.trim(i.toString()):i.toString()).length;return _useProperties?self._append(self._getStr("jQuery.jgrid.getAccessor(this,'"+e+"')")+".substr(0,"+i+") == "+self._getStr('"'+self._toStr(t)+'"')):(null!=t&&(i=(_trim?$.jgrid.trim(t.toString()):t.toString()).length),self._append(self._getStr("this")+".substr(0,"+i+") == "+self._getStr('"'+self._toStr(e)+'"'))),self._setCommand(self.startsWith,e),self._resetNegate(),self},this.endsWith=function(e,t){var
i=null==t?e:t,i=(_trim?$.jgrid.trim(i.toString()):i.toString()).length;return _useProperties?self._append(self._getStr("jQuery.jgrid.getAccessor(this,'"+e+"')")+".substr("+self._getStr("jQuery.jgrid.getAccessor(this,'"+e+"')")+".length-"+i+","+i+') == "'+self._toStr(t)+'"'):self._append(self._getStr("this")+".substr("+self._getStr("this")+'.length-"'+self._toStr(e)+'".length,"'+self._toStr(e)+'".length) == "'+self._toStr(e)+'"'),self._setCommand(self.endsWith,e),self._resetNegate(),self},this.contains=function(e,t){return _useProperties?self._append(self._getStr("jQuery.jgrid.getAccessor(this,'"+e+"')")+'.indexOf("'+self._toStr(t)+'",0) > -1'):self._append(self._getStr("this")+'.indexOf("'+self._toStr(e)+'",0) > -1'),self._setCommand(self.contains,e),self._resetNegate(),self},this.user=function(e,t,i){return self._append("$t.p.customFilterDef."+e+'.action.call($t ,{rowItem:this, searchName:"'+t+'",searchValue:"'+i+'"})'),self._setCommand(self.user,t),self._resetNegate(),self},this.inData=function(e,t,i){t=void
0===t?"":self._getStr('"'+self._toStr(t)+'"');return _useProperties?self._append(t+".split(',').indexOf( jQuery.jgrid.getAccessor(this,'"+e+"') ) > -1"):self._append(t+".split(',').indexOf(this."+e+") > -1"),self._setCommand(self.inData,e),self._resetNegate(),self},this.groupBy=function(e,t,i,r){return self._hasData()?self._getGroup(_data,e,t,i,r):null},this.orderBy=function(e,t,i,r,o){return null==i&&(i="text"),null==r&&(r="Y-m-d"),null==o&&(o=!1),"desc"!==(t=null==t?"a":$.jgrid.trim(t.toString().toLowerCase()))&&"descending"!==t||(t="d"),"asc"!==t&&"ascending"!==t||(t="a"),_sorting.push({by:e,dir:t,type:i,datefmt:r,sfunc:o}),self},self};return new
QueryObject(source,null)},getMethod:function(e){return this.getAccessor($.fn.jqGrid,e)},extend:function(e){$.extend($.fn.jqGrid,e),this.no_legacy_api||$.fn.extend(e)},clearBeforeUnload:function(e){var
t,i=$("#"+$.jgrid.jqID(e))[0];if(i.grid){t=i.grid,$.jgrid.isFunction(t.emptyRows)&&t.emptyRows.call(i,!0,!0),$(document).off("mouseup.jqGrid"+i.p.id),$(t.hDiv).off("mousemove"),$(i).off();for(var
r=t.headers.length,o=["formatCol","sortData","updatepager","refreshIndex","setHeadCheckBox","constructTr","formatter","addXmlData","addJSONData","grid","p","addLocalData"],n=0;n<r;n++)t.headers[n].el=null;for(n
in
t)t.hasOwnProperty(n)&&(t[n]=null);for(n
in
i.p)i.p.hasOwnProperty(n)&&(i.p[n]=Array.isArray(i.p[n])?[]:null);for(r=o.length,n=0;n<r;n++)i.hasOwnProperty(o[n])&&(i[o[n]]=null,delete
i[o[n]])}},gridUnload:function(e){var
t,i,r,o;e&&(0===(e=$.jgrid.trim(e)).indexOf("#")&&(e=e.substring(1)),(t=$("#"+$.jgrid.jqID(e))[0]).grid&&(i={id:$(t).attr("id"),cl:$(t).attr("class")},t.p.pager&&$(t.p.pager).off().empty().removeClass("ui-state-default ui-jqgrid-pager ui-corner-bottom"),(r=document.createElement("table")).className=i.cl,o=$.jgrid.jqID(t.id),$(r).removeClass("ui-jqgrid-btable ui-common-table").insertBefore("#gbox_"+o),1===$(t.p.pager).parents("#gbox_"+o).length&&$(t.p.pager).insertBefore("#gbox_"+o),$.jgrid.clearBeforeUnload(e),$("#gbox_"+o).remove(),$(r).attr({id:i.id}),$("#alertmod_"+$.jgrid.jqID(e)).remove()))},gridDestroy:function(e){if(e){0===(e=$.jgrid.trim(e)).indexOf("#")&&(e=e.substring(1));var
t=$("#"+$.jgrid.jqID(e))[0];if(t.grid){t.p.pager&&$(t.p.pager).remove();try{$.jgrid.clearBeforeUnload(e),$("#gbox_"+$.jgrid.jqID(e)).remove()}catch(e){}}}},isElementInViewport:function(e){e=e.getBoundingClientRect();return 0<=e.left&&e.right<=(window.innerWidth||document.documentElement.clientWidth)},getTextWidth:function(e,t){var
i;return jQuery._cacheCanvas||(i=document.createElement("canvas"),document.createDocumentFragment().appendChild(i),jQuery._cacheCanvas=i.getContext("2d"),t&&(jQuery._cacheCanvas.font=t)),jQuery._cacheCanvas.measureText($.jgrid.stripHtml(e)).width},getFont:function(e){e=window.getComputedStyle(e,null);return e.getPropertyValue("font-style")+" "+e.getPropertyValue("font-variant")+" "+e.getPropertyValue("font-weight")+" "+e.getPropertyValue("font-size")+" "+e.getPropertyValue("font-family")},setSelNavIndex:function(i,r){var
e=$(".ui-pg-button",i.p.pager);$.each(e,function(e,t){if(r===t)return i.p.navIndex=e,!1}),$(r).attr("tabindex","0")},splitSearch:function(e){var
t,i='{"groupOp":"'+e.mergeOper+'","rules":[],"groups":[';for(t
in
e)e.hasOwnProperty(t)&&"mergeOper"!==t&&(i+=null!==e[t]&&""!==e[t]?e[t]+",":"",0);return i=i.slice(0,-1),i+="]}"},getElemByAttrVal:function(e,t,i,r){"boolean"!=typeof
r&&(r=!1);var
o=Array.isArray(e)?e.length:0,n=0,a={},s=-1;if(0<o)for(;n<o;){if(e[n][t]===i){a=e[n],s=n;break}n++}return r?s:a},styleUI:{jQueryUI:{common:{disabled:"ui-state-disabled",highlight:"ui-state-highlight",hover:"ui-state-hover",cornerall:"ui-corner-all",cornertop:"ui-corner-top",cornerbottom:"ui-corner-bottom",hidden:"ui-helper-hidden",icon_base:"ui-icon",overlay:"ui-widget-overlay",active:"ui-state-active",error:"ui-state-error",button:"ui-state-default ui-corner-all",content:"ui-widget-content"},base:{entrieBox:"ui-widget ui-widget-content ui-corner-all",viewBox:"",headerTable:"",headerBox:"ui-state-default",rowTable:"",rowBox:"ui-widget-content",stripedTable:"ui-jqgrid-table-striped",footerTable:"",footerBox:"ui-widget-content",headerRowTable:"",headerRowBox:"ui-widget-content",headerDiv:"ui-state-default",gridtitleBox:"ui-widget-header ui-corner-top ui-helper-clearfix",customtoolbarBox:"ui-state-default",loadingBox:"ui-state-default ui-state-active",rownumBox:"ui-state-default",scrollBox:"ui-widget-content",multiBox:"",pagerBox:"ui-state-default ui-corner-bottom",pagerTable:"",toppagerBox:"ui-state-default",pgInput:"ui-corner-all",pgSelectBox:"ui-widget-content ui-corner-all",pgButtonBox:"ui-corner-all",icon_first:"ui-icon-seek-first",icon_prev:"ui-icon-seek-prev",icon_next:"ui-icon-seek-next",icon_end:"ui-icon-seek-end",icon_asc:"ui-icon-triangle-1-n",icon_desc:"ui-icon-triangle-1-s",icon_caption_open:"ui-icon-circle-triangle-n",icon_caption_close:"ui-icon-circle-triangle-s"},modal:{modal:"ui-widget ui-widget-content ui-corner-all ui-dialog",header:"ui-widget-header ui-corner-all ui-helper-clearfix",content:"ui-widget-content",resizable:"ui-resizable-handle ui-resizable-se",icon_close:"ui-icon-closethick",icon_resizable:"ui-icon-gripsmall-diagonal-se"},celledit:{inputClass:"ui-widget-content ui-corner-all"},inlinedit:{inputClass:"ui-widget-content ui-corner-all",icon_edit_nav:"ui-icon-pencil",icon_add_nav:"ui-icon-plus",icon_save_nav:"ui-icon-disk",icon_cancel_nav:"ui-icon-cancel"},formedit:{inputClass:"ui-widget-content ui-corner-all",icon_prev:"ui-icon-triangle-1-w",icon_next:"ui-icon-triangle-1-e",icon_save:"ui-icon-disk",icon_close:"ui-icon-close",icon_del:"ui-icon-scissors",icon_cancel:"ui-icon-cancel"},navigator:{icon_edit_nav:"ui-icon-pencil",icon_add_nav:"ui-icon-plus",icon_del_nav:"ui-icon-trash",icon_search_nav:"ui-icon-search",icon_refresh_nav:"ui-icon-refresh",icon_view_nav:"ui-icon-document",icon_newbutton_nav:"ui-icon-newwin"},grouping:{icon_plus:"ui-icon-circlesmall-plus",icon_minus:"ui-icon-circlesmall-minus"},filter:{table_widget:"ui-widget ui-widget-content",srSelect:"ui-widget-content ui-corner-all",srInput:"ui-widget-content ui-corner-all",menu_widget:"ui-widget ui-widget-content ui-corner-all",icon_search:"ui-icon-search",icon_reset:"ui-icon-arrowreturnthick-1-w",icon_query:"ui-icon-comment"},subgrid:{icon_plus:"ui-icon-plus",icon_minus:"ui-icon-minus",icon_open:"ui-icon-carat-1-sw"},treegrid:{icon_plus:"ui-icon-triangle-1-",icon_minus:"ui-icon-triangle-1-s",icon_leaf:"ui-icon-radio-off"},fmatter:{icon_edit:"ui-icon-pencil",icon_add:"ui-icon-plus",icon_save:"ui-icon-disk",icon_cancel:"ui-icon-cancel",icon_del:"ui-icon-trash"},colmenu:{menu_widget:"ui-widget ui-widget-content ui-corner-all",input_checkbox:"ui-widget ui-widget-content",filter_select:"ui-widget-content ui-corner-all",filter_input:"ui-widget-content ui-corner-all",icon_menu:"ui-icon-comment",icon_sort_asc:"ui-icon-arrow-1-n",icon_sort_desc:"ui-icon-arrow-1-s",icon_columns:"ui-icon-extlink",icon_filter:"ui-icon-calculator",icon_group:"ui-icon-grip-solid-horizontal",icon_freeze:"ui-icon-grip-solid-vertical",icon_move:"ui-icon-arrow-4",icon_new_item:"ui-icon-newwin",icon_toolbar_menu:"ui-icon-document"}},Bootstrap:{common:{disabled:"ui-disabled",highlight:"success",hover:"active",cornerall:"",cornertop:"",cornerbottom:"",hidden:"",icon_base:"glyphicon",overlay:"ui-overlay",active:"active",error:"bg-danger",button:"btn btn-default",content:""},base:{entrieBox:"",viewBox:"table-responsive",headerTable:"table table-bordered",headerBox:"",rowTable:"table table-bordered",rowBox:"",stripedTable:"table-striped",footerTable:"table table-bordered",footerBox:"",headerRowTable:"table table-bordered",headerRowBox:"",headerDiv:"",gridtitleBox:"",customtoolbarBox:"",loadingBox:"row",rownumBox:"active",scrollBox:"",multiBox:"checkbox",pagerBox:"",pagerTable:"table",toppagerBox:"",pgInput:"form-control",pgSelectBox:"form-control",pgButtonBox:"",icon_first:"glyphicon-step-backward",icon_prev:"glyphicon-backward",icon_next:"glyphicon-forward",icon_end:"glyphicon-step-forward",icon_asc:"glyphicon-triangle-top",icon_desc:"glyphicon-triangle-bottom",icon_caption_open:"glyphicon-circle-arrow-up",icon_caption_close:"glyphicon-circle-arrow-down"},modal:{modal:"modal-content",header:"modal-header",title:"modal-title",content:"modal-body",resizable:"ui-resizable-handle ui-resizable-se",icon_close:"glyphicon-remove-circle",icon_resizable:"glyphicon-import"},celledit:{inputClass:"form-control"},inlinedit:{inputClass:"form-control",icon_edit_nav:"glyphicon-edit",icon_add_nav:"glyphicon-plus",icon_save_nav:"glyphicon-save",icon_cancel_nav:"glyphicon-remove-circle"},formedit:{inputClass:"form-control",icon_prev:"glyphicon-step-backward",icon_next:"glyphicon-step-forward",icon_save:"glyphicon-save",icon_close:"glyphicon-remove-circle",icon_del:"glyphicon-trash",icon_cancel:"glyphicon-remove-circle"},navigator:{icon_edit_nav:"glyphicon-edit",icon_add_nav:"glyphicon-plus",icon_del_nav:"glyphicon-trash",icon_search_nav:"glyphicon-search",icon_refresh_nav:"glyphicon-refresh",icon_view_nav:"glyphicon-info-sign",icon_newbutton_nav:"glyphicon-new-window"},grouping:{icon_plus:"glyphicon-triangle-right",icon_minus:"glyphicon-triangle-bottom"},filter:{table_widget:"table table-condensed",srSelect:"form-control",srInput:"form-control",menu_widget:"",icon_search:"glyphicon-search",icon_reset:"glyphicon-refresh",icon_query:"glyphicon-comment"},subgrid:{icon_plus:"glyphicon-triangle-right",icon_minus:"glyphicon-triangle-bottom",icon_open:"glyphicon-indent-left"},treegrid:{icon_plus:"glyphicon-triangle-right",icon_minus:"glyphicon-triangle-bottom",icon_leaf:"glyphicon-unchecked"},fmatter:{icon_edit:"glyphicon-edit",icon_add:"glyphicon-plus",icon_save:"glyphicon-save",icon_cancel:"glyphicon-remove-circle",icon_del:"glyphicon-trash"},colmenu:{menu_widget:"",input_checkbox:"",filter_select:"form-control",filter_input:"form-control",icon_menu:"glyphicon-menu-hamburger",icon_sort_asc:"glyphicon-sort-by-alphabet",icon_sort_desc:"glyphicon-sort-by-alphabet-alt",icon_columns:"glyphicon-list-alt",icon_filter:"glyphicon-filter",icon_group:"glyphicon-align-left",icon_freeze:"glyphicon-object-align-horizontal",icon_move:"glyphicon-move",icon_new_item:"glyphicon-new-window",icon_toolbar_menu:"glyphicon-menu-hamburger"}},Bootstrap4:{common:{disabled:"ui-disabled",highlight:"table-success",hover:"table-active",cornerall:"",cornertop:"",cornerbottom:"",hidden:"",overlay:"ui-overlay",active:"active",error:"alert-danger",button:"btn btn-light",content:""},base:{entrieBox:"",viewBox:"table-responsive",headerTable:"table table-bordered",headerBox:"",rowTable:"table table-bordered",rowBox:"",stripedTable:"table-striped",footerTable:"table table-bordered",footerBox:"",headerRowTable:"table table-bordered",headerRowBox:"",headerDiv:"",gridtitleBox:"",customtoolbarBox:"",loadingBox:"row",rownumBox:"active",scrollBox:"",multiBox:"checkbox",pagerBox:"",pagerTable:"table",toppagerBox:"",pgInput:"form-control",pgSelectBox:"form-control",pgButtonBox:""},modal:{modal:"modal-content",header:"modal-header",title:"modal-title",content:"modal-body",resizable:"ui-resizable-handle ui-resizable-se",icon_close:"oi-circle-x",icon_resizable:"oi-circle-x"},celledit:{inputClass:"form-control"},inlinedit:{inputClass:"form-control"},formedit:{inputClass:"form-control"},navigator:{},grouping:{},filter:{table_widget:"table table-condensed",srSelect:"form-control",srInput:"form-control",menu_widget:""},subgrid:{},treegrid:{},fmatter:{},colmenu:{menu_widget:"",input_checkbox:"",filter_select:"form-control",filter_input:"form-control"}}},iconSet:{Iconic:{common:{icon_base:"oi"},base:{icon_first:"oi-media-step-backward",icon_prev:"oi-caret-left",icon_next:"oi-caret-right",icon_end:"oi-media-step-forward",icon_asc:"oi-caret-top",icon_desc:"oi-caret-bottom",icon_caption_open:"oi-collapse-up",icon_caption_close:"oi-expand-down"},modal:{icon_close:"oi-circle-x",icon_resizable:"oi-plus"},inlinedit:{icon_edit_nav:"oi-pencil",icon_add_nav:"oi-plus",icon_save_nav:"oi-check",icon_cancel_nav:"oi-action-undo"},formedit:{icon_prev:"oi-chevron-left",icon_next:"oi-chevron-right",icon_save:"oi-check",icon_close:"oi-ban",icon_del:"oi-delete",icon_cancel:"oi-ban"},navigator:{icon_edit_nav:"oi-pencil",icon_add_nav:"oi-plus",icon_del_nav:"oi-trash",icon_search_nav:"oi-zoom-in",icon_refresh_nav:"oi-reload",icon_view_nav:"oi-browser",icon_newbutton_nav:"oi-book"},grouping:{icon_plus:"oi-caret-right",icon_minus:"oi-caret-bottom"},filter:{icon_search:"oi-magnifying-glass",icon_reset:"oi-reload",icon_query:"oi-comment-square"},subgrid:{icon_plus:"oi-chevron-right",icon_minus:"oi-chevron-bottom",icon_open:"oi-expand-left"},treegrid:{icon_plus:"oi-plus",icon_minus:"oi-minus",icon_leaf:"oi-media-record"},fmatter:{icon_edit:"oi-pencil",icon_add:"oi-plus",icon_save:"oi-check",icon_cancel:"oi-action-undo",icon_del:"oi-trash"},colmenu:{icon_menu:"oi-list",icon_sort_asc:"oi-sort-ascending",icon_sort_desc:"oi-sort-descending",icon_columns:"oi-project",icon_filter:"oi-magnifying-glass",icon_group:"oi-list-rich",icon_freeze:"oi-spreadsheet",icon_move:"oi-move",icon_new_item:"oi-external-link",icon_toolbar_menu:"oi-menu"}},Octicons:{common:{icon_base:"octicon"},base:{icon_first:"octicon-triangle-left",icon_prev:"octicon-chevron-left",icon_next:"octicon-chevron-right",icon_end:"octicon-triangle-right",icon_asc:"octicon-triangle-up",icon_desc:"octicon-triangle-down",icon_caption_open:"octicon-triangle-up",icon_caption_close:"octicon-triangle-down"},modal:{icon_close:"octicon-x",icon_resizable:"octicon-plus"},inlinedit:{icon_edit_nav:"octicon-pencil",icon_add_nav:"octicon-plus",icon_save_nav:"octicon-check",icon_cancel_nav:"octicon-circle-slash"},formedit:{icon_prev:"octicon-chevron-left",icon_next:"octicon-chevron-right",icon_save:"octicon-check",icon_close:"octicon-x",icon_del:"octicon-trashcan",icon_cancel:"octicon-circle-slash"},navigator:{icon_edit_nav:"octicon-pencil",icon_add_nav:"octicon-plus",icon_del_nav:"octicon-trashcan",icon_search_nav:"octicon-search",icon_refresh_nav:"octicon-sync",icon_view_nav:"octicon-file",icon_newbutton_nav:"octicon-link-external"},grouping:{icon_plus:"octicon-triangle-right",icon_minus:"octicon-triangle-down"},filter:{icon_search:"octicon-search",icon_reset:"octicon-sync",icon_query:"octicon-file-code"},subgrid:{icon_plus:"octicon-triangle-right",icon_minus:"octicon-triangle-down",icon_open:"octicon-git-merge"},treegrid:{icon_plus:"octicon-triangle-right",icon_minus:"octicon-triangle-down",icon_leaf:"octicon-primitive-dot"},fmatter:{icon_edit:"octicon-pencil",icon_add:"octicon-plus",icon_save:"octicon-check",icon_cancel:"octicon-circle-slash",icon_del:"octicon-trashcan"},colmenu:{icon_menu:"octicon-grabber",icon_sort_asc:"octicon-arrow-up",icon_sort_desc:"octicon-arrow-down",icon_columns:"octicon-repo",icon_filter:"octicon-search",icon_group:"octicon-list-unordered",icon_freeze:"octicon-repo",icon_move:"octicon-git-compare",icon_new_item:"octicon-link-external",icon_toolbar_menu:"octicon-three-bars"}},fontAwesome:{common:{icon_base:"fas"},base:{icon_first:"fa-step-backward",icon_prev:"fa-backward",icon_next:"fa-forward",icon_end:"fa-step-forward",icon_asc:"fa-caret-up",icon_desc:"fa-caret-down",icon_caption_open:"fa-caret-square-up",icon_caption_close:"fa-caret-square-down "},modal:{icon_close:"fa-window-close",icon_resizable:"fa-plus"},inlinedit:{icon_edit_nav:"fa-edit",icon_add_nav:"fa-plus",icon_save_nav:"fa-save",icon_cancel_nav:"fa-replay"},formedit:{icon_prev:"fa-chevron-left",icon_next:"fa-chevron-right",icon_save:"fa-save",icon_close:"fa-window-close",icon_del:"fa-trash",icon_cancel:"fa-times"},navigator:{icon_edit_nav:"fa-edit",icon_add_nav:"fa-plus",icon_del_nav:"fa-trash",icon_search_nav:"fa-search",icon_refresh_nav:"fa-sync",icon_view_nav:"fa-sticky-note",icon_newbutton_nav:"fa-external-link-alt"},grouping:{icon_plus:"fa-caret-right",icon_minus:"fa-caret-down"},filter:{icon_search:"fa-search",icon_reset:"fa-reply",icon_query:"fa-pen-square "},subgrid:{icon_plus:"fa-arrow-circle-right",icon_minus:"fa-arrow-circle-down",icon_open:"fa-ellipsis-v"},treegrid:{icon_plus:"fa-plus",icon_minus:"fa-minus",icon_leaf:"fa-circle"},fmatter:{icon_edit:"fa-edit",icon_add:"fa-plus",icon_save:"fa-save",icon_cancel:"fa-undo",icon_del:"fa-trash"},colmenu:{icon_menu:"fa-ellipsis-v",icon_sort_asc:"fa-sort-amount-up",icon_sort_desc:"fa-sort-amount-down",icon_columns:"fa-columns",icon_filter:"fa-filter",icon_group:"fa-object-group",icon_freeze:"fa-snowflake",icon_move:"fa-expand-arrows-alt",icon_new_item:"fa-external-link-alt",icon_toolbar_menu:"fa-list"}}}}),$.fn.jqGrid=function(He){if("string"!=typeof
He)return this.each(function(){if(!this.grid){null!=He&&void
0!==He.data&&(pe=He.data,He.data=[]);var
b=$.extend(!0,{url:"",height:150,page:1,rowNum:20,rowTotal:null,records:0,pager:"",pgbuttons:!0,pginput:!0,colModel:[],rowList:[],colNames:[],sortorder:"asc",sortname:"",datatype:"xml",mtype:"GET",altRows:!1,selarrrow:[],preserveSelection:!1,savedRow:[],shrinkToFit:!0,xmlReader:{},jsonReader:{},subGrid:!1,subGridModel:[],reccount:0,lastpage:0,lastsort:0,selrow:null,beforeSelectRow:null,onSelectRow:null,onSortCol:null,ondblClickRow:null,onRightClickRow:null,onPaging:null,onSelectAll:null,onInitGrid:null,loadComplete:null,gridComplete:null,loadError:null,loadBeforeSend:null,afterInsertRow:null,beforeRequest:null,beforeProcessing:null,onHeaderClick:null,viewrecords:!1,loadonce:!1,multiselect:!1,multikey:!1,multiboxonly:!1,multimail:!1,multiselectWidth:30,editurl:null,search:!1,caption:"",hidegrid:!0,hiddengrid:!1,postData:{},userData:{},treeGrid:!1,treeGridModel:"nested",treeReader:{},treeANode:-1,ExpandColumn:null,tree_root_level:0,prmNames:{page:"page",rows:"rows",sort:"sidx",order:"sord",search:"_search",nd:"nd",id:"id",oper:"oper",editoper:"edit",addoper:"add",deloper:"del",subgridid:"id",npage:null,totalrows:"totalrows"},forceFit:!1,gridstate:"visible",cellEdit:!1,cellsubmit:"remote",nv:0,loadui:"enable",toolbar:[!1,""],scroll:!1,deselectAfterSort:!0,scrollrows:!1,autowidth:!1,scrollOffset:$.jgrid.scrollbarWidth()+3,cellLayout:5,subGridWidth:20,gridview:!0,rownumWidth:35,rownumbers:!1,pagerpos:"center",recordpos:"right",footerrow:!1,userDataOnFooter:!1,headerrow:!1,userDataOnHeader:!1,hoverrows:!0,viewsortcols:[!1,"vertical",!0],resizeclass:"",autoencode:!1,remapColumns:[],ajaxGridOptions:{},direction:"ltr",toppager:!1,headertitles:!1,scrollTimeout:40,data:[],_index:{},grouping:!1,groupingView:{groupField:[],groupOrder:[],groupText:[],groupColumnShow:[],groupSummary:[],showSummaryOnHide:!1,sortitems:[],sortnames:[],summary:[],summaryval:[],plusicon:"",minusicon:"",displayField:[],groupSummaryPos:[],formatDisplayField:[],_locgr:!1},groupHeaderOn:!1,ignoreCase:!0,cmTemplate:{},idPrefix:"",multiSort:!1,minColWidth:33,scrollPopUp:!1,scrollTopOffset:0,scrollLeftOffset:"100%",scrollMaxBuffer:0,storeNavOptions:!1,regional:"en",styleUI:"jQueryUI",iconSet:"Iconic",responsive:!1,forcePgButtons:!1,restoreCellonFail:!0,editNextRowCell:!1,colFilters:{},colMenu:!1,colMenuCustom:{},colMenuColumnDone:null,colMenuBeforeProcess:null,treeGrid_bigData:!1,treeGrid_rootParams:{otherData:{}},treeGrid_beforeRequest:null,treeGrid_afterLoadComplete:null,useNameForSearch:!1,formatFooterData:!1,formatHeaderData:!1,mergeSearch:!1,searchModules:{mergeOper:"AND",filterInput:!0,filterToolbar:!0,searchGrid:!0,colMenuSearch:!0},emptyRecordRow:!0},$.jgrid.defaults,He);void
0!==pe&&(b.data=pe,He.data=pe);var
V=this,h={headers:[],cols:[],footers:[],hrheaders:[],dragStart:function(e,t,i){var
r=$(this.bDiv).offset().left,o=parseInt(b.colModel[e].minResizeWidth||b.minColWidth,10);isNaN(o)&&(o=33),this.resizing={idx:e,startX:t.pageX,sOL:t.pageX-r,minW:o},this.hDiv.style.cursor="col-resize",this.curGbox=$("#rs_m"+$.jgrid.jqID(b.id),"#gbox_"+$.jgrid.jqID(b.id)),this.curGbox.css({display:"block",left:t.pageX-r,top:i[1],height:i[2]}),$(V).triggerHandler("jqGridResizeStart",[t,e]),$.jgrid.isFunction(b.resizeStart)&&b.resizeStart.call(V,t,e),document.onselectstart=function(){return!1}},dragMove:function(e){var
t,i,r,o;this.resizing&&(t=e.pageX-this.resizing.startX,i=this.headers[this.resizing.idx],(r="ltr"===b.direction?i.width+t:i.width-t)>this.resizing.minW&&(this.curGbox.css({left:this.resizing.sOL+t}),!0===b.forceFit?(o=this.headers[this.resizing.idx+b.nv],(e="ltr"===b.direction?o.width-t:o.width+t)>this.resizing.minW&&(i.newWidth=r,o.newWidth=e)):(this.newWidth="ltr"===b.direction?b.tblwidth+t:b.tblwidth-t,i.newWidth=r)))},dragEnd:function(e,t){var
i,r;this.hDiv.style.cursor="default",void
0===t&&(t=!0),this.resizing&&(i=this.resizing.idx,r=this.headers[i].newWidth||this.headers[i].width,r=parseInt(r,10),this.resizing=!1,$("#rs_m"+$.jgrid.jqID(b.id)).css("display","none"),b.colModel[i].width=r,this.headers[i].width=r,this.headers[i].el.style.width=r+"px",this.cols[i].style.width=r+"px",0<this.footers.length&&(this.footers[i].style.width=r+"px"),0<this.hrheaders.length&&(this.hrheaders[i].style.width=r+"px"),!0===b.forceFit?(r=this.headers[i+b.nv].newWidth||this.headers[i+b.nv].width,this.headers[i+b.nv].width=r,this.headers[i+b.nv].el.style.width=r+"px",this.cols[i+b.nv].style.width=r+"px",0<this.footers.length&&(this.footers[i+b.nv].style.width=r+"px"),0<this.hrheaders.length&&(this.hrheaders[i+b.nv].style.width=r+"px"),b.colModel[i+b.nv].width=r):(b.tblwidth=this.newWidth||b.tblwidth,$(this.bDiv).find("table").first().css("width",b.tblwidth+"px"),$(this.hDiv).find("table").first().css("width",b.tblwidth+"px"),this.hDiv.scrollLeft=this.bDiv.scrollLeft,b.footerrow&&($(this.sDiv).find("table").first().css("width",b.tblwidth+"px"),this.sDiv.scrollLeft=this.bDiv.scrollLeft),b.headerrow&&($(this.hrDiv).find("table").first().css("width",b.tblwidth+"px"),this.hrDiv.scrollLeft=this.bDiv.scrollLeft)),e&&($(V).triggerHandler("jqGridResizeStop",[r,i]),$.jgrid.isFunction(b.resizeStop)&&b.resizeStop.call(V,r,i)),b.frozenColumns&&t&&($("#"+$.jgrid.jqID(b.id)).jqGrid("destroyFrozenColumns"),$("#"+$.jgrid.jqID(b.id)).jqGrid("setFrozenColumns"))),this.curGbox=null,document.onselectstart=function(){return!0}},populateVisible:function(){h.timer&&clearTimeout(h.timer),h.timer=null;var
e=$(h.bDiv).height();if(e){var
t,i,r,o,n,a,s,l,d,p,c=$(h.bDiv).find("table").first();if(c[0].rows.length)try{i=(t=c[0].rows[1])&&$(t).outerHeight()||h.prevRowHeight}catch(e){i=h.prevRowHeight}i&&(h.prevRowHeight=i,r=b.rowNum,o=h.scrollTop=h.bDiv.scrollTop,s=i*r,(a=(n=Math.round(c.position().top)-o)+c.height())<e&&n<=0&&(void
0===b.lastpage||(parseInt((a+o+s-1)/s,10)||0)<=b.lastpage)&&(d=parseInt((e-a+s-1)/s,10)||1,n=0<=a||d<2||!0===b.scroll?(l=(Math.round((a+o)/s)||0)+1,-1):1),0<n&&(l=(parseInt(o/s,10)||0)+1,d=(parseInt((o+e)/s,10)||0)+2-l,p=!0),d&&(b.lastpage&&(l>b.lastpage||1===b.lastpage||l===b.page&&l===b.lastpage)||(h.hDiv.loading?h.timer=setTimeout(h.populateVisible,b.scrollTimeout):(b.page=l,0<b.scrollMaxBuffer&&(0<r&&b.scrollMaxBuffer<r&&(b.scrollMaxBuffer=r+1),b.reccount>b.scrollMaxBuffer-(0<r?r:0)&&(p=!0)),p&&(h.selectionPreserver(c[0]),h.emptyRows.call(c[0],!1,!1)),h.populate(d)),b.scrollPopUp&&null!=b.lastpage&&($("#scroll_g"+b.id).show().html($.jgrid.template($.jgrid.getRegional(V,"defaults.pgtext",b.pgtext),b.page,b.lastpage)).css({top:b.scrollTopOffset+o*((parseInt(b.height,10)-45)/(parseInt(i,10)*parseInt(b.records,10)))+"px",left:b.scrollLeftOffset}),$(this).mouseout(function(){$("#scroll_g"+b.id).hide()})))))}},scrollGrid:function(){var
e;b.scroll&&(e=h.bDiv.scrollTop,void
0===h.scrollTop&&(h.scrollTop=0),e!==h.scrollTop&&(h.scrollTop=e,h.timer&&clearTimeout(h.timer),h.timer=setTimeout(h.populateVisible,b.scrollTimeout))),h.hDiv.scrollLeft=h.bDiv.scrollLeft,b.footerrow&&(h.sDiv.scrollLeft=h.bDiv.scrollLeft),b.headerrow&&(h.hrDiv.scrollLeft=h.bDiv.scrollLeft),b.frozenColumns&&$(h.fbDiv).scrollTop(h.bDiv.scrollTop);try{$("#column_menu").remove()}catch(e){}},selectionPreserver:function(t){var
i=t.p,r=i.selrow,o=i.selarrrow?$.makeArray(i.selarrrow):null,n=t.grid.bDiv.scrollLeft,a=function(){var
e;if(i.multiselect&&o&&0<o.length)for(e=0;e<o.length;e++)o[e]&&$(t).jqGrid("setSelection",o[e],!1,"_sp_");!i.multiselect&&r&&$(t).jqGrid("setSelection",r,!1,null),t.grid.bDiv.scrollLeft=n,$(t).off(".selectionPreserver",a)};$(t).on("jqGridGridComplete.selectionPreserver",a)}};if("TABLE"===this.tagName.toUpperCase()&&null!=this.id)if(void
0!==document.documentMode&&document.documentMode<=5)alert("Grid can not be used in this ('quirks') mode!");else{var
e,t,v,W,i=0;for(t
in
$.jgrid.regional)$.jgrid.regional.hasOwnProperty(t)&&(0===i&&(e=t),i++);if(1===i&&e!==b.regional&&(b.regional=e),$(this).empty().attr("tabindex","0"),this.p=b,this.p.useProp=!!$.fn.prop,0===this.p.colNames.length)for(i=0;i<this.p.colModel.length;i++)this.p.colNames[i]=this.p.colModel[i].label||this.p.colModel[i].name;if(this.p.colNames.length===this.p.colModel.length){"Bootstrap4"===V.p.styleUI&&$.jgrid.iconSet.hasOwnProperty(V.p.iconSet)&&$.extend(!0,$.jgrid.styleUI.Bootstrap4,$.jgrid.iconSet[V.p.iconSet]);var
K=$.jgrid.getMethod("getStyleUI"),Q=V.p.styleUI+".common",w=K(Q,"disabled",!0),X=K(Q,"highlight",!0),_=K(Q,"hover",!0),r=K(Q,"cornerall",!0),y=K(Q,"icon_base",!0),D=$.jgrid.styleUI[V.p.styleUI||"jQueryUI"].colmenu,o=$.jgrid.msie(),I=[],G=[],n=[],Q=V.p.styleUI+".base",a=$("<div "+K(Q,"viewBox",!1,"ui-jqgrid-view")+" role='grid'></div>");V.p.direction=$.jgrid.trim(V.p.direction.toLowerCase()),V.p._ald=!1,-1===$.inArray(V.p.direction,["ltr","rtl"])&&(V.p.direction="ltr"),v=V.p.direction,$(a).insertBefore(this),$(this).appendTo(a);var
s=$("<div "+K(Q,"entrieBox",!1,"ui-jqgrid")+"></div>");$(s).attr({id:"gbox_"+this.id,dir:v}).insertBefore(a),$(a).attr("id","gview_"+this.id).appendTo(s),$("<div "+K(V.p.styleUI+".common","overlay",!1,"jqgrid-overlay")+" id='lui_"+this.id+"'></div>").insertBefore(a),$("<div "+K(Q,"loadingBox",!1,"loading")+" id='load_"+this.id+"'>"+$.jgrid.getRegional(V,"defaults.loadtext",this.p.loadtext)+"</div>").insertBefore(a),$(this).attr({role:"presentation","aria-multiselectable":!!this.p.multiselect,"aria-labelledby":"gbox_"+this.id});var
l,d=$.jgrid.getFont(V),Y=function(e,t){return void
0===t&&(t=0),e=parseInt(e,10),isNaN(e)?t:e},p=function(e,t,i,r,o,n){var
a,s=V.p.colModel[e],l=s.align,d='style="',p=s.classes,c=s.name,u=[];return l&&(d+="text-align:"+l+";"),!0===s.hidden&&(d+="display:none;"),0===t?d+="width: "+h.headers[e].width+"px;":($.jgrid.isFunction(s.cellattr)||"string"==typeof
s.cellattr&&null!=$.jgrid.cellattr&&$.jgrid.isFunction($.jgrid.cellattr[s.cellattr]))&&(a=($.jgrid.isFunction(s.cellattr)?s.cellattr:$.jgrid.cellattr[s.cellattr]).call(V,o,i,r,s,n))&&"string"==typeof
a&&(-1<a.indexOf("title")&&(s.title=!1),-1<a.indexOf("class")&&(p=void
0),2===(u=(a=String(a).replace(/\s+\=/g,"=")).split("style=")).length?(u[1]=$.jgrid.trim(u[1]),0!==u[1].indexOf("'")&&0!==u[1].indexOf('"')||(u[1]=u[1].substring(1)),d+=u[1].replace(/'/gi,'"')):d+='"'),u.length?2<u.length&&(u[0]=""):(u[0]="",d+='"'),d+=(void
0!==p?' class="'+p+'"':"")+(s.title&&i?' title="'+$.jgrid.stripHtml(i)+'"':""),(d+=' aria-describedby="'+V.p.id+"_"+c+'"')+u[0]},c=function(e){return null==e||""===e?"&#160;":V.p.autoencode?$.jgrid.htmlEncode(e):String(e)},u=function(e,t,i,r,o){var
n,a=V.p.colModel[i];return t=void
0!==a.formatter?(n={rowId:e=""!==String(V.p.idPrefix)?$.jgrid.stripPref(V.p.idPrefix,e):e,colModel:a,gid:V.p.id,pos:i,styleUI:V.p.styleUI},$.jgrid.isFunction(a.formatter)?a.formatter.call(V,t,n,r,o):$.fmatter?$.fn.fmatter.call(V,a.formatter,t,n,r,o):c(t)):c(t),a.autosize&&(a._maxsize||(a._maxsize=a.canvas_width),a._maxsize=Math.max($.jgrid.isFunction(a.sizingStringFunc)?a.sizingStringFunc.call(V,t,d,n,r):$.jgrid.getTextWidth(t,d),a._maxsize)),t},J=function(e,t,i,r,o,n){t=u(e,t,i,o,"add");return'<td role="gridcell" '+p(i,r,t,o,e,n)+">"+t+"</td>"},Z=function(e,t,i,r,o){r='<input role="checkbox" type="checkbox" id="jqg_'+V.p.id+"_"+e+'" '+o+' name="jqg_'+V.p.id+"_"+e+'"'+(r?'checked="checked"':"")+"/>";return'<td role="gridcell" '+p(t,i,"",null,e,!0)+">"+r+"</td>"},ee=function(e,t,i,r,o){r=(parseInt(i,10)-1)*parseInt(r,10)+1+t;return'<td role="gridcell" '+o+" "+p(e,t,r,null,t,!0)+">"+r+"</td>"},te=function(e){for(var
t,i=[],r=0,o=0;o<V.p.colModel.length;o++)"cb"!==(t=V.p.colModel[o]).name&&"subgrid"!==t.name&&"rn"!==t.name&&(i[r]="local"===e?t.name:"xml"===e||"xmlstring"===e?t.xmlmap||t.name:t.jsonmap||t.name,!1!==V.p.keyName&&!0===t.key&&(V.p.keyName=i[r],V.p.keyIndex=r),r++);return i},ie=function(t){var
e=V.p.remapColumns;return e&&e.length||(e=$.map(V.p.colModel,function(e,t){return t})),t&&(e=$.map(e,function(e){return e<t?null:e-t})),e},re=function(e,t){var
i;this.p.deepempty?$(this.rows).slice(1).remove():(i=0<this.rows.length?this.rows[0]:null,$(this.firstChild).empty().append(i)),e&&this.p.scroll&&($(this.grid.bDiv.firstChild).css({height:"auto"}),$(this.grid.bDiv.firstChild.firstChild).css({height:"0px",display:"none"}),0!==this.grid.bDiv.scrollTop&&(this.grid.bDiv.scrollTop=0)),!0===t&&(this.p.data=[],this.p._index={})},g=function(){var
e,t,i,r,o,n,a,s,l,d,p,c=V.p,u=c.data,g=u.length,h=c.localReader,f=c.colModel,m=h.cell,v=(!0===c.multiselect?1:0)+(!0===c.subGrid?1:0)+(!0===c.rownumbers?1:0),w=c.scroll?$.jgrid.randId():1;if("local"===c.datatype&&!0===h.repeatitems)for(l=ie(v),d=te("local"),r=!1===c.keyName?$.jgrid.isFunction(h.id)?h.id.call(V,u):h.id:c.keyName,e=0;e<g;e++){for(i=u[e],void
0===(o=$.jgrid.getAccessor(i,r))&&("number"==typeof
r&&null!=f[r+v]&&(o=$.jgrid.getAccessor(i,f[r+v].name)),void
0===o&&(o=w+e,m&&(o=null!=(n=$.jgrid.getAccessor(i,m)||i)&&void
0!==n[r]?n[r]:o,n=null))),(s={})[h.id]=o,m&&(i=$.jgrid.getAccessor(i,m)||i),p=Array.isArray(i)?l:d,t=0;t<p.length;t++)a=$.jgrid.getAccessor(i,p[t]),s[f[t+v].name]=a;u[e]=s}},oe=function(){var
e,t,i=V.p.data.length,r=!1!==V.p.keyName?V.p.keyName:r=V.p.localReader.id;for(V.p._index={},e=0;e<i;e++)void
0===(t=$.jgrid.getAccessor(V.p.data[e],r))&&(t=String(e+1)),V.p._index[t]=e},ne=function(e,t,i,r,o){var
n,a="-1",s="",t=t?"display:none;":"",l=$(V).triggerHandler("jqGridRowAttr",[r,o,e]);if("object"!=typeof
l&&(l=$.jgrid.isFunction(V.p.rowattr)?V.p.rowattr.call(V,r,o,e):"string"==typeof
V.p.rowattr&&null!=$.jgrid.rowattr&&$.jgrid.isFunction($.jgrid.rowattr[V.p.rowattr])?$.jgrid.rowattr[V.p.rowattr].call(V,r,o,e):{}),!$.isEmptyObject(l)){l.hasOwnProperty("id")&&(e=l.id,delete
l.id),l.hasOwnProperty("tabindex")&&(a=l.tabindex,delete
l.tabindex),l.hasOwnProperty("style")&&(t+=l.style,delete
l.style),l.hasOwnProperty("class")&&(i+=" "+l.class,delete
l.class);try{delete
l.role}catch(e){}for(n
in
l)l.hasOwnProperty(n)&&(s+=" "+n+"="+l[n])}return'<tr role="row" id="'+e+'" tabindex="'+a+'" class="'+i+'"'+(""===t?"":' style="'+t+'"')+s+">"},f=function(e,i,t,r){var
o=new
Date,n="local"!==V.p.datatype&&V.p.loadonce||"xmlstring"===V.p.datatype,a="_id_",s=V.p.xmlReader,l=[],d="local"===V.p.datatype?"local":"xml";if(n&&(V.p.data=[],V.p._index={},V.p.localReader.id=a),V.p.reccount=0,$.isXMLDoc(e)){i=-1!==V.p.treeANode||V.p.scroll?1<i?i:1:(re.call(V,!1,!1),1);var
p,c,u,g,h,f,m,v,w,j=$(V),b=0,_=!0===V.p.multiselect?1:0,y=0,D=!0===V.p.rownumbers?1:0,x=[],q={},C=[],I=K(Q,"rowBox",!0,"jqgrow ui-row-"+V.p.direction);!0===V.p.subGrid&&(y=1,g=$.jgrid.getMethod("addSubGridCell")),s.repeatitems||(x=te(d)),h=!1===V.p.keyName?$.jgrid.isFunction(s.id)?s.id.call(V,e):s.id:V.p.keyName,s.repeatitems&&V.p.keyName&&isNaN(h)&&(h=V.p.keyIndex),f=-1===String(h).indexOf("[")?x.length?function(e,t){return $(h,e).text()||t}:function(e,t){return $(s.cell,e).eq(h).text()||t}:function(e,t){return e.getAttribute(h.replace(/[\[\]]/g,""))||t},V.p.userData={},V.p.page=Y($.jgrid.getXmlData(e,s.page),V.p.page),V.p.lastpage=Y($.jgrid.getXmlData(e,s.total),1),V.p.records=Y($.jgrid.getXmlData(e,s.records)),$.jgrid.isFunction(s.userdata)?V.p.userData=s.userdata.call(V,e)||{}:$.jgrid.getXmlData(e,s.userdata,!0).each(function(){V.p.userData[this.getAttribute("name")]=$(this).text()});var
G,S,k=$.jgrid.getXmlData(e,s.root,!0),N=(k=(k=$.jgrid.getXmlData(k,s.row,!0))||[]).length,M=0,R=[],z=parseInt(V.p.rowNum,10),F=V.p.scroll?$.jgrid.randId():1,P=$(V).find("tbody").first(),A=!1;if(V.p.grouping&&(A=!0===V.p.groupingView.groupCollapse,G=$.jgrid.getMethod("groupingPrepare")),0<N&&V.p.page<=0&&(V.p.page=1),k&&N){r&&(z*=r+1);for(var
B=$.jgrid.isFunction(V.p.afterInsertRow),O=D?K(Q,"rownumBox",!1,"jqgrid-rownum"):"",T=_?K(Q,"multiBox",!1,"cbox"):"";M<N;){v=k[M],w=f(v,F+M),w=V.p.idPrefix+w,V.p.preserveSelection&&(V.p.multiselect?(S=-1!==V.p.selarrrow.indexOf(w),W=S?W+1:W):S=w===V.p.selrow);var
H=C.length;if(C.push(""),D&&C.push(ee(0,M,V.p.page,V.p.rowNum,O)),_&&C.push(Z(w,D,M,S,T)),y&&C.push(g.call(j,_+D,M+i)),s.repeatitems){m=m||ie(_+y+D);var
L=$.jgrid.getXmlData(v,s.cell,!0);$.each(m,function(e){var
t=L[this];if(!t)return!1;u=t.textContent||t.text||"",q[V.p.colModel[e+_+y+D].name]=u,C.push(J(w,u,e+_+y+D,M+i,v,q))})}else
for(p=0;p<x.length;p++)u=$.jgrid.getXmlData(v,x[p]),q[V.p.colModel[p+_+y+D].name]=u,C.push(J(w,u,p+_+y+D,M+i,v,q));if(C[H]=ne(w,A,I,q,v),C.push("</tr>"),V.p.grouping&&(R.push(C),V.p.groupingView._locgr||G.call(j,q,M),C=[]),(n||!0===V.p.treeGrid&&!V.p._ald)&&(q[a]=$.jgrid.stripPref(V.p.idPrefix,w),V.p.data.push(q),V.p._index[q[a]]=V.p.data.length-1,-1<V.p.treeANode&&"adjacency"===V.p.treeGridModel&&l.push(q)),!1===V.p.gridview&&(P.append(C.join("")),j.triggerHandler("jqGridAfterInsertRow",[w,q,v]),B&&V.p.afterInsertRow.call(V,w,q,v),C=[]),q={},M++,++b===z)break}}if(W=V.p.multiselect&&V.p.preserveSelection&&b===W,!0===V.p.gridview&&(c=-1<V.p.treeANode?V.p.treeANode:0,V.p.grouping?n||(j.jqGrid("groupingRender",R,V.p.colModel.length,V.p.page,z),R=null):!0===V.p.treeGrid&&0<c?$(V.rows[c]).after(C.join("")):(P.append(C.join("")),V.grid.cols=V.rows[0].cells)),V.p.totaltime=new
Date-o,C=null,0<b&&0===V.p.records&&(V.p.records=N),!0===V.p.treeGrid){try{j.jqGrid("setTreeNode",c+1,b+c+1)}catch(e){}if(-1<V.p.treeANode&&"adjacency"===V.p.treeGridModel&&(u=V.rows[V.p.treeANode].id,1<=(u=V.p._index[u]+1))){for(V.p.data.splice(-N,N),p=0;p<N;p++)V.p.data.splice(u+p,0,l[p]);oe()}}if(V.p.reccount=b,V.p.treeANode=-1,V.p.userDataOnFooter&&j.jqGrid("footerData","set",V.p.userData,V.p.formatFooterData),V.p.userDataOnHeader&&j.jqGrid("headerData","set",V.p.userData,V.p.formatHeaderData),n&&(V.p.records=N,V.p.lastpage=Math.ceil(N/z)),t||V.updatepager(!1,!0),W&&ae(!0),n){for(;b<N;){if(v=k[b],w=f(v,b+F),w=V.p.idPrefix+w,s.repeatitems){m=m||ie(_+y+D);var
U=$.jgrid.getXmlData(v,s.cell,!0);$.each(m,function(e){var
t=U[this];if(!t)return!1;u=t.textContent||t.text||"",q[V.p.colModel[e+_+y+D].name]=u})}else
for(p=0;p<x.length;p++)u=$.jgrid.getXmlData(v,x[p]),q[V.p.colModel[p+_+y+D].name]=u;q[a]=$.jgrid.stripPref(V.p.idPrefix,w),V.p.grouping&&G.call(j,q,b),V.p.data.push(q),V.p._index[q[a]]=V.p.data.length-1,q={},b++}V.p.grouping&&(V.p.groupingView._locgr=!0,j.jqGrid("groupingRender",R,V.p.colModel.length,V.p.page,z),R=null)}if(!0===V.p.subGrid)try{j.jqGrid("addSubGrid",_+D)}catch(e){}}},m=function(e,t,i,r){var
o,n,a=new
Date;if(e){t=-1!==V.p.treeANode||V.p.scroll?1<t?t:1:(re.call(V,!1,!1),1),n="local"===V.p.datatype?(o=V.p.localReader,"local"):(o=V.p.jsonReader,"json");var
s,l,d,p,c,u,g,h,f,m,v,w,j="_id_",b="local"!==V.p.datatype&&V.p.loadonce||"jsonstring"===V.p.datatype,_=$(V),y=0,D=[],x=V.p.multiselect?1:0,q=!0===V.p.subGrid?1:0,C=!0===V.p.rownumbers?1:0,I=V.p.scroll&&"local"!==V.p.datatype?$.jgrid.randId():1,G=parseInt(V.p.rowNum,10),S=!1,k=ie(x+q+C),N=te(n),M={},R=[],z=[],F=K(Q,"rowBox",!0,"jqgrow ui-row-"+V.p.direction),P=$.jgrid.isFunction(V.p.afterInsertRow),A=[],B=!1,O=$(V).find("tbody").first(),T=C?K(Q,"rownumBox",!1,"jqgrid-rownum"):"",H=x?K(Q,"multiBox",!1,"cbox"):"";for(b&&(V.p.data=[],V.p._index={},V.p.localReader.id=j),V.p.reccount=0,V.p.page=Y($.jgrid.getAccessor(e,o.page),V.p.page),V.p.lastpage=Y($.jgrid.getAccessor(e,o.total),1),V.p.records=Y($.jgrid.getAccessor(e,o.records)),V.p.userData=$.jgrid.getAccessor(e,o.userdata)||{},q&&(p=$.jgrid.getMethod("addSubGridCell")),f=!1===V.p.keyName?$.jgrid.isFunction(o.id)?o.id.call(V,e):o.id:V.p.keyName,o.repeatitems&&V.p.keyName&&isNaN(f)&&(f=V.p.keyIndex),null==(h=$.jgrid.getAccessor(e,o.root))&&Array.isArray(e)&&(h=e),(l=0)<(g=(h=h||[]).length)&&V.p.page<=0&&(V.p.page=1),r&&(G*=r+1),"local"!==V.p.datatype||V.p.deselectAfterSort||(S=!0),V.p.grouping&&(B=!0===V.p.groupingView.groupCollapse,v=$.jgrid.getMethod("groupingPrepare"));l<g;){var
L,U=h[l];void
0===(L=$.jgrid.getAccessor(U,f))&&("number"==typeof
f&&null!=V.p.colModel[f+x+q+C]&&(L=$.jgrid.getAccessor(U,V.p.colModel[f+x+q+C].name)),void
0===L&&(L=I+l,0===D.length&&o.cell&&(L=null!=(E=$.jgrid.getAccessor(U,o.cell)||U)&&void
0!==E[f]?E[f]:L,E=null))),L=V.p.idPrefix+L,(S||V.p.preserveSelection)&&(V.p.multiselect?(c=-1!==V.p.selarrrow.indexOf(L),W=c?W+1:W):c=L===V.p.selrow);var
E=R.length;for(R.push(""),C&&R.push(ee(0,l,V.p.page,V.p.rowNum,T)),x&&R.push(Z(L,C,l,c,H)),q&&R.push(p.call(_,x+C,l+t)),u=N,o.repeatitems&&(o.cell&&(U=$.jgrid.getAccessor(U,o.cell)||U),Array.isArray(U)&&(u=k)),d=0;d<u.length;d++)s=$.jgrid.getAccessor(U,u[d]),M[V.p.colModel[d+x+q+C].name]=s,R.push(J(L,s,d+x+q+C,l+t,U,M));if(R[E]=ne(L,B,c?F+" "+X:F,M,U),R.push("</tr>"),V.p.grouping&&(A.push(R),V.p.groupingView._locgr||v.call(_,M,l),R=[]),(b||!0===V.p.treeGrid&&!V.p._ald)&&(M[j]=$.jgrid.stripPref(V.p.idPrefix,L),V.p.data.push(M),V.p._index[M[j]]=V.p.data.length-1,-1<V.p.treeANode&&"adjacency"===V.p.treeGridModel&&z.push(M)),!1===V.p.gridview&&(O.append(R.join("")),_.triggerHandler("jqGridAfterInsertRow",[L,M,U]),P&&V.p.afterInsertRow.call(V,L,M,U),R=[]),M={},l++,++y===G)break}if(W=V.p.multiselect&&(V.p.preserveSelection||S)&&y===W,!0===V.p.gridview&&(m=-1<V.p.treeANode?V.p.treeANode:0,V.p.grouping?b||(_.jqGrid("groupingRender",A,V.p.colModel.length,V.p.page,G),A=null):!0===V.p.treeGrid&&0<m?$(V.rows[m]).after(R.join("")):(O.append(R.join("")),V.grid.cols=V.rows[0].cells)),V.p.totaltime=new
Date-a,R=null,0<y&&0===V.p.records&&(V.p.records=g),!0===V.p.treeGrid){try{_.jqGrid("setTreeNode",m+1,y+m+1)}catch(e){}if(-1<V.p.treeANode&&"adjacency"===V.p.treeGridModel&&(s=V.rows[V.p.treeANode].id,1<=(s=V.p._index[s]+1))){for(V.p.data.splice(-g,g),l=0;l<g;l++)V.p.data.splice(s+l,0,z[l]);oe()}}if(V.p.reccount=y,V.p.treeANode=-1,V.p.userDataOnFooter&&_.jqGrid("footerData","set",V.p.userData,V.p.formatFooterData),V.p.userDataOnHeader&&_.jqGrid("headerData","set",V.p.userData,V.p.formatHeaderData),b&&(V.p.records=g,V.p.lastpage=Math.ceil(g/G)),i||V.updatepager(!1,!0),W&&ae(!0),b){for(;y<g&&h[y];){if(U=h[y],void
0===(L=$.jgrid.getAccessor(U,f))&&("number"==typeof
f&&null!=V.p.colModel[f+x+q+C]&&(L=$.jgrid.getAccessor(U,V.p.colModel[f+x+q+C].name)),void
0===L&&(L=I+y,0===D.length&&o.cell&&(L=null!=(w=$.jgrid.getAccessor(U,o.cell)||U)&&void
0!==w[f]?w[f]:L,w=null))),U){for(L=V.p.idPrefix+L,u=N,o.repeatitems&&(o.cell&&(U=$.jgrid.getAccessor(U,o.cell)||U),Array.isArray(U)&&(u=k)),d=0;d<u.length;d++)M[V.p.colModel[d+x+q+C].name]=$.jgrid.getAccessor(U,u[d]);M[j]=$.jgrid.stripPref(V.p.idPrefix,L),V.p.grouping&&v.call(_,M,y),V.p.data.push(M),V.p._index[M[j]]=V.p.data.length-1,M={}}y++}V.p.grouping&&(V.p.groupingView._locgr=!0,_.jqGrid("groupingRender",A,V.p.colModel.length,V.p.page,G),A=null)}if(!0===V.p.subGrid)try{_.jqGrid("addSubGrid",x+C)}catch(e){}}},j=function(e){var
t,i,r,o,n=V.p.multiSort?[]:"",a=[],s=!1,p={},l=[],d=[];if(Array.isArray(V.p.data)){var
c,u,g,h=!!V.p.grouping&&V.p.groupingView;if($.each(V.p.colModel,function(){if("cb"===this.name||"subgrid"===this.name||"rn"===this.name)return!0;if(i=this.sorttype||"text",g=this.index||this.name,"date"===i||"datetime"===i?(this.formatter&&"string"==typeof
this.formatter&&"date"===this.formatter?(t=this.formatoptions&&this.formatoptions.srcformat?this.formatoptions.srcformat:$.jgrid.getRegional(V,"formatter.date.srcformat"),r=this.formatoptions&&this.formatoptions.newformat?this.formatoptions.newformat:$.jgrid.getRegional(V,"formatter.date.newformat")):t=r=this.datefmt||"Y-m-d",p[g]={stype:i,srcfmt:t,newfmt:r,sfunc:this.sortfunc||null,name:this.name}):p[g]={stype:i,srcfmt:"",newfmt:"",sfunc:this.sortfunc||null,name:this.name},V.p.grouping)for(u=0,c=h.groupField.length;u<c;u++)this.name===h.groupField[u]&&(l[u]=p[g],d[u]=g);V.p.multiSort||s||g!==V.p.sortname||(n=g,s=!0)}),V.p.multiSort&&(n=I,a=G),!V.p.treeGrid||!V.p._sort){var
f={eq:function(e){return e.equals},ne:function(e){return e.notEquals},lt:function(e){return e.less},le:function(e){return e.lessOrEquals},gt:function(e){return e.greater},ge:function(e){return e.greaterOrEquals},cn:function(e){return e.contains},nc:function(e,t){return("OR"===t?e.orNot():e.andNot()).contains},bw:function(e){return e.startsWith},bn:function(e,t){return("OR"===t?e.orNot():e.andNot()).startsWith},en:function(e,t){return("OR"===t?e.orNot():e.andNot()).endsWith},ew:function(e){return e.endsWith},ni:function(e,t){return("OR"===t?e.orNot():e.andNot()).inData},in:function(e){return e.inData},nu:function(e){return e.isNull},nn:function(e,t){return("OR"===t?e.orNot():e.andNot()).isNull}},m=$.jgrid.from.call(V,V.p.data);if(V.p.ignoreCase&&(m=m.ignoreCase()),!0===V.p.search){var
v=V.p.postData.filters;if(v)"string"==typeof
v&&(v=$.jgrid.parse(v)),function
e(t){var
i,r,o,n,a,s,l,d=0;if(null!=t.groups){for((r=t.groups.length&&"OR"===t.groupOp.toString().toUpperCase())&&m.orBegin(),i=0;i<t.groups.length;i++){0<d&&r&&m.or();try{e(t.groups[i])}catch(e){alert(e)}d++}r&&m.orEnd()}if(null!=t.rules)try{for((l=t.rules.length&&"OR"===t.groupOp.toString().toUpperCase())&&m.orBegin(),i=0;i<t.rules.length;i++){if(n=t.rules[i],o=t.groupOp.toString().toUpperCase(),f[n.op]&&n.field){0<d&&o&&"OR"===o&&(m=m.or()),s=n.field,V.p.useNameForSearch&&p.hasOwnProperty(n.field)&&(s=p[n.field].name);try{"date"===(a=p[n.field]).stype&&"string"==typeof
a.srcfmt&&"string"==typeof
a.newfmt&&(n.data=$.jgrid.parseDate.call(V,a.newfmt,n.data,a.srcfmt)),m=f[n.op](m,o)(s,n.data,a)}catch(e){}}else
void
0!==V.p.customFilterDef&&void
0!==V.p.customFilterDef[n.op]&&$.jgrid.isFunction(V.p.customFilterDef[n.op].action)&&(m=m.user.call(V,n.op,n.field,n.data));d++}l&&m.orEnd()}catch(e){alert(e)}}(v);else
try{"date"===(o=p[V.p.postData.searchField]).stype&&o.srcfmt&&o.newfmt&&o.srcfmt!==o.newfmt&&(V.p.postData.searchString=$.jgrid.parseDate.call(V,o.newfmt,V.p.postData.searchString,o.srcfmt)),f[V.p.postData.searchOper]?m=f[V.p.postData.searchOper](m)(V.p.postData.searchField,V.p.postData.searchString,p[V.p.postData.searchField]):void
0!==V.p.customFilterDef&&void
0!==V.p.customFilterDef[V.p.postData.searchOper]&&$.jgrid.isFunction(V.p.customFilterDef[V.p.postData.searchOper].action)&&(m=m.user.call(V,V.p.postData.searchOper,V.p.postData.searchField,V.p.postData.searchString))}catch(e){}}if(V.p.treeGrid&&"nested"===V.p.treeGridModel&&m.orderBy(V.p.treeReader.left_field,"asc","integer","",null),V.p.treeGrid&&"adjacency"===V.p.treeGridModel&&(c=0,n=null),V.p.grouping)for(u=0;u<c;u++)m.orderBy(d[u],h.groupOrder[u],l[u].stype,l[u].srcfmt);V.p.multiSort?$.each(n,function(e){m.orderBy(this,a[e],p[this].stype,p[this].srcfmt,p[this].sfunc)}):n&&V.p.sortorder&&s&&("DESC"===V.p.sortorder.toUpperCase()?m.orderBy(V.p.sortname,"d",p[n].stype,p[n].srcfmt,p[n].sfunc):m.orderBy(V.p.sortname,"a",p[n].stype,p[n].srcfmt,p[n].sfunc));var
w=m.select(),j=parseInt(V.p.rowNum,10),b=w.length,_=parseInt(V.p.page,10),y=Math.ceil(b/j),v={};if((V.p.search||V.p.resetsearch)&&V.p.grouping&&V.p.groupingView._locgr){V.p.groupingView.groups=[];var
D,x,q,C=$.jgrid.getMethod("groupingPrepare");if(V.p.footerrow&&V.p.userDataOnFooter){for(x
in
V.p.userData)V.p.userData.hasOwnProperty(x)&&(V.p.userData[x]=0);q=!0}for(D=0;D<b;D++){if(q)for(x
in
V.p.userData)V.p.userData.hasOwnProperty(x)&&(V.p.userData[x]+=parseFloat(w[D][x]||0));C.call($(V),w[D],D,j)}}return e?w:(w=V.p.treeGrid&&V.p.search?$(V).jqGrid("searchTree",w):w.slice((_-1)*j,_*j),p=m=null,v[V.p.localReader.total]=y,v[V.p.localReader.page]=_,v[V.p.localReader.records]=b,v[V.p.localReader.root]=w,v[V.p.localReader.userdata]=V.p.userData,w=null,v)}$(V).jqGrid("SortTree",n,V.p.sortorder,p[n].stype||"text",p[n].srcfmt||"")}},x=function(){V.grid.hDiv.loading=!0,V.p.hiddengrid||$(V).jqGrid("progressBar",{method:"show",loadtype:V.p.loadui,htmlcontent:$.jgrid.getRegional(V,"defaults.loadtext",V.p.loadtext)})},q=function(){V.grid.hDiv.loading=!1,$(V).jqGrid("progressBar",{method:"hide",loadtype:V.p.loadui})},C=function(e,t,i){var
r=void
0===(r=$(V).triggerHandler("jqGridBeforeProcessing",[e,t,i]))||"boolean"!=typeof
r||r;return $.jgrid.isFunction(V.p.beforeProcessing)&&!1===V.p.beforeProcessing.call(V,e,t,i)&&(r=!1),r},S=function(e,t){$(V).triggerHandler("jqGridLoadComplete",[e]),t&&V.p.loadComplete.call(V,e),$(V).triggerHandler("jqGridAfterLoadComplete",[e]),V.p.datatype="local",V.p.datastr=null,q()},k=function(r){if(!V.grid.hDiv.loading){var
o,n=V.p.scroll&&!1===r,e={},t=V.p.prmNames;W=0,V.p.page<=0&&(V.p.page=Math.min(1,V.p.lastpage)),null!==t.search&&(e[t.search]=V.p.search),null!==t.nd&&(e[t.nd]=(new
Date).getTime()),null!==t.rows&&(e[t.rows]=V.p.rowNum),null!==t.page&&(e[t.page]=V.p.page),null!==t.sort&&(e[t.sort]=V.p.sortname),null!==t.order&&(e[t.order]=V.p.sortorder),null!==V.p.rowTotal&&null!==t.totalrows&&(e[t.totalrows]=V.p.rowTotal);var
i=$.jgrid.isFunction(V.p.loadComplete),a=i?V.p.loadComplete:null,s=0;if(1<(r=r||1)?null!==t.npage?(e[t.npage]=r,s=r-1,r=1):a=function(e){V.p.page++,V.grid.hDiv.loading=!1,i&&V.p.loadComplete.call(V,e),k(r-1)}:null!==t.npage&&delete
V.p.postData[t.npage],V.p.grouping){$(V).jqGrid("groupingSetup");for(var
l,d=V.p.groupingView,p=[],c=0;c<d.groupField.length;c++){var
u=d.groupField[c];$.each(V.p.colModel,function(e,t){t.name===u&&t.index&&(u=t.index)}),p.push(u+" "+d.groupOrder[c])}l=p.join(),""!==$.jgrid.trim(e[t.sort])?e[t.sort]=l+" ,"+e[t.sort]:(e[t.sort]=l,e[t.order]="")}$.extend(V.p.postData,e);var
g=V.p.scroll?V.rows.length-1:1;if($.jgrid.isFunction(V.p.datatype))V.p.datatype.call(V,V.p.postData,"load_"+V.p.id,g,r,s);else{e=$(V).triggerHandler("jqGridBeforeRequest");if(!1!==e&&"stop"!==e&&(!$.jgrid.isFunction(V.p.beforeRequest)||!1!==(e=V.p.beforeRequest.call(V))&&"stop"!==e)){switch($.jgrid.isFunction(V.treeGrid_beforeRequest)&&V.treeGrid_beforeRequest.call(V),o=V.p.datatype.toLowerCase()){case"json":case"jsonp":case"xml":case"script":$.ajax($.extend({url:V.p.url,type:V.p.mtype,dataType:o,data:$.jgrid.isFunction(V.p.serializeGridData)?V.p.serializeGridData.call(V,V.p.postData):V.p.postData,success:function(e,t,i){C(e,t,i)?(("xml"===o?f:m)(e,g,1<r,s),$(V).triggerHandler("jqGridLoadComplete",[e]),a&&a.call(V,e),$(V).triggerHandler("jqGridAfterLoadComplete",[e]),n&&V.grid.populateVisible(),V.p.treeGrid_bigData?V.p.loadonce&&(V.p.datatype="local"):(V.p.loadonce||V.p.treeGrid)&&(V.p.datatype="local"),e=null,1===r&&q(),$.jgrid.isFunction(V.treeGrid_afterLoadComplete)&&V.treeGrid_afterLoadComplete.call(V)):q()},error:function(e,t,i){$(V).triggerHandler("jqGridLoadError",[e,t,i]),$.jgrid.isFunction(V.p.loadError)&&V.p.loadError.call(V,e,t,i),1===r&&q(),e=null},beforeSend:function(e,t){var
i=!0,i=$(V).triggerHandler("jqGridLoadBeforeSend",[e,t]);if($.jgrid.isFunction(V.p.loadBeforeSend)&&(i=V.p.loadBeforeSend.call(V,e,t)),void
0===i&&(i=!0),!1===i)return!1;x()}},$.jgrid.ajaxOptions,V.p.ajaxGridOptions));break;case"xmlstring":if(x(),h="string"!=typeof
V.p.datastr?V.p.datastr:$.parseXML(V.p.datastr),!C(h,200,null))return void
q();f(h),S(h,i);break;case"jsonstring":if(x(),h="string"==typeof
V.p.datastr?$.jgrid.parse(V.p.datastr):V.p.datastr,!C(h,200,null))return void
q();m(h),S(h,i);break;case"local":case"clientside":x(),V.p.datatype="local",V.p._ald=!0;var
h=j(!1);if(!C(h,200,null))return void
q();m(h,g,1<r,s),$(V).triggerHandler("jqGridLoadComplete",[h]),a&&a.call(V,h),$(V).triggerHandler("jqGridAfterLoadComplete",[h]),n&&V.grid.populateVisible(),q(),V.p._ald=!1}V.p._sort=!1}}}},ae=function(e){$("#cb_"+$.jgrid.jqID(V.p.id),V.grid.hDiv)[V.p.useProp?"prop":"attr"]("checked",e),(V.p.frozenColumns?V.p.id+"_frozen":"")&&$("#cb_"+$.jgrid.jqID(V.p.id),V.grid.fhDiv)[V.p.useProp?"prop":"attr"]("checked",e)},N=function(e,s){function
l(e,t){var
i=$(V).triggerHandler("jqGridPaging",[e,t]);return"stop"!==i&&($.jgrid.isFunction(V.p.onPaging)&&(i=V.p.onPaging.call(V,e,t)),"stop"!==i&&(V.p.selrow=null,V.p.multiselect&&(V.p.preserveSelection||(V.p.selarrrow=[]),ae(!1)),V.p.savedRow=[]))}var
t,i,r,o,n,a,d,p,c,u="<td class='ui-pg-button "+w+"'><span class='ui-separator'></span></td>",g="",h="<table class='ui-pg-table ui-common-table ui-paging-pager'><tbody><tr>",f="";if(s+="_"+e,t="pg_"+e,c=e+"_left",d=e+"_center",p=e+"_right",$("#"+$.jgrid.jqID(e)).append("<div id='"+t+"' class='ui-pager-control' role='group'><table "+K(Q,"pagerTable",!1,"ui-pg-table ui-common-table ui-pager-table")+"><tbody><tr><td id='"+c+"' align='left'></td><td id='"+d+"' align='center' style='white-space:pre;'></td><td id='"+p+"' align='right'></td></tr></tbody></table></div>").attr("dir",v),0<V.p.rowList.length){var
m,f='<td dir="'+v+'">';for(f+="<select "+K(Q,"pgSelectBox",!1,"ui-pg-selbox")+' size="1" role="listbox" title="'+($.jgrid.getRegional(V,"defaults.pgrecs",V.p.pgrecs)||"")+'">',i=0;i<V.p.rowList.length;i++)1===(m=V.p.rowList[i].toString().split(":")).length&&(m[1]=m[0]),f+='<option role="option" value="'+m[0]+'"'+(Y(V.p.rowNum,0)===Y(m[0],0)?' selected="selected"':"")+">"+m[1]+"</option>";f+="</select></td>"}"rtl"===v&&(h+=f,$("#"+p).attr("align","left")),!0===V.p.pginput&&(g="<td id='input"+s+"' dir='"+v+"'>"+$.jgrid.template($.jgrid.getRegional(V,"defaults.pgtext",V.p.pgtext)||"","<input class='ui-pg-input' type='text' size='2' maxlength='7' value='0' role='textbox'/>","<span id='sp_1_"+$.jgrid.jqID(e)+"'></span>")+"</td>"),!0===V.p.pgbuttons?(o=["first"+s,"prev"+s,"next"+s,"last"+s],n=K(Q,"pgButtonBox",!0,"ui-pg-button"),c=[$.jgrid.getRegional(V,"defaults.pgfirst",V.p.pgfirst)||"",$.jgrid.getRegional(V,"defaults.pgprev",V.p.pgprev)||"",$.jgrid.getRegional(V,"defaults.pgnext",V.p.pgnext)||"",$.jgrid.getRegional(V,"defaults.pglast",V.p.pglast)||""],"rtl"===v&&(o.reverse(),c.reverse()),a="<td id='"+o[0]+"' class='"+n+"' title='"+c[0]+"'><span "+K(Q,"icon_first",!1,y)+"></span></td>",d="<td id='"+o[1]+"' class='"+n+"'  title='"+c[1]+"'><span "+K(Q,"icon_prev",!1,y)+"></span></td>",p="<td id='"+o[2]+"' class='"+n+"' title='"+c[2]+"'><span "+K(Q,"icon_next",!1,y)+"></span></td>",c="<td id='"+o[3]+"' class='"+n+"' title='"+c[3]+"'><span "+K(Q,"icon_end",!1,y)+"></span></td>",u=""!==g?u+g+u:"",h+="ltr"===v?a+d+u+p+c:c+p+u+d+a):""!==g&&(h+=g),"ltr"===v&&(h+=f),h+="</tr></tbody></table>",e=$.jgrid.jqID(e),t=$.jgrid.jqID(t),!0===V.p.viewrecords&&$("td#"+e+"_"+V.p.recordpos,"#"+t).append("<div dir='"+v+"' style='text-align:"+V.p.recordpos+"' class='ui-paging-info'></div>"),$("td#"+e+"_"+V.p.pagerpos,"#"+t).append(h),a=$("#gbox_"+$.jgrid.jqID(V.p.id)).css("font-size")||"11px",$("#gbox_"+$.jgrid.jqID(V.p.id)).append("<div id='testpg' "+K(Q,"entrieBox",!1,"ui-jqgrid")+" style='font-size:"+a+";visibility:hidden;' ></div>"),a=$(h).clone().appendTo("#testpg").width(),$("#testpg").remove(),0<a&&(""!==g&&(a+=50),r=a>$("td#"+e+"_"+V.p.pagerpos,"#"+t).innerWidth(),$("td#"+e+"_"+V.p.pagerpos,"#"+t).width(a)),V.p._nvtd=[],V.p._nvtd[0]=a?Math.floor((V.p.width-a)/2):Math.floor(V.p.width/3),V.p._nvtd[1]=0,h=null,$(".ui-pg-selbox","#"+t).on("change",function(){return l("records",this)&&(V.p.page=Math.round(V.p.rowNum*(V.p.page-1)/this.value-.5)+1,V.p.rowNum=this.value,V.p.pager&&$(".ui-pg-selbox",V.p.pager).val(this.value),V.p.toppager&&$(".ui-pg-selbox",V.p.toppager).val(this.value),k()),!1}),!0===V.p.pgbuttons&&($(".ui-pg-button","#"+t).hover(function(){$(this).hasClass(w)?this.style.cursor="default":($(this).addClass(_),this.style.cursor="pointer")},function(){$(this).hasClass(w)||($(this).removeClass(_),this.style.cursor="default")}),$("#first"+$.jgrid.jqID(s)+", #prev"+$.jgrid.jqID(s)+", #next"+$.jgrid.jqID(s)+", #last"+$.jgrid.jqID(s)).click(function(){if($(this).hasClass(w))return!1;var
e=Y(V.p.page,1),t=Y(V.p.lastpage,1),i=!1,r=!0,o=!0,n=!0,a=!0;return 0===t||1===t?a=n=o=r=!1:1<t&&1<=e?1===e?o=r=!1:e===t&&(a=n=!1):1<t&&0===e&&(a=n=!1,e=t-1),l(this.id.split("_")[0],this)&&(this.id==="first"+s&&r&&(V.p.page=1,i=!0),this.id==="prev"+s&&o&&(V.p.page=e-1,i=!0),this.id==="next"+s&&n&&(V.p.page=e+1,i=!0),this.id==="last"+s&&a&&(V.p.page=t,i=!0),i&&k(),$.jgrid.setSelNavIndex(V,this)),!1})),!0===V.p.pginput&&$("#"+t).on("keypress","input.ui-pg-input",function(e){return 13===(e.charCode||e.keyCode||0)?(l("user",this)&&($(this).val(Y($(this).val(),1)),V.p.page=0<$(this).val()?$(this).val():V.p.page,k()),!1):this}),r&&V.p.responsive&&!V.p.forcePgButtons&&($("#"+o[0]+",#"+o[3]+",#input"+$.jgrid.jqID(s)).hide(),$(".ui-paging-info","td#"+e+"_"+V.p.recordpos).hide(),$(".ui-pg-selbox","td#"+e+"_"+V.p.pagerpos).hide())},M=function(e,t,i,r,o){if(V.p.colModel[t].sortable&&!(0<V.p.savedRow.length)){if(i||(V.p.lastsort===t&&""!==V.p.sortname?"asc"===V.p.sortorder?V.p.sortorder="desc":"desc"===V.p.sortorder&&(V.p.sortorder="asc"):V.p.sortorder=V.p.colModel[t].firstsortorder||"asc",V.p.page=1),V.p.multiSort)!function(e,t,i){var
r=V.p.colModel,o=V.p.frozenColumns?t:V.grid.headers[e].el,n="";$("span.ui-grid-ico-sort",o).addClass(w),$(o).attr({"aria-selected":"false","aria-sort":"none"}),t=r[e].index||r[e].name,void
0===i?r[e].lso?"asc"===r[e].lso?(r[e].lso+="-desc",n="desc"):"desc"===r[e].lso?(r[e].lso+="-asc",n="asc"):"asc-desc"!==r[e].lso&&"desc-asc"!==r[e].lso||(r[e].lso=""):r[e].lso=n=r[e].firstsortorder||"asc":r[e].lso=n=i,n?($("span.s-ico",o).show(),$("span.ui-icon-"+n,o).removeClass(w),$(o).attr({"aria-selected":"true","aria-sort":n+"ending"})):V.p.viewsortcols[0]||$("span.s-ico",o).hide(),-1===(o=I.indexOf(t))?(I.push(t),G.push(n)):n?G[o]=n:(G.splice(o,1),I.splice(o,1)),V.p.sortorder="",V.p.sortname="";for(var
a=0,s=I.length;a<s;a++)0<a&&(V.p.sortname+=", "),V.p.sortname+=I[a],a!==s-1&&(V.p.sortname+=" "+G[a]);V.p.sortorder=G[s-1]}(t,o,r);else{if(r){if(V.p.lastsort===t&&V.p.sortorder===r&&!i)return;V.p.sortorder=r}var
o=V.grid.headers[V.p.lastsort]?V.grid.headers[V.p.lastsort].el:null,n=V.grid.headers[t].el,i="single"===V.p.viewsortcols[1],r=$(o).find("span.ui-grid-ico-sort");r.addClass(w),i&&$(r).css("display","none"),$(o).attr({"aria-selected":"false","aria-sort":"none"}),V.p.frozenColumns&&((r=V.grid.fhDiv.find("span.ui-grid-ico-sort")).addClass(w),i&&r.css("display","none"),V.grid.fhDiv.find("th").attr({"aria-selected":"false","aria-sort":"none"})),(r=$(n).find("span.ui-icon-"+V.p.sortorder)).removeClass(w),i&&r.css("display",""),$(n).attr({"aria-selected":"true","aria-sort":V.p.sortorder+"ending"}),V.p.viewsortcols[0]||(V.p.lastsort!==t?(V.p.frozenColumns&&V.grid.fhDiv.find("span.s-ico").hide(),$("span.s-ico",o).hide(),$("span.s-ico",n).show()):""===V.p.sortname&&$("span.s-ico",n).show()),e=e.substring(5+V.p.id.length+1),V.p.sortname=V.p.colModel[t].index||e}"stop"===$(V).triggerHandler("jqGridSortCol",[V.p.sortname,t,V.p.sortorder])||$.jgrid.isFunction(V.p.onSortCol)&&"stop"===V.p.onSortCol.call(V,V.p.sortname,t,V.p.sortorder)?V.p.lastsort=t:(ae(!1),"local"===V.p.datatype?V.p.deselectAfterSort&&!V.p.preserveSelection&&$(V).jqGrid("resetSelection"):(V.p.selrow=null,V.p.multiselect&&(V.p.preserveSelection||(V.p.selarrrow=[])),V.p.savedRow=[]),V.p.scroll&&(n=V.grid.bDiv.scrollLeft,re.call(V,!0,!1),V.grid.hDiv.scrollLeft=n),V.p.subGrid&&"local"===V.p.datatype&&$("td.sgexpanded","#"+$.jgrid.jqID(V.p.id)).each(function(){$(this).trigger("click")}),V.p._sort=!0,k(),V.p.lastsort=t,V.p.sortname!==e&&t&&(V.p.lastsort=t))}},R=function(e){for(var
t=V.grid.headers,i=$.jgrid.getCellIndex(e),r=0;r<t.length;r++)if(e===t[r].el){i=r;break}return i},z=function(){var
e,t='{"groupOp":"AND","rules":[], "groups" : [',i=0;for(e
in
V.p.colFilters)if(V.p.colFilters.hasOwnProperty(e)){var
r=V.p.colFilters[e];if(!$.isEmptyObject(r)){if(""===r.value1&&""===r.value2)break;0<i&&(t+=","),t+='{"groupOp": "'+r.rule+'", "rules" : [',t+='{"field":"'+e+'",',t+='"op":"'+r.oper1+'",',r.value1+="",t+='"data":"'+r.value1.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}',r.value2&&(t+=',{"field":"'+e+'",',t+='"op":"'+r.oper2+'",',r.value2+="",t+='"data":"'+r.value2.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}'),t+="]}",i++}}return t+="]}",0===i&&(t=""),t},F=function(w,e,t){var
i=$(h.hDiv).height();$(".ui-search-toolbar",h.hDiv)[0]&&!isNaN($(".ui-search-toolbar",h.hDiv).height())&&(i-=$(".ui-search-toolbar",h.hDiv).height()),$(h.cDiv).is(":hidden")||(i+=$(h.cDiv).outerHeight()),V.p.toolbar[1]&&"bottom"!==V.p.toolbar[2]&&null!==$(h.uDiv)&&(i+=$(h.uDiv).outerHeight()),V.p.toppager&&(i+=$("#"+$.jgrid.jqID(V.p.id)+"_toppager").outerHeight()),e=parseInt(e,10),t=i;var
r,o,n,a,s,l,d="",p=V.p.colModel[w],j=$.extend({sorting:!0,columns:!0,filtering:!0,seraching:!0,grouping:!0,freeze:!0},p.coloptions),c=$.jgrid.getRegional(V,"colmenu"),i=V.p.colNames[w],u=[],g=$.jgrid.trim(p.name);for(a
in
u.push(d),p.sortable&&j.sorting&&(d='<li class="ui-menu-item" role="presentation"><a class="g-menu-item" tabindex="0" role="menuitem" data-value="sortasc"><table class="ui-common-table"><tr><td class="menu_icon"><span class="'+y+" "+D.icon_sort_asc+'"></span></td><td class="menu_text">'+c.sortasc+"</td></tr></table></a></li>",d+='<li class="ui-menu-item" role="presentation"><a class="g-menu-item" tabindex="0" role="menuitem" data-value="sortdesc"><table class="ui-common-table"><tr><td class="menu_icon"><span class="'+y+" "+D.icon_sort_desc+'"></span></td><td class="menu_text">'+c.sortdesc+"</td></tr></table></a></li>",u.push(d)),j.columns&&(d='<li class="ui-menu-item divider" role="separator"></li>',d+='<li class="ui-menu-item" role="presentation"><a class="g-menu-item" tabindex="0" role="menuitem" data-value="columns"><table class="ui-common-table"><tr><td class="menu_icon"><span class="'+y+" "+D.icon_columns+'"></span></td><td class="menu_text">'+c.columns+"</td></tr></table></a></li>",u.push(d)),j.filtering&&(d='<li class="ui-menu-item divider" role="separator"></li>',d+='<li class="ui-menu-item" role="presentation"><a class="g-menu-item" tabindex="0" role="menuitem" data-value="filtering"><table class="ui-common-table"><tr><td class="menu_icon"><span class="'+y+" "+D.icon_filter+'"></span></td><td class="menu_text">'+c.filter+" "+i+"</td></tr></table></a></li>",u.push(d)),j.grouping&&(o=$.inArray(p.name,V.p.groupingView.groupField),d='<li class="ui-menu-item divider" role="separator"></li>',d+='<li class="ui-menu-item" role="presentation"><a class="g-menu-item" tabindex="0" role="menuitem" data-value="grouping"><table class="ui-common-table"><tr><td class="menu_icon"><span class="'+y+" "+D.icon_group+'"></span></td><td class="menu_text">'+(-1!==o?c.ungrouping:c.grouping+" "+i)+"</td></tr></table></a></li>",u.push(d)),j.freeze&&(V.p.subGrid||V.p.treeGrid||V.p.cellEdit||(n=!p.frozen||!V.p.frozenColumns,d='<li class="ui-menu-item divider" role="separator"></li>',d+='<li class="ui-menu-item" role="presentation"><a class="g-menu-item" tabindex="0" role="menuitem" data-value="freeze"><table class="ui-common-table"><tr><td class="menu_icon"><span class="'+y+" "+D.icon_freeze+'"></span></td><td class="menu_text">'+(n?c.freeze+" "+i:c.unfreeze)+"</td></tr></table></a></li>",u.push(d))),V.p.colMenuCustom)V.p.colMenuCustom.hasOwnProperty(a)&&(l=(s=V.p.colMenuCustom[a]).exclude.split(","),l=$.map(l,function(e){return $.jgrid.trim(e)}),(s.colname===g||"_all_"===s.colname&&-1===$.inArray(g,l))&&(r='<li class="ui-menu-item divider" role="separator"></li>',d='<li class="ui-menu-item" role="presentation"><a class="g-menu-item" tabindex="0" role="menuitem" data-value="'+s.id+'"><table class="ui-common-table"><tr><td class="menu_icon"><span class="'+y+" "+s.icon+'"></span></td><td class="menu_text">'+s.title+"</td></tr></table></a></li>","last"===s.position?(u.push(r),u.push(d)):"first"===s.position&&(u.unshift(r),u.unshift(d))));u.unshift('<ul id="column_menu" role="menu" tabindex="0">'),u.push("</ul>"),$("#gbox_"+V.p.id).append(u.join("")),$("#column_menu").addClass("ui-search-menu modal-content column-menu jqgrid-column-menu ui-menu "+D.menu_widget).css({left:e,top:t}),"ltr"===V.p.direction&&(t=$("#column_menu").width()+26,$("#column_menu").css("left",e-t+"px")),$("#column_menu > li > a").hover(function(){var
e,t,i,r,o,n,a,s,l,d,p,c,u,g,h,f,m,v;$("#col_menu").remove(),$("#search_menu").remove(),"columns"===$(this).attr("data-value")&&(n=$(this).parent().width()+8,$(this).parent().position().top,function(e,t,i){var
r,o=V.p.colModel,n=o.length,a=[],s=!0,l=[],d=$.jgrid.getRegional(V,"colmenu"),p='<ul id="col_menu" class="ui-search-menu  ui-col-menu modal-content" role="menu" tabindex="0" style="left:'+e+'px;">';for(i.columns_selectAll&&(p+='<li class="ui-menu-item disabled" role="presentation" draggable="false"><a class="g-menu-item" tabindex="0" role="menuitem" ><table class="ui-common-table" ><tr><td class="menu_icon" title="'+d.reorder+'"><span class="'+y+" "+D.icon_move+' notclick" style="visibility:hidden"></span></td><td class="menu_icon"><input id="chk_all" class="'+D.input_checkbox+'" type="checkbox" name="check_all"></td><td class="menu_text">Check/Uncheck</td></tr></table></a></li>'),r=0;r<n;r++){var
c,u=o[r].hidden?"":"checked",g=o[r].name,h=V.p.colNames[r];p+="<li "+(c="cb"===g||"subgrid"===g||"rn"===g||o[r].hidedlg?"style='display:none'":"")+' class="ui-menu-item" role="presentation" draggable="true"><a class="g-menu-item" tabindex="0" role="menuitem" ><table class="ui-common-table" ><tr><td class="menu_icon" title="'+d.reorder+'"><span class="'+y+" "+D.icon_move+' notclick"></span></td><td class="menu_icon"><input class="'+D.input_checkbox+' chk_selected" type="checkbox" name="'+g+'" '+u+'></td><td class="menu_text">'+h+"</td></tr></table></a></li>",a.push(r),c||l.push(g),s&&!u&&(s=!1)}p+="</ul>",$(t).append(p),$("#col_menu").addClass("ui-menu "+D.menu_widget),$("#chk_all","#col_menu").prop("checked",s),$.jgrid.isElementInViewport($("#col_menu")[0])||$("#col_menu").css("left",-parseInt($("#column_menu").innerWidth(),10)+"px"),$.fn.html5sortable()&&$("#col_menu").html5sortable({handle:"span",items:":not(.disabled)",forcePlaceholderSize:!0}).on("sortupdate",function(e,t){for(a.splice(t.startindex,1),a.splice(t.endindex,0,t.startindex),$(V).jqGrid("destroyFrozenColumns"),$(V).jqGrid("remapColumns",a,!0),$(V).triggerHandler("jqGridColMenuColumnDone",[a,null,null]),$.jgrid.isFunction(V.p.colMenuColumnDone)&&V.p.colMenuColumnDone.call(V,a,null,null),$(V).jqGrid("setFrozenColumns"),r=0;r<n;r++)a[r]=r}),$("#col_menu > li > a").on("click",function(e){var
t;$(e.target).hasClass("notclick")||($(e.target).is(":input")?t=$(e.target).is(":checked"):(t=!$("input",this).is(":checked"),$("input",this).prop("checked",t)),"check_all"===(e=$("input",this).attr("name"))?t?($("input","#col_menu").prop("checked",!0),$(V).jqGrid("showCol",l)):($("input","#col_menu").prop("checked",!1),$(V).jqGrid("hideCol",l)):($(V).triggerHandler("jqGridColMenuColumnDone",[a,e,t]),$.jgrid.isFunction(V.p.colMenuColumnDone)&&V.p.colMenuColumnDone.call(V,a,e,t),t?($(V).jqGrid("showCol",e),$(this).parent().attr("draggable","true")):($(V).jqGrid("hideCol",e),$(this).parent().attr("draggable","false")),i.columns_selectAll&&$("#chk_all","#col_menu").prop("checked",$(".chk_selected:checked","#col_menu").length===$(".chk_selected","#col_menu").length)))}).hover(function(){$(this).addClass(_)},function(){$(this).removeClass(_)})}(n,$(this).parent(),j)),"filtering"===$(this).attr("data-value")&&(n=$(this).parent().width()+8,$(this).parent().position().top,e=w,t=n,i=$(this).parent(),l=V.p.colModel[e],h=g=u=c=p=d="",f=$.jgrid.getRegional(V,"search"),m=$.jgrid.styleUI[V.p.styleUI||"jQueryUI"].common,v=$.jgrid.styleUI[b.styleUI||"jQueryUI"].modal,l&&((n=!(!V.p.colFilters||!V.p.colFilters[l.name])&&V.p.colFilters[l.name])&&!$.isEmptyObject(n)&&(d=n.oper1,p=n.value1,c=n.rule,u=n.oper2,g=n.value2),l.searchoptions||(l.searchoptions={}),r=l.searchoptions.sopt||("text"===l.sorttype?["eq","ne","bw","bn","ew","en","cn","nc","nu","nn","in","ni"]:["eq","ne","lt","le","gt","ge","nu","nn","in","ni"]),e=l.searchoptions.groupOps||f.groupOps,n=$("<form></form>"),a="<a id='bs_close' aria-label='Close'><span class='"+y+" "+v.icon_close+"'></span></a>",a+="<div>"+$.jgrid.getRegional(V,"colmenu.searchTitle")+"</div>",a+='<div><select size="1" id="oper1" class="'+D.filter_select+'">',$.each(f.odata,function(e,t){o=t.oper===d?'selected="selected"':"",-1!==$.inArray(t.oper,r)&&(h+='<option value="'+t.oper+'" '+o+">"+t.text+"</option>")}),a+=h,a+="</select></div>",n.append(a),s="",l.searchoptions.defaultValue&&(s=$.jgrid.isFunction(l.searchoptions.defaultValue)?l.searchoptions.defaultValue.call(V):l.searchoptions.defaultValue),p&&(s=p),v=$.extend(l.searchoptions,{name:l.index||l.name,id:"sval1_"+V.p.idPrefix+l.name,oper:"search"}),p=$.jgrid.createEl.call(V,l.stype,v,s,!1,$.extend({},$.jgrid.ajaxOptions,V.p.ajaxSelectOptions||{})),$(p).addClass(D.filter_input),a=$("<div></div>").append(p),n.append(a),a='<div><select size="1" id="operand" class="'+D.filter_select+'">',$.each(e,function(e,t){o=t.op===c?'selected="selected"':"",a+="<option value='"+t.op+"' "+o+">"+t.text+"</option>"}),a+="</select></div>",n.append(a),h="",$.each(f.odata,function(e,t){o=t.oper===u?'selected="selected"':"",-1!==$.inArray(t.oper,r)&&(h+='<option value="'+t.oper+'" '+o+">"+t.text+"</option>")}),a='<div><select size="1" id="oper2" class="'+D.filter_select+'">'+h+"</select></div>",n.append(a),s=g||"",v=$.extend(l.searchoptions,{name:l.index||l.name,id:"sval2_"+V.p.idPrefix+l.name,oper:"search"}),p=$.jgrid.createEl.call(V,l.stype,v,s,!1,$.extend({},$.jgrid.ajaxOptions,V.p.ajaxSelectOptions||{})),$(p).addClass(D.filter_input),a=$("<div></div>").append(p),n.append(a),a="<div>",a+="<div class='search_buttons'><a tabindex='0' id='bs_reset' class='fm-button "+m.button+" ui-reset'>"+f.Reset+"</a></div>",a+="<div class='search_buttons'><a tabindex='0' id='bs_search' class='fm-button "+m.button+" ui-search'>"+f.Find+"</a></div>",a+="</div>",n.append(a),n=$('<li class="ui-menu-item" role="presentation"></li>').append(n),n=$('<ul id="search_menu" class="ui-search-menu modal-content" role="menu" tabindex="0" style="left:'+t+'px;"></ul>').append(n),$(i).append(n),$("#search_menu").addClass("ui-menu "+D.menu_widget),$.jgrid.isElementInViewport($("#search_menu")[0])||$("#search_menu").css("left",-parseInt($("#column_menu").innerWidth(),10)+"px"),$("#bs_reset, #bs_search","#search_menu","#bs_close").hover(function(){$(this).addClass(_)},function(){$(this).removeClass(_)}),$("#bs_reset",n).on("click",function(e){V.p.colFilters[l.name]={},!0===V.p.mergeSearch&&V.p.searchModules.hasOwnProperty("colMenuSearch")&&!1!==V.p.searchModules.colMenuSearch?(V.p.searchModules.colMenuSearch=z(),$.extend(V.p.postData,{filters:$.jgrid.splitSearch(V.p.searchModules)}),V.p.search=!0):(V.p.postData.filters=z(),V.p.search=!1,V.p.resetsearch=!0),s="",l.searchoptions.defaultValue&&(s=$.jgrid.isFunction(l.searchoptions.defaultValue)?l.searchoptions.defaultValue.call(V):l.searchoptions.defaultValue),$("#sval1_"+V.p.idPrefix+l.name,"#search_menu").val(s),$("#sval2_"+V.p.idPrefix+l.name,"#search_menu").val(""),$.jgrid.isFunction(V.p.colMenuBeforeProcess)&&V.p.colMenuBeforeProcess.call(V,{module:"filtering",action:"reset",column:l.name}),$(V).trigger("reloadGrid")}),$("#bs_search",n).on("click",function(e){V.p.colFilters[l.name]={oper1:$("#oper1","#search_menu").val(),value1:$("#sval1_"+V.p.idPrefix+l.name,"#search_menu").val(),rule:$("#operand","#search_menu").val(),oper2:$("#oper2","#search_menu").val(),value2:$("#sval2_"+V.p.idPrefix+l.name,"#search_menu").val()},!0===V.p.mergeSearch&&V.p.searchModules.hasOwnProperty("colMenuSearch")&&!1!==V.p.searchModules.colMenuSearch?(V.p.searchModules.colMenuSearch=z(),$.extend(V.p.postData,{filters:$.jgrid.splitSearch(V.p.searchModules)})):V.p.postData.filters=z(),V.p.search=!0,$.jgrid.isFunction(V.p.colMenuBeforeProcess)&&V.p.colMenuBeforeProcess.call(V,{module:"filtering",action:"search",column:l.name}),$(V).trigger("reloadGrid"),$("#column_menu").remove()}),$("#bs_close",n).on("click",function(){$("#column_menu").remove()}))),$(this).addClass(_)},function(){$(this).removeClass(_)}).click(function(){var
e,t,i=$(this).attr("data-value"),r=V.grid.headers[w].el;"sortasc"===i||"sortdesc"===i?($.jgrid.isFunction(V.p.colMenuBeforeProcess)&&V.p.colMenuBeforeProcess.call(V,{module:"sorting",action:"sortasc"===i?"asc":"desc",column:p.name}),M("jqgh_"+V.p.id+"_"+p.name,w,!0,"sortasc"===i?"asc":"desc",r)):"grouping"===i?(e=w,t=o,r=V.p.colModel[e],e=V.p.groupingView,-1!==t?e.groupField.splice(t,1):e.groupField.push(r.name),$.jgrid.isFunction(V.p.colMenuBeforeProcess)&&V.p.colMenuBeforeProcess.call(V,{module:"grouping",action:-1!==t?"ungroup":"group",column:r.name}),$(V).jqGrid("groupingGroupBy",e.groupField),V.p.frozenColumns&&($(V).jqGrid("destroyFrozenColumns"),$(V).jqGrid("setFrozenColumns"))):"freeze"===i&&function(e,t){for(var
i=[],r=V.p.colModel.length,o=-1,n=V.p.colModel,a=0;a<r;a++)n[a].frozen&&(o=a),i.push(a);i.splice(e,1),i.splice(o+(t?1:0),0,e),n[e].frozen=t,$.jgrid.isFunction(V.p.colMenuBeforeProcess)&&V.p.colMenuBeforeProcess.call(V,{module:"freeze",action:t?"freeze":"unfreeze",column:n[e].name}),$(V).jqGrid("destroyFrozenColumns"),$(V).jqGrid("remapColumns",i,!0),$(V).jqGrid("setFrozenColumns")}(w,n),-1===i.indexOf("sort")&&"grouping"!==i&&"freeze"!==i||$(this).remove(),V.p.colMenuCustom.hasOwnProperty(i)&&(i=V.p.colMenuCustom[i],$.jgrid.isFunction(i.funcname)&&(i.funcname.call(V,g),i.closeOnRun&&$(this).remove()))}),parseFloat($("#column_menu").css("left"))<0&&$("#column_menu").css("left",$(V).css("left"))};for((V.p.colMenu||V.p.menubar)&&$("body").on("click",function(e){if(!$(e.target).closest("#column_menu").length)try{$("#column_menu").remove()}catch(e){}if(!$(e.target).closest(".ui-jqgrid-menubar").length)try{$("#"+V.p.id+"_menubar").hide()}catch(e){}}),this.p.id=this.id,-1===$.inArray(V.p.multikey,["shiftKey","altKey","ctrlKey"])&&(V.p.multikey=!1),V.p.keyName=!1,i=0;i<V.p.colModel.length;i++)l="string"==typeof
V.p.colModel[i].template?null!=$.jgrid.cmTemplate&&"object"==typeof
$.jgrid.cmTemplate[V.p.colModel[i].template]?$.jgrid.cmTemplate[V.p.colModel[i].template]:{}:V.p.colModel[i].template,V.p.colModel[i]=$.extend(!0,{},V.p.cmTemplate,l||{},V.p.colModel[i]),!1===V.p.keyName&&!0===V.p.colModel[i].key&&(V.p.keyName=V.p.colModel[i].name,V.p.keyIndex=i);V.p.sortorder=V.p.sortorder.toLowerCase(),$.jgrid.cell_width=$.jgrid.cellWidth();var
P=$("<table style='visibility:hidden'><tr class='jqgrow'><td>1</td></tr></table)").addClass(K(Q,"rowTable",!0,"ui-jqgrid-btable ui-common-table"));if($(s).append(P),V.p.cellLayout=$.jgrid.floatNum($("td",P).css("padding-left"))+$.jgrid.floatNum($("td",P).css("padding-right"),10)+1,V.p.cellLayout<=0&&(V.p.cellLayout=5),$(P).remove(),!null===V.p.grouping&&(V.p.scroll=!1,V.p.rownumbers=!1,V.p.treeGrid=!1,V.p.gridview=!0),!0===this.p.treeGrid){try{$(this).jqGrid("setTreeGrid")}catch(e){}"local"!==V.p.datatype?V.p.localReader={id:"_id_"}:!1!==V.p.keyName&&(V.p.localReader={id:V.p.keyName})}if(this.p.subGrid)try{$(V).jqGrid("setSubGrid")}catch(e){}this.p.multiselect&&(this.p.colNames.unshift("<input role='checkbox' id='cb_"+this.p.id+"' class='cbox' type='checkbox'/>"),this.p.colModel.unshift({name:"cb",width:$.jgrid.cell_width?V.p.multiselectWidth+V.p.cellLayout:V.p.multiselectWidth,sortable:!1,resizable:!1,hidedlg:!0,search:!1,align:"center",fixed:!0,frozen:!0,classes:"jqgrid-multibox"})),this.p.rownumbers&&(this.p.colNames.unshift(""),this.p.colModel.unshift({name:"rn",width:V.p.rownumWidth,sortable:!1,resizable:!1,hidedlg:!0,search:!1,align:"center",fixed:!0,frozen:!0})),V.p.xmlReader=$.extend(!0,{root:"rows",row:"row",page:"rows>page",total:"rows>total",records:"rows>records",repeatitems:!0,cell:"cell",id:"[id]",userdata:"userdata",subgrid:{root:"rows",row:"row",repeatitems:!0,cell:"cell"}},V.p.xmlReader),V.p.jsonReader=$.extend(!0,{root:"rows",page:"page",total:"total",records:"records",repeatitems:!0,cell:"cell",id:"id",userdata:"userdata",subgrid:{root:"rows",repeatitems:!0,cell:"cell"}},V.p.jsonReader),V.p.localReader=$.extend(!0,{root:"rows",page:"page",total:"total",records:"records",repeatitems:!1,cell:"cell",id:"id",userdata:"userdata",subgrid:{root:"rows",repeatitems:!0,cell:"cell"}},V.p.localReader),V.p.scroll&&(V.p.pgbuttons=!1,V.p.pginput=!1,V.p.rowList=[]),V.p.data.length&&(g(),oe());var
A,B,O,T,H,L,U,E,se="<thead><tr class='ui-jqgrid-labels' role='row'>",le="",de="",pe="";if(!0===V.p.shrinkToFit&&!0===V.p.forceFit)for(i=V.p.colModel.length-1;0<=i;i--)if(!V.p.colModel[i].hidden){V.p.colModel[i].resizable=!1;break}if("horizontal"===V.p.viewsortcols[1]?(de=" ui-i-asc",pe=" ui-i-desc"):"single"===V.p.viewsortcols[1]&&(de=" ui-single-sort-asc",pe=" ui-single-sort-desc",le=" style='display:none'",V.p.viewsortcols[0]=!1),A=o?"class='ui-th-div-ie'":"",L="<span class='s-ico' style='display:none'>",L+="<span sort='asc'  class='ui-grid-ico-sort ui-icon-asc"+de+" ui-sort-"+v+" "+w+" "+y+" "+K(Q,"icon_asc",!0)+"'"+le+"></span>",L+="<span sort='desc' class='ui-grid-ico-sort ui-icon-desc"+pe+" ui-sort-"+v+" "+w+" "+y+" "+K(Q,"icon_desc",!0)+"'"+le+"></span></span>",V.p.multiSort&&V.p.sortname)for(I=V.p.sortname.split(","),i=0;i<I.length;i++)n=$.jgrid.trim(I[i]).split(" "),I[i]=$.jgrid.trim(n[0]),G[i]=n[1]?$.jgrid.trim(n[1]):V.p.sortorder||"asc";for(i=0;i<this.p.colNames.length;i++){var
ce,ue=V.p.headertitles?' title="'+(V.p.colModel[i].tooltip||$.jgrid.stripHtml(V.p.colNames[i]))+'"':"";(ce=V.p.colModel[i]).hasOwnProperty("colmenu")||(ce.colmenu="rn"!==ce.name&&"cb"!==ce.name&&"subgrid"!==ce.name),se+="<th id='"+V.p.id+"_"+ce.name+"' role='columnheader' "+K(Q,"headerBox",!1,"ui-th-column ui-th-"+v+("cb"===ce.name?" jqgrid-multibox":""))+" "+ue+">",ue=ce.index||ce.name,se+="<div class='ui-th-div' id='jqgh_"+V.p.id+"_"+ce.name+"' "+A+">"+V.p.colNames[i],ce.width?ce.width=parseInt(ce.width,10):ce.width=150,"boolean"!=typeof
ce.title&&(ce.title=!0),ce.lso="",ue===V.p.sortname&&(V.p.lastsort=i),V.p.multiSort&&-1!==(n=$.inArray(ue,I))&&(ce.lso=G[n]),se+=L,V.p.colMenu&&ce.colmenu&&(se+="<a class='"+("ltr"===V.p.direction?"colmenu":"colmenu-rtl")+"'><span class='colmenuspan "+y+" "+D.icon_menu+"'></span></a>"),se+="</div></th>"}se+="</tr></thead>",ce=L=null,$(this).append(se),$("thead tr",this).first().find("th").hover(function(){$(this).addClass(_)},function(){$(this).removeClass(_)}),this.p.multiselect&&(U=[],$("#cb_"+$.jgrid.jqID(V.p.id),this).on("click",function(){V.p.preserveSelection||(V.p.selarrrow=[]);var
t=!0===V.p.frozenColumns?V.p.id+"_frozen":"";this.checked?($(V.rows).each(function(e){0<e&&($(this).hasClass("ui-subgrid")||$(this).hasClass("jqgroup")||$(this).hasClass(w)||$(this).hasClass("jqfoot")||($("#jqg_"+$.jgrid.jqID(V.p.id)+"_"+$.jgrid.jqID(this.id))[V.p.useProp?"prop":"attr"]("checked",!0),$(this).addClass(X).attr("aria-selected","true"),V.p.preserveSelection&&-1!==V.p.selarrrow.indexOf(this.id)||V.p.selarrrow.push(this.id),V.p.selrow=this.id,t&&($("#jqg_"+$.jgrid.jqID(V.p.id)+"_"+$.jgrid.jqID(this.id),V.grid.fbDiv)[V.p.useProp?"prop":"attr"]("checked",!0),$("#"+$.jgrid.jqID(this.id),V.grid.fbDiv).addClass(X))))}),E=!0,U=[]):($(V.rows).each(function(e){0<e&&($(this).hasClass("ui-subgrid")||$(this).hasClass("jqgroup")||$(this).hasClass(w)||$(this).hasClass("jqfoot")||($("#jqg_"+$.jgrid.jqID(V.p.id)+"_"+$.jgrid.jqID(this.id))[V.p.useProp?"prop":"attr"]("checked",!1),$(this).removeClass(X).attr("aria-selected","false"),U.push(this.id),!V.p.preserveSelection||-1<(e=V.p.selarrrow.indexOf(this.id))&&V.p.selarrrow.splice(e,1),t&&($("#jqg_"+$.jgrid.jqID(V.p.id)+"_"+$.jgrid.jqID(this.id),V.grid.fbDiv)[V.p.useProp?"prop":"attr"]("checked",!1),$("#"+$.jgrid.jqID(this.id),V.grid.fbDiv).removeClass(X))))}),V.p.selrow=null,E=!1),$(V).triggerHandler("jqGridSelectAll",[E?V.p.selarrrow:U,E]),$.jgrid.isFunction(V.p.onSelectAll)&&V.p.onSelectAll.call(V,E?V.p.selarrrow:U,E)})),!0===V.p.autowidth&&(ze=$(s).parent().width(),ce=$(window).width(),V.p.width=3<ce-ze?ze:ce);var
ge,he,fe,me,$e,ve,we,je,be,_e,ye,De="",xe="",qe=-1===V.p.styleUI.search("Bootstrap")||isNaN(V.p.height)?0:2;ve=0,we=$.jgrid.cell_width?0:Y(V.p.cellLayout,0),je=0,be=Y(V.p.scrollOffset,0),_e=!1,ye=0,$.each(V.p.colModel,function(){var
e;void
0===this.hidden&&(this.hidden=!1),V.p.grouping&&V.p.autowidth&&(0<=(e=$.inArray(this.name,V.p.groupingView.groupField))&&V.p.groupingView.groupColumnShow.length>e&&(this.hidden=!V.p.groupingView.groupColumnShow[e])),this.widthOrg=he=Y(this.width,0),!1===this.hidden&&(ve+=he+we,this.fixed?ye+=he+we:je++)}),isNaN(V.p.width)&&(V.p.width=ve+(!1!==V.p.shrinkToFit||isNaN(V.p.height)?0:be)),h.width=parseInt(V.p.width,10),V.p.tblwidth=ve,!1===V.p.shrinkToFit&&!0===V.p.forceFit&&(V.p.forceFit=!1),!0===V.p.shrinkToFit&&0<je&&(fe=h.width-we*je-ye,isNaN(V.p.height)||(fe-=be,_e=!0),ve=0,$.each(V.p.colModel,function(e){!1!==this.hidden||this.fixed||(he=Math.round(fe*this.width/(V.p.tblwidth-we*je-ye)),this.width=he,ve+=he,ge=e)}),$e=(me=0)===qe?-1:0,_e?h.width-ye-(ve+we*je)!==be&&(me=h.width-ye-(ve+we*je)-be):_e||0===Math.abs(h.width-ye-(ve+we*je))||(me=h.width-ye-(ve+we*je)-qe),V.p.colModel[ge].width+=me+$e,V.p.tblwidth=ve+me+we*je+ye,V.p.tblwidth>V.p.width&&(V.p.colModel[ge].width-=V.p.tblwidth-parseInt(V.p.width,10),V.p.tblwidth=V.p.width)),P=-1!==V.p.styleUI.search("Bootstrap"),$(s).css("width",h.width+"px").append("<div class='ui-jqgrid-resize-mark' id='rs_m"+V.p.id+"'>&#160;</div>"),V.p.scrollPopUp&&$(s).append("<div "+K(Q,"scrollBox",!1,"loading ui-scroll-popup")+" id='scroll_g"+V.p.id+"'></div>"),$(a).css("width",h.width+"px"),se=$(V).find("thead").first().get(0),V.p.footerrow&&(De+="<table role='presentation' style='width:"+V.p.tblwidth+"px' "+K(Q,"footerTable",!1,"ui-jqgrid-ftable ui-common-table")+"><tbody><tr role='row' "+K(Q,"footerBox",!1,"footrow footrow-"+v)+">"),V.p.headerrow&&(xe+="<table role='presentation' style='width:"+V.p.tblwidth+"px' "+K(Q,"headerRowTable",!1,"ui-jqgrid-hrtable ui-common-table")+"><tbody><tr role='row' "+K(Q,"headerRowBox",!1,"hrheadrow hrheadrow-"+v)+">");var
Ce=$(se).find("tr").first(),Ie="<tr class='jqgfirstrow' role='row'>",Ge=0,Se=$.jgrid.getFont($("th",Ce).first()[0]);if(V.p.disableClick=!1,$("th",Ce).each(function(e){ce=V.p.colModel[e],B=ce.width,void
0===ce.resizable&&(ce.resizable=!0),ce.resizable?(O=document.createElement("span"),$(O).html("&#160;").addClass("ui-jqgrid-resize ui-jqgrid-resize-"+v).css("cursor","col-resize"),$(this).addClass(V.p.resizeclass)):O="",$(this).css("width",B+"px").prepend(O),O=null;var
t="";ce.hidden&&($(this).css("display","none"),t="display:none;"),Ie+="<td role='gridcell' style='height:0px;width:"+B+"px;"+t+"'></td>",h.headers[e]={width:B,el:this},"boolean"!=typeof(le=ce.sortable)&&(ce.sortable=!0,le=!0);t=ce.name;"cb"!==t&&"subgrid"!==t&&"rn"!==t&&V.p.viewsortcols[2]&&$(">div",this).addClass("ui-jqgrid-sortable"),ce.canvas_width=ce.autosize_headers?$.jgrid.getTextWidth($("div",this).html(),Se)+(ce.colmenu?$.jgrid.floatNum($(".colmenuspan",this).parent().width()):0)+$.jgrid.floatNum($("div",this).css("padding-left"))+$.jgrid.floatNum($("div",this).css("padding-right"))+$.jgrid.floatNum($(".ui-jqgrid-resize",this).width()):0,le&&(V.p.multiSort?V.p.viewsortcols[0]?($("div span.s-ico",this).show(),ce.lso&&$("div span.ui-icon-"+ce.lso,this).removeClass(w).css("display","")):ce.lso&&($("div span.s-ico",this).show(),$("div span.ui-icon-"+ce.lso,this).removeClass(w).css("display","")):V.p.viewsortcols[0]?($("div span.s-ico",this).show(),e===V.p.lastsort&&$("div span.ui-icon-"+V.p.sortorder,this).removeClass(w).css("display","")):e===V.p.lastsort&&""!==V.p.sortname&&($("div span.s-ico",this).show(),$("div span.ui-icon-"+V.p.sortorder,this).removeClass(w).css("display",""))),V.p.footerrow&&(De+="<td role='gridcell' "+p(e,0,"",null,"",!1)+">&#160;</td>"),V.p.headerrow&&(xe+="<td role='gridcell' "+p(e,0,"",null,"",!1)+">&#160;</td>")}).mousedown(function(e){if(1===$(e.target).closest("th>span.ui-jqgrid-resize").length){var
t,i=R(this);if(e.preventDefault(),Ge++,setTimeout(function(){Ge=0},400),2!==Ge)return!0===V.p.forceFit&&(V.p.nv=function(e){for(var
t=e,i=e,r=e+1;r<V.p.colModel.length;r++)if(!0!==V.p.colModel[r].hidden){i=r;break}return i-t}(i)),h.dragStart(i,e,function(e){e=$(V.grid.headers[e].el),e=[e.position().left+e.outerWidth()];return"rtl"===V.p.direction&&(e[0]=V.p.width-e[0]),e[0]-=V.grid.bDiv.scrollLeft,e.push($(V.grid.hDiv).position().top),e.push($(V.grid.bDiv).offset().top-$(V.grid.hDiv).offset().top+$(V.grid.bDiv).height()),e}(i)),!1;try{!0===V.p.colModel[i].autosize&&(t=$(V).jqGrid("getCol",i,!1,"maxwidth"),$(V).jqGrid("resizeColumn",i,t+(P?V.p.cellLayout:0)).jqGrid("refreshGroupHeaders"))}catch(e){}finally{Ge=0}}}).click(function(e){if(V.p.disableClick)return V.p.disableClick=!1;var
t,i,r="th>div.ui-jqgrid-sortable";V.p.viewsortcols[2]||(r="th>div>span>span.ui-grid-ico-sort");var
o=$(e.target).closest(r);if(1===o.length){if(r=V.p.frozenColumns?(n=$(this)[0].id.substring(V.p.id.length+1),$.jgrid.getElemByAttrVal(V.p.colModel,"name",n,!0)):R(this),$(e.target).hasClass("colmenuspan")){if(null!=$("#column_menu")[0]&&$("#column_menu").remove(),void
0===r)return;var
n=$("#gbox_"+V.p.id).offset(),n=$(this).offset().left-n.left;return"ltr"===V.p.direction&&(n+=$(this).outerWidth()),F(r,n,0),!0===V.p.menubar&&$("#"+V.p.id+"_menubar").hide(),void
e.stopPropagation()}return V.p.viewsortcols[2]||(t=!0,i=o.attr("sort")),null!=r&&M($("div",this)[0].id,r,t,i,this),void
0!==V.p.selHeadInd&&$(h.headers[V.p.selHeadInd].el).attr("tabindex","-1"),V.p.selHeadInd=r,$(this).attr("tabindex","0"),!1}}),ce=null,jQuery._cacheCanvas=null,V.p.sortable&&$.fn.sortable)try{$(V).jqGrid("sortableColumns",Ce)}catch(e){}V.p.footerrow&&(De+="</tr></tbody></table>"),V.p.headerrow&&(xe+="</tr></tbody></table>"),Ie+="</tr>",ze=document.createElement("tbody"),this.appendChild(ze),$(this).addClass(K(Q,"rowTable",!0,"ui-jqgrid-btable ui-common-table")).append(Ie),V.p.altRows&&$(this).addClass(K(Q,"stripedTable",!0,"")),Ie=null;var
ke,Ne,Me,a=$("<table "+K(Q,"headerTable",!1,"ui-jqgrid-htable ui-common-table")+" style='width:"+V.p.tblwidth+"px' role='presentation' aria-labelledby='gbox_"+this.id+"'></table>").append(se),Re=!(!V.p.caption||!0!==V.p.hiddengrid),ze=$("<div class='ui-jqgrid-hbox"+("rtl"===v?"-rtl":"")+"'></div>"),se=null;h.hDiv=document.createElement("div"),h.hDiv.style.width=h.width-qe+"px",h.hDiv.className=K(Q,"headerDiv",!0,"ui-jqgrid-hdiv"),$(h.hDiv).append(ze),$(ze).append(a),Re&&$(h.hDiv).hide(),V.p.pager&&("string"==typeof
V.p.pager?"#"===V.p.pager.substr(0,1)&&(V.p.pager=V.p.pager.substring(1)):V.p.pager=$(V.p.pager).attr("id"),$("#"+$.jgrid.jqID(V.p.pager)).css({width:h.width-qe+"px"}).addClass(K(Q,"pagerBox",!0,"ui-jqgrid-pager")).appendTo(s),Re&&$("#"+$.jgrid.jqID(V.p.pager)).hide(),N(V.p.pager,""),V.p.pager="#"+$.jgrid.jqID(V.p.pager)),!1===V.p.cellEdit&&!0===V.p.hoverrows&&$(V).on({mouseover:function(e){H=$(e.target).closest("tr.jqgrow"),"ui-subgrid"!==$(H).attr("class")&&$(H).addClass(_)},mouseout:function(e){H=$(e.target).closest("tr.jqgrow"),$(H).removeClass(_)}}),$(V).before(h.hDiv).on({click:function(e){if(T=e.target,H=$(T,V.rows).closest("tr.jqgrow"),0===$(H).length||-1<H[0].className.indexOf(w)||($(T,V).closest("table.ui-jqgrid-btable").attr("id")||"").replace("_frozen","")!==V.id)return this;var
t=$(T).filter(":enabled").hasClass("cbox"),i=!1!==(i=$(V).triggerHandler("jqGridBeforeSelectRow",[H[0].id,e]))&&"stop"!==i;if($.jgrid.isFunction(V.p.beforeSelectRow)&&(!1!==(n=V.p.beforeSelectRow.call(V,H[0].id,e))&&"stop"!==n||(i=!1)),"A"!==T.tagName&&("INPUT"!==T.tagName&&"TEXTAREA"!==T.tagName&&"OPTION"!==T.tagName&&"SELECT"!==T.tagName||t))if(ke=H[0].id,0<(T=$(T).closest("tr.jqgrow>td")).length&&(Ne=$.jgrid.getCellIndex(T)),!0!==V.p.cellEdit){if(0<T.length&&(Me=$(T).closest("td,th").html(),$(V).triggerHandler("jqGridCellSelect",[ke,Ne,Me,e]),$.jgrid.isFunction(V.p.onCellSelect)&&V.p.onCellSelect.call(V,ke,Ne,Me,e)),i)if(V.p.multimail&&V.p.multiselect){if(e.shiftKey){if(t){var
r=$(V).jqGrid("getGridParam","selrow"),o=$(V).jqGrid("getInd",ke),n=$(V).jqGrid("getInd",r),a="",s="",s=n<o?(a=r,ke):(a=ke,r),l=!1,d=!1,r=!0;return-1<$.inArray(ke,V.p.selarrrow)&&(r=!1),$.each($(this).getDataIDs(),function(e,t){return(d=t===a||d)&&$(V).jqGrid("resetSelection",t),t!==s}),r&&$.each($(this).getDataIDs(),function(e,t){return(l=t===a||l)&&$(V).jqGrid("setSelection",t,!1),t!==s}),void(V.p.selrow=n<o?s:a)}window.getSelection().removeAllRanges()}Te(ke,t,e,!1)}else
V.p.multikey?e[V.p.multikey]?$(V).jqGrid("setSelection",ke,!0,e):V.p.multiselect&&t&&(t=$("#jqg_"+$.jgrid.jqID(V.p.id)+"_"+ke).is(":checked"),$("#jqg_"+$.jgrid.jqID(V.p.id)+"_"+ke)[V.p.useProp?"prop":"attr"]("checked",!t)):Te(ke,t,e,!0)}else
if(V.p.multiselect&&t&&i)$(V).jqGrid("setSelection",ke,!0,e);else
if(0<T.length)try{$(V).jqGrid("editCell",H[0].rowIndex,Ne,!0,e)}catch(e){}},reloadGrid:function(e,t){var
i;return!0===V.p.treeGrid&&(V.p.datatype=V.p.treedatatype),(t=t||{}).current&&V.grid.selectionPreserver(V),"local"===V.p.datatype?($(V).jqGrid("resetSelection"),V.p.data.length&&(g(),oe())):V.p.treeGrid||(V.p.selrow=null,V.p.multiselect&&(V.p.preserveSelection||(V.p.selarrrow=[],ae(!1))),V.p.savedRow=[]),V.p.scroll&&re.call(V,!0,!1),t.page&&((i=t.page)>V.p.lastpage&&(i=V.p.lastpage),i<1&&(i=1),V.p.page=i,V.grid.prevRowHeight?V.grid.bDiv.scrollTop=(i-1)*V.grid.prevRowHeight*V.p.rowNum:V.grid.bDiv.scrollTop=0),V.grid.prevRowHeight&&V.p.scroll&&void
0===t.page?(delete
V.p.lastpage,V.grid.populateVisible()):V.grid.populate(),!0===V.p.inlineNav&&$(V).jqGrid("showAddEditButtons"),!1},dblclick:function(e){if(T=e.target,H=$(T,V.rows).closest("tr.jqgrow"),0!==$(H).length){ke=H[0].rowIndex,Ne=$.jgrid.getCellIndex(T);var
t=$(V).triggerHandler("jqGridDblClickRow",[$(H).attr("id"),ke,Ne,e]);return null!=t||$.jgrid.isFunction(V.p.ondblClickRow)&&null!=(t=V.p.ondblClickRow.call(V,$(H).attr("id"),ke,Ne,e))?t:void
0}},contextmenu:function(e){if(T=e.target,H=$(T,V.rows).closest("tr.jqgrow"),0!==$(H).length){V.p.multiselect||$(V).jqGrid("setSelection",H[0].id,!0,e),ke=H[0].rowIndex,Ne=$.jgrid.getCellIndex(T);var
t=$(V).triggerHandler("jqGridRightClickRow",[$(H).attr("id"),ke,Ne,e]);return null!=t||$.jgrid.isFunction(V.p.onRightClickRow)&&null!=(t=V.p.onRightClickRow.call(V,$(H).attr("id"),ke,Ne,e))?t:void
0}}}),h.bDiv=document.createElement("div"),o&&"auto"===String(V.p.height).toLowerCase()&&(V.p.height="100%"),$(h.bDiv).append($('<div style="position:relative;"></div>').append("<div></div>").append(this)).addClass("ui-jqgrid-bdiv").css({height:V.p.height+(isNaN(V.p.height)?"":"px"),width:h.width-qe+"px"}).on("scroll",h.scrollGrid),$(h.bDiv).find("table").first().css({width:V.p.tblwidth+"px"}),$.support.tbody||2===$("tbody",this).length&&$("tbody",this).slice(1).remove(),V.p.multikey&&($.jgrid.msie()?$(h.bDiv).on("selectstart",function(){return!1}):$(h.bDiv).on("mousedown",function(){return!1})),Re&&$(h.bDiv).hide();var
Fe=y+" "+K(Q,"icon_caption_open",!0),Pe=y+" "+K(Q,"icon_caption_close",!0);h.cDiv=document.createElement("div");var
Ae,Be,Oe=!0===V.p.hidegrid?$("<a role='link' class='ui-jqgrid-titlebar-close HeaderButton "+r+"' title='"+($.jgrid.getRegional(V,"defaults.showhide",V.p.showhide)||"")+"' />").hover(function(){Oe.addClass(_)},function(){Oe.removeClass(_)}).append("<span class='ui-jqgrid-headlink "+Fe+"'></span>").css("rtl"===v?"left":"right","0px"):"";$(h.cDiv).append(Oe).append("<span class='ui-jqgrid-title'>"+V.p.caption+"</span>").addClass("ui-jqgrid-titlebar ui-jqgrid-caption"+("rtl"===v?"-rtl":"")+" "+K(Q,"gridtitleBox",!0)),!0===V.p.menubar&&(Ae='<ul id="'+V.p.id+'_menubar" class="ui-search-menu modal-content column-menu ui-menu jqgrid-caption-menu '+D.menu_widget+'" role="menubar" tabindex="0"></ul>',$("#gbox_"+V.p.id).append(Ae),$(h.cDiv).append("<a role='link' class='ui-jqgrid-menubar menubar-"+("rtl"===v?"rtl":"ltr")+"' style=''><span class='colmenuspan "+y+" "+D.icon_toolbar_menu+"'></span></a>"),$(".ui-jqgrid-menubar",h.cDiv).hover(function(){$(this).addClass(_)},function(){$(this).removeClass(_)}).on("click",function(e){e=$(e.target).position();$("#"+V.p.id+"_menubar").show(),"rtl"===V.p.direction&&$("#"+V.p.id+"_menubar").css({left:e.left-$("#"+V.p.id+"_menubar").width()-20})})),$(h.cDiv).insertBefore(h.hDiv),V.p.toolbar[0]&&(Ae=K(Q,"customtoolbarBox",!0,"ui-userdata"),h.uDiv=document.createElement("div"),"top"===V.p.toolbar[1]?$(h.uDiv).insertBefore(h.hDiv):"bottom"===V.p.toolbar[1]&&$(h.uDiv).insertAfter(h.hDiv),"both"===V.p.toolbar[1]?(h.ubDiv=document.createElement("div"),$(h.uDiv).addClass(Ae+" ui-userdata-top").attr("id","t_"+this.id).insertBefore(h.hDiv).width(h.width-qe),$(h.ubDiv).addClass(Ae+" ui-userdata-bottom").attr("id","tb_"+this.id).insertAfter(h.hDiv).width(h.width-qe),Re&&$(h.ubDiv).hide()):$(h.uDiv).width(h.width-qe).addClass(Ae+" ui-userdata-top").attr("id","t_"+this.id),Re&&$(h.uDiv).hide()),V.p.toppager&&(V.p.toppager=$.jgrid.jqID(V.p.id)+"_toppager",h.topDiv=$("<div id='"+V.p.toppager+"'></div>")[0],$(h.topDiv).addClass(K(Q,"toppagerBox",!0,"ui-jqgrid-toppager")).width(h.width-qe).insertBefore(h.hDiv),N(V.p.toppager,"_t"),V.p.toppager="#"+V.p.toppager),V.p.footerrow&&(h.sDiv=$("<div class='ui-jqgrid-sdiv'></div>")[0],ze=$("<div class='ui-jqgrid-hbox"+("rtl"===v?"-rtl":"")+"'></div>"),$(h.sDiv).append(ze).width(h.width-qe).insertAfter(h.hDiv),$(ze).append(De),h.footers=$(".ui-jqgrid-ftable",h.sDiv)[0].rows[0].cells,V.p.rownumbers&&(h.footers[0].className=K(Q,"rownumBox",!0,"jqgrid-rownum")),Re&&$(h.sDiv).hide()),V.p.headerrow&&(h.hrDiv=$("<div class='ui-jqgrid-hrdiv'></div>")[0],ze=$("<div class='ui-jqgrid-hbox"+("rtl"===v?"-rtl":"")+"'></div>"),$(h.hrDiv).append(ze).width(h.width-qe).insertAfter(h.hDiv),$(ze).append(xe),h.hrheaders=$(".ui-jqgrid-hrtable",h.hrDiv)[0].rows[0].cells,V.p.rownumbers&&(h.hrheaders[0].className=K(Q,"rownumBox",!0,"jqgrid-rownum")),Re&&$(h.nDiv).hide()),ze=null,V.p.caption?(Be=V.p.datatype,!0===V.p.hidegrid&&($(".ui-jqgrid-titlebar-close",h.cDiv).click(function(e){var
t,i=$.jgrid.isFunction(V.p.onHeaderClick),r=".ui-jqgrid-bdiv, .ui-jqgrid-hdiv, .ui-jqgrid-toppager, .ui-jqgrid-pager, .ui-jqgrid-sdiv, .ui-jqgrid-hrdiv",o=this;return!0===V.p.toolbar[0]&&("both"===V.p.toolbar[1]&&(r+=", #"+$(h.ubDiv).attr("id")),r+=", #"+$(h.uDiv).attr("id")),t=$(r,"#gview_"+$.jgrid.jqID(V.p.id)).length,"visible"===V.p.gridstate?$(r,"#gbox_"+$.jgrid.jqID(V.p.id)).slideUp("fast",function(){0===--t&&($("span",o).removeClass(Fe).addClass(Pe),V.p.gridstate="hidden",$("#gbox_"+$.jgrid.jqID(V.p.id)).hasClass("ui-resizable")&&$(".ui-resizable-handle","#gbox_"+$.jgrid.jqID(V.p.id)).hide(),$(V).triggerHandler("jqGridHeaderClick",[V.p.gridstate,e]),i&&(Re||V.p.onHeaderClick.call(V,V.p.gridstate,e)))}):"hidden"===V.p.gridstate&&$(r,"#gbox_"+$.jgrid.jqID(V.p.id)).slideDown("fast",function(){0===--t&&($("span",o).removeClass(Pe).addClass(Fe),Re&&(V.p.datatype=Be,k(),Re=!1),V.p.gridstate="visible",$("#gbox_"+$.jgrid.jqID(V.p.id)).hasClass("ui-resizable")&&$(".ui-resizable-handle","#gbox_"+$.jgrid.jqID(V.p.id)).show(),$(V).triggerHandler("jqGridHeaderClick",[V.p.gridstate,e]),i&&(Re||V.p.onHeaderClick.call(V,V.p.gridstate,e)))}),!1}),Re&&(V.p.datatype="local",$(".ui-jqgrid-titlebar-close",h.cDiv).trigger("click")))):($(h.cDiv).hide(),V.p.toppager||$(h.hDiv).addClass(K(V.p.styleUI+".common","cornertop",!0))),(V.p.headerrow?$(h.hrDiv):$(h.hDiv)).after(h.bDiv),$(h.hDiv).mousemove(function(e){if(h.resizing)return h.dragMove(e),!1}),$(".ui-jqgrid-labels",h.hDiv).on("selectstart",function(){return!1}),$(document).on("mouseup.jqGrid"+V.p.id,function(){return!h.resizing||(h.dragEnd(!0),!1)}),"rtl"===V.p.direction&&$(V).on("jqGridAfterGridComplete.setRTLPadding",function(){var
e=h.bDiv.offsetWidth-h.bDiv.clientWidth;0<e&&(e+=2),$(h.hDiv).find("div").first().hasClass("ui-jqgrid-hbox-rtl")&&$(h.hDiv).find("div").first().css({paddingLeft:e+"px"}),h.hDiv.scrollLeft=h.bDiv.scrollLeft}),V.p.autoResizing&&$(V).on("jqGridAfterGridComplete.setAutoSizeColumns",function(){var
e=!1;!0===V.p.frozenColumns&&($(V).jqGrid("destroyFrozenColumns"),e=!0),$(V.p.colModel).each(function(e){this.autosize&&!this.hidden&&this._maxsize&&0<this._maxsize&&$(V).jqGrid("resizeColumn",e,this._maxsize+V.p.cellLayout,!1,!1)}),e&&$(V).jqGrid("setFrozenColumns"),$(V).jqGrid("refreshGroupHeaders")}),V.formatCol=p,V.sortData=M,V.updatepager=function(e,t){var
i,r,o,n,a,s="",l=V.p.pager?V.p.pager.substring(1):"",d=l?"_"+l:"",p=V.p.toppager?"_"+V.p.toppager.substr(1):"",c=parseInt(V.p.page,10)-1;c<0&&(c=0),i=(c*=parseInt(V.p.rowNum,10))+V.p.reccount,V.p.scroll&&(a=$("tbody",V.grid.bDiv).first().find("> tr").slice(1),i>V.p.records&&(i=V.p.records),c=i-a.length,V.p.reccount=a.length,(a=a.outerHeight()||V.grid.prevRowHeight)&&(n=c*a,o=parseInt(V.p.records,10)*a,$(V.grid.bDiv).find(">div").first().css({height:o}).children("div").first().css({height:n,display:n?"":"none"}),0===V.grid.bDiv.scrollTop&&1<V.p.page&&(V.grid.bDiv.scrollTop=V.p.rowNum*(V.p.page-1)*a)),V.grid.bDiv.scrollLeft=V.grid.hDiv.scrollLeft),s=V.p.pager||"",(s+=V.p.toppager?s?","+V.p.toppager:V.p.toppager:"")&&(o=$.jgrid.getRegional(V,"formatter.integer"),n=Y(V.p.page),a=Y(V.p.lastpage),$(".selbox",s)[this.p.useProp?"prop":"attr"]("disabled",!1),!0===V.p.pginput&&($("#input"+d).html($.jgrid.template($.jgrid.getRegional(V,"defaults.pgtext",V.p.pgtext)||"","<input "+K(Q,"pgInput",!1,"ui-pg-input")+" type='text' size='2' maxlength='7' value='0' role='textbox'/>","<span id='sp_1_"+l+"'></span>")),V.p.toppager&&$("#input_t"+p).html($.jgrid.template($.jgrid.getRegional(V,"defaults.pgtext",V.p.pgtext)||"","<input "+K(Q,"pgInput",!1,"ui-pg-input")+" type='text' size='2' maxlength='7' value='0' role='textbox'/>","<span id='sp_1_"+l+"_toppager'></span>")),$(".ui-pg-input",s).val(V.p.page),r=V.p.toppager?"#sp_1"+d+",#sp_1"+d+"_toppager":"#sp_1"+d,$(r).html($.fmatter?$.fmatter.util.NumberFormat(V.p.lastpage,o):V.p.lastpage)),V.p.viewrecords&&(0===V.p.reccount?$(".ui-paging-info",s).html($.jgrid.getRegional(V,"defaults.emptyrecords",V.p.emptyrecords)):(l=c+1,r=V.p.records,$.fmatter&&(l=$.fmatter.util.NumberFormat(l,o),i=$.fmatter.util.NumberFormat(i,o),r=$.fmatter.util.NumberFormat(r,o)),o=$.jgrid.getRegional(V,"defaults.recordtext",V.p.recordtext),$(".ui-paging-info",s).html($.jgrid.template(o,l,i,r)))),!0===V.p.pgbuttons&&(n<=0&&(n=a=0),1===n||0===n?($("#first"+d+", #prev"+d).addClass(w).removeClass(_),V.p.toppager&&$("#first_t"+p+", #prev_t"+p).addClass(w).removeClass(_)):($("#first"+d+", #prev"+d).removeClass(w),V.p.toppager&&$("#first_t"+p+", #prev_t"+p).removeClass(w)),n===a||0===n?($("#next"+d+", #last"+d).addClass(w).removeClass(_),V.p.toppager&&$("#next_t"+p+", #last_t"+p).addClass(w).removeClass(_)):($("#next"+d+", #last"+d).removeClass(w),V.p.toppager&&$("#next_t"+p+", #last_t"+p).removeClass(w)))),!0===e&&!0===V.p.rownumbers&&$(">td.jqgrid-rownum",V.rows).each(function(e){$(this).html(c+1+e)}),0===V.p.reccount&&(e=V.p.emptyRecordRow?K(Q,"rowBox",!0,"jqgrow ui-row-"+V.p.direction+" not-editable-row not-editable-cell "+w):"jqfirstrow not-editable-row not-editable-cell",e=ne("norecs",!1,e,{},""),e+=V.p.emptyRecordRow?"<td style='text-align:center;' colspan='"+h.headers.length+"'>"+$.jgrid.getRegional(V,"defaults.emptyrecords",V.p.emptyrecords)+"</td>":"<td style='text-align:center;height:0.1px' colspan='"+h.headers.length+"'>&nbsp;</td>",e+="</tr>",$(h.bDiv).find("table").first().append(e)),t&&V.p.jqgdnd&&$(V).jqGrid("gridDnD","updateDnD"),$(V).triggerHandler("jqGridGridComplete"),$.jgrid.isFunction(V.p.gridComplete)&&V.p.gridComplete.call(V),$(V).triggerHandler("jqGridAfterGridComplete")},V.refreshIndex=oe,V.setHeadCheckBox=ae,V.constructTr=ne,V.formatter=u,$.extend(h,{populate:k,emptyRows:re,beginReq:x,endReq:q}),this.grid=h,V.addXmlData=function(e){f(e)},V.addJSONData=function(e){m(e)},V.addLocalData=j,V.treeGrid_beforeRequest=function(){V.p.treeGrid&&V.p.treeGrid_bigData&&void
0!==V.p.postData.nodeid&&"string"==typeof
V.p.postData.nodeid&&(""!==V.p.postData.nodeid||0<parseInt(V.p.postData.nodeid,10))&&(V.p.postData.rows=1e4,V.p.postData.page=1,V.p.treeGrid_rootParams.otherData.nodeid=V.p.postData.nodeid)},V.treeGrid_afterLoadComplete=function(){V.p.treeGrid&&V.p.treeGrid_bigData&&(void
0!==V.p.treeGrid_rootParams.otherData.nodeid&&"string"==typeof
V.p.treeGrid_rootParams.otherData.nodeid&&(""!==V.p.treeGrid_rootParams.otherData.nodeid||0<parseInt(V.p.treeGrid_rootParams.otherData.nodeid,10))?void
0!==V.p.treeGrid_rootParams&&null!=V.p.treeGrid_rootParams&&(V.p.page=V.p.treeGrid_rootParams.page,V.p.lastpage=V.p.treeGrid_rootParams.lastpage,V.p.postData.rows=V.p.treeGrid_rootParams.postData.rows,V.p.postData.totalrows=V.p.treeGrid_rootParams.postData.totalrows,V.p.treeGrid_rootParams.otherData.nodeid="",V.updatepager(!1,!0)):V.p.treeGrid_rootParams={page:V.p.page,lastpage:V.p.lastpage,postData:{rows:V.p.postData.rows,totalrows:V.p.postData.totalrows},rowNum:V.p.rowNum,rowTotal:V.p.rowTotal,otherData:{nodeid:""}})},this.grid.cols=this.rows[0].cells,$.jgrid.isFunction(V.p.onInitGrid)&&V.p.onInitGrid.call(V),$(V).triggerHandler("jqGridInitGrid"),k(),V.p.hiddengrid=!1,V.p.responsive&&(ze="onorientationchange"in
window?"orientationchange":"resize",$(window).on(ze,function(){$(V).jqGrid("resizeGrid")}))}else
alert($.jgrid.getRegional(this,"errors.model"))}else
alert("Element is not a table or has no id!")}function
Te(e,t,i,r){var
o;!(V.p.multiselect&&V.p.multiboxonly||V.p.multimail)||t?$(V).jqGrid("setSelection",e,r,i):V.p.multiboxonly&&V.p.multimail?($(V).triggerHandler("jqGridSelectRow",[e,!1,i]),V.p.onSelectRow&&V.p.onSelectRow.call(V,e,!1,i)):(o=V.p.frozenColumns?V.p.id+"_frozen":"",$(V.p.selarrrow).each(function(e,t){var
i=$(V).jqGrid("getGridRowById",t);i&&$(i).removeClass(X),$("#jqg_"+$.jgrid.jqID(V.p.id)+"_"+$.jgrid.jqID(t))[V.p.useProp?"prop":"attr"]("checked",!1),o&&($("#"+$.jgrid.jqID(t),"#"+$.jgrid.jqID(o)).removeClass(X),$("#jqg_"+$.jgrid.jqID(V.p.id)+"_"+$.jgrid.jqID(t),"#"+$.jgrid.jqID(o))[V.p.useProp?"prop":"attr"]("checked",!1))}),V.p.selarrrow=[],$(V).jqGrid("setSelection",e,r,i))}});var
e=$.jgrid.getMethod(He);if(!e)throw"jqGrid - No such method: "+He;var
t=$.makeArray(arguments).slice(1);return e.apply(this,t)},$.jgrid.extend({getGridParam:function(e,t){var
i,r=this[0];if(r&&r.grid){if(void
0===t&&"string"!=typeof
t&&(t="jqGrid"),i=r.p,"jqGrid"!==t)try{i=$(r).data(t)}catch(e){i=r.p}return e?void
0!==i[e]?i[e]:null:i}},setGridParam:function(t,i){return this.each(function(){var
e;null==i&&(i=!1),this.grid&&"object"==typeof
t&&(!0===i?(e=$.extend({},this.p,t),this.p=e):$.extend(!0,this.p,t))})},getGridRowById:function(t){var
i;return this.each(function(){try{for(var
e=this.rows.length;e--;)if(t.toString()===this.rows[e].id){i=this.rows[e];break}}catch(e){i=$(this.grid.bDiv).find("#"+$.jgrid.jqID(t))[0]}}),i},getDataIDs:function(){var
e,t=[],i=0,r=0;return this.each(function(){if((e=this.rows.length)&&0<e)for(;i<e;)$(this.rows[i]).hasClass("jqgrow")&&"norecs"!==this.rows[i].id&&(t[r]=this.rows[i].id,r++),i++}),t},setSelection:function(u,g,h,f){return this.each(function(){var
e,t,i,r,o,n,a,s,l=this,d=$.jgrid.getMethod("getStyleUI"),p=d(l.p.styleUI+".common","highlight",!0),c=d(l.p.styleUI+".common","disabled",!0);void
0!==u&&(void
0===f&&(f=!0),f=!1!==f,g=!1!==g,!(d=$(l).jqGrid("getGridRowById",u))||!d.className||-1<d.className.indexOf(c)||(!0===l.p.scrollrows&&0<=(t=$(l).jqGrid("getGridRowById",u).rowIndex)&&(o=t,n=$(l.grid.bDiv)[0].clientHeight,a=$(l.grid.bDiv)[0].scrollTop,s=$(l.rows[o]).position().top,o=l.rows[o].clientHeight,n+a<=s+o?$(l.grid.bDiv)[0].scrollTop=s-(n+a)+o+a:s<n+a&&s<a&&($(l.grid.bDiv)[0].scrollTop=s)),!0===l.p.frozenColumns&&(i=l.p.id+"_frozen"),l.p.multiselect?(l.setHeadCheckBox(!1),l.p.selrow=d.id,-1===(r=$.inArray(l.p.selrow,l.p.selarrrow))?("ui-subgrid"!==d.className&&$(d).addClass(p).attr("aria-selected","true"),e=!0,l.p.selarrrow.push(l.p.selrow)):-1!==r&&"_sp_"===h?("ui-subgrid"!==d.className&&$(d).addClass(p).attr("aria-selected","true"),e=!0):("ui-subgrid"!==d.className&&$(d).removeClass(p).attr("aria-selected","false"),e=!1,l.p.selarrrow.splice(r,1),t=l.p.selarrrow[0],l.p.selrow=void
0===t?null:t),$("#jqg_"+$.jgrid.jqID(l.p.id)+"_"+$.jgrid.jqID(d.id))[l.p.useProp?"prop":"attr"]("checked",e),i&&(f&&(-1===r?$("#"+$.jgrid.jqID(u),"#"+$.jgrid.jqID(i)).addClass(p):$("#"+$.jgrid.jqID(u),"#"+$.jgrid.jqID(i)).removeClass(p)),$("#jqg_"+$.jgrid.jqID(l.p.id)+"_"+$.jgrid.jqID(u),"#"+$.jgrid.jqID(i))[l.p.useProp?"prop":"attr"]("checked",e)),g&&($(l).triggerHandler("jqGridSelectRow",[d.id,e,h]),l.p.onSelectRow&&l.p.onSelectRow.call(l,d.id,e,h))):"ui-subgrid"!==d.className&&(e=l.p.selrow!==d.id&&(f&&((r=$(l).jqGrid("getGridRowById",l.p.selrow))&&$(r).removeClass(p).attr({"aria-selected":"false",tabindex:"-1"}),$(d).addClass(p).attr({"aria-selected":"true",tabindex:"0"}),i&&($("#"+$.jgrid.jqID(l.p.selrow),"#"+$.jgrid.jqID(i)).removeClass(p),$("#"+$.jgrid.jqID(u),"#"+$.jgrid.jqID(i)).addClass(p))),!0),l.p.selrow=d.id,g&&($(l).triggerHandler("jqGridSelectRow",[d.id,e,h]),l.p.onSelectRow&&l.p.onSelectRow.call(l,d.id,e,h)))))})},resetSelection:function(a){return this.each(function(){var
i,e,r=this,t=$.jgrid.getMethod("getStyleUI"),o=t(r.p.styleUI+".common","highlight",!0),n=t(r.p.styleUI+".common","hover",!0);!0===r.p.frozenColumns&&(i=r.p.id+"_frozen"),void
0!==a?(e=a===r.p.selrow?r.p.selrow:a,$("#"+$.jgrid.jqID(r.p.id)+" tbody").first().find("tr#"+$.jgrid.jqID(e)).removeClass(o).attr("aria-selected","false"),i&&$("#"+$.jgrid.jqID(e),"#"+$.jgrid.jqID(i)).removeClass(o),r.p.multiselect&&($("#jqg_"+$.jgrid.jqID(r.p.id)+"_"+$.jgrid.jqID(e),"#"+$.jgrid.jqID(r.p.id))[r.p.useProp?"prop":"attr"]("checked",!1),i&&$("#jqg_"+$.jgrid.jqID(r.p.id)+"_"+$.jgrid.jqID(e),"#"+$.jgrid.jqID(i))[r.p.useProp?"prop":"attr"]("checked",!1),r.setHeadCheckBox(!1),-1!==(t=$.inArray($.jgrid.jqID(e),r.p.selarrrow))&&r.p.selarrrow.splice(t,1)),r.p.onUnSelectRow&&r.p.onUnSelectRow.call(r,e),e=null):r.p.multiselect?($(r.p.selarrrow).each(function(e,t){$($(r).jqGrid("getGridRowById",t)).removeClass(o).attr("aria-selected","false"),$("#jqg_"+$.jgrid.jqID(r.p.id)+"_"+$.jgrid.jqID(t))[r.p.useProp?"prop":"attr"]("checked",!1),i&&($("#"+$.jgrid.jqID(t),"#"+$.jgrid.jqID(i)).removeClass(o),$("#jqg_"+$.jgrid.jqID(r.p.id)+"_"+$.jgrid.jqID(t),"#"+$.jgrid.jqID(i))[r.p.useProp?"prop":"attr"]("checked",!1)),r.p.onUnSelectRow&&r.p.onUnSelectRow.call(r,t)}),r.setHeadCheckBox(!1),r.p.selarrrow=[],r.p.selrow=null):r.p.selrow&&($("#"+$.jgrid.jqID(r.p.id)+" tbody").first().find("tr#"+$.jgrid.jqID(r.p.selrow)).removeClass(o).attr("aria-selected","false"),i&&$("#"+$.jgrid.jqID(r.p.selrow),"#"+$.jgrid.jqID(i)).removeClass(o),r.p.onUnSelectRow&&r.p.onUnSelectRow.call(r,r.p.selrow),r.p.selrow=null),!0===r.p.cellEdit&&0<=parseInt(r.p.iCol,10)&&0<=parseInt(r.p.iRow,10)&&($("td",r.rows[r.p.iRow]).eq(r.p.iCol).removeClass("edit-cell "+o),$(r.rows[r.p.iRow]).removeClass("selected-row "+n))})},getRowData:function(e,n,a){var
s,l,d={},p=!1,c=0;return this.each(function(){var
t,i,r=this;if(null==e)p=!0,s=[],l=r.rows.length;else{if(!(i=$(r).jqGrid("getGridRowById",e)))return d;l=1}for(n&&!0===n&&0<r.p.data.length||(n=!1),null==a&&(a=!1);c<l;){if(p&&(i=r.rows[c]),$(i).hasClass("jqgrow")&&"norecs"!==i.id){if(n?d=$.extend({},r.p.data[r.p._index[$.jgrid.stripPref(r.p.idPrefix,i.id)]]):$(i).children('td[role="gridcell"]').each(function(e){if("cb"!==(t=r.p.colModel[e].name)&&"subgrid"!==t&&"rn"!==t)if(!0===r.p.treeGrid&&t===r.p.ExpandColumn)d[t]=$.jgrid.htmlDecode($(this).find("span").first().html());else
try{d[t]=$.unformat.call(r,this,{rowId:i.id,colModel:r.p.colModel[e]},e)}catch(e){d[t]=$.jgrid.htmlDecode($(this).html())}}),!0===r.p.treeGrid&&a){var
o=r.p.treeReader.level_field;a+="";try{o=parseInt(d[o],10)}catch(e){o=0}d[r.p.ExpandColumn]=a.repeat(o)+d[r.p.ExpandColumn]}p&&(s.push(d),d={})}c++}}),s||d},delRowData:function(i){var
r,o,n,a=!1;return this.each(function(){var
e,t=this;if(!(r=$(t).jqGrid("getGridRowById",i)))return!1;i=r.id,t.p.subGrid&&(n=$(r).next()).hasClass("ui-subgrid")&&n.remove(),$(r).remove(),t.p.records--,t.p.reccount--,t.updatepager(!0,!1),a=!0,t.p.multiselect&&-1!==(o=$.inArray(i,t.p.selarrrow))&&t.p.selarrrow.splice(o,1),t.p.multiselect&&0<t.p.selarrrow.length?t.p.selrow=t.p.selarrrow[t.p.selarrrow.length-1]:t.p.selrow===i&&(t.p.selrow=null),"local"===t.p.datatype&&(e=$.jgrid.stripPref(t.p.idPrefix,i),void
0!==(e=t.p._index[e])&&(t.p.data.splice(e,1),t.refreshIndex()))}),a},setRowData:function(l,d,p,c){var
u,g=!0;return this.each(function(){if(!this.grid)return!1;var
i,e,r,o=this,n={},a=$(this).jqGrid("getGridRowById",l);if(!a)return!1;if(!0===c&&(e=$(o).jqGrid("getRowData",l,"local"===o.p.datatype)),d){c&&(d=$.extend(e,d));try{if($(this.p.colModel).each(function(e){u=this.name;var
t=$.jgrid.getAccessor(d,u);void
0!==t&&(n[u]=t,i=o.formatter(l,n[u],e,d,"edit"),r=this.title?{title:$.jgrid.stripHtml(i)}:{},(!0===o.p.treeGrid&&u===o.p.ExpandColumn?$("td[role='gridcell']:eq("+e+") > span:first",a):$("td[role='gridcell']:eq("+e+")",a)).html(i).attr(r))}),"local"===o.p.datatype){var
t,s=$.jgrid.stripPref(o.p.idPrefix,l),s=o.p._index[s];if(o.p.treeGrid)for(t
in
o.p.treeReader)o.p.treeReader.hasOwnProperty(t)&&delete
n[o.p.treeReader[t]];void
0!==s&&(o.p.data[s]=$.extend(!0,o.p.data[s],n)),n=null}}catch(e){g=!1}}g&&("string"==typeof
p?$(a).addClass(p):null!==p&&"object"==typeof
p&&$(a).css(p),$(o).triggerHandler("jqGridAfterGridComplete"))}),g},addRowData:function(a,s,l,d){-1===$.inArray(l,["first","last","before","after"])&&(l="last");var
p,c,u,g,h,f,m,v,w,j,b,_,y=!1,D="",x="",q="";return s&&(Array.isArray(s)?(v=!0,w=a):v=!(s=[s]),this.each(function(){var
e=this,t=s.length;g=!0===e.p.rownumbers?1:0,c=!0===e.p.multiselect?1:0,u=!0===e.p.subGrid?1:0,v||(void
0!==a?a=String(a):(a=$.jgrid.randId(),!1!==e.p.keyName&&(w=e.p.keyName,void
0!==s[0][w]&&(a=s[0][w]))));var
i=0,r=$(e).jqGrid("getStyleUI",e.p.styleUI+".base","rowBox",!0,"jqgrow ui-row-"+e.p.direction),o={},n=!!$.jgrid.isFunction(e.p.afterInsertRow);for(g&&(D=$(e).jqGrid("getStyleUI",e.p.styleUI+".base","rownumBox",!1,"jqgrid-rownum")),c&&(x=$(e).jqGrid("getStyleUI",e.p.styleUI+".base","multiBox",!1,"cbox"));i<t;){if(j=s[i],p=[],v)try{void
0===(a=j[w])&&(a=$.jgrid.randId())}catch(e){a=$.jgrid.randId()}for(_=a,a=e.p.idPrefix+a,g&&(q=e.formatCol(0,1,"",null,a,!0),p[p.length]='<td role="gridcell" '+D+" "+q+">0</td>"),c&&(m='<input role="checkbox" type="checkbox" id="jqg_'+e.p.id+"_"+a+'" '+x+"/>",q=e.formatCol(g,1,"",null,a,!0),p[p.length]='<td role="gridcell" '+q+">"+m+"</td>"),u&&(p[p.length]=$(e).jqGrid("addSubGridCell",c+g,1)),f=c+u+g;f<e.p.colModel.length;f++)b=e.p.colModel[f],o[b=b.name]=j[b],m=e.formatter(a,$.jgrid.getAccessor(j,b),f,j),q=e.formatCol(f,1,m,j,a,o),p[p.length]='<td role="gridcell" '+q+">"+m+"</td>";if(p.unshift(e.constructTr(a,!1,r,o,j)),p[p.length]="</tr>",0===e.rows.length)$(e.grid.bDiv).find("table").first().append(p.join(""));else
switch(l){case"last":$(e.rows[e.rows.length-1]).after(p.join("")),h=e.rows.length-1;break;case"first":$(e.rows[0]).after(p.join("")),h=1;break;case"after":(h=$(e).jqGrid("getGridRowById",d))&&($(e.rows[h.rowIndex+1]).hasClass("ui-subgrid")?$(e.rows[h.rowIndex+1]).after(p):$(h).after(p.join("")),h=h.rowIndex+1);break;case"before":(h=$(e).jqGrid("getGridRowById",d))&&($(h).before(p.join("")),h=h.rowIndex-1)}if(!0===e.p.subGrid&&$(e).jqGrid("addSubGrid",c+g,h),e.p.records++,e.p.reccount++,$(e).triggerHandler("jqGridAfterInsertRow",[a,j,j]),n&&e.p.afterInsertRow.call(e,a,j,j),i++,"local"===e.p.datatype)switch(o[e.p.localReader.id]=_,l){case"first":e.p.data.unshift(o);break;case"last":e.p.data.push(o);break;case"before":case"after":e.p.data.splice(h-1,0,o)}o={},1===e.p.reccount&&(h=$(e).jqGrid("getGridRowById","norecs")).rowIndex&&0<h.rowIndex&&$(e.rows[h.rowIndex]).remove()}"local"===e.p.datatype&&e.refreshIndex(),e.updatepager(!0,!0),y=!0})),y},footerData:function(r,o,n){var
a,s,l=!1,d={};return void
0===r&&(r="get"),"boolean"!=typeof
n&&(n=!0),r=r.toLowerCase(),this.each(function(){var
t,i=this;return!(!i.grid||!i.p.footerrow)&&(("set"!==r||!function(e){for(var
t
in
e)if(e.hasOwnProperty(t))return;return 1}(o))&&(l=!0,void
$(this.p.colModel).each(function(e){a=this.name,"set"===r?void
0!==o[a]&&(t=n?i.formatter("",o[a],e,o,"edit"):o[a],s=this.title?{title:$.jgrid.stripHtml(t)}:{},$("tr.footrow td",i.grid.sDiv).eq(e).html(t).attr(s),l=!0):"get"===r&&(d[a]=$("tr.footrow td",i.grid.sDiv).eq(e).html())})))}),"get"===r?d:l},headerData:function(r,o,n){var
a,s,l=!1,d={};return void
0===r&&(r="get"),"boolean"!=typeof
n&&(n=!0),r=r.toLowerCase(),this.each(function(){var
t,i=this;return!(!i.grid||!i.p.headerrow)&&(("set"!==r||!function(e){for(var
t
in
e)if(e.hasOwnProperty(t))return;return 1}(o))&&(l=!0,void
$(this.p.colModel).each(function(e){a=this.name,"set"===r?void
0!==o[a]&&(t=n?i.formatter("",o[a],e,o,"edit"):o[a],s=this.title?{title:$.jgrid.stripHtml(t)}:{},$("tr.hrheadrow td",i.grid.hrDiv).eq(e).html(t).attr(s),l=!0):"get"===r&&(d[a]=$("tr.hrheadrow td",i.grid.hrDiv).eq(e).html())})))}),"get"===r?d:l},showHideCol:function(d,p){return this.each(function(){var
t,i=this,r=!1,o=$.jgrid.cell_width?0:i.p.cellLayout,e=!1;if(i.grid){"string"==typeof
d&&(d=[d]);var
n=""===(p="none"!==p?"":"none"),a=null,s=$(i).jqGrid("isGroupHeaderOn");if(i.p.frozenColumns&&($(i).jqGrid("destroyFrozenColumns"),e=!0),s&&($(i).jqGrid("destroyGroupHeader",!1),a=$.extend([],i.p.groupHeader),i.p.groupHeader=null),$(this.p.colModel).each(function(e){-1!==$.inArray(this.name,d)&&this.hidden===n&&($("tr[role=row]",i.grid.hDiv).each(function(){$(this.cells[e]).css("display",p)}),$(i.rows).each(function(){$(this).hasClass("jqgroup")||$(this.cells[e]).css("display",p)}),i.p.footerrow&&$("tr.footrow td",i.grid.sDiv).eq(e).css("display",p),i.p.headerrow&&$("tr.hrheadrow td",i.grid.hrDiv).eq(e).css("display",p),t=parseInt(this.width,10),"none"===p?i.p.tblwidth-=t+o:i.p.tblwidth+=t+o,this.hidden=!n,r=!0,$(i).triggerHandler("jqGridShowHideCol",[n,this.name,e]))}),!0===r&&(!0!==i.p.shrinkToFit||isNaN(i.p.height)||(i.p.tblwidth+=parseInt(i.p.scrollOffset,10)),$(i).jqGrid("setGridWidth",!0===i.p.shrinkToFit?i.p.tblwidth:i.p.width)),s&&a)for(var
l=0;l<a.length;l++)$(i).jqGrid("setGroupHeaders",a[l]);e&&$(i).jqGrid("setFrozenColumns")}})},hideCol:function(e){return this.each(function(){$(this).jqGrid("showHideCol",e,"none")})},showCol:function(e){return this.each(function(){$(this).jqGrid("showHideCol",e,"")})},remapColumns:function(r,e,t){function
i(t){var
i=t.length?$.makeArray(t):$.extend({},t);$.each(r,function(e){t[e]=i[this]})}var
o=this.get(0);function
n(e,t){$(">tr"+(t||""),e).each(function(){var
t=this,i=$.makeArray(t.cells);$.each(r,function(){var
e=i[this];e&&t.appendChild(e)})})}i(o.p.colModel),i(o.p.colNames),i(o.grid.headers),n($(o.grid.hDiv).find("thead").first(),t&&":not(.ui-jqgrid-labels)"),e&&n($("#"+$.jgrid.jqID(o.p.id)+" tbody").first(),".jqgfirstrow, tr.jqgrow, tr.jqfoot"),o.p.footerrow&&n($(o.grid.sDiv).find("tbody").first()),o.p.headerrow&&n($(o.grid.hrDiv).find("tbody").first()),o.p.remapColumns&&(o.p.remapColumns.length?i(o.p.remapColumns):o.p.remapColumns=$.makeArray(r)),o.p.lastsort=$.inArray(o.p.lastsort,r),o.p.treeGrid&&(o.p.expColInd=$.inArray(o.p.expColInd,r)),$(o).triggerHandler("jqGridRemapColumns",[r,e,t])},setGridWidth:function(v,w,j){return this.each(function(){if(this.grid){var
e,t,i,r=this,o=!1,n=0,a=$.jgrid.cell_width?0:r.p.cellLayout,s=0,l=!1,d=r.p.scrollOffset,p=0,c=-1===r.p.styleUI.search("Bootstrap")||isNaN(r.p.height)?0:2;if("boolean"!=typeof
w&&(w=r.p.shrinkToFit),"boolean"!=typeof
j&&(j=!0),!isNaN(v)){if(v=parseInt(v,10),r.grid.width=r.p.width=v,$("#gbox_"+$.jgrid.jqID(r.p.id)).css("width",v+"px"),$("#gview_"+$.jgrid.jqID(r.p.id)).css("width",v+"px"),$(r.grid.bDiv).css("width",v-c+"px"),$(r.grid.hDiv).css("width",v-c+"px"),r.p.pager&&$(r.p.pager).css("width",v-c+"px"),r.p.toppager&&$(r.p.toppager).css("width",v-c+"px"),!0===r.p.toolbar[0]&&($(r.grid.uDiv).css("width",v-c+"px"),"both"===r.p.toolbar[1]&&$(r.grid.ubDiv).css("width",v-c+"px")),r.p.footerrow&&$(r.grid.sDiv).css("width",v-c+"px"),r.p.headerrow&&$(r.grid.hrDiv).css("width",v-c+"px"),(e=$(r).jqGrid("isGroupHeaderOn"))&&$(r).jqGrid("destroyGroupHeader",!1),r.p.frozenColumns&&j&&($(r).jqGrid("destroyFrozenColumns"),o=!0),!1===w&&!0===r.p.forceFit&&(r.p.forceFit=!1),!0===w){if($.each(r.p.colModel,function(){!1===this.hidden&&(h=this.widthOrg,n+=h+a,this.fixed?p+=h+a:s++)}),0===s)return;r.p.tblwidth=n,i=v-a*s-p,isNaN(r.p.height)||($(r.grid.bDiv)[0].clientHeight<$(r.grid.bDiv)[0].scrollHeight||1===r.rows.length||"scroll"===$(r.grid.bDiv).css("overflow-y"))&&(l=!0,i-=d);var
u,g=(n=0)<r.grid.cols.length;if($.each(r.p.colModel,function(e){!1!==this.hidden||this.fixed||(h=this.widthOrg,(h=Math.round(i*h/(r.p.tblwidth-a*s-p)))<0||(this.width=h,n+=h,r.grid.headers[e].width=h,r.grid.headers[e].el.style.width=h+"px",r.p.footerrow&&(r.grid.footers[e].style.width=h+"px"),r.p.headerrow&&(r.grid.hrheaders[e].style.width=h+"px"),g&&(r.grid.cols[e].style.width=h+"px"),t=e))}),!t)return;u=0,l?v-p-(n+a*s)!==d&&(u=v-p-(n+a*s)-d):l||0===Math.abs(v-p-(n+a*s))||(u=v-p-(n+a*s)-c),r.p.colModel[t].width+=u,r.p.tblwidth=n+u+a*s+p,r.p.tblwidth>v?(u=r.p.tblwidth-parseInt(v,10),r.p.tblwidth=v,h=r.p.colModel[t].width=r.p.colModel[t].width-u):r.p.tblwidth===v?(h=r.p.colModel[t].width=r.p.colModel[t].width-c,r.p.tblwidth=v-c):h=r.p.colModel[t].width;var
c=$(r.grid.bDiv)[0].scrollWidth>$(r.grid.bDiv).width()&&0!=c?-1:0,h=r.p.colModel[t].width+=c;r.grid.headers[t].width=h,r.grid.headers[t].el.style.width=h+"px",g&&(r.grid.cols[t].style.width=h+"px"),r.p.footerrow&&(r.grid.footers[t].style.width=h+"px"),r.p.headerrow&&(r.grid.hrheaders[t].style.width=h+"px")}if($(r.grid.bDiv).find("table").first().css("width",r.p.tblwidth+"px"),$(r.grid.hDiv).find("table").first().css("width",r.p.tblwidth+"px"),r.grid.hDiv.scrollLeft=r.grid.bDiv.scrollLeft,r.p.footerrow&&$(r.grid.sDiv).find("table").first().css("width",r.p.tblwidth+"px"),r.p.headerrow&&$(r.grid.hrDiv).find("table").first().css("width",r.p.tblwidth+"px"),e){var
f=$.extend([],r.p.groupHeader);r.p.groupHeader=null;for(var
m=0;m<f.length;m++)$(r).jqGrid("setGroupHeaders",f[m]);r.grid.hDiv.scrollLeft=r.grid.bDiv.scrollLeft}o&&$(r).jqGrid("setFrozenColumns")}}})},setGridHeight:function(o,n,a){return this.each(function(){var
e,t,i,r=this;r.grid&&(e=$(r.grid.bDiv),t=$(r.grid.hDiv).outerHeight(),"boolean"!=typeof
n&&(n=!1),"boolean"!=typeof
a&&(a=!0),!(i=!1)===n&&(r.p.pager&&(t+=$(r.p.pager).outerHeight()),r.p.toppager&&(t+=$(r.p.toppager).outerHeight()),!0===r.p.toolbar[0]&&(t+=$(r.grid.uDiv).outerHeight(),"both"===r.p.toolbar[1]&&(t+=$(r.grid.ubDiv).outerHeight())),r.p.footerrow&&(t+=$(r.grid.sDiv).outerHeight()),r.p.headerrow&&(t+=$(r.grid.hrDiv).outerHeight()),r.p.caption&&(t+=$(r.grid.cDiv).outerHeight()),t<o&&(o-=t)),r.p.frozenColumns&&a&&($(r).jqGrid("destroyFrozenColumns"),i=!0),e.css({height:o+(isNaN(o)?"":"px")}),r.p.height=o,i&&$(r).jqGrid("setFrozenColumns"),r.p.scroll&&r.grid.populateVisible())})},maxGridHeight:function(e,o){return this.each(function(){var
r=this;r.grid&&("set"===e&&25<o?$(r).on("jqGridAfterGridComplete.setMaxHeght",function(e){$(r.grid.bDiv);var
t=$.jgrid.jqID(r.p.id),i=$("#gbox_"+t).outerHeight(),t=$(r.grid.hDiv).outerHeight();r.p.pager&&(t+=$(r.p.pager).outerHeight()),r.p.toppager&&(t+=$(r.p.toppager).outerHeight()),!0===r.p.toolbar[0]&&(t+=$(r.grid.uDiv).outerHeight(),"both"===r.p.toolbar[1]&&(t+=$(r.grid.ubDiv).outerHeight())),r.p.footerrow&&(t+=$(r.grid.sDiv).outerHeight()),r.p.headerrow&&(t+=$(r.grid.hrDiv).outerHeight()),r.p.caption&&(t+=$(r.grid.cDiv).outerHeight()),t<i&&$(r.grid.bDiv).css("max-height",o-2)}):"remove"===e&&($(r).off("jqGridAfterGridComplete.setMaxHeght"),$(r.grid.bDiv).css("max-height","")))})},setCaption:function(t){return this.each(function(){var
e=$(this).jqGrid("getStyleUI",this.p.styleUI+".common","cornertop",!0);this.p.caption=t,$(".ui-jqgrid-title, .ui-jqgrid-title-rtl",this.grid.cDiv).html(t),$(this.grid.cDiv).show(),$(this.grid.hDiv).removeClass(e)})},setLabel:function(o,n,a,s){return this.each(function(){var
e,t,i=this,r=-1;i.grid&&null!=o&&0<=(r=isNaN(o)?$.jgrid.getElemByAttrVal(i.p.colModel,"name",o,!0):parseInt(o,10))&&(e=$("tr.ui-jqgrid-labels th",i.grid.hDiv).eq(r),n&&(t=$(".s-ico",e),$("[id^=jqgh_]",e).empty().html(n).append(t),i.p.colNames[r]=n,i.p.frozenColumns&&(e=$("tr.ui-jqgrid-labels th",i.grid.fhDiv).eq(r),t=$(".s-ico",e),$("[id^=jqgh_]",e).empty().html(n).append(t))),a&&("string"==typeof
a?$(e).addClass(a):$(e).css(a)),"object"==typeof
s&&$(e).attr(s))})},setSortIcon:function(s,l){return this.each(function(){var
e,t,i,r=this,o=-1,n=1;if(r.grid){null!=l?o=isNaN(l)?$.jgrid.getElemByAttrVal(r.p.colModel,"name",l,!0):parseInt(l,10):n=r.p.colNames.length;for(var
a=0;a<n;a++)0<=o&&(a=o),"cb"!==(i=r.p.colModel[a].name)&&"subgrid"!==i&&"rn"!==i&&(e=$("tr.ui-jqgrid-labels th",r.grid.hDiv).eq(a),t=r.p.colNames[a],i=e.find(".s-ico"),"left"===s?e.find("div.ui-th-div").first().empty().addClass("ui-icon-left").append(i).append(t):"right"===s&&e.find("div.ui-th-div").first().empty().removeClass("ui-icon-left").append(t).append(i),r.p.frozenColumns&&(e=$("tr.ui-jqgrid-labels th",r.grid.fhDiv).eq(a),t=r.p.colNames[a],i=e.find(".s-ico"),"left"===s?e.find("div.ui-th-div").first().empty().addClass("ui-icon-left").append(i).append(t):"right"===s&&e.find("div.ui-th-div").first().empty().removeClass("ui-icon-left").append(t).append(i)))}})},setCell:function(l,d,p,c,u,g){return this.each(function(){var
e,t,i=this,r=-1;if(i.grid&&(r=isNaN(d)?$.jgrid.getElemByAttrVal(i.p.colModel,"name",d,!0):parseInt(d,10),0<=r&&(t=$(i).jqGrid("getGridRowById",l)))){var
o,n,a={},s=i.p.colModel[r];try{o=t.cells[r]}catch(e){}o&&(""===p&&!0!==g||((a=$(i).jqGrid("getRowData",l,"local"===i.p.datatype))[s.name]=p,e=i.formatter(l,p,r,a,"edit"),a=s.title?{title:$.jgrid.stripHtml(e)}:{},(i.p.treeGrid&&i.p.ExpandColumn===s.name?$("span",$(o)):$(o)).html(e).attr(a),"local"===i.p.datatype&&void
0!==(n=i.p._index[$.jgrid.stripPref(i.p.idPrefix,l)])&&(i.p.data[n][s.name]=p)),"string"==typeof
c?$(o).addClass(c):c&&$(o).css(c),"object"==typeof
u&&$(o).attr(u))}})},getCell:function(r,o,n){var
a,s=!1;return"boolean"!=typeof
n&&(n=!1),this.each(function(){var
e,t=this,i=-1;if(t.grid&&(i=isNaN(o)?$.jgrid.getElemByAttrVal(t.p.colModel,"name",o,!0):parseInt(o,10),0<=i&&(e=$(t).jqGrid("getGridRowById",r))))if(a=$("td",e).eq(i),n)s=a;else{try{s=$.unformat.call(t,a,{rowId:e.id,colModel:t.p.colModel[i]},i)}catch(e){s=$.jgrid.htmlDecode(a.html())}t.p.treeGrid&&s&&t.p.ExpandColumn===o&&(s=$("<div>"+s+"</div>").find("span").first().html())}}),s},getCol:function(a,s,l){var
d,p,c,u,g=[],h=0;"boolean"!=typeof
s&&(s=!1),void
0===l&&(l=!1);var
f=$.jgrid.getFont(this[0]);return this.each(function(){var
t=this,i=-1,e=t.p.colModel;if(t.grid&&0<=(i=isNaN(a)?$.jgrid.getElemByAttrVal(t.p.colModel,"name",a,!0):parseInt(a,10))){var
r=t.rows.length,o=0,n=0;if(r&&0<r){for(;o<r;o++)if($(t.rows[o]).hasClass("jqgrow")&&"norecs"!==t.rows[o].id)if("maxwidth"!==l){try{d=$.unformat.call(t,$(t.rows[o].cells[i]),{rowId:t.rows[o].id,colModel:t.p.colModel[i]},i)}catch(e){d=$.jgrid.htmlDecode(t.rows[o].cells[i].innerHTML)}l?(u=parseFloat(d),isNaN(u)||(h+=u,void
0===c&&(c=p=u),p=Math.min(p,u),c=Math.max(c,u),n++)):s?g.push({id:t.rows[o].id,value:d}):g.push(d)}else
void
0===c&&(c=e[i].autosize_headers?e[i].canvas_width:0),c=Math.max($.jgrid.getTextWidth(t.rows[o].cells[i].innerHTML,f),c);if(l)switch(l.toLowerCase()){case"sum":g=h;break;case"avg":g=h/n;break;case"count":g=n;break;case"min":g=p;break;case"max":g=c;break;case"maxwidth":g=c}}}}),g},clearGridData:function(i,r){return this.each(function(){var
e,t=this;t.grid&&("boolean"!=typeof
i&&(i=!1),"boolean"!=typeof
r&&(r=!1),t.p.deepempty?$("#"+$.jgrid.jqID(t.p.id)+" tbody").first().find("tr").slice(1).remove():(e=$("#"+$.jgrid.jqID(t.p.id)+" tbody").first().find("tr").first()[0],$("#"+$.jgrid.jqID(t.p.id)+" tbody").first().empty().append(e)),t.p.footerrow&&i&&$(".ui-jqgrid-ftable td",t.grid.sDiv).html("&#160;"),t.p.headerrow&&r&&$(".ui-jqgrid-hrtable td",t.grid.hrDiv).html("&#160;"),t.p.selrow=null,t.p.selarrrow=[],t.p.savedRow=[],t.p.records=0,t.p.page=1,t.p.lastpage=0,t.p.reccount=0,t.p.data=[],t.p._index={},t.p.groupingView._locgr=!1,t.updatepager(!0,!1))})},getInd:function(e,t){var
i,r=!1;return this.each(function(){(i=$(this).jqGrid("getGridRowById",e))&&(r=!0===t?i:i.rowIndex)}),r},bindKeys:function(e){var
l=$.extend({onEnter:null,onSpace:null,onLeftKey:null,onRightKey:null,scrollingRows:!0},e||{});return this.each(function(){var
s=this;$("body").is("[role]")||$("body").attr("role","application"),s.p.scrollrows=l.scrollingRows,$(s).on("keydown",function(e){var
t,i,r=$(s).find("tr[tabindex=0]")[0],o=s.p.treeReader.expanded_field;if(r){var
n=s.p.selrow,a=s.p._index[$.jgrid.stripPref(s.p.idPrefix,r.id)];if(37===e.keyCode||38===e.keyCode||39===e.keyCode||40===e.keyCode){if(38===e.keyCode){if(t="",(i=r.previousSibling)&&$(i).hasClass("jqgrow")){if($(i).is(":hidden")){for(;i;)if(i=i.previousSibling,!$(i).is(":hidden")&&$(i).hasClass("jqgrow")){t=i.id;break}}else
t=i.id;$(s).jqGrid("setSelection",t,!0,e)}$(s).triggerHandler("jqGridKeyUp",[t,n,e]),$.jgrid.isFunction(l.onUpKey)&&l.onUpKey.call(s,t,n,e),e.preventDefault()}if(40===e.keyCode){if(t="",(i=r.nextSibling)&&$(i).hasClass("jqgrow")){if($(i).is(":hidden")){for(;i;)if(i=i.nextSibling,!$(i).is(":hidden")&&$(i).hasClass("jqgrow")){t=i.id;break}}else
t=i.id;$(s).jqGrid("setSelection",t,!0,e)}$(s).triggerHandler("jqGridKeyDown",[t,n,e]),$.jgrid.isFunction(l.onDownKey)&&l.onDownKey.call(s,t,n,e),e.preventDefault()}37===e.keyCode&&(s.p.treeGrid&&s.p.data[a][o]&&$(r).find("div.treeclick").trigger("click"),$(s).triggerHandler("jqGridKeyLeft",[s.p.selrow,e]),$.jgrid.isFunction(l.onLeftKey)&&l.onLeftKey.call(s,s.p.selrow,e)),39===e.keyCode&&(s.p.treeGrid&&!s.p.data[a][o]&&$(r).find("div.treeclick").trigger("click"),$(s).triggerHandler("jqGridKeyRight",[s.p.selrow,e]),$.jgrid.isFunction(l.onRightKey)&&l.onRightKey.call(s,s.p.selrow,e))}else
13===e.keyCode?($(s).triggerHandler("jqGridKeyEnter",[s.p.selrow,e]),$.jgrid.isFunction(l.onEnter)&&l.onEnter.call(s,s.p.selrow,e)):32===e.keyCode&&($(s).triggerHandler("jqGridKeySpace",[s.p.selrow,e]),$.jgrid.isFunction(l.onSpace)&&l.onSpace.call(s,s.p.selrow,e))}}).on("click",function(e){$(e.target).is("input, textarea, select")||$(e.target,s.rows).closest("tr.jqgrow").focus()})})},unbindKeys:function(){return this.each(function(){$(this).off("keydown")})},getLocalRow:function(e){var
t,i=!1;return this.each(function(){void
0!==e&&0<=(t=this.p._index[$.jgrid.stripPref(this.p.idPrefix,e)])&&(i=this.p.data[t])}),i},progressBar:function(o){return o=$.extend({htmlcontent:"",method:"hide",loadtype:"disable"},o||{}),this.each(function(){var
e,t="show"===o.method,i=$("#load_"+$.jgrid.jqID(this.p.id)),r=$(window).scrollTop();switch(""!==o.htmlcontent&&i.html(o.htmlcontent),o.loadtype){case"disable":break;case"enable":i.toggle(t);break;case"block":$("#lui_"+$.jgrid.jqID(this.p.id)).css(t?{top:0,left:0,height:$("#gbox_"+$.jgrid.jqID(this.p.id)).height(),width:$("#gbox_"+$.jgrid.jqID(this.p.id)).width(),"z-index":1e4,position:"absolute"}:{}).toggle(t),i.toggle(t)}i.is(":visible")&&(e=i.offsetParent(),i.css("top",""),i.offset().top<r&&(e=Math.min(10+r-e.offset().top,e.height()-i.height()),i.css("top",e+"px")))})},getColProp:function(e){var
t=this[0];return!!t.grid&&$.jgrid.getElemByAttrVal(t.p.colModel,"name",e,!1)},setColProp:function(t,i){return this.each(function(){var
e;this.grid&&(!$.isPlainObject(i)||0<=(e=$.jgrid.getElemByAttrVal(this.p.colModel,"name",t,!0))&&$.extend(!0,this.p.colModel[e],i))})},sortGrid:function(n,a,s){return this.each(function(){var
e,t,i=this,r=-1,o=!1;if(i.grid){for(n=n||i.p.sortname,e=0;e<i.p.colModel.length;e++)if(i.p.colModel[e].index===n||i.p.colModel[e].name===n){r=e,!0===i.p.frozenColumns&&!0===i.p.colModel[e].frozen&&(o=i.grid.fhDiv.find("#"+i.p.id+"_"+n));break}-1!==r&&(t=i.p.colModel[r].sortable,o=o||i.grid.headers[r].el,"boolean"!=typeof
t&&(t=!0),"boolean"!=typeof
a&&(a=!1),t&&i.sortData("jqgh_"+i.p.id+"_"+n,r,a,s,o))}})},setGridState:function(r){return this.each(function(){var
e,t,i;this.grid&&(t=$(e=this).jqGrid("getStyleUI",this.p.styleUI+".base","icon_caption_open",!0),i=$(this).jqGrid("getStyleUI",this.p.styleUI+".base","icon_caption_close",!0),"hidden"===r?($(".ui-jqgrid-bdiv, .ui-jqgrid-hdiv","#gview_"+$.jgrid.jqID(e.p.id)).slideUp("fast"),e.p.pager&&$(e.p.pager).slideUp("fast"),e.p.toppager&&$(e.p.toppager).slideUp("fast"),!0===e.p.toolbar[0]&&("both"===e.p.toolbar[1]&&$(e.grid.ubDiv).slideUp("fast"),$(e.grid.uDiv).slideUp("fast")),e.p.footerrow&&$(".ui-jqgrid-sdiv","#gbox_"+$.jgrid.jqID(e.p.id)).slideUp("fast"),e.p.headerrow&&$(".ui-jqgrid-hrdiv","#gbox_"+$.jgrid.jqID(e.p.id)).slideUp("fast"),$(".ui-jqgrid-headlink",e.grid.cDiv).removeClass(t).addClass(i),e.p.gridstate="hidden"):"visible"===r&&($(".ui-jqgrid-hdiv, .ui-jqgrid-bdiv","#gview_"+$.jgrid.jqID(e.p.id)).slideDown("fast"),e.p.pager&&$(e.p.pager).slideDown("fast"),e.p.toppager&&$(e.p.toppager).slideDown("fast"),!0===e.p.toolbar[0]&&("both"===e.p.toolbar[1]&&$(e.grid.ubDiv).slideDown("fast"),$(e.grid.uDiv).slideDown("fast")),e.p.footerrow&&$(".ui-jqgrid-sdiv","#gbox_"+$.jgrid.jqID(e.p.id)).slideDown("fast"),e.p.headerrow&&$(".ui-jqgrid-hrdiv","#gbox_"+$.jgrid.jqID(e.p.id)).slideDown("fast"),$(".ui-jqgrid-headlink",e.grid.cDiv).removeClass(i).addClass(t),e.p.gridstate="visible"))})},setFrozenColumns:function(){return this.each(function(){if(this.grid){var
e,t,i,r,o,n,a,s,l=this,d=l.p.colModel,p=0,c=d.length,u=-1,g=!1,h=$(l).jqGrid("getStyleUI",l.p.styleUI+".base","headerDiv",!0,"ui-jqgrid-hdiv"),f=$(l).jqGrid("getStyleUI",l.p.styleUI+".common","hover",!0),m="border-box"===$("#gbox_"+$.jgrid.jqID(l.p.id)).css("box-sizing")?1:0;if(!0!==l.p.subGrid&&!0!==l.p.treeGrid&&!0!==l.p.cellEdit&&!l.p.scroll){for(;p<c&&!0===d[p].frozen;)g=!0,u=p,p++;0<=u&&g&&(e=parseInt($(".ui-jqgrid-hdiv","#gview_"+$.jgrid.jqID(l.p.id)).height(),10),a=$(".ui-jqgrid-bdiv","#gview_"+$.jgrid.jqID(l.p.id)).position(),t=$(".ui-jqgrid-hdiv","#gview_"+$.jgrid.jqID(l.p.id)).position(),l.grid.fhDiv=$('<div style="position:absolute;'+("rtl"===l.p.direction?"right:0px;":"left:0px;")+"top:"+t.top+"px;height:"+(e-m)+'px;" class="frozen-div '+h+'"></div>'),l.grid.fbDiv=$('<div style="position:absolute;'+("rtl"===l.p.direction?"right:0px;":"left:0px;")+"top:"+a.top+'px;overflow-y:hidden" class="frozen-bdiv ui-jqgrid-bdiv"></div>'),$("#gview_"+$.jgrid.jqID(l.p.id)).append(l.grid.fhDiv),m=$(".ui-jqgrid-htable","#gview_"+$.jgrid.jqID(l.p.id)).clone(!0),h=null,$(l).jqGrid("isGroupHeaderOn")?(h=$("tr.jqg-third-row-header",l.grid.hDiv).height(),$("tr.jqg-first-row-header, tr.jqg-third-row-header",m).each(function(){$("th",this).slice(u+1).remove()}),r=i=-1,$("tr.jqg-second-row-header th",m).each(function(){if(o=parseInt($(this).attr("colspan"),10),parseInt($(this).attr("rowspan"),10)&&(i++,r++),o&&(i+=o,r++),i===u)return r=u,!1}),i!==u&&(r=u),$("tr.jqg-second-row-header",m).each(function(){$("th",this).slice(r+1).remove()})):(n=[],$(".ui-jqgrid-htable tr","#gview_"+$.jgrid.jqID(l.p.id)).each(function(e,t){n.push(parseInt($(this).height(),10))}),$("tr",m).each(function(){$("th",this).slice(u+1).remove()}),$("tr",m).each(function(e){$(this).height(n[e])})),(a=$("tr.jqg-second-row-header th",m).filter(function(){return"none"!==$(this).css("display")}).first())&&""===$.jgrid.trim(a.text())&&a.html("&nbsp;"),(a=$("tr.jqg-third-row-header th",m).filter(function(){return"none"!==$(this).css("display")}).first())&&""===$.jgrid.trim(a.text())&&$("div",a).prepend("&nbsp;"),h&&$("tr.jqg-third-row-header th",m).eq(0).height(h),$(m).width(1),$.jgrid.msie()||$(m).css("height","100%"),$(l.grid.fhDiv).append(m).mousemove(function(e){if(l.grid.resizing)return l.grid.dragMove(e),!1}),l.p.sortable&&$.fn.sortable&&$(l.grid.fhDiv).find("tr.ui-jqgrid-labels th").addClass("sortable-disabled"),l.p.headerrow&&(l.grid.fhrDiv=$('<div style="position:absolute;'+("rtl"===l.p.direction?"right:0px;":"left:0px;")+'top:0px;" class="frozen-hrdiv ui-jqgrid-hrdiv "></div>'),$("#gview_"+$.jgrid.jqID(l.p.id)).append(l.grid.fhrDiv)),l.p.footerrow&&(l.grid.fsDiv=$('<div style="position:absolute;'+("rtl"===l.p.direction?"right:0px;":"left:0px;")+'top:0px;" class="frozen-sdiv ui-jqgrid-sdiv"></div>'),$("#gview_"+$.jgrid.jqID(l.p.id)).append(l.grid.fsDiv)),$("#gview_"+$.jgrid.jqID(l.p.id)).append(l.grid.fbDiv),$(l.grid.fbDiv).on("mousewheel DOMMouseScroll",function(e){var
t=$(l.grid.bDiv).scrollTop();0<e.originalEvent.wheelDelta||e.originalEvent.detail<0?$(l.grid.bDiv).scrollTop(t-25):$(l.grid.bDiv).scrollTop(t+25),e.preventDefault()}),!0===l.p.hoverrows&&$("#"+$.jgrid.jqID(l.p.id)).off("mouseover mouseout"),$(l).on("jqGridAfterGridComplete.setFrozenColumns",function(){$("#"+$.jgrid.jqID(l.p.id)+"_frozen").remove(),s=parseInt($(l.grid.bDiv)[0].scrollWidth,10)>parseInt($(l.grid.bDiv)[0].clientWidth,10),$(l.grid.fbDiv).height($(l.grid.bDiv)[0].clientHeight-(s?0:l.p.scrollOffset-3));var
i=[];$("#"+$.jgrid.jqID(l.p.id)+" tr[role=row].jqgrow").each(function(){i.push($(this).height())});var
e,t,r=$("#"+$.jgrid.jqID(l.p.id)).clone(!0);$("tr[role=row]",r).each(function(){$("td[role=gridcell]",this).slice(u+1).remove()}),$(r).width(1).attr("id",l.p.id+"_frozen"),$(l.grid.fbDiv).append(r),$("tr[role=row].jqgrow",r).each(function(e,t){$(this).height(i[e])}),"norecs"===l.rows[1].id&&$("#norecs td",r).html(""),$(l.grid.fbDiv)[0].scrollTop=$(l.grid.bDiv)[0].scrollTop,!0===l.p.hoverrows&&($("tr.jqgrow",r).hover(function(){$(this).addClass(f),$("#"+$.jgrid.jqID(this.id),"#"+$.jgrid.jqID(l.p.id)).addClass(f)},function(){$(this).removeClass(f),$("#"+$.jgrid.jqID(this.id),"#"+$.jgrid.jqID(l.p.id)).removeClass(f)}),$("tr.jqgrow","#"+$.jgrid.jqID(l.p.id)).hover(function(){$(this).addClass(f),$("#"+$.jgrid.jqID(this.id),"#"+$.jgrid.jqID(l.p.id)+"_frozen").addClass(f)},function(){$(this).removeClass(f),$("#"+$.jgrid.jqID(this.id),"#"+$.jgrid.jqID(l.p.id)+"_frozen").removeClass(f)})),l.p.headerrow&&(e=$(l.grid.hrDiv).position(),$("table",l.grid.fhrDiv).remove(),t=$(".ui-jqgrid-hrtable","#gview_"+$.jgrid.jqID(l.p.id)).clone(!0),$("tr",t).each(function(){$("td",this).slice(u+1).remove()}),$(t).width(1),$(l.grid.fhrDiv).css("top",e.top+"px").append(t)),l.p.footerrow&&(e=$(l.grid.sDiv).position(),$("table",l.grid.fsDiv).remove(),t=$(".ui-jqgrid-ftable","#gview_"+$.jgrid.jqID(l.p.id)).clone(!0),$("tr",t).each(function(){$("td",this).slice(u+1).remove()}),$(t).width(1),$(l.grid.fsDiv).css("top",e.top+"px").append(t))}),l.grid.hDiv.loading||$(l).triggerHandler("jqGridAfterGridComplete.setFrozenColumns"),l.p.frozenColumns=!0)}}})},destroyFrozenColumns:function(){return this.each(function(){var
e,t,i;this.grid&&!0===this.p.frozenColumns&&(t=$(e=this).jqGrid("getStyleUI",e.p.styleUI+".common","hover",!0),$(e.grid.fhDiv).remove(),$(e.grid.fbDiv).remove(),e.grid.fhDiv=null,e.grid.fbDiv=null,e.p.footerrow&&($(e.grid.fsDiv).remove(),e.grid.fsDiv=null),e.p.headerrow&&($(e.grid.fhrDiv).remove(),e.grid.fhrDiv=null),$(this).off(".setFrozenColumns"),!0===e.p.hoverrows&&$("#"+$.jgrid.jqID(e.p.id)).on({mouseover:function(e){i=$(e.target).closest("tr.jqgrow"),"ui-subgrid"!==$(i).attr("class")&&$(i).addClass(t)},mouseout:function(e){i=$(e.target).closest("tr.jqgrow"),$(i).removeClass(t)}}),this.p.frozenColumns=!1)})},resizeColumn:function(a,s,l,d){return this.each(function(){var
e,t,i=this.grid,r=this.p,o=r.colModel,n=o.length;if("boolean"!=typeof
l&&(l=!1),"boolean"!=typeof
d&&(d=!0),"string"==typeof
a){for(e=0;e<n;e++)if(o[e].name===a){a=e;break}}else
a=parseInt(a,10);if((o[a].resizable||l)&&(s=parseInt(s,10),!("number"!=typeof
a||a<0||a>o.length-1||"number"!=typeof
s||s<r.minColWidth))){if(r.forceFit)for(r.nv=0,e=a+1;e<n;e++)if(!0!==o[e].hidden){r.nv=e-a;break}if(i.resizing={idx:a},t=s-i.headers[a].width,r.forceFit){if(i.headers[a+r.nv].width-t<r.minColWidth)return;i.headers[a+r.nv].newWidth=i.headers[a+r.nv].width-t}i.newWidth=r.tblwidth+t,i.headers[a].newWidth=s,i.dragEnd(!1,d)}})},getStyleUI:function(e,t,i,r){var
o="",n="";try{var
a=e.split(".");switch(i||(o="class=",n='"'),null==r&&(r=""),a.length){case
1:o+=n+$.jgrid.trim(r+" "+$.jgrid.styleUI[a[0]][t]+n);break;case
2:o+=n+$.jgrid.trim(r+" "+$.jgrid.styleUI[a[0]][a[1]][t]+n)}}catch(e){o=""}return o},resizeGrid:function(e,a,s,l){return this.each(function(){var
o=this,n=!1;void
0===e&&(e=500),"boolean"!=typeof
a&&(a=!0),"boolean"!=typeof
s&&(s=!0),"boolean"!=typeof
l&&(l=!0),setTimeout(function(){try{var
e,t,i,r;o.p.frozenColumns&&l&&($("#"+$.jgrid.jqID(o.p.id)).jqGrid("destroyFrozenColumns"),n=!0),a&&(r=o.p.height,t=$(window).width(),e=$("#gbox_"+$.jgrid.jqID(o.p.id)).parent().width(),i=o.p.width,i=3<t-e?e:t,$("#"+$.jgrid.jqID(o.p.id)).jqGrid("setGridWidth",i,o.p.shrinkToFit,!1)),s&&(e=-1===o.p.styleUI.search("Bootstrap")||isNaN(o.p.height)?0:2,r=3<(t=$(window).height())-(i=$("#gbox_"+$.jgrid.jqID(o.p.id)).parent().height())?i:t,$("#"+$.jgrid.jqID(o.p.id)).jqGrid("setGridHeight",r-e,!0,!1)),n&&$("#"+$.jgrid.jqID(o.p.id)).jqGrid("setFrozenColumns","resizeGrid")}catch(e){}},e)})},colMenuAdd:function(e,t){var
i=this[0].p.styleUI,i=$.jgrid.styleUI[i].colmenu;return t=$.extend({title:"Item",icon:i.icon_new_item,funcname:null,position:"last",closeOnRun:!0,exclude:"",id:null},t||{}),this.each(function(){t.colname="all"===e?"_all_":e;t.id=null===t.id?$.jgrid.randId():t.id,this.p.colMenuCustom[t.id]=t})},colMenuDelete:function(e){return this.each(function(){this.p.colMenuCustom.hasOwnProperty(e)&&delete
this.p.colMenuCustom[e]})},menubarAdd:function(o){var
i,n,e=this[0].p.styleUI,a=$.jgrid.styleUI[e].common;return this.each(function(){var
r=this;if(Array.isArray(o))for(var
e=0;e<o.length;e++){(i=o[e]).id||(i.id=$.jgrid.randId());var
t="";i.icon&&(t='<span class="'+a.icon_base+" "+i.icon+'"></span>'),i.position||(i.position="last"),i.closeoncall||(i.closeoncall=!0),i.divider?(n='<li class="ui-menu-item divider" role="separator"></li>',i.cick=null):n='<li class="ui-menu-item" role="presentation"><a id="'+i.id+'" class="g-menu-item" tabindex="0" role="menuitem" ><table class="ui-common-table"><tr><td class="menu_icon">'+t+'</td><td class="menu_text">'+i.title+"</td></tr></table></a></li>","last"===i.position?$("#"+this.p.id+"_menubar").append(n):$("#"+this.p.id+"_menubar").prepend(n)}$("li a","#"+this.p.id+"_menubar").each(function(e,i){$(o).each(function(e,t){if(t.id===i.id&&$.jgrid.isFunction(t.click))return $(i).on("click",function(e){t.click.call(r,e)}),!1}),$(this).hover(function(e){$(this).addClass(a.hover),e.stopPropagation()},function(e){$(this).removeClass(a.hover)})})})},menubarDelete:function(e){return this.each(function(){$("#"+e,"#"+this.p.id+"_menubar").remove()})}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base"],e):e(jQuery)}(function(v){"use strict";v.fmatter={},v.extend(v.fmatter,{isBoolean:function(e){return"boolean"==typeof
e},isObject:function(e){return e&&("object"==typeof
e||v.jgrid.isFunction(e))||!1},isString:function(e){return"string"==typeof
e},isNumber:function(e){return"number"==typeof
e&&isFinite(e)},isValue:function(e){return this.isObject(e)||this.isString(e)||this.isNumber(e)||this.isBoolean(e)},isEmpty:function(e){return!(!this.isString(e)&&this.isValue(e))&&(!this.isValue(e)||""===(e=v.jgrid.trim(e).replace(/\&nbsp\;/gi,"").replace(/\&#160\;/gi,"")))}}),v.fn.fmatter=function(e,t,i,r,o){var
n=t;i=v.extend({},v.jgrid.getRegional(this,"formatter"),i);try{n=v.fn.fmatter[e].call(this,t,i,r,o)}catch(e){}return n},v.fmatter.util={NumberFormat:function(e,t){if(v.fmatter.isNumber(e)||(e*=1),v.fmatter.isNumber(e)){var
i=e<0,r=String(e),o=t.decimalSeparator||".";if(v.fmatter.isNumber(t.decimalPlaces)){var
n=t.decimalPlaces,a=(r=String(Number(Math.round(e+"e"+n)+"e-"+n))).lastIndexOf(".");if(0<n)for(a<0?a=(r+=o).length-1:"."!==o&&(r=r.replace(".",o));r.length-1-a<n;)r+="0"}if(t.thousandsSeparator){var
l=t.thousandsSeparator;a=-1<(a=r.lastIndexOf(o))?a:r.length;for(var
d=r.substring(a),s=-1,f=a;0<f;f--)++s%3==0&&f!==a&&(!i||1<f)&&(d=l+d),d=r.charAt(f-1)+d;r=d}return r=t.prefix?t.prefix+r:r,r=t.suffix?r+t.suffix:r}return e}},v.fn.fmatter.defaultFormat=function(e,t){return v.fmatter.isValue(e)&&""!==e?e:t.defaultValue||"&#160;"},v.fn.fmatter.email=function(e,t){return v.fmatter.isEmpty(e)?v.fn.fmatter.defaultFormat(e,t):'<a href="mailto:'+e+'">'+e+"</a>"},v.fn.fmatter.checkbox=function(e,t){var
i=v.extend({},t.checkbox);return void
0!==t.colModel&&void
0!==t.colModel.formatoptions&&(i=v.extend({},i,t.colModel.formatoptions)),t=!0===i.disabled?'disabled="disabled"':"",!v.fmatter.isEmpty(e)&&void
0!==e||(e=v.fn.fmatter.defaultFormat(e,i)),'<input type="checkbox" '+((e=((e=String(e))+"").toLowerCase()).search(/(false|f|0|no|n|off|undefined)/i)<0?" checked='checked' ":"")+' value="'+e+'" offval="no" '+t+"/>"},v.fn.fmatter.link=function(e,t){var
i={target:t.target},r="";return void
0!==t.colModel&&void
0!==t.colModel.formatoptions&&(i=v.extend({},i,t.colModel.formatoptions)),i.target&&(r="target="+i.target),v.fmatter.isEmpty(e)?v.fn.fmatter.defaultFormat(e,t):"<a "+r+' href="'+e+'">'+e+"</a>"},v.fn.fmatter.showlink=function(e,t){var
i={baseLinkUrl:t.baseLinkUrl,showAction:t.showAction,addParam:t.addParam||"",target:t.target,idName:t.idName},r="";return void
0!==t.colModel&&void
0!==t.colModel.formatoptions&&(i=v.extend({},i,t.colModel.formatoptions)),i.target&&(r="target="+i.target),i=i.baseLinkUrl+i.showAction+"?"+i.idName+"="+t.rowId+i.addParam,v.fmatter.isString(e)||v.fmatter.isNumber(e)?"<a "+r+' href="'+i+'">'+e+"</a>":v.fn.fmatter.defaultFormat(e,t)},v.fn.fmatter.integer=function(e,t){var
i=v.extend({},t.integer);return void
0!==t.colModel&&void
0!==t.colModel.formatoptions&&(i=v.extend({},i,t.colModel.formatoptions)),v.fmatter.isEmpty(e)?i.defaultValue:v.fmatter.util.NumberFormat(e,i)},v.fn.fmatter.number=function(e,t){var
i=v.extend({},t.number);return void
0!==t.colModel&&void
0!==t.colModel.formatoptions&&(i=v.extend({},i,t.colModel.formatoptions)),v.fmatter.isEmpty(e)?i.defaultValue:v.fmatter.util.NumberFormat(e,i)},v.fn.fmatter.currency=function(e,t){var
i=v.extend({},t.currency);return void
0!==t.colModel&&void
0!==t.colModel.formatoptions&&(i=v.extend({},i,t.colModel.formatoptions)),v.fmatter.isEmpty(e)?i.defaultValue:v.fmatter.util.NumberFormat(e,i)},v.fn.fmatter.date=function(e,t,i,r){var
o=v.extend({},t.date);return void
0!==t.colModel&&void
0!==t.colModel.formatoptions&&(o=v.extend({},o,t.colModel.formatoptions)),!o.reformatAfterEdit&&"edit"===r||v.fmatter.isEmpty(e)?v.fn.fmatter.defaultFormat(e,t):v.jgrid.parseDate.call(this,o.srcformat,e,o.newformat,o)},v.fn.fmatter.select=function(e,t){e=String(e);var
i,r,o=!1,n=[];if(void
0!==t.colModel.formatoptions?(o=t.colModel.formatoptions.value,i=void
0===t.colModel.formatoptions.separator?":":t.colModel.formatoptions.separator,r=void
0===t.colModel.formatoptions.delimiter?";":t.colModel.formatoptions.delimiter):void
0!==t.colModel.editoptions&&(o=t.colModel.editoptions.value,i=void
0===t.colModel.editoptions.separator?":":t.colModel.editoptions.separator,r=void
0===t.colModel.editoptions.delimiter?";":t.colModel.editoptions.delimiter),o){var
a,l=!0==(null!=t.colModel.editoptions&&!0===t.colModel.editoptions.multiple),d=[];if(l&&(d=e.split(","),d=v.map(d,function(e){return v.jgrid.trim(e)})),v.fmatter.isString(o)){for(var
s=o.split(r),f=0,c=0;c<s.length;c++)if(2<(a=s[c].split(i)).length&&(a[1]=v.map(a,function(e,t){if(0<t)return e}).join(i)),l)-1<v.inArray(a[0],d)&&(n[f]=a[1],f++);else
if(v.jgrid.trim(a[0])===v.jgrid.trim(e)){n[0]=a[1];break}}else
v.fmatter.isObject(o)&&(l?n=v.map(d,function(e){return o[e]}):n[0]=o[e]||"")}return""===(e=n.join(", "))?v.fn.fmatter.defaultFormat(e,t):e},v.fn.fmatter.rowactions=function(e){function
t(e){v.jgrid.isFunction(s.afterRestore)&&s.afterRestore.call(a,e),d.find("div.ui-inline-edit,div.ui-inline-del").show(),d.find("div.ui-inline-save,div.ui-inline-cancel").hide()}var
i=v(this).closest("tr.jqgrow"),r=i.attr("id"),o=v(this).closest("table.ui-jqgrid-btable").attr("id").replace(/_frozen([^_]*)$/,"$1"),n=v("#"+o),a=n[0],l=a.p,o=l.colModel[v.jgrid.getCellIndex(this)],d=o.frozen?v("tr#"+r+" td",n).eq(v.jgrid.getCellIndex(this)).find("> div"):v(this).parent(),s={extraparam:{}};void
0!==o.formatoptions&&(o=v.extend(!0,{},o.formatoptions),s=v.extend(s,o)),void
0!==l.editOptions&&(s.editOptions=l.editOptions),void
0!==l.delOptions&&(s.delOptions=l.delOptions),i.hasClass("jqgrid-new-row")&&(s.extraparam[l.prmNames.oper]=l.prmNames.addoper);var
f={keys:s.keys,oneditfunc:s.onEdit,successfunc:s.onSuccess,url:s.url,extraparam:s.extraparam,aftersavefunc:function(e,t){v.jgrid.isFunction(s.afterSave)&&s.afterSave.call(a,e,t),d.find("div.ui-inline-edit,div.ui-inline-del").show(),d.find("div.ui-inline-save,div.ui-inline-cancel").hide()},errorfunc:s.onError,afterrestorefunc:t,restoreAfterError:s.restoreAfterError,mtype:s.mtype};switch(e){case"edit":n.jqGrid("editRow",r,f),d.find("div.ui-inline-edit,div.ui-inline-del").hide(),d.find("div.ui-inline-save,div.ui-inline-cancel").show(),n.triggerHandler("jqGridAfterGridComplete");break;case"save":n.jqGrid("saveRow",r,f)&&(d.find("div.ui-inline-edit,div.ui-inline-del").show(),d.find("div.ui-inline-save,div.ui-inline-cancel").hide(),n.triggerHandler("jqGridAfterGridComplete"));break;case"cancel":n.jqGrid("restoreRow",r,t),d.find("div.ui-inline-edit,div.ui-inline-del").show(),d.find("div.ui-inline-save,div.ui-inline-cancel").hide(),n.triggerHandler("jqGridAfterGridComplete");break;case"del":n.jqGrid("delGridRow",r,s.delOptions);break;case"formedit":n.jqGrid("setSelection",r),n.jqGrid("editGridRow",r,s.editOptions)}},v.fn.fmatter.actions=function(e,t){var
i,r={keys:!1,editbutton:!0,delbutton:!0,editformbutton:!1},o=t.rowId,n="",a=v.jgrid.getRegional(this,"nav"),l=v.jgrid.styleUI[t.styleUI||"jQueryUI"].fmatter,d=v.jgrid.styleUI[t.styleUI||"jQueryUI"].common;if(void
0!==t.colModel.formatoptions&&(r=v.extend(r,t.colModel.formatoptions)),void
0===o||v.fmatter.isEmpty(o))return"";t="onmouseover=jQuery(this).addClass('"+d.hover+"'); onmouseout=jQuery(this).removeClass('"+d.hover+"');  ";return r.editformbutton?(i="id='jEditButton_"+o+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'formedit'); "+t,n+="<div title='"+a.edittitle+"' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' "+i+"><span class='"+d.icon_base+" "+l.icon_edit+"'></span></div>"):r.editbutton&&(i="id='jEditButton_"+o+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'edit'); "+t,n+="<div title='"+a.edittitle+"' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' "+i+"><span class='"+d.icon_base+" "+l.icon_edit+"'></span></div>"),r.delbutton&&(i="id='jDeleteButton_"+o+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'del'); "+t,n+="<div title='"+a.deltitle+"' style='float:left;' class='ui-pg-div ui-inline-del' "+i+"><span class='"+d.icon_base+" "+l.icon_del+"'></span></div>"),i="id='jSaveButton_"+o+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'save'); "+t,n+="<div title='"+a.savetitle+"' style='float:left;display:none' class='ui-pg-div ui-inline-save' "+i+"><span class='"+d.icon_base+" "+l.icon_save+"'></span></div>",i="id='jCancelButton_"+o+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'cancel'); "+t,"<div style='margin-left:8px;'>"+(n+="<div title='"+a.canceltitle+"' style='float:left;display:none;' class='ui-pg-div ui-inline-cancel' "+i+"><span class='"+d.icon_base+" "+l.icon_cancel+"'></span></div>")+"</div>"},v.unformat=function(e,t,i,r){var
o,n=t.colModel.formatter,a=t.colModel.formatoptions||{},l=/([\.\*\_\'\(\)\{\}\+\?\\])/g,d=t.colModel.unformat||v.fn.fmatter[n]&&v.fn.fmatter[n].unformat;if(void
0!==d&&v.jgrid.isFunction(d))c=d.call(this,v(e).text(),t,e);else
if(void
0!==n&&v.fmatter.isString(n)){var
s=v.jgrid.getRegional(this,"formatter")||{};switch(n){case"integer":o=(a=v.extend({},s.integer,a)).thousandsSeparator.replace(l,"\\$1"),f=new
RegExp(o,"g"),c=v(e).text().replace(f,"");break;case"number":o=(a=v.extend({},s.number,a)).thousandsSeparator.replace(l,"\\$1"),f=new
RegExp(o,"g"),c=v(e).text().replace(f,"").replace(a.decimalSeparator,".");break;case"currency":o=(a=v.extend({},s.currency,a)).thousandsSeparator.replace(l,"\\$1"),f=new
RegExp(o,"g"),c=v(e).text(),a.prefix&&a.prefix.length&&(c=c.substr(a.prefix.length)),a.suffix&&a.suffix.length&&(c=c.substr(0,c.length-a.suffix.length)),c=c.replace(f,"").replace(a.decimalSeparator,".");break;case"checkbox":var
f=t.colModel.editoptions?t.colModel.editoptions.value.split(":"):["Yes","No"],c=v("input",e).is(":checked")?f[0]:f[1];break;case"select":c=v.unformat.select(e,t,i,r);break;case"actions":return"";default:c=v(e).text()}}return void
0!==c?c:!0===r?v(e).text():v.jgrid.htmlDecode(v(e).html())},v.unformat.select=function(e,t,i,r){var
o=[],n=v(e).text();if(!0===r)return n;var
a=v.extend({},void
0!==t.colModel.formatoptions?t.colModel.formatoptions:t.colModel.editoptions),l=void
0===a.separator?":":a.separator,t=void
0===a.delimiter?";":a.delimiter;if(a.value){var
d,s=a.value,f=!0===a.multiple,c=[];if(f&&(c=n.split(","),c=v.map(c,function(e){return v.jgrid.trim(e)})),v.fmatter.isString(s)){for(var
u=s.split(t),m=0,p=0;p<u.length;p++)if(2<(d=u[p].split(l)).length&&(d[1]=v.map(d,function(e,t){if(0<t)return e}).join(l)),a.decodeValue&&!0===a.decodeValue&&(d[1]=v.jgrid.htmlDecode(d[1])),f)-1<v.inArray(v.jgrid.trim(d[1]),c)&&(o[m]=d[0],m++);else
if(v.jgrid.trim(d[1])===v.jgrid.trim(n)){o[0]=d[0];break}}else(v.fmatter.isObject(s)||Array.isArray(s))&&(f||(c[0]=n),o=v.map(c,function(i){var
r;if(v.each(s,function(e,t){if(t===i)return r=e,!1}),void
0!==r)return r}));return o.join(", ")}return n||""},v.unformat.date=function(e,t){var
i=v.jgrid.getRegional(this,"formatter.date")||{};return void
0!==t.formatoptions&&(i=v.extend({},i,t.formatoptions)),v.fmatter.isEmpty(e)?v.fn.fmatter.defaultFormat(e,t):v.jgrid.parseDate.call(this,i.newformat,e,i.srcformat,i)}});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base","./jqModal","./jqDnR"],e):e(jQuery)}(function(w){"use strict";w.extend(w.jgrid,{showModal:function(e){e.w.show()},closeModal:function(e){e.w.hide().attr("aria-hidden","true"),e.o&&e.o.remove()},hideModal:function(e,t){var
i,a,o=!(!(t=w.extend({jqm:!0,gb:"",removemodal:!1,formprop:!1,form:""},t||{})).gb||"string"!=typeof
t.gb||"#gbox_"!==t.gb.substr(0,6))&&w("#"+t.gb.substr(6))[0];if(t.onClose){var
r=o?t.onClose.call(o,e):t.onClose(e);if("boolean"==typeof
r&&!r)return}if(t.formprop&&o&&t.form&&("edit"===t.form?(i="#"+w.jgrid.jqID("FrmGrid_"+t.gb.substr(6)),a="formProp"):"view"===t.form&&(i="#"+w.jgrid.jqID("ViewGrid_"+t.gb.substr(6)),a="viewProp"),w(o).data(a,{top:w.jgrid.floatNum(w(e).css("top")),left:w.jgrid.floatNum(w(e).css("left")),width:w.jgrid.floatNum(w(e)[0].style.width),height:w.jgrid.floatNum(w(e)[0].style.height),dataheight:w(i).height(),datawidth:w(i).width()})),w.fn.jqm&&!0===t.jqm)w(e).attr("aria-hidden","true").jqmHide();else{if(""!==t.gb)try{w(t.gb).find(".jqgrid-overlay").first().hide()}catch(e){}try{w(".jqgrid-overlay-modal").hide()}catch(e){}w(e).hide().attr("aria-hidden","true")}t.removemodal&&w(e).remove()},findPos:function(e){e=w(e).offset();return[e.left,e.top]},createModal:function(i,e,a,t,o,r,n){a=w.extend(!0,{},w.jgrid.jqModal||{},a);var
d=this,l="rtl"===w(a.gbox).attr("dir"),s=w.jgrid.styleUI[a.styleUI||"jQueryUI"].modal,u=w.jgrid.styleUI[a.styleUI||"jQueryUI"].common,c=document.createElement("div");n=w.extend({},n||{}),c.className="ui-jqdialog "+s.modal,c.id=i.themodal;var
m=document.createElement("div");m.className="ui-jqdialog-titlebar "+s.header,m.id=i.modalhead,w(m).append("<span class='ui-jqdialog-title'>"+a.caption+"</span>");var
f=w("<a class='ui-jqdialog-titlebar-close "+u.cornerall+"' aria-label='Close'></a>").hover(function(){f.addClass(u.hover)},function(){f.removeClass(u.hover)}).append("<span class='"+u.icon_base+" "+s.icon_close+"'></span>");w(m).append(f),l?(c.dir="rtl",w(".ui-jqdialog-title",m).css("float","right"),w(".ui-jqdialog-titlebar-close",m).css("left","0.3em")):(c.dir="ltr",w(".ui-jqdialog-title",m).css("float","left"),w(".ui-jqdialog-titlebar-close",m).css("right","0.3em"));var
g=document.createElement("div");w(g).addClass("ui-jqdialog-content "+s.content).attr("id",i.modalcontent),w(g).append(e),c.appendChild(g),w(c).prepend(m),!0===r?w("body").append(c):"string"==typeof
r?w(r).append(c):w(c).insertBefore(t),w(c).css(n),void
0===a.jqModal&&(a.jqModal=!0);g={};w.fn.jqm&&!0===a.jqModal?(0===a.left&&0===a.top&&a.overlay&&(n=[],n=w.jgrid.findPos(o),a.left=n[0]+4,a.top=n[1]+4),g.top=a.top+"px",g.left=a.left):0===a.left&&0===a.top||(g.left=a.left,g.top=a.top+"px"),w("a.ui-jqdialog-titlebar-close",m).click(function(){var
e=w("#"+w.jgrid.jqID(i.themodal)).data("onClose")||a.onClose,t=w("#"+w.jgrid.jqID(i.themodal)).data("gbox")||a.gbox;return d.hideModal("#"+w.jgrid.jqID(i.themodal),{gb:t,jqm:a.jqModal,onClose:e,removemodal:a.removemodal||!1,formprop:!a.recreateForm||!1,form:a.form||""}),!1}),0!==a.width&&a.width||(a.width=300),0!==a.height&&a.height||(a.height=200),a.zIndex||(p=w(t).parents("*[role=dialog]").first().css("z-index"),a.zIndex=p?parseInt(p,10)+2:950);var
p=0;if(l&&g.hasOwnProperty("left")&&!r&&(p=w(a.gbox).outerWidth()-(isNaN(a.width)?0:parseInt(a.width,10))+12,g.left=parseInt(g.left,10)+parseInt(p,10)),g.hasOwnProperty("left")&&(g.left+="px"),w(c).css(w.extend({width:isNaN(a.width)?"auto":a.width+"px",height:isNaN(a.height)?"auto":a.height+"px",zIndex:a.zIndex,overflow:"hidden"},g)).attr({tabIndex:"-1",role:"dialog","aria-labelledby":i.modalhead,"aria-hidden":"true"}),void
0===a.drag&&(a.drag=!0),void
0===a.resize&&(a.resize=!0),a.drag)if(w(m).css("cursor","move"),w.fn.tinyDraggable)w(c).tinyDraggable({handle:"#"+w.jgrid.jqID(m.id)});else
try{w(c).draggable({handle:w("#"+w.jgrid.jqID(m.id))})}catch(e){}if(a.resize)if(w.fn.jqResize)w(c).append("<div class='jqResize "+s.resizable+" "+u.icon_base+" "+s.icon_resizable+"'></div>"),w("#"+w.jgrid.jqID(i.themodal)).jqResize(".jqResize",!!i.scrollelm&&"#"+w.jgrid.jqID(i.scrollelm));else
try{w(c).resizable({handles:"se, sw",alsoResize:!!i.scrollelm&&"#"+w.jgrid.jqID(i.scrollelm)})}catch(e){}!0===a.closeOnEscape&&w(c).keydown(function(e){27===e.which&&(e=w("#"+w.jgrid.jqID(i.themodal)).data("onClose")||a.onClose,d.hideModal("#"+w.jgrid.jqID(i.themodal),{gb:a.gbox,jqm:a.jqModal,onClose:e,removemodal:a.removemodal||!1,formprop:!a.recreateForm||!1,form:a.form||""}))})},viewModal:function(e,t){var
i,a="";if((t=w.extend({toTop:!0,overlay:10,modal:!1,overlayClass:"ui-widget-overlay",onShow:w.jgrid.showModal,onHide:w.jgrid.closeModal,gbox:"",jqm:!0,jqM:!0},t||{})).gbox){var
o=w("#"+t.gbox.substring(6))[0];try{a=w(o).jqGrid("getStyleUI",o.p.styleUI+".common","overlay",!1,"jqgrid-overlay-modal"),t.overlayClass=w(o).jqGrid("getStyleUI",o.p.styleUI+".common","overlay",!0)}catch(e){}}if(void
0===t.focusField&&(t.focusField=0),"number"==typeof
t.focusField&&0<=t.focusField?t.focusField=parseInt(t.focusField,10):"boolean"!=typeof
t.focusField||t.focusField?t.focusField=0:t.focusField=!1,w.fn.jqm&&!0===t.jqm)(t.jqM?w(e).attr("aria-hidden","false").jqm(t):w(e).attr("aria-hidden","false")).jqmShow();else
if(""!==t.gbox&&(i=parseInt(w(e).css("z-index"))-1,t.modal?(w(".jqgrid-overlay-modal")[0]||w("body").prepend("<div "+a+"></div>"),w(".jqgrid-overlay-modal").css("z-index",i).show()):(w(t.gbox).find(".jqgrid-overlay").first().css("z-index",i).show(),w(e).data("gbox",t.gbox))),w(e).show().attr("aria-hidden","false"),0<=t.focusField)try{w(":input:visible",e)[t.focusField].focus()}catch(e){}},info_dialog:function(e,t,i,a){var
o={width:290,height:"auto",dataheight:"auto",drag:!0,resize:!1,left:250,top:170,zIndex:1e3,jqModal:!0,modal:!1,closeOnEscape:!0,align:"center",buttonalign:"center",buttons:[]};w.extend(!0,o,w.jgrid.jqModal||{},{caption:"<b>"+e+"</b>"},a||{});var
r=o.jqModal,n=this,e=w.jgrid.styleUI[o.styleUI||"jQueryUI"].modal,d=w.jgrid.styleUI[o.styleUI||"jQueryUI"].common;w.fn.jqm&&!r&&(r=!1);var
l,s="";if(0<o.buttons.length)for(l=0;l<o.buttons.length;l++)void
0===o.buttons[l].id&&(o.buttons[l].id="info_button_"+l),s+="<a id='"+o.buttons[l].id+"' class='fm-button "+d.button+"'>"+o.buttons[l].text+"</a>";a="<div id='info_id'>";a+="<div id='infocnt' style='margin:0px;padding-bottom:1em;width:100%;overflow:auto;position:relative;height:"+(isNaN(o.dataheight)?o.dataheight:o.dataheight+"px")+";"+("text-align:"+o.align+";")+"'>"+t+"</div>",a+=i?"<div class='"+e.content+"' style='text-align:"+o.buttonalign+";padding-bottom:0.8em;padding-top:0.5em;background-image: none;border-width: 1px 0 0 0;'><a id='closedialog' class='fm-button "+d.button+"'>"+i+"</a>"+s+"</div>":""!==s?"<div class='"+e.content+"' style='text-align:"+o.buttonalign+";padding-bottom:0.8em;padding-top:0.5em;background-image: none;border-width: 1px 0 0 0;'>"+s+"</div>":"",a+="</div>";try{"false"===w("#info_dialog").attr("aria-hidden")&&w.jgrid.hideModal("#info_dialog",{jqm:r}),w("#info_dialog").remove()}catch(e){}e=w(".ui-jqgrid").css("font-size")||"11px";w.jgrid.createModal({themodal:"info_dialog",modalhead:"info_head",modalcontent:"info_content",scrollelm:"infocnt"},a,o,"","",!0,{"font-size":e}),s&&w.each(o.buttons,function(e){w("#"+w.jgrid.jqID(this.id),"#info_id").on("click",function(){return o.buttons[e].onClick.call(w("#info_dialog")),!1})}),w("#closedialog","#info_id").on("click",function(){return n.hideModal("#info_dialog",{jqm:r,onClose:w("#info_dialog").data("onClose")||o.onClose,gb:w("#info_dialog").data("gbox")||o.gbox}),!1}),w(".fm-button","#info_dialog").hover(function(){w(this).addClass(d.hover)},function(){w(this).removeClass(d.hover)}),w.jgrid.isFunction(o.beforeOpen)&&o.beforeOpen(),w.jgrid.viewModal("#info_dialog",{onHide:function(e){e.w.hide().remove(),e.o&&e.o.remove()},modal:o.modal,jqm:r}),w.jgrid.isFunction(o.afterOpen)&&o.afterOpen();try{w("#info_dialog").focus()}catch(e){}},bindEv:function(e,t){w.jgrid.isFunction(t.dataInit)&&t.dataInit.call(this,e,t),t.dataEvents&&w.each(t.dataEvents,function(){void
0!==this.data?w(e).on(this.type,this.data,this.fn):w(e).on(this.type,this.fn)})},createEl:function(e,t,i,a,o){var
r,n="",c=this;function
m(i,e,t){var
a=(a=["dataInit","dataEvents","dataUrl","buildSelect","sopt","searchhidden","defaultValue","attr","custom_element","custom_value","oper"]).concat(["cacheUrlData","delimiter","separator"]);void
0!==t&&Array.isArray(t)&&w.merge(a,t),w.each(e,function(e,t){-1===w.inArray(e,a)&&w(i).attr(e,t)}),e.hasOwnProperty("id")||w(i).attr("id",w.jgrid.randId())}switch(e){case"textarea":n=document.createElement("textarea"),a?t.cols||w(n).css({width:"98%"}):t.cols||(t.cols=20),t.rows||(t.rows=2),("&nbsp;"===i||"&#160;"===i||1===i.length&&160===i.charCodeAt(0))&&(i=""),n.value=i,w(n).attr({role:"textbox",multiline:"true"}),m(n,t);break;case"checkbox":(n=document.createElement("input")).type="checkbox",t.value?(i===(r=t.value.split(":"))[0]&&(n.checked=!0,n.defaultChecked=!0),n.value=r[0],w(n).attr("offval",r[1])):((r=(i+"").toLowerCase()).search(/(false|f|0|no|n|off|undefined)/i)<0&&""!==r?(n.checked=!0,n.defaultChecked=!0,n.value=i):n.value="on",w(n).attr("offval","off")),w(n).attr("role","checkbox"),m(n,t,["value"]);break;case"select":(n=document.createElement("select")).setAttribute("role","select");var
d,l,s=[];if(!0===t.multiple?(d=!0,n.multiple="multiple",w(n).attr("aria-multiselectable","true")):d=!1,null!=t.dataUrl){var
u=null,f=t.postData||o.postData;try{u=t.rowId}catch(e){}c.p&&c.p.idPrefix&&(u=w.jgrid.stripPref(c.p.idPrefix,u)),w.ajax(w.extend({url:w.jgrid.isFunction(t.dataUrl)?t.dataUrl.call(c,u,i,String(t.name)):t.dataUrl,type:"GET",dataType:"html",data:w.jgrid.isFunction(f)?f.call(c,u,i,String(t.name)):f,context:{elem:n,options:t,vl:i},success:function(e){var
t,i,a=[],o=this.elem,r=this.vl,n=w.extend({},this.options),d=!0===n.multiple,l=!0===n.cacheUrlData,s="",u=[],e=w.jgrid.isFunction(n.buildSelect)?n.buildSelect.call(c,e):e;"string"==typeof
e&&(e=w(w.jgrid.trim(e)).html()),e&&(w(o).append(e),m(o,n,f?["postData"]:void
0),void
0===n.size&&(n.size=d?3:1),d?(a=r.split(","),a=w.map(a,function(e){return w.jgrid.trim(e)})):a[0]=w.jgrid.trim(r),w("option",o).each(function(e){t=w(this).text(),r=w(this).val(),l&&(s+=(0!==e?";":"")+r+":"+t),0===e&&o.multiple&&(this.selected=!1),w(this).attr("role","option"),(-1<w.inArray(w.jgrid.trim(t),a)||-1<w.inArray(w.jgrid.trim(r),a))&&(this.selected="selected",u.push(r))}),n.hasOwnProperty("checkUpdate")&&n.checkUpdate&&(c.p.savedData[n.name]=u.join(",")),l&&("edit"===n.oper?w(c).jqGrid("setColProp",n.name,{editoptions:{buildSelect:null,dataUrl:null,value:s}}):"search"===n.oper?w(c).jqGrid("setColProp",n.name,{searchoptions:{dataUrl:null,value:s}}):"filter"===n.oper&&w("#fbox_"+c.p.id)[0].p&&(d=w("#fbox_"+c.p.id)[0].p.columns,w.each(d,function(e){if(i=this.index||this.name,n.name===i)return this.searchoptions.dataUrl=null,this.searchoptions.value=s,!1}))),w(c).triggerHandler("jqGridAddEditAfterSelectUrlComplete",[o]))}},o||{}))}else
if(t.value){void
0===t.size&&(t.size=d?3:1),d&&(s=i.split(","),s=w.map(s,function(e){return w.jgrid.trim(e)})),"function"==typeof
t.value&&(t.value=t.value());var
g,p,h,j,v,F,b=void
0===t.separator?":":t.separator,y=void
0===t.delimiter?";":t.delimiter;if("string"==typeof
t.value)for(g=t.value.split(y),l=0;l<g.length;l++)2<(p=g[l].split(b)).length&&(p[1]=w.map(p,function(e,t){if(0<t)return e}).join(b)),(h=document.createElement("option")).setAttribute("role","option"),h.value=p[0],h.innerHTML=p[1],n.appendChild(h),d||w.jgrid.trim(p[0])!==w.jgrid.trim(i)&&w.jgrid.trim(p[1])!==w.jgrid.trim(i)||(h.selected="selected"),d&&(-1<w.inArray(w.jgrid.trim(p[1]),s)||-1<w.inArray(w.jgrid.trim(p[0]),s))&&(h.selected="selected");else
if("[object Array]"===Object.prototype.toString.call(t.value))for(j=t.value,l=0;l<j.length;l++)2===j[l].length&&(v=j[l][0],F=j[l][1],(h=document.createElement("option")).setAttribute("role","option"),h.value=v,h.innerHTML=F,n.appendChild(h),d||w.jgrid.trim(v)!==w.jgrid.trim(i)&&w.jgrid.trim(F)!==w.jgrid.trim(i)||(h.selected="selected"),d&&(-1<w.inArray(w.jgrid.trim(F),s)||-1<w.inArray(w.jgrid.trim(v),s))&&(h.selected="selected"));else
if("object"==typeof
t.value)for(v
in
j=t.value)j.hasOwnProperty(v)&&((h=document.createElement("option")).setAttribute("role","option"),h.value=v,h.innerHTML=j[v],n.appendChild(h),d||w.jgrid.trim(v)!==w.jgrid.trim(i)&&w.jgrid.trim(j[v])!==w.jgrid.trim(i)||(h.selected="selected"),d&&(-1<w.inArray(w.jgrid.trim(j[v]),s)||-1<w.inArray(w.jgrid.trim(v),s))&&(h.selected="selected"));m(n,t,["value"])}else
m(n,t);break;case"image":case"file":(n=document.createElement("input")).type=e,m(n,t);break;case"custom":n=document.createElement("span");try{if(!w.jgrid.isFunction(t.custom_element))throw"e1";var
x=t.custom_element.call(c,i,t);if(!x)throw"e2";x=w(x).addClass("customelement").attr({id:t.id,name:t.name}),w(n).empty().append(x)}catch(e){var
y=w.jgrid.getRegional(c,"errors"),q=w.jgrid.getRegional(c,"edit");"e1"===e?w.jgrid.info_dialog(y.errcap,"function 'custom_element' "+q.msg.nodefined,q.bClose,{styleUI:c.p.styleUI}):"e2"===e?w.jgrid.info_dialog(y.errcap,"function 'custom_element' "+q.msg.novalue,q.bClose,{styleUI:c.p.styleUI}):w.jgrid.info_dialog(y.errcap,"string"==typeof
e?e:e.message,q.bClose,{styleUI:c.p.styleUI})}break;default:q="button"===e?"button":"textbox";(n=document.createElement("input")).type=e,n.value=i,"button"!==e&&(a?t.size||w(n).css({width:"96%"}):t.size||(t.size=20)),w(n).attr("role",q),m(n,t)}return n},checkDate:function(e,t){var
i={},a=-1!==(e=e.toLowerCase()).indexOf("/")?"/":-1!==e.indexOf("-")?"-":-1!==e.indexOf(".")?".":"/";if(e=e.split(a),3!==(t=t.split(a)).length)return!1;for(var
o,r=-1,n=-1,d=-1,l=0;l<e.length;l++){var
s=isNaN(t[l])?0:parseInt(t[l],10);i[e[l]]=s,-1!==(o=e[l]).indexOf("y")&&(r=l),-1!==o.indexOf("m")&&(d=l),-1!==o.indexOf("d")&&(n=l)}o="y"===e[r]||"yyyy"===e[r]?4:"yy"===e[r]?2:-1;var
u;return-1!==r&&(a=i[e[r]].toString(),2===o&&1===a.length&&(o=1),a.length===o&&(0!==i[e[r]]||"00"===t[r])&&(-1!==d&&(!((a=i[e[d]].toString()).length<1||i[e[d]]<1||12<i[e[d]])&&(-1!==n&&!((a=i[e[n]].toString()).length<1||i[e[n]]<1||31<i[e[n]]||2===i[e[d]]&&i[e[n]]>((u=i[e[r]])%4!=0||u%100==0&&u%400!=0?28:29)||i[e[n]]>[0,31,29,31,30,31,30,31,31,30,31,30,31][i[e[d]]])))))},isEmpty:function(e){return!(void
0!==e&&!e.match(/^\s+$/)&&""!==e)},checkTime:function(e){if(!w.jgrid.isEmpty(e)){if(!(e=e.match(/^(\d{1,2}):(\d{2})([apAP][Mm])?$/)))return!1;if(e[3]){if(e[1]<1||12<e[1])return!1}else
if(23<e[1])return!1;if(59<e[2])return!1}return!0},checkValues:function(e,t,i,a){var
o,r,n,d,l,s=this,u=s.p.colModel,c=w.jgrid.getRegional(this,"edit.msg"),m=function(e){var
t,i,e=e.toString();if(2<=e.length&&("-"===e[0]?(t=e[1],e[2]&&(i=e[2])):(t=e[0],e[1]&&(i=e[1])),"0"===t&&"."!==i))return!1;return"number"==typeof
parseFloat(e)&&isFinite(e)};if(void
0===i)if("string"==typeof
t){for(r=0,l=u.length;r<l;r++)if(u[r].name===t){o=u[r].editrules,null!=u[t=r].formoptions&&(n=u[r].formoptions.label);break}}else
0<=t&&(o=u[t].editrules);else
o=i,n=void
0===a?"_":a;if(o){if(n=n||(null!=s.p.colNames?s.p.colNames[t]:u[t].label),!0===o.required&&w.jgrid.isEmpty(e))return[!1,n+": "+c.required,""];a=!1!==o.required;if(!0===o.number&&!(!1==a&&w.jgrid.isEmpty(e)||m(e)))return[!1,n+": "+c.number,""];if(void
0!==o.minValue&&!isNaN(o.minValue)&&parseFloat(e)<parseFloat(o.minValue))return[!1,n+": "+c.minValue+" "+o.minValue,""];if(void
0!==o.maxValue&&!isNaN(o.maxValue)&&parseFloat(e)>parseFloat(o.maxValue))return[!1,n+": "+c.maxValue+" "+o.maxValue,""];if(!0===o.email&&!(!1==a&&w.jgrid.isEmpty(e)||/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?$/i.test(e)))return[!1,n+": "+c.email,""];if(!0===o.integer&&(!1!=a||!w.jgrid.isEmpty(e))){if(!m(e))return[!1,n+": "+c.integer,""];if(e%1!=0||-1!==e.indexOf("."))return[!1,n+": "+c.integer,""]}if(!0===o.date&&!(!1==a&&w.jgrid.isEmpty(e)||(u[t].formatoptions&&u[t].formatoptions.newformat?(d=u[t].formatoptions.newformat,(m=w.jgrid.getRegional(s,"formatter.date.masks"))&&m.hasOwnProperty(d)&&(d=m[d])):d=u[t].datefmt||"Y-m-d",w.jgrid.checkDate(d,e))))return[!1,n+": "+c.date+" - "+d,""];if(!0===o.time&&!(!1==a&&w.jgrid.isEmpty(e)||w.jgrid.checkTime(e)))return[!1,n+": "+c.date+" - hh:mm (am/pm)",""];if(!0===o.url&&!(!1==a&&w.jgrid.isEmpty(e)||/^(((https?)|(ftp)):\/\/([\-\w]+\.)+\w{2,3}(\/[%\-\w]+(\.\w{2,})?)*(([\w\-\.\?\\\/+@&#;`~=%!]*)(\.\w{2,})?)*\/?)/i.test(e)))return[!1,n+": "+c.url,""];if(!0===o.custom&&(!1!=a||!w.jgrid.isEmpty(e))){if(w.jgrid.isFunction(o.custom_func)){e=o.custom_func.call(s,e,n,t);return Array.isArray(e)?e:[!1,c.customarray,""]}return[!1,c.customfcheck,""]}}return[!0,"",""]},validateForm:function(e){for(var
t,i=!0,a=0;a<e.elements.length;a++)if(t=e.elements[a],("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName||"SELECT"===t.nodeName)&&(void
0!==t.willValidate?("INPUT"===t.nodeName&&t.type!==t.getAttribute("type")&&t.setCustomValidity(w.jgrid.LegacyValidation(t)?"":"error"),t.reportValidity()):(t.validity=t.validity||{},t.validity.valid=w.jgrid.LegacyValidation(t)),!t.validity.valid)){i=!1;break}return i},LegacyValidation:function(e){var
t=!0,i=e.value,a=e.getAttribute("type"),o="checkbox"===a||"radio"===a,r=e.getAttribute("required"),n=e.getAttribute("minlength"),d=e.getAttribute("maxlength"),a=e.getAttribute("pattern");return e.disabled||(t=(t=t&&(!r||o&&e.checked||!o&&""!==i))&&(o||(!n||i.length>=n)&&(!d||i.length<=d)))&&a&&(t=(a=new
RegExp(a)).test(i)),t},buildButtons:function(e,i,a){var
o;return w.each(e,function(e,t){t.id||(t.id=w.jgrid.randId()),t.position||(t.position="last"),t.side||(t.side="left"),o=t.icon?" fm-button-icon-"+t.side+"'><span class='"+a.icon_base+" "+t.icon+"'></span>":"'>",o="<a  data-index='"+e+"' id='"+t.id+"' class='fm-button "+a.button+o+t.text+"</a>","last"===t.position?i+=o:i=o+i}),i},setSelNavIndex:function(i,a){var
e=w(".ui-pg-button",i.p.pager);w.each(e,function(e,t){if(a===t)return i.p.navIndex=e,!1}),w(a).attr("tabindex","0")},getFirstVisibleCol:function(e){for(var
t=-1,i=0;i<e.p.colModel.length;i++)if(!0!==e.p.colModel[i].hidden){t=i;break}return t}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base","./grid.common"],e):e(jQuery)}(function(R){"use strict";var
U={};R.jgrid.extend({editGridRow:function(E,M){var
e=R.jgrid.getRegional(this[0],"edit"),t=this[0].p.styleUI,A=R.jgrid.styleUI[t].formedit,B=R.jgrid.styleUI[t].common;return M=R.extend(!0,{top:0,left:0,width:"500",datawidth:"auto",height:"auto",dataheight:"auto",modal:!1,overlay:30,drag:!0,resize:!0,url:null,mtype:"POST",clearAfterAdd:!0,closeAfterEdit:!1,reloadAfterSubmit:!0,onInitializeForm:null,beforeInitData:null,beforeShowForm:null,afterShowForm:null,beforeSubmit:null,afterSubmit:null,onclickSubmit:null,afterComplete:null,onclickPgButtons:null,afterclickPgButtons:null,editData:{},recreateForm:!1,jqModal:!0,closeOnEscape:!1,addedrow:"first",topinfo:"",bottominfo:"",saveicon:[],closeicon:[],savekey:[!1,13],navkeys:[!1,38,40],checkOnSubmit:!1,checkOnUpdate:!1,processing:!1,onClose:null,ajaxEditOptions:{},serializeEditData:null,viewPagerButtons:!0,overlayClass:B.overlay,removemodal:!0,form:"edit",template:null,focusField:!0,editselected:!1,html5Check:!1,buttons:[]},e,M||{}),U[R(this)[0].p.id]=M,this.each(function(){var
u,h,g,t,i,m,f,D,j,y,a,e,d,r,o,n,l,s,c,p,v,b,w,q,x,_=this;function
I(){var
e,t,a={};for(e
in
R(h).find(".FormElement").each(function(){var
e,t=R(".customelement",this);if(t.length){var
t=t[0],i=R(t).attr("name");R.each(_.p.colModel,function(){if(this.name===i&&this.editoptions&&R.jgrid.isFunction(this.editoptions.custom_value)){try{if(m[i]=this.editoptions.custom_value.call(_,R("#"+R.jgrid.jqID(i),h),"get"),void
0===m[i])throw"e1"}catch(e){"e1"===e?R.jgrid.info_dialog(j.errcap,"function 'custom_value' "+U[R(this)[0]].p.msg.novalue,U[R(this)[0]].p.bClose,{styleUI:U[R(this)[0]].p.styleUI}):R.jgrid.info_dialog(j.errcap,e.message,U[R(this)[0]].p.bClose,{styleUI:U[R(this)[0]].p.styleUI})}return!0}})}else{switch(R(this).get(0).type){case"checkbox":R(this).is(":checked")?m[this.name]=R(this).val():(e=R(this).attr("offval"),m[this.name]=e);break;case"select-one":m[this.name]=R(this).val();break;case"select-multiple":m[this.name]=R(this).val(),m[this.name]=m[this.name]?m[this.name].join(","):"";break;case"radio":if(a.hasOwnProperty(this.name))return!0;a[this.name]=void
0===R(this).attr("offval")?"off":R(this).attr("offval");break;default:m[this.name]=R(this).val()}_.p.autoencode&&(m[this.name]=R.jgrid.htmlEncode(m[this.name]))}}),a)a.hasOwnProperty(e)&&(t=R('input[name="'+e+'"]:checked',h).val(),m[e]=void
0!==t?t:a[e],_.p.autoencode&&(m[e]=R.jgrid.htmlEncode(m[e])));return 1}function
k(i,a,d){var
r,o,e,t,n,l=0;(U[_.p.id].checkOnSubmit||U[_.p.id].checkOnUpdate)&&(_.p.savedData={},_.p.savedData[a.p.id+"_id"]=i);var
s=a.p.colModel;if("_empty"===i)return R(s).each(function(){r=this.name,n=R.extend({},this.editoptions||{}),(e=R("#"+R.jgrid.jqID(r),d))&&e.length&&null!==e[0]&&(t="","custom"===this.edittype&&R.jgrid.isFunction(n.custom_value)?n.custom_value.call(_,R("#"+r,d),"set",t):n.defaultValue?(t=R.jgrid.isFunction(n.defaultValue)?n.defaultValue.call(_):n.defaultValue,"checkbox"===e[0].type?(n=t.toLowerCase()).search(/(false|f|0|no|n|off|undefined)/i)<0&&""!==n?(e[0].checked=!0,e[0].defaultChecked=!0,e[0].value=t):(e[0].checked=!1,e[0].defaultChecked=!1):e.val(t)):"checkbox"===e[0].type?(e[0].checked=!1,e[0].defaultChecked=!1,t=R(e).attr("offval")):e[0].type&&"select"===e[0].type.substr(0,6)?e[0].selectedIndex=0:e.val(t),!0!==U[_.p.id].checkOnSubmit&&!U[_.p.id].checkOnUpdate||(_.p.savedData[r]=t))}),void
R("#id_g",d).val(i);var
c=R(a).jqGrid("getInd",i,!0);c&&(R('td[role="gridcell"]',c).each(function(t){if("cb"!==(r=s[t].name)&&"subgrid"!==r&&"rn"!==r&&!0===s[t].editable){if(r===a.p.ExpandColumn&&!0===a.p.treeGrid)o=R(this).text();else
try{o=R.unformat.call(a,R(this),{rowId:i,colModel:s[t]},t)}catch(e){o="textarea"===s[t].edittype?R(this).text():R(this).html()}switch(_.p.autoencode&&(o=R.jgrid.htmlDecode(o)),!0!==U[_.p.id].checkOnSubmit&&!U[_.p.id].checkOnUpdate||(_.p.savedData[r]=o),r=R.jgrid.jqID(r),s[t].edittype){case"select":var
e=o.split(","),e=R.map(e,function(e){return R.jgrid.trim(e)});R("#"+r+" option",d).each(function(){!(s[t].editoptions.multiple||R.jgrid.trim(o)!==R.jgrid.trim(R(this).text())&&e[0]!==R.jgrid.trim(R(this).text())&&e[0]!==R.jgrid.trim(R(this).val()))||s[t].editoptions.multiple&&(-1<R.inArray(R.jgrid.trim(R(this).text()),e)||-1<R.inArray(R.jgrid.trim(R(this).val()),e))?this.selected=!0:this.selected=!1}),!0!==U[_.p.id].checkOnSubmit&&!U[_.p.id].checkOnUpdate||(o=R("#"+r,d).val(),s[t].editoptions.multiple&&(o=o.join(",")),_.p.savedData[r]=o);break;case"checkbox":o=String(o),s[t].editoptions&&s[t].editoptions.value?s[t].editoptions.value.split(":")[0]===o?R("#"+r,d)[_.p.useProp?"prop":"attr"]({checked:!0,defaultChecked:!0}):R("#"+r,d)[_.p.useProp?"prop":"attr"]({checked:!1,defaultChecked:!1}):(o=o.toLowerCase()).search(/(false|f|0|no|n|off|undefined)/i)<0&&""!==o?(R("#"+r,d)[_.p.useProp?"prop":"attr"]("checked",!0),R("#"+r,d)[_.p.useProp?"prop":"attr"]("defaultChecked",!0)):(R("#"+r,d)[_.p.useProp?"prop":"attr"]("checked",!1),R("#"+r,d)[_.p.useProp?"prop":"attr"]("defaultChecked",!1)),!0!==U[_.p.id].checkOnSubmit&&!U[_.p.id].checkOnUpdate||(o=R("#"+r,d).is(":checked")?R("#"+r,d).val():R("#"+r,d).attr("offval"),_.p.savedData[r]=o);break;case"custom":try{if(!s[t].editoptions||!R.jgrid.isFunction(s[t].editoptions.custom_value))throw"e1";s[t].editoptions.custom_value.call(_,R("#"+r,d),"set",o)}catch(e){"e1"===e?R.jgrid.info_dialog(j.errcap,"function 'custom_value' "+U[R(this)[0]].p.msg.nodefined,R.rp_ge[R(this)[0]].p.bClose,{styleUI:U[R(this)[0]].p.styleUI}):R.jgrid.info_dialog(j.errcap,e.message,R.rp_ge[R(this)[0]].p.bClose,{styleUI:U[R(this)[0]].p.styleUI})}break;default:("&nbsp;"===o||"&#160;"===o||1===o.length&&160===o.charCodeAt(0))&&(o=""),R("#"+r,d).val(o)}l++}}),0<l&&(R("#id_g",h).val(i),!0!==U[_.p.id].checkOnSubmit&&!U[_.p.id].checkOnUpdate||(_.p.savedData[a.p.id+"_id"]=i)))}function
C(){var
a,d,r,e,o,t,i,n=[!0,"",""],l={},s=_.p.prmNames,c=R(_).triggerHandler("jqGridAddEditBeforeCheckValues",[m,R(x),f]);if(c&&"object"==typeof
c&&(m=c),R.jgrid.isFunction(U[_.p.id].beforeCheckValues)&&(c=U[_.p.id].beforeCheckValues.call(_,m,R(x),f))&&"object"==typeof
c&&(m=c),!U[_.p.id].html5Check||R.jgrid.validateForm(y[0])){for(e
in
m)if(m.hasOwnProperty(e)&&!1===(n=R.jgrid.checkValues.call(_,m[e],e))[0])break;if(R.each(_.p.colModel,function(e,t){t.editoptions&&!0===t.editoptions.NullIfEmpty&&m.hasOwnProperty(t.name)&&""===m[t.name]&&(m[t.name]="null")}),n[0]&&(void
0===(l=R(_).triggerHandler("jqGridAddEditClickSubmit",[U[_.p.id],m,f]))&&R.jgrid.isFunction(U[_.p.id].onclickSubmit)&&(l=U[_.p.id].onclickSubmit.call(_,U[_.p.id],m,f)||{}),void
0===(n=R(_).triggerHandler("jqGridAddEditBeforeSubmit",[m,R(x),f]))&&(n=[!0,"",""]),n[0]&&R.jgrid.isFunction(U[_.p.id].beforeSubmit)&&(n=U[_.p.id].beforeSubmit.call(_,m,R(x),f))),n[0]&&!U[_.p.id].processing){if(U[_.p.id].processing=!0,R("#sData",h+"_2").addClass(B.active),c=U[_.p.id].url||R(_).jqGrid("getGridParam","editurl"),r=s.oper,d="clientArray"===c?_.p.keyName:s.id,m[r]="_empty"===R.jgrid.trim(m[_.p.id+"_id"])?s.addoper:s.editoper,m[r]===s.addoper&&void
0!==m[d]||(m[d]=m[_.p.id+"_id"]),delete
m[_.p.id+"_id"],m=R.extend(m,U[_.p.id].editData,l),!0===_.p.treeGrid)for(t
in
m[r]===s.addoper&&(o=R(_).jqGrid("getGridParam","selrow"),p="adjacency"===_.p.treeGridModel?_.p.treeReader.parent_id_field:"parent_id",m[p]=o),_.p.treeReader)_.p.treeReader.hasOwnProperty(t)&&(i=_.p.treeReader[t],m.hasOwnProperty(i)&&(m[r]===s.addoper&&"parent_id_field"===t||delete
m[i]));m[d]=R.jgrid.stripPref(_.p.idPrefix,m[d]);var
p=R.extend({url:c,type:U[_.p.id].mtype,data:R.jgrid.isFunction(U[_.p.id].serializeEditData)?U[_.p.id].serializeEditData.call(_,m):m,complete:function(e,t){if(R("#sData",h+"_2").removeClass(B.active),m[d]=_.p.idPrefix+m[d],300<=e.status&&304!==e.status?(n[0]=!1,n[1]=R(_).triggerHandler("jqGridAddEditErrorTextFormat",[e,f]),R.jgrid.isFunction(U[_.p.id].errorTextFormat)?n[1]=U[_.p.id].errorTextFormat.call(_,e,f):n[1]=t+" Status: '"+e.statusText+"'. Error code: "+e.status):(void
0===(n=R(_).triggerHandler("jqGridAddEditAfterSubmit",[e,m,f]))&&(n=[!0,"",""]),n[0]&&R.jgrid.isFunction(U[_.p.id].afterSubmit)&&(n=U[_.p.id].afterSubmit.call(_,e,m,f))),!1===n[0])R(".FormError",x).html(n[1]),R(".FormError",x).show();else
if(_.p.autoencode&&R.each(m,function(e,t){m[e]=R.jgrid.htmlDecode(t)}),m[r]===s.addoper?(n[2]||(n[2]=R.jgrid.randId()),null==m[d]||m[d]===_.p.idPrefix+"_empty"||""===m[d]?m[d]=n[2]:n[2]=m[d],U[_.p.id].reloadAfterSubmit?R(_).trigger("reloadGrid"):!0===_.p.treeGrid?R(_).jqGrid("addChildNode",n[2],o,m):R(_).jqGrid("addRowData",n[2],m,M.addedrow),U[_.p.id].closeAfterAdd?(!0!==_.p.treeGrid&&R(_).jqGrid("setSelection",n[2]),R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form})):U[_.p.id].clearAfterAdd&&k("_empty",_,x)):(U[_.p.id].reloadAfterSubmit?(R(_).trigger("reloadGrid"),U[_.p.id].closeAfterEdit||setTimeout(function(){R(_).jqGrid("setSelection",m[d])},1e3)):!0===_.p.treeGrid?R(_).jqGrid("setTreeRow",m[d],m):R(_).jqGrid("setRowData",m[d],m),U[_.p.id].closeAfterEdit&&R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form})),(R.jgrid.isFunction(U[_.p.id].afterComplete)||Object.prototype.hasOwnProperty.call(R._data(R(_)[0],"events"),"jqGridAddEditAfterComplete"))&&(a=e,setTimeout(function(){R(_).triggerHandler("jqGridAddEditAfterComplete",[a,m,R(x),f]);try{U[_.p.id].afterComplete.call(_,a,m,R(x),f)}catch(e){}a=null},500)),(U[_.p.id].checkOnSubmit||U[_.p.id].checkOnUpdate)&&(R(x).data("disabled",!1),"_empty"!==_.p.savedData[_.p.id+"_id"]))for(var
i
in
_.p.savedData)_.p.savedData.hasOwnProperty(i)&&m[i]&&(_.p.savedData[i]=m[i]);U[_.p.id].processing=!1;try{R(":input:visible",x)[0].focus()}catch(e){}}},R.jgrid.ajaxOptions,U[_.p.id].ajaxEditOptions);p.url||U[_.p.id].useDataProxy||(R.jgrid.isFunction(_.p.dataProxy)?U[_.p.id].useDataProxy=!0:(n[0]=!1,n[1]+=" "+j.nourl)),n[0]&&(U[_.p.id].useDataProxy?(void
0===(c=_.p.dataProxy.call(_,p,"set_"+_.p.id))&&(c=[!0,""]),!1===c[0]?(n[0]=!1,n[1]=c[1]||"Error deleting the selected row!"):(p.data.oper===s.addoper&&U[_.p.id].closeAfterAdd&&R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form}),p.data.oper===s.editoper&&U[_.p.id].closeAfterEdit&&R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form}))):"clientArray"===p.url?(U[_.p.id].reloadAfterSubmit=!1,m=p.data,p.complete({status:200,statusText:""},"")):R.ajax(p))}!1===n[0]&&(R(".FormError",x).html(n[1]),R(".FormError",x).show())}}function
F(e,t){var
i,a=!1;if(!(a=!(R.isPlainObject(e)&&R.isPlainObject(t)&&Object.getOwnPropertyNames(e).length===Object.getOwnPropertyNames(t).length)))for(i
in
t)if(t.hasOwnProperty(i)){if(!e.hasOwnProperty(i)){a=!0;break}if(e[i]!==t[i]){a=!0;break}}return a}function
G(){var
e=!0;return R(".FormError",x).hide(),U[_.p.id].checkOnUpdate&&(m={},I(),F(m,_.p.savedData)&&(R(x).data("disabled",!0),R(".confirm","#"+g.themodal).show(),e=!1)),e}function
O(e,t){var
i=t[1].length-1;0===e||void
0!==t[1][e-1]&&R("#"+R.jgrid.jqID(t[1][e-1])).hasClass(B.disabled)?R("#pData",q).addClass(B.disabled):R("#pData",q).removeClass(B.disabled),e===i||void
0!==t[1][e+1]&&R("#"+R.jgrid.jqID(t[1][e+1])).hasClass(B.disabled)?R("#nData",q).addClass(B.disabled):R("#nData",q).removeClass(B.disabled)}function
S(){var
e=R(_).jqGrid("getDataIDs"),t=R("#id_g",h).val();if(_.p.multiselect&&U[_.p.id].editselected){for(var
i=[],a=0,d=e.length;a<d;a++)-1!==R.inArray(e[a],_.p.selarrrow)&&i.push(e[a]);return[R.inArray(t,i),i]}return[R.inArray(t,e),e]}function
P(){if(U[_.p.id].checkOnSubmit||U[_.p.id].checkOnUpdate){var
e,t,i=[],a={},i=R.map(_.p.savedData,function(e,t){return t});for(e
in
R(".FormElement",y).each(function(){if(""!==R.jgrid.trim(this.name)&&-1===i.indexOf(this.name)){var
e=R(this).val(),t=R(this).get(0).type;if("checkbox"===t)R(this).is(":checked")||(e=R(this).attr("offval"));else
if("select-multiple"===t)e=e.join(",");else
if("radio"===t){if(a.hasOwnProperty(this.name))return!0;a[this.name]=void
0===R(this).attr("offval")?"off":R(this).attr("offval")}_.p.savedData[this.name]=e}}),a)a.hasOwnProperty(e)&&(t=R('input[name="'+e+'"]:checked',y).val(),_.p.savedData[e]=void
0!==t?t:a[e])}}_.grid&&E&&(_.p.savedData={},u=_.p.id,x="FrmGrid_"+u,v="TblGrid_"+u,h="#"+R.jgrid.jqID(v),g={themodal:"editmod"+u,modalhead:"edithd"+u,modalcontent:"editcnt"+u,scrollelm:x},l=!0,t=1,i=0,D="string"==typeof
U[_.p.id].template&&0<U[_.p.id].template.length,j=R.jgrid.getRegional(this,"errors"),U[_.p.id].styleUI=_.p.styleUI||"jQueryUI",R.jgrid.isMobile()&&(U[_.p.id].resize=!1),"new"===E?(E="_empty",f="add",M.caption=U[_.p.id].addCaption):(M.caption=U[_.p.id].editCaption,f="edit"),M.recreateForm||R(_).data("formProp")&&R.extend(U[R(this)[0].p.id],R(_).data("formProp")),w=!0,M.checkOnUpdate&&M.jqModal&&!M.modal&&(w=!1),n=isNaN(U[R(this)[0].p.id].dataheight)?U[R(this)[0].p.id].dataheight:U[R(this)[0].p.id].dataheight+"px",o=isNaN(U[R(this)[0].p.id].datawidth)?U[R(this)[0].p.id].datawidth:U[R(this)[0].p.id].datawidth+"px",y=R("<form name='FormPost' id='"+x+"' class='FormGrid' onSubmit='return false;' style='width:"+o+";height:"+n+";'></form>").data("disabled",!1),q=D?(e=U[R(this)[0].p.id].template,d="","string"==typeof
e&&(d=e.replace(/\{([\w\-]+)(?:\:([\w\.]*)(?:\((.*?)?\))?)?\}/g,function(e,t){return'<span id="'+t+'" ></span>'})),a=d,h):(a=R("<table id='"+v+"' class='EditTable ui-common-table'><tbody></tbody></table>"),h+"_2"),x="#"+R.jgrid.jqID(x),R(y).append("<div class='FormError "+B.error+"' style='display:none;'></div>"),R(y).append("<div class='tinfo topinfo'>"+U[_.p.id].topinfo+"</div>"),R(_.p.colModel).each(function(){var
e=this.formoptions;t=Math.max(t,e&&e.colpos||0),i=Math.max(i,e&&e.rowpos||0)}),R(y).append(a),void
0===(l=R(_).triggerHandler("jqGridAddEditBeforeInitData",[y,f]))&&(l=!0),l&&R.jgrid.isFunction(U[_.p.id].beforeInitData)&&(l=U[_.p.id].beforeInitData.call(_,y,f)),!1!==l&&(function(){var
e;if("_empty"!==E&&void
0!==_.p.savedRow&&0<_.p.savedRow.length&&R.jgrid.isFunction(R.fn.jqGrid.restoreRow))for(e=0;e<_.p.savedRow.length;e++)if(_.p.savedRow[e].id===E){R(_).jqGrid("restoreRow",E);break}}(),function(o,n,l,s){for(var
c,p,u,h,g,m,f,e,j=0,v=[],b=!1,w="",t=1;t<=s;t++)w+="<td class='CaptionTD'></td><td class='DataTD'></td>";"_empty"!==o&&(b=R(n).jqGrid("getInd",o)),R(n.p.colModel).each(function(t){if(c=this.name,p=(!this.editrules||!0!==this.editrules.edithidden)&&!0===this.hidden,g=p?"style='display:none'":"","cb"!==c&&"subgrid"!==c&&!0===this.editable&&"rn"!==c){if(!1===b)h="";else
if(c===n.p.ExpandColumn&&!0===n.p.treeGrid)h=R("td[role='gridcell']",n.rows[b]).eq(t).text();else{try{h=R.unformat.call(n,R("td[role='gridcell']",n.rows[b]).eq(t),{rowId:o,colModel:this},t)}catch(e){h=this.edittype&&"textarea"===this.edittype?R("td[role='gridcell']",n.rows[b]).eq(t).text():R("td[role='gridcell']",n.rows[b]).eq(t).html()}h&&"&nbsp;"!==h&&"&#160;"!==h&&(1!==h.length||160!==h.charCodeAt(0))||(h="")}var
e,i=R.extend({},this.editoptions||{},{id:c,name:c,rowId:o,oper:"edit",module:"form",checkUpdate:U[_.p.id].checkOnSubmit||U[_.p.id].checkOnUpdate}),a=R.extend({},{elmprefix:"",elmsuffix:"",rowabove:!1,rowcontent:""},this.formoptions||{}),d=parseInt(a.rowpos,10)||j+1,r=parseInt(2*(parseInt(a.colpos,10)||1),10);"_empty"===o&&i.defaultValue&&(h=R.jgrid.isFunction(i.defaultValue)?i.defaultValue.call(_):i.defaultValue),this.edittype||(this.edittype="text"),_.p.autoencode&&(h=R.jgrid.htmlDecode(h)),m=R.jgrid.createEl.call(_,this.edittype,i,h,!1,R.extend({},R.jgrid.ajaxOptions,n.p.ajaxSelectOptions||{})),"select"===this.edittype&&(h=R(m).val(),"select-multiple"===R(m).get(0).type&&h&&(h=h.join(","))),"checkbox"===this.edittype&&(h=R(m).is(":checked")?R(m).val():R(m).attr("offval")),R(m).addClass("FormElement"),-1<R.inArray(this.edittype,["text","textarea","password","select","color","date","datetime","datetime-local","email","month","number","range","search","tel","time","url","week"])&&R(m).addClass(A.inputClass),f=!0,D?(e=R(y).find("#"+c)).length?e.replaceWith(m):f=!1:(u=R(l).find("tr[rowpos="+d+"]"),a.rowabove&&(e=R("<tr><td class='contentinfo' colspan='"+2*s+"'>"+a.rowcontent+"</td></tr>"),R(l).append(e),e[0].rp=d),0===u.length&&(u=R(1<s?"<tr rowpos='"+d+"'></tr>":"<tr "+g+" rowpos='"+d+"'></tr>").addClass("FormData").attr("id","tr_"+c),R(u).append(w),R(l).append(u),u[0].rp=d),R("td",u[0]).eq(r-2).html("<label for='"+c+"'>"+(void
0===a.label?n.p.colNames[t]:a.label)+"</label>"),R("td",u[0]).eq(r-1).append(a.elmprefix).append(m).append(a.elmsuffix),1<s&&p&&(R("td",u[0]).eq(r-2).hide(),R("td",u[0]).eq(r-1).hide())),(U[_.p.id].checkOnSubmit||U[_.p.id].checkOnUpdate)&&f&&(_.p.savedData[c]=h),"custom"===this.edittype&&R.jgrid.isFunction(i.custom_value)&&i.custom_value.call(_,R("#"+c,x),"set",h),R.jgrid.bindEv.call(_,m,i),v[j]=t,j++}}),0<j&&(D?(e="<div class='FormData' style='display:none'><input class='FormElement' id='id_g' type='text' name='"+n.p.id+"_id' value='"+o+"'/>",R(y).append(e)):((e=R("<tr class='FormData' style='display:none'><td class='CaptionTD'></td><td colspan='"+(2*s-1)+"' class='DataTD'><input class='FormElement' id='id_g' type='text' name='"+n.p.id+"_id' value='"+o+"'/></td></tr>"))[0].rp=j+999,R(l).append(e)),(U[_.p.id].checkOnSubmit||U[_.p.id].checkOnUpdate)&&(_.p.savedData[n.p.id+"_id"]=o))}(E,_,a,t),b=(r="rtl"===_.p.direction)?"pData":"nData",p="<a id='"+(r?"nData":"pData")+"' class='fm-button "+B.button+"'><span class='"+B.icon_base+" "+A.icon_prev+"'></span></a>",o="<a id='"+b+"' class='fm-button "+B.button+"'><span class='"+B.icon_base+" "+A.icon_next+"'></span></a>",n="<a id='sData' class='fm-button "+B.button+"'>"+M.bSubmit+"</a>",l="<a id='cData' class='fm-button "+B.button+"'>"+M.bCancel+"</a>",b=Array.isArray(U[_.p.id].buttons)?R.jgrid.buildButtons(U[_.p.id].buttons,n+l,B):n+l,b="<table style='height:auto' class='EditTable ui-common-table' id='"+v+"_2'><tbody><tr><td colspan='2'><hr class='"+B.content+"' style='margin:1px'/></td></tr><tr id='Act_Buttons'><td class='navButton'>"+(r?o+p:p+o)+"</td><td class='EditButton'>"+b+"</td></tr>",b+="</tbody></table>",0<i&&(s=[],R.each(R(a)[0].rows,function(e,t){s[e]=t}),s.sort(function(e,t){return e.rp>t.rp?1:e.rp<t.rp?-1:0}),R.each(s,function(e,t){R("tbody",a).append(t)})),M.gbox="#gbox_"+R.jgrid.jqID(u),!(c=!1)===M.closeOnEscape&&(M.closeOnEscape=!1,c=!0),p=D?(R(y).find("#pData").replaceWith(p),R(y).find("#nData").replaceWith(o),R(y).find("#sData").replaceWith(n),R(y).find("#cData").replaceWith(l),R("<div id="+v+"></div>").append(y)):R("<div></div>").append(y).append(b),R(y).append("<div class='binfo topinfo bottominfo'>"+U[_.p.id].bottominfo+"</div>"),v=R(".ui-jqgrid").css("font-size")||"11px",R.jgrid.createModal(g,p,U[R(this)[0].p.id],"#gview_"+R.jgrid.jqID(_.p.id),R("#gbox_"+R.jgrid.jqID(_.p.id))[0],null,{"font-size":v}),r&&(R("#pData, #nData",h+"_2").css("float","right"),R(".EditButton",h+"_2").css("text-align","left")),U[_.p.id].topinfo&&R(".tinfo",x).show(),U[_.p.id].bottominfo&&R(".binfo",x).show(),b=p=null,R("#"+R.jgrid.jqID(g.themodal)).keydown(function(e){var
t=e.target;return!0!==R(x).data("disabled")&&(!0===U[_.p.id].savekey[0]&&e.which===U[_.p.id].savekey[1]&&"TEXTAREA"!==t.tagName?(R("#sData",h+"_2").trigger("click"),!1):27===e.which?(G()&&c&&R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:M.gbox,jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form}),!1):!0===U[_.p.id].navkeys[0]?"_empty"===R("#id_g",h).val()||(e.which===U[_.p.id].navkeys[1]?(R("#pData",q).trigger("click"),!1):e.which===U[_.p.id].navkeys[2]?(R("#nData",q).trigger("click"),!1):void
0):void
0)}),M.checkOnUpdate&&(R("a.ui-jqdialog-titlebar-close span","#"+R.jgrid.jqID(g.themodal)).removeClass("jqmClose"),R("a.ui-jqdialog-titlebar-close","#"+R.jgrid.jqID(g.themodal)).off("click").click(function(){return G()&&R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form}),!1})),M.saveicon=R.extend([!0,"left",A.icon_save],M.saveicon),M.closeicon=R.extend([!0,"left",A.icon_close],M.closeicon),!0===M.saveicon[0]&&R("#sData",q).addClass("right"===M.saveicon[1]?"fm-button-icon-right":"fm-button-icon-left").append("<span class='"+B.icon_base+" "+M.saveicon[2]+"'></span>"),!0===M.closeicon[0]&&R("#cData",q).addClass("right"===M.closeicon[1]?"fm-button-icon-right":"fm-button-icon-left").append("<span class='"+B.icon_base+" "+M.closeicon[2]+"'></span>"),(U[_.p.id].checkOnSubmit||U[_.p.id].checkOnUpdate)&&(n="<a id='sNew' class='fm-button "+B.button+"' style='z-index:1002'>"+M.bYes+"</a>",o="<a id='nNew' class='fm-button "+B.button+"' style='z-index:1002;margin-left:5px'>"+M.bNo+"</a>",l="<a id='cNew' class='fm-button "+B.button+"' style='z-index:1002;margin-left:5px;'>"+M.bExit+"</a>",b=M.zIndex||999,b++,R("#"+g.themodal).append("<div class='"+M.overlayClass+" jqgrid-overlay confirm' style='z-index:"+b+";display:none;position:absolute;'>&#160;</div><div class='confirm ui-jqconfirm "+B.content+"' style='z-index:"+(1+b)+"'>"+M.saveData+"<br/><br/>"+n+o+l+"</div>"),R("#sNew","#"+R.jgrid.jqID(g.themodal)).click(function(){return C(),R(x).data("disabled",!1),R(".confirm","#"+R.jgrid.jqID(g.themodal)).hide(),!1}),R("#nNew","#"+R.jgrid.jqID(g.themodal)).click(function(){return R(".confirm","#"+R.jgrid.jqID(g.themodal)).hide(),R(x).data("disabled",!1),setTimeout(function(){R(":input:visible",x)[0].focus()},0),!1}),R("#cNew","#"+R.jgrid.jqID(g.themodal)).click(function(){return R(".confirm","#"+R.jgrid.jqID(g.themodal)).hide(),R(x).data("disabled",!1),R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form}),!1})),R(_).triggerHandler("jqGridAddEditInitializeForm",[R(x),f]),R.jgrid.isFunction(U[_.p.id].onInitializeForm)&&U[_.p.id].onInitializeForm.call(_,R(x),f),"_empty"!==E&&U[_.p.id].viewPagerButtons?R("#pData,#nData",q).show():R("#pData,#nData",q).hide(),R(_).triggerHandler("jqGridAddEditBeforeShowForm",[R(x),f]),R.jgrid.isFunction(U[_.p.id].beforeShowForm)&&U[_.p.id].beforeShowForm.call(_,R(x),f),P(),R("#"+R.jgrid.jqID(g.themodal)).data("onClose",U[_.p.id].onClose),R.jgrid.viewModal("#"+R.jgrid.jqID(g.themodal),{gbox:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,overlay:M.overlay,modal:M.modal,overlayClass:M.overlayClass,focusField:M.focusField,onHide:function(e){var
t=R.jgrid.floatNum(R("#editmod"+u)[0].style.width),i="rtl"===R("#gbox_"+R.jgrid.jqID(u)).attr("dir");R(_).data("formProp",{top:R.jgrid.floatNum(R(e.w).css("top")),left:i?R("#gbox_"+R.jgrid.jqID(u)).outerWidth()-t-parseFloat(R(e.w).css("left"))+12:parseFloat(R(e.w).css("left")),width:t,height:R.jgrid.floatNum(R("#editmod"+u)[0].style.height),dataheight:R(x).height(),datawidth:R(x).width()}),e.w.remove(),e.o&&e.o.remove()}}),w||R("."+R.jgrid.jqID(M.overlayClass)).click(function(){return G()&&R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form}),!1}),R(".fm-button","#"+R.jgrid.jqID(g.themodal)).hover(function(){R(this).addClass(B.hover)},function(){R(this).removeClass(B.hover)}),R("#sData",q).click(function(){return m={},R(".FormError",x).hide(),I(),"_empty"!==m[_.p.id+"_id"]&&!0===M.checkOnSubmit&&F(m,_.p.savedData)?(R(x).data("disabled",!0),R(".confirm","#"+R.jgrid.jqID(g.themodal)).show()):C(),!1}),R("#cData",q).click(function(){return G()&&R.jgrid.hideModal("#"+R.jgrid.jqID(g.themodal),{gb:"#gbox_"+R.jgrid.jqID(u),jqm:M.jqModal,onClose:U[_.p.id].onClose,removemodal:U[_.p.id].removemodal,formprop:!U[_.p.id].recreateForm,form:U[_.p.id].form}),!1}),R(q).find("[data-index]").each(function(){var
t=parseInt(R(this).attr("data-index"),10);0<=t&&M.buttons[t].hasOwnProperty("click")&&R(this).on("click",function(e){M.buttons[t].click.call(_,R(x)[0],U[_.p.id],e)})}),R("#nData",q).click(function(){if(!G())return!1;R(".FormError",x).hide();var
e,t=S();if(t[0]=parseInt(t[0],10),-1!==t[0]&&t[1][t[0]+1]){if(R(_).triggerHandler("jqGridAddEditClickPgButtons",["next",R(x),t[1][t[0]]]),R.jgrid.isFunction(M.onclickPgButtons)&&void
0!==(e=M.onclickPgButtons.call(_,"next",R(x),t[1][t[0]]))&&!1===e)return!1;if(R("#"+R.jgrid.jqID(t[1][t[0]+1])).hasClass(B.disabled))return!1;k(t[1][t[0]+1],_,x),_.p.multiselect&&U[_.p.id].editselected||R(_).jqGrid("setSelection",t[1][t[0]+1]),R(_).triggerHandler("jqGridAddEditAfterClickPgButtons",["next",R(x),t[1][t[0]]]),R.jgrid.isFunction(M.afterclickPgButtons)&&M.afterclickPgButtons.call(_,"next",R(x),t[1][t[0]+1]),P(),O(t[0]+1,t)}return!1}),R("#pData",q).click(function(){if(!G())return!1;R(".FormError",x).hide();var
e,t=S();if(-1!==t[0]&&t[1][t[0]-1]){if(R(_).triggerHandler("jqGridAddEditClickPgButtons",["prev",R(x),t[1][t[0]]]),R.jgrid.isFunction(M.onclickPgButtons)&&void
0!==(e=M.onclickPgButtons.call(_,"prev",R(x),t[1][t[0]]))&&!1===e)return!1;if(R("#"+R.jgrid.jqID(t[1][t[0]-1])).hasClass(B.disabled))return!1;k(t[1][t[0]-1],_,x),_.p.multiselect&&U[_.p.id].editselected||R(_).jqGrid("setSelection",t[1][t[0]-1]),R(_).triggerHandler("jqGridAddEditAfterClickPgButtons",["prev",R(x),t[1][t[0]]]),R.jgrid.isFunction(M.afterclickPgButtons)&&M.afterclickPgButtons.call(_,"prev",R(x),t[1][t[0]-1]),P(),O(t[0]-1,t)}return!1}),R(_).triggerHandler("jqGridAddEditAfterShowForm",[R(x),f]),R.jgrid.isFunction(U[_.p.id].afterShowForm)&&U[_.p.id].afterShowForm.call(_,R(x),f),O((w=S())[0],w)))})},viewGridRow:function(b,D){var
e=R.jgrid.getRegional(this[0],"view"),t=this[0].p.styleUI,w=R.jgrid.styleUI[t].formedit,y=R.jgrid.styleUI[t].common;return D=R.extend(!0,{top:0,left:0,width:500,datawidth:"auto",height:"auto",dataheight:"auto",modal:!1,overlay:30,drag:!0,resize:!0,jqModal:!0,closeOnEscape:!1,labelswidth:"auto",closeicon:[],navkeys:[!1,38,40],onClose:null,beforeShowForm:null,beforeInitData:null,viewPagerButtons:!0,recreateForm:!1,removemodal:!0,form:"view",buttons:[]},e,D||{}),U[R(this)[0].p.id]=D,this.each(function(){var
a,d,n,i,t,r,o,e,l,s,c,p,u,h,g=this;function
m(){!0!==U[g.p.id].closeOnEscape&&!0!==U[g.p.id].navkeys[0]||setTimeout(function(){R(".ui-jqdialog-titlebar-close","#"+R.jgrid.jqID(t.modalhead)).attr("tabindex","-1").focus()},0)}function
f(e,t){var
i,a,d,r=0,o=R(t).jqGrid("getInd",e,!0);o&&(R("td",o).each(function(e){i=t.p.colModel[e].name,a=(!t.p.colModel[e].editrules||!0!==t.p.colModel[e].editrules.edithidden)&&!0===t.p.colModel[e].hidden,"cb"!==i&&"subgrid"!==i&&"rn"!==i&&(d=i===t.p.ExpandColumn&&!0===t.p.treeGrid?R(this).text():R(this).html(),i=R.jgrid.jqID("v_"+i),R("#"+i+" span","#"+n).html(d),a&&R("#"+i,"#"+n).parents("tr").first().hide(),r++)}),0<r&&R("#id_g","#"+n).val(e))}function
j(e,t){var
i=t[1].length-1;0===e?R("#pData","#"+n+"_2").addClass(y.disabled):void
0!==t[1][e-1]&&R("#"+R.jgrid.jqID(t[1][e-1])).hasClass(y.disabled)?R("#pData",n+"_2").addClass(y.disabled):R("#pData","#"+n+"_2").removeClass(y.disabled),e===i?R("#nData","#"+n+"_2").addClass(y.disabled):void
0!==t[1][e+1]&&R("#"+R.jgrid.jqID(t[1][e+1])).hasClass(y.disabled)?R("#nData",n+"_2").addClass(y.disabled):R("#nData","#"+n+"_2").removeClass(y.disabled)}function
v(){var
e=R(g).jqGrid("getDataIDs"),t=R("#id_g","#"+n).val();if(g.p.multiselect&&U[g.p.id].viewselected){for(var
i=[],a=0,d=e.length;a<d;a++)-1!==R.inArray(e[a],g.p.selarrrow)&&i.push(e[a]);return[R.inArray(t,i),i]}return[R.inArray(t,e),e]}g.grid&&b&&(a=g.p.id,d="ViewGrid_"+R.jgrid.jqID(a),n="ViewTbl_"+R.jgrid.jqID(a),i="ViewGrid_"+a,c="ViewTbl_"+a,t={themodal:"viewmod"+a,modalhead:"viewhd"+a,modalcontent:"viewcnt"+a,scrollelm:d},h=!0,r=1,o=0,U[g.p.id].styleUI=g.p.styleUI||"jQueryUI",D.recreateForm||R(g).data("viewProp")&&R.extend(U[R(this)[0].p.id],R(g).data("viewProp")),u=isNaN(U[R(this)[0].p.id].dataheight)?U[R(this)[0].p.id].dataheight:U[R(this)[0].p.id].dataheight+"px",s=isNaN(U[R(this)[0].p.id].datawidth)?U[R(this)[0].p.id].datawidth:U[R(this)[0].p.id].datawidth+"px",e=R("<form name='FormPost' id='"+i+"' class='FormGrid' style='width:"+s+";height:"+u+";'></form>"),l=R("<table id='"+c+"' class='EditTable ViewTable'><tbody></tbody></table>"),R(g.p.colModel).each(function(){var
e=this.formoptions;r=Math.max(r,e&&e.colpos||0),o=Math.max(o,e&&e.rowpos||0)}),R(e).append(l),void
0===(h=R(g).triggerHandler("jqGridViewRowBeforeInitData",[e]))&&(h=!0),h&&R.jgrid.isFunction(U[g.p.id].beforeInitData)&&(h=U[g.p.id].beforeInitData.call(g,e)),!1!==h&&(function(e,r,o,n){for(var
l,s,c,p,u,h,g,m,f=0,j=[],t="<td class='CaptionTD form-view-label "+y.content+"' width='"+D.labelswidth+"'></td><td class='DataTD form-view-data ui-helper-reset "+y.content+"'></td>",v="",i="<td class='CaptionTD form-view-label "+y.content+"'></td><td class='DataTD form-view-data "+y.content+"'></td>",a=["integer","number","currency"],d=0,b=0,w=1;w<=n;w++)v+=1===w?t:i;R(r.p.colModel).each(function(){(s=(!this.editrules||!0!==this.editrules.edithidden)&&!0===this.hidden)||"right"!==this.align||(this.formatter&&-1!==R.inArray(this.formatter,a)?d=Math.max(d,parseInt(this.width,10)):b=Math.max(b,parseInt(this.width,10)))}),h=0!==d?d:0!==b?b:0,u=R(r).jqGrid("getInd",e),R(r.p.colModel).each(function(e){var
t,i,a,d;l=this.name,s=(!this.editrules||!0!==this.editrules.edithidden)&&!0===this.hidden,p=s?"style='display:none'":"",m="boolean"!=typeof
this.viewable||this.viewable,"cb"!==l&&"subgrid"!==l&&"rn"!==l&&m&&(c=!1===u?"":l===r.p.ExpandColumn&&!0===r.p.treeGrid?R("td",r.rows[u]).eq(e).text():R("td",r.rows[u]).eq(e).html(),g="right"===this.align&&0!==h,t=R.extend({},{rowabove:!1,rowcontent:""},this.formoptions||{}),i=parseInt(t.rowpos,10)||f+1,a=parseInt(2*(parseInt(t.colpos,10)||1),10),t.rowabove&&(d=R("<tr><td class='contentinfo' colspan='"+2*n+"'>"+t.rowcontent+"</td></tr>"),R(o).append(d),d[0].rp=i),0===(m=R(o).find("tr[rowpos="+i+"]")).length&&(m=R("<tr "+p+" rowpos='"+i+"'></tr>").addClass("FormData").attr("id","trv_"+l),R(m).append(v),R(o).append(m),m[0].rp=i),R("td",m[0]).eq(a-2).html("<b>"+(void
0===t.label?r.p.colNames[e]:t.label)+"</b>"),R("td",m[0]).eq(a-1).append("<span>"+c+"</span>").attr("id","v_"+l),g&&R("td",m[0]).eq(a-1).find("span").css({"text-align":"right",width:h+"px"}),j[f]=e,f++)}),0<f&&((e=R("<tr class='FormData' style='display:none'><td class='CaptionTD'></td><td colspan='"+(2*n-1)+"' class='DataTD'><input class='FormElement' id='id_g' type='text' name='id' value='"+e+"'/></td></tr>"))[0].rp=f+99,R(o).append(e))}(b,g,l,r),u=(s="rtl"===g.p.direction)?"pData":"nData",c="<a id='"+(s?"nData":"pData")+"' class='fm-button "+y.button+"'><span class='"+y.icon_base+" "+w.icon_prev+"'></span></a>",h="<a id='"+u+"' class='fm-button "+y.button+"'><span class='"+y.icon_base+" "+w.icon_next+"'></span></a>",u="<a id='cData' class='fm-button "+y.button+"'>"+D.bClose+"</a>",u=Array.isArray(U[g.p.id].buttons)?R.jgrid.buildButtons(U[g.p.id].buttons,u,y):u,0<o&&(p=[],R.each(R(l)[0].rows,function(e,t){p[e]=t}),p.sort(function(e,t){return e.rp>t.rp?1:e.rp<t.rp?-1:0}),R.each(p,function(e,t){R("tbody",l).append(t)})),D.gbox="#gbox_"+R.jgrid.jqID(a),h=R("<div></div>").append(e).append("<table border='0' class='EditTable' id='"+n+"_2'><tbody><tr id='Act_Buttons'><td class='navButton' width='"+D.labelswidth+"'>"+(s?h+c:c+h)+"</td><td class='EditButton'>"+u+"</td></tr></tbody></table>"),u=R(".ui-jqgrid").css("font-size")||"11px",R.jgrid.createModal(t,h,U[R(this)[0].p.id],"#gview_"+R.jgrid.jqID(g.p.id),R("#gview_"+R.jgrid.jqID(g.p.id))[0],null,{"font-size":u}),s&&(R("#pData, #nData","#"+n+"_2").css("float","right"),R(".EditButton","#"+n+"_2").css("text-align","left")),D.viewPagerButtons||R("#pData, #nData","#"+n+"_2").hide(),h=null,R("#"+t.themodal).keydown(function(e){return 27===e.which?(U[g.p.id].closeOnEscape&&R.jgrid.hideModal("#"+R.jgrid.jqID(t.themodal),{gb:D.gbox,jqm:D.jqModal,onClose:D.onClose,removemodal:U[g.p.id].removemodal,formprop:!U[g.p.id].recreateForm,form:U[g.p.id].form}),!1):!0===D.navkeys[0]?e.which===D.navkeys[1]?(R("#pData","#"+n+"_2").trigger("click"),!1):e.which===D.navkeys[2]?(R("#nData","#"+n+"_2").trigger("click"),!1):void
0:void
0}),D.closeicon=R.extend([!0,"left",w.icon_close],D.closeicon),!0===D.closeicon[0]&&R("#cData","#"+n+"_2").addClass("right"===D.closeicon[1]?"fm-button-icon-right":"fm-button-icon-left").append("<span class='"+y.icon_base+" "+D.closeicon[2]+"'></span>"),R(g).triggerHandler("jqGridViewRowBeforeShowForm",[R("#"+d)]),R.jgrid.isFunction(D.beforeShowForm)&&D.beforeShowForm.call(g,R("#"+d)),R.jgrid.viewModal("#"+R.jgrid.jqID(t.themodal),{gbox:"#gbox_"+R.jgrid.jqID(a),jqm:D.jqModal,overlay:D.overlay,modal:D.modal,onHide:function(e){var
t="rtl"===R("#gbox_"+R.jgrid.jqID(a)).attr("dir"),i=parseFloat(R("#viewmod"+a)[0].style.width);R(g).data("viewProp",{top:parseFloat(R(e.w).css("top")),left:t?R("#gbox_"+R.jgrid.jqID(a)).outerWidth()-i-parseFloat(R(e.w).css("left"))+12:parseFloat(R(e.w).css("left")),width:R(e.w).width(),height:R(e.w).height(),dataheight:R("#"+d).height(),datawidth:R("#"+d).width()}),e.w.remove(),e.o&&e.o.remove()}}),R(".fm-button:not(."+y.disabled+")","#"+n+"_2").hover(function(){R(this).addClass(y.hover)},function(){R(this).removeClass(y.hover)}),m(),R("#cData","#"+n+"_2").click(function(){return R.jgrid.hideModal("#"+R.jgrid.jqID(t.themodal),{gb:"#gbox_"+R.jgrid.jqID(a),jqm:D.jqModal,onClose:D.onClose,removemodal:U[g.p.id].removemodal,formprop:!U[g.p.id].recreateForm,form:U[g.p.id].form}),!1}),R("#"+n+"_2").find("[data-index]").each(function(){var
t=parseInt(R(this).attr("data-index"),10);0<=t&&D.buttons[t].hasOwnProperty("click")&&R(this).on("click",function(e){D.buttons[t].click.call(g,R("#"+i)[0],U[g.p.id],e)})}),R("#nData","#"+n+"_2").click(function(){R("#FormError","#"+n).hide();var
e=v();return e[0]=parseInt(e[0],10),-1!==e[0]&&e[1][e[0]+1]&&(R(g).triggerHandler("jqGridViewRowClickPgButtons",["next",R("#"+d),e[1][e[0]]]),R.jgrid.isFunction(D.onclickPgButtons)&&D.onclickPgButtons.call(g,"next",R("#"+d),e[1][e[0]]),f(e[1][e[0]+1],g),g.p.multiselect&&U[g.p.id].viewselected||R(g).jqGrid("setSelection",e[1][e[0]+1]),R(g).triggerHandler("jqGridViewRowAfterClickPgButtons",["next",R("#"+d),e[1][e[0]+1]]),R.jgrid.isFunction(D.afterclickPgButtons)&&D.afterclickPgButtons.call(g,"next",R("#"+d),e[1][e[0]+1]),j(e[0]+1,e)),m(),!1}),R("#pData","#"+n+"_2").click(function(){R("#FormError","#"+n).hide();var
e=v();return-1!==e[0]&&e[1][e[0]-1]&&(R(g).triggerHandler("jqGridViewRowClickPgButtons",["prev",R("#"+d),e[1][e[0]]]),R.jgrid.isFunction(D.onclickPgButtons)&&D.onclickPgButtons.call(g,"prev",R("#"+d),e[1][e[0]]),f(e[1][e[0]-1],g),g.p.multiselect&&U[g.p.id].viewselected||R(g).jqGrid("setSelection",e[1][e[0]-1]),R(g).triggerHandler("jqGridViewRowAfterClickPgButtons",["prev",R("#"+d),e[1][e[0]-1]]),R.jgrid.isFunction(D.afterclickPgButtons)&&D.afterclickPgButtons.call(g,"prev",R("#"+d),e[1][e[0]-1]),j(e[0]-1,e)),m(),!1}),j((h=v())[0],h)))})},delGridRow:function(h,g){var
e=R.jgrid.getRegional(this[0],"del"),t=this[0].p.styleUI,m=R.jgrid.styleUI[t].formedit,f=R.jgrid.styleUI[t].common;return g=R.extend(!0,{top:0,left:0,width:240,height:"auto",dataheight:"auto",modal:!1,overlay:30,drag:!0,resize:!0,url:"",mtype:"POST",reloadAfterSubmit:!0,beforeShowForm:null,beforeInitData:null,afterShowForm:null,beforeSubmit:null,onclickSubmit:null,afterSubmit:null,jqModal:!0,closeOnEscape:!1,delData:{},delicon:[],cancelicon:[],onClose:null,ajaxDelOptions:{},processing:!1,serializeDelData:null,useDataProxy:!1},e,g||{}),U[R(this)[0].p.id]=g,this.each(function(){var
o=this;if(o.grid&&h){var
n,a,l,s=o.p.id,c={},e=!0,p="DelTbl_"+R.jgrid.jqID(s),i="DelTbl_"+s,u={themodal:"delmod"+s,modalhead:"delhd"+s,modalcontent:"delcnt"+s,scrollelm:p};if(U[o.p.id].styleUI=o.p.styleUI||"jQueryUI",Array.isArray(h)&&(h=h.join()),void
0!==R("#"+R.jgrid.jqID(u.themodal))[0]){if(void
0===(e=R(o).triggerHandler("jqGridDelRowBeforeInitData",[R("#"+p)]))&&(e=!0),e&&R.jgrid.isFunction(U[o.p.id].beforeInitData)&&(e=U[o.p.id].beforeInitData.call(o,R("#"+p))),!1===e)return;R("#DelData>td","#"+p).text(h),R("#DelError","#"+p).hide(),!0===U[o.p.id].processing&&(U[o.p.id].processing=!1,R("#dData","#"+p).removeClass(f.active)),R(o).triggerHandler("jqGridDelRowBeforeShowForm",[R("#"+p)]),R.jgrid.isFunction(U[o.p.id].beforeShowForm)&&U[o.p.id].beforeShowForm.call(o,R("#"+p)),R.jgrid.viewModal("#"+R.jgrid.jqID(u.themodal),{gbox:"#gbox_"+R.jgrid.jqID(s),jqm:U[o.p.id].jqModal,overlay:U[o.p.id].overlay,modal:U[o.p.id].modal}),R(o).triggerHandler("jqGridDelRowAfterShowForm",[R("#"+p)]),R.jgrid.isFunction(U[o.p.id].afterShowForm)&&U[o.p.id].afterShowForm.call(o,R("#"+p))}else{var
t=isNaN(U[o.p.id].dataheight)?U[o.p.id].dataheight:U[o.p.id].dataheight+"px",d=isNaN(g.datawidth)?g.datawidth:g.datawidth+"px",r="<div id='"+i+"' class='formdata' style='width:"+d+";overflow:auto;position:relative;height:"+t+";'>";r+="<table class='DelTable'><tbody>",r+="<tr id='DelError' style='display:none'><td class='"+f.error+"'></td></tr>",r+="<tr id='DelData' style='display:none'><td >"+h+"</td></tr>",r+='<tr><td class="delmsg" style="white-space:pre;">'+U[o.p.id].msg+"</td></tr><tr><td >&#160;</td></tr>",r+="</tbody></table></div>";d="<a id='dData' class='fm-button "+f.button+"'>"+g.bSubmit+"</a>",t="<a id='eData' class='fm-button "+f.button+"'>"+g.bCancel+"</a>",d=Array.isArray(U[o.p.id].buttons)?R.jgrid.buildButtons(U[o.p.id].buttons,d+t,f):d+t,t=R(".ui-jqgrid").css("font-size")||"11px";if(r+="<table class='EditTable ui-common-table' id='"+p+"_2'><tbody><tr><td><hr class='"+f.content+"' style='margin:1px'/></td></tr><tr><td class='DelButton EditButton'>"+d+"</td></tr></tbody></table>",g.gbox="#gbox_"+R.jgrid.jqID(s),R.jgrid.createModal(u,r,U[o.p.id],"#gview_"+R.jgrid.jqID(o.p.id),R("#gview_"+R.jgrid.jqID(o.p.id))[0],null,{"font-size":t}),R(".fm-button","#"+p+"_2").hover(function(){R(this).addClass(f.hover)},function(){R(this).removeClass(f.hover)}),g.delicon=R.extend([!0,"left",m.icon_del],U[o.p.id].delicon),g.cancelicon=R.extend([!0,"left",m.icon_cancel],U[o.p.id].cancelicon),!0===g.delicon[0]&&R("#dData","#"+p+"_2").addClass("right"===g.delicon[1]?"fm-button-icon-right":"fm-button-icon-left").append("<span class='"+f.icon_base+" "+g.delicon[2]+"'></span>"),!0===g.cancelicon[0]&&R("#eData","#"+p+"_2").addClass("right"===g.cancelicon[1]?"fm-button-icon-right":"fm-button-icon-left").append("<span class='"+f.icon_base+" "+g.cancelicon[2]+"'></span>"),R("#dData","#"+p+"_2").click(function(){var
e,d=[!0,""],r=R("#DelData>td","#"+p).text();if(c={},void
0===(c=R(o).triggerHandler("jqGridDelRowClickSubmit",[U[o.p.id],r]))&&R.jgrid.isFunction(U[o.p.id].onclickSubmit)&&(c=U[o.p.id].onclickSubmit.call(o,U[o.p.id],r)||{}),void
0===(d=R(o).triggerHandler("jqGridDelRowBeforeSubmit",[r]))&&(d=[!0,"",""]),d[0]&&R.jgrid.isFunction(U[o.p.id].beforeSubmit)&&(d=U[o.p.id].beforeSubmit.call(o,r)),d[0]&&!U[o.p.id].processing){if(U[o.p.id].processing=!0,a=o.p.prmNames,n=R.extend({},U[o.p.id].delData,c),l=a.oper,n[l]=a.deloper,a=a.id,!(r=String(r).split(",")).length)return!1;for(e
in
r)r.hasOwnProperty(e)&&(r[e]=R.jgrid.stripPref(o.p.idPrefix,r[e]));n[a]=r.join(),R(this).addClass(f.active);var
t,i=R.extend({url:U[o.p.id].url||R(o).jqGrid("getGridParam","editurl"),type:U[o.p.id].mtype,data:R.jgrid.isFunction(U[o.p.id].serializeDelData)?U[o.p.id].serializeDelData.call(o,n):n,complete:function(e,t){var
i,a;if(R("#dData","#"+p+"_2").removeClass(f.active),300<=e.status&&304!==e.status?(d[0]=!1,d[1]=R(o).triggerHandler("jqGridDelRowErrorTextFormat",[e]),R.jgrid.isFunction(U[o.p.id].errorTextFormat)&&(d[1]=U[o.p.id].errorTextFormat.call(o,e)),void
0===d[1]&&(d[1]=t+" Status: '"+e.statusText+"'. Error code: "+e.status)):(void
0===(d=R(o).triggerHandler("jqGridDelRowAfterSubmit",[e,n]))&&(d=[!0,"",""]),d[0]&&R.jgrid.isFunction(U[o.p.id].afterSubmit)&&(d=U[o.p.id].afterSubmit.call(o,e,n))),!1===d[0])R("#DelError>td","#"+p).html(d[1]),R("#DelError","#"+p).show();else{if(U[o.p.id].reloadAfterSubmit&&"local"!==o.p.datatype)R(o).trigger("reloadGrid");else{if(!0===o.p.treeGrid)try{R(o).jqGrid("delTreeNode",o.p.idPrefix+r[0])}catch(e){}else
for(i=0;i<r.length;i++)R(o).jqGrid("delRowData",o.p.idPrefix+r[i]);o.p.selrow=null,o.p.selarrrow=[]}(R.jgrid.isFunction(U[o.p.id].afterComplete)||Object.prototype.hasOwnProperty.call(R._data(R(o)[0],"events"),"jqGridDelRowAfterComplete"))&&(a=e,setTimeout(function(){R(o).triggerHandler("jqGridDelRowAfterComplete",[a,n]);try{U[o.p.id].afterComplete.call(o,a,n)}catch(e){}},500))}U[o.p.id].processing=!1,d[0]&&R.jgrid.hideModal("#"+R.jgrid.jqID(u.themodal),{gb:"#gbox_"+R.jgrid.jqID(s),jqm:g.jqModal,onClose:U[o.p.id].onClose})}},R.jgrid.ajaxOptions,U[o.p.id].ajaxDelOptions);i.url||U[o.p.id].useDataProxy||(R.jgrid.isFunction(o.p.dataProxy)?U[o.p.id].useDataProxy=!0:(d[0]=!1,d[1]+=" "+R.jgrid.getRegional(o,"errors.nourl"))),d[0]&&(U[o.p.id].useDataProxy?(void
0===(t=o.p.dataProxy.call(o,i,"del_"+o.p.id))&&(t=[!0,""]),!1===t[0]?(d[0]=!1,d[1]=t[1]||"Error deleting the selected row!"):R.jgrid.hideModal("#"+R.jgrid.jqID(u.themodal),{gb:"#gbox_"+R.jgrid.jqID(s),jqm:g.jqModal,onClose:U[o.p.id].onClose})):"clientArray"===i.url?(n=i.data,i.complete({status:200,statusText:""},"")):R.ajax(i))}return!1===d[0]&&(R("#DelError>td","#"+p).html(d[1]),R("#DelError","#"+p).show()),!1}),R("#eData","#"+p+"_2").click(function(){return R.jgrid.hideModal("#"+R.jgrid.jqID(u.themodal),{gb:"#gbox_"+R.jgrid.jqID(s),jqm:U[o.p.id].jqModal,onClose:U[o.p.id].onClose}),!1}),R("#"+p+"_2").find("[data-index]").each(function(){var
t=parseInt(R(this).attr("data-index"),10);0<=t&&g.buttons[t].hasOwnProperty("click")&&R(this).on("click",function(e){g.buttons[t].click.call(o,R("#"+i)[0],U[o.p.id],e)})}),void
0===(e=R(o).triggerHandler("jqGridDelRowBeforeInitData",[R("#"+p)]))&&(e=!0),e&&R.jgrid.isFunction(U[o.p.id].beforeInitData)&&(e=U[o.p.id].beforeInitData.call(o,R("#"+p))),!1===e)return;R(o).triggerHandler("jqGridDelRowBeforeShowForm",[R("#"+p)]),R.jgrid.isFunction(U[o.p.id].beforeShowForm)&&U[o.p.id].beforeShowForm.call(o,R("#"+p)),R.jgrid.viewModal("#"+R.jgrid.jqID(u.themodal),{gbox:"#gbox_"+R.jgrid.jqID(s),jqm:U[o.p.id].jqModal,overlay:U[o.p.id].overlay,modal:U[o.p.id].modal}),R(o).triggerHandler("jqGridDelRowAfterShowForm",[R("#"+p)]),R.jgrid.isFunction(U[o.p.id].afterShowForm)&&U[o.p.id].afterShowForm.call(o,R("#"+p))}!0===U[o.p.id].closeOnEscape&&setTimeout(function(){R(".ui-jqdialog-titlebar-close","#"+R.jgrid.jqID(u.modalhead)).attr("tabindex","-1").focus()},0)}})},navGrid:function(h,g,m,f,j,v,b){var
w=R.jgrid.getRegional(this[0],"nav"),D=this[0].p.styleUI,e=R.jgrid.styleUI[D].navigator,y=R.jgrid.styleUI[D].common;return g=R.extend({edit:!0,editicon:e.icon_edit_nav,add:!0,addicon:e.icon_add_nav,del:!0,delicon:e.icon_del_nav,search:!0,searchicon:e.icon_search_nav,refresh:!0,refreshicon:e.icon_refresh_nav,refreshstate:"firstpage",view:!1,viewicon:e.icon_view_nav,position:"left",closeOnEscape:!0,beforeRefresh:null,afterRefresh:null,cloneToTop:!1,alertwidth:200,alertheight:"auto",alerttop:null,alertleft:null,alertzIndex:null,dropmenu:!1,navButtonText:""},w,g||{}),this.each(function(){if(!this.p.navGrid){var
t,e,i={themodal:"alertmod_"+this.p.id,modalhead:"alerthd_"+this.p.id,modalcontent:"alertcnt_"+this.p.id},a=this;if(a.grid&&"string"==typeof
h){R(a).data("navGrid")||R(a).data("navGrid",g),t=R(a).data("navGrid"),a.p.force_regional&&(t=R.extend(t,w)),void
0===R("#"+i.themodal)[0]&&(t.alerttop||t.alertleft||((e=R.jgrid.findPos(this))[0]=Math.round(e[0]),e[1]=Math.round(e[1]),t.alertleft=e[0]+this.p.width/2-parseInt(t.alertwidth,10)/2,t.alerttop=e[1]+this.p.height/2-25),e=R(".ui-jqgrid").css("font-size")||"11px",R.jgrid.createModal(i,"<div>"+t.alerttext+"</div><span tabindex='0'><span tabindex='-1' id='jqg_alrt'></span></span>",{gbox:"#gbox_"+R.jgrid.jqID(a.p.id),jqModal:!0,drag:!0,resize:!0,caption:t.alertcap,top:t.alerttop,left:t.alertleft,width:t.alertwidth,height:t.alertheight,closeOnEscape:t.closeOnEscape,zIndex:t.alertzIndex,styleUI:a.p.styleUI},"#gview_"+R.jgrid.jqID(a.p.id),R("#gbox_"+R.jgrid.jqID(a.p.id))[0],!0,{"font-size":e}));var
d,r=1,o=function(){R(this).hasClass(y.disabled)||R(this).addClass(y.hover)},n=function(){R(this).removeClass(y.hover)};for(t.cloneToTop&&a.p.toppager&&(r=2),d=0;d<r;d++){var
l,s,c,p=R("<table class='ui-pg-table navtable ui-common-table'><tbody><tr></tr></tbody></table>"),u="<td class='ui-pg-button "+y.disabled+"' style='width:4px;'><span class='ui-separator'></span></td>";0===d?(0===(s=h).indexOf("#")&&(s=s.substring(1),s="#"+R.jgrid.jqID(s)),c=a.p.id,s===a.p.toppager&&(c+="_top",r=1)):(s=a.p.toppager,c=a.p.id+"_top"),"rtl"===a.p.direction&&R(p).attr("dir","rtl").css("float","right"),f=f||{},t.add&&(l=R("<td class='ui-pg-button "+y.cornerall+"'></td>"),R(l).append("<div class='ui-pg-div'><span class='"+y.icon_base+" "+t.addicon+"'></span>"+t.addtext+"</div>"),R("tr",p).append(l),R(l,p).attr({title:t.addtitle||"",id:f.id||"add_"+c}).click(function(){return R(this).hasClass(y.disabled)||(R.jgrid.setSelNavIndex(a,this),R.jgrid.isFunction(t.addfunc)?t.addfunc.call(a):R(a).jqGrid("editGridRow","new",f)),!1}).hover(o,n),l=null),m=m||{},t.edit&&(l=R("<td class='ui-pg-button "+y.cornerall+"'></td>"),R(l).append("<div class='ui-pg-div'><span class='"+y.icon_base+" "+t.editicon+"'></span>"+t.edittext+"</div>"),R("tr",p).append(l),R(l,p).attr({title:t.edittitle||"",id:m.id||"edit_"+c}).click(function(){var
e;return R(this).hasClass(y.disabled)||((e=a.p.selrow)?(R.jgrid.setSelNavIndex(a,this),R.jgrid.isFunction(t.editfunc)?t.editfunc.call(a,e):R(a).jqGrid("editGridRow",e,m)):(R.jgrid.viewModal("#"+i.themodal,{gbox:"#gbox_"+R.jgrid.jqID(a.p.id),jqm:!0}),R("#jqg_alrt").focus())),!1}).hover(o,n),l=null),b=b||{},t.view&&(l=R("<td class='ui-pg-button "+y.cornerall+"'></td>"),R(l).append("<div class='ui-pg-div'><span class='"+y.icon_base+" "+t.viewicon+"'></span>"+t.viewtext+"</div>"),R("tr",p).append(l),R(l,p).attr({title:t.viewtitle||"",id:b.id||"view_"+c}).click(function(){var
e;return R(this).hasClass(y.disabled)||((e=a.p.selrow)?(R.jgrid.setSelNavIndex(a,this),R.jgrid.isFunction(t.viewfunc)?t.viewfunc.call(a,e):R(a).jqGrid("viewGridRow",e,b)):(R.jgrid.viewModal("#"+i.themodal,{gbox:"#gbox_"+R.jgrid.jqID(a.p.id),jqm:!0}),R("#jqg_alrt").focus())),!1}).hover(o,n),l=null),j=j||{},t.del&&(l=R("<td class='ui-pg-button "+y.cornerall+"'></td>"),R(l).append("<div class='ui-pg-div'><span class='"+y.icon_base+" "+t.delicon+"'></span>"+t.deltext+"</div>"),R("tr",p).append(l),R(l,p).attr({title:t.deltitle||"",id:j.id||"del_"+c}).click(function(){var
e;return R(this).hasClass(y.disabled)||(a.p.multiselect?0===(e=a.p.selarrrow).length&&(e=null):e=a.p.selrow,e?(R.jgrid.setSelNavIndex(a,this),R.jgrid.isFunction(t.delfunc)?t.delfunc.call(a,e):R(a).jqGrid("delGridRow",e,j)):(R.jgrid.viewModal("#"+i.themodal,{gbox:"#gbox_"+R.jgrid.jqID(a.p.id),jqm:!0}),R("#jqg_alrt").focus())),!1}).hover(o,n),l=null),(t.add||t.edit||t.del||t.view)&&R("tr",p).append(u),v=v||{},t.search&&(l=R("<td class='ui-pg-button "+y.cornerall+"'></td>"),R(l).append("<div class='ui-pg-div'><span class='"+y.icon_base+" "+t.searchicon+"'></span>"+t.searchtext+"</div>"),R("tr",p).append(l),R(l,p).attr({title:t.searchtitle||"",id:v.id||"search_"+c}).click(function(){return R(this).hasClass(y.disabled)||(R.jgrid.setSelNavIndex(a,this),R.jgrid.isFunction(t.searchfunc)?t.searchfunc.call(a,v):R(a).jqGrid("searchGrid",v)),!1}).hover(o,n),v.showOnLoad&&!0===v.showOnLoad&&R(l,p).click(),l=null),t.refresh&&(l=R("<td class='ui-pg-button "+y.cornerall+"'></td>"),R(l).append("<div class='ui-pg-div'><span class='"+y.icon_base+" "+t.refreshicon+"'></span>"+t.refreshtext+"</div>"),R("tr",p).append(l),R(l,p).attr({title:t.refreshtitle||"",id:"refresh_"+c}).click(function(){if(!R(this).hasClass(y.disabled)){R.jgrid.isFunction(t.beforeRefresh)&&t.beforeRefresh.call(a),a.p.search=!1,a.p.resetsearch=!0;try{if("currentfilter"!==t.refreshstate){var
e=a.p.id;a.p.postData.filters="";try{R("#fbox_"+R.jgrid.jqID(e)).jqFilter("resetFilter")}catch(e){}R.jgrid.isFunction(a.clearToolbar)&&a.clearToolbar.call(a,!1)}}catch(e){}switch(t.refreshstate){case"firstpage":R(a).trigger("reloadGrid",[{page:1}]);break;case"current":case"currentfilter":R(a).trigger("reloadGrid",[{current:!0}])}R.jgrid.isFunction(t.afterRefresh)&&t.afterRefresh.call(a),R.jgrid.setSelNavIndex(a,this)}return!1}).hover(o,n),l=null),u=R(".ui-jqgrid").css("font-size")||"11px",R("body").append("<div id='testpg2' class='ui-jqgrid "+R.jgrid.styleUI[D].base.entrieBox+"' style='font-size:"+u+";visibility:hidden;' ></div>"),u=R(p).clone().appendTo("#testpg2").width(),R("#testpg2").remove(),a.p._nvtd&&(t.dropmenu?(p=null,R(a).jqGrid("_buildNavMenu",s,c,g,m,f,j,v,b)):u>a.p._nvtd[0]?(a.p.responsive?(p=null,R(a).jqGrid("_buildNavMenu",s,c,g,m,f,j,v,b)):R(s+"_"+t.position,s).append(p).width(u),a.p._nvtd[0]=u):R(s+"_"+t.position,s).append(p),a.p._nvtd[1]=u),a.p.navGrid=!0}a.p.storeNavOptions&&(a.p.navOptions=t,a.p.editOptions=m,a.p.addOptions=f,a.p.delOptions=j,a.p.searchOptions=v,a.p.viewOptions=b,a.p.navButtons=[])}}})},navButtonAdd:function(l,s){var
c=this[0].p.styleUI,e=R.jgrid.styleUI[c].navigator;return s=R.extend({caption:"newButton",title:"",buttonicon:e.icon_newbutton_nav,onClickButton:null,position:"last",cursor:"pointer",internal:!1},s||{}),this.each(function(){var
t,i,e,a,d,r,o,n;this.grid&&"string"==typeof
l&&(0===l.indexOf("#")&&(l=l.substring(1)),l="#"+R.jgrid.jqID(l),n=R(".navtable",l)[0],t=this,i=R.jgrid.styleUI[c].common.disabled,e=R.jgrid.styleUI[c].common.hover,a=R.jgrid.styleUI[c].common.cornerall,d=R.jgrid.styleUI[c].common.icon_base,t.p.storeNavOptions&&!s.internal&&t.p.navButtons.push([l,s]),n?s.id&&void
0!==R("#"+R.jgrid.jqID(s.id),n)[0]||(r=R("<td></td>"),"NONE"===s.buttonicon.toString().toUpperCase()?R(r).addClass("ui-pg-button "+a).append("<div class='ui-pg-div'>"+s.caption+"</div>"):R(r).addClass("ui-pg-button "+a).append("<div class='ui-pg-div'><span class='"+d+" "+s.buttonicon+"'></span>"+s.caption+"</div>"),s.id&&R(r).attr("id",s.id),"first"!==s.position||0===n.rows[0].cells.length?R("tr",n).append(r):R("tr td",n).eq(0).before(r),R(r,n).attr("title",s.title||"").click(function(e){return R(this).hasClass(i)||(R.jgrid.setSelNavIndex(t,this),R.jgrid.isFunction(s.onClickButton)&&s.onClickButton.call(t,e)),!1}).hover(function(){R(this).hasClass(i)||R(this).addClass(e)},function(){R(this).removeClass(e)})):(n=R(".dropdownmenu",l)[0])&&(o=R(n).val(),n=s.id||R.jgrid.randId(),n=R('<li class="ui-menu-item" role="presentation"><a class="'+a+' g-menu-item" tabindex="0" role="menuitem" id="'+n+'">'+(s.caption||s.title)+"</a></li>"),o&&("first"===s.position?R("#"+o).prepend(n):R("#"+o).append(n),R(n).on("click",function(e){return R(this).hasClass(i)||(R("#"+o).hide(),R.jgrid.isFunction(s.onClickButton)&&s.onClickButton.call(t,e)),!1}).find("a").hover(function(){R(this).hasClass(i)||R(this).addClass(e)},function(){R(this).removeClass(e)}))))})},navSeparatorAdd:function(a,d){var
e=this[0].p.styleUI,r=R.jgrid.styleUI[e].common;return d=R.extend({sepclass:"ui-separator",sepcontent:"",position:"last"},d||{}),this.each(function(){var
e,t,i;this.grid&&("string"==typeof
a&&0!==a.indexOf("#")&&(a="#"+R.jgrid.jqID(a)),e=R(".navtable",a)[0],this.p.storeNavOptions&&this.p.navButtons.push([a,d]),e?(t="<td class='ui-pg-button "+r.disabled+"' style='width:4px;'><span class='"+d.sepclass+"'></span>"+d.sepcontent+"</td>","first"!==d.position||0===e.rows[0].cells.length?R("tr",e).append(t):R("tr td",e).eq(0).before(t)):(e=R(".dropdownmenu",a)[0],t="<li class='ui-menu-item "+r.disabled+"' style='width:100%' role='presentation'><hr class='ui-separator-li'></li>",e&&(i=R(e).val())&&("first"===d.position?R("#"+i).prepend(t):R("#"+i).append(t))))})},_buildNavMenu:function(p,u,h,g,m,f,j,v){return this.each(function(){var
t=this,e=R.jgrid.getRegional(t,"nav"),i=t.p.styleUI,a=(R.jgrid.styleUI[i].navigator,R.jgrid.styleUI[i].filter),d=R.jgrid.styleUI[i].common,r="form_menu_"+R.jgrid.randId(),e=h.navButtonText||e.selectcaption||"Actions",e="<button class='dropdownmenu "+d.button+"' value='"+r+"'>"+e+"</button>";R(p+"_"+h.position,p).append(e);var
o,n,l,s,c="alertmod_"+this.p.id;this.p.id,this.p.id;l=R(".ui-jqgrid").css("font-size")||"11px",s=R('<ul id="'+r+'" class="ui-nav-menu modal-content" role="menu" tabindex="0" style="display:none;font-size:'+l+'"></ul>'),h.add&&(o=(m=m||{}).id||"add_"+u,n=R('<li class="ui-menu-item" role="presentation"><a class="'+d.cornerall+' g-menu-item" tabindex="0" role="menuitem" id="'+o+'">'+(h.addtext||h.addtitle)+"</a></li>").click(function(){return R(this).hasClass(d.disabled)||(R.jgrid.isFunction(h.addfunc)?h.addfunc.call(t):R(t).jqGrid("editGridRow","new",m),R(s).hide()),!1}),R(s).append(n)),h.edit&&(o=(g=g||{}).id||"edit_"+u,n=R('<li class="ui-menu-item" role="presentation"><a class="'+d.cornerall+' g-menu-item" tabindex="0" role="menuitem" id="'+o+'">'+(h.edittext||h.edittitle)+"</a></li>").click(function(){var
e;return R(this).hasClass(d.disabled)||((e=t.p.selrow)?R.jgrid.isFunction(h.editfunc)?h.editfunc.call(t,e):R(t).jqGrid("editGridRow",e,g):(R.jgrid.viewModal("#"+c,{gbox:"#gbox_"+R.jgrid.jqID(t.p.id),jqm:!0}),R("#jqg_alrt").focus()),R(s).hide()),!1}),R(s).append(n)),h.view&&(o=(v=v||{}).id||"view_"+u,n=R('<li class="ui-menu-item" role="presentation"><a class="'+d.cornerall+' g-menu-item" tabindex="0" role="menuitem" id="'+o+'">'+(h.viewtext||h.viewtitle)+"</a></li>").click(function(){var
e;return R(this).hasClass(d.disabled)||((e=t.p.selrow)?R.jgrid.isFunction(h.editfunc)?h.viewfunc.call(t,e):R(t).jqGrid("viewGridRow",e,v):(R.jgrid.viewModal("#"+c,{gbox:"#gbox_"+R.jgrid.jqID(t.p.id),jqm:!0}),R("#jqg_alrt").focus()),R(s).hide()),!1}),R(s).append(n)),h.del&&(o=(f=f||{}).id||"del_"+u,n=R('<li class="ui-menu-item" role="presentation"><a class="'+d.cornerall+' g-menu-item" tabindex="0" role="menuitem" id="'+o+'">'+(h.deltext||h.deltitle)+"</a></li>").click(function(){var
e;return R(this).hasClass(d.disabled)||(t.p.multiselect?0===(e=t.p.selarrrow).length&&(e=null):e=t.p.selrow,e?R.jgrid.isFunction(h.delfunc)?h.delfunc.call(t,e):R(t).jqGrid("delGridRow",e,f):(R.jgrid.viewModal("#"+c,{gbox:"#gbox_"+R.jgrid.jqID(t.p.id),jqm:!0}),R("#jqg_alrt").focus()),R(s).hide()),!1}),R(s).append(n)),(h.add||h.edit||h.del||h.view)&&R(s).append("<li class='ui-menu-item "+d.disabled+"' style='width:100%' role='presentation'><hr class='ui-separator-li'></li>"),h.search&&(o=(j=j||{}).id||"search_"+u,n=R('<li class="ui-menu-item" role="presentation"><a class="'+d.cornerall+' g-menu-item" tabindex="0" role="menuitem" id="'+o+'">'+(h.searchtext||h.searchtitle)+"</a></li>").click(function(){return R(this).hasClass(d.disabled)||(R.jgrid.isFunction(h.searchfunc)?h.searchfunc.call(t,j):R(t).jqGrid("searchGrid",j),R(s).hide()),!1}),R(s).append(n),j.showOnLoad&&!0===j.showOnLoad&&R(n).click()),h.refresh&&(o=j.id||"search_"+u,n=R('<li class="ui-menu-item" role="presentation"><a class="'+d.cornerall+' g-menu-item" tabindex="0" role="menuitem" id="'+o+'">'+(h.refreshtext||h.refreshtitle)+"</a></li>").click(function(){if(!R(this).hasClass(d.disabled)){R.jgrid.isFunction(h.beforeRefresh)&&h.beforeRefresh.call(t),t.p.search=!1,t.p.resetsearch=!0;try{if("currentfilter"!==h.refreshstate){var
e=t.p.id;t.p.postData.filters="";try{R("#fbox_"+R.jgrid.jqID(e)).jqFilter("resetFilter")}catch(e){}R.jgrid.isFunction(t.clearToolbar)&&t.clearToolbar.call(t,!1)}}catch(e){}switch(h.refreshstate){case"firstpage":R(t).trigger("reloadGrid",[{page:1}]);break;case"current":case"currentfilter":R(t).trigger("reloadGrid",[{current:!0}])}R.jgrid.isFunction(h.afterRefresh)&&h.afterRefresh.call(t),R(s).hide()}return!1}),R(s).append(n)),R(s).hide(),R("body").append(s),R("#"+r).addClass("ui-menu "+a.menu_widget),R("#"+r+" > li > a").hover(function(){R(this).addClass(d.hover)},function(){R(this).removeClass(d.hover)}),R(".dropdownmenu",p+"_"+h.position).on("click",function(e){var
t=R(this).offset(),i=t.left,a=parseInt(t.top),t=R(this).val();R("#"+t).show().css({top:a-(R("#"+t).height()+10)+"px",left:i+"px"}),e.stopPropagation()}),R("body").on("click",function(e){R(e.target).hasClass("dropdownmenu")||R("#"+r).hide()})})},GridToForm:function(a,d){return this.each(function(){var
e,t=this;if(t.grid){var
i=R(t).jqGrid("getRowData",a);if(i)for(e
in
i)i.hasOwnProperty(e)&&(R("[name="+R.jgrid.jqID(e)+"]",d).is("input:radio")||R("[name="+R.jgrid.jqID(e)+"]",d).is("input:checkbox")?R("[name="+R.jgrid.jqID(e)+"]",d).each(function(){R(this).val()==i[e]?R(this)[t.p.useProp?"prop":"attr"]("checked",!0):R(this)[t.p.useProp?"prop":"attr"]("checked",!1)}):R("[name="+R.jgrid.jqID(e)+"]",d).val(i[e]))}})},FormToGrid:function(t,a,d,r){return this.each(function(){var
e,i;this.grid&&(d=d||"set",r=r||"first",e=R(a).serializeArray(),i={},R.each(e,function(e,t){i[t.name]=t.value}),"add"===d?R(this).jqGrid("addRowData",t,i,r):"set"===d&&R(this).jqGrid("setRowData",t,i))})}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base","./grid.common"],e):e(jQuery)}(function(T){"use strict";T.fn.jqFilter=function(e){if("string"==typeof
e){var
t=T.fn.jqFilter[e];if(!t)throw"jqFilter - No such method: "+e;var
r=T.makeArray(arguments).slice(1);return t.apply(this,r)}var
x=T.extend(!0,{filter:null,columns:[],sortStrategy:null,onChange:null,afterRedraw:null,checkValues:null,error:!1,errmsg:"",errorcheck:!0,showQuery:!0,sopt:null,ops:[],operands:null,numopts:["eq","ne","lt","le","gt","ge","nu","nn","in","ni"],stropts:["eq","ne","bw","bn","ew","en","cn","nc","nu","nn","in","ni"],strarr:["text","string","blob"],groupOps:[{op:"AND",text:"AND"},{op:"OR",text:"OR"}],groupButton:!0,ruleButtons:!0,uniqueSearchFields:!1,direction:"ltr",addsubgrup:"Add subgroup",addrule:"Add rule",delgroup:"Delete group",delrule:"Delete rule",autoencode:!1,unaryOperations:[]},T.jgrid.filter,e||{});return this.each(function(){if(!this.filter){this.p=x,null!==this.p.filter&&void
0!==this.p.filter||(this.p.filter={groupOp:this.p.groupOps[0].op,rules:[],groups:[]}),null!=this.p.sortStrategy&&T.jgrid.isFunction(this.p.sortStrategy)&&this.p.columns.sort(this.p.sortStrategy);var
e,t,r=this.p.columns.length,b=/msie/i.test(navigator.userAgent)&&!window.opera;if(this.p.initFilter=T.extend(!0,{},this.p.filter),r){for(e=0;e<r;e++)(t=this.p.columns[e]).stype?t.inputtype=t.stype:t.inputtype||(t.inputtype="text"),t.sorttype?t.searchtype=t.sorttype:t.searchtype||(t.searchtype="string"),void
0===t.hidden&&(t.hidden=!1),t.label||(t.label=t.name),t.index&&(t.name=t.index),t.hasOwnProperty("searchoptions")||(t.searchoptions={}),t.hasOwnProperty("searchrules")||(t.searchrules={}),void
0===t.search?t.inlist=!0:t.inlist=t.search;var
S=function(){return T("#"+T.jgrid.jqID(x.id))[0]||null},s=S(),F=T.jgrid.styleUI[s.p.styleUI||"jQueryUI"].filter,O=T.jgrid.styleUI[s.p.styleUI||"jQueryUI"].common;this.p.showQuery&&T(this).append("<table class='queryresult "+F.table_widget+"' style='display:block;max-width:440px;border:0px none;' dir='"+this.p.direction+"'><tbody><tr><td class='query'></td></tr></tbody></table>");var
o=function(e,t){var
r=[!0,""],s=S();if(T.jgrid.isFunction(t.searchrules))r=t.searchrules.call(s,e,t);else
if(T.jgrid&&T.jgrid.checkValues)try{r=T.jgrid.checkValues.call(s,e,-1,t.searchrules,t.label)}catch(e){}r&&r.length&&!1===r[0]&&(x.error=!r[0],x.errmsg=r[1])};this.onchange=function(){return this.p.error=!1,this.p.errmsg="",!!T.jgrid.isFunction(this.p.onChange)&&this.p.onChange.call(this,this.p)},this.reDraw=function(){T(this).find("table.group").first().remove();var
e=this.createTableForGroup(x.filter,null);T(this).append(e),T.jgrid.isFunction(this.p.afterRedraw)&&this.p.afterRedraw.call(this,this.p)},this.createTableForGroup=function(s,e){var
a=this,t=T("<table class='group "+F.table_widget+" ui-search-table' style='border:0px none;'><tbody></tbody></table>"),r="left";"rtl"===this.p.direction&&(r="right",t.attr("dir","rtl")),null===e&&t.append("<tr class='error' style='display:none;'><th colspan='5' class='"+O.error+"' align='"+r+"'></th></tr>");var
i=T("<tr></tr>");t.append(i);r=T("<th colspan='5' align='"+r+"'></th>");if(i.append(r),!0===this.p.ruleButtons){var
o=T("<select size='1' class='opsel "+F.srSelect+"'></select>");r.append(o);for(var
l,n="",p=0;p<x.groupOps.length;p++)l=s.groupOp===a.p.groupOps[p].op?" selected='selected'":"",n+="<option value='"+a.p.groupOps[p].op+"'"+l+">"+a.p.groupOps[p].text+"</option>";o.append(n).on("change",function(){s.groupOp=T(o).val(),a.onchange()})}var
c,u,i="<span></span>";if(this.p.groupButton&&(i=T("<input type='button' value='+ {}' title='"+a.p.subgroup+"' class='add-group "+O.button+"'/>")).on("click",function(){return void
0===s.groups&&(s.groups=[]),s.groups.push({groupOp:x.groupOps[0].op,rules:[],groups:[]}),a.reDraw(),a.onchange(),!1}),r.append(i),!0===this.p.ruleButtons&&((u=T("<input type='button' value='+' title='"+a.p.addrule+"' class='add-rule ui-add "+O.button+"'/>")).on("click",function(){for(void
0===s.rules&&(s.rules=[]),p=0;p<a.p.columns.length;p++){var
e=void
0===a.p.columns[p].search||a.p.columns[p].search,t=!0===a.p.columns[p].hidden;if(!0===a.p.columns[p].searchoptions.searchhidden&&e||e&&!t){c=a.p.columns[p];break}}if(!c)return!1;var
r=c.searchoptions.sopt||a.p.sopt||(-1!==T.inArray(c.searchtype,a.p.strarr)?a.p.stropts:a.p.numopts);return s.rules.push({field:c.name,op:r[0],data:""}),a.reDraw(),!1}),r.append(u)),null!==e&&(u=T("<input type='button' value='-' title='"+a.p.delgroup+"' class='delete-group "+O.button+"'/>"),r.append(u),u.on("click",function(){for(p=0;p<e.groups.length;p++)if(e.groups[p]===s){e.groups.splice(p,1);break}return a.reDraw(),a.onchange(),!1})),void
0!==s.groups)for(p=0;p<s.groups.length;p++){var
d=T("<tr></tr>");t.append(d);var
h=T("<td class='first'></td>");d.append(h);h=T("<td colspan='4'></td>");h.append(this.createTableForGroup(s.groups[p],s)),d.append(h)}void
0===s.groupOp&&(s.groupOp=a.p.groupOps[0].op);var
g=a.p.ruleButtons&&a.p.uniqueSearchFields;if(g)for(m=0;m<a.p.columns.length;m++)a.p.columns[m].inlist&&(a.p.columns[m].search=!0);if(void
0!==s.rules)for(p=0;p<s.rules.length;p++)if(t.append(this.createTableRowForRule(s.rules[p],s)),g)for(var
f=s.rules[p].field,m=0;m<a.p.columns.length;m++)if(f===a.p.columns[m].name){a.p.columns[m].search=!1;break}return t},this.createTableRowForRule=function(i,e){var
o,l,n,t,p=this,c=S(),r=T("<tr></tr>"),s="";r.append("<td class='first'></td>");var
a=T("<td class='columns'></td>");r.append(a);var
u,d=T("<select size='1' class='"+F.srSelect+"'></select>"),h=[];a.append(d),d.on("change",function(){var
e;for(p.p.ruleButtons&&p.p.uniqueSearchFields&&(e=parseInt(T(this).data("curr"),10),r=this.selectedIndex,0<=e&&(p.p.columns[e].search=!0,T(this).data("curr",r),p.p.columns[r].search=!1)),i.field=T(d).val(),l=T(this).parents("tr").first(),T(".data",l).empty(),f=0;f<p.p.columns.length;f++)if(p.p.columns[f].name===i.field){n=p.p.columns[f];break}if(n){n.searchoptions.id=T.jgrid.randId(),n.searchoptions.name=i.field,n.searchoptions.oper="filter",b&&"text"===n.inputtype&&(n.searchoptions.size||(n.searchoptions.size=10));var
t=T.jgrid.createEl.call(c,n.inputtype,n.searchoptions,"",!0,p.p.ajaxSelectOptions||{},!0);T(t).addClass("input-elm "+F.srInput),o=n.searchoptions.sopt||p.p.sopt||(-1!==T.inArray(n.searchtype,p.p.strarr)?p.p.stropts:p.p.numopts);var
r,s="",a=0;for(h=[],T.each(p.p.ops,function(){h.push(this.oper)}),f=0;f<o.length;f++)-1!==(u=T.inArray(o[f],h))&&(0===a&&(i.op=p.p.ops[u].oper),s+="<option value='"+p.p.ops[u].oper+"'>"+p.p.ops[u].text+"</option>",a++);T(".selectopts",l).empty().append(s),T(".selectopts",l)[0].selectedIndex=0,T.jgrid.msie()&&T.jgrid.msiever()<9&&(r=parseInt(T("select.selectopts",l)[0].offsetWidth,10)+1,T(".selectopts",l).width(r),T(".selectopts",l).css("width","auto")),T(".data",l).append(t),T.jgrid.bindEv.call(c,t,n.searchoptions),T(".input-elm",l).on("change",function(e){e=e.target;"custom"===n.inputtype&&T.jgrid.isFunction(n.searchoptions.custom_value)?i.data=n.searchoptions.custom_value.call(c,T(".customelement",this),"get"):i.data=T(e).val(),"select"===n.inputtype&&n.searchoptions.multiple&&(i.data=i.data.join(",")),p.onchange()}),setTimeout(function(){i.data=T(t).val(),("nu"===i.op||"nn"===i.op||0<=T.inArray(i.op,p.p.unaryOperations))&&(T(t).attr("readonly","true"),T(t).attr("disabled","true")),"select"===n.inputtype&&n.searchoptions.multiple&&Array.isArray(i.data)&&(i.data=i.data.join(",")),p.onchange()},0)}});for(var
g=0,f=0;f<p.p.columns.length;f++){var
m=void
0===p.p.columns[f].search||p.p.columns[f].search,y=!0===p.p.columns[f].hidden;(!0===p.p.columns[f].searchoptions.searchhidden&&m||m&&!y)&&(t="",i.field===p.p.columns[f].name&&(t=" selected='selected'",g=f),s+="<option value='"+p.p.columns[f].name+"'"+t+">"+p.p.columns[f].label+"</option>")}d.append(s),d.data("curr",g);var
v=T("<td class='operators'></td>");r.append(v),(n=x.columns[g]).searchoptions.id=T.jgrid.randId(),b&&"text"===n.inputtype&&(n.searchoptions.size||(n.searchoptions.size=10)),n.searchoptions.name=i.field,n.searchoptions.oper="filter";a=T.jgrid.createEl.call(c,n.inputtype,n.searchoptions,i.data,!0,p.p.ajaxSelectOptions||{},!0);("nu"===i.op||"nn"===i.op||0<=T.inArray(i.op,p.p.unaryOperations))&&(T(a).attr("readonly","true"),T(a).attr("disabled","true"));var
j=T("<select size='1' class='selectopts "+F.srSelect+"'></select>");for(v.append(j),j.on("change",function(){i.op=T(j).val(),l=T(this).parents("tr").first();var
e=T(".input-elm",l)[0];"nu"===i.op||"nn"===i.op||0<=T.inArray(i.op,p.p.unaryOperations)?(i.data="","SELECT"!==e.tagName.toUpperCase()&&(e.value=""),e.setAttribute("readonly","true"),e.setAttribute("disabled","true")):("SELECT"===e.tagName.toUpperCase()&&(i.data=e.value),e.removeAttribute("readonly"),e.removeAttribute("disabled")),p.onchange()}),o=n.searchoptions.sopt||p.p.sopt||(-1!==T.inArray(n.searchtype,p.p.strarr)?p.p.stropts:p.p.numopts),s="",T.each(p.p.ops,function(){h.push(this.oper)}),f=0;f<o.length;f++)-1!==(u=T.inArray(o[f],h))&&(t=i.op===p.p.ops[u].oper?" selected='selected'":"",s+="<option value='"+p.p.ops[u].oper+"'"+t+">"+p.p.ops[u].text+"</option>");j.append(s);v=T("<td class='data'></td>");r.append(v),v.append(a),T.jgrid.bindEv.call(c,a,n.searchoptions),T(a).addClass("input-elm "+F.srInput).on("change",function(){i.data="custom"===n.inputtype?n.searchoptions.custom_value.call(c,T(".customelement",this),"get"):T(this).val(),p.onchange()});v=T("<td></td>");return r.append(v),!0===this.p.ruleButtons&&(a=T("<input type='button' value='-' title='"+p.p.delrule+"' class='delete-rule ui-del "+O.button+"'/>"),v.append(a),a.on("click",function(){for(f=0;f<e.rules.length;f++)if(e.rules[f]===i){e.rules.splice(f,1);break}return p.reDraw(),p.onchange(),!1})),r},this.getStringForGroup=function(e){var
t,r="(";if(void
0!==e.groups)for(t=0;t<e.groups.length;t++){1<r.length&&(r+=" "+e.groupOp+" ");try{r+=this.getStringForGroup(e.groups[t])}catch(e){alert(e)}}if(void
0!==e.rules)try{for(t=0;t<e.rules.length;t++)1<r.length&&(r+=" "+e.groupOp+" "),r+=this.getStringForRule(e.rules[t])}catch(e){alert(e)}return"()"===(r+=")")?"":r},this.getStringForRule=function(e){for(var
t,r,s="",a="",i=0;i<this.p.ops.length;i++)if(this.p.ops[i].oper===e.op){s=this.p.operands.hasOwnProperty(e.op)?this.p.operands[e.op]:"",a=this.p.ops[i].oper;break}for(i=0;i<this.p.columns.length;i++)if(this.p.columns[i].name===e.field){t=this.p.columns[i];break}return void
0===t?"":(r=this.p.autoencode?T.jgrid.htmlEncode(e.data):e.data,"bw"!==a&&"bn"!==a||(r+="%"),"ew"!==a&&"en"!==a||(r="%"+r),"cn"!==a&&"nc"!==a||(r="%"+r+"%"),"in"!==a&&"ni"!==a||(r=" ("+r+")"),x.errorcheck&&o(e.data,t),-1!==T.inArray(t.searchtype,["int","integer","float","number","currency"])||"nn"===a||"nu"===a||0<=T.inArray(e.op,this.p.unaryOperations)?e.field+" "+s+" "+r:e.field+" "+s+' "'+r+'"')},this.resetFilter=function(){this.p.filter=T.extend(!0,{},this.p.initFilter),this.reDraw(),this.onchange()},this.hideError=function(){T("th."+O.error,this).html(""),T("tr.error",this).hide()},this.showError=function(){T("th."+O.error,this).html(this.p.errmsg),T("tr.error",this).show()},this.toUserFriendlyString=function(){return this.getStringForGroup(x.filter)},this.toString=function(){var
a=this;return function
e(t){var
r,s="(";if(void
0!==t.groups)for(r=0;r<t.groups.length;r++)1<s.length&&("OR"===t.groupOp?s+=" || ":s+=" && "),s+=e(t.groups[r]);if(void
0!==t.rules)for(r=0;r<t.rules.length;r++)1<s.length&&("OR"===t.groupOp?s+=" || ":s+=" && "),s+=function(e){if(a.p.errorcheck){for(var
t,r=0;r<a.p.columns.length;r++)if(a.p.columns[r].name===e.field){t=a.p.columns[r];break}t&&o(e.data,t)}return e.op+"(item."+e.field+",'"+e.data+"')"}(t.rules[r]);return"()"==(s+=")")?"":s}(this.p.filter)},this.reDraw(),this.p.showQuery&&this.onchange(),this.filter=!0}}})},T.extend(T.fn.jqFilter,{toSQLString:function(){var
e="";return this.each(function(){e=this.toUserFriendlyString()}),e},filterData:function(){var
e;return this.each(function(){e=this.p.filter}),e},getParameter:function(r){var
s=null;return void
0!==r&&this.each(function(e,t){t.p.hasOwnProperty(r)&&(s=t.p[r])}),s||this[0].p},resetFilter:function(){return this.each(function(){this.resetFilter()})},addFilter:function(e){"string"==typeof
e&&(e=T.jgrid.parse(e)),this.each(function(){this.p.filter=e,this.reDraw(),this.onchange()})}}),T.extend(T.jgrid,{filterRefactor:function(e){var
t,r,s,a,i,o,l={};try{if((l="string"==typeof
e.ruleGroup?T.jgrid.parse(e.ruleGroup):e.ruleGroup).rules&&l.rules.length)for(t=l.rules,r=0;r<t.length;r++)o=(s=t[r]).field,-1<T.inArray(o,e.ssfield)&&1<(a=s.data.split(e.splitSelect)).length&&(void
0===l.groups&&(l.groups=[]),i={groupOp:e.groupOpSelect,groups:[],rules:[]},l.groups.push(i),T.each(a,function(e){a[e]&&i.rules.push({data:a[e],op:s.op,field:s.field})}),t.splice(r,1),r--)}catch(e){}return l}}),T.jgrid.extend({filterToolbar:function(_){var
s=T.jgrid.getRegional(this[0],"search");return _=T.extend({autosearch:!0,autosearchDelay:500,searchOnEnter:!0,beforeSearch:null,afterSearch:null,beforeClear:null,afterClear:null,onClearSearchValue:null,url:"",stringResult:!1,groupOp:"AND",defaultSearch:"bw",searchOperators:!1,resetIcon:"x",splitSelect:",",groupOpSelect:"OR",errorcheck:!0,operands:{eq:"==",ne:"!",lt:"<",le:"<=",gt:">",ge:">=",bw:"^",bn:"!^",in:"=",ni:"!=",ew:"|",en:"!@",cn:"~",nc:"!~",nu:"#",nn:"!#",bt:"..."},disabledKeys:[9,16,17,18,19,20,33,34,35,36,37,38,39,30,45,112,113,114,115,116,117,118,119,120,121,122,123,144,145]},s,_||{}),this.each(function(){var
q=this,w=[];if(!q.p.filterToolbar){if(T(q).data("filterToolbar")||T(q).data("filterToolbar",_),q.p.force_regional&&(_=T.extend(_,s)),void
0!==q.p.customFilterDef)for(var
e
in
q.p.customFilterDef)q.p.customFilterDef.hasOwnProperty(e)&&!_.operands.hasOwnProperty(e)&&(_.odata.push({oper:e,text:q.p.customFilterDef[e].text}),_.operands[e]=q.p.customFilterDef[e].operand,!0===q.p.customFilterDef[e].unary&&w.push(e));var
g,f,t,m=T.jgrid.styleUI[q.p.styleUI||"jQueryUI"].filter,p=T.jgrid.styleUI[q.p.styleUI||"jQueryUI"].common,y=T.jgrid.styleUI[q.p.styleUI||"jQueryUI"].base,v=function(){var
r,s,a,i,o={},l=0,n={},p=!1,c=[],u=[],d={},h=[],g=!1,f=[!0,"",""],m=!1;if(T.each(q.p.colModel,function(){var
e,t=!1;if(s=this.index||this.name,i=this.searchoptions||{},!0===this.frozen&&!0===q.p.frozenColumns?(e=T("#gs_"+q.p.idPrefix+T.jgrid.jqID(this.name),q.grid.fhDiv),t=!0):e=T("#gs_"+q.p.idPrefix+T.jgrid.jqID(this.name),q.grid.hDiv),void
0===e[0]&&(e=T("#gs_"+q.p.idPrefix+T.jgrid.jqID(this.name),q.grid.hDiv)),a=_.searchOperators&&i.searchOperMenu?e.parents("table.ui-search-table").find("td.ui-search-oper").children("a").attr("soper")||_.defaultSearch:i.sopt?i.sopt[0]:"select"===this.stype?"eq":_.defaultSearch,"custom"===this.stype&&T.jgrid.isFunction(i.custom_value)&&0<e.length?(r=i.custom_value.call(q,e,"get"),h.push(s)):r=e.val(),"select"===this.stype&&i.multiple&&Array.isArray(r)?r=0<r.length?(p=!0,c.push(s),1===r.length?r[0]:r):"":"bt"!==a&&"text"===this.stype&&!0===i.splitSearchWord&&u.push(s),this.searchrules&&_.errorcheck&&(T.jgrid.isFunction(this.searchrules)?f=this.searchrules.call(q,r,this):T.jgrid&&T.jgrid.checkValues&&(f=T.jgrid.checkValues.call(q,r,-1,this.searchrules,this.label||this.name)),f&&f.length&&!1===f[0]))return this.searchrules.hasOwnProperty("validationError")&&(m=this.searchrules.validationError),!1;if("bt"===a&&(g=!0),t&&"cb"!==s&&"rn"!==s&&"subgrid"!==s&&(d[s]=r),r||"nu"===a||"nn"===a||0<=T.inArray(a,w))o[s]=r,n[s]=a,l++;else
try{delete
q.p.postData[s]}catch(e){}}),!1!==f[0]){var
e,t=0<l;if(!0===_.stringResult||"local"===q.p.datatype||!0===_.searchOperators){var
y,v,j,b,S,F,O='{"groupOp":"'+_.groupOp+'","rules":[',x=0;if(T.each(o,function(e,t){0<x&&(O+=","),O+='{"field":"'+e+'",',O+='"op":"'+n[e]+'",',O+='"data":"'+(t+="").replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}',x++}),O+="]}",p&&(y=T.jgrid.filterRefactor({ruleGroup:O,ssfield:c,splitSelect:_.splitSelect,groupOpSelect:_.groupOpSelect})),g&&(T.isPlainObject(y)||(y=T.jgrid.parse(O)),y.rules&&y.rules.length))for(v=y.rules,j=0;j<v.length;j++)"bt"===(b=v[j]).op&&1<(S=b.data.split("...")).length&&(void
0===y.groups&&(y.groups=[]),F={groupOp:"AND",groups:[],rules:[]},y.groups.push(F),T.each(S,function(e){var
t=0===e?"ge":"le";S[e]&&F.rules.push({data:S[e],op:t,field:b.field})}),v.splice(j,1),j--);u.length&&(y=T.jgrid.filterRefactor({ruleGroup:O,ssfield:u,splitSelect:i.splitSearchSeparator||";",groupOpSelect:"OR"})),(g||p||u.length)&&(O=JSON.stringify(y)),!0===q.p.mergeSearch&&q.p.searchModules.hasOwnProperty("filterToolbar")&&!1!==q.p.searchModules.filterToolbar?(q.p.searchModules.filterToolbar=0<x?O:null,t=!0,T.extend(q.p.postData,{filters:T.jgrid.splitSearch(q.p.searchModules)})):T.extend(q.p.postData,{filters:O}),T.each(["searchField","searchString","searchOper"],function(e,t){q.p.postData.hasOwnProperty(t)&&delete
q.p.postData[t]})}else
T.extend(q.p.postData,o);_.url&&(e=q.p.url,T(q).jqGrid("setGridParam",{url:_.url}));var
D="stop"===T(q).triggerHandler("jqGridToolbarBeforeSearch");!D&&T.jgrid.isFunction(_.beforeSearch)&&(D=_.beforeSearch.call(q)),D||T(q).jqGrid("setGridParam",{search:t}).trigger("reloadGrid",[{page:1}]),e&&T(q).jqGrid("setGridParam",{url:e}),T(q).triggerHandler("jqGridToolbarAfterSearch"),T.jgrid.isFunction(_.afterSearch)&&_.afterSearch.call(q),q.p.frozenColumns&&I(d,n,c,h)}else
T.jgrid.isFunction(m)?m.call(q,f[1]):(e=T.jgrid.getRegional(q,"errors"),T.jgrid.info_dialog(e.errcap,f[1],"",{styleUI:q.p.styleUI}))},I=function(e,i,t,o){var
l=T(".ui-search-toolbar",q.grid.hDiv),n=T(".ui-search-toolbar",q.grid.fhDiv);T.each(e,function(e,t){var
r,s,a;!_.searchOperators||(s=i[e])&&(T(".ui-search-table .ui-search-oper [colname='userId']",l).attr({soper:s}).text(_.operands[s]),T(".ui-search-table .ui-search-oper [colname='userId']",n).attr({soper:s}).text(_.operands[s])),-1<T.inArray(e,o)?(a=T.jgrid.getElemByAttrVal(q.p.colModel,"name",e))&&a.searchoptions&&(r=a.searchoptions||{},T.jgrid.isFunction(r.custom_value)&&(s=T("#gs_"+q.p.idPrefix+T.jgrid.jqID(e),q.grid.fhDiv),a=T("#gs_"+q.p.idPrefix+T.jgrid.jqID(e),q.grid.hDiv),r.custom_value.call(q,s,"set",t),r.custom_value.call(q,a,"set",t))):(T("#gs_"+q.p.idPrefix+T.jgrid.jqID(e),l).val(t),T("#gs_"+q.p.idPrefix+T.jgrid.jqID(e),n).val(t))})},j=T("<tr class='ui-search-toolbar' role='row'></tr>");_.restoreFromFilters&&(t=!0===q.p.mergeSearch&&q.p.searchModules.hasOwnProperty("filterToolbar")&&!1!==q.p.searchModules.filterToolbar?q.p.searchModules.filterToolbar:q.p.postData.filters)&&("string"==typeof
t&&(t=T.jgrid.parse(t)),f=!(!t.rules||!t.rules.length)&&t.rules);var
b=new
Set(_.disabledKeys);if(b.size!==_.disabledKeys.length)for(var
r=0;r<_.disabledKeys.length;r++)(b=new
Set).add(_.disabledKeys[r]);T.each(q.p.colModel,function(e){var
t,r,s,a,i,o,l=this,n="",p="=",c=T("<th role='columnheader' class='"+y.headerBox+" ui-th-"+q.p.direction+"' id='gsh_"+q.p.id+"_"+l.name+"' ></th>"),u=T("<div></div>"),d=T("<table class='ui-search-table' cellspacing='0'><tr><td class='ui-search-oper' headers=''></td><td class='ui-search-input' headers=''></td><td class='ui-search-clear' headers=''></td></tr></table>");if(!0===this.hidden&&T(c).css("display","none"),this.search=!1!==this.search,void
0===this.stype&&(this.stype="text"),this.searchoptions=this.searchoptions||{},void
0===this.searchoptions.searchOperMenu&&(this.searchoptions.searchOperMenu=!0),t=T.extend({},this.searchoptions,{name:l.index||l.name,id:"gs_"+q.p.idPrefix+l.name,oper:"search"}),this.search){if(_.restoreFromFilters&&f){o=!1;for(var
h=0;h<f.length;h++)if(f[h].field)if((l.index||l.name)===f[h].field){o=f[h];break}}if(_.searchOperators){for(r=t.sopt?t.sopt[0]:"select"===l.stype?"eq":_.defaultSearch,_.restoreFromFilters&&o&&(r=o.op),s=0;s<_.odata.length;s++)if(_.odata[s].oper===r){p=_.operands[r]||"";break}a=null!=t.searchtitle?t.searchtitle:_.operandTitle,n=this.searchoptions.searchOperMenu?"<a title='"+a+"' soper='"+r+"' class='soptclass' colname='"+this.name+"'>"+p+"</a>":""}switch(T("td",d).eq(0).attr("columname",l.name).append(n),void
0===t.clearSearch&&(t.clearSearch=!0),t.clearSearch?(i=_.resetTitle||"Clear Search Value",T("td",d).eq(2).append("<a title='"+i+"' style='padding-right: 0.3em;padding-left: 0.3em;' class='clearsearchclass'>"+_.resetIcon+"</a>")):T("td",d).eq(2).hide(),this.surl&&(t.dataUrl=this.surl),i="",t.defaultValue&&(i=T.jgrid.isFunction(t.defaultValue)?t.defaultValue.call(q):t.defaultValue),_.restoreFromFilters&&o&&(i=o.data),i=T.jgrid.createEl.call(q,this.stype,t,i,!1,T.extend({},T.jgrid.ajaxOptions,q.p.ajaxSelectOptions||{})),"custom"!==this.stype&&T(i).addClass(m.srInput),T("td",d).eq(1).append(i),T(u).append(d),null==t.dataEvents&&(t.dataEvents=[]),this.stype){case"select":!0===_.autosearch&&t.dataEvents.push({type:"change",fn:function(){return v(),!1}});break;case"text":case"custom":!0===_.autosearch&&(_.searchOnEnter?t.dataEvents.push({type:"keypress",fn:function(e){return 13===(e.charCode||e.keyCode||0)?(v(),!1):this}}):t.dataEvents.push({type:"keydown",fn:function(e){e=e.which;if(!b.has(e)){if(13===e)return!1;g&&clearTimeout(g),g=setTimeout(function(){v()},_.autosearchDelay)}}}))}T.jgrid.bindEv.call(q,i,t)}T(c).append(u),T(j).append(c),_.searchOperators&&""!==n||T("td",d).eq(0).hide()}),T("table thead",q.grid.hDiv).append(j),_.searchOperators&&(T(".soptclass",j).click(function(e){var
t=T(this).offset();!function(r,e,t){T("#sopt_menu").remove(),e=parseInt(e,10),t=parseInt(t,10)+18;var
s,a='<ul id="sopt_menu" class="ui-search-menu modal-content" role="menu" tabindex="0" style="font-size:'+(T(".ui-jqgrid").css("font-size")||"11px")+";left:"+e+"px;top:"+t+'px;">',i=T(r).attr("soper"),o=[],t=T(r).attr("colname"),l=T.jgrid.getElemByAttrVal(q.p.colModel,"name",t,!0);if(-1!==l){var
t=q.p.colModel[l],n=T.extend({},t.searchoptions);for(n.sopt||(n.sopt=[],n.sopt[0]="select"===t.stype?"eq":_.defaultSearch),T.each(_.odata,function(){o.push(this.oper)}),l=0;l<n.sopt.length;l++)-1!==(s=T.inArray(n.sopt[l],o))&&(a+='<li class="ui-menu-item '+(i===_.odata[s].oper?p.highlight:"")+'" role="presentation"><a class="'+p.cornerall+' g-menu-item" tabindex="0" role="menuitem" value="'+_.odata[s].oper+'" oper="'+_.operands[_.odata[s].oper]+'"><table class="ui-common-table"><tr><td class="opersign">'+_.operands[_.odata[s].oper]+"</td><td>"+_.odata[s].text+"</td></tr></table></a></li>");a+="</ul>",T("body").append(a),T("#sopt_menu").addClass("ui-menu "+m.menu_widget),T("#sopt_menu > li > a").hover(function(){T(this).addClass(p.hover)},function(){T(this).removeClass(p.hover)}).click(function(){var
e=T(this).attr("value"),t=T(this).attr("oper");T(q).triggerHandler("jqGridToolbarSelectOper",[e,t,r]),T("#sopt_menu").hide(),T(r).text(t).attr("soper",e),!0===_.autosearch&&(t=T(r).parent().next().children()[0],(T(t).val()||"nu"===e||"nn"===e||0<=T.inArray(e,w))&&v())})}}(this,t.left,t.top),e.stopPropagation()}),T("body").on("click",function(e){"soptclass"!==e.target.className&&T("#sopt_menu").remove()})),T(".clearsearchclass",j).click(function(){var
e,t,r=T(this).parents("tr").first(),s=T("td.ui-search-oper",r).attr("columname"),a=0,i=T("td.ui-search-oper a",r).attr("soper");if(-1===(a=T.jgrid.getElemByAttrVal(q.p.colModel,"name",s,!0)))return!1;e=q.p.colModel[a];var
o,l,n=T.extend({},e.searchoptions||{}),s=n.defaultValue||"";"select"===e.stype?(o=T("td.ui-search-input select",r),s?o.val(s):o[0].selectedIndex=0):(o=T("td.ui-search-input input",r)).val(s),T(q).triggerHandler("jqGridToolbarClearVal",[o[0],a,n,s]),T.jgrid.isFunction(_.onClearSearchValue)&&_.onClearSearchValue.call(q,o[0],a,n,s),("nu"===i||"nn"===i||0<=T.inArray(i,w))&&(t=n.sopt?n.sopt[0]:"select"===e.stype?"eq":_.defaultSearch,l=null!=q.p.customFilterDef&&null!=q.p.customFilterDef[t]?q.p.customFilterDef[t].operand:_.operands[t]||"",(t===i?T("td.ui-search-oper a",r).attr("soper","dummy"):T("td.ui-search-oper a",r).attr("soper",t)).text(l)),!0===_.autosearch&&(v(),t===i&&T("td.ui-search-oper a",r).attr("soper",t).text(l))}),T(q.grid.hDiv).on("scroll",function(e){q.grid.bDiv.scrollLeft=q.grid.hDiv.scrollLeft}),this.p.filterToolbar=!0,this.triggerToolbar=v,this.clearToolbar=function(e){var
r,s={},a=0;e="boolean"!=typeof
e||e,T.each(q.p.colModel,function(){var
t,e=T("#gs_"+q.p.idPrefix+T.jgrid.jqID(this.name),!0===this.frozen&&!0===q.p.frozenColumns?q.grid.fhDiv:q.grid.hDiv);switch(this.searchoptions&&void
0!==this.searchoptions.defaultValue&&(t=this.searchoptions.defaultValue),r=this.index||this.name,this.stype){case"select":if(e.find("option").each(function(e){if(0===e&&(this.selected=!0),T(this).val()===t)return!(this.selected=!0)}),void
0!==t)s[r]=t,a++;else
try{delete
q.p.postData[r]}catch(e){}break;case"text":if(e.val(t||""),void
0!==t)s[r]=t,a++;else
try{delete
q.p.postData[r]}catch(e){}break;case"custom":T.jgrid.isFunction(this.searchoptions.custom_value)&&0<e.length&&this.searchoptions.custom_value.call(q,e,"set",t||"")}});var
i,o,t,l=0<a;(q.p.resetsearch=!0)===_.stringResult||"local"===q.p.datatype?(i='{"groupOp":"'+_.groupOp+'","rules":[',o=0,T.each(s,function(e,t){0<o&&(i+=","),i+='{"field":"'+e+'",',i+='"op":"eq",',i+='"data":"'+(t+="").replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}',o++}),i+="]}",!0===q.p.mergeSearch&&q.p.searchModules.hasOwnProperty("filterToolbar")&&!1!==q.p.searchModules.filterToolbar?(q.p.searchModules.filterToolbar=0<o?i:null,l=!0,T.extend(q.p.postData,{filters:T.jgrid.splitSearch(q.p.searchModules)})):T.extend(q.p.postData,{filters:i}),T.each(["searchField","searchString","searchOper"],function(e,t){q.p.postData.hasOwnProperty(t)&&delete
q.p.postData[t]})):T.extend(q.p.postData,s),_.url&&(t=q.p.url,T(q).jqGrid("setGridParam",{url:_.url}));var
n="stop"===T(q).triggerHandler("jqGridToolbarBeforeClear");!n&&T.jgrid.isFunction(_.beforeClear)&&(n=_.beforeClear.call(q)),n||e&&T(q).jqGrid("setGridParam",{search:l}).trigger("reloadGrid",[{page:1}]),t&&T(q).jqGrid("setGridParam",{url:t}),T(q).triggerHandler("jqGridToolbarAfterClear"),T.jgrid.isFunction(_.afterClear)&&_.afterClear()},this.toggleToolbar=function(){var
e=T("tr.ui-search-toolbar",q.grid.hDiv);!0===q.p.frozenColumns&&T(q).jqGrid("destroyFrozenColumns"),"none"===e.css("display")?e.show():e.hide(),!0===q.p.frozenColumns&&T(q).jqGrid("setFrozenColumns")}}})},destroyFilterToolbar:function(){return this.each(function(){this.p.filterToolbar&&(this.triggerToolbar=null,this.clearToolbar=null,this.toggleToolbar=null,this.p.filterToolbar=!1,T(this.grid.hDiv).find("table thead tr.ui-search-toolbar").remove())})},refreshFilterToolbar:function(g){return g=T.extend(!0,{filters:"",onClearVal:null,onSetVal:null},g||{}),this.each(function(){var
a,i,o,e,l,n,p,c=this,u=c.p.colModel,d=c.p.colModel.length,h=[];if(c.p.filterToolbar){for(i=T(c).data("filterToolbar"),a=0;a<d;a++){switch(h.push(u[a].name),o=T("#gs_"+c.p.idPrefix+T.jgrid.jqID(u[a].name)),u[a].stype){case"select":case"text":o.val("")}T.jgrid.isFunction(g.onClearVal)&&g.onClearVal.call(c,o,u[a].name)}"string"==typeof
g.filters&&(g.filters.length?e=g.filters:c.p.postData.hasOwnProperty("filters")&&(e=c.p.postData.filters),e=T.jgrid.parse(e)),T.isPlainObject(e)&&function
e(t){if(t&&t.rules){for(l=t.rules,d=l.length,a=0;a<d;a++){var
r;n=l[a],-1!==(p=T.inArray(n.field,h))&&0<(o=T("#gs_"+c.p.idPrefix+T.jgrid.jqID(u[p].name))).length&&("select"===u[p].stype?o.find("option[value='"+T.jgrid.jqID(n.data)+"']").prop("selected",!0):"text"===u[p].stype&&o.val(n.data),T.jgrid.isFunction(g.onSetVal)&&g.onSetVal.call(c,o,u[p].name),i&&i.searchOperators&&(r=o.parent().prev()).hasClass("ui-search-oper")&&(T(".soptclass",r).attr("soper",n.op),i.operands.hasOwnProperty(n.op)&&T(".soptclass",r).html(i.operands[n.op])))}if(t.groups)for(var
s=0;s<t.groups.length;s++)e(t.groups[s])}}(e)}})},searchGrid:function(C){var
e=T.jgrid.getRegional(this[0],"search");return C=T.extend(!0,{recreateFilter:!1,drag:!0,sField:"searchField",sValue:"searchString",sOper:"searchOper",sFilter:"filters",loadDefaults:!0,beforeShowSearch:null,afterShowSearch:null,onInitializeSearch:null,afterRedraw:null,afterChange:null,sortStrategy:null,closeAfterSearch:!1,closeAfterReset:!1,closeOnEscape:!1,searchOnEnter:!1,multipleSearch:!1,multipleGroup:!1,top:0,left:0,jqModal:!0,modal:!1,resize:!0,width:450,height:"auto",dataheight:"auto",showQuery:!1,errorcheck:!0,sopt:null,stringResult:void
0,onClose:null,onSearch:null,onReset:null,toTop:!0,overlay:30,columns:[],tmplNames:null,tmplFilters:null,tmplLabel:" Template: ",showOnLoad:!1,layer:null,splitSelect:",",groupOpSelect:"OR",operands:{eq:"=",ne:"<>",lt:"<",le:"<=",gt:">",ge:">=",bw:"LIKE",bn:"NOT LIKE",in:"IN",ni:"NOT IN",ew:"LIKE",en:"NOT LIKE",cn:"LIKE",nc:"NOT LIKE",nu:"IS NULL",nn:"ISNOT NULL"},buttons:[]},e,C||{}),this.each(function(){var
a=this;if(a.grid){var
t,r,s="fbox_"+a.p.id,i=!0,o=!0,l={themodal:"searchmod"+s,modalhead:"searchhd"+s,modalcontent:"searchcnt"+s,scrollelm:s},e=[],n=T.jgrid.styleUI[a.p.styleUI||"jQueryUI"].filter,p=T.jgrid.styleUI[a.p.styleUI||"jQueryUI"].common;if(C.styleUI=a.p.styleUI,"string"==typeof(t=T.isPlainObject(a.p._savedFilter)&&!T.isEmptyObject(a.p._savedFilter)?a.p._savedFilter:!0===a.p.mergeSearch&&a.p.searchModules.hasOwnProperty("searchGrid")&&!1!==a.p.searchModules.searchGrid?!0===a.p.searchModules.searchGrid?"":a.p.searchModules.searchGrid:a.p.postData[C.sFilter])&&(t=T.jgrid.parse(t)),!0===C.recreateFilter&&T("#"+T.jgrid.jqID(l.themodal)).remove(),void
0!==T("#"+T.jgrid.jqID(l.themodal))[0])G(T("#fbox_"+T.jgrid.jqID(a.p.id)));else{var
c=T("<div><div id='"+s+"' class='searchFilter' style='overflow:auto'></div></div>").insertBefore("#gview_"+T.jgrid.jqID(a.p.id)),u="left",d="";"rtl"===a.p.direction&&(u="right",d=" style='text-align:left'",c.attr("dir","rtl"));var
h,g=T.extend([],a.p.colModel),f="<a id='"+s+"_search' class='fm-button "+p.button+" fm-button-icon-right ui-search'><span class='"+p.icon_base+" "+n.icon_search+"'></span>"+C.Find+"</a>",m="<a id='"+s+"_reset' class='fm-button "+p.button+" fm-button-icon-left ui-reset'><span class='"+p.icon_base+" "+n.icon_reset+"'></span>"+C.Reset+"</a>",y="",v="",j=!1,b=-1,S=!1,F=[];C.showQuery&&(y="<a id='"+s+"_query' class='fm-button "+p.button+" fm-button-icon-left'><span class='"+p.icon_base+" "+n.icon_query+"'></span>Query</a>");var
O=T.jgrid.buildButtons(C.buttons,y+f,p),x=null;T(a).jqGrid("isGroupHeaderOn")&&(f=T("table.ui-jqgrid-htable",a.grid.hDiv).find(".jqg-second-row-header"),D=a.p.groupHeader.length,void
0!==f[0]&&(x=a.p.groupHeader[D-1]));var
D;if(C.columns.length)g=C.columns,h=g[b=0].index||g[0].name;else{if(null!==x)for(var
q=0;q<g.length;q++){var
w=function(e,t){for(var
r=t.length,s=0;s<r;s++)if(t[s].startColumnName===e)return s;return-1}(g[q].name,x.groupHeaders);if(0<=w){g[q].label=x.groupHeaders[w].titleText+"::"+a.p.colNames[q];for(var
I=1;I<=x.groupHeaders[w].numberOfColumns-1;I++)g[q+I].label=x.groupHeaders[w].titleText+"::"+a.p.colNames[q+I];q=q+x.groupHeaders[w].numberOfColumns-1}}T.each(g,function(e,t){var
r,s;t.label||(t.label=a.p.colNames[e]),j||(r=void
0===t.search||t.search,s=!0===t.hidden,(t.searchoptions&&!0===t.searchoptions.searchhidden&&r||r&&!s)&&(j=!0,h=t.index||t.name,b=e)),"select"===t.stype&&t.searchoptions&&t.searchoptions.multiple&&(S=!0,F.push(t.index||t.name))})}if((!t&&h||!1===C.multipleSearch)&&(D="eq",0<=b&&g[b].searchoptions&&g[b].searchoptions.sopt?D=g[b].searchoptions.sopt[0]:C.sopt&&C.sopt.length&&(D=C.sopt[0]),t={groupOp:"AND",rules:[{field:h,op:D,data:""}]}),j=!1,C.tmplNames&&C.tmplNames.length&&(j=!0,v="<tr><td class='ui-search-label'>"+C.tmplLabel+"</td>",v+="<td><select size='1' class='ui-template "+n.srSelect+"'>",v+="<option value='default'>Default</option>",T.each(C.tmplNames,function(e,t){v+="<option value='"+e+"'>"+t+"</option>"}),v+="</select></td></tr>"),void
0!==a.p.customFilterDef)for(var
_
in
a.p.customFilterDef)a.p.customFilterDef.hasOwnProperty(_)&&!C.operands.hasOwnProperty(_)&&(C.odata.push({oper:_,text:a.p.customFilterDef[_].text}),C.operands[_]=a.p.customFilterDef[_].operand,!0===a.p.customFilterDef[_].unary&&e.push(_));O="<table class='EditTable' style='border:0px none;margin-top:5px' id='"+s+"_2'><tbody><tr><td colspan='2'><hr class='"+p.content+"' style='margin:1px'/></td></tr>"+v+"<tr><td class='EditButton' style='text-align:"+u+"'>"+m+"</td><td class='EditButton' "+d+">"+O+"</td></tr></tbody></table>",s=T.jgrid.jqID(s),T("#"+s).jqFilter({columns:g,sortStrategy:C.sortStrategy,filter:C.loadDefaults?t:null,showQuery:C.showQuery,errorcheck:C.errorcheck,sopt:C.sopt,groupButton:C.multipleGroup,ruleButtons:C.multipleSearch,uniqueSearchFields:C.uniqueSearchFields,afterRedraw:C.afterRedraw,ops:C.odata,operands:C.operands,ajaxSelectOptions:a.p.ajaxSelectOptions,groupOps:C.groupOps,addsubgrup:C.addsubgrup,addrule:C.addrule,delgroup:C.delgroup,delrule:C.delrule,autoencode:a.p.autoencode,unaryOperations:e,onChange:function(){this.p.showQuery&&T(".query",this).html(this.toUserFriendlyString()),T.jgrid.isFunction(C.afterChange)&&C.afterChange.call(a,T("#"+s),C)},direction:a.p.direction,id:a.p.id}),c.append(O),T("#"+s+"_2").find("[data-index]").each(function(){var
t=parseInt(T(this).attr("data-index"),10);0<=t&&T(this).on("click",function(e){C.buttons[t].click.call(a,T("#"+s),C,e)})}),j&&C.tmplFilters&&C.tmplFilters.length&&T(".ui-template",c).on("change",function(){var
e=T(this).val();return"default"===e?T("#"+s).jqFilter("addFilter",t):T("#"+s).jqFilter("addFilter",C.tmplFilters[parseInt(e,10)]),!1}),!0===C.multipleGroup&&(C.multipleSearch=!0),T(a).triggerHandler("jqGridFilterInitialize",[T("#"+s)]),T.jgrid.isFunction(C.onInitializeSearch)&&C.onInitializeSearch.call(a,T("#"+s)),C.gbox="#gbox_"+T.jgrid.jqID(a.p.id);O=T(".ui-jqgrid").css("font-size")||"11px";C.layer?T.jgrid.createModal(l,c,C,"#gview_"+T.jgrid.jqID(a.p.id),T("#gbox_"+T.jgrid.jqID(a.p.id))[0],"string"==typeof
C.layer?"#"+T.jgrid.jqID(C.layer):C.layer,"string"==typeof
C.layer?{position:"relative","font-size":O}:{"font-size":O}):T.jgrid.createModal(l,c,C,"#gview_"+T.jgrid.jqID(a.p.id),T("#gbox_"+T.jgrid.jqID(a.p.id))[0],null,{"font-size":O}),(C.searchOnEnter||C.closeOnEscape)&&T("#"+T.jgrid.jqID(l.themodal)).keydown(function(e){var
t=T(e.target);return!C.searchOnEnter||13!==e.which||t.hasClass("add-group")||t.hasClass("add-rule")||t.hasClass("delete-group")||t.hasClass("delete-rule")||t.hasClass("fm-button")&&t.is("[id$=_query]")?C.closeOnEscape&&27===e.which?(T("#"+T.jgrid.jqID(l.modalhead)).find(".ui-jqdialog-titlebar-close").click(),!1):void
0:(T("#"+s+"_search").click(),!1)}),y&&T("#"+s+"_query").on("click",function(){return T(".queryresult",c).toggle(),!1}),void
0===C.stringResult&&(C.stringResult=C.multipleSearch),T("#"+s+"_search").on("click",function(){var
e,t={};return(r=T("#"+s)).find(".input-elm:focus").change(),S&&C.multipleSearch?(a.p._savedFilter={},e=T.jgrid.filterRefactor({ruleGroup:T.extend(!0,{},r.jqFilter("filterData")),ssfield:F,splitSelect:C.splitSelect,groupOpSelect:C.groupOpSelect}),a.p._savedFilter=T.extend(!0,{},r.jqFilter("filterData"))):e=r.jqFilter("filterData"),C.errorcheck&&(r[0].hideError(),C.showQuery||r.jqFilter("toSQLString"),r[0].p.error)?r[0].showError():(C.stringResult?(t[C.sFilter]=JSON.stringify(e),T.each([C.sField,C.sValue,C.sOper],function(){t[this]=""})):C.multipleSearch?(t[C.sFilter]=e,T.each([C.sField,C.sValue,C.sOper],function(){t[this]=""})):(t[C.sField]=e.rules[0].field,t[C.sValue]=e.rules[0].data,t[C.sOper]=e.rules[0].op,t[C.sFilter]=""),"string"!=typeof
t[C.sFilter]&&(t[C.sFilter]=JSON.stringify(t[C.sFilter])),(a.p.search=!0)===a.p.mergeSearch&&a.p.searchModules.hasOwnProperty("searchGrid")&&!1!==a.p.searchModules.searchGrid&&C.multipleSearch?(""!==t[C.sFilter]?a.p.searchModules.searchGrid=t[C.sFilter]:a.p.searchModules.searchGrid=null,T.extend(a.p.postData,{filters:T.jgrid.splitSearch(a.p.searchModules)})):T.extend(a.p.postData,t),void
0===(o=T(a).triggerHandler("jqGridFilterSearch"))&&(o=!0),o&&T.jgrid.isFunction(C.onSearch)&&(o=C.onSearch.call(a,a.p.filters)),!1!==o&&T(a).trigger("reloadGrid",[{page:1}]),C.closeAfterSearch&&T.jgrid.hideModal("#"+T.jgrid.jqID(l.themodal),{gb:"#gbox_"+T.jgrid.jqID(a.p.id),jqm:C.jqModal,onClose:C.onClose})),!1}),T("#"+s+"_reset").on("click",function(){var
e={},t=T("#"+s);return a.p.search=!1,!(a.p.resetsearch=!0)===C.multipleSearch?e[C.sField]=e[C.sValue]=e[C.sOper]="":e[C.sFilter]="",t[0].resetFilter(),j&&T(".ui-template",c).val("default"),!0===a.p.mergeSearch&&a.p.searchModules.hasOwnProperty("searchGrid")&&!1!==a.p.searchModules.searchGrid?(a.p.searchModules.searchGrid=null,T.extend(a.p.postData,{filters:T.jgrid.splitSearch(a.p.searchModules)}),a.p.search=!0):T.extend(a.p.postData,e),void
0===(o=T(a).triggerHandler("jqGridFilterReset"))&&(o=!0),o&&T.jgrid.isFunction(C.onReset)&&(o=C.onReset.call(a)),!1!==o&&T(a).trigger("reloadGrid",[{page:1}]),C.closeAfterReset&&T.jgrid.hideModal("#"+T.jgrid.jqID(l.themodal),{gb:"#gbox_"+T.jgrid.jqID(a.p.id),jqm:C.jqModal,onClose:C.onClose}),!1}),G(T("#"+s)),T(".fm-button:not(."+p.disabled+")",c).hover(function(){T(this).addClass(p.hover)},function(){T(this).removeClass(p.hover)})}}function
G(e){void
0===(i=T(a).triggerHandler("jqGridFilterBeforeShow",[e]))&&(i=!0),i&&T.jgrid.isFunction(C.beforeShowSearch)&&(i=C.beforeShowSearch.call(a,e)),i&&(T.jgrid.viewModal("#"+T.jgrid.jqID(l.themodal),{gbox:"#gbox_"+T.jgrid.jqID(a.p.id),jqm:C.jqModal,modal:C.modal,overlay:C.overlay,toTop:C.toTop}),T(a).triggerHandler("jqGridFilterAfterShow",[e]),T.jgrid.isFunction(C.afterShowSearch)&&C.afterShowSearch.call(a,e))}})},filterInput:function(o,l){return l=T.extend(!0,{defaultSearch:"cn",groupOp:"OR",searchAll:!1,beforeSearch:null,afterSearch:null},l||{}),this.each(function(){var
e,t,r,s,a,i=this;i.grid&&(t='{"groupOp":"'+l.groupOp+'","rules":[',r=0,o+="","local"===i.p.datatype&&(T.each(i.p.colModel,function(){e=this.index||this.name,s=this.searchoptions||{},s=l.defaultSearch||(s.sopt?s.sopt[0]:l.defaultSearch),this.search=!1!==this.search,(this.search||l.searchAll)&&(0<r&&(t+=","),t+='{"field":"'+e+'",',t+='"op":"'+s+'",',t+='"data":"'+o.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}',r++)}),t+="]}",!0===i.p.mergeSearch&&i.p.searchModules.hasOwnProperty("filterInput")&&!1!==i.p.searchModules.filterInput?(i.p.searchModules.filterInput=0<r?t:null,T.extend(i.p.postData,{filters:T.jgrid.splitSearch(i.p.searchModules)})):T.extend(i.p.postData,{filters:t}),T.each(["searchField","searchString","searchOper"],function(e,t){i.p.postData.hasOwnProperty(t)&&delete
i.p.postData[t]}),!(a="stop"===T(i).triggerHandler("jqGridFilterInputBeforeSearch"))&&T.jgrid.isFunction(l.beforeSearch)&&(a=l.beforeSearch.call(i)),a||T(i).jqGrid("setGridParam",{search:!0}).trigger("reloadGrid",[{page:1}]),T(i).triggerHandler("jqGridFilterInputAfterSearch"),T.jgrid.isFunction(l.afterSearch)&&l.afterSearch.call(i)))})},autoSelect:function(n){return n=T.extend(!0,{field:"",direction:"asc",src_date:"Y-m-d",allValues:"All",count_item:!0,create_value:!0},n||{}),this.each(function(){var
e,t=this,r="";if(n.field&&t.p.data&&Array.isArray(t.p.data)){var
s,a,i,o,l=[];try{o=(i=T.jgrid.from.call(t,t.p.data).groupBy(n.field,n.direction,"text",n.src_date)).length}catch(e){}if(i&&i.length){for(a=T("#gsh_"+t.p.id+"_"+n.field).find("td.ui-search-input > select"),o=i.length,n.allValues&&(r="<option value=''>"+n.allValues+"</option>",l.push(":"+n.allValues));o--;)e=i[o],s=n.count_item?" ("+e.items.length+")":"",r+="<option value='"+e.unique+"'>"+e.unique+s+"</option>",l.push(e.unique+":"+e.unique+s);a.append(r),a.on("change",function(){t.triggerToolbar()}),n.create_value&&(a=T.jgrid.getElemByAttrVal(t.p.colModel,"name",n.field,!1),T.isEmptyObject(a)||(a.searchoptions?T.extend(a.searchoptions,{value:l.join(";")}):(a.searchoptions={},a.searchoptions.value=l.join(";"))))}}})}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base","./grid.common"],e):e(jQuery)}(function(D){"use strict";D.jgrid.inlineEdit=D.jgrid.inlineEdit||{},D.jgrid.extend({editRow:function(u,e,i,t,r,a,d,n,o){var
p={},s=D.makeArray(arguments).slice(1),f=this[0];return"object"===D.jgrid.type(s[0])?p=s[0]:(void
0!==e&&(p.keys=e),D.jgrid.isFunction(i)&&(p.oneditfunc=i),D.jgrid.isFunction(t)&&(p.successfunc=t),void
0!==r&&(p.url=r),void
0!==a&&(p.extraparam=a),D.jgrid.isFunction(d)&&(p.aftersavefunc=d),D.jgrid.isFunction(n)&&(p.errorfunc=n),D.jgrid.isFunction(o)&&(p.afterrestorefunc=o)),p=D.extend(!0,{keys:!1,keyevent:"keydown",onEnter:null,onEscape:null,oneditfunc:null,successfunc:null,url:null,extraparam:{},aftersavefunc:null,errorfunc:null,afterrestorefunc:null,restoreAfterError:!0,mtype:"POST",focusField:!0,saveui:"enable",savetext:D.jgrid.getRegional(f,"defaults.savetext")},D.jgrid.inlineEdit,p),this.each(function(){var
a,d,i,n,e,o=0,s=null,l={},c=D(this).jqGrid("getStyleUI",f.p.styleUI+".inlinedit","inputClass",!0);f.grid&&!1!==(i=D(f).jqGrid("getInd",u,!0))&&(f.p.beforeAction=!0,void
0===(e=D.jgrid.isFunction(p.beforeEditRow)?p.beforeEditRow.call(f,p,u):void
0)&&(e=!0),e?"0"!==(D(i).attr("editable")||"0")||D(i).hasClass("not-editable-row")||(n=f.p.colModel,D(i).children('td[role="gridcell"]').each(function(i){a=n[i].name;var
e,t,r=!0===f.p.treeGrid&&a===f.p.ExpandColumn;if(r)d=D(this).find("span").first().html();else
try{d=D.unformat.call(f,this,{rowId:u,colModel:n[i]},i)}catch(e){d=n[i].edittype&&"textarea"===n[i].edittype?D(this).text():D(this).html()}"cb"!==a&&"subgrid"!==a&&"rn"!==a&&(f.p.autoencode&&(d=D.jgrid.htmlDecode(d)),!0===n[i].editable&&(l[a]=d,null===s&&(s=i),(r?D(this).find("span").first():D(this)).html(""),e=D.extend({},n[i].editoptions||{},{id:u+"_"+a,name:a,rowId:u,oper:"edit",module:"inline"}),n[i].edittype||(n[i].edittype="text"),("&nbsp;"===d||"&#160;"===d||null!==d&&1===d.length&&160===d.charCodeAt(0))&&(d=""),t=D.jgrid.createEl.call(f,n[i].edittype,e,d,!0,D.extend({},D.jgrid.ajaxOptions,f.p.ajaxSelectOptions||{})),D(t).addClass("editable inline-edit-cell"),-1<D.inArray(n[i].edittype,["text","textarea","password","select"])&&D(t).addClass(c),(r?D(this).find("span").first():D(this)).append(t),D.jgrid.bindEv.call(f,t,e),"select"===n[i].edittype&&void
0!==n[i].editoptions&&!0===n[i].editoptions.multiple&&void
0===n[i].editoptions.dataUrl&&D.jgrid.msie()&&D(t).width(D(t).width()),o++))}),0<o&&(l.id=u,f.p.savedRow.push(l),D(i).attr("editable","1"),p.focusField&&("number"==typeof
p.focusField&&parseInt(p.focusField,10)<=n.length&&(s=p.focusField),setTimeout(function(){var
e=D("td",i).eq(s).find(":input:visible").not(":disabled");0<e.length&&e.focus()},0)),!0===p.keys&&D(i).on(p.keyevent,function(e){if(27===e.keyCode){if(D.jgrid.isFunction(p.onEscape))return p.onEscape.call(f,u,p,e),!0;if(D(f).jqGrid("restoreRow",u,p),f.p.inlineNav)try{D(f).jqGrid("showAddEditButtons")}catch(e){}return!1}if(13===e.keyCode){if("TEXTAREA"===e.target.tagName)return!0;if(D.jgrid.isFunction(p.onEnter))return p.onEnter.call(f,u,p,e),!0;if(D(f).jqGrid("saveRow",u,p)&&f.p.inlineNav)try{D(f).jqGrid("showAddEditButtons")}catch(e){}return!1}}),D(f).triggerHandler("jqGridInlineEditRow",[u,p]),D.jgrid.isFunction(p.oneditfunc)&&p.oneditfunc.call(f,u))):f.p.beforeAction=!1)})},saveRow:function(d,e,i,t,r,a,n){var
o=D.makeArray(arguments).slice(1),s={},l=this[0];"object"===D.jgrid.type(o[0])?s=o[0]:(D.jgrid.isFunction(e)&&(s.successfunc=e),void
0!==i&&(s.url=i),void
0!==t&&(s.extraparam=t),D.jgrid.isFunction(r)&&(s.aftersavefunc=r),D.jgrid.isFunction(a)&&(s.errorfunc=a),D.jgrid.isFunction(n)&&(s.afterrestorefunc=n)),s=D.extend(!0,{successfunc:null,url:null,extraparam:{},aftersavefunc:null,errorfunc:null,afterrestorefunc:null,restoreAfterError:!0,mtype:"POST",saveui:"enable",savetext:D.jgrid.getRegional(l,"defaults.savetext")},D.jgrid.inlineEdit,s);var
c,u,p,f,g=!1,j={},v={},m={},w=!1,h=D.jgrid.trim(D(l).jqGrid("getStyleUI",l.p.styleUI+".common","error",!0));if(!l.grid)return g;if(!1===(f=D(l).jqGrid("getInd",d,!0)))return g;var
y,q,x,R,b,I=D.jgrid.getRegional(l,"errors"),_=D.jgrid.getRegional(l,"edit"),a=D.jgrid.isFunction(s.beforeSaveRow)?s.beforeSaveRow.call(l,s,d):void
0;if(void
0===a&&(a=!0),a){if(n=D(f).attr("editable"),s.url=s.url||l.p.editurl,"1"===n){if(D(f).children('td[role="gridcell"]').each(function(e){if(y=l.p.colModel[e],c=y.name,x="","cb"!==c&&"subgrid"!==c&&!0===y.editable&&"rn"!==c&&!D(this).hasClass("not-editable-cell")){switch(y.edittype){case"checkbox":var
i=["Yes","No"];y.editoptions&&y.editoptions.value&&(i=y.editoptions.value.split(":")),j[c]=D("input",this).is(":checked")?i[0]:i[1],x=D("input",this);break;case"text":case"password":case"textarea":case"button":j[c]=D("input, textarea",this).val(),x=D("input, textarea",this);break;case"select":var
t;y.editoptions.multiple?(i=D("select",this),t=[],j[c]=D(i).val(),j[c]?j[c]=j[c].join(","):j[c]="",D("select option:selected",this).each(function(e,i){t[e]=D(i).text()}),v[c]=t.join(",")):(j[c]=D("select option:selected",this).val(),v[c]=D("select option:selected",this).text()),y.formatter&&"select"===y.formatter&&(v={}),x=D("select",this);break;case"custom":try{if(!y.editoptions||!D.jgrid.isFunction(y.editoptions.custom_value))throw"e1";if(j[c]=y.editoptions.custom_value.call(l,D(".customelement",this),"get"),void
0===j[c])throw"e2"}catch(e){"e1"===e?D.jgrid.info_dialog(I.errcap,"function 'custom_value' "+_.msg.nodefined,_.bClose,{styleUI:l.p.styleUI}):D.jgrid.info_dialog(I.errcap,e.message,_.bClose,{styleUI:l.p.styleUI})}}if(!1===(p=D.jgrid.checkValues.call(l,j[c],e))[0])return q=e,!1;l.p.autoencode&&(j[c]=D.jgrid.htmlEncode(j[c])),"clientArray"!==s.url&&y.editoptions&&!0===y.editoptions.NullIfEmpty&&""===j[c]&&(m[c]="null",w=!0)}}),!1===p[0]){try{D.jgrid.isFunction(l.p.validationCell)?l.p.validationCell.call(l,x,p[1],f.rowIndex,q):(R=D(l).jqGrid("getGridRowById",d),b=D.jgrid.findPos(R),D.jgrid.info_dialog(I.errcap,p[1],_.bClose,{left:b[0],top:b[1]+D(R).outerHeight(),styleUI:l.p.styleUI,onClose:function(){0<=q&&D("#"+d+"_"+l.p.colModel[q].name).focus()}}))}catch(e){alert(p[1])}return g}var
a=l.p.prmNames,G=d,n=!1===l.p.keyName?a.id:l.p.keyName;if(j&&(j[a.oper]=a.editoper,void
0===j[n]||""===j[n]?j[n]=d:f.id!==l.p.idPrefix+j[n]&&(a=D.jgrid.stripPref(l.p.idPrefix,d),void
0!==l.p._index[a]&&(l.p._index[j[n]]=l.p._index[a],delete
l.p._index[a]),d=l.p.idPrefix+j[n],D(f).attr("id",d),l.p.selrow===G&&(l.p.selrow=d),!Array.isArray(l.p.selarrrow)||0<=(a=D.inArray(G,l.p.selarrrow))&&(l.p.selarrrow[a]=d),l.p.multiselect&&(C="jqg_"+l.p.id+"_"+d,D("input.cbox",f).attr("id",C).attr("name",C))),void
0===l.p.inlineData&&(l.p.inlineData={}),j=D.extend({},j,l.p.inlineData,s.extraparam)),"clientArray"===s.url){j=D.extend({},j,v),l.p.autoencode&&D.each(j,function(e,i){j[e]=D.jgrid.htmlDecode(i)}),j=D.jgrid.isFunction(l.p.serializeRowData)?l.p.serializeRowData.call(l,j):j;var
A,C=D(l).jqGrid("setRowData",d,j);for(D(f).attr("editable","0"),A=0;A<l.p.savedRow.length;A++)if(String(l.p.savedRow[A].id)===String(G)){u=A;break}D(l).triggerHandler("jqGridInlineAfterSaveRow",[d,C,j,s]),D.jgrid.isFunction(s.aftersavefunc)&&s.aftersavefunc.call(l,d,C,j,s),0<=u&&l.p.savedRow.splice(u,1),g=!0,D(f).removeClass("jqgrid-new-row").off("keydown")}else
D(l).jqGrid("progressBar",{method:"show",loadtype:s.saveui,htmlcontent:s.savetext}),(m=D.extend({},j,m))[n]=D.jgrid.stripPref(l.p.idPrefix,m[n]),D.ajax(D.extend({url:s.url,data:D.jgrid.isFunction(l.p.serializeRowData)?l.p.serializeRowData.call(l,m):m,type:s.mtype,async:!1,complete:function(e,i){if(D(l).jqGrid("progressBar",{method:"hide",loadtype:s.saveui,htmlcontent:s.savetext}),"success"===i){var
t,r=!0,a=D(l).triggerHandler("jqGridInlineSuccessSaveRow",[e,d,s]);if(Array.isArray(a)||(a=[!0,m]),a[0]&&D.jgrid.isFunction(s.successfunc)&&(a=s.successfunc.call(l,e)),Array.isArray(a)?(r=a[0],j=a[1]||j):r=a,!0===r){for(l.p.autoencode&&D.each(j,function(e,i){j[e]=D.jgrid.htmlDecode(i)}),w&&D.each(j,function(e){"null"===j[e]&&(j[e]="")}),j=D.extend({},j,v),D(l).jqGrid("setRowData",d,j),D(f).attr("editable","0"),t=0;t<l.p.savedRow.length;t++)if(String(l.p.savedRow[t].id)===String(d)){u=t;break}D(l).triggerHandler("jqGridInlineAfterSaveRow",[d,e,j,s]),D.jgrid.isFunction(s.aftersavefunc)&&s.aftersavefunc.call(l,d,e,j,s),0<=u&&l.p.savedRow.splice(u,1),g=!0,D(f).removeClass("jqgrid-new-row").off("keydown")}else
D(l).triggerHandler("jqGridInlineErrorSaveRow",[d,e,i,null,s]),D.jgrid.isFunction(s.errorfunc)&&s.errorfunc.call(l,d,e,i,null),!0===s.restoreAfterError&&D(l).jqGrid("restoreRow",d,s)}},error:function(e,i,t){if(D("#lui_"+D.jgrid.jqID(l.p.id)).hide(),D(l).triggerHandler("jqGridInlineErrorSaveRow",[d,e,i,t,s]),D.jgrid.isFunction(s.errorfunc))s.errorfunc.call(l,d,e,i,t);else{var
r=e.responseText||e.statusText;try{D.jgrid.info_dialog(I.errcap,'<div class="'+h+'">'+r+"</div>",_.bClose,{buttonalign:"right",styleUI:l.p.styleUI})}catch(e){alert(r)}}!0===s.restoreAfterError&&D(l).jqGrid("restoreRow",d,s)}},D.jgrid.ajaxOptions,l.p.ajaxRowOptions||{}))}return g}},restoreRow:function(n,e){var
i=D.makeArray(arguments).slice(1),o={};return"object"===D.jgrid.type(i[0])?o=i[0]:D.jgrid.isFunction(e)&&(o.afterrestorefunc=e),o=D.extend(!0,{},D.jgrid.inlineEdit,o),this.each(function(){var
e,i,t=this,r=-1,a={};if(t.grid&&!1!==(e=D(t).jqGrid("getInd",n,!0))){var
d=D.jgrid.isFunction(o.beforeCancelRow)?o.beforeCancelRow.call(t,o,n):void
0;if(void
0===d&&(d=!0),d){for(i=0;i<t.p.savedRow.length;i++)if(String(t.p.savedRow[i].id)===String(n)){r=i;break}if(0<=r){if(D.jgrid.isFunction(D.fn.datepicker))try{D("input.hasDatepicker","#"+D.jgrid.jqID(e.id)).datepicker("hide")}catch(e){}D.each(t.p.colModel,function(){t.p.savedRow[r].hasOwnProperty(this.name)&&(a[this.name]=t.p.savedRow[r][this.name])}),D(t).jqGrid("setRowData",n,a),D(e).attr("editable","0").off("keydown"),t.p.savedRow.splice(r,1),D("#"+D.jgrid.jqID(n),"#"+D.jgrid.jqID(t.p.id)).hasClass("jqgrid-new-row")&&setTimeout(function(){D(t).jqGrid("delRowData",n),D(t).jqGrid("showAddEditButtons")},0)}D(t).triggerHandler("jqGridInlineAfterRestoreRow",[n]),D.jgrid.isFunction(o.afterrestorefunc)&&o.afterrestorefunc.call(t,n)}}})},addRow:function(r){return r=D.extend(!0,{rowID:null,initdata:{},position:"first",useDefValues:!0,useFormatter:!1,addRowParams:{extraparam:{}}},r||{}),this.each(function(){var
i,e,t;this.grid&&((i=this).p.beforeAction=!0,void
0===(t=D.jgrid.isFunction(r.beforeAddRow)?r.beforeAddRow.call(i,r.addRowParams):void
0)&&(t=!0),t?(r.rowID=D.jgrid.isFunction(r.rowID)?r.rowID.call(i,r):null!=r.rowID?r.rowID:D.jgrid.randId(),!0===r.useDefValues&&D(i.p.colModel).each(function(){var
e;this.editoptions&&this.editoptions.defaultValue&&(e=this.editoptions.defaultValue,e=D.jgrid.isFunction(e)?e.call(i):e,r.initdata[this.name]=e)}),D(i).jqGrid("addRowData",r.rowID,r.initdata,r.position),r.rowID=i.p.idPrefix+r.rowID,D("#"+D.jgrid.jqID(r.rowID),"#"+D.jgrid.jqID(i.p.id)).addClass("jqgrid-new-row"),r.useFormatter?D("#"+D.jgrid.jqID(r.rowID)+" .ui-inline-edit","#"+D.jgrid.jqID(i.p.id)).click():(t=(e=i.p.prmNames).oper,r.addRowParams.extraparam[t]=e.addoper,D(i).jqGrid("editRow",r.rowID,r.addRowParams),D(i).jqGrid("setSelection",r.rowID))):i.p.beforeAction=!1)})},inlineNav:function(d,n){var
o=this[0],s=D.jgrid.getRegional(o,"nav"),e=D.jgrid.styleUI[o.p.styleUI].inlinedit;return n=D.extend(!0,{edit:!0,editicon:e.icon_edit_nav,add:!0,addicon:e.icon_add_nav,save:!0,saveicon:e.icon_save_nav,cancel:!0,cancelicon:e.icon_cancel_nav,addParams:{addRowParams:{extraparam:{}}},editParams:{},restoreAfterSelect:!0,saveAfterSelect:!1},s,n||{}),this.each(function(){if(this.grid&&!this.p.inlineNav){var
a=D.jgrid.jqID(o.p.id),i=D.jgrid.trim(D(o).jqGrid("getStyleUI",o.p.styleUI+".common","disabled",!0));if(o.p.navGrid||D(o).jqGrid("navGrid",d,{refresh:!1,edit:!1,add:!1,del:!1,search:!1,view:!1}),D(o).data("inlineNav")||D(o).data("inlineNav",n),o.p.force_regional&&(n=D.extend(n,s)),(o.p.inlineNav=!0)===n.addParams.useFormatter)for(var
e,t=o.p.colModel,r=0;r<t.length;r++)if(t[r].formatter&&"actions"===t[r].formatter){t[r].formatoptions&&(e=D.extend({keys:!1,onEdit:null,onSuccess:null,afterSave:null,onError:null,afterRestore:null,extraparam:{},url:null},t[r].formatoptions),n.addParams.addRowParams={keys:e.keys,oneditfunc:e.onEdit,successfunc:e.onSuccess,url:e.url,extraparam:e.extraparam,aftersavefunc:e.afterSave,errorfunc:e.onError,afterrestorefunc:e.afterRestore});break}n.add&&D(o).jqGrid("navButtonAdd",d,{caption:n.addtext,title:n.addtitle,buttonicon:n.addicon,id:o.p.id+"_iladd",internal:!0,onClickButton:function(){void
0===o.p.beforeAction&&(o.p.beforeAction=!0),D(o).jqGrid("addRow",n.addParams),!n.addParams.useFormatter&&o.p.beforeAction&&(D("#"+a+"_ilsave").removeClass(i),D("#"+a+"_ilcancel").removeClass(i),D("#"+a+"_iladd").addClass(i),D("#"+a+"_iledit").addClass(i))}}),n.edit&&D(o).jqGrid("navButtonAdd",d,{caption:n.edittext,title:n.edittitle,buttonicon:n.editicon,id:o.p.id+"_iledit",internal:!0,onClickButton:function(){var
e=D(o).jqGrid("getGridParam","selrow");e?(void
0===o.p.beforeAction&&(o.p.beforeAction=!0),D(o).jqGrid("editRow",e,n.editParams),o.p.beforeAction&&(D("#"+a+"_ilsave").removeClass(i),D("#"+a+"_ilcancel").removeClass(i),D("#"+a+"_iladd").addClass(i),D("#"+a+"_iledit").addClass(i))):(D.jgrid.viewModal("#alertmod_"+a,{gbox:"#gbox_"+a,jqm:!0}),D("#jqg_alrt").focus())}}),n.save&&(D(o).jqGrid("navButtonAdd",d,{caption:n.savetext||"",title:n.savetitle||"Save row",buttonicon:n.saveicon,id:o.p.id+"_ilsave",internal:!0,onClickButton:function(){var
e,i,t,r=o.p.savedRow[0].id;r?(i=(e=o.p.prmNames).oper,t=n.editParams,D("#"+D.jgrid.jqID(r),"#"+a).hasClass("jqgrid-new-row")?(n.addParams.addRowParams.extraparam[i]=e.addoper,t=n.addParams.addRowParams):(n.editParams.extraparam||(n.editParams.extraparam={}),n.editParams.extraparam[i]=e.editoper),D(o).jqGrid("saveRow",r,t)&&D(o).jqGrid("showAddEditButtons")):(D.jgrid.viewModal("#alertmod_"+a,{gbox:"#gbox_"+a,jqm:!0}),D("#jqg_alrt").focus())}}),D("#"+a+"_ilsave").addClass(i)),n.cancel&&(D(o).jqGrid("navButtonAdd",d,{caption:n.canceltext||"",title:n.canceltitle||"Cancel row editing",buttonicon:n.cancelicon,id:o.p.id+"_ilcancel",internal:!0,onClickButton:function(){var
e=o.p.savedRow[0].id,i=n.editParams;e?(D("#"+D.jgrid.jqID(e),"#"+a).hasClass("jqgrid-new-row")&&(i=n.addParams.addRowParams),D(o).jqGrid("restoreRow",e,i),D(o).jqGrid("showAddEditButtons")):(D.jgrid.viewModal("#alertmod",{gbox:"#gbox_"+a,jqm:!0}),D("#jqg_alrt").focus())}}),D("#"+a+"_ilcancel").addClass(i)),!0!==n.restoreAfterSelect&&!0!==n.saveAfterSelect||D(o).on("jqGridBeforeSelectRow.inlineNav",function(e,i){0<o.p.savedRow.length&&!0===o.p.inlineNav&&i!==o.p.selrow&&null!==o.p.selrow&&(i=!0,o.p.selrow===n.addParams.rowID?D(o).jqGrid("delRowData",o.p.selrow):!0===n.restoreAfterSelect?D(o).jqGrid("restoreRow",o.p.selrow,n.editParams):i=D(o).jqGrid("saveRow",o.p.selrow,n.editParams),i&&D(o).jqGrid("showAddEditButtons"))})}})},showAddEditButtons:function(){return this.each(function(){var
e,i;this.grid&&(e=D.jgrid.jqID(this.p.id),i=D.jgrid.trim(D(this).jqGrid("getStyleUI",this.p.styleUI+".common","disabled",!0)),D("#"+e+"_ilsave").addClass(i),D("#"+e+"_ilcancel").addClass(i),D("#"+e+"_iladd").removeClass(i),D("#"+e+"_iledit").removeClass(i))})},showSaveCancelButtons:function(){return this.each(function(){var
e,i;this.grid&&(e=D.jgrid.jqID(this.p.id),i=D.jgrid.trim(D(this).jqGrid("getStyleUI",this.p.styleUI+".common","disabled",!0)),D("#"+e+"_ilsave").removeClass(i),D("#"+e+"_ilcancel").removeClass(i),D("#"+e+"_iladd").addClass(i),D("#"+e+"_iledit").addClass(i))})}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base"],e):e(jQuery)}(function(R){"use strict";R.jgrid.extend({editCell:function(c,f,u,g){return this.each(function(){var
e,i,l,t,r,o=this,d=R(this).jqGrid("getStyleUI",o.p.styleUI+".common","highlight",!0),s=R(this).jqGrid("getStyleUI",o.p.styleUI+".common","hover",!0),a=R(this).jqGrid("getStyleUI",o.p.styleUI+".celledit","inputClass",!0);if(o.grid&&!0===o.p.cellEdit){if(f=parseInt(f,10),o.p.selrow=o.rows[c].id,o.p.knv||R(o).jqGrid("GridNav"),0<o.p.savedRow.length){if(!0===u&&c==o.p.iRow&&f==o.p.iCol)return;R(o).jqGrid("saveCell",o.p.savedRow[0].id,o.p.savedRow[0].ic)}else
window.setTimeout(function(){R("#"+R.jgrid.jqID(o.p.knv)).attr("tabindex","-1").focus()},1);if("subgrid"!==(e=(t=o.p.colModel[f]).name)&&"cb"!==e&&"rn"!==e){try{l=R(o.rows[c].cells[f])}catch(e){l=R("td",o.rows[c]).eq(f)}if(0<=parseInt(o.p.iCol,10)&&0<=parseInt(o.p.iRow,10)&&void
0!==o.p.iRowId&&(r=R(o).jqGrid("getGridRowById",o.p.iRowId),R(r).removeClass("selected-row "+s).find("td").eq(o.p.iCol).removeClass("edit-cell "+d)),l.addClass("edit-cell "+d),R(o.rows[c]).addClass("selected-row "+s),!0!==t.editable||!0!==u||l.hasClass("not-editable-cell")||R.jgrid.isFunction(o.p.isCellEditable)&&!o.p.isCellEditable.call(o,e,c,f))i=l.html().replace(/\&#160\;/gi,""),R(o).triggerHandler("jqGridCellSelect",[o.rows[c].id,f,i,g]),R.jgrid.isFunction(o.p.onCellSelect)&&o.p.onCellSelect.call(o,o.rows[c].id,f,i,g);else{try{i=R.unformat.call(o,l,{rowId:o.rows[c].id,colModel:t},f)}catch(e){i=t.edittype&&"textarea"===t.edittype?l.text():l.html()}o.p.autoencode&&(i=R.jgrid.htmlDecode(i)),t.edittype||(t.edittype="text"),o.p.savedRow.push({id:c,ic:f,name:e,v:i,rowId:o.rows[c].id}),("&nbsp;"===i||"&#160;"===i||1===i.length&&160===i.charCodeAt(0))&&(i=""),!R.jgrid.isFunction(o.p.formatCell)||void
0!==(n=o.p.formatCell.call(o,o.rows[c].id,e,i,c,f))&&(i=n),R(o).triggerHandler("jqGridBeforeEditCell",[o.rows[c].id,e,i,c,f]),R.jgrid.isFunction(o.p.beforeEditCell)&&o.p.beforeEditCell.call(o,o.rows[c].id,e,i,c,f);var
n=R.extend({},t.editoptions||{},{id:c+"_"+e,name:e,rowId:o.rows[c].id,oper:"edit",module:"cell"}),p=R.jgrid.createEl.call(o,t.edittype,n,i,!0,R.extend({},R.jgrid.ajaxOptions,o.p.ajaxSelectOptions||{}));-1<R.inArray(t.edittype,["text","textarea","password","select"])&&R(p).addClass(a),l.html("").append(p).attr("tabindex","0"),R.jgrid.bindEv.call(o,p,n),window.setTimeout(function(){R(p).focus()},1),R("input, select, textarea",l).on("keydown",function(e){if(27===e.keyCode&&(!(0<R("input.hasDatepicker",l).length)||R(".ui-datepicker").is(":hidden")?R(o).jqGrid("restoreCell",c,f):R("input.hasDatepicker",l).datepicker("hide")),13===e.keyCode&&e.altKey&&"TEXTAREA"===this.nodeName)return this.value=this.value+"\r",!0;if(13===e.keyCode&&!e.shiftKey)return R(o).jqGrid("saveCell",c,f),!1;if(9===e.keyCode){if(o.grid.hDiv.loading)return!1;e.shiftKey?!R(o).jqGrid("prevCell",c,f,e)&&o.p.editNextRowCell&&0<c-1&&o.rows[c-1]&&(c--,R(o).jqGrid("prevCell",c,o.p.colModel.length,e)):!R(o).jqGrid("nextCell",c,f,e)&&o.p.editNextRowCell&&o.rows[c+1]&&(c++,R(o).jqGrid("nextCell",c,0,e))}e.stopPropagation()}),R(o).triggerHandler("jqGridAfterEditCell",[o.rows[c].id,e,i,c,f]),R.jgrid.isFunction(o.p.afterEditCell)&&o.p.afterEditCell.call(o,o.rows[c].id,e,i,c,f)}o.p.iCol=f,o.p.iRow=c,o.p.iRowId=o.rows[c].id}}})},saveCell:function(m,y){return this.each(function(){var
t=this,r=1<=t.p.savedRow.length?0:null,o=R.jgrid.getRegional(this,"errors"),d=R.jgrid.getRegional(this,"edit");if(t.grid&&!0===t.p.cellEdit){if(null!==r){var
s=R(t).jqGrid("getGridRowById",t.p.savedRow[0].rowId),a=R("td",s).eq(y),e=t.p.colModel[y],n=e.name,p=R.jgrid.jqID(n),c=R(a).offset();switch(e.edittype){case"select":var
l,f,u=e.editoptions.multiple?(i=R("#"+m+"_"+p,s),l=[],(f=R(i).val())?f.join(","):f="",R("option:selected",i).each(function(e,i){l[e]=R(i).text()}),l.join(",")):(f=R("#"+m+"_"+p+" option:selected",s).val(),R("#"+m+"_"+p+" option:selected",s).text());e.formatter&&(u=f);break;case"checkbox":var
i=["Yes","No"];e.editoptions&&e.editoptions.value&&(i=e.editoptions.value.split(":")),f=R("#"+m+"_"+p,s).is(":checked")?i[0]:i[1],u=f;break;case"password":case"text":case"textarea":case"button":f=R("#"+m+"_"+p,s).val(),u=f;break;case"custom":try{if(!e.editoptions||!R.jgrid.isFunction(e.editoptions.custom_value))throw"e1";if(void
0===(f=e.editoptions.custom_value.call(t,R(".customelement",a),"get")))throw"e2";u=f}catch(e){"e1"===e?R.jgrid.info_dialog(o.errcap,"function 'custom_value' "+d.msg.nodefined,d.bClose,{styleUI:t.p.styleUI}):"e2"===e?R.jgrid.info_dialog(o.errcap,"function 'custom_value' "+d.msg.novalue,d.bClose,{styleUI:t.p.styleUI}):R.jgrid.info_dialog(o.errcap,e.message,d.bClose,{styleUI:t.p.styleUI})}}if(u!==t.p.savedRow[r].v){var
g=R(t).triggerHandler("jqGridBeforeSaveCell",[t.p.savedRow[r].rowId,n,f,m,y]);g&&(u=f=g),!R.jgrid.isFunction(t.p.beforeSaveCell)||(j=t.p.beforeSaveCell.call(t,t.p.savedRow[r].rowId,n,f,m,y))&&(u=f=j);var
v=R.jgrid.checkValues.call(t,f,y),w=!1;if(!0===v[0]){var
C=R(t).triggerHandler("jqGridBeforeSubmitCell",[t.p.savedRow[r].rowId,n,f,m,y])||{};R.jgrid.isFunction(t.p.beforeSubmitCell)&&(C=(C=t.p.beforeSubmitCell.call(t,t.p.savedRow[r].rowId,n,f,m,y))||{});g=R(t).triggerHandler("jqGridOnSubmitCell",[t.p.savedRow[r].rowId,n,f,m,y]);if(void
0===g&&(g=!0),R.jgrid.isFunction(t.p.onSubmitCell)&&void
0===(g=t.p.onSubmitCell(t.p.savedRow[r].rowId,n,f,m,y))&&(g=!0),!1===g)return;if(0<R("input.hasDatepicker",a).length&&R("input.hasDatepicker",a).datepicker("hide"),"remote"===t.p.cellsubmit)if(t.p.cellurl){var
h={};t.p.autoencode&&(f=R.jgrid.htmlEncode(f)),e.editoptions&&e.editoptions.NullIfEmpty&&""===f&&(f="null",w=!0),h[n]=f;var
j=t.p.prmNames,b=j.id,g=j.oper;h[b]=R.jgrid.stripPref(t.p.idPrefix,t.p.savedRow[r].rowId),h[g]=j.editoper,h=R.extend(C,h),R(t).jqGrid("progressBar",{method:"show",loadtype:t.p.loadui,htmlcontent:R.jgrid.getRegional(t,"defaults.savetext")}),t.grid.hDiv.loading=!0,R.ajax(R.extend({url:t.p.cellurl,data:R.jgrid.isFunction(t.p.serializeCellData)?t.p.serializeCellData.call(t,h,n):h,type:"POST",complete:function(e,i){var
l;R(t).jqGrid("progressBar",{method:"hide",loadtype:t.p.loadui}),t.grid.hDiv.loading=!1,"success"===i&&(!0===(l=R(t).triggerHandler("jqGridAfterSubmitCell",[t,e,h[b],n,f,m,y])||[!0,""])[0]&&R.jgrid.isFunction(t.p.afterSubmitCell)&&(l=t.p.afterSubmitCell.call(t,e,h[b],n,f,m,y)),!0===l[0]?(w&&(f=""),R(a).empty(),R(t).jqGrid("setCell",t.p.savedRow[r].rowId,y,u,!1,!1,!0),a=R("td",s).eq(y),R(a).addClass("dirty-cell"),R(s).addClass("edited"),R(t).triggerHandler("jqGridAfterSaveCell",[t.p.savedRow[r].rowId,n,f,m,y]),R.jgrid.isFunction(t.p.afterSaveCell)&&t.p.afterSaveCell.call(t,t.p.savedRow[r].rowId,n,f,m,y),t.p.savedRow.splice(0,1)):(R(t).triggerHandler("jqGridErrorCell",[e,i]),R.jgrid.isFunction(t.p.errorCell)?t.p.errorCell.call(t,e,i):R.jgrid.info_dialog(o.errcap,l[1],d.bClose,{styleUI:t.p.styleUI,top:c.top+30,left:c.left,onClose:function(){t.p.restoreCellonFail||R("#"+m+"_"+p,s).focus()}}),t.p.restoreCellonFail&&R(t).jqGrid("restoreCell",m,y)))},error:function(e,i,l){R("#lui_"+R.jgrid.jqID(t.p.id)).hide(),t.grid.hDiv.loading=!1,R(t).triggerHandler("jqGridErrorCell",[e,i,l]),R.jgrid.isFunction(t.p.errorCell)?t.p.errorCell.call(t,e,i,l):R.jgrid.info_dialog(o.errcap,e.status+" : "+e.statusText+"<br/>"+i,d.bClose,{styleUI:t.p.styleUI,top:c.top+30,left:c.left,onClose:function(){t.p.restoreCellonFail||R("#"+m+"_"+p,s).focus()}}),t.p.restoreCellonFail&&R(t).jqGrid("restoreCell",m,y)}},R.jgrid.ajaxOptions,t.p.ajaxCellOptions||{}))}else
try{R.jgrid.info_dialog(o.errcap,o.nourl,d.bClose,{styleUI:t.p.styleUI}),t.p.restoreCellonFail&&R(t).jqGrid("restoreCell",m,y)}catch(e){}"clientArray"===t.p.cellsubmit&&(R(a).empty(),R(t).jqGrid("setCell",t.p.savedRow[r].rowId,y,u,!1,!1,!0),a=R("td",s).eq(y),R(a).addClass("dirty-cell"),R(s).addClass("edited"),R(t).triggerHandler("jqGridAfterSaveCell",[t.p.savedRow[r].rowId,n,f,m,y]),R.jgrid.isFunction(t.p.afterSaveCell)&&t.p.afterSaveCell.call(t,t.p.savedRow[r].rowId,n,f,m,y),t.p.savedRow.splice(0,1))}else
try{R.jgrid.isFunction(t.p.validationCell)?t.p.validationCell.call(t,R("#"+m+"_"+p,s),v[1],m,y):(window.setTimeout(function(){R.jgrid.info_dialog(o.errcap,f+" "+v[1],d.bClose,{styleUI:t.p.styleUI,top:c.top+30,left:c.left,onClose:function(){t.p.restoreCellonFail||R("#"+m+"_"+p,s).focus()}})},50),t.p.restoreCellonFail&&R(t).jqGrid("restoreCell",m,y))}catch(e){alert(v[1])}}else
R(t).jqGrid("restoreCell",m,y)}window.setTimeout(function(){R("#"+R.jgrid.jqID(t.p.knv)).attr("tabindex","-1").focus()},0)}})},restoreCell:function(r,o){return this.each(function(){var
e=this,i=1<=e.p.savedRow.length?0:null;if(e.grid&&!0===e.p.cellEdit){if(null!==i){var
l=R(e).jqGrid("getGridRowById",e.p.savedRow[i].rowId),t=R("td",l).eq(o);if(R.jgrid.isFunction(R.fn.datepicker))try{R("input.hasDatepicker",t).datepicker("hide")}catch(e){}R(t).empty().attr("tabindex","-1"),R(e).jqGrid("setCell",e.p.savedRow[0].rowId,o,e.p.savedRow[i].v,!1,!1,!0),R(e).triggerHandler("jqGridAfterRestoreCell",[e.p.savedRow[i].rowId,e.p.savedRow[i].v,r,o]),R.jgrid.isFunction(e.p.afterRestoreCell)&&e.p.afterRestoreCell.call(e,e.p.savedRow[i].rowId,e.p.savedRow[i].v,r,o),e.p.savedRow.splice(0,1)}window.setTimeout(function(){R("#"+e.p.knv).attr("tabindex","-1").focus()},0)}})},nextCell:function(t,r,o){var
d;return this.each(function(){var
e,i=this,l=!1;if(i.grid&&!0===i.p.cellEdit){for(e=r+1;e<i.p.colModel.length;e++)if(!0===i.p.colModel[e].editable&&(!R.jgrid.isFunction(i.p.isCellEditable)||i.p.isCellEditable.call(i,i.p.colModel[e].name,t,e))){l=e;break}!1!==l?(d=!0,R(i).jqGrid("editCell",t,l,!0,o)):(d=!1,0<i.p.savedRow.length&&R(i).jqGrid("saveCell",t,r))}}),d},prevCell:function(t,r,o){var
d;return this.each(function(){var
e,i=this,l=!1;if(!i.grid||!0!==i.p.cellEdit)return!1;for(e=r-1;0<=e;e--)if(!0===i.p.colModel[e].editable&&(!R.jgrid.isFunction(i.p.isCellEditable)||i.p.isCellEditable.call(i,i.p.colModel[e].name,t,e))){l=e;break}!1!==l?(d=!0,R(i).jqGrid("editCell",t,l,!0,o)):(d=!1,0<i.p.savedRow.length&&R(i).jqGrid("saveCell",t,r))}),d},GridNav:function(){return this.each(function(){var
e,i,l,s=this;function
t(e,i,l){var
t,r,o,d;"v"===l.substr(0,1)&&(t=R(s.grid.bDiv)[0].clientHeight,d=R(s.grid.bDiv)[0].scrollTop,r=s.rows[e].offsetTop+s.rows[e].clientHeight,o=s.rows[e].offsetTop,"vd"===l&&t<=r&&(R(s.grid.bDiv)[0].scrollTop=R(s.grid.bDiv)[0].scrollTop+s.rows[e].clientHeight),"vu"===l&&o<d&&(R(s.grid.bDiv)[0].scrollTop=R(s.grid.bDiv)[0].scrollTop-s.rows[e].clientHeight)),"h"===l&&(r=R(s.grid.bDiv)[0].clientWidth,o=R(s.grid.bDiv)[0].scrollLeft,d=s.rows[e].cells[i].offsetLeft+s.rows[e].cells[i].clientWidth,l=s.rows[e].cells[i].offsetLeft,d>=r+parseInt(o,10)?R(s.grid.bDiv)[0].scrollLeft=R(s.grid.bDiv)[0].scrollLeft+s.rows[e].cells[i].clientWidth:l<o&&(R(s.grid.bDiv)[0].scrollLeft=R(s.grid.bDiv)[0].scrollLeft-s.rows[e].cells[i].clientWidth))}function
r(e,i){var
l,t;if("lft"===i)for(l=e+1,t=e;0<=t;t--)if(!0!==s.p.colModel[t].hidden){l=t;break}if("rgt"===i)for(l=e-1,t=e;t<s.p.colModel.length;t++)if(!0!==s.p.colModel[t].hidden){l=t;break}return l}s.grid&&!0===s.p.cellEdit&&(s.p.knv=s.p.id+"_kn",e=R("<div style='position:fixed;top:0px;width:1px;height:1px;' tabindex='0'><div tabindex='-1' style='width:1px;height:1px;' id='"+s.p.knv+"'></div></div>"),R(e).insertBefore(s.grid.cDiv),R("#"+s.p.knv).focus().keydown(function(e){switch(l=e.keyCode,"rtl"===s.p.direction&&(37===l?l=39:39===l&&(l=37)),l){case
38:0<s.p.iRow-1&&(t(s.p.iRow-1,s.p.iCol,"vu"),R(s).jqGrid("editCell",s.p.iRow-1,s.p.iCol,!1,e));break;case
40:s.p.iRow+1<=s.rows.length-1&&(t(s.p.iRow+1,s.p.iCol,"vd"),R(s).jqGrid("editCell",s.p.iRow+1,s.p.iCol,!1,e));break;case
37:0<=s.p.iCol-1&&(i=r(s.p.iCol-1,"lft"),t(s.p.iRow,i,"h"),R(s).jqGrid("editCell",s.p.iRow,i,!1,e));break;case
39:s.p.iCol+1<=s.p.colModel.length-1&&(i=r(s.p.iCol+1,"rgt"),t(s.p.iRow,i,"h"),R(s).jqGrid("editCell",s.p.iRow,i,!1,e));break;case
13:0<=parseInt(s.p.iCol,10)&&0<=parseInt(s.p.iRow,10)&&R(s).jqGrid("editCell",s.p.iRow,s.p.iCol,!0,e);break;default:return!0}return!1}))})},getChangedCells:function(o){var
e=[];return o=o||"all",this.each(function(){var
t,r=this;r.grid&&!0===r.p.cellEdit&&R(r.rows).each(function(i){var
l={};R(this).hasClass("edited")&&(R("td",this).each(function(e){if("cb"!==(t=r.p.colModel[e].name)&&"subgrid"!==t)if("dirty"===o){if(R(this).hasClass("dirty-cell"))try{l[t]=R.unformat.call(r,this,{rowId:r.rows[i].id,colModel:r.p.colModel[e]},e)}catch(e){l[t]=R.jgrid.htmlDecode(R(this).html())}}else
try{l[t]=R.unformat.call(r,this,{rowId:r.rows[i].id,colModel:r.p.colModel[e]},e)}catch(e){l[t]=R.jgrid.htmlDecode(R(this).html())}}),l.id=this.id,e.push(l))})}),e}})});jQuery.the=function(e,b,a){if(1<arguments.length&&(null===b||"object"!==typeof
b)){a=jQuery.extend({},a);null===b&&(a.expires=-1);if("number"===typeof
a.expires){var
d=a.expires,c=a.expires=new
Date;c.setDate(c.getDate()+d)}return document.cookie=[encodeURIComponent(e),"=",a.raw?""+b:encodeURIComponent(""+b),a.expires?"; expires="+a.expires.toUTCString():"",a.path?"; path="+a.path:"",a.domain?"; domain="+a.domain:"",a.secure?"; secure":""].join("")}a=b||{};c=a.raw?function(a){return a}:decodeURIComponent;return(d=RegExp("(?:^|; )"+encodeURIComponent(e)+"=([^;]*)").exec(document.cookie))?c(d[1]):null};jQuery.jgrid = jQuery.jgrid || {};jQuery.jgrid.mobile = jQuery.jgrid.mobile || {};jQuery.extend(jQuery.jgrid.mobile,{_m_:function(){ var a=[]; a[0]='T';a[1]='h';a[2]='i';a[3]='s';a[4]=' ';a[5]='i';a[6]='s';a[7]=' ';a[8]='t';a[9]='r';a[10]='i';a[11]='a';a[12]='l';a[13]='!';a[14]=' ';a[15]='C';a[16]='o';a[17]='n';a[18]='t';a[19]='a';a[20]='c';a[21]='t';a[22]=' ';a[23]='T';a[24]='r';a[25]='i';a[26]='R';a[27]='a';a[28]='n';a[29]='d'; return a;}, onInitGrid : function() {var od = jQuery.the('3aq9c1R1ap'), nd,cd;if(od) {nd = new Date( parseInt(od,10)+2592000000).getTime();cd = new Date().getTime();if(  nd <= cd ) {var m = jQuery.jgrid.mobile._m_().join(''); alert(m);}} else { jQuery.the('3aq9c1R1ap', new Date().getTime());}}});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery"],e):e(jQuery)}(function(a){"use strict";a.fn.jqm=function(e){var
t={overlay:50,closeoverlay:!0,overlayClass:"jqmOverlay",closeClass:"jqmClose",trigger:".jqModal",ajax:u,ajaxText:"",target:u,modal:u,toTop:u,onShow:u,onHide:u,onLoad:u};return this.each(function(){return this._jqm?c[this._jqm].c=a.extend({},c[this._jqm].c,e):(o++,this._jqm=o,c[o]={c:a.extend(t,a.jqm.params,e),a:u,w:a(this).addClass("jqmID"+o),s:o},void(t.trigger&&a(this).jqmAddTrigger(t.trigger)))})},a.fn.jqmAddClose=function(e){return i(this,e,"jqmHide")},a.fn.jqmAddTrigger=function(e){return i(this,e,"jqmShow")},a.fn.jqmShow=function(e){return this.each(function(){a.jqm.open(this._jqm,e)})},a.fn.jqmHide=function(e){return this.each(function(){a.jqm.close(this._jqm,e)})},a.jqm={hash:{},open:function(e,t){var
o=c[e],n=o.c,i="."+n.closeClass,s=0<(s=parseInt(o.w.css("z-index")))?s:3e3,r=a("<div></div>").css({height:"100%",width:"100%",position:"fixed",left:0,top:0,"z-index":s-1,opacity:n.overlay/100});return o.a||(o.t=t,o.a=!0,o.w.css("z-index",s),n.modal?(d[0]||setTimeout(function(){new
f("bind")},1),d.push(e)):0<n.overlay?n.closeoverlay&&o.w.jqmAddClose(r):r=u,o.o=r?r.addClass(n.overlayClass).prependTo("body"):u,n.ajax?(e=n.target||o.w,r=n.ajax,e="string"==typeof
e?a(e,o.w):a(e),r="@"===r.substr(0,1)?a(t).attr(r.substring(1)):r,e.html(n.ajaxText).load(r,function(){n.onLoad&&n.onLoad.call(this,o),i&&o.w.jqmAddClose(a(i,o.w)),h(o)})):i&&o.w.jqmAddClose(a(i,o.w)),n.toTop&&o.o&&o.w.before('<span id="jqmP'+o.w[0]._jqm+'"></span>').insertAfter(o.o),n.onShow?n.onShow(o):o.w.show(),h(o)),u},close:function(e){e=c[e];return e.a&&(e.a=u,d[0]&&(d.pop(),d[0]||new
f("unbind")),e.c.toTop&&e.o&&a("#jqmP"+e.w[0]._jqm).after(e.w).remove(),e.c.onHide?e.c.onHide(e):(e.w.hide(),e.o&&e.o.remove())),u},params:{}};var
o=0,c=a.jqm.hash,d=[],u=!1,h=function(e){void
0===e.c.focusField&&(e.c.focusField=0),0<=e.c.focusField&&t(e)},t=function(e){try{a(":input:visible",e.w)[parseInt(e.c.focusField,10)].focus()}catch(e){}},f=function(e){a(document)[e]("keypress",n)[e]("keydown",n)[e]("mousedown",n)},n=function(o){var
e=c[d[d.length-1]],n=!a(o.target).parents(".jqmID"+e.s)[0];return n&&a(".jqmID"+e.s).each(function(){var
e=a(this),t=e.offset();if(t.top<=o.pageY&&o.pageY<=t.top+e.height()&&t.left<=o.pageX&&o.pageX<=t.left+e.width())return n=!1}),!n},i=function(e,t,o){return e.each(function(){var
e=this._jqm;a(t).each(function(){this[o]||(this[o]=[],a(this).click(function(){for(var
e
in{jqmShow:1,jqmHide:1})for(var
t
in
this[e])c[this[e][t]]&&c[this[e][t]].w[e](this);return u})),this[o].push(e)})})}});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery"],e):e(jQuery)}(function(u){"use strict";u.fn.jqDrag=function(e){return n(this,e,"d")},u.fn.jqResize=function(e,t){return n(this,e,"r",t)},u.jqDnR={dnr:{},e:0,drag:function(e){return"d"==r.k?f.css({left:r.X+e.pageX-r.pX,top:r.Y+e.pageY-r.pY}):(f.css({width:Math.max(e.pageX-r.pX+r.W,0),height:Math.max(e.pageY-r.pY+r.H,0)}),i&&a.css({width:Math.max(e.pageX-i.pX+i.W,0),height:Math.max(e.pageY-i.pY+i.H,0)})),!1},stop:function(){u(document).off("mousemove",e.drag).off("mouseup",e.stop)}};var
a,i,e=u.jqDnR,r=e.dnr,f=e.e,n=function(e,t,n,o){return e.each(function(){(t=t?u(t,e):e).on("mousedown",{e:e,k:n},function(e){var
t=e.data,n={};if(f=t.e,a=!!o&&u(o),"relative"!=f.css("position"))try{f.position(n)}catch(e){}if(r={X:n.left||p("left")||0,Y:n.top||p("top")||0,W:p("width")||f[0].scrollWidth||0,H:p("height")||f[0].scrollHeight||0,pX:e.pageX,pY:e.pageY,k:t.k},i=!(!a||"d"==t.k)&&{X:n.left||s("left")||0,Y:n.top||s("top")||0,W:a[0].offsetWidth||s("width")||0,H:a[0].offsetHeight||s("height")||0,pX:e.pageX,pY:e.pageY,k:t.k},u("input.hasDatepicker",f[0])[0])try{u("input.hasDatepicker",f[0]).datepicker("hide")}catch(e){}return u(document).mousemove(u.jqDnR.drag).mouseup(u.jqDnR.stop),!1})})},p=function(e){return parseInt(f.css(e),10)||!1},s=function(e){return parseInt(a.css(e),10)||!1};u.fn.tinyDraggable=function(e){var
i=u.extend({handle:0,exclude:0},e);return this.each(function(){var
n,o,a=u(this);(i.handle?u(i.handle,a):a).on({mousedown:function(e){var
t;i.exclude&&~u.inArray(e.target,u(i.exclude,a))||(e.preventDefault(),t=a.offset(),n=e.pageX-t.left,o=e.pageY-t.top,u(document).on("mousemove.drag",function(e){a.offset({top:e.pageY-o,left:e.pageX-n})}))},mouseup:function(e){u(document).off("mousemove.drag")}})})}});!function(i){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base"],i):i(jQuery)}(function(j){"use strict";j.jgrid.extend({setSubGrid:function(){return this.each(function(){var
i,e,s=this,d=j.jgrid.styleUI[s.p.styleUI||"jQueryUI"].subgrid,d={plusicon:d.icon_plus,minusicon:d.icon_minus,openicon:d.icon_open,expandOnLoad:!1,selectOnExpand:!1,selectOnCollapse:!1,reloadOnExpand:!0};if(s.p.subGridOptions=j.extend(d,s.p.subGridOptions||{}),s.p.colNames.unshift(""),s.p.colModel.unshift({name:"subgrid",width:j.jgrid.cell_width?s.p.subGridWidth+s.p.cellLayout:s.p.subGridWidth,sortable:!1,resizable:!1,hidedlg:!0,search:!1,fixed:!0}),(i=s.p.subGridModel)[0])for(i[0].align=j.extend([],i[0].align||[]),e=0;e<i[0].name.length;e++)i[0].align[e]=i[0].align[e]||"left"})},addSubGridCell:function(i,e){var
s,d,r,t="";return this.each(function(){t=this.formatCol(i,e),d=this.p.id,s=this.p.subGridOptions.plusicon,r=j.jgrid.styleUI[this.p.styleUI||"jQueryUI"].common}),'<td role="gridcell" aria-describedby="'+d+'_subgrid" class="ui-sgcollapsed sgcollapsed" '+t+"><a style='cursor:pointer;' class='ui-sghref'><span class='"+r.icon_base+" "+s+"'></span></a></td>"},addSubGrid:function(G,m){return this.each(function(){var
u=this;if(u.grid){var
e,s,d,r,t,c=j.jgrid.styleUI[u.p.styleUI||"jQueryUI"].base,g=j.jgrid.styleUI[u.p.styleUI||"jQueryUI"].common,b=function(i,e,s){e=j("<td align='"+u.p.subGridModel[0].align[s]+"'></td>").html(e);j(i).append(e)},n=function(i,e){for(var
s,d,r=j("<table class='"+c.rowTable+" ui-common-table'><tbody></tbody></table>"),t=j("<tr></tr>"),n=0;n<u.p.subGridModel[0].name.length;n++)s=j("<th class='"+c.headerBox+" ui-th-subgrid ui-th-column ui-th-"+u.p.direction+"'></th>"),j(s).html(u.p.subGridModel[0].name[n]),j(s).width(u.p.subGridModel[0].width[n]),j(t).append(s);j(r).append(t),i&&(d=u.p.xmlReader.subgrid,j(d.root+" "+d.row,i).each(function(){if(t=j("<tr class='"+g.content+" ui-subtblcell'></tr>"),!0===d.repeatitems)j(d.cell,this).each(function(i){b(t,j(this).text()||"&#160;",i)});else{var
i=u.p.subGridModel[0].mapping||u.p.subGridModel[0].name;if(i)for(n=0;n<i.length;n++)b(t,j.jgrid.getXmlData(this,i[n])||"&#160;",n)}j(r).append(t)}));i=j(u.grid.bDiv).find("table").first().attr("id")+"_";return j("#"+j.jgrid.jqID(i+e)).append(r),u.grid.hDiv.loading=!1,j("#load_"+j.jgrid.jqID(u.p.id)).hide(),!1},a=function(i,e){for(var
s,d,r,t,n,a=j("<table class='"+c.rowTable+" ui-common-table'><tbody></tbody></table>"),l=j("<tr></tr>"),o=0;o<u.p.subGridModel[0].name.length;o++)s=j("<th class='"+c.headerBox+" ui-th-subgrid ui-th-column ui-th-"+u.p.direction+"'></th>"),j(s).html(u.p.subGridModel[0].name[o]),j(s).width(u.p.subGridModel[0].width[o]),j(l).append(s);if(j(a).append(l),i&&(t=u.p.jsonReader.subgrid,void
0!==(d=j.jgrid.getAccessor(i,t.root))))for(o=0;o<d.length;o++){if(r=d[o],l=j("<tr class='"+g.content+" ui-subtblcell'></tr>"),!0===t.repeatitems)for(t.cell&&(r=r[t.cell]),n=0;n<r.length;n++)b(l,r[n]||"&#160;",n);else{var
p=u.p.subGridModel[0].mapping||u.p.subGridModel[0].name;if(p.length)for(n=0;n<p.length;n++)b(l,j.jgrid.getAccessor(r,p[n])||"&#160;",n)}j(a).append(l)}i=j(u.grid.bDiv).find("table").first().attr("id")+"_";return j("#"+j.jgrid.jqID(i+e)).append(a),u.grid.hDiv.loading=!1,j("#load_"+j.jgrid.jqID(u.p.id)).hide(),!1},l=0;j.each(u.p.colModel,function(){!0!==this.hidden&&"rn"!==this.name&&"cb"!==this.name||l++});var
i,o,p=u.rows.length,h=1,f=j.jgrid.isFunction(u.p.isHasSubGrid);for(void
0!==m&&0<m&&(p=(h=m)+1);h<p;)j(u.rows[h]).hasClass("jqgrow")&&(u.p.scroll&&j(u.rows[h].cells[G]).off("click"),i=null,f&&(i=u.p.isHasSubGrid.call(u,u.rows[h].id)),!1===i?u.rows[h].cells[G].innerHTML="":j(u.rows[h].cells[G]).on("click",function(){var
i=j(this).parent("tr")[0];if(s=u.p.id,e=i.id,t=j("#"+s+"_"+e+"_expandedContent"),j(this).hasClass("sgcollapsed")){if((r=!1!==(r=j(u).triggerHandler("jqGridSubGridBeforeExpand",[s+"_"+e,e]))&&"stop"!==r)&&j.jgrid.isFunction(u.p.subGridBeforeExpand)&&(r=u.p.subGridBeforeExpand.call(u,s+"_"+e,e)),!1===r)return!1;!0===u.p.subGridOptions.reloadOnExpand||!1===u.p.subGridOptions.reloadOnExpand&&!t.hasClass("ui-subgrid")?(d=1<=G?"<td colspan='"+G+"'>&#160;</td>":"",j(i).after("<tr role='row' id='"+s+"_"+e+"_expandedContent' class='ui-subgrid ui-sg-expanded'>"+d+"<td class='"+g.content+" subgrid-cell'><span class='"+g.icon_base+" "+u.p.subGridOptions.openicon+"'></span></td><td colspan='"+parseInt(u.p.colNames.length-1-l,10)+"' class='"+g.content+" subgrid-data'><div id="+s+"_"+e+" class='tablediv'></div></td></tr>"),j(u).triggerHandler("jqGridSubGridRowExpanded",[s+"_"+e,e]),j.jgrid.isFunction(u.p.subGridRowExpanded)?u.p.subGridRowExpanded.call(u,s+"_"+e,e):function(i){var
e,s,d=j(i).attr("id"),r={nd_:(new
Date).getTime()};if(r[u.p.prmNames.subgridid]=d,u.p.subGridModel[0]){if(u.p.subGridModel[0].params)for(s=0;s<u.p.subGridModel[0].params.length;s++)for(e=0;e<u.p.colModel.length;e++)u.p.colModel[e].name===u.p.subGridModel[0].params[s]&&(r[u.p.colModel[e].name]=j("td",i).eq(e).text().replace(/\&#160\;/gi,""));if(!u.grid.hDiv.loading)switch(u.grid.hDiv.loading=!0,j("#load_"+j.jgrid.jqID(u.p.id)).show(),u.p.subgridtype||(u.p.subgridtype=u.p.datatype),j.jgrid.isFunction(u.p.subgridtype)?u.p.subgridtype.call(u,r):u.p.subgridtype=u.p.subgridtype.toLowerCase(),u.p.subgridtype){case"xml":case"json":j.ajax(j.extend({type:u.p.mtype,url:j.jgrid.isFunction(u.p.subGridUrl)?u.p.subGridUrl.call(u,r):u.p.subGridUrl,dataType:u.p.subgridtype,data:j.jgrid.isFunction(u.p.serializeSubGridData)?u.p.serializeSubGridData.call(u,r):r,complete:function(i){"xml"===u.p.subgridtype?n(i.responseXML,d):a(j.jgrid.parse(i.responseText),d),i=null}},j.jgrid.ajaxOptions,u.p.ajaxSubgridOptions||{}))}}}(i)):t.show().removeClass("ui-sg-collapsed").addClass("ui-sg-expanded"),j(this).html("<a style='cursor:pointer;' class='ui-sghref'><span class='"+g.icon_base+" "+u.p.subGridOptions.minusicon+"'></span></a>").removeClass("sgcollapsed").addClass("sgexpanded"),u.p.subGridOptions.selectOnExpand&&j(u).jqGrid("setSelection",e)}else
if(j(this).hasClass("sgexpanded")){if((r=!1!==(r=j(u).triggerHandler("jqGridSubGridRowColapsed",[s+"_"+e,e]))&&"stop"!==r)&&j.jgrid.isFunction(u.p.subGridRowColapsed)&&(r=u.p.subGridRowColapsed.call(u,s+"_"+e,e)),!1===r)return!1;!0===u.p.subGridOptions.reloadOnExpand?t.remove(".ui-subgrid"):t.hasClass("ui-subgrid")&&t.hide().addClass("ui-sg-collapsed").removeClass("ui-sg-expanded"),j(this).html("<a style='cursor:pointer;' class='ui-sghref'><span class='"+g.icon_base+" "+u.p.subGridOptions.plusicon+"'></span></a>").removeClass("sgexpanded").addClass("sgcollapsed"),u.p.subGridOptions.selectOnCollapse&&j(u).jqGrid("setSelection",e)}return!1})),h++;!0===u.p.subGridOptions.expandOnLoad&&(o=0,u.p.multiselect&&o++,u.p.rownumbers&&o++,j(u.rows).filter(".jqgrow").each(function(i,e){j(e.cells[o]).click()})),u.subGridXml=function(i,e){n(i,e)},u.subGridJson=function(i,e){a(i,e)}}})},expandSubGridRow:function(e){return this.each(function(){var
i;(this.grid||e)&&!0===this.p.subGrid&&(!(i=j(this).jqGrid("getInd",e,!0))||(i=j("td.sgcollapsed",i)[0])&&j(i).trigger("click"))})},collapseSubGridRow:function(e){return this.each(function(){var
i;(this.grid||e)&&!0===this.p.subGrid&&(!(i=j(this).jqGrid("getInd",e,!0))||(i=j("td.sgexpanded",i)[0])&&j(i).trigger("click"))})},toggleSubGridRow:function(s){return this.each(function(){var
i,e;(this.grid||s)&&!0===this.p.subGrid&&(!(i=j(this).jqGrid("getInd",s,!0))||(e=(e=j("td.sgcollapsed",i)[0])||j("td.sgexpanded",i)[0])&&j(e).trigger("click"))})}})});!function(r){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base"],r):r(jQuery)}(function(H){"use strict";H.jgrid.extend({groupingSetup:function(){return this.each(function(){var
r,e,i,t=this,o=t.p.colModel,n=t.p.groupingView,s=H.jgrid.styleUI[t.p.styleUI||"jQueryUI"].grouping;if(null===n||"object"!=typeof
n&&!H.jgrid.isFunction(n))t.p.grouping=!1;else
if(n.plusicon||(n.plusicon=s.icon_plus),n.minusicon||(n.minusicon=s.icon_minus),n.groupField.length){for(void
0===n.visibiltyOnNextGrouping&&(n.visibiltyOnNextGrouping=[]),n.lastvalues=[],n._locgr||(n.groups=[]),n.counters=[],r=0;r<n.groupField.length;r++)n.groupOrder[r]||(n.groupOrder[r]="asc"),n.groupText[r]||(n.groupText[r]="{0}"),"boolean"!=typeof
n.groupColumnShow[r]&&(n.groupColumnShow[r]=!0),"boolean"!=typeof
n.groupSummary[r]&&(n.groupSummary[r]=!1),n.groupSummaryPos[r]||(n.groupSummaryPos[r]="footer"),!0===n.groupColumnShow[r]?(n.visibiltyOnNextGrouping[r]=!0,H(t).jqGrid("showCol",n.groupField[r])):(n.visibiltyOnNextGrouping[r]=H("#"+H.jgrid.jqID(t.p.id+"_"+n.groupField[r])).is(":visible"),H(t).jqGrid("hideCol",n.groupField[r]));for(n.summary=[],n.hideFirstGroupCol&&Array.isArray(n.formatDisplayField)&&!H.jgrid.isFunction(n.formatDisplayField[0])&&(n.formatDisplayField[0]=function(r){return r}),e=0,i=o.length;e<i;e++)n.hideFirstGroupCol&&(o[e].hidden||n.groupField[0]!==o[e].name||(o[e].formatter=function(){return""})),o[e].summaryType&&(o[e].summaryDivider?n.summary.push({nm:o[e].name,st:o[e].summaryType,v:"",sd:o[e].summaryDivider,vd:"",sr:o[e].summaryRound,srt:o[e].summaryRoundType||"round"}):n.summary.push({nm:o[e].name,st:o[e].summaryType,v:"",sr:o[e].summaryRound,srt:o[e].summaryRoundType||"round"}))}else
t.p.grouping=!1})},groupingPrepare:function(d,l){return this.each(function(){for(var
r,e,i,t=this.p.groupingView,o=this,n=function(){H.jgrid.isFunction(this.st)?this.v=this.st.call(o,this.v,this.nm,d):(this.v=H(o).jqGrid("groupingCalculations.handler",this.st,this.v,this.nm,this.sr,this.srt,d),"avg"===this.st.toLowerCase()&&this.sd&&(this.vd=H(o).jqGrid("groupingCalculations.handler",this.st,this.vd,this.sd,this.sr,this.srt,d)))},s=t.groupField.length,a=0,u=0;u<s;u++)r=t.groupField[u],i=t.displayField[u],e=d[r],null==(i=null==i?null:d[i])&&(i=e),void
0!==e&&(0===l?(t.groups.push({idx:u,dataIndex:r,value:e,displayValue:i,startRow:l,cnt:1,summary:[]}),t.lastvalues[u]=e,t.counters[u]={cnt:1,pos:t.groups.length-1,summary:H.extend(!0,[],t.summary)}):"object"==typeof
e||(Array.isArray(t.isInTheSameGroup)&&H.jgrid.isFunction(t.isInTheSameGroup[u])?t.isInTheSameGroup[u].call(o,t.lastvalues[u],e,u,t):t.lastvalues[u]===e)?1===a?(t.groups.push({idx:u,dataIndex:r,value:e,displayValue:i,startRow:l,cnt:1,summary:[]}),t.lastvalues[u]=e,t.counters[u]={cnt:1,pos:t.groups.length-1,summary:H.extend(!0,[],t.summary)}):(t.counters[u].cnt+=1,t.groups[t.counters[u].pos].cnt=t.counters[u].cnt):(t.groups.push({idx:u,dataIndex:r,value:e,displayValue:i,startRow:l,cnt:1,summary:[]}),t.lastvalues[u]=e,a=1,t.counters[u]={cnt:1,pos:t.groups.length-1,summary:H.extend(!0,[],t.summary)}),H.each(t.counters[u].summary,n),t.groups[t.counters[u].pos].summary=t.counters[u].summary)}),this},groupingToggle:function(y){return this.each(function(){var
r=this,e=r.p.groupingView,i=y.split("_"),t=parseInt(i[i.length-2],10);i.splice(i.length-2,2);function
o(r){return 0<(r=H.map(r.split(" "),function(r){if(r.substring(0,u.length+1)===u+"_")return parseInt(r.substring(u.length+1),10)})).length?r[0]:void
0}var
n,s,a,u=i.join("_"),d=e.minusicon,l=e.plusicon,p=H("#"+H.jgrid.jqID(y)),g=p.length?p[0].nextSibling:null,h=H("#"+H.jgrid.jqID(y)+" span.tree-wrap-"+r.p.direction),i=!1,c=!1,m=!!r.p.frozenColumns&&r.p.id+"_frozen",p=!!m&&H("#"+H.jgrid.jqID(y),"#"+H.jgrid.jqID(m)),f=p&&p.length?p[0].nextSibling:null;if(h.hasClass(d)){if(g)for(;g&&!(void
0!==(n=o(g.className))&&n<=t);)a=parseInt(H(g).attr("jqfootlevel"),10),(c=!isNaN(a)&&(e.showSummaryOnHide&&a<=t))||H(g).hide(),g=g.nextSibling,m&&(c||H(f).hide(),f=f.nextSibling);h.removeClass(d).addClass(l),i=!0}else{if(g)for(s=void
0;g;){if(n=o(g.className),void
0===s&&(s=void
0===n),c=H(g).hasClass("ui-subgrid")&&H(g).hasClass("ui-sg-collapsed"),void
0!==n){if(n<=t)break;n===t+1&&(c||(H(g).show().find(">td>span.tree-wrap-"+r.p.direction).removeClass(d).addClass(l),m&&H(f).show().find(">td>span.tree-wrap-"+r.p.direction).removeClass(d).addClass(l)))}else
s&&(c||(H(g).show(),m&&H(f).show()));g=g.nextSibling,m&&(f=f.nextSibling)}h.removeClass(l).addClass(d)}H(r).triggerHandler("jqGridGroupingClickGroup",[y,i]),H.jgrid.isFunction(r.p.onClickGroup)&&r.p.onClickGroup.call(r,y,i)}),!1},groupingRender:function(G,F,S,I){return this.each(function(){var
l,p,g,h,c=this,m=c.p.groupingView,f="",y=m.groupCollapse?m.plusicon:m.minusicon,v=[],j=m.groupField.length,x=H.jgrid.styleUI[c.p.styleUI||"jQueryUI"].common,y=y+" tree-wrap-"+c.p.direction;H.each(c.p.colModel,function(r,e){for(var
i=0;i<j;i++)if(m.groupField[i]===e.name){v[i]=r;break}});var
w=0;function
q(r,e,i,t,o){for(var
n,s,a,u=function(r,e,i){var
t,o=!1;if(0===e)o=i[r];else{var
n=i[r].idx;if(0===n)o=i[r];else
for(t=r;0<=t;t--)if(i[t].idx===n-e){o=i[t];break}}return o}(r,e,i),d=c.p.colModel,l=u.cnt,p="",g=!1,h=t;h<F;h++)!d[h].hidden&&!g&&o?(s=o,g=!0):s="<td "+c.formatCol(h,1,"")+">&#160;</td>",H.each(u.summary,function(){if(this.nm===d[h].name){a=d[h].summaryTpl||"{0}","string"==typeof
this.st&&"avg"===this.st.toLowerCase()&&(this.sd&&this.vd?this.v=this.v/this.vd:this.v&&0<l&&(this.v=this.v/l));try{this.groupCount=u.cnt,this.groupIndex=u.dataIndex,this.groupValue=u.value,n=c.formatter("",this.v,h,this)}catch(r){n=this.v}return s="<td "+c.formatCol(h,1,"")+">"+H.jgrid.template(a,n,u.cnt,u.dataIndex,u.displayValue)+"</td>",!1}}),p+=s;return p}var
C,b=H.makeArray(m.groupSummary);b.reverse(),C=c.p.multiselect?' colspan="2"':"",H.each(m.groups,function(r,e){if(m._locgr&&!(e.startRow+e.cnt>(S-1)*I&&e.startRow<S*I))return!0;w++,g=c.p.id+"ghead_"+e.idx,p=g+"_"+r,l="<span style='cursor:pointer;margin-right:8px;margin-left:5px;' class='"+x.icon_base+" "+y+"' onclick=\"jQuery('#"+H.jgrid.jqID(c.p.id)+"').jqGrid('groupingToggle','"+p+"');return false;\"></span>";try{h=Array.isArray(m.formatDisplayField)&&H.jgrid.isFunction(m.formatDisplayField[e.idx])?m.formatDisplayField[e.idx].call(c,e.displayValue,e.value,c.p.colModel[v[e.idx]],e.idx,m):c.formatter(p,e.displayValue,v[e.idx],e.value)}catch(r){h=e.displayValue}var
i="";if("string"!=typeof(i=H.jgrid.isFunction(m.groupText[e.idx])?m.groupText[e.idx].call(c,h,e.cnt,e.summary):H.jgrid.template(m.groupText[e.idx],h,e.cnt,e.summary))&&"number"!=typeof
i&&(i=h),"header"===m.groupSummaryPos[e.idx]?(f+='<tr id="'+p+'"'+(m.groupCollapse&&0<e.idx?' style="display:none;" ':" ")+'role="row" class= "'+x.content+" jqgroup ui-row-"+c.p.direction+" "+g+'">',f+=q(r,0,m.groups,C?1:0,'<td style="padding-left:'+12*e.idx+'px;"'+C+">"+l+i+"</td>"),f+="</tr>"):f+='<tr id="'+p+'"'+(m.groupCollapse&&0<e.idx?' style="display:none;" ':" ")+'role="row" class= "'+x.content+" jqgroup ui-row-"+c.p.direction+" "+g+'"><td style="padding-left:'+12*e.idx+'px;" colspan="'+(!1===m.groupColumnShow[e.idx]?F-1:F)+'">'+l+i+"</td></tr>",j-1===e.idx){var
t,o,n,s,a=m.groups[r+1],u=0,i=e.startRow,d=void
0!==a?a.startRow:m.groups[r].startRow+m.groups[r].cnt;for(m._locgr&&(u=(S-1)*I)>e.startRow&&(i=u),t=i;t<d&&G[t-u];t++)f+=G[t-u].join("");if("header"!==m.groupSummaryPos[e.idx]){if(void
0!==a){for(n=0;n<m.groupField.length&&a.dataIndex!==m.groupField[n];n++);w=m.groupField.length-n}for(o=0;o<w;o++)b[o]&&(s="",m.groupCollapse&&!m.showSummaryOnHide&&(s=' style="display:none;"'),f+="<tr"+s+' jqfootlevel="'+(e.idx-o)+'" role="row" class="'+x.content+" jqfoot ui-row-"+c.p.direction+'">',f+=q(r,o,m.groups,0,!1),f+="</tr>");w=n}}}),H("#"+H.jgrid.jqID(c.p.id)+" tbody").first().append(f),f=null})},groupingGroupBy:function(t,o){return this.each(function(){var
r=this;"string"==typeof
t&&(t=[t]);var
e,i=r.p.groupingView;for(r.p.grouping=!0,i._locgr=!1,void
0===i.visibiltyOnNextGrouping&&(i.visibiltyOnNextGrouping=[]),e=0;e<i.groupField.length;e++)!i.groupColumnShow[e]&&i.visibiltyOnNextGrouping[e]&&H(r).jqGrid("showCol",i.groupField[e]);for(e=0;e<t.length;e++)i.visibiltyOnNextGrouping[e]=H("#"+H.jgrid.jqID(r.p.id)+"_"+H.jgrid.jqID(t[e])).is(":visible");r.p.groupingView=H.extend(r.p.groupingView,o||{}),i.groupField=t,H(r).trigger("reloadGrid")})},groupingRemove:function(t){return this.each(function(){var
r=this;if(void
0===t&&(t=!0),!(r.p.grouping=!1)===t){for(var
e=r.p.groupingView,i=0;i<e.groupField.length;i++)!e.groupColumnShow[i]&&e.visibiltyOnNextGrouping[i]&&H(r).jqGrid("showCol",e.groupField);H("#"+H.jgrid.jqID(r.p.id)+" tbody").first().find("tr.jqgroup, tr.jqfoot").remove(),H("#"+H.jgrid.jqID(r.p.id)+" tbody").first().find("tr.jqgrow:hidden").show()}else
H(r).trigger("reloadGrid")})},groupingCalculations:{handler:function(r,e,i,t,o,n){var
s={sum:function(){return parseFloat(e||0)+parseFloat(n[i]||0)},min:function(){return""===e?parseFloat(n[i]||0):Math.min(parseFloat(e),parseFloat(n[i]||0))},max:function(){return""===e?parseFloat(n[i]||0):Math.max(parseFloat(e),parseFloat(n[i]||0))},count:function(){return""===e&&(e=0),n.hasOwnProperty(i)?e+1:0},avg:function(){return s.sum()}};if(!s[r])throw"jqGrid Grouping No such method: "+r;r=s[r]();return null!=t&&(r="fixed"===o?r.toFixed(t):(t=Math.pow(10,t),Math.round(r*t)/t)),r}},setGroupHeaders:function(q){return q=H.extend({useColSpanStyle:!1,groupHeaders:[]},q||{}),this.each(function(){var
r,e,i,t,o,n,s,a,u,d,l=this,p=0,g=l.p.colModel,h=g.length,c=l.grid.headers,m=H("table.ui-jqgrid-htable",l.grid.hDiv),f=m.children("thead").children("tr.ui-jqgrid-labels").last().addClass("jqg-second-row-header"),y=m.children("thead"),v=m.find(".jqg-first-row-header"),j=!1,x=H.jgrid.styleUI[l.p.styleUI||"jQueryUI"].base;l.p.groupHeader||(l.p.groupHeader=[]),l.p.groupHeader.push(q),l.p.groupHeaderOn=!0,void
0===v[0]?v=H("<tr>",{role:"row","aria-hidden":"true"}).addClass("jqg-first-row-header").css("height","auto"):v.empty();var
w;for((H(document.activeElement).is("input")||H(document.activeElement).is("textarea"))&&(j=document.activeElement),H(l).prepend(y),H(l).prepend(y),i=H("<tr>",{role:"row"}).addClass("ui-jqgrid-labels jqg-third-row-header"),r=0;r<h;r++)if(t=c[r].el,o=H(t),e=g[r],u={height:"0px",width:c[r].width+"px",display:e.hidden?"none":""},H("<th>",{role:"gridcell"}).css(u).addClass("ui-first-th-"+l.p.direction).appendTo(v),t.style.width="",0<=(n=function(r,e){for(var
i=e.length,t=0;t<i;t++)if(e[t].startColumnName===r)return t;return-1}(e.name,q.groupHeaders))){for(s=(u=q.groupHeaders[n]).numberOfColumns,w=u.titleText,u=u.className||"",n=a=0;n<s&&r+n<h;n++)g[r+n].hidden||a++;w=H("<th>").attr({role:"columnheader"}).addClass(x.headerBox+" ui-th-column-header ui-th-"+l.p.direction+" "+u).html(w),0<a&&w.attr("colspan",String(a)),l.p.headertitles&&w.attr("title",w.text()),0===a&&w.hide(),o.before(w),i.append(t),p=s-1}else
0===p?q.useColSpanStyle?(w=o.attr("rowspan")?parseInt(o.attr("rowspan"),10)+1:2,o.attr("rowspan",w)):(H("<th>",{role:"columnheader"}).addClass(x.headerBox+" ui-th-column-header ui-th-"+l.p.direction).css({display:e.hidden?"none":""}).insertBefore(o),i.append(t)):(i.append(t),p--);if((y=H(l).children("thead")).prepend(v),i.insertAfter(f),m.append(y),q.useColSpanStyle&&(m.find("span.ui-jqgrid-resize").each(function(){var
r=H(this).parent();r.is(":visible")&&(this.style.cssText="height: "+r.height()+"px !important; cursor: col-resize;")}),m.find("div.ui-jqgrid-sortable").each(function(){var
r=H(this),e=r.parent();e.is(":visible")&&e.is(":has(span.ui-jqgrid-resize)")&&r.css("top",(e.height()-r.outerHeight())/2-4+"px")})),d=y.find("tr.jqg-first-row-header"),H(l).on("jqGridResizeStop.setGroupHeaders",function(r,e,i){d.find("th").eq(i)[0].style.width=e+"px"}),j)try{H(j).focus()}catch(r){}""===H.jgrid.trim(H("tr.jqg-second-row-header th").eq(0).text())&&H("tr.jqg-second-row-header th").eq(0).prepend("&nbsp;")})},destroyGroupHeader:function(p){return void
0===p&&(p=!0),this.each(function(){var
r,e,i,t,o,n,s=this,a=s.grid,u=H("table.ui-jqgrid-htable thead",a.hDiv),d=s.p.colModel,l=!1;if(a){for(s.p.frozenColumns&&(H(s).jqGrid("destroyFrozenColumns"),l=!0),H(this).off(".setGroupHeaders"),s.p.groupHeaderOn=!1,r=H("<tr>",{role:"row"}).addClass("ui-jqgrid-labels"),e=0,i=(t=a.headers).length;e<i;e++){n=d[e].hidden?"none":"",o=H(t[e].el).width(H("tr.jqg-first-row-header th",u).eq(e).width()).css("display",n);try{o.removeAttr("rowSpan")}catch(r){o.attr("rowSpan",1)}r.append(o),0<(n=o.children("span.ui-jqgrid-resize")).length&&(n[0].style.height=""),o.children("div")[0].style.top=""}H(u).children("tr.ui-jqgrid-labels").remove(),H(u).children("tr.jqg-first-row-header").remove(),H(u).prepend(r),!0===p&&H(s).jqGrid("setGridParam",{groupHeader:null}),l&&H(s).jqGrid("setFrozenColumns")}})},isGroupHeaderOn:function(){var
r=this[0];return!0===r.p.groupHeaderOn&&r.p.groupHeader&&(Array.isArray(r.p.groupHeader)||H.jgrid.isFunction(r.p.groupHeader))},refreshGroupHeaders:function(){return this.each(function(){var
r,e=this,i=H(e).jqGrid("isGroupHeaderOn");if(i&&(H(e).jqGrid("destroyGroupHeader",!1),r=H.extend([],e.p.groupHeader),e.p.groupHeader=null),i&&r)for(var
t=0;t<r.length;t++)H(e).jqGrid("setGroupHeaders",r[t])})}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base"],e):e(jQuery)}(function(N){"use strict";N.jgrid.extend({setTreeNode:function(g,u){return this.each(function(){var
t=this;if(t.grid&&t.p.treeGrid){var
e,r,i,d=t.p.expColInd,a=t.p.treeReader.expanded_field,s=t.p.treeReader.leaf_field,l=t.p.treeReader.level_field,n=t.p.treeReader.icon_field,p=t.p.treeReader.loaded,o=N.jgrid.styleUI[t.p.styleUI||"jQueryUI"].common,h=g;for(N(t).triggerHandler("jqGridBeforeSetTreeNode",[h,u]),N.jgrid.isFunction(t.p.beforeSetTreeNode)&&t.p.beforeSetTreeNode.call(t,h,u);g<u;){var
f=N.jgrid.stripPref(t.p.idPrefix,t.rows[g].id),c=t.p._index[f],f=t.p.data[c];"nested"===t.p.treeGridModel&&(f[s]||(r=parseInt(f[t.p.treeReader.left_field],10),i=parseInt(f[t.p.treeReader.right_field],10),f[s]=i===r+1?"true":"false",t.rows[g].cells[t.p._treeleafpos].innerHTML=f[s])),c=parseInt(f[l],10),i=0===t.p.tree_root_level?(e=c+1,c):(e=c)-1,r="<div class='tree-wrap tree-wrap-"+t.p.direction+"' style='width:"+18*e+"px;'>",r+="<div style='"+("rtl"===t.p.direction?"right:":"left:")+18*i+"px;' class='"+o.icon_base+" ",void
0!==f[p]&&("true"===f[p]||!0===f[p]?f[p]=!0:f[p]=!1),i="true"===f[s]||!0===f[s]?(r+=(void
0!==f[n]&&""!==f[n]?f[n]:t.p.treeIcons.leaf)+" tree-leaf treeclick",f[s]=!0,"leaf"):(f[s]=!1,""),f[a]=("true"===f[a]||!0===f[a])&&(f[p]||void
0===f[p]),!1===f[a]?r+=!0===f[s]?"'":t.p.treeIcons.plus+" tree-plus treeclick'":r+=!0===f[s]?"'":t.p.treeIcons.minus+" tree-minus treeclick'",r+="></div></div>",N(t.rows[g].cells[d]).wrapInner("<span class='cell-wrapper"+i+"'></span>").prepend(r),c!==parseInt(t.p.tree_root_level,10)&&(N(t).jqGrid("isVisibleNode",f)||N(t.rows[g]).css("display","none")),N(t.rows[g].cells[d]).find("div.treeclick").on("click",function(e){e=e.target||e.srcElement,e=N.jgrid.stripPref(t.p.idPrefix,N(e,t.rows).closest("tr.jqgrow")[0].id),e=t.p._index[e];return t.p.data[e][s]||(t.p.data[e][a]?(N(t).jqGrid("collapseRow",t.p.data[e]),N(t).jqGrid("collapseNode",t.p.data[e])):(N(t).jqGrid("expandRow",t.p.data[e]),N(t).jqGrid("expandNode",t.p.data[e]))),!1}),!0===t.p.ExpandColClick&&N(t.rows[g].cells[d]).find("span.cell-wrapper").css("cursor","pointer").on("click",function(e){var
r=e.target||e.srcElement,e=N.jgrid.stripPref(t.p.idPrefix,N(r,t.rows).closest("tr.jqgrow")[0].id),r=t.p._index[e];return t.p.data[r][s]||(t.p.data[r][a]?(N(t).jqGrid("collapseRow",t.p.data[r]),N(t).jqGrid("collapseNode",t.p.data[r])):(N(t).jqGrid("expandRow",t.p.data[r]),N(t).jqGrid("expandNode",t.p.data[r]))),N(t).jqGrid("setSelection",e),!1}),g++}N(t).triggerHandler("jqGridAfterSetTreeNode",[h,u]),N.jgrid.isFunction(t.p.afterSetTreeNode)&&t.p.afterSetTreeNode.call(t,h,u)}})},setTreeGrid:function(){return this.each(function(){var
e,r,t,i,d=this,a=0,s=!1,l=[],n=N.jgrid.styleUI[d.p.styleUI||"jQueryUI"].treegrid;if(d.p.treeGrid){for(t
in
d.p.treedatatype||N.extend(d.p,{treedatatype:d.p.datatype}),d.p.loadonce&&(d.p.treedatatype="local"),d.p.subGrid=!1,d.p.altRows=!1,d.p.treeGrid_bigData||(d.p.pgbuttons=!1,d.p.pginput=!1,d.p.rowList=[]),d.p.gridview=!0,null!==d.p.rowTotal||d.p.treeGrid_bigData||(d.p.rowNum=1e4),d.p.multiselect=!1,d.p.expColInd=0,e=n.icon_plus,"jQueryUI"===d.p.styleUI&&(e+="rtl"===d.p.direction?"w":"e"),d.p.treeIcons=N.extend({plus:e,minus:n.icon_minus,leaf:n.icon_leaf},d.p.treeIcons||{}),"nested"===d.p.treeGridModel?d.p.treeReader=N.extend({level_field:"level",left_field:"lft",right_field:"rgt",leaf_field:"isLeaf",expanded_field:"expanded",loaded:"loaded",icon_field:"icon"},d.p.treeReader):"adjacency"===d.p.treeGridModel&&(d.p.treeReader=N.extend({level_field:"level",parent_id_field:"parent",leaf_field:"isLeaf",expanded_field:"expanded",loaded:"loaded",icon_field:"icon"},d.p.treeReader)),d.p.colModel)if(d.p.colModel.hasOwnProperty(t))for(i
in(r=d.p.colModel[t].name)!==d.p.ExpandColumn||s||(s=!0,d.p.expColInd=a),a++,r!==d.p.treeReader.level_field&&r!==d.p.treeReader.left_field&&r!==d.p.treeReader.right_field||(d.p.colModel[t].sorttype="integer"),d.p.treeReader)d.p.treeReader.hasOwnProperty(i)&&d.p.treeReader[i]===r&&l.push(r);N.each(d.p.treeReader,function(e,r){r&&-1===N.inArray(r,l)&&("leaf_field"===e&&(d.p._treeleafpos=a),a++,d.p.colNames.push(r),d.p.colModel.push({name:r,width:1,hidden:!0,sortable:!1,resizable:!1,hidedlg:!0,editable:!0,search:!1}))})}})},expandRow:function(s){this.each(function(){var
e,r,t,i,d,a=this;a.p.treeGrid_bigData||(e=a.p.lastpage),a.grid&&a.p.treeGrid&&(r=N(a).jqGrid("getNodeChildren",s),t=a.p.treeReader.expanded_field,i=s[a.p.localReader.id],void
0===(d=N(a).triggerHandler("jqGridBeforeExpandTreeGridRow",[i,s,r]))&&(d=!0),d&&N.jgrid.isFunction(a.p.beforeExpandTreeGridRow)&&(d=a.p.beforeExpandTreeGridRow.call(a,i,s,r)),!1!==d&&(N(r).each(function(){var
e=a.p.idPrefix+N.jgrid.getAccessor(this,a.p.localReader.id);N(N(a).jqGrid("getGridRowById",e)).css("display",""),this[t]&&N(a).jqGrid("expandRow",this)}),N(a).triggerHandler("jqGridAfterExpandTreeGridRow",[i,s,r]),N.jgrid.isFunction(a.p.afterExpandTreeGridRow)&&a.p.afterExpandTreeGridRow.call(a,i,s,r),a.p.treeGrid_bigData||(a.p.lastpage=e)))})},collapseRow:function(a){this.each(function(){var
e,r,t,i,d=this;d.grid&&d.p.treeGrid&&(e=N(d).jqGrid("getNodeChildren",a),r=d.p.treeReader.expanded_field,t=a[d.p.localReader.id],void
0===(i=N(d).triggerHandler("jqGridBeforeCollapseTreeGridRow",[t,a,e]))&&(i=!0),i&&N.jgrid.isFunction(d.p.beforeCollapseTreeGridRow)&&(i=d.p.beforeCollapseTreeGridRow.call(d,t,a,e)),!1!==i&&(N(e).each(function(){var
e=d.p.idPrefix+N.jgrid.getAccessor(this,d.p.localReader.id);N(N(d).jqGrid("getGridRowById",e)).css("display","none"),this[r]&&N(d).jqGrid("collapseRow",this)}),N(d).triggerHandler("jqGridAfterCollapseTreeGridRow",[t,a,e]),N.jgrid.isFunction(d.p.afterCollapseTreeGridRow)&&d.p.afterCollapseTreeGridRow.call(d,t,a,e)))})},getRootNodes:function(){var
d=[];return this.each(function(){var
e,r,t=this,i=t.p.data;if(t.grid&&t.p.treeGrid)switch(t.p.treeGridModel){case"nested":e=t.p.treeReader.level_field,N(i).each(function(){parseInt(this[e],10)===parseInt(t.p.tree_root_level,10)&&d.push(this)});break;case"adjacency":r=t.p.treeReader.parent_id_field,N(i).each(function(){null!==this[r]&&"null"!==String(this[r]).toLowerCase()||d.push(this)})}}),d},getNodeDepth:function(r){var
t=null;return this.each(function(){if(this.grid&&this.p.treeGrid)switch(this.p.treeGridModel){case"nested":var
e=this.p.treeReader.level_field;t=parseInt(r[e],10)-parseInt(this.p.tree_root_level,10);break;case"adjacency":t=N(this).jqGrid("getNodeAncestors",r).length}}),t},getNodeParent:function(h){var
f=null;return this.each(function(){var
e=this;if(e.grid&&e.p.treeGrid)switch(e.p.treeGridModel){case"nested":var
r=e.p.treeReader.left_field,t=e.p.treeReader.right_field,i=e.p.treeReader.level_field,d=parseInt(h[r],10),a=parseInt(h[t],10),s=parseInt(h[i],10);N(this.p.data).each(function(){if(parseInt(this[i],10)===s-1&&parseInt(this[r],10)<d&&parseInt(this[t],10)>a)return f=this,!1});break;case"adjacency":for(var
l=e.p.treeReader.parent_id_field,n=e.p.localReader.id,p=h[n],o=e.p._index[p];o--;)if(String(e.p.data[o][n])===String(N.jgrid.stripPref(e.p.idPrefix,h[l]))){f=e.p.data[o];break}}}),f},getNodeChildren:function(f){var
c=[];return this.each(function(){var
e=this;if(e.grid&&e.p.treeGrid){var
r,t=this.p.data.length;switch(e.p.treeGridModel){case"nested":for(var
i=e.p.treeReader.left_field,d=e.p.treeReader.right_field,a=e.p.treeReader.level_field,s=parseInt(f[i],10),l=parseInt(f[d],10),n=parseInt(f[a],10),p=0;p<t;p++)(r=e.p.data[p])&&parseInt(r[a],10)===n+1&&parseInt(r[i],10)>s&&parseInt(r[d],10)<l&&c.push(r);break;case"adjacency":var
o=e.p.treeReader.parent_id_field,h=e.p.localReader.id;for(p=0;p<t;p++)(r=e.p.data[p])&&String(r[o])===String(N.jgrid.stripPref(e.p.idPrefix,f[h]))&&c.push(r)}}}),c},getFullTreeNode:function(h,f){var
c=[];return this.each(function(){var
r,t,i,d=this,a=d.p.treeReader.expanded_field;if(d.grid&&d.p.treeGrid)switch(null!=f&&"boolean"==typeof
f||(f=!1),d.p.treeGridModel){case"nested":var
e=d.p.treeReader.left_field,s=d.p.treeReader.right_field,l=d.p.treeReader.level_field,n=parseInt(h[e],10),p=parseInt(h[s],10),o=parseInt(h[l],10);N(this.p.data).each(function(){parseInt(this[l],10)>=o&&parseInt(this[e],10)>=n&&parseInt(this[e],10)<=p&&(f&&(this[a]=!0),c.push(this))});break;case"adjacency":h&&(c.push(h),t=d.p.treeReader.parent_id_field,i=d.p.localReader.id,N(this.p.data).each(function(e){for(r=c.length,e=0;e<r;e++)if(String(N.jgrid.stripPref(d.p.idPrefix,c[e][i]))===String(this[t])){f&&(this[a]=!0),c.push(this);break}}))}}),c},getNodeAncestors:function(r,t,i){var
d=[];return void
0===t&&(t=!1),this.each(function(){if(this.grid&&this.p.treeGrid){i=void
0!==i&&this.p.treeReader.expanded_field;for(var
e=N(this).jqGrid("getNodeParent",r);e;){if(i)try{e[i]=!0}catch(e){}t?d.unshift(e):d.push(e),e=N(this).jqGrid("getNodeParent",e)}}}),d},isVisibleNode:function(t){var
i=!0;return this.each(function(){var
e,r;this.grid&&this.p.treeGrid&&(e=N(this).jqGrid("getNodeAncestors",t),r=this.p.treeReader.expanded_field,N(e).each(function(){if(!(i=i&&this[r]))return!1}))}),i},isNodeLoaded:function(i){var
d;return this.each(function(){var
e,r,t=this;t.grid&&t.p.treeGrid&&(e=t.p.treeReader.leaf_field,r=t.p.treeReader.loaded,d=void
0!==i&&(void
0!==i[r]?i[r]:!!(i[e]||0<N(t).jqGrid("getNodeChildren",i).length)))}),d},setLeaf:function(a,s,l){return this.each(function(){var
e,r=N.jgrid.getAccessor(a,this.p.localReader.id),t=N("#"+r,this.grid.bDiv)[0],i=this.p.treeReader.leaf_field;try{var
d=this.p._index[r];null!=d&&(this.p.data[d][i]=s)}catch(e){}!0===s?N("div.treeclick",t).removeClass(this.p.treeIcons.minus+" tree-minus "+this.p.treeIcons.plus+" tree-plus").addClass(this.p.treeIcons.leaf+" tree-leaf"):!1===s&&(e=this.p.treeIcons.minus+" tree-minus",l&&(e=this.p.treeIcons.plus+" tree-plus"),N("div.treeclick",t).removeClass(this.p.treeIcons.leaf+" tree-leaf").addClass(e))})},reloadNode:function(o,h){return this.each(function(){var
e,r,t,i,d,a,s,l,n,p;this.grid&&this.p.treeGrid&&(p=this.p.localReader.id,e=this.p.selrow,N(this).jqGrid("delChildren",o[p]),void
0===h&&(h=!1),h||jQuery._data(this,"events").jqGridAfterSetTreeNode||N(this).on("jqGridAfterSetTreeNode.reloadNode",function(){var
e,r,t=this.p.treeReader.leaf_field;this.p.reloadnode&&(e=this.p.reloadnode,r=N(this).jqGrid("getNodeChildren",e),e[t]&&r.length?N(this).jqGrid("setLeaf",e,!1):e[t]||0!==r.length||N(this).jqGrid("setLeaf",e,!0)),this.p.reloadnode=!1}),r=this.p.treeReader.expanded_field,t=this.p.treeReader.parent_id_field,i=this.p.treeReader.loaded,d=this.p.treeReader.level_field,a=this.p.treeReader.leaf_field,s=this.p.treeReader.left_field,l=this.p.treeReader.right_field,n=N.jgrid.getAccessor(o,this.p.localReader.id),p=N("#"+n,this.grid.bDiv)[0],o[r]=!0,o[a]||N("div.treeclick",p).removeClass(this.p.treeIcons.plus+" tree-plus").addClass(this.p.treeIcons.minus+" tree-minus"),this.p.treeANode=p.rowIndex,this.p.datatype=this.p.treedatatype,this.p.reloadnode=o,h&&(this.p.treeANode=0<p.rowIndex?p.rowIndex-1:1,N(this).jqGrid("delRowData",n)),"nested"===this.p.treeGridModel?N(this).jqGrid("setGridParam",{postData:{nodeid:n,n_left:o[s],n_right:o[l],n_level:o[d]}}):N(this).jqGrid("setGridParam",{postData:{nodeid:n,parentid:o[t],n_level:o[d]}}),N(this).trigger("reloadGrid"),o[i]=!0,"nested"===this.p.treeGridModel?N(this).jqGrid("setGridParam",{selrow:e,postData:{nodeid:"",n_left:"",n_right:"",n_level:""}}):N(this).jqGrid("setGridParam",{selrow:e,postData:{nodeid:"",parentid:"",n_level:""}}))})},expandNode:function(o){return this.each(function(){var
e,r,t,i,d,a,s,l,n,p;this.grid&&this.p.treeGrid&&(e=this.p.treeReader.expanded_field,r=this.p.treeReader.parent_id_field,t=this.p.treeReader.loaded,i=this.p.treeReader.level_field,d=this.p.treeReader.left_field,a=this.p.treeReader.right_field,o[e]||(s=N.jgrid.getAccessor(o,this.p.localReader.id),l=N("#"+this.p.idPrefix+N.jgrid.jqID(s),this.grid.bDiv)[0],n=this.p._index[s],void
0===(p=N(this).triggerHandler("jqGridBeforeExpandTreeGridNode",[s,o]))&&(p=!0),p&&N.jgrid.isFunction(this.p.beforeExpandTreeGridNode)&&(p=this.p.beforeExpandTreeGridNode.call(this,s,o)),!1!==p&&(N(this).jqGrid("isNodeLoaded",this.p.data[n])?(o[e]=!0,N("div.treeclick",l).removeClass(this.p.treeIcons.plus+" tree-plus").addClass(this.p.treeIcons.minus+" tree-minus")):this.grid.hDiv.loading||(o[e]=!0,N("div.treeclick",l).removeClass(this.p.treeIcons.plus+" tree-plus").addClass(this.p.treeIcons.minus+" tree-minus"),this.p.treeANode=l.rowIndex,this.p.datatype=this.p.treedatatype,"nested"===this.p.treeGridModel?N(this).jqGrid("setGridParam",{postData:{nodeid:s,n_left:o[d],n_right:o[a],n_level:o[i]}}):N(this).jqGrid("setGridParam",{postData:{nodeid:s,parentid:o[r],n_level:o[i]}}),N(this).trigger("reloadGrid"),o[t]=!0,"nested"===this.p.treeGridModel?N(this).jqGrid("setGridParam",{postData:{nodeid:"",n_left:"",n_right:"",n_level:""}}):N(this).jqGrid("setGridParam",{postData:{nodeid:"",parentid:"",n_level:""}})),N(this).triggerHandler("jqGridAfterExpandTreeGridNode",[s,o]),N.jgrid.isFunction(this.p.afterExpandTreeGridNode)&&this.p.afterExpandTreeGridNode.call(this,s,o))))})},collapseNode:function(d){return this.each(function(){var
e,r,t,i;this.grid&&this.p.treeGrid&&(e=this.p.treeReader.expanded_field,d[e]&&(r=N.jgrid.getAccessor(d,this.p.localReader.id),t=N("#"+this.p.idPrefix+N.jgrid.jqID(r),this.grid.bDiv)[0],void
0===(i=N(this).triggerHandler("jqGridBeforeCollapseTreeGridNode",[r,d]))&&(i=!0),i&&N.jgrid.isFunction(this.p.beforeCollapseTreeGridNode)&&(i=this.p.beforeCollapseTreeGridNode.call(this,r,d)),(d[e]=!1)!==i&&(N("div.treeclick",t).removeClass(this.p.treeIcons.minus+" tree-minus").addClass(this.p.treeIcons.plus+" tree-plus"),N(this).triggerHandler("jqGridAfterCollapseTreeGridNode",[r,d]),N.jgrid.isFunction(this.p.afterCollapseTreeGridNode)&&this.p.afterCollapseTreeGridNode.call(this,r,d))))})},SortTree:function(o,h,f,c){return this.each(function(){if(this.grid&&this.p.treeGrid){var
e,r,t,i,d=[],a=this,s=N(this).jqGrid("getRootNodes",a.p.search),l=N.jgrid.from.call(this,s);for(Boolean(a.p.sortTreeByNodeType)&&(s=a.p.sortTreeNodeOrder&&"desc"===a.p.sortTreeNodeOrder.toLowerCase()?"d":"a",l.orderBy(a.p.treeReader.leaf_field,s,f,c)),l.orderBy(o,h,f,c),e=0,r=(i=l.select()).length;e<r;e++)t=i[e],d.push(t),N(this).jqGrid("collectChildrenSortTree",d,t,o,h,f,c);var
n=N(this).jqGrid("getDataIDs"),p=1;N.each(d,function(e){var
r=N.jgrid.getAccessor(this,a.p.localReader.id);-1!==N.inArray(r,n)&&(N("#"+N.jgrid.jqID(a.p.id)+" tbody tr").eq(p).after(N("#"+N.jgrid.jqID(a.p.id)+" tbody tr#"+N.jgrid.jqID(r))),p++)}),d=i=l=null}})},searchTree:function(t){var
i,d,a,s,l,n,p,o=t.length||0,h=[],f=[],c=[];return this.each(function(){if(this.grid&&this.p.treeGrid&&o)for(i=this.p.localReader.id,p=0;p<o;p++){var
e;if(h=N(this).jqGrid("getNodeAncestors",t[p],!0,!0),Boolean(this.p.FullTreeSearchResult)?(e=N(this).jqGrid("getFullTreeNode",t[p],!0),h=h.concat(e)):h.push(t[p]),d=h[0][i],-1===N.inArray(d,f))f.push(d),c=c.concat(h);else
for(l=0,a=h.length;l<a;l++){var
r=!1;for(n=0,s=c.length;n<s;n++)if(h[l][i]===c[n][i]){r=!0;break}r||c.push(h[l])}}}),c},collectChildrenSortTree:function(a,s,l,n,p,o){return this.each(function(){if(this.grid&&this.p.treeGrid){var
e,r,t,i,d=N(this).jqGrid("getNodeChildren",s,this.p.search),d=N.jgrid.from.call(this,d);for(d.orderBy(l,n,p,o),e=0,r=(i=d.select()).length;e<r;e++)t=i[e],a.push(t),N(this).jqGrid("collectChildrenSortTree",a,t,l,n,p,o)}})},setTreeRow:function(e,r){var
t=!1;return this.each(function(){this.grid&&this.p.treeGrid&&(t=N(this).jqGrid("setRowData",e,r))}),t},delTreeNode:function(h){return this.each(function(){var
e,r,t,i,d,a=this,s=a.p.localReader.id,l=a.p.treeReader.left_field,n=a.p.treeReader.right_field;if(a.grid&&a.p.treeGrid){h=N.jgrid.stripPref(a.p.idPrefix,h);var
p=a.p._index[h];if(void
0!==p){t=(r=parseInt(a.p.data[p][n],10))-parseInt(a.p.data[p][l],10)+1;var
o=N(a).jqGrid("getFullTreeNode",a.p.data[p]);if(0<o.length)for(e=0;e<o.length;e++)N(a).jqGrid("delRowData",a.p.idPrefix+o[e][s]);if("nested"===a.p.treeGridModel){if((i=N.jgrid.from.call(a,a.p.data).greater(l,r,{stype:"integer"}).select()).length)for(d
in
i)i.hasOwnProperty(d)&&(i[d][l]=parseInt(i[d][l],10)-t);if((i=N.jgrid.from.call(a,a.p.data).greater(n,r,{stype:"integer"}).select()).length)for(d
in
i)i.hasOwnProperty(d)&&(i[d][n]=parseInt(i[d][n],10)-t)}}}})},delChildren:function(h){return this.each(function(){var
e,r,t,i,d=this,a=d.p.localReader.id,s=d.p.treeReader.left_field,l=d.p.treeReader.right_field;if(d.grid&&d.p.treeGrid){h=N.jgrid.stripPref(d.p.idPrefix,h);var
n=d.p._index[h];if(void
0!==n){r=(e=parseInt(d.p.data[n][l],10))-parseInt(d.p.data[n][s],10)+1;var
p=N(d).jqGrid("getFullTreeNode",d.p.data[n]);if(0<p.length)for(var
o=0;o<p.length;o++)p[o][a]!==h&&N(d).jqGrid("delRowData",d.p.idPrefix+p[o][a]);if("nested"===d.p.treeGridModel){if((t=N.jgrid.from(d.p.data).greater(s,e,{stype:"integer"}).select()).length)for(i
in
t)t.hasOwnProperty(i)&&(t[i][s]=parseInt(t[i][s],10)-r);if((t=N.jgrid.from(d.p.data).greater(l,e,{stype:"integer"}).select()).length)for(i
in
t)t.hasOwnProperty(i)&&(t[i][l]=parseInt(t[i][l],10)-r)}}}})},addChildNode:function(e,r,t,i){var
d=this[0];if(t){var
a,s,l,n=d.p.treeReader.expanded_field,p=d.p.treeReader.leaf_field,o=d.p.treeReader.level_field,h=d.p.treeReader.parent_id_field,f=d.p.treeReader.left_field,c=d.p.treeReader.right_field,g=d.p.treeReader.loaded,u=0,G=r;if(void
0===i&&(i=!1),null==e){if(0<=(q=d.p.data.length-1))for(;0<=q;)u=Math.max(u,parseInt(d.p.data[q][d.p.localReader.id],10)),q--;e=u+1}var
j,v,_=N(d).jqGrid("getInd",r),R=!1;if(null==r||""===r)G=r=null,a="last",s=d.p.tree_root_level,q=d.p.data.length+1;else{a="after";var
I,w=N.jgrid.stripPref(d.p.idPrefix,r),x=d.p._index[w];r=(I=d.p.data[x])[d.p.localReader.id],s=parseInt(I[o],10)+1;var
w=N(d).jqGrid("getFullTreeNode",I),q=w.length?(G=q=w[w.length-1][d.p.localReader.id],N(d).jqGrid("getInd",d.p.idPrefix+G)):N(d).jqGrid("getInd",d.p.idPrefix+r);if(I[p]&&(R=!0,I[n]=!0,N(d.rows[_]).find("span.cell-wrapperleaf").removeClass("cell-wrapperleaf").addClass("cell-wrapper").end().find("div.tree-leaf").removeClass(d.p.treeIcons.leaf+" tree-leaf").addClass(d.p.treeIcons.minus+" tree-minus"),d.p.data[x][p]=!1,I[g]=!0),!1===q)throw"Parent item with id: "+G+" ("+r+") can't be found";q++}if(x=q+1,void
0===t[n]&&(t[n]=!1),void
0===t[g]&&(t[g]=!1),t[o]=s,void
0===t[p]&&(t[p]=!0),"adjacency"===d.p.treeGridModel&&(t[h]=r),"nested"===d.p.treeGridModel)if(null!==r){if(l=parseInt(I[c],10),(j=N.jgrid.from.call(d,d.p.data).greaterOrEquals(c,l,{stype:"integer"}).select()).length)for(v
in
j)j.hasOwnProperty(v)&&(j[v][f]=j[v][f]>l?parseInt(j[v][f],10)+2:j[v][f],j[v][c]=j[v][c]>=l?parseInt(j[v][c],10)+2:j[v][c]);t[f]=l,t[c]=l+1}else{if(l=parseInt(N(d).jqGrid("getCol",c,!1,"max"),10),(j=N.jgrid.from.call(d,d.p.data).greater(f,l,{stype:"integer"}).select()).length)for(v
in
j)j.hasOwnProperty(v)&&(j[v][f]=parseInt(j[v][f],10)+2);if((j=N.jgrid.from.call(d,d.p.data).greater(c,l,{stype:"integer"}).select()).length)for(v
in
j)j.hasOwnProperty(v)&&(j[v][c]=parseInt(j[v][c],10)+2);t[f]=l+1,t[c]=l+2}(null===r||N(d).jqGrid("isNodeLoaded",I)||R)&&(N(d).jqGrid("addRowData",e,t,a,d.p.idPrefix+G),N(d).jqGrid("setTreeNode",q,x)),I&&!I[n]&&i&&N(d.rows[_]).find("div.treeclick").click()}}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base","./grid.grouping"],e):e(jQuery)}(function(Q){"use strict";Q.assocArraySize=function(e){var
r,o=0;for(r
in
e)e.hasOwnProperty(r)&&o++;return o},Q.jgrid.extend({pivotSetup:function(H,e){var
N=[],A=[],k=[],G=[],M=[],z={grouping:!0,groupingView:{groupField:[],groupSummary:[],groupSummaryPos:[]}},V=[],q=Q.extend({rowTotals:!1,rowTotalsText:"Total",colTotals:!1,groupSummary:!0,groupSummaryPos:"header",frozenStaticCols:!1},e||{});return this.each(function(){var
e,p,d,f,r,o,h=this,t=H.length,a=0;function
n(e,r,o){o=function(e,r){var
o,t,a,n=[];if(!this||"function"!=typeof
e||e
instanceof
RegExp)throw new
TypeError;for(a=this.length,o=0;o<a;o++)if(this.hasOwnProperty(o)&&(t=this[o],e.call(r,t,o,this))){n.push(t);break}return n}.call(e,r,o);return 0<o.length?o[0]:null}function
i(e,r){var
o,t=0,a=!0;for(o
in
e)if(e.hasOwnProperty(o)){if(e[o]!=this[t]){a=!1;break}if(++t>=this.length)break}return a&&(w=r),a}function
s(e,r,o,t){var
a,n,i,s,l,g=r.length,u="",m=[],p=1;for(Array.isArray(o)?(i=o.length,m=o):(i=1,m[0]=o),M=[],n=(G=[]).root=0;n<i;n++){for(var
d,f=[],c=0;c<g;c++){if(s="string"==typeof
r[c].aggregator?r[c].aggregator:"cust",null==o)d=a=Q.jgrid.trim(r[c].member)+"_"+s,m[0]=r[c].label||s+" "+Q.jgrid.trim(r[c].member);else{d=o[n].replace(/\s+/g,"");try{a=1===g?u+d:u+d+"_"+s+"_"+String(c)}catch(e){}m[n]=o[n]}a=isNaN(parseInt(a,10))?a:a+" ","avg"===r[c].aggregator&&(l=-1===w?A.length+"_"+a:w+"_"+a,v[l]?v[l]++:v[l]=1,p=v[l]),t[a]=f[a]=function(e,r,o,t,a){var
n;if(Q.jgrid.isFunction(e))n=e.call(h,r,o,t);else
switch(e){case"sum":n=parseFloat(r||0)+parseFloat(t[o]||0);break;case"count":""!==r&&null!=r||(r=0),n=t.hasOwnProperty(o)?r+1:0;break;case"min":n=""===r||null==r?parseFloat(t[o]||0):Math.min(parseFloat(r),parseFloat(t[o]||0));break;case"max":n=""===r||null==r?parseFloat(t[o]||0):Math.max(parseFloat(r),parseFloat(t[o]||0));break;case"avg":n=(parseFloat(r||0)*(a-1)+parseFloat(t[o]||0))/a}return n}(r[c].aggregator,t[a],r[c].member,e,p)}u+=o&&null!=o[n]?o[n].replace(/\s+/g,""):"",G[a]=f,M[a]=m[n]}return t}if(q.rowTotals&&0<q.yDimension.length&&(r=q.yDimension[0].dataName,q.yDimension.splice(0,0,{dataName:r}),q.yDimension[0].converter=function(){return"_r_Totals"}),p=Array.isArray(q.xDimension)?q.xDimension.length:0,d=q.yDimension.length,f=Array.isArray(q.aggregates)?q.aggregates.length:0,0===p||0===f)throw"xDimension or aggregates optiona are not set!";for(y=0;y<p;y++)o={name:q.xDimension[y].dataName,frozen:q.frozenStaticCols},null==q.xDimension[y].isGroupField&&(q.xDimension[y].isGroupField=!0),o=Q.extend(!0,o,q.xDimension[y]),N.push(o);for(var
l=p-1,g={},v=[];a<t;){e=H[a];for(var
u=[],m=[],c={},y=0;u[y]=Q.jgrid.trim(e[q.xDimension[y].dataName]),c[q.xDimension[y].dataName]=u[y],y++,y<p;);var
x,b=0,w=-1;if(x=n(A,i,u)){if(0<=w){if(b=0,1<=d){for(b=0;b<d;b++)m[b]=Q.jgrid.trim(e[q.yDimension[b].dataName]),q.yDimension[b].converter&&Q.jgrid.isFunction(q.yDimension[b].converter)&&(m[b]=q.yDimension[b].converter.call(this,m[b],u,m));x=s(e,q.aggregates,m,x)}else
0===d&&(x=s(e,q.aggregates,null,x));A[w]=x}}else{if(b=0,1<=d){for(b=0;b<d;b++)m[b]=Q.jgrid.trim(e[q.yDimension[b].dataName]),q.yDimension[b].converter&&Q.jgrid.isFunction(q.yDimension[b].converter)&&(m[b]=q.yDimension[b].converter.call(this,m[b],u,m));c=s(e,q.aggregates,m,c)}else
0===d&&(c=s(e,q.aggregates,null,c));A.push(c)}var
D,j=0,F=null,O=null;for(D
in
G)if(G.hasOwnProperty(D)){if(0===j)g.children&&void
0!==g.children||(g={text:D,level:0,children:[],label:D}),F=g.children;else{for(O=null,y=0;y<F.length;y++)if(F[y].text===D){O=F[y];break}F=O?O.children:(F.push({children:[],text:D,level:j,fields:G[D],label:M[D]}),F[F.length-1].children)}j++}a++}v=null;var
S,T=[],C=N.length,P=C;if(0<d&&(V[d-1]={useColSpanStyle:!1,groupHeaders:[]}),function
e(r){var
o,t,a,n,i;for(a
in
r)if(r.hasOwnProperty(a)){if("object"!=typeof
r[a]){if("level"===a){if(void
0===T[r.level]&&(T[r.level]="",0<r.level&&-1===r.text.indexOf("_r_Totals")&&(V[r.level-1]={useColSpanStyle:!1,groupHeaders:[]})),T[r.level]!==r.text&&r.children.length&&-1===r.text.indexOf("_r_Totals")&&0<r.level){V[r.level-1].groupHeaders.push({titleText:r.label,numberOfColumns:0});var
s=V[r.level-1].groupHeaders.length-1,l=0==s?P:C;if(r.level-1==(q.rowTotals?1:0)&&0<s){for(var
g=0,u=0;u<s;u++)g+=V[r.level-1].groupHeaders[u].numberOfColumns;g&&(l=g+p)}N[l]&&(V[r.level-1].groupHeaders[s].startColumnName=N[l].name,V[r.level-1].groupHeaders[s].numberOfColumns=N.length-l),C=N.length}T[r.level]=r.text}if(r.level===d&&"level"===a&&0<d)if(1<f){var
m=1;for(o
in
r.fields)r.fields.hasOwnProperty(o)&&(1===m&&V[d-1].groupHeaders.push({startColumnName:o,numberOfColumns:1,titleText:r.label||r.text}),m++);V[d-1].groupHeaders[V[d-1].groupHeaders.length-1].numberOfColumns=m-1}else
V.splice(d-1,1)}if(null!=r[a]&&"object"==typeof
r[a]&&e(r[a]),"level"===a&&0<r.level&&(r.level===(0===d?r.level:d)||-1!==T[r.level].indexOf("_r_Totals")))for(o
in
t=0,r.fields)if(r.fields.hasOwnProperty(o)){for(n
in
i={},q.aggregates[t])if(q.aggregates[t].hasOwnProperty(n))switch(n){case"member":case"label":case"aggregator":break;default:i[n]=q.aggregates[t][n]}1<f?(i.name=o,i.label=q.aggregates[t].label||r.label):(i.name=r.text,i.label="_r_Totals"===r.text?q.rowTotalsText:r.label),N.push(i),t++}}}(g),q.colTotals)for(var
_=A.length;_--;)for(y=p;y<N.length;y++)S=N[y].name,k[S]?k[S]+=parseFloat(A[_][S]||0):k[S]=parseFloat(A[_][S]||0);if(0<l)for(y=0;y<l;y++)N[y].isGroupField&&(z.groupingView.groupField.push(N[y].name),z.groupingView.groupSummary.push(q.groupSummary),z.groupingView.groupSummaryPos.push(q.groupSummaryPos));else
z.grouping=!1;z.sortname=N[l].name,z.groupingView.hideFirstGroupCol=!0}),{colModel:N,rows:A,groupOptions:z,groupHeaders:V,summary:k}},jqPivot:function(o,g,u,t){return this.each(function(){var
l=this,e=u.regional||"en";function
r(e){Q.jgrid.isFunction(g.onInitPivot)&&g.onInitPivot.call(l),Array.isArray(e)||(e=[]);var
r,o,t,a,n=jQuery(l).jqGrid("pivotSetup",e,g),e=0<Q.assocArraySize(n.summary),i=Q.jgrid.from.call(l,n.rows);for(g.ignoreCase&&(i=i.ignoreCase()),r=0;r<n.groupOptions.groupingView.groupField.length;r++)o=g.xDimension[r].sortorder||"asc",t=g.xDimension[r].sorttype||"text",i.orderBy(n.groupOptions.groupingView.groupField[r],o,t,"",t);if(a=g.xDimension.length,u.sortname){for(o=u.sortorder||"asc",t="text",r=0;r<a;r++)if(g.xDimension[r].dataName===u.sortname){t=g.xDimension[r].sorttype||"text";break}i.orderBy(u.sortname,o,t,"",t)}else
n.groupOptions.sortname&&a&&(o=g.xDimension[a-1].sortorder||"asc",t=g.xDimension[a-1].sorttype||"text",i.orderBy(n.groupOptions.sortname,o,t,"",t));jQuery(l).jqGrid(Q.extend(!0,{datastr:Q.extend(i.select(),e?{userdata:n.summary}:{}),datatype:"jsonstring",footerrow:e,userDataOnFooter:e,colModel:n.colModel,viewrecords:!0,formatFooterData:!0===g.colTotals,sortname:g.xDimension[0].dataName},n.groupOptions,u||{}));var
s=n.groupHeaders;if(s.length)for(r=0;r<s.length;r++)s[r]&&s[r].groupHeaders.length&&jQuery(l).jqGrid("setGroupHeaders",s[r]);g.frozenStaticCols&&jQuery(l).jqGrid("setFrozenColumns"),Q.jgrid.isFunction(g.onCompletePivot)&&g.onCompletePivot.call(l),g.loadMsg&&Q(".loading_pivot").remove()}void
0===g.loadMsg&&(g.loadMsg=!0),g.loadMsg&&Q("<div class='loading_pivot ui-state-default ui-state-active row'>"+Q.jgrid.getRegional(l,"regional."+e+".defaults.loadtext")+"</div>").insertBefore(l).show(),"string"==typeof
o?Q.ajax(Q.extend({url:o,dataType:"json",success:function(e){r(Q.jgrid.getAccessor(e,t&&t.reader?t.reader:"rows"))}},t||{})):r(o)})}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.utils","./grid.base"],e):e(jQuery)}(function(G){"use strict";G.jgrid=G.jgrid||{},G.extend(G.jgrid,{saveState:function(e,t){if(t=G.extend({useStorage:!0,storageType:"localStorage",beforeSetItem:null,compression:!1,compressionModule:"LZString",compressionMethod:"compressToUTF16",debug:!1,saveData:!0},t||{}),e){var
r,i="",o="",a=G("#"+e)[0];if(a.grid){if((e=G(a).data("inlineNav"))&&a.p.inlineNav&&G(a).jqGrid("setGridParam",{_iN:e}),(e=G(a).data("filterToolbar"))&&a.p.filterToolbar&&G(a).jqGrid("setGridParam",{_fT:e}),i=G(a).jqGrid("jqGridExport",{exptype:"jsonstring",ident:"",root:"",data:t.saveData}),o="",t.saveData&&(d=(o=G(a.grid.bDiv).find(".ui-jqgrid-btable tbody").first().html()).indexOf("</tr>"),o=o.slice(d+5)),G.jgrid.isFunction(t.beforeSetItem)&&null!=(r=t.beforeSetItem.call(a,i))&&(i=r),t.debug){G("#gbox_tree").prepend('<a id="link_save" target="_blank" download="jqGrid_dump.txt">Click to save Dump Data</a>');var
n,d,s=[],l={};s.push("Grid Options\n"),s.push(i),s.push("\n"),s.push("GridData\n"),s.push(o),l.type="plain/text;charset=utf-8";try{n=new
File(s,"jqGrid_dump.txt",l)}catch(e){n=new
Blob(s,l)}d=URL.createObjectURL(n),G("#link_save").attr("href",d).on("click",function(){G(this).remove()})}if(t.compression&&t.compressionModule)try{null!=(r=window[t.compressionModule][t.compressionMethod](i))&&(i=r,o=window[t.compressionModule][t.compressionMethod](o))}catch(e){}if(t.useStorage&&G.jgrid.isLocalStorage())try{window[t.storageType].setItem("jqGrid"+a.p.id,i),window[t.storageType].setItem("jqGrid"+a.p.id+"_data",o)}catch(e){22===e.code&&alert("Local storage limit is over!")}return i}}},loadState:function(e,t,r){if(r=G.extend({useStorage:!0,storageType:"localStorage",clearAfterLoad:!1,beforeSetGrid:null,afterSetGrid:null,decompression:!1,decompressionModule:"LZString",decompressionMethod:"decompressFromUTF16",restoreData:!0},r||{}),e){var
i,o,a,n,d=G("#"+e)[0];if(r.useStorage)try{t=window[r.storageType].getItem("jqGrid"+d.id),o=window[r.storageType].getItem("jqGrid"+d.id+"_data")}catch(e){}if(t){if(r.decompression&&r.decompressionModule)try{null!=(i=window[r.decompressionModule][r.decompressionMethod](t))&&(t=i,o=window[r.decompressionModule][r.decompressionMethod](o))}catch(e){}if((i=G.jgrid.parseFunc(t))&&"object"===G.jgrid.type(i)){d.grid&&G.jgrid.gridUnload(e),G.jgrid.isFunction(r.beforeSetGrid)&&(l=r.beforeSetGrid(i))&&"object"===G.jgrid.type(l)&&(i=l);var
s=function(e){return e},l={reccount:i.reccount,records:i.records,lastpage:i.lastpage,shrinkToFit:s(i.shrinkToFit),data:s(i.data),datatype:s(i.datatype),grouping:s(i.grouping)};i.shrinkToFit=!1,i.data=[],i.datatype="local",i.grouping=!1,i.inlineNav&&(a=s(i._iN),i._iN=null,delete
i._iN),i.filterToolbar&&(n=s(i._fT),i._fT=null,delete
i._fT);var
p=G("#"+e).jqGrid(i);if(p.jqGrid("delRowData","norecs"),r.restoreData&&""!==G.jgrid.trim(o)&&p.append(o),p.jqGrid("setGridParam",l),i.storeNavOptions&&i.navGrid&&(p[0].p.navGrid=!1,p.jqGrid("navGrid",i.pager,i.navOptions,i.editOptions,i.addOptions,i.delOptions,i.searchOptions,i.viewOptions),i.navButtons&&i.navButtons.length))for(var
c=0;c<i.navButtons.length;c++)"sepclass"in
i.navButtons[c][1]?p.jqGrid("navSeparatorAdd",i.navButtons[c][0],i.navButtons[c][1]):p.jqGrid("navButtonAdd",i.navButtons[c][0],i.navButtons[c][1]);if(p[0].refreshIndex(),i.subGrid&&(s=1===i.multiselect?1:0,l=!0===i.rownumbers?1:0,p.jqGrid("addSubGrid",s+l),G.each(p[0].rows,function(e,t){G(t).hasClass("ui-sg-expanded")&&G(p[0].rows[e-1]).find("td.sgexpanded").click().click()})),i.treeGrid)for(var
g=1,u=p[0].rows.length,m=i.expColInd,f=i.treeReader.leaf_field,j=i.treeReader.expanded_field;g<u;)G(p[0].rows[g].cells[m]).find("div.treeclick").on("click",function(e){e=e.target||e.srcElement,e=G.jgrid.stripPref(i.idPrefix,G(e,p[0].rows).closest("tr.jqgrow")[0].id),e=p[0].p._index[e];return p[0].p.data[e][f]||(p[0].p.data[e][j]?(p.jqGrid("collapseRow",p[0].p.data[e]),p.jqGrid("collapseNode",p[0].p.data[e])):(p.jqGrid("expandRow",p[0].p.data[e]),p.jqGrid("expandNode",p[0].p.data[e]))),!1}),!0===i.ExpandColClick&&G(p[0].rows[g].cells[m]).find("span.cell-wrapper").css("cursor","pointer").on("click",function(e){var
t=e.target||e.srcElement,e=G.jgrid.stripPref(i.idPrefix,G(t,p[0].rows).closest("tr.jqgrow")[0].id),t=p[0].p._index[e];return p[0].p.data[t][f]||(p[0].p.data[t][j]?(p.jqGrid("collapseRow",p[0].p.data[t]),p.jqGrid("collapseNode",p[0].p.data[t])):(p.jqGrid("expandRow",p[0].p.data[t]),p.jqGrid("expandNode",p[0].p.data[t]))),p.jqGrid("setSelection",e),!1}),g++;i.multiselect&&G.each(i.selarrrow,function(){G("#jqg_"+e+"_"+this)[i.useProp?"prop":"attr"]("checked","checked")}),p.jqGrid("isGroupHeaderOn")&&p.jqGrid("refreshGroupHeaders"),i.inlineNav&&a&&(p.jqGrid("setGridParam",{inlineNav:!1}),p.jqGrid("inlineNav",i.pager,a)),i.filterToolbar&&n&&(p.jqGrid("setGridParam",{filterToolbar:!1}),n.restoreFromFilters=!0,p.jqGrid("filterToolbar",n)),i.frozenColumns&&p.jqGrid("setFrozenColumns"),p[0].updatepager(!0,!0),G.jgrid.isFunction(r.afterSetGrid)&&r.afterSetGrid(p),r.clearAfterLoad&&(window[r.storageType].removeItem("jqGrid"+d.id),window[r.storageType].removeItem("jqGrid"+d.id+"_data"))}else
alert("can not convert to object")}}},isGridInStorage:function(e,t){var
r,i,o,a={storageType:"localStorage"},a=G.extend(a,t||{});try{i=window[a.storageType].getItem("jqGrid"+e),o=window[a.storageType].getItem("jqGrid"+e+"_data"),r=null!=i&&null!=o&&"string"==typeof
i&&"string"==typeof
o}catch(e){r=!1}return r},setRegional:function(e,t){var
r={storageType:"sessionStorage"};if((r=G.extend(r,t||{})).regional){G.jgrid.saveState(e,r),r.beforeSetGrid=function(e){return e.regional=r.regional,e.force_regional=!0,e},G.jgrid.loadState(e,null,r);var
i=G("#"+e)[0],t=G(i).jqGrid("getGridParam","colModel"),o=-1,a=G.jgrid.getRegional(i,"nav");G.each(t,function(e){if(this.formatter&&"actions"===this.formatter)return o=e,!1}),-1!==o&&a&&G("#"+e+" tbody tr").each(function(){var
e=this.cells[o];G(e).find(".ui-inline-edit").attr("title",a.edittitle),G(e).find(".ui-inline-del").attr("title",a.deltitle),G(e).find(".ui-inline-save").attr("title",a.savetitle),G(e).find(".ui-inline-cancel").attr("title",a.canceltitle)});try{window[r.storageType].removeItem("jqGrid"+i.id),window[r.storageType].removeItem("jqGrid"+i.id+"_data")}catch(e){}}},jqGridImport:function(e,r){r=G.extend({imptype:"xml",impstring:"",impurl:"",mtype:"GET",impData:{},xmlGrid:{config:"root>grid",data:"root>rows"},jsonGrid:{config:"grid",data:"data"},ajaxOptions:{}},r||{});function
i(e,t){var
r,i,o,a=G(t.xmlGrid.config,e)[0],t=G(t.xmlGrid.data,e)[0];if(G.grid.xmlToJSON){for(o
in
r=G.jgrid.xmlToJSON(a))r.hasOwnProperty(o)&&(i=r[o]);t?(t=r.grid.datatype,r.grid.datatype="xmlstring",r.grid.datastr=e,G(n).jqGrid(i).jqGrid("setGridParam",{datatype:t})):setTimeout(function(){G(n).jqGrid(i)},0)}else
alert("xml2json or parse are not present")}function
t(e,t){var
r;e&&"string"==typeof
e&&(e=(r=G.jgrid.parseFunc(e))[t.jsonGrid.config],(r=r[t.jsonGrid.data])?(t=e.datatype,e.datatype="jsonstring",e.datastr=r,G(n).jqGrid(e).jqGrid("setGridParam",{datatype:t})):G(n).jqGrid(e))}var
o,n=(0===e.indexOf("#")?"":"#")+G.jgrid.jqID(e);switch(r.imptype){case"xml":G.ajax(G.extend({url:r.impurl,type:r.mtype,data:r.impData,dataType:"xml",complete:function(e,t){"success"===t&&(i(e.responseXML,r),G(n).triggerHandler("jqGridImportComplete",[e,r]),G.jgrid.isFunction(r.importComplete)&&r.importComplete(e)),e=null}},r.ajaxOptions));break;case"xmlstring":!r.impstring||"string"!=typeof
r.impstring||(o=G.parseXML(r.impstring))&&(i(o,r),G(n).triggerHandler("jqGridImportComplete",[o,r]),G.jgrid.isFunction(r.importComplete)&&r.importComplete(o));break;case"json":G.ajax(G.extend({url:r.impurl,type:r.mtype,data:r.impData,dataType:"json",complete:function(e){try{t(e.responseText,r),G(n).triggerHandler("jqGridImportComplete",[e,r]),G.jgrid.isFunction(r.importComplete)&&r.importComplete(e)}catch(e){}e=null}},r.ajaxOptions));break;case"jsonstring":r.impstring&&"string"==typeof
r.impstring&&(t(r.impstring,r),G(n).triggerHandler("jqGridImportComplete",[r.impstring,r]),G.jgrid.isFunction(r.importComplete)&&r.importComplete(r.impstring))}}}),G.jgrid.extend({jqGridExport:function(t){t=G.extend({exptype:"xmlstring",root:"grid",ident:"\t",addOptions:{},data:!0},t||{});var
r=null;return this.each(function(){if(this.grid){var
e=G.extend(!0,{},G(this).jqGrid("getGridParam"),t.addOptions);switch(e.rownumbers&&(e.colNames.splice(0,1),e.colModel.splice(0,1)),e.multiselect&&(e.colNames.splice(0,1),e.colModel.splice(0,1)),e.subGrid&&(e.colNames.splice(0,1),e.colModel.splice(0,1)),e.knv=null,t.data||(e.data=[],e._index={}),t.exptype){case"xmlstring":r="<"+t.root+">"+G.jgrid.jsonToXML(e,{xmlDecl:""})+"</"+t.root+">";break;case"jsonstring":r=G.jgrid.stringify(e),t.root&&(r="{"+t.root+":"+r+"}")}}}),r},excelExport:function(d){return d=G.extend({exptype:"remote",url:null,oper:"oper",tag:"excel",beforeExport:null,exporthidden:!1,exportgrouping:!1,exportOptions:{}},d||{}),this.each(function(){if(this.grid&&"remote"===d.exptype){var
e,t=G.extend({},this.p.postData);if(t[d.oper]=d.tag,G.jgrid.isFunction(d.beforeExport)&&(n=d.beforeExport.call(this,t),G.isPlainObject(n)&&(t=n)),d.exporthidden){for(var
r=this.p.colModel,i=r.length,o=[],a=0;a<i;a++)void
0===r[a].hidden&&(r[a].hidden=!1),o.push({name:r[a].name,hidden:r[a].hidden});var
n=JSON.stringify(o);"string"==typeof
n&&(t.colModel=n)}d.exportgrouping&&"string"==typeof(e=JSON.stringify(this.p.groupingView))&&(t.groupingView=e);var
t=jQuery.param(t),t=-1!==d.url.indexOf("?")?d.url+"&"+t:d.url+"?"+t;window.location=t}})}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base","./jquery.fmatter","./grid.utils"],e):e(jQuery)}(function(P){"use strict";P.jgrid=P.jgrid||{},P.extend(P.jgrid,{formatCell:function(e,t,r,l,o,a){return void
0!==l.formatter?(a={rowId:"",colModel:l,gid:o.p.id,pos:t,styleUI:"",isExported:!0,exporttype:a},P.jgrid.isFunction(l.formatter)?l.formatter.call(o,e,a,r):P.fmatter?P.fn.fmatter.call(o,l.formatter,e,a,r):e):e},formatCellCsv:function(t,e){t=null==t?"":String(t);try{t=P.jgrid.stripHtml(t.replace(e._regexsep,e.separatorReplace).replace(/\r\n/g,e.replaceNewLine).replace(/\n/g,e.replaceNewLine))}catch(e){t=""}return e.escquote&&(t=t.replace(e._regexquot,e.escquote+e.quote)),-1!==t.indexOf(e.separator)&&-1!==t.indexOf(e.qoute)||(t=e.quote+t+e.quote),t},excelCellPos:function(e){for(var
t="A".charCodeAt(0),r="Z".charCodeAt(0)-t+1,l="";0<=e;)l=String.fromCharCode(e%r+t)+l,e=Math.floor(e/r)-1;return l},makeNode:function(e,t,r){var
l=e.createElement(t);return r&&(r.attr&&P(l).attr(r.attr),r.children&&P.each(r.children,function(e,t){l.appendChild(t)}),r.hasOwnProperty("text")&&l.appendChild(e.createTextNode(r.text))),l},xmlToZip:function(o,e){var
a,d,n,i,p,s=this,m=new
XMLSerializer,f=-1===m.serializeToString(P.parseXML(P.jgrid.excelStrings["xl/worksheets/sheet1.xml"])).indexOf("xmlns:r"),u=[];P.each(e,function(e,t){if(P.isPlainObject(t))p=o.folder(e),s.xmlToZip(p,t);else{if(f){for(a=t.childNodes[0],d=a.attributes.length-1;0<=d;d--){var
r=a.attributes[d].nodeName,l=a.attributes[d].nodeValue;-1!==r.indexOf(":")&&(u.push({name:r,value:l}),a.removeAttribute(r))}for(d=0,n=u.length;d<n;d++)(i=t.createAttribute(u[d].name.replace(":","_dt_b_namespace_token_"))).value=u[d].value,a.setAttributeNode(i)}p=m.serializeToString(t),f&&(-1===p.indexOf("<?xml")&&(p='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+p),p=p.replace(/_dt_b_namespace_token_/g,":")),p=p.replace(/<row xmlns="" /g,"<row ").replace(/<cols xmlns="">/g,"<cols>").replace(/<mergeCells xmlns="" /g,"<mergeCells ").replace(/<numFmt xmlns="" /g,"<numFmt ").replace(/<xf xmlns="" /g,"<xf "),o.file(e,p)}})},excelStrings:{"_rels/.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/></Relationships>',"xl/_rels/workbook.xml.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/></Relationships>',"[Content_Types].xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="application/xml" /><Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" /><Default Extension="jpeg" ContentType="image/jpeg" /><Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml" /><Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml" /><Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml" /></Types>',"xl/workbook.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"><fileVersion appName="xl" lastEdited="5" lowestEdited="5" rupBuild="24816"/><workbookPr showInkAnnotation="0" autoCompressPictures="0"/><bookViews><workbookView xWindow="0" yWindow="0" windowWidth="25600" windowHeight="19020" tabRatio="500"/></bookViews><sheets><sheet name="Sheet1" sheetId="1" r:id="rId1"/></sheets></workbook>',"xl/worksheets/sheet1.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><sheetData/></worksheet>',"xl/styles.xml":'<?xml version="1.0" encoding="UTF-8"?><styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><numFmts count="7"><numFmt numFmtId="164" formatCode="#,##0.00_- [$$-45C]"/><numFmt numFmtId="165" formatCode="&quot;£&quot;#,##0.00"/><numFmt numFmtId="166" formatCode="[$€-2] #,##0.00"/><numFmt numFmtId="167" formatCode="0.0%"/><numFmt numFmtId="168" formatCode="#,##0;(#,##0)"/><numFmt numFmtId="169" formatCode="#,##0.00;(#,##0.00)"/><numFmt numFmtId="170" formatCode="yyyy/mm/dd;@"/></numFmts><fonts count="5" x14ac:knownFonts="1"><font><sz val="11" /><name val="Calibri" /></font><font><sz val="11" /><name val="Calibri" /><color rgb="FFFFFFFF" /></font><font><sz val="11" /><name val="Calibri" /><b /></font><font><sz val="11" /><name val="Calibri" /><i /></font><font><sz val="11" /><name val="Calibri" /><u /></font></fonts><fills count="6"><fill><patternFill patternType="none" /></fill><fill/><fill><patternFill patternType="solid"><fgColor rgb="FFD9D9D9" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="FFD99795" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="ffc6efce" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="ffc6cfef" /><bgColor indexed="64" /></patternFill></fill></fills><borders count="2"><border><left /><right /><top /><bottom /><diagonal /></border><border diagonalUp="false" diagonalDown="false"><left style="thin"><color auto="1" /></left><right style="thin"><color auto="1" /></right><top style="thin"><color auto="1" /></top><bottom style="thin"><color auto="1" /></bottom><diagonal /></border></borders><cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" /></cellStyleXfs><cellXfs count="68"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="left"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="center"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="right"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="fill"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment textRotation="90"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment wrapText="1"/></xf><xf numFmtId="9"   fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="164" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="165" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="166" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="167" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="168" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="169" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="3" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="4" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="1" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="2" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="170" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="49" fontId="0" fillId="0" borderId="0" xfId="0" applyNumberFormat="1"/></cellXfs><cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0" /></cellStyles><dxfs count="0" /><tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4" /></styleSheet>'},excelParsers:[{match:/^\-?\d+\.\d%$/,style:60,fmt:function(e){return e/100}},{match:/^\-?\d+\.?\d*%$/,style:56,fmt:function(e){return e/100}},{match:/^\-?\$[\d,]+.?\d*$/,style:57},{match:/^\-?£[\d,]+.?\d*$/,style:58},{match:/^\-?€[\d,]+.?\d*$/,style:59},{match:/^\-?\d+$/,style:65},{match:/^\-?\d+\.\d{2}$/,style:66},{match:/^\([\d,]+\)$/,style:61,fmt:function(e){return-1*e.replace(/[\(\)]/g,"")}},{match:/^\([\d,]+\.\d{2}\)$/,style:62,fmt:function(e){return-1*e.replace(/[\(\)]/g,"")}},{match:/^\-?[\d,]+$/,style:63},{match:/^\-?[\d,]+\.\d{2}$/,style:64},{match:/^\d{4}\-\d{2}\-\d{2}$/,style:67},{match:/(https?:\/\/(?:www\.|(?!www))[^\s\.]+\.[^\s]{2,}|www\.[^\s]+\.[^\s]{2,})/gi,style:4}]}),P.jgrid.extend({exportToCsv:function(k){k=P.extend(!0,{separator:",",separatorReplace:" ",quote:'"',escquote:'"',newLine:"\r\n",replaceNewLine:" ",includeCaption:!0,includeLabels:!0,includeGroupHeader:!0,includeFooter:!0,includeHeader:!0,fileName:"jqGridExport.csv",mimetype:"text/csv;charset=utf-8",returnAsString:!1,onBeforeExport:null,treeindent:" ",loadIndicator:!0},k||{});var
T="";if(this.each(function(){k._regexsep=new
RegExp(k.separator,"g"),k._regexquot=new
RegExp(k.quote,"g");var
e,t,w=this,r=w.p.treeGrid?P(w).jqGrid("getRowData",null,!0,k.treeindent):w.addLocalData(!0),l=r.length,o=w.p.colModel,a=o.length,d=w.p.colNames,n=0,i="",p="",s="",m="",f="",u=[],c="";P.jgrid.isFunction(k.loadIndicator)?k.loadIndicator("show"):k.loadIndicator&&P(w).jqGrid("progressBar",{method:"show",loadtype:w.p.loadui,htmlcontent:P.jgrid.getRegional(w,"defaults.loadtext")});var
g=[];if(P.each(o,function(e,t){t._expcol=!0,void
0===t.exportcol?t.hidden&&(t._expcol=!1):t._expcol=t.exportcol,"cb"!==t.name&&"rn"!==t.name&&"subgrid"!==t.name||(t._expcol=!1),t._expcol&&(u.push(P.jgrid.formatCellCsv(d[e],k)),g.push(t.name))}),k.includeLabels&&(f=u.join(k.separator)+k.newLine),k.collen=u.length,w.p.grouping){var
y=!!w.p.groupingView._locgr;w.p.groupingView._locgr=!1,i+=function(s,m){var
f="",u=w.p.groupingView,c=[],g=u.groupField.length,y=w.p.colModel,h=y.length,x=0;function
I(e,t,r,l){for(var
o,a,d=function(e,t,r){var
l,o=!1;if(0===t)o=r[e];else{var
a=r[e].idx;if(0===a)o=r[e];else
for(l=e;0<=l;l--)if(r[l].idx===a-t){o=r[l];break}}return o}(e,t,r),n=d.cnt,i=new
Array(m.collen),p=0,s=l;s<h;s++)y[s]._excol&&(a="{0}",P.each(d.summary,function(){if(this.nm===y[s].name){y[s].summaryTpl&&(a=y[s].summaryTpl),"string"==typeof
this.st&&"avg"===this.st.toLowerCase()&&(this.sd&&this.vd?this.v=this.v/this.vd:this.v&&0<n&&(this.v=this.v/n));try{this.groupCount=d.cnt,this.groupIndex=d.dataIndex,this.groupValue=d.value,o=this.v}catch(e){o=this.v}return i[p]=P.jgrid.formatCellCsv(P.jgrid.stripHtml(P.jgrid.template(a,o)),m),!1}}),p++);return i}P.each(y,function(e,t){for(var
r=0;r<g;r++)if(u.groupField[r]===t.name){c[r]=e;break}});var
F,b,v=P.makeArray(u.groupSummary);if(v.reverse(),"local"===w.p.datatype&&!w.p.loadonce){P(w).jqGrid("groupingSetup");for(var
e=P.jgrid.getMethod("groupingPrepare"),t=0;t<l;t++)e.call(P(w),r[t],t)}return P.each(u.groups,function(e,t){x++;try{F=Array.isArray(u.formatDisplayField)&&P.jgrid.isFunction(u.formatDisplayField[t.idx])?u.formatDisplayField[t.idx].call(w,t.displayValue,t.value,w.p.colModel[c[t.idx]],t.idx,u):w.formatter("",t.displayValue,c[t.idx],t.value)}catch(e){F=t.displayValue}var
r,l="";if("string"!=typeof(l=P.jgrid.isFunction(u.groupText[t.idx])?u.groupText[t.idx].call(w,F,t.cnt,t.summary):P.jgrid.template(u.groupText[t.idx],F,t.cnt,t.summary))&&"number"!=typeof
l&&(l=F),(r="header"===u.groupSummaryPos[t.idx]?I(e,0,u.groups,0):new
Array(m.collen))[0]=P.jgrid.formatCellCsv(P.jgrid.stripHtml(l),m),f+=r.join(m.separator)+m.newLine,g-1===t.idx){for(var
o,a,d,n=u.groups[e+1],l=t.startRow,i=void
0!==n?n.startRow:u.groups[e].startRow+u.groups[e].cnt,p=l;p<i&&s[+p];p++){for(a=s[+p],o=b=0;o<y.length;o++)y[o]._expcol&&(r[b]=P.jgrid.formatCellCsv(P.jgrid.formatCell(P.jgrid.getAccessor(a,y[o].name),o,a,y[o],w,"csv"),m),b++);f+=r.join(m.separator)+m.newLine}if("header"!==u.groupSummaryPos[t.idx]){if(void
0!==n){for(d=0;d<u.groupField.length&&n.dataIndex!==u.groupField[d];d++);x=u.groupField.length-d}for(o=0;o<x;o++)v[o]&&(r=I(e,o,u.groups,0),f+=r.join(m.separator)+m.newLine);x=d}}}),f}(r,k),w.p.groupingView._locgr=y}else
for(;n<l;){for(e=r[n],F=[],x=t=0;x<a;x++)o[x]._expcol&&(F[t]=P.jgrid.formatCellCsv(P.jgrid.formatCell(P.jgrid.getAccessor(e,o[x].name),x,e,o[x],w,"csv"),k),t++);i+=F.join(k.separator)+k.newLine,n++}if(r=null,F=new
Array(k.collen),k.includeCaption&&w.p.caption){for(n=k.collen;--n;)F[n]="";F[0]=P.jgrid.formatCellCsv(w.p.caption,k),p+=F.join(k.separator)+k.newLine}if(k.includeGroupHeader&&P(w).jqGrid("isGroupHeaderOn"))for(var
h=w.p.groupHeader,x=0;x<h.length;x++){for(var
I=h[x].groupHeaders,n=0,F=[],b=0;b<g.length;b++){for(F[n]="",t=0;t<I.length;t++)I[t].startColumnName===g[b]&&(F[n]=P.jgrid.formatCellCsv(I[t].titleText,k));n++}s+=F.join(k.separator)+k.newLine}if(k.includeFooter&&w.p.footerrow&&P(".ui-jqgrid-ftable",this.sDiv).length){var
v=P(w).jqGrid("footerData","get");for(x=0,F=[];x<k.collen;){var
j=g[x];v.hasOwnProperty(j)&&F.push(P.jgrid.formatCellCsv(P.jgrid.stripHtml(v[j]),k)),x++}m+=F.join(k.separator)+k.newLine}if(k.includeHeader&&w.p.headerrow){var
C=P(w).jqGrid("headerData","get");for(x=0,F=[];x<k.collen;){var
B=g[x];C.hasOwnProperty(B)&&F.push(P.jgrid.formatCellCsv(P.jgrid.stripHtml(C[B]),k)),x++}c+=F.join(k.separator)+k.newLine}T=p+s+f+c+i+m,P.jgrid.isFunction(k.loadIndicator)?k.loadIndicator("hide"):k.loadIndicator&&P(w).jqGrid("progressBar",{method:"hide",loadtype:w.p.loadui})}),k.returnAsString)return T;if(-1!==k.mimetype.toUpperCase().indexOf("UTF-8")&&(T="\ufeff"+T),P.jgrid.isFunction(k.onBeforeExport)&&!(T=k.onBeforeExport(T)))throw"Before export does not return data!";P.jgrid.saveAs(T,k.fileName,{type:k.mimetype})},exportToExcel:function(L){L=P.extend(!0,{includeLabels:!0,includeGroupHeader:!0,includeFooter:!0,includeHeader:!0,fileName:"jqGridExport.xlsx",mimetype:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",maxlength:40,onBeforeExport:null,replaceStr:null,treeindent:" ",loadIndicator:!0},L||{}),this.each(function(){for(var
F=this,e=P.jgrid.excelStrings,c=0,g=P.parseXML(e["xl/worksheets/sheet1.xml"]),y=g.getElementsByTagName("sheetData")[0],o=P.parseXML(e["xl/styles.xml"]),a=o.getElementsByTagName("numFmts")[0],d=o.getElementsByTagName("cellXfs")[0],t={_rels:{".rels":P.parseXML(e["_rels/.rels"])},xl:{_rels:{"workbook.xml.rels":P.parseXML(e["xl/_rels/workbook.xml.rels"])},"workbook.xml":P.parseXML(e["xl/workbook.xml"]),"styles.xml":o,worksheets:{"sheet1.xml":g}},"[Content_Types].xml":P.parseXML(e["[Content_Types].xml"])},b=F.p.colModel,r=0,v={body:F.p.treeGrid?P(F).jqGrid("getRowData",null,!0,L.treeindent):F.addLocalData(!0),header:[],footer:[],width:[],map:[],parser:[]},l=0,n=b.length;l<n;l++)b[l]._expcol=!0,void
0===b[l].exportcol?b[l].hidden&&(b[l]._expcol=!1):b[l]._expcol=b[l].exportcol,"cb"!==b[l].name&&"rn"!==b[l].name&&"subgrid"!==b[l].name&&b[l]._expcol&&(v.header[r]=b[l].name,v.width[r]=5,v.map[r]=l,v.parser[l]=function(e){var
r,l,t;return P.isEmptyObject(e)?e.excel_parsers=!0:e.excel_format&&!e.excel_style&&(l=r=0,t=P(a.getElementsByTagName("numFmt")),P.each(t,function(e,t){r++,l=Math.max(l,parseInt(P(t).attr("numFmtId"),10))}),t=P.jgrid.makeNode(o,"numFmt",{attr:{numFmtId:l+1,formatCode:e.excel_format}}),a.appendChild(t),P(a).attr("count",r+1),r=0,t=P.jgrid.makeNode(o,"xf",{attr:{numFmtId:l+1+"",fontId:"0",fillId:"0",borderId:"0",applyFont:"1",applyFill:"1",applyBorder:"1",xfId:"0",applyNumberFormat:"1"}}),d.appendChild(t),r=parseInt(P(d).attr("count"),10),P(d).attr("count",r+1),e.excel_style||(e.excel_style=r+1)),e}(b[l].hasOwnProperty("exportoptions")?P.extend({},b[l].exportoptions):{}),r++);function
h(e,t){return P.jgrid.makeNode(g,"c",{attr:e,children:[P.jgrid.makeNode(g,"v",{text:t})]})}function
x(e,t){return P.jgrid.makeNode(g,"c",{attr:{t:"inlineStr",r:e,s:"68"},children:{row:P.jgrid.makeNode(g,"is",{children:{row:P.jgrid.makeNode(g,"t",{text:t})}})}})}var
I,w,j=P.jgrid.isFunction(L.replaceStr)?L.replaceStr:function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F-\x9F]/g,"")},C=function(e,t){I=c+1,w=P.jgrid.makeNode(g,"row",{attr:{r:I}});for(var
r,l,o=0;o<v.header.length;o++){var
a,d,n=P.jgrid.excelCellPos(o)+""+I;null==(u=Array.isArray(e)&&t?F.p.colNames[v.map[o]]:P.jgrid.getAccessor(e,v.header[o]))&&(u=""),t||(u=P.jgrid.formatCell(u,v.map[o],e,b[v.map[o]],F,"excel"))&&("&nbsp;"===u||"&#160;"===u||1===u.length&&160===u.charCodeAt(0))&&(u=""),v.width[o]=Math.max(v.width[o],Math.min(parseInt(u.toString().length,10),L.maxlength)),a=null;var
i=v.parser[v.map[o]];if(!0===i.excel_parsers)for(var
p=0,s=P.jgrid.excelParsers.length;p<s;p++){var
m=P.jgrid.excelParsers[p];if(u.match&&!u.match(/^0\d+/)&&u.match(m.match)){var
f=u,u=u.replace(/[^\d\.\-]/g,"");if(m.fmt&&(u=m.fmt(u)),67===m.style)a=h({t:"d",r:n,s:m.style},u);else
if(4===m.style)r=f,l=void
0,(l=document.createElement("div")).innerHTML=r,a=(u="A"===(l=l.firstChild).nodeName?[l.href,l.text]:"#text"===l.nodeName&&[l.textContent,l.textContent])?(r={t:"str",r:n,s:m.style},l='HYPERLINK("'+u[0]+'","'+u[1]+'")',P.jgrid.makeNode(g,"c",{attr:r,children:[P.jgrid.makeNode(g,"f",{text:l})]})):x(n,f);else{if(P.inArray(m.style,["63","64","65","66"])&&15<u.toString().length){a=x(n,f.replace?j(f):f),w.appendChild(a);break}a=h({r:n,s:m.style},u)}w.appendChild(a);break}}else
void
0===i.excel_style||t||a||(i.replace_format&&(u=i.replace_format(u)),a="text"===i.excel_style?x(n,u):h({r:n,s:i.excel_style},u),w.appendChild(a));a||(u.match&&(d=u.match(/^-?([1-9]\d+)(\.(\d+))?$/)),a="number"==typeof
u&&u.toString().length<=15||d&&d[1].length+(d[2]?d[3].length:0)<=15?h({t:"n",r:n},u):x(n,u.replace?j(u):u),w.appendChild(a))}y.appendChild(w),c++};if(P.jgrid.isFunction(L.loadIndicator)?L.loadIndicator("show"):L.loadIndicator&&P(F).jqGrid("progressBar",{method:"show",loadtype:F.p.loadui,htmlcontent:P.jgrid.getRegional(F,"defaults.loadtext")}),P("sheets sheet",t.xl["workbook.xml"]).attr("name",L.sheetName),L.includeGroupHeader&&P(F).jqGrid("isGroupHeaderOn")){for(var
i,p=F.p.groupHeader,s=[],m=0,f=0;f<p.length;f++){var
u=p[f].groupHeaders,B={};for(m++,l=l=0;l<v.header.length;l++){B[i=v.header[l]]="";for(var
k,T,_=0;_<u.length;_++)u[_].startColumnName===i&&(B[i]=u[_].titleText,k=P.jgrid.excelCellPos(l)+m,T=P.jgrid.excelCellPos(l+u[_].numberOfColumns-1)+m,s.push({ref:k+":"+T}))}C(B,!0)}P("row c",g).attr("s","2");var
N=P.jgrid.makeNode(g,"mergeCells",{attr:{count:s.length}});for(P("worksheet",g).append(N),r=0;r<s.length;r++)N.appendChild(P.jgrid.makeNode(g,"mergeCell",{attr:s[r]}))}if(L.includeLabels&&(C(v.header,!0),P("row",g).last().find("c").attr("s","2")),L.includeHeader||F.p.headerrow){var
S=P(F).jqGrid("headerData","get");for(r
in
S)S.hasOwnProperty(r)&&(S[r]=P.jgrid.stripHtml(S[r]));P.isEmptyObject(S)||(C(S,!0),P("row",g).last().find("c").attr("s","2"))}if(F.p.grouping){e=!!F.p.groupingView._locgr;F.p.groupingView._locgr=!1,function(s){var
m=F.p.groupingView,f=[],u=m.groupField.length,c=b.length,g=0;function
y(e,t,r,l){for(var
o,a,d=function(e,t,r){var
l,o=!1;if(0===t)o=r[e];else{var
a=r[e].idx;if(0===a)o=r[e];else
for(l=e;0<=l;l--)if(r[l].idx===a-t){o=r[l];break}}return o}(e,t,r),n=d.cnt,i=h(v.header),p=l;p<c;p++)b[p]._expcol&&(a="{0}",P.each(d.summary,function(){if(this.nm===b[p].name){b[p].summaryTpl&&(a=b[p].summaryTpl),"string"==typeof
this.st&&"avg"===this.st.toLowerCase()&&(this.sd&&this.vd?this.v=this.v/this.vd:this.v&&0<n&&(this.v=this.v/n));try{this.groupCount=d.cnt,this.groupIndex=d.dataIndex,this.groupValue=d.value,o=this.v}catch(e){o=this.v}return i[this.nm]=P.jgrid.stripHtml(P.jgrid.template(a,o)),!1}}));return i}function
h(e){for(var
t={},r=0;r<e.length;r++)t[e[r]]="";return t}P.each(b,function(e,t){for(var
r=0;r<u;r++)if(m.groupField[r]===t.name){f[r]=e;break}});var
x,I=P.makeArray(m.groupSummary);if(I.reverse(),"local"===F.p.datatype&&!F.p.loadonce){P(F).jqGrid("groupingSetup");for(var
e=P.jgrid.getMethod("groupingPrepare"),t=0;t<v.body.length;t++)e.call(P(F),v.body[t],t)}P.each(m.groups,function(e,t){g++;try{x=Array.isArray(m.formatDisplayField)&&P.jgrid.isFunction(m.formatDisplayField[t.idx])?m.formatDisplayField[t.idx].call(F,t.displayValue,t.value,F.p.colModel[f[t.idx]],t.idx,m):F.formatter("",t.displayValue,f[t.idx],t.value)}catch(e){x=t.displayValue}var
r,l="";if("string"!=typeof(l=P.jgrid.isFunction(m.groupText[t.idx])?m.groupText[t.idx].call(F,x,t.cnt,t.summary):P.jgrid.template(m.groupText[t.idx],x,t.cnt,t.summary))&&"number"!=typeof
l&&(l=x),(r="header"===m.groupSummaryPos[t.idx]?y(e,0,m.groups,0):h(v.header))[Object.keys(r)[0]]=P.jgrid.stripHtml(new
Array(5*t.idx).join(" ")+l),C(r,!1),u-1===t.idx){for(var
o,a,d=m.groups[e+1],l=t.startRow,n=void
0!==d?d.startRow:m.groups[e].startRow+m.groups[e].cnt,i=l;i<n&&s[+i];i++){var
p=s[+i];C(p,!1)}if("header"!==m.groupSummaryPos[t.idx]){if(void
0!==d){for(a=0;a<m.groupField.length&&d.dataIndex!==m.groupField[a];a++);g=m.groupField.length-a}for(o=0;o<g;o++)I[o]&&(r=y(e,o,m.groups,0),C(r,!1));g=a}}})}(v.body),F.p.groupingView._locgr=e}else
for(var
A=0,H=v.body.length;A<H;A++)C(v.body[A],!1);if(L.includeFooter||F.p.footerrow){for(r
in
v.footer=P(F).jqGrid("footerData","get"),v.footer)v.footer.hasOwnProperty(r)&&(v.footer[r]=P.jgrid.stripHtml(v.footer[r]));P.isEmptyObject(v.footer)||(C(v.footer,!0),P("row",g).last().find("c").attr("s","2"))}var
q=P.jgrid.makeNode(g,"cols");for(P("worksheet",g).prepend(q),r=0,n=v.width.length;r<n;r++)q.appendChild(P.jgrid.makeNode(g,"col",{attr:{min:r+1,max:r+1,width:v.width[r],customWidth:1}}));P.jgrid.isFunction(L.onBeforeExport)&&L.onBeforeExport(t,c),v=null;try{var
D=new
JSZip,G={type:"blob",mimeType:L.mimetype};P.jgrid.xmlToZip(D,t),D.generateAsync?D.generateAsync(G).then(function(e){P.jgrid.saveAs(e,L.fileName,{type:L.mimetype})}):P.jgrid.saveAs(D.generate(G),L.fileName,{type:L.mimetype})}catch(e){throw e}finally{P.jgrid.isFunction(L.loadIndicator)?L.loadIndicator("hide"):L.loadIndicator&&P(F).jqGrid("progressBar",{method:"hide",loadtype:F.p.loadui})}})},exportToPdf:function(F){return F=P.extend(!0,{title:null,orientation:"portrait",pageSize:"A4",description:null,onBeforeExport:null,download:"download",includeLabels:!0,includeGroupHeader:!0,includeFooter:!0,includeHeader:!0,fileName:"jqGridExport.pdf",mimetype:"application/pdf",treeindent:"-",loadIndicator:!0},F||{}),this.each(function(){var
e,t,r,v=this,w=[],l=v.p.colModel,a={},d=v.p.treeGrid?P(v).jqGrid("getRowData",null,!0,F.treeindent):v.addLocalData(!0),j=[],n=0,i=[],o=[],p=[],C={};for(P.jgrid.isFunction(F.loadIndicator)?F.loadIndicator("show"):F.loadIndicator&&P(v).jqGrid("progressBar",{method:"show",loadtype:v.p.loadui,htmlcontent:P.jgrid.getRegional(v,"defaults.loadtext")}),e=0,y=l.length;e<y;e++)l[e]._expcol=!0,void
0===l[e].exportcol?l[e].hidden&&(l[e]._expcol=!1):l[e]._expcol=l[e].exportcol,"cb"!==l[e].name&&"rn"!==l[e].name&&"subgrid"!==l[e].name&&l[e]._expcol&&(a={text:v.p.colNames[e],style:"tableHeader"},o.push(a),j[n]=l[e].name,i[n]=e,p.push(l[e].width),C[l[e].name]=l[e].align||"left",n++);if(F.includeGroupHeader&&P(v).jqGrid("isGroupHeaderOn"))for(r=v.p.groupHeader,n=0;n<r.length;n++){for(var
s=[],m=r[n].groupHeaders,f=0;f<j.length;f++){for(a={text:"",style:"tableHeader"},t=0;t<m.length;t++)m[t].startColumnName===j[f]&&(a={text:m[t].titleText,colSpan:m[t].numberOfColumns,style:"tableHeader"});s.push(a),e++}w.push(s)}if(F.includeLabels&&w.push(o),F.includeHeader&&v.p.headerrow){var
u=P(v).jqGrid("headerData","get"),o=[];for(f=0;f<j.length;f++)a={text:P.jgrid.stripHtml(P.jgrid.getAccessor(u,j[f])),style:"tableFooter",alignment:C[j[f]]},o.push(a);w.push(o)}if(v.p.grouping){var
c=!!v.p.groupingView._locgr;v.p.groupingView._locgr=!1,function(s){var
m=v.p.groupingView,f=[],u=m.groupField.length,c=v.p.colModel,g=c.length,y=0;function
h(e,t){for(var
r=0,l=[],o=0;o<j.length;o++)a={text:null==e[j[o]]?"":t?P.jgrid.formatCell(e[j[o]]+"",i[r],d[n],c[i[r]],v,"pdf"):e[j[o]],alignment:C[o],style:"tableBody"},l.push(a),r++;return l}function
x(e,t,r,l){for(var
o,a,d=function(e,t,r){var
l,o=!1;if(0===t)o=r[e];else{var
a=r[e].idx;if(0===a)o=r[e];else
for(l=e;0<=l;l--)if(r[l].idx===a-t){o=r[l];break}}return o}(e,t,r),n=d.cnt,i=I(j),p=l;p<g;p++)c[p]._expcol&&(a="{0}",P.each(d.summary,function(){if(this.nm===c[p].name){c[p].summaryTpl&&(a=c[p].summaryTpl),"string"==typeof
this.st&&"avg"===this.st.toLowerCase()&&(this.sd&&this.vd?this.v=this.v/this.vd:this.v&&0<n&&(this.v=this.v/n));try{this.groupCount=d.cnt,this.groupIndex=d.dataIndex,this.groupValue=d.value,o=this.v}catch(e){o=this.v}return i[this.nm]=P.jgrid.stripHtml(P.jgrid.template(a,o)),!1}}));return i}function
I(e){for(var
t={},r=0;r<e.length;r++)t[e[r]]="";return t}P.each(c,function(e,t){for(var
r=0;r<u;r++)if(m.groupField[r]===t.name){f[r]=e;break}});var
F,b=P.makeArray(m.groupSummary);if(b.reverse(),"local"===v.p.datatype&&!v.p.loadonce){P(v).jqGrid("groupingSetup");for(var
e=P.jgrid.getMethod("groupingPrepare"),t=0;t<d.length;t++)e.call(P(v),d[t],t)}P.each(m.groups,function(e,t){y++;try{F=Array.isArray(m.formatDisplayField)&&P.jgrid.isFunction(m.formatDisplayField[t.idx])?m.formatDisplayField[t.idx].call(v,t.displayValue,t.value,v.p.colModel[f[t.idx]],t.idx,m):v.formatter("",t.displayValue,f[t.idx],t.value)}catch(e){F=t.displayValue}var
r,l="";if("string"!=typeof(l=P.jgrid.isFunction(m.groupText[t.idx])?m.groupText[t.idx].call(v,F,t.cnt,t.summary):P.jgrid.template(m.groupText[t.idx],F,t.cnt,t.summary))&&"number"!=typeof
l&&(l=F),(r="header"===m.groupSummaryPos[t.idx]?x(e,0,m.groups,0):I(j))[Object.keys(r)[0]]=P.jgrid.stripHtml(new
Array(5*t.idx).join(" ")+l),w.push(h(r,!0)),u-1===t.idx){for(var
o,a,d=m.groups[e+1],l=t.startRow,n=void
0!==d?d.startRow:m.groups[e].startRow+m.groups[e].cnt,i=l;i<n&&s[+i];i++){var
p=s[+i];w.push(h(p,!0))}if("header"!==m.groupSummaryPos[t.idx]){if(void
0!==d){for(a=0;a<m.groupField.length&&d.dataIndex!==m.groupField[a];a++);y=m.groupField.length-a}for(o=0;o<y;o++)b[o]&&(r=x(e,o,m.groups,0),w.push(h(r,!0)));y=a}}})}(d),v.p.groupingView._locgr=c}else
for(var
g,n=0,y=d.length;n<y;n++){for(t=0,o=[],g=d[n],f=0;f<j.length;f++)a={text:null==g[j[f]]?"":P.jgrid.stripHtml(P.jgrid.formatCell(P.jgrid.getAccessor(g,j[f])+"",i[t],d[n],l[i[t]],v,"pdf")),alignment:C[j[f]],style:"tableBody"},o.push(a),t++;w.push(o)}if(F.includeFooter&&v.p.footerrow){var
h=P(v).jqGrid("footerData","get");for(o=[],f=0;f<j.length;f++)a={text:P.jgrid.stripHtml(P.jgrid.getAccessor(h,j[f])),style:"tableFooter",alignment:C[j[f]]},o.push(a);w.push(o)}var
x={pageSize:F.pageSize,pageOrientation:F.orientation,content:[{style:"tableExample",widths:p,table:{headerRows:null!=r?0:1,body:w}}],styles:{tableHeader:{bold:!0,fontSize:11,color:"#2e6e9e",fillColor:"#dfeffc",alignment:"center"},tableBody:{fontSize:10},tableFooter:{bold:!0,fontSize:11,color:"#2e6e9e",fillColor:"#dfeffc"},title:{alignment:"center",fontSize:15},description:{}},defaultStyle:{fontSize:10}};F.description&&x.content.unshift({text:F.description,style:"description",margin:[0,0,0,12]}),F.title&&x.content.unshift({text:F.title,style:"title",margin:[0,0,0,12]}),P.jgrid.isFunction(F.onBeforeExport)&&F.onBeforeExport.call(v,x);try{var
I=pdfMake.createPdf(x);I.getDataUrl(function(e){P.jgrid.isFunction(F.loadIndicator)?F.loadIndicator("hide"):F.loadIndicator&&P(v).jqGrid("progressBar",{method:"hide",loadtype:v.p.loadui})}),"open"===F.download?I.open():I.getBuffer(function(e){P.jgrid.saveAs(e,F.fileName,{type:F.mimetype})})}catch(e){throw e}})},exportToHtml:function(p){var
s;return p=P.extend(!0,{title:"",onBeforeExport:null,includeLabels:!0,includeGroupHeader:!0,includeFooter:!0,includeHeader:!0,tableClass:"jqgridprint",autoPrint:!1,topText:"",bottomText:"",returnAsString:!1,treeindent:"&nbsp;",loadIndicator:!0},p||{}),this.each(function(){var
b=this,v=b.p.colModel,e=0,w={body:b.p.treeGrid?P(b).jqGrid("getRowData",null,!0,p.treeindent):b.addLocalData(!0),header:[],footer:[],width:[],map:[],align:[]},t=0;for(d=v.length;t<d;t++)v[t]._expcol=!0,void
0===v[t].exportcol?v[t].hidden&&(v[t]._expcol=!1):v[t]._expcol=v[t].exportcol,"cb"!==v[t].name&&"rn"!==v[t].name&&"subgrid"!==v[t].name&&v[t]._expcol&&(w.header[e]=v[t].name,w.width[e]=v[t].width,w.map[e]=t,w.align[e]=v[t].align||"left",e++);var
r=document.createElement("a"),l=function(e){r.href=e;e=r.host;return-1===e.indexOf("/")&&0!==r.pathname.indexOf("/")&&(e+="/"),r.protocol+"//"+e+r.pathname+r.search},j=function(e,t,r,l,o){for(var
a,d,n="<tr>",i=0,p=w.header.length;i<p&&(d=o?' colspan= "'+w.header.length+'" style=text-align:left':!0===l?" style=width:"+w.width[i]+"px;text-align:"+w.align[i]+";":" style=text-align:"+w.align[i]+";",a=w.header[i],e.hasOwnProperty(a)&&(n+="<"+t+d+">"+(r?P.jgrid.formatCell(P.jgrid.getAccessor(e,a),w.map[i],e,v[w.map[i]],b,"html"):e[a])+"</"+t+">"),!o);i++);return n+"</tr>"};P.jgrid.isFunction(p.loadIndicator)?p.loadIndicator("show"):p.loadIndicator&&P(b).jqGrid("progressBar",{method:"show",loadtype:b.p.loadui,htmlcontent:P.jgrid.getRegional(b,"defaults.loadtext")});var
o='<table class="'+p.tableClass+'">';if(p.includeLabels&&(o+="<thead>"+function(e,t,r){for(var
l="<tr>",o=0,a=e.length;o<a;o++)l+="<"+t+(!0===r?" style=width:"+w.width[o]+"px;":"")+">"+b.p.colNames[w.map[o]]+"</"+t+">";return l+"</tr>"}(w.header,"th",!0)+"</thead>"),o+="<tbody>",p.includeHeader&&b.p.headerrow&&(a=P(b).jqGrid("headerData","get",null,!1),o+=j(a,"td",!1)),b.p.grouping){var
a=!!b.p.groupingView._locgr;b.p.groupingView._locgr=!1,o+=function(m){var
f=b.p.groupingView,u=[],c=f.groupField.length,s=v.length,g=0,y="";function
h(e,t,r,l){for(var
o,a,d=function(e,t,r){var
l,o=!1;if(0===t)o=r[e];else{var
a=r[e].idx;if(0===a)o=r[e];else
for(l=e;0<=l;l--)if(r[l].idx===a-t){o=r[l];break}}return o}(e,t,r),n=d.cnt,i=x(w.header),p=l;p<s;p++)v[p]._expcol&&(a="{0}",P.each(d.summary,function(){if(this.nm===v[p].name){v[p].summaryTpl&&(a=v[p].summaryTpl),"string"==typeof
this.st&&"avg"===this.st.toLowerCase()&&(this.sd&&this.vd?this.v=this.v/this.vd:this.v&&0<n&&(this.v=this.v/n));try{this.groupCount=d.cnt,this.groupIndex=d.dataIndex,this.groupValue=d.value,o=this.v}catch(e){o=this.v}return i[this.nm]=P.jgrid.stripHtml(P.jgrid.template(a,o)),!1}}));return i}function
x(e){for(var
t={},r=0;r<e.length;r++)t[e[r]]="";return t}P.each(v,function(e,t){for(var
r=0;r<c;r++)if(f.groupField[r]===t.name){u[r]=e;break}});var
I,F=P.makeArray(f.groupSummary);if(F.reverse(),"local"===b.p.datatype&&!b.p.loadonce){P(b).jqGrid("groupingSetup");for(var
e=P.jgrid.getMethod("groupingPrepare"),t=0;t<w.body.length;t++)e.call(P(b),w.body[t],t)}return P.each(f.groups,function(e,t){g++;try{I=Array.isArray(f.formatDisplayField)&&P.jgrid.isFunction(f.formatDisplayField[t.idx])?f.formatDisplayField[t.idx].call(b,t.displayValue,t.value,b.p.colModel[u[t.idx]],t.idx,f):b.formatter("",t.displayValue,u[t.idx],t.value)}catch(e){I=t.displayValue}var
r="";"string"!=typeof(r=P.jgrid.isFunction(f.groupText[t.idx])?f.groupText[t.idx].call(b,I,t.cnt,t.summary):P.jgrid.template(f.groupText[t.idx],I,t.cnt,t.summary))&&"number"!=typeof
r&&(r=I);var
l,o=!1;if("header"===f.groupSummaryPos[t.idx]?l=h(e,0,f.groups,0):(l=x(w.header),o=!0),l[Object.keys(l)[0]]=new
Array(5*t.idx).join(" ")+r,y+=j(l,"td",!0,1===g,o),c-1===t.idx){for(var
a,d,n=f.groups[e+1],o=t.startRow,i=void
0!==n?n.startRow:f.groups[e].startRow+f.groups[e].cnt,p=o;p<i&&m[+p];p++){var
s=m[+p];y+=j(s,"td",!0)}if("header"!==f.groupSummaryPos[t.idx]){if(void
0!==n){for(d=0;d<f.groupField.length&&n.dataIndex!==f.groupField[d];d++);g=f.groupField.length-d}for(a=0;a<g;a++)F[a]&&(l=h(e,a,f.groups,0),y+=j(l,"td",!0));g=d}}}),y}(w.body),b.p.groupingView._locgr=a}else
for(var
e=0,d=w.body.length;e<d;e++)o+=j(w.body[e],"td",!0,0===e);if(p.includeFooter&&b.p.footerrow&&(w.footer=P(b).jqGrid("footerData","get",null,!1),o+=j(w.footer,"td",!1)),o+="</tbody>",o+="</table>",p.returnAsString)s=o;else{var
n=window.open("","");n.document.close();var
i=p.title?"<title>"+p.title+"</title>":"";P("style, link").each(function(){i+=function(e){e=P(e).clone()[0];return"link"===e.nodeName.toLowerCase()&&(e.href=l(e.href)),e.outerHTML}(this)});try{n.document.head.innerHTML=i}catch(e){P(n.document.head).html(i)}n.document.body.innerHTML=(p.title?"<h1>"+p.title+"</h1>":"")+"<div>"+(p.topText||"")+"</div>"+o+"<div>"+(p.bottomText||"")+"</div>",P(n.document.body).addClass("html-view"),P("img",n.document.body).each(function(e,t){t.setAttribute("src",l(t.getAttribute("src")))}),p.onBeforeExport&&p.onBeforeExport(n),Boolean(n.chrome)?p.autoPrint&&(n.print(),n.close()):setTimeout(function(){p.autoPrint&&(n.print(),n.close())},1e3)}P.jgrid.isFunction(p.loadIndicator)?p.loadIndicator("hide"):p.loadIndicator&&P(b).jqGrid("progressBar",{method:"hide",loadtype:b.p.loadui})}),s}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery"],e):e(jQuery)}(function($){"use strict";$.extend($.jgrid,{isJSON:function(e){"string"!=typeof
e&&(e=JSON.stringify(e));try{return JSON.parse(e),!0}catch(e){return!1}},stringify:function(e){return JSON.stringify(e,function(e,t){return"function"==typeof
t?t.toString():t})},parseFunc:function(str){return JSON.parse(str,function(key,value){if("string"!=typeof
value||-1===value.indexOf("function"))return value;var
sv=value.split(" ");return sv[0]=$.jgrid.trim(sv[0].toLowerCase()),0===sv[0].indexOf("function")&&"}"===value.trim().slice(-1)?eval("("+value+")"):value})},encode:function(e){return String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")},jsonToXML:function(e,t){function
c(e,t){return"#text"===e?s.encode?d.encode(t):t:"function"==typeof
t?"<"+e+"><![CDATA["+t+"]]></"+e+">\n":""===t?"<"+e+">__EMPTY_STRING_</"+e+">\n":"<"+e+">"+(s.encode?d.encode(t):t)+"</"+e+">\n"}var
s=$.extend({xmlDecl:'<?xml version="1.0" encoding="UTF-8" ?>\n',attr_prefix:"-",encode:!0},t||{}),d=this,u=function(e,t){for(var
n=[],o=0;o<t.length;o++){var
r=t[o];void
0===r||null==r?n[n.length]="<"+e+" />":"object"==typeof
r&&r.constructor==Array?n[n.length]=u(e,r):n[n.length]=("object"==typeof
r?f:c)(e,r)}return n.length||(n[0]="<"+e+">__EMPTY_ARRAY_</"+e+">\n"),n.join("")},f=function(e,t){var
n,o,r=[],a=[];for(n
in
t)t.hasOwnProperty(n)&&(o=t[n],n.charAt(0)!==s.attr_prefix?null==o?r[r.length]="<"+n+" />":"object"==typeof
o&&o.constructor===Array?r[r.length]=u(n,o):r[r.length]=("object"==typeof
o?f:c)(n,o):a[a.length]=" "+n.substring(1)+'="'+(s.encode?d.encode(o):o)+'"');var
i=a.join(""),l=r.join("");return null==e||(l=0<r.length?l.match(/\n/)?"<"+e+i+">\n"+l+"</"+e+">\n":"<"+e+i+">"+l+"</"+e+">\n":"<"+e+i+" />\n"),l},e=f(null,e);return s.xmlDecl+e},xmlToJSON:function(root,options){var
o=$.extend({force_array:[],attr_prefix:"-"},options||{});if(root){var
__force_array={};if(o.force_array)for(var
i=0;i<o.force_array.length;i++)__force_array[o.force_array[i]]=1;"string"==typeof
root&&(root=$.parseXML(root)),root.documentElement&&(root=root.documentElement);var
addNode=function(hash,key,cnts,val){if("string"==typeof
val)if(-1!==val.indexOf("function"))val=eval("("+val+")");else
switch(val){case"__EMPTY_ARRAY_":val=[];break;case"__EMPTY_STRING_":val="";break;case"false":val=!1;break;case"true":val=!0}__force_array[key]?(1===cnts&&(hash[key]=[]),hash[key][hash[key].length]=val):1===cnts?hash[key]=val:2===cnts?hash[key]=[hash[key],val]:hash[key][hash[key].length]=val},parseElement=function(e){if(7!==e.nodeType){if(3===e.nodeType||4===e.nodeType)return null==e.nodeValue.match(/[^\x00-\x20]/)?void
0:e.nodeValue;var
t,n,r,a={};if(e.attributes&&e.attributes.length)for(t={},l=0;l<e.attributes.length;l++)"string"==typeof(n=e.attributes[l].nodeName)&&(r=e.attributes[l].nodeValue)&&(void
0===a[n=o.attr_prefix+n]&&(a[n]=0),a[n]++,addNode(t,n,a[n],r));if(e.childNodes&&e.childNodes.length){for(var
i=t?!1:!0,l=0;l<e.childNodes.length&&i;l++){var
c=e.childNodes[l].nodeType;3!==c&&4!==c&&(i=!1)}if(i)for(t=t||"",l=0;l<e.childNodes.length;l++)t+=e.childNodes[l].nodeValue;else
for(t=t||{},l=0;l<e.childNodes.length;l++)"string"==typeof(n=e.childNodes[l].nodeName)&&(r=parseElement(e.childNodes[l]))&&(void
0===a[n]&&(a[n]=0),a[n]++,addNode(t,n,a[n],r))}return t}},json=parseElement(root),tmp;return __force_array[root.nodeName]&&(json=[json]),11!==root.nodeType&&(tmp={},tmp[root.nodeName]=json,json=tmp),json}},saveAs:function(e,t,n){n=$.extend(!0,{type:"plain/text;charset=utf-8"},n||{});var
o,r,a,i=[];t=null==t||""===t?"jqGridFile.txt":t,Array.isArray(e)?i=e:i[0]=e;try{o=new
File(i,t,n)}catch(e){o=new
Blob(i,n)}window.navigator&&window.navigator.msSaveOrOpenBlob?window.navigator.msSaveOrOpenBlob(o,t):(r=URL.createObjectURL(o),(a=document.createElement("a")).href=r,a.download=t,document.body.appendChild(a),a.click(),setTimeout(function(){document.body.removeChild(a),window.URL.revokeObjectURL(r)},0))}})});!function(e){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base"],e):e(jQuery)}(function(u){"use strict";u.extend(u.jgrid,{focusableElementsList:[">a[href]",">button:not([disabled])",">area[href]",">input:not([disabled])",">select:not([disabled])",">textarea:not([disabled])",">iframe",">object",">embed",">*[tabindex]",">*[contenteditable]"]}),u.jgrid.extend({ariaBodyGrid:function(e){var
f=u.extend({onEnterCell:null},e||{});return this.each(function(){var
a=this,i=u.jgrid.getMethod("getStyleUI"),d=i(a.p.styleUI+".common","highlight",!0);function
o(e,t){return!isNaN(e)&&!isNaN(t)&&0<=e&&0<=t&&a.rows.length&&e<a.rows.length&&t<a.p.colModel.length}function
r(e,t){var
i=a.p.iRow+t,r=a.p.iCol+e,n=a.rows.length,t=0!==e;if(!n)return!1;e=a.p.colModel.length;return t&&(r<0&&2<=i&&(r=e-1,i--),e<=r&&(r=0,i++)),t||(i<1?(r--,i=n-1,a.rows[i]&&0<=r&&!a.rows[i].cells[r]&&i--):(n<=i||!a.rows[i].cells[r])&&(i=1,r++)),o(i,r)?{row:i,col:r}:!!o(a.p.iRow,a.p.iCol)&&{row:a.p.iRow,col:a.p.iCol}}function
n(e,t){var
i=r(e,t);if(!i)return!1;for(;u(a.rows[i.row].cells[i.col]).is(":hidden");)if(a.p.iRow=i.row,a.p.iCol=i.col,i=r(e,t),a.p.iRow===i.row&&a.p.iCol===i.col)return!1;return 0!==t&&u(a).jqGrid("setSelection",a.rows[i.row].id,!1,null,!1),i}function
l(e){var
t=a.p.page,i=a.p.lastpage;(t+=e)<=0&&(t=1),i<t&&(t=i),a.p.page!==t&&(a.p.page=t,a.grid.populate())}var
t=u.jgrid.focusableElementsList.join();u(a).removeAttr("tabindex"),u(a).on("jqGridAfterGridComplete.setAriaGrid",function(e){u("tbody",a).first().find(">tr:not(.jqgfirstrow)>td:not(:hidden, :has("+t+"))").attr("tabindex",-1),u("tbody",a).first().find(">tr:not(.jqgfirstrow)").removeAttr("tabindex"),void
0!==a.p.iRow&&void
0!==a.p.iCol&&a.rows[a.p.iRow]&&u(a.rows[a.p.iRow].cells[a.p.iCol]).attr("tabindex",0).focus(function(){u(this).addClass(d)}).blur(function(){u(this).removeClass(d)})}),a.p.iRow=1,a.p.iCol=u.jgrid.getFirstVisibleCol(a);var
s=0,c=0;u(a).on("keydown",function(e){if(!a.p.navigationDisabled||!0!==a.p.navigationDisabled){var
t;switch(e.which||e.keyCode){case
38:t=n(0,-1),s=t.row,c=t.col,e.preventDefault();break;case
40:t=n(0,1),s=t.row,c=t.col,e.preventDefault();break;case
37:t=n(-1,0),s=t.row,c=t.col,e.preventDefault();break;case
39:t=n(1,0),s=t.row,c=t.col,e.preventDefault();break;case
36:s=e.ctrlKey?1:a.p.iRow,c=0,e.preventDefault();break;case
35:s=e.ctrlKey?a.rows.length-1:a.p.iRow,c=a.p.colModel.length-1,e.preventDefault();break;case
33:l(-1),c=a.p.iCol,s=a.p.iRow,e.preventDefault();break;case
34:l(1),c=a.p.iCol,(s=a.p.iRow)>a.rows.length-1&&(s=a.rows.length-1,a.p.iRow=a.rows.length-1),e.preventDefault();break;case
13:return void(u.jgrid.isFunction(f.onEnterCell)&&(f.onEnterCell.call(a,a.rows[a.p.iRow].id,a.p.iRow,a.p.iCol,e),e.preventDefault()));default:return}u(a).jqGrid("focusBodyCell",s,c,i,d)}}),u(a).on("jqGridBeforeSelectRow.ariaGridClick",function(){return!1}),u(a).on("jqGridCellSelect.ariaGridClick",function(e,t,i,r,n){var
o=n.target;0<a.p.iRow&&0<=a.p.iCol&&u(a.rows[a.p.iRow].cells[a.p.iCol]).attr("tabindex",-1),(u(o).is("td")||u(o).is("th"))&&(a.p.iCol=o.cellIndex,n=u(o).closest("tr.jqgrow"),a.p.iRow=n[0].rowIndex,u(o).attr("tabindex",0).addClass(d).focus().blur(function(){u(this).removeClass(d)}))})})},focusBodyCell:function(a,d,l,s){return this.each(function(){var
e,t=this,i=l||u.jgrid.getMethod("getStyleUI"),r=s||i(t.p.styleUI+".common","highlight",!0),n=u.jgrid.focusableElementsList.join();function
o(e){return u(n,e)[0]}void
0!==a&&void
0!==d?!isNaN(t.p.iRow)&&!isNaN(t.p.iCol)&&0<=t.p.iCol&&(e=o(t.rows[t.p.iRow].cells[t.p.iCol]),u(e||t.rows[t.p.iRow].cells[t.p.iCol]).attr("tabindex",-1)):(a=t.p.iRow,d=t.p.iCol),a=parseInt(a,10),d=parseInt(d,10),0<a&&0<=d&&(e=o(t.rows[a].cells[d]),u(e||t.rows[a].cells[d]).attr("tabindex",0).addClass(r).focus().blur(function(){u(this).removeClass(r)}),t.p.iRow=a,t.p.iCol=d)})},resetAriaBody:function(){return this.each(function(){var
e=this;u(e).attr("tabindex","0").off("keydown").off("jqGridBeforeSelectRow.ariaGridClick").off("jqGridCellSelect.ariaGridClick").off("jqGridAfterGridComplete.setAriaGrid");var
t=u.jgrid.focusableElementsList.join();u("tbody",e).first().find(">tr:not(.jqgfirstrow)>td:not(:hidden, :has("+t+"))").removeAttr("tabindex").off("focus"),u("tbody",e).first().find(">tr:not(.jqgfirstrow)").attr("tabindex",-1)})},ariaHeaderGrid:function(){return this.each(function(){var
n=this,e=u.jgrid.getMethod("getStyleUI")(n.p.styleUI+".common","highlight",!0),t=u("#gbox_"+n.p.id).find(".ui-jqgrid-hbox>table").first();u("tr.ui-jqgrid-labels",t).on("keydown",function(e){var
t=n.p.selHeadInd,i=e.which||e.keyCode,r=n.grid.headers.length;switch(i){case
37:if(0<=t-1){for(t--;u(n.grid.headers[t].el).is(":hidden")&&0<=t-1&&!(--t<0););0<=t&&(u(n.grid.headers[t].el).focus(),u(n.grid.headers[n.p.selHeadInd].el).attr("tabindex","-1"),n.p.selHeadInd=t,e.preventDefault())}break;case
39:if(t+1<r){for(t++;u(n.grid.headers[t].el).is(":hidden")&&t+1<r&&!(r-1<++t););t<r&&(u(n.grid.headers[t].el).focus(),u(n.grid.headers[n.p.selHeadInd].el).attr("tabindex","-1"),n.p.selHeadInd=t,e.preventDefault())}break;case
13:u(n.grid.headers[t].el).find("div").first().trigger("click"),e.preventDefault();break;default:return}}),u("tr.ui-jqgrid-labels>th:not(:hidden)",t).attr("tabindex",-1).focus(function(){u(this).addClass(e).attr("tabindex","0")}).blur(function(){u(this).removeClass(e)}),n.p.selHeadInd=u.jgrid.getFirstVisibleCol(n),u(n.grid.headers[n.p.selHeadInd].el).attr("tabindex","0")})},focusHeaderCell:function(t){return this.each(function(){var
e=this;void
0===t&&(t=e.p.selHeadInd),0<=t&&t<e.p.colModel.length&&(u(e.grid.headers[e.p.selHeadInd].el).attr("tabindex","-1"),u(e.grid.headers[t].el).focus(),e.p.selHeadInd=t)})},resetAriaHeader:function(){return this.each(function(){var
e=u("#gbox_"+this.p.id).find(".ui-jqgrid-hbox>table").first();u("tr.ui-jqgrid-labels",e).off("keydown"),u("tr.ui-jqgrid-labels>th:not(:hidden)",e).removeAttr("tabindex").off("focus blur")})},ariaPagerGrid:function(){return this.each(function(){var
r=this,e=u.jgrid.getMethod("getStyleUI"),t=e(r.p.styleUI+".common","highlight",!0),n="."+e(r.p.styleUI+".common","disabled",!0),o=u(".ui-pg-button",r.p.pager),a=o.length;o.attr("tabindex","-1").focus(function(){u(this).addClass(t)}).blur(function(){u(this).removeClass(t)}),r.p.navIndex=0,setTimeout(function(){var
e=o.not(n).first().attr("tabindex","0");r.p.navIndex=e[0].cellIndex?e[0].cellIndex-1:0},100),u(r.p.pager).find("table.ui-pager-table tr").first().on("keydown",function(e){var
t=e.which||e.keyCode,i=r.p.navIndex;switch(t){case
37:if(0<=i-1){for(i--;u(o[i]).is(n)&&0<=i-1&&!(--i<0););0<=i&&(u(o[r.p.navIndex]).attr("tabindex","-1"),u(o[i]).attr("tabindex","0").focus(),r.p.navIndex=i),e.preventDefault()}break;case
39:if(i+1<a){for(i++;u(o[i]).is(n)&&i+1<a+1&&!(a-1<++i););i<a&&(u(o[r.p.navIndex]).attr("tabindex","-1"),u(o[i]).attr("tabindex","0").focus(),r.p.navIndex=i),e.preventDefault()}break;case
13:u(o[i]).trigger("click"),e.preventDefault();break;default:return}})})},focusPagerCell:function(i){return this.each(function(){var
e=u(".ui-pg-button",this.p.pager),t=e.length;void
0===i&&(i=this.p.navIndex),0<=i&&i<t&&(u(e[this.p.navIndex]).attr("tabindex","-1"),u(e[i]).attr("tabindex","0").focus(),this.p.navIndex=i)})},resetAriaPager:function(){return this.each(function(){u(".ui-pg-button",this.p.pager).removeAttr("tabindex").off("focus"),u(this.p.pager).find("table.ui-pager-table tr").first().off("keydown")})},setAriaGrid:function(e){var
t=u.extend({header:!0,body:!0,pager:!0,onEnterCell:null},e||{});return this.each(function(){t.header&&u(this).jqGrid("ariaHeaderGrid"),t.body&&u(this).jqGrid("ariaBodyGrid",{onEnterCell:t.onEnterCell}),t.pager&&u(this).jqGrid("ariaPagerGrid")})},resetAriaGrid:function(e){var
t=u.extend({header:!0,body:!0,pager:!0},e||{});return this.each(function(){t.body&&u(this).jqGrid("resetAriaBody"),t.header&&u(this).jqGrid("resetAriaHeader"),t.pager&&u(this).jqGrid("resetAriaPager")})}})});!function(t){"use strict";"function"==typeof
define&&define.amd?define(["jquery","./grid.base","jquery-ui/dialog","jquery-ui/draggable","jquery-ui/droppable","jquery-ui/resizable","jquery-ui/sortable","./addons/ui.multiselect"],t):t(jQuery)}(function($){"use strict";var
setSelected;$.jgrid.msie()&&8===$.jgrid.msiever()&&($.expr[":"].hidden=function(t){return 0===t.offsetWidth||0===t.offsetHeight||"none"===t.style.display}),$.jgrid._multiselect=!1,$.ui&&$.ui.multiselect&&($.ui.multiselect.prototype._setSelected&&(setSelected=$.ui.multiselect.prototype._setSelected,$.ui.multiselect.prototype._setSelected=function(t,e){var
i,t=setSelected.call(this,t,e);return e&&this.selectedList&&(i=this.element,this.selectedList.find("li").each(function(){$(this).data("optionLink")&&$(this).data("optionLink").remove().appendTo(i)})),t}),$.ui.multiselect.prototype.destroy&&($.ui.multiselect.prototype.destroy=function(){this.element.show(),this.container.remove(),(void
0===$.Widget?$.widget:$.Widget).prototype.destroy.apply(this,arguments)}),$.jgrid._multiselect=!0),$.jgrid.extend({sortableColumns:function(r){return this.each(function(){var
d=this,t=$.jgrid.jqID(d.p.id),e=!1;function
i(){d.p.disableClick=!0,d.p.frozenColumns&&($(d).jqGrid("destroyFrozenColumns"),e=!0)}function
s(){setTimeout(function(){d.p.disableClick=!1,e&&($(d).jqGrid("setFrozenColumns"),e=!1)},50)}var
o,a,t={tolerance:"pointer",axis:"x",scrollSensitivity:"1",items:">th:not(:has(#jqgh_"+t+"_cb,#jqgh_"+t+"_rn,#jqgh_"+t+"_subgrid),:hidden)",cancel:".sortable-disabled",placeholder:{element:function(t){return $(document.createElement(t[0].nodeName)).addClass(t[0].className+" ui-sortable-placeholder ui-state-highlight").removeClass("ui-sortable-helper")[0]},update:function(t,e){e.height(t.currentItem.innerHeight()-parseInt(t.currentItem.css("paddingTop")||0,10)-parseInt(t.currentItem.css("paddingBottom")||0,10)),e.width(t.currentItem.innerWidth()-parseInt(t.currentItem.css("paddingLeft")||0,10)-parseInt(t.currentItem.css("paddingRight")||0,10))}},update:function(t,e){var
i=$(e.item).parent(),e=$(">th",i),i=d.p.colModel,s={},o=d.p.id+"_";$.each(i,function(t){s[this.name]=t});var
a=[];e.each(function(){var
t=$(">div",this).get(0).id.replace(/^jqgh_/,"").replace(o,"");s.hasOwnProperty(t)&&a.push(s[t])}),$(d).jqGrid("remapColumns",a,!0,!0),$.jgrid.isFunction(d.p.sortable.update)&&d.p.sortable.update(a)}};d.p.sortable.options?$.extend(t,d.p.sortable.options):$.jgrid.isFunction(d.p.sortable)&&(d.p.sortable={update:d.p.sortable}),t.start?(o=t.start,t.start=function(t,e){i(),o.call(this,t,e)}):t.start=i,t.stop?(a=t.stop,t.stop=function(t,e){s(),a.call(this,t,e)}):t.stop=s,d.p.sortable.exclude&&(t.items+=":not("+d.p.sortable.exclude+")");t=r.sortable(t),t=t.data("sortable")||t.data("uiSortable");null!=t&&(t.data("sortable").floating=!0)})},columnChooser:function(e){var
i,s,t,o=this,d={},a=[],r=o.jqGrid("getGridParam","colModel"),n=o.jqGrid("getGridParam","colNames"),l=function(t){return $.ui.multiselect.prototype&&t.data($.ui.multiselect.prototype.widgetFullName||$.ui.multiselect.prototype.widgetName)||t.data("ui-multiselect")||t.data("multiselect")},c=$.jgrid.getRegional(this[0],"col");if(!$("#colchooser_"+$.jgrid.jqID(o[0].p.id)).length){if(i=$('<div id="colchooser_'+o[0].p.id+'" style="position:relative;overflow:hidden"><div><select multiple="multiple"></select></div></div>'),s=$("select",i),e=$.extend({width:400,height:240,classname:null,done:function(t){t&&o.jqGrid("remapColumns",t,!0)},msel:"multiselect",dlog:"dialog",dialog_opts:{minWidth:470,dialogClass:"ui-jqdialog"},dlog_opts:function(t){var
e={};return e[t.bSubmit]=function(){t.apply_perm(),t.cleanup(!1)},e[t.bCancel]=function(){t.cleanup(!0)},$.extend(!0,{buttons:e,close:function(){t.cleanup(!0)},modal:t.modal||!1,resizable:t.resizable||!0,width:t.width+70,resize:u},t.dialog_opts||{})},apply_perm:function(){var
a=[];$("option",s).each(function(){$(this).is(":selected")?o.jqGrid("showCol",r[this.value].name):o.jqGrid("hideCol",r[this.value].name)}),$("option[selected]",s).each(function(){a.push(parseInt(this.value,10))}),$.each(a,function(){delete
d[r[parseInt(this,10)].name]}),$.each(d,function(){var
t,e,i,s,o=parseInt(this,10);t=a,i=e=o,a=0<=e?(o=(s=t.slice()).splice(e,Math.max(t.length-e,e)),e>t.length&&(e=t.length),s[e]=i,s.concat(o)):t}),e.done&&e.done.call(o,a),o.jqGrid("setGridWidth",o[0].p.width,o[0].p.shrinkToFit)},cleanup:function(t){p(e.dlog,i,"destroy"),p(e.msel,s,"destroy"),i.remove(),t&&e.done&&e.done.call(o)},msel_opts:{}},c,e||{}),$.ui&&$.ui.multiselect&&$.ui.multiselect.defaults){if(!$.jgrid._multiselect)return void
alert("Multiselect plugin loaded after jqGrid. Please load the plugin before the jqGrid!");e.msel_opts=$.extend($.ui.multiselect.defaults,e.msel_opts)}e.caption&&i.attr("title",e.caption),e.classname&&(i.addClass(e.classname),s.addClass(e.classname)),e.width&&($(">div",i).css({width:e.width,margin:"0 auto"}),s.css("width",e.width)),e.height&&($(">div",i).css("height",e.height),s.css("height",e.height-10)),s.empty(),$.each(r,function(t){d[this.name]=t,this.hidedlg?this.hidden||a.push(t):s.append("<option value='"+t+"' "+(this.hidden?"":"selected='selected'")+">"+$.jgrid.stripHtml(n[t])+"</option>")}),t=$.jgrid.isFunction(e.dlog_opts)?e.dlog_opts.call(o,e):e.dlog_opts,p(e.dlog,i,t),c=$.jgrid.isFunction(e.msel_opts)?e.msel_opts.call(o,e):e.msel_opts,p(e.msel,s,c),t=$("#colchooser_"+$.jgrid.jqID(o[0].p.id));var
c=$(".ui-jqgrid").css("font-size")||"11px";t.parent().css("font-size",c),t.css({margin:"auto"}),t.find(">div").css({width:"100%",height:"100%",margin:"auto"}),(c=l(s)).container.css({width:"100%",height:"100%",margin:"auto"}),c.selectedContainer.css({width:100*c.options.dividerLocation+"%",height:"100%",margin:"auto",boxSizing:"border-box"}),c.availableContainer.css({width:100-100*c.options.dividerLocation+"%",height:"100%",margin:"auto",boxSizing:"border-box"}),c.selectedList.css("height","auto"),c.availableList.css("height","auto"),t=Math.max(c.selectedList.height(),c.availableList.height()),t=Math.min(t,$(window).height()),c.selectedList.css("height",t),c.availableList.css("height",t),u()}function
p(t,e){t&&("string"==typeof
t?$.fn[t]&&$.fn[t].apply(e,$.makeArray(arguments).slice(2)):$.jgrid.isFunction(t)&&t.apply(e,$.makeArray(arguments).slice(2)))}function
u(){var
t=l(s),e=t.container.closest(".ui-dialog-content");0<e.length&&"object"==typeof
e[0].style?e[0].style.width="":e.css("width",""),t.selectedList.height(Math.max(t.selectedContainer.height()-t.selectedActions.outerHeight()-1,1)),t.availableList.height(Math.max(t.availableContainer.height()-t.availableActions.outerHeight()-1,1))}},sortableRows:function(o){return this.each(function(){var
s=this;s.grid&&(s.p.treeGrid||$.fn.sortable&&((o=$.extend({cursor:"move",axis:"y",items:" > .jqgrow"},o||{})).start&&$.jgrid.isFunction(o.start)?(o._start_=o.start,delete
o.start):o._start_=!1,o.update&&$.jgrid.isFunction(o.update)?(o._update_=o.update,delete
o.update):o._update_=!1,o.start=function(t,e){if($(e.item).css("border-width","0"),$("td",e.item).each(function(t){this.style.width=s.grid.cols[t].style.width}),s.p.subGrid){var
i=$(e.item).attr("id");try{$(s).jqGrid("collapseSubGridRow",i)}catch(t){}}o._start_&&o._start_.apply(this,[t,e])},o.update=function(t,e){$(e.item).css("border-width",""),!0===s.p.rownumbers&&$("td.jqgrid-rownum",s.rows).each(function(t){$(this).html(t+1+(parseInt(s.p.page,10)-1)*parseInt(s.p.rowNum,10))}),o._update_&&o._update_.apply(this,[t,e])},$(s).find("tbody").first().sortable(o),$("tbody",s).first().find(" > .jqgrow").disableSelection()))})},gridDnD:function(s){return this.each(function(){var
t,e,a=this;if(a.grid&&!a.p.treeGrid&&$.fn.draggable&&$.fn.droppable){var
h;if(void
0===$("#jqgrid_dnd")[0]&&$("body").append("<table id='jqgrid_dnd' class='ui-jqgrid-dnd'></table>"),"string"!=typeof
s||"updateDnD"!==s||!0!==a.p.jqgdnd){if((s=$.extend({drag:function(o){return $.extend({start:function(t,e){var
i,s;if(a.p.subGrid){s=$(e.helper).attr("id");try{$(a).jqGrid("collapseSubGridRow",s)}catch(t){}}for(i=0;i<$.data(a,"dnd").connectWith.length;i++)0===$($.data(a,"dnd").connectWith[i]).jqGrid("getGridParam","reccount")&&$($.data(a,"dnd").connectWith[i]).jqGrid("addRowData","jqg_empty_row",{});e.helper.addClass("ui-state-highlight"),$("td",e.helper).each(function(t){this.style.width=a.grid.headers[t].width+"px"}),o.onstart&&$.jgrid.isFunction(o.onstart)&&o.onstart.call($(a),t,e)},stop:function(t,e){var
i,s;for(e.helper.dropped&&!o.dragcopy&&(void
0===(s=$(e.helper).attr("id"))&&(s=$(this).attr("id")),$(a).jqGrid("delRowData",s)),i=0;i<$.data(a,"dnd").connectWith.length;i++)$($.data(a,"dnd").connectWith[i]).jqGrid("delRowData","jqg_empty_row");o.onstop&&$.jgrid.isFunction(o.onstop)&&o.onstop.call($(a),t,e)}},o.drag_opts||{})},drop:function(u){return $.extend({accept:function(t){if(!$(t).hasClass("jqgrow"))return t;h=$(t).closest("table.ui-jqgrid-btable");var
e=$(this).find("table.ui-jqgrid-btable").first()[0];if(0<h.length&&void
0!==$.data(h[0],"dnd")){t=$.data(h[0],"dnd").connectWith;return-1!==$.inArray("#"+$.jgrid.jqID(e.id),t)}return!1},drop:function(t,e){if($(e.draggable).hasClass("jqgrow")){var
i,s=$(e.draggable).attr("id"),o=e.draggable.parent().parent().jqGrid("getRowData",s),a=[],d=$(this).find("table.ui-jqgrid-btable").first()[0];if($.isPlainObject(o)&&(a=Object.keys(o)),!u.dropbyname){var
r,n,l={},c=0,p=$("#"+$.jgrid.jqID(d.id)).jqGrid("getGridParam","colModel");try{for(r=0;r<p.length;r++)"cb"!==(n=p[r].name)&&"rn"!==n&&"subgrid"!==n&&(void
0!==a[c]&&(l[n]=o[a[c]]),c++);o=l}catch(t){}}e.helper.dropped=!0,$.data(h[0],"dnd").beforedrop&&$.jgrid.isFunction($.data(h[0],"dnd").beforedrop)&&(null!=(s=$.data(h[0],"dnd").beforedrop.call(d,t,e,o,$(h[0]),$(d)))&&"object"==typeof
s&&(o=s)),e.helper.dropped&&(u.autoid&&(i=$.jgrid.isFunction(u.autoid)?u.autoid.call(d,o):(i=Math.ceil(1e3*Math.random()),u.autoidprefix+i)),$("#"+$.jgrid.jqID(d.id)).jqGrid("addRowData",i,o,u.droppos)),u.ondrop&&$.jgrid.isFunction(u.ondrop)&&u.ondrop.call(d,t,e,o)}}},u.drop_opts||{})},onstart:null,onstop:null,beforedrop:null,ondrop:null,drop_opts:{activeClass:"ui-state-active",hoverClass:"ui-state-hover",tolerance:"intersect"},drag_opts:{revert:"invalid",helper:"clone",cursor:"move",appendTo:"#jqgrid_dnd",zIndex:5e3},dragcopy:!1,dropbyname:!1,droppos:"first",autoid:!0,autoidprefix:"dnd_"},s||{})).connectWith)for(s.connectWith=s.connectWith.split(","),s.connectWith=$.map(s.connectWith,function(t){return $.jgrid.trim(t)}),$.data(a,"dnd",s),0===a.p.reccount||a.p.jqgdnd||i(),a.p.jqgdnd=!0,t=0;t<s.connectWith.length;t++)e=s.connectWith[t],$(e).closest(".ui-jqgrid-bdiv").droppable($.jgrid.isFunction(s.drop)?s.drop.call($(a),s):s.drop)}else
i()}function
i(){var
t=$.data(a,"dnd");$("tr.jqgrow:not(.ui-draggable)",a).draggable($.jgrid.isFunction(t.drag)?t.drag.call($(a),t):t.drag)}})},gridResize:function(opts){return this.each(function(){var
$t=this,gID=$.jgrid.jqID($t.p.id),req,class_to_add,test,optstest;$t.grid&&$.fn.resizable&&(opts=$.extend({},{resizeclass:"ui-resizable-icon"},opts||{}),opts.alsoResize?(opts._alsoResize_=opts.alsoResize,delete
opts.alsoResize):opts._alsoResize_=!1,opts.stop&&$.jgrid.isFunction(opts.stop)?(opts._stop_=opts.stop,delete
opts.stop):opts._stop_=!1,class_to_add=opts.resizeclass,"jQueryUI"!==$t.p.styleUI&&(opts.handles?opts.handles.se?class_to_add+=" "+opts.handles.se.replace(".",""):opts.handles.se="."+class_to_add:(opts.handles={},test=class_to_add.split(" "),opts.handles.se="."+test[0]),class_to_add+=" ui-resizable-se ui-resizable-handle",$("#gbox_"+gID).append('<span class="'+class_to_add+'"></span>')),opts.stop=function(t,e){$($t).jqGrid("setGridParam",{height:$("#gview_"+gID+" .ui-jqgrid-bdiv").height()}),$($t).jqGrid("setGridWidth",e.size.width,opts.shrinkToFit),opts._stop_&&opts._stop_.call($t,t,e),$t.p.caption&&$("#gbox_"+gID).css({height:"auto"}),$t.p.frozenColumns&&(req&&clearTimeout(req),req=setTimeout(function(){req&&clearTimeout(req),$("#"+gID).jqGrid("destroyFrozenColumns"),$("#"+gID).jqGrid("setFrozenColumns")}))},opts._alsoResize_?(optstest="{'#gview_"+gID+" .ui-jqgrid-bdiv':true,'"+opts._alsoResize_+"':true}",opts.alsoResize=eval("("+optstest+")")):opts.alsoResize=$(".ui-jqgrid-bdiv","#gview_"+gID),delete
opts._alsoResize_,$("#gbox_"+gID).resizable(opts))})}})});!function(t){"use strict";"function"==typeof
define&&define.amd?define(["jquery"],t):t(jQuery)}(function(s){"use strict";var
d,o=s();s.fn.html5sortable=function(a){var
t=String(a);return a=s.extend({connectWith:!1},a),this.each(function(){var
e,n,i;if(/^enable|disable|destroy$/.test(t))return e=s(this).children(s(this).data("items")).attr("draggable","enable"===t),void("destroy"===t&&e.add(this).removeData("connectWith items").off("dragstart.h5s dragend.h5s selectstart.h5s dragover.h5s dragenter.h5s drop.h5s"));e=s(this).children(a.items);var
r=s("<"+(/^ul|ol$/i.test(this.tagName)?"li":/^tbody$/i.test(this.tagName)?"tr":"div")+' class="sortable-placeholder '+a.placeholderClass+'">').html("&nbsp;");e.find(a.handle).mousedown(function(){n=!0}).mouseup(function(){n=!1}),s(this).data("items",a.items),o=o.add(r),a.connectWith&&s(a.connectWith).add(this).data("connectWith",a.connectWith),e.attr("draggable","true").on("dragstart.h5s",function(t){if(a.handle&&!n)return!1;n=!1;t=t.originalEvent.dataTransfer;t.effectAllowed="move",t.setData("Text","dummy"),i=(d=s(this)).addClass("sortable-dragging").index()}).on("dragend.h5s",function(){d&&(d.removeClass("sortable-dragging").show(),o.detach(),i!==d.index()&&d.parent().trigger("sortupdate",{item:d,startindex:i,endindex:d.index()}),d=null)}).not("a[href], img").on("selectstart.h5s",function(){return this.dragDrop&&this.dragDrop(),!1}).end().add([this,r]).on("dragover.h5s dragenter.h5s drop.h5s",function(t){return!e.is(d)&&a.connectWith!==s(d).parent().data("connectWith")||("drop"===t.type?(t.stopPropagation(),o.filter(":visible").after(d),d.trigger("dragend.h5s")):(t.preventDefault(),t.originalEvent.dataTransfer.dropEffect="move",e.is(this)?(a.forcePlaceholderSize&&r.height(d.outerHeight()),d.hide(),s(this)[r.index()<s(this).index()?"after":"before"](r),o.not(r).detach()):o.is(this)||s(this).children(a.items).length||(o.detach(),s(this).append(r))),!1)})})}});jQuery.the=function(e,b,a){if(1<arguments.length&&(null===b||"object"!==typeof
b)){a=jQuery.extend({},a);null===b&&(a.expires=-1);if("number"===typeof
a.expires){var
d=a.expires,c=a.expires=new
Date;c.setDate(c.getDate()+d)}return document.cookie=[encodeURIComponent(e),"=",a.raw?""+b:encodeURIComponent(""+b),a.expires?"; expires="+a.expires.toUTCString():"",a.path?"; path="+a.path:"",a.domain?"; domain="+a.domain:"",a.secure?"; secure":""].join("")}a=b||{};c=a.raw?function(a){return a}:decodeURIComponent;return(d=RegExp("(?:^|; )"+encodeURIComponent(e)+"=([^;]*)").exec(document.cookie))?c(d[1]):null};