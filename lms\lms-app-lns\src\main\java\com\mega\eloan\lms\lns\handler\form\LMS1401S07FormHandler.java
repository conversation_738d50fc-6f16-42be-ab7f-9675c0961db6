/*
 * LMS1401S07FormHandler.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.tools.ant.util.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.handler.form.LMSCOMMONFormHandler;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.panels.LMSS05APanel;
import com.mega.eloan.lms.lns.report.impl.LMS1201R01RptServiceImpl;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S16A;
import com.mega.eloan.lms.model.L120S16B;
import com.mega.eloan.lms.model.L120S16C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 國內授信簽報書企金(共用) FormHandler
 * </pre>
 * 
 * @since 2012/10/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/1,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1401s07formhandler")
@DomainClass(L120M01A.class)
public class LMS1401S07FormHandler extends LMSCOMMONFormHandler {

	// public final String space = "&nbsp;";
	// 找出IMG標籤
	@SuppressWarnings("unused")
	private final static String regxpForImgTag = "<\\s*img\\s+([^>]*)\\s*>";
	// 找出IMG標籤的SRC屬性
	@SuppressWarnings("unused")
	private final static String regxpForImaTagSrcAttrib = "src=\"([^\"]+)\"";

	// J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
	// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
	private String[] page01S16a = new String[] { "custId", "dupNo", "cntrNo",
			"typCd", "custName", "property", "currentApply", "lnSubject",
			"payDeadline", "guarantor", "collateral", "itemDscr2", "itemDscr3",
			"itemDscr4", "CurrentApplyCurrMsg", "itemDscrC" };
	private final static String adjFieldNmS16a = "_s16a";

	private String[] page01S16c = new String[] { "custId", "dupNo", "typCd",
			"custName", "lvTotAmt", "blTotAmt", "incApplyTotAmt",
			"incApplyMemo", "loanTotAmt", "assureTotAmt", "expMemo" };
	private final static String adjFieldNmS16c = "_s16c";

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	MisdbBASEService misDbService;

	@Resource
	LMS1201Service service1201;

	@Resource
	LMS1401Service lms1401Service;

	@Resource
	BranchService branchSrv;
	@Resource
	NumberService number;
	@Resource
	CodeTypeService codeService;
	@Resource
	ICustomerService icustSrv;
	@Resource
	LMSService lmsService;

	/**
	 * 透過MIS.CUSDATA取得借款人客戶名稱
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCustData(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			// 轉大寫
			custId = custId.toUpperCase();
		}
		List<Map<String, Object>> custData = icustSrv.findByIdBy0024(custId);
		Map<String, String> map = new TreeMap<String, String>();
		if (custData != null) {
			StringBuilder sbVal = new StringBuilder();
			for (Map<String, Object> mCust : custData) {
				sbVal.setLength(0);
				sbVal.append(Util.trim(mCust.get("CUSTID"))).append(
						Util.trim(mCust.get("DUPNO")));
				map.put(sbVal.toString(), Util.trim(mCust.get("CNAME")));
			}
		} else {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		if (!map.isEmpty()) {
			CapAjaxFormResult selCus = new CapAjaxFormResult(map);
			result.set("selCus", selCus);
		}
		return result;
	}

	/**
	 * 新增利害關係人
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL120s16a(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean clmtOther = false;
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		List<L120S16A> listL120s16a = service1201.findL120s16aByMainId(mainId);

		// 記錄需要刪除的利害關係人
		List<L120S16A> listToDela = new ArrayList<L120S16A>();
		List<L120S16B> listToDelb = new ArrayList<L120S16B>();
		for (L120S16A l120s16a : listL120s16a) {
			for (String oid : oidArray) {
				L140M01A l140m01a = lms1401Service.findModelByOid(
						L140M01A.class, oid);
				if (l140m01a != null) {
					if (l140m01a.getCustId().equals(l120s16a.getCustId())
							&& l140m01a.getDupNo().equals(l120s16a.getDupNo())
							&& l140m01a.getCntrNo()
									.equals(l120s16a.getCntrNo())) {
						listToDela.add(l120s16a);
						// 刪除明細
						Set<L120S16B> tlistL120s16b = l120s16a.getL120s16b();

						if (tlistL120s16b != null && !tlistL120s16b.isEmpty()) {
							for (L120S16B l120s16b : tlistL120s16b) {
								listToDelb.add(l120s16b);
							}
						}

					}
				}
			}
		}
		// 如果已有資料則初始化
		if (!listToDela.isEmpty() && !listToDelb.isEmpty()) {
			try {
				service1201.deleteListL120s16ab(listToDela, listToDelb);
			} catch (Exception e) {
				logger.error("addL120s16a exception", e);
			}
		}

		List<L120S16B> listL120s16b = service1201.findL120s16bByMainId(mainId);
		listL120s16a = new ArrayList<L120S16A>();
		listL120s16b = new ArrayList<L120S16B>();
		for (String oid : oidArray) {
			L140M01A l140m01a = lms1401Service.findModelByOid(L140M01A.class,
					oid);
			if (l140m01a != null) {
				List<L140M01C> listL140m01c = lms1401Service
						.findL140m01cListByMainId(l140m01a.getMainId());
				L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
				L120S16A l120s16a = new L120S16A();
				L120S16B l120s16b1 = new L120S16B();
				L120S16B l120s16b2 = new L120S16B();
				L120S16B l120s16b3 = new L120S16B();
				L120S16B l120s16b5 = new L120S16B();
				// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
				L120S16B l120s16bC = new L120S16B();

				// 設定 利害關係人授信條件對照表主檔(L120S16A)內容
				// 文件編號
				l120s16a.setMainId(mainId);

				// 授信戶統編(本案)
				l120s16a.setCustId(Util.trim(l140m01a.getCustId()));

				// 授信戶重複序號(本案)
				l120s16a.setDupNo(Util.trim(l140m01a.getDupNo()));

				// 授信戶額度序號(本案)
				l120s16a.setCntrNo(Util.trim(l140m01a.getCntrNo()));

				// 授信戶區部別(本案)
				l120s16a.setTypCd(Util.trim(l140m01a.getTypCd()));

				// 授信戶(本案)
				l120s16a.setCustName(Util.trim(l140m01a.getCustName()));

				// 授信科目(本案)
				l120s16a.setLnSubject(Util.trim(l140m01a.getLnSubject()));

				// 期限(本案)
				// l120s16a.setPayDeadline(LMSUtil.getUseDeadline(Util
				// .nullToSpace(l140m01a.getUseDeadline()), Util
				// .nullToSpace(l140m01a.getDesp1()),
				// MessageBundleScriptCreator
				// .getComponentResource(LMSCommomPage.class)));

				// J-110-0386_05097_B1002 Web
				// e-Loan國內與海外企金授信「相關文件」頁籤新增「授信額度主要敘做條件彙總表」
				// 國際金融業務分行 李易穎 2021-11-02 NOTES MAIL 主要條件彙總表, 建議調整!
				// 簽報書內之期限實為"授信期間"，內容不應擷取"動用期限"，故建議：(1)修改項目名稱為"授信期間"，(2)該空格留白，由經辦自行輸入。
				// 比照簽報書版本，建議：(1)將"連保人"修改為"保證人"；(2)項目左右順序一致。
				l120s16a.setPayDeadline("");

				// 連保人(本案)
				l120s16a.setGuarantor(Util.trim(l140m01a.getGuarantor()));

				// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
				// 性質
				l120s16a.setProperty(Util.trim(l140m01a.getProPerty()));

				// 建立人員號碼
				l120s16a.setCreator(user.getUserId());
				// 建立日期
				l120s16a.setCreateTime(CapDate.getCurrentTimestamp());
				// 異動人員號碼
				l120s16a.setUpdater(user.getUserId());
				// 異動日期
				l120s16a.setUpdateTime(CapDate.getCurrentTimestamp());

				// 設定利害關係人授信條件對照表明細檔(L120S16B)內容
				l120s16b1 = setL120s16b(l120s16b1, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.利_費_率, mainId,
						true, l120s16a);
				l120s16b2 = setL120s16b(l120s16b2, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.擔保品, mainId,
						true, l120s16a);
				l120s16b3 = setL120s16b(l120s16b3, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.期限說明, mainId,
						true, l120s16a);
				l120s16b5 = setL120s16b(l120s16b5, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.現請額度, mainId,
						true, l120s16a);
				// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
				l120s16bC = setL120s16b(l120s16bC, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.說明, mainId,
						true, l120s16a);

				if (!listL140m01c.isEmpty()) {
					for (L140M01C model : listL140m01c) {
						if (UtilConstants.DEFAULT.是.equals(Util.trim(model
								.getLmtOther()))) {
							clmtOther = true;
						}
					}
				}
				// 將設定好的Model丟到List裡準備儲存
				l120s16a.setChkYN(UtilConstants.DEFAULT.是);
				listL120s16a.add(l120s16a);
				listL120s16b.add(l120s16b1);
				listL120s16b.add(l120s16b2);
				listL120s16b.add(l120s16b3);
				listL120s16b.add(l120s16b5);
				// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
				listL120s16b.add(l120s16bC);

			}
		}
		// 儲存
		service1201.saveListL120s16a(listL120s16a, listL120s16b);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		result.set("clmtOther", clmtOther);
		return result;
	}// ;

	/**
	 * 設定利害關係人授信條件對照表明細檔(L120S16B)內容
	 * 
	 * @param l120s16b
	 *            L120S16B
	 * @param l140m01a
	 *            L140M01A
	 * @param type
	 *            String 1.本案授信戶 2.對照授信戶
	 * @param itemType
	 *            String 2利(費)率 3擔保品 4期限說明
	 * @param mainId
	 *            String 文件編號
	 * @param newDoc
	 *            boolean 新增文件時為true，否則為false
	 * @param l120s16a
	 *            L120S16A
	 * @return L120S16B
	 */
	private L120S16B setL120s16b(L120S16B l120s16b, L140M01A l140m01a,
			String itemType, String mainId, boolean newDoc, L120S16A l120s16a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// L120S16B model = new L120S16B();
		// model.setOid(l120s16b.getOid());
		// 文件編號
		l120s16b.setMainId(mainId);

		// 授信戶統編
		l120s16b.setCustId(Util.trim(l120s16a.getCustId()));

		// 授信戶重複序號
		l120s16b.setDupNo(Util.trim(l120s16a.getDupNo()));

		// 授信戶額度序號
		l120s16b.setCntrNo(Util.trim(l120s16a.getCntrNo()));

		// 項目類別
		l120s16b.setItemType(itemType);
		if (newDoc) {
			// 如果是本案授信戶就要把從L140M01B抓到的項目以及說明Set上去

			L140M01B l140m01b = lms1401Service.findL140m01bUniqueKey(
					l140m01a.getMainId(), itemType);
			if (l140m01b != null) {
				// 項目說明
				if (UtilConstants.Casedoc.L120s16bItemType.擔保品.equals(itemType)) {
					// 擔保品
					l120s16b.setItemDscr(getL140NewDanBow(l140m01a)
							+ Util.trim(l140m01b.getItemDscr()));
				} else if (UtilConstants.Casedoc.L120s16bItemType.現請額度
						.equals(itemType)) {

					Map<String, String> proPertyMap = codeService
							.findByCodeType("lms1405s02_proPerty", "zh_TW");

					StringBuffer str = new StringBuffer();
					String[] temp = Util.trim(l140m01a.getProPerty()).split(
							"\\|");
					for (String perty : temp) {
						str.append(Util.nullToSpace(proPertyMap.get(perty)))
								.append("、");
					}
					if (str.length() == 0) {
						str.append("、");
					}

					String propertyStr = str.toString().substring(0,
							str.length() - 1);

					l120s16b.setItemDscr(Util.trim(l140m01a
							.getCurrentApplyCurr())
							+ UtilConstants.Mark.SPACE

							+ ((l140m01a.getCurrentApplyAmt() == null || BigDecimal.ZERO
									.compareTo(l140m01a.getCurrentApplyAmt()) == 0) ? BigDecimal.ZERO
									: Util.trim(Util.nullToSpace(NumConverter.addComma(l140m01a
											.getCurrentApplyAmt()
											.divide(Util
													.parseBigDecimal("1000"),
													0, BigDecimal.ROUND_HALF_UP)))))

							+ (Util.equals(propertyStr, "") ? "" : "("
									+ propertyStr + ")")
					// J-110-0371 新版簽報書_主要申請敘作內容
					// 額度欄：只引入[幣別][金額][性質]，循環使用或不循環使用不必引入

					// + " " + codeService.getDescOfCodeType(
					// "lms1405s0202_reUse", Util.trim(l140m01a.getReUse())) +
					// " " + codeService.getDescOfCodeType(
					// "lms1405s0202_otherCurr",
					// Util.trim(l140m01a.getOtherCurr()))

					);
				} else if (UtilConstants.Casedoc.L120s16bItemType.說明
						.equals(itemType)) {
					// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
					l120s16b.setItemDscr(UtilConstants.Mark.SPACE);
				} else {
					l120s16b.setItemDscr(Util.trim(l140m01b.getItemDscr()));
				}
			} else {
				l120s16b.setItemDscr(UtilConstants.Mark.SPACE);
			}

		} else {
			L140M01B l140m01b = lms1401Service.findL140m01bUniqueKey(
					l140m01a.getMainId(), itemType);
			if (l140m01b != null) {
				// 項目說明
				if (UtilConstants.Casedoc.L120s16bItemType.擔保品.equals(itemType)) {
					// 擔保品
					l120s16b.setItemDscr(getL140NewDanBow(l140m01a)
							+ Util.trim(l140m01b.getItemDscr()));
				} else if (UtilConstants.Casedoc.L120s16bItemType.現請額度
						.equals(itemType)) {

					Map<String, String> proPertyMap = codeService
							.findByCodeType("lms1405s02_proPerty", "zh_TW");

					StringBuffer str = new StringBuffer();
					String[] temp = Util.trim(l140m01a.getProPerty()).split(
							"\\|");
					for (String perty : temp) {
						str.append(Util.nullToSpace(proPertyMap.get(perty)))
								.append("、");
					}
					if (str.length() == 0) {
						str.append("、");
					}

					String propertyStr = str.toString().substring(0,
							str.length() - 1);

					l120s16b.setItemDscr(Util.trim(l140m01a
							.getCurrentApplyCurr())
							+ UtilConstants.Mark.SPACE

							+ ((l140m01a.getCurrentApplyAmt() == null || BigDecimal.ZERO
									.compareTo(l140m01a.getCurrentApplyAmt()) == 0) ? BigDecimal.ZERO
									: Util.trim(Util.nullToSpace(NumConverter.addComma(l140m01a
											.getCurrentApplyAmt()
											.divide(Util
													.parseBigDecimal("1000"),
													0, BigDecimal.ROUND_HALF_UP)))))

							+ (Util.equals(propertyStr, "") ? "" : "("
									+ propertyStr + ")")
					// J-110-0371 新版簽報書_主要申請敘作內容
					// 額度欄：只引入[幣別][金額][性質]，循環使用或不循環使用不必引入

					// + " " + codeService.getDescOfCodeType(
					// "lms1405s0202_reUse", Util.trim(l140m01a.getReUse())) +
					// " " + codeService.getDescOfCodeType(
					// "lms1405s0202_otherCurr",
					// Util.trim(l140m01a.getOtherCurr()))

					);
				} else if (UtilConstants.Casedoc.L120s16bItemType.說明
						.equals(itemType)) {
					// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
					l120s16b.setItemDscr(UtilConstants.Mark.SPACE);
				} else {
					l120s16b.setItemDscr(Util.trim(l140m01b.getItemDscr()));
				}
			} else {
				l120s16b.setItemDscr(UtilConstants.Mark.SPACE);
			}
		}
		if (Util.isEmpty(l120s16b.getCreator())
				&& Util.isEmpty(l120s16b.getCreateTime())) {
			// 建立人員號碼
			l120s16b.setCreator(user.getUserId());
			// 建立日期
			l120s16b.setCreateTime(CapDate.getCurrentTimestamp());
		}
		// 異動人員號碼
		l120s16b.setUpdater(user.getUserId());
		// 異動日期
		l120s16b.setUpdateTime(CapDate.getCurrentTimestamp());

		l120s16b.setType("1");
		return l120s16b;
	}

	/**
	 * 刪除主要敘作條件
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL120s16b(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		// 刪除L120s06a和L120s06b List
		service1201.deleteListL120s16a(oidArray);
		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}// ;

	/**
	 * <pre>
	 * 查詢主要敘作條件
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s16b(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		boolean requery = params.getBoolean("requery");

		L120S16A l120s16a = service1201.findL120s16aByOid(oid);
		L120M01A l120m01a = null;
		if (l120s16a != null) {
			// 如果不為空
			// 開始找資料

			L120S16B l120s16b1 = null;
			L120S16B l120s16b2 = null;
			L120S16B l120s16b3 = null;

			L120S16B l120s16b7 = null;

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			L120S16B l120s16bC = null;

			Set<L120S16B> list = l120s16a.getL120s16b();
			for (L120S16B model : list) {

				// 本案授信戶

				if (UtilConstants.Casedoc.L120s16bItemType.利_費_率.equals(Util
						.trim(model.getItemType()))) {
					// 利費率
					if (l120s16b1 == null) {
						l120s16b1 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.擔保品
						.equals(Util.trim(model.getItemType()))) {
					// 擔保品
					if (l120s16b2 == null) {
						l120s16b2 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.期限說明
						.equals(Util.trim(model.getItemType()))) {
					// 期限說明
					if (l120s16b3 == null) {
						l120s16b3 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.現請額度
						.equals(Util.trim(model.getItemType()))) {
					// 現請額度
					if (l120s16b7 == null) {
						l120s16b7 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.說明
						.equals(Util.trim(model.getItemType()))) {
					// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
					// 說明
					if (l120s16bC == null) {
						l120s16bC = model;
					}
				}

			}

			CapAjaxFormResult myForm2Result = DataParse.toResult(l120s16a);

			if (l120s16b1 != null && l120s16b2 != null && l120s16b3 != null) {
				// 本案利(費)率內容
				myForm2Result.set("itemDscr2", l120s16b1.getItemDscr());
				// 本案擔保品內容
				myForm2Result.set("itemDscr3", l120s16b2.getItemDscr());
				// 本案其期限說明內容
				myForm2Result.set("itemDscr4", l120s16b3.getItemDscr());

			}
			// J-107-0227_10702_B1001 企金敘做條件異動比較表新增欄位
			if (l120s16b7 != null) {
				// 現請額度
				myForm2Result.set("CurrentApplyCurrMsg",
						l120s16b7.getItemDscr());

			} else {
				myForm2Result.set("CurrentApplyCurrMsg",
						UtilConstants.Mark.SPACE);

			}

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			// 說明
			if (l120s16bC != null) {
				// 現請額度
				myForm2Result.set("itemDscrC", l120s16bC.getItemDscr());

			} else {
				myForm2Result.set("itemDscrC", UtilConstants.Mark.SPACE);
			}

			myForm2Result
					.set("typCd",
							getMessage("typCd."
									+ Util.trim(myForm2Result.get("typCd"))));
			// myForm2Result.set(
			// "typCd",
			// TypCdEnum.getEnum(
			// Util.trim(myForm2Result.get("typCd")))
			// .name());

			l120m01a = service1201.findL120m01aByMainId(l120s16a.getMainId());
			if (Util.isNotEmpty(Util.trim(l120m01a.getDocStatus()))) {
				if (UtilConstants.Mark.SPACE.equals(l120m01a.getDocStatus())) {
					myForm2Result.set("docStatus", UtilConstants.Mark.SPACE);
				} else {
					myForm2Result
							.set("docStatus",
									getMessage("docStatus."
											+ CreditDocStatusEnum.getEnum(
													l120m01a.getDocStatus())
													.getCode()));
				}
			}

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			for (String fieldName : page01S16a) {
				myForm2Result.set(fieldName + adjFieldNmS16a,
						(String) myForm2Result.get(fieldName));
				myForm2Result.removeField(fieldName);
			}

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表。
			// 性質
			StringBuilder stringTemp1 = new StringBuilder("");
			myForm2Result.set("property_s16a", l120s16a.getProperty());
			if (!Util.isEmpty(l120s16a.getProperty())) {
				// 處理性質顯示
				String[] proPerty = l120s16a.getProperty().split(
						UtilConstants.Mark.SPILT_MARK);
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1401S02Panel.class);
				for (String type : proPerty) {
					if (!Util.isEmpty(type)) {
						String name = prop.getProperty("L140M01a.type" + type);
						stringTemp1
								.append(stringTemp1.length() > 0 ? ", " : "")
								.append(name);
					}

				}
				myForm2Result.set("propertyShow_s16a", stringTemp1.toString());
			}

			result.set("tLMS1401S07Form01", myForm2Result);

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			if (LMSUtil.isContainValue(Util.trim(l120s16a.getProperty()),
					UtilConstants.Cntrdoc.Property.不變)) {
				result.set("propertyIsNotChange", "Y");
			} else {
				result.set("propertyIsNotChange", "N");
			}

			// if (l120s16b3 != null) {
			// if (Util.isNotEmpty(l120s16b3.getItemDscr())) {
			// result.set("clmtOther", true);
			// }
			// }
		}
		return result;
	}

	/**
	 * 依照額度明細表主檔取得擔保維持率描述
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @return 串好的擔保維持率描述
	 */
	private String getL140NewDanBow(L140M01A l140m01a) {
		if (l140m01a != null) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1201R01RptServiceImpl.class);
			String headItem5Str = "";
			// 2012_11_12_Rex New_ 增加擔保維持率描述
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getHeadItem5())) {
				// L140M01A.HeadItem5=擔保維持率控管不得低於
				String temp = prop.getProperty("L140M01A.HeadItem5");
				if (UtilConstants.Cntrdoc.l140m01aMRateType.自訂.equals(l140m01a
						.getMRateType())) {
					if (l140m01a.getMRate() != null) {
						temp += LMSUtil.calcZero(l140m01a.getMRate()) + " ％。";
					} else {
						temp += "0 ％。";
					}

				} else {
					temp += "140％。";
				}
				headItem5Str = temp + "<br/>";
			}
			String headItem1 = "";
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getHeadItem1())) {
				String gutPercent = l140m01a.getGutPercent() == null ? "0"
						: LMSUtil.calcZero(l140m01a.getGutPercent()) + "";
				// L140M01A.HeadItem1=中小信保基金保證
				headItem1 = prop.getProperty("L140M01A.HeadItem1") + gutPercent
						+ "％";
				String gutCutDate = Util.getDate(l140m01a.getGutCutDate());
				if (Util.isNotEmpty(gutCutDate)) {
					// L140M01A.GutCutDate=信保首次動用有效期限
					headItem1 += "【" + prop.getProperty("L140M01A.GutCutDate")
							+ " " + gutCutDate + "】。";
				}
			}
			String drc = Util.trim(headItem1)
					+ (Util.isEmpty(headItem5Str) ? "" : headItem5Str);
			return drc;
		} else {
			return UtilConstants.Mark.SPACE;
		}
	}

	/**
	 * <pre>
	 * 查詢利害關係人(本案)
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s06bl(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		boolean requery = params.getBoolean("requery");
		String typCd2 = params.getString("typCd2");
		String custId2 = params.getString("custId2");
		String dupNo2 = params.getString("dupNo2");
		String custName2 = params.getString("custName2");
		String payDeadline2 = params.getString("payDeadline2");
		String guarantor2 = params.getString("guarantor2");
		String purpose2 = params.getString("purpose2");
		String lnSubject2 = params.getString("lnSubject2");
		String cntrNo2 = params.getString("cntrNo2");
		String property2 = params.getString("property2");
		String currentApplyCurr2 = params.getString("currentApplyCurr2");
		String currentApplyAmt2 = params.getString("currentApplyAmt2");
		String gutPercent2 = params.getString("gutPercent2");
		String guarantorMemo2 = params.getString("guarantorMemo2");
		L120S16A l120s16a = service1201.findL120s16aByOid(oid);
		if (l120s16a != null) {
			// 如果不為空
			// 開始找資料
			L120S16B l120s16b1 = null;
			L120S16B l120s16b2 = null;
			L120S16B l120s16b3 = null;
			// L120S16B l120s16b4 = null;
			L120S16B l120s16b5 = null;
			L120S16B l120s16b6 = null;
			L120S16B l120s16b7 = null;
			L120S16B l120s16b8 = null;
			L120S16B l120s16b9 = null;
			Set<L120S16B> list = l120s16a.getL120s16b();
			for (L120S16B model : list) {

				// 本案授信戶
				if (UtilConstants.Casedoc.L120s16bItemType.利_費_率.equals(Util
						.trim(model.getItemType()))) {
					// 利費率
					if (l120s16b1 == null) {
						l120s16b1 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.擔保品
						.equals(Util.trim(model.getItemType()))) {
					// 擔保品
					if (l120s16b2 == null) {
						l120s16b2 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.期限說明
						.equals(Util.trim(model.getItemType()))) {
					// 期限說明
					if (l120s16b3 == null) {
						l120s16b3 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.現請額度
						.equals(Util.trim(model.getItemType()))) {
					// 現請額度
					if (l120s16b5 == null) {
						l120s16b5 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.動用期限
						.equals(Util.trim(model.getItemType()))) {
					// 動用期限
					if (l120s16b6 == null) {
						l120s16b6 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.聯貸總額度
						.equals(Util.trim(model.getItemType()))) {
					// 聯貸總額度
					if (l120s16b7 == null) {
						l120s16b7 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.動用先決條件
						.equals(Util.trim(model.getItemType()))) {
					// 動用先決條件
					if (l120s16b8 == null) {
						l120s16b8 = model;
					}
				} else {
					// 增額條款
					if (l120s16b9 == null) {
						l120s16b9 = model;
					}
				}

			}
			CapAjaxFormResult myForm2Result = DataParse.toResult(l120s16a);

			L140M01A l140m01a = lms1401Service
					.findL140m01aByCaseMainIdCustIdCntrNoItemType(
							l120s16a.getMainId(), l120s16a.getCustId(),
							l120s16a.getDupNo(), l120s16a.getCntrNo(),
							UtilConstants.Cntrdoc.ItemType.額度明細表);

			if (l140m01a != null) {
				// List<L140M01C> listL140m01c = service1405
				// .findL140m01cListByMainId(l140m01a.getMainId());
				// 設定 利害關係人授信條件對照表主檔(L120S16A)內容
				// 性質(本案)
				myForm2Result
						.set("property", Util.trim(l140m01a.getProPerty()));
				// 現請額度－幣別(本案)
				myForm2Result.set("currentApplyCurr",
						Util.trim(l140m01a.getCurrentApplyCurr()));
				// 現請額度－金額(本案)
				if (Util.isNotEmpty(Util.trim(l140m01a.getCurrentApplyAmt()))) {
					myForm2Result.set("currentApplyAmt", Util.trim(Util
							.nullToSpace(l140m01a.getCurrentApplyAmt())));
				} else {
					myForm2Result.set("currentApplyAmt", "0");
				}

				// 保證成數(本案)
				if (Util.isNotEmpty(Util.trim(l140m01a.getCurrentApplyAmt()))) {
					myForm2Result.set("gutPercent", Util.trim(Util
							.nullToSpace(l140m01a.getGutPercent())));
				} else {
					myForm2Result.set("gutPercent", "0");
				}

				// 連保人備註(本案)
				myForm2Result.set("guarantorMemo",
						Util.trim(l140m01a.getGuarantorMemo()));
				// 授信戶統編(本案)
				myForm2Result.set("custId", Util.trim(l140m01a.getCustId()));
				// 授信戶重複序號(本案)
				myForm2Result.set("dupNo", Util.trim(l140m01a.getDupNo()));
				// 授信戶額度序號(本案)
				myForm2Result.set("cntrNo", Util.trim(l140m01a.getCntrNo()));
				// 授信戶區部別(本案)
				myForm2Result.set("typCd", Util.trim(l140m01a.getTypCd()));
				// 授信戶(本案)
				myForm2Result
						.set("custName", Util.trim(l140m01a.getCustName()));
				// 授信科目(本案)
				myForm2Result.set("lnSubject",
						Util.trim(l140m01a.getLnSubject()));
				// 資金用途(本案)
				L120M01A l120m01a = service1201.findL120m01aByMainId(l120s16a
						.getMainId());
				// if
				// (UtilConstants.Casedoc.DocKind.授權內.equals(Util.trim(l120m01a
				// .getDocKind()))) {
				// 授權內
				// 利害關係人條件對照表，資金用途現在授權內外都有，應該都要引進
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS05APanel.class);
				String[] purposes = Util.trim(l120m01a.getPurpose()).split(
						UtilConstants.Mark.SPILT_MARK);
				int count = 0;
				// 資金用途
				StringBuilder sbPur = new StringBuilder();

				for (String purpose : purposes) {
					if (UtilConstants.Casedoc.purpose.購料週轉金.equals(purpose)) {
						// 購料週轉金
						sbPur.append(pop.getProperty("cls120s05.checkbox1"));
					} else if (UtilConstants.Casedoc.purpose.營運週轉金
							.equals(purpose)) {
						// 營運週轉金
						sbPur.append(pop.getProperty("cls120s05.checkbox2"));
					} else if (UtilConstants.Casedoc.purpose.其他.equals(purpose)) {
						// 其他
						if (Util.isEmpty(l120m01a.getPurposeOth())) {
							sbPur.append(pop.getProperty("cls120s05.checkbox3"));
						} else {
							sbPur.append(Util.trim(l120m01a.getPurposeOth()));
						}
					} else {
						sbPur.append(UtilConstants.Mark.SPACE);
					}
					// 不是最後一筆中間用全形頓號隔開
					if (count < (purposes.length - 1)) {
						sbPur.append(UtilConstants.Mark.MARKDAN);
					}
					count++;
				}
				myForm2Result.set("purpose", sbPur.toString());
				// } else {
				// // 授權外
				// myForm2Result.set("purpose", UtilConstants.Mark.SPACE);
				// }
				// 期限(本案)
				myForm2Result.set("payDeadline", l140m01a.getPayDeadline());
				// 連保人(本案)
				myForm2Result.set("guarantor",
						Util.trim(l140m01a.getGuarantor()));

			}

			if (requery) {
				// 對照區部別
				if (Util.isNotEmpty(typCd2)) {
					myForm2Result.set("typCd2", typCd2);
				}
				// 對照統編
				myForm2Result.set("custId2", custId2);
				// 對照重覆序號
				myForm2Result.set("dupNo2", dupNo2);
				// 對照戶名
				myForm2Result.set("custName2", custName2);
				// 對照額度序號
				myForm2Result.set("cntrNo2", cntrNo2);
				// 對照授信科目
				myForm2Result.set("lnSubject2", lnSubject2);
				// 對照資金用途
				myForm2Result.set("purpose2", purpose2);
				// 對照期限
				myForm2Result.set("payDeadline2", payDeadline2);
				// 對照連保人
				myForm2Result.set("guarantor2", guarantor2);
				// 性質(對照)
				myForm2Result.set("property2", property2);
				// 現請額度－幣別(對照)
				myForm2Result.set("currentApplyCurr2", currentApplyCurr2);
				// 現請額度－金額(對照)
				myForm2Result.set("currentApplyAmt2", currentApplyAmt2);
				// 保證成數(對照)
				myForm2Result.set("gutPercent2", gutPercent2);
				// 連保人備註(對照)
				myForm2Result.set("guarantorMemo2", guarantorMemo2);
			}
			if (l120s16b1 != null && l120s16b2 != null && l120s16b3 != null) {
				l120s16b1 = setL120s16b(l120s16b1, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.利_費_率,
						l120s16a.getMainId(), false, l120s16a);
				l120s16b2 = setL120s16b(l120s16b2, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.擔保品,
						l120s16a.getMainId(), false, l120s16a);
				l120s16b3 = setL120s16b(l120s16b3, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.期限說明,
						l120s16a.getMainId(), false, l120s16a);
				// 本案利(費)率內容
				myForm2Result.set("itemDscr2", l120s16b1.getItemDscr());
				// 本案擔保品內容
				myForm2Result.set("itemDscr3", l120s16b2.getItemDscr());
				// 本案期限說明內容
				myForm2Result.set("itemDscr4", l120s16b3.getItemDscr());
			}
			if (l120s16b1 == null && l120s16b2 == null && l120s16b3 == null) {
				// 本案利(費)率內容
				myForm2Result.set("itemDscr2", UtilConstants.Mark.SPACE);
				// 本案擔保品內容
				myForm2Result.set("itemDscr3", UtilConstants.Mark.SPACE);
				// 本案期限說明內容
				myForm2Result.set("itemDscr4", UtilConstants.Mark.SPACE);
			}
			// 本案現請額度
			if (l120s16b5 != null) {
				l120s16b5 = setL120s16b(l120s16b5, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.現請額度,
						l120s16a.getMainId(), false, l120s16a);
			} else {
				l120s16b5 = new L120S16B();
				l120s16b5 = setL120s16b(l120s16b5, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.現請額度,
						l120s16a.getMainId(), false, l120s16a);

			}
			// 本案動用期限
			if (l120s16b6 != null) {
				l120s16b6 = setL120s16b(l120s16b6, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.動用期限,
						l120s16a.getMainId(), false, l120s16a);
			} else {
				l120s16b6 = new L120S16B();
				l120s16b6 = setL120s16b(l120s16b6, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.動用期限,
						l120s16a.getMainId(), false, l120s16a);
			}
			// 本案聯貸總額度
			if (l120s16b7 != null) {
				l120s16b7 = setL120s16b(l120s16b7, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.聯貸總額度,
						l120s16a.getMainId(), false, l120s16a);
			} else {
				l120s16b7 = new L120S16B();
				l120s16b7 = setL120s16b(l120s16b7, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.聯貸總額度,
						l120s16a.getMainId(), false, l120s16a);
			}
			// 本案動用先決條件
			if (l120s16b8 != null) {
				l120s16b8 = setL120s16b(l120s16b8, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.動用先決條件,
						l120s16a.getMainId(), false, l120s16a);
			} else {
				l120s16b8 = new L120S16B();
				l120s16b8 = setL120s16b(l120s16b8, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.動用先決條件,
						l120s16a.getMainId(), false, l120s16a);
			}
			// 本案增額條款
			if (l120s16b9 != null) {
				l120s16b9 = setL120s16b(l120s16b9, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.增額條款,
						l120s16a.getMainId(), false, l120s16a);
				l120s16b9 = setL120s16b(l120s16b9, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.增額條款,
						l120s16a.getMainId(), false, l120s16a);
			} else {
				l120s16b9 = new L120S16B();
				l120s16b9 = setL120s16b(l120s16b9, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.增額條款,
						l120s16a.getMainId(), false, l120s16a);
			}
			myForm2Result.set("CurrentApplyCurrMsg", l120s16b5.getItemDscr());
			myForm2Result.set("UseDeadlineMsg", l120s16b6.getItemDscr());
			myForm2Result.set("TotalEloanAmt", l120s16b7.getItemDscr());
			myForm2Result.set("UsePrerequisites", l120s16b8.getItemDscr());
			myForm2Result.set("IncreaseClause", l120s16b9.getItemDscr());

			myForm2Result
					.set("typCd",
							getMessage("typCd."
									+ Util.trim(myForm2Result.get("typCd"))));
			// myForm2Result.set(
			// "typCd",
			// TypCdEnum.getEnum(
			// Util.trim(myForm2Result.get("typCd")))
			// .name());
			if (Util.isNotEmpty(Util.trim(myForm2Result.get("typCd2")))) {
				if (Util.isNumeric(Util.trim(myForm2Result.get("typCd2")))) {
					myForm2Result.set(
							"typCd2",
							getMessage("typCd."
									+ Util.trim(myForm2Result.get("typCd2"))));
					// myForm2Result.set(
					// "typCd2",
					// TypCdEnum.getEnum(
					// Util.trim(myForm2Result
					// .get("typCd2"))).name());
				}
			}
			result.set("tLMS1401S07Form01", myForm2Result);
			if (l120s16b3 != null) {
				if (Util.isNotEmpty(l120s16b3.getItemDscr())) {
					result.set("clmtOther", true);
				}
			}
		}
		return result;
	}

	/**
	 * <pre>
	 * 查詢主要敘作條件(本案)
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s16bl(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		boolean requery = params.getBoolean("requery");

		L120S16A l120s16a = service1201.findL120s16aByOid(oid);
		if (l120s16a != null) {
			// 如果不為空
			// 開始找資料
			L120S16B l120s16b1 = null;
			L120S16B l120s16b2 = null;
			L120S16B l120s16b3 = null;
			// L120S16B l120s16b4 = null;
			L120S16B l120s16b5 = null;
			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			L120S16B l120s16bC = null;

			Set<L120S16B> list = l120s16a.getL120s16b();
			for (L120S16B model : list) {

				// 本案授信戶
				if (UtilConstants.Casedoc.L120s16bItemType.利_費_率.equals(Util
						.trim(model.getItemType()))) {
					// 利費率
					if (l120s16b1 == null) {
						l120s16b1 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.擔保品
						.equals(Util.trim(model.getItemType()))) {
					// 擔保品
					if (l120s16b2 == null) {
						l120s16b2 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.期限說明
						.equals(Util.trim(model.getItemType()))) {
					// 期限說明
					if (l120s16b3 == null) {
						l120s16b3 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.現請額度
						.equals(Util.trim(model.getItemType()))) {
					// 現請額度
					if (l120s16b5 == null) {
						l120s16b5 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.說明
						.equals(Util.trim(model.getItemType()))) {
					// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
					// 說明
					if (l120s16bC == null) {
						l120s16bC = model;
					}
				}

			}
			CapAjaxFormResult myForm2Result = DataParse.toResult(l120s16a);

			L140M01A l140m01a = lms1401Service
					.findL140m01aByCaseMainIdCustIdCntrNoItemType(
							l120s16a.getMainId(), l120s16a.getCustId(),
							l120s16a.getDupNo(), l120s16a.getCntrNo(),
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			if (l140m01a != null) {
				// List<L140M01C> listL140m01c = service1405
				// .findL140m01cListByMainId(l140m01a.getMainId());
				// 設定 利害關係人授信條件對照表主檔(L120S16A)內容
				// 性質(本案)
				myForm2Result
						.set("property", Util.trim(l140m01a.getProPerty()));
				// 現請額度－幣別(本案)
				myForm2Result.set("currentApplyCurr",
						Util.trim(l140m01a.getCurrentApplyCurr()));
				// 現請額度－金額(本案)
				if (Util.isNotEmpty(Util.trim(l140m01a.getCurrentApplyAmt()))) {
					myForm2Result.set("currentApplyAmt", Util.trim(Util
							.nullToSpace(l140m01a.getCurrentApplyAmt())));
				} else {
					myForm2Result.set("currentApplyAmt", "0");
				}

				// 保證成數(本案)
				if (Util.isNotEmpty(Util.trim(l140m01a.getCurrentApplyAmt()))) {
					myForm2Result.set("gutPercent", Util.trim(Util
							.nullToSpace(l140m01a.getGutPercent())));
				} else {
					myForm2Result.set("gutPercent", "0");
				}

				// 連保人備註(本案)
				myForm2Result.set("guarantorMemo",
						Util.trim(l140m01a.getGuarantorMemo()));
				// 授信戶統編(本案)
				myForm2Result.set("custId", Util.trim(l140m01a.getCustId()));
				// 授信戶重複序號(本案)
				myForm2Result.set("dupNo", Util.trim(l140m01a.getDupNo()));
				// 授信戶額度序號(本案)
				myForm2Result.set("cntrNo", Util.trim(l140m01a.getCntrNo()));
				// 授信戶區部別(本案)
				myForm2Result.set("typCd", Util.trim(l140m01a.getTypCd()));
				// 授信戶(本案)
				myForm2Result
						.set("custName", Util.trim(l140m01a.getCustName()));
				// 授信科目(本案)
				myForm2Result.set("lnSubject",
						Util.trim(l140m01a.getLnSubject()));
				// 資金用途(本案)
				L120M01A l120m01a = service1201.findL120m01aByMainId(l120s16a
						.getMainId());
				// if
				// (UtilConstants.Casedoc.DocKind.授權內.equals(Util.trim(l120m01a
				// .getDocKind()))) {
				// 授權內
				// 利害關係人條件對照表，資金用途現在授權內外都有，應該都要引進
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMSS05APanel.class);
				String[] purposes = Util.trim(l120m01a.getPurpose()).split(
						UtilConstants.Mark.SPILT_MARK);
				int count = 0;
				// 資金用途
				StringBuilder sbPur = new StringBuilder();

				for (String purpose : purposes) {
					if (UtilConstants.Casedoc.purpose.購料週轉金.equals(purpose)) {
						// 購料週轉金
						sbPur.append(pop.getProperty("cls120s05.checkbox1"));
					} else if (UtilConstants.Casedoc.purpose.營運週轉金
							.equals(purpose)) {
						// 營運週轉金
						sbPur.append(pop.getProperty("cls120s05.checkbox2"));
					} else if (UtilConstants.Casedoc.purpose.其他.equals(purpose)) {
						// 其他
						if (Util.isEmpty(l120m01a.getPurposeOth())) {
							sbPur.append(pop.getProperty("cls120s05.checkbox3"));
						} else {
							sbPur.append(Util.trim(l120m01a.getPurposeOth()));
						}
					} else {
						sbPur.append(UtilConstants.Mark.SPACE);
					}
					// 不是最後一筆中間用全形頓號隔開
					if (count < (purposes.length - 1)) {
						sbPur.append(UtilConstants.Mark.MARKDAN);
					}
					count++;
				}
				myForm2Result.set("purpose", sbPur.toString());
				// } else {
				// // 授權外
				// myForm2Result.set("purpose", UtilConstants.Mark.SPACE);
				// }
				// 期限(本案)
				// J-110-0371 新版簽報書_主要申請敘作內容
				// 期限欄：引入 [動用期限]
				myForm2Result.set("payDeadline", LMSUtil.getUseDeadline(Util
						.nullToSpace(l140m01a.getUseDeadline()), Util
						.nullToSpace(l140m01a.getDesp1()),
						MessageBundleScriptCreator
								.getComponentResource(LMSCommomPage.class)));
				// 連保人(本案)
				myForm2Result.set("guarantor",
						Util.trim(l140m01a.getGuarantor()));

			}

			if (l120s16b1 != null && l120s16b2 != null && l120s16b3 != null) {
				l120s16b1 = setL120s16b(l120s16b1, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.利_費_率,
						l120s16a.getMainId(), false, l120s16a);
				l120s16b2 = setL120s16b(l120s16b2, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.擔保品,
						l120s16a.getMainId(), false, l120s16a);
				l120s16b3 = setL120s16b(l120s16b3, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.期限說明,
						l120s16a.getMainId(), false, l120s16a);
				// 本案利(費)率內容
				myForm2Result.set("itemDscr2", l120s16b1.getItemDscr());
				// 本案擔保品內容
				myForm2Result.set("itemDscr3", l120s16b2.getItemDscr());
				// 本案期限說明內容
				myForm2Result.set("itemDscr4", l120s16b3.getItemDscr());
			}
			if (l120s16b1 == null && l120s16b2 == null && l120s16b3 == null) {
				// 本案利(費)率內容
				myForm2Result.set("itemDscr2", UtilConstants.Mark.SPACE);
				// 本案擔保品內容
				myForm2Result.set("itemDscr3", UtilConstants.Mark.SPACE);
				// 本案期限說明內容
				myForm2Result.set("itemDscr4", UtilConstants.Mark.SPACE);
			}
			// 本案現請額度
			if (l120s16b5 != null) {
				l120s16b5 = setL120s16b(l120s16b5, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.現請額度,
						l120s16a.getMainId(), false, l120s16a);
			} else {
				l120s16b5 = new L120S16B();
				l120s16b5 = setL120s16b(l120s16b5, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.現請額度,
						l120s16a.getMainId(), false, l120s16a);

			}

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			// 說明
			// 因為說明是經辦自己打的，所以就不要清除
			// if (l120s16bC != null) {
			// myForm2Result.set("itemDscrC", l120s16bC.getItemDscr());
			//
			// } else {
			// myForm2Result.set("itemDscrC", UtilConstants.Mark.SPACE);
			// }

			myForm2Result.set("CurrentApplyCurrMsg", l120s16b5.getItemDscr());

			myForm2Result
					.set("typCd",
							getMessage("typCd."
									+ Util.trim(myForm2Result.get("typCd"))));

			myForm2Result.set("CurrentApplyCurrMsg", l120s16b5.getItemDscr());

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表。
			// 性質
			StringBuilder stringTemp1 = new StringBuilder("");
			myForm2Result.set("property_s16a", l140m01a.getProPerty());
			if (!Util.isEmpty(l140m01a.getProPerty())) {
				// 處理性質顯示
				String[] proPerty = l140m01a.getProPerty().split(
						UtilConstants.Mark.SPILT_MARK);
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1401S02Panel.class);
				for (String type : proPerty) {
					if (!Util.isEmpty(type)) {
						String name = prop.getProperty("L140M01a.type" + type);
						stringTemp1
								.append(stringTemp1.length() > 0 ? ", " : "")
								.append(name);
					}

				}
				myForm2Result.set("propertyShow_s16a", stringTemp1.toString());
			}

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			for (String fieldName : page01S16a) {
				myForm2Result.set(fieldName + adjFieldNmS16a,
						(String) myForm2Result.get(fieldName));
				myForm2Result.removeField(fieldName);
			}

			result.set("tLMS1401S07Form01", myForm2Result);
			// if (l120s16b3 != null) {
			// if (Util.isNotEmpty(l120s16b3.getItemDscr())) {
			// result.set("clmtOther", true);
			// }
			// }
		}
		return result;
	}

	/**
	 * 儲存主要敘作條件(更新)
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s16b(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L120S16A l120s16a = service1201.findL120s16aByOid(oid);
		L120M01A l120m01a = service1201.findL120m01aByMainId(l120s16a
				.getMainId());

		if (l120s16a != null) {
			String formLms1205s07 = params.getString("tLMS1401S07Form01");

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			formLms1205s07 = StringUtils.replace(formLms1205s07,
					adjFieldNmS16a, "");

			JSONObject myForm2Json = JSONObject.fromObject(formLms1205s07);

			// for (String fieldName : page01S16a) {
			// myForm2Json.put(fieldName,
			// myForm2Json.optString(fieldName + adjFieldNmS16a));
			// myForm2Json.remove(fieldName + adjFieldNmS16a);
			// }

			DataParse.toBean(myForm2Json.toString(), l120s16a); // formLms1205s07

			L120S16B l120s16b1 = null;
			L120S16B l120s16b2 = null;
			L120S16B l120s16b3 = null;

			L120S16B l120s16b7 = null;
			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			L120S16B l120s16bC = null;

			Set<L120S16B> list = l120s16a.getL120s16b();
			for (L120S16B model : list) {

				// 本案授信戶
				if (UtilConstants.Casedoc.L120s06bItemType.利_費_率.equals(Util
						.trim(model.getItemType()))) {
					// 利費率
					if (l120s16b1 == null) {
						l120s16b1 = model;
					}
				} else if (UtilConstants.Casedoc.L120s06bItemType.擔保品
						.equals(Util.trim(model.getItemType()))) {
					// 擔保品
					if (l120s16b2 == null) {
						l120s16b2 = model;
					}
				} else if (UtilConstants.Casedoc.L120s06bItemType.期限說明
						.equals(Util.trim(model.getItemType()))) {
					// 期限說明
					if (l120s16b3 == null) {
						l120s16b3 = model;
					}
				} else if (UtilConstants.Casedoc.L120s06bItemType.現請額度
						.equals(Util.trim(model.getItemType()))) {
					// 現請額度
					if (l120s16b7 == null) {
						l120s16b7 = model;
					}
				} else if (UtilConstants.Casedoc.L120s16bItemType.說明
						.equals(Util.trim(model.getItemType()))) {
					// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
					// 說明
					if (l120s16bC == null) {
						l120s16bC = model;
					}
				}

			}
			if (l120s16b1 == null) {
				l120s16b1 = new L120S16B();
			}
			if (l120s16b2 == null) {
				l120s16b2 = new L120S16B();
			}
			if (l120s16b3 == null) {
				l120s16b3 = new L120S16B();
			}

			if (l120s16b1 != null) {
				// 本案利(費)率內容
				l120s16b1.setItemDscr(myForm2Json.getString("itemDscr2"));
			}
			if (l120s16b2 != null) {
				// 本案擔保品內容
				l120s16b2.setItemDscr(myForm2Json.getString("itemDscr3"));
			}
			if (l120s16b3 != null) {
				// 本案期限說明內容
				l120s16b3.setItemDscr(myForm2Json.getString("itemDscr4"));
			}

			if (l120s16b7 == null) {
				l120s16b7 = new L120S16B();
				L140M01A l140m01a = lms1401Service
						.findL140m01aByCaseMainIdCustIdCntrNoItemType(
								l120s16a.getMainId(), l120s16a.getCustId(),
								l120s16a.getDupNo(), l120s16a.getCntrNo(),
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				l120s16b7 = setL120s16b(l120s16b7, l140m01a,
						UtilConstants.Casedoc.L120s06bItemType.現請額度,
						l120s16a.getMainId(), true, l120s16a);
			}

			if (l120s16b7 != null) {
				// 現請額度
				l120s16b7.setItemDscr(myForm2Json
						.getString("CurrentApplyCurrMsg"));
			}

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
			if (l120s16bC == null) {
				l120s16bC = new L120S16B();
				L140M01A l140m01a = lms1401Service
						.findL140m01aByCaseMainIdCustIdCntrNoItemType(
								l120s16a.getMainId(), l120s16a.getCustId(),
								l120s16a.getDupNo(), l120s16a.getCntrNo(),
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				l120s16bC = setL120s16b(l120s16bC, l140m01a,
						UtilConstants.Casedoc.L120s16bItemType.說明,
						l120s16a.getMainId(), true, l120s16a);
			}

			if (l120s16bC != null) {
				l120s16bC.setItemDscr(myForm2Json.getString("itemDscrC"));

			}

			// 檢核設定
			l120s16a.setChkYN(UtilConstants.DEFAULT.是);
			try {

				// 有引進利害關係人對照表
				// 允許儲存
				service1201.save(l120m01a, l120s16a, l120s16b1, l120s16b2,
						l120s16b3, l120s16b7, l120s16bC);

			} catch (Exception e) {
				logger.error("[saveL120s16b] service1201.save EXCEPTION!!", e);
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", UtilConstants.Mark.HTMLSPACE);
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
			}
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}// ;

	/**
	 * 新增授信額度異動情形
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL120s16c(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		boolean clmtOther = false;
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		List<L120S16C> listL120s16c = service1201.findL120s16cByMainId(mainId);

		// 記錄需要刪除
		List<L120S16C> listToDela = new ArrayList<L120S16C>();

		for (L120S16C l120s16c : listL120s16c) {
			for (String oid : oidArray) {
				L120S01A l120s01a = service1201.findL120s01aByOid(oid);
				if (l120s01a != null) {
					if (l120s01a.getCustId().equals(l120s16c.getCustId())
							&& l120s01a.getDupNo().equals(l120s16c.getDupNo())) {
						listToDela.add(l120s16c);

					}
				}
			}
		}
		// 如果已有資料則初始化
		if (!listToDela.isEmpty()) {
			try {
				service1201.deleteListL120s16c(listToDela);
			} catch (Exception e) {
				logger.error("addL120s16c exception", e);
			}
		}
		listL120s16c = new ArrayList<L120S16C>();

		for (String oid : oidArray) {
			L120S01A l120s01a = service1201.findL120s01aByOid(oid);

			L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
			L120S16C l120s16c = new L120S16C();
			l120s16c.setMainId(mainId);
			l120s16c.setCustId(Util.trim(l120s01a.getCustId()));
			l120s16c.setDupNo(Util.trim(l120s01a.getDupNo()));
			l120s16c.setCustName(Util.trim(l120s01a.getCustName()));
			// 授信戶區部別(本案)
			l120s16c.setTypCd(Util.trim(l120s01a.getTypCd())); // 存代碼不是存DBU、OBU

			JSONObject jsonL120s16c = setL120s16c(l120s16c, mainId,
					Util.trim(l120s01a.getCustId()),
					Util.trim(l120s01a.getDupNo()));

			// 前准額度
			l120s16c.setLvTotAmt(jsonL120s16c.optString("lvTotAmt", ""));

			// 餘額
			l120s16c.setBlTotAmt(jsonL120s16c.optString("blTotAmt", ""));

			// 增加減少
			l120s16c.setIncApplyTotAmt(jsonL120s16c.optString("incApplyTotAmt",
					""));

			// 額度增減說明
			l120s16c.setIncApplyMemo(jsonL120s16c.optString("incApplyMemo", ""));

			// 授信總額度
			l120s16c.setLoanTotAmt(jsonL120s16c.optString("loanTotAmt", ""));

			// 其中擔保
			l120s16c.setAssureTotAmt(jsonL120s16c.optString("assureTotAmt", ""));

			// 建立人員號碼
			l120s16c.setCreator(user.getUserId());
			// 建立日期
			l120s16c.setCreateTime(CapDate.getCurrentTimestamp());
			// 異動人員號碼
			l120s16c.setUpdater(user.getUserId());
			// 異動日期
			l120s16c.setUpdateTime(CapDate.getCurrentTimestamp());

			// 將設定好的Model丟到List裡準備儲存
			l120s16c.setChkYN(UtilConstants.DEFAULT.是);

			listL120s16c.add(l120s16c);

		}
		// 儲存
		service1201.saveListL120s16c(listL120s16c);
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}// ;

	/**
	 * <pre>
	 * 查詢利害關係人
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s16c(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		boolean requery = params.getBoolean("requery");

		L120S16C l120s16c = service1201.findL120s16cByOid(oid);
		L120M01A l120m01a = null;
		if (l120s16c != null) {
			// 如果不為空
			// 開始找資料
			CapAjaxFormResult myForm2Result = DataParse.toResult(l120s16c);

			myForm2Result
					.set("typCd",
							getMessage("typCd."
									+ Util.trim(myForm2Result.get("typCd"))));

			l120m01a = service1201.findL120m01aByMainId(l120s16c.getMainId());
			if (Util.isNotEmpty(Util.trim(l120m01a.getDocStatus()))) {
				if (UtilConstants.Mark.SPACE.equals(l120m01a.getDocStatus())) {
					myForm2Result.set("docStatus", UtilConstants.Mark.SPACE);
				} else {
					myForm2Result
							.set("docStatus",
									getMessage("docStatus."
											+ CreditDocStatusEnum.getEnum(
													l120m01a.getDocStatus())
													.getCode()));
				}
			}

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			for (String fieldName : page01S16c) {
				myForm2Result.set(fieldName + adjFieldNmS16c,
						(String) myForm2Result.get(fieldName));
				myForm2Result.removeField(fieldName);
			}

			result.set("tLMS1401S07Form02", myForm2Result);

		}
		return result;
	}

	/**
	 * 儲存授信額度異動情形(更新)
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120s16c(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L120S16C l120s16c = service1201.findL120s16cByOid(oid);
		L120M01A l120m01a = service1201.findL120m01aByMainId(l120s16c
				.getMainId());

		if (l120s16c != null) {
			String formLms11401s07 = params.getString("tLMS1401S07Form02");

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			formLms11401s07 = StringUtils.replace(formLms11401s07,
					adjFieldNmS16c, "");

			JSONObject myForm2Json = JSONObject.fromObject(formLms11401s07);

			// for (String fieldName : page01S16c) {
			// myForm2Json.put(fieldName,
			// myForm2Json.optString(fieldName + adjFieldNmS16c));
			// myForm2Json.remove(fieldName + adjFieldNmS16c);
			// }

			DataParse.toBean(formLms11401s07, l120s16c);

			// 檢核設定
			l120s16c.setChkYN(UtilConstants.DEFAULT.是);
			try {

				// 有引進利害關係人對照表
				// 允許儲存
				service1201.save(l120m01a, l120s16c);

			} catch (Exception e) {
				logger.error("[saveL120s16c] service1201.save EXCEPTION!!", e);
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", UtilConstants.Mark.HTMLSPACE);
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.輸入位數超過, param), getClass());
			}
		}
		if (params.getAsBoolean("showMsg", true)) {
			// 印出儲存成功訊息!
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}// ;

	/**
	 * <pre>
	 * 重新引進 授信額度異動情形內容
	 * @param params PageParameters
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s16cl(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		boolean requery = params.getBoolean("requery"); // 會是TRUE

		L120S16C l120s16c = service1201.findL120s16cByOid(oid);
		if (l120s16c != null) {

			CapAjaxFormResult myForm2Result = DataParse.toResult(l120s16c);

			JSONObject jsonL120s16c = setL120s16c(l120s16c, mainId,
					l120s16c.getCustId(), l120s16c.getDupNo());
			if (jsonL120s16c != null) {

				// // 授信戶統編(本案)
				// myForm2Result.set("custId",
				// jsonL120s16c.optString("custId", ""));
				// // 授信戶重複序號(本案)
				// myForm2Result.set("dupNo", jsonL120s16c.optString("dupNo",
				// ""));
				//
				// // 授信戶區部別(本案)
				//
				// myForm2Result.set("typCd", jsonL120s16c.optString("typCd",
				// ""));
				//
				// // 授信戶(本案)
				// myForm2Result.set("custName",
				// jsonL120s16c.optString("custName", ""));

				myForm2Result.set("typCd", jsonL120s16c.optString("typCd", "")); // 已經FORMAT過的DBU/OBU

				// 前准額度
				myForm2Result.set("lvTotAmt",
						jsonL120s16c.optString("lvTotAmt", ""));

				// 餘額
				myForm2Result.set("blTotAmt",
						jsonL120s16c.optString("blTotAmt", ""));

				// 增加減少
				myForm2Result.set("incApplyTotAmt",
						jsonL120s16c.optString("incApplyTotAmt", ""));

				// 額度增減說明
				myForm2Result.set("incApplyMemo",
						jsonL120s16c.optString("incApplyMemo", ""));

				// 授信總額度
				myForm2Result.set("loanTotAmt",
						jsonL120s16c.optString("loanTotAmt", ""));

				// 其中擔保
				myForm2Result.set("assureTotAmt",
						jsonL120s16c.optString("assureTotAmt", ""));

			}

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			for (String fieldName : page01S16c) {
				myForm2Result.set(fieldName + adjFieldNmS16c,
						(String) myForm2Result.get(fieldName));
				myForm2Result.removeField(fieldName);
			}

			result.set("tLMS1401S07Form02", myForm2Result);

		}
		return result;
	}

	private JSONObject setL120s16c(L120S16C l120s16c, String mainId,
			String custId, String dupNo) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		JSONObject myForm2Result = null;

		if (l120s16c != null) {
			// 重新引進時，先塞舊資料
			myForm2Result = DataParse.toJSON(l120s16c);
		} else {
			myForm2Result = new JSONObject();
		}

		List<L140M01A> l140m01aList = lms1401Service
				.findL140m01aListByMainIdCustId(mainId, custId, dupNo,
						UtilConstants.Cntrdoc.ItemType.額度明細表);

		Map<String, String> proPertyMap = codeService.findByCodeType(
				"lms1405s02_proPerty", "zh_TW");
		StringBuffer addDscr = new StringBuffer("");
		StringBuffer minusDscr = new StringBuffer("");

		if (l140m01aList != null && !l140m01aList.isEmpty()) {

			BigDecimal totalBal = BigDecimal.ZERO;

			for (L140M01A l140m01a : l140m01aList) {
				if (l140m01a != null) {

					// 額度增減說明要顯示 新做、增額、取消、減額
					String[] temp = Util.trim(l140m01a.getProPerty()).split(
							"\\|");
					for (String perty : temp) {
						if (Util.equals(perty,
								UtilConstants.Cntrdoc.Property.新做)
								|| Util.equals(perty,
										UtilConstants.Cntrdoc.Property.增額)) {

							if (addDscr.toString().indexOf(
									Util.nullToSpace(proPertyMap.get(perty))) == -1) {
								addDscr.append(
										(Util.equals(addDscr.toString(), "") ? ""
												: "、"))
										.append(Util.nullToSpace(proPertyMap
												.get(perty)));
							}
						} else if (Util.equals(perty,
								UtilConstants.Cntrdoc.Property.取消)
								|| Util.equals(perty,
										UtilConstants.Cntrdoc.Property.減額)) {

							if (minusDscr.toString().indexOf(
									Util.nullToSpace(proPertyMap.get(perty))) == -1) {
								minusDscr
										.append((Util.equals(
												minusDscr.toString(), "") ? ""
												: "、")).append(
												Util.nullToSpace(proPertyMap
														.get(perty)));
							}

						}

					}

					String loanTotCurr = Util.trim(l140m01a.getLoanTotCurr());
					if (Util.equals(loanTotCurr, "")) {
						loanTotCurr = "TWD";
					}
					if (Util.notEquals(Util.trim(l140m01a.getBLCurr()), "")
							&& l140m01a.getBLAmt() != null) {
						if (Util.notEquals(Util.trim(l140m01a.getBLCurr()),
								loanTotCurr)) {
							BranchRate branchRate = lmsService
									.getBranchRate(user.getUnitNo());
							totalBal = totalBal
									.add(branchRate.toOtherAmt(
											Util.trim(l140m01a.getBLCurr()),
											loanTotCurr,
											l140m01a.getBLAmt() == null ? BigDecimal.ZERO
													: l140m01a.getBLAmt()));
						} else {
							totalBal = totalBal
									.add(l140m01a.getBLAmt() == null ? BigDecimal.ZERO
											: l140m01a.getBLAmt());
						}

					}
				}
			}

			if (Util.equals(addDscr.toString(), "")) {
				addDscr.append("增加");
			}
			if (Util.equals(minusDscr.toString(), "")) {
				minusDscr.append("減少");
			}

			for (L140M01A l140m01a : l140m01aList) {
				if (l140m01a != null) {

					// 連保人備註(本案)
					// myForm2Result.set("guarantorMemo",
					// Util.trim(l140m01a.getGuarantorMemo()));

					// 授信戶統編(本案)
					myForm2Result
							.put("custId", Util.trim(l140m01a.getCustId()));
					// 授信戶重複序號(本案)
					myForm2Result.put("dupNo", Util.trim(l140m01a.getDupNo()));

					// 授信戶區部別(本案)
					myForm2Result.put("typCd", Util.trim(l140m01a.getTypCd()));
					// 授信戶(本案)
					myForm2Result.put("custName",
							Util.trim(l140m01a.getCustName()));

					// 前准額度

					myForm2Result
							.put("lvTotAmt",
									Util.trim(l140m01a.getLVTotCurr())
											+ UtilConstants.Mark.SPACE

											+ ((l140m01a.getLVTotAmt() == null || BigDecimal.ZERO
													.compareTo(l140m01a
															.getLVTotAmt()) == 0) ? BigDecimal.ZERO
													: Util.trim(Util
															.nullToSpace(NumConverter
																	.addComma(l140m01a
																			.getLVTotAmt()
																			.divide(Util
																					.parseBigDecimal("1000"),
																					0,
																					BigDecimal.ROUND_HALF_UP))))));

					// 餘額
					myForm2Result
							.put("blTotAmt",
									Util.trim(l140m01a.getLoanTotCurr())
											+ UtilConstants.Mark.SPACE

											+ ((totalBal == null || BigDecimal.ZERO
													.compareTo(totalBal) == 0) ? BigDecimal.ZERO
													: Util.trim(Util
															.nullToSpace(NumConverter
																	.addComma(totalBal
																			.divide(Util
																					.parseBigDecimal("1000"),
																					0,
																					BigDecimal.ROUND_HALF_UP))))));

					// 增加減少
					// 增加
					String addStr = (l140m01a.getIncApplyTotAmt() == null || BigDecimal.ZERO
							.compareTo(l140m01a.getIncApplyTotAmt()) == 0) ? ""
							: Util.trim(l140m01a.getIncApplyTotCurr())
									+ UtilConstants.Mark.SPACE

									+ ((l140m01a.getIncApplyTotAmt() == null || BigDecimal.ZERO
											.compareTo(l140m01a
													.getIncApplyTotAmt()) == 0) ? BigDecimal.ZERO
											: Util.trim(Util.nullToSpace(NumConverter
													.addComma(l140m01a
															.getIncApplyTotAmt()
															.divide(Util
																	.parseBigDecimal("1000"),
																	0,
																	BigDecimal.ROUND_HALF_UP)))));

					String minusStr = (l140m01a.getDecApplyTotAmt() == null || BigDecimal.ZERO
							.compareTo(l140m01a.getDecApplyTotAmt()) == 0) ? ""
							: Util.trim(l140m01a.getDecApplyTotCurr())
									+ UtilConstants.Mark.SPACE

									+ ((l140m01a.getDecApplyTotAmt() == null || BigDecimal.ZERO
											.compareTo(l140m01a
													.getDecApplyTotAmt()) == 0) ? BigDecimal.ZERO
											: Util.trim(Util.nullToSpace(NumConverter
													.addComma(l140m01a
															.getDecApplyTotAmt()
															.divide(Util
																	.parseBigDecimal("1000"),
																	0,
																	BigDecimal.ROUND_HALF_UP)))));

					/*
					 * // 2021/02/20 授審 吳宜真襄理 : 金額部分倘為減少，請增列中括弧，以
					 * (TWD10,000,000) 顯示 minusStr = (Util.isEmpty(minusStr) ?
					 * minusStr : "(" + minusStr + ")"); String factAdjust = "";
					 * if (Util.notEquals(addStr, "") &&
					 * Util.notEquals(minusStr, "")) { factAdjust =
					 * (Util.equals(addStr, "") ? "" : addStr) + "／" +
					 * (Util.equals(addStr, "") ? "" : minusStr); } else if
					 * (Util.notEquals(addStr, "")) { factAdjust = addStr; }
					 * else if (Util.notEquals(minusStr, "")) { factAdjust =
					 * minusStr; }
					 */
					// J-110-0371 新版簽報書_主要申請敘作內容
					// 淨增加/減少欄：請直接呈現額度明細表中「授信額度合計」欄位中[現請額度-前准額度]
					// 前准額度合計
					String lvTotAmtStr = (l140m01a.getLVTotAmt() == null || BigDecimal.ZERO
							.compareTo(l140m01a.getLVTotAmt()) == 0) ? ""
							: Util.trim(l140m01a.getLVTotCurr())
									+ UtilConstants.Mark.SPACE
									+ ((l140m01a.getLVTotAmt() == null || BigDecimal.ZERO
											.compareTo(l140m01a.getLVTotAmt()) == 0) ? BigDecimal.ZERO
											: Util.trim(Util.nullToSpace(NumConverter.addComma(l140m01a
													.getLVTotAmt()
													.divide(Util
															.parseBigDecimal("1000"),
															0,
															BigDecimal.ROUND_HALF_UP)))));
					// 授信額度合計
					String loanTotAmtStr = (l140m01a.getLoanTotAmt() == null || BigDecimal.ZERO
							.compareTo(l140m01a.getLoanTotAmt()) == 0) ? ""
							: Util.trim(l140m01a.getLoanTotCurr())
									+ UtilConstants.Mark.SPACE
									+ ((l140m01a.getLoanTotAmt() == null || BigDecimal.ZERO
											.compareTo(l140m01a.getLoanTotAmt()) == 0) ? BigDecimal.ZERO
											: Util.trim(Util.nullToSpace(NumConverter.addComma(l140m01a
													.getLoanTotAmt()
													.divide(Util
															.parseBigDecimal("1000"),
															0,
															BigDecimal.ROUND_HALF_UP)))));
					String factAdjust = "";
					if (Util.isNotEmpty(lvTotAmtStr)
							&& Util.isNotEmpty(loanTotAmtStr)) {
						if (Util.equals(Util.trim(l140m01a.getLVTotCurr()),
								Util.trim(l140m01a.getLoanTotCurr()))) {
							BigDecimal subtraction = l140m01a.getLoanTotAmt()
									.subtract(l140m01a.getLVTotAmt());
							factAdjust = Util.trim(l140m01a.getLVTotCurr())
									+ UtilConstants.Mark.SPACE
									+ Util.trim(Util.nullToSpace(NumConverter.addComma(subtraction
											.abs()
											.divide(Util
													.parseBigDecimal("1000"),
													0, BigDecimal.ROUND_HALF_UP))));
							if (subtraction.compareTo(BigDecimal.ZERO) == -1) { // 負值
								factAdjust = ("(" + factAdjust + ")");
							}
						} else { // 幣別不同 - 呈現 "授信額度合計/前准額度合計"
							factAdjust = loanTotAmtStr + "／" + lvTotAmtStr;
						}
					} else { // 只有一個有值或都沒值
						factAdjust = lvTotAmtStr + loanTotAmtStr;
					}

					myForm2Result.put("incApplyTotAmt", factAdjust);

					// 額度增減說明

					String factAdjustMemo = "";
					if (Util.notEquals(addStr, "")
							&& Util.notEquals(minusStr, "")) {
						factAdjustMemo = (Util.equals(addStr, "") ? ""
								: addDscr.toString())
								+ "："
								+ addStr
								+ "，"
								+ (Util.equals(minusStr, "") ? "" : minusDscr
										.toString()) + "：" + minusStr;
					} else if (Util.notEquals(addStr, "")) {
						factAdjustMemo = (Util.equals(addStr, "") ? ""
								: addDscr.toString()) + "：" + addStr;
					} else if (Util.notEquals(minusStr, "")) {
						factAdjustMemo = (Util.equals(minusStr, "") ? ""
								: minusDscr.toString()) + "：" + minusStr;
					}

					myForm2Result.put("incApplyMemo", factAdjustMemo);

					// 授信總額度
					myForm2Result
							.put("loanTotAmt",
									Util.trim(l140m01a.getLoanTotCurr())
											+ UtilConstants.Mark.SPACE

											+ ((l140m01a.getLoanTotAmt() == null || BigDecimal.ZERO
													.compareTo(l140m01a
															.getLoanTotAmt()) == 0) ? BigDecimal.ZERO
													: Util.trim(Util
															.nullToSpace(NumConverter
																	.addComma(l140m01a
																			.getLoanTotAmt()
																			.divide(Util
																					.parseBigDecimal("1000"),
																					0,
																					BigDecimal.ROUND_HALF_UP))))));

					// 其中擔保
					myForm2Result
							.put("assureTotAmt",
									Util.trim(l140m01a.getAssureTotCurr())
											+ UtilConstants.Mark.SPACE

											+ ((l140m01a.getAssureTotAmt() == null || BigDecimal.ZERO
													.compareTo(l140m01a
															.getAssureTotAmt()) == 0) ? BigDecimal.ZERO
													: Util.trim(Util
															.nullToSpace(NumConverter
																	.addComma(l140m01a
																			.getAssureTotAmt()
																			.divide(Util
																					.parseBigDecimal("1000"),
																					0,
																					BigDecimal.ROUND_HALF_UP))))));

					break;
				}

			}
		}

		myForm2Result.put("typCd",
				getMessage("typCd." + Util.trim(myForm2Result.get("typCd"))));

		return myForm2Result;
	}

	/**
	 * 刪除授信額度異動情形
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL120s16c(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String sign = Util.trim(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(sign);
		// 刪除L120s06a和L120s06b List
		service1201.deleteListL120s16c(oidArray);
		// 印出刪除成功訊息
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}// ;

	/**
	 * 同步額度明細表列印順序
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult resetL120s16aPrintSeq(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);

		List<L140M01A> l140m01aList = lms1401Service
				.findL140m01aListByL120m01cMainId(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表);
		Map<String, Integer> custMap = new HashMap<String, Integer>();
		if (l140m01aList != null && !l140m01aList.isEmpty()) {
			for (L140M01A l140m01a : l140m01aList) {
				if (l140m01a != null) {

					String custId = Util.trim(l140m01a.getCustId());
					String dupNo = Util.trim(l140m01a.getDupNo());
					String cntrNo = Util.trim(l140m01a.getCntrNo());

					String fullKey = custId + "-" + dupNo;
					if (!custMap.containsKey(fullKey)) {
						custMap.put(fullKey, l140m01a.getPrintSeq());
					}

					L120S16A l120s16a = service1201
							.findL120s16aByMainIdCustIdCntrNo(mainId, custId,
									dupNo, cntrNo);
					if (l120s16a != null) {

						l120s16a.setPrintSeq(l140m01a.getPrintSeq());
						service1201.save(l120s16a);
					}
				}
			}
		}

		// 授信額度異動情形也要同步調整列印順序
		if (custMap != null && !custMap.isEmpty()) {
			for (String key : custMap.keySet()) {
				String custId = key.split("-")[0];
				String dupNo = key.split("-")[1];

				L120S16C l120s16c = service1201.findL120s16cByUniqueKey(mainId,
						custId, dupNo);
				if (l120s16c != null) {

					l120s16c.setPrintSeq(custMap.get(key));
					service1201.save(l120s16c);
				}

			}
		}

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}// ;

}
