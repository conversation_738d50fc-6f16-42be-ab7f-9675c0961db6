package com.mega.eloan.lms.batch.service.impl;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.LMSBATCHDao;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.model.LMSBATCH;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.eloan.lms.rpt.pages.LMS9511V01Page;
import com.mega.eloan.lms.rpt.report.LMS9511R01RptService;
import com.mega.eloan.lms.rpt.service.LMS9511R01XLSService;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import jxl.read.biff.BiffException;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授管處報表使用批次
 * </pre>
 * 
 * @since 2013/01/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/14,Vector,new
 *          </ul>
 */
@Service("RptLMSBatch2ServiceImpl")
public class RptLMSBatch2ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(RptLMSBatch2ServiceImpl.class);

	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	private static final long serialVersionUID = 1L;

	@Resource
	CodeTypeService codetypeService;// com.bcodetype

	@Resource
	LMSBATCHDao lmsbatchDao;

	@Resource
	LMSRPTDao lmsRptDao;

	@Resource
	LMS9511Service lms9511Service;

	@Resource
	LMS9511R01RptService lms9511r01Service;

	@Resource
	LMS9511R01XLSService lms9511r01XlsService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag = new JSONObject();
		LOGGER.info("RptBatch2ServiceImpl 開始========================");
		LOGGER.info("JSON=>" + json.toString());
		mag = WebBatchCode.RC_ERROR;
		String remark = null;
		String rptNoTemp = null;
		String docType = null;
		String dataStartDate = null;
		String dataEndDate = null;
		String unitNo = null;
		String userId = null;
		// J-105-0065-001 Web e-Loan 授信管理系統修改異常通報副知風控處說明文字及新增統計報表

		rptNoTemp = Util.trim(json.get("rptNo"));
		remark = Util.trim(json.get("remark"));
		docType = Util.trim(json.get("docType"));
		dataStartDate = Util.trim(json.get("dataStartDate"));
		dataEndDate = Util.trim(json.get("dataEndDate"));
		unitNo = Util.trim(json.get("unitNo"));
		userId = Util.trim(json.get("userId"));

		JSONObject request = json.getJSONObject("request");
		if (request != null) {
			if (request.containsKey("rptNo")) {
				rptNoTemp = Util.trim(request.getString("rptNo"));
			}
			if (request.containsKey("remark")) {
				remark = Util.trim(request.getString("remark"));
			}
			if (request.containsKey("docType")) {
				docType = Util.trim(request.getString("docType"));
			}
			if (request.containsKey("dataStartDate")) {
				dataStartDate = Util.trim(request.getString("dataStartDate"));
			}
			if (request.containsKey("dataEndDate")) {
				dataEndDate = Util.trim(request.getString("dataEndDate"));
			}
			if (request.containsKey("unitNo")) {
				unitNo = Util.trim(request.getString("unitNo"));
			}
			if (request.containsKey("userId")) {
				userId = Util.trim(request.getString("userId"));
			}

		}

		Map<String, String> docTypeMap = codetypeService
				.findByCodeType("lms9511v01_docType1");
		String rptStart = null;
		if (rptNoTemp.length() >= 3) {
			rptStart = rptNoTemp.substring(0, 3);
		}
		Map<String, String> map = null;
		if (Util.isEmpty(dataStartDate)) {
			if (map == null) {
				map = this.getDateData(rptStart, rptNoTemp);
			}
			dataStartDate = map.get("bgnDate");
		}
		if (Util.isEmpty(dataEndDate)) {
			if (map == null) {
				map = this.getDateData(rptStart, rptNoTemp);
			}
			dataEndDate = map.get("endDate");
		}
		String[] rptNos = rptNoTemp.split("\\^");
		// 新增批次報表BATCH
		for (String rptNo : rptNos) {
			LOGGER.info("rptNo = " + rptNo);
			if (UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單
					.equalsIgnoreCase(rptNo)) {
				Iterator<IBranch> allBranch = branchService.getAllBranch()
						.iterator();
				while (allBranch.hasNext()) {
					String nowBranch = allBranch.next().getBrNo();
					LOGGER.info("exporting " + nowBranch + "'s " + rptNo);
					mag = this.addLMSBatch(nowBranch, userId, rptNo, remark,
							docType, dataStartDate, dataEndDate, mag,
							docTypeMap);
				}
				// J-109-0115_10702_B1002 Web e-Loan新增海外分行自動產生「授信業務異常通報月報表」
			} else if (UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報
					.equalsIgnoreCase(rptNo) && "xxx".equals(unitNo)) {
				List<IBranch> obsbranchList = branchService
						.getBranchByFlag("2");
				for (IBranch branch : obsbranchList) {
					mag = this.addLMSBatch(branch.getBrNo(), userId, rptNo,
							remark, docType, dataStartDate, dataEndDate, mag,
							docTypeMap);
				}
			} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處, rptNo)
					|| Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處, rptNo)) {
				if(Util.isEmpty(remark)){ //J-110-0013 【CLS180R03B】=> select * from com.bschmain where schId='SLMS-00117'
					/*
					  	依 remarks : dataType=0/1/2 
					  	來決定查 DB 時，要採用 queryBrnoAmt.approveTime  或  queryBrnoAmt.useStartDate  或  queryBrnoAmt.lnStartDate
					*/
					remark = "dateType=0";
				}
				mag = this.addLMSBatch(unitNo, userId, rptNo, remark, docType,
						dataStartDate, dataEndDate, mag, docTypeMap);
			}else {
				mag = this.addLMSBatch(unitNo, userId, rptNo, remark, docType,
						dataStartDate, dataEndDate, mag, docTypeMap);
			}

			LOGGER.info(Util.trim(docTypeMap.get(rptNo))
					+ "RptBatchServiceImpl 結束========================");
		}
		LOGGER.info("RptBatchServiceImpl 結束========================");
		return mag;
	}

	/**
	 * 建立新增批次報表
	 * 
	 * @param mag
	 */
	private JSONObject addLMSBatch(String unitNo, String userId, String rptNo,
			String remark, String docType, String dataStartDate,
			String dataEndDate, JSONObject mag, Map<String, String> docTypeMap) {
		LOGGER.info("addLMSBatch 開始========================");
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		LMSBATCH batch = null;
		String mainId = null;
		Date bngDate = null;
		Date endDate = null;
		boolean execResult = false;
		boolean fileResult = false;
		Calendar calendar = Calendar.getInstance();
		Map<String, String> clsRptName = codetypeService
				.findByCodeType("lms9511v01_docType2");
		bngDate = lms9511Service.getStartDateForLMSRPT(dataStartDate);
		endDate = lms9511Service.getEndDateForLMSRPT(dataEndDate);
		// 檢核營運中心已敘做案件清單資料是否符合重新產生
		if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單)) {
			boolean checkAResult = lms9511Service.checkL180R02AData(unitNo,
					rptNo, bngDate, endDate);
			if (!checkAResult) {
				LOGGER.info(Util.trim(docTypeMap.get(rptNo)) + " "
						+ prop.getProperty("checkData.number01"));
				mag = WebBatchCode.RC_ERROR;
				// mag = JSONObject.fromObject("{\"rc\": 8, \"rcmsg\": \""+
				// Util.trim(docTypeMap.get(rptNo)) + " " +
				// prop.getProperty("checkData.number01") + "\"}");
				return mag;
			}
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表)) {
			int year = Integer.parseInt(TWNDate.toAD(bngDate).split("-")[0]);
			if (year > calendar.get(Calendar.YEAR)) {
				LOGGER.info(Util.trim(docTypeMap.get(rptNo)) + " "
						+ prop.getProperty("checkData.number02"));
				mag = WebBatchCode.RC_ERROR;
				// mag = JSONObject.fromObject("{\"rc\": 8, \"rcmsg\": \""+
				// Util.trim(docTypeMap.get(rptNo)) + " " +
				// prop.getProperty("checkData.number02") + "\"}");
				return mag;
			}
			List<LMSRPT> lmsRptList = lms9511Service.findLMSRptForRPTNO(rptNo);
			for (LMSRPT lmsRpt : lmsRptList) {
				int year2 = Integer.parseInt(TWNDate.toAD(lmsRpt.getDataDate())
						.split("-")[0]);
				if (year == year2) {
					LOGGER.info(Util.trim(docTypeMap.get(rptNo)) + " "
							+ prop.getProperty("checkData.number03"));
					mag = WebBatchCode.RC_ERROR;
					// mag = JSONObject.fromObject("{\"rc\": 8, \"rcmsg\": \""+
					// Util.trim(docTypeMap.get(rptNo)) + " " +
					// prop.getProperty("checkData.number03") + "\"}");
					return mag;
				}
			}
		} else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單)
				|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單)) {
			boolean checkAResult = lms9511Service.checkL180R01Data(remark,
					unitNo, userId, rptNo, bngDate, endDate);
			if (!checkAResult) {
				LOGGER.info(Util.trim(docTypeMap.get(rptNo)) + " "
						+ prop.getProperty("checkData.number04"));
				mag = WebBatchCode.RC_ERROR;
				// mag = JSONObject.fromObject("{\"rc\": 8, \"rcmsg\": \""+
				// Util.trim(docTypeMap.get(rptNo)) + " " +
				// prop.getProperty("checkData.number04") +"\"}");
				return mag;
			} else {

			}
		}else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸案件明細表)) {
			if(Util.isEmpty(bngDate) && Util.isEmpty(endDate)){
				String sysDate = TWNDate.toAD(CapDate.getCurrentTimestamp());
				String thisYear = String.valueOf(Util.parseInt(StringUtils
						.substring(sysDate, 0, 4)));
				Date batchBgnDate = CapDate.addMonth(
						CapDate.parseDate(StringUtils.substring(sysDate, 0, 7)
								+ "-01"), -1);
				Date batchEndDate = CapDate.shiftDays(
						CapDate.parseDate(StringUtils.substring(sysDate, 0, 7)
								+ "-01"), -1);
				bngDate = batchBgnDate ;
				endDate = batchEndDate ;
			}
		}else if (rptNo
				.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.特定金錢信託案件量統計報表)) {
			if(Util.isEmpty(bngDate) && Util.isEmpty(endDate)){
				String sysDate = TWNDate.toAD(CapDate.getCurrentTimestamp());
				String thisYear = String.valueOf(Util.parseInt(StringUtils
						.substring(sysDate, 0, 4)));
				Date batchBgnDate = CapDate.addMonth(
						CapDate.parseDate(StringUtils.substring(sysDate, 0, 7)
								+ "-01"), -1);
				Date batchEndDate = CapDate.shiftDays(
						CapDate.parseDate(StringUtils.substring(sysDate, 0, 7)
								+ "-01"), -1);
				bngDate = batchBgnDate ;
				endDate = batchEndDate ;
			}
		}
		try {
			// 確認是否有重複日期的報表
			mainId = lms9511Service.checkAndReplaceBatchData(getClass()
					.getSimpleName(), remark, unitNo, userId, rptNo, bngDate,
					endDate);
			// 新增報表
			batch = lms9511Service.addbatchData(remark, unitNo, rptNo, bngDate,
					endDate, mainId);
		} catch (RowsExceededException e1) {
			LOGGER.error("RowsExceededException = " + e1.getStackTrace());
			mag = WebBatchCode.RC_ERROR;
			return mag;
		} catch (BiffException e1) {
			LOGGER.error("BiffException = " + e1.getStackTrace());
			mag = WebBatchCode.RC_ERROR;
			return mag;
		} catch (WriteException e1) {
			LOGGER.error("WriteException = " + e1.getStackTrace());
			mag = WebBatchCode.RC_ERROR;
			return mag;
		} catch (CapMessageException e1) {
			LOGGER.error("CapMessageException = " + e1.getStackTrace());
			mag = WebBatchCode.RC_ERROR;
			return mag;
		} catch (IOException e1) {
			LOGGER.error("IOException = " + e1.getStackTrace());
			mag = WebBatchCode.RC_ERROR;
			return mag;
		} catch (URISyntaxException e1) {
			LOGGER.error("URISyntaxException = " + e1.getStackTrace());
			mag = WebBatchCode.RC_ERROR;
			return mag;
		} catch (Exception e1) {
			LOGGER.error("Exception = " + e1.getStackTrace());
			mag = WebBatchCode.RC_ERROR;
			return mag;
		}
//		
		if (batch != null) {
			boolean checkResult = false;
			if (Util.isNotEmpty(mainId)) {
				checkResult = true;
			}
			try {
				execResult = lms9511Service.execBatchData(batch.getMainId(),
						checkResult);
			} catch (Exception e) {
				mag = WebBatchCode.RC_ERROR;
				return mag;
			}
			if (execResult) {
				try {
					fileResult = lms9511Service.createFileForAddBatch(batch,
							clsRptName,"");
				} catch (Exception e) {
					mag = WebBatchCode.RC_ERROR;
					return mag;
				}
				if (rptNo
						.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表)) {
					List<LMSRPT> lmsRptList = lms9511Service
							.findLMSRptForRPTNO(rptNo);
					int year = 0;
					String mainIdR16 = null;
					for (LMSRPT lmsRpt : lmsRptList) {
						int year2 = Integer.parseInt(TWNDate.toAD(
								lmsRpt.getDataDate()).split("-")[0]);
						if (year2 > year) {
							year = year2;
							mainIdR16 = lmsRpt.getMainId();
						}
					}
					for (LMSRPT lmsRpt : lmsRptList) {
						if (Util.trim(mainIdR16).equals(lmsRpt.getMainId())) {
							lmsRpt.setNowRpt("Y");
						} else {
							lmsRpt.setNowRpt("N");
						}
					}
					lms9511Service.saveLmsRptList(lmsRptList);
				} else if (Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處, rptNo) 
						|| Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.全行地政士黑名單CSV, rptNo)
						|| Util.equals(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處, rptNo)
						) {
					int rtn = lms9511Service.send_CLS180R03B_to_Audit(batch.getMainId());
					if(rtn != 0){
						return WebBatchCode.RC_ERROR;
					}
				}
			}
		}
		if (fileResult) {
			mag = WebBatchCode.RC_SUCCESS;
		}

		LOGGER.info("addLMSBatch 結束========================");
		return mag;
	}
	
	
	/**
	 * 
	 * 
	 */
//	private JSONObject addLMSBatch_CLS180R52CSV(String unitNo, String userId, String rptNo,
//			String remark, String docType, String dataStartDate,
//			String dataEndDate, JSONObject mag, Map<String, String> docTypeMap) {
//		LOGGER.info("addLMSBatch_CLS180R52CSV 開始========================");
//		boolean fileResult = false;
//		
//		try {
//			fileResult = lms9511Service.createFileForAddBatch(batch,clsRptName);
//		} catch (Exception e) {
//			mag = WebBatchCode.RC_ERROR;
//			return mag;
//		}
//		
//		
//		if (fileResult) {
//			mag = WebBatchCode.RC_SUCCESS;
//		}
//		return mag;
//		
//	}
	

	/**
	 * 取得RPTNO所使用的起始和結束時間
	 * 
	 * @param rptStart
	 * @param rptNo
	 * @return
	 */
	private Map<String, String> getDateData(String rptStart, String rptNo) {
		LOGGER.info("取得起始結束時間開始");
		String bgnDate = null;
		String endDate = null;
		Date dataStartDate = LMSUtil.getExMonthFirstDay(-1);
		Date dataEndDate = LMSUtil.getExMonthLastDay(-1);
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -1);
		Date dataStartDate2 = LMSUtil.getExMonthFirstDay(-2);
		Date dataStartDate3 = LMSUtil.getExMonthFirstDay(1);
		Date dataEndDate2 = LMSUtil.getExMonthLastDay(1);
		Calendar cal2 = Calendar.getInstance();
		cal2.add(Calendar.DATE, -1 * 7);
		cal2.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
		Calendar cal3 = Calendar.getInstance();
		cal3.add(Calendar.DATE, -1 * 7);
		cal3.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		Date dataStartDate4 = LMSUtil.getExMonthFirstDay(-3);
		Calendar cal4 = Calendar.getInstance();
		cal4.add(Calendar.MONTH, -2);
		// J-109-0132_05097_B1001 e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表。
		Date dataStartDate7 = LMSUtil.getExMonthFirstDay(-6);
		Date dataEndDate7 = LMSUtil.getExMonthLastDay(-1);

		Map<String, String> map = new LinkedHashMap<String, String>();
		if (rptStart.equals("LMS")) {
			if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.已敘做授信案件清單)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信契約產生主辦聯貸案一覽表)) {
				// 已敘做授信案件清單
				// 授信契約產生主辦聯貸案一覽表
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心已敘做授信案件清單)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營業單位授信報案考核彙總表)) {
				// 營運中心已敘做授信案件清單
				// 營業單位授信報案考核彙總表
				bgnDate = TWNDate.toAD(dataStartDate).substring(0, 7);
				endDate = TWNDate.toAD(dataEndDate).substring(0, 7);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信契約已逾期控制表)) {
				// 授信契約已逾期控制表
				bgnDate = TWNDate.toAD(dataStartDate2);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.信保案件未動用屆期清單)) {
				// 信保案件未動用屆期清單
				bgnDate = TWNDate.toAD(dataStartDate3);
				endDate = TWNDate.toAD(dataEndDate2);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.金融機構辦理振興經濟非中小企業專案貸款)
					|| rptNo.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企業自行申請展延案件案件統計表)) {
				// 金融機構辦理振興經濟非中小企業專案貸款
				// 企業自行申請展延案件
				bgnDate = TWNDate.toAD(cal3.getTime());
				endDate = TWNDate.toAD(cal2.getTime());
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信案件統計表)) {
				// 授信案件統計表
				bgnDate = TWNDate.toAD(dataStartDate4).substring(0, 7);
				endDate = TWNDate.toAD(dataEndDate).substring(0, 7);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.營運中心每日授權外授信案件清單)) {
				// 區域中心每日授權外授信案件清單
				bgnDate = TWNDate.toAD(cal.getTime());
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.常董會報告事項彙總及申報案件數統計表)) {
				// 常董會報告事項彙總及申報案件數統計表
				bgnDate = CapDate.getCurrentDate("yyyy");
				endDate = CapDate.getCurrentDate("yyyy");
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.本行各營業單位各級授權範圍內承做授信案件統計表)) {
				// 本行各營業單位各級授權範圍內承做授信案件統計表
				bgnDate = CapDate.getCurrentDate("yyyy-MM");
				endDate = CapDate.getCurrentDate("yyyy-MM");
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.分行授信淨增加額度統計表)) {
				// 分行授信淨增加額度統計表
				bgnDate = TWNDate.toAD(dataStartDate4).substring(0, 7);
				endDate = TWNDate.toAD(dataEndDate).substring(0, 7);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.金融機構辦理振興經濟非中小企業專案貸款)) {
				// 振興經濟非中小企業專案貸款暨信用保證要點執行情形調查表
				bgnDate = TWNDate.toAD(dataStartDate4).substring(0, 7);
				endDate = TWNDate.toAD(dataEndDate).substring(0, 7);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信業務異常通報月報)) {
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.授信異常通報案件報送統計表)) {
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金聯貸新作案件統計表)) {
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金授信案件敘做情形及比較表)) {
				bgnDate = TWNDate.toAD(dataStartDate).substring(0, 7);
				endDate = TWNDate.toAD(dataEndDate).substring(0, 7);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.已核准授信額度辦理狀態通報彙總表)) {
				// 已核准授信額度辦理狀態通報彙總表
				bgnDate = TWNDate.toAD(dataStartDate4).substring(0, 7);
				endDate = TWNDate.toAD(dataEndDate).substring(0, 7);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金已核准授信額度辦理狀態通報彙總表)) {
				// 企金已核准授信額度辦理狀態通報彙總表
				bgnDate = TWNDate.toAD(dataStartDate4).substring(0, 7);
				endDate = TWNDate.toAD(dataEndDate).substring(0, 7);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企金共同行銷報表)) {
				bgnDate = TWNDate.toAD(Calendar.getInstance().getTime());
				endDate = TWNDate.toAD(Calendar.getInstance().getTime());
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.簽報階段都更危老業務統計表)) {
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.企業社會責任貸放情形統計表)) {
				Calendar sDay = Calendar.getInstance(); // 01/01
				sDay.add(Calendar.YEAR, -1);
				sDay.set(Calendar.MONTH, Calendar.JANUARY);
				sDay.set(Calendar.DAY_OF_MONTH, 1);
				Calendar eDay = Calendar.getInstance(); // 12/31
				eDay.add(Calendar.YEAR, -1);
				eDay.set(Calendar.MONTH, Calendar.DECEMBER);
				eDay.set(Calendar.DAY_OF_MONTH,
						eDay.getActualMaximum(Calendar.DAY_OF_MONTH));
				bgnDate = TWNDate.toAD(sDay.getTime());
				endDate = TWNDate.toAD(eDay.getTime());
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE3.董事會權限核定授信案件報表)) {
				Calendar today = Calendar.getInstance();
				Date todayTime = new Date();
				today.setTime(todayTime);
				int month = today.get(Calendar.MONTH);
				Calendar sDay = Calendar.getInstance();
				Calendar eDay = Calendar.getInstance();
				if (month < 7) { // 前一年後半 07/01~12/31
					sDay.add(Calendar.YEAR, -1);
					sDay.set(Calendar.MONTH, Calendar.JULY);
					sDay.set(Calendar.DAY_OF_MONTH, 1);
					bgnDate = TWNDate.toAD(sDay.getTime());
					eDay.add(Calendar.YEAR, -1);
					eDay.set(Calendar.MONTH, Calendar.DECEMBER);
					eDay.set(Calendar.DAY_OF_MONTH,
							eDay.getActualMaximum(Calendar.DAY_OF_MONTH));
					endDate = TWNDate.toAD(eDay.getTime());
				} else { // 今年前半 01/01~06/30
					sDay.set(Calendar.MONTH, Calendar.JANUARY);
					sDay.set(Calendar.DAY_OF_MONTH, 1);
					bgnDate = TWNDate.toAD(sDay.getTime());
					eDay.set(Calendar.MONTH, Calendar.JUNE);
					eDay.set(Calendar.DAY_OF_MONTH,
							eDay.getActualMaximum(Calendar.DAY_OF_MONTH));
					endDate = TWNDate.toAD(eDay.getTime());
				}
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.法令遵循自評授信案件明細報表)) {
				// J-109-0132_05097_B1001
				// e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表。
				bgnDate = TWNDate.toAD(dataStartDate7);
				endDate = TWNDate.toAD(dataEndDate7);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.兆元振興融資方案辦理情形總表)) {
				// J-109-0132_05097_B1001
				// e-Loan授信系統新增「法令遵循自評檢核表」之抽測筆數所需之各檢核項目授信案件明細報表。
				bgnDate = CapDate.getCurrentDate("yyyy-MM-dd");
				endDate = CapDate.getCurrentDate("yyyy-MM-dd");
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.小規模營業人授信異常通報表)) {
				// J-109-0315_05097_B1001 Web
				// e-loan企金授信新增小規模營業人央行C方案授信案件之異常通報上一個月獲核定之異動名單
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE1.境內法人於國際金融業務分行辦理外幣授信業務報表)) {
				// J-110-0049_05097_B1001 Web
				// e-Loan企金授信增加「境內法人於國際金融業務分行辦理外幣授信業務報表」
				String lms180R63_bgnDate = Util.trim(lmsService
						.getSysParamDataValue("LMS_LMS180R63_BGNDATE"));
				if (Util.notEquals(lms180R63_bgnDate, "")) {
					bgnDate = lms180R63_bgnDate;
				} else {
					bgnDate = "2020-01-01";
				}

				endDate = TWNDate.toAD(dataEndDate);
			}else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.歡喜信貸案件明細表)) {
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			}
		} else if (rptStart.equals("CLS")) {
			String cut_rptNo = rptNo.substring(0, 9);
			// Vector done
			if (cut_rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已敘作消金案件清單)) {
				// 已敘作消金案件清單
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (cut_rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.已核准尚未撥款案件報表)) {
				// 已核准尚未撥款案件報表
				bgnDate = TWNDate.toAD(cal4.getTime()).substring(0, 7);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內分行敘做房屋貸款月報_稽核處)) {
				// 授權內分行敘做房屋貸款月報_稽核處 => 若未特別指定參數，則採用 {前一個月} 的起迄日
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			} else if (rptNo
					.equalsIgnoreCase(UtilConstants.RPTREPORT.DOCTYPE2.授權內已敘做個人消費金融業務月報表_稽核處)) {
				// 授權內分行敘做房屋貸款月報_稽核處 => 若未特別指定參數，則採用 {前一個月} 的起迄日
				bgnDate = TWNDate.toAD(dataStartDate);
				endDate = TWNDate.toAD(dataEndDate);
			}
		}
		map.put("bgnDate", bgnDate);
		map.put("endDate", endDate);
		LOGGER.info("bgnDate = " + bgnDate);
		LOGGER.info("endDate = " + endDate);
		LOGGER.info("取得起始結束時間結束");
		return map;
	}
}
