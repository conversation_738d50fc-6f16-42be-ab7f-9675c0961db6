---------------------------------------------------------
-- LMS.L140M01A_BF 額度明細表(變動前)
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M01A_BF;
CREATE TABLE LMS.L140M01A_BF (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	HEADITEM1_BF  CHAR(1)      ,
	APLCURR_BF    CHAR(3)      ,
	CURRENTAPPLY_BF DECIMAL(17,2),
	REUSE_BF      CHAR(1)      ,
	GUTPERCENT_BF DECIMAL(5,2) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140M01A_BF PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M01A_BF01;
CREATE UNIQUE INDEX LMS.XL140M01A_BF01 ON LMS.L140M01A_BF (MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M01A_BF IS '額度明細表(變動前)';
COMMENT ON LMS.L140M01A_BF (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	HEADITEM1_BF  IS '本額度有無送保', 
	APLCURR_BF    IS '現請額度幣別', 
	CURRENTAPPLY_BF IS '現請額度', 
	REUSE_BF      IS '循環/不循環', 
	GUTPERCENT_BF IS '保證成數', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
