<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
		<script type="text/javascript">loadScript('pagejs/ctr/LMS9990M01Page');</script>
			<div class="button-menu funcContainer" id="buttonPanel">		
                <button id="btnAdd" >
                	<span class="ui-icon ui-icon-jcs-13"></span>
					<th:block th:text="#{'button.add'}"><!--新增--></th:block>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}"><!--離開--></th:block>
				</button>
            </div>
			<form id="selectBossForm">
				<div id="showTitle">
					    <div class=" tit2 color-black" >
						<th:block th:text="#{'L999M01AM01.showTitle01'}"><!--主要借款人--></th:block>：<span name="custData" id="custData" class="color-blue"></span>
					    <br/>
						<th:block th:text="#{'L999M01AM01.showTitle02'}"><!--簽報書案號--></th:block>：<span name="caseNo" id="caseNo" class="color-blue"></span>
					    </div>
				</div>
			</form>
			<div class="tabs doc-tabs">
                <ul>
                	<li id="tabs_1" > <a href="#tab-01" goto="01"><b><th:block th:text="#{'L999M01AM01.title01'}"><!--  約據書清單--></th:block></b></a></li>
					<li><a href="#tab-02" goto="02"><b><th:block th:text="#{'L999M01AM01.title02'}"><!--  附加檔案--></th:block></b></a></li>
                </ul>
                <div class="tabCtx-warp">
                		<div id="tabs-00" th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
				</div>
			</div>
			<div id="amountBox" class="content" style="display:none;">
			  	<form id="addForm">
					<label><b class="text-blue"><th:block th:text="#{'L999M01AM01.addContractType'}"><!--種類：--></th:block>：</b>
					<!-- <select id="contractType" name="contractType" comboKey="lms9990m01_contractType" combotype="4" space="true"/> -->
					</label>
					<br />
					<input type="checkbox" name="contractType" id="contractType" />
		    		<p />
					<label><b class="text-blue"><th:block th:text="#{'L999M01AM01.addL140M01A'}"><!--選擇額度明細表：--></th:block>：</b></label>
		    		<div id="amountGridViewBox" ></div>  
				</form>
			</div>
		</th:block>
    </body>
</html>
