/* 
 * CLS1131ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.cls.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.mega.eloan.lms.dao.*;
import com.mega.eloan.lms.model.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.utils.SQLParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.cls.service.CLS1130Service;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
@Service
@Qualifier("CLS1130Service")
public class CLS1130ServiceImpl extends AbstractEloandbJdbc implements
		CLS1130Service {

	private static final Logger logger = LoggerFactory
			.getLogger(CLS1130ServiceImpl.class);
	@Resource
	C101M01ADao c101m01aDao;

	@Resource
	C101S01ADao c101s01aDao;

	@Resource
	C101S01BDao c101s01bDao;

	@Resource
	C101S01CDao c101s01cDao;

	@Resource
	C101S01DDao c101s01dDao;

	@Resource
	C101S01EDao c101s01eDao;

	@Resource
	C101S01FDao c101s01fDao;

	@Resource
	C101S01GDao c101s01gDao;
	
	@Resource
	C101S01G_NDao c101s01g_nDao;

	@Resource
	C101S01HDao c101s01hDao;

	@Resource
	C101S01IDao c101s01iDao;

	@Resource
	C101S01JDao c101s01jDao;

	@Resource
	C101S01KDao c101s01kDao;

	@Resource
	C101S01LDao c101s01lDao;

	@Resource
	C101S01MDao c101s01mDao;

	@Resource
	C101S01NDao c101s01nDao;

	@Resource
	C101S01ODao c101s01oDao;

	@Resource
	C101S01PDao c101s01pDao;

	@Resource
	C101S01QDao c101s01qDao;

	@Resource
	C101S01Q_NDao c101s01q_nDao;
	
	@Resource
	C101S01RDao c101s01rDao;
	
	@Resource
	C101S01R_NDao c101s01r_nDao;
	
	@Resource
	C101S01SDao c101s01sDao;
	
	@Resource
	C101S01UDao c101s01uDao;
	
	@Resource
	C101S01VDao c101s01vDao;

	@Resource
	C101S01WDao c101s01wDao;

	@Resource
	C120S01WDao c120s01wDao;

	@Resource
	C101S01XDao c101s01xDao;
	
	@Resource
	C101S01ZDao c101s01zDao;
	
	// C120 --------------------------------------
	@Resource
	C120M01ADao c120m01aDao;

	@Resource
	C120S01ADao c120s01aDao;

	@Resource
	C120S01BDao c120s01bDao;

	@Resource
	C120S01CDao c120s01cDao;

	@Resource
	C120S01DDao c120s01dDao;

	@Resource
	C120S01EDao c120s01eDao;

	@Resource
	C120S01FDao c120s01fDao;

	@Resource
	C120S01GDao c120s01gDao;

	@Resource
	C120S01HDao c120s01hDao;

	@Resource
	C120S01IDao c120s01iDao;

	@Resource
	C120S01JDao c120s01jDao;

	@Resource
	C120S01KDao c120s01kDao;

	@Resource
	C120S01LDao c120s01lDao;

	@Resource
	C120S01MDao c120s01mDao;

	@Resource
	C120S01NDao c120s01nDao;

	@Resource
	C120S01ODao c120s01oDao;

	@Resource
	C120S01PDao c120s01pDao;

	@Resource
	C120S01QDao c120s01qDao;
	
	@Resource
	C120S01RDao c120s01rDao;
		
	@Resource
	C120S01SDao c120s01sDao;
	
	@Resource
	C120S01TDao c120s01tDao;
	
	@Resource
	C120S01UDao c120s01uDao;
	
	@Resource
	C120S01VDao c120s01vDao;

	@Resource
	C120S01XDao c120s01xDao;
	
	@Resource
	L120M01ADao l120m01aDao;
	
	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L161S01DDao l161s01dDao;

	@Resource
	C101S01YDao c101s01yDao;

	@Resource
	C120S01YDao c120s01yDao;
	
	@Resource
	C120S01ZDao c120s01zDao;
	
	@Resource
	C101S02ADao c101s02aDao;

	@Resource
	C120S02ADao c120s02aDao;
	
	@Resource
	C160S02ADao c160s02aDao;
	
	@Resource
	C101S02BDao c101s02bDao;
	
	@Resource
	C120S02BDao c120s02bDao;
	
	@Resource
	C101S02SDao c101s02sDao;
	
	@Resource
	C120S02SDao c120s02sDao;

	@Resource
	C101S02CDao c101s02cDao;

	@Resource
	C120S02CDao c120s02cDao;
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CLS1130ServiceImpl.save", e);
				}

				if (model instanceof C101M01A) {
					// add default import flag
					C101M01A c101m01a = ((C101M01A) model);
					if (LMSUtil.check2(c101m01a.getCustId())) {
						c101m01a.setNaturalFlag(UtilConstants.DEFAULT.是);
					} else {
						c101m01a.setNaturalFlag(UtilConstants.DEFAULT.否);
						c101m01a.setImportFlag(UtilConstants.DEFAULT.是);
					}
					c101m01aDao.save(c101m01a);
				} else if (model instanceof C101S01A) {
					c101s01aDao.save(((C101S01A) model));
				} else if (model instanceof C101S01B) {
					c101s01bDao.save(((C101S01B) model));
				} else if (model instanceof C101S01C) {
					c101s01cDao.save(((C101S01C) model));
				} else if (model instanceof C101S01D) {
					c101s01dDao.save(((C101S01D) model));
				} else if (model instanceof C101S01E) {
					c101s01eDao.save(((C101S01E) model));
				} else if (model instanceof C101S01F) {
					c101s01fDao.save(((C101S01F) model));
				} else if (model instanceof C101S01G) {
					c101s01gDao.save(((C101S01G) model));
				} else if (model instanceof C101S01H) {
					c101s01hDao.save(((C101S01H) model));
				} else if (model instanceof C101S01I) {
					c101s01iDao.save(((C101S01I) model));
				} else if (model instanceof C101S01J) {
					c101s01jDao.save(((C101S01J) model));
				} else if (model instanceof C101S01K) {
					c101s01kDao.save(((C101S01K) model));
				} else if (model instanceof C101S01L) {
					c101s01lDao.save(((C101S01L) model));
				} else if (model instanceof C101S01M) {
					c101s01mDao.save(((C101S01M) model));
				} else if (model instanceof C101S01N) {
					c101s01nDao.save(((C101S01N) model));
				} else if (model instanceof C101S01O) {
					c101s01oDao.save(((C101S01O) model));
				} else if (model instanceof C101S01P) {
					c101s01pDao.save(((C101S01P) model));
				} else if (model instanceof C101S01Q) {
					c101s01qDao.save(((C101S01Q) model));
				} else if (model instanceof C101S01R) {
					c101s01rDao.save(((C101S01R) model));
				} else if (model instanceof C101S01S) {
					c101s01sDao.save(((C101S01S) model));
				} else if (model instanceof C101S01U) {
					c101s01uDao.save(((C101S01U) model));
				} else if (model instanceof C101S01V) {
					c101s01vDao.save(((C101S01V) model));
				} else if (model instanceof C101S01W) {
					c101s01wDao.save(((C101S01W) model));
				} else if (model instanceof C101S01X) {
					c101s01xDao.save(((C101S01X) model));
				} else if (model instanceof C101S01Y) {
					c101s01yDao.save(((C101S01Y) model));
				} else if (model instanceof C101S01Z) {
					c101s01zDao.save(((C101S01Z) model));
				} else if (model instanceof C101S02A) {
					c101s02aDao.save(((C101S02A) model));
				} else if (model instanceof C101S02B) {
					c101s02bDao.save(((C101S02B) model));
				} else if (model instanceof C101S02S) {
					c101s02sDao.save(((C101S02S) model));
				} else if (model instanceof C101S01G_N) {
					c101s01g_nDao.save(((C101S01G_N) model));
				} else if (model instanceof C101S01Q_N) {
					c101s01q_nDao.save(((C101S01Q_N) model));
				} else if (model instanceof C101S01R_N) {
					c101s01r_nDao.save(((C101S01R_N) model));
				} else if (model instanceof C101S02C) {
					c101s02cDao.save(((C101S02C) model));
				}
				// C120 --------------------------------------
				else if (model instanceof C120M01A) {
					c120m01aDao.save(((C120M01A) model));
				} else if (model instanceof C120S01A) {
					c120s01aDao.save(((C120S01A) model));
				} else if (model instanceof C120S01B) {
					c120s01bDao.save(((C120S01B) model));
				} else if (model instanceof C120S01C) {
					c120s01cDao.save(((C120S01C) model));
				} else if (model instanceof C120S01D) {
					c120s01dDao.save(((C120S01D) model));
				} else if (model instanceof C120S01E) {
					c120s01eDao.save(((C120S01E) model));
				} else if (model instanceof C120S01F) {
					c120s01fDao.save(((C120S01F) model));
				} else if (model instanceof C120S01G) {
					c120s01gDao.save(((C120S01G) model));
				} else if (model instanceof C120S01H) {
					c120s01hDao.save(((C120S01H) model));
				} else if (model instanceof C120S01I) {
					c120s01iDao.save(((C120S01I) model));
				} else if (model instanceof C120S01J) {
					c120s01jDao.save(((C120S01J) model));
				} else if (model instanceof C120S01K) {
					c120s01kDao.save(((C120S01K) model));
				} else if (model instanceof C120S01L) {
					c120s01lDao.save(((C120S01L) model));
				} else if (model instanceof C120S01M) {
					c120s01mDao.save(((C120S01M) model));
				} else if (model instanceof C120S01N) {
					c120s01nDao.save(((C120S01N) model));
				} else if (model instanceof C120S01O) {
					c120s01oDao.save(((C120S01O) model));
				} else if (model instanceof C120S01P) {
					c120s01pDao.save(((C120S01P) model));
				} else if (model instanceof C120S01Q) {
					c120s01qDao.save(((C120S01Q) model));
				} else if (model instanceof C120S01R) {
					c120s01rDao.save(((C120S01R) model));
				} else if (model instanceof C120S01S) {
					c120s01sDao.save(((C120S01S) model));
				} else if (model instanceof C120S01U) {
					c120s01uDao.save(((C120S01U) model));
				} else if (model instanceof C120S01X) {
					c120s01xDao.save(((C120S01X) model));
				} else if (model instanceof L120M01A) {
					l120m01aDao.save(((L120M01A) model));
				} else if (model instanceof L161S01D) {
					l161s01dDao.save(((L161S01D) model));
				} else if (model instanceof C120S01Y) {
					c120s01yDao.save(((C120S01Y) model));
				} else if (model instanceof C120S01Z) {
					c120s01zDao.save(((C120S01Z) model));
				} else if (model instanceof C120S02A) {
					c120s02aDao.save(((C120S02A) model));
				} else if (model instanceof C120S02B) {
					c120s02bDao.save(((C120S02B) model));
				} else if (model instanceof C120S02S) {
					c120s02sDao.save(((C120S02S) model));
				} else if (model instanceof C120S02C) {
					c120s02cDao.save(((C120S02C) model));
				}
			}
		}
	}

	@Override
	public void save(List<GenericBean> list) {
		for (GenericBean model : list) {
			save(model);
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof C101M01A) {
				c101m01aDao.delete(((C101M01A) model));
			} else if (model instanceof C101S01A) {
				c101s01aDao.delete(((C101S01A) model));
			} else if (model instanceof C101S01B) {
				c101s01bDao.delete(((C101S01B) model));
			} else if (model instanceof C101S01C) {
				c101s01cDao.delete(((C101S01C) model));
			} else if (model instanceof C101S01D) {
				c101s01dDao.delete(((C101S01D) model));
			} else if (model instanceof C101S01E) {
				c101s01eDao.delete(((C101S01E) model));
			} else if (model instanceof C101S01F) {
				c101s01fDao.delete(((C101S01F) model));
			} else if (model instanceof C101S01G) {
				c101s01gDao.delete(((C101S01G) model));
			} else if (model instanceof C101S01H) {
				c101s01hDao.delete(((C101S01H) model));
			} else if (model instanceof C101S01I) {
				c101s01iDao.delete(((C101S01I) model));
			} else if (model instanceof C101S01J) {
				c101s01jDao.delete(((C101S01J) model));
			} else if (model instanceof C101S01K) {
				c101s01kDao.delete(((C101S01K) model));
			} else if (model instanceof C101S01L) {
				c101s01lDao.delete(((C101S01L) model));
			} else if (model instanceof C101S01M) {
				c101s01mDao.delete(((C101S01M) model));
			} else if (model instanceof C101S01N) {
				c101s01nDao.delete(((C101S01N) model));
			} else if (model instanceof C101S01O) {
				c101s01oDao.delete(((C101S01O) model));
			} else if (model instanceof C101S01P) {
				c101s01pDao.delete(((C101S01P) model));
			} else if (model instanceof C101S01Q) {
				c101s01qDao.delete(((C101S01Q) model));
			} else if (model instanceof C101S01R) {
				c101s01rDao.delete(((C101S01R) model));
			} else if (model instanceof C101S01S) {
				c101s01sDao.delete(((C101S01S) model));
			} else if (model instanceof C101S01U) {
				c101s01uDao.delete(((C101S01U) model));
			} else if (model instanceof C101S01V) {
				c101s01vDao.delete(((C101S01V) model));
			} else if (model instanceof C101S01W) {
				c101s01wDao.delete(((C101S01W) model));
			} else if (model instanceof C101S01X) {
				c101s01xDao.delete(((C101S01X) model));
			} else if (model instanceof C101S01Y) {
				c101s01yDao.delete(((C101S01Y) model));
			} else if (model instanceof C101S01Z) {
				c101s01zDao.delete(((C101S01Z) model));
			} else if (model instanceof C101S02A) {
				c101s02aDao.delete(((C101S02A) model));
			} else if (model instanceof C101S02B) {
				c101s02bDao.delete(((C101S02B) model));
			} else if (model instanceof C101S02C) {
				c101s02cDao.delete(((C101S02C) model));
			} else if (model instanceof C101S02S) {
				c101s02sDao.delete(((C101S02S) model));
			} else if (model instanceof C101S01G_N) {
				c101s01g_nDao.delete(((C101S01G_N) model));
			} else if (model instanceof C101S01Q_N) {
				c101s01q_nDao.delete(((C101S01Q_N) model));
			} else if (model instanceof C101S01R_N) {
				c101s01r_nDao.delete(((C101S01R_N) model));
			} 
			// C120 --------------------------------------
			else if (model instanceof C120M01A) {
				c120m01aDao.delete(((C120M01A) model));
			} else if (model instanceof C120S01A) {
				c120s01aDao.delete(((C120S01A) model));
			} else if (model instanceof C120S01B) {
				c120s01bDao.delete(((C120S01B) model));
			} else if (model instanceof C120S01C) {
				c120s01cDao.delete(((C120S01C) model));
			} else if (model instanceof C120S01D) {
				c120s01dDao.delete(((C120S01D) model));
			} else if (model instanceof C120S01E) {
				c120s01eDao.delete(((C120S01E) model));
			} else if (model instanceof C120S01F) {
				c120s01fDao.delete(((C120S01F) model));
			} else if (model instanceof C120S01G) {
				c120s01gDao.delete(((C120S01G) model));
			} else if (model instanceof C120S01H) {
				c120s01hDao.delete(((C120S01H) model));
			} else if (model instanceof C120S01I) {
				c120s01iDao.delete(((C120S01I) model));
			} else if (model instanceof C120S01J) {
				c120s01jDao.delete(((C120S01J) model));
			} else if (model instanceof C120S01K) {
				c120s01kDao.delete(((C120S01K) model));
			} else if (model instanceof C120S01L) {
				c120s01lDao.delete(((C120S01L) model));
			} else if (model instanceof C120S01M) {
				c120s01mDao.delete(((C120S01M) model));
			} else if (model instanceof C120S01N) {
				c120s01nDao.delete(((C120S01N) model));
			} else if (model instanceof C120S01O) {
				c120s01oDao.delete(((C120S01O) model));
			} else if (model instanceof C120S01P) {
				c120s01pDao.delete(((C120S01P) model));
			} else if (model instanceof C120S01Q) {
				c120s01qDao.delete(((C120S01Q) model));
			} else if (model instanceof C120S01R) {
				c120s01rDao.delete(((C120S01R) model));
			} else if (model instanceof C120S01S) {
				c120s01sDao.delete(((C120S01S) model));
			} else if (model instanceof C120S01T) {
				c120s01tDao.delete(((C120S01T) model));
			} else if (model instanceof C120S01U) {
				c120s01uDao.delete(((C120S01U) model));
			} else if (model instanceof C120S01V) {
				c120s01vDao.delete(((C120S01V) model));
			} else if (model instanceof C120S01X) {
				c120s01xDao.delete(((C120S01X) model));
			} else if (model instanceof C120S01Y) {
				c120s01yDao.delete(((C120S01Y) model));
			} else if (model instanceof C120S01Z) {
				c120s01zDao.delete(((C120S01Z) model));
			} else if (model instanceof C120S02A) {
				c120s02aDao.delete(((C120S02A) model));
			} else if (model instanceof C120S02B) {
				c120s02bDao.delete(((C120S02B) model));
			} else if (model instanceof C120S02C) {
				c120s02cDao.delete(((C120S02C) model));
			} else if (model instanceof C120S02S) {
				c120s02sDao.delete(((C120S02S) model));
			}
		}
	}

	@Override
	public void deleteByJPQL(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof C101M01A) {
				c101m01aDao.deleteByOid(((C101M01A) model).getOid());
			} else if (model instanceof C101S01A) {
				c101s01aDao.deleteByOid(((C101S01A) model).getOid());
			} else if (model instanceof C101S01B) {
				c101s01bDao.deleteByOid(((C101S01B) model).getOid());
			} else if (model instanceof C101S01C) {
				c101s01cDao.deleteByOid(((C101S01C) model).getOid());
			} else if (model instanceof C101S01D) {
				c101s01dDao.deleteByOid(((C101S01D) model).getOid());
			} else if (model instanceof C101S01E) {
				c101s01eDao.deleteByOid(((C101S01E) model).getOid());
			} else if (model instanceof C101S01F) {
				c101s01fDao.deleteByOid(((C101S01F) model).getOid());
			} else if (model instanceof C101S01G) {
				c101s01gDao.deleteByOid(((C101S01G) model).getOid());
			} else if (model instanceof C101S01H) {
				c101s01hDao.deleteByOid(((C101S01H) model).getOid());
			} else if (model instanceof C101S01I) {
				c101s01iDao.deleteByOid(((C101S01I) model).getOid());
			} else if (model instanceof C101S01J) {
				c101s01jDao.deleteByOid(((C101S01J) model).getOid());
			} else if (model instanceof C101S01K) {
				c101s01kDao.deleteByOid(((C101S01K) model).getOid());
			} else if (model instanceof C101S01L) {
				c101s01lDao.deleteByOid(((C101S01L) model).getOid());
			} else if (model instanceof C101S01M) {
				c101s01mDao.deleteByOid(((C101S01M) model).getOid());
			} else if (model instanceof C101S01N) {
				c101s01nDao.deleteByOid(((C101S01N) model).getOid());
			} else if (model instanceof C101S01O) {
				c101s01oDao.deleteByOid(((C101S01O) model).getOid());
			} else if (model instanceof C101S01P) {
				c101s01pDao.deleteByOid(((C101S01P) model).getOid());
			} else if (model instanceof C101S01Q) {
				c101s01qDao.deleteByOid(((C101S01Q) model).getOid());
			} else if (model instanceof C101S01R) {
				c101s01rDao.deleteByOid(((C101S01R) model).getOid());
			} else if (model instanceof C101S01S) {
				c101s01sDao.deleteByOid(((C101S01S) model).getOid());
			} else if (model instanceof C101S01U) {
				c101s01uDao.deleteByOid(((C101S01U) model).getOid());
			} else if (model instanceof C101S01V) {
				c101s01vDao.deleteByOid(((C101S01V) model).getOid());
			} else if (model instanceof C101S01X) {
				c101s01xDao.deleteByOid(((C101S01X) model).getOid());
			} else if (model instanceof C101S01Y) {
				c101s01yDao.deleteByOid(((C101S01Y) model).getOid());
			} else if (model instanceof C101S01Z) {
				c101s01zDao.deleteByOid(((C101S01Z) model).getOid());
			} else if (model instanceof C101S02A) {
				c101s02aDao.deleteByOid(((C101S02A) model).getOid());
			} else if (model instanceof C101S02B) {
				c101s02bDao.deleteByOid(((C101S02B) model).getOid());
			} else if (model instanceof C101S02C) {
				c101s02cDao.deleteByOid(((C101S02C) model).getOid());
			}
			// C120 --------------------------------------
			else if (model instanceof C120M01A) {
				c120m01aDao.deleteByOid(((C120M01A) model).getOid());
			} else if (model instanceof C120S01A) {
				c120s01aDao.deleteByOid(((C120S01A) model).getOid());
			} else if (model instanceof C120S01B) {
				c120s01bDao.deleteByOid(((C120S01B) model).getOid());
			} else if (model instanceof C120S01C) {
				c120s01cDao.deleteByOid(((C120S01C) model).getOid());
			} else if (model instanceof C120S01D) {
				c120s01dDao.deleteByOid(((C120S01D) model).getOid());
			} else if (model instanceof C120S01E) {
				c120s01eDao.deleteByOid(((C120S01E) model).getOid());
			} else if (model instanceof C120S01F) {
				c120s01fDao.deleteByOid(((C120S01F) model).getOid());
			} else if (model instanceof C120S01G) {
				c120s01gDao.deleteByOid(((C120S01G) model).getOid());
			} else if (model instanceof C120S01H) {
				c120s01hDao.deleteByOid(((C120S01H) model).getOid());
			} else if (model instanceof C120S01I) {
				c120s01iDao.deleteByOid(((C120S01I) model).getOid());
			} else if (model instanceof C120S01J) {
				c120s01jDao.deleteByOid(((C120S01J) model).getOid());
			} else if (model instanceof C120S01K) {
				c120s01kDao.deleteByOid(((C120S01K) model).getOid());
			} else if (model instanceof C120S01L) {
				c120s01lDao.deleteByOid(((C120S01L) model).getOid());
			} else if (model instanceof C120S01M) {
				c120s01mDao.deleteByOid(((C120S01M) model).getOid());
			} else if (model instanceof C120S01N) {
				c120s01nDao.deleteByOid(((C120S01N) model).getOid());
			} else if (model instanceof C120S01O) {
				c120s01oDao.deleteByOid(((C120S01O) model).getOid());
			} else if (model instanceof C120S01P) {
				c120s01pDao.deleteByOid(((C120S01P) model).getOid());
			} else if (model instanceof C120S01Q) {
				c120s01qDao.deleteByOid(((C120S01Q) model).getOid());
			} else if (model instanceof C120S01R) {
				c120s01rDao.deleteByOid(((C120S01R) model).getOid());
			} else if (model instanceof C120S01S) {
				c120s01sDao.deleteByOid(((C120S01S) model).getOid());
			} else if (model instanceof C120S01U) {
				c120s01uDao.deleteByOid(((C120S01U) model).getOid());
			} else if (model instanceof C120S01V) {
				c120s01vDao.deleteByOid(((C120S01V) model).getOid());
			} else if (model instanceof C120S01X) {
				c120s01xDao.deleteByOid(((C120S01X) model).getOid());
			} else if (model instanceof C120S01Y) {
				c120s01yDao.deleteByOid(((C120S01Y) model).getOid());
			} else if (model instanceof C120S01Z) {
				c120s01zDao.deleteByOid(((C120S01Z) model).getOid());
			} else if (model instanceof C120S02A) {
				c120s02aDao.deleteByOid(((C120S02A) model).getOid());
			} else if (model instanceof C120S02B) {
				c120s02bDao.deleteByOid(((C120S02B) model).getOid());
			} else if (model instanceof C120S02C) {
				c120s02cDao.deleteByOid(((C120S02C) model).getOid());
			}
		}
	}

	@Override
	public void delete(List<GenericBean> list) {
		if (list != null) {
			for (GenericBean model : list) {
				delete(model);
			}
		}
	}

	@Override
	public void deleteByJPQL(List<GenericBean> list) {
		if (list != null) {
			for (GenericBean model : list) {
				deleteByJPQL(model);
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C101M01A.class) {
			return c101m01aDao.findPage(search);
		} else if (clazz == C101S01A.class) {
			return c101s01aDao.findPage(search);
		} else if (clazz == C101S01B.class) {
			return c101s01bDao.findPage(search);
		} else if (clazz == C101S01C.class) {
			return c101s01cDao.findPage(search);
		} else if (clazz == C101S01D.class) {
			return c101s01dDao.findPage(search);
		} else if (clazz == C101S01E.class) {
			return c101s01eDao.findPage(search);
		} else if (clazz == C101S01F.class) {
			return c101s01fDao.findPage(search);
		} else if (clazz == C101S01G.class) {
			return c101s01gDao.findPage(search);
		} else if (clazz == C101S01H.class) {
			return c101s01fDao.findPage(search);
		} else if (clazz == C101S01I.class) {
			return c101s01iDao.findPage(search);
		} else if (clazz == C101S01J.class) {
			return c101s01jDao.findPage(search);
		} else if (clazz == C101S01K.class) {
			return c101s01kDao.findPage(search);
		} else if (clazz == C101S01L.class) {
			return c101s01lDao.findPage(search);
		} else if (clazz == C101S01M.class) {
			return c101s01mDao.findPage(search);
		} else if (clazz == C101S01N.class) {
			return c101s01nDao.findPage(search);
		} else if (clazz == C101S01O.class) {
			return c101s01oDao.findPage(search);
		} else if (clazz == C101S01P.class) {
			return c101s01pDao.findPage(search);
		} else if (clazz == C101S01Q.class) {
			return c101s01qDao.findPage(search);
		} else if (clazz == C101S01R.class) {
			return c101s01rDao.findPage(search);
		} else if (clazz == C101S01X.class) {
			return c101s01xDao.findPage(search);
		} else if (clazz == C101S01Z.class) {
			return c101s01zDao.findPage(search);
		}
		// C120 --------------------------------------
		else if (clazz == C120M01A.class) {
			return c120m01aDao.findPage(search);
		} else if (clazz == C120S01A.class) {
			return c120s01aDao.findPage(search);
		} else if (clazz == C120S01B.class) {
			return c120s01bDao.findPage(search);
		} else if (clazz == C120S01C.class) {
			return c120s01cDao.findPage(search);
		} else if (clazz == C120S01D.class) {
			return c120s01dDao.findPage(search);
		} else if (clazz == C120S01E.class) {
			return c120s01eDao.findPage(search);
		} else if (clazz == C120S01F.class) {
			return c120s01fDao.findPage(search);
		} else if (clazz == C120S01G.class) {
			return c120s01gDao.findPage(search);
		} else if (clazz == C120S01H.class) {
			return c120s01fDao.findPage(search);
		} else if (clazz == C120S01I.class) {
			return c120s01iDao.findPage(search);
		} else if (clazz == C120S01J.class) {
			return c120s01jDao.findPage(search);
		} else if (clazz == C120S01K.class) {
			return c120s01kDao.findPage(search);
		} else if (clazz == C120S01L.class) {
			return c120s01lDao.findPage(search);
		} else if (clazz == C120S01M.class) {
			return c120s01mDao.findPage(search);
		} else if (clazz == C120S01N.class) {
			return c120s01nDao.findPage(search);
		} else if (clazz == C120S01O.class) {
			return c120s01oDao.findPage(search);
		} else if (clazz == C120S01P.class) {
			return c120s01pDao.findPage(search);
		} else if (clazz == C120S01Q.class) {
			return c120s01qDao.findPage(search);
		} else if (clazz == C120S01R.class) {
			return c120s01rDao.findPage(search);
		} else if (clazz == C120S01T.class) {
			return c120s01tDao.findPage(search);
		} else if (clazz == C120S01X.class) {
			return c120s01xDao.findPage(search);
		} else if (clazz == C120S01Z.class) {
			return c120s01zDao.findPage(search);
		} else if (clazz == L161S01D.class) {
			return l161s01dDao.findPage(search);
		} 

		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return (T)findModelByOid(clazz, oid, false);
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T extends GenericBean> T findModelByOid(Class<?> clazz, String oid,
			boolean create) {
		if (clazz == C101M01A.class) {
			C101M01A model = Util.isEmpty(oid) ? null : c101m01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101M01A() : model);
		} else if (clazz == C101S01A.class) {
			C101S01A model = Util.isEmpty(oid) ? null : c101s01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01A() : model);
		} else if (clazz == C101S01B.class) {
			C101S01B model = Util.isEmpty(oid) ? null : c101s01bDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01B() : model);
		} else if (clazz == C101S01C.class) {
			C101S01C model = Util.isEmpty(oid) ? null : c101s01cDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01C() : model);
		} else if (clazz == C101S01D.class) {
			C101S01D model = Util.isEmpty(oid) ? null : c101s01dDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01D() : model);
		} else if (clazz == C101S01E.class) {
			C101S01E model = Util.isEmpty(oid) ? null : c101s01eDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01E() : model);
		} else if (clazz == C101S01F.class) {
			C101S01F model = Util.isEmpty(oid) ? null : c101s01fDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01F() : model);
		} else if (clazz == C101S01G.class) {
			C101S01G model = Util.isEmpty(oid) ? null : c101s01gDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01G() : model);
		} else if (clazz == C101S01H.class) {
			C101S01H model = Util.isEmpty(oid) ? null : c101s01hDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01H() : model);
		} else if (clazz == C101S01I.class) {
			C101S01I model = Util.isEmpty(oid) ? null : c101s01iDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01I() : model);
		} else if (clazz == C101S01J.class) {
			C101S01J model = Util.isEmpty(oid) ? null : c101s01jDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01J() : model);
		} else if (clazz == C101S01K.class) {
			C101S01K model = Util.isEmpty(oid) ? null : c101s01kDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01K() : model);
		} else if (clazz == C101S01L.class) {
			C101S01L model = Util.isEmpty(oid) ? null : c101s01lDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01L() : model);
		} else if (clazz == C101S01M.class) {
			C101S01M model = Util.isEmpty(oid) ? null : c101s01mDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01M() : model);
		} else if (clazz == C101S01N.class) {
			C101S01N model = Util.isEmpty(oid) ? null : c101s01nDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01N() : model);
		} else if (clazz == C101S01O.class) {
			C101S01O model = Util.isEmpty(oid) ? null : c101s01oDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01O() : model);
		} else if (clazz == C101S01P.class) {
			C101S01P model = Util.isEmpty(oid) ? null : c101s01pDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01P() : model);
		} else if (clazz == C101S01Q.class) {
			C101S01Q model = Util.isEmpty(oid) ? null : c101s01qDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01Q() : model);
		} else if (clazz == C101S01R.class) {
			C101S01R model = Util.isEmpty(oid) ? null : c101s01rDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01R() : model);
		} else if (clazz == C101S01S.class) {
			C101S01S model = Util.isEmpty(oid) ? null : c101s01sDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01S() : model);
		} else if (clazz == C101S01U.class) {
			C101S01U model = Util.isEmpty(oid) ? null : c101s01uDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01U() : model);
		} else if (clazz == C101S01V.class) {
			C101S01V model = Util.isEmpty(oid) ? null : c101s01vDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01V() : model);
		} else if (clazz == C101S01W.class) {
			C101S01W model = Util.isEmpty(oid) ? null : c101s01wDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01W() : model);
		} else if (clazz == C101S01X.class) {
			C101S01X model = Util.isEmpty(oid) ? null : c101s01xDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01X() : model);
		} else if (clazz == C101S01Y.class) {
			C101S01Y model = Util.isEmpty(oid) ? null : c101s01yDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01Y() : model);
		} else if (clazz == C101S01Z.class) {
			C101S01Z model = Util.isEmpty(oid) ? null : c101s01zDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S01Z() : model);
		} else if (clazz == C101S02A.class) {
			C101S02A model = Util.isEmpty(oid) ? null : c101s02aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S02A() : model);
		} else if (clazz == C101S02B.class) {
			C101S02B model = Util.isEmpty(oid) ? null : c101s02bDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S02B() : model);
		} else if (clazz == C101S02C.class) {
			C101S02C model = Util.isEmpty(oid) ? null : c101s02cDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S02C() : model);
		} else if (clazz == C101S02S.class) {
			C101S02S model = Util.isEmpty(oid) ? null : c101s02sDao
					.findByOid(oid);
			return (T) (create && model == null ? new C101S02S() : model);
		}
		// C120 --------------------------------------
		else if (clazz == C120M01A.class) {
			C120M01A model = Util.isEmpty(oid) ? null : c120m01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120M01A() : model);
		} else if (clazz == C120S01A.class) {
			C120S01A model = Util.isEmpty(oid) ? null : c120s01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01A() : model);
		} else if (clazz == C120S01B.class) {
			C120S01B model = Util.isEmpty(oid) ? null : c120s01bDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01B() : model);
		} else if (clazz == C120S01C.class) {
			C120S01C model = Util.isEmpty(oid) ? null : c120s01cDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01C() : model);
		} else if (clazz == C120S01D.class) {
			C120S01D model = Util.isEmpty(oid) ? null : c120s01dDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01D() : model);
		} else if (clazz == C120S01E.class) {
			C120S01E model = Util.isEmpty(oid) ? null : c120s01eDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01E() : model);
		} else if (clazz == C120S01F.class) {
			C120S01F model = Util.isEmpty(oid) ? null : c120s01fDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01F() : model);
		} else if (clazz == C120S01G.class) {
			C120S01G model = Util.isEmpty(oid) ? null : c120s01gDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01G() : model);
		} else if (clazz == C120S01H.class) {
			C120S01H model = Util.isEmpty(oid) ? null : c120s01hDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01H() : model);
		} else if (clazz == C120S01I.class) {
			C120S01I model = Util.isEmpty(oid) ? null : c120s01iDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01I() : model);
		} else if (clazz == C120S01J.class) {
			C120S01J model = Util.isEmpty(oid) ? null : c120s01jDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01J() : model);
		} else if (clazz == C120S01K.class) {
			C120S01K model = Util.isEmpty(oid) ? null : c120s01kDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01K() : model);
		} else if (clazz == C120S01L.class) {
			C120S01L model = Util.isEmpty(oid) ? null : c120s01lDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01L() : model);
		} else if (clazz == C120S01M.class) {
			C120S01M model = Util.isEmpty(oid) ? null : c120s01mDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01M() : model);
		} else if (clazz == C120S01N.class) {
			C120S01N model = Util.isEmpty(oid) ? null : c120s01nDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01N() : model);
		} else if (clazz == C120S01O.class) {
			C120S01O model = Util.isEmpty(oid) ? null : c120s01oDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01O() : model);
		} else if (clazz == C120S01P.class) {
			C120S01P model = Util.isEmpty(oid) ? null : c120s01pDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01P() : model);
		} else if (clazz == C120S01Q.class) {
			C120S01Q model = Util.isEmpty(oid) ? null : c120s01qDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01Q() : model);
		} else if (clazz == C120S01R.class) {
			C120S01R model = Util.isEmpty(oid) ? null : c120s01rDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01R() : model);
		} else if (clazz == C120S01S.class) {
			C120S01S model = Util.isEmpty(oid) ? null : c120s01sDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01S() : model);
		} else if (clazz == C120S01U.class) {
			C120S01U model = Util.isEmpty(oid) ? null : c120s01uDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01U() : model);
		} else if (clazz == C120S01V.class) {
			C120S01V model = Util.isEmpty(oid) ? null : c120s01vDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01V() : model);
		} else if (clazz == C120S01W.class) {
			C120S01W model = Util.isEmpty(oid) ? null : c120s01wDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01W() : model);
		} else if (clazz == C120S01X.class) {
			C120S01X model = Util.isEmpty(oid) ? null : c120s01xDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01X() : model);
		} else if (clazz == C120S01Z.class) {
			C120S01Z model = Util.isEmpty(oid) ? null : c120s01zDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01Z() : model);
		} else if (clazz == L161S01D.class) {
			L161S01D model = Util.isEmpty(oid) ? null : l161s01dDao
					.findByOid(oid);
			return (T) (create && model == null ? new L161S01D() : model);
		} else if (clazz == C120S01Y.class) {
			C120S01Y model = Util.isEmpty(oid) ? null : c120s01yDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S01Y() : model);
		} else if (clazz == C120S02A.class) {
			C120S02A model = Util.isEmpty(oid) ? null : c120s02aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S02A() : model);
		} else if (clazz == C120S02B.class) {
			C120S02B model = Util.isEmpty(oid) ? null : c120s02bDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S02B() : model);
		} else if (clazz == C120S02C.class) {
			C120S02C model = Util.isEmpty(oid) ? null : c120s02cDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S02C() : model);
		} else if (clazz == C120S02S.class) {
			C120S02S model = Util.isEmpty(oid) ? null : c120s02sDao
					.findByOid(oid);
			return (T) (create && model == null ? new C120S02S() : model);
		}
		
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == C101M01A.class) {
			return c101m01aDao.findByMainId(mainId);
		} else if (clazz == C101S01A.class) {
			return c101s01aDao.findByMainId(mainId);
		} else if (clazz == C101S01B.class) {
			return c101s01bDao.findByMainId(mainId);
		} else if (clazz == C101S01C.class) {
			return c101s01cDao.findByMainId(mainId);
		} else if (clazz == C101S01D.class) {
			return c101s01dDao.findByMainId(mainId);
		} else if (clazz == C101S01E.class) {
			return c101s01eDao.findByMainId(mainId);
		} else if (clazz == C101S01F.class) {
			return c101s01fDao.findByMainId(mainId);
		} else if (clazz == C101S01G.class) {
			return c101s01gDao.findByMainId(mainId);
		} else if (clazz == C101S01H.class) {
			return c101s01hDao.findByMainId(mainId);
		} else if (clazz == C101S01I.class) {
			return c101s01iDao.findByMainId(mainId);
		} else if (clazz == C101S01J.class) {
			return c101s01jDao.findByMainId(mainId);
		} else if (clazz == C101S01K.class) {
			return c101s01kDao.findByMainId(mainId);
		} else if (clazz == C101S01L.class) {
			return c101s01lDao.findByMainId(mainId);
		} else if (clazz == C101S01M.class) {
			return c101s01mDao.findByMainId(mainId);
		} else if (clazz == C101S01N.class) {
			return c101s01nDao.findByMainId(mainId);
		} else if (clazz == C101S01O.class) {
			return c101s01oDao.findByMainId(mainId);
		} else if (clazz == C101S01P.class) {
			return c101s01pDao.findByMainId(mainId);
		} else if (clazz == C101S01Q.class) {
			return c101s01qDao.findByMainId(mainId);
		} else if (clazz == C101S01R.class) {
			return c101s01rDao.findByMainId(mainId);
		} else if (clazz == C101S01S.class) {
			return c101s01sDao.findByMainId(mainId);
		} else if (clazz == C101S01U.class) {
			return c101s01uDao.findByMainId(mainId);
		} else if (clazz == C101S01V.class) {
			return c101s01vDao.findByMainId(mainId);
		} else if (clazz == C101S01W.class) {
			return c101s01wDao.findByMainId(mainId);
		} else if (clazz == C101S01X.class) {
			return c101s01xDao.findByMainId(mainId);
		} else if (clazz == C101S01Y.class) {
			return c101s01yDao.findByMainId(mainId);
		} else if (clazz == C101S01Z.class) {
			return c101s01zDao.findByMainId(mainId);
		} else if (clazz == C101S02A.class) {
			return c101s02aDao.findByMainId(mainId);
		} else if (clazz == C101S02B.class) {
			return c101s02bDao.findByMainId(mainId);
		} else if (clazz == C101S02C.class) {
			return c101s02cDao.findByMainId(mainId);
		}
		// C120 --------------------------------------
		else if (clazz == C120M01A.class) {
			return c120m01aDao.findByMainId(mainId);
		} else if (clazz == C120S01A.class) {
			return c120s01aDao.findByMainId(mainId);
		} else if (clazz == C120S01B.class) {
			return c120s01bDao.findByMainId(mainId);
		} else if (clazz == C120S01C.class) {
			return c120s01cDao.findByMainId(mainId);
		} else if (clazz == C120S01D.class) {
			return c120s01dDao.findByMainId(mainId);
		} else if (clazz == C120S01E.class) {
			return c120s01eDao.findByMainId(mainId);
		} else if (clazz == C120S01F.class) {
			return c120s01fDao.findByMainId(mainId);
		} else if (clazz == C120S01G.class) {
			return c120s01gDao.findByMainId(mainId);
		} else if (clazz == C120S01H.class) {
			return c120s01hDao.findByMainId(mainId);
		} else if (clazz == C120S01I.class) {
			return c120s01iDao.findByMainId(mainId);
		} else if (clazz == C120S01J.class) {
			return c120s01jDao.findByMainId(mainId);
		} else if (clazz == C120S01K.class) {
			return c120s01kDao.findByMainId(mainId);
		} else if (clazz == C120S01L.class) {
			return c120s01lDao.findByMainId(mainId);
		} else if (clazz == C120S01M.class) {
			return c120s01mDao.findByMainId(mainId);
		} else if (clazz == C120S01N.class) {
			return c120s01nDao.findByMainId(mainId);
		} else if (clazz == C120S01O.class) {
			return c120s01oDao.findByMainId(mainId);
		} else if (clazz == C120S01P.class) {
			return c120s01pDao.findByMainId(mainId);
		} else if (clazz == C120S01Q.class) {
			return c120s01qDao.findByMainId(mainId);
		} else if (clazz == C120S01R.class) {
			return c120s01rDao.findByMainId(mainId);
		} else if (clazz == C120S01S.class) {
			return c120s01sDao.findByMainId(mainId);
		} else if (clazz == C120S01U.class) {
			return c120s01uDao.findByMainId(mainId);
		} else if (clazz == C120S01V.class) {
			return c120s01vDao.findByMainId(mainId);
		} else if (clazz == C120S01X.class) {
			return c120s01xDao.findByMainId(mainId);
		} else if (clazz == C120S01Z.class) {
			return c120s01zDao.findByMainId(mainId);
		} else if (clazz == L161S01D.class) {
			return l161s01dDao.findByMainId(mainId);
		} else if (clazz == C120S01Y.class) {
			return c120s01yDao.findByMainId(mainId);
		} else if (clazz == C120S02A.class) {
			return c120s02aDao.findByMainId(mainId);
		} else if (clazz == C120S02B.class) {
			return c120s02bDao.findByMainId(mainId);
		} else if (clazz == C120S02C.class) {
			return c120s02cDao.findByMainId(mainId);
		}
		
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByRelationKey(Class clazz,
			String mainId, String custId, String dupNo) {
		if (clazz == C101M01A.class) {
			return c101m01aDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C101S01A.class) {
			return c101s01aDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01B.class) {
			return c101s01bDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01C.class) {
			return c101s01cDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01D.class) {
			return c101s01dDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01E.class) {
			return c101s01eDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01F.class) {
			return c101s01fDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01G.class) {
			return c101s01gDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C101S01H.class) {
			return c101s01hDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01I.class) {
			return c101s01iDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01J.class) {
			return c101s01jDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01K.class) {
			// return c101s01kDao.findByIndex01(mainId, custId, dupNo, null,
			// null);
			return c101s01kDao.findByIndex01(mainId, null, null, custId, dupNo);
		} else if (clazz == C101S01L.class) {
			return c101s01lDao.findByIndex01(mainId, custId, dupNo, null, null,
					null);
		} else if (clazz == C101S01M.class) {
			return c101s01mDao.findByIndex01(mainId, custId, dupNo, null);
		} else if (clazz == C101S01N.class) {
			return c101s01nDao.findByIndex01(mainId, custId, dupNo, null);
		} else if (clazz == C101S01O.class) {
			return c101s01oDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S01P.class) {
			return c101s01pDao.findByIndex01(mainId, custId, dupNo, null);
		} else if (clazz == C101S01Q.class) {
			return c101s01qDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C101S01R.class) {
			return c101s01rDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C101S01S.class) {
			return c101s01sDao.findByList(mainId, custId, dupNo, null);
		} else if (clazz == C101S01U.class) {
			return c101s01uDao.findByMainIdCustIdDupNo(mainId, custId, dupNo);
		} else if (clazz == C101S01V.class) {
			return c101s01vDao.findByMainIdandCustID(mainId, custId, dupNo);
		} else if (clazz == C101S01X.class) {
			return c101s01xDao.findByMainIdCustIdDupNo(mainId, custId, dupNo);
		} else if (clazz == C101S01W.class) {
			return c101s01wDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C101S01Y.class) {
			return c101s01yDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C101S01Z.class) {
			return c101s01zDao.findByMainIdCustIdDupNo(mainId, custId, dupNo);
		} else if (clazz == C101S02A.class) {
			return c101s02aDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C101S02B.class) {
			return c101s02bDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C101S02C.class) {
			return c101s02cDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C101S02S.class) {
			return c101s02sDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C101S01G_N.class) {
			return c101s01g_nDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C101S01Q_N.class) {
			return c101s01q_nDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C101S01R_N.class) {
			return c101s01r_nDao.findByIndex01(mainId, null, custId, dupNo);
		} 
		// C120 --------------------------------------
		else if (clazz == C120M01A.class) {
			return c120m01aDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C120S01A.class) {
			return c120s01aDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01B.class) {
			return c120s01bDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01C.class) {
			return c120s01cDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01D.class) {
			return c120s01dDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01E.class) {
			return c120s01eDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01F.class) {
			return c120s01fDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01G.class) {
			return c120s01gDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C120S01H.class) {
			return c120s01hDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01I.class) {
			return c120s01iDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01J.class) {
			return c120s01jDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01K.class) {
			return c120s01kDao.findByIndex01(mainId, null, null, custId, dupNo);
		} else if (clazz == C120S01L.class) {
			return c120s01lDao.findByIndex01(mainId, custId, dupNo, null, null,
					null);
		} else if (clazz == C120S01M.class) {
			return c120s01mDao.findByIndex01(mainId, custId, dupNo, null);
		} else if (clazz == C120S01N.class) {
			return c120s01nDao.findByIndex01(mainId, custId, dupNo, null);
		} else if (clazz == C120S01O.class) {
			return c120s01oDao.findByIndex01(mainId, custId, dupNo);
		} else if (clazz == C120S01P.class) {
			return c120s01pDao.findByIndex01(mainId, custId, dupNo, null);
		} else if (clazz == C120S01Q.class) {
			return c120s01qDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C120S01R.class) {
			return c120s01rDao.findByIndex01(mainId, null, custId, dupNo);
		} else if (clazz == C120S01S.class) {
			return c120s01sDao.findByList(mainId, custId, dupNo); 
		} else if (clazz == C120S01T.class) {
			return c120s01tDao.findByList(mainId, custId, dupNo); 
		} else if (clazz == C120S01U.class) {
			return c120s01uDao.findByMainIdCustIdDupNo(mainId, custId, dupNo);
		} else if (clazz == C120S01V.class) {
			return c120s01vDao.findByMainIdandCustID(mainId, custId, dupNo);
		} else if (clazz == C120S01X.class) {
			return c120s01xDao.findByMainIdCustIdDupNo(mainId, custId, dupNo);
		} else if (clazz == C120S01W.class) {
			return c120s01wDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C120S01Y.class) {
			return c120s01yDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C120S01Z.class) {
			return c120s01zDao.findByMainIdCustIdDupNo(mainId, custId, dupNo);
		} else if (clazz == C120S02A.class) {
			return c120s02aDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C120S02B.class) {
			return c120s02bDao.findByList(mainId, custId, dupNo);
		} else if (clazz == C120S02C.class) {
			return c120s02cDao.findByIndex01(mainId, custId, dupNo);
		}else if (clazz == C120S02S.class) {
			return c120s02sDao.findByList(mainId, custId, dupNo);
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	public <T extends GenericBean> T findModelByKey(Class<?> clazz,
			String mainId, String custId, String dupNo) {
		return (T)findModelByKey(clazz, mainId, custId, dupNo, false);
	}

	@SuppressWarnings("unchecked")
	public <T extends GenericBean> T findModelByKey(Class<?> clazz,
			String mainId, String custId, String dupNo, boolean create) {
		if (clazz == C101M01A.class) {
			C101M01A model = c101m01aDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101M01A() : model);
		} else if (clazz == C101S01A.class) {
			C101S01A model = c101s01aDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01A() : model);
		} else if (clazz == C101S01B.class) {
			C101S01B model = c101s01bDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01B() : model);
		} else if (clazz == C101S01C.class) {
			C101S01C model = c101s01cDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01C() : model);
		} else if (clazz == C101S01D.class) {
			C101S01D model = c101s01dDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01D() : model);
		} else if (clazz == C101S01E.class) {
			C101S01E model = c101s01eDao.findByUniqueKey(mainId, custId, dupNo);
			if (model == null && create) {
				model = new C101S01E();
				model.setIsFromOld(UtilConstants.DEFAULT.否); // default
			}
			return (T) model;
			// return (T) (model == null && create ? new C101S01E() : model);
		} else if (clazz == C101S01F.class) {
			C101S01F model = c101s01fDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01F() : model);
		} else if (clazz == C101S01G.class) {
			C101S01G model = c101s01gDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101S01G() : model);
		} else if (clazz == C101S01H.class) {
			C101S01H model = c101s01hDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01H() : model);
		} else if (clazz == C101S01I.class) {
			C101S01I model = c101s01iDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01I() : model);
		} else if (clazz == C101S01J.class) {
			C101S01J model = c101s01jDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01J() : model);
		} else if (clazz == C101S01K.class) {
			C101S01K model = c101s01kDao.findByUniqueKey(mainId, custId, dupNo,
					null, null);
			return (T) (model == null && create ? new C101S01K() : model);
		} else if (clazz == C101S01L.class) {
			C101S01L model = c101s01lDao.findByUniqueKey(mainId, custId, dupNo,
					null, null, null);
			return (T) (model == null && create ? new C101S01L() : model);
		} else if (clazz == C101S01M.class) {
			C101S01M model = c101s01mDao.findByUniqueKey(mainId, custId, dupNo,
					null);
			return (T) (model == null && create ? new C101S01M() : model);
		} else if (clazz == C101S01N.class) {
			C101S01N model = c101s01nDao.findByUniqueKey(mainId, custId, dupNo,
					null);
			return (T) (model == null && create ? new C101S01N() : model);
		} else if (clazz == C101S01O.class) {
			C101S01O model = c101s01oDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01O() : model);
		} else if (clazz == C101S01Q.class) {
			C101S01Q model = c101s01qDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101S01Q() : model);
		} else if (clazz == C101S01R.class) {
			C101S01R model = c101s01rDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101S01R() : model);
		} else if (clazz == C101S01X.class) {
			C101S01X model = c101s01xDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01X() : model);
		} else if (clazz == C101S01Z.class) {
			C101S01Z model = c101s01zDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S01Z() : model);
		} else if (clazz == C101S02B.class) {
			C101S02B model = c101s02bDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S02B() : model);
		} else if (clazz == C101S02C.class) {
			C101S02C model = c101s02cDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S02C() : model);
		} else if (clazz == C101S02S.class) {
			C101S02S model = c101s02sDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C101S02S() : model);
		} else if (clazz == C101S01G_N.class) { //消金評等雙軌模型使用
			C101S01G_N model = c101s01g_nDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101S01G_N() : model);
		} else if (clazz == C101S01Q_N.class) { //消金評等雙軌模型使用
			C101S01Q_N model = c101s01q_nDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101S01Q_N() : model);
		} else if (clazz == C101S01R_N.class) { //消金評等雙軌模型使用
			C101S01R_N model = c101s01r_nDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C101S01R_N() : model);
		} 
		// C120 --------------------------------------
		else if (clazz == C120M01A.class) {
			C120M01A model = c120m01aDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C120M01A() : model);
		} else if (clazz == C120S01A.class) {
			C120S01A model = c120s01aDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01A() : model);
		} else if (clazz == C120S01B.class) {
			C120S01B model = c120s01bDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01B() : model);
		} else if (clazz == C120S01C.class) {
			C120S01C model = c120s01cDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01C() : model);
		} else if (clazz == C120S01D.class) {
			C120S01D model = c120s01dDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01D() : model);
		} else if (clazz == C120S01E.class) {
			C120S01E model = c120s01eDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01E() : model);
		} else if (clazz == C120S01F.class) {
			C120S01F model = c120s01fDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01F() : model);
		} else if (clazz == C120S01G.class) {
			C120S01G model = c120s01gDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C120S01G() : model);
		} else if (clazz == C120S01H.class) {
			C120S01H model = c120s01hDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01H() : model);
		} else if (clazz == C120S01I.class) {
			C120S01I model = c120s01iDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01I() : model);
		} else if (clazz == C120S01J.class) {
			C120S01J model = c120s01jDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01J() : model);
		} else if (clazz == C120S01K.class) {
			C120S01K model = c120s01kDao.findByUniqueKey(mainId, custId, dupNo,
					null, null);
			return (T) (model == null && create ? new C120S01K() : model);
		} else if (clazz == C120S01L.class) {
			C120S01L model = c120s01lDao.findByUniqueKey(mainId, custId, dupNo,
					null, null, null);
			return (T) (model == null && create ? new C120S01L() : model);
		} else if (clazz == C120S01M.class) {
			C120S01M model = c120s01mDao.findByUniqueKey(mainId, custId, dupNo,
					null);
			return (T) (model == null && create ? new C120S01M() : model);
		} else if (clazz == C120S01N.class) {
			C120S01N model = c120s01nDao.findByUniqueKey(mainId, custId, dupNo,
					null);
			return (T) (model == null && create ? new C120S01N() : model);
		} else if (clazz == C120S01O.class) {
			C120S01O model = c120s01oDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01O() : model);
		} else if (clazz == C120S01Q.class) {
			C120S01Q model = c120s01qDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C120S01Q() : model);
		} else if (clazz == C120S01R.class) {
			C120S01R model = c120s01rDao.findByUniqueKey(mainId, null, custId,
					dupNo);
			return (T) (model == null && create ? new C120S01R() : model);
		} else if (clazz == C120S01X.class) {
			C120S01X model = c120s01xDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01X() : model);
		} else if (clazz == C120S01Z.class) {
			C120S01Z model = c120s01zDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S01Z() : model);
		} else if (clazz == C120S02B.class) {
			C120S02B model = c120s02bDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S02B() : model);
		} else if (clazz == C120S02C.class) {
			C120S02C model = c120s02cDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S02C() : model);
		} else if (clazz == C120S02S.class) {
			C120S02S model = c120s02sDao.findByUniqueKey(mainId, custId, dupNo);
			return (T) (model == null && create ? new C120S02S() : model);
		}
		
		return null;
	}

	@Override
	public void copy(String targetMainId, String[] originMainIds) {
		if (Util.isNotEmpty(targetMainId) && originMainIds != null) {
			// (2)前狀態為警示者要提示訊息顯示借款人xxx目前屬婉卻警示戶。
			for (String originMainId : originMainIds) {
				C101M01A c101m01a = c101m01aDao.findByMainIdone(originMainId);
				String custId = c101m01a.getCustId();
				String dupNo = c101m01a.getDupNo();
				for (Class<?> clazz : LMSUtil.C101Class) {
					if(LMSUtil.disableC101S01F_C120S01F() && clazz==C101S01F.class){
						continue;
					}
					String originTable = clazz.getSimpleName();
					String targetTable = originTable.replace("C101", "C120");
					// delete
					if(clazz==C101S01K.class){
						//C120S01K 的 custId 欄位，放的和其它 table 不同
						//所以 C120S01K 在 delete 時，用 where lnGeId=? and lnGeDupNo=?
						List<C120S01K> list = c120s01kDao.findByIndex01(targetMainId, null, null, custId, dupNo);
						if(CollectionUtils.isNotEmpty(list)){
							c120s01kDao.delete(list);
						}
					}else{
						this.getJdbc().updateByCustParam("ClsCust.delete_by_mainId_custId_dupNo",
								new Object[] { targetTable },
								new Object[] { targetMainId, custId , dupNo });	
					}
					
					// copy
					String sql = SQLParse.getCopySQLbyMainId(originMainId,
							targetMainId, clazz, targetTable);
					this.getJdbc().updateBySQL(sql, new Object[] {});
				}
				
				//copy L120S01M,L120S01N,L120S01O
				if(true){
					Class<?>[] mnoArr = { L120S01M.class,
							L120S01N.class, L120S01O.class};
					for (Class<?> clazz : mnoArr) {
						String targetTable = clazz.getSimpleName();
						
						this.getJdbc().updateByCustParam("ClsCust.delete_by_mainId_custId_dupNo",
								new Object[] { targetTable },
								new Object[] { targetMainId, custId , dupNo }); 
						// copy
						String sql = SQLParse.getCopySQLbyMainId(originMainId,
								targetMainId, clazz, targetTable);
						this.getJdbc().updateBySQL(sql, new Object[] {});
					}
				}
			}
		}
	}
	
	@Override
	public void saveC120M01A(C120M01A c120m01a) {
		c120m01aDao.save(c120m01a);
		
		String mainId = Util.trim(c120m01a.getMainId());
		String c120m01aCustId = Util.trim(c120m01a.getCustId());
		String c120m01aDupNo = Util.trim(c120m01a.getDupNo());
		String c120m01aCustName = Util.trim(c120m01a.getCustName());
		
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
		if (l120m01a != null) {
			
			if ("Y".equals(c120m01a.getKeyMan())) {
				String l120m01aCustId = Util.trim(l120m01a.getCustId());
				String l120m01aDupNo = Util.trim(l120m01a.getDupNo());				
				if (l120m01aCustId.equals(c120m01aCustId) && l120m01aDupNo.equals(c120m01aDupNo)) {
					l120m01a.setCustName(c120m01aCustName);
					l120m01aDao.save(l120m01a);
				}
			}
			
			List<L140M01A> l140m01alist = l140m01aDao.findL140m01aListByL120m01cMainId(mainId);
			for (L140M01A l140m01a : l140m01alist) {
				if (l140m01a != null) {
					if (c120m01aCustId.equals(l140m01a.getCustId()) && c120m01aDupNo.equals(l140m01a.getDupNo())) {
						l140m01a.setCustName(c120m01aCustName);
						l140m01aDao.save(l140m01a);
					}
				}
			}

		}
	}
}
