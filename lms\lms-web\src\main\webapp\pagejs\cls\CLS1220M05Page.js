//做為畫面init完成後使用
var initDfd = window.initDfd || $.Deferred();
var _handler="cls1220m05formhandler";
pageJsInit(function(){
$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	
    $.form.init({
        formId: "tabForm",
        formHandler: _handler,
        formAction: "query",
        loadSuccess: function(json){    	
        	if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
				tabForm.lockDoc();
			} //若操作者 只有 EL00 權限，要能切換頁籤 setIgnoreTempSave(true)
        	if(true){
        		//當 html 中的 element 中有 codeType
            	tabForm.buildItem();		
        	}

        	tabForm.injectData(json);
        	
        	initDfd.resolve(json);	
        	ilog.debug("@CLS1220M05Page.js, mainId= " + responseJSON.mainId);
        	if(json.isClosed=="X"){
				tabForm.lockDoc();
        		$("#btnSave").addClass(" ui-state-disabled ").prop("disabled", "true");
        		$("#btnDiscardLoan").addClass(" ui-state-disabled ").prop("disabled", "true");
        	}
        }
    });
    
    //設定經辦下拉選單(派案-指定簽案行員)
	/*$.ajax({
		type : "POST",
		handler: "cls1220m10formhandler",
	    action: "getMegaEmpInfo",
		success:function(responseData){
			if(responseData.Success){ 
				$("#SignMegaEmp").setItems({
                    item: responseData.bossListAO,
                    space: true,
                    format: "{value} {key}"
                });
			}
		}
	});*/
	
	if(responseJSON.noOpenDoc){
		$(".ordinary").hide();
	}

    
    var saveAction = function(opts){
        if (tabForm.valid()) {
        	return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    page: responseJSON.page,
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                )
            }).done(function(json){
				tabForm.injectData(json);
				//更新 opener 的 Grid
				API.triggerOpener();
				if(json.errorFlag){
					CommonAPI.showErrorMessage(json.errorMessage);
				}
			});
        	
        } else {
            return $.Deferred();
        }
    }
    
    
    var sendAction = function(){
    	//設定經辦下拉選單(派案-指定簽案行員)，配合地政士案件於派案時需將重複派案人員從清單內移除，改成點派案時再撈簽案行員
    	$.ajax({
			type : "POST",
			handler: "cls1220m10formhandler",
		    action: "getMegaEmpInfo"
		}).done(function(responseData){
			if(responseData.Success){ 
				var signMegaEmp = $("#SignMegaEmp");
				signMegaEmp.setItems({
					item: responseData.bossListAO,
					space: true,
					format: "{value} {key}"
				});
			}
			
			//跳派案視窗選擇派案人員
			var sendDiv = $("#SendDiv");
			sendDiv.thickbox({
				title: i18n.cls1220m04['button.send'], width: 400, height: 200, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
				buttons: {
					"sure": function(){
						$.ajax({
							type : "POST",
							handler : "cls1220m10formhandler",
							data : {
								formAction : "sendToMegaEmp",
								mainOid : responseJSON.mainOid,
								mainId: responseJSON.mainId,
								SignMegaEmp: $("select#SignMegaEmp").val(),
								evaMegaEmpNo: $("select#evaMegaEmpSend").val(),
								estUnitName: $('#estUnitNameSend').val()
							}
						}).done(function(responseData){
							if(responseData.Success){
								$.thickbox.close();
								CommonAPI.triggerOpener("gridview", "reloadGrid");
								window.close();
//		    							CommonAPI.showMessage(responseData.Message);
							}else{
								CommonAPI.showErrorMessage(responseData.Message);
							}
						});
					},
					"cancel": function(){
						$.thickbox.close();
					}
				}
			});
		});

    }
    
    var prepare_ploan_discardLoan = function(){
    	var my_dfd = $.Deferred();
    	$.ajax({
            type: "POST", handler: "cls1220m01formhandler",
            data:{ formAction: "prepare_ploan_discardLoan", mainId: responseJSON.mainId}
        }).done(function(json){
			if(json.cfmMsg && json.cfmMsg!=""){
				API.confirmMessage(json.cfmMsg, function(b){
					if (b) {
						my_dfd.resolve();
					}
				});
			}else{
				my_dfd.resolve();
			}
		});
    	return my_dfd;
    }
    
    var setEstData = function(){
    	//設定經辦下拉選單(有無擔保品-指定收件行員)
    	return $.ajax({
    		type : "POST",
    		handler: "cls1220m10formhandler",
    	    //action: "getMegaEmpInfo",
    	    data:$.extend( {
            	formAction: "getMegaEmpInfo",
            	isEvaMegaEmp:"Y"
                }, {}
            )
    	}).done(function(responseData){
			if(responseData.Success){ //成功
				var evaMegaEmpSend = $("#evaMegaEmpSend");
				evaMegaEmpSend.setItems({
					item: responseData.bossListAO,
					space: true,
					format: "{value} {key}"
				});
			}
		});
    }
    
    btnPanel.find("#btnSave").click(function(){
    		saveAction({'allowIncomplete':'Y','checkSave':'Y'}).done(function(json){ //執行將自動儲存資料，是否繼續此動作?
    			if(json.saveOkFlag){
    				var dyna = [];
    				if(true){
    					dyna.push(i18n.def.saveSuccess);
    				}	
    				if(json.IncompleteMsg){
    					dyna.push(json.IncompleteMsg);
    				}	
    				API.showMessage(dyna.join("<br/>-------------------<br/>"));
    			}
    		});
    	
    }).end().find("#btnDiscardLoan").click(function(){
    	prepare_ploan_discardLoan().done(function(){
    		$.ajax({
                type: "POST", handler: "cls1220m01formhandler",
                data:{ formAction: "run_ploan_discardLoan", mainId: responseJSON.mainId}
            }).done(function(json){
				//        			$("#btnSave").addClass(" ui-state-disabled ").attr("disabled", "true");            		
				//        			$("#btnDiscardLoan").addClass(" ui-state-disabled ").attr("disabled", "true");
									//更新 opener 的 Grid
				//                	API.triggerOpener();
									$.thickbox.close();
									CommonAPI.triggerOpener("gridview", "reloadGrid");
									window.close();
								});	
    	});
    	
    }).end().find("#btnPrint").click(function(){
    	print();
    }).end().find("#btnSend").click(function(){ //派案
    	CommonAPI.confirmMessage(i18n.cls1220m05['button.sendMessage'], function(b){ //執行將自動儲存資料，是否繼續此動作?
    		if (b) {
    			//因主管也可以改派案資料，所以派案前先儲存
                saveAction({'allowIncomplete':'Y','checkSave':'Y','checkCredit':'N'}).done(function(json){
            		if(json.saveOkFlag){
            			var dyna = [];
            			if(true){
            				$.ajax({
                				type : "POST",
                				handler : "cls1220m10formhandler",
                				data : {
                					formAction : "checkSendData",
                					mainOid : responseJSON.mainOid,
                					mainId: responseJSON.mainId
                				}
                			}).done(function(responseData){
								var Success = responseData.Success;
								if(responseData.Success){
									//先根據estFlag判斷要不要隱藏欄位，藏完再顯示dialog
									$.ajax({
										type : "POST",
										handler: "cls1220m10formhandler",
										action: "getEstFlag",
										data : {
											mainOid : responseJSON.mainOid,
											mainId: responseJSON.mainId
										}
									}).done(function(responseData){
											var estFlag = responseData.estFlag;
											if(estFlag == "1"){
												$("#MegaEmpTr").show();
												$("#estUnitTr").hide();
												setEstData().done(function(){
													sendAction();
												});
											}else if(estFlag == "3"){
												$("#MegaEmpTr").hide();
												$("#estUnitTr").show();
												sendAction();
											}else{
												$("#MegaEmpTr").hide();
												$("#estUnitTr").hide();
												sendAction();
											}
										});

								}else{
									CommonAPI.showErrorMessage(responseData.Message);
								}
								API.triggerOpener();
							});			
            			}	
            			if(json.IncompleteMsg){
            				dyna.push(json.IncompleteMsg);
            				API.showMessage(dyna.join("<br/>-------------------<br/>"));
            			}	
            		}
                });
             }
        });
    	
    });
    

});

});


function showJSON(){
	$.ajax({
        type: "POST", handler: 'cls1220m04formhandler', data:{ formAction: "showJSON", mainId: responseJSON.mainId}  
    }).done(function(json){});
}

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});