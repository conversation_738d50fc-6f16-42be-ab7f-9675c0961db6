package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 團貸明細查詢
 * </pre>
 * 
 * @since 2018/04/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2018/04/02,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/fms/cls9071v02")
public class CLS9071V02Page extends AbstractEloanInnerView {

	public CLS9071V02Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));
		if(true){
			boolean area_onTheWay = false;
			String logonBr = user.getUnitNo();
			if (Util.equals(UtilConstants.BankNo.資訊處, logonBr)
					|| Util.equals(UtilConstants.BankNo.授管處, logonBr)
					|| Util.equals(UtilConstants.BankNo.授信行銷處, logonBr)
					|| Util.equals(UtilConstants.BankNo.消金業務處, logonBr)) {
				area_onTheWay = true;
			}
			
			// UPGRADE: 前端須配合改Thymeleaf的樣式
			// add(_getComp("area_onTheWay", area_onTheWay ));
			model.addAttribute("area_onTheWay", area_onTheWay);
		}
		
		renderJsI18N(CLS9071V02Page.class);
	}

	public String[] getJavascriptPath() {

		return new String[] { "pagejs/fms/CLS9071V02Page.js" };
	}
	
	// UPGRADE: 承上，前端須配合改Thymeleaf的樣式
//	private Component _getComp(String id, boolean visible){
//		//new AclLabel(id, AuthType.Query, params, visible);
//		Label r = new Label(id, "");
//		r.setVisible(visible);
//		return r;
//	}
}
