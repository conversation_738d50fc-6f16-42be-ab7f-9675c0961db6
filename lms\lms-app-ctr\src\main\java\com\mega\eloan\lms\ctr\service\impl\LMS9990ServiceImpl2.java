/* 
 * LMS1205ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.ctr.constants.CtrConstants;
import com.mega.eloan.lms.ctr.pages.LMS9990M06Page;
import com.mega.eloan.lms.ctr.pages.LMS9990M07Page;
import com.mega.eloan.lms.ctr.service.LMS9990Service2;
import com.mega.eloan.lms.dao.C900M01ADao;
import com.mega.eloan.lms.dao.C900M01DDao;
import com.mega.eloan.lms.dao.C999A01ADao;
import com.mega.eloan.lms.dao.C999M01ADao;
import com.mega.eloan.lms.dao.C999M01BDao;
import com.mega.eloan.lms.dao.C999M01CDao;
import com.mega.eloan.lms.dao.C999M01DDao;
import com.mega.eloan.lms.dao.C999S01ADao;
import com.mega.eloan.lms.dao.C999S01BDao;
import com.mega.eloan.lms.dao.C999S02ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L140S02BDao;
import com.mega.eloan.lms.dao.L140S02CDao;
import com.mega.eloan.lms.dao.L140S02DDao;
import com.mega.eloan.lms.dao.L140S02EDao;
import com.mega.eloan.lms.dao.L140S02FDao;
import com.mega.eloan.lms.dao.L140S02IDao;
import com.mega.eloan.lms.dao.L140S02KDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.LmsCustdataService;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C900M01A;
import com.mega.eloan.lms.model.C999A01A;
import com.mega.eloan.lms.model.C999M01A;
import com.mega.eloan.lms.model.C999M01B;
import com.mega.eloan.lms.model.C999M01C;
import com.mega.eloan.lms.model.C999M01D;
import com.mega.eloan.lms.model.C999S01A;
import com.mega.eloan.lms.model.C999S01B;
import com.mega.eloan.lms.model.C999S02A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02B;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02F;
import com.mega.eloan.lms.model.L140S02I;
import com.mega.eloan.lms.model.L140S02K;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金約據書ServiceImpl
 * </pre>
 * 
 * @since 2012/8/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/8/15,Miller,new
 *          </ul>
 */
@Service
public class LMS9990ServiceImpl2 extends AbstractCapService implements
		LMS9990Service2 {

	// // ENTER效果
	// // Word XML 換行語法
	private final static String strTab = "<w:br/>";

	@Resource
	TempDataService tempDataService;
	@Resource
	LmsCustdataService lmsCustdataService;

	@Resource
	L120M01ADao l120m01aDao;
	@Resource
	C900M01ADao c900m01aDao;
	@Resource
	C900M01DDao c900m01dDao;
	@Resource
	C999M01ADao c999m01aDao;

	@Resource
	C999A01ADao c999a01aDao;

	@Resource
	C999M01BDao c999m01bDao;

	@Resource
	C999M01CDao c999m01cDao;

	@Resource
	C999M01DDao c999m01dDao;

	@Resource
	C999S01ADao c999s01aDao;

	@Resource
	C999S01BDao c999s01bDao;

	@Resource
	C999S02ADao c999s02aDao;

	@Resource
	L140S01ADao l140s01aDao;
	@Resource
	L140S02ADao l140s02aDao;
	@Resource
	L140S02BDao l140s02bDao;
	@Resource
	L140S02CDao l140s02cDao;
	@Resource
	L140S02DDao l140s02dDao;
	@Resource
	L140S02EDao l140s02eDao;
	@Resource
	L140S02FDao l140s02fDao;
	@Resource
	L140S02IDao l140s02iDao;
	@Resource
	L140M01ADao l140m01aDao;
	@Resource
	L140S02KDao l140s02kDao;

	@Resource
	DocLogService docLogService;

	@Resource
	DocFileDao docFileDao;

	@Resource
	BranchService branchService;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	MisdbBASEService misDBService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	LMSService lmsService;

	@Resource
	CodeTypeService codeService;

	@Resource
	EloandbBASEService eloanDbBaseService;
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C999M01A) {
					if (Util.isEmpty(((C999M01A) model).getRandomCode())) {
						((C999M01A) model).setUpdater(user.getUserId());
						((C999M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						((C999M01A) model).setRandomCode(IDGenerator
								.getRandomCode());
						c999m01aDao.save((C999M01A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((C999M01A) model)
									.getMainId());
						}
						// 新增授權檔
						C999A01A c999a01a = new C999A01A();
						c999a01a.setAuthTime(CapDate.getCurrentTimestamp());
						c999a01a.setAuthType("1");
						c999a01a.setAuthUnit(user.getUnitNo());
						c999a01a.setMainId(((C999M01A) model).getMainId());
						c999a01a.setOwner(user.getUserId());
						c999a01a.setOwnUnit(user.getUnitNo());
						c999a01aDao.save(c999a01a);
					} else {
						// 當文件狀態為編製中時文件亂碼才變更
						if ((FlowDocStatusEnum.編製中.toString())
								.equals(((C999M01A) model).getDocStatus())) {
							((C999M01A) model).setRandomCode(IDGenerator
									.getRandomCode());
							((C999M01A) model).setUpdater(user.getUserId());
							((C999M01A) model).setUpdateTime(CapDate
									.getCurrentTimestamp());
							c999m01aDao.save((C999M01A) model);
							if (!"Y".equals(SimpleContextHolder
									.get(EloanConstants.TEMPSAVE_RUN))) {
								tempDataService
										.deleteByMainId(((C999M01A) model)
												.getMainId());
							}
							docLogService.record(((C999M01A) model).getOid(),
									DocLogEnum.SAVE);
						}

					}

				} else if (model instanceof C999M01B) {
					((C999M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999M01B) model).setUpdater(user.getUserId());
					((C999M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999M01B) model).getOid() == null) {
						((C999M01B) model).setCreator(user.getUserId());
						((C999M01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999m01bDao.save((C999M01B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C999M01B) model)
								.getMainId());
					}
				} else if (model instanceof C999M01C) {
					((C999M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999M01C) model).setUpdater(user.getUserId());
					((C999M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999M01C) model).getOid() == null) {
						((C999M01C) model).setCreator(user.getUserId());
						((C999M01C) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999m01cDao.save((C999M01C) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C999M01C) model)
								.getMainId());
					}
				} else if (model instanceof C999M01D) {
					((C999M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999M01D) model).setUpdater(user.getUserId());
					((C999M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999M01D) model).getOid() == null) {
						((C999M01D) model).setCreator(user.getUserId());
						((C999M01D) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999m01dDao.save((C999M01D) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C999M01D) model)
								.getMainId());
					}
				} else if (model instanceof C999S01A) {
					((C999S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999S01A) model).setUpdater(user.getUserId());
					((C999S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999S01A) model).getOid() == null) {
						((C999S01A) model).setCreator(user.getUserId());
						((C999S01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999s01aDao.save((C999S01A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C999S01A) model)
								.getMainId());
					}
				} else if (model instanceof C999S01B) {
					((C999S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999S01B) model).setUpdater(user.getUserId());
					((C999S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999S01B) model).getOid() == null) {
						((C999S01B) model).setCreator(user.getUserId());
						((C999S01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999s01bDao.save((C999S01B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C999S01B) model)
								.getMainId());
					}
				} else if (model instanceof C999S02A) {
					((C999S02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((C999S02A) model).setUpdater(user.getUserId());
					((C999S02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((C999S02A) model).getOid() == null) {
						((C999S02A) model).setCreator(user.getUserId());
						((C999S02A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c999s02aDao.save((C999S02A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((C999S02A) model)
								.getMainId());
					}
				} else if (model instanceof L140M01A) {
					((L140M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L140M01A) model).setUpdater(user.getUserId());
					((L140M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L140M01A) model).getOid() == null) {
						((L140M01A) model).setCreator(user.getUserId());
						((L140M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l140m01aDao.save((L140M01A) model);
				} else if (model instanceof L140S02A) {
					((L140S02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L140S02A) model).setUpdater(user.getUserId());
					((L140S02A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L140S02A) model).getOid() == null) {
						((L140S02A) model).setCreator(user.getUserId());
						((L140S02A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l140s02aDao.save((L140S02A) model);
				} else if (model instanceof L140S02C) {
					((L140S02C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L140S02C) model).setUpdater(user.getUserId());
					((L140S02C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L140S02C) model).getOid() == null) {
						((L140S02C) model).setCreator(user.getUserId());
						((L140S02C) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l140s02cDao.save((L140S02C) model);
				} else if (model instanceof L140S02D) {
					((L140S02D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L140S02D) model).setUpdater(user.getUserId());
					((L140S02D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L140S02D) model).getOid() == null) {
						((L140S02D) model).setCreator(user.getUserId());
						((L140S02D) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l140s02dDao.save((L140S02D) model);
				} else if (model instanceof L140S02E) {
					((L140S02E) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L140S02E) model).setUpdater(user.getUserId());
					((L140S02E) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L140S02E) model).getOid() == null) {
						((L140S02E) model).setCreator(user.getUserId());
						((L140S02E) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l140s02eDao.save((L140S02E) model);
				} else if (model instanceof L140S02F) {
					((L140S02F) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L140S02F) model).setUpdater(user.getUserId());
					((L140S02F) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L140S02F) model).getOid() == null) {
						((L140S02F) model).setCreator(user.getUserId());
						((L140S02F) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l140s02fDao.save((L140S02F) model);
				} else if (model instanceof L140S02I) {
					((L140S02I) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L140S02I) model).setUpdater(user.getUserId());
					((L140S02I) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L140S02I) model).getOid() == null) {
						((L140S02I) model).setCreator(user.getUserId());
						((L140S02I) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l140s02iDao.save((L140S02I) model);
				} else if (model instanceof L140S02K) {
					((L140S02K) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L140S02K) model).setUpdater(user.getUserId());
					((L140S02K) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L140S02K) model).getOid() == null) {
						((L140S02K) model).setCreator(user.getUserId());
						((L140S02K) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l140s02kDao.save((L140S02K) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C999A01A) {
					c999a01aDao.delete((C999A01A) model);
				} else if (model instanceof C999M01A) {
					c999m01aDao.delete((C999M01A) model);
				} else if (model instanceof C999M01B) {
					c999m01bDao.delete((C999M01B) model);
				} else if (model instanceof C999M01C) {
					c999m01cDao.delete((C999M01C) model);
				} else if (model instanceof C999M01D) {
					c999m01dDao.delete((C999M01D) model);
				} else if (model instanceof C999S01A) {
					c999s01aDao.delete((C999S01A) model);
				} else if (model instanceof C999S01B) {
					c999s01bDao.delete((C999S01B) model);
				} else if (model instanceof C999S02A) {
					c999s02aDao.delete((C999S02A) model);
				} else if (model instanceof L140M01A) {
					l140m01aDao.delete((L140M01A) model);
				} else if (model instanceof L140S02A) {
					l140s02aDao.delete((L140S02A) model);
				} else if (model instanceof L140S02C) {
					l140s02cDao.delete((L140S02C) model);
				} else if (model instanceof L140S02D) {
					l140s02dDao.delete((L140S02D) model);
				} else if (model instanceof L140S02E) {
					l140s02eDao.delete((L140S02E) model);
				} else if (model instanceof L140S02F) {
					l140s02fDao.delete((L140S02F) model);
				} else if (model instanceof L140S02I) {
					l140s02iDao.delete((L140S02I) model);
				} else if (model instanceof L140S02K) {
					l140s02kDao.delete((L140S02K) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C999M01A.class) {
			return c999m01aDao.findPage(search);
		} else if (clazz == C999M01B.class) {
			return c999m01bDao.findPage(search);
		} else if (clazz == C999M01C.class) {
			return c999m01cDao.findPage(search);
		} else if (clazz == C999M01D.class) {
			return c999m01dDao.findPage(search);
		} else if (clazz == C999S01A.class) {
			return c999s01aDao.findPage(search);
		} else if (clazz == C999S01B.class) {
			return c999s01bDao.findPage(search);
		} else if (clazz == C999S02A.class) {
			return c999s02aDao.findPage(search);
		} else if (clazz == L140M01A.class) {
			return l140m01aDao.findPage(search);
		} else if (clazz == L140S02A.class) {
			return l140s02aDao.findPage(search);
		} else if (clazz == L140S02C.class) {
			return l140s02cDao.findPage(search);
		} else if (clazz == L140S02D.class) {
			return l140s02dDao.findPage(search);
		} else if (clazz == L140S02E.class) {
			return l140s02eDao.findPage(search);
		} else if (clazz == L140S02F.class) {
			return l140s02fDao.findPage(search);
		} else if (clazz == L140S02I.class) {
			return l140s02iDao.findPage(search);
		} else if (clazz == L140S02K.class) {
			return l140s02kDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}

	@Override
	public Page<Map<String, Object>> listDupNoToCustId(String custId,
			ISearch search) throws CapException {
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		List<Map<String, Object>> list = lmsCustdataService
				.findCustDataByCustId(custId);
		// 同身份證字號的人列表
		for (Map<String, Object> map : list) {
			data = new HashMap<String, Object>();
			data.put("custName", Util.nullToSpace(map.get("cName")));
			data.put("custId", Util.nullToSpace(map.get("custId")));
			data.put("dupNo", Util.nullToSpace(map.get("dupNo")));
			beanList.add(data);
		}

		return new Page<Map<String, Object>>(beanList, beanList.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public List<C999A01A> findC999a01aByMainId(String mainId) {
		return c999a01aDao.findByMainId(mainId);
	}	
	
	@Override
	public C999M01A findC999m01aByOid(String oid) {
		return c999m01aDao.findByOid(oid);
	}	
	
	@Override
	public C999M01A findC999m01aByMainId(String mainId) {
		return c999m01aDao.findByMainId(mainId);
	}

	@Override
	public C999M01B findC999m01bByUniqueKey(String mainId, String custId,
			String dupNo, String type) {
		return c999m01bDao.findByUniqueKey(mainId, custId, dupNo, type);
	}

	@Override
	public List<C999M01B> findC999m01bByMainId(String mainId) {
		return c999m01bDao.findByMainId(mainId);
	}

	@Override
	public List<C999M01C> findC999m01cByMainId(String mainId) {
		return c999m01cDao.findByMainId(mainId);
	}

	@Override
	public void saveListC999m01c(List<C999M01C> list) {
		if (list != null && !list.isEmpty()) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			int count = 0;
			for (C999M01C model : list) {
				model.setUpdater(user.getUserId());
				model.setUpdateTime(CapDate.getCurrentTimestamp());
				list.set(count, model);
				count++;
			}
			c999m01cDao.save(list);
		}
	}

	@Override
	public C999M01D findC999m01dByUniqueKey(String mainId, String itemType) {
		return c999m01dDao.findByUniqueKey(mainId, itemType);
	}

	@Override
	public List<C999M01D> findC999m01dByMainId(String mainId) {
		return c999m01dDao.findByMainId(mainId);
	}

	@Override
	public C999S01A findC999s01aByOid(String oid) {
		return c999s01aDao.findByOid(oid);
	}

	@Override
	public List<C999S01A> findC999s01aByMainId(String mainId) {
		return c999s01aDao.findByMainId(mainId);
	}

	@Override
	public void saveListC999s01a(List<C999S01A> list) {
		if (list != null && !list.isEmpty()) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			int count = 0;
			for (C999S01A model : list) {
				model.setUpdater(user.getUserId());
				model.setUpdateTime(CapDate.getCurrentTimestamp());
				list.set(count, model);
				count++;
			}
			c999s01aDao.save(list);
		}
	}

	@Override
	public C999S01B findC999s01bByOid(String oid) {
		return c999s01bDao.findByOid(oid);
	}

	@Override
	public List<C999S01B> findC999s01bByMainId(String mainId) {
		return c999s01bDao.findByMainId(mainId);
	}

	@Override
	public C999S01B findC999s01bByUniqueKey(String mainId, String pid,
			String type) {
		return c999s01bDao.findByUniqueKey(mainId, pid, type);
	}

	@Override
	public void saveListC999s01b(List<C999S01B> list) {
		if (!list.isEmpty()) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			int count = 0;
			for (C999S01B model : list) {
				model.setUpdater(user.getUserId());
				model.setUpdateTime(CapDate.getCurrentTimestamp());
				list.set(count, model);
				count++;
			}
			c999s01bDao.save(list);
		}
	}

	@Override
	public void saveMetaListC999s01b(List<C999S01B> list, C999M01A meta) {
		saveListC999s01b(list);
		save(meta);
	}

	@Override
	public void saveMetaListC999s01ab(List<C999S01B> list, C999S01A c999s01a, C999M01A meta) {
		saveListC999s01b(list);
		save(meta, c999s01a);
	}	
	
	@Override
	public C999S02A findC999s02aByOid(String oid) {
		return c999s02aDao.findByOid(oid);
	}

	@Override
	public List<C999S02A> findC999s02aByMainId(String mainId) {
		return c999s02aDao.findByMainId(mainId);
	}

	@Override
	public L140S01A findL140s01aByOid(String oid) {
		return l140s01aDao.findByOid(oid);
	}

	@Override
	public List<L140S01A> findL140s01aByMainId(String mainId) {
		return l140s01aDao.findByMainId(mainId);
	}

	@Override
	public L140S02A findL140s02aByOid(String oid) {
		return l140s02aDao.findByOid(oid);
	}

	@Override
	public List<L140S02A> findL140s02aByMainId(String mainId) {
		return l140s02aDao.findByMainId(mainId);
	}

	@Override
	public L140S02A findl140s02aByUniqueKey(String mainId, Integer seq) {
		return l140s02aDao.findByUniqueKey(mainId, seq);
	}

	@Override
	public L140S02B findL140s02bByOid(String oid) {
		return l140s02bDao.findByOid(oid);
	}

	@Override
	public List<L140S02B> findL140s02bByMainId(String mainId) {
		return l140s02bDao.findByMainId(mainId);
	}

	@Override
	public L140S02B findl140s02bByUniqueKey(String mainId, Integer seq,
			String cntrNo) {
		return l140s02bDao.findByUniqueKey(mainId, seq, cntrNo);
	}

	@Override
	public L140S02C findL140s02cByOid(String oid) {
		return l140s02cDao.findByOid(oid);
	}

	@Override
	public List<L140S02C> findL140s02cByMainId(String mainId) {
		return l140s02cDao.findByMainId(mainId);
	}

	@Override
	public L140S02C findl140s02cByUniqueKey(String mainId, Integer seq) {
		return l140s02cDao.findByUniqueKey(mainId, seq);
	}

	@Override
	public L140S02D findL140s02dByOid(String oid) {
		return l140s02dDao.findByOid(oid);
	}

	@Override
	public List<L140S02D> findL140s02dByMainId(String mainId) {
		return l140s02dDao.findByMainId(mainId);
	}

	@Override
	public L140S02D findl140s02dByUniqueKey(String mainId, Integer seq,
			Integer phase) {
		return l140s02dDao.findByUniqueKey(mainId, seq, phase);
	}

	@Override
	public L140S02E findL140s02eByOid(String oid) {
		return l140s02eDao.findByOid(oid);
	}

	@Override
	public List<L140S02E> findL140s02eByMainId(String mainId) {
		return l140s02eDao.findByMainId(mainId);
	}

	@Override
	public L140S02E findl140s02eByUniqueKey(String mainId, Integer seq) {
		return l140s02eDao.findByUniqueKey(mainId, seq);
	}

	@Override
	public L140S02I findL140s02iByOid(String oid) {
		return l140s02iDao.findByOid(oid);
	}

	@Override
	public List<L140S02I> findL140s02iByMainId(String mainId) {
		return l140s02iDao.findByMainId(mainId);
	}

	@Override
	public L140S02I findl140s02iByUniqueKey(String mainId, Integer seq,
			String stdCustId, String stdDupNo) {
		return l140s02iDao.findByUniqueKey(mainId, seq, stdCustId, stdDupNo);
	}

	@Override
	public L140S02K findL140s02kByOid(String oid) {
		return l140s02kDao.findByOid(oid);
	}

	@Override
	public List<L140S02K> findL140s02kByMainId(String mainId) {
		return l140s02kDao.findByMainId(mainId);
	}

	@Override
	public L140S02K findl140s02kByUniqueKey(String mainId, Integer seq) {
		return l140s02kDao.findByUniqueKey(mainId, seq);
	}

	@Override
	public String getCustName(String l140CustName, char type, String args) {
		switch (type) {
		case '1':
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M06Page.class);
			StringBuilder sbName = new StringBuilder();
			if (args.equals(CtrConstants.Book9990.CreateType.分行)) {
				return sbName.append(pop.getProperty("C999M01AM06.title03"))
						.append("(").append(getBrName()).append(")").toString();
			} else if (args.equals(CtrConstants.Book9990.CreateType.全行)) {
				// C999M01AM06.title03 = 兆豐國際商業銀行股份有限公司
				return pop.getProperty("C999M01AM06.title03");
			}
		case '2':
			return Util.trim(l140CustName);
		}
		return null;
	}

	@Override
	public String getBrNo() {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		return Util.trim(user.getUnitNo());
	}

	@Override
	public String getBrName() {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		return Util.trim(branchService.getBranchName(user.getUnitNo()));
	}

	// /**
	// * 篩選重複項次
	// *
	// * @param itemNos
	// * 所有項次群組
	// * @return 非重複的項次群組
	// */
	// private Object[] filterItemNo(String[] itemNos) {
	// Set<String> setItemNo = new HashSet<String>();
	// for (String itemNo : itemNos) {
	// setItemNo.add(itemNo);
	// }
	// return (setItemNo.size() > 0) ? setItemNo.toArray() : null;
	// }

	// /**
	// * 獲取兩個字串陣列之間的重複元素集合
	// *
	// * @param array1
	// * 物件陣列參數1
	// * @param array2
	// * 物件陣列參數2
	// * @return
	// */
	// @SuppressWarnings({ "rawtypes", "unchecked" })
	// public static Object[] findSame(Object array1[], Object array2[]) {
	// HashSet result = new HashSet();// 重複元素結果集合
	// HashSet set = new HashSet();// 利用HashSet來尋找重複元素
	// for (Object str : array1) {
	// set.add(Util.trim(Util.nullToSpace(str)));// 把 array1 添加到 set,有過濾作用
	// }
	// for (Object str2 : array2) {// 遍歷第二個數組
	// if (!set.add(Util.trim(Util.nullToSpace(str2)))) {// 若有重複元素，add方法返回
	// // false
	// result.add(Util.trim(Util.nullToSpace(str2)));// 將重複出現的元素加入結果集合
	// }
	// }
	// return (result.size() > 0) ? result.toArray() : null;
	// }

	@Override
	public List<C999S01A> impotAllC999s01a(String[] mainIdsFor140,
			String c999MainId, PageParameters params)
			throws CapMessageException {
		List<C999S01A> list = new ArrayList<C999S01A>();
		List<C900M01A> listProd = c900m01aDao.getAll(true);
		String contractType = Util.trim(params.getString("contractType"));

		// 用來紀錄項次數值，預設從1開始指派
		int count = 1;
		for (String mainId : mainIdsFor140) {
			L140M01A l140m01a = l140m01aDao.findByMainId(mainId);
			String l120MainId = (l140m01a.getL120m01c() == null) ? UtilConstants.Mark.SPACE
					: l140m01a.getL120m01c().getMainId();
			L120M01A meta = l120m01aDao.findByMainId(l120MainId);
			List<L140S02A> listL140s02a = this.findL140s02aByMainId(mainId);
			for (L140S02A l140s02a : listL140s02a) {
				// 個金產品種類檔是否有產品種類
				boolean hasProd = false;
				String prodKind = Util.trim(l140s02a.getProdKind());
				for (C900M01A proModel : listProd) {
					if (prodKind.equals(Util.trim(proModel.getProdKind()))) {
						hasProd = true;
						break;
					}
				}
				// 有產品種類
				if (hasProd) {
					String c140MainId = Util.trim(l140s02a.getMainId());
					L140S02E l140s02e = l140s02eDao.findByUniqueKey(c140MainId,
							count);
					if (l140s02e == null) {
						l140s02e = new L140S02E();
					}
					L140S02F l140s02f = l140s02fDao.findByUniqueKey(c140MainId,
							count);
					if (l140s02f == null) {
						l140s02f = new L140S02F();
					}
					// C900M01D c900m01d = c900m01dDao.findByUniqueKey(Util
					// .trim(l140s02a.getSubjCode()));
					// if(c900m01d == null){
					// c900m01d = new C900M01D();
					// }
					// 建立個金約據書產品種類檔
					C999S01A c999s01a = this.addC999s01a(c999MainId, count,
							Util.trim(l140m01a.getCntrNo()), prodKind,
							Util.trim(l140s02a.getSubjCode()),
							Util.trim(l140s02a.getLoanCurr()),
							this.trimNull(l140s02a.getLoanAmt()));
					list.add(c999s01a);
					// 這裡丟出額度明細表MainId及項次以尋找契約內容並建立契約內容表(C999S01B)
					c999s01bDao.save(getAllC999S01B(meta, l140s02a, l140s02e,
							l140s02f, count, Util.trim(c999MainId),
							Util.trim(l120MainId),
							Util.trim(c999s01a.getUid()), contractType));
					count++;
				}
			}
		}
		if (count > 2
				&& CtrConstants.Book9990.contractType.政策性留學貸款
						.equals(contractType)) {
			// 政策性留貸只會有一筆產品種類
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M06Page.class);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.注意,
					pop.getProperty("C999M01AM06.message06")), getClass());
		}
		return list;
	}

	/**
	 * 依照有產品種類之額度明細表文件編號與項次設定所有個金約據書契約內容檔(新增約據書專用)
	 * 
	 * @param c140MainId
	 *            有產品種類之額度明細表文件編號
	 * @param seq
	 *            項次
	 * @param c999MainId
	 *            個金約據書主檔文件編號
	 * @return 設定好的所有個金約據書契約內容檔
	 */
	private List<C999S01B> getAllC999S01B(L120M01A meta, L140S02A l140s02a,
			L140S02E l140s02e, L140S02F l140s02f, Integer seq,
			String c999MainId, String l120MainId, String uid,
			String contractType) {
		List<C999S01B> list = new ArrayList<C999S01B>();
		List<String> oList = new ArrayList<String>();
		StringBuilder tempSb = new StringBuilder();
		tempSb.setLength(0);
		// 償還辦法Json
		JSONObject json1 = new JSONObject();
		// 利息計付Json
		JSONObject json2 = new JSONObject(); // 限制清償
		JSONObject json2a = new JSONObject(); // 得隨時清償
		// 借款用途Json
		JSONObject json3 = new JSONObject();
		// 契約金額Json
		JSONObject json4 = new JSONObject();

		// 指標位置
		int index;

		if (CtrConstants.Book9990.contractType.一般.equals(contractType)) {
			// 償還辦法設定
			if (l140s02e != null) {
				if ("1".equals(Util.trim(l140s02e.getPayWay()))
						&& UtilConstants.DEFAULT.是.equals(Util.trim(l140s02e
								.getNowExtend()))) {
					json1.put("22Ara", "3");
				} else if ("1".equals(Util.trim(l140s02e.getPayWay()))
						&& (UtilConstants.DEFAULT.否.equals(Util.trim(l140s02e
								.getNowExtend())) || Util.isEmpty(Util
								.trim(l140s02e.getNowExtend())))) {
					json1.put("22Ara", "2");
				} else if ("2".equals(Util.trim(l140s02e.getPayWay()))) {
					json1.put("22Ara", "4");
				} else if ("7".equals(Util.trim(l140s02e.getPayWay()))) {
					json1.put("22Ara", "1");
				} else if (!"1".equals(Util.trim(l140s02e.getPayWay()))
						&& !"2".equals(Util.trim(l140s02e.getPayWay()))
						&& !"7".equals(Util.trim(l140s02e.getPayWay()))) {
					json1.put("22Aca", "5");
					json1.put("jsonDataE", Util.trim(l140s02a.getPayoffWay()));
				}
			}

			// 利息計付設定
			if (l140s02f != null) {
				if (Util.isNotEmpty(Util.trim(l140s02f.getPCalCon1()))) {
					// _23Aca _jsonDataF01
					json2.put("23Aca", "2");
					json2.put("jsonDataF01", Util.trim(l140s02a.getRateDesc()));
				} else {
					json2a.put("23Acb", "4");
					json2a.put("jsonDataF02", Util.trim(l140s02a.getRateDesc()));
				}
			}

			// 借款用途
			if (meta != null) {
				String[] purposes = Util.trim(meta.getPurpose()).split(
						UtilConstants.Mark.SPILT_MARK);
				for (String purpose : purposes) {
					if (Util.isNotEmpty(purpose)) {
						switch (purpose.charAt(0)) {
						case 'G': // 個人或家庭理財
							json3.put("19Ac1", "1");
							break;
						case 'A': // 購買房屋
							json3.put("19Ac2", "2");
							break;
						case 'B': // 修繕房屋
							json3.put("19Ac3", "3");
							break;
						case 'D': // 購買汽車
							json3.put("19Ac4", "4");
							break;
						case 'J': // 繳付甲方或甲方子女學費
							json3.put("19Ac5", "5");
							break;
						case 'H': // 購置耐久性消費財
							json3.put("19Ac6", "6");
							break;
						case '3': // 其他
							json3.put("19Ac7", "7");
							json3.put("jsonDataB",
									Util.trim(meta.getPurposeOth()));
							break;
						default:
							if (!json3.containsKey("19Ac7")
									|| Util.isEmpty(json3.getString("19Ac7"))) {
								json3.put("19Ac7", "7");
							} else {
								tempSb.append(
										(tempSb.length() > 0) ? UtilConstants.Mark.SPACE
												: Util.trim(json3
														.getString("jsonDataB")))
										.append("，");
							}
							oList.add(codeTypeService.getDescOfCodeType(
									"cls1141_purpose", purpose));
						}
					}
				}
				index = oList.size();
				for (int i = 0; i < index; i++) {
					tempSb.append(oList.get(i)).append("、");
					if (i == index - 1) {
						// 去除頓號
						tempSb.deleteCharAt(tempSb.length() - 1);
					}
				}
				if (tempSb.length() > 0) {
					json3.put("jsonDataB", tempSb.toString());
				}
			}

			// 契約金額
			json4.put("jsonDataA", NumConverter.addComma(l140s02a.getLoanAmt()));

			list.add(addC999s01b(json1, c999MainId, uid,
					CtrConstants.Book9990.Type.償還辦法));
			list.add(addC999s01b(json2, c999MainId, uid,
					CtrConstants.Book9990.Type.利息計付_限制清償期間));
			list.add(addC999s01b(json2a, c999MainId, uid,
					CtrConstants.Book9990.Type.利息計付_得隨時清償));
			list.add(addC999s01b(json3, c999MainId, uid,
					CtrConstants.Book9990.Type.借款用途));
			list.add(addC999s01b(json4, c999MainId, uid,
					CtrConstants.Book9990.Type.契約金額));
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.動用方式));
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.撥款方式));
		} else if (CtrConstants.Book9990.contractType.政策性留學貸款
				.equals(contractType)) {
			// 政策留貸
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.契約金額_政策留貸));
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.借款用途_政策留貸));
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.申請方式及借款期限_寬限期));
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.動用方式_政策留貸));
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.撥款方式_政策留貸));
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.償還辦法_政策留貸));
			list.add(addC999s01b(new JSONObject(), c999MainId, uid,
					CtrConstants.Book9990.Type.利息計付_政策留貸));
		}
		return list;
	}

	/**
	 * 新增個金約據書契約內容檔
	 * 
	 * @param json
	 *            jsonData
	 * @param c999MainId
	 *            個金約據書主檔文件編號
	 * @param uid
	 *            個金約據書產品種類uid
	 * @param kind
	 *            種類
	 * @return 個金約據書契約內容檔
	 */
	private C999S01B addC999s01b(JSONObject json, String c999MainId,
			String uid, String kind) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C999S01B c999s01b = new C999S01B();
		c999s01b.setMainId(c999MainId);
		c999s01b.setPid(uid);
		c999s01b.setType(kind);
		c999s01b.setJsonData(json.toString());
		c999s01b.setUpdateTime(CapDate.getCurrentTimestamp());
		c999s01b.setUpdater(user.getUserId());
		c999s01b.setUpdateTime(CapDate.getCurrentTimestamp());
		c999s01b.setCreator(user.getUserId());
		c999s01b.setCreateTime(CapDate.getCurrentTimestamp());
		return c999s01b;
	}

	/**
	 * 新增個金約據書主檔
	 * 
	 * @param meta
	 *            簽報書主檔
	 * @param contractType
	 *            contractType
	 * @param contractType2
	 *            contractType2
	 * @param contractKind
	 *            contractKind
	 * @param l140CustId
	 *            l140CustId
	 * @param l140DupNo
	 *            l140DupNo
	 * @param l140CustName
	 *            l140CustName
	 * @param txCode
	 *            txCode
	 * @return C999M01A C999M01A
	 */
	@Override
	public C999M01A addC999M01A(L120M01A meta, String contractType,
			String contractType2, String contractKind, String l140CustId,
			String l140DupNo, String l140CustName, String txCode) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C999M01A c999m01a = new C999M01A();
		c999m01a.setContractType(contractType);
		c999m01a.setContractKind(contractKind);
		c999m01a.setContractType2(contractType2);
		c999m01a.setMainId(IDGenerator.getUUID());
		c999m01a.setSrcMainId(meta.getMainId());
		c999m01a.setTypCd(meta.getTypCd());
		c999m01a.setCustId(l140CustId);
		c999m01a.setDupNo(l140DupNo);
		c999m01a.setCustName(l140CustName);
		c999m01a.setUnitType(meta.getUnitType());
		c999m01a.setOwnBrId(meta.getOwnBrId());
		c999m01a.setCaseYear(meta.getCaseYear());
		c999m01a.setCaseBrId(meta.getCaseBrId());
		c999m01a.setCaseSeq(meta.getCaseSeq());
		c999m01a.setCaseNo(meta.getCaseNo());
		c999m01a.setCaseDate(meta.getCaseDate());
		c999m01a.setCreator(user.getUserId());
		c999m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c999m01a.setDocStatus(FlowDocStatusEnum.編製中.toString());
		// 給save使用的(因為剛新增的資料會沒有文件亂碼)
		// c999m01a.setRandomCode(IDGenerator.getRandomCode());
		c999m01a.setTxCode(txCode);
		return c999m01a;
	}

	/**
	 * 新增個金約據書立約人檔
	 * 
	 * @param custId
	 *            custId
	 * @param dupNo
	 *            dupNo
	 * @param custName
	 *            custName
	 * @param type
	 *            type
	 * @param custPos
	 *            custPos
	 * @param c999MainId
	 *            c999MainId
	 * @return C999M01B
	 */
	@Override
	public C999M01B addC999m01b(String l140CustId, String l140DupNo,
			String l140CustName, String type, String c999MainId) {
		C999M01B c999m01b = new C999M01B();
		c999m01b.setMainId(c999MainId);
		c999m01b.setType(type);
		// TODO 待Mega提供檢查分行全行Method後修改，目前暫時預設為分行(1)
		c999m01b.setCustName(getCustName(
				l140CustName,
				Util.isEmpty(type) ? null : type.charAt(0),
				(CtrConstants.Book9990.KindType.兆豐國際商業銀行股份有限公司_乙方.equals(type)) ? CtrConstants.Book9990.KindType.兆豐國際商業銀行股份有限公司_乙方
						: UtilConstants.Mark.SPACE));
		// 取得地址相關資料
		Map<String, Map<Integer, String>> addrData = this.findAddrData(
				l140CustId, l140DupNo);
		if (CtrConstants.Book9990.KindType.兆豐國際商業銀行股份有限公司_乙方.equals(type)) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			// 取得單位相關資料
			Map<String, String> bankMap = lmsService.getBrnoData(user
					.getUnitNo());
			if (bankMap != null && !bankMap.isEmpty()) {
				// 單位代碼
				c999m01b.setBrNo(bankMap.get("unitBrno"));
				// 單位名稱
				c999m01b.setBrName(bankMap.get("unitName"));
				// 單位地址
				c999m01b.setAddr(bankMap.get("unitAddr"));
				// 電話
				c999m01b.setTel(bankMap.get("unitTel"));
				// 統編
				c999m01b.setCustId(bankMap.get("unitTaxNo"));
				c999m01b.setDupNo("");
			}
		} else {
			c999m01b.setCustId(l140CustId);
			c999m01b.setDupNo(l140DupNo);
			if (addrData.isEmpty()) {
				// 郵遞區號
				c999m01b.setAddrZip(null);
				// 地址(縣市)
				c999m01b.setAddrCity(null);
				// 地址(區鄉鎮市)
				c999m01b.setAddrTown(null);
				// 地址
				c999m01b.setAddr(null);
			} else {
				// 郵遞區號
				c999m01b.setAddrZip(getAddrZip(addrData));
				// 地址(縣市)
				c999m01b.setAddrCity(getAddrCity(addrData));
				// 地址(區鄉鎮市)
				c999m01b.setAddrTown(getAddrTown(addrData));
				// 地址
				c999m01b.setAddr(getAddr(addrData));
			}
		}
		return c999m01b;
	}

	/**
	 * 新增個金約據書連保人(保證人)檔
	 * 
	 * @param custId
	 *            custId
	 * @param dupNo
	 *            dupNo
	 * @param custPos
	 *            custPos
	 * @param c999MainId
	 *            c999MainId
	 * @param c140MainId
	 *            c140MainId
	 * @return C999M01C C999M01C
	 */
	@Override
	public C999M01C addC999m01c(String custId, String dupNo, String custName,
			String custPos, String c999MainId) {
		C999M01C c999m01c = new C999M01C();
		// 文件編號
		c999m01c.setMainId(Util.trim(c999MainId));
		// 統一編號
		c999m01c.setCustId(custId);
		// 重覆序號
		c999m01c.setDupNo(dupNo);
		// 名稱
		c999m01c.setCustName(custName);
		// 性質(相關身份)
		c999m01c.setCustPos(custPos);
		// 取得地址相關資料
		Map<String, Map<Integer, String>> addrData = this.findAddrData(custId,
				dupNo);
		if (addrData.isEmpty()) {
			// 郵遞區號
			c999m01c.setAddrZip(null);
			// 地址(縣市)
			c999m01c.setAddrCity(null);
			// 地址(區鄉鎮市)
			c999m01c.setAddrTown(null);
			// 地址
			c999m01c.setAddr(null);
		} else {
			// 郵遞區號
			c999m01c.setAddrZip(getAddrZip(addrData));
			// 地址(縣市)
			c999m01c.setAddrCity(getAddrCity(addrData));
			// 地址(區鄉鎮市)
			c999m01c.setAddrTown(getAddrTown(addrData));
			// 地址
			c999m01c.setAddr(getAddr(addrData));
		}
		return c999m01c;
	}

	/**
	 * 新增個金約據書產品種類檔
	 * 
	 * @param c999MainId
	 *            c999MainId
	 * @param itemNo
	 *            itemNo
	 * @param cntrNo
	 *            cntrNo
	 * @param prodKind
	 *            prodKind
	 * @param subjCode
	 *            subjCode
	 * @param loanCurr
	 *            loanCurr
	 * @param loanAmt
	 *            loanAmt
	 * @return C999S01A C999S01A
	 */
	@Override
	public C999S01A addC999s01a(String c999MainId, Integer itemNo,
			String cntrNo, String prodKind, String subjCode, String loanCurr,
			BigDecimal loanAmt) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C999S01A c999s01a = new C999S01A();
		// 文件編號
		c999s01a.setMainId(c999MainId);
		// Uid
		c999s01a.setUid(IDGenerator.getUUID());
		// 項次
		c999s01a.setItemNo(itemNo);
		// 額度序號
		c999s01a.setCntrNo(Util.trim(cntrNo));
		// 產品種類
		c999s01a.setProdKind(prodKind);
		// 科目
		c999s01a.setSubjCode(Util.trim(subjCode));
		// 幣別
		c999s01a.setLoanCurr(Util.trim(loanCurr));
		// 金額
		c999s01a.setLoanAmt(this.trimNull(loanAmt));
		// 建立時間
		c999s01a.setCreateTime(CapDate.getCurrentTimestamp());
		// 建立人員
		c999s01a.setCreator(user.getUserId());
		return c999s01a;
	}

	/**
	 * 新增個金約據書契約內容檔
	 * 
	 * @param c999MainId
	 *            c999MainId
	 * @param pid
	 *            pid
	 * @param itemType
	 *            itemType
	 * @param itemDscr
	 *            itemDscr
	 * @return C999S01B C999S01B
	 */
	@Override
	public C999S01B addC999s01b(String c999MainId, String pid, String itemType,
			String itemDscr) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C999S01B c999s01b = new C999S01B();
		// 文件編號
		c999s01b.setMainId(c999MainId);
		// Pid
		c999s01b.setPid(pid);
		// 契約項目
		c999s01b.setType(itemType);
		// 契約內容
		c999s01b.setJsonData(Util.trim(itemDscr));
		// 異動人員號碼
		c999s01b.setUpdater(user.getUserId());
		// 異動日期
		c999s01b.setUpdateTime(CapDate.getCurrentTimestamp());
		// 建立人員號碼
		c999s01b.setCreator(user.getUserId());
		// 建立日期
		c999s01b.setCreateTime(CapDate.getCurrentTimestamp());
		return c999s01b;
	}

	@Override
	public String comBineC999s01b(String mainId, String type,
			boolean getSubTitle, boolean isWord) throws CapMessageException {
		List<C999S01A> listC999s01a = this.findC999s01aByMainId(mainId);
		String str = null;
		if (!listC999s01a.isEmpty()) {
			str = this.comBineC999s01b(listC999s01a, type, getSubTitle, isWord);
		}
		return Util.trim(str);
	}

	/**
	 * 將契約內容串起來
	 * 
	 * @param jsons
	 * @param type
	 * @param itemNos
	 * @param getSubTitle
	 *            : 是否只取得子標題 true->是 false->否
	 * @return getSubTitle = true -> 取得子標題 false -> 取得串好的契約內容
	 * @throws CapMessageException
	 */
	private String getDscr(JSONArray jsons, String type, List<String> itemNos,
			boolean getSubTitle, boolean isWord) throws CapMessageException {
		JSONObject resultJson = new JSONObject();
		StringBuilder sb = new StringBuilder();
		// jsonDataA 契約金額
		// jsonDataB 借款用途
		// jsonDataC 動用方式
		// jsonDataD 撥款方式
		// jsonDataE 償還辦法
		// jsonDataF01 利息計付1
		// jsonDataF02 利息計付2
		if (!jsons.isEmpty()) {
			if (jsons.size() > 1) {
				// 多筆產品種類
				if (getSubTitle) {
					sb.append(this.getSubTitleByType(jsons, type, false));
				} else {
					for (int i = 0; i < jsons.size(); i++) {
						if (Util.isNotEmpty(this.getJDataByType(
								jsons.getJSONObject(i), type, false))) {
							// 利用CodeType讀取項次名稱
							sb.append(getKeyName(itemNos.get(i)))
									.append("：")
									.append(this.getJDataByType(
											jsons.getJSONObject(i), type, false))
									.append((isWord) ? strTab : "<br/>");
						}
					}
				}
			} else if (jsons.size() == 1) {
				// 單筆產品種類
				if (getSubTitle) {
					sb.append(this.getSubTitleByType(jsons, type, true));
				} else {
					sb.append(
							this.getJDataByType(jsons.getJSONObject(0), type,
									false)).append("。");
				}
			} else {
				// 例外狀況...
				// C999M01AM07.message02=資料異常！請洽資訊處！
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS9990M07Page.class);
				throw new CapMessageException(
						pop.getProperty("C999M01AM07.message02"), getClass());
			}
		}
		resultJson.put(Util.trim(this.getJDataByType(null, type, true)),
				sb.toString());
		return Util.trim(Util.nullToSpace(sb));
	}

	@Override
	public String getJDataByType(JSONObject json, String type, boolean getKey) {
		if (CtrConstants.Book9990.Type.契約金額.equals(type)) {
			return (getKey) ? "jsonDataA" : Util.trim(Util.nullToSpace(json
					.get("jsonDataA")));
		} else if (CtrConstants.Book9990.Type.借款用途.equals(type)) {
			return (getKey) ? "jsonDataB" : Util.trim(Util.nullToSpace(json
					.get("jsonDataB")));
		} else if (CtrConstants.Book9990.Type.動用方式.equals(type)) {
			return (getKey) ? "jsonDataC" : Util.trim(Util.nullToSpace(json
					.get("jsonDataC")));
		} else if (CtrConstants.Book9990.Type.撥款方式.equals(type)) {
			return (getKey) ? "jsonDataD" : Util.trim(Util.nullToSpace(json
					.get("jsonDataD")));
		} else if (CtrConstants.Book9990.Type.償還辦法.equals(type)) {
			return (getKey) ? "jsonDataE" : Util.trim(Util.nullToSpace(json
					.get("jsonDataE")));
		} else if (CtrConstants.Book9990.Type.利息計付_限制清償期間.equals(type)) {
			return (getKey) ? "jsonDataF01" : Util.trim(Util.nullToSpace(json
					.get("jsonDataF01")));
		} else if (CtrConstants.Book9990.Type.利息計付_得隨時清償.equals(type)) {
			return (getKey) ? "jsonDataF02" : Util.trim(Util.nullToSpace(json
					.get("jsonDataF02")));
		} else {
			return null;
		}
	}

	@Override
	public String getSubTitleByType(JSONArray jsons, String type,
			boolean isSingle) {
		StringBuilder sb = new StringBuilder();
		StringBuilder sb2 = new StringBuilder();
		if (!jsons.isEmpty()) {
			for (int index = 0; index < jsons.size(); index++) {
				JSONObject json = jsons.getJSONObject(index);
				if (!json.isEmpty()) {
					if (CtrConstants.Book9990.Type.借款用途.equals(type)) {
						// 借款用途
						if (isSingle) {
							// 單筆時的顯示
							String keys[] = Util.getMapKey(json);
							for (int i = 0; i <= keys.length - 1; i++) {
								// 防止不必要的Key被帶入子標題
								if (!keys[i].contains("oid")
										&& !keys[i].contains("jsonData")) {
									String jsonVal = Util.trim(Util
											.nullToSpace(json.get(keys[i])));
									if (!Util.isEmpty(jsonVal)) {
										sb.append(
												NumConverter
														.numberToChinese(jsonVal))
												.append("、");
									}
								}
							}
							sb.deleteCharAt(sb.length() - 1);
						} else {
							// 多筆時的顯示
							if (json.containsKey(CtrConstants.Book9990.CheckBoxId.借款用途CheckBox)) {
								// 有勾選"其他"checkBox
								sb.append(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
										.get(CtrConstants.Book9990.CheckBoxId.借款用途CheckBox)))));
							}
						}
						return sb.toString();
					} else if (CtrConstants.Book9990.Type.動用方式.equals(type)) {
						// 動用方式
						if (isSingle) {
							// 單筆時的顯示
							if (json.containsKey(CtrConstants.Book9990.RadioName.動用方式Radio)) {
								// 有點選Radio
								sb.append(
										NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
												.get(CtrConstants.Book9990.RadioName.動用方式Radio)))));
							}
						}
						if (json.containsKey(CtrConstants.Book9990.CheckBoxId.動用方式CheckBox)) {
							// 有勾選"其他"checkBox
							sb.append(
									(Util.isNotEmpty(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.動用方式CheckBox))))))) ? "、"
											: "")
									.append(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.動用方式CheckBox))))));
						}
						return sb.toString();
					} else if (CtrConstants.Book9990.Type.撥款方式.equals(type)) {
						// 撥款方式
						if (isSingle) {
							// 單筆時的顯示
							if (json.containsKey(CtrConstants.Book9990.RadioName.撥款方式Radio)) {
								// 有點選Radio
								sb.append(
										NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
												.get(CtrConstants.Book9990.RadioName.撥款方式Radio)))));
							}
						}
						if (json.containsKey(CtrConstants.Book9990.CheckBoxId.撥款方式CheckBox)) {
							// 有勾選"其他"checkBox
							sb.append(
									(Util.isNotEmpty(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.撥款方式CheckBox))))))) ? "、"
											: "")
									.append(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.撥款方式CheckBox))))));
						}
						return sb.toString();
					} else if (CtrConstants.Book9990.Type.償還辦法.equals(type)) {
						if (isSingle) {
							// 單筆時的顯示
							if (json.containsKey(CtrConstants.Book9990.RadioName.償還辦法Radio)) {
								// 有點選Radio
								sb.append(
										NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
												.get(CtrConstants.Book9990.RadioName.償還辦法Radio)))));
							}
						}
						if (json.containsKey(CtrConstants.Book9990.CheckBoxId.償還辦法CheckBox)) {
							// 有勾選"其他"checkBox
							sb.append(
									(Util.isNotEmpty(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.償還辦法CheckBox))))))) ? "、"
											: "")
									.append(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.償還辦法CheckBox))))));
						}
						return sb.toString();
					} else if (CtrConstants.Book9990.Type.利息計付_限制清償期間
							.equals(type)) {
						if (isSingle) {
							// 單筆時的顯示
							if (json.containsKey(CtrConstants.Book9990.RadioName.利息計付_限制清償期間Radio)) {
								// 有點選Radio
								sb.append(
										NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
												.get(CtrConstants.Book9990.RadioName.利息計付_限制清償期間Radio)))));
							}
						}
						if (json.containsKey(CtrConstants.Book9990.CheckBoxId.利息計付_限制清償期間CheckBox)) {
							// 有勾選"其他"checkBox
							sb.append(
									(Util.isNotEmpty(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.利息計付_限制清償期間CheckBox))))))) ? "、"
											: "")
									.append(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.利息計付_限制清償期間CheckBox))))));
						}
						return sb.toString();
					} else if (CtrConstants.Book9990.Type.利息計付_得隨時清償
							.equals(type)) {
						if (isSingle) {
							// 單筆時的顯示
							if (json.containsKey(CtrConstants.Book9990.RadioName.利息計付_得隨時清償Radio)) {
								// 有點選Radio
								sb2.append(
										NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
												.get(CtrConstants.Book9990.RadioName.利息計付_得隨時清償Radio)))));
							}
						}
						if (json.containsKey(CtrConstants.Book9990.CheckBoxId.利息計付_得隨時清償CheckBox)) {
							// 有勾選"其他"checkBox
							sb2.append(
									(Util.isNotEmpty(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.利息計付_得隨時清償CheckBox))))))) ? "、"
											: "")
									.append(Util.trim(NumConverter.numberToChinese(Util.trim(Util.nullToSpace(json
											.get(CtrConstants.Book9990.CheckBoxId.利息計付_得隨時清償CheckBox))))));
						}
						return sb2.toString();
					} else {
						return null;
					}
				}
			}
		} else {
			return null;
		}
		return null;
	}

	/**
	 * 從產品種類將同類型同項次契約內容篩選出來
	 * 
	 * @param listC999s01a
	 *            產品種類檔
	 * @param type
	 *            要篩選的類型
	 * @param getSubTitle
	 *            是否只取得子標題
	 * @return 篩選後的契約內容檔
	 * @throws CapMessageException
	 */
	private String comBineC999s01b(List<C999S01A> listC999s01a, String type,
			boolean getSubTitle, boolean isWord) throws CapMessageException {
		JSONArray jsons = new JSONArray();
		List<String> itemNos = new ArrayList<String>();
		for (C999S01A c999s01a : listC999s01a) {
			// 項次為刪除的產品種類不合併
			if (!CtrConstants.Book9990.ItemNo.刪除.equals(Util.trim(c999s01a
					.getItemNo()))) {
				List<C999S01B> c999s01bs = c999s01a.getC999s01bs();
				if (!c999s01bs.isEmpty()) {
					for (C999S01B c999s01b : c999s01bs) {
						if (type.equals(Util.trim(c999s01b.getType()))) {
							// 取得集合資料
							if (!Util.isEmpty(c999s01b.getJsonData())) {
								jsons.add(JSONObject.fromObject(c999s01b
										.getJsonData()));
								itemNos.add(Util.trim(c999s01a.getItemNo()));
							}
						}
					}
				}
			}
		}
		return this.getDscr(jsons, type, itemNos, getSubTitle, isWord);
	}

	@Override
	public List<C999S01B> setJsonData(C999S01A c999s01a, JSONObject allJson) {
		List<C999S01B> list = c999s01a.getC999s01bs();
		if (!list.isEmpty()) {
			for (C999S01B c999s01b : list) {
				String type = Util.trim(c999s01b.getType());
				JSONObject json = new JSONObject();
				if (CtrConstants.Book9990.Type.契約金額.equals(type)) {
					json = allJson.getJSONObject("s18Panel");
				} else if (CtrConstants.Book9990.Type.借款用途.equals(type)) {
					json = allJson.getJSONObject("s19Panel");
				} else if (CtrConstants.Book9990.Type.動用方式.equals(type)) {
					json = allJson.getJSONObject("s20Panel");
				} else if (CtrConstants.Book9990.Type.撥款方式.equals(type)) {
					json = allJson.getJSONObject("s21Panel");
				} else if (CtrConstants.Book9990.Type.償還辦法.equals(type)) {
					json = allJson.getJSONObject("s22Panel");
				} else if (CtrConstants.Book9990.Type.利息計付_限制清償期間.equals(type)) {
					json = allJson.getJSONObject("s23Panel");
				} else if (CtrConstants.Book9990.Type.利息計付_得隨時清償.equals(type)) {
					json = allJson.getJSONObject("s24Panel");
				} else if (CtrConstants.Book9990.Type.契約金額_政策留貸.equals(type)) {
					json = allJson.getJSONObject("s28Panel");
				} else if (CtrConstants.Book9990.Type.借款用途_政策留貸.equals(type)) {
					json = allJson.getJSONObject("s29Panel");
				} else if (CtrConstants.Book9990.Type.申請方式及借款期限_寬限期
						.equals(type)) {
					json = allJson.getJSONObject("s30Panel");
				} else if (CtrConstants.Book9990.Type.動用方式_政策留貸.equals(type)) {
					json = allJson.getJSONObject("s31Panel");
				} else if (CtrConstants.Book9990.Type.撥款方式_政策留貸.equals(type)) {
					json = allJson.getJSONObject("s32Panel");
				} else if (CtrConstants.Book9990.Type.償還辦法_政策留貸.equals(type)) {
					json = allJson.getJSONObject("s33Panel");
				} else if (CtrConstants.Book9990.Type.利息計付_政策留貸.equals(type)) {
					json = allJson.getJSONObject("s34Panel");
				}

				// else if (CtrConstants.Book9990.Type.違約金及遲延利息.equals(type)) {
				// json = allJson.getJSONObject("s25Panel");
				// } else if (CtrConstants.Book9990.Type.服務.equals(type)) {
				// json = allJson.getJSONObject("s26Panel");
				// } else if (CtrConstants.Book9990.Type.管轄法院.equals(type)) {
				// json = allJson.getJSONObject("s27Panel");
				// }

				if (CtrConstants.Book9990.Type.利息計付_限制清償期間.equals(type)) {
					c999s01b.setJsonData(json.isEmpty() ? null : this
							.getS23ByKind(json,
									CtrConstants.Book9990.Page.利息計付1));
				} else if (CtrConstants.Book9990.Type.利息計付_得隨時清償.equals(type)) {
					c999s01b.setJsonData(json.isEmpty() ? null : this
							.getS23ByKind(json,
									CtrConstants.Book9990.Page.利息計付2));
				} else {
					c999s01b.setJsonData(json.isEmpty() ? Util.trim(c999s01b
							.getJsonData()) : json.toString().replaceAll("_",
							UtilConstants.Mark.SPACE));
				}

			}
		}
		return list;
	}

	/**
	 * 依照類型將利息計付JsonData分割好
	 * 
	 * @param json
	 *            整個利息計付(F01 + F02)
	 * @param kind
	 *            類型
	 * @return 利息計付1: F01 JsonData 利息計付2: F02 JsonData
	 */
	private String getS23ByKind(JSONObject json, int kind) {
		String[] jsonCols = (kind == CtrConstants.Book9990.Page.利息計付1) ? new String[] {
				"23Ara", "23Aca", "jsonDataF01", "jsonDataF01a",
				"jsonDataF01b", "jsonDataF01c", "jsonDataF01d", "jsonDataF01e",
				"jsonDataF01f", "jsonDataF01g", "jsonDataF01h", "jsonDataF01i",
				"jsonDataF01j", "jsonDataF01k", "jsonDataF01l", "jsonDataF01m",
				"jsonDataF01n", "jsonDataF01o", "jsonDataF01p", "jsonDataF01q",
				"jsonDataF01r", "jsonDataF01s", "jsonDataF03a", "jsonDataF03b",
				"jsonDataF03c", "jsonDataF03d", "jsonDataF03e", "jsonDataF03f",
				"jsonDataF03g", "jsonDataF03h", "jsonDataF03i", "jsonDataF03j",
				"23Acc1", "jsonDataF03k", "jsonDataF03l", "jsonDataF03m",
				"jsonDataF03n", "jsonDataF03o", "jsonDataF03p", "jsonDataF03q",
				"jsonDataF03r", "23Acc2", "jsonDataF03s", "jsonDataF03t",
				"jsonDataF03u", "jsonDataF03v", "jsonDataF03w", "jsonDataF03x",
				"jsonDataF03y", "jsonDataF03z", "23Acc3", "jsonDataF03A",
				"jsonDataF03B", "jsonDataF03C", "jsonDataF03D" }
				: new String[] { "23Arb", "23Acb", "jsonDataF02",
						"jsonDataF02a", "jsonDataF02b", "jsonDataF02c",
						"jsonDataF02d", "jsonDataF02e" };
		JSONObject newJson = new JSONObject();
		for (String jsonCol : jsonCols) {
			newJson.put(jsonCol, Util.trim(json.get("_" + jsonCol)));
		}
		return newJson.toString();
	}

	/**
	 * 將BigDecimal資料null改空白且去空白，最後刪除金額撇節號
	 * 
	 * @param bgNum
	 *            BigDecimal資料
	 * @return 修改後的BigDecimal資料
	 */
	private BigDecimal trimNull(BigDecimal bgNum) {
		return new BigDecimal(NumConverter.delComma(Util.trim(Util
				.nullToSpace(bgNum))));
	}

	@Override
	public List<C999M01C> importAllC999m01c(String[] mainIdsFor140,
			String c999MainId) {
		List<C999M01C> list = new ArrayList<C999M01C>();
//		StringBuilder sb = new StringBuilder();
//		sb.setLength(0);
//		for (String mainId : mainIdsFor140) {
//			sb.append((sb.length() > 0) ? "," : UtilConstants.Mark.SPACE)
//			.append("'").append(mainId).append("'");
//		}
		List<Map<String, Object>> listMap = eloanDbBaseService.selToC999m01c(mainIdsFor140);
		if(listMap != null && !listMap.isEmpty()){
			for(Map<String, Object> map : listMap){
				// 額度明細表性質是否與連保人資料相關
				boolean isLianBow = false;
//					C.共同借款人
//					G.連帶保證人(個金)
//					N.ㄧ般保證人(個金)
//					S.擔保品提供人(個金)						
				String custPos = Util.trim(map.get("CUSTPOS"));
				if (CtrConstants.Book9990.CustPos.共同借款人或共同發票人
						.equals(custPos)) {
					isLianBow = true;
				} else if (CtrConstants.Book9990.CustPos.連帶保證人或擔保品提供人兼連帶保證人
						.equals(custPos)) {
					isLianBow = true;
				} else if (CtrConstants.Book9990.CustPos.一般保證人或擔保品提供人兼一般保證人
						.equals(custPos)) {
					isLianBow = true;
				} else if (CtrConstants.Book9990.CustPos.擔保品提供人
						.equals(custPos)) {
					isLianBow = true;
				} else {
					isLianBow = false;
				}
				// 與連保人資料相關
				if (isLianBow) {
					String custId = Util.trim(map.get("CUSTID"));
					String dupNo = Util.trim(map.get("DUPNO"));
					String custName = Util.trim(map.get("CUSTNAME"));
					C999M01C c999m01c = this.addC999m01c(custId, dupNo,
							custName, custPos, c999MainId);
					list.add(c999m01c);
				}				
			}
		}
		return list;
	}

	/**
	 * 由地址相關資料取得郵遞區號
	 * 
	 * @param addrData
	 *            地址相關資料
	 * @return 郵遞區號
	 */
	private Integer getAddrZip(Map<String, Map<Integer, String>> addrData) {
		Map<Integer, String> map = addrData.get("addrZip");
		return (map.isEmpty()) ? null : Util.parseInt(Util.trim(map.get(1)));
	}

	/**
	 * 由地址相關資料取得地址(縣市)
	 * 
	 * @param addrData
	 *            地址相關資料
	 * @return 地址(縣市)
	 */
	private String getAddrCity(Map<String, Map<Integer, String>> addrData) {
		Map<Integer, String> map = addrData.get("addrCity");
		return (map.isEmpty()) ? null : Util.trim(map.get(1));
	}

	/**
	 * 由地址相關資料取得地址(區鄉鎮市)
	 * 
	 * @param addrData
	 *            地址相關資料
	 * @return 地址(區鄉鎮市)
	 */
	private String getAddrTown(Map<String, Map<Integer, String>> addrData) {
		Map<Integer, String> map = addrData.get("addrTown");
		return (map.isEmpty()) ? null : Util.trim(map.get(1));
	}

	/**
	 * 由地址相關資料取得地址
	 * 
	 * @param addrData
	 *            地址相關資料
	 * @return 地址
	 */
	private String getAddr(Map<String, Map<Integer, String>> addrData) {
		Map<Integer, String> map = addrData.get("addr");
		return (map.isEmpty()) ? null : Util.trim(map.get(1));
	}

	/**
	 * 依照統編及重覆序號透過JDBC從MIS DB 取得地址相關資料
	 * 
	 * @param custId
	 *            統編
	 * @param dupNo
	 *            重覆序號
	 * @return 地址相關資料集合
	 */
	private Map<String, Map<Integer, String>> findAddrData(String custId,
			String dupNo) {
		List<?> rows1 = this.misDBService.findELCUS21_selAddrr(custId, dupNo);
		Iterator<?> it1 = rows1.iterator();
		int dataCount = 0;
		// 綜合Map集合
		Map<String, Map<Integer, String>> resultMap = new HashMap<String, Map<Integer, String>>();
		// 郵遞區號Map
		Map<Integer, String> getMap1 = new TreeMap<Integer, String>();
		// 地址(縣市)Map
		Map<Integer, String> getMap2 = new TreeMap<Integer, String>();
		// 地址(區鄉鎮市)Map
		Map<Integer, String> getMap3 = new TreeMap<Integer, String>();
		// 通訊地址Map
		Map<Integer, String> getMap4 = new TreeMap<Integer, String>();
		while (it1.hasNext()) {
			Map<?, ?> dataMap1 = (Map<?, ?>) it1.next();
			dataCount++;
			if (dataCount <= 10) {
				String addrZip = Util.trim(Util.nullToSpace(dataMap1
						.get("ADDRZIP")));
				String addrCity = Util.trim(Util.nullToSpace(dataMap1
						.get("CITYR")));
				String addrTown = Util.trim(Util.nullToSpace(dataMap1
						.get("TOWNR")));
				// 申貸戶之通訊地址...
				if ("FALSE".equals(Util.toFullCharString(Util.trim(Util
						.nullToSpace(dataMap1.get("LINR")))))) {
					StringBuilder sbAddr = new StringBuilder();
					sbAddr.append(addrZip)
							.append(" ")
							.append(addrCity)
							.append(addrTown)
							.append(Util.trim(Util.nullToSpace(dataMap1
									.get("LEER")))).append(sbAddr);
					getMap1.put(dataCount, addrZip);
					getMap2.put(dataCount, addrCity);
					getMap3.put(dataCount, addrTown);
					getMap4.put(dataCount, Util.trim(sbAddr.toString()));
				} else {
					StringBuilder sbAddr = new StringBuilder();
					sbAddr.append(addrZip)
							.append(" ")
							.append(addrCity)
							.append(addrTown)
							.append(Util.trim(Util.nullToSpace(dataMap1
									.get("LEER"))))
							.append(Util.trim(Util.nullToSpace(dataMap1
									.get("LINR"))))
							.append(Util.trim(Util.nullToSpace(dataMap1
									.get("ADDRR"))));
					getMap1.put(dataCount, addrZip);
					getMap2.put(dataCount, addrCity);
					getMap3.put(dataCount, addrTown);
					getMap4.put(dataCount, Util.trim(sbAddr.toString()));
				}
			}
		}
		// 把所有Map種類塞入綜合Map
		resultMap.put("addrZip", getMap1);
		resultMap.put("addrCity", getMap2);
		resultMap.put("addrTown", getMap3);
		resultMap.put("addr", getMap4);
		return resultMap;
	}

	@Override
	public void saveC9990All(C999M01A c999m01a, C999M01B c999m01b1,
			C999M01B c999m01b2, List<C999M01C> listC999m01c,
			List<C999S01A> listC999s01a) {
		this.save(c999m01a, c999m01b1, c999m01b2);
		this.saveListC999m01c(listC999m01c);
		this.saveListC999s01a(listC999s01a);

	}

	@Override
	public List<C999S01A> modifySeqs(String[] oidArray, String[] itemNoArray) throws CapException {
		List<C999S01A> list = new ArrayList<C999S01A>();
		if (oidArray.length > 0 && itemNoArray.length > 0) {
			// 取得真實項次
			// String reaItemNo[] = getRealItemNo(itemNoArray);
			for (int i = 0; i < oidArray.length; i++) {
				C999S01A model = null;
				model = c999s01aDao.findByOid(Util.trim(oidArray[i]));
				if (model != null) {
					list.add(model);
				}
			}
			int index = 0;
			if (index < itemNoArray.length) {
				for (C999S01A model : list) {
					model.setItemNo(Util.parseInt(Util.trim(itemNoArray[index])));
					index++;
				}
			}
		} else {
			// C999M01AM07.message02=資料異常！請洽資訊處！
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M07Page.class);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", pop.getProperty("C999M01AM07.message02")),
					getClass());
		}
		return list;
	}

	// /**
	// * 由含有Html Tag的下拉式選單中取得所選數值
	// *
	// * @param seqArray
	// * 含有HTML Tag的下拉式選單
	// * @return 真正所選值
	// */
	// private String[] getRealItemNo(String seqArray[]) {
	// String[] rseq = new String[seqArray.length];
	// String findSel = "selected";
	// String findVal = "value=";
	// int index = 0;
	// for (String seq : seqArray) {
	// int theSel = seq.indexOf(findSel);
	// int theVal = seq.indexOf(findVal, theSel) + findVal.length();
	// rseq[index] = seq.substring(theVal+1, seq.indexOf(">", theVal)-1);
	// index++;
	// }
	// return rseq;
	// }

	@Override
	public void deleteUploadFile(String[] oids) {

		List<DocFile> docfile = docFileDao.findAllByOid(oids);
		docFileDao.delete(docfile);
	}

	/**
	 * 取得項次名次對應表
	 * 
	 * @param val
	 *            數值
	 * @return 項次名稱
	 */
	public String getKeyName(String val) {
		return Util.isEmpty(val) ? "未編項次" : codeService.getDescOfCodeType(
				"ctrItemNo", val);
	}

	@Override
	public void delRelC999(C999M01A model, List<C999A01A> list1,
			List<C999M01B> list2, List<C999M01C> list3, List<C999M01D> list4,
			List<C999S01A> list5, List<C999S01B> list6, List<C999S02A> list7) {
		if(model != null){
			c999m01aDao.delete(model);
		}
		if(list1 != null && !list1.isEmpty()){
			c999a01aDao.delete(list1);
		}
		if(list2 != null && !list2.isEmpty()){
			c999m01bDao.delete(list2);
		}
		if(list3 != null && !list3.isEmpty()){
			c999m01cDao.delete(list3);
		}
		if(list4 != null && !list4.isEmpty()){
			c999m01dDao.delete(list4);
		}
		if(list5 != null && !list5.isEmpty()){
			c999s01aDao.delete(list5);
		}
		if(list6 != null && !list6.isEmpty()){
			c999s01bDao.delete(list6);
		}
		if(list7 != null && !list7.isEmpty()){
			c999s02aDao.delete(list7);
		}		
	}
}
