package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

/** 放款引介資料檔 **/
public class LNF13E extends GenericBean {

	private static final long serialVersionUID = 1L;

	@Size(max = 12)
	@Column(name = "LNF13E_CONTRACT", length = 12, columnDefinition = "CHAR(12)", unique = true)
	private String lnf13e_contract;

	@Size(max = 6)
	@Column(name = "LNF13E_MEGA_EMPNO", length = 6, columnDefinition = "CHAR(6)")
	private String lnf13e_mega_empno;
	
	/** select * from com.bcodetype where codetype ='L140M01A_agntNo' */
	@Size(max = 5)
	@Column(name = "LNF13E_AGNT_NO", length = 5, columnDefinition = "CHAR(5)")
	private String lnf13e_agnt_no;

	@Size(max = 1)
	@Column(name = "LNF13E_AGNT_CHAIN", length = 1, columnDefinition = "CHAR(1)")
	private String lnf13e_agnt_chain;
	
	/** select * from com.bcodetype where codetype='L140S02A_megaCode' */
	@Size(max = 5)
	@Column(name = "LNF13E_MEGA_CODE", length = 5, columnDefinition = "CHAR(5)")
	private String lnf13e_mega_code;

	@Size(max = 5)
	@Column(name = "LNF13E_SUB_UNITNO", length = 5, columnDefinition = "CHAR(5)")
	private String lnf13e_sub_unitno;

	@Size(max = 6)
	@Column(name = "LNF13E_SUB_EMPNO", length = 6, columnDefinition = "CHAR(6)")
	private String lnf13e_sub_empno;
	
	@Size(max = 22)
	@Column(name = "LNF13E_SUB_EMPNM", length = 22, columnDefinition = "CHAR(22)")
	private String lnf13e_sub_empnm;
	
	@Size(max = 1)
	@Column(name = "LNF13E_INTRO_SRC", length = 1, columnDefinition = "CHAR(1)")
	private String lnf13e_intro_src;
	
	@Size(max = 3)
	@Column(name = "LNF13E_MEGAEMP_BRN", length = 3, columnDefinition = "CHAR(3)")
	private String lnf13e_megaemp_brn;
	
	@Size(max = 102)
	@Column(name = "LNF13E_INTRO_NAME", columnDefinition = "VARCHAR(102)")
	private String lnf13e_intro_name;
	
	/** 
	 * 房仲證書(明)字號-年
	 */
	@Column(name="LNF13E_LICENSE_Y", columnDefinition="DECIMAL(3,0)")
	private BigDecimal lnf13e_license_y;
	
	/** 
	 * 房仲證書(明)字號-年登字
	 */
	@Size(max = 12)
	@Column(name = "LNF13E_LICENSE_W", length = 12, columnDefinition = "CHAR(12)")
	private String lnf13e_license_w;
	
	/** 
	 * 房仲證書(明)字號-編號
	 */
	@Size(max = 6)
	@Column(name = "LNF13E_LICENSE_NO", length = 6, columnDefinition = "CHAR(6)")
	private String lnf13e_license_no;
	

	public String getLnf13e_contract() {
		return lnf13e_contract;
	}

	public void setLnf13e_contract(String lnf13e_contract) {
		this.lnf13e_contract = lnf13e_contract;
	}

	public String getLnf13e_mega_empno() {
		return lnf13e_mega_empno;
	}

	public void setLnf13e_mega_empno(String lnf13e_mega_empno) {
		this.lnf13e_mega_empno = lnf13e_mega_empno;
	}

	public String getLnf13e_agnt_no() {
		return lnf13e_agnt_no;
	}

	public void setLnf13e_agnt_no(String lnf13e_agnt_no) {
		this.lnf13e_agnt_no = lnf13e_agnt_no;
	}

	public String getLnf13e_agnt_chain() {
		return lnf13e_agnt_chain;
	}

	public void setLnf13e_agnt_chain(String lnf13e_agnt_chain) {
		this.lnf13e_agnt_chain = lnf13e_agnt_chain;
	}

	public String getLnf13e_mega_code() {
		return lnf13e_mega_code;
	}

	public void setLnf13e_mega_code(String lnf13e_mega_code) {
		this.lnf13e_mega_code = lnf13e_mega_code;
	}

	public String getLnf13e_sub_unitno() {
		return lnf13e_sub_unitno;
	}

	public void setLnf13e_sub_unitno(String lnf13e_sub_unitno) {
		this.lnf13e_sub_unitno = lnf13e_sub_unitno;
	}

	public String getLnf13e_sub_empno() {
		return lnf13e_sub_empno;
	}

	public void setLnf13e_sub_empno(String lnf13e_sub_empno) {
		this.lnf13e_sub_empno = lnf13e_sub_empno;
	}

	public String getLnf13e_sub_empnm() {
		return lnf13e_sub_empnm;
	}

	public void setLnf13e_sub_empnm(String lnf13e_sub_empnm) {
		this.lnf13e_sub_empnm = lnf13e_sub_empnm;
	}

	public String getLnf13e_intro_src() {
		return lnf13e_intro_src;
	}

	public void setLnf13e_intro_src(String lnf13e_intro_src) {
		this.lnf13e_intro_src = lnf13e_intro_src;
	}

	public String getLnf13e_megaemp_brn() {
		return lnf13e_megaemp_brn;
	}

	public void setLnf13e_megaemp_brn(String lnf13e_megaemp_brn) {
		this.lnf13e_megaemp_brn = lnf13e_megaemp_brn;
	}

	public String getLnf13e_intro_name() {
		return lnf13e_intro_name;
	}

	public void setLnf13e_intro_name(String lnf13e_intro_name) {
		this.lnf13e_intro_name = lnf13e_intro_name;
	}

	public BigDecimal getLnf13e_license_y() {
		return lnf13e_license_y;
	}

	public void setLnf13e_license_y(BigDecimal lnf13e_license_y) {
		this.lnf13e_license_y = lnf13e_license_y;
	}

	public String getLnf13e_license_w() {
		return lnf13e_license_w;
	}

	public void setLnf13e_license_w(String lnf13e_license_w) {
		this.lnf13e_license_w = lnf13e_license_w;
	}

	public String getLnf13e_license_no() {
		return lnf13e_license_no;
	}

	public void setLnf13e_license_no(String lnf13e_license_no) {
		this.lnf13e_license_no = lnf13e_license_no;
	}
}
