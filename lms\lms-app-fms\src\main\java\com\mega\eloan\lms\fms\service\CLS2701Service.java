package com.mega.eloan.lms.fms.service;

import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C900M01I;
import com.mega.eloan.lms.model.C900S01I;
import com.mega.eloan.lms.model.L120M01A;


public interface CLS2701Service extends AbstractService{
	public C900M01I findC900M01I_oid(String oid);
	public C900M01I findC900M01I_mainId(String mainId);
	
	public C900S01I findC900S01I_oid(String oid);
	public C900S01I findC900S01I_mainId_caseMainId(String mainId, String caseMainId);
	public void saveC900S01I(List<C900S01I> list);
	public List<C900S01I> findC900S01I_mainId(String mainId);
	
	public List<L120M01A> findL120M01A_oid(String[] oid_arr);
}
