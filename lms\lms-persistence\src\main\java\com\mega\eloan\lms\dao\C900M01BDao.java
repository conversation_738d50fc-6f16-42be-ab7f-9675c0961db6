/* 
 * C900M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01B;

/** 產品種類對應表 **/
public interface C900M01BDao extends IGenericDao<C900M01B> {

	C900M01B findByOid(String oid);

	List<C900M01B> findByMainId(String mainId);
	
	List<C900M01B> getAll();

	C900M01B findByUniqueKey(String prodKind, String subjCode);

	List<C900M01B> findByIndex01(String prodKind, String subjCode);
}