<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<util:map id="obsdbSql" map-class="java.util.HashMap" key-type="java.lang.String">
 	<!-- ########## -->
    <!-- 授信共用 -->
    <!-- ########## -->

				 
		<!-- 授信消金狀態報送 核准時 新增-->
	    <entry key="ELF461.insert">
	        <value>
	        	INSERT INTO #SCHEMA#.ELF461(
					ELF461_CUSTID , ELF461_DUPNO , ELF461_CNTRNO, ELF461_BRNO, ELF461_STATUS,
					ELF461_APPRYY,ELF461_APPRMM, ELF461_CTYPE, ELF461_GUTCDATE, ELF461_PROJNO,
					ELF461_PROPERTY, ELF461_UPDATER,ELF461_TMESTAMP,ELF461_ISREVIVE_Y,ELF461_MAINID,
                    ELF461_ISOFCLCGA, ELF461_CGA_COUNTRY, ELF461_CGA_CRDTYPE, ELF461_CGA_CRDAREA,
                    ELF461_CGA_CRDPRED, ELF461_CGA_CRDGRAD, ELF461_CGA_RSKRTO, ELF461_CGA_GRADSCR,
					ELF461_ISSPEFIN,ELF461_SPEFINTYPE,ELF461_ISADC,ELF461_ISPROJOP,
					ELF461_HQPROJ_OPT1, ELF461_HQPROJ_OPT2, ELF461_HQPROJ_OPT3, 
					ELF461_HQPROJ_OPT4, ELF461_HQPROJ_OPT5, ELF461_HQPROJ_RES,
					ELF461_APPDATE, ELF461_CONTYPE, ELF461_APP_NO,
					ELF461_CURR, ELF461_CURAMT, ELF461_CURR_L, ELF461_CURAMT_L
					)
 				VALUES (?,?,?,?,?,
						?,?,?,?,?,
 						?,?,?,?,?,
                        ?,?,?,?,
                        ?,?,?,?,
						?,?,?,?,
						?,?,?,
						?,?,?,
						?,?,?,
						?,?,?,
						?)
	        </value>
	    </entry> 		 
				 
		<!-- 授信消金狀態報送 核准時 查詢-->
	    <entry key="ELF461.selByKey">
	        <value>
	        	SELECT * FROM #SCHEMA#.ELF461 WHERE ELF461_CUSTID =?  AND ELF461_DUPNO =? AND ELF461_CNTRNO =? ORDER BY  ELF461_TMESTAMP DESC
	        </value>
	    </entry> 
		
		<!-- 授信消金狀態報送 核准時 更新-->
	    <entry key="ELF461.update">
	        <value>
	        	UPDATE #SCHEMA#.ELF461 SET 
					ELF461_BRNO=?, ELF461_STATUS=?,
					ELF461_APPRYY=?,ELF461_APPRMM=?, ELF461_CTYPE=?,ELF461_UPDATER=?, ELF461_GUTCDATE=?, ELF461_PROJNO=?,
					ELF461_PROPERTY=?,ELF461_TMESTAMP=? ,ELF461_ISREVIVE_Y=?,ELF461_MAINID=?, ELF461_ISOFCLCGA=?,
                    ELF461_CGA_COUNTRY=?, ELF461_CGA_CRDTYPE=?, ELF461_CGA_CRDAREA=?, ELF461_CGA_CRDPRED=?,
                    ELF461_CGA_CRDGRAD=?, ELF461_CGA_RSKRTO=?, ELF461_CGA_GRADSCR=?,
					ELF461_ISSPEFIN=?,ELF461_SPEFINTYPE=?,ELF461_ISADC=?,ELF461_ISPROJOP=?,
					ELF461_HQPROJ_OPT1=?, ELF461_HQPROJ_OPT2=?, ELF461_HQPROJ_OPT3=?, 
					ELF461_HQPROJ_OPT4=?, ELF461_HQPROJ_OPT5=?, ELF461_HQPROJ_RES=?,
					ELF461_APPDATE=?, ELF461_CONTYPE=?, ELF461_APP_NO=?,
					ELF461_CURR=?, ELF461_CURAMT=?, ELF461_CURR_L=?, ELF461_CURAMT_L=?
					WHERE ELF461_CUSTID =?  AND ELF461_DUPNO =? AND ELF461_CNTRNO =?
	        </value>
	    </entry>
		
		
	 	<!--海外分行授信案件統計檔  新增-->
	    <entry key="ELF404.insert">
	        <value>
	           INSERT INTO #SCHEMA#.ELF404
			    (ELF404_BRNO, ELF404_APPRYY, ELF404_APPRMM, ELF404_CTYPE, ELF404_CASEDEPT,
				 ELF404_CASENO, ELF404_CNTRNO,
				 ELF404_CITEM1,ELF404_CITEM2,ELF404_CITEM3,ELF404_CITEM4,ELF404_CITEM5,ELF404_APPRAMT, ELF404_TMESTAMP)
			    VALUES 
				(
				  ?, ?, ?, ?, ?,
				  ?, ?, 
				  ?, ?, ?, ?, ?, ?, ?
				)
	        </value>
	    </entry> 
		
		<!-- 更新ELF461 MISELLSEEK 上傳用-->
	    <entry key="ELF461.updateOnlyRevive">
	        <value>
	         UPDATE #SCHEMA#.ELF461 SET 
			 ELF461_ISREVIVE_Y=?,ELF461_MAINID=?
			 WHERE ELF461_CUSTID =?  AND ELF461_DUPNO =? AND ELF461_CNTRNO =?
	        </value>
	    </entry>
	
	 
	 	<!--海外分行授信案件統計檔  更新-->
	    <entry key="ELF404.update">
	        <value>
	          UPDATE #SCHEMA#.ELF404 SET ELF404_BRNO=?,ELF404_APPRYY=?,ELF404_APPRMM=?,ELF404_CTYPE=?,ELF404_CASENO=?,
			  ELF404_CNTRNO=?,ELF404_CITEM1=?,ELF404_CITEM2=?,ELF404_CITEM3=?,ELF404_CITEM4=?,ELF404_CITEM5=?,ELF404_APPRAMT=?,
			  ELF404_UPDATER=?,ELF404_CASEDEPT=?, ELF404_TMESTAMP=?
			   WHERE ELF404_BRNO=? AND ELF404_APPRYY=? AND ELF404_APPRMM=? AND ELF404_CTYPE=? AND ELF404_CASEDEPT=? AND
			   ELF404_CASENO=? AND ELF404_CNTRNO=?
	        </value>
	    </entry>   
	 
	  	<!--海外分行授信案件統計檔  查詢-->
	    <entry key="ELF404.select">
	        <value>
	          SELECT * FROM #SCHEMA#.ELF404 
			  WHERE ELF404_BRNO=? AND ELF404_APPRYY=? AND ELF404_APPRMM=? AND ELF404_CTYPE=? AND ELF404_CASEDEPT=? AND
			   ELF404_CASENO=? AND ELF404_CNTRNO=?
	        </value>
	    </entry>
	 
	 	<!--海外分行授信案件統計檔  新增-->
	    <entry key="ELF404.insert2">
	        <value>
	           INSERT INTO #SCHEMA#.ELF404
			    (ELF404_BRNO,ELF404_APPRYY,ELF404_APPRMM,ELF404_CTYPE,ELF404_CASENO,ELF404_CNTRNO,ELF404_CITEM6,
				ELF404_CITEM7,ELF404_CITEM8,ELF404_CITEM9,ELF404_CITEM10,ELF404_APPRAMT,ELF404_UPDATER,ELF404_CASEDEPT,
				ELF404_TMESTAMP)
			    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	        </value>
	    </entry>
	 
	 	<!--海外分行授信案件統計檔  更新-->
	    <entry key="ELF404.update2">
	        <value>
	          UPDATE #SCHEMA#.ELF404 SET ELF404_BRNO=?,ELF404_APPRYY=?,ELF404_APPRMM=?,ELF404_CTYPE=?,ELF404_CASENO=?,
			  ELF404_CNTRNO=?,ELF404_CITEM6=?,ELF404_CITEM7=?,ELF404_CITEM8=?,ELF404_CITEM9=?,ELF404_CITEM10=?,ELF404_APPRAMT=?,
			  ELF404_UPDATER=?,ELF404_CASEDEPT=?, ELF404_TMESTAMP=?
			   WHERE ELF404_BRNO=? AND ELF404_APPRYY=? AND ELF404_APPRMM=? AND ELF404_CTYPE=? AND ELF404_CASENO=?
			   AND ELF404_CNTRNO=?
	        </value>
	    </entry>
		
		<!--海外分行授信案件統計檔  查詢-->
	    <entry key="ELF404.select2">
	        <value>
	          SELECT * FROM #SCHEMA#.ELF404 
			  WHERE ELF404_BRNO=? AND ELF404_APPRYY=? AND ELF404_APPRMM=? AND ELF404_CTYPE=? AND ELF404_CASENO=?
			   AND ELF404_CNTRNO=?
	        </value>
	    </entry>     

		<!--個金資料檔  刪除-->
	    <entry key="ELF386.delete">
	        <value>
	            DELETE FROM #SCHEMA#.ELF386
	            WHERE ELF386_CUSTID=? AND ELF386_DUPNO=? AND ELF386_CNTRNO=?
	        </value>
	    </entry>
	    
		<!--個金資料檔  新增-->
	    <entry key="ELF386.insert">
	        <value>
	            INSERT INTO #SCHEMA#.ELF386
	            (ELF386_CUSTID,ELF386_DUPNO,ELF386_CNTRNO,ELF386_STAFFNO,ELF386_CUSBAN,ELF386_CUSNM,ELF386_SDATE,
	            ELF386_ISMATES,ELF386_DEGREECD,ELF386_POS,ELF386_SENIORITY,ELF386_HINCOME,ELF386_DRATE,ELF386_ODEP,
	            ELF386_OBUSINESS,ELF386_CMSSTATUS,ELF386_CREDIT,ELF386_YRATE,ELF386_ISPFUND,ELF386_INVMBAL,
	            ELF386_INVOBAL,ELF386_LOANPCT,ELF386_ISBINS,ELF386_YPAY,ELF386_OMONEY,ELF386_UPDATER,ELF386_TMESTAMP)
	            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	        </value>
	    </entry>

		<!-- 由CES貼過來 -->
		<entry key="ELF001.queryAvgAmtTById">
			<value><![CDATA[SELECT ELF001_MEGA_ID AS CUSTID, CAST((ELF001_GL*10000)+ELF001_SUB AS CHAR(8)) AS APCD,
				ELF001_CURR, (ELF001_M_AVG_AMT_T/1000) AS TWDBAL
				FROM #SCHEMA#.ELF001 T1
				WHERE ELF001_BR_CODE = ?
				AND ELF001_MEGA_ID = ?
				AND ELF001_YYMM = ( SELECT MAX(ELF001_YYMM) FROM #SCHEMA#.ELF001 T2 WHERE T1.ELF001_MEGA_ID = T2.ELF001_MEGA_ID)
				AND CAST((ELF001_GL*10000)+ELF001_SUB AS CHAR(8))
				IN ( '20000000', '20400000', '20430000', '20800000', '20810100', '20810200', '21200000', 
				'21200000', '21220000', '21240101', '21240102', '21240201', '21240202', '21260000', '21600000', 
				'21620000', '22000000', '22010000', '22020000', '22040000', '22050000', '22090000', '22160000', 
				'22250000', '22420300', '27770000' ) ORDER BY ELF001_M_AVG_AMT_T DESC
			]]></value>
		</entry>
		
		<!--貢獻度  查詢(海外存款)-->
		<entry key="ELF003.queryProfitContributeById">
			<value><![CDATA[SELECT ELF003_ID_NO, ELF003_LOC_CURR, ELF003_AMT FROM #SCHEMA#.ELF003 WHERE ELF003_ID_NO = ?]]></value>
		</entry>

        <!--貢獻度  查詢(海外存款)-->
		<entry key="ELF003.queryProfitContributeByIdDate">
			<value><![CDATA[SELECT ELF003_YYMM,ELF003_ID_NO, ELF003_LOC_CURR, ELF003_AMT FROM #SCHEMA#.ELF003 WHERE ELF003_ID_NO = ? AND ELF003_YYMM BETWEEN ? AND ?  ORDER BY ELF003_YYMM ASC]]></value>
		</entry>
    
    <!-- ########## -->
    <!-- 徵信相關(from CES.xxxx) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 擔保品相關(from CMS.xxxx) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(簽報書) -->
    <!-- 個金授信(簽報書) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 沿用徵信交易 -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(額度明細表) -->
    <!-- 個金授信(額度明細表) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(動審表) -->
    <!-- 個金授信(動審表) -->
	<!-- ########## -->
		<!-- ELF164 授信消金利率檔 Start -->
	
			<entry key="ELF164.insert">
				<value>
					INSERT INTO #SCHEMA#.ELF164(
					       ELF164_BR_NO,ELF164_CONTRACT,ELF164_CUST_ID,ELF164_KIND,ELF164_SWFT,
						   ELF164_LNAP_CODE,ELF164_INTRT_TYPE,ELF164_INT_KIND,ELF164_INT_BASE,ELF164_INT_SPREAD,
						   ELF164_INT_TYPE,ELF164_INTCHG_TYPE,ELF164_INT_MEMO)
					
					VALUES(?,?,?,?,?,
						   ?,?,?,?,?,
						   ?,?,?)
					
					</value>
			</entry>
	
			<entry key="ELF164.findByBR_NO">
				<value>SELECT * FROM #SCHEMA#.ELF164 WHERE ELF164_BR_NO=? </value>
			</entry>
			
			<!-- ELF164 授信消金利率檔  END-->
			<!-- ELF383 授信額度檔 Start-->
			
			<!--G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記-->
			<!--J-111-0208_05097_B1001 Web e-Loan國內外企金授信簽報，各額度LGD隨核定內容上送ALOAN、AS400(ELF383) -->
			<entry key="ELF383.insert">
				<value>
				INSERT INTO #SCHEMA#.ELF383 (
					ELF383_CUSTID, ELF383_DUPNO, ELF383_CNTRNO, ELF383_SDATE, ELF383_CASETYPE,
					ELF383_LNFLAG, ELF383_OLDAMT, ELF383_CURAMT,ELF383_OLDCURR,
					ELF383_CURCURR, ELF383_LNQTFLAG, ELF383_RECLFLAG, ELF383_SGUFLAG, ELF383_LRPTYPE,
					
					ELF383_LLNNO, ELF383_LLNFDATE, ELF383_LLNEDATE, ELF383_LLNMON, ELF383_LNUSENO,
					ELF383_USEFMDT, ELF383_USEENDT, ELF383_USEFTMN, ELF383_MEMO, ELF383_GRANTNO,
					ELF383_COMMBORW, ELF383_UPDATER, ELF383_TMESTAMP,
					
					ELF383_RECLCHG, ELF383_SGUCHG, ELF383_GUTFLAG, ELF383_GUTPER, ELF383_LLNEFLAG,
					ELF383_USEEFLAG, ELF383_LNNOFLAG, ELF383_UNICHGFLAG, ELF383_REFLAG, ELF383_UNIONAMT,
					ELF383_SHAREAMT, ELF383_PERMITTYPE, ELF383_HIDEUNION, ELF383_SETDATE, ELF383_UNIONAREA,
					
					ELF383_UNIONROLE, ELF383_RISKAREA, ELF383_EXISTDATE, ELF383_FEEDATE, ELF383_COUNTRYDT,
					ELF383_CRDTTBL, ELF383_MOWTYPE, ELF383_MOWTBL1, ELF383_SYNDIPFD, ELF383_COKIND,
					ELF383_CNTRNOM, ELF383_RCLNO, ELF383_DOCUMENTNO, ELF383_CRDTYMD, ELF383_CRDTBR,
					
					ELF383_MOWYMD, ELF383_MOWBR, ELF383_MODYDATE, ELF383_MOODYGRD, ELF383_SPDATE,
					ELF383_SPGRD, ELF383_FITCHDATE, ELF383_FITCHGRD, ELF383_CONTROLCD, ELF383_DURINGFLAG,
					ELF383_LTVRATE, ELF383_LOCATIONCD, ELF383_JCICMARK, ELF383_RISKFACTORS, ELF383_RISKFACTAMT,
					ELF383_UNSECUREFLAG,ELF383_EXCEPT,
					ELF383_ISHEDGE,ELF383_ENHANCEAMT,ELF383_NETSWFT,ELF383_NETAMT,ELF383_NETAMTUNIT,
					ELF383_ISREVIVE,ELF383_REVTARGET,ELF383_REVSUBITEM,ELF383_REVPURPOSE,
					ELF383_ESGGFG,ELF383_ESGGTYPE,ELF383_ESGSFG,ELF383_ESGSTYPE,ELF383_ESGSUNRE,
                    ELF383_ESGGTYPE_1,ELF383_ESGGTYPE_2,ELF383_ESGGTYPE_3,ELF383_CNTRLGD,ELF383_STDAUTH,
					ELF383_EXPERF_FG,ELF383_FLAW_FG,ELF383_FLAW_AMT,
					ELF383_SOCIALFLAG, ELF383_SOCIALKIND, ELF383_SOCIALTA, ELF383_SOCIALRESP
					 ) VALUES (
				
				?, ?, ?, ?, ?,
				?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				
				?, ?, ?, 
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?, ?, ?,
				?, ?, ?,
				?, ?, ?, ?)
					
				 </value>
			</entry>
			<entry key="ELF383.delByKey">
				<value>
				DELETE FROM #SCHEMA#.ELF383 WHERE ELF383_CUSTID=? AND ELF383_DUPNO=?  AND ELF383_CNTRNO  = ? AND ELF383_SDATE = ?
					
				 </value>
			</entry>
			<!-- ELF383 授信額度檔 Start-->
			
			<!-- ELF384 科(子)目及其限額檔 Start-->
			<entry key="ELF384.insert">
	        <value>
				INSERT INTO #SCHEMA#.ELF384(
					ELF384_CUSTID, ELF384_DUPNO, ELF384_CNTRNO, ELF384_SDATE, ELF384_LOANTP,
					ELF384_CHGFLAG, ELF384_OLDCURR, ELF384_OLDQUOTA, ELF384_NEWCURR, ELF384_NEWQUOTA,
					ELF384_LNGU, ELF384_UPDATER, ELF384_TMESTAMP) 
					VALUES (?, ?, ?, ?, ?,
							?, ?, ?, ?, ?,
							?, ?, ?)
	        </value>
	    </entry>
		
		
		<entry key="ELF384.update">
	        <value>
				UPDATE #SCHEMA#.ELF384 SET
					ELF384_CHGFLAG=?, ELF384_OLDCURR=?, ELF384_OLDQUOTA=?, ELF384_NEWCURR=?, ELF384_NEWQUOTA=?,
				    ELF384_LNGU=?, ELF384_UPDATER=?, ELF384_TMESTAMP=? 
			   WHERE ELF384_CUSTID = ? AND ELF384_DUPNO =? AND ELF384_CNTRNO  = ?  AND ELF384_SDATE = ? AND ELF384_LOANTP = ?
	        </value>
	    </entry>
		
		<entry key="ELF384.selByUniqueKey">
	        <value>
				 SELECT ELF384_CUSTID FROM #SCHEMA#.ELF384 WHERE
				  ELF384_CUSTID = ? AND ELF384_DUPNO =? AND ELF384_CNTRNO  = ?  AND ELF384_SDATE = ? AND ELF384_LOANTP in (?)
	        </value>
	    </entry>
		
		
		
		
		
		<!--動審表上傳用 刪除 ELF384 依系統時間  gfnQUOTSUBProcess-->
    <entry key="ELF384.delBySdate">
        <value>
         DELETE FROM  #SCHEMA#.ELF384 WHERE  ELF384_CUSTID=?  AND ELF384_DUPNO = ?  AND ELF384_CNTRNO=? AND ELF384_SDATE = ?
        </value>
    </entry>
	
	
	<!--動審表上傳用 更新舊資料為取消  ELF384  gfnQUOTSUBProcess-->
    <entry key="ELF384.updateChgflagBySdate">
        <value>
        UPDATE  #SCHEMA#.ELF384 
			SET ELF384_CHGFLAG='2' ,ELF384_TMESTAMP= ? , ELF384_ONLNTIME=0, ELF384_BTHTIME=0
		 WHERE ELF384_CUSTID = ? AND ELF384_DUPNO = ?  AND ELF384_CNTRNO = ?  AND ELF384_SDATE =? 
        </value>
    </entry>
	
	
	<!--動審表上傳用  將舊資料複製到新資料 且將變更註記變為取消, a-Loan Batch Time 改成Null  gfnQUOTSUBProcess-->
    <entry key="ELF384.insetSelChgflagBySdate">
        <value>
		     INSERT INTO #SCHEMA#.ELF384 
			 	    (ELF384_CUSTID,ELF384_DUPNO,ELF384_CNTRNO,ELF384_SDATE,ELF384_LOANTP,
					ELF384_CHGFLAG,ELF384_OLDCURR,ELF384_OLDQUOTA,ELF384_NEWCURR,ELF384_NEWQUOTA,ELF384_LNGU,ELF384_UPDATER,ELF384_TMESTAMP) 
			     SELECT 
				 		ELF384_CUSTID,ELF384_DUPNO,ELF384_CNTRNO,{0},ELF384_LOANTP,''2'',ELF384_OLDCURR,ELF384_OLDQUOTA,
						ELF384_NEWCURR,ELF384_NEWQUOTA,ELF384_LNGU,{1}, {2}
				 FROM #SCHEMA#.ELF384 
			     	WHERE  
				 ELF384_CUSTID= ?  AND ELF384_DUPNO = ?  AND ELF384_CNTRNO = ?  AND ELF384_SDATE = ?
        </value>
    </entry>
		
			<!-- ELF384 科(子)目及其限額檔 END-->
			
			<!-- ELF385 聯貸案參貸比率檔 Start-->
			
			<!--新增-->
			    <entry key="ELF385.insert">
			        <value>
				INSERT INTO #SCHEMA#.ELF385 (
					ELF385_UNLNTYPE,ELF385_CNTRNO, ELF385_UNITNO,  ELF385_MAINBH, ELF385_LNAMT,
					ELF385_UPDATER, ELF385_TMESTAMP)
			
					VALUES (?, ?, ?, ?, ?,
						  ?, ?)
			        </value>
			    </entry>
				
				<!--刪除-->
				  <entry key="ELF385.delByCntrno">
			        <value>
				 DELETE FROM #SCHEMA#.ELF385 where ELF385_CNTRNO =  ? AND　ELF385_UNLNTYPE=?
			        </value>
			    </entry>
				<!--刪除-->
				  <entry key="ELF385.delByCntrnoOnly">
			        <value>
				 		DELETE FROM #SCHEMA#.ELF385 where ELF385_CNTRNO =  ? 
			        </value>
			    </entry>
			<!-- ELF385 聯貸案參貸比率檔 END-->
			
			<!-- ELF388 核准額度資料檔 Start-->
		<entry key="ELF388.insert">
	        <value>
				<![CDATA[INSERT INTO #SCHEMA#.ELF388(
					ELF388_QUOTANO, ELF388_CUSTID, ELF388_DUPNO, ELF388_ICBCNO, ELF388_CNAME, 
					ELF388_OMGRNAME, ELF388_FMGRNAME, ELF388_APPROLVL, ELF388_UPDATER, ELF388_TMESTAMP) 
						VALUES (?, ?, ?,?, ?,
								?, ?, ?,?, ?)
				]]>
	        </value>
	    </entry>
		
		<entry key="ELF388.update">
	        <value>
		 
		 UPDATE #SCHEMA#.ELF388  SET  
		 	ELF388_CUSTID = ? , ELF388_DUPNO=?, ELF388_ICBCNO=? , ELF388_CNAME=?, 
		 	ELF388_FMGRNAME=?, ELF388_APPROLVL=?, ELF388_UPDATER=?,ELF388_TMESTAMP= ?
			WHERE ELF388_QUOTANO = ?
	        </value>
	    </entry>
		
		<entry key="ELF388.selByQuotano">
	        <value>
					SELECT * FROM #SCHEMA#.ELF388 WHERE ELF388_QUOTANO=? 
	        </value>
	    </entry>
			<!-- ELF388 核准額度資料檔 END-->
			
			
			<!-- ELF422 額度資訊檔 Start-->
				<entry key="ELF422.insert">
	        <value>
		INSERT INTO #SCHEMA#.ELF422(
			ELF422_CUSTID, ELF422_DUPNO, ELF422_BRANCH, ELF422_CNTRNO, ELF422_APPDATE,
			ELF422_APRDATE, ELF422_CUSTNAME, ELF422_BOSSID, ELF422_BOSSNAME, ELF422_UNID, 
			ELF422_NEWCASE, ELF422_UPDATER, ELF422_TMESTAMP) 
			 VALUES (
			  ?, ?, ?, ?, ?,
			  ?, ?, ?, ?, ?, 
			  ?, ?, ?)
	        </value>
	    </entry>
		
		
		<entry key="ELF422.delByKey">
	        <value>
			 DELETE FROM #SCHEMA#.ELF422 WHERE  ELF422_CUSTID = ? AND   ELF422_DUPNO=? AND ELF422_BRANCH=?  AND   ELF422_CNTRNO=?
	        </value>
	    </entry>
			<!-- ELF422 額度資訊檔 END-->
			
			<!-- ELF476 企金簽案費率檔 Start-->
		<entry key="ELF476.insert">
	        <value>
		INSERT INTO #SCHEMA#.ELF476(
			ELF476_CNTRNO, ELF476_SUBJECT, ELF476_SDATE, ELF476_RTYPE, ELF476_CP_TYPE,
			ELF476_CP1_RATE, ELF476_CP1_FEE, ELF476_CP2_RATE1, ELF476_CP2_RATE2, ELF476_CP_DES,
			ELF476_CF_TYPE, ELF476_CF1_RATE, ELF476_CF1_MTH1, ELF476_CF1_MD, ELF476_CF1_MTH2, 
			
			ELF476_CF2_RATE, ELF476_CF2_MD, ELF476_CF2_MTH, ELF476_CF_DES, ELF476_CPY_TYPE,
			ELF476_CPY1_RATE, ELF476_CPY1_MTH1, ELF476_CPY1_MD, ELF476_CPY1_MTH2, ELF476_CPY2_RATE,
			ELF476_CPY2_MD, ELF476_CPY2_MTH, ELF476_CPY_DES, ELF476_PA_TYPE, ELF476_PA1_RATE,
			
			ELF476_PA1_MD, ELF476_PA1_MTH, ELF476_PA2_RATE, ELF476_PA2_MD, ELF476_PA2_MTH,
			ELF476_PA_DES, ELF476_UPDATER, ELF476_TMESTAMP) 
			VALUES (
			
			?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			
			?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			
			?, ?, ?, ?, ?,
			?, ?, ?)
	        </value>
	    </entry>
		
		
		<entry key="ELF476.delByUniqueKey">
	        <value>
				 DELETE FROM #SCHEMA#.ELF476 WHERE ELF476_CNTRNO = ? AND ELF476_SUBJECT = ?  AND ELF476_SDATE =?  AND ELF476_RTYPE =? 
	        </value>
	    </entry>
			<!-- ELF476 企金簽案費率檔 END-->  
    
    <!-- ########## -->
    <!-- 企金授信(其他) -->
    <!-- 個金授信(其他) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金覆審(覆審名單) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金覆審(覆審報告表) -->
    <!-- ########## -->
    
		<!-- ELF346 join ELF345 取得擔保品資料-->  
		<entry key="ELF345AAND346.listAMTData">
	        <value>
				SELECT DISTINCT T1.TYPCD,T1.BRANCH,T1.CUSTID,T1.DUPNO, 
				T1.CNTRNO,T1.COLLNO,T2.CURR, 
				T2.APPAMT,T2.LOANAMT,T2.LOANRT,T2.RGSTCUR,T2.RGSTAMT, 
				T2.APPUSD,T2.LOANUSD,T2.RGSTUDS 
				FROM  
				(  
				  SELECT * FROM #SCHEMA#.ELF346
				  WHERE BRANCH = ?
	              AND CNTRNO = ?
				) AS T1 
				LEFT OUTER JOIN  
				#SCHEMA#.ELF345 AS T2
				ON  
				(T1.COLLNO=T2.COLLNO AND 
				T1.CUSTID=T2.CUSTID And T1.DUPNO=T2.DUPNO  
				AND T1.BRANCH=T2.BRANCH) 
				ORDER BY T1.COLLNO ASC 
	        </value>
	    </entry>
		  
		<entry key="ELF348AAND349.listCOLData">
	        <value>
	        <![CDATA[
				SELECT DISTINCT T1.SITE1,T1.SITE2,T1.SITE3,T1.SITE4,T1.LNNO1,T1.LNNO2,T1.IMMNO,T2.BN1,T2.BN2,T1.LADDR  FROM 
				(SELECT  * FROM #SCHEMA#.ELF348 WHERE CUSTID = ? AND DUPNO = ? AND COLLNO  = ? AND BRANCH = ?) AS T1 
				LEFT OUTER JOIN 
				(SELECT  * FROM #SCHEMA#.ELF349 WHERE CUSTID =  ? AND DUPNO = ? AND COLLNO  = ? AND BRANCH = ? AND PRMAFF NOT IN ('03','07','10','13','14')   ) AS T2 
				ON  
				T1.BN1 = T2.BN1 AND  
				T1.BN2 = T2.BN2 
				]]>
	        </value>
	    </entry>
		
		<entry key="ELF349.listCOLData">
	        <value>
	        	<![CDATA[
				SELECT DISTINCT SITE1,SITE2,SITE3,SITE4,SITE5,SITE6,SITE7,SITE8,SITE9,SITE10,SITE11,SITE12 
				FROM #SCHEMA#.ELF349
				WHERE CUSTID = ? AND DUPNO = ? AND COLLNO  = ? AND BRANCH = ? 
				AND PRMAFF NOT IN ('03','07','10','13','14')
				]]>
	        </value>
	    </entry>
		
		<entry key="ELF.listCMSData">
	        <value>
	        	<![CDATA[
				SELECT {0} FROM #SCHEMA#.{1} WHERE {2} {3}	
				]]>
	        </value>
	    </entry>
		
		<entry key="ELF.listELF347Data">
	        <value>
	        	<![CDATA[
				select T0.INSKND1,T0.INSKND2,T0.INSKND3 AS INSKND,T0.INSNM,T0.INSCUR,T0.INSAMT,T0.INSEDT
				from #SCHEMA#.ELF347 T0
				WHERE T0.TYPCD = ?
				AND T0.BRANCH = ?
				AND T0.CUSTID= ?
				AND T0.DUPNO = ?
				AND T0.COLLNO = ?
				]]>
	        </value>
	    </entry>
			
    
    <!-- ########## -->
    <!-- 個金覆審(覆審名單) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 個金覆審(覆審報告表) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 稽核工作底稿 -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 管理報表 -->
    <!-- ########## -->
		
		<entry key="ELF506.findByCntrNo">
	        <value>
	        	SELECT * FROM #SCHEMA#.ELF506 WHERE ELF506_CNTRNO = ?
	        </value>
	    </entry>
        <entry key="ELF506.findByCustId">
            <value>
                SELECT * FROM #SCHEMA#.ELF506 WHERE ELF506_CUST_ID = ?
            </value>
        </entry>
		<entry key="ELF506.findByAdcCaseNo">
			<value>
				SELECT * FROM #SCHEMA#.ELF506 WHERE ELF506_ADC_CASENO = ?
			</value>
		</entry>
		<entry key="ELF506.deleteByCntrNo">
	        <value>
	        	DELETE FROM #SCHEMA#.ELF506 WHERE ELF506_CNTRNO = ?
	        </value>
	    </entry>
		<!-- J-113-0059 額度明細表約定融資註記欄位，配合於簽報書核准時寫入ELF506，回補舊案資料 -->
		<entry key="MIS.ELF506.updateExcpetByCntrNo">
        	<value>
				UPDATE #SCHEMA#.ELF506 SET ELF506_EXCEPT=?, ELF506_EX_QA_Y=?,ELF506_EX_QA_PLUS=?  WHERE ELF506_CNTRNO =?
        	</value>
    	</entry>
		<entry key="ELF506.insert">
	        <value>
	        	INSERT INTO #SCHEMA#.ELF506
				(ELF506_CNTRNO,ELF506_CN_LOAN_FG,ELF506_DIRECT_FG,ELF506_S_TRADE_FG,
				ELF506_GUAR1_RATE,ELF506_GUAR2_RATE,ELF506_GUAR3_RATE,
				ELF506_COLL1_RATE,ELF506_COLL2_RATE,ELF506_COLL3_RATE,ELF506_COLL4_RATE,ELF506_COLL5_RATE,
				ELF506_MODIFYTIME,ELF506_CREATETIME,ELF506_CREATEUNIT,ELF506_MODIFYUNIT,ELF506_DOCUMENT_NO,
				ELF506_IGOL_FLAG,ELF506_CN_TMU_FG,ELF506_CN_BUS_KIND,ELF506_CUST_ID,ELF506_722_FLAG,
			    ELF506_722_MODUNIT,ELF506_722_DOC_NO,ELF506_722_MODTIME,ELF506_722_SDATE,ELF506_722_IS_BUY,ELF506_722_EX_ITEM,
			    ELF506_LOAN_TARGET,ELF506_IS_TYPE,ELF506_GRNT_TYPE,ELF506_GRNT_CLASS,ELF506_OTHCRD_TYPE,ELF506_UNION_AREA3,ELF506_NCN_SBLC_FG,
				ELF506_NCN_INSTALMENT,ELF506_PROD_KIND,ELF506_ADC_CASENO, ELF506_EXCEPT, ELF506_EX_QA_Y, ELF506_EX_QA_PLUS)
				VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
	        </value>
	    </entry>
		
		<!-- J-104-0XXX-001  Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別-->
	    <entry key="ELF506.updateLoanTarget">
	        <value>
	        	UPDATE #SCHEMA#.ELF506 SET ELF506_LOAN_TARGET=? WHERE ELF506_CNTRNO =?
	        </value>
	    </entry>
		
		<!-- G-104-0333-001 配合萬磅分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權。-->
	    <entry key="ELF506.updateCntrNoByCntrNo">
	        <value>
	        	UPDATE #SCHEMA#.ELF506 SET ELF506_CNTRNO=?,ELF506_MODIFYUNIT=? WHERE ELF506_CNTRNO =?
	        </value>
	    </entry>
		
		
		<!-- G-104-0097-001 泰子行淨值讀取方式。-->
		<!--J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循-->
		<!--
		    ELF006_BRN_TYPE:M=台灣總行 H=子行 S=分行
			ELF006_LIMIT_TYPE:01=淨值 02=資本額
		-->
	    <entry key="ELF006.selectElf006NetValue">
	        <value>
	        	SELECT * FROM #SCHEMA#.ELF006 WHERE ELF006_BRN_TYPE IN ('H','S') AND ELF006_LIMIT_TYPE = '01'  ORDER BY ELF006_BRN_TYPE ASC
	        </value>
	    </entry>
		
		<!--J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循-->
		<!--
		    ELF006_BRN_TYPE:M=台灣總行 H=子行 S=分行
			ELF006_LIMIT_TYPE:01=淨值 02=資本額
		-->
		<entry key="ELF006.selectElf006ByBrnTypeLimitType">
	        <value>
	        	SELECT * FROM #SCHEMA#.ELF006 WHERE ELF006_BRN_TYPE = ? AND ELF006_LIMIT_TYPE = ?  ORDER BY ELF006_BRN_TYPE ASC
	        </value>
	    </entry>
		
		<!-- J-105-0202-001 Web e-Loan企金授信修改客戶統編。-->
	    <entry key="J1050202.update_ELF461_01">
	        <value>
	        	UPDATE #SCHEMA#.ELF461 SET 
					 ELF461_CUSTID =?,ELF461_DUPNO =?
					WHERE ELF461_CUSTID =?  AND ELF461_DUPNO =? AND ELF461_CNTRNO =?
	        </value>
	    </entry>
		
		<!-- J-105-0202-001 Web e-Loan企金授信修改客戶統編。-->
	    <entry key="J1050202.update_ELF506_01">
	        <value>
	        	UPDATE #SCHEMA#.ELF506 SET 
					ELF506_CUST_ID =?
					WHERE ELF506_CNTRNO =?
	        </value>
	    </entry>
		
		 
		<entry key="J1050202.update_ELF383_01">
	        <value>
	        	UPDATE #SCHEMA#.ELF383 SET 
					 ELF383_CUSTID =?,ELF383_DUPNO =?
					WHERE ELF383_CUSTID =?  AND ELF383_DUPNO =? AND ELF383_CNTRNO =?
	        </value>
	    </entry>
		
		<entry key="J1050202.update_ELF384_01">
	        <value>
	        	UPDATE #SCHEMA#.ELF384 SET 
					 ELF384_CUSTID =?,ELF384_DUPNO =?
					WHERE ELF384_CUSTID =?  AND ELF384_DUPNO =? AND ELF384_CNTRNO =?
	        </value>
	    </entry>
		
		<entry key="J1050202.update_ELF388_01">
	        <value>
	        	UPDATE #SCHEMA#.ELF388 SET 
					 ELF388_CUSTID =?,ELF388_DUPNO =?
					WHERE ELF388_CUSTID =?  AND ELF388_DUPNO =? AND ELF388_QUOTANO =?
	        </value>
	    </entry>
		
		<entry key="J1050202.update_ELF164_01">
	        <value>
	        	UPDATE #SCHEMA#.ELF164 SET 
					ELF164_CUST_ID =?
					WHERE ELF164_CUST_ID = ? AND ELF164_CONTRACT = ?
	        </value>
	    </entry>

	    <entry key="J1050202.update_ELF422_01">
	        <value>
	        	UPDATE #SCHEMA#.ELF422 SET 
					 ELF422_CUSTID =?,ELF422_DUPNO =?
					WHERE ELF422_CUSTID =?  AND ELF422_DUPNO =? AND ELF422_CNTRNO =?
	        </value>
	    </entry>
	    
	    <entry key="ELF515.insert">
	        <value>
			INSERT INTO #SCHEMA#.ELF515
			(ELF515_CNTRNO, ELF515_TYPE, ELF515_INSTALMENT, ELF515_CNTRNO_M, ELF515_INTER_OUTER, ELF515_SITE1, ELF515_SITE2, ELF515_SITE3, ELF515_SITE4, ELF515_ADDRESS, ELF515_BUILD_STATE, ELF515_BUILD_DATE, ELF515_SUB_TYPE1, ELF515_SUB_TYPE2, ELF515_SUB_TYPE3, ELF515_SUB_TYPE4, ELF515_SUB_TYPE5, ELF515_SUB_TYPE6, ELF515_SUB_TYPE7, ELF515_DATA1, ELF515_DATA2, ELF515_DATA3, ELF515_DATA4, ELF515_DATA5, ELF515_EMPL_NO, ELF515_SUPV_NO, ELF515_CREATEUNIT, ELF515_MODIFYUNIT, ELF515_CREATETIME, ELF515_MODIFYTIME)
			VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)

	        </value>
	    </entry>
	    <entry key="ELF515.delete">
	        <value>
				DELETE from #SCHEMA#.ELF515 where ELF515_CNTRNO = ? 
	        </value>
	    </entry>
		
		
		<!--G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權-->
		<entry key="ELF515.findByCntrNoForForBt">
	        <value>
	        	SELECT * FROM #SCHEMA#.ELF515 WHERE ELF515_CNTRNO = ? OR ELF515_CNTRNO_M = ?
	        </value>
	    </entry>
		
		
		<!--G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權-->
	    <entry key="ELF515.updateCntrNoByCntrNoForBt">
	        <value>
	        	UPDATE #SCHEMA#.ELF515 SET ELF515_CNTRNO = (CASE WHEN ELF515_CNTRNO = ? THEN ? ELSE ELF515_CNTRNO END ) ,ELF515_CNTRNO_M = (CASE WHEN ELF515_CNTRNO_M = ? THEN ? ELSE ELF515_CNTRNO_M END ) , ELF515_MODIFYUNIT=? WHERE ELF515_CNTRNO =? OR ELF515_CNTRNO_M = ?
	        </value>
	    </entry>

        <entry key="ELF515.batch001">
            <value>
				INSERT INTO #SCHEMA#.ELF515
				(ELF515_CNTRNO, ELF515_TYPE, ELF515_INSTALMENT, ELF515_CNTRNO_M, ELF515_INTER_OUTER, ELF515_SITE1, ELF515_SITE2, ELF515_SITE3, ELF515_SITE4,
				ELF515_ADDRESS, ELF515_BUILD_STATE, ELF515_SUB_TYPE1, ELF515_SUB_TYPE2, ELF515_SUB_TYPE3, ELF515_SUB_TYPE4, ELF515_SUB_TYPE5,
				ELF515_SUB_TYPE6, ELF515_SUB_TYPE7, ELF515_DATA1, ELF515_DATA2, ELF515_DATA3, ELF515_DATA4, ELF515_DATA5, ELF515_EMPL_NO, ELF515_SUPV_NO,
				ELF515_CREATEUNIT, ELF515_MODIFYUNIT, ELF515_CREATETIME, ELF515_MODIFYTIME,ELF515_BUILD_DATE)
				select
				ELF506_CNTRNO, ELF506_722_EX_ITEM,'N','','','','','','',
				'','','','','','','',
				'','','','','','','','SYSTEM','SYSTEM',
				'900','900',BIGINT(left(REPLACE(REPLACE(char(current TIMESTAMP),'-',''),'.',''),17)) ,BIGINT(left(REPLACE(REPLACE(char(current TIMESTAMP),'-',''),'.',''),17)),0
				from #SCHEMA#.elf506
				where ELF506_722_EX_ITEM != ''
            </value>
        </entry>
		<entry key="ELF515.deleteAll">
			<value>
				Delete from #SCHEMA#.elf515
			</value>
		</entry>
		
		
		
		
		<!-- 更新往來異常戶彙總檔(結案) -->
	    <entry key="LNFE0851.updateUnNormal4">
	        <value>
					UPDATE REMOTE.LNFE0851 SET LNFE0851_CLOSEFG=?,
					LNFE0851_CLOSEDT=?,LNFE0851_CLOSEPNO=?,LNFE0851_CLOSEUID=?
					WHERE 
					LNFE0851_CUST_ID=? AND LNFE0851_CUST_DUP=? {0} AND LNFE0851_CLOSEFG != ''Y''
	        </value>
	    </entry>
	
		<!-- 更新往來異常戶彙總檔結案註記 -->
	    <entry key="LNFE0851.uploadUnNormal5">
	        <value>
				UPDATE REMOTE.LNFE0851 SET LNFE0851_CLOSEFG=?, 
				LNFE0851_CLOSEDT=?, LNFE0851_CLOSEPNO=?, LNFE0851_CLOSEUID=?
				WHERE LNFE0851_CUST_ID=? AND LNFE0851_CUST_DUP=? AND LNFE0851_MDBRNO=?
				AND LNFE0851_CLOSEUID=?
	        </value>
	    </entry>
	
		<!-- 查詢往來異常戶彙總檔 -->
	    <entry key="LNFE0851.selUnNormal4">
	        <value>
	        	<![CDATA[
					SELECT * FROM REMOTE.LNFE0851
					WHERE LNFE0851_CUST_ID=? AND LNFE0851_CUST_DUP=? AND LNFE0851_MDBRNO=? AND LNFE0851_CLOSEFG <> 'Y'
				]]>
	        </value>
	    </entry>
	
		<!-- 上傳往來異常戶彙總檔 -->
		<!--LNFE0851_AGRAGREECONT, LNFE0851_HORNOR與ALOAN LN.LNFE0851不同-->
	    <entry key="LNFE0851.uploadUnNormal4">
	        <value>
				INSERT INTO REMOTE.LNFE0851(LNFE0851_CUST_ID, LNFE0851_CUST_DUP, 
				LNFE0851_CUST_NAME, LNFE0851_MDBRNO, LNFE0851_EXP_LOSS, 
				LNFE0851_COLL_STAT, LNFE0851_PROCESS, LNFE0851_COPROCESS, 
				LNFE0851_MDDT, LNFE0851_UPDATER, LNFE0851_AGREEMENT, 
				LNFE0851_AGRAGREECONT, LNFE0851_HORNOR, LNFE0851_NHNORCONT, 
				LNFE0851_CREATEDT, LNFE0851_MDFLAG, LNFE0851_CLOSEFG, 
				LNFE0851_CLOSEDT, LNFE0851_CLOSEPNO, LNFE0851_CLOSEUID,LNFE0851_NDCODE) 
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	        </value>
	    </entry>
		
		<!-- 查詢往來異常戶彙總檔 -->
    <entry key="LNFE0851.updateUnNormaData">
        <value>
        	<![CDATA[
				UPDATE REMOTE.LNFE0851
				SET 
				LNFE0851_NDCODE = ?
				WHERE LNFE0851_CUST_ID=? AND LNFE0851_CUST_DUP=? AND LNFE0851_MDBRNO=? AND LNFE0851_CLOSEFG <> 'Y'
			]]>
        </value>
    </entry>

	<entry key="ELFCUST.queryByIdDupNo">
		<value>
			<![CDATA[
				SELECT *
				FROM #SCHEMA#.ELFCUST
				WHERE ELFCUST_CUSTID = ? AND ELFCUST_DUPNO = ? AND ELFCUST_STUS <> 'C'
				ORDER BY ELFCUST_UPDT DESC, ELFCUST_AS400ID
			]]>
		</value>
	</entry>

	<!-- SELECT DISTINCT CASE WHEN ELF602_FO_KIND LIKE '%D%' THEN 1
							WHEN ELF602_FO_KIND LIKE '%W%' THEN 2 ELSE 3 END AS SEQ,
			ELF602_CUSTID, ELF602_DUPNO, ELF602_CNTRNO, ELF602_LOAN_NO,
			CASE WHEN LOCATE('|', ELF602_FO_KIND) > 0 THEN SUBSTR(ELF602_FO_KIND, 1, LOCATE('|', ELF602_FO_KIND)-1)
					ELSE ELF602_FO_KIND END AS ELF602_FO_KIND
		FROM #SCHEMA#.ELF602
		WHERE ELF602_STATUS IN ('1','2') AND ELF602_CNTRNO LIKE ? -->
    <entry key="ELF602.queryElf602List">
        <value>
            <![CDATA[
            	SELECT 
				       SEQ,ELF602_CUSTID AS CUSTID, ELF602_DUPNO AS DUPNO, 
					   ELF602_CNTRNO AS CNTRNO, ELF602_LOAN_NO AS LOANNO, CNAME,
					   ELF602_FO_KIND AS FOLLOWKIND, ELF602_FO_DATE AS FOLLOWDATE						
				 FROM (
					SELECT
						DISTINCT CASE WHEN ELF602_FO_KIND LIKE ''%D%'' THEN 1
										WHEN ELF602_FO_KIND LIKE ''%W%'' THEN 2 ELSE 3 END AS SEQ,
						ELF602_CUSTID, ELF602_DUPNO, ELF602_CNTRNO, ELF602_LOAN_NO, ELFCUST_ENAME AS CNAME,
						CASE WHEN LOCATE(''|'', ELF602_FO_KIND) > 0 THEN SUBSTR(ELF602_FO_KIND, 1, LOCATE(''|'', ELF602_FO_KIND)-1)
								ELSE ELF602_FO_KIND END AS ELF602_FO_KIND, ELF602_FO_DATE,
						ROW_NUMBER() OVER (PARTITION BY ELF602_CUSTID, ELF602_DUPNO, ELF602_CNTRNO, ELF602_LOAN_NO,ELF602_FO_KIND ORDER BY ELF602_FO_DATE ASC, ELFCUST_UPDT DESC, ELFCUST_AS400ID ) AS RANK
					FROM #SCHEMA#.ELF602
					LEFT JOIN #SCHEMA#.ELFCUST ON ELF602_CUSTID = ELFCUST_CUSTID AND ELF602_DUPNO = ELFCUST_DUPNO
					WHERE ELF602_STATUS IN (''1'',''2'')  AND ELF602_BR_NO = ? AND (ELFCUST_STUS <> ''C'' OR ELFCUST_CUSTID IS NULL)
				) T1
				WHERE T1.RANK = 1
				{0}
			]]>
        </value>
    </entry>

	<entry key="ELF601.getElf601">
		<value>
			SELECT * FROM #SCHEMA#.ELF601
		</value>
	</entry>
	<entry key="ELF602.getElf602">
		<value>
			SELECT * FROM #SCHEMA#.ELF602
		</value>
	</entry>
	<entry key="ELF601.getElf601ByUnid">
		<value>
			SELECT * FROM #SCHEMA#.ELF601 WHERE ELF601_UNID=?
		</value>
	</entry>
	<entry key="ELF602.getElf602ByUnid">
		<value>
			SELECT * FROM #SCHEMA#.ELF602 WHERE ELF602_UNID=?
		</value>
	</entry>
	<entry key="ELF601.getElf601ByUnidLike">
		<value>
			SELECT * FROM #SCHEMA#.ELF601 WHERE ELF601_UNID like ?
		</value>
	</entry>
	<entry key="ELF602.getElf602ByDatasrcLike">
		<value>
			SELECT * FROM #SCHEMA#.ELF602 WHERE ELF602_DATASRC like ?
		</value>
	</entry>
	<entry key="ELF602.getElf602ByDatasrc">
		<value>
			SELECT * FROM #SCHEMA#.ELF602 WHERE ELF602_DATASRC = ?
		</value>
	</entry>
	<entry key="ELF601.updateELF601Status">
		<value>
			UPDATE #SCHEMA#.ELF601 SET ELF601_STATUS = ? WHERE ELF601_UNID = ?
		</value>
	</entry>
	<entry key="ELF602.updateELF602StatusAndMemo">
		<value>
			UPDATE #SCHEMA#.ELF602 SET ELF602_STATUS = ?, ELF602_FO_MEMO = ? WHERE ELF602_UNID = ?
		</value>
	</entry>
	<entry key="ELF601.delete">
		<value>
			DELETE FROM #SCHEMA#.ELF601 WHERE ELF601_UNID = ?
		</value>
	</entry>
	<entry key="ELF602.delete">
		<value>
			DELETE FROM #SCHEMA#.ELF602 WHERE ELF602_UNID = ?
		</value>
	</entry>
    <entry key="ELF602.deleteByFoDateLikeUnid">
        <value>
            DELETE FROM #SCHEMA#.ELF602 WHERE ELF602_FO_DATE = ? AND ELF602_UNID Like ?
        </value>
    </entry>
	<entry key="ELF601.insert">
		<value>
			INSERT INTO #SCHEMA#.ELF601
			(ELF601_UNID,ELF601_CNTRNO,ELF601_LOAN_NO,ELF601_LOAN_KIND,
			ELF601_FO_KIND,ELF601_FO_CONTENT,ELF601_FO_WAY,
			ELF601_FO_CYCLE,ELF601_FO_BEG_DATE,ELF601_FO_END_DATE,
			ELF601_FO_NEXT_DATE,ELF601_STAFF,ELF601_FO_STAFFNO,
			ELF601_AO_STAFFNO,ELF601_STATUS,
			ELF601_CRE_DATE,ELF601_CRE_TELLER,ELF601_CRE_SUPVNO,
			ELF601_UPD_DATE,ELF601_UPD_TELLER,ELF601_UPD_SUPVNO,
			ELF601_FULL_CONTENT,ELF601_CUSTID,ELF601_DUPNO,ELF601_BR_NO,
            ELF601_CASE_MARK, ELF601_SUID, ELF601_SAPPTIME, ELF601_SSEQNO)
			VALUES
			(?, ?, ?, ?,
			?, ?, ?,
			?, ?, ?,
			?, ?, ?,
			?, ?,
			?, ?, ?,
			?, ?, ?,
			?, ?, ?, ?,
            ?, ?, ?, ?)
		</value>
	</entry>
	<entry key="ELF602.insert">
		<value>
			INSERT INTO #SCHEMA#.ELF602
			(ELF602_UNID,ELF602_CNTRNO,ELF602_LOAN_NO,ELF602_LOAN_KIND,
			ELF602_FO_KIND,ELF602_FO_CONTENT,ELF602_STAFF,ELF602_FO_DATE,
			ELF602_CHKDATE,ELF602_CONFORM_FG,ELF602_FO_MEMO,
			ELF602_STATUS,ELF602_DATASRC,ELF602_UNUSUAL_FG,
			ELF602_UNUSUALDESC,ELF602_ISNOTIONAL,ELF602_ISAML,
			ELF602_UPD_DATE,ELF602_UPD_TELLER,ELF602_UPD_SUPVNO,
			ELF602_FULL_CONTENT,ELF602_FIELDMAINID,ELF602_FILEDESC,
			ELF602_CUSTID,ELF602_DUPNO,ELF602_BR_NO,ELF602_CASE_MARK)
			VALUES
			(?, ?, ?, ?,
			?, ?, ?, ?,
			?, ?, ?,
			?, ?, ?,
			?, ?, ?,
			?, ?, ?,
			?, ?, ?,
			?, ?, ?, ?)
		</value>
	</entry>
		
	    	
	    <!-- 更新MISELLSEEK 暴險註記  更新最新一筆TMESTAMP-->
	    <entry key="ELF461.updateOnlySpecialFinRisk">
	        <value>
	         UPDATE #SCHEMA#.ELF461 SET 
			 ELF461_ISSPEFIN=?, ELF461_SPEFINTYPE=?, ELF461_ISADC=?, ELF461_ISPROJOP=?,
			 ELF461_HQPROJ_OPT1=?, ELF461_HQPROJ_OPT2=?, ELF461_HQPROJ_OPT3=?,
        	 ELF461_HQPROJ_OPT4=?, ELF461_HQPROJ_OPT5=?, ELF461_HQPROJ_RES=?
			 WHERE ELF461_CUSTID =?  AND ELF461_DUPNO =? AND ELF461_CNTRNO =? AND ELF461_TMESTAMP =?
	        </value>
	    </entry>
		
		<entry key="ELF461.insertOnlySpecialFinRisk">
	        <value>
	          INSERT INTO #SCHEMA#.ELF461 (
	          ELF461_CUSTID,ELF461_DUPNO,ELF461_CNTRNO,ELF461_BRNO,ELF461_STATUS,
	          ELF461_APPRYY,ELF461_APPRMM,ELF461_UPDATER,ELF461_TMESTAMP,
			  ELF461_ISSPEFIN,ELF461_SPEFINTYPE,ELF461_ISADC,ELF461_ISPROJOP,
			  ELF461_HQPROJ_OPT1, ELF461_HQPROJ_OPT2, ELF461_HQPROJ_OPT3,
              ELF461_HQPROJ_OPT4, ELF461_HQPROJ_OPT5, ELF461_HQPROJ_RES
	          ) VALUES(
				?,?,?,?,?,
				?,?,?,?,
				?,?,?,?, 
				?,?,?,
				?,?,?
			  )
	        </value>
	    </entry>	
		
		
		
		
		<!--J-111-0087_05097_B1001 Web e-Loan國內海外企金簽報書就不同業務授權獨立產生LGD數據-->
		<!--簽報書上傳用 - 新增授信額度共用檔ELF025 -->
	    <entry key="ELF025.insert">
	        <value>
	            INSERT INTO REMOTE.ELF025 
				(
	               	ELF025_SDATE,ELF025_MAINID,ELF025_BRANCH,ELF025_CASEDATE,ELF025_DOCUMENT_NO,
					ELF025_CONTRACT_CO,ELF025_CONTRACT,ELF025_SWFT,ELF025_FACT_AMT,ELF025_UPDATER,
					ELF025_TMESTAMP
	            ) 
				VALUES
				(
				   ?, ?, ?, ?, ?, 
				   ?, ?, ?, ?, ?,
				   ?
				)
	        </value>
	    </entry>
	 
	
		<!--J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能-->
		<entry key="ELF506.update_PROD_KIND_ByCntrNo">
			<value>
			update #SCHEMA#.elf506 set ELF506_PROD_KIND = ?,ELF506_ADC_CASENO = ? WHERE ELF506_CNTRNO = ?
			</value>
		</entry>
		
		
		<!--簽報書上傳用 - 刪除授信額度共用檔ELF025依簽報書MAINID -->
	    <entry key="ELF025.delByMainId">
	        <value>
	            DELETE FROM REMOTE.ELF025 WHERE ELF025_MAINID =?
	        </value>
	    </entry>

	    <!--動審表上傳用 -主從債務人檔 ELLNGTEE(401)  新增 -->
	    <entry key="ELF401.insert">
	        <value>
	            INSERT INTO REMOTE.ELF401
	            (BRNO, CUSTID, DUPNO, CNTRNO, LNGEFLAG, LNGEID, DUPNO1, LNGENM, NTCODE, LNGERE, UPDDT,  UPDATER,RELEASEDT , LNGEOUT, TIMESTAMP, GRTRT,PRIORITY, GUANAEXPO, GRTAMT, LOCALID) values
	            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,'Y',CURRENT TIMESTAMP,?,?,?,?,?)
	        </value>
	    </entry>
		
	   <!--動審表上傳用 -主從債務人檔 ELLNGTEE(401)   刪除該 分行號碼 、客戶編號、重覆序號、額度序號已存在的 -->
		<!--BRNO=? AND   不要含BRNO，因為資料建檔那邊的BRNO是維護分行，如果007維護 005的額度序號，那BRNO會變成007 -->
	    <entry key="ELF401.delByUniqueKey">
	        <value>
	            DELETE FROM REMOTE.ELF401 WHERE CUSTID =? AND DUPNO=? AND CNTRNO=?
	        </value>
	    </entry>
		<!--動審表上傳用 - J-113-0035 貸後追蹤分項紀錄檔-->
		<entry key="ELF603.insertForInside">
			<value>
				<![CDATA[
				INSERT INTO #SCHEMA#.ELF603
					(
					  ELF603_UID, ELF603_APPTIME, ELF603_CNTRNO, ELF603_SEQNO, 
					  ELF603_ESGTYPE, ELF603_ESGMODEL, ELF603_TRACOND, ELF603_TRAPROFIK, 
					  ELF603_TRAMONTH, ELF603_CONTENT, ELF603_UPDATER, ELF603_UPDATETIME
					) 
				VALUES 
					(
					  ?, ?, ?, ?,
					  ?, ?, ?, ?,
					  ?, ?, ?, ?
					)
				]]>
			</value>
		</entry>
		<!--動審表上傳用 - J-113-0035 貸後追蹤分項紀錄檔 刪除-->
	    <entry key="ELF603.delByUidIdAppDate">
	        <value>
	            <![CDATA[
				DELETE FROM #SCHEMA#.ELF603 WHERE ELF603_UID = ? AND ELF603_APPTIME = ?
			    ]]>
	        </value>
	    </entry>
		
		<entry key="ELF603.delByUidIdAppDateCntrNo">
	        <value>
	            <![CDATA[
				DELETE FROM #SCHEMA#.ELF603 WHERE ELF603_UID = ? AND ELF603_APPTIME = ? AND ELF603_CNTRNO = ?
			    ]]>
	        </value>
	    </entry>
		
	    <entry key="ELF603.delBUid">
	        <value>
	            <![CDATA[
				DELETE FROM #SCHEMA#.ELF603 WHERE ELF603_UID = ?
			    ]]>
	        </value>
	    </entry>
		
		 <entry key="ELF603.getElf603ByKey">
	        <value>
	            <![CDATA[
				SELECT * FROM #SCHEMA#.ELF603 WHERE ELF603_UID=? AND ELF603_APPTIME=? AND ELF603_CNTRNO=? AND ELF603_SEQNO=?
			    ]]>
	        </value>
	    </entry>
		
		<entry key="ELFAPPNO.listAppNoData">
	        <value>
	            <![CDATA[
				select * from #SCHEMA#.ELFAPPNO WHERE ELFAPPNO_BRANCH = ? and ELFAPPNO_CUSTID = ? and ELFAPPNO_DUPNO = ?
			    ]]>
	        </value>
	    </entry>
		
		<!--J-113-0XXX 泰國個金徵信評等檔(INSERT)-->
		<entry key="ELF026.insertForInside">
			<value>
				<![CDATA[
				INSERT INTO #SCHEMA#.ELF026
					(
						ELF026_BR_CD,ELF026_CUST_KEY,ELF026_GL_SUB,ELF026_CONTRACT,
						ELF026_CHK_DATE,ELF026_CUST_ID,ELF026_DUP_NO,ELF026_NOTE_ID,
						ELF026_F_R_FLAG,ELF026_F_RATING,ELF026_RATING_DATE,ELF026_RATING_ID,
						ELF026_LN_CODE,ELF026_MOW_TYPE,ELF026_MOW_VER1,ELF026_MOW_VER2,ELF026_LN_PERIOD
					) 
				VALUES 
					(
					  ?, ?, ?, ?,
					  ?, ?, ?, ?,
					  ?, ?, ?, ?,
					  ?, ?, ?, ?, ?
					)
				]]>
			</value>
		</entry>
		<!--J-113-0XXX 泰國個金徵信評等檔(DELETE)-->
		<entry key="ELF026.delByKey">
	        <value>
	            <![CDATA[
				DELETE FROM #SCHEMA#.ELF026 
					WHERE ELF026_BR_CD = ? 
					AND ELF026_CUST_KEY = ?
					AND ELF026_GL_SUB = ? 
					AND ELF026_CONTRACT = ? 
					AND ELF026_CHK_DATE = ? 
					AND ELF026_CUST_ID = ? 
					AND ELF026_DUP_NO = ? 
					AND ELF026_NOTE_ID = ? 
			    ]]>
	        </value>
	    </entry>
		
		
	</util:map>
</beans>
