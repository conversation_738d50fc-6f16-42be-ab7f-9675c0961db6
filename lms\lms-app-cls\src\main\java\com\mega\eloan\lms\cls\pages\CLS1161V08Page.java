package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**
 * 消金模擬動審編製中
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/cls/cls1161v08")
public class CLS1161V08Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			list.add(LmsButtonEnum.View);
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			list.add(LmsButtonEnum.Add);
			list.add(LmsButtonEnum.Modify);
			list.add(LmsButtonEnum.Delete);
		}
		addToButtonPanel(model, list);

		renderJsI18N(CLS1161V08Page.class);

		// UPGRADE: 待確認畫面是否正常
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1161V08Page');");
	}

}
