/* 
 * MisEJF356Service.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * BAM302->EJV35601->EJF356(共同債務資料)
 * </pre>
 * 
 * @since 2012/8/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/8/7,TimChiang,new
 *          </ul>
 */
public interface MisEJF356Service {
	
	/**
	 * 查詢聯徵BAM302資料。
	 * @param qDate 介面_取得聯徵資料日期，查詢日期，格式為 YYY/MM/DD
	 * @param compIds 統編，可輸入多筆
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findLoanAmountAndBalance(String qDate, List<String> compIds);
	
	/**
	 * 查詢聯徵BAM302資料。多用於引進聯徵中心共同債務資料
	 * @param qDate 介面_取得聯徵資料日期，查詢日期，格式為 YYY/MM/DD
	 * @param compIds 統編，可輸入多筆
	 * @param prIds 產品別，可輸入多筆
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findLoanAmountAndBalance(String qDate, List<String> compIds, List<String> prIds);	
}
