<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<div id="C160M02ADiv" class="content">
			<form id="C160M02AForm" name="C160M02AForm" >
				<fieldset>
					<legend><b><th:block th:text="#{'title.baseInfo'}">基本資訊</th:block></b></legend>
					<table class="tb2" width="100%">
						<tr>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M02A.ownBrId'}">編製單位代號</th:block>&nbsp;&nbsp;</td>
							<td width="35%">
								<span id="ownBrId" class="field" ></span>&nbsp;<span id="ownBrIdName" class="field" ></span>
							</td>		
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M02A.caseNo'}">案件號碼</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="caseNo" class="field"></span>&nbsp;
							</td>					
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M02A.docStatus'}">目前文件狀態</th:block>&nbsp;&nbsp;</td>
							<td colspan="3"><b class="text-red"><span id="docStatus" class="field" ></span>&nbsp;</b></td>							
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M02A.custId'}">統一編號</th:block>&nbsp;&nbsp;</td>
							<td ><span id="custId" class="field" ></span>&nbsp;<span id="dupNo" class="field" ></span></td>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M02A.custName'}">客戶名稱</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="custName" class="field"></span>&nbsp;</td>							
						</tr>
						<tr>
							<td class="hd2" align="right">&nbsp;&nbsp;</td>
							<td colspan="3" >
								<button type="button" id="btCaseType3" class="forview">
									<span class="text-only"><th:block th:text="#{'button.btCaseType3'}" class="btCaseType">匯入EXCEL</th:block></span>
								</button>								
								<br/>
								<span id="cntrNo" class="field" ></span>
							</td>
						</tr>
					</table>
				</fieldset>
				<fieldset>
					<legend><b><th:block th:text="#{'title.docLog'}">文件異動記錄</th:block></b></legend>
					<div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
					<table class="tb2" width="100%">
						<tr>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M02A.creator'}">建立人員號碼</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="creator" class="field" ></span>(<span id="createTime" class="field" ></span>)</td>
							<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M02A.updater'}">異動人員號碼</th:block>&nbsp;&nbsp;</td>
							<td width="35%"><span id="updater" class="field" ></span>(<span id="updateTime" class="field" ></span>)</td>
						</tr>
						<tr>
							<td class="hd2" align="right">&nbsp;</td>
							<td>&nbsp;</td>
							<td class="hd2" align="right"><th:block th:text="#{'C160M02A.randomCode'}">文件亂碼</th:block>&nbsp;&nbsp;</td>
							<td><span id="randomCode" class="field" ></span>&nbsp;</td>
						</tr>
					</table>
				</fieldset>
				<fieldset>
					<legend><b><th:block th:text="#{'title.businessUnits'}">營業單位</th:block></b></legend>
					<table width="100%">
						<tr>							
							<td width="15%" align="right"><b class="text-red"><th:block th:text="#{'C160M02A.apprId'}">經辦</th:block>：&nbsp;</b></td>
							<td width="85%" align="left"><span id="apprId" class="field" ></span>&nbsp;<span id="apprNm" class="field" ></span></td>
						</tr>
					</table>
				</fieldset>
			</form>
		</div>
		
		
		<!-- 匯入EXCEL -->
		<div id="CaseType3ThickBox" style="display:none;" >
			<form id="CaseType3Form">
					<table class="tb2" width="100%">
						<tr>
							<td width="30%" class="hd2" align="right">
								<button type="button" id="btUploadExcel" >
									<span class="text-only"><th:block th:text="#{'button.btUploadExcel'}">上傳Excel</th:block></span>
								</button>
							</td>
							<td width="70%">
								<input type="hidden" id="excelId" name="excelId" />
								<input type="hidden" id="importExcelStatus" name="importExcelStatus" />
								<a href="#" id="downloadExcel" ><th:block th:text="#{'button.view'}">調閱</th:block></a>
								&nbsp;&nbsp;
								<span id="progressTr" class="text-red" style="display:none;" >
									<th:block th:text="#{'C160M02A.progress'}">處理進度</th:block>：
									<b><span id="progress" class="field" ></span></b>&nbsp;%
								</span>
							</td>
						</tr>
						<tr>
							<td width="30%" class="hd2" align="right"><th:block th:text="#{'C160M02A.finCount'}">完成筆數</th:block>&nbsp;&nbsp;</td>
							<td width="70%"><span id="finCount" class="field" ></span>/<span id="totCount" class="field" ></span></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M02A.custId1'}">匯入之機關團體統編</th:block>&nbsp;&nbsp;</td>
							<td><span id="custId" class="field" ></span>&nbsp;<span id="dupNo" class="field" ></span></td>
						</tr>
						<tr>
							<td class="hd2" align="right"><th:block th:text="#{'C160M02A.custName1'}">匯入之機關團體名稱</th:block>&nbsp;&nbsp;</td>
							<td><span id="custName" class="field" ></span>&nbsp;</td>
						</tr>
					</table>
			</form>
		</div>
		<script type="text/javascript">loadScript('pagejs/cls/CLS1161S21Panel');</script>
	</th:block>
</body>
</html>
