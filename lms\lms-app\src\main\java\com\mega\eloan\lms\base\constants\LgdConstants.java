/* 
 * UtilConstants.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.constants;

/**
 * <pre>
 * 專案常用 參數
 * </pre>
 * 
 * @since 2011/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/29,REX,new
 *          <li>2013/06/25,Rex,修改選項代碼錯誤 自然人 = "1"; 公司法人 = "5";土地抵押貸款 =
 *          "2";非央行自然人 = "4";
 *          </ul>
 */
public interface LgdConstants {

	/** LGD授權業務別 **/
	interface bussType {
		static final String 一般 = "1";
		static final String 衍生性金融商品 = "3";
		static final String 交換票據抵用 = "4";
		static final String 整批團貸 = "5";
		static final String 進口 = "7";
		static final String 出口 = "8";
		static final String 同業交易 = "9";
		static final String 保兌 = "A";
		static final String 承兌 = "B";
		static final String 應收帳款賣方_有追 = "C";
		static final String 應收帳款賣方_無追 = "D";
		static final String 供應鏈融資_賣方 = "E";
		static final String 供應鏈融資_買方 = "F";
		static final String 貼現 = "G";
		static final String 預約付款應收帳款承購_有追索權= "H";
		static final String 遠匯 = "X";
		static final String 其他 = "Z";
	}

	interface haveNo {
		String 有 = "1";
		String 無 = "2";
		String NA = "3";
	}

	/**
	 * 擔保品種類
	 */
	interface CollTyp1 {
		String 不動產 = "01";
		String 動產 = "02";
		String 權利質權 = "03";
		String 動產質權 = "04";
		String 保證 = "05";
		String 額度本票分期償還票據 = "06";
		String 融資客票 = "07";
		String 信託占有 = "08";
		String 參貸他行 = "09";
		String 其他 = "10";
		String 反面承諾 = "11";
		String 浮動擔保抵押 = "12";
	}

	/** 權利質權 */
	interface PLEDGEOFRIGHTS {
		String 銀行定存單 = "01";
		String 國庫券 = "02";
		String 公債 = "03";
		String 金融債券 = "04";
		String 央行儲蓄券 = "05";
		String 公司債 = "06";
		String 股票 = "07";
		String 開放型基金 = "08";
		String 票券 = "09";

	}

}
