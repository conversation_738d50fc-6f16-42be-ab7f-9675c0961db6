
var initDfd = $.Deferred(), inits = {
    fhandle: "lms7500m01formhandler",
    ghandle: "lms7500gridhandler"
};

var RealEstateAction = {
    _isLoad: false,
	beforeGrid: null,
	afterGrid: null,
	fileGrid: null,
	isMegaInternalCntrNo: null,
	realEstateFormDetailData: {},
	_initEvent: function(){
		
        $("#addRealEaste").click(function(){
            $("input[name='isBuy'],input[name='isInstalment']").addClass("required");
            if ($("input[name='isBuy'],input[name='isInstalment']").valid()) {
                RealEstateAction.addRealEstate();
            }
            $("input[name='isBuy'],input[name='isInstalment']").removeClass("required");
        });
        $("#deleteRealEaste").click(function(){
            RealEstateAction.deleteRealEstate();
        })
	
		$("input[name='estateSubType']").click(function(){
			RealEstateAction.setEditEstateBoxUI();
			//J-109-0248_05097_B1001 Web e-Loan授信都更之計畫進度，將已送件未核定納入排除72-2項目
			$('#estateStatus').trigger('change');
		})
		
		//J-109-0248_05097_B1001 Web e-Loan授信都更之計畫進度，將已送件未核定納入排除72-2項目
		$('#estateStatus').change(function(){
			var estateSubType = $("input[name='estateSubType']:radio:checked").val();
			var estateStatus = $(this).val() ;
			if(estateSubType == '02' && estateStatus == '2' ){
				$('#showTipsForStatus_2').show();
			}else{
				$('#showTipsForStatus_2').hide();
			}
			//J-110-0054_10702_B1001 Web e-Loan額度明細表不動產暨72-2相關註記修改
			if(estateSubType == '03'){
				$("#estateSubType03").show();
				$("#estateOwner").addClass("required");
			}
			else{
				$("#estateSubType03").hide();
				$("#estateOwner").removeClass("required");
			}
		});

		$("input[name='buildWay']").click(function(){
			if($(this).val() != "02" && $(this).val() != "03"){
				$("#landlordNum").val("")
			}
			//J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
			if($(this).val() != "05"){
				$("#otherDesc").val("")
			}
			RealEstateAction._isLoad && RealEstateAction.setEditEstateBoxUI();
		})
		
		$("#mCntrNo").change(function(){
			$(':input', '#realEstateFormDetail').not('#mCntrNo,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
			RealEstateAction._isLoad &&  RealEstateAction.setEditEstateBoxUI();
		})
		
		$("input[name='position']").change(function(){
			RealEstateAction._isLoad && RealEstateAction.setEditEstateBoxUI();
		});
			
		$("#estateButton").click(function(){
			$(':input', '#realEstateFormDetail').not('#mCntrNo,:button, :submit, :reset, :hidden').removeAttr('checked').removeAttr('selected').not(':checkbox, :radio').val('');
			
	        $.ajax({
	            handler: inits.fhandle,
	            action: "queryL140MM3CByMCntrNo",
	            data: {
	            	mCntrNo : $("#mCntrNo").val()
	            },
	            success: function(obj){
	            	$("#realEstateFormDetail").injectData(obj);
					RealEstateAction.realEstateFormDetailData = obj;

             	   $("#realEstateFormDetail").find("select").trigger("change", "init");
					RealEstateAction.setEditEstateBoxUI();
					//J-110-0054_10702_B1001 Web e-Loan額度明細表不動產暨72-2相關註記修改
					var estateSubType = $("input[name='estateSubType']:radio:checked").val();
					if(estateSubType == '03'){
						$("#estateSubType03").show();
						$("#estateOwner").addClass("required");
					}
					else{
						$("#estateSubType03").hide();
						$("#estateOwner").removeClass("required");
						$("#estateOwner").val("");
					}
	            }
			})			
			
		})
		
		/*
		$("#uploadEstateFile").click(function(){
			RealEstateAction.uploadEstateFile();
		});
		
		$("#deleteEstateFile").click(function(){
			RealEstateAction.deleteEstateFile();
		});
		*/
		
		//融資業務分類只可選擇一筆
		var $a0 = $("input[name='estateType']");
		$a0.click(function(){
			var checkCanClick = true;
			$a0.each(function(k, v){
				var isDiAndCheck = ($(this).attr("checked")) && ($(this).attr("disabled"))				
				if(isDiAndCheck){
					checkCanClick = false;
					return false;
				}
			})
			if(!checkCanClick){
				return checkCanClick;
			} else {
				var value = $(this).val();				
				$("input[name='estateType']").filter("[value !='"+ value + "']").attr("checked", false);
			}			
		})
		
		
		var defaultSelect = "<option value=''>" + i18n.def.comboSpace + "</option>";
        $("#estateCityId").setItems({
            item: QueryCityCode.getCode("1", ""),
            sort: "asc",
            value: "",
            fn: function(k, v){
            	RealEstateAction.changCityValue($("#estateCityId"), $("#estateAreaId"), $("#estateSit3No"), $("#estateSit4No"), defaultSelect, (!v));
            }
        });
        
        $("#estateAreaId").change(function(k, v){
			if ($("#estateAreaId").val() != "") {
				RealEstateAction.changAreaValue($("#estateCityId"), $("#estateAreaId"), $("#estateSit3No"), $("#estateSit4No"), defaultSelect, (!v));
			}
        });
		
		
		$("#estateCityIdXX").setItems({
            item: QueryCityCode.getCode("1", ""),
            sort: "asc",
            value: "",
            fn: function(k, v){
            	RealEstateAction.changCityValue($("#estateCityIdXX"), $("#estateAreaIdXX"), $("#estateSit3NoXX"), $("#estateSit4NoXX"), defaultSelect, (!v));
            }
        });
		
		$("#estateAreaIdXX").change(function(k, v){
			if ($("#estateAreaIdXX").val() != "") {
				RealEstateAction.changAreaValue($("#estateCityIdXX"), $("#estateAreaIdXX"), $("#estateSit3NoXX"), $("#estateSit4NoXX"), defaultSelect, (!v));
			}
        });
		
        $("input[name='sectKind']").change(function(k, v){			
			var useItem = "";
            var value = $(this).val();
			
            if (value == "1") {
                useItem = CommonAPI.loadCombos(["LandUse21", "cms1010_useKind1"]);
                $("#useSect").setItems({
                    item: useItem.LandUse21,
                    format: "{key}"
                });
                $("#useKind").setItems({
                    item: useItem.cms1010_useKind1,
                    format: "{key}"
                });
            } else if(value == "2") {
				useItem = CommonAPI.loadCombos(["LandUse22", "cms1010_useKind2"]);
                $("#useSect").setItems({
                    item: useItem.LandUse22,
                    format: "{key}"
                });
                $("#useKind").setItems({
                    item: useItem.cms1010_useKind2,
                    format: "{key}"
                });
			}
			
			if(RealEstateAction.realEstateFormDetailData.useSect){
				$("#useSect").val(RealEstateAction.realEstateFormDetailData.useSect);
			}
            if(RealEstateAction.realEstateFormDetailData.useKind){
				$("#useKind").val(RealEstateAction.realEstateFormDetailData.useKind);
			}
        });
		
		$("input[name=estateStatus_common]").change(function(){
			var position = $("input[name='position']:radio:checked").val();
			RealEstateAction.switchCompletionDateAndExpFactoryNoObetainingDate($(this).val(), position);
		});
		
		$("input[name=position]").change(function(){
			var estateStatus = $("input[name='estateStatus_common']:radio:checked").val();
			RealEstateAction.switchCompletionDateAndExpFactoryNoObetainingDate(estateStatus, $(this).val());
		});
		
		$("#overDate_common").change(function(){
			RealEstateAction.isShowEstateStatusAndOverDateDontModifyMessage();
		});
	},
	_reloadBeforeGrid:function(){
        this.beforeGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
            postData: {
                mainId: responseJSON.mainId,//$("#mainId").val(),
                mainId: $("#mainId").val(),
                flag: "N"
            }
        }).trigger("reloadGrid");
	},
	_reloadAfterGrid:function(){
        this.afterGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
            postData: {
                mainId: responseJSON.mainId,//$("#mainId").val(),
                mainId: $("#mainId").val(),
                flag: "Y"
            }
        }).trigger("reloadGrid");
	},
	_initGrid: function(){
		this.beforeGrid = $("#realEstateBeforeGrid").iGrid({
			height: 100,
			handler: inits.ghandle,
			sortname: 'createTime',
			sortorder: 'asc',
			action: "queryL140mm3c",
			postData: {
				mainId: responseJSON.mainId,//$("#mainId").val(),
				flag: "N"
			},
			loadComplete: function(){
				$('#realEstateBeforeGrid a').click(function(e) {
					// 避免<a href="#"> go to top
					e.preventDefault();
				});
			},
			colModel: [{
				colHeader: i18n.lms7500m01["L140M01T.view.001"],//類別
				width: 80,
				name: 'estateType',
				sortable: true,
				formatter: 'click',
				onclick: RealEstateAction.openEditEstateBox
			}, {
				colHeader: " ",
				name: 'checkYN',
				align: 'center',
				width: 5
			}, {
				colHeader: "oid",
				name: 'oid',
				hidden: true
			}, {
				colHeader: "flag",
				name: 'flag',
				hidden: true
			}],
			ondblClickRow: function(rowid){
				var data = RealEstateAction.beforeGrid.getRowData(rowid);
				RealEstateAction.openEditEstateBox(null, null, data);
			}
		});
		
		this.afterGrid = $("#realEstateAfterGrid").iGrid({
			height: 100,
			handler: inits.ghandle,
			sortname: 'createTime',
			sortorder: 'asc',
			action: "queryL140mm3c",
			postData: {
				mainId: responseJSON.mainId,//$("#mainId").val(),
				flag: "Y"
			},
			loadComplete : function() {
				$('#realEstateAfterGrid a').click(function(e) {
					// 避免<a href="#"> go to top
					e.preventDefault();
				});
			},
			colModel: [{
				colHeader: i18n.lms7500m01["L140M01T.view.001"],//類別
				width: 80,
				name: 'estateType',
				sortable: true,
				formatter: 'click',
				onclick: RealEstateAction.openEditEstateBox
			}, {
				colHeader: " ",
				name: 'checkYN',
				align: 'center',
				width: 5
			}, {
				colHeader: "oid",
				name: 'oid',
				hidden: true
			}, {
				colHeader: "flag",
				name: 'flag',
				hidden: true
			}],
			ondblClickRow: function(rowid){
				var data = RealEstateAction.afterGrid.getRowData(rowid);
				RealEstateAction.openEditEstateBox(null, null, data);
			}
		});
	},
	_init: function(){
        if (!this._isLoad) {
            this._initGrid();
            this._initEvent();
            //this.initFileGrid();
            this._isLoad = true;            
        }
        else {
            this._reloadGrid();
        }
    },
    /*
    initFileGrid: function(){
    	this.fileGrid = $("#estateFileGrid").iGrid({
			height: 100,
			handler: inits.ghandle,
			sortorder: 'asc',
			action: "queryFile",
			localFirst: true,
			postData: {
				mainId: $("#mainId").val()
			},
			loadComplete : function() {
				$('#estateFileGrid a').click(function(e) {
					// 避免<a href="#"> go to top
					e.preventDefault();
				});
			},
			colModel: [{
				colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
				width: 80,
				name: 'srcFileName',
				sortable: true,
				formatter: 'click',
				onclick: function (cellvalue, options, rowObject){
				    $.capFileDownload({
				        handler:"simplefiledwnhandler",
				        data : {
				            fileOid:rowObject.oid
				        }
				    });
				}
			}, {
                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
                name: 'fileDesc',
                width: 100,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
                name: 'uploadTime',
                width: 100,
                align: "center",
                sortable: true
				},
			{
				colHeader: "oid",
				name: 'oid',
				hidden: true
			}]
		});
    },
    */
    deleteRealEstate: function(){
        var girdId = this.afterGrid.getGridParam('selrow');
        var data = this.afterGrid.getRowData(girdId);
        if (!girdId) {          
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(result){
            if (result) {
                $.ajax({
                    handler: inits.fhandle,
                    action: "deleteL140mm3c",
                    data: {
                        tOid: data.oid,
                        mainId: $("#mainId").val()
                    },
                    success: function(obj){
                        RealEstateAction.afterGrid.trigger("reloadGrid");
                        
                        $("input[name='is722Flag']").attr('checked', false);
                        $("input[name='is722Flag'][value='" + obj.is722Flag + "']").attr("checked", true);
                    }
                })
            }
        });
    },
    setEditEstateBoxUI: function(){
    	$("#reBuildView1").hide();
    	$("#reBuildView2").hide();
    	$("#reBuildView3").hide();
    	$("#reBuildView4").hide();
		$("#landlordView").hide();
		$("#otherDescView").hide(); //J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
    	$("#_estateSubType04View").hide();
		
		$(".estateView").hide();
		
		// 是否為營運周轉金用途
		var isTurnOver = $("input[name='isTurnOver']").val();
    	    	    	
    	var value1 = $("#_estateType").val();
				
		if(value1 == "A0#" || value1.substring(0, 1) == "D"){
			$("#" + value1.replace( /(:|\.|\[|\]|,|=|@|#)/g, "\\$1" ) + "_View").show().siblings("div").hide();			
			if(value1 != "D01"){
				$("#common_View").show()
			}
		}
		
		var position = $("input[name='position']:checked").val();
		
		if(position == "0"){
			$("#commonSiteView").show();
			$("#A0井_View_sub").show();
		} else {
			$("#commonSiteView").hide();
			$("#A0井_View_sub").hide();
		}
		
		
    	if(value1 == "D01"){
    		$("#reBuildView1").show();
			// 當是都更危老時，如果是海外額度序號，或是營運周轉金的用途，名細都可以編輯
			if(isTurnOver == "Y" || !RealEstateAction.isMegaInternalCntrNo){
				$("#reBuildView4").hide();
				$("#reBuildView1 input").attr("disabled", false).attr("readOnly", false);
				$( "#overDate").datepicker(  );
				
				$("#reBuildView2").find("input, select").each(function(){
					if($(this).hasClass("editable")){
						
					} else {
						$(this).attr("disabled", false);
						$(this).attr("readOnly", false);
					}
				})
			} else {
				$("#reBuildView4").show();
				$("#reBuildView1 input").attr("disabled", true).attr("readOnly", true);
				$( "#overDate").datepicker( "destroy" );
				$("#reBuildView2").find("input, select").each(function(){
					if($(this).hasClass("editable")){
						
					} else {
						$(this).attr("disabled", true);
						$(this).attr("readOnly", true);
					}
				})
			}
    	}
    	if(value1 == "D01"){
			
    		//var isCityRebuild = $("input[name='isCityRebuild']:checked").val();
			//當是都更危老時，非營運周轉金的用途，國內額度序號才出現引入按鈕
			
    		//if((isCityRebuild == "Y" && isTurnOver == "N" && RealEstateAction.isMegaInternalCntrNo) ){
			if((isTurnOver == "N" && RealEstateAction.isMegaInternalCntrNo) ){
				//母戶額度序號引入
    			$("#reBuildView3").show();
    		}
    		
    		value2 = $("input[name='estateSubType']:checked").val()
    		if(value2 == undefined || value2 == "01"){
    			$("#reBuildView2").hide();
    		} else {
    			$("#reBuildView2").show();
    			/*    			
    			RealEstateAction.fileGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料    	            
    	            postData: {
    	            	mainId: responseJSON.mainId,//$("#mainId").val(),
    	            	fieldId: "estateType" + $("#_estateType").val()
    	            },
    	            search: true
    	        }).trigger("reloadGrid");
    			*/
    		}
    		if(value2 == "04") {
    			$("#_estateSubType04View").show();
    		} else {
    			$("#_estateSubType04View").hide();
    		}
    		
			var buildWay = $("input[name='buildWay']:checked").val();
			if(buildWay == "02" || buildWay == "03" ){
				$("#landlordView").show();
			}
			//J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
			if(buildWay == "05"){
				$("#otherDescView").show();
			}
    	} else {
    		$("#reBuildView2").hide();
    	}
    },
    openEditEstateBox: function(cellvalue, options, data){

        var $form = $("#realEstateFormDetail");
        $form.reset();
		var estateTypeCode = data.estateType.split(" ")[0];
		RealEstateAction.isShowFactoryBuyingOption(estateTypeCode);
		RealEstateAction.switchCompletionDateAndExpFactoryNoObetainingDate(data.estateStatus, data.position);

		$form.find(":input").removeAttr("disabled").removeAttr("readonly");
		
		var buttons = {
			"saveData": function(){
	            if ($form.valid()) {
	                $.ajax({
	                    handler: inits.fhandle,
	                    formId: "empty",
	                    action: "saveL140MM3C",
	                    data: $.extend($("#realEstateFormDetail").serializeData(), {
	                        mainId: responseJSON.mainId,//$("#mainId").val(),
	                        oid: data.oid,
							cntrNo : $("#cntrNo").val()
	                    }),
	                    
	                    success: function(obj){				
							RealEstateAction.afterGrid.trigger("reloadGrid");
	                    
	                        //saveSuccess=儲存成功
	                        CommonAPI.confirmMessage(i18n.def["saveSuccess"], function(b){
	                            if (b) {
	                                $.thickbox.close();
	                            }
	                        });
							$("input[name='is722Flag']").attr('checked', false);
							$("input[name='is722Flag'][value='"+obj.is722Flag+"']").attr("checked", true);
	                    }
	                });
	            }
	        },
	        "close": function(){
	            $.thickbox.close();
	        }
		}

        $.ajax({
            handler: inits.fhandle,
            formId: "empty",
            action: "queryL140MM3C",
            data: {
                tOid: data.oid,
				mainId : responseJSON.mainId,//$("#mainId").val()
				cntrNo : $("#cntrNo").val()
            },
            success: function(obj){
            	RealEstateAction.realEstateFormDetailData = obj;
				RealEstateAction.isMegaInternalCntrNo = obj.isMegaInternalCntrNo;
                $form.injectData(obj);
				$("input[name='sectKind']:checked").triggerHandler("change")
                $form.find("select").trigger("change", "init");
                RealEstateAction.setEditEstateBoxUI();
				
				var itemType2Special = false;

				if(inits.itemType == "2"){
					var caseBox16 = $("#caseBox16").attr("checked");
					if(!caseBox16){
						itemType2Special = true;
					}
				}
				/*
                if (data.flag == "N" || itemType2Special) {
                    delete buttons.saveData
                    $form.find(":input").attr("disabled", true).attr("readonly", true);
                    $("#estateFileGridView").hide();
                    $form.find("button").hide();
                }
                else {
                    $("#estateFileGridView").show();
                    $form.find("button").show();
                }
                */	
				
                $("#realEstateDetailThickbox").thickbox({
                    title: i18n.lms7500m01["L140M01T.dial.001"], //"不動產暨72-2相關資訊註記",
                    width: 800,//$("#_estateType").val() == "001" ? 800 : 200,
                    height: 400,//$("#_estateType").val() == "001" ? 400 : 150,
                    modal: true,
                    readOnly: _openerLockDoc == "1" || inits.toreadOnly ,
                    i18n: i18n.def,
                    buttons: buttons
                });
            }
        });
    },
    addRealEstate: function(){
    	var buttons = {};
    	buttons[i18n.def.newData] = function(){
			var $a0 = $("input[name='estateType']");
			var check = false;
			$a0.each(function(k, v){
				if(!($(this).attr("disabled"))&&!check){
					var isCheck = ($(this).attr("checked"));
					if(isCheck){
						check = true;
					}
				}
			});
			if(!check){
				return CommonAPI.showMessage(i18n.lms7500m01['checkSelect']);
			};
			if(!checkEstateType(et)){
				return CommonAPI.showMessage(i18n.lms7500m01['L140MM3A.message09']);
			};
            $.ajax({
                handler: inits.fhandle,
                action: "quickAddEstateDatas",
                data: $.extend($("#realEstateForm").serializeData(), {
                    mainId: responseJSON.mainId,//$("#mainId").val(),
                    isInstalment: $("input[name='isInstalment']:checked").val(),
					oid: responseJSON.oid
                }),
                success: function(obj){
					$("#realEstateFormDetail").injectData(obj);
                    $.thickbox.close();
                    RealEstateAction.afterGrid.trigger("reloadGrid");
                    $("input[name='is722Flag']").attr('checked', false);
                    $("input[name='is722Flag'][value='" + obj.is722Flag + "']").attr("checked", true);
					if(obj.warnMessage){
						CommonAPI.showMessage(obj.warnMessage);
					}
                }
            })
    	};
		buttons[i18n.def.close] = function(){
    		$.thickbox.close()
    	};
    	
    	$.ajax({
            handler: inits.fhandle,
            action: "getSelectedEstateType",
            data: {
            	mainId : $("#mainId").val()
            },
            success: function(obj){
            	$("input[name='estateType']").attr("disabled", false).attr("checked", false);
            	for(var tst in obj.estateType){
            		$("input[name='estateType'][value='" + obj.estateType[tst] + "']").attr("disabled", true).attr("checked", true);
            	}
            }
		}).done(function(){
			$("#realEstateThickbox").thickbox({
				title: i18n.lms7500m01["L140M01T.dial.001"], //"不動產暨72-2相關資訊註記",
	    		modal: false,
	    		width:600,
	    		height:500,
	    		open: function(){
	    			
	    		},
	    		buttons:buttons
	    	})
		})
    },
    changCityValue: function(tCity, tArea, tSite3, tSite4, defaultSelect, isClear){
        var value = tCity.val();
        var obj = QueryCityCode.getCode("2", value);

        tArea.setItems({
            item: obj,
            value: RealEstateAction.realEstateFormDetailData['estateAreaId'] || ""
        });
        
        if (isClear) {
            tSite3.html(defaultSelect);
            tSite4.html(defaultSelect);
            RealEstateAction.realEstateFormDetailData['estateAreaId'] = "";
            RealEstateAction.realEstateFormDetailData['estateSit3No'] = "";
            RealEstateAction.realEstateFormDetailData['estateSit4No'] = "";
        }
    },
    changAreaValue : function (tCity, tArea, tSite3, tSite4, defaultSelect, isClear){
        if (isClear) {
            
        }
        
        tSite3.setItems({
            item: RealEstateAction.getSITE3(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
            value: util.addZeroBefore($.trim(RealEstateAction.realEstateFormDetailData['estateSit3No']).replace(",", "") || "", 4)
        });
        
        tSite4.setItems({
            item: RealEstateAction.getSITE4(tCity.find(":selected").text(), tArea.find(":selected").text(), "N"),
            value:RealEstateAction.realEstateFormDetailData['estateSit4No'] || ""
        }); 
    },
    getSITE3: function(fCity, fZip, loanBuild){
        if (!fCity || !fZip) 
            return {};
        var SITE3 = null;
        $.ajax({
            handler: "lmscommonformhandler",
            action: "querySIET3",
            type: 'post',
            async: false,
            formId: "empty",
            data: {
                fCity: fCity,
                fZip: fZip
            },
            success: function(obj){
                if (loanBuild == "Y") {
                    SITE3NO = obj['SITE3NO'];
                }
                else {
                    SITE3 = obj['SITE3'];
                }
            }
        });
        return SITE3;
    },
	getSITE4: function(fCity, fZip, loanBuild){
        if (!fCity || !fZip) 
            return {};
        var SITE4 = null;
        $.ajax({
            handler: "lmscommonformhandler",
            action: "querySIT4NO",
            type: 'post',
            async: false,
            formId: "empty",
            data: {
                LOCATE1DESC: fCity,
                LOCATE2DESC: fZip
            },
            success: function(obj){
                if (loanBuild == "Y") {
                    SITE4NO = obj['SITE4NO'];
                }
                else {
                    SITE4 = obj['SITE4'];
                }
            }
        });
        return SITE4;
    },
	
	isShowFactoryBuyingOption: function(estateTypeCode){
		
		$("#factoryBuyingLabel").hide();
		if (estateTypeCode == "A0#") {
			$("#factoryBuyingLabel").show();
		}
	},
	
	switchCompletionDateAndExpFactoryNoObetainingDate: function(estateStatus, position){

		if(estateStatus == "4"){ //購置廠房
			$("#overDateLabel").hide();
			$("#expGetFactoryNoDateLabel").show();
		}
		else{
			$("#overDateLabel").show(); // 預計/完工年月
			$("#expGetFactoryNoDateLabel").hide();//預計取得工廠登記編號之日期
		}
	},
	    /*,
    uploadEstateFile:function(){
		var limitFileSize=3145728;
		MegaApi.uploadDialog({
			fieldId:"estateType" + $("#_estateType").val(),
            fieldIdHtml:"size='30'",
            fileDescId:"fileDesc",
            fileDescHtml:"size='30' maxlength='30'",
			subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
			limitSize:limitFileSize,
            width:320,
            height:190,
			data:{
				mainId:$("#mainId").val()
			},
			success : function(obj) {
				RealEstateAction.fileGrid.trigger("reloadGrid");
			}
	   });
    },
    deleteEstateFile:function(){
    	var select  = this.fileGrid.getGridParam('selrow');		
		// confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){				
				var data = RealEstateAction.fileGrid.getRowData(select);
				if(data.oid == "" || data.oid == undefined || data.oid == null){		
					// TMMDeleteError=請先選擇需修改(刪除)之資料列
					CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
					return;
				}				
				$.ajax({
					handler : inits.fhandle,
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteUploadFile",
						fileOid:data.oid
					},
					success : function(obj) {
						RealEstateAction.fileGrid.trigger("reloadGrid");
					}
				});
			}else{
				return ;
			}
		});
    }
    */
	isShowEstateStatusAndOverDateDontModifyMessage: function(){

		var overDate_common = $("#overDate_common").val();
		var before_overDate_common = $.trim($("#before_overDate_common").val());

		if (userInfo.unitNo != "918" && before_overDate_common != '' && overDate_common != before_overDate_common) {

			$('#overDate_common').val(before_overDate_common);
			
			var msg = " 1.興建程序無須修改，例如興建中改為已完工。<br/>" +
					  " 2.欲展延「預計取得所有權日」或「預計/完工年月」，須以變更條件另行簽報。";
					  
			CommonAPI.showErrorMessage(msg);
		}
	}
};

// 驗證readOnly狀態
function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    // auth.readOnly ||
    if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
        return true;
    }
    return false;
}

var et;
//import Data
function initData(){
	$.ajax({
	    handler: inits.fhandle,
	    data: {//把資料轉成json
	        formAction: "queryL140mm3a",
	        oid: responseJSON.oid,
			cntrNo: responseJSON.cntrNo,
			mainId: responseJSON.mainId,
			custId: responseJSON.custId,
			dupNo: responseJSON.dupNo,
			custName: responseJSON.custName
	    },
	    success: function(obj){
			$("#mainId").val(obj.mainId);
			$("#cntrNo").val(obj.cntrNo);
			
			if (obj.is722OnFlag == "") {
                $("[name='is722OnFlag'][value='" + $("[name='is722OnFlag']:radio:checked").val() + "']:radio").attr("checked", false);
            } else {
            	var is722OnFlag = $("[name='is722OnFlag'");
            	is722OnFlag.filter("[value='" + obj.is722OnFlag + "']:radio").attr("checked", "checked");
                //$("[name='is722OnFlag'][value='" + obj.is722OnFlag + "']:radio").attr("checked", "checked");
            }
			$("#is722QDate").val(obj.is722QDate);
			$("#is722CntrNo").val(obj.cntrNo);
			
			if (obj.cntrNoChkExistFlag == "") {
                $("[name='cntrNoChkExistFlag'][value='" + $("[name='cntrNoChkExistFlag']:radio:checked").val() + "']:radio").attr("checked", false);
            } else {
            	var cntrNoChkExistFlag = $("[name='cntrNoChkExistFlag'");
            	cntrNoChkExistFlag.filter("[value='" + obj.cntrNoChkExistFlag + "']:radio").attr("checked", "checked");
                //$("[name='cntrNoChkExistFlag'][value='" + obj.cntrNoChkExistFlag + "']:radio").attr("checked", "checked");
            }
			$("#cntrNoChkExistDate").val(obj.cntrNoChkExistDate);
			
			if (obj.isBuyOn == "") {
	            $("input[name='isBuyOn']").attr("checked", false).triggerHandler("change");
	        } else {
	        	var isBuyOn = $("input[name='isBuyOn']");
	        	isBuyOn.filter("[value='" + obj.isBuyOn + "']").attr("checked", true).triggerHandler("change");
	        	
	        	//isBuyOn.injectData(obj);
	        	
	            //$("input[name='isBuyOn'][value='" + obj.isBuyOn + "']").attr("checked", true).triggerHandler("change");
	        }
			
	        if (obj.isInstalmentOn == "") {
                $("input[name='isInstalmentOn']").attr("checked", false).triggerHandler("change");
            } else {
            	var isInstalmentOn = $("input[name='isInstalmentOn']");
            	isInstalmentOn.filter("[value='" + obj.isInstalmentOn + "']").attr("checked", true).triggerHandler("change");
                //$("input[name='isInstalmentOn'][value='" + obj.isInstalmentOn + "']").attr("checked", true).triggerHandler("change");
            } 
			
			if (obj.isBuy == "") {
	            $("input[name='isBuy']").attr("checked", false).triggerHandler("change");
	        } else {
	        	var isBuy = $("input[name='isBuy']");
	        	isBuy.filter("[value='" + obj.isBuy + "']").attr("checked", true).triggerHandler("change");
	            //$("input[name='isBuy'][value='" + obj.isBuy + "']").attr("checked", true).triggerHandler("change");
	        }
			if (obj.isInstalment == "") {
                $("input[name='isInstalment']").attr("checked", false).triggerHandler("change");
            } else {
            	var isInstalment = $("input[name='isInstalment']");
            	isInstalment.filter("[value='" + obj.isInstalment + "']").attr("checked", true).triggerHandler("change");
                //$("input[name='isInstalment'][value='" + obj.isInstalment + "']").attr("checked", true).triggerHandler("change");
            } 
			if (obj.is722Flag == "") {
                $("[name='is722Flag'][value='" + $("[name='is722Flag']:radio:checked").val() + "']:radio").attr("checked", false);
            } else {
            	var is722Flag = $("input[name='is722Flag']");
            	is722Flag.filter("[value='" + obj.is722Flag + "']").attr("checked", true).triggerHandler("change");
                //$("[name='is722Flag'][value='" + obj.is722Flag + "']:radio").attr("checked", "checked");
            }
			
			//J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
			if (obj.updateItem2 == "" || obj.updateItem2 == null || obj.updateItem2 == undefined) {
                $("[name='updateItem2'][value='Y']").attr("checked", false);
            } else {
                $("[name='updateItem2'][value='Y']").attr("checked", "checked");
            }
			
			if (obj.updateItem3 == "" || obj.updateItem3 == null || obj.updateItem3 == undefined) {
                $("[name='updateItem3'][value='Y']").attr("checked", false);
            } else {
                $("[name='updateItem3'][value='Y']").attr("checked", "checked");
            }
			
			//J-111-06XX_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			if (obj.updateItem4 == "" || obj.updateItem4 == null || obj.updateItem4 == undefined) {
                $("[name='updateItem4'][value='Y']").attr("checked", false);
            } else {
                $("[name='updateItem4'][value='Y']").attr("checked", "checked");
            }

			if (obj.updateItem5 == "" || obj.updateItem5 == null || obj.updateItem5 == undefined) {
                $("[name='updateItem5'][value='Y']").attr("checked", false);
            } else {
                $("[name='updateItem5'][value='Y']").attr("checked", "checked");
            }
			
			
			$('body').injectData(obj);
			adjust722UI();
			isLandUI();
			
			
			
			et = obj.etValue;
			
			//J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
			$("input[name='isBuyOn']").trigger('change');
			$("input[name='isInstalment']").trigger('change');
			$('input:checkbox[name="updateItem2"]').trigger('change');
			$('input:checkbox[name="updateItem3"]').trigger('change');
			//J-111-06XX_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			$('input:checkbox[name="updateItem4"]').trigger('change');
			
			$('input:checkbox[name="updateItem5"]').trigger('change');
			
			$("input[name='isSpecialFinRiskOn']").trigger('change');
		    $("#specialFinRiskTypeOn").trigger('change');
		    $("input[name='isSpecialFinRisk']").trigger('change');
		    $("#specialFinRiskType").trigger('change');
		    
		    //J-111-06XX_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
		    $("#lnType").trigger('change');
		    $("#lnTypeOn").trigger('change');
		    
			 
			if(obj.showSpecialFinRisk=="Y"){
				$('#isSpecialFinRiskShow').show();
				$('#isSpecialFinRiskShowOn').show();
			}else{
				$('#isSpecialFinRiskShow').hide();	
				$('#isSpecialFinRiskShowOn').hide();
			}

			//J-111-06XX_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			//N.A.、33、34 才可變更
			$("#lnType").find("option[value!='00'][value!='33'][value!='34']").attr('disabled', 'disabled');  
			 
			
	    }
	});
}

function adjust722UI(){
	var isBuyFlag = $("input[name='isBuy']:checked").val();
	if(isBuyFlag == "Y"){
		$("#isInstalmentView").show();
	} else {
		$("#isInstalmentView").hide();
	}
	var isInstalment = $("input[name='isInstalment']:checked").val();
	$("#realEstateAfterGridView")[isBuyFlag == "Y" ? "show":"hide"]();		
}

function isLandUI(){
	var lnType =  $("#lnType").val();
	if(lnType == "33" || lnType == "34"){
		$("#isLandView").show();
	} else {
		$("#adcCaseNo").val("");
		$("#isLandView").hide();
	}
	
	//J-111-06XX_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	var lnTypeOn =  $("#lnTypeOn").val();
	if(lnTypeOn == "33" || lnTypeOn == "34"){
		$("#isLandViewOn").show();
	} else {
		$("#isLandViewOn").hide();
	}
}

function checkEstateType(obj){
	var chk;
	var result = false;
	$("input[name='estateType']").each(function(){
		if($(this).attr("checked")){
			chk = $(this).val();
		}
    });
	$.each(obj, function(index, value) {
    	if(chk==value){
			result = true;
		}
	});	
	return result;
}

/** J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位 取得高品質最終結果 */
function getHighQualityResult(isold){
	if(isold){
		if($("input[name^='isHighQualityProjOpt_'][name$='On']:checked").length < 5){
			//沒填妥 放空值 
			$("#isHighQualityProjResultOn").val("");
			$("#div_hqProjResHighQualityProjectFinaceOn").hide();
			$("#div_hqProjResProjectFinaceOn").hide();
		}else if($("input[name^='isHighQualityProjOpt_'][name$='On'][value='Y']:checked").length == 5){
			//高品質融資選項5個都勾 >> 顯示「高品質專案融資」，適用80%風險權數
			$("#isHighQualityProjResultOn").val("Y");
			$("#div_hqProjResHighQualityProjectFinaceOn").show();
			$("#div_hqProjResProjectFinaceOn").hide();
		}else{//不屬於高品質專案融資，顯示「專案融資」，適用100%風險權數
			$("#isHighQualityProjResultOn").val("N");
			$("#div_hqProjResHighQualityProjectFinaceOn").hide();
			$("#div_hqProjResProjectFinaceOn").show();		
		}
	}else{
		if($("input[name^='isHighQualityProjOpt_']:checked").not("[name$='On']").length < 5){
			//沒填妥 放空值 
			$("#isHighQualityProjResult").val("");
			$("#div_hqProjResHighQualityProjectFinace").hide();
			$("#div_hqProjResProjectFinace").hide();
		}else if($("input[name^='isHighQualityProjOpt_'][value='Y']:checked").not("[name$='On']").length == 5){
			//高品質融資選項5個都勾 >> 顯示「高品質專案融資」，適用80%風險權數
			$("#isHighQualityProjResult").val("Y");
			$("#div_hqProjResHighQualityProjectFinace").show();
			$("#div_hqProjResProjectFinace").hide();
		}else{//不屬於高品質專案融資，顯示「專案融資」，適用100%風險權數
			$("#isHighQualityProjResult").val("N");
			$("#div_hqProjResHighQualityProjectFinace").hide();
			$("#div_hqProjResProjectFinace").show();		
		}
	}
}

var FinancingNotesAgreed = {
	
	isShowQ2Q7AttachedItem: 'N',
	initExceptFlagQA: function(){

	    $.ajax({
	        handler: 'lms7500m01formhandler',
	        action: "initExceptFlag",
	        data: {//把資料轉成json
	        	tabFormMainId: $("#tabFormMainId").val(),
	        	mainId: responseJSON.mainId
	        },
	        success: function(obj){

				FinancingNotesAgreed.isShowQ2Q7AttachedItem = obj.isShowQ2Q7AttachedItem;
	        	var $boxContex = $("#mainPanel");
	        	var exceptFlagQAisY = $boxContex.find("#exceptFlagQAisY").val();
	        	var exceptFlagQAPlus = $boxContex.find("#exceptFlagQAPlus").val();
				var inputDefaultName = "exceptFlagQ";
				FinancingNotesAgreed.processInitValue(inputDefaultName, exceptFlagQAisY, exceptFlagQAPlus, obj.isShowQ2Q7AttachedItem);
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr($boxContex.find("#exceptFlag").val());
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
				
				var exceptFlagQAisY = $boxContex.find("#exceptFlagQAisYOn").val();
	        	var exceptFlagQAPlus = $boxContex.find("#exceptFlagQAPlusOn").val();
				var inputDefaultName = "exceptFlagQOn";
	        	FinancingNotesAgreed.processInitValue(inputDefaultName, exceptFlagQAisY, exceptFlagQAPlus, obj.isShowQ2Q7AttachedItem);

	        	exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr($boxContex.find("#exceptFlagOn").val());
				FinancingNotesAgreed.setExceptFlagDesLastTime(exceptFlagDes);
	        }
	    });    
	},
	
	processInitValue: function (inputDefaultName, exceptFlagQAisY, exceptFlagQAPlus, isShowQ2Q7AttachedItem){

		var $boxContex = $("#mainPanel");
		if(exceptFlagQAisY != ""){
			for(var i = 1 ; i <= exceptFlagQAisY ; i++){
				inputName = inputDefaultName + i;

				$boxContex.find("#"+inputName+"Tr").show();
				$("input[name='"+ inputName +"YN'][value='N']").attr('checked',true);
				if(i == exceptFlagQAisY){
					$("input[name='"+ inputName +"YN'][value='Y']").attr('checked',true);
					
					//J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q2及Q7新增勾選項目
					if(isShowQ2Q7AttachedItem == "Y"){
						if( exceptFlagQAisY == "2" || exceptFlagQAisY == "7"){
							//第二或第七題勾是
							$boxContex.find("#"+inputName+"PlusTr").show();
							if( exceptFlagQAPlus == "Y"){
								$("input[name='"+ inputName +"PlusYN'][value='Y']").attr('checked',true);
							}
							if( exceptFlagQAPlus == "N"){
								$("input[name='"+ inputName +"PlusYN'][value='N']").attr('checked',true);
							}
						}else{
							$boxContex.find("#"+inputName+"PlusTr").hide();
						}
					}
					
				}
			}
		}

		if(isShowQ2Q7AttachedItem == 'Y'){
			$('#' + inputDefaultName + '2PlusNote').show();
			$('#' + inputDefaultName + '7PlusNote').show();
		}
		else{
			$('#' + inputDefaultName + '2PlusNote').hide();
			$('#' + inputDefaultName + '7PlusNote').hide();
		}
	},
	
	initExceptFlagQAEvent : function(){
		var $boxContex = $("#mainPanel");
		$boxContex.find("[name=exceptFlagQ1YN]:radio").click(function(){
			FinancingNotesAgreed.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ1YN]:radio:checked").val() == "Y") {
				FinancingNotesAgreed.setExceptFlagValue('_');//_:N.A.
				$("#exceptFlagQAisY").val("1");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("_");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
				FinancingNotesAgreed.resetExceptFlagQAItem(["2","3","4","5","6","7","8"]);
			}else{
				$boxContex.find("#exceptFlagQ2Tr").show();
				FinancingNotesAgreed.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ2YN]:radio").click(function(){
			FinancingNotesAgreed.resetExceptFlagQAPlusItem(["2","7"]);
			$boxContex.find("#exceptFlagQ2PlusTr").hide();
			if ($boxContex.find("[name=exceptFlagQ2YN]:radio:checked").val() == "Y") {
				FinancingNotesAgreed.resetExceptFlagQAItem(["3","4","5","6","7","8"]);
				FinancingNotesAgreed.cleanExceptFlagDes();
				$("#exceptFlagQAisY").val("2");
				if(FinancingNotesAgreed.isShowQ2Q7AttachedItem == 'Y'){
					$boxContex.find("#exceptFlagQ2PlusTr").show();
				}
				else{
					var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("Y");
					FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
					FinancingNotesAgreed.setExceptFlagValue('Y');	
				}
				
			}else{
				$boxContex.find("#exceptFlagQ3Tr").show();
				FinancingNotesAgreed.cleanExceptFlagDes();
				// J-112-0566 企金簽報書「額度明細表」中約定融資註記部分針對Q2新增勾選項目
			}
		});
		$boxContex.find("[name=exceptFlagQ3YN]:radio").click(function(){
			FinancingNotesAgreed.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ3YN]:radio:checked").val() == "Y") {
				FinancingNotesAgreed.resetExceptFlagQAItem(["4","5","6","7","8"]);
				FinancingNotesAgreed.setExceptFlagValue('N'); //N:不可取消融資額度
				$("#exceptFlagQAisY").val("3");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("N");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}else{
				$boxContex.find("#exceptFlagQ4Tr").show();
				FinancingNotesAgreed.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ4YN]:radio").click(function(){
			FinancingNotesAgreed.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ4YN]:radio:checked").val() == "Y") {
				FinancingNotesAgreed.resetExceptFlagQAItem(["5","6","7","8"]);
				FinancingNotesAgreed.setExceptFlagValue('N');//N:不可取消融資額度
				$("#exceptFlagQAisY").val("4");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("N");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}else{
				$boxContex.find("#exceptFlagQ5Tr").show();
				FinancingNotesAgreed.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ5YN]:radio").click(function(){
			FinancingNotesAgreed.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ5YN]:radio:checked").val() == "Y") {
				FinancingNotesAgreed.resetExceptFlagQAItem(["6","7","8"]);
				FinancingNotesAgreed.setExceptFlagValue('N');//N:不可取消融資額度
				$("#exceptFlagQAisY").val("5");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("N");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}else{
				$boxContex.find("#exceptFlagQ6Tr").show();
				FinancingNotesAgreed.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ6YN]:radio").click(function(){
			FinancingNotesAgreed.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ6YN]:radio:checked").val() == "Y") {
				FinancingNotesAgreed.resetExceptFlagQAItem(["7","8"]);
				FinancingNotesAgreed.setExceptFlagValue('N');//N:不可取消融資額度
				$("#exceptFlagQAisY").val("6");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("N");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}else{
				$boxContex.find("#exceptFlagQ7Tr").show();
				FinancingNotesAgreed.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ7YN]:radio").click(function(){

			FinancingNotesAgreed.resetExceptFlagQAPlusItem(["2","7"]);
			$boxContex.find("#exceptFlagQ7PlusTr").hide();
			if ($boxContex.find("[name=exceptFlagQ7YN]:radio:checked").val() == "Y") {
				FinancingNotesAgreed.resetExceptFlagQAItem(["8"]);
				FinancingNotesAgreed.cleanExceptFlagDes();
				$("#exceptFlagQAisY").val("7");
				
				if (FinancingNotesAgreed.isShowQ2Q7AttachedItem == 'Y') {
					$boxContex.find("#exceptFlagQ7PlusTr").show();
				}
				else{
					var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("C");
					FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
					FinancingNotesAgreed.setExceptFlagValue('C');			
				}
				
			}else{
				$boxContex.find("#exceptFlagQ8Tr").show();
				FinancingNotesAgreed.cleanExceptFlagDes();
			}
		});
		$boxContex.find("[name=exceptFlagQ8YN]:radio").click(function(){
			FinancingNotesAgreed.resetExceptFlagQAPlusItem(["2","7"]);
			if ($boxContex.find("[name=exceptFlagQ8YN]:radio:checked").val() == "Y") {
				FinancingNotesAgreed.setExceptFlagValue('N');//N:不可取消融資額度
				$("#exceptFlagQAisY").val("8");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("N");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}else{
				FinancingNotesAgreed.cleanExceptFlagDes();
			}
		});
		//J-112-0566企金簽報書「額度明細表」中約定融資註記部分針對Q2新增勾選項目
		$boxContex.find("[name=exceptFlagQ2PlusYN]:radio").click(function(){
			if ($boxContex.find("[name=exceptFlagQ2PlusYN]:radio:checked").val() == "Y") {
				//勾選無條件可取消融資額度且符合(a)(b)(c)要改下Z
				FinancingNotesAgreed.setExceptFlagValue('Z'); //Z:無條件可取消融資額度
				$("#exceptFlagQAPlus").val("Y");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("Z");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}else if($boxContex.find("[name=exceptFlagQ2PlusYN]:radio:checked").val() == "N"){
				FinancingNotesAgreed.setExceptFlagValue('Y');//Y:無條件可取消融資額度
				$("#exceptFlagQAPlus").val("N");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("Y");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}
		});
		//J-112-0566企金簽報書「額度明細表」中約定融資註記部分針對Q7新增勾選項目
		$boxContex.find("[name=exceptFlagQ7PlusYN]:radio").click(function(){
			if ($boxContex.find("[name=exceptFlagQ7PlusYN]:radio:checked").val() == "Y") {
				//勾選有條件可取消融資額度且符合(a)(b)(c)要改下D
				FinancingNotesAgreed.setExceptFlagValue('D');//D:有條件可取消融資額度
				$("#exceptFlagQAPlus").val("Y");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("D");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}else if($boxContex.find("[name=exceptFlagQ7PlusYN]:radio:checked").val() == "N"){
				FinancingNotesAgreed.setExceptFlagValue('C');//C:有條件可取消融資額度
				$("#exceptFlagQAPlus").val("N");
				var exceptFlagDes = FinancingNotesAgreed.getExceptFlagDescStr("C");
				FinancingNotesAgreed.setExceptFlagDesThisTime(exceptFlagDes);
			}
		});
	},
	
	resetExceptFlagQAItem : function(items){
		var $boxContex = $("#mainPanel");
		for(var i = 0 ; i < items.length ; i++ ){
			var inputName = "exceptFlagQ"+items[i];
			$boxContex.find("#"+inputName+"Tr").hide();
			$("input[name='"+ inputName +"YN'][value='Y']").attr('checked',false);
			$("input[name='"+ inputName +"YN'][value='N']").attr('checked',false);
		}
	},
	
	resetExceptFlagQAPlusItem : function(items){//
		var $boxContex = $("#mainPanel");
		for(var i = 0 ; i < items.length ; i++ ){
			var inputName = "exceptFlagQ"+items[i];
			$boxContex.find("#"+inputName+"PlusTr").hide();
			$("input[name='"+ inputName +"PlusYN'][value='Y']").attr('checked',false);
			$("input[name='"+ inputName +"PlusYN'][value='N']").attr('checked',false);
		}
		$("#exceptFlagQAPlus").val("");
	},
	
	cleanExceptFlagDes : function(){
		$("#exceptFlag").val("");
		$("#exceptFlagQAisY").val("");
		var exceptFlagDes = $("#exceptFlagDes");
		exceptFlagDes.injectData({'exceptFlagDes':''},false);
	},
	
	getExceptFlagDescStr : function(exceptFlag){

		var exceptFlagStr = "";
		switch (exceptFlag) {
        case "Y":
            // L140M01a.exceptFlag_Y=無條件可取消融資額度
        	exceptFlagStr = i18n.lms7500m01["L140M01a.exceptFlag_Y"]+i18n.lms7500m01["L140M01a.exceptFlag_Y_memo1"];
            break;
        case "Z":// J-112-0566 配合金管會修訂「銀行自有資本與風險性資產計算方法說明及表格修正重點」，新增代碼Z
            // L140M01a.exceptFlag_Z=無條件可取消融資額度
        	exceptFlagStr = i18n.lms7500m01["L140M01a.exceptFlag_Z"]+i18n.lms7500m01["L140M01a.exceptFlag_Z_memo1"];
            break;
        case "C":
        	// L140M01a.exceptFlag_C=有條件可取消融資額度
        	exceptFlagStr = i18n.lms7500m01["L140M01a.exceptFlag_C"]+i18n.lms7500m01["L140M01a.exceptFlag_C_memo1"];
        	break;
        case "D":// J-112-0566 配合金管會修訂「銀行自有資本與風險性資產計算方法說明及表格修正重點」，新增代碼D
        	// L140M01a.exceptFlag_D=有條件可取消融資額度
        	exceptFlagStr = i18n.lms7500m01["L140M01a.exceptFlag_D"]+i18n.lms7500m01["L140M01a.exceptFlag_D_memo1"];
        	break;
        case "N":
            // L140M01a.exceptFlag_N=不可取消融資額度
        	exceptFlagStr = i18n.lms7500m01["L140M01a.exceptFlag_N"]+i18n.lms7500m01["L140M01a.exceptFlag_N_memo1"];
            break;
        case "_":
        	exceptFlagStr = "N.A.";
            break;
        default:
            break;
		}

		return exceptFlagStr;
	},
	
	setExceptFlagDesLastTime: function(exceptFlagStr){
		$("#exceptFlagDesOn").injectData({'exceptFlagDesOn':exceptFlagStr},false);
	},
	
	setExceptFlagDesThisTime: function(exceptFlagStr){
		$("#exceptFlagDes").injectData({'exceptFlagDes':exceptFlagStr},false);
	},
	
	setExceptFlagValue: function(value){
		$('#exceptFlag').val(value);
	}
}

$(document).ready(function(){
	initData();
    if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
		_openerLockDoc="1";
    }
	RealEstateAction._init();
	$("#check1").show();

	var adcGrid = $("#queryAdcGridview").iGrid({
	    handler: 'lms7500gridhandler',
        height: 300,
        width: 350,
        autowidth: false,
        action: "queryAdcList",
        postData: {
        },
        rowNum: 15,
        sortname: "adcCaseNo",
        colModel: [{
            colHeader: i18n.lms7500m01["L140MM3A.adcCaseNo"],
            align: "center",
            name: 'adcCaseNo'
        }]
    });
	
	$("input[name='isBuy']").click(function(){
		var value = $(this).val();
		if(value == "N"){
			$("input[name='isInstalment']").attr("checked", false);
			$("input[name='is722Flag'][value='N']").attr("checked", true);			
			$.ajax({
				handler: inits.fhandle,
				action:"deleteCurrentL140mm3cs",
	            data: {	                
	                mainId: $("#mainId").val(),
					oid: responseJSON.oid
	            },
	            success: function(obj){
					
				}				
			});
		} else {
			$("input[name='is722Flag']").attr("checked", false);
			$("input[name='is722Flag'][value='Y']").attr("checked", true);			
			RealEstateAction.afterGrid.trigger("reloadGrid");
		}
		 adjust722UI();
	});
	$("input[name='isBuyOn']").change(function(){		

	});
	$("input[name='isInstalment']").change(function(){
		adjust722UI();
	});
	$(".isInstalmentClass,input[name='isInstalment']").mouseup(function(event){
		var isDisab = $("input[name='isInstalment']").attr("disabled");
		if(isDisab){
			return ;
		}
		
		// onmouseup可以取得Before的值
		var checkedCount = $("input[name='isInstalment']:checked").size();
		var gridCount = RealEstateAction.afterGrid.getGridParam("reccount")
		oldValue = $("input[name='isInstalment']:checked").val();
        var $this = $(this);
        var type = $this.attr("type");
        if (type == "radio") {
			//取消事件傳導，不然如果點擊到radio會執行2次
            event.stopPropagation();
        } else {
            $this = $this.children();
        }

        if (!$this.is(":checked") && checkedCount && gridCount) {
			//L140M01a.message210=異動此欄位將會刪除「不動產暨72-2相關資訊註記」，是否繼續？
            CommonAPI.confirmMessage(i18n.lms7500m01['L140MM3A.message04'], function(r){
                if (!r) {
                    $("input[name='isInstalment'][value=" + oldValue + "]").attr("checked", true)
                } else {
                    $.ajax({
                        handler: inits.fhandle,
                        action: "deleteCurrentL140mm3cs",
                        data: {
                            mainId: $("#mainId").val(),
							oid: responseJSON.oid
                        },
                        success: function(obj){
                            RealEstateAction.afterGrid.trigger("reloadGrid");
                        }
                    });                    
                }
            });
        }        
	});
	
	$("input[name='exItem']").click(function(){			
		 adjust722UI();
	});
	
	$("#cancelExItem").click(function(){
		$("input[name='exItem']").attr("checked", false);
		adjust722UI();
	});
	
	/**
     * 引進帳務資訊72-2
     */
    $("#applyOnLine772").click(function(){
       //初始化    		   
	   $.ajax({
            async: false,
            handler: inits.fhandle,
            data: {
                formAction: "queryOnLine772Flag",
                mainId: $("#mainId").val(),
                cntrNo: $("#cntrNo").val(),
				oid: responseJSON.oid
            },
            success: function(obj){
				
                //如果是被退回修改的案件，那麼資料不重引，只更新查詢日期
                if (obj.isOldCase && obj.isOldCase == "Y") {
                	$("#is722QDate").val(obj.is722QDate);
                }
                else {
                    if (obj.is722OnFlag == "") {
                        $("[name='is722OnFlag'][value='" + $("[name='is722OnFlag']:radio:checked").val() + "']:radio").attr("checked", false);
                    }
                    else {
                        $("[name='is722OnFlag'][value='" + obj.is722OnFlag + "']:radio").attr("checked", "checked");
                    }
                    
                    $("#is722CntrNo").val(obj.is722CntrNo);
                    $("#is722QDate").val(obj.is722QDate);
                    
                    if (obj.cntrNoChkExistFlag == "") {
                        $("[name='cntrNoChkExistFlag'][value='" + $("[name='cntrNoChkExistFlag']:radio:checked").val() + "']:radio").attr("checked", false);
                    }
                    else {
                        $("[name='cntrNoChkExistFlag'][value='" + obj.cntrNoChkExistFlag + "']:radio").attr("checked", "checked");
                    }
                    $("#cntrNoChkExistDate").val(obj.cntrNoChkExistDate);
                    
                    if (obj.isBuyOn == "") {
                        $("input[name='isBuyOn']").attr("checked", false).triggerHandler("change");
                    }
                    else {
                    	var isBuyOn = $("input[name='isBuyOn']");
                    	isBuyOn.filter("[value='" + obj.isBuyOn + "']").attr("checked", true).triggerHandler("change");
                        //$("input[name='isBuyOn'][value='" + obj.isBuyOn + "']").attr("checked", true).triggerHandler("change");
                    }
					
					if (obj.isInstalmentOn == "") {
                        $("input[name='isInstalmentOn']").attr("checked", false).triggerHandler("change");
                    }
                    else {
                    	var isInstalmentOn = $("input[name='isInstalmentOn']");
                    	isInstalmentOn.filter("[value='" + obj.isInstalmentOn + "']").attr("checked", true).triggerHandler("change");     	            
                    	//$("input[name='isInstalmentOn'][value='" + obj.isInstalmentOn + "']").attr("checked", true).triggerHandler("change");
                    }      
                }
                
                RealEstateAction.beforeGrid.trigger("reloadGrid") 
            }
        });
    });
	
//	$("input[name='isBuy']:checked").triggerHandler("click");
//	$("input[name='isBuyOn']:checked").triggerHandler("change");
	
	// 呈主管覆核 選授信主管人數
    $("#numPerson").change(function(){
        $('#bossItem').empty();
        var value = $(this).val();
        if (value) {
            var html = '';
            for (var i = 1; i <= value; i++) {
                var name = 'boss' + i;
                html += i + '. '
                // || '授信主管'
                html += '<select id="' + name + '" name="boss"' +
                '" class="required" CommonManager="kind:2;type:2" />';
                html += '<br/>';
            }
            $('#bossItem').append(html).find('select').each(function(){
                $(this).setItems({
                    item: item,
                    format: "{value} {key}"
                });
            });
        }     
    });

    $("#newAdcCaseNoBt").click(function(){
        $("#newAdcCaseNoBox").thickbox({
            title: i18n.lms7500m01["L140MM3A.adcCaseNo"],
            width: 650,
            height: 300,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var type = $("input[name=newAdcCaseNoRadio]:checked").val();
                    var adcCaseNo = $("#adcCaseNo").val();
                    if ($.trim(adcCaseNo) != "" && type != "del") {
                        return CommonAPI.showErrorMessage(i18n.lms7500m01["L140MM3A.message14"]);
                    }
                    if(type == "new") {
                        $.ajax({
                            handler: inits.fhandle,
                            action: "queryNewAdcCaseNo",
                            data: {
                                oid: responseJSON.oid
                            },
                            success: function(obj){
                                $("#adcCaseNo").val(obj.adcCaseNo);
                                $.thickbox.close();
                            }
                        });
                    } else if(type == "org") {
                        var orgAdcCaseNo = $("#orgAdcCaseNo").val();
                        if ($.trim(orgAdcCaseNo) == "") {
                            return CommonAPI.showErrorMessage(i18n.lms7500m01["checkInput"]);
                        }
                        $.ajax({
                            handler: inits.fhandle,
                            action: "chkAdcCaseNo",
                            data: {
                                adcCaseNo: orgAdcCaseNo,
                                oid: responseJSON.oid,
                                save: true
                            },
                            success: function(obj){
                                if (obj.msg && obj.msg != "") {
                                    return API.showErrorMessage(obj.msg);
                                } else {
                                    $("#adcCaseNo").val(orgAdcCaseNo);
                                    $.thickbox.close();
                                }
                            }
                        });
                    } else if(type == "del") {
                        $("#adcCaseNo").val('');
                        $.thickbox.close();
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    

    $("#queryAdcCaseNoBt").click(function(){
        $("#queryCustId").val($("#custId").val());
        $("#queryDupNo").val($("#dupNo").val());
        $("#queryAdcCaseNoBox").thickbox({
            title: i18n.lms7500m01["L140MM3A.adcCaseNo"],
            width: 30,
            height: 100,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            buttons: {
                "sure": function(){
                    var queryType = $("input[name=queryType]:checked").val();
                    if(queryType == "1") {
                        var queryCustId = $("#queryCustId").val();
                        var queryDupNo = $("#queryDupNo").val();
                        if ($.trim(queryDupNo) == "") {
                            queryDupNo = "0";
                        }
                        if ($.trim(queryCustId) == "") {
                            return CommonAPI.showErrorMessage(i18n.lms7500m01["checkInput"]);
                        }
                    } else if(queryType == "2") {
                        var queryCntrNo = $("#queryCntrNo").val();
                        if ($.trim(queryCntrNo) == "") {
                            return CommonAPI.showErrorMessage(i18n.lms7500m01["checkInput"]);
                        }
                    }
                    $.thickbox.close();
                    adcGrid.jqGrid("setGridParam", {
                        postData: {
                            queryType: queryType,
                            queryCustId: queryCustId,
                            queryDupNo: queryDupNo,
                            queryCntrNo: queryCntrNo
                        },
                        search: true
                    }).trigger("reloadGrid");
                    $("#queryAdcThickBox").thickbox({
                       title: i18n.lms7500m01["checkSelect"]+i18n.lms7500m01["L140MM3A.adcCaseNo"],
                       width: 400,
                       height: 450,
                       align: "center",
                       valign: "bottom",
                       modal: false,
                       i18n: i18n.def,
                       buttons: {
                            "sure": function(){
                                var row = adcGrid.getGridParam('selrow');
                                if (!row) {
                                    return CommonAPI.showMessage(i18n.def["grid_selector"]);
                                }
                                if (row.length > 1) {
                                    CommonAPI.showMessage(i18n.lms7500m01["L140MM3A.error1"]);
                                } else {
                                    CommonAPI.confirmMessage(i18n.lms7500m01["L140MM3A.message15"], function(b){
                                        if (b) {
                                            var adcCaseNo = $("#adcCaseNo").val();
                                            if ($.trim(adcCaseNo) != "") {
                                                return CommonAPI.showErrorMessage(i18n.lms7500m01["L140MM3A.message14"]);
                                            }
                                            var data = adcGrid.getRowData(row);
                                            $("#adcCaseNo").val(data.adcCaseNo);
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            },
                            "close": function(){
                                $.thickbox.close();
                            }
                        }
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });

    $("input[name=newAdcCaseNoRadio]").click(function(){
        if ("org" == $(this).val()) {
            $("#originalInput").show();
        } else {
            $("#originalInput").hide();
            $("#orgAdcCaseNo").val('');
        }
    });

    $("input[name=queryType]").click(function(){
        var queryType = $(this).val();
        $(".queryInput").hide();
        $("#queryInput_" + queryType).show();
    });
	
	var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
//		if (userInfo.unitType != "2" && userInfo.unitType != "4") {
//			//分行報案時儲存才清，已經到授管處、營運中心者不清，免得額度明細表與額度批覆書內容不一致
//		    var isBuy= $("input[name='isBuy']:radio:checked" ).val(); 
//			if(!isBuy){  
//			    $("input[name='is722Flag']:radio:checked").each(function(){
//				     $(this).removeAttr("checked");
//				});
//			}
//		}
        saveData(true);
    }).end().find("#btnSend").click(function(){
//        saveData(false, sendBoss);
        saveData(false, confirmMsg);
    }).end().find("#btnCheck").click(function(){
        openCheck();
    }).end().find("#btnPrint").click(function(){
        if (checkReadonly()) {
            printAction();
        }
        else {
            // saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作?
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    saveData(false, printAction);
                }
            });
        }
    });
    
    //J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
    $('input:checkbox[name="updateItem2"]').change(function(){	
    	var value = $('input:checkbox:checked[name="updateItem2"]').val();
    	if(value == "Y"){
    		$("#tab2").show();
    	}else{
    		$("#tab2").hide();
    	} 
	}).trigger('change');
    
    //J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
    $('input:checkbox[name="updateItem3"]').change(function(){	
    	var value = $('input:checkbox:checked[name="updateItem3"]').val();
    	if(value == "Y"){
    		$("#tab3").show();
    	}else{
    		$("#tab3").hide();
    	} 
	}).trigger('change');
    
    $('input:checkbox[name="updateItem4"]').change(function(){	
    	var value = $('input:checkbox:checked[name="updateItem4"]').val();
    	if(value == "Y"){
    		$("#tab4").show();
    	}else{
    		$("#tab4").hide();
    	} 
	}).trigger('change');
	
	 $('input:checkbox[name="updateItem5"]').change(function(){	
    	var value = $('input:checkbox:checked[name="updateItem5"]').val();
    	if(value == "Y"){
    		$("#tab5").show();
    	}else{
    		$("#tab5").hide();
    	} 
	}).trigger('change');
    
    //J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
    $("input[name='isSpecialFinRiskOn']").change(function(){
        var value = $("input[name=isSpecialFinRiskOn]:checked").val();
        if (value == "Y") {
            $("#isSpecialFinRiskSpanOn").show();
             
        } else {
            $("#isSpecialFinRiskSpanOn").find("select").val("");
            $("#isSpecialFinRiskSpanOn").hide();
        }
    }).trigger('change');
    
    
    // J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
    $("#specialFinRiskTypeOn").change(function(){
        var value = $("#specialFinRiskTypeOn").val();
        if (value == "1") {
        	//專案融資
            $("#isProjectFinOperateStagSpanOn").show();                        
        } else {
        	$("input[name='isProjectFinOperateStagOn']" ).removeAttr('checked');   //塞值
            $("#isProjectFinOperateStagSpanOn").hide();
        }
        $("input[name='isProjectFinOperateStagOn']" ).triggerHandler("change");
    }).trigger('change');
    
    //J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位
    $("input[name='isProjectFinOperateStagOn']").change(function(){
    	//專案融資是否屬營運階段>選擇是時才顯示"高品質融資"相關選項
    	if( $("input[name='isProjectFinOperateStagOn']:checked").val() == "Y"){
    		$("#isHqProjSpanOn").show();
    	}else{
    		$("input[name='isHighQualityProjOpt_1On']").removeAttr('checked');//勾選拿掉
    		$("input[name='isHighQualityProjOpt_2On']").removeAttr('checked');//勾選拿掉
    		$("input[name='isHighQualityProjOpt_3On']").removeAttr('checked');//勾選拿掉
    		$("input[name='isHighQualityProjOpt_4On']").removeAttr('checked');//勾選拿掉
    		$("input[name='isHighQualityProjOpt_5On']").removeAttr('checked');//勾選拿掉
    		$("#isHighQualityProjResultOn").val("");//最終結果清空
            $("#isHqProjSpanOn").hide();       
    	}
    	//取得高品質最終結果
    	getHighQualityResult(true);
    });
    
    $("input[name^='isHighQualityProjOpt_'][name$='On']").change(function(){
    	//取得高品質最終結果
    	getHighQualityResult(true);
    });
    
    
    //J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
    $("input[name='isSpecialFinRisk']").change(function(){
        var value = $("input[name=isSpecialFinRisk]:checked").val();
        if (value == "Y") {
            $("#isSpecialFinRiskSpan").show();
             
        } else {
            $("div#isSpecialFinRiskSpan").find("select").val("");
            $("#isSpecialFinRiskSpan").hide();
        }
        $("#specialFinRiskType").trigger('change');
    }).trigger('change');
    
    
    // J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
    $("#specialFinRiskType").change(function(){
        var value = $("#specialFinRiskType").val();
        if (value == "1") {
        	//專案融資
            $("#isProjectFinOperateStagSpan").show();                        
        } else {
        	$("input[name='isProjectFinOperateStag']" ).removeAttr('checked');   //塞值
            $("#isProjectFinOperateStagSpan").hide();
        }
        $("input[name='isProjectFinOperateStag']" ).triggerHandler("change");
    }).trigger('change');
    
    //J-112-0417 e-Loan簽報書新增高品質專案融資判斷欄位
    $("input[name='isProjectFinOperateStag']").change(function(){
    	//專案融資是否屬營運階段>選擇是時才顯示"高品質融資"相關選項
    	if( $("input[name='isProjectFinOperateStag']:checked").val() == "Y"){
    		$("#isHqProjSpan").show();	
    	}else{
    		$("input[name='isHighQualityProjOpt_1']").removeAttr('checked');//勾選拿掉
    		$("input[name='isHighQualityProjOpt_2']").removeAttr('checked');//勾選拿掉
    		$("input[name='isHighQualityProjOpt_3']").removeAttr('checked');//勾選拿掉
    		$("input[name='isHighQualityProjOpt_4']").removeAttr('checked');//勾選拿掉
    		$("input[name='isHighQualityProjOpt_5']").removeAttr('checked');//勾選拿掉
    		$("#isHighQualityProjResult").val("");//最終結果清空
            $("#isHqProjSpan").hide();       
    	}
    	//取得高品質最終結果
    	getHighQualityResult(false);
    });
    
    $("input[name^='isHighQualityProjOpt_']").not("[name$='On']").change(function(){
    	//取得高品質最終結果
    	getHighQualityResult(false);
    });
    
    //J-111-06XX_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
    $("#lnType").change(function(){
    	isLandUI();
    }).trigger('change');
    
    //J-111-06XX_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
    $("#lnTypeOn").change(function(){
    	isLandUI();
    }).trigger('change');
    
    
    //EVENT END***********************************************************************************************
	
	// 儲存的動作
    function saveData(showMsg, tofn){
		
		// 為檢查UI的值是否皆無異常
		if ($("#mainPanel").valid() == false) {
			return;
		}
		
		var allresult = {};
		var localresult = {};
		var selfresult = {};
		FormAction.open = true;
		$.ajax({
			handler: inits.fhandle,
			data: {// 把資料轉成json
				formAction: "saveL140mm3a",
				oid: responseJSON.oid,
				page: responseJSON.page,
				txCode: responseJSON.txCode,
				showMsg: showMsg
			},
			success: function(obj){
//				$("input[name='isBuy']:checked").triggerHandler("click");
//				$("input[name='isBuyOn']:checked").triggerHandler("change");
				
				if (responseJSON.page == "01") {
					$('body').injectData(obj);
				}		
				
				CommonAPI.triggerOpener("gridview", "reloadGrid");
				if ($("#mainOid").val()) {
					setRequiredSave(false);
				}
				else {
					setRequiredSave(true);
				}

                // 執行列印
                if (!showMsg && tofn) {
                    tofn();
                }
			}
		});
    }
	
	var item;
	// 呈主管前 提示訊息
	function confirmMsg(){
	    var confirmMsg = getConfirmMsg();
        if (confirmMsg != null) {
            CommonAPI.confirmMessage(confirmMsg, function(b){
                if (b) {
                    sendBoss();
                } else {
                    return;
                }
            });
        } else {
            sendBoss();
        }
	}
	// 呈主管 - 編製中
    function sendBoss(){
        $.ajax({
            handler: inits.fhandle,
            action: "checkData",
            data: {},
            success: function(json){
                $('#managerItem').empty();
                $('#bossItem').empty();
                item = json.bossList;
                var bhtml = '1. <select id="boss1" name="boss" class="required" CommonManager="kind:2;type:2"/>';
                $('#bossItem').append(bhtml).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                var html = '<select id="manager" name="manager" class="required" CommonManager="kind:2;type:2" />';
                $('#managerItem').append(html).find('select').each(function(){
                    $(this).setItems({
                        item: item,
                        format: "{value} {key}"
                    });
                });
                
                // L140MM1B.message27=是否呈主管覆核？
                CommonAPI.confirmMessage(i18n.lms7500m01["L140MM3B.message01"], function(b){
                    if (b) {
                        $("#selectBossBox").thickbox({
                            // L140MM1B.bt14=覆核
                            title: i18n.lms7500m01['L140MM1B.bt14'],
                            width: 500,
                            height: 300,
                            modal: true,
                            readOnly: false,
                            valign: "bottom",
                            align: "center",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                
                                    var selectBoss = $("select[name^=boss]").map(function(){
                                        return $(this).val();
                                    }).toArray();
                                    
                                    for (var i in selectBoss) {
                                        if (selectBoss[i] == "") {
                                            // L140MM1B.error2=請選擇
                                            // L140MM1B.bossId=授信主管
                                            return CommonAPI.showErrorMessage(i18n.lms7500m01['checkSelect'] +
                                            i18n.lms7500m01['L140MM3B.bossId']);
                                        }
                                    }
                                    if ($("#manager").val() == "") {
                                        // L140MM1B.error2=請選擇
                                        // L140MM1B.managerId=經副襄理
                                        return CommonAPI.showErrorMessage(i18n.lms7500m01['checkSelect'] +
                                        i18n.lms7500m01['L140MM3B.managerId']);
                                    }
                                    // 驗證是否有重複的主管
                                    if (checkArrayRepeat(selectBoss)) {
                                        // L140MM1B.message31=主管人員名單重複請重新選擇
                                        return CommonAPI.showErrorMessage(i18n.lms7500m01['L140MM3B.message02']);
                                    }
                                    
                                    flowAction({
                                        page: responseJSON.page,
                                        saveData: true,
                                        selectBoss: selectBoss,
                                        manager: $("#manager").val()
                                    });
                                    $.thickbox.close();
                                    
                                },
                                
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                });
            }
        });
    }
	
	// 待覆核 - 覆核
    function openCheck(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            // L140MM1B.bt14=覆核
            title: i18n.lms7500m01['L140MM1B.bt14'],
            width: 100,
            height: 100,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        // L140MM1B.error2=請選擇
                        return CommonAPI.showMessage(i18n.lms7500m01['checkSelect']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            // 一般退回到編製中01O
                            // L140MM1B.message32=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.lms7500m01['L140MM3B.message03'], function(b){
                                if (b) {
                                    flowAction({
                                        flowAction: false
                                    });
                                }
                            }); 
                            break;
                        case "3":
                            // L140MM1B.message34=該案件是否確定執行核定作業
                            CommonAPI.confirmMessage(i18n.lms7500m01['L140MM3B.message04'], function(b){
                                if (b) {
                                    //checkDate();
				                    flowAction({
				                        flowAction: true,
				                        checkDate: CommonAPI.getToday()//forCheckDate
				                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
	
	function flowAction(sendData){
        $.ajax({
            handler: inits.fhandle,
            data: $.extend({
                formAction: "flowAction",
                mainOid: $("#mainOid").val()
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
				window.close();
            }
        });
    }
	
	// 輸入核定日期視窗
    function checkDate(){
        // 帶入今天日期
        $("#forCheckDate").val(CommonAPI.getToday());
        $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
            // L140MM1B.message38 = 請輸入核定日
            title: i18n.lms7500m01['L140MM3B.message05'],
            width: 100,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
            readOnly: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var forCheckDate = $("#forCheckDate").val();
                    if ($.trim(forCheckDate) == "") {
                        // L140MM1B.message38 = 請輸入核定日
                        return CommonAPI.showErrorMessage(i18n.lms7500m01['L140MM3B.message05']);
                    }
                    flowAction({
                        flowAction: true,
                        checkDate: forCheckDate
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
	
	// 列印動作
    function printAction(){
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                mainOid: responseJSON.oid,
                fileDownloadName: "lms7500r01.pdf",
                serviceName: "lms7500r01rptservice"
            }
        });
    }
	
	// 檢查陣列內容是否重複
    function checkArrayRepeat(arrVal){
        var newArray = [];
        for (var i = arrVal.length; i--;) {
            var val = arrVal[i];
            if ($.inArray(val, newArray) == -1) {
                newArray.push(val);
            }
            else {
                return true;
            }
        }
        return false;
    }

    function getConfirmMsg(){
        var result = null;
        $.ajax({
            type: "POST",
            handler: inits.fhandle,
            async: false,
            data: {
                formAction: "getConfirmMsg",
                mainOid: $("#mainOid").val()
            },
            success: function(responseData){
                var confirmMsg = responseData.confirmMsg;
                if (confirmMsg != undefined && confirmMsg != null && confirmMsg != "") {
                    result = confirmMsg;
                }
            }
        });
        return result;
    }
    
	FinancingNotesAgreed.initExceptFlagQA();
    FinancingNotesAgreed.initExceptFlagQAEvent();
    
});