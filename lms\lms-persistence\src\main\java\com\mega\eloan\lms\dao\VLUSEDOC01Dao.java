/* 
 * VLUSEDOC01Dao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.VLUSEDOC01;

/** 動用審核表取得額度序號 **/
public interface VLUSEDOC01Dao extends IGenericDao<VLUSEDOC01> {
	/**
	 * 取得可動用的額度序號
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @param useBrId
	 *            登錄分行
	 * @param itemType
	 *            額度明細表種類 1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return
	 */
	public List<VLUSEDOC01> findUseCntrNo(String caseMainId, String[] useBrId,
			String itemType);

	/**
	 * 取得可動用的額度序號
	 * 
	 * @param caseMainId
	 *            簽報書mainid
	 * @param mainIds
	 *            額度明細表 manId
	 * @param useBrId
	 *            登錄分行
	 * @param itemType
	 *            額度明細表種類 1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return
	 */
	public List<VLUSEDOC01> findUseCntrNo(String caseMainId, String[] mainIds,
			String[] useBrId, String itemType);

	List<VLUSEDOC01> findByCustIdDupId(String custId, String DupNo);

}