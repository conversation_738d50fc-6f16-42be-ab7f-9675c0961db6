package com.mega.eloan.lms.obsdb.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;

/**
 * <pre>
 * 海外貸後管理	ELF601-控制檔 ELF602-紀錄檔
 * </pre>
 * 
 * @since 2021/8/
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/8/,009301,new
 *          </ul>
 */
public interface ObsdbELF601Service {

	Map<String, Object> findByIdDupNo(String BRNID, String custId, String dupNo);

	List<Map<String, Object>> queryElf602List(ISearch pageSetting,String BRNID);

	List<ELF601> getElf601ByCntrNoLoanNo(String BRNID, String cntrNo, String loanNo);

	List<ELF602> getElf602ByCntrNoLoanNo(String BRNID, String cntrNo, String loanNo,
		 	boolean undone);

	List<ELF601> getElf601ByFilter(String custId, String dupNo, String BRNID,
		   String cntrNo, String loanNo, String status);

	List<ELF602> getElf602ByFilter(String custId, String dupNo, String BRNID,
		   String cntrNo, String loanNo, String startDate, String endDate, String[] handlingStatus);

    List<ELF601> getElf601OnlyById(String custId, String dupNo, String brNo);

    List<ELF601> getElf601OnlyIdByFilter(String custId, String dupNo, String status, String brNo);

    List<ELF602> getElf602OnlyById(String custId, String dupNo, boolean undone, String brNo);

    List<ELF602> getElf602OnlyIdByFilter(String custId, String dupNo,
             String startDate, String endDate, String[] handlingStatus, String brNo);

	ELF601 getElf601ByUnid(String BRNID, String unid);

	ELF602 getElf602ByUnid(String BRNID, String unid);

	List<ELF601> getElf601ByUnidLike(String BRNID, String unid);

	List<ELF602> getElf602ByDatasrcLike(String BRNID, String datasrc);
	
	List<ELF602> getElf602ByDatasrc(String BRNID, String datasrc);
	
	void deleteELF601(String BRNID, String unid);

	void deleteELF602(String BRNID, String unid);

    void deleteELF602ByFoDateLikeUnid(String BRNID, Date foDate,String likeUnid);

    void insertELF601(String BRNID, ELF601 elf601);

	void insertELF602(String BRNID, ELF602 elf602);

	void updateELF601Status(String brNo, String unid, String status);
	
	void updateELF602StatusAndMemo(String brNo, String unid, String status, String memo);

    void insertElf602(List<ELF602> overseaElf602List);
}
