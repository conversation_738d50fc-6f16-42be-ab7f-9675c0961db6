---------------------------------------------------------
-- LMS.L999S01B 綜合授信契約書借款種類檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999S01B;
CREATE TABLE LMS.L999S01B (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	ITEMTY<PERSON><PERSON>      CHAR(1)       not null,
	ITEM01        VARCHAR(20)  ,
	ITEM02        CHAR(1)      ,
	ITEM03<PERSON>R<PERSON>    CHAR(3),
	ITEM03        DECIMAL(15,0),
	ITEM03<PERSON>IT    DECIMAL(13,0),
	ITEM04        VARCHAR(768) ,
	ITEM05        DECIMAL(3,0) ,
	ITEM06        DECIMAL(3,0) ,
	ITEM07        DECIMAL(3,0) ,
	ITEM08        DECIMAL(3,0) ,
	ITEM09        DECIMAL(3,0) ,
	ITEM10        DECIMAL(3,0) ,
	ITEM11        VARCHAR(768) ,
	ITEM12        VARCHAR(768) ,
	ITEM13        VARCHAR(768) ,
	ITEM14        VARCHAR(768) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L999S01B PRIMARY KEY(OID)
) IN  EL_DATA_8KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL999S01B01;
CREATE UNIQUE INDEX LMS.XL999S01B01 ON LMS.L999S01B   (MAINID, ITEMTYPE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999S01B IS '綜合授信契約書借款種類檔';
COMMENT ON LMS.L999S01B (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	ITEMTYPE      IS '借款種類', 
	ITEM01        IS '透支支票存款帳號', 
	ITEM02        IS '是否為循環額度', 
	ITEM03CURR    IS '借款額度幣別', 
	ITEM03        IS '借款額度', 
	ITEM03UNIT    IS '借款額度單位', 
	ITEM04        IS '利息計付/手續費計付', 
	ITEM05        IS '清償期限/保證期限/承兌期限(天數)', 
	ITEM06        IS '清償期限(天數)', 
	ITEM07        IS '清償期限(天數)', 
	ITEM08        IS '清償期限(天數)', 
	ITEM09        IS '動用之方式及條件(成數)', 
	ITEM10        IS '動用之方式及條件(天數)', 
	ITEM11        IS '利息計付(計息標準)/清償期限/委任保證範圍', 
	ITEM12        IS '利息計付(計息標準)/動用之方式及條件/委任保證方式', 
	ITEM13        IS '利息計付(繳息方式)', 
	ITEM14        IS '利息計付(匯票承兌)', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);





--2013/07/24,Rex,新增借款額度描述
ALTER TABLE LMS.L999S01B ADD COLUMN ITEM15 VARCHAR(1536);
COMMENT ON LMS.L999S01B (ITEM15 IS '借款額度文字描述'); 
REORG table LMS.L999S01B ;


