package com.mega.eloan.lms.fms.handler.form;

import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.lms.base.service.CLSService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls9081formhandler")
public class CLS9081Form<PERSON>andler extends AbstractFormHandler {

	@Resource
	CLSService clsService;
	
	@DomainAuth(AuthType.Query)
	public IResult loadData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Date date = CapDate.addMonth(CapDate.getCurrentTimestamp(), -1);
		result.set("yyyy_MM", Util.trim(StringUtils.substring(TWNDate.toAD(date), 0, 7)));
		return result;
	}
}
