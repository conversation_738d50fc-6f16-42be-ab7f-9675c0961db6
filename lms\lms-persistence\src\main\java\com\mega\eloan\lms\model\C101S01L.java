/* 
 * C101S01L.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金相關查詢利害關係人檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01L", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "xType", "xCustId", "xDupNo", "qCustId", "qDupNo" }))
public class C101S01L extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 關係種類
	 * <p/>
	 * 1.銀行法<br/>
	 * 2.金控法44條<br/>
	 * 3.金控法45條
	 */
	@Size(max = 1)
	@Column(name = "XTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String xType;

	/**
	 * 利害關係人身分證統編
	 * <p/>
	 * (MIS.ELREMAIN.REID)
	 */
	@Size(max = 10)
	@Column(name = "XCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String xCustId;

	/**
	 * 利害關係人身分證統編重複碼
	 * <p/>
	 * (MIS.ELREMAIN.DUPNO)
	 */
	@Size(max = 1)
	@Column(name = "XDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String xDupNo;

	/**
	 * 利害關係人姓名
	 * <p/>
	 * (MIS.ELREMAIN.REIDNM)
	 */
	@Size(max = 120)
	@Column(name = "XCUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String xCustName;

	/**
	 * 關係層級
	 * <p/>
	 * 0.無<br/>
	 * ※1.銀行法<br/>
	 * 1.本行利害關係人<br/>
	 * (MIS.ELREMAIN)<br/>
	 * 2.本行有利害關係人<br/>
	 * (MIS.ELRESECD)<br/>
	 * 3.本行有利害關係企業<br/>
	 * (MIS.ELRESCOM)<br/>
	 * ※2.金控法44條<br/>
	 * ※3.金控法45條<br/>
	 * 4.金控利害關係人
	 */
	@Size(max = 1)
	@Column(name = "RELVL", length = 1, columnDefinition = "CHAR(1)")
	private String relvl;

	/**
	 * 銀行法控管對象別(MIS.ELREMAIN.RECTL)
	 * ※1.銀行法<br/>
	 * 1.本行首長及董監事<br/>
	 * 2.本行國內負責人或辦理授信人員<br/>
	 * 3.本行國外負責人或辦理授信人員<br/>
	 * 4.本行職員<br/>
	 * 5.本行轉投資事業(含信託持股)<br/>
	 * 6.主要股東<br/>
	 * 7.本行轉投資事業投資3％以下，且本行擔任該 投資事業之法人董監事及經理人(含信託持股)<br/>
	 * 8.本行實質關係人<br/>
	 */
	@Size(max = 1)
	@Column(name = "RECTL", length = 1, columnDefinition = "CHAR(1)")
	private String rectl;

	/**
	 * 授信影響等級
	 * <p/>
	 * ※1.銀行法<br/>
	 * L.所在分行<br/>
	 * G.全行<br/>
	 * (MIS.ELREMAIN.RELCD)
	 */
	@Size(max = 1)
	@Column(name = "RELCD", length = 1, columnDefinition = "CHAR(1)")
	private String relcd;

	/**
	 * 金控法控管對象別 (關係人原因)
	 * <p/>
	 * ※2.金控法44條<br/>
	 * ※2.金控法45條<br/>
	 * (MIS.ELREX45.MAGA_MEMO)
	 */
	@Size(max = 162)
	@Column(name = "MAGAMEMO", length = 162, columnDefinition = "VARCHAR(162)")
	private String magaMemo;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 查詢之身分證統編(本人/配偶)
	 */
	@Size(max = 10)
	@Column(name = "QCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String qCustId;

	/**
	 * 查詢之身分證統編重複碼(本人/配偶)
	 */
	@Size(max = 1)
	@Column(name = "QDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String qDupNo;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得關係種類
	 * <p/>
	 * 1.銀行法<br/>
	 * 2.金控法44條<br/>
	 * 3.金控法45條
	 */
	public String getXType() {
		return this.xType;
	}

	/**
	 * 設定關係種類
	 * <p/>
	 * 1.銀行法<br/>
	 * 2.金控法44條<br/>
	 * 3.金控法45條
	 **/
	public void setXType(String value) {
		this.xType = value;
	}

	/**
	 * 取得利害關係人身分證統編
	 * <p/>
	 * (MIS.ELREMAIN.REID)
	 */
	public String getXCustId() {
		return this.xCustId;
	}

	/**
	 * 設定利害關係人身分證統編
	 * <p/>
	 * (MIS.ELREMAIN.REID)
	 **/
	public void setXCustId(String value) {
		this.xCustId = value;
	}

	/**
	 * 取得利害關係人身分證統編重複碼
	 * <p/>
	 * (MIS.ELREMAIN.DUPNO)
	 */
	public String getXDupNo() {
		return this.xDupNo;
	}

	/**
	 * 設定利害關係人身分證統編重複碼
	 * <p/>
	 * (MIS.ELREMAIN.DUPNO)
	 **/
	public void setXDupNo(String value) {
		this.xDupNo = value;
	}

	/**
	 * 取得利害關係人姓名
	 * <p/>
	 * (MIS.ELREMAIN.REIDNM)
	 */
	public String getXCustName() {
		return this.xCustName;
	}

	/**
	 * 設定利害關係人姓名
	 * <p/>
	 * (MIS.ELREMAIN.REIDNM)
	 **/
	public void setXCustName(String value) {
		this.xCustName = value;
	}

	/**
	 * 取得關係層級
	 * <p/>
	 * 0.無<br/>
	 * ※1.銀行法<br/>
	 * 1.本行利害關係人<br/>
	 * (MIS.ELREMAIN)<br/>
	 * 2.本行有利害關係人<br/>
	 * (MIS.ELRESECD)<br/>
	 * 3.本行有利害關係企業<br/>
	 * (MIS.ELRESCOM)<br/>
	 * ※2.金控法44條<br/>
	 * ※3.金控法45條<br/>
	 * 4.金控利害關係人
	 */
	public String getRelvl() {
		return this.relvl;
	}

	/**
	 * 設定關係層級
	 * <p/>
	 * 0.無<br/>
	 * ※1.銀行法<br/>
	 * 1.本行利害關係人<br/>
	 * (MIS.ELREMAIN)<br/>
	 * 2.本行有利害關係人<br/>
	 * (MIS.ELRESECD)<br/>
	 * 3.本行有利害關係企業<br/>
	 * (MIS.ELRESCOM)<br/>
	 * ※2.金控法44條<br/>
	 * ※3.金控法45條<br/>
	 * 4.金控利害關係人
	 **/
	public void setRelvl(String value) {
		this.relvl = value;
	}

	/** 取得銀行法控管對象別 **/
	public String getRectl() {
		return this.rectl;
	}

	/** 設定銀行法控管對象別 **/
	public void setRectl(String value) {
		this.rectl = value;
	}

	/**
	 * 取得授信影響等級
	 * <p/>
	 * ※1.銀行法<br/>
	 * L.所在分行<br/>
	 * G.全行<br/>
	 * (MIS.ELREMAIN.RELCD)
	 */
	public String getRelcd() {
		return this.relcd;
	}

	/**
	 * 設定授信影響等級
	 * <p/>
	 * ※1.銀行法<br/>
	 * L.所在分行<br/>
	 * G.全行<br/>
	 * (MIS.ELREMAIN.RELCD)
	 **/
	public void setRelcd(String value) {
		this.relcd = value;
	}

	/**
	 * 取得金控法控管對象別 (關係人原因)
	 * <p/>
	 * ※2.金控法44條<br/>
	 * ※2.金控法45條<br/>
	 * (MIS.ELREX45.MAGA_MEMO)
	 */
	public String getMagaMemo() {
		return this.magaMemo;
	}

	/**
	 * 設定金控法控管對象別 (關係人原因)
	 * <p/>
	 * ※2.金控法44條<br/>
	 * ※2.金控法45條<br/>
	 * (MIS.ELREX45.MAGA_MEMO)
	 **/
	public void setMagaMemo(String value) {
		this.magaMemo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得查詢之身分證統編(本人/配偶) **/
	public String getQCustId() {
		return this.qCustId;
	}

	/** 設定查詢之身分證統編(本人/配偶) **/
	public void setQCustId(String value) {
		this.qCustId = value;
	}

	/** 取得查詢之身分證統編重複碼(本人/配偶) **/
	public String getQDupNo() {
		return this.qDupNo;
	}

	/** 設定查詢之身分證統編重複碼(本人/配偶) **/
	public void setQDupNo(String value) {
		this.qDupNo = value;
	}
}
