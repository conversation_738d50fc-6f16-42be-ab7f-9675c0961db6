<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<script type="text/javascript">
				loadScript('pagejs/crs/LMS2401M01Page');
			</script>
            <div class="button-menu funcContainer" id="buttonPanel">
            	<!-- ===================================== -->
            	<th:block th:if="${_btnSave_visible}">
					<button type="button" id="btnSave">
						<span class="ui-icon ui-icon-jcs-04" ></span>
						<th:block th:text="#{'button.save'}">儲存</th:block>
					</button>
				</th:block>
            	<!-- ===================================== -->
            	<th:block th:if="${_btnDOC_EDITING_visible}">
					<button type="button" id="btnSend">
						<span class="ui-icon ui-icon-jcs-02" ></span>
						<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
					</button>
				</th:block>
				<!-- ===================================== -->
				<th:block th:if="${_btnWAIT_APPROVE_visible}">
					<button type="button" id="btnAccept">
						<span class="ui-icon ui-icon-check" ></span>
						<th:block th:text="#{'button.check'}">覆核</th:block>
					</button>					
				</th:block>
				<!-- ===================================== -->
				<button type="button" id="btnModifyDate">
					<span class="ui-icon ui-icon-jcs-15" ></span>
					<th:block th:text="#{'button.ExceptRetrialDate'}">修改預計覆審日</th:block>
				</button>
				<!-- ===================================== -->
				<th:block th:if="${_btnApply_visible}">
					<button type="button" id="btnSendRetrialReport">
				    	<span class="ui-icon ui-icon-jcs-216" ></span>
						<th:block th:text="#{'button.SendRetrialReport'}">傳送分行覆審報告表</th:block>
					</button>
				</th:block>
				<!-- ===================================== -->
				<th:block th:if="${_btnSend_visible}">
					<button type="button" id="btnRetrialEvaluation">				    	
						<th:block th:text="#{'button.RetrialEvaluation'}">覆審考核表</th:block>
					</button>
					<button type="button" id="btnSendBtt">				    	
						<th:block th:text="#{'button.SendBtt'}">重新傳送覆審名單至BTT</th:block>
					</button>
				</th:block>
				<!-- ===================================== -->				
				<button type="button" id="btnNckdFlagO">					
					<th:block th:text="#{'button.ChkThenSetNckdFlagO'}">調整重覆產生名單</th:block>
				</button>
				<button type="button" id="btnAddNotShowCust">					
					<th:block th:text="#{'button.AddNotShowCust'}">補抓名單</th:block>
				</button>
				<button type="button" id="btnExit" class="forview">
					<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>
            </div>
			
			<div class="tit2 color-black">
                <table width="100%">
                    <tr>
                        <td width="100%">
                            <th:block th:text="#{'doc.title'}">授信消金案件覆審明細表</th:block>
							<span class="color-blue" id="titleInfo" ></span>
                        </td>
                    </tr>
                </table>
            </div>
			<div class="tabs doc-tabs">
                <ul>
                    <li id="tab01"><a href="#tab-01" goto="01"><b><th:block th:text="#{'tab.01'}">文件資訊</th:block></b></a></li>
                    <li id="tab02"><a href="#tab-02" goto="02"><b><th:block th:text="#{'tab.02'}">分行覆審名單資料</th:block></b></a></li>                    
                </ul>
                <div class="tabCtx-warp">
                    <form id="tabForm">
						<div th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
                    </form>
                </div>
            </div>
			
			<div id="_div_btnModifyDate" style="display: none">
				<form id="_div_btnModifyDate_form">
				<table>
					<tr><td>
						<b><th:block th:text="#{'ui_lms2401.msg03'}">請輸入預計覆審日</th:block>：</b>
						&nbsp;<input type="text" size="8" maxlength="10" class="required date" _requiredLength="10" id="defaultCTLDate" name="defaultCTLDate" />
						<span class="text-red">(YYYY-MM-DD)</span>
					</td></tr>
				</table>
				</form>
			</div>
			
			<div id="_div_btnEscrow1" style="display: none">
				<form id="_div_btnEscrow1_form">
				<table>
					<tr><td>
						<b><th:block th:text="#{'ui_lms2401.msg09'}">請輸入欲產生之價金履約保證覆審案件的保證起始日</th:block>：</b>
						&nbsp;<input type="text" size="8" maxlength="10" class="required date" _requiredLength="10" id="escrowSDate" name="escrowSDate" />
						<span class="text-red">(YYYY-MM-DD)</span>
					</td></tr>
				</table>
				</form>
			</div>
			
			<div id="_div_btnEscrowComIdList" style="display: none">
				<div id="gridEscrowComIdList"></div>
			</div>
			
			<div id="_div_btnEscrowList" style="display: none">
				<div id="gridEscrowList"></div>
			</div>
			
			<div id="_div_btnNckdFlagO" style="display: none">
				<div id="gridNckdFlagO">
				</div>
			</div>
			
			<div id="_div_btnEscrowMenu4" style="display: none">
				<form id="_div_btnEscrowMenu4_form">
				<table>
					<tr><td>
						<b><th:block th:text="#{'C241M01B.lcNo'}">保證編號</th:block>：</b>
						&nbsp;<input type="text" size="14" maxlength="20" class="required " id="escrowMenu4_lcNo" name="escrowMenu4_lcNo" />
					</td></tr>
				</table>
				</form>
			</div>
			
			<div id="_div_DocKindS_R1toR5" style="display: none">
				<div id="gridDocKindS_R1toR5"></div>
			</div>
			
			<div id="_div_DocKindS_R6" style="display: none">
				<div id="gridDocKindS_R6"></div>
			</div>
			
			<div id="_div_DocKindS_empNo" style="display: none">
				<div id="gridDocKindS_empNo"></div>
			</div>
			
			<div id="_div_detail_Rule95_1" style="display: none">
				<div id="gridRule95_1"></div>
			</div>
			
			<div id="_div_detail_R1R2S" style="display: none">
				<div>
				本次欲抽樣戶數： <input type="text" size="3" id="samplingCnt_R1R2S" name="samplingCnt_R1R2S" style="text-align:right;" class="numeric required" integer="3"/>戶
					<!--
					既有的抽樣邏輯，會用總筆數*比率，去計算出{抽樣件數}
					但是，不動產十足擔保的年度抽樣比率不得低於各該營業單位前一年度承作該類授信戶數之10%，不足二戶應至少覆審二戶
					=> 若是新成立的分行，前1年度只有0件，就算用抽樣 100%，算出的{抽樣件數=0}
					===> 若直接讓 覆審人員 輸入 戶數，會比輸入 抽樣比率 再換算，會較合適
					
					
					本次欲抽樣 <input type="text" size="3" id="samplingRate_R1R2S" name="samplingRate_R1R2S" style="text-align:right;" class="numeric required" integer="3"/>％
					-->
				</div>
			</div>
			
			<div id="_div_detail_R14" style="display: none">
				<div>
				本次欲抽樣戶數： <input type="text" size="3" id="samplingCnt_R14" name="samplingCnt_R14" style="text-align:right;" class="numeric required" integer="3"/>戶
					<!--
					既有的抽樣邏輯，會用總筆數*比率，去計算出{抽樣件數}
					但是，不動產十足擔保的年度抽樣比率不得低於各該營業單位前一年度承作該類授信戶數之10%，不足二戶應至少覆審二戶
					=> 若是新成立的分行，前1年度只有0件，就算用抽樣 100%，算出的{抽樣件數=0}
					===> 若直接讓 覆審人員 輸入 戶數，會比輸入 抽樣比率 再換算，會較合適
					
					
					本次欲抽樣 <input type="text" size="3" id="samplingRate_R1R2S" name="samplingRate_R1R2S" style="text-align:right;" class="numeric required" integer="3"/>％
					-->
				</div>
			</div>
			
			<div id="_div_detail_projectCreditLoan" style="display: none">
				<div id="statInfoProjectCreditLoan"></div>
				<form id='div_detail_projectCreditLoan_form'>
					<table class='tb2' width='95%' >
						<tr style='vertical-align:top; '>
					  		<td class='hd2' width='20%' nowrap>「專案信貸」撥款日</td>				 
					  		<td width='80%'>
					  			<input type="text" size="8" maxlength="10" class="required date" _requiredLength="10" id="projectCreditLoanSDate" name="projectCreditLoanSDate" />
								~
								<input type="text" size="8" maxlength="10" class="required date" _requiredLength="10" id="projectCreditLoanEDate" name="projectCreditLoanEDate" />
								&nbsp;&nbsp;&nbsp;&nbsp;
								<button type="button" id='filter_gridProjectCreditLoanBtn'>
									<span class='text-only'><th:block th:text="#{'button.search'}"></th:block></span>
								</button>
								<div>※ 依指定「撥款起迄日」查詢後的結果，「不含」已覆審過的「專案信貸」案件
								</div>
					  		</td>						
						</tr>						
					</table>
				</form>
				<div id="gridProjectCreditLoan"></div>
			</div>
			<div th:insert="~{base/panels/RetrialPtMgrIdPanel :: RetrialPtMgrIdPanel}"></div>
        </th:block>
    </body>
</html>

