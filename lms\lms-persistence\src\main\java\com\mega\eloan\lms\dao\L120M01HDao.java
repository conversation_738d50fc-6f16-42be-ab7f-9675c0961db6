/* 
 * L120M01HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01H;

/** 授審會／催收會會議決議檔 **/
public interface L120M01HDao extends IGenericDao<L120M01H> {

	L120M01H findByOid(String oid);
	
	List<L120M01H> findByMainId(String mainId);
	
	L120M01H findByUniqueKey(String mainId, String meetingType);

	List<L120M01H> findByIndex01(String mainId, String meetingType);
}