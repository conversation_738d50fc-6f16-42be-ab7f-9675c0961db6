package com.mega.eloan.lms.rpt.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.Casedoc;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L180R02ADao;
import com.mega.eloan.lms.dao.LMSRPTDao;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L180R02A;
import com.mega.eloan.lms.model.LMSRPT;
import com.mega.eloan.lms.rpt.pages.LMS9511V01Page;
import com.mega.eloan.lms.rpt.service.LMS9511Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生營運中心授信已敘做PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms9511r05rptservice")
public class LMS9511R05RptServiceImpl implements FileDownloadService {

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	BranchService branchService;

	@Resource
	L180R02ADao l180r02aDao;

	@Resource
	LMSRPTDao lmsRptDao;

	@Resource
	LMS9511Service service9511;

	@Resource
	LMSService lmsService;

	@Resource
	LMS1201Service service1201;

	@Resource
	LMS1405Service service1405;

	@Resource
	LMS1401Service service1401;

	@Resource
	BranchService branch;

	@Resource
	L140S02ADao l140s02aDao;

	@Resource
	C120M01ADao c120m01aDao;

	@Resource
	L140S01ADao l140s01aDao;

	@Resource
	LMS1205Service service1205;

	/**
	 * 產生LMS1205R03的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public OutputStream genLMS1205R02A(String brNo, LMSRPT lmsRpt,
			List<L180R02A> l180r02aList, String docType, String docKind,
			String showPrintNote, Map<String, String> docTypeMap,
			Map<String, String> docKindMap, Locale locale, Properties prop)
			throws FileNotFoundException, IOException, Exception {
		OutputStream outputStream = null;
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		ReportGenerator generator = null;
		if ("Y".equals(showPrintNote)) {
			generator = new ReportGenerator("report/rpt/LMS9511R02A02_"
					+ locale.toString() + ".rpt");
		} else {
			generator = new ReportGenerator("report/rpt/LMS9511R02A01_"
					+ locale.toString() + ".rpt");
		}
		try {
			titleRows = this.setL180R02AListData(titleRows, l180r02aList,
					locale);
			rptVariableMap = this.setLMSRPTData(rptVariableMap, lmsRpt, brNo,
					prop);
			rptVariableMap.put("LMS9511R02A.DOCTYPE",
					Util.trim(docTypeMap.get(docType)));
			rptVariableMap.put("LMS9511R02A.DOCKIND",
					Util.trim(docKindMap.get(docKind)));
			BigDecimal totalTWD = BigDecimal.ZERO;
			BigDecimal totalUSD = BigDecimal.ZERO;
			BigDecimal otherToUSDAmt = null;
			BranchRate rate = null;
			for (L180R02A l180r02a : l180r02aList) {
				if ("A".equals(l180r02a.getNoUseAmt())) {
					if ("TWD".equals(l180r02a.getAsCurr())) {
						totalTWD = LMSUtil.addTotal(totalTWD, l180r02a
								.getAsAmt() == null ? BigDecimal.ZERO
								: l180r02a.getAsAmt());
					} else if ("USD".equals(l180r02a.getAsCurr())) {
						totalUSD = LMSUtil.addTotal(totalUSD, l180r02a
								.getAsAmt() == null ? BigDecimal.ZERO
								: l180r02a.getAsAmt());
					} else {
						rate = lmsService.getBranchRate(l180r02a.getBrno());
						if (Util.isEmpty(Util.trim(l180r02a.getAsCurr()))) {

						} else {
							String curr = l180r02a.getAsCurr();
							BigDecimal big = l180r02a.getAsAmt();
							otherToUSDAmt = rate.toUSDAmt(curr, big);
							totalUSD = LMSUtil
									.addTotal(totalUSD, otherToUSDAmt);
						}

					}
				} else if ("D".equals(l180r02a.getNoUseAmt())) {
					if ("TWD".equals(l180r02a.getAsCurr())) {
						totalTWD = LMSUtil.subTotal(totalTWD,
								l180r02a.getAsAmt());
					} else if ("USD".equals(l180r02a.getAsCurr())) {
						totalUSD = LMSUtil.subTotal(totalUSD,
								l180r02a.getAsAmt());
					} else {
						rate = lmsService.getBranchRate(l180r02a.getBrno());
						otherToUSDAmt = rate.toUSDAmt(l180r02a.getAsCurr(),
								l180r02a.getAsAmt());
						totalUSD = LMSUtil.subTotal(totalUSD, otherToUSDAmt);
					}
				}
			}
			rptVariableMap.put("LMS9511R02A.TOTALTWD",
					NumConverter.addComma(totalTWD));
			rptVariableMap.put("LMS9511R02A.TOTALUSD",
					NumConverter.addComma(totalUSD));
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (titleRows != null) {
				titleRows.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param dataCollection
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setL180R02AListData(
			List<Map<String, String>> titleRows, List<L180R02A> l180r02aList,
			Locale locale) throws CapException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		try {

			Map<String, String> mapInTitleRows = null;
			for (L180R02A l180r02a : l180r02aList) {
				String noUse = Util.trim(l180r02a.getNoUseAmt());

				mapInTitleRows = Util.setColumnMap();
				mapInTitleRows.put("ReportBean.column01",
						TWNDate.toAD(l180r02a.getCaseDate()));
				mapInTitleRows.put(
						"ReportBean.column02",
						Util.trim(l180r02a.getCustId())
								+ Util.trim(l180r02a.getDupNo()));
				mapInTitleRows.put("ReportBean.column03",
						Util.trim(l180r02a.getCustName()));
				mapInTitleRows.put("ReportBean.column04",
						Util.trim(l180r02a.getCntrNo()));
				mapInTitleRows.put("ReportBean.column05",
						Util.trim(l180r02a.getLnSubject()));
				if ("N".equals(noUse)) {
					mapInTitleRows
							.put("ReportBean.column06",
									"＊"
											+ Util.trim(l180r02a.getAsCurr())
											+ " "
											+ NumConverter.addComma(l180r02a
													.getAsAmt()));
				} else if ("A".equals(noUse)) {
					mapInTitleRows
							.put("ReportBean.column06",
									Util.trim(l180r02a.getAsCurr())
											+ " "
											+ NumConverter.addComma(l180r02a
													.getAsAmt()));
				} else if ("D".equals(noUse)) {
					mapInTitleRows
							.put("ReportBean.column06",
									Util.trim(l180r02a.getAsCurr())
											+ " "
											+ "-"
											+ NumConverter.addComma(l180r02a
													.getAsAmt()));
				} else if ("X".equals(noUse)) {
					mapInTitleRows
							.put("ReportBean.column06",
									"＊"
											+ Util.trim(l180r02a.getAsCurr())
											+ " "
											+ NumConverter.addComma(l180r02a
													.getAsAmt()));
				} else if ("Y".equals(noUse)) {
					mapInTitleRows
							.put("ReportBean.column06",
									"＊"
											+ Util.trim(l180r02a.getAsCurr())
											+ " "
											+ "-"
											+ NumConverter.addComma(l180r02a
													.getAsAmt()));
				}
				mapInTitleRows.put(
						"ReportBean.column07",
						Util.trim(l180r02a.getLV2Curr()) + " "
								+ NumConverter.addComma(l180r02a.getLV2Amt()));
				mapInTitleRows.put("ReportBean.column08",
						Util.trim(l180r02a.getDesp1()));
				mapInTitleRows.put("ReportBean.column09",
						LMSUtil.getProPerty(l180r02a.getProperty(), prop));
				mapInTitleRows.put("ReportBean.column10",
						Util.trim(l180r02a.getCaseNo()));
				mapInTitleRows.put("ReportBean.column11",
						TWNDate.toAD(l180r02a.getHqCheckDate()));
				mapInTitleRows.put("ReportBean.column12",
						Util.trim(l180r02a.getHqCheckMemo()));

				// J-109-0085_10702_B1001 Web
				// e-Loan已敘做授信案件清單及區域授信管理中心授權內外已核准/已婉卻授信案件報表，增加企、消金信用評等資料修改
				String L120M01A_mainId = l180r02a.getL120M01A_MainId();
				String L140M01A_mainId = l180r02a.getL140M01A_MainId();
				String column13 = null;
				if (Util.isNotEmpty(L120M01A_mainId)
						&& Util.isNotEmpty(L140M01A_mainId)) {
					L120M01A l120m01a = service1201
							.findL120m01aByMainId(L120M01A_mainId);
					L140M01A l140m01a = service1405
							.findL140m01aByMainId(L140M01A_mainId);
					String docType = l120m01a.getDocType();
					// 企金
					if (Util.nullToSpace(docType).equals("1")) {
						// L120S01A．借款人主檔
						List<L120S01A> l120s01aList = service1201
								.findL120s01aByMainIdForOrder(L120M01A_mainId);

						if (l140m01a == null) {
							l140m01a = new L140M01A();
						}
						List<L120S01C> l120s01cList = l120s01cList = service1201
								.findL120s01cByMainId(L120M01A_mainId);
						L120S01A l120s01aFor140 = null;
						for (L120S01A l120s01a : l120s01aList) {
							if (Util.nullToSpace(l120s01a.getCustId()).equals(
									Util.nullToSpace(l140m01a.getCustId()))
									&& Util.nullToSpace(l120s01a.getCustId())
											.equals(Util.nullToSpace(l140m01a
													.getCustId()))) {
								l120s01aFor140 = l120s01a;
							}
						}
						if (l120s01aFor140 == null)
							l120s01aFor140 = new L120S01A();
						Map<String, String> crdTypeMap = codetypeservice
								.findByCodeType("CRDType", locale.toString());
						column13 = this.setL120S01CData(l120s01aFor140,
								l120s01cList, crdTypeMap, prop, l140m01a);
					}
					// 消金
					else {
						// 判斷是否為海外
						if (LMSUtil.isOverSea_CLS(l120m01a)) {
							List<L120S01A> l120s01aList = service1205
									.findL120s01aByMainIdForOrder(L120M01A_mainId);
							L120S01A l120s01aFor140 = null;
							for (L120S01A l120s01a : l120s01aList) {
								if (Util.nullToSpace(l120s01a.getCustId())
										.equals(Util.nullToSpace(l140m01a
												.getCustId()))
										&& Util.nullToSpace(l120s01a.getDupNo())
												.equals(Util
														.nullToSpace(l140m01a
																.getDupNo()))) {
									l120s01aFor140 = l120s01a;
								}
							}
							if (l120s01aFor140 == null)
								l120s01aFor140 = new L120S01A();
							column13 = this.set_cls_gradeData(l120m01a,
									l140m01a, prop);
						} else {
							Map<String, String> c120m01aMap = new HashMap<String, String>();
							Map<String, String> l140s01aMap = new HashMap<String, String>();
							List<L140S02A> l140s02as = l140s02aDao
									.findByMainId(L140M01A_mainId);
							this._c102m01aAndl140s01aMap(c120m01aMap,
									l140s01aMap, l120m01a, l140m01a);
							if (l140s02as.size() > 1) {
								column13 = Util.trim(shortModelKind(prop,
										l120m01a, l140m01a, l140s02as,
										c120m01aMap, l140s01aMap));
							} else {
								// 當額度明細表,只有1個產品時,執行此段程式
								L140S02A l140s02a = l140s02as.get(0);
								column13 = showModelKind(prop, l120m01a,
										l140m01a, l140s02a, c120m01aMap,
										l140s01aMap);
							}
						}
					}
				}
				mapInTitleRows.put("ReportBean.column13", column13);
				titleRows.add(mapInTitleRows);
			}
			if (l180r02aList.isEmpty()) {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} finally {

		}
		return titleRows;
	}

	/**
	 * 塞值 RptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param dataCollection2
	 * @return
	 * @throws CapException
	 */
	private Map<String, String> setLMSRPTData(
			Map<String, String> rptVariableMap, LMSRPT lmsRpt, String brNo,
			Properties prop) throws CapException {
		try {
			String[] date = TWNDate.toAD(lmsRpt.getDataDate()).split("-");
			if (date != null && date.length > 2) {
				rptVariableMap.put(
						"LMS9511R02A.dateYM",
						date[0] + prop.getProperty("LMS9511X09.number03")
								+ date[1]
								+ prop.getProperty("LMS9511X09.number04"));
			} else {
				rptVariableMap.put("LMS9511R02A.dateYM", "");
			}
			rptVariableMap.put("LMS9511R02A.brNo",
					Util.trim(branchService.getBranchName(brNo)));
			rptVariableMap.put("LMS9511R02A.RANDOMCODE",
					Util.trim(lmsRpt.getRandomCode()));

		} finally {

		}
		return rptVariableMap;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.ReportService#generateReport(
	 */
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS9511V01Page.class);
		Locale locale = null;
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		// 先確認是該列印哪張報表
		String areaBranchId = Util.trim(params.getString("areaBranchId"));
		String printNote = Util.trim(params.getString("printNote"));
		String[] choickBranchs = params.getStringArray("choickBranchs");
		String[] oids = params.getStringArray("oids");
		String printType = Util.trim(params.getString("printType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docType = Util.trim(params.getString("docType"));
		String mainId = Util.trim(params.getString("mainId"));
		Map<String, String> docTypeMap = null;
		Map<String, String> docKindMap = null;
		LMSRPT lmsRpt = null;
		List<L180R02A> l180r02aList = null;
		Map<String, List<L180R02A>> printBranchMap = new LinkedHashMap<String, List<L180R02A>>();
		OutputStream outputStream = null;
		int subLine = 8;
		Properties propEloanPage = null;
		boolean vaResult = false;
		try {
			// 確認是直式還是橫式列印
			if ("Y".equals(printNote)) {
				vaResult = true;
			} else {
				vaResult = false;
			}
			vaResult = false;
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			docTypeMap = codetypeservice.findByCodeType("L120M01A_docType");
			docKindMap = codetypeservice.findByCodeType("L120M01A_docKind");
			if (docTypeMap == null) {
				docTypeMap = new LinkedHashMap<String, String>();
			}
			if (docKindMap == null) {
				docKindMap = new LinkedHashMap<String, String>();
			}
			locale = LMSUtil.getLocale();
			lmsRpt = lmsRptDao.findByIndex03(mainId);
			if (lmsRpt == null)
				lmsRpt = new LMSRPT();
			if ("1".equals(printType)) {
				l180r02aList = l180r02aDao.findByIndex01(mainId, docType,
						docKind, null, null);
			} else if ("2".equals(printType)) {
				l180r02aList = l180r02aDao.findByIndex03(oids, mainId, docType,
						docKind);
			} else if ("3".equals(printType)) {
				l180r02aList = l180r02aDao.findByIndex01(mainId, docType,
						docKind, null, choickBranchs);
			}
			if (l180r02aList != null && !l180r02aList.isEmpty()) {
				for (L180R02A l180r02a : l180r02aList) {
					if (printBranchMap.containsKey(l180r02a.getBrno())) {
						List<L180R02A> l180r02aList2 = printBranchMap
								.get(l180r02a.getBrno());
						l180r02aList2.add(l180r02a);
						printBranchMap.put(l180r02a.getBrno(), l180r02aList2);
					} else {
						List<L180R02A> l180r02aList2 = new LinkedList<L180R02A>();
						l180r02aList2.add(l180r02a);
						printBranchMap.put(l180r02a.getBrno(), l180r02aList2);
					}
				}
			}

			if ("1".equals(printType)) {
				List<IBranch> allBranchs = branchService
						.getBranchOfGroup(areaBranchId);

				for (IBranch ibranch : allBranchs) {
					String brNo = ibranch.getBrNo();
					if (printBranchMap.containsKey(brNo)) {
						List<L180R02A> l180r02aList2 = printBranchMap.get(brNo);
						if (l180r02aList2 == null) {
							l180r02aList2 = new LinkedList<L180R02A>();
						}
						outputStream = this.genLMS1205R02A(brNo, lmsRpt,
								l180r02aList2, docType, docKind, printNote,
								docTypeMap, docKindMap, locale, prop);
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);

					} else {
						outputStream = this
								.genLMS1205R02A(brNo, lmsRpt,
										new LinkedList<L180R02A>(), docType,
										docKind, printNote, docTypeMap,
										docKindMap, locale, prop);
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);
					}

				}
			} else if ("2".equals(printType)) {
				if (printBranchMap.isEmpty()) {
					MegaSSOUserDetails user = MegaSSOSecurityContext
							.getUserDetails();
					List<L180R02A> l180r02aList2 = new LinkedList<L180R02A>();
					outputStream = this.genLMS1205R02A(user.getUnitNo(),
							lmsRpt, l180r02aList2, docType, docKind, printNote,
							docTypeMap, docKindMap, locale, prop);

				} else {
					for (String brNo : printBranchMap.keySet()) {
						List<L180R02A> l180r02aList2 = printBranchMap.get(brNo);
						if (l180r02aList2 == null) {
							l180r02aList2 = new LinkedList<L180R02A>();
						}
						outputStream = this.genLMS1205R02A(brNo, lmsRpt,
								l180r02aList2, docType, docKind, printNote,
								docTypeMap, docKindMap, locale, prop);
					}
				}
				if (outputStream != null) {
					pdfNameMap.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);
				}

			} else if ("3".equals(printType)) {

				for (String brNo : choickBranchs) {
					if (printBranchMap.containsKey(brNo)) {
						List<L180R02A> l180r02aList2 = printBranchMap.get(brNo);
						if (l180r02aList2 == null) {
							l180r02aList2 = new LinkedList<L180R02A>();
						}
						outputStream = this.genLMS1205R02A(brNo, lmsRpt,
								l180r02aList2, docType, docKind, printNote,
								docTypeMap, docKindMap, locale, prop);
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);

					} else {
						outputStream = this
								.genLMS1205R02A(brNo, lmsRpt,
										new LinkedList<L180R02A>(), docType,
										docKind, printNote, docTypeMap,
										docKindMap, locale, prop);
						pdfNameMap.put(
								new ByteArrayInputStream(
										((ByteArrayOutputStream) outputStream)
												.toByteArray()), subLine);
					}
				}

			}

			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine, vaResult);
			}
		} finally {
			if (pdfNameMap != null) {
				pdfNameMap.clear();
			}
		}
		return outputStream;
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 企金信用評等(L120S01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @param crdTypeMap
	 *            bcodetype的crdType
	 * @return Map<String,String> rptVariableMap
	 */
	@SuppressWarnings("unused")
	private String setL120S01CData(L120S01A l120s01a,
			List<L120S01C> l120s01cList, Map<String, String> crdTypeMap,
			Properties prop, L140M01A l140m01a) {
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		StringBuffer str5 = new StringBuffer();// 個人信用評等 J-105-0156-001 Web
												// e-Loan企金額度明細表增加得引入消金個人信用評等

		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			if (Util.nullToSpace(l120s01a.getCustId()).equals(
					Util.nullToSpace(l120s01c.getCustId()))
					&& Util.nullToSpace(l120s01a.getDupNo()).equals(
							Util.nullToSpace(l120s01c.getDupNo()))) {
				String crdType = Util.trim(l120s01c.getCrdType());
				String grade = Util.trim(l120s01c.getGrade());
				tempGrade.setLength(0);
				if ("NA".equals(crdType)) {
					naResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
					// .append(prop.getProperty("L120S05A.GRPGRRDN"))
					// .append("、");
				} else if ("DB".equals(crdType) || "DL".equals(crdType)
						|| "OU".equals(crdType) || "OB".equals(crdType)
						|| "A0".equals(crdType) || "A1".equals(crdType)
						|| "A2".equals(crdType)) {
					if (str3.length() != 0) {
						str3.append("、");
					}

					if ("NA".equals(grade)) {

					} else {

						if ("A0".equals(crdType) || "A1".equals(crdType)
								|| "A2".equals(crdType)) {

							if (Util.isNumeric(grade)) {
								tempGrade.append(grade)
										.append(prop.getProperty("tempGrade"))
										.append(" ");
							}

							// 取得MOW等級之說明
							tempGrade.append(lmsService.getMowGradeName(prop,
									crdType, grade));

							str3.append(
									Util.nullToSpace(crdTypeMap.get(crdType)))
									.append(" : ")
									.append(tempGrade.toString())
									.append("【")
									.append(prop
											.getProperty("L120S01C.CRDTITLE02"))
									.append(Util.nullToSpace(TWNDate
											.toAD(l120s01c.getCrdTYear())))
									.append(" ")
									.append(prop
											.getProperty("L120S01C.CRDTITLE03"))
									.append(" ")
									.append(l120s01c.getCrdTBR())
									.append(" ")
									.append(Util.nullToSpace(branch
											.getBranchName(Util
													.nullToSpace(l120s01c
															.getCrdTBR()))))
									.append("】");

						} else {
							str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
									.append(grade)
									.append("【")
									.append(prop
											.getProperty("L120S01C.CRDTITLE02"))
									.append(Util.nullToSpace(TWNDate
											.toAD(l120s01c.getCrdTYear())))
									.append(" ")
									.append(prop
											.getProperty("L120S01C.CRDTITLE03"))
									.append(" ")
									.append(l120s01c.getCrdTBR())
									.append(" ")
									.append(Util.nullToSpace(branch
											.getBranchName(Util
													.nullToSpace(l120s01c
															.getCrdTBR()))))
									.append("】");
						}

					}

				} else if ("NO".equals(crdType)) {
					noResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
					// .append(prop.getProperty("L120S01C.NOCRD01"))
					// .append("、");
				} else if ("M".equals(Util.getLeftStr(crdType, 1))) {
					if (Util.isNumeric(grade)) {
						tempGrade.append(grade)
								.append(prop.getProperty("tempGrade"))
								.append(" ");
					}

					// 取得MOW等級之說明
					tempGrade.append(lmsService.getMowGradeName(prop, crdType,
							grade));

					if (str2.length() != 0) {
						str2.append("、");
					}
					str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(tempGrade.toString())
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				} else if (Casedoc.CrdType.MOODY.equals(crdType)
						|| Casedoc.CrdType.SAndP.equals(crdType)
						|| Casedoc.CrdType.Fitch.equals(crdType)
						|| Casedoc.CrdType.中華信評.equals(crdType)
						|| Casedoc.CrdType.FitchTW.equals(crdType)
						|| Casedoc.CrdType.KBRA.equals(crdType)) {
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (str1.length() != 0) {
						str1.append("、");
					}
					str1.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(Util.nullToSpace(crdTypeMap.get(l120s01c
									.getCrdType()))).append("】");
				} else if (crdType.startsWith("C")
						&& Util.notEquals(crdType, "CS")) {
					if (str4.length() != 0) {
						str4.append("、");
					}
					str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				}
			}
		}

		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		String buscd = Util.trim(l120s01a.getBusCode());
		if (Util.equals(buscd, "130300") || Util.equals(buscd, "060000")) {
			// 個人戶
			str5.append(service1401.buildL140S03AStr(l140m01a.getMainId()));
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部評等一定要串
		boolean result = false;
		// rptVariableMap.put("L120S01C.CRD", "");
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "\r" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}

		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		// L120S01C.CRDTITLE07=個金評等 :
		if (str5.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE07")
					+ str5.toString());
		}

		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}

		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());
		// rptVariableMap.put("L120S01C.CRD", total.toString());
		return total.toString();
	}

	private void _c102m01aAndl140s01aMap(Map<String, String> c120m01aMap,
			Map<String, String> l140s01aMap, L120M01A l120m01a,
			L140M01A l140m01a) {

		List<C120M01A> c120m01alist = c120m01aDao.findByMainId(l120m01a
				.getMainId());
		List<L140S01A> l140s01as = l140s01aDao.findByMainId(l140m01a
				.getMainId());

		for (C120M01A c120m01a : c120m01alist) {
			String custPos = "";
			String tCustid = "";
			String tDupNo = "";
			String gCustid = "";
			String gDupNo = "";

			tCustid = Util.trim(c120m01a.getCustId());
			tDupNo = Util.trim(c120m01a.getDupNo());

			for (L140S01A l140s01a : l140s01as) {
				gCustid = Util.trim(l140s01a.getCustId());
				gDupNo = Util.trim(l140s01a.getDupNo());

				if ((gCustid + gDupNo).equals(tCustid + tDupNo)) {
					custPos = Util.trim(l140s01a.getCustPos());
					break;
				}
			}

			c120m01aMap
					.put(tCustid + tDupNo, Util.trim(c120m01a.getCustName()));
			l140s01aMap.put(tCustid + tDupNo, custPos);
		}
	}

	/**
	 * 單筆版信評組字內容
	 */
	private String showModelKind(Properties prop_CLS1141R01RptServiceImpl,
			L120M01A l120m01a, L140M01A l140m01a, L140S02A l140s02a,
			Map<String, String> c120m01aMap, Map<String, String> l140s01aMap) {
		String custPos = "";
		String tCustid = "";
		String tDupNo = "";
		String gCustid = "";
		String gDupNo = "";

		if (LMSUtil.isParentCase(l120m01a)) {
			return "";
		}

		String grade1 = Util.trim(l140s02a.getGrade1());

		tCustid = Util.trim(l140s02a.getCustId());
		tDupNo = Util.trim(l140s02a.getDupNo());
		gCustid = Util.trim(l140m01a.getCustId());
		gDupNo = Util.trim(l140m01a.getDupNo());
		custPos = Util.trim(l140s01aMap.get(tCustid + tDupNo));
		if (("C").equals(custPos)) {
			custPos = prop_CLS1141R01RptServiceImpl
					.getProperty("L140S01A.custPosC");
		} else if (("N").equals(custPos)) {
			custPos = prop_CLS1141R01RptServiceImpl
					.getProperty("L140S01A.custPosN");
		} else if (("G").equals(custPos)) {
			custPos = prop_CLS1141R01RptServiceImpl
					.getProperty("L140S01A.custPosG");
		} else if ((tCustid + tDupNo).equals(gCustid + gDupNo)) {
			custPos = prop_CLS1141R01RptServiceImpl
					.getProperty("L140S01A.custPosK");
		}

		String modelKindDesc = MessageFormat.format(
				prop_CLS1141R01RptServiceImpl
						.getProperty("CLS1151R03.custId02"), custPos)
				+ "："
				+ Util.trim(l140s02a.getCustId())
				+ " "
				+ Util.trim(l140s02a.getDupNo())
				+ " "
				+ Util.trim(c120m01aMap.get(Util.trim(l140s02a.getCustId())
						+ Util.trim(l140s02a.getDupNo()))) + "，";

		if (Util.isNotEmpty(l140s02a)) {
			String modelKind = Util.trim(l140s02a.getModelKind());
			String property = Util.trim(l140s02a.getProperty());

			HashSet<String> prop7_8 = new HashSet<String>();
			{
				prop7_8.add(UtilConstants.Cntrdoc.Property.不變);
				prop7_8.add(UtilConstants.Cntrdoc.Property.取消);
			}
			if (prop7_8.contains(property)) {
				return "";// 以前印出prop_CLS1141R01RptServiceImpl.getProperty("CLS1151R03.grade3");本案無評等
			} else {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(CLS1151S01Page.class);
				if (Util.equals(UtilConstants.L140S02AModelKind.免辦, modelKind)) {
					return prop.getProperty("L140S02A.modelKind.0");
				} else if (Util.equals(UtilConstants.L140S02AModelKind.房貸,
						modelKind)) {
					return modelKindDesc
							+ prop.getProperty("L140S02A.modelKind.1")
							+ "："
							+ LMSUtil.getFinalGrade(l140s02a.getModelKind(),
									grade1);
				} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸,
						modelKind)) {
					return modelKindDesc
							+ prop.getProperty("L140S02A.modelKind.2")
							+ "："
							+ LMSUtil.getFinalGrade(l140s02a.getModelKind(),
									grade1);
				} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸,
						modelKind)) {
					return modelKindDesc
							+ prop.getProperty("L140S02A.modelKind.3")
							+ "："
							+ LMSUtil.getFinalGrade(l140s02a.getModelKind(),
									grade1);
				}
			}
		}
		return modelKindDesc
				+ prop_CLS1141R01RptServiceImpl
						.getProperty("CLS1151R03.grade2")
				+ "："
				+ LMSUtil.getFinalGrade(UtilConstants.L140S02AModelKind.房貸,
						grade1);
	}

	/**
	 * 多筆版信評組字內容
	 */
	private String shortModelKind(Properties prop_CLS1141R01RptServiceImpl,
			L120M01A l120m01a, L140M01A l140m01a, List<L140S02A> l140s02as,
			Map<String, String> c120m01aMap, Map<String, String> l140s01aMap) {
		LinkedHashMap<Integer, String> map = new LinkedHashMap<Integer, String>();
		for (L140S02A l140s02a : l140s02as) {
			if (l140s02a.getSeq() == null) {
				continue;
			}
			String desc = showModelKind(prop_CLS1141R01RptServiceImpl,
					l120m01a, l140m01a, l140s02a, c120m01aMap, l140s01aMap);
			map.put(l140s02a.getSeq(), Util.trim(desc));
		}
		if (Util.isEmpty(Util.trim(StringUtils.join(map.values(), "")))) {
			// 當 N 個產品都是「不需評等」
			return "";
		}

		/*
		 * 加工，讓資料呈現 A. B.
		 */
		Map<Integer, String> seq_printStrMap = LMSUtil
				.getPrintStrForProdSeqNo(l140s02as
						.toArray(new L140S02A[l140s02as.size()]));
		List<String> r = new ArrayList<String>();
		for (Integer seq : map.keySet()) {
			String desc = map.get(seq);

			String seqStr = Util.trim(seq);
			if (seq_printStrMap.containsKey(seq)) {
				seqStr = seq_printStrMap.get(seq);
			}
			if (true) {
				/*
				 * 若有A, B個產品［A:續約、B:不變］ 為免只印出B. 加工處理為印出B.免辦評等
				 */

				// CLS1151R03.noGrade00=免辦評等
				// CLS1151R03.noGrade01=尚未勾選評等
				if (Util.isEmpty(Util.trim(desc))) {
					desc = prop_CLS1141R01RptServiceImpl
							.getProperty("CLS1151R03.noGrade00");
				}
			}
			r.add(seqStr + "." + desc);
		}
		return StringUtils.join(r, "<br/>");
	}

	private String set_cls_gradeData(L120M01A l120m01a, L140M01A l140m01a,
			Properties prop) {
		// L120S01H.clsnagrade=未評等
		// L120S01H.clsnograde=免辦
		String Result = null;
		C120S01A c120s01a = service1205
				.findC120S01AByUniqueKey(l120m01a.getMainId(),
						l140m01a.getCustId(), l140m01a.getDupNo());
		if (true) {
			/*
			 * 有 rptVariableMap.put("L120S01C.CRD", ""); 在rpt隱藏公式才會落入
			 * L120S01C.CRD='' 無 rptVariableMap.put("L120S01C.CRD", "");
			 * 在rpt隱藏公式，L120S01C.CRD 可能是 null
			 */

			// 影響 rpt 的row【未選擇模型評等】隱藏與否
			// rptVariableMap.put("L120S01C.CRD", "");
		}

		if (c120s01a != null) {
			if ("CK".equals(c120s01a.getO_crdType())) {
				Result = c120s01a.getO_grade();
			} else if ("NA".equals(c120s01a.getO_crdType())) {
				Result = prop.getProperty("L120S01H.clsnagrade");
			} else if ("NO".equals(c120s01a.getO_crdType())) {
				Result = prop.getProperty("L120S01H.clsnograde");
			}
		}
		if (true) {
			String cntrNoAdoptGrade = "";
			if (Util.isNotEmpty(Util.trim(l120m01a.getRatingFlag()))
					&& OverSeaUtil.isCaseDoc_CLS_RatingFlag_ON(l120m01a)) {

				cntrNoAdoptGrade = service1405.build_l140m01aAdoptGrade(
						l140m01a, "<br/>");
			}
			// rptVariableMap.put("cntrNoAdoptGrade", cntrNoAdoptGrade);
			Result = cntrNoAdoptGrade;
		}
		return Result;
	}
}
