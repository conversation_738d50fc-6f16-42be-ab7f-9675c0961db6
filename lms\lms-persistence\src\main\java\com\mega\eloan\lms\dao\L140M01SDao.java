/* 
 * L140M01SDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01S;

/** 應收帳款買方額度資訊主檔 **/
public interface L140M01SDao extends IGenericDao<L140M01S> {

	L140M01S findByOid(String oid);

	List<L140M01S> findByMainId(String mainId);

	L140M01S findByUniqueKey(String mainId, String type, Integer itemSeq,
			String custId, String dupNo);

	List<L140M01S> findByIndex01(String mainId, String type, Integer itemSeq);

	List<L140M01S> findByMainIdType(String mainId, String type);

	List<L140M01S> findByMainIdTypeCustId(String mainId, String type,
			String custId, String dupNo);

	public List<L140M01S> findByOids(String[] oids);

}