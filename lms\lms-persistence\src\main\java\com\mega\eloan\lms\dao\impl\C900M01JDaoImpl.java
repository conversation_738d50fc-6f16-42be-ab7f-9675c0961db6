package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.lms.dao.C900M01JDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01J;

/** 地政士黑名單 **/
@Repository
public class C900M01JDaoImpl extends LMSJpaDao<C900M01J, String>
	implements C900M01JDao {

	@Override
	public C900M01J findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C900M01J findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C900M01J> findActiveByCustId(String custId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime","");
		search.addSearchModeParameters(SearchMode.IN, EloanConstants.DOC_STATUS
					, new String[]{FlowDocStatusEnum.已核准.getCode(), 
						FlowDocStatusEnum.待解除.getCode()});
		if(true){
			Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
			map.put("approveTime", true);
			search.setOrderBy(map);	
		}
		search.setMaxResults(Integer.MAX_VALUE);
		
		List<C900M01J> list = createQuery(search).getResultList();
		return list;
	}
	
}