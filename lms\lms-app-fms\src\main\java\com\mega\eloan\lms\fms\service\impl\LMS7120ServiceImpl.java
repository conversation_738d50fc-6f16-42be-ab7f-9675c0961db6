/* 
 * LMS7120ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.dao.L140M01QDao;
import com.mega.eloan.lms.dao.L712M01ADao;
import com.mega.eloan.lms.dao.L918S01ADao;
import com.mega.eloan.lms.fms.constants.fmsConstants;
import com.mega.eloan.lms.fms.service.LMS7120Service;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L712M01A;
import com.mega.eloan.lms.model.L918S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 停權解除維護ServiceImpl
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Service
public class LMS7120ServiceImpl extends AbstractCapService implements
		LMS7120Service {
	@Resource
	L712M01ADao l712m01aDao;
	@Resource
	L918S01ADao l918s01aDao;
	@Resource
	FlowService flowService;
	@Resource
	FlowNameService flowNameService;
	@Resource
	L140M01QDao l140m01qDao;

	private static final Logger logger = LoggerFactory
			.getLogger(LMS7120ServiceImpl.class);

	@Override
	public void save(GenericBean... entity) {
		// 進行無限多筆儲存
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				try {// 設定更新與建立人員
					if (Util.isEmpty(model.get(EloanConstants.OID))) {// 有OID=>已存在=>不更改建立人員
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("CapException!!", e);
				}

				if (model instanceof L712M01A) {
					l712m01aDao.save((L712M01A) model);
					// 記錄文件異動記錄
					// docLogService.record(model.getOid(),DocLogEnum.SAVE);
				} else if (model instanceof L140M01Q) {
					l140m01qDao.save((L140M01Q) model);
				}
			}
		}

	}

	@Override
	public void saveMainAndSubList(L712M01A meta, L140M01Q l140m01q) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l140m01q.setUpdater(user.getUserId());
		l140m01q.setUpdateTime(CapDate.getCurrentTimestamp());
		l140m01qDao.save(l140m01q);
		this.save(meta);
	}

	@Override
	public void delMainAndSubList(L712M01A meta, List<L918S01A> list) {
		if (list != null) {
			l918s01aDao.delete(list);
		}
		if (meta != null) {
			l712m01aDao.delete(meta);
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L712M01A) {
					((L712M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					this.save(model);
				} else if (model instanceof L140M01Q) {
					//deleteTable.properties 有設L712M01A deletedTime 刪除時一併刪除L140M01Q
				}
			}
		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L712M01A.class) {
			return l712m01aDao.findPage(search);
		} else if (clazz == L918S01A.class) {
			return l918s01aDao.findPage(search);
		}
		return null;
	}

	@Override
	public L712M01A findL712m01aByOid(String oid) {
		return l712m01aDao.findByOid(oid);
	}

	@Override
	public L712M01A findL712m01aByMainId(String mainId) {
		return l712m01aDao.findByMainId(mainId);
	}

	@Override
	public void deleteL712m01a(String oid) {
		L712M01A model = l712m01aDao.findByOid(oid);
		if (model != null) {
			this.delete(model);
		}
	}

	@Override
	public List<L918S01A> findL918s01aByMainId(String mainId) {
		return l918s01aDao.findByMainId(mainId);
	}

	@Override
	public L918S01A findL918s01aByOid(String oid) {
		return l918s01aDao.findByOid(oid);
	}

	@Override
	public void deleteL918s01a(String oid) {
		L712M01A model = l712m01aDao.findByOid(oid);
		if (model != null) {
			model.setDeletedTime(CapDate.getCurrentTimestamp());
			this.save(model);
		}
	}

	@Override
	public void delList918s01aByMainId(String mainId) {
		List<L918S01A> list = l918s01aDao.findByMainId(mainId);
		for (L918S01A model : list) {
			if (fmsConstants.stopRelease.statusFlag.新增.equals(Util.trim(model
					.getStatusFlag()))) {
				model.setStatusFlag(fmsConstants.stopRelease.statusFlag.新增後刪除);
			} else {
				model.setStatusFlag(fmsConstants.stopRelease.statusFlag.刪除);
			}
		}
		if (list.size() > 0) {
			l918s01aDao.save(list);
		}
	}

	@Override
	public void saveList918s01a(List<L918S01A> list) {
		if (list.size() > 0) {
			l918s01aDao.save(list);
		}
	}

	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String next) throws Throwable {
		L712M01A meta = (L712M01A) model;
		try {
			l712m01aDao.save(meta);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				// 判斷flow走哪個流程
				inst = flowService.start("LMS7120Flow", mainOid,
						user.getUserId(), user.getUnitNo());
			}
			String nowDefname = inst.getDefinition().getName();

			logger.info("[flowAction]LMS7120ServiceImpl.flowAction nowDefname ====>"
					+ nowDefname);
			if (setResult) {
				logger.info("[flowAction]LMS7120ServiceImpl.flowAction flowName Key====>"
						+ flowNameService.getKey(next));
				// 當有setResult值才要塞
				inst.setAttribute("result", flowNameService.getKey(next));
			}
			inst.next();
			if (inst.getAttribute("flowCode") != null) {
				logger.info("[flowAction]LMS7120ServiceImpl.flowAction result next flowCode ====>"
						+ inst.getAttribute("result"));
				String flowCode = (String) inst.getAttribute("flowCode");
				inst = flowService
						.start(flowCode, mainOid, user.getUserId(),
								user.getUnitNo(), "result",
								inst.getAttribute("result"));
			}
		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	// 以下程式碼不會用到
	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		return null;
	}
}
