/* 
 * LMS1201GridHandler.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.grid;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.CustIdFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.CESConstant;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CMSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSM02BPanel;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.panels.LMSS23APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbCOLBASEService;
import com.mega.eloan.lms.enums.GaapFlagEnum;
import com.mega.eloan.lms.enums.PeriodTypeEnum;
import com.mega.eloan.lms.lms.service.FSSGridService;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lns.pages.LMS1201M01Page;
import com.mega.eloan.lms.lns.pages.LMS1601M01Page;
import com.mega.eloan.lms.lns.pages.LMSS02BPage;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S06Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S07Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S11Panel;
import com.mega.eloan.lms.lns.panels.LMSS07APanel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.model.C100M01;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C140S04A;
import com.mega.eloan.lms.model.C140S04B;
import com.mega.eloan.lms.model.C140S04C;
import com.mega.eloan.lms.model.C140S09A;
import com.mega.eloan.lms.model.C140S09B;
import com.mega.eloan.lms.model.C140S09C;
import com.mega.eloan.lms.model.C140S09D;
import com.mega.eloan.lms.model.C140S09E;
import com.mega.eloan.lms.model.C140S09F;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120M01J;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S01R;
import com.mega.eloan.lms.model.L120S01T;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S04B;
import com.mega.eloan.lms.model.L120S05B;
import com.mega.eloan.lms.model.L120S05D;
import com.mega.eloan.lms.model.L120S05E;
import com.mega.eloan.lms.model.L120S05F;
import com.mega.eloan.lms.model.L120S06A;
import com.mega.eloan.lms.model.L120S08A;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09C;
import com.mega.eloan.lms.model.L120S10A;
import com.mega.eloan.lms.model.L120S11A;
import com.mega.eloan.lms.model.L120S14A;
import com.mega.eloan.lms.model.L120S14B;
import com.mega.eloan.lms.model.L120S14C;
import com.mega.eloan.lms.model.L120S14D;
import com.mega.eloan.lms.model.L120S14E;
import com.mega.eloan.lms.model.L120S14F;
import com.mega.eloan.lms.model.L120S16A;
import com.mega.eloan.lms.model.L120S16C;
import com.mega.eloan.lms.model.L120S21A;
import com.mega.eloan.lms.model.L120S21B;
import com.mega.eloan.lms.model.L120S21C;
import com.mega.eloan.lms.model.L120S23A;
import com.mega.eloan.lms.model.L120S24A;
import com.mega.eloan.lms.model.L120S24B;
import com.mega.eloan.lms.model.L120S25A;
import com.mega.eloan.lms.model.L130S01A;
import com.mega.eloan.lms.model.L130S02A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L161S01D;
import com.mega.eloan.lms.model.L161S01E;
import com.mega.eloan.lms.model.L720M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 授信簽報書Grid
 * </pre>
 * 
 * @since 2011/8/1
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/8/1,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1201gridhandler")
public class LMS1201GridHandler extends AbstractGridHandler {

	@Resource
	LMS1201Service service1201;

	@Resource
	LMS1405Service service1405;

	@Resource
	CodeTypeService codeservice;

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branchService;

	@Resource
	DocFileService docfileservice;

	@Resource
	FSSGridService gridService; // 查詢待列印的一般財務報表

	@Resource
	LMSService lmsService;

	@Resource
	MisMISLN20Service misMISLN20Service;

	private final String DATEYYYYMMDD = "yyyy-MM-dd";

	@Resource
	EloandbCOLBASEService colService;
	@Resource
	LMS1601Service lms1601Service;

	@Resource
	AMLRelateService amlRelateService;

	@Resource
	EloandbBASEService eloanDbBaseService;

	@Resource
	DwdbBASEService dwdbService;

	@Resource
	LMSLgdService lmsLgdService;

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a(ISearch pageSetting,	PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		Date fromDate = null;
		Date endDate = null;
		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
		}
		// 當為授管處簽案時要加上此條件
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& (UnitTypeEnum.分行.equals(UnitTypeEnum.convertToUnitType(user
						.getUnitType())) || UnitTypeEnum.國金部
						.equals(UnitTypeEnum.convertToUnitType(user
								.getUnitType())))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					UtilConstants.BankNo.授管處);
		}
		switch (docStatusEnum) {
		case 海外_編製中:
			if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
				String[] showDoc = new String[] {
						CreditDocStatusEnum.海外_編製中.getCode(),
						CreditDocStatusEnum.會簽後修改編製中.getCode() };
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						showDoc);
				pageSetting
						.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.EQUALS,
										"hqMeetFlag", "0"),
								new SearchModeParameter(SearchMode.OR,
										new SearchModeParameter(
												SearchMode.IS_NULL,
												"hqMeetFlag", ""),
										new SearchModeParameter(
												SearchMode.EQUALS,
												"hqMeetFlag",
												UtilConstants.Mark.SPACE)));
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			break;
		case 海外_待覆核:
			if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
				String[] showDoc = new String[] {
						CreditDocStatusEnum.海外_待覆核.getCode(),
						CreditDocStatusEnum.會簽後修改待覆核.getCode(),
						CreditDocStatusEnum.總處營業單位待覆核.getCode() };
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						showDoc);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			break;
		case 海外_呈總行:

			// 當登錄分行為澳洲 ，分行呈總行文件狀態 後為提會待登錄 或提會帶覆核
			// IBranch branchtype = branch.getBranch(user.getUnitNo());
			// if (UtilConstants.Country.澳洲.equals(branchtype.getCountryType())
			// || UtilConstants.Country.加拿大.equals(branchtype
			// .getCountryType())
			// || UtilConstants.Country.泰國.equals(branchtype
			// .getCountryType())) {
			String[] showDoc = new String[] {
					CreditDocStatusEnum.海外_待覆核.getCode(),
					CreditDocStatusEnum.海外_總行提會待登錄.getCode(),
					CreditDocStatusEnum.海外_總行提會待覆核.getCode(),
					CreditDocStatusEnum.泰國_提會待登錄.getCode(),
					CreditDocStatusEnum.泰國_提會待覆核.getCode() };
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					showDoc);
			// } else {
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
			// "docStatus", CreditDocStatusEnum.海外_待覆核.getCode());
			// }
			break;
		case 海外_呈授管處:
			// 當狀態為海外_呈授管處時要變更搜尋條件要文件狀態為C或H結尾都要顯示，並且join授權檔
			pageSetting
					.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%H"),
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%C"));
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "docStatus", CreditDocStatusEnum.授管處_待陳復.getCode());
			break;
		case 特殊分行提授審會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行授審會);
			break;
		case 特殊分行提催收會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行逾審會);
			break;
		case 特殊分行提常董會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行常董會);
			break;
		case 特殊分行提審計委員會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行審計委員會);
			break;
		case 總處營業單位已會簽:
			// 已會簽在提授審會時不能不能出現此案件
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "hqMeetFlag",
							"0"), new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.IS_NULL,
									"hqMeetFlag", ""), new SearchModeParameter(
									SearchMode.EQUALS, "hqMeetFlag",
									UtilConstants.Mark.SPACE)));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			break;
		case 營運中心_呈總處:
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "docStatus",
					"%H");

			break;

		case 營運中心_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "S"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "A")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));

			break;

		case 授管處_待收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"hqReceiveDate", "");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
			break;
		case 授管處_已收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"hqReceiveDate", "");
			break;
		case 授管處_審查中:
			// 審查中在提授審會時不能不能出現此案件
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "hqMeetFlag",
							"0"), new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.IS_NULL,
									"hqMeetFlag", ""), new SearchModeParameter(
									SearchMode.EQUALS, "hqMeetFlag",
									UtilConstants.Mark.SPACE)));

			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			break;
		case 授管處_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "C"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "S")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));
			break;
		case 海外_已核准:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
			// 如果登錄的是總行要多看到 泰國的提會待登錄、和泰國提會待覆核的案件
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				// 已核准案件要排除陳覆陳述案，
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						new String[] { CreditDocStatusEnum.海外_已核准.getCode(),
								CreditDocStatusEnum.泰國_提會待登錄.getCode(),
								CreditDocStatusEnum.泰國_提會待覆核.getCode() });
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			break;
		case 海外_陳復案_陳述案:
			// 已核准 且 docCode = 3
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;

		case 授管處_免批覆案件:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;
		case 營運中心_所有提會案件:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"rptTitleArea1", "");
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"rptTitleArea1", "");
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);
		// if (CreditDocStatusEnum.海外_編製中.getCode().equals(docStatus)) {
		// // 排除掉異常通報案件
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
		// "docCode", UtilConstants.Casedoc.DocCode.異常通報);
		// }
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));

				// (108)第 3230 號
				String docStatusStr = getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode());
				if (LMSUtil.isSpecialBranch(Util.trim(model.getCaseBrId()))) {
					// (108)第 3230 號
					if ((CreditDocStatusEnum.授管處_審查中.getCode().equals(
							model.getDocStatus()) || CreditDocStatusEnum.授管處_已會簽
							.getCode().equals(model.getDocStatus()))
							&& (UtilConstants.Casedoc.AreaChk.送會簽.equals(model
									.getAreaChk()))) {
						// docStatus.LWC=會簽中
						model.setDocStatus(getMessage("docStatus.LWC"));
					}

					// (108)第 3230 號
					if ((StringUtils.contains(docStatusStr, "會簽"))
							&& (UtilConstants.Casedoc.AreaChk.送初審.equals(model
									.getAreaChk()) || UtilConstants.Casedoc.AreaChk.送初審審查
									.equals(model.getAreaChk()))) {
						docStatusStr = StringUtils.replace(docStatusStr, "會簽",
								"審查");
					}
				}

				model.setDocStatus(docStatusStr);

			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}

			// RPA狀態*****************************************************
			if (!Util.isEmpty(Util.trim(model.getApplyStatus()))) {
				model.setApplyStatus(pop.getProperty("l120m01a.applyStatusC."
						+ Util.trim(model.getApplyStatus())));

			} else {
				model.setApplyStatus("");
			}

		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)會簽Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult querySpectial(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);

		// (108)第 3230 號
		pageSetting.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.EQUALS, "areaChk",
						UtilConstants.Casedoc.AreaChk.送會簽),
				new SearchModeParameter(SearchMode.EQUALS, "areaChk",
						UtilConstants.Casedoc.AreaChk.送初審));

		pageSetting.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.EQUALS, "docStatus",
						CreditDocStatusEnum.授管處_審查中.getCode()),
				new SearchModeParameter(SearchMode.EQUALS, "docStatus",
						CreditDocStatusEnum.授管處_待放行.getCode()));

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));

				// (108)第 3230 號
				String docStatusStr = getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode());

				// (108)第 3230 號
				if (LMSUtil.isSpecialBranch(Util.trim(model.getCaseBrId()))) {
					if ((StringUtils.contains(docStatusStr, "會簽"))
							&& (UtilConstants.Casedoc.AreaChk.送初審.equals(model
									.getAreaChk()) || UtilConstants.Casedoc.AreaChk.送初審審查
									.equals(model.getAreaChk()))) {
						docStatusStr = StringUtils.replace(docStatusStr, "會簽",
								"審查");
					}
				}
				// (108)第 3230 號
				model.setDocStatus(docStatusStr);

			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L120M01AGrid 資料(異常通報用)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a1(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		Date fromDate = null;
		Date endDate = null;
		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "typCd",
			// UtilConstants.Casedoc.typCd.海外);
			// 限定只顯示企金案件
			if (branchService.isOBSBranch(user.getUnitNo())) {
				// 海外分行，應同時顯示企金、消金的異常通報案件
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docType", UtilConstants.Casedoc.DocType.企金);
			}
		}
		// 當為授管處簽案時要加上此條件
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& (UnitTypeEnum.分行.equals(UnitTypeEnum.convertToUnitType(user
						.getUnitType())) || UnitTypeEnum.國金部
						.equals(UnitTypeEnum.convertToUnitType(user
								.getUnitType())))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					UtilConstants.BankNo.授管處);
		}
		// 限定只顯示異常通報案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
				UtilConstants.Casedoc.DocCode.異常通報);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				// (108)第 3230 號
				String docStatusStr = getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode());
				model.setUid(Util.trim(model.getDocStatus()));

				// (108)第 3230 號
				if (LMSUtil.isSpecialBranch(Util.trim(model.getCaseBrId()))) {
					if ((StringUtils.contains(docStatusStr, "會簽"))
							&& (UtilConstants.Casedoc.AreaChk.送初審.equals(model
									.getAreaChk()) || UtilConstants.Casedoc.AreaChk.送初審審查
									.equals(model.getAreaChk()))) {
						docStatusStr = StringUtils.replace(docStatusStr, "會簽",
								"審查");
					}
				}
				model.setDocStatus(docStatusStr);
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L120M01AGrid 資料(授審、催收、常董)用
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120m01a3(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		switch (docStatusEnum) {
		case 特殊分行提授審會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行授審會);
			break;
		case 特殊分行提催收會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行逾審會);
			break;
		case 特殊分行提常董會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行常董會);
			break;
		case 特殊分行提審計委員會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行審計委員會);
			break;
		default:
			String kind = params.getString("kind");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", kind);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
				user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);

		for (int w = 0; w < page.getContent().size(); w++) {
			L120M01A model = (L120M01A) page.getContent().get(w);
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			strB.append(
					("1".equals(model.getDocKind()) && "1".equals(model
							.getDocType())) ? pop.getProperty("L1205G.grid1")
							: ("2".equals(model.getDocKind()) && "1"
									.equals(model.getDocType())) ? pop
									.getProperty("L1205G.grid2") : ("1"
									.equals(model.getDocKind()) && "2"
									.equals(model.getDocType())) ? pop
									.getProperty("L1205G.grid12") : pop
									.getProperty("L1205G.grid13")).append("(")
					.append(docCodeName(model.getDocCode())).append(")");
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			model.setDocStatus(getMessage("docStatus."
					+ CreditDocStatusEnum.getEnum(model.getDocStatus())
							.getCode()));
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			model.setAreaAppraiser(getPerName(Util.trim(model
					.getAreaAppraiser())));
			model.setHqAppraiser(getPerName(Util.trim(model.getHqAppraiser())));
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L121M01AGrid 資料(海外聯貸案Grid)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL121m01a(ISearch pageSetting,	PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		Date fromDate = null;
		Date endDate = null;

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
		}

		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}
		switch (docStatusEnum) {
		case 營運中心_海外聯貸案_已會簽:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_已會簽.getCode());
			break;
		case 營運中心_海外聯貸案_待放行:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_待放行.getCode());
			break;
		case 營運中心_海外聯貸案_會簽中:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_會簽中.getCode());
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);

		for (int w = 0; w < page.getContent().size(); w++) {
			L120M01A model = (L120M01A) page.getContent().get(w);
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			strB.append(
					"1".equals(model.getDocKind()) ? pop
							.getProperty("L1205G.grid1") : pop
							.getProperty("L1205G.grid2")).append("(")
					.append(docCodeName(model.getDocCode())).append(")");
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			model.setDocStatus(getMessage("docStatus."
					+ CreditDocStatusEnum.getEnum(model.getAreaDocstatus())
							.getCode()));
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 篩選L120M01AGrid 外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120m01a2(ISearch pageSetting, PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String typCd = Util.trim(params.getString("typCd"));
		String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));

		String caseBrId = Util.trim(params.getString("caseBrId"));

		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		String caseLvl = Util.trim(params.getString("caseLvl"));

		// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書。
		// 去篩選簽報書統編、額度、MAINID 符合者
		List<String> l120m01a_maindId_list = lmsService
				.getL120M01AMainIdByFilterForm(params); // 額度明細表符合 統編、額度、MAINID
														// 之簽報書MAINID

		if (l120m01a_maindId_list.size() > 20000) {
			if (Util.isNotEmpty(custName)) {
				// 筆數太多會掛掉
				// 戶名符合條件筆數太多l120m01a_maindId_list.size()，請增加篩選條件後再執行本作業。
				throw new CapMessageException("簽報書基本查詢條件符合筆數太多" + "("
						+ Util.trim(l120m01a_maindId_list.size()) + "筆)"
						+ "，請增加簽報書篩選條件後再執行本作業", getClass());

			}

		}

		if (Util.equals(custId, "") && Util.equals(custName, "")) {
			if (l120m01a_maindId_list.size() > 0) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "mainId",
						l120m01a_maindId_list);
			}
		} else {
			// 篩選條件有統編、戶名時
			if (l120m01a_maindId_list.size() > 0) {

				if (Util.isNotEmpty(custId) && Util.isNotEmpty(custName)) {
					pageSetting.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.IN, "mainId",
									l120m01a_maindId_list),
							new SearchModeParameter(SearchMode.AND,
									new SearchModeParameter(SearchMode.EQUALS,
											"custId", custId),
									new SearchModeParameter(SearchMode.LIKE,
											"custName", custName + "%")));
				} else {
					if (Util.isNotEmpty(custId)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN,
										"mainId", l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.EQUALS,
										"custId", custId));
					}
					if (Util.isNotEmpty(custName)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN,
										"mainId", l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.LIKE,
										"custName", custName + "%"));
					}
				}

			} else {
				if (Util.isNotEmpty(custId)) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"custId", custId);
				}
				if (Util.isNotEmpty(custName)) {
					pageSetting.addSearchModeParameters(SearchMode.LIKE,
							"custName", custName + "%");
				}

			}
		}

		Date fromDate = null;
		Date endDate = null;
		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
			// if (docStatus.equals(CreditDocStatusEnum.海外_已核准.getCode())) {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "approveTime", reason);
			// } else {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "caseDate", reason);
			// }
		}
		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
		}
		if (Util.isNotEmpty(docType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		}
		if (Util.isNotEmpty(docKind)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		if (Util.isNotEmpty(docCode)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					docCode);
		}

		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		if (Util.isNotEmpty(caseLvl)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseLvl",
					caseLvl);
		}

		if (Util.isNotEmpty(updater)) {
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"hqAppraiser", updater);
			} else if (UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.北一區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.北二區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.南部區域授信中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(user.getUnitNo())) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"areaAppraiser", updater);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"updater", updater);
			}
		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE + " 23:59:59") });
		}
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}
		// 當為授管處簽案時要加上此條件
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& (UnitTypeEnum.分行.equals(UnitTypeEnum.convertToUnitType(user
						.getUnitType())) || UnitTypeEnum.國金部
						.equals(UnitTypeEnum.convertToUnitType(user
								.getUnitType())))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					UtilConstants.BankNo.授管處);
		}
		switch (docStatusEnum) {
		case 海外_呈總行:

			// 當登錄分行為澳洲 ，分行呈總行文件狀態 後為提會待登錄 或提會帶覆核
			IBranch branchtype = branchService.getBranch(user.getUnitNo());
			if (UtilConstants.Country.澳洲.equals(branchtype.getCountryType())
					|| UtilConstants.Country.加拿大.equals(branchtype
							.getCountryType())) {
				String[] showDoc = new String[] {
						CreditDocStatusEnum.海外_待覆核.getCode(),
						CreditDocStatusEnum.海外_總行提會待登錄.getCode(),
						CreditDocStatusEnum.海外_總行提會待覆核.getCode() };
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						showDoc);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", CreditDocStatusEnum.海外_待覆核.getCode());
			}
			break;
		case 海外_呈授管處:
			// 當狀態為海外_呈授管處時要變更搜尋條件要文件狀態為C或H結尾都要顯示，並且join授權檔
			pageSetting
					.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%H"),
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%C"));
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "docStatus", CreditDocStatusEnum.授管處_待陳復.getCode());
			break;
		case 營運中心_呈總處:
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "docStatus",
					"%H");

			break;

		case 營運中心_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "S"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "A")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));

			break;

		case 授管處_待收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"hqReceiveDate", "");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
			break;
		case 授管處_已收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"hqReceiveDate", "");
			break;
		case 授管處_審查中:
			// 審查中在提授審會時不能不能出現此案件
			// pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
			// "hqMeetFlag", null);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			break;
		case 授管處_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "C"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "S")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));
			break;
		case 海外_已核准:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
			// 如果登錄的是總行要多看到 泰國的提會待登錄、和泰國提會待覆核的案件
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				// 已核准案件要排除陳覆陳述案，
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						new String[] { CreditDocStatusEnum.海外_已核准.getCode(),
								CreditDocStatusEnum.泰國_提會待登錄.getCode(),
								CreditDocStatusEnum.泰國_提會待覆核.getCode() });
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			break;
		case 海外_陳復案_陳述案:
			// 已核准 且 docCode = 3
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;

		case 授管處_免批覆案件:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;
		case 營運中心_所有提會案件:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"rptTitleArea1", "");
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"rptTitleArea1", "");
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		if (Util.equals(docStatus, CreditDocStatusEnum.海外_已核准)
				&& (Util.equals(user.getUnitNo(), UtilConstants.BankNo.授信行銷處) || Util
						.equals(user.getUnitNo(), UtilConstants.BankNo.消金業務處))) {
			if (Util.notEquals(caseBrId, "")) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"l120a01a.authUnit", caseBrId);
			}

		} else {
			// J-111-0058_05097_B1002 Web e-Loan授信額度批覆表總處核定批示顯示內容調整
			// 因為北區分行授權外案件直送授審處，導致北區營運中心看不到分行直送的案件，所以針對北區營運中心加上 (t0.areaChk =
			// '3' AND areaBrId = '931')之條件
			if (Util.equals(user.getUnitNo(), UtilConstants.BankNo.北一區營運中心)
					&& Util.equals(CreditDocStatusEnum.海外_已核准.getCode(),
							docStatus)) {

				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"l120a01a.authUnit", user.getUnitNo());

				// pageSetting.addSearchModeParameters(SearchMode.OR,
				// new SearchModeParameter(SearchMode.EQUALS,
				// "l120a01a.authUnit", user.getUnitNo()),
				// new SearchModeParameter(SearchMode.AND,
				// new SearchModeParameter(SearchMode.EQUALS,
				// "areaChk",
				// UtilConstants.Casedoc.AreaChk.送審查),
				// new SearchModeParameter(SearchMode.EQUALS,
				// "areaBrId", user.getUnitNo())));
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"l120a01a.authUnit", user.getUnitNo());
			}

			if (Util.notEquals(user.getUnitType(), "S")
					&& Util.notEquals(user.getUnitType(), "A")) {
				// 當非授管處或營運中心時
				// 排除掉海外授信案件
				pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
						"typCd", UtilConstants.Casedoc.typCd.海外);
			}
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);

		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("userid", new UserNameFormatter(userservice)); // 使用者名稱格式化

		for (int w = 0; w < page.getContent().size(); w++) {
			L120M01A model = (L120M01A) page.getContent().get(w);
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			strB.append(
					"1".equals(model.getDocKind()) ? pop
							.getProperty("L1205G.grid1") : pop
							.getProperty("L1205G.grid2")).append("(")
					.append(docCodeName(model.getDocCode())).append(")");
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			if (Util.trim(model.getCaseNo()).indexOf("舊") != -1) {
				model.setGist("V");
			} else {
				model.setGist(UtilConstants.Mark.SPACE);
			}
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 和 method : queryL120m01a 內的邏輯相同
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if ("Y".equals(model.getReEstFlag())) {
				model.setReEstFlag(pop.getProperty("l120m01a.edit"));
			} else if ("A".equals(model.getReEstFlag())) {
				model.setReEstFlag(pop.getProperty("l120m01a.editA"));
			} else {
				model.setReEstFlag("");
			}
			// 針對授管處特殊分行已會簽的顯示
			// if (docStatus.equals("03K|01K|02K|04K")) {
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
			// }
		}

		// model.setDocStatus(CreditDocStatusEnum.getEnum(model.getDocStatus()));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢L120S01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01a(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("keyMan", true);
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);

		// pageSetting.addOrderBy("oid");
		pageSetting.addOrderBy("keyMan", true);
		pageSetting.addOrderBy("custShowSeqNum", false);
		pageSetting.addOrderBy("custId", false);
		pageSetting.addOrderBy("dupNo", false);
		// pageSetting.addOrderBy("oid", false);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S01A.class,
				pageSetting);
		// Page<L120S01A> page = service.getPage(pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S01A model = list.get(i);
			model.setCustId(model.getCustId() + " " + model.getDupNo());
			model.setCustPos(findCustPos(model));
			CodeType code1 = new CodeType();
			CodeType code2 = new CodeType();
			String custRlt = Util.trim(model.getCustRlt());
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			if ("Y".equals(model.getKeyMan())) {
				model.setKeyMan("*");
			} else {
				model.setKeyMan("");
			}
			if (!Util.isEmpty(custRlt)) {
				if (!(custRlt.contains("X"))) {
					// 其他綜合關係
					code1 = codeservice.findByCodeTypeAndCodeValue(
							"Relation_type31", custRlt.substring(0, 1), LocaleContextHolder.getLocale().toString());
					code2 = codeservice.findByCodeTypeAndCodeValue(
							"Relation_type32", custRlt.substring(1, 2), LocaleContextHolder.getLocale().toString());
				} else {
					if (custRlt.endsWith("X")) {
						// 企業關係人
						code1 = codeservice.findByCodeTypeAndCodeValue(
								"Relation_type1", custRlt, LocaleContextHolder.getLocale().toString());
					} else {
						// 親屬關係
						code1 = codeservice.findByCodeTypeAndCodeValue(
								"Relation_type2", custRlt, LocaleContextHolder.getLocale().toString());
					}
				}
			}
			if (code1 != null && code2 != null) {
				if (!Util.isEmpty(code2.getCodeDesc())) {
					StringBuilder strB = new StringBuilder();
					strB.append(code1.getCodeDesc()).append("-")
							.append(code2.getCodeDesc());
					// code2有資料則設定code1+code2
					model.setCustRlt(strB.toString());
				} else {
					if (!Util.isEmpty(code1.getCodeDesc())) {
						// code1有資料則設定code1
						model.setCustRlt(code1.getCodeDesc());
					} else {
						// 無資料則設為空
						model.setCustRlt("");
					}
				}
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 依客戶統編查詢L120S01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s01aById(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		String custId = Util.nullToSpace(params.getString("custId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S01A.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S01AGrid 資料(借款人引入)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01aToGetData(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S01A.class,
				pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 相關文件夾帶個金報書選擇Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01aPer(ISearch pageSetting, PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		// 建立主要Search 條件
		// 相關文件企金簽報書可夾帶個金簽報書（限編製中案件）
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS,
						EloanConstants.DOC_STATUS,
						CreditDocStatusEnum.海外_編製中.getCode());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String custId = Util.trim(params.getString("queryL120m01aPerId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2013/07/04,Rex,新增分行篩選
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 排除海外個金簽報書
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
				UtilConstants.Casedoc.typCd.海外);
		// 排除案號為空
		pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL, "caseNo",
				"");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);
		List<L120M01A> list = (List<L120M01A>) page.getContent();
		for (L120M01A model : list) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢資信簡表(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e1(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainId1(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表(徵信)-- 集團
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e1Grp(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		String authBrId = user.getUnitNo();
		String custId = Util.trim(l120m01a.getCustId());
		String dupNo = Util.trim(l120m01a.getDupNo());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainId1(authBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e2(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainId2(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告2(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e3(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String oid = Util.nullToSpace(params.getString("oid"));
		String mainId = Util.nullToSpace(params.getString("mainId"));
		L120S01A l120s01a = service1201.findL120s01aByOid(oid);
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainId);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120m01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainId2(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告--依照使用者輸入之統編(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIds(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String custId = Util.nullToSpace(params.getString("custId"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainId(caseBrId,
				custId, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIds2(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainId2s(caseBrId,
				mainid, mainid, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告 MainId(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdss2(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainId2ss(caseBrId,
				pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
			map.put("creator",
					Util.isEmpty(map.get("creator")) ? "" : withIdName(Util
							.trim(Util.nullToSpace(map.get("creator")))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIda(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainIda(caseBrId,
				mainid, mainid, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdb(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String custId = Util.nullToSpace(params.getString("custId"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainIdb(caseBrId,
				custId, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdc(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = service1201.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainIdc(caseBrId,
				pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢簽報書敘述說明檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120m01d(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "itemType", "C");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01D.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S03AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s03a(ISearch pageSetting, PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("cntrNo");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S03A.class,
				pageSetting);

		List<L120S03A> list = (List<L120S03A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S03A model = list.get(i);
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			// 非信保
			if ("2".equals(model.getCrdFlag())) {
				model.setCrdFlag(pop.getProperty("L1205S07.index2"));
				model.setCrdRskRatio(null);
				model.setRskAmt2(model.getRskAmt1());
				model.setRskr2(model.getRskr1());
				model.setCamt2(model.getCamt1());
				model.setBisr2(model.getBisr1());
				model.setCostr2(model.getCostr1());
			} else {
				model.setCrdFlag(pop.getProperty("L1205S07.index1"));
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S04AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s04a(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		// J-109-0370 相關評估改版
		boolean needCustId = params.getBoolean("needCustId");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		if (needCustId) {
			String keyCustId = Util.trim(params.getString("keyCustId"));
			String keyDupNo = Util.trim(params.getString("keyDupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyCustId",
					keyCustId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo",
					keyDupNo);
		}

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S04A.class,
				pageSetting);
		List<L120S04A> list = (List<L120S04A>) page.getContent();

		if (list != null && !list.isEmpty()) {

			Collections.sort(list, new Comparator<L120S04A>() {

				@Override
				public int compare(L120S04A object1, L120S04A object2) {
					// TODO Auto-generated method stub
					int cr = 0;
					String[] resStr1 = Util.trim(object1.getCustRelation())
							.split(",");
					Arrays.sort(resStr1);
					String[] resStr2 = Util.trim(object2.getCustRelation())
							.split(",");
					Arrays.sort(resStr2);

					int a = resStr2[0].compareTo(resStr1[0]);

					String prtFlag1 = object1.getPrtFlag();
					String prtFlag2 = object2.getPrtFlag();
					int prtFlag = prtFlag2.compareTo(prtFlag1);

					if (prtFlag != 0) {
						cr = (prtFlag > 0) ? -1 : 5;
					} else if (a != 0) {
						cr = (a > 0) ? -2 : 4;
					} else {
						long b = (object2.getProfit() == null ? 0 : object2
								.getProfit())
								- (object1.getProfit() == null ? 0 : object1
										.getProfit());
						if (b != 0) {
							cr = (b > 0) ? 3 : -3;
						} else {
							int c = object2.getCustId().compareTo(
									object1.getCustId());
							if (c != 0) {
								cr = (c > 0) ? -4 : 2;
							} else {
								// String oid1 = object1.getOid();
								// String oid2 = object2.getOid();
								// int oidFlag = oid2.compareTo(oid2);
								// if(oidFlag != 0){
								// cr = (oidFlag > 0)? -5:1;
								// }
							}
						}
					}

					return cr;
				}
			});
		}

		for (int i = 0; i < list.size(); i++) {
			L120S04A model = list.get(i);
			if (Util.isEmpty(model.getCustId())) {
				model.setCustId("");
			} else {
				model.setCustId(model.getCustId() + " " + model.getDupNo());
			}
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			if ("1".equals(model.getPrtFlag())) {
				model.setPrtFlag("V");
			} else {
				model.setPrtFlag("X");
			}

			StringBuilder sb = new StringBuilder();
			String[] strs = Util.trim(model.getCustRelation()).split(",");
			// 對陣列進行排序
			Arrays.sort(strs);
			String custRelationIndex = "";
			for (String s : strs) {
				if (Util.equals(Util.trim(custRelationIndex), "")) {
					custRelationIndex = s;
				}
				if (sb.length() > 0)
					sb.append("/");
				sb.append(Util.trim(pop.getProperty("L1205S07.checkbox" + s)));
			}
			model.setCustRelationIndex(sb.toString());
			model.setCustRelation(custRelationIndex);

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S04AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s04b(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);

		String mainid = Util.trim(params.getString("mainId"));
		// J-109-0370 相關評估改版
		boolean needCustId = params.getBoolean("needCustId");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		if (needCustId) {
			String keyCustId = Util.trim(params.getString("keyCustId"));
			String keyDupNo = Util.trim(params.getString("keyDupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyCustId",
					keyCustId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo",
					keyDupNo);
		}
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S04B.class,
				pageSetting);
		List<L120S04B> list = (List<L120S04B>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S04B model = list.get(i);
			if (Util.equals(model.getDocKind(), "A")) {
				// L1205S07.grid48=共借戶與本行往來實績彙總表
				model.setRptName(pop.getProperty("L1205S07.grid48"));
			} else {
				// L1205S07.grid43=借戶暨關係戶與本行往來實績彙總表
				model.setRptName(pop.getProperty("L1205S07.grid43"));
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S05BGrid 資料(集團)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05b(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S05B.class,
				pageSetting);
		List<L120S05B> list = (List<L120S05B>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S05B model = list.get(i);
			StringBuilder strBuf = new StringBuilder();
			strBuf.append(model.getCustId()).append(model.getDupNo())
					.append(" ").append(model.getCustName());
			model.setCustName(strBuf.toString());
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S05DGrid 資料(關係企業)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05d(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S05D.class,
				pageSetting);
		List<L120S05D> list = (List<L120S05D>) page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S06AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s06a(ISearch pageSetting, PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS07APanel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S06A.class,
				pageSetting);
		List<L120S06A> list = (List<L120S06A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S06A model = list.get(i);
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			StringBuilder str1 = new StringBuilder();
			StringBuilder str2 = new StringBuilder();
			str1.append(model.getCustId()).append(" ").append(model.getDupNo())
					.append(" ").append(model.getCustName());
			str2.append(model.getCustId2()).append(" ")
					.append(model.getDupNo2()).append(" ")
					.append(model.getCustName2());
			model.setCustId(str1.toString());
			model.setCustId2(str2.toString());
			if ("1".equals(model.getPrintMode())) {
				model.setPrintMode(pop.getProperty("L1205S07.grid1"));
			} else {
				model.setPrintMode(pop.getProperty("L1205S07.grid2"));
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L140SM01AGrid 資料(對照)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL140m01a2(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String custId = Util.trim(params.getString("custId"));
		String caseBrid = Util.trim(params.getString("textBrid"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getLihai(custId, caseBrid,
				pageSetting);
		List<Map<String, Object>> list = (List<Map<String, Object>>) page
				.getContent();
		for (Map<String, Object> map : list) {
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			sb.append("docStatus.");
			map.put("docStatus",
					getMessage(sb.append(Util.trim(map.get("docStatus")))
							.toString()));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 根據後端相關身份值找出相對應的名稱
	 * 
	 * @param model
	 *            L120S01A
	 * @return String
	 */
	public String findCustPos(L120S01A model) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		if (Util.trim(model.getCustPos()).length() != 0) {
			switch (model.getCustPos().toCharArray()[0]) {
			case 'C':
				return pop.getProperty("L1205G.grid4");
			case 'D':
				return pop.getProperty("L1205G.grid5");
			case 'G':
				return pop.getProperty("L1205G.grid6");
			case 'N':
				return pop.getProperty("L1205G.grid7");
			case 'S':
				return pop.getProperty("L1205G.grid8");
			default:
				return "";
			}
		}
		return "";
	}

	/**
	 * 查詢L120S01AGrid 資料(原始)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01aOrigin(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S01A.class,
				pageSetting);
		// Page<L120S01A> page = service.getPage(pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 依照案件別代碼取得相對應案件別名稱
	 * 
	 * @param doccode
	 *            String
	 * @return Properties
	 */
	public String docCodeName(String doccode) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		if ("1".equals(doccode)) {
			return pop.getProperty("L1205G.grid9");
		} else if ("2".equals(doccode)) {
			return pop.getProperty("L1205G.grid10");
		} else {
			return pop.getProperty("L1205G.grid11");
		}
	}

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String fieldId = params.getString("fieldId");
		boolean needCngName = params.getBoolean("needCngName");
		boolean needBranch = params.getBoolean("needBranch");

		String flagOnlyNull = Util
				.nullToSpace(params.getString("flagOnlyNull"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);

		// J-112-0508_05097_B1001 Web e-Loan為提升授信簽報效率,
		// 三大部及授審處可由eloan授信系統依據授審會及常董會提案稿所需文件之順序產生相關提案稿pdf
		if (Util.equals(flagOnlyNull, "Y")) {
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "flag", "");
		}

		Page<DocFile> page = docfileservice.readToGrid(pageSetting);
		List<DocFile> list = (List<DocFile>) page.getContent();
		if (needCngName) {
			for (DocFile file : list) {
				// other.msg61=借戶暨關係戶與本行授信往來比較表
				// J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
				if (Util.equals(Util.trim(file.getSrcFileName()),
						"LMS1205R24A_B.xls")) {
					file.setSrcFileName(pop.getProperty("other.msg61_B")
							+ ".xls");
				} else {
					file.setSrcFileName(pop.getProperty("other.msg61") + ".xls");
				}

			}
		}
		if (needBranch) {
			// 需要分行名稱(透過CRYEAR欄位顯示)
			for (DocFile file : list) {
				file.setCrYear(Util.trim(file.getBranchId())
						+ " "
						+ branchService.getBranchName(Util.trim(file
								.getBranchId())));
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢檔案上傳的grid FOR CES來的檔案
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryfileFromCES(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		String fieldId = params.getString("fieldId");
		if (fieldId.contains(",")) {
			String[] fieldIds = fieldId.split(",");
			pageSetting.addSearchModeParameters(SearchMode.IN, "fieldId",
					fieldIds);
		} else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
					fieldId);
		}
		Page<DocFile> page = docfileservice.readToGrid(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢需列印的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryPrint(ISearch pageSetting,	PageParameters params) throws CapException {
		String printCondition = Util.nullToSpace(params
				.getString("printCondition"));
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = service1201.getBorrows(mainId,
				printCondition, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢 登錄主要負責人連保人資信狀況資料 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryViewA(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01e.docType", "4");
		Page page = service1201.getC140M04APage(pageSetting);
		Map formatter = new HashMap();
		formatter.put("pcTitle", new CodeTypeFormatter(codeservice, "Title2"));
		formatter.put("pcType", new I18NFormatter("pcType."));
		formatter.put("pcSex", new I18NFormatter("pcSex."));
		List<C140M04A> c140m04as = (List<C140M04A>) page.getContent();
		List<L120M01E> listL120m01e = service1201.findL120m01eByMainId(mainId);
		List<L120S01A> listL120s01a = service1201.findL120s01aByMainId(mainId);
		if (!listL120m01e.isEmpty()) {
			for (L120M01E l120m01e : listL120m01e) {
				for (C140M04A model : c140m04as) {
					if (model.equals(l120m01e.getC140m04a())) {
						if (l120m01e != null) {
							String custId = model.getL120m01e().getDocCustId();
							String dupNo = model.getL120m01e().getDocDupNo();
							String custName = null;
							// 設定借款人中文名稱
							for (L120S01A l120s01a : listL120s01a) {
								if (custId.equals(Util.trim(l120s01a
										.getCustId()))
										&& dupNo.equals(Util.trim(l120s01a
												.getDupNo()))) {
									custName = Util
											.trim(l120s01a.getCustName());
									break;
								}
							}
							model.getL120m01e().setDocCustId(
									custId + " " + dupNo);
							model.getL120m01e()
									.setDocDupNo(Util.trim(custName));
						}
						break;
					}
				}
			}
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 經營事業 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SA(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = service1201.getC140S04APage(pageSetting);
		Map formatter = new HashMap();
		for (C140S04A model : (List<C140S04A>) page.getContent()) {
			if (!Util.isEmpty(model.getInvCap21())) {
				model.setInvCap21(model.getInvCap21().setScale(0));
			}
			if (!Util.isEmpty(model.getAmtUnitST())) {
				model.setAmtUnitST(model.getAmtUnitST().setScale(0));
			}
		}
		formatter.put("invCap11", new CodeTypeFormatter(codeservice,
				"Common_Currcy"));
		formatter.put("invCap21",
				new CodeTypeFormatter(codeservice, "CurrUnit"));
		formatter.put("amtUnitST", new CodeTypeFormatter(codeservice,
				"CurrUnit"));
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 本人之土地 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SB(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = service1201.getC140S04BPage(pageSetting);
		Map formatter = new HashMap();
		formatter.put("landUse", new landUse1());
		formatter.put("landLevel", new CodeTypeFormatter(codeservice,
				"LandLevel"));
		formatter.put("landRate", new landRate());
		formatter.put("landMp", new landMp());
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 本人之建物 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SC(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = service1201.getC140S04CPage(pageSetting);
		Map formatter = new HashMap();
		formatter.put("buUse", new CodeTypeFormatter(codeservice, "BuUse"));
		formatter.put("buStru", new CodeTypeFormatter(codeservice, "BuStru"));
		formatter.put("buMp", new buMp());
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryView(ISearch pageSetting, PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		String type = CapString.trimNull(params.getString("gridType"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);

		CapGridResult result = new CapGridResult();

		// 取得資料
		if ("A".equals(type)) {
			Page<C140S09A> page = service1201.getC140S09APage(pageSetting);
			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			formatter.put("gBal", new gBal());

			List<C140S09A> s09as1 = new ArrayList<C140S09A>();
			List<C140S09A> s09as2 = new ArrayList<C140S09A>();
			List<C140S09A> s09as3 = new ArrayList<C140S09A>();
			// 先排GID公司戶,
			for (C140S09A s09a : page.getContent()) {
				if (s09a.getGId() != null
						&& CapString.trimNull(s09a.getGId()).length() <= 8)
					s09as1.add(s09a);
			}
			Collections.sort(s09as1, new Comparator<C140S09A>() {
				public int compare(C140S09A o1, C140S09A o2) {
					Integer ii = (o1.getGNa().compareTo(o2.getGNa()));
					return ii;
				}
			});
			// 在排GID為空，但企業名稱有值
			for (C140S09A s09a : page.getContent()) {
				if (CapString.isEmpty(s09a.getGId()))
					s09as2.add(s09a);
			}
			Collections.sort(s09as2, new Comparator<C140S09A>() {
				public int compare(C140S09A o1, C140S09A o2) {
					Integer ii = (o1.getGNa().compareTo(o2.getGNa()));
					return ii;
				}
			});
			// 排非公司戶GID
			for (C140S09A s09a : page.getContent()) {
				if (s09a.getGId() != null
						&& CapString.trimNull(s09a.getGId()).length() > 8)
					s09as3.add(s09a);
			}
			Collections.sort(s09as3, new Comparator<C140S09A>() {
				public int compare(C140S09A o1, C140S09A o2) {
					Integer ii = (o1.getGNa().compareTo(o2.getGNa()));
					return ii;
				}
			});

			List<C140S09A> s09as = new ArrayList<C140S09A>();
			s09as.addAll(s09as1);
			s09as.addAll(s09as2);
			s09as.addAll(s09as3);

			result = new CapGridResult(s09as, page.getTotalRow(), formatter);
		} else if ("B".equals(type)) {
			Page<C140S09B> page = service1201.getC140S09BPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("C".equals(type)) {
			Page<C140S09C> page = service1201.getC140S09CPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("D".equals(type)) {
			Page<C140S09D> page = service1201.getC140S09DPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("E".equals(type)) {
			Page<C140S09E> page = service1201.getC140S09EPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("F".equals(type)) {
			Page<C140S09F> page = service1201.getC140S09FPage(pageSetting);

			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			formatter.put("linv14", new CodeTypeFormatter(codeservice,
					"Common_Currcy", CodeTypeFormatter.ShowTypeEnum.Val_Desc));
			result = new CapGridResult(page.getContent(), page.getTotalRow(),
					formatter);
		}

		return result;
	}

	/**
	 * gBal formatter
	 */
	class gBal implements IBeanFormatter {
		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S09A c140s09a = (C140S09A) in;
			String GMbkBal = c140s09a.getGMbkBal() == null ? "" : c140s09a
					.getGMbkBal().toPlainString();
			String GObuBal = c140s09a.getGObuBal() == null ? "" : c140s09a
					.getGObuBal().toPlainString();
			String GOvsBal = c140s09a.getGOvsBal() == null ? "" : c140s09a
					.getGOvsBal().toPlainString();
			return CapMath.add(new String[] { GMbkBal, GObuBal, GOvsBal });
		}
	}

	/**
	 * buMp formatter (buMp + "/" + buMm)
	 */
	class buMp implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04C meta = (C140S04C) in;

			return new StringBuffer()
					.append(CapString.trimNull(meta.getBuMp()))
					.append(" / ")
					.append(meta.getBuCurr())
					.append(new NumericFormatter().reformat(meta.getBuMm()))
					.append(new CodeTypeFormatter(codeservice, "CurrUnit")
							.reformat(meta.getBuAmtUnit())).toString();
		}
	}

	/**
	 * landUse1 formatter
	 */
	class landUse1 implements IBeanFormatter {

		private static final long serialVersionUID = 1L;
		Map<String, String> LandUse1 = null;
		Map<String, Map<String, String>> LandUse2 = new HashMap<String, Map<String, String>>();

		public landUse1() {
			LandUse1 = codeservice.findByCodeType("LandUse1");
		}

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04B meta = (C140S04B) in;
			Map<String, String> iiLandUse2 = new HashMap<String, String>();
			if (!CapString.isEmpty(meta.getLandUse1())) {
				if (!LandUse2.containsKey(meta.getLandUse1())) {
					iiLandUse2 = codeservice.findByCodeType("LandUse2"
							+ meta.getLandUse1());
					LandUse2.put(meta.getLandUse1(), iiLandUse2);
				} else {
					iiLandUse2 = LandUse2.get(meta.getLandUse1());
				}

				return new StringBuffer(LandUse1.get(meta.getLandUse1()))
						.append("/").append(iiLandUse2.get(meta.getLandUse2()))
						.toString();
			}

			return EloanConstants.EMPTY_STRING;
		}
	}

	/**
	 * landRate formatter
	 */
	class landRate implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04B meta = (C140S04B) in;
			StringBuffer sb = new StringBuffer();

			if (meta.getLandRateC() != null) {
				sb.append(meta.getLandRateC());
			}

			sb.append('/');

			if (meta.getLandRateD() != null) {
				sb.append(meta.getLandRateD());
			}

			return sb.toString();
		}
	}

	/**
	 * landMp formatter (landMp + "/" + landMm)
	 */
	class landMp implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04B meta = (C140S04B) in;

			return new StringBuffer()
					.append(CapString.trimNull(meta.getLandMp()))
					.append(" / ")
					.append(meta.getLandCurr())
					.append(new NumericFormatter().reformat(meta.getLandMm()))
					.append(new CodeTypeFormatter(codeservice, "CurrUnit")
							.reformat(meta.getLandAmtUnit())).toString();
		}
	}

	/**
	 * 查詢待列印的一般財務報表。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryIncludeFSS(ISearch pageSetting, PageParameters params) throws CapException {
		String gaapFlag = params.getString("gaapFlag",
				GaapFlagEnum.GAAP.getCode()); // 2013.03.07 Mike Add 整合IFRS財報引進
		String getBranch = CapString.trimNull(params.getString("qryBranch"));
		if (CapString.isEmpty(getBranch)) {
			getBranch = MegaSSOSecurityContext.getUnitNo();
		}
		if (!CapString.isEmpty(params.getString("fssCustId", null))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					params.getString("fssCustId"));
		}
		if (!CapString.isEmpty(params.getString("fssDupNo", null))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					params.getString("fssDupNo"));
		}
		String type = CapString.trimNull(params.getString("type"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		// add ifrs 合併報表別
		// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
		if (!CapString.isEmpty(params.getString(
				(GaapFlagEnum.IFRS.isEquals(gaapFlag) || GaapFlagEnum.EAS
						.isEquals(gaapFlag)) ? "conso" : "fssConso", null))) {
			pageSetting
					.addSearchModeParameters(
							SearchMode.EQUALS,
							"conso",
							params.getString((GaapFlagEnum.IFRS
									.isEquals(gaapFlag) || GaapFlagEnum.EAS
									.isEquals(gaapFlag)) ? "conso" : "fssConso"));
		}

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS,
						CESConstant.DOC_STATUS,
						params.getString("fssDocStatus", "230"));
		if (params.getAsBoolean("fssPeriodType", false)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"periodType", PeriodTypeEnum.YEAR.getCode());
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"f101a01as.authUnit", getBranch);
		pageSetting.addSearchModeParameters(SearchMode.IN,
				"f101a01as.authType",
				new String[] { DocAuthTypeEnum.MODIFY.getCode(),
						DocAuthTypeEnum.VIEW_TRANSFER.getCode() });

		if (!CapString.isEmpty(params.getString("fssSource", null))) {
			String fssSource = params.getString("fssSource");
			if (fssSource.indexOf("|") > 0) {
				String[] split = fssSource.split("\\" + "|");
				pageSetting.addSearchModeParameters(SearchMode.IN, "source",
						split);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"source", fssSource);
			}
		}
		// 增加GAAP會計準則
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "gaapFlag",
				gaapFlag);
		Page<F101M01A> page = gridService.getF1010V01(pageSetting);
		// Page<Map<String, Object>> page = service1201.getFss(getBranch,
		// fssCustId, fssDupNo, pageSetting);

		// formatter
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("custId", new CustIdFormatter());
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("approver", new UserNameFormatter(userservice));
		IFormatter myFmt = new IFormatter() {
			private static final long serialVersionUID = 1L;

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				return "1".equals(in) ? "V" : "";
			}
		};

		// 2013.03.07 Mike Add 整合IFRS財報引進 Start
		if (GaapFlagEnum.IFRS.isEquals(gaapFlag)) {
			map.put("conso", new CodeTypeFormatter(codeservice, "IFRSConso"));
		} else if (GaapFlagEnum.EAS.isEquals(gaapFlag)) {
			// J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
			map.put("conso", new CodeTypeFormatter(codeservice, "IFRSConso"));
		} else {
			map.put("conso", myFmt);
		}
		// 2013.03.07 Mike Add 整合IFRS財報引進 End

		// map.put("conso", myFmt);
		map.put("inFlag", myFmt);
		map.put("publicFlag", myFmt);
		map.put("source", new CodeTypeFormatter(codeservice, "FssSource"));
		return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	}

	/**
	 * Mow模型評等Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryMowTrust(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120S01A l120s01a = service1201.findL120s01aByOid(thisOid);
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getMowTrust(custId, dupNo,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢列印營運中心意見
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryPrintArea(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				"hqReceiveDate", "");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "areaChk", "3");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);
		StringBuilder allCust = new StringBuilder();
		StringBuilder docName = new StringBuilder();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			allCust.setLength(0);
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo()).append(" ")
					.append(model.getCustName());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			model.setDocKind(this.getCaseType(model, pop, docName));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(this.getUserName(model.getUpdater()));
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(this.getUserName(model
						.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(Util.trim(model.getAreaAppraiser()));
			}
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(this.getUserName(model.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setApproveTime(model.getAreaSendInfo());
			}

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得 案件類別名稱
	 * 
	 * @param model
	 *            簽報書主檔
	 * @param pop
	 *            語系檔
	 * @param temp
	 *            暫存的stringBuffer
	 * 
	 * @return
	 */
	private String getCaseType(L120M01A model, Properties pop, StringBuilder temp) {
		String areaTitle = null;
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		temp.setLength(0);
		if (UtilConstants.Casedoc.DocType.企金.equals(model.getDocType())) {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				String authLvl = Util.trim(model.getAuthLvl());
				if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(authLvl)) {
					// L1205G.grid0=營運中心授權內
					temp.append(pop.getProperty("L1205G.grid0"));
				} else if (UtilConstants.Casedoc.AuthLvl.總行授權內.equals(authLvl)) {
					// L1205G.grid14=母行授權內
					temp.append(pop.getProperty("L1205G.grid14"));
				} else {
					// L1205G.grid1=分行授權內
					temp.append(pop.getProperty("L1205G.grid1"));
				}
			} else {
				areaTitle = queryAreaTitle(model);
				if (Util.isNotEmpty(areaTitle)) {
					temp.append(areaTitle);
				} else {
					temp.append(pop.getProperty("L1205G.grid2"));
				}
			}
		} else {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				temp.append(pop.getProperty("L1205G.grid12"));
			} else {
				areaTitle = queryAreaTitle(model);
				if (Util.isNotEmpty(areaTitle)) {
					temp.append(areaTitle);
				} else {
					temp.append(pop.getProperty("L1205G.grid13"));
				}
			}
		}
		// L1205G.grid9=一般
		// L1205G.grid10=其他
		// L1205G.grid11=陳復/陳述案
		temp.append("(");
		if (UtilConstants.Casedoc.DocCode.一般.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid9"));
		} else if (UtilConstants.Casedoc.DocCode.其他.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid10"));
		} else {
			if (UtilConstants.Casedoc.DocCode.異常通報.equals(Util.trim(model
					.getDocCode()))) {
				// other.msg59=異常通報案件
				temp.append(pop2.getProperty("other.msg59"));
			} else {
				temp.append(pop.getProperty("L1205G.grid11"));
			}
		}
		temp.append(")");
		return temp.toString();
	}

	/**
	 * 取得國內屬營運中心制分行的標題名稱
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return
	 * @throws CapException
	 */
	private String queryAreaTitle(L120M01A l120m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch tBranch = branchService.getBranch((l120m01a != null) ? Util
				.trim(l120m01a.getCaseBrId()) : user.getUnitNo());
		String docKind = Util.trim(l120m01a.getDocKind());
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (tBranch != null) {
			String brnGroup = Util.trim(tBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				if (UtilConstants.Casedoc.DocKind.授權外.equals(docKind)) {
					/*
					 * 因為海外分行不屬於營運中心制，所以提醒第四階段，國內屬營運中心制分行時TITLE顯示會有差異
					 * 國內營運中心制分行，分行授權外案件會顯示營運中心授權外案件簽報書
					 */
					// other.msg131=營運中心授權外
					return pop.getProperty("other.msg131");
				}
			}
		}
		return null;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userservice.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 查詢使用者自訂範本Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL720m01a(ISearch pageSetting, PageParameters params) throws CapException {

		// 建立主要Search 條件
		// pageSetting.addOrderBy("patternNM");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L720M01A.class,
				pageSetting);

		List<L720M01A> list = (List<L720M01A>) page.getContent();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				L720M01A model = list.get(i);
				StringBuilder fullUp = new StringBuilder();
				fullUp.append(getPerName(model.getUpdater())).append(" (")
						.append(TWNDate.toFullTW(model.getUpdateTime()))
						.append(")");
				model.setUpdater(fullUp.toString());
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢異常通報事項Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL130S01a(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 建立主要Search 條件
		String mainId = params.getString(EloanConstants.MAIN_ID);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L130S01A.class,
				pageSetting);

		List<L130S01A> list = (List<L130S01A>) page.getContent();
		// 格式化Grid內容
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				L130S01A model = list.get(i);
				String seqKind = Util.trim(model.getSeqKind());
				String headMonth = Util.trim(model.getHeadMonth());
				String areaMonth = Util.trim(model.getAreaMonth());
				// other.msg18=已辦
				// other.msg19=擬辦
				// other.msg20=其他
				if (UtilConstants.seqKind.擬辦.equals(seqKind)) {
					model.setSeqKind(pop.getProperty("other.msg19"));
				} else if (UtilConstants.seqKind.已辦.equals(seqKind)) {
					model.setSeqKind(pop.getProperty("other.msg18"));
				} else if (UtilConstants.seqKind.其他.equals(seqKind)) {
					model.setSeqKind(pop.getProperty("other.msg20"));
				} else if (UtilConstants.seqKind.空白.equals(seqKind)) {
					model.setSeqKind(UtilConstants.Mark.SPACE);
				}

				if (Util.isNotEmpty(headMonth)) {
					model.setHeadMonth(headMonth);
				} else if (Util.isNotEmpty(areaMonth)) {
					model.setHeadMonth(areaMonth);
				} else {
					model.setHeadMonth(UtilConstants.Mark.SPACE);
				}
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL130S02A(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMSM02BPanel.class);
		// 建立主要Search 條件
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String seqNo = Util.trim(params.getString("seqNo"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "seqNo", seqNo);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L130S02A.class,
				pageSetting);

		List<L130S02A> list = (List<L130S02A>) page.getContent();
		// 格式化Grid內容
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				L130S02A model = list.get(i);
				String controlType = Util.trim(model.getCtlType());
				if (Util.equals("1", controlType)) {
					model.setCtlType(prop.getProperty("L130S02A.ctlType.1"));
				} else if (Util.equals("2", controlType)) {
					model.setCtlType(prop.getProperty("L130S02A.ctlType.2"));
				} else if (Util.equals("3", controlType)) {
					model.setCtlType(prop.getProperty("L130S02A.ctlType.3"));
				}
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	private Map<String, String> _l130s02a_idDupNameMap(ISearch pageSetting,	String mainId) {
		Map<String, String> r = new LinkedHashMap<String, String>();
		boolean use_c120m01a = false;
		if (true) {
			L120M01A l120m01a = lmsService.findModelByMainId(L120M01A.class,
					mainId);
			if (l120m01a != null
					&& Util.equals(UtilConstants.Casedoc.DocType.個金,
							l120m01a.getDocType())) {
				use_c120m01a = true;
			}
		}
		if (true) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
					mainId);
		}
		Page<?> page = lmsService.findPage(use_c120m01a ? C120M01A.class
				: L120S01A.class, pageSetting);
		List<?> raw_list = page.getContent();
		// 格式化Grid內容
		if (!raw_list.isEmpty()) {
			for (int i = 0; i < raw_list.size(); i++) {
				String custId = "";
				String dupNo = "";
				String name = "";
				if (use_c120m01a) {
					C120M01A model = (C120M01A) raw_list.get(i);
					custId = model.getCustId();
					dupNo = model.getDupNo();
					name = model.getCustName();
				} else {
					L120S01A model = (L120S01A) raw_list.get(i);
					custId = model.getCustId();
					dupNo = model.getDupNo();
					name = model.getCustName();
				}

				r.put(LMSUtil.getCustKey_len10custId(custId, dupNo),
						Util.trim(name));
			}
		}
		return r;
	}

	public CapMapGridResult queryL130S02A_cntrNo(ISearch pageSetting, PageParameters params) throws CapException {

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		Map<String, String> idDupNameMap = _l130s02a_idDupNameMap(pageSetting,
				mainId);

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (String idDup : idDupNameMap.keySet()) {
			String name = idDupNameMap.get(idDup);

			List<Map<String, Object>> lnf020_list = misMISLN20Service
					.findByCustIdDupNoIncludeCancel(idDup);
			LinkedHashSet<String> cntrNoSet = new LinkedHashSet<String>();
			for (Map<String, Object> row : lnf020_list) {
				String cntrNo = MapUtils.getString(row, "LNF020_CONTRACT");
				Date cancelDate = (Date) MapUtils.getObject(row,
						"LNF020_CANCEL_DATE");
				if (cancelDate == null) {
					cntrNoSet.add(cntrNo);
				}
			}
			if (cntrNoSet.size() > 0) {
				for (String cntrNo : cntrNoSet) {
					Map<String, Object> m = new HashMap<String, Object>();
					// ~~~
					m.put("idDup", idDup);
					m.put("name", name);
					m.put("cntrNo", cntrNo);
					// ~~~
					list.add(m);
				}
			}
		}
		return new CapMapGridResult(list, list.size());
	}

	public CapMapGridResult queryL130S02A_idDup(ISearch pageSetting, PageParameters params) throws CapException {

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		Map<String, String> idDupNameMap = _l130s02a_idDupNameMap(pageSetting,
				mainId);

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (String idDup : idDupNameMap.keySet()) {
			String name = idDupNameMap.get(idDup);

			Map<String, Object> m = new HashMap<String, Object>();
			// ~~~
			m.put("idDup", idDup);
			m.put("name", name);
			// ~~~
			list.add(m);
		}
		return new CapMapGridResult(list, list.size());
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id) {
		return (!Util.isEmpty(userservice.getUserName(id)) ? userservice
				.getUserName(id) : id);
	}

	/**
	 * 將指定字串轉成 (字串 + " " + 字串對應名稱)格式
	 * 
	 * @param str
	 *            指定字串
	 * @return 轉換後格式
	 */
	public String withIdName(String str) {
		StringBuilder sb = new StringBuilder();
		sb.append(Util.addZeroWithValue(Util.trim(str), 6)).append(" ")
				.append(getPerName(Util.trim(str)));
		return sb.toString();
	}

	/**
	 * 查詢擔保品 MainId(by 分行、by 簽報書主要借款人、by 擔保品大類)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCmsMainId(ISearch pageSetting,	PageParameters params) throws CapException {
		Page<Map<String, Object>> page = null;

		// Grid種類
		String type = Util.trim(params.getString("type"));

		if (type.equals("1")) {
			// 建立主要Search 條件
			String branchId = Util.trim(params.getString("branchId"));
			// 第三個參數為formatting
			page = service1201.getCmsMainId(branchId, pageSetting);
		} else if (type.equals("2")) {
			// 建立主要Search 條件
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			// 第三個參數為formatting
			page = service1201.getCmsMainId2(mainId, pageSetting);
		} else if (type.equals("3")) {
			// 建立主要Search 條件
			String collTyp1 = Util.trim(params.getString("collTyp1"));
			// 第三個參數為formatting
			page = service1201.getCmsMainId3(collTyp1, pageSetting);
		}

		if (page == null) {
			// 查無資料
			return new CapMapGridResult(new ArrayList<Map<String, Object>>(), 0);
		}

		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			sb.append(Util.trim(map.get("custId"))).append(" ")
					.append(Util.trim(map.get("dupNo"))).append(" ")
					.append(Util.trim(map.get("custName")));
			map.put("collKind",
					codeservice.getDescOfCodeType("cms1090_collTyp1",
							Util.trim(map.get("collTyp1"))));
			map.put("custName", sb.toString());
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? "" : CMSDocStatusEnum
							.getMessage(Util.trim(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
			map.put("megaAmt", Util.isEmpty(map.get("megaAmt")) ? ""
					: NumConverter.delComma(Util.trim(map.get("megaAmt"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 清除擔保品Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult beforeClearCMS(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID, ""));
		// 建立主要Search 條件
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.L120m01eDocType.擔保品估價報告書);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01E.class,
				pageSetting);

		// List<L120M01E> list = (List<L120M01E>) page.getContent();
		// if (!list.isEmpty()) {
		// for (int i = 0; i < list.size(); i++) {
		// L120M01E model = list.get(i);
		// StringBuilder fullUp = new StringBuilder();
		// fullUp.append(getPerName(model.getUpdater())).append(" (")
		// .append(TWNDate.toFullTW(model.getUpdateTime()))
		// .append(")");
		// model.setUpdater(fullUp.toString());
		// }
		// }
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢擔保品
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            <pre>
	 *            colltyp1 擔保品大類 
	 *            custId 客戶統編 
	 *            dupNo 重覆序號 
	 *            branch 分行代號
	 * </pre>
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCMS(ISearch pageSetting, PageParameters params) throws CapException {
		String colltyp1 = Util.trim(params.getString("collTyp1", ""));
		String custId = Util.trim(params.getString("cmsCustId", ""));
		String dupNo = Util.trim(params.getString("cmsDupNo", ""));
		String branch = Util.trim(params.getString("branchId", "005"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "collTyp1",
				colltyp1);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		pageSetting.addSearchModeParameters(
				SearchMode.IN,
				EloanConstants.DOC_STATUS,
				new String[] { CMSDocStatusEnum.分行_編製中.getCode(),
						CMSDocStatusEnum.分行_待覆核.getCode(),
						CMSDocStatusEnum.分行_已覆核.getCode(),
						CMSDocStatusEnum.分行_待設質.getCode(),
						CMSDocStatusEnum.分行_已設質.getCode(),
						CMSDocStatusEnum.分行_待塗銷.getCode(),
						CMSDocStatusEnum.聯行傳回.getCode(),
						CMSDocStatusEnum.代鑑價編製中.getCode(),
						CMSDocStatusEnum.代鑑價待覆核.getCode(),
						CMSDocStatusEnum.代鑑價已完成.getCode(),
						CMSDocStatusEnum.營運中心_編製中.getCode(),
						CMSDocStatusEnum.營運中心_待覆核.getCode(),
						CMSDocStatusEnum.營運中心_已覆核.getCode(),
						CMSDocStatusEnum.營運中心_已傳回.getCode(),
						CMSDocStatusEnum.營運中心_待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核編制中.getCode(),
						CMSDocStatusEnum.營運中心_覆核待覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已傳回.getCode(),
						CMSDocStatusEnum.待斷頭.getCode(),
						CMSDocStatusEnum.已斷頭.getCode(),
						CMSDocStatusEnum.補提.getCode(),
						CMSDocStatusEnum.擔保率不足.getCode(),
						CMSDocStatusEnum.授管處_編製中.getCode(),
						CMSDocStatusEnum.授管處_待覆核.getCode(),
						CMSDocStatusEnum.授管處_已覆核.getCode()

				});
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		Page<? extends GenericBean> page = lmsService.findPage(C100M01.class,
				pageSetting);
		List<C100M01> c100m01s = (List<C100M01>) page.getContent();
		for (C100M01 model : c100m01s) {
			model.setAppraiserName(userservice.getUserName(model.getAppraiser()));
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService, ShowTypeEnum.Name));
		dataReformatter.put("collTyp1", new CodeTypeFormatter(codeservice, "lmsUseCms_collTyp1"));
		dataReformatter.put("docStatus", new I18NFormatter("status."));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢 逾催案件報告表 Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCaseInfo(ISearch pageSetting, PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("gridMainId"));

		List<Map<String, Object>> s104m01aList = colService
				.getS104M01AList(mainId);

		CapMapGridResult m01Result = new CapMapGridResult(s104m01aList,
				s104m01aList.size());

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("grantType", new CodeTypeFormatter(codeservice,
				"grantType"));

		for (Map<String, Object> map : s104m01aList) {
			map.put("custValue", map.get("custId") + " " + map.get("dupNo"));
		}

		m01Result.setDataReformatter(formatter);
		m01Result.setColumns(new String[] { "mainId", "pid", "branchId",
				"custId", "dupNo", "custName", "caseNo", "grantType" });

		return m01Result;

	}

	/**
	 * 查詢 逾催案件報告表之借款人輸入 Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryBorrower(ISearch pageSetting, PageParameters params) throws CapException {
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String oid = Util.nullToSpace(params.getString("oid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		L120M01A l120m01a = service1201.findL120m01aByOid(oid);

		Page<? extends GenericBean> page = service1201.findPage(L120S01A.class,
				pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();

		List<Map<String, Object>> m01List = new ArrayList<Map<String, Object>>();
		for (L120S01A model : list) {

			Map<String, Object> m01Map = new HashMap<String, Object>();
			m01Map.put("keyMan", "Y".equals(model.getKeyMan()) ? "*" : "");
			m01Map.put("custNo", model.getCustId() + " " + model.getDupNo());
			m01Map.put("custName", model.getCustName());
			m01Map.put("custId", model.getCustId());
			m01Map.put("dupNo", model.getDupNo());
			m01Map.put("oid", model.getOid());
			m01Map.put("mainId", model.getMainId());
			m01Map.put("caseNo", l120m01a.getCaseNo());
			m01Map.put("caseDate", l120m01a.getCaseDate());
			m01Map.put("colOid", IDGenerator.getUUID());
			m01List.add(m01Map);
		}

		CapMapGridResult m01Result = new CapMapGridResult(m01List, 1);
		m01Result.setColumns(new String[] { "keyMan", "custNo", "custName",
				"custId", "dupNo", "oid", "mainId", "caseNo", "caseDate",
				"colOid" });
		return m01Result;
	}

	/**
	 * 查詢文件 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryDocView(ISearch pageSetting, PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("mainId"));
		mainId = checkMainId(params, mainId);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "sysId", "LMS");
		// #1578 #330 附件分行問題
		// String branch = CapString.trimNull(params.getString("qryBranch"));
		// if(CapString.isEmpty(branch)){
		// //調整附件查詢，應該以C140M01A所屬分行為條件
		// // branch = MegaSSOSecurityContext.getUnitNo();
		// C140M01A meta = service.getC140M01AByMainId(mainId);
		// if(meta!=null)
		// branch = meta.getOwnBrId();
		// }
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
		// branch);
		String fieldId = CapString.trimNull(params.getString("fieldId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		// 取得資料
		Page<DocFile> page = docfileservice.readToGrid(pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	private String checkMainId(PageParameters params, String mainId) {
		if (CapString.isEmpty(mainId)
				&& !CapString.isEmpty(params.getString(CESConstant.MAIN_OID))) {
			C140M01A meta = service1201.getC140M01A(params
					.getString(CESConstant.MAIN_OID));
			if (meta != null) {
				mainId = meta.getMainId();
			}
		}
		return mainId;
	}

	/**
	 * 授管處列印該簽報書資信簡表Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult printCesGrid(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<L120M01E> list = service1201.findL120m01eByMainId(mainId);
		List<String> ces140MainIds = new ArrayList<String>();
		List<String> ces120MainIds = new ArrayList<String>();
		for (L120M01E l120m01e : list) {
			if (UtilConstants.Casedoc.L120m01eDocType.徵信報告書.equals(Util
					.trim(l120m01e.getDocType()))) {
				ces140MainIds.add(Util.trim(l120m01e.getDocOid()));
			} else if (UtilConstants.Casedoc.L120m01eDocType.資信簡表.equals(Util
					.trim(l120m01e.getDocType()))) {
				ces120MainIds.add(Util.trim(l120m01e.getDocOid()));
			}
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = lmsService.getCesPrint(ces140MainIds,
				ces120MainIds, pageSetting);
		List<Map<String, Object>> listData = page.getContent();
		for (Map<String, Object> map : listData) {
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			sb.append(Util.trim(map.get("cesFDate"))).append(" ")
					.append(Util.trim(map.get("sn"))).append(" ")
					.append(Util.trim(map.get("custName")));
			map.put("docDscr", sb.toString());
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 授管處列印該簽報書資信簡表Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult findL120M01E(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String docType = Util.trim(params.getString("docType"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				docType);

		Page<? extends GenericBean> page = service1201.findPage(L120M01E.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢文件 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCreditRisk(ISearch pageSetting, PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("mainId"));
		String custId = CapString.trimNull(params.getString("custId"));
		String dupNo = CapString.trimNull(params.getString("dupNo"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 取得資料
		Page<? extends GenericBean> page = service1201.findPage(L120S01M.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	/**
	 * 查詢文件 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s08a(ISearch pageSetting,	PageParameters params) throws CapException {

		String mainId = CapString.trimNull(params.getString("mainId"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// pageSetting.addOrderBy("seqNo");
		// 取得資料
		Page<? extends GenericBean> page = service1201.findPage(L120S08A.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	/**
	 * 查詢L120S09AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s09a(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting

		// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
		pageSetting.addOrderBy("seqNum");
		pageSetting.addOrderBy("checkSeq");
		pageSetting.addOrderBy("custRelation");
		pageSetting.addOrderBy("custId");
		pageSetting.addOrderBy("dupNo");
		pageSetting.addOrderBy("custEName");
		pageSetting.addOrderBy("custName");

		Page<? extends GenericBean> page = service1201.findPage(L120S09A.class,
				pageSetting);

		List<L120S09A> list = (List<L120S09A>) page.getContent();

		for (int i = 0; i < list.size(); i++) {
			L120S09A model = list.get(i);
			if (Util.isEmpty(model.getCustId())) {
				model.setCustId("");
			} else {
				model.setCustId(model.getCustId() + " " + model.getDupNo());
			}

			if ("".equals(Util.trim(model.getBlackListCode()))) {
				model.setBlackListCode("");
			} else if (UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單
					.equals(Util.trim(model.getBlackListCode()))) {
				model.setBlackListCode(UtilConstants.Casedoc.L120s09aBlackListShow.未列於黑名單);
			} else if (UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單
					.equals(Util.trim(model.getBlackListCode()))) {
				model.setBlackListCode(UtilConstants.Casedoc.L120s09aBlackListShow.可能是黑名單);
			} else {
				model.setBlackListCode(UtilConstants.Casedoc.L120s09aBlackListShow.是黑名單);
			}

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			if ("3".equals(Util.trim(model.getCm1AmlStatus()))) {
				model.setCm1AmlStatus(UtilConstants.Casedoc.L120s09aBlackListShow.拒絕交易);
			} else {
				model.setCm1AmlStatus("");
			}

			// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
			if (Util.isNotEmpty(Util.trim(model.getCmfwarnpResult()))) {
				String cmfwarnpResultDesc = pop
						.getProperty("L120S09a.cmfwarnpResult."
								+ Util.trim(model.getCmfwarnpResult()));
				if (UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.不適用
						.equals(model.getCmfwarnpResult())) {
					// L120S09a.cmfwarnpResult3.GridDesc = 維護中
					cmfwarnpResultDesc = pop
							.getProperty("L120S09a.cmfwarnpResult3.GridDesc");
				}
				model.setCmfwarnpResult(cmfwarnpResultDesc);
			} else {
				model.setCmfwarnpResult("");
			}

			StringBuilder sb = new StringBuilder();

			// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			// 不能CALL AMLRelateService 因為會觸發交易，導致model 會把修改後的值真的存起來
			String[] strs = this.sortCustRelation(Util.trim(
					model.getCustRelation()).split(","));

			String custRelationIndex = "";
			for (String s : strs) {
				if (Util.equals(Util.trim(custRelationIndex), "")) {
					custRelationIndex = s;
				}
				if (sb.length() > 0)
					sb.append("/");
				sb.append(Util.trim(pop.getProperty("L120S09a.checkbox" + s)));
			}
			model.setCustRelationIndex(sb.toString());
			model.setCustRelation(custRelationIndex);

		}

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S09CGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s09c(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);

		Page<? extends GenericBean> page = service1201.findPage(L120S09C.class,
				pageSetting);

		Map<String, IFormatter> fmtMap = new HashMap<String, IFormatter>();
		fmtMap.put("caseType", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String caseType = "";
				L120S09C l120s09c = (L120S09C) in;
				// 分版本caseType要抓不同組的codeType
				// 11311跟11307長一模一樣
				if ("11311".equals(l120s09c.getVersionDate()) || "11307".equals(l120s09c.getVersionDate())) {
					caseType = codeservice.getDescOfCodeType(
							"lmss20a_caseTypeV11307",
							Util.trim(l120s09c.getCaseType()));
				}else if ("11107".equals(l120s09c.getVersionDate())) {
					caseType = codeservice.getDescOfCodeType(
							"lmss20a_caseTypeV11107",
							Util.trim(l120s09c.getCaseType()));
				} else {
					caseType = codeservice.getDescOfCodeType(
							"lmss20a_caseType",
							Util.trim(l120s09c.getCaseType()));
				}
				return caseType;
			}
		});

		return new CapGridResult(page.getContent(), page.getTotalRow(), fmtMap);
	}

	/**
	 * 查詢連保人Grid 資料 J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01p(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String type = Util.nullToSpace(params.getString("type"));

		String rType = Util.nullToSpace(params.getString("rType"));

		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);

		Page<? extends GenericBean> page = service1201.findPage(L120S01P.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	public String[] sortCustRelation(String[] strs) {

		// 對陣列進行排序
		// 轉成數字後再SORT
		List<String> asList = Arrays.asList(strs);
		List<Integer> newList = new ArrayList();

		for (String str : asList) {
			if (Util.notEquals(str, "")) {
				newList.add(new BigDecimal(str).intValue());
			}
		}

		int index = 0;
		int[] newIntArr = new int[newList.toArray().length];
		for (Integer xInt : newList) {
			newIntArr[index] = xInt;
			index++;
		}

		// 對陣列進行排序
		Arrays.sort(newIntArr);

		// 回復原來的陣列
		index = 0;
		String[] rtnList = new String[newList.toArray().length];
		for (Integer intX : newIntArr) {

			String newStr = new BigDecimal(intX).toPlainString();
			rtnList[index] = newStr;
			index++;

		}

		return rtnList;
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01jCustTotal(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String type = Util.nullToSpace(params.getString("type"));
		String custId2 = "9999999999";
		String dupNo2 = "9";

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId2",
				custId2);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);

		pageSetting.addOrderBy("printSeq", false);

		Page<? extends GenericBean> page = service1201.findPage(L120M01J.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01jByCustId(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String type = Util.nullToSpace(params.getString("type"));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addOrderBy("itemSeq", false);
		Page<? extends GenericBean> page = service1201.findPage(L120M01J.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	/**
	 * 取得整份簽報書所引用的個金評等
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryC120m01a(ISearch pageSetting,	PageParameters params) throws CapException {

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				Util.trim(params.getString(EloanConstants.MAIN_ID)));

		Map<String, Boolean> orderByMap = pageSetting.getOrderBy();
		if (orderByMap != null && orderByMap.containsKey("custNumber")) {
			orderByMap.put("custId", orderByMap.get("custNumber"));
			orderByMap.remove("custNumber");
		}
		Page<? extends GenericBean> page = service1201.findPage(C120M01A.class,
				pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S20AGrid 資料 J-108-0243 微型企業
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s10a(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS23APanel.class);
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		pageSetting.addOrderBy("custRelation");
		pageSetting.addOrderBy("custId");
		pageSetting.addOrderBy("dupNo");
		pageSetting.addOrderBy("custName");

		Page<? extends GenericBean> page = service1201.findPage(L120S10A.class,
				pageSetting);

		List<L120S10A> list = (List<L120S10A>) page.getContent();

		for (int i = 0; i < list.size(); i++) {
			L120S10A model = list.get(i);
			if (Util.isEmpty(model.getCustId())) {
				model.setCustId("");
			} else {
				model.setCustId(model.getCustId() + " " + model.getDupNo());
			}

			StringBuilder sb = new StringBuilder();
			String[] strs = this.sortCustRelation(Util.trim(
					model.getCustRelation()).split(","));
			Arrays.sort(strs);
			for (String s : strs) {
				if (sb.length() > 0)
					sb.append("/");
				sb.append(Util.trim(pop.getProperty("L120s10a.relation" + s)));
			}
			model.setCustRelationStr(sb.toString());
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s11aMainCust(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String custId2 = "9999999999";
		String dupNo2 = "9";

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId2",
				custId2);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);

		pageSetting.addOrderBy("printSeq", false);

		Page<? extends GenericBean> page = service1201.findPage(L120S11A.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s11aByCustId(ISearch pageSetting,	PageParameters params) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S06Panel.class);

		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString("mainId"));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addOrderBy("itemSeq", false);
		Page<? extends GenericBean> page = service1201.findPage(L120S11A.class,
				pageSetting);
		// CapGridResult result = new CapGridResult(page.getContent(),
		// page.getTotalRow());
		// return result;

		List<L120S11A> list = (List<L120S11A>) page.getContent();

		for (int i = 0; i < list.size(); i++) {
			L120S11A model = list.get(i);

			String relTypeStr = "";
			relTypeStr = Util.trim(pop.getProperty("L120S11A.relType_"
					+ Util.trim(model.getRelType())));

			model.setRelTypeStr(relTypeStr);

		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L120M01AGrid 資料(已覆核案件退回紀錄)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a_337027(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1201M01Page.class);

		String typCd = Util.trim(params.getString("typCd"));
		String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));

		String caseBrId = Util.trim(params.getString("caseBrId"));

		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		String caseLvl = Util.trim(params.getString("caseLvl"));

		// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書。
		// 去篩選簽報書統編、額度、MAINID 符合者
		List<String> l120m01a_maindId_list = lmsService
				.getL120M01AMainIdByFilterForm(params); // 額度明細表符合 統編、額度、MAINID
														// 之簽報書MAINID

		if (l120m01a_maindId_list.size() > 20000) {
			if (Util.isNotEmpty(custName)) {
				// 筆數太多會掛掉
				// 戶名符合條件筆數太多l120m01a_maindId_list.size()，請增加篩選條件後再執行本作業。
				throw new CapMessageException("簽報書基本查詢條件符合筆數太多" + "("
						+ Util.trim(l120m01a_maindId_list.size()) + "筆)"
						+ "，請增加簽報書篩選條件後再執行本作業", getClass());

			}

		}

		if (Util.equals(custId, "") && Util.equals(custName, "")) {
			if (l120m01a_maindId_list.size() > 0) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "mainId",
						l120m01a_maindId_list);
			}
		} else {
			// 篩選條件有統編、戶名時
			if (l120m01a_maindId_list.size() > 0) {

				if (Util.isNotEmpty(custId) && Util.isNotEmpty(custName)) {
					pageSetting.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.IN, "mainId",
									l120m01a_maindId_list),
							new SearchModeParameter(SearchMode.AND,
									new SearchModeParameter(SearchMode.EQUALS,
											"custId", custId),
									new SearchModeParameter(SearchMode.LIKE,
											"custName", custName + "%")));
				} else {
					if (Util.isNotEmpty(custId)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN,
										"mainId", l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.EQUALS,
										"custId", custId));
					}
					if (Util.isNotEmpty(custName)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN,
										"mainId", l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.LIKE,
										"custName", custName + "%"));
					}
				}

			} else {
				if (Util.isNotEmpty(custId)) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"custId", custId);
				}
				if (Util.isNotEmpty(custName)) {
					pageSetting.addSearchModeParameters(SearchMode.LIKE,
							"custName", custName + "%");
				}

			}
		}

		Date fromDate = null;
		Date endDate = null;
		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
			// if (docStatus.equals(CreditDocStatusEnum.海外_已核准.getCode())) {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "approveTime", reason);
			// } else {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "caseDate", reason);
			// }
		}
		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
		}
		if (Util.isNotEmpty(docType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
					docType);
		}
		if (Util.isNotEmpty(docKind)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		if (Util.isNotEmpty(docCode)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					docCode);
		}
		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		if (Util.isNotEmpty(caseLvl)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseLvl",
					caseLvl);
		}

		if (Util.isNotEmpty(updater)) {
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"hqAppraiser", updater);
			} else if (UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.北一區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.北二區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.南部區域授信中心.equals(user.getUnitNo())
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(user.getUnitNo())) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"areaAppraiser", updater);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"updater", updater);
			}
		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE + " 23:59:59") });
		}

		// 當為授管處簽案時要加上此條件
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& (UnitTypeEnum.分行.equals(UnitTypeEnum.convertToUnitType(user
						.getUnitType())) || UnitTypeEnum.國金部
						.equals(UnitTypeEnum.convertToUnitType(user
								.getUnitType())))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					UtilConstants.BankNo.授管處);
		}

		if ((Util.equals(user.getUnitNo(), UtilConstants.BankNo.授信行銷處) || Util
				.equals(user.getUnitNo(), UtilConstants.BankNo.消金業務處))) {
			if (Util.notEquals(caseBrId, "")) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"l120a01a.authUnit", caseBrId);
			}

		} else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120a01a.authUnit", user.getUnitNo());

			if (Util.notEquals(user.getUnitType(), "S")
					&& Util.notEquals(user.getUnitType(), "A")) {
				// 當非授管處或營運中心時
				// 排除掉海外授信案件
				pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
						"typCd", UtilConstants.Casedoc.typCd.海外);
			}
		}

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);

		// 限定只顯示已覆核案件退回紀錄文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				CreditDocStatusEnum.已覆核案件退回紀錄文件狀態.getCode());

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120M01A.class,
				pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));

			if (!Util.isEmpty(Util.trim(model.getApproveBackDocStatus()))) {
				model.setApproveBackDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(
								model.getApproveBackDocStatus()).getCode()));
			}

			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());

			model.setApproveBackUser(!Util.isEmpty(userservice
					.getUserName(model.getApproveBackUser())) ? userservice
					.getUserName(model.getApproveBackUser()) : model
					.getApproveBackUser());

			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult BorrowersQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String chairman = Util.nullToSpace(params.getString("chairman"));
		List<Map<String, Object>> data = new LinkedList<Map<String, Object>>();

		L120M01A meta = service1201.findL120m01aByMainId(mainId);
		if (meta != null) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("borrowId", Util.trim(meta.getCustId()));
			map.put("borrowDupNo", Util.trim(meta.getDupNo()));
			map.put("borrowName", Util.trim(meta.getCustName()));
			map.put("type", "M"); // 借款人
			data.add(map);

			if (Util.equals(chairman, "Y")) {
				L120S01B l120s01b = service1201.findL120s01bByUniqueKey(mainId,
						meta.getCustId(), meta.getDupNo());
				if (l120s01b != null) {
					if (Util.equals(l120s01b.getPosType(), "3")) {
						Map<String, Object> map2 = new HashMap<String, Object>();
						map2.put("borrowId",
								Util.trim(l120s01b.getChairmanId()));
						map2.put("borrowDupNo",
								Util.trim(l120s01b.getChairmanDupNo()));
						map2.put("borrowName",
								Util.trim(l120s01b.getChairman()));
						map2.put("type", l120s01b.getPosType());
						data.add(map2);
					}
				}
			}

		}

		int start = pageSetting.getFirstResult();
		int pagNumber = pageSetting.getMaxResults();
		int end = start + pagNumber > data.size() ? start
				+ (data.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = data.get(b);
			beanListnew.add(rowData);
		}

		Page<Map<String, Object>> page = new Page<Map<String, Object>>(
				beanListnew, data.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01aByL120m01a(ISearch pageSetting, PageParameters params) throws CapException {
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		L120M01A meta = service1201.findL120m01aByMainId(caseMainId);
		String itemType = lmsService.checkL140M01AItemType(meta);
		// a: LMSPrintContractThickBoxa(小規) b: LMSPrintContractThickBoxb(青創)
		String qType = Util.nullToSpace(params.getString("qType", ""));

		if (Util.isEmpty(qType)) { // init
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.mainId", "");
		} else {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.mainId", caseMainId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.itemType", itemType);
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					UtilConstants.Field.刪除時間, "");
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"proPerty", UtilConstants.Cntrdoc.Property.取消);
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"proPerty", UtilConstants.Cntrdoc.Property.不變);
			if (Util.equals(qType, "a")) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"isRescue", "Y");
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"isCbRefin", "Y");
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"isSmallBuss", "C");
			} else if (Util.equals(qType, "b")) {
				String bgnDate = (Util.equals(Util.trim(lmsService
						.getSysParamDataValue("LMS_L140_LNTYPE_61_BGNDATE")),
						"") ? "2020-08-01" : lmsService
						.getSysParamDataValue("LMS_L140_LNTYPE_61_BGNDATE"));
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"lnType", "61");
				pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
						"applyDate", bgnDate);
			}
		}
		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryContractPrintList(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));

		List<Map<String, Object>> data = new LinkedList<Map<String, Object>>();

		// printItem= '1|2|3|4';
		// 1.央行C方案授信合約書 2.本票 3.本票授權書-公司 4.授權扣帳範例
		List<L120S14A> l120s14a = (List<L120S14A>) service1201
				.findL120s14ListByMainId(L120S14A.class, mainId);
		if (l120s14a != null && !l120s14a.isEmpty()) {
			for (L120S14A s14a : l120s14a) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "合約書");
				map.put("printItem", "1");
				map.put("printCntrno", Util.nullToSpace(s14a.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14a.getOid()));
				data.add(map);
			}
		}

		List<L120S14B> l120s14b = (List<L120S14B>) service1201
				.findL120s14ListByMainId(L120S14B.class, mainId);
		if (l120s14b != null && !l120s14b.isEmpty()) {
			for (L120S14B s14b : l120s14b) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "本票");
				map.put("printItem", "2");
				map.put("printCntrno", Util.nullToSpace(s14b.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14b.getOid()));
				data.add(map);
			}
		}

		List<L120S14C> l120s14c = (List<L120S14C>) service1201
				.findL120s14ListByMainId(L120S14C.class, mainId);
		if (l120s14c != null && !l120s14c.isEmpty()) {
			for (L120S14C s14c : l120s14c) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "本票授權書");
				map.put("printItem", "3");
				map.put("printCntrno", Util.nullToSpace(s14c.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14c.getOid()));
				data.add(map);
			}
		}

		List<L120S14D> l120s14d = (List<L120S14D>) service1201
				.findL120s14ListByMainId(L120S14D.class, mainId);
		if (l120s14d != null && !l120s14d.isEmpty()) {
			for (L120S14D s14d : l120s14d) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "扣款授權書");
				map.put("printItem", "4");
				map.put("printCntrno", Util.nullToSpace(s14d.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14d.getOid()));
				data.add(map);
			}
		}

		List<L120S14F> l120s14f = (List<L120S14F>) service1201
				.findL120s14ListByMainId(L120S14F.class, mainId);
		if (l120s14f != null && !l120s14f.isEmpty()) {
			for (L120S14F s14f : l120s14f) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "增補合約");
				map.put("printItem", "5");
				map.put("printCntrno", Util.nullToSpace(s14f.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14f.getOid()));
				data.add(map);
			}
		}

		int start = pageSetting.getFirstResult();
		int pagNumber = pageSetting.getMaxResults();
		int end = start + pagNumber > data.size() ? start
				+ (data.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = data.get(b);
			beanListnew.add(rowData);
		}

		Page<Map<String, Object>> page = new Page<Map<String, Object>>(
				beanListnew, data.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S05BGrid 資料(集團)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05f(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String custId = Util.nullToSpace(params.getString("qCustId"));
		String dupNo = Util.nullToSpace(params.getString("qDupNo"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S05F.class,
				pageSetting);
		List<L120S05F> list = (List<L120S05F>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S05F model = list.get(i);
			StringBuilder strBuf = new StringBuilder();
			strBuf.append(model.getCustId()).append(model.getDupNo())
					.append(" ").append(model.getCustName());
			model.setCustName(strBuf.toString());
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S05BGrid 資料(集團)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05e(ISearch pageSetting,	PageParameters params) throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS02BPage.class);

		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String custId = Util.nullToSpace(params.getString("qCustId"));
		String dupNo = Util.nullToSpace(params.getString("qDupNo"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S05E.class,
				pageSetting);
		List<L120S05E> list = (List<L120S05E>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S05E model = list.get(i);
			StringBuilder strBuf = new StringBuilder();

			String grpFlag = Util.trim(model.getGrpFlag());

			if (Util.equals(grpFlag, "Y")) {
				strBuf.append(pop.getProperty("l120s05.grpKind1"));
			} else if (Util.equals(grpFlag, "A")) {
				strBuf.append(pop.getProperty("l120s05.grpKind2"));
			} else {
				strBuf.append(pop.getProperty("l120s05.grpKind3"));
			}

			model.setGrpFlag(strBuf.toString());
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL120s01qList(ISearch pageSetting, PageParameters params) throws CapException {
		String mainid = Util.nullToSpace(params.getString("mainId"));
		Page<Map<String, Object>> page = lmsService.findListbyL120S01Aseq(
				"L120S01Q", mainid);
		List<Map<String, Object>> list = page.getContent();
		return new CapMapGridResult(list, page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL120s04dList(ISearch pageSetting, PageParameters params) throws CapException {
		String mainid = Util.nullToSpace(params.getString("mainId"));
		Page<Map<String, Object>> page = lmsService.findListbyL120S01Aseq(
				"L120S04D", mainid);
		List<Map<String, Object>> list = page.getContent();
		return new CapMapGridResult(list, page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01rList(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		Page<? extends GenericBean> page = lmsService.findPage(L120S01R.class,
				pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}

	/**
	 * 跳出相關保證人清單供選擇
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL120s01rCustChoose(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		Page<Map<String, Object>> page = lmsService.findL120s01rCustChoose(
				mainId, pageSetting);

		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * L120S24A
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s24aList(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addOrderBy("cntrNo_s24a");// 用額度序號排序

		Page<? extends GenericBean> page = lmsService.findPage(L120S24A.class,
				pageSetting);

		String L120S24A_20250101_ON = lmsService.isL120s24a2025On();
		// 如果已經啟用，最新的版本就是20250101
		final String newestVersionDate = "Y".equals(L120S24A_20250101_ON) ? UtilConstants.L120s24aVersion.Ver_20250101
				: UtilConstants.L120s24aVersion.Ver_20220812;
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		// 幣別轉換
		// formatter.put("currentApplyCurr_s24a", new
		// CodeTypeFormatter(codeservice,
		// "Common_Currcy", CodeTypeFormatter.ShowTypeEnum.ValSpaceDesc));

		// 現請額度
		formatter.put("currentApplyAmt_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCurrentApplyAmt_s24a(), true, false);
				return res;
			}
		});
		// CCF
		formatter.put("ccf_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String onlyGuar_s24a = Util.trim(meta.getOnlyGuar_s24a());
				if ("N".equals(onlyGuar_s24a)) {
					res = "NA";
				} else {
					res = LMSUtil.processL120s24aBigDecimal(meta.getCcf_s24a(),
							false, true);
				}
				return res;
			}
		});
		// 純表外之信用相當額
		formatter.put("ccfAmt_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String onlyGuar_s24a = Util.trim(meta.getOnlyGuar_s24a());
				if ("N".equals(onlyGuar_s24a)) {
					res = "NA";
				} else {
					res = LMSUtil.processL120s24aBigDecimal(
							meta.getCcfAmt_s24a(), true, false);
				}
				return res;
			}
		});
		// 特殊融資
		formatter.put("specialFinRiskType_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String specialFinRiskType_s24a = Util.trim(meta.getSpecialFinRiskType_s24a());
				if ("0".equals(specialFinRiskType_s24a) || "".equals(specialFinRiskType_s24a)) {
					res = "N";
				} else {
					res = "Y";
				}
				return res;
			}
		});
		// 抵減前風險權數
		formatter.put("beforeDeductRW_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getBeforeDeductRW_s24a(), false, true);
				return res;
			}
		});
		// 合格擔保品抵減金額
		formatter.put("totalDisCollAmt_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String hasQuaColl_s24a = Util.trim(meta.getHasQuaColl_s24a());
				if ("N".equals(hasQuaColl_s24a)) {
					res = "NA";
				} else if (Util.isEmpty(hasQuaColl_s24a)) {
					res = "";
				} else {
					res = LMSUtil.processL120s24aBigDecimal(
							meta.getTotalDisCollAmt_s24a(), true, false);
				}
				return res;
			}
		});
		// 合格擔保品抵減後暴險額
		formatter.put("calDisCollExposureAmt_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCalDisCollExposureAmt_s24a(), true, false);
				return res;
			}
		});
		// 信保部位風險性資產
		formatter.put("calHasGutDeptRWA_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				String hasGutClass_s24a = Util.trim(meta.getHasGutClass_s24a());
				if ("N".equals(hasGutClass_s24a)) {
					res = "NA";
				} else {
					res = LMSUtil.processL120s24aBigDecimal(
							meta.getCalHasGutDeptRWA_s24a(), true, false);
				}
				return res;
			}
		});
		// 非信保部位風險性資產
		formatter.put("calNoGutRWA_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCalNoGutRWA_s24a(), true, false);
				return res;
			}
		});
		// 抵減後風險性資產
		formatter.put("calDeductRWA_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCalDeductRWA_s24a(), true, false);
				return res;
			}
		});
		// 抵減後風險權數
		formatter.put("calDeductRW_s24a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24A meta = (L120S24A) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getCalDeductRW_s24a(), false, true);
				return res;
			}
		});
		// 當前最新的版本，為了讓前端可以判斷要不要出現提示的字眼
		formatter.put("newestVersionDate", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				return newestVersionDate;
			}
		});
		
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	/**
	 * L120S24B
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s24bList(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String refOid_s24b = Util.nullToSpace(params.getString("refOid_s24b"));

		Page<L120S24B> page = lmsService.findL120s24bByRefOid_s24b(mainId,
				refOid_s24b, pageSetting);
		Map<String, IFormatter> fmtMap = new HashMap<String, IFormatter>();
		fmtMap.put("quaColl_s24b", new CodeTypeFormatter(codeservice,
				"quaColl_s24b"));
		fmtMap.put("currSym_s24b", new CodeTypeFormatter(codeservice,
				"Common_YesNo"));
		// 擔保品價值
		fmtMap.put("collAmt_s24b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24B meta = (L120S24B) in;
				res = LMSUtil.processL120s24aBigDecimal(meta.getCollAmt_s24b(),
						false, false);
				return res;
			}
		});
		// 折扣後擔保品價值
		fmtMap.put("disCollAmt_s24b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String res = "";
				L120S24B meta = (L120S24B) in;
				res = LMSUtil.processL120s24aBigDecimal(
						meta.getDisCollAmt_s24b(), false, false);
				return res;
			}
		});
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), fmtMap);

		return result;
	}

	/**
	 * 查詢借款人說明->1.中央政府/央行名單
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s24aCentralGov(ISearch pageSetting, PageParameters params) throws CapException {
		Page<Map<String, Object>> page = lmsService
				.queryL120s24aCentralGov(pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢借款人說明->2.非營利國營事業名單
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s24aNonPro(ISearch pageSetting, PageParameters params) throws CapException {
		Page<Map<String, Object>> page = lmsService
				.queryL120s24aNonPro(pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * J-109-0371_05097_B1002 簡化青年創業及啟動金貸款簽報書簽案流程
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryContractPrintList_lnType61(ISearch pageSetting, PageParameters params)	throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));

		List<Map<String, Object>> data = new LinkedList<Map<String, Object>>();

		// printItem= '1|2|3|4';
		// 1.央行C方案授信合約書 2.本票 3.本票授權書-公司 4.授權扣帳範例
		List<L120S14E> l120s14e = (List<L120S14E>) service1201
				.findL120s14ListByMainId(L120S14E.class, mainId);
		if (l120s14e != null && !l120s14e.isEmpty()) {
			for (L120S14E s14e : l120s14e) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "合約書");
				map.put("printItem", "1");
				map.put("printCntrno", Util.nullToSpace(s14e.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14e.getOid()));
				data.add(map);
			}
		}

		List<L120S14B> l120s14b = (List<L120S14B>) service1201
				.findL120s14ListByMainId(L120S14B.class, mainId);
		if (l120s14b != null && !l120s14b.isEmpty()) {
			for (L120S14B s14b : l120s14b) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "本票");
				map.put("printItem", "2");
				map.put("printCntrno", Util.nullToSpace(s14b.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14b.getOid()));
				data.add(map);
			}
		}

		List<L120S14C> l120s14c = (List<L120S14C>) service1201
				.findL120s14ListByMainId(L120S14C.class, mainId);
		if (l120s14c != null && !l120s14c.isEmpty()) {
			for (L120S14C s14c : l120s14c) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "本票授權書");
				map.put("printItem", "3");
				map.put("printCntrno", Util.nullToSpace(s14c.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14c.getOid()));
				data.add(map);
			}
		}

		List<L120S14D> l120s14d = (List<L120S14D>) service1201
				.findL120s14ListByMainId(L120S14D.class, mainId);
		if (l120s14d != null && !l120s14d.isEmpty()) {
			for (L120S14D s14d : l120s14d) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("docName", "扣款授權書");
				map.put("printItem", "4");
				map.put("printCntrno", Util.nullToSpace(s14d.getCntrNo()));
				map.put("printOid", Util.nullToSpace(s14d.getOid()));
				data.add(map);
			}
		}

		int start = pageSetting.getFirstResult();
		int pagNumber = pageSetting.getMaxResults();
		int end = start + pagNumber > data.size() ? start
				+ (data.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = data.get(b);
			beanListnew.add(rowData);
		}

		Page<Map<String, Object>> page = new Page<Map<String, Object>>(
				beanListnew, data.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S06AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s16a(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S07Panel.class);
		// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S02Panel.class);

		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);

		pageSetting.addOrderBy("printSeq");
		pageSetting.addOrderBy("custId");
		pageSetting.addOrderBy("cntrNo");

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S16A.class,
				pageSetting);
		List<L120S16A> list = (List<L120S16A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S16A model = list.get(i);
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			StringBuilder str1 = new StringBuilder();
			StringBuilder str2 = new StringBuilder();
			str1.append(model.getCustId()).append(" ").append(model.getDupNo())
					.append(" ").append(model.getCustName());

			model.setCustId(str1.toString());

			// J-110-0327_05097_B1001 Web e-Loan國內與海外授信簽報書新增額度檢視表。
			// 性質
			StringBuilder stringTemp1 = new StringBuilder("");
			if (!Util.isEmpty(model.getProperty())) {
				// 處理性質顯示
				String[] proPerty = model.getProperty().split(
						UtilConstants.Mark.SPILT_MARK);

				for (String type : proPerty) {
					if (!Util.isEmpty(type)) {
						String name = prop.getProperty("L140M01a.type" + type);
						stringTemp1
								.append(stringTemp1.length() > 0 ? ", " : "")
								.append(name);
					}
				}
				model.setProperty(stringTemp1.toString());
			}

		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S16CGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s16c(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S07Panel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));

		// 取得借款人順序
		LinkedHashMap<String, Integer> custIdOrderMap = new LinkedHashMap<String, Integer>();
		List<L120S01A> l120s01aList = service1201
				.findL120s01aByMainIdForOrder(mainid);
		int seq = 0;
		for (L120S01A l120s01a : l120s01aList) {
			String custId = Util.trim(l120s01a.getCustId());
			String dupNo = Util.trim(l120s01a.getDupNo());
			String fullCustId = custId + "-" + dupNo;
			if (!custIdOrderMap.containsKey(fullCustId)) {
				seq++;
				custIdOrderMap.put(fullCustId, seq);
			}
		}

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service1201.findPage(L120S16C.class,
				pageSetting);
		List<L120S16C> list = (List<L120S16C>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S16C model = list.get(i);

			String mCustId = Util.trim(model.getCustId());
			String mDupNo = Util.trim(model.getDupNo());
			String mFullCustId = mCustId + "-" + mDupNo;
			if (custIdOrderMap.containsKey(mFullCustId)) {
				model.setPrintSeq(custIdOrderMap.get(mFullCustId));
			} else {
				if (Util.isEmpty(Util.nullToSpace(model.getPrintSeq()))) {
					model.setPrintSeq(99); // 有序號用原本的序號 沒序號再壓99
				}
			}

			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			StringBuilder str1 = new StringBuilder();
			StringBuilder str2 = new StringBuilder();
			str1.append(model.getCustId()).append(" ").append(model.getDupNo())
					.append(" ").append(model.getCustName());

			model.setCustId(str1.toString());

		}

		if (list != null && !list.isEmpty()) {
			Collections.sort(list, new Comparator<L120S16C>() {
				public int compare(L120S16C o1, L120S16C o2) {
					Integer ii = (o1.getPrintSeq().compareTo(o2.getPrintSeq()));
					return ii;
				}
			});
		}

		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * RPA查詢名單
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL161S01E(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		// 查這份文件的MainId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);

		Page<? extends GenericBean> page = lms1601Service.findPage(
				L161S01E.class, pageSetting);

		List<L161S01E> l161s01es = (List<L161S01E>) page.getContent();
		for (L161S01E l161s01e : l161s01es) {
			StringBuilder sb = new StringBuilder();

			// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			// 不能CALL AMLRelateService 因為會觸發交易，導致model 會把修改後的值真的存起來
			String[] strs = this.sortCustRelation(Util.trim(
					l161s01e.getCustRelation()).split(","));

			String custRelationIndex = "";
			for (String s : strs) {
				if (Util.equals(Util.trim(custRelationIndex), "")) {
					custRelationIndex = s;
				}
				if (sb.length() > 0)
					sb.append("/");
				sb.append(Util.trim(pop.getProperty("L120S09a.checkbox" + s)));
			}
			l161s01e.setCustRelationIndex(sb.toString());
			l161s01e.setCustRelation(custRelationIndex);
		}

		return new CapGridResult(l161s01es, page.getTotalRow(), null);
	}

	/**
	 * RPA查詢結果
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryRpaInfo(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String[] codeType = { "lms1201s28_type" };
		Map<String, CapAjaxFormResult> codeMap = codeservice
				.findByCodeType(codeType);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		Page<? extends GenericBean> page = lms1601Service.findPage(
				L161S01D.class, pageSetting);

		Map<String, IFormatter> fmtMap = new HashMap<String, IFormatter>();

		fmtMap.put("type", new CodeTypeFormatter(codeservice,
				"lms1201s28_type", CodeTypeFormatter.ShowTypeEnum.Desc));
		fmtMap.put("status", new CodeTypeFormatter(codeservice,
				"l161s01d_status", CodeTypeFormatter.ShowTypeEnum.Desc));

		List<L161S01D> l161s01ds = (List<L161S01D>) page.getContent();
		for (L161S01D l161s01d : l161s01ds) {

		}

		return new CapGridResult(l161s01ds, page.getTotalRow(), fmtMap);
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdaByCustId(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		String caseBrId = Util.nullToSpace(params.getString("caseBrId"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCesMainIdaByCustId(
				caseBrId, custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢簽報書/動審表借款人名單，給他選要帶入哪些人的資料進態樣檢核表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult showAmlStateChoose(ISearch pageSetting,	PageParameters params) throws CapException {

		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		// 依據是簽報書or動審表，抓不同的借款人來源丟回新增畫面的預設資料
		L120M01A l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
				mainId);

		if (l120m01a != null) {
			// 簽報書

			// 借款人姓名、統編
			// 先放主借款人
			Map<String, Object> csutData = new HashMap<String, Object>();
			csutData.put("custId", l120m01a.getCustId());
			csutData.put("dupNo", l120m01a.getDupNo());
			csutData.put("custName", l120m01a.getCustName());
			list.add(csutData);

			List<L120S01A> l120s01aList = amlRelateService
					.findL120s01aByMainId(mainId);
			for (L120S01A model : l120s01aList) {
				String custId = model.getCustId();
				String dupNo = model.getDupNo();
				if (Util.trim(custId).equals(Util.trim(l120m01a.getCustId()))
						&& Util.trim(dupNo).equals(
								Util.trim(l120m01a.getDupNo()))) {
					// 主借款人已放進list
				} else {
					csutData = new HashMap<String, Object>();
					csutData.put("custId", model.getCustId());
					csutData.put("dupNo", model.getDupNo());
					csutData.put("custName", model.getCustName());
					list.add(csutData);
				}
			}

		} else {
			// 動審表
			L160M01A l160m01a = amlRelateService.findModelByMainId(
					L160M01A.class, mainId);
			if (l160m01a != null) {

				// 借款人姓名、統編
				// 先放主借款人
				Map<String, Object> csutData = new HashMap<String, Object>();
				csutData.put("custId", l160m01a.getCustId());
				csutData.put("dupNo", l160m01a.getDupNo());
				csutData.put("custName", l160m01a.getCustName());
				list.add(csutData);

				List<Map<String, Object>> custNameList = eloanDbBaseService
						.findL140M01AInfoByMainId(l160m01a.getMainId());

				// 過濾重複的借款人
				Map<String, Map<String, Object>> distMap = new HashMap<String, Map<String, Object>>();
				for (Map<String, Object> custMap : custNameList) {
					String custId = Util.trim(custMap.get("CUSTID"));
					String dupNo = Util.trim(custMap.get("DUPNO"));
					if (distMap.get(custId + custMap) != null) {
						// 重複的人不放入map
					} else {
						distMap.put(custId + dupNo, custMap);
					}
				}

				for (Map.Entry<String, Map<String, Object>> entry : distMap
						.entrySet()) {
					Map<String, Object> map = entry.getValue();
					String custName = Util.trim(map.get("CUSTNAME"));
					String custId = Util.trim(map.get("CUSTID"));
					String dupNo = Util.trim(map.get("DUPNO"));
					if (Util.trim(custId).equals(
							Util.trim(l160m01a.getCustId()))
							&& Util.trim(dupNo).equals(
									Util.trim(l160m01a.getDupNo()))) {
						// 主借款人已放進list
					} else {
						csutData = new HashMap<String, Object>();
						csutData.put("custId", custId);
						csutData.put("dupNo", dupNo);
						csutData.put("custName", custName);
						list.add(csutData);
					}
				}
			}
		}

		CapMapGridResult grid = new CapMapGridResult(list, list.size());
		return grid;
	}

	/**
	 * 查詢徵信報告 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCes140MainIdaByCustId(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		String caseBrId = Util.nullToSpace(params.getString("caseBrId"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = service1201.getCes140MainIdaByCustId(
				caseBrId, custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * J-110-0382 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s21aDistinct(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.setDistinct(true);
		pageSetting.setDistinctColumn(new String[] { EloanConstants.MAIN_ID,
				"cntrNoCo_s21a", "currCo_s21a", "factAmtCo_s21a" });
		pageSetting.addOrderBy("cntrNoCo_s21a", false);
		pageSetting.addOrderBy("currCo_s21a", false);
		pageSetting.addOrderBy("factAmtCo_s21a", false);
		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21A.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("co_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String co_s21a = "";
				L120S21A meta = (L120S21A) in;
				co_s21a = Util.nullToSpace(meta.getCurrCo_s21a())
						+ (meta.getFactAmtCo_s21a() == null ? "" : df
								.format(meta.getFactAmtCo_s21a()));

				return co_s21a;
			}
		});

		List<L120S21A> l120s21as = (List<L120S21A>) page.getContent();

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s21a(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNoCo_s21a = Util.nullToSpace(params
				.getString("cntrNoCo_s21a"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNoCo_s21a",
				cntrNoCo_s21a);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("cntrNoCo_s21a", false);
		pageSetting.addOrderBy("cntrNo_s21a", false);
		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21A.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("currentApply_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String currentApply_s21a = "";
				L120S21A meta = (L120S21A) in;
				currentApply_s21a = Util.nullToSpace(meta
						.getCurrentApplyCurr_s21a())
						+ (meta.getCurrentApplyAmt_s21a() == null ? "" : df
								.format(meta.getCurrentApplyAmt_s21a()));
				return currentApply_s21a;
			}
		});
		formatter.put("bl_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String bl_s21a = "";
				L120S21A meta = (L120S21A) in;
				bl_s21a = Util.nullToSpace(meta.getBlCurr_s21a())
						+ (meta.getBlAmt_s21a() == null ? "" : df.format(meta
								.getBlAmt_s21a()));
				return bl_s21a;
			}
		});
		formatter.put("rcv_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String rcv_s21a = "";
				L120S21A meta = (L120S21A) in;
				rcv_s21a = Util.nullToSpace(meta.getRcvCurr_s21a())
						+ (meta.getRcvInt_s21a() == null ? "" : df.format(meta
								.getRcvInt_s21a()));
				return rcv_s21a;
			}
		});

		formatter.put("reUse_s21a", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String reUse_s21a = "";
				L120S21A meta = (L120S21A) in;
				reUse_s21a = Util.equals(Util.trim(meta.getReUse_s21a()), "1") ? "N"
						: (Util.equals(Util.trim(meta.getReUse_s21a()), "2") ? "Y"
								: "");
				return reUse_s21a;
			}
		});

		List<L120S21A> l120s21as = (List<L120S21A>) page.getContent();

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s21b(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"hasCntrDoc_s21b", "Y");
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("custId_s21b", false);
		pageSetting.addOrderBy("dupNo_s21b", false);
		pageSetting.addOrderBy("cntrNo_s21b", false);
		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21B.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("custId_s21b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B meta = (L120S21B) in;
				return meta.getCustId_s21b() + meta.getDupNo_s21b();
			}
		});

		// J-110-0485_05097_B1004 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		formatter.put("bussType_s21b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B meta = (L120S21B) in;
				return codeservice.getDescOfCodeType("LGD_BussType",
						Util.trim(meta.getBussType_s21b()));

			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("collateralRecoveryCms", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B l120s21b = (L120S21B) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (l120s21b.getCollateralRecoveryCms() == null ? "-"
						: NumConverter.addComma(l120s21b
								.getCollateralRecoveryCms().toPlainString()));
			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("collateralRecoveryOth", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B l120s21b = (L120S21B) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (l120s21b.getCollateralRecoveryOth() == null ? "-"
						: NumConverter.addComma(l120s21b
								.getCollateralRecoveryOth().toPlainString()));
			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("creditRecovery", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B l120s21b = (L120S21B) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (l120s21b.getCreditRecovery() == null ? "-"
						: NumConverter.addComma(l120s21b.getCreditRecovery()
								.toPlainString()));
			}
		});

		// J-111-0083_05097_B1002 Web
		// e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類
		formatter.put("isStandAloneAuth_s21b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B meta = (L120S21B) in;
				return Util.isEmpty(meta.getIsStandAloneAuth_s21b()) ? ""
						: Util.trim(meta.getIsStandAloneAuth_s21b())
								+ "."
								+ codeservice.getDescOfCodeType(
										"lms140_isStandAloneAuth",
										Util.trim(meta
												.getIsStandAloneAuth_s21b()));

			}
		});

		List<L120S21B> l120s21bs = (List<L120S21B>) page.getContent();

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL120s21b_1(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"hasCntrDoc_s21b", "Y");
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("custId_s21b", false);
		pageSetting.addOrderBy("dupNo_s21b", false);

		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21B.class, pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("custId_s21b", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21B meta = (L120S21B) in;
				return meta.getCustId_s21b() + meta.getDupNo_s21b();
			}
		});

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		List<L120S21B> l120s21bs = (List<L120S21B>) page.getContent();
		String gCustId = "";
		for (L120S21B l120s21b : l120s21bs) {
			Map<String, Object> row = new HashMap<String, Object>();
			String custId_s21b = Util.trim(l120s21b.getCustId_s21b());
			if (Util.notEquals(gCustId, custId_s21b)) {
				gCustId = custId_s21b;
				row.put("custId_s21b", custId_s21b);
				row.put("custLgd",
						l120s21b.getCustLgd() == null ? null
								: new NumericFormatter("##.##")
										.reformat(l120s21b.getCustLgd()));
				list.add(row);
			}

		}
		// return new CapGridResult(page.getContent(), page.getTotalRow(),
		// formatter);
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(list, pageSetting);
		return new CapMapGridResult(pages.getContent(), list.size());

	}

	/**
	 * M-110-0227 資料倉儲提供fptinsrt table至DWOTS供eloan授信簽報利率合理性分析引用 查詢FTP利率
	 */
	public CapMapGridResult queryFtpRate(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String curr = Util.trim(params.getString("curr", ""));
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if (Util.isNotEmpty(curr)) {
			String currCode = "";
			if (Util.equals(curr, "TWD")) {
				currCode = "00";
			} else if (Util.equals(curr, "USD")) {
				currCode = "01";
			} else {
				currCode = "";
			}
			List<Map<String, Object>> data = dwdbService
					.findOTS_FTPINSRTByCurr(currCode);
			for (Map<String, Object> map : data) {
				Map<String, Object> row = new HashMap<String, Object>();
				String INS_CD = Util.trim(MapUtils.getString(map, "INS_CD")); // 代號
				String FTP_CNM = Util.trim(MapUtils.getString(map, "FTP_CNM")); // 中文說明
				String INS_RT = Util.trim(MapUtils.getString(map, "INS_RT")); // 利率
				row.put("insCdCnm", INS_CD + " — " + FTP_CNM);
				row.put("insRt",
						new NumericFormatter("##.#####").reformat(INS_RT));
				list.add(row);
			}
		}
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(list, pageSetting);
		return new CapMapGridResult(pages.getContent(), list.size());
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s21c(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNo_s21c = Util.nullToSpace(params.getString("cntrNo_s21c"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo_s21c",
				cntrNo_s21c);

		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("collType_s21c", false);
		pageSetting.addOrderBy("colKind_s21c", false);
		Page<? extends GenericBean> page = lmsLgdService.findPage(
				L120S21C.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("collType_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				return codeservice.getDescOfCodeType("LGD_CollType",
						Util.trim(l120s21c.getCollType_s21c()));
			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("colCurr_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (Util.equals(l120s21c.getColKind_s21c(), "999901") ? "-"
						: l120s21c.getColCurr_s21c());
			}
		});

		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("colTimeValue_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
				return (Util.equals(l120s21c.getColKind_s21c(), "999901") ? "-"
						: l120s21c.getColTimeValue_s21c() == null ? ""
								: NumConverter
										.addComma(l120s21c
												.getColTimeValue_s21c()
												.toPlainString()));
			}
		});

		// J-110-0485_05097_B1008 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
		formatter.put("colRecoveryTwd_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則

				String returnVal = "";
				if (Util.equals(l120s21c.getColKind_s21c(), "050300")) {
					returnVal = "N.A.";
				} else if (Util.equals(l120s21c.getColKind_s21c(), "999901")) {
					returnVal = "-";
				} else {
					returnVal = l120s21c.getColRecoveryTwd_s21c() == null ? "0"
							: NumConverter.addComma(l120s21c
									.getColRecoveryTwd_s21c().toPlainString());
				}

				return returnVal;
			}
		});

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// 前順位設定金額
		formatter.put("colPreRgstAmt_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				String colKind_s21c = Util.trim(l120s21c.getColKind_s21c());
				return (lmsLgdService.hideRgstInfoForL120s21c(colKind_s21c) ? ""
						: l120s21c.getColPreRgstAmt_s21c() == null ? "0"
								: NumConverter.addComma(l120s21c
										.getColPreRgstAmt_s21c()
										.toPlainString()));
			}
		});

		// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
		// 擔保品設定金額
		formatter.put("colRgstAmt_s21c", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S21C l120s21c = (L120S21C) in;
				String colKind_s21c = Util.trim(l120s21c.getColKind_s21c());
				return (lmsLgdService.hideRgstInfoForL120s21c(colKind_s21c) ? ""
						: l120s21c.getColRgstAmt_s21c() == null ? "0"
								: NumConverter.addComma(l120s21c
										.getColRgstAmt_s21c().toPlainString()));
			}
		});

		List<L120S21C> l120s21cs = (List<L120S21C>) page.getContent();

		// J-110-0485_05097_B1009 Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
		for (L120S21C l120s21c : l120s21cs) {
			// J-110-0485_05097_B1009 Web
			// e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能
			BigDecimal colShareRate_s21c = l120s21c.getColShareRate_s21c();
			if (colShareRate_s21c == null) {
				if (Util.equals(Util.trim(l120s21c.getColCoUseFlag_s21c()), "N")) {
					if (l120s21c.getColPreRgstAmt_s21c() == null
							|| BigDecimal.ZERO.compareTo(l120s21c
									.getColPreRgstAmt_s21c()) == 0) {
						colShareRate_s21c = new BigDecimal(100);
					}
				}
			}

			l120s21c.setColShareRate_s21c(colShareRate_s21c);

		}

		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

	/**
	 * J-111-0397 RWA
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s23aList(ISearch pageSetting,	PageParameters params) throws CapException {
		DecimalFormat df = new DecimalFormat("###,###,###,###,###.#####");
		// Map<String, String> proPertyMap =
		// codeservice.findByCodeType("lms1405s02_proPerty");
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cellvalue = Util.nullToSpace(params
				.getString("cellvalue", "RWA"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("rwaCustId", false);
		pageSetting.addOrderBy("rwaDupNo", false);
		pageSetting.addOrderBy("rwaCntrNo", false);
		pageSetting.addOrderBy("oid", false);

		Page<? extends GenericBean> page = service1201.findPage(L120S23A.class,
				pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("rwaCustId", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S23A l120s23a = (L120S23A) in;
				String mCustId = Util.trim(l120s23a.getRwaCustId());
				String mDupNo = Util.trim(l120s23a.getRwaDupNo());
				String mFullCustId = mCustId + "　" + mDupNo;
				return mFullCustId;
			}
		});

		formatter.put("rwaProPerty", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				Map<String, String> proPertyMap = codeservice
						.findByCodeType("lms1405s02_proPerty");
				L120S23A l120s23a = (L120S23A) in;
				String[] ppArr = Util.nullToSpace(l120s23a.getRwaProPerty())
						.split(UtilConstants.Mark.SPILT_MARK);
				StringBuffer temp = new StringBuffer();
				for (String value : ppArr) {
					temp.append(temp.length() > 0 ? "、" : "");
					temp.append(Util.trim(proPertyMap.get(value)));
				}
				return temp.toString();
			}
		});

		formatter.put("rwaTwd", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S23A l120s23a = (L120S23A) in;
				return l120s23a.getRwaTwd()
						.setScale(0, BigDecimal.ROUND_HALF_UP).toString();
			}
		});

		List<L120S23A> list = (List<L120S23A>) page.getContent();
		List<L120S23A> newList = new ArrayList<L120S23A>();
		for (int i = 0; i < list.size(); i++) {
			L120S23A model = list.get(i);

			if (model != null) {
				if (Util.equals(cellvalue, "RORWA")) {
					if (!lmsService.chkNeedRorwa(model)
							|| !lmsService.chkCalcRorwa(model)) {
						continue;
					}
				}
				/*
				 * String mCustId = Util.trim(model.getRwaCustId()); String
				 * mDupNo = Util.trim(model.getRwaDupNo()); String mFullCustId =
				 * mCustId + "　" + mDupNo; model.setRwaCustId(mFullCustId);
				 * String[] ppArr = Util.nullToSpace(model.getRwaProPerty())
				 * .split(UtilConstants.Mark.SPILT_MARK); StringBuffer temp =
				 * new StringBuffer(); for (String value : ppArr) {
				 * temp.append(temp.length() > 0 ? "、" : "");
				 * temp.append(Util.trim(proPertyMap.get(value))); }
				 * model.setRwaProPerty(temp.toString());
				 */
				newList.add(model);
			}
		}
		return new CapGridResult(newList, page.getTotalRow(), formatter);
	}

	/**
	 * BIS
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s25aList(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("bisCustId_s25a", false);
		pageSetting.addOrderBy("bisDupNo_s25a", false);
		pageSetting.addOrderBy("bisCntrNo_s25a", false);
		pageSetting.addOrderBy("oid", false);

		Page<? extends GenericBean> page = service1201.findPage(L120S25A.class,
				pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter.put("bisSheetItem", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				Map<String, String> proPertyMap = codeservice
						.findByCodeType("bisSheetItem");
				L120S25A l120s25a = (L120S25A) in;
				String bisSheetItem = Util.nullToSpace(l120s25a
						.getBisSheetItem());
				return Util.trim(proPertyMap.get(bisSheetItem));
			}
		});

		formatter.put("bisCcf", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;
				String bisSheetItem = Util.nullToSpace(l120s25a
						.getBisSheetItem());
				BigDecimal bisCcf = l120s25a.getBisCcf();
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return Util.equals(bisSheetItem, "Y") ? df.format(bisCcf) : "";
			}
		});

		formatter.put("bisRiskAdjReturn", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisRiskAdjReturn = l120s25a.getBisRiskAdjReturn();
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return bisRiskAdjReturn == null ? "N.A." : df
						.format(bisRiskAdjReturn);
			}
		});

		formatter.put("bisRiskAdjReturn_1", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisRiskAdjReturn_1 = l120s25a
						.getBisRiskAdjReturn_1();
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return bisRiskAdjReturn_1 == null ? "N.A." : df
						.format(bisRiskAdjReturn_1);
			}
		});

		// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
		formatter.put("bisRorwa", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisRorwa = l120s25a.getBisRorwa();
				BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
						: l120s25a.getBisRItemD()); // %
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return rItemD.compareTo(BigDecimal.ZERO) != 0 ? (bisRorwa == null ? ""
						: df.format(bisRorwa))
						: "N.A.";
			}
		});

		formatter.put("bisRorwa_1", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisRorwa_1 = l120s25a.getBisRorwa_1();
				BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
						: l120s25a.getBisRItemD()); // %
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return rItemD.compareTo(BigDecimal.ZERO) != 0 ? (bisRorwa_1 == null ? ""
						: df.format(bisRorwa_1))
						: "N.A.";
			}
		});

		// J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
		formatter.put("bisImpactNum", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S25A l120s25a = (L120S25A) in;

				BigDecimal bisImpactNum = l120s25a.getBisImpactNum();
				BigDecimal rItemD = (l120s25a.getBisRItemD() == null ? BigDecimal.ZERO
						: l120s25a.getBisRItemD()); // %
				DecimalFormat df = new DecimalFormat(
						"###,###,###,###,###,###,###,###.#####");
				return rItemD.compareTo(BigDecimal.ZERO) != 0 ? (bisImpactNum == null ? ""
						: df.format(bisImpactNum))
						: "N.A.";
			}
		});

		List<L120S25A> list = (List<L120S25A>) page.getContent();

		return new CapGridResult(list, page.getTotalRow(), formatter);
	}

	/**
	 * J-112-0125 私募基金
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01t(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		String flag = Util.nullToSpace(params.getString("flag"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "flag", flag);

		Page<? extends GenericBean> page = service1201.findPage(L120S01T.class,
				pageSetting);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("privateFund", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L120S01T l120s01t = (L120S01T) in;

				String fundNo = Util.nullToSpace(l120s01t.getPrivateFundNo());
				String fundName = Util.nullToSpace(l120s01t
						.getPrivateFundName());
				String privateFund = fundNo
						+ (Util.isNotEmpty(fundNo) ? ". " : "") + fundName;
				return privateFund;
			}
		});
		List<L120S01T> list = (List<L120S01T>) page.getContent();

		return new CapGridResult(list, page.getTotalRow(), formatter);
	}

	/**
	 * J-112-0225_05097_B1001 Web e-Loan企金授信調整BIS評估表FTP引用資料
	 */
	public CapMapGridResult queryBisCapitalCost(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		// 如為短期引用：
		// 資金成本 = 平均實質存款利率
		// 如為中長期引用：
		// 資金成本 = 平均實質存款利率 + MAX [ (中長期FTP - 短期FTP) , 0 ]
		// 註：平均實質存款利率來源為CRMAC076；FTP來源為CRAAL001。(均為月底)
		// 短期FTP = 【短期放款 逾9個月且1年以下】。
		// 中長期FTP = 【中長期放款 逾1年且3年以下】。

		// INS_CD FTP_CNM MEMO CUR_CD INS_DT AP_TYPE INS_RT OTS_LST_DATA_SRC
		// OTS_DATA_SRC_DT OTS_LST_MNT_DT
		// TWD
		// @J 短期放款 逾9個月且1年以下 [(9M+1Y)/2 TAIBOR]+min{3%,5bps}+調整項 00 2023-05-09
		// LN 1.711355 DW_FTPINSRT 2023-05-09 2023-05-09
		// @E 中長期放款 逾1年且3年以下 [1Y TAIBOR ]+min{3%,5bps}+調整項 00 2023-05-09 LN
		// 1.769330 DW_FTPINSRT 2023-05-09 2023-05-09

		// USD
		// @Q 短期放款 逾9個月且1年以下 [(9M+12M)/2 TAIMEAN]+min{3%,5bps}+調整項 01 2023-05-09
		// LN 5.045000 DW_FTPINSRT 2023-05-09 2023-05-09
		// @5 中長期放款 逾1年且3年以下 [12M TAIMEAN]+min{3%,5bps}+調整項 01 2023-05-09 LN
		// 4.800000 DW_FTPINSRT 2023-05-09 2023-05-09

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S11Panel.class);

		String curr = Util.trim(params.getString("curr", ""));
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		if (Util.isNotEmpty(curr)) {

			// ***********************************************************************
			// 資金成本 = 平均實質存款利率
			// ***********************************************************************

			// J-112-0170 配合常董會決議, eloan國內授信管理系統, 調整授信成本收益概算表的顯示內容
			// 資金成本(B) from DW 實質存款平均利率計算成本
			// 抓總行資料，TWD一份、USD一份
			String CYC_DT = "";
			// DATA_DEF = 'B' --廣義
			String DATA_DEF = Util.trim(lmsService
					.getSysParamDataValue("LMS_BIS_AVG_DEPOSIT_DATA_DEF"));
			// DW_DEP_CD = 'L' --含OBU D = DBU L = DBU+OBU
			String DW_DEP_CD = Util.trim(lmsService
					.getSysParamDataValue("LMS_BIS_AVG_DEPOSIT_DEP_CD"));
			// DATA_TYP = 'N' --不含行存
			String DATA_TYP = Util.trim(lmsService
					.getSysParamDataValue("LMS_BIS_AVG_DEPOSIT_DATA_TYP"));

			List<Map<String, Object>> acinsrtData = null;
			BigDecimal INREAL_RATE_DP = null;// 存款實質利率

			// 抓出資料基準時間
			CYC_DT = CapDate.getCurrentDate("yyyyMM") + "01";

			acinsrtData = dwdbService.findOTS_ACINSRTByColumn(CYC_DT, "999",
					DATA_DEF, curr, DW_DEP_CD, DATA_TYP);
			// 如果該月還沒有資料，則往前抓前面月份的資料
			// 了不起抓個12次?
			if (acinsrtData == null || acinsrtData.isEmpty()) {
				for (int i = 0; i < 12; i++) {
					CYC_DT = CapDate.addMonth(CYC_DT, -1);
					acinsrtData = dwdbService.findOTS_ACINSRTByColumn(CYC_DT,
							"999", DATA_DEF, curr, DW_DEP_CD, DATA_TYP);
					if (acinsrtData != null && !acinsrtData.isEmpty()) {
						break;
					}
				}
			}
			if (acinsrtData == null || acinsrtData.isEmpty()) {
				INREAL_RATE_DP = new BigDecimal(Util.trim(lmsService
						.getSysParamDataValue("LMS_BIS_AVG_DEPOSIT_INREAL_"
								+ curr)));// 預設的存款實質利率
				// ex:0.808
			} else {
				// 只會抓全行，且此條件後只會有一筆資料
				// 會是BigDecimal
				INREAL_RATE_DP = (BigDecimal) acinsrtData.get(0).get(
						"INREAL_RATE_DP");
			}

			// ***********************************************************************
			// 中長期資金成本 = 平均實質存款利率 + MAX [ (中長期FTP - 短期FTP) , 0 ]
			// ***********************************************************************

			String currCode = "";
			String insCdStrS = "";
			String insCdStrL = "";

			if (Util.equals(curr, "TWD")) {
				currCode = "00";
				insCdStrS = Util.trim(lmsService
						.getSysParamDataValue("LMS_BIS_AVG_DEPOSIT_FTP_S_"
								+ curr)); // "@J"; // 短期FTP
				insCdStrL = Util.trim(lmsService
						.getSysParamDataValue("LMS_BIS_AVG_DEPOSIT_FTP_L_"
								+ curr)); // "@E"; // 中長期FTP
			} else if (Util.equals(curr, "USD")) {
				currCode = "01";
				insCdStrS = Util.trim(lmsService
						.getSysParamDataValue("LMS_BIS_AVG_DEPOSIT_FTP_S_"
								+ curr)); // "@Q"; // 短期FTP
				insCdStrL = Util.trim(lmsService
						.getSysParamDataValue("LMS_BIS_AVG_DEPOSIT_FTP_L_"
								+ curr)); // "@5"; // 中長期FTP
			} else {
				currCode = "";
			}

			// 中長期資金成本
			BigDecimal ftpRateS = null;
			BigDecimal ftpRateL = null;
			BigDecimal longCaptialCost = null;
			if (Util.notEquals(insCdStrS, "") && Util.notEquals(insCdStrL, "")) {

				List<String> insCdListS = Arrays.asList(insCdStrS.split(","));// 短期FT
				List<String> insCdListL = Arrays.asList(insCdStrL.split(","));// 中長期FTP

				List<Map<String, Object>> data = dwdbService
						.findOTS_FTPINSRTByCurr_lastMonth(currCode);

				for (Map<String, Object> map : data) {
					String INS_CD = Util
							.trim(MapUtils.getString(map, "INS_CD")); // 代號
					String FTP_CNM = Util.trim(MapUtils.getString(map,
							"FTP_CNM")); // 中文說明
					String INS_RT = Util
							.trim(MapUtils.getString(map, "INS_RT")); // 利率
					// 短期FT
					if (insCdListS.contains(INS_CD)) {
						ftpRateS = Util.parseBigDecimal(INS_RT);
					}
					// 中長期FTP
					if (insCdListL.contains(INS_CD)) {
						ftpRateL = Util.parseBigDecimal(INS_RT);
					}
				}

				// 中長期資金成本
				if (INREAL_RATE_DP != null && ftpRateS != null
						&& ftpRateL != null) {
					// 資金成本 = 平均實質存款利率 + MAX [ (中長期FTP - 短期FTP) , 0 ]
					longCaptialCost = INREAL_RATE_DP.add((ftpRateL
							.subtract(ftpRateS)).max(BigDecimal.ZERO));
				}

			}

			// 短期資金成本********************************************************
			Map<String, Object> rowS = new HashMap<String, Object>();
			// L120S25A.bisCapitalCost_S=短期資金成本
			rowS.put("insOrder", "1"); // 排序用
			rowS.put("insCdCnm", prop.getProperty("L120S25A.bisCapitalCost_S"));
			rowS.put("insRt", INREAL_RATE_DP == null ? "N.A."
					: new NumericFormatter("##.#####").reformat(INREAL_RATE_DP));
			list.add(rowS);

			// 中長期資金成本********************************************************
			Map<String, Object> rowL = new HashMap<String, Object>();
			// L120S25A.bisCapitalCost_L=中長期資金成本
			rowS.put("insCrder", "2"); // 排序用
			rowL.put("insCdCnm", prop.getProperty("L120S25A.bisCapitalCost_L"));
			rowL.put(
					"insRt",
					longCaptialCost == null ? "N.A." : new NumericFormatter(
							"##.#####").reformat(longCaptialCost
							.toPlainString()));
			list.add(rowL);

		}

		Page<Map<String, Object>> pages = LMSUtil.setPageMap(list, pageSetting);
		return new CapMapGridResult(pages.getContent(), list.size());
	}
}