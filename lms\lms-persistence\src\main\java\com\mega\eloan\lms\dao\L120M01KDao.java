/* 
 * L120M01KDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01K;

/** 簽報書逾權記錄檔 **/
public interface L120M01KDao extends IGenericDao<L120M01K> {

	L120M01K findByOid(String oid);

	List<L120M01K> findByMainId(String mainId);

	List<L120M01K> findByIndex01(String mainId, String overType);

	List<L120M01K> findByMainIdAndOverType(String mainId, String[] overTypeArr);
}
