package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 消金信用評等模型(待覆核)
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1035v02")
public class LMS1035V02Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);
		// 加上Button
		// 主管跟經辦都會出現的按鈕
		if(true){
			addToButtonPanel(model, LmsButtonEnum.Filter); 
		}
		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			
		}

		renderJsI18N(LMS1035V01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS1035V01Page');");
	}

	// @Override
	// public String[] getJavascriptPath() {
	// return new String[] { "pagejs/lms/LMS1035V01Page.js" };
	// }
}