package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.IDataObject;

/** 網銀線上增貸 <br/>
[docStatus]
  ● 在判斷是否符合線上增貸資格時，借用 docStatus 去存放{A00:通過, A01:非本行房貸戶, A02:有連保人保證人, A03:央行受限戶, A04:可增貸金額過低} <br/>
  ● 真正寫入申請資料時, 仿資料建檔{全部進件資料: , 編製中:01O, 待覆核:02O}
  
[applyStatus  及   n_applyStatus]
	0A0:受理中(未收件)	0B0:審核中	[Z01:不承做, Z02:轉臨櫃, Z03:已核貸]
*/
@NamedEntityGraph(name = "C122M01A-entity-graph", attributeNodes = { @NamedAttributeNode("c122m01f")})
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C122M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C122M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	
	
	/** 
	 * 申請文件狀態(回傳給網銀)<p/>
	 * 受理中(未收件):0A0  <br/>
	 *  審核中:0B0<br/>
	 *  //Z開頭表示，對網銀而言，該申請己close<br/>
	 *  不承做:Z01<br/>
	 *  轉臨櫃:Z02<br/>
	 *  已核貸:Z03
	 */
	@Size(max=3)
	@Column(name="APPLYSTATUS", length=3, columnDefinition="CHAR(3)")
	private String applyStatus;

	/** 申請時點 **/
	@Column(name="APPLYTS", columnDefinition="TIMESTAMP")
	private Timestamp applyTS;

	/** 申請幣別 **/
	@Size(max=3)
	@Column(name="APPLYCURR", length=3, columnDefinition="CHAR(3)")
	private String applyCurr;

	/** 申請金額(萬元) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="APPLYAMT", columnDefinition="Dec(17,2)")
	private BigDecimal applyAmt;

	/** 借款年限 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="MATURITY", columnDefinition="Dec(5,2)")
	private BigDecimal maturity;
	
	/** 借款年限-月份數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="MATURITYM", columnDefinition="Dec(5,2)")
	private BigDecimal maturityM;

	/** 借款用途 **/
	@Size(max=60)
	@Column(name="PURPOSE", length=60, columnDefinition="VARCHAR(60)")
	private String purpose;

	/** 還款財源 **/
	@Size(max=30)
	@Column(name="RESOURCE", length=30, columnDefinition="VARCHAR(30)")
	private String resource;

	/** 是否寬限期 **/
	@Size(max=1)
	@Column(name="NOWEXTEND", length=1, columnDefinition="CHAR(1)")
	private String nowExtend;

	/** 寬限期 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="EXTYEAR", columnDefinition="Dec(5,2)")
	private BigDecimal extYear;

	/** 貸款利息通知方式。
	通知的種類包括 (1)貸款利息收據 (2)利率調整通知書
	● 貸款利息收據, 是[收據], 有制式的Form => 不適合用 簡訊  
	● 在消金契約書的﹝第N條 利率調整之通知﹞有選項[]簡訊 []書面 []電子郵件 
	註：參考 BTT L517交易(LNP5170, LNF011)
	註：新網銀的房貸增貸畫面，利息收據通知 []郵寄 []電子郵件 []不通知	 
	註：既有的欄位 ELF500_NOTICE_TYPE 會帶入 LNF020_NOTICE_TYPE
	目前值域 {1.郵寄,2.e-mail, 3.不通知, 4.簡訊}
	*/
	@Size(max=1)
	@Column(name="NOTIFYWAY", length=1, columnDefinition="CHAR(1)")
	private String notifyWay;

	/** 申貸結果是否通知客戶 **/
	@Size(max=1)
	@Column(name="NOTIFYCUST", length=1, columnDefinition="CHAR(1)")
	private String notifyCust;
	
	/** 通知內容 **/
	@Size(max=300)
	@Column(name="NOTIFYMEMO", length=300, columnDefinition="VARCHAR(300)")
	private String notifyMemo;

	/** 投資事業 **/
	@Size(max=40)
	@Column(name="INVENT", length=40, columnDefinition="VARCHAR(40)")
	private String invEnt;

	/** 投資事業金額幣別 **/
	@Size(max=3)
	@Column(name="INVENTCURR", length=3, columnDefinition="VARCHAR(3)")
	private String invEntCurr;

	/** 投資事業金額 **/
	@Digits(integer=11, fraction=2, groups = Check.class)
	@Column(name="INVENTAMT", columnDefinition="DEC(11,2)")
	private BigDecimal invEntAmt;

	/** 其他不動產 **/
	@Size(max=70)
	@Column(name="OTHESTATE", length=70, columnDefinition="VARCHAR(70)")
	private String othEstate;

	/** 地號 **/
	@Size(max=80)
	@Column(name="LANDNO", length=80, columnDefinition="VARCHAR(80)")
	private String landNo;

	/** 建號 **/
	@Size(max=80)
	@Column(name="BUILDNO", length=80, columnDefinition="VARCHAR(80)")
	private String buildNo;
	
	/**
	 * applyStatus狀態的變更，要經覆核，才生效。EX由受理中→已核貸，會先在 n_applyStatus 塞入已核貸(此時，回覆網銀仍是受理中)，等覆核後，將n_applyStatus塞入applyStatus
	 */
	@Size(max=3)
	@Column(name="N_APPLYSTATUS", length=3, columnDefinition="VARCHAR(3)")
	private String n_applyStatus;

	/** 身分證發證地 **/
	@Column(name="IDCARDISSUEAREA", length=18, columnDefinition="VARCHAR(18)")
	private String idCardIssueArea;
	
	/** 身分證發證日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="IDCARDISSUEDATE", columnDefinition="DATE")
	private Date idCardIssueDate;
	
	/** 身分證換補資料 {0初發 1補發 2換發} **/
	@Column(name="IDCARDCHGFLAG", length=1, columnDefinition="CHAR(1)")
	private String idCardChgFlag;
	
	/** 身分證有無照片 **/
	@Column(name="IDCARDPHOTO", length=1, columnDefinition="CHAR(1)")
	private String idCardPhoto;
	
	/** 核貸幣別 **/
	@Size(max=3)
	@Column(name="APPROVECURR", length=3, columnDefinition="VARCHAR(3)")
	private String approveCurr;
	
	/** 核貸金額(萬元) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="APPROVEAMT", columnDefinition="Dec(17,2)")
	private BigDecimal approveAmt;
	
	/** 申請IP位址 **/
	@Size(max=45)
	@Column(name="APPLYIPADDR", length=45, columnDefinition="VARCHAR(45)")
	private String applyIPAddr;
	
	/** 申貸項目{'H':'房貸增貸_網銀', 'C':'線上信貸_雲端櫃台(卡友信貸)', 'B':'勞工紓困', 'D':'勞工紓困非e-Loan線上進件'
	 			 'P':'線上信貸_PLOAN系統_主借人','Q':'線上信貸_PLOAN系統_從債務人', 'E':'線上房貸_PLOAN系統', 'F':'線上房貸_PLOAN系統_從債務人',
	 			 'I':'青創100萬以下', 'J':'青創100萬以上', 'O':'其他_主借人', 'R':'其他_從債務人' } **/
	@Column(name="APPLYKIND", length = 1, columnDefinition = "CHAR(1)")
	private String applyKind;
	
	/** 通知T1時點 **/
	@Column(name="NOTIFYT1TS", columnDefinition="TIMESTAMP")
	private Timestamp notifyT1TS;
	
	/**
	 * A:短期週轉(1年期)
	 * B:中期週轉(最長3年期)
	 * C:中長期放款(2-20年期)
	 */
	@Column(name="MATURITYKIND", length=1, columnDefinition="CHAR(1)")
	private String maturityKind;

	/** 活動代碼 **/
	@Column(name="PROMOTIONCODE", length=7, columnDefinition="VARCHAR(7)")
	private String promotionCode;

	/** 聯絡行員 **/
	@Column(name="CONTACTEMPNO", length = 6, columnDefinition = "CHAR(6)")
	private String contactEmpNo;
	
	/** 行銷人員代碼 **/
	@Column(name="MARKETINGSTAFF", length = 10, columnDefinition = "CHAR(10)")
	private String marketingStaff;
	
	/** 姓_英文 **/
    @Column(length = 100, columnDefinition = "CHAR(100)")
    private String custENameLast;
    
    /** 名_英文 **/
    @Column(length = 100, columnDefinition = "CHAR(100)")
    private String custENameFirst;
    
    /** 原始申貸分行 **/
	@Column(name="ORGBRID", length=3, columnDefinition="CHAR(3)")
	private String orgBrId;
    
	/** 線上同意查EJ，值域{空白:舊版}, {自線上同意聯徵查詢上線後:Y或N} **/
	@Column(name="AGREEQUERYEJ", length=1, columnDefinition="CHAR(1)")
	private String agreeQueryEJ;
	
	/** 統計Flag
	 	<ul>
	 	<li>select * from com.bcodetype where codetype='ploan_c122m01a_statFlag'
	 	</li>
	 	<li>也可查看 CLS1220M05Page_zh_TW.properties
	 	</li>
	 	</ul>
	 */
	@Column(name="STATFLAG", length=1, columnDefinition="CHAR(1)")
	private String statFlag;
	
	/** 線上同意查EJ IP **/
	@Column(name="AGREEQUERYEJIP", length=45, columnDefinition="VARCHAR(45)")
	private String agreeQueryEJIp;
	
	/** 線上同意查EJ 時間 **/
	@Column(name="AGREEQUERYEJTS", columnDefinition="TIMESTAMP")
	private Timestamp agreeQueryEJTs;
	
	/** 線上同意查EJ 版號**/
	@Column(name="AGREEQUERYEJVER", length=10, columnDefinition="VARCHAR(10)")
	private String agreeQueryEJVer;
	
	/** 線上同意查EJ 手機**/
	@Column(name="AGREEQUERYEJMTEL", length=14, columnDefinition="VARCHAR(14)")
	private String agreeQueryEJMtel;
	
	/** PLOAN案件編號 **/
	@Column(name="PLOANCASENO", length=30, columnDefinition="VARCHAR(30)")
	private String ploanCaseNo;
	
	/** PLOAN案件ID(彙總案件編號) **/
	@Column(name="PLOANCASEID", length=30, columnDefinition="VARCHAR(30)")
	private String ploanCaseId;
	
	/** PLOAN案件身分別 **/
	@Column(name="PLOANCASEPOS", length=1, columnDefinition="VARCHAR(1)")
	private String ploanCasePos;
	
	/** 本次貸款屬於 	1.新房貸申請	2.既有房貸增貸 */
	@Column(name="PLOAN002_PURPOSETYPE", length=1, columnDefinition="VARCHAR(1)")
	private String ploan002_purposeType;

	/** 行銷方案      select * from com.bcodetype where codetype='ploan_plan' */
	@Column(name="PLOANPLAN", length=20, columnDefinition="VARCHAR(20)")
	private String ploanPlan;
	
	/**執行作廢經辦*/
	@Column(name="VOIDER", length=6, columnDefinition="CHAR(6)")
	private String voider;

	/**作廢時間*/
	@Column(name="VOIDTIME", columnDefinition="TIMESTAMP")
	private Timestamp voidTime;

	/** 查利害關係人的 timestamp **/
	@Column(name="STKHQUERYEJTS", columnDefinition="timestamp")
	private Timestamp stkhQueryEJTs;

	/** 銀行法利害關係人 **/
	@Size(max=1)
	@Column(name="STKHBANK33", length=1, columnDefinition="CHAR(1)")
	private String stkhBank33;

	/** 金控法第44條利害關係人 **/
	@Size(max=1)
	@Column(name="STKHFH44", length=1, columnDefinition="CHAR(1)")
	private String stkhFh44;

	/** 金控法第45條利害關係人 **/
	@Size(max=1)
	@Column(name="STKHFH45", length=1, columnDefinition="CHAR(1)")
	private String stkhFh45;

	/** 實質關係人(授信以外交易) **/
	@Size(max=1)
	@Column(name="STKHRELFG", length=1, columnDefinition="CHAR(1)")
	private String stkhRelFg;

	/** 公司法與本行董事具有控制從屬關係公司 **/
	@Size(max=1)
	@Column(name="STKHCOFG", length=1, columnDefinition="CHAR(1)")
	private String stkhCoFg;

	/** 是否具有美國納稅義務人身分 **/
	@Size(max=1)
	@Column(name="NEEDW8BEN", length=1, columnDefinition="CHAR(1)")
	private String needW8BEN;

	/** 是否具有中華民國以外之納稅義務人身分 **/
	@Size(max=1)
	@Column(name="NEEDCRS", length=1, columnDefinition="CHAR(1)")
	private String needCRS;

	/** 
	 * 利率變動通知方式<p/>
	 * select * from dbo.code_rate_adjustment_notification where disabled=0 <br/>
	 *  ● 2 書面寄送(通訊地址)<br/>
	 *  ● 3 電子郵件
	 */
	@Size(max=1)
	@Column(name="RATEADJNOTIFY", length=1, columnDefinition="CHAR(1)")
	private String rateAdjNotify;
	
	/** 
	 * 是否同意共同行銷<p/>
	 * Y/N/空白
	 */
	@Size(max=1)
	@Column(name="CROSSMARKET", length=1, columnDefinition="CHAR(1)")
	private String crossMarket;
	
	/** PLOAN認證方式  , select * from dbo.code_identity_type  */
	@Column(name="PLOANIDENTITYTYPE", length=16, columnDefinition="VARCHAR(16)")
	private String ploanIdentityType;
	
	/** PLOAN引介分行 */
	@Column(name="PLOANINTRODUCEBR1ST", length=3, columnDefinition="VARCHAR(3)")
	private String ploanIntroduceBr1st;
	
	
	/* ----------------進件管理Start---------------- */
	/** 
	 * 進件類型<p/>
	 * 1.線下<br/>
	 *  2.線上
	 */
	@Size(max=1)
	@Column(name="INCOMTYPE", length=1, columnDefinition="CHAR(1)")
	private String IncomType;
	
	
	/** 30天內於同IP進件次數 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="APPLYIPFREQ", columnDefinition="Dec(5,0)")
	private BigDecimal applyIpFreq;
	
	/** 
	 * 是否為購屋<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="PURCHASEHOUSE", length=1, columnDefinition="CHAR(1)")
	private String purchaseHouse;

	/* ----------------進件管理End---------------- */
	
	/** 電銷中心分攤年月 */
	@Column(name="AMORT_YM", length=7, columnDefinition="CHAR(7)")
	private String amort_ym; //透過 SLMS-00153 批次作業寫入
	
	/** 電銷中心分攤分行 */
	@Column(name="AMORT_OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String amort_ownBrId; //透過 SLMS-00153 批次作業寫入
	
	/** 電銷中心分攤原始分行 */
	@Column(name="AMORT_ORGBRID", length=3, columnDefinition="CHAR(3)")
	private String amort_orgBrId; //透過 SLMS-00153 批次作業寫入
	
	/** 是否符合歡喜信貸初審通過狀態(Y/N) */
	@Size(max=1)
	@Column(name="INITCHECKSTATUS", length=1, columnDefinition="CHAR(1)")
	private String initCheckStatus;

	/** 初審查詢次數 */
	@Digits(integer = 3, fraction = 0)
	@Column(name = "INITCHECKTIMES", columnDefinition = "DECIMAL(3,0)")
	private Integer initCheckTimes;
	
	/** 是否為團貸案件(Y/N) */
	@Size(max=1)
	@Column(name="ISGROUPLOAN", length=1, columnDefinition="CHAR(1)")
	private String isGroupLoan;
	
	/** 婉拒簡訊發送狀態(S/F/U/D)
	 *  S: 成功
	 *  F: 失敗
	 *  U: API在途
	 *  D: 已送達顧客之電信業者，未送達顧客手機
	 */
	@Size(max=1)
	@Column(name="SMSSENDINGSTATUS", length=1, columnDefinition="CHAR(1)")
	private String smsSendingStatus;
	
	/** 
	 *  歡喜信貸初審檢核完成時間
	 */
	@Column(name="INITCHKFINISHTIME", columnDefinition="TIMESTAMP")
	private Timestamp initChkFinishTime;
	
	/** RPA查詢家事時間 **/
	@Column(name="FARPAJOBTS", columnDefinition="timestamp")
	private Timestamp faRpaJobTs;
	
	/** 
	 *  信貸資料被上傳至DW狀態
	 *  Y-已被上傳至DW；N-應被上傳至DW；D-DW上之對應資料應被刪除；null-新建未被賦值之資料
	 */
	@Size(max=1)
	@Column(name="IS_UPLOADED_DW_HPCL", columnDefinition="CHAR(1)")
	private String is_uploaded_dw_hpcl;
	
	/** 線上同意查EJ 時間(補件) **/
	@Column(name="AGREEQUERYEJTS_PATCH", columnDefinition="TIMESTAMP")
	private Timestamp agreeQueryEJTs_patch;

	/** 使用專案      select * from com.bcodetype where codetype='ploan_plan' and codedesc2='usePlan' */
	@Column(name="USEPLAN", length=20, columnDefinition="VARCHAR(20)")
	private String usePlan;
	
	/** 中鋼線上進件查詢負面新聞時間 **/
	@Column(name="QUERYWISENEWSTS", columnDefinition="timestamp")
	private Timestamp queryWiseNewsTs;

	/** 
	 * 取得申請文件狀態(回傳給網銀)<p/>
	 * 受理中(未收件):0A0  <br/>
	 *  審核中:0B0<br/>
	 *  //Z開頭表示，對網銀而言，該申請己close<br/>
	 *  不承做:Z01<br/>
	 *  轉臨櫃:Z02<br/>
	 *  己核貸:Z03
	 */
	public String getApplyStatus() {
		return this.applyStatus;
	}
	/**
	 *  設定申請文件狀態(回傳給網銀)<p/>
	 *  受理中(未收件):0A0  <br/>
	 *  審核中:0B0<br/>
	 *  //Z開頭表示，對網銀而言，該申請己close<br/>
	 *  不承做:Z01<br/>
	 *  轉臨櫃:Z02<br/>
	 *  己核貸:Z03
	 **/
	public void setApplyStatus(String value) {
		this.applyStatus = value;
	}

	/** 取得申請時點 **/
	public Timestamp getApplyTS() {
		return this.applyTS;
	}
	/** 設定申請時點 **/
	public void setApplyTS(Timestamp value) {
		this.applyTS = value;
	}

	/** 取得申請幣別 **/
	public String getApplyCurr() {
		return this.applyCurr;
	}
	/** 設定申請幣別 **/
	public void setApplyCurr(String value) {
		this.applyCurr = value;
	}

	/** 取得申請金額 **/
	public BigDecimal getApplyAmt() {
		return this.applyAmt;
	}
	/** 設定申請金額 **/
	public void setApplyAmt(BigDecimal value) {
		this.applyAmt = value;
	}

	/** 取得借款年限 **/
	public BigDecimal getMaturity() {
		return this.maturity;
	}
	/** 設定借款年限 **/
	public void setMaturity(BigDecimal value) {
		this.maturity = value;
	}
	
	/** 取得借款年限-月數 **/
	public BigDecimal getMaturityM() {
		return this.maturityM;
	}
	/** 設定借款年限-月數 **/
	public void setMaturityM(BigDecimal value) {
		this.maturityM = value;
	}

	/** 取得借款用途 **/
	public String getPurpose() {
		return this.purpose;
	}
	/** 設定借款用途 **/
	public void setPurpose(String value) {
		this.purpose = value;
	}

	/** 取得還款財源 **/
	public String getResource() {
		return this.resource;
	}
	/** 設定還款財源 **/
	public void setResource(String value) {
		this.resource = value;
	}

	/** 取得是否寬限期 **/
	public String getNowExtend() {
		return this.nowExtend;
	}
	/** 設定是否寬限期 **/
	public void setNowExtend(String value) {
		this.nowExtend = value;
	}

	/** 取得寬限期 **/
	public BigDecimal getExtYear() {
		return this.extYear;
	}
	/** 設定寬限期 **/
	public void setExtYear(BigDecimal value) {
		this.extYear = value;
	}

	/** 取得貸款利息通知方式 **/
	public String getNotifyWay() {
		return this.notifyWay;
	}
	/** 設定貸款利息通知方式 **/
	public void setNotifyWay(String value) {
		this.notifyWay = value;
	}

	/** 取得申貸結果是否通知客戶 **/
	public String getNotifyCust() {
		return this.notifyCust;
	}
	/** 設定申貸結果是否通知客戶 **/
	public void setNotifyCust(String value) {
		this.notifyCust = value;
	}
	
	/** 取得通知內容 **/
	public String getNotifyMemo() {
		return this.notifyMemo;
	}
	/** 設定通知內容 **/
	public void setNotifyMemo(String value) {
		this.notifyMemo = value;
	}

	/** 取得投資事業 **/
	public String getInvEnt() {
		return this.invEnt;
	}
	/** 設定投資事業 **/
	public void setInvEnt(String value) {
		this.invEnt = value;
	}

	/** 取得投資事業金額幣別 **/
	public String getInvEntCurr() {
		return this.invEntCurr;
	}
	/** 設定投資事業金額幣別 **/
	public void setInvEntCurr(String value) {
		this.invEntCurr = value;
	}

	/** 取得投資事業金額 **/
	public BigDecimal getInvEntAmt() {
		return this.invEntAmt;
	}
	/** 設定投資事業金額 **/
	public void setInvEntAmt(BigDecimal value) {
		this.invEntAmt = value;
	}

	/** 取得其他不動產 **/
	public String getOthEstate() {
		return this.othEstate;
	}
	/** 設定其他不動產 **/
	public void setOthEstate(String value) {
		this.othEstate = value;
	}

	/** 取得地號 **/
	public String getLandNo() {
		return this.landNo;
	}
	/** 設定地號 **/
	public void setLandNo(String value) {
		this.landNo = value;
	}

	/** 取得建號 **/
	public String getBuildNo() {
		return this.buildNo;
	}
	/** 設定建號 **/
	public void setBuildNo(String value) {
		this.buildNo = value;
	}
	public String getN_applyStatus() {
		return this.n_applyStatus;
	}
	public void setN_applyStatus(String value) {
		this.n_applyStatus = value;
	}
	public String getIdCardIssueArea() {
		return idCardIssueArea;
	}
	public void setIdCardIssueArea(String idCardIssueArea) {
		this.idCardIssueArea = idCardIssueArea;
	}
	public Date getIdCardIssueDate() {
		return idCardIssueDate;
	}
	public void setIdCardIssueDate(Date idCardIssueDate) {
		this.idCardIssueDate = idCardIssueDate;
	}
	public String getIdCardChgFlag() {
		return idCardChgFlag;
	}
	public void setIdCardChgFlag(String idCardChgFlag) {
		this.idCardChgFlag = idCardChgFlag;
	}
	public String getIdCardPhoto() {
		return idCardPhoto;
	}
	public void setIdCardPhoto(String idCardPhoto) {
		this.idCardPhoto = idCardPhoto;
	}	
	public String getApproveCurr() {
		return approveCurr;
	}
	public void setApproveCurr(String approveCurr) {
		this.approveCurr = approveCurr;
	}
	public BigDecimal getApproveAmt() {
		return approveAmt;
	}
	public void setApproveAmt(BigDecimal approveAmt) {
		this.approveAmt = approveAmt;
	}
	public String getApplyIPAddr() {
		return applyIPAddr;
	}
	public void setApplyIPAddr(String applyIPAddr) {
		this.applyIPAddr = applyIPAddr;
	}
	public String getApplyKind() {
		return applyKind;
	}
	public void setApplyKind(String applyKind) {
		this.applyKind = applyKind;
	}
	public Timestamp getNotifyT1TS() {
		return notifyT1TS;
	}
	public void setNotifyT1TS(Timestamp notifyT1TS) {
		this.notifyT1TS = notifyT1TS;
	}	
	public String getMaturityKind() {
		return maturityKind;
	}
	public void setMaturityKind(String maturityKind) {
		this.maturityKind = maturityKind;
	}
	public String getPromotionCode() {
		return promotionCode;
	}
	public void setPromotionCode(String promotionCode) {
		this.promotionCode = promotionCode;
	}
	public String getContactEmpNo() {
		return contactEmpNo;
	}
	public void setContactEmpNo(String contactEmpNo) {
		this.contactEmpNo = contactEmpNo;
	}
	public String getMarketingStaff() {
		return marketingStaff;
	}
	public void setMarketingStaff(String marketingStaff) {
		this.marketingStaff = marketingStaff;
	}
	public String getCustENameLast() {
		return custENameLast;
	}
	public void setCustENameLast(String custENameLast) {
		this.custENameLast = custENameLast;
	}
	public String getCustENameFirst() {
		return custENameFirst;
	}
	public void setCustENameFirst(String custENameFirst) {
		this.custENameFirst = custENameFirst;
	}
	public String getOrgBrId() {
		return orgBrId;
	}
	public void setOrgBrId(String orgBrId) {
		this.orgBrId = orgBrId;
	}
	public String getAgreeQueryEJ() {
		return agreeQueryEJ;
	}
	public void setAgreeQueryEJ(String agreeQueryEJ) {
		this.agreeQueryEJ = agreeQueryEJ;
	}
	public String getStatFlag() {
		return statFlag;
	}
	public void setStatFlag(String statFlag) {
		this.statFlag = statFlag;
	}
	public String getAgreeQueryEJIp() {
		return agreeQueryEJIp;
	}
	public void setAgreeQueryEJIp(String agreeQueryEJIp) {
		this.agreeQueryEJIp = agreeQueryEJIp;
	}
	public Timestamp getAgreeQueryEJTs() {
		return agreeQueryEJTs;
	}
	public void setAgreeQueryEJTs(Timestamp agreeQueryEJTs) {
		this.agreeQueryEJTs = agreeQueryEJTs;
	}
	public String getAgreeQueryEJVer() {
		return agreeQueryEJVer;
	}
	public void setAgreeQueryEJVer(String agreeQueryEJVer) {
		this.agreeQueryEJVer = agreeQueryEJVer;
	}
	public String getAgreeQueryEJMtel() {
		return agreeQueryEJMtel;
	}
	public void setAgreeQueryEJMtel(String agreeQueryEJMtel) {
		this.agreeQueryEJMtel = agreeQueryEJMtel;
	}
	public String getPloanCaseNo() {
		return ploanCaseNo;
	}
	public void setPloanCaseNo(String ploanCaseNo) {
		this.ploanCaseNo = ploanCaseNo;
	}
	public String getPloanCaseId() {
		return ploanCaseId;
	}
	public void setPloanCaseId(String ploanCaseId) {
		this.ploanCaseId = ploanCaseId;
	}
	public String getPloanCasePos() {
		return ploanCasePos;
	}
	public void setPloanCasePos(String ploanCasePos) {
		this.ploanCasePos = ploanCasePos;
	}
	
	public String getPloan002_purposeType() {
		return ploan002_purposeType;
	}
	public void setPloan002_purposeType(String ploan002_purposeType) {
		this.ploan002_purposeType = ploan002_purposeType;
	}

	public String getPloanPlan() {
		return ploanPlan;
	}
	public void setPloanPlan(String ploanPlan) {
		this.ploanPlan = ploanPlan;
	}
	
	public String getVoider() {
		return voider;
	}

	public void setVoider(String voider) {
		this.voider = voider;
	}

	public Timestamp getVoidTime() {
		return voidTime;
	}

	public void setVoidTime(Timestamp voidTime) {
		this.voidTime = voidTime;
	}

	/** 取得查利害關係人的 timestamp **/
	public Timestamp getStkhQueryEJTs() {
		return this.stkhQueryEJTs;
	}
	/** 設定查利害關係人的 timestamp **/
	public void setStkhQueryEJTs(Timestamp value) {
		this.stkhQueryEJTs = value;
	}

	/** 取得銀行法利害關係人 **/
	public String getStkhBank33() {
		return this.stkhBank33;
	}
	/** 設定銀行法利害關係人 **/
	public void setStkhBank33(String value) {
		this.stkhBank33 = value;
	}

	/** 取得金控法第44條利害關係人 **/
	public String getStkhFh44() {
		return this.stkhFh44;
	}
	/** 設定金控法第44條利害關係人 **/
	public void setStkhFh44(String value) {
		this.stkhFh44 = value;
	}

	/** 取得金控法第45條利害關係人 **/
	public String getStkhFh45() {
		return this.stkhFh45;
	}
	/** 設定金控法第45條利害關係人 **/
	public void setStkhFh45(String value) {
		this.stkhFh45 = value;
	}

	/** 取得實質關係人(授信以外交易) **/
	public String getStkhRelFg() {
		return this.stkhRelFg;
	}
	/** 設定實質關係人(授信以外交易) **/
	public void setStkhRelFg(String value) {
		this.stkhRelFg = value;
	}

	/** 取得公司法與本行董事具有控制從屬關係公司 **/
	public String getStkhCoFg() {
		return this.stkhCoFg;
	}
	/** 設定公司法與本行董事具有控制從屬關係公司 **/
	public void setStkhCoFg(String value) {
		this.stkhCoFg = value;
	}

	/** 取得是否具有美國納稅義務人身分 **/
	public String getNeedW8BEN() {
		return this.needW8BEN;
	}
	/** 設定是否具有美國納稅義務人身分 **/
	public void setNeedW8BEN(String value) {
		this.needW8BEN = value;
	}

	/** 取得是否具有中華民國以外之納稅義務人身分 **/
	public String getNeedCRS() {
		return this.needCRS;
	}
	/** 設定是否具有中華民國以外之納稅義務人身分 **/
	public void setNeedCRS(String value) {
		this.needCRS = value;
	}

	/** 
	 * 取得利率變動通知方式<p/>
	 * select * from dbo.code_rate_adjustment_notification where disabled=0 <br/>
	 *  ● 2 書面寄送(通訊地址)<br/>
	 *  ● 3 電子郵件
	 */
	public String getRateAdjNotify() {
		return this.rateAdjNotify;
	}
	/**
	 *  設定利率變動通知方式<p/>
	 *  select * from dbo.code_rate_adjustment_notification where disabled=0 <br/>
	 *  ● 2 書面寄送(通訊地址)<br/>
	 *  ● 3 電子郵件
	 **/
	public void setRateAdjNotify(String value) {
		this.rateAdjNotify = value;
	}

	/** 
	 * 取得是否同意共同行銷<p/>
	 * Y/N/空白
	 */
	public String getCrossMarket() {
		return this.crossMarket;
	}
	/**
	 *  設定是否同意共同行銷<p/>
	 *  Y/N/空白
	 **/
	public void setCrossMarket(String value) {
		this.crossMarket = value;
	}

	/** PLOAN認證方式 */
	public String getPloanIdentityType() {
		return ploanIdentityType;
	}
	/** PLOAN認證方式 */
	public void setPloanIdentityType(String ploanIdentityType) {
		this.ploanIdentityType = ploanIdentityType;
	}

	/** PLOAN引介分行 */
	public String getPloanIntroduceBr1st() {
		return ploanIntroduceBr1st;
	}
	/** PLOAN引介分行 */
	public void setPloanIntroduceBr1st(String ploanIntroduceBr1st) {
		this.ploanIntroduceBr1st = ploanIntroduceBr1st;
	}


	/**
	 * 顯示用欄位
	 * For View 顯示是否為紓困
	 * @param isOnlyProd69
	 */
	@Transient
	private String loanBrNo;
	
	public void setLoanBrNo(String loanBrNo) {
		this.loanBrNo = loanBrNo;
	}

	public String getLoanBrNo() {
		return loanBrNo;
	}

	@Transient
	private String payrollTransfersBrNo;

	public String getPayrollTransfersBrNo() {
		return payrollTransfersBrNo;
	}
	public void setPayrollTransfersBrNo(String payrollTransfersBrNo) {
		this.payrollTransfersBrNo = payrollTransfersBrNo;
	}
	
	/* ----------------進件管理Start---------------- */
	/** 取得進件類型 **/
	public String getIncomType() {
		return this.IncomType;
	}
	/** 設定進件類型 **/
	public void setIncomType(String value) {
		this.IncomType = value;
	}
	
	/** 取得30天內於同IP進件次數 **/
	public BigDecimal getApplyIpFreq() {
		return this.applyIpFreq;
	}
	/** 設定30天內於同IP進件次數**/
	public void setApplyIpFreq(BigDecimal value) {
		this.applyIpFreq = value;
	}

	/** 
	 * 取得是否為購屋<p/>
	 * Y/N
	 */
	public String getPurchaseHouse() {
		return this.purchaseHouse;
	}
	/**
	 *  設定是否為購屋<p/>
	 *  Y/N
	 **/
	public void setPurchaseHouse(String value) {
		this.purchaseHouse = value;
	}
	
	// bi-directional many-to-one association to 
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false)})
	private C122M01F c122m01f;
	
	public C122M01F getC122m01f() {
		return c122m01f;
	}
	public void setC122m01f(C122M01F c122m01f) {
		this.c122m01f = c122m01f;
	}
	
	/* ----------------進件管理End---------------- */

	public String getAmort_ym() {
		return amort_ym;
	}
	public void setAmort_ym(String amort_ym) {
		this.amort_ym = amort_ym;
	}
	public String getAmort_ownBrId() {
		return amort_ownBrId;
	}
	public void setAmort_ownBrId(String amort_ownBrId) {
		this.amort_ownBrId = amort_ownBrId;
	}
	public String getAmort_orgBrId() {
		return amort_orgBrId;
	}
	public void setAmort_orgBrId(String amort_orgBrId) {
		this.amort_orgBrId = amort_orgBrId;
	}
	public String getInitCheckStatus() {
		return initCheckStatus;
	}
	public void setInitCheckStatus(String initCheckStatus) {
		this.initCheckStatus = initCheckStatus;
	}
	public Integer getInitCheckTimes() {
		return initCheckTimes;
	}
	public void setInitCheckTimes(Integer initCheckTimes) {
		this.initCheckTimes = initCheckTimes;
	}
	public String getIsGroupLoan() {
		return isGroupLoan;
	}
	public void setIsGroupLoan(String isGroupLoan) {
		this.isGroupLoan = isGroupLoan;
	}
	public String getSmsSendingStatus() {
		return smsSendingStatus;
	}
	public void setSmsSendingStatus(String smsSendingStatus) {
		this.smsSendingStatus = smsSendingStatus;
	}
	public Timestamp getInitChkFinishTime() {
		return initChkFinishTime;
	}
	public void setInitChkFinishTime(Timestamp initChkFinishTime) {
		this.initChkFinishTime = initChkFinishTime;
	}
	public String getIs_uploaded_dw_hpcl() {
		return is_uploaded_dw_hpcl;
	}
	public void setIs_uploaded_dw_hpcl(String is_uploaded_dw_hpcl) {
		this.is_uploaded_dw_hpcl = is_uploaded_dw_hpcl;
	}
	public Timestamp getFaRpaJobTs() {
		return faRpaJobTs;
	}
	public void setFaRpaJobTs(Timestamp faRpaJobTs) {
		this.faRpaJobTs = faRpaJobTs;
	}
	public void setAgreeQueryEJTs_patch(Timestamp agreeQueryEJTs_patch) {
		this.agreeQueryEJTs_patch = agreeQueryEJTs_patch;
	}
	public Timestamp getAgreeQueryEJTs_patch() {
		return agreeQueryEJTs_patch;
	}

	public String getUsePlan() {
		return usePlan;
	}

	public void setUsePlan(String usePlan) {
		this.usePlan = usePlan;
	}
	public Timestamp getQueryWiseNewsTs() {
		return queryWiseNewsTs;
	}
	public void setQueryWiseNewsTs(Timestamp queryWiseNewsTs) {
		this.queryWiseNewsTs = queryWiseNewsTs;
	}
}
