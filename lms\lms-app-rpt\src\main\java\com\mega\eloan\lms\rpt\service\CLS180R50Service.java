package com.mega.eloan.lms.rpt.service;

import java.util.Map;
import java.util.Properties;

import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;




/**
 * <pre>
 * 整批房貸統計表
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS180R50Service {
	
	public Map<String, Map<String, Object>> getStatisticsDataForMonthlyReport(String branchNo, Properties prop);
	
	public int setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException;
	
	public int setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException;
	
	public int setBodyContent(WritableSheet sheet, Map<String, Map<String, Object>> dataMap, int colIndex, int rowIndex) throws RowsExceededException, WriteException;
	
	public int setBodyContentForHeadOffice(WritableSheet sheet, Map<String, Map<String, Object>> dataMap, int colIndex, int rowIndex) throws RowsExceededException, WriteException;
	
	public void setFooterContent(WritableSheet sheet, Map<String, Integer> footerMap, Properties prop, int rowIndex) throws WriteException;
}
