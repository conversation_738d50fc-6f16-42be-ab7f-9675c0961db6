/* 
 * L810M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L810M01A;

/** 優惠貸款相關控制表 **/
public interface L810M01ADao extends IGenericDao<L810M01A> {

	L810M01A findByOid(String oid);
	
	List<L810M01A> findByMainId(String mainId);

	List<L810M01A> findByIndex01(String brno, String useType, String rptType);
}