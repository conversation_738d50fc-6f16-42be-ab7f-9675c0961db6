/* 
 * C160S01FDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import java.math.BigDecimal;
import com.mega.eloan.lms.dao.C160S01FDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160S01F;

/** 代償轉貸借新還舊明細檔 **/
@Repository
public class C160S01FDaoImpl extends LMSJpaDao<C160S01F, String>
	implements C160S01FDao {

	@Override
	public C160S01F findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160S01F> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C160S01F> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public List<C160S01F> findByMainIdSeqRefMainid(String mainId,Integer seq,String refmainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		List<C160S01F> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public BigDecimal getSubAmtSum(String mainId,Integer seq,String refmainId) {
		List<C160S01F> c160s01flist=findByMainIdSeqRefMainid(mainId,seq,refmainId);
		BigDecimal sum=new BigDecimal(0);
		for(C160S01F c160s01f:c160s01flist){
			if(Util.isNotEmpty(c160s01f.getSubAmt())){
				sum=sum.add(c160s01f.getSubAmt());
			}
		}
		return sum;
	}
	
	@Override
	public C160S01F findByUniqueKey(String mainId, Integer seq, String bankNo, String branchNo, String subACNo, String refmainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		search.addSearchModeParameters(SearchMode.EQUALS, "bankNo", bankNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchNo", branchNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "subACNo", subACNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160S01F> findByIndex01(String mainId, Integer seq, String bankNo, String branchNo, String subACNo, String refmainId){
		ISearch search = createSearchTemplete();
		List<C160S01F> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (bankNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bankNo", bankNo);
		if (branchNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchNo", branchNo);
		if (subACNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subACNo", subACNo);
		if (refmainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}