package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta_;
import com.mega.eloan.lms.dao.L192M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L192M01A;

@Repository
public class L192M01ADaoImpl extends LMSJpaDao<L192M01A, String> implements
		L192M01ADao {

	// private static final String a =
	// "select distinct a.custId,a.dupNo,a.custName from L160M01A a where a.ownBrId = :ownBrId and a.docStatus = :docStatus";

	@Override
	@SuppressWarnings("unchecked")
	public List<Object[]> getDistinctFinishBorrowId(String ownBrId,
			String docStatus, ISearch search) {
		Query query = entityManager
				.createNamedQuery("L160M01A.selDistinctBorrow");
		query.setParameter("ownBrId", ownBrId);
		query.setParameter("docStatus", docStatus);
		query.setFirstResult(search.getFirstResult());
		query.setMaxResults(search.getMaxResults());
		List<Object[]> resultList = query.getResultList();
		return resultList;
	}

	// private static final String aa =
	// "select count(*) from (select distinct a.custId,a.dupNo,a.custName from lms.L160M01A a where a.ownBrId = ?1 and a.docStatus = ?2)";
	public int getCountDistinctFinishBorrowId(String ownBrId, String docStatus) {
		Query query = entityManager
				.createNamedQuery("L160M01A.selDistinctBorrowCount");
		query.setParameter(1, ownBrId);
		query.setParameter(2, docStatus);
		Integer count = (Integer) query.getSingleResult();
		return count.intValue();
	}

	@Override
	public synchronized BigDecimal getWpNo(String brNo, String wpYear) {
		Query query = entityManager.createNamedQuery("L192M01A.getWpNo");
		query.setParameter("ownBrId", brNo);
		query.setParameter("wpYear", Integer.parseInt(wpYear));
		BigDecimal value = (BigDecimal) query.getSingleResult();
		if (value == null) {
			return new BigDecimal("0001");
		}
		return value.add(new BigDecimal(1));
	}

	@Override
	public L192M01A findLatestL192M01byBrNoShtTypeInnerAudit(String brNo,
			String shtType, String innerAudit) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				Meta_.ownBrId.getName(), brNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "shtType", shtType);
		search.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
				innerAudit);
		search.addOrderBy(Meta_.docStatus.getName());
		search.addOrderBy("checkDate", true);
		search.setMaxResults(1);
		return findUniqueOrNone(search);
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<Object[]> getDistinctFinishBorrowId2(String ownBrId,
			String docStatus, ISearch search) {
		Query query = entityManager
				.createNamedQuery("L160M01A.selDistinctBorrow2");
		query.setParameter(1, ownBrId);
		query.setParameter(2, docStatus);
		query.setFirstResult(search.getFirstResult());
		query.setMaxResults(search.getMaxResults());
		List<Object[]> resultList = query.getResultList();
		return resultList;
	}

	@Override
	public int getCountDistinctFinishBorrowId2(String ownBrId, String docStatus) {
		Query query = entityManager
				.createNamedQuery("L160M01A.selDistinctBorrowCount2");
		query.setParameter(1, ownBrId);
		query.setParameter(2, FlowDocStatusEnum.已核准.getCode());
		Integer count = (Integer) query.getSingleResult();
		return count.intValue();
	}
}
