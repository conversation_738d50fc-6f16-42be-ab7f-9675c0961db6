package com.mega.eloan.lms.fms.handler.grid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.service.LMS7700Service;
import com.mega.eloan.lms.model.L140MM5A;
import com.mega.eloan.lms.model.L140MM5C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 電子文件維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms7700gridhandler")
public class LMS7700GridHandler extends AbstractGridHandler {

	@Resource
	LMS7700Service lms7700Service;
	
	@Resource
	CodeTypeService codetypeService;
	
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140mm5a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, 
				UtilConstants.Field.目前編製行, user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		Page<? extends GenericBean> page = lms7700Service.findPage(
				L140MM5A.class, pageSetting);

		List<L140MM5A> l140mm5alist = (List<L140MM5A>) page.getContent();

		return new CapGridResult(l140mm5alist, page.getTotalRow());

	}
	
	public CapGridResult queryL140mm5c(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		Page<? extends GenericBean> page = lms7700Service.findPage(
				L140MM5C.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter.put("dataFrom", new CodeTypeFormatter(codetypeService,
				"lms7700_dataFrom",
				com.mega.eloan.common.formatter.CodeTypeFormatter.ShowTypeEnum.ValSpaceDesc));
		
		formatter.put("reason", new CodeTypeFormatter(codetypeService,
				"lms7700_reason",
				com.mega.eloan.common.formatter.CodeTypeFormatter.ShowTypeEnum.ValSpaceDesc));
		
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}
}