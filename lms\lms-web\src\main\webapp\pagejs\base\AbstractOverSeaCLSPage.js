var FORM_ATTR_C120M01A_OID = "_C120M01A_OID";
var FORM_ATTR_C120M01A_MAINID = "_C120M01A_MAINID";
var FORM_ATTR_TX_FLAG = "_TX_FLAG";
/*
 * 因每個頁面傳入的 save 參數不同
 * 為了讓 引入 「授信信用風險管理」遵循檢核 時，能save 住資料
 * 用一個 json 去 keep thickbox["saveData"] 傳入的資料 
 */
var keep_pass_save_param = {};

/**
 * txFlag的值，參考 OverSeaUtil.java
 */
function overSeaCLSPage_initEvent(json_overSeaCLS_query){
	var c120m01a_oid = json_overSeaCLS_query.c120m01a_oid;
	var txFlag = json_overSeaCLS_query.formAttrTxFlag;
	var c120m01a_mainId = json_overSeaCLS_query.c120m01a_mainId;
	var c120m01a_noReqFieldForRating = json_overSeaCLS_query.c120m01a_noReqFieldForRating;
	ilog.debug("overSeaCLSPage_initEvent["+txFlag+"]"+c120m01a_oid+"["+c120m01a_mainId+"]"+c120m01a_noReqFieldForRating);
	
	var $frm = overSeaCLSPage_getForm();
	
	$frm.attr(FORM_ATTR_C120M01A_OID , c120m01a_oid);
	$frm.attr(FORM_ATTR_TX_FLAG      , txFlag);
	$frm.attr(FORM_ATTR_C120M01A_MAINID , c120m01a_mainId);
	
	if(txFlag=="lms1015v00cb" || txFlag=="lms1025v00cb" || txFlag=="lms1035v00cb"){
		//在 LMS1015S02PanelC1 的 constructor(String id, boolean isBasicData) 去指定是否「基本資料」
		//未出現 custPos，所以不用調整
	}else{
		//主借人，用 checkbox 的 keyMan 來設定
		$frm.find("#custPos option[value=M]").remove();
	}
	
	/*
	 是否出現「必填的mark」
	 	<b class="star ratingJPReq">＊</b>
	 */
	if(c120m01a_noReqFieldForRating=="Y"){
		if(isTxFlagJP(txFlag)){
			$frm.find(".ratingJPReq").hide();
		}	
	}else{
		if(isTxFlagJP(txFlag)){
			$frm.find(".ratingJPReq").show();
		}
	}	
	
	if(true){
		$frm.find("button.fx_getRlt2").click(function(){		
			overSeaCLSPage_getRlt2();
		});
		
		$frm.find("#btn_overSeaCLSPage_printCreditRisk").click(function(){		
			overSeaCLSPage_printCreditRisk();
		});
		
		if(txFlag=="lms1015v00cb" || txFlag=="lms1025v00cb" || txFlag=="lms1035v00cb"){
			//在列印時，url的層次不同，且要印出案號
			//先比照國內，於「基本資料」先不出現列印的btn
			$frm.find("#btn_overSeaCLSPage_printCreditRisk").hide();
		}
	}		
	
	if(true){
		//Open頁面
		hs_by_keyMan();				
	}
	
	if(true){
		//================================
		//基本資料  
		$frm.find("input#keyMan[type=checkbox]").click(function(){
	        if ($(this).attr("checked") == true) {
	            $(this).val("Y");
	        }
	        else {
	            $(this).val("N");
	        }
	        hs_by_keyMan();
	    });
		
		$frm.find('#custRlt_main').change(function(){
			$("span.custRlt_mainV").hide();
			
	        switch ($(this).val()) {        
	            case "1":
	                //請選擇企業關係人(含法人、自然人)
	                $("span#custRlt_main1").show();
	                break;
	            case "2":
	                //請選擇親屬關係人
	            	$("span#custRlt_main2").show();
	                break;
	                
	            case "3":
	                //請選擇其他綜合關係
	            	$("span#custRlt_main3").show();
	                break;
	            default:
	                break;
	        }
	    });
	}	
	
	if(true){
		//================================
		//服務單位 
		$frm.find('#jobType1').change(function(){
	        var my_jobType1 = $(this).val();
	        if (my_jobType1 && my_jobType1!='') {
	        	var code = overSeaCLS_get_jobType2CodeValue(my_jobType1);
	        	
	            var item = CommonAPI.loadCombos(code);
	            $frm.find('#jobType2').setItems({
	                item: item[code],
	                format: '{key}'
	            });
	        }else{
	        	//職業別大類由 任1項 → 空白，要清空小類
	        	$frm.find('#jobType2').setItems({
	                item: {},
	                format: '{key}'
	            });            
	        }
	    });
	}	
		
	if(true){
		//================================
		//償債能力
		
		//是否進行ICR分析
		$frm.find('input[name="ICRFlag"]').change(function(){
			var ICRFlag = $(this).val();        
			if(ICRFlag=='Y'){
	        	$frm.find('#ICRYes').show();
	        }else{
	        	$frm.find('#ICR').val('');
	        	$frm.find('#ICRYes').hide();
	        }		
	    });
		//Open頁面
		$frm.find('input[name="ICRFlag"]:checked').triggerHandler('change');
		
		$frm.find('input[name="issueLC"]').change(function(){
			var issueLC = $(this).val();
			if(issueLC=='Y'){					
	        	$frm.find('#trICRFlag').hide();
	        	$frm.find('#ICRYes').hide();
	        	//---
	        	$frm.injectData({'ICRFlag':'', 'ICR':''});
	        }else{
	        	$frm.find('#trICRFlag').show();
	        	$frm.find('#ICRYes').show();
	        }		
	    });
		$frm.find('input[name="issueLC"]:checked').triggerHandler('change');
		
		//因為後 trigger 的 issueLC 可能動到ICRFlag，所以再 trigger ICRFlag
		$frm.find('input[name="ICRFlag"]:checked').triggerHandler('change');
	}
	if(true){
		//Open頁面
		var mateFlag = $frm.find('input[name="mateFlag"]:checked').val();
		if(mateFlag=="B"){
			$frm.find('#div_mateFlag_B').show();
		}else if(mateFlag=="C"){
			$frm.find('#div_mateFlag_C').show();		
		}
	}
	
	if(true){
		//================================
		//配偶資料
		$frm.find('input[name="mateFlag"]').click(function(){	
	        var mateFlag = $(this).val();
	        if(mateFlag=="A"){
	        	$frm.find('#div_mateFlag_B').hide();
	        	$frm.find('#div_mateFlag_C').hide();
	        }else if(mateFlag=="B"){
	        	$frm.find('#div_mateFlag_B').show();
	        	$frm.find('#div_mateFlag_C').hide();
	        }else if(mateFlag=="C"){
	        	$frm.find('#div_mateFlag_B').hide();
	        	$frm.find('#div_mateFlag_C').show();
				_chooseMateInCase();
	        }
	    });
		
		if(true){
			$frm.find('#mJobKind').change(function(){
				var mJobKind = $(this).val();        
				if(mJobKind=='99'){
		        	$frm.find('#mJobOther').show();
		        }else{
		        	$frm.find('#mJobOther').val('');
		        	$frm.find('#mJobOther').hide();
		        }
		    });
			//Open頁面
			$frm.find('#mJobKind').trigger('change');	
		}	
		
		$frm.find('#mFmtJobType1').change(function(){
	        var my_jobType1 = $(this).val();
	        if (my_jobType1 && my_jobType1!='') {
	        	var code = overSeaCLS_get_jobType2CodeValue(my_jobType1);
	        	
	            var item = CommonAPI.loadCombos(code);
	            $frm.find('#mFmtJobType2').setItems({
	                item: item[code],
	                format: '{key}'
	            });
	        }else{
	        	//職業別大類由 任1項 → 空白，要清空小類
	        	$frm.find('#mFmtJobType2').setItems({
	                item: {},
	                format: '{key}'
	            });            
	        }
	    });
	}
	if(true){
		//================================
		//相關查詢資料
		$frm.find("input[name='mbRlt33']").click(function(){
	    	var $desc = $frm.find("#mbRltDscr33");
	    	if ($(this).val() == "2" || $(this).val() == "3") {
	    		$desc.hide();
	        	$desc.val("");
	        }else {
	        	$desc.show();
	        }
	    });
		//Open頁面
		$frm.find("input[name='mbRlt33']:checked").trigger('click');
		
		$frm.find('span.class_l120s01m_queryDate').click(function(){
			//alert(">AbstractOverSeaCLSPage.js (5 matches) [90701] AA");
	    	var val = $( this ).val();
	    	if(val && val.length==10){
	    		$.ajax({
	                handler: 'lms1115formhandler',
	                type: "POST",
	                dataType: "json",
	                action: "queryL120s01m",
	                data: {
	                    custId : $frm.find("#custId").val() ,
	                	dupNo : $frm.find("#dupNo").val() ,
	                    mainId: $frm.attr(FORM_ATTR_C120M01A_MAINID),
	                    'noOpenDoc':true
	                },
	                success: function(json){
	                    var $formL120s01m = $("#formL120s01m");
	                    
	                    //J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
	        			var grpYear = json.formL120s01m.grpYear;
	        			var grpGrrd = json.formL120s01m.grpGrade;
	        			if(grpYear){
	        				//判斷2017以後為新版，之前為舊版
	        				if(parseInt(grpYear, 10) >= 2017){
	        					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
	        			        //評等等級
	        			        $("#grpGrade").setItems({
	        			            item: obj.GroupGrade2017,
	        			            format: "{key}"
	        			        });
	        	
	        				}else{
	        					var obj = CommonAPI.loadCombos(["GroupGrade"]);
	        			        //評等等級
	        			        $("#grpGrade").setItems({
	        			            item: obj.GroupGrade,
	        			            format: "{key}"
	        			        });
	        				}
	        	
	        			}else{
	        				var obj = CommonAPI.loadCombos(["GroupGrade"]);
	        		        
	        		        //評等等級
	        		        $("#grpGrade").setItems({
	        		            item: obj.GroupGrade,
	        		            format: "{key}"
	        		        });

	        			}
	        			
	                    $formL120s01m.setData(json.formL120s01m);
	                    $formL120s01m.readOnlyChilds(true);
	                    
	                    if (json.formL120s01m.dataNotShow_020 == "Y") {
	                        $formL120s01m.find("#data_020").hide();
							$formL120s01m.find("#data_022").hide();
//							J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
			                $formL120s01m.find("#data_023").hide();
	                    }
	                    else {
	                        $formL120s01m.find("#data_020").show();
							//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
							if($formL120s01m.find("#localGroup").val() == "" ){
								//舊案
						        $formL120s01m.find("#data_022").show();	
							}else{
								$formL120s01m.find("#data_022").hide();
							}
							//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
			                $formL120s01m.find("#data_023").show();
	                    }
	                    
	                    if (json.formL120s01m.dataNotShow_080 == "Y") {
	                        $formL120s01m.find("#data_080").hide();
							$formL120s01m.find("#data_082").hide();
							$formL120s01m.find("#data_083").hide();
	                    }
	                    else {
	                        $formL120s01m.find("#data_080").show();
							//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
							if($formL120s01m.find("#localGroup").val() == "" ){
								//舊案
						        $formL120s01m.find("#data_082").show();
							}else{
								$formL120s01m.find("#data_082").hide();
							}
							$formL120s01m.find("#data_083").show();
	                    }
	                    
	                    if (json.formL120s01m.dataNotShow_030 == "Y") {
	                        $formL120s01m.find("#data_030").hide();
							$formL120s01m.find("#data_032").hide();
							$formL120s01m.find("#data_033").hide();
	                    }
	                    else {
	                        $formL120s01m.find("#data_030").show();
							//J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
							if($formL120s01m.find("#localGroup").val() == "" ){
								//舊案
						        $formL120s01m.find("#data_032").show();
							}else{
								$formL120s01m.find("#data_032").hide();
							}
							$formL120s01m.find("#data_033").show();
	                    }
	                    
	                    if (json.formL120s01m.grpNo == "") {
	                        $formL120s01m.find("#grpYear").hide();
	                        $formL120s01m.find("#grpGrade").hide();
	                    }else{
	        				$formL120s01m.find("#grpYear").show();
	                        $formL120s01m.find("#grpGrade").show();
	        			}
	                    
	                    if (json.formL120s01m.mbRlt == "1") {
	        				$formL120s01m.find("#dataDate_060").show();
	                        $formL120s01m.find("#dataDate_070").hide();
	                    }
	                    else {
	                        $formL120s01m.find("#dataDate_060").hide();
	        				$formL120s01m.find("#dataDate_070").show();
	                    }
	                    
						//G-104-0097-001 Web e-Loan 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
	        			if (json.formL120s01m.hasLocalAmt == "Y") {
	        				//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
	                    	if (json.formL120s01m.countryType == "TH") {
	                    		 $formL120s01m.find(".showLocal").show();
	                    		 $formL120s01m.find(".showLocal_KH").hide();
	                             $formL120s01m.find("#localNetValCurr1").val(json.formL120s01m.localNetValCurr);
	                    	}else if (json.formL120s01m.countryType == "KH") {
	                    		 $formL120s01m.find(".showLocal").hide();
	                   		     $formL120s01m.find(".showLocal_KH").show();
	                             $formL120s01m.find("#localNetValCurr1_KH").val(json.formL120s01m.localNetValCurr_KH);
	                    	}else{
	                    		 $formL120s01m.find(".showLocal").hide();
	                   		     $formL120s01m.find(".showLocal_KH").hide();
	                             $formL120s01m.find("#localNetValCurr1").val('');
	                             $formL120s01m.find("#localNetValCurr1_KH").val('');
	                    	}
	                    	
	        				
	        			}else{
	        				//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
	                        $formL120s01m.find(".showLocal").hide();
	                        $formL120s01m.find(".showLocal_KH").hide();
	                        $formL120s01m.find("#localNetValCurr1").val("");
	                        $formL120s01m.find("#localNetValCurr1_KH").val("");
	        			}	
						
						//J-108-0100_05097_B1001 Web e-Loan企金授信系統「授信信用風險管理」遵循檢核表增列海外當地限額辦法之遵循
		            	if (json.formL120s01m.countryType == "TH") {
		            		 $formL120s01m.find(".showLocal").show();
		            		 $formL120s01m.find(".showLocal_KH").hide();
		                     $formL120s01m.find("#localNetValCurr1").val(json.formL120s01m.localNetValCurr);
		            	}else if (json.formL120s01m.countryType == "KH") {
		            		 $formL120s01m.find(".showLocal").hide();
		           		     $formL120s01m.find(".showLocal_KH").show();
		                     $formL120s01m.find("#localNetValCurr1_KH").val(json.formL120s01m.localNetValCurr_KH);
		            	}else{
		            		 $formL120s01m.find(".showLocal").hide();
		           		     $formL120s01m.find(".showLocal_KH").hide();
		                     $formL120s01m.find("#localNetValCurr1").val('');
		                     $formL120s01m.find("#localNetValCurr1_KH").val('');
		            	}
		            	
						
						if (json.formL120s01m.dataNotShow_090 == "Y") {
			                $formL120s01m.find("#data_090").hide();
							$formL120s01m.find("#data_092").hide();
							$formL120s01m.find("#showLocalGroup").val("");
			            }
			            else {
			                //J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
							if($formL120s01m.find("#localGroup").val() == "" ){
								//舊案
						        $formL120s01m.find("#data_090").hide();
							    $formL120s01m.find("#data_092").hide();
								$formL120s01m.find("#showLocalGroup").val("");
							}else{
								$formL120s01m.find("#data_090").show();
							    $formL120s01m.find("#data_092").show();
								//無集團不顯示集團代號
								if($formL120s01m.find("#localGroup").val()=="000000000"){
									$formL120s01m.find("#showLocalGroup").val("N.A.");
								}else{
									$formL120s01m.find("#showLocalGroup").val(parseInt($formL120s01m.find("#localGroup").val(),10));
								}	
							}
			            }
						
	                    $("#tL120s01m").thickbox({ // 使用選取的內容進行彈窗
	                        title: i18n.abstractoverseacls["l120s01m.item26"], // 「授信信用風險管理」遵循檢核
	                        width: 965,
	                        height: 480,
	                        modal: true,
	                        i18n: i18n.def,
	                        buttons: {	                        
	                            "close": function(){
	                                $.thickbox.close();                                
	                            }
	                        }
	                    });
	                }
	            });
	    	}
	    });
		
		//VEDA相關欄位
		$frm.find('input[name="vedaScoreFlag"]').change(function(){
			var v = $(this).val();        
			if(v=='2'||v=='3'){
	        	$frm.find('#vedaScore').val('');
	        }
	    });
		$frm.find('input[name="vedaEnquiriesFlag"]').change(function(){
			var v = $(this).val();        
			if(v=='2'||v=='3'){
	        	$frm.find('#vedaEnquiriesTimes').val('');
	        }
	    });
		$frm.find('input[name="vedaFileAgeFlag"]').change(function(){
			var v = $(this).val();        
			if(v=='2'||v=='3'){
	        	$frm.find('#vedaFileAge').val('');
	        }
	    });

		
		if(isTxFlagJP(txFlag)){
			//在【票信退補紀錄 eChkFlag, 聯徵逾催呆紀錄eJcicFlag】 click NA
			//清空對應的欄位			
			$frm.find("input[name='eChkFlag']").click(function(){
		    	if ($(this).val()== "3") {
		    		var json = {};
		    		if(true){
		    			var clearCol = ["isQdata9","isQdata10"];		    		
			    		$.each(clearCol, function(idx, colName){
			    			json[colName] = "3";
			    		})	
		    		}		    		
		    		$frm.injectData(json);
		        }
		    });
			$frm.find("input[name='eJcicFlag']").click(function(){
		    	if ($(this).val()== "3") {
		    		var json = {};
		    		if(true){
		    			/*
		    			 日本模型的[退票,拒絕往來]，因為無法連線查ETCH
		    			 所以是用 JCIC 的資料來決定
		    			    p.s. JCIC的文件有提到, JCIC只有列出50萬以上的[退票,拒絕往來]記錄
		    			                        所以並不完整
		    			
		    			但因日本地區分行實務上，也無法取得ETCH的資料
		    			所以：當 eJcicFlag==NA 時，也把【日本地區分行】UI的[退票,拒絕往來]設成NA
		    			*/
		    			var clearCol = ["isQdata9", "isQdata10"
		    			                , "isQdata11","isQdata13", "isQdata19", "isQdata20"
			    		                , "isQdata25", "isQdata26", "isQdata28", "isQdata21", "isQdata22"
			    		                , "isQdata23", "isQdata24", "isQdata27"];		    		
			    		$.each(clearCol, function(idx, colName){
			    			json[colName] = "3";
			    		})	
		    		}
		    		if(true){
		    			var clearCol2 = ["balQdata25","balQdata26", "balQdata28", "cntQdata21", "cntQdata22"
			    		                 , "cntQdata23", "cntQdata24", "cntQdata27"];		    		
			    		$.each(clearCol2, function(idx, colName){
			    			json[colName] = "";
			    		})	
		    		}	
		    		if(true){ //清空J10
		    			json["j10_score_flag"] = "NA";
		    			json["j10_score"] = "";
		    		}
		    		$frm.injectData(json);
		        }
		    });
		}
		if(isTxFlagTH(txFlag)){
			//NCB相關欄位
			$frm.find('input[name="ncbOver90DaysFlag"]').change(function(){
				var v = $(this).val();        
				if(v=='2'||v=='3'){
		        	$frm.find('#ncbOver90Days').val('');
		        }
		    });
			$frm.find('input[name="ncbMaximumDpdFlag"]').change(function(){
				var v = $(this).val();        
				if(v=='2'||v=='3'){
		        	$frm.find('#ncbMaximumDpd').val('');
		        }
		    });
			$frm.find('input[name="ncbEnqRecent6MFlag"]').change(function(){
				var v = $(this).val();        
				if(v=='2'||v=='3'){
		        	$frm.find('#ncbEnqRecent6M').val('');
		        }
		    });
			
			$frm.find('input[name="ncbRecord"]').change(function(){
				var v = $(this).val();        
				if(v=='2'||v=='3'){					
		        	$frm.injectData({ 'ncbQDate': ''
		        		,'ncbOver90DaysFlag':'3'  ,'ncbOver90Days':''
	        			,'ncbMaximumDpdFlag':'3'  ,'ncbMaximumDpd':''
        				,'ncbEnqRecent6MFlag':'3' ,'ncbEnqRecent6M':''
		        	});
		        }
		    });
		}
		//==============
		//處理附加檔案
		if(isTxFlagJP(txFlag)){
			if($frm.find("#attchFileOid").val()){
				$frm.find("#getJcicFile").show();
			}
		}else if(isTxFlagAU(txFlag)){
			if($frm.find("#attchFileOid").val()){
				$frm.find("#getVedaFile").show();
			}
		}else if(isTxFlagTH(txFlag)){
			if($frm.find("#attchFileOid").val()){
				$frm.find("#getThIdvFile").show();
			}
		}
		//==============
		//JP
		$frm.find("#href_JP_JCIC_NOTE").click(function(){
			// 找出路徑
			var url_arr= DOMPurify.sanitize(location.pathname).split('/');
			var open_url = "../simple/FileProcessingService";
			if(url_arr.length==6){
				open_url = "../../simple/FileProcessingService";
			}

            $.form.submit({
                url: open_url,
                target: "_blank",
                data: {
                    fileId: "JP_JCIC_NOTE.pdf",
                    fileDownloadName: "JP_JCIC_NOTE.pdf",
                    serviceName: "lmsfiledownloadservice"
                }
            });
        });
		$frm.find("#href_JP_J10_REASON").click(function(){
			// 找出路徑
			var url_arr= DOMPurify.sanitize(location.pathname).split('/');
			var open_url = "../simple/FileProcessingService";
			if(url_arr.length==6){
				open_url = "../../simple/FileProcessingService";
			}

            $.form.submit({
                url: open_url,
                target: "_blank",
                data: {
                    fileId: "JP_J10_REASON.pdf",
                    fileDownloadName: "JP_J10_REASON.pdf",
                    serviceName: "lmsfiledownloadservice"
                }
            });
        });
		$frm.find("#href_AU_VEDA_NOTE").click(function(){
			// 找出路徑
			var url_arr= DOMPurify.sanitize(location.pathname).split('/');
			var open_url = "../simple/FileProcessingService";
			if(url_arr.length==6){
				open_url = "../../simple/FileProcessingService";
			}

            $.form.submit({
                url: open_url,
                target: "_blank",
                data: {
                    fileId: "AU_VEDA_NOTE.pdf",
                    fileDownloadName: "AU_VEDA_NOTE.pdf",
                    serviceName: "lmsfiledownloadservice"
                }
            });
        });
	}
	//---------------
	function hs_by_keyMan(){
		if( $frm.find("#keyMan:checked").val()=="Y"){
			$("tr.hs_by_keyMan").hide();
		}else{
			$("tr.hs_by_keyMan").show();
		}	
	}
	//---------------
	function _chooseMateInCase(){
		
		var grid_id = "grid_ChooseMateInCase";
			
		var my_post_data = {
			formAction : "queryMateInCase",
			oid: $frm.attr(FORM_ATTR_C120M01A_OID)
		};
		
		if($("#"+grid_id+".ui-jqgrid-btable").length >0){
			$("#"+grid_id).jqGrid("setGridParam", {
				postData : my_post_data,
				search : true
			}).trigger("reloadGrid");	        		
		}else{
			$("#"+grid_id).iGrid({
				handler : 'lms1015gridhandler',
				height : 280,
				divWidth : 0,
				postData : my_post_data,			
				colModel : [ {
					colHeader: "&nbsp;", 
					align: "center", width: 10, sortable: false, name: 'keyMan'
				}, {
		            colHeader: i18n.abstractoverseacls["l120s01a.custid"],
		            align: "left", width: 100, sortable: false, name: 'custId'
		        }, {
		        	colHeader: i18n.abstractoverseacls["l120s01a.custname2"],
		            align: "left", width: 100, sortable: false, name: 'custName'
		        }, {
		            colHeader: i18n.abstractoverseacls["l120s01a.custrlt"],
		            align: "left", width: 100, sortable: false, name: 'o_custRlt'
		        }, {
		        	colHeader: i18n.abstractoverseacls["l120s01a.custpos"],
		            align: "left", width: 100, sortable: false, name: 'custPos'
		        }, {
		        	colHeader: 'dupNo', name: 'dupNo', hidden: true
		        } ]
			});
		}    
	            
		$("#thickBoxChooseMateInCase").thickbox({
	        title: i18n.abstractoverseacls["l120s02.thickbox10"],
	        width: 640, height: 440, align: 'center', valign: 'bottom', modal: true,
	        buttons: {
	            "sure": function(){

	            	var row = $("#"+grid_id).getGridParam('selrow');
	            	if(row){
	            		var data = $("#"+grid_id).getRowData(row);
	            		
	            		$frm.find("#C_mCustId").val(data.custId);
	            		$frm.find("#C_mDupNo").val(data.dupNo);
	            		$frm.find("#C_mName").val(data.custName);
	            		$.thickbox.close();
	            	}else{
	            		API.showMessage(i18n.abstractoverseacls["l120s02.alert1"]);
	            	}
	            },
	            "cancel": function(){
	               $.thickbox.close();
	            }
	        }
	    });
	}
	//---------------
	function isTxFlagJP(param){
		if(param=="lms1015v00cb" || param=="lms1015s02cb"){
			return true;
		}
		return false;
	}
	function isTxFlagAU(param){
		if(param=="lms1025v00cb" || param=="lms1025s02cb"){
			return true;
		}
		return false;
	}
	function isTxFlagTH(param){
		if(param=="lms1035v00cb" || param=="lms1035s02cb"){
			return true;
		}
		return false;
	}
}

function overSeaCLSPage_buildItem(){
	//============================
	// 處理下拉選單排序
	/*
	若以  $("select#jobType1") 去查找, 可能會找到2個 element 
	<form id="CLS1205S02bForm"> @ LMSS02APanel02.html
	<form id='overSeaCLSPageForm'> @
	
	=> 改成多判斷 overSeaCLSPageForm 裡面的 element
	*/
	var $frm = overSeaCLSPage_getForm();
	if(true){
		var jobType1_v = $frm.find("select#jobType1").attr("data-codetype") ||'';		
		if(jobType1_v){
			$.ajax({
				type : "POST", handler : "lms1015m01formhandler", async: false,//用「同步」的方式
				data : {
					formAction : "codeTypeWithOrder" ,
					key : jobType1_v
				},
				success:function(obj){
					var chooseItem1 = $frm.find("select#jobType1");
					var chooseItem2 = $frm.find("select#mFmtJobType1");
					var _addSpace = false;
					if(chooseItem1.attr("space")=="true"){
						_addSpace = true;	
					}
					if(chooseItem2.attr("space")=="true"){
						_addSpace = true;	
					}
					
					$.each(obj.itemOrder, function(idx, c_val) {
						var currobj = {};
						var c_desc = obj.item[c_val];
						currobj[c_val] = c_desc;
				
						//select
						chooseItem1.setItems({ item: currobj, format: "{key}", clear:false, space: (_addSpace?(idx==0):false) });
						chooseItem2.setItems({ item: currobj, format: "{key}", clear:false, space: (_addSpace?(idx==0):false) });
					});
				}
			});
		}
	}
	
}

function overSeaCLSPage_2ndInjectData_afterInitEvent(json){
	var $frm = overSeaCLSPage_getForm();
	$frm.find("#jobType1").trigger('change');
	$frm.find("#jobType2").val( json.jobType2 );
	
	$frm.find("#mFmtJobType1").trigger('change');
	$frm.find("#mFmtJobType2").val( json.mFmtJobType2 );
}

function overSeaCLSPage_saveAjaxParam(param){
	keep_pass_save_param = param || {};
}

function overSeaCLSPage_runSaveAjax(){

	return $.ajax({type : "POST", handler : 'lms1015m01formhandler',
		data : $.extend(
		{'formAction':'overSeaCLS_save'}
		, keep_pass_save_param
		, overSeaCLSPage_getForm().serializeData()
		, {'formAttrTxFlag':overSeaCLSPage_getForm().attr(FORM_ATTR_TX_FLAG)||''}
	  )
	});
}

function overSeaCLSPage_getForm(){
	return $("#overSeaCLSPageForm");
}

function overSeaCLSPage_coAddr_sameAs_fAddr(){
	var $frm = overSeaCLSPage_getForm();
	
	var fAddr = $frm.find("#fAddr").val();
	if (fAddr == undefined || fAddr == "") {
	    //戶籍地址為空!
	    CommonAPI.showMessage(i18n.abstractoverseacls["l120s02.alert11"]);
	} else {
		$frm.find("#coAddr").val(fAddr);
	}
}
function overSeaCLSPage_reg_imp_custData(){
	var $frm = overSeaCLSPage_getForm();
	
	$.ajax({
        handler: 'lms1115formhandler',
        type: "POST",
        data: {
            formAction: "getCustData3",
            oid: $frm.attr(FORM_ATTR_C120M01A_OID)
        },
        success: function(json){
        	if(json.CLS1205S02aForm){
        		/*
	        	custId 		"19541007??"
        		custName 	"Test１９５４１００７"
	        	birthday	"0001-01-01"
	        	fAddr		"00000"
        		busCode		"130300"
        		bussKind	""
        		ecoNm		"國外非金融機構－在台無住所外國人"
        		ecoNm07A	""
        		custClass	"2"
        		displayBusCd"130300：國外非金融機構－在台無住所外國人"
        		*/
        		if(json.CLS1205S02aForm.custName){
        			$frm.find("#custName").val(DOMPurify.sanitize(json.CLS1205S02aForm.custName));
        		}
        		if(json.CLS1205S02aForm.birthday){
        			$frm.find("#birthday").val(DOMPurify.sanitize(json.CLS1205S02aForm.birthday));
        		}
        		if(json.CLS1205S02aForm.fAddr){
        			$frm.find("#fAddr").val(DOMPurify.sanitize(json.CLS1205S02aForm.fAddr));
        		}
        		if(json.CLS1205S02aForm.busCode){
        			$frm.find("#busCode").val(DOMPurify.sanitize(json.CLS1205S02aForm.busCode));
        		}
        		if(json.CLS1205S02aForm.ecoNm){
        			$frm.find("#ecoNm").val(DOMPurify.sanitize(json.CLS1205S02aForm.ecoNm));
        		}
        		if(json.CLS1205S02aForm.custClass){
        			$frm.find("#o_custClass").val(DOMPurify.sanitize(json.CLS1205S02aForm.custClass));
        		}
        		$frm.find("input[name=o_crdType][value=NO]").attr('checked','checked');
        	}
        	
        	//只在個人戶才執行
        	if(json.CLS1205S02aForm.busCode=="060000" || json.CLS1205S02aForm.busCode=="130300" ){
        		$div = $("#thickBoxImp_custData");
            	if(json.empty1 && json.empty2 && json.empty3 && json.empty4){
    		    	//下拉式選單全部找不到，顯示執行成功
            		//CommonAPI.showMessage(i18n.def["runSuccess"]);
            	}else{
            		
            		_helper( json.empty1, json.sCoAddr,  "#tr_s_coAddr", "#s_coAddr");
            		_helper( json.empty2, json.sMComTel, "#tr_s_comTel", "#s_comTel");
            		_helper( json.empty3, json.sMTel,    "#tr_s_mTel"  , "#s_mTel");
            		_helper( json.empty4, json.sEmail,   "#tr_s_email" , "#s_email");
            		        		        		  
            		$div.thickbox({
        		        title: i18n.abstractoverseacls["l120s02.thickbox6"],
        		        width: 640, height: 280, align: 'center', valign: 'bottom', modal: true,
        		        buttons: {
        		            "sure": function(){

        		            	if( $("#tr_s_coAddr").is(":visible")){
        		            		$frm.find("#coAddr").val( $("#s_coAddr option:selected").val() );
        		            	}
        		            	if( $("#tr_s_comTel").is(":visible")){
        		            		$frm.find("#comTel").val( $("#s_comTel option:selected").val() );
        		            	}
        		            	if( $("#tr_s_mTel").is(":visible")){
        		            		$frm.find("#mTel").val( $("#s_mTel option:selected").val() );
        		            	}
        		            	if( $("#tr_s_email").is(":visible")){
        		            		$frm.find("#email").val( $("#s_email option:selected").val() );
        		            	}
        		                $.thickbox.close();
        		            },
        		            "cancel": function(){
        		                API.confirmMessage(i18n.def['flow.exit'], function(res){
        		                    if (res) {
        		                        $.thickbox.close();
        		                    }
        		                });
        		            }
        		        }
        		    });
            		
    		    }
        	} 
        	$("button#saveData").trigger('click');
        }
    });
	
	function _helper( jsonIsEmpty, jsonVal, htmlTrId, htmlTrSelId){
		if(jsonIsEmpty){
			$(htmlTrId).hide();
		}else{
			$(htmlTrSelId).setItems({ item : jsonVal, format : "{key}", space: false });
			$(htmlTrId).show();
		}
	}
}

function overSeaCLSPage_reg_imp_busCode(){
	var $frm = overSeaCLSPage_getForm();
	$.ajax({
        handler: 'lms1115formhandler',
        type: "POST",
        data: {
            formAction: "getBusCdAndCustClass",
            custId: $frm.find("#custId").val(),
            dupNo: $frm.find("#dupNo").val()
        },
        success: function(json){
        	var _inj = {};
        	if(true){
        		_inj['busCode'] = json.busCode;
        		_inj['ecoNm'] = json.ecoNm;
        	}
        	$frm.injectData(_inj);
        }
    });
}

function overSeaCLSPage_reg_o_custRlt(){
	var todo = function(data){
		$('#o_custRltDesc').val(data.context);
        $('#o_custRlt').val(data.rKindD);
    };
    relationshipBT(todo);
    
    function relationshipBT(callback){ //關係
        //初始化登錄關係畫面
        $("#custRlt_main").val("");
        $("span.custRlt_mainV").hide();
        $("select.custRlt_sel").val("");
        
        $("#thickboxCustRlt").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.abstractoverseacls["l120s02.thickbox7"],
            width: 600, height: 220, modal: true, align: "center", valign: "bottom",
            buttons: {
                "sure": function(){
                    var context;
                    var rKindM = $('#custRlt_main').find(":selected").val();
                    var rKindD;
                    if(rKindM=="1"){
                    	var $elm = $("select.custRlt_sel[name=rationSelect1]").find(":selected");
                        if ($elm.val() == "") {
                            //grid.selrow=請先選擇一筆資料。
                            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                        }
                        context = $elm.text();
                        rKindD = $elm.val();
                    }else if(rKindM=="2"){
                    	var $elm = $("select.custRlt_sel[name=rationSelect2]").find(":selected");
                    	if ($elm.val() == "") {
                            //grid.selrow=請先選擇一筆資料。
                            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                        }
                        context = $elm.text();
                        rKindD = $elm.val();
                    }else if(rKindM=="3"){
                    	var $elmA = $("select.custRlt_sel[name=rationSelect31]").find(":selected");
                    	var $elmB = $("select.custRlt_sel[name=rationSelect32]").find(":selected");
                    	if ($elmA.val() == "" || $elmB.val() == "") {
                            //grid.selrow=請先選擇一筆資料。
                            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                        }
                        context = $elmA.text() + '-' + $elmB.text();
                        rKindD = $elmA.val() + $elmB.val();
                    }else{
                    	return CommonAPI.showMessage(i18n.def["grid.selrow"]);
                    }
                       
                    var obj = {};
                    obj.context = context;
                    obj.rKindM = rKindM;
                    obj.rKindD = rKindD;
                    
                    callback(obj);
                    $.thickbox.close();
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }     
}
 

function overSeaCLSPage_reg_imp_mateData(){
	i18n.def.newData = i18n.def.query;
	AddCustAction.open({
		handler: 'lms1015m01formhandler',
		action : 'imp_mateData',
		data : {
        },
		callback : function(json){
			var $frm = overSeaCLSPage_getForm();
			$frm.injectData(json);
			//關掉 AddCustAction 的 thickbox
        	$.thickbox.close();
		}
	});	
}

function overSeaCLSPage_applyCreditRisk(needAsk){
	 /*
     * 對於 19110101AB 這類的 id,可能查不到 or 不準
     * 以 user 在畫面上輸入的為主
     */
	
	var $frm = overSeaCLSPage_getForm();
	
    var mbRlt = $frm.find("input[name='isQdata2']:radio:checked").val();
    var mhRlt44 = $frm.find("input[name='isQdata3']:radio:checked").val();
    var mhRlt45 = $frm.find("input[name='isQdata16']:radio:checked").val();
    //---
    var custId = $frm.find("#custId").val();
	var dupNo = $frm.find("#dupNo").val();
    var busCode = $frm.find("#busCode").val();
    var custName = $frm.find("#custName").val();
    
    $.ajax({
        handler: 'lms1115formhandler',
        type: "POST",
        dataType: "json",
        data: {
            formAction: "prepareImportL120S01m",
            'custId' : custId ,
        	'dupNo' : dupNo,
        	'mbRlt' : mbRlt,
        	'mhRlt44' : mhRlt44,
        	'mhRlt45' : mhRlt45,
        	'busCode' : busCode,
        	'custName' : custName ,
            'mainId' : $frm.attr(FORM_ATTR_C120M01A_MAINID)
        },
        success: function(json_prepare){
        	overSeaCLSPage_proc_needAsk(needAsk).done(function(){
        		//===============
        		//當 server端對應的 method 有 @DomainAuth(value = AuthType.Modify 時
        		//會因此，寫入  BDocOpener
        		//在 view 去 load page並以thickBox打開
        		//thickBox 的 close 不像 page 的 close(會把 BDocOpener 內的 record 刪掉)
        		
        		//為免 另一個 user，開啟同一個客戶，跳出「已被...開啟中」
        		var helper_param = {'noOpenDoc':true};
        		$.ajax({
                    handler: 'lms1115formhandler',
                    type: "POST",
                    dataType: "json",
                    data: $.extend(
                    {
                        formAction: "deleteL120s01m",
                        custId : custId ,
                    	dupNo : dupNo ,
                        mainId: $frm.attr(FORM_ATTR_C120M01A_MAINID)
                    }
                    , helper_param),
                    success: function(){
                    	//===============
                    	overSeaCLSPage_importL120S01m(needAsk, $.extend(json_prepare, helper_param));    	
                    }
                });
            		
        	});        	
        }
    })
}

function overSeaCLSPage_proc_needAsk(needAsk){
	var my_dfd = $.Deferred();
	if(needAsk){
		var $frm = overSeaCLSPage_getForm();
		var val = $frm.find('span.class_l120s01m_queryDate').val();
		if(val && val.length==10){
			CommonAPI.confirmMessage(i18n.abstractoverseacls["l120s01m.confirm1"], function(b){
	            if (b) {
	            	my_dfd.resolve();
	            }else{
	            	my_dfd.reject();
	            }
	        });	
		}else{
			my_dfd.resolve();	
		}		
	}else{
		my_dfd.resolve();
	}
	return my_dfd.promise();
}

function overSeaCLSPage_printCreditRisk(){
	
	var $frm = overSeaCLSPage_getForm();
	var custId = $frm.find("#custId").val();
	var dupNo = $frm.find("#dupNo").val();
	
	var val = $frm.find('span.class_l120s01m_queryDate').val();
	if(val && val.length==10){
		 $.form.submit({
		        url: "../../simple/FileProcessingService",
		        target: "_blank",
		        data: {
		            rptOid: "R30" + "^" + "",
		            mainId: $frm.attr(FORM_ATTR_C120M01A_MAINID),		            
		            fileDownloadName: "lms1205r30.pdf",
		            serviceName: "lms1205r01rptservice",
		            custId: custId,
		            dupNo: dupNo,
		            mode: "ONE",
		            'c120m01a_oid': $frm.attr(FORM_ATTR_C120M01A_OID)
		        }
		    });	
	}else{
        //EFD0002=INFO|報表無資料|
        return CommonAPI.showErrorMessage(i18n.abstractoverseacls["l120s02.alert10"]);
    }
}

function overSeaCLSPage_importL120S01m(needAsk, json_prepare){    
	var $frm = overSeaCLSPage_getForm();   
    $.ajax({
        handler: 'lms1115formhandler',
        type: "POST",
        dataType: "json",
        data:$.extend({
        	formAction: "importL120s01m",
            mainId: $frm.attr(FORM_ATTR_C120M01A_MAINID)
            }, 
            json_prepare
        ),	
        success: function(json_importL120s01m){
        	//=========
        	$.ajax({
                handler: 'lms1115formhandler',
                type: "POST",
                dataType: "json",
                data:$.extend({
                	formAction: "queryL120S01m_date",
                    mainId: $frm.attr(FORM_ATTR_C120M01A_MAINID)
                    }, 
                    json_prepare
                ),	
                success: function(json_queryL120S01m_date){
                	var $frm = overSeaCLSPage_getForm();
                	//可能舊的 form 也 LMSS02APanel05 包含了 同樣的id，為免異常
                	//前面用 $frm 包起來
                	$frm.find("#l120s01m_queryDate").html(DOMPurify.sanitize(json_queryL120S01m_date.l120s01m_queryDate));
                	
                    if (needAsk == true) {
                        //執行成功
                        CommonAPI.showMessage(i18n.abstractoverseacls["l120s01m.message1"]);
                    }
                    
                    /*
                     * 「授信信用風險管理」遵循檢核
                     * 會和 UI上的  ［］本行利害關係人 ［］金控利害關係人(44條) ［］金控利害關係人(45條) 
                     * 的值有關
                     * 
                     * 若不 save 住資料，兩者之間會不一致
                     */
                    overSeaCLSPage_runSaveAjax();
                }
            });           
        }
    });
}
function overSeaCLSPage_showDetail(relType,hasLocal){

	var $frm = overSeaCLSPage_getForm();
	
    var custId = $frm.find("#custId").val();
	var dupNo = $frm.find("#dupNo").val();
    
    // 進行查詢 
    $.ajax({ //查詢主要借款人資料
        handler: 'lms1115formhandler',
        type: "POST",
        dataType: "json",
        action: "queryL120s01o",
        data: {
            custId: custId,
            dupNo: dupNo,
            relType: relType,
            mainId: $frm.attr(FORM_ATTR_C120M01A_MAINID),
            'noOpenDoc':true,
			hasLocal:hasLocal
        },
        success: function(json){
            var $formL120s01o = $("#formL120s01o");
            $formL120s01o.find("#showDetailHtml").html(DOMPurify.sanitize(json.showDetailResult));
            
			//G-104-0097-001 Web e-Loan 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。						
			//海外_編製中("01O"),	
			//海外_待補件("07O"), 	
			//會簽後修改編製中("01K"),	
            var buttons = {};
			if(responseJSON.mainDocStatus =="01O" || responseJSON.mainDocStatus =="07O"  || responseJSON.mainDocStatus =="01K"  ){
				//$formL120s01o.readOnlyChilds(false);   =>不可以設為false 因為有其他不需要開放編輯的input欄位
				
				if (hasLocal == "Y") {
				    buttons["sure"] = function(){
					    $.ajax({  	    
		                    type: "POST",
							dataType: "json",
		                    handler: 'lms1115formhandler',
		                    data: {
		                        formAction: "resetLocalL120S01OAndReCaculate",
		                        custId: custId,
					            dupNo: dupNo,
					            relType: relType,
					            mainId: responseJSON.mainId,
								FORML120S01O: JSON.stringify($formL120s01o.serializeData())
		                    },
		                    success: function(json){
		                        $.thickbox.close();
		                        $.thickbox.close(); 
		                    }
		                });
					};
				}	
				
			}else{
				$formL120s01o.readOnlyChilds(true);
			}
			
			buttons ["cancel" ] = function(){
                 $. thickbox.close ();
            };
			
            $("#tL120s01o").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.abstractoverseacls["l120s01m.item26"], // 「授信信用風險管理」遵循檢核
                width: 965,
                height: 480,
                modal: true,
                i18n: i18n.def,
                buttons: buttons
            });
        }
    });    
}

function overSeaCLSPage_getRlt2(){
	CommonAPI.confirmMessage(i18n.abstractoverseacls["l120s02.confirm3"], function(b){
        if (b) {
        	var $frm = overSeaCLSPage_getForm();
            $.ajax({
                type: "POST",
                handler: 'lms1115formhandler',
                data: {
                    formAction: "getRlt2",
                    oid: $frm.attr(FORM_ATTR_C120M01A_OID)
                },
                success: function(responseData){
                	if(responseData.CLS1205S02eForm){
                		var _inj = {};
                		if(responseData.CLS1205S02eForm.isQdata2){
                			_inj['isQdata2'] = responseData.CLS1205S02eForm.isQdata2;                			
                    	}
						if(responseData.CLS1205S02eForm.isQdata3){
							_inj['isQdata3'] = responseData.CLS1205S02eForm.isQdata3;
						}						
						if(responseData.CLS1205S02eForm.isQdata16){
							_inj['isQdata16'] = responseData.CLS1205S02eForm.isQdata16;
						}
						$frm.injectData(_inj);	
                	}
                	
                	overSeaCLSPage_runSaveAjax();
                	
                    if (responseData.noData && responseData.noData != "") {
                        CommonAPI.showErrorMessage(i18n.abstractoverseacls('l120s02.alert13', {
                            'colName': responseData.noData
                        }));
                    }
                }
            });
        }
        else {
            CommonAPI.showMessage(i18n.abstractoverseacls["l120s02.alert10"]);
        }
    });
}

function overSeaCLSPage_getSel(){
	CommonAPI.confirmMessage(i18n.abstractoverseacls["l120s02.confirm3"], function(b){
        if (b) {
        	var $frm = overSeaCLSPage_getForm();
            $.ajax({
                type: "POST",
                handler: 'lms1115formhandler',
                data: {
                    formAction: "getSel",
                    oid: $frm.attr(FORM_ATTR_C120M01A_OID)
                },
                success: function(responseData){         
                	if(responseData.CLS1205S02eForm){
                		var _inj = {};
                		if(responseData.CLS1205S02eForm.isQdata6){
                			_inj['isQdata6'] = responseData.CLS1205S02eForm.isQdata6;                			
                    	}
						$frm.injectData(_inj);	
                	}
                }
            });
        }
        else {
            CommonAPI.showMessage(i18n.abstractoverseacls["l120s02.alert10"]);
        }
    });
}

function overSeaCLSPage_downloadFileByDocFileOid(docOid){
	ilog.debug("download_oid["+(docOid||'')+"]");
	$.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:docOid
        }
    });
}

function overSeaCLS_logicalDeleteBDocFile(docOid){
	return $.ajax({type : "POST", handler : 'lms1015m01formhandler',
		action: "overSeaCLS_logicalDeleteBDocFile",
		data : {'docOid': docOid }
	});
}

function overSeaCLS_set_c120m01a_attchFileOid(){
	var $frm = overSeaCLSPage_getForm();
	
	var docOid = $frm.find("#attchFileOid").val();
	var c120m01a_oid = $frm.attr(FORM_ATTR_C120M01A_OID);
	
	return $.ajax({type : "POST", handler : 'lms1015m01formhandler',
		action: "overSeaCLS_set_c120m01a_attchFileOid",
		data : {'c120m01a_oid': c120m01a_oid, 'docOid': docOid }
	});
}
//====================
//澳洲-基本資料：附加檔案 veda 
function overSeaCLS_AU_uploadVedaReportDoc(){
	var $frm = overSeaCLSPage_getForm();
	var my_fieldId = ($frm.find("#custId").val()||'')
				+ ($frm.find("#dupNo").val()||'') 
				+ "_veda";
	
	overSeaCLS_common_upload_attchFileOid(my_fieldId , $frm.find("#getVedaFile"));
}

function overSeaCLS_AU_deleteVedaReportDoc(){
	var $frm = overSeaCLSPage_getForm();
	overSeaCLS_common_delete_attchFileOid($frm.find("#getVedaFile"));
}

function overSeaCLS_AU_downloadVedaReportDoc(){
	overSeaCLS_common_download_attchFileOid();
}
//====================
//日本-基本資料：附加檔案 jcic
function overSeaCLS_JP_uploadJcicReportDoc(){
	var $frm = overSeaCLSPage_getForm();
	var my_fieldId = ($frm.find("#custId").val()||'')
				+ ($frm.find("#dupNo").val()||'') 
				+ "_jcic";
	
	overSeaCLS_common_upload_attchFileOid(my_fieldId , $frm.find("#getJcicFile"));
}

function overSeaCLS_JP_deleteJcicReportDoc(){
	var $frm = overSeaCLSPage_getForm();
	overSeaCLS_common_delete_attchFileOid($frm.find("#getJcicFile"));
}

function overSeaCLS_JP_downloadJcicReportDoc(){
	overSeaCLS_common_download_attchFileOid();
}
//====================
//泰國-基本資料：附加檔案 idv
function overSeaCLS_TH_uploadThIdvDoc(){
	var $frm = overSeaCLSPage_getForm();
	var my_fieldId = ($frm.find("#custId").val()||'')
				+ ($frm.find("#dupNo").val()||'') 
				+ "_th_idv";
	
	overSeaCLS_common_upload_attchFileOid(my_fieldId , $frm.find("#getThIdvFile"));	
}

function overSeaCLS_TH_deleteThIdvDoc(){
	var $frm = overSeaCLSPage_getForm();
	overSeaCLS_common_delete_attchFileOid($frm.find("#getThIdvFile"));	
}

function overSeaCLS_TH_downloadThIdvDoc(){
	overSeaCLS_common_download_attchFileOid();
}
//====================
function overSeaCLS_common_upload_attchFileOid(my_fieldId , a_href_elm){
	var $frm = overSeaCLSPage_getForm();
	
	/*
	 deleteDup會讓同一[mainId, fieldId]下只有1個檔案
	 但之後在簽報書中 mainId 都是L120M01A.mainId
	 所以把 fieldId 都加上 (custId+dupNo)
	以免甲的附加檔案，被乙的附加檔案 蓋掉
	 */			
	var limitFileSize=3145728;
	MegaApi.uploadDialog({
		fieldId:my_fieldId,
        fieldIdHtml:"size='30'", fileDescId:"fileDesc", fileDescHtml:"size='30' maxlength='30'",
		subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
		limitSize:limitFileSize, width:320, height:190,
		data:{
			mainId: $frm.attr(FORM_ATTR_C120M01A_MAINID),
			preDelAll: true,
			deleteDup: true
		},
		success : function(obj) {
			a_href_elm.show();
			$frm.find("#attchFileOid").val(obj.fileKey);
			
			overSeaCLS_set_c120m01a_attchFileOid();
		}
   });
}
function overSeaCLS_common_delete_attchFileOid(a_href_elm){
	var $frm = overSeaCLSPage_getForm();
	
	var docOid = $frm.find("#attchFileOid").val();
	if(docOid){
		overSeaCLS_logicalDeleteBDocFile(docOid).done(function(){
			a_href_elm.hide();
			$frm.find("#attchFileOid").val("");

			overSeaCLS_set_c120m01a_attchFileOid();
		});
	}
}
function overSeaCLS_common_download_attchFileOid(){
	var $frm = overSeaCLSPage_getForm();
	
	var docOid = $frm.find("#attchFileOid").val();
	if(docOid){
		overSeaCLSPage_downloadFileByDocFileOid(docOid);
	}else{
		var l120m01a_people_docOid = $("#attchFileOid").val();
		if(l120m01a_people_docOid){
			overSeaCLSPage_downloadFileByDocFileOid(l120m01a_people_docOid);
		}
		
	}
}
//====================
function overSeaCLS_get_jobType2CodeValue(jobType1){
	var code = jobType1;
	if(code=="01"){
		code = "lms1205s01_jobType2a";                    
	}else if(code=="02"){
		code = "lms1205s01_jobType2b";
	}else if(code=="03"){
		code = "lms1205s01_jobType2c";
	}else if(code=="04"){
		code = "lms1205s01_jobType2d";                    
	}else if(code=="05"){
		code = "lms1205s01_jobType2e";
	}else if(code=="06"){
		code = "lms1205s01_jobType2f";                    
	}else if(code=="07"){
		code = "lms1205s01_jobType2g";
	}else if(code=="08"){
		code = "lms1205s01_jobType2h";                    
	}else if(code=="09"){
		code = "lms1205s01_jobType2i";
	}else if(code=="10"){
		code = "lms1205s01_jobType2j";                    
	}else if(code=="11"){
		code = "lms1205s01_jobType2k";
	}else if(code=="12"){
		code = "lms1205s01_jobType2l";                    
	}else if(code=="13"){
		code = "lms1205s01_jobType2m";
	}else if(code=="14"){
		code = "lms1205s01_jobType2n";
	}	
	return code;
}
