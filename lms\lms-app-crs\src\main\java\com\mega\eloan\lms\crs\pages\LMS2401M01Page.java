package com.mega.eloan.lms.crs.pages;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.panels.RetrialPtMgrIdPanel;
import com.mega.eloan.lms.crs.panels.LMS2401S01Panel;
import com.mega.eloan.lms.crs.panels.LMS2401S02Panel;
import com.mega.eloan.lms.model.C240M01A;


@Controller
@RequestMapping("/crs/lms2401m01/{page}")
public class LMS2401M01Page extends AbstractEloanForm {
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS2401M01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnSave", params, getDomainClass(),
				AuthType.Modify	, RetrialDocStatusEnum.編製中
								, RetrialDocStatusEnum.已核准
								, RetrialDocStatusEnum.已產生覆審名單報告檔));
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, RetrialDocStatusEnum.待覆核));
		addAclLabel(model, new AclLabel("_btnApply", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.已核准));
		addAclLabel(model, new AclLabel("_btnSend", params, getDomainClass(),
				AuthType.Modify, RetrialDocStatusEnum.已產生覆審名單報告檔));
		
		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		model.addAttribute("tabID", tabID);
		panel.processPanelData(model, params);
		
		new RetrialPtMgrIdPanel("divRetrialPtMgrIdPanel").processPanelData(model, params);
	}

	// 頁籤
	private Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS2401S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS2401S02Panel(TAB_CTX, true);
			break;
		
		default:
			panel = new LMS2401S01Panel(TAB_CTX, true);
			break;
		}
		renderJsI18N(LMS2401M01Page.class);
		renderJsI18N(LMS2411M01Page.class);
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C240M01A.class;
	}
}
