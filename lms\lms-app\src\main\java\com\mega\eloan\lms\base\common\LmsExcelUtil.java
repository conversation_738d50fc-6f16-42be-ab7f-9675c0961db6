package com.mega.eloan.lms.base.common;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import tw.com.jcs.common.Util;

/**
 * POI 處理 excel
 */
public class LmsExcelUtil {
	
	private LmsExcelUtil() {
		throw new UnsupportedOperationException("Utility class");
	}
	
	//UPGRADETODO Apache POI Util Method 都換完Jxl需要刪除
	public static HSSFCellStyle setCellFormat(HSSFWorkbook book, HSSFFont font,
			HorizontalAlignment horizontalAlignment) {
		return LmsExcelUtil.setCellFormat(book, font, horizontalAlignment, true);
	}

	/**
	 * @param book
	 * @param font
	 * @param horizontalAlignment
	 * @return
	 */
	public static HSSFCellStyle setCellFormat(HSSFWorkbook book, HSSFFont font, HorizontalAlignment horizontalAlignment,
			boolean borderResult) {
		return LmsExcelUtil.setCellFormat(book, font, horizontalAlignment, borderResult, true);
	}

	/**
	 * 設定字型格式
	 * 
	 * @param book
	 *            自行
	 * @param font
	 *            自行
	 * @param alignment
	 *            自行
	 * @param borderResult
	 *            設置邊框
	 * @param wrapResult
	 *            自動換行
	 * @return
	 */
	public static HSSFCellStyle setCellFormat(HSSFWorkbook book, HSSFFont font, HorizontalAlignment horizontalAlignment,
			boolean borderResult, boolean wrapResult) {
		HSSFCellStyle format = book.createCellStyle();

		// 套用字型
		format.setFont(font);
		// 垂直靠上
		format.setVerticalAlignment(VerticalAlignment.TOP);
		// 水平對齊
		format.setAlignment(horizontalAlignment);
		// 自動換行
		format.setWrapText(wrapResult);
		// 設置邊框
		if (borderResult) {
			format.setBorderTop(BorderStyle.THIN);
			format.setBorderBottom(BorderStyle.THIN);
			format.setBorderLeft(BorderStyle.THIN);
			format.setBorderRight(BorderStyle.THIN);
		}
		return format;
	}
	
	/**
	 * 加入儲存格
	 *  
	 * @param row
	 *            要加入新儲存格的列
	 * @param cellNum
	 *            欄位索引(第幾格)
	 * @param content
	 *            儲存格內容
	 * @param format
	 *            格式
	 * <p>
	 * 	參照於CES的作法，使用HSSF/XSSF/SXSSF的共用父介面為參數型別，HSSF/XSSF/SXSSF皆可通用。
	 * 	取代為原本JXL的addCell，簡化POI createCell、setCellValue、setCellStyle的步驟。
	 * </p>
	 */
	public static void addCell(Row row, int cellNum, String content, CellStyle format) {
		Cell cell = row.createCell(cellNum);
		cell.setCellValue(content);
		if (null != format) {
			cell.setCellStyle(format);
		}

	}
	
	/**
	 * 取得 Excel 中指定 Cell 的字串內容，並自動去除前後空白。
	 * 使用 DataFormatter 處理所有型別（數值、日期、文字等），
	 * 若 Cell 為日期格式，預設格式為yyyy-MM-dd做字串轉換輸出。
	 *
	 * @param cell 欲解析的 Apache POI Cell 物件，可能包含數值、日期、文字等內容
	 * @return 去除前後空白後的 Cell 字串內容，若 cell 為 null 則回傳空字串
	 */
	public static String getCellValue(Cell cell) {
		return getCellValue(cell, "yyyy-MM-dd", false);
	}
	
	/**
	 * 取得 Excel 中指定 Cell 的字串內容，並自動去除前後空白。
	 * 使用 DataFormatter 處理所有型別（數值、日期、文字等），
	 * 若 Cell 為日期格式，預設格式為yyyy-MM-dd做字串轉換輸出。
	 *
	 * @param cell 欲解析的 Apache POI Cell 物件，可能包含數值、日期、文字等內容
	 * @param returnNullIfCellIsNull 若為 true，當 cell 為 null 時回傳 null；否則回傳空字串
	 * @return 去除前後空白後的 Cell 字串內容，若為空值根據參數控制回傳 null / 空字串
	 */
	public static String getCellValue(Cell cell, boolean returnNullIfCellIsNull) {
		return getCellValue(cell, "yyyy-MM-dd", returnNullIfCellIsNull);
	}

	/**
	 * 取得 Excel 中指定 Cell 的字串內容，並自動去除前後空白。
	 * 使用 DataFormatter 處理所有型別（數值、日期、文字等），
	 * 若 Cell 為日期格式，則使用指定的格式字串轉換輸出。
	 *
	 * @param cell 欲解析的 Apache POI Cell 物件，可能包含數值、日期、文字等內容
	 * @param dateFormat 當 Cell 為日期格式時，使用的輸出格式（例："yyyy-MM-dd"）
	 * @param returnNullIfCellIsNull 若為 true，當 cell 為 null 時回傳 null；否則回傳空字串
	 * @return 去除前後空白後的 Cell 字串內容，若為空值根據參數控制回傳 null / 空字串
	 */
	public static String getCellValue(Cell cell, String dateFormat, boolean returnNullIfCellIsNull) {
		if (cell == null)
			return returnNullIfCellIsNull ? null : "";

		DataFormatter formatter = new DataFormatter();
		CellType cellType = cell.getCellType();

		if (cellType == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
			Date date = cell.getDateCellValue();
			return new SimpleDateFormat(dateFormat).format(date);
		}

		return Util.trim(formatter.formatCellValue(cell));
	}
}
