package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.C103M01A;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 訪談紀錄表
 * </pre>
 * 
 * @since 2022/04/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/04/27
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls3801m01/{page}")
public class CLS3801M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model,
				new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
						AuthType.Accept, false, CreditDocStatusEnum.海外_待覆核,
						CreditDocStatusEnum.先行動用_待覆核));

		renderJsI18N(CLS3801M01Page.class);

		new DocLogPanel("_docLog").processPanelData(model, params);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C103M01A.class;
	}

}
