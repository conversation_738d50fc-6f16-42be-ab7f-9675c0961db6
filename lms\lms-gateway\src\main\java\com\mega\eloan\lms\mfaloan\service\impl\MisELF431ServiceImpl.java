/* 
 * MisELF431ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF431;
import com.mega.eloan.lms.mfaloan.service.MisELF431Service;

/**
 * <pre>
 * 優惠房貸額度控管檔 MIS.ELF431(ELAPPCON)
 * </pre>
 * 
 * @since 2014/03/21
 */
@Service
public class MisELF431ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF431Service {

	@Override
	public ELF431 findByCntrno(String cntrNo) {
		Map<String, Object> rowData = this.getJdbc().queryForMap(
				"MIS.ELF431_findByCntrNo",
				new Object[] { cntrNo });
		
		if (rowData == null) {
			return null;
		} else {
			ELF431 model = new ELF431();
			DataParse.map2Bean(rowData, model);
			return model;
		}
	}

}
