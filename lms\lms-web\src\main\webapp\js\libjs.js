require.config({
  // all js setting
  paths : {
    'jquery' : 'js/lib/jquery/3.6.0/jquery-3.6.0',
    'jqueryui' : 'js/lib/jquery-ui/1.13.2/jquery-ui',
    'qtip' : 'js/lib/jquery.qtip/3.0.3/jquery.qtip-3.0.3',
    'blockui' : 'js/lib/jquery.blockUI/2.70.0/jquery.blockUI-2.70.0',
    'jqgrid' : 'js/lib/jqGrid/5.5.5/jquery.jqGrid-5.5.5',
    'jqgridi18n' : 'js/lib/jqGrid/5.5.5/i18n/grid.locale-en',
    'corner' : 'js/lib/jquery.corner/2.13/jquery.corner-2.13',
    'uniform' : 'js/lib/jquery.uniform/4.3.0/jquery.uniform.standalone',
    'validate' : 'js/lib/jquery-validation/1.19.5/jquery.validate',
    'jqscrollto' : 'js/lib/jquery.scrollTo/2.1.3/jquery.scrollTo',
    'ajaxfileupload' : 'js/lib/ajaxfileupload/ajaxfileupload',
    'stomp' : 'js/lib/websocket/stomp'
  },
  shim : {
    'jqueryui' : [ 'jquery' ],
    'validate' : [ 'jquery' ],
    'qtip' : [ 'jquery' ],
    'blockui' : [ 'jquery' ],
    'jqgrid' : [ 'jqgridi18n', 'jquery' ],
    'jqgridi18n' : [ 'jquery' ],
    'jqscrollto' : [ 'jquery' ],
    'corner' : [ 'jquery' ],
    'uniform' : [ 'jquery' ],
    'ajaxfileupload' : [ 'jquery' ],
    'stomp' : [ 'jquery', 'jqueryui' ]
  }
});

define('libjs', [ 'jquery', 'jqueryui', 'validate', 'qtip', 'blockui', 'jqgrid', 'jqgridi18n', 'jqscrollto',
  'corner', 'uniform', 'ajaxfileupload', 'stomp' ], function(libjs) {
  console.log("libjs init");
});
