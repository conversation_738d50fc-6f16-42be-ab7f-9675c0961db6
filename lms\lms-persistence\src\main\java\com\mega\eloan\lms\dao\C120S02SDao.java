/* 
 * C120S02SDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S02S;

/** 聯徵T70證券暨期貨違約交割記錄資訊檔 **/
public interface C120S02SDao extends IGenericDao<C120S02S> {

	C120S02S findByOid(String oid);
	
	List<C120S02S> findByMainId(String mainId);
	
	C120S02S findByUniqueKey(String mainId, String custId, String dupNo);

	List<C120S02S> findByList(String mainId, String custId, String dupNo);
}