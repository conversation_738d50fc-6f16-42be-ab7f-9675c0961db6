package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1141BarcodeR01RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * rpt報表service程式
 * </pre>
 * 
 * @since 2014/05/30
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
@Service("cls1141barcoder01rptservice")
public class CLS1141BarcodeR01RptServiceImpl implements FileDownloadService, CLS1141BarcodeR01RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1141BarcodeR01RptServiceImpl.class);
	@Resource
	CLSService clsService;
	@Resource
	CLS1131Service cls1131Service;
	@Resource
	CLS1141Service cls1141Service;
	@Resource
	CLS1151Service cls1151Service;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	BranchService branchService;

	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			if (baos == null) {
				return null;
			} else {
				return baos.toByteArray();
			}
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public OutputStream generateReport(PageParameters params) throws FileNotFoundException, IOException, Exception {

		OutputStream outputStream = null;
		List<InputStream> list = new LinkedList<InputStream>();

		try {
			String mainId = params.getString(EloanConstants.MAIN_ID);
			// 主借人
			C120M01A mainC120M01A = cls1141Service.findC120M01AByMainIdAndKeyMan(mainId);
			// 從債務人
			// 各額度明細表內的借保人資料
			Map<String, Set<String>> relatedCustPos = new HashMap<String, Set<String>>();
			List<L140M01A> l140m01as = cls1141Service.findl140m01aByl120m01cMainid(mainC120M01A.getMainId());
			for (L140M01A l140m01a : l140m01as) {
				List<L140S01A> l140s01as = clsService.findL140S01A(l140m01a);
				for (L140S01A l140s01a : l140s01as) {
					Set<String> custPosList = relatedCustPos.get(l140s01a.getCustId());
					if (custPosList == null) {
						custPosList = new HashSet<String>();
					}
					if (!CapString.trimNull(l140s01a.getCustId()).equals(CapString.trimNull(mainC120M01A.getCustId()))) {
						custPosList.add(l140s01a.getCustPos());
						relatedCustPos.put(l140s01a.getCustId(), custPosList);
					}
				}
			}
			// 依主借人、各個從債務人分別列印PDF後再合併
			// 1.主借人
			OutputStream mainOutputStream = getOutputStream(mainC120M01A, null);
			list.add(new ByteArrayInputStream(((ByteArrayOutputStream) mainOutputStream).toByteArray()));
			// 2.各個從債務人
			for (Entry<String, Set<String>> relateCase : relatedCustPos.entrySet()) {
				OutputStream relOutputStream = getOutputStream(mainC120M01A, relateCase);
				list.add(new ByteArrayInputStream(((ByteArrayOutputStream) relOutputStream).toByteArray()));
			}
			if (list != null && list.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(list, outputStream);
			} else {
				throw new CapMessageException("無PDF檔案資料，請改用一鍵列印HTML", this.getClass());
			}

		} finally {
		}

		return outputStream;
	}

	/**
	 * 取得PDF outputStream
	 * 
	 * @param mainC120M01A
	 * @param relateCaseC120M01A
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	private OutputStream getOutputStream(C120M01A mainC120M01A, Entry<String, Set<String>> relateCase) throws FileNotFoundException,
			IOException, Exception {
		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;
		try {
			locale = LMSUtil.getLocale();
			ReportGenerator generator = new ReportGenerator("report/cls/CLS1220BarcodeR01_" + locale.toString()
					+ ".rpt");
			rptVariableMap = new LinkedHashMap<String, String>();
			rptVariableMap.put("branch",
					"兆豐銀行 - " + mainC120M01A.getOwnBrId() + " " + branchService.getBranchName(mainC120M01A.getOwnBrId()));
			generator.setVariableData(rptVariableMap);
			List<Map<String, String>> rowsDatas = new ArrayList<Map<String, String>>();
			rowsDatas = getRowsDatas(mainC120M01A, relateCase);
			generator.setRowsData(rowsDatas);
			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	/**
	 * 設定CommonBean1
	 * 
	 * @param params
	 * @return
	 */
	private List<Map<String, String>> getRowsDatas(C120M01A mainC120M01A, Entry<String, Set<String>> relateCase) {
		List<Map<String, String>> rowsDatas = new ArrayList<Map<String, String>>();
		List<Map<String, String>> textBarcodeList = getTextAndBarcodeCode(mainC120M01A, relateCase);
		for (Map<String, String> textBarcodeMap : textBarcodeList) {
			Map<String, String> map = new HashMap<String, String>();
			map.put("CommonBean1.field01", textBarcodeMap.get("text"));
			map.put("CommonBean1.field02", textBarcodeMap.get("barcode"));
			rowsDatas.add(map);
		}
		return rowsDatas;
	}

	/**
	 * 取得BARCODE資料LIST
	 * 
	 * @param mainC120M01A
	 * @param relateCaseC122M01A
	 * @return
	 */
	private List<Map<String, String>> getTextAndBarcodeCode(C120M01A mainC120M01A, Entry<String, Set<String>> relateCase) {
		List<Map<String, String>> datas = new ArrayList<Map<String, String>>();
		Map<String, String> map = new HashMap<String, String>();
		// 共同借款人 = "C";
		// 連帶保證人 = "G";
		// 擔保品提供人 = "S";
		// ㄧ般保證人 = "N";
		Map<String, String> casePosMap = codeTypeService.findByCodeType("L140S01A_custPos");
		// 文件標題判斷relateCaseC122M01A是否為NULL產生不同的的文字
		// 兆豐銀行貸款文件(主借款人-A123456789)
		// 兆豐銀行貸款文件(保證人-A123456789)
		String pageTitleText = "";
		if (relateCase == null) {
			pageTitleText = "兆豐銀行貸款文件(主借款人-" + mainC120M01A.getCustId() + ")";
		} else {
			String posDESC = "";
			for (String casePos : relateCase.getValue()) {
				posDESC = CapString.trimNull(casePosMap.get(casePos));
			}
			pageTitleText = "兆豐銀行貸款文件(" + posDESC + "-" + relateCase.getKey() + ")";
		}
		map = new HashMap<String, String>();
		map.put("text", pageTitleText);
		// 條碼
		map.put("barcode", "ITI01400");
		datas.add(map);

		// 案號：PA20220401038507
		// J-112-0067 修改條碼案號規則
		// 由２０２２竹科新安(兆)授字第００５０１號為基準取號
		// "OD"+caseYear(四碼數字)+caseBrId(三碼數字)+"00"+caseSeq(五碼數字) 組成16碼的編號	
		// OD20220200000501 (簽報書編號)
		L120M01A meta = cls1141Service.findL120m01aByMainId(mainC120M01A.getMainId());
		String caseNo = clsService.tranL120M01ACaseNoToMEGAImageCaseNo(
				meta.getCaseYear(), meta.getOwnBrId(),
				meta.getCaseSeq());
		map = new HashMap<String, String>();
		map.put("text", "案號：" + caseNo);
		// 案號條碼
		map.put("barcode", caseNo);
		datas.add(map);

		// 申請日期：20220401
		map = new HashMap<String, String>();
		map.put("text", "申請日期：" + CapDate.formatDate(meta.getCaseDate(), "yyyyMMdd"));
		// 申請日期條碼
		map.put("barcode", CapDate.formatDate(meta.getCaseDate(), "yyyyMMdd"));
		datas.add(map);

		// 主借款人A123456789
		map = new HashMap<String, String>();
		map.put("text", "主借款人：" + mainC120M01A.getCustId());
		// 主借款人條碼0-A123456789
		map.put("barcode", "0-" + mainC120M01A.getCustId());
		datas.add(map);

		if (relateCase == null) {
			// 主借款人部分額外再加一個主借款人條碼1-A123456789
			// 主借款人A123456789
			map = new HashMap<String, String>();
			map.put("text", "主借款人：" + mainC120M01A.getCustId());
			// 主借款人條碼0-A123456789
			map.put("barcode", "1-" + mainC120M01A.getCustId());
			datas.add(map);
		} else {
			// 關係人條碼
			for (String casePos : relateCase.getValue()) {
				String posDESC = CapString.trimNull(casePosMap.get(casePos));
				// 文字
				map = new HashMap<String, String>();
				map.put("text", posDESC + "：" + relateCase.getKey());
				// 條碼
				map.put("barcode", "1-" + relateCase.getKey());
				datas.add(map);
			}
		}
		return datas;
	}

}
