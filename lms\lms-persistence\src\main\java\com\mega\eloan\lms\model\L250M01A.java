package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/**
 * 企消金模擬動審主檔
 * 
 * <AUTHOR>
 * 
 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L250M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L250M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	/**
	 * JOIN條件 L240A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l250m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L250A01A> l250a01a;
	
	@ToStringExclude
	@OneToMany(mappedBy = "l250m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L250M01B> l250m01b;

	/**
	 * 案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/** 經辦 **/
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;

	/** 覆核主管 **/
	@Column(name = "RECHECKID", length = 6, columnDefinition = "CHAR(6)")
	private String reCheckId;

	/** 授信主管 **/
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossId;

	/** 經副襄理 **/
	@Column(name = "MANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String managerId;

	/** 簽報書主檔MAINID **/
	@Column(name = "SRCMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String srcMainId;

	@Column(name = "ALLCANPAY", length = 1, columnDefinition = "CHAR(1)")
	private String allCanPay;

	@Column(name = "RPTID", length = 3, columnDefinition = "CHAR(3)")
	private String rptId;

	private String project1;
	private String project2;
	private String project3;
	private String project4;
	private String project5;
	private String project6;
	private String project7;

	private int version;

	/**
	 * 取得案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/**
	 * 設定案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 取得經辦 **/
	public String getApprId() {
		return this.apprId;
	}

	/** 設定經辦 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得覆核主管 **/
	public String getReCheckId() {
		return this.reCheckId;
	}

	/** 設定覆核主管 **/
	public void setReCheckId(String value) {
		this.reCheckId = value;
	}

	/** 取得授信主管 **/
	public String getBossId() {
		return this.bossId;
	}

	/** 設定授信主管 **/
	public void setBossId(String value) {
		this.bossId = value;
	}

	/** 取得經副襄理 **/
	public String getManagerId() {
		return this.managerId;
	}

	/** 設定經副襄理 **/
	public void setManagerId(String value) {
		this.managerId = value;
	}

	/**
	 * 簽報書主檔MAINID
	 * 
	 * @param srcMainId
	 */
	public void setSrcMainId(String srcMainId) {
		this.srcMainId = srcMainId;
	}

	/**
	 * 簽報書主檔MAINID
	 * 
	 * @return
	 */
	public String getSrcMainId() {
		return srcMainId;
	}

	/**
	 * 全部動用
	 * 
	 * @param allCanPay
	 */
	public void setAllCanPay(String allCanPay) {
		this.allCanPay = allCanPay;
	}

	/**
	 * 全部動用
	 * 
	 * @return
	 */
	public String getAllCanPay() {
		return allCanPay;
	}

	/**
	 * LMS/CLS
	 * 
	 * @param rptId
	 */
	public void setRptId(String rptId) {
		this.rptId = rptId;
	}

	/**
	 * LMS/CLS
	 * 
	 * @return
	 */
	public String getRptId() {
		return rptId;
	}

	/**
	 * 青年安心成家
	 * 
	 * @param project1
	 */
	public void setProject1(String project1) {
		this.project1 = project1;
	}

	/**
	 * 青年安心成家
	 * 
	 * @return
	 */
	public String getProject1() {
		return project1;
	}

	/**
	 * 歡喜房貸
	 * 
	 * @param project2
	 */
	public void setProject2(String project2) {
		this.project2 = project2;
	}

	/**
	 * 歡喜房貸
	 * 
	 * @return
	 */
	public String getProject2() {
		return project2;
	}

	/**
	 * 行家理財(不動產)
	 * 
	 * @param project3
	 */
	public void setProject3(String project3) {
		this.project3 = project3;
	}

	/**
	 * 行家理財(不動產)
	 * 
	 * @return
	 */
	public String getProject3() {
		return project3;
	}

	/**
	 * 行家理財(定存)
	 * 
	 * @param project4
	 */
	public void setProject4(String project4) {
		this.project4 = project4;
	}

	/**
	 * 行家理財(定存)
	 * 
	 * @return
	 */
	public String getProject4() {
		return project4;
	}

	/**
	 * 行家理財(股票擔保)
	 * 
	 * @param project5
	 */
	public void setProject5(String project5) {
		this.project5 = project5;
	}

	/**
	 * 行家理財(股票擔保)
	 * 
	 * @return
	 */
	public String getProject5() {
		return project5;
	}

	public Set<L250A01A> getL250a01a() {
		return l250a01a;
	}

	public void setL250a01a(Set<L250A01A> l250a01a) {
		this.l250a01a = l250a01a;
	}

	public void setL250m01b(Set<L250M01B> l250m01b) {
		this.l250m01b = l250m01b;
	}

	public Set<L250M01B> getL250m01b() {
		return l250m01b;
	}

	/**
	 * 版本
	 * 
	 * @param version
	 */
	public void setVersion(int version) {
		this.version = version;
	}

	/**
	 * 版本
	 * 
	 * @return
	 */
	public int getVersion() {
		return version;
	}

	/**
	 * 行家理財
	 * 
	 * @param project6
	 */
	public void setProject6(String project6) {
		this.project6 = project6;
	}

	/**
	 * 行家理財
	 * 
	 * @return
	 */
	public String getProject6() {
		return project6;
	}

	/**
	 * 行家理財(其它)
	 * 
	 * @param project7
	 */
	public void setProject7(String project7) {
		this.project7 = project7;
	}

	/**
	 * 行家理財(其它)
	 * 
	 * @return
	 */
	public String getProject7() {
		return project7;
	}

}
