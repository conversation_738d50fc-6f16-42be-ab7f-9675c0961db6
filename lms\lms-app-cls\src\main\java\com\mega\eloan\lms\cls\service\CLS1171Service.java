/* 
 * CLS1171Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L141M01B;
import com.mega.eloan.lms.model.L141M01C;
import com.mega.eloan.lms.model.L141M01D;

/**
 * <pre>
 * 聯行額度明細表
 * </pre>
 * 
 * @since 2011/12/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/2,REX,new
 *          </ul>
 */
public interface CLS1171Service extends AbstractService {

	/**
	 * 儲存L141M01B．聯行額度明細表關聯檔
	 * 
	 * @param l141m01bs
	 *            聯行額度明細表關聯檔List
	 */
	public void saveL141m01bs(List<L141M01B> l141m01bs);

	/**
	 * 儲存複製l120m01a
	 * 
	 * @param l141m01bs
	 *            聯行額度明細關聯檔
	 * 
	 * @param l141m01cs
	 *            共同借款人檔
	 * @param entity
	 *            model
	 */
	public void saveL141m01cs(List<L141M01B> l141m01bs,
			List<L141M01C> l141m01cs, GenericBean... entity);

	/**
	 * 儲存L141M01D．聯行額度明細表簽章欄檔
	 * 
	 * @param l141m01ds
	 *            聯行額度明細表簽章欄檔List
	 */
	public void saveL141m01ds(List<L141M01D> l141m01ds);

	/**
	 * 流程
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            要儲存的modle
	 * @param setResult
	 *            是否有設定動作
	 * @param next
	 *            執行的動作
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String next) throws Throwable;

	/**
	 * 刪除 聯行額度明細表簽章欄檔
	 * 
	 * @param l141m01ds
	 *            聯行額度明細表簽章欄檔List
	 */
	public void deleteL141m01ds(List<L141M01D> l141m01ds);

	/**
	 * 查詢簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            銀行類別
	 * @return List<L141M01D>
	 */
	public List<L141M01D> findL141m01dsByMainIdAndBranchType(String mainId,
			String branchType);

	/**
	 * 查詢簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            銀行類別 * 1. 聯行<br/>
	 *            2. 參貸行/額度所屬行
	 * @param staffJob
	 *            人員職稱
	 * @return
	 */
	public L141M01D findL141m01dsByUniqueKey(String mainId, String branchType,
			String staffJob);

	/**
	 * 複製案件簽報書到新聯行額度明細表
	 * 
	 * @param l120m01a
	 *            要複製的案件簽報書
	 * @param brank
	 *            傳送連到的聯行
	 * @param l120m01cs
	 *            關連檔
	 * @return 新的聯行額度明細表
	 */
	public L141M01A copyL120M01A(L120M01A l120m01a, String brank,
			List<L120M01C> l120m01cs);

	/**
	 * 查詢列印所需要的grid
	 * 
	 * @param mainId
	 *            聯行額度明細表 mainId
	 * @param pageSetting
	 *            查詢條件
	 * @return grid
	 */
	public Page<Map<String, Object>> getPringMenu(String mainId,
			ISearch pageSetting);

	public L141M01A findL141M01AByMainId(String mainId);

}