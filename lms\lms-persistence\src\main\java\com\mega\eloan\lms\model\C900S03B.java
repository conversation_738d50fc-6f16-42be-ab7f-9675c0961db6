package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** FTP_IDWM0002檔  **/
@Entity
@Table(name="C900S03B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S03B extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	@Column(name="FN", length=10, columnDefinition="CHAR(10)")
	private String fn;

	@Column(name="GENDATE", length=8, columnDefinition="CHAR(8)")
	private String genDate;
	
	@Column(name="GENTIME", length=6, columnDefinition="CHAR(6)")
	private String genTime;
	
	@Digits(integer=9, fraction=0)
	@Column(name="IDX", columnDefinition="DEC(9,0)")
	private BigDecimal idx;
	
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)")
	private String custId;
	
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;
	
	@Column(name="BRNO", length=3, columnDefinition="CHAR(3)")
	private String brNo;
	
	@Column(name="GRA_CD", length=2, columnDefinition="CHAR(2)")
	private String gra_cd;
	
	@Column(name="GRA_NAME", length=12, columnDefinition="CHAR(12)")
	private String gra_name;
	
	@Column(name="AO_CD", length=6, columnDefinition="CHAR(6)")
	private String ao_cd;
	
	@Column(name="AO_NAME", length=20, columnDefinition="CHAR(20)")
	private String ao_name;
	

	public String getOid() {
		return this.oid;
	}
	
	public void setOid(String value) {
		this.oid = value;
	}

	public String getFn() {
		return fn;
	}

	public void setFn(String fn) {
		this.fn = fn;
	}

	public String getGenDate() {
		return genDate;
	}

	public void setGenDate(String genDate) {
		this.genDate = genDate;
	}

	public String getGenTime() {
		return genTime;
	}

	public void setGenTime(String genTime) {
		this.genTime = genTime;
	}

	public BigDecimal getIdx() {
		return idx;
	}

	public void setIdx(BigDecimal idx) {
		this.idx = idx;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public String getBrNo() {
		return brNo;
	}

	public void setBrNo(String brNo) {
		this.brNo = brNo;
	}

	public String getGra_cd() {
		return gra_cd;
	}

	public void setGra_cd(String gra_cd) {
		this.gra_cd = gra_cd;
	}

	public String getGra_name() {
		return gra_name;
	}

	public void setGra_name(String gra_name) {
		this.gra_name = gra_name;
	}

	public String getAo_cd() {
		return ao_cd;
	}

	public void setAo_cd(String ao_cd) {
		this.ao_cd = ao_cd;
	}

	public String getAo_name() {
		return ao_name;
	}

	public void setAo_name(String ao_name) {
		this.ao_name = ao_name;
	}
}
