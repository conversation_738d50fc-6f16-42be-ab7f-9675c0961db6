var _handler = "lms2430m01formhandler";
var responseJSON = {};

$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");

	// 載入不覆審代碼選項
	$.ajax({
		type: 'post', 
		async: false, 
		handler: _handler, 
		data: {'formAction':'load_nckdFlag'}
	}).done(function(obj){
		var chooseItem = $("#chgNckdFlag");
		if(chooseItem.length > 0) {
			var _addSpace = false;
			if(chooseItem.attr("space")=="true"){
				_addSpace = true;		
			}
			
			$.each(obj.itemOrder, function(idx, brNo) {
				var currobj = {};
				var brName = obj.item[brNo];
				currobj[brNo] = brName;        		
				//select
				chooseItem.setItems({ 
					item: currobj, 
					format: "{value} {key}", 
					clear: false, 
					space: (_addSpace?(idx==0):false) 
				});
			});
		}
	}).fail(function(xhr, status, error) {
		console.error("載入不覆審代碼失敗:", error);
		console.error("錯誤詳情:", {
			status: status,
			statusText: xhr.statusText,
			responseText: xhr.responseText
		});
	}).always(function() {
		console.log("載入不覆審代碼請求完成");
	});
	
	// 初始化表單 
	$.form.init({
		formHandler: _handler, 
		formAction: 'query', 
		loadSuccess: function(json){			
			console.log("表單資料載入成功:", json);
			
			// 設定全域變數
			responseJSON = json;
			
			// 設定借款人資訊
			if (json.custId) $("#custId").text(json.custId);
			if (json.dupNo) $("#dupNo").text(json.dupNo);
			if (json.custName) $("#custName").text(json.custName);
			
			// 設定分行資訊
			if (json.elfBranch) $("#elfBranch").text(json.elfBranch);
			if (json.elfBranchName) $("#elfBranchName").text(json.elfBranchName);
			
			// 設定文件狀態
			if (json.docStatusName) $("#docStatusName").text(json.docStatusName);
			
			// 設定日期資訊
			if (json.elfCrDate) $("#elfCrDate").text(json.elfCrDate);
			if (json.elfLrDate) $("#elfLrDate").text(json.elfLrDate);
			if (json.elfLastRealDt) $("#elfLastRealDt").text(json.elfLastRealDt);
			if (json.elfRemomo) $("#elfRemomo").text(json.elfRemomo);
			
			// 設定建立者和異動者資訊
			if (json.creator) $("#creator").text(json.creator);
			if (json.createTime) $("#createTime").text(json.createTime);
			if (json.updater) $("#updater").text(json.updater);
			if (json.updateTime) $("#updateTime").text(json.updateTime);
			
			// 設定簽核資訊
			if (json.apprId) $("#apprId").text(json.apprId);
			if (json.approvercn) $("#approvercn").text(json.approvercn);
			
			// 設定報表亂碼
			if (json.randomCode) $("#randomCode").text(json.randomCode);
			
			// 設定主頁面借款人顯示
			if (json.custInfo) {
				$("#custInfo").text(json.custInfo);
			}
			
			// 如果沒有資料，初始化為空白表單
			if (!json.mainOid) {
				console.log("沒有 mainOid，初始化空白表單");
				responseJSON.page = "01";
				responseJSON.mainOid = "";
				responseJSON.mainDocStatus = "";
			}
			
			// 建構表單項目
			tabForm.buildItem();
			
			// 確保 Panel 內容已載入後再注入資料
			setTimeout(function() {
				// 先控制頁面 Read/Write 狀態
				if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
					tabForm.lockDoc();
				}
				
				// 注入資料到表單
				tabForm.injectData(json);
				//console.log("資料注入完成");
			}, 100);
		},
		loadError: function(xhr, status, error) {
			console.error("表單資料載入失敗:", error);
			console.error("錯誤詳情:", {
				status: status,
				statusText: xhr.statusText,
				responseText: xhr.responseText
			});
			
			// 即使載入失敗，也要建立基本的表單結構
			responseJSON = {
				page: "01",
				mainOid: "",
				mainDocStatus: ""
			};
			
			// 建構表單項目
			tabForm.buildItem();
			
			// 確保 Panel 內容已載入
			setTimeout(function() {
				console.log("錯誤處理完成，表單結構已建立");
			}, 100);
		}
	});
	
	// 儲存按鈕
	btnPanel.find("#btnSave").click(function(){		
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				if(json.IncompleteMsg){
					API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
				}else{
					API.showMessage(i18n.def.saveSuccess);	
				}	
			}
		}).fail(function(xhr, status, error) {
			console.error("儲存失敗:", error);
			if (typeof API !== 'undefined' && API.showMessage) {
				API.showMessage("儲存失敗，請稍後再試");
			}
		});
	});

	// 呈主管覆核按鈕
	btnPanel.find("#btnSend").click(function(){	
		saveAction().done(function(json_saveAction){
			if(json_saveAction.saveOkFlag){
				API.confirmMessage(i18n.def.confirmApply, function(result){
					if (result) {
						flowAction({'decisionExpr':'呈主管'});	
					}
				});
			}
		}).fail(function(xhr, status, error) {
			console.error("送審前儲存失敗:", error);
			if (typeof API !== 'undefined' && API.showMessage) {
				API.showMessage("送審前儲存失敗，請稍後再試");
			}
		});
	});

	// 覆核按鈕
	btnPanel.find("#btnAccept").click(function(){
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");

			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.def['accept']+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.def['return']+"</label></p>");

			dyna.push("</form>");
			
			dyna.push("</div>");
			
			$('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
			title: i18n.def["confirmApprove"],
			width: 380,
			height: 180,
			align: "center",
			valign: "bottom",
			modal: false,
			i18n: i18n.def,
			buttons: {
				"sure": function(){
					if (!$("#"+_form).valid()) {
						return;
					}
					var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
					if(val=="1"){
						flowAction({'decisionExpr':'核定'});
					}else if(val=="2"){
						flowAction({'decisionExpr':'退回'});
					}
					$.thickbox.close();
				},
				"cancel": function(){
					$.thickbox.close();
				}
			}
		});
	});	
	
	// 流程操作函數
	var flowAction = function(opts){
		return $.ajax({
			type: "POST",
			handler: _handler, 
			action: "flowAction",
			data: $.extend({
				mainOid: $("#mainOid").val(), 
				mainDocStatus: $("#mainDocStatus").val() 
			}, (opts||{}))
		}).done(function(json){
			console.log("流程操作成功:", json);            	
			            	CommonAPI.triggerOpener("gridview", "reloadGrid"); 
            	window.close();            	
		}).fail(function(xhr, status, error) {
			console.error("流程操作失敗:", error);
			console.error("錯誤詳情:", {
				status: status,
				statusText: xhr.statusText,
				responseText: xhr.responseText
			});
			if (typeof API !== 'undefined' && API.showMessage) {
				API.showMessage("流程操作失敗，請稍後再試");
			}
		}).always(function() {
			console.log("流程操作請求完成");
		});
	};
   
});

// 儲存操作函數
function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		var optsPage = {};
		
		return $.ajax({
			type: "POST",
			handler: _handler,
			data: $.extend({
				formAction: "saveMain",
				page: responseJSON.page || $("#page").val() || "01",
				mainOid: responseJSON.mainOid || $("#mainOid").val()
			}, 
			tabForm.serializeData(),
			optsPage,
			(opts||{}))
		}).done(function(json){
			console.log("儲存成功:", json);
			
			// 更新 responseJSON
			responseJSON = $.extend(responseJSON, json);
			
			// 更新顯示資訊
			if (json.custId) $("#custId").text(json.custId);
			if (json.dupNo) $("#dupNo").text(json.dupNo);
			if (json.custName) $("#custName").text(json.custName);
			if (json.elfBranch) $("#elfBranch").text(json.elfBranch);
			if (json.elfBranchName) $("#elfBranchName").text(json.elfBranchName);
			if (json.docStatusName) $("#docStatusName").text(json.docStatusName);
			if (json.creator) $("#creator").text(json.creator);
			if (json.createTime) $("#createTime").text(json.createTime);
			if (json.updater) $("#updater").text(json.updater);
			if (json.updateTime) $("#updateTime").text(json.updateTime);
			if (json.custInfo) $("#custInfo").text(json.custInfo);
			
			tabForm.injectData(json);
			
			//更新 opener 的 Grid
			CommonAPI.triggerOpener("gridview", "reloadGrid");
		}).fail(function(xhr, status, error) {
			console.error("儲存操作失敗:", error);
			console.error("錯誤詳情:", {
				status: status,
				statusText: xhr.statusText,
				responseText: xhr.responseText
			});
		}).always(function() {
			console.log("儲存操作請求完成");
		});
	}else{
		console.log("表單驗證失敗");
		return $.Deferred().reject("表單驗證失敗");
	}
}
