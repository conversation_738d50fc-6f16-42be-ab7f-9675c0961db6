/* 
 * L180M02BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L181M01B;

/** 覆審控制維護明細檔 **/
public interface L181M01BDao extends IGenericDao<L181M01B> {

	L181M01B findByOid(String oid);
	
	List<L181M01B> findByMainId(String mainId);
	
	L181M01B findByUniqueKey(String mainId, String type);

	List<L181M01B> findByIndex01(String mainId, String type);
}