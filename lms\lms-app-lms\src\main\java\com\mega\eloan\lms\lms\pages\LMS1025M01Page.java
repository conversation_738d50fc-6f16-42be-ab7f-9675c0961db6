
package com.mega.eloan.lms.lms.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.panels.OverSeaCLSOuterPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ScoreServiceAU;
import com.mega.eloan.lms.lms.panels.LMS1025S01Panel;
import com.mega.eloan.lms.lms.panels.LMS1025S02Panel;
import com.mega.eloan.lms.lms.panels.LMS1025S03Panel;
import com.mega.eloan.lms.lms.panels.LMS1025S04Panel;
import com.mega.eloan.lms.lms.panels.LMS1025S05Panel;
import com.mega.eloan.lms.lms.panels.LMS1025S06Panel;
import com.mega.eloan.lms.model.C121M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1025m01/{page}")
public class LMS1025M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Autowired
	CLSService clsService;
	
	@Autowired
	ScoreServiceAU scoreServiceAU;
	
	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待覆核));
		addAclLabel(model,
				new AclLabel("_btn_APPROVED", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_已核准));
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);	
		boolean showTab04 = true;
		boolean showTab05 = true;
		String mowTypeCountry = Util.trim(meta.getMowTypeCountry());
		String varVer = Util.trim(meta.getVarVer());
		if(Util.isEmpty(varVer)){ //無評等的情況，抓最新版
			varVer = scoreServiceAU.get_Version_AU();
		}
		if(Util.equals(mowTypeCountry, OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_法國) 
				|| Util.equals(mowTypeCountry, OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_加拿大)){ 
			//法國、加拿大 >> 不顯示[veda report資訊]、[評等調整]頁籤
			showTab04 = false;
			showTab05 = false;
		}else{
			//澳洲 >> 3.0評等版本不顯示[評等調整]頁籤
			if(Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)){
				showTab05 = false;
			}
		}
		
		boolean title_1_2_0 = true;
		boolean title_3_0 = false;
		
		if(Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)){
			title_1_2_0 = false;
			title_3_0 = true;
		}
		
		model.addAttribute("tab_VedaReport", showTab04);
		model.addAttribute("tab_Adjustment", showTab05);
		model.addAttribute("title_1_2_0", title_1_2_0);
		model.addAttribute("title_3_0", title_3_0);
		
		
		renderJsI18N(LMS1025M01Page.class);
		renderJsI18N(LMS1025V01Page.class);
		
		renderJsI18N(AbstractOverSeaCLSPage.class);
		model.addAttribute("_divOverSeaCLSPanel_visible", true);
		new OverSeaCLSOuterPanel("divOverSeaCLSPanel").processPanelData(model, params);
		
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, params);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}// ;

	// 頁籤
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1025S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS1025S02Panel(TAB_CTX, true);
			break;
		case 3:
			panel = new LMS1025S03Panel(TAB_CTX, true);

			break;
		case 4:
			String mainOid = params.getString(EloanConstants.MAIN_OID);
			C121M01A meta = clsService.findC121M01A_oid(mainOid);	
			String varVer = Util.trim(meta.getVarVer());
			panel = new LMS1025S04Panel(TAB_CTX, true, varVer);
			break;
		case 5:
			panel = new LMS1025S05Panel(TAB_CTX, true);
			break;
		case 6:
			panel = new LMS1025S06Panel(TAB_CTX, true);
			break;		
		default:
			panel = new LMS1025S01Panel(TAB_CTX, true);
			break;
		}

		return panel;
	}

	public Class<? extends Meta> getDomainClass() {
		return C121M01A.class;
	}

}
