/* 
 * L120S06A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 利害關係人授信條件對照表主檔  <br/>
 * XL120S06A01    	+MAINID+CUSTID+DUPNO+CNTRNO+REFMAINID  U => REFMAINID 在 DB 中也是UK之一
 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S06A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo","cntrNo"}))
public class L120S06A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 額度明細文件編號<p/>
	 * 101/02/21 Miller新增<br/>
	 *  L140M01A_mainId
	 */
	@Column(name="REFMAINID", length=32, columnDefinition="CHAR(32)")
	private String refMainId;

	/**
	
	/** 
	 * 本案授信戶統編<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * 本案授信戶重複序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)")
	private String dupNo;

	/** 
	 * 本案授信戶額度序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 對照授信戶統編<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CUSTID2", length=10, columnDefinition="VARCHAR(10)")
	private String custId2;

	/** 
	 * 對照授信戶重複序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="DUPNO2", length=1, columnDefinition="CHAR(01)")
	private String dupNo2;

	/** 
	 * 對照授信戶額度序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CNTRNO2", length=12, columnDefinition="CHAR(12)")
	private String cntrNo2;

	/** 
	 * 本案授信戶區部別<p/>
	 * 101/02/18新增<br/>
	 *  資料來源：額度明細表主檔
	 */
	@Column(name="TYPCD", length=1, columnDefinition="CHAR(1)")
	private String typCd;
	
	/** 本案授信戶 **/
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 
	 * 性質<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="PROPERTY", length=30, columnDefinition="VARCHAR(30)")
	private String property;

	/** 
	 * 現請額度－幣別<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CURRENTAPPLYCURR", length=3, columnDefinition="CHAR(3)")
	private String currentApplyCurr;

	/** 
	 * 現請額度－金額<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CURRENTAPPLYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/** 
	 * 授信科目<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="LNSUBJECT", length=1536, columnDefinition="VARCHAR(1536)")
	private String lnSubject;

	/** 
	 * 資金用途<p/>
	 * 資料來源：簽報書主檔<br/>
	 *  ※(授權內)<br/>
	 *  1.購料週轉金<br/>
	 *  2.營運週轉金<br/>
	 *  3.其他
	 */
	@Column(name="PURPOSE", length=450, columnDefinition="VARCHAR(450)")
	private String purpose;

	/** 
	 * 保證成數<p/>
	 * 中小信保基金保證<br/>
	 *  資料來源：額度明細表主檔
	 */
	@Column(name="GUTPERCENT", columnDefinition="DECIMAL(5,2)")
	private Double gutPercent;

	/** 
	 * 期限<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="PAYDEADLINE", length=600, columnDefinition="VARCHAR(600)")
	private String payDeadline;

	/** 
	 * 連保人<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="GUARANTOR", length=1800, columnDefinition="VARCHAR(1800)")
	private String guarantor;

	/** 
	 * 連保人備註<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="GUARANTORMEMO", length=900, columnDefinition="VARCHAR(900)")
	private String guarantorMemo;

	/** 
	 * 對照授信戶區部別<p/>
	 * 101/02/18新增<br/>
	 *  資料來源：額度明細表主檔
	 */
	@Column(name="TYPCD2", length=1, columnDefinition="CHAR(1)")
	private String typCd2;
	
	/** 對照授信戶 **/
	@Column(name="CUSTNAME2", length=120, columnDefinition="VARCHAR(120)")
	private String custName2;

	/** 
	 * 性質<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="PROPERTY2", length=30, columnDefinition="VARCHAR(30)")
	private String property2;

	/** 
	 * 現請額度－幣別<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CURRENTAPPLYCURR2", length=3, columnDefinition="CHAR(3)")
	private String currentApplyCurr2;

	/** 
	 * 現請額度－金額<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="CURRENTAPPLYAMT2", columnDefinition="DECIMAL(17,2)")
	private BigDecimal currentApplyAmt2;

	/** 
	 * 授信科目<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="LNSUBJECT2", length=1536, columnDefinition="VARCHAR(1536)")
	private String lnSubject2;

	/** 
	 * 資金用途<p/>
	 * 資料來源：簽報書主檔<br/>
	 *  ※(授權內)<br/>
	 *  1.購料週轉金<br/>
	 *  2.營運週轉金<br/>
	 *  3.其他
	 */
	@Column(name="PURPOSE2", length=450, columnDefinition="VARCHAR(450)")
	private String purpose2;

	/** 
	 * 保證成數<p/>
	 * 中小信保基金保證<br/>
	 *  資料來源：額度明細表主檔
	 */
	@Column(name="GUTPERCENT2", columnDefinition="DECIMAL(5,2)")
	private Double gutPercent2;

	/** 
	 * 期限<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="PAYDEADLINE2", length=600, columnDefinition="VARCHAR(600)")
	private String payDeadline2;

	/** 
	 * 連保人<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="GUARANTOR2", length=1800, columnDefinition="VARCHAR(1800)")
	private String guarantor2;

	/** 
	 * 連保人備註<p/>
	 * 資料來源：額度明細表主檔
	 */
	@Column(name="GUARANTORMEMO2", length=900, columnDefinition="VARCHAR(900)")
	private String guarantorMemo2;

	/** 
	 * 列印模式<p/>
	 * 單獨一頁 | 1<br/>
	 *  合併共頁 | 2 <br/>
	 *  在一份簽報書會有N個額度明細表，也代表可能會有多份授信條件對照表。
	 *  <ul>
	 *  <li>只有1筆授信條件對照表，選「單獨一頁」或「合併共頁」的效果是一樣的
	 *  </li>
	 *  <li>當有3筆授信條件對照表，1st選「單獨一頁」而把2nd,3rd選「合併共頁」-> 就會看到差別
	 *  </li>
	 *  </ul>
	 */
	@Column(name="PRINTMODE", length=1, columnDefinition="CHAR(01)")
	private String printMode;

	/** 
	 * 輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name="CHKYN", length=1, columnDefinition="CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得額度明細文件編號<p/>
	 * 101/02/21 Miller新增<br/>
	 *  L140M01A_mainId
	 */
	public String getRefMainId() {
		return this.refMainId;
	}
	/**
	 *  設定額度明細文件編號<p/>
	 *  101/02/21 Miller新增<br/>
	 *  L140M01A_mainId
	 **/
	public void setRefMainId(String value) {
		this.refMainId = value;
	}
	
	/** 
	 * 取得本案授信戶統編<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定本案授信戶統編<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得本案授信戶重複序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定本案授信戶重複序號<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得本案授信戶額度序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}
	/**
	 *  設定本案授信戶額度序號<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得對照授信戶統編<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCustId2() {
		return this.custId2;
	}
	/**
	 *  設定對照授信戶統編<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCustId2(String value) {
		this.custId2 = value;
	}

	/** 
	 * 取得對照授信戶重複序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getDupNo2() {
		return this.dupNo2;
	}
	/**
	 *  設定對照授信戶重複序號<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setDupNo2(String value) {
		this.dupNo2 = value;
	}

	/** 
	 * 取得對照授信戶額度序號<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCntrNo2() {
		return this.cntrNo2;
	}
	/**
	 *  設定對照授信戶額度序號<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCntrNo2(String value) {
		this.cntrNo2 = value;
	}

	/** 
	 * 取得本案授信戶區部別<p/>
	 * 101/02/18新增<br/>
	 *  資料來源：額度明細表主檔
	 */
	public String getTypCd() {
		return this.typCd;
	}
	/**
	 *  設定本案授信戶區部別<p/>
	 *  101/02/18新增<br/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}
	
	/** 取得本案授信戶 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定本案授信戶 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得性質<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getProPerty() {
		return this.property;
	}
	/**
	 *  設定性質<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setProPerty(String value) {
		this.property = value;
	}

	/** 
	 * 取得現請額度－幣別<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}
	/**
	 *  設定現請額度－幣別<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/** 
	 * 取得現請額度－金額<p/>
	 * 資料來源：額度明細表主檔
	 */
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}
	/**
	 *  設定現請額度－金額<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/** 
	 * 取得授信科目<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getLnSubject() {
		return this.lnSubject;
	}
	/**
	 *  設定授信科目<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setLnSubject(String value) {
		this.lnSubject = value;
	}

	/** 
	 * 取得資金用途<p/>
	 * 資料來源：簽報書主檔<br/>
	 *  ※(授權內)<br/>
	 *  1.購料週轉金<br/>
	 *  2.營運週轉金<br/>
	 *  3.其他
	 */
	public String getPurpose() {
		return this.purpose;
	}
	/**
	 *  設定資金用途<p/>
	 *  資料來源：簽報書主檔<br/>
	 *  ※(授權內)<br/>
	 *  1.購料週轉金<br/>
	 *  2.營運週轉金<br/>
	 *  3.其他
	 **/
	public void setPurpose(String value) {
		this.purpose = value;
	}

	/** 
	 * 取得保證成數<p/>
	 * 中小信保基金保證<br/>
	 *  資料來源：額度明細表主檔
	 */
	public Double getGutPercent() {
		return this.gutPercent;
	}
	/**
	 *  設定保證成數<p/>
	 *  中小信保基金保證<br/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setGutPercent(Double value) {
		this.gutPercent = value;
	}

	/** 
	 * 取得期限<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getPayDeadline() {
		return this.payDeadline;
	}
	/**
	 *  設定期限<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setPayDeadline(String value) {
		this.payDeadline = value;
	}

	/** 
	 * 取得連保人<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getGuarantor() {
		return this.guarantor;
	}
	/**
	 *  設定連保人<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setGuarantor(String value) {
		this.guarantor = value;
	}

	/** 
	 * 取得連保人備註<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getGuarantorMemo() {
		return this.guarantorMemo;
	}
	/**
	 *  設定連保人備註<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setGuarantorMemo(String value) {
		this.guarantorMemo = value;
	}

	/** 
	 * 取得對照授信戶區部別<p/>
	 * 101/02/18新增<br/>
	 *  資料來源：額度明細表主檔
	 */
	public String getTypCd2() {
		return this.typCd2;
	}
	/**
	 *  設定對照授信戶區部別<p/>
	 *  101/02/18新增<br/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setTypCd2(String value) {
		this.typCd2 = value;
	}
	
	/** 取得對照授信戶 **/
	public String getCustName2() {
		return this.custName2;
	}
	/** 設定對照授信戶 **/
	public void setCustName2(String value) {
		this.custName2 = value;
	}

	/** 
	 * 取得性質<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getProPerty2() {
		return this.property2;
	}
	/**
	 *  設定性質<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setProPerty2(String value) {
		this.property2 = value;
	}

	/** 
	 * 取得現請額度－幣別<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getCurrentApplyCurr2() {
		return this.currentApplyCurr2;
	}
	/**
	 *  設定現請額度－幣別<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCurrentApplyCurr2(String value) {
		this.currentApplyCurr2 = value;
	}

	/** 
	 * 取得現請額度－金額<p/>
	 * 資料來源：額度明細表主檔
	 */
	public BigDecimal getCurrentApplyAmt2() {
		return this.currentApplyAmt2;
	}
	/**
	 *  設定現請額度－金額<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setCurrentApplyAmt2(BigDecimal value) {
		this.currentApplyAmt2 = value;
	}

	/** 
	 * 取得授信科目<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getLnSubject2() {
		return this.lnSubject2;
	}
	/**
	 *  設定授信科目<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setLnSubject2(String value) {
		this.lnSubject2 = value;
	}

	/** 
	 * 取得資金用途<p/>
	 * 資料來源：簽報書主檔<br/>
	 *  ※(授權內)<br/>
	 *  1.購料週轉金<br/>
	 *  2.營運週轉金<br/>
	 *  3.其他
	 */
	public String getPurpose2() {
		return this.purpose2;
	}
	/**
	 *  設定資金用途<p/>
	 *  資料來源：簽報書主檔<br/>
	 *  ※(授權內)<br/>
	 *  1.購料週轉金<br/>
	 *  2.營運週轉金<br/>
	 *  3.其他
	 **/
	public void setPurpose2(String value) {
		this.purpose2 = value;
	}

	/** 
	 * 取得保證成數<p/>
	 * 中小信保基金保證<br/>
	 *  資料來源：額度明細表主檔
	 */
	public Double getGutPercent2() {
		return this.gutPercent2;
	}
	/**
	 *  設定保證成數<p/>
	 *  中小信保基金保證<br/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setGutPercent2(Double value) {
		this.gutPercent2 = value;
	}

	/** 
	 * 取得期限<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getPayDeadline2() {
		return this.payDeadline2;
	}
	/**
	 *  設定期限<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setPayDeadline2(String value) {
		this.payDeadline2 = value;
	}

	/** 
	 * 取得連保人<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getGuarantor2() {
		return this.guarantor2;
	}
	/**
	 *  設定連保人<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setGuarantor2(String value) {
		this.guarantor2 = value;
	}

	/** 
	 * 取得連保人備註<p/>
	 * 資料來源：額度明細表主檔
	 */
	public String getGuarantorMemo2() {
		return this.guarantorMemo2;
	}
	/**
	 *  設定連保人備註<p/>
	 *  資料來源：額度明細表主檔
	 **/
	public void setGuarantorMemo2(String value) {
		this.guarantorMemo2 = value;
	}

	/** 
	 * 取得列印模式<p/>
	 * 單獨一頁 | 1<br/>
	 *  合併共頁 | 2
	 */
	public String getPrintMode() {
		return this.printMode;
	}
	/**
	 *  設定列印模式<p/>
	 *  單獨一頁 | 1<br/>
	 *  合併共頁 | 2
	 **/
	public void setPrintMode(String value) {
		this.printMode = value;
	}

	/** 
	 * 取得輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}
	/**
	 *  設定輸入資料檢誤完成(Y/N)<p/>
	 *  100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
