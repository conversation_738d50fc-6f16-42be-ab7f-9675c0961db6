package com.mega.eloan.lms.cls.pages;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.panels.CLS101S01S1Panel;
import com.mega.eloan.lms.cls.panels.CLS101S01S2Panel;
import com.mega.eloan.lms.cls.panels.CLS101S01S3Panel;
import com.mega.eloan.lms.cls.panels.CLS101S01S4Panel;
import com.mega.eloan.lms.cls.panels.CLS101S01S5Panel;
import com.mega.eloan.lms.cls.panels.CLS101S01S6Panel;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01H;
import com.mega.eloan.lms.model.C120S01I;
import com.mega.eloan.lms.model.C120S01S;
import com.mega.eloan.lms.model.C120S01U;
import com.mega.eloan.lms.model.C120S04W;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 一件列印HTML
 * </pre>
 * 
 * @since 2020/05/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/05/22,EL09763
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131p03")
public class CLS1131P03Page extends AbstractOutputPage {

	@Autowired
	CLS1131Service cms1131Service;

	@Autowired
	CLSService clsService;

	@Autowired
	CodeTypeService codeTypeService;
	
	@Autowired
	RPAProcessService rpaProcessService;
	
	@Autowired
	EjcicService ejcicService;

	private static final long serialVersionUID = 1L;

	private static final String PAGE_BREAK_DIV = "<div class=\"pageBreak\">&nbsp;</div>";

	// UPGRADETODO: 不確定Header是否還需要
	// @Override
	// protected void setHeaders(WebResponse response) {
	// if (true) {
	// response.setHeader("X-UA-Compatible", "IE=11");
	// }
	// }

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		setNeedHtml(true); // need html

		setJavascript(new String[] { "pagejs/cls/CLS1131P03Page.js" });

		// 設定隱藏欄位
		Map<String, Object> mapData = null;
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String[] hideInput = { EloanConstants.MAIN_ID, "custId", "dupNo",
				"queryType", "dataType", "isC120M01A" };
		for (String id : hideInput) {
			String value = CapString.trimNull((params.getString(id)));
			mapData = new HashMap<String, Object>();
			mapData.put("id", id);
			mapData.put("name", id);
			mapData.put("type", "text");
			mapData.put("value", value);
			list.add(mapData);
		}
		model.addAttribute("list", list);

		// 沒有傳入dataType:印全部
		String dataType = (params.getString("dataType"));
		if (dataType == null) {
			StringBuilder sb = new StringBuilder();
			sb.append(this.getP01HTMLOutputString(params));
			sb.append(this.getP02HTMLOutputString(params));
			String result = sb.append("&nbsp").toString()
					.replaceFirst(PAGE_BREAK_DIV, "");
			// 若產出頁面有多餘空白字元長度資料則一鍵列印原PDF檔加入換頁
			if (result.length() > "&nbsp".length()) {
				params.put("addPageBreak", true);
			} else {
				params.put("addPageBreak", false);
			}
			printAll(model, params);
			return result;
		} else {
			// 單一HTML不加換頁
			params.put("addPageBreak", false);
			printOne(model, params);
		}

		return "&nbsp;";
	}



	/**
	 * RPS原PDF檔資料轉HTML呈現
	 * 
	 * @return
	 */
	private Set<String> getPanelIdSet() {
		Set<String> panelIdSet = new HashSet<String>();
		panelIdSet.add("CLS101S01S1");// 往來客戶信用異常資料
		panelIdSet.add("CLS101S01S2");// 客戶是否為利害關係人資料
		panelIdSet.add("CLS101S01S3");// 婉卻紀錄資料
		panelIdSet.add("CLS101S01S4");// 證券違約交割資料
		panelIdSet.add("CLS101S01S5");// 內政部國民身分證領換補資料
		panelIdSet.add("CLS101S01S6");
		return panelIdSet;
	}

	/**
	 * 一鍵列印原PDF檔
	 */
	private void printAll(ModelMap model, PageParameters params) {
		params.put("printAll", true);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		boolean isC120M01A = Util.equals("Y",
				Util.trim(params.getString("isC120M01A")));
		Set<String> panelIdSet = getPanelIdSet();
		try {
			if (isC120M01A) {
				List<C120S01S> s01s_list = new ArrayList<C120S01S>();
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, "1"));
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, "2"));
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, "3"));
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, "4"));
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, ClsConstants.C101S01S_dataType.行內_身分證驗證));
				
				for (C120S01S c101s01s : s01s_list) {
					String dataType = c101s01s.getDataType();
					if ("J".equals(c101s01s.getReportFileType())) {
						addPanel(dataType, params, panelIdSet);
					}
				}
				
				C120S04W c120s04w = this.rpaProcessService.getC120S04WBy(mainId, custId);
				if(null != c120s04w && "A02".equals(c120s04w.getStatus())){
					addPanel(ClsConstants.C101S01S_dataType.RPA受監護輔助宣告查詢, params, panelIdSet);
				}
				
			} else {
				List<C101S01S> s01s_list = new ArrayList<C101S01S>();
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, "1"));
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, "2"));
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, "3"));
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, "4"));
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, ClsConstants.C101S01S_dataType.行內_身分證驗證));
				
				for (C101S01S c101s01s : s01s_list) {
					String dataType = c101s01s.getDataType();
					if ("J".equals(c101s01s.getReportFileType())) {
						addPanel(dataType, params, panelIdSet);
					}
				}
				
				C101S04W c101s04w = this.rpaProcessService.getC101S04WBy(mainId, custId);
				if(null != c101s04w && "A02".equals(c101s04w.getStatus())){
					addPanel(ClsConstants.C101S01S_dataType.RPA受監護輔助宣告查詢, params, panelIdSet);
				}
			}
			// 未產生panel的地方放空Panel
			for (String id : panelIdSet) {
				model.addAttribute(id, false);
			}
		} catch (SecurityException e) {
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 單一列印原PDF檔
	 * 
	 * @throws ClassNotFoundException
	 * @throws NoSuchMethodException
	 * @throws SecurityException
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 * @throws IllegalArgumentException
	 */
	private void printOne(ModelMap model, PageParameters params) {
		String dataType = params.getString("dataType");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		boolean isC120M01A = Util.equals("Y",
				Util.trim(params.getString("isC120M01A")));
		Set<String> panelIdSet = getPanelIdSet();
		List<? extends GenericBean> list = new ArrayList<GenericBean>();
		try {
			if (isC120M01A) {
				list = clsService.findC120S01S_byIdDupDataType(mainId, custId,
						dupNo, dataType);
			} else {
				list = clsService.findC101S01S_byIdDupDataType(mainId, custId,
						dupNo, dataType);
			}
			if (list != null && list.size() > 0) {
				String reportFileType = isC120M01A ? ((C120S01S) list.get(0))
						.getReportFileType() : ((C101S01S) list.get(0))
						.getReportFileType();
				if ("J".equals(reportFileType)) {
					addPanel(dataType, params, panelIdSet);
				}
			}
			for (String id : panelIdSet) {
				model.addAttribute(id, false);
			}
		} catch (SecurityException e) {
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 依panelIdSet內的id設定panel，設定完成後移除Set內id
	 * 
	 * @param dataType
	 * @param params
	 * @param panelIdSet
	 */
	private Panel addPanel(String dataType, PageParameters params,
			Set<String> panelIdSet) {
		boolean printAll = params.getAsBoolean("printAll", false);
		// 印全部時，判斷非第一個產生的頁面時，加入換頁
		if (printAll == true && panelIdSet.size() < 4) {
			params.put("addPageBreak", true);
		}
		String id = "CLS101S01S" + dataType;
		Panel panel = null;
		if ("1".equals(dataType) && panelIdSet.contains(id)) {
			panel = new CLS101S01S1Panel(id);
		} else if ("2".equals(dataType) && panelIdSet.contains(id)) {
			panel = new CLS101S01S2Panel(id);
		} else if ("3".equals(dataType) && panelIdSet.contains(id)) {
			panel = new CLS101S01S3Panel(id);
		} else if ("4".equals(dataType) && panelIdSet.contains(id)) {
			panel = new CLS101S01S4Panel(id);
		} else if (ClsConstants.C101S01S_dataType.行內_身分證驗證.equals(dataType) && panelIdSet.contains(id)) {
			panel = new CLS101S01S5Panel(id);
		} else if (ClsConstants.C101S01S_dataType.RPA受監護輔助宣告查詢.equals(dataType) && panelIdSet.contains(id)) {
			panel = new CLS101S01S6Panel(id);
		} else {
			panel = new Panel(id);
		}
		// 加入後set移除
		panelIdSet.remove(id);
		return panel;
	}

	/**
	 * 取得一鍵查詢回傳為HTML檔並合併
	 * 
	 * @param params
	 * @return
	 */
	private String getP01HTMLOutputString(PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String queryType = Util.trim(
				params.getString(UtilConstants.External.查詢類別)).toLowerCase();
		StringBuilder sb = new StringBuilder();
		boolean isC120M01A = params.getBoolean("isC120M01A");
		Class<? extends GenericBean> clazz = null;
		String tag = "";
		// 票信
		tag = "htmlResult";
		clazz = isC120M01A ? C120S01I.class : C101S01I.class;

		List<? extends GenericBean> etchList = cms1131Service
				.findListByRelationKey(clazz, mainId, custId, dupNo);
		for (GenericBean bean : etchList) {
			try {
				String appendStr = (String) bean.get(tag);
				if (!CapString.isEmpty(appendStr)) {
					sb.append(PAGE_BREAK_DIV);
					sb.append(appendStr);
				}
			} catch (CapException e) {
			}
		}
		// 聯徵
		tag = "htmlData";
		
		List<GenericBean> list = new ArrayList<GenericBean>();
		clazz = isC120M01A ? C120S01H.class : C101S01H.class;
		List<? extends GenericBean> listS01H = cms1131Service.findListByRelationKey(clazz, mainId, custId, dupNo);
		list.addAll(listS01H);
		
		clazz = isC120M01A ? C120S01U.class : C101S01U.class;
		List<? extends GenericBean> listS01U = cms1131Service.findListByRelationKey(clazz, mainId, custId, dupNo);
		list.addAll(listS01U);
		
		boolean s01h_contains_Z13 = false;
		try {
			int size = list.size();
			LinkedHashMap<String, List<Integer>> ord_map = new LinkedHashMap<String, List<Integer>>();
			List<Integer> other_list = new ArrayList<Integer>(); // 放其它的TXID
			// 按順序 H116:逾期催收及呆帳資料, H135:授信資料, H128:信用卡資料, HB29, HB68, HJ10,
			// HZ13, HZ21
			String[] txid_arr = new String[] { "H116", "H135", "H128", "HB29","HB68", "HJ10", CrsUtil.EJ_TXID_HZ13, CrsUtil.EJ_TXID_HZ21,CrsUtil.EJ_TXID_HD10, CrsUtil.EJ_TXID_HR20 };
			for (String txId : txid_arr) {
				ord_map.put(txId, new ArrayList<Integer>());
			}
			for (int i = 0; i < size; i++) {
				GenericBean bean = list.get(i);
				String txId = "";
				try {
					txId = (String) bean.get("txid");
				} catch (CapException e) {
				}
				if (ord_map.containsKey(txId)) {
					ord_map.get(txId).add(i);
				} else {
					other_list.add(i);
				}
				if (Util.equals(txId, CrsUtil.EJ_TXID_HZ13)) {
					s01h_contains_Z13 = true;
				}
			}
			// ~~~~~~~~~~~~~~~~~~~~~~
			List<Integer> new_seq_list = new ArrayList<Integer>();
			for (String txId : ord_map.keySet()) { // 依指定的 txId 順序呈現
				List<Integer> match_list = ord_map.get(txId);
				if (match_list.size() > 0) {
					new_seq_list.addAll(match_list);
				}
			}
			new_seq_list.addAll(other_list);
			// ~~~~~~~~~~~~~~~~~~~~~~
			if (new_seq_list.size() == size) { // 先 check 重排序後的筆數相否相同
				for (Integer idx : new_seq_list) {
					GenericBean bean = list.get(idx);

					String txId = "";
					try {
						txId = (String) bean.get("txid");
					} catch (CapException e) {
					}
					if (s01h_contains_Z13
							&& Util.equals(txId, CrsUtil.EJ_TXID_HZ13)) {
						// P9查詢結果已包含Z13，但另外在 標準查詢 的UI去呈現Z13
						continue;
					}

					try {
						String appendStr = (String) bean.get(tag);
						if (!CapString.isEmpty(appendStr)) {
							sb.append(PAGE_BREAK_DIV);
							sb.append(appendStr);
						}
					} catch (CapException e) {
					}
				}
			} else {
				throw new CapException("diff size", getClass());
			}
		} catch (Exception excep) {
			// 之前版本：無特定順序
			for (GenericBean bean : list) {
				try {
					String appendStr = (String) bean.get(tag);
					if (!CapString.isEmpty(appendStr)) {
						sb.append(PAGE_BREAK_DIV);
						sb.append(appendStr);
					}
				} catch (CapException e) {
				}
			}
		}

		if (sb.length() > 0) {
			setNeedHtml(true);
			// 執行浮水印
			// O-112-0233 調整浮水印，UnitNo若在EJF369有對應的VDEPTID就改用VDEPTID
			String wm_msg = UtilConstants.兆豐銀行代碼 
					+ ejcicService.findEJF369VDEPTID(user.getUnitNo()) + " "
					+ user.getUserId() + " " + user.getLoginIP();
			sb.append("<script>window.onload = function(){");
			sb.append("watermark('").append(wm_msg).append("');");
			sb.append("};</script>");
		}

		return sb.toString();
	}

	private String getP02HTMLOutputString(PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String txId = Util.trim(params.getString("txId"));
		
		String[] txid_arr = new String[]{CrsUtil.EJ_TXID_B36, CrsUtil.EJ_TXID_D10, CrsUtil.EJ_TXID_R20, CrsUtil.EJ_TXID_S11};
		StringBuilder sb = new StringBuilder();

		boolean isC120M01A = params.getBoolean("isC120M01A");		
		if (isC120M01A) {
			C120S01E model = clsService.findC120S01E(mainId, custId, dupNo);
			if (model != null) {
				String extract_html = "";
				extract_html = extract_data(model.getZ13_html());
				if (!CapString.isEmpty(extract_html)) {
					sb.append(PAGE_BREAK_DIV);
					sb.append(extract_html);
				}
				extract_html = extract_data(model.getZ21_html());
				if (!CapString.isEmpty(extract_html)) {
					sb.append(PAGE_BREAK_DIV);
					sb.append(extract_html);
				}
				for(String item_in_txid : txid_arr){
					for (C120S01U s01u : clsService.findC120S01U_txid(
							model.getMainId(), model.getCustId(), model.getDupNo(),
							item_in_txid)) {
						extract_html = extract_data(s01u.getHtmlData());
						if (!CapString.isEmpty(extract_html)) {
							sb.append(PAGE_BREAK_DIV);
							sb.append(extract_html);
						}
					}	
				}
			}
		} else {
			C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
			C101S01E model = clsService.findC101S01E(c101m01a);
			if (model != null) {
				String extract_html = "";
				extract_html = extract_data(model.getZ13_html());
				if (!CapString.isEmpty(extract_html)) {
					sb.append(PAGE_BREAK_DIV);
					sb.append(extract_html);
				}
				extract_html = extract_data(model.getZ21_html());
				if (!CapString.isEmpty(extract_html)) {
					sb.append(PAGE_BREAK_DIV);
					sb.append(extract_html);
				}
				for(String item_in_txid : txid_arr){
					for (C101S01U s01u : clsService.findC101S01U_txid(
							model.getMainId(), model.getCustId(), model.getDupNo(),
							item_in_txid)) {
						extract_html = extract_data(s01u.getHtmlData());
						if (!CapString.isEmpty(extract_html)) {
							sb.append(PAGE_BREAK_DIV);
							sb.append(extract_html);
						}
					}	
				}
			}
		}

		if (sb.length() > 0) {
			setNeedHtml(true);
			// 執行浮水印
			// O-112-0233 調整浮水印，UnitNo若在EJF369有對應的VDEPTID就改用VDEPTID
			String wm_msg = UtilConstants.兆豐銀行代碼 
					+ ejcicService.findEJF369VDEPTID(user.getUnitNo()) + " "
					+ user.getUserId() + " " + user.getLoginIP();
			sb.append("<script>window.onload = function(){");
			sb.append("watermark('").append(wm_msg).append("');");
			sb.append("};</script>");
		}

		return sb.toString();
	}

	/**
	 * 目前去 parse Z13及Z21 的 HtmlResponse
	 */
	private String extract_data(String raw) {
		if (raw == null) {
			return "";
		}
		String lower_str = raw.toLowerCase();
		int idx_charset = lower_str.indexOf("charset");
		if (idx_charset > 1) {
			// 在正常回覆的 html，先以 charset 去拆分字串
			int beg_idx = lower_str.substring(0, idx_charset).lastIndexOf(
					"<html>");
			String end_tag = "</html>";
			int end_idx = lower_str.indexOf(end_tag, idx_charset);
			if (beg_idx > 1 && end_idx > 1 && beg_idx < end_idx) {
				return StringUtils.substring(raw, beg_idx,
						end_idx + "</html>".length());
			}
		}
		return raw;
	}

	// UPGRADE: 待確認是否需要ViewName
	@Override
	protected String getViewName() {
		return null;
	}

}
