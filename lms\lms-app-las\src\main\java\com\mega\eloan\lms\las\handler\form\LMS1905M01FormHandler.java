package com.mega.eloan.lms.las.handler.form;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.ContractDocUtil;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DWAslndavgovsService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.las.pages.LMS1935V01Page;
import com.mega.eloan.lms.las.report.LMS1905R01RptService;
import com.mega.eloan.lms.las.report.LMS1905R02RptService;
import com.mega.eloan.lms.las.service.LMS1905Service;
import com.mega.eloan.lms.las.service.LMS1935Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELCUS21Service;
import com.mega.eloan.lms.mfaloan.service.MisELCUS27Service;
import com.mega.eloan.lms.mfaloan.service.MisELF346Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElacnmService;
import com.mega.eloan.lms.mfaloan.service.MisIcbcBrService;
import com.mega.eloan.lms.mfaloan.service.MisLNF150Service;
import com.mega.eloan.lms.mfaloan.service.MisQuotainfService;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S12A;
import com.mega.eloan.lms.model.L120S13A;
import com.mega.eloan.lms.model.L120S15A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01D;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.eloan.lms.model.L192S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.MemberService;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 查詢房貸業務工作底稿
 * 
 * <AUTHOR>
 * 
 */
@Scope("request")
@Controller("lms1905m01formhandler")
@DomainClass(L192M01A.class)
public class LMS1905M01FormHandler extends AbstractFormHandler {
	private static final int MAXLEN_L192M01A_STATEMENTADDRTO = StrUtils.getEntityFileldLegth(L192M01A.class, "statementAddrTo", 180);
	private static final int MAXLEN_L192M01A_STATEMENTADDRFROM = StrUtils.getEntityFileldLegth(L192M01A.class, "statementAddrFrom", 180);
	private static final int MAXLEN_L192M01A_TADDR = StrUtils.getEntityFileldLegth(L192M01A.class, "tAddr", 180);
	
	@Resource
	CLSService clsService;

	@Resource
	LMSService lmsService;

	@Resource
	LMS1905Service lms1905Service;

	@Resource
	LMS1935Service lms1935Service;
	
	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userService;

	@Resource
	MisLNF150Service misLNF150Service;

	@Resource
	MisELF346Service misELF346Service;

	@Resource
	MisRatetblService misRatetblService;

	@Resource
	MisElacnmService misElacnmService;

	@Resource
	DWAslndavgovsService dwAslndavgovsService;

	@Resource
	MisELCUS21Service misELCUS21Service;

	@Resource
	MisELCUS27Service misELCUS27Service;

	@Resource
	MisELLNGTEEService misELLNGTEEService;

	@Resource
	MisQuotainfService misQuotainfService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisIcbcBrService misIcbcBrService;

	@Resource
	LMS1905R01RptService lms1905r01RptService;

	@Resource
	LMS1905R02RptService lms1905r02RptService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	DocCheckService docCheckService;

	@Autowired
	MemberService memberService;

	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBaseService;
	
	// MisCollXXXX misCollXXXX;
	//
	// MisELF369Service misELF369Service;
	//
	// MisELF347Service misELF347Service;

	public IResult checkPrint(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString(EloanConstants.MAIN_OID);

		L192M01A meta = lms1905Service.getL192M01A(oid);
		Set<L192S01A> l192s01as = meta.getL192s01as();

		Set<L192S01A> returnData = new HashSet<L192S01A>();
		BigDecimal zero = BigDecimal.ZERO;
		StringBuffer returnMessage = new StringBuffer();
		int printSize = 0;
		StringBuffer totalMessage = new StringBuffer();
		totalMessage.append("申請內容共"+l192s01as.size()+"筆，");
		if (!l192s01as.isEmpty()){
			for (L192S01A l192s01a : l192s01as) {
				//確認LNF020寄送函證FLAG若為9:不寄送函證，則不納入列印，且將此筆資料送至前端提示不列印。
				boolean notice = true;
				if ("13".contains(branchService.getBranch(meta.getOwnBrId()).getBrNoFlag())){
					List<Map<String, Object>> noticetypes;
					try {
						noticetypes = misdbBaseService.findLNF020_NOTICE_TYPE(l192s01a.getQuotaNo());
					} catch (Exception e){
						throw new CapMessageException("與中心主機無法連線，請稍後再試。", this.getClass());
					}
					if (noticetypes != null){
						for (Map<String, Object> map : noticetypes){
							if ("9".equals(map.get("LNF020_NOTICE_TYPE"))){
								notice = false;
							}
						}
					}
				}
				if (notice){
					returnData.add(l192s01a);
					printSize++;
				} else {
					returnMessage.append("放款科目:"+l192s01a.getSubject()+" 帳號"+l192s01a.getAccNo()+" 額度序號"+l192s01a.getQuotaNo()+"已設定不寄送函證，不納入對帳單列印資料。<br>");
				}
			}
		}
		totalMessage.append("納入列印筆數共"+printSize+"筆。<br>");
		//2.有資料，前端會留存mainid供列印使用。無資料，會於前端顯示不列印訊息
		String mark;
		if (printSize == 0) {
			mark = "N";
			lms1905Service.printMark(meta, mark);
		} else if (printSize == l192s01as.size()){
			mark = "Y";
			lms1905Service.printMark(meta, mark);
		} else {
			mark = "P";
			lms1905Service.printMark(meta, mark);
		}
		result.set("L192S01A_PRINT_MARK", mark);
		result.set("NO_PRINT_DETAIL",totalMessage.append(returnMessage.toString()).toString());
		return result;
	}

	/**
	 * 從view的按鈕整批 稽核_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendGAll(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1905Service.getL192M01A(mainOid);
		lms1905Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 從view的按鈕整批 分行_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendAll(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1905Service.getL192M01A(mainOid);
		lms1905Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 從view的按鈕整批傳送稽核室
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendAllNextG(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1905Service.getL192M01A(mainOid);
		lms1905Service.saveAndSendDocument(l192m01a);
		return result;
	}

	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult acceptG(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1905Service.getL192M01A(mainOid);
		}

		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}

		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1905Service.flowControl(meta.getOid(), "稽核_核准");
		return result;
	}

	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult accept(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1905Service.getL192M01A(mainOid);
		}

		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}

		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}

		lms1905Service.flowControl(meta.getOid(), "分行_核准");
		return result;
	}

	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult returnG(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1905Service.getL192M01A(mainOid);
		}

		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}

		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1905Service.flowControl(meta.getOid(), "稽核_退回");
		return result;
	}

	@DomainAuth(value = AuthType.Accept, CheckDocStatus = false)
	public IResult returnB(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1905Service.getL192M01A(mainOid);
		}

		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}

		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1905Service.flowControl(meta.getOid(), "分行_退回");
		return result;
	}

	/**
	 * 傳送稽核室
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendNextG(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1905Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 稽核_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendG(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1905Service.saveAndSendDocument(l192m01a);

		return result;
	}

	/**
	 * 分行_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult send(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1905Service.saveAndSendDocument(l192m01a);

		return result;
	}

	/**
	 * 取得申請內容資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getL192S01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("l192s01aoid");
		L192S01A l192s01a = lms1905Service.getL192S01A(oid);

		Map<String, IFormatter> fmt = new HashMap<String, IFormatter>();

		@SuppressWarnings("serial")
		IFormatter decimalFormat = new IFormatter() {
			DecimalFormat df = new DecimalFormat("#,###,###,###,###");

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				if (in != null) {
					return df.format(in);
				}
				return "";
			}
		};
		fmt.put("quotaAmt", decimalFormat);
		fmt.put("balAmt", decimalFormat);

		result.add(new CapAjaxFormResult(l192s01a.toJSONObject(new String[] {
				"balDate", "subject", "accNo", "quotaNo", "quotaCurr",
				"quotaAmt", "balCurr", "balAmt", "appDate", "signDate",
				"useDate", "fromDate", "endDate", "way", "appr", "checkDate",
				"checkCurr", "checkAmt", "endorser" }, fmt)));
		return result;
	}

	/**
	 * 取得擔保品資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getL192S02A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("l192s02oid");
		L192S02A l192s02a = lms1905Service.getL192S02A(oid);

		// logger.debug(l192s02a.toString());

		// result.add(new CapAjaxFormResult(l192s02a.toString()));
		result.add(new CapAjaxFormResult(l192s02a.toJSONObject(new String[] {
				"oid", "gteName", "estCurr", "estAmt", "loanCurr", "loanAmt",
				"setCurr", "setAmt", "estDate", "owner", "setDate",
				"setDateFrom", "setDateEnd", "setPosition", "insurance",
				"insDateFrom", "insDateEnd", "insPaper" }, null)));
		// 避免子文件的oid 蓋掉畫面上主件的oid
		String l192s02oid = (String) result.get("oid");
		result.removeField("oid");
		result.set("l192s02oid", l192s02oid);
		// logger.debug(result.get("oid").toString());
		logger.debug(result.get("l192s02oid").toString());

		return result;
	}

	/**
	 * 刪除稽核工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL192M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString("deleteMainOid");
		String mainId = params.getString("deleteMainId");

		Map<String, String> lockedUser = docCheckService
				.listLockedDocUser(mainId);
		if (lockedUser == null) {
			lms1905Service.deleteL192M01A(oid);
		} else {
			String message = getPopMessage("EFD0055", lockedUser);
			result.set("deleteMessage", message);
		}

		// Map<String, String> lockedUser =
		// docCheckService.listLockedDocUser(mainId);
		// if (lockedUser == null) {
		// lms1905Service.deleteL192M01A(oid);
		// } else {
		// throw new CapMessageException(getPopMessage("EFD0055", lockedUser),
		// getClass());
		// }

		return result;
	}

	/**
	 * 刪除擔保品資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL192S02A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("deleteMainOid");
		String mainId = params.getString("deleteMainId");

		L192S02A l192s02a = new L192S02A();
		l192s02a.setOid(oid);
		l192s02a.setMainId(mainId);
		lms1905Service.deleteL192S02A(l192s02a);

		// throw new CapException();

		return result;
	}

	/**
	 * 更新擔保品資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult updateL192S02A(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String l192s02oid = params.getString("l192s02oid");

		L192S02A l192s02a = lms1905Service.getL192S02A(l192s02oid);

		parseL192S02A(params, l192s02a);

		lms1905Service.saveL192S02A(l192s02a);

		return result;
	}

	/**
	 * 新增擔保品資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL192S02A(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);
		String oid = params.getString(EloanConstants.MAIN_OID);

		L192S02A l192s02a = new L192S02A();
		l192s02a.setMainId(mainId);

		parseL192S02A(params, l192s02a);

		l192s02a.setCreator(user.getUserId());
		l192s02a.setCreateTime(new Date());

		lms1905Service.saveL192S02A(l192s02a);
		result.set(EloanConstants.MAIN_OID, oid);
		result.set(EloanConstants.MAIN_ID, mainId);

		return result;
	}

	/**
	 * 擔保品資料處理
	 * 
	 * @param params
	 *            PageParameters
	 * @param l192s02a
	 *            擔保品資料
	 */
	private void parseL192S02A(PageParameters params, L192S02A l192s02a) {

		String gteName = params.getString("gteName", "").trim();
		l192s02a.setGteName(gteName);

		String estCurr = params.getString("estCurr", "").trim();
		l192s02a.setEstCurr(estCurr);

		String estAmt = params.getString("estAmt", "").trim().replace(",", "");
		l192s02a.setEstAmt("".equals(estAmt) ? null : new BigDecimal(estAmt));

		String loanCurr = params.getString("loanCurr", "").trim();
		l192s02a.setLoanCurr(loanCurr);

		String loanAmt = params.getString("loanAmt", "").trim()
				.replace(",", "");
		l192s02a.setLoanAmt("".equals(loanAmt) ? null : new BigDecimal(loanAmt));

		String setCurr = params.getString("setCurr", "").trim();
		l192s02a.setSetCurr(setCurr);

		String setAmt = params.getString("setAmt", "").trim().replace(",", "");
		l192s02a.setSetAmt("".equals(setAmt) ? null : new BigDecimal(setAmt));

		String estDate = params.getString("estDate").trim();
		l192s02a.setEstDate("".equals(estDate) ? null : CapDate.getDate(
				estDate, "yyyy-MM-dd"));

		String owner = params.getString("owner", "").trim();
		l192s02a.setOwner(owner);

		String setDate = params.getString("setDate", "").trim();
		l192s02a.setSetDate("".equals(setDate) ? null : CapDate.getDate(
				setDate, "yyyy-MM-dd"));

		String setDateFrom = params.getString("setDateFrom", "").trim();
		l192s02a.setSetDateFrom("".equals(setDateFrom) ? null : CapDate
				.getDate(setDateFrom, "yyyy-MM-dd"));

		String setDateEnd = params.getString("setDateEnd", "").trim();
		l192s02a.setSetDateEnd("".equals(setDateEnd) ? null : CapDate.getDate(
				setDateEnd, "yyyy-MM-dd"));

		String setPosition = params.getString("setPosition", "").trim();
		l192s02a.setSetPosition("".equals(setPosition) ? null : new Integer(
				setPosition));

		String insurance = params.getString("insurance", "").trim();
		l192s02a.setInsurance(insurance);

		String insDateFrom = params.getString("insDateFrom", "").trim();
		l192s02a.setInsDateFrom("".equals(insDateFrom) ? null : CapDate
				.getDate(insDateFrom, "yyyy-MM-dd"));

		String insDateEnd = params.getString("insDateEnd", "").trim();
		l192s02a.setInsDateEnd("".equals(insDateEnd) ? null : CapDate.getDate(
				insDateEnd, "yyyy-MM-dd"));

		String insPaper = params.getString("insPaper", "").trim();
		l192s02a.setInsPaper(insPaper);
	}

	/**
	 * 新增稽核工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addNew(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONArray custIDs = JSONArray.fromObject(params.getString("custIDs"));
		String innerAudit = params.getString("innerAudit");

		// 回傳json array資料
		JSONArray returnJsonArray = new JSONArray();

		for (Object data : custIDs) {

			JSONObject d = (JSONObject) data;

			String custId_dupNo = d.getString("custid").toUpperCase();
			String custId = custId_dupNo
					.substring(0, custId_dupNo.length() - 1);
			String dupNo = custId_dupNo.substring(custId_dupNo.length() - 1,
					custId_dupNo.length()).toUpperCase();
			String custName = d.getString("name");

			L192M01A l192m01a = new L192M01A();
			String uid_mainId = IDGenerator.getUUID();
			l192m01a.setRandomCode(IDGenerator.getRandomCode());
			l192m01a.setUid(uid_mainId);
			l192m01a.setMainId(uid_mainId);
			l192m01a.setCustId(custId);
			l192m01a.setDupNo(dupNo);
			l192m01a.setCustName(custName);
			l192m01a.setUnitType("1");

			// 1.查核授信業務稽核工作底稿
			// 2.查核房屋貸款稽核工作底稿
			l192m01a.setShtType(UtilConstants.ShtType.房貸業務工作底稿);

			l192m01a.setInnerAudit(innerAudit);

			List<Map<String, Object>> custPhones = misELCUS27Service
					.getCustPhone(custId, dupNo);

			for (Map<String, Object> custPhone : custPhones) {
				String areano = custPhone.get("AREANO") == null ? ""
						: (String) custPhone.get("AREANO");
				String telno = custPhone.get("TELNO") == null ? ""
						: (String) custPhone.get("TELNO");
				String tTel = areano.trim() + telno.trim();
				l192m01a.setTTel(tTel);

				// 取第一筆即可
				break;
			}

			// 取得登入分行資料
			l192m01a.setOwnBrId("Y".equals(innerAudit) ? user.getUnitNo()
					: params.getString("brId"));

			l192m01a.setCreator(user.getUserId());
			l192m01a.setCreateTime(CapDate.getCurrentTimestamp());

			if ("Y".equals(innerAudit)) {
				// 如果是內部查核的話，取得登入分行的
				Map<String, Object> bankInfo = misIcbcBrService
						.getBankInfo(user.getUnitNo());
				String statementAddrFrom = (String) (bankInfo == null ? ""
						: bankInfo.get("ADDR"));
				l192m01a.setStatementAddrFrom(statementAddrFrom);
			} else {
				// 稽核查核的話，取得本身自己的單位
				// Map<String, Object> bankInfo = misIcbcBrService
				// .getBankInfo(user.getSsoUnitNo());
				Map<String, Object> bankInfo = misIcbcBrService
						.getBankInfo("906");
				String statementAddrFrom = (String) (bankInfo == null ? ""
						: bankInfo.get("ADDR"));
				l192m01a.setStatementAddrFrom(statementAddrFrom);
			}

			String statementAddrTo = this.getCustAddr(custId, dupNo);
			statementAddrTo = CapString.halfWidthToFullWidth(statementAddrTo); //轉全型
			if(clsService.is_function_on_codetype("l192m01a_statementAddrTo_nosub")){
			}else{
				statementAddrTo = Util.truncateString(statementAddrTo, MAXLEN_L192M01A_STATEMENTADDRTO);
			}
			l192m01a.setStatementAddrTo(statementAddrTo);			

			L192M01D l192m01d = new L192M01D();

			if (!CapString.isEmpty(params.getString("checkBase"))) {
				l192m01a.setCheckBase(CapDate.getDate(
						params.getString("checkBase"), "yyyy-MM-dd"));
			}

			if (!CapString.isEmpty(params.getString("checkDate"))) {
				l192m01a.setCheckDate(CapDate.getDate(
						params.getString("checkDate"), "yyyy-MM-dd"));
			}

			if (!CapString.isEmpty(params.getString("checkMan"))) {
				l192m01a.setCheckMan(params.getString("checkMan"));
			}

			if (!CapString.isEmpty(params.getString("leader"))) {
				l192m01a.setLeader(params.getString("leader"));
			}
			if (!CapString.isEmpty(params.getString("userItem1"))) {
				l192m01d.setUserItem1(params.getString("userItem1"));
			}

			if (!CapString.isEmpty(params.getString("userItem2"))) {
				l192m01d.setUserItem2(params.getString("userItem2"));
			}

			if (!CapString.isEmpty(params.getString("userItem3"))) {
				l192m01d.setUserItem3(params.getString("userItem3"));
			}

			l192m01d.setMainId(l192m01a.getMainId());
			l192m01a.setL192m01d(l192m01d);

			lms1905Service.saveNewDocument(l192m01a);

			includeData(l192m01a.getOid());

			JSONObject returnJsonObject = new JSONObject();
			returnJsonObject.put(EloanConstants.MAIN_OID,
					CapString.trimNull(l192m01a.getOid()));
			returnJsonObject.put(EloanConstants.MAIN_DOC_STATUS,
					CapString.trimNull(l192m01a.getDocStatus()));
			returnJsonObject.put(EloanConstants.MAIN_ID,
					CapString.trimNull(l192m01a.getMainId()));
			returnJsonObject.put(EloanConstants.MAIN_UID,
					CapString.trimNull(l192m01a.getUid()));
			returnJsonArray.add(returnJsonObject);

		}

		logger.debug("jsonarray : " + returnJsonArray.toString());
		result.set("custIDs", returnJsonArray.toString());
		return result;

	}

	/**
	 * 稽核工作底稿資料查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));

		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1905Service.getL192M01A(mainOid);
		}
		switch (page) {
		case 1:
			result.add(new CapAjaxFormResult(meta.toJSONObject(
					new String[] { "ownBrId", "checkBase", "docStatus",
							"checkDate", "checkMan", "tNo", "leader", "wpNo",
							"mtDoc", "randomCode" }, null)));

			// result.set("ownBrName",
			// branchService.getBranchName(meta.getOwnBrId()));
			result.set("ownBrName", new BranchNameFormatter(branchService,
					ShowTypeEnum.IDSpaceName).reformat(meta.getOwnBrId()));

			break;
		case 2:
			result.add(new CapAjaxFormResult(meta.toJSONObject(new String[] {
					"custId", "dupNo", "custName", "tTel", "tAddr", "cdQ1",
					"cdQ2", "statementAddrFrom", "statementAddrTo" }, null)));

			// 因地址可能會有很多筆，所以先select 出來秀在畫面上，如果資料過長，提示使用者資料要先修正並儲存
			if (meta.getTAddr() == null || "".equals(meta.getTAddr().trim())) {
				String allAddress = this.getCustAddr(meta.getCustId(),
						meta.getDupNo());
				result.set("tAddr", CapString.halfWidthToFullWidth(allAddress));
			}

			if (meta.getStatementAddrTo() == null
					|| "".equals(meta.getStatementAddrTo().trim())) {
				String allAddress = this.getCustAddr(meta.getCustId(),
						meta.getDupNo());

				result.set("statementAddrTo",
						CapString.halfWidthToFullWidth(allAddress));
			}

			result.set("ownBrName",
					branchService.getBranchName(meta.getOwnBrId()));
			// List<C112S01A> datas = service.getC112S01A(meta);
			// String key = null;
			// for (C112S01A data : datas) {
			// key = data.getItem();
			// result.set(key, data.getReqFlag());
			// result.set("g" + key, data.getInFlag());
			// }
			// break;
			break;

		case 3:
			break;
		case 4:
			break;
		case 5:
			L192M01D l192m01d = meta.getL192m01d();
			if (l192m01d != null) {
				result.add(new CapAjaxFormResult(l192m01d.toJSONObject(
						new String[] { "ck1", "ck2", "ck3Date", "ck4", "ck5",
								"ck6", "userItem1", "userCk1", "userItem2",
								"userCk2", "userItem3", "userCk3" }, null)));
			}

			break;
		case 6:
			result.add(new CapAjaxFormResult(meta.toJSONObject(new String[] {
					"gist", "processComm" }, null)));
			break;
		default:
		}

		IBranch iBranch = branchService.getBranch(MegaSSOSecurityContext
				.getUnitNo());

		// required information
		if (meta.getCreator() == null) {
			result.set("creator", MegaSSOSecurityContext.getUserName());
			result.set("createTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(CapDate
							.getCurrentTimestamp())));
			result.set("updater", MegaSSOSecurityContext.getUserName());
			result.set("updateTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(CapDate
							.getCurrentTimestamp())));
		} else {
			result.set("creator", userService.getUserName(meta.getCreator()));
			result.set("createTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(meta.getCreateTime())));

			result.set("updater", userService.getUserName(meta.getUpdater()));
			result.set("updateTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(meta.getUpdateTime())));
		}
		result.set(EloanConstants.MAIN_OID, CapString.trimNull(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(meta.getMainId()));
		result.set(EloanConstants.MAIN_UID, CapString.trimNull(meta.getUid()));
		result.set("docStatusCN",
				getMessage("docStatus." + meta.getDocStatus()));
		result.set("innerAudit", meta.getInnerAudit());
		return result;
	}

	private void truncate_addr(L192M01A l192m01a){
		l192m01a.setTAddr(Util.truncateString(l192m01a.getTAddr(), MAXLEN_L192M01A_TADDR));
		l192m01a.setStatementAddrFrom(Util.truncateString(l192m01a.getStatementAddrFrom(), MAXLEN_L192M01A_STATEMENTADDRFROM));
		l192m01a.setStatementAddrTo(Util.truncateString(l192m01a.getStatementAddrTo(), MAXLEN_L192M01A_STATEMENTADDRTO));
	}
	/**
	 * 儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult save(PageParameters params)
			throws CapException {
		L192M01A l192m01a = collectionData(params);
		truncate_addr(l192m01a);
		l192m01a.setRandomCode(IDGenerator.getRandomCode());
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1905Service.saveL192M01A(l192m01a);
		return query(params);
	}

	/**
	 * TEMP儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempSave(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		truncate_addr(l192m01a);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));
		lms1905Service.saveL192M01A(l192m01a);
		return result;
	}

	/**
	 * 資料處理
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return 稽核工作底稿
	 * @throws CapException
	 */
	private L192M01A collectionData(PageParameters params)
			throws CapException {

		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1905Service.getL192M01A(mainOid);

		// params = convertParameters(params);
		l192m01a = CapBeanUtil.map2Bean(params, l192m01a);

		switch (page) {
		case 1:
			if (l192m01a == null) {
				l192m01a = new L192M01A();
			}

			if (!CapString.isEmpty(params.getString("checkBase"))) {
				l192m01a.setCheckBase(CapDate.getDate(
						params.getString("checkBase"), "yyyy-MM-dd"));
			} else {
				l192m01a.setCheckBase(null);
			}

			if (!CapString.isEmpty(params.getString("checkDate"))) {
				l192m01a.setCheckDate(CapDate.getDate(
						params.getString("checkDate"), "yyyy-MM-dd"));
			} else {
				l192m01a.setCheckDate(null);
			}

			logger.debug(l192m01a.getTNo());

			break;
		case 5:
			L192M01D l192m01d = l192m01a.getL192m01d();
			if (l192m01d == null) {
				l192m01d = new L192M01D();
				l192m01d.setMainId(l192m01a.getMainId());
			}
			l192m01d.setCk1(params.getString("ck1"));
			l192m01d.setCk2(params.getString("ck2"));

			if (!CapString.isEmpty(params.getString("ck3Date"))) {
				l192m01d.setCk3Date(CapDate.getDate(
						params.getString("ck3Date"), "yyyy-MM-dd"));
			} else {
				l192m01d.setCk3Date(null);
			}

			l192m01d.setCk4(params.getString("ck4"));

			l192m01d.setCk5(params.getString("ck5"));

			l192m01d.setCk6(params.getString("ck6"));

			l192m01d.setUserItem1(params.getString("userItem1"));
			l192m01d.setUserCk1(params.getString("userCk1"));
			l192m01d.setUserItem2(params.getString("userItem2"));
			l192m01d.setUserCk2(params.getString("userCk2"));
			l192m01d.setUserItem3(params.getString("userItem3"));
			l192m01d.setUserCk3(params.getString("userCk3"));

			l192m01a.setL192m01d(l192m01d);

			break;

		default:
			break;
		}

		return l192m01a;

	}

	// /**
	// * 轉換params裡面的內容
	// *
	// * @param params
	// * @return
	// */
	// private PageParameters convertParameters(PageParameters params) {
	//
	// if (!CapString.isEmpty(params.getString("checkBase"))) {
	// params.put("checkBase", CapDate.getDate(
	// params.getString("checkBase"), "yyyy-MM-dd"));
	// }
	//
	// if (!CapString.isEmpty(params.getString("checkDate"))) {
	// params.put("checkDate", CapDate.getDate(
	// params.getString("checkDate"), "yyyy-MM-dd"));
	// }
	//
	// return params;
	// }

	/**
	 * 重新引入資料(申請內容，借，連保人等資料)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult doInclude(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// String custId = params.getString("custId");
		// String dupNo = params.getString("dupNo");
		// String mainId = params.getString(EloanConstants.MAIN_ID);
		// String brNo = user.getUnitNo();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		includeData(mainOid);
		return result;
	}

	/**
	 * 取得中文姓名資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getMisCustData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId_dupNo = CapString.trimNull(params.getString("custId"))
				.toUpperCase();

		if (custId_dupNo.length() == 0) {
			return result;
		}

		String custId = custId_dupNo.substring(0, custId_dupNo.length() - 1);
		String dupNo = custId_dupNo.substring(custId_dupNo.length() - 1,
				custId_dupNo.length());

		Map<String, Object> custData = (Map<String, Object>) misCustdataService
				.findCustdataSelCname(custId, dupNo);
		if (custData != null && !custData.isEmpty()) {
			result.set("custName", (String) custData.get("CNAME"));
		}
		return result;
	}

	/**
	 * 取得最新一份工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getLatestAuditSheet(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String shtType = params.getString("shtType");
		String innerAudit = params.getString("innerAudit");
		String brNo = "Y".equals(innerAudit) ? user.getUnitNo() : params
				.getString("brId");

		L192M01A l192m01a = lms1905Service
				.getLatestL192M01byBrNoShtTypeInnerAudit(brNo, shtType,
						innerAudit);
		L192M01D l192m01d = null;
		if (l192m01a != null) {
			l192m01d = l192m01a.getL192m01d();
		}

		result.set(
				"leader",
				l192m01a == null ? ""
						: CapString.trimNull(l192m01a.getLeader()));
		result.set(
				"userItem1",
				l192m01d == null ? "" : CapString.trimNull(l192m01d
						.getUserItem1()));
		result.set(
				"userItem2",
				l192m01d == null ? "" : CapString.trimNull(l192m01d
						.getUserItem2()));
		result.set(
				"userItem3",
				l192m01d == null ? "" : CapString.trimNull(l192m01d
						.getUserItem3()));
		return result;
	}

	/**
	 * 引入資料
	 * 
	 * @param mainOid
	 *            mainOid
	 */
	private void includeData(String mainOid) {

		L192M01A meta = lms1905Service.getL192M01A(mainOid);
		String custId = meta.getCustId();
		String dupNo = meta.getDupNo();
		String brNo = meta.getOwnBrId();
		String mainId = meta.getMainId();

		Set<String> contractNo = new HashSet<String>();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// String custId = params.getString("custId");
		// String dupNo = params.getString("dupNo");
		// String mainId = params.getString(EloanConstants.MAIN_ID);

		// String mainOid = params.getString(EloanConstants.MAIN_OID);

		logger.debug("custId : " + custId + " dupNo : " + dupNo);

		// 借款人及連保人基本資料檔
		List<L192M01B> l192m01bs = new ArrayList<L192M01B>();
		// 申請內容檔
		List<L192S01A> l192s01as = new ArrayList<L192S01A>();

		// 放款科目資料
		Map<String, String> dpCodes = misElacnmService.getDpCode();

		// ＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
		// XXX 以下取得的房貸資料, 對應到 where lnapcode in ('273','473','474','673','674')

		// 國內放款資料，無聯貿
		List<Map<String, Object>> lnf150data = misLNF150Service
				.findByCustIdDupOnlyHouse(custId, dupNo, brNo);
		// 國內放款資料，有聯貿
		List<Map<String, Object>> lnf150data2 = misLNF150Service
				.findByCustIdDupOnlyHouse2(custId, dupNo, brNo);

		// 海外放款資料
		// 海外dw資料，如果重覆序號為0就帶空白
		List<Map<String, Object>> asLndAvgOvsData = dwAslndavgovsService
				.getByCustIdBrNo(custId + ("0".equals(dupNo) ? " " : dupNo),
						brNo);

		if (lnf150data2 != null && !lnf150data2.isEmpty()) {
			String tmp_loan_no_m = "";
			BigDecimal tempQUOTAPRV = BigDecimal.ZERO;
			BigDecimal tempLOANBAL = BigDecimal.ZERO;

			String subject = "";// 科目
			String accNo = "";// 帳號
			String quotaNo = "";// 額度序號
			Date balDate = null;// 餘額日期
			String quotaCurr = "";// 額度幣別
			String balCurr = "";// 餘額幣別
			BigDecimal quotaAmt = BigDecimal.ZERO;// 額度金額
			BigDecimal balAmt = BigDecimal.ZERO;// 餘額金額
			Date fromDate = null;// 契約起日(動用日)
			Date endDate = null;// 契約迄日
			for (Map<String, Object> data : lnf150data2) {
				String lnf024_loan_no_m = (String) data.get("LNF024_LOAN_NO_M");

				if ("".equals(tmp_loan_no_m)
						|| tmp_loan_no_m.equals(lnf024_loan_no_m)) {
					subject = (String) data.get("LOANTP");// 科目
					accNo = lnf024_loan_no_m;// 帳號
					quotaNo = (String) (data.get("QUOTANO") == null ? "" : data
							.get("QUOTANO"));// 額度序號
					balDate = (Date) data.get("UPDTDT");// 餘額日期

					if (dpCodes.containsKey(subject)) {
						subject = dpCodes.get(subject);
					}

					quotaCurr = (String) (data.get("QUOTACURR") == null ? ""
							: data.get("QUOTACURR"));// 額度幣別
					balCurr = (String) (data.get("CURR") == null ? "" : data
							.get("CURR"));// 餘額幣別
					tempQUOTAPRV = (BigDecimal) data.get("QUOTAPRV");// 額度金額
					tempLOANBAL = (BigDecimal) data.get("LOANBAL");// 餘額金額
					fromDate = (Date) data.get("APRVDT");// 契約起日(動用日)
					endDate = (Date) data.get("DUEDT");// 契約迄日
					quotaAmt = quotaAmt.add(tempQUOTAPRV);
					balAmt = balAmt.add(tempLOANBAL);

				} else {

					L192S01A l192s01a = new L192S01A();
					l192s01a.setMainId(mainId);
					l192s01a.setAccNo(accNo);
					l192s01a.setQuotaNo(quotaNo);
					l192s01a.setQuotaCurr(quotaCurr);
					l192s01a.setBalCurr(balCurr);
					l192s01a.setQuotaAmt(quotaAmt);
					l192s01a.setBalAmt(balAmt);
					l192s01a.setFromDate(fromDate);
					l192s01a.setEndDate(endDate);
					l192s01a.setSubject(subject);
					l192s01a.setBalDate(balDate);
					l192s01a.setUseDate(fromDate);

					contractNo.add(l192s01a.getQuotaNo());

					this.getQuotainfData(custId, dupNo, brNo, quotaNo, l192s01a);

					l192s01a.setCreator(user.getUserId());
					l192s01a.setCreateTime(new Date());

					l192s01as.add(l192s01a);

					quotaAmt = BigDecimal.ZERO;
					balAmt = BigDecimal.ZERO;

					subject = (String) data.get("LOANTP");// 科目
					accNo = lnf024_loan_no_m;// 帳號
					quotaNo = (String) (data.get("QUOTANO") == null ? "" : data
							.get("QUOTANO"));// 額度序號
					balDate = (Date) data.get("UPDTDT");// 餘額日期

					if (dpCodes.containsKey(subject)) {
						subject = dpCodes.get(subject);
					}

					quotaCurr = (String) (data.get("QUOTACURR") == null ? ""
							: data.get("QUOTACURR"));// 額度幣別
					balCurr = (String) (data.get("CURR") == null ? "" : data
							.get("CURR"));// 餘額幣別
					tempQUOTAPRV = (BigDecimal) data.get("QUOTAPRV");// 額度金額
					tempLOANBAL = (BigDecimal) data.get("LOANBAL");// 餘額金額
					fromDate = (Date) data.get("APRVDT");// 契約起日(動用日)
					endDate = (Date) data.get("DUEDT");// 契約迄日
					quotaAmt = quotaAmt.add(tempQUOTAPRV);
					balAmt = balAmt.add(tempLOANBAL);

				}
				tmp_loan_no_m = lnf024_loan_no_m;
			}

			L192S01A l192s01a = new L192S01A();
			l192s01a.setMainId(mainId);
			l192s01a.setAccNo(accNo);
			l192s01a.setQuotaNo(quotaNo);
			l192s01a.setQuotaCurr(quotaCurr);
			l192s01a.setBalCurr(balCurr);
			l192s01a.setQuotaAmt(quotaAmt);
			l192s01a.setBalAmt(balAmt);
			l192s01a.setFromDate(fromDate);
			l192s01a.setEndDate(endDate);
			l192s01a.setSubject(subject);
			l192s01a.setBalDate(balDate);
			l192s01a.setUseDate(fromDate);

			contractNo.add(l192s01a.getQuotaNo());

			this.getQuotainfData(custId, dupNo, brNo, quotaNo, l192s01a);

			l192s01a.setCreator(user.getUserId());
			l192s01a.setCreateTime(new Date());
			l192s01as.add(l192s01a);
		}

		if (lnf150data != null && !lnf150data.isEmpty()) {
			for (Map<String, Object> data : lnf150data) {
				String subject = (String) data.get("LOANTP");// 科目

				// 只針對房貸的科目，sql也有先排除了
				// if (!("51".equals(subject) || "61".equals(subject)
				// || "23".equals(subject) || "65".equals(subject) || "62"
				// .equals(subject))) {
				// continue;
				// }

				String accNo = (String) (data.get("LOANNO") == null ? "" : data
						.get("LOANNO"));// 帳號
				String quotaNo = (String) (data.get("QUOTANO") == null ? ""
						: data.get("QUOTANO"));// 額度序號
				Date balDate = (Date) data.get("UPDTDT");// 餘額日期

				if (dpCodes.containsKey(subject)) {
					subject = dpCodes.get(subject);
				}

				// 儲存額度序號再去撈主從債務人檔
				contractNo.add(quotaNo);

				String quotaCurr = (String) (data.get("QUOTACURR") == null ? ""
						: data.get("QUOTACURR"));// 額度幣別
				String balCurr = (String) (data.get("CURR") == null ? "" : data
						.get("CURR"));// 餘額幣別
				BigDecimal quotaAmt = (BigDecimal) data.get("QUOTAPRV");// 額度金額
				BigDecimal balAmt = (BigDecimal) data.get("LOANBAL");// 餘額金額
				Date fromDate = (Date) data.get("APRVDT");// 契約起日(動用日)
				Date endDate = (Date) data.get("DUEDT");// 契約迄日

				L192S01A l192s01a = new L192S01A();
				l192s01a.setMainId(mainId);
				l192s01a.setAccNo(accNo);
				l192s01a.setQuotaNo(quotaNo);
				l192s01a.setQuotaCurr(quotaCurr);
				l192s01a.setBalCurr(balCurr);
				l192s01a.setQuotaAmt(quotaAmt);
				l192s01a.setBalAmt(balAmt);
				l192s01a.setFromDate(fromDate);
				l192s01a.setEndDate(endDate);
				l192s01a.setSubject(subject);
				l192s01a.setBalDate(balDate);
				l192s01a.setUseDate(fromDate);

				this.getQuotainfData(custId, dupNo, brNo, quotaNo, l192s01a);

				l192s01a.setCreator(user.getUserId());
				l192s01a.setCreateTime(new Date());

				l192s01as.add(l192s01a);
			}
		}

		// 海外放款資料
		if (asLndAvgOvsData != null && !asLndAvgOvsData.isEmpty()) {
			for (Map<String, Object> asLndAvgOvs : asLndAvgOvsData) {

				String subject = (String) asLndAvgOvs.get("GL_AC_KEY");// 海外用會計科目

				// 只針對房貸的科目
				if (!("1260-50-00".equals(subject)
						|| "1350-62-00".equals(subject)
						|| "1350-63-00".equals(subject)
						|| "1450-15-00".equals(subject) || "1450-20-00"
						.equals(subject))) {
					continue;
				}

				String accNo = (String) asLndAvgOvs.get("ACCT_KEY");// 帳號
				String quotaNo = (String) asLndAvgOvs.get("FACT_CONTR");// 額度序號
				Date balDate = (Date) asLndAvgOvs.get("DW_DATA_SRC_DT");// 餘額日期

				if (dpCodes.containsKey(subject)) {
					subject = dpCodes.get(subject);
				}

				// 儲存額度序號 去撈主從債務人檔
				contractNo.add(quotaNo);

				String quotaCurr = (String) asLndAvgOvs.get("FACT_CUR_CD");// 額度幣別
				String balCurr = (String) asLndAvgOvs.get("CUR_CD");// 餘額幣別
				BigDecimal quotaAmt = (BigDecimal) asLndAvgOvs.get("FACT_AMT");// 額度金額
				BigDecimal balAmt = (BigDecimal) asLndAvgOvs.get("LN_BAL");// 餘額金額

				Date fromDate = (Date) asLndAvgOvs.get("CONTR_START_DT");// 契約起日(動用日)
				Date endDate = (Date) asLndAvgOvs.get("CONTR_DUE_DT");// 契約迄日

				L192S01A l192s01a = new L192S01A();
				l192s01a.setMainId(mainId);
				l192s01a.setAccNo(accNo);
				l192s01a.setQuotaNo(quotaNo);
				l192s01a.setQuotaCurr(quotaCurr);
				l192s01a.setBalCurr(balCurr);
				l192s01a.setQuotaAmt(quotaAmt);
				l192s01a.setBalAmt(balAmt);
				l192s01a.setFromDate(fromDate);
				l192s01a.setEndDate(endDate);
				l192s01a.setSubject(subject);
				l192s01a.setBalDate(balDate);
				l192s01a.setCreator(user.getUserId());
				l192s01a.setCreateTime(new Date());

				l192s01as.add(l192s01a);
			}
		}

		// 先篩選出同樣的資料，避免重覆的資料一真去select db
		// L192M01B 有實作hashCode 和 equals ，custid, dupno,custype,main 為unique
		for (String cntrNo : contractNo) {

			List<Map<String, Object>> ellngteeDatas = misELLNGTEEService
					.getByCustIdCntrNo(custId, dupNo, cntrNo);

			for (Map<String, Object> ellngteeData : ellngteeDatas) {
				String lngeId = (String) (ellngteeData.get("LNGEID") == null ? ""
						: ellngteeData.get("LNGEID"));
				String dupNo1 = (String) (ellngteeData.get("DUPNO1") == null ? ""
						: ellngteeData.get("DUPNO1"));
				String lngeFlag = (String) (ellngteeData.get("LNGEFLAG") == null ? ""
						: ellngteeData.get("LNGEFLAG"));
				String lngeNm = (String) (ellngteeData.get("LNGENM") == null ? ""
						: ellngteeData.get("LNGENM"));

				L192M01B l192m01b = new L192M01B();
				l192m01b.setMainId(mainId);
				l192m01b.setCustId(lngeId);
				l192m01b.setDupNo(dupNo1);

				// C: 共同借款人
				// D: 共同發票人　
				// E: 票據債務人（指金融交易之擔保背書）
				// G: 連帶保證人，擔保品提供人兼連帶保證人
				// L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人
				// S: 擔保品提供人
				// N: ㄧ般保證人
				if ("C".equals(lngeFlag)) {
					// 1.借款人, 2.連保人
					l192m01b.setCustType("1");
				} else {
					l192m01b.setCustType("2");
				}

				l192m01b.setCustName(lngeNm);
				l192m01bs.add(l192m01b);
			}
		}

		L192M01B main = new L192M01B();
		main.setMainId(mainId);
		main.setCustId(meta.getCustId());
		main.setDupNo(meta.getDupNo());
		main.setCustType("1");
		l192m01bs.add(main);

		// 清除重覆的資料，有實作L192M01B 的hashCode 和equals
		Set<L192M01B> uniqueTmp = new HashSet<L192M01B>(l192m01bs);
		List<L192M01B> l192m01bs2 = new ArrayList<L192M01B>();

		for (L192M01B l192m01b : uniqueTmp) {

			String id = l192m01b.getCustId();
			String dup = l192m01b.getDupNo();
			Map<String, Object> data = (Map<String, Object>) misCustdataService
					.findCustdataMapByMainId(id, dup);

			if (data != null) {
				String buscd = (String) data.get("BUSCD");
				String custName = (String) data.get("CNAME");
				// String posi = "";

				if (!isEnterpriseCustomers(buscd)) {
					
					C120S01B c120s01b = lms1905Service
							.getLatestC120S01BByCustId(id, dup);
					if (c120s01b != null) {
						l192m01b.setIncomeCurr(c120s01b.getPayCurr());
						l192m01b.setIncomeAmt(c120s01b.getPayAmt());

						CodeType codeType = codeTypeService
								.findByCodeTypeAndCodeValue(
										"lms1205s01_jobType1", CapString
												.trimNull(c120s01b
														.getJobType1()));
						String posiName = codeType != null ? CapString
								.trimNull(codeType.getCodeDesc()) : "";

						int posiBtyeLength = posiName.getBytes().length;

						posiName = new String(posiName.getBytes(), 0,
								posiBtyeLength <= 60 ? posiBtyeLength : 60);

						l192m01b.setPosi(posiName);

						// posi =
						// codeTypeService.findByCodeTypeAndCodeValue(codeType,
						// codeValue); //再從codeType抓對應的值

					}

				}
				l192m01b.setCustName(custName);
			}

			l192m01bs2.add(l192m01b);
		}
		// 取得擔保品資料
		List<L192S02A> l192s02as = this.getCmsData(custId, dupNo, brNo, mainId);

		lms1905Service
				.includeData(meta, l192m01bs2, null, l192s01as, l192s02as);

		// lms1905Service.reNewL192S01A(mainOid, l192s01as);
		// lms1905Service.reNewL192M01B(mainOid, l192m01bs2);
		// return result;
	}

	/**
	 * 取得客戶地址資料
	 * 
	 * @param custId
	 *            custId
	 * @param dupNo
	 *            dupNo
	 * @return
	 */
	private String getCustAddr(String custId, String dupNo) {

		List<Map<String, Object>> custAddrs = misELCUS21Service
				.getCustAddressForLas(custId, dupNo);

		StringBuffer allAddress = new StringBuffer("");
		for (int i = 0; i < custAddrs.size(); i++) {
			Map<String, Object> custAddr = custAddrs.get(i);

			// J-105-0233-001 Web
			// e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
			// String addrzip = custAddr.get("ADDRZIP") == null ? ""
			// : (String) custAddr.get("ADDRZIP");
			// String cityr = custAddr.get("CITYR") == null ? ""
			// : (String) custAddr.get("CITYR");
			// String townr = custAddr.get("TOWNR") == null ? ""
			// : (String) custAddr.get("TOWNR");
//			String addrr = custAddr.get("ADDRR") == null ? ""
//					: (String) custAddr.get("ADDRR");
			
			String addrr = custAddr.get("FULLADDR") == null ? ""
					: (String) custAddr.get("FULLADDR");

			allAddress.append(new StringBuffer().append(addrr.trim()));
			if ((i + 1) != custAddrs.size()) {
				allAddress.append(';');
			}
		}
		return allAddress.toString();

	}

	/**
	 * 判斷是否為企業戶
	 * 
	 * @param buscd
	 * @return Y/N
	 */
	private boolean isEnterpriseCustomers(String buscd) {
		if (LMSUtil.isBusCode_060000_130300(buscd)) {
			return false;
		}
		return true;
	}

	/**
	 * 判斷使用者是否有EL02角色
	 * 
	 * @return
	 */
	private boolean hasEL02() {
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();

		for (String role : eloanRoles) {
			if (role != null && role.endsWith("EL02")) {
				return true;
			}
		}

		return false;
	}

	/**
	 * 取得房貸 額度資訊檔資料
	 * 
	 * @param custId
	 *            custId
	 * @param dupNo
	 *            dupNo
	 * @param brNo
	 *            brNo
	 * @param quotaNo
	 *            額度序號
	 * @param l192s01a
	 *            申請內容
	 */
	private void getQuotainfData(String custId, String dupNo, String brNo,
			String quotaNo, L192S01A l192s01a) {
		// 房貸 額度資訊檔
		Map<String, Object> quotainfData = misQuotainfService.getByKey(custId,
				dupNo, brNo, quotaNo);

		if (quotainfData != null) {
			// 申請日 得到的是民國年
			String appDate = (String) quotainfData.get("APPDATE");
			TWNDate _appDate = new TWNDate();
			_appDate.setTime(appDate);
			l192s01a.setAppDate(_appDate);

			// 核準日 得到的是民國年
			String signDate = (String) quotainfData.get("APRDATE");
			TWNDate _signDate = new TWNDate();
			_signDate.setTime(signDate);
			l192s01a.setSignDate(_signDate);

			// 進帳方式(存款帳號)
			String tAccNo = (String) quotainfData.get("ACCNO");

			String way = "";
			DecimalFormat df = new DecimalFormat("###,###,###,###");

			if (tAccNo != null && tAccNo.trim().length() > 0) {
				BigDecimal tRctAmt1 = (BigDecimal) quotainfData.get("RCTAMT1");

				way = "1.存款帳號：" + tAccNo + "，進帳金額：" + df.format(tRctAmt1) + "元";
			}

			String tRmtBh = (String) quotainfData.get("RMTBH");
			if (tRmtBh != null && tRmtBh.trim().length() > 0) {
				String tRmtNo = (String) quotainfData.get("RMTNO"); // 進帳方式(匯款-解款帳號)

				BigDecimal tRctAmt2 = (BigDecimal) quotainfData.get("RCTAMT2");// 進帳方式匯款之進帳金額

				way = way + "  2.匯款-解款銀行：" + tRmtBh + "，解款帳號：" + tRmtNo
						+ "，解款金額：" + df.format(tRctAmt2) + "元";
			}

			// 房貸有setWay(進帳方式/計息方式)
			l192s01a.setWay(way);

			// 核准者 appr VARCHAR(38)
			String tBossName = (String) quotainfData.get("BOSSNAME");// 核准主管姓名

			l192s01a.setAppr(tBossName);
		}
	}

	/**
	 * 註記是否列印對帳單
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult printMark(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String printMark = params.getString("printMark");
		L192M01A meta = lms1905Service.getL192M01A(mainOid);
		if (meta != null) {
			lms1905Service.printMark(meta, printMark);
		}
		return result;
	}

	private List<L192S02A> getCmsData(String custId, String dupNo, String brNo,
			String mainId) { // 引入擔保品資料

		// 取得已設定擔保品資料
		List<Map<String, Object>> cmsDatas = eloandbcmsBASEService
				.getSetDataByCustIdAndBranch(custId, dupNo, brNo);

		// 擔保品資料
		List<L192S02A> l192s02as = new ArrayList<L192S02A>();
		for (Map<String, Object> cmsData : cmsDatas) {
			L192S02A l192s02a = new L192S02A();

			String cmsMainId = MapUtils.getString(cmsData, "mainId", "");
			String collTyp1 = MapUtils.getString(cmsData, "collTyp1", "");
			String collTyp2 = MapUtils.getString(cmsData, "collTyp2", "");

			String cmsName = "";

			if ("01".equals(collTyp1)) {
				String target = "";
				int lmsCount = 0;
				List<Map<String, Object>> landDatas = eloandbcmsBASEService
						.getC101M03ByMainId(cmsMainId);
				List<Map<String, Object>> buildDatas = eloandbcmsBASEService
						.getC101M04ByMainId(cmsMainId);
				lmsCount = landDatas.size() + buildDatas.size();
				if (CollectionUtils.isEmpty(landDatas)) {
					if (!CollectionUtils.isEmpty(buildDatas)) {
						for (Map<String, Object> buildData : buildDatas) {
							target = MapUtils.getString(buildData, "target");
						}
					}
				} else {
					for (Map<String, Object> landData : landDatas) {
						target = MapUtils.getString(landData, "target");
						break;
					}
				}

				cmsName += cmsName + target;

				if (lmsCount >= 2) {
					cmsName += "等" + lmsCount + "筆";
				}

				logger.debug("cmsName : " + cmsName);

				String ownerNames = "";
				List<Map<String, Object>> ownerDatas = eloandbcmsBASEService
						.getC101S03BByMainId(cmsMainId);
				Set<String> ownerNameSet = new HashSet<String>();
				for (Map<String, Object> ownerData : ownerDatas) {
					ownerNameSet.add((String) ownerData.get("ownerNm"));
				}

				if (CollectionUtils.isNotEmpty(ownerNameSet)) {
					int allCount = ownerNameSet.size();
					int tmpCount = 0;
					for (String ownerName : ownerNameSet) {
						tmpCount++;
						if (tmpCount > 4) {
							break;
						}

						if (tmpCount < 4 && allCount != tmpCount) {
							ownerNames = ownerNames + ownerName + "、";
						} else {
							ownerNames = ownerNames + ownerName;
						}

					}

					if (allCount > 4) {
						ownerNames += "等" + allCount + "筆";
					}
				}
				// 不動產保險資料
				List<Map<String, Object>> insuranceDatas = eloandbcmsBASEService
						.getC101M08ByMainId(cmsMainId);
				/*
					參考MIS.COLLINS 保險種類，同樣代號(例如32)，代表不同意義{不動產:32其他保險}{動產: 32汽車保險}
					● 不動產的「保險種類」值域select * from com.bcodetype where codetype='cms1010_insType' and locale='zh_TW' order by codeorder
					● 動產的「保險種類」值域select * from com.bcodetype where codetype='insType' and locale='zh_TW' order by codeorder
				*/
				Map<String, String> insType = codeTypeService
						.findByCodeType("cms1010_insType");

				String insDesc = "";
				Date insBDate = null;
				Date insEDate = null;
				if (CollectionUtils.isNotEmpty(insuranceDatas)) {
					for (Map<String, Object> insuranceData : insuranceDatas) {
						int insTypes = (Integer) MapUtils.getObject(
								insuranceData, "insType");

						String insTitle = "";
						List<String> ins = Util.valueOfCheckBoxOptionList(
								insTypes, 11);

						for (int i = 0; i < ins.size(); i++) {
							if (i == 0) {
								insTitle = insTitle + insType.get(ins.get(i));
							} else {
								insTitle = insTitle + ","
										+ insType.get(ins.get(i));
							}
						}
						// System.out.println(insTitle);
						BigDecimal insAmt = (BigDecimal) MapUtils.getObject(
								insuranceData, "insAmt", BigDecimal.ZERO);
						if (insAmt != null) {
							insAmt = insAmt.divide(BigDecimal.valueOf(10000));
						}

						insDesc = insTitle + insAmt + "萬";

						insBDate = (Date) MapUtils.getObject(insuranceData,
								"insBDate", null);
						insEDate = (Date) MapUtils.getObject(insuranceData,
								"insEDate", null);

					}
				}

				l192s02a.setGteName(cmsName);
				l192s02a.setOwner(ownerNames);
				l192s02a.setInsurance(insDesc);
				l192s02a.setInsDateFrom(insBDate);
				l192s02a.setInsDateEnd(insEDate);

			} else if ("02".equals(collTyp1)) {
				Map<String, String> cms1020_collTyp2Map = codeTypeService
						.findByCodeType("cms1020_collTyp2");
				String target = MapUtils.getString(cms1020_collTyp2Map,
						collTyp2, "");
				cmsName += cmsName + target;

				// 動產保險資料
				List<Map<String, Object>> insuranceDatas = eloandbcmsBASEService
						.getC102S01DByMainId(cmsMainId);

				Map<String, String> insType = codeTypeService
						.findByCodeType("insType");

				String insDesc = "";
				Date insBDate = null;
				Date insEDate = null;
				if (CollectionUtils.isNotEmpty(insuranceDatas)) {
					for (Map<String, Object> insuranceData : insuranceDatas) {
						int insTypes = (Integer) MapUtils.getObject(
								insuranceData, "insType");

						String insTitle = "";
						List<String> ins = Util.valueOfCheckBoxOptionList(
								insTypes, 11);

						for (int i = 0; i < ins.size(); i++) {
							if (i == 0) {
								insTitle = insTitle + insType.get(ins.get(i));
							} else {
								insTitle = insTitle + ","
										+ insType.get(ins.get(i));
							}
						}
						// System.out.println(insTitle);
						BigDecimal insAmt = (BigDecimal) MapUtils.getObject(
								insuranceData, "insAmt", BigDecimal.ZERO);
						if (insAmt != null) {
							insAmt = insAmt.divide(BigDecimal.valueOf(10000));
						}

						insDesc = insTitle + insAmt + "萬";

						insBDate = (Date) MapUtils.getObject(insuranceData,
								"insBDate", null);
						insEDate = (Date) MapUtils.getObject(insuranceData,
								"insEDate", null);

					}
				}

				l192s02a.setGteName(cmsName);
				l192s02a.setInsurance(insDesc);
				l192s02a.setInsDateFrom(insBDate);
				l192s02a.setInsDateEnd(insEDate);

			} else if ("03".equals(collTyp1)) {
				Map<String, String> cms1030_collTyp2 = codeTypeService
						.findByCodeType("cms1030_collTyp2");

				String target = MapUtils.getString(cms1030_collTyp2, collTyp2,
						"");

				cmsName += cmsName + target;
				if ("01".equals(collTyp2)) {

					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01AByMainId(cmsMainId);
					// 抓取存單號碼

					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							Map<String, String> cms1030_flg = codeTypeService
									.findByCodeType("cms1030_flg");
							String flag = MapUtils.getString(c103, "flg", "");
							String flagName = MapUtils.getString(cms1030_flg,
									flag, "");
							String depNo = MapUtils
									.getString(c103, "depNo", "");

							cmsName += flagName + "存單號碼" + depNo;

							l192s02a.setGteName(cmsName);
							break;
						}
					}

				} else if ("02".equals(collTyp2)) {
					// 國庫券
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01BByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							String kind = MapUtils.getString(c103, "kind", "");
							// 甲種，乙種代碼
							Map<String, String> C103S01B_kind = codeTypeService
									.findByCodeType("C103S01B_kind");

							String kindName = MapUtils.getString(C103S01B_kind,
									kind, "");

							cmsName += kindName + name;
							break;
						}
						l192s02a.setGteName(cmsName);
					}
				} else if ("03".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					String bondNm = "";
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01CByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							bondNm = MapUtils.getString(c103, "bondNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");
							break;
						}
						l192s02a.setGteName(bondNm + ":數量(" + qnty.intValue()
								+ ")");
					}
				} else if ("04".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					String bondNm = "";
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01DByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							bondNm = MapUtils.getString(c103, "bondNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");

							break;
						}
						l192s02a.setGteName(bondNm + ":數量(" + qnty.intValue()
								+ ")");
					}
				} else if ("05".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					BigDecimal parVal = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01EByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							parVal = (BigDecimal) MapUtils.getObject(c103,
									"parVal");
							break;
						}
						l192s02a.setGteName(name + ":面額(" + parVal + ")");
					}
				} else if ("06".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					String cbNm = null;
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01FByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							cbNm = MapUtils.getString(c103, "cbNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");
							break;
						}
						l192s02a.setGteName(name + ":(" + cbNm + "數量" + qnty
								+ ")");

					}
				} else if ("07".equals(collTyp2)) {

					String stkNm = null;
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01GByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							stkNm = MapUtils.getString(c103, "stkNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");
							cmsName += ":" + stkNm + "(鑑價數量:" + qnty + ")";
							break;
						}
						l192s02a.setGteName(cmsName);
					}

				} else if ("08".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					String fundNm = null;
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01HByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							fundNm = MapUtils.getString(c103, "fundNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");
							break;
						}
						cmsName += cmsName + name + ":" + fundNm + "數量:" + qnty
								+ ")";
						l192s02a.setGteName(cmsName);
					}
				}
			} else if ("04".equals(collTyp1)) {
				List<Map<String, Object>> c104Data = eloandbcmsBASEService
						.getC104S01ByMainId(cmsMainId);
				String trstNm = null;
				BigDecimal qnty = null;
				if (CollectionUtils.isNotEmpty(c104Data)) {
					for (Map<String, Object> c104 : c104Data) {
						trstNm = MapUtils.getString(c104, "trstNm", "");
						qnty = (BigDecimal) MapUtils.getObject(c104, "Qnty");
						break;
					}
					cmsName += "倉單:" + trstNm + "(數量" + qnty + ")";
					l192s02a.setGteName(cmsName);
				}

			} else if ("05".equals(collTyp1)) {
				Map<String, String> cms1052_collTyp2 = codeTypeService
						.findByCodeType("cms1052_collTyp2");
				if ("01".equals(collTyp2)) {
					String name = MapUtils.getString(cms1052_collTyp2,
							collTyp2, "");
					// String kind = MapUtils.getString(map, key)

					List<Map<String, Object>> c105Data = eloandbcmsBASEService
							.getC105S01AByMainId(cmsMainId);
					String curr = null;
					BigDecimal appVal = null;

					if (CollectionUtils.isNotEmpty(c105Data)) {
						for (Map<String, Object> c105 : c105Data) {
							Map<String, String> c105s01a_grtKind = codeTypeService
									.findByCodeType("C105S01A_grtKind");
							String kind = MapUtils.getString(c105, "grtKind",
									"");
							String kindName = MapUtils.getString(
									c105s01a_grtKind, kind, "");
							curr = MapUtils.getString(c105, "curr", "");
							appVal = (BigDecimal) MapUtils.getObject(c105,
									"appVal");

							cmsName += name + "," + "種類:" + kindName + " "
									+ curr + appVal;
							break;
						}
						l192s02a.setGteName(cmsName);
					}
				} else if ("02".equals(collTyp2)) {
					String name = MapUtils.getString(cms1052_collTyp2,
							collTyp2, "");
					String grtDept = null;
					String grtKind = null;
					String curr = null;
					BigDecimal appVal = null;
					List<Map<String, Object>> c105Data = eloandbcmsBASEService
							.getC105S01BByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c105Data)) {
						for (Map<String, Object> c105 : c105Data) {
							grtDept = MapUtils.getString(c105, "grtDept", "");
							grtKind = MapUtils.getString(c105, "grtKind", "");
							curr = MapUtils.getString(c105, "curr", "");
							appVal = (BigDecimal) MapUtils.getObject(c105,
									"appVal");

							cmsName += name + ":" + grtDept + ",種類:" + grtKind
									+ " " + curr + appVal;
							break;
						}
						l192s02a.setGteName(cmsName);
					}
				} else if ("03".equals(collTyp2)) {
					String name = MapUtils.getString(cms1052_collTyp2,
							collTyp2, "");
					String grtDept = null;
					String grtKind = null;
					String curr = null;
					BigDecimal appVal = null;
					List<Map<String, Object>> c105Data = eloandbcmsBASEService
							.getC105S01CByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c105Data)) {
						for (Map<String, Object> c105 : c105Data) {
							grtDept = MapUtils.getString(c105, "grtDept", "");
							grtKind = MapUtils.getString(c105, "grtKind", "");
							curr = MapUtils.getString(c105, "curr", "");
							appVal = (BigDecimal) MapUtils.getObject(c105,
									"appVal");
							cmsName += name + ":" + grtDept + ",(項目:" + grtKind
									+ " " + curr + appVal;

							break;
						}

					}
				} else if ("04".equals(collTyp2)) {
					String name = MapUtils.getString(cms1052_collTyp2,
							collTyp2, "");
					String grtNm = null;
					String grtItem = null;
					String curr = null;
					BigDecimal appVal = null;
					List<Map<String, Object>> c105Data = eloandbcmsBASEService
							.getC105S01DByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c105Data)) {
						for (Map<String, Object> c105 : c105Data) {
							grtNm = MapUtils.getString(c105, "grtNm", "");
							grtItem = MapUtils.getString(c105, "grtItem", "");
							curr = MapUtils.getString(c105, "curr", "");
							appVal = (BigDecimal) MapUtils.getObject(c105,
									"appVal");
							cmsName += name + ":" + grtNm + ",(方式:" + grtItem
									+ " " + curr + appVal;
							break;
						}
						l192s02a.setGteName(cmsName);
					}
				}
			} else if ("06".equals(collTyp1)) {
				List<Map<String, Object>> c106Data = eloandbcmsBASEService
						.getC106S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c106Data)) {
					for (Map<String, Object> c106 : c106Data) {
						String kind = MapUtils.getString(c106, "kind", "");
						String ntType = MapUtils.getString(c106, "ntType", "");
						String ntNo = MapUtils.getString(c106, "ntNo", "");

						if ("1".equals(kind)) {
							// "額度本票：";
							cmsName += "額度本票：";
						} else if ("2".equals(kind)) {
							// "備償票據：";
							cmsName += "備償票據：";
						}

						if ("1".equals(ntType)) {
							// "本票(票據號碼：";
							cmsName += "本票(票據號碼：" + ntNo + ")";
						} else if ("2".equals(ntType)) {
							// "匯票(票據號碼：";
							cmsName += "匯票(票據號碼：" + ntNo + ")";
						} else if ("3".equals(ntType)) {
							// "支票(票據號碼：";
							cmsName += "支票(票據號碼：" + ntNo + ")";
						}
						l192s02a.setGteName(cmsName);
						break;
					}
				}
			} else if ("07".equals(collTyp1)) {
				List<Map<String, Object>> c107Data = eloandbcmsBASEService
						.getC107S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c107Data)) {
					for (Map<String, Object> c107 : c107Data) {
						String ntType = MapUtils.getString(c107, "ntType", "");
						String isuNm = MapUtils.getString(c107, "isuNm", "");
						String ntNo = MapUtils.getString(c107, "ntNo", "");

						cmsName += ntType.equals("1") ? "本票" : "匯票";
						cmsName += "(發票人名稱：" + isuNm + "　票據號碼：" + ntNo;
						break;
					}
					l192s02a.setGteName(cmsName);

				}
			} else if ("08".equals(collTyp1)) {
				Map<String, String> cms1020_collTyp2Map = codeTypeService
						.findByCodeType("cms1020_collTyp2");
				String target = MapUtils.getString(cms1020_collTyp2Map,
						collTyp2, "");

				List<Map<String, Object>> c108Data = eloandbcmsBASEService
						.getC108S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c108Data)) {
					for (Map<String, Object> c108 : c108Data) {
						String collNm = MapUtils.getString(c108, "collNm", "");
						String unit = MapUtils.getString(c108, "unit", "");
						BigDecimal qnty = (BigDecimal) MapUtils.getObject(c108,
								"qnty");

						cmsName += "信托佔有(" + target + ")" + collNm + " " + qnty
								+ unit;
						break;
					}
					l192s02a.setGteName(cmsName);
				}

			} else if ("09".equals(collTyp1)) {

				List<Map<String, Object>> c109Data = eloandbcmsBASEService
						.getC109S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c109Data)) {
					for (Map<String, Object> c109 : c109Data) {
						String collNm = MapUtils.getString(c109, "collNm", "");
						String unit = MapUtils.getString(c109, "unit", "");
						BigDecimal qnty = (BigDecimal) MapUtils.getObject(c109,
								"qnty");
						cmsName += "參貸他行：" + collNm + " " + qnty + unit;
						break;
					}
					l192s02a.setGteName(cmsName);
				}
				// 其它
			} else if ("10".equals(collTyp1)) {
				List<Map<String, Object>> c110Data = eloandbcmsBASEService
						.getC110S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c110Data)) {
					for (Map<String, Object> c110 : c110Data) {
						String collNm = MapUtils.getString(c110, "collNm", "");
						String unit = MapUtils.getString(c110, "unit", "");
						BigDecimal qnty = (BigDecimal) MapUtils.getObject(c110,
								"qnty");

						cmsName += "其他擔保品：" + collNm + " " + qnty + unit;
						break;
					}

				}

				// 反面承諾
			} else if ("11".equals(collTyp1)) {
				List<Map<String, Object>> c110Data = eloandbcmsBASEService
						.getC110S01BByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c110Data)) {
					for (Map<String, Object> c110 : c110Data) {

						break;
					}
					cmsName += "反面承諾";
					l192s02a.setGteName(cmsName);
				}

				// 浮動擔保抵押
			} else if ("12".equals(collTyp1)) {
				List<Map<String, Object>> c110Data = eloandbcmsBASEService
						.getC110S01CByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c110Data)) {
					for (Map<String, Object> c110 : c110Data) {
						String collName = MapUtils.getString(c110, "collName",
								"");
						cmsName += "其他擔保品：" + collName;
						break;
					}
					l192s02a.setGteName(cmsName);
				}
			}

			Date estData = (Date) MapUtils.getObject(cmsData, "estDate", null);
			String currCd = MapUtils.getString(cmsData, "currCd", "");
			BigDecimal appAmt = (BigDecimal) MapUtils.getObject(cmsData,
					"appAmt", null);
			BigDecimal loanAmt = (BigDecimal) MapUtils.getObject(cmsData,
					"loanAmt", null);

			// 設定資料檔
			Map<String, Object> setData = eloandbcmsBASEService
					.getC100M03ByMainId(cmsMainId);

			Date setDate = (Date) MapUtils.getObject(setData, "setDate", null);
			Date setDateFrom = (Date) MapUtils.getObject(setData, "SETBDATE",
					null);
			Date setDateEnd = (Date) MapUtils.getObject(setData, "SETEDATE",
					null);
			BigDecimal setOdr = (BigDecimal) MapUtils.getObject(setData,
					"setOdr", null);
			String setCurr = MapUtils.getString(setData, "setCurr", "");
			BigDecimal setAmt = (BigDecimal) MapUtils.getObject(setData,
					"setAmt", null);

			l192s02a.setEstDate(estData);
			l192s02a.setEstCurr(currCd);
			l192s02a.setLoanCurr(currCd);
			l192s02a.setEstAmt(appAmt);
			l192s02a.setLoanAmt(loanAmt);
			l192s02a.setMainId(mainId);
			l192s02a.setSetDate(setDate);
			l192s02a.setSetDateFrom(setDateFrom);
			l192s02a.setSetDateEnd(setDateEnd);
			l192s02a.setSetPosition(setOdr == null ? null : setOdr.intValue());
			l192s02a.setSetCurr(setCurr);
			l192s02a.setSetAmt(setAmt);

			l192s02as.add(l192s02a);
		}
		return l192s02as;
	}

	/**
	 * private void includeCmsData(String custId, String dupNo) {
	 * List<Map<String, Object>> cmsData = misELF346Service.getElF346Data(
	 * custId, dupNo);
	 * 
	 * for (Map<String, Object> data : cmsData) { String _estDate = (String)
	 * data.get("ESTDT"); // 鑑價日期 _estDate = this.addSlash(_estDate); TWNDate
	 * estDate = new TWNDate(); estDate.setTime(_estDate);
	 * 
	 * String _setDate = (String) data.get("RGSTDT"); // 設定日期 _setDate =
	 * this.addSlash(_setDate); TWNDate setDate = new TWNDate();
	 * setDate.setTime(_setDate);
	 * 
	 * String setCurr = (String) data.get("RGSTCUR");// 設定幣別 BigDecimal setAmt =
	 * (BigDecimal) data.get("RGSTAMT"); // 設定金額 BigDecimal loanAmt =
	 * (BigDecimal) data.get("LOANAMT"); // 押值 String loanCurr = (String)
	 * data.get("CURR"); // 押值幣別 BigDecimal setPosition = (BigDecimal)
	 * data.get("RGSTODR"); // 設定順位
	 * 
	 * L192S02A l192s02a = new L192S02A(); l192s02a.setEstDate(estDate);
	 * l192s02a.setSetDate(setDate); l192s02a.setSetCurr(setCurr);
	 * l192s02a.setSetAmt(setAmt); l192s02a.setLoanCurr(loanCurr);
	 * l192s02a.setLoanAmt(loanAmt);
	 * l192s02a.setSetPosition(Integer.parseInt(setPosition.toString()));
	 * 
	 * } }
	 * 
	 * private String addSlash(String txt) { String dd =
	 * txt.substring(txt.length() - 2, txt.length()); String mm =
	 * txt.substring(txt.length() - 4, txt.length() - 2); String yy =
	 * txt.substring(0, txt.length() - 4); return yy + "-" + mm + "-" + dd; }
	 * 
	 * private String getCmsName(String typCd, String brId, String custId,
	 * String dupNo, String collNo) {
	 * 
	 * String aa = collNo.substring(0, 2); String bb = collNo.substring(3, 5);
	 * 
	 * String cmsName = "";
	 * 
	 * if ("01".equals(aa)) { cmsName = "不動產(土地/建物/地上權)";
	 * 
	 * if ("01".equals(bb) || "04".equals(bb)) { List<Map<String, Object>>
	 * cmsData = misCollXXXX.getColl0102( typCd, brId, custId, dupNo, collNo);
	 * 
	 * if (cmsData != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0); String site1 = CapString.trimNull((String) data
	 * .get("SITE1")); String site2 = CapString.trimNull((String) data
	 * .get("SITE2")); String site3 = CapString.trimNull((String) data
	 * .get("SITE3")); String site4 = CapString.trimNull((String) data
	 * .get("SITE4")); String site5 = CapString.trimNull((String) data
	 * .get("SITE5")); String site6 = CapString.trimNull((String) data
	 * .get("SITE6")); String site7 = CapString.trimNull((String) data
	 * .get("SITE7")); String site8 = CapString.trimNull((String) data
	 * .get("SITE8")); String site9 = CapString.trimNull((String) data
	 * .get("SITE9")); String site10 = CapString.trimNull((String) data
	 * .get("SITE10")); String site11 = CapString.trimNull((String) data
	 * .get("SITE11")); String site12 = CapString.trimNull((String) data
	 * .get("SITE12"));
	 * 
	 * cmsName = cmsName + site1 + site2 + site3 + site4 + site5 + site6 + site7
	 * + site8 + site9 + site10 + site11 + site12; }
	 * 
	 * } else if ("02".equals(bb)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0101( typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * String site1 = CapString.trimNull((String) data .get("SITE1")); String
	 * site2 = CapString.trimNull((String) data .get("SITE2")); String site3 =
	 * CapString.trimNull((String) data .get("SITE3")); String site4 =
	 * CapString.trimNull((String) data .get("SITE4")); String immo = CapString
	 * .trimNull((String) data.get("IMMNO"));
	 * 
	 * cmsName = cmsName + site1 + site2 + site3 + "段" + site4 + "小段" + immo +
	 * "地號";
	 * 
	 * }
	 * 
	 * } else if ("03".equals(bb)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0102( typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * String immo = CapString .trimNull((String) data.get("IMMNO")); cmsName =
	 * cmsName + "建號：" + immo;
	 * 
	 * }
	 * 
	 * } } else if ("02".equals(aa)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0102(typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * if ("01".equals(bb)) { cmsName = "動產(機器設備)："; } else if ("02".equals(bb))
	 * { cmsName = "動產(工具)："; } else if ("03".equals(bb)) { cmsName = "動產(原料)：";
	 * } else if ("04".equals(bb)) { cmsName = "動產(半製品)："; } else if
	 * ("05".equals(bb)) { cmsName = "動產(成品)："; } else if ("06".equals(bb)) {
	 * cmsName = "動產(車輛)："; } else if ("07".equals(bb)) { cmsName = "動產(船舶)："; }
	 * else if ("08".equals(bb)) { cmsName = "動產(航空器)："; } else if
	 * ("09".equals(bb)) { cmsName = "動產(其他)："; } String collnm =
	 * CapString.trimNull((String) data.get("COLLNM")); cmsName = cmsName +
	 * collnm; } } else if ("03".equals(aa)) { if ("01".equals(bb)) {
	 * List<Map<String, Object>> cmsData = misCollXXXX.getColl0301( typCd, brId,
	 * custId, dupNo, collNo); String flg = ""; StringBuffer depNo = new
	 * StringBuffer(); for (Map<String, Object> data : cmsData) {
	 * depNo.append(CapString.trimNull((String) data.get("DEPNO")) + " "); flg =
	 * CapString.trimNull((String) data.get("FLG")); }
	 * 
	 * if ("1".equals(flg)) { cmsName = cmsName + "定存單，存單號碼" + depNo.toString();
	 * } else if ("2".equals(flg)) { cmsName = cmsName + "定儲存單，存單號碼" +
	 * depNo.toString(); } else if ("3".equals(flg)) { cmsName = cmsName +
	 * "可轉讓定存，存單號碼" + depNo.toString(); } else if ("4".equals(flg)) { cmsName =
	 * cmsName + "他行定存單，存單號碼" + depNo.toString(); } } else if ("02".equals(bb))
	 * { List<Map<String, Object>> cmsData = misCollXXXX.getColl0302( typCd,
	 * brId, custId, dupNo, collNo); if (cmsData != null && !cmsData.isEmpty())
	 * { Map<String, Object> data = cmsData.get(0);
	 * 
	 * String kind = CapString.trimNull((String) data.get("KIND")); if
	 * ("1".equals(kind)) { BigDecimal qnty = (BigDecimal) data.get("QNTY");
	 * cmsName = cmsName + "甲種國庫券(數量：" + qnty + ")"; } else if
	 * ("2".equals(kind)) { BigDecimal qnty = (BigDecimal) data.get("QNTY");
	 * cmsName = cmsName + "乙種國庫券(數量：" + qnty + ")"; } } } else if
	 * ("03".equals(bb)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0303( typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * String bondNm = CapString.trimNull((String) data .get("BONDNM"));
	 * BigDecimal qnty = (BigDecimal) data.get("QNTY"); cmsName = cmsName +
	 * "公債：" + bondNm + "(數量：" + qnty + ")";
	 * 
	 * } } else if ("04".equals(bb)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0304( typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * String bbondNm = CapString.trimNull((String) data .get("BBONDNM"));
	 * BigDecimal qnty = (BigDecimal) data.get("QNTY");
	 * 
	 * cmsName = cmsName + "金融債券：" + bbondNm + "(數量：" + qnty + ")"; } } else if
	 * ("05".equals(bb)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0305( typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * BigDecimal parval = (BigDecimal) data.get("PARVAL"); cmsName = cmsName +
	 * "央行儲蓄券：面額" + parval; } } else if ("06".equals(bb)) { List<Map<String,
	 * Object>> cmsData = misCollXXXX.getColl0306( typCd, brId, custId, dupNo,
	 * collNo); if (cmsData != null && !cmsData.isEmpty()) { Map<String, Object>
	 * data = cmsData.get(0);
	 * 
	 * String cbNm = CapString.trimNull((String) data.get("CBNM"));
	 * 
	 * BigDecimal qnty = (BigDecimal) data.get("QNTY"); cmsName = cmsName +
	 * "公司債：" + cbNm + "(數量：" + qnty + ")";
	 * 
	 * } } else if ("07".equals(bb)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0307( typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * String stkNm = CapString.trimNull((String) data .get("STKNM"));
	 * BigDecimal qnty = (BigDecimal) data.get("QNTY"); cmsName = cmsName +
	 * "股票：" + stkNm + "(鑑價數量：" + qnty + ")"; } } else if ("08".equals(bb)) {
	 * List<Map<String, Object>> cmsData = misCollXXXX.getColl0308( typCd, brId,
	 * custId, dupNo, collNo); if (cmsData != null && !cmsData.isEmpty()) {
	 * Map<String, Object> data = cmsData.get(0); String fundNm =
	 * CapString.trimNull((String) data .get("FUNDNM")); BigDecimal qnty =
	 * (BigDecimal) data.get("QNTY");
	 * 
	 * cmsName = cmsName + "開放行基金：：" + fundNm + "(數量：" + qnty + ")"; } } } else
	 * if ("04".equals(aa)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0401(typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * String trstNm = CapString.trimNull((String) data.get("TRSTNM"));
	 * BigDecimal qnty = (BigDecimal) data.get("QNTY"); cmsName = cmsName +
	 * "倉單：" + trstNm + "(數量：" + qnty + ")";
	 * 
	 * } } else if ("05".equals(aa)) { if ("01".equals(bb)) { List<Map<String,
	 * Object>> cmsData = misCollXXXX.getColl0501( typCd, brId, custId, dupNo,
	 * collNo); if (cmsData != null && !cmsData.isEmpty()) { Map<String, Object>
	 * data = cmsData.get(0);
	 * 
	 * String grtBkNm = CapString.trimNull((String) data .get("GRTBKNM"));
	 * String grtKind = CapString.trimNull((String) data .get("GRTKIND"));
	 * String curr = CapString.trimNull((String) data.get("CURR")); BigDecimal
	 * ogrtAmt = (BigDecimal) data.get("OGRTAMT"); cmsName = cmsName + "銀行保證：" +
	 * grtBkNm; if ("1".equals(grtKind)) { cmsName = cmsName + "(種類：借款保證　" +
	 * curr + ogrtAmt + ")"; } else if ("2".equals(grtKind)) { cmsName = cmsName
	 * + "(種類：Counter Guarantee　" + curr + ogrtAmt + ")"; } else if
	 * ("3".equals(grtKind)) { cmsName = cmsName + "(種類：其他　" + curr + ogrtAmt +
	 * ")"; } } } else if ("02".equals(bb)) { List<Map<String, Object>> cmsData
	 * = misCollXXXX.getColl0502( typCd, brId, custId, dupNo, collNo); if
	 * (cmsData != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * String grtDept = CapString.trimNull((String) data .get("GRTDEPT"));
	 * String grtKind = CapString.trimNull((String) data .get("GRTKIND"));
	 * String curr = CapString.trimNull((String) data.get("CURR")); BigDecimal
	 * ogrtAmt = (BigDecimal) data.get("OGRTAMT"); cmsName = cmsName +
	 * "政府公庫主管機關保證：" + grtDept + "，(種類：" + grtKind + " " + curr + ogrtAmt + ")";
	 * 
	 * } } else if ("03".equals(bb)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0503( typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0);
	 * 
	 * String grtNm = CapString.trimNull((String) data .get("GRTNM")); String
	 * grtItem = CapString.trimNull((String) data .get("GRTITEM"));
	 * 
	 * String curr = CapString.trimNull((String) data.get("CURR")); BigDecimal
	 * ogrtAmt = (BigDecimal) data.get("OGRTAMT");
	 * 
	 * cmsName = cmsName + "信用保證機構保證：" + grtNm + "，(項目：" + grtItem + " " + curr
	 * + ogrtAmt + ")"; } } else if ("04".equals(bb)) { List<Map<String,
	 * Object>> cmsData = misCollXXXX.getColl0504( typCd, brId, custId, dupNo,
	 * collNo); if (cmsData != null && !cmsData.isEmpty()) { Map<String, Object>
	 * data = cmsData.get(0);
	 * 
	 * String grtNm = CapString.trimNull((String) data .get("GRTNM")); String
	 * grtItem = CapString.trimNull((String) data .get("GRTITEM"));
	 * 
	 * String curr = CapString.trimNull((String) data.get("CURR")); BigDecimal
	 * ogrtAmt = (BigDecimal) data.get("OGRTAMT");
	 * 
	 * cmsName = cmsName + "母公司保證：" + grtNm + "，(項目：" + grtItem + " " + curr +
	 * ogrtAmt + ")"; } } } else if ("06".equals(aa)) { List<Map<String,
	 * Object>> cmsData = misCollXXXX.getColl0601(typCd, brId, custId, dupNo,
	 * collNo); if (cmsData != null && !cmsData.isEmpty()) { Map<String, Object>
	 * data = cmsData.get(0); String kind = CapString.trimNull((String)
	 * data.get("KIND")); if ("1".equals(kind)) { cmsName = cmsName + "額度本票："; }
	 * else if ("2".equals(kind)) { cmsName = cmsName + "備償票據："; }
	 * 
	 * String nttype = CapString.trimNull((String) data.get("NTTYPE")); String
	 * ntno = CapString.trimNull((String) data.get("NTNO")); if
	 * ("1".equals(nttype)) { cmsName = cmsName + "本票(票據號碼：" + ntno + ")"; }
	 * else if ("2".equals(nttype)) { cmsName = cmsName + "匯票(票據號碼：" + ntno +
	 * ")"; } else if ("3".equals(nttype)) { cmsName = cmsName + "支票(票據號碼：" +
	 * ntno + ")"; }
	 * 
	 * } } else if ("07".equals(aa)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0701(typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0); cmsName = cmsName + "貼現票據："; String nttype =
	 * CapString.trimNull((String) data.get("NTTYPE")); String isuNm =
	 * CapString.trimNull((String) data.get("ISUNM")); String ntno =
	 * CapString.trimNull((String) data.get("NTNO")); if ("1".equals(nttype)) {
	 * cmsName = cmsName + "本票(發票人名稱：" + isuNm + "　票據號碼：" + ntno + ")"; } else
	 * if ("2".equals(nttype)) { cmsName = cmsName + "匯票(發票人名稱：" + isuNm +
	 * "　票據號碼：" + ntno + ")"; }
	 * 
	 * } } else if ("08".equals(aa)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl08(typCd, brId, custId, dupNo, collNo); if (cmsData !=
	 * null && !cmsData.isEmpty()) { Map<String, Object> data = cmsData.get(0);
	 * if ("01".equals(bb)) { cmsName = cmsName + "信託占有(機器設備)："; } else if
	 * ("02".equals(bb)) { cmsName = cmsName + "信託占有(工具)："; } else if
	 * ("03".equals(bb)) { cmsName = cmsName + "信託占有(原料)："; } else if
	 * ("04".equals(bb)) { cmsName = cmsName + "信託占有(半製品)："; } else if
	 * ("05".equals(bb)) { cmsName = cmsName + "信託占有(成品)："; } else if
	 * ("06".equals(bb)) { cmsName = cmsName + "信託占有(車輛)："; } else if
	 * ("07".equals(bb)) { cmsName = cmsName + "信託占有(船舶)："; } else if
	 * ("08".equals(bb)) { cmsName = cmsName + "信託占有(航空器)："; } else if
	 * ("09".equals(bb)) { cmsName = cmsName + "信託占有(其他)："; } String collNm =
	 * CapString.trimNull((String) data.get("COLLNM")); BigDecimal qnty =
	 * (BigDecimal) data.get("QNTY"); String unit = CapString.trimNull((String)
	 * data.get("UNIT")); cmsName = cmsName + collNm + " " + qnty + unit; } }
	 * else if ("09".equals(aa)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl0901(typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0); String collNm = CapString.trimNull((String)
	 * data.get("COLLNM")); BigDecimal qnty = (BigDecimal) data.get("QNTY");
	 * String unit = CapString.trimNull((String) data.get("UNIT")); cmsName =
	 * cmsName + "參貸他行：" + collNm + " " + qnty + unit; } } else if
	 * ("10".equals(aa)) { List<Map<String, Object>> cmsData =
	 * misCollXXXX.getColl1001(typCd, brId, custId, dupNo, collNo); if (cmsData
	 * != null && !cmsData.isEmpty()) { Map<String, Object> data =
	 * cmsData.get(0); String collNm = CapString.trimNull((String)
	 * data.get("COLLNM")); BigDecimal qnty = (BigDecimal) data.get("QNTY");
	 * String unit = CapString.trimNull((String) data.get("UNIT")); cmsName =
	 * cmsName + "其他擔保品：" + collNm + " " + qnty + unit; } }
	 * 
	 * return cmsName; }
	 * 
	 * private String getOwnerName(String typCd, String brId, String custId,
	 * String dupNo, String collNo, String collSn) { List<Map<String, Object>>
	 * ownerData = misELF369Service.getELF369Data( typCd, brId, custId, dupNo,
	 * collNo, collSn); int size = 0; StringBuffer ownerName = new
	 * StringBuffer(); if (ownerData != null && !ownerData.isEmpty()) { size =
	 * ownerData.size(); for (int i = 0; i < ownerData.size(); i++) {
	 * Map<String, Object> data = ownerData.get(i); String name =
	 * CapString.trimNull((String) data.get("OWNERNM")); ownerName.append(name);
	 * if (i == 4) { break; } if (size > 1) { ownerName.append("、"); } }
	 * 
	 * if (size > 4) { ownerName.append("...等" + size + "人 "); } } return
	 * ownerName.toString(); }
	 * 
	 * private String getCollInsData(String custId, String duNo, String collNo)
	 * { IBranch branch =
	 * branchService.getBranch(MegaSSOSecurityContext.getUnitNo());
	 * 
	 * String useSWFT = branch.getUseSWFT();
	 * 
	 * 
	 * 
	 * Map<String, String> insuranceKind = new HashMap<String, String>();
	 * insuranceKind.put("01", "火險"); insuranceKind.put("02", "地震險");
	 * insuranceKind.put("03", "颱風險"); insuranceKind.put("04", "財產綜合損失險");
	 * insuranceKind.put("05", "營建工程綜合損失險"); insuranceKind.put("12", "其他保險");
	 * Map<String, BigDecimal> insuranceAmt = new HashMap<String, BigDecimal>();
	 * 
	 * 
	 * 
	 * 
	 * List<Map<String, Object>> collInsData = misELF347Service.getELF347Data(
	 * custId, duNo, collNo);
	 * 
	 * for (Map<String, Object> data : collInsData) { String inskind1 =
	 * CapString.trimNull((String) data.get("INSKND1")); String inskind2 =
	 * CapString.trimNull((String) data.get("INSKND2")); String inskind3 =
	 * CapString.trimNull((String) data.get("INSKND3")); String insNm =
	 * CapString.trimNull((String) data.get("INSNM")); String insbDt =
	 * CapString.trimNull((String) data.get("INSBDT")); String inseDt =
	 * CapString.trimNull((String) data.get("INSEDT"));
	 * 
	 * if (!"".equals(inskind1) && "".equals(inskind2) && "".equals(inskind3) &&
	 * "".equals(insNm)) { if (insuranceKind.containsKey(inskind1)) {
	 * 
	 * } } else if (!"".equals(inskind2) || !"".equals(inskind3) ||
	 * !"".equals(insNm)) {
	 * 
	 * }
	 * 
	 * } return null; }
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkLoanNo_house(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONArray custIDs = JSONArray.fromObject(params.getString("custIDs"));
		String innerAudit = params.getString("innerAudit");

		// 比照 addNew(...)
		String ownBrId = ("Y".equals(innerAudit) ? user.getUnitNo() : params
				.getString("brId"));

		List<String> msg_list = new ArrayList<String>();
		if (Util.isNotEmpty(ownBrId)) {
			for (Object data : custIDs) {

				JSONObject d = (JSONObject) data;

				String custId_dupNo = d.getString("custid").toUpperCase();
				String custId = custId_dupNo.substring(0,
						custId_dupNo.length() - 1);
				String dupNo = custId_dupNo.substring(
						custId_dupNo.length() - 1, custId_dupNo.length())
						.toUpperCase();
				String custName = d.getString("name");
				// ==========
				/*
				 * 可能同時含房貸、透支 ●全房貸, 應出現 ●房貸、透支, 應出現 ●全透支 → 無房貸資料
				 */

				// 以下是 LMS1925M01FormHandler :: includeData (授信業務) 的 method
				if (true) {
					Set<String> loanNo_collection = new HashSet<String>();
					if (at_least_one_house_loanNo(loanNo_collection) == false) {
						// 國內放款資料-非聯貸
						List<Map<String, Object>> lnf150data = misLNF150Service
								.findByCustIdDup(custId, dupNo, ownBrId);
						for (Map<String, Object> map : lnf150data) {
							String loanNo = Util.trim(MapUtils.getString(map,
									"LOANNO"));

							loanNo_collection.add(loanNo + "-Part1");
						}
					}
					if (at_least_one_house_loanNo(loanNo_collection) == false) {
						// 國內放款資料-聯貸
						List<Map<String, Object>> lnf150data2 = misLNF150Service
								.findByCustIdDup2(custId, dupNo, ownBrId);
						for (Map<String, Object> map : lnf150data2) {
							String loanNo = Util.trim(MapUtils.getString(map,
									"LNF024_LOAN_NO_M"));

							loanNo_collection.add(loanNo + "-Part2");
						}
					}
					if (at_least_one_house_loanNo(loanNo_collection) == false) {
						List<Map<String, Object>> asLndAvgOvsData = dwAslndavgovsService
								.getByCustIdBrNo(custId
										+ ("0".equals(dupNo) ? " " : dupNo),
										ownBrId);
						for (Map<String, Object> asLndAvgOvs : asLndAvgOvsData) {
							String accNo = Util.trim(MapUtils.getString(
									asLndAvgOvs, "ACCT_KEY"));// 帳號
							loanNo_collection.add(accNo + "-Part3");
						}
					}

					if (at_least_one_house_loanNo(loanNo_collection) == false) {
						String lnapInfo = StringUtils.join(
								get_LNAP(loanNo_collection), ",");
						String lnapStr = "";
						if (Util.isNotEmpty(lnapInfo)) {
							lnapStr = "[" + lnapInfo + "] ";
						}
						msg_list.add(custId + "-" + dupNo + " " + custName
								+ " 放款科目" + lnapStr + "不含房貸科目");
					}
				}
			}

			if (msg_list.size() > 0) {
				msg_list.add("請在【授信業務工作底稿】處理!");
			}
		}

		String msg = StringUtils.join(msg_list, "<br/>");
		if (Util.isNotEmpty(msg)) {
			result.set("msg", msg);
		}
		return result;
	}
	
	// J-111-0011 新增於eloan授信管理系統(906稽核處)稽核工作底稿，增列「已核准額度明細表及案件簽報書」功能鍵
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult resultRpt = new CapAjaxFormResult();

		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		Properties rptProperties = MessageBundleScriptCreator.getComponentResource(LMS1935V01Page.class);
		// 前端來的可能一位借款人
		String oid = Util.trim(params.getString("oid"));
		L120M01A l120m01a = null;// 該借款人的最新一筆簽報書
		List<L120S01B> l120s01bList = new ArrayList<L120S01B>();
		Map<String, Object> dataL120m01a = new HashMap<String, Object>();;// for簽報書
		
		// 一位一位借款人的資料做處理
		if (Util.isNotEmpty(oid)) {
			// L192M01A.OID ->該位借款人最近一筆已核准的簽報書+額度明細/額度批覆
			List<Map<String, Object>> dataList = lms1935Service.findPrintL140M01AByOidForLMS1935(oid);
			
			if(dataList == null || dataList.isEmpty()){
				// EFD0036=INFO|查無資料!|
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
			
			if (dataList != null && !dataList.isEmpty()) {
				boolean addL120M01ARpt = false;
				Map<String, Object> data = null;// for迴圈每一圈的額度明細
				
				for (Map<String, Object> dataMap : dataList) {
					
					// form SQL的簽報書、額度明細表的值
					String lms120m01aMainId = Util.trim(MapUtils.getString(
							dataMap, "LMS120M01A_MAINID"));
					String l140m01aOid = Util.trim(MapUtils.getString(
							dataMap, "OID"));
					String custId = Util.trim(MapUtils.getString(
							dataMap, "CUSTID"));
					String dupNo = Util.trim(MapUtils.getString(
							dataMap, "DUPNO"));
					String custName = Util.trim(MapUtils.getString(
							dataMap, "CUSTNAME"));
					String cntrno = Util.trim(MapUtils.getString(
							dataMap, "CNTRNO"));
					
					String isheadcheck = Util.trim(MapUtils
							.getString(dataMap, "ISHEADCHECK"));
					
					if(l120m01a == null){
						// 因為都是同一筆簽報書的資料，取一次就好
						l120m01a = lmsService.findModelByMainId(L120M01A.class, lms120m01aMainId);
						l120s01bList = lms1935Service.findL120s01bByMainId(lms120m01aMainId);
					}
					
					if(l120m01a == null){
						// EFD0036=INFO|查無資料!|
						// 查無資料
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
					}
					if (TypCdEnum.海外.getCode().equals(l120m01a.getTypCd())) {
						// ----------------海外開始----------------
						// 海外企消金都走同一支
						// 先加入一筆簽報書的RPT，如果有加過了就不會再跑進來
						if(!addL120M01ARpt){
							addL120M01ARpt = true;// 改成已加過簽報書!!
							if ("2".equals(l120m01a.getDocCode())
									|| "3".equals(l120m01a.getDocCode())
									|| "4".equals(l120m01a.getDocCode())) {
								data = new HashMap<String, Object>();
								data.put("custName", Util.nullToSpace(l120m01a.getCustName())
										+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
										+ Util.nullToSpace(l120m01a.getDupNo()));
								data.put("cntrNo", "");
								data.put("oid", Util.nullToSpace(l120m01a.getOid()));
								if ("2".equals(l120m01a.getDocCode())) {
									data.put("rptName",
											rptProperties.getProperty("TITLE.RPTNAME6"));
								}
								if ("3".equals(l120m01a.getDocCode())) {
									data.put("rptName",
											rptProperties.getProperty("TITLE.RPTNAME7"));
								}
								if ("2".equals(l120m01a.getDocKind())) {
									data.put("rptNo", "LMS1205R08");
									data.put("rpt", "R08");
								} else {
									data.put("rptNo", "LMS1205R09");
									data.put("rpt", "R09");
								}
								dataL120m01a = data;
							} else if ("1".equals(l120m01a.getDocType())) {
								data = new HashMap<String, Object>();
								data.put("custName", Util.nullToSpace(l120m01a.getCustName())
										+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
										+ Util.nullToSpace(l120m01a.getDupNo()));
								data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME1"));
								data.put("cntrNo", "");
								if ("2".equals(Util.nullToSpace(l120m01a.getDocKind()))) {
									data.put("rptNo", "LMS1205R01");
									data.put("rpt", "R01");
								} else {
									boolean result = false;
									for (L120S01B l120s01b : l120s01bList) {
										if (Util.nullToSpace(l120m01a.getCustId()).equals(
												l120s01b.getCustId())
												&& Util.nullToSpace(l120m01a.getDupNo())
												.equals(l120s01b.getDupNo())) {
											if ("1".equals(l120s01b.getInvMFlag())) {
												result = true;
											}
										}
									}
									if (result) {
										data.put("rptNo", "LMS1205R06");
										data.put("rpt", "R06");
									} else {
										data.put("rptNo", "LMS1205R05");
										data.put("rpt", "R05");
									}
								}
								data.put("oid", Util.nullToSpace(l120m01a.getOid()));
								dataL120m01a = data;
							} else if ("2".equals(l120m01a.getDocType())) {
								data = new HashMap<String, Object>();
								data.put("custName", Util.nullToSpace(l120m01a.getCustName())
										+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
										+ Util.nullToSpace(l120m01a.getDupNo()));
								data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME2"));
								data.put("cntrNo", "");
								if ("1".equals(Util.nullToSpace(l120m01a.getDocKind()))) {
									data.put("rptNo", "LMS1205R11");
									data.put("rpt", "R11");
								} else {
									data.put("rptNo", "LMS1205R10");
									data.put("rpt", "R10");
								}
								data.put("oid", Util.nullToSpace(l120m01a.getOid()));
								dataL120m01a = data;
							}
						}							
						
						// 當 L120M01A.docCode in ('3','4') ...不顯示「額度明細表」項目及「額度批附表」項目
						// L140M01A．額度明細表主檔
						// 4.1.13_資本適足率影響數
						if (!("3".equals(l120m01a.getDocCode()) || "4".equals(l120m01a
								.getDocCode()))) {
							// L140M01A．額度明細表主檔
							data = new HashMap<String, Object>();
							data.put("custName", custId + " " + dupNo + " " + custName);
							data.put("custId", custId);
							data.put("dupNo", dupNo);
							data.put("rptName",
									Util.equals("Y", isheadcheck) ? rptProperties.getProperty("TITLE.RPTNAME9") : 
										rptProperties.getProperty("TITLE.RPTNAME8"));
							data.put("cntrNo", Util.nullToSpace(cntrno));
							data.put("oid", Util.nullToSpace(l140m01aOid));
							data.put("rpt", Util.equals("Y", isheadcheck) ? "R13" : "R12");
							data.put("rptNo", Util.equals("Y", isheadcheck) ? "LMS1405R02" : "LMS1405R01");
							beanList.add(data);
						}
						// ----------------海外結束----------------
					} else {
						// ----------------國內開始----------------
						if(UtilConstants.Casedoc.DocType.個金.equals(l120m01a.getDocType())){
							// ----------------國內個金開始----------------
							
							if(!addL120M01ARpt){
								addL120M01ARpt = true;// 改成已加過簽報書!!
								
								boolean l120m01a_simplifyFlag_A = false;
								if(Util.equals(UtilConstants.Casedoc.SimplifyFlag.一般消金, l120m01a.getSimplifyFlag())){
									l120m01a_simplifyFlag_A = true;
								}
								// 其他類型
								if ("2".equals(l120m01a.getDocCode())
										|| "3".equals(l120m01a.getDocCode())
										|| "4".equals(l120m01a.getDocCode())
										|| "5".equals(l120m01a.getDocCode())) {
									data = new HashMap<String, Object>();
									data.put("custName", Util.nullToSpace(l120m01a.getCustName())
											+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
											+ Util.nullToSpace(l120m01a.getDupNo()));
									data.put("cntrNo", "");
									data.put("oid", Util.nullToSpace(l120m01a.getOid()));
									if ("2".equals(l120m01a.getDocCode())
											|| "5".equals(l120m01a.getDocCode())) {
										data.put("rptName",	rptProperties.getProperty("TITLE.RPTNAME6"));
									}
									if ("3".equals(l120m01a.getDocCode())) {
										data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME7"));
									}
									
									if ("2".equals(l120m01a.getDocKind())) {
										data.put("rptNo", "CLS1201R08");
										data.put("rpt", "R08");
									} else {
										data.put("rptNo", "CLS1201R09");
										data.put("rpt", "R09");
									}
									dataL120m01a = data;
								} else if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a
										.getDocType())) {
									data = new HashMap<String, Object>();
									data.put("custName", Util.nullToSpace(l120m01a.getCustName())
											+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
											+ Util.nullToSpace(l120m01a.getDupNo()));
									data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME2"));
									data.put("cntrNo", "");
									
									boolean l120m01a_simplifyFlag_B = false;
									boolean l120m01a_simplifyFlag_C = false;
									boolean l120m01a_simplifyFlag_D = false;
									if(Util.equals(UtilConstants.Casedoc.SimplifyFlag.卡友信貸, l120m01a.getSimplifyFlag())){
										String fmtstr_html = build_html_SimplifyFlag_B(l120m01a);
										if(Util.isNotEmpty(fmtstr_html)){
											l120m01a_simplifyFlag_B = true;
										}else{
											//照舊
										}
									}else if(Util.equals(UtilConstants.Casedoc.SimplifyFlag.勞工紓困, l120m01a.getSimplifyFlag())){
										String fmtstr_html = build_html_SimplifyFlag_C(l120m01a);
										if(Util.isNotEmpty(fmtstr_html)){
											l120m01a_simplifyFlag_C = true;
										}else{
											//照舊
										}
									}else if(Util.equals(UtilConstants.Casedoc.SimplifyFlag.歡喜信貸, l120m01a.getSimplifyFlag())){
										String fmtstr_html = build_html_SimplifyFlag_D(l120m01a);
										if(Util.isNotEmpty(fmtstr_html)){
											l120m01a_simplifyFlag_D = true;
										}else{
											//照舊
										}
									}
									
									if (UtilConstants.Casedoc.DocKind.授權內.equals(Util.trim(l120m01a.getDocKind()))) {  //分行授權內、營運中心授權內
										if(l120m01a_simplifyFlag_A){
											data.put("rptNo", "CLS1141R11A");
											data.put("rpt", "R11A");
										} else if(l120m01a_simplifyFlag_B){
											data.put("rptNo", "CLS1141R11B");
											data.put("rpt", "R11B");
										} else if(l120m01a_simplifyFlag_C){
											data.put("rptNo", "CLS1141R11C");
											data.put("rpt", "R11C");
										} else if(l120m01a_simplifyFlag_D){
											data.put("rptNo", "CLS1141R11D");
											data.put("rpt", "R11D");
										} else{
											data.put("rptNo", "CLS1141R11");
											data.put("rpt", "R11");
										}
									} else {
										if(l120m01a_simplifyFlag_A){
											data.put("rptNo", "CLS1141R10A");
											data.put("rpt", "R10A");
										} else if(l120m01a_simplifyFlag_B){
											data.put("rptNo", "CLS1141R10B");
											data.put("rpt", "R10B");
										} else if(l120m01a_simplifyFlag_C){
											data.put("rptNo", "CLS1141R10C");
											data.put("rpt", "R10C");
										} else if(l120m01a_simplifyFlag_B){
											data.put("rptNo", "CLS1141R10D");
											data.put("rpt", "R10D");
										} else{
											data.put("rptNo", "CLS1141R10");
											data.put("rpt", "R10");
										}
									}
									data.put("oid", Util.nullToSpace(l120m01a.getOid()));
									dataL120m01a = data;
								}
							}
							
							// 當 L120M01A.docCode in ('3','4') ...不顯示「額度明細表」項目及「額度批附表」項目
							// L140M01A．額度明細表主檔
							if (!("3".equals(l120m01a.getDocCode()) || "4".equals(l120m01a
									.getDocCode()))) {
								// L140M01A．額度明細表主檔
								data = new HashMap<String, Object>();
								data.put("custName", custName + " "	+ custId + " " + dupNo);
								data.put("custId", custId);
								data.put("dupNo", dupNo);
								data.put("rptName",Util.equals("Y", isheadcheck) ? rptProperties.getProperty("TITLE.RPTNAME9") : 
									rptProperties.getProperty("TITLE.RPTNAME8"));
								data.put("cntrNo", Util.nullToSpace(cntrno));
								data.put("oid", Util.nullToSpace(l140m01aOid));
								data.put("rpt", Util.equals("Y", isheadcheck) ? "R13" : "R12");
								data.put("rptNo", Util.equals("Y", isheadcheck) ? "CLS1151R02" : "CLS1151R01");
								beanList.add(data);
							}
							// ----------------國內個金結束----------------
						}else{
							// ----------------國內企金開始----------------
							// 先加入一筆簽報書的RPT，如果有加過了就不會再跑進來
							if(!addL120M01ARpt){
								addL120M01ARpt = true;// 改成已加過簽報書!!
								
								if ("2".equals(l120m01a.getDocCode())
										|| "3".equals(l120m01a.getDocCode())
										|| "4".equals(l120m01a.getDocCode())) {
									data = new HashMap<String, Object>();
									data.put("custName", Util.nullToSpace(l120m01a.getCustId())
											+ " " + Util.nullToSpace(l120m01a.getDupNo()) + " "
											+ Util.nullToSpace(l120m01a.getCustName()));
									data.put("cntrNo", "");
									data.put("oid", Util.nullToSpace(l120m01a.getOid()));
									if ("2".equals(l120m01a.getDocCode())) {
										data.put("rptName",
												rptProperties.getProperty("TITLE.RPTNAME6"));
									}
									if ("3".equals(l120m01a.getDocCode())) {
										data.put("rptName",
												rptProperties.getProperty("TITLE.RPTNAME7"));
									}
									if ("2".equals(l120m01a.getDocKind())) {
										data.put("rptNo", "LMS1205R08");
										data.put("rpt", "R08");
									} else {
										data.put("rptNo", "LMS1205R09");
										data.put("rpt", "R09");
									}
									dataL120m01a = data;
								} else if ("1".equals(l120m01a.getDocType())) {
									data = new HashMap<String, Object>();
									data.put("custName", Util.nullToSpace(l120m01a.getCustId())
											+ " " + Util.nullToSpace(l120m01a.getDupNo()) + " "
											+ Util.nullToSpace(l120m01a.getCustName()));
									data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME1"));
									data.put("cntrNo", "");
									if ("2".equals(Util.nullToSpace(l120m01a.getDocKind()))) {
										data.put("rptNo", "LMS1205R01");
										data.put("rpt", "R01");
									} else {
										// J-108-0243 微型企業
										if (Util.equals("Y",
												Util.nullToSpace(l120m01a.getMiniFlag()))
												&& !lmsService.hidePanelbyCaseType_004(l120m01a)) {
											// 國發基金協助新創事業紓困融資加碼方案 2600萬要印一般授權內簽報書
											data.put("rptNo", "LMS1201R37");
											data.put("rpt", "R37");
										} else {
											boolean result = false;
											for (L120S01B l120s01b : l120s01bList) {
												if (Util.nullToSpace(l120m01a.getCustId()).equals(
														l120s01b.getCustId())
														&& Util.nullToSpace(l120m01a.getDupNo())
														.equals(l120s01b.getDupNo())) {
													if ("1".equals(l120s01b.getInvMFlag())) {
														result = true;
													}
												}
											}
											if (result) {
												data.put("rptNo", "LMS1205R06");
												data.put("rpt", "R06");
											} else {
												data.put("rptNo", "LMS1205R05");
												data.put("rpt", "R05");
											}
										}
									}
									data.put("oid", Util.nullToSpace(l120m01a.getOid()));
									dataL120m01a = data;
								} else if ("2".equals(l120m01a.getDocType())) {
									data = new HashMap<String, Object>();
									data.put("custName", Util.nullToSpace(l120m01a.getCustId())
											+ " " + Util.nullToSpace(l120m01a.getDupNo()) + " "
											+ Util.nullToSpace(l120m01a.getCustName()));
									data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME2"));
									data.put("cntrNo", "");
									if ("1".equals(Util.nullToSpace(l120m01a.getDocKind()))) {
										data.put("rptNo", "LMS1205R11");
										data.put("rpt", "R11");
									} else {
										data.put("rptNo", "LMS1205R10");
										data.put("rpt", "R10");
									}
									data.put("oid", Util.nullToSpace(l120m01a.getOid()));
									dataL120m01a = data;
								}
							}
							
							// 開始加額度明細/批覆
							// 當 L120M01A.docCode in ('3','4') ...不顯示「額度明細表」項目及「額度批附表」項目
							// L140M01A．額度明細表主檔
							if (!("3".equals(l120m01a.getDocCode()) || "4".equals(l120m01a
									.getDocCode()))) {
								// L140M01A．額度明細表主檔
								data = new HashMap<String, Object>();
								data.put("custName", custId + " " + dupNo + " " + custName);
								data.put("custId", custId);
								data.put("dupNo", dupNo);
								data.put("rptName",
										Util.equals("Y", isheadcheck) ? rptProperties.getProperty("TITLE.RPTNAME9") : 
											rptProperties.getProperty("TITLE.RPTNAME8"));
								data.put("cntrNo", Util.nullToSpace(cntrno));
								data.put("oid", Util.nullToSpace(l140m01aOid));
								data.put("rpt", Util.equals("Y", isheadcheck) ? "R13" : "R12");
								data.put("rptNo", Util.equals("Y", isheadcheck) ? "LMS1401R02" : "LMS1401R01");
								beanList.add(data);
							}
							
							// ----------------國內企金結束----------------
						}
						// ----------------國內開始----------------
					}
					
					
				}// for迴圈結束
			}
			
			
			// 共用區塊~~~~~~
			resultRpt.set("mainId", l120m01a.getMainId());// 自己放簽報書的mainId
			if(TypCdEnum.海外.getCode().equals(l120m01a.getTypCd())){
				resultRpt.set("rptService", "lms1205r01rptservice");// 海外rptservice
			}else{
				resultRpt.set("rptService", UtilConstants.Casedoc.DocType.個金.equals(l120m01a.getDocType())
						? "cls1141r01rptservice" : "lms1201r01rptservice");// rptservice
			}
			
			List<String> l140m01aStringList = new ArrayList<String>();// 裝額度明細的組裝後字串
			if(!dataL120m01a.isEmpty()){
				// oid_arr.push(data.rpt+"^"+data.oid+"^"+data.custId+"^"+data.dupNo+"^"+data.cntrNo );
				// 組裝簽報書
				resultRpt.set("L120M01ARpt", dataL120m01a.get("rpt") +"^"+ dataL120m01a.get("oid") + "^" + 
						dataL120m01a.get("custId") + "^" + dataL120m01a.get("dupNo") + "^" + dataL120m01a.get("cntrNo") );
				
				//組裝額度明細
				for(Map<String, Object> data : beanList){
					l140m01aStringList.add(data.get("rpt") +"^"+ data.get("oid") + "^" + 
							data.get("custId") + "^" + data.get("dupNo") + "^" + data.get("cntrNo") );
				}
				resultRpt.set("L140M01ARpt", StringUtils.join( l140m01aStringList, "\\|"));
			}
		} else {
			// EFD0036=INFO|查無資料!|
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}
		
		return resultRpt;
	}
	
	
	private String build_html_SimplifyFlag_B(L120M01A l120m01a){
		List<L120S12A> model_list = clsService.findL120S12A(l120m01a.getMainId());
		List<L120S12A> choose_list = new ArrayList<L120S12A>();
		for(L120S12A l120s12a : model_list){
			if(Util.isNotEmpty(l120s12a.getRefMainId()) && Util.isNotEmpty(Util.trim(l120s12a.getRefSeq()))){
				choose_list.add(l120s12a);
			}else{
				continue; //空的
			}
			
		}
		if(choose_list.size()==0){
			return "";
		}
		
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1935V01Page.class);
		String item_match = prop.getProperty("item.match");
		String item_notMatch = prop.getProperty("item.notMatch");
		String html_table_class = "fmtTbl";
		String html_noBorderTbl_class = "noBorderTbl";
		String html_table_style_width = " width:18.1cm; ";
		String html_noBorderTbl_style_width = ""; //" width:9cm; ";
		String html_noBorderTbl_c1_style_width = " width:0.8cm; "; 
		String html_noBorderTbl_c2_style_width = " width:3.0cm; ";
		String html_noBorderTbl_c3_style_width = " width:1.5cm; ";
		String html_noBorderTbl_c4_style_width = " width:9.0cm; "; //為了第 (2),(5) 折行
		
		String td_empty = "<td>&nbsp;</td>";
		StringBuffer sb = new StringBuffer();
		for(L120S12A l120s12a: choose_list ){
			L140M01A l140m01a = clsService.findL140M01A_mainId(l120s12a.getRefMainId());
			L140S02A l140s02a = clsService.findL140S02A(l120s12a.getRefMainId(), l120s12a.getRefSeq());
			//
			Map<String, String> map_formData = get_l120s12a_formData(l120m01a, l140m01a, l140s02a);
			sb.append("<table class='"+html_table_class+"' style='margin-top:0px; "+html_table_style_width+"' >");
			sb.append("<tr>");
			sb.append("<td colspan='2'>");
			sb.append(prop.getProperty("header_basicData")); //header_basicData=基本資料
			sb.append("</td>");
			sb.append("</tr>");
			if(true){
				_l120s12a_append_sb_column2(sb
						, prop.getProperty("custName")+"："+MapUtils.getString(map_formData, "l120s12a_custName")
						, prop.getProperty("occupation")+"："+Util.trim(l120s12a.getOccupation()));
				_l120s12a_append_sb_column2(sb
						, prop.getProperty("birthday")+"："+MapUtils.getString(map_formData, "l120s12a_birthday")
						, prop.getProperty("seniority")+"："+MapUtils.getString(map_formData, "l120s12a_seniority"));
				if(true){
					sb.append("<tr>");
					sb.append("<td colspan='2' >").append(prop.getProperty("address")+"："+MapUtils.getString(map_formData, "l120s12a_address")).append("</td>");
					sb.append("</tr>");
				}
				_l120s12a_append_sb_column2(sb
						, prop.getProperty("applyAmt")+"：TWD "+NumConverter.addComma(LMSUtil.pretty_numStr(l120s12a.getApplyAmt()))+prop.getProperty("curr.unit") 
						, prop.getProperty("yearIncome")+"："+MapUtils.getString(map_formData, "l120s12a_yearIncome"));
				_l120s12a_append_sb_column2(sb
						, prop.getProperty("lnYearMonth")+"："+MapUtils.getString(map_formData, "l120s12a_lnYearMonth")						
						, prop.getProperty("l120m01a_resource")+"： "+MapUtils.getString(map_formData, "l120s12a_l120m01a_resource"));
				_l120s12a_append_sb_column2(sb
						, prop.getProperty("l120m01a_purpose")+"："+MapUtils.getString(map_formData, "l120s12a_l120m01a_purpose")						
						, prop.getProperty("drate")+"： "+MapUtils.getString(map_formData, "l120s12a_drate")+"&nbsp;&nbsp;"+prop.getProperty("see_attch"));
				_l120s12a_append_sb_column2(sb
						, prop.getProperty("l140m01r_desc")+"："+MapUtils.getString(map_formData, "l120s12a_l140m01r_desc")						
						, prop.getProperty("guarantor_name")+"： "+MapUtils.getString(map_formData, "l120s12a_guarantor_name"));
			}
			sb.append("</table>");
			//======================			
			sb.append("<table class='"+html_table_class+"' style='margin-top:6px; "+html_table_style_width+"' >");
			sb.append("<td colspan='3'>");
			sb.append(prop.getProperty("header_chkItem")); //header_chkItem=審核項目
			sb.append("</td>");
			
			_l120s12a_append_sb_column3(sb, "1."+prop.getProperty("item010.desc"), _l120s12a_itemval_Y(l120s12a.getItem010(), item_match), _l120s12a_itemval_N(l120s12a.getItem010(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "2."+prop.getProperty("item020.desc"), _l120s12a_itemval_Y(l120s12a.getItem020(), item_match), _l120s12a_itemval_N(l120s12a.getItem020(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "3."+prop.getProperty("item030.desc"), _l120s12a_itemval_Y(l120s12a.getItem030(), item_match), _l120s12a_itemval_N(l120s12a.getItem030(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "4."+prop.getProperty("item040.desc")+prop.getProperty("item040.desc1"), _l120s12a_itemval_Y(l120s12a.getItem040(), item_match), _l120s12a_itemval_N(l120s12a.getItem040(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "5."+prop.getProperty("item050.desc"), _l120s12a_itemval_Y(l120s12a.getItem050(), item_match)
						+prop.getProperty("grade.descA")+Util.trim(l120s12a.getGrade1())+prop.getProperty("grade.descB")
						, _l120s12a_itemval_N(l120s12a.getItem050(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "6."+prop.getProperty("item060.desc"), _l120s12a_itemval_Y(l120s12a.getItem060(), item_match), _l120s12a_itemval_N(l120s12a.getItem060(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "7."+prop.getProperty("item070.desc"), _l120s12a_itemval_Y(l120s12a.getItem070(), item_match), _l120s12a_itemval_N(l120s12a.getItem070(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "8."+prop.getProperty("item080.desc"), _l120s12a_itemval_Y(l120s12a.getItem080(), item_match), _l120s12a_itemval_N(l120s12a.getItem080(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "9."+prop.getProperty("item090.desc"), _l120s12a_itemval_Y(l120s12a.getItem090(), item_match), _l120s12a_itemval_N(l120s12a.getItem090(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "10."+prop.getProperty("item100.desc"), _l120s12a_itemval_Y(l120s12a.getItem100(), item_match), _l120s12a_itemval_N(l120s12a.getItem100(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "11."+prop.getProperty("item110.desc"), _l120s12a_itemval_Y(l120s12a.getItem110(), item_match), _l120s12a_itemval_N(l120s12a.getItem110(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "12."+prop.getProperty("item120.desc")+prop.getProperty("item120.desc1"), _l120s12a_itemval_Y(l120s12a.getItem120(), item_match), _l120s12a_itemval_N(l120s12a.getItem120(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "13."+prop.getProperty("item130.desc"), _l120s12a_itemval_Y(l120s12a.getItem130(), item_match), _l120s12a_itemval_N(l120s12a.getItem130(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "14."+prop.getProperty("item150.desc")+prop.getProperty("item150.desc1"), _l120s12a_itemval_Y(l120s12a.getItem150(), item_match), _l120s12a_itemval_N(l120s12a.getItem150(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "15."+prop.getProperty("item160.desc"), _l120s12a_itemval_Y(l120s12a.getItem160(), item_match), _l120s12a_itemval_N(l120s12a.getItem160(), item_notMatch) );
			_l120s12a_append_sb_column3(sb, "16."+prop.getProperty("item170.desc"), _l120s12a_itemval_Y(l120s12a.getItem170(), item_match), _l120s12a_itemval_N(l120s12a.getItem170(), item_notMatch) );
			if(true){
				sb.append("<tr>");
				sb.append("<td>").append("17."+prop.getProperty("item140.descA")).append("&nbsp;").append(MapUtils.getString(map_formData, "l120s12a_approveAmt")).append(prop.getProperty("item140.descB"));
				if(true){ //第14項之下包含5個細項
					/*  
					 // 在 span 指定 width:50px; display: inline-block; 看到的垂直對齊不一致 => 改用 table
					String span_dbr_style = " style='width:50px; display: inline-block; text-align:right;' ";
					sb.append(UtilConstants.Mark.HTMLBR).append("(1)").append(prop.getProperty("item140.desc1"));
					sb.append(UtilConstants.Mark.HTMLBR).append("(2)").append(prop.getProperty("item140.desc2A")).append("&nbsp;").append(NumConverter.addComma(LMSUtil.pretty_numStr(l120s12a.getMonthAmtA()))).append(prop.getProperty("curr.unit"))
						.append(prop.getProperty("item140.desc2B"))
						.append(prop.getProperty("item140.desc2C")).append("&nbsp;").append(NumConverter.addComma(LMSUtil.pretty_numStr(l120s12a.getMonthAmtB()))).append(prop.getProperty("curr.unit"));
					sb.append(UtilConstants.Mark.HTMLBR).append("(3)").append(prop.getProperty("item140.desc31A")).append("<span "+span_dbr_style+" >").append(LMSUtil.pretty_numStr(l120s12a.getDbr22A())).append("</span>").append(prop.getProperty("item140.desc31B"));
					sb.append(UtilConstants.Mark.HTMLBR).append("&nbsp;&nbsp;&nbsp;").append(prop.getProperty("item140.desc32A")).append("<span "+span_dbr_style+" >").append(LMSUtil.pretty_numStr(l120s12a.getDbr22B())).append("</span>").append(prop.getProperty("item140.desc32B"));
					sb.append(UtilConstants.Mark.HTMLBR).append("(4)").append(prop.getProperty("item140.desc4"));
					sb.append(UtilConstants.Mark.HTMLBR).append("(5)").append(prop.getProperty("item140.desc5"));
					*/
					sb.append("<table class='"+html_noBorderTbl_class+"'  style='"+html_noBorderTbl_style_width+"'>");
					sb.append("<tr><td>").append("(1)").append("</td><td colspan='3'>").append(prop.getProperty("item140.desc1")).append("</td>"+td_empty+"</tr>");
					sb.append("<tr><td>").append("(2)").append("</td><td colspan='3'>").append(prop.getProperty("item140.desc2A")+"&nbsp;"+NumConverter.addComma(LMSUtil.pretty_numStr(l120s12a.getMonthAmtA()))+prop.getProperty("curr.unit"));
					sb.append(prop.getProperty("item140.desc2B"))
						.append(prop.getProperty("item140.desc2C")).append("&nbsp;").append(NumConverter.addComma(LMSUtil.pretty_numStr(l120s12a.getMonthAmtB()))).append(prop.getProperty("curr.unit"))
						.append("</td>"+td_empty+"</tr>");
					
					if(l120s12a.getDbr22A()!=null && l120s12a.getDbr22B()==null){
						sb.append("<tr><td style='"+html_noBorderTbl_c1_style_width+"' >").append("(3)").append("</td>")
						.append("<td style='"+html_noBorderTbl_c2_style_width+"' >").append(prop.getProperty("item140.desc31A")).append("</td>")
						.append("<td align='right' style='"+html_noBorderTbl_c3_style_width+"' >").append(LMSUtil.pretty_numStr(l120s12a.getDbr22A())).append("&nbsp;</td>")
						.append("<td style='"+html_noBorderTbl_c4_style_width+"' >").append(prop.getProperty("item140.desc31B")).append("</td>"+td_empty+"</tr>");
					}else if(l120s12a.getDbr22A()==null && l120s12a.getDbr22B()!=null){
						sb.append("<tr><td style='"+html_noBorderTbl_c1_style_width+"' >").append("(3)").append("</td>")
						.append("<td style='"+html_noBorderTbl_c2_style_width+"' >").append(prop.getProperty("item140.desc32A")).append("</td>")
						.append("<td align='right' style='"+html_noBorderTbl_c3_style_width+"' >").append(LMSUtil.pretty_numStr(l120s12a.getDbr22B())).append("&nbsp;</td>")
						.append("<td style='"+html_noBorderTbl_c4_style_width+"' >").append(prop.getProperty("item140.desc32B")).append("</td>"+td_empty+"</tr>");
					}else{ //兩者都填，或都不填
					sb.append("<tr><td style='"+html_noBorderTbl_c1_style_width+"' >").append("(3)").append("</td>")
						.append("<td style='"+html_noBorderTbl_c2_style_width+"' >").append(prop.getProperty("item140.desc31A")).append("</td>")
						.append("<td align='right' style='"+html_noBorderTbl_c3_style_width+"' >").append(LMSUtil.pretty_numStr(l120s12a.getDbr22A())).append("&nbsp;</td>")
						.append("<td style='"+html_noBorderTbl_c4_style_width+"' >").append(prop.getProperty("item140.desc31B")).append("</td>"+td_empty+"</tr>");
					sb.append("<tr><td>").append("&nbsp;").append("</td>")
						.append("<td style='"+html_noBorderTbl_c2_style_width+"' >").append(prop.getProperty("item140.desc32A")).append("</td>")
						.append("<td align='right' style='"+html_noBorderTbl_c3_style_width+"' >").append(LMSUtil.pretty_numStr(l120s12a.getDbr22B())).append("&nbsp;</td>")
						.append("<td style='"+html_noBorderTbl_c4_style_width+"' >").append(prop.getProperty("item140.desc32B")).append("</td>"+td_empty+"</tr>");
					}
					sb.append("<tr><td>").append("(4)").append("</td><td colspan='3'>").append(prop.getProperty("item140.desc4")).append("</td>"+td_empty+"</tr>");
					sb.append("<tr><td>").append("(5)").append("</td><td colspan='3'>").append(prop.getProperty("item140.desc5")).append("</td>"+td_empty+"</tr>");
					sb.append("</table>");
					sb.append(prop.getProperty("item140.desc6"));
				}				
				sb.append("</td>");
				sb.append("<td>").append(_l120s12a_itemval_Y(l120s12a.getItem140(), item_match)).append("</td>");
				sb.append("<td>").append(_l120s12a_itemval_N(l120s12a.getItem140(), item_notMatch)).append("</td>");
				sb.append("</tr>");	
			}
			if(true){
				sb.append("<tr>");
				sb.append("<td colspan='3'>").append(prop.getProperty("memo.desc")).append(prop.getProperty("memo.desc2")).append(UtilConstants.Mark.HTMLBR);
				String raw_memo = Util.trim(l120s12a.getMemo());
				raw_memo = ContractDocUtil.convert_string_for_XML_Predefined_entities(raw_memo);
				if(Util.isEmpty(raw_memo)){
					sb.append("&nbsp;");
				}else{
					sb.append(raw_memo.replace("\r\n", UtilConstants.Mark.HTMLBR).replace("\r", UtilConstants.Mark.HTMLBR).replace("\n", UtilConstants.Mark.HTMLBR));
				}
				sb.append("</td>");
				sb.append("</tr>");	
			}
			sb.append("</table>");
		}		
		String tbl_str = sb.toString();
		
		return "<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'>"
		+"<style>"
		+"table."+html_table_class+" {border-collapse:collapse; } "
		+"table."+html_table_class+" td{border-style: solid;border-width: 1px; vertical-align:top; font-family:標楷體;font-size:14px}"
		+"table."+html_noBorderTbl_class+" td{border: 0px; } "
		+"</style></head>"
		+ "<body>"
		+ tbl_str
		+ "</body>"
		+ "</html>";
	}
	
	public String build_html_SimplifyFlag_C(L120M01A l120m01a){
		List<L120S13A> model_list = clsService.findL120S13A(l120m01a.getMainId());
		List<L120S13A> choose_list = new ArrayList<L120S13A>();
		for(L120S13A l120s13a : model_list){
			if(Util.isNotEmpty(l120s13a.getRefMainId()) && Util.isNotEmpty(Util.trim(l120s13a.getRefSeq()))){
				choose_list.add(l120s13a);
			}else{
				continue; //空的
			}
			
		}
		if(choose_list.size()==0){
			return "";
		}
		
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1935V01Page.class);
		String item_match = prop.getProperty("CLS1201S23Panel.item.match");
		String item_notMatch = prop.getProperty("CLS1201S23Panel.item.notMatch");
		String html_table_class = "fmtTbl";
		String html_noBorderTbl_class = "noBorderTbl";
		String html_table_style_width = " width:18.1cm; ";
		StringBuffer sb = new StringBuffer();
		for(L120S13A l120s13a: choose_list ){
			L140M01A l140m01a = clsService.findL140M01A_mainId(l120s13a.getRefMainId());
			L140S02A l140s02a = clsService.findL140S02A(l120s13a.getRefMainId(), l120s13a.getRefSeq());
			//
			Map<String, String> map_formData = get_l120s13a_formData(l120m01a, l140m01a, l140s02a);
			sb.append("<table class='"+html_table_class+"' style='margin-top:0px; "+html_table_style_width+"' >");
			sb.append("<tr>");
			sb.append("<td colspan='2'>");
			sb.append(prop.getProperty("CLS1201S23Panel.header_basicData")); //header_basicData=基本資料
			sb.append("</td>");
			sb.append("</tr>");
			if(true){
				_l120s13a_append_sb_column2(sb
						, prop.getProperty("CLS1201S23Panel.custName")+"："+MapUtils.getString(map_formData, "l120s13a_custName")
						, prop.getProperty("CLS1201S23Panel.occupation")+"："+Util.trim(l120s13a.getOccupation()));
				_l120s13a_append_sb_column2(sb
						, prop.getProperty("CLS1201S23Panel.birthday")+"："+MapUtils.getString(map_formData, "l120s13a_birthday")
						, prop.getProperty("CLS1201S23Panel.yearIncome")+"："+MapUtils.getString(map_formData, "l120s13a_yearIncome"));
				_l120s13a_append_sb_column1(sb
						, prop.getProperty("CLS1201S23Panel.address")+"："+MapUtils.getString(map_formData, "l120s13a_address") );
				_l120s13a_append_sb_column1(sb
						, prop.getProperty("CLS1201S23Panel.applyAmt")+"：TWD "+NumConverter.addComma(LMSUtil.pretty_numStr(l120s13a.getApplyAmt()))+prop.getProperty("CLS1201S23Panel.curr.unit"));
				_l120s13a_append_sb_column1(sb
						, prop.getProperty("CLS1201S23Panel.lnYearMonth")+"："+MapUtils.getString(map_formData, "l120s13a_lnYearMonth"));
				_l120s13a_append_sb_column2(sb
						, prop.getProperty("CLS1201S23Panel.l120m01a_purpose")+"："+MapUtils.getString(map_formData, "l120s13a_l120m01a_purpose")
						, prop.getProperty("CLS1201S23Panel.drate")+"： "+MapUtils.getString(map_formData, "l120s13a_drate"));
			}
			sb.append("</table>");
			//======================			
			sb.append("<table class='"+html_table_class+"' style='margin-top:6px; "+html_table_style_width+"' >");
			sb.append("<td colspan='3'>");
			sb.append(prop.getProperty("CLS1201S23Panel.header_chkItem")); //header_chkItem=審核項目
			sb.append("</td>");
			
			_l120s13a_append_sb_column3(sb, "1."+prop.getProperty("CLS1201S23Panel.item010.desc"), _l120s12a_itemval_Y(l120s13a.getItem010(), item_match), _l120s12a_itemval_N(l120s13a.getItem010(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "2."+prop.getProperty("CLS1201S23Panel.item020.desc"), _l120s12a_itemval_Y(l120s13a.getItem020(), item_match), _l120s12a_itemval_N(l120s13a.getItem020(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "3."+prop.getProperty("CLS1201S23Panel.item030.desc"), _l120s12a_itemval_Y(l120s13a.getItem030(), item_match), _l120s12a_itemval_N(l120s13a.getItem030(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "4."+prop.getProperty("CLS1201S23Panel.item040.desc"), _l120s12a_itemval_Y(l120s13a.getItem040(), item_match), _l120s12a_itemval_N(l120s13a.getItem040(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "5."+prop.getProperty("CLS1201S23Panel.item050.desc"), _l120s12a_itemval_Y(l120s13a.getItem050(), item_match), _l120s12a_itemval_N(l120s13a.getItem050(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "6."+prop.getProperty("CLS1201S23Panel.item060.desc"), _l120s12a_itemval_Y(l120s13a.getItem060(), item_match), _l120s12a_itemval_N(l120s13a.getItem060(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "7."+prop.getProperty("CLS1201S23Panel.item070.desc"), _l120s12a_itemval_Y(l120s13a.getItem070(), item_match), _l120s12a_itemval_N(l120s13a.getItem070(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "8."+prop.getProperty("CLS1201S23Panel.item080.desc"), _l120s12a_itemval_Y(l120s13a.getItem080(), item_match), _l120s12a_itemval_N(l120s13a.getItem080(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "9."+prop.getProperty("CLS1201S23Panel.item090.desc")+prop.getProperty("CLS1201S23Panel.item090.desc1"), _l120s12a_itemval_Y(l120s13a.getItem090(), item_match), _l120s12a_itemval_N(l120s13a.getItem090(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "10."+prop.getProperty("CLS1201S23Panel.item100.desc"), _l120s12a_itemval_Y(l120s13a.getItem100(), item_match), _l120s12a_itemval_N(l120s13a.getItem100(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "11."+prop.getProperty("CLS1201S23Panel.item110.desc"), _l120s12a_itemval_Y(l120s13a.getItem110(), item_match), _l120s12a_itemval_N(l120s13a.getItem110(), item_notMatch) );
			_l120s13a_append_sb_column3(sb, "12."+prop.getProperty("CLS1201S23Panel.item120.desc"), _l120s12a_itemval_Y(l120s13a.getItem120(), item_match), _l120s12a_itemval_N(l120s13a.getItem120(), item_notMatch) );
			
			
			if(true){
				sb.append("<tr>");
				sb.append("<td colspan='3'>").append(prop.getProperty("CLS1201S23Panel.memo.desc")).append(prop.getProperty("CLS1201S23Panel.memo.desc2")).append(UtilConstants.Mark.HTMLBR);
				String raw_memo = Util.trim(l120s13a.getMemo());
				raw_memo = ContractDocUtil.convert_string_for_XML_Predefined_entities(raw_memo);
				if(Util.isEmpty(raw_memo)){
					sb.append("&nbsp;");
				}else{
					sb.append(raw_memo.replace("\r\n", UtilConstants.Mark.HTMLBR).replace("\r", UtilConstants.Mark.HTMLBR).replace("\n", UtilConstants.Mark.HTMLBR));
				}
				sb.append("</td>");
				sb.append("</tr>");	
			}
			sb.append("</table>");
		}		
		String tbl_str = sb.toString();
		
		return "<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'>"
		+"<style>"
		+"table."+html_table_class+" {border-collapse:collapse; } "
		+"table."+html_table_class+" td{border-style: solid;border-width: 1px; vertical-align:top; font-family:標楷體;font-size:14px}"
		+"table."+html_noBorderTbl_class+" td{border: 0px; } "
		+"</style></head>"
		+ "<body>"
		+ tbl_str
		+ "</body>"
		+ "</html>";
		
	}
	
	private String build_html_SimplifyFlag_D(L120M01A l120m01a){
		List<L120S15A> model_list = clsService.findL120S15A(l120m01a.getMainId());
		List<L120S15A> choose_list = new ArrayList<L120S15A>();
		for(L120S15A l120s15a : model_list){
			if(Util.isNotEmpty(l120s15a.getRefMainId()) && Util.isNotEmpty(Util.trim(l120s15a.getRefSeq()))){
				choose_list.add(l120s15a);
			}else{
				continue; //空的
			}
			
		}
		if(choose_list.size()==0){
			return "";
		}
		
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1935V01Page.class);
		String item_match = prop.getProperty("CLS1201S25Panel.item.match");
		String item_matchCreditLoanCriterion = prop.getProperty("CLS1201S25Panel.item.matchCreditLoanCriterion");
		String item_notMatch = prop.getProperty("CLS1201S25Panel.item.notMatch");
		String html_table_class = "fmtTbl";
		String html_noBorderTbl_class = "noBorderTbl";
		String html_table_style_width = " width:18.1cm; ";
		String html_noBorderTbl_style_width = ""; //" width:9cm; ";
		
		StringBuffer sb = new StringBuffer();
		for(L120S15A l120s15a: choose_list ){
			L140M01A l140m01a = clsService.findL140M01A_mainId(l120s15a.getRefMainId());
			L140S02A l140s02a = clsService.findL140S02A(l120s15a.getRefMainId(), l120s15a.getRefSeq());
			String l120s15a_rptId = Util.trim(l120s15a.getRptId());
			if(Util.isEmpty(l120s15a_rptId)){
				l120s15a_rptId = "V2020";
			}
			//
			Map<String, String> map_formData = get_l120s15a_formData(l120m01a, l140m01a, l140s02a);
			sb.append("<table class='"+html_table_class+"' style='margin-top:0px; "+html_table_style_width+"' >");
			sb.append("<tr>");
			sb.append("<td colspan='2'>");
			sb.append(prop.getProperty("CLS1201S25Panel.header_basicData")); //header_basicData=基本資料
			sb.append("</td>");
			sb.append("</tr>");
			if(true){
				_l120s15a_append_sb_column2(sb
						, prop.getProperty("CLS1201S25Panel.custName")+"："+MapUtils.getString(map_formData, "l120s15a_custName")
						, prop.getProperty("CLS1201S25Panel.occupation")+"："+Util.trim(l120s15a.getOccupation()));
				_l120s15a_append_sb_column2(sb
						, prop.getProperty("CLS1201S25Panel.applyAmt")+"：TWD "+NumConverter.addComma(LMSUtil.pretty_numStr(l120s15a.getApplyAmt()))+prop.getProperty("CLS1201S25Panel.curr.unit")
						, prop.getProperty("CLS1201S25Panel.seniority")+"："+MapUtils.getString(map_formData, "l120s15a_seniority"));
				
				_l120s15a_append_sb_column2(sb
						, prop.getProperty("CLS1201S25Panel.lnYearMonth")+"："+MapUtils.getString(map_formData, "l120s15a_lnYearMonth") 
						, prop.getProperty("CLS1201S25Panel.avgMincomeAmt")+"：TWD "+NumConverter.addComma(LMSUtil.pretty_numStr(l120s15a.getAvgMincomeAmt()))+prop.getProperty("CLS1201S25Panel.curr.unit"));
				_l120s15a_append_sb_column2(sb
						, prop.getProperty("CLS1201S25Panel.pmtAmt")+"：TWD "+NumConverter.addComma(LMSUtil.pretty_numStr(l120s15a.getPmtAmt()))+prop.getProperty("CLS1201S25Panel.curr.unit")						
						, prop.getProperty("CLS1201S25Panel.drate")+"： "+MapUtils.getString(map_formData, "l120s15a_drate")+"&nbsp;&nbsp;"+prop.getProperty("CLS1201S25Panel.see_attch"));
			}
			sb.append("</table>");
			//======================			
			if(true){
				BigDecimal sum_feeNo01 = BigDecimal.ZERO;
				BigDecimal sum_feeNo02 = BigDecimal.ZERO;
				BigDecimal sum_feeNo03 = BigDecimal.ZERO;
				BigDecimal sum_feeNo04 = BigDecimal.ZERO;
				BigDecimal sum_feeNo05 = BigDecimal.ZERO;
				BigDecimal sum_feeNo06 = BigDecimal.ZERO;
				BigDecimal sum_feeNo07 = BigDecimal.ZERO;
				for(L140M01R l140m01r: clsService.findL140M01R_exclude_feeSrc3(l120m01a.getMainId())){
					String feeNo = l140m01r.getFeeNo();
					BigDecimal amt = l140m01r.getFeeAmt();
					
					if(Util.equals("01", feeNo)){
						sum_feeNo01 = sum_feeNo01.add(amt);
					}else if(Util.equals("02", feeNo)){
						sum_feeNo02 = sum_feeNo02.add(amt);
					}else if(Util.equals("03", feeNo)){
						sum_feeNo03 = sum_feeNo03.add(amt);
					}else if(Util.equals("04", feeNo)){
						sum_feeNo04 = sum_feeNo04.add(amt);
					}else if(Util.equals("05", feeNo)){
						sum_feeNo05 = sum_feeNo05.add(amt);
					}else if(Util.equals("06", feeNo)){
						sum_feeNo06 = sum_feeNo06.add(amt);
					}else if(Util.equals("07", feeNo)){
						sum_feeNo07 = sum_feeNo07.add(amt);
					}else{
						continue;
					}
				}
				
				List<String> m01r_dataStr_list = new ArrayList<String>();
				Map<String, String> feeNoMap = clsService.get_codeTypeWithOrder("cls1141_feeNo");
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "01", feeNoMap, sum_feeNo01);	
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "02", feeNoMap, sum_feeNo02);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "03", feeNoMap, sum_feeNo03);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "04", feeNoMap, sum_feeNo04);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "05", feeNoMap, sum_feeNo05);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "06", feeNoMap, sum_feeNo06);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "07", feeNoMap, sum_feeNo07);
				
				String m01r_dataStr = m01r_dataStr_list.size()==0?"無":(StringUtils.join(m01r_dataStr_list, "、")+"。");
						
				sb.append("<table class='"+html_table_class+"' style='margin-top:6px; "+html_table_style_width+"' >");
				sb.append("<tr>");
				sb.append("<td style='width:90px;'>").append("各項費用").append("</td>");
				sb.append("<td>").append(m01r_dataStr).append("</td>");
				sb.append("</tr>");
				sb.append("</table>");
			}			
			//======================			
			sb.append("<table class='"+html_table_class+"' style='margin-top:6px; "+html_table_style_width+"' >");
			sb.append("<td colspan='3'>");
			sb.append(prop.getProperty("CLS1201S25Panel.header_chkItem")); //header_chkItem=審核項目
			sb.append("</td>");
			
		
			if(Util.equals(l120s15a_rptId, "V202201")){ //V202201
				prop = MessageBundleScriptCreator.getComponentResource(LMS1935V01Page.class);
				_l120s15a_append_sb_column3(sb, "1."+prop.getProperty("CLS1201S25BPanel.item010.desc"), _l120s12a_itemval_Y(l120s15a.getItem010(), item_match), _l120s12a_itemval_N(l120s15a.getItem010(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "2."+prop.getProperty("CLS1201S25BPanel.item020.desc"), _l120s12a_itemval_Y(l120s15a.getItem020(), item_match), _l120s12a_itemval_N(l120s15a.getItem020(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "3."+prop.getProperty("CLS1201S25BPanel.item030.desc"), _l120s12a_itemval_Y(l120s15a.getItem030(), item_match), _l120s12a_itemval_N(l120s15a.getItem030(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "4."+prop.getProperty("CLS1201S25BPanel.item040.desc")+prop.getProperty("CLS1201S25BPanel.item040.desc1"), _l120s12a_itemval_Y(l120s15a.getItem040(), item_match), _l120s12a_itemval_N(l120s15a.getItem040(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "5."+prop.getProperty("CLS1201S25BPanel.item060.desc")+prop.getProperty("CLS1201S25BPanel.item060.desc1"), _l120s12a_itemval_Y(l120s15a.getItem060(), item_match), _l120s12a_itemval_N(l120s15a.getItem060(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "6."+prop.getProperty("CLS1201S25BPanel.item070.desc"), _l120s12a_itemval_Y(l120s15a.getItem070(), item_match), _l120s12a_itemval_N(l120s15a.getItem070(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "7."+prop.getProperty("CLS1201S25BPanel.item080.desc"), _l120s12a_itemval_Y(l120s15a.getItem080(), item_match), _l120s12a_itemval_N(l120s15a.getItem080(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "8."+prop.getProperty("CLS1201S25BPanel.item090.desc"), _l120s12a_itemval_Y(l120s15a.getItem090(), item_match), _l120s12a_itemval_N(l120s15a.getItem090(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "9."+prop.getProperty("CLS1201S25BPanel.item100.desc"), _l120s12a_itemval_Y(l120s15a.getItem100(), item_match), _l120s12a_itemval_N(l120s15a.getItem100(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "10."+prop.getProperty("CLS1201S25BPanel.item110.desc"), _l120s12a_itemval_Y(l120s15a.getItem110(), item_match), _l120s12a_itemval_N(l120s15a.getItem110(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "11."+prop.getProperty("CLS1201S25BPanel.item120.desc")+prop.getProperty("CLS1201S25BPanel.item120.desc1"), _l120s12a_itemval_Y(l120s15a.getItem120(), item_match), _l120s12a_itemval_N(l120s15a.getItem120(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "12."+prop.getProperty("CLS1201S25BPanel.item130.desc"), _l120s12a_itemval_Y(l120s15a.getItem130(), item_match), _l120s12a_itemval_N(l120s15a.getItem130(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "13."+prop.getProperty("CLS1201S25BPanel.item140.desc")+prop.getProperty("CLS1201S25BPanel.item140.desc1"), _l120s12a_itemval_Y(l120s15a.getItem140(), item_match), _l120s12a_itemval_N(l120s15a.getItem140(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "14."+prop.getProperty("CLS1201S25BPanel.item150.desc"), _l120s12a_itemval_Y(l120s15a.getItem150(), item_match), _l120s12a_itemval_N(l120s15a.getItem150(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "15."+prop.getProperty("CLS1201S25BPanel.item160.desc"), _l120s12a_itemval_Y(l120s15a.getItem160(), item_match), _l120s12a_itemval_N(l120s15a.getItem160(), item_notMatch) );
				if(true){
					sb.append("<tr>");
					sb.append("<td>").append("16."+prop.getProperty("CLS1201S25BPanel.item170.descA")).append("&nbsp;").append(MapUtils.getString(map_formData, "l120s15a_approveAmt")).append(prop.getProperty("CLS1201S25BPanel.item170.descB"));
					if(true){ //第14項之下包含5個細項
						sb.append("<table class='"+html_noBorderTbl_class+"'  style='"+html_noBorderTbl_style_width+"'>");
						sb.append("<tr><td>").append("(1)").append("</td><td>").append(prop.getProperty("CLS1201S25BPanel.item170.desc1")).append("</td></tr>");
						sb.append("<tr><td>").append("(2)").append("</td><td>").append(prop.getProperty("CLS1201S25BPanel.item170.desc2A")+"&nbsp;"+MapUtils.getString(map_formData, "l120s15a_drate_atItem")+"&nbsp;");
						if(Util.equals(l120s15a_rptId, "V202101")){
							
						}else if(Util.equals(l120s15a_rptId, "V2020")){
							sb.append(prop.getProperty("CLS1201S25BPanel.item170.desc2B"));
						}
						sb.append("</td></tr>");
						sb.append("<tr><td>").append("(3)").append("</td><td>").append(prop.getProperty("CLS1201S25BPanel.item170.desc3A")+"&nbsp;"
									+(l120s15a.getDbr22A()==null?"&nbsp;&nbsp;&nbsp;&nbsp;":LMSUtil.pretty_numStr(l120s15a.getDbr22A()))+"&nbsp;");
						sb.append(prop.getProperty("CLS1201S25BPanel.item170.desc3B"))
							.append("</td></tr>");
					
						sb.append("<tr><td>").append("(4)").append("</td><td>").append(prop.getProperty("CLS1201S25BPanel.item170.desc4")).append("</td></tr>");
						sb.append("<tr><td>").append("(5)").append("</td><td>").append(prop.getProperty("CLS1201S25BPanel.item170.desc5")).append("</td></tr>");
						sb.append("</table>");
						sb.append(prop.getProperty("CLS1201S25BPanel.item170.desc6"));
					}				
					sb.append("</td>");
					String l120s15a_item170_descY = item_match;
					l120s15a_item170_descY = item_matchCreditLoanCriterion;
					sb.append("<td>").append(_l120s12a_itemval_Y(l120s15a.getItem170(), l120s15a_item170_descY)).append("</td>");
					sb.append("<td>").append(_l120s12a_itemval_N(l120s15a.getItem170(), item_notMatch)).append("</td>");
					sb.append("</tr>");	
				}
			}else{
				_l120s15a_append_sb_column3(sb, "1."+prop.getProperty("CLS1201S25BPanel.item010.desc"), _l120s12a_itemval_Y(l120s15a.getItem010(), item_match), _l120s12a_itemval_N(l120s15a.getItem010(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "2."+prop.getProperty("CLS1201S25BPanel.item020.desc"), _l120s12a_itemval_Y(l120s15a.getItem020(), item_match), _l120s12a_itemval_N(l120s15a.getItem020(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "3."+prop.getProperty("CLS1201S25BPanel.item030.desc"), _l120s12a_itemval_Y(l120s15a.getItem030(), item_match), _l120s12a_itemval_N(l120s15a.getItem030(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "4."+prop.getProperty("CLS1201S25BPanel.item040.desc")+prop.getProperty("CLS1201S25BPanel.item040.desc1"), _l120s12a_itemval_Y(l120s15a.getItem040(), item_match), _l120s12a_itemval_N(l120s15a.getItem040(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "5."+prop.getProperty("CLS1201S25BPanel.item050.desc"), _l120s12a_itemval_Y(l120s15a.getItem050(), item_match)
							+prop.getProperty("CLS1201S25BPanel.grade.descA")+Util.trim(l120s15a.getGrade1())+prop.getProperty("CLS1201S25BPanel.grade.descB")
							, _l120s12a_itemval_N(l120s15a.getItem050(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "6."+prop.getProperty("CLS1201S25BPanel.item060.desc")+prop.getProperty("CLS1201S25BPanel.item060.desc1"), _l120s12a_itemval_Y(l120s15a.getItem060(), item_match), _l120s12a_itemval_N(l120s15a.getItem060(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "7."+prop.getProperty("CLS1201S25BPanel.item070.desc"), _l120s12a_itemval_Y(l120s15a.getItem070(), item_match), _l120s12a_itemval_N(l120s15a.getItem070(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "8."+prop.getProperty("CLS1201S25BPanel.item080.desc"), _l120s12a_itemval_Y(l120s15a.getItem080(), item_match), _l120s12a_itemval_N(l120s15a.getItem080(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "9."+prop.getProperty("CLS1201S25BPanel.item090.desc"), _l120s12a_itemval_Y(l120s15a.getItem090(), item_match), _l120s12a_itemval_N(l120s15a.getItem090(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "10."+prop.getProperty("CLS1201S25BPanel.item100.desc"), _l120s12a_itemval_Y(l120s15a.getItem100(), item_match), _l120s12a_itemval_N(l120s15a.getItem100(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "11."+prop.getProperty("CLS1201S25BPanel.item110.desc"), _l120s12a_itemval_Y(l120s15a.getItem110(), item_match), _l120s12a_itemval_N(l120s15a.getItem110(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "12."+prop.getProperty("CLS1201S25BPanel.item120.desc")+prop.getProperty("CLS1201S25BPanel.item120.desc1"), _l120s12a_itemval_Y(l120s15a.getItem120(), item_match), _l120s12a_itemval_N(l120s15a.getItem120(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "13."+prop.getProperty("CLS1201S25BPanel.item130.desc"), _l120s12a_itemval_Y(l120s15a.getItem130(), item_match), _l120s12a_itemval_N(l120s15a.getItem130(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "14."+prop.getProperty("CLS1201S25BPanel.item140.desc")+prop.getProperty("CLS1201S25BPanel.item140.desc1"), _l120s12a_itemval_Y(l120s15a.getItem140(), item_match), _l120s12a_itemval_N(l120s15a.getItem140(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "15."+prop.getProperty("CLS1201S25BPanel.item150.desc"), _l120s12a_itemval_Y(l120s15a.getItem150(), item_match), _l120s12a_itemval_N(l120s15a.getItem150(), item_notMatch) );
				_l120s15a_append_sb_column3(sb, "16."+prop.getProperty("CLS1201S25BPanel.item160.desc"), _l120s12a_itemval_Y(l120s15a.getItem160(), item_match), _l120s12a_itemval_N(l120s15a.getItem160(), item_notMatch) );
				if(true){
					sb.append("<tr>");
					sb.append("<td>").append("17."+prop.getProperty("CLS1201S25BPanel.item170.descA")).append("&nbsp;").append(MapUtils.getString(map_formData, "l120s15a_approveAmt")).append(prop.getProperty("CLS1201S25BPanel.item170.descB"));
					if(true){ //第14項之下包含5個細項
						/*  
						 // 在 span 指定 width:50px; display: inline-block; 看到的垂直對齊不一致 => 改用 table
						String span_dbr_style = " style='width:50px; display: inline-block; text-align:right;' ";
						sb.append(UtilConstants.Mark.HTMLBR).append("(1)").append(prop.getProperty("item140.desc1"));
						sb.append(UtilConstants.Mark.HTMLBR).append("(2)").append(prop.getProperty("item140.desc2A")).append("&nbsp;").append(NumConverter.addComma(LMSUtil.pretty_numStr(l120s12a.getMonthAmtA()))).append(prop.getProperty("curr.unit"))
							.append(prop.getProperty("item140.desc2B"))
							.append(prop.getProperty("item140.desc2C")).append("&nbsp;").append(NumConverter.addComma(LMSUtil.pretty_numStr(l120s12a.getMonthAmtB()))).append(prop.getProperty("curr.unit"));
						sb.append(UtilConstants.Mark.HTMLBR).append("(3)").append(prop.getProperty("item140.desc31A")).append("<span "+span_dbr_style+" >").append(LMSUtil.pretty_numStr(l120s12a.getDbr22A())).append("</span>").append(prop.getProperty("item140.desc31B"));
						sb.append(UtilConstants.Mark.HTMLBR).append("&nbsp;&nbsp;&nbsp;").append(prop.getProperty("item140.desc32A")).append("<span "+span_dbr_style+" >").append(LMSUtil.pretty_numStr(l120s12a.getDbr22B())).append("</span>").append(prop.getProperty("item140.desc32B"));
						sb.append(UtilConstants.Mark.HTMLBR).append("(4)").append(prop.getProperty("item140.desc4"));
						sb.append(UtilConstants.Mark.HTMLBR).append("(5)").append(prop.getProperty("item140.desc5"));
						*/
						sb.append("<table class='"+html_noBorderTbl_class+"'  style='"+html_noBorderTbl_style_width+"'>");
						sb.append("<tr><td>").append("(1)").append("</td><td>").append(prop.getProperty("CLS1201S25BPanel.item170.desc1")).append("</td></tr>");
						sb.append("<tr><td>").append("(2)").append("</td><td>").append(prop.getProperty("CLS1201S25BPanel.item170.desc2A")+"&nbsp;"+MapUtils.getString(map_formData, "l120s15a_drate_atItem")+"&nbsp;");
						if(Util.equals(l120s15a_rptId, "V202101")){
							
						}else if(Util.equals(l120s15a_rptId, "V2020")){
							sb.append(prop.getProperty("item170.desc2B"));
						}
						sb.append("</td></tr>");
						sb.append("<tr><td>").append("(3)").append("</td><td>").append(prop.getProperty("item170.desc3A")+"&nbsp;"
									+(l120s15a.getDbr22A()==null?"&nbsp;&nbsp;&nbsp;&nbsp;":LMSUtil.pretty_numStr(l120s15a.getDbr22A()))+"&nbsp;");
						sb.append(prop.getProperty("item170.desc3B"))
							.append("</td></tr>");
					
						sb.append("<tr><td>").append("(4)").append("</td><td>").append(prop.getProperty("item170.desc4")).append("</td></tr>");
						sb.append("<tr><td>").append("(5)").append("</td><td>").append(prop.getProperty("item170.desc5")).append("</td></tr>");
						sb.append("</table>");
						sb.append(prop.getProperty("item170.desc6"));
					}				
					sb.append("</td>");
					String l120s15a_item170_descY = item_match;
					if(Util.equals(l120s15a_rptId, "V202101")){
						l120s15a_item170_descY = item_matchCreditLoanCriterion;
					}else if(Util.equals(l120s15a_rptId, "V2020")){
						l120s15a_item170_descY = item_match;
					}
					sb.append("<td>").append(_l120s12a_itemval_Y(l120s15a.getItem170(), l120s15a_item170_descY)).append("</td>");
					sb.append("<td>").append(_l120s12a_itemval_N(l120s15a.getItem170(), item_notMatch)).append("</td>");
					sb.append("</tr>");	
				}
			}
			if(true){
				sb.append("<tr>");
				sb.append("<td colspan='3'>").append(prop.getProperty("memo.desc")).append(prop.getProperty("memo.desc2")).append(UtilConstants.Mark.HTMLBR);
				String raw_memo = Util.trim(l120s15a.getMemo());
				raw_memo = ContractDocUtil.convert_string_for_XML_Predefined_entities(raw_memo);
				if(Util.isEmpty(raw_memo)){
					sb.append("&nbsp;");
				}else{
					sb.append(raw_memo.replace("\r\n", UtilConstants.Mark.HTMLBR).replace("\r", UtilConstants.Mark.HTMLBR).replace("\n", UtilConstants.Mark.HTMLBR));
				}
				sb.append("</td>");
				sb.append("</tr>");	
			}
			sb.append("</table>");
		}		
		String tbl_str = sb.toString();
		
		return "<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'>"
		+"<style>"
		+"table."+html_table_class+" {border-collapse:collapse; } "
		+"table."+html_table_class+" td{border-style: solid;border-width: 1px; vertical-align:top; font-family:標楷體;font-size:14px}"
		+"table."+html_noBorderTbl_class+" td{border: 0px; } "
		+"</style></head>"
		+ "<body>"
		+ tbl_str
		+ "</body>"
		+ "</html>";
	}
	
	private Map<String, String> get_l120s12a_formData(L120M01A l120m01a, L140M01A l140m01a, L140S02A l140s02a){
		
		Map<String, String> r = new HashMap<String, String>();
		if(l120m01a!=null){
			Map<String, CapAjaxFormResult> codeMap = codeTypeService.findByCodeType(new String[] { "cls1141_resource","cls1141_purpose" });

			CapAjaxFormResult cls1141_purpose = codeMap.get("cls1141_purpose");
			CapAjaxFormResult cls1141_resourceMap = codeMap.get("cls1141_resource");
			String resourceOthDesc = "";
			if(Util.isNotEmpty(Util.trim(l120m01a.getResourceOth()))){
				resourceOthDesc = "&nbsp;"+"<u>"+Util.trim(l120m01a.getResourceOth())+"</u>";
			}
			String purposeOthDesc = "";
			if(Util.isNotEmpty(Util.trim(l120m01a.getPurposeOth()))){
				purposeOthDesc = "&nbsp;"+"<u>"+Util.trim(l120m01a.getPurposeOth())+"</u>";
			}
			r.put("l120s12a_l120m01a_resource", this.getPurpose(Util.trim(l120m01a.getResource()), cls1141_resourceMap)+resourceOthDesc);
			r.put("l120s12a_l120m01a_purpose", this.getPurpose(Util.trim(l120m01a.getPurpose()), cls1141_purpose)+purposeOthDesc);
			
			r.put("l120s12a_l140m01r_desc", l140m01r_desc(l120m01a));
		}
		if(l140m01a!=null){
			r.put("l120s12a_custName", l140m01a.getCustName());
			
			String l120m01a_mainId = l120m01a.getMainId();
			String idDup = LMSUtil.getCustKey_len10custId(l140m01a.getCustId(), l140m01a.getDupNo());
			Set<String> idDup11Set = new HashSet<String>();
			idDup11Set.add(idDup);
			Map<String, C120S01A> map_c120s01a = clsService.findIdDup_C120S01A(l120m01a_mainId, idDup11Set);
			Map<String, C120S01B> map_c120s01b = clsService.findIdDup_C120S01B(l120m01a_mainId, idDup11Set);
			Map<String, C120S01C> map_c120s01c = clsService.findIdDup_C120S01C(l120m01a_mainId, idDup11Set);
			C120S01A c120s01a = map_c120s01a.get(idDup);
			C120S01B c120s01b = map_c120s01b.get(idDup);
			C120S01C c120s01c = map_c120s01c.get(idDup);
			if(c120s01a!=null){
				r.put("l120s12a_birthday", Util.trim(TWNDate.toAD(c120s01a.getBirthday())));
				r.put("l120s12a_address", Util.toSemiCharString(Util.trim(c120s01a.getCoTarget())));
			}
			if(c120s01b!=null){
				r.put("l120s12a_seniority",	get_seniority_desc(c120s01b));
				r.put("l120s12a_yearIncome", Util.trim(c120s01b.getPayCurr())+ " "+ NumConverter.addComma(Util.parseInt(c120s01b.getPayAmt().add(c120s01b.getOthAmt())))+"萬元");
			}
			if(c120s01c!=null){
				r.put("l120s12a_drate", LMSUtil.pretty_numStr(c120s01c.getDRate())+"%");	
			}
			
			r.put("l120s12a_approveAmt", NumConverter.addComma(Arithmetic.div(l140m01a.getCurrentApplyAmt(), BigDecimal.valueOf(10000), 4)));//單位:萬元
			
			if(true){ //保證人姓名
				Set<String> guarantor_name_set = new LinkedHashSet<String>();
				for(L140S01A l140s01a : clsService.findL140S01A(l140m01a)){
					if(Util.equals(UtilConstants.lngeFlag.連帶保證人, l140s01a.getCustPos())
							|| Util.equals(UtilConstants.lngeFlag.ㄧ般保證人, l140s01a.getCustPos()) ){
						guarantor_name_set.add(Util.trim(l140s01a.getCustName()));
					}
				}
				String guarantor_name = StringUtils.join(guarantor_name_set, "、");
				if(Util.isEmpty(guarantor_name)){
					guarantor_name = "無";
				}
				r.put("l120s12a_guarantor_name", guarantor_name);
			}
		}
		if(l140s02a!=null){
			r.put("l120s12a_lnYearMonth", l140s02a.getLnYear()+"年"+l140s02a.getLnMonth()+"月");
			
		}
		return r;
	}
	
	private Map<String, String> get_l120s13a_formData(L120M01A l120m01a, L140M01A l140m01a, L140S02A l140s02a){
		
		Map<String, String> r = new HashMap<String, String>();
		if(l120m01a!=null){
			Map<String, CapAjaxFormResult> codeMap = codeTypeService.findByCodeType(new String[] { "cls1141_resource","cls1141_purpose" });

			CapAjaxFormResult cls1141_purpose = codeMap.get("cls1141_purpose");
			CapAjaxFormResult cls1141_resourceMap = codeMap.get("cls1141_resource");
			String resourceOthDesc = "";
			if(Util.isNotEmpty(Util.trim(l120m01a.getResourceOth()))){
				resourceOthDesc = "&nbsp;"+"<u>"+Util.trim(l120m01a.getResourceOth())+"</u>";
			}
			String purposeOthDesc = "";
			if(Util.isNotEmpty(Util.trim(l120m01a.getPurposeOth()))){
				purposeOthDesc = "&nbsp;"+"<u>"+Util.trim(l120m01a.getPurposeOth())+"</u>";
			}
			r.put("l120s13a_l120m01a_resource", this.getPurpose(Util.trim(l120m01a.getResource()), cls1141_resourceMap)+resourceOthDesc);
			r.put("l120s13a_l120m01a_purpose", this.getPurpose(Util.trim(l120m01a.getPurpose()), cls1141_purpose)+purposeOthDesc);
		}
		if(l140m01a!=null){
			r.put("l120s13a_custName", l140m01a.getCustName());
			
			String l120m01a_mainId = l120m01a.getMainId();
			String idDup = LMSUtil.getCustKey_len10custId(l140m01a.getCustId(), l140m01a.getDupNo());
			Set<String> idDup11Set = new HashSet<String>();
			idDup11Set.add(idDup);
			Map<String, C120S01A> map_c120s01a = clsService.findIdDup_C120S01A(l120m01a_mainId, idDup11Set);
			Map<String, C120S01B> map_c120s01b = clsService.findIdDup_C120S01B(l120m01a_mainId, idDup11Set);
			Map<String, C120S01C> map_c120s01c = clsService.findIdDup_C120S01C(l120m01a_mainId, idDup11Set);
			C120S01A c120s01a = map_c120s01a.get(idDup);
			C120S01B c120s01b = map_c120s01b.get(idDup);
			C120S01C c120s01c = map_c120s01c.get(idDup);
			if(c120s01a!=null){
				r.put("l120s13a_birthday", Util.trim(TWNDate.toAD(c120s01a.getBirthday())));
				r.put("l120s13a_address", Util.toSemiCharString(Util.trim(c120s01a.getCoTarget())));
			}
			if(c120s01b!=null){
				r.put("l120s13a_seniority",	get_seniority_desc(c120s01b));
				r.put("l120s13a_yearIncome", Util.trim(c120s01b.getPayCurr())+ " "+ NumConverter.addComma(Util.parseInt(c120s01b.getPayAmt().add(c120s01b.getOthAmt())))+"萬元");
			}
			if(c120s01c!=null){
				r.put("l120s13a_drate", LMSUtil.pretty_numStr(c120s01c.getDRate())+"%");	
			}
			r.put("l120s13a_approveAmt", NumConverter.addComma(Arithmetic.div(l140m01a.getCurrentApplyAmt(), BigDecimal.valueOf(10000), 4)));//單位:萬元
		}
		if(l140s02a!=null){
			r.put("l120s13a_lnYearMonth", l140s02a.getLnYear()+"年"+l140s02a.getLnMonth()+"月");
			
		}
		return r;
	}

	private Map<String, String> get_l120s15a_formData(L120M01A l120m01a, L140M01A l140m01a, L140S02A l140s02a){
		
		Map<String, String> r = new HashMap<String, String>();
		if(l120m01a!=null){
			
		}
		if(l140m01a!=null){
			r.put("l120s15a_custName", l140m01a.getCustName());
			
			String l120m01a_mainId = l120m01a.getMainId();
			String idDup = LMSUtil.getCustKey_len10custId(l140m01a.getCustId(), l140m01a.getDupNo());
			Set<String> idDup11Set = new HashSet<String>();
			idDup11Set.add(idDup);
			Map<String, C120S01A> map_c120s01a = clsService.findIdDup_C120S01A(l120m01a_mainId, idDup11Set);
			Map<String, C120S01B> map_c120s01b = clsService.findIdDup_C120S01B(l120m01a_mainId, idDup11Set);
			Map<String, C120S01C> map_c120s01c = clsService.findIdDup_C120S01C(l120m01a_mainId, idDup11Set);
			C120S01A c120s01a = map_c120s01a.get(idDup);
			C120S01B c120s01b = map_c120s01b.get(idDup);
			C120S01C c120s01c = map_c120s01c.get(idDup);
			if(c120s01a!=null){
								
			}
			if(c120s01b!=null){
				r.put("l120s15a_seniority",	get_seniority_desc(c120s01b));
				
			}
			if(c120s01c!=null){
				r.put("l120s15a_drate", LMSUtil.pretty_numStr(c120s01c.getDRate())+"%");
				r.put("l120s15a_drate_atItem", LMSUtil.pretty_numStr(c120s01c.getDRate())+"%");
			}
			
			r.put("l120s15a_approveAmt", NumConverter.addComma(Arithmetic.div(l140m01a.getCurrentApplyAmt(), BigDecimal.valueOf(10000), 4)));//單位:萬元
			
		}
		if(l140s02a!=null){
			r.put("l120s15a_lnYearMonth", l140s02a.getLnYear()+"年"+l140s02a.getLnMonth()+"月");
			
		}
		return r;
	}
	
	private String getPurpose(String purpose, CapAjaxFormResult purposeMap) {
		StringBuffer str = new StringBuffer();
		String[] arrayStr = purpose.split(UtilConstants.Mark.SPILT_MARK);
		for (String key : arrayStr) {
			if (Util.isEmpty(key)) {
				continue;
			}
			str.append(str.length() > 0 ? "、" : "");
			str.append(purposeMap.get(key));
		}

		return str.toString();
	}
	
	private String get_seniority_desc(C120S01B c120s01b){
		String seniority_desc = LMSUtil.pretty_numStr(c120s01b.getSeniority())+"年"; //default
		if(c120s01b.getSeniority()!=null){
			Integer[] seniorityYM_arr = ClsUtility.seniorityYM_decode(c120s01b.getSeniority());
			if(c120s01b.getSnrM()==null){
				seniority_desc = String.valueOf(seniorityYM_arr[0])+"年";
			}else{
				seniority_desc = String.valueOf(seniorityYM_arr[0])+"年"+String.valueOf(seniorityYM_arr[1])+"月";
			}
		}	
		return seniority_desc;
	}
	
	private String l140m01r_desc(L120M01A l120m01a){
		StringBuffer collMemo = new StringBuffer();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1935V01Page.class);

		String mainId = l120m01a.getMainId();
		String tAuthLvl = l120m01a.getAuthLvl();
		// tmpStr1授權外的值tmpStr2原授權內的值
		String tmpStr1 = "";
		String tmpStr2 = "";
		tmpStr1 = setL140M01RListDataBefore(mainId, "1");

		if (!"1".equals(tAuthLvl)) {
			tmpStr2 = setL140M01RListDataBefore(mainId, tAuthLvl);
		}

		if (!"".equals(tmpStr2)) {
			if (!"".equals(Util.trim(l120m01a.getSignNo()))) {
				if (!tmpStr1.equalsIgnoreCase(tmpStr2)) {
					// L140M01R.A02=原簽報內容為{0}已被審查單位批覆異動成為{1}
					collMemo.append(MessageFormat.format(
							prop.getProperty("L140M01R.A02"), tmpStr2, tmpStr1));
				} else {
					collMemo.append(tmpStr1);
				}
			} else {
				collMemo.append(tmpStr1);
			}

		} else {
			collMemo.append(tmpStr1);
		}

		if (!"".equals(collMemo.toString())) {
			return collMemo.toString();
		} else {
			return "無。";
		}
	}
	
	private String setL140M01RListDataBefore(String mainId, String AuthLvl) {
		List<Map<String, Object>> maps = null;
		// AuthLvl->1為分行授權內->其它皆為授權外
		if ("1".equals(AuthLvl)) {
			maps = eloandbBASEService.findL140M01RByL120M01A(mainId);
		} else {
			maps = eloandbBASEService.findL140M01RByL120M01AInfeeSrc3(mainId);
		}

		StringBuffer collMemo = new StringBuffer();
		if (maps != null) {
			// feeNo,feeswft,count(*) as tCount,SUM(FEEAMT) AS tSum
			String feeNo = "";
			String feeNoName = "";
			String feeswft = "";
			int tCount = 0;
			int ttCount = 1;
			BigDecimal tSum = BigDecimal.ZERO;
			Map<String, String> feeNoMap = null;

			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1935V01Page.class);

			for (Map<String, Object> map : maps) {
				if (map != null) {
					if (ttCount > 1) {
						collMemo.append("；");
					}
					ttCount = ttCount + 1;
					feeNo = Util.trim(map.get("feeNo"));
					feeswft = Util.trim(map.get("feeswft"));
					tCount = (Integer) map.get("tCount");
					tSum = (BigDecimal) map.get("tSum");
					feeNoMap = codeTypeService.findByCodeType("cls1141_feeNo");
					feeNoName = feeNoMap.get(feeNo);
					// L140M01R.A01={0}共{1}筆合計為{2} {3}元
					collMemo.append(MessageFormat.format(
							prop.getProperty("L140M01R.A01"), feeNoName,
							tCount, feeswft, tSum));
				}
			}
			if (!"".equals(collMemo.toString())) {
				collMemo.append("。");
			} else {
				collMemo.append("無。");
			}
		}
		return collMemo.toString();
	}
	
	private void _l120s12a_append_sb_column2(StringBuffer sb, String s1, String s2){
		sb.append("<tr>");
		sb.append("<td width='50%' >").append(s1).append("</td>");
		sb.append("<td width='50%' >").append(s2).append("</td>");
		sb.append("</tr>");	
	}
	private void _l120s13a_append_sb_column2(StringBuffer sb, String s1, String s2){
		sb.append("<tr>");
		sb.append("<td width='58%' >").append(s1).append("</td>");
		sb.append("<td width='42%' >").append(s2).append("</td>");
		sb.append("</tr>");	
	}
	private void _l120s13a_append_sb_column1(StringBuffer sb, String s1){
		sb.append("<tr>");
		sb.append("<td colspan='2' >").append(s1).append("</td>");
		sb.append("</tr>");	
	}
	private void _l120s12a_append_sb_column3(StringBuffer sb, String s1, String s2, String s3){
		sb.append("<tr>");
		sb.append("<td>").append(s1).append("</td>");
		sb.append("<td style='width:100px;' >").append(s2).append("</td>"); //在 td 加上 nowrap 但沒反應
		sb.append("<td style='width:60px;' >").append(s3).append("</td>");
		sb.append("</tr>");	
	}
	
	private String _l120s12a_itemval_Y(String val, String item_match){
		return (Util.equals(val, "Y")?"■":"□")+item_match;
	}
	private String _l120s12a_itemval_N(String val, String item_notMatch){
		return (Util.equals(val, "N")?"■":"□")+item_notMatch;
	}
	private void _l120s13a_append_sb_column3(StringBuffer sb, String s1, String s2, String s3){
		sb.append("<tr>");
		sb.append("<td>").append(s1).append("</td>");
		sb.append("<td style='width:60px;' >").append(s2).append("</td>"); //在 td 加上 nowrap 但沒反應
		sb.append("<td style='width:60px;' >").append(s3).append("</td>");
		sb.append("</tr>");	
	}
	
	private void _l120s15a_append_sb_column2(StringBuffer sb, String s1, String s2){
		sb.append("<tr>");
		sb.append("<td width='50%' >").append(s1).append("</td>");
		sb.append("<td width='50%' >").append(s2).append("</td>");
		sb.append("</tr>");	
	}
	private void _l120s15a_append_sb_column3(StringBuffer sb, String s1, String s2, String s3){
		sb.append("<tr>");
		sb.append("<td>").append(s1).append("</td>");
		sb.append("<td style='width:60px;' >").append(s2).append("</td>"); //在 td 加上 nowrap 但沒反應
		sb.append("<td style='width:60px;' >").append(s3).append("</td>");
		sb.append("</tr>");	
	}
	private void proc_SimplifyFlag_D_L140M01R(List<String> m01r_dataStr_list, String feeNo, Map<String, String> feeNoMap, BigDecimal feeNoAmt){
		if(feeNoAmt.compareTo(BigDecimal.ZERO)>0 ){
			m01r_dataStr_list.add( LMSUtil.getDesc(feeNoMap, feeNo)+":"+NumConverter.addComma(LMSUtil.pretty_numStr(feeNoAmt)) );
		}
	}

			
	private boolean at_least_one_house_loanNo(Collection<String> collec) {
		return LMSUtil.elm_join(get_HOUSE_LNAP(), get_LNAP(collec)).size() > 0;
	}

	private Set<String> get_LNAP(Collection<String> collec) {
		Set<String> r = new HashSet<String>();
		for (String loanNo : collec) {
			r.add(CrsUtil.getSubjCodeFromLNF030_LOAN_NO(loanNo));
		}
		return r;
	}

	private Set<String> get_HOUSE_LNAP() {
		Set<String> r = new HashSet<String>();
		r.add("273");
		r.add("473");
		r.add("474");
		r.add("673");
		r.add("674");
		return r;
	}
}
