var pageAction = {
    handler: 'cls1161m03formhandler',
    f_mainId : 'c160s03a_sample_for_elf533',
	f_fieldId:'ELF533_SAMPLE',
    grid: null,
    oid: null,
    build: function(){
        pageAction.grid = $("#gridview").iGrid({
            // localFirst: true,
            handler: 'cls1161gridhandler',
            height: 400,
            action: 'queryViewC160M03A',
            rowNum: 17,
            rownumbers: true,
            sortname: 'createTime',
            postData: {
				docStatus: viewstatus
			},
            colModel: [{
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'dupNo',
                hidden: true
            }, {
                name: 'docURL',
                hidden: true
            }, {
                colHeader: i18n.cls1161m03["C160M03A.custId"], // 身分證統編重複碼
                align: "left", width: 50, sortable: true, 
                name: 'custId',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    pageAction.openDoc(rowObject);
                }
            }, {
                colHeader: i18n.cls1161m03["C160M03A.custName"], // 借款人姓名
                align: "left", width: 120, sortable: true, 
                name: 'custName'
            }, {
                colHeader: i18n.cls1161m03["C160M03A.cntrNo"],
                align: "center", width: 60, sortable: true, 
                name: 'cntrNo'
            }, {
                colHeader: i18n.cls1161m03["C160M03A.packNo"],
                align: "center", width: 30,  sortable: true, 
                name: 'packNo'
            }, {
                colHeader: i18n.cls1161m03["C160M03A.totCount"],
                align: "center", width: 45,  sortable: true,
                name: 'totCount'
            }, {
                colHeader: i18n.cls1161m03["C160M03A.updateTime"],
                align: "center", width: 65,  sortable: true, 
                name: 'updateTime'
            }],
            ondblClickRow: function(rowid){
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openDoc(data);
            }
        });
        
        //pageAction.handler
        
        // 新增
        $("#buttonPanel").find("#btnAdd").click(function(){     
        	var _id = "c160m03a_input";
    		var _form = _id+"_form";
    		if ($("#"+_id).length == 0){
    			var dyna = [];
    			dyna.push("<div id='"+_id+"' style='display:none;' >");
    			dyna.push("<form id='"+_form+"'>");
    			dyna.push("	<table class='tb2'><tr><td class='hd2'>額度&nbsp;&nbsp;&nbsp;&nbsp;</td>");
    			dyna.push(" <td>");
    			dyna.push(" <select id='idDupCntrNo' name='idDupCntrNo' >");
    			dyna.push(" <option value=''>"+i18n.def.comboSpace+"</option>");
    			if(userInfo.unitNo=='007' || userInfo.unitNo=='055' ){
        			dyna.push(" <option value='23358846_0_007110500316'>【23358846-0】007110500316 </option>");
        			dyna.push(" <option value='23358846_0_007110500319'>【23358846-0】007110500319 </option>");	
    			}else if(userInfo.unitNo=='201'){
    				dyna.push(" <option value='96964262_0_201110600999'>【96964262-0】201110600999 </option>");
    			}
    			dyna.push(" </select>");
    			dyna.push(" </td></tr></table>");
    			dyna.push("</form>");
    			
    			dyna.push("</div>");
    			
    		     $('body').append(dyna.join(""));
    		}
    		//clear data
    		$("#"+_form).reset();
        	
        	$("#"+_id).thickbox({
    			title : '輸入',
    			width : 400, height : 150, modal : false,
    			align : 'center', valign: 'bottom', i18n: i18n.def,
    			buttons : {
    				'sure' : function(){
    					var $form = $('#'+_form);
    					if ($form.valid()){	
    						$.thickbox.close();
    						pageAction.reloadGrid();
    						
    						$.ajax({
    					        handler: pageAction.handler,
    					        data: $.extend({}, $form.serializeData(), {
    			                    formAction: "addCase"
    			                }),
    					        }).done(function(json){
    					        	pageAction.openDoc(json.data);
    					        	//~~~
                                    pageAction.grid.reload();
    					    	});
    					}
    				},
    				'cancel' : function(){
						$.thickbox.close();    					
    				}
    			}
    		});
        	
        }) // 編輯 & 調閱
.end().find("#btnModify,#btnView").click(function(){
            var data = pageAction.grid.getSingleData();
            if (data) {
                pageAction.openDoc(data);
            }
        }) // 刪除
.end().find("#btnDelete").click(function(){
            var data = pageAction.grid.getSingleData();
            if (data) {
                MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                    if (action) {
                        $.ajax({
                            handler: pageAction.handler,
                            action: 'deleteCase',
                            data: data,
                            }).done(function(response){
                                pageAction.grid.reload();
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                        	});
                    }
                });
            }
        }) // 篩選
.end().find('#btnFilter').click(function(){
            pageAction.openFilter();
        })
			.end().find('#btnUPCls').click(function() {
				var limitFileSize = 3145728;
				//將上傳到 /elnfs/LMS/007/2017/c160s03a_sample_for_elf533/ELF533_SAMPLE/xyz.xls
				MegaApi.uploadDialog({
					fieldId: pageAction.f_fieldId,
					fieldIdHtml: "size='30'", fileDescId: "fileDesc", fileDescHtml: "size='30' maxlength='30'",
					subTitle: i18n.def('insertfileSize', { 'fileSize': (limitFileSize / 1048576).toFixed(2) }),
					limitSize: limitFileSize, width: 320, height: 190,
					data: {
						mainId: pageAction.f_mainId,
						deleteDup: true,
						sysId: "LMS"
					},
					success: function() {
						MegaApi.showMessage("ok");
					}
				});
			})
.end().find('#btnCreate').click(function(){
	$("#expLnfForm [name=procDateB]").val(CommonAPI.getToday());
	$("#expLnfForm [name=procDateE]").val(CommonAPI.getToday());
	
	$("#divExpLnf").thickbox({
		title : '輸入查詢條件',
		width : 480, height : 380, modal : false,
		align : 'center', valign: 'bottom', i18n: i18n.def,
		buttons : {
			'sure' : function(){
				var $form = $('#expLnfForm');
				var fmtType = $form.find("[name=fmtType]:checked").val();
				if ($form.valid()){	
					$.thickbox.close();
							
					var _fileDownloadName = "data.txt";
					if(fmtType=="1A"){
						_fileDownloadName = "916S0.txt";
					}else if(fmtType=="1B"){
						_fileDownloadName = "919S1.txt";
					}else if(fmtType=="2A"){
						_fileDownloadName = "919S0.txt";
					}else if(fmtType=="2B"){
						_fileDownloadName = "919S1.txt";
					}else if(fmtType=="2C"){
						_fileDownloadName = "919S2.txt";
					}else if(fmtType=="2D"){
						_fileDownloadName = "919S3.txt";
					}else if(fmtType=="2E"){
						_fileDownloadName = "919S4.txt";
					}else if(fmtType=="3"){
						_fileDownloadName = "917S.txt";
					}
					//不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
                    $.form.submit({
                   	url: __ajaxHandler,
                		target : "_blank",
                		data : $.extend({
                			_pa : 'lmsdownloadformhandler',    
                 			'fileDownloadName' : _fileDownloadName ,            			
                			'serviceName' : "cls1161v05rptservcie"
                		}, $form.serializeData() )
                	});
				}
			},
			'cancel' : function(){
				$.thickbox.close();    					
			}
		}
	});
		}) 
.end().find('#btnCreateExl').click(function(){
		    $.ajax({
		        handler: pageAction.handler,
		        action: 'fetch_sample_xls',
		        data: {
		        	'f_mainId' : pageAction.f_mainId ,
		        	'f_fieldId' : pageAction.f_fieldId
		        },
		        }).done(function(json_f){
		            if(json_f.fileOid){
		            	ilog.debug("fetch["+json_f.fileOid+"]");
		            	$.capFileDownload({
		    		        target: "_self",
		    		        handler: "simplefiledwnhandler",
		    		        data: {
		    		            fileOid: json_f.fileOid
		    		        }
		    		    });        	
		            }else{
		            	MegaApi.showMessage("Not Foud");
		            }
		    	});
			
		});
    },
    /**
     * 開啟文件
     */
    openDoc: function(data){
        $.form.submit({
            url: data.docURL,
            data: $.extend(data, {
                mainOid: data.oid || '',
                mainDocStatus: viewstatus
            }),
            target: data.oid
        });
    },
    /**
     * 篩選
     */
    openFilter: function(){
    	var _id = "c160m03a_tb";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>請輸入欲查詢之ID：");
			dyna.push("	<table class='tb2'><tr><td class='hd2'>身分證統編&nbsp;&nbsp;&nbsp;&nbsp;</td>");
			dyna.push(" <td>");
			dyna.push(" <input type='text' id='search_custId' name='search_custId' maxlength=10>&nbsp;&nbsp;&nbsp;&nbsp;");
			dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
    	
    	$("#"+_id).thickbox({
			title : i18n.def['query'] || '篩選',
			width : 400, height : 150, modal : false,
			align : 'center', valign: 'bottom', i18n: i18n.def,
			buttons : {
				'sure' : function(){
					var $form = $('#'+_form);
					if ($form.valid()){	
						$.thickbox.close();
						pageAction.reloadGrid($form.serializeData());
					}
				}
			}
		});
    },
    /**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	}
}

$(function(){
    pageAction.build();
});
