<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
        	<style>
				
			</style>
        	<script type="text/javascript">loadScript('pagejs/cls/CLS1161M03Page');</script>
			
           	<div class="button-menu funcContainer" id="buttonPanel">
				
				<!--海外分行 編製中-->
				<th:block th:if="${_btnDOC_EDITING_visible}"><button type="button" id="btnSave">
						<span class="ui-icon ui-icon-jcs-04" ></span>
						<th:block th:text="#{'button.save'}">儲存</th:block>
					</button>
					
					<button type="button" id="btnSend">
						<span class="ui-icon ui-icon-jcs-02" ></span>
						<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
					</button>
				</th:block>
			
				<!--海外分行 待覆核-->
				<th:block th:if="${_btnWAIT_APPROVE_visible}"><button type="button" id="btnAccept">
						<span class="ui-icon ui-icon-check" ></span>
						<th:block th:text="#{'button.check'}">覆核</th:block>
					</button>	
		        </th:block>
				
				<!--海外分行 已覆核-->
				<th:block th:if="${_btn_APPROVED_visible}"><button type="button" id="btnDelApprove">
						<span class="" ></span>
						檢核 a-loan 未引入，退回編製中
					</button>
		        </th:block>
				
				<button id="btnPrint" class="forview">
                    <span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}">列印</th:block>
                </button>
				
				<button type="button" id="btnExit" class="forview">
					<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>						
			</div>	
		
			<div class="tit2 color-black">
				<th:block th:text="#{'title.tab00'}">整批自動開戶 </th:block>： <span id="titleInfo" class="color-blue" ></span>
				
			</div>
            <div id="mainTab" class="tabs doc-tabs">
                <ul>
                	<li id="mainTab01" ><a href="#tab-01" goto="01"><b><th:block th:text="#{'title.tab01'}">文件資訊</th:block></b></a></li>
					<th:block th:if="${_for900View}">
                		<li id="mainTab02" ><a href="#tab-02" goto="02"><b><th:block th:text="#{'title.tab02'}">詳細資料</th:block></b></a></li>                   
                	</th:block>
                	                   
                
                </ul>
                <div class="tabCtx-warp">
                	<form id="tabForm">
                   	 	<div th:id="${tabIdx}" th:insert="${panelName} :: ${panelFragmentName}"></div>
               		</form>
                </div>
            </div>
						

		<!-- 匯入EXCEL -->
		<div id="divXlsBox" style="display:none;" >
			<form id='xlsFrm'>			
			<table class="tb2" width="100%">
				<tr>
					<td width="30%" class="hd2" align="right">
						<button type="button" id="btUploadExcel" >
							<span class="text-only"><th:block th:text="#{'button.btUploadExcel'}">上傳Excel</th:block></span>
						</button>
					</td>
					<td width="70%">
						<input type="hidden" id="excelId" name="excelId" />
						<a href="#" id="downloadExcel" ><th:block th:text="#{'button.view'}">調閱</th:block></a>
						&nbsp;&nbsp;
						<span id="progressTr" class="text-red" style="display:none;" >
							<th:block th:text="#{'label.progress'}">處理進度</th:block>：
							<b><span id="progress" class="field" ></span></b>&nbsp;%
						</span>
					</td>
				</tr>
			</table>
			</form>
		</div>
		
		</th:block>
    </body>
</html>
