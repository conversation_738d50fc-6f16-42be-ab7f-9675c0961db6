/* 
 * C122M01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 進件管理資料檔 **/
@NamedEntityGraph(name = "C122M01F-entity-graph", attributeNodes = { @NamedAttributeNode("c122m01a")})
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C122M01F", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C122M01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * uid<p/>
	 * not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 */
	@Size(max=32)
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 分行代碼 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 客戶統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 申貸戶姓名 **/
	@Size(max=150)
	@Column(name="CUSTNAME", length=150, columnDefinition="VARCHAR(150)")
	private String custName;

	/** 
	 * 引介來源<p/>
	 * 1:行員引介<br/>
	 *  2:房仲業者引介<br/>
	 *  3:金控子公司員工引介<br/>
	 *  4:代書(地政士)引介<br/>
	 *  5:往來企金戶所屬員工<br/>
	 *  6:本行客戶引介<br/>
	 *  7:經總處核准之整批貸款<br/>
	 *  8:分行自辦小規模整批貸款<br/>
	 *  9:本行現有貸款客戶<br/>
	 *  A:本行現有非貸款客戶(與本行已有非貸款往來業務者)<br/>
	 *  N:無(自來件)
	 */
	@Size(max=1)
	@Column(name="INTRODUCESRC", length=1, columnDefinition="CHAR(1)")
	private String introduceSrc;

	/** 引介行員代號 **/
	@Size(max=6)
	@Column(name="MEGAEMPNO", length=6, columnDefinition="VARCHAR(6)")
	private String megaEmpNo;

	/** 引介房仲代號 **/
	@Size(max=5)
	@Column(name="AGNTNO", length=5, columnDefinition="VARCHAR(5)")
	private String agntNo;

	/** 
	 * 引介房仲連鎖店類型<p/>
	 * A:直營店, B:加盟店
	 */
	@Size(max=1)
	@Column(name="AGNTCHAIN", length=1, columnDefinition="CHAR(1)")
	private String agntChain;

	/** 買賣合約書編號 **/
	@Size(max=20)
	@Column(name="DEALCONTRACTNO", length=20, columnDefinition="VARCHAR(20)")
	private String dealContractNo;

	/** 引介子公司代號 **/
	@Size(max=5)
	@Column(name="MEGACODE", length=5, columnDefinition="VARCHAR(5)")
	private String megaCode;

	/** 引介子公司分支代號 **/
	@Size(max=5)
	@Column(name="SUBUNITNO", length=5, columnDefinition="VARCHAR(5)")
	private String subUnitNo;

	/** 引介子公司員工編號 **/
	@Size(max=6)
	@Column(name="SUBEMPNO", length=6, columnDefinition="VARCHAR(6)")
	private String subEmpNo;

	/** 
	 * 引介子公司員工姓名<p/>
	 * 引介來源5、6共用
	 */
	@Size(max=30)
	@Column(name="SUBEMPNM", length=30, columnDefinition="VARCHAR(30)")
	private String subEmpNm;

	/** 
	 * 引介客戶ID/企業統編<p/>
	 * 引介來源5、6共用
	 */
	@Size(max=10)
	@Column(name="INTROCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String introCustId;

	/** 
	 * 引介客戶重複序號<p/>
	 * 引介來源5、6共用
	 */
	@Size(max=1)
	@Column(name="INTRODUPNO", length=1, columnDefinition="CHAR(1)")
	private String introDupNo;

	/** 引介客戶名稱/企業名稱 **/
	@Size(max=120)
	@Column(name="INTROCUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String introCustName;

	/** 引介來源建商名稱 **/
	@Size(max=90)
	@Column(name="INTROBUILDDERNAME", length=90, columnDefinition="VARCHAR(90)")
	private String introBuildDerName;

	/** 引介來源建案名稱 **/
	@Size(max=90)
	@Column(name="INTROBUILDCASENAME", length=90, columnDefinition="VARCHAR(90)")
	private String introBuildCaseName;

	/** 經總處核准整批貸款-整批批號 **/
	@Size(max=32)
	@Column(name="BATCHCODESBR", length=32, columnDefinition="VARCHAR(32)")
	private String batchCodeSbr;

	/** 
	 * 簽案行員編號<p/>
	 * (由派案人員勾選該行所屬同仁)
	 */
	@Size(max=6)
	@Column(name="SIGNMEGAEMPNO", length=6, columnDefinition="VARCHAR(6)")
	private String signMegaEmpNo;

	/** 簽案行員姓名 **/
	@Size(max=30)
	@Column(name="SIGNMEGAEMPNAME", length=30, columnDefinition="VARCHAR(30)")
	private String signMegaEmpName;

	/** 
	 * 有無擔保品<p/>
	 * 有:<br/>
	 *  A: 指定收件行行員<br/>
	 *  B: 委由估價中心<br/>
	 *  C: 委外估價<br/>
	 *  無:N
	 */
	@Size(max=1)
	@Column(name="ESTFLAG", length=1, columnDefinition="CHAR(1)")
	private String estFlag;

	/** 估價行員行編 **/
	@Size(max=6)
	@Column(name="EVAMEGAEMPNO", length=6, columnDefinition="VARCHAR(6)")
	private String evaMegaEmpNo;

	/** 估價行員姓名 **/
	@Size(max=30)
	@Column(name="EVAMEGAEMPNAME", length=30, columnDefinition="VARCHAR(30)")
	private String evaMegaEmpName;

	/** 委外估價單位名稱 **/
	@Size(max=120)
	@Column(name="ESTUNITNAME", length=120, columnDefinition="VARCHAR(120)")
	private String estUnitName;

	/** 地址縣市 **/
	@Size(max=2)
	@Column(name="ESTADDRESSCITY", length=2, columnDefinition="VARCHAR(2)")
	private String estAddressCity;

	/** 地址鄉鎮區域 **/
	@Size(max=3)
	@Column(name="ESTADDRESSAREA", length=3, columnDefinition="VARCHAR(3)")
	private String estAddressArea;

	/** 地址村里 **/
	@Size(max=10)
	@Column(name="ESTADDRESSVILLAGE", length=10, columnDefinition="VARCHAR(10)")
	private String estAddressVillage;

	/** 
	 * 地址街道<p/>
	 * 1=路<br/>
	 *  2=街<br/>
	 *  3=大道<br/>
	 *  4=道
	 */
	@Size(max=1)
	@Column(name="ESTADDRESSSTREET", length=1, columnDefinition="VARCHAR(1)")
	private String estAddressStreet;

	/** 地址段 **/
	@Size(max=3)
	@Column(name="ESTADDRESSSECTION", length=3, columnDefinition="VARCHAR(3)")
	private String estAddressSection;

	/** 地址巷 **/
	@Size(max=3)
	@Column(name="ESTADDRESSLANE", length=3, columnDefinition="VARCHAR(3)")
	private String estAddressLane;

	/** 地址弄 **/
	@Size(max=3)
	@Column(name="ESTADDRESSALLEY", length=3, columnDefinition="VARCHAR(3)")
	private String estAddressAlley;

	/** 地址號 **/
	@Size(max=5)
	@Column(name="ESTADDRESSNO", length=5, columnDefinition="VARCHAR(5)")
	private String estAddressNo;

	/** 地址樓 **/
	@Size(max=3)
	@Column(name="ESTADDRESSFLOOR", length=3, columnDefinition="VARCHAR(3)")
	private String estAddressFloor;

	/** 地址樓之 **/
	@Size(max=3)
	@Column(name="ESTADDRESSLASTNO", length=3, columnDefinition="VARCHAR(3)")
	private String estAddressLastNo;

	/** 地址室 **/
	@Size(max=3)
	@Column(name="ESTADDRESSROOM", length=3, columnDefinition="VARCHAR(3)")
	private String estAddressRoom;

	/** 擔保品座落地址 **/
	@Size(max=500)
	@Column(name="ESTADDRESS", length=500, columnDefinition="VARCHAR(500)")
	private String estAddress;

	/** 第一次執行派案時間 **/
	@Column(name="FIRSTASSIGNEMPTIME", columnDefinition="TIMESTAMP")
	private Timestamp firstAssignEmpTime;
	
	/**
	 * 經辦重複分派之地政士案件
	 * Y：是
	 * N：否
	 * 房貸非地政士引介或信貸案件為NULL或空值
	 */
	@Size(max=1)
	@Column(name="ISSAMEMEGAEMPLAACASE", length=1, columnDefinition="CHAR(1)")
	private String isSameMegaEmpLaaCase;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得uid<p/>
	 * not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 */
	public String getUid() {
		return this.uid;
	}
	/**
	 *  設定uid<p/>
	 *  not null (先放mainid，未來有複製功能的時候在改成複製的mainid)
	 **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 取得分行代碼 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定分行代碼 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得客戶統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定客戶統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得申貸戶姓名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定申貸戶姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得引介來源<p/>
	 * 1:行員引介<br/>
	 *  2:房仲業者引介<br/>
	 *  3:金控子公司員工引介<br/>
	 *  4:代書(地政士)引介<br/>
	 *  5:往來企金戶所屬員工<br/>
	 *  6:本行客戶引介<br/>
	 *  7:經總處核准之整批貸款<br/>
	 *  8:分行自辦小規模整批貸款<br/>
	 *  9:本行現有貸款客戶<br/>
	 *  A:本行現有非貸款客戶(與本行已有非貸款往來業務者)<br/>
	 *  N:無(自來件)
	 */
	public String getIntroduceSrc() {
		return this.introduceSrc;
	}
	/**
	 *  設定引介來源<p/>
	 *  1:行員引介<br/>
	 *  2:房仲業者引介<br/>
	 *  3:金控子公司員工引介<br/>
	 *  4:代書(地政士)引介<br/>
	 *  5:往來企金戶所屬員工<br/>
	 *  6:本行客戶引介<br/>
	 *  7:經總處核准之整批貸款<br/>
	 *  8:分行自辦小規模整批貸款<br/>
	 *  9:本行現有貸款客戶<br/>
	 *  A:本行現有非貸款客戶(與本行已有非貸款往來業務者)<br/>
	 *  N:無(自來件)
	 **/
	public void setIntroduceSrc(String value) {
		this.introduceSrc = value;
	}

	/** 取得引介行員代號 **/
	public String getMegaEmpNo() {
		return this.megaEmpNo;
	}
	/** 設定引介行員代號 **/
	public void setMegaEmpNo(String value) {
		this.megaEmpNo = value;
	}

	/** 取得引介房仲代號 **/
	public String getAgntNo() {
		return this.agntNo;
	}
	/** 設定引介房仲代號 **/
	public void setAgntNo(String value) {
		this.agntNo = value;
	}

	/** 
	 * 取得引介房仲連鎖店類型<p/>
	 * A:直營店, B:加盟店
	 */
	public String getAgntChain() {
		return this.agntChain;
	}
	/**
	 *  設定引介房仲連鎖店類型<p/>
	 *  A:直營店, B:加盟店
	 **/
	public void setAgntChain(String value) {
		this.agntChain = value;
	}

	/** 取得買賣合約書編號 **/
	public String getDealContractNo() {
		return this.dealContractNo;
	}
	/** 設定買賣合約書編號 **/
	public void setDealContractNo(String value) {
		this.dealContractNo = value;
	}

	/** 取得引介子公司代號 **/
	public String getMegaCode() {
		return this.megaCode;
	}
	/** 設定引介子公司代號 **/
	public void setMegaCode(String value) {
		this.megaCode = value;
	}

	/** 取得引介子公司分支代號 **/
	public String getSubUnitNo() {
		return this.subUnitNo;
	}
	/** 設定引介子公司分支代號 **/
	public void setSubUnitNo(String value) {
		this.subUnitNo = value;
	}

	/** 取得引介子公司員工編號 **/
	public String getSubEmpNo() {
		return this.subEmpNo;
	}
	/** 設定引介子公司員工編號 **/
	public void setSubEmpNo(String value) {
		this.subEmpNo = value;
	}

	/** 
	 * 取得引介子公司員工姓名<p/>
	 * 引介來源5、6共用
	 */
	public String getSubEmpNm() {
		return this.subEmpNm;
	}
	/**
	 *  設定引介子公司員工姓名<p/>
	 *  引介來源5、6共用
	 **/
	public void setSubEmpNm(String value) {
		this.subEmpNm = value;
	}

	/** 
	 * 取得引介客戶ID/企業統編<p/>
	 * 引介來源5、6共用
	 */
	public String getIntroCustId() {
		return this.introCustId;
	}
	/**
	 *  設定引介客戶ID/企業統編<p/>
	 *  引介來源5、6共用
	 **/
	public void setIntroCustId(String value) {
		this.introCustId = value;
	}

	/** 
	 * 取得引介客戶重複序號<p/>
	 * 引介來源5、6共用
	 */
	public String getIntroDupNo() {
		return this.introDupNo;
	}
	/**
	 *  設定引介客戶重複序號<p/>
	 *  引介來源5、6共用
	 **/
	public void setIntroDupNo(String value) {
		this.introDupNo = value;
	}

	/** 取得引介客戶名稱/企業名稱 **/
	public String getIntroCustName() {
		return this.introCustName;
	}
	/** 設定引介客戶名稱/企業名稱 **/
	public void setIntroCustName(String value) {
		this.introCustName = value;
	}

	/** 取得引介來源建商名稱 **/
	public String getIntroBuildDerName() {
		return this.introBuildDerName;
	}
	/** 設定引介來源建商名稱 **/
	public void setIntroBuildDerName(String value) {
		this.introBuildDerName = value;
	}

	/** 取得引介來源建案名稱 **/
	public String getIntroBuildCaseName() {
		return this.introBuildCaseName;
	}
	/** 設定引介來源建案名稱 **/
	public void setIntroBuildCaseName(String value) {
		this.introBuildCaseName = value;
	}

	/** 取得經總處核准整批貸款-整批批號 **/
	public String getBatchCodeSbr() {
		return this.batchCodeSbr;
	}
	/** 設定經總處核准整批貸款-整批批號 **/
	public void setBatchCodeSbr(String value) {
		this.batchCodeSbr = value;
	}

	/** 
	 * 取得簽案行員編號<p/>
	 * (由派案人員勾選該行所屬同仁)
	 */
	public String getSignMegaEmpNo() {
		return this.signMegaEmpNo;
	}
	/**
	 *  設定簽案行員編號<p/>
	 *  (由派案人員勾選該行所屬同仁)
	 **/
	public void setSignMegaEmpNo(String value) {
		this.signMegaEmpNo = value;
	}

	/** 取得簽案行員姓名 **/
	public String getSignMegaEmpName() {
		return this.signMegaEmpName;
	}
	/** 設定簽案行員姓名 **/
	public void setSignMegaEmpName(String value) {
		this.signMegaEmpName = value;
	}

	/** 
	 * 取得有無擔保品<p/>
	 * 有:<br/>
	 *  A: 指定收件行行員<br/>
	 *  B: 委由估價中心<br/>
	 *  C: 委外估價<br/>
	 *  無:N
	 */
	public String getEstFlag() {
		return this.estFlag;
	}
	/**
	 *  設定有無擔保品<p/>
	 *  有:<br/>
	 *  A: 指定收件行行員<br/>
	 *  B: 委由估價中心<br/>
	 *  C: 委外估價<br/>
	 *  無:N
	 **/
	public void setEstFlag(String value) {
		this.estFlag = value;
	}

	/** 取得估價行員行編 **/
	public String getEvaMegaEmpNo() {
		return this.evaMegaEmpNo;
	}
	/** 設定估價行員行編 **/
	public void setEvaMegaEmpNo(String value) {
		this.evaMegaEmpNo = value;
	}

	/** 取得估價行員姓名 **/
	public String getEvaMegaEmpName() {
		return this.evaMegaEmpName;
	}
	/** 設定估價行員姓名 **/
	public void setEvaMegaEmpName(String value) {
		this.evaMegaEmpName = value;
	}

	/** 取得委外估價單位名稱 **/
	public String getEstUnitName() {
		return this.estUnitName;
	}
	/** 設定委外估價單位名稱 **/
	public void setEstUnitName(String value) {
		this.estUnitName = value;
	}

	/** 取得地址縣市 **/
	public String getEstAddressCity() {
		return this.estAddressCity;
	}
	/** 設定地址縣市 **/
	public void setEstAddressCity(String value) {
		this.estAddressCity = value;
	}

	/** 取得地址鄉鎮區域 **/
	public String getEstAddressArea() {
		return this.estAddressArea;
	}
	/** 設定地址鄉鎮區域 **/
	public void setEstAddressArea(String value) {
		this.estAddressArea = value;
	}

	/** 取得地址村里 **/
	public String getEstAddressVillage() {
		return this.estAddressVillage;
	}
	/** 設定地址村里 **/
	public void setEstAddressVillage(String value) {
		this.estAddressVillage = value;
	}

	/** 
	 * 取得地址街道<p/>
	 * 1=路<br/>
	 *  2=街<br/>
	 *  3=大道<br/>
	 *  4=道
	 */
	public String getEstAddressStreet() {
		return this.estAddressStreet;
	}
	/**
	 *  設定地址街道<p/>
	 *  1=路<br/>
	 *  2=街<br/>
	 *  3=大道<br/>
	 *  4=道
	 **/
	public void setEstAddressStreet(String value) {
		this.estAddressStreet = value;
	}

	/** 取得地址段 **/
	public String getEstAddressSection() {
		return this.estAddressSection;
	}
	/** 設定地址段 **/
	public void setEstAddressSection(String value) {
		this.estAddressSection = value;
	}

	/** 取得地址巷 **/
	public String getEstAddressLane() {
		return this.estAddressLane;
	}
	/** 設定地址巷 **/
	public void setEstAddressLane(String value) {
		this.estAddressLane = value;
	}

	/** 取得地址弄 **/
	public String getEstAddressAlley() {
		return this.estAddressAlley;
	}
	/** 設定地址弄 **/
	public void setEstAddressAlley(String value) {
		this.estAddressAlley = value;
	}

	/** 取得地址號 **/
	public String getEstAddressNo() {
		return this.estAddressNo;
	}
	/** 設定地址號 **/
	public void setEstAddressNo(String value) {
		this.estAddressNo = value;
	}

	/** 取得地址樓 **/
	public String getEstAddressFloor() {
		return this.estAddressFloor;
	}
	/** 設定地址樓 **/
	public void setEstAddressFloor(String value) {
		this.estAddressFloor = value;
	}

	/** 取得地址樓之 **/
	public String getEstAddressLastNo() {
		return this.estAddressLastNo;
	}
	/** 設定地址樓之 **/
	public void setEstAddressLastNo(String value) {
		this.estAddressLastNo = value;
	}

	/** 取得地址室 **/
	public String getEstAddressRoom() {
		return this.estAddressRoom;
	}
	/** 設定地址室 **/
	public void setEstAddressRoom(String value) {
		this.estAddressRoom = value;
	}

	/** 取得擔保品座落地址 **/
	public String getEstAddress() {
		return this.estAddress;
	}
	/** 設定擔保品座落地址 **/
	public void setEstAddress(String value) {
		this.estAddress = value;
	}
	
	/** 第一次執行派案時間 **/
	public Timestamp getFirstAssignEmpTime() {
		return firstAssignEmpTime;
	}
	/** 第一次執行派案時間 **/
	public void setFirstAssignEmpTime(Timestamp firstAssignEmpTime) {
		this.firstAssignEmpTime = firstAssignEmpTime;
	}
	
	/** 取得經辦重複分派之地政士案件 **/
	public String getIsSameMegaEmpLaaCase() {
		return isSameMegaEmpLaaCase;
	}
	/** 設定經辦重複分派之地政士案件 **/
	public void setIsSameMegaEmpLaaCase(String isSameMegaEmpLaaCase) {
		this.isSameMegaEmpLaaCase = isSameMegaEmpLaaCase;
	}
	
	/**
	 * join 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false)})
	private C122M01A c122m01a;

	public void setC122m01a(C122M01A c122m01a) {
		this.c122m01a = c122m01a;
	}

	public C122M01A getC122m01a() {
		return c122m01a;
	}
}
