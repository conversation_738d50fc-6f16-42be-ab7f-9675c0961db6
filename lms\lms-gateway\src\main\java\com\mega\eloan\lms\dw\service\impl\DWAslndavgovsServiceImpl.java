package com.mega.eloan.lms.dw.service.impl;

import java.util.Calendar;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dw.service.DWAslndavgovsService;

@Service
public class DWAslndavgovsServiceImpl extends AbstractDWJdbc implements
		DWAslndavgovsService  {

	public List<?> findAslndavgovsJoinLnquotov(String custId,String dupNo){
		return this.getJdbc().queryForList("DW_ASLNDAVGOVS.joinDW_LNQUOTOV", new Object[] { custId+dupNo });
	}

	public List<?> findAslndavgovsSelQuotano(String custId,String dupNo){
		return this.getJdbc().queryForList("DW_ASLNDAVGOVS.selQuotano", new Object[] { custId+dupNo });
	}
	
	public List<?> findLnquotovJoinAslndavgovs(String branch,String custId,String dupNo){
		String custKey = "";
		if("0".equals(Util.trim(dupNo))){
			custKey = custId;
		}else{
			custKey = custId + dupNo;
		}
		return this.getJdbc().queryForList("DW_LNQUOTOV.joinDW_ASLNDAVGOVS", new Object[] { branch,custKey });
	}

	@Override
	public List<Map<String,Object>> findLNQUOTOVselFACT_AMT_T(String custId,String dupNo,String branchId){
		return getJdbc().queryForList("LNQUOTOV.joinASLNDAVGOVS", new Object[] { branchId,custId+dupNo });
	}
	
	public List<?> findAslndavgovsSelStatus(String loanNo){
		return this.getJdbc().queryForList("DW_ASLNDAVGOVS.selStatus", new Object[] { loanNo });
	}
	
	public List<?> findLnquotovSelFactAmt(String custId,String dupNo,String contract){
		return this.getJdbc().queryForList("DW_LNQUOTOV.selFactAmt", new Object[] { custId+dupNo,contract });
	}
	
	public List<?> findLnquotovSelLnbal(String custId,String dupNo){
		return this.getJdbc().queryForList("DW_LNQUOTOV.selLnbal", new Object[] { custId+dupNo });
	}
	
	public List<?> findAslndavqovsSelActcd(String loanNo){
		return this.getJdbc().queryForList("ASLNDAVGOVS.selACTCD", new Object[] { loanNo });
	}
	
	public Map<String,Object> findLnquotovSelRevovle(String custId,String dupNo){
		String custKey = "";
		if("0".equals(Util.trim(dupNo))){
			custKey = custId;
		}else{
			custKey = custId + dupNo;
		}
		return this.getJdbc().queryForMap("DW_LNQUOTOV.selRevovle", new Object[] { custKey });
	}

	@Override
	public List<Map<String, Object>> getByCustIdBrNo(String custId, String brCd) {
		return getJdbc().queryForList("DW_ASLNDAVGOVS.selectByCustIdBrNo",
				new String[] { custId, brCd, brCd });
	}
	
	@Override
	public Map<String,Object> findSumFact(String custId,String dupNo){
		String custKey = "";
		if("0".equals(Util.trim(dupNo))){
			custKey = custId;
		}else{
			custKey = custId + dupNo;
		}
		return this.getJdbc().queryForMap("DW_LNQUOTOV.selSumFact", new Object[] { custKey });
	}
	
	@Override
	public Map<String,Object> findCstate(String custId,String dupNo, String branch){
		String custKey = "";
		if("0".equals(Util.trim(dupNo))){
			custKey = custId;
		}else{
			custKey = custId + dupNo;
		}
		return this.getJdbc().queryForMap("DW_LNQUOTOV.selCstate", new Object[] { custKey, branch, custKey, branch });
	}
	
	@Override
	public List<Map<String, Object>> findPreviousDayCreditDetailByCustId(
			String custId, String dupNo) {
		// SQL1 Get額度序號/科目/授信餘額/授信額度/幣別
		if ("0".equals(dupNo)) {
			dupNo = "";
		}
		List<Map<String, Object>> query1 = getJdbc().queryForList(
				"ASLNAVGOVS_SS.findPrevDayCrdDetailStep1",
				new String[] { custId + dupNo });
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MONTH, -1);
		cal.get(Calendar.MONTH);
		if (!query1.isEmpty()) {
			// SQL2 Get年度動用率 dwadm.DW_LNMRATEOVS
			Map<String, Object> query2 = null;
			String dateStart = CapDate.formatDate(cal.getTime(), "yyyy-MM")
					+ "-" + cal.getActualMinimum(Calendar.DATE);
			String dateEnd = CapDate.formatDate(cal.getTime(), "yyyy-MM") + "-"
					+ cal.getActualMaximum(Calendar.DATE);
			for (Map<String, Object> data : query1) {
				query2 = getJdbc().queryForMap(
						"DW_LNMRATEOVS.findPrevDayCrdDetailStep2",
						new Object[] { custId + dupNo, data.get("FACT_CONTR"),
								dateStart, dateEnd }, "MRA_");
				if (query2 != null) {
					data.put("USED_RTEY", query2.get("USED_RTEY"));
				} else {
					data.put("USED_RTEY", null);
				}
			}
			return query1;
		}
		return null;
	}	
}
