/* 
 * LMS140MServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.File;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.gwclient.DWUCB1FTPClient;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMS2105V01Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.L180R60ADao;
import com.mega.eloan.lms.dao.impl.C240M01ADaoImpl;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.service.LMS9100GService;
import com.mega.eloan.lms.lms.service.LMS9101GService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180R60A;
import com.mega.eloan.lms.obsdb.service.ObsdbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 央行註記異動作業
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS2105V01ServiceImpl extends AbstractCapService implements
		LMS2105V01Service {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS2105V01ServiceImpl.class);

	@Resource
	L180R60ADao l180r60aDao;

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	LMSService lmsService;

	@Resource
	L170M01ADao l170m01aDao;

	@Resource
	RetrialService retrialService;

	@Resource
	C240M01ADaoImpl c240m01aDao;

	@Resource
	DocFileService docFileService;
	@Resource
	DocFileDao docFileDao;

	@Resource
	BranchService branchService;

	@Resource
	ObsdbBASEService obsDBService;

	@Resource
	MisdbBASEService misdbBASEService;
	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	DocFileService fileService;

	@Resource
	DWUCB1FTPClient ftpClient;

	@Resource
	LMS9100GService lms9100gService;
	@Resource
	LMS9101GService lms9101gService;

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L180R60A.class) {
			return l180r60aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L180R60A) {
					((L180R60A) model).setUpdater(user.getUserId());
					((L180R60A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l180r60aDao.save((L180R60A) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L180R60A.class) {
			return l180r60aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L180R60A.class) {
			return (T) l180r60aDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L180R60A) {
					l180r60aDao.delete((L180R60A) model);
				}

			}
		}

	}

	/**
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 */
	@Override
	public List<L180R60A> findL180r60aAll() {
		return l180r60aDao.findAll();
	}

	/**
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 * 
	 * @param list
	 */
	@Override
	public void deleteListL180r60a(List<L180R60A> list) {
		l180r60aDao.delete(list);
	}

	/**
	 * J-109-0519_05097_B1001 Web e-Loan產生央行C方案借款人，且兌付振興三倍券達888張之名單
	 * 
	 * @param list
	 */
	@Override
	public void saveListL180r60a(List<L180R60A> list) {
		if (!list.isEmpty()) {
			l180r60aDao.save(list);
		}

	}

	/**
	 * J-110-0304_05097_B1003 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * @param params
	 * @param uFile
	 * @return
	 */
	@Override
	public String rpaUpdateLmsReCheckReport(PageParameters params, MultipartFile uFile) {
		String errorMsg = "";

		String custId = params.getString("custId", "");
		String dupNo = params.getString("dupNo", "");
		String brNo = params.getString("brNo", "");
		String areaNo = params.getString("areaNo", "");
		String custType = params.getString("custType", "");
		String retrialDate = params.getString("retrialDate", "");
		String rpaKey = params.getString("rpaKey", "");
		String pid = rpaKey.split("_")[0]; // "12345678_A"
		String ctlType = rpaKey.split("_")[1]; // "12345678_A"
		String rpaUserId = params.getString("rpaUserId", "");
		String data_searchResult = params.getString("data_searchResult", "");
		String isLast = params.getString("isLast", "");

		// is = uFile.getInputStream();

		if (Util.equals(custType, "1")) {
			// 企金
			errorMsg = this.processLmsRPAreturnData(params, uFile);
		} else {
			// 消金
			errorMsg = this.processClsRPAreturnData(params, uFile);
		}

		// 執行沒有錯誤
		if (Util.equals(Util.trim(errorMsg), "")) {

			// 最後一筆時，要把本日該分行執行的覆審名單註記為RPA已完成
			if (Util.equals(isLast, "Y")) {

				List<L180M01A> l180m01as = l180m01aDao.findMaxDataDate(brNo,
						Util.parseDate(retrialDate));

				if (l180m01as != null && !l180m01as.isEmpty()) {

					for (L180M01A l180m01a : l180m01as) {

						if (l180m01a.getRpaDate() != null
								&& Util.equals(Util.trim(l180m01a.getStatus()),
										"A01")) {
							if (LMSUtil.cmpDate(l180m01a.getRpaDate(), "==",
									Util.parseDate(CapDate
											.getCurrentDate("yyyy-MM-dd")))) {
								// RPA完成
								l180m01a.setStatus("A02");
								retrialService.save(l180m01a);
							}
						}

					}
				}

				List<C240M01A> c240m01as = c240m01aDao.findMaxDataDate(brNo,
						Util.parseDate(retrialDate));

				if (c240m01as != null && !c240m01as.isEmpty()) {

					for (C240M01A c240m01a : c240m01as) {

						if (c240m01a.getRpaDate() != null
								&& Util.equals(Util.trim(c240m01a.getStatus()),
										"A01")) {
							if (LMSUtil.cmpDate(c240m01a.getRpaDate(), "==",
									Util.parseDate(CapDate
											.getCurrentDate("yyyy-MM-dd")))) {
								// RPA完成
								c240m01a.setStatus("A02");
								retrialService.save(c240m01a);
							}
						}
					}
				}
			}
		}

		return errorMsg;

	}

	private String processLmsRPAreturnData(PageParameters params, MultipartFile uFile) {
		String custId = params.getString("custId", "");
		String dupNo = params.getString("dupNo", "");
		String brNo = params.getString("brNo", "");
		String areaNo = params.getString("areaNo", "");
		String custType = params.getString("custType", "");
		String retrialDate = params.getString("retrialDate", "");
		String rpaKey = params.getString("rpaKey", "");
		String pid = rpaKey.split("_")[0]; // "12345678_A"
		String ctlType = rpaKey.split("_")[1]; // "12345678_A"
		String rpaUserId = params.getString("rpaUserId", "");
		String data_searchResult = params.getString("data_searchResult", "");
		String isLast = params.getString("isLast", "");
		String chkCheck = params.getString("chkCheck"); // 是否有支存Y/N
		// (是/否)
		String chkResult = params.getString("chkResult"); // 支存戶況是否正常
		// Y/N
		// (是/否)
		String responseCode = params.getString("responseCode");
		String responseMsg = params.getString("responseMsg");

		InputStream is = null;

		logger.info("傳入參數==>[{}]", custId + "|" + dupNo + "|" + brNo + "|"
				+ areaNo + "|" + custType + "|" + retrialDate + "|" + rpaKey
				+ "|" + rpaUserId + "|" + isLast);

		List<L180M01A> l180m01as = l180m01aDao.findByBranchIdAndLikeMainId(
				brNo, pid);

		String errorMsg = "";
		boolean findRetrial = false;
		if (l180m01as != null && !l180m01as.isEmpty()) {

			for (L180M01A l180m01a : l180m01as) {

				if (Util.equals(
						Util.getRightStr(Util.trim(l180m01a.getMainId()), 8),
						pid)) {

					L170M01A l170m01a = l170m01aDao.findByPidCustIdDup(
							Util.trim(l180m01a.getMainId()), custId, dupNo,
							ctlType);

					if (l170m01a != null) {
						try {
							// 自動比對債票信及卡信資料
							retrialService.getEjcicReusltRecord(l170m01a, true);
						} catch (Exception e) {
							logger.error(e.getMessage(), e);
						}
						try {
							is = uFile.getInputStream();
							byte[] bytes1 = IOUtils.toByteArray(is);

							// 設定上傳檔案資訊
							DocFile docFile = new DocFile();
							docFile.setBranchId(l180m01a.getOwnBrId());
							docFile.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
							docFile.setMainId(l170m01a.getMainId());
							docFile.setPid(null);
							docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
							docFile.setFieldId("lrs");
							docFile.setDeletedTime(null);
							docFile.setSrcFileName("L224_" + brNo + "-"
									+ custId + "-" + pid + "-"
									+ CapDate.getCurrentDate("yyyy-MM-dd")
									+ ".xlsx");
							docFile.setUploadTime(CapDate.getCurrentTimestamp());
							docFile.setSysId(docFileService.getSysId());
							docFile.setFileSize(bytes1.length);
							docFile.setFileDesc("L224與聯徵查詢結果");
							docFile.setFlag("R");
							docFile.setTotPages(224); // 用來代表是RPA上傳的

							// InputStream is = new
							// ByteArrayInputStream(bytes1);
							// ;
							String fileKey = "";
							int[] dimension = { -1, -1 };
							try {

								if (responseCode.equals("0")) {
									// 設定上傳檔案處理物件
									docFile.setData(bytes1);

									// J-110-0304_05097_B1004 Web
									// e-Loan授信覆審配合RPA作業修改
									// 儲存上傳檔案
									// fileKey = docFileService.save(docFile);

									fileKey = this.deleteExistAndSave(docFile);

									l170m01a.setStatus("A02"); // 查詢完成

									String memo = "";
									if (data_searchResult.equals("0")) {
										memo = "有案件";
									} else {
										memo = "查無資料";
									}
									l170m01a.setReason(Util
											.truncateToFitUtf8ByteLength(
													responseMsg + "-" + memo,
													300));

								} else {
									l170m01a.setStatus("A03"); // 查詢失敗
									l170m01a.setReason(responseMsg);
								}

								// 若是圖檔取得其尺寸
								// dimension =
								// docFileService.getImageDimension(docFile);
							} catch (Exception e) {
								logger.error(e.getMessage(), e);
								throw new CapMessageException("file IO ERROR",
										getClass());
							} finally {
								// if (is != null) {
								// try {
								// is.close();
								// } catch (IOException e) {
								// logger.debug("inputStream close Error",
								// getClass());
								// }
								// }
							}

							if (responseCode.equals("0")) {

								List<L170M01D> list = retrialService
										.findL170M01D_orderBySeq(l170m01a);

								for (L170M01D l170m01d : list) {
									if (Util.equals(l170m01d.getItemNo(),
											"N002")) {
										// 借戶支票存款往來情形（‵0‵支票存款）是否正常？

										if (Util.equals(chkCheck, "Y")) {
											// 有支票存款
											l170m01d.setChkCheck("");
											l170m01d.setChkResult(chkResult);
										} else if (Util.equals(chkCheck, "N")) {
											// 無支票存款
											l170m01d.setChkCheck(chkResult);
											l170m01d.setChkResult("K");
										}
									}

								}

							}

							// J-110-0505_05097_B1001 Web
							// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
							// String LMS_RPA_RETRIAL_PRINT_PDF = Util
							// .trim(lmsService
							// .getSysParamDataValue("LMS_RPA_RETRIAL_PRINT_PDF_LMS"));
							//
							// if (Util.equals(LMS_RPA_RETRIAL_PRINT_PDF, "Y"))
							// {
							// autoPrintCntrNoPdfLms(l170m01a,
							// l180m01a.getOwnBrId());
							// }

							// J-110-0505_05097_B1001 Web
							// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
							String LMS_RPA_RETRIAL_PRINT_PDF = Util
									.trim(lmsService
											.getSysParamDataValue("LMS_RPA_RETRIAL_PRINT_PDF_LMS"));

							if (Util.equals(LMS_RPA_RETRIAL_PRINT_PDF, "Y")) {
								autoPrintCntrNoPdfLms(l170m01a,
										l180m01a.getOwnBrId());
							}

							findRetrial = true;
							retrialService.save(l170m01a);

						} catch (Exception e) {
							errorMsg = e.getMessage();
							logger.error(e.getMessage(), getClass());
							e.printStackTrace();
						}

					}

					l180m01a.setRpaDate(CapDate.getCurrentTimestamp());
					if (Util.notEquals(Util.trim(l180m01a.getStatus()), "A03")) {
						// RPA執行中
						l180m01a.setStatus("A01");
					}
					retrialService.save(l180m01a);

					break;
				}
			}
		}

		if (!findRetrial) {
			errorMsg = "找不到覆審報告表" + custId + "|" + dupNo + "|" + brNo + "|"
					+ custType + "|" + rpaKey;
		}

		return errorMsg;
	}

	private String deleteExistAndSave(DocFile docFile) {
		String fileKey = "";

		String RPA_RETRIAL_DEL_AND_SAVE_FILE = Util.trim(lmsService
				.getSysParamDataValue("RPA_RETRIAL_DEL_AND_SAVE_FILE"));
		if (Util.equals(RPA_RETRIAL_DEL_AND_SAVE_FILE, "Y")) {

			List<DocFile> docFileList = docFileService.findByIDAndName(
					docFile.getMainId(), docFile.getFieldId(), null);

			// L224_041-09477027-84f8fe8a-2021-10-20
			if (docFile.getSrcFileName().length() > 26) {

				for (DocFile oldDocFile : docFileList) {

					if (oldDocFile.getSrcFileName().length() > 26) {

						if (Util.equals(Util.getLeftStr(
								docFile.getSrcFileName(), 26), Util.getLeftStr(
								oldDocFile.getSrcFileName(), 26))) {
							if (Util.equals(
									Util.getRightStr(
											oldDocFile.getSrcFileName(), 5),
									".xlsx")) {
								File file = docFileService
										.getRealFile(oldDocFile);
								if (file.exists()) {
									FileUtils.deleteQuietly(file);
								}

								this.docFileDao.delete(oldDocFile);
							}
						}
					}
				}
			}
		}

		fileKey = docFileService.save(docFile);

		return fileKey;
	}

	private String processClsRPAreturnData(PageParameters params, MultipartFile uFile) {

		String custId = params.getString("custId", "");
		String dupNo = params.getString("dupNo", "");
		String brNo = params.getString("brNo", "");
		String areaNo = params.getString("areaNo", "");
		String custType = params.getString("custType", "");
		String retrialDate = params.getString("retrialDate", "");
		String rpaKey = params.getString("rpaKey", "");
		String pid = rpaKey.split("_")[0]; // "12345678_A"
		String ctlType = rpaKey.split("_")[1]; // "12345678_A"
		String rpaUserId = params.getString("rpaUserId", "");
		String data_searchResult = params.getString("data_searchResult", "");
		String isLast = params.getString("isLast", "");
		String chkCheck = params.getString("chkCheck"); // 是否有支存Y/N
		// (是/否)
		String chkResult = params.getString("chkResult"); // 支存戶況是否正常
		// Y/N
		// (是/否)
		String responseCode = params.getString("responseCode");
		String responseMsg = params.getString("responseMsg");

		InputStream is = null;

		logger.info("傳入參數==>[{}]", custId + "|" + dupNo + "|" + brNo + "|"
				+ areaNo + "|" + custType + "|" + retrialDate + "|" + rpaKey
				+ "|" + rpaUserId + "|" + isLast);

		List<C240M01A> c240m01as = c240m01aDao.findByBranchIdAndLikeMainId(
				brNo, pid);

		String errorMsg = "";
		boolean findRetrial = false;
		if (c240m01as != null && !c240m01as.isEmpty()) {

			for (C240M01A c240m01a : c240m01as) {

				if (Util.equals(
						Util.getRightStr(Util.trim(c240m01a.getMainId()), 8),
						pid)) {

					List<C241M01A> c241m01as = retrialService
							.findC241M01A_C240M01A_idDup(c240m01a.getMainId(),
									custId, dupNo);

					for (C241M01A c241m01a : c241m01as) {

						if (c241m01a != null) {
							try {
								// 自動比對債票信及卡信資料
								retrialService.getEjcicReusltRecord(c241m01a,
										true);
							} catch (Exception e) {
								logger.error(e.getMessage(), e);
							}
							try {
								is = uFile.getInputStream();
								byte[] bytes1 = IOUtils.toByteArray(is);

								// 設定上傳檔案資訊
								DocFile docFile = new DocFile();
								docFile.setBranchId(c241m01a.getOwnBrId());
								docFile.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
								docFile.setMainId(c241m01a.getMainId());
								docFile.setPid(null);
								docFile.setCrYear(CapDate
										.getCurrentDate("yyyy"));
								docFile.setFieldId("crs");
								docFile.setDeletedTime(null);
								docFile.setSrcFileName("L224_" + brNo + "-"
										+ custId + "-" + pid + "-"
										+ CapDate.getCurrentDate("yyyy-MM-dd")
										+ ".xlsx");
								docFile.setUploadTime(CapDate
										.getCurrentTimestamp());
								docFile.setSysId(docFileService.getSysId());
								docFile.setFileSize(bytes1.length);
								docFile.setFileDesc("L224與聯徵查詢結果");
								docFile.setTotPages(224);

								// InputStream is = new ByteArrayInputStream(
								// bytes1);
								// ;
								String fileKey = "";
								int[] dimension = { -1, -1 };
								try {

									if (responseCode.equals("0")) {
										// 設定上傳檔案處理物件
										docFile.setData(bytes1);

										// 儲存上傳檔案
										// J-110-0304_05097_B1004 Web
										// e-Loan授信覆審配合RPA作業修改
										// 儲存上傳檔案
										// fileKey =
										// docFileService.save(docFile);

										fileKey = this
												.deleteExistAndSave(docFile);

										c241m01a.setStatus("A02"); // 查詢完成

										String memo = "";
										if (data_searchResult.equals("0")) {
											memo = "有案件";
										} else {
											memo = "查無資料";
										}
										c241m01a.setReason(Util
												.truncateToFitUtf8ByteLength(
														responseMsg + "-"
																+ memo, 300));

									} else {
										c241m01a.setStatus("A03"); // 查詢失敗
										c241m01a.setReason(responseMsg);
									}

									// 若是圖檔取得其尺寸
									// dimension =
									// docFileService.getImageDimension(docFile);
								} catch (Exception e) {
									logger.error(e.getMessage(), e);
									throw new CapMessageException(
											"file IO ERROR", getClass());
								} finally {
									// if (is != null) {
									// try {
									// is.close();
									// } catch (IOException e) {
									// logger.debug(
									// "inputStream close Error",
									// getClass());
									// }
									// }
								}

								// J-110-0505_05097_B1001 Web
								// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
								// String LMS_RPA_RETRIAL_PRINT_PDF = Util
								// .trim(lmsService
								// .getSysParamDataValue("LMS_RPA_RETRIAL_PRINT_PDF_CLS"));
								//
								// if (Util.equals(LMS_RPA_RETRIAL_PRINT_PDF,
								// "Y")) {
								// autoPrintCntrNoPdfCls(c241m01a, c240m01a,
								// c240m01a.getOwnBrId());
								// }

								// J-110-0505_05097_B1001 Web
								// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
								String LMS_RPA_RETRIAL_PRINT_PDF = Util
										.trim(lmsService
												.getSysParamDataValue("LMS_RPA_RETRIAL_PRINT_PDF_CLS"));

								if (Util.equals(LMS_RPA_RETRIAL_PRINT_PDF, "Y")) {
									autoPrintCntrNoPdfCls(c241m01a, c240m01a,
											c240m01a.getOwnBrId());
								}

								findRetrial = true;
								retrialService.save(c241m01a);

							} catch (Exception e) {
								errorMsg = e.getMessage();
								logger.error(e.getMessage(), getClass());
								e.printStackTrace();
							}
							break;
						}

					}

					c240m01a.setRpaDate(CapDate.getCurrentTimestamp());
					if (Util.notEquals(Util.trim(c240m01a.getStatus()), "A03")) {
						// RPA執行中
						c240m01a.setStatus("A01");
					}
					retrialService.save(c240m01a);
					break;
				}

			}
		}

		if (!findRetrial) {
			errorMsg = "找不到覆審報告表" + custId + "|" + dupNo + "|" + brNo + "|"
					+ custType + "|" + rpaKey;
		}

		return errorMsg;
	}

	/**
	 * J-111-0423_05097_B1001 Web
	 * e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
	 * 
	 * @param listData
	 * @return
	 */
	@Override
	public String processEsgFile(List<Map<String, String>> dataList) {
		String errMsg = "";
		String cntrNo = "";
		String esgSustainLoan = "";
		String esgSustainLoanType_E = "";
		String esgSustainLoanType_S = "";
		String esgSustainLoanType_G = "";
		String esgSustainLoanUnReach = "";

		for (Map<String, String> rowData : dataList) {

			cntrNo = Util.trim(MapUtils.getString(rowData, "cntrNo", ""));
			esgSustainLoan = Util.trim(MapUtils.getString(rowData,
					"esgSustainLoan", ""));
			esgSustainLoanType_E = Util.trim(MapUtils.getString(rowData,
					"esgSustainLoanType_E", ""));
			esgSustainLoanType_S = Util.trim(MapUtils.getString(rowData,
					"esgSustainLoanType_S", ""));
			esgSustainLoanType_G = Util.trim(MapUtils.getString(rowData,
					"esgSustainLoanType_G", ""));
			esgSustainLoanUnReach = Util.trim(MapUtils.getString(rowData,
					"esgSustainLoanUnReach", ""));

			if (Util.equals(cntrNo, "")) {
				continue;
			}

			StringBuffer tEsgSustainLoanType = new StringBuffer("");

			if (Util.notEquals(esgSustainLoanType_E, "")) {
				if (Util.notEquals(tEsgSustainLoanType.toString(), "")) {
					tEsgSustainLoanType.append("|");
				}
				tEsgSustainLoanType.append("E");
			}

			if (Util.notEquals(esgSustainLoanType_S, "")) {
				if (Util.notEquals(tEsgSustainLoanType.toString(), "")) {
					tEsgSustainLoanType.append("|");
				}
				tEsgSustainLoanType.append("S");
			}

			if (Util.notEquals(esgSustainLoanType_G, "")) {
				if (Util.notEquals(tEsgSustainLoanType.toString(), "")) {
					tEsgSustainLoanType.append("|");
				}
				tEsgSustainLoanType.append("G");
			}

			if (Util.equals(tEsgSustainLoanType.toString(), "")) {
				continue;
			}

			String esgSustainLoanType = lmsService
					.convertEsgSustainLoanTypeToAloan(tEsgSustainLoanType
							.toString());

			// ELF383 永續績效連結授信類別
			// 1.E環境
			// 2.S社會責任
			// 3.G公司治理
			// 4.ES環境及社會責任
			// 5.EG環境及公司治理
			// 6.SG社會責任及公司治理
			// 7.ESG環境、社會責任及公司治理

			misdbBASEService.updateEsgDataFromLms2105v01ServiceImpl(cntrNo,
					esgSustainLoan, esgSustainLoanType, esgSustainLoanUnReach);

			// ELOAN 永續績效連結授信類別 E|S|G
			eloandbBASEService.updateEsgDataFromLms2105v01ServiceImpl(cntrNo,
					esgSustainLoan, tEsgSustainLoanType.toString(),
					esgSustainLoanUnReach);

			String brNo = Util.getLeftStr(cntrNo, 3);
			if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(brNo)
					.getBrNoFlag())) {

				// ELF383 永續績效連結授信類別
				// 1.E環境
				// 2.S社會責任
				// 3.G公司治理
				// 4.ES環境及社會責任
				// 5.EG環境及公司治理
				// 6.SG社會責任及公司治理
				// 7.ESG環境、社會責任及公司治理
				obsDBService.updateEsgDataFromLms2105v01ServiceImpl(brNo,
						cntrNo, esgSustainLoan, esgSustainLoanType,
						esgSustainLoanUnReach);
			}

		}

		return errMsg;

	}

	@Override
	public List<DocFile> processBatGutFile(List<Map<String, Object>> dataList,
			String mainId, String ssoUnitNo) {
		// 組成匯出文字資料。
		StringBuffer fileString = new StringBuffer();
		// HEADER
		StringBuilder headerSB = new StringBuilder();
		String ContentType = "application/octet-stream";
		String[] cols = new String[] { "columnKey1", "columnKey2",
				"columnKey3", "columnKey4", "columnKey5" };

		String fileName = "IMEL0117";

		// 設定上傳檔案資訊
		DocFile docFileD = getDocFile(mainId, "batGutUpl", ssoUnitNo);
		DocFile docFileH = getDocFile(mainId, "batGutUpl", ssoUnitNo);
		List<DocFile> ftpFiles = new ArrayList<DocFile>();
		String DATE_FORMAT_STR = "yyyy.MM.dd";

		try {
			// write header
			headerSB.append(
					StringUtils.replace(
							CapDate.getCurrentDate(DATE_FORMAT_STR), ".", ""))
					.append(StringUtils.replace(
							CapDate.getCurrentDate(DATE_FORMAT_STR), ".", ""))
					.append(fileName + ".D")
					.append(StringUtils.replace(
							CapDate.getCurrentDate(DATE_FORMAT_STR + "HHMMSS"),
							".", ""))
					.append(CapString.fillZeroHead(
							String.valueOf(dataList.size()), 9)).append("\n");

			// 開始放查出資料
			if (!CollectionUtils.isEmpty(dataList)) {
				for (Map<String, Object> data : dataList) {

					for (int i = 0; i < cols.length; i++) {
						String col = cols[i];

						String val = MapUtils.getString(data, col);

						if (Util.equals(Util.trim(val), "")) {
							val = "";
						}

						val = val.replace("\n", "").replace("\"", "'")
								.replace(",", "，");
						if (i == cols.length - 1) {
							fileString.append(val);
						} else {
							fileString.append(val).append(",");
						}

					}

					fileString.append("\r\n"); // <--資料量太多會造成out of memory

				}
			}

			byte[] byteArray = null;
			try {
				byteArray = fileString.toString().getBytes("UTF-8");

			} catch (UnsupportedEncodingException e) {
				byteArray = fileString.toString().getBytes(); // <--資料量太多會造成out
																// of memory
			}

			// 產出D檔
			docFileD.setData(byteArray);
			docFileD.setFileDesc(fileName + ".D");
			docFileD.setSrcFileName(fileName + ".D");
			docFileD.setContentType(ContentType);
			fileService.save(docFileD);
			ftpFiles.add(docFileD);

			// 產出H檔
			docFileH.setData(headerSB.toString().getBytes("UTF-8"));
			docFileH.setFileDesc(fileName + ".H");
			docFileH.setSrcFileName(fileName + ".H");
			docFileH.setContentType(ContentType);
			fileService.save(docFileH);
			ftpFiles.add(docFileH);

		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}

		return ftpFiles;

	}

	@Override
	public void sendToFTP(List<DocFile> files, String mainId) {
		ArrayList<String> filePath = new ArrayList<String>();
		ArrayList<String> fileName = new ArrayList<String>();
		if (!CollectionUtils.isEmpty(files)) {
			for (DocFile file : files) {
				filePath.add(fileService.getFilePath(file));
				fileName.add(file.getSrcFileName());
			}
			try {
				String DWFTPUCB1_DIR_PATH = Util.trim(lmsService
						.getSysParamDataValue("DWFTPUCB1_DIR_PATH"));
				ftpClient.send(mainId, filePath.toArray(new String[] {}),
						DWFTPUCB1_DIR_PATH, fileName.toArray(new String[] {}),
						true, true, false);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private DocFile getDocFile(String mainId, String dataType, String ssoUnitNo) {
		// 設定上傳檔案資訊
		DocFile docFile = new DocFile();
		docFile.setBranchId(ssoUnitNo);
		docFile.setMainId(mainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setDeletedTime(null);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setFieldId(dataType);
		docFile.setSysId(fileService.getSysId());

		return docFile;
	}

	private void autoPrintCntrNoPdfLms(L170M01A l170m01a, String brNo) {

		Map<String, String> cntrNo_list = new HashMap<String, String>();
		Set<String> cntrNo_Set = new HashSet<String>();
		List<L170M01B> l170m01b_list = retrialService
				.findL170M01B_orderBy(l170m01a);

		if (l170m01b_list == null || l170m01b_list.isEmpty()) {
			// 若覆審報告表沒有授信額度資料，就先自動引進
			retrialService.importLNtoL170M01B(l170m01a);
			l170m01b_list = retrialService.findL170M01B_orderBy(l170m01a);
		}

		if (CollectionUtils.isNotEmpty(l170m01b_list)) {
			for (L170M01B l170m01b : l170m01b_list) {
				String cntrNo = Util.trim(l170m01b.getCntrNo());
				cntrNo_list.put(cntrNo, "");
				cntrNo_Set.add(cntrNo); // 要查詢的額度Set
			}
		}

		// [下午 01:28] 金至忠(授信審查處,襄理)
		// 我問覆審人員他們是用id查覆審分行已核准簽報書, 拿該分行的額度明細表出來看!(沒有在對額度序號)
		// List<Map<String, Object>> dataList = lms1700Service
		// .findPrint_L140M01A_By_CntrNos(cntrNo_Set);

		if (cntrNo_Set != null && !cntrNo_Set.isEmpty()) {

			List<Map<String, Object>> dataList = retrialService
					.findPrint_L140M01A_By_CustId(l170m01a.getCustId(),
							l170m01a.getDupNo(), cntrNo_Set);
			List<String> paramList = new ArrayList<String>();
			if (dataList != null && !dataList.isEmpty()) {

				for (Map<String, Object> dataMap : dataList) {

					// OID, CUSTID, CNTRNO, MAINID, ENDDATE, ISHEADCHECK
					String OID = Util.trim(MapUtils.getString(dataMap, "OID"));

					String CUSTID = Util.trim(MapUtils.getString(dataMap,
							"CUSTID"));
					String DUPNO = Util.trim(MapUtils.getString(dataMap,
							"DUPNO"));

					String CNTRNO = Util.trim(MapUtils.getString(dataMap,
							"CNTRNO"));

					String ISHEADCHECK = Util.trim(MapUtils.getString(dataMap,
							"ISHEADCHECK"));

					String rptNo = Util.equals("Y", ISHEADCHECK) ? "R13"
							: "R12";

					cntrNo_list.put(CNTRNO, "Y");

					String rptOid = rptNo + "^" + OID + "^" + CUSTID + "^"
							+ DUPNO + "^" + CNTRNO;

					paramList.add(rptOid);

				}

				CapAjaxFormResult result = new CapAjaxFormResult();
				PageParameters params = new CapMvcParameters();
				params.put("rptOid", StringUtils.join(paramList, "\\|"));
				params.put("createFile", true);
				String saveFile = "mainId:" + l170m01a.getMainId() + ";"
						+ "fileId:lrs" + ";" + "class:L170M01A" + ";" + "brNo:"
						+ brNo + ";" + "rpa:Y";
				params.put("saveFile", saveFile);
				try {
					lms9100gService.generateReport(params);
				} catch (Exception e) {
					logger.debug("autoPrintCntrNoPdfLms error  : ", e);
				}

			}
		}

	}

	private void autoPrintCntrNoPdfCls(C241M01A c241m01a, C240M01A c240m01a,
			String brNo) throws CapException {

		Map<String, String> cntrNo_list = new HashMap<String, String>();
		Set<String> cntrNo_Set = new HashSet<String>();
		List<C241M01B> c241m01b_list = retrialService
				.findC241M01B_c241m01a(c241m01a);

		if (c241m01b_list == null || c241m01b_list.isEmpty()) {
			// 若覆審報告表沒有授信額度資料，就先自動引進
			retrialService.importLNtoC241M01B_single(c240m01a, c241m01a);
			c241m01b_list = retrialService.findC241M01B_c241m01a(c241m01a);
		}

		if (CollectionUtils.isNotEmpty(c241m01b_list)) {
			for (C241M01B c241m01b : c241m01b_list) {
				String cntrNo = Util.trim(c241m01b.getQuotaNo());
				cntrNo_list.put(cntrNo, "");
				cntrNo_Set.add(cntrNo); // 要查詢的額度Set
			}
		}

		// [下午 01:28] 金至忠(授信審查處,襄理)
		// 我問覆審人員他們是用id查覆審分行已核准簽報書, 拿該分行的額度明細表出來看!(沒有在對額度序號)
		// List<Map<String, Object>> dataList = lms1700Service
		// .findPrint_L140M01A_By_CntrNos(cntrNo_Set);

		if (cntrNo_Set != null && !cntrNo_Set.isEmpty()) {
			List<Map<String, Object>> dataList = retrialService
					.findPrint_L140M01A_By_CustId(c241m01a.getCustId(),
							c241m01a.getDupNo(), cntrNo_Set);
			List<String> paramList = new ArrayList<String>();
			if (dataList != null && !dataList.isEmpty()) {

				for (Map<String, Object> dataMap : dataList) {

					// OID, CUSTID, CNTRNO, MAINID, ENDDATE, ISHEADCHECK
					String OID = Util.trim(MapUtils.getString(dataMap, "OID"));

					String CUSTID = Util.trim(MapUtils.getString(dataMap,
							"CUSTID"));
					String DUPNO = Util.trim(MapUtils.getString(dataMap,
							"DUPNO"));

					String CNTRNO = Util.trim(MapUtils.getString(dataMap,
							"CNTRNO"));

					String ISHEADCHECK = Util.trim(MapUtils.getString(dataMap,
							"ISHEADCHECK"));

					String rptNo = Util.equals("Y", ISHEADCHECK) ? "R13"
							: "R12";

					cntrNo_list.put(CNTRNO, "Y");

					String rptOid = rptNo + "^" + OID + "^" + CUSTID + "^"
							+ DUPNO + "^" + CNTRNO;

					paramList.add(rptOid);

				}

				CapAjaxFormResult result = new CapAjaxFormResult();
				PageParameters params = new CapMvcParameters();
				params.put("rptOid", StringUtils.join(paramList, "\\|"));
				params.put("createFile", true);
				String saveFile = "mainId:" + c241m01a.getMainId() + ";"
						+ "fileId:crs" + ";" + "class:C241M01A" + ";" + "brNo:"
						+ brNo + ";" + "rpa:Y";
				params.put("saveFile", saveFile);
				try {
					lms9101gService.generateReport(params);
				} catch (Exception e) {
					logger.debug("autoPrintCntrNoPdfCls error  : ", e);
				}
			}
		}

	}

}
