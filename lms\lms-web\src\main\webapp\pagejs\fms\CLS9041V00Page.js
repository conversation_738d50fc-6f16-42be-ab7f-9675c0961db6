var pageAction = {
	grid : null,
	build : function(){
		pageAction.grid = $("#gridview").iGrid({
			handler : 'cls9041gridhandler',
			height : 400,
			action :  "showMenu",
			rowNum:15,
			rownumbers:true,
			colModel : [{
				colHeader : i18n.cls9041v00["stuname"], //承作分行
				name : 'codeDesc', //col.id
				align : "left",
				width : 50, //設定寬度
				sortable : false //是否允許排序
				//formatter : 'click',
				//onclick : function,
			},{
				name : 'codeValue',
				hidden: true
			}],
			ondblClickRow: function(rowid){
				var data = pageAction.grid.getRowData(rowid);
				switch(data.codeValue){
				case '1':case '2':case '3':
					pageAction.openRpt(data);
					break;
					
				case '4':
					pageAction.openWindow(data);
					break;
					
				case '5':
					pageAction.getStuData();
					break;
					
				case '6':
					pageAction.openWindow(data);
					break;
					
				case '7':
					pageAction.openWindow(data);
				}
			}				
		});
	},
	/**
	 * 開啟S1~S3&Q1~Q3
	 */
	openRpt : function(data){
		$.form.submit({
			url : "../fms/cls9041m01",
			target : "_blank",
			data : {
				
			}
		});
	},
	/**
	 * 開啟 就學貸款歷史明細.xls
	 */
	getStuData : function() {
		$.form.submit({
			url : "../fms/cls9041r05.xls",
			target : "_blank",
			data : {
				fileDownloadName : "CLS9041PreList.xls",
				serviceName : "cls9041xlsservice"
			}
		});
	},
	/**
	 * 開啟畫面
	 */
	openWindow : function(data){
		$.form.submit({
			url : "../fms/cls9041m0"+data.codeValue,
			target : "_blank",
			data : {
				
			}
		});
	},
	/**
	 * 取得資料表之選擇列
	 */
	getRowData : function(){
		var row = pageAction.grid.getGridParam('selrow');
		var data;
		if (row) {
			data = pageAction.grid.getRowData(row);
		}else{
			MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["grid.selrow"]);
		}
		return data;
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	}
}

$(function() {
	pageAction.build();
});