/* 
 * AmtUnitEnum.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

import java.math.BigDecimal;

import tw.com.iisi.cap.util.CapMath;

/**
 * <pre>
 * 單位 enum。
 * </pre>
 * 
 * @since 2011/8/4
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/4,Sunkist Wang,new</li>
 *          </ul>
 */
public enum AmtUnitEnum {
    /**
     * 1 元
     */
    DOLOAR("1"),
    /**
     * 100 佰
     */
    HUNDRED("100"),
    /**
     * 1000 仟
     */
    THOUSAND("1000"),
    /**
     * 10000 萬
     */
    TEN_THOUSAND("10000"),
    /**
     * 1000000 佰萬
     */
    ONE_MILLION("1000000"),
    /**
     * 10000000 仟萬
     */
    TEN_MILLION("10000000");

    private String code;

    AmtUnitEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public BigDecimal getBigDecimal() {
        return CapMath.getBigDecimal(code);
    }

    public boolean isEquals(Object other) {
        if (other instanceof String) {
            return code.equals(other);
        } else {
            return super.equals(other);
        }
    }

    public static AmtUnitEnum getEnum(String code) {
        for (AmtUnitEnum enums : AmtUnitEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
