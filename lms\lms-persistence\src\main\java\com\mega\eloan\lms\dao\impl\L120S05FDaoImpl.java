/* 
 * L120S05FDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S05FDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S05F;

/** 借款人集團授信明細檔 **/
@Repository
public class L120S05FDaoImpl extends LMSJpaDao<L120S05F, String>
	implements L120S05FDao {

	@Override
	public L120S05F findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S05F> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S05F> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L120S05F findByUniqueKey(String mainId, String custId, String dupNo, String custId1, String dupNo1){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (custId1 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId1", custId1);
		if (dupNo1 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo1", dupNo1);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120S05F> findByIndex01(String mainId, String custId, String dupNo, String custId1, String dupNo1){
		ISearch search = createSearchTemplete();
		List<L120S05F> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (custId1 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId1", custId1);
		if (dupNo1 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo1", dupNo1);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S05F> findByIndex02(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		List<L120S05F> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public int delModel(String mainId, String custId, String dupNo){
		Query query = getEntityManager().createNamedQuery("L120S05F.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		query.setParameter("CUSTID", custId); //設置參數
		query.setParameter("DUPNO", dupNo); //設置參數
		return query.executeUpdate();
	}
	
}