/* 
 * C140M01ADaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.lms.dao.C140SFFFDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C140M04B;
import com.mega.eloan.lms.model.C140SFFF;

/**
 * <pre>
 * 徵信調查報告書自由格式
 * </pre>
 * 
 * @since 2011/9/23
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
@Repository
public class C140SFFFDaoImpl extends LMSJpaDao<C140SFFF, String> implements
      C140SFFFDao {

	public C140SFFF findC140SFFF(String mainId,String pid,String fieldId,String tab){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		search.addSearchModeParameters(SearchMode.EQUALS, "fieldid",
				fieldId);
		search.addSearchModeParameters(SearchMode.EQUALS, "tab", tab);
		C140SFFF entity = findUniqueOrNone(search);
		return entity;
	}
	
	public List<C140SFFF> findC140SFFF(String mainId,String pid,String tab){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);		
		search.addSearchModeParameters(SearchMode.EQUALS, "tab", tab);
		
		return find(search);
	}
	
	@Override
	public int deleteByMeta(GenericBean meta) {
		Query query = entityManager
				.createNamedQuery("ces140sfff.deleteByMainIdAndPid");
		if(meta instanceof C140M04B){
			query.setParameter("mainId", ((C140M04B)meta).getMainId());
			query.setParameter("pid", ((C140M04B)meta).getUid());
		}
		
		return query.executeUpdate();
	}
}// ;
