package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.dao.utils.SearchParameterUtil;
import tw.com.iisi.cap.util.CapDate;

import com.mega.eloan.lms.dao.C340M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C340M01A;

/** 消金契約書主檔 **/
@Repository
public class C340M01ADaoImpl extends LMSJpaDao<C340M01A, String>
	implements C340M01ADao {

	@Override
	public C340M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C340M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C340M01A> findByCtrTypeContrNumber_OrderByCreateTimeDesc(String ctrType, String contrNumber){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", ctrType);
		search.addSearchModeParameters(SearchMode.EQUALS, "contrNumber", contrNumber);
		//不要排除已有 deletedTime 的資料
		search.addOrderBy("createTime", true);
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C340M01A> findByPloanCtrNo(String ploanCtrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrNo", ploanCtrNo);
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C340M01A> findByPloanCtrNo_OrderByCreateTimeAsc(String ploanCtrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrNo", ploanCtrNo);
		search.addOrderBy("createTime", false);
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C340M01A> findByPloanCtrNo_ctrType(String ploanCtrNo, String ctrType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrNo", ploanCtrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", ctrType);
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C340M01A> findByNotifyT1TS(String[] ctrType_arr){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "ctrType", ctrType_arr);
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "9");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.IS_NULL, "ploanNotifyT1TS", "");
		search.setMaxResults(Integer.MAX_VALUE);
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C340M01A> findByCustId(String custId,String ctrType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", ctrType);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.NOT_EQUALS, "ploanCtrStatus", "2"),
				new SearchModeParameter(SearchMode.IS_NULL, "ploanCtrStatus", ""));
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C340M01A> findByCustId_ctrTypeA_ploanCtrStatus9_tabMainId_orderBy_ploanCtrBegDateDesc(String custId, String tabMainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", "A");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "9");
		search.addSearchModeParameters(SearchMode.EQUALS, "c340m01bs.tabMainId", tabMainId);		
		search.addOrderBy("ploanCtrBegDate", true);
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C340M01A> findByCustId_ctrTypeA_ctrTypeB_ploanCtrStatus9_tabMainId_orderBy_ploanCtrBegDateDesc(String custId, String tabMainId,String[] ctrType_arr){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IN, "ctrType", ctrType_arr);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "9");
		search.addSearchModeParameters(SearchMode.EQUALS, "c340m01bs.tabMainId", tabMainId);
		search.addOrderBy("ploanCtrBegDate", true);
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C340M01A findByCaseMainid(String caseMainid,String[] ctrType_arr){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "caseMainId", caseMainid);
		search.addSearchModeParameters(SearchMode.IN, "ctrType", ctrType_arr);
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "9");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("createTime", true);
		List<C340M01A> list = createQuery(search).getResultList();
		if(list.size()>0){
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<C340M01A> findByCtrTypeA_isNeedACH_misFlag_orderBy_ploanCtrBegDateDesc(){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", "A");
		search.addSearchModeParameters(SearchMode.EQUALS, "isNeedACH", "Y");
		search.addSearchModeParameters(SearchMode.IS_NULL, "misFlag", "");

		search.addOrderBy("ploanCtrBegDate", true);
		List<C340M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C340M01A> findC340M01ACompleteOverExprDate(Date startDate, String[] ctrTypes) {
		ISearch search = this.createSearchTemplete();

		search.addSearchModeParameters(SearchMode.IN, "ctrType", ctrTypes);
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "9");
		//調整"對保完成"後超過14天的契約書，更新haveLnf020狀態
		search.addSearchModeParameters(SearchMode.LESS_THAN, "ploanCtrSignTimeM", CapDate.shiftDaysString(CapDate.getCurrentDate("yyyy-MM-dd"),"yyyy-MM-dd",-14));
		search.addSearchModeParameters(SearchMode.GREATER_THAN, "ploanCtrSignTimeM", startDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "haveLnf020", null);
        search.setMaxResults(Integer.MAX_VALUE);

		return this.find(search);

	}

	@Override
	public C340M01A findByCaseMainid_ctrTypeA(String caseMainid){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "caseMainId", caseMainid);
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", "A");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
        search.addSearchModeParameters(SearchMode.NOT_EQUALS, "ploanCtrStatus", "2");
		search.addOrderBy("createTime", true);

		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C340M01A> findByPloanCtrBegDate_ctrTypeC(String ploanCtrBegDate){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", "C");
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", "05O");
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrBegDate", ploanCtrBegDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "faRpaJobTs", null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime","");
		
		return this.find(search);
	}

	@Override
	public List<C340M01A> findNotifypLoanSendMail_ctrTypeA() {
		ISearch search = this.createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "9");
		search.addSearchModeParameters(SearchMode.EQUALS, "ctrType", "A");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "notifypLoanSendMail", "N");
		search.setMaxResults(Integer.MAX_VALUE);

		return this.find(search);

	}
	@Override
	public List<C340M01A> findNotifypLoanSendMail(String[] ctrTypes) {
		ISearch search = this.createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "9");
		search.addSearchModeParameters(SearchMode.IN, "ctrType", ctrTypes);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "notifypLoanSendMail", "N");
		search.setMaxResults(Integer.MAX_VALUE);

		return this.find(search);

	}

	@Override
	public C340M01A findByCaseMainid_PloanCtrStatus9(String caseMainid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "caseMainId", caseMainid);
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCtrStatus", "9");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("createTime", true);
		List<C340M01A> list = createQuery(search).getResultList();
		if(list.size()>0){
			return list.get(0);
		}
		return null;
	}
}