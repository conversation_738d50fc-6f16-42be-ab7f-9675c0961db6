/* 

 * L140M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import javax.persistence.Query;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01A;

/** 額度明細表主檔 **/
@Repository
public class L140M01ADaoImpl extends LMSJpaDao<L140M01A, String> implements
		L140M01ADao {

	@Override
	public L140M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L140M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("custId");
		return findUniqueOrNone(search);
	}

	@Override
	public L140M01A findByUniqueKey(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("custId");
		return findUniqueOrNone(search);
	}

	@Override
	public L140M01A findOidAndId(String oid, String id) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", id);
		return findUniqueOrNone(search);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findCurrByMainId(String caseMainId, String itemType) {
		Query query = getEntityManager().createNamedQuery("L140M01A.selCurr");
		query.setParameter("MAINID", caseMainId); // 設置參數
		query.setParameter("ITEMTYPE", itemType); // 設置參數
		query.setMaxResults(Integer.MAX_VALUE);
		return query.getResultList();
	}

	@Override
	public List<L140M01A> findByMainIdAndCustId(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String caseMainId,
			String itemType, String docStatus) {
		ISearch search = createSearchTemplete();

		if (docStatus != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
		}
		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.itemType", itemType);
		}

		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("printSeq", false);
		printSeqMap.put("custId", false);
		printSeqMap.put("cntrNo", false);
		search.setOrderBy(printSeqMap);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainIdForPrint(
			String caseMainId, String itemType, String docStatus) {
		ISearch search = createSearchTemplete();

		if (docStatus != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
		}
		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.itemType", itemType);
		}

		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("printSeq", false);
		printSeqMap.put("custId", false);
		printSeqMap.put("cntrNo", false);
		search.setOrderBy(printSeqMap);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainIdOrderByCust(
			String caseMainId, String itemType) {
		ISearch search = createSearchTemplete();

		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.itemType", itemType);
		}

		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("custId");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByMainIdList(String[] mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String caseMainId,
			String[] itemType, String docStatus) {
		ISearch search = createSearchTemplete();
		if (docStatus != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
		}
		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.IN, "l120m01c.itemType",
					itemType);
		}

		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("custId");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListBycntrNo(String cntrNo,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}
	
	@Override
	public List<L140M01A> findL140m01aListBycntrNo_orderByCT(String cntrNo,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("createTime", true);
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L140M01A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140M01A> list = createQuery(L140M01A.class, search)
				.getResultList();

		return list;
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainId(String caseMainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("custId");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainIdAndDocstatus(
			String caseMainId, String[] docstatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);

		search.addSearchModeParameters(SearchMode.IN, "docStatus", docstatus);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public L140M01A findByL120m01cMainIdAndcntrNo(String mainId, String cntrNo,
			String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.itemType",
				itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainIdOrderByIsDerivativesAndHasDerivatives(
			String caseMainId, String itemType, String docStatus) {
		ISearch search = createSearchTemplete();

		if (docStatus != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
		}
		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.itemType", itemType);
		}

		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("isDerivatives", false);
		printSeqMap.put("hasDerivatives", false);
		search.setOrderBy(printSeqMap);
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByL141m01bMainIdForPrint(
			String caseMainId, String itemType, String docStatus) {
		ISearch search = createSearchTemplete();

		if (docStatus != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
		}
		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l141m01b.itemType", itemType);
		}

		search.addSearchModeParameters(SearchMode.EQUALS, "l141m01b.mainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("printSeq", false);
		printSeqMap.put("custId", false);
		printSeqMap.put("cntrNo", false);
		search.setOrderBy(printSeqMap);
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	public L140M01A findByCntrNoForNew(String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.IN, "docStatus",
				new String[] { FlowDocStatusEnum.已核准.getCode(),
						FlowDocStatusEnum.結案.getCode() });

		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "7");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "8");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "7|8");

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "approveTime",
				"");
		search.addOrderBy("approveTime", true);
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}

	/**
	 * 取得簽報書項下符合條件(統編、重複序號與額度序號)的額度明細表
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	@Override
	public List<L140M01A> findByMainIdCustIdCntrno(String mainId,
			String custId, String dupNo, String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	/**
	 * 取得簽報書項下符合條件(統編、重複序號與文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見)的額度明細表 J-104-0270-001
	 * Web e-Loan國內授信管理系統OBU戶檢核要有聯徵虛擬統編才能送呈主管
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @param itemType
	 * @return
	 */
	@Override
	public List<L140M01A> findByMainIdAndCustId(String caseMainId,
			String custId, String dupNo, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.itemType",
				itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);

		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("printSeq", false);
		printSeqMap.put("custId", false);
		printSeqMap.put("cntrNo", false);
		search.setOrderBy(printSeqMap);

		return find(search);
	}

	/**
	 * J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 取得額度性質非不變或解除之額度明細表
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @param itemType
	 * @return
	 */
	@Override
	public List<L140M01A> findByMainIdAndCustIdWithoutProperty7Or8(
			String caseMainId, String custId, String dupNo, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.itemType",
				itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "7");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "8");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "7|8");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	/**
	 * 取得最近一次簽案日期的額度明細表 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param docStatus
	 * @return
	 */
	@Override
	public L140M01A findByCustIdCntrnoDocStatus(String custId, String dupNo,
			String cntrNo, String docStatus) {
		ISearch search = createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addOrderBy("caseDate", true);
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}

	// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
	public List<Object[]> findFullTextSearch(String fxUserId, String fxGroupId,
			String fxCaseDateName, String fxCaseDateS, String fxCaseDateE,
			String fxEndDateS, String fxEndDateE, String fxTypCd,
			String fxDocType, String fxDocKind, String fxDocCode,
			String fxUpdaterName, String fxUpdater, String fxCaseBrId,
			String fxCustId, String fxDocRslt, String fxDocStatus,
			String fxLnSubject, String fxRateText, String fxOtherCondition,
			String fxReportOther, String fxReportReason1,
			String fxReportReason2, String fxAreaOption, String fxCollateral,
			String fxCustName, String fxBusCode, String fxCurr,
			String fxLmtDays1, String fxLmtDays2, String fxRateText1,
			String fxGuarantor, String fxCntrNo, String fxCollateral1,
			String unitType, String fxCrGrade, String fxCrKind,
			String fxBldUse, String fxOnlyLand) {

		// http://127.0.0.1:9081/lms-web/app/scheduler?input={'serviceId':'lmsbatch7840serviceimpl','request':{"mainOid":"D67D9DD1327111E893A0F3E4C0A83B82","updater":"918001","unitNo":"918","docStatus":"05O","filterForm":{"custId":"","custName":"","fromDate":"2016-12-28","endDate":"2017-03-28","approveDateS":"","approveDateE":"","cntrNo":"","fxCurr":"","fxLnSubject":"","fxLmtDays1":"0","fxLmtDays2":"999","fxRateText1":"","fxRateText":"","fxGuarantor":"Y","fxCollateral1":"","fxCollateral":"","fxBusCode":"","fxOtherCondition":"","typCd":"","docType":"","docKind":"","docCode":"","caseBrId":"","updater":"","fxCurrShow":"","fxLnSubjectShow":"","fxRateText1Show":"","fxCollateral1Show":"","fxCrGrade":"3","fxCrKind":""},"unitType":"S","userId":"918001"}}

		/*
		 * 
		 * http://www.dpriver.com/pp/sqlformat.htm
		 * 
		 * SELECT DISTINCT MAINID FROM ( SELECT DISTINCT MAINID FROM
		 * LMS.L140M01B WHERE itemtype = '3' AND itemdscr LIKE '%ＳＩＢＯＲ%' AND
		 * MAINID IN --擔保品 ( SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE
		 * itemtype = '4' AND itemdscr LIKE '%%' AND MAINID IN --其他敘做條件 ( SELECT
		 * DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '2' AND itemdscr
		 * LIKE '%ＳＩＢＯＲ%' AND MAINID IN --企金利(費)率 ( SELECT DISTINCT MAINID FROM
		 * LMS.L140M01A WHERE lnsubject LIKE '%%' AND MAINID IN --企金授信科子目 (
		 * SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C WHERE
		 * LMS.L120M01C.MAINID IN --串額度明細表 ( SELECT DISTINCT MAINID FROM
		 * LMS.L120M01D WHERE ITEMTYPE = '7' AND itemdscr LIKE '%%' AND MAINID
		 * IN --簽報書-營運中心說明及意見 ( SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE
		 * (ITEMTYPE = '4' OR ITEMTYPE = 'E') AND itemdscr LIKE '%%' AND MAINID
		 * IN --簽報書-綜合評估及敘作理由(併於主表) ( SELECT DISTINCT MAINID FROM LMS.L120M01D
		 * WHERE ITEMTYPE = '3' AND itemdscr LIKE '%%' AND MAINID IN --簽報書-其他 (
		 * SELECT DISTINCT MAINID FROM LMS.L120S01B WHERE BusCode = '123456' AND
		 * MAINID IN --行業對象別 ( SELECT DISTINCT MAINID FROM LMS.L120S01A WHERE
		 * LMS.L120S01A.custname LIKE '%%' AND MAINID IN --客戶名稱 (
		 * 
		 * SELECT DISTINCT MAINID FROM LMS.L120A01A WHERE AUTHUNIT = '918' AND
		 * MAINID IN --簽報書-其他 ( (SELECT DISTINCT LMS.L120M01A.MAINID FROM
		 * LMS.L120M01A WHERE LMS.L120M01A.NOTESUP IS NULL AND caseDate BETWEEN
		 * '2012-11-13' AND '2013-11-13' AND ( ( DOCSTATUS = '05O' OR DOCSTATUS
		 * = 'L3G' OR DOCSTATUS = 'L4G' ) ) ) )
		 * 
		 * ) ) ) ) ) ) ) ) ) ) UNION ALL SELECT DISTINCT MAINID FROM ( SELECT
		 * DISTINCT MAINID FROM LMS.L140S02A WHERE (rateDesc LIKE '%%' OR
		 * freeRateDesc LIKE '%%' ) AND MAINID IN --企金利(費)率 ( SELECT DISTINCT
		 * MAINID FROM LMS.l140S02a,lms.C900M01D WHERE lms.C900M01D.subjCode =
		 * LMS.L140S02A.subjCode AND lms.C900M01D.SUBJNM LIKE '%%' AND MAINID IN
		 * --個金授信科目 ( SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C
		 * WHERE LMS.L120M01C.MAINID IN --串額度明細表 ( SELECT DISTINCT MAINID FROM
		 * LMS.C120M01A WHERE LMS.C120M01A.CUSTPOS = 'C' AND
		 * LMS.C120M01A.custname LIKE '%%' AND MAINID IN --客戶名稱 ( SELECT
		 * DISTINCT MAINID FROM LMS.L120A01A WHERE AUTHUNIT = '918' AND MAINID
		 * IN --簽報書-其他 ( (SELECT DISTINCT LMS.L120M01A.MAINID FROM LMS.L120M01A
		 * WHERE LMS.L120M01A.NOTESUP IS NULL AND LMS.L120M01A.docType = '2' AND
		 * caseDate BETWEEN '2012-11-13' AND '2013-11-13' AND ( ( DOCSTATUS =
		 * '05O' OR DOCSTATUS = 'L3G' OR DOCSTATUS = 'L4G' ) ) ) ) ) ) ) ) ) )
		 */

		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// String unitType = user.getUnitType();

		// 企金
		StringBuffer com120 = new StringBuffer();
		StringBuffer com120Condtion = new StringBuffer();
		StringBuffer comCondtion = new StringBuffer();
		StringBuffer comOth1 = new StringBuffer();
		StringBuffer comOth2 = new StringBuffer();
		StringBuffer comOth3 = new StringBuffer();
		StringBuffer comOth4 = new StringBuffer();
		StringBuffer comOth5 = new StringBuffer();
		StringBuffer comOth6 = new StringBuffer();
		StringBuffer comOth7 = new StringBuffer();
		StringBuffer comOth8 = new StringBuffer();
		StringBuffer comOth9 = new StringBuffer();
		StringBuffer comOth10 = new StringBuffer();
		StringBuffer comOth11 = new StringBuffer();
		StringBuffer comOth12 = new StringBuffer();
		StringBuffer comOth13 = new StringBuffer(); // 科目
		StringBuffer comOth14 = new StringBuffer(); // 利費率

		StringBuffer comOth15 = new StringBuffer(); // RIGHT OUTER JOIN CMS

		int rightCount = 0;

		// 判斷有沒有額度明細表條件
		boolean hasCntrDoc = false;
		hasCntrDoc = true; // 永遠要串額度明細表

		// if (!StringUtils.isBlank(fxLnSubject)
		// || !StringUtils.isBlank(fxRateText)
		// || !StringUtils.isBlank(fxOtherCondition)
		// || !StringUtils.isBlank(fxCollateral)
		// || !StringUtils.isBlank(fxCurr)
		// || !StringUtils.isBlank(fxLmtDays1)
		// || !StringUtils.isBlank(fxLmtDays2)
		// || !StringUtils.isBlank(fxRateText1)
		// || !StringUtils.isBlank(fxGuarantor)
		// || !StringUtils.isBlank(fxCntrNo)) {
		// hasCntrDoc = true;
		// }

		// 企金核心條件************************************************************************
		if (!StringUtils.isBlank(fxDocStatus)) {
			com120Condtion.append(" AND ( " + fxDocStatus + " ) ");
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			com120Condtion.append(" AND " + fxCaseDateName
					+ " BETWEEN ?19  AND ?20 ");
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			com120Condtion.append(" AND enddate  BETWEEN ?21 AND ?22 ");
		}

		if (!StringUtils.isBlank(fxTypCd)) {
			com120Condtion.append(" AND typCd = ?1 ");
		}

		if (!StringUtils.isBlank(fxDocType)) {
			com120Condtion.append(" AND docType  = ?2  ");
		}

		if (!StringUtils.isBlank(fxDocKind)) {
			com120Condtion.append(" AND docKind  = ?3  ");
		}

		if (!StringUtils.isBlank(fxDocCode)) {
			com120Condtion.append(" AND docCode  = ?4  ");
		}

		if (!StringUtils.isBlank(fxUpdaterName)
				&& !StringUtils.isBlank(fxUpdater)) {
			com120Condtion.append(" AND " + fxUpdaterName + "  = ?5  ");
		}

		if (!StringUtils.isBlank(fxCaseBrId)) {
			com120Condtion.append(" AND caseBrId = ?6  ");
		}

		// if (!StringUtils.isBlank(fxCustId)) {
		// com120Condtion.append(" AND custId   = ?7  ");
		// }

		if (!StringUtils.isBlank(fxDocRslt)) {
			com120Condtion.append(" AND docRslt   = ?8  ");
		}

		// --判斷簽報書授權
		String authTbl = "";
		if (BranchTypeEnum.授管處.getCode().equals(unitType)
				|| BranchTypeEnum.國金部.getCode().equals(unitType)) {
			authTbl = "";
		} else {
			// J-107-0069-002
			// e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
			authTbl = ""; // 開放全行時要打開
			// authTbl = ",com.BELSBRN";
			// com120Condtion
			// .append(" AND com.BELSBRN.brNo = LMS.L120M01A.CASEBRID  AND com.BELSBRN.brnGroup   = ?9  ");

		}

		com120.append(" ( SELECT DISTINCT LMS.L120M01A.MAINID FROM LMS.L120M01A"
				+ authTbl
				+ " WHERE LMS.L120M01A.DELETEDTIME IS NULL AND LMS.L120M01A.NOTESUP IS NULL "
				+ com120Condtion.toString() + ")");

		// 借款人****************************************************************

		if (!StringUtils.isBlank(fxBusCode)) {
			// 行業對象別T11會用到，所以若條件有戶名或行業對象別，則T10 SQL就要加上去
			rightCount = rightCount + 1;
			comOth3.append(" SELECT DISTINCT MAINID FROM LMS.L120S01B WHERE BusCode = ?11  AND MAINID IN ("); // --行業對象別
		}

		// J-107-0069-001
		// e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
		String crGradeBgn = "";
		String crGradeEnd = "";
		if (!StringUtils.isBlank(fxCrGrade)) {
			// 評等

			if (Util.equals(fxCrGrade, "A")) {
				// A-E
				if (Util.equals(fxCrKind, "1")) {
					// 大型企業
					crGradeBgn = "1";
					crGradeEnd = "6";
				} else {
					// 其他企業
					crGradeBgn = "1";
					crGradeEnd = "7";
				}
			} else if (Util.equals(fxCrGrade, "B")) {
				if (Util.equals(fxCrKind, "1")) {
					// 大型企業
					crGradeBgn = "3";
					crGradeEnd = "8";
				} else {
					// 其他企業
					crGradeBgn = "4";
					crGradeEnd = "9";
				}
			} else if (Util.equals(fxCrGrade, "C")) {
				if (Util.equals(fxCrKind, "1")) {
					// 大型企業
					crGradeBgn = "5";
					crGradeEnd = "10";
				} else {
					// 其他企業
					crGradeBgn = "6";
					crGradeEnd = "11";
				}
			} else if (Util.equals(fxCrGrade, "D")) {
				if (Util.equals(fxCrKind, "1")) {
					// 大型企業
					crGradeBgn = "7";
					crGradeEnd = "11";
				} else {
					// 其他企業
					crGradeBgn = "9";
					crGradeEnd = "12";
				}
			} else if (Util.equals(fxCrGrade, "E")) {
				if (Util.equals(fxCrKind, "1")) {
					// 大型企業
					crGradeBgn = "10";
					crGradeEnd = "13";
				} else {
					// 其他企業
					crGradeBgn = "11";
					crGradeEnd = "13";
				}
			} else {
				// 1-13
				switch (Util.parseInt(fxCrGrade)) {
				// 依文件建檔日期查詢
				case 1:
				case 2:
					// 小於10 若鍵入授信戶等級為1~9級者，則搜尋同類授信對象之「模型評等等級」為與授信戶差距在上下2級以內者
					crGradeBgn = "1";
					crGradeEnd = Integer.toString(Util.parseInt(fxCrGrade) + 2);
					break;
				case 3:
				case 4:
				case 5:
				case 6:
				case 7:
				case 8:
				case 9:
					// 小於10 若鍵入授信戶等級為1~9級者，則搜尋同類授信對象之「模型評等等級」為與授信戶差距在上下2級以內者
					crGradeBgn = Integer.toString(Util.parseInt(fxCrGrade) - 2);
					crGradeEnd = Integer.toString(Util.parseInt(fxCrGrade) + 2);
					break;
				default:
					// 大於10
					crGradeBgn = Integer.toString(Util.parseInt(fxCrGrade) - 1);
					crGradeEnd = Integer.toString(Util.parseInt(fxCrGrade) + 1);
					break;
				}

			}

			StringBuffer gradeStr = new StringBuffer("");
			for (int crGrade = Util.parseInt(crGradeBgn); crGrade <= Util
					.parseInt(crGradeEnd); crGrade++) {
				gradeStr.append(
						(Util.equals(gradeStr.toString(), "") ? "" : ","))
						.append("'").append(Integer.toString(crGrade))
						.append("'");
			}

			rightCount = rightCount + 1;
			comOth2.append(" SELECT DISTINCT MAINID FROM LMS.L120S01C WHERE  LEFT(crdType,1) ='M' AND grade in ("
					+ gradeStr + ") AND MAINID IN ("); // --行業對象別
		}

		// 簽報書***************************************************************
		// 目前用不到
		// if (!StringUtils.isBlank(fxReportOther)) {
		// rightCount = rightCount + 1;
		// comOth4.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE ITEMTYPE = '3' AND itemdscr LIKE ?12 AND MAINID IN  (");
		// // --簽報書-其他
		// }
		//
		// if (!StringUtils.isBlank(fxReportReason1)) {
		// rightCount = rightCount + 1;
		// comOth5.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE (ITEMTYPE = '4' OR ITEMTYPE = 'E') AND itemdscr LIKE ?13 AND MAINID IN (");
		// // --簽報書-綜合評估及敘作理由(併於主表)
		// }
		//
		// if (!StringUtils.isBlank(fxAreaOption)) {
		// rightCount = rightCount + 1;
		// comOth6.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE ITEMTYPE = '7' AND itemdscr LIKE ?14 AND MAINID IN  (");
		// // --簽報書-營運中心說明及意見
		// }

		// 額度明細表相關條件***************************************************************

		rightCount = rightCount + 1;
		comOth7.append(" SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C WHERE LMS.L120M01C.MAINID IN  ("); // --串額度明細表

		// L140M01A相關***************************************************************
		if (!StringUtils.isBlank(fxCustName) || !StringUtils.isBlank(fxCustId)
				|| !StringUtils.isBlank(fxCurr)
				|| !StringUtils.isBlank(fxGuarantor)
				|| !StringUtils.isBlank(fxCntrNo)) {

			rightCount = rightCount + 1;
			comOth12.append(" SELECT DISTINCT MAINID FROM LMS.L140M01A WHERE DOCSTATUS = '030' AND DELETEDTIME IS NULL "); // --企金授信科子目

			if (!StringUtils.isBlank(fxCntrNo)) {
				comOth12.append(" AND cntrNo = ?15");
			}

			if (!StringUtils.isBlank(fxCurr)) {
				// 幣別
				String[] currArr = fxCurr.split("\\|");
				int currCount = 2900;
				comOth12.append(" AND currentApplyCurr IN (");
				StringBuffer currBuf = new StringBuffer("");
				for (String curr : currArr) {
					if (Util.equals(currBuf.toString(), "")) {
						currBuf.append("?" + currCount++);
					} else {
						currBuf.append(", ?" + currCount++);
					}

				}
				comOth12.append(currBuf.toString());
				comOth12.append(")");
			}

			if (!StringUtils.isBlank(fxCustId)) {
				// 統編
				comOth12.append(" AND custid = ?7");
			}

			if (!StringUtils.isBlank(fxCustName)) {
				// 戶名
				comOth12.append(" AND custname LIKE ?23");
			}

			if (!StringUtils.isBlank(fxGuarantor)
					&& Util.equals(fxGuarantor, "Y")) {
				// 保證人有/無--無 无 None
				comOth12.append(" AND (RTRIM(GUARANTOR) != ?24  AND RTRIM(GUARANTOR) != ?25  AND RTRIM(GUARANTOR) != ?26 ) ");
			} else if (!StringUtils.isBlank(fxGuarantor)
					&& Util.equals(fxGuarantor, "N")) {
				// 保證人有/無--無 无 None
				comOth12.append(" AND (RTRIM(GUARANTOR) = ?24  OR RTRIM(GUARANTOR) = ?25  OR RTRIM(GUARANTOR) = ?26  OR RTRIM(GUARANTOR) = '' ) ");
			}

			comOth12.append(" AND MAINID IN ("); // --企金授信科子目
		}

		// if (!StringUtils.isBlank(fxLnSubject)) {
		// // 企個金科目非共用同一檔案，故其中有一個符合就算
		// rightCount = rightCount + 1;
		// comOth8.append(" SELECT DISTINCT MAINID FROM LMS.L140M01A WHERE DELETEDTIME IS NULL AND lnsubject LIKE ?15 AND MAINID IN (");
		// // --企金授信科子目
		// }

		// L140M01C相關***************************************************************
		if (!StringUtils.isBlank(fxLnSubject)
				|| !StringUtils.isBlank(fxLmtDays1)
				|| !StringUtils.isBlank(fxLmtDays2)) {

			rightCount = rightCount + 1;
			comOth13.append(" SELECT DISTINCT MAINID FROM LMS.L140M01C WHERE MAINID IS NOT NULL "); // --企金授信科子目

			if (!StringUtils.isBlank(fxLnSubject)) {
				// 科目

				String[] subjArr = fxLnSubject.split("\\|");
				int subjCount = 3000;
				comOth13.append("AND loanTP IN (");
				StringBuffer subjBuf = new StringBuffer("");
				for (String subj : subjArr) {
					if (Util.equals(subjBuf.toString(), "")) {
						subjBuf.append("?" + subjCount++);
					} else {
						subjBuf.append(", ?" + subjCount++);
					}

				}
				comOth13.append(subjBuf.toString());
				comOth13.append(")");

			}

			if (!StringUtils.isBlank(fxLmtDays1)
					|| !StringUtils.isBlank(fxLmtDays2)) {
				// 清償期限－天數
				comOth13.append("AND lmtDays BETWEEN ?27 AND ?28 ");
			}
			comOth13.append(" AND MAINID IN ("); // --企金授信科子目

		}

		// L140M01N相關***************************************************************
		if (!StringUtils.isBlank(fxRateText1)) {
			rightCount = rightCount + 1;
			comOth14.append(" SELECT DISTINCT MAINID FROM LMS.L140M01N WHERE MAINID IS NOT NULL AND ( "); // 結構化利率

			int rateCount = 3100;
			StringBuffer rateBuf = new StringBuffer("");
			String[] rateArr = fxRateText1.split("\\|");
			for (String rate : rateArr) {
				if (Util.equals(rateBuf.toString(), "")) {
					rateBuf.append(" rateBase LIKE ?" + rateCount++);
				} else {
					rateBuf.append(" OR rateBase LIKE ?" + rateCount++);
				}
			}
			comOth14.append(rateBuf.toString());
			comOth14.append(") AND MAINID IN ("); // --企金結構化利率
		}

		if (!StringUtils.isBlank(fxRateText)) {
			// 企個金利率、費率非共用同一檔案，故其中有一個符合就算
			rightCount = rightCount + 1;
			comOth9.append(" SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '2' AND itemdscr LIKE ?16 AND MAINID IN  ("); // --企金利(費)率
		}

		if (!StringUtils.isBlank(fxOtherCondition)) {
			rightCount = rightCount + 1;
			comOth10.append(" SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '4' AND itemdscr LIKE ?17 AND MAINID IN 	("); // --其他敘做條件
		}

		if (!StringUtils.isBlank(fxCollateral)) {
			rightCount = rightCount + 1;
			comOth11.append(" SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '3' AND itemdscr LIKE ?18 AND MAINID IN  ("); // --擔保品
		}

		// 組合SQL 企金
		comCondtion.append(comOth11).append(comOth10).append(comOth9)
				.append(comOth8).append(comOth14).append(comOth13)
				.append(comOth12).append(comOth7).append(comOth6)
				.append(comOth5).append(comOth4).append(comOth3)
				.append(comOth2).append(comOth1).append(com120);

		// 補左括號
		for (int i = 1; i <= rightCount; i++) {
			comCondtion.append(")");
		}

		// 已上額度明細WITH TABLE T140 條件 完成
		// RIGHT JOIN
		// 擔保品****************************************************************************

		/*
		 * SELECT T140.mainid,T140.CNTRNO,CMSS.CNTRNO,CMSS.CNTRNO FROM (select
		 * cms.c100m01.mainid,cms.c100s03a.CNTRNO as CNTRNO from
		 * cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null AND
		 * cms.c100m01.mainid = cms.c100s03a.mainid and cms.c100m01.docstatus in
		 * ('13B','14B','15B','16B') and cms.c100m01.colltyp1 in ('01') and
		 * cms.c100m01.DELETEDTIME is null ) AS T140 right outer join ( select
		 * CMSM.CNTRNO from ( select
		 * cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO as CNTRNO
		 * from cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null
		 * AND cms.c100m01.mainid = cms.c100s03a.mainid AND
		 * cms.c100m01.DELETEDTIME is null ) AS CMSM left outer join cms.C101M04
		 * on CMSM.mainid = cms.C101M04.mainid where ( CMSM.colltyp1 in ('01')
		 * and VALUE(cms.C101M04.bldUse,'') in ('08') OR CMSM.colltyp1 in
		 * ('02','03') ) group by CMSM.CNTRNO ) as CMSS on CMSS.CNTRNO =
		 * T140.CNTRNO
		 */

		// OLD
		// if (!StringUtils.isBlank(fxCollateral1)) {
		// int cmsCount = 3200;
		// String[] cmsArr = fxCollateral1.split("\\|");
		// comOth15.append("right outer join (select cms.c100s03a.CNTRNO as CNTRNO from cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null AND cms.c100m01.mainid = cms.c100s03a.mainid and cms.c100m01.docstatus in ('13B','14B','15B','16B') and cms.c100m01.colltyp1 in (");
		// StringBuffer cmsBuf = new StringBuffer("");
		// for (String cms : cmsArr) {
		// if (Util.equals(cmsBuf.toString(), "")) {
		// cmsBuf.append("?" + cmsCount++);
		// } else {
		// cmsBuf.append(", ?" + cmsCount++);
		// }
		// }
		// comOth15.append(cmsBuf.toString());
		// comOth15.append(") and cms.c100m01.DELETEDTIME is null group by cms.c100s03a.CNTRNO) as CMSS on CMSS.CNTRNO = T140.CNTRNO");
		// }

		// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		// ─────────────────────────────────
		// 程式修改申請編號：(112)第 2919 號
		//
		// 填報單位：(918)授信審查處
		// 填報人員：(03830)郭祐麟
		// 填報單位內部編號：
		// 修改事項：授審處及各分處協助營業單位辦理利害關係人授信案件於E-Loan系統辦理全行「同類授信對象」案例搜尋，其中搜尋條件「擔保品--不動產」項下依建物謄本主要用途增加次選項，即按擔保品檔中建物「主要用途」欄：計有「住家用」、「商業用」、「工業用」、「住商用」、「住工用」、「工商用」、「農舍用」、「其他」等8類
		// ─────────────────────────────────

		// String fxBldUse = "01|08";
		if (!StringUtils.isBlank(fxCollateral1)) {
			String cmsSql = "";
			if (Util.equals(Util.trim(fxOnlyLand), "Y")) {
				/*
				 * SELECT T140.mainid,T140.CNTRNO,CMSS.CNTRNO,CMSS.CNTRNO FROM
				 * (select cms.c100m01.mainid,cms.c100s03a.CNTRNO as CNTRNO from
				 * cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null
				 * AND cms.c100m01.mainid = cms.c100s03a.mainid and
				 * cms.c100m01.docstatus in ('13B','14B','15B','16B') and
				 * cms.c100m01.colltyp1 in ('01') and cms.c100m01.DELETEDTIME is
				 * null ) AS T140 right outer join ( SELECT CNTRNO FROM ( select
				 * CMSM.CNTRNO,MAX(VALUE(cms.C101M04.SEQNO,0)) AS MAXSEQNO from
				 * ( select
				 * cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO
				 * as CNTRNO from cms.c100s03a,cms.c100m01 where
				 * cms.c100m01.mainid is not null AND cms.c100m01.mainid =
				 * cms.c100s03a.mainid AND cms.c100m01.DELETEDTIME is NULL AND
				 * cms.c100m01.colltyp1 in ('01') ) AS CMSM left outer join
				 * cms.C101M04 on CMSM.mainid = cms.C101M04.mainid group by
				 * CMSM.CNTRNO ) AS CMST WHERE CMST.MAXSEQNO = 0 ) as CMSS on
				 * CMSS.CNTRNO = T140.CNTRNO
				 */
				cmsSql = "right outer join (   SELECT CNTRNO FROM    ( 	   select CMSM.CNTRNO,MAX(VALUE(cms.C101M04.SEQNO,0)) AS MAXSEQNO from 	   (		   select 		     cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO as CNTRNO  		   from 		     cms.c100s03a,cms.c100m01		     where 		     cms.c100m01.mainid is not null AND		     cms.c100m01.mainid = cms.c100s03a.mainid AND		     cms.c100m01.DELETEDTIME is NULL AND		     cms.c100m01.colltyp1 in ('01') AND cms.c100m01.docstatus in ('13B','14B','15B','16B')  	   ) AS CMSM 	   left outer join	   cms.C101M04 	   on 	   CMSM.mainid = cms.C101M04.mainid 	   group by 	   CMSM.CNTRNO   ) AS CMST   WHERE CMST.MAXSEQNO = 0) as CMSS  on CMSS.CNTRNO = T140.CNTRNO";

			} else {

				/*
				 * SELECT T140.mainid,T140.CNTRNO,CMSS.CNTRNO,CMSS.CNTRNO FROM
				 * (select cms.c100m01.mainid,cms.c100s03a.CNTRNO as CNTRNO from
				 * cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null
				 * AND cms.c100m01.mainid = cms.c100s03a.mainid and
				 * cms.c100m01.docstatus in ('13B','14B','15B','16B') and
				 * cms.c100m01.colltyp1 in ('01') and cms.c100m01.DELETEDTIME is
				 * null ) AS T140 right outer join ( select CMSM.CNTRNO from (
				 * select
				 * cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO
				 * as CNTRNO from cms.c100s03a,cms.c100m01 where
				 * cms.c100m01.mainid is not null AND cms.c100m01.mainid =
				 * cms.c100s03a.mainid AND cms.c100m01.DELETEDTIME is null ) AS
				 * CMSM left outer join cms.C101M04 on CMSM.mainid =
				 * cms.C101M04.mainid where ( CMSM.colltyp1 in ('01') and
				 * VALUE(cms.C101M04.bldUse,'') in ('08') OR CMSM.colltyp1 in
				 * ('02','03') ) group by CMSM.CNTRNO ) as CMSS on CMSS.CNTRNO =
				 * T140.CNTRNO
				 */

				int cmsCount01 = 3200;
				int cmsCount02 = 3300;
				String[] cmsArr = fxCollateral1.split("\\|");
				String[] bldUseArr = fxBldUse.split("\\|");
				String cms01 = "";
				String cms02 = "";
				String cmsOr_01 = "";
				StringBuffer cmsBuf01 = new StringBuffer("");
				StringBuffer cmsBuf02 = new StringBuffer("");
				for (String cms : cmsArr) {

					if (Util.equals(cms, "01")
							&& Util.notEquals(Util.trim(fxBldUse), "")) {
						// 01不動產，需要主要用途
						for (String bldUse : bldUseArr) {
							if (Util.equals(cmsBuf01.toString(), "")) {
								cmsBuf01.append("CMSM.colltyp1 in ('01') and cms.C101M04.bldUse in ( "
										+ "?" + cmsCount01++);
							} else {
								cmsBuf01.append(", ?" + cmsCount01++);
							}
						}

					} else if (Util.equals(cms, "01")
							&& Util.equals(Util.trim(fxBldUse), "")) {
						// 01不動產，不需要主要用途
						cmsBuf01.append("CMSM.colltyp1 in ('01'");
					} else {
						if (Util.equals(cmsBuf02.toString(), "")) {
							cmsBuf02.append("CMSM.colltyp1 in ( " + "?"
									+ cmsCount02++);
						} else {
							cmsBuf02.append(", ?" + cmsCount02++);
						}
					}

				}

				cms01 = Util.equals(cmsBuf01, "") ? "" : " ( "
						+ (cmsBuf01.append(")")).toString() + " ) ";
				cms02 = Util.equals(cmsBuf02, "") ? "" : (cmsBuf02.append(")"))
						.toString();

				if (Util.notEquals(cms01, "") && Util.notEquals(cms02, "")) {
					cmsOr_01 = "OR";
				} else {
					cmsOr_01 = "";
				}

				cmsSql = " right outer join (   select CMSM.CNTRNO from    (	   select 	     cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO as CNTRNO  	   from 	     cms.c100s03a,cms.c100m01	     where 	     cms.c100m01.mainid is not null AND	     cms.c100m01.mainid = cms.c100s03a.mainid AND	     cms.c100m01.DELETEDTIME is null AND cms.c100m01.docstatus in ('13B','14B','15B','16B')   ) AS CMSM    left outer join   cms.C101M04    on    CMSM.mainid = cms.C101M04.mainid    where   (        "
						+ cms01
						+ "        "
						+ cmsOr_01
						+ "        "
						+ cms02
						+ "      )      group by    CMSM.CNTRNO) as CMSS  on CMSS.CNTRNO = T140.CNTRNO";

			}

			comOth15.append(cmsSql);

		}

		// 最外層用MAINID串回L120M01A
		String finalSql = null;

		// finalSql =
		// "SELECT DISTINCT LMS.L140M01A.* FROM LMS.L140M01A WHERE LMS.L140M01A.DELETEDTIME IS NULL AND DOCSTATUS = '030' AND MAINID IN ("
		// + comCondtion
		// +
		// ") ORDER BY CASEDATE DESC,OWNBRID ASC,CASENO DESC,CUSTID ASC,DUPNO ASC WITH UR";

		finalSql = "WITH T140 AS (SELECT DISTINCT LMS.L140M01A.* FROM LMS.L140M01A WHERE LMS.L140M01A.DELETEDTIME IS NULL AND DOCSTATUS = '030' AND MAINID IN ("
				+ comCondtion
				+ ") ) SELECT * FROM T140 "
				+ comOth15
				+ " WHERE T140.CNTRNO IS NOT NULL AND T140.MAINID IS NOT NULL ORDER BY CASEDATE DESC,OWNBRID ASC,CASENO DESC,CUSTID ASC,DUPNO ASC FETCH FIRST 300 ROWS ONLY WITH UR";

		// System.out.println("finalSql=" + finalSql);
		logger.info("finalSql=" + finalSql);

		String sql = finalSql;
		Query query = entityManager.createNativeQuery(sql); // 排除掉最後面的AND

		if (!StringUtils.isBlank(fxTypCd)) {
			query.setParameter(1, fxTypCd);
		}

		if (!StringUtils.isBlank(fxDocType)) {
			query.setParameter(2, fxDocType);
		}

		if (!StringUtils.isBlank(fxDocKind)) {
			query.setParameter(3, fxDocKind);
		}

		if (!StringUtils.isBlank(fxDocCode)) {
			query.setParameter(4, fxDocCode);
		}

		if (!StringUtils.isBlank(fxUpdaterName)
				&& !StringUtils.isBlank(fxUpdater)) {
			query.setParameter(5, fxUpdater);
		}

		if (!StringUtils.isBlank(fxCaseBrId)) {
			query.setParameter(6, fxCaseBrId);
		}

		if (!StringUtils.isBlank(fxCustId)) {
			query.setParameter(7, fxCustId);
		}

		if (!StringUtils.isBlank(fxDocRslt)) {
			query.setParameter(8, fxDocRslt);
		}

		if (!StringUtils.isBlank(fxGroupId)) {
			if (BranchTypeEnum.授管處.getCode().equals(unitType)
					|| BranchTypeEnum.國金部.getCode().equals(unitType)) {
				query.setParameter(9, "");
			} else {
				query.setParameter(9, fxGroupId);
			}
		}

		if (!StringUtils.isBlank(fxCustName)) {
			query.setParameter(10, fxCustName);
		}

		if (!StringUtils.isBlank(fxBusCode)) {
			query.setParameter(11, fxBusCode);
		}

		if (!StringUtils.isBlank(fxReportOther)) {
			query.setParameter(12, fxReportOther);
		}

		if (!StringUtils.isBlank(fxReportReason1)) {
			query.setParameter(13, fxReportReason1);
		}

		if (!StringUtils.isBlank(fxAreaOption)) {
			query.setParameter(14, fxAreaOption);
		}

		// if (!StringUtils.isBlank(fxLnSubject)) {
		// query.setParameter(15, fxLnSubject);
		// if (hasClsDoc == true) {
		// query.setParameter(515, fxLnSubject);
		// }
		// }

		if (!StringUtils.isBlank(fxRateText)) {
			query.setParameter(16, fxRateText);
		}

		if (!StringUtils.isBlank(fxOtherCondition)) {
			query.setParameter(17, fxOtherCondition);
		}

		if (!StringUtils.isBlank(fxCollateral)) {
			query.setParameter(18, fxCollateral);
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			query.setParameter(19, fxCaseDateS);
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			query.setParameter(20, fxCaseDateE);
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			query.setParameter(21, fxEndDateS);
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			query.setParameter(22, fxEndDateE);
		}

		if (!StringUtils.isBlank(fxCustName)) {
			// 戶名
			query.setParameter(23, fxCustName);
		}

		if (!StringUtils.isBlank(fxCntrNo)) {
			query.setParameter(15, fxCntrNo);
		}

		if (!StringUtils.isBlank(fxCurr)) {
			// 幣別
			String[] currArr = fxCurr.split("\\|");
			int currCount = 2900;
			for (String curr : currArr) {
				query.setParameter(currCount++, curr);
			}
		}

		if (!StringUtils.isBlank(fxGuarantor)
				&& (Util.equals(fxGuarantor, "Y") || Util.equals(fxGuarantor,
						"N"))) {
			// 戶名--無 无 None
			query.setParameter(24, "無");
			query.setParameter(25, "无");
			query.setParameter(26, "None");

		}

		// L140M01C相關***************************************************************

		if (!StringUtils.isBlank(fxLnSubject)) {
			// 科目
			int subjCount = 3000;
			String[] subjArr = fxLnSubject.split("\\|");
			for (String subj : subjArr) {
				query.setParameter(subjCount++, subj);
			}
		}

		if (!StringUtils.isBlank(fxLmtDays1)
				|| !StringUtils.isBlank(fxLmtDays2)) {
			// 清償期限－天數
			query.setParameter(27, Util.notEquals(fxLmtDays1, "") ? fxLmtDays1
					: fxLmtDays2);
			query.setParameter(28, Util.notEquals(fxLmtDays2, "") ? fxLmtDays2
					: fxLmtDays1);
		}

		// L140M01N相關***************************************************************

		if (!StringUtils.isBlank(fxRateText1)) {
			int rateCount = 3100;
			String[] rateArr = fxRateText1.split("\\|");
			for (String rate : rateArr) {
				query.setParameter(rateCount++, "%" + rate + "%");
			}

		}

		// CMS擔保品相關***************************************************************
		// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		// ─────────────────────────────────
		// 程式修改申請編號：(112)第 2919 號
		//
		// 填報單位：(918)授信審查處
		// 填報人員：(03830)郭祐麟
		// 填報單位內部編號：
		// 修改事項：授審處及各分處協助營業單位辦
		if (!StringUtils.isBlank(fxCollateral1)) {
			if (Util.equals(Util.trim(fxOnlyLand), "Y")) {
				// 不動產只要查純土地
			} else {
				// 擔保品
				int cmsCount01 = 3200;
				int cmsCount02 = 3300;

				String[] bldUseArr = fxBldUse.split("\\|");
				for (String bldUse : bldUseArr) {
					query.setParameter(cmsCount01++, bldUse);
				}

				String[] cmsArr = fxCollateral1.split("\\|");
				for (String cms : cmsArr) {
					if (Util.notEquals(cms, "01")) {
						query.setParameter(cmsCount02++, cms);
					}
				}
			}
		}

		// query.setFirstResult(search.getFirstResult());
		// query.setMaxResults(search.getMaxResults());
		List<Object[]> resultList = query.getResultList();
		return resultList;

	}

	// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
	public List<Object[]> findFullTextSearch_CLS(String fxUserId,
			String fxGroupId, String fxCaseDateName, String fxCaseDateS,
			String fxCaseDateE, String fxEndDateS, String fxEndDateE,
			String fxTypCd, String fxDocType, String fxDocKind,
			String fxDocCode, String fxUpdaterName, String fxUpdater,
			String fxCaseBrId, String fxCustId, String fxDocRslt,
			String fxDocStatus, String fxLnSubject, String fxRateText,
			String fxOtherCondition, String fxReportOther,
			String fxReportReason1, String fxReportReason2,
			String fxAreaOption, String fxCollateral, String fxCustName,
			String fxBusCode, String fxCurr, String fxLmtDays1,
			String fxLmtDays2, String fxRateText1, String fxGuarantor,
			String fxCntrNo, String fxCollateral1, String unitType,
			String fxIsCls, String fxProdKind, String fxLnSubjectCls,
			String fxRateTextCls, String fxLnMonth1, String fxLnMonth2,
			String fxBldUse, String fxOnlyLand) {

		/*
		 * 
		 * http://www.dpriver.com/pp/sqlformat.htm
		 * 
		 * SELECT DISTINCT MAINID FROM ( SELECT DISTINCT MAINID FROM
		 * LMS.L140M01B WHERE itemtype = '3' AND itemdscr LIKE '%ＳＩＢＯＲ%' AND
		 * MAINID IN --擔保品 ( SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE
		 * itemtype = '4' AND itemdscr LIKE '%%' AND MAINID IN --其他敘做條件 ( SELECT
		 * DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '2' AND itemdscr
		 * LIKE '%ＳＩＢＯＲ%' AND MAINID IN --企金利(費)率 ( SELECT DISTINCT MAINID FROM
		 * LMS.L140M01A WHERE lnsubject LIKE '%%' AND MAINID IN --企金授信科子目 (
		 * SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C WHERE
		 * LMS.L120M01C.MAINID IN --串額度明細表 ( SELECT DISTINCT MAINID FROM
		 * LMS.L120M01D WHERE ITEMTYPE = '7' AND itemdscr LIKE '%%' AND MAINID
		 * IN --簽報書-營運中心說明及意見 ( SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE
		 * (ITEMTYPE = '4' OR ITEMTYPE = 'E') AND itemdscr LIKE '%%' AND MAINID
		 * IN --簽報書-綜合評估及敘作理由(併於主表) ( SELECT DISTINCT MAINID FROM LMS.L120M01D
		 * WHERE ITEMTYPE = '3' AND itemdscr LIKE '%%' AND MAINID IN --簽報書-其他 (
		 * SELECT DISTINCT MAINID FROM LMS.L120S01B WHERE BusCode = '123456' AND
		 * MAINID IN --行業對象別 ( SELECT DISTINCT MAINID FROM LMS.L120S01A WHERE
		 * LMS.L120S01A.custname LIKE '%%' AND MAINID IN --客戶名稱 (
		 * 
		 * SELECT DISTINCT MAINID FROM LMS.L120A01A WHERE AUTHUNIT = '918' AND
		 * MAINID IN --簽報書-其他 ( (SELECT DISTINCT LMS.L120M01A.MAINID FROM
		 * LMS.L120M01A WHERE LMS.L120M01A.NOTESUP IS NULL AND caseDate BETWEEN
		 * '2012-11-13' AND '2013-11-13' AND ( ( DOCSTATUS = '05O' OR DOCSTATUS
		 * = 'L3G' OR DOCSTATUS = 'L4G' ) ) ) )
		 * 
		 * ) ) ) ) ) ) ) ) ) ) UNION ALL SELECT DISTINCT MAINID FROM ( SELECT
		 * DISTINCT MAINID FROM LMS.L140S02A WHERE (rateDesc LIKE '%%' OR
		 * freeRateDesc LIKE '%%' ) AND MAINID IN --企金利(費)率 ( SELECT DISTINCT
		 * MAINID FROM LMS.l140S02a,lms.C900M01D WHERE lms.C900M01D.subjCode =
		 * LMS.L140S02A.subjCode AND lms.C900M01D.SUBJNM LIKE '%%' AND MAINID IN
		 * --個金授信科目 ( SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C
		 * WHERE LMS.L120M01C.MAINID IN --串額度明細表 ( SELECT DISTINCT MAINID FROM
		 * LMS.C120M01A WHERE LMS.C120M01A.CUSTPOS = 'C' AND
		 * LMS.C120M01A.custname LIKE '%%' AND MAINID IN --客戶名稱 ( SELECT
		 * DISTINCT MAINID FROM LMS.L120A01A WHERE AUTHUNIT = '918' AND MAINID
		 * IN --簽報書-其他 ( (SELECT DISTINCT LMS.L120M01A.MAINID FROM LMS.L120M01A
		 * WHERE LMS.L120M01A.NOTESUP IS NULL AND LMS.L120M01A.docType = '2' AND
		 * caseDate BETWEEN '2012-11-13' AND '2013-11-13' AND ( ( DOCSTATUS =
		 * '05O' OR DOCSTATUS = 'L3G' OR DOCSTATUS = 'L4G' ) ) ) ) ) ) ) ) ) )
		 */

		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// String unitType = user.getUnitType();

		// 企金
		StringBuffer com120 = new StringBuffer();
		StringBuffer com120Condtion = new StringBuffer();
		StringBuffer comCondtion = new StringBuffer();

		int rightCount = 0;

		// 判斷有沒有額度明細表條件
		boolean hasCntrDoc = false;
		hasCntrDoc = true; // 永遠要串額度明細表

		// 核心條件************************************************************************
		if (!StringUtils.isBlank(fxDocStatus)) {
			com120Condtion.append(" AND ( " + fxDocStatus + " ) ");
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			com120Condtion.append(" AND " + fxCaseDateName
					+ " BETWEEN ?19  AND ?20 ");
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			com120Condtion.append(" AND enddate  BETWEEN ?21 AND ?22 ");
		}

		if (!StringUtils.isBlank(fxTypCd)) {
			com120Condtion.append(" AND typCd = ?1 ");
		}

		if (!StringUtils.isBlank(fxDocType)) {
			com120Condtion.append(" AND docType  = ?2  ");
		}

		if (!StringUtils.isBlank(fxDocKind)) {
			com120Condtion.append(" AND docKind  = ?3  ");
		}

		if (!StringUtils.isBlank(fxDocCode)) {
			com120Condtion.append(" AND docCode  = ?4  ");
		}

		if (!StringUtils.isBlank(fxUpdaterName)
				&& !StringUtils.isBlank(fxUpdater)) {
			com120Condtion.append(" AND " + fxUpdaterName + "  = ?5  ");
		}

		if (!StringUtils.isBlank(fxCaseBrId)) {
			com120Condtion.append(" AND caseBrId = ?6  ");
		}

		if (!StringUtils.isBlank(fxDocRslt)) {
			com120Condtion.append(" AND docRslt   = ?8  ");
		}

		// --判斷簽報書授權
		String authTbl = "";
		if (BranchTypeEnum.授管處.getCode().equals(unitType)
				|| BranchTypeEnum.國金部.getCode().equals(unitType)) {
			authTbl = "";
		} else {
			authTbl = "";
			/*
			 * authTbl = ",com.BELSBRN"; com120Condtion .append(
			 * " AND com.BELSBRN.brNo = LMS.L120M01A.CASEBRID  AND com.BELSBRN.brnGroup   = ?9  "
			 * );
			 */
		}

		com120.append(" ( SELECT DISTINCT LMS.L120M01A.MAINID FROM LMS.L120M01A"
				+ authTbl
				+ " WHERE LMS.L120M01A.DELETEDTIME IS NULL AND LMS.L120M01A.NOTESUP IS NULL AND typCd != '5' AND docType = '2' "
				+ com120Condtion.toString() + ")");

		// 借款人****************************************************************

		// 簽報書***************************************************************
		// 目前用不到
		// if (!StringUtils.isBlank(fxReportOther)) {
		// rightCount = rightCount + 1;
		// comOth4.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE ITEMTYPE = '3' AND itemdscr LIKE ?12 AND MAINID IN  (");
		// // --簽報書-其他
		// }
		//
		// if (!StringUtils.isBlank(fxReportReason1)) {
		// rightCount = rightCount + 1;
		// comOth5.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE (ITEMTYPE = '4' OR ITEMTYPE = 'E') AND itemdscr LIKE ?13 AND MAINID IN (");
		// // --簽報書-綜合評估及敘作理由(併於主表)
		// }
		//
		// if (!StringUtils.isBlank(fxAreaOption)) {
		// rightCount = rightCount + 1;
		// comOth6.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE ITEMTYPE = '7' AND itemdscr LIKE ?14 AND MAINID IN  (");
		// // --簽報書-營運中心說明及意見
		// }

		// 額度明細表相關條件***************************************************************
		StringBuffer com120c = new StringBuffer("");
		rightCount = rightCount + 1;
		com120c.append(" SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C WHERE LMS.L120M01C.MAINID IN  ("); // --串額度明細表

		// L140M01A相關***************************************************************
		StringBuffer l140m01aBuf = new StringBuffer();
		if (!StringUtils.isBlank(fxCustName) || !StringUtils.isBlank(fxCustId)
				|| !StringUtils.isBlank(fxCurr)
				|| !StringUtils.isBlank(fxGuarantor)
				|| !StringUtils.isBlank(fxCntrNo)) {

			rightCount = rightCount + 1;
			l140m01aBuf
					.append(" SELECT DISTINCT MAINID FROM LMS.L140M01A WHERE DOCSTATUS = '030' AND DELETEDTIME IS NULL "); // --企金授信科子目

			if (!StringUtils.isBlank(fxCntrNo)) {
				l140m01aBuf.append(" AND cntrNo = ?15");
			}

			if (!StringUtils.isBlank(fxCurr)) {
				// 幣別
				String[] currArr = fxCurr.split("\\|");
				int currCount = 2900;
				l140m01aBuf.append(" AND currentApplyCurr IN (");
				StringBuffer currBuf = new StringBuffer("");
				for (String curr : currArr) {
					if (Util.equals(currBuf.toString(), "")) {
						currBuf.append("?" + currCount++);
					} else {
						currBuf.append(", ?" + currCount++);
					}

				}
				l140m01aBuf.append(currBuf.toString());
				l140m01aBuf.append(")");
			}

			if (!StringUtils.isBlank(fxCustId)) {
				// 統編
				l140m01aBuf.append(" AND custid = ?7");
			}

			if (!StringUtils.isBlank(fxCustName)) {
				// 戶名
				l140m01aBuf.append(" AND custname LIKE ?23");
			}

			l140m01aBuf.append(" AND MAINID IN (");
		}

		// L140S02A相關***************************************************************
		StringBuffer l140s02aBuf = new StringBuffer("");
		if (!StringUtils.isBlank(fxProdKind)
				|| !StringUtils.isBlank(fxLnSubjectCls)
				|| !StringUtils.isBlank(fxLnMonth1)
				|| !StringUtils.isBlank(fxLnMonth2)
				|| !StringUtils.isBlank(fxRateText)) {

			rightCount = rightCount + 1;
			l140s02aBuf
					.append(" SELECT DISTINCT MAINID FROM LMS.L140S02A WHERE MAINID IS NOT NULL ");

			if (!StringUtils.isBlank(fxProdKind)) {
				// 產品種類
				String[] prodKindArr = fxProdKind.split("\\|");
				int prodKindCount = 3500;
				l140s02aBuf.append(" AND prodKind IN (");
				StringBuffer prodKindBuf = new StringBuffer("");
				for (String prodKind : prodKindArr) {
					if (Util.equals(prodKindBuf.toString(), "")) {
						prodKindBuf.append("?" + prodKindCount++);
					} else {
						prodKindBuf.append(", ?" + prodKindCount++);
					}

				}
				l140s02aBuf.append(prodKindBuf.toString());
				l140s02aBuf.append(") ");

			}

			if (!StringUtils.isBlank(fxLnSubjectCls)) {
				// 科目
				String[] subjCodeArr = fxLnSubjectCls.split("\\|");
				int subjCodeCount = 3600;
				l140s02aBuf.append(" AND subjCode IN (");
				StringBuffer subjCodeBuf = new StringBuffer("");
				for (String subjCode : subjCodeArr) {
					if (Util.equals(subjCodeBuf.toString(), "")) {
						subjCodeBuf.append("?" + subjCodeCount++);
					} else {
						subjCodeBuf.append(", ?" + subjCodeCount++);
					}

				}
				l140s02aBuf.append(subjCodeBuf.toString());
				l140s02aBuf.append(") ");

			}

			if (!StringUtils.isBlank(fxLnMonth1)
					|| !StringUtils.isBlank(fxLnMonth2)) {
				// 授信期間-月數
				l140s02aBuf
						.append(" AND ((case when lnYear is not null then lnYear *12 else 0 end )+ (case when lnMonth is not null then lnMonth else 0 end )) BETWEEN ?37 AND ?38 ");
			}

			if (!StringUtils.isBlank(fxRateText)) {
				l140s02aBuf.append(" AND rateDesc  LIKE ?16"); // --利率組字
			}

			l140s02aBuf.append(" AND MAINID IN ("); // --企金授信科子目

		}

		// L140S01A相關***************************************************************
		StringBuffer l140s01aBuf = new StringBuffer("");
		if (!StringUtils.isBlank(fxGuarantor)) {
			if (Util.equals(fxGuarantor, "Y")) {
				rightCount = rightCount + 1;
				l140s01aBuf
						.append(" SELECT DISTINCT MAINID FROM LMS.L140S01A WHERE MAINID IS NOT NULL AND custPos IN ('G','N') ");
				l140s01aBuf.append(" group by mainid HAVING COUNT(*) > 0  ");
				l140s01aBuf.append(" AND MAINID IN ("); // --消金授信科子目
			} else if (Util.equals(fxGuarantor, "N")) {
				rightCount = rightCount + 1;
				l140s01aBuf
						.append(" SELECT MAINID FROM LMS.L140M01A WHERE DOCURL='CLS' AND APPROVETIME IS NOT NULL AND DELETEDTIME IS NULL AND MAINID NOT IN (SELECT MAINID FROM LMS.L140S01A WHERE custPos IN ('G','N')) ");
				l140s01aBuf.append(" AND MAINID IN ("); // --消金授信科子目
			}
		}

		// L140S02D相關***************************************************************
		StringBuffer l140s02dBuf = new StringBuffer("");
		if (!StringUtils.isBlank(fxRateTextCls)) {
			rightCount = rightCount + 1;
			l140s02dBuf
					.append(" SELECT DISTINCT MAINID FROM LMS.L140S02D WHERE MAINID IS NOT NULL ");

			// --利率
			String[] rateTypeArr = fxRateTextCls.split("\\|");
			int rateTypeCount = 3900;
			l140s02dBuf.append(" AND rateType IN (");
			StringBuffer rateTypeBuf = new StringBuffer("");
			for (String rateType : rateTypeArr) {
				if (Util.equals(rateTypeBuf.toString(), "")) {
					rateTypeBuf.append("?" + rateTypeCount++);
				} else {
					rateTypeBuf.append(", ?" + rateTypeCount++);
				}

			}
			l140s02dBuf.append(rateTypeBuf.toString());
			l140s02dBuf.append(") ");
			l140s02dBuf.append(" AND MAINID IN (");
		}

		// 其他關鍵字條件
		StringBuffer l140m01bBuf1 = new StringBuffer("");
		if (!StringUtils.isBlank(fxOtherCondition)) {
			rightCount = rightCount + 1;
			l140m01bBuf1
					.append(" SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '4' AND itemdscr LIKE ?17 AND MAINID IN ("); // --其他敘做條件
		}

		StringBuffer l140m01bBuf2 = new StringBuffer("");
		if (!StringUtils.isBlank(fxCollateral)) {
			rightCount = rightCount + 1;
			l140m01bBuf2
					.append(" SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '3' AND itemdscr LIKE ?18 AND MAINID IN ("); // --擔保品
		}

		// 組合SQL 企金
		comCondtion.append(l140m01bBuf2).append(l140m01bBuf1)
				.append(l140s02dBuf).append(l140s01aBuf).append(l140s02aBuf)
				.append(l140m01aBuf).append(com120c).append(com120);

		// 補左括號
		for (int i = 1; i <= rightCount; i++) {
			comCondtion.append(")");
		}

		// 已上額度明細WITH TABLE T140 條件 完成
		// RIGHT JOIN
		// 擔保品****************************************************************************

		// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		StringBuffer cmsCondtion = new StringBuffer(""); // RIGHT OUTER JOIN CMS

		// if (!StringUtils.isBlank(fxCollateral1)) {
		// int cmsCount = 3200;
		// String[] cmsArr = fxCollateral1.split("\\|");
		// cmsCondtion
		// .append("right outer join (select cms.c100s03a.CNTRNO as CNTRNO from cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null AND cms.c100m01.mainid = cms.c100s03a.mainid and cms.c100m01.docstatus in ('13B','14B','15B','16B') and cms.c100m01.colltyp1 in (");
		// StringBuffer cmsBuf = new StringBuffer("");
		// for (String cms : cmsArr) {
		// if (Util.equals(cmsBuf.toString(), "")) {
		// cmsBuf.append("?" + cmsCount++);
		// } else {
		// cmsBuf.append(", ?" + cmsCount++);
		// }
		// }
		// cmsCondtion.append(cmsBuf.toString());
		// cmsCondtion
		// .append(") and cms.c100m01.DELETEDTIME is null group by cms.c100s03a.CNTRNO) as CMSS on CMSS.CNTRNO = T140.CNTRNO");
		// }

		// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		// ─────────────────────────────────
		// 程式修改申請編號：(112)第 2919 號
		//
		// 填報單位：(918)授信審查處
		// 填報人員：(03830)郭祐麟
		// 填報單位內部編號：
		// 修改事項：授審處及各分處協助營業單位辦理利害關係人授信案件於E-Loan系統辦理全行「同類授信對象」案例搜尋，其中搜尋條件「擔保品--不動產」項下依建物謄本主要用途增加次選項，即按擔保品檔中建物「主要用途」欄：計有「住家用」、「商業用」、「工業用」、「住商用」、「住工用」、「工商用」、「農舍用」、「其他」等8類
		// ─────────────────────────────────
		/*
		 * SELECT T140.mainid,T140.CNTRNO,CMSS.CNTRNO,CMSS.CNTRNO FROM (select
		 * cms.c100m01.mainid,cms.c100s03a.CNTRNO as CNTRNO from
		 * cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null AND
		 * cms.c100m01.mainid = cms.c100s03a.mainid and cms.c100m01.docstatus in
		 * ('13B','14B','15B','16B') and cms.c100m01.colltyp1 in ('01') and
		 * cms.c100m01.DELETEDTIME is null ) AS T140 right outer join ( select
		 * CMSM.CNTRNO from ( select
		 * cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO as CNTRNO
		 * from cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null
		 * AND cms.c100m01.mainid = cms.c100s03a.mainid AND
		 * cms.c100m01.DELETEDTIME is null ) AS CMSM left outer join cms.C101M04
		 * on CMSM.mainid = cms.C101M04.mainid where ( CMSM.colltyp1 in ('01')
		 * and VALUE(cms.C101M04.bldUse,'') in ('08') OR CMSM.colltyp1 in
		 * ('02','03') ) group by CMSM.CNTRNO ) as CMSS on CMSS.CNTRNO =
		 * T140.CNTRNO
		 */

		// String fxBldUse = "01|08";
		if (!StringUtils.isBlank(fxCollateral1)) {
			String cmsSql = "";
			if (Util.equals(Util.trim(fxOnlyLand), "Y")) {
				/*
				 * SELECT T140.mainid,T140.CNTRNO,CMSS.CNTRNO,CMSS.CNTRNO FROM
				 * (select cms.c100m01.mainid,cms.c100s03a.CNTRNO as CNTRNO from
				 * cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null
				 * AND cms.c100m01.mainid = cms.c100s03a.mainid and
				 * cms.c100m01.docstatus in ('13B','14B','15B','16B') and
				 * cms.c100m01.colltyp1 in ('01') and cms.c100m01.DELETEDTIME is
				 * null ) AS T140 right outer join ( SELECT CNTRNO FROM ( select
				 * CMSM.CNTRNO,MAX(VALUE(cms.C101M04.SEQNO,0)) AS MAXSEQNO from
				 * ( select
				 * cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO
				 * as CNTRNO from cms.c100s03a,cms.c100m01 where
				 * cms.c100m01.mainid is not null AND cms.c100m01.mainid =
				 * cms.c100s03a.mainid AND cms.c100m01.DELETEDTIME is NULL AND
				 * cms.c100m01.colltyp1 in ('01') ) AS CMSM left outer join
				 * cms.C101M04 on CMSM.mainid = cms.C101M04.mainid group by
				 * CMSM.CNTRNO ) AS CMST WHERE CMST.MAXSEQNO = 0 ) as CMSS on
				 * CMSS.CNTRNO = T140.CNTRNO
				 */
				cmsSql = "right outer join (   SELECT CNTRNO FROM    ( 	   select CMSM.CNTRNO,MAX(VALUE(cms.C101M04.SEQNO,0)) AS MAXSEQNO from 	   (		   select 		     cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO as CNTRNO  		   from 		     cms.c100s03a,cms.c100m01		     where 		     cms.c100m01.mainid is not null AND		     cms.c100m01.mainid = cms.c100s03a.mainid AND		     cms.c100m01.DELETEDTIME is NULL AND		     cms.c100m01.colltyp1 in ('01')	AND cms.c100m01.docstatus in ('13B','14B','15B','16B')   ) AS CMSM 	   left outer join	   cms.C101M04 	   on 	   CMSM.mainid = cms.C101M04.mainid 	   group by 	   CMSM.CNTRNO   ) AS CMST   WHERE CMST.MAXSEQNO = 0) as CMSS  on CMSS.CNTRNO = T140.CNTRNO";

			} else {

				/*
				 * SELECT T140.mainid,T140.CNTRNO,CMSS.CNTRNO,CMSS.CNTRNO FROM
				 * (select cms.c100m01.mainid,cms.c100s03a.CNTRNO as CNTRNO from
				 * cms.c100s03a,cms.c100m01 where cms.c100m01.mainid is not null
				 * AND cms.c100m01.mainid = cms.c100s03a.mainid and
				 * cms.c100m01.docstatus in ('13B','14B','15B','16B') and
				 * cms.c100m01.colltyp1 in ('01') and cms.c100m01.DELETEDTIME is
				 * null ) AS T140 right outer join ( select CMSM.CNTRNO from (
				 * select
				 * cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO
				 * as CNTRNO from cms.c100s03a,cms.c100m01 where
				 * cms.c100m01.mainid is not null AND cms.c100m01.mainid =
				 * cms.c100s03a.mainid AND cms.c100m01.DELETEDTIME is null ) AS
				 * CMSM left outer join cms.C101M04 on CMSM.mainid =
				 * cms.C101M04.mainid where ( CMSM.colltyp1 in ('01') and
				 * VALUE(cms.C101M04.bldUse,'') in ('08') OR CMSM.colltyp1 in
				 * ('02','03') ) group by CMSM.CNTRNO ) as CMSS on CMSS.CNTRNO =
				 * T140.CNTRNO
				 */

				int cmsCount01 = 3200;
				int cmsCount02 = 3300;
				String[] cmsArr = fxCollateral1.split("\\|");
				String[] bldUseArr = fxBldUse.split("\\|");
				String cms01 = "";
				String cms02 = "";
				String cmsOr_01 = "";
				StringBuffer cmsBuf01 = new StringBuffer("");
				StringBuffer cmsBuf02 = new StringBuffer("");
				for (String cms : cmsArr) {

					if (Util.equals(cms, "01")
							&& Util.notEquals(Util.trim(fxBldUse), "")) {
						// 01不動產，需要主要用途
						for (String bldUse : bldUseArr) {
							if (Util.equals(cmsBuf01.toString(), "")) {
								cmsBuf01.append("CMSM.colltyp1 in ('01') and cms.C101M04.bldUse in ( "
										+ "?" + cmsCount01++);
							} else {
								cmsBuf01.append(", ?" + cmsCount01++);
							}
						}

					} else if (Util.equals(cms, "01")
							&& Util.equals(Util.trim(fxBldUse), "")) {
						// 01不動產，不需要主要用途
						cmsBuf01.append("CMSM.colltyp1 in ('01'");
					} else {
						if (Util.equals(cmsBuf02.toString(), "")) {
							cmsBuf02.append("CMSM.colltyp1 in ( " + "?"
									+ cmsCount02++);
						} else {
							cmsBuf02.append(", ?" + cmsCount02++);
						}
					}

				}

				cms01 = Util.equals(cmsBuf01, "") ? "" : " ( "
						+ (cmsBuf01.append(")")).toString() + " ) ";
				cms02 = Util.equals(cmsBuf02, "") ? "" : (cmsBuf02.append(")"))
						.toString();

				if (Util.notEquals(cms01, "") && Util.notEquals(cms02, "")) {
					cmsOr_01 = "OR";
				} else {
					cmsOr_01 = "";
				}

				cmsSql = " right outer join (   select CMSM.CNTRNO from    (	   select 	     cms.c100m01.mainid,cms.c100m01.colltyp1,cms.c100s03a.CNTRNO as CNTRNO  	   from 	     cms.c100s03a,cms.c100m01	     where 	     cms.c100m01.mainid is not null AND	     cms.c100m01.mainid = cms.c100s03a.mainid AND	     cms.c100m01.DELETEDTIME is null  AND cms.c100m01.docstatus in ('13B','14B','15B','16B')  ) AS CMSM    left outer join   cms.C101M04    on    CMSM.mainid = cms.C101M04.mainid    where   (        "
						+ cms01
						+ "        "
						+ cmsOr_01
						+ "        "
						+ cms02
						+ "      )      group by    CMSM.CNTRNO) as CMSS  on CMSS.CNTRNO = T140.CNTRNO";

			}

			cmsCondtion.append(cmsSql);

		}

		// 最外層用MAINID串回L120M01A
		String finalSql = null;

		// finalSql =
		// "SELECT DISTINCT LMS.L140M01A.* FROM LMS.L140M01A WHERE LMS.L140M01A.DELETEDTIME IS NULL AND DOCSTATUS = '030' AND MAINID IN ("
		// + comCondtion
		// +
		// ") ORDER BY CASEDATE DESC,OWNBRID ASC,CASENO DESC,CUSTID ASC,DUPNO ASC WITH UR";

		finalSql = "WITH T140 AS (SELECT DISTINCT LMS.L140M01A.* FROM LMS.L140M01A WHERE LMS.L140M01A.DELETEDTIME IS NULL AND DOCSTATUS = '030' AND MAINID IN ("
				+ comCondtion
				+ ") ) SELECT * FROM T140 "
				+ cmsCondtion
				+ " WHERE T140.CNTRNO IS NOT NULL AND T140.MAINID IS NOT NULL ORDER BY CASEDATE DESC,OWNBRID ASC,CASENO DESC,CUSTID ASC,DUPNO ASC FETCH FIRST 1000 ROWS ONLY WITH UR";

		// System.out.println("finalSql=" + finalSql);
		logger.info("finalSql=" + finalSql);

		String sql = finalSql;
		Query query = entityManager.createNativeQuery(sql); // 排除掉最後面的AND

		if (!StringUtils.isBlank(fxTypCd)) {
			query.setParameter(1, fxTypCd);
		}

		if (!StringUtils.isBlank(fxDocType)) {
			query.setParameter(2, fxDocType);
		}

		if (!StringUtils.isBlank(fxDocKind)) {
			query.setParameter(3, fxDocKind);
		}

		if (!StringUtils.isBlank(fxDocCode)) {
			query.setParameter(4, fxDocCode);
		}

		if (!StringUtils.isBlank(fxUpdaterName)
				&& !StringUtils.isBlank(fxUpdater)) {
			query.setParameter(5, fxUpdater);
		}

		if (!StringUtils.isBlank(fxCaseBrId)) {
			query.setParameter(6, fxCaseBrId);
		}

		if (!StringUtils.isBlank(fxCustId)) {
			query.setParameter(7, fxCustId);
		}

		if (!StringUtils.isBlank(fxDocRslt)) {
			query.setParameter(8, fxDocRslt);
		}

		if (!StringUtils.isBlank(fxGroupId)) {
			if (BranchTypeEnum.授管處.getCode().equals(unitType)
					|| BranchTypeEnum.國金部.getCode().equals(unitType)) {
				query.setParameter(9, "");
			} else {
				query.setParameter(9, fxGroupId);
			}
		}

		if (!StringUtils.isBlank(fxCustName)) {
			query.setParameter(10, fxCustName);
		}

		if (!StringUtils.isBlank(fxBusCode)) {
			query.setParameter(11, fxBusCode);
		}

		if (!StringUtils.isBlank(fxReportOther)) {
			query.setParameter(12, fxReportOther);
		}

		if (!StringUtils.isBlank(fxReportReason1)) {
			query.setParameter(13, fxReportReason1);
		}

		if (!StringUtils.isBlank(fxAreaOption)) {
			query.setParameter(14, fxAreaOption);
		}

		// if (!StringUtils.isBlank(fxLnSubject)) {
		// query.setParameter(15, fxLnSubject);
		// if (hasClsDoc == true) {
		// query.setParameter(515, fxLnSubject);
		// }
		// }

		if (!StringUtils.isBlank(fxRateText)) {
			query.setParameter(16, fxRateText);
		}

		if (!StringUtils.isBlank(fxOtherCondition)) {
			query.setParameter(17, fxOtherCondition);
		}

		if (!StringUtils.isBlank(fxCollateral)) {
			query.setParameter(18, fxCollateral);
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			query.setParameter(19, fxCaseDateS);
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			query.setParameter(20, fxCaseDateE);
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			query.setParameter(21, fxEndDateS);
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			query.setParameter(22, fxEndDateE);
		}

		if (!StringUtils.isBlank(fxCustName)) {
			// 戶名
			query.setParameter(23, fxCustName);
		}

		if (!StringUtils.isBlank(fxCntrNo)) {
			query.setParameter(15, fxCntrNo);
		}

		if (!StringUtils.isBlank(fxCurr)) {
			// 幣別
			String[] currArr = fxCurr.split("\\|");
			int currCount = 2900;
			for (String curr : currArr) {
				query.setParameter(currCount++, curr);
			}
		}

		if (!StringUtils.isBlank(fxGuarantor)
				&& (Util.equals(fxGuarantor, "Y") || Util.equals(fxGuarantor,
						"N"))) {
			// 戶名--無 无 None
			query.setParameter(24, "無");
			query.setParameter(25, "无");
			query.setParameter(26, "None");

		}

		// l140s02a相關***************************************************************

		if (!StringUtils.isBlank(fxProdKind)) {
			// 產品種類
			int prodKindCount = 3500;
			String[] prodKindArr = fxProdKind.split("\\|");
			for (String prodKind : prodKindArr) {
				query.setParameter(prodKindCount++, prodKind);
			}
		}

		if (!StringUtils.isBlank(fxLnSubjectCls)) {
			// 科目
			int subjCodeCount = 3600;
			String[] subjCodeArr = fxLnSubjectCls.split("\\|");
			for (String subjCode : subjCodeArr) {
				query.setParameter(subjCodeCount++, subjCode);
			}
		}

		if (!StringUtils.isBlank(fxLnMonth1)
				|| !StringUtils.isBlank(fxLnMonth2)) {
			// 清償期間－月數
			query.setParameter(37, Util.notEquals(fxLnMonth1, "") ? fxLnMonth1
					: fxLnMonth2);
			query.setParameter(38, Util.notEquals(fxLnMonth2, "") ? fxLnMonth2
					: fxLnMonth1);
		}

		// l140s02d相關***************************************************************
		// --利率
		if (!StringUtils.isBlank(fxRateTextCls)) {
			int rateTypeCount = 3900;
			String[] rateTypeArr = fxRateTextCls.split("\\|");
			for (String rateType : rateTypeArr) {
				query.setParameter(rateTypeCount++, rateType);
			}

		}

		// CMS擔保品相關***************************************************************

		// if (!StringUtils.isBlank(fxCollateral1)) {
		// String[] cmsArr = fxCollateral1.split("\\|");
		// int cmsCount = 3200;
		// for (String cms : cmsArr) {
		// query.setParameter(cmsCount++, cms);
		// }
		// }

		// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		// ─────────────────────────────────
		// 程式修改申請編號：(112)第 2919 號
		//
		// 填報單位：(918)授信審查處
		// 填報人員：(03830)郭祐麟
		// 填報單位內部編號：
		// 修改事項：授審處及各分處協助營業單位辦
		if (!StringUtils.isBlank(fxCollateral1)) {
			if (Util.equals(Util.trim(fxOnlyLand), "Y")) {
				// 不動產只要查純土地
			} else {
				// 擔保品
				int cmsCount01 = 3200;
				int cmsCount02 = 3300;

				String[] bldUseArr = fxBldUse.split("\\|");
				for (String bldUse : bldUseArr) {
					query.setParameter(cmsCount01++, bldUse);
				}

				String[] cmsArr = fxCollateral1.split("\\|");
				for (String cms : cmsArr) {
					if (Util.notEquals(cms, "01")) {
						query.setParameter(cmsCount02++, cms);
					}
				}
			}
		}

		// query.setFirstResult(search.getFirstResult());
		// query.setMaxResults(search.getMaxResults());
		List<Object[]> resultList = query.getResultList();
		return resultList;

	}

	@Override
	public Page<L140M01A> findL140m01aListByL140m01atmp1UserIdForSearch(
			ISearch search, String userId) {

		search = createSearchTemplete();
		if (userId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l140m01atmp1.notesUp", userId);
		}
		search.setMaxResults(Integer.MAX_VALUE);

		search.addOrderBy("caseDate", true);
		search.addOrderBy("ownBrId");
		search.addOrderBy("caseNo", true);
		search.addOrderBy("custId");
		search.addOrderBy("cntrNo");

		// LinkedHashMap<String, Boolean> printSeqMap = new
		// LinkedHashMap<String, Boolean>();
		// printSeqMap.put("endDate", false);
		// printSeqMap.put("custId", false);
		// printSeqMap.put("cntrNo", false);
		// search.setOrderBy(printSeqMap);
		return findPage(search);
	}

	@Override
	public L140M01A findLatestApprovedL140m01a(String cntrNo, String custId,
			String dupNo) {

		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "approveTime",
				"");
		search.addOrderBy("createTime", true);
		search.setMaxResults(1);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainIdAndProperty(
			String caseMainId, String itemType, String property) {

		ISearch search = createSearchTemplete();

		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.itemType", itemType);
		}

		if (property != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "proPerty",
					property);
		}

		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "7");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "8");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty", "7|8");

		search.setMaxResults(Integer.MAX_VALUE);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("printSeq", false);
		printSeqMap.put("custId", false);
		printSeqMap.put("cntrNo", false);
		search.setOrderBy(printSeqMap);
		return find(search);
	}

	@Override
	public L140M01A findByL120m01cMainIdCustIdAndcntrNo(String caseMainId,
			String custId, String dupNo, String cntrNo, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.itemType",
				itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		}
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M01A> findL140m01aListByCustIdForSmallBussC(String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "isRescue", "Y");
		search.addSearchModeParameters(SearchMode.EQUALS, "isCbRefin", "Y");
		search.addSearchModeParameters(SearchMode.EQUALS, "isSmallBuss", "C");
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.setMaxResults(Integer.MAX_VALUE);

		search.addOrderBy("caseDate");
		search.addOrderBy("cntrNo");

		return find(search);
	}

	@Override
	public L140M01A findLatestApprovedL140m01a(String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "approveTime",
				"");
		search.addOrderBy("createTime", true);
		search.setMaxResults(1);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140M01A> findL140m01aByCntrNo(String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return find(search);
	}
	
	/**
	 * 
	 * J-112-0180_10173_B1010 修改額度明細表不變案件-增加編輯核貸成數按鈕以編輯LTV值
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @param itemType
	 * @return
	 */
	@Override
	public List<L140M01A> findByMainIdAndCustIdOnlyContainProperty7(String caseMainId, String custId, String dupNo, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId",
				caseMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.itemType",
				itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.EQUALS, "proPerty", "7");
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

    @Override
    public List<Object[]> findSMEAList(Date sDate, Date eDate) {
        Query query = entityManager
                .createNamedQuery("CES.C270M01A.getSMEAList");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        query.setParameter(1, sdf1.format(sDate));
        query.setParameter(2, sdf2.format(eDate));

        return query.getResultList();
    }

	@Override
	public List<L140M01A> findL140m01aByProjRefCntrNo(String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "projRefCntrNo", cntrNo);
		return find(search);
	}
	
	@Override
	public List<L140M01A> findL140m01aListByL120m01cMainIdExcludeCntrNo(String caseMainId, String itemType, String cntrNo) {
		ISearch search = createSearchTemplete();

		if (itemType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01c.itemType", itemType);
		}

		search.addSearchModeParameters(SearchMode.EQUALS, "l120m01c.mainId", caseMainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "cntrNo", cntrNo);
		
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}
}