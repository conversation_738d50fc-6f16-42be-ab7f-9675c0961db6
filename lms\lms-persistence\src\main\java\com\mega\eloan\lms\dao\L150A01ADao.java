/* 
 * L150A01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L150A01A;

/** 小放會會議紀錄授權檔 **/
public interface L150A01ADao extends IGenericDao<L150A01A> {

	L150A01A findByOid(String oid);

	List<L150A01A> findByMainId(String mainId);

	L150A01A findByUniqueKey(String mainId, String ownUnit, String authType,
			String authUnit);

	List<L150A01A> findByIndex01(String mainId, String ownUnit,
			String authType, String authUnit);
}