/* 
 * LMS7110FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.ELRoleEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.fms.constants.fmsConstants;
import com.mega.eloan.lms.fms.pages.LMS7110M01Page;
import com.mega.eloan.lms.fms.service.LMS7110Service;
import com.mega.eloan.lms.mfaloan.service.MisELF511Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L918M01A;
import com.mega.eloan.lms.model.L918S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 停權解除維護FormHandler
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7110formhandler")
public class LMS7110FormHandler extends AbstractFormHandler {

	@Resource
	LMS7110Service service7110;

	@Resource
	BranchService branch;

	@Resource
	MisELF511Service misElf511Service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	MisdbBASEService misdbBASEService;

	/**
	 * 設定所有分行
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult setAllBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> map = new TreeMap<String, String>();
		List<IBranch> list = branch.getAllBranch();
		for (IBranch thisBranch : list) {
			map.put(Util.trim(thisBranch.getBrNo()),
					Util.trim(thisBranch.getBrName()));
		}
		CapAjaxFormResult caseBrId = new CapAjaxFormResult(map);
		result.set("caseBrId", caseBrId);
		return result;
	}

	/**
	 * 查詢停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL918m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formStopDetail1 = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L918M01A meta = service7110.findL918m01aByMainId(mainId);
		if (meta == null) {
			meta = new L918M01A();
		}
		// 針對欄位做額外處理後設值到前端
		formStopDetail1.set("caseBrId", Util.isEmpty(Util.trim(meta
				.getCaseBrId())) ? "N.A." : Util.trim(meta.getCaseBrId()) + " "
				+ branch.getBranchName(Util.nullToSpace(meta.getCaseBrId())));
		formStopDetail1.set(
				"custId",
				Util.isEmpty(Util.trim(meta.getCustId())) ? "N.A." : Util
						.trim(meta.getCustId()));
		formStopDetail1
				.set("dupNo",
						Util.isEmpty(Util.trim(meta.getDupNo())) ? UtilConstants.Mark.SPACE
								: Util.trim(meta.getDupNo()));
		formStopDetail1
				.set("custName",
						Util.isEmpty(Util.trim(meta.getCustName())) ? UtilConstants.Mark.SPACE
								: Util.trim(meta.getCustName()));
		formStopDetail1.set("caseNo", Util.trim(meta.getCaseNo()));

		formStopDetail1.set("stopUpdater",
				getPerName(Util.trim(meta.getStopUpdater())));
		formStopDetail1.set("stopApprover",
				getPerName(Util.trim(meta.getStopApprover())));
		formStopDetail1.set("caseDate", TWNDate.toFullAD(meta.getCaseDate()));
		formStopDetail1.set("stopApprTime",
				TWNDate.toFullAD(meta.getStopApprTime()));
		result.set("formStopDetail1", formStopDetail1);
		return result;
	}

	/**
	 * 查詢停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL918s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formStopDetail2 = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L918S01A model = service7110.findL918s01aByOid(oid);
		if (model == null) {
			model = new L918S01A();
		}
		formStopDetail2 = DataParse.toResult(model, DataParse.Delete, "oid",
				"mainId");
		// TODO 案號要做特殊處理(顯示要用)
		formStopDetail2.set(
				"modlifyMons",
				(Util.isEmpty(Util.trim(model.getStatusFlag()))) ? Util
						.trim(model.getSuspendMons()) : Util.trim(model
						.getModlifyMons()));
		result.set("formStopDetail2", formStopDetail2);
		return result;
	}

	/**
	 * 依照使用者選擇刪除停權所有相關資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult startDel(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L918M01A l918m01a = service7110.findL918m01aByMainId(mainId);
		List<L918S01A> list = service7110.findL918s01aByMainId(mainId);
		if (isAuthForDel(mainId)) {
			// 具有刪除權限
			// 進行刪除
			service7110.delMainAndSubList(l918m01a, list);
		} else {
			// 不具有刪除權限
			Map<String, String> param = new HashMap<String, String>();
			param.put("txCode", UtilConstants.Mark.HTMLSPACE);
			param.put("methodName", UtilConstants.Mark.HTMLSPACE);
			param.put("authType", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0004", param), getClass());
		}
		// 印出刪除成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}

	/**
	 * 判定是否具有刪除權限
	 * 
	 * @param oid
	 *            文件Oid
	 * @return true: 具有權限, false: 不具有權限
	 */
	private boolean isAuthForDel(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 主管
		if (user.getRoles().containsKey(ELRoleEnum.主管.getCode())) {
			return true;
		} else {
			// 非主管
			L918M01A l918m01a = service7110.findL918m01aByMainId(mainId);
			if (user.getUserId().equals(l918m01a.getUpdater())) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 查詢停權明細檔資料並建立停權主檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult startQuery(PageParameters params)
			throws CapException {
		// 取得當前使用者資料
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
		String mainId = IDGenerator.getUUID();
		String caseBrId = Util.trim(params.getString("caseBrId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));

		List<L918S01A> list = null;

		// 開始設定停權主檔
		L918M01A l918m01a = new L918M01A();
		l918m01a.setMainId(mainId);
		l918m01a.setCustId(custId);
		l918m01a.setDupNo(dupNo);
		l918m01a.setCustName(custName);
		l918m01a.setCaseBrId(caseBrId);
		l918m01a.setOwnBrId(user.getUnitNo());
		l918m01a.setDocStatus(CreditDocStatusEnum.授管處_停權編製中);
		l918m01a.setDocURL("/fms/lms7110m01");
		l918m01a.setStopUpdater(user.getUserId());
		l918m01a.setCaseDate(new Date());

		if (Util.isNotEmpty(caseBrId) && Util.isNotEmpty(custId)
				&& Util.isNotEmpty(dupNo)) {
			// 透過 統編及分行代碼查詢停權明細
			listMap = misElf511Service.selStop3(getAllCust(custId, dupNo),
					caseBrId);
		} else if (Util.isNotEmpty(caseBrId)) {
			// 透過分行代碼查詢停權明細
			listMap = misElf511Service.selStop2(caseBrId);
		} else {
			// 透過統編查詢停權明細
			listMap = misElf511Service.selStop1(getAllCust(custId, dupNo));
		}

		if (listMap != null && !listMap.isEmpty()) {
			list = new ArrayList<L918S01A>();
			for (Map<String, Object> map : listMap) {
				L918S01A tempModel = new L918S01A();
				String allCustId = Util.trim(map.get("ELF511_CUST_ID"));
				// 設定文件編號
				tempModel.setMainId(mainId);
				// 設定案件編號
				tempModel.setSetDocNo(Util.trim(map.get("ELF511_SET_DOC_NO")));
				// 分行別
				tempModel.setBranchNo(Util.trim(map.get("ELF511_BRANCH")));
				if (Util.isNotEmpty(allCustId)) {
					Map<String, String> tempMap = getCustMap(allCustId);
					if (tempMap.containsKey("custId")) {
						// 客戶統編
						tempModel.setCustId(tempMap.get("custId"));
					}
					if (tempMap.containsKey("dupNo")) {
						// 重覆序號
						tempModel.setDupNo(tempMap.get("dupNo"));
					}
				}
				// 停權代碼
				tempModel.setSuspendCode(Util.trim(map
						.get("ELF511_SUSPEND_CODE")));
				// 停權月數
				tempModel.setSuspendMons(Util.parseInt(map
						.get("ELF511_SUSPEND_MONS")));
				// 本次停權月數
				tempModel.setChangeMons(Util.parseInt(map
						.get("ELF511_CHANGE_MONS")));
				// 停權狀態碼
				tempModel.setStopStatus(Util.trim(map.get("ELF511_STATUS")));
				// 最近簽案後首播日
				tempModel.setLoanDate(Util.parseDate(map
						.get("ELF511_LOAN_DATE")));
				// 逾期設定日
				tempModel.setOvDate(Util.parseDate(map.get("ELF511_OV_DATE")));
				// 設定單位
				tempModel.setSetDepart(Util.trim(map.get("ELF511_SET_DEPART")));
				// 設定覆核人員
				tempModel.setSetEmpNo(Util.trim(map.get("ELF511_SET_EMP_NO")));
				// 設定日期
				tempModel
						.setSetDate(Util.parseDate(map.get("ELF511_SET_DATE")));
				// 修改單位
				tempModel.setModDepart(Util.trim(map.get("ELF511_MOD_DEPART")));
				// 修改覆核人員
				tempModel.setModEmpNo(Util.trim(map.get("ELF511_MOD_EMP_NO")));
				// 修改日期
				tempModel
						.setModDate(Util.parseDate(map.get("ELF511_MOD_DATE")));
				// 修改案件編號
				tempModel.setModDocNo(Util.trim(map.get("ELF511_MOD_DOC_NO")));
				// 設定文件編號
				tempModel.setSetMainId(Util.truncateString(
						Util.trim(map.get("ELF511_SET_MAINID")), 32));
				// 修改文件編號
				tempModel.setModMainId(Util.truncateString(
						Util.trim(map.get("ELF511_MOD_MAINID")), 32));
				// 系統代碼
				tempModel.setClassNo(Util.trim(map.get("ELF511_CLASS")));

				// 設定完加入List裡
				list.add(tempModel);
			}
		}

		// 存檔
		service7110.saveMainAndSubList(l918m01a, list);

		// 開始設定要拋到前端的參數
		result.set(EloanConstants.MAIN_ID, mainId);
		result.set(EloanConstants.OID, Util.trim(l918m01a.getOid()));
		result.set("docURL", "/fms/lms7110m01");
		result.set("mainDocStatus", CreditDocStatusEnum.授管處_停權編製中.getCode());
		result.set("custId", custId);
		result.set("dupNo", dupNo);
		result.set("caseBrId", caseBrId);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * 儲存停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL918s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formStopDetail2 = params.getString("formStopDetail2");
		L918S01A model = service7110.findL918s01aByOid(oid);
		if (model == null) {
			model = new L918S01A();
		}
		DataParse.toBean(formStopDetail2, model);
		model.setMainId(mainId);
		if (Util.isEmpty(oid)) {
			// 肯定是新增
			model.setStatusFlag(fmsConstants.stopRelease.statusFlag.新增);
		} else {
			String statusFlag = Util.trim(model.getStatusFlag());
			if ((!Util.trim(model.getSuspendMons()).equals(
					Util.trim(model.getModlifyMons())))
					&& !(fmsConstants.stopRelease.statusFlag.新增
							.equals(statusFlag)
							|| fmsConstants.stopRelease.statusFlag.刪除
									.equals(statusFlag) || fmsConstants.stopRelease.statusFlag.新增後刪除
							.equals(statusFlag))) {
				// 若suspendMons <> modlifyMons 且狀態Flag不為(A、D、R)
				// 則將狀態Flag設為M
				model.setStatusFlag(fmsConstants.stopRelease.statusFlag.修改);
			} else {
				if ((Util.trim(model.getSuspendMons()).equals(Util.trim(model
						.getModlifyMons())))
						&& (fmsConstants.stopRelease.statusFlag.修改
								.equals(statusFlag))) {
					model.setStatusFlag(UtilConstants.Mark.SPACE);
				}
			}
		}
		service7110.save(model);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		return result;
	}

	/**
	 * 刪除停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult delL918s01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String listOid = Util.trim(params.getString("list"));
		List<L918S01A> list = new ArrayList<L918S01A>();
		if (Util.isNotEmpty(listOid)) {
			String oids[] = listOid.split(",");
			for (String oid : oids) {
				L918S01A model = service7110.findL918s01aByOid(oid);
				if (model != null) {
					String modifyMons = Util.trim(model.getModlifyMons());
					Timestamp updateTime = CapDate.getCurrentTimestamp();
					model.setUpdater(user.getUserId());
					model.setUpdateTime(updateTime);
					if (fmsConstants.stopRelease.statusFlag.新增.equals(Util
							.trim(model.getStatusFlag()))) {
						// 若狀態Flag原為A
						// 將狀態Flag註記為R並將L900S01A停權月數(L900S01A.suspendMons)
						// 設為修改停權月數(L900S01A.modlifyMons)，
						// 最後將L900S01A修改停權月數(L900S01A.modlifyMons)更改為0
						model.setStatusFlag(fmsConstants.stopRelease.statusFlag.新增後刪除);
						model.setSuspendMons(Util.parseInt(modifyMons));
						model.setModlifyMons(0);
					} else {
						// 將狀態Flag註記為D並將L900S01A修改停權月數(L900S01A.modlifyMons)更改為0
						model.setStatusFlag(fmsConstants.stopRelease.statusFlag.刪除);
						model.setModlifyMons(0);
					}
					list.add(model);
				}
			}
		}
		service7110.saveList918s01a(list);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}

	/**
	 * 取消刪除停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult undoDelL918s01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String listOid = Util.trim(params.getString("list"));
		List<L918S01A> list = new ArrayList<L918S01A>();
		if (Util.isNotEmpty(listOid)) {
			String oids[] = listOid.split(",");
			for (String oid : oids) {
				L918S01A model = service7110.findL918s01aByOid(oid);
				if (model != null) {
					String suspendMons = Util.trim(model.getSuspendMons());
					Timestamp updateTime = CapDate.getCurrentTimestamp();
					model.setUpdater(user.getUserId());
					model.setUpdateTime(updateTime);
					if (fmsConstants.stopRelease.statusFlag.新增後刪除.equals(Util
							.trim(model.getStatusFlag()))) {
						// 狀態Flag原先為R
						// 將狀態Flag註記設為A並將L900S01A修改停權月數(L900S01A.modlifyMons)
						// 設為L900S01A停權月數(L900S01A.suspendMons)值，
						// 最後將L900S01A停權月數(L900S01A.suspendMons)清空
						model.setStatusFlag(fmsConstants.stopRelease.statusFlag.新增);
						model.setModlifyMons(Util.parseInt(suspendMons));
						model.setSuspendMons(null);
					} else if (fmsConstants.stopRelease.statusFlag.刪除
							.equals(Util.trim(model.getStatusFlag()))) {
						// 將狀態Flag清空並將L900S01A修改停權月數(L900S01A.modlifyMons)
						// 設為L900S01A停權月數(L900S01A.suspendMons)值
						model.setStatusFlag(UtilConstants.Mark.SPACE);
						model.setModlifyMons(Util.parseInt(suspendMons));
					}
					list.add(model);
				}
			}
		}
		service7110.saveList918s01a(list);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(
						UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * flow案件簽報書
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String list = Util.trim(params.getString("list"));
		if (Util.isNotEmpty(oid)) {
			// 若oid不為空，則走單一流程
			L918M01A l918m01a = (L918M01A) service7110.findL918m01aByOid(oid);
			if (l918m01a == null) {
				logger.info("\n l918m01a=====> null");
			}
			startFlow(params, l918m01a, oid);
		} else {
			// 若list不為空，代表同時有很多案件跑同一流程
			if (Util.isNotEmpty(list)) {
				String oids[] = list.split(",");
				for (String thisOid : oids) {
					L918M01A l918m01a = (L918M01A) service7110
							.findL918m01aByOid(thisOid);
					if (l918m01a == null) {
						logger.info("\n l918m01a=====> null");
					}
					startFlow(params, l918m01a, thisOid);
				}
			} else {
				// 例外情形，正常下不會發生
				logger.info("\n l918m01a=====> null");
			}
		}
		return new CapAjaxFormResult();
	}

	/**
	 * 開始進行流程
	 * 
	 * @param params
	 * @param parent
	 * @param l918m01a
	 * @param oid
	 * @throws CapMessageException
	 */
	private void startFlow(PageParameters params,
			L918M01A l918m01a, String oid) throws CapMessageException {
		if (!Util.isEmpty(l918m01a)) {
			try {
				service7110.flowAction(oid, l918m01a,
						params.containsKey("flowAction"),
						params.getString("flowAction", ""));
			} catch (FlowMessageException t1) {
				logger.error(
						"[flowAction] lms7110Service.flowAction FlowException!!",
						t1);
				if (t1.getExtraMessage() == null
						|| t1.getExtraMessage().isEmpty()) {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
				} else {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage(), t1.getExtraMessage()),
							getClass());
				}
			} catch (Throwable t1) {
				logger.error(
						"[flowAction] lms7110Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}
	}

	/**
	 * 取得完整Id(統編加重覆序號)
	 * 
	 * @param custid
	 * @param dupNo
	 * @return
	 */
	private String getAllCust(String custid, String dupNo) {
		StringBuilder strb = new StringBuilder();
		// if ("0".equals(dupNo)) {
		// dupNo = "";
		// }
		return strb.append(CapString.fillString(custid, 10, false, ' '))
				.append(dupNo).toString();
	}

	/**
	 * 依照完整客戶統編取得Map資料
	 * 
	 * @param allCust
	 *            完整客戶統編
	 * @return Map資料
	 */
	private Map<String, String> getCustMap(String allCust) {
		Map<String, String> map = new HashMap<String, String>();
		if (Util.isNotEmpty(allCust)) {
			map.put("custId", Util.trim((LMSUtil.checkSubStr(allCust, 0,
					allCust.length() - 1)) ? allCust.substring(0,
					allCust.length() - 1) : UtilConstants.Mark.SPACE));
			map.put("dupNo",
					Util.trim((LMSUtil.checkSubStr(allCust,
							allCust.length() - 1)) ? allCust.substring(allCust
							.length() - 1) : UtilConstants.Mark.SPACE));
		}
		return map;
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者id + " " + 使用者名稱
	 */
	private String getPerName(String id) {
		StringBuilder sb = new StringBuilder();
		if (!Util.isEmpty(userinfoservice.getUserName(id))) {
			sb.append(id).append(" ").append(userinfoservice.getUserName(id));
		} else {
			sb.append(id);
		}
		return sb.toString();
	}

	/**
	 * 查詢目前停權起迄
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryStopELF510(PageParameters params)
			throws CapException {
		Properties pop = MessageBundleScriptCreator
		.getComponentResource(LMS7110M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		
		L918M01A model = service7110.findL918m01aByMainId(mainId);
		if (model == null) {
			model = new L918M01A();
		}
		String brno = model.getCaseBrId();
		StringBuffer stopMsg = new StringBuffer("");
		if (Util.notEquals(brno, "")) {
			List<Map<String, Object>> listMap = misdbBASEService
					.elf510_queryStop(brno);
			if (!listMap.isEmpty()) {
				for (Map<String, Object> stopData : listMap) {
					if (Util.equals(stopData.get("ELF510_CLASS"), "CLS")) {
						// 個金
						stopMsg.append(pop.getProperty("L918M01A.CLS")); //"個金"
					} else {
						// 企金
						stopMsg.append(pop.getProperty("L918M01A.LMS")); //企金
					}
					stopMsg.append("=>")
							.append(stopData.get("ELF510_SUSPENDED_BD"))
							.append("~")
							.append(stopData.get("ELF510_SUSPENDED_ED"))
							.append("<br/>");
				}
			}
		}else{
			stopMsg.append(pop.getProperty("L918M01A.message02"));
		}

		if (Util.equals(stopMsg.toString(), "")) {
			stopMsg.append(brno).append(pop.getProperty("L918M01A.message01"));  //分行目前無停權記錄
		}

		result.set("stopMsg", stopMsg.toString());
		return result;
	}
}
