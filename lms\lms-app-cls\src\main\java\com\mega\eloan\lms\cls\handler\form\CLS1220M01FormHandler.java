package com.mega.eloan.lms.cls.handler.form;

import java.io.File;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.MEGAImageApiEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.gwclient.BillHunterMailTypeEnum;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.model.SmsContent;
import com.mega.eloan.common.model.SmsProfile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.MEGAImageService;
import com.mega.eloan.common.service.SmsService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1220M01Page;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01C;
import com.mega.eloan.lms.model.C122S01A;
import com.mega.eloan.lms.model.C122S01B;
import com.mega.eloan.lms.model.C122S01E;
import com.mega.eloan.lms.model.C900M01M;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上申貸原始資料(個金)
 * </pre>
 * 
 * @since 2015/4/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/4/18,003738,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1220m01formhandler")
@DomainClass(C122M01A.class)
public class CLS1220M01FormHandler extends AbstractFormHandler {

	@Resource
	CLS1220Service service;

	@Resource
	RetrialService retrialService;
	
	@Resource
	UserInfoService userInfoService;	
	
	@Resource
	BranchService branchService;

	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;

	@Resource
	DocLogService docLogService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	private EmailClient emailClient;
	
	@Resource
	private SmsService smsService;
	
	@Resource
    EloandbBASEService eloandbService;
	
	@Resource
	MEGAImageService mEGAImageService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	NumberService number;
	
	@Resource
	CodeTypeService codeTypeService;
	
	
	Properties prop_cls1220m01 = MessageBundleScriptCreator.getComponentResource(CLS1220M01Page.class);
	Properties prop_abstractEloan = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	/**
	 * 查詢文件
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int page = Util.parseInt(params.getString("page"));

		C122M01A meta = null;
		if (mainOid != null) {
			meta = service.getC122M01A_byOid(mainOid); 
		}
		switch (page) {
		case 1:
			LMSUtil.addMetaToResult(result, meta, new String[]{  "ownBrId", "custId", "dupNo"
					, "custName", "docStatus"
					, "notifyWay", "notifyCust", "notifyMemo"
					, "idCardIssueArea", "idCardIssueDate", "idCardChgFlag", "idCardPhoto"
					, "applyCurr"
					, "maturity", "nowExtend", "extYear"
					, "purpose", "resource"
					, "createTime", "updateTime", "randomCode"
					, "contactEmpNo", "marketingStaff"});
				
			result.set("ownBrName", branchService.getBranchName(meta.getOwnBrId()));
			result.set("docStatusCN", LMSUtil.getDesc(service.get_DocStatusDescMap(), Util.trim(meta.getDocStatus()) ));
			
			result.set("applyAmt", NumConverter.addComma(meta.getApplyAmt()==null?BigDecimal.ZERO:meta.getApplyAmt()));			
			result.set("applyTS", CapDate.formatDate(meta.getApplyTS(), "yyyy-MM-dd"));
			result.set("applyDocId", meta.getMainId());
			result.set("applyDocStatusCN", LMSUtil.getDesc(service.get_ApplyDocStatusDescMap(), Util.trim(meta.getApplyStatus()) ));
			result.set("n_applyStatus", Util.trim(Util.isNotEmpty(Util.trim(meta.getN_applyStatus()))?meta.getN_applyStatus():meta.getApplyStatus()));
			result.set("approveAmt", NumConverter.addComma(meta.getApproveAmt()==null?BigDecimal.ZERO:meta.getApproveAmt()));
			result.set("creator", Util.trim(meta.getCreator())+" "+Util.trim(new UserNameFormatter(userInfoService).reformat(meta.getCreator())));
			result.set("updater", Util.trim(meta.getUpdater())+" "+Util.trim(new UserNameFormatter(userInfoService).reformat(meta.getUpdater())));
			result.set("contactEmpNm", userInfoService.getUserName(meta.getContactEmpNo()));
			
			result.set("receDate", "");			
			if(true){	
				_sel_c122s01a_batchNo(result, meta);				
			}
			break;
		case 2:
			C120S01A c120s01a = service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01a==null){
				c120s01a = new C120S01A(); 
			}
			LMSUtil.addMetaToResult(result, c120s01a, ClsUtil.C122_C120S01A_COLS);
			result.set("marry", ClsUtil.convert_nb_to_eloan_marry(c120s01a));
			break;
		case 3:
			C120S01B c120s01b = service.findC120S01B(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01b==null){
				c120s01b = new C120S01B(); 
			}
			LMSUtil.addMetaToResult(result, c120s01b, ClsUtil.C122_C120S01B_COLS);
			break;
		case 4:
			C120S01C c120s01c = service.findC120S01C(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01c==null){
				c120s01c = new C120S01C(); 
			}
			LMSUtil.addMetaToResult(result, c120s01c, ClsUtil.C122_C120S01C_COLS);
			break;
		case 5:
			break;
		default:
			break;			
		}		
		
		return defaultResult( params, meta, result);
	}
	
	private void _sel_c122s01a_batchNo(CapAjaxFormResult result, C122M01A meta){
		CapAjaxFormResult selItem = new CapAjaxFormResult();
		CapAjaxFormResult selItemOrder = new CapAjaxFormResult();
		if(true){
			_setMapWithOrder(selItem, selItemOrder, "c122s01a_batchNo"
					, service.getC122S01A_batchNo(meta.getMainId(), Util.equals(meta.getDocStatus(), CLSDocStatusEnum.編製中.getCode())));
		}				
		result.set("selItem", selItem);
		result.set("selItemOrder", selItemOrder);
	}
	
	private void _setMapWithOrder(CapAjaxFormResult result, CapAjaxFormResult rItemOrder
			, String elm, Map<String, String> m){
		List<String> ord = new ArrayList<String>();
		if(m instanceof LinkedHashMap){
			for(String k : m.keySet()){
				ord.add(k);
			}
		}else{
			TreeSet<String> ts = new TreeSet<String>();
			ts.addAll(m.keySet());
			ord.addAll(ts);
		}
		result.set(elm, new CapAjaxFormResult(m));
		rItemOrder.set(elm, ord);
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C122M01A meta,
			CapAjaxFormResult result) {		
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("applyDocStatus", Util.trim(meta.getApplyStatus()));
		result.set("titInfo", getTitInfo(meta));
		return result;
	}
	
	private String getTitInfo(C122M01A meta) {
		StringBuffer title = new StringBuffer();
		title.append(CapString.trimNull(meta.getCustId()));
		title.append(' ');
		title.append(CapString.trimNull(meta.getDupNo()));
		title.append(' ');
		title.append(CapString.trimNull(meta.getCustName()));
		return title.toString();
	}
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}
	
	private IResult _saveAction(PageParameters params, String tempSave)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//-------------------
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);		
		String page = params.getString(EloanConstants.PAGE);
		C122M01A meta = null;
		try{
			meta = service.getC122M01A_byOid(mainOid);
			
			if ("01".equals(page)) {
				CapBeanUtil.map2Bean(params, meta, new String[] {"notifyCust","notifyMemo"
						, "n_applyStatus", "approveAmt", "contactEmpNo"
				});
				if(meta.getApproveAmt()!=null){
					meta.setApproveCurr("TWD");
				}
				if(true){					
					String contactEmpNo = Util.trim(meta.getContactEmpNo());
					if(Util.isNotEmpty(contactEmpNo)){
						// 不足六碼前補0
						if (contactEmpNo.length() < 6) {
							contactEmpNo = Util.addZeroWithValue(contactEmpNo, 6);
						}
						meta.setContactEmpNo(contactEmpNo);	
					}					
				}
			}
			
			service.save(meta);			
			result.set(KEY, true);				
			
			List<String> promptMsg_list = new ArrayList<String>();
			if (UtilConstants.C122_ApplyStatus.已核准.equals(meta.getN_applyStatus())){
				C122S01B s01b = service.getC122S01B_byMainId(meta.getMainId());
				if (s01b == null) {
					result.set(KEY, false);	
					promptMsg_list.add(prop_cls1220m01.getProperty("C122S01B.loanDataNotFound"));
				}else{
					if(meta.getApproveAmt()==null || meta.getApproveAmt().compareTo(BigDecimal.ZERO)<=0){
						result.set(KEY, false);	
						promptMsg_list.add(prop_cls1220m01.getProperty("C122M01A.approveAmtShouleGt0"));
					}
				}
			}
			
			if(true){
				String contactEmpNo = Util.trim(meta.getContactEmpNo());
				if(Util.isNotEmpty(contactEmpNo)){
					String contactEmpNm = userInfoService.getUserName(contactEmpNo);
					if(Util.equals(contactEmpNm, contactEmpNo)){
						promptMsg_list.add(MessageFormat.format(prop_cls1220m01.getProperty("C122M01A.contactEmpNo_notValid"), contactEmpNo));
					}					
				}
			}
			
			if(promptMsg_list.size()>0){
				String promptMsg = StringUtils.join(promptMsg_list, UtilConstants.Mark.HTMLBR);
				if(Util.isNotEmpty(promptMsg)){
					if(allowIncomplete){
						result.set("IncompleteMsg", promptMsg);		
//						result.set(CapConstants.AJAX_NOTIFY_MESSAGE, promptMsg);
					}else{
						throw new CapMessageException(promptMsg, getClass());	
					}
				}
			}
		}catch(Exception e){
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}	
		
		result.add(query(params));
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify + AuthType.Accept, CheckDocStatus = true)
	public IResult flowAction(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C122M01A meta = null;
		C122S01A c122s01a = null;
		final String NO_NEXT_DOCSTATUS = "-1"; //在C122M01A中，docStatus可能空白，所以用-1表示未決定的status
		String nextDocStatus = NO_NEXT_DOCSTATUS;
		try{
			meta = service.getC122M01A_byOid(mainOid);
			
			if(Util.equals(meta.getDocStatus(), CLSDocStatusEnum.編製中.getCode())){
				if(Util.equals("取消編製", decisionExpr)){
					nextDocStatus = "";
				}else{
					nextDocStatus = CLSDocStatusEnum.待覆核.getCode();
				}
			}else if(Util.equals(meta.getDocStatus(), CLSDocStatusEnum.待覆核.getCode())){
				if(Util.equals("核定", decisionExpr)){
					if(Util.equals(meta.getUpdater(), user.getUserId())){
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
					}else{
						nextDocStatus = "";
					}
				}else if(Util.equals("退回", decisionExpr)){
					nextDocStatus = CLSDocStatusEnum.編製中.getCode();	
				}else{
					throw new CapMessageException("不明的狀態:"+decisionExpr, getClass());
				}
			}else{
				nextDocStatus = CLSDocStatusEnum.編製中.getCode();
			}
		}catch(CapMessageException e){
			throw e;
		}catch(Exception e){
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}	
		
		if(Util.notEquals(NO_NEXT_DOCSTATUS, nextDocStatus)){					
			if(Util.equals(nextDocStatus, CLSDocStatusEnum.待覆核.getCode())){
				//編製中→待覆核
				docLogService.record(meta.getOid(), DocLogEnum.FORWARD);	
			}else if(Util.equals(nextDocStatus, "")){				
				if(Util.equals(meta.getDocStatus(), CLSDocStatusEnum.編製中.getCode())){
					//編製中→全部進件資料
					docLogService.record(meta.getOid(), DocLogEnum.BACK);
				}else if(Util.equals(meta.getDocStatus(), CLSDocStatusEnum.待覆核.getCode())){
					//待覆核→全部進件資料
					docLogService.record(meta.getOid(), DocLogEnum.ACCEPT);
					
					if(Util.isNotEmpty(Util.trim(meta.getN_applyStatus()))){
						Set<String> endStatusSet = new HashSet<String>();
						endStatusSet.add(UtilConstants.C122_ApplyStatus.不承做);
						endStatusSet.add(UtilConstants.C122_ApplyStatus.轉臨櫃);
						endStatusSet.add(UtilConstants.C122_ApplyStatus.已核准);
						
						if(endStatusSet.contains(meta.getN_applyStatus())){
							//在 sendEmails(...) 判斷 notifyCust.notifyCust 來決定 "是否需寄 mail"
							sendEmails(meta, Util.trim(meta.getN_applyStatus()));
//							sendSMSs(meta);
						}
						
					}
					if(Util.isNotEmpty(Util.trim(meta.getN_applyStatus())) 
							&& Util.notEquals(meta.getN_applyStatus(), meta.getApplyStatus())){
						meta.setApplyStatus(meta.getN_applyStatus());
						meta.setN_applyStatus("");
					}
					/*
					 * 若原本勾已核貸，且引入額度資料
					 * 但在 selectBox 又改成不承做
					 * 此時，覆核不應寫入  ApproveFlag==Y
					 */
					if(Util.equals(UtilConstants.C122_ApplyStatus.已核准, meta.getApplyStatus())){
						c122s01a = service.getC122S01A_mainIdBatchNo(meta.getMainId(), service.maxBatchNoInC122S01A(meta.getMainId()));
						if(c122s01a!=null && Util.notEquals("Y", c122s01a.getApproveFlag() )){
							logger.trace("c122s01a.approveFlag ["+c122s01a.getApproveFlag()+" -> Y]");
							c122s01a.setApproveFlag("Y");
						}
						
						List<C122S01B> c122s01b_list = clsService.findC122S01B_mainIdBatchNo(meta.getMainId(), c122s01a.getBatchNo());
						Set<String> cntrNoSet = new HashSet<String>();
						for(C122S01B c122s01b : c122s01b_list){
							String cntrNo = Util.trim(c122s01b.getCntrNo());
							cntrNoSet.add(cntrNo);
						}
						if(cntrNoSet.size()>0){
							String srcflag = "1";
							for(String cntrNo: cntrNoSet){
								misdbBASEService.update_elf459_srcflag(srcflag, cntrNo);	
							}
						}
					}
				}
				
			}else if(Util.equals(nextDocStatus, CLSDocStatusEnum.編製中.getCode())){
				if(Util.equals(meta.getDocStatus(), CLSDocStatusEnum.待覆核.getCode())){
					//待覆核→退回
					docLogService.record(meta.getOid(), DocLogEnum.BACK);
				}else{
					//已收件轉編製中
				}
			}
			meta.setDocStatus(nextDocStatus);	
			//=========================
			service.daoSaveC122M01A(meta);
			if(c122s01a!=null){
				service.daoSaveC122S01A(c122s01a);	
			}
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(value = AuthType.Modify , CheckDocStatus = true)
	public IResult deleteC122S01B(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//-------------
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int batchNo = Util.parseInt(params.getString("batchNo"));
		
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		String mainId = meta.getMainId();
		C122S01A c122s01a = service.getC122S01A_mainIdBatchNo(mainId, batchNo);
		
		verifyApproveFlagY(c122s01a);//覆核過不可再異動
		
		if(true){
			List<String> c122s01b_oid_list = new ArrayList<String>();
			for(String s01b_oid: Util.trim(params.getString("oids")).split("\\|")){
				c122s01b_oid_list.add(s01b_oid);	
			}
			service.delC122S01B(meta, c122s01b_oid_list);	
		}
		return defaultResult( params, meta, result);		
		
	}
	
	@DomainAuth(value = AuthType.Modify , CheckDocStatus = true)
	public IResult addC122S01A_B(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//-------------
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int unused_batchNo = -1;
		int batchNo = params.getInt("batchNo", unused_batchNo);
		
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		String mainId = meta.getMainId();
		C122S01A c122s01a = null;
		if(batchNo>=0){
			c122s01a = service.getC122S01A_mainIdBatchNo(mainId, batchNo);
		}
		
		if(c122s01a==null){
			c122s01a = new C122S01A();
			c122s01a.setBatchNo(unused_batchNo);//BatchNo先填入-1
		}
		
		verifyApproveFlagY(c122s01a);//覆核過不可再異動
		
		List<L140S02A> l140s02a_list = new ArrayList<L140S02A>();		
		List<String> promptMsg = new ArrayList<String>();
		for(String s02a_oid: Util.trim(params.getString("oids")).split("\\|")){
			L140S02A l140s02a = service.getL140S02A_byOid(s02a_oid);
			if(l140s02a==null){
				continue;
			}			
			l140s02a_list.add(l140s02a);	
		}
		if(l140s02a_list.size()>0){
			//當BatchNo為-1，會【把 c122s01a 填入 mainId，把BatchNo另外塞值】
			service.addC122S01A_B(meta, c122s01a, l140s02a_list, promptMsg);			
		}
		if(promptMsg.size()>0){
			result.set("promptMsg", "額度序號-產品序號 已存在：<br>"+StringUtils.join(promptMsg, "<br>"));
		}

		if(true){	
			_sel_c122s01a_batchNo(result, meta);
			result.set("currentBatchNo", c122s01a.getBatchNo());
		}
		return defaultResult( params, meta, result);
	}
	
	private void verifyApproveFlagY(C122S01A c122s01a) throws CapMessageException{
		if(Util.equals(c122s01a.getApproveFlag(), "Y")){
			throw new CapMessageException("已覆核，不可再異動", getClass());
		}
	} 
	
	@DomainAuth(value = AuthType.Modify , CheckDocStatus = false)
	public IResult getC122S01B(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		String c122s01b_oid = Util.trim(params.getString("c122s01b_oid"));
		C122S01B c122s01b = service.getC122S01B_byOid(c122s01b_oid);
		LMSUtil.addMetaToResult(result, c122s01b, new String[]{  "cntrNoMainId"
				, "seq", "cntrNo", "prodKind"
				, "loanCurr", "loanAmt", "rateDesc"});
		
		Map<String, String> prodKindMap = retrialService.get_crs_prodKindMap();		
		L140S02A l140s02a = service.getL140S02A_byUK(c122s01b.getCntrNoMainId(), c122s01b.getSeq());
		result.set("prodKindDesc", LMSUtil.getDesc(prodKindMap, c122s01b.getProdKind()));
		result.set("loanAmt_o", l140s02a.getLoanAmt());
		result.set("rateDesc_o", l140s02a.getRateDesc());
		return defaultResult( params, meta, result);		
	}
	
	@DomainAuth(value = AuthType.Modify , CheckDocStatus = false)
	public IResult saveC122S01B(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//-------------
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		String c122s01b_oid = Util.trim(params.getString("c122s01b_oid"));
		C122S01B c122s01b = service.getC122S01B_byOid(c122s01b_oid);
		C122S01A c122s01a = service.getC122S01A_mainIdBatchNo(meta.getMainId(), c122s01b.getBatchNo());
			
		verifyApproveFlagY(c122s01a);//覆核過不可再異動
		
		if(true){			
			CapBeanUtil.map2Bean(params, c122s01b, new String[] {"loanAmt", "rateDesc"});		
			service.save(c122s01b);						
		}		
		return defaultResult( params, meta, result);
		
	}
	
	@DomainAuth(value = AuthType.Modify , CheckDocStatus = false)
	public IResult queryUnMatchReason(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		List<C122M01A> list = service.queryUnMatchReason(custId, dupNo);
		result.set("hasData", list.size()>0?"Y":"N");
		result.set("custId", custId);
		result.set("dupNo", dupNo);
		return result;		
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult getL140M01AParam(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		L140M01A l140m01a = service.findL140M01A(Util.trim(params.getString("cntrNoMainId")));
		if(l140m01a==null){
			result.set("isFound", "N");
		}else{
			result.set("isFound", "Y");
			//ref CLS1141M01FormHandler:: getRelateA
			L120M01C l120m01c = l140m01a.getL120m01c();
			result.set(EloanConstants.MAIN_ID, l140m01a.getMainId());
			result.set(EloanConstants.OID, l140m01a.getOid());
			result.set("CaseMainId", l120m01c.getMainId());
			result.set("itemType", l120m01c.getItemType());
			result.set("CLSAction_isReadOnly", "Y");
			//------------
			result.set("l140m01a_oid", l140m01a.getOid());
		}
		return result;		
	}
	
	
	/**
	 * 傳送電子帳單系統E-Mail
	 * @param meta
	 */
	private void sendEmails(C122M01A meta, String resultStatus) {
		if(!Util.equals(meta.getNotifyCust(), "Y")){
			return;
		}
		//-----------
		C120S01A s01a = service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
		String email = Util.trim(s01a==null?"":s01a.getEmail());
		if(Util.isEmpty(email)){
			return;
		}
		
		if(Util.equals(UtilConstants.C122_ApplyStatus.不承做, resultStatus) 
			|| Util.equals(UtilConstants.C122_ApplyStatus.轉臨櫃, resultStatus)
			|| Util.equals(UtilConstants.C122_ApplyStatus.已核准, resultStatus)){
			BillHunterMailTypeEnum mailType = BillHunterMailTypeEnum.getEnum(resultStatus);
			logger.debug("bill email address={}", email);

			String body = "";
			if(true){
				String brId = Util.trim(meta.getOwnBrId());
				String brTel = "";
				if(true){
					IBranch iBranch = branchService.getBranch(brId);
					if(iBranch!=null){
						brTel = iBranch.getTel();
					}
				}
				
				List<String> mail_body = new ArrayList<String>();	
				mail_body.add("<img alt='Embedded Image' src='data:image/jpg;base64,/9j/4AAQSkZJRgABAgEASABIAAD/4QZFRXhpZgAATU0AKgAAAAgABwESAAMAAAABAAEAAAEaAAUAAAABAAAAYgEbAAUAAAABAAAAagEoAAMAAAABAAIAAAExAAIAAAAcAAAAcgEyAAIAAAAUAAAAjodpAAQAAAABAAAApAAAANAACvyAAAAnEAAK/IAAACcQQWRvYmUgUGhvdG9zaG9wIENTMyBXaW5kb3dzADIwMDg6MDk6MjQgMTQ6Mjc6MTAAAAAAA6ABAAMAAAAB//8AAKACAAQAAAABAAAD1aADAAQAAAABAAAAVAAAAAAAAAAGAQMAAwAAAAEABgAAARoABQAAAAEAAAEeARsABQAAAAEAAAEmASgAAwAAAAEAAgAAAgEABAAAAAEAAAEuAgIABAAAAAEAAAUPAAAAAAAAAEgAAAABAAAASAAAAAH/2P/gABBKRklGAAECAABIAEgAAP/tAAxBZG9iZV9DTQAC/+4ADkFkb2JlAGSAAAAAAf/bAIQADAgICAkIDAkJDBELCgsRFQ8MDA8VGBMTFRMTGBEMDAwMDAwRDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAENCwsNDg0QDg4QFA4ODhQUDg4ODhQRDAwMDAwREQwMDAwMDBEMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwM/8AAEQgADgCgAwEiAAIRAQMRAf/dAAQACv/EAT8AAAEFAQEBAQEBAAAAAAAAAAMAAQIEBQYHCAkKCwEAAQUBAQEBAQEAAAAAAAAAAQACAwQFBgcICQoLEAABBAEDAgQCBQcGCAUDDDMBAAIRAwQhEjEFQVFhEyJxgTIGFJGhsUIjJBVSwWIzNHKC0UMHJZJT8OHxY3M1FqKygyZEk1RkRcKjdDYX0lXiZfKzhMPTdePzRieUpIW0lcTU5PSltcXV5fVWZnaGlqa2xtbm9jdHV2d3h5ent8fX5/cRAAICAQIEBAMEBQYHBwYFNQEAAhEDITESBEFRYXEiEwUygZEUobFCI8FS0fAzJGLhcoKSQ1MVY3M08SUGFqKygwcmNcLSRJNUoxdkRVU2dGXi8rOEw9N14/NGlKSFtJXE1OT0pbXF1eX1VmZ2hpamtsbW5vYnN0dXZ3eHl6e3x//aAAwDAQACEQMRAD8Abo9GFbmNdntc/FY5osaySfeH7Huaz3vrrdX+kYxWOvdNoxXF9Nbq63N9Wh7mhosrLmMlnpez/DVu+hXfX/hPpoOJi1i0ux81jnAS5ppsgiW7Ro5rt/qbPS9N3rep/NK/1rH6jZh456nmeligkUtfVcSXR9Kz1n2Xf1d/6NZvAfDw9Ufx9T0mQ5TzcMkZzGLS4e3n+WPzx4Pb9XG4DaqH1WvOXjtNQqPpuPuf6rjXY1mv0sT6d/8AwaazA6abfTfmYjh6xqL9kt2bPV+17t383v8A1fZ/pPz1ddht9k9RfwfTmm+Yhv0fztnp+mouwmQN3UnRIiacjnTapBHsB/jf+hskskus5j/qU/8A4Gck9M6ZZWHm7Ha41WWFjdwIdW702Ue1zWvtyf52n/g1Tt6biz+htLh+8NWz4e73LoTgidepv7RNGT46f9NBuwMXefX6q31IE+pRfujWPpqWIzj5eL7b/wC6WDLi4jxmxroMcx/0cEHnH4NjfouDh9xQix7NHAhdEendN79WqH/WLv7kN3Teld+sVD/rF/8A5FTQnnHzQ4vwScmDoZj/AKnl/wC8cIKQWqel9Fn/AJapH/oPf/6TSHS+jf8Al1V/7D5H/pNWIyveJHmoZoDpM/8AU8v/AHjmBOWsdy0H4hag6X0b/wAuqv8A2HyP/SakOl9G/wDLqr/tjI/9Jp4PYFf7+OvUJ1448n/eOOaKzxLfh/tQ3Y7h9Eh34Fbv7L6N/wCXVP8A2xkf+kkx6X0b/wAuqj/1jI/9Jp4nMdJfUMUsnKy7j+7DJH/uXny1zTDgR8UatbX7L6NGvWaY7zRfH/ntQHS+ifmdapn/AML5Ef8AntSRyHrGQ+hYJnEB6ZSl4GGQH/otKtWK1aZ0zpXbrNP/ALD3/wDkUdnTem9usU/+w9/9ydxjx/xZMXuR/rf4k/8AvWmktD9ndO/8t6f/AGHv/uT/ALN6d/5cU/8AsPf/AHJcQ8f8WSOOP9b/ABZ/96//2f/tC7pQaG90b3Nob3AgMy4wADhCSU0EBAAAAAAABxwCAAACAFAAOEJJTQQlAAAAAAAQ74MM8t+VNUyemMLOKAti7zhCSU0D7QAAAAAAEABIAAAAAQACAEgAAAABAAI4QklNBCYAAAAAAA4AAAAAAAAAAAAAP4AAADhCSU0EDQAAAAAABAAAAB44QklNBBkAAAAAAAQAAAAeOEJJTQPzAAAAAAAJAAAAAAAAAAABADhCSU0ECgAAAAAAAQAAOEJJTScQAAAAAAAKAAEAAAAAAAAAAjhCSU0D9QAAAAAASAAvZmYAAQBsZmYABgAAAAAAAQAvZmYAAQChmZoABgAAAAAAAQAyAAAAAQBaAAAABgAAAAAAAQA1AAAAAQAtAAAABgAAAAAAAThCSU0D+AAAAAAAcAAA/////////////////////////////wPoAAAAAP////////////////////////////8D6AAAAAD/////////////////////////////A+gAAAAA/////////////////////////////wPoAAA4QklNBAgAAAAAABAAAAABAAACQAAAAkAAAAAAOEJJTQQeAAAAAAAEAAAAADhCSU0EGgAAAAADOwAAAAYAAAAAAAAAAAAAAFQAAAPVAAAAAwB0AG8AcAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAD1QAAAFQAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAQAAAAAAAG51bGwAAAACAAAABmJvdW5kc09iamMAAAABAAAAAAAAUmN0MQAAAAQAAAAAVG9wIGxvbmcAAAAAAAAAAExlZnRsb25nAAAAAAAAAABCdG9tbG9uZwAAAFQAAAAAUmdodGxvbmcAAAPVAAAABnNsaWNlc1ZsTHMAAAABT2JqYwAAAAEAAAAAAAVzbGljZQAAABIAAAAHc2xpY2VJRGxvbmcAAAAAAAAAB2dyb3VwSURsb25nAAAAAAAAAAZvcmlnaW5lbnVtAAAADEVTbGljZU9yaWdpbgAAAA1hdXRvR2VuZXJhdGVkAAAAAFR5cGVlbnVtAAAACkVTbGljZVR5cGUAAAAASW1nIAAAAAZib3VuZHNPYmpjAAAAAQAAAAAAAFJjdDEAAAAEAAAAAFRvcCBsb25nAAAAAAAAAABMZWZ0bG9uZwAAAAAAAAAAQnRvbWxvbmcAAABUAAAAAFJnaHRsb25nAAAD1QAAAAN1cmxURVhUAAAAAQAAAAAAAG51bGxURVhUAAAAAQAAAAAAAE1zZ2VURVhUAAAAAQAAAAAABmFsdFRhZ1RFWFQAAAABAAAAAAAOY2VsbFRleHRJc0hUTUxib29sAQAAAAhjZWxsVGV4dFRFWFQAAAABAAAAAAAJaG9yekFsaWduZW51bQAAAA9FU2xpY2VIb3J6QWxpZ24AAAAHZGVmYXVsdAAAAAl2ZXJ0QWxpZ25lbnVtAAAAD0VTbGljZVZlcnRBbGlnbgAAAAdkZWZhdWx0AAAAC2JnQ29sb3JUeXBlZW51bQAAABFFU2xpY2VCR0NvbG9yVHlwZQAAAABOb25lAAAACXRvcE91dHNldGxvbmcAAAAAAAAACmxlZnRPdXRzZXRsb25nAAAAAAAAAAxib3R0b21PdXRzZXRsb25nAAAAAAAAAAtyaWdodE91dHNldGxvbmcAAAAAADhCSU0EKAAAAAAADAAAAAE/8AAAAAAAADhCSU0EEQAAAAAAAQEAOEJJTQQUAAAAAAAEAAAACDhCSU0EDAAAAAAFKwAAAAEAAACgAAAADgAAAeAAABpAAAAFDwAYAAH/2P/gABBKRklGAAECAABIAEgAAP/tAAxBZG9iZV9DTQAC/+4ADkFkb2JlAGSAAAAAAf/bAIQADAgICAkIDAkJDBELCgsRFQ8MDA8VGBMTFRMTGBEMDAwMDAwRDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAENCwsNDg0QDg4QFA4ODhQUDg4ODhQRDAwMDAwREQwMDAwMDBEMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwM/8AAEQgADgCgAwEiAAIRAQMRAf/dAAQACv/EAT8AAAEFAQEBAQEBAAAAAAAAAAMAAQIEBQYHCAkKCwEAAQUBAQEBAQEAAAAAAAAAAQACAwQFBgcICQoLEAABBAEDAgQCBQcGCAUDDDMBAAIRAwQhEjEFQVFhEyJxgTIGFJGhsUIjJBVSwWIzNHKC0UMHJZJT8OHxY3M1FqKygyZEk1RkRcKjdDYX0lXiZfKzhMPTdePzRieUpIW0lcTU5PSltcXV5fVWZnaGlqa2xtbm9jdHV2d3h5ent8fX5/cRAAICAQIEBAMEBQYHBwYFNQEAAhEDITESBEFRYXEiEwUygZEUobFCI8FS0fAzJGLhcoKSQ1MVY3M08SUGFqKygwcmNcLSRJNUoxdkRVU2dGXi8rOEw9N14/NGlKSFtJXE1OT0pbXF1eX1VmZ2hpamtsbW5vYnN0dXZ3eHl6e3x//aAAwDAQACEQMRAD8Abo9GFbmNdntc/FY5osaySfeH7Huaz3vrrdX+kYxWOvdNoxXF9Nbq63N9Wh7mhosrLmMlnpez/DVu+hXfX/hPpoOJi1i0ux81jnAS5ppsgiW7Ro5rt/qbPS9N3rep/NK/1rH6jZh456nmeligkUtfVcSXR9Kz1n2Xf1d/6NZvAfDw9Ufx9T0mQ5TzcMkZzGLS4e3n+WPzx4Pb9XG4DaqH1WvOXjtNQqPpuPuf6rjXY1mv0sT6d/8AwaazA6abfTfmYjh6xqL9kt2bPV+17t383v8A1fZ/pPz1ddht9k9RfwfTmm+Yhv0fztnp+mouwmQN3UnRIiacjnTapBHsB/jf+hskskus5j/qU/8A4Gck9M6ZZWHm7Ha41WWFjdwIdW702Ue1zWvtyf52n/g1Tt6biz+htLh+8NWz4e73LoTgidepv7RNGT46f9NBuwMXefX6q31IE+pRfujWPpqWIzj5eL7b/wC6WDLi4jxmxroMcx/0cEHnH4NjfouDh9xQix7NHAhdEendN79WqH/WLv7kN3Teld+sVD/rF/8A5FTQnnHzQ4vwScmDoZj/AKnl/wC8cIKQWqel9Fn/AJapH/oPf/6TSHS+jf8Al1V/7D5H/pNWIyveJHmoZoDpM/8AU8v/AHjmBOWsdy0H4hag6X0b/wAuqv8A2HyP/SakOl9G/wDLqr/tjI/9Jp4PYFf7+OvUJ1448n/eOOaKzxLfh/tQ3Y7h9Eh34Fbv7L6N/wCXVP8A2xkf+kkx6X0b/wAuqj/1jI/9Jp4nMdJfUMUsnKy7j+7DJH/uXny1zTDgR8UatbX7L6NGvWaY7zRfH/ntQHS+ifmdapn/AML5Ef8AntSRyHrGQ+hYJnEB6ZSl4GGQH/otKtWK1aZ0zpXbrNP/ALD3/wDkUdnTem9usU/+w9/9ydxjx/xZMXuR/rf4k/8AvWmktD9ndO/8t6f/AGHv/uT/ALN6d/5cU/8AsPf/AHJcQ8f8WSOOP9b/ABZ/96//2QA4QklNBCEAAAAAAFUAAAABAQAAAA8AQQBkAG8AYgBlACAAUABoAG8AdABvAHMAaABvAHAAAAATAEEAZABvAGIAZQAgAFAAaABvAHQAbwBzAGgAbwBwACAAQwBTADMAAAABADhCSU0EAQAAAAAAtgAGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAABAMkkkgDG+woAySSSAMb7CgDJJJIAxvsKAAIA3DDDAMn7TQDlVVUAyyftAOVVVQDLJ+0AAQDlVVUA/+9MAOVVVQD/70wA5VVVAP/vTAABAMwwwwD/zeUAzDDDAP/N5QDMMMMA/83lOEJJTQQGAAAAAAAHAAgBAQABAQD/4Q6WaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLwA8P3hwYWNrZXQgYmVnaW49Iu+7vyIgaWQ9Ilc1TTBNcENlaGlIenJlU3pOVGN6a2M5ZCI/PiA8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIiB4OnhtcHRrPSJBZG9iZSBYTVAgQ29yZSA0LjEtYzAzNiA0Ni4yNzY3MjAsIE1vbiBGZWIgMTkgMjAwNyAyMjo0MDowOCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eGFwPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnBob3Rvc2hvcD0iaHR0cDovL25zLmFkb2JlLmNvbS9waG90b3Nob3AvMS4wLyIgeG1sbnM6eGFwTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnRpZmY9Imh0dHA6Ly9ucy5hZG9iZS5jb20vdGlmZi8xLjAvIiB4bWxuczpleGlmPSJodHRwOi8vbnMuYWRvYmUuY29tL2V4aWYvMS4wLyIgeGFwOkNyZWF0ZURhdGU9IjIwMDgtMDktMjRUMTQ6MjA6MTIrMDg6MDAiIHhhcDpNb2RpZnlEYXRlPSIyMDA4LTA5LTI0VDE0OjI3OjEwKzA4OjAwIiB4YXA6TWV0YWRhdGFEYXRlPSIyMDA4LTA5LTI0VDE0OjI3OjEwKzA4OjAwIiB4YXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDUzMgV2luZG93cyIgZGM6Zm9ybWF0PSJpbWFnZS9qcGVnIiBwaG90b3Nob3A6Q29sb3JNb2RlPSIzIiBwaG90b3Nob3A6SGlzdG9yeT0iIiB4YXBNTTpJbnN0YW5jZUlEPSJ1dWlkOkExQzA3NEQxMDE4QUREMTFCODU4RDIzMURENEY4OUM0IiB0aWZmOk9yaWVudGF0aW9uPSIxIiB0aWZmOlhSZXNvbHV0aW9uPSI3MjAwMDAvMTAwMDAiIHRpZmY6WVJlc29sdXRpb249IjcyMDAwMC8xMDAwMCIgdGlmZjpSZXNvbHV0aW9uVW5pdD0iMiIgdGlmZjpOYXRpdmVEaWdlc3Q9IjI1NiwyNTcsMjU4LDI1OSwyNjIsMjc0LDI3NywyODQsNTMwLDUzMSwyODIsMjgzLDI5NiwzMDEsMzE4LDMxOSw1MjksNTMyLDMwNiwyNzAsMjcxLDI3MiwzMDUsMzE1LDMzNDMyOzAzRTE4MjYzREQ5NUFFMzYxOTEzODNFNjc0NTRBODNDIiBleGlmOlBpeGVsWERpbWVuc2lvbj0iOTgxIiBleGlmOlBpeGVsWURpbWVuc2lvbj0iODQiIGV4aWY6Q29sb3JTcGFjZT0iLTEiIGV4aWY6TmF0aXZlRGlnZXN0PSIzNjg2NCw0MDk2MCw0MDk2MSwzNzEyMSwzNzEyMiw0MDk2Miw0MDk2MywzNzUxMCw0MDk2NCwzNjg2NywzNjg2OCwzMzQzNCwzMzQzNywzNDg1MCwzNDg1MiwzNDg1NSwzNDg1NiwzNzM3NywzNzM3OCwzNzM3OSwzNzM4MCwzNzM4MSwzNzM4MiwzNzM4MywzNzM4NCwzNzM4NSwzNzM4NiwzNzM5Niw0MTQ4Myw0MTQ4NCw0MTQ4Niw0MTQ4Nyw0MTQ4OCw0MTQ5Miw0MTQ5Myw0MTQ5NSw0MTcyOCw0MTcyOSw0MTczMCw0MTk4NSw0MTk4Niw0MTk4Nyw0MTk4OCw0MTk4OSw0MTk5MCw0MTk5MSw0MTk5Miw0MTk5Myw0MTk5NCw0MTk5NSw0MTk5Niw0MjAxNiwwLDIsNCw1LDYsNyw4LDksMTAsMTEsMTIsMTMsMTQsMTUsMTYsMTcsMTgsMjAsMjIsMjMsMjQsMjUsMjYsMjcsMjgsMzA7RjcyMDMwQTZFNzg3RTNFMzNBRDE1MzkzQzZBRjZFNDUiLz4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8P3hwYWNrZXQgZW5kPSJ3Ij8+/+4AIUFkb2JlAGRAAAAAAQMAEAMCAwYAAAAAAAAAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQECAgICAgICAgICAgMDAwMDAwMDAwMBAQEBAQEBAQEBAQICAQICAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA//CABEIAFQD1QMBEQACEQEDEQH/xAEwAAABBQADAQEAAAAAAAAAAAADAAECBAUGCAkHCgEBAAMAAwEBAQEAAAAAAAAAAAECAwQFBgcJCAoQAAADBgQEBgICAwADAQAAAAABAhEDBAUGBxAxCAkhMhgZIEESFhcoMEIiFBMzFUBgKSMRAAADBQQEBgkMCgsLCgcAAAECAwARBAUGITEHCEFhgRIQUXGxwRORoSIUF6fXSAnw0TJCchWVFjaW1hggMOHxUoIjNTdHYpLC0jNDJCZmpoeyU3ODk9MlZYZnx0CiY6NERVVWtlezdYV2J7c4EgABAgMDBAwICggFAgQHAAABAgMAEQQhEgUQMUEGIPBRYXGBkSKSE5TVoTJCM9MUNDYwsWLSI5PUFTUHQMHR8VJygqLhskNjJMJTYMMlF3Ojs0RUFib/2gAMAwEBAhEDEQAAAOov5/8A+r9iANCikLRKnHBpItNYIHBrUHaa292rkinrlxrtus4h2/S8P7noeH910HC++6HhPfdBwz0XQcI9D5/gvpvO4nZ9cW20p0LfSU3K5JL6TnQjaxOj30PpdTsds87H12LfUjac3NOj6XPPKlNy21PPKlNyW1mudrO+5rarS1hzJLEtqS2hr6zaltqRyCTsZoDsODxjuPnfHu18Pgdx4jj3deFrczqyW3Nvclrk1vLGqEIQhCEBC3mXI3Lvc88m2tqTlp4cfUrnq55ahpZZ6poMtRjoGhFdJTSrbRil+0aRoTW7NbVdECtk00NXV4vEIiKUOI7Dfgj+t8SJGK/TeD8r9tu6/KnkPz74V138f3Xzb6L/AED5s9z+nPz3n/RIqivIN9IRx2BgL0DrYbMN860QG8wjMdqVrW472vW/PvVeX+e+o8v899b5f596/wAx899d5vivpOjedStjW0nbQ2minUzabc3J0K2I2lFz21lrY9uW6xLbWXLe8knWS51pOXY5GqmbDmPFiW0LfU1tSW1JO5GxGxovPlbWY5iEIyuf5rjndeB496DwHHe/+fZPd+TfKiEIQhCEBC3mXI3Lvc08m5NtNlp4cfWrnpZ5a0tPHPTNBlqMdE0oroqaCdCtdKY0i7Nb81uV0chbIc0VdTxdhyA4hzsN+CP63sDOV8LxH6OPqn4F+oXuPlfwbwvd+e/xD2/nT0noOV/TO580eJ+4vHOX6StaeO13va8fj+OlaI1ORlxtqeco6ZY8abe2NGtQ2zu7V4vnpp8njUIji867+3FyKzkdtw+L+48d829x5b537byvzz3flePei6h2xW852NyNCtiNpRc86y1ue/KebEa2LcuU2JbWS515uVY5GjTaw5jxYltCW1PrsRoSdyNiNjReXK0K3LG6EIQhFPl9Pxr0Pzni/ovmfF/SfM6HM6NCEIQELeZcjcu9zTyLc21Wenhx9WuennlrS0sc9E02Wox0DSit9TTToUro2jSL81tzXQroxG2QZoWus4vEciOOMdh/wR/W+JE9Sn5k1fa/n5+27+yf55nD80n5v/0h1w+ed/0l35dz6z/d/nbzf0MyOT4DvD9e/PDzc+Y/2/7efWvzyvdh5HyT+YfoD66fUfz0+J9L9X+JdN9Q9APdfyH4T/Kf0k9kfof8K3teH4A/Iv0y/Sr9r/J7x1+b/wB08v7j5/2G9b/OHlT8x/Qj0N9v/Iny/qPo/Rrx398l5Fa00DenFO84Hzn3vj/nP0Hyfz76D5fjnrenLfckayXPbWWlz35TzJG1i3LlNi21k0MtNyj8jV5sdy3ixbazvofXYkaEnWbcrc0XlytJtDRyHWQhCEIQhkYnc+L4n6b5ZxL0/wAoxO48YhAQt5lyNy73NPItzbVZ6WHH1q56meWrLSxz0TTZajHQNCK6KmmnQyroaRpl+a3JreroiFshzQldZRdhCESInYf8Ef1vYGevuH41dA/iXyH1T934/wBev6R+a9FviHtfLz+f/oPwfTk9ivvf3vyz7/8AWjhHa/KfaP7r+THj183/ALx/QB9E/g71D9J/Of4sfi37GfoI+tfl7Q6v6H5VeX/pr1F95/E/gf8ALP0s96vqP5tc40678vnxj9af16/bvxz/ADvfLv0X9HPXfzZ2C9F8P/LN8b/Wr73z/Cxa/L/Of14Halaa19a0JBnOvphUvPH+9675x9H8twT6P5ThP0TzYO1yna5r8p0knaxPMlaSW1kudaTl2ORrKbGct4sW2pdtC22JGhJ1k1M5JovLlaTaFbyi6EIQhCEIQhGX2XmeHeq+R8N9Z8gwe28dPa09di73NbkXFtNXVz4unlnr0z0aV1KU1DQcbUV0IaKuhFdM0K00LNEvTnoTFqLvFlNWRJKiyEIQ5Ga9h/wQ/XFAj142/JXrh/M387djeRwewXvOk/Vx+pn8ufJuh7H8MX8N/wBKfdp/tDy35f6f4jyXXK9ueV0+l9l4PT5/mfgfnfs5+x8/Uz3ocXseSdl5/wCfdd7XV7DxOfe2LwPdY3L6S7l2PJux+f2+R1Fzj9pnTy7m9NzX2NbWK80r6Vz7AMwa8endV0jO2kU45nKjiPufOcH+n+Z4l9P89S73hknY88uVpJOspudaTl2eRo02sOY8WLbUl9DabEaEnabYrcsaT5V5tCtyReIhCEIQhCEIQhGT2vlOH+v+ScL9l8godx541+Rbs1IaefE1Ms9WueplGnXPSRpOLqGhDRV0aU0ZnRimhZpTF6aaMWsDxZDiITm60oshiRGa9h/wQ/XFgZ9+67+fO5Ps/wAUed/Je49KfonkvVr+n/l+hvXxE+W/QPz6fE/3p+Mdn9l1+7+Hd2fq/wDBGky8/vJf0x2G5/h+Ydp4T4D1v0f7J2fzvkk8fh2HcQjTpV577n3q9R/O/FK8mCMqvL0des+LdX9I7e+k/n7Jx7Pr50n1vqF5D+7yb51JrW2rRAs6uvHq3VdIzdrQjGhyK1p4+Zydad8eMe26ri31LzWH9Q6QfcYlnV1zrScuxyNVM2HMeLFtqS+htNiNCTtNoVySRcnK0m0K3NF2aQZoQhCEIQhCEInOklnZ8d9F4Hh3tfkfGvZ/JLu/C1MONqRTWplqY10oppGk4+mpow0JjTrTQiuijQlozF9W+ixMTi6izjCmqFFnGHGmOw/4IfrhAYir3w4n8Ndwus/M75r8j7Ltz77oO232rpfz4eJ/W3pFz/7JijKi3Gaam5PnsTHvdac8mbn0jLVecw2itosWyzKwrwDSK9sqFonpXPtWvpWUxv8AL4wNIrTWrtSiBZ1dePVuraRm7WHGOfyK1r8fN5G1O/Hzrzn7RmcmMT6B02Z9W8+D6L1Br2nPKPrqrzYcx4sS2hb6m12JGhJ3I2I2JFycrSbQrc0XlOooyYQhCEIQhCEInOsx0Mskg5HB4z7b43he6+EWOf5XUZ6uNNKtNE0mWpGOjLRiNA0q56KuhLRRfml+YPNJV1nF0RnN1lFozm6yVSyi3Yj8Ef1vaIjKEQyu/wATyfZXD+das6dY+b/ReBzPSDiGmgbzX2vGMHkCA9QbVBasL5VQNgozac6llfSQaZCmtWaivWvetW0CvWrpWtNa+tc+QJzracendX1rnayNlnciKt8aG+tO/Gzr2zORGbrNK8Zu+dLtOPV+rdHZ+sdPZ7qqvaw5jxYltC31LpsVoSdyNSNyrz5Ok2ho5BZvMZYMUQhCEIQhCEIItObMskoQhprk+0+L8V+lfzra5fR8rphyvPDktMdOM7KCJKgkQUtI0Ua6umrbQ6YpcZV0umKskuiRbpH6Qvyz+dTEhDoeTxDXTiEPJxSjY6Hg0whWhSU1YiTVaYimFoe1WRGYlMDk0mQ1g5hTEExVa1Iya0DuSgbyrVDZBUUzC0C0kcxC0CmAzMb53ffdfPsamaSgSbzmSTYi0puRaU2IsRM72mmcXml0OhEiZIcYEUiqDESJk1kmQ5IciBAb8HH9b8svTjq1ppVpdipSY5IkFRbRaLcQQmOSJEiY5ImOWory35P8DMIISChCYYmTDEyYQkTJhRwwUcMIKOGJBAwgwgwgwUcMFJhAo4YQUcMTJhhBhBhBgpMISCEghMkGCEwhImTCjhggQRUMQyiuMEJBRwhIKSBlUqESQQkFHCEgpMIFJBRBSQUkFChCYUISP//aAAgBAgABBQDxKeu3ZoeOno4kPSGFgkmg/UDSRkSSI0PHiCh57O3CnNcVk4EPdOu3Bw156xciGvrNEiFvrKlHDXmot+IW41ERYhpxJ40Zk0x/HDyHEEDCcksPwcB6TxI2BJmZlxLj4DIETMWpCSIywaYaEfyMjaGHiQaQcRkXChzU00dhzV6Q4qeUPQ4joOJwJgRyhauH4v0Ifr5FgeZh5kov4eLzMv5/vh5FlgWB/kjI5EMo6OmCYadT2mZKHFfyKKfSl+6maYKPcxyQQRzeB3zBJtLA2sINaGkQg5zN4MQdyK4gTgr1VU4EFfWFMQN36KiygaspmZAj9REZEDBcjsmF+BJsNHLiQPIsjxSTCwPB2E8vhPwuJpMIYQ9XTJ0IWtYJRQ08lMWFcfx/oQ/XywPMw8yP/X4vMx++HkWWBYH+OPjCgYW3VJIgI2c0JP6hXG6YIqalMLCwUvOHpqYSN9GS6InsBAxjqPhSCTIjVFuEgnrsyOMh0q/vQoS+dECjYUw7inDTi3KT/tuQT508BxLhBoiHCgpSUkmMhUmh86WSohyg0xLoIfu1JKJhWwE9jIFUBdCt4AS+98xQJfeOk4kS2qqamhYmTPCQQfDEs/1LiR4ETTxPBBsNjC8J/hho+NhBC1jNHIhazlz0QsxgY38H6EP1LLA8zDzL9PF5mGtXh5FlgWB/jkkM4mNW0VXsNLa0JRKSo/4zmsFG+mFSrUdK3Mf07MYh04l9SkJNCQEwnsHSEph0VhKJVJSp2npH/wARdOyNaIORS864c0/JVHVUhkpyK30mgHsCuSU6cPW0pl8LM5fSkicOY6lJC9RTUrdTKd+2ZMSq7k0DKk0fIZW+lLuTyd2iYSWDXVPt6BOLqaROXEsdKa7TwJrB6WiXVJPpScuvBVkGUsvXJ34ltbUrOTQZMDcEk00FwxLP9SJhYEbDI2lgeCUGRn4jBA/xwtQTaDEJWwhJ9Ko3w/oQ/UssDzMPMv08XmYRzYeRZYFgf45a9KHl8fUBMojUbWFEIlGq2jZi6nmna7TlM4piqpLHQNPmZuUrQ6IUsmGXUK4tP9+s3BR8qpKL/wAsiKIdMksX/arp0n1lViUqp+gn7lErJ8ojrZ4r/ppT/ndqQpDqj0k8qNKnCUXIL1QdHoQmn0/5WxE1dOKsqxUa+qCqFPf+U7IiCcjBG3BvBGXAyldQzyUKll46khRKrv0zFCXzeVTdBF6TSbCxLgP1I2keKSYWB4JNoPxkD/NBzeZQAg61WIKeSuPHmlLUEOLPLA0l6jC/Sw1GaPA3iRtCjYXn++HkWR4NDfweWNMw0PF1HOqSmskmkFT6jEnpdSohHJUlMSKrZZUFAvqWn0ydIRUpCCezl3MnKqrcHMp5NZwiDrefy+HeV5VxBExmZTH3PUBCJqGdx0M5XOJfCOamnjt0tUVMVOPdE6VD1BPIN05eLhjdzGOdu3j54+dwk2n0PCP6jqOKdxkDPnxOptNncZFxsyjohOZZGEkwExoRkHeYaQdvVuVym5NWyoSm8sveFK6nkE6S0m4GXBJMI/EeDsNaXiIH/wCDBzqawZwNcmk5fOJZMgRmwMwa1ZhRsT+ngLMLPh+374Fk3xEQPw+WMeiKdHUhSyrpC4mcllT+SVhbAlpvZbB4iGuZL5ou4s7gIOYSxw/dOSFITFxKp9/2qZSiAcSCNi6nTTjqSy+aU48Q7mEgfO/+lTblKnlLunD6Op6IjVRNLuzeP6chYKRzCDfyt0+ptSf79NRBnMKXeiIjKcdKJ9Ilvqli5K6hYeYyZ5LHsVTTl9U0RJ30ldm1Ccj8CMg7zxImmXAFwElr2qZOUou/APilc8lE5QZgjYDxI2lgeDvNjC8RA/8AwS4A+JtNsvqmcQAl1dQD8OYpxFOfJvEwrlLl8Rk0fsWeHkRBgPwN/HAv5pTsdNaakFZpmtrrhwb6kaBrMjTUUlo5w+eTSezAEFOkPR/z4YQ65jApKEQYODhlBMBCmP6MMCgYck/8+ECYKGBwjggqGcvB/ShzH9SGBQcOY/owwRBuEqTDuEmUK5Mf03ARCQ6TSwgWXgRkHeeKOYM4J5GMJwt46OT3JqSWFJbm0/Mg6euoh2GcUcuB4O8z8ZA8G/nLww0XEwbyW13FOjl84ls1BnxPiGMLy8X7fth5FgeJEDwIgYIgeLcWCMl8BHk5lsRDB5KlxIg5fBwCc8CCOZphp4NIOx6iBGRYFmfEFh6VBHANLBhhPAEfqMZmWRhuDAjIOywYYTzOy4hvBPKHeQSf8ZZOprJnkkus9ISqeymdpTwLA8EGRAzT4WHgRg8yIzHpUPSr/wAVKlIVKKyioQoCYQEzdG7SDdpYaSIGQZ4GE1hNYGYNDfA3FoaG4GtJL+FKVHwpSoKylKkPhSlR8J0qPhSlB8KUoPhelh8L0sPhelh8L0sPhelgmzNLpHw3TA+G6YBWapgh8NUwPhqmB8O00Ph6mR8P00Ph+mh8P00Ph+mh8QU0PiCmgm0dNpL4lpwFaWmyHxNTg+J6cHxRTo+KadHxVT4+LKeHxZTw+LafHxdT4+LqfBWwkJD4xkI+M5EQ+N5GPjeRj46kY+O5IPj6TD4+kwKgJOQKgpQQKhpQQKh5SQ9lSoeypWPZkrDqkJe5eS+czOBc+6I8e6I8e548e6I8FVMwIe6o8e6o8e7JiPdkxHu2ZD3ZMR7smI94TMe8JmPd8yHu+ZD3fMh7vmQ93zIe75kPd8yHu+ZD3fMh7vmQ93zIe75kPd8yHu+ZD3fMh7vmQ93zIe75kPd8yHu+ZD3fMh7vmQ93zIe75kPd8zENXM6hHyb01WlHzVVI+aKoMfM1UD5lqcfMlTj5kqcfMlTj5kqcfMlTj5kqcfMlTj5kqcfMlTj5kqcfMlTj5kqcfMlTj5kqcfMlTj5kqcfMlTj5kqcfMlTj5kqcfMVTGr/3v//aAAgBAwABBQDFhDgCJRh/FQsKpxEQ8WDaWJhObOHqMepQW7S8D6SyWKTE0BQ8WIm0Nunxv7E0G8KJ0+SRYiNPkyQIqx9bw4ibY17CCKkE9gCJRJNLDBZECDviRZpCCLAuIImmXAOhng7NR4EEtPAi4pzIJSwk5kCMzBLYCIwgmFgkZAs0ESiCeV1ggzITGm6dnyJjY2hY0R+nt8kTCy9dwJTCm59JwhoYCMEPL8XkWaOZAVkrBeasj5cEf7MP2LnXkeeH7q58F5h3l+JouteGjLPyO4+si61fxZOp1PHsLLJ1KV0Dqzu7bmItpdSj7sSEKzTn4CL+IeYFgvBqiTEyqWRxRlu6JihFWapKIEXZJ4kRVpqwhRFUpU0uSf8A+aiCOA8k5J5g6BZlk6wLJGKc8E5lknLBPLgkFkHeBEwnWBcAhRpV4JlR1LTcTKyNJRYmVip44EyoGsJQDSafx+RZo5kg8lYLzVkfLgj/AGYfsXOsHnh+6ufBeYRl+K4dbym29H3XuPUlypzCT6AgEubnO4UQtdvX4czmDjU0DX0/s5WVHVZKK6phWaSNpOnpkbtZBMO+UP6z8E7eEX9Z+HsO+SEOHqwUJEBTtaQbl4sKcPkhhqL+u+D1Cg7cvVA4Z+ZqdqQaXL4RkrhotEbb2kY0ou0UtUI211RQ4jKbn8tBGxbQgySksyJhOsCyQDwTzFkR+oizLJORF6jCeXBILIO8CNpOsMwnNCmH45lIJJOSmtlqTjRNrIVJCCa01P5Ifj8izRzJHkrBeasj5cEf7MP2LnWDzw/dXPgvMIy/FuAVxFqiKop5cZTZ8DEJKSJMPLiSb+VJiobQVWsVESpQurO62pa3lUalrgTs7A15cS6L2/F97xyy7kPf29ULH1JcyrJdo2nl9LxQyNN94bvxd5db9x62pGp4K8OpF3U2kes68ra3tc6nr0REwpDU1euDi793BmlvLYnqYu49hNId36zuMNTt5bnyG48fc+70wjqKuhVLjTUd96pc0hp9vdMKguIomKCuAYI2TyqYCOt1Tz4ou2ce7EZTM9l4yN0WBZIB4EbDLIi9JFmWRBJ+kwnlImmEgssw7wTyuzYYLJOZBJtL8JkSim9u6PnIm9iSE5t9V0j8PkWaOYsvJWC81ZHy4I/2YfsXOsKzw/dXPgvMIy/FrIevH2o9DkVDb6TT5UXaqcw6oC4lKLEDHy2Pcm+Gi1+t3fpWd84iqIe2ELSr1Nu9McTB0rcvVpRy5XfCIpmZQ728sgc03ounMacC80qRMU5vrrclM0m1zDkcK8gtG0BAot68jDpuPdx8PNJjqniv61koxzVUVNdDkQj/AKeriOfIvtNCkaXEmoCZzzTTpS9rSXT3pwcy6JuiZtBBeWD3lHqEZLJfMDjKBk70RdDzWHOIgY2CNLBngRNMsgWZBOWCeVJ+kwkFkQd4J5XfOC4As0hGX5ZzRtM1AJ3YuHWJ3QNVyDBgLNGZcArlMm4LzWxh8uBKMjw/ZjFrzPEzYtfBSTaQNJGPQQImeNv8sdesgiJJctEc7eO30eRCLmaSQfESuazCSxUlnrqdSzQRT76Y1arOv4C2kdS83h7STVzR9rLe26fVLpksbVs4lulLTNMnSaFoqJo34Ns+SZNZy2VNz2LgaCqKqIyxtppjNP69P0m4mEVZi0MHUFnrYVlNJpKICduYqiKWjpnDSOXQcbPqDs/HVXJbKWMpeaUXVFm5TERdpbbR9H2/pK21GS3BeREzB7ynh+ysjCiStMbTkmiTiqJfpEVLY+DCcyIzwTmQTlgnlwTi7wTwJ2TTwLNIRl/4E2oakp6meWJIinVG1NTZoY0uIPDyXmrlPlBGR+D9mtWvPDzVzmkjBEzwqUZGk2l4P3x1K2lO7ltZO8fwUTFSqOepj5RUpD2XUxG8pmIhCpKBj4iAsJa13aS26hqPoiYXFth8TajoqNr6b3qo6j7Bxt7Iu9FZURfX/PMqDvfJo86Cv1OX8BB6k5lUEio6+1P0S5pnUhNHLiW36ndaXcpWfQV1JtLNQkOqIovUTTxOqE1JSpNO0lf6Zwj2WXrhJNp6kV14ypZzQN3ZbciV05qInsksHI7tya7qiIlEF5YPeU8P2MmlgvnQRKS02RMklcYIqk3yDfQcVBhILJOKeXBOHm7wIIJRmCyLNIRkFl+dBEZEkiCuBTy2dIz4T2y89gRHwEdLH7f4hYXwI+XBKSNWDOJF/NRtPDzMmrUoyP1mEm0sTSRgiZ4WccWGNSukyHuM+jnlTUXMHFUU8+Kaz2TskVI1RcGcabNK8Baoj4mowhZpM374indM0jUsYp+8NJPnqQUS+If53wOLiEg4p+ZlEvjBxT4x/neux/bfA4p8P870LiHxAoh81cQ9Mf2XwTEPg8fvjTgrF7yng1hmZEWC+d1yguIMiIvSSkxUhgYgRFPxrgGlSDIm4JywSCy83eBNY6wLIs0hGQ9ZfnRkpRkZF6wREWExlctm8PUNk5ZFFP6OqSmVrzVxP9cEf7MP281Ew8TNi15hGWClGRpNpBSjI0m0lKMjSbSw9RAzIh6iwquhKOriFm2iqx0yfybRdYyUvaZo+lKLgm4KzTmZNLEx6khRGrBOCuOJB6E8QrmaCzPBpBeWD3lPDM1MMsFpV6nfICNhKJpER+kFyREO4iEv5CkPnD5wZcE4JBZeaWli6PAgWZGwI5WGGGPQf5iUZAi9YImeFaEPUVfZuVzd5OqbmlPRSnSSBuyCiMj4kbMWA0kY9BD0kDSRD0JMGlJj0JBERYcAaSMEREOANJGCIiBpIwRMBvEE87k18x3Jr5juS3yHcmvmO5JfIdyS+Q7kd8h3JL5juR3xHcjviO5FfEHuRXxMdyK+I7kd8Qe5FfEx3Ib4juQ3xHchviO4/fAdx++A7jt7x3Hb3juO3vB7jV7jHcZvcO41e4dxq9w7i97R3Fr2juK3sHcUvYO4pexvcUvYO4nesdxK9YPcRvUY7iF6h3Eb1juH3qHcMvQO4Xegdwu9A7hd6B3CL0DuE3nHcGvMO4NeYdwW8w7gt5h3BLyjuC3mHcDvKC3A7ykO4FeUdwO8oVr+vGsla67sqV11XbHXfd0dd93iHXnd8ded3h163gHXxeEdfd4gWv68iR1/3kHcCvKO4HeQdwW8w7g15gW4Tech3C70DuFXoHcJvOO4Tecdwm847hN5x3CbzjuE3nHcJvOO4Tecdwm847hN5x3CbzjuE3nHcJvOO4TecFuFXoIdwy9A7hl6B3DL0DuGXoHcMvQO4Zegdwy9AmOvm7M3hHup+u3jzqZrsHqVrox1JVyY6kK4HUhXA6kK4HUhXA6kK4HUhXA6j64MHqOrcx1HVuOo2tx1G1uOoytx1GVuOoytx1GVuOoytx1GVuOoytx1GVuOoytx1GVuD1FVqa//AHv/2gAIAQEAAQUAHmvMZg+AS7evQ+dLcBXEGw8DDzIiaCM0hS3jHzhw/VHUbR0eUbZW0cecfpnslGCO0h2kihMNGFNGI/RnP3RTDSfdeDKPsLd+WiPpSqZQMgnAsEZFmkJBenBPKCwYEmZ4JzCcwnMETCIFkQTkXpHHEjDQnMiCS4nm7wSZmcVKpVMiireU7EHEWxUQiaCqKGETKplBGRmOGCCaZfieZ+ZZpCRD8zvJJtEL/rhj4uudHFboOzDoOg6JhOeRxwDviTrJ1m6NinWR8rDNLwjaojakyIiJgSRtCuB+fn5g+BKyYLV2jri8VSWp2ybXW0ktXVtaW2TqP1L2znapppx0vailXKtfWdpqlBheRpwUTRwMLBhbAbQ9wNvqP+QUpQmVOU9NimNkbUzMTXS9b1+Uz0pxaRNNOly5cJjb6uJMSkqdqSCJgRgnlCQWZZO8E5mEZhOeBAsiCOXwECySCzTg7wRmkzI/BFSOURoiqBkr4Rdvo9AiKdnUEbDI/wALzPzLNISIbmd5u8oX/VDZu/8AY65kZOw5ydB3k5/1ug55HIdB3m6yVylkvNXFLDMHmQLJeRZfr5gzMgYoij5vXtVaXKAo62F6ar0/V5Xpz7aumdXornbfkkvTebSXeCg0WruXKtaFLVVTU1o6ojD967cpfVXIId66mUC/RHVzSsvepuFR7xTyfSdMO8uPRjs4OtaWmb2Z1jTknP5Gol+uGncojEx1VU7AnDVbTkaH0bCOXT6taXQcFNZdMSm1RSWWJeXIot2tzOJZFQz6sKcdB9FUNUwmll7bzITXTjKTE0sNW0CUzoyrJKC4AiMwlgLMsneCczCOYJzLIjaCBZEEcoPPAgWRGEhODvBHEEEmz8EVLoGNEXREofiMoWZuSi5XMYE/E8z8yzSCENm7CRC/6ofJ3zuuZOToOg6DvJzk6ydcrkOg553WQLJWf6O8izTmWS+ILMeYPiRmNtKkaelU2239V/xjr2cvnEQ5eqY5rK8b3+3Ul1Xrw7ty3/r1DuAyCTTqdGKQg7KVPfa2WkC19C1HqHpKxOnildDOiPQ9dzSQ90N6KI6iLc2rtrWe8tZrRdpGr+a6+dMuhmjdGOzhZGz+oSlp9pT2UnGmfc9p2x+nTUZZ3bP0azuVXV229E8NB6QaLl+pK78y2mNK7y4G5TpotHpUqzQrp/0yXD050tb7RTSdOX1tRbGD3JYjSNG1VqL1caQriW2sHLnpxMA9ImqaYUsyKZ01Ts3EzsvR0UczsjOIYTOiaqlIZ6TQXBgTmYRzBOZZETCIeRBOQMERmYIFkRcUhOZ5oPiHYIECNpfhMmiMpyTRojKBEbTs4gPA8z8yzSCyhs3WbvOFP/8AOGIw753GaCP0ug6DoOcnOTjJ1yuQ6DnmRgWSwXMnlLNJMIs/2/Y8vIMML4DTBDEjbvl9LesaTNxDULprlNG7udlKlhLg7besSERdWnLk2qn0wqgan0u4/RCYp1No5hfKdVnOZxrU1NTq4N9NHOyzqTh5noXpzVBbWopdt8V5PrtbvVlKJhK4lu77S0hnGhHaXqihrbaZkXqr+CrzdzjrhRl1a2tPMdRVPU5berbTU7tv0nFVHqMol9pRpKze7RSMfLInZ/oopzoKsvEajIyoasvtTdMa5d46jakrndW1bUvEONMqHaXLt5xM2EHmS+Z4HoUwhNJFJpqmZWtp+IOYWxn8IIuWzCXLCMwkFkCBG0EE4pNhggWRBITmeaOYIzLMgnL8sbJZXMRHUE7MR1PTeXhfM3iWacyEPwN2ECGZ6YcOed0f8neTvgTtpB0HXK6b6HWTrglzk6yd/wAQg2AjUYNTAxpmgiIiZg0jHFhYMHngZtG3k8e3NszbL/nXBkcgts8eHRlqHqo1whjq9dirXahKE1kWNqjSjqD1+pTbe3Jimagv5I7qUHNroyV3X99rnX4gKd1w62aCpOdbhu5ZSE0VeKqiuo/vxe+Iezi+Vz5lRjyMquX2ulGo++FMUvL4FxW8OVs77a160sre+5Fg6WkdQUrTLmRx1LU/SUtmlJS2nXla36qC0Nx9Q+rS99I3nprVJVsjPVDeaR3juDdS+95a6IyUl9mQeG0LIKyecErC+VTGG0PnaHhTCjpBHCOoKJcqjJXMIA08TYeBAiBBOB54FgkJCQYQXEJMFmQTkRNL88XT8mmJR9vmiOkk0lhpIEIfmdMY7ELyQ4dc7phG7MyCD4O2h0HX+tzyOMnTWOsnYdlxQRBjCNo8jwNRAjMyDOGRmP2BmwKMaZ7zzCxd29Y1n57bKvLL6wqAjXNur+aTIs3OuHS1EOZdqcp+q3+qmR0ldjXFf27UxvXdIxp2uvF6ddUMFerbvo+R6XJRpgvTqY1ISzTP26qEuVpI/v0JerTTVMlTeTS7QNLXQLQXTlo7n3K0e3F1QQ1xNDNHP5BG6O6IsRp+mD2o9FNFTXQrM46mbuaJbsJfXu0C1wdxrqaPqdnMZE6X53czXxN9OfxPEXT0u1Lpequ4ugS3N0NTVQaV6j0WwKlKgHvN5KBh4HzWZh5yrZg8yUfB9zLYZTGSyuJXE0y9QHsM/hjIEXAgjlB4lgnNITggj9QQCzIJydhRfnRgwjKYUtJo84+iI5wDhn8K+dkHeUKZ+mHaHR8XOaCDsw7DrgHXK5Z6XLCJ1yueJOjaHebs2EpRhORZ8GmPS0cEk0xxY0NIybgsPAbBpM1nzixomGj+yF53Mw0g6naZf2Z093sdHWN4LT6daN1Zaxahv/FmZGDEylMvmSFUJSqikcDW9FSyQ2ypKn3L6j6dfrfW9pF4tNE00lLy3NGKenQFJmXsWlXKYegKQhH8dSFPTEk27o92a6IppRrommPSih6Xdk/t7SLwQVGUzLREW9o+IeFb+k3BQ9HU5BvfSSA9z81F/FTA8D3iFZqyXxwW0gvJ9moRJH6lMbEMUp9Aw6g9hlu1ERkaOXwECyTmkJzDvBGZZkE5OwwGTD/KnIiGWL+Gh4pEXSkOo38vi4I4ZLEQ5GHJ/wAnR8XfEOz4u2GHYdM9Dkv4ueV1yuuVzk6PigGQSbSMNBmxRA2NM+LTYZmRkTQ1XpBkYURqBkzCnauqekYyntcepKmXNTa4NSdTuZ/UtRVVGGR4GHmTSLE+IW0GrgtpmHuYyMwoya84YPcjzMPcltas+D1rTNgW0KCzaHhBRhZ/xMHk8Pg8yfZrJgiDaozYH/Ba2B9/sUXD/OSVO1oe+AgWScy4BPMHeDsuJZkbAnJAaQMml+VBcMvEZEZREmhnqIhM0lr2FmD71QcUhYg0wbwnEHL1E7gYEFCQqQTpykkmlJJfGkijHpB3HRDHMbEGIeIfKNL18YJb4f5HzPXEj/JED1v2+t+z/JEA1vzCXj8h61mZPVESHUWuD6XdrUdLW1uD0tbW5kWlXa2MdKm1uOlDa3UOk7a4UfSRtcN6RdrkdIW1ys+j7a4HR1tdDo22ull0Y7XQLRdtcmXRbtdAtFO10YPRJtcqHRBtcrLod2uVH0L7XIPQjtcLHQbtcDoJ2uTHQRtcgtAO1wYPb82tzHb32t1Dt57Wyx27NrYHt0bW5g9uPa1WO23taA9tra0MHtp7Wah2ztrIwe2RtZGO2BtYLB7Xe1goK2tNrBQ7Vm1eou1PtWqB7T21YYLaa2rDJW0jtVKB7RW1UYVtB7U6jLZ72plBezptRrM9mraiUR7MG0+9Pss7TwVsn7TqgeyTtNqB7Hm0wo+x1tMNRsh7TyC7Iu08OyLtQgtkPaiMi2RNqQz7IW1KC2RNqcwWyJtUEC2RNqkzLZE2qyBbIm1YYLZF2riHZC2sAWyFtYqHZB2sgWyFtZsLZC2tDItkHa1Mz2PdrUwWx1tamOxxtbDsa7W4LYz2uDItjHa5Mz2MtrkiXscbWyCebI21i7CtlHasSZ7K21WQ7K+1UC2VNqswWyrtWEOyntXGOyltXAtlDavMuyftXgtkzawMuyXtYhWydtYJB7Km1akL2V9qh4k9j3aeSt1sl7U7kONl7awdB3s4bXDoI2edr0iLZ92vyBbPe2AZdnrbABbPG2CZFs7bYRgtnfbESEbPu2SgO9ojbNdhO0dtpEO0ntqAtpDbVMu0ftqjtGbawLaL21zHaJ22B2hdtkFtB7bRgtoLbbMdn/bcBbPu26YTtC7caIT/AOeg/wDnuP8A57hPb5Z/8+gnt+MR2/R/8/x9AgjoCH0EH0HCOggfQkJ6Ch9DAjoNH0PCehBn0RH0UCehZn0WH0ZH0ZCehtn0cH0eCeh4fSEfSQfSQfSYfSYfSkfSkfSsJ6LWfS4fTEJ6MmJ6NG/TYJ6OGfTgfTsfTsJ6PWfT4fUAJ6QR9Qx9RR9RR9SB9SB9Sx9Sx9TQnpPYnpRb9UwnpTb9VQnpWb9WAjpYCulphdLrEdLw+sAT0wj6xBHTGPrKE9MzPrOPrUE9NjE9NrXnTb6XvTR6X/S6HnSp6j6UG/VAJ6UmfVMJ6VGfVUI6V2fVgF0ssLpbC+lkL6VWfVAK6T2/VAJ6UmfVMJ6VGfVUI6V2fVgF0ssR0tt+roLpfYXS+PrEPrEC6ZGF0yD6zhHTQ0umofW0I6bmJ6b2/XII6dGfXUf/2gAIAQICBj8A2HFGaAHFoSTukD4zEm3Aoz0GZ45GN/gizb+uNOSV2cc2USImNtu2yAZCzbt/VEkOKTwEgfHnhss41WJUjNJ5yzg51kK6vWaskd1d7/MDLihP/rQcAEpLaaUDvnmAk6c+eEh5FG9LPNspJ3uYsADigCr1ep1W2lDi02cBSu3jlvQBV6vVKDPyXELs3bQi3TLNvwlLy6phRPlNTHDzCuyB1esdOlRE5LvNnj6wJlxwPVMUpnZy8R1tWfNmJz6IvSs270C3Pt3IFgyT0bG9ZKLAOTYZzGfwDLpiUzE7YzZM2TPsNPgiY/VsTOAq6IznYWzlwx/x6haBvEgcmaPpFIcHyky/y3YlUUPRV+oj9cXVOKbn/Ek/GCocsfQVSFncBBPJo443uCLY8WWRSduj4PjyDYHhyDhhPwAMcWwP6Cyw20p6udMm2kDnr0f0o3VcWeyPXdb8VXTNKE00tN5yW4tZIPKofywpOH6kUr13yqhxa1HhuXPjMFNf+X9GlE/GpnnmV8IKusBPJCV6q4s485nNHWSDpG4y8CQveE1K+RDw6st1Tdjja7HEK0TG4dBExv2ZRs57DNFuaBJMpbn7d3f5IBocWqWrs/FdWkW57AoC3elxwLmsDqxuOBLg/vST4YSmso6OoFk5pWgnd8VUpnduyG5CRiOr7iTutOhXDYsIPBbA66qepyZecaMrd9F8cMSosepVqOjrEg25uaog28ETTaN0ZuWOHItOmJ/AzgbCUT2IGxMJ+D+grHEjcnMchmPBEnktujfF0/2yHggJqqVxB3RJQ/6T4DADVc3e3FG6f7pT4oKwbP3fB8eQbA8OQcMcXwPFsD+gLfKL6ppSlOla1GSEjhNvACdEMuYiUu6z1DJefUZTYaABQ22PJUqYAkJoSb3jmYKaVtDTNvOdN0dEAr8HHCl1uuSWl7jdOVeFTqPihaqPXULcBzLp5DpJdMuSE9c6042DYtB3M1hkR4SNBhGLUCgNZaRE0uf99tI5zbm6qVoVnMp+MBDNY2m6hYtTpSoGRB/lNh3/AAxMkCJLUBPfHh2yi9e5u7t22QUlzniPPAQFlYAix0GA2Fgq27sokVWxaoi3SNoiaXARt2/HElOpnwiJJdTPhEBSjJMXS6mZH64mlQzRJTgnAIMxF4KFy3w/q3otcTt3vj0wPu7GH2SnNccUnPvJInonn34A++A+iWZ5tK/DJKv7tAgJxTA2Vj+JpxSD0VBY8MITVJqaRz5SL6eNTZUf7YQMPxylcUfJ6wJV0VXVeCAdHx7f15BvxLZS+BlsiN2APh/+LVuIG4FEDjGY8YgB8Nup3xdPKmQ8BgCpZcaVu+MnlEj/AGwPVattZOgG3o5xyfAceQbA5BwxxfA8WwP6BTeuJvYbhlMurdGgkA3Qd+SSBvr44YxLF3h6vUlaHFmUkF0i6reSFJSCdCLZWQCDMEZxp35iyFSzw8Ou8o6d/wDfCj1soaFUorwlahfGct2+OjgzqTmInptjFaSkI+7axpNW1LNJY593eM0nhnPJTUGKurRRrQoTSZSVYEk6LoJtFmm2UxGGMroWVKDdQFG6JKIKrpOgyEpGVnDGH01ASa4n6VaTzJKukJSMxKQJ3huyM8yaBTtC11iqc3iZTtUqarVAz3Tn3xFTOgaB5maVnNTmAUM53udvw9RuMA0PrLgCJ80AFUs3BDR+6WurPyHP8xVLwRXu09AlDyAiSgFC2/dVKZI0ECyYirdqqVt53rUC8sEyElE7m5Cb1I11JmlJlnJMxbMGSbQbwzS50U1NQ0vULCRfIKpKJOi8SZAWA8koQThiQ8EpVdLoM92dg45iRzQp04YkuFKllIdlIZxKQNhGaQsEeo1SlFlozACVLvSIMlAKSQJDPPinIwhKaCQN42onILEpE6LmcTzRTVtA2pCrEyuFN6XlFV60kkWAD45iuqGUP1S7FX1BIQLJESM5nQTLcG6adtNDTFJT5cyozJGgW2eGHaFLoZoi8Z/wgZ/8AM2bMLYeUjA21U5Si4Os6u6mWcyUCSrOZ6YdqabBUMqRzr/XXs2fmqWZg7wMobJzytgcEb25FohJw7GalkDQlarvGgkp3NB5LISKsU9Wj5abiuk3dHKkwlOKYW/TrlaUSdT/ANChxJMAUONsFw+Ss9WrkcCSeKcc2ZByWgjbyZJRPZDLOJ7AQDZ+iDq6xSkDQrnDgtmRxEQE11HxoP8A0q+dADNYkL/hVzT4c/8ASTsePINgcg4Y4vgTsD+gfmlWTktFHTj+lSmQR4TPhggrBz8f+GjTvw1h4bRiWBJkEsvEhTY3GnhNSBoCVJcQPJCYDeI6vYnS1K+bJKWnkzJkJLDqCRwoEPVFJTUNWglSrrdRJek/6yGkz/qg4drDhNRRVcphLqCm8BpSbUqHykkjfgFQKtu7xj98fl4HD9MMPfbtzlCHFBAPAAkDgyD1ymceCWzdQgyJXMSvWEystlI2jgNDQhATUop3CpCfJvJKgmW7IAndNumKXEaqiS3Ut1AQV+KV80qtRdEyJWqBEs0jOYoxMX0JIkDIyTbMgrFnAOSFKvi55RvCQ4SHJwlxQk4p5RlbpvbpJnAFiXi3dndcNgAlOck6NEYo/eN1aUSBEpALJ3BIkkkiK1xT9xKahu2RM89lg05oUlanGxcUoAKSLqUzIBTd5sxukndEUAWpXV9WClRWFJUkkmaSEps0Hg0CHkJbN7q2gSiV4pUgGRvWS45w3Noj6NaecE3pJQZc4aDmlOKlaGXHZJnJIBloBVOy7bK3TKUoeQtTaViUwbnN3Jy5onvmzRFMr1dxICpXrqbhnLSmYJs3d2KW5psVzQcyx8kk2TsN6Uo6vnD6OYMhMKvmzNKZFkjIaYdfeYvpCyZOZ52jnSAtBtlLeMxD6KRJVdUJyBMgAJmzMBPPmjAmXvGU2QRK2V+3wZoujcgZJjJKDufHt4YtG3ggfdmLVDI3ErN3oElPKDCUYnTU9Y1ukFtfKmaf7ICMQRUUbmm8m+jpIBVyoEdZhmIs1CfkLBI4U+MOScA6du9BB0fBAbARP9HApqpQQPJNqeiZjkAgJxCkBH8SLP7TZ/cIAYqkhz+FXNVxA5+KcSiR3cgnsJbuQTjjlsZaYMTGQ8H6Jj2rdZMU2NYYpsfzpSUiXygoNqG4ZQ7hmJBV9JN1Vt1xM+atPCM8rUqsIEWIinPVnx0/5huzhO5KHcJx6gbfplzkSBeQr+NtUpoWNBHAZiYiuwRw9YG1BTapecbXahUhpIsUNCkqzynFPhyJdXhmHoaUc/0rhvKHDYqf+OQHV0ujFC2RJu03CJqnmzSnnnMAiFY02KkKSuSnbJAq0GSpzPDM6YQzi2JOLaSSQnnZ81oMzmzZt/TDVK1ic2EJCUg2EJlIaD+qAha3EKUlKhzTmWkKSfEFikkEWiwiPvldQ8nEyb18pUJ6MxEpWS4s8XvvlwjTK/m6R+Llh2ifxNxynXK8kBRnLNab0uES4c0Ptsdezh61pCuYSLyQSBaDbKZlD7KcUV1Ts70wSVTsMyd6ydn6oZR1z7ziQbnjG6BNRCQLBumQtPLDj1Ml94ISASEiwCQSM24LLBO2U4epmqxTaVWLBQZnOJTG9nlZvwXKeqeaeKZEiYJmQT4ROW0Ospr3g05K8LueRsnZAbqK15xpJmEkGU80wmUpyJGaycKbpKl/7uZ0c66m8oysnpM84tOmOsVUO9UhaRPnSCjMp3JK5ipfym2yFYxidM6rrLp6wg7nNtzZhICeiUKxBvEHTXKzmRmc1hkN60SIsilqMYKzNM0FQslMpmLNJBA3SDE9yBwZDAnmycewsG3w+GA4y4pDo8pJIUN8EGYPARAT94+sMJzJfHWcV6xwdOEN41ha2VZipo30caTdUOK9AOGYqy6v+G8Er6CrqvBBGnLL4JUAj9JSWK1YA0K5yeQzlARiNCZfxNnw3SfiPFpIFJWIU4fJPNUOFJt5JwBt+OM8DYGOP9WxOWW9B4Mp/QaTFMPJ+86JwONgeUBYtufyhaPlAZs4p9Y6RlLlI4R1o8ph4+NPSlKzMichemggc2LuIsOBseUgBXgJB5CYa9Y1oYYWCmxxDqTnB0ty8MBVNrW08rQG0OrJ4JNy8MBGD0TqgfLckkcSQSTx3Yf1nrpKNO0lplIzvVAKjJI8pLajIynNYCd2HqmuE8RqXC67vLV5O+EJkOU6clRV1DiW71M5dJMgVJQqQFomVEgJAtMeqessmiK0LInYSskkS+QAkHQJGHLtNRdYXEXhJK0lISSbhACUnNeAAzTPNmBT9SqlNUlaBzQkLkULKr1pUedKZVmI5oSmQhj1hdK4pSadslV1RSE0iAqR0SULs/JIlYZQwt8Ua3RTICQq4JESC0qmCArNdvDNeuSJgoSxRFSKS8JhJJcLiUyJPjFKbQDomTOZjEXWlUZ56ykSQTMKF0JnMlJE5BMkXbDOH3Kl2mXN8lJmm6R1Srt8W2XpTvWzMlWQgKbo1OrWhKzzLomg3yjQJGVqbAZkbsYc827Sh1JTMpugyLS7182qPOkCVZibABC6ZiuaZqkVKXBeVdmLsphW6kics5BsnppTVO0i5raN6y+VnzxcJ5xSTPxubmugRTBxqkR5lRklIt6y6oGWi7apP9R3YZS63ShBCFEpSkGfW3VCz5ABIznxs9sVJUxRyvNDm3V82bl9SboASZBM5WiaTnM4W26qhS2XEXVIldKb6pFSQRKQkFTkZeNnnDiKR1i8t1gkJugEhL4JkmQsmJyEheFpnbRNO1LElNMggqvGYPirToSLZ+LoE5QVBFKoqU0MyJAErvEAWCyV6WYkEyNsBFI6yapCRKcr13rF3Qg6FBRBUJTKSDmFrZ0ylA+DkI44vaeQ8v7ICWMTU5TJ8h4daniKueOJQEBrGsPWwqVqmz1iOjYscV+OswvEWnhuJVzhwpMlDjAyWg7CexMAfpU4BGeEpFT1rI8lfOHL4w5TwQlvEGVML/iHPR4BeHBJW+YD9K8hxrdSQR4J27xkRFkT05DFuaexOSRgnegn9Ezw9iOBtpcp3wfWKVXm30nPIHmhSvKBsUbZg53XtVMUbosVlNVDVkouq3G3SJhO4l0SH/cMLC9UKtxuzntJFQg74WypYI34aL2rtSw0fKeSGUjhLqkyhLD9eK3HFDm09NNfO33Bb0QB8qEYzj5HXtz6lhPm2BosFhc3JCSNEyb0bdu3jyBKxOY3BvaNso80Nu/DrVBibzNO54yUqIChvjMdNm/uRedJWu2028VpO3Ruc5sHbp27keZHDxbf8YH0Y27duaJXAE+Dbvxa0IUOqFp4c0J5gEuGXxxzmxOUoAKLOWDNv4tv7Il1IzcG3Tbx70EhoDb+7bKJhEjt27bZ9XMy02/4nlgyTxTkOGyOcgHhgEN27c25F06M0Dg2Z2AyShXFG39ZgOtOFLgzEGRB3iJEQluoeTV0w8l21XE4JK6V4Qhutv0VQf4+c39YnN/UEwl1h1K2TmUkgg8YyS34GxP6eHaWoW25upJHEZZxvGyEt4mwHW/4kySvhlYk8Au8MD1OoCnJWoNixu8028YmN+ANu3igiDwxPKdhLegj9CzZc8AVdMlwjMTYocChJQ4jA9Rx6vZTuB6Yluc5JO9nMSrsbrn0nOFPXQeG4EnwwpNHTIbvZyPGP8yjMq440S22yyiPFPg/bGY5M8GP3R+/INh4pg6eWNPIcp/wiQ/V+3JMwNhv5ScqeGAf25JwobAztnHW4ZXOMq0hJN07xQZpUOEQhvHaAK3XGubL+ZtRl0VAb0B3DK5t3dTOSxwoVJQ5JRI6NjnjONjo5RsLBHimM36KFJUQoZiM4hLGJINRTz8af0g4yZK4DI/K0R11E+Fp0iZvJ/mBtG9PPomIlLTkzbKemJ6f0BKJ84gniEp/GI/EMQ6bPoI/EMQ6bPoIsr8Q6bPoI/EMQ6bPoI/EMQ6bPoI9uxD6xr0Ee3Yh9Y16CPb8Q6bPoI9vxDps+gj2/EOmz6CPb8Q6bPoI9vxDps+giyvr+mz6CPb6/ptegj2+v6bXoI9vr+m16CPb6/ptegj2+v6bXoI9vr+mz6CPbq7ptegj26u6bXoY9urum16GPbq7ptehj26u6bXoY9urum16GPbq7ptehiytrum16GPba7ptehiytrem16GPba3pNehj22u6bfoo9trem16GPa63pt+ij26t6bfoY9srOm36KPa6zpt+ij2yt6bfoo9srOm36KPbKzpt+ij2ys6bfoo9rrOm36KLKys6bfoo9qquk36KPaqrpN+ij2iq6SPRx7RU9Jv0cS9aqukj0ce01XSR6OLKqq6SPRx7VU9JHo4sqKjpI9HHn6jpI9HHtFR0kejj2io5UejgfTv2b6PRwh5mrqUPJtCkqSCDvEImIDC6s1CRmLoBUN68gIJ4VTO/HmWeRXz48yzyK+fHmWeRXz48yzyK+fFjLPIr58ezsdFXz48wx0VfPjzLPIr58eZZ5FfPjzDHRV8+PMMdFXzo8wx0VfPjzDHRX8+PMMci/nx5hjor+fHmGORfz48wx0VfPjzDHRX8+PMMdFfz48wx0V/PjzDHRX8+PMMdFfz48wx0V/PjzDHRX8+PMMci/nx5hjor+fHmGOiv58eYY6K/nx5hjor+fHmGOiv58eYY6K/nx5hjor+fHmGOiv58eYY6K/nx5hjor+fHmGOiv58eYY6K/nx5hjor+fHmGOiv58IfpQ0h5OYgLB/z2jdBsOmEpVRUCiBKZbdmd8yeAmd4Ab0fh+H9B708ewYf0HvTx7BQdB708ewUHQd9PHsFB0HfTx7BQdB308ewUHQd9PHsFB0HfTx7BQdB308ewUHQd9PHsFB0HfTx7BQdB308ew0HQd9PHsNB0HfTx7BQdB308ewUHQd9PHsFB0HfTx7BQdB308ewUHQd9PHsFB0HfTx7BQdB308ewUHQd9PHsFB0HfTx7BQdB308BXqFBMAjxHdMv9/e/wDHn//aAAgBAwIGPwDLmjMfBEgLYCaqrZaVuLWhH+YiCKSqadP+2pK/8pMZoz7CUWWQJLIPDEnEhSNwgH44PrGD0jkp+My2rhlNP79MOGo1Sw83pzkyhJtz2pAPGLYBVq02iz/TceR8Tn6oAZTWtS/hfn/nSv8AVCvVNY6xs3vKQ2sS3LLh45wfUtZqdaZ/6jK02cKVqtzaI+gXQvp3UulB5FtpHhiatXHlplOaFNrH9qyZ70oPr2B1bUv42XQOW7LwxcUqStwyB37PjiefJaItMA6chg2mWScWZFZSZ+HJON7ISclsbf2nYWzPHFsTynYiFZARB+9sEpahRzlbaSocCpXhxGB6o1U0av8AadKgeEPB3wSgqwrWYK3EvNEf3oUf/p/4FTdGxUpH/ZdHxOdWo8ABO9BGJ4NUsJGlbawniURdPETCSBt4rYt+MwNzIfghlOVfB+3Zq26cvFkEHhy8UJyjgyHh+DzHPt28cDFtaKydU4D1FK3dL76hnug2IbBsU6vmJFgvqkmKmh1ZrvufAyopSilUUG7/ALlR55wyzhHVp+TCqjFtZKh54mZM7xnu3nFLJ4c8IqMH1qq6epSZhQmCDvKbUhQ5eOKem1vd+/tWwQFB5RU4hOY3KiXWtkaA8HUaDuwMe1Ur792QeYXIPsLMzddQCbDLmuJJQsZjOYGzO/kGQ5NHJG3b+vfgC8dvLCk1mG07oM/HbQrPotTywS9q5TpUdLd5s/2FIj/jO1dOQNDgWORxKj4Ynh+sSZbjrRHhQoj+2CWW6aoTutuhJ5FhI8MK9dwGqSBpDZWOVF4eGLrk0r3CCDyG2DB3MoyKyCFZTlHwAynKckoVlmNiTiGAUriz5VwJX00yX4YKqB2ppFaAlfWIHE4FLMv/AIkKVhWL09QgaFhTSuKXWJJ4VJ/VBNZgD/Vjymx1qZbpU1fAH80t+FBQIULCPghlMCOOF8H7dmrj+PLxZBB4cvFCdjx/B45rljVtHRtc1AMlOuq5rTKZ6XFkCdkheVmBjGdeNZqsuLffDbSLerBtAQ2J2MMpBCUjx1AqVMlRUFPPFbmkJE58cwmAlnCFLT8pwJPgQYT1+DEJ+S5P40CCACgq0KFnKJp8MYdrdqvUFNKHLrzEz1S21EXm1jMW17kptrurQRISwXW3AnSvDK5kOJn4yDmW2u3x21goVukT3J5J9WZQLyTMxY2dv748yZwEFBvWCB9GbRAKmyBwGBdQYV9Gc234o5yZbd3NAuoO3e278Wtnb+vegADMf2QfozZFolKF3UTsO3bxwOaDyDbtnElDbxQbqDt4IV6/h7TqTnvoSr/MDBnhKWlHS0pTZ8Bu/wBsKVh+LPNHcWEuDlFw+GP+K9T1A3AooPIsAf3QRW4PUIA0hJUOVF4eGLvlDRpjfjPp254EAQrKcoyTGwkMgynKck4VsN74D/1XCWHzurQkqHAqV4cREKXQKqKNw6Eqvo6Ll5XItMKXhdbT1bYzC1pw/wBKpo/+bChiuEPsJHlKQbnEsTQeJR2YymBHHC+D9uzVx/Hl4sgg8OXihOx4/g9Tvy3oHyEqT6y6AZTceUWWbwH/AG20OKH89kLocPSSunCVIH8Vywp/mUCSBnKuGCDnGcZpcIyIJGdIgWE7eWFsq0gjl0H9ucSEa5fl5XvEqo3BVMgmcpqDL6Rwnql8JUbCYEawazfl5h1JW6yUHVvervpWoPsIWDUNNXFIKX1tXupJC03ubcUSJfmfi2D6xYjRYccS1aXSNAraXStPt0y6llKRJSQ8oqDoEwskymgiet2tus+HIoNSVONsYZTOMlFaeqU6H6qpMwUB9RSlDShMIbSoXRz3tc8J1f1uxFnBqTWFlDLTQUUICaZlSWQEUr00qUpai0VKDhmSy8RzNWGmtesXVTB3EbxIWtKgK19HOcNM4FBpsJSlSnT6ukCSqcWmg/MGgxlxGuytVaBw1ckqdL62WOseN5JSVrKlKUSnxiTnjFqZj808RViTCXQAcQwkqUtoKn/x00gdKuaR1QUHJyR40aiav6168Vdbg2ImsLlO45SuJLXqTdRT3+qaS4hwB5K1EqAVzeYkpUI1SwfVXW7EMKpF4VVulFKtKOvqOupmqcLmDNIU6AZESSVGYFsYgwzrLixxxiVRUUk21JRT07aQeYthbSlVKbhbNM4ErdUuTYJQo4zrVr3rYcWYeq1miCmqVDjNM3zAl40rbaC44oFa0EKLXNSVk3gH6Ck/MN9WBu1b9Kp9jB1s9UZrQ2GCHFdY4VgpF1xLiVC8ASCIbwlf5jPpwaneYo01VTg63yt0hLahUFbguuJWUhZdcK3FkqUkGK7WnAW6cVz11oOvVbdIKcPoUkPocdZqEKcQ4UBtpbclEzJkJKrqqs12QqppU0bS7uJXFPqoXS668y0hBDnryFJZdKLFXTevSuJ1mwbW+vpK9xCl1Yfbrm31sdetPV0YpkMgtsoSHJLW+6byVJkBdCUam4LitZguBMIQ7SOUVK7VuV9TdKih+82lAabuq6ynaLilJktapEJRrZW1GuWtXrFLUqQPu8MIomyinaWQUvudaiSlFSk84hBTziqcsA1+ew2pxrXBODsqCU3OtqHlJSkFU7oleM3FCaggKUlC1AJOAUdX+fFfQayMvVorHE4X94offLrai024uldQhmkJLTSmVXHEmwm4AnDMAxn89azGkVQcaTRLwRNMlbpQVpUalukbLRbAKgFrSlUpGZIBIyyBkOCFJrsOZdnpKQT0s/hEXqcO06vkLmOisK8BEK9RxBtxI8lYLauUXgfBBVU4a51Y8pMljlQVRdNitw2GDv5TlnkAynhgHIIkMhynIIlu/DkEWQo1OCNIePltfRKnu8yQUf5wrghS8BxvgQ+n/wAxsf8AlQtVXgzi2B/qNDrUS3SUTKR/OE7AZTk44Xwft2auP48vFkEHhy8UJ2PH8GhhwzbSzSAA6AKO8Jf1EnhMWDb+3TC6kE0+IHOtAElHdWiwEnSRdVukwVU2I0zrYtmb6DZvXVDwwlp515qQAmpuzN8gqPgj1jD6tt5ndQQqW8ZZjvGRidmeMdYaJ6pzDqkKH9LCs/8AMAeHhyYodVtasMwSrcebS9V1yQpDNNJRdU0FKQ3145pR1s2iAoG6SFp/MHXVvEKqr1YrNZcLaZralRv1Qp6lpp+r6whJ6pby1hB8hKbs5oJjWP8ALnV7XZ3ENVH8I+8WqNcqkUTgqgyvq63rioIWVWUzralGd9LgKV9ZrXVNodXR1imnS8tkKbDzii36ulbNE8b6QhICXFdZaJzmIRTjC3XK9aSpDIpXw4tIIvXEqwtE7t4TUSAJgEiMVwSjqjUU1JgjDSXCEC8htTaU2NpQ2JJEpJSAJWCDVFpVXq4zjCqwset4WL7jzi0kt9V1tYtBS6uSVkJSkzXdKbPyv1edZQX6F/E+scS6HetcXRob5q0qUktNsMsBpSCQQVATAnGpmG4XgH3i/VavYo31fWtshIPUDrr7vM+hN1cp3jKy0SgP0bGE4hcxCjo3Kh6lfeD9RUXG3nGqo1l6pQ06FZm0I8UN822Nd/U2aRrEDibjVSwzSrpHWHWW0tdXUMqfqAFkALSptSUqSqZBWXFRqziVZWspplYhizjKa5T5o2H2K5xAdQ1Tpmp43yApYWEEXk3SZxjBpsYQ6hWJUNYRSVFYKVx+pxBsOg09QUgqA5wIQZcySubYmlcx/DMND7zKC5WPu08wEqcKKdbMnVVCurmEovXmw7YQDLBH8LpMQraVXXFl5t3EZPhLc3VU/XLS+sNo5yi0kX/F51gjWxk62YdWuPU6VmnFVVuVzJbWEFS6esIW21NwJUUokVlCVHNPH/XllrqbqqciqeaBKqByw/TpbZKXkNTUyGStDgSskkqNTjhVSVdcMZUhbS6h0JdpV4fTgPFKFqfWyy6rrFrAU4RO+o2xhGqGEaxt0Va5hzSE1OHp+j6uaSfV+tW4ooU1NAdC73O61BQZJGquIawVLFOioQ8EqdWhAUtVRUSSgqIvLVdMgOcQFGRlH5/4jQqC6ZvFKe4pJBHsbYziemYz5xm0mccewHDEtB3v3xZZwRKto23Z6VJE+kLeQiCqjccp16JG8kf0qt5FCP8AiuNPp3jcV0VTH90XaylW0flAgcuY8Rg2xvbePJL4AQDkOU5BA2Bjj+GUcUwdlbx8sC459Yi6o8BJG9C3dXsXUg6G3xeH1iACBwoUd+FrrsJWaZP+o39IiW6SiZSP5wnIMhgRYLYO+NmVacvFAgRPKIBiZyTMaYkNnLe2Gq2uzSD6rVUjJJzi/TrLTonu9Wpsy3IC0SlHj2wuahmORFZhtSW3RnHkqH8KhmKTuHhEjFNiKAEXkkKGe6tNihwA2iecWxr3ry4g+qNU3UoJzFVQ4CBptDTM/wCobuRX/u1TYWvU9qrZXPELnq6X73Vsklzm3ipZbSDYoLUgzBINL+VGMMYE8xVUt5rC1pYkqnpyJKbpyCLjSgkpKQm4UgpkRZWVeoeoeH4ZV1CUocdYZQha0JN4IKkiZRO2UwJgE2gSxDH8f/LmmdxuteU685NYLri1TWtUlC0qtO/mzwquwzUCiqqdDzzRKXFqSHaZ5dO+2ohfjtPNuMuJJmlaFJMiDDn5bs4BSOakob9WVRJM20ISQstGRvAzUFWqvWhWmAj/ANqMDugWf8SnzcPV8Vp35xh+s2A/l7htFj1Kl0NPtNIaUgPJuueIEJN5Nk1BRSCbtpJOD1VY1h9frbT0D6qY30uOpo3XG231ISFEKaU4EIUoggKkJi2MCxmq/LfD/vDC7gpClu4hgNrLjaW2kXW0hDhK0i7IEzAmIxTGE0NDhtO8tC6l0Iba61cksoW+4AlTiwAhtKnCo3bqBZIRQ4fjNdgmA0VVUPKabfeQ0HXHVl15SesVO1bl9avFSVi9dmAcC1ixzU+jrq6hX1tM5MlKTMKCgEm4sFQQq6sKSCEqlMAhqmxrBaWspUOJWht5tDqUuJBurSlYN1SZkBQkRMylMxhGMVWrNIvE6Au+rru85rrUXXboBA56QEqvA2CyUPYnQ4HSsYm42ELeQ0hLq0Az6tTgF9SQUghJURYDKyKas1k1bwY674qVdW442x63VGmZQFXVqT1i+pZQicjzEgZoGH4RqXhLOMVTFQ6EFDSnnWEKaRUkEpK1MpVUtIcBNwdchJAChMflbqDj2DsVWGFxH3cy6gLaIWovDqr15RDilF1VpClTWbZmn/L+u1MoF6mMG81TlM221TUq+i8SUqBcXzwZyUoTkZRjWA/lnQUNPTM1hRVop1JUpNT1TTlx4gqVfDLjKgFHxFolYRshw5RG9B2+GChSQU74H65wqdJ1bm63zDx6DxphS6GqC/krEjyyIPIIPrFKsJGkCaeUTET0RLTlnsBlOU5JRPc2Bjj/AEFwYjgbJdUZlxA6tye7fRIz/mmN6Fu6t4xbn6uoGfgdQODOg75gnFsIcQwP9RIvtngWiaR/VI70TBnEvgDLYcUDh2EoTEzEhsZCJnY8WwxDC6FgL1ioVGooxLx1BJS4xP8A3m7E/wC6lvNnh7V/FAtFeySlN8FKlJTZIg5lt+KpJtkOGU6VwK3lWHllDgThi1gg+KUK4PKiS8LUj+ZSB8aovVryQf4U2+GwRSatYWw45iVc/wCIgFSghySUJAFt96UkpzyVPcnhGrTjaRjbv/IrCn/8hwD6OelLCAloaCUqOm2cUuqWHYc7U+sY9hIfQ0kqUKY17IqHCBmQ00VLWoyCUhRMgmcJ1wVgGJo13aw2uw5pxIUFpRQsMoS4lxV1KFV9Q5VvU9qAqaClZsUKd+o1q1zNGzhlWph1ZqqF9mqW/ThCa1Lr633mm2y42wuocdSsqIaK3A2qNaXNZ6fWFvU12jrlTrVvOUfXN1tMmk9WvJTTNTpi8ptFMnnNEdc5UPNqWMZVq4dYqWkp39YK1pukU802/Uu611TtN1iW/Oh6jeNQhskh9ghZCm5g6w4bgzmt9Bh7ms9Y7VPUqK19RbfS4uiqadLTyFPtlZV60lha+f1PrbS0JCIaeqtY9bmUVut6WHCh5+nDWGNYZUOdY22gj1Zt+pUG3HUkEO9UlCkrbQY/Lqhxam1uQ2rD6JmrWp2qQzcepqhFUtxKA2hqobcUi+6+pyrS8lDjSmkA3sCw7V/DdZqZLGAMt1SJVKHm1t4lRpq26NSiLqxSJeNMGFSLd80/PKYr328S1vp8NoqHEKigQXalL7pbrkepNVZUS86sslz6GoKnnGbgdnK7H5i4biWD6yrwJ5qoKUVKqh6nLjWKUfq3qs0pp0J9WLq0Ip0eZmXnXnW1rFHrXiWp1djeqNXqw/hyxS0/rimnl1KXVtusTmlqqaJQXJFH0YS4QmRjWhnVjDtaqVxFPiyQyC6mkbpEpCcGaoG0KUwmoQ31ST6pN4KD3XqVJMazu4ZiutlUq7jdMyHHql4dUjDuuonWwZzeXWzQw8k3iT1KLAlIxqpwfG9aHq9LlbSsJqX33G1IVhCaluo+lmi994rW006rmNuJDCChtJRGr1PS6wa23m2MXf8A+UmqpCmqS1h3qVNUqqHHVPsdcp9SFOOqQ6Q+2mbSFJihqsJa13erRh1eX2qoPCoZqzR0yJMVD7bt4LdDrlOUF5tLilJpkgXWxhVdrjQYsmlpqDHWG3awVClpbeqMAdYQp2pKnj1nVvlkPK6woZWk2tqA1yxTCdV8eW41imOPNKS0GWwipp1Buoo6lSZrqX3EIbQJVCAnnBu9bCKV+s1spqenYxlxP0lYh5xTbNCqhaW66S+oKdU8WEuG85ddbCQi+iK2s1uwvF29WK95xc0pcQx64cOoA47WIAF5tTaFM0y1ktNVDTiCOtU2pKpZpxx7AcOURLKYTPRZEhBU7SgO/wASOaeOVh4wYnRPhYHkq5p5bUmAmpp1I3yLOXMeI5BByjKcgg5DE07Axx5J/DziyLmdB0bu8d0bxhbi8NFNVk+cYk2Z7pRItq40270KewKrbrGR5CpNOy3BM9WuzTeb3kwumxGidYqR5DiSlUt0AymLDIiYOgkQTkBg7Ap0ZZ6YM9gYHBEhkmdhMxIbGews27Rw28sVOu2oART65T6x5iYbTULA860qQDdUqXOmQh42kpXO89geu2B1LNayq6slsodSf9xpUj/WiYULQmDdxZlCtxZ6sjhCwJGFhOINrVoCOeeRIMMYRqxgL9RVuqupQlsrWd+4LAndU4QlOmQhrW3XBLdVr0oXm0g9Y3SFQtUF5nKgiwuDmtDmtknnxybeSJRzf37bYnfPKdMYXiOs2qWGYliNAq9SvVVKw+7TqJSVFlbqFraJKUklCkkkAnMIupUUpGYCQ+KOauW3bux5w2ZrdumAS4Z7f1x508p3P3SifWHRvfFBm5m27sWrO3aPi34HPtnxR5w27dunjgc+2e3bux41u3NA55zaNu/8VkK+kNg+Lhgi+bDZEr5tiQWRd3LNu3fgArmNv7uWLcnyco4coiZymDw5DAnmi4RNO5KY5DOApCOqXuozdEzHJKCpoB1A3LFchz8U4KVpIO/ZlGU5BByW54VsDHH+hSETOU0mKULVRTHyXEhQnuiYsO4RIjQYcf1brVUr0rG3JuNHeCrXEcJLm4AIUMWwxaGJyDqee0dznpsTPcXdV8mBLNEt3YK4/jyz3oJ2IjiyceWQiZySETMSETOXPk/dG5x/4wmj1u1ao8QZA5pdQkrRP+B0ScT/AErEKcYocRpEkzut1AUniDzbhHThLr+GYhWSM7rtRdRxhltonpQcP1R1dpMOpCBMMNhJVL+NfjuHfWoxb+w557CWx8YQJDIvINgmFqlCuHIcvHsBw5RFhGUqlZAyS4fDBgK0ZN7RF19sK27uccUTpnZbyrfCLeWcXXWyD4OXNFuU5bQZZVZJZRlzj4aQiZje2K23EBTahIgiYIOcEGwg7kKrtXlN0dYRa1KTCt8XBNo55lKVpMgAhNqo9UxjDnGHTmnalVgmULBKFgTE7qjImRkbIIAsiyLM0Eg2mN+M+W3YXomYzRZltizJMziQiZiQhLRPPIJHACAbc3lD9WYx7qap9lxDvOPdTVPsuId5x7p6p9lxDvOPdXVTstf3nHupqn2Wv7zj3V1T7LX95x7q6qdmxDvOPdbVTs2Id5x7q6qdmxDvOPdXVTs2Id5x7q6qdmxDvOPdXVTs2Id5x7q6qdmxDvOPdXVTs2Id6R7q6qdmxDvOPdXVTs2Id5x7q6qdmxDvOPdXVTs2Id5x7q6qdmxDvOPdXVTs2Id5x7raqdmr+8491tVOzV/ece62qnZq/vOPdXVTs1f3nHutqr2av7zj3W1V7LX95R7raq9mr+8491tVey13eUH/APl9VuzV/eUe6+q3Zq/vKPdjVbs1f3lE/wD9X1W7NX95R7r6rdmr+8o919VuzV/eUe6+q3Zq/vKLdV9VuzV/eUe6+q3Zq/vKPdfVbs1f3lHuxqv2ev7yj3Y1X7NXd5R7sar9mru8o92NV+z1/eUe7Gq/Z6/vKPdrVjs1d3jHuxqv2au7yj3Y1X7NW94x7s6r9mre8Y92dWOz13eMe7OrHZ67vGPdjVjs1d3jHuzqx2eu7xj3Y1Y7NXd4xZqzqx2eu7xj3Z1Y7NXd4x7s6sdmru8YKV6taskHdpq3vGLydWNWk8FPWy8OIGPd3Vzs9b9vj3c1b7PW94R7u6t9nre8I93NW+z1veEe7erXZ63vCPdvVrs9b3hHu3q12et7wj3b1a7PXd4x7t6tdnru8Y92tWez13eMe7WrHZ67vGPdjVjs1d3jHuzqx2eu7xj3Z1X7NXd4x7sar9nru8o92NV+z1/eUe7Gq/Zq7vGPdnVfs1d3lHuzqv2au7yj3Z1X7NXd5R7s6r9mru8o92dV+zV3eUe7Oq/Zq7vKPdnVfs1d3lHuzqx2au7yj3Z1Y7NXd5R7s6r9mru8o92dV+zV3eUe7Oq/Zq7vKPdnVfs1d3lHuzqv2au7yiQ1Y1X7NXd5R7sar9mru8o92NV+zV3eUe7Gq/Zq7vKPdjVfs1d3lHuxqv2au7yj3Y1X7NXd5R7sar9mru8odoMT1N1TfpFi1K6WuI4R/wCpTSoT5qkkKSbUkG2FrTgmDISVEhKWqqSQTmF6sUqQzC8pRlnJNsfhOEfVVH2qPwnCPqqj7VH4VhP1VR9qj8Kwn6qo+1R+FYV9VUfao/CsK+qqPtUfhWFfVVH2qPwrCvqqj7VH4VhX1VR9qj8Kwr6qo+0xbhWFfVVH2mJfdWFfVVH2mPwvCvqqj7TH4XhX1VR9pj8Lwr6qo+0x+F4V9VUfaY/C8K+qqPtMfheFfVVH2mPwvCvqqj7TH4XhX1VR9pj8Lwr6qo+0x+F4V9VUfaY/C8K+qqPtMfheFfVVH2mEr+6sKvAEeaf0yn/9zvD/AMef/9oACAEBAQY/AGv2dlg4uLk+5waLBdePa4hta0RDlP2+QGEUkFlQeNqSSqnZEhTADAK6K6BRf/CpqJFM7Q9QC2hpAHsAgN4hpe/SL7H3NYDrXXBq7fb4ePRdxhotDtvYN5947HaL3aOJu5EQtC6ywLh1C3szDynEOxa35dFFfud0euRSWBwWlD8oQwuKOjQxwjqRpeLBUDgp3xT8pVE+8bfO8xoPe7s1o23s+Iw3o8w90BuolCEGJhOICZ5oHvcTHeD3jaAbXnH4lEhBOYR3pfN51C7jzb26QnfyiJSuEQduDY4LGP3oeq5YYSuKWGnaUSRMXeyAsdALqH7q1wnY/vXXM+hXlNuFjZVLo4pTd07fOgtAHEg2A5z77bbBGWVzJIsBK8hY+VzCBEw90IFA8MrHkcNndO47GMMKFNTUCh/2SddQcbbgJMYWD7t1ohc5jCtQ03iSEEAE8tPBTMovDeDdCBiogxw5AG2y9jBNKbqCX7u+8YuUTGHKG57Pu1ECEcXTbYzhsF7nC4BeGoXCwOHSPqEXetwWB2O1yt6h4Ra8ebpbuenp4A28/A62wXW38F/M33fXf0cAfd9V/AHKHPwcgPb1fdb1dIj9h9/tPta1+x3S1rtn2TvVzNyA/wBVg8A7OngAXusf23bbtL2Hv2XwcSI2CZaHTOcORQS9YGwWeiSLgTX/AMmiTHKI6HliwiQAORzCMJNgPxJxMOJNorJKHC7/AKP7giRCHigC/vWIB7uMCxAoHM/UAi38rgIuHAPbKw6hSbDiXcMHIIsFnbF/7W9rh/bfcBr2tuC31Xfaw5PXbZwbODs8wMAfsg5zMHIbnAWH3TDyAHZE3rsbW93bYHaQB3/Ob8b1m5BfzMHuQ6GD3PrMGsBHtt6uMfXYH6BB/q2N6tTbCttBnjrYOT9yw+5LztylLzsWz2xuYRYw6HBzla0dJucW5TiPaFijqEO2PAA6Hl7T2AeITv1XsA6HD2xBzbX7N13PwbHP0/eYrn6uPQ7a3JZxOe+9oemaHlMRHxaqqScRF7igwkCChg3TLHIA9Yod7ypg8xh4gtaGqfHGYxE+ne4Cw09BpJx0YCgBvGTNCHVJLpaQBsHrzmOHEIvY0FRWX6j1kIUNwi1UTSYxSyxSBYJkJIlJkUDO0FMblFlYOuMsdAxssOcSLGpWezeUxxEnCD0izyHqCEUUANBtwo2MslgPVMVh3iWqU6sNhtWacLKo6ar7omMhT6pIpWQVOoDrEoVdKNELkWiqWraULSuYQyipElDkU72jSJm3RUhVDgS725BADkvGxn9A+r73Bp0cmntt+2/53A7X7W7T2mdpC7d08VurSwcg9DWBt0gIhbZxua177XCP33Oe1l+tvxh6eB46Q2fufvNxdyJQC+0Q7Vnr3s4RNui94C9wgN4OuEB0gIC/SwlmlPyKYgd2/wB+ymXRJjuu3jLQxjiActrGFajZbDGMDt+Wni5acLXvAIOJSSsc5wlENTHGXx1SSkw7wlKSOho9Er7u4jIQyolK/wDvjxDSxjSasYZUHDuJTWVREOYXB7ZWDiIojxH9gDGNDwcpm5CgPdSyapFOLgAf4GPTgj6bAvY3vnSk9hilEz1fe6IXQcWwRBeGIsiJde85hIoUUzlvIcokOA8QlMAGew3dpvv6L9Ghto8wcAerTwG90PAHIDDs6eAOUOfgDbzDwDrB32QbecftA7GN7keAdnTwB7kf7oWf9j/KZdCKCN5+pKRT/KpgVTtsIw54qDG1wEV69MOUq4HUF3u2EYONhogLwKqU8MpyA7riCP4wAwivLojdAH76JQiE3cYmhxUKUOVzCUQcYLBAbBeGgftQcnrts4NjA20eYGDZ/dCwcg/uQb8YehjDoeXnFg5Dc4sTZzNt6AYdnSxfcB0MHufWYvueluyxtgdPSwbWD8Vg916zO029DBydDDs52DkDmYPVoFhENXO1gCLhHRrEWDl6BYdQjziwcgczbfXY/KZg1br+yDByCHMPNwCI3ar/AFmDj291t0tJaSkSRlZjO4xOHTcTfBBMTAK8SYC2biCe8a0bRcGlsOcm2HRkksS1qFmOMmYitIbc996Bw7l8JArSyiJJFuE8JiPiZMpxAhERRR66TSRcyiO7ErImRGFkMsl8hlQEBGHiZwseAhUkAAQT6mDQTiY9RMpBBwikAGveNrKqT3GiClCigGEU5RRS0zTKJrvy8bUUuOIF/wAGD9TRXvFjsC8SQyjiTuiOphzHIJrDLS6ollUyv/6M7LzCXwsurKAgTd8kmNFR6y0zhjQ5gUJFFk8QnBTpFZAxXgaHIqJDAAgIOe0zy5Y1KAbHmnJRHx+FlfxpARnVeQcghFYiNpmoDqARRevZBAomXh1jACszgklSK7y6W8rN6XnaAw80kcatAxKW64BFIwgmsmBnD1UQQQOXUPAJlDFI60XmELAAXX3PbqVZlDFUs7kVCXjd7ZusSiUlCOe8hymcFlrn3Wt1MXOYJBQDCUSnWIUwCG3uRDTqYoEnsE81gflyCA7phe61wsEWaPQLD7u91onACbt737Xs40/gA0C9UL9dtgWdtupgZ1BxKovcVNUphDS4oALzcjAEzmsNCCNodaoUH2+6YCpVFLREQs/LEB7xsc4REXtvwswhVyheZNUDAXjePqfob+VTiAS3XPA0QmBnuCxwi8HMHe06l6o2OAsSkI2g64omGwG69SJIRK0d8ReW7esELBB1zbh5xCAIGB4CqnYJtHsr3s+CikogLvyZgNaF4WM6PmULCi9zlVSlENdo3AAMJDT6CEzxBwKANvEIhZ9xhjYeMRPDCG8ChTlEHWWvAbLw5eRjb84gUt179+IKR26Nvtn2CHYBjQ0QFOzsTOeSIhYONOIDYHdqpHUD2VwCAsY/xfLLjnd3cpi4qCcA7ovKiCqkLo/A0sY0lqSYwg+1RmUNDxqelwCtDGg1AK93tRcDGNAqSqcJgFne8WMKuYLf4mNIiXe7nQcb2P7509NYYhfZLd6Kqw94g8IiHBZAQsv3nObdG8OTj0heG1rPVrFhtvERDgDkBh2dPAHKHPwBt5h4A9Wjgf8AYBt5x+0DsY3uR4B2dPA/iB3ZF/Bq+79oHvuDh4gXO3lUiGOAfsVHb5dggwmhxXgzDcCanWph+Itvm0fhAwjCLw8YULii+GVH8U4mS/6xh77g4hAA9uZMeqEf2KpXpDsEfsw5PXbZwbGBto8wMXl6RYNW9zgLfjD0MblBg/xnOxNnMwe7Bg916zB7n1mD3PrMX3PS2wedjazWa+5KwcgcwMH4rB7r1mDkN0MHL08Acgc3A7TxafZcTG92PQw8vQDG5ekWDkBrNX7ph5TfuWH3Rf3LbX7N2/k4DBxffBrrrtWoXcjYmY/VjBJxVN4QUhUFVxKShXhEQ1JydefxEGUwh3J5pFpIwwDYICa5j48Y2zMoQeOR6vpnEeqY0QFGn4zECaS6cy2crHMJQhZFJ59KYOEUN7CFlphEA3EnMkugsnEILpkWRWSOVVJZJQoKJqpqEExVE1CCAlEBEBC6xlR4k1Bt1FHtNHB30buYuJD2dwFWUDd0C+xjuiR0gHdiA6XXGeAuuEWkOKlCLp03jFQk5l1V0tUUHuwwzObyKLJHQUBOwS3SxBIlVHquuH8oBTiQwmTMJWwzx3pmA97pLjPRMiqc8EUgF7wjJxK0ZqrL1OJWVRqsVCmAbQFFw28GH+HeY2uquwxwjxDGY0hE4kUhFSmGVoKrZ9L15fRNW1GSdQMZCxNFyepVYdWbEKeEOWD3lO+EiEMJsg+H+IdFYWYgVlUeCHpe0cWarlswllZUniRV+XirsWqdwxq8k5NGRcpii01CyKFPLlR6haCAhAXKkumoUmFeC2GeKKWNmYmS0zNKxzN4pUPWcNU+B0FNqul0gmtG4VYYxsKipCVMrQspFQ0ynEMr1SsZGHSMJzl73gMI8SMacOcMomu6vyeYuVTUs7qioJJKJnGxEHjXiHT0XiCYk/x8wqGDjqYlMkgIYk4ShkEZcmJQPOpWKpSxWZKdRuEuCELVEhkOTmJkcINayGnZzJpnP8sOFlXLIy2lC4303GymPrurp1FxJYZKSQ6lTxqpyFh52cooEmmVWoaSg4zAMufDGzC8MOEY2cS2Up0HTeJleyuSUulEyyYwc1QlktgZTDopgnEFP1aYFEwvEBwnqOrsnFCSvC3EOZUFFx8zhMpPpGpPJ4Kmq7i5SSDiEMbpzmBiMNISVFQnCTqlXhBk/UfysyYICIFzHYs5bsA5RQlW4OTXAKl5fX8HT2PlKLxNdxWYbE7BjG01PnxOxCqWQ1LRp53hmvAwJYaGUVgTpRPWRkYVaGWDGef4yYE4aY2REnxzwEpyPmWJsHUkdD0HhHF0XjPXeL86kZacm0rWLOi0vh4Y0EVQIgi0cCKfVKicEzYeVzEQeDENgfiIovhHgzmNXnley+bVzjfi7WM3jJCBKmkuL8irKUU9gNMkY+GqGErenjxsBIIODE0yOQkUkxMBMAcF4jBOe4TYdU5KMdu86lxhn9IVbi/PpchUU5jcOi43zyfVgrQFPwkxSgpbNRPDozwpDRaaBUTJKqzWqq0y3SSV4wUJh1h5jtTODdY+kgw8mhcQKZUJJqtqaOxPp9WgiTDDWjpdSManMllJjARssjIVUyCy8OQxVjSLESRZY6dmWKmJVNYl4/1VgJh36STDuRwVD4bJqzOsKXnOEMsgcP4mJr2iKgpKGio+ACUy5GWyyBhk0U4mIABMTDnDOsI7EJeW97RtVDTmG+C9UY9T/EA1IRMvnEwomMoOisQMM6nSkE7p2GjjR8xgpqjEQaCQimAGMCieH9MUrg3jlK5LjQvj/iDIYmc5K5/UUhwllOZ3DqEoLDKgcVq8nVfypaj4zKPXMkjalg4WZp98inFoopikYSR0RhbMcFqfx0w7lIyeU4OhSuK+WyucKZdiVG4Z06pD1njknihVdcTCW1zVFYTpeDXXhJdT0lhUoKKQPuioVVSIrDHmpqXwezIV+Sp5lS2YmjsxeZ7D/K7SOV3BE0yl8oTxAoqJ+N0bVEVWFcRs9goaR1RO4eVSiEmCa0Ign15BUi8ksmp7DX0Ycpp/G7DGla1mEBnYxIzATLNRVcDVmOOJdGQcdT8zwUoZTCupiRlM03CwcvjlAgU4udoxQd7IwhETHx3yxeESkssWAEvzR4hUkvWNRrTZWlsMMPZHUs1ViggiAEyjIiIQlEGeHlkMsqmgvGnQRVXh0jGVJjpUlC+iMw/xxwEqKksCp9l5pGq87khyn1PQmF8ZSc7h5LW1Z0wjjHSFTr15jpBwaM8mstnkKjMZGoiQpkUxiTHWnWI0H6IShMnMFTsbIKgi8bpJ6RmX5hY1CnPf6GkcdL5bhpH41VWNRITiZzBGGVXhIGIXhRATgBQKcxYVY29vnRIYz+5G63k162B/FpAHW7OILGEeUbez0cdjG3RscFgcnNqYwTSRyuNOL95RSERBdxn2hEEKRd9v4TCeCCYyk4vcELE98IFEeNGMBY7tQHBhNKprATAgAI9VEkVgF3cT/wCUoC/3QMY0bJI0Ei/x8On32i4PbdZCiqBQ5XNuiAlMHtTA43YFzP4+jgDlDn4A28w8AjxAI8ABwByAw8rO4hEOFwcOzgHYwhxgIcDuPg5Qf2BEPtwirApEOP8AGQ74c7+Meq3SHH3QCwml8c+9yUYV3ZXRLo/wbGFaCVOmX+NQDr03cYilvGIX3QF+wDk9dtnBs4No8wMHJ++YeUeYjO07w9DDYN4MH+M52Jy9IsFg/wAII7HjbyN+N6zB7kOhg9z6zdnnbYPOwcpeYG9Wpg/FYPdes3Zb8YefgDkBg2c7bB/uhb8b90DDyv7QNbZZp5TMHuABn6N2/RfxsHKP9yDbQ5wYfcBzDwBby2X+s2ob9Yhp7bZtYqFHq42LphCXKHJaY8LMcR6TlkYmNm8JFoFc6YvH2I2sD0xEBtEBJYI2WGfeFlvbsaW0QspCYq4Yy0pIeXUjWETFpzOnYMBK6EpWq0AiI6XwKSYOThIlKMhEgFyREgvRh6lw3xTpSaxXVQww8FCU7U0vLERBwRKBI9KdyqIOkUxwHeNCpj+xFo2ZySWUJWKaqsTGEg5BWhIaZGTVOdYqaaNSS6QwhlTAYLAXG3Sx6ZxQo2p6En26ZROW1NK4mWqRaIPDvqXrqh3nNIS0Py0Oqsnb7Jh3VAvAB7obQtuB4P3t3tNldmipA74KhOEYd9pgh0q4q9BMAueXcOZ1g8EggsasAsZsz1NQ0gnkXTOBmB0wiZXUVfV8n3onS8jqSOlcrm1ToUNHLKqJR55OmEzSOokdLrQIpDrZBsA53hbhvhTjLg36PnPFN6+wIwZk0MlRmDMZjnhRiBUOGmCCUhhVZmQ9aUpQUmlgTjujhMo6P76KmUsSUjYaZp8acsEkwZx1kuNq+W2u8XpDLVMJojHmVJYWhWEim9T4GfE2VyleqJPCQRUYmpJVFwsMAAEMpBGBWGCDoug4+YyOU1XhhUeINPSvDaSYmHp6s1sM6flctr2Oxbm9P4iZpcKIUlOR0fUcyOtGSmDLLUCwqgiUm4qdoipfCjTMooKWzmWSCosSZhmDwuj6EpSazpKKWlMHUU5pjPvUKsHGzROCVNDw5EFYpcpDCmmYpTCGXvFuqKbSpGpMT8081xAqKm0FqiiUZTPavXqGoZxDpxVXTGb1NEgMwmahxUj4qIiTmMJlFDGERaEpYJtLsIcytYZL5Plgg8UFMu3pCI2Gpuk8N6HkE0lsLWRq/wDiDlhpipZVOsOZcKsbL4eImUfFpihBiuiuO/nGzIU5OaiCS49w2RtWj6Cm9CTXDuHoihZDjVX1ZHXm1OT+UymfS3Eyq8V8Wqvi6lQjkwik1CwhlSJmUErZg8UMSMb4zL7T+F2c7IdiF8cYLDas8U4yeL01M8T4mIw3h6ToOHi6hUPiRJzxUo68qSiCXfLligQ4mBWQ1tNs5+XRGf5fszWZaiMFsL8wOEeHCuE2DWCHxgq7CihK+wAk2V88owUqvETD6IgxQTXnE0m6iyS6syL16XVBg/D1bMMU6qpo+AlOVJhliLiJjdI8wkrxEo2vJrOayRqGgsTZLhNhBFTGn4KNnB5ceHmMvVjYNaC6kihYEkGmTOZhlSWF2Ivv9D4MZAcP68qPKVJMHpNmfxjwlxVyz0tMp9hvVOJeOc4hpDL8LJLH0WkpGS6VLS1aZIRAoxIRKYCQ2WOFqXA6f0jM4DLpmayvyuPzHYK5XZhmOo/B3ATJ9VEXhsrJ8ccEoeeTeWyCbLnPAqoKTcvfb4oqkOUYgTKUPFI4G41ZgFqTpyo59C0Fgrhnh/ixGKRMR3jTSM0xBpfFWEiMPQw5hPf8ycTEzMUkEZgtBOUA4lAcdqaxanmXTAStCDhVDYnYTVphZkQPH4ZTObVfGQOGEoxxNhVS9T4NyGPrGqRGGgyVFHRQScB78fDbxjthJOVMqeMOBKMgnFQSRCuI7BfLJQeA1fQlSytOayk1L4g5WSxlF13U8TDU8pEwhIqNUiEZcCwkTKPWuUVpyTo1IStJpMKexlk6uW/CzFWeQNOSbNxhMMRMpEUuD9RVdiXDzLCeoqkVRl1QGqeFlczkffUNDopolSLSGXNCBxpwPw4jshNPVdRmKFJ4FYUTWa4V5lKSzzY4Rc1wmks+n9GSvBymcWcWsMJMMhlspiYmBkUPEFSPBQiQqogfEHMzWeAdRYsyaVY41tVs4wQzUTFNOrJrVIxM0Oghi+WjqepeUlnUprASx8ZJwlgy8DIDL1yRKPWKKZ3k5NTdW1tM5ZNaSma8PT8pmdRRkrkUpwnw0gTR8WjAw0avBSKWFWTSFUQKiiJiFeV4N6H6VTaWzCVRSGXPF060BMYRaCjUUl8x1bxyIrwsUmksQsRDHKoURKAGTMBgeBgFipkBxClAgW9qxz9rBfYAXcnZ0s4RvDQ/1gZ1oja/mss1MOzmBg29DBt4xG7WNu1hv0vvEb338uxje+MqgYswh/CKw5AWB42OXIBFw/bMJoBaMlqgvApSnLFw736CL/lnO/ZvYTQR4SZJBd1Sne64h/gohyY28Rxbcj4KJhDb3/aETpgLvwTCG4e/QIs9n2aeXZwCHGAh9kPK/s2hw9rh2NyMOxn8QCPAG3mHgD3I/wB0PALbftwjFwSKhx/jSh1S3ED1ktxQXaxEGMaXRhicSMWXfLyAumAGKHKQw62EV4NQUw/jkPy6VtwiZN4ph7oCsHJ67ertcGzgDaN79AcTBxbBAXmt7TBZcLhcDrHAPZsax94u7T+21uq7W92hg5BG/s6rWK6wLHP4nANu1gC5wus4tr+NgHb6rGLpsAOLiewe59Zii994DZcF4dtgc59r3hr0OtYeN+rRcI8oM71tXE4GAeTtNt0A62za8WAHPtuHXyMDrRAQs0i7pFzBodeIhZcHM9r9Ig6ztjbcwDbaHGGi7VpZzxEHgDhHRYFjmDTbaL3NbxWhY59luxnvuHRc/d5RsZ7x0ucDwF13Zsa8dV2qwXWPBuQAB46XjqdxNdxcQPd2Gc4bgHQ/SD73PdwgDrbdV9zZj8vUAqkWo63oGqYSkUFTAUq1UBAFqCkERCx5IqqJOiiIus6wdTQ86lJOrUKoMJN5UqG7MJDN0BFOOk81hh/Kw0bCRBTlcNhwB4PBij1BjXe0Hjtdc8eLttLTd7jZHQRvY/gxSQjoZIOIhHcfsXaX8vK0yw8xYpWAqenpgmp1B1iAnNpHHGIJEJzTk2SKWNkk5hBcZNdAwCLtw4HTExDVxgXNotefmlEXAx1GTgqHVxFWUlUZO+KVmJYVJ6ZJmuURhYlNLeIWPQVKQRKBWy55ezqpmm2HlASGHqVEhgDqahJLxmVRkM4fa1JO4gg8Yp8EOnlZnOLEoxom9J1RLUS4JmnYV7H0eSXDN6rQhk6cA02PAQ0BKAjFDJhvIGhSrlEqiRDlmubLDioMcKdmVJVmEsqTM3IJjVyMZKa7reEiwWgJ3iNDr98Gn1UQEat3yivEHWi0IgwKgYio70jhMwmabEzGuV09ERUypuU1/W86qSWyaYR8OSGipjBwMwjV0Eo5SFKCfW7u+UgmKUQAxgGQUPhJnWxHpGgaMlEFT9K0rK5nLSS6QSKWoBDS+VwKZpeZQkNCQ6ZSpgYwiABe9kpTWOeXGanZirJaaqRKWzaKg4SKiKerKnJVWNJzpJKIlJTHltS0rPYKYQartyIg4tJUgiU4CMFj7H4w1RLseYqdhX8vxcihIhU8fPjBES0lTwUUvCkhYkxhQVRBQiQpCKZi3lFyq8TnlzFitErqKGAmN2JZCioqcxzOTTqkiZSiI2AUAKF1jVNh9Xma/FnEbDesoynJjU1HVxX1RVlKptF0fHrTanVRSqaYzVaCPLZiuZYO9jI9YYC9Zv7pXVpIIWY1xRuC1RYiUdA15Cml8bKqaj8UKUlk4n1Dwc2iIiHRUhqpk8oiIyMhEwOmoCAmO4QB7Yr0PT+bnE6FpLHhaaxWMkuiqsi5pG4ixM8kkNTU7iaonM0UjZ1MYmcU5CpwMUc0RvrwheqOJiDujRuF8BUdWYlryCWTKT4f0ieZTaoy07JlFpjVE3lNHyVWIikJJLVItaLmEQhBppJGVOsuconMYzVHVNNUJjdmfq2kKWpmXVTO6Mo+c1tGU5StPyhKm6OgJmrIZaqnDkhpLJCwsAiYOvXThTAQFOrO7FCh8FsfavwipPF6ASkmK9LSk0HLlqjg4JGOgAg45RaDNMZfEw0LMYqHFSHPDrgjEKpb26c5R/mbiVPKLjzyqKkkdM6Unsyp2Pj5LHGQPHyiMi5TFwkRFyyPUh0zKoHMKSokLvFHdavcP5BiTMZbReKBqZNiJTyK8OSAq41FzdafUsabAokY64yOcxB4lIQMVyhxe8GgqPPiTOo+jJZHLzSV0fGT6PiKXlk3iirEippL6fPFGlEFMl0olUqi6aJVTlUM81ogM9paicccfU8uGEpYRCfUhTdaV8hg/QKWJFUziJg/fSnpVNoelpWasqumkaJTrI78XEqnLvGsAEapxJzTY+4gYf4Z1LQcrhImKrWryUVR9brQ8/n+GvVS+AmMPIJXWcRD4fTKOlsUCITAwyhZUigmQExS5oMzeHOOs9pTGg0jj0MxlfUlUi1M1j33J4KDpVUlZKQJZEBIqQwSKcsRKqmCkND7qBRIkYCzzM5SuZmvZFmAqaHFCocTZTO1ICp5/BGg5ZLzy6dGgCw8PMpapCSaDIeGWRMifvZMxiCJAEKExLzT4i4i4i1XU9CQsdhdUWIp40U5nhwnUtTSVOOo4i6UPA/F49XSSboCpCJlTUj4aJ3hFUDiwGC4e64gc67lBzFusCwdvJz8FgO7nR6gtYR0XWX3MHKbnbs9DXgL7xGy3ZZoYeQOdnXi59133LGe/l1/dYxFSJqJnFxk1SgoQdQkMBiD2GObvLvRQf46CN1Fo2CPU90gOwoMYYCNSiAvBOIAEFXC+wDvMkYdPtWEIuEWSAPb7u8mPIoQTE7bPf8Ad6GdxdF/2L+N3as+zHkYdjG9yPA/i6eDkKPO/gFtrG1W8/8AyA3fUvQMoYREV0iigsAue/rkt0wjbpeDCeVxohpCHjQeN42FiUwc7lJtYe/INUhAsBYjlUB/xqe8QNrhawXhx2O5wFg9X3mDkHiDVoa+14AIcYbz2HlBnO4x5u0wiJruWzVY34o7LbmII3WczbX28oOYNvGPHe5zmDRYD+Syzib8X1m2h2hePZe3LZZt0a2eP4Qv7PZsa294+qxtgNtBgC60NHQz9fqsZwXd08Hhqddxu7bBZp5bLLdTOLyc7x7LBx6PuXNsusfr4mcAhbe9+m/TewCIaXOs1hqa2/S6x3Fx8TaXuC+1wuvB9j2cNr3PeGnudG1uN1tzaH+oezu8GwR7DB+CNodjoamq3hYlWFg0oyGh5sKRzEFOFBchyRXcuthFu69y/aGd7LYqJcIMZo+HmWJUslKCUZKMPMT5wcFpxA1DKigaHLRVfTI5o6WxBigSHjll4UDkHvcTwkPiVSEykkYIEKtN6XKWcytS4BVNKopaHmcI8LRImpEXWCI2NBLr4tyKTuVRMKM+k9RytYogon3JgVkwpi4Rt3TCGsQYoyvFmUTw7nERkksqGYqqCFji9VKerABEHAJjAGtk4ajJLM4oqxiFLMJwQsCj3QuAyMEmovFLP0AcUR0NPc2ldjABg1lUoynKAp2JiiopwOLGPtKxc7qWZIS7eenMqPwkm8/ISYxYbyCk4gwgiiYU4jdqavJgsqqlMIxZOW9cImOWBIsocihgEXgpFKGFQ1wuMADdwTnHqGqGV0vNaIyz5q16Dms6iEoaDWxQPl6xBJhpJkRWMVNeYz6tzwUJCo+yXiFSJlATGY+WqArKiZzlKn2YjArNXV8jmJjqQ8ZUGO2LOItSVFQE4ksnLHzieybLdhBJqCkE83e+lUxQjinhg3zonTpVbBXIJTlRVLj/AIEyXEikCRtC424f1xgTJsNsWRrGdYCTqkMNKTw9o2taiq+Bk0wnkskEqkURBIIJREYnDy4Y9EMu6eGtTZbqmzCQFQZdUpkGDdPyOl8TU6bn+X/EKbY2FxIRJNJ5iNVEbLsWEJNCTCYVHGEBObIqGlctk0rioaFUpKHxQkWWuvY6uEsh+Ela1NipK5DUc7oXCWlPReUFSWKJqZjJiuUaUmMixxpOGlERNU/5TKZqgokgZKKMChMMqwxJpjJLi9W8lyM5fqQwhofEKaYSYZy1Ou6FmtLSPMjhzijOKgoGqpdRVRqSCHhS0gvPYGHL3l77DTswgotY0USKQp/A/JhPplh36Pad1ZR6c+pGn8Q4qoM4NV5ocOZGSTVLP5wInxJm2HmEMriI2WSyLR6qIgQjFotJZGMiCGzvTfD2d5LZxMoXF7MbWmX2k0KGoSZVvCzSkMZMKp3gdJZVNaqiqjnVYYdVHR0DOSwsokMJKaOXkcRFQMxTmEYdDvbGrEDE6o8qtUJ1dm4xFq7BWeQ0FRMbQ0/pOfZYMdo3Aye46y+TwBlJhT5sc5nTpKnVncOdZOPLDkm7oYiwBRcDH0FknrbEDEXETLNQOZeeo0tSUfh/RkFOsutQFzF1VgjCSxOW0fSMnRxCTlJTTin0E5NLp0WKGWFIKqapMltRSjEPK2bGGmalwnhp/NcNpJS9D17CUxVeT/G0+L3hTfNJ7iFUsxh8ZSSCXxsfUceUoTvrBlctlEti4WFPO8FaOx/ofLpj9QedLDnMvT80xJrlfCqXVDTEnwzm1Jy2dU5W/e54deqsLqvTSmCECY4RYJx51YMiixTEHAg+M9R5Pa1pONrXIvHRNWJQcDFYxVTi9GziMjfSB1ZmMqKdSeXV5NMLagmYzZeHGrDFkow60qNKUEhGIBsvUBiRhVkkw5hyRno+8VMSY2jaCo6iIpasZpmtVovMrQtQREIcqZMO5TlxShomeSBQve5SpGmkSJ4ldZU+D0qxSwXyi0rRkZKsv2KuLc4wioCS0vWELUEuz6TjDWtsN0D0uYZ4nTcHlBkkDNZrKoMe/pnCxK00iBiY9YkUXGicVJgTkfhUp3W2SnDxOHwpnNC41Q0xwVWrnNYhmNxhwxgqApHDqW4fYjTOgIGlYONg5bJoaMlhPeyNUIWZxCaxKrkuJcV6OWjqeNj1llgsOsQcKJfATLDitsDobMBilPpnNMUcJ8PKnoaNgFJBh5MJLKZ9DKjIZpHS+FQXncUooK0afFGmcBapwejZzXFU+j2reqqewdTw7lNPxdb0Lhh6QykMWJjKKVwql8pw+lp6eUqalSTj3ihiSoI6bIrImOnGJqKZeKFrHGLKxSozzBn0YtB1ZMJnUFc4lVnU1Q4L4/ws1xHwqzGYMSaehI5XgTh9h/VM5ncZEy6MouexseQkGpNVUDHInN55JqFyN4lR1Wz/ANHJSU1CY0fg7OaDpWST/ETNJBZsarkVG0PDp4YSyOl2GsFSkJOjy0hoKXKxcDHlVUik4eLPg9SOCFVYQTPHrB2QyqmKlUqCNhJ5WK+AiObDNjNqIo7BOfR0yWhZLWVOx1TQM5q2DThyTmc01PJUulEDBwsYitCiZ7+qTAX8e5aI6xay2wNdjx7LDyCw3dH3m1BYPLpc9g29DBxP9T9TCDtAi7a4HD9xj+4Hma/jdr4DcvS3K4Ojst6vVY9jA6y3ubwNsYRGFKic1oqQ7kTPEXiIgX8nbrBhGFXTVLa5NQAId3FvA8hh5XNurpHSG7ugsEdRvYm2DwBwBt5x+0DsY3uR4OS/gdxgIcAttYdnSz+z9ve3J0s7QN4OAQEOIQF4CAsJjQ3eyxnj10IIIjvC/ujpgUUT7SsJ4BZONTB4gkdyEQAcRd4RRUEA0iYnIwJRCKyCgPHcVTMmYQeNoAYA3ijxhYLWPEbLdF9uuxrLvuWM4eXmd2mc9zylcOsXiIMAan87+2xLefQ/lYHXC8bh5dIOC0Gtus6dDFdZYF2nSL2D3PrdhgB7322Wht1sAC5w6w13utBhc68dL729XEG1v2oc7C/QLh4vXYH6BAeZnXg4eazTocwBZa/UAOAeXQLPHS664bO0DDxjeL7b3WdzqbXxjseIaHs+8Bc59loiFwue3FqvdxhYPGLCF73CO9rG/ibbo0gD9A62fbosssC+wBsez7HPsETO0vdc3LY7ldYz9N1/7IA4uB/EA9uxg29Ddrjst4x1dhoqh60hIasMKaig1pJPqbnkInOZVEyWP/JxspmsqiiqozKTLE9oJTGTFwluLu/HbKPiZT1OhM3xa2D+Ic5UJL4BdR6hoWjq8EIs5YLe7lKEm5AOjYXvsQAAYqcfgpXEaimo73wpiCh6vlapSi8FIeZUtFTaGUTEgPDugeXQ0MtMMNqmkMKUCieOqiFTpiCSKAA8ysTPlpeTdKW8LR1NFLV1iHArzkqRiLSik5mCNnVGKaEUqQnVRaSyprDGgSFWErwTWII7wI03IIclMYZSNIkvktOy5AJbBjLoUwhCwiEuQAqUFLUxETAm7fVOInOIiIvC/VqC19+ngdGwqcSXue5UAo2luc8o2+rWzveeEAA3n/kk9Npndy/S1XU/hripibhlS+IMKlA1/TFAV/VlH0/XMEhDx0IhBVhJqemsvl1SwaMJMolIqcYmuQqcQqUA3TmAxEoGVoFMUO7UEAMYwudaIg8RENNgg28rLIY4msEBITevsAe55G3zSeG37DCIJkAXvANBB0ByNuBK4fde/wBiV4jp9rpt2t1gyKCE7/ZdUR+0dz7lui5t0ZPCutcHVlEHDeNwO0NuJyeEKUQMUwdUnaDws9jc8PVc3fMPJYRNZ798qZHveOnd1B2G3YuWILAR3siAF1gW27GExZLCALnewII2gLt4RDW4bXs73phRARG3qyP7ZbrH2iwl96oXdEXu6pO/UG7fdrZxZVDgA+yDcKIC+8bQv162cpJ4U1gveQo2cdwh2mOWDlUMgBgEDbhC7wvsH2oaOP75lVpLCKKGvMYhXg++wAt7Yt+Sk0IR4C8ATIIaBvEu9eDdfDSuGSV3n9YVMpTCIaXgD7GAABwFAAAdup2hgeLrn6OOzQ3qd67Db3TrnavWYXPvF4a9TBt6GKGvna/d9lZ6uJh5BZ7g08em/gG4XDpDZ2Xtpv5R1O4n7Wfr6PuN+MboawdHaNz2tYLxHTxcdoWD2GEpgAQucIBu9gQYRAvVGf7S7SDt0XlAORzCAbpgC4Q9kGwQ5mcNnLYwbecftA7GEOMBDgHZ08AbeYeAW2sOzp4Hfbtv2IpRCKayY+1UIU4APGDw7k2sLWE8CqZAz3giqJlUrrinF6pLePf5GdEJCUj3AqAvSOIud3YPKURdYAuHUz+y+8BFw2g5heGgAs0Bc/Txs7Tb2Af67AIaBHRZpdzMHI/1aXWtpvC3RYP3WLyByDYHYYORgAAuvsDXxMFg6bncY6h4m0DrBnbbLrfvM/k1a7g4ntx2hrfYHG3uhcNlrhd22Dld9/lYBDkG5ztPZYLHOt1WbtzhuABax9ltlw6h1i2kLQC2x49gWDWDrnuG69z2ERdZq08ei1nvdaOsB3e2waRc4RDXaAjpBrNG22+1gC5zn2WcgMIiNoPCzk5A4213O03dl+nge63Z3PJYwOtva2+z8K63jAG0uERvAfUFjFjaan80kcUA7wqS6LUQIYbHdYiA9SpaHtijZ2WLDwddqrkTLulGITVKpqETQ8QgIu5PXY0PGV8vDkUKJDDCpHFW28QUiVl90RAQuBhmFSTuZTuMtctMotaJAgiP8URQ3VogI37oAwOdf3ICGoOIH83CHEA3WhxXAAWgDCPHvdt3COrU77ztbaultruTXyMNmkefg7POHAbWI87adgP6BYLH7ePk5GDjA3BsNzMPKPOxrhtLsv7bbDczDy6tnablHmZ7tD7RdaA6HsN9mi8WERc9z7wv7La3i/sBqBgsEL7wdxMUdYerttbcL/udhhHif2wEOltg8nFzuYei/ZrY1g2jp7LO5O3q4wc20OZuURHsuYHaQALRALvvMLunj7IsOt2gdXa19ljPuf0tyAHEw6f2Qhf90WEDlEHC8BCzslse3cGAeMNIcoC77L1dPAHRwDs6eDk6eEGHZ08Drft231vsxAQAQEHCAg8BDiEBsEGUFBRWCXEN5MUipKQwiVNTuFIc5CmDrlNwN8qgAmXeHcOIgDGTXN3O8IJrplKZBYpTGADEMKZRDfAoiBTAU7rygwCc4GdZaUoWCLxfugFjBvpEEdNqgWjyKAwbyBRF1v5RcLP8qDB/Jy/5Vf8AzzB/Jy2OAPyi9jv8a1iBQ0ezW/zjOBIrg/ZKfv72cVMobVP37OKUgB+N++awqf7Uf3zfxYPD8HQF3tmvJa4fYDe6+wwDoYA7i9790wvGx3t2dvEAb3gQf37P3yXf3sf37O3yO/wY/v2/hif5EP37fwib3Of1Qvvf/fHM/rE+Tqhd/wDFZ2+n/kh5P765vZpaf4o2m/8AjdLezT4v4Ibf+t1t7NMdXVm/zrPNuiLhAHAYHP5Tjc9nOC99rxf29TLzAqL4SGiYSDXWeG6nERyUatCpCUT74mWTl6wgIAIBuC8QeD/zvn8+EMu30fb88Z+/hDLv9Hmd78Z/PhDLv9HmsnOfv4Qy8fR1vzzn7+EcvH0dYB9+c/fwjl4+jrD/AKZz9Pd/4jl4+jl7P9+s/T//AJjl30f7NN+e8/Xwll4+jTfnvP1Z/rLLw7/00357z8/CeXr6NN+fc/Pwnl4+jDfn3PyDv9aZefowIN+f8/fwrl4+izPCf5+fhTLx9FW/P+fn4Vy8fRVhdP8APzr/ANK5ebQ/B+S2lragz82f61y8fRRvlBn5vu998vP0Ub5Q5+rXf975eXf+k2+UWfn4Xy8fRFnjUWfn4Xy8fRBvlHn6+F8vH0PYf5yZ+u6d/wB8ZeNFzv5nN8pM/Xwxl4+hzP8AjLn6+GcvGv8Aobra2pc/fwzl3+hjP+M2fu3/AF1l3+hbO+M+fsNNk6y7hzUVrb5UZ/PhrLt9CG+VOfu94/6by726P/JDO+NOfzi/PeXbT/sO3yqz+/DmXb6CtbVef74dy7cTv/IrAI1Xn9us/wBO5dfoGLO+Nmf+2z8/ZdfoG1tW5/h/+u5dPoAz/jd6QC//AMfy66v6ACwfzv8ASA/D+XTyftbWHpAfnBl08nrB/PH0gQf7Q5dPJ4zvjj6QK3+kWXTydtbWfpA/nFly8nLPCtPSB/OPLl5OGtrb0g17/lHlx8mzOGtvSDfOXLl5NWeNb+kHfqqXLj5NGf8AHn0hFv8ASbLh5MmeaufSFPusqXLd5MBc3y69IWXkqfLeA/8A6vZ4156QwB/+6Mtof8LWtr30hw8tU5bR/wCFbW1/6Q/ZVOWwP+FLO8IHpEXjZ8q8tnkoYRHEL0idt/8AOzLV5JWeGIfpFAHjCrMtQf8ACVneEL0iBtZ6ry1iO0QwlAR2vb9IHpDvnXlt8k7fpA9IZ868t3knZ4Yg+kM21Xlu8k7ODEH0hfzry3+Sdv0gekJ+deW/yTtZiD6Qj515cPJM36QPSDbary4+SdnBiB6Qb515cfJO36QfSCjy1Xly8k7ODED0gfzry5eSdn+EH0gQOffVWXJ3L+idv0g+kB+deXTyTNZiD6QCz+leXTyTt+kHP/8AOvLp5JmEQxAz+2cdV5dvJOz/AAgZ/fnXl18k7O8IOf3bVmXfyTtbiDn8+dmXfyTOazELP587Mu/klb9IWfz52Zd/JK36Q8/fzsy7+SRn+EPP187cu/kkZwYh5+vnbl48kbP8Iefr525d/JIzxxCz+DyVZl36cJQbusQPSADyVXl0HnwoBnDX3pA9lVZc/JUw/wA/fSC2f0py5eStvl96QX505cvJWz/j96QTbVOXLyVM4K+9IH86cufkqZ/x+9IF86cufkqb5fekC+dWXPyVM/4/+kC21Vl08lLfL/0gPzqy6eSln/H/ANIB86sunkpb5f8ApAPnVl08lLW1/wCkBufZVWXTyUtbX3pA7f6U5c/JUwpqV36QI5DB3RD1PlxMQwAIC4xTYViAg9zbxa/9IYQDDYQlWZcBKW6wN/Cgx3P4xFg3cQfSEi78Kq8t482FAN3FfekDEP2VU5c7exhYDBu15n8F3HU+XbowvBrK5z8bamy89GGLfLnPsPLU2XryZM/48Z9dtTZe/Jk3y5z6fObL35Mmf8ec+fzmy9+TFnBXOfP5zZfPJi1lc58eL5TZfPJiwCFcZ7dtS5f9XFhkDdzW2enbUmAI/wDDUGeFbZ59tSYBeTZvlrnl+ceAfk3Z4Vrnk+cmAfk2b5bZ5PnJgJ5Nm+W2eP5yYCeTVnBW2eL5yYCeTVvltni+cuAvk1b5b54fnLgL5NGf8d88G2pcBvJozvjvng+cuA3kzb5cZ3/nLgN5M2srjO985cB/Jky8EFc53eoiIiFilA+MuBDxVg04tJEQ/wDxm5wEjlH2aez55niRbzyvEk3nleJJrPrkeJJ+1vPI8SbA764zv7E+hhd9cXb4FO03nieJVvPC8SzC764L9fgXe3ngeJdvO+8TDC763ui/wM6287zxMt3P1uvE30t53Xiaaz63L9fgbu5m87jxNt3P1tnP/wBzvS3nbeJxvO08TzA762Tv7H287LxPt52HigbzsPFA1n1rvFD22867xQt51niibzqn6/BH2tDedV4o286jxSN51HikbzpvFK3nTeKVvOk8UzedJ4pm86LxTt3P1oHP/wB1PS3nQeKhvOe8VLA76zjv7K2s+s5dp8Fbec14rGB31mXa/BZxt5zHitbzlvFc3nLeK5gd9ZR39l7ecp4r285LxYNZ9ZB+vwYv2N5yHixbzjvFk3nHeLJvOM8WbecZ4s284rxaN5xXi0bziPFqwO+sO7X4NuNrPrDbfBv0N5wni4az6wb9fg47TecF4uWB31gn6/BzxN5wHi6azw/v1+Du7mYf0/7PB29g/T5cH/t4wu8Pei/wea2/X14vW7nw8eL7pb9fHi+YXeHfb4Pn9q1v17eL9u58Ojn/ANAelv16f1Ab9eX9QWs8OGv5B37WB3hwfr+IfE3deHBz/a/EO9+ux7BveHK72vxBftfY9h3/AA+fieDzX+Ew731g36vBw68eNh//AKGe/wD3bOv1t5w3i2az6wrtfg4bzhPFw1n1gvFz29becF4uWs+sDfp8HLecB4ums8Pzv7O2H9Pt2nwd+p7A/wAP+zwddtgf9YPRd4OOKy/U3nDeLZiv+sPfY7wbar284bxbNZ9YV2vwcN5wni4az6wXi57etvOC8XLWfWBv0+DlvOA8XTWeH539nbWeH27T4O9Tfr88XjB+nq4P/b1v187fB90N+vjxfN+vjxfMDvDt4v8Aosb9eu34gdDfr0/qA1nhzu0/EFv147fiF0aW/Xh/UJrPDffp+Ibfrt1P+Il+xv12f1Fazw136fiK366v6jN//9k=' />");
				mail_body.add("<div style='width:780px;margin-left:12px;'>");
				if(true){
					int defaultPx = 18;
					mail_body.add("<div style='font-size:"+defaultPx+"px;'>親愛的  "+ meta.getCustName()+" 貴賓您好：</div>");
					mail_body.add("<div style='font-size:"+defaultPx+"px;padding-left:20px;'>");
					if(true){
						String content = "";
						if(Util.equals(UtilConstants.C122_ApplyStatus.不承做, resultStatus)){
							content = "不承做";	
						}else if(Util.equals(UtilConstants.C122_ApplyStatus.轉臨櫃, resultStatus)){
							content = "轉臨櫃申請，請洽分行辦理（電話："+brTel+"）。";
						}else if(Util.equals(UtilConstants.C122_ApplyStatus.已核准, resultStatus)){
							if(meta.getApproveAmt()==null || (meta.getApproveAmt()!=null && meta.getApproveAmt().compareTo(BigDecimal.ZERO)==0)){
								content = "請洽分行辦理簽約（電話："+brTel+"）";
							}else{
								content = "已核准額度"
									+Util.trim(meta.getApproveCurr())+pretty_numStr(meta.getApproveAmt())+"萬元"
									+"，請洽分行辦理簽約（電話："+brTel+"）";		
							}
						}
						mail_body.add("您申請的房貸增貸經核「"+content+"」");						
					}	
					mail_body.add("</div>");
					mail_body.add("<div align='right' style='font-size:"+defaultPx+"px;'>謝謝您！兆豐國際商業銀行  敬上</div>");	
				}				
				mail_body.add("</div>");
				mail_body.add("<img alt='Embedded Image' src='data:image/jpg;base64,/9j/4AAQSkZJRgABAgEASABIAAD/4QUURXhpZgAATU0AKgAAAAgABwESAAMAAAABAAEAAAEaAAUAAAABAAAAYgEbAAUAAAABAAAAagEoAAMAAAABAAIAAAExAAIAAAAcAAAAcgEyAAIAAAAUAAAAjodpAAQAAAABAAAApAAAANAACvyAAAAnEAAK/IAAACcQQWRvYmUgUGhvdG9zaG9wIENTMyBXaW5kb3dzADIwMDg6MDk6MjQgMTQ6MzU6NTcAAAAAA6ABAAMAAAAB//8AAKACAAQAAAABAAAD1aADAAQAAAABAAAATAAAAAAAAAAGAQMAAwAAAAEABgAAARoABQAAAAEAAAEeARsABQAAAAEAAAEmASgAAwAAAAEAAgAAAgEABAAAAAEAAAEuAgIABAAAAAEAAAPeAAAAAAAAAEgAAAABAAAASAAAAAH/2P/gABBKRklGAAECAABIAEgAAP/tAAxBZG9iZV9DTQAC/+4ADkFkb2JlAGSAAAAAAf/bAIQADAgICAkIDAkJDBELCgsRFQ8MDA8VGBMTFRMTGBEMDAwMDAwRDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAENCwsNDg0QDg4QFA4ODhQUDg4ODhQRDAwMDAwREQwMDAwMDBEMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwM/8AAEQgADACgAwEiAAIRAQMRAf/dAAQACv/EAT8AAAEFAQEBAQEBAAAAAAAAAAMAAQIEBQYHCAkKCwEAAQUBAQEBAQEAAAAAAAAAAQACAwQFBgcICQoLEAABBAEDAgQCBQcGCAUDDDMBAAIRAwQhEjEFQVFhEyJxgTIGFJGhsUIjJBVSwWIzNHKC0UMHJZJT8OHxY3M1FqKygyZEk1RkRcKjdDYX0lXiZfKzhMPTdePzRieUpIW0lcTU5PSltcXV5fVWZnaGlqa2xtbm9jdHV2d3h5ent8fX5/cRAAICAQIEBAMEBQYHBwYFNQEAAhEDITESBEFRYXEiEwUygZEUobFCI8FS0fAzJGLhcoKSQ1MVY3M08SUGFqKygwcmNcLSRJNUoxdkRVU2dGXi8rOEw9N14/NGlKSFtJXE1OT0pbXF1eX1VmZ2hpamtsbW5vYnN0dXZ3eHl6e3x//aAAwDAQACEQMRAD8A6T7Jh/8Acaj/ALar/wDIJfZMP/uNR/2zX/5BFSUfp8Gf+kf6z/nofseF/wBxaP8Atmv/AMgl9jwv+4uP/wBs1/8AkEZJL0+Cv6R/rP8AnoRg4TjAxKCfD0a//SaX7OwufsVEePoV/wDpNG17fglrHeEvT4I/pH+s/wCeh/ZmD/3Bx/8A2Hr/APSab9l4B/7QY5/9B6//AEmrA3zpM/NL3ef4o+nwV+v/ANZ/z0I6T0mPdg44drp9lqPw12JfsrpPu/UqBEbf1WrXx3ez2o3u8/xS9/n+KI8PwR+u/r/85E7pXSQYbh0HxnFqGn+Ykel9LDZGFRMSP1WoD79iL7/5U/NNr5o6+Kv1/wDX/wCch/ZeD/3Ax/8A2Hq/9JpDpeASQMDHJHIGPV8f9Gje7z/FON3afOJS18U/r/6//OQfsvBH/aDH/wDYev8A9Jpj0vp5GuBjkf8Aher/ANJKx75/On5ykN/bd8pS9Xij9d/X/wCc1h07po4wsYf9Yq/9Jp/sHTv+4eN/2xV/6TR0kfX/AFkfrP634oPsOB/3Ex/+2av/AEml9hwP+4mP/wBs1/8ApNHSS9f9ZX6z+t+L/9n/7QnoUGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAAccAgAAAgBQADhCSU0EJQAAAAAAEO+DDPLflTVMnpjCzigLYu84QklNA+0AAAAAABAASAAAAAEAAgBIAAAAAQACOEJJTQQmAAAAAAAOAAAAAAAAAAAAAD+AAAA4QklNBA0AAAAAAAQAAAAeOEJJTQQZAAAAAAAEAAAAHjhCSU0D8wAAAAAACQAAAAAAAAAAAQA4QklNBAoAAAAAAAEAADhCSU0nEAAAAAAACgABAAAAAAAAAAI4QklNA/UAAAAAAEgAL2ZmAAEAbGZmAAYAAAAAAAEAL2ZmAAEAoZmaAAYAAAAAAAEAMgAAAAEAWgAAAAYAAAAAAAEANQAAAAEALQAAAAYAAAAAAAE4QklNA/gAAAAAAHAAAP////////////////////////////8D6AAAAAD/////////////////////////////A+gAAAAA/////////////////////////////wPoAAAAAP////////////////////////////8D6AAAOEJJTQQIAAAAAAAQAAAAAQAAAkAAAAJAAAAAADhCSU0EHgAAAAAABAAAAAA4QklNBBoAAAAAA10AAAAGAAAAAAAAAAAAAABMAAAD1QAAABQAbQBlAGcAYQBiAGEAbgBrAF8AYgBpAGwAbABfAHIAMgA0AF8AYwAxAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAPVAAAATAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAABAAAAABAAAAAAAAbnVsbAAAAAIAAAAGYm91bmRzT2JqYwAAAAEAAAAAAABSY3QxAAAABAAAAABUb3AgbG9uZwAAAAAAAAAATGVmdGxvbmcAAAAAAAAAAEJ0b21sb25nAAAATAAAAABSZ2h0bG9uZwAAA9UAAAAGc2xpY2VzVmxMcwAAAAFPYmpjAAAAAQAAAAAABXNsaWNlAAAAEgAAAAdzbGljZUlEbG9uZwAAAAAAAAAHZ3JvdXBJRGxvbmcAAAAAAAAABm9yaWdpbmVudW0AAAAMRVNsaWNlT3JpZ2luAAAADWF1dG9HZW5lcmF0ZWQAAAAAVHlwZWVudW0AAAAKRVNsaWNlVHlwZQAAAABJbWcgAAAABmJvdW5kc09iamMAAAABAAAAAAAAUmN0MQAAAAQAAAAAVG9wIGxvbmcAAAAAAAAAAExlZnRsb25nAAAAAAAAAABCdG9tbG9uZwAAAEwAAAAAUmdodGxvbmcAAAPVAAAAA3VybFRFWFQAAAABAAAAAAAAbnVsbFRFWFQAAAABAAAAAAAATXNnZVRFWFQAAAABAAAAAAAGYWx0VGFnVEVYVAAAAAEAAAAAAA5jZWxsVGV4dElzSFRNTGJvb2wBAAAACGNlbGxUZXh0VEVYVAAAAAEAAAAAAAlob3J6QWxpZ25lbnVtAAAAD0VTbGljZUhvcnpBbGlnbgAAAAdkZWZhdWx0AAAACXZlcnRBbGlnbmVudW0AAAAPRVNsaWNlVmVydEFsaWduAAAAB2RlZmF1bHQAAAALYmdDb2xvclR5cGVlbnVtAAAAEUVTbGljZUJHQ29sb3JUeXBlAAAAAE5vbmUAAAAJdG9wT3V0c2V0bG9uZwAAAAAAAAAKbGVmdE91dHNldGxvbmcAAAAAAAAADGJvdHRvbU91dHNldGxvbmcAAAAAAAAAC3JpZ2h0T3V0c2V0bG9uZwAAAAAAOEJJTQQoAAAAAAAMAAAAAT/wAAAAAAAAOEJJTQQRAAAAAAABAQA4QklNBBQAAAAAAAQAAAAJOEJJTQQMAAAAAAP6AAAAAQAAAKAAAAAMAAAB4AAAFoAAAAPeABgAAf/Y/+AAEEpGSUYAAQIAAEgASAAA/+0ADEFkb2JlX0NNAAL/7gAOQWRvYmUAZIAAAAAB/9sAhAAMCAgICQgMCQkMEQsKCxEVDwwMDxUYExMVExMYEQwMDAwMDBEMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMAQ0LCw0ODRAODhAUDg4OFBQODg4OFBEMDAwMDBERDAwMDAwMEQwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCAAMAKADASIAAhEBAxEB/90ABAAK/8QBPwAAAQUBAQEBAQEAAAAAAAAAAwABAgQFBgcICQoLAQABBQEBAQEBAQAAAAAAAAABAAIDBAUGBwgJCgsQAAEEAQMCBAIFBwYIBQMMMwEAAhEDBCESMQVBUWETInGBMgYUkaGxQiMkFVLBYjM0coLRQwclklPw4fFjczUWorKDJkSTVGRFwqN0NhfSVeJl8rOEw9N14/NGJ5SkhbSVxNTk9KW1xdXl9VZmdoaWprbG1ub2N0dXZ3eHl6e3x9fn9xEAAgIBAgQEAwQFBgcHBgU1AQACEQMhMRIEQVFhcSITBTKBkRShsUIjwVLR8DMkYuFygpJDUxVjczTxJQYWorKDByY1wtJEk1SjF2RFVTZ0ZeLys4TD03Xj80aUpIW0lcTU5PSltcXV5fVWZnaGlqa2xtbm9ic3R1dnd4eXp7fH/9oADAMBAAIRAxEAPwDpPsmH/wBxqP8Atqv/AMgl9kw/+41H/bNf/kEVJR+nwZ/6R/rP+eh+x4X/AHFo/wC2a/8AyCX2PC/7i4//AGzX/wCQRkkvT4K/pH+s/wCehGDhOMDEoJ8PRr/9Jpfs7C5+xUR4+hX/AOk0bXt+CWsd4S9Pgj+kf6z/AJ6H9mYP/cHH/wDYev8A9Jpv2XgH/tBjn/0Hr/8ASasDfOkz80vd5/ij6fBX6/8A1n/PQjpPSY92Djh2un2Wo/DXYl+yuk+79SoERt/VatfHd7Paje7z/FL3+f4ojw/BH67+v/zkTuldJBhuHQfGcWoaf5iR6X0sNkYVExI/VagPv2Ivv/lT802vmjr4q/X/ANf/AJyH9l4P/cDH/wDYer/0mkOl4BJAwMckcgY9Xx/0aN7vP8U43dp84lLXxT+v/r/85B+y8Ef9oMf/ANh6/wD0mmPS+nka4GOR/wCF6v8A0krHvn86fnKQ39t3ylL1eKP139f/AJzWHTumjjCxh/1ir/0mn+wdO/7h43/bFX/pNHSR9f8AWR+s/rfig+w4H/cTH/7Zq/8ASaX2HA/7iY//AGzX/wCk0dJL1/1lfrP634v/2ThCSU0EIQAAAAAAVQAAAAEBAAAADwBBAGQAbwBiAGUAIABQAGgAbwB0AG8AcwBoAG8AcAAAABMAQQBkAG8AYgBlACAAUABoAG8AdABvAHMAaABvAHAAIABDAFMAMwAAAAEAOEJJTQQGAAAAAAAHAAgBAQABAQD/4Q6WaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLwA8P3hwYWNrZXQgYmVnaW49Iu+7vyIgaWQ9Ilc1TTBNcENlaGlIenJlU3pOVGN6a2M5ZCI/PiA8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIiB4OnhtcHRrPSJBZG9iZSBYTVAgQ29yZSA0LjEtYzAzNiA0Ni4yNzY3MjAsIE1vbiBGZWIgMTkgMjAwNyAyMjo0MDowOCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eGFwPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnBob3Rvc2hvcD0iaHR0cDovL25zLmFkb2JlLmNvbS9waG90b3Nob3AvMS4wLyIgeG1sbnM6eGFwTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnRpZmY9Imh0dHA6Ly9ucy5hZG9iZS5jb20vdGlmZi8xLjAvIiB4bWxuczpleGlmPSJodHRwOi8vbnMuYWRvYmUuY29tL2V4aWYvMS4wLyIgeGFwOkNyZWF0ZURhdGU9IjIwMDgtMDktMjRUMTQ6MjA6MTErMDg6MDAiIHhhcDpNb2RpZnlEYXRlPSIyMDA4LTA5LTI0VDE0OjM1OjU3KzA4OjAwIiB4YXA6TWV0YWRhdGFEYXRlPSIyMDA4LTA5LTI0VDE0OjM1OjU3KzA4OjAwIiB4YXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDUzMgV2luZG93cyIgZGM6Zm9ybWF0PSJpbWFnZS9qcGVnIiBwaG90b3Nob3A6Q29sb3JNb2RlPSIzIiBwaG90b3Nob3A6SGlzdG9yeT0iIiB4YXBNTTpJbnN0YW5jZUlEPSJ1dWlkOjNEQTk4QUQ5MDI4QUREMTFCODU4RDIzMURENEY4OUM0IiB0aWZmOk9yaWVudGF0aW9uPSIxIiB0aWZmOlhSZXNvbHV0aW9uPSI3MjAwMDAvMTAwMDAiIHRpZmY6WVJlc29sdXRpb249IjcyMDAwMC8xMDAwMCIgdGlmZjpSZXNvbHV0aW9uVW5pdD0iMiIgdGlmZjpOYXRpdmVEaWdlc3Q9IjI1NiwyNTcsMjU4LDI1OSwyNjIsMjc0LDI3NywyODQsNTMwLDUzMSwyODIsMjgzLDI5NiwzMDEsMzE4LDMxOSw1MjksNTMyLDMwNiwyNzAsMjcxLDI3MiwzMDUsMzE1LDMzNDMyOzJEMTJGMDQ4QzYyMjhEQTRCQzIyN0VDREM0MTdBQUU4IiBleGlmOlBpeGVsWERpbWVuc2lvbj0iOTgxIiBleGlmOlBpeGVsWURpbWVuc2lvbj0iNzYiIGV4aWY6Q29sb3JTcGFjZT0iLTEiIGV4aWY6TmF0aXZlRGlnZXN0PSIzNjg2NCw0MDk2MCw0MDk2MSwzNzEyMSwzNzEyMiw0MDk2Miw0MDk2MywzNzUxMCw0MDk2NCwzNjg2NywzNjg2OCwzMzQzNCwzMzQzNywzNDg1MCwzNDg1MiwzNDg1NSwzNDg1NiwzNzM3NywzNzM3OCwzNzM3OSwzNzM4MCwzNzM4MSwzNzM4MiwzNzM4MywzNzM4NCwzNzM4NSwzNzM4NiwzNzM5Niw0MTQ4Myw0MTQ4NCw0MTQ4Niw0MTQ4Nyw0MTQ4OCw0MTQ5Miw0MTQ5Myw0MTQ5NSw0MTcyOCw0MTcyOSw0MTczMCw0MTk4NSw0MTk4Niw0MTk4Nyw0MTk4OCw0MTk4OSw0MTk5MCw0MTk5MSw0MTk5Miw0MTk5Myw0MTk5NCw0MTk5NSw0MTk5Niw0MjAxNiwwLDIsNCw1LDYsNyw4LDksMTAsMTEsMTIsMTMsMTQsMTUsMTYsMTcsMTgsMjAsMjIsMjMsMjQsMjUsMjYsMjcsMjgsMzA7OTkyNDZGQTdDODg1RTQ2MEVFREEzREU2M0IxM0QxMEYiLz4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8P3hwYWNrZXQgZW5kPSJ3Ij8+/+4AIUFkb2JlAGRAAAAAAQMAEAMCAwYAAAAAAAAAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQECAgICAgICAgICAgMDAwMDAwMDAwMBAQEBAQEBAQEBAQICAQICAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA//CABEIAEwD1QMBEQACEQEDEQH/xAEGAAEAAQUBAQEAAAAAAAAAAAAABAECAwUGBwgJAQEBAQEBAAMBAQAAAAAAAAAAAQIDBAUGBwgKEAABAgUDAwQABQQCAwAAAAABAAIDBAUGCREHCBIXGCETFhkQYDEUFSBQgCNwIjBBMhEAAgIBBAEDAwMDAAYFDQEAAQIDBAUREgYHEwAUCCEiFTIWF/AjGDFBUXEzJFBggJHREDBAgbHh8UM0JSY2J2cSAAECAwEKBwwEBwsJCQAAAAEAESExAkFRYRID0wTUBaU38HGi0qM0NRCBkaGxwdEiMqQ2B+HxE5NQYFKSFZUGIHCAQmJyghRlFycwwjNDYyR0dRYjc4NkhJQlVdX/2gAMAwEBAhEDEQAAAPWv4/8A9B9ZK2YIvTGXazG3a3FkttliW7sPVvuKGNLdMesYNWm+UWsdUmMVxitj7Y9c7NYiXNdSNqRtymkbecSYNyIuJzi9OOHUj9UPtKTlre1s3zhdNRryh6a7si9bE3mJ0xHs1XRI+9/EzP1X4y/5LMh6bM3Prq1rLvpndLddZDqvTK3fvdu9SL1unS62/Vj88XFpUqYzIWlS4xl5QyF13jbyFhUuMcmStP8AbvybQfe/xPoL5uj48unxx2GeWcvLS4sJSTpnaJskzF5aXFheWlDIWlDZ8+P71fxh/BwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAVKAFQUBUoABSBUoVAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKV90/J8fO+Wptzs9XiuczL4L5d/VXv59FufP3k6e5+jG86TgeN7DrPmLw70nLfvft5c7jXOYe9+nPl3HXnfHXmnDQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAod72zpMNrtq83Bll25/nev3nPolhGzsg5vPZZyDL1Opg2kRlNXJhl0mKAAAAKFShUoCoAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQ6HpJBvNznMOi1NXnWCzaLy2HRbk6zVZsJZ1avCOs3UnVq4z5QjdaYSFLgjZ6kKWRZggUjPtBxep3nnsWZpz2Lu9IRadV0zCzcCbPVhZkLLo+k1WbnSdq6PDPZn1riOFnV3vfPY7zzmLBl2upxHO7vV5DkzWwY3fRXOc1usywlI1eaAAAAAAAAAAAAAAAAAAAAAAAAAOw6zKnR7cjzdbtCJ1YU4Dlrves3lmkkgZ11Ws9d3nB8rpM6kZdd1nl3C+sd5pTV8kzd0uEGO26ypzuL2vTPnfLXR7mkzet6zYWc7zvnXLXfdc6PFqbpIVs6sBqsMKdt2c9hutEchh0OmGTzTj02tnsvqxUwRn0zyeTcOnpXbKyscFx33vfHL89dRucRynonZ5bx3xXGgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAChUAAAAAAAAAAoVAAAAAAAAAAAAKAqAChUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAH//aAAgBAgABBQDtbfa7W32u1l9rtbfa7W32u1l+IbXX6F2vvpDbG+gu2F9rtffK7X30mbYXwD2vvRdsb2Q2yvjTtjfCG2l8NXbO9V20vVdtb1XbW9V23vVdtr1C7bXou295pm3F6LtxeKG3V5Lt1eK7dXiht3d4Pby70Nv7vA7f3cu393L4Ddy+A3cvgN2pth3YB8EuxNsa6mr4Nc6+DXOhZNzAtsu5Wr4Zca+G3JoLPuMAWlcRDbSr7QLTr6FrV3T4tXE22a6w0Wu3/STT7nlZmB/PUlfz9JX89S1/PUlNr9JB+Q0pfIaSvkNHXyGjr5DRl8ioy+S0dfJKIvklDQuWihfJaIvk1EXySir5JRV8koq+SUVfJKKvklFXySir5JRV8koq+SUVfJKKvklFXySir5JRV8koq+SUVfJKKvklFXySir5JRV8johXySiqj7qStPTd1LFe07o2IQ7c6xSu5ljLubZC7m2Qu5tkLubZC7m2Qu5tkLubZC7m2Qu5tkLuZZC7mWQu5lkLuZZC7mWQu5lkLuZZC7mWQu5lkLuZZC7mWQu5lkI7lWV7n/F2mn9X6L9fw/wDeh/qKHqf1/NA/WGwdIZ/tALmf/Zl3Atm9ADp0wi5kExGRI8QFzIQhoge/MaNgGIHmZJ97RjIhDeiA7WI8ta+IBDhyxAEYAH8y+8/qD4i9569x5RedHxHO/BkR7F70VzhGi6CLEavefq6M9w6naOcXkag+6/VsV0MmM7X3XJkQsDnl/wD59Fp/fvVBgI6CG+y8IQ3FCD6CGQDDcF7YeAAXGGSvZILYbi1sNzmOhPCDXkhjnNENzg2DEIMN4TWPeeh3S+E6GvaiatZEcS3QdGjXQ3tXtxdDDiNBY8JrHOQhvLnN6W+y1dDgvbc1EEEMPQW9Ke3oIgkj2zoYbgmQ+sCGSA3V3sFNZqugkezqiwucYf8ArfDIGhCDepRILmCHKuiMiy74T4kB7AIGofD9txgO6nNLR0PXQ4H2yHCE8uEF7mljg1zHgOY5h/ujXN6A9jgZgljXBrRGPQXtcwR9CY5cWdPV7jdfdZqIobDhxGsHvgubE6nNitbDgxGsHuNLDMBz+slpjQ3Ix2A9bWwxGYDMRPdcXMIEcCJDmA0+6C0zGrmPYGNijWNEERzo4aPeYoUZsOH06BsZjR1QzDjFj3CMegva5oj6Exi5Q3Na1sQNeI/UvcHW2K0L3nae5/rERpZ1tBOgLOnqMRkUQnQWw3uhujRXNc0RG6RdHOdMNKbMaFzmsEZ7Xr9w73PcawfuGA+4xoiuAZFcx35D/wCv9x9P8Fv/2gAIAQMAAQUA84eLq83+LqPODi8V5wcXF5v8XV5wcXl5v8Xl5wcXV5vcXyvN/i+vN/i+jze4vlDm9xfC83+L6PN3i+vN3i+vN3jAvN3jAvNzjEvNzjCV5tcYl5tcYgvNrjEjzY4xlea/GRea/GNebHGRO5rcZHIc1uMzR5q8ZyfNXjQhzT4zg+afGcrzS4zrzR40J3NHjQV5ocaV5ocZ07mhxqcPM3jWvM3jWvM3jYvM3javMzjcvMrjajzJ43lDmXxvaPMfjevMjjevMjjgvMnjf0+Y/HBDmLxw0PMbjiT5i8cFMcseNMZO5YcfWnyz4/Ly249ocuOPYXlzx4Xl1x5Xl5x3Xl9x2XmBx2TOYXHJq8xOOS8xeOK8xeOC8x+N6HMrjeAOZXG1eZXGteZvG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1eZXG1N5mcbAPM3jWvM3jWvM3jWvM3jWvM3jWvMzjWvMzjWVde+XE+ttjb6bTsid9NqyjvltYU7e/a4rvdtgu922C73bYLvdtgu922C73bYI73bYJ29u2JXe3bLp717ZLvXtku9e2S717ZLvXtku9e2S717ZLvXtku9e2K717ZLvXtku9e2SO9O2hj/wDGn6f0/r+Gq1/AnT8Nfw/T8zEapxKJGn6n10cNFDX/AGKP66ENbpqj+jf1HqWeg9SDqnDQAejT6uTST+ZuhdKLRroNCEBp+AYF0hdI16QV0jQABaeoGiPqA0BFoJDQF0gHp1QGn5PJXUNOpatI6gtV1Iu1IXUtQg4LqC6guoaahEgLqC1BRICJ0Qdquoaahei6tTqCuoLUFdQWoC1AXoUCF1Baha6rULUL0RdoepdSLtVr6kjQu0Wui/Ra6LUOXUtfwJ0QdqnP0LXaoO1XXqmnqXU3TqC1GgIK9F1DTqBXUNdQUCD/AHUg6kHXp1Rb6lup00d0ldJWi6V0nQtKLdSGEB2uhYUQSeldPoAdegoNKAPV0nQAhAELpIBYUG6ENIXSdS0oAg9JC6HLpKAIXSVoQWghdA10OvSVoQiDqQXDpK0OhaSuj16dXaHUNOiOpAa4IhxI6gADqW+rQi06dHoB1FrSF0ajpcSGkItOg1TdfzmdV6/0+q9f8EP/2gAIAQEAAQUA+pvIEQcTfP8A0GJnICXHE7kB9s4lsgbXnExkDKOJbII9xxMZAyw4lMg7X/UpkGIdiPyEtLsSWQYB2I7IS531IZCPaOIvIY14xG5DCDiMyFhHERkPc44hciDnOxEZDDDOILIk178QORR6OIDIoAMPeRZ0T6gMiftuw75GhEfh4yNvBw65G9H4ccj7nfTnkeMN+G3JCHvw2ZI3h2GnJIUcMuSdz4mGbJMYZwv5LA92F7JY5r8LOS5HCpkydEdhXyZGFEwnZOOt2E7Jy9jsJGTrQYRMnznvwi5PzDiYPMovXEwdZSHp2DfKUREwW5UHxXYLMp4hx8EWVcxjggysvEXA3lbKOBTLC578CeWMtZgWy2QYkPA3lhc36Gcr2gwM5Xi/6H8rQhnA3lca9uB/K2jghyr6MwQZWTE+ibKr7YwRZV2PGCbKqQ3BTlSahgmyqF/0WZUPb+inKmxzcFuVAD6Lsp+gwV5US84LspvtnBVlSa8YLMqJH0WZUNBgpypmJ9F2U/2zgpyph/0WZUSDgsyoaDBTlTL/AKLcqHtHBTlSDxgryolDBblPa36LMqLon0YZTvbOCvKiHjBdlQI+i7KfoMFeVExPoxyne27BZlRD5/ArlGqDm4FssEEyeCXK4yJKYO8pkKDBwi5RYb4eE3J5p9KGTjQYTMnTnjCfk2MIYTMnRf8ASjk29tuFDJux8DCvkwaIGGHJRDUPDLknMT6b8kIhnDVkka/6bMkWgw1ZJC/6bskHtHDXkka8YbMkZDsN+R9qOGrJI1/025IyjhsyRAQsOORptI/wpsi3/wDVRbXjPvyj0aPUbZe6PUpvaSbpU3S9+4NNp0GfkaLL0ayqpMUfbiq16hXnuVdX7mo0SxmUY05khSIG4e7UvTaLtvWbhbPTO7j3xL5jNt23LzjTFGlbY2qqkrP3tcE/QbcuK7adAtCj7KxqdKw9zKdCkZ/8yFQdzLwl5n5VXmzZ3Eu39lFuu4Y0N9w1V8nWLqrtfZ+hpN+3VRIVU3GvOs1juTfolKfelw0odyrr/lqlf131alfyM6KbWazUK/UJaYiSkyb9u90zb941y2BGvmtx3fP6+VRbprFAlq7cVUuON+TfRUi3jUpaStaNGErtrVpiFLWvVqhDg7dPmmS9mT8WUj2lUYLaNYUerQ5emMmat8HmhMwttqtEmJWzJ2cpVOteq1S3Z+xLokZmBbNyTUaDQ61HpVHtat16Sl7Du2OyLbVyS4p1oXZV3wqFWo9KuOx7gtRvwW9f5KRtm5KnUp+kzVNlPi0ZlOjW1ckun2hdkKXnrZuSm06PbNySsKDbVyTCl7UuCeqFQpEzTZSPYUeDAjW1ckso9m3HItmZaZkpmWterTVvVCkzNNk65SZmgVun2DGqUrL2ZPxZOPaVRgto1hRqsyUtOFHlqPbkxV67A29dMwJa04U1ElLLbHgwdvHzLYVpVKNWX2lCgUOoWRNSMqqdJGozlybaVW35S09iZ+7LVvPaStWtctxbYXBRJal7TfvpO77Wi2fVpna+65O5Y1DrcClTVs3HIztRptRpE7OWlU5OfFmXHCqkpY9wTEr8ZuMUWcoVbp8tVqHWqBM/3MaKmVSizlFlqpbk/TqluhNxaPb9wij0ym7lTUlRxddEqNtu3HbKTkTcD+QnaI2iuqsa5rcfUIW4VuPn6ZelNk7Usu7adb1PG6cpPVK1brp1TrNuX9Q6VblnXbTLdosC7aI63Z/dWlTdyG4Pftud3UtqbmW7iUWQjRLqtynWlKX5Q2TO5V3U68a1UKpblclG7hW3LXHbO51NplQmb3lpyhT270m+pUSvUSHRJS/aGyZ3Ku6m3jWo+5su2nP3Utr91ZO41NtO3302muptOv2hSMvOz9p1W2r8n7Srdbpu5UzJUht10SoW27cdspORdwP385Z9dplIlaDdEnQbtpu6BmXydzSMO4aXfVFkGu3RmmwJG7f2lqfNJWJZNQuq22RI7ZZr7c/YfzM1elDvOSsWvWPKWZWZqyJ3cW+6zbleotHuu3ZuWvad/f1aq7t02oR6TudLwo1UrFCtGUv656bc0dm9tSbdktdVuUWWG51uQ6lJ3bTpdXjXKfIU28a3SKqPzx6/4I//2gAIAQICBj8A7C6bN8quwumzfKqOo+mzfLLsLps3yq7C6bN8suwx9/m+VUNSD77N8qh/8JL/AG2bZVBtRdNm2WXYR++zbLLsPps3yq7D6bN8qgatSdNm+WXYI+9zfKrsPps3yqjqSP8A32b5Vdin77N8qoak6bN8quxD97m2VXYx++xGVXY3TYjKp/0N02Iyq7G6bEZVdjdNiMquxemxGVXYY++xGVRw9T9NiMquxz99icqo6n6XEZVdjn73EZVdjj73Ec9P+hwf/FxGUXZPS4nKJhqfpcRlF2T0uJyi7J6XE5RdkdLicouyelxOUXZPS4nKJv0T0uJyi7J6XE89dk9LicouzukxWUXZvSYrKIH9HD7zFc9BtX9Ji8ouzukxXPTfo7pMVz0QdWz/ANpiueo6u6TFc9N/UOXi+eo5jy8Xz039R5eL56P+4n8/F89UVUZk1QkcPFuOL11RisdiKc7zMH2cbXQam/k4zDwh38ILDzvN8Zm2Ptpqasf0asWaocYBvLrfJr5q61ya+autD82vmLrfJr5q62fza+auv8ivmrrnIr5q65yK+auucivmrrvIr5q650dfNXXORX6F13kYzmrrnJr5i64PzMZzV1sfmYzmrrnR181dd5GM5q67yMZzV13kYzmrrvIxnNXXeRjOauu8jGc1dd5GM5q67yMZzV13kYzmrrvIxnNXXeRjOauu8jGc1dd5GM5q67yMZzV13kYzmrrvIxnNXXeRjOauu8jGc1dd5GM5q67yMZzV13kYzmof75yK+asXic7z04/NBYacZhU/zasGLWCp7gNIVFVWujTUQ7HE49wbhbFkPxEi+gDr3oc4yKb9OdDnGRXbnQ5xkV230OcZJdt9DnGSXbfQ5xkl230OcZJdt9DnGSXbfQ5xkl230OcZJdt9DnGSXbfQ5xkl230OcZJdt9DnGSXbfQ5xkl230OcZJdt9DnGSXbfQ5xkl230OcZJdt9DnGSXbfQ5xkl230OcZJdt9DnGSXbfQ5xklRV+nfVFNQ/0OcTOC3+qvH96+Sa3unuR7oFqBaB7h/cgWqHdf8ZeHmVRYSLQBDYAaOASYGRMoMQsfQSxdg9LQiXYQJYO0MK60Di8VUQIglhQCBUaWi+EI2tF4qg1iqqk0Uu4OC0DUXBiXY2SIiSFWDROoXWiCwjTU2CAYlr5aCpAAYFr8LvqgmBDGIbwoEVvab0uEFiSQ0J3I1y9ahiRONycxiniGiCxZ6m/lPYDggGLuAwpIpcmo4RcklhAGwzNRiHAAsEaGppP/AGQLNH2pRwiZVAsO+ICmsFjUQYERL4cT6k7IQscwBwTVB4QqEATEBgBMEvAxAAAAVZpxjn1iPXEjVQxewTbvqb+qDNz7ItECqMEg4sG0P4i11Phhmc+pQ0HYMC7xJFrG8WqpoxhFVRsgBGD0vFndrLlQcCkwqrY2CUQ5aqqN+2bTwq6vs3ppk9IYxAH+rD34vCBaKpIrECHLC+weqqEjECBJnBURBLXKRBz+SSDF+LiY/jKbqqMCTdAMDMOQTxiAibqNTxaQYCTSa+Yi7GaakgBhL2mEg7PPisXtF3e+98hiTceN8I02Xm4d6TxaSOEQ5LyF0mEJeKMl30MEiTGAj4Ymf1MCqK6qxhgvci73GcmL+ElYIrODJphmaRujzWqBYNgyEndo98zcKqpqY/yRfvX/AAG1GmowNggPEL3hcwdYLjBd5X7bt4WRupzw4mQIMio1wuDyNLjDTuoEM4LuwJHE4LIez+bSPNFgfEy/iP8AzaeP8m7Y6qFMQTFwJxsIMvOgaiHvADyf5d0/4egiamFNj3bzcBaqzVUBSHjdvBnJu3GtiF61QEWAjHvyZiC7qjBDkmE/B4xe8BVGDXSTVIB3Nk+86FRID2zaLWTPoeUVUWAYTuiAfxxvwmqTUaQSHE5RucXjWC8LvCPC6j6wNDO4dmdpTnCEUAcGJYX4AwuQI9plhBnZ2ta1jKwzWMqp9mlpzj5Y3FiwA9VQdhGHe4WogAkgsWDxkB5mRqA9UW3ONeqHLtC+/oKxhwT6ojDhwiqHoIwpX+JNTQSbwRqFJYXoIiqUDK658ywRizhcRdYNNFWFcby9+CpJqmHHk8oI+tDDrAg4EuHFOSoeg+tK/wAXoVJ+zqYygYz9ELtiFRoOCbWQegsZeVVNSYTvLBooJqa4eHfvqmp4kQ4ov4wVCqnCZ7bj2wl33VL0kGqUJ8V1VCuk0kC0XwPOjSZh/EjXYPo9IVJJnHzeZEGyHfVBwhhVSEXMSG8IKFRIAInGEWaEzDxPfVRYBhO6IB/HG/CapJxgpel7ZB7gMmPhRNRpwXIB9YuR3oNfRxZqAMeKEbHJDDiT/aUGkW+s0wJM9oizKsfaAUAs8WcvCT3VWTXSACLSXeVn1KkDGDDqkIxiRcvWrFU/lCYhDg78ReSprqrpBqDtFzFpsUPWBqYFouAQ4m0GLw4zLuEPw83HYrtTRFyFJflNxwmQsLDZ3aBv8dyI+pGgFzGxrTEvK6927Mg4JgLhEpkOxNN+d0Bwg+NpFJc+V4C4B4yJwWAKgQTY/FbxPxMZRVdNtIc+LvW2LCIOCZG6gMAuYCFqIqBBvqukj2fTwDoUkNUQ8bkT5k9QZyIN+U5HkQrwTgXWhNprCqoIHFdl4bLqaukg3/wqKKyQxJDTiz3LnevusCvCFAJIkTFhesE3HFGFNNIAItYO2CAGLQMJgCMTcVYsIheP1OIC7YShQXwTSQY2uTdjMcbMSxVOKqhS05tGoyviq9FrjHGYPqvSwIABJcEktAWy8aoNRJIoIJN0iq/fCGG+Ba3DvIxP2RpAEJRdgHuibzJi86C59UhmDO1IpjGDgWPBxG00g1OAQ1lRLsSbz3HgGNw0mkxNhsaoG7H1nE7SUSROkgwBia8JwDOy4RftorJIppLiNIEgC4EpSAJYtCawSH9UjyscIxt9kMP4zlyDjBU8fB7NQ849DsjSXfAweVhXfqkypxgLeuKpASe0TnMtbCKxlJcuQbshV5zPvquBBIqFn8Ym2JH80MIkuYg0VAE1A0G57IIZ/BwEasXSSQQQ/GabIw9W7evotTOmkXfZDRdx32JFyJRNMKYwN+s1WcaBJOEAA1kAwv8Aetax0awDGsVeB4CMXJNxrheFLvCkB7fbwuF8ogkxoa++HhcO+jWHcuWYCYImPanMkNcVNJrqBpLwtgB3pX53kZsRTYD7NLWv4W/ol4YVLgB/HVUfOiBXUfVAYyc0gRiZRMBxXRQRSQ1YqkLHgW9qcyS4MQCImhjMnx0n/NhaCxWFSbQ3FHyeCMJIUYBNDRLxiz24LixwWYKhyRWARAAgzIjhC6zMFjK6STUS8h5ieFqpowqhRgkEPaSS7Ob3kMDAYqoEBmebRqMBfFV6LGxjjMH1XpYEAAkuCSWgLZeNUmsmoiggvdLi7fBs8KNOEQeIEHjB873mKqqlQ1QAFjvbM2cJPXXUKjSaXHGC4iLId5zMqqr7Sp4RaYtBDxeFtkjZjBTXVS5DEAX3hhBnd2EAZQQxeFV9mQQXvmqIERaIPGTh1UCHqiBxFnHh8RN1UU4dQnBgxiZxthZ50+LfCwGIYAezgku5kL1+xMJcHhweaGEzX3bxR4RcQRpqxgpoBLuJwAB9UThKDScgkLBrNGE9ywzd6DF7kwYsQAhUK6MHi7wcYDExdmYhwWgv9NRhMJU3AP5ILki8wWLoqrpOLwQ49a0uQGHE7wJjEs3tg/nMBc9bh3lW1MDhcccNn/PN6VwvRgmWCGYD2cGOFbKREL7RxNJqfBNVrwIEmcf0XYtEsS1JppPq0sJC6XhCLrGVYRNMGDwYVAt4Aypoochqr0ag0nMmgX70IuMJjjBUYynLwztuBo4thEUVC85wr7ARVZc4VVYqgRCbsQS1kTNhCERgEtxAeIOOMgxhaHP4hRoH4Rh/AW//2gAIAQMCBj8A3n7N1toC3on9Xa20Bb0Nm620Bbztm610Bbztm620BD/E7ZutbP8A0DLeds7W2gLebs7W2gKPzOs/+u1toC3nQ/5drbQFvO2drbQFvO2drbQFvO2drbQFvOH6u1toCh8ztna20Bbzdna20Bbzdna20Bbztna20Bbz9na2/wDz1H5mD9Xa20BbzNna20FH/Eyf9na20FbzNna20FD/ABNdv7O1toK3mbO1roK3mbO1roK3mbO1toKD/Mw/q7WuhKoD5kz/ALO1roKJ/vJn/Z+tdBW8nZ+tdBT/AN5Wzta6Ct5Wz9a6Ct5Wzta6Ct5Wz9a6CmHzK2frXQVvK2frXQVvK2frXQUx+ZGz9a6At5Gz9aaCt5Gz9aaCh/iPs/WmgreRs/WmgreKf1frTQlvH2frTQVH5jD9Xa00NMPmPs/WmhreNs/WmhLeNs/WmhLeOP1frTQkKf7xdn600Jbx9n600JMfmND/AIDWmhJz8xdn6z0JN/eLD/l+s9CRqo+YRorvZhrNvB/UmWCPmIKqLozDWY8uZhfH/uOstDW8H3HWWhqPzA9x1loa3gn/ANjrLQ1vC9w1noa3g+4az0RbwvcNZ6Et4fuGs9CRf5h+4az0JbxfcNaaEt4uz9aaEt4+z9aaEt42z9aaEgB8x9n600Jbx9n600Fbx9n600FbyNn600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600Jbx9n600JR+ZGz9aaEt5Gz9aaCt5Gz9aaCt5Gz9aaCt5Gz9aaCt5Gz9aaCt5Gz9a6Eo/MjZ+tNBWMznU/zHpzHWUwKdW60+wrNgqxYzFqHb2sWAzmo0YyqAroo/a6nGAEjCpzbPgKgCwNOFmwqYiIwqaS0wDBR/ar3XPMgn/6p92zzR0G/aiI/8tnejr4n92zvIL4n92zvIL4n92zvIL4n92zvIL4n92zvIL4n92zvIKH7Te7Z3kEw/aaH/DZ3kE3/AFN7tneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQXxN7vneQWLxo/ag4IpqBH9XzqZNJEPsGsLQYObv738fxrazhfVKLC6LUWIdUxskomPC+VJVcPSqrvDi8qCiWjxIRfhxrvIcQ86gLqLr6SqXAJ4cauC9DvSCZ/H9KI4eJG5+Mz2Lh6UZosEO7HuPaj3JJz3G7hX1oEIPP8UITV9SUQj6qgIqKk6koiLr2VAQQuIxWE8O5FTRionuzU1NMiAVNQKmpqYV1SU+6zxU+4QyYCLqKKgIp1KKAaKkXKPqs19SRalS7jpkQ0k7RTFGBTsndTU4KBQDp3TJnRioH8KuFCRUZ/WgyqKcCCnb5keP0dwPdTXu4CFC75mRunhwKhdQufSgRdTXkOL0IcOEl4fGn4W+nuXvoCIaCAhIjwoy4BEwn5mVnAunvNw8KayPlhw9C4XFIT87oykVxN513lFG4gCqjwkAnU7fMjx+hSTcXiKg00IBCDlEilj9AV7h6UZM/mClafL3IKXByoP4fpRgW4/pUQeHfRIpj3k5HkVMbnm9CI4/HevfQiWHC+phUiD/QrLPKnYSZHj4eTxoCEmlxcG8aj+IcvwhBR/cwUf4CH//aAAgBAQEGPwCEjoIk2GKwqO0ely8pDBPsjHY5dlL/AGg6aMwIGpB0lP8AASFYGCSOO1ek2jDMxVVR17IKSl9pICE6qpYfaCRHGOgdZJEEqK/anSgIiYFvJLu7HUQx+NS+r7R49H/SQfXmPQekZfxAntXpcbn27iEVuyNz7FI3EAhdy66bl1kjPQBDwxmWUHtXpXSJdASJWPZG1HDMFKk7hIdmm76eoWXoIk2GKwqO0uljJIQ2z7I17GMjKX+0HTRmBUa7SBIB0JHpG6I8h7Y6QWIO7+NFFhuyREwbQkEMRsBb9IJAm/gIiESCMMe0+lBq+gZlVT2QGYINNxAO3cNdNy6yxt8ftrxIZJge1ukx4lUKxEhPZO1HDMEKn7t5Cabjp6gKfH8sJ2KwgdqdKl5iCE+2Nex/Iys/2qdNGZSASQQI1/x/DGaRo4RH2t0nKZWR/HrEI+x3MiNJqqsNVdgQpJB0mb+AFKwMFkcdsdIlAxYqqq47JKSsxBICk6qpYfQEiOIfH/75kEqr/K/SQIjIZg8m7slRCnjXfq5UCPR/0kH15T8fSI/J4wx7X6RG59u5lQHsgF9i6FtAdu5ddNw1kjb4+aNChkmB7X6QHiUAEiQ/yXtSQMwXadG8hCab/p6hK/Hxm9wxWBV7V6SZ5SGCfZGvZJcqznap00ZgQNSCBOf8flKwsFkYdtdHsisWKqFcdlFJixUkBSxKqWH0BPqONfj2N8yCVVPbHR4IjILh5N3ZYEKCNd5L7QI9H/SQfUUS/Hr+5KvmRT2z0gNsZBcSSa9kgQoIxv1faPHo/wCkg+jN/j2BGX8Ydu2ekRucjcyop7J1fYNN2moXcuum4ayRn49aPChklB7Z6NAiUBSRI38mbUcMwUqSG8h2abvp6iK/HknzsywL/K/SBeXRwmqRjsvyMpc7QdNCwIGpBAmb/HpdsDBZHHbnRbJuZiqqjjs0pKzaEgKW1VSw+0EhIx8dzvmQSxqe2+jBpGQzmSTXswCFBGN+r7QI9H/SQfXlPx5/t+Txhj230au59u4qo/k0M+xSN2moXcuum4aujfHbRoY/JLr250UPEqjUiRv5N2o4ZgpUkN5Ds03/AE9QlfjqSbDMIVHbPRheUhgmscY7NMjKZDtB00ZgQCSpAmYfHYFIGCyOO3uh2TczFFVHHZ+yVn2kgKSSqlh9oJEUY+Omryp5EU9udEppEVLeSUt2eRCnjXfq+0CP7/0kH15j8dNIzIYwx7c6JG59NzBAez9z7ARuIBC7l103DWWM/HLR4l8kobt7obSFAoJEjfyeFRwzBCp0byHZpv8Ap6h2/HBj5yywqO3eiS8pDBNUjHZ+9lMh2ggaMwIGpUgTMfjipWFgsjL3D0K0e5m2KqOO0ikrOVJAUtqqlh9oJEUQ+N53yp5VB7h6FBERVn8khPaAEKBFL6vtAj0f9JDehN/jeBHuMW5u3uhfufTeyoH7S3MYxpuIBC6rrpuGssZ+NujxIZJQe4egwIlABIkb+U9I3BYLtJDbzs03fT1EV+Njf8wxWFR270GzyaME+xB2mZGDSfaDpozBgNSpAmYfG1SsDBZHHcvQDRhmbYqo47U2Ss5UkbC2qqWH2qSIo1+NP9ySPyqp7k6AXSIgvvk3dqgQoI0LkvtAj0f9JB9CUfGrSISeJXbuToAbpAAzKo/lTc+xdN2gO3cuum5dZI/8Z9HhQyTA9y/H4CJQBqJGPau1HDME2khhIQmm46eotPjMT7klYVHcnQBkl0YJqkY7UMhBk1UHTRmBA1KkCZh8Z1IgISRx3R8e2TczlVVJB2zskZtCQFLEqCR9oJEcX+Mh3zR+RVPc/wAfAREQW8kmvbAEKeMb9X2jx6P+kg+jKPjFpGZfGGPdHx6G59m4qoPbO5yqkbtAQu4a6bl1kQ/GHR4o/JKD3V8eAIlUAkSMe2dqOCwQqSG8hCab/t9RFfi+W9wxEKjuj49GSUqRHqkf8s72QyHaDpozAgakECU/4vrtgYLI692/HVk3M21VSQduFJWbaSAhOqqWH2gkQonxcO6SPyop7r+OgIjIZt8pbt0CGPxjfq20ePR/0kH15h8XCYzJ49x7u+Oi7pNA5CBu3dzbARu0+i7hrpuXWRP8WdGhjMkwPeHxxXxABWPkY9wbUkBYIUP3eQhNN/09REfFZv8AmGKwqO7vjkzyEMF1SNe3vIymQ7QdNGYEDUg6TSD4rgrAQsjjvP42mPUsUVUcdw7JWYgkbSdVUsPoCRHEvxS1kmj8qA96fG4HxkF/JKW7hCwp4xv1faBHo+u0g+jOPiltjMnjVj3p8bBufTcQgbuPc5UabtPou4a6bhrJGfiiHMKGSVZO9vjV/ZUBSQ7HuQKjhmC7T93kITTd9PUTN8UWVp2KwoO8fjc7yEME1jRO4i7qZDtB0AZgQNSCBM3+KqlYGCyOvenxtZNzMVVUcdxFJWbaSApYlVLD7QSIox8VfvmjEqKe8fjeCIyC/kk17gAhTxjfq+0ePR/0kH15v8VtIy/jDHvH43jc+3cVQHuDc+wEbtAQu5ddNw1kjPxVIaGMySg94/G4CJQASJG/mDajhmClSQ3kOzTd9PUJHxVJNglYVHd3xxLykME1SMdvmRlMh2ggaFgQCSCBM3+K4KwMFkde8vjeybmYqqo69wlJWbaSApOqqWH2gkRofiufJKglRT3f8cRpEVLeSQnt8CJPEN+rbQI9H/SQfXmPxZ0j3+MMe7/jkNz7dxVAe3wz7ARu0BC7l103DWWNvivtaKPySg94/HDSNQASJD/LwVHDMEKkhvIQmm/6eotPiwxM7FYVHdvxzLykME1SMdvF2UudoOmjMCBqQQJWPxbG2BgJHHePxwMe4uVVUde3ykrPoSApbVVLD6AkRRj4snfKglRT3f8AHIaR6FvJJr28BCnjXfq+0CPR/wBJB9eb/FvSPf4wx7u+OY3Pt3FUB7dDPsBG7QELuXXTcNZIz8WtGhjMkoPd3xyHiUAEiRv5f2o4ZgpU6N5Ds03fT1ER8W2JsMVhVe6/jsXlKsE1RB25vZTIdoOmjMCASQQJm/xdBWBgsjju/wCOTJuZiiqjjt4pKzEEgKWJVSw+0EiKMfFr75kEqKe7fjoNIyC3kk17c0hTxjfq+0ePR/0kH15R8XdI/J4wzd2/HUbn27iqL/Le59ikbiNQu5ddNw1kjPxa0eKMySg93fHMCJQASJG/l7ajhmClSQ3kOzTd9PUWnxaYmdisKjuz46l5SGCapGO3C7KXO0HTRmBA1IIEp/xcUrAwWRx3h8cim5mKKqSDt4pKzEEgKTqqlh9oJEcQ+LX9yWMSqp7u+OY0jILiSQnt0CFPGu/V9oEf3/pIPry/4uaR+TxBj3b8dBufbuKqD27ufYpG7QELuXXTcNZIz8WtGhjMkoPd3xzAiUAEiQnt7ajhmCFSQ3kOzTf9vqEj4tMfcMVhUd2fHUvKQwTVIx24XZTJ9oOmjMCBqQQJmHxcBSBgsjju/wCOTJuZiiqki9ulJWYqSAhOqqWH2qSI4x8WvvljEqA93fHMaRlS/kk3dujwp413kvtAj0f9JB9eb/FvSLyeIMe7fjoNz7dxCg9uhm2KRuIBC7l103DWSM/FrR4YzJMD3d8cwIlAUkSN/L21HDMF2khvIQmm/wCnqEj4tE+disKjuz46tJIQwT7Ix255GUudoOmjMCBqVIEx/wAXVKwMFkde7/jm0YZmKKqSDt0pKz7SQEJ1VSw+0EiOIfFs75YxKinu346KRGVL+STXt3SFPGN+rkDx/f8ApIPrzH4u6R7/ABhj3b8dRufbuKoD24GfYCN2gIXcuum4ayRn4t6NDGZJQe7vjmBEoAJEhPbu1HDMEKkhvIdmm/7fURHxaYmdisKjuv47F5SGCfZGO3C7KXO0HTRmBA1IIErf4uqVgIWRx3f8c2TcWKKqOO3ikrMVJAQtqqlh9oJEUQ+LR3zRiVFPdvx0GkZBbySE9ugQp4136vtAj0f9JB9eU/FzSPyeMMe7fjqNz7dxVR/LoZ9qkbtNdu5ddNw1kRvi3o8MZklB7u+OgEShVJ8hPb21JAxClSQ3k0TTf9PSTj4tvDatO3jli7q+OrPZkMmjSSQDtwPOZJHI36bmdSAxKsPVg/4sRvHXYK80fenxuMRYtsXxOe4B5S2hIA1JVSw+0EiGJviqUaZBKqnvH43/AGx6Fg8mvcBEKeMbyX0Hj+/9JB9eST4vFY9/iDHu347aF9u4hB/LZZyqkbtAQu4a6bhq8cnxhKNCjSTA91fHjSJQATvYdslY5AWCbT93kITTf9PUJPxkYmZisKjuX4/M8pDBPsjXtYyMrSHaDpozAgfVSBMx+M6hYCFkcd0fHxo9zMUVUde1ykrMVJAUtqqlh9oJEUQ+Mx3zRiVFPc3x+UiMgt5JCe1wIU8Y36vtAj0f9JB9PP8A40qIkbaWbuf4+IWb7dViR+11eZkDgsEDFQQToCPUUY+Mx3yxiVFPcvx+BEZBbySa9rAQp4xv1faPHo/6SD68x+NGke/xhj3L8fxufbuKoD2sGfYCN2gIXcuum4avG3xo0eGMySj+Zfj8PEAqkiRj2ttSQMwUqSG8h2abvt9Q6/Gpv75KwqO4ehGklKsI9URe1DIymQ7QdNCwIBJUgSu/xvQLAwWR17j6DZNxYqqq69plJWfaSAhJKqWH2qSI4z8bzvmQSIv8wdCjSIgt5JNe0dIVEa79X2jx6P8ApIPoy/456R7/ABBj290ONz7dxVQe0Az7VI3aAhdy66bl1kjPxx0eKMySg9v9DARKACRI38o7UcMwUqSG8h2abvp6mb/HNStdgsrr3B0KyBixVVR17RKSs+hICFiVUsPtBIjj/wAcNHljEqKe3+hlIiIZhJJr2iBDH4136vtAj+/9JB9eb/HPSPyeIMe3uhxufbuYKD2iGfYNN2gIXcuum4ayIfjho8KGSYHt/oYCJQAT5T/KO2NwzBNp0byEJpv+nqEj44sfOxWFV7d6ILyEME+yMdnlyrOdoOmjMCBqQQH1+OsekcnjbTuLoR9WO7TYU7SJlj/tn701X6j6/cNZI2+OBDwxmWYHt/ocCJAASJD/ACiAjhmClSQwkOzTd9PUJHxwJM7FYVHbvRLPKQwT7Ix2gZGUv9oOmjMCASQdJT/jmpWBgsjr3D0KybmYqqpIvaJSVm0JAQnVVLD6AkXqj/GqNr81nFX68p7b6HNqLHVquYjuRp//AE/ekVhrUEjJ/paOJZP0Lr/2Kdf9n+7/AHf6wf8Ab/3/APd6sW2qUsntpZzH05sfgxnqkOFt9V4HNYitWzmT6a5Ry+alNX5Ma1SplLiTQUo4aKw5TxvDP2hx6RKWNuDN1MDRXIcJuyQWcDjZMryaG1HgOKcej41krz4riNbJ1ca1SkM5ZgjZZWre7p3uA8eOQ9jar5zh+Yuz4nB9R43P43Gcwv8AXWPx9+nkMVyi9zjDXahMUy5Vsb7zIe4gW4iIoIxk3KW5fyTHZfgPBLudg5RR5EvEa3BqOF4LnexczxvP4vNy37nI4L1GldmkKVYGmjmruLdqerHLyOGfBccgrT8irtZTL3mmwiyZPB8jlwEaUsz132caMPCKmPyt0ZCxYqxxxSN76SeFDpQxNDEUqiY7N5HjkNyIQHKe14nTpihSy8uQ6643yKbwcd5HjIqkiZbI0padeNmaxN/zb4G7Szov5e+MoM3hPxlqp+3va2kixmmTmZq2V/K1mMv9kDwbdjakj1wa3NLPDQnhkp4+7kDJUxWMzVnL901bM2Hvz896/wAdBlb2AtWY7dh8jDLTMdERR2JbMb0+EwtHQz+OhpZCfOVr1/j3IcZhYOT9lU6eMguNNlO2MRavWpbNWmYcbHj7MaWYG95Vp/2KFz8dZyman5JlrPL7U2bzr8iy9vi3A6dji2HzlLI2cXx6/fgnu3c808ElJblbH4+KWSGCCCSaXiL4jGYTPPQ6fgtXoHwNua7anv8AYVnHZHGVorFHsTk81KVbmdpZZsbhlrOtku0qRT0hR5RUuwURnWwuToUY7eAxaZjmbW6PbVvK596rdLGe9eeCtDBM+LpS4+zEUWWxk5ClSc4drU0aXuRrLxnD5jEcs4/HWgw2X5HZyNzj3HZuJcao47J3aPMac96Ww746eSxar0ooEo0415TPjuRXLc0P8iXadjH9z8VjuVsPkub9OLi58XysQyY/g2CurWmWvjravNsWWFXLSLpZlkknllfj3BHlms5etn7MsjcD40zyz56kqVM3NIx3PbiCx2WJkUBWA9Uzi8vBy/jmLymHtjJ2eOTx1spXiNO5ehscYyd+lJagjlMkDV5bMC2VT/iIr7hVuDmGLTGXK7ZzKyVunujMjRrWIYrEPGcHlcJi87PbocvtCW29ZJDMzV7cjEVkp5F4s3Fg+Q5zimU5HnJpsMMTJhuOcUq+XLQwYY5Hilvl+P8A3F7f8zM9fFQXLHtHrqnhykMs0KYnKyeDluZhhZstYuQcJyeP5DVyMGUx89++mJ5J2BhK3MMZtSVLFgWVuzvHZtVPLFLJk+VW5uPw5DG8OnmuYqnleE8crYXJXrnIMFx4yXBZ+P3F8T4L8DRSWhjMy9ixFXVIJigWzDj7bZmDF08fy/jWR5XLmuO8EbFeeOzfOChgzvM+c1LFaxWx1TIyx2MVjEyMJnn/AOOI4NMPL76C9LkMW9qOXH8a6+49ipMfHkLlOvPTfrnknIcNkpmv1bcUzyGKeMwqh3KFCf8AWW3cN3FXLd+fMTTWspxbiuYsxryB70uaqVLGVwtybH4vIy5Ky0lOuY6pazMfH/dkLX7te3BQfKQzwXauLx+MxeLmSfC5Hj0r/hcdTq4eGf8AD5e1EsiQLIhsSOrCR2YrSiv0aoShhceMhQ4/x7HZ4UuOy4ubBwtySjioOQt+OlwVMxubW/Wum4nb6rI2Tmikq5Vs776skNPK2s2bNi3DmMrmKsUOVzOUpz2pjWsW5p5qolkWFkDuDkseHo1quXuyX8gtDD4jHSWWeWCz7A2KFGtZjwcdqvFMmOR0oRTRJIkKuiMJ0zFqG+0+Uu5hrEuOxgvJbyFm3duQ1shHTjvUsZPeyE9g0YZEoixK8oiEjFjr/wC/TX/Z/r9QRY7IwwxVcVYwixzYrD3opcLatZq5aw91L2PsrfxVu5yCzLNXn8kMsvidlLV65iwubyGYEuQ49ebJYUwY3FU6VHIy5Q5mzkI8XRo1sZLdvZX+/ZlkhaSywHlZwoAOP/dub/F/hDx38R72Q4cYX8N+B9n+H+uMGmMG3yeHy+X+9u839z1ElazSlhr4STjqVclhMJmaRw0mfflDU5aOYx12rYAz0hsCSRHlRhorhVAF/ObuOnLZOCaDIXZOEcIkkspbGRjvNKj8cMXnykWUnjtyhBLbjbZMzqiKpwNvNTxYDzyWfwGMr0sJghJK1KRwMNhq1DG+FbNFLAj8YjW00s4AlmmeT8QJtuP99+RlrpHFGLF0V/awT25I0Se2akDOsCysyVzNN4gpnmMkmTykkElySCjWLVqNHG1krY6jWxtCCvRxtanQqwVqFSKNEiiRQq/6NSSa9qJYWlrTw2I0s1q12s8kMiyIlincinp3IWZQGilR4pF1V1Kkj1DYjzU1aOCGxUixVKCnQ47HSuyVZsjjV4xQr1+O/isvLSje7U9r7e667p0kZmJibDDCRzQXkyVe7e4rxbMZKrdi8PhlqZPM4e/kKoryVldFjlVY31dQGYsasr0uIBqszTxLF17wCujSNVnqlbMUHGI4rkAissRHMskQlCSBfJHGyj/kOFaD/wDzPrc/7f8AV+1Trp9dfr/q+p9XaVD8XLUyE9Gxcq5fAce5BXexjYr8dOwsOfxeTirTV4snOoeIISsrA6jT1TmybUQcfSGOpQY7EYjB06tP3lzIGGGhhaOPpIGu5CaRm8e9nkJYn/y/1/Wnr+v61/8AMf1/r/0ev6/3/wDs/wCoP1/0enyF3L4rjuKSdqkWUzQyjVruRjSOeXH0a+FxeYyduetXlWSd0rmCsskQmkjeeustufJZjC4DHVLs+KXL5Gxbv4y9lK2xrFHEy8XoZ+fK+3rssslivHLUijliLyobFYTVls5PCY3J5LN2eP4PDW5cnLd5Bk4KeDu1kxd7GYrI8fNPKQ8kp+0tz3oak6ziUS+E+X1xt8csN9uU5SfBYyKCVYZBnK9ijBJirJurVhinEeXpzCUM1Yx2lBl8iTpFhUr8t4vNkOSic8ZxITlKXs7sz+T43RNWeXisWLpfl8nipFgN6xV2IyvP4NW24vJ28hi8XhclixlXzV9r/scckmczuAqUbiUcddv2MpfvcestDWpwWpWrI07BYorDw5mYT0pqWJwlfkUWQgknenmsPaz2L47Xu4eQ1Ukm1yOVVXjmWCWs8E0M6xWIXhFB5eR8dwxyfHcnyilDlByJ3lxWFs8ogzD64jj2Vjjmx8fFLM7IzKZInjEXkkLor42LK4xIFmsqc1YmsVcT7SoJpZsiBbqQZJoHrwtJHAtY3Z/tijgadliIH5nCjCfhDyMcpP5k4b8N+ZPGfee1GG/c3/7LrQMf47y+X+7t9t/f9RV5Mng602UvV8bxdJpck37wu3Mdh8tQgwxrYiaPH+8x/JMbIjZY4xF98iyFGjsLDDkVyOKit3MXlc5jMBM2Q/K5TB4RsmuVyteeLHS4OCCkMJeJitXa9qQU38cT74PLyPk9NYJMbxWbCQ5gGYJZiTkE9yrRtRQuAk8SW6axOFYyq80ZCMgkZONUBjJr+R5Zx2tynD4vERTZTISYq7JkRWZq1OKRjNJVxrWSkZkMcDLvKOHRL1erx7N2J8Xeq4zJwwYm/NLjsleunG0sffjjgZqd65kVMEUUgWSSYFFBb6erOegxGUlwlKZatzMxY+5JiqtpjAEgsZBIzTgnZrcICM4b+6n0O5QbVvDUZ8nLVymLxC4zH1rdzKWbOVx+fycL1KVStM00Fenxuy0x1DINpCldzLyhmwWUqS8OxdfL52rdxmSgt1K1qxTjhR65ploZmp22u6TeJTRrzTBiqfXDefj+bh/cXi/b/mxV+L875/beE4bdXAyYm95Dt8G/d5k013rrPFieL8hyclaGhZsx47CZK69etla4uYyxOlatK0UORqHywO2izR/chI+vq1noMPlJsHRnWrezMWPty4qpYdq4WvayCRGpDOxtxAIzhiZU+n3DWQZynPQmWHjVkQ2MfmIHaLk2Ku5SsrTWcbBUisUWx89azHLIkvu4ZUiWVYZ2jGHPD+U/lzSOT/FHAZb8j+OE/tfyHsPaCz7IWh4/Ls8fk+3XX6ereIxvH83kMvj/AHHv8VSxN65kqXtZ0rWhbowV5bNY1rTrHJvQbJCFbQkD1g7k7QPDn8VNl6Qhd2eOtFm8xgXSyGjRY5jcwkrKFMi+JlOoOqr7q3mMHjcg9L8pU47kLF2vmruMNc24r0bGi2FqG5TjaatXt3K9y5EI2gilFir5sN7jj+bg/cXi/b/nxV+EZz3ArGE4YyQKMoJveRbfBv3GVNNd49Yu3LxbkcdTNzUq2GtPhMklbL2ckhkx1fGTtW8d+xfjG6FIi7Sr9UBHqpmcjx/N4/E3xXNHK3sTfq466LddrdQ1L08EdayLNVGkj2Od8YLDUAn1RntcfzdaDK0bWSxk1jE3oYcljqNMZG7kKEktdUuUqePInlljLRxwnexCnX1mPb8fzk37d8v7gMOKvy/gjD7nzfmNtc/jDCac27z+Pb4n1/S2hxWGxOVzl78Xj8xJVxGIy9qzHRyVGhdjmao+PjueCJclCnn8ZryMytE8sTxyPg7k7wNDn8XNl6QhZ2eOtDm8xgmSyGijRJjcwkrAIXUxMp3A6qqrFyPj1vMNx2ryg8bgXkS5ZMTY47Fy2R2tXOPVePmelx2RrMqC8SwRkj8ku1Gw/uOP5uA8i8X7fEuKvxfnfP7fw/hi8CjKeb3kW3wb9xlTTXePWZGXxd7Az4TC1uQWKeco38XcsY21nsZx2KSnXtVY2m3ZHKr9TsjKRyaNuUKbFS7BPUt1JpqtqrYilgs1bMDtFPXngkVJIZoZUKsrBWRgdRrqPV7k0KQfjKM0scqtMq2Hiqz4apfswxabDDj7nJcZE4ZlkZ7yGJJEistDhLkzwvDyDFzZamkO9pIq0WbzGCdLAeNESY28JKwCF18bLqwO5Vy+BuSQyXMJlchibUlV3evJYx1uWnPJXeWKKRoGlhYruRG0I1A/0Dj7pyPjsGV5VAZePcdsLyEZbJSNnMlx6rVSzBx6xx6nPkMrjJI4jYvQxoGV5XjUlhi8pbyGLxeFyWLGVfM32v8AscckmbzmAqUbiUcbdv2MpfvcestDWpwWpWrI07BYobDw5mYT0pqWJwlfkUWQgknenmsPaz2L47Xu4eQ1Ukm1yOVVXjmWCWs8E0M6xWIXhGPebkfHcMcpx3Jcoow5Qcjd5MVhbXJ4cu+7Eceyscc2Pj4nZmKMyl4njEW+QsiTZC3ynjuIxRy17EYzJ34uTy1c5axaU5774+viuNZPJV4a9bKVJNbkFRmWyoRWdZVjmwPv8XRlq185Zt5Gxaa7ia1bj2MyGXyVhbeBgy7XYUp4yRo3qpYE/wBNmqsG9WcnDy3i7cfqU7lufkmzla4+Oxj8lxzG2sb7JuLryJ7sM/LaD7lpGu0c/wBspZJFTKTjlHHYcFip8ZTl5NLFyj8VPkMtWt2aNCtUi45LyQzyw4y1q70I4FNVwZBvh8matWeVcVx2Lw13C0fys8uevUsjJn6eUyOLagmCwGXuoJaOJmeVLUNaas2kUyRzB40wyV+WcVmyHJRY/bOKCcrS5nSmeyfHKPtppeLx4uicvlMXIsHvLNTYjq0/hBbbhMOk9I/nqWOydXK+Wf8ADV8bdq+7vZC5fFZvDS40IrMeUmVXjpS0bKsT4XPrHZq9yrjuPky2Lu5XG4WxFyaXK24KeUyuHWNXo8auYaCe3fw0yRCW3GumhkZFOvqOSHM4TJ5NsJjOR2uPY45n8zTw2TwtTkC3HF7DUsZe9ljL0ctmOnatSwRb5mXwwzyRf+HqGkLlKi9jyLFYyNj2lL3Aid61ee4ytBTFywqwrNMY60TyB5pIog8iVp908toQT2MxUu0lxDYKOpxHgfJ7IyEl24Fimjt83XHJFII5JrUUcaK01hIBjuUVs/7ZMhSvW0p/tfkuSMctJ+aQ+H3mHo34Zmkm4tXX7A06+82mLzezhvQcax0s/IZ2xdzN2LUWLt4eti8ZW5JmMF+RyUmUaKLH4tIcbHYmvTPHRjWfVZ5IAlmUWq2O5Fdirw3JMol/iuYwNmFMdGkuQzOOpZBPd5LiESyqPyDR15KzMq3a1MzVhY45kshzPi2Gp5zCzcgsRz2/yeXrY2lJyizl5cfh8C2XtXji8Dxd7MrWPYJ7qUUdwsxuokw9jMYPM2YBcjuPgrFueKjcoZPIYi7jbi5ChjbMF6CzjnbaYyrwSRyo7JIrHlnFbENEZDhWEy3I81MLqPTXDYyhFkRdqyBDPY/IQWoBBGIhKHsKJViCyGOrnp8RlYsHdnatTzM2PtR4m3ZVrAavWyLxCnPOjVJgUR2bWJvp9reoMdd4/m6mRs3lxtahaxV+C5YyTRULC4+GpLAk0t5oMtVcQqpkKWYm00kQmbGZfH3cXka/j9xQyNSxRuQeaNLEPmrWkimi8sEqsu4AMjAj6H1zPHSz0jPwU2/zDRyTNHY9pybGcVlGOLVkabXI5SN1Egh/tBj9GAQthspi7mAyC4PNZ9a+fpX8VJJjsHiMpmbU0MViqJZDNBiJo4iECNMNrMo3MtS3PUnxkV/K8VxlFL2Py4tXk5hHmmw+Vx1OpjbdzJ4uRMDON1dJpZGKrDHKWIUci/b+b/b5+v5z8Tf/AA+gtCjr+TFf2RHvv7P/ABCfN9v6vp6huX8PlKVSzBQtV7duharVpq2WS7JirEU80SRSQZKPHWGruCVmWCQoWCMQlPPYjKYW5LAlqOplsfbx1l6zySxJYSC5FDK0DywuoYDaWRhrqD/0p/Wn0+v1/wBv09Q8c5HYymOp47KZLNY3JYTG1MzZM+XqYijkqF3G3sxg4ngliwdaSCeOyjQMkivFP5leueP5ds1hsRj83mM5g7WPq0OR5KL8zBiaVvF5avZu8WrXgK+EqtHahet45I5Q1eRbCmrj6WGrQ423Rydx4LNnF4TLXsdiqnFeD8W43Li89doNksbyCrW4o0lm3RTHlpyksQQhEh5bQMt5GzuEhpUPbPtigyS5jHee5P8A3o2h8vF5MpRMiLJIYb8sBBinl9YDjH5LkVfjI4fnOLcnxlO08dWazmuQcmykeZx9GO9BFbmx8eUpO6PJUe8taSlLItWVy2D4jlFylTGVcVTXIZOhSqXb9XNYnk3P8tj5qlC1kaMGUxVjE83kgnjeenMtjbKrskJis8jt4NZ8Fcs8Qq8XwuY49jcVxPIWrEPL8Jn7eZy9bjJx1HHT36NOzAEqiUxQeCu72NklmTj+RzVrN5XIY/rvmnEclfvze+uWsjyCPn8GKmFq3dknsUakPKKqO0jLJGsbhEYKm+ivI5cnBgxOGyT4SvUtZVqyAsYqMV+zUprPM6hBJIxWIMX2SbRE2ZpC/mm45nOLVuOxSQ8bpUZeLQU+T4vk8FPD8ebl+RhyNI2MOqSST5SGzNPdmtTPNPvaxxq7PHm4v2DnMTmOPxxUaM55MMLxzg3H4K+ZkfL1/wBre8/YEMzvAMt4ffOoEnt1axFh5J83GK+EzuJs8VrwQNxTkuSy75lqHLs3ZOUh8ebwH5WuYFbHXJCcPW22ItVNbIY7IwXpYMzm8T+RamsDzR8cl4zzzivJPZGazAq5sY7mIej5FaDzoTL9q7He/k63tp8xxbN4bOWU49heSUqOSyvaGV7FaxjeN8itvj8xSMc8VUJbnheBpGlUs0KCXA8nyOXnxlHhfMBk4rYy/A+M5DJYpuP8L47klk43Sv43I4zF4/DcWWSPHYnHZY5CCd8asvkjNqT8baq3ZL0fF+ZcajjXEcfv6T8iocjFPMQckyoscgw9GObNpWmw9D2tY6SXfNJLNPVn5HjbsN2WfMec12qxwPGnk6/7J4ooneaxC6aZDmNVjtV9IY5T9WVFkbC2xlIJR1rZ4ok9elUso+bXtGbn1IssmSqsuLsUgleSYFpYZGYrBIqjfhs6Be9s/YmC55mcVU4lw3B+xGKv3bjUUy+K0zHNrkMWVmiguXpqH6WeSFnsbq/I8beku28vnOUcd5A9+d/P5xi8fzGvkZL1qac2pb9u1yGJw2j79HLMpA38ot+xygbMV+y6FCvPhuN3bEI5nJyy3jcr+4cg9zOYWCA8gjq2MPjWr1Nwlu+eWSaetYw+QoVcret0Mp0rm7FS5BUx1Y2eseL5XAZSjDehu5KR4chJLXkrzmujaPIHiBjXy5biGJlzmSgu0srLXyeQxdDDzDJZfkPWt+SpNjq2dzUcdGnj+AuyTiw7yTWghhRYzI917NW6YZsH15WUTYjj+ehtWuF8Pg49kMa2I5IuSwNAZa4XetmWrW7uOgQqlZ1tWI/UGSxcF6CGL9yarfjgil/+8c/5lyqsQkFm2g8eP5DCkn36iZXA3KFdkyeVbNxchqYXGYOPGY6rROHvfg8LUwOGycmXs3Tdxnho067Wqnsrfupa8hjsV1sqtROQQR5uf8z2hxrsnkFOahQr/hfwd3PXJsNh7KZaz+4fMOSyotmePGf/AEyExDzMIeP5HMDN5Czj8HiqWVtbYLNq7k6neEHZt655bWQSSyLOIjdPJKwke62jAITL6sUrhylvK2+BTcatXrXjsGzmp+3W7BmvWLUtt7M0M+NYq8rBpntnQqUJk9ZDM0p8rUs5ibk+ZetjOMcQ40+DzeV4ly3DYbXP4UrnuXz4zI8q1XI2paUsccUkvt5Zpx4MXQylrkWNuca5Fk+TYmfj0dU2MjZylTjlc1PytnI1ZeMT05OMRNFdjr5JlawW8H9kLNeexVumCXDdewKs2I4/nobNrhnEK/Hb+NfEckTJYGh+VtF2rZlq1y7j4UKpWdbViL1BkcZDeggi/cm5L8UEcp/M8/5jyqttWCzbX+3j+RQpJq30mRwNyhXYLV5Tz2WL9hVeHtwaeFavERZfgsfDb1xLicsuKYILbPkYl/FBpZlCHxsxmTEWRSyZMHZfDee3SuF43UsQY7Cy5572EkytQpneYZOp+TQRZXK2WnyJZ3kiqyeR7IoS4+7evR3szZ8cTV6sEsOTzfU2Q8cOS8kt3FXvY8GuqlyvEbNKzJXmgYSL5IsxkauW3+xzeNx2OoW60FTI5PG34c/PJlmrR5G37X2BxNdJoo2sRpLcRTL9FMmJ46/HIJ+MrjFxWbvynJ/uKSPOx0rPMLtWhX5BW4ffylfKRbsTZu0ZZI0oY9pSXqxlONQW8lyKlm+OccyOIWnX47jb2Ku2pOTcm5BTZ8tJyqhcrQSrm0ikPspWiKsyrJqAc7yPA5LkU1vP8hyeXkxuX47jcbWp1spbuXnjTI0+V5mW1PWmlSMA1ohIpZty6BGwPF/yPIq/GP2fneMcoxlO28daa1muQcmycWZoUo70EVyfHxZOlI6M9Nry1pKUsi1ZpC+D4jlFylTGVsVTXIZOhSp3r9XM4nk3YGWx81SjYyVGDKYqxiebyQTRvPTmWxtlV2SExWeR28Gs+CuWeIVeL4XMcexuK4nkLViHl+Ez9vM5etxk46jjp79GnZgCVRKYoPBXd7GySzJx/I5qzm8tkcd13zXiOSv35vf3LWR5BFz+DFTLat3ZJ56NSHlFRHaRlkjWJwiMFQPbiuZ3kWGazPG1ynSwGD5jx7PVYo/+ShzHGc/lcNjHmxFhpZYXse+UvMrRpXkh8k2S5FjKt7B1npc0hwdTG3ZbFzCy57j2cxWCSLJzS1bMn4ezkoS1nVZisRcKX0Uy5HOck5tjeS2eELw6xyXDn8rko/x3LMLncXkar2OSYF6/usTUlo2oY3j3SR+6eSxNcsbMxk7fNOezS3oKcA5HPi8dlcjmaKQQJfwXKeNZPlEtPK4q20MSBZsnYrmvVEctaUTAVuXV8XmeYcHXN5Tid6tkeH42pHYuDA4rP0cwbeNq8q45WwsPIclllvrRrTz1KTA1490ccb+qGA/cHMLfGJ+Oco49yhZrL1rOUs5/k/KsxFyX8cmbsRWMpDHlaU9iJrcTXGglpSzmtLJJJcwrQ78uvnxeHyni3T0ONZtktckx8d42VtVTJax6RV4o09uauWyyyqxtAjD4CDmPPMHLjOO5rEXePYqlHJxfN2r+d5Hlle7ZTmONYQXamYiq2C+PmKpGftkXRPUeexUmbschl4Vi+HTY7IYujTw1DTgVTg2YyKZOvnL13LeSnHOa0Jq0ts06Su7LC0E6irLNNGYKzu1iulaRbL1oWuRCNLNsNBBcMiRSFgZY1WRkjZjGtP8AJ/hPZf8AM+b9x/uP8MP+Un8XvP2l/wDkP/G2+P2//wA3bv8A7W/1+Hv5/F8axK5RhkY87gMm9bIUk4hwjieL5Dg8Jw2jbqYrkWJbiVi5HVWxTr1hcWks9mpJaVq1TP3uHw5yvlLUtdZ+O0pcg2Kt799bNZLIdI9hLNPQt1RLUMM8hkgvMkrRivFF6rZWjynhGO4zDRmpVHh4bYl9tHVpRY6LIZnjrdVVsDl83lLd2XIvAKhg/tPWS3V2VJhOKvNeA2rwxXGqrVqnAJsRbK8e4riKtyvjcvF1jXyXmy+eputVDdoVKVKJITrFOY6nCMFyLkvFpuK43i2Ix3J8Pcx3OaCb5+Y5rM5CHCnh+Fx8smaqYdaPvTPIKGQvxwTytclrxzxVpI81i8vTr4qjSoRYSzzO1isLSpK8MOHoNzyOPOQwwlWm8YeSuhn0jKjWNc1spXVq5O72P4ta0C2FxucxvYw4duIyTxpdiy/aeT/JEbkWrDW8G50l8vGZace7J1/49xdjDSYTg+ApTQ8MucYeKTJdl2YpuQ2qORl4vFKYbCU4aTzKWsPDU2WOCYRctdzkeBvdgSSumU49mvaY3luGwmPpXIMbgM5nuNx0a+SityTYGfKTx5LwSm2Yq+QCLx04qGeCLCceOFnklxOCwcdqwnIeQ5T3tbEcbhr4nGwXIcrHIa8aFoJC8bS2WX3M3LMzcyXKMzgb2bpZjjXHcjlrBpVDjOxuOcso15q0lu7TxQjwuHlrK8Ec/haQKqmMsfVHE4uXN5TH06PZsjZG/i6OJuyZHnvCK/E61NcZBm8xAKWPmxsM0lg297pPIFhBiXyx5JU5RL77uDjfauToTCgMdixWnzs2bw+GqjIH3V5PyMKLkJGrfkI0QSV6vt1M3GQ8N5vw3XfOeJWikVfWTJcm/kP2E0G60N9KI8tr+Zm2yLsk2o+1d+RtflLpv8i7Rw/Yn4vC8y41dGKlWvyh85BgrvFsjyiDE3cdPkakceVtiCfIaxMlFBTk3Y2LES3pkri5YuyyYTF8Rx01237WHzVeI4PJZbC4697OjDFYtVpYFuxxQh66ywyWLX/UL/f/AF/v/r/V/p/9F/r+tfX9f+J/6A+h0/8AWR/36ev6/wDgB/2EP//Z' />");
				body = StringUtils.join(mail_body, "<br>");
			}
			ElsUser mailUser = new ElsUser();
			mailUser.setUserId(meta.getCustId());
			mailUser.setUserName(meta.getCustName());
			mailUser.setNewMailName(email);
			List<ElsUser> mailUserList = new ArrayList<ElsUser>();
			mailUserList.add(mailUser);
			logger.debug("emailClient[bf]["+email+"]"+meta.getCustId()+"-"+meta.getDupNo());
			emailClient.sendBillHunter(false, mailUserList, mailType, body);
			logger.debug("emailClient[af]["+email+"]"+meta.getCustId()+"-"+meta.getDupNo());
		}
	}
	private String pretty_numStr(BigDecimal r) {
		if(r==null){
			return "";
		}else{
			return r.stripTrailingZeros().toPlainString();
		}	
	}
	
	/**
	 * 傳送簡訊服務
	 * @param meta
	 */
	private void sendSMSs(C122M01A meta) throws CapMessageException {
		if(!Util.equals(meta.getNotifyCust(), "Y")){
			return;
		}
		//-----------
		C120S01A s01a = service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
		String cellPhone = Util.trim(s01a==null?"":s01a.getMTel());
		if (StringUtils.isBlank(cellPhone)) {
			return;
		}
		
		logger.debug("Step1.新增手機設定檔");
		// TODO 是否有額外的介面處理此Step
		SmsProfile smsProfile = new SmsProfile();
		smsProfile.setTypCd(TypCdEnum.DBU);
		smsProfile.setOwnBrId(meta.getOwnBrId());
		smsProfile.setCustId(meta.getCustId());
		smsProfile.setDupNo(meta.getDupNo());
		smsProfile.setCustName(meta.getCustName());
		smsProfile.setContactId(meta.getCustId());
		smsProfile.setContactName(meta.getCustName());
		smsProfile.setTel(cellPhone);
		
		logger.debug("Step2.覆核手機設定檔");
		//是否有額外的介面處理此Step
		smsProfile.setApproveTime(CapDate.getCurrentTimestamp());
		smsService.createPhoneProfile(smsProfile);
		
		logger.debug("Step3.單筆新增簡訊");
		//若上述2Step均在其他介面處理，則第一件事則是依據條件取出前面已覆核的手機設定檔
		//SmsProfile smsProfile = smsService.queryApprovedSmsProfiles(meta.getOwnBrId(), meta.getCustId(), meta.getDupNo()).get(0);
		SmsContent smsObj = new SmsContent(meta.getMainId(), smsProfile);
		//處理簡訊內文
		smsObj.setMsgContent("Hello!簡訊. sent form Lms Bank3.0. 2015/05/05");
		smsService.createSingleMessage(smsObj);
		
		logger.debug("Step4.傳送簡訊");
		smsService.sendMessages(meta.getMainId());
		
		logger.debug("Step5.查詢簡訊");
		//是否有另外的查詢介面
		List<SmsContent> smsObjList = smsService.queryMsgsByMainId(meta.getMainId());
		logger.debug("===================");
		for (SmsContent smsContent : smsObjList) {
			logger.debug("{}", smsContent);
		}
		logger.debug("===================");
	}

	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult test_email(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		C122M01A meta = service.getC122M01A_byMainId(Util.trim(params.getString(EloanConstants.MAIN_ID)));
		String resultStatus = Util.trim(params.getString("resultStatus"));
		if(meta!=null){
			sendEmails(meta, resultStatus);
		}
		return result;		
	}

	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult run_ploan_update_brNo(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		for (String c122m01a_mainId : Util.trim(params.getString("c122m01a_mainId_arr"))
				.split("\\|")) {
			C122M01A c122m01a = service.getC122M01A_byMainId(c122m01a_mainId);
			if(c122m01a==null){
				continue;
			}
			List<C122M01C> c122m01c_list = service.getC122M01C_byMainIdOrderBySeqDesc(c122m01a_mainId);
			if(c122m01c_list.size()>0){
				C122M01C c122m01c = c122m01c_list.get(0);			
				String ploanCaseId = Util.trim(c122m01a.getPloanCaseId());
				String newBrNo = Util.trim(c122m01c.getBrNoAft());
				if(Util.isNotEmpty(ploanCaseId) && Util.isNotEmpty(newBrNo) ){
					service.ploan_update_brNo(ploanCaseId, newBrNo);
				}
			}
		}	
		return result;		
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult ploan_discard_loan_byCaseId(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		for (String c122m01a_mainId : Util.trim(params.getString("c122m01a_mainId_arr"))
				.split("\\|")) {
			C122M01A c122m01a = service.getC122M01A_byMainId(c122m01a_mainId);
			if(c122m01a==null){
				throw new CapMessageException("mainId["+c122m01a_mainId+"]not_found", getClass());
			}
			if(true){
				String ploanCaseId = Util.trim(c122m01a.getPloanCaseId());
				if(Util.isNotEmpty(ploanCaseId)){
					JSONObject jsonObj = service.ploan_discard_loanCase(user.getUserId(), ploanCaseId);
					result.set("mainId["+c122m01a_mainId+"][ploanCaseId="+ploanCaseId+"]", new CapAjaxFormResult(jsonObj));
				}
			}			
		}	
		return result;		
	}
	
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult ploan_discard_loan_byCaseNo(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		/*
		 	在 testing 環境測試 /service/api/eloan/apply/discardLoan

			若輸入的 caseNo 是 從債務人 的 caseNo
			=> ｛從債務人 的 caseNo 變成 已作廢｝｛主借人的 caseNo 仍然有效｝
			
			若輸入的 caseNo 是 主借人 的 caseNo
			=> ｛從債務人、主借人 兩者都變成 已作廢｝ 
		 */
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		for (String c122m01a_mainId : Util.trim(params.getString("c122m01a_mainId_arr"))
				.split("\\|")) {
			C122M01A c122m01a = service.getC122M01A_byMainId(c122m01a_mainId);
			if(c122m01a==null){
				throw new CapMessageException("mainId["+c122m01a_mainId+"]not_found", getClass());
			}
			if(true){
				String ploanCaseNo = Util.trim(c122m01a.getPloanCaseNo());
				if(Util.isNotEmpty(ploanCaseNo)){
					JSONObject jsonObj = service.ploan_discard_loanCase(user.getUserId(), ploanCaseNo);
					result.set("mainId["+c122m01a_mainId+"][ploanCaseNo="+ploanCaseNo+"]", new CapAjaxFormResult(jsonObj));
				}
			}			
		}	
		return result;		
	}

	@DomainAuth(AuthType.Modify)
	public IResult prepare_ploan_discardLoan(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		String c122m01a_mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(c122m01a_mainId);
		if(c122m01a==null){
			throw new CapMessageException("mainId["+c122m01a_mainId+"]not_found", getClass());
		}
		String cfmMsg = "";
		String ploanCaseNo = Util.trim(c122m01a.getPloanCaseNo());
		String ploanCaseId = Util.trim(c122m01a.getPloanCaseId());
		if(Util.isNotEmpty(ploanCaseNo)){
			if(Util.equals(ClsConstants.C122M01A_StatFlag.已核貸, c122m01a.getStatFlag())){
				throw new CapMessageException(ploanCaseNo+"已核貸(不應執行作廢)", getClass());
			}
			//if(Util.equals(ClsConstants.C122M01A_IsClosed.X, c122m01a.isClosed())){  ==> isClosed() 回傳 boolean
			if(Util.equals(ClsConstants.C122M01A_StatFlag.已作廢, c122m01a.getStatFlag())){
				throw new CapMessageException(ploanCaseNo+"已作廢", getClass());
			}
		
			String incomType = Util.trim(c122m01a.getIncomType());
			if(Util.equals(incomType, UtilConstants.C122_IncomType.線下)){ //線下進件
				cfmMsg = "是否作廢 " + ploanCaseNo+" ？<br/>此動作無法回復，若誤{作廢}，需重新進行線下資料建檔"; 
			}else{
				if(Util.equals(ploanCaseNo, ploanCaseId)){
					//作廢 主借人
					cfmMsg = "是否作廢 " + ploanCaseNo+" ？<br/>此動作無法回復，若誤{作廢}，需再請客戶線上申請進件"; 
				}else{
					//作廢 從債務人
					cfmMsg = "是否只作廢 從債務人案件(" + ploanCaseNo+")？<br/>此動作無法回復，若誤{作廢}，需再請客戶線上申請進件";
				}
			}
			
			
		}		
		
		if(Util.isNotEmpty(cfmMsg)){
			result.set("cfmMsg", cfmMsg);
		}
		
		return result;		
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult run_ploan_discardLoan(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		String c122m01a_mainId = Util.trim(params.getString("mainId"));
		C122M01A c122m01a = service.getC122M01A_byMainId(c122m01a_mainId);
		if(c122m01a==null){
			throw new CapMessageException("mainId["+c122m01a_mainId+"]not_found", getClass());
		}
		String ploanCaseNo = Util.trim(c122m01a.getPloanCaseNo());
		String ploanCaseId = Util.trim(c122m01a.getPloanCaseId());
		if(Util.isNotEmpty(ploanCaseNo)){
			//if(Util.equals(ClsConstants.C122M01A_IsClosed.X, c122m01a.isClosed())){  ==> isClosed() 回傳 boolean
			if(Util.equals(ClsConstants.C122M01A_StatFlag.已作廢, c122m01a.getStatFlag())){
				throw new CapMessageException(ploanCaseNo+"已作廢", getClass());
			}
			String updater = user.getUserId();
			String incomType = Util.trim(c122m01a.getIncomType());
			if(Util.equals(incomType, UtilConstants.C122_IncomType.線下)){ //線下進件
				set_c122m01a_to_discard(c122m01a,  updater);
				clsService.save(c122m01a);
				clsService.addC122S01F(c122m01a,c122m01a.getDocStatus(),null,null);//增加作廢流程紀錄
			}else{ 
				if(Util.equals(ploanCaseNo, ploanCaseId)){
					//作廢 主借人 (底下的從債務人，也會一併作廢)
					if( !Util.equals(c122m01a.getApplyKind(), UtilConstants.C122_ApplyKind.I) &&
							!Util.equals(c122m01a.getApplyKind(), UtilConstants.C122_ApplyKind.J)){//非青創案件才連動PLOAN
						service.ploan_discard_loanCase(updater, ploanCaseId);
					}
					for(C122M01A model_c122m01a : service.findC122M01A_by_ploanCaseId(ploanCaseId)){
						set_c122m01a_to_discard(model_c122m01a,  updater);
						clsService.save(model_c122m01a);
						clsService.addC122S01F(model_c122m01a,model_c122m01a.getDocStatus(),null,null);//增加作廢流程紀錄
					}	
				}else{
					//作廢 從債務人
					if( !Util.equals(c122m01a.getApplyKind(), UtilConstants.C122_ApplyKind.I) &&
							!Util.equals(c122m01a.getApplyKind(), UtilConstants.C122_ApplyKind.J)){//非青創案件才連動PLOAN
						service.ploan_discard_loanCase(updater, ploanCaseNo);
					}
					set_c122m01a_to_discard(c122m01a,  updater);
					clsService.save(c122m01a);
					clsService.addC122S01F(c122m01a,c122m01a.getDocStatus(),null,null);//增加作廢流程紀錄
				}
			}
		}		
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, "已作廢 成功");
		return result;		
	}
	
	private void set_c122m01a_to_discard(C122M01A c122m01a, String updater){
		c122m01a.setIsClosed(ClsConstants.C122M01A_IsClosed.X);
		c122m01a.setStatFlag(ClsConstants.C122M01A_StatFlag.已作廢);
		c122m01a.setDocStatus(UtilConstants.C122_DocStatus.已作廢);
		c122m01a.setUpdater(updater);
		c122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		// J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW
		c122m01a.setIs_uploaded_dw_hpcl("D");// 押D，每日批次上傳非已結案資料至DW時，可被撈出並刪除DW資料
	}
	
	/**
	 * <pre>
	 * 透過分行別查詢 團體消貸名單控制檔該分行的團貸母戶編號
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult getGrpCntrNoList(PageParameters params) {
		String branch = MegaSSOSecurityContext.getUnitNo();
		CapAjaxFormResult r = new CapAjaxFormResult();
		r.putAll(eloandbService.getGrpCntrNoList(branch));
		return r;
	}
	
	/**
	 * <pre>
	 * J-110-0395調閱資料須加註查詢理由<br>
	 * 查詢是否當日已有紀錄
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult findTodayRecord(PageParameters params) {
		CapAjaxFormResult r = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		List<C122S01E> s01es = service.findTodayRecord(mainId);
		if (!s01es.isEmpty()) {
			r.set("queryReasonIsRecorded", "Y");
		}
		return r;
	}
	
	/**
	 * <pre>
	 * J-110-0395調閱資料須加註查詢理由<br>
	 * 寫入查詢理由
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult recordQueryReason(PageParameters params) {
		CapAjaxFormResult r = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C122M01A meta = service.getC122M01A_byMainId(mainId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String applyKind = meta.getApplyKind();
		String userId = user.getUserId();
		String userIp = user.getLoginIP();
		String queryReason = params.getString("queryReason");
		Timestamp queryTime = CapDate.getCurrentTimestamp();
		C122S01E s01e = new C122S01E();
		s01e.setApplyKind(applyKind);
		s01e.setMainId(mainId);
		s01e.setQueryReason(queryReason);
		s01e.setQueryTime(queryTime);
		s01e.setUserId(userId);
		s01e.setUserIp(userIp);
		service.save(s01e);
		return r;
	}
	
	/**
	 * <pre>
	 * 取得上傳視窗對應客戶下拉選單
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws Exception 
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult getUploadFileRelationshipList(PageParameters params)
			throws Exception {
		CapAjaxFormResult r = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		try {
			C122M01A meta = service.getC122M01A_byMainId(mainId);
			// 主借人
			r.set("00-" + meta.getCustId(), "主借款人-" + meta.getCustId());
			Map<String, String> ploan_casePosMap = codeTypeService.findByCodeType("ploan_casePos");
			// 取得線上進件的連保人保證人擔保品提供人
			List<C122M01A> relateCaseC122M01As = service
					.findMetaApplyKind_relateCase(meta.getApplyKind(), meta.getPloanCaseId());
			for (int i = 0; i < relateCaseC122M01As.size(); i++) {
				C122M01A c122m01a = relateCaseC122M01As.get(i);
				String posDESC = ploan_casePosMap.get(c122m01a.getPloanCasePos());
				// 從債務人
				r.set("01-" + c122m01a.getCustId(), posDESC + "-" + c122m01a.getCustId());
			}
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
		return r;
	}
	
	/**
	 * <pre>
	 * J-111-0223 新增eloan消金文件影像查詢匯入功能
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws Exception 
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult eLoanUploadImageFile(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult r = new CapAjaxFormResult();
		String formId = params.getString("formId");
		String stakeholderID = params.getString("stakeholderID");
		String docFileOid = params.getString("docFileOid");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C122M01A meta = service.getC122M01A_byMainId(mainId);
		// 判斷是否為主借款人更改第四碼
		// FormId=CLI*的不用更改，都是固定編號
		if (CapString.isEmpty(formId)) {
			throw new CapMessageException("mainId["+mainId+"] formId is empty", getClass());
		}
		// 20230428,陳詡涵(消金業務處,科長)將Ploan傳過來的檔案如果是關係人的表單代碼也帶0，下列程式不用特別判斷
//		if (!formId.startsWith("CLI")) {
//			char[] formIdChars = formId.toCharArray();
//			if (meta.getCustId().equals(stakeholderID)) {
//				formIdChars[3] = '0';
//			} else {
//				formIdChars[3] = '1';
//			}
//			formId = String.valueOf(formIdChars);
//		}
		C900M01M c900m01m = new C900M01M();
		try {
			Map<String, File> realFileMap = new HashMap<String, File>(); 
			DocFile docFile = docFileService.findByOidAndSysId(docFileOid, "LMS");
			File file = docFileService.getRealFile(docFile);
			realFileMap.put(docFileOid, file);
			// 不可接受檔案類型
			if (!mEGAImageService.isAvalibleFileType(docFile)) {
				return r;
			}
			if (!CapString.isEmpty(formId) && !CapString.isEmpty(docFileOid)) {
				// 上傳指定項目
				c900m01m.setApplicationDate(CapDate.formatDate(meta.getApplyTS(), "yyyyMMdd"));
				c900m01m.setBorrower(meta.getCustId());
				c900m01m.setBranch(meta.getOwnBrId());
				c900m01m.setCaseNo(meta.getPloanCaseNo());
				c900m01m.setDocFileOid(docFileOid);
				c900m01m.setFormId(formId);
				c900m01m.setMainId(mainId);
				c900m01m.setStakeholderID(stakeholderID);
				c900m01m.setUpdateTime(CapDate.getCurrentTimestamp());
				c900m01m.setUserCode(user.getUserId());
			}
			// 依上傳紀錄檔內容與文件類型執行上傳文件數位化作業
			clsService.eLoanUploadImageFile(c900m01m, meta);
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
		return r;
	}
	
	/**
	 * <pre>
	 * 取得eLoan連動影像查詢URL
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getEloanQueryImageListUrl(PageParameters params)
			throws CapException {
		CapAjaxFormResult r = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C122M01A meta = service.getC122M01A_byMainId(mainId);
		try {
			Map<String, String> clsQueryMap = mEGAImageService.getEloanQueryImageListParam(
					MEGAImageApiEnum.連動影像查詢, mainId, meta.getPloanCaseNo(),
					meta.getCustId());
			r.putAll(clsQueryMap);
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
		return r;
	}
	
	/**
	 * <pre>
	 * 連動刪除影像
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult eloanCloseImage(PageParameters params) throws CapException {
		CapAjaxFormResult r = new CapAjaxFormResult();
		// ELOAN附檔OID
		// 文件數位化docId=文件數位化文件序號
		String docId = params.getString("docId");
		// 文件數位化docFileOid=ELOAN附檔OID
		String docFileOid = params.getString("docFileOid");
		String fieldId = params.getString("fieldId");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C122M01A meta = service.getC122M01A_byMainId(mainId);
		try {
			String respString = "";
			// fieldId=MEGAImage 刪除文件數位化資料
			if ("MEGAImage".equals(fieldId)) {
				JSONObject resp = mEGAImageService.eloanCloseImage(docId);
				if (MEGAImageService.連動刪除影像_code.處理失敗.equals(resp.optString("code"))) {
					respString += "docId:" + docId + resp.optString("message") + "<br/>";
				}
			} else {
				docFileOid = docId;
				// 尋找已上傳紀錄檔的批號，刪除文件數位化FTP上資料夾
				clsService.delEloanUploadImageDir(docFileOid);
			}
			// 刪除本地資料
			docFileService.delete(docFileOid);
			respString += RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功);
			r.set(CapConstants.AJAX_NOTIFY_MESSAGE, respString);
			// 更新紀錄檔
			List<GenericBean> updateList = new ArrayList<GenericBean>();
			List<C900M01M> c900m01ms = clsService.findC900M01MByCaseNoDocFileOid(meta.getPloanCaseNo(), docFileOid);
			for (C900M01M c900m01m : c900m01ms) {
				c900m01m.setDeletedTime(CapDate.getCurrentTimestamp());
				updateList.add(c900m01m);
			}
			clsService.save(updateList);
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
		return r;
	}
	
}	
