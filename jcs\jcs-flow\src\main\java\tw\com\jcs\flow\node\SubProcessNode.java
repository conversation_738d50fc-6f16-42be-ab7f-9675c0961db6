package tw.com.jcs.flow.node;

import java.util.List;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowDefinitionImpl;
import tw.com.jcs.flow.core.FlowInstanceImpl;
import tw.com.jcs.flow.service.impl.FlowServiceImpl;

/**
 * <pre>
 * SubProcessNode
 * TODO
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public class SubProcessNode extends FlowNode {
    private static final Logger logger = LoggerFactory.getLogger(SubProcessNode.class);

    String definition;

    public String getDefinition() {
        return definition;
    }

    public void setDefinition(String definition) {
        this.definition = definition;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.node.FlowNode#next(tw.com.jcs.flow.core.FlowInstanceImpl)
     */
    @Override
    public void next(FlowInstanceImpl instance) {
        logger.info("\n State ==>" + instance.getState());
        List<Object> subList = instance.getSubInstanceList().get(instance.getState());
        if (subList == null) {
            instance.handle();
            // 如果還沒建立子流程，則建立新的，並執行子流程的Next
            FlowDefinitionImpl def = instance.getEngine().getDefinition(definition);
            FlowInstanceImpl subInst = ((FlowServiceImpl) instance.getEngine().getService()).startSubFlow(def, instance, def.getStartNode().getName(), instance.getState());
            subInst.next();
        } else {
            List<FlowInstance> subInstList = instance.getEngine().getPersistence().queryForInstance(subList.toArray(new Object[subList.size()]));

            boolean allDone = true;
            // 判斷子流程是否都已結束
            for (FlowInstance subInst : subInstList) {
                boolean result = false;
                for (FlowNode node : subInst.getDefinition().getEndNodes()) {
                    result |= subInst.getState().equals(node.getName());
                }
                allDone &= result;
                if (!result) {
                    // 將流程的attribute搬到子流程中
                    for (Entry<String, Object> entry : instance.getData().entrySet()) {
                        subInst.setAttribute(entry.getKey(), entry.getValue());
                    }
                    // 如果子流程尚未結束，則執行子流程的Next (由父流程推動子流程)
                    subInst.next();
                }
                if (subInst.getNode() instanceof EndNode) {
                    allDone = true;
                } else if (subInst.getNextNode() instanceof EndNode) {
                    subInst.next();
                    allDone = true;
                }
                if (allDone) {
                    // 將子流程的attribute搬到父流程中，以便父流程執行next動作。
                    for (Entry<String, Object> entry : ((FlowInstanceImpl) subInst).getData().entrySet()) {
                        instance.setAttribute(entry.getKey(), entry.getValue());
                    }
                }
            }

            if (subInstList.isEmpty()) {
                instance.handle();
                // 如果還沒建立子流程，則建立新的，並執行子流程的Next
                FlowDefinitionImpl def = instance.getEngine().getDefinition(definition);
                FlowInstanceImpl subInst = ((FlowServiceImpl) instance.getEngine().getService()).startSubFlow(def, instance, def.getStartNode().getName(), instance.getState());
                subInst.next();
            }

            if (allDone && !subInstList.isEmpty()) {
                finishCurrentNode(instance);
                changeToThisNode(instance);
                instance.next();
            }
        }
    }

}
