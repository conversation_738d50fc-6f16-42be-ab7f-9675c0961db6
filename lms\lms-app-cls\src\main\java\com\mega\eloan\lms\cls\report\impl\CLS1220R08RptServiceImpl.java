package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1220R08RptService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 中鋼團體消貸進件資料XLS
 */
@Service("cls1220r08rptservcie")
public class CLS1220R08RptServiceImpl implements FileDownloadService, CLS1220R08RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1220R08RptServiceImpl.class);

	@Resource
	BranchService branchService;

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Override
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateXls(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private ByteArrayOutputStream generateXls(PageParameters params)
	throws IOException, Exception {
		String csc_applyTS_beg = Util.trim(params.getString("csc_applyTS_beg"));
		String csc_applyTS_end = Util.trim(params.getString("csc_applyTS_end"));
		String ploanPlan = Util.trim(params.getString("csc_ploan_plan"));

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		genXls(outputStream, ploanPlan, csc_applyTS_beg, csc_applyTS_end);
		
		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private void genXls(ByteArrayOutputStream outputStream, String ploanPlan, String csc_applyTS_beg, String csc_applyTS_end) throws IOException,
			WriteException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		WritableWorkbook workbook = null;
		WritableSheet sheet1 = null;

		if (true) {
			// ---
			workbook = Workbook.createWorkbook(outputStream);
			sheet1 = workbook.createSheet("清單", 0);

			WritableFont headFont10 = new WritableFont(
					WritableFont.createFont("標楷體"), 10);
			WritableCellFormat cellFormatL_10 = new WritableCellFormat(
					headFont10);
			{
				cellFormatL_10.setAlignment(Alignment.LEFT);
				cellFormatL_10.setWrap(true);
			}
			// ======
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatR = new WritableCellFormat(headFont12);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatL_Border = new WritableCellFormat(
					cellFormatL);
			{
				cellFormatL_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}

			WritableCellFormat cellFormatR_Border = new WritableCellFormat(
					cellFormatR);
			{
				cellFormatR_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			// ======
			WritableFont headFont14 = new WritableFont(
					WritableFont.createFont("標楷體"), 14);
			WritableCellFormat cellFormatC_14 = new WritableCellFormat(
					headFont14);
			{
				cellFormatC_14.setAlignment(Alignment.CENTRE);
				cellFormatC_14.setWrap(true);
			}
			// ======

			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("分行代碼", 12);
			headerMap.put("分行別", 15);
			headerMap.put("統編", 20);
			headerMap.put("姓名", 15);
			headerMap.put("職工編號", 15);
			// J-113-0083 中鋼消貸EXCLE多產出-生日、EMAIL、學歷欄位 --start--
			headerMap.put("生日", 15);
			headerMap.put("學歷", 15);
			headerMap.put("EMAIL", 25);
			// J-113-0083 中鋼消貸EXCLE多產出-生日、EMAIL、學歷欄位 --end--
			headerMap.put("線上進件時間", 30);
			headerMap.put("申貸金額", 15);
			headerMap.put("銀行別", 15);
			headerMap.put("入帳帳號", 20);
			headerMap.put("支票領取地點", 15);
			headerMap.put("行銷方案", 30);
			headerMap.put("完成對保時間", 30);
			headerMap.put("完成對保 IP", 20);
			headerMap.put("利害關係人查詢時間", 30);
			headerMap.put("銀行法利害關係人", 10);
			headerMap.put("金控法第44條利害關係人 ", 10);
			headerMap.put("金控法第45條利害關係人 ", 10);
			headerMap.put("實質關係人(授信以外交易)", 10);
			headerMap.put("公司法與本行董事具有控制從屬關係公司", 10);
			headerMap.put("是否具有美國納稅義務人身分", 10);
			headerMap.put("是否具有中華民國以外之納稅義務人身分", 10);
			headerMap.put("利率變動通知方式", 25);
			headerMap.put("案件狀態", 15);

			int totalColSize = headerMap.size();

			List<String[]> rows = new ArrayList<String[]>();
			
			String brNo = user.getUnitNo();
			Map<String, String> cacheMap = new HashMap<String, String>();
			for (Map<String, Object> map_row : eloandbBASEService.findCLS1220R08(brNo, ploanPlan, csc_applyTS_beg, csc_applyTS_end)) {				
				// ---
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				String ownBrId = Util.trim(MapUtils.getString(map_row, "OWNBRID"));
				arr[0] = ownBrId;
				arr[1] = getBrName(cacheMap, ownBrId);
				arr[2] = Util.trim(MapUtils.getString(map_row, "CUSTID"));
				arr[3] = Util.trim(MapUtils.getString(map_row, "CUSTNAME"));
				arr[4] = Util.trim(MapUtils.getString(map_row, "STAFFNO"));
				// J-113-0083 中鋼消貸EXCLE多產出-生日、EMAIL、學歷欄位 --start--
				arr[5] = Util.trim(MapUtils.getString(map_row, "BIRTHDAY"));
				arr[6] = Util.trim(MapUtils.getString(map_row, "EDU_DESC"));
				arr[7] = Util.trim(MapUtils.getString(map_row, "EMAIL"));
				// J-113-0083 中鋼消貸EXCLE多產出-生日、EMAIL、學歷欄位 --end--
				arr[8] = Util.trim(TWNDate.toFullAD((Timestamp)MapUtils.getObject(map_row, "APPLYTS")));
				
				BigDecimal applyamt = Util.notEquals(Util.trim(map_row.get("APPLYAMT")), "") ? Util
						 .parseBigDecimal(Util.trim(map_row.get("APPLYAMT"))): BigDecimal.ZERO;
				// DB存的是萬元，但要呈現的是元
				arr[9] = applyamt.multiply(new BigDecimal("10000")).toPlainString();
				
				arr[10] = Util.trim(MapUtils.getString(map_row, "APPNBANKCODE"));
				arr[11] = Util.trim(MapUtils.getString(map_row, "DPACCT"));
				arr[12] = Util.trim(MapUtils.getString(map_row, "CHECKPLACE"));
				arr[13] = Util.trim(MapUtils.getString(map_row, "PLOANPLAN_DESC"));
				arr[14] = Util.trim(TWNDate.toFullAD((Timestamp)MapUtils.getObject(map_row, "AGREEQUERYEJTS")));
				arr[15] = Util.trim(MapUtils.getString(map_row, "AGREEQUERYEJIP"));
				arr[16] = Util.trim(TWNDate.toFullAD((Timestamp)MapUtils.getObject(map_row, "STKHQUERYEJTS")));
				
				// 銀行法利害關係人   1:是  0:否
				String stkhBank33 = Util.trim(MapUtils.getString(map_row, "STKHBANK33"));
				arr[17] = "1".equals(stkhBank33) ? "是" : "0".equals(stkhBank33) ? "否" : "";
				// 金控法第44條利害關係人   1:是  0:否
				String stkhFh44 = Util.trim(MapUtils.getString(map_row, "STKHFH44"));
				arr[18] = "1".equals(stkhFh44) ? "是" : "0".equals(stkhFh44) ? "否" : "";
				// 金控法第45條利害關係人    1:是  0:否
				String stkhFh45 = Util.trim(MapUtils.getString(map_row, "STKHFH45"));
				arr[19] = "1".equals(stkhFh45) ? "是" : "0".equals(stkhFh45) ? "否" : "";
				// 實質關係人(授信以外交易)    1:是  0:否
				String stkhRelFg = Util.trim(MapUtils.getString(map_row, "STKHRELFG"));
				arr[20] = "1".equals(stkhRelFg) ? "是" : "0".equals(stkhRelFg) ? "否" : "";
				// 公司法與本行董事具有控制從屬關係公司   1:是  0:否
				String stkhCoFg = Util.trim(MapUtils.getString(map_row, "STKHCOFG"));
				arr[21] = "1".equals(stkhCoFg) ? "是" : "0".equals(stkhCoFg) ? "否" : "";
				
				// 是否具有美國納稅義務人身分   Y:是  N:否  空白:空白
				String needW8BEN = Util.trim(MapUtils.getString(map_row, "NEEDW8BEN"));
				arr[22] = "Y".equals(needW8BEN) ? "是" : "N".equals(needW8BEN) ? "否" : "";
				// 是否具有中華民國以外之納稅義務人身分    Y:是  N:否  空白:空白
				String needCRS = Util.trim(MapUtils.getString(map_row, "NEEDCRS"));
				arr[23] = "Y".equals(needCRS) ? "是" : "N".equals(needCRS) ? "否" : "";

				// 利率變動通知方式 ● 2 書面寄送(通訊地址) ● 3 電子郵件
				String rateAdjNotify = Util.trim(MapUtils.getString(map_row, "RATEADJNOTIFY"));
				arr[24] = "2".equals(rateAdjNotify) ? "書面寄送(通訊地址)" : "3".equals(rateAdjNotify) ? "電子郵件" : "";
				
				arr[25] = Util.trim(MapUtils.getString(map_row, "CODEDESC"));
				// ---
				rows.add(arr);
			}

			int rowIdx = 0;
			// ==============================
			if(true){
				int colIdx = 0;
				for (String h : headerMap.keySet()) {
					int colWidth = headerMap.get(h);
					sheet1.setColumnView(colIdx, colWidth);
					sheet1.addCell(new Label(colIdx, rowIdx, h, cellFormatL_Border));
					// ---
					colIdx++;
				}
			}
			// ==============================
			if(true){
				rowIdx = 1;
				int i_row = 0;
				for (String[] arr : rows) {				
					for(int i_col = 0; i_col<totalColSize; i_col++){
						if(i_col == 9){
							sheet1.addCell(new Label(i_col, rowIdx + i_row, NumConverter.addComma(
									arr[i_col], "#,###,###,###,##0"),
									cellFormatR_Border)); // 申貸金額
						}else{
							sheet1.addCell(new Label(i_col, rowIdx + i_row, arr[i_col], cellFormatL_Border));
						}
					}				
					// ---
					i_row++;
				}
			}
			
			// ---
			workbook.write();
			workbook.close();
		}
	}

	private String getBrName(Map<String, String> cacheMap, String brNo){
		if(!cacheMap.containsKey(brNo)){
			IBranch obj = branchService.getBranch(brNo);
			if(obj!=null){
				cacheMap.put(brNo, Util.trim(obj.getBrName()));
			}
		}
		return Util.trim(cacheMap.get(brNo));
	}
	
}
