/* 
 * L182M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.util.CapDate;

import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.lms.dao.L182M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L182M01A;

/** 覆審預約單檔 **/
@Repository
public class L182M01ADaoImpl extends LMSJpaDao<L182M01A, String>
	implements L182M01ADao {

	@Override
	public L182M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L182M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
			
	@Override
	public List<L182M01A> findByDocStatus(String docStatus){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		List<L182M01A> list = createQuery(L182M01A.class,search).getResultList();
		return list;
	}

	@Override
	public List<L182M01A> findByIndex01(String docStatus, Date genDate, Date baseDate, String branchList){
		ISearch search = createSearchTemplete();
		List<L182M01A> list = null;
		if (docStatus != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		if (genDate != null)
			search.addSearchModeParameters(SearchMode.LESS_EQUALS, "genDate", genDate);
		if (baseDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "baseDate", baseDate);
		if (branchList != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchList", branchList);
		//檢查是否有查詢參數
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L182M01A.class,search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L182M01A> findUnProcessedTypCd5(String docStatus){
		ISearch search = createSearchTemplete();
		List<L182M01A> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "typCd", TypCdEnum.海外.getCode());
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.LESS_EQUALS, "genDate", CapDate.getCurrentTimestamp());
		
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L182M01A.class,search).getResultList();
		}
		return list;
	}
}