package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisELF348Service {

	/**
	 * 取得同一地號擔保品資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	List<Map<String, Object>> getELF348DPChk(String SITE1, String SITE2,
			String SITE3, String SITE4, String LNNO1, String LNNO2);

	/**
	 * 取得同一地號擔保品資料(無小段)
	 * 
	 * @param
	 * @param
	 * @return
	 */
	List<Map<String, Object>> getELF348DPChk(String SITE1, String SITE2,
			String SITE3, String LNNO1, String LNNO2);

	/**
	 * 取得段(SITE3)資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	List<Map<String, Object>> getELF348SITE3(String SITE1, String SITE2);

	/**
	 * 取得小段(SITE4)資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	List<Map<String, Object>> getELF348SITE4(String sITE1, String sITE2,
			String fSITE3);

	/**
	 * [個金額度明細表] 查詢擔保品已敘做總數額度序序號
	 * 
	 * @param SITE1
	 *            縣市
	 * @param SITE2
	 *            地區
	 * @param SITE3
	 * @param SITE4
	 * @param LNNO1
	 *            地號 左四碼
	 * @param LNNO2地號
	 *            右四碼
	 * @return
	 */
	List<Map<String, Object>> getCOLL0101findByKey(String SITE1, String SITE2,
			String SITE3, String SITE4, String LNNO1, String LNNO2);
}
