/* 
 * C140A01ADao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C140A01A;
import com.mega.eloan.lms.model.C140M01A;

/**
 * <pre>
 * 徵信調查報告書 授權 Dao
 * </pre>
 * 
 * @since 2011/11/16
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
public interface C140A01ADao extends IGenericDao<C140A01A> {

	/**
	 * 查詢登入分行之授權
	 * 
	 * @param meta
	 *            主檔
	 * @return C140A01A
	 */
	C140A01A findOwnAuth(C140M01A meta);
	
	/**
	 * 查詢編製/移送授權
	 * 
	 * @param meta
	 *            主檔
	 * @return C140A01A
	 */
	C140A01A findModify(C140M01A meta);
}
