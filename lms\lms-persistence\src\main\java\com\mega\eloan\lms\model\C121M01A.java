package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 海外消金評等模型主檔

內部評等＞評等文件，其 MAINID==RATINGID
在複製到簽報書後，同一個RATINGID 會再 copy 出新的{DOCSTATUS==ZZZ 的 MAINID}
● select * from lms.c121m01a where ratingId in (select ratingId from lms.c121m01a 
     where mainId in (select mainId from lms.c120m01a where caseId='responseJSON.mainId')
  )
    MAINID                           DOCSTATUS RANDOMCODE                       RATINGID                        
    -------------------------------- --------- -------------------------------- --------------------------------
    bab2d9a9e1cf4fa28912282190a92f43 05O       3150d8505d1f4dd89b1c63deafef841e bab2d9a9e1cf4fa28912282190a92f43
    7ccff676272e4ffcbb5a8bd8617b061b ZZZ       3150d8505d1f4dd89b1c63deafef841e bab2d9a9e1cf4fa28912282190a92f43


在 C120M01A 之中
● select * from lms.c120m01a where mainId='responseJSON.mainId' （用 mainId=?，則 ratingKind 只抓得到2,3）
● select * from lms.c120m01a where caseId='responseJSON.mainId' （用 caseId=?，則 ratingKind 只抓得到1,2）
    MAINID                           CASEID                           RATINGKIND
    -------------------------------- -------------------------------- ----------
    7ccff676272e4ffcbb5a8bd8617b061b e743da063bfa49a69dde492b88ae46f8 1        


在L120M01A 之中
    MAINID                           RATINGFLAG
    -------------------------------- ----------
    e743da063bfa49a69dde492b88ae46f8 AU 

*/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C121M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C121M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L120A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "c121m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<C121A01A> c121a01a;

	public Set<C121A01A> getC121a01a() {
		return c121a01a;
	}

	public void setC121A01A(Set<C121A01A> c121a01a) {
		this.c121a01a = c121a01a;
	}
	
	/** 評等文件ID **/
	@Size(max=32)
	@Column(name="RATINGID", length=32, columnDefinition="CHAR(32)")
	private String ratingId;

	/** 評等日期(主借人完成最終評等的日期) **/
	@Temporal(TemporalType.DATE)
	@Column(name="RATINGDATE", columnDefinition="DATE")
	private Date ratingDate;

	/** 案件號碼-年度 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="CASEYEAR", columnDefinition="DECIMAL(4,0)")
	private Integer caseYear;

	/** 案件號碼-分行 **/
	@Size(max=3)
	@Column(name="CASEBRID", length=3, columnDefinition="CHAR(3)")
	private String caseBrId;

	/** 案件號碼-流水號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="CASESEQ", columnDefinition="DECIMAL(5,0)")
	private Integer caseSeq;

	/** 評等文件編號 **/
	@Size(max=62)
	@Column(name="CASENO", length=62, columnDefinition="VARCHAR(62)")
	private String caseNo;

	/** 模型版本 **/
	@Size(max=3)
	@Column(name="VARVER", length=3, columnDefinition="VARCHAR(3)")
	private String varVer;

	/** 授信期間-年數 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="LNYEAR", columnDefinition="DECIMAL(2,0)")
	private Integer lnYear;

	/** 授信期間-月數 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="LNMONTH", columnDefinition="DECIMAL(2,0)")
	private Integer lnMonth;

	/** 評等模型類別{A:澳洲消金申請信用評等, J:日本消金申請信用評等, T:泰國消金申請信用評等} **/
	@Size(max=1)
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)")
	private String mowType;
	
	/** 國家別代碼 **/
	@Size(max=2)
	@Column(name = "MOWTYPECOUNTRY", columnDefinition = "VARCHAR(2)")
	private String mowTypeCountry;
	
	/** 清償期限資料格式 **/
	@Size(max=1)
	@Column(name="REPAYMENTSCHFMT", length=1, columnDefinition="CHAR(1)")
	private String repaymentSchFmt;
	
	/** 清償期限－天數 **/
	@Digits(integer=3, fraction=0)
	@Column(name="REPAYMENTSCHDAYS", columnDefinition="DECIMAL(3,0)")
	private Integer repaymentSchDays;
	
	/** 取得評等文件ID **/
	public String getRatingId() {
		return this.ratingId;
	}
	/** 設定評等文件ID **/
	public void setRatingId(String value) {
		this.ratingId = value;
	}

	/** 取得評等日期(主借人完成最終評等的日期) **/
	public Date getRatingDate() {
		return this.ratingDate;
	}
	/** 設定評等日期(主借人完成最終評等的日期) **/
	public void setRatingDate(Date value) {
		this.ratingDate = value;
	}

	/** 取得案件號碼-年度 **/
	public Integer getCaseYear() {
		return this.caseYear;
	}
	/** 設定案件號碼-年度 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/** 取得案件號碼-分行 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}
	/** 設定案件號碼-分行 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 取得案件號碼-流水號 **/
	public Integer getCaseSeq() {
		return this.caseSeq;
	}
	/** 設定案件號碼-流水號 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/** 取得評等文件編號 **/
	public String getCaseNo() {
		return this.caseNo;
	}
	/** 設定評等文件編號 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得模型版本 **/
	public String getVarVer() {
		return this.varVer;
	}
	/** 設定模型版本 **/
	public void setVarVer(String value) {
		this.varVer = value;
	}

	/** 取得授信期間-年數 **/
	public Integer getLnYear() {
		return this.lnYear;
	}
	/** 設定授信期間-年數 **/
	public void setLnYear(Integer value) {
		this.lnYear = value;
	}

	/** 取得授信期間-月數 **/
	public Integer getLnMonth() {
		return this.lnMonth;
	}
	/** 設定授信期間-月數 **/
	public void setLnMonth(Integer value) {
		this.lnMonth = value;
	}
	
	/** 取得評等模型類別 **/
	public String getMowType() {
		return this.mowType;
	}
	/** 設定評等模型類別 **/
	public void setMowType(String value) {
		this.mowType = value;
	}
	
	/** 取得國家別代碼 **/
	public String getMowTypeCountry() {
		return this.mowTypeCountry;
	}
	/** 設定國家別代碼 **/
	public void setMowTypeCountry(String value) {
		this.mowTypeCountry = value;
	}
	
	/** 取得清償期限資料格式 **/
	public String getRepaymentSchFmt() {
		return repaymentSchFmt;
	}
	/** 設定清償期限資料格式 **/
	public void setRepaymentSchFmt(String repaymentSchFmt) {
		this.repaymentSchFmt = repaymentSchFmt;
	}

	/** 取得清償期限－天數 **/
	public Integer getRepaymentSchDays() {
		return repaymentSchDays;
	}
	/** 設定清償期限－天數 **/
	public void setRepaymentSchDays(Integer repaymentSchDays) {
		this.repaymentSchDays = repaymentSchDays;
	}



	/** XXX 顯示用欄位 */
	@Transient
	private String lnPeriod;

	public void setLnPeriod(String value) {
		this.lnPeriod = value;
	}

	public String getLnPeriod() {
		return lnPeriod;
	}
	
	/** XXX 顯示用欄位 */
	@Transient
	private String cmsLocation;

	public void setCmsLocation(String value) {
		this.cmsLocation = value;
	}

	public String getCmsLocation() {
		return cmsLocation;
	}
	
}
