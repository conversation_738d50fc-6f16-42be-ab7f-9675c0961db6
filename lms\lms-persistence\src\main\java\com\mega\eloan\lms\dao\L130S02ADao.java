/* 
 * L130S02ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L130S02A;

/** 異常通報表額度控管設定 **/
public interface L130S02ADao extends IGenericDao<L130S02A> {

	L130S02A findByOid(String oid);
	L130S02A findByOid_NotDel(String oid);
	List<L130S02A> findByMainIdSeqNo(String mainId, String seqNo);
	L130S02A findByUniqueIdx(String mainId, String seqNo, String ctlType, String ctlItem);
	int deleteByKeyDeletedTime(String mainId, String seqNo, String ctlType, String ctlItem);
}