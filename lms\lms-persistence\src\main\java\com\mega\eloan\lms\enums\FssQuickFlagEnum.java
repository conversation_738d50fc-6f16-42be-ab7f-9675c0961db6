/* 
 * FssQuickFlagEnum.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * 是否為速動資產
 * 1 是
 * 0 否
 * </pre>
 * 
 * @since 2011/8/12
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/12,Sunkist Wang,new
 *          </ul>
 */
public enum FssQuickFlagEnum {

    /** 1 是 **/
    YES("1"),
    /** 2 否 **/
    NO("0");

    private String code;

    FssQuickFlagEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public boolean isEquals(Object other) {
        if (other instanceof String) {
            return code.equals(other);
        } else {
            return super.equals(other);
        }
    }

    public static FssQuickFlagEnum getEnum(String code) {
        for (FssQuickFlagEnum enums : FssQuickFlagEnum.values()) {
            if (enums.isEquals(code)) {
                return enums;
            }
        }
        return null;
    }
}
