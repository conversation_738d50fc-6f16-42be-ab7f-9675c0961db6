/* 
 * LMS1405Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01ATMP1;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01G;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01O_0307;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L140M01T;
import com.mega.eloan.lms.model.L140M02A;
import com.mega.eloan.lms.model.L140S04A;
import com.mega.eloan.lms.model.L140S06A;
import com.mega.eloan.lms.model.L782A01A;
import com.mega.eloan.lms.model.L782M01A;
import com.mega.eloan.lms.model.L999LOG01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

/**
 * <pre>
 * 額度明細表介面
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,REX,new
 *          </ul>
 */
public interface LMS1405Service extends AbstractService {

	// All

	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findModelListByMainId(Class clazz,
			String mainId);

	/**
	 * 額度批覆表
	 * 
	 * @param mainId
	 *            文件編號
	 * @return L140M02A
	 */
	L140M02A findL140M02AByMainId(String mainId);

	/**
	 * 儲存model
	 * 
	 * @param entity
	 *            model
	 * 
	 */
	void save(GenericBean... entity);

	/**
	 * 刪除model
	 * 
	 * @param entity
	 *            model
	 * @return
	 */
	void delete(GenericBean... entity);

	/**
	 * 取得Grid呈現所需的資料
	 * 
	 * @param clazz
	 *            要搜索model的class
	 * @param search
	 *            搜索的條件
	 * @return Page
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 用oid取得這筆資料
	 * 
	 * @param <T>
	 *            model
	 * @param clazz
	 *            要搜索model的class
	 * @param oid
	 *            文件編號
	 * 
	 * @return GenericBean
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 取得額度明細表的說明敘述檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return List<L140M01B>
	 */
	List<L140M01B> findL140m01bByMainId(String mainId);

	// L782M01A
	/**
	 * 取得特殊案件登錄的紀錄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param loanTP
	 *            授信科目代碼
	 * @return L782M01A
	 */
	L782M01A findL782m01aByUniqueKey(String mainId, String loanTP);

	/**
	 * 複製額度明細表， 將選擇的額度明細表複製後儲存
	 * 
	 * @param newMainId
	 *            複製後的mainId
	 * @param mainId
	 *            複製的mainId來源
	 * @param copyL140M01E
	 *            2012_05_28_建霖 是否要複製 攤貸比率
	 * @param copyL140S05A
	 *            是否複製變更條件項目
	 * @param copyL140S11A
	 *            是否複製敘做條件異動比較
	 *  @param copyL140S12A  
	 *            是否複製其他續做條件追蹤分項
	 */
	void copyL140m01All(String newMainId, String mainId, Boolean copyL140M01E,
			Boolean copyL140M01Q, Boolean copyL140M01T, Boolean copyL140S05A,
			Boolean copyL140S11A, Boolean copyL140S12A);

	// L120M01B
	/**
	 * 取得額度種類的紀錄
	 * 
	 * @param mainId
	 *            文件編號
	 * @return L120M01B
	 */
	L120M01B findL120m01bByUniqueKey(String mainId);

	/**
	 * 找出關聯檔內該mainId下所有的額度明細表、額度批覆表、母行法人提案意見
	 * 
	 * @param caseMainId
	 *            案件簽報書文件編號
	 * @return List<L120M01C>
	 */
	List<L120M01C> findL120m01cListByMainId(String caseMainId);

	/**
	 * 找出關聯檔內該mainId下所有的額度明細表、額度批覆表、母行法人提案意見
	 * 
	 * @param caseMainId
	 *            案件簽報書文件編號
	 * @return List<L120M01C>
	 */
	List<L120M01C> findL120m01cListByMainId(String caseMainId,
			String docStatus, String[] caseType);

	/**
	 * 判斷相同借款人的額度明細表，有幾種幣別，跟有幾個借款人已登錄額度明細表
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return List<Object[]>
	 */
	List<Object[]> findL140m01aListByL120m01c(String caseMainId, String caseType);

	/**
	 * 計算額度明細表合計
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return Map<String, Map<String, Long>>
	 */
	Map<String, Map<String, BigDecimal>> findL140m01Count(String caseMainId,
			String caseType) throws Exception;

	/**
	 * 計算額度明細表合計兩種以上幣別
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param curr
	 *            要算出的幣別
	 * @param showCurr
	 *            是否顯示多幣別
	 * 
	 * @return Map<String, Map<String, BigDecimal>> 計算結果
	 */
	Map<String, Map<String, BigDecimal>> findL140m01CountToTwoCurr(
			String caseMainId, String caseType, String curr, Boolean showCurr,
			Boolean saveRateFg) throws Exception;

	/**
	 * 計算額度明細表合計給供調整視窗使用
	 * 
	 * J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param caseMainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param countCurr
	 *            主要計算幣別
	 * @param showCurr
	 *            是否顯示多幣別
	 * @return 計算結果
	 */
	CapAjaxFormResult findL140m01editCount(String caseMainId, String caseType,
			String countCurr, Boolean showCurr, Boolean saveRateFg,
			CapAjaxFormResult result) throws Exception;

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType, String docStatus);

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType, String docStatus, String[] dataSrc);

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            String [] 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param docStatus
	 *            額度明細表文件狀態
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String[] caseType, String docStatus);

	/**
	 * 找出此案件簽報書底下的額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * 
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainId(String mainId,
			String caseType);

	/**
	 * 找出此案件簽報書底下的額度明細表-給列印用的順序
	 * 
	 * @param mainId
	 *            案件簽報書的mainid
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * 
	 * @return List<L140M01A>
	 */

	List<L140M01A> findL140m01aListByL120m01cMainIdForPrint(String mainId,
			String caseType);

	// L140M01a

	/**
	 * 刪除案件簽報書下所有的額度明細表相關資料
	 * 
	 * 根據要被刪除的oid找出底下所有的額度明細表mainId在做刪除
	 * 
	 * @param mainId
	 *            傳進來的oid
	 * @return boolean 回傳執行結果
	 */
	boolean deleteL140m01All(String mainId);

	/**
	 * 取得額度明細表
	 * 
	 * @param mainId
	 *            額度明細表文件編號
	 * @return L140M01A
	 */
	L140M01A findL140m01aByMainId(String mainId);

	/**
	 * 儲存額度明細檔主檔model list
	 * 
	 * @param list
	 *            List<L140M01A>
	 */
	void saveL140m01aList(List<L140M01A> list);

	/**
	 * 根據此額度明細表的mainId 刪除底下所有關聯table
	 * 
	 * @param mainId
	 *            文件編號
	 */
	void deleteL140m01(String mainId);

	/**
	 * 根據案件簽報書的mainId、借款人統編、重複序號 找出額度明細表 並將計算的授信總額度放入
	 * 
	 * @param caseMainId
	 *            簽報書的mainId
	 * @param caseType
	 *            額度明細表種類
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param count
	 *            總和
	 * @return boolean
	 */
	boolean findL140m01aListByMainIdCount(String caseMainId, String caseType,
			String custId, String dupNo, long count);

	/**
	 * 取得額度明細表的說明敘述檔
	 * 
	 * @param mainId
	 *            額度明細表文件編號
	 * @param itemType
	 *            說明的種類
	 * 
	 *            <pre>
	 *  1、限額條件
	 * 	2、利(費)率
	 * 	3、擔保品
	 * 	4、其他敘做條件
	 * 	5、敘做條件異動情形
	 * 	6、附表第一頁
	 * 	7、附表第二頁
	 * 	8、附表第三頁
	 * </pre>
	 * 
	 * @return L140M01B
	 */
	L140M01B findL140m01bUniqueKey(String mainId, String itemType);

	/**
	 * 儲存額度明細表的說明敘述檔List
	 * 
	 * @param list
	 *            L140M01B List
	 */
	void saveL140m01bList(List<L140M01B> list);

	/**
	 * 額度授信科目資料檔
	 * 
	 * @param mainId
	 *            文件編號
	 * 
	 * @return List<L140M01C>
	 */
	List<L140M01C> findL140m01cListByMainId(String mainId);

	/**
	 * 額度授信科目刪除多筆
	 * 
	 * @param oids
	 *            文件編號
	 * @param mainId
	 *            額度明細表mainId
	 */
	void deleteL140m01cList(String[] oids, String mainId);

	// L140M01D

	/**
	 * 額度授信科目限額檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param lmtType
	 *            <pre>
	 *            1科子目限額
	 *            2科子目合併限額
	 * </pre>
	 * @param lmtSeq
	 *            序列號
	 * @return L140M01D
	 */
	L140M01D findL140m01dUniqueKey(String mainId, String lmtType, Integer lmtSeq);

	/**
	 * 取出額度授信科目限額檔最大的seq
	 * 
	 * @param mainId
	 *            文件編號
	 * @param lmtType
	 *            序號
	 * @return 最大的seq
	 */
	int findL140m01dByMainIdAndlmtTypeMax(String mainId, String lmtType);

	/**
	 * 額度授信科目限額檔刪除多筆
	 * 
	 * @param oids
	 *            文件編號陣列
	 * 
	 */
	void deleteL140m01dList(String[] oids);

	/**
	 * 額度聯行攤貸比率檔 List
	 * 
	 * @param mainId
	 *            文件編號
	 * @param shareBrId
	 *            攤貸分行代碼
	 * @return L140M01E
	 */
	L140M01E findL140m01eByUniqueKey(String mainId, String shareBrId);

	/**
	 * 動審額度聯行攤貸比率檔 List
	 * 
	 * @param mainId
	 *            文件編號
	 * @param shareBrId
	 *            攤貸分行代碼
	 * @return L140M01E
	 */
	L140M01E_AF findL140m01e_afByUniqueKey(String mainId, String shareBrId);

	
	/**
	 * 額度聯行攤貸比率檔 刪除多筆
	 * 
	 * @param oids
	 *            文件編號陣列
	 */
	void deleteL140m01eList(String[] oids);

	// L140M01F

	/**
	 * 額度利費率主檔 取出這個mainID底下的最大Seq
	 * 
	 * @param mainId
	 *            文件編號
	 * @return 最大Seq
	 */
	int findL140m01fByMainIdMax(String mainId);

	/**
	 * 額度利費率主檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @return L140M01F
	 */
	L140M01F findL140m01fByUniqueKey(String mainId, Integer rateSeq);

	/**
	 * 額度利費率主檔刪除，並同時刪除L140M01G、L140M01H
	 * 
	 * @param oids
	 *            文件編號
	 * @param Seqs
	 *            序號
	 * @param mainId
	 *            額度明細表mainId
	 * 
	 */
	void deleteL140m01fList(String[] oids, String[] Seqs, String mainId);

	/**
	 * 額度利率明細檔特定幣別
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @param rateType
	 *            幣別種類
	 * 
	 *            <pre>
	 * 1新台幣、2美金、3日幣、4歐元、5雜幣
	 * </pre>
	 * @return L140M01G
	 */
	L140M01G findL140m01gByUniqueKey(String mainId, Integer rateSeq,
			String rateType);

	/**
	 * 額度利率明細檔所有幣別
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @return List<L140M01G>
	 */
	List<L140M01G> findL140m01gListByMainIdAndRateSeq(String mainId,
			Integer rateSeq);

	// L140M01H
	/**
	 * 額度費率明細檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @param rateSeq
	 *            序列號
	 * @return L140M01H
	 */
	L140M01H findL140m01hByUniqueKey(String mainId, Integer rateSeq);

	// 方法

	/**
	 * 組成幣別和利率說明
	 * 
	 * @param MainId
	 *            文件編號
	 * @param RateSeq
	 *            序列號
	 * @return String
	 */
	String toRateDrc(String MainId, Integer RateSeq);

	/**
	 * 組成限額字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pageNum
	 *            頁碼
	 * @param copyL140M01E
	 *            是否要組攤貸 字串
	 * @return String
	 */
	String saveL140m01bDscr1(String mainId, String pageNum, Boolean copyL140M01E);

	/**
	 * 組成限額字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pageNum
	 *            頁碼
	 * @return String
	 */
	String saveL140m01bDscr1(String mainId, String pageNum);

	/**
	 * 組成費率字串後儲存 並且 更改檢核狀態
	 * 
	 * @param mainId
	 *            文件編號
	 * @param pageNum
	 *            頁碼
	 * @return String
	 */

	String saveL140m01bDscr2(String mainId, String pageNum, boolean getDscr);

	/**
	 * 轉換符號
	 * 
	 * @param word
	 *            JSONArray
	 * @return String
	 */
	String convertMark(JSONArray word);

	/**
	 * 轉換符號
	 * 
	 * @param word
	 *            JSONArray
	 * @return array
	 */
	String[] convertMark(String word);

	/**
	 * 查詢基準利率
	 * 
	 * @param type
	 *            利率的種類 6D - 本行基準利率 、6S-基準利率月指標利率 、QX-6165初級市場ＣＰ九十天期(KX)平均利率
	 * @param curr
	 *            幣別
	 * @return 幣別利率
	 */
	String queryBaseRate(String type, String curr);

	/**
	 * 取得額度明細表分行逾放比
	 * 
	 * @param ownBrIds
	 *            要引進的所有銀行列表
	 * 
	 * @return
	 */
	HashMap<String, Map<String, Object>> queryLnf226(
			HashMap<String, String> ownBrIds);

	/**
	 * 找出這個簽報書底下的額度明細表 By custId 排序
	 * 
	 * @param mainId
	 *            文件編號
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @return List<L140M01A>
	 */
	public List<L140M01A> findL140m01aListByL120m01cMainIdOrderByCust(
			String mainId, String caseType);

	/**
	 * 額度序號給號
	 * 
	 * @param ownBrId
	 *            分行代碼
	 * @param unitCode
	 *            借款人類別 DBU-1 ,OBU-4,海外-5
	 * @param classCD
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> queryLnsp0050(String ownBrId, String unitCode,
			String classCD);

	/**
	 * 引進帳務資料 購料放款案下已開狀未到單金額
	 * 
	 * @param custId
	 *            客戶ID
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param lastcurr
	 *            前准額度幣別
	 * @return Map<String, String>
	 */
	Map<String, String> queryDwlnquotov(String custId, String dupNo,
			String cntrNo, String lastcurr);

	// void startFlow(String mainOid);
	/*
	 * public void flowAction(String mainOid, GenericBean model, boolean
	 * setResult, boolean resultType) throws Throwable;
	 */
	/**
	 * 判斷是否已經登錄國內分行
	 * 
	 * @param MainId
	 *            文件編號
	 * @param flag
	 *            String
	 * @return List<L140M01E>
	 */
	List<L140M01E> queryByMainIdAndFlag(String MainId, String[] flag);

	/**
	 * 搜尋這個連保人是否已經登錄 J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 * 
	 * @param mainId
	 *            文件編號
	 * @param type
	 *            1.自然人 2.法人
	 * 
	 * @param rId
	 *            連保人統編
	 * @param rDupNo
	 *            連保人統編重覆碼
	 * @return L140M01I
	 */
	// L140M01I findL140m01iByUniqueKey(String mainId, String type, String rId,
	// String rDupNo);

	/**
	 * 搜尋額度明細表是否存在此額度序號
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return 額度明細表
	 */
	List<L140M01A> findL140m01aBycntrNo(String cntrNo, String custId,
			String dupNo);

	/**
	 * 刪除連保人list
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @param mainId
	 *            文件編號
	 * @return boolean
	 */
	boolean deleteListL140m01i(String[] oids, String mainId);

	/**
	 * 根據mainId Array 找出所有的額度明細表
	 * 
	 * @param mainId
	 *            mainId陣列
	 * @return List<L140M01A>
	 */
	List<L140M01A> findL140m01aListByMainIdList(String[] mainId);

	/**
	 * 取得該ID 在徵信報告書底的連保人
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return Map<String, Map<String, JSONObject>>
	 * 
	 *         <pre>
	 * N 自然人  Map<id , name >
	 * </pre>
	 * 
	 */
	Map<String, Map<String, JSONObject>> findL140m01iPeopleData(String custId,
			String dupNo);

	/**
	 * 儲存連保人資料
	 * 
	 * @param L140m01iList
	 *            連保人陣列
	 */
	void saveL140m01iList(List<L140M01I> L140m01iList);

	/**
	 * 根據oids 抓出所有額度明細表
	 * 
	 * @param oids
	 *            陣列
	 * @return List<L140M01A>
	 */
	public List<L140M01A> findL140m01aByOids(String[] oids);

	/**
	 * 特殊登錄案件
	 * 
	 * @param mainId
	 *            文件編號
	 * @param authUnit
	 *            目前分行
	 * @return L782A01A
	 */
	public L782A01A findL782A01AByMainId(String mainId, String authUnit);

	/**
	 * 額度明細表主要儲存
	 * 
	 * @param l140m01bs
	 *            額度明細表敘述檔
	 * @param l140m01es
	 *            攤貸行
	 * @param entity
	 *            額度明細表相關檔案
	 */
	public void saveMain(List<L140M01B> l140m01bs, List<L140M01E> l140m01es,
			GenericBean... entity);

	/**
	 * 複製額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書mainId
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * 
	 * @param mainName
	 *            要設定的借款人名稱和統一編號
	 * @param oidList
	 *            要複製的額度明細表oid
	 */
	public void copyCntrdoc(String mainId, String caseType, String[] mainName,
			String[] oidList) throws CapException;

	/**
	 * 複製額度明細表
	 * 
	 * @param mainId
	 *            案件簽報書mainId
	 * @param caseType
	 *            文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
	 * @param newMainId
	 *            新的案件簽報書mainId
	 * @param caseNo
	 *            新的案件簽報書案號
	 * @param caseDate
	 *            新的案件簽報書簽案日期
	 * @throws CapException
	 */
	public void copyCntrdoc(String mainId, String caseType, String newMainId,
			String caseNo, Date caseDate) throws CapException;

	/**
	 * 複製聯行額度明細表 <br>
	 * Uid 塞入原轉入聯行的簽報書mainId
	 * 
	 * @param mainId
	 *            目前案件簽報書mainId
	 * @param selectMainid
	 *            原聯行案件簽報書mainId
	 */
	public void copyCntrdocByl141m01a(String mainId, String selectMainid)
			throws CapException;

	/**
	 * 刪除多個額度明細表
	 * 
	 * @param list
	 *            要刪除的額度明細表集合
	 */
	void delL140m01aList(List<L140M01A> list);

	/**
	 * 刪除多個收復彙計數，並更改額度明細表檢核欄位
	 * 
	 * @param oids
	 *            收付彙計數的oid
	 * @param l140m01a
	 *            額度明細表主檔
	 */
	void delL140M01KByOids(String[] oids, L140M01A l140m01a);

	/**
	 * 限額控管
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param parent
	 *            Component
	 * @param showCntrNo
	 *            是否顯示額度序號 true 為呈主管時檢查 false 為 給號時檢查
	 * @return 限額控管的訊息 若不需限額控管 回傳 空白
	 * @throws CapMessageException
	 */

	public Map<String, String> gfnDB2ChkNeedControlByCntrDoc(L140M01A l140m01a, Boolean showCntrNo) throws CapMessageException;

	/**
	 * 限額控管 呈案 檢查
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param parent
	 *            Component
	 * @return 限額控管的訊息 若不需限額控管 回傳 空白
	 * @throws CapMessageException
	 */

	public String gfnDB2ChkNeedControl(L140M01A l140m01a)
			throws CapMessageException;

	/**
	 * 儲存聯行攤貸比率
	 * 
	 * @param l140m01es
	 *            聯行攤貸比率
	 */
	public void savelistL140M01E(List<L140M01E> l140m01es);

	/**
	 * 儲存聯行攤貸比率
	 * 
	 * @param l140m01es
	 *            聯行攤貸比率
	 */
	public void savelistL140M01E_AF(List<L140M01E_AF> l140m01e_afs);
	
	/**
	 * 儲存關聯檔list
	 */

	public void savelistL120M01C(List<L120M01C> l120m01cs);

	/**
	 * 引進擔保品
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param oids
	 *            擔保品 C100M01 oid陣列
	 * @param parent
	 *            Component
	 * @return
	 */
	public String inculdeL140M01O(String mainId, String[] oids)
			throws CapMessageException;

	/**
	 * 刪除擔保品
	 * 
	 * @param oids
	 *            L140M01O.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId
	 */
	public String deleteL140M01O(String[] oids, String mainId);

	/**
	 * 重新引進擔保品描述
	 * 
	 * @param mainId
	 *            額度明細表 mainId
	 * 
	 *            return 取得擔保品描述
	 */
	public String reloadCMSDesc(String mainId);

	L140M01Q findL140m01qByMainId(String mainId);

	/**
	 * 重新引進共同借款人
	 * 
	 * @param oids
	 *            L120S01A.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId
	 * @param parent
	 *            Component
	 * @param custPos
	 *            性質
	 * @return
	 * @throws CapMessageException
	 */
	public String inculdeL140M01J(String[] oids, String mainId, String custPos) throws CapMessageException;

	/**
	 * 刪除共同借款人
	 * 
	 * @param oids
	 *            L140M01J.oid 陣列
	 * @param mainId
	 *            額度明細表 mainId
	 * @return
	 */
	public String deleteL140M01J(String[] oids, String mainId);

	/**
	 * 組共用借款人字串
	 * 
	 * @param l140m01js
	 *            共用借款人檔
	 * @return 組成字串
	 */
	public String getL140M01JStr(List<L140M01J> l140m01js);

	/**
	 * 組共用借款人字串
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @return 組成字串
	 */
	public String getL140M01JStr(String mainId);

	/**
	 * 取得國別
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @return Map<custid+dupno , 國別>
	 */
	public HashMap<String, String> getCustCounty(String mainId);

	/**
	 * 重新引進 更新該份額度明細表 姓名 案號
	 * 
	 * @param caseMainId
	 *            簽報書mainId
	 * @param parent
	 * @throws CapMessageException
	 */
	public void reloadCustName(String caseMainId)
			throws CapMessageException;

	/**
	 * 額度明細表登錄科目向上、下排序用
	 * 
	 * @param oid
	 * @return
	 */
	L140M01C findL140m01cByOid(String oid);

	/**
	 * 額度明細表登錄科目向上、下設定序號
	 * 
	 * @param model
	 *            L140M01C
	 * @param upOrDown
	 *            向上=true 向下=false
	 * @return
	 */
	boolean changeSeqNum(L140M01C l140m01c, boolean upOrDown);

	/**
	 * 重設額度明細表項下所有科目之seqNum
	 * 
	 * @param l140m01c
	 * @param upOrDown
	 * @return
	 */
	boolean resetL140M01CAllSeqNum(String mainId);

	/**
	 * 取得簽報書項下符合條件(統編、重複序號與額度序號)的額度明細表
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public List<L140M01A> findL140m01aListByainIdCustIdCntrno(String mainId,
			String custId, String dupNo, String cntrNo);

	public String build_l140m01cAdoptGrade(L140M01C l140m01c);

	public String build_l140m01cAdoptGrade(String c121MainId,
			String c121CustId, String c121DupNo, String grade, String modelType);

	public String build_l140m01aAdoptGrade(L140M01A l140m01a);

	public String build_l140m01aAdoptGrade(L140M01A l140m01a, String sepSign);
	
	public String build_LastGradeForTH(String c121MainId,
			String c121CustId, String c121DupNo, String grade, String modelType);
	

	/**
	 * L140M01A sync 從債務人、擔保品
	 */
	public void syncL140Rating(L140M01A l140m01a, String c121MainId,
			L140M01C l140m01c);

	public JSONObject buildQualitativeFactor(C121M01A c121m01a,
			C121S01A c121s01a);

	public String contactGuarantor(L140M01A l140m01a, String rType);

	/**
	 * 回傳不在 RatingDoc 的 cntrNo
	 */
	public Set<String> l140m01aMainBorrower_notInRating(L120M01A l120m01a,
			List<L140M01A> listL140m01a);

	/**
	 * 檢核 科目 未選擇評等
	 */
	public List<String> ratingElm_1_noGrade(List<L140M01C> l140m01c_list,
			Map<String, String> loanTpMap);

	/**
	 * 可能原先引入評等文件 001號(mainId:aaaaaaaa)，之後退回評等文件並 update 評等因子 <br>
	 * 再重引評等文件 001號(mainId:bbbbbbbb) <br>
	 * [1]檢核L140M01C.c121MainId 是否存在於 L120M01A.caseId <br>
	 * [2]檢核L140M01C.c121CustId 是否存在於評等文件內 <br>
	 * [3]檢核選取的 grade 是否==評等文件的最終評等
	 */
	public List<String> ratingElm_2_notExist(L120M01A l120m01a,
			List<L140M01C> l140m01c_list, Map<String, String> loanTpMap);

	/**
	 * 同一額度下(當多個評等文件) <br>
	 * ● 共借人、連保人 的人數、資料應相同 <br>
	 * ● 擔保品的因子、地址應相同
	 */
	public List<String> ratingElm_3_diff(L120M01A l120m01a,
			List<L140M01C> l140m01c_list);

	/**
	 * 比對 評等文件[共借人、連保人][擔保品] 與額度明細表內容[L140M01I 連保人, L140M01J 共借人]
	 */
	public List<String> ratingElm_4_vsl140m01a(L120M01A l120m01a,
			L120M01C l120m01c, L140M01A l140m01a, List<L140M01C> l140m01c_list);

	/**
	 * J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 * 依額度明細表借款人取得借款人基本資料
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	L120S01D findL120S01DByKey(String caseMainId, String custId, String dupNo);

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。
	 * 計算授信額度合計時，將本案異動金額加入信用風險遵循 FOR LOCAL
	 * 
	 * @param caseMainId
	 * @param caseType
	 * @return
	 */
	public String reCaculateL120S01M(String caseMainId, String caseType);

	public String resetLocalL120S01OAndReCaculateImpls(PageParameters params);

	/**
	 * 確認海外額度明細表內是否有台灣的額度序號
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean hasTaiwanCntrno(L140M01A l140m01a);

	/**
	 * 取得額度性質非不變或解除之額度明細表/批覆書 J-105-0179-001 Web
	 * e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 */
	public List<L140M01A> findL140m01aListByMainIdCustIdWithoutProperty7Or8(
			String caseMainId, String custId, String dupNo, String itemType);

	/**
	 * 取得額度明細表主借款人與所有共借人L120S01D List J-105-0250-001 Web e-Loan 新增利害關係人檢核
	 * 
	 * @param l140m01a
	 * @return
	 */
	public List<L120S01D> getCntrDocAllBorrowerL120S01D(L140M01A l140m01a);

	/**
	 * 檢查額度明細表是否有無擔科目 J-105-0250-001 Web e-Loan 新增利害關係人檢核
	 * 
	 * @param l140m01a
	 * @param exceptSubject
	 * @return
	 */
	public boolean chkCntrDocHasUnSecureSubject(L140M01A l140m01a,
			String sbjProperty, String[] exceptSubject);

    public boolean chkIsNeedDerivEval(L140M01A l140m01a);

	/**
	 * J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @param caseMainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public L120S01B findL120S01BByKey(String caseMainId, String custId,
			String dupNo);

	/*
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 */
	public L140M01I findL140m01iByUniqueKeyWithRType(String mainId,
			String type, String rId, String rDupNo, String rType);

	/*
	 * J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	 */
	public List<L140M01I> findL140m01iListWithRType(String mainId, String rType);

	/**
	 * 不符合授信政策案 J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	 */
	public List<L140S04A> findL140s04aListByMainIdWithItemType(String mainId,
			String itemType);

	/**
	 * 不符合授信政策案 J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	 */
	public L140S04A findL140s04aByOid(String oid);

	/**
	 * 不符合授信政策案 J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊
	 */
	public void deleteListL140s04a(List<L140S04A> l140s04as);

	public int delPreSearchResult(String userId) throws CapException;

	// J-107-0069-001
	// e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。
	// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
	public int execFullTextSearch(String fxUserId, String fxGroupId,
			String fxCaseDateName, String fxCaseDateS, String fxCaseDateE,
			String fxEndDateS, String fxEndDateE, String fxTypCd,
			String fxDocType, String fxDocKind, String fxDocCode,
			String fxUpdaterName, String fxUpdater, String fxCaseBrId,
			String fxCustId, String fxDocRslt, String fxDocStatus,
			String fxLnSubject, String fxRateText, String fxOtherCondition,
			String fxReportOther, String fxReportReason1,
			String fxReportReason2, String fxAreaOption, String fxCollateral,
			String fxCustName, String fxBusCode, String fxCurr,
			String fxLmtDays1, String fxLmtDays2, String fxRateText1,
			String fxGuarantor, String fxCntrNo, String fxCollateral1,
			String unitType, String fxIsCls, String fxProdKind,
			String fxLnSubjectCls, String fxRateTextCls, String fxLnMonth1,
			String fxLnMonth2, String fxCrGrade, String fxCrKind, String uid,
			String fxBldUse, String fxOnlyLand) throws CapException;

	public Page<L140M01A> findL140m01aByL140m01atmp1UserId(ISearch search,
			String userId);

	public Page<L140M01ATMP1> findL140m01atmp1ByUserId(ISearch search,
			String userId);

	public L999LOG01A findLatestL999log01a(String creator, String itemType);

	public L999LOG01A findL999log01aByMainId(String logMainId);

	public int delL140m01aTmp1ByUid(String uid) throws CapException;

	void saveL140m01tList(List<L140M01T> list);

	void deleteL140m01tAndFile(String oid);

	L140M01T getBuildInfoByMcntrNo(String mCntrNo);

	void deleteCurrentL140m01ts(String mainId);

	List<L140M01T> findCurrentL140m01ts(String mainId);

	List<L140M01T> findLastL140m01ts(String mainId);

	L140M01T findL140m01t(String mainId, String estateType);

	public List<L140S06A> findL140s06as(String mainId);

	void saveL140s06aList(List<L140S06A> list);

	void deleteL140s06a(String oid);

	public void deleteL140s06aAll(String mainId);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L140M01O_0307> findL140m01o_0307as(String mainId);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param list
	 */
	public void saveL140m01o_0307List(List<L140M01O_0307> list);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param oids
	 * @param mainId
	 */
	public void deleteL140m01o_0307(String[] oids, String mainId);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param mainId
	 */
	public void deleteL140m01o_0307All(String mainId);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * @param mainId
	 * @param stkNo
	 * @param StkNm
	 * @return
	 */
	public List<L140M01O_0307> findL140m01o_0307ByStkNoAndStkNm(String mainId,
			String stkNo, String StkNm);

	/**
	 * 取得代碼轉換
	 * 
	 * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public String queryL140s08aByItemNameAndSubItem(String itemName,
			String subItem) throws CapException;

	/**
	 * 查詢model by mainId
	 * 
	 * @param <T>
	 * @param clazz
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByManId(Class clazz, String mainId);

	/**
	 * 由額度批覆表找出對應的那筆額度明細表
	 * 
	 * @param l140m01aItemType2
	 * @return
	 */
	public L140M01A findItemType1FromItemType2(L140M01A l140m01aItemType2);

	/**
	 * 重新設定簽報書團貸序號
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @param parentCntrNo
	 *            團貸額度序號
	 */
	public void setL140M03AGrpCntrNo(String mainId, String parentCntrNo,
			String welfareCmte);

	public L140M01M findL140m01mByMainId(String mainId);

	L140M01A findL140m01aByCaseMainIdCustIdCntrNoItemType(String caseMainId,
			String custId, String dupNo, String cntrNo, String itemType);

	public List<L140M01A> findL140m01aListByMainIdCustId(String caseMainId,
			String custId, String dupNo, String itemType);

	/**
	 * J-111-0411_05097_B1001 Web e-Loan企金授信新增不動產授信例外管理相關功能
	 * 
	 * @param l140m01a
	 * @return
	 */
	public boolean displayIntRegReason(L140S06A l140s06a);

	public boolean isExTotalCreditLimit(L140M01A l140m01a);

	public void saveCountEditForm(PageParameters params)
			throws CapException;
	
	/**
	 * 動審額度聯行攤貸比率檔 刪除多筆
	 * 
	 * @param oids
	 *            文件編號陣列
	 */
	void deleteL140m01e_afList(String[] oids);

}