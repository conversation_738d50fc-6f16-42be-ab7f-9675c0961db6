/* 
 * CLS1141R01RptService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.report;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;

/**
 * <pre>
 * rpt報表service程式 - 簽報書及額度明細表
 * </pre>
 * 
 * @since 2011/10/18
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS1141R01RptService {

	OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception;

	/**
	 * J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @param itemType
	 * @return
	 */
	public String buildLMS140LgdTotalHtml(L120M01A l120m01a, L140M01A l140m01a,
			String itemType) throws Exception;

	/**
	 * J-111-0461_05097_B1008 授信額度合計新增單獨另計授權及各組LGD合計檢核
	 * 
	 * @param l120m01a
	 * @param l140m01a
	 * @param itemType
	 * @param modeType
	 *            1:額度明細表畫面 2:額度明細表列印
	 * @return
	 */
	public String buildLMS140LgdTotalHtml_2(L120M01A l120m01a,
			L140M01A l140m01a, String itemType, String modeType)
			throws Exception;

}
