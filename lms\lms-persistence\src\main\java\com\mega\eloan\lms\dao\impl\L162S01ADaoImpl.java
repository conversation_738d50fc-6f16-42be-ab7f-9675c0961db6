/* 
 * L161S01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L162S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L162S01A;

/** 主從債務人資料表檔 **/
@Repository
public class L162S01ADaoImpl extends LMSJpaDao<L162S01A, String> implements
		L162S01ADao {

	@Override
	public L162S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L162S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("cntrNo", false);
		printSeqMap.put("custId", false);
		search.setOrderBy(printSeqMap);
		List<L162S01A> list = createQuery(L162S01A.class, search)
				.getResultList();

		return list;
	}

	@Override
	public L162S01A findByUniqueKey(String mainId, String custId, String dupNo,
			String cntrNo, String rId, String rDupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "rId", rId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rDupNo", rDupNo);
		return findUniqueOrNone(search);
	}
	
	@Override
	public L162S01A findS01AByCustIdCntrNo(String custId, String dupNo, String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L162S01A> findByIndex01(String mainId, String custId,
			String dupNo, String cntrNo, String rId, String rDupNo) {
		ISearch search = createSearchTemplete();
		List<L162S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (rId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rId", rId);
		if (rDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rDupNo", rDupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L162S01A.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L162S01A> findByOid(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L162S01A> list = createQuery(L162S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L162S01A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L162S01A> list = createQuery(L162S01A.class, search)
				.getResultList();

		return list;
	}

	@Override
	public List<L162S01A> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L162S01A> list = createQuery(L162S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L162S01A> findByMainIdOrderByGrid(String mainId) {
		// 統編 ASC 額度序號 ASC 相關身分ASC 關係 ASC
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		// printSeqMap.put("custId", false);
		printSeqMap.put("cntrNo", false);
		printSeqMap.put("rType", false);
		// printSeqMap.put("rKindM", false);
		printSeqMap.put("rKindD", false);
		printSeqMap.put("rId", false);
		search.setOrderBy(printSeqMap);
		List<L162S01A> list = createQuery(L162S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L162S01A> findByMainIdCntrNo(String mainId, String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L162S01A> list = createQuery(L162S01A.class, search)
				.getResultList();

		return list;
	}

	@Override
	public List<L162S01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

}