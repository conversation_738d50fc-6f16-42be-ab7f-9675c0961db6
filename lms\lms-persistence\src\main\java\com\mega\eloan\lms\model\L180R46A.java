/* 
 * L180R46A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 共同行銷未結案資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L180R46A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L180R46A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 簽報書mainId<p/>
	 * 來源：L120M01A.mainId
	 */
	@Size(max=32)
	@Column(name="SRCMAINID", length=32, columnDefinition="CHAR(32)")
	private String srcMainId;

	/** 
	 * 額度明細表mainId<p/>
	 * 來源：L140M01A.mainId
	 */
	@Size(max=32)
	@Column(name="CNTRMAINID", length=32, columnDefinition="CHAR(32)")
	private String cntrMainId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 案件號碼 **/
	@Size(max=62)
	@Column(name="CASENO", length=62, columnDefinition="VARCHAR(62)")
	private String caseNo;

	/** 
	 * 案件號碼-分行<p/>
	 * 來源：L120M01A.caseBrId
	 */
	@Size(max=3)
	@Column(name="CASEBRID", length=3, columnDefinition="CHAR(3)")
	private String caseBrId;

	/** 本案最後批示日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 
	 * 類型<p/>
	 *  A產險 B票券 C證券<br/>
	 *  MIS.SYNBANK (英文大小寫有差)<br/>
	 *  A:<br/>
	 *  B( FINKIND='06' FINCODE='060' BRNNO<>''<br/>
	 *  C( FINKIND='1A' FINCODE='700' BRNNO<>''
	 */
	@Size(max=1)
	@Column(name="TYPE", length=1, columnDefinition="VARCHAR(1)")
	private String type;

	/** 第一層-是否 **/
	@Size(max=1)
	@Column(name="CHECKYN", length=1, columnDefinition="VARCHAR(1)")
	private String checkYN;

	/** 
	 * 第二層-結果<p/>
	 *  A1已洽兆豐產險<br/>
	 *  A2未洽兆豐產險<br/>
	 *  A3已於擔保品檔鍵入投保狀況<br/>
	 *  B1已洽財務處或兆豐票券<br/>
	 *  B2未洽財務處或兆豐票券<br/>
	 *  B3未與財務處或兆豐票券辦理簽證承銷<br/>
	 *  C1已洽兆豐證券<br/>
	 *  C2未洽兆豐證券<br/>
	 *  C3未與財兆豐證券辦理簽證承銷
	 */
	@Size(max=2)
	@Column(name="RESULT", length=2, columnDefinition="VARCHAR(2)")
	private String result;
	
	/** 
	 * 窗口使用<p/>
	 *  Ex. 兆豐票券金融公司高雄分公司(下拉FINKIND='06' FINCODE='060' BRNNO=’002’) 	=> 002
	 */
	@Size(max=5)
	@Column(name="CONTACT", length=5, columnDefinition="VARCHAR(5)")
	private String contact;

	/** 
	 * 說明<p/>
	 *  若為窗口使用(A1.B1.C1)<br/>
	 *  若為原因(textbox) 使用時機<br/>
	 *  (type=’B’ && checkYN=’N’)、B3、(type=’C’ && checkYN=’N’)、C3.
	 */
	@Size(max=300)
	@Column(name="MEMO", length=300, columnDefinition="VARCHAR(300)")
	private String memo;

	/** 
	 * 通知次數<p/>
	 *  若更新不同簽報書mainId則歸0<br/>
	 *  若同一簽報書mainId則不歸0<br/>
	 *  需通知時機 A2.B2.C2 && cnt<8
	 */
	private Integer cnt;

	/** 原負責經辦 **/
	@Size(max=6)
	@Column(name="APPRAISER", length=6, columnDefinition="CHAR(6)")
	private String appraiser;

	/** 知會經辦 **/
	@Size(max=6)
	@Column(name="INFOAPPRAISER", length=6, columnDefinition="CHAR(6)")
	private String infoAppraiser;

	/** 
	 * 最後異動來源<p/>
	 * 簽報書LMS /建檔維護FMS
	 */
	@Size(max=3)
	@Column(name="UPDFROM", length=3, columnDefinition="CHAR(3)")
	private String updFrom;
	
	/** 最近一次提醒日期 **/
	@Column(name="NOTIFYTIME", columnDefinition="TIMESTAMP")
	private Timestamp notifyTime;

	/** 建立人員 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立時間 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 更新人員 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 更新時間 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得簽報書mainId<p/>
	 * 來源：L120M01A.mainId
	 */
	public String getSrcMainId() {
		return this.srcMainId;
	}
	/**
	 *  設定簽報書mainId<p/>
	 *  來源：L120M01A.mainId
	 **/
	public void setSrcMainId(String value) {
		this.srcMainId = value;
	}

	/** 
	 * 取得額度明細表mainId<p/>
	 * 來源：L140M01A.mainId
	 */
	public String getCntrMainId() {
		return this.cntrMainId;
	}
	/**
	 *  設定額度明細表mainId<p/>
	 *  來源：L140M01A.mainId
	 **/
	public void setCntrMainId(String value) {
		this.cntrMainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得案件號碼 **/
	public String getCaseNo() {
		return this.caseNo;
	}
	/** 設定案件號碼 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 
	 * 取得案件號碼-分行<p/>
	 * 來源：L120M01A.caseBrId
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}
	/**
	 *  設定案件號碼-分行<p/>
	 *  來源：L120M01A.caseBrId
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 取得本案最後批示日期 **/
	public Date getEndDate() {
		return this.endDate;
	}
	/** 設定本案最後批示日期 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 
	 * 取得類型<p/>
	 * A產險 B票券 C證券<br/>
	 *  MIS.SYNBANK (英文大小寫有差)<br/>
	 *  A:<br/>
	 *  B: FINKIND='06' FINCODE='060' BRNNO<>''<br/>
	 *  C: FINKIND='1A' FINCODE='700' BRNNO<>''
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定類型<p/>
	 *  A產險 B票券 C證券<br/>
	 *  MIS.SYNBANK (英文大小寫有差)<br/>
	 *  A:<br/>
	 *  B: FINKIND='06' FINCODE='060' BRNNO<>''<br/>
	 *  C: FINKIND='1A' FINCODE='700' BRNNO<>''
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得第一層-是否 **/
	public String getCheckYN() {
		return this.checkYN;
	}
	/** 設定第一層-是否 **/
	public void setCheckYN(String value) {
		this.checkYN = value;
	}

	/** 
	 * 取得第二層-結果<p/>
	 *  A1已洽兆豐產險<br/>
	 *  A2未洽兆豐產險<br/>
	 *  A3已於擔保品檔鍵入投保狀況<br/>
	 *  B1已洽財務處或兆豐票券<br/>
	 *  B2未洽財務處或兆豐票券<br/>
	 *  B3未與財務處或兆豐票券辦理簽證承銷<br/>
	 *  C1已洽兆豐證券<br/>
	 *  C2未洽兆豐證券<br/>
	 *  C3未與財兆豐證券辦理簽證承銷
	 */
	public String getResult() {
		return this.result;
	}
	/**
	 *  設定第二層-結果<p/>
	 *  A1已洽兆豐產險<br/>
	 *  A2未洽兆豐產險<br/>
	 *  A3已於擔保品檔鍵入投保狀況<br/>
	 *  B1已洽財務處或兆豐票券<br/>
	 *  B2未洽財務處或兆豐票券<br/>
	 *  B3未與財務處或兆豐票券辦理簽證承銷<br/>
	 *  C1已洽兆豐證券<br/>
	 *  C2未洽兆豐證券<br/>
	 *  C3未與財兆豐證券辦理簽證承銷
	 **/
	public void setResult(String value) {
		this.result = value;
	}
	
	/** 
	 * 取得窗口<p/>
	 */
	public String getContact() {
		return this.contact;
	}
	/**
	 *  設定窗口<p/>
	 **/
	public void setContact(String value) {
		this.contact = value;
	}

	/** 
	 * 取得說明<p/>
	 */
	public String getMemo() {
		return this.memo;
	}
	/**
	 *  設定說明<p/>
	 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 
	 * 取得通知次數<p/>
	 *  若更新不同簽報書mainId則歸0<br/>
	 *  若同一簽報書mainId則不歸0<br/>
	 *  需通知時機 A2.B2.C2 && cnt<8
	 */
	public Integer getCnt() {
		return this.cnt;
	}
	/**
	 *  設定通知次數<p/>
	 *  若更新不同簽報書mainId則歸0<br/>
	 *  若同一簽報書mainId則不歸0<br/>
	 *  需通知時機 A2.B2.C2 && cnt<8
	 **/
	public void setCnt(Integer value) {
		this.cnt = value;
	}

	/** 取得原負責經辦 **/
	public String getAppraiser() {
		return this.appraiser;
	}
	/** 設定原負責經辦 **/
	public void setAppraiser(String value) {
		this.appraiser = value;
	}

	/** 取得知會經辦 **/
	public String getInfoAppraiser() {
		return this.infoAppraiser;
	}
	/** 設定知會經辦 **/
	public void setInfoAppraiser(String value) {
		this.infoAppraiser = value;
	}

	/** 
	 * 取得最後異動來源<p/>
	 * 簽報書LMS /建檔維護FMS
	 */
	public String getUpdFrom() {
		return this.updFrom;
	}
	/**
	 *  設定最後異動來源<p/>
	 *  簽報書LMS /建檔維護FMS
	 **/
	public void setUpdFrom(String value) {
		this.updFrom = value;
	}
	
	/** 取得最近一次提醒日期 **/
	public Timestamp getNotifyTime() {
		return this.notifyTime;
	}
	/** 設定最近一次提醒日期**/
	public void setNotifyTime(Timestamp value) {
		this.notifyTime = value;
	}

	/** 取得建立人員 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立時間 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立時間 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得更新人員 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定更新人員 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得更新時間 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定更新時間 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
