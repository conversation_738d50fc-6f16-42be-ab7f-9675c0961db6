/* 
 * L141M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;

import tw.com.iisi.cap.model.IDataObject;

/** 聯行額度明細表主檔 **/
@Entity
@Table(name = "L141M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L141M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	/**
	 * JOIN條件 L141A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l141m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L141A01A> l141a01a;

	public Set<L141A01A> getL141a01a() {
		return l141a01a;
	}

	public void setL141a01a(Set<L141A01A> l141a01a) {
		this.l141a01a = l141a01a;
	}

	/**
	 * JOIN條件 L141M01D． 聯行額度明細表簽章欄檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l141m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@OrderBy("seq,oid")
	private List<L141M01D> l141m01d;

	public List<L141M01D> getL141M01D() {
		return l141m01d;
	}

	public void setL141M01D(List<L141M01D> l141m01d) {
		this.l141m01d = l141m01d;
	}

	/**
	 * 簽報書文件編號
	 * <p/>
	 * 100/12/02調整<br/>
	 * 來源簽報書的文件編號<br/>
	 * 來源：L120M01A.mainId
	 */
	@Column(name = "SRCMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String srcMainId;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 101/07/05新增<br/>
	 * 來源：L120M01A.caseBrId
	 */
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 案件號碼
	 * <p/>
	 * 來源：L120M01A.caseNo
	 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 來源：L120M01A.caseDate
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 簽報書核准日期
	 * <p/>
	 * 來源：L120M01A.endDate
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "FINALDATE", columnDefinition = "DATE")
	private Date finalDate;

	/**
	 * 企/個金案件
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.docType<br/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 授權別
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.docKind<br/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String docKind;

	/**
	 * 授權等級
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.authLvl<br/>
	 * ※docKind=授權內<br/>
	 * 1分行授權內<br/>
	 * 2總行授權內<br/>
	 * 3營運中心授權內
	 */
	@Column(name = "AUTHLVL", length = 1, columnDefinition = "VARCHAR(1)")
	private String authLvl;

	/**
	 * 案件審核層級
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.caseLvl<br/>
	 * 1常董會權限<br/>
	 * 2常董會權限簽奉總經理核批<br/>
	 * 3常董會權限簽准由副總經理核批<br/>
	 * 4利費率變更案件由總處經理核定<br/>
	 * 5屬常董會授權總經理逕核案件<br/>
	 * 6總經理權限內<br/>
	 * 7副總經理權限<br/>
	 * 8處長權限<br/>
	 * 9其他(經理)<br/>
	 * A董事會權限<br/>
	 * B營運中心營運長/副營運長權限<br/>
	 * C利費率變更案件由董事長核定<br/>
	 * D個金處經理權限
	 */
	@Column(name = "CASELVL", length = 2, columnDefinition = "CHAR(2)")
	private String caseLvl;

	/**
	 * 本案是否有同業聯貸案額度
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01B.unitCase<br/>
	 * Y/N（是/否）
	 */
	@Column(name = "UNITCASE", length = 1, columnDefinition = "VARCHAR(1)")
	private String unitCase;

	/**
	 * 聯行經辦
	 * <p/>
	 * 來源：L120M01F.staffNo<br/>
	 * branchType=1 & staffJob=L1
	 */
	@Column(name = "COAPPRAISER", length = 6, columnDefinition = "CHAR(6)")
	private String coAppraiser;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/**
	 * 設定簽報書文件編號
	 * <p/>
	 * 100/12/02調整<br/>
	 * 來源簽報書的文件編號<br/>
	 * 來源：L120M01A.mainId
	 **/
	public String getSrcMainId() {
		return this.srcMainId;
	}

	/**
	 * 設定簽報書文件編號
	 * <p/>
	 * 100/12/02調整<br/>
	 * 來源簽報書的文件編號<br/>
	 * 來源：L120M01A.mainId
	 **/
	public void setSrcMainId(String value) {
		this.srcMainId = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 101/07/05新增<br/>
	 * 來源：L120M01A.caseBrId
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 101/07/05新增<br/>
	 * 來源：L120M01A.caseBrId
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 來源：L120M01A.caseNo
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 來源：L120M01A.caseNo
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 來源：L120M01A.caseDate
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 來源：L120M01A.caseDate
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得簽報書核准日期
	 * <p/>
	 * 來源：L120M01A.endDate
	 */
	public Date getFinalDate() {
		return this.finalDate;
	}

	/**
	 * 設定簽報書核准日期
	 * <p/>
	 * 來源：L120M01A.endDate
	 **/
	public void setFinalDate(Date value) {
		this.finalDate = value;
	}

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.docType<br/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.docType<br/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得授權別
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.docKind<br/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	public String getDocKind() {
		return this.docKind;
	}

	/**
	 * 設定授權別
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.docKind<br/>
	 * 1授權內<br/>
	 * 2授權外
	 **/
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/**
	 * 取得授權等級
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.authLvl<br/>
	 * ※docKind=授權內<br/>
	 * 1分行授權內<br/>
	 * 2總行授權內<br/>
	 * 3營運中心授權內
	 */
	public String getAuthLvl() {
		return this.authLvl;
	}

	/**
	 * 設定授權等級
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.authLvl<br/>
	 * ※docKind=授權內<br/>
	 * 1分行授權內<br/>
	 * 2總行授權內<br/>
	 * 3營運中心授權內
	 **/
	public void setAuthLvl(String value) {
		this.authLvl = value;
	}

	/**
	 * 取得案件審核層級
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.caseLvl<br/>
	 * 1常董會權限<br/>
	 * 2常董會權限簽奉總經理核批<br/>
	 * 3常董會權限簽准由副總經理核批<br/>
	 * 4利費率變更案件由總處經理核定<br/>
	 * 5屬常董會授權總經理逕核案件<br/>
	 * 6總經理權限內<br/>
	 * 7副總經理權限<br/>
	 * 8處長權限<br/>
	 * 9其他(經理)<br/>
	 * A董事會權限<br/>
	 * B營運中心營運長/副營運長權限<br/>
	 * C利費率變更案件由董事長核定<br/>
	 * D個金處經理權限
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}

	/**
	 * 設定案件審核層級
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01A.caseLvl<br/>
	 * 1常董會權限<br/>
	 * 2常董會權限簽奉總經理核批<br/>
	 * 3常董會權限簽准由副總經理核批<br/>
	 * 4利費率變更案件由總處經理核定<br/>
	 * 5屬常董會授權總經理逕核案件<br/>
	 * 6總經理權限內<br/>
	 * 7副總經理權限<br/>
	 * 8處長權限<br/>
	 * 9其他(經理)<br/>
	 * A董事會權限<br/>
	 * B營運中心營運長/副營運長權限<br/>
	 * C利費率變更案件由董事長核定<br/>
	 * D個金處經理權限
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/**
	 * 取得本案是否有同業聯貸案額度
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01B.unitCase<br/>
	 * Y/N（是/否）
	 */
	public String getUnitCase() {
		return this.unitCase;
	}

	/**
	 * 設定本案是否有同業聯貸案額度
	 * <p/>
	 * 100/12/07新增<br/>
	 * 來源：L120M01B.unitCase<br/>
	 * Y/N（是/否）
	 **/
	public void setUnitCase(String value) {
		this.unitCase = value;
	}

	/**
	 * 取得聯行經辦
	 * <p/>
	 * 來源：L120M01F.staffNo<br/>
	 * branchType=1 & staffJob=L1
	 */
	public String getCoAppraiser() {
		return this.coAppraiser;
	}

	/**
	 * 設定聯行經辦
	 * <p/>
	 * 來源：L120M01F.staffNo<br/>
	 * branchType=1 & staffJob=L1
	 **/
	public void setCoAppraiser(String value) {
		this.coAppraiser = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}
}
