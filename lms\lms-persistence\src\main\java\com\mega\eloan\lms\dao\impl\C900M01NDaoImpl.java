/* 
 * C900M01MDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C900M01NDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01N;

/** 分行可分案人員檔 **/
@Repository
public class C900M01NDaoImpl extends LMSJpaDao<C900M01N, String> implements
		C900M01NDao {

	@Override
	public C900M01N findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01N> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C900M01N> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C900M01N> findByIndex01(String brNo) {
		ISearch search = createSearchTemplete();
		List<C900M01N> list = null;
		if (brNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M01N> findByBrNoAndAssignEmpNo(String brNo,
			String[] assignEmpNos) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		search.addSearchModeParameters(SearchMode.IN, "assignEmpNo",
				assignEmpNos);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C900M01N> list = createQuery(search).getResultList();
		return list;
	}

}