package com.mega.eloan.lms.lns.pages;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.AbstractFileDownloadPage;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.lns.report.impl.LMS1201R01RptServiceImpl;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * 提供notes直接下載pdf
 * 
 * security.xml要增加設定
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/simple/FileProcessingPageForLms1201R46")
public class FileProcessingPageForLms1201R46 extends
		AbstractFileDownloadPage {
	
	@Autowired
    private HttpServletRequest request;
	
	@Autowired
	L120M01ADao l120m01aDao;

	@Autowired
	LMS1201Service service1201;

	@Autowired
	SysParameterService sysParameterService;

	public FileProcessingPageForLms1201R46(PageParameters parameters) {

		super();

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * tw.com.iisi.cap.base.pages.AbstractCapPage#execute(org.apache.wicket.
	 * PageParameters)
	 */
	@Override
	public void execute(ModelMap model, PageParameters params) {
		this.fileDownloadName = params.getString("fileDownloadName");
		this.serviceName = params.getString("serviceName");

		// http://**************:9081/lms-web/app/simple/FileProcessingPageForLms1201R46?random=0.7614665330930181&mainId=c8363c49d2c840f4b97da4397cdf41ec

		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();

		String remoteHost = request.getRemoteHost();

		String ipList = this.sysParameterService
				.getParamValue("SYS_FULLSEARCH_ALLOW_IP_LIST");
		LOGGER.info("[isVaildIP]IP_LIST=[{}] SRC_IP=[{}] ", ipList, remoteHost);
		if (ipList.indexOf(remoteHost) == -1) {
			throw new RuntimeException("使用者IP錯誤");
		}

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);

		if (l120m01a == null) {
			throw new RuntimeException("簽報書不存在:" + mainId);
		}

		if (!Util.equals(l120m01a.getDocType(),
				UtilConstants.Casedoc.DocType.企金)) {
			throw new RuntimeException("消金簽報書無此功能:" + mainId);
		}

		Map<String, Object> data = null;
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();

		data = new HashMap<String, Object>();

		Properties rptProperties = MessageBundleScriptCreator
				.getComponentResource(LMS1201R01RptServiceImpl.class);

		data.put(
				"custName",
				Util.nullToSpace(l120m01a.getCustId()) + " "
						+ Util.nullToSpace(l120m01a.getDupNo()) + " "
						+ Util.nullToSpace(l120m01a.getCustName()));
		data.put("cntrNo", "");

		data.put("rptName", "");
		data.put("oid", "");
		data.put("refMainId", "");
		data.put("rpt", "R46A");
		data.put("rptNo", "LMS1205R46");
		data.put("custId", "");
		data.put("dupNo", "");
		beanList.add(data);

		StringBuffer newRptOid = new StringBuffer("");
		if (beanList != null && !beanList.isEmpty()) {
			for (Map<String, Object> beanMap : beanList) {
				String content = Util.trim(MapUtils.getString(beanMap, "rpt",
						""))
						+ "^"
						+ Util.trim(MapUtils.getString(beanMap, "oid", ""))
						+ "^"
						+ Util.trim(MapUtils.getString(beanMap, "custId", ""))
						+ "^"
						+ Util.trim(MapUtils.getString(beanMap, "dupNo", ""))
						+ "^"
						+ Util.trim(MapUtils.getString(beanMap, "cntrNo", ""))
						+ "^"
						+ Util.trim(MapUtils
								.getString(beanMap, "refMainId", "")) + "|";
				newRptOid.append(content);
			}
		}

		String rptOid = Util.trim(newRptOid.toString());

		if (Util.notEquals(rptOid, "")) {
			rptOid = rptOid.substring(0, StringUtils.length(rptOid) - 1);
		}

		params.add("rptOid", rptOid);
		String tfileDownloadName = "LMS1201R46.pdf";
		if (!params.containsKey("serviceName")) {
			String tserviceName = "";

			if (Util.equals(l120m01a.getTypCd(), UtilConstants.Casedoc.typCd.海外)) {
				tserviceName = "lms1205r01rptservice";
				tfileDownloadName = "LMS1205R46.pdf";
			} else {
				tserviceName = "lms1201r01rptservice";
				tfileDownloadName = "LMS1201R46.pdf";
			}

			this.serviceName = tserviceName;
			params.add("serviceName", tserviceName);
		}

		if (!params.containsKey("fileDownloadName")) {
			this.fileDownloadName = tfileDownloadName;
			params.add("fileDownloadName", tfileDownloadName);
		}

		//UPGRADETODO 下面寫法要改寫，之後研究
		//getRequestCycle().setRequestTarget(
				//new ResourceStreamRequestTarget(new FileDownloadStreamWriter(
						//params)));

	}

	@Override
	public String getDownloadFileName() {
		return this.fileDownloadName;
	}

	@Override
	public String getFileDownloadServiceName() {
		return this.serviceName;
	}
	
	@Override
	//UPGRADE
	protected String getViewName() {
		return null;
	}
	
	//UPGRADETODO 下面寫法要改寫，之後研究
//	final class FileDownloadStreamWriter extends AbstractResourceStreamWriter {
//
//		private static final long serialVersionUID = 1L;
//		private PageParameters params;
//
//		/**
//		 * constructor
//		 * 
//		 * @param params
//		 *            PageParameters
//		 */
//		public FileDownloadStreamWriter(PageParameters params) {
//			super();
//			this.params = params;
//		}
//
//		/*
//		 * (non-Javadoc)
//		 * 
//		 * @see
//		 * org.apache.wicket.util.resource.IResourceStreamWriter#write(java.
//		 * io.OutputStream)
//		 */
//		public void write(OutputStream output) {
//			try {
//				output.write(getContent(params));
//				output.flush();
//			} catch (Exception ex) {
//				LOGGER.error(ex.getMessage(), ex);
//				throw new RuntimeException(ex);
//			}
//		}
//
//		/*
//		 * (non-Javadoc)
//		 * 
//		 * @see org.apache.wicket.util.resource.IResourceStream#getContentType()
//		 */
//		public String getContentType() {
//			return getFileContentType();
//		}
//
//	}
}
