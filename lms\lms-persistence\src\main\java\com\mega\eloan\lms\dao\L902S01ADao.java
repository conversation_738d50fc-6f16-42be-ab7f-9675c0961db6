/* 
 * L902S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L902S01A;

/** 私募基金旗下事業明細檔 **/
public interface L902S01ADao extends IGenericDao<L902S01A> {

	L902S01A findByOid(String oid);

	List<L902S01A> findByMainId(String mainId);

	List<L902S01A> findByIndex01(String mainId);

	List<L902S01A> findByPeNo(String peNo);

	List<L902S01A> findByCustId(String custId, String dupNo);

	public List<L902S01A> findByRptMainIdAndCustId(String rptMainId,
			String custId, String dupNo);

	public List<L902S01A> findByCustIdAndPeNo(String custId, String dupNo,
			String peNo);

	List<L902S01A> findByMainIdIgnoreDelete(String mainId);
}