/* 
 * CLS1131Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.gwclient.Brmp005O;
import com.mega.eloan.common.gwclient.IdentificationCheckGwReqMessage;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01B;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01G_N;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01J;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01R_N;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C101S01V;
import com.mega.eloan.lms.model.C101S01W;
import com.mega.eloan.lms.model.C101S01X;
import com.mega.eloan.lms.model.C101S01Y;
import com.mega.eloan.lms.model.C101S02C;
import com.mega.eloan.lms.model.C101S02S;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01W;
import com.mega.eloan.lms.model.C120S01X;
import com.mega.eloan.lms.model.C120S01Y;
import com.mega.eloan.lms.model.C120S02C;
import com.mega.eloan.lms.model.C120S02S;
import com.mega.eloan.lms.model.EJCICCOMMON;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L120S01N;
import com.mega.eloan.lms.model.L120S01O;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
public interface CLS1131Service extends CLS1130Service {

	/**
	 * save
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param list
	 */
	void save(String mainId, String custId, String dupNo, String type,
			List<GenericBean> list);

	/**
	 * save
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param list
	 * @param del_classes 進行save前，要先刪除的 table(例：C101S01H, C101S01I)
	 */
	void save(String mainId, String custId, String dupNo,
			List<GenericBean> list, Class<?>... del_classes);

	/**
	 * 取得個金徵信借款人主檔資料
	 * 
	 * @param ownBrId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	C101M01A findC101M01A(String ownBrId, String custId, String dupNo);

	/**
	 * 取得MIS DB之借款人資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> findMisCustData(String custId, String dupNo);

	/**
	 * 取得MIS 配偶之借款人資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> findMisMateData(String custId, String dupNo);

	/**
	 * 解析5碼郵地區號
	 * 
	 * @param zip
	 * @return
	 */
	JSONObject parseZip(String zip);

	/**
	 * 資料查詢
	 * 
	 * @param dataList
	 * @param custId
	 * @param mainId
	 * @param dupNo
	 * @param type
	 * @return
	 */
	JSONObject queryData(List<GenericBean> dataList, C101S01E c101s01e, C101S01J c101s01j,
			PageParameters params) throws CapMessageException;

	/**
	 * 取得聯徵和票信信查詢結果(HTML)
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<GenericBean> getHtml(String mainId, String custId, String dupNo, String prodId);
	
	void deleteL120s01mno(String mainId, String custId,String dupNo);
	L120S01M findL120s01m(String mainId, String custId,String dupNo);
	
	List<L120S01N> findL120s01nByCustId(String mainId, String custId,
			String dupNo);
	
	List<L120S01O> findL120s01oByCustIdRelType(String mainId, String custId,
			String dupNo, String relType);
	/**
	 * 判斷評分因子是否改變
	 * ● 未改變 ，回傳 true
	 * ● 有改變 ，回傳 false
	 * 
	 * @param model
	 * @param json
	 * @return
	 */
	boolean unChg_RatingFactor_G(GenericBean model, JSONObject json, String version_G);
	boolean unChg_RatingFactor_Q(GenericBean model, JSONObject json, String version_NotHouseLoan);
	boolean unChg_RatingFactor_R(GenericBean model, JSONObject json, String version_R);
	
	public CapAjaxFormResult loadScore_G(C101S01G model_g) throws CapException;
	public CapAjaxFormResult loadScore_Q(C101S01Q model_q) throws CapException;
	public CapAjaxFormResult loadScore_Q(C120S01Q model_q) throws CapException;
	public CapAjaxFormResult loadScore_R(C101S01R model_r) throws CapException;
	
	public CapAjaxFormResult loadScore_GN(C101S01G_N model_gn) throws CapException;
	public CapAjaxFormResult loadScore_QN(C101S01Q_N model_qn) throws CapException;
	public CapAjaxFormResult loadScore_RN(C101S01R_N model_rn) throws CapException;
	
	public String changCityR (String key);
	public String changTownR (String key);
	public String changLeeR (String key);
	
	public void c122m01aStatusTo0B0(String ownBrId, String[] applyKind_arr, String custId, String dupNo, String empNo);
	public String[] parseAddr(String fullStr);
	
	public void injectAbnormalDocParam(CapAjaxFormResult result, L120M01A l120m01a, String injectKey);
	
	public Map<String, Object> getCreditAnomalyData(String custId, String dupNo, String custName) throws CapFormatException;
	
	public C101S01S modifyC101S01SForMixRecordData(String mainId, String custId, String dupNo, String dataType, String dataStatus, byte[] loanCreditDesFile);

	public Map<String, Object> getStakeholderData(String custId, String dupNo, String custName) throws CapMessageException;
	
	public Map<String, Object> getRefusedData(String custId, String dupNo, String custName) throws CapFormatException;
	
	public byte[] getReportOfCreditAnomalyData(Map<String, Object> map) throws FileNotFoundException, IOException, Exception;
	
//	public List<Map<String, Object>> loadDataArchivalRecordData(String mainId);
	
	public Map<String, Object> getCURIQ01(String mainId, String custId) throws CapMessageException;
	
	public String sync_ej_data_to_C101S01E(C101M01A c101m01a);
	
	/**
	 * 是否為JSON格式
	 * @param byteArr
	 * @return
	 */
	public boolean isJSON(byte[] byteArr);
	public List<C101S01V> findC101S01VByMainid(String mainId,String ownBrId,String CustID,String Dupno);

	/**
	 * 個人收入明細欄位
	 * @return
	 */
	String[] getPersonalIncomeDetailColumns();

	/**
	 * 計算個人收入明細
	 * @param s01b
	 * @throws CapException
	 */
	void calPersonalIncomeDetail(C101S01B s01b) throws CapException;

	/**
	 * 計算個人收入明細 V1版
	 * @param s01b
	 * @param jsObject
	 * @throws CapException
	 */
	void calPersonalIncomeDetailV1(C101S01B s01b, JSONObject jsObject) throws CapException;

	/**
	 * 儲存收入明細，依消金處訂義的公式，計算各收入明細
	 *
	 * @param s01w
	 */
	void calPersonalIncomeItem(C101S01W s01w);

	/**
	 * 儲存收入明細(重新計算加檢核)
	 *
	 * @param s01b
	 * @throws CapException
	 */
	void calPersonalIncomeDetailV2(C101S01B s01b) throws CapException;

	/**
	 * RPA地政士查詢
	 * @param mainId
	 * @param queryLaaName
	 * @throws CapMessageException 
	 */
	void queryRpaQueryLaaName(String mainId, String queryLaaName) throws CapMessageException;

	/**
	 * RPA地政士引入
	 * @param mainId
	 * @param oid
	 * @return
	 */
	CapAjaxFormResult importRpaDetail(String mainId, String oid);

	CapAjaxFormResult importRpaDetailByAgent(String mainId, String oid,
			String laaName, String laaYear, String laaWord, String laaNo,
			String laaOffice);
	
	public String gotoRPAJobs_04(String c101m01a_mainId, String custId, String dupNo, String token, String functionCode) throws CapException;
	
	public byte[] generateJSONObjectForIdCardCheck(IdentificationCheckGwReqMessage req, Calendar currentDateTime) throws Exception;

	public void computeScoreForLabourBailout(String custId, String queryBranch, C101S01X c101s01x, int b1Score, int c1Score);

	public void processIsApprovedFlagAndFlowFlag(boolean isApproved, C101S01X c101s01x);

	public C101S01X getC101S01X(String mainId, String custId, String dupNo);
	public C120S01X getC120S01X(String mainId, String custId, String dupNo);

	boolean checkIsApprovedForLabourBailout(C101S01X c101s01x, boolean isManualReview);

	public void checkForLabourBailout4(String applyDate) throws CapMessageException;

	public C101S01X getC101S01XObject(String mainId, String custId, String dupNo);

	public int findC122m01aAgreeQueryEJMtelCount(String agreeQueryEJMtel, String applyTs);

	public int findC122m01aAgreeQueryEJIpCount(String agreeQueryEJIp, String applyTs);

	public void saveC101S01X(C101S01X c101s01x);

	public void saveC101S01X(C101S01X c101s01x, String applyDate, String applyQuota);

	public void processEjcicAndEtchDataForC101S01X(String c101m01a_mainId, String custId, String dupNo, String queryBranch, C101S01X c101s01x) throws CapMessageException;

	public C101S01Y getC101S01Y(String oid);

	public C120S01Y getC120S01Y(String oid);
	
	public void saveC101S01Y(C101S01Y c101s01y);
	
	public void saveC120S01Y(C120S01Y c120s01y);

	Page<C101S01Y> getC101S01YPage(ISearch search);

	Page<C101S01W> getC101S01WPage(ISearch search);

	Page<C120S01Y> getC120S01YPage(ISearch search);

	Page<C120S01W> getC120S01WPage(ISearch search);

	void deleteC101S01Y(C101S01Y c101s01y);

	void deleteC120S01Y(C120S01Y c120s01y);

	List<C101S01Y> getC101S01YList(String mainId, String custId, String dupNo);

	List<C120S01Y> getC120S01YList(String mainId, String custId, String dupNo);

	C120S01Y getC120S01Y(String mainId, String custId, String dupNo,
			String LAAYEAR, String LAAWORD, String LAANO);

	C101S01Y getC101S01Y(String mainId, String custId, String dupNo,
			String LAAYEAR, String LAAWORD, String LAANO);
	public Map<String, Object> getEjcicReusltRecord(String custId, String prodId, String qDate, String mainId, String dupNo);

	public String checkIsQueryEjcicS11(String c101m01a_mainId, String custId, String dupNo);
	
	/**
	 * 判斷手機號碼是否該查詢WF
	 * @param mTel
	 * @return
	 * @throws CapMessageException 
	 */
	public boolean isNeedQueryWitcherFin(String mTel) throws CapMessageException;
	
	/**
	 * 查詢WITCHER FIN資料並存檔到資料庫中
	 * @throws CapMessageException 
	 */
	public void queryWitcherFin(String mainId, String custId, String dupNo, String mTel, JSONObject result) throws CapMessageException;
	
	List<EJCICCOMMON> findEJCICCOMMONList(String custId, String prodId, String txId, String qdate);
	List<C101S01H> findC122S01HList(String mainId);
	List<C101S01U> findC122S01UList(String mainId, String custId, String dupNo, String txId);
	void findC122S01ItoC101S01H_U(String mainId, String custId, String dupNo, String[] qdate_array);
	
	
	public CapAjaxFormResult mappingClsJob (String clsJobType1Val ,String clsJobType2Val,String clsJobTitleVal,String capital,boolean isNPO)throws CapMessageException;

	public void saveC101S02S(String mainId, String custId, String dupNo, String branchNo);

	public boolean isQueryEjcicT70Info(String custId, String dupNo, String branchNo, String mainId);

	public C101S02S getC101s02sByUniqueKey(String mainId, String custId, String dupNo);

	public void deleteC101S02S(String mainId, String custId, String dupNo);

	public C120S02S getC120s02sByUniqueKey(String mainId, String custId, String dupNo);

	void setC101s01eVersion(String version, C101M01A c101m01a);
	public Map<String,String> findTermGroup(String mainId,String custId,String dupNo);

	boolean isCloseFinHoldingDefaultDeliveryFunction();

	public C101S02S copyNewC101S02SAndC101S01U(String c101m01a_mainId, C101S02S c101s02s) throws CapException;
	
	public CapAjaxFormResult loadScoreSDT(CapAjaxFormResult formResult, String markModel, String mainId, String custId, String dupNo) throws CapException;
	
	public void newScoreModel_01(CapAjaxFormResult formResult, String[] scoreArr, String[] HighGapArr, String[] InfluenceArr, String[] HighScoreArr);
	public C101S02C findC101S02C(String mainId);
	public C120S02C findC120S02C(String mainId);
	public Brmp005O getAutoCheck(Brmp005O brmp005o_obj) throws CapException;
	
	/**
	 * J-113-0341 個金徵信作業新增「年輕族群客戶加強關懷提問單」,計算年紀
	 * @param birthday
	 * @return
	 */
	public int getAge(Date birthday);
}
