/* 
 * LMS1815V04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthType;


/**<pre>
 * 授信簽報書總行待覆核
 * </pre>
 * @since  2011/10/19
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205v26")
public class LMS1205V26Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		//設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_提會待覆核);
		if (this.getAuth(AuthType.Accept)) {
			// 主管權限時要顯示的按鈕...
			addToButtonPanel(model, LmsButtonEnum.View);
		} else {
			// 否則需要顯示的按鈕
			// 加上Button
			addToButtonPanel(model, LmsButtonEnum.View);
		}		
		//套用哪個i18N檔案
		renderJsI18N(LMS1205V01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS1205V01Page');");
	}// ;

}
