package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 核准額度資料檔  MIS.ELF447n
 * </pre>
 * 
 * @since 2012/6/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/6/8,REX,new
 *          </ul>
 */
public interface MisELF447xService {
	/**
	 * 
	 * 新增 企金
	 * 
	 * @param ELF447N_UNID
	 *            文件編號
	 * @param ELF447N_PROJECT_NO
	 *            案件編號
	 * @param ELF447N_CLASS
	 *            報案類別 1. 授信 ( 預設 ) 2. 應收帳款無追索 3. 衍生性金融商品
	 * @param ELF447N_CUSTID
	 *            客戶統編
	 * @param ELF447N_DUPNO
	 *            重覆序號
	 * @param ELF447N_STATUS
	 *            文件狀態 0- 預約 ( 預約到期日內有效 ) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 ( 核定日起六個月有效
	 *            )A- 已撤銷B- 已婉卻C- 已退件
	 * @param ELF447N_STATUS_DT
	 *            狀態日0- 預約到期日 1- 報核日 2- 核定日A- 撤銷日 B- 婉卻日
	 * @param ELF447N_CONTRACT
	 *            額度序號
	 * @param ELF447N_BRANCH
	 *            主辦分行代號
	 * @param ELF447N_PROCESS_BR
	 *            目前案件所在分行代號
	 * @param ELF447N_FACT_TYPE
	 *            '額度控管種類
	 * @param ELF447N_SYSTYPE
	 *            系統類別 1- 企金 2- 個金
	 * @param ELF447N_CASELEVEL
	 *            授權等級
	 * @param ELF447N_TMESTAMP
	 *            資料修改日期
	 * @param ELF447N_PROPERTY
	 *            授信性質別
	 * @param ELF447N_CURAMT
	 *            現請額度金額
	 * @param ELF447N_CURR
	 *            現請額度幣別
	 * @param ELF447N_OLDAMT
	 *            前請額度金額
	 * @param ELF447N_OLDCURR
	 *            前請額度幣別
	 * @param ELF447N_GRPNO
	 *            集團代碼
	 * @param ELF447N_RISK_CNTRY
	 *            風險國別
	 * @param ELF447N_RISK_AREA
	 *            風險區域別
	 * @param ELF447N_BUS_CD
	 *            行業對象別
	 * @param ELF447N_BUS_SUB_CD
	 *            行業對象別細分類
	 * @param ELF447N_BUILD_NAME
	 *            團貸案名稱
	 * @param ELF447N_SITE1
	 *            土地座落 (縣市)
	 * @param ELF447N_SITE2
	 *            土地座落 (鄉鎮)
	 * @param ELF447N_SITE3
	 *            土地座落 (段)
	 * @param ELF447N_SITE4土地座落
	 *            (小段)
	 * @param ELF447N_NUSEDATE
	 *            'DATE 未動用原因維護日
	 * @param ELF447N_NUSEMEMO
	 *            由簽約未動用報送來 FLMS230M01
	 * @param ELF447N_INT_MEMO
	 *            varch(255) 利率 MEMO
	 * @param ELF447N_RESIDENce
	 *            是否興建住宅
	 */
	public void insert(String ELF447N_UNID, String ELF447N_PROJECT_NO,
			String ELF447N_CLASS, String ELF447N_CUSTID, String ELF447N_DUPNO,
			String ELF447N_STATUS, String ELF447N_STATUS_DT,
			String ELF447N_CONTRACT, String ELF447N_BRANCH,
			String ELF447N_PROCESS_BR, String ELF447N_FACT_TYPE,
			String ELF447N_SYSTYPE, String ELF447N_CASELEVEL,
			String ELF447N_PROPERTY, BigDecimal ELF447N_CURAMT,
			String ELF447N_CURR, BigDecimal ELF447N_OLDAMT,
			String ELF447N_OLDCURR, String ELF447N_GRPNO,
			String ELF447N_RISK_CNTRY, String ELF447N_RISK_AREA,
			String ELF447N_BUS_CD, String ELF447N_BUS_SUB_CD,
			String ELF447N_BUILD_NAME, String ELF447N_SITE1,
			String ELF447N_SITE2, String ELF447N_SITE3, String ELF447N_SITE4,
			String ELF447N_NUSEDATE, String ELF447N_NUSEMEMO,
			Timestamp ELF447N_TMESTAMP, String ELF447N_INT_MEMO,
			String ELF447N_RESIDENce, String ELF447N_PROD_CLASS,
			BigDecimal ELF447N_LAND_AREA, Date ELF447N_BUILD_DATE,
			BigDecimal ELF447N_WAIT_MONTH, String ELF447N_LOCATE_CD,
			BigDecimal ELF447N_SITE3NO, String ELF447N_SITE4NO,
			String ELF447N_LAND_TYPE, String ELF447N_PROPERTIES,
			String ELF447N_REVIEWBR, String ELF447N_ISHEDGE,
			BigDecimal ELF447N_ENHANCEAMT, String ELF447N_PROJ_CLASS,
			String ELF447N_ENDDATE, String ELF447N_RGTCURR,
			BigDecimal ELF447N_RGTAMT, BigDecimal ELF447N_RGTUNIT,
			String ELF447N_COND_CHG, String ELF447N_INTREG,
			String ELF447N_ISRESCUE, String ELF447N_RESCUEITEM,
			BigDecimal ELF447N_RESCUERATE, String ELF447N_ACT_CODE,
			String ELF447N_OVAUTH_LN, String ELF447N_OVAUTH_EX,
			String ELF447N_OVAUTH_AL, String ELF447N_LNNOFLAG,
			BigDecimal ELF447N_CURAMT_S, BigDecimal ELF447N_CURAMT_N); // 在ELF447N新增欄位時，除了在
	// misSQL.xml
	// 加欄位之外，在
	// ELF447N
	// 也應同時增加欄位。避免
	// delete/insert後，原本有值的欄位被清成null

	/**
	 * 刪除by 借款人資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 */
	public void delByCustKey(String custId, String dupNo, String cntrNo);

	/**
	 * 刪除byOid
	 * 
	 * @param oid
	 *            簽報書mainid
	 * 
	 */
	public void deleteByunId(String oid);

	/**
	 * 查詢ELF447N
	 * 
	 * @param unid
	 *            簽報書mainid
	 * @return ELF447N 資料
	 */
	/**
	 * @param ELF447N_STATUS
	 *            文件狀態
	 * @param ELF447N_STATUS_DT
	 *            狀態日
	 * @param ELF447N_NUSEDATE
	 *            未動用原因維護日
	 * @param ELF447N_NUSEMEMO
	 *            簽約未動用原因 VCHAR(804)
	 * @param ELF447N_NSIGN_CODE
	 *            不簽約原因 代碼 // 01;02;03;04;05;06;07;…
	 * @param ELF447N_NSIGN_MEMO
	 *            不簽約原因說明(點選第99項時，於100文字以內由營業單位鍵入簡要說明)
	 * @param unid
	 *            簽報書mainId
	 * @param cntrNo
	 *            額度序號
	 * 
	 *            J-111-0551_05097_B1003 Web
	 *            e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
	 * 
	 * @return
	 */
	public int updateByUnidAndContract(String ELF447N_STATUS,
			String ELF447N_STATUS_DT, String ELF447N_NUSEDATE,
			String ELF447N_NUSEMEMO, String ELF447N_NSIGN_CODE,
			String ELF447N_NSIGN_MEMO, String unid, String cntrNo,
			BigDecimal ELF447N_CURAMT_S, BigDecimal ELF447N_CURAMT_N);

	/**
	 * 
	 * 新增 個金
	 * 
	 * @param ELF447N_UNID
	 *            文件編號
	 * @param ELF447N_PROJECT_NO
	 *            案件編號
	 * @param ELF447N_CLASS
	 *            報案類別 1. 授信 ( 預設 ) 2. 應收帳款無追索 3. 衍生性金融商品
	 * @param ELF447N_CUSTID
	 *            客戶統編
	 * @param ELF447N_DUPNO
	 *            重覆序號
	 * @param ELF447N_STATUS
	 *            文件狀態 0- 預約 ( 預約到期日內有效 ) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 ( 核定日起六個月有效
	 *            )A- 已撤銷B- 已婉卻C- 已退件
	 * @param ELF447N_STATUS_DT
	 *            狀態日0- 預約到期日 1- 報核日 2- 核定日A- 撤銷日 B- 婉卻日
	 * @param ELF447N_CONTRACT
	 *            額度序號
	 * @param ELF447N_BRANCH
	 *            主辦分行代號
	 * @param ELF447N_PROCESS_BR
	 *            目前案件所在分行代號
	 * @param ELF447N_FACT_TYPE
	 *            '額度控管種類
	 * @param ELF447N_SYSTYPE
	 *            系統類別 1- 企金 2- 個金
	 * @param ELF447N_CASELEVEL
	 *            授權等級
	 * @param ELF447N_TMESTAMP
	 *            資料修改日期
	 * @param ELF447N_PROPERTY
	 *            授信性質別
	 * @param ELF447N_CURAMT
	 *            現請額度金額
	 * @param ELF447N_CURR
	 *            現請額度幣別
	 * @param ELF447N_OLDAMT
	 *            前請額度金額
	 * @param ELF447N_OLDCURR
	 *            前請額度幣別
	 * @param ELF447N_GRPNO
	 *            集團代碼
	 * @param ELF447N_RISK_CNTRY
	 *            風險國別
	 * @param ELF447N_RISK_AREA
	 *            風險區域別
	 * @param ELF447N_BUS_CD
	 *            行業對象別
	 * @param ELF447N_BUS_SUB_CD
	 *            行業對象別細分類
	 * @param ELF447N_BUILD_NAME
	 *            團貸案名稱
	 * @param ELF447N_SITE1
	 *            土地座落 (縣市)
	 * @param ELF447N_SITE2
	 *            土地座落 (鄉鎮)
	 * @param ELF447N_SITE3
	 *            土地座落 (段)
	 * @param ELF447N_SITE4土地座落
	 *            (小段)
	 * @param ELF447N_NUSEDATE
	 *            'DATE 未動用原因維護日
	 * @param ELF447N_NUSEMEMO
	 *            由簽約未動用報送來 FLMS230M01
	 * @param ELF447N_INT_MEMO
	 *            varch(255) 利率 MEMO 是否興建住宅
	 * @param ELF447N_RESIDENce
	 *            利率
	 * @param ELF447N_INT_Rate
	 *            產品
	 * @param ELF447N_PROD_CLASS
	 *            科目
	 * @param ELF447N_ACT_CODE
	 *            用途別
	 * @param ELF447N_PURPOSE
	 *            利率
	 */
	public void insertByCls(String ELF447N_UNID, String ELF447N_PROJECT_NO,
			String ELF447N_CLASS, String ELF447N_CUSTID, String ELF447N_DUPNO,
			String ELF447N_STATUS, String ELF447N_STATUS_DT,
			String ELF447N_CONTRACT, String ELF447N_BRANCH,
			String ELF447N_PROCESS_BR, String ELF447N_FACT_TYPE,
			String ELF447N_SYSTYPE, String ELF447N_CASELEVEL,
			String ELF447N_PROPERTY, BigDecimal ELF447N_CURAMT,
			String ELF447N_CURR, BigDecimal ELF447N_OLDAMT,
			String ELF447N_OLDCURR, String ELF447N_GRPNO,
			String ELF447N_RISK_CNTRY, String ELF447N_RISK_AREA,
			String ELF447N_BUS_CD, String ELF447N_BUS_SUB_CD,
			String ELF447N_BUILD_NAME, String ELF447N_SITE1,
			String ELF447N_SITE2, String ELF447N_SITE3, String ELF447N_SITE4,
			String ELF447N_NUSEDATE, String ELF447N_NUSEMEMO,
			Timestamp ELF447N_TMESTAMP, String ELF447N_INT_MEMO,
			String ELF447N_RESIDENce, BigDecimal ELF447N_INT_Rate,
			String ELF447N_PROD_CLASS, String ELF447N_ACT_CODE,
			String ELF447N_PURPOSE, BigDecimal ELF447N_LAND_AREA,
			Date ELF447N_BUILD_DATE, BigDecimal ELF447N_WAIT_MONTH,
			String ELF447N_LOCATE_CD, BigDecimal ELF447N_SITE3NO,
			String ELF447N_SITE4NO, String ELF447N_LAND_TYPE,
			String ELF447N_PROPERTIES, String ELF447N_ENDDATE,
			String ELF447N_RGTCURR, BigDecimal ELF447N_RGTAMT,
			BigDecimal ELF447N_RGTUNIT, String ELF447N_COND_CHG,
			String ELF447N_INTREG, String ELF447N_LNNOFLAG,
			BigDecimal ELF447N_CURAMT_S, BigDecimal ELF447N_CURAMT_N); // 在ELF447N新增欄位時，除了在
																		// misSQL.xml
																		// 加欄位之外，在
	// ELF447N 也應同時增加欄位。避免
	// delete/insert後，原本有值的欄位被清成null

	/**
	 * 
	 * 新增 企金
	 * 
	 * @param ELF447N_UNID
	 *            文件編號
	 * @param ELF447N_PROJECT_NO
	 *            案件編號
	 * @param ELF447N_CLASS
	 *            報案類別 1. 授信 ( 預設 ) 2. 應收帳款無追索 3. 衍生性金融商品
	 * @param ELF447N_CUSTID
	 *            客戶統編
	 * @param ELF447N_DUPNO
	 *            重覆序號
	 * @param ELF447N_STATUS
	 *            文件狀態 0- 預約 ( 預約到期日內有效 ) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 ( 核定日起六個月有效
	 *            )A- 已撤銷B- 已婉卻C- 已退件
	 * @param ELF447N_STATUS_DT
	 *            狀態日0- 預約到期日 1- 報核日 2- 核定日A- 撤銷日 B- 婉卻日
	 * @param ELF447N_CONTRACT
	 *            額度序號
	 * @param ELF447N_BRANCH
	 *            主辦分行代號
	 * @param ELF447N_PROCESS_BR
	 *            目前案件所在分行代號
	 * @param ELF447N_FACT_TYPE
	 *            '額度控管種類
	 * @param ELF447N_SYSTYPE
	 *            系統類別 1- 企金 2- 個金
	 * @param ELF447N_CASELEVEL
	 *            授權等級
	 * @param ELF447N_TMESTAMP
	 *            資料修改日期
	 * @param ELF447N_PROPERTY
	 *            授信性質別
	 * @param ELF447N_CURAMT
	 *            現請額度金額
	 * @param ELF447N_CURR
	 *            現請額度幣別
	 * @param ELF447N_OLDAMT
	 *            前請額度金額
	 * @param ELF447N_OLDCURR
	 *            前請額度幣別
	 * @param ELF447N_GRPNO
	 *            集團代碼
	 * @param ELF447N_RISK_CNTRY
	 *            風險國別
	 * @param ELF447N_RISK_AREA
	 *            風險區域別
	 * @param ELF447N_BUS_CD
	 *            行業對象別
	 * @param ELF447N_BUS_SUB_CD
	 *            行業對象別細分類
	 * @param ELF447N_BUILD_NAME
	 *            團貸案名稱
	 * @param ELF447N_SITE1
	 *            土地座落 (縣市)
	 * @param ELF447N_SITE2
	 *            土地座落 (鄉鎮)
	 * @param ELF447N_SITE3
	 *            土地座落 (段)
	 * @param ELF447N_SITE4土地座落
	 *            (小段)
	 * @param ELF447N_NUSEDATE
	 *            'DATE 未動用原因維護日
	 * @param ELF447N_NUSEMEMO
	 *            由簽約未動用報送來 FLMS230M01
	 * @param ELF447N_INT_MEMO
	 *            varch(255) 利率 MEMO
	 * @param ELF447N_RESIDENce
	 *            是否興建住宅
	 * @param ELF447N_NSIGN_CODE
	 *            簽約未動用原因 VCHAR(804)
	 * @param ELF447N_NSIGN_MEMO
	 *            不簽約原因說明(點選第99項時，於100文字以內由營業單位鍵入簡要說明)
	 */
	public void insertByL2305FLow(String ELF447N_UNID,
			String ELF447N_PROJECT_NO, String ELF447N_CLASS,
			String ELF447N_CUSTID, String ELF447N_DUPNO, String ELF447N_STATUS,
			String ELF447N_STATUS_DT, String ELF447N_CONTRACT,
			String ELF447N_BRANCH, String ELF447N_PROCESS_BR,
			String ELF447N_FACT_TYPE,

			String ELF447N_SYSTYPE, String ELF447N_CASELEVEL,
			String ELF447N_PROPERTY,

			BigDecimal ELF447N_CURAMT, String ELF447N_CURR,
			BigDecimal ELF447N_OLDAMT, String ELF447N_OLDCURR,
			String ELF447N_GRPNO, String ELF447N_RISK_CNTRY,
			String ELF447N_RISK_AREA, String ELF447N_BUS_CD,
			String ELF447N_BUS_SUB_CD,

			String ELF447N_BUILD_NAME, String ELF447N_SITE1,
			String ELF447N_SITE2, String ELF447N_SITE3, String ELF447N_SITE4,

			String ELF447N_NUSEDATE, String ELF447N_NUSEMEMO,
			Timestamp ELF447N_TMESTAMP, String ELF447N_INT_MEMO,
			String ELF447N_RESIDENce, String ELF447N_NSIGN_CODE,
			String ELF447N_NSIGN_MEMO, String ELF447N_PROD_CLASS,
			BigDecimal ELF447N_LAND_AREA, Date ELF447N_BUILD_DATE,
			BigDecimal ELF447N_WAIT_MONTH, String ELF447N_LOCATE_CD,
			BigDecimal ELF447N_SITE3NO, String ELF447N_SITE4NO,
			String ELF447N_LAND_TYPE, String ELF447N_PROPERTIES,
			String ELF447N_REVIEWBR, String ELF447N_ISHEDGE,
			BigDecimal ELF447N_ENHANCEAMT, String ELF447N_PROJ_CLASS,
			String ELF447N_ENDDATE, String ELF447N_RGTCURR,
			BigDecimal ELF447N_RGTAMT, BigDecimal ELF447N_RGTUNIT,
			BigDecimal ELF447N_CURAMT_S, BigDecimal ELF447N_CURAMT_N); // 在ELF447N新增欄位時，除了在
																		// misSQL.xml
																		// 加欄位之外，在
																		// ELF447N
																		// 也應同時增加欄位。避免
																		// delete/insert後，原本有值的欄位被清成null

	/**
	 * 武漢肺炎--刪除簽報書的額度明細表
	 * 
	 * @param oid
	 * @param cntrNo
	 */
	public void deleteByunIdAndCntrNo(String oid, String cntrNo);

	public List<Map<String, Object>> findByUnid(String ELF447N_UNID);

	public Map<String, Object> findByUnidAndCntrNo(String ELF447N_UNID,
			String ELF447N_CONTRACT);

	/**
	 * J-109-0470 配合海管處因應本行110年施行LTV法，海外分行Web
	 * e-Loan擔保品管理系統新增/修改相關欄位及ADC維護頁面等程式功能修改。
	 */
	public List<Map<String, Object>> getByCntrNoOrderByEndDateDesc(String cntrNo);

}
