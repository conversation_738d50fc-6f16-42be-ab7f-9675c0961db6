/**
 * 文件數位化共用js
 */
var initS08lJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		this.handlerName = "cls1141m01formhandler";		
	}
};

$(document).ready(function() {
	// 2012_07_20_rex add 取得sso 連線資訊
	setCloseConfirm(true);
	initS08lJson.setHandler();
	
	var mEGAImageListForm = $("#mEGAImageListForm");	
	
	//文件數位化影像清單
	var mEGAImageListGrid = mEGAImageListForm.find("#mEGAImageListGrid").iGrid({
	    caption: i18n.clss08a['L120S08L.title'], //'數位文件化影像清單',
	    handler: 'cls1141gridhandler',
	    height: 200,
	    width: 950,
	    needPager: false,
	    autowidth: false,
	    shrinkToFit: false,
	    grouping: true,
	    groupingView: {
	        groupField: ['CLCaseNo'],
	        groupColumnShow: [false]
	    },
	    postData: {
	        mainId: responseJSON.mainId,
	        formAction: 'queryMEGAImageList'
	    },
	    // 表單代碼	主借款人ID	關係人ID	關係	掃描日期時間	掃描人員	覆核日期時間	覆核人員	分行代碼	交易日期	影像編號	掃描作業選項
	    colModel: [{
	        colHeader: i18n.clss08a['L120S08L.grid.DormId'], // 表單代碼,
	        name: 'FormId',
	        formatter: 'click',
	        onclick: openMegaImage,
	        width: 200
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.Borrower'], // 主借款人ID,
	        name: 'Borrower',
	        width: 100
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.StakeholderID'], // 關係人ID,
	        name: 'StakeholderID'
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.Relationship'], // 關係,
	        name: 'Relationship'
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.ScanDateTime'], //'掃描日期時間'
	        name: 'ScanDateTime'
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.ScanUserId'], // 掃描人員,
	        name: 'ScanUserId'
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.VerifyDateTime'], // 覆核日期時間,
	        name: 'VerifyDateTime',
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.VerifyUserId'], // 覆核人員,
	        name: 'VerifyUserId',
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.Branch'], // 分行代碼,
	        name: 'Branch'
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.ApplicationDate'], // 交易日期,
	        name: 'ApplicationDate'
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.DocId'], // "影像編號",
	        name: 'DocId'
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.ScanType'], //"掃描作業選項"
	        name: 'ScanType'
	    }, {
	        colHeader: i18n.clss08a['L120S08L.grid.CLCaseNo'], // 案號,
	        name: 'CLCaseNo',
	        hidden: true
	    }, {
	        name: 'BranchCode',
	        hidden: true
	    }, {
	        name: 'UserCode',
	        hidden: true
	    }, {
	        name: 'CaseNo',
	        hidden: true
	    }, {
	        name: 'checksum',
	        hidden: true
	    }, {
	        name: 'id',
	        hidden: true
	    }, {
	        name: 'url',
	        hidden: true
	    }, {
	        name: 'Sender',
	        hidden: true
	    }]
	});
	
	//開啟數位文件化影像
	function openMegaImage(cellvalue, options, ret){
		if(ret.url){
			var checksum = ret.checksum.replace(/\r/g,"").replace(/\n/g,"");
			$.form.submit({
				url: ret.url,
				type: "POST",
				target: responseJSON.mainId,
				data: {
					'BranchCode': ret.BranchCode,
					'UserCode': ret.UserCode,
					'CaseNo': ret.CaseNo,
					'id': ret.id,
					'Sender': ret.Sender,
					'checksum': checksum
				}
			});
        }
    };

});
