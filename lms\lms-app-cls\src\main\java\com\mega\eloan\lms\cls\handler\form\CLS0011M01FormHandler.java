/* 
 * LMS0015FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.form;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.cls.service.CLS0011Service;
import com.mega.eloan.lms.model.L001M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 待辦事項
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,REX,new
 *          </ul>
 */
@Controller("cls0011m01formhandler")
public class CLS0011M01FormHandler extends AbstractFormHandler {

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLS0011Service cls0011Service;

	/**
	 * 查詢目前分行經辦跟主管
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public IResult queryPeople(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管,SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.經辦人員 };
		Map<String, String> m = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("peopleList", new CapAjaxFormResult(m));
		return result;
	}

	/**
	 * 儲存篩選條件
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public IResult saveFilter(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L001M01A l001m01a = cls0011Service.findL001m01a(user.getUserId());

		if (l001m01a == null) {
			l001m01a = new L001M01A();
			l001m01a.setUserId(user.getUserId());
		}
		String type = Util.nullToSpace(params.getString("type"));
		String selectDoc = Util.nullToSpace(params.getString("selectDoc"));
		String selectCase = Util.nullToSpace(params.getString("selectCase"));
		String sort = Util.nullToSpace(params.getString("sort"));
		l001m01a.setCaseType(selectCase);
		l001m01a.setDocStatus(selectDoc);
		l001m01a.setFilterType(type);
		l001m01a.setSortType(sort);
		switch (Util.parseInt(type)) {
		// 依文件建檔日期查詢
		case 1:
			Date startDate = Util.parseDate(Util.nullToSpace(params
					.getString("startDate")));
			Date endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate")));
			l001m01a.setStartDate(startDate);
			l001m01a.setEndDate(endDate);
			break;
		// 依客戶ID查詢
		case 2:
			String custId = Util.nullToSpace(params.getString("custId"));
			l001m01a.setCustId(custId);
			break;
		// 依經辦or主管查詢
		case 3:
			String mangerId = Util.nullToSpace(params.getString("mangerId"));
			l001m01a.setMangerId(mangerId);
			break;
		}
		String checkShow = Util.nullToSpace(params.getString("checkShow"));
		l001m01a.setCheckShow(checkShow);
		cls0011Service.save(l001m01a);
		return new CapAjaxFormResult();
	}

	/**
	 * 查詢是否有篩選條件
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public IResult queryFilter(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L001M01A l001m01a = cls0011Service.findL001m01a(user.getUserId());
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("show", true);

		// 無查詢篩選條件 直接出現"篩選視窗"
		if (l001m01a == null) {
			return result;
		}
		// 當查詢到儲存篩選條件 並勾選下次不顯示篩選視窗，直接帶入所有的篩選條件並執行grid
		if (UtilConstants.DEFAULT.是.equals(l001m01a.getCheckShow())) {
			result.set("show", false);
		}

		// 當查詢到儲存篩選條件 並無勾選下次不顯示視窗，跳出"篩選視窗"並把篩選的條件帶到視窗上
		result.set("type", l001m01a.getFilterType());
		result.set("selectDoc", l001m01a.getDocStatus());
		result.set("selectCase", l001m01a.getCaseType());
		result.set("startDate", l001m01a.getStartDate());
		result.set("endDate", l001m01a.getEndDate());
		result.set("custId", l001m01a.getCustId());
		result.set("mangerId", l001m01a.getMangerId());
		result.set("checkShow", l001m01a.getCheckShow());
		result.set("sort", l001m01a.getSortType());

		return result;
	}

	/**
	 * 案件改分派
	 * 
	 * @param params
	 *            PageParameters
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	public IResult changePeople(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String selectPeople = Util.trim(params.getString("selectPeople"));
		String selectCaseType = Util.trim(params.getString("selectCaseType"));
		String selectCaseOid = Util.trim(params.getString("selectCaseOid"));

		if ("L120M01A".equals(selectCaseType)) {
			L120M01A L120M01A = cls0011Service.findModelByOid(L120M01A.class,
					selectCaseOid);
			L120M01A.setUpdater(selectPeople);
			cls0011Service.save(L120M01A);
		} else if ("L141M01A".equals(selectCaseType)) {
			L141M01A L141M01A = cls0011Service.findModelByOid(L141M01A.class,
					selectCaseOid);
			L141M01A.setUpdater(selectPeople);
			cls0011Service.save(L141M01A);
		} else if ("L160M01A".equals(selectCaseType)) {
			L160M01A L160M01A = cls0011Service.findModelByOid(L160M01A.class,
					selectCaseOid);
			L160M01A.setUpdater(selectPeople);
			cls0011Service.save(L160M01A);
		}
		return result;
	}
}
