/* 
 *  LMSCOMMONGridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.grid;

import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.mfaloan.service.MisELF442Service;
import com.mega.eloan.lms.model.L140S07A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 共用LMS Grid
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lmscommongridhandler")
public class LMSCOMMONGridHandler extends AbstractGridHandler {

	@Resource
	LMSService lmsService;

	@Resource
	DocFileService docFileService;
	
	@Resource
	MisELF442Service misELF442Service;

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            mainId : 該文件mainId<Br/>
	 *            fieldId: 該文件fieldId
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryFile(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String fieldId = Util.nullToSpace(params.getString("fieldId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
	
	
	/**
	 * 查詢ELF442產品33/34預約資料
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult ELF442Query(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branchId = Util.nullToSpace(user.getUnitNo());
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String toDay = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		
		List<Map<String, Object>> list = null;
		list = misELF442Service.findELF442ByLandBuild(custId, dupNo, toDay);
		
		for (Map<String, Object> map : list) {
			//StringBuilder sb = new StringBuilder();			
			String tProdClass ="";			
			tProdClass= Util.trim(map.get("ELF442_PROD_CLASS"));
			
			String tCntrno = "";
			tCntrno = Util.trim(map.get("ELF442_CNTRNO"));
			tCntrno = tCntrno.substring(0, 3);
			
			Boolean sameBranch = false;
			
			if (branchId.equals(tCntrno)){
				sameBranch=true;
			}
			
			if (sameBranch && ("33".equals(tProdClass) || "34".equals(tProdClass))) {

				map.put("elf442_cntrno", Util.trim(map.get("ELF442_CNTRNO")));
				map.put("elf442_curr", Util.trim(map.get("ELF442_CURR")));
				map.put("elf442_quota", Util.trim(map.get("ELF442_QUOTA")));
				map.put("elf442_enddt", Util.trim(map.get("ELF442_ENDDT")));
				map.put("elf442_prod_class", Util.trim(map.get("ELF442_PROD_CLASS")));
				
				map.put("elf442_land_area", Util.trim(map.get("ELF442_LAND_AREA")));
				map.put("elf442_build_date", Util.trim(map.get("ELF442_BUILD_DATE")));
				map.put("elf442_wait_month", Util.trim(map.get("ELF442_WAIT_MONTH")));
				
				map.put("elf442_location_cd", Util.trim(map.get("ELF442_LOCATION_CD")));
				map.put("elf442_site3no", Util.trim(map.get("ELF442_SITE3NO")));
				map.put("elf442_site4no", Util.trim(map.get("ELF442_SITE4NO")));
				map.put("elf442_land_type", Util.trim(map.get("ELF442_LAND_TYPE")));
				
			}
		}
		return new CapMapGridResult(list, list.size());
	}
	
	/**
	 * 查詢購置高價住宅貸款檢核表
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryCheckListOfHighPricedHousingLoan(ISearch pageSetting, PageParameters params) throws CapException {
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String mainId = Util.nullToSpace(params.getString("l140m01aMainId"));
		List<Map<String, Object>> list = lmsService.queryCheckListOfHighPricedHousingLoan(mainId, custId, dupNo);
		return new CapMapGridResult(list, list.size());
	}

    @SuppressWarnings("unchecked")
    public CapGridResult queryL140s07aList(ISearch pageSetting, PageParameters params) throws CapException {
        // 查這份文件的MinId
        String mainId = Util.nullToSpace(params.getString("mainId"));

        // 取得文件狀態
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.MAIN_ID, mainId);
        pageSetting.setMaxResults(Integer.MAX_VALUE);

        Page<? extends GenericBean> page = lmsService.findPage(L140S07A.class, pageSetting);

        CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());

        return result;
    }
}
