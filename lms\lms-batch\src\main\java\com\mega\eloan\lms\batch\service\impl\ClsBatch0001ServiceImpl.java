package com.mega.eloan.lms.batch.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF506Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

/**
 * J-113-0417 配合修改LLMLN998消金授信案件例外管理報表，ELF500新增寫入家庭所得，回補舊案資料
 */
@Service("clsBatch0001ServiceImpl")
public class ClsBatch0001ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	EloandbBASEService eloandbService;

	@Resource
	LMSService lmsService;

	@Resource
	MisELF500Service misELF500Service;

	@Resource
	ObsdbELF506Service obsdbELF506Service;

	@Resource
	BranchService branchService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");

		String errMsg = this.doLmsBatch0001(request);
		if (Util.notEquals(errMsg, "")) {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE,
					"clsBatch0001ServiceImpl-doLmsBatch0001執行失敗！==>" + errMsg);
			return result;
		} else {
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"clsBatch0001ServiceImpl執行成功！");
		}

		return result;
	}

	// 取得ELF500需要更新約 家庭所得之額度序號清單後，以該額度序號取得最新一筆動審核准之額度對應的簽報書下的徵信資訊寫回ELF500
	@NonTransactional
	public String doLmsBatch0001(JSONObject request) {

		StringBuffer errMsg = new StringBuffer("");
		String eloanTime = Util.trim(request.getString("eloanTime"));
		int batch_cnt = Util.parseInt(request.optString("batch_cnt"));

		if (batch_cnt == 0) {
			batch_cnt = 1000;
		}

		if (Util.isEmpty(eloanTime)) {
			eloanTime = "2024-11-20 00:00:00";// 預設補更新日期為2024/11/19(含)以前的資料
		} else {
			eloanTime = eloanTime + " 00:00:00";// 補指定更新日期(不含)以前的資料
		}

		List<Map<String, Object>> elf500List = misELF500Service
				.findNeedUpdateFincomeData(eloanTime);

		if (elf500List != null && elf500List.size() > 0) {
			int runCount = 1;
			int updateCount = 1;
			int errorCount = 0;
			for (Map<String, Object> elf500Map : elf500List) {
				String custId = Util.trim(MapUtils.getObject(elf500Map,
						"ELF500_CUSTID"));
				String dupNo = Util.trim(MapUtils.getObject(elf500Map,
						"ELF500_DUPNO"));
				String elf500_cntrNo = Util.trim(MapUtils.getObject(elf500Map,
						"ELF500_CNTRNO"));
				String elf500_dt_source = Util.trim(MapUtils.getObject(
						elf500Map, "ELF500_DT_SOURCE"));
				String elf500_hincome = Util.trim(MapUtils.getObject(elf500Map,
						"ELF500_HINCOME"));
				logger.info("[clsBatch0001ServiceImpl-ELF500_CNTRNO => "
						+ elf500_cntrNo + " ; runCount => " + runCount);
				try {
					int fincome = 0;
					if (Util.equals("IL", elf500_dt_source)) {
						// ILOAN寫進去的就直接幫他補
						fincome = Integer.parseInt(elf500_hincome);
					} else {
						// 以額度序號取得最新一筆動審核准的家庭所得
						Map<String, Object> fincomeMap = eloandbService
								.findLatestFincomeByCntrNo(elf500_cntrNo,
										custId, dupNo);
						if (fincomeMap != null && Util.isNotEmpty(fincomeMap)) {
							String fincome_s = Util.nullToSpace(MapUtils
									.getString(fincomeMap, "FINCOME"));
							if (Util.isNotEmpty(fincome_s)) {
								fincome = Integer.parseInt(fincome_s);
								if(fincome > 99999){
									fincome = 99999;
								}
							}
						}
					}

					if (fincome > 0) {// 有資料再更新ELF500 家庭所得
						// 更新 ELF500 家庭所得
						misELF500Service.updateFincomeByCntrNo(fincome,
								elf500_cntrNo);
						logger.info("[clsBatch0001ServiceImpl-更新MIS.ELF500家庭所得欄位(ELF500_FINCOME)]，額度序號 => "
								+ elf500_cntrNo + "，家庭所得(FINCOME)=" + fincome);
						updateCount++;
					}

				} catch (Exception e) {
					errorCount++;
					logger.error("[clsBatch0001ServiceImpl] update ELF500_FINCOME Exception ====>"
							+ "額度序號 =>> " + elf500_cntrNo);
					if (Util.isEmpty(errMsg)) {
						errMsg.append(
								"[clsBatch0001ServiceImpl]-更新下列額度序號家庭所得欄位失敗 =>")
								.append(elf500_cntrNo);

					} else {
						errMsg.append("、").append(elf500_cntrNo);
					}
					if (errorCount == 30) {
						// 如果已經更新30筆失敗的話就先中斷批次
						break;
					}
				}
				if (updateCount == batch_cnt) {
					break;
				}
				runCount++;
			}
			logger.info("[clsBatch0001ServiceImpl] update ELF500_FINCOME 更新筆數(updateCount) ====>"
					+ updateCount);
		}
		return errMsg.toString();
	}

}
