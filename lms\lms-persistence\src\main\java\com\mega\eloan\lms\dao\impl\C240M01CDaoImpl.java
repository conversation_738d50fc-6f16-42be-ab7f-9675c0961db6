package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C240M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C240M01C;

/** 消金覆審名單抽樣資訊檔 **/
@Repository
public class C240M01CDaoImpl extends LMSJpaDao<C240M01C, String>
	implements C240M01CDao {

	@Override
	public C240M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C240M01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C240M01C> list = createQuery(C240M01C.class,search).getResultList();
		return list;
	}
	
	@Override
	public C240M01C findByMainIdRuleNo(String mainId, String ruleNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "ruleNo", ruleNo);
		return findUniqueOrNone(search);
	}
}