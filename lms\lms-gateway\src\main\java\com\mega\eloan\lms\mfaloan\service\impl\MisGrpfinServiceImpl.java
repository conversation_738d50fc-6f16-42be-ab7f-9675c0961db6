package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisGrpfinService;

@Service
public class MisGrpfinServiceImpl extends AbstractMFAloanJdbc implements
		MisGrpfinService {
	
	public List<?> findGrpfinJoinForRPTGroupData1(String grpNo) {
		return this.getJdbc().queryForList("MISGRPFIN.selRPTGroupData1",
				new String[] { grpNo });
	}
	
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisGrpfinService#findByGrpId(java.
	 * lang.String)
	 */
	@Override
	public List<Map<String, Object>> findByGrpId(String grpId) {
		return getJdbc().queryForList("GRPFIN.findByGrpId",
				new String[] { grpId });
	}	
}
