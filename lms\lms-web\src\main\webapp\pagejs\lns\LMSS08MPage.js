// J-112-0357 新增敘做條件異動比較表
var dfd1 = new $.Deferred();
var initS08mJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	},
	// 設定附加檔案內容
	fileSet : function(upFileId, fieldId, delFileId, fileGridId){
	    // 上傳檔案按鈕
        $("#" + upFileId).click(function(){
            var limitFileSize=9437103;
            MegaApi.uploadDialog({
                fieldId:fieldId,
                fieldIdHtml:"size='30'",
                fileDescId:"fileDesc",
                fileDescHtml:"size='30' maxlength='30'",
                subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
                limitSize:limitFileSize,
                width:320,
                height:190,
                data:{
                    mainId: responseJSON.mainId
                },
                success : function(obj) {
                    $("#" + fileGridId).trigger("reloadGrid");
                }
           });
        });
		// 刪除檔案按鈕
		$("#" + delFileId).click(function(){
			var select  = $("#" + fileGridId).getGridParam('selrow');		
			// confirmDelete=是否確定刪除?
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){				
					var data = $("#" + fileGridId).getRowData(select);
					if(data.oid == "" || data.oid == undefined || data.oid == null){		
						// TMMDeleteError=請先選擇需修改(刪除)之資料列
						CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
						return;
					}				
					$.ajax({
						handler : (this.handlerName == null) ? "lms1201formhandler" : this.handlerName,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "deleteUploadFile",
							fileOid:data.oid
						},
						success : function(obj) {
							$("#" + fileGridId).trigger("reloadGrid");
						}
					});
				}else{
					return ;
				}
			});
		});		
	},
	// 設定附加檔案Grid
	fileGrid : function(fileGridId, fieldId){
		// 檔案上傳grid
		$("#" + fileGridId).iGrid({
			handler : 'lms1201gridhandler',
			height : 50,
			sortname : 'srcFileName',
			postData : {
				formAction : "queryfile",
				fieldId: fieldId,
				mainId: responseJSON.mainId
			},
			rowNum : 15,
			caption: "&nbsp;",
			colModel : [ {
				colHeader : i18n.lmss08a['L120S08.grid21'],// 原始檔案名稱,
				name : 'srcFileName',
				width : 120,
				align: "left",
				sortable : false,
				formatter : 'click',
				onclick : openDoc
			}, {
				colHeader :  i18n.lmss08a['L120S08.grid22'],// 檔案說明
				name : 'fileDesc',
				width : 140,
				sortable : false
			}, {
				colHeader : i18n.lmss08a['L120S08.grid23'],// 上傳時間
				name : 'uploadTime',
				width : 140,
				sortable : false
			}, {
				name : 'oid',
				hidden : true
			}]
		});		
	}
};

$(document).ready(function() {
	
	setCloseConfirm(true);
	// 設定handler名稱
	initS08mJson.setHandler();
	
    var fileGridId = "lmss08m_gridFile";
	initS08mJson.fileSet("lmss08m_uploadFile", "condCompareReport", "lmss08m_deleteFile", fileGridId);
	 
	// 設定附加檔案Grid
	initS08mJson.fileGrid(fileGridId, "condCompareReport");
	
	//產生報表
	$("#lmss08m_generate").click(function(){
		lmss08m_generateReport(fileGridId);
	});
});

function lmss08m_generateReport(fileGridId){

    $.ajax({
        handler: "lms1201docservice",
        action: "saveCreatDoc",
        data: {
            mainId: responseJSON.mainId,
            fileName: "LMSDoc11A.htm",
            docTempType: "LMSDoc11A",
            fileDownloadName: "LMSDoc11A.doc",
            fieldId: "condCompareReport"
        },
        success: function(obj){
            $("#"+fileGridId).trigger("reloadGrid");
        }
    });
}

function openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}