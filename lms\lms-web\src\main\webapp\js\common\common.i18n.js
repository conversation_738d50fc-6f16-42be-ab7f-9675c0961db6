// i18n plugin
;(function($){
    var s = {};
    window['i18n'] = {
        load: function(f){
            $.ajax($.extend({
                async: false,
                dataType: 'script',
                cache: true,
                data: {
                    f: f
                }
            }, s));
        },
        set: function(key, jsonValue){
            $.extend(window['i18n'][key] = (function(value){
                return function(key, values){
                    var msg = value[key];
                    msg && values &&
                    $.each(values, function(i, v){
                        msg = msg.replace(new RegExp("\\${" + i + "\\}", "g"), v);
                    });
                    return msg;
                };
            })(jsonValue), jsonValue);
        },
        reset:function(){
        	for (var key in window['i18n']){
        		if (key =='load' || key=='set' || key=='reset' || key=='setup' || key=='def'){
        			continue;
        		}else{
        			window['i18n'][key] = null;
        		}
        	}
        },
        setup: function(settings){
            s = $.extend(true, s, settings);
        }
    };
})(jQuery);