/* 
 * RPS4035V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

/**
 * <pre>
 * 婉卻記錄查詢
 * </pre>
 * 
 * @since 2011/12/31
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller
@RequestMapping("/lms/rps4035v00")
public class RPS4035V00Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.DOC_EDITING);
		// UPGRADE: add(new Label("_buttonPanel"))若無影響，就可以刪掉
		// add(new Label("_buttonPanel", ""));
		renderJsI18N(RPS4035V00Page.class);
	}
	
	@Override
	protected String getContentPageName() {
		return "lms/pages/RPS4035V00Page";
	}
}
