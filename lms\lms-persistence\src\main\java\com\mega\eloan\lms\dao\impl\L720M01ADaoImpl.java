/* 
 * L720M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L720M01ADao;
import com.mega.eloan.lms.model.L720M01A;

/** 使用者自訂表格範本檔 **/
@Repository
public class L720M01ADaoImpl extends LMSJpaDao<L720M01A, String>
	implements L720M01ADao {

	@Override
	public L720M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L720M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L720M01A> list = createQuery(L720M01A.class, search).getResultList();
		return list;
	}
	
	@Override
	public L720M01A findByUniqueKey(String patternNM){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "patternNM", patternNM);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L720M01A> findByIndex01(String patternNM){
		ISearch search = createSearchTemplete();
		List<L720M01A> list = null;
		if (patternNM != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "patternNM", patternNM);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L720M01A.class, search).getResultList();
		}
		return list;
	}
}