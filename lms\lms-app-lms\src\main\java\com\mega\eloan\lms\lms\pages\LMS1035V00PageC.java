
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.panels.L120S01MPanel;
import com.mega.eloan.lms.lms.panels.LMS1035S02PanelC1;
import com.mega.eloan.lms.lms.panels.LMS1035S02PanelC2;
import com.mega.eloan.lms.lms.panels.LMS1035S02PanelC3;
import com.mega.eloan.lms.lms.panels.LMS1035S02PanelC4;
import com.mega.eloan.lms.lms.panels.LMS1035S02PanelC5;

/**
 * <pre>
 * 消金信用評等模型 （LMS1035V00PageC , LMS1035S02PageC）含相同Panel
 * ⇒ 原始參照 LMS1115S02PageC
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1035v00c/{page}")
public class LMS1035V00PageC extends AbstractOverSeaCLSPage {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		
		renderJsI18N(AbstractOverSeaCLSPage.class);
		
		model.addAttribute("_PanelC1_visible", true);
		new LMS1035S02PanelC1("PanelC1", true, true).processPanelData(model, params);

		model.addAttribute("_PanelC2_visible", true);
		new LMS1035S02PanelC2("PanelC2").processPanelData(model, params);

		model.addAttribute("_PanelC3_visible", true);
		new LMS1035S02PanelC3("PanelC3").processPanelData(model, params);

		model.addAttribute("_PanelC4_visible", true);
		new LMS1035S02PanelC4("PanelC4").processPanelData(model, params);

		String branch = params.getString("_branch");
		model.addAttribute("_PanelC4_visible", true);
		new LMS1035S02PanelC5("PanelC5", true, branch).processPanelData(model, params);

		Panel panel = new L120S01MPanel("l120s01mPanel");
		panel.processPanelData(model, params);
	}
	
	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}
}
