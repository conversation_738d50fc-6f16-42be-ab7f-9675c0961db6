<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
			<style type="text/css">        		 
			 .linkDocFile{color:#5291EF;text-decoration:underline;}
			 .delDocFile{color:blue;}
			 
			 .dt_width{ display: inline-block; width : 70px;}
			</style>
			<script type="text/javascript">
				loadScript('pagejs/crs/LMS2411S02Panel');
			</script>
			<!-- ====================================================================== -->
			
			<fieldset>
                <legend>
                    <th:block th:text="#{'subtitle.21'}">案件資訊</th:block>
                </legend>
	            <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                	<th:block th:if="${show21ROW1_NG}">
		                    <tr>
		                        <td width="30%" class="hd1">
		                            <th:block th:text="#{'doc.branchName'}">分行名稱</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td>
		                            <span id="branchName" ></span>&nbsp;
		                        </td>
								<td width="30%" class="hd1">
		                            <th:block th:text="#{'C241M01A.staff'}">是否為行員</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td>
		                            <span id="staff" ></span>&nbsp;
		                        </td>
		                    </tr>
		                    <tr>
		                    	<td class="hd1">
		                            <th:block th:text="#{'label.custInfo'}">借款人</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td colspan="3">
		                            <span id="custId"></span>&nbsp; <th:block th:text="#{'C241M01A.dupNo'}">重覆序號</th:block>:<span id="dupNo"></span> 　<span id="custName"></span>&nbsp;
		                        </td>                        
		                    </tr>
						</th:block>
						<th:block th:if="${show21ROW1_P}">
							<tr>
		                        <td width="30%" class="hd1">
		                            <th:block th:text="#{'doc.branchName'}">分行名稱</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td>
		                            <span id="branchName" ></span>&nbsp;
		                        </td>
								<td width="30%" class="hd1">
		                            <th:block th:text="#{'label.lnf660_loan_class'}">類別</th:block>
		                        </td>
		                        <td>
		                            <span id="lnf660_loan_class"></span>&nbsp;
		                        </td>
		                    </tr>
							<tr>
								<td class="hd1">
		                            <th:block th:text="#{'label.comInfo'}">合作仲介名稱</th:block>
		                        </td>
		                        <td colspan="3">
		                            <span id="comId"></span>&nbsp; <th:block th:text="#{'C241M01A.dupNo'}">重覆序號</th:block>:<span id="comDup"></span> 　<span id="comName"></span>&nbsp;
		                        </td>  
							</tr>
							<tr>
								<td class="hd1">
		                            <th:block th:text="#{'label.lnf660_m_contract'}">額度序號</th:block>
		                        </td>
		                        <td colspan="3">
		                            <span id="lnf660_m_contract"></span>&nbsp;
		                        </td>  
							</tr>
							<tr>
								<td class="hd1">
		                            <th:block th:text="#{'label.sellerInfo'}">賣方名稱</th:block>
		                        </td>
		                        <td colspan="3">
		                            <th:block th:text="#{'label.id'}">統一編號</th:block>:<span id="sellerId" ></span>&nbsp; 
									<th:block th:text="#{'C241M01A.dupNo'}">重覆序號</th:block>:<span id="sellerDup" ></span>&nbsp;
									<th:block th:text="#{'label.name'}">姓名</th:block>:<span id="sellerName" ></span>&nbsp;
		                        </td>  
							</tr>
							<tr>
								<td class="hd1">
		                            <th:block th:text="#{'label.buyerInfo'}">買方名稱</th:block>
		                        </td>
		                        <td colspan="3">
		                            <th:block th:text="#{'label.id'}">統一編號</th:block>:<span id="buyerId" ></span>&nbsp; 
									<th:block th:text="#{'C241M01A.dupNo'}">重覆序號</th:block>:<span id="buyerDup" ></span>&nbsp;
									<th:block th:text="#{'label.name'}">姓名</th:block>:<span id="buyerName" ></span>&nbsp;
		                        </td>  
							</tr>
							<tr>
								<td class="hd1">
		                            <th:block th:text="#{'C241M01A.pbAcct'}">存款(代收)帳號</th:block>
		                        </td>
		                        <td colspan="3">
									<input type="text" id="pbAcct" name="pbAcct" size="20" maxlength="20">&nbsp;
		                        </td>  
							</tr>
							<tr>
								<td class="hd1">
		                            <th:block th:text="#{'C241M01B.lcNo'}">保證編號</th:block>
		                        </td>
		                        <td colspan="3">
		                            <span id="lcNo" data-memo='若有N個C241M01B, 把 lcNo 用、串起來呈現' ></span>&nbsp;
		                        </td>  
							</tr>
						</th:block>
						<!-- ============= -->
						<th:block th:if="${showGrpInfo}">
						<tr>
	                    	<td class="hd1">
	                            <th:block th:text="#{'C241M01A.grpCntrNo'}">團貸序號</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <span id="grpCntrNo"></span>&nbsp;　<th:block th:text="#{'C241M01A.grpCount'}">團貸筆數</th:block>: <span id="grpCount"></span>&nbsp;
	                        </td>
							<td class="hd1">
	                            <th:block th:text="#{'C241M01A.grpEnd'}">動用止日</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <span id="grpEnd"></span>&nbsp;
	                        </td>                        
	                    </tr>
						</th:block>
						<!-- ============= -->
						<tr>
	                    	<td class="hd1">
	                            <th:block th:text="#{'C241M01A.projectNo'}">覆審序號</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td colspan="3">
	                            <span id="projectNo"></span>&nbsp;
	                        </td>
	                    </tr>
	                </tbody>
	            </table>
			</fieldset>			
            <!-- ================================================== -->
			<th:block th:if="${docFmt_hidden}">
				<input type="hidden" name="docFmt"      id="docFmt"     >
			</th:block>
			<!-- ================================================== -->
			<fieldset>
                <legend>
                    <th:block th:text="#{'subtitle.22'}">覆審訊息</th:block>
                </legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                	 <tr><!-- ROW1 -->
	                        <td width="30%" class="hd1">
	                        	<span class="text-red" id="prompt_retrialDate" style="display:none">＊</span>
	                            <th:block th:text="#{'C241M01A.retrialDate'}">實際覆審日期</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                        	<input type="text" size="8" class="date required" _requiredLength="10" id="retrialDate" name="retrialDate" />
	                        </td>
							<td width="35%" class="hd1">
								<div id="titile_overdue">
									<th:block th:if="${show22ROW1_NG}">
										<th:block th:text="#{'C241M01A.overdueYN'}">無擔逾期戶是否需半年再審</th:block>&nbsp;&nbsp;
									</th:block>
									
									<th:block th:if="${show22ROW1_P}">
										<th:block th:text="#{'C241M01A.overdueP'}">已逾到期日</th:block>&nbsp;&nbsp;
									</th:block>
								</div>
	                        </td>
	                        <td>
	                        	<!-- 
								在 LMS2411M01Page.js 裡的 buildOverdue(...) 
								去呈現不同的欄位
								-->
	                        	<div id="input_overdue"></div>&nbsp;
	                        </td>
	                    </tr>
						<th:block th:if="${show_docKindS_pa_period}">
							<tr>
		                        <td class="hd1">
		                        	<th:block th:text="#{'label.docKindS_pa_period'}">覆審基準期間</th:block>&nbsp;
		                        </td>
		                        <td colspan='3'>
		                        	<span id='docKindS_pa_period' ></span>&nbsp;
		                        </td>
							</tr>
									
						</th:block>
	                    <tr>
	                    	<td class="hd1">
	                            <th:block th:text="#{'C241M01A.lastRetrialDate'}">上次覆審日期</th:block>&nbsp;&nbsp;
								<div id='div_btn_printBefReviewDoc'>
									<button type="button" id="btn_printBefReviewDoc" class='forview'><span class="text-only"><th:block th:text="#{'btn.printBefReviewDoc'}">調閱覆審報告</th:block></span></button>									
								</div>
	                        </td>
	                        <td>
	                            <span id="lastRetrialDate"></span>&nbsp;
								<div id="div_btn_importBefText" style='display:none'>
									<button type="button" id="btn_importBefText"><span class="text-only"><th:block th:text="#{'btn.importBefText'}">匯入前次覆審意見資料</th:block></span></button>									
								</div>
	                        </td>
	                    	<td class="hd1">
	                            <th:block th:text="#{'C241M01A.shouldReviewDate'}">最遲應覆審日期</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                            <span id="shouldReviewDate" class="dt_width"></span>&nbsp;
	                        </td>
	                    </tr>
						<tr>
	                    	<td class="hd1">
	                            <th:block th:text="#{'C241M01A.retrialKind'}">覆審類別</th:block>&nbsp;&nbsp;<span id="crs_retrialKind_memo" class="" style='font-size:80%;'><u>說明</u></span>
	                        </td>
	                        <td>
	                            <span id="retrialKind"></span>&nbsp;
	                        </td>
	                    	<td class="hd1">
	                            <th:block th:text="#{'C241M01A.specifyCycle'}">99類覆審週期</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td>
	                        	<span id="specifyCycle"></span>&nbsp;
	                        </td>
	                    </tr>
						<!-- =================== -->
						<th:block th:if="${showUnkdInfo}">
						<tr>
	                    	<td class="hd1" style='background:pink'>
	                            <th:block th:text="#{'C241M01A.uckdDt'}">異常通報日期</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td colspan="3" style='background:pink'>
	                            <span id="uckdDt"></span>&nbsp;
	                        </td>
	                    </tr>
						<tr>
	                    	<td class="hd1" style='background:pink'>
	                            <th:block th:text="#{'label.mdFlagDesc'}">異常通報內容</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td colspan="3" style='background:pink'>
	                            <span id="mdFlagDesc"></span>&nbsp;
	                        </td>
	                    </tr>
						</th:block>
						<!-- =================== -->
						<th:block th:if="${show22ROW4_NG}">
							<tr>
		                    	<td class="hd1">
		                            <th:block th:text="#{'label.nCkdFlag'}">不覆審原因</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td>
		                            <span id="nCkdFlag"></span>&nbsp;
		                        </td>
		                    	<td class="hd1">
		                            <th:block th:text="#{'C241M01A.nCkdMemo'}">不覆審備註</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td>
		                            <span id="nCkdMemo"></span>&nbsp;
									<div>
										<span id="nckdDetail" ></span>&nbsp;
									</div>
		                        </td>
		                    </tr>
						</th:block>
						<th:block th:if="${show22ROW4_P}">
						</th:block>
						<!-- =================== -->	
						<th:block th:if="${docFmt_show_1}">
							<tr>
		                    	<td class="hd1">
		                            <th:block th:text="#{'label.docFmt'}">覆審報告表格式</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td colspan='3'>
					           		<label><input type="radio" value="A" name="docFmt" id="docFmt" ><th:block th:text="#{'label.docFmt.A'}">一般覆審報告表</th:block></label>
					           		<span style='margin-left:50px;'/>
									<label><input type="radio" value="B" name="docFmt" id="docFmt" ><th:block th:text="#{'label.docFmt.B'}">（含土建融實地覆審）覆審報告表</th:block></label>
		                        </td>
		                    </tr>
						</th:block>
	                   <!-- =================== -->
						<th:block th:if="${docFmt_show_2}">
							<tr>
		                    	<td class="hd1">
		                            <th:block th:text="#{'label.realCkFg'}">帳務含土建融</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td>
					           		<input id="realCkFg" name="realCkFg" type="radio" value="Y" disabled>是									
									<span style='margin-left:20px;'/>
									<input id="realCkFg" name="realCkFg" type="radio" value="O" disabled>是(同業聯貸參貸)
									<span style='margin-left:20px;'/>									
									<input id="realCkFg" name="realCkFg" type="radio" value="N" disabled>否
		                        </td>
		                    	<td class="hd1" nowrap>
		                            <th:block th:text="#{'C241M01A.lastRealDt'}">上次土建融實地覆審日</th:block>&nbsp;&nbsp;
		                        </td>
		                        <td>
	                            	<span id="lastRealDt" class="dt_width"></span>&nbsp;
		                        </td>
		                    </tr>
						</th:block>
	                </tbody>
	            </table>
			</fieldset>
			
				
			<th:block th:if="${showC241M01F}">
				<fieldset>
               	 	<legend>
              	      <th:block th:text="#{'subtitle.25'}">承辦行員</th:block>
               	 	</legend>
					
					<div id='table_C241M01F'> <!-- 參考 LMS1035S03Panel.js  -->
					</div>
				</fieldset>
			</th:block>
			<!-- ================================================== -->
			<!-- 一般 或 團貸子戶 -->
			<th:block th:if="${showLNPanelN}">
				<div id="PanelN">
					<fieldset>
		                <legend>
		                    <th:block th:text="#{'subtitle.23'}">授信帳務資料</th:block>
		                </legend>
						<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
							<caption style='font-weight: bold; color: #000000; '>
								<th:block th:text="#{'label.lnDataDate'}">帳務資料日期</th:block>：<span id="lnDataDate" class="color-red" ></span>
								&nbsp;&nbsp;&nbsp;&nbsp;
								<button type="button" id="btn_importLN"><span class="text-only"><th:block th:text="#{'btn.importLN'}">重引帳務資料</th:block></span></button>
							</caption>
			                <tbody>
			                    <tr>
			                        <td width="30%" class="hd1">
			                            <th:block th:text="#{'C241M01A.totQuota'}">額度合計</th:block>&nbsp;&nbsp;
			                        </td>
			                        <td>
			                        	<div>
			                        		<span style="flow:left"  id="totQuotaCurr" ></span>&nbsp;
											<span id="totQuota" name="totQuota" class="field" ></span>
										</div>								
			                        </td>
									<td width="30%" class="hd1">
			                            <th:block th:text="#{'C241M01A.totBal'}">前日結欠餘額合計</th:block>&nbsp;&nbsp;
			                        </td>
			                        <td>
			                        	<div>
			                        		<span style="flow:left"  id="totBalCurr" ></span>&nbsp;
											<span id="totBal" name="totBal" class="field" ></span>
										</div>	                           
			                        </td>
			                    </tr>	                    				
			                </tbody>
			            </table>
						<div id="gridviewC241M01B" style="width:100%;margin-left:0px;margin-right:0px"></div>	
					</fieldset>
				</div>		
			</th:block>
			<!-- ================================================== -->
			<!-- 價金履保 -->
			<th:block th:if="${sshowLNPanelP}">
				<div id="PanelP">
					<fieldset>
		                <legend>
		                    <th:block th:text="#{'subtitle.23'}">授信帳務資料</th:block>
		                </legend>
						<div id="gridviewC241M01B" style="width:100%;margin-left:0px;margin-right:0px"></div>	
					</fieldset>
				</div>		
			</th:block>
			<!-- ================================================== -->
			<!-- 一般 或 團貸母戶 -->
			<th:block th:if="${showLNPanelG}">
				<div id="PanelG">
					<fieldset>
						<legend><b><th:block th:text="#{'subtitle.24'}">團貸子戶</th:block></b></legend>
						
						<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
			                    <tr>
			                        <td width="30%" class="hd1">
			                            <th:block th:text="#{'C241M01A.totQuota'}">額度合計</th:block>&nbsp;&nbsp;
			                        </td>
			                        <td>
			                        	<div>
			                        		<span style="flow:left"  id="totQuotaCurr" ></span>&nbsp;
											<input type="text" id="totQuota" name="totQuota" size="18" maxlength="22" integer="15" fraction="2" class="numeric" />
										</div>								
			                        </td>
									<td width="30%" class="hd1">
			                            <th:block th:text="#{'C241M01A.totBal'}">前日結欠餘額合計</th:block>&nbsp;&nbsp;
			                        </td>
			                        <td>
			                        	<div>
			                        		<span style="flow:left"  id="totBalCurr" ></span>&nbsp;
											<input type="text" id="totBal" name="totBal" size="18" maxlength="22" integer="15" fraction="2" class="numeric" />
										</div>	                           
			                        </td>
			                    </tr>	                    				
			                </tbody>
			            </table>
						
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td>
									<table  border="0">
										<tr style="vertical-align:top;">
										<td><th:block th:text="#{'label.grpDetailFile'}">＠團貸所有明細EXCEL檔：</th:block></td>
										<td><div id="grpDetailFile"></div></td>
										</tr>
									</table>
								</td>
							</tr>       
					        <tr class="hd1">
								<td>							
									<button type="button" id="btn_print" class="forview"><span class="text-only"><th:block th:text="#{'btn.print'}">勾選資料列印</th:block></span></button>							
									<button type="button" id="btn_exp_grpDetail" class="forview"><span class="text-only"><th:block th:text="#{'btn.exp_grpDetail'}">匯出本案團貸所有明細</th:block></span></button>
									<button type="button" id="btn_add_grpDetail" class="forview"><span class="text-only"><th:block th:text="#{'btn.add_grpDetail'}">新增團貸子戶</th:block></span></button>
									<button type="button" id="btn_movetobr_grpDetail" class="forview"><span class="text-only"><th:block th:text="#{'btn.movetobr_grpDetail'}">勾選資料送受檢單位登錄</th:block></span></button>													
								</td>
							</tr>
							<tr class="hd1">
								<td><div id="gridviewGrpDetail" style="width:100%;margin-left:0px;margin-right:0px"></div></td>
					   		</tr>
						</table>
					</fieldset>
				</div>		
			</th:block>
			
        </th:block>
    </body>
</html>
