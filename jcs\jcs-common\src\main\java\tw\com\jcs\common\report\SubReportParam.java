package tw.com.jcs.common.report;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Vector;

import com.inet.report.Area;
import com.inet.report.Element;
import com.inet.report.Engine;
import com.inet.report.FieldElement;
import com.inet.report.ReportException;
import com.inet.report.Section;
import com.inet.report.TextInterpretationProperties;

import tw.com.jcs.common.PropUtil;

/**
 * <pre>
 * SubReportParam
 * </pre>
 * 
 * @since 2022年12月13日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月13日
 *          </ul>
 */
public class SubReportParam {

    final static String BASEURL = PropUtil.getProperty("baseUrl");

    private List<Integer> subRepIndexs;

    private List<Map<String, String>> params;

    private List<List<Map<String, String>>> rowDatas;

    /**
     * 設定子報表資料
     * 
     * @param subRepIndex
     *            子報表順序
     * @param params
     *            參數欄位
     * @param rowData
     *            重覆資料
     */
    public void setData(int subRepIndex, Map<String, String> params, List<Map<String, String>> rowData) {
        this.subRepIndexs.add(subRepIndex);
        this.params.add(params);
        this.rowDatas.add(rowData);
    }

    public SubReportParam() {
        subRepIndexs = new ArrayList<Integer>();
        params = new ArrayList<Map<String, String>>();
        rowDatas = new ArrayList<List<Map<String, String>>>();
    }

    /**
     * 設定子報表資料
     * 
     * @param engine
     *            {@link com.inet.report.Engine}
     * @return
     * @throws ReportException
     */
    public Engine execute(Engine engine) throws ReportException {

        for (int i = 0; i < subRepIndexs.size(); i++) {
            Engine srp = engine.getSubReport(subRepIndexs.get(i));
            // 子報表也加上baseurl，這樣圖片才能列印
            int counti = srp.getAreaCount();
            for (int l = 0; l < counti; l++) {
                // 該區域的所有細分區域,詳細資料a,詳細資料b.......
                Area dArea = srp.getArea(l);
                if (dArea == null) {
                    continue;
                }
                int countk = dArea.getSectionCount();
                for (int k = 0; k < countk; k++) {
                    Section dSec = dArea.getSection(k);
                    Vector<?> elemsV = dSec.getElementsV();
                    int countj = elemsV.size();
                    for (int j = 0; j < countj; j++) {
                        Element elem = (Element) elemsV.elementAt(j);
                        if (elem instanceof FieldElement) {
                            FieldElement fElem = (FieldElement) elem;

                            if (fElem.getTextInterpretation() == TextInterpretationProperties.ADVANCED_HTML_TEXT) {
                                fElem.setBaseUrl(BASEURL);
                            }
                        }
                    }
                }
            }
            Map<String, String> rptVariableMap = params.get(i);

            if (rptVariableMap != null && rptVariableMap.size() > 0) {
                for (String key : rptVariableMap.keySet()) {
                    if (rptVariableMap.get(key) == null) {
                        rptVariableMap.put(key, "");
                    }
                }
                for (Entry<String, String> entry : rptVariableMap.entrySet()) {
                    srp.setPrompt(entry.getKey(), entry.getValue());
                }
            }
            List<Map<String, String>> rowData = rowDatas.get(i);

            int rowCount = 0;

            String[] titles = null;
            Object[][] rows = null;
            if (rowData != null && rowData.size() > 0) {

                Map<String, String> titleMap = rowData.get(0);
                int columnCount = titleMap.size();
                titles = titleMap.keySet().toArray(new String[] {});
                rows = new String[rowData.size()][columnCount];

                int count = 0;
                for (Map<String, String> map : rowData) {
                    count = 0;
                    String[] dataRows = new String[columnCount];
                    for (String key : titles) {
                        dataRows[count++] = map.get(key);
                    }
                    rows[rowCount++] = dataRows;
                }

            }

            srp.setData(titles, rows);
        }

        return engine;
    }

}
