/* 
 * LMS1411M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel04;
import com.mega.eloan.lms.lns.panels.LMS1411S01Panel;
import com.mega.eloan.lms.lns.panels.LMS1411S02Panel;
import com.mega.eloan.lms.lns.panels.LMS1411S03Panel;
import com.mega.eloan.lms.model.L141M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * [國內企金]聯行額度明細表
 * </pre>
 * 
 * @since 2012/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/29,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1411m01/{page}")
public class LMS1411M01Page extends AbstractEloanForm {
	@Autowired
	LMSService lmsService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {

		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_待覆核));
		
		renderJsI18N(LMS1411M01Page.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		panel.processPanelData(model, params);
		model.addAttribute("tabID", tabID);
		renderJsI18N(LMS1411M01Page.class);
		renderJsI18N(LMS1205M01Page.class, "print");
	}// ;

	// 頁籤
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1411S01Panel(TAB_CTX, true);
			break;
		case 2:
			Map<String, String> msgs = lmsService.getAllDervPeriod();
			renderJsI18NWithMsgName("dervPeriodCodeType", msgs);
			msgs = lmsService.getCodeType("lms120_noFactCountry");
			renderJsI18NWithMsgName("lms120_noFactCountry", msgs);
			msgs = lmsService.getCodeType("lms120_freezeFactCountry");
			renderJsI18NWithMsgName("lms120_freezeFactCountry", msgs);
			// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			msgs = lmsService.getCodeType("lms140_esgGreenSpendType");
			renderJsI18NWithMsgName("lms140_esgGreenSpendType", msgs);
			// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
			msgs = lmsService.getCodeType("lms140_esgSustainLoanType");
			renderJsI18NWithMsgName("lms140_esgSustainLoanType", msgs);
			//J-113-0329 企金授信新增社會責任授信
			msgs = lmsService.getCodeType("lms140_socialKind");
			renderJsI18NWithMsgName("lms140_socialKind", msgs);
			msgs = lmsService.getCodeType("lms140_socialTa");
			renderJsI18NWithMsgName("lms140_socialTa", msgs);
			msgs = lmsService.getCodeType("lms140_socialResp");
			renderJsI18NWithMsgName("lms140_socialResp", msgs);
			
			panel = new LMS1411S02Panel(TAB_CTX, true);
			renderJsI18N(LMS1401S02Panel.class);
			renderJsI18N(LMS1401S02Panel04.class);
			break;
		case 3:
			panel = new LMS1411S03Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1411S01Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L141M01A.class;
	}
}
