package com.mega.eloan.lms.lrs.service;

import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01H;
import com.mega.eloan.lms.model.L180M01A;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.ICapService;

public interface LMS1700Service extends ICapService {
	public String l170m01a_docStatusDesc(L170M01A meta);

	public String checkIncompleteMsg(L170M01A meta);

	/**
	 * 引入 負責人
	 */
	public void fnGetChairman(L170M01A meta);

	/**
	 * 引入 0024 行業別
	 */
	public void fnGetCustBusData(L170M01A meta);

	/**
	 * 引入 資信簡表行業別
	 */
	public void fnGetLastCES120DocBusCd(L170M01A meta);

	/**
	 * 引入 保證人 <br/>
	 * 有 cntrNo 回傳true
	 */
	public boolean getRlt_Guarantor(L170M01A meta, List<L170M01H> insUpdList,
			List<L170M01H> delList);

	/**
	 * 引入 共同借款人 <br/>
	 * 有 cntrNo 回傳true
	 */
	public boolean getRlt_Borrower(L170M01A meta, List<L170M01H> insUpdList,
			List<L170M01H> delList);

	/**
	 * 引入 符合授信額度標準 <br/>
	 * 在notes的程式 <br/>
	 * ●符合授信額度標準 的 mode==1 , doc.MLoanPerson <br/>
	 * ●主要授信戶　　　 的 mode==2 , doc.MLoanPersonA
	 */
	public String gfnDB2calllnsp0150(int mode, String branch, String custId,
			String dupNo);

	public L170M01A addNewL170(String retrialBrNo, String examBrNo,
			String custId, String dupNo, String custName,
			boolean autoGetLoanData, List<String> failMsgList, String ctlType);

	/**
	 * 在覆審名單傳送分行時，會寫入大量的L170M01A 為了不lock 住 table，造成 覆審組-編製中... 的 view 出現 timeout
	 * 把 addNewL170 拆成 2 部分： _basicL170M01A(...) 及 _genL170M01A(...)
	 */
	public L170M01A _basicL170M01A(String examBrNo, String custId,
			String dupNo, String custName, List<String> failMsgList,
			String ctlType);

	public void _genL170M01A(String retrialBrNo, L170M01A l170m01a,
			boolean autoGetLoanData);

	public void impData(L170M01A meta, String flag, Map<String, String> showItem);

	public void delMeta(L170M01A meta);

	public List<L170M01A> findUnFinish(String ownBrId, String custId,
			String dupNo);

	public void up_to_mis(L170M01A meta) throws CapException;

	public List<Map<String, Object>> excludeExpiredCesF101(
			List<Map<String, Object>> src_list);

	public List<Map<String, Object>> getCesf101(String brNo, String custId,
			String dupNo);

	public void getFinData(L170M01C l170m01c, String[] f101m01a_mainIds);

	public void setFinToDoc(L170M01A meta);

	public void update_ptMgrId(L170M01A meta, String mgrId);

	public List<L170M01A> impBySelOrDate(String oids, String s_retrialDate,
			String unitNo, List<String> errMsg);

	public void replaceWithBef(L170M01A meta, L170M01A befMeta);

	public List<L170M01A> save_basicL170M01A(L180M01A l180m01a);

	public void update_l170m01a_rpid(JSONObject rq) throws CapException;

	// J-108-0268 逾期情形
	public boolean chkOverDue(L170M01A meta);

}
