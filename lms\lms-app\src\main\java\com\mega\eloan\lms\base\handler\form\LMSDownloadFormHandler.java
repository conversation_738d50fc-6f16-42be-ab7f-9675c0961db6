/* 
 * LMSDownloadFormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import java.io.ByteArrayOutputStream;
import java.util.LinkedHashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.FileDownLoadConstant;
import com.mega.eloan.common.utils.SpringContextHelper;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.handler.FileDownloadHandler;
import tw.com.iisi.cap.response.CapByteArrayDownloadResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/** 下載WORD EXCEL CSV方法
 * <AUTHOR> 2012/12/26
 *
 */
@Controller("lmsdownloadformhandler")
public class LMSDownloadFormHandler extends FileDownloadHandler {

//	protected String fileDownloadName;
//
//	protected String serviceName;


	protected static final Logger logger = LoggerFactory
			.getLogger(LMSDownloadFormHandler.class);
	

	@Override
	public IResult beforeDownload(PageParameters params) throws CapException {
		byte[] bytes = null;
		ByteArrayOutputStream baos = null;
		IResult result = null;
		final String fileDownloadName = Util.trim(params.getString(
				"fileDownloadName", "temp"));
		final String serviceName = Util.trim(params.getString(
				"serviceName", ""));
		try {
			bytes = ((FileDownloadService) SpringContextHelper.getBean(
					serviceName)).getContent(params);
			result = new CapByteArrayDownloadResult(bytes, this.getFileContentType(fileDownloadName), fileDownloadName);
		} catch (Exception e) {
			logger.error("[beforeDownload] Exception!!", e);
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator rptGenerator = new ReportGenerator();
			rptVariableMap.put("ERRORMSG",
					"EFD0066:" + ReportGenerator.getErrorInfoFromException(e));
			rptGenerator.setVariableData(rptVariableMap);
			try {
				baos = (ByteArrayOutputStream) rptGenerator
						.generateExceptionReport(LMSUtil.getLocale());
			} catch (Exception e1) {
				logger.error("[beforeDownload ERRORMSG] Exception!!", e1);

			}
			if(baos != null){
				bytes = baos.toByteArray();
				result = new CapByteArrayDownloadResult(bytes, FileDownLoadConstant.CONTENT_TTYPE_PDF, "ERRORMSG.PDF");
			}
		}
		return result;
	}

	@Override
	public String getOperationName() {
		return EloanConstants.OPERATION_FILEDWN;
	}

	/**
	 * 實作檔案下載Service的名稱
	 * 
	 * @return 檔案下載Service的名稱
	 */
//	public String getFileDownloadServiceName() {
//		return this.serviceName;
//	}

	/**
	 * 取得檔案下載的content type
	 * 
	 * @return content type
	 */
	protected String getFileContentType(String fileDownloadName) {
		String fn = fileDownloadName;
		if (!"".equals(Util.nullToSpace(fileDownloadName))) {
			fn = fileDownloadName;
			if (fn != null) {
				int pos = fn.lastIndexOf(".");
				if (pos != -1) {
					String contentType = FileDownLoadConstant.mimeTypeMap.get(fn.substring(pos));
					if (contentType != null) {
						return contentType;
					}
				}
			}
		} 
//		else {
//			fn = this.getFileContentType();
//		}
		logger.info("contentType = " + FileDownLoadConstant.CONTENT_TTYPE_UNKONW);

		return FileDownLoadConstant.CONTENT_TTYPE_UNKONW;
	}

}
