var __handler = "";
var borrowData = {};

pageJsInit(function() {
$(function() {
initDfd = initDfd || $.Deferred(); 
initDfd.done(function(){
    setCloseConfirm(true);
    if (responseJSON.docURL == "/lms/lms1201m01") {
        // 授權外企金
        __handler = "lms1201formhandler";
    }else {
        // 授權內企金
        __handler = "lms1101formhandler";
    }	
	
    // 內層GridView
    // alert(JSON.stringify(responseJSON));
    $("#getCustData").click(function(){
		var $addborrow = $("#addborrow");
		var $custId = $addborrow.find("#custId").val();
		var $custName = $addborrow.find("#custName").val();
		if(($custId != null && $custId != undefined && $custId != '')
		&& ($custName != null && $custName != undefined && $custName != '')){
			// 統一編號、名稱擇一輸入引進即可
			CommonAPI.showErrorMessage(i18n.lmss02b["l120s02.alert26"]);
		}else if(($custId == null || $custId == undefined || $custId == '')
		&& ($custName == null || $custName == undefined || $custName == '')){
			// 請輸入統一編號或名稱
			CommonAPI.showErrorMessage(i18n.lmss02b["l120s02.alert27"]);
		}else{
		    var defaultOption = {};
			if($custId != null && $custId != undefined && $custId != ''){
				defaultOption = {
					defaultValue: $custId //預設值 
				};
			}else{
				defaultOption = {
					defaultName : $custName
				};				
			}			
			//綁入MegaID
			CommonAPI.openQueryBox(
				$.extend({
/*
	                defaultValue: $custId, //預設值 
	                defaultName : $custName,
*/
					doNewUser: false,
					defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : ($custName != null && $custName != undefined && $custName != '') ? "2" : "",
	                divId:"addborrow", //在哪個div 底下 
	                autoResponse: { // 是否自動回填資訊 
	                           id: "custId", // 統一編號欄位ID 
	                       dupno: "dupNo", // 重覆編號欄位ID 
	                      name: "custName" // 客戶名稱欄位ID 
	                },fn:function(obj){
						//alert(obj.buscd);
						$addborrow.find("#buscd").val(obj.buscd);
						
		                if ($("#addborrow").valid()) {
		                    var exist = false;
		                    // 企金
		                    $.ajax({
		                        // 驗證前端資料有無和後端資料重複
		                        type: "POST",
		                        handler: __handler,
		                        data: {
		                            formAction: "checkAddBorrow",
		                            mainId: responseJSON.mainId,
									custId : obj.custid,
									dupNo : obj.dupno,
		                            checkdata: exist
		                        }
							}).done(function(responseData) {
								$.thickbox.close();
								exist = responseData.checkdata;
								if (exist == true) {
								    CommonAPI.showMessage(i18n.lmss02b["l120s02.alert8"]);
								}
								else {
								    //$("#invMDscr").val("");
								    // 沒有任何主要借款人
								    if (!responseData.haseCust) {
								        CommonAPI.confirmMessage(i18n.lmss02b["l120s02.confirm4"], function(b){
								            if (b) {
								                addNewBor(obj, true);
								            }
								            else {
								                addNewBor(obj, false);
								            }
								        });
								    }
								    else {
								        addNewBor(obj, false);
								    }
								    // $.thickbox.close();
								}
								//$.thickbox.close();
		                    });
		                }						
					}
				},defaultOption)
			);			
		}
    });
    
    var L120S01aGrid = $("#l120s01agrid").iGrid({ // 借款人基本資料GridView
        handler: 'lms1201gridhandler',
        height: 350,
        sortname: 'keyMan|custShowSeqNum|custId|dupNo',
		sortorder: 'desc|asc|asc|asc',
		multiselect: true,
        postData: {
            formAction: "queryL120s01a",
            rowNum: 10
        },
        rownumbers: true,
        rowNum: 10,
        // multiselect : true,
        colModel: [{
            colHeader: "&nbsp;", // 主要借款人Flag
            align: "center",
            width: 10, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'keyMan' // col.id
        }, {
            colHeader: i18n.lmss02b["l120s01a.custid"], // 身分證統編
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lmss02b["l120s01a.custname2"], // 借款人名稱
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lmss02b["l120s01a.custrlt"], // 與主要借款人關係
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custRlt' // col.id
        }, {
            colHeader: i18n.lmss02b["l120s01a.custpos"], // 相關身份
            align: "left",
            width: 100, // 設定寬度
            sortable: false, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custPos' // col.id
        }, {
            colHeader: "&nbsp",//"檢核欄位",
            name: 'chkYN',
            width: 20,
            //sortable: true,
            align: "center"
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120S01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
	
	
	
    
    //借款人明細上所有的thickbox add by fantasy
    if ($('#lmsThickBoxDiv').length == 0){
		$('body').append('<div id="lmsThickBoxDiv" ></div>');
		$("#lmsThickBoxDiv").load('../../lms/lmss02b02', function(){
			$("#lmsThickBoxDiv").buildItem();
		});
	}
});
});
});

// 控制借款人企金頁籤點擊時唯讀
function setSubReadOnly(borrowPage){
	if($("#" + borrowPage).attr("hasSet") == "false"){
			if (borrowPage == "borrowPage02") {
				$("#tab-0b").load('../../lms/lmss02b03', function(){
					if (responseJSON.readOnly == "true") {
						responseJSON["borrowPage2"] = null;
						var $borrowPage02 = $("#L120S01gForm_1,#formIdDscr1");
						$borrowPage02.readOnlyChilds(true);
						$borrowPage02.find("button").hide();
					}
					var $L120S01gForm_1 = $("#L120S01gForm_1");
					var $formIdDscr1 = $("#formIdDscr1");
					$L120S01gForm_1.buildItem();
					$formIdDscr1.buildItem();
		            $formIdDscr1.setData(borrowData.formIdDscr1);
		            $L120S01gForm_1.setData(borrowData.L120S01gForm_1);
		            // 控制"引進徵信相關資料隱藏顯示功能"
					if(borrowData.L120S01gForm_1){
			            if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
			                $L120S01gForm_1.find("#rcdFlag").prop("checked", true);
							$L120S01gForm_1.find(".hideThis").show();				
							$formIdDscr1.find(".hideThis").show();
							//$L120S01gForm_1.find(".cesCheck").prop("checked", true);
			            }
			            else {
			                $L120S01gForm_1.find("#rcdFlag").prop("checked", false);
							$L120S01gForm_1.find(".hideThis").hide();
							$formIdDscr1.find(".hideThis").hide();
							//$L120S01gForm_1.find(".cesCheck").prop("checked", false);
			            }
			            if (borrowData.L120S01gForm_1.runFlag == "Y") {
			                $L120S01gForm_1.find("#runFlag").prop("checked", true);
			                $L120S01gForm_1.find("#tabs-2_show").show();
			            }
			            else {
			                $L120S01gForm_1.find("#runFlag").prop("checked", false);
			                $L120S01gForm_1.find("#tabs-2_show").hide();
			            }						
					}else{
						// 新增時直接塞預設值
		                $L120S01gForm_1.find("#rcdFlag").prop("checked", false);
						$L120S01gForm_1.find(".hideThis").hide();
						$L120S01gForm_1.find(".cesCheck").prop("checked", false);
						$formIdDscr1.find(".hideThis").hide();
			            $L120S01gForm_1.find("#runFlag").prop("checked", false);
			            $L120S01gForm_1.find("#tabs-2_show").hide();
					}										
				});
			}else if(borrowPage == "borrowPage03"){
				$("#tab-0c").load('../../lms/lmss02b04', function(){
				    if (responseJSON.readOnly == "true") {
						var $borrowPage03 = $("#L120S01gForm_2,#formIdDscr2");
						$borrowPage03.readOnlyChilds(true);
						$borrowPage03.find("button").hide();
				    }
					var $L120S01gForm_2 = $("#L120S01gForm_2");
					var $formIdDscr2 = $("#formIdDscr2");
					$L120S01gForm_2.buildItem();
					$formIdDscr2.buildItem();					
		            $formIdDscr2.setData(borrowData.formIdDscr2);
		            $L120S01gForm_2.setData(borrowData.L120S01gForm_2);
					// 控制"引進徵信相關資料隱藏顯示功能"
					if(borrowData.L120S01gForm_1 && borrowData.L120S01gForm_2){
						buildL120s01eKind2(borrowData);
			            if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
							$L120S01gForm_2.find(".hideThis").show();
							$formIdDscr2.find(".hideThis").show();
			            }
			            else {
							$L120S01gForm_2.find(".hideThis").hide();
							$formIdDscr2.find(".hideThis").hide();
			            }
			            if (borrowData.L120S01gForm_2.finFlag == "Y") {
			                $L120S01gForm_2.find("#finFlag").prop("checked", true);
			                $L120S01gForm_2.find("#tabs-3_show").show();
			            }
			            else {
			                $L120S01gForm_2.find("#finFlag").prop("checked", false);
			                $L120S01gForm_2.find("#tabs-3_show").hide();
			            }
					}else{
						// 新增時直接塞預設值
						$L120S01gForm_2.find(".hideThis").hide();
						$formIdDscr2.find(".hideThis").hide();
						$L120S01gForm_2.find(".cesCheck").prop("checked", false);
		                $L120S01gForm_2.find("#finFlag").prop("checked", false);
		                $L120S01gForm_2.find("#tabs-3_show").hide();
					}
				});		
			}else if(borrowPage == "borrowPage04"){
				$("#tab-0d").load('../../lms/lmss02b05', function(){
				    if (responseJSON.readOnly == "true") {
						responseJSON["borrowPage4"] = null;
						var $borrowPage04 = $("#L120S01fForm");			
						$borrowPage04.readOnlyChilds(true);
						$borrowPage04.find("button").hide();
				    }
					var $L120S01fForm = $("#L120S01fForm");
					util.init($L120S01fForm);
					$L120S01fForm.buildItem();
					$L120S01fForm.setData(borrowData.L120S01fForm);
					// 控制"引進徵信相關資料隱藏顯示功能"
					if (borrowData.L120S01gForm_1 && borrowData.L120S01fForm) {			            
			            if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
							$L120S01fForm.find(".hideThis").show();
							$L120S01fForm.find("#rcdFlag").prop("checked", true);		
			            }
			            else {
							$L120S01fForm.find(".hideThis").hide();
							$L120S01fForm.find("#rcdFlag").prop("checked", false);
			            }						
					}else{
						// 新增時直接塞預設值
						$L120S01fForm.find(".hideThis").hide();
						$L120S01fForm.find(".cesCheck").prop("checked", false);
						$L120S01fForm.find("#rcdFlag").prop("checked", false);
					}
				});		
			}			
			$("#" + borrowPage).attr("hasSet", "true");
			$("#" + borrowPage).attr("hasSet2", "true");
	}else{
		if ($("#" + borrowPage).attr("hasSet2") == "false") {
			if (borrowPage == "borrowPage02") {
				if (responseJSON.readOnly == "true") {
					responseJSON["borrowPage2"] = null;
					var $borrowPage02 = $("#L120S01gForm_1,#formIdDscr1");
					$borrowPage02.readOnlyChilds(true);
					$borrowPage02.find("button").hide();
				}
				var $L120S01gForm_1 = $("#L120S01gForm_1");
				var $formIdDscr1 = $("#formIdDscr1");
				$L120S01gForm_1.buildItem();
				$formIdDscr1.buildItem();
	            $formIdDscr1.setData(borrowData.formIdDscr1);
	            $L120S01gForm_1.setData(borrowData.L120S01gForm_1);
	            // 控制"引進徵信相關資料隱藏顯示功能"
				if (borrowData.L120S01gForm_1) {
		            if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
		                $L120S01gForm_1.find("#rcdFlag").prop("checked", true);
						$L120S01gForm_1.find(".hideThis").show();				
						$formIdDscr1.find(".hideThis").show();
						$L120S01gForm_1.find(".cesCheck").prop("checked", true);
		            }
		            else {
		                $L120S01gForm_1.find("#rcdFlag").prop("checked", false);
						$L120S01gForm_1.find(".hideThis").hide();
						$formIdDscr1.find(".hideThis").hide();
						$L120S01gForm_1.find(".cesCheck").prop("checked", false);
		            }
		            if (borrowData.L120S01gForm_1.runFlag == "Y") {
		                $L120S01gForm_1.find("#runFlag").prop("checked", true);
		                $L120S01gForm_1.find("#tabs-2_show").show();
		            }
		            else {
		                $L120S01gForm_1.find("#runFlag").prop("checked", false);
		                $L120S01gForm_1.find("#tabs-2_show").hide();
		            }					
				}else{
	                $L120S01gForm_1.find("#rcdFlag").prop("checked", false);
					$L120S01gForm_1.find(".hideThis").hide();
					$formIdDscr1.find(".hideThis").hide();
					$L120S01gForm_1.find(".cesCheck").prop("checked", false);	
	                $L120S01gForm_1.find("#runFlag").prop("checked", false);
	                $L120S01gForm_1.find("#tabs-2_show").hide();										
				}
			}else if(borrowPage == "borrowPage03"){
			    if (responseJSON.readOnly == "true") {
					var $borrowPage03 = $("#L120S01gForm_2,#formIdDscr2");
					$borrowPage03.readOnlyChilds(true);
					$borrowPage03.find("button").hide();
			    }
				var $L120S01gForm_2 = $("#L120S01gForm_2");
				var $formIdDscr2 = $("#formIdDscr2");
				$L120S01gForm_2.buildItem();
				$formIdDscr2.buildItem();					
	            $formIdDscr2.setData(borrowData.formIdDscr2);
	            $L120S01gForm_2.setData(borrowData.L120S01gForm_2);
	            // 控制"引進徵信相關資料隱藏顯示功能"
				if (borrowData.L120S01gForm_1 && borrowData.L120S01gForm_2) {
					buildL120s01eKind2(borrowData);
		            if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
						$L120S01gForm_2.find(".hideThis").show();
						$formIdDscr2.find(".hideThis").show();
		            }
		            else {
						$L120S01gForm_2.find(".hideThis").hide();
						$formIdDscr2.find(".hideThis").hide();
		            }
		            if (borrowData.L120S01gForm_2.finFlag == "Y") {
		                $L120S01gForm_2.find("#finFlag").prop("checked", true);
		                $L120S01gForm_2.find("#tabs-3_show").show();
		            }
		            else {
		                $L120S01gForm_2.find("#finFlag").prop("checked", false);
		                $L120S01gForm_2.find("#tabs-3_show").hide();
		            }					
				}else{
					$L120S01gForm_2.find(".hideThis").hide();
					$formIdDscr2.find(".hideThis").hide();
	                $L120S01gForm_2.find("#finFlag").prop("checked", false);
	                $L120S01gForm_2.find("#tabs-3_show").hide();											
				}										
			}else if(borrowPage == "borrowPage04"){
				var $L120S01fForm = $("#L120S01fForm");
				util.init($L120S01fForm);
				$L120S01fForm.buildItem();
				$L120S01fForm.setData(borrowData.L120S01fForm);
	            // 控制"引進徵信相關資料隱藏顯示功能"
				if (borrowData.L120S01gForm_1 && borrowData.L120S01fForm) {
		            if (borrowData.L120S01gForm_1.rcdFlag == "Y") {
						$L120S01fForm.find(".hideThis").show();
						$L120S01fForm.find("#rcdFlag").prop("checked", true);		
		            }
		            else {
						$L120S01fForm.find(".hideThis").hide();
						$L120S01fForm.find("#rcdFlag").prop("checked", false);
		            }					
				}else{
					$L120S01fForm.find(".hideThis").hide();
					$L120S01fForm.find("#rcdFlag").prop("checked", false);
				}										
			}			
		}
		$("#" + borrowPage).attr("hasSet2", "true");		
	}
}

function thickboxTrust(){
	//J-111-0443_05097_B1006 Web e-Loan企金授信開發授信BIS評估表
	var $L120S01aForm = $("#L120S01aForm");
	$.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "chkDocStatusCanEditBorrower",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").val(),
            dupNo: $L120S01aForm.find("#dupNo").val()
        }
	}).done(function(resp) {
		if (resp.canEdit == "Y") {
			$("#thickboxTrust").thickbox({
		        // 使用選取的內容進行彈窗
		        title: i18n.lmss02b["l120s02.thickbox16"],
		        width: 300,
		        height: 200,
		        modal: true,
		        align: "center",
		        valign: "bottom",
		        i18n: i18n.def,
		        buttons: {
		            "sure": function(){
		                $(".trust").val("");
		                var trustVal = $("input[name='selTrust']:radio:checked").val();
		                if (trustVal == 1) {
		                    getL120s01c(1);
		                    $.thickbox.close();
		                }
		                else if (trustVal == 2) {
		                        //getL120s01c(2);
		                        // 內部Mow信評;
		                        mowGrid(true);
		                    }
		                    else {
		                        getL120s01c(4);
		                        $.thickbox.close();
		                    }
		            },
		            "cancel": function(){
		                API.confirmMessage(i18n.def['flow.exit'], function(res){
		                    if (res) {
		                        $.thickbox.close();
		                    }
		                });
		            }
		        }
		    });
		}	
    });
 
}

function queryBorrow(rowObject){
	borrowData = {};   
	var $L120S01aForm = $("#L120S01aForm");
	var $L120S01gForm_1 = $("#L120S01gForm_1");
	var $L120S01gForm_2 = $("#L120S01gForm_2");
	var $L120S01fForm = $("#L120S01fForm");	
    $L120S01aForm.find("#stockStatus").nextAll().hide();
    $L120S01aForm.find("#spanStock,#stockDate").val('');	
	$L120S01aForm.find(".hideMow1").hide();
    $L120S01aForm.find(".hideMow2").hide();
    $L120S01aForm.find(".hideMow3").hide();
    $L120S01aForm.find(".hideMow4").hide();
    $L120S01aForm.find(".hideMow5").hide();
    $L120S01aForm.find(".hideMow6").hide();
	$L120S01aForm.find(".hideMow7").hide();
	//J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
	$L120S01aForm.find(".hideMow8").hide();
	$L120S01aForm.find(".hideMow9").hide();
	
	
	// 預設為非主要借款人
    $L120S01aForm.find("#keyMan").prop("checked", false);
    //$L120S01aForm.find("#keyMan").val("N");
    $("#custRltShow").val("");	
    // 讀取已有的資料以進行修改
    ilog.debug(rowObject);
    // 企金
    $.ajax({ // 查詢主要借款人資料
        handler: __handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "queryBorrow",
            mainId: responseJSON.mainId,
            oid: rowObject.oid,
            theOid: rowObject.oid,
            isOther: false
        }
	}).done(function(obj2) {
		// firefox 印Log       
        ilog.debug(obj2);
        var L120S01aForm = $("#L120S01aForm");

		L120S01aForm.setData(obj2.L120S01aForm);
		 
        gridCreditRiskReLoad();		
		
		borrowData = obj2;
		responseJSON["borrowPage2"] = obj2.L120S01gForm_1;
		responseJSON["borrowPage4"] = obj2.L120S01fForm;
		

		L120S01aForm.find("#typCd").text(obj2.L120S01aForm.typCd);
        if(obj2.rejtCaseBefore){
			// 控制變更前婉卻種類顯示
			L120S01aForm.find(".showBefore").show();
		}
        if(obj2.rejtCaseBefore1){
			// 控制變更前婉卻種類顯示
			L120S01aForm.find(".showBefore1").show();
		}
        if (obj2.L120S01aForm.grade1) {
            L120S01aForm.find(".hideMow1").show();
        }
        if (obj2.L120S01aForm.grade2) {
            L120S01aForm.find(".hideMow2").show();
        }
        if (obj2.L120S01aForm.grade3) {
            L120S01aForm.find(".hideMow3").show();
        }
		if (obj2.L120S01aForm.grade7) {
            L120S01aForm.find(".hideMow7").show();
        }
		
		//J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
		if (obj2.L120S01aForm.grade8) {
            L120S01aForm.find(".hideMow8").show();
        }
		if (obj2.L120S01aForm.grade9) {
            L120S01aForm.find(".hideMow9").show();
        }
		
        if (obj2.L120S01aForm.grade4) {
            L120S01aForm.find(".hideMow4").show();
        }
        if (obj2.L120S01aForm.grade5) {
            L120S01aForm.find(".hideMow5").show();
        }
        if (obj2.L120S01aForm.grade6) {
            L120S01aForm.find(".hideMow6").show();
        }
		if(obj2.L120S01aForm.stockStatus == "6" || obj2.L120S01aForm.stockStatus == ""){
        	L120S01aForm.find("#stockStatus").nextAll().hide();
    		L120S01aForm.find("#spanStock,#stockDate").val('');
        }else{
        	L120S01aForm.find("#stockStatus").nextAll().show();
			L120S01aForm.find("#spanStock").text(L120S01aForm.find("#stockStatus :selected").text());
        }
		
        //ie7 不知道為何會disabled，故要重設
        $("#L120S01gForm_1").find("#rcdFlag").prop("disabled", false);			
		// 非主要借款人的畫面控制
		if (obj2.L120S01aForm.keyMan != "Y") {
		    if (obj2.L120S01aForm.custRlt != "" && obj2.L120S01aForm.custPos != "") {
				var $cThickboxCustRlt = $("#cThickboxCustRlt");
                if (obj2.L120S01aForm.isOther) {
                    // 其他綜合關係
                    L120S01aForm.find("#custRlt").val(obj2.L120S01aForm.custRlt);
					var theText = $cThickboxCustRlt.find("#custRlt_content3 :selected").text();
					L120S01aForm.find("#custRltShow").val(obj2.L120S01aForm.custRlt1 + obj2.L120S01aForm.custRlt2 + " " + $cThickboxCustRlt.find("#custRlt_content3 :selected").text() + "-");
                    L120S01aForm.find("#custRltShow").val(L120S01aForm.find("#custRltShow").val() + " " + $cThickboxCustRlt.find("#custRlt_content4 :selected").text());
                }					
		        else {
		            $cThickboxCustRlt.find("select[name='custRlt_content'] option").each(function(i){
						var $this = $(this);
		                if ($this.val() == obj2.L120S01aForm.custRlt) {
		                    L120S01aForm.find("#custRlt").val(obj2.L120S01aForm.custRlt);
		                    L120S01aForm.find("#custRltShow").val(obj2.L120S01aForm.custRlt + " " + $this.text());
		                }
		            });
		        }
		        L120S01aForm.find("#chk_radio1").show();
		    }
		    else {
		        L120S01aForm.find("#custRlt").val("");
		        L120S01aForm.find("#custPos option:eq(0)").prop("selected", true);
		        L120S01aForm.find("#chk_radio1").show();
		    }				
		}
		
		
        // 標題設定
		var $showBorrowData = $("#showBorrowData");
        $showBorrowData.find("#custId").val(obj2.custId);
        $showBorrowData.find("#dupNo").val(obj2.dupNo);
		$showBorrowData.find("#typCd").val(obj2.typCd);
        L120S01aForm.find(".tab-c-item").show();
        openDocAddBorrow(obj2.oid);
		
		L120S01aForm.find("#btnPrintCreditRisk").show();
		setTimeout(function(){
			$("#invMDscr").val(obj2.L120S01aForm.invMDscr);
		},500);
		
		//J-108-0195_05097_B1001 Web e-Loan企金戶授信簽報書「借款人基本資料」頁面，有關引進「有無金控法44條所稱與金控有利害關係人」增加提醒字句。
		if(obj2.L120S01aForm.showCaRlt206 == "Y"){
			$(".showCaRlt206").show();
		}else{
			$(".showCaRlt206").hide();
		}

		// J-109-0370 相關評估改版
		if (obj2.L120S01aForm.unfConFlag == "Y") {
		    $("#L120S01aForm").find(".unfConMemoShow").show();
		} else {
		    $("#L120S01aForm").find(".unfConMemoShow").hide();
		    $("#L120S01aForm").find(".unfConMemoShow").find("#unfConMemo").val("");
		}
		//	L120S01aForm.find("#unfConFlag").trigger("click");
		//	if (obj2.L120S01aForm.isPrint == "Y") {
		//		L120S01aForm.find("#isPrint").prop("checked", true);
		//	} else {
		//		L120S01aForm.find("#isPrint").prop("checked", false);
		//	}
		
		//if (obj2.L120S01aForm.isPrint == "Y") {
			//L120S01aForm.find("#isPrint").prop("checked", true);
		//}
    });
}

/**
 * 讀取已有的資料以進行修改
 *
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc(cellvalue, options, rowObject){
	$("#borrowPage02,#borrowPage03,#borrowPage04").attr("hasSet2", "false");
    if ($("#lmss02_panel").attr("openFlag") == "true") {
        $("#lmss02_panel").load("../../lms/lmss02b", function(){
            var $L120S01aForm = $("#L120S01aForm");
            //var $L120S01fForm = $("#L120S01fForm");			
            util.init($L120S01aForm);
            //util.init($L120S01fForm);
			$L120S01aForm.buildItem();
			
			//$("#openDocaddborrow").buildItem();
            queryBorrow(rowObject);
        });
    }
    else {
        queryBorrow(rowObject);
    }
};

/**
 * 新增借款人所開啟的ThickBox內容
 *
 * @param value
 * @param titleName
 * @param tWidth
 * @param tHeight
 */
function openList(value, titleName, tWidth, tHeight){
	titleName = i18n.lmss02b["l120s01a.btn1"];
    $("#addborrow").reset();
    $.ajax({
        // 根據外層授信簽報書企金/個金案件自動替User勾選並改成唯讀狀態
        handler: __handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "checkDocType",
            mainId: responseJSON.mainId,
            oid: responseJSON.oid
        }
	}).done(function(json) {
		$("#addborrow").find("[name='docType']").each(function(i){
		    if ($(this).attr("value") == json.docType) {
		        $(this).prop("checked", true);
		    }
		    $(this).prop("disabled", true);
		});		
    });
    
    $("#" + value).thickbox({
        // 使用選取的內容進行彈窗
        title: titleName,
        width: 800,
        height:380,
        modal: true,
/*
        valign: "bottom",
        align: "center",
*/
        i18n: i18n.def,
        buttons: {
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//J-104-0222-001  Web e-Loan授信簽報系統中的借款人列印順序調整
//向上或向下移動
function upDownCustShowSeqNum(upDown){
	var rows = $("#l120s01agrid").getGridParam('selarrrow');
	if (!rows || rows == "") {
        return CommonAPI.showMessage(i18n.def["grid.selrow"])
    }
	if(rows.length > 1){
		return CommonAPI.showMessage(i18n.def["grid.selrow"])
	} 
	var list = "";
	var data;
	if (rows != 'undefined' && rows != null  ) {
		data = $("#l120s01agrid").getRowData(rows);
		list = data.oid;
	} 
	
	upDownCustShowSeqNumF(data, upDown); 
}

//J-104-0222-001  Web e-Loan授信簽報系統中的借款人列印順序調整
function upDownCustShowSeqNumF(data, upDown){
	 
	$.ajax({
		type : "POST",
		handler: __handler,
		data : {
			formAction : "changeCustShowSeqNumMain",
			detailOid : data.oid,
			mainId: responseJSON.mainId,
			upOrDown : upDown
		}
	}).done(function(json) {
		$('#l120s01agrid').jqGrid('setGridParam', { 
			loadComplete: function(){
				var l120s01agrid = $("#l120s01agrid") ;
		    	l120s01agrid.setSelection(json.newSelect); 
				l120s01agrid.jqGrid('setGridParam', { 
					loadComplete: function(){ 
						//執行完後把loadComplete清空，要不然GRID 的REFRESH也會觸發上面的setSelection
					} 
			 	})
			} 
		});
		$("#l120s01agrid").trigger("reloadGrid");//.setSelection(1,true);
	});
	
	
}

function addBorrowMain(showMsg, check){
	borrowData = {};
	var $addborrow = $("#addborrow");
	var buscd = $addborrow.find("#buscd").val();
	var renCd = "";
	if(buscd != "130300" && buscd != "060000" 
	&& buscd != "130300" && buscd != "60000"
	){
		// 記錄法人或自然人
		renCd = "C";
	}else{
		// 自然人
		renCd = "N";		
	}
    $.ajax({
        type: "POST",
        handler: __handler,
        data: {
            formAction: "addBorrowMain",
            mainId: responseJSON.mainId,
            docType: $addborrow.find("input[name='docType']:checked").val(),
            typCd: $addborrow.find("input[name='typCd']:checked").val(),
            custId: $addborrow.find("#custId").val(),
            dupNo: $addborrow.find("#dupNo").val(),
            custName: $addborrow.find("#custName").val(),
			buscd : buscd,
			renCd : renCd,
            idDscr1: "",
            idDscr2: "",
            check: check,
            showMsg: showMsg
        }
	}).done(function(responseData) {
            var $L120S01aForm = $("#L120S01aForm");
            var $L120S01fForm = $("#L120S01fForm");
//            if (responseJSON.docType == "1" &&
//            responseJSON.docKind == "1") {
            if (responseJSON.docType == "1") {
                $("#hUnfCon").hide();
                if (responseJSON.docKind == "1") {
                    $(".sBorrowData").show();
                    $("#noInv").hide();
                    $("#noInv2").hide();
                } else if (responseJSON.docKind == "2") {
//                     $(".sBorrowData").hide();
                     $("#noInv").show();
                     $("#noInv2").show();
                     if ($("input[type='radio'][name='invMFlag']:checked").val() == "1") {
                        $("#hInvMDscr").show();
                     } else {
                        $("#hInvMDscr").hide();
                     }

                     // 授信戶/集團是否有「已核准未完成簽約」  企金一般授權外才顯示
                     $("#hUnfCon").show();
                     if ($("input[type='radio'][name='unfConFlag']:checked").val() == "Y") {
                         $("#L120S01aForm").find(".unfConMemoShow").show();
                     } else {
                         $("#L120S01aForm").find(".unfConMemoShow").hide();
                         $("#L120S01aForm").find(".unfConMemoShow").find("#unfConMemo").val("");
                     }
                 }
            }
            else {
                $(".sBorrowData").hide();
                $("#noInv").show();
                $("#noInv2").hide();
                $("#hUnfCon").hide();
            }
            
            // 初始化前端form
            $("#showBorrowData").reset();
            // 第一頁籤
            $L120S01aForm.reset();
            $L120S01aForm.find("#posType option:eq(2)").prop("selected", true);
            // 第二頁籤
            $("#formIdDscr1").reset();
            $("#L120S01gForm_1").reset();
            // 第三頁籤
            $("#formIdDscr2").reset();
            $("#L120S01gForm_2").reset();
            // 第四頁籤
            $L120S01fForm.reset();
            // 第一頁籤
            $L120S01aForm.setData(responseData.L120S01aForm, false);
            $L120S01aForm.find("#typCd").text(responseData.L120S01aForm.typCd);			
            // 第二頁籤
            var formIdDscr1 = $("#formIdDscr1");
            formIdDscr1.setData(responseData.formIdDscr1, false);
            // 第三頁籤
            var formIdDscr2 = $("#formIdDscr2");
            formIdDscr2.setData(responseData.formIdDscr2, false);
            // $("form").setData(responseData);
            var custRlt = $("#custRlt");
            custRlt.val("");
            $("#custPos option:eq(0)").prop("selected", true);
            $("#chk_radio1").show();
            $L120S01aForm.find("#custRltShow").val("");
            
            if (check) {
                $L120S01aForm.find("#keyMan:checkbox").prop("checked", true);
                //$L120S01aForm.find("#keyMan").val("Y");
                $("#custRltShow").val("");
                $("#custRlt").html("");
                $("#custPos option:eq(0)").prop("selected", true);
                $("#chk_radio1").hide();
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
            else {
                $L120S01aForm.find("#keyMan:checkbox").prop("checked", false);
                //$L120S01aForm.find("#keyMan").val("N"); // 初始化主要借款人
                if ($("#custRlt").html() != "" &&
                $("#custPos option:selected").val() != "") {
                    $("[name='custRlt_content'] option").each(function(i){
                        if ($(this).val() == $("#custRlt").html()) {
                            $("#custRltShow").val($("#custRlt").html() + " " + $(this).text());
                        }
                    });
                    $("#chk_radio1").show();
                }
                else {
                    $("#custRlt").html("");
                    $("#custPos option:eq(0)").prop("selected", true);
                    $("#chk_radio1").show();
                }
            }
            $("#l120s01agrid").trigger("reloadGrid"); // 更新Grid內容
            //$("#spanStock").html($("#stockStatus option:eq(0)").html());
			$L120S01aForm.find("#stockStatus").nextAll().hide();
            $L120S01aForm.find("#spanStock,#stockDate").val('');
	        //隱藏股票代碼以及股價欄位
	        $L120S01aForm.find(".classStock").hide();			
            $L120S01aForm.find(".none").show();
            $L120S01aForm.find(".hideMow1").hide();
            $L120S01aForm.find(".hideMow2").hide();
            $L120S01aForm.find(".hideMow3").hide();
            $L120S01aForm.find(".hideMow4").hide();
            $L120S01aForm.find(".hideMow5").hide();
            $L120S01aForm.find(".hideMow6").hide();
			$L120S01aForm.find(".hideMow7").hide();
			//J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
			$L120S01aForm.find(".hideMow8").hide();
			$L120S01aForm.find(".hideMow9").hide();
			
			showHide($("#L120S01gForm_1").find("#rcdFlag"),'.hideThis');
			checkUncheck($("#L120S01gForm_1").find("#rcdFlag"),'.cesCheck');
            
            if (responseJSON.docType == "1" &&
            responseJSON.docKind == "1") {
				if (responseJSON.docCode == "1" &&
				responseJSON.miniFlag == "Y") {
				// J-108-0243 微型企業
				// 企金一般授權內微型企業要show徵信資料
				}
				else {
					$(".hBorrowData2").hide();
				}
            }
			$L120S01aForm.find("#rgtCurr").val("");
			$L120S01aForm.find("#rgtAmt").val("");
			$L120S01aForm.find("#rgtUnit").val("1");
			$L120S01aForm.find("#cptlCurr").val("");
			$L120S01aForm.find("#cptlAmt").val("");
			$L120S01aForm.find("#cptlUnit").val("1");
			$L120S01aForm.find("#cmpAddr").val("");
			$L120S01aForm.find("#factoryAddr").val("");
			
			//J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
			$L120S01aForm.find("#netSwft").val("");
			$L120S01aForm.find("#netAmt").val("");
			$L120S01aForm.find("#netAmtUnit").val("1");

            
            // 開始設定資料到前端...
			var showBorrowData = $("#showBorrowData");
			showBorrowData.setData(responseData.showBorrowData, false);
			openDocAddBorrow(responseData.oid); // 開啟ThickBox
			setTimeout(function(){
				var invMDscr =$("#invMDscr");
				var idDscr1 = $("#idDscr1");
				var idDscr2 = $("#idDscr2");
				invMDscr.val("");
				idDscr1.val("");
				idDscr2.val("");
				// 自動點選重新引進按鈕
				getCustData2();
			},500);
    });
}

function addNewBor(showMsg, check){
	var $hBorrowData = $(".hBorrowData");
	var $sBorrowData = $(".sBorrowData");
	$("#borrowPage02,#borrowPage03,#borrowPage04").attr("hasSet2", "false");	
    if ($("#lmss02_panel").attr("openFlag") == "true") {		
        $("#lmss02_panel").load("../../lms/lmss02b", function(){
			//$("#openDocaddborrow").buildItem();
			$("#L120S01aForm").buildItem();
            if (responseJSON.docCode == "2" ||
            responseJSON.docCode == "3" ||
			responseJSON.docCode == "4") {
            }
            addBorrowMain(showMsg, check);
        });
        $("#lmss02_panel").attr("openFlag", false);
    }
    else {
		//$("#openDocaddborrow").buildItem();
        if (responseJSON.docCode == "2" ||
        responseJSON.docCode == "3" ||
		responseJSON.docCode == "4") {
        }
        addBorrowMain(showMsg, check)
    }
}



