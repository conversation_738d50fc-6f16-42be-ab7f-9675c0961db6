/* 
 * L120S11A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 本案借款人同時為其他授信戶應收帳款債務人之額度資料 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S11A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "custId2", "dupNo2" }))
public class L120S11A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 統一編號
	 * <p/>
	 * 申貸戶ID
	 */
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Column(name = "CUSTNAME", length = 150, columnDefinition = "VARCHAR(150)")
	private String custName;

	/**
	 * 列印順序
	 * <p/>
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "PRINTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer printSeq;

	/**
	 * 序號
	 * <p/>
	 * 合計=99999
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "ITEMSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer itemSeq;

	/**
	 * 關係企業統一編號
	 * <p/>
	 * 關係企業ID<br/>
	 * 合計= 9999999999
	 */
	@Size(max = 10)
	@Column(name = "CUSTID2", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId2;

	/**
	 * 關係企業重覆序號
	 * <p/>
	 * 合計= 9
	 */
	@Size(max = 1)
	@Column(name = "DUPNO2", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo2;

	/** 關係企業名稱 **/
	@Column(name = "CUSTNAME2", length = 150, columnDefinition = "VARCHAR(150)")
	private String custName2;

	/**
	 * 關係代碼
	 * <p/>
	 * 1.申貸戶本人<br/>
	 * 2.同一關係企業<br/>
	 * 11.負責人<br/>
	 * 12.配偶(0024)<br/>
	 * 13.配偶(LNFE085)<br/>
	 * 14.一等親
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "RELTYPE", columnDefinition = "DECIMAL(3,0)")
	private Integer relType;

	/**
	 * 關係企業負責人身分證字號
	 * <p/>
	 * relType為11、12、13、14有值
	 */
	@Size(max = 10)
	@Column(name = "SUPCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String supCustId;

	/**
	 * 關係企業負責人重複序號
	 * <p/>
	 * relType為11、12、13、14有值
	 */
	@Size(max = 1)
	@Column(name = "SUPDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String supDupNo;

	/**
	 * 關係企業負責人戶名
	 * <p/>
	 * relType為11、12、13、14有值
	 */
	@Size(max = 150)
	@Column(name = "SUPNAME", length = 150, columnDefinition = "VARCHAR(150)")
	private String supName;

	/** 授信幣別 **/
	@Size(max = 3)
	@Column(name = "FACTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String factCurr;

	/** 授信額度金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "FACTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal factAmt;
	
	/** 
	 * 擔保授信額度幣別<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 */
	@Size(max = 3)
	@Column(name = "FACTCURRS", length = 3, columnDefinition = "CHAR(3)")
	private String factCurrS;

	/** 
	 * 擔保授信額度金額<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "FACTAMTS", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal factAmtS;

	/** 
	 * 無擔保授信額度幣別<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 */
	@Size(max = 3)
	@Column(name = "FACTCURRN", length = 3, columnDefinition = "CHAR(3)")
	private String factCurrN;

	/** 
	 * 無擔保授信額度金額<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "FACTAMTN", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal factAmtN;

	/** 進口幣別 **/
	@Size(max = 3)
	@Column(name = "IMCURR", length = 3, columnDefinition = "CHAR(3)")
	private String imCurr;

	/** 進口額度金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "IMAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal imAmt;

	/** 出口幣別 **/
	@Size(max = 3)
	@Column(name = "EXCURR", length = 3, columnDefinition = "CHAR(3)")
	private String exCurr;

	/** 出口額度金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "EXAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal exAmt;

	/** 出口符合出口實績規範者額度金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "EXEXPERFYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal exExperfYAmt;

	/** 不符出口實績規範者額度金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "EXEXPERFNAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal exExperfNAmt;

	/** 瑕疵單據額度限額金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "EXFLAWAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal exFlawAmt;

	/** 應收帳款承購賣方幣別 **/
	@Size(max = 3)
	@Column(name = "ARSELLERCURR", length = 3, columnDefinition = "CHAR(3)")
	private String arSellerCurr;

	/** 應收帳款承購賣方額度金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "ARSELLERAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal arSellerAmt;

	/** 衍生性商品交易幣別 **/
	@Size(max = 3)
	@Column(name = "SUBMITCURR", length = 3, columnDefinition = "CHAR(3)")
	private String submitCurr;

	/** 衍生性商品交易額度金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "SUBMITAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal submitAmt;
	
	/** 
	 * 版本編號<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權<br/>
	 *  上版後的資料才會塞1，區分新舊資料不同，附表列印不同樣式
	 */
	@Size(max = 3)
	@Column(name = "VERSION", length = 3, columnDefinition = "VARCHAR(3)")
	private String version;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "Date")
	private Date dataDate;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * LGD合計版本
	 */
	@Column(name = "LGDTOTAMT_VER", length = 13, columnDefinition = "CHAR(13)")
	private String lgdTotAmt_Ver;
	
	/**
	 * LGD1合計
	 */
	@Column(name = "LGDTOTAMT_1", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_1;

	/**
	 * LGD2合計
	 */
	@Column(name = "LGDTOTAMT_2", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_2;

	/**
	 * LGD3合計
	 */
	@Column(name = "LGDTOTAMT_3", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_3;

	/**
	 * LGD4合計
	 */
	@Column(name = "LGDTOTAMT_4", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_4;

	/**
	 * LGD5合計
	 */
	@Column(name = "LGDTOTAMT_5", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_5;
	
	/** 關係代碼說明(非DB欄位) **/
	@Transient
	private String relTypeStr;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得統一編號
	 * <p/>
	 * 申貸戶ID
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定統一編號
	 * <p/>
	 * 申貸戶ID
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得序號
	 * <p/>
	 * 合計=99999
	 */
	public Integer getItemSeq() {
		return this.itemSeq;
	}

	/**
	 * 設定序號
	 * <p/>
	 * 合計=99999
	 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/**
	 * 取得關係企業統一編號
	 * <p/>
	 * 關係企業ID<br/>
	 * 合計= 9999999999
	 */
	public String getCustId2() {
		return this.custId2;
	}

	/**
	 * 設定關係企業統一編號
	 * <p/>
	 * 關係企業ID<br/>
	 * 合計= 9999999999
	 **/
	public void setCustId2(String value) {
		this.custId2 = value;
	}

	/**
	 * 取得關係企業重覆序號
	 * <p/>
	 * 合計= 9
	 */
	public String getDupNo2() {
		return this.dupNo2;
	}

	/**
	 * 設定關係企業重覆序號
	 * <p/>
	 * 合計= 9
	 **/
	public void setDupNo2(String value) {
		this.dupNo2 = value;
	}

	/** 取得關係企業名稱 **/
	public String getCustName2() {
		return this.custName2;
	}

	/** 設定關係企業名稱 **/
	public void setCustName2(String value) {
		this.custName2 = value;
	}

	/**
	 * 取得關係代碼
	 * <p/>
	 * 1.申貸戶本人<br/>
	 * 2.同一關係企業<br/>
	 * 11.負責人<br/>
	 * 12.配偶(0024)<br/>
	 * 13.配偶(LNFE085)<br/>
	 * 14.一等親
	 */
	public Integer getRelType() {
		return this.relType;
	}

	/**
	 * 設定關係代碼
	 * <p/>
	 * 1.申貸戶本人<br/>
	 * 2.同一關係企業<br/>
	 * 11.負責人<br/>
	 * 12.配偶(0024)<br/>
	 * 13.配偶(LNFE085)<br/>
	 * 14.一等親
	 **/
	public void setRelType(Integer value) {
		this.relType = value;
	}

	/**
	 * 取得關係企業負責人身分證字號
	 * <p/>
	 * relType為11、12、13、14有值
	 */
	public String getSupCustId() {
		return this.supCustId;
	}

	/**
	 * 設定關係企業負責人身分證字號
	 * <p/>
	 * relType為11、12、13、14有值
	 **/
	public void setSupCustId(String value) {
		this.supCustId = value;
	}

	/**
	 * 取得關係企業負責人重複序號
	 * <p/>
	 * relType為11、12、13、14有值
	 */
	public String getSupDupNo() {
		return this.supDupNo;
	}

	/**
	 * 設定關係企業負責人重複序號
	 * <p/>
	 * relType為11、12、13、14有值
	 **/
	public void setSupDupNo(String value) {
		this.supDupNo = value;
	}

	/**
	 * 取得關係企業負責人戶名
	 * <p/>
	 * relType為11、12、13、14有值
	 */
	public String getSupName() {
		return this.supName;
	}

	/**
	 * 設定關係企業負責人戶名
	 * <p/>
	 * relType為11、12、13、14有值
	 **/
	public void setSupName(String value) {
		this.supName = value;
	}

	/** 取得授信幣別 **/
	public String getFactCurr() {
		return this.factCurr;
	}

	/** 設定授信幣別 **/
	public void setFactCurr(String value) {
		this.factCurr = value;
	}

	/** 取得授信額度金額 **/
	public BigDecimal getFactAmt() {
		return this.factAmt;
	}

	/** 設定授信額度金額 **/
	public void setFactAmt(BigDecimal value) {
		this.factAmt = value;
	}

	/** 
	 * 取得擔保授信額度幣別<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 */
	public String getFactCurrS() {
		return this.factCurrS;
	}
	/**
	 *  設定擔保授信額度幣別<p/>
	 *  2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 **/
	public void setFactCurrS(String value) {
		this.factCurrS = value;
	}

	/** 
	 * 取得擔保授信額度金額<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 */
	public BigDecimal getFactAmtS() {
		return this.factAmtS;
	}
	/**
	 *  設定擔保授信額度金額<p/>
	 *  2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 **/
	public void setFactAmtS(BigDecimal value) {
		this.factAmtS = value;
	}

	/** 
	 * 取得無擔保授信額度幣別<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 */
	public String getFactCurrN() {
		return this.factCurrN;
	}
	/**
	 *  設定無擔保授信額度幣別<p/>
	 *  2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 **/
	public void setFactCurrN(String value) {
		this.factCurrN = value;
	}

	/** 
	 * 取得無擔保授信額度金額<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 */
	public BigDecimal getFactAmtN() {
		return this.factAmtN;
	}
	/**
	 *  設定無擔保授信額度金額<p/>
	 *  2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權
	 **/
	public void setFactAmtN(BigDecimal value) {
		this.factAmtN = value;
	}

	/** 取得進口幣別 **/
	public String getImCurr() {
		return this.imCurr;
	}

	/** 設定進口幣別 **/
	public void setImCurr(String value) {
		this.imCurr = value;
	}

	/** 取得進口額度金額 **/
	public BigDecimal getImAmt() {
		return this.imAmt;
	}

	/** 設定進口額度金額 **/
	public void setImAmt(BigDecimal value) {
		this.imAmt = value;
	}

	/** 取得出口幣別 **/
	public String getExCurr() {
		return this.exCurr;
	}

	/** 設定出口幣別 **/
	public void setExCurr(String value) {
		this.exCurr = value;
	}

	/** 取得出口符合出口實績規範者額度金額 **/
	public BigDecimal getExExperfYAmt() {
		return this.exExperfYAmt;
	}

	/** 設定出口符合出口實績規範者額度金額 **/
	public void setExExperfYAmt(BigDecimal value) {
		this.exExperfYAmt = value;
	}

	/** 取得不符出口實績規範者額度金額 **/
	public BigDecimal getExExperfNAmt() {
		return this.exExperfNAmt;
	}

	/** 設定不符出口實績規範者額度金額 **/
	public void setExExperfNAmt(BigDecimal value) {
		this.exExperfNAmt = value;
	}

	/** 取得瑕疵單據額度限額金額 **/
	public BigDecimal getExFlawAmt() {
		return this.exFlawAmt;
	}

	/** 設定瑕疵單據額度限額金額 **/
	public void setExFlawAmt(BigDecimal value) {
		this.exFlawAmt = value;
	}

	/** 取得應收帳款承購賣方幣別 **/
	public String getArSellerCurr() {
		return this.arSellerCurr;
	}

	/** 設定應收帳款承購賣方幣別 **/
	public void setArSellerCurr(String value) {
		this.arSellerCurr = value;
	}

	/** 取得應收帳款承購賣方額度金額 **/
	public BigDecimal getArSellerAmt() {
		return this.arSellerAmt;
	}

	/** 設定應收帳款承購賣方額度金額 **/
	public void setArSellerAmt(BigDecimal value) {
		this.arSellerAmt = value;
	}

	/** 取得衍生性商品交易幣別 **/
	public String getSubmitCurr() {
		return this.submitCurr;
	}

	/** 設定衍生性商品交易幣別 **/
	public void setSubmitCurr(String value) {
		this.submitCurr = value;
	}

	/** 取得衍生性商品交易額度金額 **/
	public BigDecimal getSubmitAmt() {
		return this.submitAmt;
	}

	/** 設定衍生性商品交易額度金額 **/
	public void setSubmitAmt(BigDecimal value) {
		this.submitAmt = value;
	}

	/** 
	 * 取得版本編號<p/>
	 * 2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權<br/>
	 *  上版後的資料才會塞1，區分新舊資料不同，附表列印不同樣式
	 */
	public String getVersion() {
		return this.version;
	}
	/**
	 *  設定版本編號<p/>
	 *  2021-09-01新增<br/>
	 *  J-110-0325-001<br/>
	 *  判斷金額是否逾越授權<br/>
	 *  上版後的資料才會塞1，區分新舊資料不同，附表列印不同樣式
	 **/
	public void setVersion(String value) {
		this.version = value;
	}
	
	/** 取得資料日期 **/
	public Date getDataDate() {
		return this.dataDate;
	}

	/** 設定資料日期 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定出口額度金額 **/
	public void setExAmt(BigDecimal exAmt) {
		this.exAmt = exAmt;
	}

	/** 取得出口額度金額 **/
	public BigDecimal getExAmt() {
		return exAmt;
	}

	/** 設定列印順序 **/
	public void setPrintSeq(Integer printSeq) {
		this.printSeq = printSeq;
	}

	/** 取得列印順序 **/
	public Integer getPrintSeq() {
		return printSeq;
	}

	/** 設定關係代碼說明 **/
	public void setRelTypeStr(String relTypeStr) {
		this.relTypeStr = relTypeStr;
	}

	/** 取得關係代碼說明 **/
	public String getRelTypeStr() {
		return relTypeStr;
	}
	
	/**
	 * 設定LGD合計版本
	 */
	public void setLgdTotAmt_Ver(String lgdTotAmt_Ver) {
		this.lgdTotAmt_Ver = lgdTotAmt_Ver;
	}

	/**
	 * 取得LGD合計版本
	 */
	public String getLgdTotAmt_Ver() {
		return lgdTotAmt_Ver;
	}

	/** 設定LGD1合計 **/
	public void setLgdTotAmt_1(BigDecimal lgdTotAmt_1) {
		this.lgdTotAmt_1 = lgdTotAmt_1;
	}

	/**
	 * 取得LGD1合計
	 */
	public BigDecimal getLgdTotAmt_1() {
		return lgdTotAmt_1;
	}

	/** 設定LGD2合計 **/
	public void setLgdTotAmt_2(BigDecimal lgdTotAmt_2) {
		this.lgdTotAmt_2 = lgdTotAmt_2;
	}

	/**
	 * 取得LGD2合計
	 */
	public BigDecimal getLgdTotAmt_2() {
		return lgdTotAmt_2;
	}

	/** 設定LGD3合計 **/
	public void setLgdTotAmt_3(BigDecimal lgdTotAmt_3) {
		this.lgdTotAmt_3 = lgdTotAmt_3;
	}

	/**
	 * 取得LGD3合計
	 */
	public BigDecimal getLgdTotAmt_3() {
		return lgdTotAmt_3;
	}

	/** 設定LGD4合計 **/
	public void setLgdTotAmt_4(BigDecimal lgdTotAmt_4) {
		this.lgdTotAmt_4 = lgdTotAmt_4;
	}

	/**
	 * 取得LGD4合計
	 */
	public BigDecimal getLgdTotAmt_4() {
		return lgdTotAmt_4;
	}

	/** 設定LGD5合計 **/
	public void setLgdTotAmt_5(BigDecimal lgdTotAmt_5) {
		this.lgdTotAmt_5 = lgdTotAmt_5;
	}

	/**
	 * 取得LGD5合計
	 */
	public BigDecimal getLgdTotAmt_5() {
		return lgdTotAmt_5;
	}
}
