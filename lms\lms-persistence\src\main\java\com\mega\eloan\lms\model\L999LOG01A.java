/* 
 * L999LOG01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 額度進階查詢記錄檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L999LOG01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L999LOG01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 查詢類別
	 * <p/>
	 * 2.額度明細表查詢
	 */
	@Size(max = 1)
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/**
	 * 查詢結果
	 * <p/>
	 * Y.成功<br/>
	 * N.失敗<br/>
	 * ?.無結果
	 */
	@Size(max = 1)
	@Column(name = "RESULT", length = 1, columnDefinition = "CHAR(1)")
	private String result;

	/** 項目說明 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "ITEMDSCR", columnDefinition = "CLOB")
	private String itemDscr;

	/** 執行訊息 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "EXECMSG", columnDefinition = "CLOB")
	private String execMsg;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 結果筆數<br/>
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "EXECOUNT", length = 1, columnDefinition = "DECIMAL(5,0)")
	private BigDecimal exeCount;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得查詢類別
	 * <p/>
	 * 2.額度明細表查詢
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定查詢類別
	 * <p/>
	 * 2.額度明細表查詢
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/**
	 * 取得查詢結果
	 * <p/>
	 * Y.成功<br/>
	 * N.失敗<br/>
	 * ?.無結果
	 */
	public String getResult() {
		return this.result;
	}

	/**
	 * 設定查詢結果
	 * <p/>
	 * Y.成功<br/>
	 * N.失敗<br/>
	 * ?.無結果
	 **/
	public void setResult(String value) {
		this.result = value;
	}

	/** 取得項目說明 **/
	public String getItemDscr() {
		return this.itemDscr;
	}

	/** 設定項目說明 **/
	public void setItemDscr(String value) {
		this.itemDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public void setExecMsg(String execMsg) {
		this.execMsg = execMsg;
	}

	public String getExecMsg() {
		return execMsg;
	}

	public void setExeCount(BigDecimal exeCount) {
		this.exeCount = exeCount;
	}

	public BigDecimal getExeCount() {
		return exeCount;
	}
}
