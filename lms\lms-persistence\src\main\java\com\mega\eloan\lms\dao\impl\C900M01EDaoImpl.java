/* 
 * C900M01EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.lms.dao.C900M01EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01E;

/** 疑似利用偽造證件或財力證明用以申辦信用卡或貸款警示訊息檔 **/
@Repository
public class C900M01EDaoImpl extends LMSJpaDao<C900M01E, String>
	implements C900M01EDao {

	@Override
	public C900M01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C900M01E findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
		
	@Override
	public C900M01E findActiveByCustId(String custId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime","");
		search.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, FlowDocStatusEnum.已核准.getCode());
		{
			Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
			map.put("approveTime", true);
			search.setOrderBy(map);	
		}
		C900M01E r = findUniqueOrNone(search);
		if(r==null){
			return null;
		}else{
			/*
			@ CLS9061M01Page.html
			
				isClosed=N 未解除
				isClosed=Y 解除
			*/
			if(r.isClosed()){ //若 已"解除" 
				return null;
			}
			return r;	
		}		
	}
}