/* 
 * L160M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.IDataObject;

/** 動用審核表主檔 **/
@NamedEntityGraph(name = "L160M01A-entity-graph", attributeNodes = { @NamedAttributeNode("l163s01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L160M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L160M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L160A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l160m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L160A01A> l160a01a;

	public Set<L160A01A> getL160a01a() {
		return l160a01a;
	}

	public void setL160a01a(Set<L160A01A> l160a01a) {
		this.l160a01a = l160a01a;
	}

	/**
	 * JOIN條件 L160M01B．動審表額度序號資料
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l160m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L160M01B> l160m01b;

	public Set<L160M01B> getL160m01b() {
		return l160m01b;
	}

	public void setL160m01b(Set<L160M01B> l160m01b) {
		this.l160m01b = l160m01b;
	}

	/**
	 * JOIN條件 L160M01D．案件簽章欄檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l160m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L160M01D> l160m01d;

	/**
	 * 案件簽章欄檔
	 * 
	 * @return L160M01D
	 */
	public Set<L160M01D> getL160M01D() {
		return l160m01d;
	}

	public void setL160M01D(Set<L160M01D> l160m01d) {
		this.l160m01d = l160m01d;
	}

	/**
	 * JOIN條件 L162S01A．主從債務人資料表檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l160m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L162S01A> l162s01a;

	/**
	 * 取得主從債務人資料表檔
	 * 
	 * @return L162S01A
	 */
	public Set<L162S01A> getL162S01A() {
		return l162s01a;
	}

	public void setL162S01A(Set<L162S01A> l162s01a) {
		this.l162s01a = l162s01a;
	}

	/**
	 * JOIN條件 L161S01A． 額度動用資訊一覽表主檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l160m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L161S01A> l161s01a;

	/**
	 * 取得聯貸案參貸比率一覽表主檔
	 * 
	 * @return L161S01A
	 */
	public Set<L161S01A> getL161S01A() {
		return l161s01a;
	}

	public void setL161S01A(Set<L161S01A> l161s01a) {
		this.l161s01a = l161s01a;
	}

	/**
	 * JOIN條件 L163S01A．先行動用呈核及控制表檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.EAGER)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L163S01A l163s01a;

	public L163S01A getL163S01A() {
		return l163s01a;
	}

	public void setL163S01A(L163S01A l163s01a) {
		this.l163s01a = l163s01a;
	}

	/**
	 * 來源註記
	 * <p/>
	 * 100/12/20新增<br/>
	 * 1.案件簽報書<br/>
	 * 2.聯行額度明細表
	 */
	@Size(max = 1)
	@Column(name = "DATASRC", length = 1, columnDefinition = "CHAR(1)")
	private String dataSrc;

	/**
	 * 來源文件編號
	 * <p/>
	 * 100/12/20調整<br/>
	 * 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId
	 */
	@Size(max = 32)
	@Column(name = "SRCMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String srcMainId;

	/**
	 * 案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 4, fraction = 0, groups = Check.class)
	@Column(name = "CASEYEAR", columnDefinition = "DECIMAL(4,0)")
	private Integer caseYear;

	/**
	 * 案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/**
	 * 是否先行動用
	 * <p/>
	 * 100/10/17新增<br/>
	 * Y/N 用來判斷是否有先行動用表
	 */
	@Size(max = 1)
	@Column(name = "USETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String useType;

	/**
	 * 案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "CASESEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer caseSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 案件審核層級
	 * <p/>
	 * 100/10/06新增<br/>
	 * 資料來源：案件簽報書<br/>
	 * 常董會權限、常董會權限簽奉總經理核批、常董會權限簽准由副總經理核批、利費率變更案件由總處經理核定、屬常董會授權總經理逕核案件、總經理權限內、
	 * 副總經理權限、企金部協理權限、其他、董事會權限、利費率變更案件由董事長核定、個金處經理權限、區域營運中心主任/副主任權限
	 */
	@Size(max = 2)
	@Column(name = "CASELVL", length = 2, columnDefinition = "CHAR(2)")
	private String caseLvl;

	/**
	 * 簽報書項下額度明細表是否全部動用
	 * <p/>
	 * Y/N<br/>
	 * 如為Y則固定顯示："簽報書項下額度明細表全部動用"；如為N則顯示所有額度序號。
	 */
	@Size(max = 1)
	@Column(name = "ALLCANPAY", length = 1, columnDefinition = "CHAR(1)")
	private String allCanPay;

	/**
	 * 授信契約書期別
	 * <p/>
	 * 1短期2中長期
	 */
	@Size(max = 1)
	@Column(name = "TTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String tType;

	/**
	 * 動用期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 */
	@Size(max = 1)
	@Column(name = "USESELECT", length = 1, columnDefinition = "CHAR(1)")
	private String useSelect;

	/**
	 * 動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "USEFROMDATE", columnDefinition = "DATE")
	private Date useFromDate;

	/**
	 * 動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "USEENDDATE", columnDefinition = "DATE")
	private Date useEndDate;

	/**
	 * 動用期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "USEMONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer useMonth;

	/**
	 * 動用期間-其他
	 * <p/>
	 * 3.其他 <br/>
	 * 102.02.18 欄位擴大 60 -> 900
	 */
	@Size(max = 900)
	@Column(name = "USEOTHER", length = 900, columnDefinition = "VARCHAR(900)")
	private String useOther;

	/**
	 * 授信期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 */
	@Size(max = 1)
	@Column(name = "LNSELECT", length = 1, columnDefinition = "CHAR(1)")
	private String lnSelect;

	/**
	 * 授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNFROMDATE", columnDefinition = "DATE")
	private Date lnFromDate;

	/**
	 * 授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNENDDATE", columnDefinition = "DATE")
	private Date lnEndDate;

	/**
	 * 授信期間-年數
	 * <p/>
	 * 100/10/11新增<br/>
	 * 2.自首動日起YY年
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNYEAR", columnDefinition = "DECIMAL(2,0)")
	private Integer lnYear;

	/**
	 * 授信期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNMONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer lnMonth;

	/**
	 * 授信期間-其他
	 * <p/>
	 * 3.其他
	 * 
	 * <br/>
	 * 102.02.18 欄位擴大 60 -> 900
	 */
	@Size(max = 900)
	@Column(name = "LNOTHER", length = 900, columnDefinition = "VARCHAR(900)")
	private String lnOther;

	/** 授信約定書簽約日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "SIGNDATE", columnDefinition = "DATE")
	private Date signDate;

	/** 連帶保證書最高本金-幣別 **/
	@Size(max = 3)
	@Column(name = "GUCURR", length = 3, columnDefinition = "CHAR(3)")
	private String guCurr;

	/** 連帶保證書最高本金-金額 **/
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "GUAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal guAmt;

	/** 連帶保證書最高本金-起始日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GUFROMDATE", columnDefinition = "DATE")
	private Date guFromDate;

	/** 連帶保證書最高本金-截止日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GUENDDATE", columnDefinition = "DATE")
	private Date guEndDate;

	/**
	 * 違約金計算條件-逾期
	 * <p/>
	 * ※案件若轉入催收系統時，可依此計算違約金。欄位值一定要輸入但不印於報表。()為預設值，可修改。<br/>
	 * 借戶如延遲還本或付息時，本金自到期日起，利息自繳息日起，逾期在(6)個月以內部份，按約定利率百分之(10)，逾期超過(6)個月部份，
	 * 按約定利率百分之(20)計付違約金。<br/>
	 * <br/>
	 * 逾期在XX個月以內部份
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "DMONTH1", columnDefinition = "DECIMAL(2,0)")
	private Integer dMonth1;

	/**
	 * 違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "DRATE1", columnDefinition = "DECIMAL(2,0)")
	private Integer dRate1;

	/**
	 * 違約金計算條件-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "DMONTH2", columnDefinition = "DECIMAL(2,0)")
	private Integer dMonth2;

	/**
	 * 違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "DRATE2", columnDefinition = "DECIMAL(2,0)")
	private Integer dRate2;

	/**
	 * 計收延遲利息加碼
	 * <p/>
	 * 案件若有轉入催收系統時，可依此計算延遲利息，預設為1.00%，可修改。<br/>
	 * ※此欄不列印於報表
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "DRATEADD", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal dRateAdd;

	/** 查核事項-資料查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BLACKDATADATE", columnDefinition = "DATE")
	private Date blackDataDate;

	/**
	 * 黑名單查詢結果
	 * <p/>
	 * ※gfnDB2calllnsp0130=02或04<br/>
	 * 64個全型字<br/>
	 * 101/04/16調整<br/>
	 * 192 ( 1024
	 */
	@Column(name = "BLACKLISTTXTERR", length = 3000, columnDefinition = "VARCHAR(3000)")
	private String blackListTxtErr;

	/**
	 * 黑名單查詢結果
	 * <p/>
	 * ※gfnDB2calllnsp0130=02或04以外<br/>
	 * 64個全型字<br/>
	 * 101/04/16調整<br/>
	 * 192 ( 1024
	 */
	@Column(name = "BLACKLISTTXTOK", length = 3000, columnDefinition = "VARCHAR(3000)")
	private String blackListTxtOK;

	/**
	 * 共同行銷
	 * <p/>
	 * 2012-11-15新增<br/>
	 * 1000個全型字
	 */
	@Column(name = "JOINMARKETING", length = 4500, columnDefinition = "VARCHAR(4500)")
	private String joinMarketing;

	/**
	 * 共同行銷查詢日期
	 * <p/>
	 * 2012-11-15新增
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "JOINMARKETINGDATE", columnDefinition = "DATE")
	private Date joinMarketingDate;

	/**
	 * 批示
	 * <p/>
	 * 128個全型字 <br/>
	 * 102.02.18 欄位擴大 384 -> 900
	 */
	@Size(max = 900)
	@Column(name = "SIGN", length = 900, columnDefinition = "VARCHAR(900)")
	private String sign;

	/**
	 * 審核意見
	 * <p/>
	 * 預設：貸放手續齊全，擬准予動用<br/>
	 * 128個全型字 <br/>
	 * 102.02.18 欄位擴大 384 -> 1800
	 */
	@Size(max = 1800)
	@Column(name = "COMM", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String comm;

	/** 經辦 **/
	@Size(max = 6)
	@Column(name = "APPRID", length = 6, columnDefinition = "CHAR(6)")
	private String apprId;

	/** 覆核主管 **/
	@Size(max = 6)
	@Column(name = "RECHECKID", length = 6, columnDefinition = "CHAR(6)")
	private String reCheckId;

	/**
	 * 授信主管
	 * <p/>
	 * 呈主管覆核時，勾選授信主管名單
	 */
	@Size(max = 6)
	@Column(name = "BOSSID", length = 6, columnDefinition = "CHAR(6)")
	private String bossId;

	/**
	 * 經副襄理
	 * <p/>
	 * 呈主管覆核時，勾選經副襄理名單
	 */
	@Size(max = 6)
	@Column(name = "MANAGERID", length = 6, columnDefinition = "CHAR(6)")
	private String managerId;

	/**
	 * 本案是否有同業聯貸案額度
	 * <p/>
	 * 引自簽報書/聯行額度明細表，引進不列印<br/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "UNITLOANCASE", length = 1, columnDefinition = "CHAR(1)")
	private String unitLoanCase;

	/**
	 * 本分行是否為管理行
	 * <p/>
	 * 引自簽報書/聯行額度明細表，引進不列印<br/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "UCMAINBRANCH", length = 1, columnDefinition = "CHAR(1)")
	private String uCMainBranch;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Size(max = 32)
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/**
	 * NEWVERSION
	 * <p/>
	 * 動審表版本
	 */
	@Size(max = 2)
	@Column(name = "NEWVERSION", length = 2, columnDefinition = "CHAR(2)")
	private String newVersion;

	/**
	 * NOEDIT1
	 * <p/>
	 * 免填授信約定書簽約日期
	 */
	@Size(max = 1)
	@Column(name = "NOEDIT1", length = 1, columnDefinition = "VARCHAR(1)")
	private String noEdit1;

	/**
	 * NOEDIT2
	 * <p/>
	 * 免填連帶保證書最高本金
	 */
	@Size(max = 1)
	@Column(name = "NOEDIT2", length = 1, columnDefinition = "VARCHAR(1)")
	private String noEdit2;

	/**
	 * NOEDIT3
	 * <p/>
	 * 免填違約金計算條件
	 */
	@Size(max = 1)
	@Column(name = "NOEDIT3", length = 1, columnDefinition = "VARCHAR(1)")
	private String noEdit3;

	/**
	 * NOEDIT4
	 * <p/>
	 * 免填計收遲延利息加碼
	 */
	@Size(max = 1)
	@Column(name = "NOEDIT4", length = 1, columnDefinition = "VARCHAR(1)")
	private String noEdit4;

	/**
	 * ISSMALLBUSS
	 * <p/>
	 * 小規模查核事項
	 */
	@Size(max = 1)
	@Column(name = "ISSMALLBUSS", length = 1, columnDefinition = "VARCHAR(1)")
	private String isSmallBuss;

	/** 小規模查核事項適用起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "SMALLBUSSCHKLISTDATE", columnDefinition = "DATE")
	private Date smallBussChkListDate;

	/** 微型企業註記 */
	@Column(name = "MINIFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String miniFlag;

	/**
	 * 適用方案
	 * <p/>
	 */
	@Column(name = "CASETYPE", length = 3, columnDefinition = "CHAR(3)")
	private String caseType;

	/** 
	 * 系統實際核准日期<p/>
	 * J-110-0547-001 2022/03/04<br/>
	 *  讓批次寫貸後管理用
	 */
	@Column(name="SYSACTAPPROVETIME", columnDefinition="TIMESTAMP")
	private Timestamp sysActApproveTime;
	
	/**
	 * 取得來源註記
	 * <p/>
	 * 100/12/20新增<br/>
	 * 1.案件簽報書<br/>
	 * 2.聯行額度明細表
	 */
	public String getDataSrc() {
		return this.dataSrc;
	}

	/**
	 * 設定來源註記
	 * <p/>
	 * 100/12/20新增<br/>
	 * 1.案件簽報書<br/>
	 * 2.聯行額度明細表
	 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}

	/**
	 * 取得來源文件編號
	 * <p/>
	 * 100/12/20調整<br/>
	 * 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId
	 */
	public String getSrcMainId() {
		return this.srcMainId;
	}

	/**
	 * 設定來源文件編號
	 * <p/>
	 * 100/12/20調整<br/>
	 * 來源簽報書/聯行額度明細表的文件編號<br/>
	 * 來源：L120M01A.mainId<br/>
	 * 來源：L141M01A.mainId
	 **/
	public void setSrcMainId(String value) {
		this.srcMainId = value;
	}

	/**
	 * 取得案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseYear() {
		return this.caseYear;
	}

	/**
	 * 設定案件號碼-年度
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseYear(Integer value) {
		this.caseYear = value;
	}

	/**
	 * 取得案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/**
	 * 設定案件號碼-分行
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/**
	 * 取得是否先行動用
	 * <p/>
	 * 100/10/17新增<br/>
	 * Y/N 用來判斷是否有先行動用表
	 */
	public String getUseType() {
		return this.useType;
	}

	/**
	 * 設定是否先行動用
	 * <p/>
	 * 100/10/17新增<br/>
	 * Y/N 用來判斷是否有先行動用表
	 **/
	public void setUseType(String value) {
		this.useType = value;
	}

	/**
	 * 取得案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 */
	public Integer getCaseSeq() {
		return this.caseSeq;
	}

	/**
	 * 設定案件號碼-流水號
	 * <p/>
	 * 100/09/27調整<br/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseSeq(Integer value) {
		this.caseSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 */
	public Date getCaseDate() {
		return this.caseDate;
	}

	/**
	 * 設定簽案日期
	 * <p/>
	 * 資料來源：案件簽報書
	 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得案件審核層級
	 * <p/>
	 * 100/10/06新增<br/>
	 * 資料來源：案件簽報書<br/>
	 * 常董會權限、常董會權限簽奉總經理核批、常董會權限簽准由副總經理核批、利費率變更案件由總處經理核定、屬常董會授權總經理逕核案件、總經理權限內、
	 * 副總經理權限、企金部協理權限、其他、董事會權限、利費率變更案件由董事長核定、個金處經理權限、區域營運中心主任/副主任權限
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}

	/**
	 * 設定案件審核層級
	 * <p/>
	 * 100/10/06新增<br/>
	 * 資料來源：案件簽報書<br/>
	 * 常董會權限、常董會權限簽奉總經理核批、常董會權限簽准由副總經理核批、利費率變更案件由總處經理核定、屬常董會授權總經理逕核案件、總經理權限內、
	 * 副總經理權限、企金部協理權限、其他、董事會權限、利費率變更案件由董事長核定、個金處經理權限、區域營運中心主任/副主任權限
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/**
	 * 取得簽報書項下額度明細表是否全部動用
	 * <p/>
	 * Y/N<br/>
	 * 如為Y則固定顯示："簽報書項下額度明細表全部動用"；如為N則顯示所有額度序號。
	 */
	public String getAllCanPay() {
		return this.allCanPay;
	}

	/**
	 * 設定簽報書項下額度明細表是否全部動用
	 * <p/>
	 * Y/N<br/>
	 * 如為Y則固定顯示："簽報書項下額度明細表全部動用"；如為N則顯示所有額度序號。
	 **/
	public void setAllCanPay(String value) {
		this.allCanPay = value;
	}

	/**
	 * 取得授信契約書期別
	 * <p/>
	 * 1短期2中長期
	 */
	public String getTType() {
		return this.tType;
	}

	/**
	 * 設定授信契約書期別
	 * <p/>
	 * 1短期2中長期
	 **/
	public void setTType(String value) {
		this.tType = value;
	}

	/**
	 * 取得動用期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 */
	public String getUseSelect() {
		return this.useSelect;
	}

	/**
	 * 設定動用期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 **/
	public void setUseSelect(String value) {
		this.useSelect = value;
	}

	/**
	 * 取得動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getUseFromDate() {
		return this.useFromDate;
	}

	/**
	 * 設定動用期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setUseFromDate(Date value) {
		this.useFromDate = value;
	}

	/**
	 * 取得動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getUseEndDate() {
		return this.useEndDate;
	}

	/**
	 * 設定動用期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setUseEndDate(Date value) {
		this.useEndDate = value;
	}

	/**
	 * 取得動用期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 */
	public Integer getUseMonth() {
		return this.useMonth;
	}

	/**
	 * 設定動用期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 **/
	public void setUseMonth(Integer value) {
		this.useMonth = value;
	}

	/**
	 * 取得動用期間-其他
	 * <p/>
	 * 3.其他
	 */
	public String getUseOther() {
		return this.useOther;
	}

	/**
	 * 設定動用期間-其他
	 * <p/>
	 * 3.其他
	 **/
	public void setUseOther(String value) {
		this.useOther = value;
	}

	/**
	 * 取得授信期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 */
	public String getLnSelect() {
		return this.lnSelect;
	}

	/**
	 * 設定授信期間選項
	 * <p/>
	 * 100/10/06新增<br/>
	 * YYYY-MM-DD~ YYYY-MM-DD<br/>
	 * 自首動日起MM個月<br/>
	 * 其他
	 **/
	public void setLnSelect(String value) {
		this.lnSelect = value;
	}

	/**
	 * 取得授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getLnFromDate() {
		return this.lnFromDate;
	}

	/**
	 * 設定授信期間-起始日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setLnFromDate(Date value) {
		this.lnFromDate = value;
	}

	/**
	 * 取得授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 */
	public Date getLnEndDate() {
		return this.lnEndDate;
	}

	/**
	 * 設定授信期間-截止日期
	 * <p/>
	 * 1.YYYY-MM-DD~ YYYY-MM-DD
	 **/
	public void setLnEndDate(Date value) {
		this.lnEndDate = value;
	}

	/**
	 * 取得授信期間-年數
	 * <p/>
	 * 100/10/11新增<br/>
	 * 2.自首動日起YY年
	 */
	public Integer getLnYear() {
		return this.lnYear;
	}

	/**
	 * 設定授信期間-年數
	 * <p/>
	 * 100/10/11新增<br/>
	 * 2.自首動日起YY年
	 **/
	public void setLnYear(Integer value) {
		this.lnYear = value;
	}

	/**
	 * 取得授信期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 */
	public Integer getLnMonth() {
		return this.lnMonth;
	}

	/**
	 * 設定授信期間-月數
	 * <p/>
	 * 2.自首動日起MM個月
	 **/
	public void setLnMonth(Integer value) {
		this.lnMonth = value;
	}

	/**
	 * 取得授信期間-其他
	 * <p/>
	 * 3.其他
	 */
	public String getLnOther() {
		return this.lnOther;
	}

	/**
	 * 設定授信期間-其他
	 * <p/>
	 * 3.其他
	 **/
	public void setLnOther(String value) {
		this.lnOther = value;
	}

	/** 取得授信約定書簽約日期 **/
	public Date getSignDate() {
		return this.signDate;
	}

	/** 設定授信約定書簽約日期 **/
	public void setSignDate(Date value) {
		this.signDate = value;
	}

	/** 取得連帶保證書最高本金-幣別 **/
	public String getGuCurr() {
		return this.guCurr;
	}

	/** 設定連帶保證書最高本金-幣別 **/
	public void setGuCurr(String value) {
		this.guCurr = value;
	}

	/** 取得連帶保證書最高本金-金額 **/
	public BigDecimal getGuAmt() {
		return this.guAmt;
	}

	/** 設定連帶保證書最高本金-金額 **/
	public void setGuAmt(BigDecimal value) {
		this.guAmt = value;
	}

	/** 取得連帶保證書最高本金-起始日期 **/
	public Date getGuFromDate() {
		return this.guFromDate;
	}

	/** 設定連帶保證書最高本金-起始日期 **/
	public void setGuFromDate(Date value) {
		this.guFromDate = value;
	}

	/** 取得連帶保證書最高本金-截止日期 **/
	public Date getGuEndDate() {
		return this.guEndDate;
	}

	/** 設定連帶保證書最高本金-截止日期 **/
	public void setGuEndDate(Date value) {
		this.guEndDate = value;
	}

	/**
	 * 取得違約金計算條件-逾期
	 * <p/>
	 * ※案件若轉入催收系統時，可依此計算違約金。欄位值一定要輸入但不印於報表。()為預設值，可修改。<br/>
	 * 借戶如延遲還本或付息時，本金自到期日起，利息自繳息日起，逾期在(6)個月以內部份，按約定利率百分之(10)，逾期超過(6)個月部份，
	 * 按約定利率百分之(20)計付違約金。<br/>
	 * <br/>
	 * 逾期在XX個月以內部份
	 */
	public Integer getDMonth1() {
		return this.dMonth1;
	}

	/**
	 * 設定違約金計算條件-逾期
	 * <p/>
	 * ※案件若轉入催收系統時，可依此計算違約金。欄位值一定要輸入但不印於報表。()為預設值，可修改。<br/>
	 * 借戶如延遲還本或付息時，本金自到期日起，利息自繳息日起，逾期在(6)個月以內部份，按約定利率百分之(10)，逾期超過(6)個月部份，
	 * 按約定利率百分之(20)計付違約金。<br/>
	 * <br/>
	 * 逾期在XX個月以內部份
	 **/
	public void setDMonth1(Integer value) {
		this.dMonth1 = value;
	}

	/**
	 * 取得違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX
	 */
	public Integer getDRate1() {
		return this.dRate1;
	}

	/**
	 * 設定違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX
	 **/
	public void setDRate1(Integer value) {
		this.dRate1 = value;
	}

	/**
	 * 取得違約金計算條件-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份
	 */
	public Integer getDMonth2() {
		return this.dMonth2;
	}

	/**
	 * 設定違約金計算條件-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份
	 **/
	public void setDMonth2(Integer value) {
		this.dMonth2 = value;
	}

	/**
	 * 取得違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金
	 */
	public Integer getDRate2() {
		return this.dRate2;
	}

	/**
	 * 設定違約金計算條件-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金
	 **/
	public void setDRate2(Integer value) {
		this.dRate2 = value;
	}

	/**
	 * 取得計收延遲利息加碼
	 * <p/>
	 * 案件若有轉入催收系統時，可依此計算延遲利息，預設為1.00%，可修改。<br/>
	 * ※此欄不列印於報表
	 */
	public BigDecimal getDRateAdd() {
		return this.dRateAdd;
	}

	/**
	 * 設定計收延遲利息加碼
	 * <p/>
	 * 案件若有轉入催收系統時，可依此計算延遲利息，預設為1.00%，可修改。<br/>
	 * ※此欄不列印於報表
	 **/
	public void setDRateAdd(BigDecimal value) {
		this.dRateAdd = value;
	}

	/** 取得查核事項-資料查詢日期 **/
	public Date getBlackDataDate() {
		return this.blackDataDate;
	}

	/** 設定查核事項-資料查詢日期 **/
	public void setBlackDataDate(Date value) {
		this.blackDataDate = value;
	}

	/**
	 * 取得黑名單查詢結果
	 * <p/>
	 * ※gfnDB2calllnsp0130=02或04<br/>
	 * 64個全型字<br/>
	 * 101/04/16調整<br/>
	 * 192 ( 1024
	 */
	public String getBlackListTxtErr() {
		return this.blackListTxtErr;
	}

	/**
	 * 設定黑名單查詢結果
	 * <p/>
	 * ※gfnDB2calllnsp0130=02或04<br/>
	 * 64個全型字<br/>
	 * 101/04/16調整<br/>
	 * 192 ( 1024
	 **/
	public void setBlackListTxtErr(String value) {
		this.blackListTxtErr = value;
	}

	/**
	 * 取得黑名單查詢結果
	 * <p/>
	 * ※gfnDB2calllnsp0130=02或04以外<br/>
	 * 64個全型字<br/>
	 * 101/04/16調整<br/>
	 * 192 ( 1024
	 */
	public String getBlackListTxtOK() {
		return this.blackListTxtOK;
	}

	/**
	 * 設定黑名單查詢結果
	 * <p/>
	 * ※gfnDB2calllnsp0130=02或04以外<br/>
	 * 64個全型字<br/>
	 * 101/04/16調整<br/>
	 * 192 ( 1024
	 **/
	public void setBlackListTxtOK(String value) {
		this.blackListTxtOK = value;
	}

	/**
	 * 取得共同行銷
	 * <p/>
	 * 2012-11-15新增<br/>
	 * 300個全型字
	 */
	public String getJoinMarketing() {
		return this.joinMarketing;
	}

	/**
	 * 設定共同行銷
	 * <p/>
	 * 2012-11-15新增<br/>
	 * 300個全型字
	 **/
	public void setJoinMarketing(String value) {
		this.joinMarketing = value;
	}

	/**
	 * 取得共同行銷查詢日期
	 * <p/>
	 * 2012-11-15新增
	 */
	public Date getJoinMarketingDate() {
		return this.joinMarketingDate;
	}

	/**
	 * 設定共同行銷查詢日期
	 * <p/>
	 * 2012-11-15新增
	 **/
	public void setJoinMarketingDate(Date value) {
		this.joinMarketingDate = value;
	}

	/**
	 * 取得批示
	 * <p/>
	 * 128個全型字
	 */
	public String getSign() {
		return this.sign;
	}

	/**
	 * 設定批示
	 * <p/>
	 * 128個全型字
	 **/
	public void setSign(String value) {
		this.sign = value;
	}

	/**
	 * 取得審核意見
	 * <p/>
	 * 預設：貸放手續齊全，擬准予動用<br/>
	 * 128個全型字
	 */
	public String getComm() {
		return this.comm;
	}

	/**
	 * 設定審核意見
	 * <p/>
	 * 預設：貸放手續齊全，擬准予動用<br/>
	 * 128個全型字
	 **/
	public void setComm(String value) {
		this.comm = value;
	}

	/** 取得經辦 **/
	public String getApprId() {
		return this.apprId;
	}

	/** 設定經辦 **/
	public void setApprId(String value) {
		this.apprId = value;
	}

	/** 取得覆核主管 **/
	public String getReCheckId() {
		return this.reCheckId;
	}

	/** 設定覆核主管 **/
	public void setReCheckId(String value) {
		this.reCheckId = value;
	}

	/**
	 * 取得授信主管
	 * <p/>
	 * 呈主管覆核時，勾選授信主管名單
	 */
	public String getBossId() {
		return this.bossId;
	}

	/**
	 * 設定授信主管
	 * <p/>
	 * 呈主管覆核時，勾選授信主管名單
	 **/
	public void setBossId(String value) {
		this.bossId = value;
	}

	/**
	 * 取得經副襄理
	 * <p/>
	 * 呈主管覆核時，勾選經副襄理名單
	 */
	public String getManagerId() {
		return this.managerId;
	}

	/**
	 * 設定經副襄理
	 * <p/>
	 * 呈主管覆核時，勾選經副襄理名單
	 **/
	public void setManagerId(String value) {
		this.managerId = value;
	}

	/**
	 * 取得本案是否有同業聯貸案額度
	 * <p/>
	 * 引自簽報書/聯行額度明細表，引進不列印<br/>
	 * Y/N
	 */
	public String getUnitLoanCase() {
		return this.unitLoanCase;
	}

	/**
	 * 設定本案是否有同業聯貸案額度
	 * <p/>
	 * 引自簽報書/聯行額度明細表，引進不列印<br/>
	 * Y/N
	 **/
	public void setUnitLoanCase(String value) {
		this.unitLoanCase = value;
	}

	/**
	 * 取得本分行是否為管理行
	 * <p/>
	 * 引自簽報書/聯行額度明細表，引進不列印<br/>
	 * Y/N
	 */
	public String getUCMainBranch() {
		return this.uCMainBranch;
	}

	/**
	 * 設定本分行是否為管理行
	 * <p/>
	 * 引自簽報書/聯行額度明細表，引進不列印<br/>
	 * Y/N
	 **/
	public void setUCMainBranch(String value) {
		this.uCMainBranch = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/**
	 * DB無欄位用來顯示預訂補全日用
	 */
	@Transient
	private Date willFinishDate;

	/**
	 * DB無欄位用來顯示預訂補全日用
	 */
	public Date getWillFinishDate() {
		return willFinishDate;
	}

	/**
	 * DB無欄位用來顯示預訂補全日用
	 */
	public void setWillFinishDate(Date willFinishDate) {
		this.willFinishDate = willFinishDate;
	}

	/**
	 * 設定NEWVERSION
	 * <p/>
	 * 動審表版本
	 **/
	public void setNewVersion(String newVersion) {
		this.newVersion = newVersion;
	}

	/**
	 * 取得NEWVERSION
	 * <p/>
	 * 動審表版本
	 */
	public String getNewVersion() {
		return newVersion;
	}

	/**
	 * 設定NOEDIT1
	 * <p/>
	 * 免填授信約定書簽約日期
	 **/
	public void setNoEdit1(String noEdit1) {
		this.noEdit1 = noEdit1;
	}

	/**
	 * 取得NOEDIT1
	 * <p/>
	 * 免填授信約定書簽約日期
	 */
	public String getNoEdit1() {
		return noEdit1;
	}

	/**
	 * 設定NOEDIT2
	 * <p/>
	 * 免填連帶保證書最高本金
	 **/
	public void setNoEdit2(String noEdit2) {
		this.noEdit2 = noEdit2;
	}

	/**
	 * 取得NOEDIT2
	 * <p/>
	 * 免填連帶保證書最高本金
	 */
	public String getNoEdit2() {
		return noEdit2;
	}

	/**
	 * 設定NOEDIT3
	 * <p/>
	 * 免填違約金計算條件
	 **/
	public void setNoEdit3(String noEdit3) {
		this.noEdit3 = noEdit3;
	}

	/**
	 * 取得NOEDIT2
	 * <p/>
	 * 免填違約金計算條件
	 */
	public String getNoEdit3() {
		return noEdit3;
	}

	/**
	 * 設定NOEDIT4
	 * <p/>
	 * 免填計收遲延利息加碼
	 **/
	public void setNoEdit4(String noEdit4) {
		this.noEdit4 = noEdit4;
	}

	/**
	 * 取得NOEDIT2
	 * <p/>
	 * 免填計收遲延利息加碼
	 */
	public String getNoEdit4() {
		return noEdit4;
	}

	public void setIsSmallBuss(String isSmallBuss) {
		this.isSmallBuss = isSmallBuss;
	}

	public String getIsSmallBuss() {
		return isSmallBuss;
	}

	public void setSmallBussChkListDate(Date smallBussChkListDate) {
		this.smallBussChkListDate = smallBussChkListDate;
	}

	public Date getSmallBussChkListDate() {
		return smallBussChkListDate;
	}

	/**
	 * 取得微型企業註記
	 * 
	 * @param miniFlag
	 */
	public String getMiniFlag() {
		return miniFlag;
	}

	/**
	 * 設定微型企業註記
	 * 
	 * @param miniFlag
	 */
	public void setMiniFlag(String miniFlag) {
		this.miniFlag = miniFlag;
	}

	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}

	public String getCaseType() {
		return caseType;
	}
	
	/** 
	 * 取得系統實際核准日期<p/>
	 * J-110-0547-001 2022/03/04<br/>
	 *  讓批次寫貸後管理用
	 */
	public Timestamp getSysActApproveTime() {
		return this.sysActApproveTime;
	}
	/**
	 *  設定系統實際核准日期<p/>
	 *  J-110-0547-001 2022/03/04<br/>
	 *  讓批次寫貸後管理用
	 **/
	public void setSysActApproveTime(Timestamp value) {
		this.sysActApproveTime = value;
	}

}
