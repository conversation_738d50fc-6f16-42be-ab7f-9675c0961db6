
var pageAction = {
	handler : 'lms8200m01formhandler',
	ghandler : 'lms8200gridhandler',
	grid : null,
	build : function(obj){
		
		var gridview_colModel = [{
	        colHeader: i18n.lms8200v01["L820M01A.custId"], //借款戶統一編號
	        align: "left", width: 100, sortable: true, name: 'custId',
	        formatter: 'click', 
	        onclick: pageAction.openDoc
	    },{
			colHeader: i18n.lms8200v01['L820M01A.dupNo'],//"重複序號",
		    name: 'dupNo',
		    width: 30,
		    sortable: true
		},{
	        colHeader: i18n.lms8200v01["L820M01A.custName"], //借款戶名稱
	        align: "left", width: 100, sortable: true, name: 'custName'
	    }, {
            colHeader: i18n.lms8200v01['L820M01A.creator'],//"分行經辦",
            name: 'updater',
            width: 80,
            sortable: true,
            align: "center"
        }, {
        	colHeader: i18n.lms8200v01["L820M01A.createTime"], // 建立日期 / 查詢日期
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime',
            formatter: 'date',
            formatoptions: {
            	srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d H:i'
            }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }];
		
		pageAction.grid = $("#gridview").iGrid({
	        handler: pageAction.ghandler,
	        height: 350,
	        width: 785,
	        autowidth: false,
	        action: "queryL820m01A",
	        postData: {
	            docStatus: viewstatus
	        },
	        rowNum: 15,
	        sortname: "custId",
	        sortorder: "desc|desc",
	        multiselect: true,
	        colModel: gridview_colModel,
	        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	            var data = $("#gridview").getRowData(rowid);
	            console.log(data);
	            pageAction.openDoc(null, null, data);
	        }
	    });
		
		var GetCntrnoGrid = $('#GetCntrnoGrid').iGrid({
	        handler: pageAction.ghandler, //設定handler
	        height: 300, //設定高度
	        action: 'queryGetCntrno', //執行的Method
	        postData: {
	            
	        },
	        needPager: false,
	        rownumbers: true,
	        colModel: [{
	            colHeader: i18n.lms8200m01["cntrNo"], // 額度序號
	            align: "center",
	            width: 100, //設定寬度
	            sortable: true, //是否允許排序
	            name: 'cntrNo'
	        }, {
	        	name: 'sDate',
	        	hidden: true
	    	}],
	        loadComplete: function () {
	        	if( GetCntrnoGrid.getGridParam("records")>0){
	        		
	        	}else{
					var custId = GetCntrnoGrid.getGridParam("postData")['custId'];
					if(custId && custId.length>1){
						$.thickbox.close();
						API.showErrorMessage(i18n.lms8200m01["cntrNoError"]);	
					}            		
	        	}
	        }  
	    });
				
	    $("#buttonPanel").find("#btnDelete").click(function(){
	        var rows = $("#gridview").getGridParam('selarrrow');
	        var data = [];
	        
	        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
	            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	        }
	        //confirmDelete=是否確定刪除?
	        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
	            if (b) {
	                for (var i in rows) {
	                    data.push($("#gridview").getRowData(rows[i]).oid);
	                }
	                
	                $.ajax({
	                    handler: pageAction.handler,
	                    data: {
	                        formAction: "deleteL820m01a",
	                        oids: data
	                    },
	                    success: function(obj){
	                        $("#gridview").trigger("reloadGrid");
	                    }
	                });
	            }
	        });
	    }).end().find("#btnAdd").click(function(){
			chose_custId().done(function(resultFrom_chose_custId){
	   	 		chose_cntrNo(resultFrom_chose_custId).done(function(resultFrom_chose_cntrNo){
		   	 			$.ajax({
					    	handler: pageAction.handler,
					        action : 'newl820m01a',
							data : {
								cntrNo: resultFrom_chose_cntrNo.cntrNo,
								custId: resultFrom_chose_cntrNo.custId,
								dupNo: resultFrom_chose_cntrNo.dupNo,
								custName: resultFrom_chose_cntrNo.custName          
					   	 	},
					        success: function(obj){
					        	console.log(obj);
						    	$.form.submit({
					            	url: '../fms/lms8200m01/01',
					                data: {
					                	formAction: "queryL820m01a",
					                    oid: obj.oid,
					                    mainOid: obj.oid,
					                    mainDocStatus: viewstatus,
					                    txCode: txCode,
										cntrNo: resultFrom_chose_cntrNo.cntrNo,
										custId: resultFrom_chose_cntrNo.custId,
										dupNo: resultFrom_chose_cntrNo.dupNo,
										custName: resultFrom_chose_cntrNo.custName,
										mainId: obj.mainId
					                 },
					                 target: obj.oid
					            });
						    	pageAction.reloadGrid();
							}
					});
					$.thickbox.close();	
				})
				//$("#GetCntrnoGrid").trigger("reloadGrid");
				pageAction.reloadGrid();
				//pageAction.grid.trigger("reloadGrid");
				//pageAction.reloadGrid($.form.serializeData());
			});
	    }).end().find("#btnView").click(function(){
	        var id = $("#gridview").getGridParam('selrow');
	        if (!id) {
	            // action_004=請先選擇需「調閱」之資料列
	            return CommonAPI.showMessage(i18n.def["action_004"]);
	        }
	        if (id.length > 1) {
				// L140M01M.error1=此功能不能多選
	            CommonAPI.showMessage(i18n.lms8200m01["L820M01A.error1"]);
	        }
	        else {
	            var result = $("#gridview").getRowData(id);
	        	//console.log(result);
	        	//console.log(openDoc);
	            pageAction.openDoc(null, null, result);
	        }
	    });
		
		function chose_custId(){	
			var my_dfd = $.Deferred();
			AddCustAction.open({
		    		handler: pageAction.handler,
					action : 'echo_custId',
					data : {
		            },
					callback : function(json){					
		            	// 關掉 AddCustAction 的 
		            	$.thickbox.close();					
						my_dfd.resolve( json );					
					}
				});
			return my_dfd.promise();
		}
		
		function chose_cntrNo(resultFrom_chose_custId){
			var my_dfd = $.Deferred();		
			GetCntrnoGrid.jqGrid("setGridParam", {
	            postData: {
	                'custId': resultFrom_chose_custId.custId
					,'dupNo': resultFrom_chose_custId.dupNo
	            },
	            search: true
	        }).trigger("reloadGrid");
			
			$("#GetCntrnoThickBox").thickbox({
		       title: i18n.lms8200m01["checkSelect"]+i18n.lms8200m01["cntrNo"], 
			   width: 400,
			   height: 450,
			   align: "center",
			   valign: "bottom",
	           modal: false, 
			   i18n: i18n.def,
			   buttons: {
	                "sure": function(){
						 var data = GetCntrnoGrid.getSingleData();
	                     if (data) {
							 $.thickbox.close();
	                    	 var cntrNo = data.cntrNo;
							 var sDate = data.sDate;
	        				 my_dfd.resolve($.extend(resultFrom_chose_custId, {'cntrNo':cntrNo, 'sDate':sDate} ));
	                     }     	
	                },
	                "cancel": function(){
	                	$.thickbox.close();
	                }
	            }	
			});	
			
			return my_dfd.promise();
		}
		
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data){
		if (data){
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		}else{
			pageAction.grid.trigger('reloadGrid');
		}
	},
	/**
	 * 開啟明細
	 */
	openDoc : function(cellvalue, options, rowObject){
        $.form.submit({			
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryL820m01a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
}

$(function() {
	var obj;//預留
	pageAction.build(obj);
});
