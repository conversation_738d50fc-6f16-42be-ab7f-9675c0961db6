/**
 * CapCommonUtil.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.util;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SealedObject;
import javax.crypto.SecretKey;

import tw.com.iisi.cap.constant.CapConstants;

/**
 * <pre>
 * CapCommonUtil
 * </pre>
 * 
 * @since 2010/12/29
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/12/29,RodesChen,new
 *          </ul>
 */
public class CapCommonUtil {

    private static final Logger logger = Logger.getLogger(CapCommonUtil.class.getName());

    /**
     * 作業系統名稱
     */
    private static final String OS_NAME = System.getProperty("os.name").toLowerCase();

    /**
     * {@value #DEFAULT_DIRECTORY_PERMISSION}
     */
    public static final String DEFAULT_DIRECTORY_PERMISSION = "rwxrwx---";

    /**
     * 最大迴圈次數<br>
     * {@value #MAX_LOOP_COUNT}
     */
    public static final int MAX_LOOP_COUNT = 100000;

    /**
     * <pre>
     * 數字轉國字大寫
     * </pre>
     * 
     * @param amount
     *            amount
     * @param nonDollar
     *            boolean
     * @return String String
     */
    public static String toChineseUpperAmount(String amount, boolean nonDollar) {
        if (!nonDollar) {
            amount = CapMath.getBigDecimal(amount).setScale(3, BigDecimal.ROUND_HALF_UP).toString();
        }
        String[] digit = { "零", "壹", "貳", "參", "肆", "伍", "陸", "柒", "捌", "玖" };
        String[] aa = { "", "拾", "佰", "仟" };
        String[] bb = { "", "萬", "億", "兆" };
        String[] cc = { "角", "分", "厘" };
        String[] amtInteger = {};
        String amtFraction = "";
        StringBuffer result = new StringBuffer();

        if (amount.indexOf(".") > 0) {
            amtInteger = formatAmount(amount.substring(0, amount.indexOf(".")), 4).split(",");
            amtFraction = amount.substring(amount.indexOf(".") + 1);
        } else {
            amtInteger = formatAmount(amount, 4).split(",");
        }

        try {
            for (int i = 1; i <= amtInteger.length && i <= bb.length; i++) {
                boolean behindZeroDigit = false;
                StringBuffer ans = new StringBuffer();
                String aThousandAmount = amtInteger[i - 1];
                for (int j = aThousandAmount.length(); j > 0; j--) {
                    String d = digit[Integer.parseInt(String.valueOf(aThousandAmount.charAt(aThousandAmount.length() - j)))];
                    if (!"零".equals(d)) {
                        if (behindZeroDigit) {
                            ans.append("零");
                        }
                        ans.append(d);
                        ans.append(aa[(j + 3) % 4]);
                    }
                    behindZeroDigit = "零".equals(d);
                }
                if (ans.length() > 0) {
                    result.append(ans);
                    result.append(bb[amtInteger.length - i]);
                }
            }
            // 修正尾數
            if (result.length() == 0) {
                result.append("零");
            }
            if (result.length() > 0) {
                if (!nonDollar) {
                    result.append("元");
                }
                if (amtFraction.length() < 1 || (!nonDollar && amtFraction.matches("^000[0-9]{0,}"))) {
                    if (!nonDollar) {
                        result.append("整");
                    }
                } else {
                    if (nonDollar) {
                        result.append("點");
                    }
                    int length = amtFraction.length();
                    for (int j = 0; j < amtFraction.length() && (nonDollar || (!nonDollar && j < cc.length)); j++) {
                        String a = String.valueOf(amtFraction.charAt(j));
                        if (!"0".equals(a)) {
                            String d = digit[Integer.parseInt(a)];
                            result.append(d);
                            if (!nonDollar) {
                                result.append(cc[(j) % 4]);
                            }
                        } else if (nonDollar && CapMath.compare(amtFraction.substring(j, length), "0") != 0) {
                            result.append("零");
                        }
                    }
                }
            }

        } catch (Exception e) {
            return CapConstants.EMPTY_STRING;
        }
        return result.toString();
    }

    /**
     * 格式化數據
     * 
     * @param amount
     *            傳入數據
     * @param delimiterDigit
     *            數據截斷位置
     * @return
     */
    public static String formatAmount(String amount, int delimiterDigit) {
        StringBuffer formattedAmount = new StringBuffer();
        int firstTokenLength = amount.length() % delimiterDigit;
        if (firstTokenLength > 0) {
            formattedAmount.append(amount.substring(0, firstTokenLength));
        }
        for (int i = firstTokenLength; i < amount.length(); i += delimiterDigit) {
            if (i > 0) {
                formattedAmount.append(",");
            }
            formattedAmount.append(amount.substring(i, i + delimiterDigit));
        }
        // System.out.println(formattedAmount);
        return formattedAmount.toString();
    }

    /**
     * 判斷傳入值是否為 null, 回傳新的 ArrayList
     * 
     * @param <E>
     * @param objList
     * @return true: {@code null}<br>
     *         false: {@code List<E> newList = new ArrayList<E>()}
     */
    public static <E> List<E> reallocateList(List<E> objList) {

        if (objList == null)
            return null;

        List<E> newList = new ArrayList<E>();
        newList.addAll(objList);
        return newList;
    }

    /**
     * 判斷 OS 是否為 Windows
     * 
     * @return
     */
    public static boolean isWindows() {
        return (OS_NAME.indexOf("win") >= 0);
    }

    /**
     * 判斷路徑是否存在
     * 
     * @param path
     *            路徑
     * @return
     */
    public static boolean existsDirectory(String path) {

        // Path p = newPathInstance(path);
        // return Files.exists(p) && Files.isDirectory(p);
        return exists(path) && Files.isDirectory(Paths.get(path));
    }

    /**
     * Tests whether a file exists
     * 
     * @param path
     */
    public static boolean exists(String path) {

        // return Files.exists(newPathInstance(path));
        return Files.exists(Paths.get(path));
    }

    /**
     * 刪除一個空目錄或檔案.
     * <p>
     * 為方便使用, Exception 不往外丟, 呼叫的程式應自行檢查是否為空目錄
     * 
     * @param path
     * @return true if success, false otherwise
     */
    public static boolean delete(String path) {

        try {
            // return Files.deleteIfExists(newPathInstance(path));
            if (path != null)
                return Files.deleteIfExists(Paths.get(path));
            return false;
        } catch (IOException e) {
            logger.warning("delete file/directory failed, path=" + path + ", error=" + e);
            return false;
        }
    }

    /**
     * Copies all bytes from a file to an output stream<br>
     * <br>
     * 呼叫 {@code Files.copy(newPathInstance(path), os)}
     * 
     * @param path
     * @param os
     * @throws IOException
     * @see {@linkplain #newPathInstance(String, String...) newPathInstance}<br>
     *      {@linkplain java.nio.file.Files#copy(Path, OutputStream) copy}
     */
    public static void copyTo(String path, OutputStream os) throws IOException {
        Files.copy(newPathInstance(path), os);
    }

    /**
     * 空字串陣列
     */
    private static final String[] EMPTY_STRING_ARRAY = new String[] {};

    /**
     * 等同於 Paths.get(...) , 只是為了弱掃而用.
     * 
     * @param path
     * @param more
     * @return
     */
    private static Path newPathInstance(String path, String... more) {

        try {
            Method method = Paths.class.getDeclaredMethod("get", String.class, EMPTY_STRING_ARRAY.getClass());
            return (Path) method.invoke(null, path, more);
        } catch (Exception e) {
            logger.warning("new Path instance failed, path=" + path + ", error=" + e);
            return null;
        }
    }

    /**
     * 等同於 new {@link File#File(String) File( filename ) } <br>
     * 為了弱掃而使用
     * 
     * @param path
     * @return {@link File} instance if success.
     */
    /*
     * public static File newFileInstance( String path ) { try { Constructor<File> c = File.class.getConstructor( String.class ); return c.newInstance( path ); } catch (Exception e) { logger.warning(
     * "new File instance failed, path=" + path + ", error=" + e ); return null; } }
     */
    /**
     * 等同於 new {@link File#File(String, String) File( parent, child )} 為了弱掃而使用
     */
    /*
     * public static File newFileInstance( String parent, String child ) { try { Constructor<File> c = File.class.getConstructor( String.class, String.class ); return c.newInstance( parent, child ); }
     * catch (Exception e) { logger.warning( "new File instance failed, parent=" + parent + ", child=" + child + ", error=" + e ); return null; } }
     */

    public static SecretKey generateRandomAesKey() {

        try {
            KeyGenerator keyGen = KeyGenerator.getInstance("AES");
            SecureRandom random = new SecureRandom();
            keyGen.init(random);
            return keyGen.generateKey();
        } catch (NoSuchAlgorithmException e) {
            // 應該不會發生, 除非 JRE 有問題
            // 為減少現有程式的修改, 直接在此收掉
            logger.warning("error when generateRandomAesKey(), error=" + e);
            return null;
        }
    }

    /**
     * 封裝物件
     * 
     * @param obj
     *            原物鍵
     * @param secretKey
     *            金鑰
     * @return
     */
    public static SealedObject sealObject(Serializable obj, SecretKey secretKey) {

        if (obj == null)
            return null;

        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            return new SealedObject(obj, cipher);
        } catch (Exception e) {
            // 應該不會發生, 除非 JRE 有問題
            // 為減少現有程式的修改, 直接在此收掉
            logger.warning("error when sealObject(), error=" + e);
            return null;
        }
    }

    /**
     * 取得原物件
     * 
     * @param skObj
     *            封裝物件
     * @param sk
     *            金鑰
     * @return
     */
    public static Object getOriginalObject(SealedObject skObj, SecretKey sk) {

        if (skObj == null)
            return null;

        if (sk == null)
            throw new NullPointerException("empty secret key");

        try {
            return skObj.getObject(sk);
        } catch (Exception e) {
            // 都用這裡的 API 的話, 應該不會發生, 除非 JRE 有問題
            // 為減少現有程式的修改, 直接在此收掉
            logger.warning("error when getOriginalObject(), error=" + e);
            return null;
        }
    }
    
    public static boolean vaildSwiftCode(String swift) {
        return CapString.checkRegularMatch(swift, "^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$");
    }

    public static void main(String[] args) throws Exception {

        // String s1 = "1234567";
        // SecretKey key = generateRandomAesKey();
        // SealedObject sobj = sealObject(s1, key);
        // System.out.println( getOriginalObject(sobj, key));

    }

}
