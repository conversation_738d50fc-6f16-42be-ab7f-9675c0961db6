/* 
 * L140S02K.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 貸款額度評等表 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S02K", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq" }))
public class L140S02K extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** 借戶從事本業或訓練經驗 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SCORE01", columnDefinition = "DECIMAL(3,0)")
	private Integer score01;

	/** 營業場所自、租用 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SCORE02", columnDefinition = "DECIMAL(3,0)")
	private Integer score02;

	/** 抵押貸款或信用貸款的保證成數 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SCORE03", columnDefinition = "DECIMAL(3,0)")
	private Integer score03;

	/** 借戶或連保人資力 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SCORE04", columnDefinition = "DECIMAL(3,0)")
	private Integer score04;

	/** 授信往來 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SCORE05", columnDefinition = "DECIMAL(3,0)")
	private Integer score05;

	/** 借戶(含事業體)無擔保授信總餘額 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SCORE06", columnDefinition = "DECIMAL(3,0)")
	private Integer score06;

	/** 借戶(含事業體)聯徵被查詢次數 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SCORE07", columnDefinition = "DECIMAL(3,0)")
	private Integer score07;

	/** 本案最高可額貸額度 **/
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal loanAmt;

	/** 分行初審綜合意見 **/
	@Size(max = 384)
	@Column(name = "DSCR", length = 384, columnDefinition = "VARCHAR(384)")
	private String dscr;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得借戶從事本業或訓練經驗 **/
	public Integer getScore01() {
		return this.score01;
	}

	/** 設定借戶從事本業或訓練經驗 **/
	public void setScore01(Integer value) {
		this.score01 = value;
	}

	/** 取得營業場所自、租用 **/
	public Integer getScore02() {
		return this.score02;
	}

	/** 設定營業場所自、租用 **/
	public void setScore02(Integer value) {
		this.score02 = value;
	}

	/** 取得抵押貸款或信用貸款的保證成數 **/
	public Integer getScore03() {
		return this.score03;
	}

	/** 設定抵押貸款或信用貸款的保證成數 **/
	public void setScore03(Integer value) {
		this.score03 = value;
	}

	/** 取得借戶或連保人資力 **/
	public Integer getScore04() {
		return this.score04;
	}

	/** 設定借戶或連保人資力 **/
	public void setScore04(Integer value) {
		this.score04 = value;
	}

	/** 取得授信往來 **/
	public Integer getScore05() {
		return this.score05;
	}

	/** 設定授信往來 **/
	public void setScore05(Integer value) {
		this.score05 = value;
	}

	/** 取得借戶(含事業體)無擔保授信總餘額 **/
	public Integer getScore06() {
		return this.score06;
	}

	/** 設定借戶(含事業體)無擔保授信總餘額 **/
	public void setScore06(Integer value) {
		this.score06 = value;
	}

	/** 取得借戶(含事業體)聯徵被查詢次數 **/
	public Integer getScore07() {
		return this.score07;
	}

	/** 設定借戶(含事業體)聯徵被查詢次數 **/
	public void setScore07(Integer value) {
		this.score07 = value;
	}

	/** 取得本案最高可額貸額度 **/
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}

	/** 設定本案最高可額貸額度 **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/** 取得分行初審綜合意見 **/
	public String getDscr() {
		return this.dscr;
	}

	/** 設定分行初審綜合意見 **/
	public void setDscr(String value) {
		this.dscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
