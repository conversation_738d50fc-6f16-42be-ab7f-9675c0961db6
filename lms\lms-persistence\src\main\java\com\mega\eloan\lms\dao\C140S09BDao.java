/* 
 * C140S09BDao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140S09B;

/**
 * <pre>
 * 徵信調查報告書第九章 集團退票(子)
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version <ul>
 *          <li>new
 *          </ul>
 */
public interface C140S09BDao extends IGenericDao<C140S09B> {
	
	/**
	 * 刪除所有資料
	 * 
	 * @param meta C140M01A
	 * @return int
	 */
	int deleteByMeta(C140M01A meta);
	
	List<C140S09B> findByMainId(String mainId);
}
