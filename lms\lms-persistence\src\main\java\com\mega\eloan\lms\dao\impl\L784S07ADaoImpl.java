/* 
 * L784S07ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L784S07ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L784S07A;

/** 常董會及申報案件明細檔 **/
@Repository
public class L784S07ADaoImpl extends LMSJpaDao<L784S07A, String> implements
		L784S07ADao {

	@Override
	public L784S07A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L784S07A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L784S07A> list = createQuery(L784S07A.class, search).getResultList();
		return list;
	}

	@Override
	public List<L784S07A> findByMainIdBrNo(String mainId,String brNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		List<L784S07A> list = createQuery(L784S07A.class, search).getResultList();
		return list;
	}

	@Override
	public L784S07A findByUniqueKey(String mainId, String brNo, String apprYY,
			String apprMM, String caseDept) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "apprYY", apprYY);
		search.addSearchModeParameters(SearchMode.EQUALS, "apprMM", apprMM);
		search.addSearchModeParameters(SearchMode.EQUALS, "caseDept", caseDept);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L784S07A> findByIndex01(String mainId, String brNo,
			String apprYY, String apprMM, String caseDept) {
		ISearch search = createSearchTemplete();
		List<L784S07A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (brNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		if (apprYY != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "apprYY", apprYY);
		if (apprMM != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "apprMM", apprMM);
		if (caseDept != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "caseDept",
					caseDept);
		search.setMaxResults(Integer.MAX_VALUE);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L784S07A.class, search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L784S07A> findByMainIdApprYM(String mainId,
			String apprYY, String apprMM, String caseDept) {
		ISearch search = createSearchTemplete();
		List<L784S07A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (apprYY != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "apprYY", apprYY);
		if (apprMM != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "apprMM", apprMM);
		if (caseDept != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "caseDept",
					caseDept);
		search.addOrderBy("brNo");
		search.setMaxResults(500);
		
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L784S07A.class, search).getResultList();
		}
		return list;
	}
	
}