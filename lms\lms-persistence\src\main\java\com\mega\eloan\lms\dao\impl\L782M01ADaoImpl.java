package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L782M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L782M01A;

/** 額度特殊案件登記表 **/
@Repository
public class L782M01ADaoImpl extends LMSJpaDao<L782M01A, String> implements
		L782M01ADao {

	@Override
	public L782M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L782M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L782M01A> list = createQuery(L782M01A.class, search).getResultList();
		return list;
	}

	@Override
	public L782M01A findByUniqueKey(String mainId, String loanTP) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "loanTP", loanTP);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L782M01A> findByIndex01(String mainId, String subject) {
		ISearch search = createSearchTemplete();
		List<L782M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (subject != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subject",
					subject);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L782M01A.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L782M01A> findByAll(String ownBrId,String releaseDateS,String releaseDateE) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "disp1", "");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "disp1", "");
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		if(Util.notEquals(releaseDateS, "") && Util.notEquals(releaseDateE, "")){
			Object[] reason = { releaseDateS, releaseDateE };
			search.addSearchModeParameters(SearchMode.BETWEEN, "dispatchDate", reason);
		}
		List<L782M01A> list = createQuery(L782M01A.class, search).getResultList();
		return list;
	}
}