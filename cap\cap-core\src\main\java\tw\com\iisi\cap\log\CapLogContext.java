/*
 * CapLogContext.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */

package tw.com.iisi.cap.log;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.logging.log4j.ThreadContext;
//import org.springframework.web.util.HtmlUtils;

import tw.com.iisi.cap.util.CapString;

/**
 * <p>
 * set log4j MDC (Mapped Diagnostic Context) for log user information..
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/6,iristu,new
 *          </ul>
 */
@SuppressWarnings({ "unchecked", "rawtypes" })
public class CapLogContext extends InheritableThreadLocal {

    private static final Log logger = LogFactory.getLog(CapLogContext.class);

    private static ThreadLocal<Map> logContext = new InheritableThreadLocal<Map>();

    /**
     * 是否使用 MDC (Mapped Diagnostic Context)
     */
    private static boolean useMDC = false;

    /**
     * {@value #LOGIN}
     */
    public static final String LOGIN = "login";

    /**
     * {@value #UNITNO}
     */
    public static final String UNITNO = "unitno";

    /**
     * {@value #SESSION_ID}
     */
    public static final String SESSION_ID = "sessionId";

    static {
        try {
            Class.forName("org.apache.logging.log4j.ThreadContext");
            useMDC = true;
        } catch (Throwable t) {
            if (logger.isDebugEnabled()) {
                logger.debug("org.apache.logging.log4j.ThreadContext was not found on the classpath, continue without");
            }
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see java.lang.InheritableThreadLocal#childValue(java.lang.Object)
     */
    @Override
    protected Object childValue(Object parentValue) {
        return new LinkedHashMap((Map) parentValue);
    }

    /**
     * Get a map containing all the objects held by the current thread.
     */
    private static Map getContext() {
        if (useMDC) {
            return ThreadContext.getContext();
        } else {
            Map m = logContext.get();
            if (m == null) {
                m = new LinkedHashMap();
                logContext.set(m);
            }
            return m;
        }
    }

    /**
     * Get the context identified by the key parameter.
     * 
     * @param key
     *            the key
     * @return Object
     */
    public static Object get(String key) {
        if (useMDC) {
            return ThreadContext.get(key);
        } else {
            return getContext().get(key);
        }
    }

    /**
     * Put a context value (the o parameter) as identified with the key parameter into the current thread's context map.
     * 
     * @param key
     *            the Key
     * @param o
     *            Object
     */
    public static void put(String key, Object o) {
        if (useMDC) {
            ThreadContext.put(key, CapString.trimNull(o));
        } else {
            getContext().put(key, o);
        }
    }

    /**
     * Remove the the context identified by the key parameter.
     * 
     * @param key
     *            the Key
     */
    public static void remove(String key) {
        if (useMDC) {
            ThreadContext.remove(key);
        } else {
            getContext().remove(key);
        }
    }

    /**
     * Remove all the object put in this thread context.
     */
    public static void resetLogContext() {
        if (useMDC)
            ThreadContext.clearAll();
        else
            getContext().clear();
    }

    /**
     * Only used if jdk logging is used.
     * 
     * @return String
     */
    public static String toLogPrefixString() {
        Map m = getContext();
        Iterator i = m.entrySet().iterator();

        StringBuilder sb = new StringBuilder("[");
        while (i.hasNext()) {
            Map.Entry e = (Map.Entry) i.next();
            sb.append((String) e.getKey()).append("=").append(e.getValue().toString());
            if (i.hasNext()) {
                sb.append("&");
            }
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * set the given login in the map
     * 
     * @param login
     *            the user Id
     */
    public static void setLogin(String login) {
        put(LOGIN, login);
    }

    /**
     * set the given unitNo in the map
     * 
     * @param unitNo
     *            the user unitNo
     */
    public static void setUnitNo(String unitNo) {
        put(UNITNO, unitNo);
    }

    /**
     * set the given web session in the map
     * 
     * @param sessionId
     *            the session id
     */
    public static void setSessionId(String sessionId) {
        put(SESSION_ID, sessionId);
    }

    /**
     * Set Requset Url
     * 
     * @param url
     */
    public static void setRequestURL(String url) {
        put("reqURI", url);
    }

}
