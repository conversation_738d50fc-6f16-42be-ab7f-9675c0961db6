/* 
 * CLS1141S11Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 額度批覆表
 * </pre>
 * 
 * @since 2011/11/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/4,<PERSON>,new
 *          </ul>
 */
public class CLS1141S11Panel extends Panel {

	public CLS1141S11Panel(String id) {
		super(id);
		// add(new LMS7405S01Panel("_lms7405s01panel"));

		// J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計
		// J-111-0343_05097_B1004 Web e-Loan修改企金額度明細表合計之功能
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		//
		// Map<String, String> lgdMap = lmsService.getLgdTotAmtParam(null,
		// user.getUnitNo(), null);
		//
		// // T=授信授權額度合計
		// String label_lgdTotAmt_T = MapUtils.getString(lgdMap, "label_T");
		// // add(new Label("label_lgdTotAmt_T", label_lgdTotAmt_T));
		//
		// int lmsLgdCount = Util.parseInt(MapUtils.getString(lgdMap,
		// "lmsLgdCount", "0"));
		// int lmsLgdCountTotal = Util.parseInt(MapUtils.getString(lgdMap,
		// "lmsLgdCountTotal", "0"));
		//
		// for (int i = 1; i <= lmsLgdCountTotal; i++) {
		// String label_lgdTotAmt = MapUtils.getString(lgdMap, "label_" + i,
		// "");
		// add(new Label("label_lgdTotAmt_" + i, label_lgdTotAmt));
		// String label_lgdTotAmt_U_1 = MapUtils.getString(lgdMap, "label_1_"
		// + i, "");
		// add(new Label("label_lgdTotAmt_" + i + "_1", label_lgdTotAmt_U_1));
		// }
	}

	public CLS1141S11Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
