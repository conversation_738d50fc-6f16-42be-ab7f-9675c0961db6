/* 
 * L230S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L230S01A;


/** 簽約未動用額度資訊檔 **/
public interface L230S01ADao extends IGenericDao<L230S01A> {

	L230S01A findByOid(String oid);

	List<L230S01A> findByMainId(String mainId);

	List<L230S01A> findByIndex01(String mainId, String srcMainId);

	List<L230S01A> findByCntrNo(String CntrNo);
	
	List<L230S01A> findByCustIdDupId(String custId,String DupNo);
	/**
	 * 查詢簽約未動用額度資訊檔
	 * 
	 * @param srcMainId
	 *            額度明細表文件狀態
	 * @param docstatus
	 *            文件狀態
	 * @return 額度資訊檔
	 */
	L230S01A findBySrcMainIdAndDocstatus(String srcMainId, String docstatus);

	/**
	 * 查詢最新簽約未動用額度資訊檔
	 * 
	 * @param srcMainId
	 *            額度明細表文件狀態
	 * @param docstatus
	 *            文件狀態
	 * @return 額度資訊檔
	 */
	L230S01A findBySrcMainIdAndDocstatusAndMaxdataDate(String srcMainId,
			String docstatus);
}