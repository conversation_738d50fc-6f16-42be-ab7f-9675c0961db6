/* 
 * C250M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C250M01A;

/** 可疑代辦案件註記作業主檔 **/
public interface C250M01ADao extends IGenericDao<C250M01A> {

	C250M01A findByOid(String oid);
	
	List<C250M01A> findByMainId(String mainId);

	List<C250M01A> findByIndex01(String mainId);

	List<C250M01A> findByIndex02(String CUSTID, String DUPNO);

	List<C250M01A> findByCntrNo_custId_dupNo_yyyymm_status(String cntrNo,
			String custId, String dupNo, String yyyymm, String status);
}