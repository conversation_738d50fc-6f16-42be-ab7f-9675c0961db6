package com.mega.eloan.lms.etch.service;

import java.util.List;
import java.util.Map;

public interface MisSuccKeyMapService
{

	/**
	 * 取得客戶票信查詢紀錄
	 * 
	 * @param custId
	 *            統編
	 * @return Map<String, Object>
	 */
	public Map<String, Object> find4111ById(String custId);

	/**
	 * 取得查詢票信資料成功紀錄
	 * @param custId 統編
	 * @return Map<String, Object>
	 */
	public Map<String, Object> find4112ById(String custId);

    @SuppressWarnings("rawtypes")
	public abstract List findLastQDateById(String s);

    public static final String SuccKeyMapCols[] = {
        "CUSTID", "DUPNO", "ADDRNUM", "REFNUM", "TXDT", "ADDRKIND", "RJTCD", "ADDRZIP", "CITYR", "TOWNR", 
        "LEER", "ADDRR"
    };

}