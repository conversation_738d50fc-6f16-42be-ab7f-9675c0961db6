/* 
 * L140S08ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S08A;

/** 代碼轉換檔 **/
public interface L140S08ADao extends IGenericDao<L140S08A> {

	L140S08A findByOid(String oid);
	
	List<L140S08A> findByMainId(String mainId);

	List<L140S08A> findByIndex01(String itemName, String mainItem);

	List<L140S08A> findByIndex02(String itemName, String subItem);
}