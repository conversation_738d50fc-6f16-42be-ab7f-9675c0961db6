var _handler = "";
initDfd.done(function(){
    //2012_07_20_rex add 取得sso 連線資訊 
    BrowserAction.init();
    setCloseConfirm(true);
    if (responseJSON.docURL == "/lms/lms1201m01") {
        // 授權外企金
        _handler = "lms1201formhandler";
        //_iHandler = "lms1201formhandler";
    }
    else 
        if (responseJSON.docURL == "/lms/lms1101m01") {
            //授權內企金
            _handler = "lms1101formhandler";
        //_iHandler = "lms1101formhandler";
        }
        else 
            if (responseJSON.docURL == "/lms/lms1211m01") {
                _handler = "lms1211formhandler";
            //_iHandler = "lms1211formhandler";
            }
            else 
                if (responseJSON.docURL == "/lms/lms1111m01") {
                    _handler = "lms1111formhandler";
                //_iHandler = "lms1111formhandler";
                }
                else {
                    _handler = "lms1301formhandler";
                //_iHandler = "lms1301formhandler";
                }
    // 第一次點擊按鈕時載入個金簽報書Grid
    $("#formL120m01e").find("#bDocDscr2").one("click", function(){
        perGrid();
        uPerGrid();
    });
    
    //J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
    $("#docDscr6 a").live( "click", function() {
        getCls(DOMPurify.sanitize(this.title));
    });
   
});

/*
 function getMain(ces){
 $.ajax({ // 查詢主要借款人資料
 handler : _handler,
 type : "POST",
 dataType : "json",
 data : {
 formAction : "getRelate1",
 mainId : responseJSON.mainId,
 cesMainId : ces
 },
 success : function(json) {
 // 徵信報告
 BrowserAction.submit({
 system : "ces",
 //url    : "app/ces/ces1405m01/02",
 url    : json.url,
 mainId : json.mainId,
 mainOid : json.mainOid,
 txCode : json.txCode,
 data   : { //其它參數
 fromView: true,
 uid : json.uid,
 mainDocStatus : json.mainDocStatus,
 oid : json.mainOid,
 mainOid : json.mainOid
 }
 });
 }
 });
 }
 function getCes(ces){
 $.ajax({ // 查詢主要借款人資料
 handler : _handler,
 type : "POST",
 dataType : "json",
 data : {
 formAction : "getRelate2",
 mainId : responseJSON.mainId,
 cesMainId : ces
 },
 success : function(json) {
 // 資信簡表
 BrowserAction.submit({
 system : "ces",
 url    : json.url,
 mainId : json.mainId,
 mainOid : json.mainOid,
 txCode : json.txCode,
 data   : { //其它參數
 uid : json.uid,
 mainDocStatus : json.mainDocStatus,
 oid : json.mainOid,
 mainOid : json.mainOid
 }
 });
 }
 });
 }
 */
function seachKind3(){
    if ($("#lmss08a_panel").attr("open") == "true") {
        $("#lmss08a_panel").load("../../lms/baselmss08a", function(){
            $("#thickboxPeo").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.lmss08a["L120S08.thickbox11"],
                width: 960,
                height: 480,
                modal: true,
                i18n: i18n.def,
                buttons: {
                    "print": function(){
                        printA41();
                    },
                    "close": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
            // 控制分頁頁籤內容唯讀(不包括下拉式選單)			
            if (responseJSON.readOnly == "true") {
                $("#tabForm_1").readOnlyChilds(true);
                $("#s41Form").find("button").hide();
                $("#tabForm_1").find("button").hide();
            }
        });
        $("#lmss08a_panel").attr("open", false);
    }
    else {
        $("#thickboxPeo").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lmss08a["L120S08.thickbox11"],
            width: 960,
            height: 480,
            modal: true,
            i18n: i18n.def,
            buttons: {
                "print": function(){
                    printA41();
                },
                "close": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }
}

function seachKind4(){
    if ($("#lmss08b_panel").attr("open") == "true") {
        $("#lmss08b_panel").load("../../lms/baselmss08b", function(){
            $.ajax({
                handler: _handler,
                type: "POST",
                dataType: "json",
                data: {
                    formAction: "queryGrp",
                    page: "91",
                    mainId: responseJSON.mainId
                },
                success: function(json){
                    var $tabForm08 = $("#tabForm08");
                    $tabForm08.reset();
                    $tabForm08.setData(json, false);
                    if ($tabForm08.find("input[name=isGroupCompany1]:radio:checked").val() == "1") {
                        $tabForm08.find("#isGroupCompany1-1").show().siblings("[id^=isGroupCompany1]").hide();
                    }
                    $tabForm08.find("#show_curr5").val("TWD");
                    $tabForm08.find("#show_curr5").val("1000");
                    $tabForm08.find("#curr5").val("TWD");
                    $tabForm08.find("#unit").val("1000");
                    $tabForm08.find("#curr6").val("TWD");
                    $tabForm08.find("#unit1").val("1000");
                    $tabForm08.find("#curr7").val("TWD");
                    $tabForm08.find("#unit2").val("1000");
                    
                    $tabForm08.find("#grpFinYear").val(json.grpFinYear);
                    $tabForm08.find("#ch9_GFin_SrcDate").val(json.ch9_GFin_SrcDate);
                    $tabForm08.find("input[type='radio'][name='ch9FinInfor1'][value='" + json.ch9FinInfor1 + "']").attr("checked", true);
                    
                    $("#thickboxGrp").thickbox({ // 使用選取的內容進行彈窗
                        title: i18n.lmss08a["L120S08.thickbox10"],
                        width: 960,
                        height: 480,
                        modal: true,
                        i18n: i18n.def,
                        buttons: {
                            "saveData": function(){
                                //								$.thickbox.close();
                                if ($("#tabForm08").valid()) {
                                    $.ajax({
                                        handler: _handler,
                                        type: "POST",
                                        dataType: "json",
                                        data: {
                                            formAction: "save",
                                            page: "91",
                                            mainId: responseJSON.mainId,
                                            toM4: $("#tabForm08").find("[name='toM4']:radio:checked").val(),
                                            typem4: $("#tabForm08").find("#typem4").val(),
                                            GroupCompanyID1: $("#tabForm08").find("#GroupCompanyID1").val(),
                                            GroupCompanyName1: $("#tabForm08").find("#GroupCompanyName1").val(),
                                            curr5: $("#tabForm08").find("#curr5").val(),
                                            unit: $("#tabForm08").find("#unit").val(),
                                            grt_IsNgRec: $("#tabForm08").find("#grt_IsNgRec").val(),
                                            grt_data_src: $("#tabForm08").find("#grt_data_src").val(),
                                            grt_data_date: $("#tabForm08").find("#grt_data_date").val(),
                                            curr6: $("#tabForm08").find("#curr6").val(),
                                            unit1: $("#tabForm08").find("#unit1").val(),
                                            grp_credit_note: $("#tabForm08").find("#grp_credit_note").val(),
                                            curr7: $("#tabForm08").find("#curr7").val(),
                                            unit2: $("#tabForm08").find("#unit2").val(),
                                            grp_ov_note: $("#tabForm08").find("#grp_ov_note").val(),
                                            gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
                                            gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
                                            ch9FinInfor1: $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val(),
                                            grpFinYear: $("#tabForm08").find("#grpFinYear").val(),
                                            ch9_GFin_SrcDate: $("#tabForm08").find("#ch9_GFin_SrcDate").val(),
                                            setGrp: true
                                        },
                                        success: function(json){
                                            $("#formL120m01e").setData(json.formL120m01e, false);
                                        }
                                    });
                                }
                            },
                            "print": function(){
                                //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
                                CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                                    if (b) {
                                        // 儲存後列印
                                        if ($("#tabForm08").valid()) {
                                            $.ajax({
                                                handler: _handler,
                                                type: "POST",
                                                dataType: "json",
                                                data: {
                                                    formAction: "save",
                                                    page: "91",
                                                    mainId: responseJSON.mainId,
                                                    toM4: $("#tabForm08").find("[name='toM4']:radio:checked").val(),
                                                    typem4: $("#tabForm08").find("#typem4").val(),
                                                    GroupCompanyID1: $("#tabForm08").find("#GroupCompanyID1").val(),
                                                    GroupCompanyName1: $("#tabForm08").find("#GroupCompanyName1").val(),
                                                    curr5: $("#tabForm08").find("#curr5").val(),
                                                    unit: $("#tabForm08").find("#unit").val(),
                                                    grt_IsNgRec: $("#tabForm08").find("#grt_IsNgRec").val(),
                                                    grt_data_src: $("#tabForm08").find("#grt_data_src").val(),
                                                    grt_data_date: $("#tabForm08").find("#grt_data_date").val(),
                                                    curr6: $("#tabForm08").find("#curr6").val(),
                                                    unit1: $("#tabForm08").find("#unit1").val(),
                                                    grp_credit_note: $("#tabForm08").find("#grp_credit_note").val(),
                                                    curr7: $("#tabForm08").find("#curr7").val(),
                                                    unit2: $("#tabForm08").find("#unit2").val(),
                                                    grp_ov_note: $("#tabForm08").find("#grp_ov_note").val(),
                                                    gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
                                                    gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
                                                    ch9FinInfor1: $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val(),
                                                    grpFinYear: $("#tabForm08").find("#grpFinYear").val(),
                                                    ch9_GFin_SrcDate: $("#tabForm08").find("#ch9_GFin_SrcDate").val(),
                                                    setGrp: true
                                                },
                                                success: function(json){
                                                    $("#formL120m01e").setData(json.formL120m01e, false);
                                                    printA91();
                                                    printA92();
                                                    printAD4();
                                                }
                                            });
                                        }
                                    }
                                });
                                
                            },
                            "close": function(){
                                API.confirmMessage(i18n.def['flow.exit'], function(res){
                                    if (res) {
                                        $.thickbox.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
            // 控制分頁頁籤內容唯讀(不包括下拉式選單)
            if (responseJSON.readOnly == "true") {
                $("#tabForm08").readOnlyChilds(true);
                $("#tabForm08").find("button").hide();
            }
        });
        $("#lmss08b_panel").attr("open", false);
    }
    else {
        $.ajax({
            handler: _handler,
            type: "POST",
            dataType: "json",
            data: {
                formAction: "queryGrp",
                page: "91",
                mainId: responseJSON.mainId
            },
            success: function(json){
                var $tabForm08 = $("#tabForm08");
                $tabForm08.reset();
                $tabForm08.setData(json, false);
                if ($tabForm08.find("input[name=isGroupCompany1]:radio:checked").val() == "1") {
                    $tabForm08.find("#isGroupCompany1-1").show().siblings("[id^=isGroupCompany1]").hide();
                }
                $tabForm08.find("#curr6").val("TWD");
                $tabForm08.find("#unit1").val("1000");
                $tabForm08.find("#curr7").val("TWD");
                $tabForm08.find("#unit2").val("1000");
                
                $tabForm08.find("#grpFinYear").val(json.grpFinYear);
                $tabForm08.find("#ch9_GFin_SrcDate").val(json.ch9_GFin_SrcDate);
                $tabForm08.find("input[type='radio'][name='ch9FinInfor1'][value='" + json.ch9FinInfor1 + "']").attr("checked", true);
                
                
                $("#thickboxGrp").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.lmss08a["L120S08.thickbox10"],
                    width: 960,
                    height: 480,
                    modal: true,
                    i18n: i18n.def,
                    buttons: {
                        "saveData": function(){
                            //$.thickbox.close();
                            $.ajax({
                                handler: _handler,
                                type: "POST",
                                dataType: "json",
                                data: {
                                    formAction: "save",
                                    page: "91",
                                    mainId: responseJSON.mainId,
                                    toM4: $("#tabForm08").find("[name='toM4']:radio:checked").val(),
                                    typem4: $("#tabForm08").find("#typem4").val(),
                                    GroupCompanyID1: $("#tabForm08").find("#GroupCompanyID1").val(),
                                    GroupCompanyName1: $("#tabForm08").find("#GroupCompanyName1").val(),
                                    curr5: $("#tabForm08").find("#curr5").val(),
                                    unit: $("#tabForm08").find("#unit").val(),
                                    grt_IsNgRec: $("#tabForm08").find("#grt_IsNgRec").val(),
                                    grt_data_src: $("#tabForm08").find("#grt_data_src").val(),
                                    grt_data_date: $("#tabForm08").find("#grt_data_date").val(),
                                    curr6: $("#tabForm08").find("#curr6").val(),
                                    unit1: $("#tabForm08").find("#unit1").val(),
                                    grp_credit_note: $("#tabForm08").find("#grp_credit_note").val(),
                                    curr7: $("#tabForm08").find("#curr7").val(),
                                    unit2: $("#tabForm08").find("#unit2").val(),
                                    grp_ov_note: $("#tabForm08").find("#grp_ov_note").val(),
                                    gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
                                    gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
                                    ch9FinInfor1: $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val(),
                                    grpFinYear: $("#tabForm08").find("#grpFinYear").val(),
                                    ch9_GFin_SrcDate: $("#tabForm08").find("#ch9_GFin_SrcDate").val(),
                                    setGrp: true
                                },
                                success: function(json){
                                    $("#formL120m01e").setData(json.formL120m01e, false);
                                }
                            });
                        },
                        "print": function(){
                            printA91();
                            printA92();
                            printAD4();
                        },
                        "close": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            }
        });
    }
}

//連保人(產報表)
function printA41(){
    if ($("#s41grid").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    }
    else {
        var pdfName = "l120r01.pdf";
        var count = 0;
        var content = "";
        content = "R41A" + "^" + "";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1201r01rptservice"
            }
        });
    }
}

//最新集團企業(產報表)
function printA91(){
    if ($("#s91t1f1grid").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    }
    else {
        var pdfName = "l120r01.pdf";
        var count = 0;
        var content = "";
        content = "R91" + "^" + "";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1201r01rptservice"
            }
        });
    }
}

//最新集團企業(產報表)(退票、拒絕往來、逾期催收與呆帳情形)
function printA92(){
    if ($("#s91t1f1grid").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    }
    else {
        var pdfName = "l120r02.pdf";
        var count = 0;
        var content = "";
        content = "R92" + "^" + "";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1201r01rptservice"
            }
        });
    }
}

//最新集團企業財務概況
function printAD4(){

    var $tabForm08 = $("#tabForm08");
    var ch9FinInfor1 = $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val();
    if (ch9FinInfor1 != "1") {
        return;
    }
    if ($("#s91t1f1grid").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    }
    else {
        var pdfName = "l120r02.pdf";
        var count = 0;
        var content = "";
        content = "RD4" + "^" + "";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1201r01rptservice"
            }
        });
    }
}


/**
 * 個金簽報書連結
 */
function getCls(cls){
    $.ajax({ // 查詢主要借款人資料
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getRelate4",
            cesOid: cls
        },
        success: function(json){
            // 個金簽報書
            BrowserAction.submit({
                system: "lms",
                url: json.url,
                mainId: json.mainId,
                txCode: json.txCode,
                data: { //其它參數
                    uid: json.uid,
                    mainDocStatus: json.mainDocStatus,
                    oid: json.mainOid,
                    mainOid: json.mainOid
                }
            });
        }
    });
}

/**
 * 個金簽報書清除
 */
function clearContentPer(){
    $.ajax({ // 查詢主要借款人資料
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "clearContent",
            mainId: responseJSON.mainId,
            docType: 6
        },
        success: function(json){
            $("#docDscr6").html("");
            $("#docDscr6").val("");
        }
    });
}

/**
 * 個金簽報書夾帶Grid ThickBox
 */
function t120m01aPer(){
    uPerGrid();
    $("#t120m01aPer").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lmscommom["other.msg128"], //other.msg128=個金簽報書
        width: 800,
        height: 480,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                //取得使用者選擇依借款人Grid資料
                var rows = $("#120m01aPerGrid").getGridParam('selarrrow');
                var list = "";
                var sign = ",";
                for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
                    if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                        var data = $("#120m01aPerGrid").getRowData(rows[i]);
                        list += ((list == "") ? "" : sign) + data.oid;
                    }
                }
                
                if (list != "") {
                    $.ajax({ // 查詢主要借款人資料
                        handler: _handler,
                        type: "POST",
                        dataType: "json",
                        data: {
                            formAction: "findRelate4",
                            mainId: responseJSON.mainId,
                            //formL120m01e : JSON.stringify($("#formL120m01e").serializeData()),
                            clsOids: list
                        },
                        success: function(json){
                            //alert(JSON.stringify(json));
                            $("#docDscr6").html(DOMPurify.sanitize(json.docDscr6));
                            if (json.docDscr6 != "" && json.docDscr6 != undefined && json.docDscr6 != null) {
                                $("#docDscr6 a").attr({
                                    "href": "#"
                                });
                            }
                        }
                    });
                    $.thickbox.close();
                    $.thickbox.close();
                }
                else {
                    //並未選擇任何資料
                    CommonAPI.showMessage(i18n.lmss08a["L120S08.alert1"]);
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 更新個金簽報書夾帶Grid
 */
function uPerGrid(){
    $("#120m01aPerGrid").jqGrid("setGridParam", {
        handler: 'lms1201gridhandler',
        action: "queryL120m01aPer",
        postData: {
            rowNum: 15
        },
        search: true
    }).trigger("reloadGrid");
}

// 個金簽報書夾帶Grid
function perGrid(){
    var formMethod;
    formMethod = "queryL120m01aPer";
    var perGrid = $("#120m01aPerGrid").iGrid({
        handler: 'lms1201gridhandler',
        width: 785,
        height: 350,
        sortname: 'caseDate|caseNo',
        sortorder: 'desc|asc',
        shrinkToFit: true,
        autowidth: false,
        //2012-09-06 黃建霖 begin
        postData: {
            formAction: formMethod,
            rowNum: 15
        },
        //2012-09-06 黃建霖 end         
        rowNum: 15,
        multiselect: true,
        hideMultiselect: false,
        colModel: [{
            colHeader: i18n.lms1201v01["l120m01a.approvetime"], // 核准日期
            align: "center",
            width: 65, // 設定寬度
            sortable: true, // 是否允許排序			
            name: 'approveTime',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            hidden: true
        }, {
            colHeader: i18n.lms1201v01["l120m01a.casedate"], // 簽案日期
            align: "center",
            width: 70, // 設定寬度
            sortable: true, // 是否允許排序
            //			formatter : 'click',
            //			onclick : openDoc,
            name: 'caseDate' // col.id
        }, {
            colHeader: i18n.lms1201v01["l120m01a.custid"], // 統一編號
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lms1201v01["l120m01a.custname"], // 客戶名稱
            align: "left",
            width: 120, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lms1201v01["l120m01a.caseno"], // 案件號碼
            align: "left",
            width: 150, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'caseNo' // col.id
        }, {
            colHeader: i18n.lms1201v01["l120m01a.docname"],// "文件名稱"(授權別+'案件簽報書'+案件別),
            name: 'docKind',
            align: "left",
            width: 110,
            sortable: true
        }, {
            colHeader: i18n.lms1201v01["l120m01a.docstatus"], // 目前文件狀態
            align: "left",
            width: 50, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: "docStatus"// col.id
        }, {
            colHeader: i18n.lms1201v01["l120m01a.creatorname"], // 建立人員名稱
            align: "left",
            width: 90, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'updater' // col.id
        }, {
            colHeader: "areaDocstatus",
            name: 'areaDocstatus',
            hidden: true
        }, {
            colHeader: "rptTitle1",
            name: 'rptTitle1',
            hidden: true
        }, {
            colHeader: "rptTitle2",
            name: 'rptTitle2',
            hidden: true
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "docRslt",
            name: 'docRslt',
            hidden: true
        }, {
            colHeader: "docCode",
            name: 'docCode',
            hidden: true
        }, {
            colHeader: "docURL",
            name: 'docURL',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }, {
            colHeader: "srcMainId",
            name: 'srcMainId',
            hidden: true
        }, {
            colHeader: "ownBrId",
            name: 'ownBrId',
            hidden: true
        }, {
            colHeader: "authLvl",
            name: 'authLvl',
            hidden: true
        }, {
            colHeader: "areaChk",
            name: 'areaChk',
            hidden: true
        }, {
            colHeader: "hqMeetFlag",
            name: 'hqMeetFlag',
            hidden: true
        }, {
            colHeader: "uid",
            name: 'uid',
            hidden: true
        }, {
            colHeader: "caseBrId",
            name: 'caseBrId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
        }
    });
}

//UFO@20130114 案件報告表 BEGIN
function seachKindCaseRpt(){
    //	if ($("#lmss08f_page").attr("open") == "true") {
    $("#lmss08f_page").load("../../lms/lmss08f", function(){
        $("#thickboxCaseRpt").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.lmss08a["L120S08.thickbox16"],
            width: 960,
            height: 480,
            modal: true,
            i18n: i18n.def,
            buttons: {
                "close": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
        
    });
    //		$("#lmss08f_page").attr("open", false);
    //	} else {
    //		$("#thickboxCaseRpt").thickbox({ // 使用選取的內容進行彈窗
    //			title : i18n.lmss08a["L120S08.thickbox16"],
    //			width : 960,
    //			height : 480,
    //			modal : true,
    //			i18n : i18n.def,
    //			buttons : {
    //				"close" : function() {
    //					API.confirmMessage(i18n.def['flow.exit'], function(res) {
    //						if (res) {
    //							$.thickbox.close();
    //						}
    //					});
    //				}
    //			}
    //		});
    //	}
}


//UFO@20130114 案件報告表 END
