/* 
 * C101S01QDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01Q_N;

/** 個金非房貸信用評等表 **/
public interface C101S01Q_NDao extends IGenericDao<C101S01Q_N> {

	C101S01Q_N findByOid(String oid);
	
	List<C101S01Q_N> findByMainId(String mainId);
	
	C101S01Q_N findByUniqueKey(String mainId, String ownBrId, String custId, String dupNo);
	
	List<C101S01Q_N> findByIndex01(String mainId, String ownBrId, String custId,
			String dupNo);

	List<C101S01Q_N> findByCustIdDupId(String custId,String dupNo);
	
	int deleteByOid(String oid);
}