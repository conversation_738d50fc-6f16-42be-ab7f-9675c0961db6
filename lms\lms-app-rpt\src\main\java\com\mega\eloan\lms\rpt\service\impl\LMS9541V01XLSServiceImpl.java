package com.mega.eloan.lms.rpt.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.model.L810M01A;
import com.mega.eloan.lms.rpt.pages.LMS9541V01Page;
import com.mega.eloan.lms.rpt.service.LMS9541V01Service;
import com.mega.sso.service.BranchService;

import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.format.Alignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

@Service("lms9541v01xlsservice")
public class LMS9541V01XLSServiceImpl implements FileDownloadService {
	@Resource
	LMS9541V01Service service;

	@Resource
	BranchService branchService;
	
	@Resource
	DocFileService fileService;

	@Resource
	DocFileDao fileDao;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9541V01XLSServiceImpl.class);

	private String[] columnName = { "brno", "areaNo", "CName", "custId",
			"appDate", "appMoney", "favloan", "noHouse", "apprDate" };

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		// Properties
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9541V01Page.class);
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> list = null;
		String brno = Util.nullToSpace(params.getString("brno"));
		// 除了授管處及資訊處可取得全部資料，其他分行依照各分行代號碼得資料
		list = service.findMisData(params.getString("kindNo"), false, false,
				brno);

//		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		try {
			baos = new ByteArrayOutputStream();
			File xls = new File(Thread
					.currentThread()
					.getContextClassLoader()
					.getResource(
							PropUtil.getProperty("loadFile.dir")
									+ "excel/L810M01A1.xls").toURI());
			Workbook wbook = Workbook.getWorkbook(xls);
			// WriteAccessRecord中有用到arraycopy，但長度寫死，導致write會出錯(ArrayIndexOutOfBoundsException)，加上settings便可解決
			WorkbookSettings settings = new WorkbookSettings();
			settings.setWriteAccess(null);

			WritableWorkbook book = Workbook.createWorkbook(baos, wbook,
					settings);
			WritableSheet sheet = book.getSheet(0);
			int headShift = 4;

			// --------------特殊格式----------
			WritableFont fontStyle = new WritableFont(
					WritableFont.createFont("標楷體"), 12, WritableFont.NO_BOLD,
					false);
			WritableCellFormat cellformat = null;
			cellformat = LMSUtil.setCellFormat(cellformat, fontStyle,
					Alignment.CENTRE);

			// 標題訊息
			Label title = new Label(2, 1, brno
					+ branchService.getBranchName(brno));
			sheet.addCell(title);
			title = new Label(2, 2, CapDate.getCurrentDate("yyyy-MM-dd"));
			sheet.addCell(title);
			title = new Label(columnName.length - 1, 2, pop.getProperty("base"));
			sheet.addCell(title);

			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> pivot = list.get(i);
				for (int j = 0; j < columnName.length; j++) {
					String value = HtmlUtils.htmlEscape(Util.trim(pivot.get(columnName[j])));
					Label label = new Label(j, i + headShift, value, cellformat);
					sheet.addCell(label);
				}
			}
			// 合計區塊
			headShift = headShift + list.size()-1;
			list = service.findMisData(params.getString("kindNo"), true, false,
					brno);
			// 合計
			String value = Util.trim(list.get(0).get("TOT_CASE"));
			Label label = new Label(0, headShift + 1, pop.getProperty("total"),
					cellformat);
			sheet.addCell(label);
			label = new Label(1, headShift + 1, value, cellformat);
			sheet.addCell(label);
			label = new Label(2, headShift + 1, pop.getProperty("record"),
					cellformat);
			sheet.addCell(label);
			// 受理額度
			value = Util.trim(list.get(0).get("APP_MONEY"));
			label = new Label(3, headShift + 1, pop.getProperty("totAppMoney"),
					cellformat);
			sheet.addCell(label);
			label = new Label(4, headShift + 1, value, cellformat);
			sheet.addCell(label);
			label = new Label(5, headShift + 1, pop.getProperty("base"),
					cellformat);
			sheet.addCell(label);
			// 優惠額度
			value = Util.trim(list.get(0).get("FAV_LOAN"));
			label = new Label(6, headShift + 1, pop.getProperty("totFavloan"),
					cellformat);
			sheet.addCell(label);
			label = new Label(7, headShift + 1, value, cellformat);
			sheet.addCell(label);
			label = new Label(8, headShift + 1, pop.getProperty("base"),
					cellformat);
			sheet.addCell(label);

			book.write();
			book.close();
			byte[] excel = baos.toByteArray();

			// 存入DocFile
			L810M01A record = service.findModelByOid(L810M01A.class,
					params.getString("oid"));

			ISearch search = fileDao.createSearchTemplete();
			search.addSearchModeParameters(SearchMode.EQUALS, "oid",
					params.getString("rptOid"));
			List<DocFile> files = fileDao.find(search);
			DocFile file;
			if (files == null || files.size() == 0) {
				file = new DocFile();
				file.setMainId(IDGenerator.getUUID());
				file.setFieldId("rpt");
				file.setSrcFileName(record.getRptName() + ".xls");
				file.setBranchId(brno);
				file.setContentType("application/msexcel");
				file.setSysId("LMS");
			} else {
				file = files.get(0);
				for (int i = 1; i < files.size(); i++) {
					fileDao.delete(files);
				}
			}
			file.setDeletedTime(null);
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setCrYear(CapDate.getCurrentDate("yyyy"));
			file.setData(excel);
			fileService.save(file);
			file = fileDao.find(file);

			// 記錄DocFile

			record.setRptOid(file.getOid());
			record.setCreateTime(CapDate.getCurrentTimestamp());
			service.save(record);

			return excel;
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}
}
