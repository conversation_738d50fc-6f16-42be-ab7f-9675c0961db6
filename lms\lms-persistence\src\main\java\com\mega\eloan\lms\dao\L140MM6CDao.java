/* 
 * L140MM6CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MM6C;

/** 共同行銷維護作業資訊檔 **/
public interface L140MM6CDao extends IGenericDao<L140MM6C> {

	L140MM6C findByOid(String oid);

	List<L140MM6C> findByIndex01(String mainId);

	L140MM6C findByIndex02(String mainId, String type);
}