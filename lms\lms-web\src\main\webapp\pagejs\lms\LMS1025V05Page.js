pageJsInit(function() {
	$(function() {
		var mainId = "lms1025v05docfile000000000000000";
		var grid = $("#gridview").iGrid({
			handler: "lms1025gridhandler",
			action: "queryModelCmp",
			sortname: 'uploadTime',
			sortorder: 'desc',
			needPager: false,
			height: '400',
			postData: {
				mainId: mainId
			},
			colModel: [{
				colHeader: i18n.lms1025v05["grid.001"],
				name: "srcFileName",
				sortable: false,
				formatter: 'click',
				onclick: function(cellvalue, options, rowObject) {
					$.capFileDownload({
						target: "_self",
						handler: "simplefiledwnhandler",
						data: {
							fileOid: rowObject.oid
						}
					});
				}
			}, {
				colHeader: i18n.def["lastUpdater"],
				name: "fileDesc",
				sortable: true
			}, {
				colHeader: i18n.def["lastUpdateTime"],
				name: "uploadTime",
				sortable: true
			}, {
				name: "oid",
				hidden: true

			}]
		});


		$("#btnCreateExl").click(function() {
			//不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
			$.form.submit({
				url: __ajaxHandler,
				target: "_blank",
				data: {
					_pa: 'lmsdownloadformhandler',
					'mode': 'expXls',
					'fileDownloadName': 'data.xls',
					'serviceName': 'lms1025xlsservice'
				}
			});
		});

		$("#btnView").click(function() {
			var fieldId = 'file';
			uploadFunction(fieldId);
		});

		var uploadFunction = function(fieldId) {
			var limitFileSize = 3145728;//3*1024*1024
			MegaApi.uploadDialog({
				handler: "lms1025v05fileuploadhandler",
				fieldId: "null",
				subTitle: i18n.def('insertfileSize', {
					'fileSize': (limitFileSize / 1048576).toFixed(2)
				}),
				fileCheck: ['xls'],
				successMsg: true,
				height: 160,
				limitSize: limitFileSize,
				data: {
					'deleteDup': false,
					'fieldId': fieldId
				},
				success: function() {
					grid.trigger('reloadGrid');
					$.thickbox.close();
				}
			});
		};

		var get_grid_oid = function() {
			var rows = $("#gridview").getGridParam('selrow');
			if (rows != 'undefined' && rows != null && rows != 0) {
				var data = $("#gridview").getRowData(rows);
				return data.oid;
			}
			return "";
		};

		$("#btnDelete").click(function() {
			var oid = get_grid_oid();
			if (oid == "") {
				CommonAPI.showMessage(i18n.def["grid.selrow"]);
				return;
			}
			CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b) {
				if (b) {
					$.ajax({
						handler: "lms1025m01formhandler", type: "POST",
						data: {
							'formAction': "delModelCompareFile",
							'docOid': oid
						},
						}).done(function(obj) {
							$("#gridview").trigger("reloadGrid");
					});
				}
			});
		});

		$("#btnUPCls").click(function() {
			//StartCompare
			var oid = get_grid_oid();
			if (oid == "") {
				CommonAPI.showMessage(i18n.def["grid.selrow"]);
				return;
			}
			CommonAPI.confirmMessage(i18n.def["confirmRun"], function(b) {
				if (b) {
					var params = {};
					var my_timeout = 7200000;//ms
					if (true) {//延長 timer
						timer_long_ajax_beg(my_timeout);
					}
					$.ajax({
						handler: "lms1025m01formhandler", action: "callBatch", timeout: my_timeout,
						data: $.extend({
							'act': 'runModelCompare'
							, 'docOid': oid
							, 'jq_timeout': (my_timeout / 1000)
						}, params),
						success: function(json_callBatch) {
							if (true) {//恢復 timer
								timer_long_ajax_end();
							}
							if (json_callBatch.r.response === "SUCCESS") {
								API.showMessage(i18n.def.runSuccess);
							} else {
								API.showErrorMessage(json_callBatch.r.response);
							}
						}
					});
				}
			});
		});
	});
});
