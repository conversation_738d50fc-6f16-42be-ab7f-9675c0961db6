/* 
 * LMS7830ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.eloandb.service.LmsCustdataService;
import com.mega.eloan.lms.lms.pages.LMS7830M01Page;
import com.mega.eloan.lms.lms.service.LMS7830Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

/**
 * <pre>
 * 簽報紀錄查詢
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/8,REX,new
 *          </ul>
 */
@Service
public class LMS7830ServiceImpl implements LMS7830Service {

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LmsCustdataService lmsCustdataService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Override
	public Page<Map<String, Object>> queryData(String custId, String dupNo,
			ISearch search) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS7830M01Page.class);
		String[] codeType = { UtilConstants.CodeTypeItem.簽案文件狀態 };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);

		List<Map<String, Object>> caseList = misdbBASEService.findEllnseek(
				custId, dupNo);
		StringBuilder temp = new StringBuilder("");
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> row : caseList) {
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("custId", (String) row.get("CUSTID"));
			data.put("dupNo", (String) row.get("DUPNO"));
			data.put("cntrNo", (String) row.get("CNTRNO"));
			data.put("brNo",
					branchService.getBranchName((String) row.get("BRNO")));

			data.put("status",
					pop.getProperty("doc." + (String) row.get("STATUS")));
			data.put("cType", codeMap.get(UtilConstants.CodeTypeItem.簽案文件狀態)
					.get((String) row.get("CTYPE")));
			temp.append((String) row.get("APPRYY")).append("-");
			temp.append((String) row.get("APPRMM"));
			data.put("caseDate", temp.toString());
			temp.setLength(0);
			beanList.add(data);
		}
		return LMSUtil.getMapGirdDataRow(beanList, search);
	}

	@Override
	public List<Map<String, Object>> queryCustData(String custId) {
		return lmsCustdataService.findCustDataByCustId(custId);
	}

}
