/* 
 * C241M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ToStringExclude;
import org.hibernate.engine.FetchStyle;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.IDataObject;

/** 覆審報告表主檔 **/
@NamedEntityGraph(name = "C241M01A-entity-graph", attributeNodes = { @NamedAttributeNode("c240m01b") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C241M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C241M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "c241m01a", fetch = FetchType.LAZY)
	private Set<C241A01A> c241a01a;

	public Set<C241A01A> getC241a01a() {
		return c241a01a;
	}

	public void setC241a01a(Set<C241A01A> c241a01a) {
		this.c241a01a = c241a01a;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "REFMAINID", nullable = false, insertable = false, updatable = false) })
	private C240M01B c240m01b;

	public C240M01B getC240m01b() {
		return c240m01b;
	}

	public void setC240m01b(C240M01B c240m01b) {
		this.c240m01b = c240m01b;
	}

	/**
	 * 是否結案
	 * <p/>
	 * Y: 已結案 N: 未結案<br/>
	 * 2011/08/10 新增：文件鎖定檢查時使用
	 */
	@Column(name = "ISCLOSED", length = 1, columnDefinition = "CHAR(1)")
	private String isClosed;

	/**
	 * 覆審序號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 */
	@Column(name = "PROJECTNO", length = 64, columnDefinition = "VARCHAR(64)")
	private String projectNo;

	/**
	 * 是否為行員
	 * <p/>
	 * Y/N
	 */
	@Column(name = "STAFF", length = 1, columnDefinition = "VARCHAR(1)")
	private String staff;

	/**
	 * 團貸序號
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 */
	@Column(name = "GRPCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String grpCntrNo;

	/**
	 * 團貸筆數
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 */
	@Column(name = "GRPCOUNT", columnDefinition = "DECIMAL(5,0)")
	private Integer grpCount;

	/** 動用止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GRPEND", columnDefinition = "DATE")
	private Date grpEnd;

	/** 實際覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "RETRIALDATE", columnDefinition = "DATE")
	private Date retrialDate;

	/** 上次覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LASTRETRIALDATE", columnDefinition = "DATE")
	private Date lastRetrialDate;

	/** 最遲應覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "SHOULDREVIEWDATE", columnDefinition = "DATE")
	private Date shouldReviewDate;

	/**
	 * 覆審類別
	 * <p/>
	 * ※所符合的覆審規則
	 */
	@Column(name = "RETRIALKIND", length = 60, columnDefinition = "VARCHAR(60)")
	private String retrialKind;

	/**
	 * 99類覆審週期
	 * <p/>
	 * ※後來加的(因ELF491_MAINCUST沒有在用故借用此欄位);如果覆審類別99時，授管要求可以自行決定覆審週期1-1個月;6-6個月;A-
	 * 12個月<br/>
	 * 項目：<br/>
	 * 0:免再審<br/>
	 * 1:一個月<br/>
	 * 3:三個月<br/>
	 * 6:六個月<br/>
	 * A:十二個月
	 */
	@Column(name = "SPECIFYCYCLE", length = 2, columnDefinition = "VARCHAR(2)")
	private String specifyCycle;

	/**
	 * 文件產生方式
	 * <p/>
	 * SYS.系統產生
	 */
	@Column(name = "NCREATDATA", length = 3, columnDefinition = "VARCHAR(3)")
	private String nCreatData;

	/** 覆審報告表種類{N:一般, G:團貸母戶, S:防杜代辦覆審, P:價金履保, H:小額/團體消費性貸款} */
	@Column(name = "DOCKIND", length = 1, columnDefinition = "VARCHAR(1)")
	private String docKind;

	/**
	 * 新貸/舊案
	 * <p/>
	 * Y.【新案】N.【舊案】P.【人工】G.【團貸】
	 */
	@Column(name = "NEWCASE", length = 1, columnDefinition = "VARCHAR(1)")
	private String newCase;

	/**
	 * 刪除(註記)不覆審客戶
	 * <p/>
	 * ※系統註記刪除<br/>
	 * Y/N
	 */
	@Column(name = "SYSDEL", length = 1, columnDefinition = "VARCHAR(1)")
	private String sysDel;

	/**
	 * 刪除(註記)不覆審客戶
	 * <p/>
	 * ※一般分行註記刪除<br/>
	 * Y/N
	 */
	@Column(name = "BRANCHDEL", length = 1, columnDefinition = "VARCHAR(1)")
	private String branchDel;

	/**
	 * 刪除(註記)不覆審客戶
	 * <p/>
	 * ※管理單位註記刪除<br/>
	 * 918, 920, 922, <br/>
	 * 931, 932, 933, 934, 935, 900<br/>
	 * Y/N
	 */
	@Column(name = "GENERALDEL", length = 1, columnDefinition = "VARCHAR(1)")
	private String generalDel;

	/**
	 * 原始實際覆審日期
	 * <p/>
	 * ※避免還原時又要重讀資料，先保留原始值
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RETRIALDATEOLD", columnDefinition = "DATE")
	private Date retrialDateOld;

	/**
	 * 原始上次覆審日期
	 * <p/>
	 * ※避免還原時又要重讀資料，先保留原始值
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LASTRETRIALDATEOLD", columnDefinition = "DATE")
	private Date lastRetrialDateOld;

	/** 不覆審原因代碼 **/
	@Column(name = "NCKDFLAG", length = 2, columnDefinition = "VARCHAR(2)")
	private String nCkdFlag;

	/** 不覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "NCKDDATE", columnDefinition = "DATE")
	private Date nCkdDate;

	/**
	 * 不覆審備註
	 * <p/>
	 * 128個全型字
	 */
	@Column(name = "NCKDMEMO", length = 384, columnDefinition = "VARCHAR(384)")
	private String nCkdMemo;

	/**
	 * 不覆審註記
	 * <p/>
	 * Y/N
	 */
	@Column(name = "RETRIALYN", length = 1, columnDefinition = "VARCHAR(1)")
	private String retrialYN;

	/**
	 * 授信資料引進日期
	 * <p/>
	 * 100/11/11新增<br/>
	 * 帳務資料日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNDATADATE", columnDefinition = "DATE")
	private Date lnDataDate;

	/**
	 * 額度幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 */
	@Column(name = "TOTQUOTACURR", length = 3, columnDefinition = "CHAR(3)")
	private String totQuotaCurr;

	/**
	 * 額度合計
	 * <p/>
	 * 100/11/11新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 */
	@Column(name = "TOTQUOTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totQuota;

	/**
	 * 前日結欠餘額幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 */
	@Column(name = "TOTBALCURR", length = 3, columnDefinition = "CHAR(3)")
	private String totBalCurr;

	/**
	 * 前日結欠餘額合計
	 * <p/>
	 * 100/11/11新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 */
	@Column(name = "TOTBAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totBal;

	/**
	 * 團貸所有明細EXCEL檔檔案位置
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 */
	@Column(name = "BRANCHEXCELFILE", length = 40, columnDefinition = "VARCHAR(40)")
	private String branchExcelFile;

	/**
	 * 團貸所有明細EXCEL檔產生時間
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)<br/>
	 * 格式：UserName(CurrentTime)
	 */
	@Column(name = "BRANCHEXCELDATE", length = 40, columnDefinition = "VARCHAR(40)")
	private String branchExcelDate;

	/**
	 * RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/** 覆審意見_編製完成日期 **/
	@Column(name = "UPDATE", columnDefinition = "TIMESTAMP")
	private Timestamp upDate;

	/**
	 * 覆審意見_覆審情形
	 * <p/>
	 * 1.覆審正常<br/>
	 * 2.異常情形，應改善或注意事項
	 */
	@Column(name = "CONFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String conFlag;

	/**
	 * 覆審意見_異常情形，應改善或注意事項
	 * <p/>
	 * 3000個全型字
	 */
	@Column(name = "CONDITION", length = 9000, columnDefinition = "VARCHAR(9000)")
	private String condition;

	/**
	 * 受檢單位洽辦情形
	 * <p/>
	 * 128個全型字
	 */
	@Column(name = "BRANCHCOMM", length = 384, columnDefinition = "VARCHAR(384)")
	private String branchComm;

	/**
	 * 無擔逾期戶是否需半年再審
	 */
	@Column(name = "OVERDUEYN", length = 1, columnDefinition = "CHAR(1)")
	private String overdueYN;

	/**
	 * 異常通報日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "UCKDDT", columnDefinition = "DATE")
	private Date uckdDt;

	/**
	 * 記錄次數
	 */
	@Column(name = "UCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String uckdLine;

	/**
	 * 異常通報代碼
	 */
	@Column(name = "MDFLAG", length = 3, columnDefinition = "VARCHAR(3)")
	private String mdFlag;

	/**
	 * 已累計8_1次數，並搬移8_1_flag、8_1_flag_o
	 */
	@Column(name = "UP8_1", length = 1, columnDefinition = "CHAR(1)")
	private String up8_1;

	/**
	 * 已逾到期日 0:無 ; 4:4月 ; 12:1年
	 */
	@Column(name = "OVERDUEP", length = 2, columnDefinition = "CHAR(2)")
	private String overdueP;

	/**
	 * 仲介ID
	 */
	@Column(name = "COMID", length = 10, columnDefinition = "VARCHAR(10)")
	private String comId;

	/**
	 * 仲介重複碼
	 */
	@Column(name = "COMDUP", length = 1, columnDefinition = "CHAR(1)")
	private String comDup;

	/**
	 * 仲介名稱
	 */
	@Column(name = "COMNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String comName;

	/**
	 * 買方ID
	 */
	@Column(name = "BUYERID", length = 10, columnDefinition = "VARCHAR(10)")
	private String buyerId;

	/**
	 * 買方重複碼
	 */
	@Column(name = "BUYERDUP", length = 1, columnDefinition = "CHAR(1)")
	private String buyerDup;

	/**
	 * 買方名稱
	 */
	@Column(name = "BUYERNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String buyerName;

	/**
	 * 價金履保控管類別
	 */
	@Column(name = "LNF660_LOAN_CLASS", length = 2, columnDefinition = "CHAR(2)")
	private String lnf660_loan_class;

	/**
	 * 存款(代收)帳號
	 */
	@Column(name = "PBACCT", length = 20, columnDefinition = "CHAR(20)")
	private String pbAcct;

	/**
	 * 母戶額度序號
	 */
	@Column(name = "LNF660_M_CONTRACT", length = 12, columnDefinition = "CHAR(12)")
	private String lnf660_m_contract;

	/** 覆審報告表格式{A一般/B實地覆審} **/
	@Column(name = "DOCFMT", length = 1, columnDefinition = "VARCHAR(1)")
	private String docFmt;

	/** 上次土建融實地覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LASTREALDT", columnDefinition = "DATE")
	private Date lastRealDt;

	/** 帳務含土建融{Y/N/O} 企金引ELF412,消金由c241m01b.lnType/syndType判斷 **/
	@Column(name = "REALCKFG", length = 1, columnDefinition = "VARCHAR(1)")
	private String realCkFg;

	/** 不覆審說明 **/
	@Column(name = "NCKDDETAIL", length = 300, columnDefinition = "VARCHAR(300)")
	private String nckdDetail;

	/** 履保母戶幣別 **/
	@Column(name = "LNF660_M_CURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String lnf660_m_curr;

	/** 履保母戶額度金額 **/
	@Column(name = "LNF660_M_FACT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf660_m_fact;

	/** 履保母戶起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF660_M_BEGDATE", columnDefinition = "DATE")
	private Date lnf660_m_begDate;

	/** 履保母戶迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF660_M_ENDDATE", columnDefinition = "DATE")
	private Date lnf660_m_endDate;

	/** 履保母戶科目 **/
	@Column(name = "LNF660_M_SUBJ", length = 3, columnDefinition = "VARCHAR(3)")
	private String lnf660_m_subj;

	/**
	 * 覆審意見_三、目前評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	@Column(name = "CURRATE", length = 3, columnDefinition = "CHAR(3)")
	private String curRate;

	/**
	 * 覆審意見_三、建議評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	@Column(name = "SUGRATE", length = 3, columnDefinition = "CHAR(3)")
	private String sugRate;

	/** 防杜代辦覆審_為代辦案件(Y/N) */
	@Column(name = "CONFLAG2A", length = 1, columnDefinition = "CHAR(1)")
	private String conFlag2A;

	/** 防杜代辦覆審_有與代辦業者掛勾(Y/N) */
	@Column(name = "CONFLAG2B", length = 1, columnDefinition = "CHAR(1)")
	private String conFlag2B;

	/** 防杜代辦覆審_有其它違規或異常(Y/N) */
	@Column(name = "CONFLAG2C", length = 1, columnDefinition = "CHAR(1)")
	private String conFlag2C;

	/** 防杜代辦覆審_基準期間 */
	@Column(name = "PA_YM", length = 7, columnDefinition = "CHAR(7)")
	private String pa_ym;

	/** 防杜代辦覆審_對象類別 */
	@Column(name = "PA_TRG", length = 2, columnDefinition = "CHAR(2)")
	private String pa_trg;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 查詢日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "OVQRYDT", columnDefinition = "DATE")
	private Date ovQryDt;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 本金逾期天數
	 */
	@Column(name = "CAPDAYS", columnDefinition = "DECIMAL(5,0)")
	private Integer capDays;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 本金逾期日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CAPDT", columnDefinition = "DATE")
	private Date capDt;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 本金資料日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CAPDATADT", columnDefinition = "DATE")
	private Date capDataDt;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 利息逾期天數
	 */
	@Column(name = "INTDAYS", columnDefinition = "DECIMAL(5,0)")
	private Integer intDays;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 利息逾期日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "INTDT", columnDefinition = "DATE")
	private Date intDt;

	/**
	 * J-108-0268 覆審案件 客戶逾期情形 利息資料日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "INTDATADT", columnDefinition = "DATE")
	private Date intDataDt;

	/**
	 * 處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	@Size(max = 3)
	@Column(name = "STATUS", length = 3, columnDefinition = "CHAR(3)")
	private String status;

	/** 回傳結果原因 **/
	@Size(max = 300)
	@Column(name = "REASON", length = 300, columnDefinition = "VARCHAR(300)")
	private String reason;

	/**
	 * 考核表(performance appraisal) - 是否有須扣分情事
	 * <p/>
	 * Y/N
	 */
	@Column(name = "NEEDPA", length = 300, columnDefinition = "CHAR(1)")
	private  String needPa;
	
	/**
	 * 考核表(performance appraisal) - 版本
	 * 依實際覆審日retrialDate決定版本
	 * <p/>
	 * <li>舊版""</li>
	 * <li>J-112-0227 新增新版覆審審核表 新增"Ver20230701",23023-07開始適用</li>
	 */
	@Column(name = "PAVER", length = 15, columnDefinition = "VARCHAR(15)")
	private  String paVer;

	/** 首次覆核時間
	 * 上線時舊案以retrialDate帶入 **/
	@Column(name="FIRSTACCTIME", columnDefinition="TIMESTAMP")
	private Timestamp firstAccTime;

	/**
	 * JOIN考評表明細檔ByMAINID
	 *
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "c241m01A", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<C241M01G> c241m01gs;

	public Set<C241M01G> getC241m01gs() {
		return c241m01gs;
	}

	public void setC241m01gs(Set<C241M01G> c241m01gs) {
		this.c241m01gs = c241m01gs;
	}

	/**
	 * 取得是否結案
	 * <p/>
	 * Y: 已結案 N: 未結案<br/>
	 * 2011/08/10 新增：文件鎖定檢查時使用
	 */
	public String getIsClosed() {
		return this.isClosed;
	}

	/**
	 * 設定是否結案
	 * <p/>
	 * Y: 已結案 N: 未結案<br/>
	 * 2011/08/10 新增：文件鎖定檢查時使用
	 **/
	public void setIsClosed(String value) {
		this.isClosed = value;
	}

	/**
	 * 取得覆審序號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 */
	public String getProjectNo() {
		return this.projectNo;
	}

	/**
	 * 設定覆審序號
	 * <p/>
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號，例：2011蘭雅(兆)覆審字第001-003號
	 **/
	public void setProjectNo(String value) {
		this.projectNo = value;
	}

	/**
	 * 取得是否為行員
	 * <p/>
	 * Y/N
	 */
	public String getStaff() {
		return this.staff;
	}

	/**
	 * 設定是否為行員
	 * <p/>
	 * Y/N
	 **/
	public void setStaff(String value) {
		this.staff = value;
	}

	/**
	 * 取得團貸序號
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 */
	public String getGrpCntrNo() {
		return this.grpCntrNo;
	}

	/**
	 * 設定團貸序號
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 **/
	public void setGrpCntrNo(String value) {
		this.grpCntrNo = value;
	}

	/**
	 * 取得團貸筆數
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 */
	public Integer getGrpCount() {
		return this.grpCount;
	}

	/**
	 * 設定團貸筆數
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 **/
	public void setGrpCount(Integer value) {
		this.grpCount = value;
	}

	/** 取得動用止日 **/
	public Date getGrpEnd() {
		return this.grpEnd;
	}

	/** 設定動用止日 **/
	public void setGrpEnd(Date value) {
		this.grpEnd = value;
	}

	/** 取得實際覆審日期 **/
	public Date getRetrialDate() {
		return this.retrialDate;
	}

	/** 設定實際覆審日期 **/
	public void setRetrialDate(Date value) {
		this.retrialDate = value;
	}

	/** 取得上次覆審日期 **/
	public Date getLastRetrialDate() {
		return this.lastRetrialDate;
	}

	/** 設定上次覆審日期 **/
	public void setLastRetrialDate(Date value) {
		this.lastRetrialDate = value;
	}

	/** 取得最遲應覆審日期 **/
	public Date getShouldReviewDate() {
		return this.shouldReviewDate;
	}

	/** 設定最遲應覆審日期 **/
	public void setShouldReviewDate(Date value) {
		this.shouldReviewDate = value;
	}

	/**
	 * 取得覆審類別
	 * <p/>
	 * ※所符合的覆審規則
	 */
	public String getRetrialKind() {
		return this.retrialKind;
	}

	/**
	 * 設定覆審類別
	 * <p/>
	 * ※所符合的覆審規則
	 **/
	public void setRetrialKind(String value) {
		this.retrialKind = value;
	}

	/**
	 * 取得99類覆審週期
	 * <p/>
	 * ※後來加的(因ELF491_MAINCUST沒有在用故借用此欄位);如果覆審類別99時，授管要求可以自行決定覆審週期1-1個月;6-6個月;A-
	 * 12個月<br/>
	 * 項目：<br/>
	 * 0:免再審<br/>
	 * 1:一個月<br/>
	 * 3:三個月<br/>
	 * 6:六個月<br/>
	 * A:十二個月
	 */
	public String getSpecifyCycle() {
		return this.specifyCycle;
	}

	/**
	 * 設定99類覆審週期
	 * <p/>
	 * ※後來加的(因ELF491_MAINCUST沒有在用故借用此欄位);如果覆審類別99時，授管要求可以自行決定覆審週期1-1個月;6-6個月;A-
	 * 12個月<br/>
	 * 項目：<br/>
	 * 0:免再審<br/>
	 * 1:一個月<br/>
	 * 3:三個月<br/>
	 * 6:六個月<br/>
	 * A:十二個月
	 **/
	public void setSpecifyCycle(String value) {
		this.specifyCycle = value;
	}

	/**
	 * 取得文件產生方式
	 * <p/>
	 * SYS.系統產生
	 */
	public String getNCreatData() {
		return this.nCreatData;
	}

	/**
	 * 設定文件產生方式
	 * <p/>
	 * SYS.系統產生
	 **/
	public void setNCreatData(String value) {
		this.nCreatData = value;
	}

	/** 取得覆審報告表種類 */
	public String getDocKind() {
		return this.docKind;
	}

	/** 設定覆審報告表種類 */
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/**
	 * 取得新貸/舊案
	 * <p/>
	 * Y.【新案】N.【舊案】P.【人工】G.【團貸】
	 */
	public String getNewCase() {
		return this.newCase;
	}

	/**
	 * 設定新貸/舊案
	 * <p/>
	 * Y.【新案】N.【舊案】P.【人工】G.【團貸】
	 **/
	public void setNewCase(String value) {
		this.newCase = value;
	}

	/**
	 * 取得刪除(註記)不覆審客戶
	 * <p/>
	 * ※系統註記刪除<br/>
	 * Y/N
	 */
	public String getSysDel() {
		return this.sysDel;
	}

	/**
	 * 設定刪除(註記)不覆審客戶
	 * <p/>
	 * ※系統註記刪除<br/>
	 * Y/N
	 **/
	public void setSysDel(String value) {
		this.sysDel = value;
	}

	/**
	 * 取得刪除(註記)不覆審客戶
	 * <p/>
	 * ※一般分行註記刪除<br/>
	 * Y/N
	 */
	public String getBranchDel() {
		return this.branchDel;
	}

	/**
	 * 設定刪除(註記)不覆審客戶
	 * <p/>
	 * ※一般分行註記刪除<br/>
	 * Y/N
	 **/
	public void setBranchDel(String value) {
		this.branchDel = value;
	}

	/**
	 * 取得刪除(註記)不覆審客戶
	 * <p/>
	 * ※管理單位註記刪除<br/>
	 * 918, 920, 922, <br/>
	 * 931, 932, 933, 934, 935, 900<br/>
	 * Y/N
	 */
	public String getGeneralDel() {
		return this.generalDel;
	}

	/**
	 * 設定刪除(註記)不覆審客戶
	 * <p/>
	 * ※管理單位註記刪除<br/>
	 * 918, 920, 922, <br/>
	 * 931, 932, 933, 934, 935, 900<br/>
	 * Y/N
	 **/
	public void setGeneralDel(String value) {
		this.generalDel = value;
	}

	/**
	 * 取得原始實際覆審日期
	 * <p/>
	 * ※避免還原時又要重讀資料，先保留原始值
	 */
	public Date getRetrialDateOld() {
		return this.retrialDateOld;
	}

	/**
	 * 設定原始實際覆審日期
	 * <p/>
	 * ※避免還原時又要重讀資料，先保留原始值
	 **/
	public void setRetrialDateOld(Date value) {
		this.retrialDateOld = value;
	}

	/**
	 * 取得原始上次覆審日期
	 * <p/>
	 * ※避免還原時又要重讀資料，先保留原始值
	 */
	public Date getLastRetrialDateOld() {
		return this.lastRetrialDateOld;
	}

	/**
	 * 設定原始上次覆審日期
	 * <p/>
	 * ※避免還原時又要重讀資料，先保留原始值
	 **/
	public void setLastRetrialDateOld(Date value) {
		this.lastRetrialDateOld = value;
	}

	/** 取得不覆審原因代碼 **/
	public String getNCkdFlag() {
		return this.nCkdFlag;
	}

	/** 設定不覆審原因代碼 **/
	public void setNCkdFlag(String value) {
		this.nCkdFlag = value;
	}

	/** 取得不覆審日期 **/
	public Date getNCkdDate() {
		return this.nCkdDate;
	}

	/** 設定不覆審日期 **/
	public void setNCkdDate(Date value) {
		this.nCkdDate = value;
	}

	/**
	 * 取得不覆審備註
	 * <p/>
	 * 128個全型字
	 */
	public String getNCkdMemo() {
		return this.nCkdMemo;
	}

	/**
	 * 設定不覆審備註
	 * <p/>
	 * 128個全型字
	 **/
	public void setNCkdMemo(String value) {
		this.nCkdMemo = value;
	}

	/**
	 * 取得不覆審註記
	 * <p/>
	 * Y/N
	 */
	public String getRetrialYN() {
		return this.retrialYN;
	}

	/**
	 * 設定不覆審註記
	 * <p/>
	 * Y/N
	 **/
	public void setRetrialYN(String value) {
		this.retrialYN = value;
	}

	/**
	 * 取得授信資料引進日期
	 * <p/>
	 * 100/11/11新增<br/>
	 * 帳務資料日期
	 */
	public Date getLnDataDate() {
		return this.lnDataDate;
	}

	/**
	 * 設定授信資料引進日期
	 * <p/>
	 * 100/11/11新增<br/>
	 * 帳務資料日期
	 **/
	public void setLnDataDate(Date value) {
		this.lnDataDate = value;
	}

	/**
	 * 取得額度幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 */
	public String getTotQuotaCurr() {
		return this.totQuotaCurr;
	}

	/**
	 * 設定額度幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 **/
	public void setTotQuotaCurr(String value) {
		this.totQuotaCurr = value;
	}

	/**
	 * 取得額度合計
	 * <p/>
	 * 100/11/11新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 */
	public BigDecimal getTotQuota() {
		return this.totQuota;
	}

	/**
	 * 設定額度合計
	 * <p/>
	 * 100/11/11新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 **/
	public void setTotQuota(BigDecimal value) {
		this.totQuota = value;
	}

	/**
	 * 取得前日結欠餘額幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 */
	public String getTotBalCurr() {
		return this.totBalCurr;
	}

	/**
	 * 設定前日結欠餘額幣別
	 * <p/>
	 * 100/11/11新增<br/>
	 * 國內：TWD<br/>
	 * 海外：本位幣
	 **/
	public void setTotBalCurr(String value) {
		this.totBalCurr = value;
	}

	/**
	 * 取得前日結欠餘額合計
	 * <p/>
	 * 100/11/11新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 */
	public BigDecimal getTotBal() {
		return this.totBal;
	}

	/**
	 * 設定前日結欠餘額合計
	 * <p/>
	 * 100/11/11新增<br/>
	 * 折算合計值(單位：仟元)<br/>
	 * 國內：(匯率檔：MIS.RATETBL)<br/>
	 * 海外：(匯率檔DW_FXRTHOVS)
	 **/
	public void setTotBal(BigDecimal value) {
		this.totBal = value;
	}

	/**
	 * 取得團貸所有明細EXCEL檔檔案位置
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 */
	public String getBranchExcelFile() {
		return this.branchExcelFile;
	}

	/**
	 * 設定團貸所有明細EXCEL檔檔案位置
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)
	 **/
	public void setBranchExcelFile(String value) {
		this.branchExcelFile = value;
	}

	/**
	 * 取得團貸所有明細EXCEL檔產生時間
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)<br/>
	 * 格式：UserName(CurrentTime)
	 */
	public String getBranchExcelDate() {
		return this.branchExcelDate;
	}

	/**
	 * 設定團貸所有明細EXCEL檔產生時間
	 * <p/>
	 * 100/11/11新增<br/>
	 * (團貸案)<br/>
	 * 格式：UserName(CurrentTime)
	 **/
	public void setBranchExcelDate(String value) {
		this.branchExcelDate = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/** 取得覆審意見_編製完成日期 **/
	public Timestamp getUpDate() {
		return this.upDate;
	}

	/** 設定覆審意見_編製完成日期 **/
	public void setUpDate(Timestamp value) {
		this.upDate = value;
	}

	/**
	 * 取得覆審意見_覆審情形
	 * <p/>
	 * 1.覆審正常<br/>
	 * 2.異常情形，應改善或注意事項
	 */
	public String getConFlag() {
		return this.conFlag;
	}

	/**
	 * 設定覆審意見_覆審情形
	 * <p/>
	 * 1.覆審正常<br/>
	 * 2.異常情形，應改善或注意事項
	 **/
	public void setConFlag(String value) {
		this.conFlag = value;
	}

	/**
	 * 取得覆審意見_異常情形，應改善或注意事項
	 */
	public String getCondition() {
		return this.condition;
	}

	/**
	 * 設定覆審意見_異常情形，應改善或注意事項
	 **/
	public void setCondition(String value) {
		this.condition = value;
	}

	/**
	 * 取得受檢單位洽辦情形
	 * <p/>
	 * 128個全型字
	 */
	public String getBranchComm() {
		return this.branchComm;
	}

	/**
	 * 設定受檢單位洽辦情形
	 * <p/>
	 * 128個全型字
	 **/
	public void setBranchComm(String value) {
		this.branchComm = value;
	}

	/**
	 * 取得無擔逾期戶是否需半年再審
	 */
	public String getOverdueYN() {
		return overdueYN;
	}

	/**
	 * 設定無擔逾期戶是否需半年再審
	 */
	public void setOverdueYN(String overdueYN) {
		this.overdueYN = overdueYN;
	}

	/**
	 * 取得異常通報日期
	 */
	public Date getUckdDt() {
		return uckdDt;
	}

	/**
	 * 設定異常通報日期
	 */
	public void setUckdDt(Date uckdDt) {
		this.uckdDt = uckdDt;
	}

	/**
	 * 取得記錄次數
	 */
	public String getUckdLine() {
		return uckdLine;
	}

	/**
	 * 設定記錄次數
	 */
	public void setUckdLine(String uckdLine) {
		this.uckdLine = uckdLine;
	}

	/**
	 * 取得異常通報代碼
	 */
	public String getMdFlag() {
		return mdFlag;
	}

	/**
	 * 設定異常通報代碼
	 */
	public void setMdFlag(String mdFlag) {
		this.mdFlag = mdFlag;
	}

	/**
	 * 取得 已累計8_1次數，並搬移8_1_flag、8_1_flag_o
	 */
	public String getUp8_1() {
		return up8_1;
	}

	/**
	 * 設定 已累計8_1次數，並搬移8_1_flag、8_1_flag_o
	 */
	public void setUp8_1(String up8_1) {
		this.up8_1 = up8_1;
	}

	/**
	 * 取得 已逾到期日 0:無 ; 4:4月 ; 12:1年
	 */
	public String getOverdueP() {
		return overdueP;
	}

	/**
	 * 設定 已逾到期日 0:無 ; 4:4月 ; 12:1年
	 */
	public void setOverdueP(String overdueP) {
		this.overdueP = overdueP;
	}

	/**
	 * 取得仲介ID
	 */
	public String getComId() {
		return comId;
	}

	/**
	 * 設定仲介ID
	 */
	public void setComId(String comId) {
		this.comId = comId;
	}

	/**
	 * 取得仲介重複碼
	 */
	public String getComDup() {
		return comDup;
	}

	/**
	 * 設定仲介重複碼
	 */
	public void setComDup(String comDup) {
		this.comDup = comDup;
	}

	/**
	 * 取得仲介名稱
	 */
	public String getComName() {
		return comName;
	}

	/**
	 * 設定仲介名稱
	 */
	public void setComName(String comName) {
		this.comName = comName;
	}

	/**
	 * 取得買方ID
	 */
	public String getBuyerId() {
		return buyerId;
	}

	/**
	 * 設定買方ID
	 */
	public void setBuyerId(String buyerId) {
		this.buyerId = buyerId;
	}

	/**
	 * 取得買方重複碼
	 */
	public String getBuyerDup() {
		return buyerDup;
	}

	/**
	 * 設定買方重複碼
	 */
	public void setBuyerDup(String buyerDup) {
		this.buyerDup = buyerDup;
	}

	/**
	 * 取得買方名稱
	 */
	public String getBuyerName() {
		return buyerName;
	}

	/**
	 * 設定買方名稱
	 */
	public void setBuyerName(String buyerName) {
		this.buyerName = buyerName;
	}

	/**
	 * 取得價金履保控管類別
	 */
	public String getLnf660_loan_class() {
		return lnf660_loan_class;
	}

	/**
	 * 設定價金履保控管類別
	 */
	public void setLnf660_loan_class(String lnf660_loan_class) {
		this.lnf660_loan_class = lnf660_loan_class;
	}

	/**
	 * 取得存款(代收)帳號
	 */
	public String getPbAcct() {
		return pbAcct;
	}

	/**
	 * 設定存款(代收)帳號
	 */
	public void setPbAcct(String pbAcct) {
		this.pbAcct = pbAcct;
	}

	/**
	 * 取得母戶額度序號
	 */
	public String getLnf660_m_contract() {
		return lnf660_m_contract;
	}

	/**
	 * 設定母戶額度序號
	 */
	public void setLnf660_m_contract(String lnf660_m_contract) {
		this.lnf660_m_contract = lnf660_m_contract;
	}

	/** 取得覆審報告表格式{A一般/B實地覆審} **/
	public String getDocFmt() {
		return docFmt;
	}

	/** 設定覆審報告表格式{A一般/B實地覆審} **/
	public void setDocFmt(String docFmt) {
		this.docFmt = docFmt;
	}

	/** 取得上次土建融實地覆審日 **/
	public Date getLastRealDt() {
		return lastRealDt;
	}

	/** 設定上次土建融實地覆審日 **/
	public void setLastRealDt(Date lastRealDt) {
		this.lastRealDt = lastRealDt;
	}

	/** 取得帳務含土建融{Y/N/O} 企金引ELF412,消金由c241m01b.lnType/syndType判斷 **/
	public String getRealCkFg() {
		return realCkFg;
	}

	/** 設定帳務含土建融{Y/N/O} 企金引ELF412,消金由c241m01b.lnType/syndType判斷 **/
	public void setRealCkFg(String realCkFg) {
		this.realCkFg = realCkFg;
	}

	/** 取得不覆審說明 **/
	public String getNckdDetail() {
		return nckdDetail;
	}

	/** 設定不覆審說明 **/
	public void setNckdDetail(String nckdDetail) {
		this.nckdDetail = nckdDetail;
	}

	/** 取得履保母戶幣別 **/
	public String getLnf660_m_curr() {
		return lnf660_m_curr;
	}

	/** 設定履保母戶幣別 **/
	public void setLnf660_m_curr(String lnf660_m_curr) {
		this.lnf660_m_curr = lnf660_m_curr;
	}

	/** 取得履保母戶額度金額 **/
	public BigDecimal getLnf660_m_fact() {
		return lnf660_m_fact;
	}

	/** 設定履保母戶額度金額 **/
	public void setLnf660_m_fact(BigDecimal lnf660_m_fact) {
		this.lnf660_m_fact = lnf660_m_fact;
	}

	/** 取得履保母戶起日 **/
	public Date getLnf660_m_begDate() {
		return lnf660_m_begDate;
	}

	/** 設定履保母戶起日 **/
	public void setLnf660_m_begDate(Date lnf660_m_begDate) {
		this.lnf660_m_begDate = lnf660_m_begDate;
	}

	/** 取得履保母戶迄日 **/
	public Date getLnf660_m_endDate() {
		return lnf660_m_endDate;
	}

	/** 設定履保母戶迄日 **/
	public void setLnf660_m_endDate(Date lnf660_m_endDate) {
		this.lnf660_m_endDate = lnf660_m_endDate;
	}

	/** 取得履保母戶科目 **/
	public String getLnf660_m_subj() {
		return lnf660_m_subj;
	}

	/** 設定履保母戶科目 **/
	public void setLnf660_m_subj(String lnf660_m_subj) {
		this.lnf660_m_subj = lnf660_m_subj;
	}

	/**
	 * 覆審意見_三、目前評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	public String getCurRate() {
		return this.curRate;
	}

	/**
	 * 覆審意見_三、目前評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	public void setCurRate(String value) {
		this.curRate = value;
	}

	/**
	 * 覆審意見_三、建議評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	public String getSugRate() {
		return this.sugRate;
	}

	/**
	 * 覆審意見_三、建議評等
	 * <p/>
	 * 107/07增加<br/>
	 */
	public void setSugRate(String value) {
		this.sugRate = value;
	}

	/** 防杜代辦覆審_為代辦案件(Y/N) */
	public String getConFlag2A() {
		return conFlag2A;
	}

	/** 防杜代辦覆審_為代辦案件(Y/N) */
	public void setConFlag2A(String conFlag2A) {
		this.conFlag2A = conFlag2A;
	}

	/** 防杜代辦覆審_有與代辦業者掛勾(Y/N) */
	public String getConFlag2B() {
		return conFlag2B;
	}

	/** 防杜代辦覆審_有與代辦業者掛勾(Y/N) */
	public void setConFlag2B(String conFlag2B) {
		this.conFlag2B = conFlag2B;
	}

	/** 防杜代辦覆審_有其它違規或異常(Y/N) */
	public String getConFlag2C() {
		return conFlag2C;
	}

	/** 防杜代辦覆審_有其它違規或異常(Y/N) */
	public void setConFlag2C(String conFlag2C) {
		this.conFlag2C = conFlag2C;
	}

	/** 防杜代辦覆審_基準期間 */
	public String getPa_ym() {
		return pa_ym;
	}

	/** 防杜代辦覆審_基準期間 */
	public void setPa_ym(String pa_ym) {
		this.pa_ym = pa_ym;
	}

	/** 防杜代辦覆審_對象類別 */
	public String getPa_trg() {
		return pa_trg;
	}

	/** 防杜代辦覆審_對象類別 */
	public void setPa_trg(String pa_trg) {
		this.pa_trg = pa_trg;
	}

	/** 取得客戶逾期情形 查詢日 **/
	public Date getOvQryDt() {
		return this.ovQryDt;
	}

	/** 設定客戶逾期情形 查詢日 **/
	public void setOvQryDt(Date value) {
		this.ovQryDt = value;
	}

	/** 取得客戶逾期情形 本金逾期天數 **/
	public Integer getCapDays() {
		return this.capDays;
	}

	/** 取得客戶逾期情形 本金逾期天數 **/
	public void setCapDays(Integer value) {
		this.capDays = value;
	}

	/** 取得客戶逾期情形 本金逾期日 **/
	public Date getCapDt() {
		return this.capDt;
	}

	/** 設定客戶逾期情形 本金逾期日 **/
	public void setCapDt(Date value) {
		this.capDt = value;
	}

	/** 取得客戶逾期情形 本金資料日 **/
	public Date getCapDataDt() {
		return this.capDataDt;
	}

	/** 設定客戶逾期情形 本金資料日 **/
	public void setCapDataDt(Date value) {
		this.capDataDt = value;
	}

	/** 取得客戶逾期情形 利息逾期天數 **/
	public Integer getIntDays() {
		return this.intDays;
	}

	/** 取得客戶逾期情形 利息逾期天數 **/
	public void setIntDays(Integer value) {
		this.intDays = value;
	}

	/** 取得客戶逾期情形 利息逾期日 **/
	public Date getIntDt() {
		return this.intDt;
	}

	/** 設定客戶逾期情形 利息逾期日 **/
	public void setIntDt(Date value) {
		this.intDt = value;
	}

	/** 取得客戶逾期情形 利息資料日 **/
	public Date getIntDataDt() {
		return this.intDataDt;
	}

	/** 設定客戶逾期情形 利息資料日 **/
	public void setIntDataDt(Date value) {
		this.intDataDt = value;
	}

	/**
	 * 取得處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	public String getStatus() {
		return this.status;
	}

	/**
	 * 設定處理狀態
	 * <p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 **/
	public void setStatus(String value) {
		this.status = value;
	}

	/** 取得回傳結果原因 **/
	public String getReason() {
		return this.reason;
	}

	/** 設定回傳結果原因 **/
	public void setReason(String value) {
		this.reason = value;
	}

	public String getNeedPa() {
		return needPa;
	}

	public void setNeedPa(String needPa) {
		this.needPa = needPa;
	}

	/** 取得首次覆核時間 **/
	public Timestamp getFirstAccTime() {
		return this.firstAccTime;
	}
	/** 設定首次覆核時間 **/
	public void setFirstAccTime(Timestamp value) {
		this.firstAccTime = value;
	}
	/** 取得考核表(performance appraisal) - 版本 **/
	public String getPaVer() {
		return paVer;
	}
	/** 設定考核表(performance appraisal) - 版本 **/
	public void setPaVer(String paVer) {
		this.paVer = paVer;
	}
}
