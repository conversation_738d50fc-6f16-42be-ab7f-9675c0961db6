package com.mega.eloan.lms.base.service;

import java.util.Date;
import java.util.List;

import org.kordamp.json.JSONObject;

import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C120S01E;

/**
 * <pre>
 * 評分 Service
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
public interface ScoreService {

	/**
	 * 初始化
	 */
	void init();
	
	/**
	 * 取得評等相關資料
	 * @param custId
	 * @param dupNo
	 * @param versionG
	 * @param versionQ
	 * @return
	 */
	JSONObject getData(String custId, String dupNo, String versionG, String versionQ, String versionR, JSONObject input_factor);

	/**
	 * 房貸評分
	 * 
	 * @param type
	 * @param data
	 * @param Immediate
	 * @return
	 */
	JSONObject score(String type, JSONObject data, String varVer);
	
	@Deprecated
	JSONObject score(String type, JSONObject data, boolean immediate);

	/**
	 * 非房貸評分
	 * 
	 * 當調整主觀評等時，傳入的 type 是「違約機率」，回傳的結果除了 DR 的值以外
	 * 還多包含[合計變量得分...等欄位]但值不準確，所以後面搭配 setHouseLoanDR(...)
	 * 只將 DR 相關的欄位抓出
	 * @param type
	 * @param data
	 * @param Immediate
	 * @return
	 */
	JSONObject scoreNotHouseLoan(String type, JSONObject data, String varVer);
	
	JSONObject scoreCardLoan(String type, JSONObject data, String varVer);
	
	void setHouseLoanDR(JSONObject houseLoanDR, JSONObject target);
	void setNotHouseLoanDR(JSONObject notHouseLoanDR, JSONObject target);
	void setCardLoanDR(JSONObject notHouseLoanDR, JSONObject target);
	
	public void clear_unUsedColumn(C101S01G model);
	public void clear_unUsedColumn(C101S01Q model);
	public void clear_unUsedColumn(C101S01R model);
	
	public String get_Version_HouseLoan();
	public String get_Version_NotHouseLoan();
	public String get_Version_CardLoan();
	public boolean clsScore_inBufferPeriodQdateExpired(String tbl,String varVer, List<C120S01E> list);
	public boolean clsScore_inBufferPeriod(String tbl, String varVer);
	public Date clsScore_ActiveDate(String tbl, String varVer);
	
	JSONObject parse(Class clazz, String fileLoc);
	String determine(String name, JSONObject item, JSONObject data);
//	public JSONObject doScoreCalculate(String ModelType, String type, JSONObject data, String varVer);
	public boolean scoreDoubleTrack();
	
}
