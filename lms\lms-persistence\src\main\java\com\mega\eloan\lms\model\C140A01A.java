package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140A01A model.
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140A01A-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name="C140A01A", uniqueConstraints = @UniqueConstraint(columnNames ={"oid"}))
public class C140A01A extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	private Timestamp authTime;

	@Column(length=1)
	private String authType;

	@Column(length=3)
	private String authUnit;

	@Column(length=6)
	private String owner;

	@Column(length=3)
	private String ownUnit;

	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
            @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	public Timestamp getAuthTime() {
		return this.authTime;
	}

	public void setAuthTime(Timestamp authTime) {
		this.authTime = authTime;
	}

	public String getAuthType() {
		return this.authType;
	}

	public void setAuthType(String authType) {
		this.authType = authType;
	}
	
	public void setAuthType(DocAuthTypeEnum authType) {
		this.authType = authType.getCode();
	}

	public String getAuthUnit() {
		return this.authUnit;
	}

	public void setAuthUnit(String authUnit) {
		this.authUnit = authUnit;
	}

	public String getOwner() {
		return this.owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public String getOwnUnit() {
		return this.ownUnit;
	}

	public void setOwnUnit(String ownUnit) {
		this.ownUnit = ownUnit;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
}