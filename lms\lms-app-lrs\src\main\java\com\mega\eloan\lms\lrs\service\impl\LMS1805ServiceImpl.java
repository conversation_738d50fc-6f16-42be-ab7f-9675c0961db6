/* 

 * LMS1805ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.model.Bstbl;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.BstblService;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L170A01ADao;
import com.mega.eloan.lms.dao.L170M01ADao;
import com.mega.eloan.lms.dao.L170M01BDao;
import com.mega.eloan.lms.dao.L170M01EDao;
import com.mega.eloan.lms.dao.L170M01FDao;
import com.mega.eloan.lms.dao.L180A01ADao;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.L180M01BDao;
import com.mega.eloan.lms.dao.L180M01CDao;
import com.mega.eloan.lms.dao.L180M01ZDao;
import com.mega.eloan.lms.dao.L181M01ADao;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.Dw_elf411ovsService;
import com.mega.eloan.lms.eloandb.service.Dw_elf412ovsService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.lrs.pages.LMS1705M01Page;
import com.mega.eloan.lms.lrs.pages.LMS1805M01Page;
import com.mega.eloan.lms.lrs.pages.LMS1805V01Page;
import com.mega.eloan.lms.lrs.service.LMS170502Service;
import com.mega.eloan.lms.lrs.service.LMS1705Service;
import com.mega.eloan.lms.lrs.service.LMS1805Service;
import com.mega.eloan.lms.lrs.service.LMS1835Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisLNFE0851Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.mfaloan.service.impl.MisELCUS25ServiceImpl;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L170A01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L180A01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01C;
import com.mega.eloan.lms.model.L180M01Z;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

@Service
public class LMS1805ServiceImpl extends AbstractCapService implements
		LMS1805Service {

	@Resource
	LMSService lmsService;

	@Resource
	LMS1835Service service1835;

	@Autowired
	DocFileService fileService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L180A01ADao l180a01aDao;

	@Resource
	L180M01ADao l180m01aDao;

	@Resource
	L180M01BDao l180m01bDao;

	@Resource
	L180M01CDao l180m01cDao;

	@Resource
	L180M01ZDao l180m01zDao;

	@Resource
	L181M01ADao l181m01aDao;

	@Resource
	L170A01ADao l170a01aDao;

	@Resource
	L170M01ADao l170m01aDao;

	@Resource
	L170M01BDao l170m01bDao;

	@Resource
	L170M01EDao l170m01eDao;

	@Resource
	L170M01FDao l170m01fDao;

	@Resource
	DocFileDao docfileDao;

	@Resource
	FlowService flowService;

	@Resource
	DocLogService docLogService;

	@Resource
	LMS1705Service lms1705Service;

	@Resource
	LMS170502Service lms170502Service;

	@Resource
	NumberService numberService;

	@Resource
	BranchService branch;

	@Resource
	MisLNFE0851Service misLNFE0851Service;

	@Resource
	MisELCUS25ServiceImpl misELCUS25ServiceImpl;

	@Resource
	MisCustdataService LmsCustdataService;

	@Resource
	DwLnquotovService dwLnquotovService;

	@Resource
	Lms412Service lms412Service;

	@Resource
	Dw_elf411ovsService dwelf411ovs;

	@Resource
	Dw_elf412ovsService dwelf412ovs;

	@Resource
	MisdbBASEService misBASEService;

	@Resource
	TempDataService tempDataService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BstblService bstblService;

	@Resource
	RetrialService retrialService;

	private static Logger logger = LoggerFactory
			.getLogger(LMS1805ServiceImpl.class);

	private final String MINDATE = "0001-01-01";

	private final String MAXDATE = "9999-12-31";

	private final String 系統產生人員 = "SYS";

	public void startFlow(String mainOid) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (user == null) {
			flowService.start("LMS1805Flow", mainOid, 系統產生人員, "");
		} else {
			flowService.start("LMS1805Flow", mainOid, user.getUserId(),
					user.getUnitNo());
		}
	}

	/**
	 * 跑flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType) throws Throwable {
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			L180M01A l180m01a = (L180M01A) model;
			l180m01aDao.save(l180m01a);
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				inst.setAttribute("result", resultType ? "核定" : "退回");
			}
			inst.next();
		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public void deleteL180m01aList(String[] oids) {
		for (String oid : oids) {
			L180M01A l180m01a = findModelByOid(L180M01A.class, oid);
			l180m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			save(l180m01a);
			// String mainId = l180m01a.getMainId();
			// deleteL180m01bList(mainId);
			// deleteL180m01cList(mainId);
			// delete(L180M01A.class, l180m01a.getOid());
		}
	}

	@Override
	public List<L180M01B> fingL180m01bByCustId(String MainId, String CustId,
			String dupno, String ctlType) {
		return l180m01bDao.findByIndex01(MainId, CustId, dupno, ctlType);
	}

	@Override
	public List<L180M01C> fingL180m01cByCustId(String mainId, String custId,
			String dupNo, String ctlType) {

		return l180m01cDao.findByUniqueKey2(mainId, custId, dupNo, ctlType);
	}

	@Override
	public void deleteL180m01bList(String mainId, String custId, String dupNo,
			String ctlType) {
		l180m01bDao.deleteL180M01BList(mainId, custId, dupNo, ctlType);
	}

	@Override
	public List<L180M01B> findL180m01bByMainId(String MainId, String ctlType) {
		return l180m01bDao.findByMainId(MainId, ctlType);
	}

	@Override
	public void deleteL180m01cList(String mainId, String custId, String dupNo,
			String ctlType) {
		l180m01cDao.deleteL180M01CList(mainId, custId, dupNo, ctlType);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L180M01A.class) {
			return (T) l180m01aDao.find(oid);
		} else if (clazz == L180M01B.class) {
			return (T) l180m01bDao.find(oid);
		} else if (clazz == L180M01C.class) {
			return (T) l180m01cDao.find(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L180M01A.class) {
			return l180m01aDao.findPage(search);
		} else if (clazz == L180M01B.class) {
			return l180m01bDao.findPage(search);
		} else if (clazz == L180M01C.class) {
			return l180m01cDao.findPage(search);
		} else if (clazz == L180M01Z.class) {
			return l180m01zDao.findPage(search);
		} else if (clazz == DocFile.class) {
			return docfileDao.findPage(search);
		} else if (clazz == L120M01A.class) {
			return l120m01aDao.findPage(search);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L180M01A) {
					if (user != null) {
						((L180M01A) model).setUpdater(user.getUserId());
						((L180M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						if (((L180M01A) model).getCreator() == null) {
							((L180M01A) model).setCreator(user.getUserId());
							((L180M01A) model).setCreateTime(CapDate
									.getCurrentTimestamp());
						}
					}
					((L180M01A) model).setRandomCode(IDGenerator
							.getRandomCode());
					l180m01aDao.save(((L180M01A) model));
					// UPGRADE:l180m01a的變更無法成功存至DB，先以flush()處理
					l180m01aDao.flush();
					if (user != null) {
						docLogService.record(((L180M01A) model).getOid(),
								DocLogEnum.SAVE);
					}
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L180M01A) model)
								.getMainId());
					}
				} else if (model instanceof L180M01B) {
					if (user != null) {
						((L180M01B) model).setUpdater(user.getUserId());
						if (((L180M01B) model).getCreator() == null) {
							((L180M01B) model).setCreator(user.getUserId());
						}
					}
					((L180M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					if (((L180M01B) model).getCreator() == null) {
						((L180M01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l180m01bDao.save(((L180M01B) model));
					if (user != null) {
						docLogService.record(((L180M01B) model).getOid(),
								DocLogEnum.SAVE);
					}
				} else if (model instanceof L180M01C) {
					if (user != null) {
						((L180M01C) model).setUpdater(user.getUserId());
						if (((L180M01C) model).getCreator() == null) {
							((L180M01C) model).setCreator(user.getUserId());
						}
					}
					((L180M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());

					if (((L180M01C) model).getCreator() == null) {
						((L180M01C) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					l180m01cDao.save(((L180M01C) model));
					if (user != null) {
						docLogService.record(((L180M01C) model).getOid(),
								DocLogEnum.SAVE);
					}
				} else if (model instanceof L180A01A) {
					l180a01aDao.save(((L180A01A) model));
					if (user != null) {
						docLogService.record(((L180A01A) model).getOid(),
								DocLogEnum.SAVE);
					}
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public void delete(Class clazz, String oid) {
		if (clazz == L180M01A.class) {
			L180M01A l180m01a = l180m01aDao.findByOid(oid);
			l180m01aDao.delete(l180m01a);
			docLogService.record(l180m01a.getOid(), DocLogEnum.DELETE);
		} else if (clazz == L180M01B.class) {
			L180M01B l180m01b = l180m01bDao.findByOid(oid);
			l180m01bDao.delete(l180m01b);
			docLogService.record(l180m01b.getOid(), DocLogEnum.DELETE);
		} else if (clazz == L180M01C.class) {
			L180M01C l180m01c = l180m01cDao.findByOid(oid);
			l180m01cDao.delete(l180m01c);
			docLogService.record(l180m01c.getOid(), DocLogEnum.DELETE);
		}
	}

	/**
	 * 產生名單sql
	 * 
	 * @param branchId
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List elf412Sql(String branchId) {

		return this.lms412Service.findLms412JoinCust(branchId);
	}

	@Override
	public String[] cauculateDate(Date dataDate, Map<String, Object> dataMap,
			int mode) {
		// Date basedate = dataDate;
		Date nextDate = Util.parseDate(MAXDATE);
		String elf412Nckdflag = Util.trim(dataMap.get("NCKDFLAG"));

		Date elf412Lrdate = (Date) dataMap.get("LRDATE");

		String elf412Maincust = Util.trim(dataMap.get("MAINCUST"));
		String elf412Mowtbl1 = Util.trim(dataMap.get("MOWTBL1"));
		String elf412Mowtype = Util.trim(dataMap.get("MOWTYPE"));
		String elf412Crdttbl = Util.trim(dataMap.get("CRDTTBL"));
		String elf412Newdate = Util.trim(dataMap.get("NEWDATE"));
		String elf412Mdflag = Util.trim(dataMap.get("MDFLAG"));
		String elf412Rckdline = Util.trim(dataMap.get("RCKDLINE"));
		Date elf412Mddt = (Date) dataMap.get("MDDT");
		String elf412Uckdline = Util.trim(dataMap.get("UCKDLINE"));
		Date elf412Uckddt = (Date) dataMap.get("UCKDDT");

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		String elf412IsAllNew = Util.trim(dataMap.get("ISALLNEW"));
		String elf412Branch = Util.trim(dataMap.get("BRANCH"));

		String[] returnVal = { MAXDATE, elf412Rckdline };
		boolean shouldCTL = false;
		/**
		 * <pre>
		 * 不覆審案件1 2 3 4 5 9 依據上次覆審日一年後才要出來 
		 * 1.本行或同業主辦之聯貸案件，非擔任管理行。 
		 * 2.十成定存。
		 * 3.純進出押戶。 
		 * 4.對政府或政府所屬機關、學校之授信案件。 
		 * 5.拆放同業或對同業之融通。 
		 * 6.已列報為逾期放款或轉列催收款項之案件。
		 * 7.銷戶 
		 * 8.本次暫不覆審 
		 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。
		 * </pre>
		 **/
		if (mode != 3 && !elf412Nckdflag.isEmpty()
				&& !"8".equals(elf412Nckdflag)) {
			if ("1".equals(elf412Nckdflag) || "2".equals(elf412Nckdflag)
					|| "3".equals(elf412Nckdflag) || "4".equals(elf412Nckdflag)
					|| "5".equals(elf412Nckdflag) || "9".equals(elf412Nckdflag)) {
				if (elf412Lrdate == null
						|| CapDate.parseDate(MINDATE).equals(elf412Lrdate)) {
					// returnVal = changeDate(basedate,
					// CapDate.getCurrentTimestamp(), nextDate, "A",
					// elf412Rckdline, mode);
					// nextDate = Util.parseDate(returnVal[0]);
					shouldCTL = true;
					// elf412Rckdline = returnVal[1];
				} else {
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(elf412Lrdate);
					calendar.add(Calendar.MONTH, 12);
					Date CompareDate = calendar.getTime();

					returnVal = changeDate(dataDate, CompareDate, nextDate,
							"A", elf412Rckdline, mode);
					nextDate = Util.parseDate(returnVal[0]);
					// elf412Rckdline = returnVal[1];
				}
			}
		}

		// A.一年覆審一次
		if (!(elf412Lrdate == null || CapDate.parseDate(MINDATE).equals(
				elf412Lrdate))) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(elf412Lrdate);
			calendar.add(Calendar.MONTH, 12);
			Date CompareDate = calendar.getTime();

			returnVal = changeDate(dataDate, CompareDate, nextDate, "A",
					elf412Rckdline, mode);
			nextDate = Util.parseDate(returnVal[0]);
			// elf412Rckdline = returnVal[1];
		} else {
			// returnVal = changeDate(basedate, new Date(),
			// nextDate, "A", elf412Rckdline, mode);
			// nextDate = Util.parseDate(returnVal[0]);
			shouldCTL = true;
			// elf412Rckdline = returnVal[1];
		}

		// B.主要授信戶符合特定評等 6個月
		if ("Y".equals(elf412Maincust)) {
			boolean tBADMOW = false;
			/**
			 * <pre>
			 * elf412Mowtbl1   信用模型評等
			 * 10	差(Poor)
			 * 11	極差(Very poor)
			 * 12	觀察清單(Watch list)
			 * 13	觀察清單(Watch list)
			 * DF	違約(Default)
			 * </pre>
			 **/
			if (("9".equals(elf412Mowtbl1) && "1".equals(elf412Mowtype))
					|| ("10".equals(elf412Mowtbl1)
							|| "11".equals(elf412Mowtbl1)
							|| "12".equals(elf412Mowtbl1)
							|| "13".equals(elf412Mowtbl1) || "DF"
							.equals(elf412Mowtbl1))) {
				tBADMOW = true;
			}
			if (tBADMOW
					|| ((elf412Crdttbl == null || elf412Crdttbl.isEmpty()) && (elf412Mowtbl1 == null || elf412Mowtbl1
							.isEmpty()))
					|| (!elf412Crdttbl.isEmpty() && elf412Crdttbl.charAt(0) >= 'D')) {
				if (elf412Lrdate != null
						&& !CapDate.parseDate(MINDATE).equals(elf412Lrdate)) {
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(elf412Lrdate);
					calendar.add(Calendar.MONTH, 6);
					Date CompareDate = calendar.getTime();

					returnVal = changeDate(dataDate, CompareDate, nextDate,
							"B", elf412Rckdline, mode);
					nextDate = Util.parseDate(returnVal[0]);
					// elf412Rckdline = returnVal[1];
				}
			}
		}

		// C.新戶、增額戶下個月出現
		if (!Util.isEmpty(Util.trim(elf412Newdate))) {
			int year = Integer.parseInt(elf412Newdate.substring(0, 4));
			if (year < 1000) {
				year = year + 1911;
			}
			StringBuilder newDate = new StringBuilder();
			newDate.append(String.valueOf(year));
			newDate.append("-");
			String month = elf412Newdate.substring(4);
			if (Util.isInteger(month) && Integer.valueOf(month) < 13) {
				newDate.append(elf412Newdate.substring(4));
				newDate.append("-01");

				boolean isRckdLineIEffective = retrialService
						.isRckdLine_I_Effective(CapDate.getCurrentTimestamp());

				String needRckdLine = LrsUtil.getNewAddRckdLineForElf412(
						elf412IsAllNew, elf412Branch);

				if (mode == 1) {
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(Util.parseDate(newDate.toString()));
					calendar.add(Calendar.MONTH, 1);
					Date CompareDate = calendar.getTime();

					if (isRckdLineIEffective) {
						returnVal = changeDate(dataDate, CompareDate, nextDate,
								needRckdLine, elf412Rckdline, mode);
					} else {
						returnVal = changeDate(dataDate, CompareDate, nextDate,
								"C", elf412Rckdline, mode);
					}

					nextDate = Util.parseDate(returnVal[0]);
					// elf412Rckdline = returnVal[1];
				} else {

					Calendar calendar = Calendar.getInstance();
					calendar.setTime(Util.parseDate(newDate.toString()));

					// 判斷首次往來之新貸戶且非"079"分行則為"I",否則為"C"
					// J-108-0078_05097_B1001 Web
					// e-Loan企金授信覆審系統修改首次往來之新授信戶應辦理覆審之期限
					Date CompareDate = calendar.getTime();
					if (isRckdLineIEffective) {

						if (Util.equals(needRckdLine, "I")) {
							calendar.add(Calendar.MONTH, 3);
						} else {
							calendar.add(Calendar.MONTH, 6);
						}
						returnVal = changeDate(dataDate, CompareDate, nextDate,
								needRckdLine, elf412Rckdline, mode);
					} else {
						calendar.add(Calendar.MONTH, 6);
						returnVal = changeDate(dataDate, CompareDate, nextDate,
								"C", elf412Rckdline, mode);
					}

					nextDate = Util.parseDate(returnVal[0]);
					// elf412Rckdline = returnVal[1];
				}
			}
		}

		// D.異常戶已三個月覆審過- 爾後半年覆審一次。
		// F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。
		if (!elf412Mdflag.isEmpty()
				&& !("E".equals(elf412Rckdline) || "G".equals(elf412Rckdline))) {
			if (elf412Lrdate == null
					|| CapDate.parseDate(MINDATE).equals(elf412Lrdate)) {
				// returnVal = changeDate(basedate, new Date(),
				// nextDate, elf412Rckdline, elf412Rckdline, mode);
				// nextDate = Util.parseDate(returnVal[0]);
				shouldCTL = true;
				// elf412Rckdline = returnVal[1];
			} else {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(elf412Lrdate);
				calendar.add(Calendar.MONTH, 6);
				Date CompareDate = calendar.getTime();

				returnVal = changeDate(dataDate, CompareDate, nextDate,
						elf412Rckdline, elf412Rckdline, mode);
				nextDate = Util.parseDate(returnVal[0]);
				// elf412Rckdline = returnVal[1];
			}
		}
		// 加強檢核 異常通報有日期 但是覆審周期不為 E、G、D、F
		if (!elf412Mdflag.isEmpty()
				&& !("E".equals(elf412Rckdline) || "G".equals(elf412Rckdline)
						|| "D".equals(elf412Rckdline) || "F"
						.equals(elf412Rckdline))) {
			if (elf412Lrdate == null
					|| CapDate.parseDate(MINDATE).equals(elf412Lrdate)) {
				// returnVal = changeDate(basedate, new Date(),
				// nextDate, elf412Rckdline, elf412Rckdline, mode);
				// nextDate = Util.parseDate(returnVal[0]);
				shouldCTL = true;
				// elf412Rckdline = returnVal[1];
			} else {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(elf412Lrdate);
				calendar.add(Calendar.MONTH, 6);
				Date CompareDate = calendar.getTime();

				returnVal = changeDate(dataDate, CompareDate, nextDate,
						elf412Rckdline, elf412Rckdline, mode);
				nextDate = Util.parseDate(returnVal[0]);
				// elf412Rckdline = returnVal[1];
			}
		}

		// E.首次通報之異常戶。（必需在首次通報日後3月內覆審）
		// G.首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）
		if (!elf412Mdflag.isEmpty() && elf412Mddt != null
				&& !CapDate.parseDate(MINDATE).equals(elf412Mddt)) {
			if ("E".equals(elf412Rckdline) || "G".equals(elf412Rckdline)) {
				if (mode == 1) {
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(elf412Mddt);
					calendar.add(Calendar.MONTH, 1);
					Date CompareDate = calendar.getTime();

					returnVal = changeDate(dataDate, CompareDate, nextDate,
							elf412Rckdline, elf412Rckdline, mode);
					nextDate = Util.parseDate(returnVal[0]);
					// elf412Rckdline = returnVal[1];
				} else {
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(elf412Mddt);
					calendar.add(Calendar.MONTH, 3);
					Date CompareDate = calendar.getTime();

					returnVal = changeDate(dataDate, CompareDate, nextDate,
							elf412Rckdline, elf412Rckdline, mode);
					nextDate = Util.parseDate(returnVal[0]);
					// elf412Rckdline = returnVal[1];
				}
			}
		}

		// H：主管機關指定覆審案件。
		if ("Y".equals(elf412Uckdline)) {
			if (elf412Uckddt == null) {
				elf412Uckddt = CapDate.parseDate(MINDATE);
			}
			if (elf412Uckddt == null || elf412Uckddt.after(elf412Lrdate)) {
				if (!(CapDate.parseDate(MINDATE).equals(elf412Uckddt) || elf412Uckddt == null)) {
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(elf412Uckddt);
					calendar.add(Calendar.MONTH, 6);
					Date CompareDate = calendar.getTime();

					returnVal = changeDate(dataDate, CompareDate, nextDate,
							"H", elf412Rckdline, mode);
					nextDate = Util.parseDate(returnVal[0]);
					// elf412Rckdline = returnVal[1];
				} else {
					// returnVal = changeDate(basedate, new Date(), nextDate,
					// "H",
					// elf412Rckdline, mode);
					// nextDate = Util.parseDate(returnVal[0]);
					shouldCTL = true;
					// elf412Rckdline = returnVal[1];
				}
			} else {
				if (elf412Lrdate == null
						|| CapDate.parseDate(MINDATE).equals(elf412Lrdate)) {
					// returnVal = changeDate(basedate,
					// new Date(), nextDate, "H",
					// elf412Rckdline, mode);
					// nextDate = Util.parseDate(returnVal[0]);
					shouldCTL = true;
					// elf412Rckdline = returnVal[1];
				} else {
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(elf412Lrdate);
					calendar.add(Calendar.MONTH, 6);
					Date CompareDate = calendar.getTime();

					returnVal = changeDate(dataDate, CompareDate, nextDate,
							"H", elf412Rckdline, mode);
					nextDate = Util.parseDate(returnVal[0]);
					// elf412Rckdline = returnVal[1];
				}
			}
		}
		//
		if (nextDate.equals(CapDate.parseDate(MAXDATE)) && shouldCTL) {
			if (mode == 3) {
				returnVal[0] = TWNDate.toAD(CapDate.getCurrentTimestamp());
			} else {
				returnVal[0] = TWNDate.toAD(CapDate.parseDate(CapDate.addMonth(
						TWNDate.toAD(dataDate).replace("-", ""), -1)));
			}
		}
		return returnVal;
	}

	public String[] cauculateDate(Date dataDate, Map<String, Object> dataMap) {
		return cauculateDate(dataDate, dataMap, 1);
	}

	/**
	 * 比較週期 mode = 3 => 維護覆審名單
	 * 
	 * @param basedate
	 * @param CompareDate
	 * @param NextDate
	 * @param RCkdLine
	 * @param exRCkdLine
	 * @return
	 */

	public String[] changeDate(Date basedate, Date CompareDate, Date NextDate,
			String rckdLine, String exRCkdLine, int mode) {
		String[] changeRCkdLine = {
				CapDate.formatDate(NextDate,
						UtilConstants.DateFormat.YYYY_MM_DD), exRCkdLine };
		if (mode != 3) {
			if (CompareDate.before(basedate)) {
				if (CompareDate.before(NextDate)) {
					NextDate = CompareDate;
					changeRCkdLine[0] = CapDate.formatDate(NextDate,
							UtilConstants.DateFormat.YYYY_MM_DD);
					changeRCkdLine[1] = rckdLine;
				}
			}
		} else {
			if (CompareDate.before(NextDate)) {
				NextDate = CompareDate;
				changeRCkdLine[0] = CapDate.formatDate(NextDate,
						UtilConstants.DateFormat.YYYY_MM_DD);
				changeRCkdLine[1] = rckdLine;
			}
		}
		return changeRCkdLine;
	}

	@Override
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public boolean updateUnusual(String branch) {
		List<?> rows = importDataSql(branch);

		Iterator<?> lnfe0851It = rows.iterator();
		while (lnfe0851It.hasNext()) {
			Map<String, Object> dataMap = (Map) lnfe0851It.next();

			String lms412CustId = Util.trim((String) dataMap.get("CUSTID"));
			String lms412DupNo = Util.trim((String) dataMap.get("DUPNO"));
			String lms412Mdflag = Util.trim((String) dataMap.get("MDFLAG"));
			String lms412Mddt = null;
			if (((Date) dataMap.get("MDDT")) != null) {
				lms412Mddt = Util.addZeroWithValue(
						TWNDate.toAD((Date) dataMap.get("MDDT")), 10);
			}
			String lms412Process = Util.trim((String) dataMap.get("PROCESS"));
			String lms412Canceldt = null;
			if (((Date) dataMap.get("CANCELDT")) != null) {
				lms412Mddt = Util.addZeroWithValue(
						TWNDate.toAD((Date) dataMap.get("CANCELDT")), 10);
			}
			if (lms412Canceldt == null || lms412Canceldt.isEmpty()
					|| MINDATE.equals(lms412Canceldt)) {
				String[] lnfe0851 = importLnfe0851(branch, lms412CustId,
						lms412DupNo, lms412Mdflag, lms412Mddt, lms412Process);
				lms412Mdflag = lnfe0851[0];
				lms412Mddt = lnfe0851[1];
				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				lms412Process = Util
						.truncateString(Util.trim(lnfe0851[2]), 200);
			}
			try {
				this.lms412Service.saveLms412updateMdflag(lms412Mdflag,
						lms412Mddt, lms412Process, lms412CustId, lms412DupNo,
						branch);
			} catch (Exception e) {
				logger.error("LMS1805ServiceImpl updateUnusual EXCEPTION!!", e);
				return false;
			}
		}
		return true;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public boolean produceList(Date dataDate, String branch, String createBy,
			String creator) {
		ISearch isearch = l180m01aDao.createSearchTemplete();
		isearch.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", creator);
		isearch.addSearchModeParameters(SearchMode.EQUALS, "branchId", branch);
		isearch.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDate);
		isearch.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		List<L180M01A> deletemodel = l180m01aDao.find(isearch);

		// 檢查是否有這份名單
		for (int i = 0; i < deletemodel.size(); i++) {
			L180M01A l180m01a = deletemodel.get(i);
			if (RetrialDocStatusEnum.待覆核.getCode().equals(
					l180m01a.getDocStatus())
			// || RetrialDocStatusEnum.已核准.getCode().equals(
			// l180m01a.getDocStatus())
			// || RetrialDocStatusEnum.已產生覆審名單報告檔.getCode().equals(
			// l180m01a.getDocStatus())
			) {
				return false;
			} else if (RetrialDocStatusEnum.編製中.getCode().equals(
					l180m01a.getDocStatus())) {
				l180m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				l180m01aDao.save(l180m01a);
			}
		}

		try {
			// 產生名單
			L180M01A cust = new L180M01A();
			cust.setMainId(IDGenerator.getUUID());
			cust.setTypCd(TypCdEnum.海外.getCode());
			cust.setRandomCode(IDGenerator.getRandomCode());
			cust.setCustId("");
			cust.setCustName("");
			// 預約單
			cust.setOwnBrId(creator);
			IBranch ibranch = this.branch.getBranch(Util.nullToSpace(creator));
			if (ibranch != null) {
				cust.setUnitType(UnitTypeEnum.convertToUnitType(ibranch
						.getUnitType()));
			}

			cust.setDataDate(dataDate);
			cust.setBranchId(branch);

			cust.setGenerateDate(CapDate.getCurrentTimestamp());
			cust.setCreateBy(createBy);
			cust.setDocURL(CapWebUtil.getDocUrl(LMS1805M01Page.class));
			
			cust.setCreateTime(CapDate.getCurrentTimestamp());
			cust.setUpdateTime(CapDate.getCurrentTimestamp());

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			if (UtilConstants.CREATEBY.每月產生.equals(createBy)) {
				cust.setCreator(系統產生人員);
				cust.setUpdater(系統產生人員);
			}

			List rows = elf412Sql(branch);
			System.out.print("=======>" + rows.size());
			Iterator it = rows.iterator();
			List<L180M01B> L180M01Bs = new ArrayList<L180M01B>();
			List<String> custids = new ArrayList<String>();
			while (it.hasNext()) {
				Map dataMap = (Map) it.next();
				JSONObject json = new JSONObject();

				Date addOne = CapDate.parseDate(CapDate.addMonth(
						TWNDate.toAD(dataDate).replace("-", ""), 1));
				String[] cauDT = cauculateDate(addOne, dataMap);
				Date duedate = Util.parseDate(cauDT[0]);

				// 產生名單明細
				String mainId = cust.getMainId();
				String custId = Util.trim((String) dataMap.get("CUSTID"));
				String dupNo = Util.trim((String) dataMap.get("DUPNO"));
				String dwDupNo = Util.trim((String) dataMap.get("DUPNO"));
				if (custids.contains(custId + dupNo)) {
					continue;
				}

				String createBY = 系統產生人員;
				String newBy170M01 = UtilConstants.DEFAULT.否;
				String docStatus1 = "1";
				if (!addOne.after(duedate)) {
					continue;
				}

				String elfCName = "";
				if (Util.trimSpace((String) dataMap.get("CNAME")).isEmpty()) {
					elfCName = Util.truncateString(
							Util.trim((String) dataMap.get("ENAME")), 120);
				} else {
					elfCName = Util.truncateString(Util.trimSpace(Util
							.trim((String) dataMap.get("CNAME"))), 120);
				}
				String eCoNm = Util.trim((String) dataMap.get("BUSCD"));

				Date elfDataDt = dataDate;
				String elfMainCust = Util.nullToSpace(dataMap.get("MAINCUST"));
				if (!UtilConstants.DEFAULT.是.equals(elfMainCust)) {
					elfMainCust = "";
				}
				String elfCrdType = Util.trim((String) dataMap.get("CRDTYPE"));
				String elfCrdTTbl = Util.trim((String) dataMap.get("CRDTTBL"));
				String elfMowType = Util.trim((String) dataMap.get("MOWTYPE"));
				String elfMowTbl1 = Util.trim((String) dataMap.get("MOWTBL1"));
				String elfLLRDate = null;
				if (((Date) dataMap.get("LLRDATE")) != null) {
					elfLLRDate = Util.addZeroWithValue(
							TWNDate.toAD((Date) dataMap.get("LLRDATE")), 10);
				}
				Date elfLRDate = (Date) dataMap.get("LRDATE");
				String elfRCkdLine = Util
						.trim((String) dataMap.get("RCKDLINE"));
				// if (!elfRCkdLine.equals(cauDT[1])) {
				// elfRCkdLine = cauDT[1];
				// }
				String elfOCkdLine = Util
						.trim((String) dataMap.get("OCKDLINE"));
				String elfUCkdLINE = Util
						.trim((String) dataMap.get("UCKDLINE"));
				String elfUCkdDt = null;
				if (((Date) dataMap.get("UCKDDT")) != null) {
					elfUCkdDt = Util.addZeroWithValue(
							TWNDate.toAD((Date) dataMap.get("UCKDDT")), 10);
				}
				String elfCState = Util.trim((String) dataMap.get("CSTATE"));
				Date elfCancelDt = (Date) dataMap.get("CANCELDT");
				String elfMDFlag = Util.trim(Util.nullToSpace(dataMap
						.get("MDFLAG")));
				if (!Util.isInteger(elfMDFlag)) {
					elfMDFlag = "";
				}
				Date elfMDDt = (Date) dataMap.get("MDDT");
				String elfProcess = Util.trim((String) dataMap.get("PROCESS"));
				String elfNewAdd = Util.trim((String) dataMap.get("NEWADD"));
				String elfNewDate = Util.trim((String) dataMap.get("NEWDATE"));
				String elfNCkdFlag = Util.trim(dataMap.get("NCKDFLAG"));
				if (!Util.isInteger(elfNCkdFlag) || "8".equals(elfNCkdFlag)) {
					elfNCkdFlag = "";
				}
				// Date elfNCkdDate = (Date) dataMap.get("NCKDDATE");
				// String elfNCkdMemo = Util.trim((String)
				// dataMap.get("NCKDMEMO"));
				String elfNextNwDt = null;
				if (((Date) dataMap.get("NEXTNMDT")) != null) {
					elfNextNwDt = Util.addZeroWithValue(
							TWNDate.toAD((Date) dataMap.get("NEXTNMDT")), 10);
				}
				String elfDBUOBU = Util.trim((String) dataMap.get("DBUOBU"));
				String elfUpdDate = null;
				if (((Date) dataMap.get("UPDDATE")) != null) {
					elfUpdDate = Util.addZeroWithValue(
							TWNDate.toAD((Date) dataMap.get("UPDDATE")), 10);
				}
				String elfUpdater = Util.trim((String) dataMap.get("UPDATER"));
				String elfMemo = Util.trim((String) dataMap.get("MEMO"));
				Date elfTmeStamp = (Date) dataMap.get("TMESTAMP");
				String ucase = Util.trim(dataMap.get("UCASE"));

				// J-108-0078_05097_B1001
				// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
				String elfIsAllNew = Util.trim(dataMap.get("ISALLNEW"));

				L180M01B details = new L180M01B();

				details.setMainId(mainId);
				details.setCustId(custId);
				details.setDupNo(dupNo);
				details.setTypCd(TypCdEnum.海外.getCode());
				details.setCreateTime(CapDate.getCurrentTimestamp());
				details.setUpdateTime(CapDate.getCurrentTimestamp());
				if (UtilConstants.CREATEBY.每月產生.equals(createBy)) {
					details.setCreator(系統產生人員);
					details.setUpdater(系統產生人員);
				} else {
					details.setCreator(user.getUserId());
					details.setUpdater(user.getUserId());
				}

				details.setCreateBY(createBY);
				details.setNewBy170M01(newBy170M01);
				details.setDocStatus1(docStatus1);
				details.setNewLRDate(elfLRDate);
				// details.setNewNCkdFlag(elfNCkdFlag);
				// details.setNewNextNwDt(elfNCkdDate);
				// details.setNewNCkdMemo(elfNCkdMemo);

				details.setElfCName(elfCName);
				details.setECoNm(eCoNm);
				details.setElfDataDt(elfDataDt);
				details.setElfMainCust(elfMainCust);
				details.setElfCrdType(elfCrdType);
				details.setElfCrdTTbl(elfCrdTTbl);
				details.setElfMowType(elfMowType);
				details.setElfMowTbl1(elfMowTbl1);
				details.setElfRCkdLine(elfRCkdLine);
				details.setElfOCkdLine(elfOCkdLine);
				details.setElfUCkdLINE(elfUCkdLINE);
				details.setElfCState(elfCState);
				details.setElfMDFlag(elfMDFlag);
				details.setElfProcess(elfProcess);
				details.setElfNewAdd(elfNewAdd);
				details.setElfNewDate(elfNewDate);

				// details.setElfNCkdFlag(elfNCkdFlag);
				// details.setElfNCkdMemo(elfNCkdMemo);
				details.setElfDBUOBU(elfDBUOBU);
				details.setElfUpdater(elfUpdater);
				details.setElfMemo(elfMemo);

				json.put("ElfLLRDate", elfLLRDate);
				details.setElfLRDate(elfLRDate);
				json.put("ElfUCkdDt", elfUCkdDt);
				details.setElfCancelDt(elfCancelDt);
				details.setElfMDDt(elfMDDt);
				// details.setElfNCkdDate(elfNCkdDate);
				json.put("ElfNextNwDt", elfNextNwDt);
				json.put("ElfUpdDate", elfUpdDate);
				details.setElfTmeStamp(elfTmeStamp);
				details.setULoan(ucase);

				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				details.setCtlType(LrsUtil.CTLTYPE_主辦覆審);

				// J-108-0078_05097_B1001
				// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
				details.setElfIsAllNew(elfIsAllNew);

				DataParse.toBean(json, details);

				// 判斷週期
				details = comCycle(details, branch);

				L180M01Bs.add(details);

				// 新案抓取新案額度
				if (!Util.parseDate(MAXDATE).equals(duedate)) {
					if (!elfNewAdd.isEmpty()) {
						List<L180M01C> l180m01cs = new ArrayList<L180M01C>();
						List elf411Rows = this.dwelf411ovs
								.findELF411ByNewDate(branch, custId, dwDupNo,
										TWNDate.toAD(dataDate));
						Iterator elf411Cntrno = elf411Rows.iterator();
						List<String> cntrs = new ArrayList<String>();
						while (elf411Cntrno.hasNext()) {
							Map cntrMap = (Map) elf411Cntrno.next();
							String cntr = Util.trim(cntrMap.get("CNTRNO"));
							if (cntrs.contains(cntr)) {
								continue;
							}
							L180M01C cntrModel = new L180M01C();

							cntrModel.setMainId(mainId);
							cntrModel.setCustId(custId);
							cntrModel.setDupNo(dupNo);
							cntrModel.setElfCntrType(elfNewAdd);
							cntrModel.setElfCustCoId("");
							cntrModel.setElfCntrNo(cntr);
							// J-106-0145-004 Web e-Loan
							// 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
							cntrModel.setCtlType(LrsUtil.CTLTYPE_主辦覆審);
							l180m01cs.add(cntrModel);
						}
						l180m01cDao.save(l180m01cs);
					}
				}
				custids.add(custId + dupNo);
			}

			// 授權檔
			L180A01A l180a01a = new L180A01A();
			l180a01a.setMainId(cust.getMainId());
			l180a01a.setAuthTime(CapDate.getCurrentTimestamp());
			l180a01a.setAuthType("1");
			l180a01a.setAuthUnit(creator);

			if (!creator.equals(branch)) {
				L180A01A l180a01a2 = new L180A01A();
				l180a01a2.setMainId(cust.getMainId());
				l180a01a2.setAuthTime(CapDate.getCurrentTimestamp());
				l180a01a2.setAuthType("4");
				l180a01a2.setAuthUnit(branch);
				if (user == null) {
					l180a01a2.setOwnUnit("");
					l180a01a2.setOwner("");
				} else {
					l180a01a2.setOwnUnit(user.getUnitNo());
					l180a01a2.setOwner(user.getUserId());
				}
				save(l180a01a2);
			}

			if (user == null) {
				l180a01a.setOwnUnit("");
				l180a01a.setOwner("");
			} else {
				cust.setUnitType(UnitTypeEnum.convertToUnitType(user
						.getUnitType()));
				cust.setOwnBrId(user.getUnitNo());

				l180a01a.setOwnUnit(user.getUnitNo());
				l180a01a.setOwner(user.getUserId());
				l180a01a.setAuthUnit(user.getUnitNo());
			}

			l180m01bDao.save(L180M01Bs);
			save(cust, l180a01a);

			L180M01A model = l180m01aDao.findByMainId(cust.getMainId());
			startFlow(model.getOid());
		} catch (Exception e) {
			logger.error("LMS1805ServiceImpl produceList EXCEPTION!!", e);
			return false;
		}
		return true;
	}

	/**
	 * 判斷週期代碼
	 * 
	 * @param elfMDFlag
	 * @param elfRCkdLine
	 * @param elfNewAdd
	 * @param elfUCkdLINE
	 * @param elfMowType
	 * @param elfMowTbl1
	 * @param elfCrdTTbl
	 * @param elfMainCust
	 * @return String[] {週期代碼,Y=為週期代碼取得 N=不為週期代碼取得}
	 */
	public String[] getRckLine(String elfMDFlag, String elfRCkdLine,
			String elfNewAdd, String elfUCkdLINE, String elfMowType,
			String elfMowTbl1, String elfCrdTTbl, String elfMainCust,
			String elfIsAllNew, String elfBranch) {
		String[] result = new String[2];
		// 異常通報代碼
		if (!Util.isEmpty(elfMDFlag)) {
			if (!"D".equals(elfRCkdLine) && !"F".equals(elfRCkdLine)) {
				if ("10".equals(elfMDFlag)) {
					result[0] = "G";
				} else {
					result[0] = "E";
				}
				result[1] = "Y";
				return result;
			}
		}

		// 新戶/增額/逾轉正
		if (!Util.isEmpty(elfNewAdd)) {
			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			String needNckdLine = LrsUtil.getNewAddRckdLineForElf412(
					elfIsAllNew, elfBranch);
			result[0] = needNckdLine;
			result[1] = "Y";
			return result;
		}
		// 主管機關指定覆審案件
		if (UtilConstants.DEFAULT.是.equals(elfUCkdLINE)) {
			result[0] = "H";
			result[1] = "Y";
			return result;
		}
		// 主要授信戶符合特定評等
		boolean tBADMOW = false;
		if ("9".equals(elfMowTbl1)) {
			if ("1".equals(elfMowType)) {
				tBADMOW = true;
			}
		} else if ("10".equals(elfMowTbl1) || "11".equals(elfMowTbl1)
				|| "12".equals(elfMowTbl1) || "13".equals(elfMowTbl1)
				|| "DF".equals(elfMowTbl1)) {
			tBADMOW = true;
		}
		if (UtilConstants.DEFAULT.是.equals(elfMainCust)) {
			if (("1".equals(elfMowType) && tBADMOW)
					|| (!"1".equals(elfMowType) && tBADMOW)
					|| (Util.isEmpty(elfCrdTTbl) && Util.isEmpty(elfMowType))) {
				result[0] = "B";
				result[1] = "Y";
			} else if (!Util.isEmpty(elfCrdTTbl)
					&& elfCrdTTbl.charAt(0) >= "D".charAt(0)) {
				result[0] = "B";
				result[1] = "Y";
			}
			return result;
		}

		if ("".equals(result[1])) {
			result[1] = "N";
		}
		return result;
	}

	/**
	 * 判斷週期
	 * 
	 * @param l180m01b
	 * @return
	 */
	private L180M01B comCycle(L180M01B l180m01b, String branch) {

		String[] rckLineResult = this.getRckLine(l180m01b.getElfMDFlag(),
				l180m01b.getElfRCkdLine(), l180m01b.getElfNewAdd(),
				l180m01b.getElfUCkdLINE(), l180m01b.getElfMowType(),
				l180m01b.getElfMowTbl1(), l180m01b.getElfCrdTTbl(),
				l180m01b.getElfMainCust(), l180m01b.getElfIsAllNew(), branch);

		if ("Y".equals(rckLineResult[1])) {
			l180m01b.setElfRCkdLine(rckLineResult[0]);
			return l180m01b;
		}

		return l180m01b;
	}

	// 從mis.custdata取值
	@Override
	public Map<String, Object> findCustdataByIdAndDupNo(String custId,
			String dupNo) {
		List<Map<String, Object>> list = LmsCustdataService.findCustDataCname(
				custId, dupNo);

		if (!Util.isEmpty(list) && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	// 新增覆審明細
	@SuppressWarnings("rawtypes")
	@Override
	public boolean produceNew(String mainId, String DupNo, String custId,
			Date dataDate, String branchId) {

		Map<String, Object> CustData = findCustdataByIdAndDupNo(custId, DupNo);
		if (CustData == null || CustData.isEmpty()) {
			return false;
		}

		List elf412Data = this.lms412Service.findLms412ByCustId(branchId,
				custId, DupNo);
		if (elf412Data == null || elf412Data.isEmpty()) {
			return false;
		}

		List rows = elf412Data;
		Iterator it = rows.iterator();
		while (it.hasNext()) {
			Map dataMap = (Map) it.next();
			L180M01B details = new L180M01B();
			JSONObject json = new JSONObject();

			// 產生名單明細
			String dupNo = Util.trim((String) dataMap.get("DUPNO"));
			String createBY = "PEO";
			String newBy170M01 = "N";
			String docStatus1 = "1";

			String elfCName = "";
			if (Util.trimSpace((String) dataMap.get("CNAME")).isEmpty()) {
				elfCName = Util.trim((String) dataMap.get("ENAME"));
			} else {
				elfCName = Util.trimSpace(Util.trim((String) dataMap
						.get("CNAME")));
			}
			String eCoNm = Util.trim((String) dataMap.get("BUSCD"));

			Date elfDataDt = dataDate;
			String elfMainCust = Util.trim((String) dataMap.get("MAINCUST"));
			String elfCrdType = Util.trim((String) dataMap.get("CRDTYPE"));
			String elfCrdTTbl = Util.trim((String) dataMap.get("CRDTTBL"));
			String elfMowType = Util.trim((String) dataMap.get("MOWTYPE"));
			String elfMowTbl1 = Util.trim((String) dataMap.get("MOWTBL1"));
			String elfLLRDate = null;
			if (((Date) dataMap.get("LLRDATE")) != null) {
				elfLLRDate = Util.addZeroWithValue(
						TWNDate.toAD((Date) dataMap.get("LLRDATE")), 10);
			}
			Date elfLRDate = (Date) dataMap.get("LRDATE");
			String elfRCkdLine = Util.trim((String) dataMap.get("RCKDLINE"));
			String elfOCkdLine = Util.trim((String) dataMap.get("OCKDLINE"));
			String elfUCkdLine = Util.trim((String) dataMap.get("UCKDLINE"));
			String elfUCkdDt = null;
			if (((Date) dataMap.get("UCKDDT")) != null) {
				elfUCkdDt = Util.addZeroWithValue(
						TWNDate.toAD((Date) dataMap.get("UCKDDT")), 10);
			}
			String elfCState = Util.trim((String) dataMap.get("CSTATE"));
			Date elfCancelDt = (Date) dataMap.get("CANCELDT");
			String elfMDFlag = Util.trim((String) dataMap.get("MDFLAG"));
			Date elfMDDt = (Date) dataMap.get("MDDT");
			String elfProcess = Util.trim((String) dataMap.get("PROCESS"));
			String elfNewAdd = Util.trim((String) dataMap.get("NEWADD"));
			String elfNewDate = Util.trim((String) dataMap.get("NEWDATE"));
			String elfNCkdFlag = Util.trim((String) dataMap.get("NCKDFLAG"));
			if (!Util.isInteger(elfNCkdFlag) || "8".equals(elfNCkdFlag)) {
				elfNCkdFlag = "";
			}
			Date elfNCkdDate = (Date) dataMap.get("NCKDDATE");
			String elfNCkdMemo = Util.trim((String) dataMap.get("NCKDMEMO"));
			String elfNextNwDt = null;
			if (((Date) dataMap.get("NEXTNMDT")) != null) {
				elfNextNwDt = Util.addZeroWithValue(
						TWNDate.toAD((Date) dataMap.get("NEXTNMDT")), 10);
			}
			String elfDBUOBU = Util.trim((String) dataMap.get("DBUOBU"));
			String elfUpdDate = null;
			if (((Date) dataMap.get("UPDDATE")) != null) {
				elfUpdDate = Util.addZeroWithValue(
						TWNDate.toAD((Date) dataMap.get("UPDDATE")), 10);
			}
			String elfUpdater = Util.trim((String) dataMap.get("UPDATER"));
			String elfMemo = Util.trim((String) dataMap.get("MEMO"));
			Date elfTmeStamp = (Date) dataMap.get("TMESTAMP");
			String ucase = Util.trim(dataMap.get("UCASE"));

			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			String elfIsAllNew = Util.trim(dataMap.get("ISALLNEW"));

			L180M01A l180m01a = l180m01aDao.findByMainId(mainId);
			if (!Util.isEmpty(l180m01a.getBatchNO())) {
				ISearch search = l180m01bDao.createSearchTemplete();
				search.addSearchModeParameters(SearchMode.EQUALS,
						EloanConstants.MAIN_ID, mainId);
				search.addSearchModeParameters(SearchMode.IS_NOT_NULL,
						"projectSeq", null);
				search.addOrderBy("projectSeq", true);
				List<L180M01B> l180m01bs = l180m01bDao.find(search);
				if (!Util.isEmpty(l180m01bs) && l180m01bs.size() != 0) {
					int seqNo = l180m01bs.get(0).getProjectSeq() + 1;

					String caseNumber = numberService.getCaseNumber(
							L180M01A.class, l180m01a.getBranchId(),
							TWNDate.toAD(l180m01a.getDataDate())
									.substring(0, 4),
							Util.addZeroWithValue(l180m01a.getBatchNO(), 3)
									+ "-" + Util.addZeroWithValue(seqNo, 3));
					details.setProjectNo(caseNumber);
					details.setProjectSeq(seqNo);
				}
			}

			details.setMainId(mainId);
			details.setCustId(custId);
			details.setDupNo(dupNo);
			details.setTypCd(TypCdEnum.海外.getCode());
			details.setCtlType(LrsUtil.CTLTYPE_主辦覆審);
			details.setCreateBY(createBY);
			details.setNewBy170M01(newBy170M01);
			details.setDocStatus1(docStatus1);

			details.setElfCName(elfCName);
			details.setECoNm(eCoNm);
			details.setElfDataDt(elfDataDt);
			details.setElfMainCust(elfMainCust);
			details.setElfCrdType(elfCrdType);
			details.setElfCrdTTbl(elfCrdTTbl);
			details.setElfMowType(elfMowType);
			details.setElfMowTbl1(elfMowTbl1);
			details.setElfRCkdLine(elfRCkdLine);
			details.setElfOCkdLine(elfOCkdLine);
			details.setElfUCkdLINE(elfUCkdLine);
			details.setElfCState(elfCState);
			details.setElfMDFlag(elfMDFlag);
			details.setElfProcess(elfProcess);
			details.setElfNewAdd(elfNewAdd);
			details.setElfNewDate(elfNewDate);

			details.setElfNCkdFlag(elfNCkdFlag);
			details.setElfNCkdMemo(elfNCkdMemo);
			details.setElfDBUOBU(elfDBUOBU);
			details.setElfUpdater(elfUpdater);
			details.setElfMemo(elfMemo);

			json.put("ElfLLRDate", elfLLRDate);
			details.setElfLRDate(elfLRDate);
			json.put("ElfUCkdDt", elfUCkdDt);
			details.setElfCancelDt(elfCancelDt);
			details.setElfMDDt(elfMDDt);
			details.setElfNCkdDate(elfNCkdDate);
			json.put("ElfNextNwDt", elfNextNwDt);
			json.put("ElfUpdDate", elfUpdDate);
			details.setElfTmeStamp(elfTmeStamp);
			details.setULoan(ucase);

			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			details.setElfIsAllNew(elfIsAllNew);

			DataParse.toBean(json, details);

			save(details);
		}
		return true;
	}

	/**
	 * 更新ELF412中的額度資料(Import_LNF022(sql)) 利用額度序號判斷是否銷戶以及戶況
	 * 
	 * @param custid
	 * @param dupno
	 * @param branchId
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public Map importLnf022Sql(String custid, String dupno, String branchId) {

		String custIdandDupno = (custid + "           ").substring(0, 10)
				+ dupno;

		return this.dwLnquotovService.findDW_LNQUOTOV_Contract(custIdandDupno,
				branchId, custid, custIdandDupno);
	}

	/**
	 * 更新ELF412中的額度資料(匯入LNF022(Import_LNF022))
	 * 
	 * @param ELF412_BRANCH
	 * @param ELF412_CUSTID
	 * @param ELF412_DUPNO
	 * @param ELF412_CSTATE
	 * @param ELF412_CANCELDT
	 * @return
	 */
	@Override
	@SuppressWarnings("rawtypes")
	public String[] importLnf022(String branch, String custId, String dupNo,
			String cstate) {
		Map dataMap = importLnf022Sql(custId, dupNo, branch);
		String info = "";
		if (dataMap != null) {

			String normalFg = Util.trim((String) dataMap.get("NORMAL_FG"));
			String ovFg = Util.trim((String) dataMap.get("OV_FG"));
			String obFg = Util.trim((String) dataMap.get("OB_FG"));
			String cdFg = Util.trim((String) dataMap.get("CD_FG"));
			int tcount = (Integer) dataMap.get("TCOUNT");

			if (tcount == 0) {
				// 無資料表示已銷戶，上銷戶註記
				cstate = "";
				info = "N";
			} else {
				/**
				 * normalFg 是否有正常餘額
				 * 
				 * <pre>
				 * cstate
				 * 0.無餘額
				 * 1.正常
				 * 2.逾期
				 * 3.催收
				 * 4.呆帳
				 * </pre>
				 **/
				if ("Y".equals(normalFg)) {
					cstate = "1";
				} else {
					if ("Y".equals(cdFg)) {
						cstate = "4";
					} else if ("Y".equals(obFg)) {
						cstate = "3";
					} else if ("Y".equals(ovFg)) {
						cstate = "2";
					} else {
						cstate = "0";
					}
				}
				info = "Y";
			}
		}

		String[] returnVal = { cstate, info };
		return returnVal;
	}

	/**
	 * 更新ELF412 - 匯入LNFE0851
	 * 
	 * @param branch
	 * @param custid
	 * @param dupno
	 * @param mdFlag
	 * @param mddt
	 * @param process
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public String[] importLnfe0851(String branch, String custid, String dupno,
			String mdFlag, String mddt, String process) {

		List rows = this.misLNFE0851Service.findLNFE0851ByCustId(custid, dupno,
				branch);

		Iterator lnfe0851It = rows.iterator();
		while (lnfe0851It.hasNext()) {
			Map dataMap = (Map) lnfe0851It.next();

			mdFlag = Util.trim((String) dataMap.get("LNFE0851_MDFLAG"));
			mddt = Util.trim((String) dataMap.get("MDDT"));
			process = Util.trim((String) dataMap.get("LNFE0851_PROCESS"));
		}

		String[] a = { mdFlag, mddt, process };
		return a;
	}

	/**
	 * 更新ELF412 - 計算週期
	 * 
	 * @param elf412Canceldt
	 * @param elf412Nckdflag
	 * @param elf412Nckddate
	 * @param elf412Nextltdt
	 * @param elf412Nextnwdt
	 * @param elf412Mdflag
	 * @param elf412Rckdline
	 * @param elf412Newadd
	 * @param elf412Uckdline
	 * @param elf412Mowtbl1
	 * @param elf412Mowtype
	 * @param elf412Maincust
	 * @param elf412Cstate
	 * @param elf412Ockdline
	 * @param elf412Crdttbl
	 * @param elf412Nckdmemo
	 * @return
	 */
	@Override
	public Object[] caculateElf412(Date elf412Canceldt, String elf412Nckdflag,
			Date elf412Nckddate, Date elf412Nextltdt, Date elf412Nextnwdt,
			String elf412Mdflag, String elf412Rckdline, String elf412Newadd,
			String elf412Uckdline, String elf412Mowtbl1, String elf412Mowtype,
			String elf412Maincust, String elf412Cstate, String elf412Ockdline,
			String elf412Crdttbl, String elf412Nckdmemo, String elf412Branch,
			String elf412IsAllNew, String elf412FcrdType,
			String elf412FcrdArea, String elf412FcrdPred, String elf412FcrdGrad) {

		// 7.銷戶，不覆審
		if (!(elf412Canceldt == null || CapDate.parseDate(MINDATE).equals(
				elf412Canceldt))) {
			if (elf412Nckdflag == null || elf412Nckdflag.isEmpty()
					|| "8".equals(elf412Nckdflag)) {
				elf412Nckdflag = "7";
				elf412Nckddate = elf412Canceldt;
				elf412Nextltdt = null;
				elf412Nextnwdt = null;
			}
			Object[] b = { elf412Canceldt, elf412Nckdflag, elf412Nckddate,
					elf412Nextltdt, elf412Nextnwdt, elf412Mdflag,
					elf412Rckdline, elf412Newadd, elf412Uckdline,
					elf412Mowtbl1, elf412Mowtype, elf412Maincust, elf412Cstate,
					elf412Ockdline, elf412Crdttbl, elf412Nckdmemo,
					elf412Branch, elf412IsAllNew, elf412FcrdType,
					elf412FcrdArea, elf412FcrdPred, elf412FcrdGrad };

			return b;
		}

		// 計算週期
		String tElf412Rckdline = "";
		String tElf412Ockdline = "";
		/**
		 * 週期代碼存放順序 // 異常通報三個月(E、G) > 異常通報六個月(D、F) > 新案(C) > 主管機關指定覆審案件(H) >
		 * 主要授信戶D即以下等(B) > // 一年覆審(A) // // 異常通報代碼 ...... 異常戶3個月
		 **/
		if (!elf412Mdflag.isEmpty()) {
			// 有新的異常通報日期，無上次覆審日或前次報送日期
			// 直接認定有新的異常通報，所以換成新的通報日期，週期改為第一次三個月內要覆審
			// D.異常戶已三個月覆審過- 爾後半年覆審一次。
			// F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。
			if (!"D".equals(elf412Rckdline) && !"F".equals(elf412Rckdline)) {
				if ("10".equals(elf412Mdflag)) {
					tElf412Rckdline = "G";
				} else {
					tElf412Rckdline = "E";
				}
			}
		}

		// 如果有異常通報且之前已經三個月覆審，則不異動覆審週期
		if (!elf412Mdflag.isEmpty()) {
			// D.異常戶已三個月覆審過- 爾後半年覆審一次。
			// F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。
			if ("D".equals(elf412Rckdline) || "F".equals(elf412Rckdline)) {
				tElf412Rckdline = elf412Rckdline;
			}
		}

		// 新戶/增額/逾轉正 6個月
		if ("".equals(tElf412Rckdline)) {
			if (!elf412Newadd.isEmpty()) {
				// 判斷首次往來之新貸戶且非"079"分行則為"I",否則為"C"
				// J-108-0078_05097_B1001 Web e-Loan企金授信覆審系統修改首次往來之新授信戶應辦理覆審之期限
				boolean isRckdLineIEffective = retrialService
						.isRckdLine_I_Effective(CapDate.getCurrentTimestamp());
				String needRckdLine = "";
				if (isRckdLineIEffective) {
					needRckdLine = LrsUtil.getNewAddRckdLineForElf412(
							elf412IsAllNew, elf412Branch);// "C" 或 "I"
				} else {
					needRckdLine = "C";
				}
				tElf412Rckdline = needRckdLine;
			}
		}

		// 主管機關指定覆審案件 6個月
		if ("".equals(tElf412Rckdline)) {
			if ("Y".equals(elf412Uckdline)) {
				tElf412Rckdline = "H";
			}
		}

		// 主要授信戶符合特定評等 6個月
		if ("".equals(tElf412Rckdline)) {
			boolean tBADMOW = false;

			tBADMOW = retrialService.isBadMow__CrdtTbl_GE_D__FCRDTYPESixMon(
					elf412Mowtbl1, elf412Mowtype, elf412Crdttbl,
					elf412FcrdType, elf412FcrdArea, elf412FcrdPred,
					elf412FcrdGrad);

			if ("Y".equals(elf412Maincust) && tBADMOW) {
				tElf412Rckdline = "B";
			}

			// if (("9".equals(elf412Mowtbl1) && "1".equals(elf412Mowtype))
			// || ("10".equals(elf412Mowtbl1)
			// || "11".equals(elf412Mowtbl1)
			// || "12".equals(elf412Mowtbl1)
			// || "13".equals(elf412Mowtbl1) || "DF"
			// .equals(elf412Mowtbl1))) {
			// tBADMOW = true;
			// }
			// if (!elf412Crdttbl.isEmpty()) {
			// if ("Y".equals(elf412Maincust)) {
			// if (tBADMOW
			// || (!elf412Crdttbl.isEmpty() && (Util.equals(
			// Util.getLeftStr(elf412Crdttbl, 1), 'D') || Util
			// .equals(Util.getLeftStr(elf412Crdttbl, 1),
			// 'E')))
			// || ((elf412Crdttbl == null || elf412Crdttbl
			// .isEmpty()) && (elf412Mowtbl1 == null || elf412Mowtbl1
			// .isEmpty()))) {
			// tElf412Rckdline = "B";
			// }
			// }
			// }
		}

		if ("".equals(tElf412Rckdline)) {
			tElf412Rckdline = "A";
		}
		if ("".equals(tElf412Ockdline)) {
			tElf412Ockdline = "A";
		}

		// 處理不覆審註記
		if (!elf412Newadd.isEmpty()) {
			// 6.如果是第一次出現新案、增額，則將不覆審註記取消
			if (!"8".equals(elf412Nckdflag)) {
				elf412Nckdflag = "";
				elf412Nckddate = null;
				elf412Nckdmemo = "";
				elf412Nextltdt = null;
				elf412Nextnwdt = null;
			}
		}

		if (!elf412Cstate.isEmpty() && elf412Cstate.charAt(0) < '2'
				&& "6".equals(elf412Nckdflag)) {
			// 不覆審註記為6.已列報為逾期放款或轉列催收款項之案件，然而本次發現有正常戶，取消不覆審註記
			elf412Nckdflag = "";
			elf412Nckddate = null;
			elf412Nckdmemo = "";
			elf412Nextltdt = null;
			elf412Nextnwdt = null;
		}

		if (!elf412Cstate.isEmpty() && elf412Cstate.charAt(0) >= '2') {
			// 6.已列報為逾期放款或轉列催收款項之案件，不覆審
			if (elf412Nckdflag == null || elf412Nckdflag.isEmpty()) {
				elf412Nckdflag = "6";
				elf412Nckddate = CapDate.getCurrentTimestamp();
				elf412Nextltdt = null;
				elf412Nextnwdt = null;
			}
		}

		if (elf412Canceldt == null
				|| CapDate.parseDate(MINDATE).equals(elf412Canceldt)) {
			// 銷戶轉正常，要覆審
			if ("7".equals(elf412Nckdflag)) {
				elf412Nckdflag = "6";
				elf412Nckddate = null;
				elf412Nextltdt = null;
				elf412Nextnwdt = null;
			}
		}

		elf412Ockdline = tElf412Ockdline;
		elf412Rckdline = tElf412Rckdline;

		Object[] a = { elf412Canceldt, elf412Nckdflag, elf412Nckddate,
				elf412Nextltdt, elf412Nextnwdt, elf412Mdflag, elf412Rckdline,
				elf412Newadd, elf412Uckdline, elf412Mowtbl1, elf412Mowtype,
				elf412Maincust, elf412Cstate, elf412Ockdline, elf412Crdttbl,
				elf412Nckdmemo };

		return a;
	}

	/**
	 * 更新ELF412基本資料(Import_Data(sql))
	 * 
	 * @param ELF412_BRANCH
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List importDataSql(String branchId) {

		return lms412Service.findAllLMS412ByBranch(branchId);
	}

	public List<Map<String, Object>> dw412Sql(String branchId, Date dataDate) {
		return dwelf412ovs.findDwElf412ovsByBranch(branchId, dataDate);

	}

	private L180M01B getL180M01B(Map<String, Object> map) {
		L180M01B l180m01b = new L180M01B();

		String elfMainCust = Util.trim(map.get("MAINCUST"));
		if (!UtilConstants.DEFAULT.是.equals(elfMainCust)) {
			elfMainCust = "";
		}
		String elfCrdType = Util.trim(map.get("DWCRDTYPE"));
		String elfCrdTTbl = Util.trim(map.get("DWCRDTTBL"));
		String elfMowType = Util.trim(map.get("DWMOWTYPE"));
		String elfMowTbl1 = Util.trim(map.get("DWMOWTBL1"));
		String elfRCkdLine = Util.trim(map.get("DWRCKDLINE"));
		String elfUCkdLINE = Util.trim(map.get("DWUCKDLINE"));
		String elfMDFlag = Util.trim(map.get("DWMDFLAG"));
		String elfNewAdd = Util.trim(map.get("DWNEWADD"));

		l180m01b.setElfMainCust(elfMainCust);
		l180m01b.setElfCrdType(elfCrdType);
		l180m01b.setElfCrdTTbl(elfCrdTTbl);
		l180m01b.setElfMowType(elfMowType);
		l180m01b.setElfMowTbl1(elfMowTbl1);
		l180m01b.setElfRCkdLine(elfRCkdLine);
		l180m01b.setElfUCkdLINE(elfUCkdLINE);
		l180m01b.setElfMDFlag(elfMDFlag);
		l180m01b.setElfNewAdd(elfNewAdd);
		return l180m01b;
	}

	/**
	 * 更新LMS412
	 * 
	 * @param r6412
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public boolean R6ToMisLms412(Map r6412) {
		Date cycMn = (Date) r6412.get("CYC_MN");
		String brNo = Util.trim((String) r6412.get("BR_NO"));
		String custId = Util.trim((String) r6412.get("CUST_ID"));
		String dupNo = Util.trim(Util.nullToSpace(r6412.get("DUPNO")));
		if (Util.isEmpty(dupNo)) {
			dupNo = "0";
		}
		String pkCust = Util.trim(r6412.get("PK_CUST"));
		String state = Util.trim(r6412.get("STATE"));
		String newadd = Util.trim(r6412.get("DWNEWADD"));
		Date newDt = null;
		if (!Util.isEmpty(Util.nullToSpace(r6412.get("NEW_DT")))) {
			newDt = CapDate.parseDate(Util.nullToSpace(r6412.get("NEW_DT"))
					+ "01");
		}
		String nckFg = Util.trim((String) r6412.get("NCK_FG"));
		Date nckDt = null;
		if (r6412.get("NCK_DT") != null
				&& !((String) r6412.get("NCK_DT")).isEmpty()) {
			nckDt = CapDate.parseDate((String) r6412.get("NCK_DT"));
		}
		Date cancelDt = null;
		if (r6412.get("CANCEL_DT") != null
				&& !((String) r6412.get("CANCEL_DT")).isEmpty()) {
			cancelDt = CapDate.parseDate((String) r6412.get("CANCEL_DT"));
		}
		String commFg = Util.trim((String) r6412.get("COMM_FG"));

		String lms412CustId = Util.trim((String) r6412.get("CUSTID"));

		L180M01B l180m01b = getL180M01B(r6412);
		l180m01b = this.comCycle(l180m01b, brNo);

		// J-108-0078_05097_B1001
		// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
		// 判斷新戶????????
		// findIsNewCustForCTL

		if (lms412CustId == null || lms412CustId.isEmpty()) {

			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			// 判斷新戶????????
			// findIsNewCustForCTL

			this.lms412Service.addLms412(brNo, custId, dupNo, pkCust, newadd,
					Util.nullToSpace(r6412.get("NEW_DT")),
					l180m01b.getElfRCkdLine());

		} else {
			String lms412Newadd = Util.trim((String) r6412.get("NEWADD"));
			Date lms412Newdate = null;
			if (!Util.isEmpty(Util.nullToSpace(r6412.get("NEWDATE")))) {
				lms412Newdate = CapDate.parseDate(Util.nullToSpace(r6412
						.get("NEWDATE")));
			}
			String lms412Nckdflag = Util.trim((String) r6412.get("NCKDFLAG"));

			Date lms412Llrdate = null;
			if (!Util.isEmpty(Util.nullToSpace(r6412.get("LLRDATE")))) {
				lms412Llrdate = CapDate.parseDate(Util.nullToSpace(r6412
						.get("LLRDATE")));
			}
			Date lms412Lrdate = null;
			if (!Util.isEmpty(r6412.get("LRDATE"))) {
				lms412Lrdate = (Date) r6412.get("LRDATE");
			}
			String lms412Nckdmemo = Util.trim((String) r6412.get("NCKDMEMO"));
			Date lms412Nextnwdt = null;
			if (!Util.isEmpty(r6412.get("NEXTNWDT"))) {
				lms412Nextnwdt = (Date) r6412.get("NEXTNWDT");
			}
			Date lms412Nextltdt = null;
			if (!Util.isEmpty(r6412.get("NEXTLTDT"))) {
				lms412Nextltdt = (Date) r6412.get("NEXTLTDT");
			}

			if (!Util.isEmpty(lms412Newadd) && !Util.isEmpty(newadd)
					&& lms412Newdate != null && newDt != null
					&& lms412Newdate.after(newDt)) {
				return false;
			} else if (!Util.isEmpty(lms412Newadd) && Util.isEmpty(newadd)) {
				return false;
			}
			if (("8".equals(lms412Nckdflag) || "9".equals(lms412Nckdflag))
					&& (nckFg == null || nckFg.isEmpty())) {
				return false;
			}
			if ("7".equals(lms412Nckdflag) && !newadd.isEmpty()) {
				lms412Llrdate = lms412Lrdate;
				lms412Lrdate = null;
				Map elchklst = misBASEService.findElchklst(brNo, custId, dupNo);
				if (elchklst != null && !elchklst.isEmpty()) {
					lms412Lrdate = (Date) elchklst.get("LRDATE");
				}
			}

			Date lms412Nckddate = null;

			if (!"8".equals(lms412Nckdflag) && !lms412Nckdflag.isEmpty()
					&& !lms412Newadd.isEmpty()) {
				lms412Nckdflag = "";
				lms412Nckddate = null;
				lms412Nckdmemo = "";
				lms412Nextnwdt = null;
				lms412Nextltdt = null;
			}

			Date lms412DataDt = cycMn;
			String lms412Branch = brNo;
			String lms412Dupno = dupNo;
			String lms412Maincust = pkCust;
			String lms412Cstate = state;
			lms412Newadd = newadd;
			lms412Newdate = newDt;
			String newDate = "";
			if (lms412Newdate != null) {
				newDate = TWNDate.toAD(lms412Newdate).substring(0, 4)
						+ TWNDate.toAD(lms412Newdate).substring(5, 7);
			}
			lms412Nckdflag = nckFg;
			lms412Nckddate = nckDt;
			Date lms412Canceldt = cancelDt;
			String lms412Dbuobu = commFg;

			// J-108-0078_05097_B1001
			// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
			// 判斷新戶????????
			// findIsNewCustForCTL

			this.lms412Service.saveLms412(lms412DataDt, lms412Maincust,
					lms412Cstate, lms412Newadd, newDate, lms412Nckdflag,
					lms412Nckddate, lms412Canceldt, lms412Dbuobu, lms412Lrdate,
					lms412Llrdate, lms412Nckdmemo, lms412Nextnwdt,
					lms412Nextltdt, l180m01b.getElfRCkdLine(), lms412Branch,
					lms412CustId, lms412Dupno);
		}

		return true;
	}

	/**
	 * 更新新作額度
	 * 
	 * @param r6412
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public boolean updateCntr(Map r6412) {
		String cycMn = TWNDate.toAD((Date) r6412.get("CYC_MN"));
		String brNo = Util.trim((String) r6412.get("BR_NO"));

		if (brNo != null && !brNo.isEmpty()) {
			String newadd = Util.trim((String) r6412.get("DWNEWADD"));
			Date newDt = null;
			if (r6412.get("NEW_DT") != null
					&& !Util.trim((String) r6412.get("NEW_DT")).isEmpty()) {
				newDt = CapDate.parseDate((String) r6412.get("NEW_DT"));
			}
			String memo = Util.trim((String) r6412.get("MEMO"));

			// String branch = Util.trim((String) r6412.get("BRANCH"));
			String custId = Util.trim((String) r6412.get("CUST_ID"));
			String dupNo = Util.trim((String) r6412.get("DUPNO"));
			if (dupNo.isEmpty()) {
				dupNo = "0";
			}
			String cntrNo = Util.trim((String) r6412.get("CNTRNO"));
			if (cntrNo != null && !cntrNo.isEmpty()) {
				List cntr = this.dwelf411ovs.findELF411ByCycMn(cycMn, brNo,
						custId, dupNo);
				Iterator cntrMap = cntr.iterator();
				while (cntrMap.hasNext()) {
					Map cntr411Map = (Map) cntrMap.next();
					String cntr411 = Util.trim((String) cntr411Map
							.get("CNTRNO"));
					Date dateYM411 = (Date) cntr411Map.get("CYC_MN");
					List<Object[]> list = l170m01bDao.findCntrNo(cntr411);
					if (list.size() == 0) {
						continue;
					}
					Date retrialDate = (Date) list.get(0)[1];
					// 覆審報告表日期要比412的覆審日期大，在備註欄上備註
					if (!Util.isEmpty(retrialDate) && !Util.isEmpty(newDt)
							&& retrialDate.after(newDt)) {
						String strnewadd = "";
						if (UtilConstants.NEWADD.新作.equals(newadd)) {
							CodeType codeType = codeTypeService
									.findByCodeTypeAndCodeValue(
											"lms1805_newAdd",
											UtilConstants.NEWADD.新作);
							strnewadd = codeType.getCodeDesc();
						} else if (UtilConstants.NEWADD.增額.equals(newadd)) {
							CodeType codeType = codeTypeService
									.findByCodeTypeAndCodeValue(
											"lms1805_newAdd",
											UtilConstants.NEWADD.增額);
							strnewadd = codeType.getCodeDesc();
						} else if (UtilConstants.NEWADD.逾放轉正.equals(newadd)) {
							CodeType codeType = codeTypeService
									.findByCodeTypeAndCodeValue(
											"lms1805_newAdd",
											UtilConstants.NEWADD.逾放轉正);
							strnewadd = codeType.getCodeDesc();
						}
						memo = TWNDate.toAD(dateYM411).substring(0, 4) + "/"
								+ TWNDate.toAD(dateYM411).substring(4)
								+ strnewadd + "已於" + retrialDate + "辦理覆審";
						newadd = "";
						newDt = null;
					}
					String mdflag = Util.trim((String) r6412.get("MDFLAG"));
					String mddt = null;
					if (((Date) r6412.get("MDDT")) != null) {
						mddt = Util.addZeroWithValue(
								TWNDate.toAD((Date) r6412.get("MDDT")), 10);
					}
					String process = Util.trim((String) r6412.get("PROCESS"));

					this.lms412Service.saveLms412Mdflag(mdflag, mddt, process,
							newadd, TWNDate.toAD(newDt), memo, brNo, custId,
							dupNo);
				}
			}

		}
		return true;
	}

	@Override
	public void deleteL180M01Z(List<String> branchid, Date dataDate) {
		for (String branch : branchid) {
			L180M01Z l180m01z = l180m01zDao.findByUniqueKey(dataDate, branch);
			if (l180m01z != null) {
				l180m01zDao.delete(l180m01z);
			}
		}
	}

	@Override
	public String updateResoure(String branch, Date dataDate) {

		String returnVal = "";

		L180M01Z l180m01zOld = l180m01zDao.findByUniqueKey(dataDate, branch);
		if (l180m01zOld != null) {
			return returnVal;
		}
		boolean sucess = true;
		logger.info("into this.dw412Sql(branch, upDate)");
		Date upDate = CapDate.parseDate(CapDate.addMonth(TWNDate.toAD(dataDate)
				.replace("-", ""), -1));
		logger.info("branch = " + branch);
		logger.info("upDate = " + upDate);
		List<Map<String, Object>> rows = this.dw412Sql(branch, upDate);
		logger.info("sql[size]===>" + rows.size());
		List<String> custids = new ArrayList<String>();
		logger.info("into for(Map<String,Object> dataMap : rows){ ");
		for (Map<String, Object> dataMap : rows) {
			// if(sucess){
			String custId = Util.nullToSpace(dataMap.get("CUST_ID"));
			String dupNo = Util.nullToSpace(dataMap.get("DUPNO"));
			if (Util.isEmpty(custId) || custids.contains(custId + dupNo)) {
				continue;
			}
			if (R6ToMisLms412(dataMap)) {
				if (!updateCntr(dataMap)) {
					// sucess = false;
				}
			} else {
				// sucess = false;
			}
			custids.add(custId + dupNo);
			// }
		}
		logger.info("sucess = " + sucess);

		if (sucess && rows.size() > 0) {
			L180M01Z l180m01z = new L180M01Z();
			l180m01z.setBranchId(branch);
			l180m01z.setDataDate(dataDate);
			l180m01z.setFinishTime(CapDate.getCurrentTimestamp());

			l180m01zDao.save(l180m01z);
		} else {
			returnVal = branch;
		}

		return returnVal;
	}

	@Override
	public boolean produceReport(Date ctlDate, String oid,
			boolean accountYN) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L180M01A l180m01a = findModelByOid(L180M01A.class, oid);
		if (!Util.isEmpty(ctlDate)) {
			l180m01a.setDefaultCTLDate(ctlDate);
		}

		List<L180M01B> l180m01blist = findL180m01bByMainId(
				l180m01a.getMainId(), "");

		Date defaultDate = l180m01a.getDefaultCTLDate();
		String branchId = l180m01a.getBranchId();

		int l180m01blistsize = l180m01blist.size();
		List<L170M01A> l170m01as = new ArrayList<L170M01A>();
		List<L170A01A> l170a01as = new ArrayList<L170A01A>();
		List<L170M01E> l170m01es = new ArrayList<L170M01E>();
		BranchRate branchRate = lmsService
				.getBranchRate(l180m01a.getBranchId());
		for (int i = 0; i < l180m01blistsize; i++) {
			L180M01B l180m01b = l180m01blist.get(i);
			String nCkdFlag = Util.trim(l180m01b.getNewNCkdFlag());
			if (!"8".equals(nCkdFlag)) {
				L170M01A l170m01a = new L170M01A();

				l170m01a.setMainId(IDGenerator.getUUID());
				l170m01a.setTypCd(TypCdEnum.海外.getCode());
				l170m01a.setCustId(l180m01b.getCustId());
				l170m01a.setDupNo(l180m01b.getDupNo());

				// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
				l170m01a.setRptId(retrialService
						.getLatestRetrialItemVer_OverSea(l170m01a));

				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				l170m01a.setCtlType(Util.trim(l180m01b.getCtlType()));

				l170m01a.setCustName(l180m01b.getElfCName());
				l170m01a.setTotBalCurr(branchRate.getMCurr());
				l170m01a.setTotQuotaCurr(branchRate.getMCurr());
				l170m01a.setUnitType("1");
				l170m01a.setOwnBrId(branchId);
				l170m01a.setRandomCode(IDGenerator.getRandomCode());
				l170m01a.setRetrialDate(defaultDate);
				l170m01a.setLastRetrialDate(l180m01b.getElfLRDate());
				l170m01a.setProjectNo(l180m01b.getProjectNo());
				l170m01a.setProjectSeq(l180m01b.getProjectSeq());
				l170m01a.setChairman(l180m01b.getSup3CNm());

				// 行業別
				Bstbl bstbl = bstblService.findByEcocd(l180m01b.getECoNm());
				if (bstbl != null) {
					l170m01a.setTradeType(bstbl.getEconm());
				}
				l170m01a.setMLoanPerson(l180m01b.getElfMainCust());
				l170m01a.setRltGuarantor(l180m01b.getRltGuarantor());
				l170m01a.setCreator(user.getUserId());
				l170m01a.setCreateTime(CapDate.getCurrentTimestamp());
				l170m01a.setDocURL(CapWebUtil.getDocUrl(LMS1705M01Page.class));
				l170m01a.setUpdater("");

				// 負責人
				Map<String, Object> sup3CNmMap = misELCUS25ServiceImpl
						.getMiselcus25(l180m01b.getCustId(),
								l180m01b.getDupNo());
				if (!Util.isEmpty(sup3CNmMap)) {
					String sup3CNm = Util.trim(sup3CNmMap.get("SUP1CNM"));
					l170m01a.setChairman(sup3CNm);
					l180m01b.setSup3CNm(sup3CNm);
				}

				l170m01a.setNCkdFlag(nCkdFlag);

				L170A01A l170a01a = new L170A01A();
				l170a01a.setAuthTime(CapDate.getCurrentTimestamp());
				l170a01a.setAuthType("1");
				l170a01a.setAuthUnit(branchId);
				l170a01a.setMainId(l170m01a.getMainId());
				l170a01a.setOwner(user.getUserId());
				l170a01a.setOwnUnit(user.getUnitNo());

				l170a01as.add(l170a01a);
				// 資信評等類別
				if (!Util.isEmpty(Util.trim(l180m01b.getElfCrdType()))
						|| !Util.isEmpty(Util.trim(l180m01b.getElfCrdTTbl()))) {
					L170M01E l170m01e = new L170M01E();
					l170m01e.setMainId(l170m01a.getMainId());
					l170m01e.setCustId(l180m01b.getCustId());
					l170m01e.setDupNo(l180m01b.getDupNo());
					l170m01e.setCrdTYear(CapDate.parseDate(MINDATE));
					l170m01e.setCrdTBR("");
					l170m01e.setCntrNo("");
					l170m01e.setCrdType(Util.isEmpty(l180m01b.getElfCrdType()) ? "DL"
							: l180m01b.getElfCrdType());
					l170m01e.setFinYear("");
					l170m01e.setGrade(l180m01b.getElfCrdTTbl());
					l170m01e.setCreator(user.getUserId());
					l170m01e.setCreateTime(CapDate.getCurrentTimestamp());
					l170m01e.setUpdater(user.getUserId());
					l170m01e.setUpdateTime(CapDate.getCurrentTimestamp());
					l170m01e.setTimeFlag("T");
					l170m01es.add(l170m01e);
				}
				// 信用模型評等類別
				if (!Util.isEmpty(Util.trim(l180m01b.getElfMowType()))
						|| !Util.isEmpty(Util.trim(l180m01b.getElfMowTbl1()))) {
					L170M01E l170m01e = new L170M01E();
					l170m01e.setMainId(l170m01a.getMainId());
					l170m01e.setCustId(l180m01b.getCustId());
					l170m01e.setDupNo(l180m01b.getDupNo());
					l170m01e.setCrdTYear(CapDate.parseDate(MINDATE));
					l170m01e.setCrdTBR("");
					l170m01e.setCntrNo("");
					l170m01e.setCrdType(l180m01b.getElfMowType());
					l170m01e.setFinYear("");
					l170m01e.setGrade(l180m01b.getElfMowTbl1());
					l170m01e.setCreator(user.getUserId());
					l170m01e.setCreateTime(CapDate.getCurrentTimestamp());
					l170m01e.setUpdater(user.getUserId());
					l170m01e.setUpdateTime(CapDate.getCurrentTimestamp());
					l170m01e.setTimeFlag("T");
					l170m01es.add(l170m01e);
				}

				if (accountYN) {
					// TODO ICE 這裡要做測試效果是不是對的
					lms170502Service.saveL170m01bByBrNoCustId(branchId,
							l170m01a.getCustId(), l170m01a.getMainId(),
							l170m01a.getDupNo(), l170m01a);

					lms1705Service.getGuarantor(l170m01a.getCustId(),
							l170m01a.getDupNo(), l170m01a.getMainId());

					// TODO 引進擔保品資料
				}
				l170m01a.setLastRetrialDate(l180m01b.getElfLRDate());
				l170m01a.setNCkdFlag(nCkdFlag);
				l170m01as.add(l170m01a);

				// 引進前次信評資料
				importExL170m01e(l170m01a.getMainId(), l170m01a.getCustId(),
						l170m01a.getDupNo(), l170m01a.getOwnBrId());
			}
		}

		l170a01aDao.save(l170a01as);
		l170m01aDao.save(l170m01as);
		l170m01eDao.save(l170m01es);
		l180m01bDao.save(l180m01blist);

		for (L170M01A l170m01a : l170m01as) {
			lms1705Service.startFlow(l170m01a.getOid(), "");
		}

		try {
			flowAction(l180m01a.getOid(), l180m01a, false, false);
		} catch (Throwable e) {
			logger.error("LMS1805ServiceImpl produceReport EXCEPTION!!", e);
			return false;
		}

		return true;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public boolean saveL180m01aDate(String[] oids, Date defaultCTLDate) {
		List<L180M01A> listL180M01A = new ArrayList();
		for (String oid : oids) {
			L180M01A l180m01a = findModelByOid(L180M01A.class, oid);
			l180m01a.setDefaultCTLDate(defaultCTLDate);
			listL180M01A.add(l180m01a);
		}
		l180m01aDao.save(listL180M01A);
		return true;
	}

	@Override
	public void saveL180m01bCancel(String oid, String reason, Date newNextNwDt) {
		L180M01B l180m01b = findModelByOid(L180M01B.class, oid);
		if (l180m01b != null) {
			if ("PEO".equals(l180m01b.getCreateBY())) {
				delete(L180M01B.class, oid);
			} else {
				// 8:暫不覆審
				if ("8".equals(reason)) {
					l180m01b.setNewNextNwDt(newNextNwDt);
				}
				// 1:需覆審 2:不覆審
				l180m01b.setDocStatus1("2");
				l180m01b.setNewNCkdFlag(reason);
				save(l180m01b);
			}
		}
	}

	@Override
	public void saveL180m01bRecancel(String[] oids, Date newLRDate) {
		// List<L180M01B> listL180m01b = new ArrayList();
		for (String oid : oids) {
			L180M01B l180m01b = findModelByOid(L180M01B.class, oid);
			l180m01b.setDocStatus1("1");
			l180m01b.setNewLRDate(newLRDate);
			l180m01b.setNewNCkdFlag(null);
			l180m01b.setNewNCkdMemo(null);
			// listL180m01b.add(l180m01b);
			save(l180m01b);
		}
		// l180m01bDao.save(listL180m01b);
	}

	@Override
	public Map<String, Object> flowCases(String[] oids, Date defaultCTLDate,
			boolean apply) {
		// List<L180M01A> listL180M01A = new ArrayList<L180M01A>();
		// for (String oid : oids) {
		// L180M01A l180m01a = findModelByOid(L180M01A.class, oid);
		// l180m01a.setDefaultCTLDate(defaultCTLDate);
		// listL180M01A.add(l180m01a);
		// }
		// l180m01aDao.save(listL180M01A);
		Map<String, Object> map = new HashMap<String, Object>();
		StringBuilder reValue = new StringBuilder();
		Properties lms1805m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1805M01Page.class);
		for (String oid : oids) {
			L180M01A l180m01a = findModelByOid(L180M01A.class, oid);
			try {
				flowAction(l180m01a.getOid(), l180m01a, true, apply);
			} catch (Throwable e) {
				reValue.append("[" + l180m01a.getBranchId() + "]"
						+ lms1805m01.getProperty("L180M01A.dataDate") + ":["
						+ CapDate.formatDate(l180m01a.getDataDate(), "yyyy-MM")
						+ "]");
				logger.error("LMS1805ServiceImpl flowCases EXCEPTION!!", e);
			}
		}
		if (Util.isEmpty(reValue.toString())) {
			map.put("success", true);
		} else {
			map.put("msg", reValue.toString());
			map.put("success", false);
		}
		return map;
	}

	@Override
	public boolean getNumber(String oid)
			throws CapMessageException {
		L180M01A l180m01a = findModelByOid(L180M01A.class, oid);

		if (l180m01a.getBatchNO() == null || l180m01a.getBatchNO() == 0) {
			int batchNO = Util.parseInt(numberService.getNumberWithMax(
					L180M01A.class, l180m01a.getBranchId(),
					TWNDate.toAD(l180m01a.getDataDate()).substring(0, 4), 999));
			l180m01a.setBatchNO(batchNO);
			save(l180m01a);
			l180m01a = findModelByOid(L180M01A.class, oid);
		}
		projectSeq(l180m01a.getMainId());
		save(l180m01a);

		return true;
	}

	public void projectSeq(String mainId) {
		L180M01A l180m01a = l180m01aDao.findByMainId(mainId);
		List<L180M01B> l180m01bs = l180m01bDao.findByMainId(mainId, "");
		for (int i = 0, size = l180m01bs.size(); i < size; i++) {
			L180M01B l180m01b = l180m01bs.get(i);
			l180m01b.setProjectSeq(i + 1);
			String caseNumber = numberService.getCaseNumber(
					L180M01A.class, l180m01a.getBranchId(),
					TWNDate.toAD(l180m01a.getDataDate()).substring(0, 4),
					Util.addZeroWithValue(l180m01a.getBatchNO(), 3) + "-"
							+ Util.addZeroWithValue(i + 1, 3));
			l180m01b.setProjectNo(caseNumber);
			save(l180m01b);
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public List<Object[]> l170M01Aproject(String mainId) {
		L180M01A l180m01a = l180m01aDao.findByMainId(mainId);
		List<L180M01B> l180m01bs = l180m01bDao.findByMainId(mainId, "");
		int max = 0;
		for (L180M01B l180m01b : l180m01bs) {
			if (l180m01b.getProjectSeq() != null
					&& l180m01b.getProjectSeq() > max) {
				max = l180m01b.getProjectSeq();
			}
		}
		List<Object[]> reslut = new ArrayList();
		for (L180M01B l180m01b : l180m01bs) {

			if ("Y".equals(Util.trim(l180m01b.getNewBy170M01()))
					&& (l180m01b.getProjectNo() == null
							|| l180m01b.getProjectSeq() == null || l180m01b
							.getProjectNo().isEmpty())) {
				l180m01b.setProjectSeq(max + 1);
				StringBuilder projectNo = new StringBuilder();
				projectNo.append(TWNDate.toAD(l180m01a.getDataDate())
						.substring(0, 4));
				String branchname = branch
						.getBranchName(l180m01a.getBranchId());
				projectNo.append(branchname);
				projectNo.append("(兆)覆審字第");
				projectNo
						.append(Util.addZeroWithValue(l180m01a.getBatchNO(), 3));
				projectNo.append("-");
				projectNo.append(Util.addZeroWithValue(max + 1, 3));
				projectNo.append("號");
				l180m01b.setProjectNo(projectNo.toString());
				save(l180m01b);
				Object[] object = { max + 1, projectNo.toString() };
				reslut.add(object);
			}
		}
		return reslut;
	}

	@Override
	public boolean checkOneUpdate(String branch) {
		Date dataDate = CapDate.parseDate(CapDate
				.getCurrentDate(UtilConstants.DateFormat.YYYY_MM) + "-01");
		L180M01Z l180m01z = l180m01zDao.findByUniqueKey(dataDate, branch);
		if (l180m01z == null) {
			return false;
		}

		return true;
	}

	@Override
	public boolean produceExcel(String oid) {
		// 是否產EXCEL成功
		boolean st = false;
		DocFile docFile = null;

		L180M01A l180m01a = findModelByOid(L180M01A.class, oid);
		if (l180m01a == null) {
			l180m01a = new L180M01A();
		}
		String ranMainId = l180m01a.getMainId();

		Properties lms1805m01pop = MessageBundleScriptCreator
				.getComponentResource(LMS1805M01Page.class);

		String listName = "listExcel";
		String dDateName = lms1805m01pop.getProperty("ctlDate");
		if (RetrialDocStatusEnum.編製中.getCode().equals(l180m01a.getDocStatus())) {
			listName = "listSearch";
			dDateName = lms1805m01pop.getProperty("L180M01A.defaultCTLDate");
		} else if (RetrialDocStatusEnum.待覆核.getCode().equals(
				l180m01a.getDocStatus())) {
			listName = "listWait";
			dDateName = lms1805m01pop.getProperty("L180M01A.defaultCTLDate");
		}

		try {

			docFile = new DocFile();
			docFile.setBranchId(l180m01a.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(ranMainId);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(listName);
			docFile.setSrcFileName(lms1805m01pop.getProperty(listName)
					+ "_"
					+ lms1805m01pop.getProperty("l180m01a.docStatus"
							+ l180m01a.getDocStatus()) + ".xls");
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(fileService.getSysId());
			docFile.setData(new byte[] {});
			fileService.save(docFile, false);

			try {
				st = this.createExcel(l180m01a.getOwnBrId(), ranMainId, listName,
						dDateName, l180m01a, lms1805m01pop, docFile);
			} catch (FileNotFoundException e) {
				logger.error(StrUtils.getStackTrace(e));
			}

		} finally {
			if (docFile != null) {
				docFile = null;
			}
		}
		return st;

	}

	/**
	 * 設定 xls的title
	 * 
	 * @param sheet
	 * @param format14Left
	 *            字型
	 * @param x
	 *            X軸位置
	 * @param y
	 *            Y軸位置
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private HSSFSheet setExcel3Title(HSSFSheet sheet,
			HSSFCellStyle format12Center, int x, int y, Properties prop) {

		HSSFRow row0 = sheet.getRow(0);
		if (row0 == null) {
		    row0 = sheet.createRow(0);
		}
		row0.setHeight((short) 500);

		HSSFRow row1 = sheet.getRow(1);
		if (row1 == null) {
		    row1 = sheet.createRow(1);
		}
		row1.setHeight((short) 450);

		HSSFRow row2 = sheet.getRow(2);
		if (row2 == null) {
		    row2 = sheet.createRow(2);
		}
		row2.setHeight((short) 700);

		// 設定欄寬 (POI 單位是 1/256 個字元寬度)
		sheet.setColumnWidth(x + 0, 5 * 256);
		sheet.setColumnWidth(x + 1, 14 * 256);
		sheet.setColumnWidth(x + 2, 12 * 256);
		sheet.setColumnWidth(x + 3, 6 * 256);
		sheet.setColumnWidth(x + 4, 18 * 256);
		sheet.setColumnWidth(x + 5, 12 * 256);
		sheet.setColumnWidth(x + 6, 10 * 256);
		sheet.setColumnWidth(x + 7, 6 * 256);
		sheet.setColumnWidth(x + 8, 8 * 256);
		sheet.setColumnWidth(x + 9, 19 * 256);
		
		HSSFRow targetRow = sheet.getRow(y);
		if (targetRow == null) {
		    targetRow = sheet.createRow(y);
		}
		
		HSSFCell cell0 = targetRow.createCell(x + 0);
		cell0.setCellValue("");
		cell0.setCellStyle(format12Center);

		HSSFCell cell1 = targetRow.createCell(x + 1);
		cell1.setCellValue(prop.getProperty("createExcel.title02"));
		cell1.setCellStyle(format12Center);

		HSSFCell cell2 = targetRow.createCell(x + 2);
		cell2.setCellValue(prop.getProperty("createExcel.title03"));
		cell2.setCellStyle(format12Center);

		HSSFCell cell3 = targetRow.createCell(x + 3);
		cell3.setCellValue(prop.getProperty("createExcel.title04"));
		cell3.setCellStyle(format12Center);

		HSSFCell cell4 = targetRow.createCell(x + 4);
		cell4.setCellValue(prop.getProperty("createExcel.title05"));
		cell4.setCellStyle(format12Center);

		HSSFCell cell5 = targetRow.createCell(x + 5);
		cell5.setCellValue(prop.getProperty("createExcel.title14"));
		cell5.setCellStyle(format12Center);

		HSSFCell cell6 = targetRow.createCell(x + 6);
		cell6.setCellValue("DBU/OBU\n" + prop.getProperty("eLF412_DBUOBU"));
		cell6.setCellStyle(format12Center);

		HSSFCell cell7 = targetRow.createCell(x + 7);
		cell7.setCellValue(prop.getProperty("createExcel.title07"));
		cell7.setCellStyle(format12Center);

		HSSFCell cell8 = targetRow.createCell(x + 8);
		cell8.setCellValue(prop.getProperty("createExcel.title10"));
		cell8.setCellStyle(format12Center);

		HSSFCell cell9 = targetRow.createCell(x + 9);
		cell9.setCellValue(prop.getProperty("createExcel.title09"));
		cell9.setCellStyle(format12Center);

		return sheet;
	}

	/**
	 * 產生覆審名單EXCEL
	 * 
	 * @param ownBrId
	 * @param ranMainId
	 * @param listName
	 * @param l180m01a
	 * @param lms1805m01pop
	 * @param dDateName
	 * @param docfile
	 * @return
	 * @throws FileNotFoundException 
	 */
	private boolean createExcel(String ownBrId, String ranMainId,
			String listName, String dDateName, L180M01A l180m01a,
			Properties lms1805m01pop, DocFile docfile) throws FileNotFoundException {
		HSSFWorkbook workbook = null;
		HSSFSheet sheet = null;
		HSSFFont font10 = null;
		HSSFFont font12 = null;
		HSSFFont font12Other = null;
		HSSFFont font14 = null;
		HSSFCellStyle format14Left = null;
		HSSFCellStyle format12Center = null;
		HSSFCellStyle format12CenterNO = null;
		HSSFCellStyle format12CenterOther = null;
		HSSFCellStyle format12Left = null;
		HSSFCellStyle format10LeftNO = null;
		HSSFCellStyle format12LeftNO = null;
		HSSFCellStyle format12RightNO = null;
		HSSFCellStyle format10RightNO = null;
		HSSFCellStyle format12Right = null;
		PrintSetup setting = null;
		String filename = LMSUtil.getUploadFilePath(ownBrId, ranMainId,
				listName);
		String xlsOid = docfile.getOid();
		File file = null;
		file = new File(filename);
		file.mkdirs();
		file = new File(filename + "/" + xlsOid + ".xls");
		FileOutputStream fileOut = new FileOutputStream(file);
		StringBuffer str = new StringBuffer();
		StringBuilder memo = new StringBuilder();

		try {
			
			workbook = new HSSFWorkbook();
			sheet = workbook.createSheet(lms1805m01pop.getProperty(listName));
			setting = sheet.getPrintSetup();
		    setting.setPaperSize(PrintSetup.A4_PAPERSIZE);
		    setting.setScale((short) 80);

			String branchName = branch.getBranchName(l180m01a.getBranchId());
			font14 = workbook.createFont();
			font14.setFontName("新細明體");
			font14.setFontHeightInPoints((short) 14);
			font14.setBold(true);
			
			font12 = workbook.createFont();
			font12.setFontName("新細明體");
			font12.setFontHeightInPoints((short) 12);
			font12.setBold(false);

			font10 = workbook.createFont();
			font10.setFontName("新細明體");
			font10.setFontHeightInPoints((short) 10);
			font10.setBold(false);

			font12Other = workbook.createFont();
			font12Other.setFontName("標楷體");
			font12Other.setFontHeightInPoints((short) 12);
			font12Other.setBold(false);
			
			// 儲存格樣式
			format12Center = workbook.createCellStyle();
			format12Center.setFont(font12);
			format12Center.setAlignment(HorizontalAlignment.CENTER);
			format12Center.setVerticalAlignment(VerticalAlignment.TOP);
			format12Center.setWrapText(true);
			format12Center.setBorderTop(BorderStyle.THIN);
			format12Center.setBorderBottom(BorderStyle.THIN);
			format12Center.setBorderLeft(BorderStyle.THIN);
			format12Center.setBorderRight(BorderStyle.THIN);
			
			format12CenterOther = workbook.createCellStyle();
			format12CenterOther.setFont(font12Other);
			format12CenterOther.setAlignment(HorizontalAlignment.CENTER);
			format12CenterOther.setVerticalAlignment(VerticalAlignment.TOP);
			format12CenterOther.setWrapText(true);
			format12CenterOther.setBorderTop(BorderStyle.THIN);
			format12CenterOther.setBorderBottom(BorderStyle.THIN);
			format12CenterOther.setBorderLeft(BorderStyle.THIN);
			format12CenterOther.setBorderRight(BorderStyle.THIN);
			// format12CenterOther.setBackground(jxl.format.Colour.LIGHT_GREEN);

			format12CenterNO = workbook.createCellStyle();
			format12CenterNO.setFont(font12);
			format12CenterNO.setAlignment(HorizontalAlignment.CENTER);
			format12CenterNO.setVerticalAlignment(VerticalAlignment.TOP);
			format12CenterNO.setWrapText(false);
			
			format12Left = workbook.createCellStyle();
			format12Left.setFont(font12);
			format12Left.setAlignment(HorizontalAlignment.LEFT);
			format12Left.setVerticalAlignment(VerticalAlignment.TOP);
			format12Left.setWrapText(true);
			format12Left.setBorderTop(BorderStyle.THIN);
			format12Left.setBorderBottom(BorderStyle.THIN);
			format12Left.setBorderLeft(BorderStyle.THIN);
			format12Left.setBorderRight(BorderStyle.THIN);

			format12LeftNO = workbook.createCellStyle();
			format12LeftNO.setFont(font12);
			format12LeftNO.setAlignment(HorizontalAlignment.LEFT);
			format12LeftNO.setVerticalAlignment(VerticalAlignment.TOP);
			format12LeftNO.setWrapText(false); 
			
			format12RightNO = workbook.createCellStyle();
			format12RightNO.setFont(font12);
			format12RightNO.setAlignment(HorizontalAlignment.RIGHT);
			format12RightNO.setVerticalAlignment(VerticalAlignment.TOP);
			format12RightNO.setWrapText(false); 

			format12Right = workbook.createCellStyle();
			format12Right.setFont(font12);
			format12Right.setAlignment(HorizontalAlignment.RIGHT);
			format12Right.setVerticalAlignment(VerticalAlignment.TOP);
			format12Right.setWrapText(true);
			format12Right.setBorderTop(BorderStyle.THIN);
			format12Right.setBorderBottom(BorderStyle.THIN);
			format12Right.setBorderLeft(BorderStyle.THIN);
			format12Right.setBorderRight(BorderStyle.THIN);

			format14Left = workbook.createCellStyle();
			format14Left.setFont(font14);
			format14Left.setAlignment(HorizontalAlignment.LEFT);
			format14Left.setVerticalAlignment(VerticalAlignment.TOP);
			format14Left.setWrapText(false); 
			format14Left.setBorderTop(BorderStyle.THIN);
			format14Left.setBorderBottom(BorderStyle.THIN);
			format14Left.setBorderLeft(BorderStyle.THIN);
			format14Left.setBorderRight(BorderStyle.THIN);

			format10RightNO = workbook.createCellStyle();
			format10RightNO.setFont(font10);
			format10RightNO.setAlignment(HorizontalAlignment.RIGHT);
			format10RightNO.setVerticalAlignment(VerticalAlignment.TOP);
			format10RightNO.setWrapText(false); 

			format10LeftNO = workbook.createCellStyle();
			format10LeftNO.setFont(font10);
			format10LeftNO.setAlignment(HorizontalAlignment.LEFT);
			format10LeftNO.setVerticalAlignment(VerticalAlignment.TOP);
			format10LeftNO.setWrapText(false); 

			sheet = this.setExcel3Title(sheet, format12CenterOther, 0, 2,
					lms1805m01pop);
			// 合併單元格
			HSSFRow row0 = sheet.getRow(0);
			if (row0 == null) {
			    row0 = sheet.createRow(0);
			}

			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 3, 9));
			// 標題
			HSSFCell cell0_0 = row0.createCell(0);
			cell0_0.setCellValue(lms1805m01pop.getProperty("createExcel2.title03") + ":"
			        + Util.trim(l180m01a.getBranchId()));
			cell0_0.setCellStyle(format14Left);

			// 設定第二個標題儲存格 (3, 0)
			HSSFCell cell3_0 = row0.createCell(3);
			cell3_0.setCellValue(Util.nullToSpace(branchName) + "【"
			        + lms1805m01pop.getProperty(listName) + "】");
			cell3_0.setCellStyle(format14Left);
			// 預計覆審日期
			HSSFRow row1 = sheet.getRow(1);
			if (row1 == null) {
			    row1 = sheet.createRow(1);
			}
			HSSFCell cell0_1 = row1.createCell(0);
			cell0_1.setCellValue(dDateName + "："
			        + Util.nullToSpace(TWNDate.toAD(l180m01a.getDefaultCTLDate())));
			cell0_1.setCellStyle(format12LeftNO);
			
			// 資料日期
			HSSFRow row2 = sheet.getRow(1);
			if (row2 == null) {
			    row2 = sheet.createRow(1);
			}
			sheet.addMergedRegion(new CellRangeAddress(1, 1, 6, 9));

			// 合併區域的左上角
			HSSFCell cell6_1 = row2.createCell(6);

			if (RetrialDocStatusEnum.編製中.getCode().equals(l180m01a.getDocStatus())
			        || RetrialDocStatusEnum.待覆核.getCode().equals(l180m01a.getDocStatus())) {
			    String assignYMValue;
			    if (l180m01a.getDataDate() != null) {
			        // 假設 TWNDate.toAD() 返回的是可以 substring 的字串格式，例如 "YYYY/MM/DD"
			        assignYMValue = TWNDate.toAD(l180m01a.getDataDate()).substring(0, 7);
			    } else {
			        assignYMValue = "";
			    }
			    cell6_1.setCellValue(lms1805m01pop.getProperty("assignYM") + "：" + assignYMValue);
			} else {
			    cell6_1.setCellValue("");
			}

			cell6_1.setCellStyle(format12CenterNO);
			
			ISearch isearch = l180m01bDao.createSearchTemplete();
			isearch.addSearchModeParameters(SearchMode.EQUALS, "mainId",
					l180m01a.getMainId());
			isearch.addOrderBy("projectSeq");
			isearch.addOrderBy("docStatus1");
			isearch.addOrderBy("elfLRDate");
			isearch.addOrderBy("custId");
			isearch.setMaxResults(Integer.MAX_VALUE);
			List<L180M01B> l180m01bs = l180m01bDao.find(isearch);
			int count = 1;
			for (L180M01B l180m01b : l180m01bs) {
				// if (!RetrialDocStatusEnum.編製中.getCode().equals(
				// l180m01a.getDocStatus())
				// && !RetrialDocStatusEnum.待覆核.getCode().equals(
				// l180m01a.getDocStatus())) {
				// if ("8".equals(l180m01b.getNewNCkdFlag())) {
				// continue;
				// }
				// }
				if ((RetrialDocStatusEnum.已核准.getCode().equals(
						l180m01a.getDocStatus()) || RetrialDocStatusEnum.已產生覆審名單報告檔
						.getCode().equals(l180m01a.getDocStatus()))
						&& "8".equals(Util.trim(l180m01b.getNewNCkdFlag()))) {
					continue;
				}
				HSSFRow dataRow = sheet.getRow(count + 2);
				if (dataRow == null) {
				    dataRow = sheet.createRow(count + 2);
				}
				// 序號
				HSSFCell cell0 = dataRow.createCell(0);
				cell0.setCellValue(String.valueOf(count));
				cell0.setCellStyle(format12Center);
				// 統一編號
				HSSFCell cell1 = dataRow.createCell(1);
				cell1.setCellValue(Util.nullToSpace(l180m01b.getCustId()));
				cell1.setCellStyle(format12Left);
				// 信用評等
				str.setLength(0);
				if (!"".equals(Util.trim(l180m01b.getElfCrdTTbl()))) {
					str.append(lms1805m01pop.getProperty("createExcel.title15"))
							.append(":")
							.append(Util.trim(l180m01b.getElfCrdTTbl()));
				}
				if (!"".equals(Util.trim(l180m01b.getElfMowTbl1()))) {
					CodeType codetype = this.codeTypeService
							.findByCodeTypeAndCodeValue("lms1705s01_crdType2",
									Util.nullToSpace(l180m01b.getElfMowType()));
					if (str.length() > 0) {
						str.append("\n");
					}
					str.append(
							(Util.isEmpty(codetype) ? l180m01b.getElfMowType()
									: codetype.getCodeDesc())).append(":")
							.append(Util.trim(l180m01b.getElfMowTbl1()));
				}
				
				HSSFRow currentRow = sheet.getRow(count + 2);
				if (currentRow == null) {
				    currentRow = sheet.createRow(count + 2);
				}
				HSSFCell cell2 = currentRow.createCell(2);
				cell2.setCellValue(str.toString());
				cell2.setCellStyle(format12Left);
				
				// 是否主要客戶
				HSSFCell cell3 = currentRow.createCell(3);
				cell3.setCellValue(Util.nullToSpace(l180m01b.getElfMainCust()));
				cell3.setCellStyle(format12Center);
				// 客戶名稱
				HSSFCell cell4 = currentRow.createCell(4);
				cell4.setCellValue(Util.nullToSpace(l180m01b.getElfCName()));
				cell4.setCellStyle(format12Left);
				// 前次覆審日期
				HSSFCell cell5 = currentRow.createCell(5);
				cell5.setCellValue(Util.nullToSpace(TWNDate.toAD(l180m01b.getElfLRDate())));
				cell5.setCellStyle(format12Center);
				//
				HSSFCell cell6 = currentRow.createCell(6);
				cell6.setCellValue("N".equals(Util.nullToSpace(l180m01b.getElfDBUOBU())) ? ""
				        : Util.nullToSpace(l180m01b.getElfDBUOBU()));
				cell6.setCellStyle(format12Center);
				// 覆審週期
				HSSFCell cell7 = currentRow.createCell(7);
				cell7.setCellValue(Util.nullToSpace(l180m01b.getElfRCkdLine()));
				cell7.setCellStyle(format12Center);

				String nckdFlag = Util.trim(l180m01b.getNewNCkdFlag());
				if (nckdFlag == null || nckdFlag.isEmpty()) {
					nckdFlag = l180m01b.getElfNCkdFlag();
				}
				// 不覆審註記
				HSSFRow dataRowForNckd = sheet.getRow(count + 2);
				if (dataRowForNckd == null) {
				    dataRowForNckd = sheet.createRow(count + 2);
				}
				HSSFCell cell8ForNckd = dataRowForNckd.createCell(8);
				cell8ForNckd.setCellValue(Util.nullToSpace(nckdFlag));
				cell8ForNckd.setCellStyle(format12Center);
				memo.setLength(0);
				String elfNewDate = "";
				if (Util.trim(l180m01b.getElfNewDate()).length() >= 6) {
					elfNewDate = l180m01b.getElfNewDate().substring(0, 4) + "/"
							+ l180m01b.getElfNewDate().substring(4, 6);
				}
				if ("C".equals(l180m01b.getElfNewAdd())) {
					memo.append(elfNewDate).append(" ")
							.append(lms1805m01pop.getProperty("newaddC"));
				} else if ("N".equals(l180m01b.getElfNewAdd())) {
					memo.append(elfNewDate).append(" ")
							.append(lms1805m01pop.getProperty("newaddN"));
				} else if ("R".equals(l180m01b.getElfNewAdd())) {
					memo.append(elfNewDate).append(" ")
							.append(lms1805m01pop.getProperty("newaddR"));
				}

				if (Util.isNotEmpty(Util.trim(l180m01b.getElfMDFlag()))) {
					if (memo.length() > 0) {
						memo.append(";");
					}
					memo.append(lms1805m01pop.getProperty("mdFlag"))
							.append(":")
							.append(TWNDate.toAD(l180m01b.getElfMDDt()));
				}

				if (Util.isNotEmpty(l180m01b.getElfMemo())) {
					if (memo.length() > 0) {
						memo.append(";");
					}
					memo.append(l180m01b.getElfMemo());
				}
				if (Util.isNotEmpty(l180m01b.getNewNCkdMemo())) {
					if (memo.length() > 0) {
						memo.append(";");
					}
					memo.append(l180m01b.getNewNCkdMemo());
				}
				// 備註
				HSSFRow dataRowForMemo = sheet.getRow(count + 2);
				if (dataRowForMemo == null) {
				    dataRowForMemo = sheet.createRow(count + 2);
				}
				HSSFCell cell9ForMemo = dataRowForMemo.createCell(9);
				cell9ForMemo.setCellValue(Util.nullToSpace(memo.toString()));
				cell9ForMemo.setCellStyle(format12Left);
				count++;
			}
			// 報表亂碼
			HSSFRow dataRow = sheet.getRow(count + 2);
			if (dataRow == null) {
			    dataRow = sheet.createRow(count + 2);
			}

			// 合併儲存格 (0, count + 2) 到 (9, count + 2)
			// POI 參數為：(firstRow, lastRow, firstCol, lastCol)
			sheet.addMergedRegion(new CellRangeAddress(count + 2, count + 2, 0, 9));

			// 建立儲存格 (0, count + 2)，這是合併區域的左上角
			HSSFCell cell = dataRow.createCell(0);
			cell.setCellValue(lms1805m01pop.getProperty("L180M01A.generateDate") + "："
			        + TWNDate.toAD(CapDate.getCurrentTimestamp()) + " / "
			        + lms1805m01pop.getProperty("randomCode") + "："
			        + Util.nullToSpace(l180m01a.getRandomCode()));
			cell.setCellStyle(format12RightNO);

			if (RetrialDocStatusEnum.編製中.getCode().equals(
					l180m01a.getDocStatus())
					|| RetrialDocStatusEnum.待覆核.getCode().equals(
							l180m01a.getDocStatus())) {
				HSSFRow infoRowPrimary = sheet.getRow(count + 3);
				if (infoRowPrimary == null) {
				    infoRowPrimary = sheet.createRow(count + 3);
				}
				infoRowPrimary.setHeight((short) 450);
				// 加入 "nckdflagInfo" 標籤
				HSSFCell infoCellPrimary = infoRowPrimary.createCell(0);
				infoCellPrimary.setCellValue(lms1805m01pop.getProperty("nckdflagInfo"));
				infoCellPrimary.setCellStyle(format10LeftNO);		
				
				// 不覆審代碼說明
				Map<String, String> map = codeTypeService
						.findByCodeType("lms1815m01_elfNCkdFlag");
				if (!Util.isEmpty(map)) {
					Object[] values = map.values().toArray();
				    str.setLength(0); 
				    int length = values.length;

				    // 第一組說明 (最多 4 個)
				    if (values.length >= 4) {
				        for (int i = 0; i < 4; i++) {
				            str.append((i + 1) + "." + values[i]);
				        }
				    } else {
				        for (int i = 0; i < length; i++) {
				            str.append((i + 1) + "." + values[i]);
				        }
				    }
				    if (str.length() > 0) {
				        int currentRowIdx = count + 4 + 0;
				        HSSFRow dataRow0 = sheet.getRow(currentRowIdx);
				        if (dataRow0 == null) {
				            dataRow0 = sheet.createRow(currentRowIdx);
				        }
				        dataRow0.setHeight((short) 450);
				        HSSFCell cell0 = dataRow0.createCell(0);
				        cell0.setCellValue(str.toString());
				        cell0.setCellStyle(format10LeftNO);
				    }

				    str.setLength(0); 

				    if (values.length >= 8) {
				        for (int i = 4; i < 8; i++) {
				            str.append((i + 1) + "." + values[i]);
				        }
				    } else {
				        for (int i = 4; i < length; i++) {
				            str.append((i + 1) + "." + values[i]);
				        }
				    }
				    if (str.length() > 0) {
				        int currentRowIdx = count + 4 + 1;
				        HSSFRow dataRow1 = sheet.getRow(currentRowIdx);
				        if (dataRow1 == null) {
				            dataRow1 = sheet.createRow(currentRowIdx);
				        }
				        dataRow1.setHeight((short) 450);
				        HSSFCell cell1 = dataRow1.createCell(0);
				        cell1.setCellValue(str.toString());
				        cell1.setCellStyle(format10LeftNO);
				    }

				    str.setLength(0); 

				    if (values.length >= 12) {
				        for (int i = 8; i < 12; i++) {
				            str.append((i + 1) + "." + values[i]);
				        }
				    } else {
				        for (int i = 8; i < length; i++) {
				            str.append((i + 1) + "." + values[i]);
				        }
				    }
				    if (str.length() > 0) {
				        int currentRowIdx = count + 4 + 2;
				        HSSFRow dataRow2 = sheet.getRow(currentRowIdx);
				        if (dataRow2 == null) {
				            dataRow2 = sheet.createRow(currentRowIdx);
				        }
				        dataRow2.setHeight((short) 450);
				        HSSFCell cell2 = dataRow2.createCell(0);
				        cell2.setCellValue(str.toString());
				        cell2.setCellStyle(format10LeftNO);
				    }
				}
			}

			workbook.write(fileOut);
			return true;

		} catch (Exception e) {
			logger.error("LMS1805ServiceImpl createExcel EXCEPTION!!", e);
			return false;
		} finally {
			if (workbook != null) {
		        try {
		            workbook.close();
		        } catch (IOException e) {
		        	logger.error(e.getMessage());
		        }
		    }
		    if (fileOut != null) {
		        try {
		            fileOut.close();
		        } catch (IOException e) {
		        	logger.error(e.getMessage());
		        }
		    }
		}
	}

	/**
	 * 設定 xls chk的title
	 * 
	 * @param sheet
	 * @param format14Left
	 *            字型
	 * @param x
	 *            X軸位置
	 * @param y
	 *            Y軸位置
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	private HSSFSheet setExcel2Title(HSSFSheet sheet,
			HSSFCellStyle format12Center, int x, int y, Properties prop) {
		sheet.setColumnWidth(x + 0, 5 * 256);
        sheet.setColumnWidth(x + 1, 6 * 256);
        sheet.setColumnWidth(x + 2, 14 * 256);
        sheet.setColumnWidth(x + 3, 18 * 256);
        sheet.setColumnWidth(x + 4, 6 * 256);
        sheet.setColumnWidth(x + 5, 20 * 256);
        sheet.setColumnWidth(x + 6, 8 * 256);
        sheet.setColumnWidth(x + 7, 6 * 256);
        sheet.setColumnWidth(x + 8, 13 * 256);
        sheet.setColumnWidth(x + 9, 20 * 256);
        sheet.setColumnWidth(x + 10, 8 * 256);
        
        HSSFRow targetRow = sheet.getRow(y);
        if (targetRow == null) {
            targetRow = sheet.createRow(y);
        }
		
        HSSFCell cell0 = targetRow.createCell(x + 0);
        cell0.setCellValue("");
        cell0.setCellStyle(format12Center);

        HSSFCell cell1 = targetRow.createCell(x + 1);
        cell1.setCellValue(prop.getProperty("createExcel.title01"));
        cell1.setCellStyle(format12Center);

        HSSFCell cell2 = targetRow.createCell(x + 2);
        cell2.setCellValue(prop.getProperty("createExcel.title02"));
        cell2.setCellStyle(format12Center);

        HSSFCell cell3 = targetRow.createCell(x + 3);
        cell3.setCellValue(prop.getProperty("createExcel.title03"));
        cell3.setCellStyle(format12Center);

        HSSFCell cell4 = targetRow.createCell(x + 4);
        cell4.setCellValue(prop.getProperty("createExcel.title04"));
        cell4.setCellStyle(format12Center);

        HSSFCell cell5 = targetRow.createCell(x + 5);
        cell5.setCellValue(prop.getProperty("createExcel.title05"));
        cell5.setCellStyle(format12Center);

        HSSFCell cell6 = targetRow.createCell(x + 6);
        cell6.setCellValue(prop.getProperty("createExcel.title06"));
        cell6.setCellStyle(format12Center);

        HSSFCell cell7 = targetRow.createCell(x + 7);
        cell7.setCellValue(prop.getProperty("createExcel.title07"));
        cell7.setCellStyle(format12Center);

        HSSFCell cell8 = targetRow.createCell(x + 8);
        cell8.setCellValue(prop.getProperty("createExcel.title08"));
        cell8.setCellStyle(format12Center);

        HSSFCell cell9 = targetRow.createCell(x + 9);
        cell9.setCellValue(prop.getProperty("createExcel.title09"));
        cell9.setCellStyle(format12Center);

        HSSFCell cell10 = targetRow.createCell(x + 10);
        cell10.setCellValue(prop.getProperty("createExcel.title10"));
        cell10.setCellStyle(format12Center);
		return sheet;
	}

	/**
	 * 產生驗證名單EXCEL
	 * 
	 * @param ownBrId
	 * @param ranMainId
	 * @param listName
	 * @param l180m01a
	 * @param lms1805m01pop
	 * @param dDateName
	 * @param docfile
	 * @return
	 * @throws FileNotFoundException 
	 * @throws WriteException
	 * @throws RowsExceededException
	 */
	private String createChkExcel(String ownBrId, String ranMainId,
			L180M01A l180m01a, Properties lms1805m01pop, DocFile docfile) throws FileNotFoundException {
		HSSFWorkbook workbook = null;
		HSSFSheet sheet = null;
		HSSFFont font12 = null;
		HSSFFont font12Other = null;
		HSSFFont font14 = null;
		HSSFCellStyle format14Center = null;
		HSSFCellStyle format12Center = null;
		HSSFCellStyle format12CenterOther = null;
		HSSFCellStyle format12Left = null;
		HSSFCellStyle format12LeftNO = null;
		HSSFCellStyle format12RightNO = null;
		HSSFCellStyle format12Right = null;
		PrintSetup setting = null;
		String filename = LMSUtil.getUploadFilePath(ownBrId, ranMainId,
				"listChk");
		String xlsOid = docfile.getOid();
		File file = null;
		file = new File(filename);
		file.mkdirs();
		file = new File(filename + "/" + xlsOid + ".xls");
		FileOutputStream fileOut = new FileOutputStream(file);

		StringBuffer str = new StringBuffer();
		try {
			workbook = new HSSFWorkbook();
			sheet = workbook.createSheet(
					lms1805m01pop.getProperty("createExcel.title00"));

			setting = sheet.getPrintSetup();
			setting.setPaperSize(PrintSetup.A4_PAPERSIZE);
			setting.setScale((short) 70);
			// setting.setTopMargin(1.00); // 上
			// setting.setBottomMargin(1.00); // 下
			// setting.setLeftMargin(1.00); // 左
			// setting.setRightMargin(1.00); // 右
			// // 空白：页眉、页脚
			// setting.setHeaderMargin(1.00); // 页眉
			// setting.setFooterMargin(1.00); // 页脚

			String branchName = branch.getBranchName(l180m01a.getBranchId());
			
			font14 = workbook.createFont();
			font14.setFontName("新細明體");
			font14.setFontHeightInPoints((short) 14);
			font14.setBold(true);
			
			font12 = workbook.createFont();
			font12.setFontName("新細明體");
			font12.setFontHeightInPoints((short) 12);
			font12.setBold(false); // WritableFont.NO_BOLD

			font12Other = workbook.createFont();
			font12Other.setFontName("標楷體");
			font12Other.setFontHeightInPoints((short) 12);
			font12Other.setBold(false);
			
			format12Center = workbook.createCellStyle();
			format12Center.setFont(font12);
			format12Center.setAlignment(HorizontalAlignment.CENTER);
			format12Center.setVerticalAlignment(VerticalAlignment.TOP);
			format12Center.setWrapText(true);
			format12Center.setBorderTop(BorderStyle.THIN);
			format12Center.setBorderBottom(BorderStyle.THIN);
			format12Center.setBorderLeft(BorderStyle.THIN);
			format12Center.setBorderRight(BorderStyle.THIN);
			
			format12CenterOther = workbook.createCellStyle();
			format12CenterOther.setFont(font12Other);
			format12CenterOther.setAlignment(HorizontalAlignment.CENTER);
			format12CenterOther.setVerticalAlignment(VerticalAlignment.TOP);
			format12CenterOther.setWrapText(true);
			format12CenterOther.setBorderTop(BorderStyle.THIN);
			format12CenterOther.setBorderBottom(BorderStyle.THIN);
			format12CenterOther.setBorderLeft(BorderStyle.THIN);
			format12CenterOther.setBorderRight(BorderStyle.THIN);
			// format12CenterOther.setBackground(jxl.format.Colour.LIGHT_GREEN);
			
			format12Left = workbook.createCellStyle();
			format12Left.setFont(font12);
			format12Left.setAlignment(HorizontalAlignment.LEFT);
			format12Left.setVerticalAlignment(VerticalAlignment.TOP);
			format12Left.setWrapText(true);
			format12Left.setBorderTop(BorderStyle.THIN);
			format12Left.setBorderBottom(BorderStyle.THIN);
			format12Left.setBorderLeft(BorderStyle.THIN);
			format12Left.setBorderRight(BorderStyle.THIN);
			
			format12LeftNO = workbook.createCellStyle();
			format12LeftNO.setFont(font12);
			format12LeftNO.setAlignment(HorizontalAlignment.LEFT);
			format12LeftNO.setVerticalAlignment(VerticalAlignment.TOP);
			format12LeftNO.setWrapText(false);
			
			format12RightNO = workbook.createCellStyle();
			format12RightNO.setFont(font12);
			format12RightNO.setAlignment(HorizontalAlignment.RIGHT);
			format12RightNO.setVerticalAlignment(VerticalAlignment.TOP);
			format12RightNO.setWrapText(false);
			
			format12Right = workbook.createCellStyle();
			format12Right.setFont(font12);
			format12Right.setAlignment(HorizontalAlignment.RIGHT);
			format12Right.setVerticalAlignment(VerticalAlignment.TOP);
			format12Right.setWrapText(true);
			format12Right.setBorderTop(BorderStyle.THIN);
			format12Right.setBorderBottom(BorderStyle.THIN);
			format12Right.setBorderLeft(BorderStyle.THIN);
			format12Right.setBorderRight(BorderStyle.THIN);
			
			format14Center = workbook.createCellStyle();
			format14Center.setFont(font14);
			format14Center.setAlignment(HorizontalAlignment.CENTER);
			format14Center.setVerticalAlignment(VerticalAlignment.TOP);
			format14Center.setWrapText(false);
			format14Center.setBorderTop(BorderStyle.THIN);
			format14Center.setBorderBottom(BorderStyle.THIN);
			format14Center.setBorderLeft(BorderStyle.THIN);
			format14Center.setBorderRight(BorderStyle.THIN);

			sheet = this.setExcel2Title(sheet, format12CenterOther, 0, 2,
					lms1805m01pop);
			// 合併單元格
			HSSFRow row0 = sheet.getRow(0);
			if (row0 == null) {
			    row0 = sheet.createRow(0);
			}
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 10));
			// 標題
			HSSFCell cell = row0.createCell(0);
			cell.setCellValue(Util.trim(l180m01a.getBranchId())
			        + Util.nullToSpace(branchName)
			        + lms1805m01pop.getProperty("listChk"));
			cell.setCellStyle(format14Center);

			String dataDate = null;
			if (!Util.isEmpty(l180m01a.getDataDate())) {
			    dataDate = TWNDate.toAD(l180m01a.getDataDate());
			}
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			
			HSSFRow row1 = sheet.getRow(1);
			if (row1 == null) {
			    row1 = sheet.createRow(1);
			}

			HSSFCell cell0_1 = row1.createCell(0);
			cell0_1.setCellValue(lms1805m01pop.getProperty("ctlDate") + "：" + Util.nullToSpace(dataDate));
			cell0_1.setCellStyle(format12LeftNO);
			sheet.addMergedRegion(new CellRangeAddress(1, 1, 8, 10));

			HSSFCell cell8_1 = row1.createCell(8);
			cell8_1.setCellValue(lms1805m01pop.getProperty("createExcel.title11") + "：" + user.getUserCName());
			cell8_1.setCellStyle(format12RightNO);

			ISearch isearch = l180m01bDao.createSearchTemplete();
			isearch.setMaxResults(Integer.MAX_VALUE);
			isearch.addSearchModeParameters(SearchMode.EQUALS, "mainId",
					l180m01a.getMainId());
			isearch.addOrderBy("projectSeq");
			isearch.addOrderBy("docStatus1");
			isearch.addOrderBy("elfLRDate");
			isearch.addOrderBy("custId");
			List<L180M01B> l180m01bs = l180m01bDao.find(isearch);

			int count = 1;
			int alreadyCTL = 0;
			HSSFFont headFont12 = workbook.createFont();
			headFont12.setFontName("新細明體");
			headFont12.setFontHeightInPoints((short) 12);
			
			HSSFCellStyle NcellFormatL = workbook.createCellStyle();
			NcellFormatL.setFont(headFont12);
			NcellFormatL.setAlignment(HorizontalAlignment.LEFT);
			NcellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
			NcellFormatL.setWrapText(true);
			NcellFormatL.setBorderTop(BorderStyle.THIN);
			NcellFormatL.setBorderBottom(BorderStyle.THIN);
			NcellFormatL.setBorderLeft(BorderStyle.THIN);
			NcellFormatL.setBorderRight(BorderStyle.THIN);

			Map<String, String> crdType2Map = codeTypeService
					.findByCodeType("lms1705s01_crdType2");
			if (crdType2Map == null) {
				crdType2Map = new LinkedHashMap<String, String>();
			}
			for (L180M01B l180m01b : l180m01bs) {
				List<Map<String, Object>> lms412List = lms412Service
						.findLms412ByCustId(Util.trim(l180m01a.getBranchId()),
								Util.trim(l180m01b.getCustId()),
								Util.trim(l180m01b.getDupNo()));
				Map<String, Object> lms412Map = null;
				if (lms412List.size() > 0) {
					lms412Map = lms412List.get(0);
				} else {
					lms412Map = new LinkedHashMap<String, Object>();
				}
				str.setLength(0);
				// 如在覆審控制檔維護有此檔案不產excel 因為沒辦法回訊息所以不座椅下判斷
				// L181M01A l181m01a = l181m01aDao.findByBranchCustId(
				// l180m01a.getBranchId(), l180m01a.getCustId(),
				// l180m01a.getDupNo());
				// if (l181m01a != null &&
				// ("010".equals(l181m01a.getDocStatus()) ||
				// "020".equals(l181m01a.getDocStatus()))) {
				// workbook.write();
				// workbook.close();
				// return false;
				// }
				String nckdFlag = Util.trim(lms412Map.get("NCKDFLAG"));
				String elfMowType = Util.trim(lms412Map.get("MOWTYPE"));
				String elfMowTbl1 = Util.trim(lms412Map.get("MOWTBL1"));
				String elfCrdTTbl = Util.trim(lms412Map.get("CRDTTBL"));
				// Date elfLRDate = (Date)lms412Map.get("ELFLRDATE");
				String elfMainCust = Util.trim(lms412Map.get("MAINCUST"));
				String elfNewDate = Util.trim(lms412Map.get("NEWDATE"));
				String elfMDFlag = Util.trim(lms412Map.get("MDFLAG"));
				Date elfMDDt = (Date) lms412Map.get("MDDT");
				String elfRCkdLine = Util.trim(lms412Map.get("RCKDLINE"));
				// Date elfUCkdDt = (Date)lms412Map.get("ELFUCKDDT");
				String elfCName = Util.trim(lms412Map.get("CNAME"));
				String elfNewAdd = Util.trim(lms412Map.get("NEWADD"));
				String memo = Util.trim(lms412Map.get("MEMO"));
				String uckdLine = Util.trim(lms412Map.get("UCKDLINE"));

				// J-108-0078_05097_B1001
				// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
				String elfIsAllNew = Util.trim(lms412Map.get("ISALLNEW"));
				String elfBranch = Util.trim(lms412Map.get("BRANCH"));

				if ((RetrialDocStatusEnum.已核准.getCode().equals(
						l180m01a.getDocStatus()) || RetrialDocStatusEnum.已產生覆審名單報告檔
						.getCode().equals(l180m01a.getDocStatus()))
						&& "8".equals(Util.trim(l180m01b.getNewNCkdFlag()))) {
					continue;
				}
				
				HSSFRow dataRow = sheet.getRow(count + 2);
				if (dataRow == null) {
				    dataRow = sheet.createRow(count + 2);
				}

				HSSFCell cell0 = dataRow.createCell(0);
				cell0.setCellValue(String.valueOf(count));
				cell0.setCellStyle(format12Center);

				HSSFCell cell1 = dataRow.createCell(1);
				cell1.setCellValue(NumConverter.addComma(Util.trim(l180m01b.getProjectSeq()), "000"));
				cell1.setCellStyle(format12Left);

				HSSFCell cell2 = dataRow.createCell(2);
				cell2.setCellValue(l180m01b.getCustId());
				cell2.setCellStyle(format12Left);

				// CodeType codetype =
				// codeTypeService.findByCodeTypeAndCodeValue("lms1705s01_crdType2",
				// Util.nullToSpace(l180m01b.getElfMowType()));
				if (Util.isNotEmpty(elfMowType)) {
					str.append(Util.trim(crdType2Map.get(elfMowType)))
							.append(" : ").append(elfMowTbl1);
				}
				if (Util.isNotEmpty(elfCrdTTbl)) {
					if (str.length() > 0) {
						str.append("\n");
					}
					str.append(lms1805m01pop.get("createExcel.title15"))
							.append(" : ").append(elfCrdTTbl);
				}
				
				HSSFRow dataRow1 = sheet.getRow(count + 2);
				if (dataRow1 == null) {
				    dataRow1 = sheet.createRow(count + 2);
				}

				// 寫入第一個儲存格 (3, count + 2)
				HSSFCell cell3 = dataRow1.createCell(3);
				cell3.setCellValue(str.toString());
				cell3.setCellStyle(format12Left);

				// 寫入第二個儲存格 (4, count + 2)
				HSSFCell cell4 = dataRow1.createCell(4);
				cell4.setCellValue(elfMainCust);
				cell4.setCellStyle(format12Center);

				// 寫入第三個儲存格 (5, count + 2)
				HSSFCell cell5 = dataRow1.createCell(5);
				cell5.setCellValue(elfCName);
				cell5.setCellStyle(format12Left);

				L170M01A l170m01a = new L170M01A();
				ISearch search = l170m01aDao.createSearchTemplete();
				search.setMaxResults(Integer.MAX_VALUE);
				search.addSearchModeParameters(SearchMode.EQUALS, "projectSeq",
						l180m01b.getProjectSeq());
				search.addSearchModeParameters(SearchMode.EQUALS, "projectNo",
						l180m01b.getProjectNo());
				// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
				search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
						l180m01b.getCtlType());
				List<L170M01A> l170m01as = l170m01aDao.find(search);

				// 上傳控制檔或更新前次覆審
				String reportYN = UtilConstants.DEFAULT.否;
				if (!Util.isEmpty(l170m01as) && l170m01as.size() != 0) {
					l170m01a = l170m01as.get(0);
					L170M01F l170m01f = l170m01fDao.findByMainId(l170m01a
							.getMainId());
					if (l170m01f == null) {
						l170m01f = new L170M01F();
					}
					if (Util.isNotEmpty(l170m01f.getUpDate())) {
						reportYN = UtilConstants.DEFAULT.是;
						alreadyCTL++;
					}
				}
				HSSFRow dataRow2 = sheet.getRow(count + 2);
				if (dataRow2 == null) {
				    dataRow2 = sheet.createRow(count + 2);
				}

				HSSFCell cell6 = dataRow2.createCell(6);
				cell6.setCellValue(Util.nullToSpace(reportYN));
				cell6.setCellStyle(format12Center);

				// 重算覆審週期
				// if("THZ0073328".equals(l180m01b.getCustId())){
				// System.out.println(1);
				// }
				Object[] cauInfo = this.getRckLine(elfMDFlag, elfRCkdLine,
						elfNewAdd, uckdLine, elfMowType, elfMowTbl1,
						elfCrdTTbl, elfMainCust, elfIsAllNew, elfBranch);
				elfRCkdLine = Util.trim(cauInfo[0]);

				HSSFRow dataRow3 = sheet.getRow(count + 2);
				if (dataRow3 == null) {
				    dataRow3 = sheet.createRow(count + 2);
				}

				HSSFCell cell7 = dataRow3.createCell(7);
				cell7.setCellValue(Util.nullToSpace(elfRCkdLine));
				cell7.setCellStyle(format12Center);

				// 覆審人員
				String reportApply = userInfoService.getUserName(Util
						.nullToSpace(l170m01a.getUpdater()));
				
				HSSFRow dataRow4 = sheet.getRow(count + 2);
				if (dataRow4 == null) {
				    dataRow4 = sheet.createRow(count + 2);
				}

				HSSFCell cell8 = dataRow4.createCell(8);				
				if ("Y".equals(reportYN)) {
				    cell8.setCellValue(Util.isEmpty(reportApply) ? Util.nullToSpace(l170m01a.getUpdater()) : reportApply);
				} else {
				    cell8.setCellValue("");
				}
				cell8.setCellStyle(format12Left);

				StringBuilder memoStr = new StringBuilder();
				if ("THZ0066190".equals(l170m01a.getCustId())) {
					System.out.println(1);
				}
				if ("C".equals(elfNewAdd)) {
					memoStr.append(
							elfNewDate.length() >= 6 ? elfNewDate.subSequence(
									0, 4) + "-" + elfNewDate.substring(4, 6)
									: elfNewDate).append(" ")
							.append(lms1805m01pop.getProperty("newaddC"));
				} else if ("N".equals(elfNewAdd)) {
					memoStr.append(
							elfNewDate.length() >= 6 ? elfNewDate.subSequence(
									0, 4) + "-" + elfNewDate.substring(4, 6)
									: elfNewDate).append(" ")
							.append(lms1805m01pop.getProperty("newaddN"));
				} else if ("R".equals(elfNewAdd)) {
					memoStr.append(
							elfNewDate.length() >= 6 ? elfNewDate.subSequence(
									0, 4) + "-" + elfNewDate.substring(4, 6)
									: elfNewDate).append(" ")
							.append(lms1805m01pop.getProperty("newaddR"));
				}

				if (!Util.isEmpty(elfMDFlag)) {
					if (memoStr.length() > 0) {
						memoStr.append(";");
					}
					memoStr.append(
							Util.trim(TWNDate.toAD(elfMDDt)).replace("-", "/"))
							.append(" ")
							.append(lms1805m01pop.getProperty("mdFlag"));
				}

				if (Util.isNotEmpty(memo)) {
					if (memoStr.length() > 0) {
						memoStr.append(";");
					}
					memoStr.append(memo);
				}
				HSSFRow dataRow5 = sheet.getRow(count + 2);
				if (dataRow5 == null) {
				    dataRow5 = sheet.createRow(count + 2);
				}

				HSSFCell cell9 = dataRow5.createCell(9);
				cell9.setCellValue(Util.nullToSpace(memoStr));
				cell9.setCellStyle(format12Left);

				HSSFCell cell10 = dataRow5.createCell(10);
				cell10.setCellValue(nckdFlag);
				cell10.setCellStyle(format12Center);

				count++;
			}

			// 左側文字
			HSSFRow rowCountPlus3 = sheet.getRow(count + 3);
			if (rowCountPlus3 == null) {
			    rowCountPlus3 = sheet.createRow(count + 3);
			}

			// 第一個儲存格
			HSSFCell cell0_countPlus3 = rowCountPlus3.createCell(0);
			cell0_countPlus3.setCellValue(lms1805m01pop.getProperty("alreadyCTL") + "："
			        + Util.nullToSpace(alreadyCTL)
			        + lms1805m01pop.getProperty("alreadyCTLTotal"));
			cell0_countPlus3.setCellStyle(format12LeftNO);

			// 右側合併儲存格
			sheet.addMergedRegion(new CellRangeAddress(count + 3, count + 3, 5, 10));
			
			// 合併區域的左上角
			HSSFCell cell5_countPlus3 = rowCountPlus3.createCell(5);
			cell5_countPlus3.setCellValue(lms1805m01pop.getProperty("randomCode") + "："
			        + Util.nullToSpace(l180m01a.getRandomCode()));
			cell5_countPlus3.setCellStyle(format12RightNO);
			
			// 新行左側文字
			HSSFRow rowCountPlus4 = sheet.getRow(count + 4);
			if (rowCountPlus4 == null) {
			    rowCountPlus4 = sheet.createRow(count + 4);
			}

			// 第三個儲存格
			HSSFCell cell0_countPlus4 = rowCountPlus4.createCell(0);
			cell0_countPlus4.setCellValue(lms1805m01pop.getProperty("nckdflagInfo"));
			cell0_countPlus4.setCellStyle(format12LeftNO);

			// 不覆審代碼說明
			Map<String, String> map = codeTypeService
					.findByCodeType("lms1815m01_elfNCkdFlag");
			if (!Util.isEmpty(map)) {
				Object[] values = map.values().toArray();
				for (int i = 0, length = values.length; i < length; i++) {
					Object obj = values[i];
				    int currentRowIdx = count + 5 + i;
				    
				    HSSFRow dataRow = sheet.getRow(currentRowIdx);
				    if (dataRow == null) {
				        dataRow = sheet.createRow(currentRowIdx);
				    }

				    HSSFCell dataCell = dataRow.createCell(0);
				    dataCell.setCellValue((i + 1) + "." + obj.toString());
				    dataCell.setCellStyle(format12LeftNO);
				}
			}

			workbook.write(fileOut);
			
		
		} catch (IOException e) {
			logger.error(e.getMessage());
		} finally {
			if (workbook != null) {
		        try {
		            workbook.close();
		        } catch (IOException e) {
		        	logger.error(e.getMessage());
		        }
		    }
		    if (fileOut != null) {
		        try {
		            fileOut.close();
		        } catch (IOException e) {
		        	logger.error(e.getMessage());
		        }
		    }
		}
		return "";
	}

	@Override
	public Map<String, Object> produceChkExcel(String oid) throws Exception {
		Map<String, Object> msg = new HashMap<String, Object>();
		DocFile docFile = new DocFile();
		try {
			L180M01A l180m01a = findModelByOid(L180M01A.class, oid);
			if (l180m01a == null) {
				l180m01a = new L180M01A();
			}

			String ranMainId = l180m01a.getMainId();

			Properties lms1805m01Prop = MessageBundleScriptCreator
					.getComponentResource(LMS1805M01Page.class);

			docFile.setBranchId(l180m01a.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(ranMainId);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId("listChk");
			docFile.setSrcFileName(lms1805m01Prop.getProperty("listChk")
					+ "_"
					+ lms1805m01Prop.getProperty("l180m01a.docStatus"
							+ l180m01a.getDocStatus()) + ".xls");
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(fileService.getSysId());
			docFile.setData(new byte[] {});
			docFile.setDeletedTime(null);
			fileService.save(docFile, false);

			String msgs = this.createChkExcel(l180m01a.getOwnBrId(), ranMainId,
					l180m01a, lms1805m01Prop, docFile);
			if (Util.isNotEmpty(msgs)) {
				msg.put("success", false);
				msg.put("errorMag", msgs);
				return msg;
			}
			// ISearch search = docfileDao.createSearchTemplete();
			// search.addSearchModeParameters(SearchMode.EQUALS, "oid",
			// docFile.getOid());
			// docFile = docfileDao.findUniqueOrNone(search);
			// if(docFile != null){
			// docFile.setDeletedTime(null);
			// fileService.save(docFile);
			// }
		} finally {

		}
		msg.put("success", true);
		return msg;
	}

	@Override
	public void saveNckdFlag(String exl180m01aOid, String l180m01aOid) {
		L180M01A exl180m01a = findModelByOid(L180M01A.class, exl180m01aOid);
		L180M01A l180m01a = findModelByOid(L180M01A.class, l180m01aOid);

		eloandbBASEService.updateForNck(
				"已列為"
						+ Util.nullToSpace(TWNDate.toAD(exl180m01a
								.getDefaultCTLDate())) + "應覆審案件",
				exl180m01a.getDefaultCTLDate(), l180m01a.getMainId(),
				exl180m01a.getMainId());
	}

	@Override
	public void changeL180M01BDocstatus(String projectNo) {
		L180M01B l180m01b = l180m01bDao.findByProjectNo(projectNo);
		if (l180m01b != null) {
			// 已覆審
			l180m01b.setDocStatus1("3");
			save(l180m01b);
		}
	}

	@Override
	public L180M01A findL180m01aByMainId(String mainId) {

		return l180m01aDao.findByMainId(mainId);
	}

	@Override
	public Map<String, Object> findByBranchAnddataDate(String bank,
			Date basedate) {
		Properties lms1805v01 = MessageBundleScriptCreator
				.getComponentResource(LMS1805V01Page.class);
		Map<String, Object> map = new HashMap<String, Object>();
		boolean st = true;
		List<L180M01A> deletemodel = l180m01aDao.findByBranchAnddataDate(bank,
				basedate);
		if (Util.isEmpty(deletemodel) || deletemodel.size() == 0) {
			// 當沒有資料則可繼續
			st = true;
		} else {
			for (L180M01A l180m01a : deletemodel) {
				if (RetrialDocStatusEnum.待覆核.getCode().equals(
						l180m01a.getDocStatus())
				// || RetrialDocStatusEnum.已核准.getCode().equals(
				// l180m01a.getDocStatus())
				// || RetrialDocStatusEnum.已產生覆審名單報告檔.getCode().equals(
				// l180m01a.getDocStatus())
				) {
					st = false;
					map.put("mag", lms1805v01.getProperty("L180M01A.message2"));
					break;
				} else if (RetrialDocStatusEnum.編製中.getCode().equals(
						l180m01a.getDocStatus())) {
					st = false;
					break;
				}
			}
		}
		map.put("check", st);
		return map;
	}

	@Override
	public L180M01Z findByDataDateBranch(String branch, Date dataDate) {
		return l180m01zDao.findByUniqueKey(dataDate, branch);
	}

	public void importExL170m01e(String mainId, String custId, String DupNo,
			String ownBrId) {
		// 取得最新取得之前次資料
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> CrdType = codeTypeService
				.findByCodeType("lms1705s01_crdType");
		List<L170M01E> l170m01es = new ArrayList<L170M01E>();
		String exMainid = eloandbBASEService.findL170M01A_exMainid(mainId,
				custId, DupNo, ownBrId);
		if (Util.isNotEmpty(exMainid)) {
			List<L170M01E> exL170m01elist_L = lms1705Service
					.findL170m01eByMainId(exMainid, "T");
			if (!exL170m01elist_L.isEmpty()) {
				for (L170M01E exLl170m01e_L : exL170m01elist_L) {
					if (Util.isNotEmpty(Util.nullToSpace(CrdType.get(Util
							.trim(exLl170m01e_L.getCrdType()))))
							|| Util.equals(UtilConstants.Type.無資料_C,
									Util.trim(exLl170m01e_L.getCrdType()))) {
						if (!"".equals(Util.nullToSpace(Util.trim(exLl170m01e_L
								.getCrdType())))) {
							L170M01E l170m01e = new L170M01E();
							l170m01e.setMainId(mainId);
							l170m01e.setCustId(custId);
							l170m01e.setDupNo(DupNo);
							l170m01e.setCrdTYear(CapDate.parseDate(MINDATE));
							l170m01e.setCrdTBR("");
							l170m01e.setCntrNo("");
							l170m01e.setCrdType(Util.isEmpty(exLl170m01e_L
									.getCrdType()) ? "DL" : Util
									.trim(exLl170m01e_L.getCrdType()));
							l170m01e.setFinYear("");
							l170m01e.setGrade(Util.trim(exLl170m01e_L
									.getGrade()));
							l170m01e.setCreator(user.getUserId());
							l170m01e.setCreateTime(CapDate
									.getCurrentTimestamp());
							l170m01e.setUpdater(user.getUserId());
							l170m01e.setUpdateTime(CapDate
									.getCurrentTimestamp());
							l170m01e.setTimeFlag("L");
							l170m01es.add(l170m01e);
						}
					} else if (UtilConstants.crdTypeC.泰國GroupA.equals(Util
							.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeC.泰國GroupB.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeC.自訂.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeC.消金評等.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.Moody.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.中華信評.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.SP.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.Fitch.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.FitchTW.equals(Util
									.trim(exLl170m01e_L.getCrdType()))
							|| UtilConstants.crdTypeN.KBRA.equals(Util
									.trim(exLl170m01e_L.getCrdType()))) {
						// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					} else {
						if (!"".equals(Util.nullToSpace(Util.trim(exLl170m01e_L
								.getCrdType())))) {
							L170M01E l170m01e = new L170M01E();
							l170m01e.setMainId(mainId);
							l170m01e.setCustId(custId);
							l170m01e.setDupNo(DupNo);
							l170m01e.setCrdTYear(CapDate.parseDate(MINDATE));
							l170m01e.setCrdTBR("");
							l170m01e.setCntrNo("");
							l170m01e.setCrdType(Util.trim(exLl170m01e_L
									.getCrdType()));
							l170m01e.setFinYear("");
							l170m01e.setGrade(Util.trim(exLl170m01e_L
									.getGrade()));
							l170m01e.setCreator(user.getUserId());
							l170m01e.setCreateTime(CapDate
									.getCurrentTimestamp());
							l170m01e.setUpdater(user.getUserId());
							l170m01e.setUpdateTime(CapDate
									.getCurrentTimestamp());
							l170m01e.setTimeFlag("L");
							l170m01es.add(l170m01e);
						}
					}
				}
			}
		}
		l170m01eDao.save(l170m01es);
	}
}
