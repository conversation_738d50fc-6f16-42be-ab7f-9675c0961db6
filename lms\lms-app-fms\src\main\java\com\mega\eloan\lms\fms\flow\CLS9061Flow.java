package com.mega.eloan.lms.fms.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.dao.C900M01EDao;
import com.mega.eloan.lms.model.C900M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;


@Component
public class CLS9061Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "CLS9061Flow";


	@Resource
	C900M01EDao c900m01eDao;
	
	@Transition(node = "開始", value = "起案")
	public void init_flow(FlowInstance instance) {		
		String oid = Util.trim(instance.getId());
		
		C900M01E meta = c900m01eDao.findByOid(oid);
		{
			meta.setApprover(null);
			meta.setApproveTime(null);	
		}		
		c900m01eDao.save(meta);
	}
	
	
	@Transition(node = "確認", value = "核定")
	public void apply(FlowInstance instance) {
		String oid = Util.trim(instance.getId());
		
		C900M01E meta = c900m01eDao.findByOid(oid);
		{
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			meta.setApprover(user.getUserId());
			meta.setApproveTime(CapDate.getCurrentTimestamp());	
		}
		c900m01eDao.save(meta);
	}
	
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C900M01E.class;
	}


	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return FlowDocStatusEnum.class;
	}
}