/* 
 * LMS1501V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import java.util.ArrayList;
import java.util.Properties;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.lms.pages.LMS1505M01Page;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * [國內企金]小放會grid畫面
 * </pre>
 * 
 * @since 2012/11/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/29,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1501v00")
public class LMS1501V00Page extends AbstractEloanInnerView {

	@Autowired
	AuthService au;

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		
		// J-112-0057_05097_B1001 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// eloan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// 1.「帳戶管理員」改「覆核」
		// 2.「紀錄」改「經辦」
		// 3.取消「遵守法令主管」
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1501V00Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (user.getUnitNo().equals(UtilConstants.BankNo.授管處)) {
			model.addAttribute("L150M01a.accounting", prop.getProperty("L150M01a.approver")); // 覆核
			model.addAttribute("L150M01a.recorder", prop.getProperty("L150M01a.appraiser")); // 經辦
		} else {
			model.addAttribute("L150M01a.accounting", prop.getProperty("l150m01a.accounting")); // 帳戶管理員
			model.addAttribute("L150M01a.recorder", prop.getProperty("l150m01a.recorder")); // 紀錄
		}
		
		// 加上Button
		ArrayList<LmsButtonEnum> btns = new ArrayList<LmsButtonEnum>();
		if (this.getAuth(AuthType.Accept)) {
			btns.add(LmsButtonEnum.View);
		}

		if (this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.Add);
			btns.add(LmsButtonEnum.Delete);
			btns.add(LmsButtonEnum.Modify);
		}

		btns.add(LmsButtonEnum.Filter);

		addToButtonPanel(model, btns.toArray(new LmsButtonEnum[] {}));
		
		renderJsI18N(LMS1505M01Page.class);
		renderJsI18N(LMS1501V00Page.class);
		renderJsI18N(AbstractEloanPage.class);
		renderJsI18N(LMS1201V01Page.class);
		renderJsI18N(LMSCommomPage.class);
		model.addAttribute("loadScript", "require(['pagejs/lns/LMS1501V00Page'], function() { loadScript('pagejs/base/GridViewFilterPanel')});");


	}

	/*
	 * @Override public String getCodeId() { return "13004"; }
	 */

}
