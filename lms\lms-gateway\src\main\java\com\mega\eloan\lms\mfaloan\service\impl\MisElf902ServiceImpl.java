package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisElf902Service;


@Service
public class MisElf902ServiceImpl extends AbstractMFAloanJdbc implements MisElf902Service {

	@Override
	public Map<String, Object> findCnameByRcustIdAndDupNo1(String rcustid, String dupNo1) {
		return getJdbc().queryForMap("ELF902.findCNAMEByRCustIdAndDupNo", new Object[] { rcustid, dupNo1 });
	}
}
