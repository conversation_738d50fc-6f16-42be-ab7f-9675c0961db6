var initS08dJson = {
    isInit: false,
    grid: null,
    init: function(){
        if (!initS08dJson.isInit) {
            initS08dJson.grid = $("#L140M01OGrid").iGrid({
                handler: CLSAction.ghandle,
                height: 230,
                rownumbers: true,
                rowNum: 10,
                shrinkToFit: false,
                postData: {
                    formAction: "queryL140M01OByL120M01A"
                },
                colModel: [{
                    colHeader: i18n.clss08a["g.ownBrid"],//擔保品持有分行,
                    name: 'BRANCH',
                    align: "left",
                    width: 60,
                    sortable: true,
                    formatter: "click",
                    onclick: initS08dJson.openLink
                }, {
                    colHeader: i18n.clss08a["g.custId"],//客戶統編,
                    name: 'CUSTID',
                    align: "left",
                    width: 100,
                    sortable: true
                }, {
                    colHeader: i18n.clss08a["g.custName"],//客戶名稱,
                    name: 'CUSTNAME',
                    align: "left",
                    width: 150,
                    sortable: true
                }, {
                    colHeader: i18n.clss08a["L120S08D.001"],//擔保品編號,
                    name: 'COLLNO',
                    align: "left",
                    width: 120,
                    sortable: true
                }, {
                    colHeader: i18n.clss08a["L120S08D.002"],//擔保品大類,
                    name: 'COLLTYP1',
                    align: "left",
                    width: 120,
                    sortable: true
                }, //				 {
                //                    colHeader: i18n.clss08a["L120S08D.003"],//擔保品小類,
                //                    name: 'COLLTYP2',
                //                    align: "left",
                //                    width: 120,
                //                    sortable: true
                //                },
                {
                    colHeader: i18n.clss08a["g.docStatusNM"],//文件狀態,
                    name: 'DOCSTATUS',
                    align: "center",
                    width: 120,
                    sortable: true
                }, {
                    name: 'CMSOID',
                    hidden: true
                }],
                ondblClickRow: function(rowid){
                    var data = initS08dJson.grid.getRowData(rowid);
                    initS08dJson.openLink(null, null, data);
                }
            });
            initS08dJson.isInit = true;
        }
    },
    openBox: function(){
        initS08dJson.init();
        $("#openBoxL140M01OGrid").thickbox({
            title: i18n.def.confirmTitle,
            width: 800,
            height: 400,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = initS08dJson.grid;
                    //單筆
                    var rowData = $grid.getSingleData();
                    if (rowData) {
                        initS08dJson.openLink(null, null, rowData);
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    openLink: function(cellvalue, options, rowObject){
        $.ajax({ // 查詢主要借款人資料
            handler: CLSAction.fhandle,
            type: "POST",
            dataType: "json",
            action: "getRelate3",
            data: {
                CMSOID: rowObject.CMSOID
            },
            success: function(json){
            	
            	var isHis = "N";
    			if(json.OWNBRID == userInfo.unitNo){
    				isHis = "Y";
    			}
    			
                // 擔保品
                // 舊方法
                BrowserAction.submit({
                    system: "cms",
                    url: json.url,
                    mainId: json.mainId,
                    txCode: json.txCode,
                    oid: json.oid,
                    mainOid: json.mainOid,
                    mainDocStatus: json.mainDocStatus,
                    data: {
                        //其它參數
                        //txCode : json.txCode,
                        oid: json.oid,
                        mainOid: json.mainOid,
                        mainDocStatus: json.mainDocStatus,
                        collKind: json.collKind,
                        collTyp2: json.collTyp2,
                        //不動產
                        pndFlag: json.pndFlag,
                        isHis: isHis
                    }
                });
            }
        });
        
    }
};
$(document).ready(function() {
	//J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
	$("#docDscr5 a").live( "click", function() {
		//擔保品估價報告書
		getCms(DOMPurify.sanitize(this.title));
	});
	
});
/**
 * 擔保品分頁查詢
 */
function seachKind5(){
    initS08dJson.openBox();
}
