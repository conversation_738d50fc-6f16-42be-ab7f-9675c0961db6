package com.mega.eloan.lms.lms.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.dao.L785M01ADao;
import com.mega.eloan.lms.lms.pages.LMS7850M01Page;
import com.mega.eloan.lms.lms.service.LMS7850Service;
import com.mega.eloan.lms.model.L785M01A;
import com.mega.eloan.lms.model.L999LOG01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 停權解除維護Grid Handler
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7850gridhandler")
public class LMS7850GridHandler extends AbstractGridHandler {

	@Resource
	LMS7850Service service7850;

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	L785M01ADao l785m01aDao;

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL785m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7850M01Page.class);
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		// 只能看到自己單位 自己的案子 與自己放行的案子
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());

		switch (docStatusEnum) {
		case 授管處_停權編製中:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.授管處_停權編製中.getCode());

			pageSetting.addSearchModeParameters(
					SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"creator", user.getUserId()),
							new SearchModeParameter(SearchMode.EQUALS,
									"updater", user.getUserId())),
					new SearchModeParameter(SearchMode.EQUALS, "approver", user
							.getUserId()));

			break;
		case 授管處_停權待覆核:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.授管處_停權待覆核.getCode());
			break;
		case 授管處_停權已覆核:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.授管處_停權已覆核.getCode());

			pageSetting.addSearchModeParameters(
					SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"creator", user.getUserId()),
							new SearchModeParameter(SearchMode.EQUALS,
									"updater", user.getUserId())),
					new SearchModeParameter(SearchMode.EQUALS, "approver", user
							.getUserId()));

			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);

			pageSetting.addSearchModeParameters(
					SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"creator", user.getUserId()),
							new SearchModeParameter(SearchMode.EQUALS,
									"updater", user.getUserId())),
					new SearchModeParameter(SearchMode.EQUALS, "approver", user
							.getUserId()));

			break;
		}

		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service7850.findPage(L785M01A.class,
				pageSetting);
		List<L785M01A> l785m01as = (List<L785M01A>) page.getContent();

		Locale locale = null;
		locale = LMSUtil.getLocale();
		Map<String, String> typCdMap = codetypeservice.findByCodeType("TypCd",
				locale.toString());

		Map<String, String> l120M01A_docTypeMap = codetypeservice
				.findByCodeType("L120M01A_docType", locale.toString());

		Map<String, String> l120M01A_docKindMap = codetypeservice
				.findByCodeType("L120M01A_docKind", locale.toString());

		Map<String, String> l120M01A_docCodeMap = codetypeservice
				.findByCodeType("L120M01A_docCode", locale.toString());

		List<Map<String, Object>> m01List = new ArrayList<Map<String, Object>>();

		for (L785M01A model : l785m01as) {

			Map<String, Object> m01Map = new HashMap<String, Object>();

			m01Map.put("oid", model.getOid());
			m01Map.put("mainId", model.getMainId());
			m01Map.put("docURL", model.getDocURL());
			m01Map.put("caseDate", model.getCaseDate());
			m01Map.put("caseNo", model.getCaseNo());

			// 經辦顯示
			if (!Util.isEmpty(model.getUpdater())) {
				m01Map.put(
						"updater",
						!Util.isEmpty(userservice.getUserName(model
								.getUpdater())) ? userservice.getUserName(model
								.getUpdater()) : Util.trim(model.getUpdater()));
			} else {
				m01Map.put("updater", getPerName(Util.trim(model.getUpdater())));
			}

			// 覆核主管顯示
			if (!Util.isEmpty(model.getApprover())) {
				m01Map.put(
						"approver",
						!Util.isEmpty(userservice.getUserName(model
								.getApprover())) ? userservice
								.getUserName(model.getApprover()) : Util
								.trim(model.getApprover()));
			} else {
				m01Map.put("approver",
						getPerName(Util.trim(model.getApprover())));
			}

			m01Map.put("approveTime", model.getApproveTime() == null ? ""
					: CapDate.formatDate(model.getApproveTime(), "yyyy-MM-dd"));

			String formatItemDscr = LMSUtil.getFormatItemDscr(
					Util.trim(model.getItemDscr()), prop, typCdMap,
					l120M01A_docTypeMap, l120M01A_docKindMap,
					l120M01A_docCodeMap);

			m01Map.put("itemDscrShow", formatItemDscr);

			L999LOG01A l999log01a = model.getL999log01a();
			if (l999log01a != null) {
				m01Map.put(
						"result",
						Util.equals(Util.trim(l999log01a.getResult()), "") ? ""
								: prop.getProperty(
										"L784M01A.Result_"
												+ Util.trim(l999log01a
														.getResult()), ""));
				m01Map.put("exeCount", l999log01a.getExeCount());
			} else {
				m01Map.put("result", "");
				m01Map.put("exeCount", "");
			}

			m01List.add(m01Map);

		}

		// Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
		// m01List, m01List.size(), pageSetting.getMaxResults(),
		// pageSetting.getFirstResult());

		// return new CapMapGridResult(returnPage.getContent(),
		// returnPage.getTotalRow());

		return new CapMapGridResult(m01List, page.getTotalRow());

	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id) {
		return (!Util.isEmpty(userservice.getUserName(id)) ? userservice
				.getUserName(id) : id);
	}

	/**
	 * 取得流水序號
	 * 
	 * @param setDocNo
	 *            完整案號
	 * @return 流水序號
	 */
	private String getSeqNo(String setDocNo) {
		int lmsIndex = setDocNo.toUpperCase().indexOf("LMS");
		int clsIndex = setDocNo.toUpperCase().indexOf("CLS");
		if (lmsIndex != -1) {
			return LMSUtil.checkSubStr(setDocNo, lmsIndex + 3) ? setDocNo
					.substring(lmsIndex + 3) : null;
		} else if (clsIndex != -1) {
			return LMSUtil.checkSubStr(setDocNo, clsIndex + 3) ? setDocNo
					.substring(clsIndex + 3) : null;
		} else {
			return null;
		}
	}

}
