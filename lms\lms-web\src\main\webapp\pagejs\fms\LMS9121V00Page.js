var L912VAction = {
    fhandler: "lms9121formhandler",
    $form: $("#lms9121tabForm"),
    gridId: "#gridview",
    /**
     * 更新grid
     */
    _tiggerGrid: function(){
        $(L912VAction.gridId).trigger("reloadGrid");
    }
};
$(function(){
    // 檔案上傳grid
    var L910M01aGrid = $('#gridfile').iGrid({
        handler: "lms9121gridhandler",
        height: 350,
        scroll: 200,
        rowNum: 200,
        sortable: false,
        loadtext: i18n.lms9121v00['L912V00.Updating'],
        colModel: [{
            colHeader: i18n.lms9121v00['L912V00.UpdateTable'],// 更新Table,
            name: 'UpdateTable',
            width: 120,
            align: "left",
            sortable: false
        }, {
            colHeader: i18n.lms9121v00['L912V00.UpdateEnd'],// 更新結果
            name: 'UpdateResult',
            width: 140,
            align: "center",
            sortable: false
        }]
    });
    $("#UpdateCntrNo").click(function(){
        openCntrNoUpdate();
    });
    $("#UpdateCustID").click(function(){
        openCustIdUpdate();
    });
});
function filterGrid(sendData){
    $("#gridfile").jqGrid("setGridParam", {
        postData: $.extend({
            formAction: "queryL912cntrNoupdate",
            docStatus: viewstatus,
            scroll: 200,
            rowNum: 200,
            sortable: false,
            type: $("[name=queryData]:checked").val()
        }, sendData || {})
    }).trigger("reloadGrid");
}

function filterGridcustId(sendData){
    $("#gridfile").jqGrid("setGridParam", {
        postData: $.extend({
            formAction: "queryL912custIdupdate",
            docStatus: viewstatus,
            scroll: 200,
            rowNum: 200,
            sortable: false,
            type: $("[name=queryData]:checked").val()
        }, sendData || {})
    }).trigger("reloadGrid");
}

/**
 * 開啟視窗(額度序號相同對象不同明細表)
 */
function openCntrNoUpdate(){
    $("#lms9121new").thickbox({
        title: i18n.lms9121v00["L912V00.UpdateCntrNo"],
        width: 500,
        height: 130,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var $form = $('#lms9121tabForm');
                if ($("#lms9121tabForm").valid()) {
                    filterGrid({
                        oldcntrNo: $form.find("#oldcntrNo").val(),
                        newcntrNo: $form.find("#newcntrNo").val()
                    });
                }
                $.thickbox.close();
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}

/**
 * 開啟視窗(客戶統編修正)
 */
function openCustIdUpdate(){
    $("#lms9121updatecustId").thickbox({
        title: i18n.lms9121v00["L912V00.UpdateCustID"],
        width: 450,
        height: 160,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var $form = $('#lms9121upcusttabForm');
                if ($("#lms9121upcusttabForm").valid()) {
                    filterGridcustId({
                        oldcustId: $form.find("#oldcustId").val(),
                        olddupNo: $form.find("#olddupNo").val(),
                        newcustId: $form.find("#newcustId").val(),
                        newdupNo: $form.find("#newdupNo").val()
                    });
                }
                $.thickbox.close();
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}
