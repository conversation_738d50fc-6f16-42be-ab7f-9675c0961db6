/* 
 * C241M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 一般/團貸覆審項目檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="C241M01C", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo","itemType","itemNo"}))
public class C241M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 覆審類別<p/>
	 * A, B
	 */
	@Column(name="ITEMTYPE", length=1, columnDefinition="CHAR(1)")
	private String itemType;

	/** 
	 * 覆審項次<p/>
	 * 覆審項目維護檔的固定項次<br/>
	 *  (與管理報表統計有關)<br/>
	 *  eg.A001, A002…, B001, B002…
	 */
	@Column(name="ITEMNO", length=4, columnDefinition="VARCHAR(4)")
	private String itemNo;

	/** 
	 * 序號<p/>
	 * 畫面顯示順序
	 */
	@Column(name="ITEMSEQ", columnDefinition="DECIMAL(5,0)")
	private Integer itemSeq;

	/** 
	 * 覆審項目<p/>
	 * 110個全型字
	 */
	@Column(name="CHKITEM", length=330, columnDefinition="VARCHAR(330)")
	private String chkItem;

	/** 
	 * 覆審結果<p/>
	 * 是|Y<br/>
	 *  否|N<br/>
	 *  －|K
	 */
	@Column(name="CHKRESULT", length=1, columnDefinition="VARCHAR(1)")
	private String chkResult;

	/** 
	 * 異常內容說明(memo)
	 */
	@Column(name="CHKTEXT", length=1500, columnDefinition="VARCHAR(1500)")
	private String chkText;

	/** 
	 * 有無支票存款檢核<p/>
	 * 只供「徵信事項_借戶支票存款往來情形（□有無支票存款）」檢核使用
	 */
	@Column(name="CHKCHECK", length=1, columnDefinition="VARCHAR(1)")
	private String chkCheck;

	/** 
	 * 前次覆審項目檢核<p/>
	 * 只供「債權確保_前次覆審有無應行改善事項？」檢核使用
	 */
	@Column(name="CHKPREREVIEW", length=1, columnDefinition="VARCHAR(1)")
	private String chkPreReview;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 記點項目 **/
	@Column(name="PTITEM", length=1, columnDefinition="VARCHAR(1)")
	private String ptItem;
	
	/** 記點經理 **/
	@Column(name="PTMGRID", length=6, columnDefinition="VARCHAR(6)")
	private String ptMgrId;
	
	/** 記點點數 **/
	@Digits(integer=1, fraction=0)
	@Column(name="PTVAL", columnDefinition="DEC(1,0)")
	private BigDecimal ptVal; 
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得覆審類別<p/>
	 * A, B
	 */
	public String getItemType() {
		return this.itemType;
	}
	/**
	 *  設定覆審類別<p/>
	 *  A, B
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 
	 * 取得覆審項次<p/>
	 * 覆審項目維護檔的固定項次<br/>
	 *  (與管理報表統計有關)<br/>
	 *  eg.A001, A002…, B001, B002…
	 */
	public String getItemNo() {
		return this.itemNo;
	}
	/**
	 *  設定覆審項次<p/>
	 *  覆審項目維護檔的固定項次<br/>
	 *  (與管理報表統計有關)<br/>
	 *  eg.A001, A002…, B001, B002…
	 **/
	public void setItemNo(String value) {
		this.itemNo = value;
	}

	/** 
	 * 取得序號<p/>
	 * 畫面顯示順序
	 */
	public Integer getItemSeq() {
		return this.itemSeq;
	}
	/**
	 *  設定序號<p/>
	 *  畫面顯示順序
	 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/** 
	 * 取得覆審項目<p/>
	 * 110個全型字
	 */
	public String getChkItem() {
		return this.chkItem;
	}
	/**
	 *  設定覆審項目<p/>
	 *  110個全型字
	 **/
	public void setChkItem(String value) {
		this.chkItem = value;
	}

	/** 
	 * 取得覆審結果<p/>
	 * 是|Y<br/>
	 *  否|N<br/>
	 *  －|K
	 */
	public String getChkResult() {
		return this.chkResult;
	}
	/**
	 *  設定覆審結果<p/>
	 *  是|Y<br/>
	 *  否|N<br/>
	 *  －|K
	 **/
	public void setChkResult(String value) {
		this.chkResult = value;
	}

	/** 
	 * 取得異常內容說明(memo)
	 */
	public String getChkText() {
		return this.chkText;
	}
	/**
	 *  設定異常內容說明(memo)
	 **/
	public void setChkText(String value) {
		this.chkText = value;
	}

	/** 
	 * 取得有無支票存款檢核<p/>
	 * 只供「徵信事項_借戶支票存款往來情形（□有無支票存款）」檢核使用
	 */
	public String getChkCheck() {
		return this.chkCheck;
	}
	/**
	 *  設定有無支票存款檢核<p/>
	 *  只供「徵信事項_借戶支票存款往來情形（□有無支票存款）」檢核使用
	 **/
	public void setChkCheck(String value) {
		this.chkCheck = value;
	}

	/** 
	 * 取得前次覆審項目檢核<p/>
	 * 只供「債權確保_前次覆審有無應行改善事項？」檢核使用
	 */
	public String getChkPreReview() {
		return this.chkPreReview;
	}
	/**
	 *  設定前次覆審項目檢核<p/>
	 *  只供「債權確保_前次覆審有無應行改善事項？」檢核使用
	 **/
	public void setChkPreReview(String value) {
		this.chkPreReview = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/** 取得記點項目 **/
	public String getPtItem() {
		return ptItem;
	}
	/** 設定記點項目 **/
	public void setPtItem(String ptItem) {
		this.ptItem = ptItem;
	}
	
	/** 取得記點經理 **/
	public String getPtMgrId() {
		return ptMgrId;
	}
	/** 設定記點經理 **/
	public void setPtMgrId(String ptMgrId) {
		this.ptMgrId = ptMgrId;
	}
	
	/** 取得記點點數 **/
	public BigDecimal getPtVal() {
		return ptVal;
	}
	/** 設定記點點數 **/
	public void setPtVal(BigDecimal ptVal) {
		this.ptVal = ptVal;
	}
}
