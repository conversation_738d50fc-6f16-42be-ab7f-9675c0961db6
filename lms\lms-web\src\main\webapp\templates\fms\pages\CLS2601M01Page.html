<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:extend>
		<script type="text/javascript" src="pagejs/fms/CLS2601M01Page.js"></script>
        <div class="button-menu funcContainer" id="buttonPanel">
        	<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnSave" />
				
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" />
					<wicket:message key="button.save">儲存</wicket:message>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" />
					<wicket:message key="button.send">呈主管覆核</wicket:message>
				</button>				
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnWAIT_APPROVE" />
				
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" />
					<wicket:message key="button.check">覆核</wicket:message>
				</button>
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnWAIT_REMOVE" />
				
				<button type="button" id="btnAcceptRemove">
					<span class="ui-icon ui-icon-check" />
					<wicket:message key="button.check">解除覆核</wicket:message>
				</button>
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnRemove" />				
				<button type="button" id="btnRemove">
					<!-- 解除 -->
					<span class="ui-icon ui-icon-unlocked" />
					<wicket:message key="button.remove"/>
				</button>	
			</wicket:enclosure>        	
			
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<wicket:message key="button.exit">離開</wicket:message>
			</button>
		</div>	
			
		<div class="tit2 color-black">
            <table width="100%">
                <tr>
                    <td width="100%">
                    	<wicket:message key="doc.title">地政士黑名單建檔作業</wicket:message>
					</td>
                </tr>
            </table>
        </div>
		
		<div style='padding:1em;'>
        <form id="tabForm">
        	<fieldset>
                <legend>
                    <b><wicket:message key="label.basicData">基本資料</wicket:message></b>
                </legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                	<tr>
							<td width="20%" class="hd1" nowrap>
								<input type="text" class="hide" id="mainOid" name="mainOid" />
	                            <wicket:message key="doc.custName">姓名/名稱</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td width="30%">
	                        	<input type="text" id="custName" name="custName" class="required" maxlength="10" size="12" />
	                        </td>
							<td width="20%" class="hd1" nowrap>
	                            <wicket:message key="C900M01H.ownBrId">分行別</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td width="30%">
								<input type="text" id="ownBrId" name="ownBrId"  class="hide required" readonly  />
	                        	<input type="text" id="ownBrIdDesc" name="ownBrIdDesc" class="" readonly />
	                        </td>
	                    </tr>
						<tr>
	                        <td width="20%" class="hd1" nowrap>
	                            <wicket:message key="doc.docStatus">文件狀態</wicket:message>&nbsp;&nbsp;								
	                        </td>
	                        <td colspan='3'>
								<b><span id="docStatus" class="color-red" /></b>
	                        </td>
	                    </tr>
						<tr>
	                        <td width="20%" class="hd1" nowrap>
	                            <wicket:message key="C900M01H.agentCert">證書</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                            (<input type="text" id="agentCertYear" name="agentCertYear" class="digits required" maxlength="3" size="1" />)&nbsp;
								<input type="text" id="agentCertWord" name="agentCertWord" class="required" maxlength="4" size="7" /><wicket:message key="C900M01H.agentCertWord">字第</wicket:message>&nbsp;
								<input type="text" id="agentCertNo" name="agentCertNo" class="digits required" maxlength="6" size="3" /><wicket:message key="C900M01H.agentCertNo">號</wicket:message>&nbsp;<br/>
								&nbsp;<br/>
        						<span wicket:id="C101S01E_laaWord_url"><!--地政士url--></span>
								<span style='padding-right:5px;' /><span id="href_LaaNoticeItem" class="text-red">※<u style="cursor:pointer;">填寫說明</u></span> <br/>
	                        </td>
	                    </tr>
						<tr>
	                        <td width="20%" class="hd1" nowrap>
	                            <wicket:message key="C900M01H.ctlFlag">控管原因</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                            <select space="true" combokey="cls260CtlFlagType" combotype="2" id="ctlFlag" name="ctlFlag" style="width:350px" class="required">
	                                <!--
	                                <option></option>
	                                <option>1.經稽核處查核確定或疑似人頭戶案件</option>
	                                <option>2.經分行查詢實價登錄價格與買賣契約價格不符之案件</option>
	                                <option>3.經分行發現疑似製作假文件之案件</option>
	                                <option>4.同業發生人頭戶房貸案經媒體揭露或受金管會裁罰案例</option>
	                                -->
	                            </select>
	                        </td>
	                    </tr>
						<tr>
							<td width="20%" class="hd1" nowrap>
	                            <wicket:message key="C900M01H.memo">備註</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                        	<textarea name="memo" id="memo" cols="87" rows="3" class="txt_mult" maxlength="300" maxlengthC="100" ></textarea>
	                        </td>
	                    </tr>
						<tr>
							<td width="20%" class="hd1">
	                            <wicket:message key="C900M01H.CtlFlagType5_select1">是否屬永慶房屋直營店或信義房屋名義仲介成交案件</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                        	<input type="checkbox" name="estateAgentFlag" id="estateAgentFlag"  value="Y" disabled="true"  maxlength="1" size="1" />
	                        </td>
	                    </tr>
						<tr>
							<td width="20%" class="hd1">
	                            <wicket:message key="C900M01H.CtlFlagType5_select2">懲戒紀錄是否屬地政士法第17條,即地政士未自己處理受託事務</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                        	<input type="checkbox" name="record17Flag" id="record17Flag" value="Y" disabled="true" maxlength="1" size="1" />
	                        </td>
	                    </tr>
						
					</tbody>
	            </table>
			</fieldset>
			
			<fieldset>
                    <legend>
                        <b><wicket:message key="doc.docUpdateLog">文件異動紀錄</wicket:message></b>
                    </legend>
                    <div class="funcContainer">
                        <div wicket:id="_docLog">
                        </div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.creator">文件建立者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="creator" name="creator"></span>
                                    (<span id="createTime" name="createTime"></span>)
                                </td>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.lastUpdater">最後異動者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="updater" name="updater"></span>
                                    (<span id="updateTime" name="updateTime"></span>)
                                </td>
                            </tr>                            
                        </tbody>
                    </table>
                </fieldset>
        </form>			
		</div>		
	</wicket:extend>
</body>
</html>
