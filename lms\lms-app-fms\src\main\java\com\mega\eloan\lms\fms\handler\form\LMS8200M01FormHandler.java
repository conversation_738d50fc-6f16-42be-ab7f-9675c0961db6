package com.mega.eloan.lms.fms.handler.form;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.gwclient.IdentificationCheckGwClient;
import com.mega.eloan.common.gwclient.IdentificationCheckGwReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.dao.C101S01ADao;
import com.mega.eloan.lms.dao.L820M01CDao;
import com.mega.eloan.lms.dao.L820M01EDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.fms.pages.LMS8200M01Page;
import com.mega.eloan.lms.fms.service.LMS8200Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.L820M01A;
import com.mega.eloan.lms.model.L820M01B;
import com.mega.eloan.lms.model.L820M01C;
import com.mega.eloan.lms.model.L820M01E;
import com.mega.eloan.lms.model.L820M01S;
import com.mega.eloan.lms.model.L820M01W;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 以房養老貸款撥款前查詢結果
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8200m01formhandler")
//@DomainClass(L820M01A.class)
public class LMS8200M01FormHandler extends AbstractFormHandler {
	
	@Resource
	DwdbBASEService dwdbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	LMS8200Service lms8200Service;
	
	@Resource
	BranchService branchService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	IdentificationCheckGwClient identificationCheckGwClient;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	L820M01CDao l820m01cDao;
	
	@Resource
	L820M01EDao l820m01eDao;
	
	@Resource
	C101S01ADao c101s01aDao;
	
	Properties prop;
	/**
	 * 新增L820M01A 以房養老貸款撥款前查詢維護主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl820m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String l820m01aMainid = "";
		L820M01A l820m01a = new L820M01A();
		l820m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l820m01a.setOwnBrId(user.getUnitNo());

		l820m01aMainid = IDGenerator.getUUID();

		l820m01a.setMainId(l820m01aMainid);
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l820m01a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		l820m01a.setDocURL(params.getString("docUrl"));
		//l820m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		//l820m01a.setCntrNo(params.getString("cntrNo"));
		l820m01a.setCustId(params.getString("custId", null));
		l820m01a.setDupNo(params.getString("dupNo", null));
		l820m01a.setCustName(params.getString("custName", null));
		//l820m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		lms8200Service.save(l820m01a);
		
		//處理明細檔
		boolean l820m01c_flag = this.newl820m01c(params, l820m01aMainid);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(EloanConstants.OID, l820m01a.getOid());
		result.set(EloanConstants.MAIN_ID, l820m01a.getMainId());
		result.set("l820m01c_flag", l820m01c_flag);
		return result;
	}
	
	/**
	 * 查詢L820M01A 以房養老貸款撥款前查詢維護主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL820m01a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> dataMap = new HashMap<String, String>();

        if (!Util.isEmpty(oid)) {
        	L820M01A l820m01a = lms8200Service.findModelByOid(L820M01A.class, oid);
            if (l820m01a == null) {
                // 開啟新案帶入起案的分行和目前文件狀態
                result.set("docStatus", this.getMessage("docStatus." + CreditDocStatusEnum.海外_編製中.getCode()));
                result.set("ownBrId", user.getUnitNo());
                result.set("ownBrName", StrUtils.concat(" ", branchService.getBranchName(user.getUnitNo())));
                result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
            } else {
                // 有按過儲存會有 RandomCode
                if (Util.isEmpty(Util.nullToSpace(l820m01a.getRandomCode()))) {//先留著
                    dataMap = lms8200Service.getData(l820m01a);

                    result.putAll(dataMap);
                } else {
                    dataMap = lms8200Service.getData(l820m01a);
                    result.putAll(dataMap);
                }
                result = formatResultShow(result, l820m01a, page);
                
				result.set(EloanConstants.OID, CapString.trimNull(l820m01a.getOid()));
				result.set(EloanConstants.MAIN_OID, CapString.trimNull(l820m01a.getOid()));
				//formatResultShow(result, l820m01a, page);
            }
        }
		return result;
	}
	
	/**
	 * 格式化顯示訊息
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L820M01A l820m01a, Integer page) throws CapException {
		String mainId = l820m01a.getMainId();
		//我應該要多一個簽章表
		switch (page) {
		case 1:
			result = DataParse.toResult(l820m01a);
			List<L820M01B> l820m01blist = (List<L820M01B>) lms8200Service.findListByMainId(L820M01B.class, mainId);
			if (!Util.isEmpty(l820m01blist)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L820M01B ll820m01b : l820m01blist) {
					// 要加上人員代碼
					String type = Util.trim(ll820m01b.getStaffJob());
					String userId = Util.trim(ll820m01b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",	" " + branchService.getBranchName(l820m01a.getOwnBrId()));

			result.set("creator", lmsService.getUserName(l820m01a.getCreator()));
			result.set("updater", lmsService.getUserName(l820m01a.getUpdater()));
			result.set("createTime", Util.nullToSpace(TWNDate.valueOf(l820m01a.getCreateTime())));
			result.set("updateTime", Util.nullToSpace(TWNDate.valueOf(l820m01a.getUpdateTime())));
			result.set("docStatus",	getMessage("docStatus." + l820m01a.getDocStatus()));
			break;
		}// close switch case

		result.set("showCustId", StrUtils.concat(CapString.trimNull(l820m01a.getCustId()), " ",
				CapString.trimNull(l820m01a.getDupNo()), " ", CapString.trimNull(l820m01a.getCustName())));
		result.set("docStatusVal", l820m01a.getDocStatus());
		//result.set("cntrNo", l820m01a.getCntrNo());
		result.set("ideSearchResult", l820m01a.getIdeSearchResult());
		result.set("FaSearchResult", l820m01a.getFaSearchResult());
		result.set(EloanConstants.OID, CapString.trimNull(l820m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,	CapString.trimNull(l820m01a.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(l820m01a.getMainId()));
		
//		L140MM6C l140mm6cA = lms7800Service.findL140mm6c(mainId, "A");
//		result = this.formatL140MM6C(result, l140mm6cA, "A");
//		L140MM6C l140mm6cB = lms7800Service.findL140mm6c(mainId, "B");
//		result = this.formatL140MM6C(result, l140mm6cB, "B");
//		L140MM6C l140mm6cC = lms7800Service.findL140mm6c(mainId, "C");
//		result = this.formatL140MM6C(result, l140mm6cC, "C");
		return result;
	}
	
	/**
	 * 刪除L820M01A 以房養老貸款撥款前查詢維護主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL820m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms8200Service.deleteL820m01as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}
	
	/**
	 * 儲存L820M01A 已房養老撥款前查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL820m01a(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.OID));

		String form = Util.trim(params.getString("mainPanel"));
		JSONObject jsonData = null;

		L820M01A l820m01a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		String showMsg1 = "";
		if (Util.isNotEmpty(oid)) {
			l820m01a = lms8200Service.findModelByOid(L820M01A.class, oid);
			l820m01a.setRandomCode(IDGenerator.getRandomCode());
		}
		Properties pop = MessageBundleScriptCreator.getComponentResource(LMS8200M01Page.class);

		l820m01a.setDeletedTime(null);

		String validate = null;
		switch (page) {
		case 1:
			jsonData = JSONObject.fromObject(form);
			DataParse.toBean(jsonData, l820m01a);
			validate = Util.validateColumnSize(l820m01a, pop, "L820M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}

			lms8200Service.save(l820m01a);
			result.set("randomCode", l820m01a.getRandomCode());
			break;
		}

		if (Util.isEmpty(showMsg1)) {
			if (showMsg) {
				showMsg1 = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
			}
		} else {
			if (showMsg) {

			} else {
				throw new CapMessageException(showMsg1, getClass());
			}
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg1);

		result.set(EloanConstants.OID, CapString.trimNull(l820m01a.getOid()));
		result.set(EloanConstants.MAIN_OID, CapString.trimNull(l820m01a.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(l820m01a.getMainId()));
		result.set(
				"showCustId",
				CapString.trimNull(l820m01a.getCustId()) + " "
						+ CapString.trimNull(l820m01a.getDupNo()) + " "
						+ CapString.trimNull(l820m01a.getCustName()));
		result.set("custId", CapString.trimNull(l820m01a.getCustId()));
		result.set("dupNo", CapString.trimNull(l820m01a.getDupNo()));
		return result;
	}
	
	public IResult echo_custId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		result.set("cntrNo", Util.trim(params.getString("cntrNo")));
		return result;
	}

	/**
	 * RPA相關查詢
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapMessageException
	 * @throws ClientProtocolException
	 * @throws IOException
	 * @throws Exception
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult keep_EJ_ST_queryOutput(PageParameters params) throws CapMessageException, ClientProtocolException, IOException, Exception  {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String txId = Util.trim(params.getString("txId"));
		String param_txId = txId;
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		
		//String empname = lmsService.getUserName(userId);
		String deptid = user.getUnitNo();
		String cbdeptid = "";
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		IBranch iBranch_deptid = branchService.getBranch(deptid);
		CapAjaxFormResult result = new CapAjaxFormResult();

		cbdeptid = get_cbdeptid_totLen4_BrNo_ChkNo(user.getUnitNo());		
		if (Util.isEmpty(cbdeptid)) {
			throw new CapMessageException("cbdeptid=", getClass());
		}
		
		//查詢===========================================================
		if (Util.equals(param_txId, CrsUtil.API_TXID_ID_CARD_CHECK)){
			//內政部國民身分證領換補資料
			//check 身分證領補換資料「發證日期 發證地點 領補換類別」是否有輸入
			//這邊根據取得的 custid , 額度序號, 產品種類 至 L140M01A額度明細(新做) => L120M01C關聯 => L120M01A簽報書=> C120M01A徵信資料 取得相關資訊		
			//取得額度明細表
			//C120S01A c120s01a = lms8200Service.findC120m01aByConst(cntrNo, custId, dupNo);
			//取得該客戶最新的簽報書中引入的徵信資料
			//C120S01A c120s01a = lms8200Service.findC120m01aByConst(custId, dupNo);
			//2023.06.07 消金郭守明科長確認改抓"個金徵信"中最新的資料
			//check 身分證領補換資料「發證日期 發證地點 領補換類別」是否有輸入
			// 撈取l820m01c 向下的額度序號, 額度序號前三碼為分行
			List<L820M01C> l820m01cs = l820m01cDao.findByMainId(mainId);
			String BRNO = null;
			for (L820M01C l820m01c : l820m01cs) {
				//取得分行資料
				BRNO = (Util.trim(l820m01c.getCntrNo()).length() > 0 ? l820m01c.getCntrNo().substring(0, 3) : "");
			}
			C101S01A c101s01a = lms8200Service.findC101s01aByConst(BRNO, custId, dupNo);
			if (c101s01a == null) {
				throw new CapMessageException("無法取得身分證領補相關資料", getClass());
			}
			
			
			
			String[] _Z21_qkey1_dataArr = get_Z21_qkey1_dataArr(c101s01a);
			validate_Z21_qkey1_dataArr(_Z21_qkey1_dataArr);
			//------------------------------------------------------------
			String applyCode = convert_c120s01a_IdCardChgFlag_to_z21_applyReasonCode(c101s01a.getIdCardChgFlag());
			String applyYYYMMDD = CapDate.formatDate(c101s01a.getIdCardIssueDate(), "YYYMMDD");
			IdentificationCheckGwReqMessage req = new IdentificationCheckGwReqMessage(c101s01a.getCustId(), applyCode, applyYYYMMDD, 
															this.clsService.getSiteIdByApiInquiry(c101s01a.getIdCard_siteId()), user.getUserId());
			identificationCheckGwClient.send(req);
			
			Calendar currentDateTime = Calendar.getInstance();
			Timestamp nowTS = new Timestamp(currentDateTime.getTimeInMillis());
			byte[] reportData = this.lms8200Service.generateJSONObjectForIdCardCheck(req, currentDateTime);
			/*
			 	1=國民身分證資料與檔存資料相符。 
			 	2=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 1 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
			 	3=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 2 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
			 	4=身分證字號 XXXXXXXXXX 目前驗證資料錯誤次數已達 3 次，今日錯誤累積達 3 次後，此身分證字號將無法查詢。 
			 	5=身分證字號 XXXXXXXXXX 驗證資料錯誤次數已達 3 次。今 日無法查詢，請明日再查！！ 
			 	6=您所查詢的國民身分證字號 XXXXXXXXXX 已停止使用。 
			 	7=您所查詢的國民身分證 XXXXXXXXXX，業依當事人申請登 錄掛失。 
			 	8=單一使用者出現異常使用情形，暫停使用者權限。
			*/
			String dataStatus = "1".equals(req.getCheckIdCardApply()) ? "0" : "1";
			L820M01S l820m1s = lms8200Service.modifyL820M01SForMixRecordData(mainId, c101s01a.getCustId(), c101s01a.getDupNo(), "5", dataStatus, reportData);
			if (l820m1s != null) {
				l820m1s.setCreateTime(nowTS);
				l820m1s.setUpdateTime(nowTS);
				l820m1s.setDataCreateTime(nowTS);
				lms8200Service.save(l820m1s);
			}

			L820M01A l820m01a = lms8200Service.findModelByOid(L820M01A.class, oid);
			if (l820m01a != null) {
				if("0".equals(dataStatus)){//發查狀態  1=國民身分證資料與檔存資料相符，更新 L820M01A.IDESEARCHRESULT身分證發查成功寫Y否則寫N
					l820m01a.setIdeSearchResult(UtilConstants.DEFAULT.是);
				}else{
					l820m01a.setIdeSearchResult(UtilConstants.DEFAULT.否);
				}
				lms8200Service.save(l820m01a);
				
				//撈取l820m01c 向下的額度序號
				//List<L820M01C> l820m01cs = l820m01cDao.findByMainId(l820m01a.getMainId());
				StringBuffer lnf242_contracts = new StringBuffer();
				//同步更新  LN.LNF242，一個額度序號只會有一筆 所以不看放款帳號
				for (L820M01C l820m01c : l820m01cs) {
					//放行後將L242  LNF242_IDCHG_FG, LNF242_WARD_FG 註記為N (N為可撥款，這邊跟L820相反)
					misdbBASEService.updateLNF242("Y".equals(l820m01a.getIdeSearchResult()) ? "N" : "Y", 
							"Y".equals(l820m01a.getFaSearchResult()) ? "N" : "Y", 
							"Y".equals(l820m01a.getWealthResult()) ? "N" : "Y", 
							l820m01a.getCustId(), l820m01a.getDupNo(), l820m01c.getCntrNo());
//					if (lnf242_contracts.toString().indexOf(Util.trim(l820m01c.getCntrNo())) == -1) {
//						if (lnf242_contracts.length() > 0) {
//							lnf242_contracts.append(",");
//						}
//						lnf242_contracts.append("'");
//						lnf242_contracts.append(Util.trim(l820m01c.getCntrNo()));
//						lnf242_contracts.append("'");
//					}
				}
				//if(lnf242_contracts.length() > 0){
				//	misdbBASEService.updateLNF242(l820m01a.getIdeSearchResult(), "Y".equals(l820m01a.getFaSearchResult()) ? "Y" : "N", 
				//			l820m01a.getCustId(), l820m01a.getDupNo(), lnf242_contracts.toString());
				//}
			}
		}else if(Util.equals(param_txId, CrsUtil.RPA_TXID_FA)){
			//司法院受監護/輔助宣告資料
			this.rpaProcessService.deleteBeforeQueryL820(mainId, custId);
			this.rpaProcessService.gotoRPAJobsForL820(new L820M01W(mainId, custId));
			L820M01A l820m01a = lms8200Service.findModelByOid(L820M01A.class, oid);
			if (l820m01a != null) {
				// * 家事查詢結果<p/> * 通過:Y * 未通過:N* 查詢中:I
				l820m01a.setFaSearchResult("I");
				lms8200Service.save(l820m01a);
			}
		}else if(Util.equals(param_txId, CrsUtil.Wealth)){
			//財富管理查詢
			L820M01A l820m01a = lms8200Service.findModelByOid(L820M01A.class, oid);
			//取得上個月資料
			// 上個月
			String toDayStr = CapDate.formatDate(new Date(),
					UtilConstants.DateFormat.YYYY_MM_DD);
			String toDayStr1 = toDayStr.replace("-", "");
			String newDateStr = CapDate.formatyyyyMMddToDateFormat(
					CapDate.addMonth(toDayStr1, -1),
					UtilConstants.DateFormat.YYYY_MM);
			// 上個月底最後一天
			String preMonLastDate = Util
					.toAD(CapDate.shiftDays(CapDate.addMonth(
							CapDate.parseDate(newDateStr + "-01"), 1), -1));
			// 上個月第一天
			String preMonFirstDate = Util.getLeftStr(preMonLastDate, 7) + "-01";
			// DW 資料日期
			Date dwQueryDate = null;//new Date();
			
			List<?> cupfmOtsRows = this.dwdbBASEService.findDW_MD_CUPFM_OTS_selCYC_MN(
					l820m01a.getCustId(), l820m01a.getDupNo(), preMonFirstDate, preMonLastDate);
			// 因倉儲會有資料時間差, 如月初尚未有新的資料進來, 則在往前抓衣個月的
			if(cupfmOtsRows.isEmpty() || cupfmOtsRows.size() == 0){
				newDateStr = CapDate.formatyyyyMMddToDateFormat(
						CapDate.addMonth(toDayStr1, -2),
						UtilConstants.DateFormat.YYYY_MM);
				preMonLastDate = Util
				.toAD(CapDate.shiftDays(CapDate.addMonth(
						CapDate.parseDate(newDateStr + "-01"), 1), -1));
				preMonFirstDate = Util.getLeftStr(preMonLastDate, 7) + "-01";
				//往前在推一個月
				cupfmOtsRows = this.dwdbBASEService.findDW_MD_CUPFM_OTS_selCYC_MN(
						l820m01a.getCustId(), l820m01a.getDupNo(), preMonFirstDate, preMonLastDate);
			}
			
			
			if (l820m01a != null) {
				L820M01E l820m01e = lms8200Service.genL820M01ERecordData(mainId, custId, dupNo);
				
				// * 財富管理查詢<p/> * 通過:Y * 未通過:N* 查詢中:I
				if(cupfmOtsRows.isEmpty()){
					//沒有資料 =>通過
					l820m01e.setDataSearchResult(UtilConstants.DEFAULT.是);//通過
					l820m01e.setQueryTime(CapDate.getCurrentTimestamp());
					//l820m01e.setQueryTime(CapDate.convertStringToTimestamp(preMonLastDate+" 00:00:00"));//改放DW資料日//郭襄臨時要求補上資料日
					l820m01e.setQueryTime(CapDate.getCurrentTimestamp());
					l820m01a.setWealthResult(UtilConstants.DEFAULT.是);//通過
				}else{
					//確認相關項目
					BigDecimal in_tr_sc_e = new BigDecimal(0);//ETF、外國債劵、連動債	
					BigDecimal in_tr_fu = new BigDecimal(0);//基金
					BigDecimal in_wm_aum = new BigDecimal(0);//理財AUM(A)+(B)+(C)
					BigDecimal in_wm_fd = new BigDecimal(0);//(A)信託商品月底餘額
					BigDecimal in_wm_ia = new BigDecimal(0);//(B)累積已繳保費/保單價值
					BigDecimal in_wm_std = new BigDecimal(0);//(C)優利投資月平均餘額
					BigDecimal in_wm_bal = new BigDecimal(0);//累積下單金額
					BigDecimal in_wm_fee = new BigDecimal(0);//累計手收金額
					BigDecimal total_amt = new BigDecimal(0);//數字總額
					//1. 如果該戶無信託業務(ETF、國外債券、連動債、基金)及財富管理業務往來(不含資產AUM)，顯示通過，即照預定撥款日撥款。
					//2. 如果該戶有信託業務(ETF、國外債券、連動債、基金)及財富管理業務往來，顯示未通過，需請經辦人工判斷，填寫檢視結果後送呈主管覆核，主管覆核後顯示通過，即照預定撥款日撥款。未於撥款前1個營業日未處理完成，則停止撥款。
					Iterator<?> itcupfmOts = cupfmOtsRows.iterator();
					while (itcupfmOts.hasNext()) {
						Map<?, ?> dataMapCupfmOts = (Map<?, ?>) itcupfmOts.next();
						//ETF、外國債劵、連動債	
						in_tr_sc_e = in_tr_sc_e.add(new BigDecimal(Util.parseLong(Util
								.nullToSpace(dataMapCupfmOts.get("IN_TR_SC_E")))));
						//基金
						in_tr_fu = in_tr_fu.add(new BigDecimal(Util.parseLong(Util
								.nullToSpace(dataMapCupfmOts.get("IN_TR_FU")))));
						//(A)信託商品月底餘額
						in_wm_fd = in_wm_fd.add(new BigDecimal(Util.parseLong(Util
								.nullToSpace(dataMapCupfmOts.get("IN_WM_FD")))));
						//(B)累積已繳保費/保單價值
						in_wm_ia = in_wm_ia.add(new BigDecimal(Util.parseLong(Util
								.nullToSpace(dataMapCupfmOts.get("IN_WM_IA")))));
						//(C)優利投資月平均餘額
						in_wm_std = in_wm_std.add(new BigDecimal(Util.parseLong(Util
								.nullToSpace(dataMapCupfmOts.get("IN_WM_STD")))));
						//理財AUM(A)+(B)+(C)
						in_wm_aum = in_wm_aum
								.add(new BigDecimal(Util.parseLong(Util.nullToSpace(dataMapCupfmOts.get("IN_WM_FD")))))
								.add(new BigDecimal(Util.parseLong(Util.nullToSpace(dataMapCupfmOts.get("IN_WM_IA")))))
								.add(new BigDecimal(Util.parseLong(Util.nullToSpace(dataMapCupfmOts.get("IN_WM_STD")))));
						//累積下單金額
						in_wm_bal = in_wm_bal.add(new BigDecimal(Util.parseLong(Util
								.nullToSpace(dataMapCupfmOts.get("IN_WM_BAL")))));
						//累計手收金額
						in_wm_fee = in_wm_fee.add(new BigDecimal(Util.parseLong(Util
								.nullToSpace(dataMapCupfmOts.get("IN_WM_FEE")))));
						//DW資料日期
						dwQueryDate = Util.parseDate((Util.trim(dataMapCupfmOts.get("CYC_MN"))));
					}
					//數字總和
					total_amt = total_amt.add(in_tr_fu).add(in_tr_sc_e).add(in_wm_aum).add(in_wm_bal)
						.add(in_wm_fd).add(in_wm_fee).add(in_wm_ia).add(in_wm_std);
					
					l820m01e.setIn_tr_fu(in_tr_fu);
					l820m01e.setIn_tr_sc_e(in_tr_sc_e);
					l820m01e.setIn_wm_aum(in_wm_aum);
					l820m01e.setIn_wm_bal(in_wm_bal);
					l820m01e.setIn_wm_fd(in_wm_fd);
					l820m01e.setIn_wm_fee(in_wm_fee);
					l820m01e.setIn_wm_ia(in_wm_ia);
					l820m01e.setIn_wm_std(in_wm_std);
					//l820m01e.setQueryTime(CapDate.convertStringToTimestamp(preMonLastDate+" 00:00:00"));//改放DW資料日//郭襄臨時要求補上資料日
					l820m01e.setQueryTime(CapDate.getCurrentTimestamp());
					if(total_amt.compareTo(new BigDecimal(0)) > 0){//數字總和>0 表示有財富管理
						l820m01a.setWealthResult(UtilConstants.DEFAULT.否);//不通過
						l820m01e.setDataSearchResult(UtilConstants.DEFAULT.否);
					}else{
						l820m01a.setWealthResult(UtilConstants.DEFAULT.是);//通過
						l820m01e.setDataSearchResult(UtilConstants.DEFAULT.是);
					}
					l820m01e.setDwQueryDate(dwQueryDate);
					//l820m01a.setWealthResult(UtilConstants.DEFAULT.否);
				}
				lms8200Service.save(l820m01a, l820m01e);
			}
		}else {
			throw new CapMessageException("unknown txId[" + txId + "]", getClass());
		}

		result.set("txId", txId);
		result.set("isDone", "Y");
		return result;
	}
	
	private String get_cbdeptid_totLen4_BrNo_ChkNo(String given_cbdeptid){
		IBranch iBranch_cbdeptid = branchService.getBranch(given_cbdeptid);
		if(iBranch_cbdeptid!=null){
			return iBranch_cbdeptid.getBrNo() + iBranch_cbdeptid.getChkNo();	
		}
		return "";
	}
	
	/**
	 * X101010107▲1000210▲2▲1000110▲0▲68000000
	 * X123400025▲0950301▲1▲0300201▲1▲66000000 <br/>
	 * [0] 身分證號 char(10) <br/>
	 * [1] 領補換日期 char(7) yyy+mm+dd <br/>
	 * [2] 領補換代號 char(1) 1:初領,2:補領,3:換領 <br/>
	 * [3] 出生日期 char(7) yyy+mm+dd <br/>
	 * [4] 有無相片 char(1) 0:有,1:無 <br/>
	 * [5] 發證地點 char(8) 66000000:中市
	 */
	private String[] get_Z21_qkey1_dataArr(C101S01A c101s01a) {
		String[] arr = new String[6];
		for (int i = 0; i < arr.length; i++) {
			arr[i] = "";
		}
		if (c101s01a != null) {
			String char10 = "          ";
			// ~~~~~~~~~~
			arr[0] = Util.trim(c101s01a.getCustId());
			arr[1] = c101s01a.getIdCardIssueDate() == null ? Util.getLeftStr(char10, 7) : TWNDate.valueOf(c101s01a.getIdCardIssueDate()).toTW();
			arr[2] = convert_c120s01a_IdCardChgFlag_to_z21_applyReasonCode(c101s01a.getIdCardChgFlag());
			arr[3] = c101s01a.getBirthday() == null ? Util.getLeftStr(char10, 7) : TWNDate.valueOf(c101s01a.getBirthday()).toTW();
			arr[4] = convert_c120s01a_IdCardPhoto_to_z21_picCd(c101s01a.getIdCardPhoto());
			arr[5] = Util.trim(c101s01a.getIdCard_siteId());
		}
		return arr;
	}
	
	private String convert_c120s01a_IdCardChgFlag_to_z21_applyReasonCode(
			String idCardChgFlag) {
		return ClsUtility.convert_c101s01a_IdCardChgFlag_to_z21_applyReasonCode(idCardChgFlag);
	}
	
	private String convert_c120s01a_IdCardPhoto_to_z21_picCd(String idCardPhoto) {
		if (Util.equals("Y", idCardPhoto)) {
			return "0";
		} else if (Util.equals("N", idCardPhoto)) {
			return "1";
		} else if (Util.equals("", idCardPhoto)) {
			return " ";
		}
		return idCardPhoto;
	}
	
	private void validate_Z21_qkey1_dataArr(String[] dataArr)
	throws CapMessageException {
		if (dataArr != null && dataArr.length == 6) {
			List<String> msg_list = new ArrayList<String>();
			if (Util.isEmpty(Util.trim(dataArr[3]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + "需輸入「" + getI18nMsg("C101S01A.birthday") + "」");
			} else if (dataArr[3].length() != 7) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + getI18nMsg("C101S01A.birthday") + "(" + dataArr[3] + ")資料長度錯誤");
			}
		
			if (Util.isEmpty(Util.trim(dataArr[1]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + "需輸入「" + getI18nMsg("label.idCardIssueDate") + "」");
			} else if (dataArr[1].length() != 7) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + getI18nMsg("label.idCardIssueDate") + "(" + dataArr[1] + ")資料長度錯誤");
			}
		
			if (Util.isEmpty(Util.trim(dataArr[5]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + "需輸入「" + getI18nMsg("label.idCard_siteId") + "」");
			} else if (dataArr[5].length() != 8) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + getI18nMsg("label.idCard_siteId") + "(" + dataArr[5] + ")資料長度應為8");
			}
		
			if (Util.isEmpty(Util.trim(dataArr[2]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + "需輸入「" + getI18nMsg("label.idCardChgFlag") + "」");
			} else if (dataArr[2].length() != 1) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + getI18nMsg("label.idCardChgFlag") + "(" + dataArr[2] + ")資料長度應為1");
			}
		
			if (Util.isEmpty(Util.trim(dataArr[4]))) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + "需輸入「" + getI18nMsg("label.idCardPhoto") + "」");
			} else if (dataArr[4].length() != 1) {
				msg_list.add((CrsUtil.EJ_TXID_Z21 + CrsUtil.EJ_TXID_Z21_DESC) + getI18nMsg("label.idCardPhoto") + "，資料長度應為1");
			}
		
			if (msg_list.size() > 0) {
//				if (l820m01a != null) {
//					//發查狀態  1=國民身分證資料與檔存資料相符，更新 L820M01A.IDESEARCHRESULT身分證發查成功寫Y否則寫N
//					l820m01a.setIdeSearchResult("N");
//					lms8200Service.save(l820m01a);
//				}
				throw new CapMessageException(StringUtils.join(msg_list, "<br/>"), getClass());
			}
		}
	}
	
	/**
	 * @param key
	 * @return
	 */
	private String getI18nMsg(String key) {
		String result = null;
		if (prop == null) {
			prop = MessageBundleScriptCreator.getComponentResource(LMS8200M01Page.class);
		}
		if (prop != null) {
			result = prop.getProperty(Util.trim(key));
		}
		return Util.trim(result);
	}
	
	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管, SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}
	
	/*** 呈主管覆核(呈主管 主管覆核 拆2個method) */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L820M01A l820m01a = lms8200Service.findModelByOid(L820M01A.class, oid);
		
		String[] formSelectBoss = params.getStringArray("selectBoss");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		//if (!Util.isEmpty(formSelectBoss)) {
		if (params.containsKey("sendBoss") && params.getBoolean("sendBoss")) {
			String manager = Util.trim(params.getString("manager"));
			List<L820M01B> models = (List<L820M01B>) lms8200Service.findListByMainId(L820M01B.class, l820m01a.getMainId());
			if (!models.isEmpty()) {
				lms8200Service.deleteL820m01bs(models, false);
			}
			
			List<L820M01B> l820m01bs = new ArrayList<L820M01B>();
			
//			for (String people : formSelectBoss) {
//				L820M01B l820m01b = new L820M01B();
//				l820m01b.setCreator(user.getUserId());
//				l820m01b.setCreateTime(CapDate.getCurrentTimestamp());
//				l820m01b.setMainId(l820m01a.getMainId());
//				l820m01b.setBranchType(user.getUnitType());
//				l820m01b.setBranchId(user.getUnitNo());
//				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
//				l820m01b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
//				l820m01b.setStaffNo(people);
//				l820m01bs.add(l820m01b);
//			}
			
//			L820M01B managerL820m01b = new L820M01B();
//			managerL820m01b.setCreator(user.getUserId());
//			managerL820m01b.setCreateTime(CapDate.getCurrentTimestamp());
//			managerL820m01b.setMainId(l820m01a.getMainId());
//			managerL820m01b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
//			managerL820m01b.setStaffNo(manager);
//			managerL820m01b.setBranchType(user.getUnitType());
//			managerL820m01b.setBranchId(user.getUnitNo());
//			l820m01bs.add(managerL820m01b);
			
			L820M01B apprL820m01b = new L820M01B();
			apprL820m01b.setCreator(user.getUserId());
			apprL820m01b.setCreateTime(CapDate.getCurrentTimestamp());
			apprL820m01b.setMainId(l820m01a.getMainId());
			apprL820m01b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL820m01b.setStaffNo(user.getUserId());
			apprL820m01b.setBranchType(user.getUnitType());
			apprL820m01b.setBranchId(user.getUnitNo());
			l820m01bs.add(apprL820m01b);
			
			lms8200Service.saveL820m01bList(l820m01bs);
		}
		
		//Boolean upMis = false;
		L820M01B L820m01bL4 = new L820M01B();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			l820m01a.setApprover(user.getUserId());
			l820m01a.setApproveTime(CapDate.getCurrentTimestamp());
			l820m01a.setSign(params.getString("sign"));
			//身分證換補&家事發查直 => 成功
			l820m01a.setIdeSearchResult(UtilConstants.DEFAULT.是);
			l820m01a.setFaSearchResult(UtilConstants.DEFAULT.是);
			//財富管理查詢結果 => 成功
			l820m01a.setWealthResult(UtilConstants.DEFAULT.是);
			//upMis = true;
			L820M01B l820m01b = lms8200Service.findL820m01b(l820m01a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l820m01b == null) {
				l820m01b = new L820M01B();
				l820m01b.setCreator(user.getUserId());
				l820m01b.setCreateTime(CapDate.getCurrentTimestamp());
				l820m01b.setMainId(l820m01a.getMainId());
				l820m01b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l820m01b.setStaffNo(user.getUserId());
				l820m01b.setBranchType(user.getUnitType());
				l820m01b.setBranchId(user.getUnitNo());
			}
			L820m01bL4 = l820m01b;	
		}
		
		if (!Util.isEmpty(l820m01a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L820M01B l820m01b = lms8200Service.findL820m01b(l820m01a.getMainId(), user.getUnitType(), user.getUnitNo(), user.getUserId(), UtilConstants.STAFFJOB.經辦L1);
						if (l820m01b != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							lms8200Service.save(L820m01bL4);
							//upMis = true;
						}
					}
				}
				lms8200Service.flowAction(l820m01a.getOid(), l820m01a, params.containsKey("flowAction"), params.getAsBoolean("flowAction", false), false);//upMis 先填false
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms8200Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  lms8200Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
			if (params.containsKey("checkDate")) {
				// 放行後 同步更新LN242對應的已房養老案件
				// 撈取l820m01c 向下的額度序號
				List<L820M01C> l820m01cs = l820m01cDao.findByMainId(l820m01a.getMainId());
				// 同步更新 LN.LNF242，一個額度序號只會有一筆 所以不看放款帳號
				for (L820M01C l820m01c : l820m01cs) {
					//放行後將L242  LNF242_IDCHG_FG, LNF242_WARD_FG, LNF242_WEALTH_FG 註記為N (N為可撥款，這邊跟L820相反)
					misdbBASEService.updateLNF242("N", "N", "N", l820m01a.getCustId(), l820m01a.getDupNo(), l820m01c.getCntrNo());
				}
			}
		}
		
		return new CapAjaxFormResult();
	}
	
	/**
	 * 新增L820M01C 以房養老貸款撥款前查詢維護明細檔
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @param l820m01aMainid
	 * @return successflag
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public boolean newl820m01c(PageParameters params, String l820m01aMainid)
			throws CapException {
		boolean successflag = false;		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String custId = params.getString("custId", null);
		String dupNo = params.getString("dupNo", null);
		
		String nowdate = CapDate.getCurrentDate("yyyy-MM-dd");
		//取得營業日資訊
		Map<String, Object> lnf320 = misdbBASEService.get_LNF320(nowdate);
		Timestamp actualtimeTime;
		String actualtime;
		if (MapUtils.isEmpty(lnf320)) {
			//非營業日，則抓下個營業日來當實際付款日	
			Map<String, Object> lnf320_nextbussdate = misdbBASEService.get_LNF320_nextBussdate(nowdate);
			if(!MapUtils.isEmpty(lnf320_nextbussdate)){
				actualtime = String.valueOf(lnf320_nextbussdate.get("LNF320_QUERY_DATE"));
				actualtimeTime =  new Timestamp(CapDate.parseDate(actualtime).getTime());
			}else{
				actualtime = nowdate;
				actualtimeTime = new Timestamp(CapDate.parseDate(actualtime).getTime());
			}

		}else{
			actualtime = String.valueOf(lnf320.get("LNF320_QUERY_DATE"));
			actualtimeTime = new Timestamp(CapDate.parseDate(actualtime).getTime());
		}
		//取得同客戶 且在下個營業日前的撥款資訊
		List<Map<String, Object>> l820m01_contractas = misdbBASEService.get_LNF242_Contractno(custId, dupNo, actualtime);


		for (Map<String, Object> l820m0a : l820m01_contractas) {
			L820M01C l820m01c = new L820M01C();
			l820m01c.setUid(null);
			l820m01c.setMainId(l820m01aMainid);
			l820m01c.setCustId(custId);
			l820m01c.setDupNo(dupNo);
			l820m01c.setCntrNo(MapUtils.getString(l820m0a, "LNF242_CONTRACT", ""));
			l820m01c.setLnf242_loan_no(MapUtils.getString(l820m0a, "LNF242_LOAN_NO", ""));
			l820m01c.setEstimateTime(new Timestamp(CapDate.parseDate(MapUtils.getString(l820m0a, "LNF242_NXTDP_DATE", "")).getTime()));
			if(l820m01c.getEstimateTime().after(actualtimeTime)){
				l820m01c.setActualTime(l820m01c.getEstimateTime());
			}else{
				l820m01c.setActualTime(actualtimeTime);
			}
			
			lms8200Service.save(l820m01c);
			
		}
		successflag = true;
		return successflag;
	}
	
	/**
	 * 取得L820M01E 財富管理發查明細
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL820m01e(PageParameters params)
			throws CapException {
		//MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		
		L820M01E l820m01e = lms8200Service.genL820M01ERecordData(mainId, custId, dupNo);
		
		CapAjaxFormResult result = DataParse.toResult(l820m01e,
				DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
						EloanConstants.OID });
		
		return result;
	}
}
