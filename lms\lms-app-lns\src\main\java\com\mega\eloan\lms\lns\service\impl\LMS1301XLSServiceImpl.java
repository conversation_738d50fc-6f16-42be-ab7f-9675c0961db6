package com.mega.eloan.lms.lns.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.lns.pages.LMS1301M01Page;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.service.BranchService;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.PageOrientation;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 異常通報產Excel
 * </pre>
 * 
 * @since 2012/12/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/3,Miller
 *          </ul>
 */
@Service("lms1301xlsservice")
public class LMS1301XLSServiceImpl implements FileDownloadService {

	@Resource
	LMS1201Service service1201;

	@Resource
	MisdbBASEService misDBService;

	@Resource
	BranchService branch;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1301XLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1301M01Page.class);
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> listMap = null;
		WritableFont font12 = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12CenterNO = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12LeftNO = null;
		WritableCellFormat format12RightNO = null;
		L120M01A meta = service1201.findL120m01aByMainId(mainId);
		String brno = null;
		String custId = null;
		String dupNo = null;
		String custName = null;
		String caseNo = null;
		try {
			baos = new ByteArrayOutputStream();
			WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableSheet sheet = book.createSheet("1", 0);
			SheetSettings settings = sheet.getSettings();
			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			settings.setOrientation(PageOrientation.LANDSCAPE);
			// 縮放比例頁寬
			settings.setFitWidth(1);
			// 縮放比例頁高
			settings.setFitHeight(5000);
			// 設定字型與格式
			// other.msg60=新細明體
			font12 = new WritableFont(WritableFont.createFont(pop2
					.getProperty("other.msg60")), 12, WritableFont.NO_BOLD);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT);
			format12CenterNO = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE, false, false);
			format12LeftNO = LMSUtil.setCellFormat(format12LeftNO, font12,
					Alignment.LEFT, false, false);
			format12RightNO = LMSUtil.setCellFormat(format12RightNO, font12,
					Alignment.RIGHT, false, false);
			sheet.mergeCells(0, 0, 7, 0);
			if (meta != null) {
				custId = Util.trim(meta.getCustId());
				dupNo = Util.trim(meta.getDupNo());
				brno = Util.trim(meta.getCaseBrId());
				custName = Util.trim(meta.getCustName());
				listMap = misDBService.selExcel(custId, dupNo, brno);
				// 測試用資料
				// listMap = misDBService.selExcel
				// ("A220821651", "0", "900");
			}
			Label labelT1 = new Label(0, 0, pop.getProperty("l130m01a.excel8"),
					format12CenterNO);
			sheet.addCell(labelT1);
			Label labelT2 = new Label(0, 1, pop.getProperty("l130m01a.excel9")
					+ Util.trim(brno) + Util.trim(branch.getBranchName(brno)),
					format12LeftNO);
			sheet.addCell(labelT2);
			Label labelT3 = new Label(0, 2,
					pop.getProperty("l130m01a.excel10") + Util.trim(custId)
							+ " " + Util.toFullCharString(custName),
					format12LeftNO);
			sheet.addCell(labelT3);
			Label labelT4 = new Label(6, 2, pop.getProperty("l130m01a.excel11")
					+ CapDate.getCurrentDate("YYY/MM/DD"), format12RightNO);
			sheet.addCell(labelT4);

			// 設定行寬
			sheet.setColumnView(0, 10);
			sheet.setColumnView(1, 20);
			sheet.setColumnView(2, 30);
			sheet.setColumnView(3, 40);
			sheet.setColumnView(4, 10);
			sheet.setColumnView(5, 5);
			sheet.setColumnView(6, 10);
			sheet.setColumnView(7, 40);

			String[] title = { pop.getProperty("l120m01a.approvetime"),
					pop.getProperty("l130m01a.excel1"),
					pop.getProperty("l130m01a.excel2"),
					pop.getProperty("l130m01a.excel3"),
					pop.getProperty("l130m01a.excel4"),
					pop.getProperty("l130m01a.excel5"),
					pop.getProperty("l130m01a.excel6"),
					pop.getProperty("l130m01a.excel7") };
			for (int j = 0; j < title.length; j++) {
				Label labelT5 = new Label(j, 3, title[j], format12Center);
				sheet.addCell(labelT5);
			}

			if (!listMap.isEmpty()) {
				for (int i = 0, k = 4; i < listMap.size(); i++, k++) {
					if (Util.isEmpty(caseNo)) {
						if (listMap.get(i).get("LNFE0854_DOC_NO") != null) {
							caseNo = Util.trim(listMap.get(i).get(
									"LNFE0854_DOC_NO"));
							// 設定核准日期
							if (listMap.get(i).get("LNFE0854_SDATE") != null) {
								Label labelDate = new Label(
										0,
										k,
										TWNDate.toTW(Util.parseDate(listMap
												.get(i).get("LNFE0854_SDATE"))),
										format12Left);
								sheet.addCell(labelDate);
							}
							// 設定案號
							Label labelCaseNo = new Label(1, k,
									getShowCaseNo(caseNo), format12Left);
							sheet.addCell(labelCaseNo);
							// 設定異常情形
							if (listMap.get(i).get("PROCESS") != null) {
								Label labelProcess = new Label(2, k, listMap
										.get(i).get("PROCESS").toString(),
										format12Left);
								sheet.addCell(labelProcess);
							}
						}
					} else {
						if (listMap.get(i).get("LNFE0854_DOC_NO") != null) {
							if (caseNo.equals(Util.trim(listMap.get(i).get(
									"LNFE0854_DOC_NO")))) {
								// 案號相同則合併儲存格
								sheet.mergeCells(0, k - 1, 0, k);
								sheet.mergeCells(1, k - 1, 1, k);
								sheet.mergeCells(2, k - 1, 2, k);
							} else {
								caseNo = null;
								k--;
								i--;
								continue;
							}
						}
					}
					// 設定項目名稱
					if (listMap.get(i).get("LNFE0856_ITEMNAME") != null) {
						Label labelContent = new Label(3, k, StrUtils.concat(
								"◎", listMap.get(i).get("LNFE0856_ITEMNAME")),
								format12Left);
						sheet.addCell(labelContent);
					}
					// 設定辦理日期
					if (listMap.get(i).get("LNFE0856_DODATE") != null) {
						Label labelDodate = new Label(
								4,
								k,
								"001/01/01".equals(TWNDate.toTW(Util
										.parseDate(listMap.get(i).get(
												"LNFE0856_DODATE")))) ? ""
										: TWNDate.toTW(Util.parseDate(listMap
												.get(i).get("LNFE0856_DODATE"))),
								format12Left);
						sheet.addCell(labelDodate);
					}
					// 設定幣別
					if (listMap.get(i).get("LNFE0856_DOCURR") != null) {
						Label labelCurr = new Label(5, k, listMap.get(i)
								.get("LNFE0856_DOCURR").toString(),
								format12Left);
						sheet.addCell(labelCurr);
					}
					// 設定辦理金額
					if (listMap.get(i).get("LNFE0856_DOAMT") != null) {
						Label labelAmt = new Label(6, k,
								NumConverter.addComma(listMap.get(i).get(
										"LNFE0856_DOAMT")), format12Right);
						sheet.addCell(labelAmt);
					}
					// 設定辦理說明
					if (listMap.get(i).get("LNFE0856_DOMEMO") != null) {
						Label labelItemName = new Label(7, k, listMap.get(i)
								.get("LNFE0856_DOMEMO").toString(),
								format12Left);
						sheet.addCell(labelItemName);
					}
				}
			}
			book.write();
			book.close();
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}

	/**
	 * 依照上傳案號格式化成異常通報顯示案號-> 民國年 + 分行名稱+(兆)授字第+末五碼流水號
	 * 
	 * @param 上傳案號格式
	 *            (102005LMS00066)
	 * @return 民國年 + 分行名稱+(兆)授字第+末五碼流水號
	 */
	private String getShowCaseNo(String projNo) {
		if (Util.isNotEmpty(projNo)) {
			String cYear = (LMSUtil.checkSubStr(projNo, 0, 3)) ? projNo
					.substring(0, 3) : projNo;
			String brno = (LMSUtil.checkSubStr(projNo, 3, 6)) ? projNo
					.substring(3, 6) : projNo;
			String brnoName = branch.getBranchName(brno);
			brnoName = brnoName.replace("分行", UtilConstants.Mark.SPACE);
			String caseSeq = (LMSUtil.checkSubStr(projNo, projNo.length() - 5)) ? projNo
					.substring(projNo.length() - 5) : projNo;
			projNo = StrUtils.concat(cYear, brnoName, "(兆)授字第", caseSeq, "號");
		}
		return projNo;
	}
}
