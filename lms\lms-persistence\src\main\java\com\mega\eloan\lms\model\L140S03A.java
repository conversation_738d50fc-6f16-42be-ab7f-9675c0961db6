/* 
 * L140S03A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 額度信用評等資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S03A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","crdType"}))
public class L140S03A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 評等（公佈）日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CRDTYEAR", columnDefinition="DATE")
	private Date crdTYear;

	/** 
	 * 評等單位<p/>
	 * 單位代號
	 */
	@Size(max=3)
	@Column(name="CRDTBR", length=3, columnDefinition="CHAR(3)")
	private String crdTBR;

	/** 
	 * 評等表類型<p/>
	 * 00:免辦<br/>
	 *  C1: 房貸申請信用評等<br/>
	 *  C2: 非房貸申請信用評等
	 */
	@Size(max=2)
	@Column(name="CRDTYPE", length=2, columnDefinition="CHAR(2)")
	private String crdType;

	/** 顯示順序 **/
	private Integer showOrder;

	/** 評等評分 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="SCORE", columnDefinition="DECIMAL(4,0)")
	private Integer score;

	/** 評等等級 **/
	@Size(max=6)
	@Column(name="GRADE", length=6, columnDefinition="CHAR(6)")
	private String grade;

	/** 評等展望 **/
	@Size(max=6)
	@Column(name="PROSPECT", length=6, columnDefinition="CHAR(6)")
	private String prospect;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 統一編號 */
	@Column(name="CUSTID", columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 */
	@Column(name="DUPNO", columnDefinition="CHAR(1)")
	private String dupNo;

	/** 對應消金評等檔OID */
	@Column(name="REFOID", columnDefinition="VARCHAR(32)")
	private String refOid;

	/** 姓名 */
	@Column(name="CUSTNAME", columnDefinition="VARCHAR(150)")
	private String custName;

    /**
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得評等（公佈）日期 **/
	public Date getCrdTYear() {
		return this.crdTYear;
	}
	/** 設定評等（公佈）日期 **/
	public void setCrdTYear(Date value) {
		this.crdTYear = value;
	}

	/** 
	 * 取得評等單位<p/>
	 * 單位代號
	 */
	public String getCrdTBR() {
		return this.crdTBR;
	}
	/**
	 *  設定評等單位<p/>
	 *  單位代號
	 **/
	public void setCrdTBR(String value) {
		this.crdTBR = value;
	}

	/** 
	 * 取得評等表類型<p/>
	 * 00:免辦<br/>
	 *  C1: 房貸申請信用評等<br/>
	 *  C2: 非房貸申請信用評等
	 */
	public String getCrdType() {
		return this.crdType;
	}
	/**
	 *  設定評等表類型<p/>
	 *  00:免辦<br/>
	 *  C1: 房貸申請信用評等<br/>
	 *  C2: 非房貸申請信用評等
	 **/
	public void setCrdType(String value) {
		this.crdType = value;
	}

	/** 取得顯示順序 **/
	public Integer getShowOrder() {
		return this.showOrder;
	}
	/** 設定顯示順序 **/
	public void setShowOrder(Integer value) {
		this.showOrder = value;
	}

	/** 取得評等評分 **/
	public Integer getScore() {
		return this.score;
	}
	/** 設定評等評分 **/
	public void setScore(Integer value) {
		this.score = value;
	}

	/** 取得評等等級 **/
	public String getGrade() {
		return this.grade;
	}
	/** 設定評等等級 **/
	public void setGrade(String value) {
		this.grade = value;
	}

	/** 取得評等展望 **/
	public String getProspect() {
		return this.prospect;
	}
	/** 設定評等展望 **/
	public void setProspect(String value) {
		this.prospect = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 統一編號
	 * @return
	 */
    public String getCustId() {
        return custId;
    }

	/**
	 * 統一編號
	 * @param custId
	 */
	public void setCustId(String custId) {
        this.custId = custId;
    }

	/**
	 * 重覆序號
	 * @return
	 */
	public String getDupNo() {
        return dupNo;
    }

	/**
	 * 重覆序號
	 * @param dupNo
	 */
	public void setDupNo(String dupNo) {
        this.dupNo = dupNo;
    }

	/**
	 * 對應消金評等檔OID
	 * @return
	 */
	public String getRefOid() {
        return refOid;
    }

	/**
	 * 對應消金評等檔OID
	 * @param refOid
	 */
	public void setRefOid(String refOid) {
        this.refOid = refOid;
    }

	/**
	 * 姓名
	 * @return
	 */
	public String getCustName() {
		return custName;
	}

	/**
	 * 姓名
	 * @param custName
	 */
	public void setCustName(String custName) {
		this.custName = custName;
	}
}
