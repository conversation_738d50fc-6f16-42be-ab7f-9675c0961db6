/* 
 * L140M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 額度授信科目資料檔 **/
@NamedEntityGraph(name = "L140M01C-entity-graph", attributeNodes = { @NamedAttributeNode("l140m01a") })
@Entity
@Table(name = "L140M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "loanTP" }))
public class L140M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 科目代碼 **/
	@Column(name = "LOANTP", length = 4, columnDefinition = "VARCHAR(4)")
	private String loanTP;

	/**
	 * 科目順序
	 * <p/>
	 */
	@Column(name = "SUBJSEQ", length = 2, columnDefinition = "VARCHAR(2)")
	private String subjSeq;

	/**
	 * 科目補充說明 <br/>
	 * 102.02.18 欄位擴大 60 -> 200
	 **/
	@Column(name = "SUBJDSCR", length = 200, columnDefinition = "VARCHAR(200)")
	private String subjDscr;

	/**
	 * 清償期限－天數
	 * <p/>
	 * 30、60、90、180、365 <br/>
	 * 101/10/24調整，改為自行輸入 DECIMAL(3,0)DECIMAL(5,0)
	 */
	@Column(name = "LMTDAYS", columnDefinition = "DECIMAL(5,0)")
	private Integer lmtDays;

	/** 清償期限－詳其他敘作條件 **/
	@Column(name = "LMTOTHER", length = 1, columnDefinition = "CHAR(01)")
	private String lmtOther;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 科目顯示順序
	 */
	@Column(name = "SEQNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer seqNum;
	
	/** 交易目的 **/
	@Column(name = "SUBJPURPOSE", length = 1, columnDefinition = "CHAR(1)")
	private String subjPurpose;
	
	/** 評等文件MainId **/
	@Column(name = "C121MAINID", length = 32, columnDefinition = "VARCHAR(32)")
	private String c121MainId;
	
	/** 評等文件Id **/
	@Column(name = "C121RATINGID", length = 32, columnDefinition = "VARCHAR(32)")
	private String c121RatingId;
	
	/** 採用評等文件中的人員 **/
	@Column(name = "C121CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String c121CustId;
	
	/** 採用評等文件中的人員 **/
	@Column(name = "C121DUPNO", length = 1, columnDefinition = "VARCHAR(1)")
	private String c121DupNo;
	
	/** 採用評等文件中人員的評分 **/
	@Column(name = "GRADE", length = 2, columnDefinition = "VARCHAR(2)")
	private String grade;
	
	/** 授信期間-年數 **/
	@Digits(integer=2, fraction=0)
	@Column(name="LNYEAR", columnDefinition="DECIMAL(2,0)")
	private Integer lnYear;

	/** 授信期間-月數 **/
	@Digits(integer=2, fraction=0)
	@Column(name="LNMONTH", columnDefinition="DECIMAL(2,0)")
	private Integer lnMonth;
	
	/** 清償期限資料格式(海外消金使用, 若為2表示清償期限<>授信期間, 應該抓lmtDays) **/
	@Size(max=1)
	@Column(name="REPAYMENTSCHFMT", length=1, columnDefinition="CHAR(1)")
	private String repaymentSchFmt;
	
	/**擔保品種類**/
	@Size(max=1)
	@Column(name="C121CMSTYPE", length=1, columnDefinition="CHAR(1)")
	private String c121CmsType;

	/**不動產種類**/
	@Size(max=1)
	@Column(name="C121LOCATIONTYPE", length=1, columnDefinition="CHAR(1)")
	private String c121LocationType;
	
	/**模型種類**/
	@Size(max=1)
	@Column(name="MODELTYPE", length=1, columnDefinition="CHAR(1)")
	private String modelType;
	
	/** 模型版本 **/
	@Size(max=3)
	@Column(name="VARVER", length=3, columnDefinition="VARCHAR(3)")
	private String varVer;
	
	/**
	 * JOIN條件 關聯檔
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得科目代碼 **/
	public String getLoanTP() {
		return this.loanTP;
	}

	/** 設定科目代碼 **/
	public void setLoanTP(String value) {
		this.loanTP = value;
	}

	/**
	 * 取得科目順序
	 * <p/>
	 */
	public String getSubjSeq() {
		return this.subjSeq;
	}

	/**
	 * 設定科目順序
	 * <p/>
	 **/
	public void setSubjSeq(String value) {
		this.subjSeq = value;
	}

	/** 取得科目補充說明 **/
	public String getSubjDscr() {
		return this.subjDscr;
	}

	/** 設定科目補充說明 VARCHAR(60) **/
	public void setSubjDscr(String value) {
		this.subjDscr = value;
	}

	/**
	 * 取得清償期限－天數
	 * <p/>
	 * 30、60、90、180、365 <br/>
	 * 101/10/24調整，改為自行輸入 DECIMAL(3,0)DECIMAL(5,0)
	 */
	public Integer getLmtDays() {
		return this.lmtDays;
	}

	/**
	 * 設定清償期限－天數
	 * <p/>
	 * 30、60、90、180、365 <br/>
	 * 101/10/24調整，改為自行輸入 DECIMAL(3,0)DECIMAL(5,0)
	 **/
	public void setLmtDays(Integer value) {
		this.lmtDays = value;
	}

	/** 取得清償期限－詳其他敘作條件 **/
	public String getLmtOther() {
		return this.lmtOther;
	}

	/** 設定清償期限－詳其他敘作條件 **/
	public void setLmtOther(String value) {
		this.lmtOther = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 設定科目顯示順序 **/
	public void setSeqNum(Integer seqNum) {
		this.seqNum = seqNum;
	}

	/** 取得科目顯示順序 **/
	public Integer getSeqNum() {
		return seqNum;
	}

	/** 設定交易目的**/
	public void setSubjPurpose(String subjPurpose) {
		this.subjPurpose = subjPurpose;
	}

	/** 取得交易目的**/
	public String getSubjPurpose() {
		return subjPurpose;
	}

	/** 取得評等文件MainId **/
	public String getC121MainId() {
		return c121MainId;
	}

	/** 設定評等文件MainId **/
	public void setC121MainId(String c121MainId) {
		this.c121MainId = c121MainId;
	}

	/** 取得評等文件Id **/
	public String getC121RatingId() {
		return c121RatingId;
	}

	/** 設定評等文件Id **/
	public void setC121RatingId(String c121RatingId) {
		this.c121RatingId = c121RatingId;
	}
	
	/** 取得採用評等文件中的人員 **/
	public String getC121CustId() {
		return c121CustId;
	}

	/** 設定採用評等文件中的人員 **/
	public void setC121CustId(String c121CustId) {
		this.c121CustId = c121CustId;
	}

	/** 取得採用評等文件中的人員 **/
	public String getC121DupNo() {
		return c121DupNo;
	}

	/** 設定採用評等文件中的人員 **/
	public void setC121DupNo(String c121DupNo) {
		this.c121DupNo = c121DupNo;
	}
	
	/** 取得採用評等文件中人員的評分 **/
	public String getGrade() {
		return grade;
	}
	
	/** 設定採用評等文件中人員的評分 **/
	public void setGrade(String grade) {
		this.grade = grade;
	}
	
	/** 取得授信期間-年數 **/
	public Integer getLnYear() {
		return this.lnYear;
	}
	/** 設定授信期間-年數 **/
	public void setLnYear(Integer value) {
		this.lnYear = value;
	}

	/** 取得授信期間-月數 **/
	public Integer getLnMonth() {
		return this.lnMonth;
	}
	/** 設定授信期間-月數 **/
	public void setLnMonth(Integer value) {
		this.lnMonth = value;
	}
	
	/** 取得清償期限資料格式 **/
	public String getRepaymentSchFmt() {
		return repaymentSchFmt;
	}
	/** 設定清償期限資料格式 **/
	public void setRepaymentSchFmt(String repaymentSchFmt) {
		this.repaymentSchFmt = repaymentSchFmt;
	}
	
	/** 取得擔保品種類 **/
	public String getC121CmsType() {
		return c121CmsType;
	}
	/** 設定擔保品種類 **/
	public void setC121CmsType(String c121CmsType) {
		this.c121CmsType = c121CmsType;
	}
	
	/** 取得不動產種類 **/
	public String getC121LocationType() {
		return c121LocationType;
	}
	/** 設定不動產種類 **/
	public void setC121LocationType(String c121LocationType) {
		this.c121LocationType = c121LocationType;
	}
	
	/** 取得模型種類 **/
	public String getModelType() {
		return modelType;
	}
	/** 設定模型種類 **/
	public void setModelType(String modelType) {
		this.modelType = modelType;
	}
	
	/** 取得模型版本 **/
	public String getVarVer() {
		return this.varVer;
	}
	/** 設定模型版本 **/
	public void setVarVer(String value) {
		this.varVer = value;
	}
}
