/* 
 * C160M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;

/** 額度明明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C160M01B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C160M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 額度明細表來源mainId **/
	@Size(max = 32)
	@Column(name = "REFMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String refmainId;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "VARCHAR(12)")
	private String cntrNo;

	/** 借款人統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 借款人重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "VARCHAR(1)")
	private String dupNo;

	/**
	 * 借款人名稱
	 * <p/>
	 * 40個中文字
	 */
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 案號 **/
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/** 授信額度合計幣別 **/
	@Size(max = 3)
	@Column(name = "LOANTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotCurr;

	/** 授信額度合計金額 **/
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "LOANTOTAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal LoanTotAmt;

	/** 黑名單資料查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BLACKDATADATE", columnDefinition = "DATE")
	private Date blackDataDate;

	/** 黑名單資料查詢日期 **/
	public Date getBlackDataDate() {
		return blackDataDate;
	}

	/** 黑名單資料查詢日期 **/
	public void setBlackDataDate(Date blackDataDate) {
		this.blackDataDate = blackDataDate;
	}

	/** 黑名單組成文字 **/
	@Size(max = 2400)
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	@Column(name = "BLACKDESC", length = 2400, columnDefinition = "VARCHAR(2400)")
	private String blackDesc;

	/**
	 * 共同行銷查詢日期
	 * <p/>
	 * 2012-11-15新增
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "JOINMARKETINGDATE", columnDefinition = "DATE")
	private Date joinMarketingDate;

	/**
	 * 共同行銷查詢日期
	 * <p/>
	 * 2012-11-15新增
	 */
	public Date getJoinMarketingDate() {
		return joinMarketingDate;
	}

	/**
	 * 共同行銷查詢日期
	 * <p/>
	 * 2012-11-15新增
	 */
	public void setJoinMarketingDate(Date joinMarketingDate) {
		this.joinMarketingDate = joinMarketingDate;
	}

	/** 共用行銷組成文字 **/
	@NotNull(message = "{required.message}", groups = Check2.class)
	@NotEmpty(message = "{required.message}", groups = Check2.class)
	@Size(max = 4000)
	@Column(name = "JOINMARKDESC", length = 4000, columnDefinition = "VARCHAR(4000)")
	private String joinMarkDesc;

	/** 年金 **/
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "ANNUITY", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal annuity;

	/** 取得年金 **/
	public BigDecimal getAnnuity() {
		return annuity;
	}

	/** 設定年金 **/
	public void setAnnuity(BigDecimal annuity) {
		this.annuity = annuity;
	}

	/** 取得其他所得 **/
	public BigDecimal getOthIncome() {
		return othIncome;
	}

	/** 設定其他所得 **/
	public void setOthIncome(BigDecimal othIncome) {
		this.othIncome = othIncome;
	}

	/** 其他所得 **/
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "OTHINCOME", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal othIncome;

	/**
	 * 資料檢核
	 * <p/>
	 * Y.通過檢核
	 */
	@Size(max = 1)
	@Column(name = "DATACHECK", length = 1, columnDefinition = "CHAR(1)")
	private String dataCheck;

	/**
	 * 取得資料檢核
	 * <p/>
	 * Y.通過檢核
	 */
	public String getDataCheck() {
		return this.dataCheck;
	}

	/**
	 * 設定資料檢核
	 * <p/>
	 * Y.通過檢核
	 **/
	public void setDataCheck(String value) {
		this.dataCheck = value;
	}

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 原授信額度合計幣別 **/
	@Size(max = 3)
	@Column(name = "BFLOANTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String bfLoanTotCurr;

	/** 原授信額度合計金額 **/
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "BFLOANTOTAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal bfLoanTotAmt;
	
	/** 額度序號是否在空地貸款控制檔中 **/
	@Size(max = 1)
	@Column(name = "ISCLEARLAND", length = 1, columnDefinition = "CHAR(1)")
	private String isClearLand;
	
	/** 控管類別 **/
	@Size(max = 1)
	@Column(name = "CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctlType;

	/** 初次核定預計動工日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "FSTDATE", columnDefinition = "DATE")
	private Date fstDate;
	
	/** 最新核定(動審)預計動工日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LSTDATE", columnDefinition = "DATE")
	private Date lstDate;
	
	/** 是否變更預計動工日 **/
	@Size(max = 1)
	@Column(name = "ISCHGSTDATE", length = 1, columnDefinition = "CHAR(1)")
	private String isChgStDate;
	
	/** 變更預計動工日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CSTDATE", columnDefinition = "DATE")
	private Date cstDate;
	
	/** 變更預計動工日原因 **/
	@Size(max = 2)
	@Column(name = "CSTREASON", length = 2, columnDefinition = "CHAR(2)")
	private String cstReason;
	
	/** 是否調降利率 **/
	@Size(max = 1)
	@Column(name = "ISCHGRATE", length = 1, columnDefinition = "CHAR(1)")
	private String isChgRate;
	
	/** 輸入本次採行措施 **/
	@Size(max = 30)
	@Column(name = "ADOPTFG", length = 30, columnDefinition = "CHAR(30)")
	private String adoptFg;
	
	/** 再加減碼幅度 **/
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "RATEADD", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal rateAdd;
	
	/** 借款人ROA **/
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "CUSTROA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal custRoa;
	
	/** 關係人ROA **/
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "RELROA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal relRoa;
	
	/** ROA查詢起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ROABGNDATE", columnDefinition = "DATE")
	private Date roaBgnDate;
	
	/** ROA查詢迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ROAENDDATE", columnDefinition = "DATE")
	private Date roaEndDate;
	
	/** 是否符合本行規定 **/
	@Size(max = 1)
	@Column(name = "ISLEGAL", length = 1, columnDefinition = "CHAR(1)")
	private String isLegal;
	
	/**
	 * 文件編號UID
	 * <p/>
	 * 102/01/17新增
	 */
	@Column(name = "UID", length = 32, columnDefinition = "CHAR(32)")
	private String uid;
	
	@Column(name = "IVRFLAG", length = 100, columnDefinition = "CHAR(100)")
	private String ivrFlag;
	
	/**
	 * J-113-0435 新增PLOAN傳來驗證後email欄位
	 */
	@Column(name = "CONFIRMEMAIL", length=120, columnDefinition="VARCHAR(120)")
	private String confirmEmail;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得額度明細表來源mainId **/
	public String getRefmainId() {
		return this.refmainId;
	}

	/** 設定額度明細表來源mainId **/
	public void setRefmainId(String value) {
		this.refmainId = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得借款人統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定借款人統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得借款人重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定借款人重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得借款人名稱
	 * <p/>
	 * 40個中文字
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定借款人名稱
	 * <p/>
	 * 40個中文字
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得案號 **/
	public String getCaseNo() {
		return this.caseNo;
	}

	/** 設定案號 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得授信額度合計幣別 **/
	public String getLoanTotCurr() {
		return this.LoanTotCurr;
	}

	/** 設定授信額度合計幣別 **/
	public void setLoanTotCurr(String value) {
		this.LoanTotCurr = value;
	}

	/** 取得授信額度合計金額 **/
	public BigDecimal getLoanTotAmt() {
		return this.LoanTotAmt;
	}

	/** 設定授信額度合計金額 **/
	public void setLoanTotAmt(BigDecimal value) {
		this.LoanTotAmt = value;
	}

	/** 取得黑名單組成文字 **/
	public String getBlackDesc() {
		return this.blackDesc;
	}

	/** 設定黑名單組成文字 **/
	public void setBlackDesc(String value) {
		this.blackDesc = value;
	}

	/** 取得共用行銷組成文字 **/
	public String getJoinMarkDesc() {
		return this.joinMarkDesc;
	}

	/** 設定共用行銷組成文字 **/
	public void setJoinMarkDesc(String value) {
		this.joinMarkDesc = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定原授信額度合計幣別 **/
	public void setBfLoanTotCurr(String value) {
		this.bfLoanTotCurr = value;
	}

	/** 取得原授信額度合計幣別 **/
	public String getBfLoanTotCurr() {
		return this.bfLoanTotCurr;
	}

	/** 設定原授信額度合計金額 **/
	public void setBfLoanTotAmt(BigDecimal value) {
		this.bfLoanTotAmt = value;
	}

	/** 取得原授信額度合計金額 **/
	public BigDecimal getBfLoanTotAmt() {
		return this.bfLoanTotAmt;
	}

	public String getIsClearLand() {
		return isClearLand;
	}

	public void setIsClearLand(String isClearLand) {
		this.isClearLand = isClearLand;
	}

	public String getCtlType() {
		return ctlType;
	}

	public void setCtlType(String ctlType) {
		this.ctlType = ctlType;
	}

	public Date getFstDate() {
		return fstDate;
	}

	public void setFstDate(Date fstDate) {
		this.fstDate = fstDate;
	}

	public Date getLstDate() {
		return lstDate;
	}

	public void setLstDate(Date lstDate) {
		this.lstDate = lstDate;
	}

	public String getIsChgStDate() {
		return isChgStDate;
	}

	public void setIsChgStDate(String isChgStDate) {
		this.isChgStDate = isChgStDate;
	}

	public Date getCstDate() {
		return cstDate;
	}

	public void setCstDate(Date cstDate) {
		this.cstDate = cstDate;
	}

	public String getCstReason() {
		return cstReason;
	}

	public void setCstReason(String cstReason) {
		this.cstReason = cstReason;
	}

	public String getIsChgRate() {
		return isChgRate;
	}

	public void setIsChgRate(String isChgRate) {
		this.isChgRate = isChgRate;
	}

	public String getAdoptFg() {
		return adoptFg;
	}

	public void setAdoptFg(String adoptFg) {
		this.adoptFg = adoptFg;
	}

	public BigDecimal getRateAdd() {
		return rateAdd;
	}

	public void setRateAdd(BigDecimal rateAdd) {
		this.rateAdd = rateAdd;
	}

	public BigDecimal getCustRoa() {
		return custRoa;
	}

	public void setCustRoa(BigDecimal custRoa) {
		this.custRoa = custRoa;
	}

	public BigDecimal getRelRoa() {
		return relRoa;
	}

	public void setRelRoa(BigDecimal relRoa) {
		this.relRoa = relRoa;
	}

	public Date getRoaBgnDate() {
		return roaBgnDate;
	}

	public void setRoaBgnDate(Date roaBgnDate) {
		this.roaBgnDate = roaBgnDate;
	}

	public Date getRoaEndDate() {
		return roaEndDate;
	}

	public void setRoaEndDate(Date roaEndDate) {
		this.roaEndDate = roaEndDate;
	}

	public String getIsLegal() {
		return isLegal;
	}

	public void setIsLegal(String isLegal) {
		this.isLegal = isLegal;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}
	
	/** 設定IVR語音檔紀錄 **/
	public void setIVRFlag(String IVRFlag) {
		this.ivrFlag = IVRFlag;
	}
	public String getIVRFlag() {
		return ivrFlag;
	}

	public String getConfirmEmail() {
		return confirmEmail;
	}

	public void setConfirmEmail(String confirmEmail) {
		this.confirmEmail = confirmEmail;
	}
	
}
