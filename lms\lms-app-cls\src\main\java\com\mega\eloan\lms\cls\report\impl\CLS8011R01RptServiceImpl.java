
package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLS8011Service;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.pages.CLS8011M01Page;
import com.mega.eloan.lms.model.C801M01A;
import com.mega.eloan.lms.model.C801M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * rpt報表service程式
 * </pre>
 * 
 * @since 2014/05/30
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
@Service("cls8011r01rptservice")
public class CLS8011R01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS8011R01RptServiceImpl.class);
	@Resource
	CLS8011Service cls8011Service;

	@Resource
	RetrialService retrialService;
	
	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			String printFlag = Util.trim(params.getString("printFlag"));
			if(Util.equals("N", printFlag)){
				baos = (ByteArrayOutputStream) this.generateXls(params);
			}else{
				baos = (ByteArrayOutputStream) this.generateReport(params);	
			}
			if(baos==null){
				return null;
			}else{
				return baos.toByteArray();	
			}
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
	
	private List<C801M01B> _filter(List<C801M01B> src, String itemType){
		List<C801M01B> r = new ArrayList<C801M01B>();
		for(C801M01B o : src){
			if(Util.equals(itemType, o.getItemType())){
				r.add(o);
			}
		}
		return r;
	}
	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception 
	 * @throws IOException 
	 */
	public OutputStream generateReport(PageParameters params) throws IOException, Exception {
	
		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = Util.trim(params.getString("rptOid")).split("\\|");
		OutputStream outputStream = null;
		Locale locale = null;
		
		try {
			locale = LMSUtil.getLocale();
						
			for (String temp : dataSplit) {
				
				outputStream = null;
				String oid = temp.split("\\^")[0];
				C801M01A meta = cls8011Service.findC801M01A_oid(oid);
				
				List<C801M01B> c801m01b_list = cls8011Service.findC801M01B_mainId(meta.getMainId());
				List<C801M01B> c801m01b_A_list = _filter(c801m01b_list, "A");
				List<C801M01B> c801m01b_B_list = _filter(c801m01b_list, "B");
				List<C801M01B> c801m01b_C_list = _filter(c801m01b_list, "C");
				
				outputStream = genCLS8011R01(locale, meta, c801m01b_A_list, c801m01b_B_list, c801m01b_C_list);
				if(outputStream != null){
					list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));				
				}	
			}
			
			outputStream = new ByteArrayOutputStream();
			//最後一個參數 直印/橫印
			PdfTools.mergeReWritePagePdf(list, outputStream, "", false, locale, 8, false);
		}finally{
			
		}
		return outputStream;
	}
	
	@SuppressWarnings("unchecked")
	public OutputStream generateXls(PageParameters params) throws IOException, Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String caseStatus = Util.trim(params.getString("caseStatus"));
		String oids = Util.trim(params.getString("oids"));
		String docType = Util.trim(params.getString("docType"));
		
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		List<C801M01A> list = new ArrayList<C801M01A>();
		if(true){			
			if(Util.isEmpty(oids)){
				ISearch pageSetting = retrialService.getMetaSearch();
				
				String docStatus = Util.trim(params.getString(EloanConstants.DOC_STATUS));
				if(Util.isNotEmpty(caseStatus)){
					pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseStatus", caseStatus);	
				}
				if(true){
					if(true){
						String caseApprId = Util.trim(params.getString("xls_caseApprId"));
						if(Util.isNotEmpty(caseApprId)){
							pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseApprId", caseApprId);	
						}	
					}
					
					String approveTime_beg_str = Util.trim(params.getString("xls_approveTime_beg"));
					//先將接近來自串轉成日期,因為pageinit關係吃不到datepikcer
					if(Util.isNotEmpty(approveTime_beg_str)){
					    String fullDateTimeStr = approveTime_beg_str + " 00:00:00";
					    
					    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					    Date approveTime_beg = dateFormat.parse(fullDateTimeStr);
					    
					    pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS, "approveTime", approveTime_beg);
					}
					String approveTime_end_str = Util.trim(params.getString("xls_approveTime_end"));
					if(Util.isNotEmpty(approveTime_end_str)){
					    String fullDateTimeStrEnd = approveTime_end_str + " 23:59:59";
					    
					    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					    Date approveTime_end = dateFormat.parse(fullDateTimeStrEnd);
					    
					    pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS, "approveTime", approveTime_end);
					}
				}
				
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
				pageSetting.addSearchModeParameters(SearchMode.IS_NULL,"deletedTime", null);
				
				if(Util.equals(docType, "1")){
					//企業戶
					pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType", docType);	
				}	
				else if(Util.equals(docType, "2")){
					//個人戶  2  或 空白
					pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docType", "1");	
				}else{
					//全部
					
				}
				
				if(true){
					pageSetting.addOrderBy("caseNo");
					pageSetting.addOrderBy("custId");
				}
				pageSetting.setMaxResults(Integer.MAX_VALUE);
				Page<? extends GenericBean> page = cls8011Service.findPage(C801M01A.class,pageSetting);
				list = (List<C801M01A>) page.getContent();
			}else{			
				String[] oid_arr = oids.split("\\|");
				for(String oid: oid_arr){
					C801M01A meta = cls8011Service.findC801M01A_oid(oid);
					if(meta!=null){
						list.add(meta);
					}
				}
			}
		}
		
		cls8011Service.exportExcel(outputStream, list);
			
		if(outputStream!=null){
			outputStream.flush();	
		}		
		return outputStream;
	}
	
	private String _riskLevel(Properties prop, String s){
		if(Util.equals("H", s)){
			return prop.getProperty("C801M01B.riskLevel.H");
		}else if(Util.equals("M", s)){
			return prop.getProperty("C801M01B.riskLevel.M");
		}else if(Util.equals("L", s)){
			return prop.getProperty("C801M01B.riskLevel.L");
		}else{
			return Util.trim(s);
		}
	}
	
	private String _itemCheck(Properties prop, String s){
		if(Util.equals("Y", s)){
			return prop.getProperty("C801M01B.itemCheck.Y");
		}else{
			return prop.getProperty("C801M01B.itemCheck.N");
		}
	}
	private String _storageLocation(Properties prop, String s){
		if(Util.equals("00", s)){
			return prop.getProperty("C801M01A.storageLocation.00");
		}else if(Util.equals("01", s)){
			return prop.getProperty("C801M01A.storageLocation.01");
		}else{
			return Util.trim(s);
		}
	}
	
	
	public OutputStream genCLS8011R01(Locale locale,C801M01A meta 
			,List<C801M01B> c801m01b_A_list
			,List<C801M01B> c801m01b_B_list
			,List<C801M01B> c801m01b_C_list)
			throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
				
		ReportGenerator generator = new ReportGenerator("report/cls/CLS8011R01_" + locale.toString() + ".rpt");

		OutputStream outputStream = null;		
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS8011M01Page.class);
		try {
			Map<String, Object> m_header = new HashMap<String, Object>();	
			LMSUtil.meta_to_map(m_header, meta, new String[]{"caseCancelDate"
					, "custName", "mName", "caseNo", "finalDate"
					, "custId", "guarantor", "destroyDate"
					, "coCustName", "custodian", "caseDate"
					, "cntrNo", "caseRecheck", "caseAppr"
					, "loanNo", "caseBoss", "caseApproveTime","chairmanName","docType"
			});
			
			m_header.put("storageLocation", _storageLocation(prop, meta.getStorageLocation()));
			//若 name 空白, 塞入 id
			if(StringUtils.isBlank(meta.getCustodian())){
				m_header.put("custodian", Util.trim(meta.getCustodianId()));	
			}
			if(StringUtils.isBlank(meta.getCaseRecheck())){
				m_header.put("caseRecheck", Util.trim(meta.getCaseRecheckId()));	
			}
			if(StringUtils.isBlank(meta.getCaseAppr())){
				m_header.put("caseAppr", Util.trim(meta.getCaseApprId()));	
			}
			if(StringUtils.isBlank(meta.getCaseBoss())){
				m_header.put("caseBoss", Util.trim(meta.getCaseBossId()));	
			}
			for(String k: m_header.keySet()){
				rptVariableMap.put(k, Util.trim(m_header.get(k)));
			}
			
			List<Map<String, String>> list = new LinkedList<Map<String, String>>();
			int size = Math.max(Math.max(c801m01b_A_list.size(), c801m01b_B_list.size()), c801m01b_C_list.size());
			for(int i=0;i<size;i++){
				Map<String, String> m = new HashMap<String, String>();
				C801M01B _a = i<c801m01b_A_list.size()?c801m01b_A_list.get(i): new C801M01B();
				m.put("CommonBean1.field01", Util.trim(_a.getItemSeq()));
				m.put("CommonBean1.field02", _itemCheck(prop, _a.getItemCheck()));
				m.put("CommonBean1.field03", Util.trim(_a.getItemContent()));
				m.put("CommonBean1.field04", _riskLevel(prop, _a.getRiskLevel()));
				
				C801M01B _b = i<c801m01b_B_list.size()?c801m01b_B_list.get(i): new C801M01B();
				m.put("CommonBean1.field05", Util.trim(_b.getItemSeq()));
				m.put("CommonBean1.field06", _itemCheck(prop, _b.getItemCheck()));
				m.put("CommonBean1.field07", Util.trim(_b.getItemContent()));
				m.put("CommonBean1.field08", _riskLevel(prop, _b.getRiskLevel()));
				
				C801M01B _c = i<c801m01b_C_list.size()?c801m01b_C_list.get(i): new C801M01B();
				m.put("CommonBean1.field09", Util.trim(_c.getItemSeq()));
				m.put("CommonBean1.field10", _itemCheck(prop, _c.getItemCheck()));
				m.put("CommonBean1.field11", Util.trim(_c.getItemContent()));
				m.put("CommonBean1.field12", _riskLevel(prop, _c.getRiskLevel()));
				list.add(m);
			}
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(list);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}			
		}
		return outputStream;
	}
}
