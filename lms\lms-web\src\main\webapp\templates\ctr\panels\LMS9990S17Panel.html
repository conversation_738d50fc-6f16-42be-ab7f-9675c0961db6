<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">loadScript('pagejs/ctr/LMS9990S17Panel');</script>
			<button type="button" id="btnModify" onclick="thickModify()"><span class="text-only"><th:block th:text="#{'C999M01AM07.setIndex'}">指定項次</th:block></span></button>
			<button type="button" id="btnDelete" onclick="delItemNo()"><span class="text-only"><th:block th:text="#{'C999M01AM07.delete'}">刪除</th:block></span></button>
			<button type="button" id="btnCancelDel" onclick="canDelItemNo()"><span class="text-only"><th:block th:text="#{'C999M01AM07.canceldel'}">取消刪除</th:block></span></button>
			<div id="sGridS17">
	            <div id="gridS17" ></div>
	        </div>
			<div id="thickModify" style="display:none;">
				<span class="color-red"><b><th:block th:text="#{'C999M01AM07.title01'}">產品種類</th:block>：</b></span>
	    		<div id="_sGridS17">
		            <div id="_gridS17" ></div>
		        </div>				
			</div>
			<div id="openDocGridS17" style="display: none;">
				<div id="loadPanelS17" openFlag="true"></div>
			</div>
        </th:block>
    </body>
</html>
