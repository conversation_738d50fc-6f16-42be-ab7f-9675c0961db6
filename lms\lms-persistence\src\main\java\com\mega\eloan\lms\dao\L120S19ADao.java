
package com.mega.eloan.lms.dao;

import com.mega.eloan.lms.model.L120S19A;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

public interface L120S19ADao extends IGenericDao<L120S19A> {

	L120S19A findByOid(String oid);

	List<L120S19A> findByMainId(String mainId);
	List<L120S19A> findByMainIdItemType(String mainId, String itemType);
	public List<L120S19A> findByMainIdItemTypeOrderBy(String mainId, String itemType,boolean isDesc);
	
	L120S19A findByMainId_itemType_latest_itemVersion(String mainId, String itemType);

	L120S19A findByMainId_itemType_first_itemVersion(String mainId, String itemType);
}