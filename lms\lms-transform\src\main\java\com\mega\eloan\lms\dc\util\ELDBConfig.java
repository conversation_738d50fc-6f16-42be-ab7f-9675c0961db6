package com.mega.eloan.lms.dc.util;

/*
 * ELDBConfig.java  version 1.0  2007/12/18
 *
 * History
 *    Vance  2007/12/18  version 1.0  Release.
 * 
 * Modified
 *    JasonYang 2010.10.15：因應 domino 提昇案 els1 與 els2 的 IP 位址變更修正
 *    
 * Copyright (C) 2007 Vance. All rights reserved.
 */

import java.io.File;
import java.io.FileInputStream;
import java.util.Properties;

import com.mega.eloan.lms.dc.conf.MainConfig;

/**
 * 從屬性檔取得預設之相關參數設定
 * 
 * <AUTHOR>
 */
public class ELDBConfig {

	// 讀取屬性檔相關變數
	private Properties prop = null;

	private String id = null;
	private String pw = null;

	// 專案屬性檔路徑
	private String configPath = "elconfig.properties";

	/**
	 * 建構子,使用預設路徑取得屬性檔案
	 */
	public ELDBConfig() {
		load(configPath);
	}

	/**
	 * 建構子,傳入檔案路徑取得屬性檔案
	 * 
	 * @param filePath
	 */
	public ELDBConfig(String filePath) {
		load(filePath);
	}

	private void load(String path) {
		prop = new Properties();
		try {
			String userPath = MainConfig.getInstance().getConfig().getDC_ROOT();

			prop.load(new FileInputStream(userPath + File.separator + "conf"
					+ File.separator + path));

			this.id = "db2inst1";
			this.pw = "db2inst1";
		} catch (Exception e) {
			System.out.println("loadProperties=" + e.toString());
		}
	}

	/**
	 * 取得屬性值.
	 * 
	 * @param key
	 *            欲取得之屬性
	 * @return String 屬性值.
	 */
	public String getProperty(String key) {
		return Util.trimSpace(prop.getProperty(key));
	}

	/**
	 * 取得屬性值.
	 * 
	 * @param key
	 *            欲取得之屬性
	 * @param dfValue
	 *            預設值
	 * @return String 屬性值.
	 */
	public String getProperty(String key, String dfValue) {
		return Util.trimSpace(prop.getProperty(key, dfValue));
	}

	/**
	 * 取得使用者ID
	 * 
	 * @param key
	 *            欲取得之屬性
	 * @return String 屬性值
	 */
	public String getID(String key) {
		return this.id;
	}

	/**
	 * 取得使用者密碼
	 * 
	 * @param key
	 *            欲取得之屬性
	 * @return String 屬性值
	 */
	public String getPw(String key) {
		return this.pw;
	}

	/**
	 * 取得URL
	 * 
	 * @param key
	 *            欲取得之屬性
	 * @return String 屬性值
	 */
	public String getUrl(String key) {
		return this.getProperty(key + ".jdbc.url");
	}

	/**
	 * 取得JDBC Driver
	 * 
	 * @param key
	 *            欲取得之屬性
	 * @return String 屬性值
	 */
	public String getDriver(String key) {
		return this.getProperty(key + ".jdbc.driver");
	}
}
