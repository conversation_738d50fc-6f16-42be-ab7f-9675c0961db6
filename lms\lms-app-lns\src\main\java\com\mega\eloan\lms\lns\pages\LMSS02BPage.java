package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.panels.LMSM01Panel;
import com.mega.eloan.lms.lms.pages.LMSS02Page;
import com.mega.eloan.lms.lns.panels.LMSS02BPanel;
import com.mega.eloan.lms.lns.panels.LMSS02BPanel01;
import com.mega.eloan.lms.lns.panels.LMSS03APanel;

/**
 * <pre>
 * 借款人企金 - 分頁
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lmss02b")
public class LMSS02BPage extends AbstractEloanForm {

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		super.afterExecute(model, params);
		
		new LMSS02BPanel01("lmss02panel01").processPanelData(model, params);
		new LMSM01Panel("lmsm01_panel").processPanelData(model, params);
		//renderJsI18N(LMS1201S05Page06.class);
		
		renderJsI18N(this.getClass());
		renderJsI18N(LMSM01Panel.class);

	}// ;
	
    /*
     * (non-Javadoc)
     * 
     * @see com.mega.eloan.common.pages.AbstractEloanForm#getViewName()
     */
    @Override
    public String getViewName() {
        // 不要headerarea
        return "common/pages/None";
    }

}
