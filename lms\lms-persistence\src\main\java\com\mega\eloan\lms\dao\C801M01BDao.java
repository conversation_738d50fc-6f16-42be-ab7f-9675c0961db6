/* 
 * C801M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C801M01B;

/** 個人資料檔案清冊明細檔 **/
public interface C801M01BDao extends IGenericDao<C801M01B> {

	C801M01B findByOid(String oid);
	
	List<C801M01B> findByMainId(String mainId);	
	List<C801M01B> findByMainId_itemType(String mainId, String itemType);
	int deleteByMainId(String mainId);
}