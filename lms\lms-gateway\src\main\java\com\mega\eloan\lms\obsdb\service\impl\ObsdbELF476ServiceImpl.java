/* 
 *ObsdbELF476ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.sql.Types;
import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF476Service;

/**
 * <pre>
 * 企金簽案費率檔  ELF476
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
@Service
public class ObsdbELF476ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF476Service {
	@Override
	public void insert(String BRNID, List<Object[]> dataList) {
		this.getJdbc(BRNID).batchUpdate(
				"ELF476.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.DECIMAL, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.DECIMAL,
						Types.DECIMAL, Types.DECIMAL, Types.CHAR, Types.CHAR,
						Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL,

						Types.DECIMAL, Types.CHAR, Types.DECIMAL, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL, Types.CHAR, Types.CHAR, Types.DECIMAL,

						Types.CHAR, Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL, Types.CHAR, Types.CHAR, Types.DECIMAL },
				dataList);

	}

	@Override
	public void delByKey(String BRNID, String cntrNo, String subject,
			String  sDate,String rtype) {
		this.getJdbc(BRNID).update("ELF476.delByUniqueKey",
				new Object[] { cntrNo, subject, sDate ,rtype});

	}
}
