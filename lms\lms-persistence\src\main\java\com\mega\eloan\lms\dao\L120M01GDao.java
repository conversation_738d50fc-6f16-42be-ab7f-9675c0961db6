/* 
 * L120M01GDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01G;

/** 整批貸款總額度資訊檔 **/
public interface L120M01GDao extends IGenericDao<L120M01G> {

	L120M01G findByOid(String oid);

	List<L120M01G> findByMainId(String mainId);

	L120M01G findByUniqueKey(String mainId);
	
	/**
	 * 同一個grpCntrNo 可能在 102年是 A建案, 103年是B建案
	 * 為了抓到正確的建案名稱
	 * 不能用 grpCntrNo去抓, 要用 mainId 去抓
	 */
	@Deprecated
	L120M01G findByGrpCntrNo(String grpCntrNo);

	List<L120M01G> findByIndex01(String mainId);
}