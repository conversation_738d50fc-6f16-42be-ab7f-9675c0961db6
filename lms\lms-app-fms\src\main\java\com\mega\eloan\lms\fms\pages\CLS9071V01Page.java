package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

/**
 * <pre>
 * 團貸明細查詢
 * </pre>
 * 
 * @since 2016/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2016/12/20,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/fms/cls9071v01")
public class CLS9071V01Page extends AbstractEloanInnerView {

	public CLS9071V01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));

		renderJsI18N(CLS9071V01Page.class);
	}

	public String[] getJavascriptPath() {

		return new String[] { "pagejs/fms/CLS9071V01Page.js" };
	}
}
