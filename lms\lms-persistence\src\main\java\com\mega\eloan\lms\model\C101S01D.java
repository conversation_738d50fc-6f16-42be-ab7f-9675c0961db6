/* 
 * C101S01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.SaveCheck;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金配偶資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01D", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C101S01D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 是否有配偶資料
	 * <p/>
	 * A. 不登錄配偶資料<br/>
	 * B. 列於本欄<br/>
	 * C. 同本案借款人
	 */
	@Size(max = 1)
	@Column(name = "MATEFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String mateFlag;

	/** 配偶統一編號 **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 10)
	@Column(name = "MCUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String mCustId;

	/** 配偶統一編號重複碼 **/
	@Size(max = 1)
	@Column(name = "MDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String mDupNo;

	/** 配偶姓名 **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 250)
	@Column(name = "MNAME", length = 250, columnDefinition = "VARCHAR(250)")
	private String mName;

	/**
	 * 外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@Size(max = 1)
	@Column(name = "MFOREGINAL", length = 1, columnDefinition = "CHAR(1)")
	private String mForeginal;

	/**
	 * 性別
	 * <p/>
	 * M男、F女
	 */
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "MSEX", length = 1, columnDefinition = "CHAR(1)")
	private String mSex;

	/** 出生日期 **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "MBIRTHDAY", columnDefinition = "DATE")
	private Date mBirthday;

	/**
	 * 行業別
	 * <p/>
	 * 100/12/08調整<br/>
	 * (單選)<br/>
	 * 01.公<br/>
	 * 02.軍<br/>
	 * 03.製造<br/>
	 * 04.營造<br/>
	 * 05.金融<br/>
	 * 06.資訊<br/>
	 * 07.保險<br/>
	 * 08.個人服務<br/>
	 * 99.其他(自行輸入)
	 */
	@Size(max = 2)
	@Column(name = "MJOBKIND", length = 2, columnDefinition = "CHAR(2)")
	private String mJobKind;

	/**
	 * 行業別(其他)
	 * <p/>
	 * 100/12/08新增<br/>
	 * 其他(自行輸入)
	 */
	@Size(max = 60)
	@Column(name = "MJOBOTHER", length = 60, columnDefinition = "VARCHAR(60)")
	private String mJobOther;

	/** 服務單位名稱 **/
	@Size(max = 250)
	@Column(name = "MCOMNAME", length = 250, columnDefinition = "VARCHAR(250)")
	private String mComName;

	/**
	 * 服務單位地址(eland)
	 * <p/>
	 * Eland代碼
	 */
	@Size(max = 10)
	@Column(name = "MCOMELAND", length = 10, columnDefinition = "VARCHAR(10)")
	private String mComEland;

	/** 服務單位地址(縣市) **/
	@Size(max = 2)
	@Column(name = "MCOMCITY", length = 2, columnDefinition = "VARCHAR(2)")
	private String mComCity;

	/**
	 * 服務單位地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 */
	@Size(max = 5)
	@Column(name = "MCOMZIP", length = 5, columnDefinition = "VARCHAR(5)")
	private String mComZip;

	/**
	 * 服務單位地址
	 * <p/>
	 * 64個全型字
	 */
	@Size(max = 192)
	@Column(name = "MCOMADDR", length = 192, columnDefinition = "VARCHAR(192)")
	private String mComAddr;

	/** 服務單位地址(標的) **/
	@Size(max = 300)
	@Column(name = "MCOMTARGET", length = 300, columnDefinition = "VARCHAR(300)")
	private String mComTarget;

	/** 服務單位電話 **/
	@Size(max = 150)
	@Column(name = "MCOMTEL", length = 150, columnDefinition = "VARCHAR(150)")
	private String mComTel;

	/** 職稱 **/
	@Size(max = 60)
	@Column(name = "MJOBTITLE", length = 60, columnDefinition = "VARCHAR(60)")
	private String mJobTitle;

	/** 年資 **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "MSENIORITY", columnDefinition = "DECIMAL(2,0)")
	private Integer mSeniority;

	/** 年薪(幣別) **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 3)
	@Column(name = "MPAYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String mPayCurr;

	/** 年薪(金額) **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "MPAYAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal mPayAmt;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得是否有配偶資料
	 * <p/>
	 * A. 不登錄配偶資料<br/>
	 * B. 列於本欄<br/>
	 * C. 同本案借款人
	 */
	public String getMateFlag() {
		return this.mateFlag;
	}

	/**
	 * 設定是否有配偶資料
	 * <p/>
	 * A. 不登錄配偶資料<br/>
	 * B. 列於本欄<br/>
	 * C. 同本案借款人
	 **/
	public void setMateFlag(String value) {
		this.mateFlag = value;
	}

	/** 取得配偶統一編號 **/
	public String getMCustId() {
		return this.mCustId;
	}

	/** 設定配偶統一編號 **/
	public void setMCustId(String value) {
		this.mCustId = value;
	}

	/** 取得配偶統一編號重複碼 **/
	public String getMDupNo() {
		return this.mDupNo;
	}

	/** 設定配偶統一編號重複碼 **/
	public void setMDupNo(String value) {
		this.mDupNo = value;
	}

	/** 取得配偶姓名 **/
	public String getMName() {
		return this.mName;
	}

	/** 設定配偶姓名 **/
	public void setMName(String value) {
		this.mName = value;
	}

	/**
	 * 取得外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public String getMForeginal() {
		return this.mForeginal;
	}

	/**
	 * 設定外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setMForeginal(String value) {
		this.mForeginal = value;
	}

	/**
	 * 取得性別
	 * <p/>
	 * M男、F女
	 */
	public String getMSex() {
		return this.mSex;
	}

	/**
	 * 設定性別
	 * <p/>
	 * M男、F女
	 **/
	public void setMSex(String value) {
		this.mSex = value;
	}

	/** 取得出生日期 **/
	public Date getMBirthday() {
		return this.mBirthday;
	}

	/** 設定出生日期 **/
	public void setMBirthday(Date value) {
		this.mBirthday = value;
	}

	/**
	 * 取得行業別
	 * <p/>
	 * 100/12/08調整<br/>
	 * (單選)<br/>
	 * 01.公<br/>
	 * 02.軍<br/>
	 * 03.製造<br/>
	 * 04.營造<br/>
	 * 05.金融<br/>
	 * 06.資訊<br/>
	 * 07.保險<br/>
	 * 08.個人服務<br/>
	 * 99.其他(自行輸入)
	 */
	public String getMJobKind() {
		return this.mJobKind;
	}

	/**
	 * 設定行業別
	 * <p/>
	 * 100/12/08調整<br/>
	 * (單選)<br/>
	 * 01.公<br/>
	 * 02.軍<br/>
	 * 03.製造<br/>
	 * 04.營造<br/>
	 * 05.金融<br/>
	 * 06.資訊<br/>
	 * 07.保險<br/>
	 * 08.個人服務<br/>
	 * 99.其他(自行輸入)
	 **/
	public void setMJobKind(String value) {
		this.mJobKind = value;
	}

	/**
	 * 取得行業別(其他)
	 * <p/>
	 * 100/12/08新增<br/>
	 * 其他(自行輸入)
	 */
	public String getMJobOther() {
		return this.mJobOther;
	}

	/**
	 * 設定行業別(其他)
	 * <p/>
	 * 100/12/08新增<br/>
	 * 其他(自行輸入)
	 **/
	public void setMJobOther(String value) {
		this.mJobOther = value;
	}

	/** 取得服務單位名稱 **/
	public String getMComName() {
		return this.mComName;
	}

	/** 設定服務單位名稱 **/
	public void setMComName(String value) {
		this.mComName = value;
	}

	/**
	 * 取得服務單位地址(eland)
	 * <p/>
	 * Eland代碼
	 */
	public String getMComEland() {
		return this.mComEland;
	}

	/**
	 * 設定服務單位地址(eland)
	 * <p/>
	 * Eland代碼
	 **/
	public void setMComEland(String value) {
		this.mComEland = value;
	}

	/** 取得服務單位地址(縣市) **/
	public String getMComCity() {
		return this.mComCity;
	}

	/** 設定服務單位地址(縣市) **/
	public void setMComCity(String value) {
		this.mComCity = value;
	}

	/**
	 * 取得服務單位地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 */
	public String getMComZip() {
		return this.mComZip;
	}

	/**
	 * 設定服務單位地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 **/
	public void setMComZip(String value) {
		this.mComZip = value;
	}

	/**
	 * 取得服務單位地址
	 * <p/>
	 * 64個全型字
	 */
	public String getMComAddr() {
		return this.mComAddr;
	}

	/**
	 * 設定服務單位地址
	 * <p/>
	 * 64個全型字
	 **/
	public void setMComAddr(String value) {
		this.mComAddr = value;
	}

	/** 取得服務單位地址(標的) **/
	public String getMComTarget() {
		return this.mComTarget;
	}

	/** 設定服務單位地址(標的) **/
	public void setMComTarget(String value) {
		this.mComTarget = value;
	}

	/** 取得服務單位電話 **/
	public String getMComTel() {
		return this.mComTel;
	}

	/** 設定服務單位電話 **/
	public void setMComTel(String value) {
		this.mComTel = value;
	}

	/** 取得職稱 **/
	public String getMJobTitle() {
		return this.mJobTitle;
	}

	/** 設定職稱 **/
	public void setMJobTitle(String value) {
		this.mJobTitle = value;
	}

	/** 取得年資 **/
	public Integer getMSeniority() {
		return this.mSeniority;
	}

	/** 設定年資 **/
	public void setMSeniority(Integer value) {
		this.mSeniority = value;
	}

	/** 取得年薪(幣別) **/
	public String getMPayCurr() {
		return this.mPayCurr;
	}

	/** 設定年薪(幣別) **/
	public void setMPayCurr(String value) {
		this.mPayCurr = value;
	}

	/** 取得年薪(金額) **/
	public BigDecimal getMPayAmt() {
		return this.mPayAmt;
	}

	/** 設定年薪(金額) **/
	public void setMPayAmt(BigDecimal value) {
		this.mPayAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
