/* 
 * BrowserActionFormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import javax.annotation.Resource;


import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.SysParameterService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 跨System相關method
 * </pre>
 * 
 * @since 2012/5/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/5/22,Fantasy,new
 *          </ul>
 */
@Scope("request")
@Controller("browseractionformhandler")
public class BrowserActionFormHandler extends AbstractFormHandler {

	@Resource
	SysParameterService sysParameterService;

	public IResult querySystemData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String system = Util.trim(params.getString("system")).toUpperCase();
		// value = http://192.168.53.85:9080/ces-web/app/ssoverify
		String value = Util.trim(sysParameterService
				.getParamValue(SysParamConstants.SSO_URL_PREFIX + system));
		if (Util.isNotEmpty(value)) {
			result.set("ssoverify", value);
		} else {
			// 查無資料
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}

		return result;
	}

	public IResult queryData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		CapAjaxFormResult data = new CapAjaxFormResult();
		data.set("ces", Util.trim(sysParameterService
				.getParamValue(SysParamConstants.SSO_URL_CES)));
		data.set("lms", Util.trim(sysParameterService
				.getParamValue(SysParamConstants.SSO_URL_LMS)));
		data.set("cms", Util.trim(sysParameterService
				.getParamValue(SysParamConstants.SSO_URL_CMS)));
		data.set("col", Util.trim(sysParameterService
				.getParamValue(SysParamConstants.SSO_URL_COL)));
		data.set("deb", Util.trim(sysParameterService
				.getParamValue(SysParamConstants.SSO_URL_DEB)));
		data.set("rps", Util.trim(sysParameterService
				.getParamValue(SysParamConstants.SSO_URL_RPS)));
		// data.set("adm", Util.trim(sysParameterService
		// .getParamValue(SysParamConstants.SSO_URL_ADM)));

		result.set("data", data);

		return result;
	}
}
