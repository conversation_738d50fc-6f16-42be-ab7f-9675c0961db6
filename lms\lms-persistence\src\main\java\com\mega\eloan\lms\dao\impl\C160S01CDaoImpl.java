/* 
 * C160S01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C160S01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160S01C;

/** 個金產品種類檔 **/
@Repository
public class C160S01CDaoImpl extends LMSJpaDao<C160S01C, String>
	implements C160S01CDao {

	@Override
	public C160S01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160S01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C160S01C> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public List<C160S01C> findByMainIdRefMainid(String mainId,String refmainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		List<C160S01C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C160S01C> findByMainIdRefMainidOrderBySeq(String mainId,String refmainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		search.addOrderBy("seq");
		List<C160S01C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C160S01C> findByMainIdRefMainidOrderByUiSeq(String mainId,String refmainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		{
			search.setOrderBy(uiSeq_seqMap());
		}		
		
		List<C160S01C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C160S01C findByUniqueKey(String mainId, Integer seq, String refmainId){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (refmainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C160S01C> findByIndex01(String mainId, Integer seq, String refmainId){
		ISearch search = createSearchTemplete();
		List<C160S01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (refmainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	private Map<String, Boolean> uiSeq_seqMap(){
		Map<String, Boolean> orderByMap = new HashMap<String, Boolean>();
		orderByMap.put("uiSeq", false);
		orderByMap.put("seq", false);
		return orderByMap;
	}
}