/* 
 * L180M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 覆審名單明細額度序號檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L180M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "elfCntrType", "elfCustCoId", "elfCntrNo",
		"ctlType" }))
public class L180M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 借款人ID
	 * <p/>
	 * ELF412_CUSTID
	 */
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 借款人重覆序號
	 * <p/>
	 * ELF412_DUPNO
	 */
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 種類
	 * <p/>
	 * 新作 | N<br/>
	 * 增額 | C<br/>
	 * DBU共管 | D<br/>
	 * OBU共管 | O
	 */
	@Column(name = "ELFCNTRTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elfCntrType;

	/**
	 * 共管客戶統編 (含重覆序號)
	 * <p/>
	 * ※海外目前無使用<br/>
	 * ELF412_DBUCOID<br/>
	 * DBU共管客戶統編<br/>
	 * ELF412_OBUCOID<br/>
	 * OBU共管客戶統編
	 */
	@Column(name = "ELFCUSTCOID", length = 11, columnDefinition = "CHAR(11)")
	private String elfCustCoId;

	/**
	 * 額度序號
	 * <p/>
	 * ELF411_CNTRNO
	 */
	@Column(name = "ELFCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String elfCntrNo;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 覆審名單類別 J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Column(name = "CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctlType;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得借款人ID
	 * <p/>
	 * ELF412_CUSTID
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定借款人ID
	 * <p/>
	 * ELF412_CUSTID
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得借款人重覆序號
	 * <p/>
	 * ELF412_DUPNO
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定借款人重覆序號
	 * <p/>
	 * ELF412_DUPNO
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得種類
	 * <p/>
	 * 新作 | N<br/>
	 * 增額 | C<br/>
	 * DBU共管 | D<br/>
	 * OBU共管 | O
	 */
	public String getElfCntrType() {
		return this.elfCntrType;
	}

	/**
	 * 設定種類
	 * <p/>
	 * 新作 | N<br/>
	 * 增額 | C<br/>
	 * DBU共管 | D<br/>
	 * OBU共管 | O
	 **/
	public void setElfCntrType(String value) {
		this.elfCntrType = value;
	}

	/**
	 * 取得共管客戶統編 (含重覆序號)
	 * <p/>
	 * ※海外目前無使用<br/>
	 * ELF412_DBUCOID<br/>
	 * DBU共管客戶統編<br/>
	 * ELF412_OBUCOID<br/>
	 * OBU共管客戶統編
	 */
	public String getElfCustCoId() {
		return this.elfCustCoId;
	}

	/**
	 * 設定共管客戶統編 (含重覆序號)
	 * <p/>
	 * ※海外目前無使用<br/>
	 * ELF412_DBUCOID<br/>
	 * DBU共管客戶統編<br/>
	 * ELF412_OBUCOID<br/>
	 * OBU共管客戶統編
	 **/
	public void setElfCustCoId(String value) {
		this.elfCustCoId = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * ELF411_CNTRNO
	 */
	public String getElfCntrNo() {
		return this.elfCntrNo;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * ELF411_CNTRNO
	 **/
	public void setElfCntrNo(String value) {
		this.elfCntrNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 設定覆審名單類別 **/
	public void setCtlType(String ctlType) {
		this.ctlType = ctlType;
	}

	/** 取得覆審名單類別 **/
	public String getCtlType() {
		return ctlType;
	}

}
