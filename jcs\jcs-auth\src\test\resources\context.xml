<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc.xsd">

	<context:annotation-config />
	<jdbc:embedded-database id="dataSource" type="H2">
		<jdbc:script location="classpath:ddl/ELSBRN.txt"/>
		<jdbc:script location="classpath:ddl/ELSPGM.txt"/>
		<jdbc:script location="classpath:ddl/ELSRLE.txt"/>
		<jdbc:script location="classpath:ddl/ELSRLF.txt"/>
		<jdbc:script location="classpath:ddl/ELSUSR.txt"/>
		<jdbc:script location="classpath:ddl/ELSUSRR.txt"/>
	</jdbc:embedded-database>

	<bean id="authQueryFactory" class="tw.com.jcs.auth.impl.AuthQueryFactoryImpl">
		<property name="configLocation" value="auth-cfg.xml" />
	</bean>

	<bean id="memberService" class="tw.com.jcs.auth.impl.MemberServiceImpl" />
	<bean id="authService" class="tw.com.jcs.auth.impl.AuthServiceImpl" />
	<bean id="codeItemService" class="tw.com.jcs.auth.impl.CodeItemServiceImpl" />


</beans>
