package com.mega.eloan.lms.base.service;

import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONArray;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.model.L250M01B;
import com.mega.eloan.lms.model.L250S02B;
import com.mega.eloan.lms.model.L250S04A;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 模擬動審
 * </pre>
 * 
 * @since 2017/07/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/07/01,007625,new
 *          </ul>
 */
public interface LMS2501Service extends AbstractService {

	String genLMSCheckList();

	JSONArray getSavedList(L250M01A meta);

	L250M01A updateMetaInfoTab(L250M01A meta) throws CapException;

	L250M01A updateCheckListTab(L250M01A meta, JSONArray listData)
			throws CapException;

	void removeLmsCheckList(L250M01A meta);

	void deleteLmsMeta(L250M01A meta);

	List<L250S04A> getL250s04as(L250M01A meta);

	void flowAction(String mainOid, GenericBean model, boolean setResult,
			String action) throws Throwable;

	void save(L250M01A meta, List<L250M01B> l250m01bs);

	String genCLSCheckList();

	L250M01A updateClsCheckListTab(L250M01A meta, JSONArray listData)
			throws CapException;

	JSONArray getClsSavedList(L250M01A meta);

	void deleteClsMeta(L250M01A meta);

	void removeClsCheckList(L250M01A meta);

	int getVersion(String rptId);
	
	public boolean getProjClassFromL250M01A(String oid) ;
	public void saveIVRFlag(String oid, List<String> addIVRList) throws CapException;
	public void deleteIVRFlag(String oid, String custId, String fileName) throws CapException;
	public List<Map<String, Object>> getIVRgrid(String oid) throws CapException;
	public List<Map<String, Object>> getIVRFiltergrid(String oid) throws CapException;
	public L140M01A findByMainId(String reMainId);
	
	public List<L250S02B> findL250S02B(String mainId);
}
