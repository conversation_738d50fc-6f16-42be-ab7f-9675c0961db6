package com.mega.eloan.lms.dc.bean;

import java.io.Serializable;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

public class L140S02BBean extends BaseBean implements Serializable {
	private static final long serialVersionUID = 3656228504251339093L;

	private String seq = "";// 序號
	private String cntrNo = "";// 流用(長擔)額度序號

	public String getSeq() {
		return seq;
	}

	public void setSeq(String seq) {
		this.seq = seq;
	}

	public String getCntrNo() {
		return cntrNo;
	}

	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append(Util.nullToSpace(this.getOid()))
				.append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getMainId()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getSeq()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getCntrNo()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(this.getCreator()).append(TextDefine.FILE_DELIM);
		sb.append(this.getCreateTime()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdater()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdateTime());
		return sb.toString();
	}

}
