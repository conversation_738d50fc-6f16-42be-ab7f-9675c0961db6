/* 
 * L999S04BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999S04B;

/** 中長期契約書授信內容及條件檔 **/
public interface L999S04BDao extends IGenericDao<L999S04B> {

	L999S04B findByOid(String oid);

	List<L999S04B> findByMainId(String mainId);

	L999S04B findByUniqueKey(String mainId, String itemType);

	List<L999S04B> findByIndex01(String mainId, String itemType);
}