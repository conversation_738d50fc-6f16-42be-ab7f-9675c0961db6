/* 
 * C900M01HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01H;

/** 地政士黑名單 **/
public interface C900M01HDao extends IGenericDao<C900M01H> {

	C900M01H findByOid(String oid);
	
	List<C900M01H> findByMainId(String mainId);
	
	List<C900M01H> findActiveByCertNo(String year, String word, String no);
	
	C900M01H findActiveMajorByCertNo(String year, String word, String no);
}