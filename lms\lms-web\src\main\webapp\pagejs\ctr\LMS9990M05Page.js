/**
 * 約據書 中長期契約書
 * @since
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
var initDfd = $.Deferred();
var inits = {
    fhandle: "lms9990m01formhandler",
    queryAction: "queryL999m01aM05",
    saveAction: "saveL999m01aM05",
    contractType: "04"//約據書種類
};
var LMS999Action = {  
    ActionMFormId: "#ActionMForm",
    save: function(showMsg, tofn){
        var $ActionMForm = $(LMS999Action.ActionMFormId);
        if ($ActionMForm.valid()) {
            $.ajax({
                async: false,
                handler: inits.fhandle,
                action: inits.saveAction,
                data: {
                    showMsg: showMsg,
                    contractType: inits.contractType,//約據書種類
                    ActionMForm: JSON.stringify($ActionMForm.serializeData())
                },
            }).done(function(responseData){
				//$ActionMForm.injectData(responseData);
				if (tofn) {
				    tofn();
				}
				
			});
        }
    },
    print: function(){
        $.capFileDownload({
            handler: "lmsdownloadformhandler",
            data: {
                mainId: responseJSON.mainId,
                contractType: responseJSON.contractType,
                fileDownloadName: "LMS9990W04.doc",
                serviceName: "lms9990doc01service"
            }
        });
    },
    //驗證readOnly狀態
    checkReadonly: function(){
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        if (auth.readOnly) {
            return true;
        }
        return false;
    },
    showdataUseItemData: function(json){
        var obj = CommonAPI.loadCombos(["megacompany"]);
        var data = {
            width: "50%",
            border: "none",
            value: json.dataUseItem,
            size: 1,
            fn: function(){
                LMS999Action.checkAll($(this));
            },
            item: obj.megacompany
        };
        $("#dataUseItem").setItems(data);
        LMS999Action.checkAll($("#dataUseItem"));
    
    },
    checkAll: function(obj){
        var $checkBox = $("input[type=checkbox][name=dataUseItem][value!=0]");
        if (obj.val() == 0) {
            var chk0 = $("input[type=checkbox][name=dataUseItem][value='" + DOMPurify.sanitize(obj.val()) + "']").is(":checked")
            if (chk0) {
                $checkBox.prop("checked", false);
                if ($(obj).prop("checked")) {
                    $checkBox.prop("disabled", true);
                }
                else {
                    $checkBox.prop("disabled", false);
                }
                
            }else{
				$checkBox.prop("disabled", false);
			}
            
        }
    },
    doChecked: function(json){
        $("input[type=radio][name=dataUseFlag][value='" + json.dataUseFlag + "']").prop("checked", true);
    }
};

$(function() {
    $.form.init({
        formHandler: inits.fhandle,
        formPostData: {
            formAction: inits.queryAction,
            srcMainId: responseJSON.srcMainId
        },
        loadSuccess: function(json){
            LMS999Action.doChecked(json);
            LMS999Action.showdataUseItemData(json);
            initDfd.resolve(json);
        }
    });
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(){
        LMS999Action.save(true);
    }).end().find("#btnPrint").click(function(){
        if (LMS999Action.checkReadonly()) {
            LMS999Action.print();
        }
        else {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
                    LMS999Action.save(false, LMS999Action.print);
                }
            });
        }
    });
});






