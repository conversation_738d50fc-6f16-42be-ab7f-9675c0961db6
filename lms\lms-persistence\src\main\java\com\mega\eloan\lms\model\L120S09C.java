/* 
 * L120S09C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 洗錢資恐態樣檢核表 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S09C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S09C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 客戶名稱 **/
	@Size(max=3000)
	@Column(name="CUSTNAME", length=3000, columnDefinition="VARCHAR(3000)")
	private String custName;

	/** 統一編號 **/
	@Size(max=2000)
	@Column(name="CUSTID", length=2000, columnDefinition="VARCHAR(2000)")
	private String custId;

	/** 案件號碼 **/
	@Size(max=62)
	@Column(name="CASENO", length=62, columnDefinition="VARCHAR(62)")
	private String caseNo;

	/** 交易日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CASEDATE", columnDefinition="DATE")
	private Date caseDate;

	/** 檢視時點 **/
	@Size(max=1)
	@Column(name="CASETYPE", length=1, columnDefinition="CHAR(1)")
	private String caseType;

	/** 態樣檢核1 **/
	@Size(max=1)
	@Column(name="CHECKITEM1", length=1, columnDefinition="CHAR(1)")
	private String checkItem1;

	/** 態樣檢核2 **/
	@Size(max=1)
	@Column(name="CHECKITEM2", length=1, columnDefinition="CHAR(1)")
	private String checkItem2;

	/** 態樣檢核3 **/
	@Size(max=1)
	@Column(name="CHECKITEM3", length=1, columnDefinition="CHAR(1)")
	private String checkItem3;

	/** 態樣檢核4 **/
	@Size(max=1)
	@Column(name="CHECKITEM4", length=1, columnDefinition="CHAR(1)")
	private String checkItem4;

	/** 態樣檢核5 **/
	@Size(max=1)
	@Column(name="CHECKITEM5", length=1, columnDefinition="CHAR(1)")
	private String checkItem5;

	/** 金額說明 **/
	@Size(max=3000)
	@Column(name="CASEAMT", length=3000, columnDefinition="VARCHAR(3000)")
	private String caseAmt;

	/** 交易目的說明 **/
	@Size(max=3000)
	@Column(name="CASEPURPOSE", length=3000, columnDefinition="VARCHAR(3000)")
	private String casePurpose;

	/** 資金來源說明 **/
	@Size(max=3000)
	@Column(name="CASESOURCE", length=3000, columnDefinition="VARCHAR(3000)")
	private String caseSource;

	/** 行業別說明 **/
	@Size(max=3000)
	@Column(name="ECONM", length=3000, columnDefinition="VARCHAR(3000)")
	private String ecoNm;

	/** 其他說明 **/
	@Size(max=3000)
	@Column(name="CASEOTHER", length=3000, columnDefinition="VARCHAR(3000)")
	private String caseOther;

	/** 是否承作 **/
	@Size(max=1)
	@Column(name="ISAPPROVE", length=1, columnDefinition="CHAR(1)")
	private String isApprove;

	/** 承作理由敘述 **/
	@Size(max=3000)
	@Column(name="ISAPPROVEREASON", length=3000, columnDefinition="VARCHAR(3000)")
	private String isApproveReason;

	/** 是否申報 **/
	@Size(max=1)
	@Column(name="ISAML", length=1, columnDefinition="CHAR(1)")
	private String isAml;
	
	/** 申報理由敘述 **/
	@Size(max=3000)
	@Column(name="ISAMLREASON", length=3000, columnDefinition="VARCHAR(3000)")
	private String isAmlReason;
	
	/** 版本日期 **/
	@Size(max=8)
	@Column(name="VERSIONDATE", length=3000, columnDefinition="VARCHAR(8)")
	private String versionDate;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}
	
	/** 取得統一編號 **/
	public String getCustId() {
		return custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String custId) {
		this.custId = custId;
	}
	
	/** 取得案件號碼 **/
	public String getCaseNo() {
		return this.caseNo;
	}
	/** 設定案件號碼 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得交易日期 **/
	public Date getCaseDate() {
		return this.caseDate;
	}
	/** 設定交易日期 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/** 取得檢視時點 **/
	public String getCaseType() {
		return this.caseType;
	}
	/** 設定檢視時點 **/
	public void setCaseType(String value) {
		this.caseType = value;
	}

	/** 取得態樣檢核1 **/
	public String getCheckItem1() {
		return this.checkItem1;
	}
	/** 設定態樣檢核1 **/
	public void setCheckItem1(String value) {
		this.checkItem1 = value;
	}

	/** 取得態樣檢核2 **/
	public String getCheckItem2() {
		return this.checkItem2;
	}
	/** 設定態樣檢核2 **/
	public void setCheckItem2(String value) {
		this.checkItem2 = value;
	}

	/** 取得態樣檢核3 **/
	public String getCheckItem3() {
		return this.checkItem3;
	}
	/** 設定態樣檢核3 **/
	public void setCheckItem3(String value) {
		this.checkItem3 = value;
	}

	/** 取得態樣檢核4 **/
	public String getCheckItem4() {
		return this.checkItem4;
	}
	/** 設定態樣檢核4 **/
	public void setCheckItem4(String value) {
		this.checkItem4 = value;
	}

	/** 取得態樣檢核5 **/
	public String getCheckItem5() {
		return this.checkItem5;
	}
	/** 設定態樣檢核5 **/
	public void setCheckItem5(String value) {
		this.checkItem5 = value;
	}

	/** 取得金額說明 **/
	public String getCaseAmt() {
		return this.caseAmt;
	}
	/** 設定金額說明 **/
	public void setCaseAmt(String value) {
		this.caseAmt = value;
	}

	/** 取得交易目的說明 **/
	public String getCasePurpose() {
		return this.casePurpose;
	}
	/** 設定交易目的說明 **/
	public void setCasePurpose(String value) {
		this.casePurpose = value;
	}

	/** 取得資金來源說明 **/
	public String getCaseSource() {
		return this.caseSource;
	}
	/** 設定資金來源說明 **/
	public void setCaseSource(String value) {
		this.caseSource = value;
	}

	/** 取得行業別說明 **/
	public String getEcoNm() {
		return this.ecoNm;
	}
	/** 設定行業別說明 **/
	public void setEcoNm(String value) {
		this.ecoNm = value;
	}

	/** 取得其他說明 **/
	public String getCaseOther() {
		return this.caseOther;
	}
	/** 設定其他說明 **/
	public void setCaseOther(String value) {
		this.caseOther = value;
	}

	/** 取得是否承作 **/
	public String getIsApprove() {
		return this.isApprove;
	}
	/** 設定是否承作 **/
	public void setIsApprove(String value) {
		this.isApprove = value;
	}

	/** 取得承作理由敘述 **/
	public String getIsApproveReason() {
		return isApproveReason;
	}
	/** 設定承作理由敘述 **/
	public void setIsApproveReason(String isApproveReason) {
		this.isApproveReason = isApproveReason;
	}
	
	/** 取得是否申報 **/
	public String getIsAml() {
		return this.isAml;
	}
	/** 設定是否申報 **/
	public void setIsAml(String value) {
		this.isAml = value;
	}

	/** 取得申報理由敘述 **/
	public String getIsAmlReason() {
		return isAmlReason;
	}
	/** 設定申報理由敘述 **/
	public void setIsAmlReason(String isAmlReason) {
		this.isAmlReason = isAmlReason;
	}

	/** 取得版本日期 **/
	public String getVersionDate() {
		return versionDate;
	}
	/** 設定版本日期 **/
	public void setVersionDate(String versionDate) {
		this.versionDate = versionDate;
	}
	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
