package com.mega.eloan.lms.fms.pages;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.model.C900S02D;

import tw.com.jcs.auth.AuthType;


@Controller
@RequestMapping(path = "/fms/cls3001m01/{page}")
public class CLS3001M01Page extends AbstractEloanForm {

	@Resource
	CLSService service;
	
	public CLS3001M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {	
		new DocLogPanel("_docLog").processPanelData(model, params);
		
		addAclLabel(model, new AclLabel("_btnSave", params, getDomainClass(),
				AuthType.Modify	, FlowDocStatusEnum.編製中));
		
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, false, FlowDocStatusEnum.待覆核));
		
		renderJsI18N(CLS3001M01Page.class);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {		
		return C900S02D.class;
	}
}
