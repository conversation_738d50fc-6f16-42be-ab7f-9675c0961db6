/* 
 * L120S01O.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 信用風險管理關係戶帳務明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01O", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "rCustId", "rDupNo", "relType" }))
public class L120S01O extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String custId;

	/** 重複序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 關係戶統一編號 **/
	@Size(max = 10)
	@Column(name = "RCUSTID", length = 10, columnDefinition = "CHAR(10)")
	private String rCustId;

	/** 關係戶重複序號 **/
	@Size(max = 1)
	@Column(name = "RDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String rDupNo;

	/** 關係戶名稱（戶名） **/
	@Size(max = 120)
	@Column(name = "RCUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String rCustName;

	/**
	 * 關係戶類別
	 * <p/>
	 * 010:同一人<br/>
	 * 020:同一關係人-自然人<br/>
	 * 080:同一關係人-自然人與法人<br/>
	 * 030:同一關係企業<br/>
	 * 040:同一人(信用風險集中)<br/>
	 * 050:集團企業(信用風險集中)
	 */
	@Size(max = 3)
	@Column(name = "RELTYPE", length = 3, columnDefinition = "CHAR(3)")
	private String relType;

	/**
	 * 授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (totAmtA+totAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totAmt;

	/**
	 * 授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totAmtA;

	/**
	 * 授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totAmtB;

	/**
	 * 無擔保授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (crdAmtA+crdAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdAmt;

	/**
	 * 無擔保授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdAmtA;

	/**
	 * 無擔保授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdAmtB;

	/**
	 * 授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lntAmtA+lntAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lntAmt;

	/**
	 * 授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNTAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lntAmtA;

	/**
	 * 授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNTAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lntAmtB;

	/**
	 * 有擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lnsAmtA+lnsAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNSAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lnsAmt;

	/**
	 * 有擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNSAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lnsAmtA;

	/**
	 * 有擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNSAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lnsAmtB;

	/**
	 * 無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lncAmtA+lncAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNCAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lncAmt;

	/**
	 * 無擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNCAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lncAmtA;

	/**
	 * 無擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNCAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lncAmtB;

	/**
	 * 不計入同一關係企業授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLntAmtA + costLntAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLntAmt;

	/**
	 * 不計入同一關係企業授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNTAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLntAmtA;

	/**
	 * 不計入同一關係企業授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNTAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLntAmtB;

	/**
	 * 不計入同一關係企業有擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLnsAmtA + costLnsAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNSAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLnsAmt;

	/**
	 * 不計入同一關係企業有擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNSAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLnsAmtA;

	/**
	 * 不計入同一關係企業有擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNSAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLnsAmtB;

	/**
	 * 不計入同一關係企業無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLncAmtA + costLncAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNCAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLncAmt;

	/**
	 * 不計入同一關係企業無擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNCAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLncAmtA;

	/**
	 * 不計入同一關係企業無擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNCAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLncAmtB;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 授信總額度(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTAMTC", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totAmtC;

	/**
	 * 無擔保授信總額度(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDAMTC", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdAmtC;

	/**
	 * 授信總餘額(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNTAMTC", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lntAmtC;

	/**
	 * 有擔保授信總餘額(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNSAMTC", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lnsAmtC;

	/**
	 * 無擔保授信總餘額(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LNCAMTC", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lncAmtC;

	/**
	 * 不計入同一關係企業授信總餘額(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNTAMTC", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLntAmtC;

	/**
	 * 不計入同一關係企業有擔保授信總餘額(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNSAMTC", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLnsAmtC;

	/**
	 * 不計入同一關係企業無擔保授信總餘額(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COSTLNCAMTC", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal costLncAmtC;

	/**
	 * 授信異動額度(當地)
	 * <p/>
	 * 單位：本位幣
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LOCALCURRENTADJVAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal localCurrentAdjVal;

	/**
	 * 計入同一關係企業授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (totAmtA+totAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTALLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totAllAmt;

	/**
	 * 計入同一關係企業授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTALLAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totAllAmtA;

	/**
	 * 計入同一關係企業授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTALLAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totAllAmtB;

	/**
	 * 計入同一關係企業無擔保授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (crdAmtA+crdAmtB)
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDALLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdAllAmt;

	/**
	 * 計入同一關係企業無擔保授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDALLAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdAllAmtA;

	/**
	 * 計入同一關係企業無擔保授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDALLAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdAllAmtB;

	/**
	 * 計入同一關係企業在途授信總額度 <p/>
	 * 單位：TWD元<br/>
	 * (totElAllAmtA+totElAllAmtB)<br/>
	 * 包含不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTELALLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totElAllAmt;

	/**
	 * 計入同一關係企業在途授信總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTELALLAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totElAllAmtA;

	/**
	 * 計入同一關係企業在途授信總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTELALLAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totElAllAmtB;

	/**
	 * 計入同一關係企業在途授信無擔保總額度 <p/>
	 * 單位：TWD元<br/>
	 * (crdElAllAmtA+crdElAllAmtB)<br/>
	 * 包含不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDELALLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdElAllAmt;

	/**
	 * 計入同一關係企業在途授信無擔保總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDELALLAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdElAllAmtA;

	/**
	 * 計入同一關係企業在途授信無擔保總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDELALLAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdElAllAmtB;

	/**
	 * 在途授信總額度 <p/>
	 * 單位：TWD元<br/>
	 * (totElAmtA+totElAmtB)<br/>
	 * 不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTELAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totElAmt;

	/**
	 * 在途授信總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTELAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totElAmtA;

	/**
	 * 在途授信總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTELAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totElAmtB;

	/**
	 * 在途授信無擔保總額度 <p/>
	 * 單位：TWD元<br/>
	 * (crdElAmtA+crdElAmtB)<br/>
	 * 不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDELAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdElAmt;

	/**
	 * 在途授信無擔保總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDELAMTA", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdElAmtA;

	/**
	 * 在途授信無擔保總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CRDELAMTB", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal crdElAmtB;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重複序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重複序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得關係戶統一編號 **/
	public String getRCustId() {
		return this.rCustId;
	}

	/** 設定關係戶統一編號 **/
	public void setRCustId(String value) {
		this.rCustId = value;
	}

	/** 取得關係戶重複序號 **/
	public String getRDupNo() {
		return this.rDupNo;
	}

	/** 設定關係戶重複序號 **/
	public void setRDupNo(String value) {
		this.rDupNo = value;
	}

	/** 取得關係戶名稱（戶名） **/
	public String getRCustName() {
		return this.rCustName;
	}

	/** 設定關係戶名稱（戶名） **/
	public void setRCustName(String value) {
		this.rCustName = value;
	}

	/**
	 * 取得關係戶類別
	 * <p/>
	 * 010:同一人<br/>
	 * 020:同一關係人-自然人<br/>
	 * 080:同一關係人-自然人與法人<br/>
	 * 030:同一關係企業<br/>
	 * 040:同一人(信用風險集中)<br/>
	 * 050:集團企業(信用風險集中)
	 */
	public String getRelType() {
		return this.relType;
	}

	/**
	 * 設定關係戶類別
	 * <p/>
	 * 010:同一人<br/>
	 * 020:同一關係人-自然人<br/>
	 * 080:同一關係人-自然人與法人<br/>
	 * 030:同一關係企業<br/>
	 * 040:同一人(信用風險集中)<br/>
	 * 050:集團企業(信用風險集中)
	 **/
	public void setRelType(String value) {
		this.relType = value;
	}

	/**
	 * 取得授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (totAmtA+totAmtB)
	 */
	public BigDecimal getTotAmt() {
		return this.totAmt;
	}

	/**
	 * 設定授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (totAmtA+totAmtB)
	 **/
	public void setTotAmt(BigDecimal value) {
		this.totAmt = value;
	}

	/**
	 * 取得授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 */
	public BigDecimal getTotAmtA() {
		return this.totAmtA;
	}

	/**
	 * 設定授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 **/
	public void setTotAmtA(BigDecimal value) {
		this.totAmtA = value;
	}

	/**
	 * 取得授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 */
	public BigDecimal getTotAmtB() {
		return this.totAmtB;
	}

	/**
	 * 設定授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 **/
	public void setTotAmtB(BigDecimal value) {
		this.totAmtB = value;
	}

	/**
	 * 取得無擔保授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (crdAmtA+crdAmtB)
	 */
	public BigDecimal getCrdAmt() {
		return this.crdAmt;
	}

	/**
	 * 設定無擔保授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (crdAmtA+crdAmtB)
	 **/
	public void setCrdAmt(BigDecimal value) {
		this.crdAmt = value;
	}

	/**
	 * 取得無擔保授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 */
	public BigDecimal getCrdAmtA() {
		return this.crdAmtA;
	}

	/**
	 * 設定無擔保授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 **/
	public void setCrdAmtA(BigDecimal value) {
		this.crdAmtA = value;
	}

	/**
	 * 取得無擔保授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 */
	public BigDecimal getCrdAmtB() {
		return this.crdAmtB;
	}

	/**
	 * 設定無擔保授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 **/
	public void setCrdAmtB(BigDecimal value) {
		this.crdAmtB = value;
	}

	/**
	 * 取得授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lntAmtA+lntAmtB)
	 */
	public BigDecimal getLntAmt() {
		return this.lntAmt;
	}

	/**
	 * 設定授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lntAmtA+lntAmtB)
	 **/
	public void setLntAmt(BigDecimal value) {
		this.lntAmt = value;
	}

	/**
	 * 取得授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getLntAmtA() {
		return this.lntAmtA;
	}

	/**
	 * 設定授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLntAmtA(BigDecimal value) {
		this.lntAmtA = value;
	}

	/**
	 * 取得授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getLntAmtB() {
		return this.lntAmtB;
	}

	/**
	 * 設定授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLntAmtB(BigDecimal value) {
		this.lntAmtB = value;
	}

	/**
	 * 取得有擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lnsAmtA+lnsAmtB)
	 */
	public BigDecimal getLnsAmt() {
		return this.lnsAmt;
	}

	/**
	 * 設定有擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lnsAmtA+lnsAmtB)
	 **/
	public void setLnsAmt(BigDecimal value) {
		this.lnsAmt = value;
	}

	/**
	 * 取得有擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getLnsAmtA() {
		return this.lnsAmtA;
	}

	/**
	 * 設定有擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLnsAmtA(BigDecimal value) {
		this.lnsAmtA = value;
	}

	/**
	 * 取得有擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getLnsAmtB() {
		return this.lnsAmtB;
	}

	/**
	 * 設定有擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLnsAmtB(BigDecimal value) {
		this.lnsAmtB = value;
	}

	/**
	 * 取得無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lncAmtA+lncAmtB)
	 */
	public BigDecimal getLncAmt() {
		return this.lncAmt;
	}

	/**
	 * 設定無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (lncAmtA+lncAmtB)
	 **/
	public void setLncAmt(BigDecimal value) {
		this.lncAmt = value;
	}

	/**
	 * 取得無擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getLncAmtA() {
		return this.lncAmtA;
	}

	/**
	 * 設定無擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLncAmtA(BigDecimal value) {
		this.lncAmtA = value;
	}

	/**
	 * 取得無擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getLncAmtB() {
		return this.lncAmtB;
	}

	/**
	 * 設定無擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setLncAmtB(BigDecimal value) {
		this.lncAmtB = value;
	}

	/**
	 * 取得不計入同一關係企業授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLntAmtA + costLntAmtB)
	 */
	public BigDecimal getCostLntAmt() {
		return this.costLntAmt;
	}

	/**
	 * 設定不計入同一關係企業授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLntAmtA + costLntAmtB)
	 **/
	public void setCostLntAmt(BigDecimal value) {
		this.costLntAmt = value;
	}

	/**
	 * 取得不計入同一關係企業授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getCostLntAmtA() {
		return this.costLntAmtA;
	}

	/**
	 * 設定不計入同一關係企業授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setCostLntAmtA(BigDecimal value) {
		this.costLntAmtA = value;
	}

	/**
	 * 取得不計入同一關係企業授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getCostLntAmtB() {
		return this.costLntAmtB;
	}

	/**
	 * 設定不計入同一關係企業授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setCostLntAmtB(BigDecimal value) {
		this.costLntAmtB = value;
	}

	/**
	 * 取得不計入同一關係企業有擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLnsAmtA + costLnsAmtB)
	 */
	public BigDecimal getCostLnsAmt() {
		return this.costLnsAmt;
	}

	/**
	 * 設定不計入同一關係企業有擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLnsAmtA + costLnsAmtB)
	 **/
	public void setCostLnsAmt(BigDecimal value) {
		this.costLnsAmt = value;
	}

	/**
	 * 取得不計入同一關係企業有擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getCostLnsAmtA() {
		return this.costLnsAmtA;
	}

	/**
	 * 設定不計入同一關係企業有擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setCostLnsAmtA(BigDecimal value) {
		this.costLnsAmtA = value;
	}

	/**
	 * 取得不計入同一關係企業有擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getCostLnsAmtB() {
		return this.costLnsAmtB;
	}

	/**
	 * 設定不計入同一關係企業有擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setCostLnsAmtB(BigDecimal value) {
		this.costLnsAmtB = value;
	}

	/**
	 * 取得不計入同一關係企業無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLncAmtA + costLncAmtB)
	 */
	public BigDecimal getCostLncAmt() {
		return this.costLncAmt;
	}

	/**
	 * 設定不計入同一關係企業無擔保授信總餘額
	 * <p/>
	 * 單位：TWD仟元<br/>
	 * (costLncAmtA + costLncAmtB)
	 **/
	public void setCostLncAmt(BigDecimal value) {
		this.costLncAmt = value;
	}

	/**
	 * 取得不計入同一關係企業無擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getCostLncAmtA() {
		return this.costLncAmtA;
	}

	/**
	 * 設定不計入同一關係企業無擔保授信總餘額(國內)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setCostLncAmtA(BigDecimal value) {
		this.costLncAmtA = value;
	}

	/**
	 * 取得不計入同一關係企業無擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getCostLncAmtB() {
		return this.costLncAmtB;
	}

	/**
	 * 設定不計入同一關係企業無擔保授信總餘額(海外)
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setCostLncAmtB(BigDecimal value) {
		this.costLncAmtB = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定授信總額度(當地) **/
	public void setTotAmtC(BigDecimal totAmtC) {
		this.totAmtC = totAmtC;
	}

	/** 取得授信總額度(當地) **/
	public BigDecimal getTotAmtC() {
		return totAmtC;
	}

	/** 無擔保授信總額度(當地) **/
	public void setCrdAmtC(BigDecimal crdAmtC) {
		this.crdAmtC = crdAmtC;
	}

	/** 取得無擔保授信總額度(當地) **/
	public BigDecimal getCrdAmtC() {
		return crdAmtC;
	}

	/** 授信總餘額(當地) **/
	public void setLntAmtC(BigDecimal lntAmtC) {
		this.lntAmtC = lntAmtC;
	}

	/** 取得授信總餘額(當地) **/
	public BigDecimal getLntAmtC() {
		return lntAmtC;
	}

	/** 設定有擔保授信總餘額(當地) **/
	public void setLnsAmtC(BigDecimal lnsAmtC) {
		this.lnsAmtC = lnsAmtC;
	}

	/** 取得有擔保授信總餘額(當地) **/
	public BigDecimal getLnsAmtC() {
		return lnsAmtC;
	}

	/** 設定無擔保授信總餘額(當地) **/
	public void setLncAmtC(BigDecimal lncAmtC) {
		this.lncAmtC = lncAmtC;
	}

	/** 取得無擔保授信總餘額(當地) **/
	public BigDecimal getLncAmtC() {
		return lncAmtC;
	}

	/** 設定不計入同一關係企業授信總餘額(當地) **/
	public void setCostLntAmtC(BigDecimal costLntAmtC) {
		this.costLntAmtC = costLntAmtC;
	}

	/** 取得不計入同一關係企業授信總餘額(當地) **/
	public BigDecimal getCostLntAmtC() {
		return costLntAmtC;
	}

	/** 設定不計入同一關係企業有擔保授信總餘額(當地) **/
	public void setCostLnsAmtC(BigDecimal costLnsAmtC) {
		this.costLnsAmtC = costLnsAmtC;
	}

	/** 取得不計入同一關係企業有擔保授信總餘額(當地) **/
	public BigDecimal getCostLnsAmtC() {
		return costLnsAmtC;
	}

	/** 設定不計入同一關係企業無擔保授信總餘額(當地) **/
	public void setCostLncAmtC(BigDecimal costLncAmtC) {
		this.costLncAmtC = costLncAmtC;
	}

	/** 取得不計入同一關係企業無擔保授信總餘額(當地) **/
	public BigDecimal getCostLncAmtC() {
		return costLncAmtC;
	}

	/** 設定授信異動額度(當地) **/
	public void setLocalCurrentAdjVal(BigDecimal localCurrentAdjVal) {
		this.localCurrentAdjVal = localCurrentAdjVal;
	}

	/** 取得授信異動額度(當地) **/
	public BigDecimal getLocalCurrentAdjVal() {
		return localCurrentAdjVal;
	}

	/**
	 * 取得計入同一關係企業授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (totAmtA+totAmtB)
	 */
	public BigDecimal getTotAllAmt() {
		return this.totAllAmt;
	}

	/**
	 * 設定計入同一關係企業授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (totAmtA+totAmtB)
	 **/
	public void setTotAllAmt(BigDecimal value) {
		this.totAllAmt = value;
	}

	/**
	 * 取得計入同一關係企業授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 */
	public BigDecimal getTotAllAmtA() {
		return this.totAllAmtA;
	}

	/**
	 * 設定計入同一關係企業授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 **/
	public void setTotAllAmtA(BigDecimal value) {
		this.totAllAmtA = value;
	}

	/**
	 * 取得計入同一關係企業授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 */
	public BigDecimal getTotAllAmtB() {
		return this.totAllAmtB;
	}

	/**
	 * 設定計入同一關係企業授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 **/
	public void setTotAllAmtB(BigDecimal value) {
		this.totAllAmtB = value;
	}

	/**
	 * 取得計入同一關係企業無擔保授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (crdAmtA+crdAmtB)
	 */
	public BigDecimal getCrdAllAmt() {
		return this.crdAllAmt;
	}

	/**
	 * 設定計入同一關係企業無擔保授信總額度
	 * <p/>
	 * 單位：TWD元<br/>
	 * (crdAmtA+crdAmtB)
	 **/
	public void setCrdAllAmt(BigDecimal value) {
		this.crdAllAmt = value;
	}

	/**
	 * 取得計入同一關係企業無擔保授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 */
	public BigDecimal getCrdAllAmtA() {
		return this.crdAllAmtA;
	}

	/**
	 * 設定計入同一關係企業無擔保授信總額度(國內)
	 * <p/>
	 * 單位：TWD元
	 **/
	public void setCrdAllAmtA(BigDecimal value) {
		this.crdAllAmtA = value;
	}

	/**
	 * 取得計入同一關係企業無擔保授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 */
	public BigDecimal getCrdAllAmtB() {
		return this.crdAllAmtB;
	}

	/**
	 * 設定計入同一關係企業無擔保授信總額度(海外)
	 * <p/>
	 * 單位：TWD元
	 **/
	public void setCrdAllAmtB(BigDecimal value) {
		this.crdAllAmtB = value;
	}

	/**
	 * 取得計入同一關係企業在途授信總額度 <p/>
	 * 單位：TWD元<br/>
	 * (totElAllAmtA+totElAllAmtB)<br/>
	 * 包含不計入同一關係企業
	 */
	public BigDecimal getTotElAllAmt() {
		return this.totElAllAmt;
	}

	/**
	 * 設定計入同一關係企業在途授信總額度 <p/>
	 * 單位：TWD元<br/>
	 * (totElAllAmtA+totElAllAmtB)<br/>
	 * 包含不計入同一關係企業
	 */
	public void setTotElAllAmt(BigDecimal value) {
		this.totElAllAmt = value;
	}

	/**
	 * 取得計入同一關係企業在途授信總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	public BigDecimal getTotElAllAmtA() {
		return this.totElAllAmtA;
	}

	/**
	 * 設定計入同一關係企業在途授信總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	public void setTotElAllAmtA(BigDecimal value) {
		this.totElAllAmtA = value;
	}

	/**
	 * 取得計入同一關係企業在途授信總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	public BigDecimal getTotElAllAmtB() {
		return this.totElAllAmtB;
	}

	/**
	 * 設定計入同一關係企業在途授信總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	public void setTotElAllAmtB(BigDecimal value) {
		this.totElAllAmtB = value;
	}

	/**
	 * 取得計入同一關係企業在途授信無擔保總額度 <p/>
	 * 單位：TWD元<br/>
	 * (crdElAllAmtA+crdElAllAmtB)<br/>
	 * 包含不計入同一關係企業
	 */
	public BigDecimal getCrdElAllAmt() {
		return this.crdElAllAmt;
	}

	/**
	 * 設定計入同一關係企業在途授信無擔保總額度 <p/>
	 * 單位：TWD元<br/>
	 * (crdElAllAmtA+crdElAllAmtB)<br/>
	 * 包含不計入同一關係企業
	 */
	public void setCrdElAllAmt(BigDecimal value) {
		this.crdElAllAmt = value;
	}

	/**
	 * 取得計入同一關係企業在途授信無擔保總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	public BigDecimal getCrdElAllAmtA() {
		return this.crdElAllAmtA;
	}

	/**
	 * 設定計入同一關係企業在途授信無擔保總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	public void setCrdElAllAmtA(BigDecimal value) {
		this.crdElAllAmtA = value;
	}

	/**
	 * 取得計入同一關係企業在途授信無擔保總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	public BigDecimal getCrdElAllAmtB() {
		return this.crdElAllAmtB;
	}

	/**
	 * 設定計入同一關係企業在途授信無擔保總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 包含不計入同一關係企業
	 */
	public void setCrdElAllAmtB(BigDecimal value) {
		this.crdElAllAmtB = value;
	}

	/**
	 * 取得在途授信總額度 <p/>
	 * 單位：TWD元<br/>
	 * (totElAmtA+totElAmtB)<br/>
	 * 不計入同一關係企業
	 */
	public BigDecimal getTotElAmt() {
		return this.totElAmt;
	}

	/**
	 * 設定在途授信總額度 <p/>
	 * 單位：TWD元<br/>
	 * (totElAmtA+totElAmtB)<br/>
	 * 不計入同一關係企業
	 */
	public void setTotElAmt(BigDecimal value) {
		this.totElAmt = value;
	}

	/**
	 * 取得在途授信總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	public BigDecimal getTotElAmtA() {
		return this.totElAmtA;
	}

	/**
	 * 設定在途授信總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	public void setTotElAmtA(BigDecimal value) {
		this.totElAmtA = value;
	}

	/**
	 * 取得在途授信總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	public BigDecimal getTotElAmtB() {
		return this.totElAmtB;
	}

	/**
	 * 設定在途授信總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	public void setTotElAmtB(BigDecimal value) {
		this.totElAmtB = value;
	}

	/**
	 * 取得在途授信無擔保總額度 <p/>
	 * 單位：TWD元<br/>
	 * (crdElAmtA+crdElAmtB)<br/>
	 * 不計入同一關係企業
	 */
	public BigDecimal getCrdElAmt() {
		return this.crdElAmt;
	}

	/**
	 * 設定在途授信無擔保總額度 <p/>
	 * 單位：TWD元<br/>
	 * (crdElAmtA+crdElAmtB)<br/>
	 * 不計入同一關係企業
	 */
	public void setCrdElAmt(BigDecimal value) {
		this.crdElAmt = value;
	}

	/**
	 * 取得在途授信無擔保總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	public BigDecimal getCrdElAmtA() {
		return this.crdElAmtA;
	}

	/**
	 * 設定在途授信無擔保總額度(國內) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	public void setCrdElAmtA(BigDecimal value) {
		this.crdElAmtA = value;
	}

	/**
	 * 取得在途授信無擔保總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	public BigDecimal getCrdElAmtB() {
		return this.crdElAmtB;
	}

	/**
	 * 設定在途授信無擔保總額度(海外) <p/>
	 * 單位：TWD元<br/>
	 * 不計入同一關係企業
	 */
	public void setCrdElAmtB(BigDecimal value) {
		this.crdElAmtB = value;
	}

}
