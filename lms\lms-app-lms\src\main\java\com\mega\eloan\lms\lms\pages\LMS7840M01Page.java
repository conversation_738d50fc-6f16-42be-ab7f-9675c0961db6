/* 
 * LMS7840M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.model.L782M01A;

/**
 * <pre>
 * 簽報紀錄查詢
 * </pre>
 * 
 * @since 2011/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/12,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms7840m01")
public class LMS7840M01Page extends AbstractEloanForm {

	@Autowired
	LMSService lmsService;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(LMS7840M01Page.class);

		Map<String, String> msgs = null;
		msgs = lmsService.getCodeType("Common_Currcy");
		renderJsI18NWithMsgName("Common_Currcy", msgs);

		msgs = lmsService.getCodeType("lms1405m01_SubItem");
		renderJsI18NWithMsgName("lms1405m01_SubItem", msgs);

		msgs = lmsService.getCodeType("lmsUseCms_collTyp1");
		renderJsI18NWithMsgName("lmsUseCms_collTyp1", msgs);

		// J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件
		msgs = lmsService.getCodeType("cms1010_bldUse");
		renderJsI18NWithMsgName("cms1010_bldUse", msgs);

	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L782M01A.class;

	}

}// ~
