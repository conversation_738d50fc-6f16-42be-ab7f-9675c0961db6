/* 
 * LMS9990DOC02ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.ctr.constants.CtrConstants;
import com.mega.eloan.lms.ctr.pages.LMS9990M07Page;
import com.mega.eloan.lms.ctr.service.LMS9990Service;
import com.mega.eloan.lms.ctr.service.LMS9990Service2;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C999M01A;
import com.mega.eloan.lms.model.C999M01B;
import com.mega.eloan.lms.model.C999M01C;
import com.mega.eloan.lms.model.C999M01D;
import com.mega.eloan.lms.model.C999S01A;
import com.mega.eloan.lms.model.C999S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產Word Service(個金約據書)
 * </pre>
 * 
 * @since 2012/8/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/8/22,Miller,new
 *          </ul>
 */
@Service("lms9990doc02service")
public class LMS9990DOC02ServiceImpl extends AbstractFormHandler implements
		FileDownloadService {
	@Resource
	LMS9990Service lms9990Service;

	@Resource
	LMS9990Service2 lms9990Service2;

	@Resource
	LMS1405Service lms1405Service;
	@Resource
	LMS1205Service lms1205Service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9990DOC02ServiceImpl.class);
	// SHIFT+ENTER效果
	@SuppressWarnings("unused")
	private final static String strTab = "<w:br/>";
	private static final String CODE_UTF_8 = "UTF-8";
	private final static String strNull = "null";

	// ENTER效果
	// Word XML 換行語法
	// private final static String strEnter =
	// "</w:t></w:r></w:p><w:p><w:r><w:t>";
	// // Word XML 表格換行語法
	// private final String tblEnter =
	// "<w:p wsp:rsidR='00810A66' wsp:rsidRDefault='00810A66'>"
	// + "<w:pPr><w:rPr><w:rFonts w:fareast='標楷體'/>"
	// + "<w:b/><w:b-cs/><w:spacing w:val='10'/>"
	// + "<w:sz w:val='26'/></w:rPr></w:pPr></w:p>";

	/**
	 * 取得整份Word文件
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.creatDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			if (baos == null) {
				baos = new ByteArrayOutputStream();
			}
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}

		}
		return null;
	}

	/**
	 * 建立文件
	 * 
	 * @param params
	 * @param parent
	 * @return
	 */
	public OutputStream creatDoc(PageParameters params) {
		OutputStream outputStream = null;
		String contractType = Util.trim(params.getString("contractType"));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String chkColIdVal = Util.trim(params.getString("chkColIdVal"));
		C999M01A meta = lms9990Service2.findC999m01aByMainId(mainId);
		if (meta == null) {
			meta = new C999M01A();
		}
		String contractKind = Util.isNotEmpty(Util.trim(params
				.getString("contractKind"))) ? Util.trim(params
				.getString("contractKind")) : Util.trim(meta.getContractKind());
		/**
		 * 個金約據書類型+種類
		 */
		try {
			if (CtrConstants.Book9990.contractType.一般.equals(contractType)) {
				// 一般
				if (CtrConstants.Book9990.contractKind.契約書含個別商議
						.equals(contractKind)) {
					outputStream = this.getL999M01AW01(params);
				} else if (CtrConstants.Book9990.contractKind.切結書
						.equals(contractKind)) {
					if (CtrConstants.Book9990.LMS999XMLFile.kind2.無辦理其他政府政策性住宅補貼切結書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW02(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.同意處份設質股票切結書存證信函
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW03(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.同意處份設質股票切結書_附同意書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW04(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.打房條款切結書_聯徵有1者
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW05(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.打房條款切結書_聯徵無1者
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW06(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.優惠貸款未重複借款切結書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW07(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.現職員工及配偶額度未逾1500萬元同意書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW08(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.租賃權益拋棄承諾書_法務處修訂意見_租賃權益拋棄承諾書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW09(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.轉貸款約定書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW10(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.屬自住型房貸_自住切結書_惟不適用央行受限戶
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW11(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.屬自住型房貸_換屋切結書_惟不適用央行受限戶
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW12(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.兆豐連帶保證書_橫
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW13(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind2.質權設定契約書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW14(params);
					}
				} else if (CtrConstants.Book9990.contractKind.同意書
						.equals(contractKind)) {
					if (CtrConstants.Book9990.LMS999XMLFile.kind3.處份同意書_股票借款
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW15(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind3.同意書_不動產抵押所有人用
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW16(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind3.連保人同意書_連保人續約同意書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW17(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind3.擔保借款抵押權設定種類約款
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW18(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind3.整批房貸同意書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW19(params);
					}
				} else if (CtrConstants.Book9990.contractKind.宣告書
						.equals(contractKind)) {
					if (CtrConstants.Book9990.LMS999XMLFile.kind4.歡喜理財家或綜合理財房屋貸款
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW20(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind4.理財型貸款或消費性貸款
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW21(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind4.銀行法12條之1
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW22(params);
					}
				} else if (CtrConstants.Book9990.contractKind.增補條款含個別商議
						.equals(contractKind)) {
					if (CtrConstants.Book9990.LMS999XMLFile.kind6.增補條款_空版
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW23(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind6.增補條款_內文選項
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW24(params);
					}
				} else if (CtrConstants.Book9990.contractKind.其他
						.equals(contractKind)) {
					if (CtrConstants.Book9990.LMS999XMLFile.kind7.本票
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW25(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind7.本票授權書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW26(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind7.個人借款支用書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW27(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind7.消費金融專用借款申請書及房屋貸款特別提醒事項_A4格式_借款申請書_房屋貸款特別提醒事項
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW28(params);
					} else if (CtrConstants.Book9990.LMS999XMLFile.kind7.房屋貸款撥款委託書
							.equals("LMS9990" + chkColIdVal + "C.xml")) {
						outputStream = this.getL999M01AW29(params);
					}
				}
			} else {
				// 政策性留學貸款
				if (CtrConstants.Book9990.LMS999XMLFile.kind8.教育部補助留學生借款契約書
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW30(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.兆銀總授管字第5054號_附件9_政策留貸_切結書
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW31(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.兆銀總授管字第5054號_附件11_政策留貸延期清償申請書暨切結書
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW32(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.兆銀總授管字第5054號_附件13_政策留貸_修畢碩士學位後繼續修讀博士延長貸款期限及寬限期申請書
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW33(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.兆銀總授管字第5054號_附件14_政策留貸_授權書
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW34(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.個金2686_附件4通知書_留學生本人為申請人用
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW35(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.個金2686_附件7通知書_留學生本人為申請人用_英文
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW36(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.教育部補助留學生就學貸款申請書每人信用查詢費300元
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW37(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.符合教育部採認規定之國外大學院校及其他事項確認查詢單
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW38(params);
				} else if (CtrConstants.Book9990.LMS999XMLFile.kind8.無法於寬限期內完成學業延期清償申請書
						.equals("LMS9990" + chkColIdVal + "C.xml")) {
					outputStream = this.getL999M01AW39(params);
				}
			}
		} catch (Exception e) {

		}
		return outputStream;
	}

	/**
	 * 取得所有個金契約書相關欄位
	 * 
	 * @return
	 */
	private String[] getAllJsonCol() {
		return new String[] { "jsonDataA", "jsonDataH", "jsonDataB", "19Ac1",
				"19Ac2", "19Ac3", "19Ac4", "19Ac5", "19Ac6", "19Ac7",
				"jsonDataI", "jsonDataGa", "jsonDataGb", "jsonDataGc",
				"jsonDataGd", "jsonDataGe", "jsonDataGf", "jsonDataGg",
				"jsonDataGh", "20Ara", "20Aca", "jsonDataC", "jsonDataC1",
				"jsonDataC2", "jsonDataC3", "jsonDataC4", "jsonDataC5",
				"jsonDataC6", "jsonDataC7", "jsonDataC8", "jsonDataC9",
				"jsonDataC10", "jsonDataC11", "jsonDataC12", "jsonDataC13",
				"jsonDataC14", "jsonDataC15", "jsonDataC16", "jsonDataC17",
				"jsonDataC18", "jsonDataC19", "jsonDataC20", "jsonDataC21",
				"jsonDataC22", "jsonDataC23", "jsonDataC24", "jsonDataC25",
				"jsonDataC26", "jsonDataC27", "jsonDataC28", "jsonDataC29",
				"jsonDataC30", "jsonDataC31", "jsonDataC32", "jsonDataJa",
				"jsonDataJb", "jsonDataJc", "jsonDataJd", "jsonDataJe",
				"jsonDataJf", "jsonDataJg", "jsonDataJh", "jsonDataJi",
				"21Ara", "21Aca", "jsonDataD", "jsonDataD1", "jsonDataD2",
				"jsonDataD3", "jsonDataD4", "jsonDataJp", "jsonDataJq",
				"22Ara", "22Aca", "jsonDataE", "jsonDataE1", "jsonDataE2",
				"jsonDataE3", "jsonDataE4", "jsonDataJr", "jsonDataJs",
				"23Ara", "23Aca", "jsonDataF01", "jsonDataF01a",
				"jsonDataF01b", "jsonDataF01c", "jsonDataF01d", "jsonDataF01e",
				"jsonDataF01f", "jsonDataF01g", "jsonDataF01h", "jsonDataF01i",
				"jsonDataF01j", "jsonDataF01k", "jsonDataF01l", "jsonDataF01m",
				"jsonDataF01n", "jsonDataF01o", "jsonDataF01p", "jsonDataF01q",
				"jsonDataF01r", "jsonDataF01s", "jsonDataF03a", "jsonDataF03b",
				"jsonDataF03c", "jsonDataF03d", "jsonDataF03e", "jsonDataF03f",
				"jsonDataF03g", "jsonDataF03h", "jsonDataF03i", "jsonDataF03j",
				"23Acc1", "jsonDataF03k", "jsonDataF03l", "jsonDataF03m",
				"jsonDataF03n", "jsonDataF03o", "jsonDataF03p", "jsonDataF03q",
				"jsonDataF03r", "23Acc2", "jsonDataF03s", "jsonDataF03t",
				"jsonDataF03u", "jsonDataF03v", "jsonDataF03w", "jsonDataF03x",
				"jsonDataF03y", "jsonDataF03z", "23Acc3", "jsonDataF03A",
				"jsonDataF03B", "jsonDataF03C", "jsonDataF03D", "23Arb",
				"23Acb", "jsonDataF02", "jsonDataF02a", "jsonDataF02b",
				"jsonDataF02c", "jsonDataF02d", "jsonDataF02e", "jsonDataL",
				"rS31Com", "cS31Com1", "cS31Com2", "cS31Com3", "cS31Com4",
				"cS31Com5", "cS31Com6", "cS31Com7", "cS31Com8", "rS31Com",
				"cS31Com1", "cS31Com2", "cS31Com3", "cS31Com4", "cS31Com5",
				"cS31Com6", "cS31Com7", "cS31Com8", "jsonDataN1", "jsonDataN2",
				"jsonDataN3", "jsonDataN4", "jsonDataN1", "jsonDataN2",
				"jsonDataN3", "jsonDataN4", "jsonDataO", "jsonDataO" };
	}

	/**
	 * 取得個金兆豐一般授信契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW01(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		String[] allJsonCol = getAllJsonCol();
		String content = null;
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind1.一般借款契約書含搭配內政部青年安家方案版本);

			// 初始化所有契約書相關Json欄位
			for (String jsonCol : allJsonCol) {
				map.put(jsonCol, UtilConstants.Mark.FULLSPACE);
			}

			// 設定標頭
			map.putAll(this.setTitle(mainId));

			// 契約金額
			map.put("jsonDataA",
					getCNumAllStr(
							this.getRealContent(mainId,
									CtrConstants.Book9990.Type.契約金額, false))
							.replace("。", UtilConstants.Mark.SPACE));

			// 借款用途
			map.put("jsonDataB", this.getRealContent(mainId,
					CtrConstants.Book9990.Type.借款用途, false));
			String titleB = this.getRealContent(mainId,
					CtrConstants.Book9990.Type.借款用途, true);
			map.put("kJsonDataB",
					strNull.equals(titleB.toLowerCase()) ? UtilConstants.Mark.FULLSPACE
							: titleB);

			// 動用方式
			map.putAll(this.setSingle(mainId, CtrConstants.Book9990.Type.動用方式));
			map.put("jsonDataC", this.getRealContent(mainId,
					CtrConstants.Book9990.Type.動用方式, false));
			String titleC = this.getRealContent(mainId,
					CtrConstants.Book9990.Type.動用方式, true);
			map.put("kJsonDataC",
					strNull.equals(titleC.toLowerCase()) ? UtilConstants.Mark.FULLSPACE
							: titleC);

			// 撥款方式
			map.putAll(this.setSingle(mainId, CtrConstants.Book9990.Type.撥款方式));
			map.put("jsonDataD",
					getRealContent(mainId, CtrConstants.Book9990.Type.撥款方式,
							false));
			String titleD = this.getRealContent(mainId,
					CtrConstants.Book9990.Type.撥款方式, true);
			map.put("kJsonDataD",
					strNull.equals(titleD.toLowerCase()) ? UtilConstants.Mark.FULLSPACE
							: titleD);

			// 償還辦法
			map.putAll(this.setSingle(mainId, CtrConstants.Book9990.Type.償還辦法));
			map.put("jsonDataE",
					getRealContent(mainId, CtrConstants.Book9990.Type.償還辦法,
							false));
			String titleE = this.getRealContent(mainId,
					CtrConstants.Book9990.Type.償還辦法, true);
			map.put("kJsonDataE",
					strNull.equals(titleE.toLowerCase()) ? UtilConstants.Mark.FULLSPACE
							: titleE);

			// 利息計付
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.利息計付_限制清償期間));
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.利息計付_得隨時清償));
			map.put("jsonDataF01",
					getRealContent(mainId,
							CtrConstants.Book9990.Type.利息計付_限制清償期間, false));
			String titleF01 = this.getRealContent(mainId,
					CtrConstants.Book9990.Type.利息計付_限制清償期間, true);
			map.put("kJsonDataF01",
					strNull.equals(titleF01.toLowerCase()) ? UtilConstants.Mark.FULLSPACE
							: titleF01);
			map.put("jsonDataF02",
					getRealContent(mainId,
							CtrConstants.Book9990.Type.利息計付_得隨時清償, false));
			String titleF02 = this.getRealContent(mainId,
					CtrConstants.Book9990.Type.利息計付_得隨時清償, true);
			map.put("kJsonDataF02",
					strNull.equals(titleF02.toLowerCase()) ? UtilConstants.Mark.FULLSPACE
							: titleF02);

			// 違約金及遲延利息
			map.putAll(this.setPage13And14(mainId));

			// 資料提供
			map.putAll(this.setPage151And152(mainId));

			// 服務
			map.putAll(this.setPage161And162(mainId));

			// 管轄法院
			map.putAll(this.setPage171And172(mainId));

			// 其他項目(特別條款與個別商議條款)
			map.putAll(this.setPage8(mainId));

			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());

			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 依照完整字串取得包含中文數字之完整字串
	 * 
	 * @param allStr
	 *            完整字串
	 * @return 包含中文數字之完整字串
	 */
	private String getCNumAllStr(String allStr) {
		int size = allStr.length();
		if (size > 0) {
			String str = allStr.replace(",", UtilConstants.Mark.SPACE);
			size = str.length();
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			StringBuilder numSb = new StringBuilder();
			for (int i = 0; i < size; i++) {
				if (Util.isNumeric(str.charAt(i))) {
					numSb.append(Util.trim(str.charAt(i)));
				} else {
					if (numSb.length() > 0) {
						sb.append(NumConverter.toChineseNumberFull(numSb
								.toString()));
						numSb.setLength(0);
					}
					sb.append(Util.trim(str.charAt(i)));
				}
			}
			if (sb.length() == 0) {
				if (numSb.length() > 0) {
					sb.append(NumConverter.toChineseNumberFull(numSb.toString()));
					numSb.setLength(0);
				}
			}
			return sb.toString();
		} else {
			return Util.trim(allStr);
		}
	}

	/**
	 * 違約金及遲延利息設定
	 * 
	 * @param mainId
	 * @return
	 */
	private Map<String, String> setPage13And14(String mainId) {
		Map<String, String> map = new HashMap<String, String>();
		C999M01A meta = lms9990Service2.findC999m01aByMainId(mainId);
		if (meta != null) {
			if (CtrConstants.Book9990.contractType.一般.equals(meta
					.getContractType())) {
				map.put("jsonDataKa", Util.trim(meta.getAMonth()));
				map.put("jsonDataKb", Util.trim(meta.getARate()));
				map.put("jsonDataKc", Util.trim(meta.getBMonth()));
				map.put("jsonDataKd", Util.trim(meta.getBRate()));
				map.put("jsonDataKe", Util.trim(meta.getCRate()));
			} else if (CtrConstants.Book9990.contractType.政策性留學貸款.equals(meta
					.getContractType())) {
				map.put("jsonDataL", Util.trim(meta.getCRate()));
			} else {
				// 查無資料則全部設為全形空白
				map.put("jsonDataKa", UtilConstants.Mark.FULLSPACE);
				map.put("jsonDataKb", UtilConstants.Mark.FULLSPACE);
				map.put("jsonDataKc", UtilConstants.Mark.FULLSPACE);
				map.put("jsonDataKd", UtilConstants.Mark.FULLSPACE);
				map.put("jsonDataKe", UtilConstants.Mark.FULLSPACE);
			}
		}
		return map;
	}

	/**
	 * 資料提供設定
	 * 
	 * @param mainId
	 * @return
	 */
	private Map<String, String> setPage151And152(String mainId) {
		Map<String, String> map = new HashMap<String, String>();
		C999M01A meta = lms9990Service2.findC999m01aByMainId(mainId);
		if (meta != null) {
			String dataUseFlag = Util.trim(meta.getDataUseFlag());
			List<String> checkList = setCheckBox((meta.getDataUseItem() == null) ? 999
					: meta.getDataUseItem());
			// 設定資料保密_是否同意
			if (UtilConstants.DEFAULT.是.equals(dataUseFlag)) {
				// 同意
				// 已勾
				// 4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				map.put("+UAAAAUAAYAQwBoAGUAYwBrADEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			} else if (UtilConstants.DEFAULT.否.equals(dataUseFlag)) {
				// 不同意
				// 已勾
				// 4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				map.put("+UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			// 設定資料保密_同意項目
			for (String checkId : checkList) {
				map.put(checkId,
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
		}
		return map;
	}

	/**
	 * 依照"資料保密_同意項目"數值勾選相對應的checkBox(用List儲存要打勾的checkBox Id)
	 * 
	 * @param dataUseItem
	 *            資料保密_同意項目數值
	 * @return IResult
	 */
	private List<String> setCheckBox(int dataUseItem) {
		Map<String, String> map = new HashMap<String, String>();
		List<String> needCheckList = new ArrayList<String>();
		map.put("1",
				"+UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("2",
				"+UAAAAUAAYAQwBoAGUAYwBrADQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("4",
				"+UAAAAUAAYAQwBoAGUAYwBrADUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("8",
				"+UAAAAUAAYAQwBoAGUAYwBrADYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("16",
				"+UAAAAUAAYAQwBoAGUAYwBrADcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("32",
				"+UAAAAUAAYAQwBoAGUAYwBrADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("64",
				"+UAAAAUAAYAQwBoAGUAYwBrADkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		// map.put("0", "cS31Com8");
		// 等於999代表數值為空
		if (dataUseItem != 999) {
			if (dataUseItem == 0) {
				// 全部勾選
				needCheckList
						.add("+UAAAAUAAcAQwBoAGUAYwBrADEAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=");
			} else {
				// 將整數轉為二進位List後由小到大排序(java)
				List<String> bList = Util.getBinaryList(dataUseItem);
				Collections.sort(bList);
				for (String str : bList) {
					needCheckList.add(map.get(str));
				}
			}
		}
		return needCheckList;
	}

	/**
	 * 服務設定
	 * 
	 * @param mainId
	 * @return
	 */
	private Map<String, String> setPage161And162(String mainId) {
		Map<String, String> map = new HashMap<String, String>();
		List<C999M01B> listC999m01b = lms9990Service2
				.findC999m01bByMainId(mainId);
		if (!listC999m01b.isEmpty()) {
			C999M01B c999m01b = null;
			for (C999M01B model : listC999m01b) {
				if (CtrConstants.Book9990.KindType.兆豐國際商業銀行股份有限公司_乙方
						.equals(Util.trim(model.getType()))) {
					c999m01b = model;
				}
			}
			if (c999m01b != null) {
				map.put("jsonDataN1", Util.trim(c999m01b.getTel()));
				map.put("jsonDataN2", Util.trim(c999m01b.getFax()));
				map.put("jsonDataN3", Util.trim(c999m01b.getEMail()));
				map.put("jsonDataN4", Util.trim(c999m01b.getOther()));
			} else {
				map.put("jsonDataN1", UtilConstants.Mark.FULLSPACE);
				map.put("jsonDataN2", UtilConstants.Mark.FULLSPACE);
				map.put("jsonDataN3", UtilConstants.Mark.FULLSPACE);
				map.put("jsonDataN4", UtilConstants.Mark.FULLSPACE);
			}
		}
		return map;
	}

	/**
	 * 管轄法院設定
	 * 
	 * @param mainId
	 * @return
	 */
	private Map<String, String> setPage171And172(String mainId) {
		Map<String, String> map = new HashMap<String, String>();
		C999M01A meta = lms9990Service2.findC999m01aByMainId(mainId);
		if (meta != null) {
			map.put("jsonDataO",
					codeTypeService.getDescOfCodeType("taiwancourt",
							Util.trim(meta.getCourtCode())));
		} else {
			map.put("jsonDataO", UtilConstants.Mark.FULLSPACE);
		}
		return map;
	}

	/**
	 * 其他項目設定
	 * 
	 * @param mainId
	 * @return
	 */
	private Map<String, String> setPage8(String mainId) {
		Map<String, String> map = new HashMap<String, String>();
		C999M01D c999m01d1 = lms9990Service2.findC999m01dByUniqueKey(mainId,
				CtrConstants.Book9990.itemType.特別條款);
		C999M01D c999m01d2 = lms9990Service2.findC999m01dByUniqueKey(mainId,
				CtrConstants.Book9990.itemType.個別商議條款);
		if (c999m01d1 != null) {
			map.put("itemContentA", Util.trim(c999m01d1.getItemContent()));
		} else {
			map.put("itemContentA", UtilConstants.Mark.FULLSPACE);
		}
		if (c999m01d2 != null) {
			map.put("itemContentC", Util.trim(c999m01d2.getItemContent()));
		} else {
			map.put("itemContentC", UtilConstants.Mark.FULLSPACE);
		}
		return map;
	}

	/**
	 * 取得個金兆豐無辦理其他政府政策性住宅補貼切結書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW02(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind2.無辦理其他政府政策性住宅補貼切結書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐同意處份設質股票切結書存證信函Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW03(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind2.同意處份設質股票切結書存證信函);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐同意處份設質股票切結書_附同意書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW04(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind2.同意處份設質股票切結書_附同意書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐打房條款切結書_聯徵有1者Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW05(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind2.打房條款切結書_聯徵有1者);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐打房條款切結書_聯徵無1者Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW06(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind2.打房條款切結書_聯徵無1者);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐優惠貸款未重複借款切結書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW07(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind2.優惠貸款未重複借款切結書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐現職員工及配偶額度未逾1500萬元同意書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW08(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind2.現職員工及配偶額度未逾1500萬元同意書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);

				map.put("custName2", Util.trim(l140m01a.getCustName()));
				map.put("custId2", custId);
				map.put("addr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);

				map.put("custName2", UtilConstants.Mark.FULLSPACE);
				map.put("custId2", UtilConstants.Mark.FULLSPACE);
				map.put("addr2", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐租賃權益拋棄承諾書_法務處修訂意見_租賃權益拋棄承諾書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW09(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind2.租賃權益拋棄承諾書_法務處修訂意見_租賃權益拋棄承諾書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐轉貸款約定書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW10(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind2.轉貸款約定書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金屬自住型房貸_自住切結書_惟不適用央行受限戶Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW11(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind2.屬自住型房貸_自住切結書_惟不適用央行受限戶);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金屬自住型房貸_換屋切結書_惟不適用央行受限戶Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW12(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind2.屬自住型房貸_換屋切結書_惟不適用央行受限戶);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐連帶保證書_橫Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW13(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind2.兆豐連帶保證書_橫);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("word", UtilConstants.Mark.FULLSPACE);
				map.put("number", UtilConstants.Mark.FULLSPACE);
				map.put("bank", UtilConstants.Mark.FULLSPACE);
				map.put("mainBorrow", UtilConstants.Mark.FULLSPACE);
				map.put("mainAmt", UtilConstants.Mark.FULLSPACE);
				// checkBox部份
				// 同意 未勾
				// 2UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				// 不同意 未勾
				// 2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				// 已勾
				// 4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				map.put("lawPlace", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowName", Util.trim(l140m01a.getCustName()));
				map.put("lianBowName1", Util.trim(l140m01a.getCustName()));
				map.put("lianBowAddr1",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("lianBorCustId1", custId);
				map.put("lianBowName2", Util.trim(l140m01a.getCustName()));
				map.put("lianBowAddr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("lianBorCustId2", custId);
				map.put("lianBowName3", Util.trim(l140m01a.getCustName()));
				map.put("lianBowAddr3",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("lianBorCustId3", custId);
				map.put("lianBowName4", Util.trim(l140m01a.getCustName()));
				map.put("lianBowAddr4",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("lianBorCustId4", custId);
			} else {
				map.put("word", UtilConstants.Mark.FULLSPACE);
				map.put("number", UtilConstants.Mark.FULLSPACE);
				map.put("bank", UtilConstants.Mark.FULLSPACE);
				map.put("mainBorrow", UtilConstants.Mark.FULLSPACE);
				map.put("mainAmt", UtilConstants.Mark.FULLSPACE);
				// checkBox部份
				// 同意 未勾
				// 2UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				// 不同意 未勾
				// 2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				// 已勾
				// 4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				map.put("lawPlace", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowName", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowName1", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowAddr1", UtilConstants.Mark.FULLSPACE);
				map.put("lianBorCustId1", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowName2", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowAddr2", UtilConstants.Mark.FULLSPACE);
				map.put("lianBorCustId2", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowName3", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowAddr3", UtilConstants.Mark.FULLSPACE);
				map.put("lianBorCustId3", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowName4", UtilConstants.Mark.FULLSPACE);
				map.put("lianBowAddr4", UtilConstants.Mark.FULLSPACE);
				map.put("lianBorCustId4", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			map.putAll(getCurrentDate("1"));
			map.putAll(getCurrentDate("2"));
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金質權設定契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW14(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind2.質權設定契約書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custId2", custId);
				map.put("addr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
				map.put("custId2", UtilConstants.Mark.FULLSPACE);
				map.put("addr2", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金處份同意書_股票借款Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW15(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind3.處份同意書_股票借款);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金同意書_不動產抵押所有人用Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW16(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind3.同意書_不動產抵押所有人用);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金連保人同意書_連保人續約同意書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW17(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind3.連保人同意書_連保人續約同意書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("custAddr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("custAddr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金擔保借款抵押權設定種類約款Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW18(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind3.擔保借款抵押權設定種類約款);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金整批房貸同意書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW19(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind3.整批房貸同意書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金歡喜理財家或綜合理財房屋貸款Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW20(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind4.歡喜理財家或綜合理財房屋貸款);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得理財型貸款或消費性貸款Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW21(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind4.理財型貸款或消費性貸款);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得銀行法12條之1Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW22(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind4.銀行法12條之1);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金增補條款_空版Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW23(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind6.增補條款_空版);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));

				map.put("custName1", Util.trim(l140m01a.getCustName()));
				map.put("custId1", custId);
				map.put("addr1",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);

				map.put("custName2", Util.trim(l140m01a.getCustName()));
				map.put("custId2", custId);
				map.put("addr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);

				map.put("custName3", Util.trim(l140m01a.getCustName()));
				map.put("custId3", custId);
				map.put("addr3",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custName1", UtilConstants.Mark.FULLSPACE);
				map.put("custId1", UtilConstants.Mark.FULLSPACE);
				map.put("addr1", UtilConstants.Mark.FULLSPACE);
				map.put("custName2", UtilConstants.Mark.FULLSPACE);
				map.put("custId2", UtilConstants.Mark.FULLSPACE);
				map.put("addr2", UtilConstants.Mark.FULLSPACE);
				map.put("custName3", UtilConstants.Mark.FULLSPACE);
				map.put("custId3", UtilConstants.Mark.FULLSPACE);
				map.put("addr3", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得增補條款_內文選項Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW24(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind6.增補條款_內文選項);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得本票Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW25(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind7.本票);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得本票授權書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW26(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind7.本票授權書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個人借款支用書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW27(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind7.個人借款支用書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得消費金融專用借款申請書及房屋貸款特別提醒事項_A4格式_借款申請書_房屋貸款特別提醒事項Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW28(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind7.消費金融專用借款申請書及房屋貸款特別提醒事項_A4格式_借款申請書_房屋貸款特別提醒事項);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得房屋貸款撥款委託書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW29(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind7.房屋貸款撥款委託書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));

				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);

				map.put("custId2", custId);
				map.put("addr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
				map.put("custTel", UtilConstants.Mark.FULLSPACE);
				map.put("custId2", UtilConstants.Mark.FULLSPACE);
				map.put("addr2", UtilConstants.Mark.FULLSPACE);
				map.put("custTel2", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金兆豐政策留貸授信契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW30(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		String[] allJsonCol = getAllJsonCol();
		String content = null;
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.Book9990.LMS999XMLFile.kind8.教育部補助留學生借款契約書);

			// 初始化所有契約書相關Json欄位
			for (String jsonCol : allJsonCol) {
				map.put(jsonCol, UtilConstants.Mark.FULLSPACE);
			}

			// 設定標頭
			map.putAll(this.setTitle(mainId));

			// 契約金額
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.契約金額_政策留貸));

			map.put("jsonDataH",
					getCNumAllStr(map.get("jsonDataH")).replace("。",
							UtilConstants.Mark.SPACE));

			// 借款用途
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.借款用途_政策留貸));

			// 申請方式及借款期限、寬限期
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.申請方式及借款期限_寬限期));

			// 動用方式
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.動用方式_政策留貸));

			// 撥款方式
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.撥款方式_政策留貸));

			// 償還辦法
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.償還辦法_政策留貸));

			// 利息計付
			map.putAll(this.setSingle(mainId,
					CtrConstants.Book9990.Type.利息計付_政策留貸));

			// 違約金及遲延利息
			map.putAll(this.setPage13And14(mainId));

			// 資料提供
			map.putAll(this.setPage151And152(mainId));

			// 服務
			map.putAll(this.setPage161And162(mainId));

			// 管轄法院
			map.putAll(this.setPage171And172(mainId));

			// 其他項目(特別條款與個別商議條款)
			map.putAll(this.setPage8(mainId));

			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());

			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得兆銀總授管字第5054號_附件9_政策留貸_切結書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW31(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.兆銀總授管字第5054號_附件9_政策留貸_切結書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custName2", Util.trim(l140m01a.getCustName()));
				map.put("custId2", custId);
				map.put("addr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
				map.put("custTel", UtilConstants.Mark.FULLSPACE);

				map.put("custName2", UtilConstants.Mark.FULLSPACE);
				map.put("custId2", UtilConstants.Mark.FULLSPACE);
				map.put("addr2", UtilConstants.Mark.FULLSPACE);
				map.put("custTel2", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得兆銀總授管字第5054號_附件11_政策留貸延期清償申請書暨切結書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW32(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.兆銀總授管字第5054號_附件11_政策留貸延期清償申請書暨切結書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custId2", custId);
				map.put("addr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail2",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custId3", custId);
				map.put("addr3",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel3",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail3",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custId4", custId);
				map.put("addr4",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel4",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail4",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
				map.put("custTel", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail", UtilConstants.Mark.FULLSPACE);

				map.put("custId2", UtilConstants.Mark.FULLSPACE);
				map.put("addr2", UtilConstants.Mark.FULLSPACE);
				map.put("custTel2", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail2", UtilConstants.Mark.FULLSPACE);

				map.put("custId3", UtilConstants.Mark.FULLSPACE);
				map.put("addr3", UtilConstants.Mark.FULLSPACE);
				map.put("custTel3", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail3", UtilConstants.Mark.FULLSPACE);

				map.put("custId4", UtilConstants.Mark.FULLSPACE);
				map.put("addr4", UtilConstants.Mark.FULLSPACE);
				map.put("custTel4", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail4", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得兆銀總授管字第5054號_附件13_政策留貸_修畢碩士學位後繼續修讀博士延長貸款期限及寬限期申請書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW33(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.兆銀總授管字第5054號_附件13_政策留貸_修畢碩士學位後繼續修讀博士延長貸款期限及寬限期申請書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custId2", custId);
				map.put("addr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail2",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);

			} else {
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
				map.put("custTel", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail", UtilConstants.Mark.FULLSPACE);

				map.put("custId2", UtilConstants.Mark.FULLSPACE);
				map.put("addr2", UtilConstants.Mark.FULLSPACE);
				map.put("custTel2", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail2", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得兆銀總授管字第5054號_附件14_政策留貸_授權書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW34(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.兆銀總授管字第5054號_附件14_政策留貸_授權書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金2686_附件4通知書_留學生本人為申請人用Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW35(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.個金2686_附件4通知書_留學生本人為申請人用);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得個金2686_附件7通知書_留學生本人為申請人用_英文Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW36(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.個金2686_附件7通知書_留學生本人為申請人用_英文);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得教育部補助留學生就學貸款申請書每人信用查詢費300元Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW37(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.教育部補助留學生就學貸款申請書每人信用查詢費300元);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得符合教育部採認規定之國外大學院校及其他事項確認查詢單Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW38(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.符合教育部採認規定之國外大學院校及其他事項確認查詢單);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custName", Util.trim(l140m01a.getCustName()));
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
			} else {
				map.put("custName", UtilConstants.Mark.FULLSPACE);
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得無法於寬限期內完成學業延期清償申請書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW39(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String[] oidFor140 = Util.trim(params.getString("oidFor140")).split(
				"\\|");
		String oid = oidFor140[0];
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class, oid);
		try {
			// 讀檔
			content = Util
					.getFileContent(Util.trim(PropUtil
							.getProperty("loadFile.dir"))
							+ "word/ctr/"
							+ CtrConstants.Book9990.LMS999XMLFile.kind8.無法於寬限期內完成學業延期清償申請書);
			if (l140m01a != null) {
				String custId = Util.trim(l140m01a.getCustId());
				String dupNo = Util.trim(l140m01a.getDupNo());
				C120S01A c120s01a = lms1205Service.findC120S01AByUniqueKey(
						mainId, custId, dupNo);
				map.put("custId", custId);
				map.put("addr",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custId2", custId);
				map.put("addr2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel2",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail2",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custId3", custId);
				map.put("addr3",
						(c120s01a != null) ? Util.trim(c120s01a.getCoAddr())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custTel3",
						(c120s01a != null) ? Util.trim(c120s01a.getCoTel())
								: UtilConstants.Mark.FULLSPACE);
				map.put("custEmail3",
						(c120s01a != null) ? Util.trim(c120s01a.getEmail())
								: UtilConstants.Mark.FULLSPACE);

			} else {
				map.put("custId", UtilConstants.Mark.FULLSPACE);
				map.put("addr", UtilConstants.Mark.FULLSPACE);
				map.put("custTel", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail", UtilConstants.Mark.FULLSPACE);

				map.put("custId2", UtilConstants.Mark.FULLSPACE);
				map.put("addr2", UtilConstants.Mark.FULLSPACE);
				map.put("custTel2", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail2", UtilConstants.Mark.FULLSPACE);

				map.put("custId3", UtilConstants.Mark.FULLSPACE);
				map.put("addr3", UtilConstants.Mark.FULLSPACE);
				map.put("custTel3", UtilConstants.Mark.FULLSPACE);
				map.put("custEmail3", UtilConstants.Mark.FULLSPACE);
			}
			// 取得現在日期(年、月、日)
			map.putAll(getCurrentDate());
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得現在日期(年、月、日)
	 * 
	 * @return 現在日期(年、月、日)
	 */
	private Map<String, String> getCurrentDate() {
		Map<String, String> map = new HashMap<String, String>();
		String fullDate = CapDate.convertTimestampToString(
				CapDate.getCurrentTimestamp(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		String year = LMSUtil.checkSubStr(fullDate, 0, 4) ? fullDate.substring(
				0, 4) : UtilConstants.Mark.FULLSPACE;
		String month = LMSUtil.checkSubStr(fullDate, 5, 7) ? fullDate
				.substring(5, 7) : UtilConstants.Mark.FULLSPACE;
		String day = LMSUtil.checkSubStr(fullDate, 8) ? fullDate.substring(8)
				: UtilConstants.Mark.FULLSPACE;
		map.put("year", CapDate.convertDateToTaiwanYear(year));
		map.put("month", month);
		map.put("day", day);
		return map;
	}

	/**
	 * 取得現在日期(年、月、日)
	 * 
	 * @param arg
	 *            後綴字
	 * @return 現在日期(年、月、日)
	 */
	private Map<String, String> getCurrentDate(String arg) {
		Map<String, String> map = new HashMap<String, String>();
		String fullDate = CapDate.convertTimestampToString(
				CapDate.getCurrentTimestamp(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		String year = LMSUtil.checkSubStr(fullDate, 0, 4) ? fullDate.substring(
				0, 4) : UtilConstants.Mark.FULLSPACE;
		String month = LMSUtil.checkSubStr(fullDate, 5, 7) ? fullDate
				.substring(5, 7) : UtilConstants.Mark.FULLSPACE;
		String day = LMSUtil.checkSubStr(fullDate, 8) ? fullDate.substring(8)
				: UtilConstants.Mark.FULLSPACE;
		map.put("year" + arg, year);
		map.put("month" + arg, month);
		map.put("day" + arg, day);
		return map;
	}

	/**
	 * 設定標題至前端
	 * 
	 * @param mainId
	 *            mainId
	 * @return IResult
	 */
	private Map<String, String> setTitle(String mainId) {
		Map<String, String> map = new HashMap<String, String>();
		C999M01A meta = lms9990Service2.findC999m01aByMainId(mainId);
		List<C999M01B> listC999m01b = lms9990Service2
				.findC999m01bByMainId(mainId);
		List<C999M01C> listC999m01c = lms9990Service2
				.findC999m01cByMainId(mainId);
		// 進行初始化
		map.put("custData", UtilConstants.Mark.FULLSPACE);
		map.put("contractNo", UtilConstants.Mark.FULLSPACE);
		map.put("custData1", UtilConstants.Mark.FULLSPACE);
		map.put("custData2", UtilConstants.Mark.FULLSPACE);
		map.put("custData3", UtilConstants.Mark.FULLSPACE);

		// 設定客戶名稱與編號
		if (meta != null) {
			map.put("custData", Util.trim(meta.getCustName()));
			map.put("contractNo", Util.trim(meta.getContractNo()));
		}
		// 設定連保人與保證人(連保人->G 保證人->N)
		if (!listC999m01c.isEmpty()) {
			// C.共同借款人
			// G.連帶保證人(個金)
			// N.ㄧ般保證人(個金)
			// S.擔保品提供人(個金)
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			StringBuilder sb2 = new StringBuilder();
			sb2.setLength(0);
			// String custData2 = null;
			// String custData3 = null;
			for (C999M01C c999m01c : listC999m01c) {
				String custPos = Util.trim(c999m01c.getCustPos());
				if (CtrConstants.Book9990.CustPos.共同借款人或共同發票人.equals(custPos)) {
					sb2.append(
							(sb2.length() > 0) ? "，" : UtilConstants.Mark.SPACE)
							.append(Util.trim(c999m01c.getCustName()));
				} else if (CtrConstants.Book9990.CustPos.連帶保證人或擔保品提供人兼連帶保證人
						.equals(custPos)
						|| CtrConstants.Book9990.CustPos.一般保證人或擔保品提供人兼一般保證人
								.equals(custPos)
						|| CtrConstants.Book9990.CustPos.擔保品提供人.equals(custPos)) {
					sb.append(
							(sb.length() > 0) ? "，" : UtilConstants.Mark.SPACE)
							.append(Util.trim(c999m01c.getCustName()));
				}
			}
			map.put("custData2", sb2.toString());
			map.put("custData3", sb.toString());
		}
		// 設定立約人
		if (!listC999m01b.isEmpty()) {
			Properties pop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M07Page.class);
			StringBuilder sb = new StringBuilder();
			for (C999M01B c999m01b : listC999m01b) {
				if (CtrConstants.Book9990.KindType.借款人_甲方.equals(Util
						.trim(c999m01b.getType()))) {
					// 甲方 C999M01AM07.type1
					sb.append(pop.getProperty("C999M01AM07.type1"))
							.append(Util.trim(c999m01b.getCustName()))
							.append("，")
							.append(Util.trim(map.get("custData2")))
							.append("，");
				} else {
					// 乙方 C999M01AM07.type2
					sb.append(pop.getProperty("C999M01AM07.type2"))
							.append(Util.trim(c999m01b.getCustName()))
							.append("，");
				}
			}
			map.put("custData1",
					Util.trim(sb.deleteCharAt(sb.length() - 1).append("。")
							.toString()));
		}
		return map;
	}

	/**
	 * 取得合併後的內容(其他)
	 * 
	 * @param mainId
	 * @param type
	 * @return
	 * @throws CapMessageException
	 */
	private String getRealContent(String mainId, String type,
			boolean getSubTitle) throws CapMessageException {
		return Util
				.trim(lms9990Service2.comBineC999s01b(mainId, type,
						getSubTitle, true)).replace("<BR>", "")
				.replace("<BR/>", "").replace("<br>", "").replace("<br/>", "")
				.replace("null、", "");
	}

	/**
	 * 單一產品種類時依類型將JsonData依欄位名稱傳到前端
	 * 
	 * @param mainId
	 *            mainId
	 * @param type
	 *            類型
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private Map<String, String> setSingle(String mainId, String type)
			throws CapException {
		Map<String, String> map = new HashMap<String, String>();
		List<C999S01A> list = lms9990Service2.findC999s01aByMainId(mainId);
		JSONObject json = new JSONObject();
		if (!list.isEmpty()) {
			if (CtrConstants.Book9990.Type.利息計付_限制清償期間.equals(type)) {
				// 項次為刪除的產品種類不設定到前端
				if (!CtrConstants.Book9990.ItemNo.刪除.equals(Util.trim(list.get(
						0).getItemNo()))) {
					// 單一產品種類
					List<C999S01B> listJsonData = list.get(0).getC999s01bs();
					if (!listJsonData.isEmpty()) {
						for (C999S01B c999s01b : listJsonData) {
							if (type.equals(Util.trim(c999s01b.getType()))) {
								json = JSONObject.fromObject(Util.trim(c999s01b
										.getJsonData()));
								if (list.size() != 1) {
									json.putAll(getSingle(json, true));
								} else {
									json.putAll(getSingle(json, false));
								}
							}
						}
					}
				}
			} else if (list.size() == 1) {
				// 項次為刪除的產品種類不設定到前端
				if (!CtrConstants.Book9990.ItemNo.刪除.equals(Util.trim(list.get(
						0).getItemNo()))) {
					// 單一產品種類
					List<C999S01B> listJsonData = list.get(0).getC999s01bs();
					if (!listJsonData.isEmpty()) {
						for (C999S01B c999s01b : listJsonData) {
							if (type.equals(Util.trim(c999s01b.getType()))) {
								json = JSONObject.fromObject(Util.trim(c999s01b
										.getJsonData()));
							}
						}
					}
				}
			} else {
				// 多產品種類時將欄位清空
				for (C999S01A c999s01a : list) {
					List<C999S01B> listJsonData = c999s01a.getC999s01bs();
					if (!listJsonData.isEmpty()) {
						for (C999S01B c999s01b : listJsonData) {
							if (type.equals(Util.trim(c999s01b.getType()))) {
								json = JSONObject.fromObject(Util.trim(c999s01b
										.getJsonData()));
								for (Object key : json.keySet()) {
									if (json.containsKey(key)) {
										json.put(String.valueOf(key), UtilConstants.Mark.SPACE);
									}
								}
							}
						}
					}
				}
			}
		}

		// 將JsonObject 轉換成 HashMap
		map = (Map<String, String>) JSONObject.toBean(json, HashMap.class);
		return map;
	}

	/**
	 * 取得單一產品JsonData內容
	 * 
	 * @param type
	 *            類型
	 * @param list
	 *            產品種類List
	 * @param isSpec
	 *            是否為利息計付_限制清償期間（青少年專案）
	 * @return
	 * @throws CapException
	 */
	private JSONObject getSingle(JSONObject json, boolean isSpec)
			throws CapException {
		if (isSpec) {
			JSONObject specJson = new JSONObject();
			// 青少年專案相關JsonData欄位
			String specKeys[] = new String[] { "jsonDataF03a", "jsonDataF03b",
					"jsonDataF03c", "jsonDataF03d", "jsonDataF03e",
					"jsonDataF03f", "jsonDataF03g", "jsonDataF03h",
					"jsonDataF03i", "jsonDataF03j", "23Acc1", "jsonDataF03k",
					"jsonDataF03l", "jsonDataF03m", "jsonDataF03n",
					"jsonDataF03o", "jsonDataF03p", "jsonDataF03q",
					"jsonDataF03r", "23Acc2", "jsonDataF03s", "jsonDataF03t",
					"jsonDataF03u", "jsonDataF03v", "jsonDataF03w",
					"jsonDataF03x", "jsonDataF03y", "jsonDataF03z", "23Acc3",
					"jsonDataF03A", "jsonDataF03B", "jsonDataF03C",
					"jsonDataF03D" };
			for (String specKey : specKeys) {
				if (json.containsKey(specKey)) {
					specJson.put(specKey, Util.trim(json.get(specKey)));
				}
			}
			return specJson;
		} else {
			return json;
		}
	}

	/**
	 * 寫入word資料
	 * 
	 * @param content
	 *            xml 內容
	 * @param map
	 *            對應欄位值
	 * @return ByteArrayOutputStream
	 * @throws CapMessageException
	 */
	private ByteArrayOutputStream writeWordContent(String content,
			Map<String, String> map) throws CapMessageException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		content = Util.replaceWordContent(content, map, "[", "]", "+UAAAA");
		OutputStreamWriter outWriter = null;
		try {
			outWriter = new OutputStreamWriter(baos, CODE_UTF_8);
			outWriter.write(content);
			outWriter.close();
			return baos;
		} catch (UnsupportedEncodingException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException i) {
			logger.error(i.getMessage());
			throw new CapMessageException(getMessage(i.getMessage()),
					getClass());
		}

	}
}
