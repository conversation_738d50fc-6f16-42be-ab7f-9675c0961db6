package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 海外消金評等模型擔保品資料 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C121S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C121S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * (若未來有N個擔保品，要再加欄位 seq)
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 評等文件ID **/
	@Size(max=32)
	@Column(name="RATINGID", length=32, columnDefinition="CHAR(32)")
	private String ratingId;

	/** 
	 * 擔保品種類<p/>
	 * 1：不動產<br/>
	 *  2：本行存款<br/>
	 *  3：經總處核准之債券型基金<br/>
	 *  4：其他經總處核准之擔保品<br/>
	 *  5：非本行認可之擔保品<br/>
	 *  6：無擔保品<br/>
	 *  7：國庫券<br/>
	 *  8：公債<br/>
	 *  9：本行認可之國內銀行或The Banker 排名(第一類資本排名)前五百名且信用卓著之國外銀行開發之保證函或擔保信用狀<br/>
	 *  A：政府指定之信用保證機構之保證<br/>
	 *  p.s.若非不動產則收合不動產座落、區域、屋齡及建物面積。
	 */
	@Size(max=1)
	@Column(name="CMSTYPE", length=1, columnDefinition="CHAR(1)")
	private String cmsType;

	/** 不動產座落 **/
	@Size(max=210)
	@Column(name="LOCATION", length=210, columnDefinition="VARCHAR(210)")
	private String location;

	/** 
	 * 座落地區<p/>
	 * 1：東京都心5區<br/>
	 *  2：東京都心5區以外18區<br/>
	 *  3：東京都23區以外<br/>
	 *  4：大阪府5區<br/>
	 *  5：大阪府5區以外19區<br/>
	 *  6：大阪府24區以外<br/>
	 *  7：其他地區
	 */
	@Size(max=1)
	@Column(name="REGION", length=1, columnDefinition="CHAR(1)")
	private String region;

	/** 
	 * 屋齡<p/>
	 * 1：5年未滿<br/>
	 *  2：5年以上~10年未滿<br/>
	 *  3：10年以上~20年未滿<br/>
	 *  4：20年以上~30年未滿<br/>
	 *  5：30年以上
	 */
	@Size(max=1)
	@Column(name="HOUSEAGE", length=1, columnDefinition="CHAR(1)")
	private String houseAge;

	/** 
	 * 建物面積<p/>
	 * 平方公尺
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="HOUSEAREA", columnDefinition="DEC(15, 2)")
	private BigDecimal houseArea;

	/** 
	 * 因子1<p/>
	 * 擔保品的座落地點及種類
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="FACTOR1", columnDefinition="DEC(1, 0)")
	private Integer factor1;

	/** 
	 * 因子2<p/>
	 * 市場環境及變現性
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="FACTOR2", columnDefinition="DEC(1, 0)")
	private Integer factor2;

	
	/** 
	 * 擔保率
	 */
	@Column(name="SECURITYRATE", columnDefinition="DEC(9, 2)")
	private BigDecimal securityRate;

	/** 擔保品用途{0：不適用(擔保品非屬不動產), 1：自住, 2：非自住, 空白：非日本地區案件}
	 */
	@Size(max=1)
	@Column(name="COLLUSAGE", length=1, columnDefinition="CHAR(1)")
	private String collUsage;
	
	/** 不動產種類{0：不適用(擔保品非屬不動產), 1：土地, 2：建物, 3：土地與建物 , 空白：非日本地區案件}
	 */
	@Size(max=1)
	@Column(name="LOCATIONTYPE", length=1, columnDefinition="CHAR(1)")
	private String locationType;
	
	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * (若未來有N個擔保品，要再加欄位 seq)
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  (若未來有N個擔保品，要再加欄位 seq)
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得評等文件ID **/
	public String getRatingId() {
		return this.ratingId;
	}
	/** 設定評等文件ID **/
	public void setRatingId(String value) {
		this.ratingId = value;
	}

	/** 
	 * 取得擔保品種類<p/>
	 * 1：不動產<br/>
	 *  2：本行存款<br/>
	 *  3：經總處核准之債券型基金<br/>
	 *  4：其他經總處核准之擔保品<br/>
	 *  5：非本行認可之擔保品<br/>
	 *  6：無擔保品<br/>
	 *  7：國庫券<br/>
	 *  8：公債<br/>
	 *  9：本行認可之國內銀行或The Banker 排名(第一類資本排名)前五百名且信用卓著之國外銀行開發之保證函或擔保信用狀<br/>
	 *  A：政府指定之信用保證機構之保證<br/>
	 *  p.s.若非不動產則收合不動產座落、區域、屋齡及建物面積。
	 */
	public String getCmsType() {
		return this.cmsType;
	}
	/**
	 *  設定擔保品種類<p/>
	 *  1：不動產<br/>
	 *  2：本行存款<br/>
	 *  3：經總處核准之債券型基金<br/>
	 *  4：其他經總處核准之擔保品<br/>
	 *  5：非本行認可之擔保品<br/>
	 *  6：無擔保品<br/>
	 *  7：國庫券<br/>
	 *  8：公債<br/>
	 *  9：本行認可之國內銀行或The Banker 排名(第一類資本排名)前五百名且信用卓著之國外銀行開發之保證函或擔保信用狀<br/>
	 *  A：政府指定之信用保證機構之保證<br/>
	 *  p.s.若非不動產則收合不動產座落、區域、屋齡及建物面積。
	 **/
	public void setCmsType(String value) {
		this.cmsType = value;
	}

	/** 取得不動產座落 **/
	public String getLocation() {
		return this.location;
	}
	/** 設定不動產座落 **/
	public void setLocation(String value) {
		this.location = value;
	}

	/** 
	 * 取得座落地區<p/>
	 * 1：東京都心5區<br/>
	 *  2：東京都心5區以外18區<br/>
	 *  3：東京都23區以外<br/>
	 *  4：大阪府5區<br/>
	 *  5：大阪府5區以外19區<br/>
	 *  6：大阪府24區以外<br/>
	 *  7：其他地區
	 */
	public String getRegion() {
		return this.region;
	}
	/**
	 *  設定座落地區<p/>
	 *  1：東京都心5區<br/>
	 *  2：東京都心5區以外18區<br/>
	 *  3：東京都23區以外<br/>
	 *  4：大阪府5區<br/>
	 *  5：大阪府5區以外19區<br/>
	 *  6：大阪府24區以外<br/>
	 *  7：其他地區
	 **/
	public void setRegion(String value) {
		this.region = value;
	}

	/** 
	 * 取得屋齡<p/>
	 * 1：5年未滿<br/>
	 *  2：5年以上~10年未滿<br/>
	 *  3：10年以上~20年未滿<br/>
	 *  4：20年以上~30年未滿<br/>
	 *  5：30年以上
	 */
	public String getHouseAge() {
		return this.houseAge;
	}
	/**
	 *  設定屋齡<p/>
	 *  1：5年未滿<br/>
	 *  2：5年以上~10年未滿<br/>
	 *  3：10年以上~20年未滿<br/>
	 *  4：20年以上~30年未滿<br/>
	 *  5：30年以上
	 **/
	public void setHouseAge(String value) {
		this.houseAge = value;
	}

	/** 
	 * 取得建物面積<p/>
	 * 平方公尺
	 */
	public BigDecimal getHouseArea() {
		return this.houseArea;
	}
	/**
	 *  設定建物面積<p/>
	 *  平方公尺
	 **/
	public void setHouseArea(BigDecimal value) {
		this.houseArea = value;
	}

	/** 
	 * 取得因子1<p/>
	 * 擔保品的座落地點及種類
	 */
	public Integer getFactor1() {
		return this.factor1;
	}
	/**
	 *  設定因子1<p/>
	 *  擔保品的座落地點及種類
	 **/
	public void setFactor1(Integer value) {
		this.factor1 = value;
	}

	/** 
	 * 取得因子2<p/>
	 * 市場環境及變現性
	 */
	public Integer getFactor2() {
		return this.factor2;
	}
	/**
	 *  設定因子2<p/>
	 *  市場環境及變現性
	 **/
	public void setFactor2(Integer value) {
		this.factor2 = value;
	}
	
	/** 
	 * 取得擔保率
	 */
	public BigDecimal getSecurityRate() {
		return securityRate;
	}
	/** 
	 * 設定擔保率
	 */
	public void setSecurityRate(BigDecimal securityRate) {
		this.securityRate = securityRate;
	}
	
	/** 取得擔保品用途{0：不適用(擔保品非屬不動產), 1：自住, 2：非自住, 空白：非日本地區案件}
	 */
	public String getCollUsage() {
		return collUsage;
	}
	/** 設定擔保品用途{0：不適用(擔保品非屬不動產), 1：自住, 2：非自住, 空白：非日本地區案件}
	 */
	public void setCollUsage(String collUsage) {
		this.collUsage = collUsage;
	}	
	
	/** 取得不動產種類{0：不適用(擔保品非屬不動產), 1：土地, 2：建物, 3：土地與建物 , 空白：非日本地區案件}
	 */
	public String getLocationType() {
		return locationType;
	}
	/** 設定不動產種類{0：不適用(擔保品非屬不動產), 1：土地, 2：建物, 3：土地與建物 , 空白：非日本地區案件}
	 */
	public void setLocationType(String locationType) {
		this.locationType = locationType;
	}	
	
	
}
