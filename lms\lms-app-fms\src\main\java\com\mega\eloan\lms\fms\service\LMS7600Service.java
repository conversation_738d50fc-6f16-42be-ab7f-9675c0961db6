package com.mega.eloan.lms.fms.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L140MM4A;
import com.mega.eloan.lms.model.L140MM4B;
import com.mega.eloan.lms.model.L140MM4C;

import tw.com.iisi.cap.exception.CapException;

/**
 * <pre>
 * 空地貸款維護作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS7600Service extends AbstractService {

	/**
	 * 刪除主檔資料 根據所選的oid
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return boolean
	 */
	boolean deleteL140mm4as(String[] oids);
	
	List<L140MM4C> findL140mm4csByMainId(String mainId);
	
	public  Map<String, String> getData(L140MM4A l140mm4a) throws CapException;

	L140MM4C findCurrentL140mm4c(String mainId);

	L140MM4C findLastL140mm4c(String mainId);

	public L140MM4A findL140mm4aByUniqueKey(String mainId);
	
	public  Map<String, String> getElfData(L140MM4A l140mm4a) throws CapException;
	
	public  Map<String, String> getElf600Data(L140MM4A l140mm4a) 
		throws CapException;
	
	public String changeDateToString(Date datetime);
	
	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param resultType
	 *            boolean
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, L140MM4A model,
			boolean setResult, boolean resultType, boolean upMis)
			throws Throwable;

	public void deleteL140mm4bs(List<L140MM4B> l140mm4bs, boolean isAll);
	
	/**
	 * 儲存案件簽章欄檔
	 */
	public void saveL140mm4bList(List<L140MM4B> list);
	
	/**
	 * 查詢案件簽章欄檔
	 * 
	 * @param mainId
	 *            案件編號
	 * @param staffNo
	 *            員編
	 * @param staffjob
	 *            人員職稱
	 */
	public L140MM4B findL140mm4b(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);
}
