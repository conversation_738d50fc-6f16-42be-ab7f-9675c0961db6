/* 
 * C122S01HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C122S01H;

/** 進件狀態明細檔 **/
public interface C122S01HDao extends IGenericDao<C122S01H> {

	C122S01H findByOid(String oid);
	
	List<C122S01H> findByMainId(String mainId);
	
	C122S01H findByUniqueKey(String mainId, String flowId);

	List<C122S01H> findByIndex01(String mainId, String flowId);
	List<C122S01H> findByRefMainId(String refMainId, String flowId);
}