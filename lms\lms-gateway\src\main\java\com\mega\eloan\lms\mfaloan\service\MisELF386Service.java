package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisELF386Service {
	/**
	 * 取得借款人所得空白資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	List<Map<String, Object>> getELF386data(String UnitNo);

	/**
	 * 取得MIS.ELF500資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	List<Map<String, Object>> getELF500data(String custId, String dupId);

	/**
	 * 取得LNF010資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	List<Map<String, Object>> getLNF010data(String custId);

	/**
	 * 新增LNF010資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public void LNF010Insert(String custId, String YPAY, String OMONEY,
			String user);

	/**
	 * 修改LNF010資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public void LNF010Update(String custId, String YPAY, String OMONEY,
			String user);

	/**
	 * 修改LN.LNF010資料(單筆)
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public void LNF010UpdateSingle(String custId, String YPAY, String OMONEY,
			String JobClass, String user);

	/**
	 * 新增LN.LNF010資料(單筆)
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public void LNF010InsertSingle(String custId, String YPAY, String OMONEY,
			String JobClass, String user);

	/**
	 * 更新MIS.ELF500資料
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public void ELF500Update(String custId, String YPAY, String OMONEY);

	/**
	 * 將EXCEL上傳處理結果寫回上傳檔案上
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public void tranSportExcel(Map<String, Object> list, String file, String save);

	/**
	 * 單筆上傳
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public void updateSingle(String custId, String tDup, String yPay,
			String oMoney, String JobClass, String user);

	/**
	 * 整批上傳(EXCEL)
	 * 
	 * @param
	 * @param
	 * @return
	 */
	public void updateExcel(String file, String user,String[] info);
	

}
