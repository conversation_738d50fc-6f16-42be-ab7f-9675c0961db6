/* 
 * L120S24BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S24B;

/** 風控風險權數擔保品明細 **/
public interface L120S24BDao extends IGenericDao<L120S24B> {

	L120S24B findByOid(String oid);
	
	List<L120S24B> findByMainId(String mainId);
	
	List<L120S24B> findByRefOid_s24b(String refOid_s24b);
}