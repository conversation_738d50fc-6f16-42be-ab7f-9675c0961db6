<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">loadScript('pagejs/lms/LMS1035S02Panel');</script>
			<!-- ====================================================================== -->
			<div>
	    		<span class="text-red"><th:block th:text="#{'title.clsRating'}">依據本行「消金授信業務信用評等應用細則」第三條：「對於消金授信之借款人、連帶保證人或共同借款人，應分別辦理信用評等，並就其評等結果擇一適用</th:block>
				</span>
	    	</div>
        	<fieldset>
                <legend>
                    <th:block th:text="#{'tab.02'}">本案關係人基本資料</th:block>
                </legend>
				<table border='0'>
					<tr>
						<td nowrap>
							<div id="bowbtn" class="funcContainer">
								&nbsp;&nbsp;
								<!--							
								<button type="button" id="btn_add_c120m01a">
									<span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
								</button>
								&nbsp;
								-->
								
								<button type="button" id="btn_imp_basicData">
									<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
								</button>
								&nbsp;
								
								<button type="button" id="btn_delRatingDocCust">
									<span class="text-only"><th:block th:text="#{'button.delete'}">刪除</th:block></span>
								</button>
								&nbsp;
								
								<button type="button" id="btn_setCustPosCustRlt">
									<span class="text-only"><th:block th:text="#{'tab02.btnSetCustPosCustRlt'}">修改相關身份</th:block></span>
								</button>
								&nbsp;
							</div>
						</td>
						<td width="15px;">&nbsp;</td>
						<td>
							<span class="text-red"><th:block th:text="#{'tab02.desc01'}">※若引進額度明細表之基本資料有異動，須更新評等後經覆核，再重新引進額度明細表。</th:block></span>
							<br><span class="text-red"><th:block th:text="#{'tab02.desc02'}">※M.主要借款人,C.共同借款人, G.連帶保證人。</th:block></span>
						</td>
					</tr>
				</table>
				<!-- ====== -->
				<div id="c120m01agrid" width="100%" style="margin-left: 10px; margin-right: 10px">
				</div>
			</fieldset>
			<!-- ====== -->
			<div id="thickBoxFromBaseData" style="display:none">
				<table border='0'>
					<tr>
						<td><input type="text" id="findBaseDataId" name="findBaseDataId" maxlength="10" size="10" class="upText" /></td>
						<td>&nbsp;&nbsp;</td>
						<td>
							<button type="button" id="findBaseDataIdBt">
			                    <span class="text-only"><th:block th:text="#{'button.filter'}">篩選</th:block></span>					
			                </button>			
						</td>
					</tr>				
                </table>
				<div id='grid_fromBaseData' ></div>			
			</div>
			<!-- ====== -->
			        	
        </th:block>
    </body>
</html>
