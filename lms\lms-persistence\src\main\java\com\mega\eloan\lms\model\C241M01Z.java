/* 
 * C241M01Z.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 覆審異動歷程檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C241M01Z", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C241M01Z extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 分行 **/
	@Size(max=3)
	@Column(name="BRANCH", length=3, columnDefinition="CHAR(3)")
	private String branch;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 異動前_覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="BF_CRDATE", columnDefinition="DATE")
	private Date bf_crDate;

	/** 異動前_類別 **/
	@Size(max=30)
	@Column(name="BF_REMOMO", length=30, columnDefinition="VARCHAR(30)")
	private String bf_remomo;

	/** 異動前_修改人 **/
	@Size(max=8)
	@Column(name="BF_UPDATER", length=8, columnDefinition="VARCHAR(8)")
	private String bf_updater;

	/** 異動前_修改時間 **/
	@Column(name="BF_TIME", columnDefinition="TIMESTAMP")
	private Timestamp bf_time;

	/** 異動前_報告書種類 **/
	@Size(max=1)
	@Column(name="BF_REPORTKIND", length=1, columnDefinition="CHAR(1)")
	private String bf_reportKind;

	/** 異動前_新案註記 **/
	@Size(max=1)
	@Column(name="BF_NEWFLAG", length=1, columnDefinition="CHAR(1)")
	private String bf_newFlag;

	/** 異動後_覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="AF_CRDATE", columnDefinition="DATE")
	private Date af_crDate;

	/** 異動後_類別 **/
	@Size(max=30)
	@Column(name="AF_REMOMO", length=30, columnDefinition="VARCHAR(30)")
	private String af_remomo;

	/** 異動後_修改人 **/
	@Size(max=8)
	@Column(name="AF_UPDATER", length=8, columnDefinition="VARCHAR(8)")
	private String af_updater;

	/** 異動後_修改時間 **/
	@Column(name="AF_TIME", columnDefinition="TIMESTAMP")
	private Timestamp af_time;

	/** 異動後_報告書種類 **/
	@Size(max=1)
	@Column(name="AF_REPORTKIND", length=1, columnDefinition="CHAR(1)")
	private String af_reportKind;

	/** 異動後_新案註記 **/
	@Size(max=1)
	@Column(name="AF_NEWFLAG", length=1, columnDefinition="CHAR(1)")
	private String af_newFlag;

	/** 
	 * 更改代碼<p/>
	 *  0.ELF490 轉 ELF491但不符合<br/>
	 *  1.ELF490 轉 ELF491符合<br/>
	 *  2.前置491轉 c241m01a<br/>
	 *  3. ELF 491轉 c241m01a<br/>
	 *  5.還原成需覆審<br/>
	 *  6.註記不覆審<br/>
	 *  7.自2020-05-22覆審改版後<br/>
	 *  8.類別99重整<br/>
	 *  9.結案算下次覆審日<br/>
	 *  A.異常通報(on)
	 *  B.異常通報(off)
	 *  C.轉逾催覆審
	 *  D.檢查R1金額結果不符合<br/>
	 *  F.覆審控制檔<br/>
	 *  G.跨月撥款<br/>
	 *  P.檢查R1金額處理中<br/>
	 *  U.檢查R1金額未處理
	 */
	@Size(max=1)
	@Column(name="REASON", length=1, columnDefinition="CHAR(1)")
	private String reason;

	/** 更改原因 **/
	@Size(max=600)
	@Column(name="REASONDESC", length=600, columnDefinition="VARCHAR(600)")
	private String reasonDesc;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得分行 **/
	public String getBranch() {
		return this.branch;
	}
	/** 設定分行 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得異動前_覆審日 **/
	public Date getBf_crDate() {
		return this.bf_crDate;
	}
	/** 設定異動前_覆審日 **/
	public void setBf_crDate(Date value) {
		this.bf_crDate = value;
	}

	/** 取得異動前_類別 **/
	public String getBf_remomo() {
		return this.bf_remomo;
	}
	/** 設定異動前_類別 **/
	public void setBf_remomo(String value) {
		this.bf_remomo = value;
	}

	/** 取得異動前_修改人 **/
	public String getBf_updater() {
		return this.bf_updater;
	}
	/** 設定異動前_修改人 **/
	public void setBf_updater(String value) {
		this.bf_updater = value;
	}

	/** 取得異動前_修改時間 **/
	public Timestamp getBf_time() {
		return this.bf_time;
	}
	/** 設定異動前_修改時間 **/
	public void setBf_time(Timestamp value) {
		this.bf_time = value;
	}

	/** 取得異動前_報告書種類 **/
	public String getBf_reportKind() {
		return this.bf_reportKind;
	}
	/** 設定異動前_報告書種類 **/
	public void setBf_reportKind(String value) {
		this.bf_reportKind = value;
	}

	/** 取得異動前_新案註記 **/
	public String getBf_newFlag() {
		return this.bf_newFlag;
	}
	/** 設定異動前_新案註記 **/
	public void setBf_newFlag(String value) {
		this.bf_newFlag = value;
	}

	/** 取得異動後_覆審日 **/
	public Date getAf_crDate() {
		return this.af_crDate;
	}
	/** 設定異動後_覆審日 **/
	public void setAf_crDate(Date value) {
		this.af_crDate = value;
	}

	/** 取得異動後_類別 **/
	public String getAf_remomo() {
		return this.af_remomo;
	}
	/** 設定異動後_類別 **/
	public void setAf_remomo(String value) {
		this.af_remomo = value;
	}

	/** 取得異動後_修改人 **/
	public String getAf_updater() {
		return this.af_updater;
	}
	/** 設定異動後_修改人 **/
	public void setAf_updater(String value) {
		this.af_updater = value;
	}

	/** 取得異動後_修改時間 **/
	public Timestamp getAf_time() {
		return this.af_time;
	}
	/** 設定異動後_修改時間 **/
	public void setAf_time(Timestamp value) {
		this.af_time = value;
	}

	/** 取得異動後_報告書種類 **/
	public String getAf_reportKind() {
		return this.af_reportKind;
	}
	/** 設定異動後_報告書種類 **/
	public void setAf_reportKind(String value) {
		this.af_reportKind = value;
	}

	/** 取得異動後_新案註記 **/
	public String getAf_newFlag() {
		return this.af_newFlag;
	}
	/** 設定異動後_新案註記 **/
	public void setAf_newFlag(String value) {
		this.af_newFlag = value;
	}	

	/** 取得更改代碼 **/
	public String getReason() {
		return this.reason;
	}
	/** 設定更改代碼 **/
	public void setReason(String value) {
		this.reason = value;
	}

	/** 取得更改原因 **/
	public String getReasonDesc() {
		return this.reasonDesc;
	}	
	/** 設定更改原因 **/
	public void setReasonDesc(String value) {
		this.reasonDesc = value;
	}
	
	public String getMainId() {
		return null;
	}
}
