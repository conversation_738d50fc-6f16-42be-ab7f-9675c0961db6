package com.mega.eloan.lms.base.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.jcs.common.Util;


/**<pre>
 * 異常通報需後續追蹤事項(營運中心、授管處)分頁
 * </pre>
 * @since  2012/10/22
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/10/22,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lmsm02/{page}")
public class LMSM02Page extends AbstractOutputPage {

	@Autowired
	L120M01ADao l120m01aDao;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		String docType = Util.trim(params.getString("docType"));
		setNeedHtml(true);		// need html
		if(Util.isEmpty(docType)){
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			L120M01A meta = l120m01aDao.findByMainId(mainId);
			if(meta == null){
				meta = new L120M01A();
			}
			docType = Util.trim(meta.getDocType());
		}

		 if(UtilConstants.Casedoc.DocType.個金.equals(docType)){
		 setJavascript(new String[] { "pagejs/lns/LMSM02BPage.js" });
		 }else{
		 setJavascript(new String[] { "pagejs/lns/LMSM02BPage.js" });
		 }
		setJavascript(new String[] { "pagejs/lns/LMSM02BPage.js" });
		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
