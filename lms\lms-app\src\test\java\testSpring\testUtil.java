/* 
 * testUtil.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package testSpring;

import org.junit.Assert;
import org.junit.Test;

/**
 * <pre>
 * 測試Util用
 * </pre>
 * 
 * @since 2012/8/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/8/21,REX,new
 *          </ul>
 */
public class testUtil {
	public final static char[] DupNoChar = { '0', '1', '2', '3', '4', '5', '6',
			'7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
			'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
			'X', 'Y', 'Z' };

	/**
	 * 測試DupNo轉換
	 */
	@Test
	public void TestChangeWord() {
		Assert.assertEquals("0 不符", this.changeWord(0), "0");
		Assert.assertEquals("10 不符", this.changeWord(10), "A");
		Assert.assertEquals("35 不符", this.changeWord(35), "Z");
		Assert.assertEquals("36 不符", this.changeWord(36), "Z");
		Assert.assertEquals("37 不符", this.changeWord(37), "Z");
	}

	private String changeWord(int size) {
		return String
				.valueOf(DupNoChar[(size >= DupNoChar.length) ? DupNoChar.length - 1
						: size]);
	}

}
