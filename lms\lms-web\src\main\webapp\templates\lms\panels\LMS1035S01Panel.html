<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">loadScript('pagejs/lms/LMS1035S01Panel');</script>
			<!-- ====================================================================== -->
			<fieldset>
                <legend>
                    <th:block th:text="#{'tab.01'}">文件資訊</th:block>					
                </legend>
				 <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td width="30%" class="hd1">
                                <th:block th:text="#{'doc.branchName'}">分行名稱</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="branchName" ></span>&nbsp;
                            </td>
							<td width="30%" class="hd1">
                                <th:block th:text="#{'C121M01A.ratingDate'}">評等日期</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="ratingDate" ></span>&nbsp;
                            </td>
                        </tr>
                        <tr>
                        	<td class="hd1" nowrap>
                                <th:block th:text="#{'C121M01A.custId'}">主要借款人身分證統編</th:block>&nbsp;&nbsp;								
                            </td>
                            <td>
                                <span id="custId"></span>&nbsp;-&nbsp;<span id="dupNo"></span> 　
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'C121M01A.custName'}">主要借款人姓名</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                               <span id="custName"></span>&nbsp;
                            </td>
                        </tr>
						<tr>
                        	<td class="hd1">
                                <th:block th:text="#{'tab01.lnPeriod'}">本案授信期間</th:block>&nbsp;&nbsp;								
                            </td>
                            <td>
                               <input type='text' id='lnYear'  name='lnYear'  class='required numeric' size="2" maxlength="2" integer="2"><th:block th:text="#{'tab01.lnYear'}">年</th:block>
							   <input type='text' id='lnMonth' name='lnMonth' class='required numeric' size="2" maxlength="2" integer="2"><th:block th:text="#{'tab01.lnMonth'}">月</th:block>
                            </td>
                        	<td class="hd1">
                                <th:block th:text="#{'tab01.repaymentSchFmt'}">本案授信期間等於額度明細表清償期限</th:block>&nbsp;&nbsp;								
                            </td>
                            <td>
                               <input type='radio' id='repaymentSchFmt' name='repaymentSchFmt' codetype='YesNo' class='required'>
							   <div id='div_repaymentSchDays'>
							   		<th:block th:text="#{'tab01.repaymentSch'}">清償期限</th:block>
									<input type='text' id='repaymentSchDays'  name='repaymentSchDays'  class='numeric' size="3" maxlength="3" integer="3">
							   		<th:block th:text="#{'tab01.days'}">天</th:block>
							   </div>
								
                            </td>                            
                        </tr>
						<tr>
                        	<td class="hd1">
                                <th:block th:text="#{'C121M01A.varVer'}">模型版本</th:block>&nbsp;&nbsp;								
                            </td>
                            <td>
                                <span id="varVer"></span>&nbsp; 　
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.docStatus'}">文件狀態</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <b><span class="color-red" id="status"></span></b>&nbsp;
                            </td>
                        </tr>
						<tr>
                        	<td class="hd1">
                                <th:block th:text="#{'C121M01A.caseNo'}">評等文件編號</th:block>&nbsp;&nbsp;
                            </td>
                            <td colspan="3">
                                <span id="caseNo"></span>&nbsp;
                            </td>
                        </tr>
                    </tbody>
                </table>
				
			</fieldset>
        	<!--==================================-->
			<fieldset>
                <legend>
                    <th:block th:text="#{'doc.docUpdateLog'}">文件異動紀錄</th:block>
                </legend>
			
                <div class="funcContainer">
                	<!-- 文件異動記錄 -->
                    <div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
                </div>
				
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'doc.creator'}">文件建立者</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="30%">
                                <span id="creator"></span>(<span id="createTime"></span>)
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.lastUpdater'}">最後異動者</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="updater"></span>(<span id="updateTime"></span>)
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                            </td>
                            <td>
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.docCode'}">報表亂碼</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="randomCode"></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </fieldset>
        </th:block>
    </body>
</html>
