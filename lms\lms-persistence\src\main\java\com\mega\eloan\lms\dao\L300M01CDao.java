/* 
 * L300M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L300M01C;

/** 覆審排名表檔 **/
public interface L300M01CDao extends IGenericDao<L300M01C> {

	L300M01C findByOid(String oid);
	
	List<L300M01C> findByMainId(String mainId);

	List<L300M01C> findByIndex01(String mainId, String ownBrId, Date creatDate);
	
	List<L300M01C> findByIndex02(String ownBrId, Date creatDate, Date bgnDate, Date endDate);
}