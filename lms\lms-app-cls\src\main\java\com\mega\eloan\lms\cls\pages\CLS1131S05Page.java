package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.panels.CLS1131S01QPanel;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01Q_N;
import com.mega.eloan.lms.model.C120S01Q;

import tw.com.jcs.common.Util;

@Controller
@RequestMapping("/cls/cls1131s05")
public class CLS1131S05Page extends AbstractOutputPage {

	@Autowired
	CLS1131Service cls1131Service;
	
	@Autowired
	ScoreService scoreService;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {

		setNeedHtml(true); // need html

		String mainId = Util.trim(params.getString("mainId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		boolean isC120M01A = Util.equals("Y", params.getString("isC120M01A"));
		String varVer = "";
		if(isC120M01A){
			C120S01Q c120s01q = cls1131Service.findModelByKey(C120S01Q.class, mainId, custId,dupNo);
			if(c120s01q!=null){
				varVer = Util.trim(c120s01q.getVarVer());
			}
		}else{
			C101S01Q c101s01q = cls1131Service.findModelByKey(C101S01Q.class, mainId, custId,dupNo);
			if(c101s01q!=null){
				varVer = Util.trim(c101s01q.getVarVer());
			}	
		}
		
		if(Util.isEmpty(Util.trim(varVer))){
			varVer = scoreService.get_Version_NotHouseLoan();
		}
		
		//雙軌模式運行
		boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
		boolean showSDT = false;
		if(scoreDoubleTrack){
			C101S01Q_N model_QN = cls1131Service.findModelByKey(C101S01Q_N.class, mainId, custId, dupNo);
			if(model_QN != null){
				showSDT = true;
			}
		}
		
		new CLS1131S01QPanel("CLS1131S01_Q", varVer, showSDT)
				.processPanelData(model, params); // add panel

		renderJsI18N(CLS1131S01QPanel.class); // render i18n

		setJavascript(new String[] { "pagejs/cls/CLS1131S05Page.js" });

		return "&nbsp;";
	}

	@Override
	protected String getViewName() {
		return null;
	}

}
