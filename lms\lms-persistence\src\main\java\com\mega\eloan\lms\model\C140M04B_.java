package com.mega.eloan.lms.model;

import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140M04B database table.
 * </pre>
 * @since  2011/10/4
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/4,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140M04B.class)
public class C140M04B_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140M04B, String> compKind;
	public static volatile SingularAttribute<C140M04B, String> editMode;
	public static volatile SingularAttribute<C140M04B, String> inputTs;
	public static volatile SingularAttribute<C140M04B, String> isGrpComp;
	public static volatile SingularAttribute<C140M04B, String> uid;
	public static volatile SingularAttribute<C140M04B, String> proCp;
	public static volatile SingularAttribute<C140M04B, String> probussId;
	public static volatile SingularAttribute<C140M04B, String> probussName;
	public static volatile SingularAttribute<C140M04B, String> tmpch1;
	public static volatile SingularAttribute<C140M04B, String> tmpch2;
	public static volatile ListAttribute<C140M04B, C140JSON> c140jsons;
//	public static volatile ListAttribute<C140M04B, C140S07B> c140s07bs;
	public static volatile SingularAttribute<C140M04A, C140M01A> c140m01a;
}
