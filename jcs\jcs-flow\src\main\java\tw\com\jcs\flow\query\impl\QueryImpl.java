package tw.com.jcs.flow.query.impl;

import java.util.List;

import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowEngineImpl;
import tw.com.jcs.flow.core.FlowEngineUnit;
import tw.com.jcs.flow.core.FlowPersistence;
import tw.com.jcs.flow.query.Query;

/**
 * <pre>
 * QueryImpl
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public class QueryImpl extends FlowEngineUnit implements Query {

    public QueryImpl(FlowEngineImpl processEngine) {
        super(processEngine);
    }

    /**
     * 流程ID
     */
    Object id;

    /**
     * 依流程定義
     */
    String definitionId;

    /**
     * 依所屬角色
     */
    String roleId;

    /**
     * 依所屬使用者
     */
    String userId;

    /**
     * 依所屬部門
     */
    String deptId;

    /**
     * 依流程狀態
     */
    String stateId;

    /**
     * 包含歷程資訊
     */
    boolean all = false;

    /**
     * 流程為已結束流程
     */
    boolean history = false;

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#definition(java.lang.String)
     */
    @Override
    public Query definition(String id) {
        this.definitionId = id;
        return this;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#role(java.lang.String)
     */
    @Override
    public Query role(String id) {
        this.roleId = id;
        return this;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#user(java.lang.String)
     */
    @Override
    public Query user(String id) {
        this.userId = id;
        return this;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#dept(java.lang.String)
     */
    @Override
    public Query dept(String id) {
        this.deptId = id;
        return this;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#state(java.lang.String)
     */
    @Override
    public Query state(String name) {
        this.stateId = name;
        return this;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#id(java.lang.Object)
     */
    @Override
    public Query id(Object id) {
        this.id = id;
        return this;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#all()
     */
    @Override
    public Query all() {
        all = true;
        return this;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#history()
     */
    @Override
    public Query history() {
        history = true;
        return this;
    }

    public Object getId() {
        return id;
    }

    public String getDefinitionId() {
        return definitionId;
    }

    public String getRoleId() {
        return roleId;
    }

    public String getUserId() {
        return userId;
    }

    public String getDeptId() {
        return deptId;
    }

    public String getStateId() {
        return stateId;
    }

    public boolean isHistory() {
        return history;
    }

    public boolean isAll() {
        return all;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#queryForList()
     */
    @Override
    public List<FlowInstance> queryForList() {
        FlowPersistence persist = getEngine().getPersistence();
        return persist.queryForInstance(this);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.query.Query#query()
     */
    @Override
    public FlowInstance query() {
        FlowPersistence persist = getEngine().getPersistence();
        List<FlowInstance> list = persist.queryForInstance(this);
        if (list.size() > 0) {
            return list.iterator().next();
        }
        return null;
    }

}
