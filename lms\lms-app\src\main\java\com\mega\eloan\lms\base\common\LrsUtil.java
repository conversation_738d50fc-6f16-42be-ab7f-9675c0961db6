package com.mega.eloan.lms.base.common;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01C;
import com.mega.eloan.lms.model.L170M01D;
import com.mega.eloan.lms.model.L170M01E;
import com.mega.eloan.lms.model.L170M01H;
import com.mega.eloan.lms.model.L170M01J;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L181M01B;

public class LrsUtil {
	public static final String V_20141001 = "Ver20141001";
	public static final String V_201412 = "Ver201412";
	public static final String V_20161101 = "Ver20161101";
	public static final String V_20170519 = "Ver20170519"; // J-106-0123-001 Web
															// e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」
	public static final String V_20170603 = "Ver20170603"; // J-106-0145-001 Web
															// e-Loan
															// 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	public static final String V_20190201 = "Ver20190201"; // J-107-0290_09301_B1001
															// CTLTYPE_主辦覆審
															// 第18項拆項 N025
	public static final String V_20220401 = "Ver20220401";	// J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
															// Z_DESC_N008 第7項應註明撥貸入借... 覆審卷備查或確認E-LOAN授信管理系統貸後管理追蹤檢核表已存載。
	                                                        // 2022/02/15 授審連喬凱說 可以先上線不用等4/1

    public static final String V_20220901 = "Ver20220901";	// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。

	// J-108-0128_05097_B1001 Web e-Loan企金授信覆審系統修改覆審報告表內容。
	public static final String V_20190701 = "Ver20190701";
	
	public static final String V_20230706 = "Ver20230706";  // J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。

	// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
	public static final String V_20240401 = "Ver20240401"; 

	// J-113-0204  新增及修正說明文句
	public static final String V_20240601 = "Ver20240601";
	
	// 報表啟用日期_STR
	
	// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句	
	public static final String V_RPT_1130401 = "113.4.1"; 	
	// J-113-0204  新增及修正說明文句
	public static final String V_RPT_1130601 = "113.6.1"; 
	
	
	// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句	
	public static final String V_RPT_11304 = "113.04"; 	
	// J-113-0204  新增及修正說明文句
	public static final String V_RPT_11306 = "113.06"; 	
	
	// 報表啟用日期_END
	
	// 屬董事會（或常董會）權限
	public static final String V_B_201708 = "VerB_201708";

    // J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句	 
	public static final String V_B_202404 = "VerB_202404";

	// 海外覆審表版本別_STR
	public static final String V_O_201809 = "Ver201809"; // 009301海外改格式
	public static final String V_O_201907 = "Ver201907"; // 009301海外改格式
	public static final String V_O_202204 = "Ver202204"; // J-111-0031 更動覆審系統內以下九式覆審報告表之文字內容。
	public static final String V_O_202210 = "Ver202210"; // J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
	public static final String V_O_202307 = "Ver202307"; // J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。			       
	public static final String V_O_202404 = "Ver202404"; // J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句     
	public static final String V_O_202406 = "Ver202406"; // J-113-0204  新增及修正說明文句
	// 海外覆審表版本別_END
	
	public static final String V_C_201901 = "VerC_201901"; // J-107-0254_09301_B1001
															// 配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
		
	// 考評表版本
	public static final String PAVER_20230701 = "Ver20230701";

	public static final String K_GET_LOAN_DATA = "autoGetLoanData";
	public static final String ATTCH_L180M01A_0 = "listExcel";
	public static final String ATTCH_L180M01A_1 = "listChk";

	public static final String ATTCH_L180M01A_ZIP = "listZip";

	public static final String M01B_CMS_NONE = "無擔保品";
	public static final String M01B_CMS_SAMEAS = "同上";

	public static final String M01E_FLAG_C = "C";
	public static final String M01E_FLAG_M = "M";
	public static final String M01E_FLAG_F = "F";

	public static final Date M01E_CUST_CRDTYEAR = CapDate
			.parseDate("9999-12-31");
	public static final String M01E_CUST_CRDTBR = "000";
	public static final String M01E_CUST_CNTRNO = "00000000000000";
	public static final String M01E_CUST_FINYEAR = "9999";

	public static final String M01H_FREE_DEBID_GN = "00000000";
	public static final String M01H_FREE_DEBID_C = "10101013";// 10101013
																// 是聯徵測試的統編

	public static final String M01H_FLAG_ALL = "ALL";
	public static final String M01H_FLAG_C = "C";
	public static final String M01H_FLAG_G = "G";
	public static final String M01H_FLAG_GN = "GN";

	public static final String A_徵信事項 = "A";
	public static final String B_債權確保 = "B";
	public static final String C_其他 = "C"; // J-106-0123-001 Web
											// e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」

	public static final String D_董事會或常董會權限 = "D";
	public static final String X_土建融 = "X"; // J-106-0145-001 Web e-Loan
											// 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	public static final String Z_電腦建檔資料 = "Z";
	public static final String Y_履約條件 = "Y";
	public static final String N001 = "N001";
	public static final String N002 = "N002";
	public static final String N003 = "N003";
	public static final String N004 = "N004";
	public static final String N005 = "N005";
	public static final String N006 = "N006";
	public static final String N007 = "N007";
	public static final String N008 = "N008";
	public static final String N009 = "N009";
	public static final String N010 = "N010";
	public static final String N011 = "N011";
	public static final String N012 = "N012";
	public static final String N013 = "N013";
	public static final String N014 = "N014";
	public static final String N015 = "N015";
	public static final String N016 = "N016";
	public static final String N017 = "N017";
	public static final String N018 = "N018";
	public static final String N019 = "N019";
	public static final String N020 = "N020";
	public static final String N021 = "N021";
	public static final String N022 = "N022";
	public static final String N023 = "N023";
	public static final String N024 = "N024";
	public static final String N025 = "N025";
	public static final String N026 = "N026";
	public static final String N027 = "N027";
	public static final String N028 = "N028"; // J-106-0123-001 Web
												// e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」
	public static final String N029 = "N029"; // N025 拆項

	public static final String N030 = "N030"; // J-108-0128_05097_B1001 Web
												// e-Loan企金授信覆審系統修改覆審報告表內容。

	public static final String N031 = "N031"; // J-108-0128_05097_B1001 Web
	// e-Loan企金授信覆審系統修改覆審報告表內容。

	// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
	public static final String N032 = "N032";
	// J-113-0204  新增及修正說明文句
	public static final String N033 = "N033";
		
	public static final String B001 = "B001";
	public static final String B002_ref_N001 = "B002";
	public static final String B003_ref_N002 = "B003";
	public static final String B004_ref_N003 = "B004";
	public static final String B005 = "B005";
	public static final String B006 = "B006";
	public static final String B007 = "B007";
	public static final String B008_ref_N011 = "B008";
	public static final String B009 = "B009";
	public static final String B010 = "B010";
	public static final String B011_ref_N015 = "B011";
	public static final String B012 = "B012";
	public static final String B013_ref_N020 = "B013";
	public static final String B014 = "B014";
	public static final String B015_N017 = "B015";
	public static final String B016 = "B016";
	// J-113-0066 企金覆審，覆審內容之覆審項目新增及修正說明文句
	public static final String B017 = "B017";
	public static final String B018 = "B018";

	public static final String ZA00 = "ZA00";
	public static final String ZA10 = "ZA10";
	public static final String ZA11 = "ZA11";
	public static final String ZA12 = "ZA12";
	
	// J-113-0204  新增及修正說明文句
	public static final String ZA13 = "ZA13";
	
	public static final String ZA20 = "ZA20";
	public static final String ZA21 = "ZA21";
	public static final String ZA22 = "ZA22";
	public static final String ZA23 = "ZA23";
	public static final String ZA24 = "ZA24";
	public static final String Z_DESC_ZA23_Y = "，若勾「有」，則應檢視該額度之下列資料建檔是否正確：";
	public static final String ZA2A = "ZA2A";
	public static final String ZA2B = "ZA2B";
	public static final String ZA2C = "ZA2C";
	public static final String ZA2D = "ZA2D";
	public static final String ZA2E = "ZA2E";
	public static final String ZA2F = "ZA2F";
	public static final String ZA30 = "ZA30";
	public static final String ZA31 = "ZA31";
	public static final String ZA32 = "ZA32";
	public static final String ZA33 = "ZA33";
	public static final String ZA34 = "ZA34";
	public static final String ZA35 = "ZA35";

	public static final String ZB00 = "ZB00";
	public static final String ZB10 = "ZB10";	// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
	public static final String ZB1A = "ZB1A";
	public static final String Z_DESC_ZB1A_Y = "，若勾「有」，則應檢視下列資料建檔是否正確：";
	public static final String ZB11 = "ZB11";
	public static final String ZB12 = "ZB12";
	public static final String ZB13 = "ZB13";
	
	// J-113-0204  新增及修正說明文句
	public static final String ZB14 = "ZB14";
	// J-113-0204  新增及修正說明文句
	public static final String ZB15 = "ZB15";
	
	public static final String ZB20 = "ZB20";	// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
	public static final String ZB2A = "ZB2A";	// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
	public static final String ZB21 = "ZB21";	// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。

	// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
	public static final String ZB30 = "ZB30";
	public static final String ZB31 = "ZB31";
	public static final String ZB3A = "ZB3A";
	public static final String ZB3B = "ZB3B"; 
	public static final String ZB32 = "ZB32";
	public static final String ZB3C = "ZB3C";
	public static final String ZB3D = "ZB3D";
	public static final String ZB33 = "ZB33";
	public static final String ZB3E = "ZB3E";
	public static final String ZB3F = "ZB3F";
	public static final String ZB3G = "ZB3G";
	public static final String ZB3H = "ZB3H";
	public static final String ZB3I = "ZB3I";
	public static final String ZB3J = "ZB3J";
	public static final String ZB34 = "ZB34";
	public static final String ZB3K = "ZB3K";
	public static final String ZB3L = "ZB3L";
	public static final String ZB3M = "ZB3M";
	public static final String ZB35 = "ZB35";
	public static final String ZB3N = "ZB3N";
	public static final String ZB3O = "ZB3O";
	public static final String ZB3P = "ZB3P";
	public static final String ZB3Q = "ZB3Q";
	public static final String ZB3R = "ZB3R";
	public static final String ZB3S = "ZB3S";
	
	// J-113-0204  新增及修正說明文句
	public static final String ZB40 = "ZB40";
	public static final String ZB50 = "ZB50";
	public static final String ZC00 = "ZC00";
	public static final String ZC10 = "ZC10";
	public static final String ZC1A = "ZC1A";
	public static final String ZC20 = "ZC20";
	public static final String ZC30 = "ZC30";
	public static final String ZC3A = "ZC3A";
	public static final String ZC3B = "ZC3B";
	public static final String ZC3C = "ZC3C";
	public static final String ZC3D = "ZC3D";
	
	// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	public static final String YA00 = "YA00";
	public static final String YA1A = "YA1A";
	public static final String Y_DESC_YA1A_Y = "，若勾「有」，則應檢視下列各項：";
	public static final String YA11 = "YA11";
	public static final String YA12 = "YA12";
	public static final String YA13 = "YA13";
	public static final String Y_DESC_YA11_N = "(若勾「否」，應予說明)";
	public static final String Y_DESC_YA11_ALL = "(均應說明辦理情形)";

	// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	public static final String YB00 = "YB00";
	public static final String YB1A = "YB1A";
	public static final String Y_DESC_YB1A_Y = "，若勾「有」，則應檢視下列各項：";
	public static final String YB11 = "YB11";
	public static final String YB12 = "YB12";
	public static final String YB13 = "YB13";
	public static final String Y_DESC_YB11_N = "(若勾「否」，應予說明)";
	public static final String Y_DESC_YB11_ALL = "(均應說明辦理情形)";

	// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	public static final String XA00 = "XA00";
	public static final String XA1A = "XA1A";
	public static final String X_DESC_XA1A_Y = "，若勾「有」，則應檢視下列各項：";
	public static final String XA11 = "XA11";
	public static final String XA12 = "XA12";
	public static final String XA13 = "XA13";
	public static final String XA14 = "XA14";
	public static final String X_DESC_XA11_N = "(若勾「否」，應予說明)";
	public static final String X_DESC_XA11_ALL = "(均應說明辦理情形)";

	// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	public static final String Z_DESC_N008 = "註明調閱傳票、相關資料及影印資金流向詳情存覆審卷備查";
	public static final String Z_DESC_N008_20170603 = "註明撥貸入借戶指定存款帳戶後之資金流向，敘明撥貸資金轉出或匯出之借貸交易帳戶和金額是否符合申貸用途之詳情，並影印資金流向資料存覆審卷備查";
	public static final String Z_DESC_N008_20220401 = "註明撥貸入借戶指定存款帳戶後之資金流向，敘明撥貸資金轉出或匯出之借貸交易帳戶和金額是否符合申貸用途之詳情，並影印資金流向資料存覆審卷備查或確認E-LOAN授信管理系統貸後管理追蹤檢核表已存載";

	public static final String Z_DESC_N012 = "註明實際工程進度/或履約情形";
	public static final String Z_DESC_N027 = "請於下表勾選所列項目之建檔資料是否正確";
	public static final String Z_DESC_N027_PRT = "除須查核各項授信電腦建檔資料覆核是否確實外，應於「附表」勾選所列項目之建檔資料是否正確";

	// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	public static final String Y_DESC_N015 = "請於下表勾選「應檢視事項」或「承諾事項」項目";
	// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	// public static final String Y_DESC_N015_Print = "若有違反承諾或約定事項是否依核定條件處置？";
	// J-109-0336 檢視事項及承諾事項之管控機制
	public static final String Y_NOTE_N015 = "請注意：屬尚未達應檢視日期或未來尚仍須檢視者，而未於「貸後管理追蹤檢核表」列管追蹤時，仍應勾「否」";
	public static final String Y_NOTE_YAYB_NA = "尚未達應檢視日期，請勾「N.A.」";

	// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	public static final String X_DESC_N011 = "土建融案件實地覆審結果，應於「附表」勾選檢視事項";

	public static final String Z_DESC_N015_PRT = "，及勾選「應檢視事項」與「承諾事項」是否符合核定條件";

	public static final String Z_DESC_N025_PRINT = "註明處理情形";
	public static final String Z_DESC_N025 = "如為是" + Z_DESC_N025_PRINT;
	public static final String Z_DESC_N017 = "如為有：";
	public static final String Z_NOTE_ZA30 = "注意!!銀行法第72條之2管控項目(3)用途別、(4)融資業務分類是否正確。";
	// J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
	public static final String Z_NOTE_ZB21 = "*因購置、興建廠房而排除列入72-2限額控管之案件，應於貸後管理檢核系統定期追蹤至廠房完工後，於e-loan「授信管理系統-建檔維護-額度相關資訊註記維護」完成建檔(國內廠房須含工廠登記/許可編號)為止。";

	public static final String L170M01D_RPLC = "‵0‵";
	// 替換成序號用的
	public static final String L170M01D_RPLCSEQ = "‵1‵";
	// 替換覆審內容文字用的
	public static final String L170M01D_RPLCSTR = "‵2‵";
	
	// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
	private static final String[] ZSYS_TITLE = { ZA00, ZA10, ZA20, ZA23, ZA30,
			                                     ZB00, ZB10, ZB1A, ZB20, ZB2A,
			                                     ZB31, ZB32, ZB33, ZB34, ZB35, ZB3N, ZB3Q,
			                                     ZC00, ZC30
			                                   };

	// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。 
	public static final String[] Z_TAB1 = { ZA10, ZA20, ZA30, ZB1A, ZB2A,
		                                    ZB31, ZB32, ZB33, ZB34, ZB35
		                                  };
	public static final String[] Z_TAB2 = { ZA11, ZA12, ZA13, 
		                                    ZA21, ZA22, ZA23, ZA24,
			                                ZA31, ZA32, ZA33, ZA34, ZA35, 
			                                ZB11, ZB12, ZB13, ZB14, ZB15, ZB21,
			                                ZB3A, ZB3B, ZB3C, ZB3D, ZB3E, ZB3J, ZB3K, ZB3L, ZB3M, ZB3N, ZB3Q,
			                                ZC10, ZC20, ZC30 
			                              };
	public static final String[] Z_TAB3 = { ZA2A, ZA2B, ZA2C, ZA2D, ZA2E, ZA2F,
		                                    ZB3F, ZB3G, ZB3H, ZB3I, ZB3O, ZB3P, ZB3R, ZB3S,
		                                    ZC1A,ZC3A, ZC3B,ZC3C,ZC3D
		                                  };
	public static final String[] Z_YNBOX = { ZA11, ZA12, ZA13,
		                                    ZA21, ZA22, ZA24,
			                                ZA2A, ZA2B, ZA2C, ZA2D, ZA2E, ZA2F, ZA31, ZA32, ZA33, ZA34, ZA35,
			                                ZB11, ZB12, ZB13, ZB14, ZB15, ZB21,
			                                ZB30, 
			                                ZB3A, ZB3B, 
			                                ZB3C ,ZB3D, 
			                                ZB3E, ZB3F, ZB3G, ZB3H, ZB3I, ZB3J,
			                                ZB3K, ZB3L, ZB3M,
			                                ZB3O, ZB3P, ZB3R, ZB3S,
			                                ZB40,
			                                ZB50,
			                                ZC10,ZC1A,ZC20,ZC3A,ZC3B,ZC3C,ZC3D
			                              };

	// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	private static final String[] YSYS_TITLE = { YA00, YA1A, YB00, YB1A };
	public static final String[] Y_TAB1 = { YA1A, YB1A };
	public static final String[] Y_TAB2 = { YA11, YA12, YA13, YB11, YB12, YB13 };
	public static final String[] Y_YNBOX = { YA11, YA12, YA13, YB11, YB12, YB13 };

	// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	private static final String[] XSYS_TITLE = { XA00 };
	public static final String[] X_TAB1 = { XA1A };
	public static final String[] X_TAB2 = { XA11, XA12, XA13, XA14 };
	public static final String[] X_YNBOX = { XA1A, XA11, XA12, XA13, XA14 };

	public static final String NCKD_1_本行或同業主辦之聯貸案件_非擔任管理行 = "1";
	public static final String NCKD_2_十成定存 = "2";
	public static final String NCKD_3_純進出押戶 = "3";
	public static final String NCKD_4_對政府或政府所屬機關_學校之授信案件 = "4";
	public static final String NCKD_5_拆放同業或對同業之融通 = "5";
	public static final String NCKD_6_已列報為逾期放款或轉列催收款項之案件 = "6";
	public static final String NCKD_7_銷戶 = "7";
	public static final String NCKD_8_本次暫不覆審 = "8";
	public static final String NCKD_9_已專案核准免辦理覆審之房屋仲介價金履約保證案件 = "9";
	public static final String NCKD_10_外勞保證中長期授信案件_已於新作後辦理一次覆審_且無增額_減額_變更條件或續約 = "10";
    public static final String NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內 = "11";
    public static final String NCKD_12_有效額度NTD1000w以下信保七成以上或十足擔保之不循環案件_已於新作增貸後辦理一次覆審 = "12";
	public static final String NCKD_13_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件_未列於抽樣需覆審名單內 = "13";
	public static final String NCKD_A_非董事會或常董會權限案件 = "A";
	public static final String NCKD_B_參貸同業主辦之聯合授信案件 = "B";
	public static final String NCKD_C_國內營業單位辦理之境外公司授信案件含對大陸地區授信 = "C";
	public static final String NCKD_D_國外營業單位單獨承做之跨國非當地國授信案件 = "D";
	public static final String NCKD_E_非實地覆審主辦分行 = "E";

	public static final String TEMP_PDF_EXT = ".del.pdf";

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	public static final String CTLTYPE_主辦覆審 = "A"; // 一般、土建融
	public static final String CTLTYPE_自辦覆審 = "B"; // 常董會權限
	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	public static final String CTLTYPE_價金履約 = "C"; // 價金履約

	// J-107-0254_09301_B1001 配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
	public static final String CTLTYPE_C_B005_prefix = "抽查覆審期間申請買賣價金履保專戶開戶案件至少3件(若不足3件，應全部查核)，並做說明。";
	public static final String CTLTYPE_C_B009_prefix = "調閱覆審期間已完成交屋案件至少3件(若不足3件，應全部查核)，保證費收取之入帳紀錄。";
	public static final String CTLTYPE_C_B007_note = "【登記簿登記內容至少應包含：履約保證書簽發日期、保證書字號、保證書金額、履保專戶帳號、經辦及甲級主管簽章】";

	public static final String RANDOMTYPE_A_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件 = "A";
	public static final String RANDOMTYPE_Z_純小規模營業人 = "Z";

	/**
	 * 當nckdFlag 為8, return true
	 * 2021/08，新增當nckdFlag=13, return true
	 *
	 * @param param
	 * @return
	 */
	public static boolean isNckdFlag_EMPTY_8(String nckdFlag) {
		// 8-本次暫不覆審
		// 2021/08 J-110-0272 新增不覆審代碼13
		if (StringUtils.isBlank(nckdFlag)
				|| Util.equals(NCKD_8_本次暫不覆審, nckdFlag)
				|| Util.equals(NCKD_13_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件_未列於抽樣需覆審名單內, nckdFlag)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 需覆審異常通報的不覆審代碼 => 走正常的calcDate判斷
	 * 2021/01 不覆審代碼 11 有異常通報需覆審
	 * 2021/01/16 不覆審代碼 12 有異常通報需覆審
     * 2021/02 不覆審代碼 11 異常通報不需覆審
	 * 2021/08 不覆審代碼 13 有異常通報需覆審
	 */
    public static boolean isNckdFlag_MDFLAG(String mdFlag, String nckdFlag) {
		String[] match_NckdFlagArr = {
				// NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內,
				NCKD_12_有效額度NTD1000w以下信保七成以上或十足擔保之不循環案件_已於新作增貸後辦理一次覆審,
				NCKD_13_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件_未列於抽樣需覆審名單內 };
        if (Util.isNotEmpty(Util.trim(mdFlag))
				&& CrsUtil.inCollection(nckdFlag, match_NckdFlagArr)) {
            return true;
        } else {
            return false;
        }
    }

	public static boolean isCustId_Z(String custId) {
		return "Z".equals(StringUtils.substring(custId, 2, 3));
	}

	public static String elf412_rocDateStr_toYYYYMM(String rocStr) {
		if (StringUtils.isBlank(rocStr)) {
			return "";
		}

		String rocYYY = StringUtils.substring(rocStr, 0, 4);
		String mm = StringUtils.substring(rocStr, 4, 6);

		return String.valueOf(Util.parseInt(rocYYY) + 1911) + mm;
	}

	public static String elf412_rocDateStr_from_Date(Date d) {
		String r = "";
		if (CrsUtil.isNOT_null_and_NOTZeroDate(d)) {
			String yyyyMM = CapDate.formatDate(d, "yyyyMM");
			r = model_elfNewDate_to_elf412_rocDateStr(yyyyMM);
		}
		return r;
	}

	public static Date elf412_rocDateStr_to_Date(String rocStr) {
		return model_elfNewDate_to_Date(elf412_rocDateStr_toYYYYMM(rocStr));
	}

	/**
	 * 201406 → Date(20140601)
	 */
	public static Date model_elfNewDate_to_Date(String yyyyMM) {
		if (StringUtils.isBlank(yyyyMM)) {
			return null;
		}
		return CapDate.parseDate(yyyyMM + "01");
	}

	/**
	 * 201406 → 010306
	 */
	public static String model_elfNewDate_to_elf412_rocDateStr(
			String l181m01bNewDate) {
		if (StringUtils.isBlank(l181m01bNewDate)) {
			return "";
		}

		String yyyy = l181m01bNewDate.substring(0, 4);
		String mm = l181m01bNewDate.substring(4, 6);
		// elf490_data_ym 的格式: 009912
		return StringUtils.right("0000" + (Integer.parseInt(yyyy) - 1911) + mm,
				6);
	}

	/**
	 * 把 010306 轉成 103.06
	 */
	public static String toStr_NewDate(String newDate, String sep) {
		if (newDate == null) {
			return "";
		}
		if (newDate.length() != 6) {
			return newDate;
		}
		return String.valueOf(Util.parseInt(StringUtils
				.substring(newDate, 0, 4)))
				+ sep
				+ StringUtils.substring(newDate, 4, 6);
	}

	public static void model_zeroDate_to_null(L180M01B model) {
		if (CrsUtil.isNull_or_ZeroDate(model.getElfLLRDate())) {
			model.setElfLLRDate(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfLRDate())) {
			model.setElfLRDate(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfUCkdDt())) {
			model.setElfUCkdDt(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfCancelDt())) {
			model.setElfCancelDt(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfMDDt())) {
			model.setElfMDDt(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfNCkdDate())) {
			model.setElfNCkdDate(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfNextNwDt())) {
			model.setElfNextNwDt(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfUpdDate())) {
			model.setElfUpdDate(null);
		}
	}

	public static void model_elf412Str_to_N(L180M01B model) {
		if (!"Y".equals(model.getElfDBUOBU())) {
			model.setElfDBUOBU("N");
		}
		if (!"Y".equals(model.getElfMainCust())) {
			model.setElfMainCust("N");
		}
		if (!"Y".equals(model.getElfUCkdLINE())) {
			model.setElfUCkdLINE("N");
		}
	}

	public static void model_zeroDate_to_null(L181M01B model) {
		if (CrsUtil.isNull_or_ZeroDate(model.getElfLRDate())) {
			model.setElfLRDate(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfMDDt())) {
			model.setElfMDDt(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfNCkdDate())) {
			model.setElfNCkdDate(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfNextNwDt())) {
			model.setElfNextNwDt(null);
		}
		if (CrsUtil.isNull_or_ZeroDate(model.getElfUCkdDt())) {
			model.setElfUCkdDt(null);
		}

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		if (CrsUtil.isNull_or_ZeroDate(model.getElfNewRptDt())) {
			model.setElfNewRptDt(null);
		}

		if (CrsUtil.isNull_or_ZeroDate(model.getElfOldRptDt())) {
			model.setElfOldRptDt(null);
		}
	}

	public static void model_elf412Str_to_N(L181M01B model) {
		if (!"Y".equals(model.getElfDBUOBU())) {
			model.setElfDBUOBU("N");
		}
		if (!"Y".equals(model.getElfMainCust())) {
			model.setElfMainCust("N");
		}
		if (!"Y".equals(model.getElfUCkdLINE())) {
			model.setElfUCkdLINE("N");
		}
	}

	public static void elf412_null_to_zeroDate(ELF412 elf412) {
		if (elf412.getElf412_llrDate() == null) {
			elf412.setElf412_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412.getElf412_lrDate() == null) {
			elf412.setElf412_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412.getElf412_mdDt() == null) {
			elf412.setElf412_mdDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412.getElf412_nckdDate() == null) {
			elf412.setElf412_nckdDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412.getElf412_cancelDt() == null) {
			elf412.setElf412_cancelDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412.getElf412_uckdDt() == null) {
			elf412.setElf412_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412.getElf412_nextNwDt() == null) {
			elf412.setElf412_nextNwDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412.getElf412_nextLtDt() == null) {
			elf412.setElf412_nextLtDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
	}

	public static void elf493_null_to_zeroDate(ELF493 elf493) {
		if (elf493.getElf493_llrDate() == null) {
			elf493.setElf493_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_lrDate() == null) {
			elf493.setElf493_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_mdDt() == null) {
			elf493.setElf493_mdDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_nckdDate() == null) {
			elf493.setElf493_nckdDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_cancelDt() == null) {
			elf493.setElf493_cancelDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_uckdDt() == null) {
			elf493.setElf493_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_nextNwDt() == null) {
			elf493.setElf493_nextNwDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_nextLtDt() == null) {
			elf493.setElf493_nextLtDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_n_nextNwDt() == null) {
			elf493.setElf493_n_nextNwDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf493.getElf493_n_lrDate() == null) {
			elf493.setElf493_n_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		if (elf493.getElf493_oldRptDt() == null) {
			elf493.setElf493_oldRptDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}

		if (elf493.getElf493_newRptDt() == null) {
			elf493.setElf493_newRptDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
	}

	public static boolean isAllGradEmpty(String _ELF412_CRDTTBL,
			String _ELF412_MOWTBL1, String _ELF412_FCRDGRAD) {
		return StringUtils.isBlank(_ELF412_CRDTTBL)
				&& StringUtils.isBlank(_ELF412_MOWTBL1)
				&& StringUtils.isBlank(_ELF412_FCRDGRAD);
	}

	public static boolean hasGrad(String _ELF412_CRDTTBL,
			String _ELF412_MOWTBL1, String _ELF412_FCRDGRAD) {
		return StringUtils.isNotBlank(_ELF412_CRDTTBL)
				|| StringUtils.isNotBlank(_ELF412_MOWTBL1)
				|| StringUtils.isNotBlank(_ELF412_FCRDGRAD);
	}

	public static String toDBUOBU_COID(Map<String, String> map) {
		List<String> r = new ArrayList<String>();
		for (String id : map.keySet()) {
			String name = map.get(id);
			r.add(id + " " + name);
		}
		return StringUtils.join(r, ",");
	}

	public static String toStrYM(Date d) {
		return CapDate.formatDate(d, UtilConstants.DateFormat.YYYY_MM);
	}

	public static String build_coMainId(L180M01B model) {
		return model.getCustId();
	}

	public static boolean isExistBatchNo(L180M01A meta) {
		Integer batchNo = meta.getBatchNO();
		if (batchNo == null || batchNo == 0) {
			return false;
		} else {
			return true;
		}
	}

	public static String getExcelName(L180M01A meta, String fieldId) {
		if (Util.equals(ATTCH_L180M01A_0, fieldId)) {
			String batchNO = "001";
			if (isExistBatchNo(meta)) {
				batchNO = Util.addZeroWithValue(meta.getBatchNO(), 3);
			}

			return meta.getBranchId()
					+ "-"
					+ Util.trim(StringUtils.substring(
							elf412_rocDateStr_from_Date(meta.getDataDate()), 1))
					+ "-" + batchNO + ".xls";
		} else if (Util.equals(ATTCH_L180M01A_1, fieldId)) {
			String batchNO = "001";
			if (isExistBatchNo(meta)) {
				batchNO = Util.addZeroWithValue(meta.getBatchNO(), 3);
			}

			return "Chk-"
					+ meta.getBranchId()
					+ "-"
					+ Util.trim(StringUtils.substring(
							elf412_rocDateStr_from_Date(meta.getDataDate()), 1))
					+ "-" + batchNO + ".xls";
		}
		return "";
	}

	public static boolean get_adjFlag(RO412 src_ro412, RO412 ro412) {
		boolean adjFlag = false;
		if (Util.notEquals(src_ro412.get_mainCust(), ro412.get_mainCust())) {
			adjFlag = true;
		}
		if (Util.equals("A", src_ro412.get_rckdLine())
				&& Util.equals("B", ro412.get_rckdLine())) {
			adjFlag = true;
		}
		return adjFlag;
	}

	/**
	 * 
	 * 格式為：年度(YYYY) +分行簡稱(3碼)+(兆)+覆審字第+ 批號+ - + 序號 + 號 例：2011蘭雅(兆)覆審字第001-003號
	 * 回傳 001-003
	 */
	public static String extractProjectNo(String s) {
		if (Util.isEmpty(Util.trim(s))) {
			return "";
		}
		String[] arr = StringUtils.split(s, "-");
		if (arr.length >= 2) {
			return StringUtils.right(arr[0], 3) + "-"
					+ StringUtils.left(arr[1], 3);
		} else {
			return s;
		}
	}

	/**
	 * NA 轉成 未評等 <br/>
	 * TWAAA 仍為 TWAAA
	 */
	public static String fcrdGrad_code_to_desc(String l170m01e_grade) {
		if (Util.equals(UtilConstants.crdType.未評等, l170m01e_grade)) {
			return "未評等";
		} else {
			return l170m01e_grade;
		}
	}

	/**
	 * 未評等 轉成 NA <br/>
	 * TWAAA 仍為 TWAAA
	 */
	public static String fcrdGrad_desc_to_code(String fcrdGrad) {
		if (Util.equals(fcrdGrad, "未評等")) {
			return UtilConstants.crdType.未評等;
		} else {
			return fcrdGrad;
		}
	}

	public static void setL170M01E_FCRD(L170M01E l170m01e_F, String FcrdType,
			String FcrdArea, String FcrdPred, String FcrdGrad) {
		l170m01e_F.setCrdType(FcrdType);
		l170m01e_F.setFcrdArea(FcrdArea);
		l170m01e_F.setFcrdPred(FcrdPred);
		// 暫先清空
		l170m01e_F.setGrade("");
		l170m01e_F.setScore(null);
		// 把UI上的 AA+|12 轉成 DB 儲存的值
		if (Util.isNotEmpty(FcrdGrad)) {
			if (FcrdGrad.indexOf("|") < 0) {
				l170m01e_F.setGrade(FcrdGrad);
			} else {
				String[] arr = FcrdGrad.split("\\|");
				if (arr.length == 2) {
					l170m01e_F.setGrade(LrsUtil.fcrdGrad_desc_to_code(arr[0]));
					l170m01e_F.setScore(Util.parseInt(arr[1]));
				}
			}
		}
	}

	public static L170M01E firstElm(List<L170M01E> list, String flag) {
		flag = (Util.isEmpty(flag) ? "T" : flag);
		if (list != null && list.size() > 0) {
			for (L170M01E e : list) {
				if (Util.equals(flag, e.getTimeFlag())) {
					return e;
				}
			}
			return null;
		} else {
			return null;
		}
	}

	public static String toStr_Guarantor(List<L170M01H> l170m01h_list,
			boolean hasCntrNo) {
		String strG = "";
		String strN = "";
		if (true) {
			TreeMap<String, String> m_g = _debIdDup_nameMap(l170m01h_list,
					UtilConstants.lngeFlag.連帶保證人);
			if (m_g.size() > 0) {
				LinkedHashSet<String> r_g = new LinkedHashSet<String>();
				r_g.addAll(m_g.values());
				strG = "(連帶)" + StringUtils.join(r_g, "、");
			}
		}

		if (true) {
			TreeMap<String, String> m_n = _debIdDup_nameMap(l170m01h_list,
					UtilConstants.lngeFlag.ㄧ般保證人);
			if (m_n.size() > 0) {
				LinkedHashSet<String> r_n = new LinkedHashSet<String>();
				r_n.addAll(m_n.values());
				strN = "(一般)" + StringUtils.join(r_n, "、");
			}
		}

		List<String> r = new ArrayList<String>();
		if (Util.isNotEmpty(strG)) {
			r.add(strG);
		}
		if (Util.isNotEmpty(strN)) {
			r.add(strN);
		}
		if (r.size() > 0) {
			return StringUtils.join(r, " ");
		}

		return hasCntrNo ? "無" : "";
	}

	public static String toStr_Borrower(List<L170M01H> l170m01h_list,
			boolean hasCntrNo) {
		String strC = "";
		if (true) {
			TreeMap<String, String> m_c = _debIdDup_nameMap(l170m01h_list,
					UtilConstants.lngeFlag.共同借款人);
			if (m_c.size() > 0) {
				LinkedHashSet<String> r = new LinkedHashSet<String>();
				r.addAll(m_c.values());
				strC = StringUtils.join(r, "、");
			}
		}

		if (Util.isNotEmpty(strC)) {
			return strC;
		}
		return hasCntrNo ? "無" : "";
	}

	private static TreeMap<String, String> _debIdDup_nameMap(
			List<L170M01H> l170m01h_list, String debType) {
		TreeMap<String, String> r = new TreeMap<String, String>();
		for (L170M01H l170m01h : l170m01h_list) {
			if (Util.equals(debType, l170m01h.getDebType())) {
				r.put(LMSUtil.getCustKey_len10custId(l170m01h.getDebId(),
						l170m01h.getDebDupNo()), Util.trim(l170m01h
						.getCustName()));
			}
		}
		return r;
	}

	private static void _add_notEmpty(List<String> r, String rs) {
		String s = Util.trim(rs);
		if (Util.isNotEmpty(s)) {
			r.add(s);
		}
	}

	public static String get_s01_busCd_bussKind(L170M01A meta) {
		return Util.trim(meta.getBusCd())
				+ (Util.isNotEmpty(meta.getBussKind()) ? "-" : "")
				+ Util.trim(meta.getBussKind());
	}

	public static String get_s01_cesBizInfo(L170M01A meta) {
		List<String> ces_list = new ArrayList<String>();
		boolean showCesBusCd = Util.isNotEmpty(Util.trim(meta.getCesBusCd()));
		boolean showCesCompleteDate = meta.getCesCompleteDate() != null;
		_add_notEmpty(ces_list, meta.getCesTradeType());
		_add_notEmpty(ces_list, showCesBusCd ? "(" + meta.getCesBusCd() + ")"
				: "");
		_add_notEmpty(ces_list, meta.getCesSN());
		_add_notEmpty(
				ces_list,
				showCesCompleteDate ? ("資信簡表完成日期：" + TWNDate.toAD(meta
						.getCesCompleteDate())) : "");
		return StringUtils.join(ces_list, "<br/>");
	}

	public static boolean isCustL170M01E(L170M01E l170m01e) {
		boolean b = Util.equals(l170m01e.getCrdTBR(), M01E_CUST_CRDTBR)
				&& Util.equals(l170m01e.getCntrNo(), M01E_CUST_CNTRNO)
				&& Util.equals(l170m01e.getFinYear(), M01E_CUST_FINYEAR)
				&& LMSUtil.cmpDate(l170m01e.getCrdTYear(), "==",
						M01E_CUST_CRDTYEAR);

		return b;
	}

	public static void initl170m01e(L170M01E l170m01e, L170M01A meta,
			String userId, String timeFlag) {
		l170m01e.setMainId(meta.getMainId());
		l170m01e.setCustId(meta.getCustId());
		l170m01e.setDupNo(meta.getDupNo());
		l170m01e.setCrdTYear(LrsUtil.M01E_CUST_CRDTYEAR);
		l170m01e.setCrdTBR(LrsUtil.M01E_CUST_CRDTBR);
		l170m01e.setCntrNo(LrsUtil.M01E_CUST_CNTRNO);
		l170m01e.setFinYear(LrsUtil.M01E_CUST_FINYEAR);

		l170m01e.setCreator(userId);
		l170m01e.setCreateTime(CapDate.getCurrentTimestamp());
		l170m01e.setUpdater(l170m01e.getCreator());
		l170m01e.setUpdateTime(l170m01e.getCreateTime());

		l170m01e.setTimeFlag(timeFlag);
	}

	public static void clear_l170m01e_C(L170M01E l170m01e_C) {
		l170m01e_C.setCrdType(UtilConstants.crdType.未評等);
		l170m01e_C.setGrade("");
	}

	public static void clear_l170m01e_M(L170M01E l170m01e_M) {
		l170m01e_M.setCrdType(UtilConstants.Casedoc.CrdType2.免辦);
		l170m01e_M.setGrade("");
	}

	public static void clear_l170m01e_ex(L170M01E l170m01e_ex, String kind) {
		if (Util.equals("C", kind)) {
			l170m01e_ex.setCrdType(UtilConstants.Type.無資料_C);
		} else if (Util.equals("M", kind)) {
			l170m01e_ex.setCrdType(UtilConstants.Type.無資料_M);
		} else {

		}
		l170m01e_ex.setGrade("");
	}

	public static String decide_mLoanPerson(String m) {
		return "Y".equals(m) ? "Y" : "N";
	}

	public static String elf493_rptDocId(L180M01A l180m01a) {
		return l180m01a.getMainId();
	}

	public static String chkResult_fmt(L170M01D l170m01d) {

		// 辦理應收帳款承購應注意買方之付款是否有逾期30日以上之情形？【否|是|－】
		/*
		 * if (Util.equals(LrsUtil.N025, l170m01d.getItemNo())) { return
		 * "N|Y|K"; }
		 */

		// 前次覆審有無應行改善事項？ 【無|有|－】
		if (Util.equals(LrsUtil.N017, l170m01d.getItemNo())) {
			return "N2|Y2|K";
		}

		// 擔保物是否發生變化，致影響本行債權？【否|是|－】
		if (Util.equals(LrsUtil.N020, l170m01d.getItemNo())) {
			return "N|Y|K";
		}

		// Z_電腦建檔
		if (Util.equals(Z_電腦建檔資料, l170m01d.getItemType())) {
			if (CrsUtil.inCollection(l170m01d.getItemNo(), ZSYS_TITLE)) {
				return "";
			} else {
				if (Util.equals(LrsUtil.ZB11, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB12, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB13, l170m01d.getItemNo())
						// J-113-0204  新增及修正說明文句
						|| Util.equals(LrsUtil.ZB14, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB15, l170m01d.getItemNo())
						//J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
						|| Util.equals(LrsUtil.ZB30, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB3E, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB3J, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB3S, l170m01d.getItemNo())
						// J-113-0204  新增及修正說明文句
						|| Util.equals(LrsUtil.ZB40, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB50, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB14, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZB15, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZC10, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZC1A, l170m01d.getItemNo())	
						|| Util.equals(LrsUtil.ZC20, l170m01d.getItemNo())	
						|| Util.equals(LrsUtil.ZC3A, l170m01d.getItemNo())	
						|| Util.equals(LrsUtil.ZC3B, l170m01d.getItemNo())	
						|| Util.equals(LrsUtil.ZC3C, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.ZC3D, l170m01d.getItemNo())	
					) {
					return "Y|N|K";
				}
				
				//J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
				if(   Util.equals(LrsUtil.ZB3A, l170m01d.getItemNo()) 
				   || Util.equals(LrsUtil.ZB34, l170m01d.getItemNo()) 
				   || Util.equals(LrsUtil.ZB3N, l170m01d.getItemNo()) 
				   || Util.equals(LrsUtil.ZB3Q, l170m01d.getItemNo()) 
				  ){
					return "Y2|N2";
				}
				
				
				return "Y|N";// 【是|否】
			}
		}

		// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		// Y_履約條件
		if (Util.equals(Y_履約條件, l170m01d.getItemType())) {
			if (CrsUtil.inCollection(l170m01d.getItemNo(), YSYS_TITLE)) {
				return "";
			} else {
				// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
				// if (LrsUtil.compareRptVersion(l170m01a.getRptId(), ">=",
				// LrsUtil.V_20170603)) {
				// J-109-0336 檢視事項及承諾事項之管控機制
				if (Util.equals(LrsUtil.YA12, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.YA13, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.YB11, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.YB12, l170m01d.getItemNo())
						|| Util.equals(LrsUtil.YB13, l170m01d.getItemNo())) {
					return "Y|N|K";
				}
				// }

				return "Y|N";// 【是|否】
			}
		}

		// J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
		// X_土建融
		if (Util.equals(X_土建融, l170m01d.getItemType())) {
			if (CrsUtil.inCollection(l170m01d.getItemNo(), XSYS_TITLE)) {
				return "";
			} else {
				// if (Util.equals(LrsUtil.ZB11, l170m01d.getItemNo())
				// || Util.equals(LrsUtil.ZB12, l170m01d.getItemNo())
				// || Util.equals(LrsUtil.ZB13, l170m01d.getItemNo())) {
				// return "Y|N|K";
				// }
				return "Y|N|K";// 【是|否|－】
			}
		}

		if (true) {
			// 董事會或常董會權限
			if (Util.equals(LrsUtil.B007, l170m01d.getItemNo())
					|| Util.equals(LrsUtil.B009, l170m01d.getItemNo())
					|| Util.equals(LrsUtil.B010, l170m01d.getItemNo())
					|| Util.equals(LrsUtil.B013_ref_N020, l170m01d.getItemNo())
					|| Util.equals(LrsUtil.B016, l170m01d.getItemNo())) {
				return "N|Y|K";
			}
			// 前次覆審有無應行改善事項？ 【無|有|－】
			if (Util.equals(LrsUtil.B015_N017, l170m01d.getItemNo())) {
				return "N2|Y2|K";
			}
		}

		// default【是|否|－】
		return "Y|N|K";
	}

	public static String getPrintItemContent(L170M01D l170m01d,
			Properties prop_lms1700m01) {
		String itemContent = Util.trim(l170m01d.getItemContent());
		if (Util.equals(LrsUtil.N002, l170m01d.getItemNo())
				|| Util.equals(LrsUtil.B003_ref_N002, l170m01d.getItemNo())) {
			// 在前端的介面 [ ]無支票存款
			// 未勾選，印出(支票存款)
			// 若勾選，印出(無支票存款)
			itemContent = StringUtils.replace(
					itemContent,
					L170M01D_RPLC,
					(Util.equals("N", l170m01d.getChkCheck()) ? prop_lms1700m01
							.getProperty("label.N2") : ""));

		}
		return itemContent;
	}

	public static void setDefaultRatio(L170M01C l170m01c) {
		l170m01c.setRatioNo1("20");
		l170m01c.setRatioNo2("11");
		l170m01c.setRatioNo3("12");
		l170m01c.setRatioNo4("22");
	}

	public static String lms180R19Name(String rptName, Date batch_dataDate,
			Map<String, String> brInfoMap) {
		return rptName + "(" + Util.trim(TWNDate.toAD(batch_dataDate)) + ")共 "
				+ brInfoMap.size() + " 間分行"
				+ show_firstN_elm(brInfoMap.keySet(), 2, "(", ")");
	}

	public static String lms180R20Name(String rptName,
			Map<String, String> brInfoMap) {
		return rptName + "共 " + brInfoMap.size() + " 間分行"
				+ show_firstN_elm(brInfoMap.keySet(), 2, "(", ")");
	}

	public static String lms180R21Name(String rptName, Date startDate,
			Date endDate, Map<String, String> brInfoMap) {
		return rptName
				+ "("
				+ StringUtils.substring(Util.trim(TWNDate.toAD(startDate)), 0,
						7) + "~"
				+ StringUtils.substring(Util.trim(TWNDate.toAD(endDate)), 0, 7)
				+ ")共 " + brInfoMap.size() + " 間分行"
				+ show_firstN_elm(brInfoMap.keySet(), 2, "(", ")");
	}

	public static String lms180R22Name(String rptName,
			Map<String, String> brInfoMap) {
		return rptName + "共 " + brInfoMap.size() + " 間分行"
				+ show_firstN_elm(brInfoMap.keySet(), 2, "(", ")");
	}

	public static String lms180R23Name(String rptName, Date startDate,
			Date endDate) {
		return rptName
				+ "("
				+ StringUtils.substring(Util.trim(TWNDate.toAD(startDate)), 0,
						7) + "~"
				+ StringUtils.substring(Util.trim(TWNDate.toAD(endDate)), 0, 7)
				+ ")";
	}

	public static String lms180R24Name(String rptName, Date startDate,
			Date endDate) {
		return rptName
				+ "("
				+ StringUtils.substring(Util.trim(TWNDate.toAD(startDate)), 0,
						7) + "~"
				+ StringUtils.substring(Util.trim(TWNDate.toAD(endDate)), 0, 7)
				+ ")";
	}

	public static String lms180R28Name(String rptName, Date startDate,
			Date endDate) {
		return rptName
				+ "("
				+ StringUtils.substring(Util.trim(TWNDate.toAD(startDate)), 0,
						7) + "~"
				+ StringUtils.substring(Util.trim(TWNDate.toAD(endDate)), 0, 7)
				+ ")";
	}

	public static String show_firstN_elm(Set<String> set, int maxCnt,
			String prefix, String postfix) {
		if (set == null || set.size() == 0) {
			return "";
		}

		int _cnt = 0;
		List<String> list = new ArrayList<String>();
		for (String brNo : set) {
			if (_cnt >= maxCnt) {
				list.add("...");
				break;
			}
			list.add(brNo);
			_cnt++;
		}
		return prefix + StringUtils.join(list, ",") + postfix;
	}

	public static boolean isL170M01B_Subject_W(L170M01B l170m01b) {
		if (l170m01b.getLnDataDate() != null) {
			// SYS
			if (Util.isEmpty(Util.trim(l170m01b.getLoanTP()))) {
				return true;
			} else {
				return false;
			}
		} else {
			// PEO
			return true;
		}
	}

	public static boolean isFromNotes(L170M01A meta) {
		String rptId = Util.trim(meta.getRptId());
		if (rptId.startsWith("V") && rptId.length() < 32) {
			return false;
		}
		return true;
	}

	public static boolean compareRptVersion(String chkVersion, String sign,
			String baseVersion) {

		String chkVersion1 = "";
		String baseVersion1 = "";

		if (Util.notEquals(Util.trim(chkVersion), "")) {
			if (Util.equals(Util.trim(Util.getLeftStr(chkVersion, 3)), "Ver")) {
				String tChkVersion = Util.getLeftStr(Util.trim(chkVersion)
						.replaceAll("Ver", "") + "00000000", 8);
				if (Util.isNumeric(tChkVersion)) {
					chkVersion1 = tChkVersion;
				}
			}
		}

		if (Util.notEquals(Util.trim(baseVersion), "")) {
			if (Util.equals(Util.trim(Util.getLeftStr(baseVersion, 3)), "Ver")) {
				String tBaseVersion = Util.getLeftStr(Util.trim(baseVersion)
						.replaceAll("Ver", "") + "00000000", 8);
				if (Util.isNumeric(tBaseVersion)) {
					baseVersion1 = tBaseVersion;
				}
			}
		}

		if (Util.equals(chkVersion1, "") || Util.equals(baseVersion1, "")) {
			return false;
		}
		int a = Integer.parseInt(chkVersion1);
		int b = Integer.parseInt(baseVersion1);
		if (sign.equals("<")) {
			return (a < b);
		} else if (sign.equals("<=")) {
			return (a <= b);
		} else if (sign.equals("==")) {
			return (a == b);
		} else if (sign.equals(">=")) {
			return (a >= b);
		} else if (sign.equals(">")) {
			return (a > b);
		}

		return false;

	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	public static void elf412b_null_to_zeroDate(ELF412B elf412b) {
		if (elf412b.getElf412b_llrDate() == null) {
			elf412b.setElf412b_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_lrDate() == null) {
			elf412b.setElf412b_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}

		if (elf412b.getElf412b_nckdDate() == null) {
			elf412b.setElf412b_nckdDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_cancelDt() == null) {
			elf412b.setElf412b_cancelDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_uckdDt() == null) {
			elf412b.setElf412b_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_nextNwDt() == null) {
			elf412b.setElf412b_nextNwDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_nextLtDt() == null) {
			elf412b.setElf412b_nextLtDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_oldRptDt() == null) {
			elf412b.setElf412b_oldRptDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_oldDraDt() == null) {
			elf412b.setElf412b_oldDraDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_newRptDt() == null) {
			elf412b.setElf412b_newRptDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412b.getElf412b_newDraDt() == null) {
			elf412b.setElf412b_newDraDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}

	}

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	public static void elf412c_null_to_zeroDate(ELF412C elf412c) {
		if (elf412c.getElf412c_llrDate() == null) {
			elf412c.setElf412c_llrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412c.getElf412c_lrDate() == null) {
			elf412c.setElf412c_lrDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}

		if (elf412c.getElf412c_nckdDate() == null) {
			elf412c.setElf412c_nckdDate(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412c.getElf412c_cancelDt() == null) {
			elf412c.setElf412c_cancelDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412c.getElf412c_uckdDt() == null) {
			elf412c.setElf412c_uckdDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412c.getElf412c_nextNwDt() == null) {
			elf412c.setElf412c_nextNwDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}
		if (elf412c.getElf412c_nextLtDt() == null) {
			elf412c.setElf412c_nextLtDt(CapDate.parseDate(CapDate.ZERO_DATE));
		}

	}

	// J-108-0078_05097_B1001
	// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
	public static String getNewAddRckdLineForElf412(String isAllNew, String brNo) {

		if (Util.equals(isAllNew, "Y")) {
			if (Util.equals(brNo, "079")) {
				return "C";
			} else {
				return "I";
			}
		} else {
			return "C";
		}

	}

	// J-110-0018_05097_B1001 Web
	// e-Loan簽報書額度明細表中增列「兆豐百億挺你專案」及「協助農地工廠合法化融資貸款」兩項專案，並產生統計報表
	public static String lms180R61Name(String rptName, String projClass) {
		return rptName + "(" + projClass + ")";
	}

	// J-110-0308 覆審考核表
	public static final String[] l170m01jCol = { "paItem01", "paItem02", "paItem03", "paItem04",
			"paItem05", "paItem06", "paItem07", "paItem08" };
	public static final String[] l170m01jColYn = { "paItem01", "paItem02", "paItem06", "paItem07", "paItem08" };
	public static final String[] l170m01jColCnt = { "paItem03", "paItem04", "paItem05" };
	public static final String[] l170m01jColScore = { "-0.5", "-1", "-0.5", "-1",
		"-0.5", "-0.5", "-1", "-1" };
	// 覆審考核表改版
	public static final String[] l170m01jCol_Ver20230701 = { "paItem01", "paItem02", "paItem03", "paItem04",
			"paItem05", "paItem06", "paItem07", "paItem08", "paItem09" };
	public static final String[] l170m01jColYn_Ver20230701 = { "paItem01", "paItem06", "paItem07", "paItem08", "paItem09" };
	public static final String[] l170m01jColCnt_Ver20230701 = { "paItem02","paItem03", "paItem04", "paItem05" };
	public static final String[] l170m01jColScore_Ver20230701 = { "-0.5", "-1.5", "-0.5", "-1",
		"-1.5", "-0.5", "-2", "-1", "-1" };
	public static void initL170m01j(List<L170M01J> l170m01jList, L170M01A meta, String userId, String verStr) {
		String[] Col = l170m01jCol;
		String[] ColYn = l170m01jColYn;
		String[] ColCnt = l170m01jColCnt;

		if(Util.equals(verStr, PAVER_20230701)){
			Col = l170m01jCol_Ver20230701;
			ColYn = l170m01jColYn_Ver20230701;
			ColCnt = l170m01jColCnt_Ver20230701;
		}

		for (String fieldName : Col) {
			String itemName = fieldName;
			L170M01J l170m01j = new L170M01J();
			l170m01j.setMainId(meta.getMainId());
			l170m01j.setItemName(itemName);
			String itemType = null;
			if(ArrayUtils.contains(ColYn, itemName)){
				itemType = "YN";
			} else if(ArrayUtils.contains(ColCnt, itemName)){
				itemType = "CNT";
			}
			l170m01j.setItemType(itemType);
			l170m01j.setDscr("");
			l170m01j.setItemYn(null);
			l170m01j.setItemCnt(null);
			l170m01j.setCreator(userId);
			l170m01j.setCreateTime(CapDate.getCurrentTimestamp());
			l170m01jList.add(l170m01j);
		}
	}

}
