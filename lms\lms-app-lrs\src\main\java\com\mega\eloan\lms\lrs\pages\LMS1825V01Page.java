/* 
 * LMS1825V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;

/**<pre>
 * 預約產生覆審名單-未處理
 * </pre>
 * @since  2011/9/19
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/19,irene,new
 *          </ul>
 */
@Controller
@RequestMapping("/lrs/lms1825v01")
public class LMS1825V01Page extends AbstractEloanInnerView {

	public LMS1825V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(RetrialDocStatusEnum.預約單_未處理);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Delete,
				LmsButtonEnum.View);
		renderJsI18N(LMS1825V01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript","loadScript('pagejs/lrs/LMS1825V01Page');");
	}// ;
}
