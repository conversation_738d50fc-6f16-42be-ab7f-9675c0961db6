var pageAction = {	
	grid_height: 200,
	build : function(){

        $.ajax({
            handler: "cls9071formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "loadData"
            },
            success: function(json){
            	var _addSpace = true;
				$.each(json.itemOrder, function(idx, k) {
            		var currobj = {};
            		currobj[k] = json.item[k];
            		
            		//select
					$("#selectCust").setItems({ item: currobj, format: "{value} {key}", clear:false, space: (_addSpace?(idx==0):false) });
				});
            }
        });
        
		if($("#pteamappGrid").length>0){
	        
        	$("#selectCust").change(function(){
   
        		$("#pteamappGrid").setGridParam({
                    postData: {
                    	'idDup': $("#selectCust").val()
                    },
                    search: true
                }).trigger("reloadGrid");
            });
            
        	$("#pteamappGrid").iGrid({
                handler: 'cls9071gridhandler',                
                height: pageAction.grid_height,                
                shrinkToFit: false,
    	        multiselect: true, 
    			multiselect : false,
    	        needPager: false,
                postData: {
                    'formAction': 'queryGrpCntrNo'
                    ,'idDup': $("#selectCust").val()
                },
                colModel: [{                   
                    colHeader: i18n.cls9071v01["PTEAMAPP.YEAR"], //年度
                    name: 'year',
                    align: "left",
                    width: 30,
                    sortable: false
                }, {
                	 colHeader: i18n.cls9071v01["PTEAMAPP.PROJECTNM"], //團貸戶戶名
                     name: 'projectnm',
                     align: "left",
                     width: 120,
                     hidden: true,
                     sortable: false
                }, {
                    colHeader: i18n.cls9071v01["PTEAMAPP.GRPCNTRNO"], //團貸編號
                    align: "left",
                    width: 100,
                    name: 'grpcntrno',                    
                    sortable: false, 
                    formatter: 'click', onclick: gridClickLNDetail
                }, {
                    colHeader: i18n.cls9071v01["issuebrno_name"],//"簽案行",
                    name: 'issuebrno_name',
                    width: 100,
                    sortable: false,
                    align: "left"
                }, {
                    colHeader: i18n.cls9071v01["PTEAMAPP.PROJECTNM"],//"案名",
                    name: 'projectnm',
                    width: 260,
                    sortable: false,
                    align: "left"
                }, {
                    colHeader: i18n.cls9071v01["PTEAMAPP.EFFFROM"], //總額度有效起日
                    align: "center",
                    width: 100, //設定寬度
                    sortable: false, //是否允許排序
                    name: 'efffrom' //col.id
                }, {
                    colHeader: i18n.cls9071v01["PTEAMAPP.EFFEND"], //總額度有效迄日
                    align: "center",
                    width: 100, //設定寬度
                    sortable: false, //是否允許排序
                    name: 'effend' //col.id
                }],
                ondblClickRow: function(rowid){
    	        	gridClickLNDetail(null, null, $("#pteamappGrid").getRowData(rowid));
                }
        	});    
        }
		
		$("#inputThickBox").thickbox({
			title : i18n.cls9071v01["inputTitle"],
			width : 780,
			height : pageAction.grid_height + 180,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					var id = $("#pteamappGrid").getGridParam('selrow');
			        if (!id) {
			            // action_004=請先選擇需「調閱」之資料列
			            return CommonAPI.showMessage(i18n.def["action_004"]);
			        }
			        var result = $("#pteamappGrid").getRowData(id);
			        gridClickLNDetail(null, null, result);
			        
				},
				'close' : function(){	
					$.thickbox.close();
				}
			}
		});
		
		function gridClickLNDetail(cellvalue, options, rowObject){
			var grpcntrno = rowObject.grpcntrno;
			$.thickbox.close();
			//==============
			
	
			//不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
            $.form.submit({
           	url: __ajaxHandler,
        		target : "_blank",
        		data : {
        			_pa : 'lmsdownloadformhandler',
        			'grpcntrno': grpcntrno,
        			'fileDownloadName' : grpcntrno+"_data.xls",
        			'serviceName' : "cls9071r01rptservice"
        		}
        	 });
		}
	}
}

$(function() {
	pageAction.build();
});