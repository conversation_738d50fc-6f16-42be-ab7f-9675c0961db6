/* 
 * LMS1815V04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;


/**<pre>
 * 授信簽報書 海外_總行提會待登錄
 * 特殊分行提常董會
 * </pre>
 * @since  2011/10/19
 * <AUTHOR> Lin
 * @version <ul>
 *           <li>2011/10/19,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1141v25")
public class CLS1141V25Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		//設定文件狀態(交易代碼)
		if(LMSUtil.isSpecialBranch(unitNo)){
			setGridViewStatus(CreditDocStatusEnum.特殊分行提常董會);
		}else{
			setGridViewStatus(CreditDocStatusEnum.海外_提會待登錄);
		}
		if (this.getAuth(AuthType.Accept)) {
			// 主管權限時要顯示的按鈕...
			addToButtonPanel(model, LmsButtonEnum.View);
		} else {
			// 否則需要顯示的按鈕
			// 加上Button
			// 經辦權限時要顯示的按鈕...
//			add(new CreditButtonPanel("_buttonPanel", null,CreditButtonEnum.View));
			addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Login3, LmsButtonEnum.Create, LmsButtonEnum.CreDoc1);
		}		
		//套用哪個i18N檔案
		renderJsI18N(CLS1141V01Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1141V01Page');");
	}// ;

}
