/* 
 *CLS9041M04ServiceImp.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.fms.service.CLS9041M04Service;
import com.mega.eloan.lms.mfaloan.service.impl.AbstractMFAloanJdbc;

/**
 * <pre>
 * 優惠房貸額度維護 - 總額度維護作業
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
/* Use MIS-RDB */
@Service
public class CLS9041M04ServiceImpl extends AbstractMFAloanJdbc implements
		CLS9041M04Service {

	@Override
	public List<Map<String, Object>> findMap(int year, int month) {
		String dateString = year + "-" + month + "%";// 搜尋該月份資料
		
		return getJdbc().queryForList("MIS.LNF022.SEARCH",
				new String[] { dateString });
	}

}
