
var pageAction = {
    handler: 'cls9041m06formhandler',
    grid: null,
    gridfile: null,//上傳檔案用grid
    build: function(){
        pageAction.grid = $("#gridview").iGrid({
            // localFirst: true,
            handler: 'cls9041m06gridhandler',
            height: 400,
            action: "query",
            rowNum: 15,
            rownumbers: true,
            sortname: "bgnDate|createTime",
	        sortorder: "desc|desc",
            // multiselect : true,
            colModel: [{
                colHeader: "oid",
                name: 'oid',
                hidden: true
                // 是否隱藏
            }, {
                colHeader: i18n.cls9041m06["C004M01A.mainId"], // mainId
                hidden: true,// 是否隱藏
                name: 'mainId' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.unid"], // unid
                hidden: true,// 是否隱藏
                name: 'unid' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.rptType"], // 報表類別
                hidden: true,// 是否隱藏
                align: "left",
                width: 3, // 設定寬度
                sortable: true, // 是否允許排序
                // formatter : 'click',
                // onclick : function,
                name: 'rptType' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.bgnDate"], // 資料起日
                hidden: true,// 是否隱藏
                name: 'bgnDate' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.endDate"], // 資料迄日
                hidden: true,// 是否隱藏
                name: 'endDate' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.rptDate"], // 確定報送日期
                hidden: true,// 是否隱藏
                name: 'rptDate' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.rptName"], // 報表名稱
                align: "left",
                width: 6, // 設定寬度
                sortable: true, // 是否允許排序
                // formatter : 'click',
                // onclick : alert(filePath),
                name: 'rptName' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.rmk"], // 備註
                hidden: true,// 是否隱藏
                name: 'rmk' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.creator"], // 建立人員號碼
                align: "left",
                width: 3, // 設定寬度
                sortable: true, // 是否允許排序
                // formatter : 'click',
                // onclick : function,
                name: 'creator' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.createTime"], // 建立日期
                align: "center",
                width: 3, // 設定寬度
                sortable: true, // 是否允許排序
                // formatter : 'click',
                // onclick : function,
                name: 'createTime' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.updater"], // 異動人員號碼
                hidden: true,// 是否隱藏
                name: 'updater' // col.id
            }, {
                colHeader: i18n.cls9041m06["C004M01A.updateTime"], // 異動日期
                hidden: true,// 是否隱藏
                name: 'updateTime' // col.id
            }],
            ondblClickRow: function(rowid){
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openDetail(data);
            }
        });
        //上傳檔案grid
        pageAction.gridfile = $("div#fileGrid").iGrid({
            handler: 'cls9041m06gridhandler',
            height: 110,
            action: 'queryfile',
            rowNum: 3,
            //multiselect: true,
            colModel: [{
                colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
                name: 'srcFileName',
                width: 120,
                align: "left",
                sortable: true,
                formatter: 'click',
                onclick: pageAction.download
            }, {
                colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
                name: 'fileDesc',
                width: 140,
                align: "center",
                sortable: true
            }, {
                colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
                name: 'uploadTime',
                hidden: true
                /*width: 140,
                 align: "center",
                 sortable: true*/
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainid',
                hidden: true
            }]
        });
        //build buttons
        //篩選
        $("#buttonPanel").find("#btnFilter").click(function(){
            $("#filter").thickbox({
                title: '',
                width: 400,
                height: 200,
                modal: true,
                align: 'center',
                valign: 'bottom',
                i18n: i18n.def,
                buttons: {
                    'sure': function(){
                        if ($('#filterForm').valid()) {
                            pageAction.grid.jqGrid("setGridParam", {
                                postData: {
                                    creator: $("#creatorFilter").val(),
                                    bgnDate: $("#bgnDateFilter").val(),
                                    endDate: $("#endDateFilter").val()
                                },
                                page: 1,
                                search: true
                            }).trigger("reloadGrid");
							$.thickbox.close();
                        }
                    },
                    'close': function(){
                        $.thickbox.close();
                    }
                }
            });
        }) //新增
.end().find("#btnAdd").click(function(){
            MegaApi.confirmMessage(i18n.cls9041m06["thickbox.addMsg"], function(action){
                if (action) {
                    $.ajax({
                        handler: pageAction.handler,
                        action: 'addC004M01A',
                        success: function(responseData){
                            pageAction.reloadGrid();
                            MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["addSuccess"]);
                        }
                    });
                }
            });
        }) //調閱
.end().find("#btnView").click(function(){
            var data = pageAction.getRowData();
            if (data) {
                pageAction.openDetail(data);
            }
        }) //刪除
.end().find("#btnDelete").click(function(){
            var data = pageAction.getRowData();
            if (data) {
                MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                    if (action) {
                        $.ajax({
                            handler: pageAction.handler,
                            action: 'deleteC004M01A',
                            data: data,
                            success: function(responseData){
                                pageAction.reloadGrid();
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                            }
                        });
                    }
                });
            }
        });
		 //上傳檔案按鈕
        $("#uploadFile").click(function(){
            if (pageAction.gridfile.jqGrid('getDataIDs').length >= 2) {//如grid已有資料=>無法insert
                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.cls9041m06["canotInsert"]);
                return;
            }
			var data = pageAction.grid.getSingleData();
            var limitFileSize = 3145728;
            MegaApi.uploadDialog({
                fieldId: "fms",
                fieldIdHtml: "size='30'",
                fileDescId: "fileDesc",
                fileDescHtml: "size='30' maxlength='30'",
                subTitle: i18n.def('insertfileSize', {
                    'fileSize': (limitFileSize / 1048576).toFixed(2)
                }),
                limitSize: limitFileSize,
                width: 320,
                height: 190,
                data: {
                    mainId: data.mainId,
                    sysId: "LMS"
                },
                success: function(){
                    pageAction.gridfile.trigger("reloadGrid");
                }
            });
        });
		$("#deleteFile").click(function(){
            var row = pageAction.gridfile.getGridParam('selrow');
            if (row.length) {
                var rowData = pageAction.gridfile.getRowData(row);
                MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                    if (action) {
                        $.ajax({
                            handler: pageAction.handler,
                            action: 'deleteFile',
                            data: {
                                oid: rowData.oid
                            },
                            success: function(responseData){
                                pageAction.gridfile.trigger("reloadGrid");
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                            }
                        });
                    }
                });
            }
            else {
                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
            }
        });
    },
    /**
     * 開啟detail
     */
    openDetail: function(data){
    
       
        
        //套進數字
        var form = $("#detailForm");
        form.find("#createTime").val(data.createTime);
        form.find("#bgnDate").val(data.bgnDate);
        form.find("#endDate").val(data.endDate);
        form.find("#creator").val(data.creator);
        form.find("#rptName").val(data.rptName);
        form.find("#rptType").val(data.rptType);
        form.find("#oid").val(data.oid);
        form.find("#mainId").val(data.mainId);
        
        //檔案IO部分
        pageAction.gridfile.jqGrid("setGridParam", {
            postData: data,
            page: 1,
            search: true
        }).trigger("reloadGrid");
        //開視窗
        $("#detail").thickbox({
            title: i18n.cls9041m06["detailTitle"],
            width: 600,
            height: 400,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.cls9041m06,
            buttons: { 
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var row = pageAction.grid.getGridParam('selrow');
        var data;
        if (row) {
            data = pageAction.grid.getRowData(row);
        }
        else {
            MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
        }
        return data;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        }
        else {
            pageAction.grid.trigger('reloadGrid');
        }
    },
    /**
     * 檔案下載
     */
    download: function(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }
}
$(document).ready(function(){
    pageAction.build();
});
