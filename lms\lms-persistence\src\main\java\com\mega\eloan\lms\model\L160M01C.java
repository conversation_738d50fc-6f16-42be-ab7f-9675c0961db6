/* 
 * L160M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 動審表查核項目資料 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L160M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "itemType", "itemSeq" }))
public class L160M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 查核項目類別
	 * <p/>
	 * 1全行共同項目<br/>
	 * 2當地特殊規定項目<br/>
	 * 3自行輸入項目(預設提供6組) * <br/>
	 * 4.國內企金(總行維護) <br/>
	 */
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/** 序號 **/
	@Column(name = "ITEMSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer itemSeq;

	/**
	 * 查核項目
	 * <p/>
	 * 64個全型字 <br/>
	 * 102.02.18 欄位擴大 192 -> 900
	 */
	@Column(name = "ITEMCONTENT", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String itemContent;

	/**
	 * 查核情形
	 * <p/>
	 * 0免附<br/>
	 * 1已收到<br/>
	 * 2未收到
	 */
	@Column(name = "ITEMCHECK", length = 1, columnDefinition = "CHAR(1)")
	private String itemCheck;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 10, columnDefinition = "VARCHAR(10)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 10, columnDefinition = "VARCHAR(10)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/** 輸入欄位1 **/
	@Column(name = "ITEMFIELD1", length = 300, columnDefinition = "VARCHAR(300)")
	private String itemField1;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得查核項目類別
	 * <p/>
	 * 1全行共同項目<br/>
	 * 2當地特殊規定項目<br/>
	 * 3自行輸入項目(預設提供6組) * <br/>
	 * 4.國內企金(總行維護) <br/>
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定查核項目類別
	 * <p/>
	 * 1全行共同項目<br/>
	 * 2當地特殊規定項目<br/>
	 * 3自行輸入項目(預設提供6組) * <br/>
	 * 4.國內企金(總行維護) <br/>
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 取得序號 **/
	public Integer getItemSeq() {
		return this.itemSeq;
	}

	/** 設定序號 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/**
	 * 取得查核項目
	 * <p/>
	 * 64個全型字
	 */
	public String getItemContent() {
		return this.itemContent;
	}

	/**
	 * 設定查核項目
	 * <p/>
	 * 64個全型字
	 **/
	public void setItemContent(String value) {
		this.itemContent = value;
	}

	/**
	 * 取得查核情形
	 * <p/>
	 * 0免附<br/>
	 * 1已收到<br/>
	 * 2未收到
	 */
	public String getItemCheck() {
		return this.itemCheck;
	}

	/**
	 * 設定查核情形
	 * <p/>
	 * 0免附<br/>
	 * 1已收到<br/>
	 * 2未收到
	 **/
	public void setItemCheck(String value) {
		this.itemCheck = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 設定輸入欄位1 **/
	public void setItemField1(String itemField1) {
		this.itemField1 = itemField1;
	}

	/** 取得輸入欄位1 **/
	public String getItemField1() {
		return itemField1;
	}
}
