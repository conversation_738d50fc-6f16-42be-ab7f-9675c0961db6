package com.mega.eloan.lms.cls.handler.form;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.cls.pages.CLS3401M07Page;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上對保契約書 => 對應 ContractDocConstants.C340M01A_CtrType.Type_C 中鋼集團消貸線上契約 
 * </pre>
 * 
 * @since 2021/11/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/11/26,EL08034,J-110-0373 中鋼消貸線上申請暨對保作業
 *          </ul>
 */
@Scope("request")
@Controller("cls3401m07formhandler")
@DomainClass(C340M01A.class)
public class CLS3401M07FormHandler extends AbstractFormHandler {
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
	
	@Resource
	BranchService branchService;

	@Resource
	CLSService clsService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	TempDataService tempDataService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLS3401Service cls3401Service;

	@Resource
	ContractDocService contractDocService;
	
	@Resource
	DwdbBASEService dwdbBASEService;

	Properties prop_cls3401m07 = MessageBundleScriptCreator.getComponentResource(CLS3401M07Page.class);
	Properties prop_abstractEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);

	
	@DomainAuth(value = AuthType.Query)
	public IResult queryC340M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C340M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC340M01A_oid(mainOid);

			String page = params.getString(EloanConstants.PAGE);

			if ("01".equals(page)) {
				//~~~~~~~~~~~~~~
				LMSUtil.addMetaToResult(result, meta,
						new String[] { "custId", "dupNo", "custName", "caseNo", "contrNumber", "contrPartyNm", "ploanCtrNo"
							, "ploanCtrExprDate", "ploanCtrSignTimeM", "ploanCtrSignTime1"
							, "ploanCtrBegDate", "ploanCtrEndDate", "ploanBorrowerIPAddr", "ploanStakeholderIPAddr"});
				String ownBrId = meta.getOwnBrId();
				result.set("ownBrId", StrUtils.concat(ownBrId, " ", branchService.getBranchName(ownBrId)));
				result.set("docStatus", getMessage("docStatus." + meta.getDocStatus()));
				result.set("ploanCtrStatus", getMessage("ploanCtrStatus." + Util.trim(meta.getPloanCtrStatus())));
				result.set("typCd", getMessage("typCd." + meta.getTypCd()));
				result.set("ctrTypeMapDesc", get_ctrType_desc(meta.getCtrType()));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime", Util.trim(TWNDate.valueOf(meta.getUpdateTime())));				
				result.set("ploanCtrDcTime", meta.getPloanCtrDcTime()==null?"":S_FORMAT.format(meta.getPloanCtrDcTime()));
				result.set("ploanCtrDcUser", _id_name(meta.getPloanCtrDcUser()));
				result.set("ploanCtrBegDateToEndDate", Util.trim(TWNDate.toAD(meta.getPloanCtrBegDate()))
						+(meta.getPloanCtrBegDate()!=null&&meta.getPloanCtrEndDate()!=null?" ~ ":"")
						+Util.trim(TWNDate.toAD(meta.getPloanCtrEndDate())));
				String cntrNo = "";
				String loanAmt = "";
				String loanPeriod = "";
				String stkhQueryEJTs = "";
				String stkhBank33 = "";
				String stkhFh44 = "";
				String stkhFh45 = "";
				String stkhRelFg = "";
				String stkhCoFg = "";
				//==========
				C160S01D c160s01d = null;
				C122M01A c122m01a = null;
				if(Util.isNotEmpty(meta.getC160s01d_oid())){
					c160s01d = clsService.findC160S01D_oid(meta.getC160s01d_oid());
				}
				if(c160s01d!=null){
					cntrNo = c160s01d.getCntrNo();
					loanAmt = NumConverter.addComma(LMSUtil.pretty_numStr(c160s01d.getLoanTotAmt()));
					loanPeriod = Util.trim(c160s01d.getMonth());
					//~~~~~~
					String c122m01a_mainId = c160s01d.getC122m01a_mainId();
					if(Util.isNotEmpty(c122m01a_mainId)){
						c122m01a = clsService.findC122M01A_mainId(c122m01a_mainId);
					}					
				}
				if(c122m01a!=null){
					stkhQueryEJTs = c122m01a.getStkhQueryEJTs()==null?"":S_FORMAT.format(c122m01a.getStkhQueryEJTs());
					stkhBank33 = Util.trim(c122m01a.getStkhBank33());
					stkhFh44 = Util.trim(c122m01a.getStkhFh44());
					stkhFh45 = Util.trim(c122m01a.getStkhFh45());
					stkhRelFg = Util.trim(c122m01a.getStkhRelFg());
					stkhCoFg = Util.trim(c122m01a.getStkhCoFg());					
				}
				Map<String, String> c122m01a_stkhQueryResult_map = c122m01a_stkhQueryResult_map();
				result.set("cntrNo", cntrNo);
				result.set("loanAmt", loanAmt);
				result.set("loanPeriod", loanPeriod);
				result.set("stkhQueryEJTs", stkhQueryEJTs);
				result.set("stkhBank33", LMSUtil.getDesc(c122m01a_stkhQueryResult_map, stkhBank33));
				result.set("stkhFh44", LMSUtil.getDesc(c122m01a_stkhQueryResult_map, stkhFh44));
				result.set("stkhFh45", LMSUtil.getDesc(c122m01a_stkhQueryResult_map, stkhFh45));
				result.set("stkhRelFg", LMSUtil.getDesc(c122m01a_stkhQueryResult_map, stkhRelFg));
				result.set("stkhCoFg", LMSUtil.getDesc(c122m01a_stkhQueryResult_map, stkhCoFg));
				result.set("rateDesc", get_rateDesc(c122m01a));
			}
		}

		//在 defaultResult(...) 去讀  c340m01c
		return defaultResult(params, meta, result);
	}

	private Map<String, String> c122m01a_stkhQueryResult_map(){
		Map<String, String> r = new HashMap<String, String>();
		r.put("1", prop_cls3401m07.getProperty("label.C122M01A.stkhQueryResult.1"));
		r.put("0", prop_cls3401m07.getProperty("label.C122M01A.stkhQueryResult.0"));
		return r;
	} 
	
	private String get_rateDesc(C122M01A c122m01a){
		if(c122m01a!=null){
			Map<String, String> ctrParam_from_C122M01A = ClsUtility.get_ctrParam_from_C122M01A(c122m01a);
			String advancedBaseRate = MapUtils.getString(ctrParam_from_C122M01A, "advancedBaseRate");
			String advancedPmRate = MapUtils.getString(ctrParam_from_C122M01A, "advancedPmRate");
			String advancedNowRate = MapUtils.getString(ctrParam_from_C122M01A, "advancedNowRate");
			return "依撥款日乙方公告之消費金融放款指標利率"+advancedBaseRate+"%加計年利率"+advancedPmRate+"%浮動計息，目前合計為年利率"+advancedNowRate+"%";
		}		
		return ""; 
	}
	private String _id_name(String raw_id) {
		String id = Util.trim(raw_id);
		return Util.trim(id + " " + Util.trim(userInfoService.getUserName(id)));
	}

	private String get_ctrType_desc(String ctrType) {
		if (Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_C)) {
			return prop_cls3401m07.getProperty("C340M01A.ctrType.C");
		}
		return "";
	}

	@DomainAuth(value = AuthType.Query)
	public IResult ploan_getJarVersion_log4j(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult(contractDocService.ploan_getInfo("getJarVersion", "log4j", "", "", ""));
		return result;
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C340M01A meta,
			CapAjaxFormResult result) throws CapException {
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("custId", meta.getCustId());
		result.set("ctrType", meta.getCtrType());
		result.set("custInfo",
				Util.trim(meta.getCustId()) + " " + Util.trim(meta.getDupNo()) + " " + Util.trim(meta.getCustName()));
		result.set("ctrTypeHeaderDesc", get_ctrType_desc(meta.getCtrType()));
		return result;
	}

}
