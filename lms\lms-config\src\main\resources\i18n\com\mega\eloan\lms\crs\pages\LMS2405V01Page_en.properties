C240M01A.expectedRetrialDate=Scheduled Credit Review Date
C240M01A.reQuantity=Number Of Reviews
C240M01A.dataEndDate=Credit Review Listing Effective As At
C240M01A.updater=Latest Personnel Change
C240M01A.updateTime=Date OF Last Change

produceList=Add Credit Review Worksheet
branchAndDataDate=Select Branch And Year/Month Of Data
dataDate=Year/Month Of Data
branchId=Branch ID

list=Credit Review Worksheet
preCTLList=Scheduled Credit Review Listing
chkList=Credit Review Validation Listing
ejcicList=Ejcic List
chooseExcel=Please select the Excel file to generate
oneBank=Single Branch
allProduce=Batch Generate
chooseBank=Please select branch
C240M01A.allDay=Update Data Year/Month
noDataDate=Year and month of data not specified
filterCondition=Search criteria
filterCustIdSearch=Inquiry by Customer ID
C240M01A.custId=Customer ID


thickbox.show00=Customer
thickbox.show01=Last Review Date
thickbox.show02=Next Review Date
thickbox.show03=Reason Code For Non-review
thickbox.show04=Remark Date For Non-review
thickbox.show05=Non-review Remarks
thickbox.show06=Credit Limit Serial Number
thickbox.show07=Account Cancellation Date
thickbox.show08=No data found in Credit Review Control File
thickbox.show09=No data found in Accounts file
thickbox.show10=Credit Review Control File
thickbox.show11=Accounts file

L180M01A.message1=The same branch of same data already have a record in Non-approval list, system will delete and re-produce, whether to perform?
L180M01A.message2=The same branch of the same data sum was the review list

update490=Update current month's Credit Review Control File

#J-110-0304_05097_B1001 Web e-Loan\u6388\u4fe1\u8986\u5be9\u914d\u5408RPA\u4f5c\u696d\u4fee\u6539
C240M01A.status=RPA\u72c0\u614b
C240M01A.rpaDate=RPA\u57f7\u884c\u65e5\u671f 

