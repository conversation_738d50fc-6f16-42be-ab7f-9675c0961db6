/* 
 * LMS1805Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01C;
import com.mega.eloan.lms.model.L180M01Z;

public interface LMS1805Service extends ICapService {

	// L180M01a 覆審名單主檔

	/**
	 * 多筆刪除L180M01A
	 * 
	 * @param oids
	 */
	void deleteL180m01aList(String[] oids);

	/**
	 * 利用 mainId、F統編、重複序號搜尋L180M01B
	 * 
	 * @param MainId
	 * @param id
	 * @param dupno
	 * @return
	 */
	List<L180M01B> fingL180m01bByCustId(String MainId, String CustId,
			String dupno, String ctlType);

	/**
	 * 利用 mainId、統編、重複序號搜尋L180M01C
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<L180M01C> fingL180m01cByCustId(String mainId, String custId,
			String dupNo, String ctlType);

	// L180M01B 覆審名單明細檔

	/**
	 * 刪除多筆L180M01B
	 * 
	 * @param mainId
	 */
	void deleteL180m01bList(String mainId, String custId, String dupNo,
			String ctlType);

	/**
	 * 利用mainId搜尋L180M01B
	 * 
	 * @param MainId
	 * @return
	 */
	List<L180M01B> findL180m01bByMainId(String MainId, String ctlType);

	// L180M01C 覆審名單明細額度序號檔

	/**
	 * 多筆刪除L180M01C
	 * 
	 * @param mainId
	 */
	void deleteL180m01cList(String mainId, String custId, String dupNo,
			String ctlType);

	/**
	 * 利用Oid做搜尋
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 搜尋
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 單筆儲存
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	/**
	 * 單筆刪除
	 * 
	 * @param clazz
	 * @param oid
	 */
	@SuppressWarnings("rawtypes")
	void delete(Class clazz, String oid);

	// // UploadFileInfo
	// List<UploadFileInfo> getUploadFileInfoList(ISearch search);
	//
	// int getUploadFileInfoCount(ISearch search);

	/**
	 * 起案所使用的flow
	 * 
	 * @param mainOid
	 */
	void startFlow(String mainOid);

	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType) throws Throwable;

	/**
	 * <pre>
	 * 計算週期 覆審週期說明： 
	 * A：一年覆審一次。 
	 * B：半年覆審一次(主要授信戶並符合信評條件)。 
	 * C：新戶/增額戶。 
	 * D：異常戶已三個月覆審過 - 爾後半年覆審一次。 
	 * E：首次通報之異常戶。（必需在首次通報日後3月內覆審）。 
	 * F：會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。
	 * G：首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審）。 
	 * H：主管機關指定覆審案件。
	 * </pre>
	 * 
	 * @param dataDate
	 *            要覆審的月份 xxxx-xx-01
	 * @param dataMap
	 * @param mode
	 *            1=名單 3=覆審控制檔
	 * @return
	 */
	String[] cauculateDate(Date dataDate, Map<String, Object> dataMap, int mode);

	/**
	 * 產生覆審名單
	 * 
	 * @param dataDate
	 * @param branchId
	 * @param createBy
	 * @return
	 */
	boolean produceList(Date dataDate, String branchId, String createBy,
			String creator);

	/**
	 * 檢查MIS.CUSTDATA是否有輸入條件的資料
	 * 
	 * @param MainId
	 * @param DupNo
	 * @return
	 */
	public Map<String, Object> findCustdataByIdAndDupNo(String MainId,
			String DupNo);

	/**
	 * 新增覆審名單明細
	 * 
	 * @param MainId
	 * @param DupNo
	 * @param custId
	 * @param dataDate
	 * @param branchId
	 * @return
	 */
	boolean produceNew(String MainId, String DupNo, String custId,
			Date dataDate, String branchId);

	/**
	 * 更新ELF412異常通報
	 * 
	 * @param branch
	 * @param custid
	 * @param dupno
	 * @param mdFlag
	 * @param mddt
	 * @param process
	 * @return
	 */
	String[] importLnfe0851(String branch, String custid, String dupno,
			String mdFlag, String mddt, String process);

	/**
	 * 更新計算LMS412
	 * 
	 * @param elf412Canceldt
	 * @param elf412Nckdflag
	 * @param elf412Nckddate
	 * @param elf412Nextltdt
	 * @param elf412Nextnwdt
	 * @param elf412Mdflag
	 * @param elf412Rckdline
	 * @param elf412Newadd
	 * @param elf412Uckdline
	 * @param elf412Mowtbl1
	 * @param elf412Mowtype
	 * @param elf412Maincust
	 * @param elf412Cstate
	 * @param elf412Ockdline
	 * @param elf412Crdttbl
	 * @param elf412Nckdmemo
	 * @return
	 */
	Object[] caculateElf412(Date elf412Canceldt, String elf412Nckdflag,
			Date elf412Nckddate, Date elf412Nextltdt, Date elf412Nextnwdt,
			String elf412Mdflag, String elf412Rckdline, String elf412Newadd,
			String elf412Uckdline, String elf412Mowtbl1, String elf412Mowtype,
			String elf412Maincust, String elf412Cstate, String elf412Ockdline,
			String elf412Crdttbl, String elf412Nckdmemo, String elf412Branch,
			String elf412IsAllNew, String elf412FcrdType,
			String elf412FcrdArea, String elf412FcrdPred, String elf412FcrdGrad);

	/**
	 * 更新產生覆審名單相關資料
	 * 
	 * @param branchid
	 * @param dataDate
	 * @return
	 */
	String updateResoure(String branchid, Date dataDate);

	/**
	 * 產生覆審報告表
	 * 
	 * @param Oid
	 * @return
	 */
	boolean produceReport(Date ctlDate, String Oid,
			boolean accountYN) throws CapException;

	/**
	 * 修改預計覆審日
	 * 
	 * @param oids
	 * @param defaultCTLDate
	 * @return
	 */
	boolean saveL180m01aDate(String[] oids, Date defaultCTLDate);

	/**
	 * 取消覆審
	 * 
	 * @param oids
	 * @param reason
	 */
	void saveL180m01bCancel(String oid, String reason, Date newNextNwDt);

	/**
	 * 恢復取消覆審
	 * 
	 * @param oids
	 */
	void saveL180m01bRecancel(String[] oids, Date newLRDate);

	/**
	 * 判斷週期代碼
	 * 
	 * @param elfMDFlag
	 * @param elfRCkdLine
	 * @param elfNewAdd
	 * @param elfUCkdLINE
	 * @param elfMowType
	 * @param elfMowTbl1
	 * @param elfCrdTTbl
	 * @param elfMainCust
	 * @return String[] {週期代碼,Y=為週期代碼取得 N=不為週期代碼取得}
	 */
	public String[] getRckLine(String elfMDFlag, String elfRCkdLine,
			String elfNewAdd, String elfUCkdLINE, String elfMowType,
			String elfMowTbl1, String elfCrdTTbl, String elfMainCust,
			String elfIsAllNew, String elfBranch);

	/**
	 * 多筆呈主管覆核
	 * 
	 * @param oids
	 * @param defaultCTLDate
	 * @return
	 */
	Map<String, Object> flowCases(String[] oids, Date defaultCTLDate,
			boolean apply);

	/**
	 * 取號
	 * 
	 * @param oid
	 * @return
	 * @throws CapMessageException
	 */
	public boolean getNumber(String oid)
			throws CapMessageException;

	List<Object[]> l170M01Aproject(String mainId);

	String[] importLnf022(String branch, String custId, String dupNo,
			String cstate);

	/**
	 * 產生名單Excel
	 * 
	 * @param oid
	 * @return
	 */
	boolean produceExcel(String oid);

	/**
	 * 更新全部需先刪除記錄檔
	 * 
	 * @param branchid
	 * @param dataDate
	 */
	void deleteL180M01Z(List<String> branchid, Date dataDate);

	/**
	 * 依前次已傳送覆審名單改列本次為暫不覆審
	 * 
	 * @param exl180m01aOid
	 * @param l180m01bOid
	 */
	void saveNckdFlag(String exl180m01aOid, String l180m01bOid);

	/**
	 * 檢查單一分行是否更新完成
	 * 
	 * @param dataDate
	 * @param branch
	 * @return
	 */
	boolean checkOneUpdate(String branch);

	/**
	 * 更新企金異常通報
	 * 
	 * @param branch
	 * @return
	 */
	boolean updateUnusual(String branch);

	/**
	 * 報告表已覆審，更改名單明細狀態
	 * 
	 * @param projectNo
	 */
	void changeL180M01BDocstatus(String projectNo);

	/**
	 * 產生驗證Excel
	 * 
	 * @param oid
	 * @return
	 * @throws Exception
	 */
	Map<String, Object> produceChkExcel(String oid) throws Exception;

	/**
	 * 找主檔MAID資料
	 * 
	 * @param mainId
	 * @return
	 */
	L180M01A findL180m01aByMainId(String mainId);

	/**
	 * 先檢查L180M01A是否有資料
	 * 
	 * @param bank
	 * @param basedate
	 * @return
	 */
	Map<String, Object> findByBranchAnddataDate(String bank, Date basedate);

	/**
	 * 找尋更新檔
	 * 
	 * @param branch
	 * @param dataDate
	 * @return
	 */
	L180M01Z findByDataDateBranch(String branch, Date dataDate);

}
