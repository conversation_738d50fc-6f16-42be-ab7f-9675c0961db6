package com.mega.eloan.lms.cls.handler.grid;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.cls.service.CLS3801Service;
import com.mega.eloan.lms.model.C103M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls3801gridhandler")
public class CLS3801GridHandler extends AbstractGridHandler {

	@Resource
	UserInfoService userInfoService;

	@Resource
	CLS3801Service cls3801Service;

	@SuppressWarnings("unchecked")
	public CapGridResult queryView(ISearch pageSetting, PageParameters params)
			throws CapException {
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (true) { // 篩選
			String custId = Util.trim(params.getString("search_custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"custId", custId);
			}
		}

		// 取得資料
		Page<? extends GenericBean> page = cls3801Service.findPage(
				C103M01A.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("updater", new UserNameFormatter(userInfoService));// 異動人員

		// 第三個參數為formatting
		return new CapGridResult(page.getContent(), page.getTotalRow(),
				formatter);
	}

}
