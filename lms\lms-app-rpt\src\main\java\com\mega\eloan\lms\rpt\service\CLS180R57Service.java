package com.mega.eloan.lms.rpt.service;

import java.util.Map;
import java.util.Properties;

import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import com.mega.eloan.lms.model.C101S01E;




/**
 * <pre>
 * 中期循環年度檢視表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS180R57Service {
	
	public Map<String, Integer> getTitleMap();
	
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException;

	public void setHeader1Content(WritableSheet sheet, Properties prop, int colIndex, int rowIndex) throws WriteException;
	
	public void setHeader2Content(WritableSheet sheet, Map<String, Integer> headerMap1, Properties prop, int colIndex, int rowIndex) throws WriteException;

	public Map<String, Integer> getHeader2Map();

	public int setBodyContentOfDebtorAndGuarantorData(WritableSheet sheet, int colIndex, int rowIndex, Properties prop, Map<String, C101S01E> debtorMap, String custPos, Map<String, String> ohterDataMap,
								Map<String, String> haveNoNaMap, Map<String, String> yesNoMap) throws RowsExceededException, WriteException;

	public Map<String, Map<String, String>> getRelatedDebtorMap(String cntrno);

	public int setBodyContent2(WritableSheet sheet, int colIndex, int rowIndex, Properties prop) throws RowsExceededException, WriteException;
}
