package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;

import com.mega.eloan.lms.model.L192M01A;

public interface L192M01ADao extends IGenericDao<L192M01A> {
	List<Object[]> getDistinctFinishBorrowId(String ownBrId,String docStatus, ISearch search);

	int getCountDistinctFinishBorrowId(String ownBrId, String docStatus);
	
	List<Object[]> getDistinctFinishBorrowId2(String ownBrId, String docStatus,ISearch search);

	int getCountDistinctFinishBorrowId2(String ownBrId, String docStatus);
	
	BigDecimal getWpNo(String brNo,String wpYear);
	
	L192M01A findLatestL192M01byBrNoShtTypeInnerAudit(String brNo,
			String shtType, String innerAudit);
}
