var L909VAction = {
    ghandler: "lms9091gridhandler",
    $form: $("#lms9091tabForm"),
    gridId: "#gridview",
    /**
     * 更新grid
     */
    _tiggerGrid: function(){
        $(L909VAction.gridId).trigger("reloadGrid");
    }
};
var check = false// 是否已輸入資料
$(function(){
    lms9091v02count();
    var btn = $("#buttonPanel");
    btn.find("#btnPrint").click(function(){
        if (check) {
            btnPrint();
        }
        else {
            lms9091v02count();
            CommonAPI.showErrorMessage(i18n.lms9091v02["L909V02.printerror"]);
        }
    });
});

var L909v02Grid = $('#gridfile').iGrid({
    handler: L909VAction.ghandler,
    height: 350,
    scroll: 500,
    rowNum: 500,
    sortable: false,
    colModel: [{
        colHeader: i18n.lms9091v02['L909V02.periods'],// 期數,
        name: 'periods',
        width: 120,
        align: "left",
        sortable: false
    }, {
        colHeader: i18n.lms9091v02['L909V02.paydate'],// 日期
        name: 'paydate',
        width: 140,
        align: "center",
        sortable: false
    }, {
        colHeader: i18n.lms9091v02['L909V02.balance'],// 期初餘額
        name: 'balance',
        width: 140,
        align: "right",
        sortable: false,
        formatter: 'currency'
    }, {
        colHeader: i18n.lms9091v02['L909V02.periodprincipal'],// 期付金本金
        name: 'periodprincipal',
        width: 140,
        align: "right",
        formatter: 'currency',
        sortable: false
    }, {
        colHeader: i18n.lms9091v02['L909V02.periodinterest'],// 期付金利息
        name: 'periodinterest',
        width: 140,
        align: "right",
        formatter: 'currency',
        sortable: false
    }, {
        colHeader: i18n.lms9091v02['L909V02.periodpay'],// 期付金金額
        name: 'periodpay',
        width: 140,
        align: "right",
        formatter: 'currency',
        sortable: false
    }, {
        colHeader: i18n.lms9091v02['L909V02.Dbalance'],// 期末餘額
        name: 'Dbalance',
        width: 140,
        align: "right",
        formatter: 'currency',
        sortable: false
    }]
});
/**
 * 更新grid
 */
function filterGrid(sendData){
    $("#gridfile").jqGrid("setGridParam", {
        postData: $.extend({
            formAction: "queryL909count2",
            scroll: 200,
            rowNum: 200,
            sortable: false,
            type: $("[name=queryData]:checked").val()
        }, sendData || {})
    }).trigger("reloadGrid");
    $.thickbox.close();
}

/**
 * 開啟列印畫面
 */
function btnPrint(){
    var $form = $('#lms9091tabForm');
    $.form.submit({
        url: "../simple/FileProcessingService",
        target: "_blank",
        data: {
            fileDownloadName: "lms909r02.pdf",
            serviceName: "lms9091r02rptservice",
            balance: $form.find("#balance").val(),
            years: $form.find("#years").val(),
            cycle: $form.find('input:radio:checked[name="cycle"]').val(),
            installments: $form.find("#installments").val(),
            repaymod: $form.find('input:radio:checked[name="repaymod"]').val(),
            graceperiod: $form.find("#graceperiod").val(),
            month: $form.find("#month").val(),
            NowFrom: $form.find("#NowFrom").val(),
            NowEnd: $form.find("#NowEnd").val(),
            PayWayAmt: $form.find("#PayWayAmt").val(),
            startdate: $form.find("#startdate").val(),
            start1: $form.find("#1start").val(),
            start2: $form.find("#2start").val(),
            start3: $form.find("#3start").val(),
            start4: $form.find("#4start").val(),
            start5: $form.find("#5start").val(),
            end1: $form.find("#1end").val(),
            end2: $form.find("#2end").val(),
            end3: $form.find("#3end").val(),
            end4: $form.find("#4end").val(),
            end5: $form.find("#5end").val(),
            rate1: $form.find("#1rate").val(),
            rate2: $form.find("#2rate").val(),
            rate3: $form.find("#3rate").val(),
            rate4: $form.find("#4rate").val(),
            rate5: $form.find("#5rate").val()
        }
    });
    
}

/**
 * 開啟視窗(登錄查詢期付金對照表條件)
 */
function lms9091v02count(){
    $("#lms9091new").thickbox({
        title: i18n.lms9091v02["L909V02.title"],
        width: 600,
        height: 500,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var $form = $('#lms9091tabForm');
                var sum = 0;
                var temp = 0
                if ($form.valid()) {
                    filterGrid({
                        balance: $form.find("#balance").val(),
                        years: $form.find("#years").val(),
                        month: $form.find("#month").val(),
                        cycle: $form.find('input:radio:checked[name="cycle"]').val(),
                        repaymod: $form.find('input:radio:checked[name="repaymod"]').val(),
                        NowFrom: $form.find("#NowFrom").val(),
                        NowEnd: $form.find("#NowEnd").val(),
                        PayWayAmt: $form.find("#PayWayAmt").val(),
                        startdate: $form.find("#startdate").val(),
                        start1: $form.find("#1start").val(),
                        start2: $form.find("#2start").val(),
                        start3: $form.find("#3start").val(),
                        start4: $form.find("#4start").val(),
                        start5: $form.find("#5start").val(),
                        end1: $form.find("#1end").val(),
                        end2: $form.find("#2end").val(),
                        end3: $form.find("#3end").val(),
                        end4: $form.find("#4end").val(),
                        end5: $form.find("#5end").val(),
                        rate1: $form.find("#1rate").val(),
                        rate2: $form.find("#2rate").val(),
                        rate3: $form.find("#3rate").val(),
                        rate4: $form.find("#4rate").val(),
                        rate5: $form.find("#5rate").val()
                    });
                    $.thickbox.close();
                    check = true;
                }
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}
