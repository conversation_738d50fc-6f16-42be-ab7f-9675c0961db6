$(document).ready(function(){
    var grid = $("#gridview").iGrid({
        handler: 'lms7700gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL140mm5a",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "approveTime",
        sortorder: "desc|desc",
        colModel: [{
	        colHeader: i18n.lms7700v01["L140MM5A.DATAYM"], // 資料日期
	        align: "center",
	        width: 100, // 設定寬度
	        sortable: true, // 是否允許排序
	        formatter: 'date',
	        formatoptions: {
	            srcformat: 'Y-m-d',
	            newformat: 'Y-m'
	        },
	        name: 'dataYM' // col.id
	    }, {
            colHeader: i18n.lms7700v01['L140MM5A.CNT'],//筆數,
            name: 'cnt',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms7700v01['L140MM5A.creator'],//"分行經辦",
            name: 'updater',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms7700v01['L140MM5A.approver'],//"覆核",
            name: 'approver',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms7700v01["L140MM5A.approveTime"], // 核准日期
            align: "left",
            width: 80, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'approveTime',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d H:i'
            }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryLMS7700M01",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    $("#buttonPanel").find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            // L140M01M.error1=此功能不能多選
            CommonAPI.showMessage(i18n.lms7700v01["L140MM5A.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    });
});
