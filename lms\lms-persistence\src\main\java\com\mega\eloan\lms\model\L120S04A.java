/* 
 * L120S04A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 關係戶於本行各項業務往來明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S04A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S04A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件產生方式<p/>
	 * 系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 */
	@Column(name="CREATEBY", length=3, columnDefinition="CHAR(3)")
	private String createBY;

	/** 統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 戶名 **/
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 區部別 **/
	@Column(name="TYPCD", length=1, columnDefinition="CHAR(1)")
	private String typCd;

	/** 
	 * 與借款人關係<p/>
	 * (多選)<br/>
	 *  借戶 | 1<br/>
	 *  借戶負責人 | 2<br/>
	 *  集團企業合計 | 3<br/>
	 *  關係企業合計 | 4<br/>
	 *  集團企業 | 5<br/>
	 *  關係企業 | 6
	 */
	@Column(name="CUSTRELATION", length=12, columnDefinition="VARCHAR(12)")
	private String custRelation;

	/** 
	 * 簽報書列印註記<p/>
	 * 要列印 | 1<br/>
	 *  不列印 | 2
	 */
	@Column(name="PRTFLAG", length=1, columnDefinition="CHAR(1)")
	private String prtFlag;

	/** 
	 * 資料查詢期間起日<p/>
	 * YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="QUERYDATES", columnDefinition="DATE")
	private Date queryDateS;

	/** 
	 * 資料查詢期間迄日<p/>
	 * YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="QUERYDATEE", columnDefinition="DATE")
	private Date queryDateE;

	/** 存款-活期存款 **/
	@Column(name="DEPTIME", columnDefinition="DECIMAL(13,0)")
	private Long depTime;

	/** 存款-定期存款 **/
	@Column(name="DEPFIXED", columnDefinition="DECIMAL(13,0)")
	private Long depFixed;

	/** 放款-額度 **/
	@Column(name="LOANQUOTA", columnDefinition="DECIMAL(13,0)")
	private Long loanQuota;

	/** 放款-平均餘額 **/
	@Column(name="LOANAVGBAL", columnDefinition="DECIMAL(13,0)")
	private Long loanAvgBal;

	/** 
	 * 放款-平均動用率<p/>
	 * %
	 */
	@Column(name="LOANAVGRATE", columnDefinition="DECIMAL(5,2)")
	private Double loanAvgRate;

	/** 外匯-進口(筆) **/
	@Column(name="EXCHGIMPREC", columnDefinition="DECIMAL(5,0)")
	private Integer exchgImpRec;

	/** 外匯-進口(金額) **/
	@Column(name="EXCHGIMPAMT", columnDefinition="DECIMAL(13,0)")
	private Long exchgImpAmt;

	/** 外匯-出口(筆) **/
	@Column(name="EXCHGEXPREC", columnDefinition="DECIMAL(5,0)")
	private Integer exchgExpRec;

	/** 外匯-出口(金額) **/
	@Column(name="EXCHGEXPAMT", columnDefinition="DECIMAL(13,0)")
	private Long exchgExpAmt;

	/** 外匯-匯出(筆) **/
	@Column(name="EXCHGOUTREC", columnDefinition="DECIMAL(5,0)")
	private Integer exchgOutRec;

	/** 外匯-匯出(金額) **/
	@Column(name="EXCHGOUTAMT", columnDefinition="DECIMAL(13,0)")
	private Long exchgOutAmt;

	/** 外匯-匯入(筆) **/
	@Column(name="EXCHGINREC", columnDefinition="DECIMAL(5,0)")
	private Integer exchgInRec;

	/** 外匯-匯入(金額) **/
	@Column(name="EXCHGINAMT", columnDefinition="DECIMAL(13,0)")
	private Long exchgInAmt;

	/** 衍生性商品-選擇權 **/
	@Column(name="DEROPTION", columnDefinition="DECIMAL(13,0)")
	private Long derOption;

	/** 衍生性商品-利率交換 **/
	@Column(name="DERRATEEXCHG", columnDefinition="DECIMAL(13,0)")
	private Long derRateExchg;

	/** 衍生性商品-換匯換利 **/
	@Column(name="DERCCS", columnDefinition="DECIMAL(13,0)")
	private Long derCCS;

	/** 衍生性商品-遠匯 **/
	@Column(name="DERDRAFT", columnDefinition="DECIMAL(13,0)")
	private Long derDraft;

	/** 衍生性商品-遠匯(含SWAP) **/
	@Column(name="DERSWAP", columnDefinition="DECIMAL(13,0)")
	private Long derSWAP;

	/** 信託業務-國內外基金債券 **/
	@Column(name="TRUSTBOND", columnDefinition="DECIMAL(13,0)")
	private Long trustBond;

	/** 信託業務-基金保管 **/
	@Column(name="TRUSTFUND", columnDefinition="DECIMAL(13,0)")
	private Long trustFund;

	/** 信託業務-集管 **/
	@Column(name="TRUSTSETACCT", columnDefinition="DECIMAL(13,0)")
	private Long trustSetAcct;

	/** 信託業務-有價證券信託 **/
	@Column(name="TRUSTSECURITIES", columnDefinition="DECIMAL(13,0)")
	private Long trustSecurities;

	/** 信託業務-不動產信託 **/
	@Column(name="TRUSTREITS", columnDefinition="DECIMAL(13,0)")
	private Long trustREITs;

	/** 信託業務-福儲信託 **/
	@Column(name="TRUSTWELDEP", columnDefinition="DECIMAL(13,0)")
	private Long trustWelDep;

	/** 信託業務-其他信託 **/
	@Column(name="TRUSTOTHER", columnDefinition="DECIMAL(13,0)")
	private Long trustOther;

	/** 財富管理-信託 **/
	@Column(name="WEALTHTRUST", columnDefinition="DECIMAL(13,0)")
	private Long wealthTrust;

	/** 財富管理-保險佣金 **/
	@Column(name="WEALTHINSCOM", columnDefinition="DECIMAL(13,0)")
	private Long wealthInsCom;

	/** 財富管理-雙元投資 **/
	@Column(name="WEALTHINVEST", columnDefinition="DECIMAL(13,0)")
	private Long wealthInvest;

	/** 薪轉戶-薪轉戶數 **/
	@Column(name="SALARYREC", columnDefinition="DECIMAL(13,0)")
	private Long salaryRec;

	/** 薪轉戶-定期定額戶數 **/
	@Column(name="SALARYFIXED", columnDefinition="DECIMAL(13,0)")
	private Long salaryFixed;

	/** 薪轉戶-房貸戶數 **/
	@Column(name="SALARYMORTGAGE", columnDefinition="DECIMAL(13,0)")
	private Long salaryMortgage;

	/** 薪轉戶-消貸戶數 **/
	@Column(name="SALARYCONSUMPTION", columnDefinition="DECIMAL(13,0)")
	private Long salaryConsumption;

	/** 薪轉戶-信用卡持卡人數 **/
	@Column(name="SALARYCARD", columnDefinition="DECIMAL(13,0)")
	private Long salaryCard;

	/** 薪轉戶-個人網銀戶數 **/
	@Column(name="SALARYNETWORK", columnDefinition="DECIMAL(13,0)")
	private Long salaryNetwork;

	/** 卡務-商務卡 **/
	@Column(name="CARDCOMMERCIAL", columnDefinition="DECIMAL(13,0)")
	private Long cardCommercial;

	/** 
	 * 卡務-聯名卡<p/>
	 * Y/N 是否有聯名卡
	 */
	@Column(name="CARDCOBRANDED", length=1, columnDefinition="CHAR(1)")
	private String cardCoBranded;

	/** GEB業務-台幣交易筆數 **/
	@Column(name="GEBTWDREC", columnDefinition="DECIMAL(13,0)")
	private Long GEBTWDRec;

	/** GEB業務-外幣交易筆數 **/
	@Column(name="GEBOTHREC", columnDefinition="DECIMAL(13,0)")
	private Long GEBOTHRec;

	/** GEB業務-信用狀交易筆數 **/
	@Column(name="GEBLCREC", columnDefinition="DECIMAL(13,0)")
	private Long GEBLCRec;

	/** 利潤貢獻度 **/
	@Column(name="PROFIT", columnDefinition="DECIMAL(13,0)")
	private Long profit;

	/** 
	 * 說明<p/>
	 * 100/12/28新增<br/>
	 *  100個全型字
	 */
	@Column(name="MEMO", length=300, columnDefinition="VARCHAR(300)")
	private String memo;

	/** 
	 * cardNoneCommercial<p/>
	 * 建霖要求新增此欄位<br/>
	 *  101/12/28 Miller新增
	 */
	@Column(name="CARDNONECOMMERCIAL", columnDefinition="DECIMAL(13,0)")
	private Long cardNoneCommercial;	
	
	/** 
	 * 輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name="CHKYN", length=1, columnDefinition="CHAR(1)")
	private String chkYN;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 分項統一編號 **/
	@Column(name="KEYCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String keyCustId;
	
	/** 分項重覆序號 **/
	@Column(name="KEYDUPNO", length=1, columnDefinition="CHAR(1)")
	private String keyDupNo;
	
	/** 與借款人關係Grid 排序(非DB欄位) **/
	@Transient
	private String custRelationIndex;
	
	/** 企業戶員工薪轉貢獻度 **/
	@Column(name="PROFITSALARY", columnDefinition="DECIMAL(13,0)")
	private Long profitSalary;
	
	/** 信託專戶利差 **/
	@Column(name="PROFITTRUSTFDTA", columnDefinition="DECIMAL(13,0)")
	private Long profitTrustFdta;
	
	/** 授信-應收帳款無追索權買方有效額度(等值台幣)(資料來源LNF02P) **/
	@Column(name="RCVBUYFACTAMT", columnDefinition="DECIMAL(13,0)")
	private Long rcvBuyFactAmt;

	/** 授信-應收帳款無追索權買方承購月平均餘額(等值台幣)(資料來源LNF150) **/
	@Column(name="RCVBUYAVGBAL", columnDefinition="DECIMAL(13,0)")
	private Long rcvBuyAvgBal;
	
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件產生方式<p/>
	 * 系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 */
	public String getCreateBY() {
		return this.createBY;
	}
	/**
	 *  設定文件產生方式<p/>
	 *  系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 **/
	public void setCreateBY(String value) {
		this.createBY = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得戶名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定戶名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得區部別 **/
	public String getTypCd() {
		return this.typCd;
	}
	/** 設定區部別 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/** 
	 * 取得與借款人關係<p/>
	 * (多選)<br/>
	 *  借戶 | 1<br/>
	 *  借戶負責人 | 2<br/>
	 *  集團企業合計 | 3<br/>
	 *  關係企業合計 | 4<br/>
	 *  集團企業 | 5<br/>
	 *  關係企業 | 6
	 */
	public String getCustRelation() {
		return this.custRelation;
	}
	/**
	 *  設定與借款人關係<p/>
	 *  (多選)<br/>
	 *  借戶 | 1<br/>
	 *  借戶負責人 | 2<br/>
	 *  集團企業合計 | 3<br/>
	 *  關係企業合計 | 4<br/>
	 *  集團企業 | 5<br/>
	 *  關係企業 | 6
	 **/
	public void setCustRelation(String value) {
		this.custRelation = value;
	}

	/** 
	 * 取得簽報書列印註記<p/>
	 * 要列印 | 1<br/>
	 *  不列印 | 2
	 */
	public String getPrtFlag() {
		return this.prtFlag;
	}
	/**
	 *  設定簽報書列印註記<p/>
	 *  要列印 | 1<br/>
	 *  不列印 | 2
	 **/
	public void setPrtFlag(String value) {
		this.prtFlag = value;
	}

	/** 
	 * 取得資料查詢期間起日<p/>
	 * YYYY-MM-01
	 */
	public Date getQueryDateS() {
		return this.queryDateS;
	}
	/**
	 *  設定資料查詢期間起日<p/>
	 *  YYYY-MM-01
	 **/
	public void setQueryDateS(Date value) {
		this.queryDateS = value;
	}

	/** 
	 * 取得資料查詢期間迄日<p/>
	 * YYYY-MM-01
	 */
	public Date getQueryDateE() {
		return this.queryDateE;
	}
	/**
	 *  設定資料查詢期間迄日<p/>
	 *  YYYY-MM-01
	 **/
	public void setQueryDateE(Date value) {
		this.queryDateE = value;
	}

	/** 取得存款-活期存款 **/
	public Long getDepTime() {
		return this.depTime;
	}
	/** 設定存款-活期存款 **/
	public void setDepTime(Long value) {
		this.depTime = value;
	}

	/** 取得存款-定期存款 **/
	public Long getDepFixed() {
		return this.depFixed;
	}
	/** 設定存款-定期存款 **/
	public void setDepFixed(Long value) {
		this.depFixed = value;
	}

	/** 取得放款-額度 **/
	public Long getLoanQuota() {
		return this.loanQuota;
	}
	/** 設定放款-額度 **/
	public void setLoanQuota(Long value) {
		this.loanQuota = value;
	}

	/** 取得放款-平均餘額 **/
	public Long getLoanAvgBal() {
		return this.loanAvgBal;
	}
	/** 設定放款-平均餘額 **/
	public void setLoanAvgBal(Long value) {
		this.loanAvgBal = value;
	}

	/** 
	 * 取得放款-平均動用率<p/>
	 * %
	 */
	public Double getLoanAvgRate() {
		return this.loanAvgRate;
	}
	/**
	 *  設定放款-平均動用率<p/>
	 *  %
	 **/
	public void setLoanAvgRate(Double value) {
		this.loanAvgRate = value;
	}

	/** 取得外匯-進口(筆) **/
	public Integer getExchgImpRec() {
		return this.exchgImpRec;
	}
	/** 設定外匯-進口(筆) **/
	public void setExchgImpRec(Integer value) {
		this.exchgImpRec = value;
	}

	/** 取得外匯-進口(金額) **/
	public Long getExchgImpAmt() {
		return this.exchgImpAmt;
	}
	/** 設定外匯-進口(金額) **/
	public void setExchgImpAmt(Long value) {
		this.exchgImpAmt = value;
	}

	/** 取得外匯-出口(筆) **/
	public Integer getExchgExpRec() {
		return this.exchgExpRec;
	}
	/** 設定外匯-出口(筆) **/
	public void setExchgExpRec(Integer value) {
		this.exchgExpRec = value;
	}

	/** 取得外匯-出口(金額) **/
	public Long getExchgExpAmt() {
		return this.exchgExpAmt;
	}
	/** 設定外匯-出口(金額) **/
	public void setExchgExpAmt(Long value) {
		this.exchgExpAmt = value;
	}

	/** 取得外匯-匯出(筆) **/
	public Integer getExchgOutRec() {
		return this.exchgOutRec;
	}
	/** 設定外匯-匯出(筆) **/
	public void setExchgOutRec(Integer value) {
		this.exchgOutRec = value;
	}

	/** 取得外匯-匯出(金額) **/
	public Long getExchgOutAmt() {
		return this.exchgOutAmt;
	}
	/** 設定外匯-匯出(金額) **/
	public void setExchgOutAmt(Long value) {
		this.exchgOutAmt = value;
	}

	/** 取得外匯-匯入(筆) **/
	public Integer getExchgInRec() {
		return this.exchgInRec;
	}
	/** 設定外匯-匯入(筆) **/
	public void setExchgInRec(Integer value) {
		this.exchgInRec = value;
	}

	/** 取得外匯-匯入(金額) **/
	public Long getExchgInAmt() {
		return this.exchgInAmt;
	}
	/** 設定外匯-匯入(金額) **/
	public void setExchgInAmt(Long value) {
		this.exchgInAmt = value;
	}

	/** 取得衍生性商品-選擇權 **/
	public Long getDerOption() {
		return this.derOption;
	}
	/** 設定衍生性商品-選擇權 **/
	public void setDerOption(Long value) {
		this.derOption = value;
	}

	/** 取得衍生性商品-利率交換 **/
	public Long getDerRateExchg() {
		return this.derRateExchg;
	}
	/** 設定衍生性商品-利率交換 **/
	public void setDerRateExchg(Long value) {
		this.derRateExchg = value;
	}

	/** 取得衍生性商品-換匯換利 **/
	public Long getDerCCS() {
		return this.derCCS;
	}
	/** 設定衍生性商品-換匯換利 **/
	public void setDerCCS(Long value) {
		this.derCCS = value;
	}

	/** 取得衍生性商品-遠匯 **/
	public Long getDerDraft() {
		return this.derDraft;
	}
	/** 設定衍生性商品-遠匯 **/
	public void setDerDraft(Long value) {
		this.derDraft = value;
	}

	/** 取得衍生性商品-遠匯(含SWAP) **/
	public Long getDerSWAP() {
		return this.derSWAP;
	}
	/** 設定衍生性商品-遠匯(含SWAP) **/
	public void setDerSWAP(Long value) {
		this.derSWAP = value;
	}

	/** 取得信託業務-國內外基金債券 **/
	public Long getTrustBond() {
		return this.trustBond;
	}
	/** 設定信託業務-國內外基金債券 **/
	public void setTrustBond(Long value) {
		this.trustBond = value;
	}

	/** 取得信託業務-基金保管 **/
	public Long getTrustFund() {
		return this.trustFund;
	}
	/** 設定信託業務-基金保管 **/
	public void setTrustFund(Long value) {
		this.trustFund = value;
	}

	/** 取得信託業務-集管 **/
	public Long getTrustSetAcct() {
		return this.trustSetAcct;
	}
	/** 設定信託業務-集管 **/
	public void setTrustSetAcct(Long value) {
		this.trustSetAcct = value;
	}

	/** 取得信託業務-有價證券信託 **/
	public Long getTrustSecurities() {
		return this.trustSecurities;
	}
	/** 設定信託業務-有價證券信託 **/
	public void setTrustSecurities(Long value) {
		this.trustSecurities = value;
	}

	/** 取得信託業務-不動產信託 **/
	public Long getTrustREITs() {
		return this.trustREITs;
	}
	/** 設定信託業務-不動產信託 **/
	public void setTrustREITs(Long value) {
		this.trustREITs = value;
	}

	/** 取得信託業務-福儲信託 **/
	public Long getTrustWelDep() {
		return this.trustWelDep;
	}
	/** 設定信託業務-福儲信託 **/
	public void setTrustWelDep(Long value) {
		this.trustWelDep = value;
	}

	/** 取得信託業務-其他信託 **/
	public Long getTrustOther() {
		return this.trustOther;
	}
	/** 設定信託業務-其他信託 **/
	public void setTrustOther(Long value) {
		this.trustOther = value;
	}

	/** 取得財富管理-信託 **/
	public Long getWealthTrust() {
		return this.wealthTrust;
	}
	/** 設定財富管理-信託 **/
	public void setWealthTrust(Long value) {
		this.wealthTrust = value;
	}

	/** 取得財富管理-保險佣金 **/
	public Long getWealthInsCom() {
		return this.wealthInsCom;
	}
	/** 設定財富管理-保險佣金 **/
	public void setWealthInsCom(Long value) {
		this.wealthInsCom = value;
	}

	/** 取得財富管理-雙元投資 **/
	public Long getWealthInvest() {
		return this.wealthInvest;
	}
	/** 設定財富管理-雙元投資 **/
	public void setWealthInvest(Long value) {
		this.wealthInvest = value;
	}

	/** 取得薪轉戶-薪轉戶數 **/
	public Long getSalaryRec() {
		return this.salaryRec;
	}
	/** 設定薪轉戶-薪轉戶數 **/
	public void setSalaryRec(Long value) {
		this.salaryRec = value;
	}

	/** 取得薪轉戶-定期定額戶數 **/
	public Long getSalaryFixed() {
		return this.salaryFixed;
	}
	/** 設定薪轉戶-定期定額戶數 **/
	public void setSalaryFixed(Long value) {
		this.salaryFixed = value;
	}

	/** 取得薪轉戶-房貸戶數 **/
	public Long getSalaryMortgage() {
		return this.salaryMortgage;
	}
	/** 設定薪轉戶-房貸戶數 **/
	public void setSalaryMortgage(Long value) {
		this.salaryMortgage = value;
	}

	/** 取得薪轉戶-消貸戶數 **/
	public Long getSalaryConsumption() {
		return this.salaryConsumption;
	}
	/** 設定薪轉戶-消貸戶數 **/
	public void setSalaryConsumption(Long value) {
		this.salaryConsumption = value;
	}

	/** 取得薪轉戶-信用卡持卡人數 **/
	public Long getSalaryCard() {
		return this.salaryCard;
	}
	/** 設定薪轉戶-信用卡持卡人數 **/
	public void setSalaryCard(Long value) {
		this.salaryCard = value;
	}

	/** 取得薪轉戶-個人網銀戶數 **/
	public Long getSalaryNetwork() {
		return this.salaryNetwork;
	}
	/** 設定薪轉戶-個人網銀戶數 **/
	public void setSalaryNetwork(Long value) {
		this.salaryNetwork = value;
	}

	/** 取得卡務-商務卡 **/
	public Long getCardCommercial() {
		return this.cardCommercial;
	}
	/** 設定卡務-商務卡 **/
	public void setCardCommercial(Long value) {
		this.cardCommercial = value;
	}

	/** 
	 * 取得卡務-聯名卡<p/>
	 * Y/N 是否有聯名卡
	 */
	public String getCardCoBranded() {
		return this.cardCoBranded;
	}
	/**
	 *  設定卡務-聯名卡<p/>
	 *  Y/N 是否有聯名卡
	 **/
	public void setCardCoBranded(String value) {
		this.cardCoBranded = value;
	}

	/** 取得GEB業務-台幣交易筆數 **/
	public Long getGEBTWDRec() {
		return this.GEBTWDRec;
	}
	/** 設定GEB業務-台幣交易筆數 **/
	public void setGEBTWDRec(Long value) {
		this.GEBTWDRec = value;
	}

	/** 取得GEB業務-外幣交易筆數 **/
	public Long getGEBOTHRec() {
		return this.GEBOTHRec;
	}
	/** 設定GEB業務-外幣交易筆數 **/
	public void setGEBOTHRec(Long value) {
		this.GEBOTHRec = value;
	}

	/** 取得GEB業務-信用狀交易筆數 **/
	public Long getGEBLCRec() {
		return this.GEBLCRec;
	}
	/** 設定GEB業務-信用狀交易筆數 **/
	public void setGEBLCRec(Long value) {
		this.GEBLCRec = value;
	}

	/** 取得利潤貢獻度 **/
	public Long getProfit() {
		return this.profit;
	}
	/** 設定利潤貢獻度 **/
	public void setProfit(Long value) {
		this.profit = value;
	}

	/** 
	 * 取得說明<p/>
	 * 100/12/28新增<br/>
	 *  100個全型字
	 */
	public String getMemo() {
		return this.memo;
	}
	/**
	 *  設定說明<p/>
	 *  100/12/28新增<br/>
	 *  100個全型字
	 **/
	public void setMemo(String value) {
		this.memo = value;
	}

	/** 
	 * 取得cardNoneCommercial<p/>
	 * 建霖要求新增此欄位<br/>
	 *  101/12/28 Miller新增
	 *  取得卡務-非商務卡
	 */
	public Long getCardNoneCommercial() {
		return this.cardNoneCommercial;
	}
	/**
	 *  設定cardNoneCommercial<p/>
	 *  建霖要求新增此欄位<br/>
	 *  設定卡務-非商務卡
	 *  101/12/28 Miller新增
	 **/
	public void setCardNoneCommercial(Long value) {
		this.cardNoneCommercial = value;
	}	
	
	/** 
	 * 取得輸入資料檢誤完成(Y/N)<p/>
	 * 100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}
	/**
	 *  設定輸入資料檢誤完成(Y/N)<p/>
	 *  100/12/05新增<br/>
	 *  Y/N<br/>
	 *  預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	/** 取得分項統一編號 **/
	public String getKeyCustId() {
		return keyCustId;
	}
	/** 設定分項統一編號 **/
	public void setKeyCustId(String keyCustId) {
		this.keyCustId = keyCustId;
	}
	
	/** 取得分項重覆序號 **/
	public String getKeyDupNo() {
		return keyDupNo;
	}
	/** 設定分項重覆序號 **/
	public void setKeyDupNo(String keyDupNo) {
		this.keyDupNo = keyDupNo;
	}
	
	/**
	 * 設定與借款人關係Grid 排序
	 * @param custRelationIndex
	 */
	public void setCustRelationIndex(String custRelationIndex) {
		this.custRelationIndex = custRelationIndex;
	}
	
	/**
	 * 取得與借款人關係Grid 排序
	 * @return
	 */
	public String getCustRelationIndex() {
		 
		return this.custRelationIndex;
	}
	
	/**
	 * 設定企業戶員工薪轉貢獻度
	 * @param profitSalary
	 */
	public void setProfitSalary(Long profitSalary) {
		this.profitSalary = profitSalary;
	}
	
	/**
	 * 取得企業戶員工薪轉貢獻度
	 * @return
	 */
	public Long getProfitSalary() {
		return profitSalary;
	}
	
	/**
	 * 設定信託專戶利差
	 * @param profitTrustFdta
	 */
	public void setProfitTrustFdta(Long profitTrustFdta) {
		this.profitTrustFdta = profitTrustFdta;
	}
	
	/**
	 * 取得信託專戶利差
	 * @param profitTrustFdta
	 */
	public Long getProfitTrustFdta() {
		return profitTrustFdta;
	}
	
	
		/**
	 * 設定授信-應收帳款無追索權買方有效額度(等值台幣)(資料來源LNF02P)
	 * @param profitTrustFdta
	 */
	public void setRcvBuyFactAmt(Long rcvBuyFactAmt) {
		this.rcvBuyFactAmt = rcvBuyFactAmt;
	}
	
	/**
	 * 取得授信-應收帳款無追索權買方有效額度(等值台幣)(資料來源LNF02P)
	 * @param profitTrustFdta
	 */
	public Long getRcvBuyFactAmt() {
		return rcvBuyFactAmt;
	}
	
	/**
	 * 設定授信-應收帳款無追索權買方承購月平均餘額(等值台幣)(資料來源LNF150) 
	 * @param profitTrustFdta
	 */
	public void setRcvBuyAvgBal(Long rcvBuyAvgBal) {
		this.rcvBuyAvgBal = rcvBuyAvgBal;
	}
	
	/**
	 * 取得授信-應收帳款無追索權買方承購月平均餘額(等值台幣)(資料來源LNF150) 
	 * @param profitTrustFdta
	 */
	public Long getRcvBuyAvgBal() {
		return rcvBuyAvgBal;
	}
}
