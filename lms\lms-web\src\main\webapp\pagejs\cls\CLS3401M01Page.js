var initDfd = $.Deferred();
var _handler = "cls3401m01formhandler";

$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;
	if(true){
		$.form.init({
			formHandler:_handler, 
			formAction:'queryC340M01A', 
			loadSuccess:function(json){			
				
				// 控制頁面 Read/Write
				if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
					tabForm.lockDoc();
					initControl_lockDoc = true;
				}
				
				tabForm.injectData(json);
				initDfd.resolve(json);	
				
				ilog.debug("[queryC340M01A]mainId='"+json.mainId +"', custId="+json.custId);
		}});
		//根據權限隱藏特定物件
        $.ajax({
            action: "check_only_expermission",
            handler: _handler
            }).done(function(responseData){
            	if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
            		$(".only-ex-permission").hide();
            	}
        });
	}
	//================================
	
	btnPanel.find("#btnSave").click(function(){		
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				if(json.IncompleteMsg){
					API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
				}else{
					API.showMessage(i18n.def.saveSuccess);	
				}	
			}
        });
	}).end().find("#btnPrint").click(function(){
		if(initControl_lockDoc) {
			printC340M01A();
		}else{
			saveAction({'allowIncomplete':'Y'}).done(function(json){
				if(json.saveOkFlag){					
					printC340M01A();
				}
	        });
		}
	}).end().find("#btnSend").click(function(){	
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			check_C340().done(function(){ 
        			API.confirmMessage(i18n.def.confirmApply, function(result){
        	            if (result) {
        	            	flowAction({'decisionExpr':'呈主管'});	   	            	
        	        	}
        	    	});
        		});	
    		}
    	});
			
	}).end().find("#btnAccept").click(function(){
		
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");

			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.def['accept']+"</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.def['return']+"</label></p>");

			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	check_C340().done(function(){
                    		flowAction({'decisionExpr':'核定'});
                    	});	    	
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
		
	});	
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            )                
            }).done(function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
        });
	}
   
});

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		var optsPage = {};
		
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                optsPage,
                ( opts||{} )
            )                
            }).done(function(json){
            	tabForm.injectData(json);
            	
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
        });
	}else{
		return $.Deferred();
	}
}

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});

var check_C340 = function (){
	var my_dfd = $.Deferred();
	$.ajax({
        type: "POST",
        handler: _handler,
        data:{
        	formAction: "check_C340",
            mainOid: responseJSON.mainOid
        }                
        }).done(function(json){        	
        	if(json.msg){
        		CommonAPI.confirmMessage(json.msg+"<br/>"+i18n.def.confirmRun, function(b){
    	            if (b) {
    	            	my_dfd.resolve(json);     
    	            } 
    	        });
        	}else{
        		my_dfd.resolve(json);
        	}
    });
	return my_dfd.promise();
}

function printC340M01A(){
    $("input[name*='printMode'][value='1']").attr("checked", true);
    var openThickbox = $("#CLS3401PrintThickbox").thickbox({
        title: i18n.def["cls3401m01.thickbox3"],
        width: 300,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var printMode = $("#CLS3401M01Form").find("[name='printMode']:checked").val();

                //檢核畫面欄位有class="required"者且不隱藏者為必要輸入欄位。
                if (!$("#CLS3401M01Form").valid()) {
                    return;
                }
                check_C340().done(function(json){
                    if(printMode==1){
                        $.form.submit({
                            url: __ajaxHandler,
                            target : "_blank",
                            data : {
                                '_pa' : 'lmsdownloadformhandler' ,
                                'serviceName': "ContractDocService" ,
                                'oid' : responseJSON.mainOid ,
                                'fileDownloadName': (json.model_custId+"-"+json.model_custName+"-"+json.model_caseNo+"-"+json.model_ctrTypeDesc+".doc") // 用docx會不能開啟
                            }
                        });
                        $.thickbox.close();
                    }
                    else{
                        $.form.submit({
                            url: __ajaxHandler,
                            target : "_blank",
                            data : {
                                '_pa' : 'lmsdownloadformhandler' ,
                                'serviceName': "ContractDocService" ,
                                'oid' : responseJSON.mainOid ,
                                'c340m01cDiff' : json.model_c340m01cDiff ,
                                'printMode' : printMode,
                                'fileDownloadName': (json.model_custId+"-"+json.model_custName+"-"+json.model_caseNo+"-"+json.model_ctrTypeDesc+".doc") // 用docx會不能開啟
                            }
                        });
                        $.thickbox.close();
                    }

                });
            },
            "cancel": function(){
                $.thickbox.close();

            }
        }
    });
}