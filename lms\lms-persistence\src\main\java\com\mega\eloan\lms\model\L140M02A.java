/* 
 * L140M02A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 額度批覆表修改註記檔 **/
@Entity
@Table(name = "L140M02A", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L140M02A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 文件編號
	 * <p/>
	 * 額度批覆表的mainId
	 */
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 額度批覆表修改註記
	 * <p/>
	 * 101/01/02調整<br/>
	 * CLOB ( VARCHAR<br/>
	 * 記錄額度批覆表修改註記，亦即有勾選checkbox的欄位。
	 */
	/* 值域 可參考 UtilConstants.L140M02AName 裡的內容
	 命名的規則, 似乎以 tab 來劃分
	 ● 第1個tab【文件資訊】 caseBox1 , caseBox2
	 ● 第2個tab【申請內容】 caseBox2_1, caseBox2_2
	 ● 第7個tab【敘做條件異動情形】 tab07
	 */
	@Column(name = "JSONDATA", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String jsonData;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得文件編號
	 * <p/>
	 * 額度批覆表的mainId
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定文件編號
	 * <p/>
	 * 額度批覆表的mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得額度批覆表修改註記
	 * <p/>
	 * 101/01/02調整<br/>
	 * CLOB ( VARCHAR<br/>
	 * 記錄額度批覆表修改註記，亦即有勾選checkbox的欄位。
	 */
	public String getJsonData() {
		return this.jsonData;
	}

	/**
	 * 設定額度批覆表修改註記
	 * <p/>
	 * 101/01/02調整<br/>
	 * CLOB ( VARCHAR<br/>
	 * 記錄額度批覆表修改註記，亦即有勾選checkbox的欄位。
	 **/
	public void setJsonData(String value) {
		this.jsonData = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
