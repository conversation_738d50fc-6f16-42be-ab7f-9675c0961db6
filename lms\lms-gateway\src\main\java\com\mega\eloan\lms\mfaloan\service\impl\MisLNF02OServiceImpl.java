package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLNF02OService;

@Service
public class MisLNF02OServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF02OService {

	/**
	 * J-108-0107_05097_B1002 Web e-Loan企金授信新核准往來企金客戶數統計表新增動用資訊與調整效能
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findByCntrNoFetchOne(String cntrNo) {
		return getJdbc().queryForMap("LNF02O.selByCntrNoFetchOne",
				new String[] { cntrNo });

	}

}
