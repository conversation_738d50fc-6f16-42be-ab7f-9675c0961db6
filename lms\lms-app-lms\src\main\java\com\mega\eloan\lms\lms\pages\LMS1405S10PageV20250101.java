/* 
 * LMS1401S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * J-113-0499 風控風險權數
 * </pre>
 * 
 * @since 2021/10/
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/10/,009301,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1405S10PageV20250101/{page}")
public class LMS1405S10PageV20250101 extends AbstractEloanForm {

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(parameters);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// remove("_headerPanel");
		model.addAttribute("showHeader", false); // 不顯示 _headerPanel
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
	
	private static final long serialVersionUID = -4024257163623646201L;
}
