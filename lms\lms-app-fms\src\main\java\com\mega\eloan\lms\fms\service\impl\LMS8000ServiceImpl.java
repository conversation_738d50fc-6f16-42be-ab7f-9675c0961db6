package com.mega.eloan.lms.fms.service.impl;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.gwclient.EloanHttpGetClient;
import com.mega.eloan.common.gwclient.EloanHttpGetReqMessage;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DeletedMeta;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DeletedMetaService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CentralBankControlService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L160M01BDao;
import com.mega.eloan.lms.dao.L260M01ADao;
import com.mega.eloan.lms.dao.L260M01BDao;
import com.mega.eloan.lms.dao.L260M01CDao;
import com.mega.eloan.lms.dao.L260M01DDao;
import com.mega.eloan.lms.dao.L260S01ADao;
import com.mega.eloan.lms.dao.L260S01BDao;
import com.mega.eloan.lms.dao.L260S01CDao;
import com.mega.eloan.lms.dao.L260S01DDao;
import com.mega.eloan.lms.dao.L260S01EDao;
import com.mega.eloan.lms.dao.L260S01FDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS8000M01Page;
import com.mega.eloan.lms.fms.pages.LMS8000V01Page;
import com.mega.eloan.lms.fms.service.LMS8000Service;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.service.MisELF517Service;
import com.mega.eloan.lms.mfaloan.service.MisELF600Service;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L260M01A;
import com.mega.eloan.lms.model.L260M01B;
import com.mega.eloan.lms.model.L260M01C;
import com.mega.eloan.lms.model.L260M01D;
import com.mega.eloan.lms.model.L260S01A;
import com.mega.eloan.lms.model.L260S01B;
import com.mega.eloan.lms.model.L260S01C;
import com.mega.eloan.lms.model.L260S01D;
import com.mega.eloan.lms.model.L260S01E;
import com.mega.eloan.lms.model.L260S01F;
import com.mega.eloan.lms.obsdb.service.ObsdbELF601Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF603Service;
import com.mega.eloan.lms.ods.service.OdsdbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.enums.IGridEnum;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowDefinitionImpl;
import tw.com.jcs.flow.core.FlowEngineImpl;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.core.FlowInstanceImpl;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 貸後管理作業
 * 
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service("LMS8000Service")
public class LMS8000ServiceImpl extends AbstractCapService implements
		LMS8000Service {

	@Resource
	FlowService flowService;

	@Resource
	TempDataService tempDataService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELF517Service misElf517Service;

	@Resource
	DocLogService docLogService;

	@Resource
	DocFileService docFileService;
	
	@Resource
	DeletedMetaService deletedMetaService;

	@Resource
	FlowEngineImpl engine;

	@Resource
	DocCheckService docCheckService;
	
	@Resource
	MisStoredProcService msps;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	L260M01ADao l260m01aDao;

	@Resource
	L260M01BDao l260m01bDao;

	@Resource
	L260M01CDao l260m01cDao;

	@Resource
	L260M01DDao l260m01dDao;

	@Resource
	L260S01ADao l260s01aDao;

	@Resource
	L260S01BDao l260s01bDao;

	@Resource
	L260S01CDao l260s01cDao;

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	@Resource
	L260S01DDao l260s01dDao;

	@Resource
	L260S01EDao l260s01eDao;
	
	// J-113-0035 為利ESG案件之貸後管控,
	// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
	@Resource
	L260S01FDao l260s01fDao;

	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L160M01BDao l160m01bDao;
	
	@Resource
	C160M01BDao c160m01bDao;

	@Resource
	C160M01ADao c160m01aDao;	

	@Resource
	UserInfoService userService;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	OdsdbBASEService odsdbBASEService;

	@Resource
	LMSService lmsService;

	@Resource
	BranchService branchService;

	@Resource
	ObsdbELF601Service obsdbELF601Service;

	@Resource
	ObsdbELF603Service obsdbELF603Service;
	
	@Resource
	SysParameterService sysParamService;

	@Resource
	EloanHttpGetClient eloanHttpGetClient;

	@Resource
	MisELF600Service misELF600Service;
	
	@Resource 
	SysParameterService sysParameterService;
	
	@Resource
	CentralBankControlService centralBankControlService;

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	@Resource
	CodeTypeService codetypeService;

	private static Logger logger = LoggerFactory
			.getLogger(LMS8000ServiceImpl.class);
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L260M01A) {
					if (Util.isEmpty(((L260M01A) model).getOid())) {
						((L260M01A) model).setCreator(user.getUserId());
						((L260M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l260m01aDao.save((L260M01A) model);

						flowService.start("LMS8000Flow",
								((L260M01A) model).getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						((L260M01A) model).setUpdater(user.getUserId());
						((L260M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l260m01aDao.save((L260M01A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L260M01A) model)
									.getMainId());
							docLogService.record(((L260M01A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
				} else if (model instanceof L260M01B) {
					((L260M01B) model).setUpdater(user.getUserId());
					((L260M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l260m01bDao.save((L260M01B) model);
				} else if (model instanceof L260M01C) {
					((L260M01C) model).setUpdater(user.getUserId());
					((L260M01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l260m01cDao.save((L260M01C) model);
				} else if (model instanceof L260M01D) {
					((L260M01D) model).setUpdater(user.getUserId());
					((L260M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l260m01dDao.save((L260M01D) model);
				} else if (model instanceof L260S01B) {
					((L260S01B) model).setUpdater(user.getUserId());
					((L260S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l260s01bDao.save((L260S01B) model);
				} else if (model instanceof L260S01C) {
					((L260S01C) model).setUpdater(user.getUserId());
					((L260S01C) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l260s01cDao.save((L260S01C) model);
				} else if (model instanceof L260S01D) {
					// J-112-0307
					// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
					((L260S01D) model).setUpdater(user.getUserId());
					((L260S01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					((L260S01D) model).setRandomCode_S01D(IDGenerator
							.getRandomCode());
					l260s01dDao.save((L260S01D) model);
				} else if (model instanceof L260S01E) {
					((L260S01E) model).setUpdater(user.getUserId());
					((L260S01E) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l260s01eDao.save((L260S01E) model);
				} else if (model instanceof L260S01F) {
					// J-113-0035 為利ESG案件之貸後管控,
					// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
					((L260S01F) model).setUpdater(user.getUserId());
					((L260S01F) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l260s01fDao.save((L260S01F) model);
				}

			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L260M01A.class) {
			return l260m01aDao.findPage(search);
		} else if (clazz == L260M01B.class) {
			return l260m01bDao.findPage(search);
		} else if (clazz == L260M01C.class) {
			return l260m01cDao.findPage(search);
		} else if (clazz == L260M01D.class) {
			return l260m01dDao.findPage(search);
		} else if (clazz == L260S01A.class) {
			return l260s01aDao.findPage(search);
		} else if (clazz == L260S01B.class) {
			return l260s01bDao.findPage(search);
		} else if (clazz == L260S01C.class) {
			return l260s01cDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId) {
		if (clazz == L260M01A.class) {
			return (T) l260m01aDao.findByMainId(mainId);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L260M01A.class) {
			return (T) l260m01aDao.findByOid(oid);
		} else if (clazz == L260M01B.class) {
			return (T) l260m01bDao.findByOid(oid);
		} else if (clazz == L260M01C.class) {
			return (T) l260m01cDao.findByOid(oid);
		} else if (clazz == L260M01D.class) {
			return (T) l260m01dDao.findByOid(oid);
		} else if (clazz == L260S01A.class) {
			return (T) l260s01aDao.findByOid(oid);
		} else if (clazz == L260S01B.class) {
			return (T) l260s01bDao.findByOid(oid);
		} else if (clazz == L260S01C.class) {
			return (T) l260s01cDao.findByOid(oid);
		} else if (clazz == L260S01D.class) {
			// J-112-0307
			// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
			return (T) l260s01dDao.findByOid(oid);
		} else if (clazz == L260S01E.class) {
			return (T) l260s01eDao.findByOid(oid);
		} else if (clazz == L260S01F.class) {
			// J-113-0035 為利ESG案件之貸後管控,
			// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
			return (T) l260s01fDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L260M01A.class) {
			return l260m01aDao.findByIndex01(mainId);
		} else if (clazz == L260M01B.class) {
			return l260m01bDao.findByMainId(mainId);
		} else if (clazz == L260M01C.class) {
			return l260m01cDao.findByMainId(mainId, false);
		} else if (clazz == L260M01D.class) {
			return l260m01dDao.findByMainId(mainId, false);
		} else if (clazz == L260S01A.class) {
			return l260s01aDao.findByMainId(mainId, false);
		} else if (clazz == L260S01B.class) {
			return l260s01bDao.findByMainId(mainId, false);
		} else if (clazz == L260S01C.class) {
			return l260s01cDao.findByMainId(mainId, false);
		} else if (clazz == L260S01D.class) {
			// J-112-0307
			// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
			return l260s01dDao.findByMainId(mainId, false);
		} else if (clazz == L260S01E.class) {
			return l260s01eDao.findByMainId(mainId, false);
		} else if (clazz == L260S01F.class) {
			// J-113-0035 為利ESG案件之貸後管控,
			// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
			return l260s01fDao.findByMainId(mainId, false);
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	@Override
	public boolean deleteL260m01as(String[] oids) throws CapMessageException {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());
		Properties popLMS8000V01PAge = MessageBundleScriptCreator
		.getComponentResource(LMS8000V01Page.class);		
		List<L260M01A> l260m01as = new ArrayList<L260M01A>();
		List<L260M01C> l260m01cs = new ArrayList<L260M01C>();
		List<L260M01D> l260m01ds = new ArrayList<L260M01D>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L260M01A l260m01a = (L260M01A) findModelByOid(L260M01A.class,
					oids[i]);
			
			/**
			 * J-112-0569 貸後管理已核准案件新增開放退回修改功能
			 * 
			 * 避免使用者將退回後的資料刪除
			 */
			StringBuffer errMsg = new StringBuffer();
			if (Util.isNotEmpty(l260m01a)
					&& Util.equals(Util.trim(l260m01a.getHasRtnModify()), "Y")) {

				errMsg.append(popLMS8000V01PAge.getProperty("L260M01A.custId"))
						.append("：").append(l260m01a.getCustId()).append("，"); // 客戶統一編號
				errMsg.append(popLMS8000V01PAge.getProperty("L260M01A.cntrNo"))
						.append("：").append(l260m01a.getCntrNo()).append("，"); // 額度序號

				String strTmp = popLMS8000V01PAge
						.getProperty("L260M01A.loanNo");// 放款帳號
				if (isOverSea) {
					strTmp = popLMS8000V01PAge
							.getProperty("L260M01A.loanNoOvs"); // 動撥編號(REF)
				}
				errMsg.append(strTmp).append("：").append(l260m01a.getLoanNo())
						.append("<br/>");

				errMsg.append(popLMS8000V01PAge.getProperty("L260M01A.error04")); // 此為退回修改案件，不可刪除

				throw new CapMessageException(errMsg.toString(), getClass());
			}
	
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l260m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l260m01a.setUpdater(user.getUserId());
			l260m01as.add(l260m01a);
			docLogService.record(l260m01a.getOid(), DocLogEnum.DELETE);

			String mainId = l260m01a.getMainId();

			List<L260M01C> l260m01cList = (List<L260M01C>) findListByMainId(
					L260M01C.class, mainId);
			if (!Util.isEmpty(l260m01cList)) {
				for (L260M01C l260m01c : l260m01cList) {
					l260m01c.setDeletedTime(CapDate.getCurrentTimestamp());
					l260m01c.setUpdater(user.getUserId());
					l260m01cs.add(l260m01c);
					docLogService.record(l260m01c.getOid(), DocLogEnum.DELETE);
				}
			}

			List<L260M01D> l260m01dList = (List<L260M01D>) findListByMainId(
					L260M01D.class, mainId);
			if (!Util.isEmpty(l260m01dList)) {
				for (L260M01D l260m01d : l260m01dList) {
					l260m01d.setDeletedTime(CapDate.getCurrentTimestamp());
					l260m01d.setUpdater(user.getUserId());
					l260m01ds.add(l260m01d);
					docLogService.record(l260m01d.getOid(), DocLogEnum.DELETE);
				}
			}
		}
		if (!l260m01as.isEmpty()) {
			l260m01aDao.save(l260m01as);
			flag = true;
		}
		if (!l260m01cs.isEmpty()) {
			l260m01cDao.save(l260m01cs);
		}
		if (!l260m01ds.isEmpty()) {
			l260m01dDao.save(l260m01ds);
		}
		return flag;
	}

	@Override
	public boolean cntrNoNotOK(L260M01A l260m01a) {

		boolean cntrNoNotOK = true;

		String sendBossChkCntrnoFlg = Util.trim(sysParameterService
				.getParamValue("LMS_J1130159_SENDBOSSCHKCNTRNO"));
		if (Util.equals(sendBossChkCntrnoFlg, "Y")) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String brNo = user.getUnitNo();
			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
					.getBranch(brNo).getBrNoFlag());

			String custId = Util.nullToSpace(l260m01a.getCustId());
			String dupNo = Util.nullToSpace(l260m01a.getDupNo());
			String cntrNo = Util.trim(l260m01a.getCntrNo());

			if (Util.isEmpty(cntrNo)) {
				// 20240521，ID階層的貸後，因此種資料是從帳務來，授審處的金襄理表示不檢核
				cntrNoNotOK = false;
				return cntrNoNotOK;
			}

			if (!custId.isEmpty() && !dupNo.isEmpty()) {
				// 若是額度序號已存在於帳務，就不檢核
				List<Map<String, Object>> data = null;
				if (isOverSea) {
					data = dwdbBASEService.findDW_ASLNDNEWOVS_ByBrNoCustId(
							brNo, custId, dupNo, "", "");
				} else {
					data = misdbBASEService.getContractAndLoanNo(custId, dupNo);
				}
				for (Map<String, Object> map : data) {
					String cntrNoDB = Util.trim(MapUtils.getString(map,
							"CONTRACT"));
					if (Util.equals(cntrNoDB, cntrNo)) {
						cntrNoNotOK = false;
						return cntrNoNotOK;
					}
				}
			} else {
				cntrNoNotOK = false;
				return cntrNoNotOK;
			}

			// 企金動用審核表(國內、海外)
			List<L160M01B> l160m01bList = l160m01bDao.findByCntrNo(cntrNo);
			if (Util.isNotEmpty(l160m01bList) && l160m01bList.size() > 0) {
				for (L160M01B l160m01b : l160m01bList) {
					L160M01A l160m01a = l160m01aDao.findByMainId(l160m01b
							.getMainId());
					if (Util.isNotEmpty(l160m01a)) {
						if (Util.equals(l160m01a.getDocStatus(),
								CreditDocStatusEnum.海外_已核准)
								|| Util.equals(l160m01a.getDocStatus(),
										CreditDocStatusEnum.先行動用_已覆核)) {
							cntrNoNotOK = false;
							return cntrNoNotOK;
						}
					}
				}
			}

			if (!isOverSea) {
				// 消金動用審核表(國內)
				List<C160M01B> c160m01bList = c160m01bDao.findByCntrNo(cntrNo);
				if (Util.isNotEmpty(c160m01bList) && c160m01bList.size() > 0) {
					for (C160M01B c160m01b : c160m01bList) {
						C160M01A c160m01a = c160m01aDao.findByMainId(c160m01b
								.getMainId());
						if (Util.isNotEmpty(c160m01a)) {
							if (Util.equals(c160m01a.getDocStatus(),
									CLSDocStatusEnum.已核准)
									|| Util.equals(c160m01a.getDocStatus(),
											CLSDocStatusEnum.先行動用_已覆核)) {
								cntrNoNotOK = false;
								return cntrNoNotOK;
							}
						}
					}
				}
			}

		} else {
			// 檢核不啟用
			cntrNoNotOK = false;
			return cntrNoNotOK;
		}

		return cntrNoNotOK;
	}
	
	@Override
	public String rtnModifyL260m01as(String[] oids, String rtnModifyReason)
			throws CapException {

		logger.info("++++++++++退回流程開始+++++++++++++++");

		String returnMsg = "";
		String errorMsg = "";

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());

		Class className = L120M01A.class;
		Class enumClassName = CreditDocStatusEnum.class;

		for (String oid : oids) {

			L260M01A l260m01a = l260m01aDao.findByOid(oid);
			Meta meta = l260m01a;

			if (meta == null) {
				logger.debug("找不到主檔文件 class:{},oid:{}", className, oid);
				errorMsg = "找不到主檔文件，oid=" + oid;
				throw new CapMessageException(errorMsg, getClass());
			}

			// 寫LOG COM.DELETEDMETA
			DeletedMeta deletedMeta = new DeletedMeta(meta);
			deletedMeta.setDeleter(user.getUserId());
			deletedMeta.setDeletedTime(CapDate.getCurrentTimestamp());

			// 避免有NULL欄位
			String[] fieldNames = new String[] { "uid", "mainId", "typCd",
					"custId", "dupNo", "custName", "unitType", "ownBrId",
					"docStatus", "randomCode", "docURL", "txCode", "isClosed",
					"creator", "createTime", "updater", "updateTime",
					"approver", "approveTime", "deletedTime" };
			for (String fieldName : fieldNames) {
				if (Util.equals("createTime", fieldName)
						|| Util.equals("updateTime", fieldName)
						|| Util.equals("approveTime", fieldName)
						|| Util.equals("deletedTime", fieldName)) {
					deletedMeta.set(
							fieldName,
							meta.get(fieldName) == null ? CapDate
									.getCurrentTimestamp() : meta
									.get(fieldName));
				} else if (Util.equals("isClosed", fieldName)) {
					deletedMeta.set(fieldName, meta.isClosed() ? "Y" : "N");
				} else {
					deletedMeta.set(fieldName, Util.trim(meta.get(fieldName)));
				}

			}

			JSONObject json = new JSONObject();
			// 退回的主表
			json.put("actionType", "backApproveL120m01a");
			json.put("caseType", "L260M01A");
			deletedMeta.setFfbody(json.toString());
			deletedMetaService.save(deletedMeta);

			// 檢查文件lock
			List<DocOpener> openerl = docCheckService.findByMainId(meta
					.getMainId());

			// 檢核文件開啟者是否為同一人
			for (int i = openerl.size() - 1; i >= 0; i--) {
				DocOpener opener = openerl.get(i);
				if (Util.trim(user.getUserId()).equals(
						Util.trim(opener.getOpener()))) {
					// 同一人時
					if (OpenTypeCode.Readonly.getCode().equals(
							opener.getOpenType())) {
						CapAjaxFormResult res = new CapAjaxFormResult();
						res.set("openerLockDoc", true);
					}
					break;
				} else if (OpenTypeCode.Writing.getCode().equals(
						opener.getOpenType())) {

					// 此文件正由 [$\{userId\}-$\{userName\}]開啟中!系統將以唯讀狀態開啟此文件。

					errorMsg = "此文件正由 [" + opener.getOpener() + "-"
							+ userInfoService.getUserName(opener.getOpener())
							+ "]開啟中!系統將以唯讀狀態開啟此文件。";
					throw new CapMessageException(errorMsg, getClass());
				}
			}

			// 前置作業
			try {
				List<FlowInstance> histroyFlow = flowService.createQuery()
						.id(oid).history().queryForList();

				eloandbBASEService.doWorkUnapp(oid, false, histroyFlow);

			} catch (Exception e) {
				logger.debug(e.getMessage());
				throw new CapMessageException(e.getMessage(), getClass());
			}

			FlowInstance inst = flowService.createQuery().id(oid).query();
			if (inst == null) {
				logger.debug(" FlowInstance is null oid ====>" + oid);
				errorMsg = " FlowInstance is null oid ====>" + oid;
				throw new CapMessageException(errorMsg, getClass());
			}

			// 取得目前的流程圖
			Map<String, Object> rowData = eloandbBASEService
					.selNowFlowWinst(oid);
			if (rowData == null) {
				logger.debug(" not find FlowWinst " + oid);
				errorMsg = " not find FlowWinst " + oid;
				throw new CapMessageException(errorMsg, getClass());
			}

			String defName = (String) rowData.get("DEFNAME");
			// 取得目前流程定義檔
			FlowDefinitionImpl definition = engine.getDefinition(defName);
			// 取得記憶體中的流程，並帶入正確的流程圖
			FlowInstanceImpl instant = engine.getPooledInstance(oid);
			if (instant != null) {
				instant.setDefinition(definition);
			}
			String state = inst.getState();
			String docStatus = definition.getNodes().get(state).getStatus();

			// 複製退回紀錄
			this.copyL260m01ATOHistory(meta.getMainId(), rtnModifyReason,
					isOverSea);

			if (!Util.isEmpty(docStatus)) {
				Enum statusEnum = Enum.valueOf(enumClassName, docStatus);
				meta.setDocStatus(statusEnum.toString());
				meta.setOwnBrId(inst.getDeptId());

				returnMsg = "{" + inst.getDeptId() + " "
						+ branchService.getBranchName(inst.getDeptId())
						+ "} next state" + docStatus;

				this.save(meta);
			} else {
				logger.debug("[not find docStatus] node state is ===> " + state);
				errorMsg = "[not find docStatus] node state is ===> " + state;
				throw new CapMessageException(errorMsg, getClass());
			}

		}

		return returnMsg;

	}
	
	@Override
	public void copyL260m01ATOHistory(String mainId, String rtnModifyReason,
			Boolean isOverSea) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String newMainId = IDGenerator.getUUID();

		// 處理L260M01A
		L260M01A oriL260m01a = l260m01aDao.findByMainId(mainId);
		if (Util.isNotEmpty(oriL260m01a)) {

			oriL260m01a.setHasRtnModify("Y");
			l260m01aDao.save(oriL260m01a);

			L260M01A newL260m01a = new L260M01A();
			try {
				DataParse.copy(oriL260m01a, newL260m01a);
			} catch (CapException e) {
				logger.error("[DataParse.copy]", e);
			}
			newL260m01a.setMainId(newMainId);
			newL260m01a.setOid(null);
			newL260m01a.setDocStatus(CreditDocStatusEnum.已覆核案件退回紀錄文件狀態.getCode());
			newL260m01a.setHasRtnModify("");		
			newL260m01a.setRtnModifyReason(Util.trim(rtnModifyReason));
			newL260m01a.setCreator(user.getUserId());
			newL260m01a.setCreateTime(CapDate.getCurrentTimestamp());
			newL260m01a.setUpdater(user.getUserId());
			newL260m01a.setUpdateTime(CapDate.getCurrentTimestamp());

			l260m01aDao.save(newL260m01a);

		}

		// 處理L260M01C
		List<L260M01C> oriL260m01cList = l260m01cDao.findByMainId(mainId, true);
		if (Util.isNotEmpty(oriL260m01cList)) {
			List<L260M01C> newL260m01cList = new ArrayList<L260M01C>();
			for (L260M01C oriL260m01c : oriL260m01cList) {
				L260M01C newL260m01c = new L260M01C();
				try {
					DataParse.copy(oriL260m01c, newL260m01c);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newL260m01c.setMainId(newMainId);
				newL260m01c.setOid(null);
				newL260m01cList.add(newL260m01c);
			}
			l260m01cDao.save(newL260m01cList);
		}

		// 處理L260M01D
		List<L260M01D> oriL260m01dList = l260m01dDao.findByMainId(mainId, true);
		List<L260M01D> newL260m01dList = new ArrayList<L260M01D>();
		if (Util.isNotEmpty(oriL260m01dList)) {
			for (L260M01D oirL260m01d : oriL260m01dList) {
				L260M01D newL260m01d = new L260M01D();
				try {
					DataParse.copy(oirL260m01d, newL260m01d);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newL260m01d.setMainId(newMainId);
				newL260m01d.setOid(null);
				newL260m01d.setFieldMainId(oirL260m01d.getOid());
				newL260m01dList.add(newL260m01d);
			}
			l260m01dDao.save(newL260m01dList);
		}

		// 處理附加檔案複製
		this.copyDocFile(newMainId, true, false, null);

		// 處理理財商品，海外沒有理財商品
		if (!isOverSea) {

			if (Util.isNotEmpty(newL260m01dList)) {
				List<L260S01A> oriL260s01aList = new ArrayList<L260S01A>();
				List<L260S01A> newL260s01aList = new ArrayList<L260S01A>();
				for (L260M01D newL260M01D : newL260m01dList) {
					oriL260s01aList = l260s01aDao.findByMainId(
							newL260M01D.getFieldMainId(), true);
					if (Util.isNotEmpty(oriL260s01aList)) {
						for (L260S01A oriL260s01a : oriL260s01aList) {
							L260S01A newL260s01a = new L260S01A();
							try {
								DataParse.copy(oriL260s01a, newL260s01a);
							} catch (CapException e) {
								logger.error("[DataParse.copy]", e);
							}
							newL260s01a.setMainId(newL260M01D.getOid());
							newL260s01a.setOid(null);
							newL260s01aList.add(newL260s01a);
						}
					}
				}
				l260s01aDao.save(newL260s01aList);
			}

		}

		// 處理餘屋貸款，海外沒有餘屋貸款
		if (!isOverSea) {

			if (Util.isNotEmpty(newL260m01dList)) {
				List<L260S01B> oriL260s01bList = new ArrayList<L260S01B>();
				List<L260S01B> newL260s01bList = new ArrayList<L260S01B>();
				for (L260M01D newL260M01D : newL260m01dList) {
					if (!this.isCaseMark(newL260M01D, "03")) {
						continue;
					}
					oriL260s01bList = l260s01bDao.findByMainId(
							newL260M01D.getFieldMainId(), true);
					if (Util.isNotEmpty(oriL260s01bList)) {
						for (L260S01B oriL260s01b : oriL260s01bList) {
							L260S01B newL260s01b = new L260S01B();
							try {
								DataParse.copy(oriL260s01b, newL260s01b);
							} catch (CapException e) {
								logger.error("[DataParse.copy]", e);
							}
							newL260s01b.setMainId(newL260M01D.getOid());
							newL260s01b.setOid(null);
							newL260s01bList.add(newL260s01b);
						}
					}
				}
				l260s01bDao.save(newL260s01bList);
			}

		}

		// J-111-0025 實價登錄 海外沒有
		if (!isOverSea) {

			if (Util.isNotEmpty(newL260m01dList)) {
				List<L260S01C> oriL260s01cList = new ArrayList<L260S01C>();
				List<L260S01C> newL260s01cList = new ArrayList<L260S01C>();
				for (L260M01D newL260M01D : newL260m01dList) {
					if (!this.isCaseMark(newL260M01D, "02")) {
						continue;
					}
					oriL260s01cList = l260s01cDao.findByMainId(
							newL260M01D.getFieldMainId(), true);
					if (Util.isNotEmpty(oriL260s01cList)) {
						for (L260S01C oriL260s01c : oriL260s01cList) {
							L260S01C newL260s01c = new L260S01C();
							try {
								DataParse.copy(oriL260s01c, newL260s01c);
							} catch (CapException e) {
								logger.error("[DataParse.copy]", e);
							}
							newL260s01c.setMainId(newL260M01D.getOid());
							newL260s01c.setOid(null);
							newL260s01cList.add(newL260s01c);
						}
					}
				}
				l260s01cDao.save(newL260s01cList);
			}

		}

		// 公司訪問記錄表，海外沒有
		if (!isOverSea) {

			if (Util.isNotEmpty(newL260m01dList)) {
				List<L260S01D> oriL260s01dList = new ArrayList<L260S01D>();
				List<L260S01D> newL260s01dList = new ArrayList<L260S01D>();
				List<L260S01E> oriL260s01eList = new ArrayList<L260S01E>();
				List<L260S01E> newL260s01eList = new ArrayList<L260S01E>();
				for (L260M01D newL260M01D : newL260m01dList) {
					if (Util.notEquals(Util.trim(newL260M01D.getFollowKind()),
							UtilConstants.Lms8000m01_fllowKind.公司訪問紀錄表限ID階層)) {
						continue;
					}
					oriL260s01dList = l260s01dDao.findByMainId(
							newL260M01D.getFieldMainId(), true);
					if (Util.isNotEmpty(oriL260s01dList)) {
						for (L260S01D oriL260s01d : oriL260s01dList) {
							L260S01D newL260s01d = new L260S01D();
							try {
								DataParse.copy(oriL260s01d, newL260s01d);
							} catch (CapException e) {
								logger.error("[DataParse.copy]", e);
							}
							newL260s01d.setMainId(newL260M01D.getOid());
							newL260s01d.setOid(null);
							newL260s01dList.add(newL260s01d);
							l260s01dDao.save(newL260s01dList);

							oriL260s01eList = l260s01eDao.findByMainId(
									oriL260s01d.getOid(), true);
							for (L260S01E oirL260s01e : oriL260s01eList) {
								L260S01E newL260s01e = new L260S01E();
								try {
									DataParse.copy(oirL260s01e, newL260s01e);
								} catch (CapException e) {
									logger.error("[DataParse.copy]", e);
								}
								newL260s01e.setMainId(newL260s01d.getOid());
								newL260s01e.setOid(null);
								newL260s01eList.add(newL260s01e);
							}
							l260s01eDao.save(newL260s01eList);

						}
					}
				}

			}
		}
		
		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		// ESG追蹤紀錄
		if (Util.isNotEmpty(newL260m01dList)) {
			List<L260S01F> oriL260s01fList = new ArrayList<L260S01F>();
			List<L260S01F> newL260s01fList = new ArrayList<L260S01F>();
			for (L260M01D newL260M01D : newL260m01dList) {
				oriL260s01fList = l260s01fDao.findByMainId(
						newL260M01D.getFieldMainId(), true);
				if (Util.isNotEmpty(oriL260s01fList)) {
					for (L260S01F oriL260s01f : oriL260s01fList) {
						L260S01F newL260s01f = new L260S01F();
						try {
							DataParse.copy(oriL260s01f, newL260s01f);
						} catch (CapException e) {
							logger.error("[DataParse.copy]", e);
						}
						newL260s01f.setMainId(newL260M01D.getOid());
						newL260s01f.setOid(null);
						newL260s01fList.add(newL260s01f);
					}
				}
			}
			l260s01fDao.save(newL260s01fList);
		}

	}
	
	@Override
	@SuppressWarnings("unchecked")
	public void copyDocFile(String l260m01aMainid, boolean isQuery,
			boolean isRs, List<L260M01D> l260m01dList) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 處理附加檔案複製
		List<L260M01D> m01dList = null;
		if (l260m01dList != null && !l260m01dList.isEmpty()) {
			// 處理特定紀錄
			m01dList = l260m01dList;
		} else {
			m01dList = (List<L260M01D>) this.findListByMainId(
					L260M01D.class, l260m01aMainid);
		}
		if (m01dList != null && m01dList.size() > 0) {
			for (L260M01D l260m01d : m01dList) {
				// 因為上傳時是用oid放在 LMS.BDocFile 的 mainId
				String oid = l260m01d.getOid();
				String filedMainId = Util.trim(l260m01d.getFieldMainId());// l260m01d.getDataSrc();
																			// ELF602的Elf602_fieldMainId
																			// =
																			// 上傳的L260M01D的OID

				L260M01D l260m01d_old = this.findModelByOid(
						L260M01D.class, filedMainId);

				if (Util.isNotEmpty(filedMainId)) {
					// J-111-0025_05097_B1001
					// 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
					// 舊版:DocFile 的MAINID = L260M01D 的FieldMainId，PID = NULL
					List<DocFile> listFile1 = docFileService.findByIDAndPid(
							filedMainId, null);
					// 新版:DocFile 的MAINID = l260m01d_old 的MAINID，PID =
					// l260m01d_old 的OID(也等於Elf602_fieldMainId)
					List<DocFile> listFile2 = null;
					if (l260m01d_old != null) {
						listFile2 = docFileService
								.findByIDAndPid(l260m01d_old.getMainId(),
										l260m01d_old.getOid());
					}

					List<DocFile> listFile = new ArrayList<DocFile>();

					if (listFile1 != null && !listFile1.isEmpty()) {

						listFile.addAll(listFile1);
					}
					if (listFile2 != null && !listFile2.isEmpty()) {
						listFile.addAll(listFile2);
					}

					// J-111-0025_05097_B1001
					// 為增進eloan擔保品及貸後管理查詢時價登入作業效率,增加相關作業需求
					// List<DocFile> listFile =
					// docFileService.findByIDAndName(filedMainId,
					// "postLoanCertified", "");
					if (!listFile.isEmpty()) {
						for (DocFile file : listFile) {
							String fid = file.getOid();
							DocFile newFile = new DocFile();
							try {
								DataParse.copy(file, newFile);
							} catch (CapException e) {
								logger.error("[DataParse.copy]", e);
							}
							// newFile.setMainId(oid);
							// newFile.setBranchId(isQuery ? "AAA" :
							// user.getUnitNo());
							// newFile.setOid(null);

							newFile.setMainId(l260m01aMainid);
							newFile.setPid(oid);
							newFile.setBranchId(user.getUnitNo());
							newFile.setCrYear(CapDate.getCurrentDate("yyyy-MM"));
							newFile.setOid(null);

							docFileService.copy(fid, newFile);
						}
					}
					// List<DocFile> listFile2 =
					// docFileService.findByIDAndName(filedMainId,
					// "postLoanRepay", "");
				}
			}
		}
	}

	@Override
	public void saveL260m01cList(List<L260M01C> list) {
		// for (L260M01C l260m01c : list) {
		// l260m01c.setUpdater(user.getUserId());
		// l260m01c.setUpdateTime(CapDate.getCurrentTimestamp());
		// }
		l260m01cDao.save(list);
	}

	@Override
	public void saveL260m01dList(List<L260M01D> list) {
		// for (L260M01D l260m01d : list) {
		// l260m01d.setUpdater(user.getUserId());
		// l260m01d.setUpdateTime(CapDate.getCurrentTimestamp());
		// }
		l260m01dDao.save(list);
	}

	@Override
	public void saveL260s01dList(List<L260S01D> list) {
		l260s01dDao.save(list);
	}

	@Override
	public void saveL260s01eList(List<L260S01E> list) {
		l260s01eDao.save(list);
	}
	
	@Override
	public void saveL260s01fList(List<L260S01F> list) {
		l260s01fDao.save(list);
	}

	@Override
	public void deleteL260m01bs(List<L260M01B> l260m01bs, boolean isAll) {
		if (isAll) {
			l260m01bDao.delete(l260m01bs);
		} else {
			List<L260M01B> L260M01BsOld = new ArrayList<L260M01B>();
			for (L260M01B l260m01b : l260m01bs) {
				String staffJob = l260m01b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L260M01BsOld.add(l260m01b);
				}
			}
			l260m01bDao.delete(L260M01BsOld);
		}
	}

	@Override
	public void saveL260m01bList(List<L260M01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L260M01B l260m01b : list) {
			l260m01b.setUpdater(user.getUserId());
			l260m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l260m01bDao.save(list);
	}

	@Override
	public L260M01B findL260m01b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l260m01bDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}

	@SuppressWarnings("unchecked")
	@Override
	public void flowAction(String mainOid, L260M01A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		try {
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS8000Flow",
						((L260M01A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					if (upMis) {
						L260M01A l260m01a = (L260M01A) findModelByOid(
								L260M01A.class, mainOid);
						// 動審表簽章欄檔取得人員職稱
						List<L260M01B> l260m01blist = l260m01bDao
								.findByMainId(l260m01a.getMainId());
						String apprId = "";
						String reCheckId = "";

						for (L260M01B l260m01b : l260m01blist) {
							String StaffJob = Util.trim(l260m01b.getStaffJob());// 取得人員職稱
							String StaffNo = Util.trim(l260m01b.getStaffNo());// 取得行員代碼
							if (Util.equals(StaffJob, "L1")) {// 分行經辦
								apprId = StaffNo;
							} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
								reCheckId = StaffNo;
							}
						}
						// 若人員職稱為空值改取m01a上的人員資料
						if (Util.isEmpty(apprId)) {
							apprId = l260m01a.getUpdater();
						}
						if (Util.isEmpty(reCheckId)) {
							reCheckId = l260m01a.getApprover();
						}

						// 上傳MIS
						String brNo = user.getUnitNo();
						boolean isOverSea = UtilConstants.BrNoType.國外
								.equals(branchService.getBranch(brNo)
										.getBrNoFlag());

						String mainId = l260m01a.getMainId();
						List<L260M01C> l260m01clist = (List<L260M01C>) this
								.findListByMainIdNotDel(L260M01C.class, mainId,
										true);
						if (l260m01clist != null && l260m01clist.size() > 0) {
							for (L260M01C l260m01c : l260m01clist) {
								if (l260m01c.getDeletedTime() != null) {
									continue;
								}
								// 20200619 金襄理說 有做修改的資料才上傳 601.602
								// if(Util.notEquals(l260m01c.getCheckYN(), "Y")
								// &&
								// Util.notEquals(l260m01c.getStatus(), "C")){
								if (Util.notEquals(l260m01c.getCheckYN(), "Y")) {
									continue;
								}
								String chkUnid = l260m01c.getUnid();
								if (Util.equals(
										Util.nullToSpace(l260m01c.getUnid()),
										"new")) {
									chkUnid = l260m01c.getOid(); // 避免之後做退回功能
																	// 新增的資料再insert會
																	// duplicate
								}
								if (isOverSea) {
									ELF601 elf601 = obsdbELF601Service
											.getElf601ByUnid(brNo, chkUnid);
									if (elf601 != null
											&& Util.isNotEmpty(elf601)) {
										this.convertL260m01cToELF601(l260m01a,
												l260m01c, elf601, true,
												isOverSea);
										obsdbELF601Service.deleteELF601(brNo,
												chkUnid);
										obsdbELF601Service.insertELF601(brNo,
												elf601);
									} else {
										elf601 = new ELF601();
										this.convertL260m01cToELF601(l260m01a,
												l260m01c, elf601, false,
												isOverSea);
										obsdbELF601Service.insertELF601(brNo,
												elf601);
									}
								} else {
									ELF601 elf601 = misdbBASEService
											.getElf601ByUnid(chkUnid);
									if (elf601 != null
											&& Util.isNotEmpty(elf601)) {
										this.convertL260m01cToELF601(l260m01a,
												l260m01c, elf601, true,
												isOverSea);
										misdbBASEService.deleteELF601(chkUnid);
										misdbBASEService.insertELF601(elf601);
									} else {
										elf601 = new ELF601();
										this.convertL260m01cToELF601(l260m01a,
												l260m01c, elf601, false,
												isOverSea);
										misdbBASEService.insertELF601(elf601);
									}
								}
							}
						}

						// J-113-0035 為利ESG案件之貸後管控,
						// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
						boolean havefollowKind12Flag = false;
						
						List<L260M01D> l260m01dlist = (List<L260M01D>) this
								.findListByMainIdNotDel(L260M01D.class, mainId,
										true);						
						if (l260m01dlist != null && l260m01dlist.size() > 0) {														
							for (L260M01D l260m01d : l260m01dlist) {
								if (l260m01d.getDeletedTime() != null) {
									continue;
								}
								// 20200619 金襄理說 有做修改的資料才上傳 601.602
								// if(Util.notEquals(l260m01d.getCheckYN(), "Y")
								// &&
								// Util.notEquals(l260m01d.getHandlingStatus(),
								// "4")){
								if (Util.notEquals(l260m01d.getCheckYN(), "Y")) {
									continue;
								}
								
								Map<String, String> followKindMap = this
										.followKindToMap(l260m01d
												.getFollowKind());
								if(followKindMap.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)){
									// J-113-0035 為利ESG案件之貸後管控,
									// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
									havefollowKind12Flag = true;
								}
								
								String chkUnid = l260m01d.getUnid();
								if (Util.equals(
										Util.nullToSpace(l260m01d.getUnid()),
										"new")) {
									chkUnid = l260m01d.getOid(); // 避免之後做退回功能
																	// 新增的資料再insert會
																	// duplicate
								}
								if (isOverSea) {
									// 海外沒有理財商品 沒有餘屋貸款
									ELF602 elf602 = obsdbELF601Service
											.getElf602ByUnid(brNo, chkUnid);
									if (elf602 != null
											&& Util.isNotEmpty(elf602)) {
										this.convertL260m01dToELF602(l260m01a,
												l260m01d, elf602, true,
												isOverSea);
										obsdbELF601Service.deleteELF602(brNo,
												chkUnid);
										obsdbELF601Service.insertELF602(brNo,
												elf602);
									} else {
										elf602 = new ELF602();
										this.convertL260m01dToELF602(l260m01a,
												l260m01d, elf602, false,
												isOverSea);
										obsdbELF601Service.insertELF602(brNo,
												elf602);
									}
								} else {
									ELF602 elf602 = misdbBASEService
											.getElf602ByUnid(chkUnid);
									if (elf602 != null
											&& Util.isNotEmpty(elf602)) {
										this.convertL260m01dToELF602(l260m01a,
												l260m01d, elf602, true,
												isOverSea);
										misdbBASEService.deleteELF602(chkUnid);
										misdbBASEService.insertELF602(elf602);
										// 理財商品上傳DW
										this.upLoadOTS_DW_LNWM_CFM(l260m01d,
												elf602);
									} else {
										elf602 = new ELF602();
										this.convertL260m01dToELF602(l260m01a,
												l260m01d, elf602, false,
												isOverSea);
										misdbBASEService.insertELF602(elf602);
										// 理財商品上傳DW
										this.upLoadOTS_DW_LNWM_CFM(l260m01d,
												elf602);
									}
									// J-110-0497 餘屋貸款 上傳 MIS.ELF517
									this.upLoadElf517(l260m01d, elf602);

									// J-111-0025 實價登錄
									this.sendL260s01cListToCMS(l260m01d);

									// J-111-0496 央行不動產管控
									this.upLoadElf600(l260m01d);																		
								}
							}
						}
						//聯徵報送用, 只有國內需要
						if (!isOverSea && havefollowKind12Flag) {
							// J-113-0035 為利ESG案件之貸後管控,
							// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
							this.chgESGFlg(l260m01a);
						}				
						
					}
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public Map<String, Object> findLastPrint_L140M01A(String cntrNo) {
		// Map<String, String> resultMap = new HashMap<String, String>();
		// String oid = "";
		// Map<String, Object> map =
		// eloandbBASEService.findLastPrint_L140M01A(cntrNo);
		// if(map != null && !map.isEmpty()){
		// resultMap.put("OID",Util.trim(map.get("OID")));
		// }
		return eloandbBASEService.findLastPrint_L140M01A(cntrNo);
	}

	/**
	 * J-110-0005 其他敘做條件to貸後管理
	 */
	public List<Map<String, Object>> findL140S09BtoPostLoanByL140M01A(String oid) {
		return eloandbBASEService.findL140S09BtoPostLoanByL140M01A(oid);
	}

	@Override
	public L260M01A findL260m01a(String custId, String dupNo, String cntrNo,
			String loanNo) {
		List<L260M01A> list = l260m01aDao.findByIndex02(custId, dupNo, cntrNo,
				loanNo);
		L260M01A r = new L260M01A();
		if (!Util.isEmpty(list) && list.size() > 0) {
			r = list.get(0);
		}
		return r;
	}

	@Override
	public L260M01A findL260m01a_notEqualsDocstatus(String custId,
			String dupNo, String cntrNo, String loanNo, String[] docStatusAry,
			String branchId) {

		List<L260M01A> list = l260m01aDao.findByNotEqualsDocsStatus(custId,
				dupNo, cntrNo, loanNo, docStatusAry, branchId);
		L260M01A r = null;
		if (!Util.isEmpty(list) && list.size() > 0) {
			r = list.get(0);
		}
		return r;
	}

	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}

	private ELF601 convertL260m01cToELF601(L260M01A l260m01a,
			L260M01C l260m01c, ELF601 newElf601, boolean hasElf601,
			boolean isOverSea) {
		
		// J-110-0363 新增ID欄位
		newElf601.setElf601_custid(Util.getLeftStr(
				Util.trim(l260m01a.getCustId()), 10));
		newElf601.setElf601_dupno(Util.getLeftStr(
				Util.trim(l260m01a.getDupNo()), 1));
		newElf601.setElf601_br_no(Util.trim(l260m01a.getOwnBrId()));
		// 因為有ID階層 以 L260M01C 的為準
		newElf601.setElf601_cntrno(Util.trim(l260m01c.getCntrNo()));
		newElf601.setElf601_loan_no(Util.trim(Util.nullToSpace(l260m01c
				.getLoanNo())));

		if (!hasElf601) {
			String unid = Util.trim(l260m01c.getUnid());
			if (Util.equals(unid, "new")) {
				unid = l260m01c.getOid();
			}
			newElf601.setElf601_unid(unid);// l260m01c.getOid());
			newElf601.setElf601_loan_kind(l260m01c.getLoanKind());
		}
		newElf601.setElf601_fo_kind(Util.trim(l260m01c.getFollowKind()));
		newElf601.setElf601_fo_content(Util.trimSizeInOS390(
				l260m01c.getFollowContent(), 800));
		newElf601.setElf601_fo_way(l260m01c.getFollowWay());
		if (Util.equals(l260m01c.getFollowWay(), "1")) { // 1-特定日期 / 2-循環週期
			newElf601.setElf601_fo_cycle(BigDecimal.valueOf(0));
		} else {
			newElf601.setElf601_fo_cycle(l260m01c.getFollowCycle());
		}
		newElf601.setElf601_fo_beg_date((Util.isEmpty(Util.nullToSpace(l260m01c
				.getFollowBgnDate())) ? null : // CapDate.parseDate(CapDate.ZERO_DATE)
												// :
				new java.sql.Date(l260m01c.getFollowBgnDate().getTime())));
		newElf601.setElf601_fo_end_date((Util.isEmpty(Util.nullToSpace(l260m01c
				.getFollowEndDate())) ? null : // CapDate.parseDate(CapDate.ZERO_DATE)
												// :
				new java.sql.Date(l260m01c.getFollowEndDate().getTime())));
		newElf601.setElf601_fo_next_date((Util.isEmpty(Util
				.nullToSpace(l260m01c.getNextFollowDate())) ? null : // CapDate.parseDate(CapDate.ZERO_DATE)
																		// :
				new java.sql.Date(l260m01c.getNextFollowDate().getTime())));

		// P-新增(用來判斷為user新增，上傳中心時轉為N)
		String status = l260m01c.getStatus();
		if (Util.equals(status, "P")) {
			status = "N";
		}
		newElf601.setElf601_status(status);
		newElf601.setElf601_staff(l260m01c.getStaff());
		newElf601.setElf601_fo_staffNo(Util.trim(l260m01c.getFo_staffNo()));
		newElf601.setElf601_ao_staffNo(Util.trim(l260m01c.getAo_staffNo()));
		if (!hasElf601) {
			newElf601.setElf601_cre_date(CapDate.getCurrentTimestamp());
			newElf601
					.setElf601_cre_teller((l260m01c.getUpdater() == null ? l260m01a
							.getUpdater() : l260m01c.getUpdater()));
			newElf601.setElf601_cre_supvno(l260m01a.getApprover());// user.getUserId());
		}
		newElf601.setElf601_upd_date(CapDate.getCurrentTimestamp());
		newElf601
				.setElf601_upd_teller((l260m01c.getUpdater() == null ? l260m01a
						.getUpdater() : l260m01c.getUpdater()));
		newElf601.setElf601_upd_supvno(l260m01a.getApprover());// user.getUserId());

		int strSize = 400;
		if (isOverSea) {
			// 因字碼關係某部分會過長無法insert (ex: cp937正常、cp930過長)，所以海外限縮長度
			strSize = 200;
		}
		newElf601.setElf601_full_content(Util.trimSizeInOS390(
				Util.toFullCharString(l260m01c.getFollowContent()), strSize));
		newElf601.setElf601_case_mark(Util.nullToSpace(l260m01c.getCaseMark()));
		
		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		// 海外日期要特別處理
		if (!isOverSea) {	
			newElf601.setElf601_suid(Util.trim(l260m01c.getFrom601SUid()));
			newElf601.setElf601_sapptime(Util.isEmpty(Util.nullToSpace(l260m01c.getFollowBgnDate())) ? null : l260m01c.getFollowBgnDate());
			newElf601.setElf601_sseqno(l260m01c.getFrom601SSeqno());
		} else{//海外欄位處理
			Timestamp as400Time = null;
			BigDecimal sseqno = new BigDecimal(0);
			if(Util.isNotEmpty(Util.nullToSpace(l260m01c.getFollowBgnDate()))){
				String apptime =  CapDate.convertTimestampToString(new Timestamp(l260m01c.getFollowBgnDate().getTime()), UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
				as400Time = CapDate.convertStringToTimestamp(apptime);		
			}
			if(Util.isNotEmpty(Util.trim(l260m01c.getFrom601SSeqno()))){
				sseqno = new BigDecimal(Util.trim(l260m01c.getFrom601SSeqno()));
			}
			newElf601.setElf601_suid(Util.trim(l260m01c.getFrom601SUid()));      
			newElf601.setElf601_sapptime(as400Time);
			newElf601.setElf601_sseqno(sseqno);
		}
		return newElf601;
	}

	private ELF602 convertL260m01dToELF602(L260M01A l260m01a,
			L260M01D l260m01d, ELF602 newElf602, boolean hasElf602,
			boolean isOverSea) {

		// J-110-0363 新增ID欄位
		newElf602.setElf602_custid(Util.getLeftStr(
				Util.trim(l260m01a.getCustId()), 10));
		newElf602.setElf602_dupno(Util.getLeftStr(
				Util.trim(l260m01a.getDupNo()), 1));
		newElf602.setElf602_br_no(Util.trim(l260m01a.getOwnBrId()));
		// 因為有ID階層 以 L260M01D 的為準
		newElf602.setElf602_cntrno(Util.trim(l260m01d.getCntrNo()));
		newElf602.setElf602_loan_no(Util.trim(Util.nullToSpace(l260m01d
				.getLoanNo())));

		if (!hasElf602) {
			String unid = Util.trim(l260m01d.getUnid());
			if (Util.equals(unid, "new")) {
				unid = l260m01d.getOid();
			}
			newElf602.setElf602_unid(unid); // l260m01d.getOid()
			newElf602.setElf602_loan_kind(l260m01d.getLoanKind());
			// staff沒開紀錄檔畫面 so 新增的時候才有 不會有更新
			// ====> 沒有畫面 新增也沒有值?!
			newElf602.setElf602_staff(Util.trim(l260m01d.getFollowStaff()));
			newElf602.setElf602_fo_date((Util.isEmpty(Util.nullToSpace(l260m01d
					.getFollowDate())) ? null : // CapDate.parseDate(CapDate.ZERO_DATE)
												// :
					new java.sql.Date(l260m01d.getFollowDate().getTime())));
			String datasrc = Util.trim(l260m01d.getDataSrc());
			if (Util.equals(datasrc, "new")) {
				datasrc = l260m01d.getOid();
			}
			newElf602.setElf602_datasrc(datasrc);
		}
		newElf602.setElf602_fo_kind(Util.trim(l260m01d.getFollowKind()));
		newElf602.setElf602_fo_content(Util.trimSizeInOS390(
				l260m01d.getFollowContent(), 800));
		newElf602.setElf602_chkdate((Util.isEmpty(Util.nullToSpace(l260m01d
				.getChkDate())) ? null : // CapDate.parseDate(CapDate.ZERO_DATE)
											// :
				new java.sql.Date(l260m01d.getChkDate().getTime())));
		newElf602
				.setElf602_conform_fg(Util.nullToSpace(l260m01d.getConformFg()));
		newElf602.setElf602_fo_memo(Util.trimSizeInOS390(
				l260m01d.getFollowMemo(), 506));
		newElf602.setElf602_status(Util.nullToSpace(l260m01d
				.getHandlingStatus()));
		newElf602.setElf602_unusual_fg(Util.nullToSpace(l260m01d
				.getRepayUnusualFg()));
		newElf602.setElf602_unusualdesc(Util.trimSizeInOS390(
				l260m01d.getUnusualDesc(), 800));
		newElf602.setElf602_isnotional(l260m01d.getIsNotional());
		newElf602.setElf602_isaml(l260m01d.getIsAML());
		newElf602.setElf602_upd_date(CapDate.getCurrentTimestamp());
		newElf602
				.setElf602_upd_teller((l260m01d.getUpdater() == null ? l260m01a
						.getUpdater() : l260m01d.getUpdater()));
		newElf602.setElf602_upd_supvno(l260m01a.getApprover());// user.getUserId());

		int strSize = 400;
		if (isOverSea) {
			// 因字碼關係某部分會過長無法insert (ex: cp937正常、cp930過長)，所以海外限縮長度
			strSize = 200;
		}
		newElf602.setElf602_full_content(Util.trimSizeInOS390(
				Util.toFullCharString(l260m01d.getFollowContent()), strSize));
		newElf602.setElf602_fieldMainId(l260m01d.getOid());
		newElf602.setElf602_fileDesc(Util.trimSizeInOS390(
				Util.nullToSpace(l260m01d.getFileDesc()), 800));
		newElf602.setElf602_case_mark(Util.nullToSpace(l260m01d.getCaseMark()));

		// J-113-0035 為利ESG案件之貸後管控,
		// ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
		// 海外日期要特別處理
		if (!isOverSea) {//國內
			newElf602.setElf602_suid(l260m01d.getFrom602SUid());
			newElf602.setElf602_sapptime(l260m01d.getFrom602SApptime());
			newElf602.setElf602_sseqno(l260m01d.getFrom602SSeqno());
			newElf602.setElf602_sesgsunre(l260m01d.getFrom602SESGsunre());
		}else{//海外
			Timestamp as400Time = null;
			BigDecimal sseqno = new BigDecimal(0);
			if(Util.isNotEmpty(Util.nullToSpace(l260m01d.getFrom602SApptime()))){
				String apptime =  CapDate.convertTimestampToString(new Timestamp(l260m01d.getFrom602SApptime().getTime()), UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
				as400Time = CapDate.convertStringToTimestamp(apptime);		
			}
			if(Util.isNotEmpty(Util.trim(l260m01d.getFrom602SSeqno()))){
				sseqno = new BigDecimal(Util.trim(l260m01d.getFrom602SSeqno()));
			}
			newElf602.setElf602_suid(Util.trim(l260m01d.getFrom602SUid()));      
			newElf602.setElf602_sapptime(as400Time);
			newElf602.setElf602_sseqno(sseqno);
			newElf602.setElf602_sesgsunre(l260m01d.getFrom602SESGsunre());
		}
		return newElf602;
	}

	@Override
	public void saveL260m01aTemp(L260M01A l260m01a) {
		l260m01aDao.save((L260M01A) l260m01a);
	}		

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public Page<Map<String, Object>> getPrintList(String oid, ISearch search)
			throws CapException {
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		L260M01A l260m01a = null;
		l260m01a = this.findModelByOid(L260M01A.class, oid);
		if (l260m01a == null) {

		} else {
			List<L260M01C> l260m01clist = (List<L260M01C>) this
					.findListByMainId(L260M01C.class, l260m01a.getMainId());
			if (l260m01clist != null && l260m01clist.size() > 0) {
				for (L260M01C l260m01c : l260m01clist) {
					data = new HashMap<String, Object>();
					data.put("custId", Util.nullToSpace(l260m01a.getCustId()));
					data.put("cntrNo", Util.nullToSpace(l260m01c.getCntrNo()));
					data.put("loanNo", Util.nullToSpace(l260m01c.getLoanNo()));
					beanList.add(data);
				}
			}

			List<L260M01D> l260m01dlist = (List<L260M01D>) this
					.findListByMainId(L260M01D.class, l260m01a.getMainId());
			if (l260m01dlist != null && l260m01dlist.size() > 0) {
				for (L260M01D l260m01d : l260m01dlist) {
					data = new HashMap<String, Object>();
					data.put("custId", Util.nullToSpace(l260m01a.getCustId()));
					data.put("cntrNo", Util.nullToSpace(l260m01d.getCntrNo()));
					data.put("loanNo", Util.nullToSpace(l260m01d.getLoanNo()));
					beanList.add(data);
				}
			}
		}
		// 排除重複
		Set<Map> setMap = new HashSet<Map>();
		for (Map<String, Object> chkMap : beanList) {
			if (setMap.add(chkMap)) {
				newList.add(chkMap);
			}
		}

		// 排序
		Collections.sort(newList, new Comparator<Map<String, Object>>() {
			public int compare(Map<String, Object> o1, Map<String, Object> o2) {
				String cntrNo1 = o1.get("cntrNo").toString();
				String cntrNo2 = o2.get("cntrNo").toString();
				int c = cntrNo1.compareTo(cntrNo2);
				if (c != 0) {
					return c;
				}

				String loanNo1 = o1.get("loanNo").toString();
				String loanNo2 = o2.get("loanNo").toString();

				return loanNo1.compareTo(loanNo2);
			}
		});

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > newList.size() ? start
				+ (newList.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = newList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, newList.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public String getLoanKind(String cntrNo) {
		// 2020/06/18 偉菘回覆 不分種類 一律 LN
		String loanKind = "";
		String fourth = Util.trim(cntrNo).substring(3, 4);
		if (Util.equals(UtilConstants.Casedoc.typCd.DBU, fourth)) {
			loanKind = "LN";
		} else if (Util.equals(UtilConstants.Casedoc.typCd.OBU, fourth)) {
			loanKind = "LN";
			// loanKind = "LO";
		} else if (Util.equals(UtilConstants.Casedoc.typCd.海外, fourth)) {
			// loanKind = "LN";
		} else {
			// loanKind = "LN";
		}
		return loanKind;
	}

	/**
	 * 設定顯示狀態
	 * <p/>
	 * 顯示、列印、排序 用 N.U -> 未來日 => 1-待追蹤 -> 過去日 => 2-已追蹤 C ==========> 3-已解除
	 * (下次追蹤日顯示 N.A.)
	 */
	@SuppressWarnings("static-access")
	@Override
	public String getStatusForShow(String status, Date nextFollowDate) {
		String statusForShow = "";

		if (this.isNull_or_ZeroDate(nextFollowDate)) {

		} else {
			if (Util.equals(status, "C")) {
				statusForShow = "3"; // C ==========> 3-已解除
			} else {
				if (this.isFutureDate(nextFollowDate)) {
					statusForShow = "1"; // 未來日 => 1-待追蹤
				} else {
					statusForShow = "2"; // 過去日 => 2-已追蹤
				}
			}
		}

		return statusForShow;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainIdNotDel(Class clazz,
			String mainId, boolean notIncDel) {
		if (clazz == L260M01C.class) {
			return l260m01cDao.findByMainId(mainId, notIncDel);
		} else if (clazz == L260M01D.class) {
			return l260m01dDao.findByMainId(mainId, notIncDel);
		} else if (clazz == L260S01A.class) {
			return l260s01aDao.findByMainId(mainId, notIncDel);
		} else if (clazz == L260S01B.class) {
			return l260s01bDao.findByMainId(mainId, notIncDel);
		} else if (clazz == L260S01C.class) {
			return l260s01cDao.findByMainId(mainId, notIncDel);
		} else if (clazz == L260S01D.class) {
			// J-112-0307
			// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表
			return l260s01dDao.findByMainId(mainId, notIncDel);
		} else if (clazz == L260S01E.class) {
			return l260s01eDao.findByMainId(mainId, notIncDel);
		} else if (clazz == L260S01F.class) {
			// J-113-0150 配合分行，E-LOAN授信管理系統修改建檔維護-貸後管理追蹤檢核表維護-查詢未完成案件等。
			return l260s01fDao.findByMainId(mainId, notIncDel);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findByMainIdAndNos(Class clazz,
			String mainId, String cntrNo, String loanNo, boolean notIncDel,
			boolean incEmptyLoanNo) {
		if (clazz == L260M01C.class) {
			return l260m01cDao.findByMainIdAndNos(mainId, cntrNo, loanNo,
					notIncDel, incEmptyLoanNo);
		} else if (clazz == L260M01D.class) {
			return l260m01dDao.findByMainIdAndNos(mainId, cntrNo, loanNo,
					notIncDel, incEmptyLoanNo);
		}
		return null;
	}

	public static boolean isNull_or_ZeroDate(Date d) {
		if (d == null) {
			return true;
		}
		if (Util.equals(CapDate.ZERO_DATE, TWNDate.toAD(d))) {
			return true;
		}
		return false;
	}

	public boolean isFutureDate(Date d) {
		Date today = CapDate.getDate(
				CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD),
				UtilConstants.DateFormat.YYYY_MM_DD);
		if (d.compareTo(today) > 0) {
			// 傳入日期比今天 大 => 未來日
			return true;
		} else {
			// 傳入日期比今天 小 或 等於 => 過去日
			return false;
		}
	}

	public List<Map<String, Object>> findOTS_DW_LNWM_MNT_ById(String custId,
			String dupNo, String bgnDate) {
		return dwdbBASEService.findOTS_DW_LNWM_MNT_ById(custId, dupNo, bgnDate);
	}

	public List<Map<String, Object>> findOTS_DW_LNWM_MNT_ByKey(String custId,
			String dupNo, String proType, String bankProCode, String accNo) {
		return dwdbBASEService.findOTS_DW_LNWM_MNT_ByKey(custId, dupNo,
				proType, bankProCode, accNo);
	}

	public List<Map<String, Object>> findOTS_DW_LNWM_CFM_ByUnid(String unid) {
		return dwdbBASEService.findOTS_DW_LNWM_CFM_ByUnid(unid);
	}

	public List<Map<String, Object>> findOTS_DW_LNWM_CFM_ByKey(String unid,
			String custId, String dupNo, String proType, String bankProCode,
			String accNo) {
		return dwdbBASEService.findOTS_DW_LNWM_CFM_ByKey(unid, custId, dupNo,
				proType, bankProCode, accNo);
	}

	public List<Map<String, Object>> findODS_0320_ById(String custId,
			String dupNo) {
		return odsdbBASEService.findODS_0320_ById(custId, dupNo);
	}

	public List<Map<String, Object>> findODS_0060_TXN(String realActNo) {
		return odsdbBASEService.findODS_0060_TXN(realActNo);
	}

	public List<Map<String, Object>> findODS_0060_HIST(String realActNo,
			String currCode, String bgnDate, String endDate) {
		return odsdbBASEService.findODS_0060_HIST(realActNo, currCode, bgnDate,
				endDate);
	}

	public List<Map<String, Object>> findODS_8250(String brNo, String ioFlag,
			String func, String remitType, String bgnDate, String endDate,
			String custId, String dupNo, String begAmt, String endAmt,
			String bankId, String ractNo) {
		return odsdbBASEService
				.findODS_8250(brNo, ioFlag, func, remitType, bgnDate, endDate,
						custId, dupNo, begAmt, endAmt, bankId, ractNo);
	}

	public List<Map<String, Object>> findODS_8410_ByAccNo(String loanNo,
			String brNo) {
		return odsdbBASEService.findODS_8410_ByAccNo(loanNo, brNo);
	}

	public List<Map<String, Object>> findODS_CMSTKTBL() {
		return odsdbBASEService.findODS_CMSTKTBL();
	}

	public Map<String, String> findODS_CMSTKTBL_SINGLE(String CMSTK_CODE) {
		Map<String, Object> objMap = odsdbBASEService
				.findODS_CMSTKTBL_SINGLE(CMSTK_CODE);
		Map<String, String> result = new HashMap<String, String>();
		if (objMap != null && !objMap.isEmpty()) {
			result.put(Util.trim(objMap.get("CMSTK_CODE")),
					Util.trim(objMap.get("CMSTK_NAME")));
		}
		return result;
	}

	public List<Map<String, Object>> findODS_CMMEMTBN() {
		return odsdbBASEService.findODS_CMMEMTBN();
	}

	public String[] getODS_Status() {
		String[] chkArr = new String[2];
		boolean chkODS = false;
		String msg = "";

		// Y：ODS可用
		String odsFlag = Util.trim(lmsService
				.getSysParamDataValue("LMS_ODS_FLAG"));
		if (Util.equals(odsFlag, "Y")) {
			Map<String, Object> odsStatusMap = odsdbBASEService.getODS_Status();
			if (odsStatusMap != null && !odsStatusMap.isEmpty()) {
				// (年/月/日/時/分/秒) 4/2/2/2/2/2
				String timeStamp = CapString.trimNull(odsStatusMap
						.get("TCSFODS_STAN"));
				// #空白，正常營運中 #E 值，ERROR
				String flag = CapString.trimNull(odsStatusMap
						.get("TCSFODS_FLAG1"));
				if (Util.isEmpty(flag)) {
					String timeOutStr = Util.trim(lmsService
							.getSysParamDataValue("LMS_ODS_TIMEOUT"));
					Integer timeOut = 0;
					if (Util.isEmpty(timeOutStr)) {
						timeOut = 60; // 預設60秒 time out
					} else {
						timeOut = (NumberUtils.isNumber(timeOutStr) ? Integer
								.parseInt(timeOutStr) : 0);
					}

					if (Util.isNotEmpty(timeStamp) && timeStamp.length() == 14
							&& NumberUtils.isNumber(timeStamp)) {
						// 拆解ODS時間
						String fullTime = timeStamp.substring(0, 4) + "-"
								+ timeStamp.substring(4, 6) + "-"
								+ timeStamp.substring(6, 8) + " "
								+ timeStamp.substring(8, 10) + ":"
								+ timeStamp.substring(10, 12) + ":"
								+ timeStamp.substring(12, 14);
						Date odsDate = CapDate
								.convertStringToTimestamp(fullTime);
						Date nowDate = CapDate.getCurrentTimestamp();

						// getTime 毫秒 換算 秒
						long diff = (nowDate.getTime() - odsDate.getTime()) / 1000;

						if (diff < timeOut) { // 小於 time out 時間
							chkODS = true;
						} else {
							msg = "4"; // ODS 資料更新過久
						}
					} else {
						msg = "3"; // ODS 資料時間異常
					}
				} else {
					msg = "2"; // ODS 營運不正常
				}
			} else {
				msg = "1"; // ODS 查無狀態
			}
		} else {
			msg = "0"; // ODS 暫停營運
		}

		chkArr[0] = (chkODS ? "Y" : "N");
		chkArr[1] = (chkODS ? "" : msg);
		return chkArr;
	}

	@Override
	public void saveL260s01aList(List<L260S01A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L260S01A l260s01a : list) {
			l260s01a.setUpdater(user.getUserId());
			l260s01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		if (!list.isEmpty()) {
			l260s01aDao.save(list);
		}
	}

	@Override
	public void deleteL260s01aList(List<L260S01A> list) {
		if (!list.isEmpty()) {
			l260s01aDao.delete(list);
		}
	}

	@Override
	public boolean deleteL260s01as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L260S01A> l260s01as = new ArrayList<L260S01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L260S01A l260s01a = (L260S01A) findModelByOid(L260S01A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l260s01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l260s01a.setUpdater(user.getUserId());
			l260s01a.setUpdateTime(CapDate.getCurrentTimestamp());
			l260s01as.add(l260s01a);
		}
		if (!l260s01as.isEmpty()) {
			l260s01aDao.save(l260s01as);
			flag = true;
		}
		return flag;
	}

	@Override
	public L260S01A findL260s01aByUniqueKey(String mainId, String custId,
			String dupNo, String proType, String bankProCode, String accNo) {
		return l260s01aDao.findByUniqueKey(mainId, custId, dupNo, proType,
				bankProCode, accNo);
	}

	@Override
	public void saveL260s01bList(List<L260S01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L260S01B l260s01b : list) {
			l260s01b.setUpdater(user.getUserId());
			l260s01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		if (!list.isEmpty()) {
			l260s01bDao.save(list);
		}
	}
	
	@Override
	public void saveL260s01fList(L260M01D l260m01d, JSONObject jsonData) {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String brNo = user.getUnitNo();
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
				.getBranch(brNo).getBrNoFlag());
		
		Map<String, String> followKindMap = this.followKindToMap(l260m01d
				.getFollowKind());		
		// 未達成註記為Y之計數器
		int countY = 0;
		// 未達成註記為N之計數器
		int countN = 0;

		// 儲存L260S01F
		JSONArray itemNoAry = jsonData.getJSONArray("itemNo_S01F");
		for (int i = 0; i < itemNoAry.size(); i++) {
			String itemNo = Util.trim(itemNoAry.getString(i));
			L260S01F l260s01FFromDB = l260s01fDao.findByMainidAndItemNo(
					l260m01d.getOid(), itemNo, true);
			if(l260s01FFromDB == null){
				l260s01FFromDB = new L260S01F();
				l260s01FFromDB.setMainId(l260m01d.getOid());
				l260s01FFromDB.setItemNo(itemNo);
				l260s01FFromDB.setItemSeq(i+1);//1開始
				l260s01FFromDB.setCreateTime(new Timestamp(System.currentTimeMillis()));
				l260s01FFromDB.setCreator(user.getUserId());
			}
			String chkResult = Util.trim(jsonData.get("chkResult"
					+ itemNoAry.getString(i) + "_S01F"));
			l260s01FFromDB.setChkResult(chkResult);

			String itemMemo = Util.trim(jsonData.get("itemMemo"
					+ itemNoAry.getString(i) + "_S01F"));
			l260s01FFromDB.setItemMemo(itemMemo);
			this.save(l260s01FFromDB);

			if (followKindMap
					.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)) {
				if (Util.equals(chkResult, "Y") || Util.equals(chkResult, "P")) {
					// 選項為全部達成、部分達成
					countN++;
				} else if (Util.equals(chkResult, "N")) {
					// 選項為未達成
					countY++;
				} else {
					// 選項為NA的不處理
				}
			}
		}

		if (followKindMap.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)) {
			// 國內且當追蹤類別為永續績效目標檢視時，更新欄位「分項ESG 約定條件全部未達成」
			// 海外也一併更新，只是最後 不call method chgESGFlg(L260M01A l260m01a) >> msps.callLNSP0600  (聯徵報送用)
			if (countY != 0 && countN == 0) {
				// 全部選項皆為未達成(不考慮選項為NA的)
				l260m01d.setFrom602SESGsunre("Y");
			} else if (countN != 0) {
				// 其一選項為已達成、部分未達成
				l260m01d.setFrom602SESGsunre("N");
			}
			this.save(l260m01d);
		}

	}

	/**
	 * J-110-0363 找所有ID層資料
	 */
	@Override
	public List<Map<String, Object>> findDataById(String clazz, String ownBrId,
			String custId, String dupNo, String[] docStatusArray) {
		return eloandbBASEService.findL260MById(clazz, ownBrId, custId, dupNo,
				docStatusArray);
	}

	/**
	 * J-110-0363 找該筆ID層資料是否有其他維護案修改中
	 */
	@Override
	public String findDataByIdAndM01A(String clazz, L260M01A l260m01a,
			String unid) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS8000M01Page.class);
		StringBuffer sb = new StringBuffer();
		if (l260m01a != null) {
			List<Map<String, Object>> m01dList = this.findDataById(clazz,
					Util.trim(l260m01a.getOwnBrId()),
					Util.trim(l260m01a.getCustId()),
					Util.trim(l260m01a.getDupNo()), new String[] {
							CreditDocStatusEnum.海外_編製中.getCode(),
							CreditDocStatusEnum.海外_待覆核.getCode() });// new
																	// ArrayList<Map<String,
																	// Object>>();
			if (m01dList != null && !m01dList.isEmpty()) {
				for (Map<String, Object> m01dMap : m01dList) {
					String m01aOid = Util.nullToSpace(MapUtils.getString(
							m01dMap, "M01AOID"));
					String m01Oid = Util.nullToSpace(MapUtils.getString(
							m01dMap, "M01OID"));
					String m01aCntrNo = Util.nullToSpace(MapUtils.getString(
							m01dMap, "CNTRNO"));
					String m01aLoanNo = Util.nullToSpace(MapUtils.getString(
							m01dMap, "LOANNO"));
					String m01Unid = Util.nullToSpace(MapUtils.getString(
							m01dMap, "UNID"));
					String m01Chk = Util.nullToSpace(MapUtils.getString(
							m01dMap, "CHECKYN"));
					String m01Status = Util.nullToSpace(MapUtils.getString(
							m01dMap, "STATUS"));
					if (Util.isNotEmpty(m01Chk)
							&& Util.notEquals(Util.trim(l260m01a.getOid()),
									m01aOid) && Util.isNotEmpty(unid)
							&& Util.equals(unid, m01Unid)) {
						sb.append(sb.toString().length() > 0 ? "<br/>" : "");
						sb.append(MessageFormat.format(
								pop.getProperty("msg.cantEdit"),
								pop.getProperty("cntrNo")
										+ "："
										+ m01aCntrNo
										+ (Util.isEmpty(m01aLoanNo) ? "" : ("、"
												+ pop.getProperty("loanNo")
												+ "：" + m01aLoanNo))));
					}
				}
			}
		}
		return sb.toString();
	}

	@SuppressWarnings("unchecked")
	private void upLoadOTS_DW_LNWM_CFM(L260M01D l260m01d, ELF602 elf602) {
		List<Object[]> delDataList = new ArrayList<Object[]>();
		List<Object[]> newDataList = new ArrayList<Object[]>();
		if (l260m01d != null) {
			List<L260S01A> l260s01aList = (List<L260S01A>) this
					.findListByMainId(L260S01A.class, l260m01d.getOid());
			for (L260S01A l260s01a : l260s01aList) {
				if (l260s01a != null) {
					String custId = Util.nullToSpace(l260s01a.getCustId());
					String custDupNo = Util.nullToSpace(l260s01a.getDupNo());
					String proType = Util.nullToSpace(l260s01a.getProType());
					String accNo = Util.nullToSpace(l260s01a.getAccNo());
					String bankProCode = Util.nullToSpace(l260s01a
							.getBankProCode());
					String unid = "";
					if (elf602 != null && Util.isNotEmpty(elf602)) {
						unid = Util.trim(elf602.getElf602_unid());
					} else {
						if (Util.equals(Util.trim(l260m01d.getUnid()), "new")) {
							unid = l260m01d.getOid();
						} else {
							unid = Util.trim(l260m01d.getUnid());
						}
					}

					List<Map<String, Object>> dwData = this
							.findOTS_DW_LNWM_CFM_ByKey(unid, custId, custDupNo,
									proType, bankProCode, accNo);
					if (dwData != null && !dwData.isEmpty()) { // 有資料 delete
																// insert
						for (Map<String, Object> map : dwData) {
							// 保留贖回通知紀錄 才不會重複通知
							String batchDt = Util.trim(MapUtils.getString(map,
									"BATCH_DT", "")); // 贖回紀錄批次日
							delDataList.add(new Object[] { unid, custId,
									custDupNo, proType, bankProCode, accNo });
							if (l260s01a.getDeletedTime() == null) {
								newDataList
										.add(new Object[] {
												unid,
												Util.trim(l260m01d.getCntrNo()),
												Util.trim(l260m01d.getLoanNo()),
												CapDate.formatDate(
														new Date(),
														UtilConstants.DateFormat.YYYY_MM_DD),
												custId,
												custDupNo,
												Util.nullToSpace(l260s01a
														.getCustName()),
												proType,
												bankProCode,
												Util.nullToSpace(l260s01a
														.getBankProName()),
												accNo,
												CapDate.formatDate(
														l260s01a.getLstBuyDt(),
														UtilConstants.DateFormat.YYYY_MM_DD),
												Util.nullToSpace(l260s01a
														.getLstBuyCurCd()),
												Util.nullToSpace(l260s01a
														.getLstBuyAmt()),
												Util.nullToSpace(l260s01a
														.getLstBuyBrCd()),
												CapDate.formatDate(
														l260s01a.getLstSellDt(),
														UtilConstants.DateFormat.YYYY_MM_DD),
												Util.nullToSpace(l260s01a
														.getLstSellCurCd()),
												Util.nullToSpace(l260s01a
														.getLstSellAmt()),
												Util.nullToSpace(l260s01a
														.getLstSellBrCd()),
												Util.nullToSpace(l260s01a
														.getTranType()),
												Util.nullToSpace(l260s01a
														.getInvAmt()),
												CapDate.formatDate(
														l260s01a.getDataDt(),
														UtilConstants.DateFormat.YYYY_MM_DD),
												(Util.isEmpty(batchDt) ? null
														: batchDt) });
							}
						}
					} else { // 無資料
						if (l260s01a.getDeletedTime() == null) {
							newDataList
									.add(new Object[] {
											unid,
											Util.trim(l260m01d.getCntrNo()),
											Util.trim(l260m01d.getLoanNo()),
											CapDate.formatDate(
													new Date(),
													UtilConstants.DateFormat.YYYY_MM_DD),
											custId,
											custDupNo,
											Util.nullToSpace(l260s01a
													.getCustName()),
											proType,
											bankProCode,
											Util.nullToSpace(l260s01a
													.getBankProName()),
											accNo,
											CapDate.formatDate(
													l260s01a.getLstBuyDt(),
													UtilConstants.DateFormat.YYYY_MM_DD),
											Util.nullToSpace(l260s01a
													.getLstBuyCurCd()),
											Util.nullToSpace(l260s01a
													.getLstBuyAmt()),
											Util.nullToSpace(l260s01a
													.getLstBuyBrCd()),
											CapDate.formatDate(
													l260s01a.getLstSellDt(),
													UtilConstants.DateFormat.YYYY_MM_DD),
											Util.nullToSpace(l260s01a
													.getLstSellCurCd()),
											Util.nullToSpace(l260s01a
													.getLstSellAmt()),
											Util.nullToSpace(l260s01a
													.getLstSellBrCd()),
											Util.nullToSpace(l260s01a
													.getTranType()),
											Util.nullToSpace(l260s01a
													.getInvAmt()),
											CapDate.formatDate(
													l260s01a.getDataDt(),
													UtilConstants.DateFormat.YYYY_MM_DD),
											null });
						}
					}
				}
			}
			dwdbBASEService.OTS_DW_LNWM_CFM_delete(delDataList);
			dwdbBASEService.OTS_DW_LNWM_CFM_insert(newDataList);
		}
	}

	/**
	 * J-110-0497 餘屋貸款
	 */
	@SuppressWarnings("unchecked")
	private void upLoadElf517(L260M01D l260m01d, ELF602 elf602) {
		if (l260m01d != null) {
			String cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
			if (this.isCaseMark(l260m01d, "03")) {// Util.equals(Util.nullToSpace(l260m01d.getCaseMark()),
													// "03")) {
				List<L260S01B> l260s01bList = (List<L260S01B>) this
						.findListByMainId(L260S01B.class, l260m01d.getOid());
				for (L260S01B l260s01b : l260s01bList) {
					if (l260s01b != null) {
						if (Util.isNotEmpty(cntrNo)) {
							String buildName = Util.trimSizeInOS390(
									Util.trim(l260s01b.getBuildName()), 90);
							BigDecimal begForSell = Util
									.parseBigDecimal(l260s01b.getBegForSell());
							BigDecimal soldNumber = Util
									.parseBigDecimal(l260s01b.getSoldNumber());
							String proStatus = Util.trim(l260s01b
									.getProStatus());
							String behindDesc = Util.trimSizeInOS390(
									Util.trim(l260s01b.getBehindDesc()), 900);
							String isSameCase = Util.trim(l260s01b
									.getIsSameCase());
							Map<String, Object> elf517DataMap = misElf517Service
									.getByCntrNo(cntrNo);
							if (elf517DataMap != null
									&& !elf517DataMap.isEmpty()) {
								misElf517Service.updateForFms(cntrNo,
										buildName, begForSell, soldNumber,
										proStatus, behindDesc, isSameCase);
							} else {
								misElf517Service.insertForFms(cntrNo,
										buildName, begForSell, soldNumber,
										proStatus, behindDesc, isSameCase);
							}
						}
					}
				}
			}
		}
	}

	public L260M01D findL260m01dByMainIdAndUnid(String mainId, String unid) {
		return l260m01dDao.findByMainIdAndUnid(mainId, unid);
	}

	public void deleteL260m01ds(List<L260M01D> l260m01ds) {
		l260m01dDao.delete(l260m01ds);
	}

	public String cutStrWithUTF8(Object value, int byteCnt)
			throws UnsupportedEncodingException {
		int zhCnt = 0;
		int cutNum = 0;
		if (byteCnt <= 0) {
			return "";
		}
		if (value != null) {
			byte[] utf8Bytes = null;

			try {
				utf8Bytes = String.valueOf(value).getBytes("UTF-8");
			} catch (UnsupportedEncodingException e) {
				logger.error("[String.getBytes]", e);
			}

			if (byteCnt >= utf8Bytes.length) {
				return new String(utf8Bytes, "UTF-8");
			}
			// UTF-8中，漢字以3個字節表示，且3個字節的ASCII碼都大於128, 即為 負
			for (int i = 0; i < byteCnt; i++) {
				if (utf8Bytes[i] < 0) { // 判斷是否為漢字
					zhCnt++;
				}
			}
			// UTF-8中一個漢字是由兩個字節組成的
			if (zhCnt % 3 == 0) {
				// 如果漢字的字節數能被 3 整除，則說明當前長度截取不會截到不完整的漢字
				cutNum = byteCnt;
			} else if (zhCnt % 3 == 2) {
				// 若餘量為 2， 則按當前長度截取會存在殘缺的漢字，所以此處截取字節數減 2，當然也可以加 1，這裡看實際需求和個人喜好
				cutNum = byteCnt - 2;
			} else {
				// 若餘量為 1，則也會截取到不完整的漢字，所以減一，亦可加
				// 二，理由同上。總之只要漢字字符個數能被三整除就不會存在殘缺漢字字符的情況
				cutNum = byteCnt - 1;
			}

			return new String(utf8Bytes, 0, cutNum, "UTF-8");
		}
		return "";
	}

	@Override
	public String[] checkDatePeriod(String dateBgn, String dateEnd,
			boolean sameDay, String txnCode) {
		/*
		 * 查詢起日 YYYMMDD 民國年(大於查詢迄日) 查詢迄日 YYYMMDD 民國年(不得大於今日) 1. 完整輸入起迄日 2. 皆為
		 * yyyy-MM-dd 3. sameDay = true => 起迄日可為同天 sameDay = false => 起日必須早於迄日
		 * 
		 * BTT 0060 4. 查詢起日 不得小於半年前 5. 查詢迄日 不得大於今日
		 * 
		 * BTT 8250 4. 查詢起日 不得小於三個月(By 月份) 6. 查詢起日迄日 需為同月 ==> 2021/06/21
		 * 金襄理說拿掉此檢核
		 */

		String[] arr = new String[3];
		boolean flag = false;
		String msg = "";
		String compareMonth = "";

		if (Util.isNotEmpty(Util.nullToSpace(dateBgn))
				&& Util.isNotEmpty(Util.nullToSpace(dateEnd))) {
			if (CapDate.validDate(dateBgn, "yyyy-MM-dd")
					&& CapDate.validDate(dateEnd, "yyyy-MM-dd")) {
				Date bgnDate = CapDate.parseDate(dateBgn);
				Date nowDate = CapDate.getCurrentTimestamp();
				Date nowDate3S = CapDate.addMonth(nowDate, -3); // 往前推三個月
				String nowDate3Str = CapDate.formatDate(nowDate3S, "yyyy-MM")
						+ "-01";
				Date nowDate6S = CapDate.addMonth(nowDate, -6); // 往前推半年
				Date compareDate = CapDate.parseDate(nowDate3Str);
				compareMonth = "3";
				if (Util.equals(txnCode, "btt0060")) {
					compareDate = nowDate6S;
					compareMonth = "6";
				}
				Date endDate = CapDate.parseDate(dateEnd);
				if (bgnDate.compareTo(nowDate) <= 0
						&& endDate.compareTo(nowDate) <= 0) {
					if (bgnDate.compareTo(compareDate) >= 0) {
						String sign = (sameDay ? ">=" : ">");
						if (LMSUtil.cmpDate(endDate, sign, bgnDate)) {
							flag = true;
						} else {
							msg = "3";
						}
					} else {
						msg = "4";
					}
				} else {
					msg = "5";
				}
			} else {
				msg = "2";
			}
		} else {
			msg = "1";
		}

		arr[0] = (flag ? "Y" : "N");
		arr[1] = (flag ? "" : msg);
		arr[2] = (flag ? "" : (Util.equals(msg, "4") ? compareMonth : ""));
		return arr;
	}

	@Override
	public boolean chkFilterDataDW(String BRNID, String custId, String dupNo,
			String cntrNo, String loanNo) {
		boolean chk = false;

		// StringBuffer sqlSb = new StringBuffer();
		// sqlSb.append(" WHERE CONTRACT='").append(cntrNo).append("'");
		// if(Util.isNotEmpty(loanNo)) {
		// sqlSb.append(" AND LOAN_NO='").append(loanNo).append("'");
		// }

		List<Map<String, Object>> data = dwdbBASEService
				.findDW_ASLNDNEWOVS_ByBrNoCustId(BRNID, custId, dupNo, cntrNo,
						loanNo);

		if (data != null && !data.isEmpty() && data.size() > 0) {
			chk = true;
		} else {

		}

		return chk;
	}

	/**
	 * J-110-0548 擔保品謄本 追蹤日(追蹤事項通知日期)前最新一筆擔保品
	 */
	@Override
	public Map<String, Object> findLastC101m01(String brNo, String cntrNo,
			String elf601_unid, String followDate) {
		return eloandbBASEService.findLastC101m01(brNo, cntrNo, elf601_unid,
				followDate);
	}

	/**
	 * J-110-0548 擔保品謄本 依擔保品申請編號取得謄本資料
	 */
	@Override
	public Map<String, Object> findC101m01ByApplyNo(String applyNo) {
		return eloandbBASEService.findC101m01ByApplyNo(applyNo);
	}

	@Override
	public boolean isCaseMark(L260M01D l260m01d, String chkCaseMark) {
		/**
		 * 案件註記
		 * <p/>
		 * 01:謄本 02:實價登錄 03:餘屋貸款
		 */

		boolean check = false;

		if (l260m01d != null) {
			String cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
			String caseMark = Util.nullToSpace(l260m01d.getCaseMark());
			if (Util.isNotEmpty(cntrNo) && Util.equals(caseMark, chkCaseMark)) {
				check = true;
			}
		}

		return check;
	}

	public List<Map<String, Object>> findC101m01List(String brNo,
			String cntrNo, String elf601_unid, String bgnDate, String endDate) {
		return eloandbBASEService.findC101m01List(brNo, cntrNo, elf601_unid,
				bgnDate, endDate);
	}

	public List<Map<String, Object>> findC100s02aList(String c101m01MainId) {
		return eloandbBASEService.findC100s02aList(c101m01MainId);
	}

	@Override
	public Map<String, Object> findC100s02aByOid(String c100s02aOid) {
		return eloandbBASEService.findC100s02aByOid(c100s02aOid);
	}

	@Override
	public String findC100s02aUrlByOid(String c100s02aOid) {
		String filePath = "";
		if (Util.isNotEmpty(c100s02aOid)) {
			Map<String, Object> cmsC100s02aData = this
					.findC100s02aByOid(c100s02aOid);
			if (cmsC100s02aData != null && !cmsC100s02aData.isEmpty()) {
				filePath = Util.nullToSpace(MapUtils.getString(cmsC100s02aData,
						"FILEPATH"));
			}
		}
		return filePath;
	}

	@Override
	public List<Map<String, Object>> findC101m29List(String brNo,
			String cntrNo, String elf601_unid, String bgnDate, String endDate) {
		return eloandbBASEService.findC101m29List(brNo, cntrNo, elf601_unid,
				bgnDate, endDate);
	}

	@Override
	public Map<String, Object> findC101m29ByOid(String c101m29Oid) {
		return eloandbBASEService.findC101m29ByOid(c101m29Oid);
	}

	@Override
	public String findC101m29UrlByOid(String c101m29Oid) {
		String filePath = "";
		if (Util.isNotEmpty(c101m29Oid)) {
			Map<String, Object> cmsC101m29Data = this
					.findC101m29ByOid(c101m29Oid);
			if (cmsC101m29Data != null && !cmsC101m29Data.isEmpty()) {
				filePath = Util.nullToSpace(MapUtils.getString(cmsC101m29Data,
						"RECVFILE_URL"));
			}
		}
		return filePath;
	}

	@Override
	public List<L260S01C> findL260s01cByRaspFileOid(String raspFileOid) {
		return l260s01cDao.findByIndex02(raspFileOid, true);
	}

	@Override
	public List<L260S01C> findL260s01cByRaspMainId(String mainId,
			String refMainId) {
		return l260s01cDao.findByIndex03(mainId, refMainId, true);
	}

	@Override
	public void saveL260s01cList(List<L260S01C> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L260S01C l260s01c : list) {
			l260s01c.setUpdater(user.getUserId());
			l260s01c.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		if (!list.isEmpty()) {
			l260s01cDao.save(list);
		}
	}

	@Override
	public boolean deleteL260s01cs(List<L260S01C> list) {
		boolean flag = false;
		if (!list.isEmpty()) {
			l260s01cDao.delete(list);
			flag = true;
		}
		/*
		 * MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		 * for (L260S01C l260s01c : list) { // 設定刪除並非直接刪除 ，只是標記刪除時間
		 * l260s01c.setUpdater(user.getUserId());
		 * l260s01c.setUpdateTime(CapDate.getCurrentTimestamp());
		 * l260s01c.setDeletedTime(CapDate.getCurrentTimestamp()); } if
		 * (!list.isEmpty()) { l260s01cDao.save(list); flag = true; }
		 */
		return flag;
	}

	@SuppressWarnings("unchecked")
	private void sendL260s01cListToCMS(L260M01D l260m01d)
			throws CapMessageException {
		if (l260m01d != null) {
			if (this.isCaseMark(l260m01d, "02")) {
				List<L260S01C> l260s01cList = (List<L260S01C>) this
						.findListByMainIdNotDel(L260S01C.class,
								l260m01d.getOid(), true);
				if (l260s01cList != null && l260s01cList.size() > 0) {
					String url = sysParamService.getParamValue("HTTP_URL_CMS")
							+ "/app/";
					for (L260S01C l260s01c : l260s01cList) {
						if (Util.notEquals(
								Util.nullToSpace(l260s01c.getCheckYN()), "Y")
								|| Util.isNotEmpty(Util.nullToSpace(l260s01c
										.getDeletedTime()))) {
							continue;
						}

						EloanHttpGetReqMessage ehcrm = new EloanHttpGetReqMessage();
						ehcrm.setUrl(url);
						ehcrm.setServiceId("scheduler");
						ehcrm.setTimeout(60);
						ehcrm.setLocalUrl(true);

						String input = "";
						JSONObject reqJSONObj = new JSONObject();
						JSONObject request = new JSONObject();

						request.put("refMainId",
								Util.nullToSpace(l260s01c.getRefMainId()));
						request.put("raspFileOid",
								Util.nullToSpace(l260s01c.getRaspFileOid()));
						request.put("collNo",
								Util.nullToSpace(l260s01c.getCollNo()));
						request.put("contractPrice",
								Util.nullToSpace(l260s01c.getContractPrice()));
						request.put("cntrDate", Util.nullToSpace(TWNDate
								.toAD(l260s01c.getCntrDate())));
						request.put("queryRASPDate", Util.nullToSpace(TWNDate
								.toAD(l260s01c.getQueryRaspDate())));
						request.put("raspStat",
								Util.nullToSpace(l260s01c.getRaspStat()));
						request.put("raspWay",
								Util.nullToSpace(l260s01c.getRaspWay()));
						request.put("rWayDt", Util.nullToSpace(TWNDate
								.toAD(l260s01c.getRWayDt())));
						request.put("raspAmt",
								Util.nullToSpace(l260s01c.getRaspAmt()));
						request.put("raspDscr",
								Util.nullToSpace(l260s01c.getRaspDscr()));
						request.put("fileOid",
								Util.nullToSpace(l260s01c.getFileOid()));

						reqJSONObj.put("serviceId", "RecvLoanRASPServiceImpl");
						reqJSONObj.put("request", request.toString());

						input = reqJSONObj.toString();

						try {
							ehcrm.setRequest("input="
									+ URLEncoder.encode(input, "utf-8"));
						} catch (UnsupportedEncodingException ex) {
							throw new CapMessageException("資料編碼錯誤，請稍後再試。"
									+ "L260S01C.Oid=" + l260s01c.getOid(),
									getClass());
						}

						try {
							JSONObject json = eloanHttpGetClient.send(ehcrm);
							String code = "";
							String message = "";
							String responseMsg = "";
							if (json != null) {
								// {"rc":0,"rcmsg":"SUCCESS","response":"執行成功"}
								code = json.getString("rc");
								message = json.getString("rcmsg");
								responseMsg = json.getString("response");

								if (!"0".equals(code)) {
									throw new CapMessageException("傳送至擔保品系統失敗，"
											+ code + ":" + message + "-"
											+ responseMsg + "。"
											+ "L260S01C.Oid="
											+ l260s01c.getOid(), getClass());
								}
							}
						} catch (Exception e) {
							throw new CapMessageException("傳送至擔保品系統失敗，請稍後再試。"
									+ "L260S01C.Oid=" + l260s01c.getOid() + e,
									getClass());
						}
					}
				}
			}
		}
	}

	@Override
	public String getRaspStatus(L260M01D l260m01d) {
		String raspStatus = "";

		if (l260m01d != null) {
			if (this.isCaseMark(l260m01d, "02")) {
				boolean hasS01c = false;				
				List<L260S01C> l260s01cList = (List<L260S01C>) this
						.findListByMainIdNotDel(L260S01C.class,
								l260m01d.getOid(), true);
				if (l260s01cList != null && l260s01cList.size() > 0) {
					hasS01c = true;
				}

				String cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
				String datasrc = Util.trim(l260m01d.getDataSrc());
				Date followDate = l260m01d.getFollowDate() == null ? new Date()
						: l260m01d.getFollowDate();
				// 一個月前到一個月內 依據申請日期由大到小
				String bgnDate = TWNDate.toAD(CapDate.addMonth(followDate, -1));
				String endDate = TWNDate.toAD(CapDate.addMonth(followDate, 1));
				String cntrNoBr = cntrNo.substring(0, 3);

				Map<String, Object> c101m29Data = eloandbBASEService
						.findC101m29Cnt(cntrNoBr, cntrNo, datasrc, bgnDate,
								endDate);
				if (c101m29Data != null && !c101m29Data.isEmpty()) {
					Integer allCnt = MapUtils.getInteger(c101m29Data, "ALLCNT",
							0);
					Integer a3Cnt = MapUtils
							.getInteger(c101m29Data, "A3CNT", 0);

					if (BigDecimal.valueOf(allCnt).compareTo(BigDecimal.ZERO) == 0) { // CMS.C101M29
																						// 無資料
						// 0: 有L260S01C實登資料 1: 無實登資料
						raspStatus = (hasS01c ? "0" : "1");
						// raspStatus = "0";
					} else if (BigDecimal.valueOf(allCnt).compareTo(
							BigDecimal.valueOf(a3Cnt)) == 0) { // CMS.C101M29
																// 有資料 且
																// RECVCODE都是A3
						raspStatus = (hasS01c ? "2" : "3");
						// raspStatus = "1";
					} else { // CMS.C101M29 有資料 RECVCODE不是都A3
						raspStatus = (hasS01c ? "4" : "5");
						// raspStatus = "2";
					}
				}
			}
		}

		return raspStatus;
	}

	/**
	 * J-111-0496 央行不動產管控
	 */
	@SuppressWarnings("unchecked")
	private void upLoadElf600(L260M01D l260m01d) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (l260m01d != null) {
			String cntrNo = Util.nullToSpace(l260m01d.getCntrNo());
			if (this.isCaseMark(l260m01d, "04")) {
				ELF600 elf600 = misELF600Service.findByContract(cntrNo);
				if (elf600 != null && Util.isNotEmpty(elf600)) {
					ELF600 newElf600 = this.convertL260m01dToELF600(l260m01d,
							elf600);
					newElf600.setElf600_updfrom("PMS");
					newElf600.setElf600_updater(user.getUserId());
					newElf600.setElf600_tmestamp(CapDate.getCurrentTimestamp());
					misELF600Service.delete(cntrNo);
					misELF600Service.insert(newElf600);
				} else {
					throw new CapMessageException("額度序號" + cntrNo
							+ "控制擋ELF600查無資料", getClass());
				}
			}
		}
	}
	
	/**
	 * 將資料寫入ALOAN，以便其向聯徵中心通報永續績效連結授信約定條件全部未達成註記 <br/>
	 * <br/>
	 * J-113-0035 為利ESG案件之貸後管控, <br/>
	 * ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制 <br/>
	 * 
	 * @param l260m01a
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private void chgESGFlg(L260M01A l260m01a) throws CapException {
		if (l260m01a != null) {

			List<L260M01D> l260m01dList = (List<L260M01D>) this
					.findListByMainIdNotDel(L260M01D.class,
							l260m01a.getMainId(), true);

			// 未達成註記為Y之計數器
			int countY = 0;
			// 未達成註記為N之計數器
			int countN = 0;
			// 未達成註記為空之計數器
			int countEmpty = 0;

			if (Util.isNotEmpty(l260m01dList) && l260m01dList.size() > 0) {
				for (L260M01D l260m01d : l260m01dList) {
					Map<String, String> followKindMap = this
							.followKindToMap(l260m01d.getFollowKind());
					if (followKindMap
							.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)) {
						List<ELF602> elf602List = misdbBASEService
								.getESGDataMaxFoDate(l260m01d.getCntrNo());
						if (Util.isNotEmpty(elf602List)) {
							for (ELF602 elf602 : elf602List) {
								String sesgsunre = Util.trim(elf602
										.getElf602_sesgsunre());
								if (Util.equals(sesgsunre, "Y")) {
									countY++;
								} else if (Util.equals(sesgsunre, "N")) {
									countN++;
								} else {
									countEmpty++;
								}
							}
						}

					}
				}
			}

			// 此註記，聯徵只接受Y或N
			if (countEmpty == 0) {
				// 該額度下，皆有檢視ESG是否達成，才能更新全部未達成註記
				
				Map<String, Object> map = new HashMap<String, Object>();
				if (countY != 0 && countN == 0 ) {
					// 該額度下，ESG皆為未達成，全部未達成註記更新為Y
					map = msps.callLNSP0600(l260m01a.getCntrNo(), "Y");					
				} else if (countN != 0 ) {
					// 該額度下，ESG其一為達成，全部未達成註記更新為N
					map =  msps.callLNSP0600(l260m01a.getCntrNo(), "N");
				}
				
				if (Util.isNotEmpty(MapUtils.getString(map, "SP_RETURN"))
						&& Util.notEquals(MapUtils.getString(map, "SP_RETURN"),
								"YES")) {
					throw new CapMessageException(Util.trim(map.get("SP_ERROR_MSG")),
							getClass());
				} 
			}
	
		}
	}

	private ELF600 convertL260m01dToELF600(L260M01D l260m01d, ELF600 newElf600) {
		newElf600.setElf600_universal_id(l260m01d.getMainId());
		newElf600.setElf600_act_st_date(l260m01d.getActStDate());
		
		if(this.centralBankControlService.isRemarkRrmovedt(l260m01d.getActStDate(), l260m01d.getCntrNo())){
			newElf600.setElf600_rrmovedt(l260m01d.getActStDate());
		}
		
		return newElf600;
	}
	
	@SuppressWarnings("unchecked")
	public void findLastESGDataAndSave(List<L260M01D> fromL260M01DList)
			throws CapException {

		if (Util.isNotEmpty(fromL260M01DList) && fromL260M01DList.size() > 0) {

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			Locale locale = LMSUtil.getLocale();

			List<CodeType> list = codetypeService.findByCodeTypeList(
					UtilConstants.Lms8000m01_esg.ESG, locale.toString());
			L260M01A l260m01a = this.findModelByMainId(L260M01A.class,
					fromL260M01DList.get(0).getMainId());

			for (L260M01D fromL260M01D : fromL260M01DList) {

				Map<String, String> followKindMap = this
						.followKindToMap(fromL260M01D.getFollowKind());
				if (!followKindMap
						.containsKey(UtilConstants.Lms8000m01_fllowKind.永續績效目標檢視)
						&& !followKindMap
								.containsKey(UtilConstants.Lms8000m01_fllowKind.其他ESG條件)) {
					// 沒有上述追蹤類別，就不處理待追蹤ESG
					continue;
				}

				List<L260S01F> lastL201S01fList = new ArrayList<L260S01F>();
				String fromL260M01DFieldMainId = Util.trim(fromL260M01D
						.getFieldMainId());
				L260M01D l260m01d = this.findModelByOid(L260M01D.class,
						fromL260M01DFieldMainId);
				boolean l260m01dOKFlg = false;
				if (Util.isNotEmpty(l260m01d)) {
					if (Util.isEmpty(l260m01d.getDeletedTime())) {
						l260m01dOKFlg = true;
					}
				}
				
				if (l260m01dOKFlg && Util.isNotEmpty(fromL260M01DFieldMainId)) {
					/*
					 * 情境一： 有上次的追蹤紀錄，可從l260m01d.fieldmainid，得到上次的l260m01d.oid，
					 * 再由其抓取相關資料
					 */
					lastL201S01fList = (List<L260S01F>) this
							.findListByMainIdNotDel(L260S01F.class,
									fromL260M01DFieldMainId, true);
					if (Util.isNotEmpty(lastL201S01fList)) {
						for (L260S01F oriL260s01f : lastL201S01fList) {
							L260S01F newL260S01F = new L260S01F();
							newL260S01F = CapBeanUtil.copyBean(oriL260s01f,
									newL260S01F, CapBeanUtil.getFieldName(
											L260S01F.class, true));
							newL260S01F.setMainId(fromL260M01D.getOid());
							newL260S01F.setOid(null);
							newL260S01F.setCreator(user.getUserId());
							newL260S01F.setCreateTime(CapDate
									.getCurrentTimestamp());
							save(newL260S01F);
						}
					}
				}

				List<Map<String, Object>> latestL260S01fList = new ArrayList<Map<String, Object>>();
				if (Util.isEmpty(lastL201S01fList)
						|| lastL201S01fList.size() == 0) {
					/*
					 * 情境二： 情境一無資料，可能還是有上次的追蹤紀錄，
					 * 因為是從elf601產生一筆elf602，故這種的l260m01d.fieldmainid為空值 ，
					 * 抓現行紀錄中，最新一筆資料
					 */
					latestL260S01fList = eloandbBASEService.findLatestL260S01F(
							fromL260M01D.getCntrNo(),
							fromL260M01D.getFrom602SUid(),
							fromL260M01D.getFrom602SApptime(),							
							fromL260M01D.getFrom602SSeqno());

					if (Util.isNotEmpty(latestL260S01fList)) {
						for (Map<String, Object> m : latestL260S01fList) {
							L260S01F newL260S01F = new L260S01F();
							newL260S01F = CapBeanUtil.map2Bean(m, newL260S01F);
							newL260S01F.setMainId(fromL260M01D.getOid());
							newL260S01F.setOid(null);
							newL260S01F.setCreator(user.getUserId());
							newL260S01F.setCreateTime(CapDate
									.getCurrentTimestamp());
							save(newL260S01F);
						}
					}
				}

				if ((Util.isEmpty(lastL201S01fList) || lastL201S01fList.size() == 0)
						&& (Util.isEmpty(latestL260S01fList) || latestL260S01fList
								.size() == 0)) {
					/*
					 * 情境三： 情境一、情境二皆無資料， 表示第一次做， 直接寫入ESG資料到目的(L260S01F)
					 */
					if (Util.isNotEmpty(list)) {
						for (CodeType codeType : list) {
							L260S01F newL260S01F = new L260S01F();
							newL260S01F.setMainId(fromL260M01D.getOid());
							newL260S01F.setItemNo(codeType.getCodeValue());
							newL260S01F.setItemSeq(codeType.getCodeOrder());
							newL260S01F.setCreateTime(new Timestamp(System
									.currentTimeMillis()));
							newL260S01F.setCreator(user.getUserId());
							save(newL260S01F);
						}
					}
				}
			}
		}
	}
	
	@Override
	public Map<String, String> followKindToMap(String followKind) {
		Map<String, String> m = new HashMap<String, String>();
		String[] followKindAry = Util.trim(followKind).split("\\|");
		for (String s : followKindAry) {
			m.put(s, s);
		}
		return m;
	}

	/**
	 * J-112-0307
	 * 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	 * 
	 * @param fromL260M01DList
	 * @throws CapException
	 * 
	 */
	@SuppressWarnings("unchecked")
	public void findVisitCompData(List<L260M01D> fromL260M01DList)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		for (L260M01D fromL260M01D : fromL260M01DList) {

			List<L260S01D> fromL260S01DList = (List<L260S01D>) findListByMainIdNotDel(
					L260S01D.class, fromL260M01D.getFieldMainId(), true);

			for (L260S01D fromL260S01D : fromL260S01DList) {

				L260S01D newL260S01D = new L260S01D();
				CapBeanUtil.copyBean(fromL260S01D, newL260S01D,
						CapBeanUtil.getFieldName(L260S01D.class, true));
				newL260S01D.setMainId(fromL260M01D.getOid());
				newL260S01D.setOid(null);
				newL260S01D.setCreateTime(new Timestamp(System
						.currentTimeMillis()));
				newL260S01D.setCreator(user.getUserId());
				save(newL260S01D);

				List<L260S01E> fromL260S01EList = (List<L260S01E>) findListByMainIdNotDel(
						L260S01E.class, fromL260S01D.getOid(), true);

				for (L260S01E fromL260S01E : fromL260S01EList) {

					L260S01E newL260S01E = new L260S01E();
					CapBeanUtil.copyBean(fromL260S01E, newL260S01E,
							CapBeanUtil.getFieldName(L260S01E.class, true));
					newL260S01E.setMainId(newL260S01D.getOid());
					newL260S01E.setOid(null);
					newL260S01E.setCreateTime(new Timestamp(System
							.currentTimeMillis()));
					newL260S01E.setCreator(user.getUserId());
					save(newL260S01E);
				}

			}
		}
	}

	@Override
	public List<Map<String,Object>> findByFilter(PageParameters params) {
		List<Object> paramValues = new ArrayList<Object>();
		StringBuilder condition = parseFilterSQL(params, paramValues);
		return eloandbBASEService.findL260M01A_L260M01DByFilter(condition,paramValues);
	}


    @Override
    public Page<L260M01A> findPageByFilter(PageParameters params) {

		List<Object> paramValues = new ArrayList<Object>();

		int maxRow = params.getInt(IGridEnum.PAGEROWS.getCode());
		int page = params.getAsInteger(IGridEnum.PAGE.getCode());
		int pageRows = params.getAsInteger(IGridEnum.PAGEROWS.getCode());
		int startRow = (page - 1) * pageRows;
		StringBuilder condition = parseFilterSQL(params, paramValues);
		String followUpType = Util.trim(Util.nullToSpace(params
				.getString("followUpType")));
		List<L260M01A> list = new ArrayList<L260M01A>();
		if("C".equalsIgnoreCase(followUpType)){
			list = l260m01cDao.findPageByFilter(condition, paramValues, startRow, maxRow);
		}else if ("D".equalsIgnoreCase(followUpType)) {
			if(condition.indexOf(" AND c.HANDLINGSTATUS IN")==-1) {
				// 排除l206m01d已刪除的
				condition.append(" AND c.HANDLINGSTATUS<> ? ");
				paramValues.add("4"); //4:已刪除
			}
			list = l260m01dDao.findPageByFilter(condition, paramValues, startRow, maxRow);
		}
		return new Page<L260M01A>(list, list.size(), maxRow,startRow);
    }

	private StringBuilder parseFilterSQL(PageParameters params, List<Object> paramValues) {
		StringBuilder condition = new StringBuilder();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);
		String followKind = Util.trim(Util.nullToSpace(params
				.getString("followKind")));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String for940 = params.getString("for940","10".equals(followKind)&&"940".equals(user.getUnitNo())?"Y":"N");
		String docStatusQ = "";
		for(int i=0;i< docStatusArray.length;i++){
			docStatusQ += "?,";
			paramValues.add(docStatusArray[i]);
		}

		condition.append(" a.docStatus IN (").append(docStatusQ, 0, docStatusQ.length() - 1).append(") ");
		if("Y".equals(for940)) {
			// 企金處批次時查全行
			condition.append(" AND c.HANDLINGSTATUS IN(?,?) ");
			paramValues.add("2");//2:辧理中
			paramValues.add("3");//3:已完成
		}else{
			condition.append(" AND a.").append(UtilConstants.Field.目前編製行).append("=? ");
			paramValues.add(user.getUnitNo());
		}

		condition.append(" AND a.").append(UtilConstants.Field.刪除時間).append(" is null ");

		String searchType = Util.nullToSpace(params.getString("searchType"));

		if (Util.equals(searchType, "filter")) {
			String custId = Util.trim(Util.nullToSpace(params
					.getString("custId")));
			if (!Util.isEmpty(custId)) {
				List<String> custIds = null;
				if (custId.contains(",")) {
					custIds = Arrays.asList(custId.split(","));
					paramValues.addAll(custIds);
					String custIdQ = "";
					for (int i = 0; i < custIds.size(); i++) {
						custIdQ += "?,";

					}
					condition.append(" AND a.").append(UtilConstants.Field.CUSTID).append(" IN (").append(custIdQ, 0, custIdQ.length() - 1).append(") ");
				} else {
					paramValues.add(custId);
					condition.append(" AND a.").append(UtilConstants.Field.CUSTID).append(" =? ");
				}
			}
			String cntrNo = Util.trim(Util.nullToSpace(params
					.getString("cntrNo")));

			String loanNo = Util.trim(Util.nullToSpace(params
					.getString("loanNo")));
			if (!Util.isEmpty(cntrNo)) {
				condition.append(" AND ").append(" cntrNo=? ");
				paramValues.add(cntrNo);
			}
			if (!Util.isEmpty(loanNo)) {
				condition.append(" AND ").append(" loanNo=? ");
				paramValues.add(loanNo);
			}


			String followKinds[] = followKind.split("\\|");

			String tmp = "";
			for (String f : followKinds) {
				tmp += " followKind like ? or ";
				paramValues.add("%" + f + "%");
			}
			tmp = tmp.substring(0, tmp.length() - 3);
			condition.append(" AND (").append(tmp).append(") ");

		}
		return condition;
	}

	@SuppressWarnings("unchecked")
	public CapAjaxFormResult showL260s01f(String l260m01fMainId)
			throws CapException {

		Locale locale = LMSUtil.getLocale();
		Map<String, String> esgMap = codetypeService.findByCodeType(
				UtilConstants.Lms8000m01_esg.ESG, locale.toString());

		CapAjaxFormResult result = new CapAjaxFormResult();

		List<L260S01F> l260s01fList = (List<L260S01F>) findListByMainIdNotDel(
				L260S01F.class, l260m01fMainId, true);

		JSONArray ja = new JSONArray();
		if (Util.isNotEmpty(l260s01fList)) {
			for (L260S01F model : l260s01fList) {

				JSONObject data = DataParse.toJSON(model);

				String title = Util.trim(MapUtils.getString(esgMap,
						model.getItemNo()));

				if (Util.isNotEmpty(title)) {
					data.put("itemContent", title);
				}

				ja.add(data);
			}
		}
		result.set("L260S01FArray", ja);
		return result;

	}
		
	public Map<String, Object> findESGData(String oid, String brNo) {
		L260M01D l260m01d = this.findModelByOid(L260M01D.class, oid);
		String from602SApptime = "";
		Map<String, Object> map = null;
		//ELF603Key檢核, 無資料跳過
		if(Util.isNotEmpty(l260m01d.getFrom602SUid()) && Util.isNotEmpty(l260m01d.getFrom602SApptime()) && Util.isNotEmpty(l260m01d.getFrom602SSeqno())){
			SimpleDateFormat sdf1 = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);		 
			from602SApptime=sdf1.format(l260m01d.getFrom602SApptime().getTime());	
			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService
					.getBranch(brNo).getBrNoFlag());
			
			if(isOverSea){
				BigDecimal as400appTime = LMSUtil.covertAs400Time(CapDate.convertStringToTimestamp(from602SApptime));
				String format_as400appTime = Util.trim(as400appTime);
				map = obsdbELF603Service.getElf603ByKey(brNo, l260m01d.getFrom602SUid(), format_as400appTime, l260m01d.getCntrNo(), Util.trim(l260m01d.getFrom602SSeqno()));
			}else{
				map = misdbBASEService.getElf603ByKey(
						l260m01d.getFrom602SUid(), from602SApptime,
						l260m01d.getCntrNo(), l260m01d.getFrom602SSeqno());
			}
		}
		return map;				
	}

	/**
	 * J-112-0307
	 * 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	 * 
	 * @param l260m01dOid
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapAjaxFormResult findL260s01d(String l260m01dOid, String custId,
			String dupNo, boolean modCompVisitVerFlag) throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		// 取得使用者登入資料
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得使用者單位別所屬分行
		IBranch branchtype = branchService.getBranch(user.getUnitNo());
		// 現行紀錄表最新版本
		final String latestVisitComVer = UtilConstants.Lms8000m01_visitComLastVer.V_O_202308;
		// 取得現行語系
		Locale locale = LMSUtil.getLocale();
		// 訪問紀錄表版本
		String newVisitType = "";

		String rptId_S01D = "";

		L260S01D l260s01d = null;

		L260M01D l260m01d = findModelByOid(L260M01D.class, l260m01dOid);
		if (Util.isNotEmpty(l260m01d) && modCompVisitVerFlag) {
			l260m01d.setCheckYN("");
			save(l260m01d);
		}

		List<L260S01D> l260s01dList = (List<L260S01D>) findListByMainIdNotDel(
				L260S01D.class, l260m01dOid, true);
		List<L260S01E> l260s01eList = new ArrayList<L260S01E>();

		if (Util.isNotEmpty(l260s01dList) && l260s01dList.size() > 0) {
			// 有編輯中的資料

			l260s01d = l260s01dList.get(0);
			rptId_S01D = Util.trim(l260s01d.getRptId_S01D());

			if (Util.isNotEmpty(l260s01d)) {

				// 查詢公司訪問紀錄表項目
				l260s01eList = (List<L260S01E>) findListByMainIdNotDel(
						L260S01E.class, l260s01d.getOid(), true);

				// 有點擊「修改公司訪問紀錄表格式為最新版本」鈕
				if (modCompVisitVerFlag) {

					l260s01d.setDeletedTime(new Timestamp(System
							.currentTimeMillis()));
					save(l260s01d);

					if (Util.isNotEmpty(l260s01eList)) {
						for (L260S01E l206s01e : l260s01eList) {
							l206s01e.setDeletedTime(new Timestamp(System
									.currentTimeMillis()));
							save(l206s01e);
						}
					}

					l260s01d = null;
					l260s01eList = null;
				}

			}
		} else {
			// 沒有編輯中的資料，則找同一筆ID中，訪問日期、異動日期最新一筆的訪問紀錄表

			Map<String, Object> m = eloandbBASEService.findLatestL260S01D(
					l260m01dOid, custId, dupNo);
			if (Util.isNotEmpty(m)) {
				L260S01D newL260S01D = new L260S01D();
				newL260S01D = CapBeanUtil.map2Bean(m, newL260S01D);
				rptId_S01D = Util.trim(newL260S01D.getRptId_S01D());
				String oriL260S01Doid = Util.trim(newL260S01D.getOid());

				if (LrsUtil.compareRptVersion(rptId_S01D, "<",
						latestVisitComVer)) {
					// 版本別小於最新版本別時，需產生新的訪問紀錄表
					l260s01d = null;
					rptId_S01D = "";
				} else {
					// 複製l260s01d、l260s01e
					newL260S01D.setMainId(l260m01dOid);
					newL260S01D.setOid(null);
					newL260S01D.setCreator(user.getUserId());
					newL260S01D.setCreateTime(CapDate.getCurrentTimestamp());
					save(newL260S01D);
					l260s01d = newL260S01D;

					List<L260S01E> oriL260S01EList = (List<L260S01E>) findListByMainIdNotDel(
							L260S01E.class, oriL260S01Doid, true);
					if (Util.isNotEmpty(oriL260S01EList)
							&& oriL260S01EList.size() > 0) {
						l260s01eList = new ArrayList<L260S01E>();
						for (L260S01E l260s01e : oriL260S01EList) {
							L260S01E newL260S01E = new L260S01E();
							newL260S01E = CapBeanUtil.copyBean(l260s01e,
									newL260S01E, new String[] { "itemNo",
											"itemSeq", "itemContent",
											"chkResult", "chkContactItem",
											"chkText", "itemType",
											"itemSeqShow" });
							newL260S01E.setMainId(newL260S01D.getOid());
							newL260S01E.setOid(null);
							newL260S01E.setCreator(user.getUserId());
							newL260S01E.setCreateTime(CapDate
									.getCurrentTimestamp());
							save(newL260S01E);
							l260s01eList.add(newL260S01E);
						}
					}
				}
			}
		}

		// 無公司訪問紀錄表時：顯示最新版資料
		if (Util.isEmpty(l260s01d)) {

			// 決定訪問紀錄表版本別
			if (Util.isEmpty(l260s01d) || Util.equals("", rptId_S01D)) {
				newVisitType = "l260s01e_V1";
			} else {

				if (LrsUtil.compareRptVersion(rptId_S01D, "<=",
						UtilConstants.Lms8000m01_visitComVer.V_O_202308)) {
					newVisitType = "l260s01e_V1";
				}

			}
			// 加拿大英語系
			if (Util.equals(locale, "en")
					&& UtilConstants.Country.加拿大.equals(branchtype
							.getCountryType())) {
				newVisitType = newVisitType + "_CA";
			}

			rptId_S01D = latestVisitComVer;

			List<CodeType> codetypeList = codetypeService
					.findByCodeTypeList(newVisitType);

			l260s01d = new L260S01D();
			l260s01d.setMainId(l260m01dOid);
			l260s01d.setCreator(user.getUserId());
			l260s01d.setCreateTime(CapDate.getCurrentTimestamp());
			l260s01d.setRptId_S01D(rptId_S01D);
			save(l260s01d);

			l260s01dList = (List<L260S01D>) findListByMainIdNotDel(
					L260S01D.class, l260m01dOid, true);
			if (Util.isNotEmpty(l260s01dList) && l260s01dList.size() > 0) {
				l260s01d = l260s01dList.get(0);
				if (Util.isNotEmpty(l260s01d)) {
					l260s01eList = new ArrayList<L260S01E>();
					for (CodeType codetype : codetypeList) {
						L260S01E model = new L260S01E();
						model.setMainId(l260s01d.getOid());
						model.setItemNo(codetype.getCodeValue());
						model.setItemContent(codetype.getCodeDesc());
						model.setItemType(codetype.getCodeDesc2());
						model.setItemSeq(codetype.getCodeOrder());
						model.setItemSeqShow(codetype.getCodeDesc3());
						model.setChkResult("");
						model.setCreator(user.getUserId());
						model.setCreateTime(CapDate.getCurrentTimestamp());
						save(model);
						l260s01eList.add(model);
					}
				}

			}

		}

		result.putAll(DataParse.toResult(l260s01d, DataParse.Delete,
				new String[] { EloanConstants.MAIN_ID, EloanConstants.OID }));

		JSONArray ja = new JSONArray();
		JSONObject typeJson = new JSONObject();
		for (L260S01E model : l260s01eList) {
			JSONObject data = DataParse.toJSON(model);
			// 覆審類別 A,B...
			String type = model.getItemType();
			int count = Util.parseInt((String) typeJson.get(type));
			typeJson.put(type, String.valueOf(++count));
			ja.add(data);
		}
		result.set("L260S01EArray", ja);

		ElsUser u = userService.getUser(l260s01d.getUnitMgr_S01D());
		if (Util.isNotEmpty(u)) {
			result.set("unitMgrName_S01D", u.getUserName());
		} else {
			result.set("unitMgrName_S01D", "");
		}
		u = userService.getUser(l260s01d.getAccountMgr_S01D());
		if (Util.isNotEmpty(u)) {
			result.set("accountMgrName_S01D", u.getUserName());
		} else {
			result.set("accountMgrName_S01D", "");
		}
		
		result.set("typeJson", new CapAjaxFormResult(typeJson));
		result.set("rptId_S01D", rptId_S01D);
		result.set("Country", branchtype.getCountryType());
		return result;

	}

	@Override
	public L260S01E findL260s01eByUniqueKey(String mainId, String itemNo) {
		return l260s01eDao.findByUniqueKey(mainId, itemNo);
	}

	@Override
	public CapAjaxFormResult saveL260S01D(JSONObject jsonData, L260M01D l260m01d)
			throws CapException {		
		List<L260S01D> l260s01dList = (List<L260S01D>) findListByMainIdNotDel(
				L260S01D.class, l260m01d.getOid(), true);
		L260S01D l260s01d = new L260S01D();

		// 類別是否為公司訪問紀錄表
		String[] tmp = l260m01d.getFollowKind().split("\\|");
		boolean haveVisitComp = false;
		for (String s : tmp) {
			if (Util.equals(s, UtilConstants.Lms8000m01_fllowKind.公司訪問紀錄表限ID階層)) {
				haveVisitComp = true;
				break;
			}
		}

		if (Util.isNotEmpty(l260s01dList) && l260s01dList.size() > 0) {
			// 有L260S01D、L260S01E，但類別沒有公司訪問紀錄表，押上刪除時間
			l260s01d = l260s01dList.get(0);
			DataParse.toBean(jsonData, l260s01d);

			if (!haveVisitComp) {
				l260s01d.setDeletedTime(new Timestamp(System
						.currentTimeMillis()));

				List<L260S01E> l260s01eList = (List<L260S01E>) findListByMainIdNotDel(
						L260S01E.class, l260s01d.getOid(), true);
				for (L260S01E l260s01e : l260s01eList) {
					l260s01e.setDeletedTime(new Timestamp(System
							.currentTimeMillis()));
					save(l260s01e);
				}
			}

			save(l260s01d);
		}

		l260s01dList = (List<L260S01D>) findListByMainIdNotDel(L260S01D.class,
				l260m01d.getOid(), true);
		List<L260S01E> l260s01eList = new ArrayList<L260S01E>();
		if (Util.isNotEmpty(l260s01dList) && l260s01dList.size() > 0) {
			L260S01D l260s01dFromDB = l260s01dList.get(0);
			Map<String, String> sameGroupMap = new HashMap<String, String>();

			if (LrsUtil.compareRptVersion(l260s01dFromDB.getRptId_S01D(), "<=",
					UtilConstants.Lms8000m01_visitComVer.V_O_202308)) {
				sameGroupMap.put("A019", "A019");
				sameGroupMap.put("A020", "A020");
			}

			if (jsonData.get("itemNo_S01E") instanceof JSONArray) {
				JSONArray itemNoAry = jsonData.getJSONArray("itemNo_S01E");
				for (int i = 0; i < itemNoAry.size(); i++) {
					String itemNo = Util.trim(itemNoAry.getString(i));
					L260S01E l260s01eFromDB = findL260s01eByUniqueKey(
							l260s01dFromDB.getOid(), itemNo);
					String chkResult = "";
					chkResult = chkResult
							+ Util.trim(jsonData.get("chkResult"
									+ itemNoAry.getString(i) + "_S01E"));
					String chkText = Util.trim(jsonData.get("chkText"
							+ itemNoAry.getString(i) + "_S01E"));

					if (sameGroupMap.containsKey(itemNo)) {

						if (jsonData.get("chkResult" + itemNoAry.getString(i)
								+ "_1_S01E") instanceof JSONArray) {

							JSONArray itemNoAry2 = jsonData
									.getJSONArray("chkResult"
											+ itemNoAry.getString(i)
											+ "_1_S01E");

							for (int j = 0; j < itemNoAry2.size(); j++) {

								if (j == 0 && !chkResult.equals("")) {
									chkResult = chkResult + "@";
								}

								chkResult = chkResult
										+ Util.trim(itemNoAry2.getString(j))
										+ "@";
							}
							chkResult = chkResult.substring(0,
									chkResult.length() - 1);
							l260s01eFromDB.setChkResult(Util.trim(chkResult));

						} else if (Util.isNotEmpty(jsonData.get("chkResult"
								+ itemNoAry.getString(i) + "_1_S01E"))) {
							chkResult = chkResult
									+ "@"
									+ jsonData.get("chkResult"
											+ itemNoAry.getString(i)
											+ "_1_S01E");
						}

					}

					l260s01eFromDB.setChkResult(Util.trim(chkResult));
					l260s01eFromDB.setChkText(chkText);

					save(l260s01eFromDB);
					l260s01eList.add(l260s01eFromDB);
				}
			}

		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONArray ja = new JSONArray();
		JSONObject typeJson = new JSONObject();
		for (L260S01E model : l260s01eList) {
			JSONObject data = DataParse.toJSON(model);
			// 覆審類別 A,B...
			String type = model.getItemType();
			int count = Util.parseInt((String) typeJson.get(type));
			typeJson.put(type, String.valueOf(++count));
			ja.add(data);
		}

		result.putAll(DataParse.toResult(l260s01d, DataParse.Delete,
				new String[] { EloanConstants.MAIN_ID, EloanConstants.OID }));

		result.set("L260S01EArray", ja);

		return result;
	}

}
