/* 
 * L140M01S.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 空地貸款資訊檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140MM4C")
public class L140MM4C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 本次(Y)，前次(N)flag
	 */
	@Column(name = "FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String flag;
	
	/**
	 * 額度序號
	 */
	@Column(name = "CONTRACT", length = 12, columnDefinition = "CHAR(12)")
	private String contract;
	
	/**
	 * 首次動用日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "USE_DATE", columnDefinition = "DATE")
	private Date use_Date;
	
	/**
	 * 使用分區類別	1.都市土地	2.非都市土地
	 */
	@Column(name = "USECD", length = 1, columnDefinition = "CHAR(1)")
	private String useCd;
	
	/**
	 * 使用分區
	 * 使用分區類別-都市土地 
	 * 		01住宅區 	02商業區 	03工業區		04行政區		05文教區		06風景區		07保護區
	 * 		08農業區		09住商混合區	10其他 		11倉庫區		12行水區		13保存區		14特定專用區
	 * 		15公共設施用地
	 * 使用分區類別-非都市土地
	 * 		01特定農業區		02一般農業區		03鄉村區		04工業區		05森林區		06山坡地保育區
	 * 		07風景區 		08特定專用區 		09住宅區		10其他 		11國家公園區	12河川區
	 */
	@Column(name = "USETYPE", length = 2, columnDefinition = "CHAR(2)")
	private String useType;
	
	/**
	 * 用地類別
	 */
	@Column(name = "LANDTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String landType;
	
	/**
	 * 土地性質
	 * 01其他	02高爾夫球用地	03空地	04農地	05林地	06養殖地	
	 * 07房地建地(不含建物)	08土地及建物		09土地及廠房
	 */
	@Column(name = "LANDKIND", length = 2, columnDefinition = "CHAR(2)")
	private String landKind;
	
	/**
	 * 聯徵B50閒置工業用地 Y/N
	 */
	@Column(name = "IDLELAND", length = 1, columnDefinition = "CHAR(1)")
	private String idleLand;
	
	/**
	 * 控管類別
	 * 1.工業區貸放款1	2.工業區貸放款2		3.工業區貸放款3
	 * 4.工業區貸放款4	5.興建房屋貸放款		X.非控管註記……??
	 */
	@Column(name = "CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctlType;
	
	/**
	 * 初次核定預計動工日
	 * IF 最新核定(動審)預計動工日 IS NULL,同步更新最新核定(動審)預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "FSTDATE", columnDefinition = "DATE")
	private Date fstDate;
	
	/**
	 * 最新核定(動審)預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LSTDATE", columnDefinition = "DATE")
	private Date lstDate;
	
	/**
	 * 解除控管日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RRMOVEDT", columnDefinition = "DATE")
	private Date rrmoveDate;
	
	/**
	 * 變更預計動工日簽報書狀態	1.簽報核准 2.完成動審
	 */
	@Column(name = "ELFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elFlag;
	
	/**
	 * 變更預計動工日最新簽報日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "DATE")
	private Date endDate;
	
	/**
	 * 變更預計動工日簽報書編號
	 */
	@Column(name = "DOCUMENTNO", length = 20, columnDefinition = "CHAR(20)")
	private String documentNo;
	
	/**
	 * 變更預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CSTDATE", columnDefinition = "DATE")
	private Date cstDate;
	
	/**
	 * 採行措施	1.縮減額度 2.是否分期收回借款 3.利率調整
	 */
	@Column(name = "ADOPTFG", length = 1, columnDefinition = "CHAR(1)")
	private String adoptFg;
	
	/**
	 * 利率再加碼幅度
	 */
	@Digits(integer = 4, fraction = 5, groups = Check.class)
	@Column(name = "RATEADD", length = 1, columnDefinition = "DECIMAL(9,5)")
	private BigDecimal rateAdd;
	
	/**
	 * 借款人ROA
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "CUSTROA", length = 1, columnDefinition = "DECIMAL(15,2)")
	private BigDecimal custRoa;
	
	/**
	 * 關係人ROA
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "RELROA", length = 1, columnDefinition = "DECIMAL(15,2)")
	private BigDecimal relRoa;
	
	/**
	 * 變更預計動工日原因
	 * (01)房市景氣不佳,延緩開發	(02)變更土地使用分區	(03)都更審議	(04)環評耗時	(05)政府單位審查耗時
	 * (06)土地整合耗時	(07)變更興建計畫,尚未申請建照或重新申請建照尚未核發	
	 * (08)建商推案較多,考量營運成本,延緩其他建案開發	(09)建築人力短缺	(10)營收成長未如預期,暫緩擴廠計畫
	 */
	@Column(name = "CSTREASON", length = 2, columnDefinition = "CHAR(2)")
	private String cstReason;
	
	/**
	 * 是否符合本行規定 Y/N
	 */
	@Column(name = "ISLEGAL", length = 1, columnDefinition = "CHAR(1)")
	private String isLegal;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	public void setFlag(String value) {
		this.flag = value;
	}

	public String getFlag() {
		return flag;
	}

	public void setContract(String value) {
		this.contract = value;
	}

	public String getContract() {
		return contract;
	}

	public void setUse_Date(Date value) {
		this.use_Date = value;
	}

	public Date getUse_Date() {
		return use_Date;
	}
	
	public void setUseCd(String value) {
		this.useCd = value;
	}

	public String getUseCd() {
		return useCd;
	}
	
	public void setUseType(String value) {
		this.useType = value;
	}

	public String getUseType() {
		return useType;
	}
	
	public void setLandType(String value) {
		this.landType = value;
	}

	public String getLandType() {
		return landType;
	}
	
	public void setLandKind(String value) {
		this.landKind = value;
	}

	public String getLandKind() {
		return landKind;
	}

	public void setIdleLand(String value) {
		this.idleLand = value;
	}

	public String getIdleLand() {
		return idleLand;
	}
	
	public void setCtlType(String value) {
		this.ctlType = value;
	}

	public String getCtlType() {
		return ctlType;
	}
	
	public void setFstDate(Date value) {
		this.fstDate = value;
	}

	public Date getFstDate() {
		return fstDate;
	}
	
	public void setLstDate(Date value) {
		this.lstDate = value;
	}

	public Date getLstDate() {
		return lstDate;
	}

	public void setRrmoveDate(Date value) {
		this.rrmoveDate = value;
	}

	public Date getRrmoveDate() {
		return rrmoveDate;
	}

	public void setElFlag(String value) {
		this.elFlag = value;
	}

	public String getElFlag() {
		return elFlag;
	}

	public void setEndDate(Date value) {
		this.endDate = value;
	}

	public Date getEndDate() {
		return endDate;
	}
	
	public void setDocumentNo(String value) {
		this.documentNo = value;
	}

	public String getDocumentNo() {
		return documentNo;
	}

	public void setCstDate(Date value) {
		this.cstDate = value;
	}

	public Date getCstDate() {
		return cstDate;
	}

	public void setAdoptFg(String value) {
		this.adoptFg = value;
	}

	public String getAdoptFg() {
		return adoptFg;
	}
	
	public BigDecimal getRateAdd() {
		return this.rateAdd;
	}

	public void setRateAdd(BigDecimal value) {
		this.rateAdd = value;
	}
	
	public BigDecimal getCustRoa() {
		return this.custRoa;
	}

	public void setCustRoa(BigDecimal value) {
		this.custRoa = value;
	}
	
	public BigDecimal getRelRoa() {
		return this.relRoa;
	}

	public void setRelRoa(BigDecimal value) {
		this.relRoa = value;
	}

	public void setCstReason(String value) {
		this.cstReason = value;
	}

	public String getCstReason() {
		return cstReason;
	}

	public void setIsLegal(String value) {
		this.isLegal = value;
	}

	public String getIsLegal() {
		return isLegal;
	}
}
