
package com.mega.eloan.lms.mfaloan.service.impl;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF490;
import com.mega.eloan.lms.mfaloan.service.MisELF490Service;

/**
 * <pre>
 * 覆審資料檔
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,EL08034,new
 *          </ul>
 */
@Service
public class MisELF490ServiceImpl extends AbstractMFAloanJdbc implements
MisELF490Service {

	@Override
	public List<ELF490> findByDataymBrno(String elf490_data_ym_beg,
			String elf490_data_ym_end, String elf490_br_no) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF490.selByDataymBrno", new String[]{elf490_data_ym_beg,elf490_data_ym_end,elf490_br_no});
		List<ELF490> list = new ArrayList<ELF490>();
		for (Map<String, Object> row : rowData) {
			ELF490 model = new ELF490();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public List<ELF490> findCustIdRecord(String elf490_data_ym_S, String elf490_data_ym_E, String custId, String dupNo){
		
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF490.findCustIdRecord"
				, new String[]{elf490_data_ym_S,elf490_data_ym_E,custId, dupNo});
		List<ELF490> list = new ArrayList<ELF490>();
		for (Map<String, Object> row : rowData) {
			ELF490 model = new ELF490();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public List<ELF490> findDataymBrnoCustIdDupNo(String elf490_data_ym, String elf490_br_no, String custId, String dupNo){
		List<ELF490> result = new ArrayList<ELF490>();
		for(ELF490 elf490 : findCustIdRecord(elf490_data_ym, elf490_data_ym, custId, dupNo)){
			if(Util.equals(elf490_br_no, elf490.getElf490_br_no())){
				result.add(elf490);
			}
		}
		return result;
	}
	
	@Override
	public String findMaxDataYM(){
		Map<String, Object> row = getJdbc().queryForMap("ELF490.findMaxDataYM",
				null);
		return MapUtils.getString(row, "ELF490_DATA_YM");
	}
}
