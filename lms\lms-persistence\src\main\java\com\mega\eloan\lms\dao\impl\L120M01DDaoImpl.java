package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120M01DDao;
import com.mega.eloan.lms.model.L120M01D;

/** 簽報書敘述說明檔 **/
@Repository
public class L120M01DDaoImpl extends LMSJpaDao<L120M01D, String> implements
		L120M01DDao {

	@Override
	public L120M01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120M01D> list = createQuery(L120M01D.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L120M01D findByUniqueKey(String mainId, String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);

		return findUniqueOrNone(search);
	}

	@Override
	public int delModel(String mainId) {
		Query query = getEntityManager().createNamedQuery("L120M01D.delModel");
		query.setParameter("MAINID", mainId); // 設置參數
		return query.executeUpdate();
	}

	@Override
	public List<L120M01D> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		return createQuery(L120M01D.class, search).getResultList();
	}

}