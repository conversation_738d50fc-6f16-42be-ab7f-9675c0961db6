var initDfd = new $.Deferred(),
	initAll = new $.Deferred();

$(document).ready(function(){
	lms7205Grid();
	var noOpenDoc = false;
	
	var auth = (responseJSON ? responseJSON.Auth : {}); //權限
/*
	if (auth.readOnly){
		setIgnoreTempSave(true);
	}
*/
	//alert(JSON.stringify(responseJSON));	
	setCloseConfirm(true);/*設定關閉此畫面時要詢問*/	
	//控制文件類別顯示
	if (responseJSON.docType == "1") {
		var $showBorrowData = $("#showBorrowData");
		$showBorrowData.find("#title0a").show();
		$showBorrowData.find("#title0b").hide();
	}else if (responseJSON.docType == "2") {
		var $showBorrowData = $("#showBorrowData");
		$showBorrowData.find("#title0a").hide();
		$showBorrowData.find("#title0b").show();
	}	
	if(responseJSON.docKind == "2"){
		var $showBorrowData = $("#showBorrowData");
		if(responseJSON.docCode == "1"){
			$showBorrowData.find("#title1").show();
			$showBorrowData.find("#title1a").hide();
			$showBorrowData.find("#title1b").hide();
		}else if(responseJSON.docCode == "2"){
			$showBorrowData.find("#title1a").show();
			$showBorrowData.find("#title1").hide();
			$showBorrowData.find("#title1b").hide();
		}else if(responseJSON.docCode == "3"){
			$showBorrowData.find("#title1b").show();
			$showBorrowData.find("#title1").hide();
			$showBorrowData.find("#title1a").hide();
		}		
	}else{
		if (responseJSON.authLvl == "2") {
			var $showBorrowData = $("#showBorrowData");
			// 總行授權內
			if(responseJSON.docCode == "1"){
				$showBorrowData.find("#title1f").show();
				$showBorrowData.find("#title1g").hide();
				$showBorrowData.find("#title1h").hide();
			}else if(responseJSON.docCode == "2"){
				$showBorrowData.find("#title1g").show();
				$showBorrowData.find("#title1f").hide();
				$showBorrowData.find("#title1h").hide();
			}else if(responseJSON.docCode == "3"){
				$showBorrowData.find("#title1h").show();
				$showBorrowData.find("#title1f").hide();
				$showBorrowData.find("#title1g").hide();
			}	
		}else if(responseJSON.authLvl == "1"){
			var $showBorrowData = $("#showBorrowData");
			// 分行授權內
			if(responseJSON.docCode == "1"){
				$showBorrowData.find("#title1c").show();
				$showBorrowData.find("#title1d").hide();
				$showBorrowData.find("#title1e").hide();
			}else if(responseJSON.docCode == "2"){
				$showBorrowData.find("#title1d").show();
				$showBorrowData.find("#title1c").hide();
				$showBorrowData.find("#title1e").hide();
			}else if(responseJSON.docCode == "3"){
				$showBorrowData.find("#title1e").show();
				$showBorrowData.find("#title1c").hide();
				$showBorrowData.find("#title1d").hide();
			}			
		}else{
			// 營運中心授權內
			if(responseJSON.docCode == "1"){
				$showBorrowData.find("#title1i").show();
				$showBorrowData.find("#title1j").hide();
				$showBorrowData.find("#title1k").hide();
			}else if(responseJSON.docCode == "2"){
				$showBorrowData.find("#title1j").show();
				$showBorrowData.find("#title1i").hide();
				$showBorrowData.find("#title1k").hide();
			}else if(responseJSON.docCode == "3"){
				$showBorrowData.find("#title1k").show();
				$showBorrowData.find("#title1j").hide();
				$showBorrowData.find("#title1i").hide();
			}			
		}				
	}
	
	//依照不同系統控制顯示頁籤
	$.ajax({
		type : "POST",
		handler : "lms1215formhandler",
		data : 
		{
			formAction : "checkBookmark",
			mainId : responseJSON.mainId,
			docKind : responseJSON.docKind,
			docCode : responseJSON.docCode,
			docStatus : responseJSON.mainDocStatus
			
		},
		success:function(responseData){
			for(o in responseData.hideBook){
				$(DOMPurify.sanitize(responseData.hideBook[o])).hide();
			}
			$(".tabs-warp").scrollToTab();			
		}
	});

	//呈主管覆核 選授信主管人數
	$("#numPerson").change(function(){
		LMSM05Panel_js_obj.numPerson_change(parseInt($(this).val(),10));
	});
	$("#LMS1205S01Form").find("[name=areaChk]").click(function(i){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		if($(this).val()=="2"){
			$LMS1205S01Form.find("#divArea1").show();
			$LMS1205S01Form.find("#divArea2").hide();
		}else if($(this).val()=="3"){
			$LMS1205S01Form.find("#divArea1").hide();
			$LMS1205S01Form.find("#divArea2").show();
		}else{
			$LMS1205S01Form.find("#divArea1").hide();
			$LMS1205S01Form.find("#divArea2").hide();
		}
	});
	
	if($("#purpose3").attr("checked")){
		$("#chkboxA-2-3c1_edit").show();
	}else{
		$("#chkboxA-2-3c1_edit").hide();
		$("#purposeOth").val("");
	}	
	if($("#resource3").attr("checked")){
		$("#chkboxA-2-3c2_edit").show();
	}else{
		$("#chkboxA-2-3c2_edit").hide();
		$("#resourceOth").val("");
	}
	
	$.form.init({
        formHandler: "lms1215formhandler",
        formPostData:{
        	formAction : "queryLms1205m01",
        	oid : responseJSON.oid,
        	page : responseJSON.page,
        	mainId : responseJSON.mainId,
        	docType : responseJSON.docType,
        	docCode : responseJSON.docCode,
        	docKind : responseJSON.docKind,
			docStatus : responseJSON.mainDocStatus,
			areaDocstatus : responseJSON.areaDocstatus,
			txCode : responseJSON.txCode,
			noOpenDoc : noOpenDoc,
        	itemDscr03 : "",
			itemDscr05 : "",
			ffbody : ""
        },
		loadSuccess:function(jsonInit){	
			// Miller 2012-06-28 用來取代Grid前端隱藏欄位傳到後端的值
			responseJSON["mainDocStatus"] = jsonInit._mainDocStatus;
			responseJSON["mainId"] = jsonInit._mainId;
			responseJSON["mainOid"] = jsonInit._mainOid;
			responseJSON["docType"] = jsonInit._docType;
			responseJSON["docCode"] = jsonInit._docCode;
			responseJSON["docKind"] = jsonInit._docKind;
			responseJSON["docURL"] = jsonInit._docURL;
			responseJSON["ownBrId"] = jsonInit._ownBrId;
			responseJSON["authLvl"] = jsonInit._authLvl;
			responseJSON["areaDocstatus"] = jsonInit._areaDocstatus;
			responseJSON["areaBrId"] = jsonInit._areaBrId;
			responseJSON["oid"] = jsonInit._oid;
			responseJSON["hqMeetFlag"] = jsonInit._hqMeetFlag;
			responseJSON["typCd"] = jsonInit._typCd;
			
			setTimeout(function(){
				var $showBorrowData = $("#showBorrowData");
				//依照不同文件狀態控制唯讀
				//分行編製中、分行待補件可編輯，其他都不可編輯
				if(responseJSON.mainDocStatus == "010"
					|| responseJSON.mainDocStatus == "01O"
					|| responseJSON.mainDocStatus == "07O|0EO"
					|| responseJSON.mainDocStatus == "07O"){
					//編製中
					if(auth.Modify && !thickboxOptions.readOnly){
						$("#L120M01dForm06").find("#phraseList").show();
						responseJSON["readOnly"] = false;
						setIgnoreTempSave(false);
						$(this).find("button").show();			
					}else{
						$("#L120M01dForm06").find("#phraseList").hide();
						responseJSON["readOnly"] = true;
						setReadOnly1();	
						setIgnoreTempSave(true);		
					}
				}else{
					//非編製中
					$("#L120M01dForm06").find("#phraseList").hide();
					responseJSON["readOnly"] = true;
					setReadOnly2();
			//		$(this).readOnlyChilds(true);
			        setIgnoreTempSave(true);
			//        $(this).find("button").hide();
			        // 將確認日期挑選改成非唯讀
			        $("#openChecDatekBox").find(":disabled,:input").removeAttr("disabled").removeAttr("readonly")
					   .end().find("#forCheckDate").datepicker();
			        if(responseJSON.mainDocStatus == "L1H"){
			        	$("#openLmsCase").readOnlyChilds(false);	
			        }       
			        $("#sendTo").readOnlyChilds(false);
			        //顯示上方主要標題按鈕
			        $("#buttonPanel :button").show();
			        //顯示異動文件記錄按鈕
			        $("#readDocLog").show();
					if(responseJSON.page == "17"){
						$("#L120M01aForm17 :button").show();
					}					
				}				
/*
			if(responseJSON.txCode == "329090" || responseJSON.txCode == "329091"){
					$("#L120M01dForm06").find("#phraseList").hide();
					responseJSON["readOnly"] = true;
					setReadOnly1();
			        setIgnoreTempSave(true);
			        // 將確認日期挑選改成非唯讀
			        $("#openChecDatekBox").find(":disabled,:input").removeAttr("disabled").removeAttr("readonly")
					   .end().find("#forCheckDate").datepicker();
			        if(responseJSON.mainDocStatus == "L1H"){
			        	$("#openLmsCase").readOnlyChilds(false);	
			        }       
			        $("#sendTo").readOnlyChilds(false);
			        //顯示上方主要標題按鈕
			        $("#buttonPanel :button").show();
			        //顯示異動文件記錄按鈕
			        $("#readDocLog").show();
			}
*/
			
			if(responseJSON.mainDocStatus == "05O"
			 || responseJSON.mainDocStatus == "06O"
			 || responseJSON.mainDocStatus == "L3G"
			 || responseJSON.mainDocStatus == "L4G"){
				$("#btnOpenLmsCase").show();
			}else{
				if(userInfo.unitNo == "918"){
					$("#btnOpenLmsCase").show();
				}else{
					$("#btnOpenLmsCase").hide();
				}
			}

			if(responseJSON.areaDocstatus == "LYC" && userInfo.unitNo == "918"){
				$("#btnBackSea").show();
			}else{
				$("#btnBackSea").hide();
			}
			//新增送審查時補充說明按鈕以及授管處補充說明Ckeditor隱藏
			if(jsonInit.showBorrowData._areaChk == "3"){
				$(".hareaChk").hide();				
			}else{
				$(".hareaChk").show();
			}							
			if(responseJSON.page == "01"){
				var $LMS1205S01Form = $("#LMS1205S01Form");
				$LMS1205S01Form.find("."+jsonInit.hideboss).show();
				$LMS1205S01Form.find("#1105").hide();
				$LMS1205S01Form.find("#1205").show();
				//送會簽
				$LMS1205S01Form.find("#areaBrId1").setItems({
					item : jsonInit.LMS1205S01Form.type2,
					format : " {value} {key}",
					space: false
				});
				//送審查
				$LMS1205S01Form.find("#areaBrId2").setItems({
					item : jsonInit.LMS1205S01Form.type1,
					format : " {value} {key}",
					space: false
				});
				$LMS1205S01Form.setData(jsonInit.LMS1205S01Form, false);
				var areaChk = jsonInit.LMS1205S01Form.areaChk;
				if(areaChk=="2"){
					$LMS1205S01Form.find("#divArea1").show();
					$LMS1205S01Form.find("#divArea2").hide();
					$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
						if($(this).val() == jsonInit.LMS1205S01Form.areaBrId){
							$(this).attr("selected",true);
						}
					});
				}else if(areaChk=="3"){
					$LMS1205S01Form.find("#divArea1").hide();
					$LMS1205S01Form.find("#divArea2").show();
					$LMS1205S01Form.find("#areaBrId2 option").each(function(i){
						if($(this).val() == jsonInit.LMS1205S01Form.areaBrId){
							$(this).attr("selected",true);
						}
					});
				}else{
					$LMS1205S01Form.find("#divArea1").hide();
					$LMS1205S01Form.find("#divArea2").hide();
				}
				$LMS1205S01Form.find("[name=areaChk]").each(function(i){
					if($(this).val() == jsonInit.LMS1205S01Form.areaChk){
						$(this).attr("checked",true);
					}
				});
				// 控制CodeType下拉式選單是否唯讀
	            if (responseJSON.readOnly.toString() == "true") {
	                $("#LMS1205S01Form select").attr("disabled", true);
	                $("#LMS1205S01Form select").attr("disabled", true);
	            }
				$LMS1205S01Form.find("#boss").html(jsonInit.LMS1205S01Form.boss);
				$LMS1205S01Form.find("#seaBoss").html(jsonInit.LMS1205S01Form.seaBoss);
				$LMS1205S01Form.find("#headReCheck").html(jsonInit.LMS1205S01Form.headReCheck);
				$LMS1205S01Form.find("#areaManager").html(jsonInit.LMS1205S01Form.areaManager);
				$LMS1205S01Form.find("#_appraiserCN").html(jsonInit.LMS1205S01Form._appraiserCN);
				$LMS1205S01Form.find("#_headReCheck").html(jsonInit.LMS1205S01Form._headReCheck);
				LMSM05Panel_js_obj.replace_LMSS01Panel_LMS1205S01Form_appr01(jsonInit.LMS1205S01Form);
				
				//判定是否有總行資料，若有則顯示外框，無則隱藏
				if(!jsonInit.LMS1205S01Form.hasAdmin){
					$LMS1205S01Form.find(".hasAdmin1").css("border","0");
					$LMS1205S01Form.find(".hasAdmin2").hide();
				}				
				
				// 控制()顯示隱藏
				var createTime = jsonInit.LMS1205S01Form.createTime;	//文件建立者
				if(createTime != null && createTime != undefined && createTime != ""){
					$LMS1205S01Form.find(".noDataHideA").show();
				}else{
					$LMS1205S01Form.find(".noDataHideA").hide();
				}				
				var updateTime = jsonInit.LMS1205S01Form.updateTime;	//文件異動者
				if(updateTime != null && updateTime != undefined && updateTime != ""){
					$LMS1205S01Form.find(".noDataHideB").show();
				}else{
					$LMS1205S01Form.find(".noDataHideB").hide();
				}																								
				var sendFirstTime = jsonInit.LMS1205S01Form.sendFirstTime; //分行首次送件
				if(sendFirstTime != null && sendFirstTime != undefined && sendFirstTime != ""){
					$LMS1205S01Form.find(".noDataHide1").show();
				}else{
					$LMS1205S01Form.find(".noDataHide1").hide();
				}				
				var sendLastTime = jsonInit.LMS1205S01Form.sendLastTime; //分行最後送件
				if(sendLastTime != null && sendLastTime != undefined && sendLastTime != ""){
					$LMS1205S01Form.find(".noDataHide2").show();
				}else{
					$LMS1205S01Form.find(".noDataHide2").hide();
				}
				
				if(jsonInit.hideReturnBH){
					$LMS1205S01Form.find("#returnBH").hide();
				}else{
					$LMS1205S01Form.find("#returnBH").show();
				}

				lmsM01Json.showPage1(jsonInit); // J-110-0458
/*
				if(jsonInit.showSea){
					$("#LMS1205S01Form").find(".appr4").show();	
				}else{
					$("#LMS1205S01Form").find(".appr4").hide();	
				}
*/						
				// J-110-0493_11557_B1001
	            // 檢查利害關係人授信額度合計是否達新台幣1億元以上
				$LMS1205S01Form.find("#rltOver100MillionTr").hide();
			}else if(responseJSON.page == "03"){
				$("#L120M01BForm").setData(jsonInit.L120M01BForm,false);
			}else if(responseJSON.page == "04"){
				$("#LMS1205S04Form").find("#gist").val(jsonInit.showBorrowData.gist);
			}else if(responseJSON.page == "05"){
				//alert(JSON.stringify(jsonInit.LMS1205S05Form03));
				if(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "2"){
					var $LMS1205S05Form01 = $("#LMS1205S05Form01");
					var $LMS1205S05Form02 = $("#LMS1205S05Form02");
					var $LMS1205S05Form03 = $("#LMS1205S05Form03");
					var $LMS1205S05Form05 = $("#LMS1205S05Form05");
					var $LMS1205S05Form06 = $("#LMS1205S05Form06");
					var $LMS1205S05Form07 = $("#LMS1205S05Form07");					
										
					$LMS1205S05Form01.reset();
					$LMS1205S05Form02.reset();
					$LMS1205S05Form03.reset();
					$LMS1205S05Form05.reset();
					$LMS1205S05Form06.reset();
					$LMS1205S05Form07.reset();
					$LMS1205S05Form01.setData(jsonInit.LMS1205S05Form01,false);
					$LMS1205S05Form02.setData(jsonInit.LMS1205S05Form02,false);
					$LMS1205S05Form03.setData(jsonInit.LMS1205S05Form03,false);
					$("#LMS1205S05Form04").setData(jsonInit.LMS1205S05Form04,false);
					$LMS1205S05Form05.setData(jsonInit.LMS1205S05Form05,false);
					$LMS1205S05Form02.readOnlyChilds(true);
					$LMS1205S05Form03.readOnlyChilds(true);
					$LMS1205S05Form05.readOnlyChilds(true);					
					$LMS1205S05Form06.setData(jsonInit.LMS1205S05Form06,false);
					if(jsonInit.LMS1205S05Form06.grpFlag == "Y"){
						$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(0)").attr("checked",true);
						$LMS1205S05Form06.find('#groupButton').show();
						$LMS1205S05Form06.find('#groupContext').show();
					}else{
						$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(0)").attr("checked",false);
						$LMS1205S05Form06.find('#groupButton').hide();
						$LMS1205S05Form06.find('#groupContext').hide();
					}
					$LMS1205S05Form07.setData(jsonInit.LMS1205S05Form07,false);
					if(jsonInit.LMS1205S05Form07.rltFlag == "Y"){
						$LMS1205S05Form07.find("input[name='rltFlag']:radio:eq(0)").attr("checked",true);
						$LMS1205S05Form07.find('#relButton').show();
						$LMS1205S05Form07.find('#relContext').show();
					}else{
						$LMS1205S05Form07.find("input[name='rltFlag']:radio:eq(0)").attr("checked",false);
						$LMS1205S05Form07.find('#relButton').hide();
						$LMS1205S05Form07.find('#relContext').hide();
					}
				}else if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
						(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "1")){
					var $CLS1205S05Form = $("#CLS1205S05Form");
					$CLS1205S05Form.setData(jsonInit);
					for(var o in jsonInit.CLS1205S05Form.purposes){
						$CLS1205S05Form.find("input[name='purpose']").each(function(i){
							var $this = $(this);
							if($this.val() == jsonInit.CLS1205S05Form.purposes[o]){
								$this.attr("checked",true);
								if($this.val()=="3"){
									$("#chkboxA-2-3c1_edit").show();
								}
							}
						});
					}
					for(var p in jsonInit.CLS1205S05Form.resources){
						$CLS1205S05Form.find("input[name='resource']").each(function(j){
							var $this = $(this);
							if($this.val() == jsonInit.CLS1205S05Form.resources[p]){
								$this.attr("checked",true);
								if($this.val()=="3"){
									$("#chkboxA-2-3c2_edit").show();
								}
							}
						});
					}
				}else if(responseJSON.docCode == "2" || responseJSON.docCode == "3"){
					setCkeditor2("itemDscr03",jsonInit.L120M01DForm03.itemDscr03);
				}
			}else if(responseJSON.page == "06"){
				setCkeditor2("itemDscr03",jsonInit.L120M01DForm03.itemDscr03);
			}else if(responseJSON.page == "07"){
//				$("#L120M01dForm04").find("#ffbody").val(jsonInit.L120M01dForm04.ffbody);
			}else if(responseJSON.page == "08"){
				var $formL120m01e = $("#formL120m01e");
				if(jsonInit.formL120m01e.cesCase == "Y"){
					$formL120m01e.find("#cesCase:checkbox").attr("checked",true);
				}else{
					$formL120m01e.find("#cesCase:checkbox").attr("checked",false);
				}
				$formL120m01e.find("#docDscr1").html(jsonInit.formL120m01e.docDscr1);
				if(jsonInit.formL120m01e.docDscr1 != "" 
				&& jsonInit.formL120m01e.docDscr1 != undefined 
				&& jsonInit.formL120m01e.docDscr1 != null){
					$("#docDscr1 a").attr({"href":"#"});	
				}else{
					var $docDscr1 = $("#docDscr1");
					$docDscr1.html("");
					$docDscr1.val("");
				}
				$formL120m01e.find("#docDscr2").html(jsonInit.formL120m01e.docDscr2);
				if(jsonInit.formL120m01e.docDscr2 != "" 
				&& jsonInit.formL120m01e.docDscr2 != undefined 
				&& jsonInit.formL120m01e.docDscr2 != null){
					$("#docDscr2 a").attr({"href":"#"});	
				}else{
					var $docDscr2 = $("#docDscr2");
					$docDscr2.html("");
					$docDscr2.val("");
				}
				$formL120m01e.find("#docDscr3").html(jsonInit.formL120m01e.docDscr3);
				$formL120m01e.find("#docDscr4").html(jsonInit.formL120m01e.docDscr4);
				$formL120m01e.find("#docDscr5").html(jsonInit.formL120m01e.docDscr5);
				if(jsonInit.formL120m01e.docDscr5 != "" 
				&& jsonInit.formL120m01e.docDscr5 != undefined 
				&& jsonInit.formL120m01e.docDscr5 != null){
					$("#docDscr5 a").attr({"href":"#"});	
				}else{
					var $docDscr5 = $("#docDscr5");
					$docDscr5.html("");
					$docDscr5.val("");
				}				
				if (responseJSON.readOnly.toString() == "true") {
					setReadOnly1();
				}								
			}else if(responseJSON.page == "09"){
				setCkeditor2("itemDscr05",jsonInit.L120M01DForm05.itemDscr05);
			}else if(responseJSON.page == "10"){
				var $formfile = $("#formfile");
				if (responseJSON.readOnly.toString() == "true") {
					setReadOnly1();
				}				
				if(jsonInit.formfile.sfilehide1a){
					$formfile.find("#filehide1a").hide();
				}else{
					$formfile.find("#filehide1a").show();
				}
				if(jsonInit.formfile.sfilehide1b){
					$formfile.find("#filehide1b").hide();
				}else{
					$formfile.find("#filehide1b").show();
					if(auth.Modify){
						$formfile.find("#filehide1b button").show();	
					}					
				}
				if (jsonInit.formfile.sfilehide1a && jsonInit.formfile.sfilehide1b) {
					$formfile.find("#filehide1").hide();
				}				
				if(jsonInit.formfile.sfilehide2a){
					$formfile.find("#filehide2a").hide();
				}else{
					$formfile.find("#filehide2a").show();
				}
				if(jsonInit.formfile.sfilehide2b){
					$formfile.find("#filehide2b").hide();
				}else{
					$formfile.find("#filehide2b").show();
					if(auth.Modify){
						$formfile.find("#filehide2b button").show();	
					}					
				}
				if (jsonInit.formfile.sfilehide2a && jsonInit.formfile.sfilehide2b) {
					$formfile.find("#filehide2").hide();
				}								
			}else if(responseJSON.page == "14"){
				//alert(JSON.stringify(jsonInit.L120M01aForm14));
				setCkeditor2("itemDscr09",jsonInit.L120M01aForm14.itemDscr09);
			}else if(responseJSON.page == "17"){
				if (jsonInit.L120M01aForm17.showBtn) {
					$("#L120M01aForm17").find("#showBtnA").show();
				}else{
					$("#L120M01aForm17").find("#showBtnA").hide();
				}
			}else if(responseJSON.page == "18"){
				$("#L120M01dForm06").find("#itemDscr06").val(jsonInit.L120M01dForm06.itemDscr06);
			}else if(responseJSON.page == "20"){
				//J-106-0029-001 新增洗錢防制頁籤
				var $LMS1205S20Form01 = $("#LMS1205S20Form01"); 
				$LMS1205S20Form01.setData(jsonInit.LMS1205S20Form01,false); 
			}else if(responseJSON.page == "21"){
					//J-106-0085-001 J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
					var $LMS1205S21Form01 = $("#LMS1205S21Form01"); 
					$LMS1205S21Form01.setData(jsonInit.LMS1205S21Form01,false);	
			}else if(responseJSON.page == "22"){
					//J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊  
					var $LMS1205S22Form01 = $("#LMS1205S22Form01"); 
					$LMS1205S22Form01.setData(jsonInit.LMS1205S22Form01,false);			
			}
			if(responseJSON.page != "01"){
				$("#showBorrowData").reset();
				$("#showBorrowData").setData(jsonInit.showBorrowData,false);	
			}			
			//$("form").setData(json);
			initDfd.resolve(auth);
		   },100);
		}
    });
	var btn = $("#buttonPanel");
	btn.find("#btnSave").click(function(showMsg){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		var $L120M01BForm = $("#L120M01BForm");
		var $LMS1205S04Form = $("#LMS1205S04Form");
		var $LMS1205S05Form01 = $("#LMS1205S05Form01");
		var $LMS1205S05Form06 = $("#LMS1205S05Form06");
		var $LMS1205S05Form07 = $("#LMS1205S05Form07");
		var $CLS1205S05Form = $("#CLS1205S05Form");
		var $L120M01DForm03 = $("#L120M01DForm03");
		var $L120M01dForm04 = $("#L120M01dForm04");
		var $formL120m01e = $("#formL120m01e");
		var $L120M01DForm05 = $("#L120M01DForm05");		
		//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
        var $LMS1205S21Form01 = $("#LMS1205S21Form01");
		//J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊 
		var $LMS1205S22Form01 = $("#LMS1205S22Form01");
		

		//alert($("#L120M01dForm04").find("#ffbody").val());
		FormAction.open=true;
		//alert($("#L120M01dForm06").find("#itemDscr06").val());
		if(responseJSON.page == "05"){
			if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
		   (responseJSON.docType == "1" && responseJSON.docCode == "1" 
			   && responseJSON.docKind == "1")){
			   	if(!$("#CLS1205S05Form").valid()){
					return;
				}
			}				
		}		
		$.ajax({
			type : "POST",
			handler : "lms1215formhandler",
			data : 
			{
				formAction : "saveAll",
				page : responseJSON.page,
				LMS1205S01Form : JSON.stringify($LMS1205S01Form.serializeData()),
				L120M01BForm : JSON.stringify($L120M01BForm.serializeData()),
				LMS1205S04Form : JSON.stringify($LMS1205S04Form.serializeData()),
				LMS1205S05Form01 : JSON.stringify($LMS1205S05Form01.serializeData()),
				LMS1205S05Form06 : JSON.stringify($LMS1205S05Form06.serializeData()),
				LMS1205S05Form07 : JSON.stringify($LMS1205S05Form07.serializeData()),
				CLS1205S05Form : JSON.stringify($CLS1205S05Form.serializeData()),
				L120M01DForm03 : JSON.stringify($L120M01DForm03.serializeData()),
				L120M01dForm04 : JSON.stringify($L120M01dForm04.serializeData()),
				formL120m01e : JSON.stringify($formL120m01e.serializeData()),
				L120M01DForm05 : JSON.stringify($L120M01DForm05.serializeData()),
				LMS1205S21Form01: JSON.stringify($LMS1205S21Form01.serializeData()),   //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位	
				LMS1205S22Form01: JSON.stringify($LMS1205S22Form01.serializeData()),   //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊 			
				itemDscr03 : getCkeditor("itemDscr03"),
				itemDscr05 : getCkeditor("itemDscr05"),
				itemDscr06 : $("#L120M01dForm06").find("#itemDscr06").val(),
				itemDscr01 : $("#LMS1205S05Form01").find("#itemDscr01").val(),//getCkeditor("itemDscr01"),
				itemDscr02 : $("#LMS1205S05Form04").find("#itemDscr02").val(),//getCkeditor("itemDscr02"),
				itemDscr07 : getCkeditor("itemDscr07"),
				itemDscr09 : getCkeditor("itemDscr09"),
				itemDscr0A : getCkeditor("itemDscr0A"),
				itemDscr0C : getCkeditor("itemDscr0C"),
				itemDscrO : getCkeditor("itemDscrO"), //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
				itemDscrP : getCkeditor("itemDscrP"), //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊 
				longCaseFlag : $("#LMS1205S05Form04").find("input[name='longCaseFlag']:radio:checked").val(),
				longCaseDscr : $("#LMS1205S05Form04").find("#longCaseDscr option:selected").val(),
				ffbody : getCkeditor("ffbody"),				
				mainId : responseJSON.mainId,
				docCode : responseJSON.docCode,
				docType : responseJSON.docType,
				showMsg : showMsg
			},
			success:function(responseData){
				FormAction.open=false;
				ilog.debug(responseData);
				var $showBorrowData = $("#showBorrowData");
				if (responseJSON.docKind == "1") {
					$showBorrowData.find("#title1").hide();
					$showBorrowData.find("#title1c").hide();
					$showBorrowData.find("#title1d").hide();
					$showBorrowData.find("#title1e").hide();				
					$showBorrowData.find("#title1f").hide();
					$showBorrowData.find("#title1g").hide();
					$showBorrowData.find("#title1h").hide();				
					if (responseData.authLvl == "2") {
						// 總行授權內
						if(responseJSON.docCode == "1"){
							$showBorrowData.find("#title1f").show();
						}else if(responseJSON.docCode == "2"){
							$showBorrowData.find("#title1g").show();
						}else if(responseJSON.docCode == "3"){
							$showBorrowData.find("#title1h").show();
						}	
					}else if(responseJSON.authLvl == "1"){
						// 分行授權內
						if(responseJSON.docCode == "1"){
							$showBorrowData.find("#title1c").show();
						}else if(responseJSON.docCode == "2"){
							$showBorrowData.find("#title1d").show();
						}else if(responseJSON.docCode == "3"){
							$showBorrowData.find("#title1e").show();
						}			
					}else{
						// 營運中心授權內
						if(responseJSON.docCode == "1"){
							$showBorrowData.find("#title1i").show();
							$showBorrowData.find("#title1j").hide();
							$showBorrowData.find("#title1k").hide();
						}else if(responseJSON.docCode == "2"){
							$showBorrowData.find("#title1i").show();
							$showBorrowData.find("#title1j").hide();
							$showBorrowData.find("#title1k").hide();
						}else if(responseJSON.docCode == "3"){
							$showBorrowData.find("#title1i").show();
							$showBorrowData.find("#title1j").hide();
							$showBorrowData.find("#title1k").hide();
						}			
					}									
				}				
				if(responseJSON.page == "01"){
					var $LMS1205S01Form = $("#LMS1205S01Form");
					$LMS1205S01Form.setData(responseData.LMS1205S01Form);
					$LMS1205S01Form.find("[name=areaChk]").each(function(i){
						if($(this).val() == responseData.LMS1205S01Form.areaChk){
							$(this).attr("checked",true);
						}
					});
					var areaChk = responseData.LMS1205S01Form.areaChk;
					if(areaChk=="2"){
						$LMS1205S01Form.find("#divArea1").show();
						$LMS1205S01Form.find("#divArea2").hide();
						$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
							if($(this).val() == responseData.LMS1205S01Form.areaBrId){
								$(this).attr("selected",true);
							}
						});
					}else if(areaChk=="3"){
						$LMS1205S01Form.find("#divArea1").hide();
						$LMS1205S01Form.find("#divArea2").show();
						$LMS1205S01Form.find("#areaBrId2 option").each(function(i){
							if($(this).val() == responseData.LMS1205S01Form.areaBrId){
								$(this).attr("selected",true);
							}
						});
					}else{
						$LMS1205S01Form.find("#divArea1").hide();
						$LMS1205S01Form.find("#divArea2").hide();
					}
					$LMS1205S01Form.find("#authLvl option").each(function(i){
						var $this = $(this);
						if($this.val() == responseData.LMS1205S01Form.authLvl){
							$this.attr("selected",true);
							if($this.val() == "3"){
								$LMS1205S01Form.find("#divArea3").show();
							}else{
								$LMS1205S01Form.find("#divArea3").hide();
							}
						}
					});
					$LMS1205S01Form.find("#areaBrId3 option").each(function(i){
						var $this = $(this);
						if($this.val() == responseData.LMS1205S01Form.areaBrId){
							$this.attr("selected",true);
						}
					});					
					setRequiredSave(false);

					lmsM01Json.showPage1(responseData); // J-110-0458
				}
				//更新授信簽報書Grid內容
				CommonAPI.triggerOpener("gridview","reloadGrid");
			}
		});
	}).end().find("#btnAccept").click(function(){
	}).end().find("#btnSendCase").click(function(){
		var $sendTo = $("#sendTo");
		if(responseJSON.hqMeetFlag == "1"){
			$sendTo.find("#sendTo1").hide();
			$sendTo.find("#sendTo2").show();
			$sendTo.find("#sendTo3").show();
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$sendTo.find("#sendTo4").show();
		}else if(responseJSON.hqMeetFlag == "2"){
			$sendTo.find("#sendTo1").show();
			$sendTo.find("#sendTo2").hide();
			$sendTo.find("#sendTo3").show();
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$sendTo.find("#sendTo4").show();
		}else if(responseJSON.hqMeetFlag == "3"){
			$sendTo.find("#sendTo1").show();
			$sendTo.find("#sendTo2").show();
			$sendTo.find("#sendTo3").hide();
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$sendTo.find("#sendTo4").show();
		}else if(responseJSON.hqMeetFlag == "4"){
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$sendTo.find("#sendTo1").show();
			$sendTo.find("#sendTo2").show();
			$sendTo.find("#sendTo3").show();
			$sendTo.find("#sendTo4").hide();
		}
		
		//J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
		//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
        $("#meetingDate").val(CommonAPI.getToday());
        
		$sendTo.thickbox({
			//l120m01a.btnSendTo=提會
	       title : i18n.lms1215m01['l120m01a.btnSendTo'],
	       width :400,
	       height : 200,
	       modal : true,
	       valign : "bottom",
		   align : "center",
		   i18n:i18n.def,
		   readOnly: false,
	       buttons: {
	                 "sure": function() {
	                	 if($("input[name='hqMeetFlag']:radio:checked").val() == undefined){
	                		 //l120m01a.error1=請選擇
	                		 return CommonAPI.showErrorMessage(i18n.lms1215m01['l120m01a.error1']); 
	                	 }else if ($("#meetingDate").val() == undefined || $("#meetingDate").val() == ''){
		                     	//J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
		                		 //J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
		                     	//l120m01a.error16=尚未登錄！
		                     	//l120s17a.meetingDate*提會日期
		                         return CommonAPI.showErrorMessage(i18n.lms1215m01['l120s17a.meetingDate']+i18n.lms1215m01['l120m01a.error16']);		 
	                	 }else{
	                		//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
		                	//J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	 	              		$.ajax({
		            			type : "POST",
		            			handler : "lms1215formhandler",
		            			data : 
		            			{
		            				formAction : "sendTo",
		            				mainId : responseJSON.mainId,
		            				hqMeetFlag : $("input[name='hqMeetFlag']:radio:checked").val(),
	                                meetingDate: $("#meetingDate").val()    
		            			},
		            			success:function(responseData){
		            				CommonAPI.triggerOpener("gridview","reloadGrid");
									API.showPopMessage(i18n.def["runSuccess"], window.close);
		            			}
		        			});
		                	$.thickbox.close();	                		 
	                	 }
	                 },            
	                 "cancel": function() {
	                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
	     					if(res){
	     						$.thickbox.close();
	     					}
	     		        });
	                 }
	               }
	        });		
	}).end().find("#btnLogeIN").click(function(){
		if(responseJSON.areaDocstatus == "LWC" && userInfo.unitNo == "025"){
     		// 國金部會簽
			signThickBox();
		}else if(responseJSON.areaDocstatus == "LWC"  
		&& (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922"
		|| userInfo.unitNo == "932" || userInfo.unitNo == "933" || userInfo.unitNo == "934"
		|| userInfo.unitNo == "935")){
			// 營運中心會簽
			signThickBox();			
		}else if((responseJSON.mainDocStatus == "L1H"
			    || responseJSON.mainDocStatus == "L2H"
				|| responseJSON.mainDocStatus == "L3H"
				|| responseJSON.mainDocStatus == "L4H"
				|| responseJSON.mainDocStatus == "L5H") && userInfo.unitNo == "918" ){
     		$.ajax({
    			type : "POST",
    			handler : "lms1215formhandler",
    			data : 
    			{
    				formAction : "querySignContent2",
    				mainId : responseJSON.mainId
    			},
    			success:function(responseData){
					//setCkeditor2("tItemDscr0A",responseData.L120M01aForm13.itemDscr0A);
					//setCkeditor2("tItemDscr0B",responseData.L120M01aForm13.itemDscr0B);
    				$("#tItemDscr0A").val(responseData.L120M01aForm13.tItemDscr0A);
    				$("#tItemDscr0B").val(responseData.L120M01aForm13.tItemDscr0B);
    				signThickBox2();
    			}
			});			
		}else if(responseJSON.mainDocStatus == "L1C"  
		&& (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922"
		|| userInfo.unitNo == "932" || userInfo.unitNo == "933" || userInfo.unitNo == "934"
		|| userInfo.unitNo == "935")){
     		$.ajax({
    			type : "POST",
    			handler : "lms1215formhandler",
    			data : 
    			{
    				formAction : "querySignContent3",
    				mainId : responseJSON.mainId
    			},
    			success:function(responseData){
					//setCkeditor2("tItemDscr07",responseData.L120M01aForm15.itemDscr07);
					//setCkeditor2("tItemDscr08",responseData.L120M01aForm15.itemDscr08);
    				$("#tItemDscr07").val(responseData.L120M01aForm15.tItemDscr07);
    				$("#tItemDscr08").val(responseData.L120M01aForm15.tItemDscr08);
    				signThickBox3();
    			}
			});			
		}else if(responseJSON.mainDocStatus == "L3M"  ){
			//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
			//海外提會待登錄
			signThickBox4();	
		}else if(responseJSON.mainDocStatus == "L3G" ){
			//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
			//泰國海外提會待登錄
			CommonAPI.showMessage("No item needs to be executed(無作業項目須執行) !");	
		}
		//signContent2
	}).end().find("#btnView").click(function(){
		//調閱
		rSignContent();
	}).end().find("#btnCheckArea").click(function(){
		
		//區域中心覆核
		btnCheckActionArea();
	}).end().find("#btnCheck").click(function(){
			//覆核 :提供退回或呈下一關
			btnCheckAction();		
	}).end().find("#btnCheckSea").click(function(){
			btnCheckSea();		
	}).end().find("#btnCaseToChange").click(function(){
		// 案件改分派
		$.ajax({
			handler : "lms1215formhandler",
			data : {
				formAction : "setBoss",
				changePeople : true
			},
			success : function(json){
				$("#hqAppraiser").setItems({
					item:json.hqAppraiser,
					space: false
				});
				$("#selectHqAppraiser").thickbox({     // 使用選取的內容進行彈窗
		           title : i18n.lms1215m01['l120m01a.error1a'],
		           width :200,
		           height : 100,
		           modal : true,
		           valign : "bottom",
		   		   align : "center",
		   		   i18n:i18n.def,
				   readOnly: false,
		           buttons: {
		                     "sure": function() {
		                  		$.ajax({
			            			type : "POST",
			            			handler : "lms1215formhandler",
			            			data : 
			            			{
			            				formAction : "saveHqAppraiser",
			            				hqAppraiser : $("#hqAppraiser").val(),
			            				mainid : responseJSON.mainId
			            			},
			            			success:function(responseData){
			            				$("#LMS1205S01Form").find("#headAppraiser").val(responseData.headAppraiser);
			            				$("#LMS1205S01Form").find("#areaAppraiser").val(responseData.areaAppraiser);
										// 更新授信簽報書Grid內容
										CommonAPI.triggerOpener("gridview",
												"reloadGrid");
			            				$.thickbox.close();
			            				CommonAPI.showMessage(responseData.runSuccess);
			            			}
		            			}); 				                       
		                     },            		                     
		                     "cancel": function() {
		                    	 API.confirmMessage(i18n.def['flow.exit'], function(res){
		         					if(res){
		         						$.thickbox.close();
		         					}
		         		        });
		                     }
		                   }
		            });				
			}
		});
	}).end().find("#btnSend").click(function(showMsg){
		if ((responseJSON.mainDocStatus == "010" ||
		responseJSON.mainDocStatus == "01O" ||
		responseJSON.mainDocStatus == "07O|0EO" ||
		responseJSON.mainDocStatus == "07O") &&
		auth.Modify &&
		!thickboxOptions.readOnly) {
            //saveBeforeSend=執行將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforeSend"], function(b){
                if (b) {					
					saveAll(showMsg);
				}				
			});			
		}else{
			sendBoss();				
		}	
	}).end().find("#btnSend3").click(function(){
		CommonAPI.confirmMessage(i18n.lms1215m01["l120m01a.message01"],function(b){
			if(b){
				$.ajax({
					handler : "lms1215formhandler",
					data : {
						formAction : "sendBossSea",
						mainId : responseJSON.mainId
					},
					success : function(json){
						// 更新授信簽報書Grid內容
						CommonAPI.triggerOpener("gridview",
								"reloadGrid");						
						API.showPopMessage(i18n.def["runSuccess"], window.close);
					}
				});				
			}
		});	
	}).end().find("#btnBackUnit").click(function(){
		
		//退回分行更正:退分行待補件
		enterBackReason("6");
		//flowAction({flowAction:"waitCase"});
	}).end().find("#btnBackInHead").click(function(){
		
		//授管處_退回更正
		btnHeadBackAction();
	}).end().find("#btnBackSea").click(function(){		
		//授管處_退回會簽意見
		CommonAPI.confirmMessage(i18n.lms1215m01["L1205m01a.confirm1"],function(b){
			if(b){
				$.ajax({
					type : "POST",
					handler : _handler,
					data : 
					{
						formAction : "backSea",
						mainId : responseJSON.mainId
					},
					success:function(responseData){
						// 更新授信簽報書Grid內容
						CommonAPI.triggerOpener("gridview",
								"reloadGrid");						
						API.showPopMessage(i18n.def["runSuccess"], window.close);
					}
				});			
			}else{
				$.thickbox.close(); 
			}
		});		
	}).end().find("#btnCheckHead").click(function(){
		
		//授管處覆核
		btnChecActionkHead();
	}).end().find("#btnBackCase").click(function(){	
		
		//撤件/陳復
		btnBackCaseBox();
	}).end().find("#btnPrint").click(function(showMsg){
		if ((responseJSON.mainDocStatus == "010" ||
		responseJSON.mainDocStatus == "01O" ||
		responseJSON.mainDocStatus == "07O|0EO" ||
		responseJSON.mainDocStatus == "07O") &&
		auth.Modify &&
		!thickboxOptions.readOnly) {
            //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
            CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                if (b) {
					var $LMS1205S01Form = $("#LMS1205S01Form");
					var $L120M01BForm = $("#L120M01BForm");
					var $LMS1205S04Form = $("#LMS1205S04Form");
					var $LMS1205S05Form01 = $("#LMS1205S05Form01");
					var $LMS1205S05Form06 = $("#LMS1205S05Form06");
					var $LMS1205S05Form07 = $("#LMS1205S05Form07");
					var $CLS1205S05Form = $("#CLS1205S05Form");
					var $L120M01DForm03 = $("#L120M01DForm03");
					var $L120M01dForm04 = $("#L120M01dForm04");
					var $formL120m01e = $("#formL120m01e");
					var $L120M01DForm05 = $("#L120M01DForm05");
					//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
                    var $LMS1205S21Form01 = $("#LMS1205S21Form01");
                    //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊  
                    var $LMS1205S22Form01 = $("#LMS1205S22Form01");

					//alert($("#L120M01dForm04").find("#ffbody").val());
					FormAction.open=true;
					//alert($("#L120M01dForm06").find("#itemDscr06").val());
					if(responseJSON.page == "05"){
						if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
					   (responseJSON.docType == "1" && responseJSON.docCode == "1" 
						   && responseJSON.docKind == "1")){
						   	if(!$("#CLS1205S05Form").valid()){
								return;
							}
						}				
					}		
					$.ajax({
						type : "POST",
						handler : "lms1215formhandler",
						data : 
						{
							formAction : "saveAll",
							page : responseJSON.page,
							LMS1205S01Form : JSON.stringify($LMS1205S01Form.serializeData()),
							L120M01BForm : JSON.stringify($L120M01BForm.serializeData()),
							LMS1205S04Form : JSON.stringify($LMS1205S04Form.serializeData()),
							LMS1205S05Form01 : JSON.stringify($LMS1205S05Form01.serializeData()),
							LMS1205S05Form06 : JSON.stringify($LMS1205S05Form06.serializeData()),
							LMS1205S05Form07 : JSON.stringify($LMS1205S05Form07.serializeData()),
							CLS1205S05Form : JSON.stringify($CLS1205S05Form.serializeData()),
							L120M01DForm03 : JSON.stringify($L120M01DForm03.serializeData()),
							L120M01dForm04 : JSON.stringify($L120M01dForm04.serializeData()),
							formL120m01e : JSON.stringify($formL120m01e.serializeData()),
							L120M01DForm05 : JSON.stringify($L120M01DForm05.serializeData()),	
							LMS1205S21Form01: JSON.stringify($LMS1205S21Form01.serializeData()),   //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
							LMS1205S22Form01: JSON.stringify($LMS1205S22Form01.serializeData()),   //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊  							
							itemDscr03 : getCkeditor("itemDscr03"),
							itemDscr05 : getCkeditor("itemDscr05"),
							itemDscr06 : $("#L120M01dForm06").find("#itemDscr06").val(),
							itemDscr01 : $("#LMS1205S05Form01").find("#itemDscr01").val(),//getCkeditor("itemDscr01"),
							itemDscr02 : $("#LMS1205S05Form04").find("#itemDscr02").val(),//getCkeditor("itemDscr02"),
							itemDscr07 : getCkeditor("itemDscr07"),
							itemDscr09 : getCkeditor("itemDscr09"),
							itemDscr0A : getCkeditor("itemDscr0A"),
							itemDscr0C : getCkeditor("itemDscr0C"),
							itemDscrO : getCkeditor("itemDscrO"), //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位		
							itemDscrP : getCkeditor("itemDscrP"), //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊 
							longCaseFlag : $("#LMS1205S05Form04").find("input[name='longCaseFlag']:radio:checked").val(),
							longCaseDscr : $("#LMS1205S05Form04").find("#longCaseDscr option:selected").val(),
							ffbody : getCkeditor("ffbody"),				
							mainId : responseJSON.mainId,
							docCode : responseJSON.docCode,
							docType : responseJSON.docType,
							showMsg : showMsg
						},
						success:function(responseData){
							FormAction.open=false;
							ilog.debug(responseData);
							if(responseJSON.page == "01"){
								var $LMS1205S01Form = $("#LMS1205S01Form");
								$LMS1205S01Form.setData(responseData.LMS1205S01Form);
								$LMS1205S01Form.find("[name=areaChk]").each(function(i){
									if($(this).val() == responseData.LMS1205S01Form.areaChk){
										$(this).attr("checked",true);
									}
								});
								var areaChk = responseData.LMS1205S01Form.areaChk;
								if(areaChk=="2"){
									$LMS1205S01Form.find("#divArea1").show();
									$LMS1205S01Form.find("#divArea2").hide();
									$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
										if($(this).val() == responseData.LMS1205S01Form.areaBrId){
											$(this).attr("selected",true);
										}
									});
								}else if(areaChk=="3"){
									$LMS1205S01Form.find("#divArea1").hide();
									$LMS1205S01Form.find("#divArea2").show();
									$LMS1205S01Form.find("#areaBrId2 option").each(function(i){
										if($(this).val() == responseData.LMS1205S01Form.areaBrId){
											$(this).attr("selected",true);
										}
									});
								}else{
									$LMS1205S01Form.find("#divArea1").hide();
									$LMS1205S01Form.find("#divArea2").hide();
								}
								$LMS1205S01Form.find("#authLvl option").each(function(i){
									var $this = $(this);
									if($this.val() == responseData.LMS1205S01Form.authLvl){
										$this.attr("selected",true);
										if($this.val() == "3"){
											$LMS1205S01Form.find("#divArea3").show();
										}else{
											$LMS1205S01Form.find("#divArea3").hide();
										}
									}
								});
								$LMS1205S01Form.find("#areaBrId3 option").each(function(i){
									var $this = $(this);
									if($this.val() == responseData.LMS1205S01Form.areaBrId){
										$this.attr("selected",true);
									}
								});																	
								setRequiredSave(false);

								lmsM01Json.showPage1(responseData); // J-110-0458
							}
							//更新授信簽報書Grid內容
							CommonAPI.triggerOpener("gridview","reloadGrid");
							// 如果saveAll檢核訊息有值，則不自動關閉儲存成功視窗
							if(responseData.hasSaveAllCheckMsg == false){
								$.thickbox.close();
							}
							printAction();	
						}
					});
                }
            });			
		}else{
			printAction();
		}
	}).end().find("#btnOpenLmsCase").click(function(){
		if($("#lmss7305_panel").attr("open") == "true"){
			$("#lmss7305_panel").load("../../lms/lms7305m01",function(){
				queryL730m01a();
				$("#lmss7305_panel").attr("open",false);
			});			
		}else{
			queryL730m01a();
		}	
	}).end().find("#btnSendWaitLogin").click(function(){
		//總行提會待登錄
		//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
		CommonAPI.confirmMessage(i18n.lms1215m01["l120m01a.message01"],function(b){
			if(b){
        		//必要欄位檢核
         		$.ajax({
        			type : "POST",
        			handler : "lms1205formhandler",
        			data : 
        			{
        				formAction : "checkSendWaitLogin",
        				mainId : responseJSON.mainId,
        				page : responseJSON.page,
        				docCode : responseJSON.docCode,
        				docType : responseJSON.docType
        			},
        			success:function(responseData){			
        				btnSendWaitLogin();
        			}
        		});					
			}
		});	
		
	}).end().find("#btnCheckWaitApprove").click(function(){
		//總行提會待覆核
		btnCheckWaitApprove();
	}).end().find("#backToHeadFirst").click(function(){
		//待陳復退回審核中
		backToHeadFirst();
	}).end().find("#btnExit").click(function(){
		setCloseConfirm(false);
	}).end().find("#btnApproveUnestablshExl").click(function(){	
		//J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
		approveUnestablshExl();	
	});


	var gridPrint = $("#printGrid").iGrid({
	    handler: 'lms1205gridhandler',
	    height: 270,
		rownumbers:true,
		multiselect: true,
		hideMultiselect:false,
		caption: "&nbsp;",
		hiddengrid : false,
	    postData: {
	        formAction: "",
			mainId: responseJSON.mainId
	    },
	    colModel: [{
	        colHeader: i18n.lms1215m01['print.custName'],// "借款人名稱",
	        name: 'custName',
	        width: 120,
	        sortable: false
	    }, {
	        colHeader: i18n.lms1215m01['print.rptNo'],// "報表編號",
	        name: 'rptNo',
	        align: "center",
	        width: 40,
	        sortable: false
	    }, {
	        colHeader: i18n.lms1215m01['print.rptName'],// "報表名稱",
	        name: 'rptName',
	        width: 70,
	        sortable: false
	    }, {
	        colHeader: i18n.lms1215m01['print.cntrNo'],// "額度序號",
	        name: 'cntrNo',
	        align: "center",
	        width: 50,
	        sortable: false
	    }, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}, {
			colHeader : "rpt",
			name : 'rpt',
			hidden : true
		}, {
			colHeader : "custId",
			name : 'custId',
			hidden : true
		}, {
			colHeader : "dupNo",
			name : 'dupNo',
			hidden : true
		}, {
			colHeader : "refMainId",
			name : 'refMainId',
			hidden : true
		}]
	});	
	
	var gridPrint2 = $("#printGrid2").iGrid({
	    handler: 'lms1205gridhandler',
	    height: 270,
		rownumbers:true,
		multiselect: false,
		hideMultiselect:false,
		caption: "&nbsp;",
		hiddengrid : false,
		sortname : 'rptNo',
	    postData: {
	        formAction: "",
			mainId: responseJSON.mainId
	    },
	    colModel: [{
	        colHeader: i18n.lms1215m01['print.custName'],// "借款人名稱",
	        name: 'custName',
	        width: 120,
	        sortable: false
	    }, {
	        colHeader: i18n.lms1215m01['print.rptNo'],// "報表編號",
	        name: 'rptNo',
	        align: "center",
	        width: 40,
	        sortable: false
	    }, {
	        colHeader: i18n.lms1215m01['print.rptName'],// "報表名稱",
	        name: 'rptName',
	        width: 100,
	        sortable: false
	    }, {
			colHeader : "rptHandle",					//組合方式
			name : 'rptHandle',
			hidden : true
		}, {
			colHeader : "cntrNo",
			name : 'cntrNo',
			hidden : true
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}, {
			colHeader : "rpt",
			name : 'rpt',
			hidden : true
		}, {
			colHeader : "custId",
			name : 'custId',
			hidden : true
		}, {
			colHeader : "dupNo",
			name : 'dupNo',
			hidden : true
		}, {
			colHeader : "refMainId",
			name : 'refMainId',
			hidden : true
		}, {
			colHeader : "rptHandle",
			name : 'rptHandle',
			hidden : true
		}, {
			colHeader : "meetingType",
			name : 'meetingType',
			hidden : true
		}, {
			colHeader : "branchType",
			name : 'branchType',
			hidden : true
		}]
	});	

	var tScore = 0;
	//計算授信報案考核表扣分與總扣分
	$("#formopenLmsCase").find(".mathit").blur(function(i){
		var mul1 = $(this).parent().parent().find("td").next().html();
		var mul2 = $(this).val();
		$(this).parent().next().html(mul1*mul2);
		
		//總扣分初始化
		tScore = 0;
		$("#formopenLmsCase").find(".totalit").each(function(j){
			if($(this).html().length == 0){
				tScore += 0;
			}else{
				tScore += parseInt($(this).html(),10);
			}			
		});
		//將計算後的總扣分結果設定到畫面上
		$("#formopenLmsCase").find("#tmGrade").html(tScore);
	});	
});

function sendBoss(){
	$.ajax({
		handler : "lms1215formhandler",
		data : {
			formAction : "setBoss"
		},
		success : function(json){
			$(".boss").setItems({
				item:json.bossList,
				space: true
			});
			$(".boss_Y01").setItems({
				item:json.bossList_Y01,
				space: true
			});
			if(!json.noBossList2){
				$(".boss2").setItems({
					item:json.bossList2,
					space: true
				});						
			}			
			$("#sUnitManager option:eq(1)").attr("selected",true);
			$("#sUnitManager").attr("disabled",true);			
			//l120m01a.message01=是否呈主管覆核？
			CommonAPI.confirmMessage(i18n.lms1215m01["l120m01a.message01"],function(b){
				if(b){
            		//必要欄位檢核
             		$.ajax({
            			type : "POST",
            			handler : "lms1215formhandler",
            			data : 
            			{
            				formAction : "checkSend",
            				mainId : responseJSON.mainId,
            				page : responseJSON.page,
            				docCode : responseJSON.docCode,
            				docType : responseJSON.docType
            			},
            			success:function(responseData){
            				dfd_getRejtMsgData().done(function(){
            					if(responseData.canSend){
                					// 授管處和營運中心的呈主管不需額外挑主管與相關負責人員。
                					if(responseData.unitType != "S" && responseData.unitType != "A"){
    	            								queryPrintSeq(selectBossBox);
                					}else{
                						// 直接跑flow，簽章欄不用建，之後於"登錄說明及意見"時再建立
                						flowAction({flowAction:"waitCheck"});	            						
                					}		
                				}else{
                					//檢核不通過
                					return CommonAPI.showErrorMessage(i18n.lms1215m01('l120m01a.error3',{'colName':responseData.tables}));
                				}
            				});
            			}
            		});					
				}
			});				
		}
	});		
}

function dfd_getRejtMsgData(){
	var my_dfd = $.Deferred();    	
	var rejtResult = getRejtMsgData();
    if (rejtResult != null) {
    	CommonAPI.confirmMessage(rejtResult, function(b){
	   	    if (b) {
	   	    	my_dfd.resolve();
			}else{
				my_dfd.reject();
			}	
	    })
    }else{
    	my_dfd.resolve();
    }    		
	return my_dfd.promise();
}

function selectBossBox(){
	LMSM05Panel_js_obj.replace_sign_descLabel();
	
	//檢核通過
		$("#selectBossBox").thickbox({     // 使用選取的內容進行彈窗
			//l120m01a.bt03=覆核
           title : i18n.lms1215m01['l120m01a.bt03'],
           width :500,
           height : 300,
           modal : true,
           valign : "bottom",
   		   align : "center",
   		   i18n:i18n.def,
		   readOnly: false,
           buttons: {
                     "sure": function() {
                    		
                    	 var  selectBoss=  $("select[name^=boss]").map(function(){
                             				return $(this).val();
                         					}).toArray();

						// 驗證主管是否都有選擇到
                        for (var i in selectBoss) {
                            if (selectBoss[i] == "") {
                                //l120m01a.error1=請選擇+ l120m01a.bossId=授信主管
                                return CommonAPI.showErrorMessage(i18n.lms1215m01['l120m01a.error1'] + $("#L120M01F_L3").val() );
                            }
                        }
                        // 驗證 副總經理 是否有選擇到
						if($('#sManager_Y01_LA').is(':visible') && $("#sManager_Y01_LA option:selected").val() == ""){
							return CommonAPI.showErrorMessage(i18n.lms1215m01['l120m01a.error1'] + $("#L120M01F_TH_UnitTypeR_LA").val());
						}
						
						// 驗證單位授權主管是否有選擇到
						if($("#sManager option:selected").val() == ""){
							//l120m01a.error1=請選擇+ l120m01a.managerId=單位/授權主管
							return CommonAPI.showErrorMessage(i18n.lms1215m01['l120m01a.error1'] + $("#L120M01F_L5").val() );
						}

						// 驗證 總經理 是否有選擇到
						if($('#sManager_Y01_L5').is(':visible') && $("#sManager_Y01_L5 option:selected").val() == ""){
							return CommonAPI.showErrorMessage(i18n.lms1215m01['l120m01a.error1'] + $("#L120M01F_TH_UnitTypeP_L5_Y01").val());
						}
					
                         //驗證是否有重複的主管
                        if(checkArrayRepeat(selectBoss)){
                        	//主管人員名單重複請重新選擇
                        	return CommonAPI.showErrorMessage(i18n.lms1215m01['l120m01a.message02']);
                        }
                        //建立簽章欄
                 		$.ajax({
                			type : "POST",
                			handler : "lms1215formhandler",
                			data : 
                			{
                				formAction : "saveL120m01f",
                				mainId : responseJSON.mainId,
                				selectBoss : selectBoss,
                				manager:$("#sManager option:selected").val(),
                				sManager_Y01_LA:$("#sManager_Y01_LA option:selected").val(),
                				sManager_Y01_L5:$("#sManager_Y01_L5 option:selected").val(),
                				AOPerson:$("#AOPerson option:selected").val(),
								sUnitManager : $("#sUnitManager option:selected").val()
                			},
                			success:function(responseData){
                				//alert(JSON.stringify(responseData));
                				flowAction({flowAction:"waitCheck"});
                			}
            			});   				                        
                        $.thickbox.close();  				                       
                     },            
                     
                     "cancel": function() {
                    	 API.confirmMessage(i18n.def['flow.exit'], function(res){
         					if(res){
         						$.thickbox.close();
         					}
         		        });
                     }
                   }
            });	            			
}
function saveAll(showMsg){
	var $LMS1205S01Form = $("#LMS1205S01Form");
	var $L120M01BForm = $("#L120M01BForm");
	var $LMS1205S04Form = $("#LMS1205S04Form");
	var $LMS1205S05Form01 = $("#LMS1205S05Form01");
	var $LMS1205S05Form06 = $("#LMS1205S05Form06");
	var $LMS1205S05Form07 = $("#LMS1205S05Form07");
	var $CLS1205S05Form = $("#CLS1205S05Form");
	var $L120M01DForm03 = $("#L120M01DForm03");
	var $L120M01dForm04 = $("#L120M01dForm04");
	var $formL120m01e = $("#formL120m01e");
	var $L120M01DForm05 = $("#L120M01DForm05");	
	//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
    var $LMS1205S21Form01 = $("#LMS1205S21Form01");
    //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊  
    var $LMS1205S22Form01 = $("#LMS1205S22Form01");

	//alert($("#L120M01dForm04").find("#ffbody").val());
	FormAction.open=true;
	//alert($("#L120M01dForm06").find("#itemDscr06").val());
	if(responseJSON.page == "05"){
		if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
	   (responseJSON.docType == "1" && responseJSON.docCode == "1" 
		   && responseJSON.docKind == "1")){
		   	if(!$("#CLS1205S05Form").valid()){
				return;
			}
		}				
	}		
	$.ajax({
		type : "POST",
		handler : "lms1215formhandler",
		data : 
		{
			formAction : "saveAll",
			page : responseJSON.page,
			LMS1205S01Form : JSON.stringify($LMS1205S01Form.serializeData()),
			L120M01BForm : JSON.stringify($L120M01BForm.serializeData()),
			LMS1205S04Form : JSON.stringify($LMS1205S04Form.serializeData()),
			LMS1205S05Form01 : JSON.stringify($LMS1205S05Form01.serializeData()),
			LMS1205S05Form06 : JSON.stringify($LMS1205S05Form06.serializeData()),
			LMS1205S05Form07 : JSON.stringify($LMS1205S05Form07.serializeData()),
			CLS1205S05Form : JSON.stringify($CLS1205S05Form.serializeData()),
			L120M01DForm03 : JSON.stringify($L120M01DForm03.serializeData()),
			L120M01dForm04 : JSON.stringify($L120M01dForm04.serializeData()),
			formL120m01e : JSON.stringify($formL120m01e.serializeData()),
			L120M01DForm05 : JSON.stringify($L120M01DForm05.serializeData()),	
			LMS1205S21Form01: JSON.stringify($LMS1205S21Form01.serializeData()),   //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位	
			LMS1205S22Form01: JSON.stringify($LMS1205S22Form01.serializeData()),   //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊 	
			itemDscr03 : getCkeditor("itemDscr03"),
			itemDscr05 : getCkeditor("itemDscr05"),
			itemDscr06 : $("#L120M01dForm06").find("#itemDscr06").val(),
			itemDscr01 : $("#LMS1205S05Form01").find("#itemDscr01").val(),//getCkeditor("itemDscr01"),
			itemDscr02 : $("#LMS1205S05Form04").find("#itemDscr02").val(),//getCkeditor("itemDscr02"),
			itemDscr07 : getCkeditor("itemDscr07"),
			itemDscr09 : getCkeditor("itemDscr09"),
			itemDscr0A : getCkeditor("itemDscr0A"),
			itemDscr0C : getCkeditor("itemDscr0C"),
			itemDscrO : getCkeditor("itemDscrO"), //J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位	
			itemDscrP : getCkeditor("itemDscrP"), //J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊  
			longCaseFlag : $("#LMS1205S05Form04").find("input[name='longCaseFlag']:radio:checked").val(),
			longCaseDscr : $("#LMS1205S05Form04").find("#longCaseDscr option:selected").val(),
			ffbody : getCkeditor("ffbody"),				
			mainId : responseJSON.mainId,
			docCode : responseJSON.docCode,
			docType : responseJSON.docType,
			showMsg : showMsg,
			saveAllAtCheckSend : 'Y'
		},
		success:function(responseData){
			FormAction.open=false;
			ilog.debug(responseData);
			if(responseJSON.page == "01"){
				var $LMS1205S01Form = $("#LMS1205S01Form");
				$LMS1205S01Form.setData(responseData.LMS1205S01Form);
				$LMS1205S01Form.find("[name=areaChk]").each(function(i){
					if($(this).val() == responseData.LMS1205S01Form.areaChk){
						$(this).attr("checked",true);
					}
				});
				var areaChk = responseData.LMS1205S01Form.areaChk;
				if(areaChk=="2"){
					$LMS1205S01Form.find("#divArea1").show();
					$LMS1205S01Form.find("#divArea2").hide();
					$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
						if($(this).val() == responseData.LMS1205S01Form.areaBrId){
							$(this).attr("selected",true);
						}
					});
				}else if(areaChk=="3"){
					$LMS1205S01Form.find("#divArea1").hide();
					$LMS1205S01Form.find("#divArea2").show();
					$LMS1205S01Form.find("#areaBrId2 option").each(function(i){
						if($(this).val() == responseData.LMS1205S01Form.areaBrId){
							$(this).attr("selected",true);
						}
					});
				}else{
					$LMS1205S01Form.find("#divArea1").hide();
					$LMS1205S01Form.find("#divArea2").hide();
				}
				$LMS1205S01Form.find("#authLvl option").each(function(i){
					var $this = $(this);
					if($this.val() == responseData.LMS1205S01Form.authLvl){
						$this.attr("selected",true);
						if($this.val() == "3"){
							$LMS1205S01Form.find("#divArea3").show();
						}else{
							$LMS1205S01Form.find("#divArea3").hide();
						}
					}
				});
				$LMS1205S01Form.find("#areaBrId3 option").each(function(i){
					var $this = $(this);
					if($this.val() == responseData.LMS1205S01Form.areaBrId){
						$this.attr("selected",true);
					}
				});				
				setRequiredSave(false);

				lmsM01Json.showPage1(responseData); // J-110-0458
			}
			//更新授信簽報書Grid內容
			CommonAPI.triggerOpener("gridview","reloadGrid");
			$.thickbox.close();
			sendBoss();
		}
	});		
}

//登錄國金部-Thickbox
function signThickBox(){
	$.ajax({
		type : "POST",
		handler : "lms1215formhandler",
		data : 
		{
			formAction : "querySignContent",
			mainId : responseJSON.mainId
		},
		success:function(responseData){
			//alert(JSON.stringify(responseData));
/*
			var oEditor = CKEDITOR.instances["tItemDscr09"];
    		if (oEditor){
				oEditor.setData(responseData.formSea.itemDscr09);
    		}
*/
			$("#tItemDscr09").val(responseData.formSea.itemDscr09);
			$("#_tItemDscr09").val($("#tItemDscr09").val());
			$("#signThick").thickbox({     // 使用選取的內容進行彈窗
		       title : i18n.lms1215m01['l120m01a.login'],
		       width :450,
		       height : 250,
		       modal : true,
		       valign : "bottom",
			   align : "center",
			   i18n:i18n.def,
			   readOnly: false,
		       buttons: {
		                 "sure": function() {
						 	var value =  $("input[name='sign']:checked").val();
							if(value == 999){
								// 登錄簽章欄人員
								signContent();
							}else{
				             		$.ajax({
					            			type : "POST",
					            			handler : "lms1215formhandler",
					            			data : 
					            			{
					            				formAction : "saveSignContent",
					            				itemDscr09 : $("#tItemDscr09").val(),
												sSeaManager : $("#sSeaManager").val(),
												sSeaBoss : $("#sSeaBoss").val(),
												sSeaAoName : $("#sSeaAoName").val(),
												sSeaAppraiserCN : $("#sSeaAppraiserCN").val(),										
					            				mainid : responseJSON.mainId
					            			},
					            			success:function(responseData){
					            				//alert(JSON.stringify(responseData));
												//$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);								
					            				//$("#L120M01aForm14").setData(responseData.L120M01aForm14,false);
												setCkeditor2("itemDscr09", responseData.L120M01aForm14.itemDscr09);
					            			}
				            			});						
								}                
							    $.thickbox.close();
			/*				     
								signContent();				 	
			     				$.ajax({
			    					type : "POST",
			    					handler : "lms1215formhandler",
			    					data : 
			    					{
			    						formAction : "querySignContent",
			    						mainId : responseJSON.mainId
			    					},
			    					success:function(responseData){
			    						//alert(JSON.stringify(responseData));
										var oEditor = CKEDITOR.instances["tItemDscr09"];
									    if (oEditor){
											oEditor.setData(responseData.formSea.itemDscr09);
									    }
			    					}
								});
			*/					                 
			                 },            
			                 "cancel": function() {
			                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
			     					if(res){
			     						$.thickbox.close();
			     					}
			     		        });
			                 }
			               }
			        });			
		}
	});		
}

//登錄授管處-Thickbox
function signThickBox2(){
	$("#_tItemDscr0A").val($("#tItemDscr0A").val());
	$("#_tItemDscr0B").val($("#tItemDscr0B").val());
	$("#signThick2").find("input[name='login']").readOnly(false);
	if(	responseJSON.hqMeetFlag == "1"
		|| responseJSON.hqMeetFlag == "2"){
			$("#signThick2").find(".hide").show();
		}else{//3董事會、4審計委員會
			//J-113-0337  配合本行將於第18屆董事會設置審計委員會替代監查人，新增「審計委員會」
			$("#signThick2").find(".hide").hide();
		}
	$("#signThick2").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1215m01['l120m01a.login'],
       width :450,
       height : 250,
       modal : true,
       valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {
                 "sure": function() {
	                 var value =  $("input[name='login']:checked").val();
	                 $.thickbox.close();
	                 if(value == 1){
	                	 //案件審核層級
	           	      var obj= CommonAPI.loadCombos(["lms1205m01_caseLvl"]);
	        	      $("#formCaseLvl").find("#caseLvl").setItems({
	        	     		item:obj.lms1205m01_caseLvl,
	        	     		format : "{value} - {key}",
	        	     		space: false
	        	     	});
	                	 signCaseLvl();
	                 }else if (value == 991) {
	                	 //J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
	                 	//J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	                     //案件收件日期
	                 	signCaseReceivedDate();	 	 
	                 }else if(value == 2){
	                	 //補充意見及審查意見
	                	 //signContent2();
	             		$.ajax({
		            			type : "POST",
		            			handler : "lms1215formhandler",
		            			data : 
		            			{
		            				formAction : "saveSignContent2",
		            				itemDscr0A : $("#tItemDscr0A").val(),
		            				itemDscr0B : $("#tItemDscr0B").val(),
									sHeadLeader : $("#sHeadLeader").val(),
									sHeadSubLeader : $("#sHeadSubLeader").val(),
									sHeadReCheck : $("#sHeadReCheck").val(),
									sHeadAppraiser : $("#sHeadAppraiser").val(),										
		            				mainid : responseJSON.mainId
		            			},
		            			success:function(responseData){
		            				//alert(JSON.stringify(responseData));
									//$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
									setCkeditor2("itemDscr0A", responseData.L120M01aForm13.itemDscr0A);
									setCkeditor2("itemDscr0B", responseData.L120M01aForm13.itemDscr0B);
		            				//$("#L120M01aForm13").find("#itemDscr0A").val(responseData.L120M01aForm13.itemDscr0A);
		            				//$("#L120M01aForm13").find("#itemDscr0B").val(responseData.L120M01aForm13.itemDscr0B);
		            			}
	            			});
	                 }else if(value == 3){
					 	// 會議決議
						$.ajax({
				    			type : "POST",
				    			handler : "lms1215formhandler",
				    			data : 
				    			{
				    				formAction : "querySignContent0",
				    				mainId : responseJSON.mainId,
									txCode : responseJSON.txCode
				    			},
				    			success:function(responseData){
				    				//alert(JSON.stringify(responseData));
									$("#formL120m01h").reset();
									$("#formL120m01h").setData(responseData.formL120m01h,false);
				    				signContent0();
				    			}
							});					 	
					 }else{
					 	// 登錄簽章欄人員
					 	signContent2();
					 }
                 },            
                 "cancel": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}

function login1(oid, isArea) {
	//$("#LMS1200V62Form1").reset();
	$("#LMS1200V62Form1").find("#rptTitle1e").html($("body").find(".system_info span").html());
	$("#LMS1200V62Thickbox1").thickbox({ // 使用選取的內容進行彈窗
			title : i18n.lms1215m01["l120v01.thickbox7"],
			width : 500,
			height : 200,
			modal : true,
			valign : "bottom",
			align : "center",
			i18n : i18n.def,
			readOnly: false,
			buttons : {
				"sure" : function() {
					var $LMS1200V62Form1 = $("#LMS1200V62Form1");
					if ($LMS1200V62Form1.valid()) {
						if ($LMS1200V62Form1.find("#rptTitle1b").val() < 1
								|| $LMS1200V62Form1.find("#rptTitle1b").val() > 12) {
							CommonAPI.showMessage(i18n.lms1215m01["l120v01.error3"]);
							return;
						} else if ($LMS1200V62Form1.find("#rptTitle1c").val() < 1
								|| $LMS1200V62Form1.find("#rptTitle1c").val() > 31) {
							CommonAPI.showMessage(i18n.lms1215m01["l120v01.error4"]);
							return;
						} else if ($LMS1200V62Form1.find(
								"#rptTitle1d").val() <= 0) {
							CommonAPI.showMessage(i18n.lms1215m01["l120v01.error6"]);
							return;
						} else if ($LMS1200V62Form1.find("#rptTitle1a").val() <= 0) {
							CommonAPI.showMessage(i18n.lms1215m01["l120v01.error8"]);
							return;
						} else {
							$.ajax({
								type : "POST",
								handler : "lms1215formhandler",
								data : {
									formAction : "login1",
									LMS1200V62Form1 : JSON.stringify($LMS1200V62Form1.serializeData()),
									oid : oid,
									isArea : isArea,
									idDelete : false,
									caseName : $LMS1200V62Form1.find("b").text()
								},
								success : function(responseData) {
								}
							});
						}
						$.thickbox.close();
					}
				},
				"cancel" : function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				},
				"del" : function() {
					$.thickbox.close();
				    var $LMS1200V62Form1 = $("#LMS1200V62Form1");
					  API.confirmMessage(i18n.def['confirmDelete'], function(res){
						if(res){
							$.ajax({
								type : "POST",
								handler : "lms1215formhandler",
								data : {
									formAction : "login1",
									LMS1200V62Form1 : JSON.stringify($LMS1200V62Form1.serializeData()),
									oid : oid,
									isArea : isArea,
									idDelete : true,
									caseName : $LMS1200V62Form1.find("b").text()
								},
								success : function(responseData) {
								}
							});
						}
						
						$.thickbox.close();
			        });
				}
			}
		});		
}

//登錄營運中心-Thickbox
function signThickBox3(){
	$("#_tItemDscr07").val($("#tItemDscr07").val());
	$("#_tItemDscr08").val($("#tItemDscr08").val());
	$("#signThick3").find("input[name='login2']").readOnly(false);
	$("#signThick3").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1215m01['l120m01a.login'],
       width :450,
       height : 250,
       modal : true,
       valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {
                 "sure": function() {
	                 var value =  $("input[name='login2']:checked").val();
	                 $.thickbox.close();
	                 if(value == 1){
	                	 //營運中心授審會會期
						$.ajax({
							type : "POST",
							handler : "lms1215formhandler",
							data : {
								formAction : "queryLogin1",
								isArea : true,
								mainId : responseJSON.mainId
							},
							success : function(responseData) {
								$("#LMS1200V62Form1").setData(responseData);
								login1(responseData.oid, true);
							}
						});						 
	                 }else if(value == 2){
	                	 //營運中心說明及意見
	                	 //signContent3();
	             		$.ajax({
		            			type : "POST",
		            			handler : "lms1215formhandler",
		            			data : 
		            			{
		            				formAction : "saveSignContent3",
		            				itemDscr07 : $("#tItemDscr07").val(),
		            				itemDscr08 : $("#tItemDscr08").val(),
									sAreaLeader	: $("#sAreaLeader").val(),
									sAreaSubLeader : $("#sAreaSubLeader").val(),
									sAreaManager : $("#sAreaManager").val(),
									sAreaAppraiser : $("#sAreaAppraiser").val(),
									itemTitle : $("#itemTitle").val(),
		            				mainid : responseJSON.mainId
		            			},
		            			success:function(responseData){
		            				//alert(JSON.stringify(responseData));
									//$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
									setCkeditor2("itemDscr07", responseData.L120M01aForm15.itemDscr07);
									setCkeditor2("itemDscr08", responseData.L120M01aForm15.itemDscr08);
		            				//$("#L120M01aForm15").find("#itemDscr07").val(responseData.L120M01aForm15.itemDscr07);
		            				//$("#L120M01aForm15").find("#itemDscr08").val(responseData.L120M01aForm15.itemDscr08);
		            			}
	            			});
	                 }else if(value == 3){
					 	// 會議決議
						$.ajax({
				    			type : "POST",
				    			handler : "lms1215formhandler",
				    			data : 
				    			{
				    				formAction : "querySignContent0",
									isArea : true,
				    				mainId : responseJSON.mainId,
									txCode : responseJSON.txCode
				    			},
				    			success:function(responseData){
				    				//alert(JSON.stringify(responseData));
									$("#formL120m01h").reset();
									$("#formL120m01h").setData(responseData.formL120m01h,false);
				    				signContent0();
				    			}
							});	
	                 }else if (value == 990) {
	                     	//#J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
		                	 //J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
	                         //案件收件日期
	                     	signCaseReceivedDate();    		
					 }else{
					 	signContent3();
					 }
                 },            
                 "cancel": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}

//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
//J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
//提會待登錄-登錄-Thickbox
function signThickBox4(){
	$("#signThick4").find("input[name='login4']").readOnly(false);
	$("#signThick4").thickbox({     // 使用選取的內容進行彈窗
     title : i18n.lms1215m01['l120m01a.login'],
     width :450,
     height : 100,
     modal : true,
     valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
     buttons: {
               "sure": function() {
	                 var value =  $("input[name='login4']:checked").val();
	                 $.thickbox.close();
	                 if (value == 992) {
                   	//#J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
	                	//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
                      //案件收件日期
                   	   signCaseReceivedDate();    	
					 }
               },            
               "cancel": function() {
              	 API.confirmMessage(i18n.def['flow.exit'], function(res){
   					if(res){
   						$.thickbox.close();
   					}
   		        });
               }
             }
      });	
}	


//查詢會簽意見內容(營運中心)-Thickbox
function rSignContent(){
	$("#signContent").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1215m01['l120m01a.looksay2'],
       width :640,
       height : 350,
       modal : true,
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {            
                 "close": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });
	$.ajax({
		type : "POST",
		handler : "lms1215formhandler",
		data : 
		{
			formAction : "querySignContent",
			mainId : responseJSON.mainId
		},
		success:function(responseData){
			//alert(JSON.stringify(responseData));
/*
			var oEditor = CKEDITOR.instances["tItemDscr09"];
    		if (oEditor){
				oEditor.setData(responseData.formSea.itemDscr09);
    		}
*/
			$("#tItemDscr09").val(responseData.formSea.itemDscr09);
		}
	});			
}

function lms7205Grid(){
		var grid = $("#lms7205Grid").iGrid({
			handler : 'lms1205gridhandler',
			height : 350,
			sortname : 'patternNM',
			postData : {
				docStatus : responseJSON.mainDocStatus,
				formAction: "queryL720m01a",
				rowNum:15
			},
			rowNum:15,
			//multiselect : true,
			colModel : [  {
				colHeader : i18n.lms1215m01['L720M01A.patternNM'],//"範本名稱",
				name : 'patternNM',
				align : "left",
				width : 200,
				sortable : true
			}, {
				colHeader : i18n.lms1215m01['L720M01A.updater'],//"最後異動人員",
				name : 'updater',
				width : 200,
				sortable : true
			}, {
				colHeader : "oid",
				name : 'oid',
				hidden : true
			} ],
	        ondblClickRow: function(rowid){		//當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	        }
		});	
}

function thickLms7205Grid(){
    $("#lms7205Grid").jqGrid("setGridParam", {
        postData: {
			docStatus : responseJSON.mainDocStatus,
            formAction: "patternNM",
            rowNum:15
        },
        search: true
    }).trigger("reloadGrid");
}

function thickLms7205Grid(){
	//thickLms7205Grid();
	$("#lms7205Grid").resetSelection();
	var openThickbox = $("#thickLms7205Grid").thickbox({	// 使用選取的內容進行彈窗
		title : i18n.lms1215m01['l120m01a.bt05'],
		width : 640,
		height : 470,
		modal: false,
		i18n : i18n.def,
		buttons : {
				"sure" : function(showMsg){
                var row1 = $("#lms7205Grid").getGridParam('selrow');
                var list1 = "";
                var data1 = $("#lms7205Grid").getRowData(row1);
                list1 = data1.oid;
				list1 = (list1 == undefined ? "" : list1);
				if (list1 != "") {
             		$.ajax({
	            			type : "POST",
	            			handler : "lms1215formhandler",
	            			data : 
	            			{
	            				formAction : "queryL720m01a",
								mainId : responseJSON.mainId,
	            				oid : list1
	            			},
	            			success:function(responseData){
	            				//alert(JSON.stringify(responseData));
								$.thickbox.close();
								$.thickbox.close();
								$("#meetingNote").val(responseData.meetingNote);
	            				//setCkeditor2("meetingNote",responseData.meetingNote);
								CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
	            			}
            			});					
				}else{
                    CommonAPI.showMessage(i18n.lms1215m01["l120m01a.error1"]);
				}
			},
				"close" : function(){
            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
 					if(res){
 						$.thickbox.close();
 					}
 		        });
			}
		}
	});	
}

//會議決議(產報表)
function printR20(){
	$.form.submit({
        url: "../../simple/FileProcessingService",
        target: "_blank",
        data: {
        	mainId : responseJSON.mainId,
        	rptOid : "R20" + "^" + "",
			otherData : (responseJSON.hqMeetFlag == "1") ? "1" : (responseJSON.hqMeetFlag == "2") ? "2" : "", 
			fileDownloadName : "l120r01.pdf",
			serviceName : "lms1205r01rptservice"
        }
    });	
}

function signContent0(){
	$("#meetingNote").readOnly(false);
	$("#signContent0").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lms1215m01['L120M01H.meetingNote'],
       width :900,
       height : 480,
       modal : true,
       valign : "bottom",
	   align : "center",
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {
                 "sure": function() {
				 	var $formL120m01h = $("#formL120m01h");
             		$.ajax({
	            			type : "POST",
	            			handler : "lms1215formhandler",
	            			data : $.extend({
	            				formAction : "saveSignContent0",
								formL120m01h : JSON.stringify($formL120m01h.serializeData()),
	            				mainid : responseJSON.mainId,
								txCode : responseJSON.txCode
	            			},(userInfo.unitNo == "920"
							 || userInfo.unitNo == "922"
							 || userInfo.unitNo == "931"
							 || userInfo.unitNo == "932"
							 || userInfo.unitNo == "933"
							 || userInfo.unitNo == "934"
							 || userInfo.unitNo == "935")?{isArea : true}:{}),
	            			success:function(responseData){
	            				//alert(JSON.stringify(responseData));
	            				//$("#L120M01aForm14").find("#itemDscr09").val(responseData.L120M01aForm14.itemDscr09);
	            			}
            			});
                  $.thickbox.close(); 
                 },            
                 "cancel": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}

//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
//J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
//登錄案件收件日期
function signCaseReceivedDate(){
	
	$("#formCaseReceivedDate").find("#caseReceivedDate").val(CommonAPI.getToday());
$("#signCaseReceivedDate").thickbox({ // 使用選取的內容進行彈窗
    title: i18n.lms1215m01['l120s17a.caseReceivedDate'],   //案件收件日期
    width: 400,
    height: 150,
    modal: true,
    valign: "bottom",
    align: "center",
    i18n: i18n.def,
    readOnly: false,
    buttons: {
        "sure": function(){
            $.ajax({
                type: "POST",
                handler: "lms1205formhandler",
                data: {
                    formAction: "setCaseReceivedDate",
                    caseReceivedDate: $("#formCaseReceivedDate").find("#caseReceivedDate").val(),
                    mainid: responseJSON.mainId
                },
                success: function(responseData){
                    
                    $.thickbox.close();
                    $.thickbox.close();
                    CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                }
            });
        },
        "cancel": function(){
            API.confirmMessage(i18n.def['flow.exit'], function(res){
                if (res) {
                    $.thickbox.close();
                }
            });
        }
    }
});
}

//登錄案件審核層級
function signCaseLvl(){
	$("#signCaseLvl").thickbox({     // 使用選取的內容進行彈窗
	       title : i18n.lms1215m01['l120m01a.caselevel'],
	       width :320,
	       height : 200,
	       modal : true,
	       valign : "bottom",
		   align : "center",
		   i18n:i18n.def,
		   readOnly: false,
	       buttons: {
	                 "sure": function() {  
	             		$.ajax({
		            			type : "POST",
		            			handler : "lms1215formhandler",
		            			data : 
		            			{
		            				formAction : "setCaseLvl",
		            				caseLvl : $("#formCaseLvl").find("#caseLvl option:selected").val(),
		            				mainid : responseJSON.mainId
		            			},
		            			success:function(responseData){
		            				//alert(JSON.stringify(responseData));
		    	                	$("#LMS1205S01Form").find("#caseLvl").val(responseData.caseLvl);
									$.thickbox.close();
									$.thickbox.close();
									CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
		            			}
	            			}); 
	                 },            
	                 "cancel": function() {
	                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
	     					if(res){
	     						$.thickbox.close();
	     					}
	     		        });
	                 }
	               }
	        });	
}

//合併多個serializeData JSON 成一個serializeData JSON
function mergeJSON(json, array){
	for ( var data in array) {
		json[data] = array[data];
	}	
}

//檢查陣列內容是否重複
function checkArrayRepeat(arrVal) {
	var newArray = [];
	for (var i = arrVal.length; i--; ) {
		var val = arrVal[i];
		if ($.inArray(val, newArray) == -1) {
			newArray.push(val);
		}else{
			return true;
		}
	}
	return false;
}

//畫面切換table 所需設定之資料 如無設定 則直接切換
$.extend(window.tempSave, {
    handler: "lms1215formhandler",
    action: "tempSave",
	beforeCheck: function(){
		if(responseJSON.page == "02"){
			var count=$("#l120s01agrid").jqGrid('getGridParam','records');
			if(count == 0){
				 CommonAPI.showErrorMessage(i18n.lms1205m01('l120m01a.error24', {
					          'colName': ''
					    }));				
				return false;
			}else{
				return true;
			}
    	}else if(responseJSON.page == "04"){
    		//案由
			if ($("#LMS1205S04Form").valid()) {
				return $("#LMS1205S04Form").valid();
			}else{
				return false;
			}
    	}else if(responseJSON.page == "05"){
    		//說明
			if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
					(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "1")){
				if($("#CLS1205S05Form").valid()){
					return $("#CLS1205S05Form").valid();	
				}else{
					return false;
				}
			}else if(responseJSON.docCode == "2" || responseJSON.docCode == "3"){
				return $("#L120M01dForm03").valid();
			}else{
				return true;
			}
    	}else{
			return true;
		}
    },	
    sendData: function(){
    	if(responseJSON.page == "01"){
    		//文件資訊
    		return $("#LMS1205S01Form").serializeData();
    	}else if(responseJSON.page == "03"){
    		//額度明細表
    		return $("#L120M01BForm").serializeData();
    	}else if(responseJSON.page == "04"){
    		//案由
			return $("#LMS1205S04Form").serializeData();
    	}else if(responseJSON.page == "05"){
    		//說明
			if(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "2"){
				return $("#LMS1205S05Form01,#LMS1205S05Form04,#LMS1205S05Form06").serializeData();
			}else if((responseJSON.docType == "2" && responseJSON.docCode == "1") || 
					(responseJSON.docType == "1" && responseJSON.docCode == "1" && responseJSON.docKind == "1")){
				return $("#CLS1205S05Form").serializeData();
			}else if(responseJSON.docCode == "2" || responseJSON.docCode == "3"){
				return $("#L120M01dForm03").serializeData();
			}

    	}else if(responseJSON.page == "06"){
    		//其他
    		return $("#L120M01dForm03").serializeData();
    	}else if(responseJSON.page == "07"){
    		//綜合評估/往來彙總
    		return $("#L120M01dForm04").serializeData();
    	}else if(responseJSON.page == "08"){
			//相關文件
			return $("#formL120m01e").serializeData();
		}else if(responseJSON.page == "09"){
    		//補充說明
    		return $("#L120M01dForm05").serializeData();
		}else if(responseJSON.page == "21"){
    		//還款來源國
    		//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
    		return $("#LMS1205S21Form01").serializeData();		
    	}else if(responseJSON.page == "22"){
    		//不符合授信政策
    		//J-106-0087-001 Web e-Loan 海外授信美國地區新增不符合授信政策案件資訊  
    		return $("#LMS1205S22Form01").serializeData();			
    	}  	       
    }
});