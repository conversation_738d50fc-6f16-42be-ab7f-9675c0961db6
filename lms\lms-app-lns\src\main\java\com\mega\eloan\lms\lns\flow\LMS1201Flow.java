package com.mega.eloan.lms.lns.flow;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L130M01ADao;
import com.mega.eloan.lms.dao.L130S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L700M01ADao;
import com.mega.eloan.lms.lms.flow.LMS1205AreaFlow;
import com.mega.eloan.lms.lms.flow.LMS1205HeadFlow;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L130S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L700M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 國內授信案件授權外國外部、國金部、金控總部分行提會流程
 * </pre>
 * 
 * @since 2012/12/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/7,Miller,new
 *          </ul>
 */
@Component
public class LMS1201Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "LMS1201Flow";

	@Resource
	CommonMetaDao metaDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L700M01ADao l700m01aDao;

	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	C120M01ADao c120m01aDao;

	@Resource
	DocLogService docLogService;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;

	@Resource
	L130M01ADao l130m01aDao;

	@Resource
	L130S01BDao l130s01bDao;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}

	/**
	 * 編製中到待覆核
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "編製中", value = "to分行待覆核")
	public void start(FlowInstance instance) {
		toWaitCheck(instance);
	}

	/**
	 * 呈送至待覆核時要做的作業
	 * 
	 * @param instance
	 */
	private void toWaitCheck(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())) {

			// (108)第 3230 號
			if (LMSUtil.isSpecialBranch(unitNo)) {

				// (108)第 3230 號
				if ((UtilConstants.Casedoc.AreaChk.送初審審查.equals(Util.trim(meta
						.getAreaChk())))) {
					lmsService.resetL140M01A(meta,
							FlowDocStatusEnum.待覆核.getCode());
				} else {
					// 會簽案不用動額度明細表狀態--已經是擬核定了
				}

			} else {
				// 授權外額度明細表要把額度明細表的文件狀態改為待覆核
				lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());
			}
		} else if (UtilConstants.Casedoc.DocKind.授權內.equals(meta.getDocKind())
				&& UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(meta
						.getAuthLvl())) {
			// 當是授權內 且為營運中心授權內 其文件 狀態要變更
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());
		}
		if (user.getUnitNo().equals(meta.getCaseBrId())) {
			lmsService.gfnDB2SetELF442_CNTRNO(meta,
					UtilConstants.Cntrdoc.ACTION.呈主管, null);
		}
	}

	/**
	 * 離開退補件
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "待補件", value = "to分行待覆核")
	public void start2(FlowInstance instance) {
		toWaitCheck(instance);
	}

	/**
	 * 已會簽退回會簽後修改編製中
	 * 
	 * @param instance
	 */
	@Transition(node = "判斷2", value = "to會簽後修改編製中")
	public void backAfter(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 「編製中」會簽後修改功能審核層級不可以修改，是否加送會審單位為「送會簽」
		// (108)第 3230 號
		if (Util.equals(Util.trim(meta.getAreaChk()),
				UtilConstants.Casedoc.AreaChk.送初審)
				|| Util.equals(Util.trim(meta.getAreaChk()),
						UtilConstants.Casedoc.AreaChk.送初審審查)) {
			meta.setAreaChk(UtilConstants.Casedoc.AreaChk.送初審審查);
		} else {
			meta.setAreaChk(UtilConstants.Casedoc.AreaChk.送會簽審查);
		}

		// 將會審單位代碼清空
		meta.setAreaBrId(null);
		// 將提會註記清空，才會完全跑到編製中
		meta.setHqMeetFlag(null);
		l120m01aDao.save(meta);
		// 變更額度明細表為編製中
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 會簽後修改待覆核退回會簽後修改編製中
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策3", value = "to會簽後修改編製中")
	public void backAfter2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 將提會註記清空，才會完全跑到編製中
		meta.setHqMeetFlag(null);
		l120m01aDao.save(meta);
		// 變更額度明細表為編製中
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 
	 * 呈授管處
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to呈授管處")
	public void sendHead(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		
		// J-111-0488_05097_B1001 Web
		// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		if (lmsService.isOverAuthSendHead(meta)) {
			meta.setAreaChk(UtilConstants.Casedoc.AreaChk.送審查);
			l120m01aDao.save(meta);
		}
		String nextOwnbrid = UtilConstants.BankNo.授管處;
		meta.setOwnBrId(nextOwnbrid);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnbrid,
				meta.getCaseBrId());
		if (l700m01a != null) {
			meta.setHqAppraiser(l700m01a.getUserNo());
		}
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 判斷退補件欄位
		lmsService.checkBackUnit(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		// 新增授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid,
				DocAuthTypeEnum.VIEW_TRANSFER.getCode());
		// 新增近期已收
		lmsService.saveL000M01A(meta, UtilConstants.BankNo.授管處);
		meta.setHqMeetFlag(null);
		// 新增近期已收

		instance.setAttribute("flowCode", LMS1205HeadFlow.FLOW_CODE);

		IBranch theBranch = branchService.getBranch(Util.trim(meta
				.getCaseBrId()));

		if (theBranch != null) {
			String mainId = Util.trim(meta.getMainId());
			List<L130S01B> listL130s01a = l130s01bDao.findByMainId(mainId);
			String brnGroup = Util.trim(theBranch.getBrnGroup());

			if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())) {
				lmsService.sendEmail(mainId, meta, listL130s01a, "0");

				// J-103-0027 核准時設定為需要傳送卡務中心

				// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
				// 但結案註記要等授管處核准時才寫為Y
				L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
				if (l130m01a != null) {
					l130m01a.setNeedSend("Y");

					// J-105-0065-001 Web e-Loan
					// 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
					l130m01a.setSend912MailTime(CapDate.getCurrentTimestamp());

					// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
					// LMSServiceImpl.java \insertLnfe0854
					l130m01aDao.save(l130m01a);
				}

			}
		}

		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_呈授管處.getCode(), nextOwnbrid);
		String areack = meta.getAreaChk();
		if (!Util.isEmpty(areack)) {
			meta.setOwnBrId(nextOwnbrid);
			meta.setHqMeetFlag("0");
			switch (Util.parseInt(areack)) {
			case 1:
				// 否 -直接送"授管處-待收件"
				// 依流程變更目前編制行
				meta.setAreaDocstatus(null);
				meta.setAreaUpdater(null);
				meta.setAreaApprover(null);
				meta.setAreaApprTime(null);
				break;
			case 2:
				// lmsService.sendL121M01A(meta);
				break;
			case 5:
				// (108)第 3230 號
				// lmsService.sendL121M01A(meta);
				break;
			case 3:
				// 送審查-送營運中心 並增加一筆資料到授權檔該營運中心案件
				meta.setAreaDocstatus(null);
				meta.setAreaUpdater(null);
				meta.setAreaApprover(null);
				meta.setAreaApprTime(null);
				// 總處營業單位不論選否還是送審查皆直接呈送授管處
				// meta.setOwnBrId(meta.getAreaBrId());
				break;
			}
		}

	}

	/**
	 * 
	 * 分行待覆核呈區域中心
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to呈區域中心")
	public void sendArea(FlowInstance instance) {
		toArea(instance);
	}

	/**
	 * 
	 * 會簽待覆核呈區域中心
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策2", value = "to呈區域中心")
	public void sendArea2(FlowInstance instance) {
		toArea(instance);
	}

	/**
	 * 
	 * 會簽後修改待覆核呈區域中心
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策3", value = "to呈區域中心")
	public void sendArea3(FlowInstance instance) {
		toArea(instance);
	}

	/**
	 * 
	 * 呈送至區域中心時要做的作業
	 * 
	 * @param instance
	 *            流程資料
	 */
	private void toArea(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);

		// J-111-0488_05097_B1001 Web
		// e-Loan企金授信簽報系統增加強分行經理權限對於合併關係企業彙總額度之管控
		if (lmsService.isOverAuthSendHead(meta)) {
			meta.setAreaChk(UtilConstants.Casedoc.AreaChk.送審查);
			l120m01aDao.save(meta);
		}
		
		String nextOwnbrid = meta.getAreaBrId();
		meta.setOwnBrId(nextOwnbrid);
		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnbrid,
				meta.getCaseBrId());
		if (l700m01a != null) {
			meta.setAreaAppraiser(l700m01a.getUserNo());
		}
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		// 先判斷是否已經有這筆授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid, UtilConstants.AuthType.送審查);
		// 判斷退補件欄位
		lmsService.checkBackUnit(meta);
		// 要去除多餘的營運中心授權檔，不然在營運中心退過的案件再其營運中心會出現
		lmsService.checkL120A01A(meta);
		// 新增近期已收
		lmsService.saveL000M01A(meta, meta.getAreaBrId());
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_呈營運中心.getCode(), nextOwnbrid);
		instance.setAttribute("flowCode", LMS1205AreaFlow.FLOW_CODE);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);

		IBranch theBranch = branchService.getBranch(Util.trim(meta
				.getCaseBrId()));

		if (theBranch != null) {
			String mainId = Util.trim(meta.getMainId());
			List<L130S01B> listL130s01a = l130s01bDao.findByMainId(mainId);
			String brnGroup = Util.trim(theBranch.getBrnGroup());

			if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())) {
				lmsService.sendEmail(mainId, meta, listL130s01a, "0");

				// J-103-0027 核准時設定為需要傳送卡務中心

				// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
				// 但結案註記要等授管處核准時才寫為Y
				L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
				if (l130m01a != null) {
					l130m01a.setNeedSend("Y");

					// J-105-0065-001 Web e-Loan
					// 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
					l130m01a.setSend912MailTime(CapDate.getCurrentTimestamp());

					// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
					// LMSServiceImpl.java \insertLnfe0854
					l130m01aDao.save(l130m01a);
				}

			}
		}
		
		

	}

	/**
	 * 退回編製中
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to退回編製中")
	public void back(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		if (UtilConstants.Casedoc.DocType.個金.equals(meta.getDocType())) {
			for (C120M01A c120m01a : c120m01aDao.findByMainId(meta.getMainId())) {
				c120m01a.setModelTyp("");
				c120m01aDao.save(c120m01a);
			}
		}
		// 新增簽章欄
		lmsService.deleteL120M01F(meta.getMainId(),
				UtilConstants.BRANCHTYPE.分行,
				new String[] { UtilConstants.STAFFJOB.執行覆核主管L4 });
		// 變更額度明細表為編製中
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 核准
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))) {
			// 分行端覆核時把特殊(總處)分行額度批覆表砍掉(DELETEDTIME)
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			for (L140M01A l140m01a : l140m01as) {
				l140m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			}
			l140m01aDao.save(l140m01as);
		}
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance, "分行已核准");

	}

	/**
	 * 已婉卻
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策", value = "to已婉卻")
	public void reject(FlowInstance instance) throws FlowException,
			CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))) {
			// 分行端覆核時把特殊(總處)分行額度批覆表砍掉(DELETEDTIME)
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			for (L140M01A l140m01a : l140m01as) {
				l140m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			}
			l140m01aDao.save(l140m01as);
			if (l140m01as != null && !l140m01as.isEmpty()) {
				lmsService.resetL140M01A(meta, FlowDocStatusEnum.結案.getCode());
			}
		}
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance, "分行已婉卻");
	}

	/**
	 * 
	 * 呈授管處
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策2", value = "to呈授管處")
	public void sendHead2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		String nextOwnbrid = UtilConstants.BankNo.授管處;
		meta.setOwnBrId(nextOwnbrid);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnbrid,
				meta.getCaseBrId());
		if (l700m01a != null) {
			meta.setHqAppraiser(l700m01a.getUserNo());
		}
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 判斷退補件欄位
		lmsService.checkBackUnit(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		// 新增授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid,
				DocAuthTypeEnum.VIEW_TRANSFER.getCode());
		// 新增近期已收
		lmsService.saveL000M01A(meta, UtilConstants.BankNo.授管處);
		meta.setHqMeetFlag(null);
		// 新增近期已收

		instance.setAttribute("flowCode", LMS1205HeadFlow.FLOW_CODE);

		IBranch theBranch = branchService.getBranch(Util.trim(meta
				.getCaseBrId()));

		if (theBranch != null) {
			String mainId = Util.trim(meta.getMainId());
			List<L130S01B> listL130s01a = l130s01bDao.findByMainId(mainId);
			String brnGroup = Util.trim(theBranch.getBrnGroup());

			if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())) {
				lmsService.sendEmail(mainId, meta, listL130s01a, "0");

				// J-103-0027 核准時設定為需要傳送卡務中心

				// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
				// 但結案註記要等授管處核准時才寫為Y
				L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
				if (l130m01a != null) {
					l130m01a.setNeedSend("Y");

					// J-105-0065-001 Web e-Loan
					// 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
					l130m01a.setSend912MailTime(CapDate.getCurrentTimestamp());

					// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
					// LMSServiceImpl.java \insertLnfe0854
					l130m01aDao.save(l130m01a);
				}

			}
		}

		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_呈授管處.getCode(), nextOwnbrid);
		String areack = meta.getAreaChk();
		if (CreditDocStatusEnum.總處營業單位待覆核.getCode().equals(meta.getDocStatus())
				&& UtilConstants.Casedoc.AreaChk.送會簽.equals(areack)) {
			areack = UtilConstants.Casedoc.AreaChk.送會簽審查;
			meta.setAreaChk(areack);
		} else if (CreditDocStatusEnum.總處營業單位待覆核.getCode().equals(
				meta.getDocStatus())
				&& UtilConstants.Casedoc.AreaChk.送初審.equals(areack)) {
			// (108)第 3230 號
			areack = UtilConstants.Casedoc.AreaChk.送初審審查;
			meta.setAreaChk(areack);
		}
		if (!Util.isEmpty(areack)) {
			meta.setOwnBrId(nextOwnbrid);
			meta.setHqMeetFlag("0");
			switch (Util.parseInt(areack)) {
			case 1:
				// 否 -直接送"授管處-待收件"
				// 依流程變更目前編制行
				meta.setAreaDocstatus(null);
				meta.setAreaUpdater(null);
				meta.setAreaApprover(null);
				meta.setAreaApprTime(null);
				break;
			case 2:
				// lmsService.sendL121M01A(meta);
				break;
			case 5:
				// (108)第 3230 號
				// lmsService.sendL121M01A(meta);
				break;
			case 3:
			case 4:
			case 6:
				// (108)第 3230 號
				// 送審查-送營運中心 並增加一筆資料到授權檔該營運中心案件
				meta.setAreaDocstatus(null);
				meta.setAreaUpdater(null);
				meta.setAreaApprover(null);
				meta.setAreaApprTime(null);
				// 總處營業單位不論選否還是送審查皆直接呈送授管處
				// meta.setOwnBrId(meta.getAreaBrId());
				break;
			}
		}

		// (108)第 3230 號
		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())) {
			String unitNo = user.getUnitNo();
			// (108)第 3230 號
			if (LMSUtil.isSpecialBranch(unitNo)) {

				// (108)第 3230 號
				if ((UtilConstants.Casedoc.AreaChk.送初審審查.equals(Util.trim(meta
						.getAreaChk())))) {
					lmsService.resetL140M01A(meta,
							FlowDocStatusEnum.待覆核.getCode());
				}

			}
		}

		lmsService.save(meta);
	}

	/**
	 * 
	 * 呈授管處
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策3", value = "to呈授管處")
	public void sendHead3(FlowInstance instance) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);

		// (108)第 3230 號
		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())) {

			// (108)第 3230 號
			if (LMSUtil.isSpecialBranch(unitNo)) {

				// (108)第 3230 號
				if ((UtilConstants.Casedoc.AreaChk.送初審審查.equals(Util.trim(meta
						.getAreaChk())))) {
					lmsService.resetL140M01A(meta,
							FlowDocStatusEnum.待覆核.getCode());
				}

			}
		}

		instance.setAttribute("flowCode", LMS1205HeadFlow.FLOW_CODE);
	}

	/**
	 * 核准
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策3", value = "to核定")
	public void complete3(FlowInstance instance) throws FlowException,
			CapException {
		
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))) {
			// 分行端覆核時把特殊(總處)分行額度批覆表砍掉(DELETEDTIME)
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			for (L140M01A l140m01a : l140m01as) {
				l140m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			}
			l140m01aDao.save(l140m01as);
		}
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance, "分行已核准");
	}

	/**
	 * 已婉卻
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策3", value = "to已婉卻")
	public void reject3(FlowInstance instance) throws FlowException,
			CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))) {
			// 分行端覆核時把特殊(總處)分行額度批覆表砍掉(DELETEDTIME)
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			for (L140M01A l140m01a : l140m01as) {
				l140m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			}
			l140m01aDao.save(l140m01as);
			if (l140m01as != null && !l140m01as.isEmpty()) {
				lmsService.resetL140M01A(meta, FlowDocStatusEnum.結案.getCode());
			}
		}
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance, "分行已婉卻");
	}

	/**
	 * 退回已會簽
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策2", value = "to退回已會簽")
	public void back2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 新增簽章欄
		lmsService.deleteL120M01F(meta.getMainId(),
				UtilConstants.BRANCHTYPE.分行,
				new String[] { UtilConstants.STAFFJOB.執行覆核主管L4 });
		// 變更額度明細表為編製中
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 核准
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策2", value = "to核定")
	public void complete2(FlowInstance instance) throws FlowException,
			CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))) {
			// 分行端覆核時把特殊(總處)分行額度批覆表砍掉(DELETEDTIME)
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			for (L140M01A l140m01a : l140m01as) {
				l140m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			}
			l140m01aDao.save(l140m01as);
		}
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance, "分行已核准");
	}

	/**
	 * 已婉卻
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策2", value = "to已婉卻")
	public void reject2(FlowInstance instance) throws FlowException,
			CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		if (LMSUtil.isSpecialBranch(Util.trim(meta.getCaseBrId()))) {
			// 分行端覆核時把特殊(總處)分行額度批覆表砍掉(DELETEDTIME)
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
			for (L140M01A l140m01a : l140m01as) {
				l140m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			}
			l140m01aDao.save(l140m01as);
			if (l140m01as != null && !l140m01as.isEmpty()) {
				lmsService.resetL140M01A(meta, FlowDocStatusEnum.結案.getCode());
			}
		}
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);

		// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
		lmsService.notifyByMailForG1To940(instance, "分行已婉卻");
	}

	/**
	 * 當案件結束時做的動作
	 * 
	 * @param instance
	 *            flow 流程檔
	 * @param docstatus
	 *            文件狀態
	 * @param staffjob
	 *            職稱欄
	 * @throws CapException
	 * @throws FlowException
	 */
	private void toEndAction(FlowInstance instance, String docstatus,
			String staffjob) throws FlowException, CapException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 為營運中心或授管處覆核為Y，分行覆核為N
		meta.setIsHeadCheck(UtilConstants.DEFAULT.否);
		String docRslt = "";
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			docRslt = UtilConstants.Casedoc.DocRslt.承做;
			//J-110-0330 確定承做，寫入AO帳號管理員
			lmsService.gfnLNF013(meta);
		} else {
			docRslt = UtilConstants.Casedoc.DocRslt.婉卻;
		}
		lmsService.uploadELLNSEEK(meta);
		meta.setDocRslt(docRslt);
		meta.setOwnBrId(meta.getCaseBrId());
		l120m01aDao.save(meta);
		// 要去除多餘的營運中心授權檔，不然在已核准或已婉卻會多出現
		lmsService.checkL120A01A(meta);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行, staffjob);
		// 更新預約額度檔
		lmsService.gfnDB2SetELF442_CNTRNO(meta,
				UtilConstants.Cntrdoc.ACTION.覆核, docstatus);
		// 新增 核准額度資料檔 MIS.ELF447n
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				docstatus, meta.getCaseBrId());
		lmsService.upLoadMIS(meta);
		lmsService.upLnunid(meta);
		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			if (!LMSUtil.isClsCase(meta)) {
				lmsService.uploadL902Data(meta, docstatus);
			}
		}

		// 當為核准、且為國內個金案件需更新團貸母戶編號 至L140M03A grpCntrNo 以利動審表取得團貸額度明細表
		if (UtilConstants.Casedoc.DocRslt.承做.equals(docRslt)
				&& LMSUtil.isClsCase(meta)
				&& UtilConstants.Casedoc.DocCode.一般.equals(meta.getDocCode())) {
			// 取得所有批附表mainId
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表, null);
			List<String> mainIds = new ArrayList<String>();
			for (L140M01A l140m01a : l140m01as) {
				mainIds.add(l140m01a.getMainId());
			}
			lmsService.setL140M03AGrpCntrNo(meta, mainIds);
		}

		// 國內個金才會有上傳優惠房貸
		if (LMSUtil.isClsCase(meta)) {
			// 國內個金簽報書上傳項目
			clsService.upMisByCls(meta);
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表,
							FlowDocStatusEnum.已核准.getCode());
			for (L140M01A l140m01a : l140m01as) {
				lmsService.upELF431(meta, l140m01a);
			}

		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}