package com.mega.eloan.lms.batch.service.impl;

import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ElDeleteFileService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * batch 刪除天數
 * </pre>
 * 
 * @since 2012/7/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/26,REX,new
 *          </ul>
 */
@Service("clearData02BatchServiceImpl")
public class ClearData02BatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(DeleteBatchServiceImpl.class);

	private static final long serialVersionUID = 1L;

	@Resource
	EloandbBASEService r6dbService;

	@Resource
	DocFileService docFileService;

	@Resource
	FlowService flowService;
	@Resource
	DocLogService docLogService;

	@Resource
	ElDeleteFileService elDeleteFileService;

	@Resource
	private SysParameterService sysParameterService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		// 可以從json內取得參數
		long t1 = System.currentTimeMillis();
		LOGGER.info("傳入參數==>[{}]", json.toString());

		int totalCount = 0;
		JSONObject request = json.getJSONObject("request");

		// deleteTable.properties 記錄需要刪除的table
		Properties properties = new Properties();
		JSONObject result = new JSONObject();

		try {

			// 針對非報表刪除的開關參數
			String isCleanDataEffective = Util.trim(sysParameterService
					.getParamValue("COM_DATA_CLEAN_EFFECTIVE"));

			if (Util.equals(isCleanDataEffective, "Y")) {
				String currentTime = CapDate.getCurrentTimestamp().toString();
				r6dbService.setDocStatusDelToDeleteTime(currentTime);
			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"ClearData02BatchServiceImpl執行成功！");

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE,
					"ClearData02BatchServiceImpl執行失敗！==>"
							+ ex.getLocalizedMessage());
		}

		return result;
	}

}
