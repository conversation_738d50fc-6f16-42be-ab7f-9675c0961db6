/* 
 * C160M01EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C160M01EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160M01E;

/** 動用審核表簽章欄檔 **/
@Repository
public class C160M01EDaoImpl extends LMSJpaDao<C160M01E, String>
	implements C160M01EDao {

	@Override
	public C160M01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160M01E> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C160M01E> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C160M01E findByUniqueKey(String mainId, String staffNo, String staffJob){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (staffNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffNo", staffNo);
		if (staffJob != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C160M01E> findByIndex01(String mainId, String staffNo, String staffJob){
		ISearch search = createSearchTemplete();
		List<C160M01E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (staffNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffNo", staffNo);
		if (staffJob != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}