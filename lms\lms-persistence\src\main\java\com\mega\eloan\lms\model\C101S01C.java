/* 
 * C101S01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.ImportCheck;
import com.mega.eloan.lms.validation.group.SaveCheck;

/** 個金償債能力檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C101S01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 其他收入
	 * <p/>
	 * 100/12/08備註<br/>
	 * 薪資收入|1<br/>
	 * 營利收入|2<br/>
	 * 投資收入|3<br/>
	 * 租金收入|4<br/>
	 * 利息收入|5<br/>
	 * ※海外才需填寫
	 */
	@Size(max = 1)
	@Column(name = "OINCOME", length = 1, columnDefinition = "VARCHAR(1)")
	private String oIncome;

	/**
	 * 其他收入金額(幣別)
	 * <p/>
	 * 100/12/08備註<br/>
	 * ※海外才需填寫
	 */
	@Size(max = 3)
	@Column(name = "OMONEYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String oMoneyCurr;

	/**
	 * 其他收入金額(金額)
	 * <p/>
	 * 100/12/08備註<br/>
	 * ※海外才需填寫
	 */
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "OMONEYAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal oMoneyAmt;

	/**
	 * 家庭所得(幣別) (夫妻年收入)
	 **/
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 3)
	@Column(name = "YFAMCURR", length = 3, columnDefinition = "CHAR(3)")
	private String yFamCurr;

	/**
	 * 家庭所得(金額) (夫妻年收入)
	 **/
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "YFAMAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal yFamAmt;

	/**
	 * 家庭所得證明文件
	 * <p/>
	 * (單選)<br/>
	 * 個人綜合所得申報資料|1<br/>
	 * 扣繳憑單|2<br/>
	 * 薪資轉帳存摺|3<br/>
	 * 勞保薪資|4<br/>
	 * 租賃契約|5<br/>
	 * 其他收入證明|6
	 */
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "YINCOMECERT", length = 1, columnDefinition = "VARCHAR(1)")
	private String yIncomeCert;

	/** 個人負債比率 **/
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			Check2.class })
	@Digits(integer = 3, fraction = 2, groups = { SaveCheck.class, Check2.class ,Check.class })
	@Column(name = "DRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal dRate;

	/** 夫妻負債比率 **/
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@Digits(integer = 3, fraction = 2, groups = {SaveCheck.class, Check.class })
	@Column(name = "YRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal yRate;
	
	/** 家庭負債比率 **/
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@Digits(integer = 3, fraction = 2, groups = {SaveCheck.class, Check.class })
	@Column(name = "FRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal fRate;
	
	

	/**
	 * 使用信用卡循環信用或現金卡情形
	 * <p/>
	 * (複選) A或B或AB<br/>
	 * 最近一年有使用兩家(含)以上銀行之信用卡循環信用紀錄| A<br/>
	 * 最近一年有使用兩家(含)以上銀行之現金卡紀錄| B
	 */
	@Size(max = 3)
	@Column(name = "CREDIT", length = 3, columnDefinition = "VARCHAR(3)")
	private String credit;

	/**
	 * 是否於本行財富管理有定時定額扣款
	 * <p/>
	 * 是 | Y<br/>
	 * 否 | N
	 */
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISPERIODFUND", length = 1, columnDefinition = "CHAR(1)")
	private String isPeriodFund;

	/**
	 * 與本行其他業務往來(財富管理業務如基金保險信用卡等)
	 * <p/>
	 * 有 | Y<br/>
	 * 無 | N
	 */
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "BUSI", length = 1, columnDefinition = "CHAR(1)")
	private String busi;

	/** 與本行財富管理三個月平均總資產(幣別) **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 3)
	@Column(name = "INVMBALCURR", length = 3, columnDefinition = "CHAR(3)")
	private String invMBalCurr;

	/** 與本行財富管理三個月平均總資產(金額) **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "INVMBALAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal invMBalAmt;

	/** 與他行財富管理三個月平均總資產(幣別) **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 3)
	@Column(name = "INVOBALCURR", length = 3, columnDefinition = "CHAR(3)")
	private String invOBalCurr;

	/** 與他行財富管理三個月平均總資產(金額) **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "INVOBALAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal invOBalAmt;

	/** 與金融機構存款往來情形(近六個月平均餘額)(幣別) **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 3)
	@Column(name = "BRANCURR", length = 3, columnDefinition = "CHAR(3)")
	private String branCurr;

	/** 與金融機構存款往來情形(近六個月平均餘額)(金額) **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "BRANAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal branAmt;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 家庭年收入(幣別) 
	 **/
	@Size(max = 3)
	@Column(name = "FINCOMECURR", length = 3, columnDefinition = "CHAR(3)")
	private String fincomeCurr;

	/**
	 * 家庭年收入(金額) 
	 **/
//	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class })
//	@NotNull(message = "{required.message}", groups = { SaveCheck.class })
	@Column(name = "FINCOME", columnDefinition = "DECIMAL(10,0)")
	private BigDecimal fincome;
	
	/**
	 * 負債比系統化欄位 json data
	 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "RATEDATA", columnDefinition = "CLOB")
	private String rateData;
	
	/**
	 * 負債比是否重新計算
	 **/
	@Size(max = 1)
	@Column(name = "RECALFLG", length = 1, columnDefinition = "CHAR(1)")
	private String reCalFlg;
	
	/** 個人資產總額 **/
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "INDASSETTOTAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal indAssetTotAmt;

	/** 個人資產總額(幣別) **/
	@Size(max = 3)
	@Column(name = "INDASSETTOTAMTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String indAssetTotAmtCurr;
	
	/** 個人負債餘額 **/
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "INDDEBTBALANCE", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal indDebtBalance;

	/** 個人負債餘額(幣別) **/
	@Size(max = 3)
	@Column(name = "INDDEBTBALANCECURR", length = 3, columnDefinition = "CHAR(3)")
	private String indDebtBalanceCurr;
	
	/** 家庭資產總額 **/
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "FASSETTOTAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal fAssetTotAmt;

	/** 家庭資產總額(幣別) **/
	@Size(max = 3)
	@Column(name = "FASSETTOTAMTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String fAssetTotAmtCurr;
	
	/** 家庭負債餘額 **/
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "FDEBTBALANCE", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal fDebtBalance;

	/** 家庭負債餘額(幣別) **/
	@Size(max = 3)
	@Column(name = "FDEBTBALANCECURR", length = 3, columnDefinition = "CHAR(3)")
	private String fDebtBalanceCurr;
	
	/** 本行有擔餘額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LOANBALSBYID", columnDefinition="DECIMAL(17,2)")
	private BigDecimal loanBalSByid;

	/** 本行有擔餘額更新時間 **/
	@Column(name="LOANBALSTIME", columnDefinition="DATE")
	private Date loanBalSTime;

	/** 本行無擔餘額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LOANBALNBYID", columnDefinition="DECIMAL(17,2)")
	private BigDecimal loanBalNByid;

	/** 本行無擔餘額更新時間 **/
	@Column(name="LOANBALNTIME", columnDefinition="DATE")
	private Date loanBalNTime;

	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得其他收入
	 * <p/>
	 * 100/12/08備註<br/>
	 * 薪資收入|1<br/>
	 * 營利收入|2<br/>
	 * 投資收入|3<br/>
	 * 租金收入|4<br/>
	 * 利息收入|5<br/>
	 * ※海外才需填寫
	 */
	public String getOIncome() {
		return this.oIncome;
	}

	/**
	 * 設定其他收入
	 * <p/>
	 * 100/12/08備註<br/>
	 * 薪資收入|1<br/>
	 * 營利收入|2<br/>
	 * 投資收入|3<br/>
	 * 租金收入|4<br/>
	 * 利息收入|5<br/>
	 * ※海外才需填寫
	 **/
	public void setOIncome(String value) {
		this.oIncome = value;
	}

	/**
	 * 取得其他收入金額(幣別)
	 * <p/>
	 * 100/12/08備註<br/>
	 * ※海外才需填寫
	 */
	public String getOMoneyCurr() {
		return this.oMoneyCurr;
	}

	/**
	 * 設定其他收入金額(幣別)
	 * <p/>
	 * 100/12/08備註<br/>
	 * ※海外才需填寫
	 **/
	public void setOMoneyCurr(String value) {
		this.oMoneyCurr = value;
	}

	/**
	 * 取得其他收入金額(金額)
	 * <p/>
	 * 100/12/08備註<br/>
	 * ※海外才需填寫
	 */
	public BigDecimal getOMoneyAmt() {
		return this.oMoneyAmt;
	}

	/**
	 * 設定其他收入金額(金額)
	 * <p/>
	 * 100/12/08備註<br/>
	 * ※海外才需填寫
	 **/
	public void setOMoneyAmt(BigDecimal value) {
		this.oMoneyAmt = value;
	}

	/**
	 * 取得家庭所得(幣別) (夫妻年收入)
	 **/
	public String getYFamCurr() {
		return this.yFamCurr;
	}

	/**
	 * 設定家庭所得(幣別) (夫妻年收入)
	 **/
	public void setYFamCurr(String value) {
		this.yFamCurr = value;
	}

	/**
	 * 取得家庭所得(金額) (夫妻年收入)
	 **/
	public BigDecimal getYFamAmt() {
		return this.yFamAmt;
	}

	/**
	 * 設定家庭所得(金額) (夫妻年收入)
	 **/
	public void setYFamAmt(BigDecimal value) {
		this.yFamAmt = value;
	}

	/**
	 * 取得家庭所得證明文件
	 * <p/>
	 * (單選)<br/>
	 * 個人綜合所得申報資料|1<br/>
	 * 扣繳憑單|2<br/>
	 * 薪資轉帳存摺|3<br/>
	 * 勞保薪資|4<br/>
	 * 租賃契約|5<br/>
	 * 其他收入證明|6
	 */
	public String getYIncomeCert() {
		return this.yIncomeCert;
	}

	/**
	 * 設定家庭所得證明文件
	 * <p/>
	 * (單選)<br/>
	 * 個人綜合所得申報資料|1<br/>
	 * 扣繳憑單|2<br/>
	 * 薪資轉帳存摺|3<br/>
	 * 勞保薪資|4<br/>
	 * 租賃契約|5<br/>
	 * 其他收入證明|6
	 **/
	public void setYIncomeCert(String value) {
		this.yIncomeCert = value;
	}

	/** 取得個人負債比率 **/
	public BigDecimal getDRate() {
		return this.dRate;
	}

	/** 設定個人負債比率 **/
	public void setDRate(BigDecimal value) {
		this.dRate = value;
	}

	/** 取得夫妻負債比率 **/
	public BigDecimal getYRate() {
		return this.yRate;
	}

	/** 設定夫妻負債比率 **/
	public void setYRate(BigDecimal value) {
		this.yRate = value;
	}
	
	/** 取得家庭負債比率 **/
	public BigDecimal getFRate() {
		return this.fRate;
	}

	/** 設定家庭負債比率 **/
	public void setFRate(BigDecimal value) {
		this.fRate = value;
	}

	/**
	 * 取得使用信用卡循環信用或現金卡情形
	 * <p/>
	 * (複選) A或B或AB<br/>
	 * 最近一年有使用兩家(含)以上銀行之信用卡循環信用紀錄| A<br/>
	 * 最近一年有使用兩家(含)以上銀行之現金卡紀錄| B
	 */
	public String getCredit() {
		return this.credit;
	}

	/**
	 * 設定使用信用卡循環信用或現金卡情形
	 * <p/>
	 * (複選) A或B或AB<br/>
	 * 最近一年有使用兩家(含)以上銀行之信用卡循環信用紀錄| A<br/>
	 * 最近一年有使用兩家(含)以上銀行之現金卡紀錄| B
	 **/
	public void setCredit(String value) {
		this.credit = value;
	}

	/**
	 * 取得是否於本行財富管理有定時定額扣款
	 * <p/>
	 * 是 | Y<br/>
	 * 否 | N
	 */
	public String getIsPeriodFund() {
		return this.isPeriodFund;
	}

	/**
	 * 設定是否於本行財富管理有定時定額扣款
	 * <p/>
	 * 是 | Y<br/>
	 * 否 | N
	 **/
	public void setIsPeriodFund(String value) {
		this.isPeriodFund = value;
	}

	/**
	 * 取得與本行其他業務往來(財富管理業務如基金保險信用卡等)
	 * <p/>
	 * 有 | Y<br/>
	 * 無 | N
	 */
	public String getBusi() {
		return this.busi;
	}

	/**
	 * 設定與本行其他業務往來(財富管理業務如基金保險信用卡等)
	 * <p/>
	 * 有 | Y<br/>
	 * 無 | N
	 **/
	public void setBusi(String value) {
		this.busi = value;
	}

	/** 取得與本行財富管理三個月平均總資產(幣別) **/
	public String getInvMBalCurr() {
		return this.invMBalCurr;
	}

	/** 設定與本行財富管理三個月平均總資產(幣別) **/
	public void setInvMBalCurr(String value) {
		this.invMBalCurr = value;
	}

	/** 取得與本行財富管理三個月平均總資產(金額) **/
	public BigDecimal getInvMBalAmt() {
		return this.invMBalAmt;
	}

	/** 設定與本行財富管理三個月平均總資產(金額) **/
	public void setInvMBalAmt(BigDecimal value) {
		this.invMBalAmt = value;
	}

	/** 取得與他行財富管理三個月平均總資產(幣別) **/
	public String getInvOBalCurr() {
		return this.invOBalCurr;
	}

	/** 設定與他行財富管理三個月平均總資產(幣別) **/
	public void setInvOBalCurr(String value) {
		this.invOBalCurr = value;
	}

	/** 取得與他行財富管理三個月平均總資產(金額) **/
	public BigDecimal getInvOBalAmt() {
		return this.invOBalAmt;
	}

	/** 設定與他行財富管理三個月平均總資產(金額) **/
	public void setInvOBalAmt(BigDecimal value) {
		this.invOBalAmt = value;
	}

	/** 取得與金融機構存款往來情形(近六個月平均餘額)(幣別) **/
	public String getBranCurr() {
		return this.branCurr;
	}

	/** 設定與金融機構存款往來情形(近六個月平均餘額)(幣別) **/
	public void setBranCurr(String value) {
		this.branCurr = value;
	}

	/** 取得與金融機構存款往來情形(近六個月平均餘額)(金額) **/
	public BigDecimal getBranAmt() {
		return this.branAmt;
	}

	/** 設定與金融機構存款往來情形(近六個月平均餘額)(金額) **/
	public void setBranAmt(BigDecimal value) {
		this.branAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得家庭年收入(幣別) **/
	public String getFincomeCurr() {
		return fincomeCurr;
	}
	/** 設定家庭年收入(幣別) **/
	public void setFincomeCurr(String fincomeCurr) {
		this.fincomeCurr = fincomeCurr;
	}
	/** 取得家庭年收入 **/
	public BigDecimal getFincome() {
		return fincome;
	}
	/** 設定家庭年收入 **/
	public void setFincome(BigDecimal fincome) {
		this.fincome = fincome;
	}

	public void setRateData(String rateData) {
		this.rateData = rateData;
	}

	public String getRateData() {
		// 處理轉檔資料，把 || 換成 ,
		return rateData == null ? rateData : rateData.replaceAll("\\|\\|", ",");
	}

	public void setReCalFlg(String reCalFlg) {
		this.reCalFlg = reCalFlg;
	}

	public String getReCalFlg() {
		return reCalFlg;
	}

	public BigDecimal getIndAssetTotAmt() {
		return indAssetTotAmt;
	}

	public void setIndAssetTotAmt(BigDecimal indAssetTotAmt) {
		this.indAssetTotAmt = indAssetTotAmt;
	}

	public String getIndAssetTotAmtCurr() {
		return indAssetTotAmtCurr;
	}

	public void setIndAssetTotAmtCurr(String indAssetTotAmtCurr) {
		this.indAssetTotAmtCurr = indAssetTotAmtCurr;
	}

	public BigDecimal getIndDebtBalance() {
		return indDebtBalance;
	}

	public void setIndDebtBalance(BigDecimal indDebtBalance) {
		this.indDebtBalance = indDebtBalance;
	}

	public String getIndDebtBalanceCurr() {
		return indDebtBalanceCurr;
	}

	public void setIndDebtBalanceCurr(String indDebtBalanceCurr) {
		this.indDebtBalanceCurr = indDebtBalanceCurr;
	}

	public BigDecimal getfAssetTotAmt() {
		return fAssetTotAmt;
	}

	public void setfAssetTotAmt(BigDecimal fAssetTotAmt) {
		this.fAssetTotAmt = fAssetTotAmt;
	}

	public String getfAssetTotAmtCurr() {
		return fAssetTotAmtCurr;
	}

	public void setfAssetTotAmtCurr(String fAssetTotAmtCurr) {
		this.fAssetTotAmtCurr = fAssetTotAmtCurr;
	}

	public BigDecimal getfDebtBalance() {
		return fDebtBalance;
	}

	public void setfDebtBalance(BigDecimal fDebtBalance) {
		this.fDebtBalance = fDebtBalance;
	}

	public String getfDebtBalanceCurr() {
		return fDebtBalanceCurr;
	}

	public void setfDebtBalanceCurr(String fDebtBalanceCurr) {
		this.fDebtBalanceCurr = fDebtBalanceCurr;
	}
	
	/** 取得本行有擔餘額 **/
	public BigDecimal getLoanBalSByid() {
		return this.loanBalSByid;
	}
	/** 設定本行有擔餘額 **/
	public void setLoanBalSByid(BigDecimal value) {
		this.loanBalSByid = value;
	}

	/** 取得本行有擔餘額更新時間 **/
	public Date getLoanBalSTime() {
		return this.loanBalSTime;
	}
	/** 設定本行有擔餘額更新時間 **/
	public void setLoanBalSTime(Date value) {
		this.loanBalSTime = value;
	}

	/** 取得本行無擔餘額 **/
	public BigDecimal getLoanBalNByid() {
		return this.loanBalNByid;
	}
	/** 設定本行無擔餘額 **/
	public void setLoanBalNByid(BigDecimal value) {
		this.loanBalNByid = value;
	}

	/** 取得本行無擔餘額更新時間 **/
	public Date getLoanBalNTime() {
		return this.loanBalNTime;
	}
	/** 設定本行無擔餘額更新時間 **/
	public void setLoanBalNTime(Date value) {
		this.loanBalNTime = value;
	}
}
