var initS23aJson = {
	_isLoad: false,
	handlerName : null,
	listGrid: null,
	setHandler : function(){
		this.handlerName = "microentformhandler";	
	},
	_initGrid: function(){
		this.listGrid = $("#gridview_microList").iGrid({
			handler : 'lms1201gridhandler',
			height : "100px",
			autowidth : true,
			sortname : 'custRelation|custId|custName',
			sortorder:'asc|asc|asc',
			postData : {
				formAction : "queryL120s10a",
				mainId : responseJSON.mainid
			},
			loadComplete : function() {
				$('#gridview_microList a').click(function(e) {
					e.preventDefault();	// 避免<a href="#"> go to top
				});
			},
			colModel : [{
				colHeader : i18n.lmss23a["L120s10a.custId"],//"統編", 
				name : 'custId',
				width : 8,
				sortable : false,
				align : 'left'
	        },{
				colHeader : i18n.lmss23a["L120s10a.custName"],//"戶名",
				width : 14,
				name : 'custName',
				sortable : false,
				align : 'left',
				formatter : 'click',
				onclick : initS23aJson.openBox
			},{
				colHeader : i18n.lmss23a["L120s10a.custRelation"],//"與本案關係",
				name : 'custRelationStr',
				width : 14,
				sortable : false,
				align : "left"
			},{
				name : 'oid',
				hidden : true
			},{
				name : 'seqNum',
				hidden : true	
			}],
			ondblClickRow : function(rowid) {
				var data = initS23aJson.listGrid.getRowData(rowid);
				initS23aJson.openBox(null, null, data);
			}
		});
	},
	_initEvent: function(){
		$("#importData").click(function(){
			$.ajax({
				async: false,
				handler : initS23aJson.handlerName,
				type : "POST",
				data : {
					formAction : "importData",
					oid : $("#oid").val()//responseJSON.oid
				},
				success : function(json) {
					$("#formDetail").setData(json.S23Form, false);
					
					if(json.S23Form.j10Date){
						$("#J10Detail").show();
						$("#noJ10").hide();
					} else {
						$("#J10Detail").hide();
						$("#noJ10").show();
					}
				}
			});
        })
	},
	_init: function(){
        if (!this._isLoad) {
            this._initGrid();
			this._initEvent();
            this._isLoad = true;            
        } else {
            this._reloadGrid();
        }
    },
	openBox: function(cellvalue, options, rowObject){
        var $form = $("#formDetail");
        $form.reset();

		var buttons = {
	        "close": function(){
	            $.thickbox.close();
	        }
		}
		
        $.ajax({
            handler: initS23aJson.handlerName,
            action: "queryL120s10a",
            data : {
				oid : rowObject.oid
			},
            success: function(obj){
				$form.setData(obj.S23Form, false);
				
				if(obj.S23Form.j10Date){
					$("#J10Detail").show();
					$("#noJ10").hide();
				} else {
					$("#J10Detail").hide();
					$("#noJ10").show();
				}
				
				$("#listDetailThickbox").thickbox({
                    title: "",//i18n.lmss23a[""],
                    width: 800,
                    height: 400,
                    modal: true,
                    readOnly: _openerLockDoc == "1" || initS23aJson.toreadOnly ,
                    i18n: i18n.def,
                    buttons: buttons
                });
            }
        });
    }
};

/**
 * 引進名單
 */
function importList(){
	var count=$("#gridview_microList").jqGrid('getGridParam','records');
	if(count > 0){
		//"執行引進後會刪除已存在之名單，是否確定執行？"
		CommonAPI.confirmMessage(i18n.lmss23a["L120s10a.message01"], function(b){
			if (b) {					
				//是的function
				$.ajax({
					async: false,
					handler : initS23aJson.handlerName,
					type : "POST",
					data : {
						formAction : "importList",
						mainId : responseJSON.mainid
					},
					success : function(json) {
						initS23aJson.listGrid.trigger("reloadGrid");
					}
				});				
			}				
		});		
	}else{
		$.ajax({
			async: false,
			handler : initS23aJson.handlerName,
			type : "POST",
			data : {
				formAction : "importList",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				initS23aJson.listGrid.trigger("reloadGrid");
			}
		});				
	}
}

function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    ilog.debug("=============readOnly=============" + auth.readOnly);
    // 海外_編製中("01O"),       海外_待補件("07O"),       會簽後修改編製中("01K")
    if (auth.readOnly || (responseJSON.mainDocStatus != "01O" && responseJSON.mainDocStatus != "07O" && responseJSON.mainDocStatus != "01K")) {
        return true;
    }
    return false;
}

$(document).ready(function(){
	initS23aJson.setHandler();
	if (checkReadonly()) {
        $(".readOnlyhide").hide();
        $("form").lockDoc();
		_openerLockDoc="1";
    }
	initS23aJson._init();
});