/* 
 * LMS0015ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dao.L001M01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L141M01ADao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.VL015M01A01Dao;
import com.mega.eloan.lms.lms.service.LMS0015Service;
import com.mega.eloan.lms.model.L001M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.VL015M01A01;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 待辦案件
 * </pre>
 * 
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */

@Service
public class LMS0015ServiceImpl implements LMS0015Service {
	private static Logger logger = LoggerFactory
			.getLogger(LMS0015ServiceImpl.class);
	@Resource
	VL015M01A01Dao l015v00aDao;

	@Resource
	L001M01ADao l001m01aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L141M01ADao l141m01aDao;

	@Resource
	L160M01ADao l160m01aDao;

	@Override
	public Page<VL015M01A01> findPage(ISearch search) {

		return l015v00aDao.findPage(search);
	}

	@Override
	public L001M01A findL001m01a(String userId) {
		return l001m01aDao.findByUniqueKey(userId);
	}

	@SuppressWarnings({ "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class<?> clazz, String oid) {
		if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByOid(oid);
		} else if (clazz == L141M01A.class) {
			return (T) l141m01aDao.findByOid(oid);
		} else if (clazz == L160M01A.class) {
			return (T) l160m01aDao.findByOid(oid);
		}
		logger.debug("Class is Null !!!");
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L001M01A) {
					l001m01aDao.save((L001M01A) model);
				} else if (model instanceof L120M01A) {
					((L120M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l120m01aDao.save((L120M01A) model);
				} else if (model instanceof L141M01A) {
					((L141M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l141m01aDao.save((L141M01A) model);
				} else if (model instanceof L160M01A) {
					((L160M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l160m01aDao.save((L160M01A) model);
				}
			}
		}
	}
}
