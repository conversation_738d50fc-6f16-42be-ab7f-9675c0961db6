package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;

/**
 * <pre>
 * 卡友貸評分調整表
 * </pre>
 * 
 * @since 2019/05/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/05/13,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131s08")
public class CLS1131S08Page extends AbstractOutputPage {

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {

		setNeedHtml(true); // need html

		setJavascript(new String[] { "pagejs/cls/CLS1131S08Page.js" });

		return "&nbsp;";
	}

	// UPGRADE: 待確認是否需要ViewName
	@Override
	protected String getViewName() {
		return null;
	}

}
