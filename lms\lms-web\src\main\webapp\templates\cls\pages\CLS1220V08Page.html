<html xmlns="http://www.w3.org/1999/xhtml"
xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody"d>
		
		<div id='div_applyKindP_History' style='display:none;' >
			<form id='div_applyKindP_History_form'>
				<table class='tb2' width='95%' >
					<tr>
				  		<td class='hd2' width='20%' nowrap><span id="div_applyKindP_History_label_custId"></span> </td>				 
				  		<td width='80%'>
				  		<input type='text' id='search_custId' name='search_custId' maxlength='10' size='10' />
							&nbsp;&nbsp;&nbsp;&nbsp;
							<button type="button" id='filter_historyBtn'>
								<span class='text-only'><th:block th:text="#{'button.search'}"></th:block></span>
							</button>
				  		</td>						
					</tr>
					
				</table>
			</form>
			<div id='grid_applyKindP_History'></div>
		</div>
		
		<div id='div_changeOwnBrId_memo' style='display:none;' >
			<form id='div_changeOwnBrId_memo_form'>
				<table class='tb2' width='98%' >
					<tr>
				  		<td class='hd2' style='width:100px; vertical-align:top;' nowrap>
							<th:block th:text="#{'changeOwnBrId_memo'}">改分派備註</th:block> 
						</td>				 
				  		<td style='padding-right:12px; '>
				  			<textarea id="memo" name="memo" class="required " style="width:100%;height:140px" maxlength='900' maxlengthC='300' >
							</textarea>				 
						</td>				 
					</tr>					
				</table>
				<span class='text-red'>※輸入的備註，將會通知「對方分行」</span>
			</form>
		</div>
		
		<div id='div_create_CSC_excel' style='display:none;' >
			<form id='div_create_CSC_excel_form'>
				<table class='tb2' width='98%' >
					<tr>
						<td class='hd2' width='30%' nowrap>
							<th:block th:text="#{'csc_applyTS'}">線上進件日期</th:block> 
						</td>
						<td>
							<input type='text' id='csc_applyTS_beg' name='csc_applyTS_beg' maxlength='10' class='date' />	 ~ 
							<input type='text' id='csc_applyTS_end' name='csc_applyTS_end' maxlength='10' class='date' />
						</td>
					</tr>
					<tr>
						<td class='hd2' width='30%' nowrap>
							<th:block th:text="#{'csc_ploanPlan'}">行銷方案</th:block> 
						</td>
						<td>
							<select space="true" id="csc_ploan_plan" name="csc_ploan_plan" combotype='2'></select>	
						</td>
					</tr>
				</table>
			</form>
		</div>
	</th:block>
</body>
</html>
