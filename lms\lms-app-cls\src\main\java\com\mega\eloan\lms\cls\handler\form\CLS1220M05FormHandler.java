package com.mega.eloan.lms.cls.handler.form;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.gwclient.PLOAN002;
import com.mega.eloan.common.gwclient.PLOAN002.PLOAN002_loanInfo;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1220M05Page;
import com.mega.eloan.lms.cls.pages.CLS1220V09Page;
import com.mega.eloan.lms.cls.panels.CLS1220S06Panel;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01B;
import com.mega.eloan.lms.model.C122M01C;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.eloan.lms.model.C122M01G;
import com.mega.eloan.lms.model.C122S01F;
import com.mega.eloan.lms.model.C122S01G;
import com.mega.eloan.lms.model.C122S01H;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上房貸
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1220m05formhandler")
@DomainClass(C122M01A.class)
public class CLS1220M05FormHandler extends AbstractFormHandler {

	@Resource
	CLS1220Service service;

	@Resource
	UserInfoService userInfoService;	
	
	@Resource
	BranchService branchService;

	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;

	@Resource
	DocLogService docLogService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	CLS1220M10FormHandler cls1220m10formhandler;
	
	Properties prop_cls1220m05 = MessageBundleScriptCreator.getComponentResource(CLS1220M05Page.class);
	Properties prop_cls1220v09 = MessageBundleScriptCreator.getComponentResource(CLS1220V09Page.class);
	Properties prop_abstractEloan = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	Properties prop_cls1220s06 = MessageBundleScriptCreator.getComponentResource(CLS1220S06Panel.class);
	
	
	private String build_addr_fullTarget(String _AddressCity //地址縣市
			, String _AddressTown //地址鄉鎮區域
			, String _AddressVillage //地址村里
			, String _AddressNeighborhood //地址鄰
			, String _AddressStreet	//地址街道
			, String _AddressSection //地址段
			, String _AddressLane	//地址巷
			, String _AddressAlley	//地址弄
			, String _AddressNo	 //地址號
			, String _AddressFloor	//地址樓
			, String _AddressRoom	//地址室 
			){
		return Util.trim(_AddressCity)+ Util.trim(_AddressTown)
			+ build_addr_2(_AddressCity //地址縣市
					,  _AddressTown //地址鄉鎮區域
					,  _AddressVillage //地址村里
					,  _AddressNeighborhood //地址鄰
					,  _AddressStreet	//地址街道
					,  _AddressSection //地址段
					,  _AddressLane	//地址巷
					,  _AddressAlley	//地址弄
					,  _AddressNo	 //地址號
					,  _AddressFloor	//地址樓
					,  _AddressRoom	//地址室 
					);
	}
	
	
	private String build_addr_2(String _AddressCity //地址縣市
			, String _AddressTown //地址鄉鎮區域
			, String _AddressVillage //地址村里
			, String _AddressNeighborhood //地址鄰
			, String _AddressStreet	//地址街道
			, String _AddressSection //地址段
			, String _AddressLane	//地址巷
			, String _AddressAlley	//地址弄
			, String _AddressNo	 //地址號
			, String _AddressFloor	//地址樓
			, String _AddressRoom	//地址室 
			){
		String s_AddressVillage = Util.trim(_AddressVillage);
		String s_AddressNeighborhood = Util.trim(_AddressNeighborhood);
		String s_AddressStreet = Util.trim(_AddressStreet);
		String s_AddressSection = Util.trim(_AddressSection);
		String s_AddressLane = Util.trim(_AddressLane);
		String s_AddressAlley = Util.trim(_AddressAlley);
		String s_AddressNo = Util.trim(_AddressNo);
		String s_AddressFloor = Util.trim(_AddressFloor);
		String s_AddressRoom = Util.trim(_AddressRoom);	
		
		return s_AddressVillage
			+ s_AddressNeighborhood+(Util.isEmpty(s_AddressNeighborhood)?"":(s_AddressNeighborhood.endsWith("鄰")?"":"鄰"))
			+ s_AddressStreet
			+ s_AddressSection+(Util.isEmpty(s_AddressSection)?"":"段")
			+ s_AddressLane+(Util.isEmpty(s_AddressLane)?"":"巷")
			+ s_AddressAlley+(Util.isEmpty(s_AddressAlley)?"":"弄")
			+ s_AddressNo+(Util.isEmpty(s_AddressNo)?"":"號")
			+ s_AddressFloor+(Util.isEmpty(s_AddressFloor)?"":"樓")
			+(Util.isEmpty(s_AddressRoom)?"":"之")+ s_AddressRoom;
	}
	
	private String ploan_loanInfo_collateralAddrInfo(PLOAN002_loanInfo ploan002_loanInfo ){
		
		String _AddressCity	=	Util.trim(ploan002_loanInfo.getCollateralAddressCity()); //縣市
		String _AddressTown	=	Util.trim(ploan002_loanInfo.getCollateralAddressTown()); //鄉鎮區域
		String _AddressVillage	=	Util.trim(ploan002_loanInfo.getCollateralAddressVillage()); //村里
		String _AddressNeighborhood	=	Util.trim(ploan002_loanInfo.getCollateralAddressNeighborhood()); //鄰
		String _AddressStreet	=	Util.trim(ploan002_loanInfo.getCollateralAddressStreet()); //街道
		String _AddressSection	=	Util.trim(ploan002_loanInfo.getCollateralAddressSection()); //段
		String _AddressLane	=	Util.trim(ploan002_loanInfo.getCollateralAddressLane()); //巷
		String _AddressAlley	=	Util.trim(ploan002_loanInfo.getCollateralAddressAlley()); //弄
		String _AddressNo	=	Util.trim(ploan002_loanInfo.getCollateralAddressNo()); //號
		String _AddressFloor	=	Util.trim(ploan002_loanInfo.getCollateralAddressFloor()); //樓
		String _AddressRoom	=	Util.trim(ploan002_loanInfo.getCollateralAddressRoom()); //室
		
		return  build_addr_fullTarget(_AddressCity //地址縣市
				, _AddressTown //地址鄉鎮區域
				, _AddressVillage //地址村里
				, _AddressNeighborhood //地址鄰
				, _AddressStreet	//地址街道
				, _AddressSection //地址段
				, _AddressLane	//地址巷
				, _AddressAlley	//地址弄
				, _AddressNo	 //地址號
				, _AddressFloor	//地址樓
				, _AddressRoom	//地址室 
				);
		
	}
	
	/**
	 * 查詢文件
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int page = Util.parseInt(params.getString("page"));

		C122M01A meta = null;
		C122M01A parent_meta = null;
		if (mainOid != null) {
			meta = service.getC122M01A_byOid(mainOid);  
			if(meta!=null){
				parent_meta = service.getPloanParentMeta(meta);
			}
		}
		String itemType = "0";
		C122M01B c122m01b = service.getC122M01B_byMainIdItemType(meta.getMainId(), itemType);
		PLOAN002 ploan_obj = null;
		if(c122m01b!=null 
				&& (Util.equals(UtilConstants.C122_ApplyKind.E, meta.getApplyKind()) || Util.equals(UtilConstants.C122_ApplyKind.F, meta.getApplyKind())) 
				&& Util.equals("PLOAN002", c122m01b.getJsonVoClass())){
			ObjectMapper objectMapper = new ObjectMapper();
			try {
				ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN002.class);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));				
			}							
		}
		//===================
		String IncomType = Util.trim(meta.getIncomType());
		switch (page) {
		case 1: // CLS1220S01PanelE.html
			LMSUtil.addMetaToResult(result, meta, new String[]{  "ownBrId", "custId", "dupNo", "orgBrId"
					, "custName", "docStatus", "agreeQueryEJTs", "agreeQueryEJVer", "agreeQueryEJIp"
					, "applyTS", "ploanCaseNo", "applyCurr", "notifyMemo", "createTime", "updateTime"
					, "statFlag", "ploanCasePos", "isClosed", "maturity","maturityM"
					, "nowExtend", "extYear", "purchaseHouse"
					});
				
			if(Util.equals(IncomType, UtilConstants.C122_IncomType.線上)){//線上進件才會有IP
				String applyIpRefg = "0";
				if(Util.isNotEmpty(meta.getApplyIpFreq())){
					if(meta.getApplyIpFreq().compareTo(BigDecimal.ZERO) !=0){
						BigDecimal ApplyIpFreq = meta.getApplyIpFreq().stripTrailingZeros();
						String ApplyIpFreqStr = ApplyIpFreq.toPlainString();
						applyIpRefg = ApplyIpFreqStr;
					}
				}
//				(在30日內共進件次數：N次)
				String newApplyIp = Util.trim(meta.getApplyIPAddr()) + " (在30日內共進件次數：" + applyIpRefg + " 次)";
				result.set("applyIPAddr", newApplyIp);
			}else{
				result.set("applyIPAddr", "");
			}
			
			//H-111-0199 iXML
			List<C122M01G> c122m01gList = service.findC122M01GbyMainId(meta.getMainId());
			if(c122m01gList!=null && c122m01gList.size()>0){
				result.set("showIxml", "Y");
			}else{
				result.set("showIxml", "");
			}
			
			result.set("ownBrName", branchService.getBranchName(meta.getOwnBrId()));
			result.set("applyAmt", NumConverter.addComma(meta.getApplyAmt()==null?BigDecimal.ZERO:meta.getApplyAmt()));
			result.set("creator", Util.trim(meta.getCreator())+" "+Util.trim(new UserNameFormatter(userInfoService).reformat(meta.getCreator())));
			result.set("updater", Util.trim(meta.getUpdater())+" "+Util.trim(new UserNameFormatter(userInfoService).reformat(meta.getUpdater())));
			result.set("incomType", Util.trim(meta.getIncomType()));
			//房貸申請書面，有出現分行清單
			String orgBrId = Util.trim(meta.getOrgBrId());
			result.set("orgBrName", Util.isNotEmpty(orgBrId)?branchService.getBranchName(orgBrId):"");
			if(true){
				Map<String, String> cache_map = new HashMap<String, String>();
				result.set("loanBrNo", service.build_loanBrNo(cache_map, meta));
				result.set("payrollTransfersBrNo", service.build_payrollTransfersBrNo(cache_map, meta));
			}
			
			if(!Util.equals("Y", meta.getNowExtend())){
				result.set("extYear", "0");
			}
			if(true){
				result.set("ploan002_purposeType", Util.trim(meta.getPloan002_purposeType()));
			}
			
			if(ploan_obj!=null && ploan_obj.getBasicInfo()!=null){
				result.set("ploan_basicInfo_avgTransactionAmt", Util.trim(ploan_obj.getBasicInfo().getAvgTransactionAmt()));
				
				String ploan_basicInfo_serviceAssociateDeptCode = Util.trim(ploan_obj.getBasicInfo().getServiceAssociateDeptCode());
				String ploan_basicInfo_relationWithBorrower = Util.trim(ploan_obj.getBasicInfo().getRelationWithBorrower());
				String ploan_basicInfo_liveWithBorrower = Util.trim(ploan_obj.getBasicInfo().getLiveWithBorrower());
				String ploan_basicInfo_guarantyReason = Util.trim(ploan_obj.getBasicInfo().getGuarantyReason());
				String ploan_basicInfo_otherGuarantyReason = Util.trim(ploan_obj.getBasicInfo().getOtherGuarantyReason());
				result.set("ploan_basicInfo_serviceAssociateDeptCode", service.getDesc_ploan_basicInfo_serviceAssociateDeptCode(ploan_basicInfo_serviceAssociateDeptCode));
				result.set("ploan_basicInfo_serviceAssociateCode", Util.trim(meta.getMarketingStaff())); //==ploan_obj.getBasicInfo().getServiceAssociateCode()	
				result.set("ploan_basicInfo_relationWithBorrower", ploan_basicInfo_relationWithBorrower);
				result.set("ploan_basicInfo_liveWithBorrower", ploan_basicInfo_liveWithBorrower);
				result.set("ploan_basicInfo_guarantyReason", ploan_basicInfo_guarantyReason);
				result.set("ploan_basicInfo_otherGuarantyReason", ploan_basicInfo_otherGuarantyReason);
			}
			if(ploan_obj!=null && ploan_obj.getLoanInfo()!=null){
				result.set("ploan_loanInfo_notificationMethod", Util.trim(ploan_obj.getLoanInfo().getNotificationMethod()));
			}
			if(ploan_obj!=null && ploan_obj.getLoanInfo()!=null){ //房貸專有欄位					
				result.set("ploan_loanInfo_combine_collateralAddrInfo", ploan_loanInfo_collateralAddrInfo(ploan_obj.getLoanInfo())); //擔保品地址
				
				String ploan_loanInfo_mortgageType = Util.trim(ploan_obj.getLoanInfo().getMortgageType());
				String ploan_loanInfo_nonPrivateUsageType = Util.trim(ploan_obj.getLoanInfo().getNonPrivateUsageType());
				String ploan_loanInfo_privateUsageType = Util.trim(ploan_obj.getLoanInfo().getPrivateUsageType());
				
				if(Util.isNotEmpty(ploan_loanInfo_nonPrivateUsageType)){
					//因欄位長度限制，在 com.bcodetype 的key 值有縮短過
					ploan_loanInfo_nonPrivateUsageType = LMSUtil.getDesc(clsService.get_codeTypeWithOrder("ploan_loanInfo_nonPrvtUsageType"), ploan_loanInfo_nonPrivateUsageType);
				}
				if(Util.isNotEmpty(ploan_loanInfo_privateUsageType)){
					ploan_loanInfo_privateUsageType = LMSUtil.getDesc(clsService.get_codeTypeWithOrder("ploan_loanInfo_privateUsageType"), ploan_loanInfo_privateUsageType);
				}
				result.set("ploan_loanInfo_mortgageType", ploan_loanInfo_mortgageType);
				result.set("ploan_loanInfo_nonPrivateUsageType", ploan_loanInfo_nonPrivateUsageType);
				result.set("ploan_loanInfo_privateUsageType", ploan_loanInfo_privateUsageType);				
			}
			
			//案件狀態
			String docstatus = Util.trim(meta.getDocStatus());
			Map<String, String> _DocStatusNewDescMap = service.get_DocStatusNewDescMap();
			result.set("docStatusDesc", _DocStatusNewDescMap.get(docstatus));
			
			//借款年限-月數
			String month =  Util.trim(meta.getMaturityM());
			if(Util.equals(month, "")){
				result.set("maturityM", "0");
			}
			//補件通知次數
			String message = "";
			if(Util.equals(meta.getDocStatus(), UtilConstants.C122_DocStatus.補件通知)){
				List<C122S01F> c122s01fList = service.findC122S01F_A02(meta.getMainId(), UtilConstants.C122_DocStatus.補件通知);
				message = prop_cls1220m05.getProperty("label.notice.01") + String.valueOf(c122s01fList.size()) + prop_cls1220m05.getProperty("label.notice.02");
			}
			
			//不承做原因
//			String count_A02 = "";
			if(Util.equals(meta.getDocStatus(), UtilConstants.C122_DocStatus.不承作)){
				List<C122S01G> c122s01gList = service.findC122S01G(meta.getMainId(), UtilConstants.C122_DocStatus.不承作);
				if(c122s01gList !=null && c122s01gList.size()>0){
					for(int i=0;i<c122s01gList.size();i++){
						C122S01G c122s01g = c122s01gList.get(i);
						String seqno = String.valueOf(i+1);
						message = message + seqno + "." + c122s01g.getCodeValue() + "-" + c122s01g.getCodeDesc() + "<br/>";
					}
				}
			}
			//取消原因
			if(Util.equals(meta.getDocStatus(), UtilConstants.C122_DocStatus.取消)){
				List<C122S01G> c122s01gList = service.findC122S01G(meta.getMainId(), UtilConstants.C122_DocStatus.取消);
				if(c122s01gList !=null && c122s01gList.size()>0){
					for(int i=0;i<c122s01gList.size();i++){
						C122S01G c122s01g = c122s01gList.get(i);
						String seqno = String.valueOf(i+1);
						message = message + seqno + "." + c122s01g.getCodeValue() + "-" + c122s01g.getCodeDesc() + "<br/>";
					}
				}
			}
			result.set("message", message);
			
			
			break;
		case 2:
			C120S01A c120s01a = service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01a==null){
				c120s01a = new C120S01A(); 
			}
//			LMSUtil.addMetaToResult(result, c120s01a, ClsUtil.C122_C120S01A_COLS);
			LMSUtil.addMetaToResult(result, c120s01a, new String[] {"birthday", "edu", "marry", "child", "mTel", "email"
					, "coCity", "coZip", "coAddr", "fCity", "coTarget", "fZip", "fAddr", "fTarget", "houseStatus"});
			
			result.set("marry", ClsUtil.convert_nb_to_eloan_marry(c120s01a));
			if(Util.equals(IncomType, "1")){
				result.set("ploan_basicInfo_homePhone", Util.trim(c120s01a.getCoTel()));
			}else{
				if(ploan_obj!=null && ploan_obj.getBasicInfo()!=null){
					String homePhone = Util.trim(ploan_obj.getBasicInfo().getHomePhone());
					if(Util.isEmpty(homePhone)){
						// homePhone = prop_cls1220m05.getProperty("ploanObj.homePhone.whenEmpty");
					}
					result.set("ploan_basicInfo_homePhone", homePhone);
				}
			}
			result.set("incomType", IncomType);
			break;	
		case 3:
			C120S01B c120s01b = service.findC120S01B(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01b==null){
				c120s01b = new C120S01B(); 
			}
			LMSUtil.addMetaToResult(result, c120s01b, new String[]{  "juId", "comName"
					, "comCity", "comZip", "comAddr", "comTarget" 
					, "comTel"
					, "jobType1", "jobType2"
					, "seniority", "payAmt", "othAmt", "snrM"	});
			if(ploan_obj!=null && ploan_obj.getBasicInfo()!=null){
				result.set("ploan_basicInfo_titleType", Util.trim(ploan_obj.getBasicInfo().getTitleType()));	
			}
			result.set("snrY", ClsUtility.get_inject_snrY(c120s01b.getSeniority()));
			result.set("incomType", Util.trim(meta.getIncomType()));
			break;	
		case 4:
			C122M01F c122m01f = service.findC122M01F(meta.getMainId());
			LMSUtil.addMetaToResult(result, c122m01f, new String[]{  "introduceSrc", "megaEmpNo", "agntNo", "agntChain", "dealContractNo", "megaCode"
					, "subUnitNo", "subEmpNo", "subEmpNm", "introCustId", "introDupNo", "introCustName", "batchCodeSbr"
					, "introBuildDerName", "introBuildCaseName" 
					, "laaName", "laaMtel", "laaYear", "laaWord", "laaNo", "laaOfficeId", "laaOffice", "laaDesc"
					, "estFlag", "estAddressCity", "estAddressArea", "estAddressVillage", "estAddressStreet", "estAddressSection", "estAddressLane"
					, "estAddressAlley", "estAddressNo", "estAddressFloor", "estAddressLastNo", "estAddressRoom", "estUnitName"
					, "evaMegaEmpNo", "evaMegaEmpName"
					});
			
			String IncomTypeStr = "";
			if(Util.equals(IncomType, "1")){ //人工進件
				IncomTypeStr = prop_cls1220s06.getProperty("C122M01A.IncomType.00");
			}else{//線上進件
				IncomTypeStr = prop_cls1220s06.getProperty("C122M01A.IncomType.01");
			}
			
			result.set("IncomTypeStr", IncomTypeStr);
			result.set("incomType", Util.trim(meta.getIncomType()));
//			result.set("evaMegaEmp", Util.trim(c122m01f.getEvaMegaEmpNo()));
			break;		
		default:
			break;			
		}		
		
		return defaultResult( params, parent_meta, meta, result);
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C122M01A parent_meta, C122M01A meta,
			CapAjaxFormResult result) {		
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		result.set("titInfo", getTitInfo(parent_meta, meta));
		result.set("ploanCaseId", Util.trim(meta.getPloanCaseId()));
		result.set("applyKind", Util.trim(meta.getApplyKind()));
		return result;
	}
	
	private String getTitInfo(C122M01A parent_meta, C122M01A meta) {
		StringBuffer title = new StringBuffer();
		if(parent_meta==null){
			title.append(CapString.trimNull(meta.getCustId()));
			title.append(' ');
			title.append(CapString.trimNull(meta.getDupNo()));
			title.append(' ');
			title.append(CapString.trimNull(meta.getCustName()));
		}else{
			title.append(CapString.trimNull(parent_meta.getCustId()));
			title.append(' ');
			title.append(CapString.trimNull(parent_meta.getDupNo()));
			title.append(' ');
			title.append(CapString.trimNull(parent_meta.getCustName()));
			title.append("（").append(prop_cls1220m05.getProperty("C122M01A.ploanCaseNo")).append("：").append(parent_meta.getPloanCaseId()).append("）");
			title.append(prop_cls1220m05.getProperty("label.ploanRelateCase")).append(" ");
			title.append(CapString.trimNull(meta.getCustId()));
			title.append(' ');
			title.append(CapString.trimNull(meta.getDupNo()));
			title.append(' ');
			title.append(CapString.trimNull(meta.getCustName()));
		}
		
		//if(Util.equals(ClsConstants.C122M01A_IsClosed.X, meta.isClosed())){  ==> isClosed() 回傳 boolean
		if(Util.equals(ClsConstants.C122M01A_StatFlag.已作廢, meta.getStatFlag())){
			title.append("(已作廢)");
		}
		return title.toString();
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}
	
	private IResult _saveAction(PageParameters params, String tempSave)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		boolean checkCredit = Util.notEquals("N", params.getString("checkCredit"));//檢查是否已發動徵信(派案、發動徵信或結案時不需檢查)
		//-------------------
		String KEY = "saveOkFlag";
		StringBuilder IncompleteMsg = new StringBuilder();
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);		
		String page = params.getString(EloanConstants.PAGE);
		C122M01A meta = null;
		try{
			meta = service.getC122M01A_byOid(mainOid);
			String IncomType = meta.getIncomType();
			C122M01F c122m01f = service.findC122M01F(meta.getMainId());
			if(c122m01f == null){ //若無對應的C122M01F，先產出一個空檔
				c122m01f = new C122M01F();
				c122m01f.setMainId(meta.getMainId());
				c122m01f.setCustId(meta.getCustId());
				c122m01f.setDupNo(meta.getDupNo());
				c122m01f.setCustName(meta.getCustName());
				c122m01f.setOwnBrId(meta.getOwnBrId());
				service.save(c122m01f);		
			}
			
			if ("01".equals(page)) {
				CapBeanUtil.map2Bean(params, meta, new String[] {"notifyMemo", "statFlag", "purchaseHouse"
				});				
			}
			if ("02".equals(page)) {
				if(Util.equals("1",IncomType)){ //基本只有人工進件的會給更新，這邊還是做個判斷
					C120S01A c120s01a = new C120S01A();
					c120s01a = service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
					if(c120s01a==null){
						c120s01a = new C120S01A(); 
					}
					CapBeanUtil.map2Bean(params, c120s01a, new String[] {"birthday", "edu", "marry", "child", "mTel", "email"
							, "coCity", "coZip", "coAddr", "fCity", "coTarget", "fZip", "fAddr", "fTarget", "houseStatus"});	
					c120s01a.setCoTel(Util.trim(params.getString("ploan_basicInfo_homePhone"))); //住宅電話寫到通訊電話欄位
					service.save(c120s01a);			
				}		
			}
			if ("03".equals(page)) {
				if(Util.equals("1",IncomType)){ //基本只有人工進件的會給更新，這邊還是做個判斷
					C120S01B c120s01b = service.findC120S01B(meta.getMainId(), meta.getCustId(), meta.getDupNo());
					if(c120s01b==null){
						c120s01b = new C120S01B(); 
					}
					CapBeanUtil.map2Bean(params, c120s01b, new String[] {"juId", "comName", "comCity", "comZip", "comAddr", "comTarget" 
							, "comTel", "jobType1", "jobType2", "seniority", "payAmt", "othAmt", "snrM"});	
					
					//換算年資
					int snrM = 0;
					int snrY = 0;
					if(Util.notEquals(Util.trim(params.getString("snrM")), "")) snrM = Integer.parseInt(params.getString("snrM"));
					if(Util.notEquals(Util.trim(params.getString("snrY")), "")) snrY = Integer.parseInt(params.getString("snrY"));
					BigDecimal seniority = BigDecimal.ZERO;
					seniority = ClsUtility.seniorityYM_encode(snrY,snrM);
					c120s01b.setSeniority(seniority);
					
					if(Util.equals(Util.trim(c120s01b.getJobType2()), "null")){ //前台這個下拉選單，沒有set到會回傳文字的null
						c120s01b.setJobType2("");
					}
					service.save(c120s01b);			
				}		
			}
			
			if ("04".equals(page)) { //進件管理頁面-儲存這邊安全起見綁一下flag存
				
				CapBeanUtil.map2Bean(params, c122m01f, new String[] {"introduceSrc", "megaEmpNo", "agntNo", "agntChain", "dealContractNo", "megaCode"
						, "subUnitNo", "subEmpNo", "subEmpNm", "introCustId", "introDupNo", "introCustName", "batchCodeSbr", "introBuildDerName", "introBuildCaseName" 
						, "laaName", "laaMtel", "laaYear", "laaWord", "laaNo", "laaOfficeId", "laaOffice", "laaDesc"
						, "estFlag", "estAddressCity", "estAddressArea", "estAddressVillage", "estAddressStreet", "estAddressSection", "estAddressLane"
						, "estAddressAlley", "estAddressNo", "estAddressFloor", "estAddressLastNo", "estAddressRoom", "estUnitName"
//						, "evaMegaEmpNo", "evaMegaEmpName"
				});	
				
				String estFlag = Util.trim(params.getString("estFlag"));
				//指定收件行員資料整治
				String evaMegaEmpNo = "";
				String evaMegaEmpName = "";
				if(Util.equals(estFlag, "1")){
					evaMegaEmpNo = Util.trim(params.getString("evaMegaEmp"));
					if(Util.notEquals(evaMegaEmpNo, "")){
						evaMegaEmpName = Util.trim(userInfoService.getUserName(evaMegaEmpNo)); // 查不到會回傳員編
					}
				}
				c122m01f.setEvaMegaEmpNo(evaMegaEmpNo);
				c122m01f.setEvaMegaEmpName(evaMegaEmpName);
				
				//組合擔保品座落位置
				String estAddress = "";
				if(Util.equals(estFlag, "4")){
					estAddress = cls1220m10formhandler.collateralAddrInfo(params);
				}
				c122m01f.setEstAddress(estAddress);
				
				if(Util.equals(Util.trim(c122m01f.getSubUnitNo()), "null")){ //前台這個下拉選單，沒有set到會回傳文字的null
					c122m01f.setSubUnitNo("");
				}
				if(Util.equals(Util.trim(c122m01f.getEstAddressArea()), "null")){
					c122m01f.setEstAddressArea("");
				}
				if(Util.notEquals(Util.trim(c122m01f.getIntroduceSrc()),"4") ||//不為地政士引介或是其他案要清空地政士重複派案註記
						Util.equals(UtilConstants.C122_ApplyKind.O, meta.getApplyKind())){
					c122m01f.setIsSameMegaEmpLaaCase("");
				}
				
				service.save(c122m01f);
				
				if (Util.equals(Util.trim(c122m01f.getEstFlag()), "1")
						&& Util.isNotEmpty(c122m01f.getSignMegaEmpNo())
						&& Util.equals(Util.trim(c122m01f.getSignMegaEmpNo()), Util.trim(c122m01f.getEvaMegaEmpNo()))) {
					String errorMessage = prop_cls1220m05.getProperty("Message.error.01");
					result.set("errorFlag", true);
					result.set("errorMessage", errorMessage);
				}
			}
			
			//J-112-0006 配合開放待派案也可以看到[徵信]、[補件通知]的button，在儲存時增加檢核
			String docStatus = Util.trim(meta.getDocStatus());
			if(docStatus.startsWith("A") && checkCredit){//為[進件作業]類別且不為派案、發動徵信或結案時 就檢核
				
				List<C122S01H> c122s01hList = service.findC122S01H(meta.getMainId(),
						UtilConstants.C122s01h_flowId.借保人資料);
				if (c122s01hList.size() == 0) { // 資料不存在
					IncompleteMsg.append(
							prop_cls1220m05.getProperty("Message.dataCheck.14"));//尚未執行進件管理之徵信作業
				}
				
			}
			
			//J-112-0006 檢查引介來源，若為[地政士引介]要檢查是否有輸入地政士
			String checkLaaMsg = "";
			if(checkCredit){
				//派案跟徵信前都會檢查地政士有沒有輸入，結案時地政士沒輸入也不會影響，所以比照要不要檢查徵信的時間點去檢查地政士輸入與否
				checkLaaMsg = service.checkIntroduceSrcLaa(meta.getMainId());
			}
			
			if(Util.isNotEmpty(checkLaaMsg)){
				if(Util.isNotEmpty(IncompleteMsg)){
					IncompleteMsg.append("<br/>");
				}
				IncompleteMsg.append(checkLaaMsg);
			}
			//J-112-0006 房貸案+地政士引介案件+已派案 在每次存檔時需重新檢核並更新地政士重複派案註記(因為前後案有可能有調整)
			service.updateIsSameMegaEmpLaaCaseFlag(meta.getOwnBrId() , meta.getMainId());
			
			if(Util.isNotEmpty(IncompleteMsg)){
				result.set("IncompleteMsg", IncompleteMsg.toString());
			}
			
			String statFlag = Util.trim(meta.getStatFlag());
			if(Util.isNotEmpty(statFlag) && !CrsUtil.inCollection(statFlag, new String[]{"0", "1", "2", "5"})){
				meta.setApplyStatus(UtilConstants.C122_ApplyStatus.不承做);
			}
			
			//~~~
			service.save(meta);			
			result.set(KEY, true);
			
		}catch(Exception e){
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}	
		
		result.add(query(params));
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult changeOwnBrId_then_mail(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String newBrNo = Util.trim(params.getString("newBrNo"));
		String memo = Util.trim(params.getString("memo"));
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		if(meta==null){
			throw new CapMessageException("["+mainOid+"]not found", getClass());
		}		
		if(Util.isEmpty(memo)){
			throw new CapMessageException(prop_cls1220v09.getProperty("changeOwnBrId_memo")+"不可空白", getClass());
		}
		if(Util.equals(meta.getOwnBrId(), newBrNo)){
			throw new CapMessageException("未更新分行別", getClass());
		}
		if(Util.equals(meta.getStatFlag(), "2")){
			//C122M01A.statFlag=申貸案件狀態
			//C122M01A.statFlag.applyKindP.2=已核貸
			throw new CapMessageException("不可改派("+prop_cls1220m05.getProperty("C122M01A.statFlag")+"："+prop_cls1220m05.getProperty("C122M01A.statFlag.applyKindE.2")+")", getClass());
		}
		if(LMSUtil.cmpDate(CapDate.addMonth(meta.getApplyTS(), 1), "<", CapDate.getCurrentTimestamp())){
			throw new CapMessageException("不可改派("+prop_cls1220m05.getProperty("C122M01A.applyTS")+"："+TWNDate.toAD(meta.getApplyTS())+")", getClass());			
		}
		
		C122M01C c122m01c = service.changeOwnBrId(meta, newBrNo, memo);
		if(c122m01c!=null){
			service.changeOwnBrId_notifyT1(meta.getPloanCaseId(), meta.getApplyKind(), c122m01c, meta.getCustId(), meta.getCustName());
		}
		
		return result;
	}

	@DomainAuth(value = AuthType.Query , CheckDocStatus = false)
	public IResult run_ploan_update_ploan002_purposeType(PageParameters params)
			throws CapException, JsonParseException, JsonMappingException,
			IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();		
		
		for (String c122m01a_mainId : Util.trim(params.getString("c122m01a_mainId_arr"))
				.split("\\|")) {
			C122M01A c122m01a = service.getC122M01A_byMainId(c122m01a_mainId);
			if(c122m01a==null){
				continue;
			}
			
			if(Util.equals(UtilConstants.C122_ApplyKind.E, c122m01a.getApplyKind())
					|| Util.equals(UtilConstants.C122_ApplyKind.F, c122m01a.getApplyKind())){

				String itemType = "0";
				C122M01B c122m01b = service.getC122M01B_byMainIdItemType(c122m01a_mainId, itemType);
				
				if(Util.equals("PLOAN002", c122m01b.getJsonVoClass())){
					ObjectMapper objectMapper = new ObjectMapper();
					PLOAN002 ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN002.class);
					if(ploan_obj.getLoanInfo()!=null){
						String ploan002_purposeType = Util.trim(ploan_obj.getLoanInfo().getPurposeType());
						c122m01a.setPloan002_purposeType(ploan002_purposeType);
						clsService.daoSave(c122m01a);
						//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
						result.set(c122m01a_mainId, ploan002_purposeType);
					}
				}
			}			
		}	
		return result;		
	}
}	
