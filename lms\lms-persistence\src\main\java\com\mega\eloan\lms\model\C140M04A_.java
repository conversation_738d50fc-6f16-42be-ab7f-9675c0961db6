package com.mega.eloan.lms.model;

import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140M04A database table.
 * </pre>
 * @since  2011/9/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/20,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140M04A.class)
public class C140M04A_  extends RelativeMeta_{
	public static volatile SingularAttribute<C140M04A, String> pcId;
	public static volatile SingularAttribute<C140M04A, String> pcName;
	public static volatile SingularAttribute<C140M04A, String> pcType;
	public static volatile SingularAttribute<C140M04A, String> pcTitle;
	public static volatile SingularAttribute<C140M04A, String> pcSex;
	public static volatile ListAttribute<C140M04A, C140JSON> c140jsons;
	public static volatile SingularAttribute<C140M04A, C140M01A> c140m01a;
//	public static volatile ListAttribute<C140M04A, C140S04A> c140s04as;
//	public static volatile ListAttribute<C140M04A, C140S04B> c140s04bs;
//	public static volatile ListAttribute<C140M04A, C140S04C> c140s04cs;
}
