var button = $("#buttonPanel");

//本年一月跟本年的上個月底
var lastDateStart1;
var lastDateEnd1;
//本年一月跟今年這個月
var lastDateStart2;
var lastDateEnd2;
//上周六
var lastDateEnd3;
//去年
var lastYear;
//下個月的一號跟月底 2013-01-01 2013-01-31
var nextDateStart1;
var nextDateEnd1;
//這個月的一號跟月底 2013-01-01 2013-01-31
var thisDateStart1;
var thisDateEnd1;
var lastDateStart5;

var L9521v01Grid01;
var LMS180R01Grid;
var LMS180R01DetailGrid;
var LMS180R02AGrid;
var LMS180R02ADetail1Grid;
var LMS180R16Grid;
var lastSel;
var LMS180R16DetailGrid;
var LMS180R15Grid;

var fileGrid;
var docType='1';//Vector done:把企個金類型改全域
//================================ START DOCUMENT READY ====================================
$(document).ready(function(){
	hideBtn();
	initData();
	$("#lms9521GridShow01").show();
	$("#buttonPanel").before('<div class=" tit2 color-black" id="searchActionName" name="searchActionName"></div>');
	//初始化欄位
	function initData(){
	    //brank("");
		$.ajax({
	        type: "POST",
	        handler: "lms9521m01formhandler",
	        data: {
	            formAction: "queryReportType"
	        },
	        success: function(obj){
	        	//企金
				$("#searchAction1").setItems({
	                // i18n : i18n.samplehome,
	                item: obj.item,
	                format: "{key}" // ,
	                // value :
	            });
				//個金
				$("#searchAction2").setItems({
	                // i18n : i18n.samplehome,
	                item: obj.clsItem,
	                format: "{key}" // ,
	                // value :
	            });
				
				lastDateStart1 = obj.DATASTARTDATE1;
				lastDateStart2 = obj.DATASTARTDATE2;
				lastDateEnd1 = obj.DATAENDDATE1;
				lastDateEnd2 = obj.DATAENDDATE2;
				lastDateEnd3 = obj.DATAENDDATE3;
				lastYear = obj.LASTYEAR;
				nextDateStart1 = obj.NEXTDATASTARTDATE1
				nextDateEnd1 = obj.NEXTDATAENDDATE1;
				thisDateStart1 = obj.THISDATASTARTDATE1;
				thisDateEnd1 = obj.THISDATAENDDATE1;
				lastDateStart5 = obj.DATASTARTDATE5;
				$("#dataStartDate1").val(lastDateStart1);
				$("#dataStartDate2").val(lastDateStart2);
				$("#dataEndDate1").val(lastDateEnd1);
				$("#dataEndDate2").val(lastDateEnd2);
				$("#year").val(lastYear);
				hideSearchActionData(obj);
	        }
	    });
	}
	//把所有按鈕隱藏起來
	function hideBtn(){
	    $("#btnView").hide();
	    $("#btnPrint").hide();
	    $("#btnSearch").show();
	    $("#btnFilter").hide();
	    $("#btnLongViewMemo").hide();
        $("#btnPullinReport").hide();
	    $("#btnReturnPage").hide();
	}
	//隱藏此人不該顯示的報表
	function hideSearchActionData(obj){
		var unitNo = userInfo ? userInfo.unitNo : "";
		var unitType = userInfo ? userInfo.unitType : "";
		var selecter = $("select#searchAction2");
		if(unitNo == '007'){
			//007：國外部
			$("select#searchAction1 option[value=LMS180R02A]").remove();
			$("select#searchAction1 option[value=LMS180R11]").remove();
			//$("select#searchAction1 option[value=LMS180R12]").remove();
			$("select#searchAction1 option[value=LMS180R13]").remove();
			$("select#searchAction1 option[value=LMS180R14]").remove();
			$("select#searchAction1 option[value=LMS180R15]").remove();
			$("select#searchAction1 option[value=LMS180R16]").remove();
			$("select#searchAction1 option[value=LMS180R17]").remove();
			$("select#searchAction1 option[value=LMS180R18]").remove();
			$("select#searchAction1 option[value=LMS180R30]").remove();
			$("select#searchAction1 option[value=LMS180R32]").remove();
			
			selecter.find("option[value=CLS180R07]").remove();
			selecter.find("option[value=CLS180R08]").remove();
			
			selecter.find("option[value=CLS180R11]").remove();		
		}else if(unitNo == '940' || unitNo == '943'){
			//企金
			$("select#searchAction1 option[value=LMS180R02A]").remove();
			$("select#searchAction1 option[value=LMS180R11]").remove();
			//$("select#searchAction1 option[value=LMS180R12]").remove();
			$("select#searchAction1 option[value=LMS180R13]").remove();
			$("select#searchAction1 option[value=LMS180R14]").remove();
			$("select#searchAction1 option[value=LMS180R15]").remove();
			$("select#searchAction1 option[value=LMS180R16]").remove();
			$("select#searchAction1 option[value=LMS180R17]").remove();
			$("select#searchAction1 option[value=LMS180R18]").remove();
			$("select#searchAction1 option[value=LMS180R30]").remove();
			$("select#searchAction1 option[value=LMS180R32]").remove();
			//個金
			selecter.find("option[value=CLS180R01]").remove();
			selecter.find("option[value=CLS180R02]").remove();
			selecter.find("option[value=CLS180R03]").remove();
			selecter.find("option[value=CLS180R04]").remove();
			if(unitNo == '007'){
				selecter.find("option[value=CLS180R05]").remove();	 // 報案考核表被扣分清單
			}else{
				
			}			
			selecter.find("option[value=CLS180R06]").remove();
			selecter.find("option[value=CLS180R07]").remove();
			selecter.find("option[value=CLS180R08]").remove();
			selecter.find("option[value=CLS180R09]").remove();
			selecter.find("option[value=CLS180R10]").remove();
			selecter.find("option[value=CLS180R11]").remove();			
		
		}else if(unitType == '2'){
			//A：區域授信中心 營運中心
			//企金
			$("select#searchAction1 option[value=LMS180R01]").remove();
			$("select#searchAction1 option[value=LMS161T02]").remove();
			$("select#searchAction1 option[value=LMS180R11]").remove();
			$("select#searchAction1 option[value=LMS180R12]").remove();
			$("select#searchAction1 option[value=LMS180R13]").remove();
			$("select#searchAction1 option[value=LMS180R14]").remove();
			$("select#searchAction1 option[value=LMS180R16]").remove();
			$("select#searchAction1 option[value=LMS180R17]").remove();
			$("select#searchAction1 option[value=LMS180R18]").remove();
			//個金
			selecter.find("option[value=CLS180R01]").remove();
			selecter.find("option[value=CLS180R02]").remove();
			selecter.find("option[value=CLS180R03]").remove();
			selecter.find("option[value=CLS180R04]").remove();
			selecter.find("option[value=CLS180R05]").remove();
			selecter.find("option[value=CLS180R06]").remove();
			selecter.find("option[value=CLS180R07]").remove();
			selecter.find("option[value=CLS180R08]").remove();
			selecter.find("option[value=CLS180R09]").remove();
			selecter.find("option[value=CLS180R10]").remove();
			selecter.find("option[value=CLS180R11]").remove();
			selecter.find("option[value=CLS250R01]").remove();
		}else if(unitNo == '918' || unitNo == '916' || unitNo == '900'){
			//S：企金部 授管處
			//企金
			$("select#searchAction1 option[value=LMS161T02]").remove();
			$("select#searchAction1 option[value=LMS180R10]").remove();
			$("select#searchAction1 option[value=LMS180R05]").remove();
			$("select#searchAction1 option[value=LMS180R30]").remove();
			$("select#searchAction1 option[value=LMS180R32]").remove();
			//個金
			selecter.find("option[value=CLS180R02]").remove();
			selecter.find("option[value=CLS180R03]").remove();
			selecter.find("option[value=CLS180R04]").remove();
			selecter.find("option[value=CLS180R06]").remove();
			selecter.find("option[value=CLS180R07]").remove();
			selecter.find("option[value=CLS180R08]").remove();
			selecter.find("option[value=CLS180R09]").remove();
			selecter.find("option[value=CLS180R10]").remove();
			if(unitNo == '918'|| unitNo == '900'){
				//display 已核准尚未撥款案件報表 CLS180R11
			}else{
				selecter.find("option[value=CLS180R11]").remove();	
			}	
		}else if(unitType == '1' || unitType == '5'){
			//B：一般分行 J簡易型分行
			
			if (userInfo.isOverSea) {
			     //海外分行
				$("select#searchAction1 option[value=LMS180R01]").remove();
				$("select#searchAction1 option[value=LMS161T02]").remove();
				$("select#searchAction1 option[value=LMS180R05]").remove();
				$("select#searchAction1 option[value=LMS180R10]").remove();
				$("select#searchAction1 option[value=LMS180R02A]").remove();
				$("select#searchAction1 option[value=LMS180R11]").remove();
				$("select#searchAction1 option[value=LMS180R12]").remove();
				$("select#searchAction1 option[value=LMS180R13]").remove();
				$("select#searchAction1 option[value=LMS180R14]").remove();
				$("select#searchAction1 option[value=LMS180R15]").remove();
				$("select#searchAction1 option[value=LMS180R16]").remove();
				$("select#searchAction1 option[value=LMS180R17]").remove();
				$("select#searchAction1 option[value=LMS180R18]").remove();
				$("select#searchAction1 option[value=LMS180R18]").remove();
				$("select#searchAction1 option[value=LMS180R19]").remove();
				$("select#searchAction1 option[value=LMS180R19B]").remove();
				$("select#searchAction1 option[value=LMS180R20]").remove();
				$("select#searchAction1 option[value=LMS180R21]").remove();
				$("select#searchAction1 option[value=LMS180R22]").remove();
				$("select#searchAction1 option[value=LMS180R23]").remove();
				$("select#searchAction1 option[value=LMS180R23B]").remove();
				$("select#searchAction1 option[value=LMS180R24]").remove();
				
				//$("select#searchAction1 option[value=LMS180R25]").remove();
				$("select#searchAction1 option[value=LMS180R26]").remove();
				$("select#searchAction1 option[value=LMS180R27]").remove();
				$("select#searchAction1 option[value=LMS180R28]").remove();
				$("select#searchAction1 option[value=LMS180R29]").remove();
				$("select#searchAction1 option[value=LMS180R30]").remove();
				$("select#searchAction1 option[value=LMS180R32]").remove();
				$("select#searchAction1 option[value=LMS180R33]").remove();
				$("select#searchAction1 option[value=LMS180R34]").remove();
				$("select#searchAction1 option[value=LMS180R35]").remove();
				$("select#searchAction1 option[value=LMS180R41]").remove();
				
				selecter.find("option[value=CLS180R01]").remove();
				selecter.find("option[value=CLS180R02]").remove();
				selecter.find("option[value=CLS180R03]").remove();
				selecter.find("option[value=CLS180R04]").remove();
				selecter.find("option[value=CLS180R05]").remove();
				selecter.find("option[value=CLS180R06]").remove();
				selecter.find("option[value=CLS180R07]").remove();
				selecter.find("option[value=CLS180R08]").remove();
				selecter.find("option[value=CLS180R09]").remove();
				selecter.find("option[value=CLS180R10]").remove();
				selecter.find("option[value=CLS180R11]").remove();
				selecter.find("option[value=CLS250R01]").remove();
				selecter.find("option[value=CLS180R17]").remove();
				
				selecter.find("option[value=CLS180R12]").remove();
				selecter.find("option[value=CLS180R13]").remove();
				selecter.find("option[value=CLS180R14]").remove();
				selecter.find("option[value=CLS180R15]").remove();
				selecter.find("option[value=CLS180R16]").remove();
			
			}else {
			    //企金
				$("select#searchAction1 option[value=LMS180R02A]").remove();
	//			$("select#searchAction1 option[value=LMS161T02]").remove();先MARK掉 正式上線要拿掉這個MARK //TODO
				$("select#searchAction1 option[value=LMS180R11]").remove();
				$("select#searchAction1 option[value=LMS180R12]").remove();
				$("select#searchAction1 option[value=LMS180R13]").remove();
				$("select#searchAction1 option[value=LMS180R14]").remove();
				$("select#searchAction1 option[value=LMS180R15]").remove();
				$("select#searchAction1 option[value=LMS180R16]").remove();
				$("select#searchAction1 option[value=LMS180R17]").remove();
				$("select#searchAction1 option[value=LMS180R18]").remove();
				$("select#searchAction1 option[value=LMS180R30]").remove();
				$("select#searchAction1 option[value=LMS180R32]").remove();
				//個金
				selecter.find("option[value=CLS180R07]").remove();
				selecter.find("option[value=CLS180R08]").remove();
				selecter.find("option[value=CLS180R11]").remove();			
			}	
				
			
		}
	}
})
//================================ END DOCUMENT READY ====================================

//================================ START BUTTON SETTING ====================================
button.find("#btnPullinReport").click(function(){
	var rptNo = $("#reportNo").val();
    // 7. 常董會及申報案件統計表
    if (rptNo == 'LMS180R16') {
    	execLMS180R16Data();
    }
}).end().find("#btnFilter").click(function(){
	showL180R02AThickBox();
}).end().find("#btnSearch").click(function(){
	showThickBox();
}).end().find("#btnLongViewMemo").click(function(){
	var rptNo = $("#reportNo").val();
	if(rptNo == 'LMS180R01'){
		showL180R01Detail();
	}else if(rptNo == 'LMS180R02A'){
		if($("#deep").val() == '1'){
			var datas = [];
			var gridview = $("#LMS180R02ADetail1Grid");
	        var ids = gridview.getGridParam('selarrrow');
	        for (var i in ids) {
	        	datas.push(gridview.getRowData(ids[i]).oid);
	        }
	        if (datas.length == 0) {
		        return CommonAPI.showMessage(i18n.lms9521v01["L784S01A.error1"]);
	        }
	        $.ajax({
				handler : "lms9521m01formhandler",
				action : 'remarkMemo',
				data : {
					oids:datas
				},
				success:function(responseData){
					gridview.trigger("reloadGrid");
				}
			});
		}else if($("#deep").val() == '2'){
			showL180R02ADetail();
		}
	}
}).end().find("#btnView").click(function(){
	if(docType=='1')
		showLMS180R16Detail();
	else{
		var selRow = fileGrid.getGridParam('selrow');
		if(selRow)
			download(null,null,fileGrid.getRowData(selRow));
		else
	        return CommonAPI.showMessage(i18n.def["grid_selector"]);
	}
}).end().find("#btnReturnPage").click(function(){
	if(docType=='1'){
		if(userInfo.unitType != 2 && $("#deep").val() == '2'){
			$("#btnPrint").hide();
			$("#" + DOMPurify.sanitize($("#reportNo").val()) + "GridShow").hide();
			$("#btnFilter").hide();
			$("#LMS180R02ADetail1GridShow").show();
			$("#deep").val('1');
			$("#mainId").val($("#mainId918").val());
			$("#areaBranchId").val('');
		}else if($("#deep").val() == '1'){
			$("#LMS180R02ADetail1GridShow").hide();
			hideBtn(DOMPurify.sanitize($("#reportNo").val()) + "GridShow");
		}else{
			hideBtn(DOMPurify.sanitize($("#reportNo").val()) + "GridShow");
		}
	}else{
		hideBtn("fileGridView");
	}
}).end().find("#btnPrint").click(function(){
	if($("#reportNo").val() == 'LMS180R02A'){
		doPrintLMS180R02A();
	}else{
		doPrint($("#reportNo").val());
	}
});
//================================ END BUTTON SETTING ====================================
//================================ START ELEMENT SETTING ====================================
//Vector done
$("input[id=docType]").change(function(){
	docType=DOMPurify.sanitize($("input[name=docType]:checked").val());
	$("tr#searchSelects").find("select").hide().val("");
	$("tr#bgnDateRow").hide();
	$("tr#endDateRow").hide();
	$("tr#dataYMRow").hide();
	$("select#searchAction"+docType).show();
	
	$("#CLS_rptNo_srcDesc").html("&nbsp;");
});

$("select#searchAction2").change(function(){
	//reset
	$("#dataStartDate1").val(lastDateStart1);
	$("#dataEndDate1").val(lastDateEnd1);
	$("#dataStartDate2").val(lastDateStart2);
	$("#dataEndDate2").val(lastDateEnd2);
	//start change
	$("#bgnDateRow").show();
	$("#endDateRow").show();
	//YYYY-MM
	$("div#dataDate3").show();
	$("div#dataDate4").show();
	//YYYY-MM-DD
	$("div#dataDate1").hide();
	$("div#dataDate2").hide();
	
	
	$("tr#typeCLS180R14Row").hide();
	
	switch($("select#searchAction2").val()){
	case '':
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
		break;
	case 'CLS180R06':
		$("#dataDate1").show();
		$("#dataDate2").show();
		$("#dataDate3").hide();
		$("#dataDate4").hide();
	}	
	//上面的寫法, switch 內沒有 default 及 break 
	if( $("select#searchAction2").val()=='CLS180R14'){
		$("tr#typeCLS180R14Row").show();	
	}	
	
	switch($("select#searchAction2").val()){
	case 'CLS180R01':
	case 'CLS180R02':
	case 'CLS180R03':
	case 'CLS180R04':
		$("#CLS_rptNo_srcDesc").html("<span class='text-red'>"+i18n.lms9521v01[$("select#searchAction2").val()+"_srcDesc"]+"</span>");
		break;
	default:
		$("#CLS_rptNo_srcDesc").html("&nbsp;");
		break;
	}
});
//================================ END ELEMENT SETTING ====================================

function getBrNos(idName){
    var choiceBrNos = [];
    $('input:checkbox:checked[id="' + idName + '"]').each(function(i){
    	choiceBrNos.push(this.value);
    });
    return choiceBrNos;
}

//彈出視窗顯示查詢條件
function showL180R02AThickBox(){
	listBranch("listBranch");
	listApprove();
	$("#lms9521thickbox3").thickbox({
        title: i18n.lms9521v01['L180R02A.filter'],
        width: 560,
        height: 340,
        modal: false,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            	if($("#lms9521v01From3").valid()){
            		var choiceBrNos = getBrNos("listBranch");
            	    if ($("#approver").val() == '' && choiceBrNos.length == 0) {
            	        //val.inelbranch=請選分行
            	        return CommonAPI.showMessage(i18n.lms9521v01['L784M01a.error02']);
            	    }
        			LMS180R02AGrid.reload({
        	        	listBranch : choiceBrNos,
        	        	approver : $("#approver").val()
        	        });
            	    
            	}
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });//Ajax
}


function listApprove(){
	$.ajax({
        type: "POST",
        handler: "lms9511m01formhandler",
        data: {
            formAction: "queryApprove",
            mainId: $("#mainId").val()
        },
        success: function(responseData){
            var data = {
                    item: responseData.approvers
                };
            $("#approver").setItems(data);
        }
    });
}

function listBranch(choiceBranch){
	$.ajax({
        type: "POST",
        handler: "lms9521m01formhandler",
        data: {
            formAction: "queryBranch",
            areaBranchId: $("#areaBranchId").val()
        },
        success: function(responseData){
        	//alert(JSON.stringify(obj));
            var data = {
                border: "none",
                size: 3,
                item: responseData.item
            };
            $("#" + choiceBranch).setItems(data);
        }
    });
}


//彈出視窗顯示查詢條件
function showThickBox(){
    $("#lms9521thickbox1").thickbox({
        title: i18n.lms9521v01['L784M01a.newInfo01'],
        width: 560,
        height: 270,
        modal: false,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            	if($("#lms9521v01From1").valid()){
	                var action = $("#searchAction"+docType).val();
	                if (action == "") {
	                    return CommonAPI.confirmMessage(i18n.lms9521v01['L784M01a.error01']);
	                }
	                if(docType=='2' ){
	                	var rptNo = $("#searchAction2").val();
	                }else{
	                	if(!$("#dataStartDate2").val().match(/^\d{4}\-(0?[1-9]|1[0-2])$/)){
		                	return CommonAPI.showMessage(i18n.def['val.date2']);
		        		}else if(!$("#dataEndDate2").val().match(/^\d{4}\-(0?[1-9]|1[0-2])$/)){
		                	return CommonAPI.showMessage(i18n.def['val.date2']);
		        		}
	                }
	                
	                actionDos();
            	}
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });//Ajax
}

//此報表需要做的事情
function actionDos(){
	var rptNos;
	var startDate;
	var endDate;
	rptNos = $("select#searchAction" + docType + " option:selected").val();
    $("#reportNo").val(rptNos);
	//需要取得哪種的日期格式
	if(docType=='2' && rptNos != 'CLS180R06'){
		startDate =$("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
		
		//===========
		if(rptNos == 'CLS180R14'){
			var rdo_typeCLS180R14_val = $("#rdo_typeCLS180R14").val();
			if(rdo_typeCLS180R14_val=="A"){
				rptNos = 'CLS180R14A';
			}else if(rdo_typeCLS180R14_val=="B"){
				rptNos = 'CLS180R14B';
			}else if(rdo_typeCLS180R14_val=="C"){
				rptNos = 'CLS180R14C';
			}else if(rdo_typeCLS180R14_val=="D"){
				rptNos = 'CLS180R14D';
			}		
		}
	}else if(rptNos == 'CLS180R06'){
		startDate =$("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();	
	}else if(rptNos == 'LMS180R11'){
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	}else if(rptNos == 'LMS180R14' || rptNos == 'LMS180R02A'){
		startDate =$("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();
	}else if(rptNos == 'LMS180R17'){
		startDate =$("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	}
//	else if(rptNo == 'LMS180R11'){
//		startDate = $("#dataStartDate2").val();
//		endDate = $("#dataEndDate2").val();
//	}
	else if(rptNos == 'LMS180R16'){
		startDate = $("#year").val();
		endDate = $("#year").val();
	}else if(rptNos == 'LMS180R18'){
		startDate = $("#dataStartDate2").val();
		endDate = $("#dataEndDate2").val();	
	}else{
		startDate = $("#dataStartDate1").val();
		endDate = $("#dataEndDate1").val();
	}
	
	
	if(rptNos=='CLS180R06'){
		goSpeGrid(null, null, {
			rptNo:'CLS180R06'
		});
	}else{
		L9521v01Grid01.reload({
	        dataStartDate: startDate,
	        dataEndDate: endDate,
	        rptNo : rptNos,
	        nowRpt : "N"
	    });
	}
    $.thickbox.close();
}

//返回(到第一個入口頁面)
function hideBtn(hideGridDiv){
    $("#" + hideGridDiv).hide();
    $("#lms9521GridShow01").show();
    $("#btnView").hide();
    $("#btnPrint").hide();
    $("#btnSearch").show();
    $("#btnFilter").hide();
    $("#btnLongViewMemo").hide();
    $("#btnReturnPage").hide();
	$("#deep").val('');
	$("#mainId").val('');
	$("#mainId918").val('');
	$("#reportNo").val('');
    $("#searchActionName").val('');
}

//進入報表GRID顯示
function goSpeGrid(cellvalue, options, rowObject){
	ilog.debug(rowObject);
	var rptNo = rowObject.rptNo;
	if(rptNo.substr(0,3)=="LMS"){
		docType='1';
		$("#btnPrint").show();
		$("#" + rptNo + "GridShow").show();
		if(rptNo == 'LMS180R02A'){
			$("#deep").val('2');
			$("#mainId").val(rowObject.mainId);
			if(userInfo.unitType != '2'){
				$("#areaBranchId").val(rowObject.jingBan);
			}else{
				$("#areaBranchId").val(rowObject.branch);
			}
			$("#btnFilter").show();
		}
	}
	else{
		
		if(rptNo=="CLS180R01"){
			fileGrid.jqGrid("hideCol","updateTime");
			fileGrid.jqGrid("showCol","sendTime");
			fileGrid.jqGrid("showCol","cfrmTime");
			fileGrid.setGridWidth(100);
		}else{
			fileGrid.jqGrid("hideCol","sendTime");
			fileGrid.jqGrid("hideCol","cfrmTime");
			fileGrid.jqGrid("showCol","updateTime");
		}
		docType='2';
		$("#fileGridView").show();
	}
	$("#LMS180R02ADetail1GridShow").hide();
	$("#reportNo").val(rptNo);
	$("#btnReturnPage").show();
	$("#btnSearch").hide();
	$("#lms9521GridShow01").hide();
    $("#searchActionName").val(rowObject.rptName);
    reloadGridAndBtnHandle(rowObject.rptNo,rowObject.mainId);
}

//重新RELOAD GRID
function reloadGridAndBtnHandle(rptNos,rptMainId){
	var unitNo = userInfo ? userInfo.unitNo : "";
	var unitType = userInfo ? userInfo.unitType : "";
	if(rptNos == 'LMS180R01' || rptNos == 'LMS180R02A'){
		if(rptNos == 'LMS180R01'){
			LMS180R01Grid.reload({
	        	mainId : rptMainId,
	            rptNo : rptNos,
	            nowRpt : "N"
	        });
		}else if(rptNos == 'LMS180R02A'){
			LMS180R02AGrid.reload({
	        	mainId : rptMainId,
	            rptNo : rptNos,
	            nowRpt : "N"
	        });
		}
    	if(unitType == '4'){
    	    $("#btnLongViewMemo").show();
    	}
    }else if(rptNos == 'LMS180R16'){
        $("#btnView").show();
        $("#btnPullinReport").show();
        
        LMS180R16Grid.reload({
            	mainId : rptMainId,
                rptNo : rptNos,
                nowRpt : "N"
        });
    }else if(rptNos == 'LMS180R15'){
	        $("#btnView").hide();
	        $("#btnPullinReport").hide();
	        
	        LMS180R15Grid.reload({
	            	mainId : rptMainId,
	                rptNo : rptNos,
	                startDate : dataDate,
	                nowRpt : "Y"
	        });
    }else if(docType=="2"){//Vector done
//    	alert(rptMainId);
    	if(rptNos=="CLS180R06"){
    		fileGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料
                postData: {
                	formAction : "queryFile",
                	bgnDate : $("#dataStartDate1").val(),
                	endDate : $("#dataEndDate1").val(),
                    rptNo : rptNos,
                    useMainId : (rptNos=="CLS180R06")
                },
                search: true
            }).trigger("reloadGrid");
    	}else{
    		fileGrid.jqGrid("setGridParam", {//重新設定grid需要查到的資料
                postData: {
                	formAction : "queryFile",
                    mainId : rptMainId,
                    rptNo : rptNos,
                    useMainId : (rptNos=="CLS180R06")
                },
                search: true
            }).trigger("reloadGrid");
    	}
		$("#btnView").show();
		$("#btnDelete").show();
		$("#btnUpload").show();
		$("#btnCreateReport").hide();
		if(unitType == '4'){
			if(rptNos=="CLS180R01")
				$("#btnLongViewMemo").show();
    	}else{
    	    if(rptNos=="CLS180R01"){
    	    	$("#btnSendDocTypeReport").show();
    	    }
    	    else{
    	    	$("#btnSendDocTypeReport").hide();
    	    }
    	}
	}
}


//進入報表GRID顯示
function goSpeLMS180R02AGrid918(cellvalue, options, rowObject){
	ilog.debug(rowObject);
	var rptNo = rowObject.rptNo;
	$("#LMS180R02ADetail1GridShow").show();
	$("#btnReturnPage").show();
	$("#lms9521GridShow01").hide();
   $("#btnSearch").hide();
   $("#searchActionName").val(rowObject.rptName);
	$("#btnFilter").hide();
   LMS180R02ADetail1Grid.reload({
    	nowRpt : "N",
        rptNo : rowObject.rptNo
   });
}

//下載檔案
function download(cellvalue, options, data){
//     alert(data.reportOidFile); 
    $.capFileDownload({
        handler: "simplefiledwnhandler",
        data: {
            fileOid: data.reportOidFile
        }
    });
}

function doPrintLMS180R02A(){
	$("#lms9521v01From4").reset();
	$("#listBranch2Div").hide();
	listBranch("listBranch2");
	$("#lms9521thickbox4").thickbox({
        title: i18n.lms9521v01['L784M01a.print'],
        width: 560,
        height: 370,
        modal: false,
        align: 'left',
        valign: 'top',
        i18n: i18n.def,
        buttons: {
            "print": function(){
            	var printType2 = $("input[name=printType2]:checked").val();
            	var datas = [];
            	var choiceBrNos
            	if(printType2 == '2'){
            		var gridview = $("#LMS180R02AGrid");
        	        var ids = gridview.getGridParam('selarrrow');
        	        for (var i in ids) {
        	        	datas.push(gridview.getRowData(ids[i]).oid);
        	        }
        	        if (datas.length == 0) {
        		        return CommonAPI.showMessage(i18n.def["grid_selector"]);
        	        }
            	}else if(printType2 == '3' ){
            		var choiceBrNos = getBrNos("listBranch2");
            	    if (choiceBrNos.length == 0) {
            	        //val.inelbranch=請選分行
            	        return CommonAPI.showMessage(i18n.def['val.inelbranch']);
            	    }
            	}
            	$.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        choickBranchs: choiceBrNos,
                        oids : datas,
                        printType : printType2,
                        docKind : $("input[name=docKind]:checked").val(),
                        docType : $("input[name=docType2]:checked").val(),
                        printNote : $("input[name=printNote]:checked").val(),
                        mainId : $("#mainId").val(),
                        areaBranchId : $("#areaBranchId").val(),
                        fileDownloadName: "LMS180R02A.pdf",
                        serviceName: "lms9511r05rptservice"
                    }
                });
            },
            "close": function(){
                $.thickbox.close();
            }
        }
    });//Ajax
}

function changePrintType2(){
	var printType2 = $("input[name=printType2]:checked").val();
	if(printType2 == '3' ){
		$("#listBranch2Div").show();
	}else{
		$("#listBranch2Div").hide();
	}
}


function showYearMonth(){
	var printType = $('input:radio:checked[name="printType"]').val();
	 if (printType == "allByAppr" || printType == "allByApprExcel"){
		$("#startYearMonth").show();
	 }else{
		$("#startYearMonth").hide();
	 }
}

//列印
function doPrint(rptNo){
	if(rptNo == 'LMS180R01' || rptNo == 'LMS180R17'){
		var rowid = LMS180R01Grid.getGridParam('selrow');
	    if (!rowid) {
	        return CommonAPI.showMessage(i18n.lms9521v01["L784S01A.error1"]);
	    }
 		var data = LMS180R01Grid.getRowData(rowid);
		download(null, null, data);
	}else if(rptNo == 'LMS180R15'){
		var rowid = LMS180R15Grid.getGridParam('selrow');
	    if (!rowid) {
	        return CommonAPI.showMessage(i18n.lms9521v01["L784S01A.error1"]);
	    }
 		var data = LMS180R15Grid.getRowData(rowid);
		download(null, null, data);
	}else if(rptNo == 'LMS180R16'){
		var LMS180R16Width = 250;
		// J-111-0329 常董會報告事項彙總及申報案件數統計表可下載成EXCEL檔
		if(userInfo.unitNo == '918' || userInfo.unitNo == "900"){
			$("#LMS180R16For918").show();// 多看得到選項-全行單月Excel
			LMS180R16Width = 350;// 把寬度拉寬一點
		}else{
			$("#LMS180R16For918").hide();// 隱藏選項-全行單月Excel
		}
        var test2 = $("#LMS180R16Print").thickbox({
            title: i18n.lms9521v01['L784M01a.ManageReport'],
            width: LMS180R16Width,
            height: 180,
            align: 'center',
            valign: 'bottom',
            modal: false,
            i18n: i18n.def,
            buttons: {
                "print": function(){
					var printType = $('input:radio:checked[name="printType"]').val();
                	 var yearM = $("#yearM1").val();
                	 var caseDept = $('input:radio:checked[name="caseDept"]').val();
                	 if (printType == "allByAppr" || printType == "allByApprExcel"){
                		 var str = "";
                         if (yearM == "") {
                             //L784M01a.startDate=起始日期
                             str = i18n.lms9521v01["L784M01a.startDate"]
                             //val.ineldate=請輸入年月
                             return CommonAPI.showMessage(str + "" + i18n.def["val.ineldate"]);
                         }
                         if (!yearM.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
                             //val.date2=日期格式錯誤(YYYY-MM)
                             return CommonAPI.showMessage(i18n.def["val.date2"]);
                         }
                		 
                	 }
                	 if (caseDept == ""){
                         var str = "";
                          str = i18n.lms9521v01["L180R02A.docType"];	 //L180R02A.docType=企/個金案件	includeId.selData=請選擇一筆資料!!
                          return CommonAPI.showMessage(str + "" + i18n.def["includeId.selData"]);
                     }
                	 var brId = "";
                	 var tempMainId = "";
                	 if(printType == 'brNo'){
                		 var id = LMS180R16Grid.getGridParam('selrow');
                  	    if (!id) {
                  	        // action_006=請先選擇需「調閱」之資料列
                  	        return CommonAPI.showMessage(i18n.def["action_006"]);
                  	    }
                  		var data = LMS180R16Grid.getRowData(id);
                  		
                  		brId = data.brNo;
                  		tempMainId = data.mainId;
                	 }
                	 
                	 // J-111-0329 常董會報告事項彙總及申報案件數統計表可下載成EXCEL檔
                	 // 這個是下載成EXCEL，其餘保持為PDF
                	 if(printType == "allByApprExcel"){
                		 $.form.submit({
                			 url: __ajaxHandler,
                			 target : "_blank",
                			 data : {
                				 _pa : 'lmsdownloadformhandler',
                				 mainId : $("#mainId").val(),
                				 brNo : brId,
                				 printType : printType,
                				 startDate : yearM,
                				 caseDept : caseDept,
                				 fileDownloadName : 'LMS180R16.xls',
                				 serviceName : "lms9511r06rptservice"
                			 }
                		 });
					}else{
						$.form.submit({
							url: "../simple/FileProcessingService",
							target: "_blank",
							data: {
								mainId: $("#mainId").val(),
								brNo : brId,
								printType : printType,
								startDate : yearM,
								caseDept : caseDept,
								fileDownloadName: "LMS180R16.pdf",
								serviceName: "lms9511r02rptservice"
							}
						});
					}
                },
                "close": function(){
                    $.thickbox.close();
                }// 關閉
            }
        });// thickbox
	}
}

//改變報表下拉式選單時 需要更換的報表時間顯示(企金)
function changeLMSData(){
	//初始化所有的日期(若是有特殊需求請到RPTNO裡面自行修正
	$("#dataStartDate1").val(lastDateStart1);
	$("#dataEndDate1").val(lastDateEnd1);
	$("#dataStartDate2").val(lastDateStart2);
	$("#dataEndDate2").val(lastDateEnd2);
	$("#year").val(lastYear);
	$("#dataDate3").hide();
	$("#dataDate4").hide();
	$("#dataDate1").show();
	$("#dataDate2").show();

	$("#bgnDateRow").show();
	$("#endDateRow").show();
	$("#yearDateRow").hide();
	var rptNo = $("#searchAction" + docType).val();
	
	if(rptNo == 'LMS180R01' || rptNo == 'LMS180R05' || rptNo == 'LMS161T02' || rptNo == 'LMS180R10'
		|| rptNo == 'LMS180R12' || rptNo == 'LMS180R13' || rptNo == 'LMS180R15' || rptNo == 'LMS180R17'
	){
		$("#bgnDateRow").show();
		$("#endDateRow").show();
		$("#yearDateRow").hide();
		$("#dataYMRow").hide();
		if(rptNo == 'LMS180R05'){
			$("#dataStartDate1").val(lastDateStart3);
		}else if(rptNo == 'LMS180R10'){
			$("#dataStartDate1").val(nextDateStart1);
			$("#dataEndDate1").val(nextDateEnd1);
		}else if(rptNo == 'LMS180R12' || rptNo == 'LMS180R13'){
			$("#dataStartDate1").val(lastDateStart4);
			$("#dataEndDate1").val(lastDateEnd4);
		}else if(rptNo == 'LMS180R15'){
			$("#dataStartDate1").val(thisDateStart1);
			$("#dataEndDate1").val(thisDateEnd1);
		}else if(rptNo == 'LMS180R17'){
			$("#dataStartDate1").val(lastDateStart3);
			$("#dataEndDate1").val(lastDateEnd3);
		}
	}else if(rptNo == 'LMS180R11' || rptNo == 'LMS180R14' || rptNo == 'LMS180R02A' || rptNo == 'LMS180R18'){
		$("#dataDate3").show();
		$("#dataDate4").show();
		$("#dataDate1").hide();
		$("#dataDate2").hide();
		if(rptNo == 'LMS180R11' || rptNo == 'LMS180R18'){
			$("#dataStartDate2").val(lastDateStart5);
			$("#dataEndDate2").val(lastDateEnd2);
			$("#bgnDateRow").show();
			$("#endDateRow").show();
			$("#yearDateRow").hide();
			$("#dataYMRow").hide();
		} 
	}else if(rptNo == 'LMS180R16'){
		$("#yearDateRow").show();
		$("#bgnDateRow").hide();
		$("#endDateRow").hide();
	}
}

function showL180R01Detail(){
	var row = $("#LMS180R01Grid").getGridParam('selrow');
    if (!row) {
        return CommonAPI.showMessage(i18n.lms9521v01["L784S01A.error1"]);
    }
    var data = $("#LMS180R01Grid").getRowData(row);
    LMS180R01DetailGrid.reload({
        mainId : data.mainId
    });
    var test2 = $("#LMS180R01GridDetailShow").thickbox({
        title: i18n.lms9521v01['L784M01a.newInfo2a'],
        width: 850,
        height: 510,
        align: 'left',
        // valign : 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "print": function(){
            	$.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        mainId: data.mainId,
                        fileDownloadName: "LMS180R0101.pdf",
                        serviceName: "lms9511r03rptservice"
                    }
                });
            },//
            "close": function(){
                $.thickbox.close();
            }// 關閉
        }
        // bottons
    
    });// thickbox
}

function showL180R02ADetail(){
	var gridview = $("#LMS180R02AGrid");
	var datas = [];
	var ids = gridview.getGridParam('selarrrow');
    for (var i in ids) {
    	datas.push(gridview.getRowData(ids[i]).oid);
    }
    if (datas.length == 0) {
	    return CommonAPI.showMessage(i18n.lms9521v01["L784S01A.error1"]);
    }
    $.ajax({
        handler: "lms9521m01formhandler",
        type: "POST",
        dataType: "json",
        data: {
            formAction: "queryLMSRPT",
            mainId: $("#mainId").val()
        },
        success: function(json){
            $("#hqCheckDate").val(json.hqCheckDate);
            $("#hqCheckMemo").val(json.hqCheckMemo);
            var test3 = $("#LMS180R01GridDetailShow2").thickbox({
                title: i18n.lms9521v01['L784M01a.newInfo2b'],
                width: 560,
                height: 380,
                align: 'left',
                modal: false,
                i18n: i18n.def,
                buttons: {
                    "saveData": function(){
                    	if($("#lms9521v01From2").valid()){
                    		$.ajax({
                                type: "POST",
                                handler: "lms9521m01formhandler",
                                data: $.extend($("#lms9521v01From2").serializeData(), {
                                    formAction: "saveL180R02ARemarkNotes",
                                    oids: datas
                                }),
                                success: function(responseData){
                                	gridview.trigger("reloadGrid");
                                    $("#lms9521v01From2").reset();
                                }
                            });//ajax
                            $.thickbox.close();
                    	}
                    },
                    "close": function(){
                        $.thickbox.close();
                    }// 關閉
                }
            });// thickbox
        }
        
    });// ajax
}

function showLMS180R16Detail(){
	var row = $("#LMS180R16Grid").getGridParam('selrow');
    if (!row) {
        return CommonAPI.showMessage(i18n.lms9521v01["L784S01A.error1"]);
    }
    var data = $("#LMS180R16Grid").getRowData(row);
    LMS180R16DetailGrid.reload({
        mainId : data.mainId,
        brNo : data.brNo
    });
    var test2 = $("#LMS180R16GridDetailShow").thickbox({
        title: i18n.lms9521v01['L784M01a.newInfo2a'] + "-" + data.brName,
        width: 850,
        height: 510,
        align: 'left',
        // valign : 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "print": function(){
            	$.form.submit({
                    url: "../simple/FileProcessingService",
                    target: "_blank",
                    data: {
                        mainId: data.mainId,
                        brNo : data.brNo,
                        fileDownloadName: "LMS180R16.pdf",
                        serviceName: "lms9511r02rptservice"
                    }
                });
            },//
            "close": function(){
                $.thickbox.close();
            }// 關閉
        }
        // bottons
    
    });// thickbox
}

//檢核日期格式YYYY-MM
function checkYM(dateYM){
	if (!dateYM.match(/^\d{4}\-(0?[1-9]|1[0-2])$/)) {
        //val.date2=日期格式錯誤(YYYY-MM)
        return CommonAPI.showMessage(i18n.def["val.date2"]);
    }
	return true;
}


function execLMS180R16Data(){
	// 跳出[執行此功能會至中心主機將「所有分行」當月分資料引進，耗時較久，請問是否確定執行？]彈窗
	$("#LMS180R16MSG").thickbox({
        title: i18n.lms9521v01['L784M01a.newInfo2'],
        width: 350,
        height: 200,
        modal: false,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                $.thickbox.close();
            	var test2 = $("#LMS180R16YM").thickbox({
                    title: i18n.lms9521v01['L784M01a.newInfo1'],
                    width: 250,
                    height: 150,
                    align: 'center',
                    valign: 'bottom',
                    modal: false,
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                        	//起始日期
                            var yearM = $("#yearM").val();
                            if($("#lms9521v01LMS180R16From").valid()){
                            	if(checkYM(yearM)){
                            		$.ajax({
                                        type: "POST",
                                        handler: "lms9511m01formhandler",
                                        data: {
                                            formAction: "importLMS180R16Data",
                                            startDate : yearM
                                        },
                                        success: function(responseData){
                                            // 更新Grid內容
                                        }
                                    });//Ajax
                                    $.thickbox.close();
                            	}
                            }
                        },
                        "close": function(){
                            $.thickbox.close();
                        }// 關閉
                    }
                });// thickbox
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });//thickbox
}



//================================ START GRID ====================================
//顯示現有報表清單(企金+個金)
L9521v01Grid01 = $("#L9521v01Grid01").iGrid({
	localFirst: true,
    handler: 'lms9521gridhandler',
    height: 350, // 設定高度
	rownumbers:true,
	action : "queryLMSRPT",
    colModel: [{
        colHeader: "oid",
        name: 'oid',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "mainId",
        name: 'mainId',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "rptNo",
        name: 'rptNo',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "reportOidFile",
        name: 'reportOidFile',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "branch",
        name: 'branch',
        hidden: true
        // 是否隱藏
    },
//    {
//        colHeader: "jingBan",//暫存營運中心代碼
//        name: 'jingBan',
//        hidden: false
//        // 是否隱藏
//    },
    {
        colHeader: i18n.lms9521v01["LMSRPT.dataDate"], // 資料日期
        align: "center",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        formatter: 'date',
        formatoptions: {
            srcformat: 'Y-m-d',
            newformat: 'Y-m'
        },
        name: 'dataDate' // col.id
    }, {
        colHeader: i18n.lms9521v01["LMSRPT.rptName"], // 報表名稱
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        formatter : 'click',
        onclick: openDoc001,
        name: 'rptName' // col.id
    }, {
        colHeader: i18n.lms9521v01["LMSRPT.updateTime"], // 建立時間
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        formatter: 'date',
        formatoptions: {
            srcformat: 'Y-m-d H:i:s',
            newformat: 'Y-m-d H:i:s'
        },
        name: 'updateTime' // col.id
    }],
 	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
 		var data = L9521v01Grid01.getRowData(rowid);
 		openDoc001(null,null,data);
 	}
});

function openDoc001(cellvalue, options, data){
	if((data.rptNo == 'LMS180R01' && data.branch == '918') || data.rptNo == 'LMS180R02A' || data.rptNo == 'LMS180R15' || data.rptNo == 'LMS180R16' || data.rptNo == 'CLS180R01' 
			|| data.rptNo == 'CLS180R02' || data.rptNo == 'CLS180R03' || data.rptNo == 'CLS180R04' || data.rptNo == 'CLS180R11'){
		if(data.rptNo == 'LMS180R16'){
		    $("#yearM1").val(data.dataDate);
	    	$("#yearM").val(data.dataDate);
		}
 		$("#mainId").val(data.mainId);
 		$("#reportNo").val(data.rptNo);
			if(data.branch == '918' && data.rptNo == 'LMS180R02A'){
				$("#deep").val('1');
				$("#mainId918").val(data.mainId);
				goSpeLMS180R02AGrid918(null, null, data);
			}else{
	 			goSpeGrid(null, null, data);
			}
		}else{
			download(null, null, data);
		}
}

//已敘做案件清單
LMS180R01Grid = $("#LMS180R01Grid").iGrid({
	localFirst: true,
    handler: 'lms9521gridhandler',
    height: 350, // 設定高度
    sortname: 'branch', // 預設排序
	rownumbers:true,
	action : "querySpeReport",
	colModel: [{
        name: 'oid',
        hidden: true
        // 是否隱藏
    }, {
        name: 'mainId', // col.id
        hidden: true
        // 是否隱藏
    },{
        colHeader: "reportOidFile",
        name: 'reportOidFile',
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9521v01["L784M01a.ownBrId2"], // 分行名稱
        align: "center",
        width: 90, // 設定寬度
        sortable: true, // 是否允許排序
        formatter : 'click',
        onclick: download,
        name: 'branch' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784M01a.dataDate"], // 資料年月
        align: "center",
        width: 40, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'dataDate', // col.id
        formatter: 'date',
        formatoptions: {
            srcformat: 'Y-m-d',
            newformat: 'Y-m'
        }
    }, {
        colHeader: i18n.lms9521v01["L784M01a.sendTime"], // 分行傳送時間
        align: "center",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'sendTime' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784M01a.cfrmTime"], // 核准日期
        align: "center",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'cfrmTime' // col.id
    }, {
        colHeader: i18n.lms9521v01["LMSRPT.updateTime"], // 建立時間
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        formatter: 'date',
        formatoptions: {
            srcformat: 'Y-m-d H:i:s',
            newformat: 'Y-m-d H:i:s'
        },
        name: 'updateTime' // col.id
    }],
     	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
     		var data = LMS180R01Grid.getRowData(rowid);
			download(null, null, data);
	}
});

LMS180R01DetailGrid = $("#LMS180R01GridDetail").iGrid({
	localFirst: true,
    handler: 'lms9521gridhandler',
    height: 350, // 設定高度
	rownumbers:true,
	multiselect: true,
	action : "queryL784s01a",
    // sortname : 'oid', //預設排序
    //multiselect : true, //是否開啟多選
    colModel: [{
        colHeader: "oid",
        name: 'oid',
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9521v01["L784S01A.endDate"], // 核定日
        align: "center",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'endDate' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784S01A.custId"], // 統一編號
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'custId' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784S01A.custName"], // 戶名
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'custName' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784S01A.cntrNo"], // 額度序號
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        // formatter : 'click',
        // onclick : function,
        name: 'cntrNo' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784S01A.currentApplyAmt"], // 額度(金額)
        align: "right",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'currentApplyAmt', // col.id
        formatter:'currency', 
		formatoptions:
		{
		    thousandsSeparator: ",",
			removeTrailingZero: true,
		    decimalPlaces: 2//小數點到第幾位
		}
    }, {
    	colHeader: i18n.lms9521v01["L784S01A.hqCheckDate"], // 總處核備日
        name: 'hqCheckDate',
        align: "center",
        width: 50,
        sortable: true
    }, {
        colHeader: i18n.lms9521v01["L784S01A.hqCheckMemo"], // 備註
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'hqCheckMemo'
    }]
});

//營運中心已敘做案件清單(918使用的)
LMS180R02ADetail1Grid = $("#LMS180R02ADetail1Grid").iGrid({
	localFirst: true,
    handler: 'lms9521gridhandler',
    height: 350, // 設定高度
	rownumbers:true,
	rowNum: 40,
	multiselect: true,
	hideMultiselect:false,
	action : "querySpeLMS180R02AReport",
	colModel: [{
        name: 'oid',
        hidden: true
        // 是否隱藏
    }, {
        name: 'mainId', // col.id
        hidden: true
        // 是否隱藏
    },{
        colHeader: "rptNo",
        name: 'rptNo',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "rptName",
        name: 'rptName',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "jingBan",//暫存營運中心代碼
        name: 'jingBan',
        hidden: true
        // 是否隱藏
    },{
        colHeader: i18n.lms9521v01["L180R02A.areaBranchId"], // 營運中心名稱
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        formatter : 'click',
        onclick: goSpeGrid,
        name: 'branch' // col.id
    }, {
        colHeader: i18n.lms9521v01["L180R02A.sendTime"], // 傳送授管處時間
        align: "center",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'sendTime' // col.id
    }, {
        colHeader: i18n.lms9521v01["L180R02A.cfrmFlag"], // 核備註記
        align: "center",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'cfrmFlag' // col.id
    }],
 	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
 		var data = LMS180R02ADetail1Grid.getRowData(rowid);
 		goSpeGrid(null, null, data);
 	}
});

//營運中心已敘做案件清單(918 and 營運中心使用的)
LMS180R02AGrid = $("#LMS180R02AGrid").iGrid({
	localFirst: true,
    handler: 'lms9521gridhandler',
    height: 350, // 設定高度
	rownumbers:true,
	rowNum: 40,
	multiselect: true,
	hideMultiselect:false,
	action : "querySpeLMS180R02AReport2",
	colModel: [{
      name: 'oid',
      hidden: true
      // 是否隱藏
  }, {
      name: 'mainId', // col.id
      hidden: true
      // 是否隱藏
  }, {
      colHeader: i18n.lms9521v01["L180R02A.approver"], // 經辦
      align: "left",
      width: 25, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'approver' // col.id
  }, {
      colHeader: i18n.lms9521v01["L180R02A.docKind"], // 授權內外
      align: "center",
      width: 25, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'docKind' // col.id
  }, {
      colHeader: i18n.lms9521v01["L180R02A.docType"], // 企個金
      align: "center",
      width: 15, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'docType' // col.id
  }, {
      colHeader: i18n.lms9521v01["L180R02A.brno"], // 分行名稱
      align: "left",
      width: 30, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'brno' // col.id
  }, {
      colHeader: i18n.lms9521v01["L180R02A.caseNo"], // 案號
      align: "center",
      width: 50, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'caseNo' // col.id
  }, {
      colHeader: i18n.lms9521v01["L784S01A.cntrNo"], // 額度序號 
      align: "center",
      width: 30, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'cntrNo' // col.id
  }, {
      colHeader: i18n.lms9521v01["L180R02A.lnSubject"], // 授信科目
      align: "left",
      width: 40, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'lnSubject' // col.id
  }, {
      colHeader: i18n.lms9521v01["L180R02A.hqCheckDate"], // 備查日期
      align: "center",
      width: 20, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'hqCheckDate' // col.id
  }, {
      colHeader: i18n.lms9521v01["L180R02A.hqCheckMemo"], // 備註
      align: "left",
      width: 15, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'hqCheckMemo' // col.id
  }, {
      colHeader: i18n.lms9521v01["L180R02A.audit"], // 審核不通過
      align: "center",
      width: 10, // 設定寬度
      sortable: true, // 是否允許排序
      name: 'audit' // col.id
  }]
});

LMS180R16Grid = $("#LMS180R16Grid").iGrid({
	localFirst: true,
    rownumbers: true,
    handler: 'lms9521gridhandler',
    height: 350, // 設定高度
    sortname: 'brNo', // 預設排序
	action : "queryL784s07a",
    // multiselect : true,
    colModel: [{
        name: 'brNo', // col.id
        hidden: true
        // 是否隱藏
    },{
        name: 'mainId', // col.id
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9521v01["L784M01a.yearDate"], // 資料年度
        align: "center",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'apprYY' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784M01a.ownBrId2"], // 分行別
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        formatter : 'click',
        onclick: showLMS180R16Detail,
        //formatter: 'click',
        // onclick : BOM,
        name: 'brName' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784M01a.rptType2"], // 資料性質
        align: "center",
        width: 100, // 設定寬度	
        sortable: true, // 是否允許排序
        name: 'caseDept' // col.id
    }],
     	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
     	showLMS180R16Detail();
	}

});

//已敘做案件清單
LMS180R15Grid = $("#LMS180R15Grid").iGrid({
	localFirst: true,
    handler: 'lms9511gridhandler',
    height: 350, // 設定高度
    sortname: 'branch', // 預設排序
	rownumbers:true,
	action : "querySpeLMS180R15Report",
	colModel: [{
        name: 'oid',
        hidden: true
        // 是否隱藏
    }, {
        name: 'mainId', // col.id
        hidden: true
        // 是否隱藏
    },{
        colHeader: "reportOidFile",
        name: 'reportOidFile',
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9521v01["L784M01a.ownBrId2"], // 分行名稱
        align: "center",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        formatter : 'click',
        onclick: download,
        name: 'branch' // col.id
    }, {
        colHeader: i18n.lms9521v01["LMSRPT.dataDate"], // 資料日期
        align: "center",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'dataDate', // col.id
        formatter: 'date',
        formatoptions: {
            srcformat: 'Y-m-d',
            newformat: 'Y-m-d'
        }
    }, {
        colHeader: i18n.lms9521v01["L784M01a.sendTime2"], // 營運中心傳送時間
        align: "center",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'sendTime' // col.id
    }],
     	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
     		var data = LMS180R15Grid.getRowData(rowid);
			download(null, null, data);
	}
});


LMS180R16DetailGrid = $("#LMS180R16GridDetail").iGrid({
	localFirst: true,
    handler: 'lms9521gridhandler',
    height: 350, // 設定高度
    sortname : 'apprMM', //預設排序
    action : "queryL784s07aForTotalMonth",
    colModel: [{
        colHeader: "oid",
        name: 'oid',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "mainId",
        name: 'mainId',
        hidden: true
        // 是否隱藏
    },{
        colHeader: "brNo",
        name: 'brNo',
        hidden: true
        // 是否隱藏
    }, {
        colHeader: i18n.lms9521v01["L784S07A.apprYY"], // 案件核准年度(民國年)
        align: "center",
        width: 50, // 設定寬度
        // formatter : 'click',
        // onclick : function,
        name: 'apprYY' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784S07A.apprMM"], // 案件核准月份
        align: "center",
        width: 50, // 設定寬度
        // formatter : 'click',
        // onclick : function,
        name: 'apprMM' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784S07A.cItem12Rec"], // 逾放展期、轉正常(筆數)
        align: "right",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'cItem12Rec' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784S07A.cItem12Amt"], // 逾放展期、轉正常(金額)
        align: "right",
        width: 50, // 設定寬度
        edittype:'text',
        editoptions: {size:10, maxlength: 15},
        sortable: true, // 是否允許排序
        //formatter : GridFormatter.number['addComma'],
        name: 'cItem12Amt' // col.id
    }, {
        colHeader: i18n.lms9521v01["L784S07A.updater"], // 異動人員
        align: "left",
        width: 50, // 設定寬度
        sortable: true, // 是否允許排序
        name: 'updater' // col.id
    }]
});
//Vector done
fileGrid = $("#fileGrid").iGrid({
    handler: 'lms9521gridhandler',
    height: 350, // 設定高度
    sortname: 'branch', // 預設排序
	rownumbers:true,
	colModel: [{
	    name: 'oid',
	    hidden: true
	    // 是否隱藏
	}, {
	    name: 'mainId', // col.id
	    hidden: true
	    // 是否隱藏
	}, {
	    name: 'reportOidFile', // col.id
	    hidden: true
	    // 是否隱藏
	}, {
	    name: 'rptMainId', // col.id
	    hidden: true
	    // 是否隱藏
	}, {
	    colHeader: i18n.lms9521v01["L784M01a.ownBrId2"], // 分行名稱
	    align: "center",
	    width: 40, // 設定寬度
	    sortable: true, // 是否允許排序
	    name: 'branch' // col.id
	}, {
	    colHeader: i18n.lms9521v01["L784M01a.startDate"], // 起始日
	    align: "center",
	    width: 50, // 設定寬度
	    sortable: true, // 是否允許排序
	    name: 'bgnDate' // col.id
	   /* formatter: 'date',
	    formatoptions: {
	        srcformat: 'Y-m-d',
	        newformat: 'Y-m'
	    }*/
	}, {
	    colHeader: i18n.lms9521v01["L784M01a.endDate"], // 迄日
	    align: "center",
	    width: 50, // 設定寬度
	    sortable: true, // 是否允許排序
	    name: 'endDate' // col.id
	   /* formatter: 'date',
	    formatoptions: {
	        srcformat: 'Y-m-d',
	        newformat: 'Y-m'
	    }*/
	}, {
	    colHeader: i18n.lms9521v01["LMSRPT.rptName"], // 報表名稱
	    align: "center",
	    width: 120, // 設定寬度
	    sortable: true, // 是否允許排序
        formatter : 'click',
        onclick: downloadFile2,
	    name: 'rptName' // col.id
	}, {
	    colHeader: i18n.lms9521v01["L784M01a.sendTime"], // 分行傳送時間
	    align: "center",
	    width: 80, // 設定寬度
	    sortable: true, // 是否允許排序
	    name: 'sendTime' // col.id
	}, {
	    colHeader: i18n.lms9521v01["L784M01a.cfrmTime"], // 核准日期
	    align: "center",
	    width: 80, // 設定寬度
	    sortable: true, // 是否允許排序
	    name: 'cfrmTime' // col.id
	}, {
	    colHeader: i18n.lms9521v01["LMSRPT.updateTime"], // 建立日期
	    align: "center",
	    width: 50, // 設定寬度
	    sortable: true, // 是否允許排序
	    name: 'updateTime', // col.id
	    formatter: 'date',
	    formatoptions: {
	        srcformat: 'Y-m-d hh:MM:ss',
	        newformat: 'Y-m-d'
	    }
	    	
	}, {
        colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
        name: 'fileDesc',
        width: 100,
        align: "center",
        sortable: true
    }],
     	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
     		var data = fileGrid.getRowData(rowid);
     		downloadFile2(null,null,data);
	}
});
function downloadFile2(cellvalue, options, data){
	$.capFileDownload({
        handler: "simplefiledwnhandler",
        data: {
            fileOid: data.oid
        }
    });
}
//================================ END GRID ====================================