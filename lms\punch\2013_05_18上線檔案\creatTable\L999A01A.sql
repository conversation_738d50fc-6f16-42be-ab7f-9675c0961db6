---------------------------------------------------------
-- LMS.L999A01A 企金約據書授權檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999A01A;
CREATE TABLE LMS.L999A01A (
	OID           CHAR(32)      not null,
	MAIN<PERSON>        CHAR(32)      not null,
	PID           CHAR(32)     ,
	OWNUNIT       CHAR(3)       not null,
	OWNER         CHAR(6)      ,
	AUTHTI<PERSON>      TIMESTAMP    ,
	AUTHTYPE      CHAR(1)       not null,
	AUTHUNIT      CHAR(3)       not null,

	constraint P_L999A01A PRIMARY KEY(OID)
) in EL_DATA_4KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL999A01A01;
CREATE UNIQUE INDEX LMS.XL999A01A01 ON LMS.L999A01A   (MAINID, OWNUNIT, AUTHTYPE, AUTHUNIT);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999A01A IS '企金約據書授權檔';
COMMENT ON LMS.L999A01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	PID           IS 'pid', 
	OWNUNIT       IS '授權單位', 
	OWNER         IS '授權人員', 
	AUTHTIME      IS '授權日期', 
	AUTHTYPE      IS '授權類別', 
	AUTHUNIT      IS '被授權單位'
);
