package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.common.model.Meta;

/**
 * <pre>
 * C140M01A model.
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,<PERSON>,new</li>
 *          </ul>
 */
@Entity
@Table(name = "C140M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140M01A extends Meta implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(length = 60)
	private String cesId;

	@Column(length = 12)
	private String cesMode;

	@Column(length = 30)
	private String otherMode;	
	
	@Column(name = "TECHSELECT", length = 1)
	private String techSelect;

	@Column(name = "EQUIPSELECT", length = 1)
	private String equipSelect;

	@Column(name = "CHP12_SELECT", length = 1)
	private String chp12Select;

	@Column(name = "CHP13_SELECT", length = 1)
	private String chp13Select;

	@Column(name = "CHP5_SELECT", length = 1)
	private String chp5Select;

	@Column(name = "CH10_SE4_SELECT", length = 1)
	private String ch10Se4Select;

	@Column(name = "CH6_LSTOCK_INV", length = 1)
	private String ch6LstockInv;

	@Column(name = "CH6_LLAND_INV", length = 1)
	private String ch6LLandInv;
	
	@Column(name = "CH6_1_TITLE", length = 90)
	private String ch61Title;

	@Column(name = "CH6_2_TITLE", length = 90)
	private String ch62Title;

	@Column(name = "CH9_FIN_INFO", length = 1)
	private String ch9FinInfo1;
	
	@Column(name = "CH9_LLAND_INV1", length = 1)
	private String ch9LlandInv1;

	@Column(name = "COM_ID", length = 20)
	private String comId;

	@Column(name = "COM_ID_1", length = 20)
	private String comId1;
	
	@Column(length = 1)
	private String edit;

	@Column(name = "EDIT_MODE1", length = 1)
	private String editMode1;

	@Column(name = "EDIT_MODE2", length = 1)
	private String editMode2;

	@Column(name = "EDIT_MODE3", length = 1)
	private String editMode3;

	@Column(length = 1)
	private String sourceRt;

	@Column(name = "TO_M1", length = 1)
	private String toM1;

	@Column(name = "TO_M2", length = 1)
	private String toM2;

	@Column(name = "TO_M3", length = 1)
	private String toM3;
	
	@Column(name="TO_M4", length=1)
	private String toM4;

	@Column(name = "TO_M5", length = 1)
	private String toM5;

	@Column(length = 20)
	private String typem1;

	@Column(length = 20)
	private String typem2;

	@Column(length = 20)
	private String typem3;
	
	@Column(length=20)
	private String typem4;

	@Column(length = 20)
	private String typem5;

	@Temporal(TemporalType.DATE)
	private Date cesFDate;
	
	@Column(length=1)
	private String normal;

	@Column(name="SE3_SELECT", length=1)
	private String se3Select;
	
	@Column(name="SE4_SELECT", length=1)
	private String se4Select;

	@Column(name="SE5_SELECT", length=1)
	private String se5Select;
	
	@Column(length=1)
	private String isGroupCompany1;
	
	@Column(length=1)
	private String returnMode;
	
	@Column(length=32)
	private String returnMainId;

	/** 徵信資料齊備日期 */
	@Temporal(TemporalType.DATE)
	private Date pDate;
	/** 前次辦理徵信單位 */
	@Column(length = 3)
	private String preUnit;
	/** 前次辦理徵信案號 */
	@Column(length = 60)
	private String preNo;
	/** 前次辦理徵信日期 */
	@Temporal(TemporalType.DATE)
	private Date preDate;
	/** 外部評等 */
	@Column(length = 1)
	private String extFlag;
	/** 聯徵虛擬統編 */
	@Column(length = 20)	
	private String ejcicId;

	/** GAAP or IFRS */
	@Column(length = 1)
	private String gaapFlag;	
	
	// bi-directional many-to-one association to C140A01A
	@ToStringExclude
	@OneToMany(mappedBy = "c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140A01A> c140a01as;

//	// bi-directional many-to-one association to C140A01B
//	@OneToMany(mappedBy = "c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
//	private List<C140A01B> c140a01bs;

	// bi-directional many-to-one association to C140JSON
	@ToStringExclude
	@OneToMany(mappedBy = "c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140JSON> c140jsons;

	// bi-directional many-to-one association to C140M04A
	@ToStringExclude
	@OneToMany(mappedBy = "c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140M04A> c140m04as;
	
	// bi-directional many-to-one association to C140M04B
	@ToStringExclude
	@OneToMany(mappedBy = "c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140M04B> c140m04bs;

	// bi-directional many-to-one association to C140M07A
	@ToStringExclude
	@OneToMany(mappedBy = "c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140M07A> c140m07as;
	
//	// bi-directional many-to-one association to C140S07B
//	@OneToMany(mappedBy = "c140m01a",cascade=CascadeType.PERSIST,fetch=FetchType.LAZY)
//	private List<C140S07B> c140s07bs;
	
//	// bi-directional many-to-one association to C140S07C
//	@OneToMany(mappedBy = "c140m01a",cascade=CascadeType.PERSIST,fetch=FetchType.LAZY)
//	private List<C140S07C> c140s07cs;

	// bi-directional many-to-one association to C140SDSC
	@ToStringExclude
	@OneToMany(mappedBy = "c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140SDSC> c140sdscs;
	
//	//bi-directional many-to-one association to C140S03A
//	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
//	private List<C140S03A> c140s03as;
//	
//	//bi-directional many-to-one association to C140S03A
//	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
//	private List<C140S03B> c140s03bs;
//	
//	//bi-directional many-to-one association to C140S03A
//	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
//	private List<C140S03C> c140s03cs;
//	
//	//bi-directional many-to-one association to C140S03A
//	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
//	private List<C140S03D> c140s03ds;
//	
//	//bi-directional many-to-one association to C140S03A
//	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
//	private List<C140S03E> c140s03es;
	
	//bi-directional many-to-one association to C140S09A
	@ToStringExclude
	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S09A> c140s09as;
	
	//bi-directional many-to-one association to C140S09B
	@ToStringExclude
	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S09B> c140s09bs;
	
	//bi-directional many-to-one association to C140S09C
	@ToStringExclude
	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S09C> c140s09cs;
	
	//bi-directional many-to-one association to C140S09D
	@ToStringExclude
	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S09D> c140s09ds;
	
	//bi-directional many-to-one association to C140S09E
	@ToStringExclude
	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S09E> c140s09es;
	
	//bi-directional many-to-one association to C140S09F
	@ToStringExclude
	@OneToMany(mappedBy="c140m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140S09F> c140s09fs;

	// bi-directional many-to-one association to C140SFFF
	// @OneToMany(mappedBy="c140m01a",cascade=CascadeType.PERSIST,fetch=FetchType.LAZY)
	@Transient
	private List<C140SFFF> c140sfffs;
	
	@Transient
	private List<BRelated> brelateds;		

	public String getCh10Se4Select() {
		return ch10Se4Select;
	}

	public void setCh10Se4Select(String ch10Se4Select) {
		this.ch10Se4Select = ch10Se4Select;
	}

	public String getCh6LstockInv() {
		return ch6LstockInv;
	}

	public void setCh6LstockInv(String ch6LstockInv) {
		this.ch6LstockInv = ch6LstockInv;
	}

	public String getCh61Title() {
		return ch61Title;
	}

	public void setCh61Title(String ch61Title) {
		this.ch61Title = ch61Title;
	}

	public String getCh62Title() {
		return ch62Title;
	}

	public void setCh62Title(String ch62Title) {
		this.ch62Title = ch62Title;
	}

	public String getCh9LlandInv1() {
		return ch9LlandInv1;
	}

	public void setCh9LlandInv1(String ch9LlandInv1) {
		this.ch9LlandInv1 = ch9LlandInv1;
	}

	public String getComId() {
		return comId;
	}

	public void setComId(String comId) {
		this.comId = comId;
	}

	public String getComId1() {
		return comId1;
	}

	public void setComId1(String comId1) {
		this.comId1 = comId1;
	}

	public String getEdit() {
		return edit;
	}

	public void setEdit(String edit) {
		this.edit = edit;
	}

	public String getEditMode1() {
		return editMode1;
	}

	public void setEditMode1(String editMode1) {
		this.editMode1 = editMode1;
	}

	public String getEditMode2() {
		return editMode2;
	}

	public void setEditMode2(String editMode2) {
		this.editMode2 = editMode2;
	}

	public String getEditMode3() {
		return editMode3;
	}

	public void setEditMode3(String editMode3) {
		this.editMode3 = editMode3;
	}

	public String getSourceRt() {
		return sourceRt;
	}

	public void setSourceRt(String sourceRt) {
		this.sourceRt = sourceRt;
	}

	public String getToM2() {
		return toM2;
	}

	public void setToM2(String toM2) {
		this.toM2 = toM2;
	}

	public String getToM3() {
		return toM3;
	}

	public void setToM3(String toM3) {
		this.toM3 = toM3;
	}
	
	public String getToM4() {
		return this.toM4;
	}

	public void setToM4(String toM4) {
		this.toM4 = toM4;
	}

	public String getToM5() {
		return toM5;
	}

	public void setToM5(String toM5) {
		this.toM5 = toM5;
	}

	public String getTypem2() {
		return typem2;
	}

	public void setTypem2(String typem2) {
		this.typem2 = typem2;
	}

	public String getTypem3() {
		return typem3;
	}

	public void setTypem3(String typem3) {
		this.typem3 = typem3;
	}
	
	public String getTypem4() {
		return this.typem4;
	}

	public void setTypem4(String typem4) {
		this.typem4 = typem4;
	}

	public String getTypem5() {
		return typem5;
	}

	public void setTypem5(String typem5) {
		this.typem5 = typem5;
	}

	public List<C140A01A> getC140a01as() {
		return c140a01as;
	}

	public void setC140a01as(List<C140A01A> c140a01as) {
		this.c140a01as = c140a01as;
	}

//	public List<C140A01B> getC140a01bs() {
//		return c140a01bs;
//	}
//
//	public void setC140a01bs(List<C140A01B> c140a01bs) {
//		this.c140a01bs = c140a01bs;
//	}

	public List<C140JSON> getC140jsons() {
		return c140jsons;
	}

	public void setC140jsons(List<C140JSON> c140jsons) {
		this.c140jsons = c140jsons;
	}

	public List<C140M04A> getC140m04as() {
		return c140m04as;
	}

	public void setC140m04as(List<C140M04A> c140m04as) {
		this.c140m04as = c140m04as;
	}
	
	public List<C140M04B> getC140m04bs() {
		return c140m04bs;
	}

	public void setC140m04bs(List<C140M04B> c140m04bs) {
		this.c140m04bs = c140m04bs;
	}
	
	public List<C140M07A> getC140m07as() {
		return c140m07as;
	}

	public void setC140m07as(List<C140M07A> c140m07as) {
		this.c140m07as = c140m07as;
	}

//	public List<C140S07B> getC140s07bs() {
//		return this.c140s07bs;
//	}
//
//	public void setC140s07bs(List<C140S07B> c140s07bs) {
//		this.c140s07bs = c140s07bs;
//	}
	
//	public List<C140S07C> getC140s07cs() {
//		return this.c140s07cs;
//	}
//
//	public void setC140s07cs(List<C140S07C> c140s07cs) {
//		this.c140s07cs = c140s07cs;
//	}

	public List<C140SDSC> getC140sdscs() {
		return c140sdscs;
	}

	public void setC140sdscs(List<C140SDSC> c140sdscs) {
		this.c140sdscs = c140sdscs;
	}

	public List<C140SFFF> getC140sfffs() {
		return c140sfffs;
	}

	public void setC140sfffs(List<C140SFFF> c140sfffs) {
		this.c140sfffs = c140sfffs;
	}
	
//	public List<C140S03A> getC140s03as() {
//		return this.c140s03as;
//	}
//
//	public void setC140s03as(List<C140S03A> c140s03as) {
//		this.c140s03as = c140s03as;
//	}
//	
//	public List<C140S03B> getC140s03bs() {
//		return this.c140s03bs;
//	}
//
//	public void setC140s03bs(List<C140S03B> c140s03bs) {
//		this.c140s03bs = c140s03bs;
//	}
	
//	public List<C140S03C> getC140s03cs() {
//		return this.c140s03cs;
//	}
//
//	public void setC140s03cs(List<C140S03C> c140s03cs) {
//		this.c140s03cs = c140s03cs;
//	}
//	
//	public List<C140S03D> getC140s03ds() {
//		return this.c140s03ds;
//	}
//
//	public void setC140s03ds(List<C140S03D> c140s03ds) {
//		this.c140s03ds = c140s03ds;
//	}
	
//	public List<C140S03E> getC140s03es() {
//		return this.c140s03es;
//	}
//
//	public void setC140s03es(List<C140S03E> c140s03es) {
//		this.c140s03es = c140s03es;
//	}
		
	public List<BRelated> getBrelateds() {
		return brelateds;
	}

	public void setBrelateds(List<BRelated> brelateds) {
		this.brelateds = brelateds;
	}

	public String getChp5Select() {
		return chp5Select;
	}

	public void setChp5Select(String chp5Select) {
		this.chp5Select = chp5Select;
	}

	public String getToM1() {
		return toM1;
	}

	public void setToM1(String toM1) {
		this.toM1 = toM1;
	}

	public String getTypem1() {
		return typem1;
	}

	public void setTypem1(String typem1) {
		this.typem1 = typem1;
	}

	public String getTechSelect() {
		return techSelect;
	}

	public void setTechSelect(String techSelect) {
		this.techSelect = techSelect;
	}

	public String getEquipSelect() {
		return equipSelect;
	}

	public void setEquipSelect(String equipSelect) {
		this.equipSelect = equipSelect;
	}

	public String getChp12Select() {
		return chp12Select;
	}

	public void setChp12Select(String chp12Select) {
		this.chp12Select = chp12Select;
	}

	public String getChp13Select() {
		return chp13Select;
	}

	public void setChp13Select(String chp13Select) {
		this.chp13Select = chp13Select;
	}

	public String getCesId() {
		return cesId;
	}

	public void setCesId(String cesId) {
		this.cesId = cesId;
	}

	public String getCesMode() {
		return cesMode;
	}

	public String[] getCesModes() {
		if (cesMode != null) {
			return cesMode.split(",");
		} else {
			return new String[0];
		}
	}

	public void setCesMode(String cesMode) {
		this.cesMode = cesMode;
	}

	public void setCesMode(String[] cesMode) {
		this.cesMode = CapString.array2String(cesMode);
	}

	public Date getCesFDate() {
		return cesFDate;
	}

	public void setCesFDate(Date cesFDate) {
		this.cesFDate = cesFDate;
	}

	public String getNormal() {
		return normal;
	}

	public void setNormal(String normal) {
		this.normal = normal;
	}
	
	public String getSe3Select() {
		return this.se3Select;
	}

	public void setSe3Select(String se3Select) {
		this.se3Select = se3Select;
	}

	public String getSe4Select() {
		return this.se4Select;
	}

	public void setSe4Select(String se4Select) {
		this.se4Select = se4Select;
	}

	public String getSe5Select() {
		return this.se5Select;
	}

	public void setSe5Select(String se5Select) {
		this.se5Select = se5Select;
	}
	
	public String getIsGroupCompany1() {
		return this.isGroupCompany1;
	}

	public void setIsGroupCompany1(String isGroupCompany1) {
		this.isGroupCompany1 = isGroupCompany1;
	}
	
	public String getReturnMode() {
		return this.returnMode;
	}

	public void setReturnMode(String returnMode) {
		this.returnMode = returnMode;
	}

	public String getReturnMainId() {
		return this.returnMainId;
	}

	public void setReturnMainId(String returnMainId) {
		this.returnMainId = returnMainId;
	}
	
	public List<C140S09A> getC140s09as() {
		return this.c140s09as;
	}

	public void setC140s09as(List<C140S09A> c140s09as) {
		this.c140s09as = c140s09as;
	}
	
	public List<C140S09B> getC140s09bs() {
		return this.c140s09bs;
	}

	public void setC140s09bs(List<C140S09B> c140s09bs) {
		this.c140s09bs = c140s09bs;
	}
	
	public List<C140S09C> getC140s09cs() {
		return this.c140s09cs;
	}

	public void setC140s09cs(List<C140S09C> c140s09cs) {
		this.c140s09cs = c140s09cs;
	}
	
	public List<C140S09D> getC140s09ds() {
		return this.c140s09ds;
	}

	public void setC140s09ds(List<C140S09D> c140s09ds) {
		this.c140s09ds = c140s09ds;
	}
	
	public List<C140S09E> getC140s09es() {
		return this.c140s09es;
	}

	public void setC140s09es(List<C140S09E> c140s09es) {
		this.c140s09es = c140s09es;
	}
	
	public List<C140S09F> getC140s09fs() {
		return this.c140s09fs;
	}

	public void setC140s09fs(List<C140S09F> c140s09fs) {
		this.c140s09fs = c140s09fs;
	}
	

	/**
	 * 徵信資料齊備日期
	 * 
	 * @param pDate
	 *            the pDate to set
	 */
	public void setPDate(Date pDate) {
		this.pDate = pDate;
	}

	/**
	 * 徵信資料齊備日期
	 * 
	 * @return the pDate
	 */
	public Date getPDate() {
		return pDate;
	}

	/**
	 * 前次辦理徵信單位
	 * 
	 * @param preUnit
	 *            the preUnit to set
	 */
	public void setPreUnit(String preUnit) {
		this.preUnit = preUnit;
	}

	/**
	 * 前次辦理徵信單位
	 * 
	 * @return the preUnit
	 */
	public String getPreUnit() {
		return this.preUnit;
	}

	/**
	 * 前次辦理徵信案號
	 * 
	 * @param preNo
	 *            the preNo to set
	 */
	public void setPreNo(String preNo) {
		this.preNo = preNo;
	}

	/**
	 * 前次辦理徵信案號
	 * 
	 * @return the preNo
	 */
	public String getPreNo() {
		return preNo;
	}

	/**
	 * 前次辦理徵信日期
	 * 
	 * @param preDate
	 *            the preDate to set
	 */
	public void setPreDate(Date preDate) {
		this.preDate = preDate;
	}

	/**
	 * 前次辦理徵信日期
	 * 
	 * @return the preDate
	 */
	public Date getPreDate() {
		return preDate;
	}

	/**
	 * (DBU)ch6四、大陸有無
	 * 
	 * @return the ch6LLandInv
	 */
	public String getCh6LLandInv() {
		return ch6LLandInv;
	}

	/**
	 * (DBU)ch6四、大陸有無
	 * 
	 * @param ch6lLandInv
	 *            the ch6LLandInv to set
	 */
	public void setCh6LLandInv(String ch6lLandInv) {
		ch6LLandInv = ch6lLandInv;
	}

	/**
	 * (DBU)ch9是否財務
	 * 
	 * @return the ch9FinInfo
	 */
	public String getCh9FinInfo1() {
		return ch9FinInfo1;
	}

	/**
	 * (DBU)ch9是否財務
	 * 
	 * @param ch9FinInfo
	 *            the ch9FinInfo to set
	 */
	public void setCh9FinInfo1(String ch9FinInfo1) {
		this.ch9FinInfo1 = ch9FinInfo1;
	}

	public String getExtFlag() {
		return extFlag;
	}

	public void setExtFlag(String extFlag) {
		this.extFlag = extFlag;
	}	

	public void setEjcicId(String ejcicId) {
		this.ejcicId = ejcicId;
	}

	public String getEjcicId() {
		return ejcicId;
	}	
	
	public String getOtherMode() {
		return otherMode;
	}

	public void setOtherMode(String otherMode) {
		this.otherMode = otherMode;
	}	

	public String getGaapFlag() {
		return gaapFlag;
	}

	public void setGaapFlag(String gaapFlag) {
		this.gaapFlag = gaapFlag;
	}	
	
}