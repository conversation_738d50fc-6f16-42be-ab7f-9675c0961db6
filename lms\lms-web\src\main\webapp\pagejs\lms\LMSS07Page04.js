var oldOid = "";
var _handler = "";
$(function() {
	gridViewShow();
	A_1_10_4();
	A_1_10_3();
	$("#showSel04").change(function(i){
/*
		$("#showGrid").show();
		$("#gridviewAA").jqGrid("setGridParam", {//重新設定grid需要查到的資料
			postData : {
				formAction:"queryL140m01a2",
				custId : $("#showSel04 option:selected").val()
			},
			search: true
		}).trigger("reloadGrid");
*/
		$("#showBrid").show();
		$("#formSearch").find("#textBrid").val(userInfo.unitNo);
	});
	$("#buttonSearch04").click(function(){				
		if($("#formSearch").valid()){
	        $.ajax({
	            type: "POST",
	            handler: responseJSON["handler"],
	            data: {
	                formAction: "getCustData",
	                custId: $("#formSearch").find("#searchId04").val()
	            },
	            success: function(responseData04){
	                // alert(JSON.stringify(responseData));
				      var selJson04 = {
					       		item : responseData04.selCus,
					       		format : "{value} - {key}",
					       		space: true
					       	};
				      $("#selCus04").setItems(selJson04);
				      $("#showSel04").show();
					  $("#selCus04").show();				
	            }
	        });						
		}
	});
	$("#buttonSearch05").click(function(){				
		if($("#formSearch").valid()){
			$("#showGrid").show();
			$("#gridviewAA").jqGrid("setGridParam", {//重新設定grid需要查到的資料
				postData : {
					formAction:"queryL140m01a2",
					custId : $("#showSel04 option:selected").val(),
					textBrid : $("#textBrid").val()
				},
				page : 1,
				search: true
			}).trigger("reloadGrid");						
		}
	});	
});

function gridViewShow(){
	var gridView = $("#gridviewShow").iGrid({
		handler: 'lms1205gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		postData : {
			formAction : "queryL120s06a",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		sortname: 'custId',
		multiselect: true,hideMultiselect:false,
		// autofit: false,
		autowidth:true,
		colModel: [{
		  colHeader: i18n.lmss07["L1205S07.gridb"],//"本次授信對像"
		  name: 'custId',
		  align:"center",
		  width: 110,
		  sortable: true,
		  formatter : 'click',
		  onclick : openDoc2
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid3"],//"本次科目"
		  name: 'lnSubject',
		  width: 80,
		  sortable: true
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid4"],//"本次額度序號"
		  width: 90,
		  name: 'cntrNo',
		  sortable: true
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid5"],//"對照授信對象"
		  name: 'custId2',
		  width: 110,
		  sortable: true,
		  align:"center"
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid6"],//"對照科目"
		  name: 'lnSubject2',
		  width: 80,
		  sortable: true,
		  align:"right"
		}, {
		  colHeader: i18n.lmss07["L1205S07.grid7"],//"對照額度序號"
		  name: 'cntrNo2',
		  width: 90,
		  sortable: true,
		  align:"left"
		}, {
		  colHeader: "<span class='text-red'>"+i18n.lmss07['L1205S07.grid8']+"</span>",//列印模式
		  name: 'printMode',
		  width:50,
		  sortable: false,
		  align:"center"
		},{
	      colHeader: "&nbsp",//"檢核欄位",
	      name: 'chkYN',
	      width: 20,
	      sortable: false,
	      align:"center"
	    },{
		  name: 'oid',
		  hidden: true
		}],
			ondblClickRow: function(rowid){
				var data = gridView.getRowData(rowid);
				openDoc2(null, null, data);
		}
	});
}
function A_1_10_4(){
	$("#gridviewCC").iGrid({
   	 handler: "lms1405gridhandler",
        height: 200,
        rownumbers:true,
		multiselect : true,
		hideMultiselect : false,
		caption: "&nbsp;",
		hiddengrid : false,
        sortname: 'printSeq|custId|cntrNo',
        sortorder: 'asc|asc|asc',	
        postData : {
				formAction:"queryL140m01a",
				itemType:"1"
			},
        colModel: [{
            colHeader: i18n.lms1405s02["L140M01a.custName"],//借款人名稱
            width: 140,
            name: 'custName',
            sortable: false			 
        }, {
            colHeader: i18n.lms1405s02["L140M01a.cntrNo"],//"額度序號",
            name: 'cntrNo',
            width: 80,
            sortable: false
        }, {
            colHeader: i18n.lms1405s02["L140M01a.cntrNoCom"],//"共用額度序號",
            name: 'commSno',
            width: 80,
            sortable: false
        }, {
            colHeader: "&nbsp;",
            name: 'currentApplyCurr',
            width: 25,
            sortable: false,
			 align:"center"
        }, {
            colHeader: i18n.lms1405s02["L140M01a.moneyAmt"],//現請額度,
            name: 'currentApplyAmt',
            width: 100,
            sortable: false,
			 align:"right",
			 formatter:'currency', 
			 formatoptions:
			   {
			    thousandsSeparator: ",",
				removeTrailingZero: true,
			    decimalPlaces: 2//小數點到第幾位
			    }
        }, {
            colHeader: i18n.lms1405s02["L140M01a.type"],//"性質"
            name: 'proPerty',
            width: 70,
            sortable: false,
            align:"center",
            formatter:proPertyFormatter
        }, {
            colHeader:i18n.lms1405s02["L140M01a.docStatus"], //"文件狀態",
            name: 'docStatus',
            width: 60,
            sortable: false,
			 align:"center"
        }, {
            colHeader: i18n.lms1405s02["L140M01a.branchId"],//"分行別",
            name: 'ownBrId',
            width: 80,
            sortable: false,
			 align:"center"
        },	{
            name: 'oid',
            hidden: true
        },{
            name: 'printSeq',
            hidden: true
        }],
        	ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
    });
}

function A_1_10_3(){
var grid = $("#gridviewAA").iGrid({
  handler: 'lms1205gridhandler',
  //height: 345, //for 15 筆
  height: "230px", //for 10 筆
  //autoHeight: true,
  width: 500,
	postData : {
		formAction : "queryL140m01a2",
		custId : $("#formSearch").find("#searchId").val(),
		textBrid : $("#textBrid").val(),
		rowNum:10
	},
  sortname: 'caseDate',
//  multiselect: true,hideMultiselect:false,
 // autofit: false,
  autowidth:true,
  caption: "&nbsp;",
  hiddengrid : false,
  colModel: [{
      colHeader: i18n.lmss07["L1205S07.grid31"],//"簽案日期"
      name: 'caseDate',
		align:"center",
		width: 80,
      sortable: false
  }, {
      colHeader: i18n.lmss07["L1205S07.grid32"],//"案號",
      name: 'caseNo',
      width: 80,
      sortable: false
  }, {
      colHeader: i18n.lmss07["L1205S07.grid33"],//"額度序號",
      name: 'cntrNo',
      width: 80,
      sortable: false,
		align:"center"
  }, {
      colHeader: i18n.lmss07["L1205S07.grid35"],//"現請額度",
      name: 'currentApplyAmt',
      width: 80,
      sortable: false,
						align:"right"
  }, {
      colHeader: i18n.lmss07["L1205S07.grid36"],//"科目",
      name: 'lnSubject',
      width:50,
      sortable: false,
		align:"center"
  },{
      name: 'mainId',
      hidden: true
  }],
  	ondblClickRow: function(rowid){  
  }
});
}