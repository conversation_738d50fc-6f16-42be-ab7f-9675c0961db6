package com.mega.eloan.lms.eloandb.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface Lms491Service {

	List<Map<String, Object>> LMS491selByBranch(String branch);

	List<?> findSelAll(String branch, Date dateYM);

	Map<String, Object> findSelRemomo(String branch);

	Map<String, Object> findSelCrdate(String branchId, String custId,
			String dupno);

	void saveInsertNew(String branch, String custId, String dupno, Date crDate,
			String updater, String rePortKind, String reMemo, String newFlag,
			Date agntDt, BigDecimal twdRt, String locRt);

	void saveUpdateNew(String remomo, Date crdate, String updater, Date agntDt,
			BigDecimal twdRt, String locRt, String branch, String custId,
			String dupNo);

	List<?> findSelMaincust(String branch, String custId, String dupNo);

	void saveUpdateOld(String remomo, Date crdate, String updater, Date agntDt,
			BigDecimal twdRt, String locRt, String branch, String custId,
			String dupNo);

	Map<String, Object> findNewSelAll(String branch, String custId, String dupNo);

	/**
	 * (個金) select elf491_crdate, char(ELF491_8_1_FLAG)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	public List<?> find491BycustIdData(String custId, String dupNo,
			String branch);

	/**
	 * (個金) select elf491_crdate, char(ELF491_8_1_FLAG)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	public List<?> find491BycustIdDataNoBranch(String custId, String dupNo);

	/**
	 * (個金) select elf491_crdate, char(ELF491_8_1_FLAG) distinct
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @return
	 */
	public List<?> find491DistinctBycustIdData(String custId, String dupNo,
			String branch);

	/**
	 * 新增一筆資料至491
	 * 
	 * @param custId
	 * @param dupNo
	 * @param branch
	 * @param lastRetrialDate
	 * @param shouldReviewDate
	 * @param nCkdMemo
	 * @param nCkdFlag
	 * @param retrialYN
	 * @param nCkdDate
	 * @param updater
	 */
	void insertNew491(String custId, String dupNo, String branch,
			Date lastRetrialDate, Date shouldReviewDate, String nCkdMemo,
			String nCkdFlag, String retrialYN, Date nCkdDate, String updater);

	/**
	 * 新增一筆資料至491(CUSTID="XXXXXXXXX", DUPNO="X")
	 * 
	 * @param branch
	 */
	void insertNew491Data(String branch);

	/**
	 * 更新覆審控制檔(覆審類別為99)
	 * 
	 * @param specifyCycle
	 * @param lastRetrialDate
	 * @param retrialDate
	 * @param reportkind
	 * @param retrialKind
	 * @param shouldReviewDate
	 * @param tFlag1
	 * @param tFlag2
	 * @param updater
	 * @param custId
	 * @param dupNo
	 * @param branch
	 */
	public int update491ByRetrialKind99(String specifyCycle,
			Date lastRetrialDate, Date retrialDate, String reportkind,
			String retrialKind, Date shouldReviewDate, String tFlag1,
			String tFlag2, String updater, String custId, String dupNo,
			String branch);

	/**
	 * 更新覆審控制檔(覆審類別為8-1)
	 * 
	 * @param tuCount
	 * @param updater
	 * @param branch
	 * @return
	 */
	public int update491ByRetrialKind81(String tuCount, String updater,
			String branch);

	/**
	 * insert LMS491(ALL)
	 * @param branch
	 * @param custid
	 * @param dupno
	 * @param maincust
	 * @param llrdate
	 * @param lrdate
	 * @param crdate
	 * @param nckdflag
	 * @param nckddate
	 * @param nckdmemo
	 * @param canceldt
	 * @param updater
	 * @param uckdline
	 * @param uckddt
	 * @param reportkind
	 * @param remomo
	 * @param newflag
	 * @param flag_8_1
	 * @param flagO8_1
	 * @param flagN8_1
	 * @param agntDt
	 * @param agntTwdRt
	 * @param agntLoc_Rt
	 */
	void insertLMS491(String branch, String custid, String dupno,
			String maincust, Date llrdate, Date lrdate, Date crdate,
			String nckdflag, Date nckddate, String nckdmemo, Date canceldt,
			String updater, String uckdline, Date uckddt, String reportkind,
			String remomo, String newflag, String flag_8_1, String flagO8_1,
			String flagN8_1, Date agntDt, BigDecimal agntTwdRt,
			String agntLoc_Rt);

	/**
	 * 刪除  lms491
	 * @param branch
	 * @param custid
	 * @param dupno
	 */
	void deleteLMS491(String branch, String custid, String dupno);

	/**
	 * 不覆審註記切換    更新491
	 * @param crDate
	 * @param nckdFlag
	 * @param nckdDate
	 * @param nckdMemo
	 * @param branch
	 * @param custid
	 * @param dupno
	 */
	void markUpdate491(String nckdFlag, Date nckdDate,
			String nckdMemo, String branch, String custid, String dupno);

}
