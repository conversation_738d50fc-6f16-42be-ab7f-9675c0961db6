package com.mega.eloan.lms.eloandb.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;

@Service("eloandbcmsBASEService")
public class EloandbcmsBASEServiceImpl extends AbstractEloandbCmsJdbc implements
		EloandbcmsBASEService {

	@Override
	public Map<String, Object> getShowDataCollType1For140(String cmsMainId) {
		return this.getJdbc().queryForMap("C101M01.getShowDataCollType1For140",
				new Object[] { cmsMainId });
	}

	@Override
	public List<Map<String, Object>> getlandBuildDataCollType1For140(
			String cmsMainId) {
		return this.getJdbc().queryForList(
				"C101M0304.getlandBuildDataCollType1For140",
				new Object[] { cmsMainId, cmsMainId });
	}

	@Override
	public Map<String, Object> getShowDataFor140(String cmsMainId) {
		return this.getJdbc().queryForMap("C100M01.getShowDataFor140",
				new Object[] { cmsMainId });
	}

	@Override
	public List<Map<String, Object>> getC101M04ByMainId(String cmsMainId) {
		return this.getJdbc().queryForList("C101M04.getDataByMainId",
				new Object[] { cmsMainId });
	}

	@Override
	public List<Map<String, Object>> getC101M03ByMainId(String cmsMainId) {
		return this.getJdbc().queryForList("C101M03.getDataByMainId",
				new Object[] { cmsMainId });
	}

	@Override
	public Map<String, Object> getC101M09ByCityAreaIr48(String cityId,
			String areaId, String Ir48) {
		return this.getJdbc().queryForMap("C101M09.getC101M09ByCityAreaIr48",
				new Object[] { cityId, areaId, Ir48 });
	}

	@Override
	public List<Map<String, Object>> getCmsSetDataByOid(String oid) {
		return getJdbc().queryForList("C100M01.getSetDataByOid",
				new String[] { oid });
	}

	@Override
	public List<Map<String, Object>> getOwnerByMainId(String mainId) {
		return getJdbc().queryForList("C101S03B.getOwnerByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getBuildingByMainId(String mainId) {
		return getJdbc().queryForList("C101M04.getBuildingByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getLandByMainId(String mainId) {
		return getJdbc().queryForList("C101M03.getLandByMainId",
				new String[] { mainId });
	}

	@Override
	public Map<String, Object> getC100M01ByOid(String oid) {
		return getJdbc().queryForMap("C100M01.getByOid", new String[] { oid });
	}

	@Override
	public Map<String, Object> getC101M06ByMainId(String mainId) {
		return getJdbc().queryForMap("C101M06.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC101M05ByMainId(String mainId) {
		return getJdbc().queryForList("C101M05.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC101M08ByMainId(String mainId) {
		return getJdbc().queryForList("C101M08.getByMainId",
				new String[] { mainId });
	}

	@Override
	public Map<String, Object> getC100M01ByMainId(String mainId) {
		return getJdbc().queryForMap("C100M01.getByMainId",
				new String[] { mainId });
	}

	@Override
	public Map<String, Object> getC100M03ByMainId(String mainId) {
		return getJdbc().queryForMap("C100M03.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getTy03ShowNameByMainId(String mainId) {
		return getJdbc().queryForList(
				"C101M01.getTy03ShowNameByMainId",
				new String[] { mainId, mainId, mainId, mainId, mainId, mainId,
						mainId });
	}

	@Override
	public Map<String, Object> getC101S05AByMainId(String mainId) {
		return getJdbc().queryForMap("C101S05A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC101S05ByCustIdDupNo(String custId,
			String dupNo) {
		return getJdbc().queryForListWithMax("C101S05A.getByCustIdDupNo",
				new String[] { custId, dupNo });
	}

	@Override
	public List<Map<String, Object>> getSetDataByCustIdAndBranch(String custId,
			String dupNo, String branch) {
		return getJdbc().queryForList("C100M01.getSetDataByBranchAndCustId",
				new String[] { custId, dupNo, branch });
	}

	@Override
	public List<Map<String, Object>> getC102S01AByMainId(String mainId) {
		return getJdbc().queryForList("C102S01A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01AByMainId(String mainId) {
		return getJdbc().queryForList("C103S01A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01BByMainId(String mainId) {
		return getJdbc().queryForList("C103S01B.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01CByMainId(String mainId) {
		return getJdbc().queryForList("C103S01C.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01DByMainId(String mainId) {
		return getJdbc().queryForList("C103S01D.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01EByMainId(String mainId) {
		return getJdbc().queryForList("C103S01E.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01FByMainId(String mainId) {
		return getJdbc().queryForList("C103S01F.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01GByMainId(String mainId) {
		return getJdbc().queryForList("C103S01G.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01HByMainId(String mainId) {
		return getJdbc().queryForList("C103S01H.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC103S01IByMainId(String mainId) {
		return getJdbc().queryForList("C103S01I.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC104S01ByMainId(String mainId) {
		return getJdbc().queryForList("C104S01.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC105S01AByMainId(String mainId) {
		return getJdbc().queryForList("C105S01A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC105S01BByMainId(String mainId) {
		return getJdbc().queryForList("C105S01B.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC105S01CByMainId(String mainId) {
		return getJdbc().queryForList("C105S01C.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC105S01DByMainId(String mainId) {
		return getJdbc().queryForList("C105S01D.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC106S01AByMainId(String mainId) {
		return getJdbc().queryForList("C106S01A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC107S01AByMainId(String mainId) {
		return getJdbc().queryForList("C107S01A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC108S01AByMainId(String mainId) {
		return getJdbc().queryForList("C108S01A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC109S01AByMainId(String mainId) {
		return getJdbc().queryForList("C109S01A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC110S01AByMainId(String mainId) {
		return getJdbc().queryForList("C110S01A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC110S01BByMainId(String mainId) {
		return getJdbc().queryForList("C110S01B.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC110S01CByMainId(String mainId) {
		return getJdbc().queryForList("C110S01C.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC101S03AByMainId(String mainId) {
		return getJdbc().queryForList("C101S03A.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC101S03BByMainId(String mainId) {
		return getJdbc().queryForList("C101S03B.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getC102S01DByMainId(String mainId) {
		return getJdbc().queryForList("C102S01D.getByMainId",
				new String[] { mainId });
	}

	@Override
	public List<Map<String, Object>> getCMSC101M15ByLOCATE1DESC(
			String LOCATE1DESC, String LOCATE2DESC) {
		return getJdbc().queryForList("CMS.C101M15ByLOCATE1DESC",
				new String[] { LOCATE1DESC, LOCATE2DESC });
	}

	@Override
	public List<Map<String, Object>> getCMSC101M09FindSECTION1(String cityName,
			String areaName) {
		return getJdbc().queryForList("CMS.C101M09FindSECTION1",
				new String[] { cityName, areaName });
	}

	@Override
	public List<Map<String, Object>> getCMSC101M09FindSECTION2(String cityName,
			String areaName, String site3) {
		return getJdbc().queryForList("CMS.C101M09FindSECTION2",
				new String[] { cityName, areaName, site3 });
	}

	@Override
	public List<Map<String, Object>> getC101M15ByLOCATE3CODE(String LOCATE3CODE) {
		return getJdbc().queryForList("CMS.C101M15ByLOCATE3CODE",
				new String[] { LOCATE3CODE });
	}

	@Override
	public List<Map<String, Object>> getCrsCollInfoByCntrNo(String cntrNo) {
		List<Map<String, Object>> r = this.getJdbc().queryForListWithMax(
				"C100S03A.CollInfo", new String[] { cntrNo });
		for (Map<String, Object> map : r) {
			String mainId = Util.trim(map.get("MAINID"));
			String collTyp1 = Util.trim(map.get("COLLTYP1"));
			BigDecimal c100m01_appAmt = (BigDecimal) map.get("C100M01_APPAMT");
			BigDecimal appAmt = BigDecimal.ZERO;
			if (Util.equals("01", collTyp1)) {
				Map<String, Object> c101m01Map = getC100M01ByMainId(mainId);
				String RPTTYPEFLAG = Util.trim(MapUtils.getString(c101m01Map,
						"RPTTYPEFLAG"));
				// J-109-0074擔保品不動產地上權相關修改
				if (Util.equals("1", RPTTYPEFLAG)
						|| Util.equals("2", RPTTYPEFLAG)
						|| Util.equals("3", RPTTYPEFLAG)
						|| Util.equals("4", RPTTYPEFLAG)) {
					String colName = "CASEAMT";
					// 新改版 {1:合併估價, 2:分開估價, 3:新地上權估價(較接近分開估價)}
					Map<String, Object> c101m06Map = getC101M06ByMainId(mainId);
					if (!CollectionUtils.isEmpty(c101m06Map)
							&& c101m06Map.containsKey(colName)
							&& c101m06Map.get(colName) != null) {
						appAmt = (BigDecimal) c101m06Map.get(colName);
					}
				} else {
					// 舊版
					Map<String, Object> c101m06Map = this.getJdbc()
							.queryForMap("C101M06.findC01AppAmt",
									new String[] { mainId });
					if (!CollectionUtils.isEmpty(c101m06Map)) {
						appAmt = (BigDecimal) c101m06Map.get("APPAMT");
					}
				}
			} else if (Util.equals("07", collTyp1)) {
				// still 0
			} else {
				if (c100m01_appAmt != null) {
					appAmt = c100m01_appAmt;
				}
			}
			map.put("APPAMT", appAmt);
		}
		return r;
	}

	@Override
	public List<Map<String, Object>> getCrsCollCntrNoByCustIdDupNo(
			String custId, String dupNo) {
		return this.getJdbc().queryForListWithMax("C100S03A.byCustIdDupNo",
				new String[] { custId, dupNo });
	}

	@Override
	public Map<String, Object> findC101M06ByL140M01A(String mainId) {
		return getJdbc().queryForMap("C101M06.findByL140M01A",
				new Object[] { mainId });
	}
	
	@Override
	public Map<String, Map<String, Object>> getCollateralLocationByDistrict(String district){
		
		Map<String, Map<String, Object>> rtnMap = new HashMap<String, Map<String, Object>>();
		
		List<Map<String, Object>> list = getJdbc().queryForListWithMax("C101M09.C101M15.getCollateralLocationByDistrict", new Object[] { district });
		for(Map<String, Object> map : list){
			String key = CapString.trimNull(map.get("DISTRICT_CODE")) + CapString.trimNull(map.get("SECTION_CODE")) + CapString.trimNull(map.get("VILLAGE_CODE"));
			rtnMap.put(key, map);
		}
		
		return rtnMap;
	}
	
	@Override
	public List<Map<String, Object>> getCollateralBuildStatus(String cmsOid){
		return getJdbc().queryForListWithMax("C101M04.C100M01.getHouseStatus", new Object[] {cmsOid});
	}
	
	@Override
	public List<Map<String, Object>> getCntrnoByComparisonCollateralOwnerId(String l140m01a_mainId, List<String> idList){
		
		if(idList.isEmpty()){
			return new ArrayList<Map<String, Object>>();
		}
		
		String questionMark = "";
		for(int i=0; i<idList.size(); i++){
			questionMark += "?, ";
		}
		questionMark = questionMark.substring(0, questionMark.length()-2);
		
		int paramCount = idList.size() + 1;
		
		Object[] params = new Object[paramCount];
		params[0] = l140m01a_mainId;
		for(int i=1; i<paramCount; i++){
			params[i] = idList.get(i-1);
		}
		
		return getJdbc().queryForListByCustParam("getCntrnoByComparisonCollateralOwnerId", 
												new Object[] { questionMark }, params);
	}
	
	@Override
	public List<Map<String, Object>> getSpecificMoneyTrustCollateralRightsPledgeData(String cmsOid) {
		return getJdbc().queryForList("C100M01.C103S01H.getSpecificMoneyTrustCollateralRightsPledgeData", new Object[] { cmsOid });
	}
	
	@Override
	public List<Map<String, Object>> getCntrnoOfSameAsCollateralOwnerIdInfo(List<String> idList){
		
		if(idList.isEmpty()){
			return new ArrayList<Map<String, Object>>();
		}
		
		String questionMark = "";
		for(int i=0; i<idList.size(); i++){
			questionMark += "?, ";
		}
		questionMark = questionMark.substring(0, questionMark.length()-2);
		
		int paramCount = idList.size();
		
		Object[] params = new Object[paramCount];
		for(int i=0; i<paramCount; i++){
			params[i] = idList.get(i);
		}
		
		return getJdbc().queryForListByCustParam("getCntrnoOfSameAsCollateralOwnerIdInfo", 
												new Object[] { questionMark }, params);
	}
	
	@Override
	public Map<String, String> getAllCollateralAreaName(){

		Map<String, String> rtnMap = new HashMap<String, String>();
		
		List<Map<String, Object>> list = getJdbc().queryForListWithMax("CMS.C101M15.getAllCollateralAreaName", new Object[] {});
		for(Map<String, Object> map : list){
			rtnMap.put(CapString.trimNull(map.get("AREAID_DESC2")), CapString.trimNull(map.get("LOCATE1DESC")) + CapString.trimNull(map.get("LOCATE2DESC")));
		}
		
		return rtnMap;
	}
	
	@Override
	public Map<String, Object> getTSumAmtAdjInRealEstateTypeByC100m01Oid(String oid){
		return this.getJdbc().queryForMap("CMS.C101M06.getTSumAmtAdjInRealEstateTypeByC100m01Oid", new String[] { oid });
	}
	
	@Override
	public List<Map<String, Object>> getSpaceDataOfCollateralByCntrNo(String cntrno){
		return this.getJdbc().queryForList("C100M01.C101M03.C100S03A.getSpaceDataOfCollateralByCntrNo", new String[] { cntrno });
	}
	
	@Override
	public List<String> getCntrNoBeenSetByC100m01Oid(String oid){
		
		List<String> rtnList = new ArrayList<String>();
		
		List<Map<String, Object>> list = this.getJdbc().queryForList("C100M01.C100S03A.getCntrNoBeenSetByC100m01Oid", new String[] { oid });
		for(Map<String, Object> map : list){
			rtnList.add(String.valueOf(map.get("CNTRNO")));
		}
		
		return rtnList;
	}
}
