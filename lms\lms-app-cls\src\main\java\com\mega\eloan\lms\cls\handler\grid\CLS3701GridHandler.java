package com.mega.eloan.lms.cls.handler.grid;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.cls.pages.CLS3701M01Page;
import com.mega.eloan.lms.cls.service.CLS3701Service;
import com.mega.eloan.lms.mfaloan.service.MisELF604Service;
import com.mega.eloan.lms.model.C126M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls3701gridhandler")
public class CLS3701GridHandler extends AbstractGridHandler {
	
	@Resource
    CodeTypeService codeTypeService;
	
	@Resource
	SysParameterService sysParameterService;
	
	@Resource
	CLS3701Service cls3701Service;
	
	@Resource
	MisELF604Service misELF604Service;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3701M01Page.class);
	
	@SuppressWarnings("unchecked")
	public CapGridResult queryView(ISearch pageSetting, PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
//		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
//        pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);
        
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "isClosed", "N");
        pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);

        if(Util.isNotEmpty(Util.trim(params.getString("caseBrId")))){
    		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", Util.trim(params.getString("caseBrId")));
    	}
        if (Util.isNotEmpty(Util.trim(params.getString("custId")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", Util.trim(params.getString("custId")));
        }
        if (Util.isNotEmpty(Util.trim(params.getString("agntNo")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "agntNo", Util.trim(params.getString("agntNo")));
        }
        
        Page<? extends GenericBean> page = cls3701Service.findPage(C126M01A.class, pageSetting);

        Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
        formatter.put("agntNo", new CodeTypeFormatter(codeTypeService,
                "L140M01A_agntNo", CodeTypeFormatter.ShowTypeEnum.Desc));        
        formatter.put("statFlag", new CodeTypeFormatter(codeTypeService,
                "C126M01A_statFlag", CodeTypeFormatter.ShowTypeEnum.Desc));
        
        return new CapGridResult(page.getContent(), page.getTotalRow(),formatter);
	}
	@SuppressWarnings("unchecked")
	public CapGridResult queryView2(ISearch pageSetting, PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
//		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
//        pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);
        
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "isClosed", "N");
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", CreditDocStatusEnum.海外_編製中.getCode());
        pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);

        if(Util.isNotEmpty(Util.trim(params.getString("caseBrId")))){
    		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", Util.trim(params.getString("caseBrId")));
    	}
        if (Util.isNotEmpty(Util.trim(params.getString("custId")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", Util.trim(params.getString("custId")));
        }
        if (Util.isNotEmpty(Util.trim(params.getString("agntNo")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "agntNo", Util.trim(params.getString("agntNo")));
        }
        
        Page<? extends GenericBean> page = cls3701Service.findPage(C126M01A.class, pageSetting);

        Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
        formatter.put("agntNo", new CodeTypeFormatter(codeTypeService,
                "L140M01A_agntNo", CodeTypeFormatter.ShowTypeEnum.Desc));        
        formatter.put("statFlag", new CodeTypeFormatter(codeTypeService,
                "C126M01A_statFlag", CodeTypeFormatter.ShowTypeEnum.Desc));
        
        return new CapGridResult(page.getContent(), page.getTotalRow(),formatter);
	}
	@SuppressWarnings("unchecked")
	public CapGridResult queryView3(ISearch pageSetting, PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
//		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
//        pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);
        
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "isClosed", "N");
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", CreditDocStatusEnum.先行動用_已覆核.getCode());
        pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);

        if(cls3701Service.checkSpecialBank(user.getUnitNo())){
        	if(Util.isNotEmpty(Util.trim(params.getString("filter")))){
        		if(Util.isNotEmpty(Util.trim(params.getString("caseBrId")))){
        			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", Util.trim(params.getString("caseBrId")));
        		}
        	}
        	else{
            	pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
            }
        	if (Util.isNotEmpty(Util.trim(params.getString("applyTS_beg"))) && Util.isNotEmpty(Util.trim(params.getString("applyTS_end")))) {
        		String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
    			String applyTS_end = Util.trim(params.getString("applyTS_end"));
        		String[] reasonStr = { applyTS_beg, applyTS_end };
        		pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "approveTime", reasonStr);
            }
    	}
        else{
        	pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
        }
        if (Util.isNotEmpty(Util.trim(params.getString("custId")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", Util.trim(params.getString("custId")));
        }
        if (Util.isNotEmpty(Util.trim(params.getString("agntNo")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "agntNo", Util.trim(params.getString("agntNo")));
        }
        
        Page<? extends GenericBean> page = cls3701Service.findPage(C126M01A.class, pageSetting);

        Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
        formatter.put("agntNo", new CodeTypeFormatter(codeTypeService,
                "L140M01A_agntNo", CodeTypeFormatter.ShowTypeEnum.Desc));        
        formatter.put("statFlag", new CodeTypeFormatter(codeTypeService,
                "C126M01A_statFlag", CodeTypeFormatter.ShowTypeEnum.Desc));
        
        return new CapGridResult(page.getContent(), page.getTotalRow(),formatter);
	}
	@SuppressWarnings("unchecked")
	public CapGridResult queryClosedCaseView(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
//		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
//        pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);
        
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "isClosed", "Y");
        pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);

        if(Util.isNotEmpty(Util.trim(params.getString("caseBrId")))){
    		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", Util.trim(params.getString("caseBrId")));
    	}
        if (Util.isNotEmpty(Util.trim(params.getString("custId")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", Util.trim(params.getString("custId")));
        }
        if (Util.isNotEmpty(Util.trim(params.getString("agntNo")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "agntNo", Util.trim(params.getString("agntNo")));
        }
        
        Page<? extends GenericBean> page = cls3701Service.findPage(C126M01A.class, pageSetting);

        Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
        formatter.put("agntNo", new CodeTypeFormatter(codeTypeService,
                "L140M01A_agntNo", CodeTypeFormatter.ShowTypeEnum.Desc));        
        formatter.put("statFlag", new CodeTypeFormatter(codeTypeService,
                "C126M01A_statFlag", CodeTypeFormatter.ShowTypeEnum.Desc));
        
        return new CapGridResult(page.getContent(), page.getTotalRow(),formatter);
	}
	
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryELF604(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
        
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
        pageSetting.addSearchModeParameters(SearchMode.EQUALS, "isClosed", "N");
        pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);

        if(Util.isNotEmpty(Util.trim(params.getString("caseBrId")))){
    		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", Util.trim(params.getString("caseBrId")));
    	}
        if (Util.isNotEmpty(Util.trim(params.getString("custId")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", Util.trim(params.getString("custId")));
        }
        if (Util.isNotEmpty(Util.trim(params.getString("agntNo")))) {
            pageSetting.addSearchModeParameters(SearchMode.EQUALS, "agntNo", Util.trim(params.getString("agntNo")));
        }
        String brNo=user.getUnitNo();
        String agntNo=params.getString("agntNo");
        String applyTS_beg = params.getString("applyTS_beg");
        String applyTS_end = params.getString("applyTS_end");
        
        List<Map<String, Object>> page = new ArrayList<Map<String, Object>>();
        if(Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end)){
        	page = misELF604Service.getByBrNoAndLoanDate(brNo,agntNo,applyTS_beg,applyTS_end);
        }

        Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
        formatter.put("agntNo", new CodeTypeFormatter(codeTypeService,
                "L140M01A_agntNo", CodeTypeFormatter.ShowTypeEnum.Desc));        
        formatter.put("statFlag", new CodeTypeFormatter(codeTypeService,
                "C126M01A_statFlag", CodeTypeFormatter.ShowTypeEnum.Desc));
        
        return new CapMapGridResult(page, page.size(),formatter);
	}
	
	public CapMapGridResult queryC126S01A(ISearch pageSetting,
			PageParameters params) throws CapException, JsonParseException,
			JsonMappingException, IOException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<Map<String, Object>> list = this.rpaProcessService.getC126S01ARealtorInfo(mainId);
		return new CapMapGridResult(list, list.size());
	}
}
