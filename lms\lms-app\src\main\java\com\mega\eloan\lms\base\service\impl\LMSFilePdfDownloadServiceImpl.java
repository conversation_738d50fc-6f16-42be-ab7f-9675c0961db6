package com.mega.eloan.lms.base.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;

import org.aspectj.util.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.service.FileDownloadService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * 
 * <AUTHOR>
 * 增加共用下載PDF
 * 
 */
@Service("lmsfiledownloadservice")
public class LMSFilePdfDownloadServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMSFilePdfDownloadServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException, IOException, URISyntaxException {
		String fileName = params.getString("fileId");
		if(Util.equals(fileName, "AU_VEDA_NOTE.pdf") 
				&& Util.equals("en", LocaleContextHolder.getLocale().toString(), true)) {
			fileName = "AU_VEDA_NOTE_en.pdf";
		}
		String tempPath = PropUtil.getProperty("loadFile.dir")
		+ "pdf/" + fileName;
		File file = new File(Thread.currentThread().getContextClassLoader()
				.getResource(tempPath).toURI());
		byte[] bytes = FileUtil.readAsByteArray(file);
		return bytes;
	}
}
