package com.mega.eloan.lms.fms.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringEscapeUtils;
import org.kordamp.json.JSONException;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.gwclient.IdentificationCheckGwReqMessage;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C101M01ADao;
import com.mega.eloan.lms.dao.C101S01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L820M01ADao;
import com.mega.eloan.lms.dao.L820M01BDao;
import com.mega.eloan.lms.dao.L820M01CDao;
import com.mega.eloan.lms.dao.L820M01EDao;
import com.mega.eloan.lms.dao.L820M01SDao;
import com.mega.eloan.lms.dao.L820M01WDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.fms.service.LMS8200Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF515Service;
import com.mega.eloan.lms.mfaloan.service.MisEllnseekservice;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L820M01A;
import com.mega.eloan.lms.model.L820M01B;
import com.mega.eloan.lms.model.L820M01C;
import com.mega.eloan.lms.model.L820M01E;
import com.mega.eloan.lms.model.L820M01S;
import com.mega.eloan.lms.model.L820M01W;
import com.mega.eloan.lms.obsdb.service.ObsdbELF461Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF506Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF515Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 以房養老案件查詢結果維護作業
 * </pre>
 * 
 * @since 2022/12/01
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class LMS8200ServiceImpl extends AbstractCapService implements
		LMS8200Service {
	private static final Logger logger = LoggerFactory
			.getLogger(LMS8200ServiceImpl.class);

	@Resource
	L820M01ADao l820m01aDao;
	
	@Resource
	L820M01BDao l820m01bDao;
	
	@Resource
	L820M01CDao l820m01cDao;
	
	@Resource
	L820M01EDao l820m01eDao;
	
	@Resource
	L820M01SDao l820m01sDao;
	
	@Resource
	L820M01WDao l820m01wDao;
	
	@Resource
	L140M01ADao l140m01aDao;
	
	@Resource
	L140S02ADao l140s02aDao;
	
	@Resource
	L120M01ADao l120m01aDao;
	
	@Resource
	L120M01CDao l120m01cDao;
	
	@Resource
	C120S01ADao c120s01aDao;
	
	@Resource
	C101S01ADao c101s01aDao;
	
	@Resource
	C101M01ADao c101m01aDao;
	
	@Resource
	FlowService flowService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	MisELF515Service misELF515Service;

	@Resource
	MisELF447nService misELF447nService;

	@Resource
	ObsdbELF506Service obsdbELF506Service;

	@Resource
	ObsdbELF515Service obsdbELF515Service;

	@Resource
	DocFileService docFileService;

	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	MisEllnseekservice misEllnseekservice;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	ObsdbELF461Service obsdbELF461Service;

	@Override
	public L820M01A findL820m01aByUniqueKey(String mainId, String custId, String dupNo) {
		return l820m01aDao.findByUniqueKey(mainId, custId, dupNo);
	}
	
	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L820M01A.class) {
			return l820m01aDao.findByMainId01(mainId);
		} else if (clazz == L820M01B.class) {
			return l820m01bDao.findByMainId(mainId);
		} else if (clazz == L820M01C.class) {
			return l820m01cDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L820M01A) {
					if (Util.isEmpty(((L820M01A) model).getOid())) {
						((L820M01A) model).setCreator(user.getUserId());
						((L820M01A) model).setCreateTime(CapDate.getCurrentTimestamp());
						l820m01aDao.save((L820M01A) model);
						flowService.start("LMS8200Flow", ((L820M01A) model).getOid(), user.getUserId(), user.getUnitNo());
					}else{
						((L820M01A) model).setUpdateTime(CapDate.getCurrentTimestamp());
						((L820M01A) model).setUpdater(user.getUserId());
						l820m01aDao.save((L820M01A) model);
					}
				}else if (model instanceof L820M01B) {
					((L820M01B) model).setUpdater(user.getUserId());
					((L820M01B) model).setUpdateTime(CapDate.getCurrentTimestamp());
					l820m01bDao.save((L820M01B) model);
				}else if (model instanceof L820M01C) {  
					l820m01cDao.save((L820M01C) model);
				}else if (model instanceof L820M01E) {
					l820m01eDao.save((L820M01E) model);
				}else if (model instanceof L820M01S) {
					l820m01sDao.save(((L820M01S) model));
				}else if (model instanceof L820M01W) {
					l820m01wDao.save(((L820M01W) model));
				}
				
			}
		}
		
	}

	@Override
	public void autosave(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L820M01A) {
					if (Util.isEmpty(((L820M01A) model).getOid())) {
						((L820M01A) model).setCreator("system");
						((L820M01A) model).setUpdater("system");
						if (Util.isEmpty(((L820M01A) model).getCreateTime())) {
							((L820M01A) model).setCreateTime(CapDate.getCurrentTimestamp());
						}
						l820m01aDao.save((L820M01A) model);
						flowService.start("LMS8200Flow", ((L820M01A) model).getOid(), "system", ((L820M01A) model).getOwnBrId());
					}else{
						((L820M01A) model).setUpdateTime(CapDate.getCurrentTimestamp());
						((L820M01A) model).setUpdater("system");
						l820m01aDao.save((L820M01A) model);
					}
				}else if (model instanceof L820M01B) {
					((L820M01B) model).setUpdater("system");
					((L820M01B) model).setUpdateTime(CapDate.getCurrentTimestamp());
					l820m01bDao.save((L820M01B) model);
				}else if (model instanceof L820M01C) {  
					l820m01cDao.save((L820M01C) model);
				}else if (model instanceof L820M01E) {  
					l820m01eDao.save((L820M01E) model);
				}else if (model instanceof L820M01S) {
					l820m01sDao.save(((L820M01S) model));
				}else if (model instanceof L820M01W) {
					l820m01wDao.save(((L820M01W) model));
				}
				
			}
		}
		
	}
	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L820M01A.class) {
			return l820m01aDao.findPage(search);
		} 
		return null;
	}

	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L820M01A.class) {
			return (T) l820m01aDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public Map<String, String> getData(L820M01A l820m01a) throws CapException {
		Map<String, String> returnMap = new LinkedHashMap<String, String>();
		String mainId = l820m01a.getMainId();
		//String cntrNo = l820m01a.getCntrNo();
		returnMap.put("mainId", mainId);
		
//		if (Util.equals(cntrNo, "")) {
//			Properties pop = MessageBundleScriptCreator.getComponentResource(LMS7800M01Page.class);
//			// 額度序號不得為空白
//			throw new CapMessageException(pop.getProperty("cntrNoEmpty"), getClass());
//		}
		
//		if(newData){
//			List<L180R46A> list = l180r46aDao.findByIndex02(cntrNo);
//			if (list != null && list.size()>0 ) {
//				this.newL140mm6c(mainId, list);
//			}
//		}
		
//		returnMap.put("cntrNo", cntrNo);
		
		return returnMap;
	}

	@Override
	public boolean deleteL820m01as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L820M01A> l820m01as = new ArrayList<L820M01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L820M01A l820m01a = (L820M01A) findModelByOid(L820M01A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l820m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l820m01a.setUpdater(user.getUserId());
			l820m01as.add(l820m01a);
			docLogService.record(l820m01a.getOid(), DocLogEnum.DELETE);
		}
		if (!l820m01as.isEmpty()) {
			l820m01aDao.save(l820m01as);
			flag = true;
		}
		return flag;
	}

	@Override
	public void flowAction(String mainOid, L820M01A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable {
		try {
			mainOid = StringEscapeUtils.escapeSql(mainOid);
			//upMis我先填false
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS8200Flow",((L820M01A) model).getOid(), user.getUserId(), user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O
				inst.setAttribute("result", resultType ? "核准" : "退回");
				if (resultType) {
					if (upMis) {

						L820M01A l820m01a = (L820M01A) findModelByOid(L820M01A.class, mainOid);
						//這邊我應該都不用做
						//if (Util.equals(l820m01a.getUpdateItem2(), "Y")) {
						//	this.upElf506(l820m01a);
						//}
						//if (Util.equals(l820m01a.getUpdateItem3(), "Y")) {
						//	this.upEllnseek(l820m01a);
						//}

					} // upMis
				}
			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

//	@Override
//	public C120S01A findC120m01aByConst(String custId, String dupNo) {
//		List<L140M01A> l140m01as = l140m01aDao.findL140m01aListBycntrNo(cntrNo, custId, dupNo);
//		//Map<String, String> returnMap = new HashMap<String, String>();
//		boolean has67or70 = false;
//		C120S01A returnobj = null;
//		for (L140M01A l140m01a : l140m01as) {
//			// 取得額度明細的產品種類
//			List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a.getMainId());
//			// 找以房養老案件 67以房養老 , 70以房養老 累積型
//			for (L140S02A l140s02a : l140s02as) {
//				if ("67".equals(l140s02a.getProdKind()) || "70".equals(l140s02a.getProdKind())) {
//					has67or70 = true;
//					break;// l140s02a break
//				}
//			}
//			// 以房養老案件 先註解，從ALOAN那邊取得資料就好
//			//if (has67or70) {
//				L120M01C l120m01c = l120m01cDao.findoneByRefMainId(l140m01a.getMainId());
//				List<C120S01A> c120s01as = c120s01aDao.findByMainId(l120m01c.getMainId());
//				if (!c120s01as.isEmpty()) {
//					for (C120S01A c120s01a : c120s01as) {
//						returnobj = c120s01a;
//						return returnobj;
//					}
//				}			
//			//} else {//無案件
//				
//			//}
//
//		}
//		return returnobj;
//	}
	@Override
	public C120S01A findC120m01aByConst(String custId, String dupNo) {
		List<L120M01A> l120m01as = l120m01aDao.findByCustIdDupId(custId, dupNo);
		//Map<String, String> returnMap = new HashMap<String, String>();
		C120S01A returnobj = null;
		for (L120M01A l120m01a : l120m01as) {
			// 取得最新徵信資料
			List<C120S01A> c120s01as = c120s01aDao.findByMainId(l120m01a.getMainId());
			if (!c120s01as.isEmpty()) {
				for (C120S01A c120s01a : c120s01as) {
					returnobj = c120s01a;
					return returnobj;
				}
			}
		}
		return returnobj;
	}
	/**
	 * 建立JSON
	 * 
	 * @param req
	 * @return
	 * @throws Exception
	 */
	@Override
	public byte[] generateJSONObjectForIdCardCheck(IdentificationCheckGwReqMessage req, Calendar currentDateTime) throws Exception {

		Map<String, String> rptVariableMap = null;
		JSONObject json = new JSONObject();

		try {
			String checkIdCardApply = req.getCheckIdCardApply();
			String id = req.getPersonId();
			String applyYYYMMDD = req.getApplyYYYMMDD();
			String queryDateTime = CapDate.convertDateToTaiwanYear(String.valueOf(currentDateTime.get(Calendar.YEAR))) + "年" 
									  + (currentDateTime.get(Calendar.MONTH)+1) + "月" 
									  + currentDateTime.get(Calendar.DAY_OF_MONTH) + "日" 
									  + currentDateTime.get(Calendar.HOUR_OF_DAY) + "時" 
									  + currentDateTime.get(Calendar.MINUTE) + "分" 
									  + currentDateTime.get(Calendar.SECOND) + "秒";
			
			String issuingDate = "已輸入";
			String issuingPlace = "已輸入";
			String applyCodeName = "已輸入";
			if(!"1".equals(checkIdCardApply)){
				issuingDate = "民國" + applyYYYMMDD.substring(0, 3) + "年" + applyYYYMMDD.substring(3, 5) + "月" + applyYYYMMDD.substring(5, 7) + "日";
				CodeType codeType = this.codeTypeService.findByTypeAndDesc2("C101S01A_idCard_siteId_for_API", req.getIssueSiteId());
				issuingPlace = codeType != null ? codeType.getCodeDesc() : "";
				applyCodeName = LMSUtil.getApplyCodeMap().get(req.getApplyCode());
			}
			
			String resultMsg = LMSUtil.getIdCardCheckReturnMessage(id, checkIdCardApply);
			
			rptVariableMap = new LinkedHashMap<String, String>();
			rptVariableMap.put("checkIdCardApply", checkIdCardApply);
			rptVariableMap.put("queryDateTime", queryDateTime);
			rptVariableMap.put("id", id);
			rptVariableMap.put("issuingDate", issuingDate);
			rptVariableMap.put("issuingPlace", issuingPlace);
			rptVariableMap.put("applyCodeName", applyCodeName);
			rptVariableMap.put("resultMsg", resultMsg);
			
			json.putAll(rptVariableMap);

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return json.toString().getBytes("utf-8");

	}

	/**
	 * dataType: 5: 行內身分驗證 ,  6: RPA受監護輔助宣告查詢
	 */
	@Override
	public L820M01S modifyL820M01SForMixRecordData(String mainId,
			String custId, String dupNo, String dataType, String dataStatus,
			byte[] loanCreditDesFile) {

		List<L820M01S> l820m01sList = l820m01sDao.findByList(mainId, custId, dupNo, dataType);
		l820m01sDao.delete(l820m01sList);
		//l820m01sDao.flush();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L820M01S l820m01s = new L820M01S();
		l820m01s.setMainId(mainId);
		l820m01s.setCustId(custId);
		l820m01s.setDupNo(dupNo);
		l820m01s.setDataType(dataType);
		l820m01s.setDataStatus(dataStatus);
		l820m01s.setReportFile(loanCreditDesFile);
		l820m01s.setFileSeq("1");
		Timestamp currentDateTime = CapDate.getCurrentTimestamp();
		l820m01s.setCreateTime(currentDateTime);
		l820m01s.setUpdater(user.getUserId());
		l820m01s.setUpdateTime(currentDateTime);
		l820m01s.setDataCreateTime(currentDateTime);
		// 20200604 新增reportFileType是JSON時，檔案類型設為J
		if (loanCreditDesFile != null && isJSON(loanCreditDesFile)) {
			l820m01s.setReportFileType("J");
		}
		l820m01sDao.save(l820m01s);
		return l820m01s;
	}

	@Override
	public boolean isJSON(byte[] byteArr) {
		String jsonstr = new String(byteArr);
		try {
			JSONObject.fromObject(jsonstr);
		} catch (JSONException ex) {
			return false;
		}
		return true;
	}

	@Override
	public void deleteL820m01bs(List<L820M01B> l820m01bs, boolean isAll) {
		if (isAll) {
			l820m01bDao.delete(l820m01bs);
		} else {
			List<L820M01B> L820M01BsOld = new ArrayList<L820M01B>();
			for (L820M01B l820m01b : l820m01bs) {
				String staffJob = l820m01b.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L820M01BsOld.add(l820m01b);
				}
			}
			l820m01bDao.delete(L820M01BsOld);
		}
		
	}

	@Override
	public void saveL820m01bList(List<L820M01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L820M01B l820m01b : list) {
			l820m01b.setUpdater(user.getUserId());
			l820m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l820m01bDao.save(list);
	}

	@Override
	public L820M01B findL820m01b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
			return l820m01bDao.findByUniqueKey(mainId, branchType, branchId,
					staffNo, staffJob);
	}

	@Override
	public List<L820M01S> findL820M01S_byIdDupDataType(String mainId,
			String custId, String dupNo, String dataType) {
		return l820m01sDao.findByIdDupDataType(mainId, custId, dupNo, dataType);
	}
	
	@Override
	public C101S01A findC101s01aByConst(String ownBrid, String custId, String dupNo) {
		C101S01A returnobj = null;
		//取得該分行徵信主檔
		C101M01A c101m01a = c101m01aDao.findByUniqueKey(null, ownBrid, custId, dupNo);
		if(c101m01a != null){
			returnobj= c101s01aDao.findByUniqueKey(c101m01a.getMainId(), custId, dupNo);
		}
//		List<C101S01A> c101s01as = c101s01aDao.findByCustIdDupId(custId, dupNo);
//		if (!c101s01as.isEmpty()) {
//			for (C101S01A c101s01a : c101s01as) {
//				returnobj = c101s01a;
//				return returnobj;
//			}
//		}
		
		return returnobj;
	}

	@Override
	public L820M01E genL820M01ERecordData(String mainId, String custId, String dupNo) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Timestamp currentDateTime = CapDate.getCurrentTimestamp();
		L820M01E l820m01e = l820m01eDao.findByUniqueKey(mainId, custId, dupNo);
		BigDecimal zero = new BigDecimal(0);
		if(l820m01e == null){
			l820m01e = new L820M01E();
			l820m01e.setCustId(custId);
			l820m01e.setDupNo(dupNo);
			l820m01e.setMainId(mainId);	
			l820m01e.setIn_tr_fu(zero);
			l820m01e.setIn_tr_sc_e(zero);
			l820m01e.setIn_wm_aum(zero);
			l820m01e.setIn_wm_bal(zero);
			l820m01e.setIn_wm_fd(zero);
			l820m01e.setIn_wm_fee(zero);
			l820m01e.setIn_wm_ia(zero);
			l820m01e.setIn_wm_std(zero);
			l820m01e.setDataSearchResult(UtilConstants.DEFAULT.是);//通過
			l820m01e.setCreator(user.getUserId());
			l820m01e.setCreateTime(currentDateTime);
		}
		
		l820m01e.setUpdater(user.getUserId());
		l820m01e.setUpdateTime(currentDateTime);
		return l820m01e;
	}

	@Override
	public List<L820M01E> findL820M01EList_byIdDup(String mainId, String custId,
			String dupNo) {
		return l820m01eDao.findByIndex01(mainId, custId, dupNo);
	}
	@Override
	public L820M01E findL820M01EByUniqueKey(String mainId, String custId, String dupNo) {
		return l820m01eDao.findByUniqueKey(mainId, custId, dupNo);
	}
}
