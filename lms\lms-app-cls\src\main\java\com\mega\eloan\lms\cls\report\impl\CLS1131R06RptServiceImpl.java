package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1131R06RptService;

import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 證券暨期貨違約交割紀錄
 * </pre>
 * 
 * @since 2020/01/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/01/20, EL08034
 *          </ul>
 */
@Service("cls1131r06rptservice")
public class CLS1131R06RptServiceImpl implements FileDownloadService,
		CLS1131R06RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1131R06RptServiceImpl.class);

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
//			 baos = (ByteArrayOutputStream) this.generateReport(params);
//			 return baos.toByteArray();
			// J-109-0148 改為產生JSONObject
			return this.generateJSONObject(params);
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {

		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;

		try {
			locale = LMSUtil.getLocale();
			rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator generator = new ReportGenerator(
					"report/cls/CLS1131R06_" + locale.toString() + ".rpt");

			List<Map<String, String>> list = new ArrayList<Map<String, String>>();
			List<Map<String, String>> eaiCURIQ01_list = (List<Map<String, String>>) params
					.get("eaiCURIQ01_list");
			for (Map<String, String> eaiMap : eaiCURIQ01_list) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("CommonBean1.field01", (String) eaiMap.get("IDNO_CURJ"));// 統一編號
				map.put("CommonBean1.field02", (String) eaiMap.get("NAME_CURJ"));// 客戶名稱
				map.put("CommonBean1.field03",
						(String) eaiMap.get("RDATE_CURJ"));// 拒絕往來日期(TSE)
				map.put("CommonBean1.field04",
						(String) eaiMap.get("CDATE_CURJ"));// 解除拒絕往來日期(TSE)
				map.put("CommonBean1.field05",
						(String) eaiMap.get("ORDATE_CURJ"));// 拒絕往來日期(OTC)
				map.put("CommonBean1.field06",
						(String) eaiMap.get("OCDATE_CURJ"));// 解除拒絕往來日期(OTC)
				map.put("CommonBean1.field07", (String) eaiMap.get("SORT_CURJ"));// 身份別
				map.put("CommonBean1.field08",
						(String) eaiMap.get("LIDNO_CURJ"));// 負責人ID
				map.put("CommonBean1.field09",
						(String) eaiMap.get("FRDATE_CURJ"));// 期貨拒絕往來日期
				map.put("CommonBean1.field10",
						(String) eaiMap.get("FCDATE_CURJ"));// 期貨解除拒絕往來日期
				list.add(map);
			}

			generator.setRowsData(list);
			rptVariableMap.put("dataQDate", params.getString("eaiCURIQ01_ts"));
			generator.setVariableData(rptVariableMap);

			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 建立JSON
	 * 
	 * @param params
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public byte[] generateJSONObject(PageParameters params) throws Exception {

		Map<String, String> rptVariableMap = null;
		JSONObject json = new JSONObject();

		try {
			rptVariableMap = new LinkedHashMap<String, String>();

			List<Map<String, String>> list = new ArrayList<Map<String, String>>();
			List<Map<String, String>> eaiCURIQ01_list = (List<Map<String, String>>) params
					.get("eaiCURIQ01_list");
			for (Map<String, String> eaiMap : eaiCURIQ01_list) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("td0", eaiMap.get("IDNO_CURJ"));// 統一編號
				map.put("td1", eaiMap.get("NAME_CURJ"));// 客戶名稱
				map.put("td2", eaiMap.get("RDATE_CURJ"));// 拒絕往來日期(TSE)
				map.put("td3", eaiMap.get("CDATE_CURJ"));// 解除拒絕往來日期(TSE)
				map.put("td4", eaiMap.get("ORDATE_CURJ"));// 拒絕往來日期(OTC)
				map.put("td5", eaiMap.get("OCDATE_CURJ"));// 解除拒絕往來日期(OTC)
				map.put("td6", eaiMap.get("SORT_CURJ"));// 身份別
				map.put("td7", eaiMap.get("LIDNO_CURJ"));// 負責人ID
				map.put("td8", eaiMap.get("FRDATE_CURJ"));// 期貨拒絕往來日期
				map.put("td9", eaiMap.get("FCDATE_CURJ"));// 期貨解除拒絕往來日期
				list.add(map);
			}

			rptVariableMap.put("dataQDate", params.getString("eaiCURIQ01_ts"));
			json.put("list", list);
			json.putAll(rptVariableMap);

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return json.toString().getBytes("utf-8");

	}

}
