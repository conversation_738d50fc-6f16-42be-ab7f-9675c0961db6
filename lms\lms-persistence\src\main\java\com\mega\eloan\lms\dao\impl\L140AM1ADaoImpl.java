/* 
 * L140AM1ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140AM1ADao;
import com.mega.eloan.lms.model.L140AM1A;

/** 央行註記異動作業授權檔 **/
@Repository
public class L140AM1ADaoImpl extends LMSJpaDao<L140AM1A, String>
	implements L140AM1ADao {

	@Override
	public L140AM1A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140AM1A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140AM1A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140AM1A findByUniqueKey(String mainId, String ownUnit, String authType, String authUnit){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownUnit", ownUnit);
		if (authType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "authType", authType);
		if (authUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "authUnit", authUnit);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140AM1A> findByIndex01(String mainId, String ownUnit, String authType, String authUnit){
		ISearch search = createSearchTemplete();
		List<L140AM1A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (ownUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "ownUnit", ownUnit);
		if (authType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "authType", authType);
		if (authUnit != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "authUnit", authUnit);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}