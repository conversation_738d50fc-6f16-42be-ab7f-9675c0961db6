/* 
 * CLS8011S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 個金個人資料清冊作業  - 文件資訊
 * </pre>
 * 
 * @since 2014/04/04
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public class CLS8011S01Panel extends Panel {

	public CLS8011S01Panel(String id) {
		super(id);
	}

	public CLS8011S01Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
