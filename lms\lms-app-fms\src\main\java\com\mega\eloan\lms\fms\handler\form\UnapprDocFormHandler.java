/* 
 *  UnapprDocFormHandlerr.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DeletedMeta;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DeletedMetaService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.LasDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.service.UnapprDocService;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L230M01A;
import com.mega.eloan.lms.model.L712M01A;
import com.mega.eloan.lms.model.L785M01A;
import com.mega.eloan.lms.model.L918M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowDefinitionImpl;
import tw.com.jcs.flow.core.FlowEngineImpl;
import tw.com.jcs.flow.core.FlowInstanceImpl;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 取消覆核
 * </pre>
 * 
 * @since 2012/6/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/6/25,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("unapprdocformhandler")
public class UnapprDocFormHandler extends AbstractFormHandler {

	@Resource
	UnapprDocService unapprDocService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	FlowService flowService;

	@Resource
	EloandbBASEService eloandbBASEService;
	@Resource
	ProdService prodService;

	@Resource
	FlowEngineImpl engine;
	@Autowired
	DocCheckService docCheckService;

	@Resource
	DeletedMetaService deletedMetaService;

	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;
	
	private static final Logger logger = LoggerFactory
			.getLogger(UnapprDocFormHandler.class);

	/**
	 * 退回流程
	 * 
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * 
	 *         <pre>
	 * { brName = 分行名稱 }
	 * </pre>
	 * @throws CapException
	 * 
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult todoBack(PageParameters params)
			throws CapException {
		logger.info("++++++++++退回流程開始+++++++++++++++");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.OID, "");
		String caseType = params.getString("caseType", "");
		Class className = null;
		Class enumClassName = null;
		// 企金覆審名單退回的點不一樣
		Boolean isSpecail = false;
		if (!Util.isEmpty(caseType)) {
			if ("L120M01A".equals(caseType)) {
				className = L120M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("L141M01A".equals(caseType)) {
				className = L141M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("L160M01A".equals(caseType)) {
				className = L160M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("L210M01A".equals(caseType)) {
				className = L210M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("L230M01A".equals(caseType)) {
				className = L230M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("L170M01A".equals(caseType)) {
				className = L170M01A.class;
				enumClassName = RetrialDocStatusEnum.class;
			} else if ("L180M01A".equals(caseType)) {
				isSpecail = true;
				className = L180M01A.class;
				enumClassName = RetrialDocStatusEnum.class;
			} else if ("C241M01A".equals(caseType)) {
				className = C241M01A.class;
				enumClassName = RetrialDocStatusEnum.class;
			} else if ("L192M01A".equals(caseType)) {
				className = L192M01A.class;
				enumClassName = LasDocStatusEnum.class;
			} else if ("C160M01A".equals(caseType)) {
				className = C160M01A.class;
				enumClassName = CLSDocStatusEnum.class;
			} else if ("C102M01A".equals(caseType)) {
				className = C102M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("L918M01A".equals(caseType)) {
				className = L918M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("L712M01A".equals(caseType)) {
				className = L712M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("L785M01A".equals(caseType)) {
				className = L785M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else if ("C124M01A".equals(caseType)) {
				className = C124M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			} else {
				className = L120M01A.class;
				enumClassName = CreditDocStatusEnum.class;
			}
		}
		Meta meta = unapprDocService.findModelByOid(className, oid);
		if (meta == null) {
			logger.debug("找不到主檔文件 class:{},oid:{}", className, oid);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		// 寫LOG COM.DELETEDMETA
		DeletedMeta deletedMeta = new DeletedMeta(meta);
		deletedMeta.setDeleter(user.getUserId());
		deletedMeta.setDeletedTime(CapDate.getCurrentTimestamp());

		// 避免有NULL欄位
		String[] fieldNames = new String[] { "uid", "mainId", "typCd",
				"custId", "dupNo", "custName", "unitType", "ownBrId",
				"docStatus", "randomCode", "docURL", "txCode", "isClosed",
				"creator", "createTime", "updater", "updateTime", "approver",
				"approveTime", "deletedTime" };
		for (String fieldName : fieldNames) {
			if (Util.equals("createTime", fieldName)
					|| Util.equals("updateTime", fieldName)
					|| Util.equals("approveTime", fieldName)
					|| Util.equals("deletedTime", fieldName)) {
				deletedMeta.set(
						fieldName,
						meta.get(fieldName) == null ? CapDate
								.getCurrentTimestamp() : meta.get(fieldName));
			} else if (Util.equals("isClosed", fieldName)) {
				deletedMeta.set(fieldName, meta.isClosed() ? "Y" : "N");
			} else {
				deletedMeta.set(fieldName, Util.trim(meta.get(fieldName)));
			}

		}

		JSONObject json = new JSONObject();
		// 退回的主表
		json.put("actionType", "todoBack");
		json.put("caseType", caseType);
		deletedMeta.setFfbody(json.toString());
		deletedMetaService.save(deletedMeta);

		// 檢查文件lock
		List<DocOpener> openerl = docCheckService
				.findByMainId(meta.getMainId());

		// 檢核文件開啟者是否為同一人
		for (int i = openerl.size() - 1; i >= 0; i--) {
			DocOpener opener = openerl.get(i);
			if (Util.trim(user.getUserId()).equals(
					Util.trim(opener.getOpener()))) {
				// 同一人時
				if (OpenTypeCode.Readonly.getCode()
						.equals(opener.getOpenType())) {
					CapAjaxFormResult res = new CapAjaxFormResult();
					res.set("openerLockDoc", true);
				}
				break;
			} else if (OpenTypeCode.Writing.getCode().equals(
					opener.getOpenType())) {
				// 被記錄編輯但編輯者不是自己時
				HashMap<String, String> hm = new HashMap<String, String>();
				hm.put("userId", opener.getOpener());
				hm.put("userName",
						userInfoService.getUserName(opener.getOpener()));
				// 此文件正由 [$\{userId\}-$\{userName\}]開啟中!系統將以唯讀狀態開啟此文件。
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0009", hm), getClass());
			}
		}
		// 前置作業
		try {
			List<FlowInstance> histroyFlow = flowService.createQuery().id(oid)
					.history().queryForList();
			if ("L160M01A".equals(caseType)) {
				eloandbBASEService.doWorkUnappByAnyDocstatus(oid,
						CreditDocStatusEnum.海外_待覆核.name(), histroyFlow,
						meta.getOwnBrId());
			} else if ("C160M01A".equals(caseType)) {
				eloandbBASEService.doWorkUnappByAnyDocstatus(oid,
						CLSDocStatusEnum.待覆核.name(), histroyFlow,
						meta.getOwnBrId());
			} else {
				eloandbBASEService.doWorkUnapp(oid, isSpecail, histroyFlow);
			}

		} catch (Exception e) {
			logger.debug(e.getMessage());
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		HashMap<String, String> msg = new HashMap<String, String>();
		FlowInstance inst = flowService.createQuery().id(oid).query();
		if (inst == null) {
			msg.put("msg", " FlowInstance is null oid ====>" + oid);
			logger.debug(" FlowInstance is null oid ====>" + oid);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得目前的流程圖
		Map<String, Object> rowData = eloandbBASEService.selNowFlowWinst(oid);
		if (rowData == null) {
			msg.put("msg", " not find FlowWinst ");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		String defName = (String) rowData.get("DEFNAME");
		// 取得目前流程定義檔
		FlowDefinitionImpl definition = engine.getDefinition(defName);
		// 取得記憶體中的流程，並帶入正確的流程圖
		FlowInstanceImpl instant = engine.getPooledInstance(oid);
		if (instant != null) {
			instant.setDefinition(definition);
		}
		String state = inst.getState();
		String docStatus = definition.getNodes().get(state).getStatus();
		if (!Util.isEmpty(docStatus)) {
			Enum statusEnum = Enum.valueOf(enumClassName, docStatus);
			meta.setDocStatus(statusEnum.toString());
			meta.setOwnBrId(inst.getDeptId());
			result.set(
					"brName",
					"{" + inst.getDeptId() + " "
							+ branchService.getBranchName(inst.getDeptId())
							+ "} next state" + docStatus);
			unapprDocService.save(meta);
		} else {
			logger.debug("[not find docStatus] node state is ===> " + state);
			msg.put("msg", "[not find docStatus] node state is ===> " + state);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		
		if ("L120M01A".equals(caseType)) {
			L120M01A l120m01a = clsService.findL120M01A_oid(oid);
			if(l120m01a!=null){
				clsService.l120m01aDoBack(l120m01a);
				lmsService.gfnDeleteLNF013(l120m01a);
				lmsService.backElf442(l120m01a);
			}
		}
		return result;
	}

	/**
	 * 更新產品種類維護
	 * 
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 * 
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult reloadProd(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		prodService.reload(true);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

}
