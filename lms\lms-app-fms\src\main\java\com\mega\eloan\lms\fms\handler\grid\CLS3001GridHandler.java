package com.mega.eloan.lms.fms.handler.grid;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.CLS3001V01Page;
import com.mega.eloan.lms.model.C900S02D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls3001gridhandler")
public class CLS3001GridHandler extends AbstractGridHandler {


	@Resource
	CLSService service;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	Properties prop = MessageBundleScriptCreator
			.getComponentResource(CLS3001V01Page.class);

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryMain(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		
		if (true) {
			//Filet查詢條件
			String category = Util.trim(params.getString("search_category"));
			String caseBrId = Util.trim(params.getString("search_caseBrId"));
			String custId = Util.trim(params.getString("search_custId"));
			String cntrNo = Util.trim(params.getString("search_cntrNo"));
			
			if(Util.isNotEmpty(category)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "category", category);
			}
			if(Util.isNotEmpty(caseBrId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId", caseBrId);
			}
			if(Util.isNotEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			}
			if(Util.isNotEmpty(cntrNo)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
			}
		}
		if(!user.getUnitNo().startsWith("9")){			
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
		}
		//========================
		FlowDocStatusEnum docStatusEnum = FlowDocStatusEnum.getEnum(docStatus);
		if (docStatusEnum != null) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);			
		} else {
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
		}
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");

		Page<? extends GenericBean> page =service.findPage(
				C900S02D.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		if(true){
			dataReformatter.put("caseBrId", new BranchNameFormatter(branchService,
					BranchNameFormatter.ShowTypeEnum.ID_Name)); // 分行名稱格式化
			UserNameFormatter userNameFormatter = new UserNameFormatter(userInfoService, UserNameFormatter.ShowTypeEnum.Name);
			dataReformatter.put("updater", userNameFormatter); // 使用者名稱格式化	
			dataReformatter.put("approver", userNameFormatter); // 使用者名稱格式化
			
			dataReformatter.put("category", new CodeTypeFormatter(codeTypeService, "C900S02D_category"));
		}
		result.setDataReformatter(dataReformatter);
		
		return result;
	}
	
	public CapMapGridResult importCnrNoData(ISearch pageSetting,
			PageParameters params) throws CapException {
		
		String queryId = Util.trim(params.getString("queryId"));
		String queryCntrNo = Util.trim(params.getString("queryCntrNo"));
		//========================
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> src_list = new ArrayList<Map<String, Object>>();
		if(Util.isNotEmpty(queryId) || Util.isNotEmpty(queryCntrNo)){
			src_list = eloandbBASEService.findL140M01A_J_107_0327(queryId, queryCntrNo);
		}
		
		for(Map<String, Object> rs : src_list){
			String mainId = Util.trim(MapUtils.getString(rs, "MAINID"));
			String custId = Util.trim(MapUtils.getString(rs, "CUSTID"));
			String dupNo = Util.trim(MapUtils.getString(rs, "DUPNO"));
			String custName = Util.trim(MapUtils.getString(rs, "CUSTNAME"));
			String cntrNo = Util.trim(MapUtils.getString(rs, "CNTRNO"));
			String caseNo = Util.trim(MapUtils.getString(rs, "CASENO"));
			String caseBrId = Util.trim(Util.getLeftStr(cntrNo, 3));
			String curr = Util.trim(MapUtils.getString(rs, "CURRENTAPPLYCURR"));
			String applyAmt = LMSUtil.pretty_numStr((BigDecimal)MapUtils.getObject(rs, "CURRENTAPPLYAMT"));
			
			if(true){
				Map<String, Object> row = new HashMap<String, Object>();
				row.put("l140m01a_mainId", mainId);
				row.put("custId", custId);
				row.put("dupNo", dupNo);
				row.put("custName", custName);
				row.put("cntrNo", cntrNo);
				row.put("document_no", caseNo);
				row.put("caseBrId", caseBrId);		
				row.put("curr", curr);
				row.put("applyAmt", applyAmt);				
				//---
				list.add(row);
			}	
		}
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
}
