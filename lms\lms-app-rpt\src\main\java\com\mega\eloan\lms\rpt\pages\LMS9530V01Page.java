package com.mega.eloan.lms.rpt.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanButtonItem;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.lns.pages.LMS1201V01Page;

@Controller
@RequestMapping(path = "/rpt/lms9530v01")
public class LMS9530V01Page extends AbstractEloanInnerView {

	public LMS9530V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		addToButtonPanel(model, LmsButtonEnum.Print, LmsButtonEnum.Filter);

		addToButtonPanel(model, new EloanButtonItem("btnChange", "ui-icon-jcs-21", "續約/條件變更"));
		addToButtonPanel(model, new EloanButtonItem("btnTableSend", "ui-icon ui-icon-jcs-06", "額度明細表傳送聯行"));

		// 套用哪個i18N檔案
		renderJsI18N(LMS1201V01Page.class);
		renderJsI18N(LMS9530V01Page.class);
//		renderJsI18N(LMS1201V01Page.class, "l120v01.alert1", "l120v01.alert2",
//				"l120v01.thickbox13", "l120v01.thickbox1", "l120v01.thickbox2");

		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/rpt/LMS9530V01Page');");
	}
}
