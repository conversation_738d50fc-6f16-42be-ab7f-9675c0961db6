package com.mega.eloan.lms.rpt.service;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;




/**
 * <pre>
 * 免計入銀行法72-2限額控管之廠房貸款案件追蹤表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS180R70Service {
	
	public Map<String, Integer> getTitleMap();
	
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException;

	public Map<String, Integer> getHeaderMap();

	public List<Map<String, Object>> processData(List<Map<String, Object>> elf515List);

	public void setBodyContent(WritableSheet sheet, List<Map<String, Object>> data, int colIndex, int rowIndex) throws RowsExceededException, WriteException;

	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException;
}