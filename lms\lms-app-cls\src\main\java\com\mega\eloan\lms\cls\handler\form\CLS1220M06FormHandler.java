package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.pages.CLS1220M04Page;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dw.service.DwBGMOPENService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上貸款 - 青創
 * </pre>
 * 
 * @since 2022/03/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/03/18,011879,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1220m06formhandler")
@DomainClass(C122M01A.class)
public class CLS1220M06FormHandler extends AbstractFormHandler {

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	CLS1220Service service;

	@Resource
	RetrialService retrialService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocCheckService docCheckService;

	@Resource
	DocLogService docLogService;

	@Resource
	CLSService clsService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	DwBGMOPENService dwBGMOPENService;

	@Resource
	MisElCUS25Service elcus25Srv;// 公司戶資料

	Properties prop_cls1220m04 = MessageBundleScriptCreator
			.getComponentResource(CLS1220M04Page.class);

	/**
	 * 檢查申請人統編與公司負責人統編是否一致
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult checkCustIdWithCompanyId(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = params.getString("custId");// 申請人統編
		String custCompanyId = params.getString("custCompanyId");// 公司統編
		String message = "";
		String checkResult="";
		Map<String, Object> cus25 = elcus25Srv.findByPk(custCompanyId, "0");
		if (cus25 != null && !cus25.isEmpty()) {
			String sup1Id = Util.toSemiCharString(CapString.trimNull(cus25
					.get("SUP1ID")));//公司負責人統編

			if (!Util.equals(custId, sup1Id)) {// 申請人統編跟公司負責人統編不一致
				checkResult="NG";
			} else {// 申請人統編跟公司負責人統編一致
				checkResult="OK";
			}
		} else {// 用公司統編沒找到負責人
			checkResult="NG";
		}
		if(!Util.equals(checkResult, "OK")){
			message = MessageFormat.format(prop_cls1220m04
					.getProperty("Message.custIdNotMatch"),	custCompanyId);
		}
		result.set("checkResult", checkResult);
		result.set("message", message);
		return result;
	}

	/**
	 * 線下案件,查詢客戶資料(引入稅籍)
	 * 
	 * @param params
	 *            PageParameters
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify)
	public IResult queryCustDataforNonNB(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId = params.getString("custId");// 申請人統編
		String custCompanyId = params.getString("custCompanyId");// 公司統編
		String dupNo = params.getString("dupNo");
		result.set("n_cnumber2", custCompanyId);
		Map<String, Object> dataMap = misCustdataService.findByIdDupNo(
				custCompanyId, "0");// 公司資料
		if (dataMap != null) {
			String cityCN = CapString.trimNull(MapUtils.getString(dataMap,
					"CITYR"));
			cityCN = cityCN.replace("臺", "台");
			CodeType cityCodeType = codeTypeService.findByTypeAndDesc(
					"counties", cityCN);
			result.set("city",
					cityCodeType != null ? cityCodeType.getCodeValue() : "");// 公司地址-縣/市
			String countyCN = CapString.trimNull(MapUtils.getString(dataMap,
					"TOWNR"));
			CodeType countyCodeType = codeTypeService.findByTypeAndDesc(
					"counties" + cityCodeType.getCodeValue(), countyCN);
			result.set("cityarea",
					countyCodeType != null ? countyCodeType.getCodeValue() : "");// 公司地址-區/市/鄉/鎮
			result.set("n_address", Util.toSemiCharString(CapString
					.trimNull(MapUtils.getString(dataMap, "LEER"))
					+ CapString.trimNull(MapUtils.getString(dataMap, "LINR"))
					+ CapString.trimNull(MapUtils.getString(dataMap, "ADDRR"))));// 公司地址
			result.set("n_cdate",
					CapString.trimNull(MapUtils.getString(dataMap, "BIRTHDT")));// 公司成立日期
			result.set("ctype", "");
			result.set("input4", "");
			result.set("cname", Util.toSemiCharString(CapString
					.trimNull(MapUtils.getString(dataMap, "CNAME"))));// 事業體名稱
			dataMap = dwBGMOPENService.getDataFromTaxation(custCompanyId);// 取得公司稅籍資料
			String orgType = CapString.trimNull(MapUtils.getString(dataMap,
					"ORG_TYPE_NAME"));
			result.set("ctype", orgTypeToCodeType(orgType));
			BigDecimal capital = new BigDecimal(CapString.isEmpty(MapUtils
					.getString(dataMap, "CAPITAL")) ? "0" : MapUtils.getString(
					dataMap, "CAPITAL"));
			result.set(
					"input4",
					capital.divide(new BigDecimal(10000), 0,
							RoundingMode.HALF_UP).toString());
		} else {
			dataMap = dwBGMOPENService.getDataFromTaxation(custCompanyId);
			if (dataMap != null) {
				String addr = CapString.trimNull(MapUtils.getString(dataMap,
						"BUS_ADDRS"));
				String cityCN = addr.replaceAll("^(.+)([縣市])(.+)([區鎮鄉市])(.+)$",
						"$1$2");
				cityCN = cityCN.replace("臺", "台");
				CodeType cityCodeType = codeTypeService.findByTypeAndDesc(
						"counties", cityCN);
				result.set("city",
						cityCodeType != null ? cityCodeType.getCodeValue() : "");
				String countyCN = addr.replaceAll(
						"^(.+)([縣市])(.+)([區鎮鄉市])(.+)$", "$3$4");
				CodeType countyCodeType = codeTypeService.findByTypeAndDesc(
						"counties" + cityCodeType.getCodeValue(), countyCN);
				result.set("cityarea",
						countyCodeType != null ? countyCodeType.getCodeValue()
								: "");
				result.set("n_address", Util.toSemiCharString(addr.replaceAll(
						"^(.+)([縣市])(.+)([區鎮鄉市])(.+)$", "$5")));
				String date = MapUtils.getString(dataMap, "CREATE_DT");
				result.set("n_cdate", TWNDate.valueOf(date).toAD('-'));
				String orgType = CapString.trimNull(MapUtils.getString(dataMap,
						"ORG_TYPE_NAME"));
				result.set("ctype", orgTypeToCodeType(orgType));
				BigDecimal capital = new BigDecimal(MapUtils.getString(dataMap,
						"CAPITAL"));
				result.set(
						"input4",
						capital.divide(new BigDecimal(10000), 0,
								RoundingMode.HALF_UP).toString());
				String custName = CapString.trimNull(MapUtils.getString(
						dataMap, "ENTITY_NAME"));// 事業體名稱
				result.set("cname", Util.toSemiCharString(custName));
			} else {
				result.set("city", "");
				result.set("cityarea", "");
				result.set("n_address", "");
				result.set("n_cdate", "");
				result.set("ctype", "");
				result.set("input4", "");
				result.set("cname", "");
			}
		}

		// 用申請人ID查0024
		Map<String, Object> custDataMap = misCustdataService.findByIdDupNo(
				custId, dupNo);

		if (custDataMap != null) {
			String cityCN = CapString.trimNull(MapUtils.getString(custDataMap,
					"CITYR"));
			cityCN = cityCN.replace("臺", "台");
			CodeType cityCodeType = codeTypeService.findByTypeAndDesc(
					"counties", cityCN);
			result.set("citya",
					cityCodeType != null ? cityCodeType.getCodeValue() : "");// 戶籍地址-縣/市
			String countyCN = CapString.trimNull(MapUtils.getString(
					custDataMap, "TOWNR"));
			CodeType countyCodeType = codeTypeService.findByTypeAndDesc(
					"counties" + cityCodeType.getCodeValue(), countyCN);
			result.set("cityareaa",
					countyCodeType != null ? countyCodeType.getCodeValue() : "");// 戶籍地址-區/市/鄉/鎮
			result.set("address", Util.toSemiCharString(CapString
					.trimNull(MapUtils.getString(custDataMap, "LEER"))
					+ CapString.trimNull(MapUtils
							.getString(custDataMap, "LINR"))
					+ CapString.trimNull(MapUtils.getString(custDataMap,
							"ADDRR"))));// 戶籍地址
			result.set("bday", CapString.trimNull(MapUtils.getString(
					custDataMap, "BIRTHDT")));// 生日
		}
		return result;
	}

	/**
	 * 中文轉換為下拉選單值
	 * 
	 * @param orgType
	 * @return
	 */
	public static String orgTypeToCodeType(String orgType) {
		if (orgType.startsWith("其他")) {
			return "5"; // 其他
		} else if (orgType.startsWith("股份有限公司")) {
			return "1"; // 股份有限公司
		} else if (orgType.startsWith("有限公司")) {
			return "2"; // 有限公司
		} else if (orgType.startsWith("合夥")) {
			return "3"; // 合夥
		} else if (orgType.startsWith("獨資")) {
			return "4"; // 獨資
		}
		return "";
	}

}
