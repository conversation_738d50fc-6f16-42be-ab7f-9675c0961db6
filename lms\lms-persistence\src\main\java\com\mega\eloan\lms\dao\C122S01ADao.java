/* 
 * C122S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C122S01A;

/** 線上增貸核貸資訊 **/
public interface C122S01ADao extends IGenericDao<C122S01A> {

	C122S01A findByOid(String oid);
	
	List<C122S01A> findByMainId(String mainId);
	
	C122S01A findByUk(String mainId, Integer batchNo);
	
	int maxBatchNoInMainId(String mainId);
}