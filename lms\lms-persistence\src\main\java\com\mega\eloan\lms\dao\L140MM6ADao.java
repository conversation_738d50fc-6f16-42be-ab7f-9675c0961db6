/* 
 * L140MM6ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MM6A;

/** 共同行銷維護作業主檔 **/
public interface L140MM6ADao extends IGenericDao<L140MM6A> {

	L140MM6A findByOid(String oid);
	
	L140MM6A findByMainId(String mainId);
	
	List<L140MM6A> findByDocStatus(String docStatus);

	List<L140MM6A> findByIndex01(String mainId);

	List<L140MM6A> findByIndex02(String cntrNo);
}