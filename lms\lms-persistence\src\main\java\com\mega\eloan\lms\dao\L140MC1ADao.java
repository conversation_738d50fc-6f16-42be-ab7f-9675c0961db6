/* 
 * L140MC1ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MC1A;

/** 疑似人頭戶檢核明細紀錄檔 **/
public interface L140MC1ADao extends IGenericDao<L140MC1A> {

	L140MC1A findByOid(String oid);
	
	List<L140MC1A> findByMainId(String mainId);
	
	L140MC1A findByUniqueKey(String mainId, String itemCode);

	void saveAll(List<L140MC1A> list);

	void deleteAll(List<L140MC1A> list);
}