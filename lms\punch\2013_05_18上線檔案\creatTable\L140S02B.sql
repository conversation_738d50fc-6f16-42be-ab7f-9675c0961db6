---------------------------------------------------------
-- LMS.L140S02B 流用(長擔)額度序號檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02B;
CREATE TABLE LMS.L140S02B (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	CNTRNO        CHAR(12)      not null,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>    TIMESTAMP    ,

	constraint P_L140S02B PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02B01;
CREATE UNIQUE INDEX LMS.XL140S02B01 ON LMS.L140S02B   (MAINID, SEQ, CNTRNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02B IS '流用(長擔)額度序號檔';
COMMENT ON LMS.L140S02B (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	CNTRNO        IS '流用(長擔)額度序號', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
