package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.bean.ELF515;
import com.mega.eloan.lms.mfaloan.service.MisELF515Service;

@Service
public class MisELF515ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF515Service {

	@Override
	public ELF515 find(String cntrNo, String type) {
		Object[] argsObAry = new Object[2];
		ELF515 elf580 = new ELF515();

		argsObAry[0] = cntrNo;
		argsObAry[1] = type;
		// select * from mis.elf515 where ELF515_CNTRNO = ? and ELF515_TYPE = ?
		List<ELF515> results = getJdbc().query("ELF515.getByCntrnoType",
				argsObAry, elf580.new ELF515RM());

		return CollectionUtils.isEmpty(results) ? null : results.get(0);
	}

	@Override
	public List<ELF515> find(String cntrNo) {
		Object[] argsObAry = new Object[1];
		ELF515 elf580 = new ELF515();

		argsObAry[0] = cntrNo;
		// select * from mis.elf515 where ELF515_CNTRNO = ?
		List<ELF515> results = getJdbc().query("ELF515.getByCntrno", argsObAry,
				elf580.new ELF515RM());

		return results;
	}

	@Override
	public ELF515 findMcntrNo(String mCntrNo) {
		Object[] argsObAry = new Object[2];
		ELF515 elf515 = new ELF515();

		argsObAry[0] = mCntrNo;
		argsObAry[1] = mCntrNo;

		// select * from mis.elf515 where ELF515_CNTRNO = ? and ELF515_CNTRNO_M
		// = ?
		List<ELF515> results = getJdbc().query("ELF515.getMCntrno", argsObAry,
				elf515.new ELF515RM());

		return CollectionUtils.isEmpty(results) ? null : results.get(0);
	}

	@Override
	public List<ELF515> getCurrentDataByCntrNo(String cntrNo) {
		Object[] argsObAry = new Object[2];
		ELF515 elf581 = new ELF515();
		argsObAry[0] = cntrNo;
		argsObAry[1] = cntrNo;

		List<ELF515> results = getJdbc().query("ELF515.getCurrentData", null,
				argsObAry, 0, Integer.MAX_VALUE, elf581.new ELF515RM());

		return results;
	}

	@Override
	public void delete(String cntrNo) {

		// delete from mis.elf515 where ELF515_CNTRNO = ?
		this.getJdbc().update("ELF515.delete", new String[] { cntrNo });
	}

	@Override
	public void insert(ELF515 elf515) {

		this.getJdbc().update(
				"ELF515.insert",
				new Object[] { Util.trim(elf515.getElf515_cntrno()),
						Util.trim(elf515.getElf515_type()),
						Util.trim(elf515.getElf515_instalment()),
						Util.trim(elf515.getElf515_cntrno_m()),
						Util.trim(elf515.getElf515_inter_outer()),
						Util.trim(elf515.getElf515_site1()),
						Util.trim(elf515.getElf515_site2()),
						Util.trim(elf515.getElf515_site3()),
						Util.trim(elf515.getElf515_site4()),
						Util.trimSizeInOS390(elf515.getElf515_address(), 100),
						Util.trim(elf515.getElf515_build_state()),
						elf515.getElf515_build_date(),
						Util.trim(elf515.getElf515_sub_type1()),
						Util.trim(elf515.getElf515_sub_type2()),
						Util.trim(elf515.getElf515_sub_type3()),
						Util.trim(elf515.getElf515_sub_type4()),
						Util.trim(elf515.getElf515_sub_type5()),
						Util.trim(elf515.getElf515_sub_type6()),
						Util.trim(elf515.getElf515_sub_type7()),
						Util.trimSizeInOS390(elf515.getElf515_data1(), 100),
						Util.trimSizeInOS390(elf515.getElf515_data2(), 100),
						Util.trimSizeInOS390(elf515.getElf515_data3(), 100),
						Util.trimSizeInOS390(elf515.getElf515_data4(), 100),
						Util.trimSizeInOS390(elf515.getElf515_data5(), 100),
						Util.trim(elf515.getElf515_empl_no()),
						Util.trim(elf515.getElf515_supv_no()),
						Util.trim(elf515.getElf515_createUnit()),
						Util.trim(elf515.getElf515_modifyUnit()) });
	}
	
	@Override
	public void do72_2BatchFrom515(){
//		INSERT INTO MIS.ELF515
//		(ELF515_CNTRNO, ELF515_TYPE, ELF515_INSTALMENT, ELF515_CNTRNO_M, ELF515_INTER_OUTER, ELF515_SITE1, ELF515_SITE2, ELF515_SITE3, ELF515_SITE4, 
//		ELF515_ADDRESS, ELF515_BUILD_STATE, ELF515_SUB_TYPE1, ELF515_SUB_TYPE2, ELF515_SUB_TYPE3, ELF515_SUB_TYPE4, ELF515_SUB_TYPE5, 
//		ELF515_SUB_TYPE6, ELF515_SUB_TYPE7, ELF515_DATA1, ELF515_DATA2, ELF515_DATA3, ELF515_DATA4, ELF515_DATA5, ELF515_EMPL_NO, ELF515_SUPV_NO, 
//		ELF515_CREATEUNIT, ELF515_MODIFYUNIT, ELF515_CREATETIME, ELF515_MODIFYTIME)
//		select 
//		ELF506_CNTRNO, ELF506_722_EX_ITEM,'N','','','','','','',
//		'','','','','','','',
//		'','','','','','','','SYSTEM','SYSTEM',
//		'900','900',current timestamp,current timestamp 
//		from mis.elf506
//		where ELF506_722_EX_ITEM != ''
		this.getJdbc().update("ELF515.batch001", new Object[]{});

	}
}
