/* 
 * C241M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C241M01B;



/** 授信帳務資料檔 **/
public interface C241M01BDao extends IGenericDao<C241M01B> {

	C241M01B findByOid(String oid);

	List<C241M01B> findByMainId(String mainId);
	
	List<C241M01B> findByMainIdByYLnDataDate(String mainId);

	C241M01B findByUniqueKey(String mainId, String custId, String dupNo);

	List<C241M01B> findByIndex01(String mainId, String custId, String dupNo);

	List<C241M01B> findByCustIdDupId(String custId,String DupNo);
	/**
	 * 利用C240M01A mainId 刪除
	 * @param mainId
	 */
	void deleteByC240M01AMainid(String mainId);

	List<C241M01B> findByCntrNo(String CntrNo);
}