package com.mega.eloan.lms.dc.thread;

import com.mega.eloan.lms.dc.action.CreateDeleteSQL;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;

/**
 * <pre>
 * GenDelSQLThread
 * </pre>
 * 
 * @since 2013/03/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/03/05,<PERSON>,new
 *          </ul>
 */
public class GenDelSQLThread extends Thread {

	private String schema = "";
	private ConfigData config = null;

	public GenDelSQLThread() {
		super();
	}

	/**
	 * Constructor
	 * 
	 * @param schema
	 *            String:目前執行的系統名稱
	 * @param pps
	 *            :Properties
	 */
	public GenDelSQLThread(String schema) {
		this.schema = schema;
	}

	public void run() {
		CreateDeleteSQL cds = new CreateDeleteSQL();
		cds.setConfigData(config);
		cds.genSql(this.schema);
	}

	public void createDeleteSQL(ConfigData config) {
		try {
			this.setConfig(config);
			this.run();
		} catch (DCException e) {
			throw new DCException("CreateDeleteSQL 時產生錯誤...", e);
		}
	}

	/**
	 * set the config
	 * 
	 * @param config
	 *            the config to set
	 */
	public void setConfig(ConfigData config) {
		if (config != null) {
			this.config = config;
		}
	}
}
