<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		<script type="text/javascript">
			loadScript('pagejs/lms/LMS2105M01Page');
		</script>
		<div class="button-menu funcContainer" id="buttonPanel">

			<!--編製中 -->
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button id="btnSave">
					<span class="ui-icon ui-icon-jcs-04"></span>
					<th:block th:text="#{'button.save'}"><!--儲存--></th:block>
				</button>
				<button id="btnSend">
					<span class="ui-icon ui-icon-jcs-02"></span>
					<th:block th:text="#{'button.send'}"><!--呈主管覆核--></th:block>
				</button>
			</th:block>

			<!--待覆核 -->
			<th:block th:if="${_btnWAIT_APPROVE_visible}">
				<button id="btnCheck">
					<span class="ui-icon ui-icon-jcs-106"></span>
					<th:block th:text="#{'button.check'}"><!--覆核--></th:block>
				</button>
			</th:block>

			<button id="btnPrint" type="button" class="forview">
				<span class="ui-icon ui-icon-jcs-03"></span>
				<th:block th:text="#{'button.print'}"><!--列印--></th:block>
			</button>
			<button id="btnExit" type="button" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}"><!--離開--></th:block>
			</button>
		</div>
		<div class="tit2 color-black">
			<th:block th:text="#{'L210M01A.title01'}"><!-- 修改資料特殊流程--></th:block>：
			(<span id="showTypCd" class="text-red"></span>)<span id="showCustId" class="color-blue"></span>
		</div>
		<div class="tabs doc-tabs">
			<ul>
				<li><a href="#tab-01" goto="01"><b><th:block th:text="#{'doc.docinfo'}"><!--  文件資訊--></th:block></b></a></li>
				<li><a href="#tab-02" goto="02"><b><th:block th:text="#{'L210M01A.title02'}"><!--  申請概要--></th:block></b></a></li>
				<li><a href="#tab-03" goto="03"><b><th:block th:text="#{'L210M01A.title03'}"><!--  變更條件前--></th:block></b></a></li>
				<li><a href="#tab-04" goto="04"><b><th:block th:text="#{'L210M01A.title04'}"><!-- 變更條件後--></th:block></b></a></li>
			</ul>
			<div class="tabCtx-warp">
				<div th:id="${tabIdx}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
			</div>
		</div>

		<!--呈主管選擇-->
		<div id="selectBossBox" style="display:none;">
			<form id="selectBossForm">
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td class="hd1" width="60%">
							<th:block th:text="#{'L210M01A.bossId'}"><!--帳務管理員--></th:block>&nbsp;&nbsp;
						</td>
						<td width="40%">
							<select id="accountPerson" name="accountPerson" class="boss"></select>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L210M01A.managerId'}"><!--  單位/授權主管--></th:block>&nbsp;&nbsp;
						</td>
						<td>
							<select id="manager" name="manager" class="boss"></select>
						</td>
					</tr>

				</table>
			</form>
		</div>

		<!--覆核視窗-->
		<div id="openCheckBox" style="display:none">
			<div>
				<label><input name="checkRadio" type="radio" value="2">
					<th:block th:text="#{'L210M01A.bt02'}"><!--  核定--></th:block>
				</label><br />
				<label><input name="checkRadio" type="radio" value="3">
					<th:block th:text="#{'L210M01A.bt03'}"><!--  駁回--></th:block>
				</label><br />
				<label><input name="checkRadio" type="radio" value="1">
					<th:block th:text="#{'L210M01A.bt01'}"><!--  退回經辦修改--></th:block>
				</label>
			</div>
		</div>

		<!--輸入核定日期視窗-->
		<div id="openChecDatekBox" style="display:none">
			<div>
				<input id="forCheckDate" type="text" size="10" maxlength="10" class="date">
			</div>
		</div>
		<!--攤貸行比例-->
		<div id="l140m01eAmtBox" style="display:none;">
			<table border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td>
						<span id="l140m01eAmtMsg"></span>
						<br />
					</td>
				</tr>
				<tr>
					<td>
						<div id="l140m01eAmtGrid"></div>
					</td>
				</tr>
			</table>
		</div>
	</th:block>
</body>
</html>