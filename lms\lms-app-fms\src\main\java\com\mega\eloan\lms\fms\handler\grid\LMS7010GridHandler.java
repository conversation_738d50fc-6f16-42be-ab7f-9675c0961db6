package com.mega.eloan.lms.fms.handler.grid;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.fms.pages.LMS7010V01Page;
import com.mega.eloan.lms.fms.service.LMS7010Service;
import com.mega.eloan.lms.model.L800M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * 常用主管資料 Service
 * 
 * @since 2011/9/29
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/10/29,Vector Lo,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7010gridhandler")
public class LMS7010GridHandler extends AbstractGridHandler {

	@Resource
	LMS7010Service service;
	@Resource
	UserInfoService userSrv;
	@Resource
	BranchService branch;

	/**
	 * 查詢L800M01A Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("rawtypes")
	public CapMapGridResult queryL800m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS7010V01Page.class);
		final String[] cols = { "oid", "brno", "dataType", "ZGName", "zhuGuan",
				"isType1", "isType2", "isType3", "isType4", "isType5", "creator",
				"createTime", "updater", "updateTime" };
		Map<String, Boolean> order = pageSetting.getOrderBy();
		if (order != null) {
			Object[] keys = order.keySet().toArray();
			for (int i = 0; i < keys.length; i++) {
				String strKey = Util.trim(keys[i]);
				if ("type".equals(strKey.substring(0, 4))) {
					order.put("ZGtype", order.get(strKey));
					order.remove(strKey);
				} else if ("dataType_show".equals(strKey)) {
					order.put("dataType", order.get(strKey));
					order.remove(strKey);
				}
			}
		}
		// 只顯示本分行資料
		pageSetting.addOrderBy("ZGName", true);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "brno",
				user.getUnitNo());
		Page<? extends GenericBean> page = service.findPage(L800M01A.class,
				pageSetting);
		List list = page.getContent();
		List<Map<String, Object>> result = new LinkedList<Map<String, Object>>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> record = new HashMap<String, Object>();
			L800M01A pivot = (L800M01A) list.get(i);			
			
			// 代入"資料類型"的顯示文字
			record.put(
					"dataType_show",
					Util.nullToSpace(pop.getProperty("dataType"
							+ pivot.getDataType())));
			// 代入員工姓名
			record.put("creatorNM", userSrv.getUserName(pivot.getCreator()));
			record.put("updaterNM", userSrv.getUserName(pivot.getUpdater()));

			// 代入原資料
			for (int x = 0; x < cols.length; x++) {
				record.put(cols[x], pivot.get(cols[x]));
			}
			result.add(record);
		}
		return new CapMapGridResult(result, result.size());
	}
}
