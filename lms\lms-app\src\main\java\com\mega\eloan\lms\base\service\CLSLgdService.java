/*
 * LMSService.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140S02A;

/**
 * <pre>
 * J-110-0986_05097_B1001 於簽報書新增LGD欄位
 * </pre>
 *
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */
/**
 * <AUTHOR>
 * 
 */
public interface CLSLgdService extends ICapService {

	public BigDecimal computingLgd(String snoKind, boolean isMortgageLoan, boolean isContainGuaranteeSubject, BigDecimal ltv, String type);

	public Map<String, Object> processByProKindAndSubject(String snoKind, String l120m01a_mainId, List<L140S02A> l140s02aList, BigDecimal ltv, String cntrno);

	public Map<String, Object> processByPurchaseOrRepairSubject(String snoKind, L140M01A l140m01a, BigDecimal ltv, String landBuildYN, String clsOrLms);

	public String computingLgdGroup(String snoKind, boolean isMortgageLoan, boolean isAllGuaranteeSubject, BigDecimal ltv, String type);

	public String getFullLgdDescription(BigDecimal lgd, String group);

	public String getComparisonTableHtml();

	public boolean isShowClsLgd(L120M01A l120m01a, String busCode, BigDecimal expectLgd);

	public void setClsLgdColumnInfo(CapAjaxFormResult result, L120M01A l120m01a, String busCode, BigDecimal expectLgd, String expectLgdGroup);

	public boolean isAllPurchaseOrRepairSubject(List<L140M01C> l140m01cList);

	public String checkApprovedPercentIsRequiredForClsCase(L120M01A l120m01a, List<L140M01A> l140m01a_list);

	public boolean isMortgageLoanModel(List<L140S02A> l140s02aList);

	public Map<String, Object> processOverseasClsCase_or_LmsPersonalCaseLgd(L140M01A l140m01a, String modelKind, String snoKind,
								BigDecimal ltv, String clsOrLms);

	public void setL140m01aExpectModelKind(L140M01A l140m01a);

	public String checkApprovedPercentIsRequiredForOverseasCls_or_LmsPersonalCase(
			String unitType, L120M01A l120m01a, List<L140M01A> l140m01aList);

}