package com.mega.eloan.lms.lms.flow;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L130M01ADao;
import com.mega.eloan.lms.dao.L130S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L700M01ADao;
import com.mega.eloan.lms.model.L120A01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L130S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L700M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 案件簽報書 - 泰國流程
 * 
 * </pre>
 * 
 * @since 2011/11/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/2,REX,new
 *          </ul>
 */
@Component
public class LMS1205TH01Flow extends AbstractFlowHandler {
	@Resource
	CommonMetaDao metaDao;

	@Resource
	L120M01FDao l120m01fDao;

	@Resource
	L140M01ADao l140m01aDao;

	@Resource
	L700M01ADao l700m01aDao;

	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	DocLogService docLogService;

	@Resource
	LMSService lmsService;

	@Resource
	L130M01ADao l130m01aDao;

	@Resource
	L130S01BDao l130s01bDao;

	public static final String FLOW_CODE = "LMS1205TH01Flow";

	/**
	 * 這裡經由起案分行決定分行待覆核、或總行待覆核
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "編製中", value = "to判斷行別")
	public void start(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// ObsBrnType 1.海外總行 2.海外一般分行 3.海外簡易分行
		String branchType = branchService.getBranch(meta.getCaseBrId())
				.getObsBrnType();
		if (UtilConstants.obsBrnType.海外總行.equals(branchType)) {
			instance.setAttribute("result", "to總行待覆核");
		} else {
			instance.setAttribute("result", "to分行待覆核");
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (user.getUnitNo().equals(meta.getCaseBrId())) {
			lmsService.gfnDB2SetELF442_CNTRNO(meta,
					UtilConstants.Cntrdoc.ACTION.呈主管, null);

		}
		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())) {
			// 授權外額度明細表要把額度明細表的文件狀態改為待覆核
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());

		} else if (UtilConstants.Casedoc.DocKind.授權內.equals(meta.getDocKind())
				&& UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(meta
						.getAuthLvl())) {
			// 當是授權內 且為營運中心授權內 其文件 狀態要變更
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());
		}
	}

	/**
	 * 這裡經由起案分行決定分行待覆核、或總行待覆核
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "待補件", value = "to判斷行別")
	public void start2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		meta.setReturnFromBH(null);
		// ObsBrnType 1.海外總行 2.海外一般分行 3.海外簡易分行
		String branchType = branchService.getBranch(meta.getCaseBrId())
				.getObsBrnType();
		if (UtilConstants.obsBrnType.海外總行.equals(branchType)) {
			instance.setAttribute("result", "to總行待覆核");
		} else {
			instance.setAttribute("result", "to分行待覆核");
		}

		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())) {
			// 授權外額度明細表要把額度明細表的文件狀態改為待覆核
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());
			// 授權外簽報書，營運中心退分行更正後，案件要顯示在待更正MENU內，
			// 但若分行修改送呈單位後(例如由呈營運中心改直接送授管處)，則原授權單位(營運中心)就要刪掉
			if (!UtilConstants.Casedoc.AreaChk.送審查.equals(meta.getAreaChk())) {
				if (FlowNameService.FlowbackUnitEnum.營運中心_退回分行.getCode()
						.equals(meta.getBackUnit())) {
					meta.setBackUnit(null);
				}
			}
		} else if (UtilConstants.Casedoc.DocKind.授權內.equals(meta.getDocKind())
				&& UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(meta
						.getAuthLvl())) {
			// 當是授權內 且為營運中心授權內 其文件 狀態要變更
			lmsService.resetL140M01A(meta, FlowDocStatusEnum.待覆核.getCode());
		}
	}

	/**
	 * 判斷核定還是婉卻
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "分行待覆核", value = "to決策")
	public void check(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);

		String result = (String) instance.getAttribute("result");
		if ("to核定".equals(result)) {
			// 檢查主管與經辦是否為同一人
			lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
					UtilConstants.STAFFJOB.經辦L1);
			List<L140M01A> l140m01as = l140m01aDao
					.findL140m01aListByL120m01cMainId(meta.getMainId(),
							UtilConstants.Cntrdoc.ItemType.額度明細表,
							FlowDocStatusEnum.已核准.getCode());
			// 當無已核准額度明細表且 該案件非陳覆陳述案 為婉卻
			if (l140m01as.isEmpty()
					&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
							.getDocCode())) {
				instance.setAttribute("result", "to已婉卻");
			} else {
				instance.setAttribute("result", "to核定");

			}
		}
	}

	/**
	 * 判斷核定還是婉卻
	 * 
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "總行待覆核", value = "to呈核")
	public void check2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = l120m01aDao.findByOid(instanceId);
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(meta.getMainId(),
						UtilConstants.Cntrdoc.ItemType.額度明細表,
						FlowDocStatusEnum.已核准.getCode());

		String result = (String) instance.getAttribute("result");
		if ("to核定".equals(result)) {
			// 檢查主管與經辦是否為同一人
			lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
					UtilConstants.STAFFJOB.經辦L1);
			// 當無已核准額度明細表且 該案件非陳覆陳述案 為婉卻
			if (l140m01as.isEmpty()
					&& !UtilConstants.Casedoc.DocCode.陳復陳述案.equals(meta
							.getDocCode())) {
				instance.setAttribute("result", "to已婉卻");
			} else {
				instance.setAttribute("result", "to核定");
			}
		}
	}

	/**
	 * 退回編製中
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to退回編製中")
	public void backFiste(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 如果執行退回的人不是 原本 起案的要刪除目前執行的人授權檔
		if (!user.getUnitNo().equals(meta.getCaseBrId())) {
			L120A01A l120a01a = l120a01aDao.findByAuthUnit(meta.getMainId(),
					user.getUnitNo());
			if (l120a01a != null) {
				l120a01aDao.delete(l120a01a);
			}
		}
		// 要砍簽章欄
		lmsService.deleteL120M01F(meta.getMainId(), null, new String[] {
				UtilConstants.STAFFJOB.執行覆核主管L4,
				UtilConstants.STAFFJOB.提會登錄經辦L7,
				UtilConstants.STAFFJOB.提會放行主管L8 });
		// 更改額度明細表文件狀態
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 退回編製中
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "呈核", value = "to退回編製中")
	public void backFiste2(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 如果執行退回的人不是 原本 起案的要刪除目前執行的人授權檔
		if (!user.getUnitNo().equals(meta.getCaseBrId())) {
			L120A01A l120a01a = l120a01aDao.findByAuthUnit(meta.getMainId(),
					user.getUnitNo());
			if (l120a01a != null) {
				l120a01aDao.delete(l120a01a);
				// 新增一筆傳送紀錄以供近期已收案件用
				lmsService.saveL000M01A(meta, meta.getCaseBrId());
			}
		}
		lmsService.deleteL120M01F(meta.getMainId(), null, new String[] {
				UtilConstants.STAFFJOB.執行覆核主管L4,
				UtilConstants.STAFFJOB.提會登錄經辦L7,
				UtilConstants.STAFFJOB.提會放行主管L8 });
		// 更改額度明細表文件狀態
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 退回待補件非該分行的案子要刪除授權檔
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "呈核", value = "to待補件")
	public void backFirst(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 如果執行退回的人不是 原本 起案的的 要刪除目前執行的人授權檔
		if (!user.getUnitNo().equals(meta.getCaseBrId())) {
			L120A01A l120a01a = l120a01aDao.findByAuthUnit(meta.getMainId(),
					user.getUnitNo());
			if (l120a01a != null) {
				l120a01aDao.delete(l120a01a);
			}
		}
		// 更改額度明細表文件狀態
		lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 分行待覆核 呈 總行待覆核
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to總行待覆核")
	public void send(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		IBranch branch = branchService.getBranch(meta.getCaseBrId());

		// 下一個編製單位
		String parentBrNo = Util.trim(branch.getParentBrNo());
		String nextOwnbrid = Util.isEmpty(parentBrNo) ? "Y01" : parentBrNo;
		// EFD0053=WARN|覆核人員不可與“經辦人員或其它覆核人員”為同一人|
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid,
				DocAuthTypeEnum.VIEW_TRANSFER.getCode());

		// 新增一筆傳送紀錄以供近期已收案件用
		lmsService.saveL000M01A(meta, nextOwnbrid);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_總行待覆核.getCode(), nextOwnbrid);
	}

	/**
	 * 
	 * 呈授管處
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "呈核", value = "to呈授管處")
	public void sendHead(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 下一個編製單位
		String nextOwnbrid = UtilConstants.BankNo.授管處;
		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnbrid,
				meta.getCaseBrId());
		meta.setOwnBrId(nextOwnbrid);
		if (l700m01a != null) {
			meta.setHqAppraiser(l700m01a.getUserNo());
		}

		// 檢查主管與經辦是否為同一人
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		meta.setHqMeetFlag(null);
		// 新增授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid,
				DocAuthTypeEnum.VIEW_TRANSFER.getCode());
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增一筆傳送紀錄以供近期已收案件用
		lmsService.saveL000M01A(meta, nextOwnbrid);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_呈授管處.getCode(), nextOwnbrid);
		instance.setAttribute("flowCode", LMS1205HeadFlow.FLOW_CODE);

		IBranch theBranch = branchService.getBranch(Util.trim(meta
				.getCaseBrId()));
		// 異常通報Mail風控處與所選參貸行
		if (theBranch != null) {
			String mainId = Util.trim(meta.getMainId());
			List<L130S01B> listL130s01a = l130s01bDao.findByMainId(mainId);
			String brnGroup = Util.trim(theBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				// 營運中心制分行於營運中心覆核時傳送異常通報MAIL給風控處與參貸行
			} else {
				// 非營運中心制分行於分行覆核時就傳送異常通報MAIL給風控處與參貸行
				// 開始進行Mail動作
				if (UtilConstants.Casedoc.DocCode.異常通報
						.equals(meta.getDocCode())) {
					lmsService.sendEmail(mainId, meta, listL130s01a, "0");

					// J-103-0027 核准時設定為需要傳送卡務中心

					// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
					// 但結案註記要等授管處核准時才寫為Y
					L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
					if (l130m01a != null) {
						l130m01a.setNeedSend("Y");

						// J-105-0065-001 Web e-Loan
						// 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
						l130m01a.setSend912MailTime(CapDate
								.getCurrentTimestamp());

						// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
						// LMSServiceImpl.java \insertLnfe0854
						l130m01aDao.save(l130m01a);
					}

				}

			}
		}

		String areack = meta.getAreaChk();
		if (UtilConstants.Casedoc.DocKind.授權外.equals(meta.getDocKind())
				&& !Util.isEmpty(areack)) {
			// 判斷退補件欄位
			lmsService.checkBackUnit(meta);
			meta.setOwnBrId(nextOwnbrid);
			switch (Util.parseInt(areack)) {
			case 1:
				// 否 -直接送"授管處-待收件"
				// 依流程變更目前編制行
				meta.setAreaDocstatus(null);
				meta.setAreaUpdater(null);
				meta.setAreaApprover(null);
				meta.setAreaApprTime(null);
				break;
			case 2:
				lmsService.sendL121M01A(meta);
				meta.setOwnBrId(nextOwnbrid);
				break;
			case 3:
				// 應該都不會跑這
				// 送審查-送營運中心 並增加一筆資料到授權檔該營運中心案件
				meta.setAreaDocstatus(null);
				meta.setAreaUpdater(null);
				meta.setAreaApprover(null);
				meta.setAreaApprTime(null);
				meta.setOwnBrId(meta.getAreaBrId());
				break;
			}
		}

	}

	/**
	 * 
	 * 呈區域中心
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "呈核", value = "to呈區域中心")
	public void sendArea(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		// 判斷退補件欄位
		lmsService.checkBackUnit(meta);
		// 下一個編製單位
		String nextOwnbrid = meta.getAreaBrId();
		meta.setOwnBrId(nextOwnbrid);
		L700M01A l700m01a = l700m01aDao.findByBranchId(nextOwnbrid,
				meta.getCaseBrId());
		if (l700m01a != null) {
			meta.setAreaAppraiser(l700m01a.getUserNo());
		}

		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				CreditDocStatusEnum.海外_呈營運中心.getCode(), nextOwnbrid);
		// 檢查主管與經辦是否為同一人
		lmsService.checkAppraiser(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.經辦L1);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增授權檔
		lmsService.saveL12A01A(meta, nextOwnbrid, UtilConstants.AuthType.送審查);
		// 新增一筆傳送紀錄以供近期已收案件用
		lmsService.saveL000M01A(meta, nextOwnbrid);
		// 要去除多餘的營運中心授權檔，不然在營運中心退過的案件再其營運中心會出現
		lmsService.checkL120A01A(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行,
				UtilConstants.STAFFJOB.執行覆核主管L4);
		
		IBranch theBranch = branchService.getBranch(Util.trim(meta
				.getCaseBrId()));

		if (theBranch != null) {
			String mainId = Util.trim(meta.getMainId());
			List<L130S01B> listL130s01a = l130s01bDao.findByMainId(mainId);
			String brnGroup = Util.trim(theBranch.getBrnGroup());

			if (UtilConstants.Casedoc.DocCode.異常通報.equals(meta.getDocCode())) {
				lmsService.sendEmail(mainId, meta, listL130s01a, "0");

				// J-103-0027 核准時設定為需要傳送卡務中心

				// 為了時效性，營運中心制分行核准送營運中心sendArea或總處分行核准送授管處sendHead時就設定為要傳給卡務
				// 但結案註記要等授管處核准時才寫為Y
				L130M01A l130m01a = l130m01aDao.findByUniqueKey(mainId);
				if (l130m01a != null) {
					l130m01a.setNeedSend("Y");

					// J-105-0065-001 Web e-Loan
					// 授信管理系統修改異常通報副知風控處說明文字及新增統計報表
					l130m01a.setSend912MailTime(CapDate.getCurrentTimestamp());

					// l130m01a.setCloseFg(""); 結案註記要等授管處核准時才寫為Y
					// LMSServiceImpl.java \insertLnfe0854
					l130m01aDao.save(l130m01a);
				}

			}
		}
		
		instance.setAttribute("flowCode", LMS1205AreaFlow.FLOW_CODE);

	}

	// 核定
	@Transition(node = "呈核", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);
	}

	// 已婉卻
	@Transition(node = "呈核", value = "to已婉卻")
	public void reject(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);
	}

	// 授權內核定
	@Transition(node = "決策", value = "to核定")
	public void insideComplete(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_已核准.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);
	}

	// 授權內婉卻
	@Transition(node = "決策", value = "to已婉卻")
	public void insideReject(FlowInstance instance) throws FlowException,
			CapException {
		this.toEndAction(instance, CreditDocStatusEnum.海外_婉卻.getCode(),
				UtilConstants.STAFFJOB.執行覆核主管L4);
	}

	/**
	 * 當案件結束時做的動作
	 * 
	 * @param instance
	 *            flow 流程檔
	 * @param docstatus
	 *            文件狀態
	 * @param staffjob
	 *            職稱欄
	 * @throws CapException
	 * @throws FlowException
	 */
	private void toEndAction(FlowInstance instance, String docstatus,
			String staffjob) throws FlowException, CapException {

		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
				instanceId);
		String docRslt = "";
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			docRslt = UtilConstants.Casedoc.DocRslt.承做;
		} else {
			docRslt = UtilConstants.Casedoc.DocRslt.婉卻;
		}
		lmsService.uploadELLNSEEK(meta);
		meta.setDocRslt(docRslt);
		meta.setOwnBrId(meta.getCaseBrId());

		l120m01aDao.save(meta);
		// 要去除多餘的營運中心授權檔，不然在已核准或已婉卻會多出現
		lmsService.checkL120A01A(meta);
		// 設定分行放行時間
		lmsService.setSentTime(meta);
		// 新增簽章欄
		lmsService.saveL120M01F(meta, UtilConstants.BRANCHTYPE.分行, staffjob);

		lmsService.gfnDB2SetELF442_CNTRNO(meta,
				UtilConstants.Cntrdoc.ACTION.覆核, docstatus);
		lmsService.gfnInsertELF447N(meta, UtilConstants.Cntrdoc.ItemType.額度明細表,
				docstatus, meta.getCaseBrId());
		lmsService.upLoadMIS(meta);
		lmsService.upLnunid(meta);
		
		// J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
		if (CreditDocStatusEnum.海外_已核准.getCode().equals(docstatus)) {
			if (!LMSUtil.isClsCase(meta)) {
				lmsService.uploadL902Data(meta, docstatus);
			}
		}
		

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L120M01A.class;
	}
}