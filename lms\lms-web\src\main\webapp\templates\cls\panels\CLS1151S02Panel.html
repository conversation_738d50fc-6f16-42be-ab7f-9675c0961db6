<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
            <button type="button" id="pullinL140S01A">
                <span class="text-only"><wicket:message key="button.pullin">引進</wicket:message></span>
            </button>
            <button type="button" id="addL140S01A">
                <span class="text-only"><wicket:message key="button.add">新增</wicket:message></span>
            </button>
            <button type="button" id="deleteL140S01A">
                <span class="text-only"><wicket:message key="button.delete">刪除</wicket:message></span>
            </button>
			<br/>
			<span class="text-red">※<wicket:message key="button.mome02">1.選不到從債務人者，請先檢查欲引進的從債務人是否已確實透過本簽報書->借款人基本資料中引入完畢。</wicket:message></span>
            <br/>
			<span class="text-red">※<wicket:message key="button.mome021">2.舊案引入之案件且如性質為[不變]者，從債務人已由原案(資料建檔中)帶入前次內容如后故不需再重引一遍。</wicket:message></span>
            <span id="checkResultSpan" class="text-red" style="display:none;">
				<br/><br/>
				<span id="checkResult"><u style="cursor:pointer;">檢核結果</u></span>
			</span>
			<br/>
			<span id="showPeopleTile" class="field text-red"><wicket:message key="page2.001">本案原</wicket:message></span>&nbsp;&nbsp;
            <b><span id="showPeople" class="field text-green"></span></b>
            <div id="L140S01AGrid" />
            <form action="" id="CLS1151Form02" name="CLS1151Form02">
                <span class="docCode5Show"><b class="star"><wicket:message key="lms.ckeditRemark3">註1:|←建議換行</wicket:message></b>
                    <br/>
                    　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←<textarea name="itemDscrD" id="itemDscrD" cols="100" rows="8" class="docCode5Show"></textarea>
                    <br/>
                    　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←
                </span>
            </form>
            <div id="C120S01ABox" style="display:none">
            	<!--
				分行 增加  從債務人 (l140s01a.type==1)
				-->
                <div>
                    <input type="text" id="findId" name="findId" maxlength="10" size="10" class="upText" />
                    &nbsp;&nbsp;
                    <button type="button" id="findIdBt">
                        <span class="text-only"><wicket:message key="button.filter">篩選</wicket:message></span>
                    </button>
                    <form action="" id="pullinL140S01AForm" name="pullinL140S01AForm">
                    	<table>
                			<tr>
                				<td><wicket:message key="L140S01A.custPos">性質</wicket:message><br/>&nbsp;
                				</td>
                				<td><select name="custPos" id="custPos" itemType="L140S01A_custPos" class="required" /><br/>&nbsp;
                				</td>
								<td>
									<span id="dsbLngeFlagGRsn" class="text-red"></span>
								</td>
                			</tr>
                			<tr>
                				<td><wicket:message key="L140S01A.rKindM">關係類別</wicket:message><br/>&nbsp;
                				</td>
                				<td><select id="rKindM" name="rKindM" class="required" codeType="cls_RelClass" itemStyle="format:{value}-{key}" /><br/>
									<select id="rKindD1" name="rKindD1" class="rKindD required" codeType="Relation_type1" itemStyle="format:{value}-{key}" />
									<select id="rKindD2" name="rKindD2" class="rKindD required" codeType="Relation_type2" itemStyle="format:{value}-{key}" />
									<select id="rKindD31" name="rKindD31" class="rKindD required" codeType="Relation_type31" itemStyle="format:{value}-{key}" />
									<select id="rKindD32" name="rKindD32" class="rKindD required" codeType="Relation_type32" itemStyle="format:{value}-{key}" />
									<span class="isLiveWithBorrowerSpan" style="display:none;">
										&nbsp;&nbsp;&nbsp;
										<wicket:message key="L140S01A.isLiveWithBorrower">是否與借款人同住</wicket:message>&nbsp;
										<label><input name="isLiveWithBorrower" type="radio" value="Y" class="required"/><wicket:message key="yes"/></label>
										<label><input name="isLiveWithBorrower" type="radio" value="N" class="required"/><wicket:message key="no"/></label>
									</span>
									<input type="hidden" id="rKindD" name="rKindD" /><br/>&nbsp;
                				</td>
                			</tr>
                			<tr class='area_reson'>
                				<td><wicket:message key="L140S01A.reson">借保原因</wicket:message>
                				</td>
                				<td><select id="reson" name="reson" class="required" codeType="cls1161m01_reson" />
									<br/> 
									<input type="text" id="resonOther" name="resonOther" class="max required" maxlength="20" size="50" />
                				</td>
                			</tr>
                			<tr class='area_guaPercent'>
                				<td><wicket:message key="L140S01A.guaPercentStr">保證人負担保證責任比率</wicket:message>
                				</td>
                				<td><input type="text" id="guaPercent" name="guaPercent" class="numeric" integer="3" fraction="2" size="6" maxlength="6" vale="" />%
                				</td>
                			</tr>
                    	</table>
                        
                    </form>
                    <div id="C120S01AGrid" />
                </div>
            </div>
            <div id="addL140S01ABox" style="display:none">
            	<!--
				總處單位 可直接增加 從債務人 (l140s01a.type==2)
				-->
                <div id="addL140S01ADiv">
                    <form action="" id="addL140S01AForm" name="addL140S01AForm">
                        <table class="tb2">
                            <tr>
                                <td class="hd1" style="width:25%">
                                    <wicket:message key="L140M01A.custId">借款人統編</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input type="text" id="addL140S01A_custId" name="addL140S01A_custId" class="required upText" maxlength="10" size="10" />
                                    <wicket:message key="L140M01A.dupCode">重複序號</wicket:message>
                                    <input type="text" id="addL140S01A_dupNo" name="addL140S01A_dupNo" class="required upText" size="1" maxlength="1"/>
                                    <button type="button" id="queyBy0024">
                                        <span class="text-only"><wicket:message key="button.search">查詢</wicket:message></span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <wicket:message key="L140M01A.custName">借款人名稱</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input type="text" id="addL140S01A_custName" name="addL140S01A_custName" class="required" maxlength="120" maxlengthC="40"/>
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <wicket:message key="L140S01A.custPos">性質</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <select id="addL140S01A_custPos" name="addL140S01A_custPos" class="required" />
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    <wicket:message key="L140S01A.rKindM">關係類別</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <select id="addL140S01A_rKindM" name="addL140S01A_rKindM" class="required" codeType="cls_RelClass" itemStyle="format:{value}-{key}" /><br/>
									<select id="addL140S01A_rKindD1" name="addL140S01A_rKindD1" class="rKindD required" codeType="Relation_type1" itemStyle="format:{value}-{key}" />
									<select id="addL140S01A_rKindD2" name="addL140S01A_rKindD2" class="rKindD required" codeType="Relation_type2" itemStyle="format:{value}-{key}" />
									<select id="addL140S01A_rKindD31" name="addL140S01A_rKindD31" class="rKindD required" codeType="Relation_type31" itemStyle="format:{value}-{key}" />
									<select id="addL140S01A_rKindD32" name="addL140S01A_rKindD32" class="rKindD required" codeType="Relation_type32" itemStyle="format:{value}-{key}" />
									<span class="isLiveWithBorrowerSpan" style="display:none;">
										&nbsp;&nbsp;&nbsp;
										<wicket:message key="L140S01A.isLiveWithBorrower">是否與借款人同住</wicket:message>&nbsp;
										<label><input name="isLiveWithBorrower" type="radio" value="Y" class="required"/><wicket:message key="yes"/></label>
										<label><input name="isLiveWithBorrower" type="radio" value="N" class="required"/><wicket:message key="no"/></label>
									</span>
									<input type="hidden" id="addL140S01A_rKindD" name="addL140S01A_rKindD" /><br/>&nbsp;
                                </td>
                            </tr>
                            <tr class='area_reson'>
                                <td class="hd1">
                                    <wicket:message key="L140S01A.reson">借保原因</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td>
                                	<select id="addL140S01A_reson" name="addL140S01A_reson" class="required" codeType="cls1161m01_reson" />
									<br/> 
									<input type="text" id="addL140S01A_resonOther" name="raddL140S01A_esonOther" class="max required" maxlength="20" size="50" />
                                </td>
                            </tr>
							<tr class='area_guaPercent'>
                                <td class="hd1">
                                    <wicket:message key="L140S01A.guaPercentStr">保證人負担保證責任比率</wicket:message>&nbsp;&nbsp;									
                                </td>
                                <td>
                                    <input type="text" id="addL140S01A_guaPercent" name="addL140S01A_guaPercent" class="numeric" integer="3" fraction="2" size="6" maxlength="6" vale="" />%
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>
			<div id="checkIsSuspectedHeadAccountResultThickbox" style="display:none">
				<style>
					.ui-jqgrid tr.jqgrow td {
					    white-space: normal !important;
					    height:auto;
					    vertical-align:text-top;
					    padding-top:2px;
					    display: table-cell;
					    vertical-align: middle;
					}
				</style>
				<div id="checkIsSuspectedHeadAccountResultGrid"/>
			</div>
            <script type="text/javascript" src="pagejs/cls/CLS1151S02Panel.js?ver=********"></script>
        </wicket:panel>
    </body>
</html>
