/* 
 * MisLNF030ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;

/**
 * <pre>
 * 引進放款帳號
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          2013/01/07,GaryC<PERSON>,new
 *          </ul>
 */
@Service
public class MisLNF030ServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF030Service {
	
	@Override
	public List<Map<String, Object>> selaLoanAC(String custId, String dupNo,
			String BranchId, String cnrtNo) {
		custId = Util.addSpaceWithValue(custId, 10) + dupNo;
		return this.getJdbc().queryForList("LNLNF030.selaLoanAC",
				new String[] { custId, BranchId, cnrtNo });
	}
	
	@Override
	public List<Map<String, Object>> selaLoanAC_allow_403_603(String custId, String dupNo,
			String branchId, String cnrtNo) {
		custId = Util.addSpaceWithValue(custId, 10) + dupNo;
		return this.getJdbc().queryForList("LNLNF030.selaLoanAC_allow_403_603",
				new String[] { custId, branchId, cnrtNo, cnrtNo });
	}
	
	@Override
	public List<Map<String, Object>> selByCustId(ISearch search, String custId,
			String dupNo) {
		// 借款人=CUSTID ,帳號=LOANNO 利率= CODE 目前餘額：= BAL 額度=AMT:OLNAPPDATE:原貸放日期 :OLNENDDATE:原貸款到期日
		return this.getJdbc().queryForList(
				"MIS.MISLN30_findByLNF030_CONTRACTAndCustId",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo });
	}

	@Override
	public List<MISLN30> selBykey(String custId, String dupNo, String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.MISLN30_findByKey",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });
		List<MISLN30> list = new ArrayList<MISLN30>();
		for (Map<String, Object> row : rowData) {
			MISLN30 model = new MISLN30();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;

	}

	@Override
	public List<MISLN30> selBykeyWithoutCancelDate(String custId, String dupNo, String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.MISLN30_findByKeyWithoutCancelDate",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });
		List<MISLN30> list = new ArrayList<MISLN30>();
		for (Map<String, Object> row : rowData) {
			MISLN30 model = new MISLN30();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;

	}

	@Override
	public BigDecimal selCrsR9_amt(String custIdDupNo, String brNo) {
		Map<String, Object> singleData = this.getJdbc().queryForMap("LNLNF030.selCrsR9_amt",
				new String[] { custIdDupNo, brNo });
		return new BigDecimal(MapUtils.getDoubleValue(singleData, "AMT"));
	}
	@Override
	public BigDecimal selSumLoanBalByCustidBrNo_CrsR9(String custIdDupNo, String brNo) {
		
		Map<String, Object> singleData = this.getJdbc().queryForMap("LNLNF030.selSumLoanBalByCustidBrNo_CrsR9",
				new String[] { custIdDupNo, brNo });
		return new BigDecimal(MapUtils.getDoubleValue(singleData, "SUM_BAL"));		
	}
	
	@Override
	public List<MISLN30> sel_CrsR9(String custIdDupNo, String brNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("LNLNF030.sel_CrsR9"
				, new String[] { custIdDupNo, brNo });
		List<MISLN30> list = new ArrayList<MISLN30>();
		for (Map<String, Object> row : rowData) {
			MISLN30 model = new MISLN30();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
	
	@Override
	public List<Map<String, Object>> selNewLnfData(String custIdDupNo){
		return this.getJdbc().queryForListWithMax("LNLNF030.selNewLnfData",
				new String[] { custIdDupNo, custIdDupNo });
	}
	
	@Override
	public List<Map<String, Object>> selGrpData_FactTypeEQ30(String custIdDupNo, String cntrNo){
		return this.getJdbc().queryForListWithMax("LNLNF030.selGrpData_FactTypeEQ30",
				new String[] { custIdDupNo, cntrNo });
	}
	

	@Override
	public List<Map<String, Object>> selByCustid_CrsR5(String custIdDupNo) {
		return getJdbc().queryForListWithMax("LNLNF030.selByCustid_CrsR5",
				new String[] { custIdDupNo });
	}
	
	@Override
	public MISLN30 selByPk(String loanNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForList("MIS.MISLN30_findLoanNo"
				,new String[] { loanNo});
		List<MISLN30> list = new ArrayList<MISLN30>();
		for (Map<String, Object> row : rowData) {
			MISLN30 model = new MISLN30();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		if(list.size()>0){
			return list.get(0);
		}else{
			return null;
		}		
	}
	
	@Override
	public MISLN30 selByPk_cancelDateNull(String loanNo){
		MISLN30 o = selByPk(loanNo);
		if(o.getLnf030_cancel_date()==null){
			return o;
		}else{
			//已被銷戶
			return null;
		}
	}
	
	@Override
	public List<MISLN30> selByCntrNo(String cntrNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.MISLN30_findCntrNo",
				new String[] { cntrNo });
		List<MISLN30> list = new ArrayList<MISLN30>();
		for (Map<String, Object> row : rowData) {
			MISLN30 model = new MISLN30();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;		
	}
	
	@Override
	public List<MISLN30> selLNF030_findCancelData(String custId, String dupNo, String cntrNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.MISLN30_findCancelData",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });
		List<MISLN30> list = new ArrayList<MISLN30>();
		for (Map<String, Object> row : rowData) {
			MISLN30 model = new MISLN30();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;

	}
	
	@Override
	public List<Map<String, Object>> selaLoanNoByCntrno(String cnrtNo) {
		return this.getJdbc().queryForList("LNLNF030.selaLoanNoByCntrno",
				new String[] { cnrtNo });
	}
	
	@Override
	public Map<String, Object> findCreditDelayPaymentRecord(String custId, String dupNo) {
		return this.getJdbc().queryForMap("LNF030.findCreditDelayPaymentRecord", new Object[] { custId + dupNo });
	}
	
	@Override
	public Map<String, Object> findLabourBailoutDataByCustIdAndLoanDate(String custId, String dupNo, String loanDate) {
		return this.getJdbc().queryForMap("LNF030.findLabourBailoutDataByCustIdAndLoanDate", new Object[] { custId + dupNo, loanDate});
	}

	@Override
	public List<MISLN30> findByCustAndCutrnoAndLoanClass(String custId, String dupNo, String cntrNo, String loanClass) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.MISLN30_findByCustAndCutrnoAndLoanClassWithoutCancelDate",
				new Object[] { Util.addSpaceWithValue(custId, 10) + dupNo, cntrNo, loanClass });
		List<MISLN30> list = new ArrayList<MISLN30>();
		for (Map<String, Object> row : rowData) {
			MISLN30 model = new MISLN30();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;

	}
}
