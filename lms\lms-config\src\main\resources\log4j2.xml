<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn" packages="com.mega.eloan.common.log">
	<Properties>
		<Property name="basePath">/elnfs/logs/${hostName}/lms</Property>
	</Properties>
	
	<Appenders>
		<Routing name="RoutingAppender">
			<Routes pattern="$${ctx:unitno:----}_$${ctx:login:-------}_$${date:yyyyMMdd}">
				<Route>
					<RollingFile name="${ctx:unitno:----}_${ctx:login:-------}_Appender"
						fileName="${basePath}/BRANCHES/${ctx:unitno:----}/${ctx:login:-------}/${date:yyyyMMdd}/${ctx:login:-------}_${date:MMddHHmmssSSS}.log"
						filePattern="${basePath}/BRANCHES/${ctx:unitno:----}/${ctx:login:-------}/${date:yyyyMMdd}/${ctx:login:-------}_%d{MMddHHmmssSSS}.log.%i">
						<PatternLayout pattern="%d [%t] [%X{sessionId}] | %X{login} | %X{reqURI} | %-28.28c{1} [%-5p] %enc{%m}{CRLF}%n" charset="UTF-8" />
						<Policies>
							<SizeBasedTriggeringPolicy size="20 MB"/>
						</Policies>
                        <DefaultRolloverStrategy fileIndex="nomax"/>
					</RollingFile>
				</Route>
			</Routes>
		</Routing>
		<RollingFile name="FILE" fileName="${basePath}/lms.log" filePattern="${basePath}/%d{yyyyMMdd}/lms.log.%i">
            <PatternLayout pattern="%d [%t] [%X{sessionId}] | %X{login} | %X{reqURI} | %-28.28c{1} [%-5p] %enc{%m}{CRLF}%n" charset="UTF-8" />
            <Policies>
            	<TimeBasedTriggeringPolicy interval="1" modulate="true" />
            	<SizeBasedTriggeringPolicy size="20 MB" />
            </Policies>
            <DefaultRolloverStrategy fileIndex="nomax" />
		</RollingFile>
		<Console name="console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d - [%-5p] %enc{%m}{CRLF}%n" />
		</Console>
		<Null name="dummy"></Null>
	</Appenders>
	<Loggers>
		<Root level="debug">
			<AppenderRef ref="${sys:consoleLoggerName:-console}" />
			<AppenderRef ref="FILE" />
			<AppenderRef ref="RoutingAppender" />
		</Root>
        <Logger name="org" level="error" />
        <Logger name="net" level="error" />
        
        <Logger name="tw.com.iisi" level="debug" />
        <Logger name="tw.com.iisi.cap.utils.CapBeanUtil" level="trace" />
        <Logger name="com.mega.eloan.common.gwclient.MonitorClient" level="error" />
        
        <Logger name="tw.com.iisi.cap.base.pages.AbstractCapPage" level="trace" />	<!-- [refs #77] 比照現有 ces-log4j 的設定 for ajax-->
        <Logger name="com.mega.eloan.common.jdbc.EloanJdbcTemplate" level="trace" />
        <Logger name="com.mega.eloan.common.jdbc.EloanNamedJdbcTemplate" level="trace" />
        
        <!-- jdbc gateway -->
        <Logger name="com.mega.eloan.lms.dw.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.eai.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.ejcic.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.eloandb.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.etch.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.megaimage.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.mfaloan.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.obsdb.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.ods.service.impl" level="trace" />
        <Logger name="com.mega.eloan.lms.tej.service.impl" level="trace" />
        <Logger name="com.mega.eloan.common.service.impl" level="trace" />
        
        <!-- apache -->
        <Logger name="org.apache.commons" level="warn" />
        <Logger name="org.apache.velocity" level="warn" />
        
        <!-- springframework -->
        <Logger name="org.springframework" level="warn" />
        <Logger name="org.springframework.security" level="warn" />
        <Logger name="org.springframework.beans.factory" level="warn" />
        <Logger name="org.springframework.beans.factory.support" level="warn" />
        <Logger name="org.springframework.transaction" level="warn" />
        <Logger name="org.springframework.orm.hibernate5" level="warn" />
        
        <!-- hibernate & cache -->
        <Logger name="org.hibernate" level="error" />
        <Logger name="org.hibernate.SQL" level="debug" /> <!-- [refs #77] log 顯示 sql -->
        <Logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="trace" /> <!-- [refs #77] log 顯示參數-->
        
        <!-- others -->
        <Logger name="com.mchange.v2" level="warn" />
        <Logger name="com.mchange.v2.resourcepool.BasicResourcePool" level="trace" /> <!-- [refs #94] for dev c3p0 connection pool usage -->
	</Loggers>
</Configuration>