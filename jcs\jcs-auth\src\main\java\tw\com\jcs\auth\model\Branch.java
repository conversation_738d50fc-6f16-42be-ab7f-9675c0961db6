package tw.com.jcs.auth.model;

import java.io.Serializable;
import java.util.Date;

/**
 * <pre>
 * 分行相關資訊
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public interface Branch extends Serializable {

    /**
     * 取得分行代碼
     * 
     * @return
     */
    String getBrNo();

    /**
     * 取得分行名稱
     * 
     * @return
     */
    String getBrName();

    /** 取得更新人員 **/
    // String getUpdater();
    /**
     * 取得更新日期
     * 
     * @return
     */
    Date getUpdTime();

    /**
     * 取得分行檢查碼
     * 
     * @return
     */
    String getChkNo();

    /**
     * 取得分行住址
     * 
     * @return
     */
    String getAddr();

    /**
     * 取得分行電話
     * 
     * @return
     */
    String getTel();

    /**
     * 取得分行等級
     * 
     * @return
     */
    String getBrClass();

    /**
     * 取得分行英文名稱
     * 
     * @return
     */
    String getEngName();

    /**
     * 取得該分行所在地之本位幣
     * 
     * @return
     */
    String getUseSWFT();

    /**
     * 取得分行名稱簡稱
     * 
     * @return
     */
    String getNameABBR();

    /**
     * 取得１國內２國外３總處４子銀行
     * 
     * @return
     */
    String getBrNoFlag();

    /**
     * 取得１北區２中區３南區４其他
     * 
     * @return
     */
    String getBrNoArea();

    /**
     * 取得經理
     * 
     * @return
     */
    String getAccManager();

    /**
     * 取得單位類別 (從Notes引進)
     * 
     * @return
     */
    String getUnitType();

    /**
     * 取得所屬區域授信中心
     * 
     * @return
     */
    String getBrnGroup();

    /**
     * 取得所屬區域授信中心名稱
     * 
     * @return
     */
    String getBrnGrpName();

    /**
     * 取得國別
     * 
     * @return
     */
    String getCountryType();

    /**
     * 取得時區
     * 
     * @return
     */
    String getTimeZone();

    /**
     * 取得主機編碼方式
     * 
     * @return
     */
    String getHostCodePage();

    /** 取得周一至週五營業開始時間 **/
    // Date getBizStartTime();

    /** 取得周一至週五營業結束時間 **/
    // Date getBizEndTime();

    /**
     * 取得週六是否營業
     * 
     * @return
     */
    String getIsSatOpen();

    /** 取得週六營業開始時間 **/
    // Date getSatStartTime();

    /** 取得週六營業結束時間 **/
    // Date getSatEndTime();

    /**
     * 取得所屬母行
     * 
     * @return
     */
    String getParentBrNo();

    /**
     * 取得預設單位類別 (從Notes引進)
     * 
     * @return
     */
    String getDefUnitType();
}