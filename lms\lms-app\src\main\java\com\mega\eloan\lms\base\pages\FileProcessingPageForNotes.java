package com.mega.eloan.lms.base.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;


/**
 * 提供notes直接下載pdf
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/simple/FileProcessingPageForNotes")
public class FileProcessingPageForNotes extends AbstractFileDownloadPage {
	
	public FileProcessingPageForNotes() {
		super();
	}
	
	@Override
	public String getDownloadFileName() {
		return this.fileDownloadName;
	}

	@Override
	public String getFileDownloadServiceName() {
		return this.serviceName;
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
