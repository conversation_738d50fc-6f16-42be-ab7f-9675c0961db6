/*
 * ADDateFormatter.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.formatter;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapFormatException;

/**
 * <p>
 * 西元年日期Format (yyyy-MM-dd).
 * </p>
 * 
 * <AUTHOR>
 * @version $Revision: 26 $
 * @version
 *          <ul>
 *          <li>2010/7/27,iristu,new
 *          <li>2011/8/02,sunkist,update {@link ADDateTimeFormatter#reformat(Object)} for Calendar.
 *          <li>2011/9/06,tammy<PERSON>, handle null Object
 *          </ul>
 */
@SuppressWarnings("serial")
public class ADDateFormatter implements IFormatter {

    /*
     * 日期格式化
     * 
     * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
     */
    @Override
    @SuppressWarnings("unchecked")
    public String reformat(Object in) throws CapFormatException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        if (in != null && !"".equals(in)) {
            if (in instanceof Calendar) {
                in = ((Calendar) in).getTime();
            } else if (in instanceof String) {
                return df.format(df.parse((String) in, new ParsePosition(0)));
            }
            return df.format(in);
        } else {
            return CapConstants.EMPTY_STRING;
        }
    }

}
