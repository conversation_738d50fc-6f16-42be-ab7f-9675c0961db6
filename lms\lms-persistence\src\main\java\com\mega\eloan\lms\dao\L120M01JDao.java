/* 
 * L120M01JDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01J;

/** 應收帳款買方額度資訊主檔 **/
public interface L120M01JDao extends IGenericDao<L120M01J> {

	L120M01J findByOid(String oid);

	List<L120M01J> findByMainId(String mainId);

	List<L120M01J> findByMainIdAndType(String mainId, String type);

	L120M01J findByMainIdTypeCustIdCntrNo(String mainId, String type,
			String custId, String dupNo, String custId2, String dupNo2,
			String cntrNo);

	L120M01J findByIndex01(String mainId, String type, String custId,
			String dupNo, String custId2, String dupNo2, String cntrNo);

	List<L120M01J> findByIndex02(String mainId);

	List<L120M01J> findByIndex03(String mainId, String type);

	List<L120M01J> findByMainIdTypeCustId(String mainId, String type,
			String custId, String dupNo);

	L120M01J findByMainIdTypeCustIdItemSeq(String mainId, String type,
			String custId, String dupNo, Integer itemSeq);

	List<L120M01J> findByMainIdTypeCustId2(String mainId, String type,
			String custId, String dupNo);
}