/* 
 * C101S01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import com.mega.eloan.lms.model.C101S02C;
import com.mega.eloan.lms.model.C120S02C;

import tw.com.iisi.cap.dao.IGenericDao;


/** 系統初審 **/
public interface C120S02CDao extends IGenericDao<C120S02C> {

	C120S02C findByOid(String oid);

	List<C120S02C> findByMainId(String mainId);

	C120S02C findByUniqueKey(String mainId, String custId, String dupNo);

	List<C120S02C> findByIndex01(String mainId, String custId, String dupNo);

	List<C120S02C> findByCustIdDupId(String custId, String DupNo);

	C120S02C findC120S02C_idDup_latestOne(String custId, String dupNo);
	C120S02C findC120S02C_idDup_latestOne(String mainId);

	int deleteByOid(String oid);
}