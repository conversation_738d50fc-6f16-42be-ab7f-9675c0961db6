/* 
 * NumberServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import java.io.Serializable;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.model.ErrorCode;
import com.mega.eloan.common.service.ErrorCodeService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信給號程式
 * </pre>
 * 
 * @since 2011/9/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/2,Fantasy,new
 *          </ul>
 */
@Service("NumberService")
public class NumberServiceImpl extends AbstractEloandbJdbc implements
		NumberService {

	private static final Logger logger = LoggerFactory
			.getLogger(NumberServiceImpl.class);

	private HashMap<String, JSONObject> container = new LinkedHashMap<String, JSONObject>();
	// 核准文號使用
	private HashMap<String, Integer> containerForEndDate = new LinkedHashMap<String, Integer>();

	private static final int freshMax = 5; // 重新取號最大次數

	@Resource
	L120M01ADao dao;

	@Resource
	L180M01ADao l180m01adao;

	@Resource
	C240M01ADao c240m01adao;

	@Resource
	BranchService branch;

	@Resource
	EloandbBASEService eloandbService;

	@Resource
	private ErrorCodeService errorCodeService;
	
    @Autowired
    private MessageSource messageSource;
	
	// @PostConstruct
	@Override
	public synchronized void init() {
		long t1 = System.currentTimeMillis();
		logger.info("授信給號程式初始化");

		logger.info("授信簽報書主檔取號初始化(JPQL)");
		init_L120M01A_JPQL();
		init_L180M01A_JPQL();
		init_C240M01A_JPQL();
		// 取得核准文號
		init_L120M01A_SqlByEndDate();
		// logger.info("授信簽報書主檔取號初始化(JDBC)");
		// init_L120M01A();
		logger.info(StrUtils.concat("init() all COST= ",
				(System.currentTimeMillis() - t1), " ms"));
		logger.info("授信給號程式初始化完成!");
	}

	@Override
	public synchronized int getNumber(String date) {
		int seq = 0;
		/*
		 * if (containerForEndDate.containsKey(date)) { seq =
		 * containerForEndDate.get(date) + 1; } else { seq = 1;
		 * containerForEndDate.put(date, 1); }
		 */

		// 核准文號只有在授管處給號
		seq = Util.parseInt(getNumber(date, UtilConstants.BankNo.授管處,
				"L140M01A"));

		return seq;
	}

	@Override
	public synchronized int getNumber(String date, String brNo) {
		int seq = 0;
		/*
		 * if (containerForEndDate.containsKey(date)) { seq =
		 * containerForEndDate.get(date) + 1; } else { seq = 1;
		 * containerForEndDate.put(date, 1); }
		 */

		// 核准文號只有在授管處給號
		seq = Util.parseInt(getNumber(date, brNo, "L140M01A"));
		return seq;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public synchronized String getNumber(Class clazz) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String branchId = Util.trim(user.getUnitNo());
		return getNumber(clazz, branchId);
	}

	@SuppressWarnings("rawtypes")
	@Override
	// public String getNumber() {
	public synchronized String getNumber(Class clazz, String BranchId) {
		return getNumber(clazz, BranchId, null);
	}

	@SuppressWarnings("rawtypes")
	@Override
	// public String getNumber() {
	public synchronized String getNumber(Class clazz, String BranchId,
			String year) {
		String currentYear = (Util.isEmpty(year) ? TWNDate.toAD(new Date())
				.substring(0, 4) : year);
		String result = null;
		if (clazz != null) {
			String className = clazz.getSimpleName();
			result = getNumber(currentYear, BranchId, className);

			/*
			 * JSONObject classJson = container.get(className); if (classJson ==
			 * null) { classJson = new JSONObject(); container.put(className,
			 * classJson); } JSONObject YearJson = (JSONObject)
			 * classJson.get(currentYear); if (YearJson == null) { YearJson =
			 * new JSONObject(); classJson.put(currentYear, YearJson); } int
			 * count = Util.parseInt((String) YearJson.get(BranchId)); result =
			 * String.valueOf(++count); YearJson.put(BranchId, result);
			 * classJson.put(currentYear, YearJson); container.put(className,
			 * classJson);
			 */
		}

		if (clazz == L120M01A.class) {
			result = Util.addZeroWithValue(result, 5);
		}

		return result;
	}

	@Override
	@SuppressWarnings("rawtypes")
	public String getCaseNumber(Class clazz, String branchId,
			String year, String number) {
		String kind = "";
		if (clazz == L180M01A.class || clazz == C240M01A.class) {
			kind = UtilConstants.WORD.覆審;
		} else if (clazz == L120M01A.class) {
			kind = UtilConstants.WORD.授;
		}

		IBranch ibranch = branch.getBranch(branchId);
		String branchName = Util.isEmpty(ibranch) ? "" : ibranch.getNameABBR();
		
		Object[] params = new Object[] { year, branchName, kind, number };
		String caseNumber = messageSource.getMessage("caseNumber", params, "caseNumber", LocaleContextHolder.getLocale());
		return caseNumber;
	}

	@Override
	public String getNumberWithMax(Class<?> clazz, String branchId, String year, int maxVal) 
	throws CapMessageException{
		String currentYear = (Util.isEmpty(year) ? TWNDate.toAD(new Date())
				.substring(0, 4) : year);
		String result = null;
		if (clazz != null) {
			String className = clazz.getSimpleName();
			result = getNumberWithMax(currentYear, branchId, className, maxVal);
		}

		if (clazz == L120M01A.class) {
			result = Util.addZeroWithValue(result, 5);
		}

		return result;
	}
	
	private String getNumberWithMax(String year, String brno, String name, int maxVal) 
	throws CapMessageException{
		NumItem ni = new NumItem();
		String date = Util.trim(year);
		if (CapDate.validDate(date, "yyyy-MM-dd")) {
			ni.setYear(CapDate.getDate(date, "yyyy-MM-dd"));
		} else {
			date = Util.trim(year) + "-01-01";
			if (CapDate.validDate(date, "yyyy-MM-dd"))
				ni.setYear(CapDate.getDate(date, "yyyy-MM-dd"));
		}
		ni.setBrno(brno);
		ni.setName(name);
		ni.setNumber(0);
		ni.setServer(getServerIP());
		ni.setUpdater(MegaSSOSecurityContext.getUserId());

		return getNumber(ni, 0, maxVal);
	}

	private String getNumber(NumItem ni, int count, int maxVal) 
	throws CapMessageException{
		String result = null;

		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"NUMBER.getMaxNumber",
				new Object[] { ni.getYear(), ni.getBrno(), ni.getName() });

		if (list.size() != 0 && count < freshMax) {
			Map<String, Object> map = list.get(0);
			ni.setNumber(Util.parseInt(map.get("NUMBER")) + 1);
			if(ni.getNumber()>maxVal){
				ErrorCode errCode = errorCodeService.getErrorCode("EFD9218", LocaleContextHolder.getLocale().toString());
				throw new CapMessageException(errCode.getMessage(), getClass());
			}
			try {
				this.getJdbc().update(
						"NUMBER.insert",
						new Object[] { ni.getYear(), ni.getBrno(),
								ni.getName(), ni.getNumber(), ni.getServer(),
								ni.getUpdater() });
				result = Util.trim(ni.getNumber());
				logger.info("授信系統取號成功!");
			} catch (GWException gwe) {
				++count;
				logger.info("第 " + count + " 次重新取號");
				try {
					Thread.sleep(500); // 延遲0.5秒
				} catch (InterruptedException e) {

				}
				result = getNumber(ni, count);
			}
		} else {
			logger.error("授信系統取號失敗!");
			// throw new GWException(e, causeClass, this.gatewayType);
		}

		return result;
	}
	
	@SuppressWarnings("rawtypes")
	public void setNumber(Class clazz, String BranchId, int count) {
		setNumber(clazz, BranchId, count, null);
	}

	@SuppressWarnings("rawtypes")
	public void setNumber(Class clazz, String BranchId, int count, String year) {
		String currentYear = (Util.isEmpty(year) ? TWNDate.toAD(new Date())
				.substring(0, 4) : year);
		String result = null;
		if (clazz != null && !Util.isEmpty(BranchId)) {
			String className = clazz.toString();
			JSONObject classJson = container.get(className);
			if (classJson == null) {
				classJson = new JSONObject();
				container.put(className, classJson);
			}
			JSONObject yearJson = (JSONObject) classJson.get(currentYear);
			if (yearJson == null) {
				yearJson = new JSONObject();
				classJson.put(currentYear, yearJson);
			}
			result = String.valueOf(count);
			yearJson.put(BranchId, result);
			// classJson.put(currentYear, yearJson);
			// container.put(className, classJson);
		}
	}

	/**
	 * 透過JDBC取得授信簽報書初始化號碼
	 */
	@Deprecated
	@SuppressWarnings({ "rawtypes" })
	public void init_L120M01A() {
		String param = TWNDate.toAD(new Date()).substring(0, 4);
		// List rows = jts.getEloanJdbc().queryForList("L120M01A.getMaxCaseSeq",
		// args);
		// List rows = eloanJdbcTemplate.queryForList("L120M01A.getMaxCaseSeq",
		// args);

		List rows = this.eloandbService.findL120M01A_MaxCaseSeq(param);

		Iterator it = rows.iterator();
		while (it.hasNext()) {
			Map dataMap = (Map) it.next();
			String branchId = Util.trim((String) dataMap.get("CASEBRID"));
			int count = Util.parseInt((String) dataMap.get("SEQ"));
			setNumber(L120M01A.class, branchId, count);
		}
	}

	/**
	 * 透過JPQL取得授信簽報書初始化號碼
	 */
	public void init_L120M01A_JPQL() {
		// EX:01
		List<Object[]> list = dao.findMaxCaseSeq();
		for (Object[] row : list) {
			String caseBrid = (String) row[0];
			Integer caseSeq = (Integer) row[1]; // if caseSeq is Integer
			setNumber(L120M01A.class, caseBrid, (int) caseSeq);
		}

		// EX:02
		// List<L120M01A> list2 = dao.findMaxCaseSeq2();
		// for (L120M01A model : list2) {
		// if (model != null) {
		// String caseBrid = Util.trim(model.getCaseBrId());
		// Integer caseSeq = model.getCaseSeq(); // if caseSeq is Integer
		// setNumber(L120M01A.class, caseBrid, (int) caseSeq);
		// }
		// }
	}

	public void init_L180M01A_JPQL() {
		List<Object[]> list = l180m01adao.findMaxbatchNO();
		for (Object[] model : list) {
			if (model != null) {
				String branchId = (String) model[0];
				Integer batchNO = (Integer) model[1];
				String year = TWNDate.toAD((Date) model[2]).substring(0, 4);
				setNumber(L180M01A.class, branchId, (int) batchNO, year);
			}
		}
	}

	public void init_C240M01A_JPQL() {
		List<Object[]> list = c240m01adao.findMaxbatchNO();
		for (Object[] model : list) {
			if (model != null) {
				String branchId = (String) model[0];
				Integer batchNO = (Integer) model[1];
				String year = TWNDate.toAD((Date) model[2]).substring(0, 4);
				setNumber(C240M01A.class, branchId, (int) batchNO, year);
			}
		}
	}

	/**
	 * 取得 授權外核准日期 最大的編號
	 */
	public void init_L120M01A_SqlByEndDate() {
		// EX:01
		List<Object[]> list = dao.findMaxEndDateSeq();
		for (Object[] row : list) {
			String date = CapDate.formatDate((Date) row[0],
					UtilConstants.DateFormat.YYYY_MM_DD);
			Integer caseSeq = (Integer) row[1]; // if caseSeq is Integer
			containerForEndDate.put(date, caseSeq);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.NumberService#getNumber(java.lang.String,
	 * java.lang.String, java.lang.String)
	 */
	private String getNumber(String year, String brno, String name) {
		NumItem ni = new NumItem();
		String date = Util.trim(year);
		if (CapDate.validDate(date, "yyyy-MM-dd")) {
			ni.setYear(CapDate.getDate(date, "yyyy-MM-dd"));
		} else {
			date = Util.trim(year) + "-01-01";
			if (CapDate.validDate(date, "yyyy-MM-dd"))
				ni.setYear(CapDate.getDate(date, "yyyy-MM-dd"));
		}
		ni.setBrno(brno);
		ni.setName(name);
		ni.setNumber(0);
		ni.setServer(getServerIP());
		ni.setUpdater(MegaSSOSecurityContext.getUserId());

		return getNumber(ni, 0);
	}

	private String getNumber(NumItem ni, int count) {
		String result = null;

		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"NUMBER.getMaxNumber",
				new Object[] { ni.getYear(), ni.getBrno(), ni.getName() });

		// new Object[] { ni.getYear(), ni.getBrno(), ni.getName(),
		// ni.getYear(), ni.getBrno(), ni.getName() });

		if (list.size() != 0 && count < freshMax) {
			Map<String, Object> map = list.get(0);
			ni.setNumber(Util.parseInt(map.get("NUMBER")) + 1);
			// ni.setNumber(Util.parseInt(map.get("NUMBER")));
			try {
				this.getJdbc().update(
						"NUMBER.insert",
						new Object[] { ni.getYear(), ni.getBrno(),
								ni.getName(), ni.getNumber(), ni.getServer(),
								ni.getUpdater() });
				result = Util.trim(ni.getNumber());
				logger.info("授信系統取號成功!");
			} catch (GWException gwe) {
				++count;
				logger.info("第 " + count + " 次重新取號");
				try {
					Thread.sleep(500); // 延遲0.5秒
				} catch (InterruptedException e) {

				}
				result = getNumber(ni, count);
			}
		} else {
			logger.error("授信系統取號失敗!");
			// throw new GWException(e, causeClass, this.gatewayType);
		}

		return result;
	}

	private String getServerIP() {
		String result = null;
		InetAddress ip;
		try {
			ip = InetAddress.getLocalHost();
			result = ip.getHostAddress();
		} catch (UnknownHostException e) {
			// logger.error(e.getMessage());
		}

		return Util.trim(result);
	}

	public static class NumItem implements Serializable {

		private static final long serialVersionUID = 7329433370534984288L;

		Date year;

		String brno;

		String name;

		int number;

		String server;

		String updater;

		public Date getYear() {
			return year;
		}

		public void setYear(Date year) {
			this.year = year;
		}

		public String getBrno() {
			return Util.trim(brno);
		}

		public void setBrno(String brno) {
			this.brno = brno;
		}

		public String getName() {
			return Util.trim(name);
		}

		public void setName(String name) {
			this.name = name;
		}

		public int getNumber() {
			return number;
		}

		public void setNumber(int number) {
			this.number = number;
		}

		public String getServer() {
			return Util.trim(server);
		}

		public void setServer(String server) {
			this.server = server;
		}

		public String getUpdater() {
			return Util.trim(updater);
		}

		public void setUpdater(String updater) {
			this.updater = updater;
		}
	}

}
