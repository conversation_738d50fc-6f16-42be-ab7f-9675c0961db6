package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONNull;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.gwclient.DWUCB1FTPClient;
import com.mega.eloan.common.gwclient.SMEGFTPClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.EMapService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS3501Service;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C124M01ADao;
import com.mega.eloan.lms.dao.C125M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisIcbcBrService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.eloan.lms.model.C125M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 勞工紓困進度資料傳送notes
 * 增加舒困2.0
 * </pre>
 * 
 * <AUTHOR>
 * @version <ul>
 * <li>2020/5/13,007625,new</li>
 * </ul>
 * @since 2020/5/13
 */

@Service("clslaborreliefbatchserviceimpl")
public class ClsLaborReliefBatchServiceImpl extends AbstractCapService implements WebBatchService {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	EMapService eMapService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	DWUCB1FTPClient dwucb1FTPClient;

	@Resource
	SMEGFTPClient smegFtpClient;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	C124M01ADao c124M01ADao;

	@Resource
	C125M01ADao c125M01ADao;

	@Resource
	MisIcbcBrService misIcbcBrService;

	@Resource
	CLS3501Service cls3501Service;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	C122M01ADao c122m01aDao;

	@Resource
	CLSService clsService;

	// 可接受的檔案名稱
	private String[] acceptFileNames = new String[] { "NT131FEEDBACK", "NT145FEEDBACK", "WLFEEDBACK", "WLFBRSLT", "NTTCFEEDBACK" };

	private String 申請書 = "WL.TXT";
	private String 第一次勞工紓困通知單 = "NT131.TXT";
	private String 第二次勞工紓困通知單 = "NT145.TXT";
	private String 勞工紓困還款通知介接檔 = "NTTC.TXT";

	@Override
	public JSONObject execute(JSONObject json) {
		// 1.勞工紓困資料傳送notes
		// 2.勞工紓困申請書
		// 3_1.勞工紓困申請書update
		// 3_2.勞工紓困申請書update
		// 4.勞工紓困通知單
		// 5.勞工紓困通知單updte
		JSONObject result = null;
		JSONObject rq = json.getJSONObject("request");
		String type = rq.getString("type");
		if ("1".equals(type)) {
			result = job1();
		} else if ("2".equals(type)) {
			result = job2();
		} else if ("4".equals(type)) {
			String mustRun = rq.optString("mustRun");
			result = job4(mustRun);
		} else if ("ftpDownload".equals(type)) {
			result = ftpGetAllFiles();
		} else if ("3_1".equals(type)) {
			String fileName = rq.getString("f");
			result = job3_1(fileName);
		} else if ("3_2".equals(type)) {
			String fileName = rq.getString("f");
			result = job3_2(fileName);
		} else if ("5_1".equals(type)) {
			String fileName = rq.getString("f");
			result = job5_1(fileName);
		} else if ("moveToBk".equals(type)) {
			result = moveFileToBk();
		} else if ("job3All".equals(type)) {
			result = job3_ALL();
		} else if ("job5All".equals(type)) {
			result = job5_ALL();
		} else if ("6".equals(type)) {
			// 產生第二次勞工紓困申請書
			result = job6();
		} else if ("7".equals(type)) {
			// 產生第二次勞工紓困通知單
			String mustRun = rq.optString("mustRun");
			result = job7(mustRun);
		} else if ("8".equals(type)) {//J-110-0301 勞工紓困還款報送信保
			// FTP給信保
			String assigndate_start = rq.optString("assigndate_start");
			String assigndate_end = rq.optString("assigndate_end");
			result = job8(assigndate_start, assigndate_end);
		} else if ("9".equals(type)) {//J-110-0301 勞工紓困還款報送信保
			// 從信保收檔回寫LNF630(指定檔案)
			String fileName = rq.getString("f");
			result = job9(fileName);
		} else if ("9_ALL".equals(type)) {//J-110-0301 勞工紓困還款報送信保
			// 從信保收檔回寫LNF630
			result = job9_ALL();
		}

		return result;
	}

	private JSONObject job1() {
		JSONObject result;

		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		int month = today.get(Calendar.MONTH) + 1;
		int date = today.get(Calendar.DATE);

		final String REPORT_NAME = "COVID_ELOAN_STUS.TXT";
		String fileName = REPORT_NAME + "." + year + String.format("%02d", month) + String.format("%02d", date);

		List<String> outList = new ArrayList<String>();


		//取得勞工舒困的目前版本，沒想到會有第二次的勞工舒困
		String c124m01aVer = Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER"));
		Integer verNo = (Util.isNumeric(c124m01aVer) ? NumberUtils.toInt(c124m01aVer) : 0);
		Date labor_ver2_date = CapDate.parseDate(
				Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER_DATE")));

		List<Map<String, Object>> laborReliefPackage = eloandbBASEService.getLaborReliefPackage(labor_ver2_date);

		for (Map<String, Object> laborDatas : laborReliefPackage) {
			String custId = Util.trim(MapUtils.getString(laborDatas, "CUSTID", ""));
			String statflag = Util.trim(MapUtils.getString(laborDatas, "STATFLAG", ""));
			outList.add(custId + "," + statflag);
		}

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + year + File.separator;

		if (outList.size() > 0) {
			try {
				File txtFile = new File(localFilePath, fileName);
				FileUtils.writeLines(txtFile, "BIG5", outList, "\r\n");
				String msgId = IDGenerator.getUUID();

				logger.info("[execute] FTP Client : {}", dwucb1FTPClient.toString());

				dwucb1FTPClient.send(msgId, txtFile.toString(), "/dw/ftpdata/ftpucb1", REPORT_NAME, false, true,
						false);
				logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder.reflectionToString(
						dwucb1FTPClient.list(msgId, dwucb1FTPClient.getServerDir())));
			} catch (Exception ex) {
				result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
				result.element(WebBatchCode.P_RESPONSE,
						this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
				logger.error(ex.getMessage(), ex);
				return result;
			}
		} else {
			logger.info("LMS.C122M01A is empty");
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}


	private JSONObject job2() {

		logger.info("----------產生勞動部勞工紓困貸款申請書介接檔-----------");

		JSONObject result;
		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		int month = today.get(Calendar.MONTH) + 1;
		int date = today.get(Calendar.DATE);
		int hour = today.get(Calendar.HOUR_OF_DAY);

		final String REPORT_NAME = "017";
		String fileName = REPORT_NAME + "" + (year - 1911) + String.format("%02d", month) + String.format("%02d",
				date) + String.format("%02d", hour) + "WL.TXT";

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ" + File.separator;


		ISearch searchTemplete = c124M01ADao.createSearchTemplete();
		searchTemplete.addSearchModeParameters(SearchMode.IS_NULL, "grntPaper", null);
		searchTemplete.addSearchModeParameters(SearchMode.EQUALS, "docStatus", "05O");
		searchTemplete.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		searchTemplete.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.IS_NULL, "dataStatus", null),
				new SearchModeParameter(SearchMode.EQUALS, "dataStatus", ""));

		searchTemplete.addOrderBy("ownBrId");
		searchTemplete.addOrderBy("custId");
		searchTemplete.setMaxResults(Integer.MAX_VALUE);
		List<C124M01A> c124m01as = c124M01ADao.find(searchTemplete);

		logger.info("c124m01a 資料筆數 {}", c124m01as.size());


		//取得該銀行的bankCode的資料
		List<Map<String, Object>> synBankList = misdbBASEService.findSynBankList("01", "017");
		Map<String, String> synbankcode = new HashMap<String, String>();

		for (Map<String, Object> data : synBankList) {
			String brnno = MapUtils.getString(data, "BRNNO", "");
			String brbkno = MapUtils.getString(data, "BRBKNO", "");
			synbankcode.put(brnno, brbkno);
		}

		JSONArray ja = new JSONArray();
		/*
		 * 取得本分行對信保保縣市的轉換代碼
		 */
		Map<String, String> cityMap = getCityMap();
		for (C124M01A c124m01a : c124m01as) {
			GINQF0WL ginqf0WL = new GINQF0WL(c124m01a, synbankcode, cityMap);
			JSONObject ginqf0WLjs = JSONObject.fromObject(ginqf0WL.toJson());
			ja.add(ginqf0WLjs);
		}


		if (ja.size() > 0) {
			try {
				File txtFile = new File(localFilePath, fileName);
				FileUtils.write(txtFile, ja.toString(), "UTF8");
				Timestamp now = CapDate.getCurrentTimestamp();

				for (C124M01A c124m01a : c124m01as) {
					c124m01a.setDataStatus("X");
					c124m01a.setBatchDate(now);
					cls3501Service.save(c124m01a);
				}
				String msgId = IDGenerator.getUUID();

				logger.info("[execute] FTP Client : {}", smegFtpClient.toString());

				smegFtpClient.send(msgId, txtFile.toString(), smegFtpClient.getServerDir() + "PAY_SEND", fileName,
						false, true, false);
				logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder.reflectionToString(
						smegFtpClient.list(msgId, smegFtpClient.getServerDir() + "PAY_SEND")));
			} catch (Exception ex) {
				result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
				result.element(WebBatchCode.P_RESPONSE,
						this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
				logger.error(ex.getMessage(), ex);
				return result;
			}
		} else {
			logger.info("LMS.C124M01A is empty");
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}


	private JSONObject job3_ALL() {
		JSONObject result;


		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File bkDupFolder = new File(localFilePath + "bkDup");

		File rootFolder = new File(localFilePath);

		File[] wlfeedbacks = rootFolder.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory()) {
					return false;
				} else if (pathname.getName().toUpperCase().contains("WLFEEDBACK")) {
					return true;
				} else {
					return false;
				}
			}
		});

		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		int year = cal.get(Calendar.YEAR) - 1911;
		int month = cal.get(Calendar.MONTH) + 1;
		int day = cal.get(Calendar.DATE);
		String today = year + String.format("%02d", month) + String.format("%02d", day);

		if (wlfeedbacks != null && wlfeedbacks.length > 0) {
			Arrays.sort(wlfeedbacks);
			for (File wlfeedback : wlfeedbacks) {

				// 因為信保ftp不給6組搬檔，而六組只留2個月記錄，導至六組過了2個月後，就又會收到以前的檔，所以這些檔就不處理了
				// 用檔名來判，如果檔名不是今天就不處理
				String fileName = wlfeedback.getName();
				String fileDate = StringUtils.substring(fileName, 3, 10);
				if (fileDate.equals(today)) {
					JSONObject rs = job3_1(wlfeedback.getName());
					if (rs.getInt(WebBatchCode.P_RC) != 0) {
						return rs;
					}
				} else {
					try {
						logger.info(wlfeedback.getName() + " 非今日檔案，刪除不處理");

						if (!bkDupFolder.exists()) {
							FileUtils.forceMkdir(bkDupFolder);
						}
						FileUtils.copyFile(wlfeedback, new File(bkDupFolder.toString(), wlfeedback.getName()));
						FileUtils.forceDelete(wlfeedback);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}


			}
		}

		File[] wlfbrslts = rootFolder.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory()) {
					return false;
				} else if (pathname.getName().toUpperCase().contains("WLFBRSLT")) {
					return true;
				} else {
					return false;
				}
			}
		});


		if (wlfbrslts != null && wlfbrslts.length > 0) {
			Arrays.sort(wlfbrslts);
			for (File wlfbrslt : wlfbrslts) {
				String fileName = wlfbrslt.getName();
				String fileDate = StringUtils.substring(fileName, 3, 10);
				if (fileDate.equals(today)) {
					JSONObject rs = job3_2(wlfbrslt.getName());
					if (rs.getInt(WebBatchCode.P_RC) != 0) {
						return rs;
					}
				} else {
					try {
						logger.info(wlfbrslt.getName() + " 非今日檔案，刪除不處理");

						if (!bkDupFolder.exists()) {
							FileUtils.forceMkdir(bkDupFolder);
						}
						FileUtils.copyFile(wlfbrslt, new File(bkDupFolder.toString(), wlfbrslt.getName()));
						FileUtils.forceDelete(wlfbrslt);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}

			}
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");

		return result;


	}

	private JSONObject moveFileToBk() {
		JSONObject result;
		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File root = new File(localFilePath);
		File[] files = root.listFiles();
		if (files != null && files.length > 0) {
			for (File file : files) {
				if (file.renameTo(new File(localFilePath + File.separator + "bk", file.getName()))) {

				} else {
					file.renameTo(new File(localFilePath + File.separator + "bk", file.getName() + "bk"));
				}

			}
		}
		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;

	}

	private JSONObject job3_1(String file) {

		logger.info("----------讀取勞動部勞工紓困貸款整批回饋檔-----------");

		JSONObject result;

		if (!file.toUpperCase().contains("WLFEEDBACK")) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + "檔名" + file + "是錯誤的");
			return result;
		}


		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File bkFolder = new File(localFilePath + "bk");
		File inFile = new File(localFilePath, file);

		try {
			List<String> inTxts = FileUtils.readLines(inFile);
			String log = inTxts.get(0);
			logger.info("log result {}", log);

			String jsonTxt = inTxts.get(1);

			JSONArray jsonArrayLogs = JSONArray.fromObject(jsonTxt);

			//解析信保回傳的處理日期
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");

			//取得勞工舒困的目前版本，沒想到會有第二次的勞工舒困
			String c124m01aVer = Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER"));
			Integer verNo = (Util.isNumeric(c124m01aVer) ? NumberUtils.toInt(c124m01aVer) : 0);

			for (int i = 0; i < jsonArrayLogs.size(); i++) {
				JSONObject jsLog = jsonArrayLogs.getJSONObject(i);
				String idNo = jsLog.optString("IdNo");
				String dataStatus = jsLog.optString("DataStatus");
				String receiveDay = String.valueOf(jsLog.getInt("ReceiveDay"));
				String receiveTime = StringUtils.leftPad(String.valueOf(jsLog.getInt("ReceiveTime")), 6, "0");
				receiveDay = (Integer.parseInt(receiveDay.substring(0, 3)) + 1911) + StringUtils.right(receiveDay, 4);

				String description = jsLog.optString("Description");
				if ("null".equals(description)) {
					description = null;
				} else {
					if (description.getBytes("UTF8").length > 600) {
						description = new String(description.getBytes(), 0, 600, "UTF8");
					}
				}

				List<C124M01A> c124m01as = c124M01ADao.findByCustIdAndDupNo(idNo, null, verNo);
				if (c124m01as != null && c124m01as.size() > 0) {
					for (C124M01A c124m01a : c124m01as) {
						c124m01a.setDataStatus(dataStatus);
						c124m01a.setDescription(description);
						String receiveDate = receiveDay + receiveTime;
						try {
							Date _receiveDate = sdf.parse(receiveDate);
							c124m01a.setReceiveDay(new Timestamp(_receiveDate.getTime()));
						} catch (ParseException e) {
							logger.info(e.toString());
						}

						cls3501Service.save(c124m01a);
					}

				} else {
					logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@信保回傳統編" + idNo + "在C124M01A內查無資料");
				}
			}

			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}
			//將執行完成的檔案備份到bk
			FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			FileUtils.forceDelete(inFile);

		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}


	private JSONObject job3_2(String file) {
		logger.info("----------讀取勞動部勞工紓困貸款整批核保情形回饋檔-----------");

		JSONObject result;


		if (!file.toUpperCase().contains("WLFBRSLT")) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + "檔名" + file + "是錯誤的");
			return result;
		}

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File bkFolder = new File(localFilePath + "bk");
		File inFile = new File(localFilePath, file);


		try {
			List<String> inTxts = FileUtils.readLines(inFile);
			String jsonTxt = inTxts.get(0);

			JSONArray jsonArrayLogs = JSONArray.fromObject(jsonTxt);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");

			//取得勞工舒困的目前版本，沒想到會有第二次的勞工舒困
			String c124m01aVer = Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER"));
			Integer verNo = (Util.isNumeric(c124m01aVer) ? NumberUtils.toInt(c124m01aVer) : 0);

			for (int i = 0; i < jsonArrayLogs.size(); i++) {
				JSONObject jsLog = jsonArrayLogs.getJSONObject(i);
				String idNo = jsLog.optString("IdNo");
				String dataStatus = jsLog.optString("DataStatus");
				String receiveDay = String.valueOf(jsLog.getInt("ReceiveDay"));
				String receiveTime = StringUtils.leftPad(String.valueOf(jsLog.getInt("ReceiveTime")), 6, "0");
				String grntPaper = jsLog.optString("GrntPaper");

				receiveDay = (Integer.parseInt(receiveDay.substring(0, 3)) + 1911) + StringUtils.right(receiveDay, 4);

				List<C124M01A> c124m01as = c124M01ADao.findByCustIdAndDupNo(idNo, null, verNo);
				if (c124m01as != null && c124m01as.size() > 0) {
					for (C124M01A c124m01a : c124m01as) {
						c124m01a.setDataStatus(dataStatus);
						if (!"".equals(grntPaper) && !"0".equals(grntPaper)) {
							c124m01a.setGrntPaper(CapMath.getBigDecimal(grntPaper));
						}

						String receiveDate = receiveDay + receiveTime;
						try {
							Date _receiveDate = sdf.parse(receiveDate);
							c124m01a.setReceiveDay(new Timestamp(_receiveDate.getTime()));
						} catch (ParseException e) {
							e.printStackTrace();
						}

						cls3501Service.save(c124m01a);
					}

				} else {
					logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@信保回傳統編" + idNo + "在C124M01A內查無資料");

				}
			}

			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}
			//將執行完成的檔案備份到bk
			FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			FileUtils.forceDelete(inFile);

		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}

	/**
	 * 1. mis抓心婷資料 抓通知單編號為空的就送
	 * 2. 寫到eloan 主檔，每天一批
	 * 3.
	 */
	private JSONObject job4(String mustRun) {

		logger.info("----------產生GWNTF1_131勞動部勞工紓困貸款通知單介接檔-----------");


		JSONObject result;
		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		int month = today.get(Calendar.MONTH) + 1;
		int date = today.get(Calendar.DATE);
		int hour = today.get(Calendar.HOUR_OF_DAY);

		final String REPORT_NAME = "017";
		String fileName = REPORT_NAME + "" + (year - 1911) + String.format("%02d", month) + String.format("%02d",
				date) + String.format("%02d", hour) + "NT131.TXT";

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ" + File.separator;


		if ("Y".equals(mustRun)) {

		} else {
			// 判斷今天是不是營業日
			Map<String, Object> lnf320 = misdbBASEService.get_LNF320(CapDate.getCurrentDate("yyyy-MM-dd"));
			if (MapUtils.isEmpty(lnf320)) {
				result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
				result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！今日非營業日");
				return result;
			}
		}



		// 先抓帳務提供要送的檔案
		List<Map<String, Object>> elf505UnFinishDatas = misdbBASEService.getELF505UnFinishData();

		Timestamp now = CapDate.getCurrentTimestamp();

		JSONArray ja = new JSONArray();

		Map<String, String> userNameMap = new HashMap<String, String>();
		Map<String, String> userExtMap = new HashMap<String, String>();

		for (Map<String, Object> data : elf505UnFinishDatas) {
			C125M01A c125m01a = new C125M01A();
			// 主機資料塞回r6
			String idno = MapUtils.getString(data, "ELF505_IDNO", "");
			String dupno = MapUtils.getString(data, "ELF505_DUPNO", "");
			String bankkeyno = MapUtils.getString(data, "ELF505_BANKKEYNO", "");
			String grntpaper = MapUtils.getString(data, "ELF505_GRNTPAPER", "");
			BigDecimal loanlimit = (BigDecimal) MapUtils.getObject(data, "ELF505_LOANLIMIT", 0);
			Date begday = (Date) MapUtils.getObject(data, "ELF505_BEGDAY", new Date());
			Date endday = (Date) MapUtils.getObject(data, "ELF505_ENDDAY", new Date());
			BigDecimal loan = (BigDecimal) MapUtils.getObject(data, "ELF505_LOAN", BigDecimal.ZERO);
			int retmethod = Util.parseInt(MapUtils.getString(data, "ELF505_RETMETHOD", ""));
			int feeterm = Util.parseInt(MapUtils.getString(data, "ELF505_FEETERM", ""));
			int graceperiod = Util.parseInt(MapUtils.getString(data, "ELF505_GRACEPERIOD", ""));
			BigDecimal loanrate = (BigDecimal) MapUtils.getObject(data, "ELF505_LOANRATE", BigDecimal.ZERO);
			int feemethod = Util.parseInt(MapUtils.getString(data, "ELF505_FEEMETHOD", ""));
			BigDecimal rate = (BigDecimal) MapUtils.getObject(data, "ELF505_RATE", BigDecimal.ZERO);

			String taxflag = Util.trim(MapUtils.getString(data, "ELF505_TAXFLAG", ""));
			String acntfeeflg = Util.trim(MapUtils.getString(data, "ELF505_ACNTFEEFLG", ""));
			String acntfeeflg2 = Util.trim(MapUtils.getString(data, "ELF505_ACNTFEEFLG2", ""));
			BigDecimal acntfee = (BigDecimal) MapUtils.getObject(data, "ELF505_ACNTFEE", BigDecimal.ZERO);
			String otherfeefg = Util.trim(MapUtils.getString(data, "ELF505_OTHERFEEFG", ""));
			String otherfeefg2 = Util.trim(MapUtils.getString(data, "ELF505_OTHERFEEFG2", ""));
			BigDecimal otherfee = (BigDecimal) MapUtils.getObject(data, "ELF505_OTHERFEE", BigDecimal.ZERO);
			String loanactno = Util.trim(MapUtils.getString(data, "ELF505_LOANACTNO", ""));

			String srlno = MapUtils.getString(data, "ELF505_SRLNO", "");
			//String srlnoapdt = MapUtils.getString(data, "ELF505_SRLNOAPDT", "");
			//String srlnoupdt = MapUtils.getString(data, "ELF505_SRLNOUPDT", "");
			//String srlnostatus = MapUtils.getString(data, "ELF505_SRLNOSTATUS", "");

			String custName = Util.trim(MapUtils.getString(data, "LCNAME", ""));
			String brbkno = Util.trim(MapUtils.getString(data, "BRBKNO", ""));
			//
			Date elf447N_enddate = (Date) MapUtils.getObject(data, "ELF447N_ENDDATE", new Date());
			String grtno = Util.trim(MapUtils.getString(data, "GRTNO", ""));
			String elf447_emp_no = Util.trim(MapUtils.getString(data, "ELF447_EMP_NO", ""));
			String tel = Util.trim(MapUtils.getString(data, "TEL", ""));
			String[] tels = this.parseTel(tel);

			//信保編號長度不為10碼就不送，免得送到信保當掉
			if (grtno.length() != 10) {
				continue;
			}


			c125m01a.setUid(IDGenerator.getUUID());
			c125m01a.setMainId(c125m01a.getUid());
			c125m01a.setTypCd(TypCdEnum.DBU.getCode());
			c125m01a.setCustId(idno);
			c125m01a.setDupNo(dupno);
			c125m01a.setCustName(custName);
			c125m01a.setUnitType(UnitTypeEnum.分行.getCode());
			c125m01a.setOwnBrId(StringUtils.left(bankkeyno, 3));
			c125m01a.setDocStatus("");
			c125m01a.setRandomCode(IDGenerator.getRandomCode());
			c125m01a.setDocURL("");
			c125m01a.setTxCode("");
			c125m01a.setCreator("EL");
			c125m01a.setCreateTime(now);
			c125m01a.setUpdater("EL");
			c125m01a.setUpdateTime(now);
			c125m01a.setBatchDate(now);
			c125m01a.setBankNo(brbkno);
			c125m01a.setBankKeyNo(bankkeyno + "-" + IDGenerator.getUUID());
			c125m01a.setGrntPaper(CapMath.getBigDecimal(grtno));
			c125m01a.setApproveNo(bankkeyno);
			c125m01a.setApproveDay(elf447N_enddate);
			c125m01a.setLoanLimit(loanlimit);
			c125m01a.setBegDay(begday);
			c125m01a.setEndDay(endday);
			c125m01a.setLoan(loan);
			c125m01a.setRetMethod(retmethod);
			c125m01a.setFeeTerm(feeterm);
			c125m01a.setGracePeriod(graceperiod);
			c125m01a.setLoanRate(loanrate);
			c125m01a.setFeeMethod(feemethod);
			c125m01a.setRate(rate);
			c125m01a.setTaxFlag(taxflag);
			c125m01a.setAcntFeeFlg(acntfeeflg);
			c125m01a.setAcntFeeFlg2(acntfeeflg2);
			c125m01a.setAcntFee(acntfee);
			c125m01a.setOtherFeeFlag(otherfeefg);
			c125m01a.setOtherFeeFlag2(otherfeefg2);
			c125m01a.setOtherFee(otherfee);
			c125m01a.setLoanActNo(loanactno);


			c125m01a.setStaffTel1(tels[0]);
			c125m01a.setStaffTel2(tels[1]);
			// 傳送到信保基金中
			c125m01a.setDataStatus("X");

			if (userNameMap.containsKey(elf447_emp_no)) {
				c125m01a.setBankUserName(userNameMap.get(elf447_emp_no));
				c125m01a.setStaffTelExt(Util.parseInt(userExtMap.get(elf447_emp_no)));
			} else {
				ElsUser user = userInfoService.getUser(elf447_emp_no);
				if (user != null) {
					String userName = Util.trim(user.getUserName());
					String telNo = Util.trim(user.getTelNo());
					c125m01a.setBankUserName(userName);
					c125m01a.setStaffTelExt(Util.parseInt(telNo));
					userNameMap.put(elf447_emp_no, userName);
					userExtMap.put(elf447_emp_no, telNo);
				} else {
					c125m01a.setBankUserName("");
					c125m01a.setStaffTelExt(0);
				}
			}

			GWNTF1_131 gwntf1_131 = new GWNTF1_131(c125m01a);


			JSONObject gwntf1_131Js = JSONObject.fromObject(gwntf1_131.toJson());
			ja.add(gwntf1_131Js);

			cls3501Service.save(c125m01a);
			misdbBASEService.updateELF505(idno, grntpaper, srlno, "X", CapDate.getCurrentDate("yyyy-MM-dd"), null, "");
		}

		if (ja.size() > 0) {
			try {
				File txtFile = new File(localFilePath, fileName);
				FileUtils.write(txtFile, ja.toString(), "UTF8");
				String msgId = IDGenerator.getUUID();


				logger.info("[execute] FTP Client : {}", smegFtpClient.toString());

				smegFtpClient.send(msgId, txtFile.toString(), smegFtpClient.getServerDir() + "PAY_SEND", fileName,
						false, true, false);
				logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder.reflectionToString(
						smegFtpClient.list(msgId, smegFtpClient.getServerDir() + "PAY_SEND")));
			} catch (Exception ex) {
				result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
				result.element(WebBatchCode.P_RESPONSE,
						this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
				logger.error(ex.getMessage(), ex);
				return result;
			}
		} else {
			logger.info("MIS.ELF505 is empty");
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}


	private JSONObject job5_ALL() {
		JSONObject result;


		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File bkDupFolder = new File(localFilePath + "bkDup");

		File rootFolder = new File(localFilePath);

		File[] nt145feedbacks = rootFolder.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory()) {
					return false;
				} else if (pathname.getName().toUpperCase().contains("NT145FEEDBACK")
						|| pathname.getName().toUpperCase().contains("NT131FEEDBACK")) {
					return true;
				} else {
					return false;
				}
			}
		});

		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		int year = cal.get(Calendar.YEAR) - 1911;
		int month = cal.get(Calendar.MONTH) + 1;
		int day = cal.get(Calendar.DATE);

		String today = year + String.format("%02d", month) + String.format("%02d", day);

		if (nt145feedbacks != null && nt145feedbacks.length > 0) {
			Arrays.sort(nt145feedbacks);
			for (File nt145feedback : nt145feedbacks) {
				// 因為信保ftp不給6組搬檔，而六組只留2個月記錄，導至六組過了2個月後，就又會收到以前的檔，所以這些檔就不處理了
				// 用檔名來判，如果檔名不是今天就不處理
				String fileName = nt145feedback.getName();
				String fileDate = StringUtils.substring(fileName, 3, 10);
				if (fileDate.equals(today)) {
					JSONObject rs = job5_1(fileName);
					if (rs.getInt(WebBatchCode.P_RC) != 0) {
						return rs;
					}
				} else {
					try {
						logger.info(nt145feedback.getName() + " 非今日檔案，刪除不處理");

						if (!bkDupFolder.exists()) {
							FileUtils.forceMkdir(bkDupFolder);
						}
						FileUtils.copyFile(nt145feedback, new File(bkDupFolder.toString(), nt145feedback.getName()));
						FileUtils.forceDelete(nt145feedback);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}

			}
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}
	//
	private JSONObject job5_1(String file) {

		logger.info("----------讀取通知單回傳結果-----------");

		JSONObject result;

		if (!file.toUpperCase().contains("NT145FEEDBACK")) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + "檔名" + file + "是錯誤的");
			return result;
		}

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File bkFolder = new File(localFilePath + "bk");
		File inFile = new File(localFilePath, file);

		String c124m01aVer = Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER"));
		Integer verNo = (Util.isNumeric(c124m01aVer) ? NumberUtils.toInt(c124m01aVer) : 0);

		try {
			List<String> inTxts = FileUtils.readLines(inFile);
			String log = inTxts.get(0);
			logger.info("log result {}", log);

			String jsonTxt = inTxts.get(1);

			JSONArray jsonArrayLogs = JSONArray.fromObject(jsonTxt);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");

			for (int i = 0; i < jsonArrayLogs.size(); i++) {
				JSONObject jsLog = jsonArrayLogs.getJSONObject(i);
				String idNo = jsLog.optString("IdNo");
				String dataStatus = jsLog.optString("DataStatus");
				//通知單編號
				String srlNo = jsLog.optString("SrlNo");
				// 信保int欄位回傳0代表無資料
				if ("0".equals(srlNo)) {
					srlNo = "";
				}
				// 心婷說如果有回傳保證編號，那也順便寫，用不用得到就再說grntPaper
				String grntPaper = jsLog.optString("GrntPaper");
				if ("0".equals(grntPaper)) {
					grntPaper = "";
				}

				String receiveDay = String.valueOf(jsLog.getInt("ReceiveDay"));
				String receiveTime = StringUtils.leftPad(String.valueOf(jsLog.getInt("ReceiveTime")), 6, "0");
				receiveDay = (Integer.parseInt(receiveDay.substring(0, 3)) + 1911) + StringUtils.right(receiveDay, 4);
				String description = jsLog.optString("Description");
				if ("null".equals(description)) {
					description = null;
				} else {
					if (description.getBytes("UTF8").length > 600) {
						description = new String(description.getBytes(), 0, 600, "UTF8");
					}
				}
				if (Util.trim(description).contains("@本案限一次動用")) {
					// 本案限一次動用，表示可能已經申請過了，就不再送了
					dataStatus = "9";
				}
				ISearch searchTemplete = c125M01ADao.createSearchTemplete();
				searchTemplete.addSearchModeParameters(SearchMode.EQUALS, "custId", idNo);
				searchTemplete.addSearchModeParameters(SearchMode.EQUALS, "dataStatus", "X");
				searchTemplete.addSearchModeParameters(SearchMode.EQUALS, "version", verNo);
				searchTemplete.setMaxResults(Integer.MAX_VALUE);

				List<C125M01A> c125m01as = c125M01ADao.find(searchTemplete);

				if (c125m01as != null && c125m01as.size() > 0) {
					for (C125M01A c125m01a : c125m01as) {
						c125m01a.setDataStatus(dataStatus);
						c125m01a.setDescription(description);
						if (!"".equals(srlNo)) {
							c125m01a.setSrlNo(CapMath.getBigDecimal(srlNo));
						}
						String receiveDate = receiveDay + receiveTime;
						try {
							Date _receiveDate = sdf.parse(receiveDate);
							c125m01a.setReceiveDay(new Timestamp(_receiveDate.getTime()));
						} catch (ParseException e) {
							e.printStackTrace();
						}
						cls3501Service.save(c125m01a);
						Map<String, Object> elf505Data = misdbBASEService.getELF505(idNo, verNo);

						if (MapUtils.isNotEmpty(elf505Data)) {
							String ap_dt = null;
							Date elf505_srlnoapdt = (Date) MapUtils.getObject(elf505Data, "ELF505_SRLNOAPDT", null);
							if (elf505_srlnoapdt != null) {
								ap_dt = CapDate.formatDate(elf505_srlnoapdt, "yyyy-MM-dd");
							}
							misdbBASEService.updateELF505withVer(idNo, grntPaper, srlNo, dataStatus, ap_dt,
									CapDate.getCurrentDate("yyyy-MM-dd"), Util.trimSizeInOS390(description, 400), verNo);
						}

					}

				} else {
					logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@信保回傳統編" + idNo + "在C125M01A內查無資料");
				}

			}

			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}
			FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			FileUtils.forceDelete(inFile);

		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}


	private JSONObject job6() {

		logger.info("----------產生勞動部第二次勞工紓困貸款申請書介接檔-----------");

		JSONObject result;
		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		int month = today.get(Calendar.MONTH) + 1;
		int date = today.get(Calendar.DATE);
		int hour = today.get(Calendar.HOUR_OF_DAY);

		final String REPORT_NAME = "017";
		String fileName = REPORT_NAME + "" + (year - 1911) + String.format("%02d", month) + String.format("%02d",
				date) + String.format("%02d", hour) + 申請書;

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ" + File.separator;

		//取得該銀行的bankCode的資料
		List<Map<String, Object>> synBankList = misdbBASEService.findSynBankList("01", "017");
		Map<String, String> synbankcode = new HashMap<String, String>();

		for (Map<String, Object> data : synBankList) {
			String brnno = MapUtils.getString(data, "BRNNO", "");
			String brbkno = MapUtils.getString(data, "BRBKNO", "");
			synbankcode.put(brnno, brbkno);
		}

		//取得勞工舒困的目前版本，沒想到會有第二次的勞工舒困
		String c124m01aVer = Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER"));
		Integer verNo = (Util.isNumeric(c124m01aVer) ? NumberUtils.toInt(c124m01aVer) : 0);

		Date labor_ver2_date = CapDate.parseDate(
				Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER_DATE")));

		JSONArray ja = new JSONArray();
		/*
		 * 取得本分行對信保保縣市的轉換代碼
		 */
		Map<String, String> cityMap = getCityMap();

		List<Map<String, Object>> dataNoC124s = eloandbBASEService.genLaborTrustData(verNo, labor_ver2_date);
		Timestamp now = CapDate.getCurrentTimestamp();

		for (Map<String, Object> dataNoC124 : dataNoC124s) {

			C124M01A c124m01a = new C124M01A();
			c124m01a.setMainId(IDGenerator.getUUID());
			c124m01a.setUid(c124m01a.getMainId());
			c124m01a.setDocStatus("05O");
			c124m01a.setDataStatus("X");
			c124m01a.setBatchDate(now);

			//遇過分行代碼是空白的資料，導至信保的批次沒過
			String ownbrid = Util.trim(MapUtils.getString(dataNoC124, "OWNBRID"));
			if (Util.isEmpty(ownbrid)) {
				continue;
			}

			c124m01a.setTypCd("");
			c124m01a.setCustId(MapUtils.getString(dataNoC124, "custId"));
			c124m01a.setDupNo("0");
			c124m01a.setCustName(MapUtils.getString(dataNoC124, "custname"));
			c124m01a.setUnitType("");
			c124m01a.setOwnBrId(ownbrid);
			c124m01a.setRandomCode(IDGenerator.getRandomCode());
			c124m01a.setDocURL("/cls/cls3501m01");
			c124m01a.setTxCode("383103");
			c124m01a.setCreator("EL");
			c124m01a.setCreateTime(now);
			c124m01a.setUpdater("EL");
			c124m01a.setUpdateTime(now);
			c124m01a.setApprover("EL");
			c124m01a.setApproveTime(now);
			c124m01a.setIsClosed("");
			c124m01a.setApproveNo(c124m01a.getCustId());
			c124m01a.setAgreement(1);
			c124m01a.setIsCreditCheck(1);
			c124m01a.setType(145);
			c124m01a.setQualiCode(1);
			c124m01a.setIsCreditAbnormal(2);
			//FIXME

			String applyAmt = MapUtils.getString(dataNoC124, "applyAmt", "10");
			String applyLoanDay = MapUtils.getString(dataNoC124, "APPLYTS", CapDate.getCurrentDate("yyyy-MM-dd"));
			c124m01a.setBorrowerBirthDay(CapDate.parseDate(Util.trim(MapUtils.getString(dataNoC124, "BIRTHDAY", ""))));
			c124m01a.setApplyLoanDay(CapDate.parseDate(applyLoanDay));
			c124m01a.setApplyLoan(CapMath.getBigDecimal(applyAmt).multiply(BigDecimal.valueOf(10000)));

			String fTel = Util.trim(MapUtils.getString(dataNoC124, "FTel", ""));
			String coTel = Util.trim(MapUtils.getString(dataNoC124, "CoTel", ""));
			if (Util.isEmpty(fTel)) {
				fTel = coTel;
			}

			String regEx="[^0-9]";
			Pattern p = Pattern.compile(regEx);
			Matcher m = p.matcher(fTel);
			// 將電話號號的非數字拿掉
			fTel = m.replaceAll("").trim();


			c124m01a.setTel1(StringUtils.substring(fTel, 0, 2));
			c124m01a.setTel2(StringUtils.substring(fTel, 2, 10));

			String mTel = Util.trim(MapUtils.getString(dataNoC124, "MTel", ""));
			m = p.matcher(mTel);
			// 將電話號號的非數字拿掉
			mTel = m.replaceAll("").trim();

			c124m01a.setOwnerCellphone1(StringUtils.left(mTel, 4));
			c124m01a.setOwnerCellphone2(StringUtils.right(mTel, 6));

			String eMail = Util.trim(MapUtils.getString(dataNoC124, "EMail", ""));

			c124m01a.setIsEmail("".equals(eMail) ? 2 : 1);
			c124m01a.setEmail(eMail);
			c124m01a.setZip(Util.trim(MapUtils.getString(dataNoC124, "FZIP", "")));
			c124m01a.setCity(Util.trim(MapUtils.getString(dataNoC124, "FCITY", "")));
			c124m01a.setDist(StringUtils.left(Util.trim(MapUtils.getString(dataNoC124, "FZIP", "")), 3));

			Map<String, String> importerMap = brUser.get(c124m01a.getOwnBrId());
			if (importerMap == null) {
				//怕名單有缺，抓不到就抓002分行的
				importerMap = brUser.get("002");
			}

			String empId = importerMap.get("empId");
			String empName = importerMap.get("empName");
			String areaCode = importerMap.get("areaCode");
			String phone = importerMap.get("phone");
			String extension = importerMap.get("extension");
			String email = importerMap.get("email");
			String cellphone = importerMap.get("cellphone");

			c124m01a.setImporterNo(empId);
			c124m01a.setImporterName(empName);
			c124m01a.setImporterCellphone1(StringUtils.left(cellphone, 4));
			c124m01a.setImporterCellphone2(StringUtils.right(cellphone, 6));
			c124m01a.setImporterTel1(areaCode);
			c124m01a.setImporterTel2(StringUtils.left(phone, 8));
			c124m01a.setImporterTelExt(StringUtils.left(extension, 3));
			c124m01a.setImporterEmail(StringUtils.left(email, 50));
			c124m01a.setVersion(verNo);


			String address = Util.trim(MapUtils.getString(dataNoC124, "FTARGET", ""));
			address = address.replaceAll("臺", "台");
			address = CapString.toHalfString(address);


			//if (address.contains("測試街01號")) {
			//	address = "新北市板橋區縣民大道3段245-1號";
			//	c124m01a.setCity("");
			//	c124m01a.setDist("");
			//}

			JSONObject locationJson = null;
			try {
				locationJson = eMapService.getLocation(address);
				JSONArray locAry = locationJson.getJSONArray("DATA");
				JSONObject locDetail = locAry.getJSONObject(0);
				if ("00".equals(locDetail.getString("LOC_CODE"))) {
					JSONObject loc_result = locDetail.getJSONObject("LOC_RESULT");
					//縣市
					//怕來源地址有舊的五都資料，所以做一下轉換
					String county = loc_result.optString("COUNTY")
							.replaceAll("臺", "台")
							.replaceAll("台北縣", "新北市")
							.replaceAll("台中縣", "台中市")
							.replaceAll("台南縣", "台南市")
							.replaceAll("高雄縣", "高雄市")
							.replaceAll("桃園縣", "桃園市");

					//鄉鎮市區
					String town = loc_result.optString("TOWN");

					if (Util.isEmpty(c124m01a.getCity()) || Util.isEmpty(c124m01a.getDist())) {
						//如果真的沒有縣市 鄉鎮市區的代碼的話，從gis的資料來抓

						if (Util.isEmpty(c124m01a.getCity())) {
							CodeType countyCode = codeTypeService.findByTypeAndDesc("counties", county, "zh_TW");
							if (countyCode != null) {
								c124m01a.setCity(Util.trim(countyCode.getCodeValue()));
							}
						}
						if (Util.isNotEmpty(c124m01a.getCity()) && Util.isEmpty(c124m01a.getDist())) {
							String city = Util.trim(c124m01a.getCity());

							CodeType townCode = codeTypeService.findByTypeAndDesc("counties" + city, town, "zh_TW");

							if (townCode != null) {
								c124m01a.setDist(Util.trim(townCode.getCodeValue()));
								c124m01a.setZip(Util.trim(townCode.getCodeValue()));
							} else {
								// 真的什麼都抓不到，就抓第一筆，避免空值
								List<CodeType> townCodetypes = codeTypeService.findByCodeTypeList("counties" + city,
										"zh_TW");
								if (townCodetypes != null && townCodetypes.size() > 0) {
									townCode = townCodetypes.get(0);
									c124m01a.setDist(Util.trim(townCode.getCodeValue()));
									c124m01a.setZip(Util.trim(townCode.getCodeValue()));
								}
							}
						}
					}

					//里
					if (loc_result.get("VILLAGE") instanceof JSONNull) {
						c124m01a.setVillage(null);
						c124m01a.setVillageName("");
					} else {
						String village = Util.trim(loc_result.optString("VILLAGE"));
						c124m01a.setVillage(village.endsWith("里") ? 1 : village.endsWith("村") ? 2 : null);
						c124m01a.setVillageName(StringUtils.left(village, village.length() - 1));
					}

					if (loc_result.get("NEBRH") instanceof JSONNull) {
						c124m01a.setNeighborhood(null);
					} else {
						//鄰
						String nebrh = loc_result.optString("NEBRH");
						c124m01a.setNeighborhood(NumberUtils.isNumber(nebrh) ? Util.parseInt(nebrh) : null);
					}

					if (loc_result.get("ROAD") instanceof JSONNull) {
						c124m01a.setRoad(null);
						c124m01a.setRoadName("");
					} else {
						//街路
						String road = Util.trim(loc_result.optString("ROAD"));
						c124m01a.setRoad(road.endsWith("路") ? 2 : road.endsWith("街") ? 3 : 1);
						if (c124m01a.getRoad() == 1) {
							c124m01a.setRoadName(road);
						} else {
							c124m01a.setRoadName(StringUtils.left(road, road.length() - 1));
						}
					}

					if (loc_result.get("SECTION") instanceof JSONNull) {
						c124m01a.setSec("");
					} else {
						//段
						String section = Util.trim(loc_result.optString("SECTION"));
						c124m01a.setSec(StringUtils.left(section, 4));
					}

					if (loc_result.get("LANE") instanceof JSONNull) {
						c124m01a.setLane("");
					} else {
						//巷
						String lane = Util.trim(loc_result.optString("LANE"));
						c124m01a.setLane(StringUtils.left(lane, 8));
					}

					if (loc_result.get("ALLEY") instanceof JSONNull) {
						c124m01a.setAlley("");
					} else {
						//弄
						String alley = Util.trim(loc_result.optString("ALLEY"));
						c124m01a.setAlley(StringUtils.left(alley, 8));
					}

					if (loc_result.get("NUM") instanceof JSONNull) {
						c124m01a.setNo1("");
						c124m01a.setNo2("");
					} else {
						//號
						String num = loc_result.optString("NUM");
						String[] nums = num.split("-");
						c124m01a.setNo1(nums[0]);
						c124m01a.setNo2(num.contains("-") && nums.length > 1 ? nums[1] : "");
					}


					String floor1 = "";
					String floor2 = "";

					//找尋樓層
					Pattern pattern = Pattern.compile("([\\d一二三四五六七八九十]+)[樓F]([之-](\\d+))?");
					Matcher matcher = pattern.matcher(address);
					boolean matchFound = matcher.find();
					while (matchFound) {
						for (int i = 0; i <= matcher.groupCount(); i++) {
							String groupStr = matcher.group(i);
							if (i == 1) {
								floor1 = Util.trim(groupStr);
							}
							if (i == 3) {
								floor2 = Util.trim(groupStr);
							}
						}

						if (matcher.end() + 1 <= address.length()) {
							matchFound = matcher.find(matcher.end());
						} else {
							break;
						}
					}

					c124m01a.setFloor1(floor1);
					c124m01a.setFloor2(floor2);
					c124m01a.setRoom("");
					//c124m01a.set
					GINQF0WL ginqf0WL = new GINQF0WL(c124m01a, synbankcode, cityMap);
					JSONObject ginqf0WLjs = JSONObject.fromObject(ginqf0WL.toJson());
					ja.add(ginqf0WLjs);


					List<C122M01A> c122m01aList = c122m01aDao.findBy_brNo_custId_applyKind_orderByApplyTSDesc(c124m01a.getOwnBrId(), c124m01a.getCustId()
							, new String[]{ UtilConstants.C122_ApplyKind.B, UtilConstants.C122_ApplyKind.D}, ClsUtility.get_labor_bailout_2021_since_ts());

					if (c122m01aList != null && !c122m01aList.isEmpty()) {
						C122M01A c122m01a = c122m01aList.get(0);
						c122m01a.setApplyStatus(UtilConstants.C122_ApplyStatus.RPA送信保中);
						c122m01a.setStatFlag("F");
						c122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
						clsService.save(c122m01a);
					}

					cls3501Service.saveC124m01a(c124m01a);
				}
				//.............

			} catch (Exception e) {
				logger.error("error", e);
			}


		}

		if (ja.size() > 0) {
			try {
				File txtFile = new File(localFilePath, fileName);
				FileUtils.write(txtFile, ja.toString(), "UTF8");
				String msgId = IDGenerator.getUUID();
				logger.info("[execute] FTP Client : {}", smegFtpClient.toString());

				smegFtpClient.send(msgId, txtFile.toString(), smegFtpClient.getServerDir() + "PAY_SEND", fileName,
						false, true, false);
				logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder.reflectionToString(
						smegFtpClient.list(msgId, smegFtpClient.getServerDir() + "PAY_SEND")));
			} catch (Exception ex) {
				result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
				result.element(WebBatchCode.P_RESPONSE,
						this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
				logger.error(ex.getMessage(), ex);
				return result;
			}
		} else {
			logger.info("LMS.C124M01A is empty");
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}


	/**
	 * 1. mis抓心婷資料 抓通知單編號為空的就送
	 * 2. 寫到eloan 主檔，每天一批
	 * 3.
	 */
	private JSONObject job7(String mustRun) {

		logger.info("----------產生GWNTF1_145勞動部第二次勞工紓困貸款通知單介接檔-----------");


		JSONObject result;
		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		int month = today.get(Calendar.MONTH) + 1;
		int date = today.get(Calendar.DATE);
		int hour = today.get(Calendar.HOUR_OF_DAY);

		final String REPORT_NAME = "017";
		String fileName = REPORT_NAME + "" + (year - 1911) + String.format("%02d", month) + String.format("%02d",
				date) + String.format("%02d", hour) + 第二次勞工紓困通知單;

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ" + File.separator;


		if ("Y".equals(mustRun)) {

		} else {
			// 判斷今天是不是營業日
			Map<String, Object> lnf320 = misdbBASEService.get_LNF320(CapDate.getCurrentDate("yyyy-MM-dd"));
			if (MapUtils.isEmpty(lnf320)) {
				result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
				result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！今日非營業日");
				return result;
			}
		}


		String c124m01aVer = Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER"));
		// 先抓帳務提供要送的檔案
		List<Map<String, Object>> elf505UnFinishDatas = misdbBASEService.getELF505UnFinishDataV2();

		Timestamp now = CapDate.getCurrentTimestamp();

		JSONArray ja = new JSONArray();

		int currentCount = 0;

		for (Map<String, Object> data : elf505UnFinishDatas) {
			C125M01A c125m01a = new C125M01A();
			// 主機資料塞回r6
			String idno = MapUtils.getString(data, "ELF505_IDNO", "");
			String dupno = MapUtils.getString(data, "ELF505_DUPNO", "");
			String bankkeyno = MapUtils.getString(data, "ELF505_BANKKEYNO", "");
			String grntpaper = MapUtils.getString(data, "ELF505_GRNTPAPER", "");
			BigDecimal loanlimit = (BigDecimal) MapUtils.getObject(data, "ELF505_LOANLIMIT", 0);
			Date begday = (Date) MapUtils.getObject(data, "ELF505_BEGDAY", new Date());
			Date endday = (Date) MapUtils.getObject(data, "ELF505_ENDDAY", new Date());
			BigDecimal loan = (BigDecimal) MapUtils.getObject(data, "ELF505_LOAN", BigDecimal.ZERO);
			int retmethod = Util.parseInt(MapUtils.getString(data, "ELF505_RETMETHOD", ""));
			int feeterm = Util.parseInt(MapUtils.getString(data, "ELF505_FEETERM", ""));
			int graceperiod = Util.parseInt(MapUtils.getString(data, "ELF505_GRACEPERIOD", ""));
			BigDecimal loanrate = (BigDecimal) MapUtils.getObject(data, "ELF505_LOANRATE", BigDecimal.ZERO);
			int feemethod = Util.parseInt(MapUtils.getString(data, "ELF505_FEEMETHOD", ""));
			BigDecimal rate = (BigDecimal) MapUtils.getObject(data, "ELF505_RATE", BigDecimal.ZERO);

			String taxflag = Util.trim(MapUtils.getString(data, "ELF505_TAXFLAG", ""));
			String acntfeeflg = Util.trim(MapUtils.getString(data, "ELF505_ACNTFEEFLG", ""));
			String acntfeeflg2 = Util.trim(MapUtils.getString(data, "ELF505_ACNTFEEFLG2", ""));
			BigDecimal acntfee = (BigDecimal) MapUtils.getObject(data, "ELF505_ACNTFEE", BigDecimal.ZERO);
			String otherfeefg = Util.trim(MapUtils.getString(data, "ELF505_OTHERFEEFG", ""));
			String otherfeefg2 = Util.trim(MapUtils.getString(data, "ELF505_OTHERFEEFG2", ""));
			BigDecimal otherfee = (BigDecimal) MapUtils.getObject(data, "ELF505_OTHERFEE", BigDecimal.ZERO);
			String loanactno = Util.trim(MapUtils.getString(data, "ELF505_LOANACTNO", ""));

			String srlno = MapUtils.getString(data, "ELF505_SRLNO", "");
			//String srlnoapdt = MapUtils.getString(data, "ELF505_SRLNOAPDT", "");
			//String srlnoupdt = MapUtils.getString(data, "ELF505_SRLNOUPDT", "");
			//String srlnostatus = MapUtils.getString(data, "ELF505_SRLNOSTATUS", "");

			//String custName = Util.trim(MapUtils.getString(data, "LCNAME", ""));
			String custName = "";
			String brbkno = Util.trim(MapUtils.getString(data, "BRBKNO", ""));
			Date elf447N_enddate = (Date) MapUtils.getObject(data, "ELF447N_ENDDATE", new Date());


			//取得勞工舒困的目前版本，沒想到會有第二次的勞工舒困
			//String c124m01aVer = Util.trim(sysParameterService.getParamValue("LMS_C124M01A_VER"));
			Integer verNo = (Util.isNumeric(c124m01aVer) ? NumberUtils.toInt(c124m01aVer) : 0);


			List<C124M01A> c124m01as = c124M01ADao.findByCustIdAndDupNo(idno, null, verNo);

			String grntPaper_s = "";
			String importerNo_s = "";

			if (c124m01as != null && c124m01as.size() > 0) {
				for (C124M01A c124m01a : c124m01as) {
					BigDecimal grntPaper = c124m01a.getGrntPaper();
					if (grntPaper != null && grntPaper.compareTo(BigDecimal.ZERO) != 0) {
						grntPaper_s = String.valueOf(grntPaper.intValue());
						importerNo_s = c124m01a.getImporterNo();
						custName = Util.trim(c124m01a.getCustName());
					}
				}
			}

			//查無申請書資料，無法處理
			if (Util.isEmpty(grntPaper_s)) {
				continue;
			}

			//
			c125m01a.setUid(IDGenerator.getUUID());
			c125m01a.setMainId(c125m01a.getUid());
			c125m01a.setTypCd(TypCdEnum.DBU.getCode());
			c125m01a.setCustId(idno);
			c125m01a.setDupNo(dupno);
			c125m01a.setCustName(custName);
			c125m01a.setUnitType(UnitTypeEnum.分行.getCode());
			c125m01a.setOwnBrId(StringUtils.left(bankkeyno, 3));
			c125m01a.setDocStatus("");
			c125m01a.setRandomCode(IDGenerator.getRandomCode());
			c125m01a.setDocURL("");
			c125m01a.setTxCode("");
			c125m01a.setCreator("EL");
			c125m01a.setCreateTime(now);
			c125m01a.setUpdater("EL");
			c125m01a.setUpdateTime(now);
			c125m01a.setBatchDate(now);
			c125m01a.setBankNo(brbkno);
			c125m01a.setBankKeyNo(bankkeyno + "-" + IDGenerator.getUUID());
			c125m01a.setGrntPaper(CapMath.getBigDecimal(grntPaper_s));
			c125m01a.setApproveNo(bankkeyno);
			c125m01a.setApproveDay(elf447N_enddate);
			c125m01a.setLoanLimit(loanlimit);
			c125m01a.setBegDay(begday);
			c125m01a.setEndDay(endday);
			c125m01a.setLoan(loan);
			c125m01a.setRetMethod(retmethod);
			c125m01a.setFeeTerm(feeterm);
			c125m01a.setGracePeriod(graceperiod);
			c125m01a.setLoanRate(loanrate);
			c125m01a.setFeeMethod(feemethod);
			c125m01a.setRate(rate);
			c125m01a.setTaxFlag(taxflag);
			c125m01a.setAcntFeeFlg(acntfeeflg);
			c125m01a.setAcntFeeFlg2(acntfeeflg2);
			c125m01a.setAcntFee(acntfee);
			c125m01a.setOtherFeeFlag(otherfeefg);
			c125m01a.setOtherFeeFlag2(otherfeefg2);
			c125m01a.setOtherFee(otherfee);
			c125m01a.setLoanActNo(loanactno);
			c125m01a.setVersion(verNo);

			Map<String, String> importerMap = brUser.get(c125m01a.getOwnBrId());
			if (importerMap == null) {
				//怕名單有缺，抓不到就抓002分行的
				importerMap = brUser.get("002");
			}

			String empId = importerMap.get("empId");
			String empName = importerMap.get("empName");
			String areaCode = importerMap.get("areaCode");
			String phone = importerMap.get("phone");
			String extension = importerMap.get("extension");
			String email = importerMap.get("email");
			String cellphone = importerMap.get("cellphone");


			c125m01a.setStaffTel1(areaCode);
			c125m01a.setStaffTel2(phone);
			c125m01a.setBankUserName(empName);
			c125m01a.setStaffTelExt(Util.parseInt(extension));

			// 傳送到信保基金中
			c125m01a.setDataStatus("X");

			GWNTF1_145 gwntf1_145 = new GWNTF1_145(c125m01a);


			JSONObject gwntf1_145Js = JSONObject.fromObject(gwntf1_145.toJson());
			ja.add(gwntf1_145Js);

			cls3501Service.save(c125m01a);
			misdbBASEService.updateELF505withVer(idno, grntpaper, srlno, "X", CapDate.getCurrentDate("yyyy-MM-dd"), null, "", verNo);

			currentCount = currentCount + 1;
			if (currentCount >= 5500) {
				// 信保規格一次只能5500筆，所以超過就break，下次批次再跑
				break;
			}
		}

		if (ja.size() > 0) {
			try {
				File txtFile = new File(localFilePath, fileName);
				FileUtils.write(txtFile, ja.toString(), "UTF8");
				String msgId = IDGenerator.getUUID();


				logger.info("[execute] FTP Client : {}", smegFtpClient.toString());

				smegFtpClient.send(msgId, txtFile.toString(), smegFtpClient.getServerDir() + "PAY_SEND", fileName,
						false, true, false);
				logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder.reflectionToString(
						smegFtpClient.list(msgId, smegFtpClient.getServerDir() + "PAY_SEND")));
			} catch (Exception ex) {
				result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
				result.element(WebBatchCode.P_RESPONSE,
						this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
				logger.error(ex.getMessage(), ex);
				return result;
			}
		} else {
			logger.info("MIS.ELF505 is empty");
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}

	private JSONObject job8(String assigndate_start, String assigndate_end) {

		logger.info("----------產生勞工紓困還款通知介接檔-----------");

		JSONObject result = null;
				
		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		int month = today.get(Calendar.MONTH) + 1;
		int date = today.get(Calendar.DATE);
		int hour = today.get(Calendar.HOUR_OF_DAY);
		
		String payday_start = "" ;
		String payday_end = "" ;
		
		//是否指定日期
		if (assigndate_start != null && !("").equals(assigndate_start)){//指定日期	
			payday_start = (Integer.parseInt(assigndate_start.substring(0, 4)) - 1911) + assigndate_start.substring(5, 7) + assigndate_start.substring(8, 10);
			payday_end = (Integer.parseInt(assigndate_end.substring(0, 4)) - 1911) + assigndate_end.substring(5, 7) + assigndate_end.substring(8, 10);
		} else {
			//不指定日期抓前一個營業日資料		
			// 判斷今天是不是營業日
			//20230626 不指定日期改抓status為空的資料，1100615~今天
			//Map<String, Object> lnf320 = misdbBASEService.get_LNF320(CapDate.getCurrentDate("yyyy-MM-dd"));
			//if (MapUtils.isEmpty(lnf320)) {//非營業日
			//	result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
			//	result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 勞工紓困還款通知執行成功！今日非營業日");
			//	return result;
			//}
			//營業日
			String beginDate = "2021-06-15";//MapUtils.getString(lnf320, "LNF320_BEGIN_DATE", "");
			payday_start = (Integer.parseInt(beginDate.substring(0, 4)) - 1911) + beginDate.substring(5, 7) + beginDate.substring(8, 10);
			payday_end = (year - 1911) + String.format("%02d", month) + String.format("%02d", date);
		}
		
		
		//一小時一個檔案
		final String REPORT_NAME = "017";
		String fileName = REPORT_NAME + "" + (year - 1911) + String.format("%02d", month) + String.format("%02d",
				date) + String.format("%02d", hour) + 勞工紓困還款通知介接檔;

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ" + File.separator;
		
		JSONObject jsonObject = new JSONObject();
		JSONArray ja = new JSONArray();
		//消金處 經辦/分機
		Map<String, String> userinf =  codeTypeService.findByCodeType("ClsLaborReliefBatch_943", "zh_TW");
		
		String default_BankUserName = Util.trim(userinf.get("username"));//"消金業務處";//銀行經辦姓名
		String default_telNo = Util.trim(userinf.get("telno"));//銀行經辦電話
		String default_StaffTelExt = Util.trim(userinf.get("stafftel"));  //銀行經辦電話分機
		
		//撈取ELF630指定還款日資料
		List<Map<String, Object>> dataList = misdbBASEService.get_ELF630(payday_start, payday_end);
		for (Map<String, Object> data : dataList) {
			//案件編號(參考值，放放款帳號)
			String BankKeyNo = MapUtils.getString(data, "ELF630_LOANNO", "");
			//原貸通知單號 format 9 
			//String OrgSrlNo = MapUtils.getString(data, "ELF630_SRLNO_ORG", "");
			String orgSrlNo8 = String.format("%08d", MapUtils.getInteger(data, "ELF630_SRLNO_ORG", 0));
			String orgSrlNo9 = String.format("%09d", MapUtils.getInteger(data, "ELF630_SRLNO_ORG", 0));
			//批次號
			String BatchNo = MapUtils.getString(data, "ELF630_BATCHNO", "");
			//銀行代碼
			String BankNo = MapUtils.getString(data, "ELF630_BANKNO", "");
//			if(orgSrlNo9.contains("T") && "0170701".equals(BankNo)){
//				BankNo = "0170077";
//			}
//			if(orgSrlNo9.contains("U") && "0170701".equals(BankNo)){
//				BankNo = "0172314";
//			}
			//統一編號
			String IdNo = MapUtils.getString(data, "ELF630_IDNO", "");
			//還款金額
			String PayAmt = MapUtils.getString(data, "ELF630_PAYAMT", "");
			//還款日
			String PayDay = MapUtils.getString(data, "ELF630_PAYDAY", "");
			//授信起日
			String BegDay = MapUtils.getString(data, "ELF630_BEGDAY", "");
			//授信訖日
			String EndDay = MapUtils.getString(data, "ELF630_ENDDAY", "");
			//授信餘額
			String LoanBal = MapUtils.getString(data, "ELF630_LOANBAL", "");
			//逾期收回來源
			String RetSource = MapUtils.getString(data, "ELF630_RETSOURCE", "");
			//經辦五碼員編
			String userNO = MapUtils.getString(data, "ELF630_BANKUSERNO", "");
			
			//用員編取得經辦資訊
			String BankUserName = "";    	//銀行經辦姓名
			String StaffTelExt = "00000";  	//銀行經辦電話分機
			String brnno = "";
			
			//用員編取得經辦資訊
			ElsUser user = userInfoService.getUser("0"+userNO);
			if (user != null) {
				BankUserName = Util.trim(user.getUserName());
				StaffTelExt = Util.trim(user.getTelNo());
				brnno = Util.trim(user.getBrno());
			}
			//取得該分行的電話
			String telNo = "";
			if ("943".equals(brnno)) {//消金處讀不到分行電話，先寫死
				telNo = default_telNo;
				StaffTelExt = default_StaffTelExt;
				BankUserName = default_BankUserName;
			} else {
				List<Map<String, Object>> synBankList = misdbBASEService.get_TelNNo(brnno);
				for (Map<String, Object> synBankdata : synBankList) {
					telNo = MapUtils.getString(synBankdata, "TELNNO", "");
				}
			}
			//相關聯絡資訊為空時, 代表可能被調單位了 都先放消金處經辦
			if (telNo == null || telNo.isEmpty()) {
				telNo = default_telNo;
				StaffTelExt = default_StaffTelExt;
				BankUserName = default_BankUserName;
			}
			if (BankUserName == null || BankUserName.isEmpty()) {
				BankUserName = default_BankUserName;
			}
			if (StaffTelExt == null || StaffTelExt.isEmpty()) {
				StaffTelExt = default_StaffTelExt;
			}else{
				//目前員工檔有些人分機填手機or網路電話, 因信保只接收5碼分機且一定要放, 但這樣會導致連絡不到
				//固遇到這種情形就直接將相關聯絡資訊轉給消金窗口
				if(StaffTelExt.length() > 5){
					telNo = default_telNo;
					StaffTelExt = default_StaffTelExt;
					BankUserName = default_BankUserName;
				}
			}
			
			if (brnno == null || brnno.isEmpty()) {
				brnno = "943";
			}
			
			String[] tels = this.parseTel(telNo);
			
			//銀行經辦電話區碼
			String StaffTel1 = tels[0];
			//銀行經辦電話號碼
			String StaffTel2 = tels[1];

			//經辦資訊回寫ELF630
			misdbBASEService.updateELF630userName(BankUserName, StaffTel1, StaffTel2, StaffTelExt, orgSrlNo8, orgSrlNo9, PayDay);
			
			//組成JSON格式
			jsonObject.put("BankKeyNo", BankKeyNo);
			jsonObject.put("OrgSrlNo", orgSrlNo9);
			jsonObject.put("BatchNo", BatchNo);
			jsonObject.put("BankNo", BankNo);
			jsonObject.put("IdNo", IdNo);
			jsonObject.put("PayAmt", PayAmt);
			jsonObject.put("PayDay", PayDay);
			jsonObject.put("BegDay", BegDay);
			jsonObject.put("EndDay", EndDay);
			jsonObject.put("LoanBal", LoanBal);
			jsonObject.put("RetSource", RetSource);
			jsonObject.put("BankUserName", BankUserName);
			jsonObject.put("StaffTel1", StaffTel1);
			jsonObject.put("StaffTel2", StaffTel2);
			jsonObject.put("StaffTelExt", StaffTelExt);
//			System.out.println("jsonObject1："  + jsonObject);
			ja.add(jsonObject);
			
		}

		//FTP透過檔案傳輸系統送檔給信保
		try {
			File txtFile = new File(localFilePath, fileName);
			FileUtils.write(txtFile, ja.toString(), "UTF8");
			Timestamp now = CapDate.getCurrentTimestamp();
			
			String msgId = IDGenerator.getUUID();
			
			logger.info("[execute] FTP Client : {}", smegFtpClient.toString());

			smegFtpClient.send(msgId, txtFile.toString(),
					smegFtpClient.getServerDir() + "PAY_SEND", fileName, false,
					true, false);
			logger.info("[execute] FTP上傳結果： {} ", ToStringBuilder
					.reflectionToString(smegFtpClient.list(msgId,
							smegFtpClient.getServerDir() + "PAY_SEND")));
		} catch (Exception ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "勞工紓困還款介接檔執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 勞工紓困還款介接檔執行成功！");
		return result;
	}
	
	
	private JSONObject job9(String file) {

		logger.info("----------讀取勞工紓困還款回饋檔-----------");

		JSONObject result;

		if (!file.toUpperCase().contains("NTTCFEEDBACK")) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "勞工紓困還款回饋檔執行失敗！==>" + "檔名" + file + "是錯誤的");
			return result;
		}

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;

		File bkFolder = new File(localFilePath + "bk");
		File inFile = new File(localFilePath, file);

		try {
			List<String> inTxts = FileUtils.readLines(inFile);
			String log = inTxts.get(0);
			logger.info("log result {}", log);

			String jsonTxt = inTxts.get(1);

			JSONArray jsonArrayLogs = JSONArray.fromObject(jsonTxt);

			//解析信保回傳的處理日期
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");

			for (int i = 0; i < jsonArrayLogs.size(); i++) {
				JSONObject jsLog = jsonArrayLogs.getJSONObject(i);
//				String orgSrlNo = jsLog.optString("OrgSrlNo");
				//目前正式機DB有8碼 & 9碼, 信保回來的資料會去掉前面的0
				String orgSrlNo8 = String.format("%08d", jsLog.getInt("OrgSrlNo"));//8碼
				String orgSrlNo9 = String.format("%09d", jsLog.getInt("OrgSrlNo"));//9碼
				String payDay = jsLog.optString("PayDay");
				String dataStatus = jsLog.optString("DataStatus");
//				String srlNo = jsLog.optString("SrlNo");
				String srlNo = String.format("%08d", jsLog.getInt("SrlNo"));
				String sysMsg = jsLog.optString("SysMsg");
				String remark = jsLog.optString("Remark");
				String receiveDay = jsLog.optString("ReceiveDay");

				//資訊回寫ELF630
				misdbBASEService.updateELF630(dataStatus, srlNo, sysMsg, remark, receiveDay, orgSrlNo8, orgSrlNo9, payDay);
			}

			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}
			//將執行完成的檔案備份到bk
			FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			FileUtils.forceDelete(inFile);

		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "勞工紓困還款回饋檔執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 勞工紓困還款回饋檔執行成功！");
		return result;
	}
	
	private JSONObject job9_ALL() {
		
		logger.info("----------處理當日勞工紓困還款回饋檔-----------");
		
		JSONObject result;

		String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
				"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;
		File bkDupFolder = new File(localFilePath + "bkDup");
		File rootFolder = new File(localFilePath);
		File[] backs = rootFolder.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory()) {
					return false;
				} else if (pathname.getName().toUpperCase().contains("NTTCFEEDBACK")) {
					return true;
				} else {
					return false;
				}
			}
		});

		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		int year = cal.get(Calendar.YEAR) - 1911;
		int month = cal.get(Calendar.MONTH) + 1;
		int day = cal.get(Calendar.DATE);
		//前一天
		Calendar cal2 = Calendar.getInstance();
		cal2.add(Calendar.DATE, -1);
		int cyear = cal2.get(Calendar.YEAR) - 1911;
		int cmonth = cal2.get(Calendar.MONTH) + 1;
		int cday = cal2.get(Calendar.DATE);
		
		String today = year + String.format("%02d", month) + String.format("%02d", day);
		String ctoday = cyear + String.format("%02d", cmonth) + String.format("%02d", cday);
		if (backs != null && backs.length > 0) {
			Arrays.sort(backs);
			for (File back : backs) {
				// 因為信保ftp不給6組搬檔，而六組只留2個月記錄，導至六組過了2個月後，就又會收到以前的檔，所以這些檔就不處理了
				// 用檔名來判，如果檔名不是今天就不處理
				// 20230601信保更新隔天才會回檔，改用檔名日期or檔名日期-1天
				// 這邊應該要改成用日期判斷
				String fileName = back.getName();
				String fileDate = StringUtils.substring(fileName, 3, 10);
				//檔案最後修改日(這不能用 怕新下來的檔案修改日會是當天)
				//SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
				//Date date = new Date(back.lastModified());
				//String fileModDate = sdf.format(date); 
				
				if (fileDate.equals(today) || fileDate.equals(ctoday)) {
					JSONObject rs = job9(fileName);
					if (rs.getInt(WebBatchCode.P_RC) != 0) {
						return rs;
					}
				} else {
					try {
						logger.info(back.getName() + " 非今日or前一日檔案，刪除不處理");

						if (!bkDupFolder.exists()) {
							FileUtils.forceMkdir(bkDupFolder);
						}
						FileUtils.copyFile(back, new File(bkDupFolder.toString(), back.getName()));
						FileUtils.forceDelete(back);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}

			}
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 當日勞工紓困還款回饋檔執行成功！");
		return result;
	}
	
	private JSONObject ftpGetAllFiles() {
		JSONObject result;

		try {
			String localFilePath = PropUtil.getProperty("docFile.dir") + File.separator + PropUtil.getProperty(
					"systemId") + File.separator + "900" + File.separator + "LABOR_SMEQ_FTP" + File.separator;
			FileUtils.forceMkdir(new File(localFilePath));
			String msgId = IDGenerator.getUUID();


			String[] ftpFiles = smegFtpClient.list(msgId, smegFtpClient.getServerDir() + "MAIN");
			for (String ftpFile : ftpFiles) {
				boolean match = false;
				if (ftpFile.toUpperCase().endsWith(".TXT")) {
					for (String acceptName : acceptFileNames) {
						if (ftpFile.contains(acceptName)) {
							match = true;
							break;
						}
					}
					if (match) {
						smegFtpClient.download(msgId, ftpFile, new File(localFilePath, ftpFile).toString(), true,
								smegFtpClient.getServerDir() + "MAIN");
						smegFtpClient.delete(msgId, ftpFile, smegFtpClient.getServerDir() + "MAIN");
					}
				}

			}

		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName() + "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
		result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！");
		return result;
	}


	/**
	 * 保證書申請檔json格式
	 */
	class GINQF0WL {

		public JSONObject toJson() {
			JSONObject js = new JSONObject();
			Field[] cols = CapBeanUtil.getField(GINQF0WL.class, false);

			for (Field field : cols) {
				try {
					if ("this$0".equals(field.getName())) {
						continue;
					}
					js.put(field.getName(), get(field.getName()));
				} catch (CapException e) {
					e.printStackTrace();
				}
			}

			return js;
		}

		public Object get(String fieldId) throws CapException {
			try {
				String field = fieldId;
				int index = fieldId.indexOf(".");
				if (index > 0) {
					field = fieldId.substring(0, index);
					Object keyClazz = get(field);
					if (keyClazz instanceof GenericBean) {
						return ((GenericBean) keyClazz).get(fieldId.substring(index + 1));
					}
				} else {
					String getter = new StringBuffer("get").append(String.valueOf(field.charAt(0)).toUpperCase())
							.append(field.substring(1))
							.toString();
					Method method = ReflectionUtils.findMethod(getClass(), getter);
					if (method == null) {
						getter = "is" + getter.substring(3);
						method = ReflectionUtils.findMethod(getClass(), getter);
					}
					if (method != null) {
						return method.invoke(this);
					} else {
						Field f = ReflectionUtils.findField(getClass(), field);
						if (f != null) {
							f.setAccessible(true);
							return f.get(this);
						}
					}
				}
				throw new CapException(new StringBuffer("field:").append(field).append(" is not exist!!").toString(),
						getClass());

			} catch (Exception e) {
				throw new CapException(e, getClass());
			}

		}// ;

		GINQF0WL(C124M01A meta, Map<String, String> synBank, Map<String, String> cityMap) {
			this.ApproveNo = StringUtils.left(Util.trim(meta.getApproveNo()) + "-" + IDGenerator.getRandomCode(), 50);
			this.BankNo = CapString.trimNull(synBank.get(meta.getOwnBrId()));
			this.Agreement = meta.getAgreement() == null ? 1 :meta.getAgreement();
			this.IsCreditCheck = meta.getIsCreditCheck();
			this.Typ = meta.getType() == null ? 145 : meta.getType();
			this.QualiCode = meta.getQualiCode() == null ? 1 : meta.getQualiCode();
			this.IsCreditAbnormal = meta.getIsCreditAbnormal() == null ? 2 : meta.getIsCreditAbnormal();
			this.IdNo = meta.getCustId();
			this.Borrower = StringUtils.left(meta.getCustName(), 10);
			this.BorrowerBirthDay = Util.parseInt(CapDate.formatDate(meta.getBorrowerBirthDay(), "YYYMMDD"));
			this.ApplyLoanDay = Util.parseInt(CapDate.formatDate(meta.getApplyLoanDay(), "YYYMMDD"));
			this.ApplyLoan = meta.getApplyLoan();
			this.Tel1 = meta.getTel1();
			this.Tel2 = meta.getTel2();
			this.OwnerCellphone1 = meta.getOwnerCellphone1();
			this.OwnerCellphone2 = meta.getOwnerCellphone2();
			this.IsEmail = meta.getIsEmail();
			this.Email = meta.getEmail();
			this.Zip = BigDecimal.valueOf(Util.parseInt(meta.getZip()));
			//縣市代碼轉換為信保基金所需代碼
			String city = cityMap.get(meta.getCity());
			this.City = BigDecimal.valueOf(Util.parseInt(city));
			this.Dist = BigDecimal.valueOf(Util.parseInt(meta.getDist()));

			this.VillageName = StringUtils.left(meta.getVillageName(), 50);
			this.Village = meta.getVillage() == null ? null :BigDecimal.valueOf(meta.getVillage());
			this.Neighborhood = meta.getNeighborhood() == null ? null : BigDecimal.valueOf(meta.getNeighborhood());
			this.RoadName = StringUtils.left(meta.getRoadName(), 20);
			this.Road = meta.getRoad();
			this.Sec = StringUtils.left(meta.getSec(), 4);
			this.Lane = StringUtils.left(meta.getLane(), 8);
			this.Alley = StringUtils.left(meta.getAlley(), 8);
			this.No1 = StringUtils.left(meta.getNo1(), 4);
			this.No2 = StringUtils.left(meta.getNo2(), 4);
			this.Floor1 = StringUtils.left(meta.getFloor1(), 3);
			this.Floor2 = StringUtils.left(meta.getFloor2(), 3);
			this.Room = StringUtils.left(meta.getRoom(), 3);
			this.ImporterName = StringUtils.left(meta.getImporterName(), 10);
			this.ImporterCellphone1 = meta.getImporterCellphone1();
			this.ImporterCellphone2 = meta.getImporterCellphone2();
			this.ImporterTel1 = meta.getImporterTel1();
			this.ImporterTel2 = meta.getImporterTel2();
			this.ImporterTelExt = meta.getImporterTelExt();
			this.ImporterEmail = meta.getImporterEmail();
			this.IsImporter = 1;
			this.StaffName = StringUtils.left(meta.getImporterName(), 10);
			this.StaffCellphone1 = meta.getImporterCellphone1();
			this.StaffCellphone2 = meta.getImporterCellphone2();
			this.StaffTel1 = meta.getImporterTel1();
			this.StaffTel2 = meta.getImporterTel2();
			this.StaffTelExt = meta.getImporterTelExt();
			this.StaffEmail = meta.getImporterEmail();

		}

		private String ApproveNo;
		private String BankNo;
		private int Agreement;
		private int IsCreditCheck;
		private int Typ;
		private int QualiCode;
		private int IsCreditAbnormal;
		private String IdNo;
		private String Borrower;
		private int BorrowerBirthDay;
		private int ApplyLoanDay;
		private BigDecimal ApplyLoan;
		private String Tel1;
		private String Tel2;
		private String OwnerCellphone1;
		private String OwnerCellphone2;
		private int IsEmail;
		private String Email;
		private BigDecimal Zip;
		private BigDecimal City;
		private BigDecimal Dist;
		private String VillageName;
		private BigDecimal Village;
		private BigDecimal Neighborhood;
		private String RoadName;
		private int Road;
		private String Sec;
		private String Lane;
		private String Alley;
		private String No1;
		private String No2;
		private String Floor1;
		private String Floor2;
		private String Room;
		private String ImporterName;
		private String ImporterCellphone1;
		private String ImporterCellphone2;
		private String ImporterTel1;
		private String ImporterTel2;
		private String ImporterTelExt;
		private String ImporterEmail;
		private int IsImporter;
		private String StaffName;
		private String StaffCellphone1;
		private String StaffCellphone2;
		private String StaffTel1;
		private String StaffTel2;
		private String StaffTelExt;
		private String StaffEmail;

		public String getApproveNo() {
			return ApproveNo;
		}

		public void setApproveNo(String approveNo) {
			ApproveNo = approveNo;
		}

		public String getBankNo() {
			return BankNo;
		}

		public void setBankNo(String bankNo) {
			BankNo = bankNo;
		}

		public int getAgreement() {
			return Agreement;
		}

		public void setAgreement(int agreement) {
			Agreement = agreement;
		}

		public int getIsCreditCheck() {
			return IsCreditCheck;
		}

		public void setIsCreditCheck(int isCreditCheck) {
			IsCreditCheck = isCreditCheck;
		}

		public int getTyp() {
			return Typ;
		}

		public void setTyp(int typ) {
			Typ = typ;
		}

		public int getQualiCode() {
			return QualiCode;
		}

		public void setQualiCode(int qualiCode) {
			QualiCode = qualiCode;
		}

		public int getIsCreditAbnormal() {
			return IsCreditAbnormal;
		}

		public void setIsCreditAbnormal(int isCreditAbnormal) {
			IsCreditAbnormal = isCreditAbnormal;
		}

		public String getIdNo() {
			return IdNo;
		}

		public void setIdNo(String idNo) {
			IdNo = idNo;
		}

		public String getBorrower() {
			return Borrower;
		}

		public void setBorrower(String borrower) {
			Borrower = borrower;
		}

		public int getBorrowerBirthDay() {
			return BorrowerBirthDay;
		}

		public void setBorrowerBirthDay(int borrowerBirthDay) {
			BorrowerBirthDay = borrowerBirthDay;
		}

		public int getApplyLoanDay() {
			return ApplyLoanDay;
		}

		public void setApplyLoanDay(int applyLoanDay) {
			ApplyLoanDay = applyLoanDay;
		}

		public BigDecimal getApplyLoan() {
			return ApplyLoan;
		}

		public void setApplyLoan(BigDecimal applyLoan) {
			ApplyLoan = applyLoan;
		}

		public String getTel1() {
			return Tel1;
		}

		public void setTel1(String tel1) {
			Tel1 = tel1;
		}

		public String getTel2() {
			return Tel2;
		}

		public void setTel2(String tel2) {
			Tel2 = tel2;
		}

		public String getOwnerCellphone1() {
			return OwnerCellphone1;
		}

		public void setOwnerCellphone1(String ownerCellphone1) {
			OwnerCellphone1 = ownerCellphone1;
		}

		public String getOwnerCellphone2() {
			return OwnerCellphone2;
		}

		public void setOwnerCellphone2(String ownerCellphone2) {
			OwnerCellphone2 = ownerCellphone2;
		}

		public int getIsEmail() {
			return IsEmail;
		}

		public void setIsEmail(int isEmail) {
			IsEmail = isEmail;
		}

		public String getEmail() {
			return Email;
		}

		public void setEmail(String email) {
			Email = email;
		}

		public BigDecimal getZip() {
			return Zip;
		}

		public void setZip(BigDecimal zip) {
			Zip = zip;
		}

		public BigDecimal getCity() {
			return City;
		}

		public void setCity(BigDecimal city) {
			City = city;
		}

		public BigDecimal getDist() {
			return Dist;
		}

		public void setDist(BigDecimal dist) {
			Dist = dist;
		}

		public String getVillageName() {
			return VillageName;
		}

		public void setVillageName(String villageName) {
			VillageName = villageName;
		}

		public BigDecimal getVillage() {
			return Village;
		}

		public void setVillage(BigDecimal village) {
			Village = village;
		}

		public BigDecimal getNeighborhood() {
			return Neighborhood;
		}

		public void setNeighborhood(BigDecimal neighborhood) {
			Neighborhood = neighborhood;
		}

		public String getRoadName() {
			return RoadName;
		}

		public void setRoadName(String roadName) {
			RoadName = roadName;
		}

		public int getRoad() {
			return Road;
		}

		public void setRoad(int road) {
			Road = road;
		}

		public String getSec() {
			return Sec;
		}

		public void setSec(String sec) {
			Sec = sec;
		}

		public String getLane() {
			return Lane;
		}

		public void setLane(String lane) {
			Lane = lane;
		}

		public String getAlley() {
			return Alley;
		}

		public void setAlley(String alley) {
			Alley = alley;
		}

		public String getNo1() {
			return No1;
		}

		public void setNo1(String no1) {
			No1 = no1;
		}

		public String getNo2() {
			return No2;
		}

		public void setNo2(String no2) {
			No2 = no2;
		}

		public String getFloor1() {
			return Floor1;
		}

		public void setFloor1(String floor1) {
			Floor1 = floor1;
		}

		public String getFloor2() {
			return Floor2;
		}

		public void setFloor2(String floor2) {
			Floor2 = floor2;
		}

		public String getRoom() {
			return Room;
		}

		public void setRoom(String room) {
			Room = room;
		}

		public String getImporterName() {
			return ImporterName;
		}

		public void setImporterName(String importerName) {
			ImporterName = importerName;
		}

		public String getImporterCellphone1() {
			return ImporterCellphone1;
		}

		public void setImporterCellphone1(String importerCellphone1) {
			ImporterCellphone1 = importerCellphone1;
		}

		public String getImporterCellphone2() {
			return ImporterCellphone2;
		}

		public void setImporterCellphone2(String importerCellphone2) {
			ImporterCellphone2 = importerCellphone2;
		}

		public String getImporterTel1() {
			return ImporterTel1;
		}

		public void setImporterTel1(String importerTel1) {
			ImporterTel1 = importerTel1;
		}

		public String getImporterTel2() {
			return ImporterTel2;
		}

		public void setImporterTel2(String importerTel2) {
			ImporterTel2 = importerTel2;
		}

		public String getImporterTelExt() {
			return ImporterTelExt;
		}

		public void setImporterTelExt(String importerTelExt) {
			ImporterTelExt = importerTelExt;
		}

		public String getImporterEmail() {
			return ImporterEmail;
		}

		public void setImporterEmail(String importerEmail) {
			ImporterEmail = importerEmail;
		}

		public int getIsImporter() {
			return IsImporter;
		}

		public void setIsImporter(int isImporter) {
			IsImporter = isImporter;
		}

		public String getStaffName() {
			return StaffName;
		}

		public void setStaffName(String staffName) {
			StaffName = staffName;
		}

		public String getStaffCellphone1() {
			return StaffCellphone1;
		}

		public void setStaffCellphone1(String staffCellphone1) {
			StaffCellphone1 = staffCellphone1;
		}

		public String getStaffCellphone2() {
			return StaffCellphone2;
		}

		public void setStaffCellphone2(String staffCellphone2) {
			StaffCellphone2 = staffCellphone2;
		}

		public String getStaffTel1() {
			return StaffTel1;
		}

		public void setStaffTel1(String staffTel1) {
			StaffTel1 = staffTel1;
		}

		public String getStaffTel2() {
			return StaffTel2;
		}

		public void setStaffTel2(String staffTel2) {
			StaffTel2 = staffTel2;
		}

		public String getStaffTelExt() {
			return StaffTelExt;
		}

		public void setStaffTelExt(String staffTelExt) {
			StaffTelExt = staffTelExt;
		}

		public String getStaffEmail() {
			return StaffEmail;
		}

		public void setStaffEmail(String staffEmail) {
			StaffEmail = staffEmail;
		}
	}

	/**
	 * 勞動部勞工紓困貸款通知單介接檔
	 */
	class GWNTF1_131 {

		public JSONObject toJson() {
			JSONObject js = new JSONObject();
			Field[] cols = CapBeanUtil.getField(GWNTF1_131.class, false);

			for (Field field : cols) {
				try {
					if ("this$0".equals(field.getName())) {
						continue;
					}
					js.put(field.getName(), get(field.getName()));
				} catch (CapException e) {
					e.printStackTrace();
				}
			}

			return js;
		}

		public Object get(String fieldId) throws CapException {
			try {
				String field = fieldId;
				int index = fieldId.indexOf(".");
				if (index > 0) {
					field = fieldId.substring(0, index);
					Object keyClazz = get(field);
					if (keyClazz instanceof GenericBean) {
						return ((GenericBean) keyClazz).get(fieldId.substring(index + 1));
					}
				} else {
					String getter = new StringBuffer("get").append(String.valueOf(field.charAt(0)).toUpperCase())
							.append(field.substring(1))
							.toString();
					Method method = ReflectionUtils.findMethod(getClass(), getter);
					if (method == null) {
						getter = "is" + getter.substring(3);
						method = ReflectionUtils.findMethod(getClass(), getter);
					}
					if (method != null) {
						return method.invoke(this);
					} else {
						Field f = ReflectionUtils.findField(getClass(), field);
						if (f != null) {
							f.setAccessible(true);
							return f.get(this);
						}
					}
				}
				throw new CapException(new StringBuffer("field:").append(field).append(" is not exist!!").toString(),
						getClass());

			} catch (Exception e) {
				throw new CapException(e, getClass());
			}

		}// ;

		GWNTF1_131(C125M01A meta) {
			this.BankNo = meta.getBankNo();
			this.BankKeyNo = StringUtils.left(meta.getBankKeyNo(), 50);
			this.GrntPaper = meta.getGrntPaper().intValue();
			this.IdNo = meta.getCustId();
			this.ApproveNo = StringUtils.left(meta.getApproveNo(), 20);
			this.ApproveDay = Util.parseInt(
					CapDate.formatDate(meta.getApproveDay() == null ? new Date() : meta.getApproveDay(), "YYYMMDD"));
			this.LoanLimit = meta.getLoanLimit();
			this.BegDay = Util.parseInt(CapDate.formatDate(meta.getBegDay(), "YYYMMDD"));
			this.EndDay = Util.parseInt(CapDate.formatDate(meta.getEndDay(), "YYYMMDD"));
			this.Loan = meta.getLoan();
			this.RetMethod = meta.getRetMethod();
			this.FeeTerm = meta.getFeeTerm();
			this.GracePeriod = meta.getGracePeriod();
			this.LoanRate = meta.getLoanRate();
			this.FeeMethod = meta.getFeeMethod();
			this.Rate = meta.getRate();
			this.TaxFlag = meta.getTaxFlag();
			this.AcntFeeFlg = meta.getAcntFeeFlg();
			this.AcntFeeFlg2 = meta.getAcntFeeFlg2();
			this.AcntFee = meta.getAcntFee();
			this.OtherFeeFlag = meta.getOtherFeeFlag();
			this.OtherFeeFlag2 = meta.getOtherFeeFlag2();
			this.OtherFee = meta.getOtherFee();
			this.LoanActNo = meta.getLoanActNo();
			this.BankUserName = StringUtils.left(meta.getBankUserName(), 20);
			this.StaffTel1 = meta.getStaffTel1();
			this.StaffTel2 = meta.getStaffTel2();
			this.StaffTelExt = meta.getStaffTelExt();
		}

		private String BankNo;
		private String BankKeyNo;
		private int GrntPaper;
		private String IdNo;
		private String ApproveNo;
		private int ApproveDay;
		private BigDecimal LoanLimit;
		private int BegDay;
		private int EndDay;
		private BigDecimal Loan;
		private int RetMethod;
		private int FeeTerm;
		private int GracePeriod;
		private BigDecimal LoanRate;
		private int FeeMethod;
		private BigDecimal Rate;
		private String TaxFlag;
		private String AcntFeeFlg;
		private String AcntFeeFlg2;
		private BigDecimal AcntFee;
		private String OtherFeeFlag;
		private String OtherFeeFlag2;
		private BigDecimal OtherFee;
		private String LoanActNo;
		private String BankUserName;
		private String StaffTel1;
		private String StaffTel2;
		private int StaffTelExt;

		public String getBankNo() {
			return BankNo;
		}

		public void setBankNo(String bankNo) {
			BankNo = bankNo;
		}

		public String getBankKeyNo() {
			return BankKeyNo;
		}

		public void setBankKeyNo(String bankKeyNo) {
			BankKeyNo = bankKeyNo;
		}

		public int getGrntPaper() {
			return GrntPaper;
		}

		public void setGrntPaper(int grntPaper) {
			GrntPaper = grntPaper;
		}

		public String getIdNo() {
			return IdNo;
		}

		public void setIdNo(String idNo) {
			IdNo = idNo;
		}

		public String getApproveNo() {
			return ApproveNo;
		}

		public void setApproveNo(String approveNo) {
			ApproveNo = approveNo;
		}

		public int getApproveDay() {
			return ApproveDay;
		}

		public void setApproveDay(int approveDay) {
			ApproveDay = approveDay;
		}

		public BigDecimal getLoanLimit() {
			return LoanLimit;
		}

		public void setLoanLimit(BigDecimal loanLimit) {
			LoanLimit = loanLimit;
		}

		public int getBegDay() {
			return BegDay;
		}

		public void setBegDay(int begDay) {
			BegDay = begDay;
		}

		public int getEndDay() {
			return EndDay;
		}

		public void setEndDay(int endDay) {
			EndDay = endDay;
		}

		public BigDecimal getLoan() {
			return Loan;
		}

		public void setLoan(BigDecimal loan) {
			Loan = loan;
		}

		public int getRetMethod() {
			return RetMethod;
		}

		public void setRetMethod(int retMethod) {
			RetMethod = retMethod;
		}

		public int getFeeTerm() {
			return FeeTerm;
		}

		public void setFeeTerm(int feeTerm) {
			FeeTerm = feeTerm;
		}

		public int getGracePeriod() {
			return GracePeriod;
		}

		public void setGracePeriod(int gracePeriod) {
			GracePeriod = gracePeriod;
		}

		public BigDecimal getLoanRate() {
			return LoanRate;
		}

		public void setLoanRate(BigDecimal loanRate) {
			LoanRate = loanRate;
		}

		public int getFeeMethod() {
			return FeeMethod;
		}

		public void setFeeMethod(int feeMethod) {
			FeeMethod = feeMethod;
		}

		public BigDecimal getRate() {
			return Rate;
		}

		public void setRate(BigDecimal rate) {
			Rate = rate;
		}

		public String getTaxFlag() {
			return TaxFlag;
		}

		public void setTaxFlag(String taxFlag) {
			TaxFlag = taxFlag;
		}

		public String getAcntFeeFlg() {
			return AcntFeeFlg;
		}

		public void setAcntFeeFlg(String acntFeeFlg) {
			AcntFeeFlg = acntFeeFlg;
		}

		public String getAcntFeeFlg2() {
			return AcntFeeFlg2;
		}

		public void setAcntFeeFlg2(String acntFeeFlg2) {
			AcntFeeFlg2 = acntFeeFlg2;
		}

		public BigDecimal getAcntFee() {
			return AcntFee;
		}

		public void setAcntFee(BigDecimal acntFee) {
			AcntFee = acntFee;
		}

		public String getOtherFeeFlag() {
			return OtherFeeFlag;
		}

		public void setOtherFeeFlag(String otherFeeFlag) {
			OtherFeeFlag = otherFeeFlag;
		}

		public String getOtherFeeFlag2() {
			return OtherFeeFlag2;
		}

		public void setOtherFeeFlag2(String otherFeeFlag2) {
			OtherFeeFlag2 = otherFeeFlag2;
		}

		public BigDecimal getOtherFee() {
			return OtherFee;
		}

		public void setOtherFee(BigDecimal otherFee) {
			OtherFee = otherFee;
		}

		public String getLoanActNo() {
			return LoanActNo;
		}

		public void setLoanActNo(String loanActNo) {
			LoanActNo = loanActNo;
		}

		public String getBankUserName() {
			return BankUserName;
		}

		public void setBankUserName(String bankUserName) {
			BankUserName = bankUserName;
		}

		public String getStaffTel1() {
			return StaffTel1;
		}

		public void setStaffTel1(String staffTel1) {
			StaffTel1 = staffTel1;
		}

		public String getStaffTel2() {
			return StaffTel2;
		}

		public void setStaffTel2(String staffTel2) {
			StaffTel2 = staffTel2;
		}

		public int getStaffTelExt() {
			return StaffTelExt;
		}

		public void setStaffTelExt(int staffTelExt) {
			StaffTelExt = staffTelExt;
		}
	}

	class GWNTF1_145 {

		public JSONObject toJson() {
			JSONObject js = new JSONObject();
			Field[] cols = CapBeanUtil.getField(GWNTF1_145.class, false);

			for (Field field : cols) {
				try {
					if ("this$0".equals(field.getName())) {
						continue;
					}
					js.put(field.getName(), get(field.getName()));
				} catch (CapException e) {
					e.printStackTrace();
				}
			}

			return js;
		}

		public Object get(String fieldId) throws CapException {
			try {
				String field = fieldId;
				int index = fieldId.indexOf(".");
				if (index > 0) {
					field = fieldId.substring(0, index);
					Object keyClazz = get(field);
					if (keyClazz instanceof GenericBean) {
						return ((GenericBean) keyClazz).get(fieldId.substring(index + 1));
					}
				} else {
					String getter = new StringBuffer("get").append(String.valueOf(field.charAt(0)).toUpperCase())
							.append(field.substring(1))
							.toString();
					Method method = ReflectionUtils.findMethod(getClass(), getter);
					if (method == null) {
						getter = "is" + getter.substring(3);
						method = ReflectionUtils.findMethod(getClass(), getter);
					}
					if (method != null) {
						return method.invoke(this);
					} else {
						Field f = ReflectionUtils.findField(getClass(), field);
						if (f != null) {
							f.setAccessible(true);
							return f.get(this);
						}
					}
				}
				throw new CapException(new StringBuffer("field:").append(field).append(" is not exist!!").toString(),
						getClass());

			} catch (Exception e) {
				throw new CapException(e, getClass());
			}

		}// ;

		GWNTF1_145(C125M01A meta) {
			this.BankNo = meta.getBankNo();
			this.BankKeyNo = StringUtils.left(meta.getBankKeyNo(), 50);
			this.GrntPaper = meta.getGrntPaper().intValue();
			this.IdNo = meta.getCustId();
			this.ApproveNo = StringUtils.left(meta.getApproveNo(), 20);
			this.ApproveDay = Util.parseInt(
					CapDate.formatDate(meta.getApproveDay() == null ? new Date() : meta.getApproveDay(), "YYYMMDD"));
			this.LoanLimit = meta.getLoanLimit();
			this.BegDay = Util.parseInt(CapDate.formatDate(meta.getBegDay(), "YYYMMDD"));
			this.EndDay = Util.parseInt(CapDate.formatDate(meta.getEndDay(), "YYYMMDD"));
			this.Loan = meta.getLoan();
			this.RetMethod = meta.getRetMethod();
			this.FeeTerm = meta.getFeeTerm();
			this.GracePeriod = meta.getGracePeriod();
			this.LoanRate = meta.getLoanRate();
			this.FeeMethod = meta.getFeeMethod();
			this.Rate = meta.getRate();
			this.TaxFlag = meta.getTaxFlag();
			this.AcntFeeFlg = meta.getAcntFeeFlg();
			this.AcntFeeFlg2 = meta.getAcntFeeFlg2();
			this.AcntFee = meta.getAcntFee();
			this.OtherFeeFlag = meta.getOtherFeeFlag();
			this.OtherFeeFlag2 = meta.getOtherFeeFlag2();
			this.OtherFee = meta.getOtherFee();
			this.LoanActNo = meta.getLoanActNo();
			this.BankUserName = StringUtils.left(meta.getBankUserName(), 20);
			this.StaffTel1 = meta.getStaffTel1();
			this.StaffTel2 = meta.getStaffTel2();
			this.StaffTelExt = meta.getStaffTelExt();
		}

		private String BankNo;
		private String BankKeyNo;
		private int GrntPaper;
		private String IdNo;
		private String ApproveNo;
		private int ApproveDay;
		private BigDecimal LoanLimit;
		private int BegDay;
		private int EndDay;
		private BigDecimal Loan;
		private int RetMethod;
		private int FeeTerm;
		private int GracePeriod;
		private BigDecimal LoanRate;
		private int FeeMethod;
		private BigDecimal Rate;
		private String TaxFlag;
		private String AcntFeeFlg;
		private String AcntFeeFlg2;
		private BigDecimal AcntFee;
		private String OtherFeeFlag;
		private String OtherFeeFlag2;
		private BigDecimal OtherFee;
		private String LoanActNo;
		private String BankUserName;
		private String StaffTel1;
		private String StaffTel2;
		private int StaffTelExt;

		public String getBankNo() {
			return BankNo;
		}

		public void setBankNo(String bankNo) {
			BankNo = bankNo;
		}

		public String getBankKeyNo() {
			return BankKeyNo;
		}

		public void setBankKeyNo(String bankKeyNo) {
			BankKeyNo = bankKeyNo;
		}

		public int getGrntPaper() {
			return GrntPaper;
		}

		public void setGrntPaper(int grntPaper) {
			GrntPaper = grntPaper;
		}

		public String getIdNo() {
			return IdNo;
		}

		public void setIdNo(String idNo) {
			IdNo = idNo;
		}

		public String getApproveNo() {
			return ApproveNo;
		}

		public void setApproveNo(String approveNo) {
			ApproveNo = approveNo;
		}

		public int getApproveDay() {
			return ApproveDay;
		}

		public void setApproveDay(int approveDay) {
			ApproveDay = approveDay;
		}

		public BigDecimal getLoanLimit() {
			return LoanLimit;
		}

		public void setLoanLimit(BigDecimal loanLimit) {
			LoanLimit = loanLimit;
		}

		public int getBegDay() {
			return BegDay;
		}

		public void setBegDay(int begDay) {
			BegDay = begDay;
		}

		public int getEndDay() {
			return EndDay;
		}

		public void setEndDay(int endDay) {
			EndDay = endDay;
		}

		public BigDecimal getLoan() {
			return Loan;
		}

		public void setLoan(BigDecimal loan) {
			Loan = loan;
		}

		public int getRetMethod() {
			return RetMethod;
		}

		public void setRetMethod(int retMethod) {
			RetMethod = retMethod;
		}

		public int getFeeTerm() {
			return FeeTerm;
		}

		public void setFeeTerm(int feeTerm) {
			FeeTerm = feeTerm;
		}

		public int getGracePeriod() {
			return GracePeriod;
		}

		public void setGracePeriod(int gracePeriod) {
			GracePeriod = gracePeriod;
		}

		public BigDecimal getLoanRate() {
			return LoanRate;
		}

		public void setLoanRate(BigDecimal loanRate) {
			LoanRate = loanRate;
		}

		public int getFeeMethod() {
			return FeeMethod;
		}

		public void setFeeMethod(int feeMethod) {
			FeeMethod = feeMethod;
		}

		public BigDecimal getRate() {
			return Rate;
		}

		public void setRate(BigDecimal rate) {
			Rate = rate;
		}

		public String getTaxFlag() {
			return TaxFlag;
		}

		public void setTaxFlag(String taxFlag) {
			TaxFlag = taxFlag;
		}

		public String getAcntFeeFlg() {
			return AcntFeeFlg;
		}

		public void setAcntFeeFlg(String acntFeeFlg) {
			AcntFeeFlg = acntFeeFlg;
		}

		public String getAcntFeeFlg2() {
			return AcntFeeFlg2;
		}

		public void setAcntFeeFlg2(String acntFeeFlg2) {
			AcntFeeFlg2 = acntFeeFlg2;
		}

		public BigDecimal getAcntFee() {
			return AcntFee;
		}

		public void setAcntFee(BigDecimal acntFee) {
			AcntFee = acntFee;
		}

		public String getOtherFeeFlag() {
			return OtherFeeFlag;
		}

		public void setOtherFeeFlag(String otherFeeFlag) {
			OtherFeeFlag = otherFeeFlag;
		}

		public String getOtherFeeFlag2() {
			return OtherFeeFlag2;
		}

		public void setOtherFeeFlag2(String otherFeeFlag2) {
			OtherFeeFlag2 = otherFeeFlag2;
		}

		public BigDecimal getOtherFee() {
			return OtherFee;
		}

		public void setOtherFee(BigDecimal otherFee) {
			OtherFee = otherFee;
		}

		public String getLoanActNo() {
			return LoanActNo;
		}

		public void setLoanActNo(String loanActNo) {
			LoanActNo = loanActNo;
		}

		public String getBankUserName() {
			return BankUserName;
		}

		public void setBankUserName(String bankUserName) {
			BankUserName = bankUserName;
		}

		public String getStaffTel1() {
			return StaffTel1;
		}

		public void setStaffTel1(String staffTel1) {
			StaffTel1 = staffTel1;
		}

		public String getStaffTel2() {
			return StaffTel2;
		}

		public void setStaffTel2(String staffTel2) {
			StaffTel2 = staffTel2;
		}

		public int getStaffTelExt() {
			return StaffTelExt;
		}

		public void setStaffTelExt(int staffTelExt) {
			StaffTelExt = staffTelExt;
		}
	}

	private Map<String, String> getCityMap() {
		Map<String, String> cityMap = new HashMap<String, String>();
		cityMap.put("01", "1");
		cityMap.put("02", "10");
		cityMap.put("03", "2");
		cityMap.put("04", "13");
		cityMap.put("05", "11");
		cityMap.put("06", "12");
		cityMap.put("07", "6");
		cityMap.put("08", "20");
		cityMap.put("09", "3");
		cityMap.put("11", "22");
		cityMap.put("12", "23");
		cityMap.put("13", "24");
		cityMap.put("14", "30");
		cityMap.put("15", "7");
		cityMap.put("16", "4");
		cityMap.put("18", "5");
		cityMap.put("20", "33");
		cityMap.put("21", "40");
		cityMap.put("22", "41");
		cityMap.put("23", "34");
		cityMap.put("24", "43");
		cityMap.put("25", "42");
		return cityMap;
	}


	private String[] parseTel(String tel) {
		String inputStr = tel;
		String patternStr = "\\(?(\\d+)\\)?-?(\\d+)";

		String[] tels = new String[] { "0", "0" };

		Pattern pattern = Pattern.compile(patternStr);
		Matcher matcher = pattern.matcher(inputStr);
		boolean matchFound = matcher.find();
		while (matchFound) {
			System.out.println(matcher.start() + "-" + matcher.end());
			for (int i = 0; i <= matcher.groupCount(); i++) {
				String groupStr = matcher.group(i);
				System.out.println(i + ":" + groupStr);
				if (i == 1) {
					tels[0] = groupStr;
				} else if (i == 2) {
					tels[1] = groupStr;
				}
			}

			if (matcher.end() + 1 <= inputStr.length()) {
				matchFound = matcher.find(matcher.end());
			} else {
				break;
			}
		}

		String firstNumber = CapString.trimNull(tels[0]);
		if (firstNumber.length() > 2) {
			tels[0] = StringUtils.left(firstNumber, 2);
			String remain = firstNumber.substring(2);
			tels[1] = remain + tels[1];
		}
		return tels;
	}


	Map<String, Map<String, String>> brUser = new HashMap<String, Map<String, String>>();

	@PostConstruct
	void init() {
		brUser.clear();
		initBranch();
	}

	/**
	 * 因為都是自動報送，請消金處給名單
	 */
	private void initBranch() {

		brUser.put("002", setBrEmpData("004529","蘇品純","07", "2510141", "708", "<EMAIL>"," 0928496616"));
		brUser.put("003", setBrEmpData("008252","周裕祐","07", "3615131", "315", "<EMAIL>","0920726301"));
		brUser.put("004", setBrEmpData("003398","劉宗賢","04", "22281171","280", "<EMAIL>","0928495789"));
		brUser.put("005", setBrEmpData("008017","林凱威","02", "27711877","213", "<EMAIL>","0919965308"));
		brUser.put("006", setBrEmpData("004547","楊勝源","06", "2231231", "200", "<EMAIL>","0911163259"));
		brUser.put("008", setBrEmpData("009669","許慈容","02", "27516041","651", "<EMAIL>","0921057298"));
		brUser.put("010", setBrEmpData("003601","楊淑華","02", "28385225","264", "<EMAIL>","0918932382"));
		brUser.put("012", setBrEmpData("003782","王立人","02", "29884455","64",  "<EMAIL>","0937928097"));
		brUser.put("013", setBrEmpData("003894","陳美華","07", "2353001", "282", "<EMAIL>","0952506011"));
		brUser.put("014", setBrEmpData("006567","許迎双","03", "3376611", "214", "<EMAIL>","0917606337"));
		brUser.put("015", setBrEmpData("003212","王美玲","02", "25119231","210", "<EMAIL>","0921159268"));
		brUser.put("016", setBrEmpData("003047","林士智","07", "8316131", "110", "<EMAIL>","0921527757"));
		brUser.put("017", setBrEmpData("003116","黃芝綺","02", "23122222","263", "<EMAIL>","0936869155"));
		brUser.put("018", setBrEmpData("004576","李家旺","04", "7232111", "212", "<EMAIL>","0988705735"));
		brUser.put("019", setBrEmpData("009153","黃俊凱","02", "27042141","207", "<EMAIL>","0910973800"));
		brUser.put("020", setBrEmpData("006416","林佳慧","03", "5775151", "248", "<EMAIL>","0958898622"));
		brUser.put("021", setBrEmpData("010025","王昱萍","02", "28714125","79",  "<EMAIL>","0911787273"));
		brUser.put("022", setBrEmpData("008995","胡惠慈","05", "2241166", "218", "<EMAIL>","0988615275"));
		brUser.put("023", setBrEmpData("006477","陳宏宇","03", "8350191", "1302","<EMAIL>","0912289543"));
		brUser.put("026", setBrEmpData("006991","連尉良","03", "5217171", "215", "<EMAIL>","0953020106"));
		brUser.put("027", setBrEmpData("004568","林雅慧","02", "89663303","59",  "<EMAIL>","0935183757"));
		brUser.put("028", setBrEmpData("007810","吳偉圖","04", "25335111","211", "<EMAIL>","0972107379"));
		brUser.put("029", setBrEmpData("004237","王沛然","03", "3982200", "3501","<EMAIL>","0920561219"));
		brUser.put("030", setBrEmpData("004456","梁仕育","02", "23568700","33",  "<EMAIL>","0929020242"));
		brUser.put("031", setBrEmpData("007760","陳玉潔","02", "27050136","208", "<EMAIL>","0932463686"));
		brUser.put("032", setBrEmpData("003747","李偉業","04", "8332561", "203", "<EMAIL>","0911624781"));
		brUser.put("034", setBrEmpData("007819","林秉賦","02", "29240086","34",  "<EMAIL>","0972188305"));
		brUser.put("035", setBrEmpData("010032","江廩楓","04", "25285566","211", "<EMAIL>","0988076492"));
		brUser.put("036", setBrEmpData("009912","吳凱琪","02", "27190690","32",  "<EMAIL>","0933879761"));
		brUser.put("037", setBrEmpData("004665","謝錦堂","04", "23115119","301", "<EMAIL>","0937791707"));
		brUser.put("038", setBrEmpData("007598","陳仲維","07", "7250688", "21",  "<EMAIL>","0963052600"));
		brUser.put("039", setBrEmpData("010809","楊惟婷","03", "4228469", "38",  "<EMAIL>","0937739506"));
		brUser.put("040", setBrEmpData("002239","許月菱","07", "5536511", "20",  "<EMAIL>","0987333177"));
		brUser.put("041", setBrEmpData("007512","林盈如","02", "22772888","126", "<EMAIL>","0916159739"));
		brUser.put("042", setBrEmpData("010314","鐘聖翔","02", "27535856","18",  "<EMAIL>","0987101676"));
		brUser.put("043", setBrEmpData("003972","李其威","02", "25567515","204", "<EMAIL>","0930062093"));
		brUser.put("044", setBrEmpData("004825","陳家蕎","04", "23752529","214", "<EMAIL>","0927773390"));
		brUser.put("045", setBrEmpData("002464","梁正宏","07", "7473566", "202", "<EMAIL>","0979021365"));
		brUser.put("046", setBrEmpData("004390","許書銓","02", "29182988","211", "<EMAIL>","0958581895"));
		brUser.put("047", setBrEmpData("002586","許惠鈴","07", "3806456", "35",  "<EMAIL>","0956801818"));
		brUser.put("048", setBrEmpData("006523","薛聖平","02", "23788188","213", "<EMAIL>","0912267512"));
		brUser.put("049", setBrEmpData("005489","洪菁霞","02", "24228558","220", "<EMAIL>","0911853642"));
		brUser.put("050", setBrEmpData("007299","程佳翎","06", "2019389", "36",  "<EMAIL>","0939486980"));
		brUser.put("051", setBrEmpData("004597","柯永富","02", "27932050","208", "<EMAIL>","0933065521"));
		brUser.put("052", setBrEmpData("005426","曾麗瑾","07", "6230300", "13",  "<EMAIL>","0912720742"));
		brUser.put("053", setBrEmpData("004749","郭淑君","08", "7323586", "204", "<EMAIL>","0933694580"));
		brUser.put("055", setBrEmpData("003967","李俊仁","02", "27152385","131", "<EMAIL>","0954004605"));
		brUser.put("056", setBrEmpData("011064","劉展佑","02", "22666866","262", "<EMAIL>","0952796198"));
		brUser.put("057", setBrEmpData("010405","周家駿","02", "25523216","286", "<EMAIL>","0932127919"));
		brUser.put("058", setBrEmpData("006551","劉永元","03", "7688168",  "268", "<EMAIL>","0938158827"));
		brUser.put("059", setBrEmpData("008100","張桂綿","07", "3355595", "602", "<EMAIL>","0972600665"));
		brUser.put("060", setBrEmpData("009338","陳原宥","04", "26656778","215", "<EMAIL>","0985910052"));
		brUser.put("061", setBrEmpData("008519","黃婉婷","03", "3665211", "211", "<EMAIL>","0982450100"));
		brUser.put("062", setBrEmpData("006343","張龍勳","03", "9310666", "24",  "<EMAIL>","0920664257"));
		brUser.put("063", setBrEmpData("004561","李銘舫","05", "5361779", "269", "<EMAIL>","0928909066"));
		brUser.put("064", setBrEmpData("007280","賓秀娟","04", "92232223", "205", "<EMAIL>","0956567967"));
		brUser.put("065", setBrEmpData("005126","陳秀慧","06", "2381611", "203", "<EMAIL>","0955005394"));
		brUser.put("066", setBrEmpData("008326","黃冠穎","07", "8067866", "212", "<EMAIL>","0963686983"));
		brUser.put("067", setBrEmpData("005469","呂靜雯","02", "26275699","52",  "<EMAIL>","0933778981"));
		brUser.put("068", setBrEmpData("003219","林瑞榮","04", "22789111","151", "<EMAIL>","0915550350"));
		brUser.put("069", setBrEmpData("008770","陳姿穎","02", "22433567","302", "<EMAIL>","0912507505"));
		brUser.put("070", setBrEmpData("004677","張菁蘭","02", "25712568","631", "<EMAIL>","0930757937"));
		brUser.put("071", setBrEmpData("007081","王姿予","04", "22321111","214", "<EMAIL>","0911606106"));
		brUser.put("072", setBrEmpData("003657","蘇昱文","07", "3157777", "23",  "<EMAIL>","0972090380"));
		brUser.put("074", setBrEmpData("009594","吳玖鋒","02", "27827588","211", "<EMAIL>","0970909723"));
		brUser.put("075", setBrEmpData("004235","林有勝","03", "7682288",  "104", "<EMAIL>","0930756328"));
		brUser.put("076", setBrEmpData("006549","李政霖","04", "25658108","211", "<EMAIL>","0911590600"));
		brUser.put("077", setBrEmpData("004628","鄭倩如","04", "26867777","203", "<EMAIL>","0937285839"));
		brUser.put("079", setBrEmpData("007183","蔡孟慈","08", "2375800", "25",  "<EMAIL>","0929534572"));
		brUser.put("080", setBrEmpData("004847","葉晏萍","03", "3525288", "226", "<EMAIL>","0930766743"));
		brUser.put("083", setBrEmpData("003420","郭虹君","04", "23500190","206", "<EMAIL>","0952770791"));
		brUser.put("103", setBrEmpData("006265","郭堂晉","07", "3711144", "202", "<EMAIL>","0929672553"));
		brUser.put("201", setBrEmpData("008805","黃煒婷","02", "25633156","6226","<EMAIL>","0972161483"));
		brUser.put("202", setBrEmpData("005272","莊雅嵐","02", "27587590","171", "<EMAIL>","0922793084"));
		brUser.put("203", setBrEmpData("008291","蘇麗文","03", "5733399", "507", "<EMAIL>","0911175669"));
		brUser.put("204", setBrEmpData("006277","吳玉絹","04", "22234021","306", "<EMAIL>","0960388382"));
		brUser.put("205", setBrEmpData("002999","楊富美","07", "2515111", "338", "<EMAIL>","0953089207"));
		brUser.put("206", setBrEmpData("004926","侯劍秋","02", "29608989","123", "<EMAIL>","0920010088"));
		brUser.put("207", setBrEmpData("003855","許章億","03", "3327126", "50",  "<EMAIL>","0935337219"));
		brUser.put("208", setBrEmpData("008162","羅唯碩","03", "5589968", "27",  "<EMAIL>","0911770465"));
		brUser.put("210", setBrEmpData("004345","余淑菁","02", "87716355","312", "<EMAIL>","0933062924"));
		brUser.put("212", setBrEmpData("003697","許薰仁","05", "2780148", "118", "<EMAIL>","0928495866"));
		brUser.put("213", setBrEmpData("005320","王藍憶","06", "2292131", "307", "<EMAIL>","0975957618"));
		brUser.put("214", setBrEmpData("005499","黃雅鈴","04", "24619000","212", "<EMAIL>","0968619917"));
		brUser.put("215", setBrEmpData("004777","曾麗君","03", "5773155", "234", "<EMAIL>","0927567648"));
		brUser.put("216", setBrEmpData("011213","莊嘉峻","02", "27203566","117", "<EMAIL>","0931830611"));
		brUser.put("219", setBrEmpData("006200","陳信文","02", "29748811","33",  "<EMAIL>","0918468788"));
		brUser.put("220", setBrEmpData("007282","吳佳蕙","04", "7613111", "226", "<EMAIL>","0919063335"));
		brUser.put("226", setBrEmpData("008242","徐光裕","02", "27196128","27",  "<EMAIL>","0955933201"));
		brUser.put("227", setBrEmpData("004448","施宏泰","07", "2265181", "281", "<EMAIL>","0928750529"));
		brUser.put("228", setBrEmpData("007788","陳瑞盛","03", "9611262", "26",  "<EMAIL>","0912288079"));
		brUser.put("229", setBrEmpData("009314","吳秉縉","02", "27037576","209", "<EMAIL>","0987750867"));
		brUser.put("231", setBrEmpData("009416","鍾家慶","02", "25683658","19",  "<EMAIL>","0911486312"));
		brUser.put("232", setBrEmpData("006073","謝坤旺","04", "25588855","222", "<EMAIL>","0965261691"));
		brUser.put("233", setBrEmpData("004080","施偉祥","04", "7788111", "206", "<EMAIL>","0928362627"));
		brUser.put("234", setBrEmpData("006511","郭守明","02", "25671488","36",  "<EMAIL>","0932234299"));
		brUser.put("235", setBrEmpData("010435","林哲榮","02", "29986661","215", "<EMAIL>","0928382420"));
		brUser.put("236", setBrEmpData("007746","黃藍蒂","03", "4262366", "17",  "<EMAIL>","0928211398"));
		brUser.put("237", setBrEmpData("006112","徐業忠","02", "87983588","116", "<EMAIL>","0953109688"));
		brUser.put("238", setBrEmpData("003674","陳秀慈","02", "22314567","204", "<EMAIL>","0952839131"));
		brUser.put("240", setBrEmpData("006442","陳慶翰","02", "23888668","610", "<EMAIL>","0933911345"));
		brUser.put("241", setBrEmpData("008873","卓啟忠","04", "24180929","208", "<EMAIL>","0953551806"));
		brUser.put("242", setBrEmpData("006677","黃正安","06", "5052828", "119", "<EMAIL>","0916125711"));
	}


	private Map<String, String> setBrEmpData(String empId, String empName, String areaCode, String phone,
			String extension, String email, String cellphone) {
		Map<String, String> value = new HashMap<String, String>();
		value.put("empId", empId);
		value.put("empName", empName);
		value.put("areaCode", areaCode);
		value.put("phone", phone);
		value.put("extension", extension);
		value.put("email", email);
		value.put("cellphone", cellphone);
		return value;

	}
}
