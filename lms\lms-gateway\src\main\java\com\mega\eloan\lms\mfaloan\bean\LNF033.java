/* 
 * LNF033.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 期付金控制檔 **/
public class LNF033 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 分行代號
	 * <p/>
	 * NOTNULL,
	 */
	@Size(max = 3)
	@Column(name = "LNF033_BR_NO", length = 3, columnDefinition = "CHAR(03)")
	private String lnf033_br_no;

	/**
	 * 放款帳號
	 * <p/>
	 * NOTNULL,
	 */
	@Size(max = 14)
	@Column(name = "LNF033_LOAN_NO", length = 14, columnDefinition = "CHAR(14)")
	private String lnf033_loan_no;

	/**
	 * 科目
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 8)
	@Column(name = "LNF033_ACT_CODE", length = 8, columnDefinition = "CHAR(08)")
	private String lnf033_act_code;

	/**
	 * 已還期數
	 * <p/>
	 * NOTNULL
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_RT_TERM", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf033_rt_term;

	/**
	 * 期付金繳款週期
	 * <p/>
	 * NOTNULL<br/>
	 * 1：月繳，2：雙繳週
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTRT_CYCL", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intrt_cycl;

	/**
	 * 本金平均攤還金額
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 償還方式為本金平均攤還適用
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_RT_PRINCPL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf033_rt_princpl;

	/** 下次繳費日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_NT_RT_DATE", columnDefinition = "DATE")
	private Date lnf033_nt_rt_date;

	/**
	 * 期付金本金
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_RT_BAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf033_rt_bal;

	/**
	 * 期付金利息
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_RT_INT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf033_rt_int;

	/**
	 * 展延累計應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_RCV_INT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf033_rcv_int;

	/** 上次還款日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_LST_RT_DATE", columnDefinition = "DATE")
	private Date lnf033_lst_rt_date;

	/**
	 * 扣帳基準日
	 * <p/>
	 * NOTNULL
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_INT_RT_DD", columnDefinition = "DECIMAL(02,0)")
	private Integer lnf033_int_rt_dd;

	/**
	 * 償還方式
	 * <p/>
	 * NOTNULL<br/>
	 * 2:本息平均攤還<br/>
	 * 3:本金平均攤還
	 */
	@Size(max = 1)
	@Column(name = "LNF033_RT_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_rt_type;

	/**
	 * 總期數
	 * <p/>
	 * NOTNULL
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_TOT_TERM", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf033_tot_term;

	/**
	 * 遲延利息加碼
	 * <p/>
	 * NOTNULL
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_OVDUE_PNT", columnDefinition = "DEC(06,4)")
	private BigDecimal lnf033_ovdue_pnt;

	/**
	 * 違約計算條件
	 * <p/>
	 * NOTNULL
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_OV_PNT", columnDefinition = "DEC(06,4)")
	private BigDecimal lnf033_ov_pnt;

	/**
	 * 優惠起期一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_BEGTERM_1", columnDefinition = "DEC(03,0)")
	private Integer lnf033_begterm_1;

	/**
	 * 優惠迄期一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ENDTERM_1", columnDefinition = "DEC(03,0)")
	private Integer lnf033_endterm_1;

	/**
	 * 優惠利率代碼一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF033_INT_CODE_1", length = 2, columnDefinition = "CHAR(02)")
	private String lnf033_int_code_1;

	/**
	 * 優惠加減碼一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_INT_SPRD_1", columnDefinition = "DECIMAL(06,4)")
	private BigDecimal lnf033_int_sprd_1;

	/**
	 * 優惠利率方式一
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INT_TYPE_1", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_int_type_1;

	/**
	 * 優惠利率變動方式一
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTCHG_1", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intchg_1;

	/**
	 * 優惠利率調整月數一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTCHG_CY_1", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intchg_cy_1;

	/**
	 * 優惠起期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_BEGTERM_2", columnDefinition = "DEC(03,0)")
	private Integer lnf033_begterm_2;

	/**
	 * 優惠迄期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ENDTERM_2", columnDefinition = "DEC(03,0)")
	private Integer lnf033_endterm_2;

	/**
	 * 優惠利率代碼二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF033_INT_CODE_2", length = 2, columnDefinition = "CHAR(02)")
	private String lnf033_int_code_2;

	/**
	 * 優惠加減碼二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_INT_SPRD_2", columnDefinition = "DECIMAL(06,4)")
	private BigDecimal lnf033_int_sprd_2;

	/**
	 * 優惠利率方式二
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INT_TYPE_2", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_int_type_2;

	/**
	 * 優惠利率變動方式二
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTCHG_2", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intchg_2;

	/**
	 * 優惠利率調整月數二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTCHG_CY_2", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intchg_cy_2;

	/**
	 * 優惠起期三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_BEGTERM_3", columnDefinition = "DEC(03,0)")
	private Integer lnf033_begterm_3;

	/**
	 * 優惠迄期三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ENDTERM_3", columnDefinition = "DEC(03,0)")
	private Integer lnf033_endterm_3;

	/**
	 * 優惠利率代碼三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF033_INT_CODE_3", length = 2, columnDefinition = "CHAR(02)")
	private String lnf033_int_code_3;

	/**
	 * 優惠加減碼三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_INT_SPRD_3", columnDefinition = "DECIMAL(06,4)")
	private BigDecimal lnf033_int_sprd_3;

	/**
	 * 優惠利率方式三
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INT_TYPE_3", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_int_type_3;

	/**
	 * 優惠利率變動方式三
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTCHG_3", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intchg_3;

	/**
	 * 優惠利率調整月數三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTCHG_CY_3", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intchg_cy_3;

	/**
	 * 行銷種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (原為”是否辦理信保”，現暫不用)
	 */
	@Size(max = 1)
	@Column(name = "LNF033_CREDIT_INS", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_credit_ins;

	/**
	 * 行銷代號
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (原為”信用保險公司編號”，001：COSTCO卡，002：FANCY卡，003：營業部股票分潤集中業務，004：FANCY卡－安泰人壽)
	 */
	@Size(max = 3)
	@Column(name = "LNF033_INSC_NO", length = 3, columnDefinition = "CHAR(03)")
	private String lnf033_insc_no;

	/**
	 * 不重算期付金(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_F_RT_AMT", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_f_rt_amt;

	/**
	 * 是否列印繳息證明(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INT_LIST", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_int_list;

	/**
	 * 自用住宅(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_OWN_HOUSE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_own_house;

	/**
	 * 展延種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：921展延<br/>
	 * 2：非志願性勞工失業展延<br/>
	 * 3：交銀世貿展延案
	 */
	@Size(max = 1)
	@Column(name = "LNF033_DEF_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_def_type;

	/**
	 * 展延項目
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：本金展延<br/>
	 * 2：本金及利息展延
	 */
	@Size(max = 1)
	@Column(name = "LNF033_DEF_ITEM", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_def_item;

	/**
	 * 本金展延起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_PRI_DEF_BEG", columnDefinition = "DEC(03,0)")
	private Integer lnf033_pri_def_beg;

	/**
	 * 本金展延迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_PRI_DEF_DUE", columnDefinition = "DEC(03,0)")
	private Integer lnf033_pri_def_due;

	/**
	 * 利息展延起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_INT_DEF_BEG", columnDefinition = "DEC(03,0)")
	private Integer lnf033_int_def_beg;

	/**
	 * 利息展延迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_INT_DEF_DUE", columnDefinition = "DEC(03,0)")
	private Integer lnf033_int_def_due;

	/**
	 * 應收利息攤還截止期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_LST_INT_DUE", columnDefinition = "DEC(03,0)")
	private Integer lnf033_lst_int_due;

	/**
	 * 服務單位統編
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 11)
	@Column(name = "LNF033_GRP_NO", length = 11, columnDefinition = "CHAR(11)")
	private String lnf033_grp_no;

	/**
	 * 批號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 4)
	@Column(name = "LNF033_GRP_SEQ", length = 4, columnDefinition = "CHAR(04)")
	private String lnf033_grp_seq;

	/**
	 * 勞貸收件編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 8)
	@Column(name = "LNF033_RECV_NO", length = 8, columnDefinition = "CHAR(08)")
	private String lnf033_recv_no;

	/**
	 * 勞貸中籤年度
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_PLAN_YEAR", columnDefinition = "DEC(3,0)")
	private Integer lnf033_plan_year;

	/**
	 * 勞貸中籤編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 7)
	@Column(name = "LNF033_LNUM", length = 7, columnDefinition = "CHAR(07)")
	private String lnf033_lnum;

	/**
	 * 輔購市府核准年
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_CITY_YYY", columnDefinition = "DEC(3,0)")
	private Integer lnf033_city_yyy;

	/**
	 * 輔購市府名冊編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 9)
	@Column(name = "LNF033_CITY_NO", length = 9, columnDefinition = "CHAR(09)")
	private String lnf033_city_no;

	/**
	 * 是否自動進帳(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_ADP_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_adp_flag;

	/** 撥款自動進帳日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_ADP_DATE", columnDefinition = "DATE")
	private Date lnf033_adp_date;

	/**
	 * 解款行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 7)
	@Column(name = "LNF033_FAX_BK", length = 7, columnDefinition = "CHAR(07)")
	private String lnf033_fax_bk;

	/**
	 * 解款行帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 14)
	@Column(name = "LNF033_FAX_ACNO", length = 14, columnDefinition = "CHAR(14)")
	private String lnf033_fax_acno;

	/**
	 * 支票銀行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 7)
	@Column(name = "LNF033_CHECK_BK", length = 7, columnDefinition = "CHAR(07)")
	private String lnf033_check_bk;

	/**
	 * 支票號碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 14)
	@Column(name = "LNF033_CHECK_NO", length = 14, columnDefinition = "CHAR(14)")
	private String lnf033_check_no;

	/**
	 * 是否自動扣帳
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (取消，改由LNF030_INTRT_ACT處理)
	 */
	@Size(max = 1)
	@Column(name = "LNF033_ART_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_art_flag;

	/**
	 * 稅籍編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 14)
	@Column(name = "LNF033_HOUSE_NO", length = 14, columnDefinition = "CHAR(14)")
	private String lnf033_house_no;

	/**
	 * 稅籍地址
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 50)
	@Column(name = "LNF033_HOUSE_ADDR", length = 50, columnDefinition = "GRAPHIC(50)")
	private String lnf033_house_addr;

	/**
	 * 信保基金手續費計收方式
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：一次收足<br/>
	 * 2：逐年計收
	 */
	@Size(max = 1)
	@Column(name = "LNF033_IPFD_R_CYL", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_ipfd_r_cyl;

	/**
	 * 寬限起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ALLOW_BEG", columnDefinition = "DEC(03,0)")
	private Integer lnf033_allow_beg;

	/**
	 * 寬限迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ALLOW_END", columnDefinition = "DEC(03,0)")
	private Integer lnf033_allow_end;

	/**
	 * 適用本息平均攤還之本金比 改為”繳款容忍區間”
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 改為”繳款容忍區間”
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ALLOW_PER", columnDefinition = "DEC(02,0)")
	private Integer lnf033_allow_per;

	/**
	 * 行銷分行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF033_COM_BR", length = 3, columnDefinition = "CHAR(03)")
	private String lnf033_com_br;

	/**
	 * 應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * （提息用）
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_INCOM_TOT", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf033_incom_tot;

	/**
	 * 期付金勞宅中籤單位
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_LISSUE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_lissue;

	/**
	 * 扣帳成功日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 10)
	@Column(name = "LNF033_DRLOG_OK_DT", length = 10, columnDefinition = "CHAR(10)")
	private String lnf033_drlog_ok_dt;

	/**
	 * 執行錯誤程式名
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 8)
	@Column(name = "LNF033_ERR_JOB", length = 8, columnDefinition = "CHAR(08)")
	private String lnf033_err_job;

	/**
	 * 還款基準日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_RT_DD", columnDefinition = "DEC(02,0)")
	private Integer lnf033_rt_dd;

	/** 下次扣帳日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_NT_INTRT_DT", columnDefinition = "DATE")
	private Date lnf033_nt_intrt_dt;

	/**
	 * 優惠起期四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_BEGTERM_4", columnDefinition = "DEC(03,0)")
	private Integer lnf033_begterm_4;

	/**
	 * 優惠迄期四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ENDTERM_4", columnDefinition = "DEC(03,0)")
	private Integer lnf033_endterm_4;

	/**
	 * 優惠利率代碼四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF033_INT_CODE_4", length = 2, columnDefinition = "CHAR(02)")
	private String lnf033_int_code_4;

	/**
	 * 優惠加減碼四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_INT_SPRD_4", columnDefinition = "DECIMAL(06,4)")
	private BigDecimal lnf033_int_sprd_4;

	/**
	 * 優惠利率方式四
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INT_TYPE_4", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_int_type_4;

	/**
	 * 優惠利率變動方式四
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTCHG_4", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intchg_4;

	/**
	 * 優惠利率調整月數四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INTCHG_CY_4", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_intchg_cy_4;

	/**
	 * OFFSET生效日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_OFFSET_DT", columnDefinition = "DATE")
	private Date lnf033_offset_dt;

	/**
	 * 提前還本管制迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_RTCAP_END", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf033_rtcap_end;

	/**
	 * 提前還本違約金計算條件
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_RTCAP_PNT", columnDefinition = "DECIMAL(06,4)")
	private BigDecimal lnf033_rtcap_pnt;

	/**
	 * 代付費用註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_AGENCY", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_agency;

	/**
	 * 代付費用管制迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_AGENCY_END", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf033_agency_end;

	/**
	 * 引介[行員/房仲]代號=>拆成 LNF13E_MEGA_EMPNO, LNF13E_AGNT_NO 
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 5)
	@Column(name = "LNF033_COMPANY_ID", length = 5, columnDefinition = "CHAR(05)")
	private String lnf033_company_id;

	/**
	 * OFFSET註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_OFFSET_FLA", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_offset_fla;

	/**
	 * 累計應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * （L017二次撥款、L02C、L509可能產生）
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_ACCU_INT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf033_accu_int;

	/**
	 * 是否轉貸(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_CHG_CASE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_chg_case;

	/**
	 * 轉貸剩餘貸款本金
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_CHG_BAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf033_chg_bal;

	/** 原貸款到期日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_ORI_P_DATE", columnDefinition = "DATE")
	private Date lnf033_ori_p_date;

	/** 轉貸日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_CHG_DATE", columnDefinition = "DATE")
	private Date lnf033_chg_date;

	/**
	 * 轉出之原金融機構代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 7)
	@Column(name = "LNF033_ORI_BK_CD", length = 7, columnDefinition = "CHAR(07)")
	private String lnf033_ori_bk_cd;

	/**
	 * 抵利帳號(目前不用) (改為代收之繳款編號後11位
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 11)
	@Column(name = "LNF033_OFFSET_ACNO", length = 11, columnDefinition = "CHAR(11)")
	private String lnf033_offset_acno;

	/** 扣帳成功日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_9160_DATE", columnDefinition = "DATE")
	private Date lnf033_9160_date;

	/**
	 * 勞貸申請人ID
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 11)
	@Column(name = "LNF033_APPLID_ID", length = 11, columnDefinition = "CHAR(11)")
	private String lnf033_applid_id;

	/** 房屋所有權取得日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_HOUSE_DATE", columnDefinition = "DATE")
	private Date lnf033_house_date;

	/**
	 * 仲介公司統編
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 11)
	@Column(name = "LNF033_AGENCY_ID", length = 11, columnDefinition = "CHAR(11)")
	private String lnf033_agency_id;

	/**
	 * 仲介獎金比率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_AGEN_RATIO", columnDefinition = "DEC(06,4)")
	private BigDecimal lnf033_agen_ratio;

	/** 帳務補登日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_BACKVAL_DT", columnDefinition = "DATE")
	private Date lnf033_backval_dt;

	/**
	 * 提前還本管制起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_RTCAP_BEG", columnDefinition = "DEC(3,0)")
	private Integer lnf033_rtcap_beg;

	/**
	 * 提前還本管制起期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_RTCAP_BEG2", columnDefinition = "DEC(3,0)")
	private Integer lnf033_rtcap_beg2;

	/**
	 * 提前還本管制迄期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_RTCAP_END2", columnDefinition = "DEC(3,0)")
	private Integer lnf033_rtcap_end2;

	/**
	 * 提前還本違約金計算條件二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_RTCAP_PNT2", columnDefinition = "DEC(6,4)")
	private BigDecimal lnf033_rtcap_pnt2;

	/**
	 * 搭配項目
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1:UP房貸,A:歡喜理財家
	 */
	@Size(max = 1)
	@Column(name = "LNF033_MATCH_ITEM", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_match_item;

	/**
	 * UP起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_UP_BEG", columnDefinition = "DEC(3,0)")
	private Integer lnf033_up_beg;

	/**
	 * UP止期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_UP_END", columnDefinition = "DEC(3,0)")
	private Integer lnf033_up_end;

	/**
	 * UP利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF033_UP_RATE", columnDefinition = "DEC(6,4)")
	private BigDecimal lnf033_up_rate;

	/** 產生扣帳日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_915X_DATE", columnDefinition = "DATE")
	private Date lnf033_915x_date;

	/** 到期日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_DUE_DATE", columnDefinition = "DATE")
	private Date lnf033_due_date;

	/**
	 * 固定期付金起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_FIX_BEG", columnDefinition = "DEC(3,0)")
	private Integer lnf033_fix_beg;

	/**
	 * 固定期付金迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_FIX_END", columnDefinition = "DEC(3,0)")
	private Integer lnf033_fix_end;

	/**
	 * 固定期付金金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_FIX_AMT", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf033_fix_amt;

	/**
	 * 省息遞減註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF033_DEC_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf033_dec_flag;

	/**
	 * 遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 6, groups = Check.class)
	@Column(name = "LNF033_DEC_SPRD", columnDefinition = "DEC(8,6)")
	private BigDecimal lnf033_dec_sprd;

	/**
	 * 利率方案
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF033_RATE_PLAN", length = 2, columnDefinition = "CHAR(02)")
	private String lnf033_rate_plan;

	/**
	 * 產品方案
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF033_PROD_PLAN", length = 2, columnDefinition = "CHAR(02)")
	private String lnf033_prod_plan;

	/**
	 * 搭配省息遞減起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_DEC_BEG", columnDefinition = "DEC(3,0)")
	private Integer lnf033_dec_beg;

	/**
	 * 借款繳保費之金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_INSC_AMT", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf033_insc_amt;

	/**
	 * 整合住宅方案核准編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 10)
	@Column(name = "LNF033_CPAMI_NO", length = 10, columnDefinition = "CHAR(10)")
	private String lnf033_cpami_no;

	/**
	 * 產品次分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF033_SUB_CLASS", length = 2, columnDefinition = "CHAR(02)")
	private String lnf033_sub_class;

	/**
	 * 申請/核准編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 30)
	@Column(name = "LNF033_APPLY_NO", length = 30, columnDefinition = "CHAR(30)")
	private String lnf033_apply_no;

	/** 類別轉換日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_TRANS_DATE", columnDefinition = "DATE")
	private Date lnf033_trans_date;

	/** 停止補貼日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_STOP_DATE", columnDefinition = "DATE")
	private Date lnf033_stop_date;

	/**
	 * 每期固定多還本金金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_ADD_PRINCPL", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf033_add_princpl;

	/**
	 * 多還本金起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ADD_BEGTERM", columnDefinition = "DEC(3,0)")
	private Integer lnf033_add_begterm;

	/**
	 * 多還本金迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_ADD_ENDTERM", columnDefinition = "DEC(3,0)")
	private Integer lnf033_add_endterm;

	/**
	 * 暫收款
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_TEMP_AMT", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf033_temp_amt;

	/** 繳款通知單產生日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_NOTIFY_DATE", columnDefinition = "DATE")
	private Date lnf033_notify_date;
	
	/**
	 * 風險權數
	 * <ul>
	 * <li>2022-02 詢問8組 林育龍，之前判斷住宅用不動產時 首撥日 >=100-04-21 要去看 LNF033_OWN_HOUSE,LN033_RISK_RATING這邊的權數, 不過採用 LTV 法就不再適用了
	 * </li>
	 * <li>J-111-0096 自 2021-06-30起，因應本行「不動產暴險」改以貸放比率(LTV)決定適用之風險權數，個人戶授信案件免填「風險權數」，新做案件上傳到 RISK_RATING 的值改為 "-1"
	 * </li>
	 * <li>trigger 由 ELF501 改 LNF033 , 可用 SELECT * FROM SYSIBM.SYSTRIGGERS where name in ('LNTR0030','LNTR0031')
	 * </li>
	 * </ul>
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_RISK_RATING", columnDefinition = "DEC(3,0)")
	private BigDecimal lnf033_risk_rating;
	
	/**
	 * 所有人統編
	 * <p/>
	 */
	@Size(max = 10)
	@Column(name = "LNF033_OWNER_ID", length = 10, columnDefinition = "CHAR(10)")
	private String lnf033_owner_id;
	
	/**
	 * 所有人名稱
	 * <p/>
	 */
	@Size(max = 63)
	@Column(name = "LNF033_OWNER_NAME", length = 63, columnDefinition = "CHAR(63)")
	private String lnf033_owner_name;

	/**
	 * 縣市首購核准編號
	 * <p/>
	 */
	@Size(max = 9)
	@Column(name = "LNF033_KG_AGREE_NO", length = 9, columnDefinition = "CHAR(9)")
	private String lnf033_kg_agree_no; 
	
	/** 縣市首購核准日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_KG_AGREE_DT", columnDefinition = "DATE")
	private Date lnf033_kg_agree_dt;
	
	/** 縣市首購終止補貼日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_KG_END_DATE", columnDefinition = "DATE")
	private Date lnf033_kg_end_date;
	
	/**
	 * 縣市終止補貼原因
	 * <p/>
	 */
	@Size(max = 1)
	@Column(name = "LNF033_KG_END_CODE", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_kg_end_code;
	
	/** 縣市首購補貼起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_KG_INT_DATE", columnDefinition = "DATE")
	private Date lnf033_kg_int_date;
	
	/**
	 * 是否搭配房貸壽險
	 * <p/>
	 */
	@Size(max = 1)
	@Column(name = "LNF033_RMBINS_FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_rmbins_flag;

	/**
	 * 是否搭配房貸壽險利率優惠方案
	 * <p/>
	 */
	@Size(max = 1)
	@Column(name = "LNF033_RMBINT_FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_rmbint_flag;
	
	/**
	 * 搭配房貸壽險利率優惠方案期數
	 * <p/>
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF033_RMBINT_TERM", columnDefinition = "DEC(3,0)")
	private Integer lnf033_rmbint_term;

	/**
	 * 是否保費融資
	 * <p/>
	 */
	@Size(max = 1)
	@Column(name = "LNF033_INS_FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_ins_flag;
	
	/**
	 * 保費融資金額
	 * <p/>
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_INS_LOANBAL", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf033_ins_loanbal;
	
	/**
	 * 自動進帳金額
	 * <p/>
	 */
	@Digits(integer = 15, fraction = 2, groups = Check.class)
	@Column(name = "LNF033_ADP_AMT", columnDefinition = "DEC(15,2)")
	private BigDecimal lnf033_adp_amt;
	
	/**
	 * 縣市首購縣市代碼
	 * Ａ台北市、Ｂ台中市、Ｃ基隆市、Ｄ台南市、Ｅ高雄市、Ｆ新北市、Ｇ宜蘭縣、Ｈ桃園縣、Ｉ嘉義市、Ｊ新竹縣<br/>
     * Ｋ苗栗縣、Ｌ台中縣、Ｍ南投縣、Ｎ彰化縣、Ｏ新竹市、Ｐ雲林縣、Ｑ嘉義縣、Ｒ台南縣、Ｓ高雄縣、Ｔ屏東縣<br/>
     * Ｕ花蓮縣、Ｖ台東縣、Ｗ金門縣、Ｘ澎湖縣、Ｙ陽明山、Ｚ連江縣、0無<br/>
	 * <p/>
	 */
	@Size(max = 1)
	@Column(name = "LNF033_KG_AREA", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_kg_area;
	
	/**
	 * 職工編號
	 */
	@Size(max = 20)
	@Column(name = "LNF033_STAFFNO", length = 20, columnDefinition = "CHAR(20)")
	private String lnf033_staffNo;
	
	/**
	 * 建物所有權是否已移轉他人
	 */
	@Size(max = 1)
	@Column(name = "LNF033_OWN_TRS_FG", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_own_trs_fg;
	
	/**
	 * 契約種類{1:個人購屋貸款定型化契約, 2:消費性無擔保貸款定型化契約, 3:其他類契約}
	 */
	@Size(max = 1)
	@Column(name = "LNF033_CNTRNO_TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_cntrNo_type;
	 
	/**
	 * 當lnf033_def_type＝8-受災居民原債務展延，展延種類細項{1:自用住宅購屋貸款展延, 2:非自用住宅之購屋貸款展延, 3:以房屋為擔保之其他貸款及其他擔保貸款, 4:債務協商債務, 5:無擔保貸款}
	 */
	@Size(max = 1)
	@Column(name = "LNF033_DEF_TYPE_S", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_def_type_s;
	
	/**
	 * 當lnf033_def_type＝8-受災居民原債務展延，災害種類{01:０２０６台南地震震災, 02:蘇迪勒風災, 03:０２０６花蓮地震震災, 04:COVID-19（武漢疫情）, 99:其他}
	 */
	@Size(max = 2)
	@Column(name = "LNF033_DEF_DISASTP", length = 2, columnDefinition = "CHAR(2)")
	private String lnf033_def_disastp;

	/**
	 * 引介房仲收取回饋金
	 */
	@Size(max = 1)
	@Column(name = "LNF033_AGNT_FBFG", length = 1, columnDefinition = "CHAR(1)")
	private String lnf033_agnt_fbfg;
	
	/** 首次還款日 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF033_1ST_RT_DT", columnDefinition = "DATE")
	private Date lnf033_1st_rt_dt; 
	
	/**
	 * 取得分行代號
	 * <p/>
	 * NOTNULL,
	 */
	public String getLnf033_br_no() {
		return this.lnf033_br_no;
	}

	/**
	 * 設定分行代號
	 * <p/>
	 * NOTNULL,
	 **/
	public void setLnf033_br_no(String value) {
		this.lnf033_br_no = value;
	}

	/**
	 * 取得放款帳號
	 * <p/>
	 * NOTNULL,
	 */
	public String getLnf033_loan_no() {
		return this.lnf033_loan_no;
	}

	/**
	 * 設定放款帳號
	 * <p/>
	 * NOTNULL,
	 **/
	public void setLnf033_loan_no(String value) {
		this.lnf033_loan_no = value;
	}

	/**
	 * 取得科目
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_act_code() {
		return this.lnf033_act_code;
	}

	/**
	 * 設定科目
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_act_code(String value) {
		this.lnf033_act_code = value;
	}

	/**
	 * 取得已還期數
	 * <p/>
	 * NOTNULL
	 */
	public Integer getLnf033_rt_term() {
		return this.lnf033_rt_term;
	}

	/**
	 * 設定已還期數
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf033_rt_term(Integer value) {
		this.lnf033_rt_term = value;
	}

	/**
	 * 取得期付金繳款週期
	 * <p/>
	 * NOTNULL<br/>
	 * 1：月繳，2：雙繳週
	 */
	public String getLnf033_intrt_cycl() {
		return this.lnf033_intrt_cycl;
	}

	/**
	 * 設定期付金繳款週期
	 * <p/>
	 * NOTNULL<br/>
	 * 1：月繳，2：雙繳週
	 **/
	public void setLnf033_intrt_cycl(String value) {
		this.lnf033_intrt_cycl = value;
	}

	/**
	 * 取得本金平均攤還金額
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 償還方式為本金平均攤還適用
	 */
	public BigDecimal getLnf033_rt_princpl() {
		return this.lnf033_rt_princpl;
	}

	/**
	 * 設定本金平均攤還金額
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 償還方式為本金平均攤還適用
	 **/
	public void setLnf033_rt_princpl(BigDecimal value) {
		this.lnf033_rt_princpl = value;
	}

	/** 取得下次繳費日 **/
	public Date getLnf033_nt_rt_date() {
		return this.lnf033_nt_rt_date;
	}

	/** 設定下次繳費日 **/
	public void setLnf033_nt_rt_date(Date value) {
		this.lnf033_nt_rt_date = value;
	}

	/**
	 * 取得期付金本金
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_rt_bal() {
		return this.lnf033_rt_bal;
	}

	/**
	 * 設定期付金本金
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rt_bal(BigDecimal value) {
		this.lnf033_rt_bal = value;
	}

	/**
	 * 取得期付金利息
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_rt_int() {
		return this.lnf033_rt_int;
	}

	/**
	 * 設定期付金利息
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rt_int(BigDecimal value) {
		this.lnf033_rt_int = value;
	}

	/**
	 * 取得展延累計應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_rcv_int() {
		return this.lnf033_rcv_int;
	}

	/**
	 * 設定展延累計應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rcv_int(BigDecimal value) {
		this.lnf033_rcv_int = value;
	}

	/** 取得上次還款日 **/
	public Date getLnf033_lst_rt_date() {
		return this.lnf033_lst_rt_date;
	}

	/** 設定上次還款日 **/
	public void setLnf033_lst_rt_date(Date value) {
		this.lnf033_lst_rt_date = value;
	}

	/**
	 * 取得扣帳基準日
	 * <p/>
	 * NOTNULL
	 */
	public Integer getLnf033_int_rt_dd() {
		return this.lnf033_int_rt_dd;
	}

	/**
	 * 設定扣帳基準日
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf033_int_rt_dd(Integer value) {
		this.lnf033_int_rt_dd = value;
	}

	/**
	 * 取得償還方式
	 * <p/>
	 * NOTNULL<br/>
	 * 2:本息平均攤還<br/>
	 * 3:本金平均攤還
	 */
	public String getLnf033_rt_type() {
		return this.lnf033_rt_type;
	}

	/**
	 * 設定償還方式
	 * <p/>
	 * NOTNULL<br/>
	 * 2:本息平均攤還<br/>
	 * 3:本金平均攤還
	 **/
	public void setLnf033_rt_type(String value) {
		this.lnf033_rt_type = value;
	}

	/**
	 * 取得總期數
	 * <p/>
	 * NOTNULL
	 */
	public Integer getLnf033_tot_term() {
		return this.lnf033_tot_term;
	}

	/**
	 * 設定總期數
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf033_tot_term(Integer value) {
		this.lnf033_tot_term = value;
	}

	/**
	 * 取得遲延利息加碼
	 * <p/>
	 * NOTNULL
	 */
	public BigDecimal getLnf033_ovdue_pnt() {
		return this.lnf033_ovdue_pnt;
	}

	/**
	 * 設定遲延利息加碼
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf033_ovdue_pnt(BigDecimal value) {
		this.lnf033_ovdue_pnt = value;
	}

	/**
	 * 取得違約計算條件
	 * <p/>
	 * NOTNULL
	 */
	public BigDecimal getLnf033_ov_pnt() {
		return this.lnf033_ov_pnt;
	}

	/**
	 * 設定違約計算條件
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf033_ov_pnt(BigDecimal value) {
		this.lnf033_ov_pnt = value;
	}

	/**
	 * 取得優惠起期一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_begterm_1() {
		return this.lnf033_begterm_1;
	}

	/**
	 * 設定優惠起期一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_begterm_1(Integer value) {
		this.lnf033_begterm_1 = value;
	}

	/**
	 * 取得優惠迄期一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_endterm_1() {
		return this.lnf033_endterm_1;
	}

	/**
	 * 設定優惠迄期一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_endterm_1(Integer value) {
		this.lnf033_endterm_1 = value;
	}

	/**
	 * 取得優惠利率代碼一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_int_code_1() {
		return this.lnf033_int_code_1;
	}

	/**
	 * 設定優惠利率代碼一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_code_1(String value) {
		this.lnf033_int_code_1 = value;
	}

	/**
	 * 取得優惠加減碼一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_int_sprd_1() {
		return this.lnf033_int_sprd_1;
	}

	/**
	 * 設定優惠加減碼一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_sprd_1(BigDecimal value) {
		this.lnf033_int_sprd_1 = value;
	}

	/**
	 * 取得優惠利率方式一
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 */
	public String getLnf033_int_type_1() {
		return this.lnf033_int_type_1;
	}

	/**
	 * 設定優惠利率方式一
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 **/
	public void setLnf033_int_type_1(String value) {
		this.lnf033_int_type_1 = value;
	}

	/**
	 * 取得優惠利率變動方式一
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 */
	public String getLnf033_intchg_1() {
		return this.lnf033_intchg_1;
	}

	/**
	 * 設定優惠利率變動方式一
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 **/
	public void setLnf033_intchg_1(String value) {
		this.lnf033_intchg_1 = value;
	}

	/**
	 * 取得優惠利率調整月數一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_intchg_cy_1() {
		return this.lnf033_intchg_cy_1;
	}

	/**
	 * 設定優惠利率調整月數一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_intchg_cy_1(String value) {
		this.lnf033_intchg_cy_1 = value;
	}

	/**
	 * 取得優惠起期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_begterm_2() {
		return this.lnf033_begterm_2;
	}

	/**
	 * 設定優惠起期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_begterm_2(Integer value) {
		this.lnf033_begterm_2 = value;
	}

	/**
	 * 取得優惠迄期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_endterm_2() {
		return this.lnf033_endterm_2;
	}

	/**
	 * 設定優惠迄期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_endterm_2(Integer value) {
		this.lnf033_endterm_2 = value;
	}

	/**
	 * 取得優惠利率代碼二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_int_code_2() {
		return this.lnf033_int_code_2;
	}

	/**
	 * 設定優惠利率代碼二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_code_2(String value) {
		this.lnf033_int_code_2 = value;
	}

	/**
	 * 取得優惠加減碼二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_int_sprd_2() {
		return this.lnf033_int_sprd_2;
	}

	/**
	 * 設定優惠加減碼二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_sprd_2(BigDecimal value) {
		this.lnf033_int_sprd_2 = value;
	}

	/**
	 * 取得優惠利率方式二
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 */
	public String getLnf033_int_type_2() {
		return this.lnf033_int_type_2;
	}

	/**
	 * 設定優惠利率方式二
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 **/
	public void setLnf033_int_type_2(String value) {
		this.lnf033_int_type_2 = value;
	}

	/**
	 * 取得優惠利率變動方式二
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 */
	public String getLnf033_intchg_2() {
		return this.lnf033_intchg_2;
	}

	/**
	 * 設定優惠利率變動方式二
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 **/
	public void setLnf033_intchg_2(String value) {
		this.lnf033_intchg_2 = value;
	}

	/**
	 * 取得優惠利率調整月數二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_intchg_cy_2() {
		return this.lnf033_intchg_cy_2;
	}

	/**
	 * 設定優惠利率調整月數二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_intchg_cy_2(String value) {
		this.lnf033_intchg_cy_2 = value;
	}

	/**
	 * 取得優惠起期三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_begterm_3() {
		return this.lnf033_begterm_3;
	}

	/**
	 * 設定優惠起期三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_begterm_3(Integer value) {
		this.lnf033_begterm_3 = value;
	}

	/**
	 * 取得優惠迄期三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_endterm_3() {
		return this.lnf033_endterm_3;
	}

	/**
	 * 設定優惠迄期三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_endterm_3(Integer value) {
		this.lnf033_endterm_3 = value;
	}

	/**
	 * 取得優惠利率代碼三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_int_code_3() {
		return this.lnf033_int_code_3;
	}

	/**
	 * 設定優惠利率代碼三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_code_3(String value) {
		this.lnf033_int_code_3 = value;
	}

	/**
	 * 取得優惠加減碼三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_int_sprd_3() {
		return this.lnf033_int_sprd_3;
	}

	/**
	 * 設定優惠加減碼三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_sprd_3(BigDecimal value) {
		this.lnf033_int_sprd_3 = value;
	}

	/**
	 * 取得優惠利率方式三
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 */
	public String getLnf033_int_type_3() {
		return this.lnf033_int_type_3;
	}

	/**
	 * 設定優惠利率方式三
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 **/
	public void setLnf033_int_type_3(String value) {
		this.lnf033_int_type_3 = value;
	}

	/**
	 * 取得優惠利率變動方式三
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 */
	public String getLnf033_intchg_3() {
		return this.lnf033_intchg_3;
	}

	/**
	 * 設定優惠利率變動方式三
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 **/
	public void setLnf033_intchg_3(String value) {
		this.lnf033_intchg_3 = value;
	}

	/**
	 * 取得優惠利率調整月數三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_intchg_cy_3() {
		return this.lnf033_intchg_cy_3;
	}

	/**
	 * 設定優惠利率調整月數三
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_intchg_cy_3(String value) {
		this.lnf033_intchg_cy_3 = value;
	}

	/**
	 * 取得行銷種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (原為”是否辦理信保”，現暫不用)
	 */
	public String getLnf033_credit_ins() {
		return this.lnf033_credit_ins;
	}

	/**
	 * 設定行銷種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (原為”是否辦理信保”，現暫不用)
	 **/
	public void setLnf033_credit_ins(String value) {
		this.lnf033_credit_ins = value;
	}

	/**
	 * 取得行銷代號
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (原為”信用保險公司編號”，001：COSTCO卡，002：FANCY卡，003：營業部股票分潤集中業務，004：FANCY卡－安泰人壽)
	 */
	public String getLnf033_insc_no() {
		return this.lnf033_insc_no;
	}

	/**
	 * 設定行銷代號
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (原為”信用保險公司編號”，001：COSTCO卡，002：FANCY卡，003：營業部股票分潤集中業務，004：FANCY卡－安泰人壽)
	 **/
	public void setLnf033_insc_no(String value) {
		this.lnf033_insc_no = value;
	}

	/**
	 * 取得不重算期付金(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_f_rt_amt() {
		return this.lnf033_f_rt_amt;
	}

	/**
	 * 設定不重算期付金(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_f_rt_amt(String value) {
		this.lnf033_f_rt_amt = value;
	}

	/**
	 * 取得是否列印繳息證明(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_int_list() {
		return this.lnf033_int_list;
	}

	/**
	 * 設定是否列印繳息證明(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_list(String value) {
		this.lnf033_int_list = value;
	}

	/**
	 * 取得自用住宅(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_own_house() {
		return this.lnf033_own_house;
	}

	/**
	 * 設定自用住宅(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_own_house(String value) {
		this.lnf033_own_house = value;
	}

	/**
	 * 取得展延種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：921展延<br/>
	 * 2：非志願性勞工失業展延<br/>
	 * 3：交銀世貿展延案
	 */
	public String getLnf033_def_type() {
		return this.lnf033_def_type;
	}

	/**
	 * 設定展延種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：921展延<br/>
	 * 2：非志願性勞工失業展延<br/>
	 * 3：交銀世貿展延案
	 **/
	public void setLnf033_def_type(String value) {
		this.lnf033_def_type = value;
	}

	/**
	 * 取得展延項目
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：本金展延<br/>
	 * 2：本金及利息展延
	 */
	public String getLnf033_def_item() {
		return this.lnf033_def_item;
	}

	/**
	 * 設定展延項目
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：本金展延<br/>
	 * 2：本金及利息展延
	 **/
	public void setLnf033_def_item(String value) {
		this.lnf033_def_item = value;
	}

	/**
	 * 取得本金展延起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_pri_def_beg() {
		return this.lnf033_pri_def_beg;
	}

	/**
	 * 設定本金展延起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_pri_def_beg(Integer value) {
		this.lnf033_pri_def_beg = value;
	}

	/**
	 * 取得本金展延迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_pri_def_due() {
		return this.lnf033_pri_def_due;
	}

	/**
	 * 設定本金展延迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_pri_def_due(Integer value) {
		this.lnf033_pri_def_due = value;
	}

	/**
	 * 取得利息展延起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_int_def_beg() {
		return this.lnf033_int_def_beg;
	}

	/**
	 * 設定利息展延起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_def_beg(Integer value) {
		this.lnf033_int_def_beg = value;
	}

	/**
	 * 取得利息展延迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_int_def_due() {
		return this.lnf033_int_def_due;
	}

	/**
	 * 設定利息展延迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_def_due(Integer value) {
		this.lnf033_int_def_due = value;
	}

	/**
	 * 取得應收利息攤還截止期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_lst_int_due() {
		return this.lnf033_lst_int_due;
	}

	/**
	 * 設定應收利息攤還截止期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_lst_int_due(Integer value) {
		this.lnf033_lst_int_due = value;
	}

	/**
	 * 取得服務單位統編
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_grp_no() {
		return this.lnf033_grp_no;
	}

	/**
	 * 設定服務單位統編
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_grp_no(String value) {
		this.lnf033_grp_no = value;
	}

	/**
	 * 取得批號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_grp_seq() {
		return this.lnf033_grp_seq;
	}

	/**
	 * 設定批號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_grp_seq(String value) {
		this.lnf033_grp_seq = value;
	}

	/**
	 * 取得勞貸收件編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_recv_no() {
		return this.lnf033_recv_no;
	}

	/**
	 * 設定勞貸收件編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_recv_no(String value) {
		this.lnf033_recv_no = value;
	}

	/**
	 * 取得勞貸中籤年度
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_plan_year() {
		return this.lnf033_plan_year;
	}

	/**
	 * 設定勞貸中籤年度
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_plan_year(Integer value) {
		this.lnf033_plan_year = value;
	}

	/**
	 * 取得勞貸中籤編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_lnum() {
		return this.lnf033_lnum;
	}

	/**
	 * 設定勞貸中籤編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_lnum(String value) {
		this.lnf033_lnum = value;
	}

	/**
	 * 取得輔購市府核准年
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_city_yyy() {
		return this.lnf033_city_yyy;
	}

	/**
	 * 設定輔購市府核准年
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_city_yyy(Integer value) {
		this.lnf033_city_yyy = value;
	}

	/**
	 * 取得輔購市府名冊編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_city_no() {
		return this.lnf033_city_no;
	}

	/**
	 * 設定輔購市府名冊編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_city_no(String value) {
		this.lnf033_city_no = value;
	}

	/**
	 * 取得是否自動進帳(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_adp_flag() {
		return this.lnf033_adp_flag;
	}

	/**
	 * 設定是否自動進帳(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_adp_flag(String value) {
		this.lnf033_adp_flag = value;
	}

	/** 取得撥款自動進帳日期 **/
	public Date getLnf033_adp_date() {
		return this.lnf033_adp_date;
	}

	/** 設定撥款自動進帳日期 **/
	public void setLnf033_adp_date(Date value) {
		this.lnf033_adp_date = value;
	}

	/**
	 * 取得解款行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_fax_bk() {
		return this.lnf033_fax_bk;
	}

	/**
	 * 設定解款行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_fax_bk(String value) {
		this.lnf033_fax_bk = value;
	}

	/**
	 * 取得解款行帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_fax_acno() {
		return this.lnf033_fax_acno;
	}

	/**
	 * 設定解款行帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_fax_acno(String value) {
		this.lnf033_fax_acno = value;
	}

	/**
	 * 取得支票銀行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_check_bk() {
		return this.lnf033_check_bk;
	}

	/**
	 * 設定支票銀行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_check_bk(String value) {
		this.lnf033_check_bk = value;
	}

	/**
	 * 取得支票號碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_check_no() {
		return this.lnf033_check_no;
	}

	/**
	 * 設定支票號碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_check_no(String value) {
		this.lnf033_check_no = value;
	}

	/**
	 * 取得是否自動扣帳
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (取消，改由LNF030_INTRT_ACT處理)
	 */
	public String getLnf033_art_flag() {
		return this.lnf033_art_flag;
	}

	/**
	 * 設定是否自動扣帳
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * (取消，改由LNF030_INTRT_ACT處理)
	 **/
	public void setLnf033_art_flag(String value) {
		this.lnf033_art_flag = value;
	}

	/**
	 * 取得稅籍編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_house_no() {
		return this.lnf033_house_no;
	}

	/**
	 * 設定稅籍編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_house_no(String value) {
		this.lnf033_house_no = value;
	}

	/**
	 * 取得稅籍地址
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_house_addr() {
		return this.lnf033_house_addr;
	}

	/**
	 * 設定稅籍地址
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_house_addr(String value) {
		this.lnf033_house_addr = value;
	}

	/**
	 * 取得信保基金手續費計收方式
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：一次收足<br/>
	 * 2：逐年計收
	 */
	public String getLnf033_ipfd_r_cyl() {
		return this.lnf033_ipfd_r_cyl;
	}

	/**
	 * 設定信保基金手續費計收方式
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：一次收足<br/>
	 * 2：逐年計收
	 **/
	public void setLnf033_ipfd_r_cyl(String value) {
		this.lnf033_ipfd_r_cyl = value;
	}

	/**
	 * 取得寬限起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_allow_beg() {
		return this.lnf033_allow_beg;
	}

	/**
	 * 設定寬限起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_allow_beg(Integer value) {
		this.lnf033_allow_beg = value;
	}

	/**
	 * 取得寬限迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_allow_end() {
		return this.lnf033_allow_end;
	}

	/**
	 * 設定寬限迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_allow_end(Integer value) {
		this.lnf033_allow_end = value;
	}

	/**
	 * 取得適用本息平均攤還之本金比 改為”繳款容忍區間”
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 改為”繳款容忍區間”
	 */
	public Integer getLnf033_allow_per() {
		return this.lnf033_allow_per;
	}

	/**
	 * 設定適用本息平均攤還之本金比 改為”繳款容忍區間”
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 改為”繳款容忍區間”
	 **/
	public void setLnf033_allow_per(Integer value) {
		this.lnf033_allow_per = value;
	}

	/**
	 * 取得行銷分行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_com_br() {
		return this.lnf033_com_br;
	}

	/**
	 * 設定行銷分行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_com_br(String value) {
		this.lnf033_com_br = value;
	}

	/**
	 * 取得應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * （提息用）
	 */
	public BigDecimal getLnf033_incom_tot() {
		return this.lnf033_incom_tot;
	}

	/**
	 * 設定應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * （提息用）
	 **/
	public void setLnf033_incom_tot(BigDecimal value) {
		this.lnf033_incom_tot = value;
	}

	/**
	 * 取得期付金勞宅中籤單位
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_lissue() {
		return this.lnf033_lissue;
	}

	/**
	 * 設定期付金勞宅中籤單位
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_lissue(String value) {
		this.lnf033_lissue = value;
	}

	/**
	 * 取得扣帳成功日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_drlog_ok_dt() {
		return this.lnf033_drlog_ok_dt;
	}

	/**
	 * 設定扣帳成功日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_drlog_ok_dt(String value) {
		this.lnf033_drlog_ok_dt = value;
	}

	/**
	 * 取得執行錯誤程式名
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_err_job() {
		return this.lnf033_err_job;
	}

	/**
	 * 設定執行錯誤程式名
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_err_job(String value) {
		this.lnf033_err_job = value;
	}

	/**
	 * 取得還款基準日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_rt_dd() {
		return this.lnf033_rt_dd;
	}

	/**
	 * 設定還款基準日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rt_dd(Integer value) {
		this.lnf033_rt_dd = value;
	}

	/** 取得下次扣帳日 **/
	public Date getLnf033_nt_intrt_dt() {
		return this.lnf033_nt_intrt_dt;
	}

	/** 設定下次扣帳日 **/
	public void setLnf033_nt_intrt_dt(Date value) {
		this.lnf033_nt_intrt_dt = value;
	}

	/**
	 * 取得優惠起期四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_begterm_4() {
		return this.lnf033_begterm_4;
	}

	/**
	 * 設定優惠起期四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_begterm_4(Integer value) {
		this.lnf033_begterm_4 = value;
	}

	/**
	 * 取得優惠迄期四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_endterm_4() {
		return this.lnf033_endterm_4;
	}

	/**
	 * 設定優惠迄期四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_endterm_4(Integer value) {
		this.lnf033_endterm_4 = value;
	}

	/**
	 * 取得優惠利率代碼四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_int_code_4() {
		return this.lnf033_int_code_4;
	}

	/**
	 * 設定優惠利率代碼四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_code_4(String value) {
		this.lnf033_int_code_4 = value;
	}

	/**
	 * 取得優惠加減碼四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_int_sprd_4() {
		return this.lnf033_int_sprd_4;
	}

	/**
	 * 設定優惠加減碼四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_int_sprd_4(BigDecimal value) {
		this.lnf033_int_sprd_4 = value;
	}

	/**
	 * 取得優惠利率方式四
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 */
	public String getLnf033_int_type_4() {
		return this.lnf033_int_type_4;
	}

	/**
	 * 設定優惠利率方式四
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1：固定，2：機動，3：定期浮動
	 **/
	public void setLnf033_int_type_4(String value) {
		this.lnf033_int_type_4 = value;
	}

	/**
	 * 取得優惠利率變動方式四
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 */
	public String getLnf033_intchg_4() {
		return this.lnf033_intchg_4;
	}

	/**
	 * 設定優惠利率變動方式四
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * M：月，Y：年
	 **/
	public void setLnf033_intchg_4(String value) {
		this.lnf033_intchg_4 = value;
	}

	/**
	 * 取得優惠利率調整月數四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_intchg_cy_4() {
		return this.lnf033_intchg_cy_4;
	}

	/**
	 * 設定優惠利率調整月數四
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_intchg_cy_4(String value) {
		this.lnf033_intchg_cy_4 = value;
	}

	/**
	 * 取得OFFSET生效日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Date getLnf033_offset_dt() {
		return this.lnf033_offset_dt;
	}

	/**
	 * 設定OFFSET生效日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_offset_dt(Date value) {
		this.lnf033_offset_dt = value;
	}

	/**
	 * 取得提前還本管制迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_rtcap_end() {
		return this.lnf033_rtcap_end;
	}

	/**
	 * 設定提前還本管制迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rtcap_end(Integer value) {
		this.lnf033_rtcap_end = value;
	}

	/**
	 * 取得提前還本違約金計算條件
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_rtcap_pnt() {
		return this.lnf033_rtcap_pnt;
	}

	/**
	 * 設定提前還本違約金計算條件
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rtcap_pnt(BigDecimal value) {
		this.lnf033_rtcap_pnt = value;
	}

	/**
	 * 取得代付費用註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_agency() {
		return this.lnf033_agency;
	}

	/**
	 * 設定代付費用註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_agency(String value) {
		this.lnf033_agency = value;
	}

	/**
	 * 取得代付費用管制迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_agency_end() {
		return this.lnf033_agency_end;
	}

	/**
	 * 設定代付費用管制迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_agency_end(Integer value) {
		this.lnf033_agency_end = value;
	}

	/**
	 * 取得引介[行員/房仲]代號=>拆成 LNF13E_MEGA_EMPNO, LNF13E_AGNT_NO 
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_company_id() {
		return this.lnf033_company_id;
	}

	/**
	 * 設定引介[行員/房仲]代號=>拆成 LNF13E_MEGA_EMPNO, LNF13E_AGNT_NO 
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_company_id(String value) {
		this.lnf033_company_id = value;
	}

	/**
	 * 取得OFFSET註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_offset_fla() {
		return this.lnf033_offset_fla;
	}

	/**
	 * 設定OFFSET註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_offset_fla(String value) {
		this.lnf033_offset_fla = value;
	}

	/**
	 * 取得累計應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * （L017二次撥款、L02C、L509可能產生）
	 */
	public BigDecimal getLnf033_accu_int() {
		return this.lnf033_accu_int;
	}

	/**
	 * 設定累計應收利息
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * （L017二次撥款、L02C、L509可能產生）
	 **/
	public void setLnf033_accu_int(BigDecimal value) {
		this.lnf033_accu_int = value;
	}

	/**
	 * 取得是否轉貸(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_chg_case() {
		return this.lnf033_chg_case;
	}

	/**
	 * 設定是否轉貸(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_chg_case(String value) {
		this.lnf033_chg_case = value;
	}

	/**
	 * 取得轉貸剩餘貸款本金
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_chg_bal() {
		return this.lnf033_chg_bal;
	}

	/**
	 * 設定轉貸剩餘貸款本金
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_chg_bal(BigDecimal value) {
		this.lnf033_chg_bal = value;
	}

	/** 取得原貸款到期日 **/
	public Date getLnf033_ori_p_date() {
		return this.lnf033_ori_p_date;
	}

	/** 設定原貸款到期日 **/
	public void setLnf033_ori_p_date(Date value) {
		this.lnf033_ori_p_date = value;
	}

	/** 取得轉貸日期 **/
	public Date getLnf033_chg_date() {
		return this.lnf033_chg_date;
	}

	/** 設定轉貸日期 **/
	public void setLnf033_chg_date(Date value) {
		this.lnf033_chg_date = value;
	}

	/**
	 * 取得轉出之原金融機構代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_ori_bk_cd() {
		return this.lnf033_ori_bk_cd;
	}

	/**
	 * 設定轉出之原金融機構代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_ori_bk_cd(String value) {
		this.lnf033_ori_bk_cd = value;
	}

	/**
	 * 取得抵利帳號(目前不用) (改為代收之繳款編號後11位
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_offset_acno() {
		return this.lnf033_offset_acno;
	}

	/**
	 * 設定抵利帳號(目前不用) (改為代收之繳款編號後11位
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_offset_acno(String value) {
		this.lnf033_offset_acno = value;
	}

	/** 取得扣帳成功日 **/
	public Date getLnf033_9160_date() {
		return this.lnf033_9160_date;
	}

	/** 設定扣帳成功日 **/
	public void setLnf033_9160_date(Date value) {
		this.lnf033_9160_date = value;
	}

	/**
	 * 取得勞貸申請人ID
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_applid_id() {
		return this.lnf033_applid_id;
	}

	/**
	 * 設定勞貸申請人ID
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_applid_id(String value) {
		this.lnf033_applid_id = value;
	}

	/** 取得房屋所有權取得日 **/
	public Date getLnf033_house_date() {
		return this.lnf033_house_date;
	}

	/** 設定房屋所有權取得日 **/
	public void setLnf033_house_date(Date value) {
		this.lnf033_house_date = value;
	}

	/**
	 * 取得仲介公司統編
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_agency_id() {
		return this.lnf033_agency_id;
	}

	/**
	 * 設定仲介公司統編
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_agency_id(String value) {
		this.lnf033_agency_id = value;
	}

	/**
	 * 取得仲介獎金比率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_agen_ratio() {
		return this.lnf033_agen_ratio;
	}

	/**
	 * 設定仲介獎金比率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_agen_ratio(BigDecimal value) {
		this.lnf033_agen_ratio = value;
	}

	/** 取得帳務補登日 **/
	public Date getLnf033_backval_dt() {
		return this.lnf033_backval_dt;
	}

	/** 設定帳務補登日 **/
	public void setLnf033_backval_dt(Date value) {
		this.lnf033_backval_dt = value;
	}

	/**
	 * 取得提前還本管制起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_rtcap_beg() {
		return this.lnf033_rtcap_beg;
	}

	/**
	 * 設定提前還本管制起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rtcap_beg(Integer value) {
		this.lnf033_rtcap_beg = value;
	}

	/**
	 * 取得提前還本管制起期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_rtcap_beg2() {
		return this.lnf033_rtcap_beg2;
	}

	/**
	 * 設定提前還本管制起期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rtcap_beg2(Integer value) {
		this.lnf033_rtcap_beg2 = value;
	}

	/**
	 * 取得提前還本管制迄期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_rtcap_end2() {
		return this.lnf033_rtcap_end2;
	}

	/**
	 * 設定提前還本管制迄期二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rtcap_end2(Integer value) {
		this.lnf033_rtcap_end2 = value;
	}

	/**
	 * 取得提前還本違約金計算條件二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_rtcap_pnt2() {
		return this.lnf033_rtcap_pnt2;
	}

	/**
	 * 設定提前還本違約金計算條件二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rtcap_pnt2(BigDecimal value) {
		this.lnf033_rtcap_pnt2 = value;
	}

	/**
	 * 取得搭配項目
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1:UP房貸,A:歡喜理財家
	 */
	public String getLnf033_match_item() {
		return this.lnf033_match_item;
	}

	/**
	 * 設定搭配項目
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 1:UP房貸,A:歡喜理財家
	 **/
	public void setLnf033_match_item(String value) {
		this.lnf033_match_item = value;
	}

	/**
	 * 取得UP起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_up_beg() {
		return this.lnf033_up_beg;
	}

	/**
	 * 設定UP起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_up_beg(Integer value) {
		this.lnf033_up_beg = value;
	}

	/**
	 * 取得UP止期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_up_end() {
		return this.lnf033_up_end;
	}

	/**
	 * 設定UP止期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_up_end(Integer value) {
		this.lnf033_up_end = value;
	}

	/**
	 * 取得UP利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_up_rate() {
		return this.lnf033_up_rate;
	}

	/**
	 * 設定UP利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_up_rate(BigDecimal value) {
		this.lnf033_up_rate = value;
	}

	/** 取得產生扣帳日 **/
	public Date getLnf033_915x_date() {
		return this.lnf033_915x_date;
	}

	/** 設定產生扣帳日 **/
	public void setLnf033_915x_date(Date value) {
		this.lnf033_915x_date = value;
	}

	/** 取得到期日 **/
	public Date getLnf033_due_date() {
		return this.lnf033_due_date;
	}

	/** 設定到期日 **/
	public void setLnf033_due_date(Date value) {
		this.lnf033_due_date = value;
	}

	/**
	 * 取得固定期付金起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_fix_beg() {
		return this.lnf033_fix_beg;
	}

	/**
	 * 設定固定期付金起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_fix_beg(Integer value) {
		this.lnf033_fix_beg = value;
	}

	/**
	 * 取得固定期付金迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_fix_end() {
		return this.lnf033_fix_end;
	}

	/**
	 * 設定固定期付金迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_fix_end(Integer value) {
		this.lnf033_fix_end = value;
	}

	/**
	 * 取得固定期付金金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_fix_amt() {
		return this.lnf033_fix_amt;
	}

	/**
	 * 設定固定期付金金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_fix_amt(BigDecimal value) {
		this.lnf033_fix_amt = value;
	}

	/**
	 * 取得省息遞減註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_dec_flag() {
		return this.lnf033_dec_flag;
	}

	/**
	 * 設定省息遞減註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_dec_flag(String value) {
		this.lnf033_dec_flag = value;
	}

	/**
	 * 取得遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_dec_sprd() {
		return this.lnf033_dec_sprd;
	}

	/**
	 * 設定遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_dec_sprd(BigDecimal value) {
		this.lnf033_dec_sprd = value;
	}

	/**
	 * 取得利率方案
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_rate_plan() {
		return this.lnf033_rate_plan;
	}

	/**
	 * 設定利率方案
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_rate_plan(String value) {
		this.lnf033_rate_plan = value;
	}

	/**
	 * 取得產品方案
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_prod_plan() {
		return this.lnf033_prod_plan;
	}

	/**
	 * 設定產品方案
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_prod_plan(String value) {
		this.lnf033_prod_plan = value;
	}

	/**
	 * 取得搭配省息遞減起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_dec_beg() {
		return this.lnf033_dec_beg;
	}

	/**
	 * 設定搭配省息遞減起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_dec_beg(Integer value) {
		this.lnf033_dec_beg = value;
	}

	/**
	 * 取得借款繳保費之金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_insc_amt() {
		return this.lnf033_insc_amt;
	}

	/**
	 * 設定借款繳保費之金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_insc_amt(BigDecimal value) {
		this.lnf033_insc_amt = value;
	}

	/**
	 * 取得整合住宅方案核准編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_cpami_no() {
		return this.lnf033_cpami_no;
	}

	/**
	 * 設定整合住宅方案核准編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_cpami_no(String value) {
		this.lnf033_cpami_no = value;
	}

	/**
	 * 取得產品次分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_sub_class() {
		return this.lnf033_sub_class;
	}

	/**
	 * 設定產品次分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_sub_class(String value) {
		this.lnf033_sub_class = value;
	}

	/**
	 * 取得申請/核准編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf033_apply_no() {
		return this.lnf033_apply_no;
	}

	/**
	 * 設定申請/核准編號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_apply_no(String value) {
		this.lnf033_apply_no = value;
	}

	/** 取得類別轉換日 **/
	public Date getLnf033_trans_date() {
		return this.lnf033_trans_date;
	}

	/** 設定類別轉換日 **/
	public void setLnf033_trans_date(Date value) {
		this.lnf033_trans_date = value;
	}

	/** 取得停止補貼日 **/
	public Date getLnf033_stop_date() {
		return this.lnf033_stop_date;
	}

	/** 設定停止補貼日 **/
	public void setLnf033_stop_date(Date value) {
		this.lnf033_stop_date = value;
	}

	/**
	 * 取得每期固定多還本金金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_add_princpl() {
		return this.lnf033_add_princpl;
	}

	/**
	 * 設定每期固定多還本金金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_add_princpl(BigDecimal value) {
		this.lnf033_add_princpl = value;
	}

	/**
	 * 取得多還本金起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_add_begterm() {
		return this.lnf033_add_begterm;
	}

	/**
	 * 設定多還本金起期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_add_begterm(Integer value) {
		this.lnf033_add_begterm = value;
	}

	/**
	 * 取得多還本金迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf033_add_endterm() {
		return this.lnf033_add_endterm;
	}

	/**
	 * 設定多還本金迄期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_add_endterm(Integer value) {
		this.lnf033_add_endterm = value;
	}

	/**
	 * 取得暫收款
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf033_temp_amt() {
		return this.lnf033_temp_amt;
	}

	/**
	 * 設定暫收款
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf033_temp_amt(BigDecimal value) {
		this.lnf033_temp_amt = value;
	}

	/** 取得繳款通知單產生日 **/
	public Date getLnf033_notify_date() {
		return this.lnf033_notify_date;
	}

	/** 設定繳款通知單產生日 **/
	public void setLnf033_notify_date(Date value) {
		this.lnf033_notify_date = value;
	}

	/**
	 * 設定風險權數
	 * <p/>
	 */
	public void setLnf033_risk_rating(BigDecimal value) {
		this.lnf033_risk_rating = value;
	}

	/**
	 * 取得風險權數
	 * <p/>
	 */
	public BigDecimal getLnf033_risk_rating() {
		return this.lnf033_risk_rating;
	}

	/**
	 * 設定所有人統編
	 * <p/>
	 */
	public void setLnf033_owner_id(String value) {
		this.lnf033_owner_id = value;
	}

	/**
	 * 取得所有人統編
	 * <p/>
	 */
	public String getLnf033_owner_id() {
		return this.lnf033_owner_id;
	}

	/**
	 * 設定所有人名稱
	 * <p/>
	 */
	public void setLnf033_owner_name(String value) {
		this.lnf033_owner_name = value;
	}

	/**
	 * 取得所有人名稱
	 * <p/>
	 */
	public String getLnf033_owner_name() {
		return this.lnf033_owner_name;
	}

	/**
	 * 設定縣市首購核准編號
	 * <p/>
	 */
	public void setLnf033_kg_agree_no(String value) {
		this.lnf033_kg_agree_no = value;
	}

	/**
	 * 取得縣市首購核准編號
	 * <p/>
	 */
	public String getLnf033_kg_agree_no() {
		return this.lnf033_kg_agree_no;
	}

	/** 設定縣市首購核准日期 **/
	public void setLnf033_kg_agree_dt(Date value) {
		this.lnf033_kg_agree_dt = value;
	}

	/** 取得縣市首購核准日期 **/
	public Date getLnf033_kg_agree_dt() {
		return this.lnf033_kg_agree_dt;
	}

	/** 設定縣市首購終止補貼日 **/
	public void setLnf033_kg_end_date(Date value) {
		this.lnf033_kg_end_date = value;
	}

	/** 取得縣市首購終止補貼日 **/
	public Date getLnf033_kg_end_date() {
		return this.lnf033_kg_end_date;
	}

	/**
	 * 設定縣市終止補貼原因
	 * <p/>
	 */
	public void setLnf033_kg_end_code(String value) {
		this.lnf033_kg_end_code = value;
	}

	/**
	 * 取得縣市終止補貼原因
	 * <p/>
	 */
	public String getLnf033_kg_end_code() {
		return this.lnf033_kg_end_code;
	}

	/** 設定縣市首購補貼起日 **/
	public void setLnf033_kg_int_date(Date value) {
		this.lnf033_kg_int_date = value;
	}

	/** 取得縣市首購補貼起日 **/
	public Date getLnf033_kg_int_date() {
		return this.lnf033_kg_int_date;
	}

	/**
	 * 設定是否搭配房貸壽險
	 * <p/>
	 */
	public void setLnf033_rmbins_flag(String value) {
		this.lnf033_rmbins_flag = value;
	}

	/**
	 * 取得是否搭配房貸壽險
	 * <p/>
	 */
	public String getLnf033_rmbins_flag() {
		return this.lnf033_rmbins_flag;
	}

	/**
	 * 設定是否搭配房貸壽險利率優惠方案
	 * <p/>
	 */
	public void setLnf033_rmbint_flag(String value) {
		this.lnf033_rmbint_flag = value;
	}

	/**
	 * 取得是否搭配房貸壽險利率優惠方案
	 * <p/>
	 */
	public String getLnf033_rmbint_flag() {
		return this.lnf033_rmbint_flag;
	}

	/**
	 * 設定搭配房貸壽險利率優惠方案期數
	 * <p/>
	 */
	public void setLnf033_rmbint_term(Integer value) {
		this.lnf033_rmbint_term = value;
	}

	/**
	 * 取得搭配房貸壽險利率優惠方案期數
	 * <p/>
	 */
	public Integer getLnf033_rmbint_term() {
		return this.lnf033_rmbint_term;
	}

	/**
	 * 設定是否保費融資
	 * <p/>
	 */
	public void setLnf033_ins_flag(String value) {
		this.lnf033_ins_flag = value;
	}

	/**
	 * 取得是否保費融資
	 * <p/>
	 */
	public String getLnf033_ins_flag() {
		return this.lnf033_ins_flag;
	}

	/**
	 * 保費融資金額
	 * <p/>
	 */
	public void setLnf033_ins_loanbal(BigDecimal value) {
		this.lnf033_ins_loanbal = value;
	}

	/**
	 * 保費融資金額
	 * <p/>
	 */
	public BigDecimal getLnf033_ins_loanbal() {
		return this.lnf033_ins_loanbal;
	}

	/**
	 * 自動進帳金額
	 * <p/>
	 */
	public void setLnf033_adp_amt(BigDecimal value) {
		this.lnf033_adp_amt = value;
	}

	/**
	 * 自動進帳金額
	 * <p/>
	 */
	public BigDecimal getLnf033_adp_amt() {
		return this.lnf033_adp_amt;
	}

	/**
	 * 縣市首購縣市代碼
	 * Ａ台北市、Ｂ台中市、Ｃ基隆市、Ｄ台南市、Ｅ高雄市、Ｆ新北市、Ｇ宜蘭縣、Ｈ桃園縣、Ｉ嘉義市、Ｊ新竹縣<br/>
     * Ｋ苗栗縣、Ｌ台中縣、Ｍ南投縣、Ｎ彰化縣、Ｏ新竹市、Ｐ雲林縣、Ｑ嘉義縣、Ｒ台南縣、Ｓ高雄縣、Ｔ屏東縣<br/>
     * Ｕ花蓮縣、Ｖ台東縣、Ｗ金門縣、Ｘ澎湖縣、Ｙ陽明山、Ｚ連江縣、0無<br/>
	 * <p/>
	 */
	public void setLnf033_kg_area(String value) {
		this.lnf033_kg_area = value;
	}

	/**
	 * 縣市首購縣市代碼
	 * Ａ台北市、Ｂ台中市、Ｃ基隆市、Ｄ台南市、Ｅ高雄市、Ｆ新北市、Ｇ宜蘭縣、Ｈ桃園縣、Ｉ嘉義市、Ｊ新竹縣<br/>
     * Ｋ苗栗縣、Ｌ台中縣、Ｍ南投縣、Ｎ彰化縣、Ｏ新竹市、Ｐ雲林縣、Ｑ嘉義縣、Ｒ台南縣、Ｓ高雄縣、Ｔ屏東縣<br/>
     * Ｕ花蓮縣、Ｖ台東縣、Ｗ金門縣、Ｘ澎湖縣、Ｙ陽明山、Ｚ連江縣、0無<br/>
	 * <p/>
	 */
	public String getLnf033_kg_area() {
		return this.lnf033_kg_area;
	}

	/**
	 * 職工編號
	 */
	public void setLnf033_staffNo(String value) {
		this.lnf033_staffNo = value;
	}

	/**
	 * 職工編號
	 */
	public String getLnf033_staffNo() {
		return this.lnf033_staffNo;
	}

	/**
	 * 建物所有權是否已移轉他人
	 */
	public String getLnf033_own_trs_fg() {
		return lnf033_own_trs_fg;
	}
	/**
	 * 建物所有權是否已移轉他人
	 */
	public void setLnf033_own_trs_fg(String lnf033_own_trs_fg) {
		this.lnf033_own_trs_fg = lnf033_own_trs_fg;
	}

	/**
	 * 契約種類
	 */
	public String getLnf033_cntrNo_type() {
		return lnf033_cntrNo_type;
	}
	/**
	 * 契約種類
	 */
	public void setLnf033_cntrNo_type(String lnf033_cntrNo_type) {
		this.lnf033_cntrNo_type = lnf033_cntrNo_type;
	}

	/**
	 * 展延種類細項
	 */
	public String getLnf033_def_type_s() {
		return lnf033_def_type_s;
	}
	/**
	 * 展延種類細項
	 */
	public void setLnf033_def_type_s(String lnf033_def_type_s) {
		this.lnf033_def_type_s = lnf033_def_type_s;
	}

	/**
	 * 受災居民原債務展延之災害種類
	 */
	public String getLnf033_def_disastp() {
		return lnf033_def_disastp;
	}
	/**
	 * 受災居民原債務展延之災害種類
	 */
	public void setLnf033_def_disastp(String lnf033_def_disastp) {
		this.lnf033_def_disastp = lnf033_def_disastp;
	}

	/** 取得引介房仲收取回饋金  **/
	public String getLnf033_agnt_fbfg() {
		return lnf033_agnt_fbfg;
	}
	/** 設定引介房仲收取回饋金  **/
	public void setLnf033_agnt_fbfg(String lnf033_agnt_fbfg) {
		this.lnf033_agnt_fbfg = lnf033_agnt_fbfg;
	}

	/** 取得首次還款日 */
	public Date getLnf033_1st_rt_dt() {
		return lnf033_1st_rt_dt;
	}
	/** 設定首次還款日 */
	public void setLnf033_1st_rt_dt(Date lnf033_1st_rt_dt) {
		this.lnf033_1st_rt_dt = lnf033_1st_rt_dt;
	}	
}
