/* 
 * L182M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L182M01A;

/** 覆審預約單檔 **/
public interface L182M01ADao extends IGenericDao<L182M01A> {

	L182M01A findByOid(String oid);
	
	L182M01A findByMainId(String mainId);
	
	List<L182M01A> findByDocStatus(String docStatus);

	List<L182M01A> findByIndex01(String docStatus, Date genDate, Date baseDate, String branchList);
	
	List<L182M01A> findUnProcessedTypCd5(String docStatus);
}