---------------------------------------------------------
-- LMS.L140S02I 留學貸款檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02I;
CREATE TABLE LMS.L140S02I (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	STDCUSTID     VARCHAR(10)   not null,
	STDDUPNO      CHAR(1)       not null,
	STD<PERSON><PERSON>       VARCHAR(60)  ,
	STDICTYPE     CHAR(1)      ,
	EDUS<PERSON>L       VARCHAR(6)   ,
	EDUDEP        VARCHAR(6)   ,
	STDCOUNTRY    VARCHAR(2)   ,
	STDCOUOTH     VARCHAR(60)  ,
	FEDUSCHL      VARCHAR(120)  ,
	FEDUDEP       VARCHAR(6)   ,
	EDUKIND       CHAR(1)      ,
	STDMONEY      DECIMAL(13,0),
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S02I PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02I01;
CREATE UNIQUE INDEX LMS.XL140S02I01 ON LMS.L140S02I   (MAINID, SEQ, STDCUSTID, STDDUPNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02I IS '留學貸款檔';
COMMENT ON LMS.L140S02I (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	STDCUSTID     IS '留學生身分證字號', 
	STDDUPNO      IS '留學生身分證重覆序號', 
	STDNAME       IS '留學生姓名', 
	STDICTYPE     IS '家庭所得類別', 
	EDUSCHL       IS '國內最高學歷', 
	EDUDEP        IS '科系', 
	STDCOUNTRY    IS '出國地區', 
	STDCOUOTH     IS '出國地區-其他', 
	FEDUSCHL      IS '就讀學校名稱', 
	FEDUDEP       IS '就讀科系', 
	EDUKIND       IS '教育階段', 
	STDMONEY      IS '保費', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
