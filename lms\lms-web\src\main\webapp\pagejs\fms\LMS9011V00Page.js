var L901VAction = {
    fhandler: "lms9011m00formhandler",
    ghandler: 'lms9011gridhandler',
    $form: $("#lms9011tabForm"),
    gridId: "#gridview",
    /**
     *更新grid
     */
    _tiggerGrid: function(){
        $(L901VAction.gridId).trigger("reloadGrid");
    },
    /**
     *取得目前gird選擇的資料
     */
    _selectRowData: function(){
        var $grid = $(L901VAction.gridId);
        var rowId = $grid.getGridParam('selrow');
        if (!rowId) {
            CommonAPI.showMessage(i18n.lms9011v00["L901M01a.error1"]);
            return false;
        }
        return $grid.getRowData(rowId);
    },
    /**
     *開啟主要視窗
     */
    openDoc: function(cellvalue, options, rowObject){
        var $form = L901VAction.$form;
        var oid = (rowObject && rowObject.oid) || "";
        $form.reset();
        $.ajax({
            handler: L901VAction.fhandler,// "lms9011m00formhandler"
            action: "query",
            data: {
                mainOid: oid
            },
            success: function(obj){
                $form.injectData(obj);
                $("#lms9011new").thickbox({
                    title: i18n.lms9011v00['L901M01a.insertData2'],// '修改動用審核表稽核項目',
                    width: 700,
                    height: 280,
                    modal: true,
                    i18n: i18n.def,
                    buttons: {
                        "saveData": function(showMsg){
                            if (!$form.valid()) {
                                return false;
                            }
                            $.ajax({
                                handler: L901VAction.fhandler,// "lms9011m00formhandler"
                                action: "save",
                                data: {
                                    mainOid: oid
                                },
                                success: function(responseData){
                                    L901VAction._tiggerGrid();
                                }
                            });
                            $.thickbox.close();
                        },
                        "close": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    }
    
};

$(function(){
    var L901M01aGrid = $(L901VAction.gridId).iGrid({
        handler: L901VAction.ghandler,//'lms9011gridhandler',
        height: 350,
        rowNum: 15,
        sortname: "itemSeq",
        postData: {
            formAction: "queryL901m01a"
        },
        colModel: [{
            colHeader: "",
            name: 'oid',
            hidden: true
        }, {
            colHeader: i18n.lms9011v00["L901M01a.itemSeq"],
            align: "center",
            width: 15,
            sortable: true,
            name: 'itemSeq'
        }, {
            colHeader: i18n.lms9011v00["L901M01a.language"], //語系，20130416 Vector 隱藏之
//            align: "center",
//            width: 50,
//            sortable: true,
            hidden:true,
            name: 'locale'
        }, {
            colHeader: i18n.lms9011v00["L901M01a.itemContent"], //查核項目內容
            align: "left",
            width: 150,
            sortable: true,
            formatter: 'click',
            onclick: L901VAction.openDoc,
            name: 'itemContent'
        }, {
            colHeader: i18n.lms9011v00["L901M01a.itemMemo"], //備註
            align: "left",
            width: 50,
            sortable: true,
            name: 'itemMemo'
        }, {
            colHeader: i18n.lms9011v00["L901M01a.updateTime"], //異動日期
            align: "center",
            width: 100,
            sortable: true,
            name: 'updateTime'
        }],
        ondblClickRow: function(rowid){
            var data = $(L901VAction.gridId).getRowData(rowid);
            L901VAction.openDoc(null, null, data);
        }
    });
});

$("#buttonPanel").find("#btnDelete").click(function(){
    var data = L901VAction._selectRowData();
    if (data) {
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: L901VAction.fhandler,// "lms9011m00formhandler"
                    action: "deleteList",
                    data: {
                        oid: data.oid
                    },
                    success: function(res){
                        L901VAction._tiggerGrid();
                    }
                });
            }
        });
    }
}).end().find("#btnAdd").click(function(){
    L901VAction.openDoc();
}).end().find("#btnModify").click(function(rowObject, showMsg){
    var data = L901VAction._selectRowData();
    if (data) {
        L901VAction.openDoc(null, null, data);
    }
});


