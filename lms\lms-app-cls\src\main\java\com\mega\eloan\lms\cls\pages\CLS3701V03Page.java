
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.service.CLS3701Service;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 房仲引介來源 - 已覆核
 * </pre>
 */
@Controller
@RequestMapping("/cls/cls3701v03")
public class CLS3701V03Page extends AbstractEloanInnerView {

	@Autowired
    CLS3701Service cls3701Service;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.先行動用_已覆核);

		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕
		list.add(LmsButtonEnum.View);
		list.add(LmsButtonEnum.Filter);
		
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {

		}
		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			//btns.add(CreditButtonEnum.FCheck);
		}
		// 指定分行出現的按鈕
		String ownBrId = params.getString("_branch");
		if (cls3701Service.checkSpecialBank(ownBrId)) {
			list.add(LmsButtonEnum.ProduceExcel);
		}
		
		// 加上Button
		addToButtonPanel(model, list);

		renderJsI18N(CLS3701M01Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS3701V03Page');");
	}

}
