package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1220R12RptService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 中鋼團體消貸控制檔XLS
 */
@Service("cls1220r12rptservcie")
public class CLS1220R12RptServiceImpl implements FileDownloadService, CLS1220R12RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1220R12RptServiceImpl.class);

	@Resource
	BranchService branchService;

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Override
	public byte[] getContent(PageParameters params) throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateXls(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	private ByteArrayOutputStream generateXls(PageParameters params)
	throws IOException, Exception {
		String grpCntrNo = Util.trim(params.getString("grpCntrNo"));
		
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		genXls(outputStream, grpCntrNo);
		
		if (outputStream != null) {
			outputStream.flush();
		}
		return outputStream;
	}

	private void genXls(ByteArrayOutputStream outputStream, String grpCntrNo) throws IOException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();		
		HSSFWorkbook workbook = new HSSFWorkbook();
		
		if(true) {
			HSSFSheet sheet1 = workbook.createSheet("清單");
			
			HSSFFont headFont10 = workbook.createFont();
			{
				headFont10.setFontName("標楷體");
				headFont10.setFontHeightInPoints((short) 10);
			}
			
			HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
			{
				cellFormatL_10.setFont(headFont10);
				cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
				cellFormatL_10.setWrapText(true);
			}
			
			HSSFFont headFont12 = workbook.createFont();
			{
				headFont12.setFontName("標楷體");
				headFont12.setFontHeightInPoints((short) 12);
			}
			
			HSSFCellStyle cellFormatL = workbook.createCellStyle();
			{
				cellFormatL.setFont(headFont12);
				cellFormatL.setAlignment(HorizontalAlignment.LEFT);
				cellFormatL.setWrapText(true);
			}
			
			HSSFCellStyle cellFormatR = workbook.createCellStyle();
			{
				cellFormatR.setFont(headFont12);
				cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
				cellFormatR.setWrapText(true);
			}
			
			HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
			{
				cellFormatL_Border.setFont(headFont12);
				cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT);
				cellFormatL_Border.setWrapText(true);
				cellFormatL_Border.setBorderTop(BorderStyle.THIN);
				cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
				cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
				cellFormatL_Border.setBorderRight(BorderStyle.THIN);
			}
			
			HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
			{
				cellFormatR_Border.setFont(headFont12);
				cellFormatR_Border.setAlignment(HorizontalAlignment.RIGHT);
				cellFormatR_Border.setWrapText(true);
				cellFormatR_Border.setBorderTop(BorderStyle.THIN);
				cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
				cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
				cellFormatR_Border.setBorderRight(BorderStyle.THIN);
			}
			
			HSSFFont headFont14 = workbook.createFont();
			{
				headFont14.setFontName("標楷體");
				headFont14.setFontHeightInPoints((short) 14);
			}

			HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
			{
			cellFormatC_14.setFont(headFont14);
			cellFormatC_14.setAlignment(HorizontalAlignment.CENTER);
			cellFormatC_14.setWrapText(true);
			}
		
			// ======
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("團貸母戶編號", 25);
			headerMap.put("分行代碼", 12);
			headerMap.put("分行別", 15);
			headerMap.put("統編", 20);
			headerMap.put("姓名", 15);
			headerMap.put("英文姓名", 25);
			headerMap.put("生日", 20);
			headerMap.put("職工編號", 15);
			headerMap.put("手機", 20);
			headerMap.put("email", 30);
			headerMap.put("職稱", 15);
			headerMap.put("申貸金額", 15);
			headerMap.put("撥款方式", 15);
			headerMap.put("預先指定撥款銀行", 15);
			headerMap.put("存款帳號", 20);
			headerMap.put("支票領取地點", 15);
			headerMap.put("是否已於「線上貸款平台完成簽約對保」", 15);
			headerMap.put("完成對保時間", 30);
			headerMap.put("完成對保 IP", 20);
			headerMap.put("是否具有美國納稅義務人身分", 10);
			headerMap.put("是否具有中華民國以外之納稅義務人身分", 10);
			headerMap.put("利率變動通知方式", 25);
			headerMap.put("案件狀態", 15);
			headerMap.put("住宅電話_區碼", 15);
			headerMap.put("住宅電話", 20);
			headerMap.put("戶籍地址_郵遞區碼", 15);
			headerMap.put("戶籍地址", 30);
			headerMap.put("通訊地址_郵遞區碼", 15);
			headerMap.put("通訊地址", 30);
			headerMap.put("服務單位電話_區碼", 15);
			headerMap.put("服務單位電話_總機_分機", 20);
			headerMap.put("現職年薪", 15);
			headerMap.put("年資_單位年", 10);
			headerMap.put("建立時間", 30);

			int totalColSize = headerMap.size();

			List<String[]> rows = new ArrayList<String[]>();
			
			String brNo = user.getUnitNo();
			Map<String, String> cacheMap = new HashMap<String, String>();
			for (Map<String, Object> map_row : eloandbBASEService.findCLS1220R12(brNo, grpCntrNo)) {				
				// ---
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				arr[0] = Util.trim(MapUtils.getString(map_row, "GRPCNTRNO"));
				String ownBrId = Util.trim(MapUtils.getString(map_row, "OWNBRID"));
				arr[1] = ownBrId;
				arr[2] = getBrName(cacheMap, ownBrId);
				arr[3] = Util.trim(MapUtils.getString(map_row, "CUSTID"));
				arr[4] = Util.trim(MapUtils.getString(map_row, "CUSTNAME"));
				arr[5] = Util.trim(MapUtils.getString(map_row, "ENGNAME"));
				arr[6] = Util.trim(MapUtils.getString(map_row, "BIRTHDAY"));
				arr[7] = Util.trim(MapUtils.getString(map_row, "EMPNO"));
				arr[8] = Util.trim(MapUtils.getString(map_row, "MTEL"));
				arr[9] = Util.trim(MapUtils.getString(map_row, "EMAIL"));
				arr[10] = Util.trim(MapUtils.getString(map_row, "JOBPOSITION"));
				
				
				BigDecimal applyamt = Util.notEquals(Util.trim(map_row.get("APPLYAMT")), "") ? Util
						 .parseBigDecimal(Util.trim(map_row.get("APPLYAMT"))): BigDecimal.ZERO;
				arr[11] = applyamt.toPlainString();
				
				// 撥款方式 {C:支票, B:銀行或郵局}
				String appnWay = Util.trim(MapUtils.getString(map_row, "APPNWAY"));
				arr[12] = "C".equals(appnWay) ? "支票" : "B".equals(appnWay) ? "銀行或郵局" : "";
				
				arr[13] = Util.trim(MapUtils.getString(map_row, "APPNBANKCODE"));
				arr[14] = Util.trim(MapUtils.getString(map_row, "DPACCT"));
				arr[15] = Util.trim(MapUtils.getString(map_row, "CHECKPLACE"));
				
				// 是否已於「線上貸款平台完成簽約對保」Y/N
				String agree = Util.trim(MapUtils.getString(map_row, "AGREE"));
				arr[16] = "Y".equals(agree) ? "是" : "N".equals(agree) ? "否" : "";
				
				arr[17] = Util.trim(TWNDate.toFullAD((Timestamp)MapUtils.getObject(map_row, "AGREEQUERYEJTS")));
				arr[18] = Util.trim(MapUtils.getString(map_row, "AGREEQUERYEJIP"));
				
				// 是否具有美國納稅義務人身分   Y:是  N:否  空白:空白
				String needW8BEN = Util.trim(MapUtils.getString(map_row, "NEEDW8BEN"));
				arr[19] = "Y".equals(needW8BEN) ? "是" : "N".equals(needW8BEN) ? "否" : "";
				// 是否具有中華民國以外之納稅義務人身分    Y:是  N:否  空白:空白
				String needCRS = Util.trim(MapUtils.getString(map_row, "NEEDCRS"));
				arr[20] = "Y".equals(needCRS) ? "是" : "N".equals(needCRS) ? "否" : "";
				
				

				// 利率變動通知方式 ● 2 書面寄送(通訊地址) ● 3 電子郵件
				String rateAdjNotify = Util.trim(MapUtils.getString(map_row, "RATEADJNOTIFY"));
				arr[21] = "2".equals(rateAdjNotify) ? "書面寄送(通訊地址)" : "3".equals(rateAdjNotify) ? "電子郵件" : "";
				
				arr[22] = Util.trim(MapUtils.getString(map_row, "CODEDESC"));
				arr[23] = Util.trim(MapUtils.getString(map_row, "HOMEPHONECODE"));
				arr[24] = Util.trim(MapUtils.getString(map_row, "HOMEPHONE"));
				arr[25] = Util.trim(MapUtils.getString(map_row, "FADDRPOSTALCODE"));
				arr[26] = Util.trim(MapUtils.getString(map_row, "FADDR"));
				arr[27] = Util.trim(MapUtils.getString(map_row, "COADDRPOSTALCODE"));
				arr[28] = Util.trim(MapUtils.getString(map_row, "COADDR"));
				arr[29] = Util.trim(MapUtils.getString(map_row, "COMPANYPHONECODE"));
				arr[30] = Util.trim(MapUtils.getString(map_row, "COMPANYPHONE"));
				BigDecimal payAmt = Util.notEquals(Util.trim(map_row.get("PAYAMT")), "") ? Util
						 .parseBigDecimal(Util.trim(map_row.get("PAYAMT"))): BigDecimal.ZERO;
				arr[31] = payAmt.toPlainString();
				
				arr[32] = Util.trim(MapUtils.getString(map_row, "SNRY"));
				arr[33] = Util.trim(TWNDate.toFullAD((Timestamp)MapUtils.getObject(map_row, "CREATETIME")));
				// ---
				rows.add(arr);
			}

			int rowIdx = 0;
			// ==============================
			//標題列
			if(true){
				HSSFRow headerRow = sheet1.createRow(rowIdx);
				int colIdx = 0;
				for (String h : headerMap.keySet()) {
				    int colWidth = headerMap.get(h);
				    sheet1.setColumnWidth(colIdx, colWidth * 256);
				    LmsExcelUtil.addCell(headerRow, colIdx, h, cellFormatL_Border);
				    colIdx++;
				}
			}
			// ==============================
			//資料列
			if(true){
				rowIdx = 1;
				for (int i_row = 0; i_row < rows.size(); i_row++) {
				    HSSFRow row = sheet1.createRow(rowIdx + i_row);
				    String[] arr = rows.get(i_row);
				    for (int i_col = 0; i_col < totalColSize; i_col++) {
				        if (i_col == 11 || i_col == 31) {
				        	// 申貸金額、現職年薪
				            LmsExcelUtil.addCell(row, i_col, NumConverter.addComma(arr[i_col], "#,###,###,###,##0"), cellFormatR_Border);
				        } else {
				        	LmsExcelUtil.addCell(row, i_col, arr[i_col], cellFormatL_Border);
				        }
				    }
				}
			}
			
			//=====
			workbook.write(outputStream);
			workbook.close();
		}
		
	}
	
	private String getBrName(Map<String, String> cacheMap, String brNo){
		if(!cacheMap.containsKey(brNo)){
			IBranch obj = branchService.getBranch(brNo);
			if(obj!=null){
				cacheMap.put(brNo, Util.trim(obj.getBrName()));
			}
		}
		return Util.trim(cacheMap.get(brNo));
	}
	
}
