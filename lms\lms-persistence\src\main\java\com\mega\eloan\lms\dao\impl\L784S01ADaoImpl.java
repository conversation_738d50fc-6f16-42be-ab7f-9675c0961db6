/* 
 * L784S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L784S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;

import com.mega.eloan.lms.model.L784S01A;


/** 已敘做授信案件明細 **/
@Repository
public class L784S01ADaoImpl extends LMSJpaDao<L784S01A, String> implements
		L784S01ADao {

	@Override
	public L784S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L784S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("endDate");
		search.addOrderBy("custId");
		List<L784S01A> list = createQuery(L784S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L784S01A findByUniqueKey(String mainId, String brId, String custId, String dupNo,
			String cntrNo) {
		ISearch search = createSearchTemplete();
		search.setMaxResults(Integer.MAX_VALUE);
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "brId", brId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L784S01A> findByIndex01(String mainId, String custId,
			String dupNo, String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L784S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		// 檢查是否有查詢參數
		search.setMaxResults(Integer.MAX_VALUE);
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L784S01A.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L784S01A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L784S01A> list = createQuery(L784S01A.class, search)
				.getResultList();

		return list;
	}
	@Override
	public List<L784S01A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L784S01A> list = createQuery(L784S01A.class,search).getResultList();
		return list;
	}
}