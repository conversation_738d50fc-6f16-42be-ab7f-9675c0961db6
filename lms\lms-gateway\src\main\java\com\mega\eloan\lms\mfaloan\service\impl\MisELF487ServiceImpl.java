
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisELF487Service;

/**
 * <pre>
 * 覆審(帳號層)
 * </pre>
 * 
 * @since 2020/12/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/12/01,EL08034,new
 *          </ul>
 */
@Service
public class MisELF487ServiceImpl extends AbstractMFAloanJdbc implements
MisELF487Service {
	@Override
	public List<Map<String, Object>> sel_by_brNo_idDup(String brNo, String custId, String dupNo){
		return this.getJdbc().queryForListWithMax("ELF487.sel_by_brNo_idDup", new String[]{brNo+"%", custId, dupNo});
	}
	
	@Override
	public List<String> sel_never_retrialData_only_Rule1(String brNo, String custId, String dupNo){
		List<String> cntrNo_list = new ArrayList<String>();
		for(Map<String, Object> map : this.getJdbc().queryForListWithMax("ELF487.sel_never_retrialData_only_Rule1"
				, new String[]{brNo, custId, dupNo})){
			String cntrNo = Util.trim(MapUtils.getString(map, "ELF487_CONTRACT"));
			cntrNo_list.add(cntrNo);
		}
		
		return cntrNo_list;
	}
	
	@Override
	public List<String> sel_never_retrialData_match___HOUSE_or_RMBINS_or_HS_CRE_FG(String brNo, String custId, String dupNo){
		List<String> cntrNo_list = new ArrayList<String>();
		for(Map<String, Object> map : this.getJdbc().queryForListWithMax("ELF487.sel_never_retrialData_match___HOUSE_or_RMBINS_or_HS_CRE_FG"
				, new String[]{brNo, custId, dupNo})){
			String cntrNo = Util.trim(MapUtils.getString(map, "ELF487_CONTRACT"));
			cntrNo_list.add(cntrNo);
		}
		
		return cntrNo_list;
	}
}
