package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import javax.persistence.Query;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/** 授信簽報書主檔 **/
@Repository
public class L120M01ADaoImpl extends LMSJpaDao<L120M01A, String> implements
		L120M01ADao {

	@Override
	public L120M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L120M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01A> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120M01A> list = createQuery(L120M01A.class, search)
				.getResultList();
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findMaxCaseSeq() {
		String currYear = TWNDate.toAD(new Date()).substring(0, 4);
		Query query = getEntityManager().createNamedQuery(
				"L120M01A.getMaxCaseSeq");
		query.setParameter("caseYear", Util.parseInt(currYear)); // 設置參數

		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<L120M01A> findMaxCaseSeq2() {
		String currYear = TWNDate.toAD(new Date()).substring(0, 4);
		Query query = getEntityManager().createNamedQuery(
				"L120M01A.getMaxCaseSeq2");
		query.setParameter("caseYear", Util.parseInt(currYear)); // 設置參數

		return query.getResultList();
	}

	@Override
	public List<L120M01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		return find(search);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findMaxEndDateSeq() {
		Query query = entityManager.createNamedQuery("L120M01A.findByEndDate");
		query.setParameter("docKind", "2"); // 設置參數
		return query.getResultList();
	}

	public List<Object[]> findFullTextSearch(String fxUserId, String fxGroupId,
			String fxCaseDateName, String fxCaseDateS, String fxCaseDateE,
			String fxEndDateS, String fxEndDateE, String fxTypCd,
			String fxDocType, String fxDocKind, String fxDocCode,
			String fxUpdaterName, String fxUpdater, String fxCaseBrId,
			String fxCustId, String fxDocRslt, String fxDocStatus,
			String fxLnSubject, String fxRateText, String fxOtherCondition,
			String fxReportOther, String fxReportReason1,
			String fxReportReason2, String fxAreaOption, String fxCollateral,
			String fxCustName, String fxBusCode, String fxCaseLvl) {

		/*
		 * 
		 * http://www.dpriver.com/pp/sqlformat.htm
		 * 
		 * SELECT DISTINCT MAINID FROM ( SELECT DISTINCT MAINID FROM
		 * LMS.L140M01B WHERE itemtype = '3' AND itemdscr LIKE '%ＳＩＢＯＲ%' AND
		 * MAINID IN --擔保品 ( SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE
		 * itemtype = '4' AND itemdscr LIKE '%%' AND MAINID IN --其他敘做條件 ( SELECT
		 * DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '2' AND itemdscr
		 * LIKE '%ＳＩＢＯＲ%' AND MAINID IN --企金利(費)率 ( SELECT DISTINCT MAINID FROM
		 * LMS.L140M01A WHERE lnsubject LIKE '%%' AND MAINID IN --企金授信科子目 (
		 * SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C WHERE
		 * LMS.L120M01C.MAINID IN --串額度明細表 ( SELECT DISTINCT MAINID FROM
		 * LMS.L120M01D WHERE ITEMTYPE = '7' AND itemdscr LIKE '%%' AND MAINID
		 * IN --簽報書-營運中心說明及意見 ( SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE
		 * (ITEMTYPE = '4' OR ITEMTYPE = 'E') AND itemdscr LIKE '%%' AND MAINID
		 * IN --簽報書-綜合評估及敘作理由(併於主表) ( SELECT DISTINCT MAINID FROM LMS.L120M01D
		 * WHERE ITEMTYPE = '3' AND itemdscr LIKE '%%' AND MAINID IN --簽報書-其他 (
		 * SELECT DISTINCT MAINID FROM LMS.L120S01B WHERE BusCode = '123456' AND
		 * MAINID IN --行業對象別 ( SELECT DISTINCT MAINID FROM LMS.L120S01A WHERE
		 * LMS.L120S01A.custname LIKE '%%' AND MAINID IN --客戶名稱 (
		 * 
		 * SELECT DISTINCT MAINID FROM LMS.L120A01A WHERE AUTHUNIT = '918' AND
		 * MAINID IN --簽報書-其他 ( (SELECT DISTINCT LMS.L120M01A.MAINID FROM
		 * LMS.L120M01A WHERE LMS.L120M01A.NOTESUP IS NULL AND caseDate BETWEEN
		 * '2012-11-13' AND '2013-11-13' AND ( ( DOCSTATUS = '05O' OR DOCSTATUS
		 * = 'L3G' OR DOCSTATUS = 'L4G' ) ) ) )
		 * 
		 * ) ) ) ) ) ) ) ) ) ) UNION ALL SELECT DISTINCT MAINID FROM ( SELECT
		 * DISTINCT MAINID FROM LMS.L140S02A WHERE (rateDesc LIKE '%%' OR
		 * freeRateDesc LIKE '%%' ) AND MAINID IN --企金利(費)率 ( SELECT DISTINCT
		 * MAINID FROM LMS.l140S02a,lms.C900M01D WHERE lms.C900M01D.subjCode =
		 * LMS.L140S02A.subjCode AND lms.C900M01D.SUBJNM LIKE '%%' AND MAINID IN
		 * --個金授信科目 ( SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C
		 * WHERE LMS.L120M01C.MAINID IN --串額度明細表 ( SELECT DISTINCT MAINID FROM
		 * LMS.C120M01A WHERE LMS.C120M01A.CUSTPOS = 'C' AND
		 * LMS.C120M01A.custname LIKE '%%' AND MAINID IN --客戶名稱 ( SELECT
		 * DISTINCT MAINID FROM LMS.L120A01A WHERE AUTHUNIT = '918' AND MAINID
		 * IN --簽報書-其他 ( (SELECT DISTINCT LMS.L120M01A.MAINID FROM LMS.L120M01A
		 * WHERE LMS.L120M01A.NOTESUP IS NULL AND LMS.L120M01A.docType = '2' AND
		 * caseDate BETWEEN '2012-11-13' AND '2013-11-13' AND ( ( DOCSTATUS =
		 * '05O' OR DOCSTATUS = 'L3G' OR DOCSTATUS = 'L4G' ) ) ) ) ) ) ) ) ) )
		 */

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitType = user.getUnitType();

		// 企個金共用
		StringBuffer com120 = new StringBuffer();
		StringBuffer com120Condtion = new StringBuffer();
		StringBuffer comCondtion = new StringBuffer();
		StringBuffer comOth1 = new StringBuffer();
		StringBuffer comOth2 = new StringBuffer();
		StringBuffer comOth3 = new StringBuffer();
		StringBuffer comOth4 = new StringBuffer();
		StringBuffer comOth5 = new StringBuffer();
		StringBuffer comOth6 = new StringBuffer();
		StringBuffer comOth7 = new StringBuffer();
		StringBuffer comOth8 = new StringBuffer();
		StringBuffer comOth9 = new StringBuffer();
		StringBuffer comOth10 = new StringBuffer();
		StringBuffer comOth11 = new StringBuffer();

		int rightCount = 0;

		// 個金專用
		StringBuffer cls120 = new StringBuffer();
		StringBuffer clsCondtion = new StringBuffer();
		StringBuffer clsOth1 = new StringBuffer();
		StringBuffer clsOth2 = new StringBuffer();
		StringBuffer clsOth3 = new StringBuffer();
		StringBuffer clsOth4 = new StringBuffer();
		StringBuffer clsOth5 = new StringBuffer();
		StringBuffer clsOth6 = new StringBuffer();

		boolean hasClsDoc = false;
		if (Util.notEquals(fxDocType, "1")) {
			// 要包含個金案件
			hasClsDoc = true;
		}

		if (!(hasClsDoc == true && (!StringUtils.isBlank(fxCustId)
				|| !StringUtils.isBlank(fxCustName)
				|| !StringUtils.isBlank(fxLnSubject) || !StringUtils
				.isBlank(fxRateText)))) {
			// 要包含個金案件 且 有篩選條件有戶名、科目、利費率時才要加UNION ALL
			hasClsDoc = false;
		}

		// 判斷有沒有額度明細表條件
		boolean hasCntrDoc = false;
		if (!StringUtils.isBlank(fxLnSubject)
				|| !StringUtils.isBlank(fxRateText)
				|| !StringUtils.isBlank(fxOtherCondition)
				|| !StringUtils.isBlank(fxCollateral)) {
			hasCntrDoc = true;
		}

		// 企金核心條件************************************************************************
		if (!StringUtils.isBlank(fxDocStatus)) {
			com120Condtion.append(" AND ( " + fxDocStatus + " ) ");
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			com120Condtion.append(" AND " + fxCaseDateName
					+ " BETWEEN ?19  AND ?20 ");
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			com120Condtion.append(" AND enddate  BETWEEN ?21 AND ?22 ");
		}

		if (!StringUtils.isBlank(fxTypCd)) {
			com120Condtion.append(" AND typCd = ?1 ");
		}

		if (!StringUtils.isBlank(fxDocType)) {
			com120Condtion.append(" AND docType  = ?2  ");
		}

		if (!StringUtils.isBlank(fxDocKind)) {
			com120Condtion.append(" AND docKind  = ?3  ");
		}

		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		if (!StringUtils.isBlank(fxCaseLvl)) {
			com120Condtion.append(" AND caseLvl  = ?23  ");
		}

		if (!StringUtils.isBlank(fxDocCode)) {
			com120Condtion.append(" AND docCode  = ?4  ");
		}

		if (!StringUtils.isBlank(fxUpdaterName)
				&& !StringUtils.isBlank(fxUpdater)) {
			com120Condtion.append(" AND " + fxUpdaterName + "  = ?5  ");
		}

		if (!StringUtils.isBlank(fxCaseBrId)) {
			com120Condtion.append(" AND caseBrId = ?6  ");
		}

		// if (!StringUtils.isBlank(fxCustId)) {
		// com120Condtion.append(" AND custId   = ?7  ");
		// }

		if (!StringUtils.isBlank(fxDocRslt)) {
			com120Condtion.append(" AND docRslt   = ?8  ");
		}

		com120.append(" ( SELECT DISTINCT LMS.L120M01A.MAINID FROM LMS.L120M01A WHERE LMS.L120M01A.DELETEDTIME IS NULL AND LMS.L120M01A.NOTESUP IS NULL "
				+ com120Condtion.toString() + ")");

		rightCount = rightCount + 1;

		if (BranchTypeEnum.授管處.getCode().equals(unitType)
				|| BranchTypeEnum.國金部.getCode().equals(unitType)) {
			comOth1.append(" SELECT DISTINCT MAINID FROM LMS.L120A01A WHERE  AUTHUNIT != ?9 AND MAINID IN ("); // --授權檔
		} else {
			comOth1.append(" SELECT DISTINCT MAINID FROM LMS.L120A01A WHERE  AUTHUNIT = ?9 AND MAINID IN ("); // --授權檔
		}

		// 借款人****************************************************************
		if (!StringUtils.isBlank(fxCustName) || !StringUtils.isBlank(fxCustId)) {
			String findName = "";
			if (!StringUtils.isBlank(fxCustName)) {
				findName = " LMS.L120S01A.custname LIKE ?10 ";
			}
			String findCustId = "";
			if (!StringUtils.isBlank(fxCustId)) {
				if (Util.equals(findName, "")) {
					findCustId = " LMS.L120S01A.custid = ?7 ";
				} else {
					findCustId = " AND LMS.L120S01A.custid = ?7 ";
				}

			}
			rightCount = rightCount + 1;
			comOth2.append(" SELECT DISTINCT MAINID FROM LMS.L120S01A WHERE "
					+ findName + findCustId + "  AND MAINID IN ("); // --客戶名稱
		}

		if (!StringUtils.isBlank(fxBusCode)) {
			// 行業對象別T11會用到，所以若條件有戶名或行業對象別，則T10 SQL就要加上去
			rightCount = rightCount + 1;
			comOth3.append(" SELECT DISTINCT MAINID FROM LMS.L120S01B WHERE BusCode = ?11  AND MAINID IN ("); // --行業對象別
		}

		// 簽報書***************************************************************
		if (!StringUtils.isBlank(fxReportOther)) {
			rightCount = rightCount + 1;
			comOth4.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE ITEMTYPE = '3' AND itemdscr LIKE ?12 AND MAINID IN  ("); // --簽報書-其他
		}

		if (!StringUtils.isBlank(fxReportReason1)) {
			rightCount = rightCount + 1;
			comOth5.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE (ITEMTYPE = '4' OR ITEMTYPE = 'E') AND itemdscr LIKE ?13 AND MAINID IN ("); // --簽報書-綜合評估及敘作理由(併於主表)
		}

		if (!StringUtils.isBlank(fxAreaOption)) {
			rightCount = rightCount + 1;
			comOth6.append(" SELECT DISTINCT MAINID FROM LMS.L120M01D WHERE ITEMTYPE = '7' AND itemdscr LIKE ?14 AND MAINID IN  ("); // --簽報書-營運中心說明及意見
		}

		// 額度明細表***************************************************************

		if (hasCntrDoc == true) {

			rightCount = rightCount + 1;
			comOth7.append(" SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C WHERE LMS.L120M01C.MAINID IN  ("); // --串額度明細表

			if (!StringUtils.isBlank(fxLnSubject)) {
				// 企個金科目非共用同一檔案，故其中有一個符合就算
				rightCount = rightCount + 1;
				comOth8.append(" SELECT DISTINCT MAINID FROM LMS.L140M01A WHERE DELETEDTIME IS NULL AND lnsubject LIKE ?15 AND MAINID IN ("); // --企金授信科子目
			}

			if (!StringUtils.isBlank(fxRateText)) {
				// 企個金利率、費率非共用同一檔案，故其中有一個符合就算
				rightCount = rightCount + 1;
				comOth9.append(" SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '2' AND itemdscr LIKE ?16 AND MAINID IN  ("); // --企金利(費)率
			}

			if (!StringUtils.isBlank(fxOtherCondition)) {
				rightCount = rightCount + 1;
				comOth10.append(" SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '4' AND itemdscr LIKE ?17 AND MAINID IN 	("); // --其他敘做條件
			}

			if (!StringUtils.isBlank(fxCollateral)) {
				rightCount = rightCount + 1;
				comOth11.append(" SELECT DISTINCT MAINID FROM LMS.L140M01B WHERE itemtype = '3' AND itemdscr LIKE ?18 AND MAINID IN  ("); // --擔保品
			}

		}

		// 組合SQL 企個金共用
		comCondtion.append(comOth11).append(comOth10).append(comOth9)
				.append(comOth8).append(comOth7).append(comOth6)
				.append(comOth5).append(comOth4).append(comOth3)
				.append(comOth2).append(comOth1).append(com120);

		// 補左括號
		for (int i = 1; i <= rightCount; i++) {
			comCondtion.append(")");
		}

		// 個金專用條件 UNION ALL
		// ***********************************************************************

		rightCount = 0;
		if (hasClsDoc == true) {
			com120Condtion = new StringBuffer();
			// 個金核心條件************************************************************************
			if (!StringUtils.isBlank(fxDocStatus)) {
				com120Condtion.append(" AND ( " + fxDocStatus + " ) ");
			}

			if (!StringUtils.isBlank(fxCaseDateName)
					&& !StringUtils.isBlank(fxCaseDateS)
					&& !StringUtils.isBlank(fxCaseDateE)) {
				com120Condtion.append(" AND " + fxCaseDateName
						+ " BETWEEN ?519  AND ?520 ");
			}

			if (!StringUtils.isBlank(fxEndDateS)
					&& !StringUtils.isBlank(fxEndDateE)) {
				com120Condtion.append(" AND enddate  BETWEEN ?521 AND ?522 ");
			}

			if (!StringUtils.isBlank(fxTypCd)) {
				com120Condtion.append(" AND typCd = ?501 ");
			}

			if (!StringUtils.isBlank(fxDocType)) {
				com120Condtion.append(" AND docType  = ?502  ");
			}

			if (!StringUtils.isBlank(fxDocKind)) {
				com120Condtion.append(" AND docKind  = ?503  ");
			}

			// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
			if (!StringUtils.isBlank(fxCaseLvl)) {
				com120Condtion.append(" AND caseLvl  = ?523  ");
			}

			if (!StringUtils.isBlank(fxDocCode)) {
				com120Condtion.append(" AND docCode  = ?504  ");
			}

			if (!StringUtils.isBlank(fxUpdaterName)
					&& !StringUtils.isBlank(fxUpdater)) {
				com120Condtion.append(" AND " + fxUpdaterName + "  = ?505  ");
			}

			if (!StringUtils.isBlank(fxCaseBrId)) {
				com120Condtion.append(" AND caseBrId = ?506  ");
			}

			// if (!StringUtils.isBlank(fxCustId)) {
			// com120Condtion.append(" AND custId   = ?507  ");
			// }

			if (!StringUtils.isBlank(fxDocRslt)) {
				com120Condtion.append(" AND docRslt   = ?508  ");
			}

			// 核心條件-個金專用 個金加上DOCTYPE = '2' --Union ALL
			// 個金專用條件用***********************

			if (hasClsDoc == true) {
				com120Condtion.append(" AND docType  = '2'  ");
			}
			cls120.append(" ( SELECT DISTINCT LMS.L120M01A.MAINID FROM LMS.L120M01A WHERE LMS.L120M01A.DELETEDTIME IS NULL AND LMS.L120M01A.NOTESUP IS NULL "
					+ com120Condtion.toString() + ")");

			rightCount = rightCount + 1;

			if (BranchTypeEnum.授管處.getCode().equals(unitType)
					|| BranchTypeEnum.國金部.getCode().equals(unitType)) {
				clsOth1.append(" SELECT DISTINCT MAINID FROM LMS.L120A01A WHERE  AUTHUNIT != ?509 AND MAINID IN ("); // --授權檔
			} else {
				clsOth1.append(" SELECT DISTINCT MAINID FROM LMS.L120A01A WHERE  AUTHUNIT = ?509 AND MAINID IN ("); // --授權檔
			}

			// 個金借款人****************************************************************

			if (!StringUtils.isBlank(fxCustName)
					|| !StringUtils.isBlank(fxCustId)) {
				String findName = "";
				if (!StringUtils.isBlank(fxCustName)) {
					findName = " LMS.C120M01A.custname LIKE ?510 ";
				}
				String findCustId = "";
				if (!StringUtils.isBlank(fxCustId)) {
					if (Util.equals(findName, "")) {
						findCustId = " LMS.C120M01A.custid = ?507 ";
					} else {
						findCustId = " AND LMS.C120M01A.custid = ?507 ";
					}

				}
				rightCount = rightCount + 1;
				clsOth2.append(" SELECT DISTINCT MAINID FROM LMS.C120M01A WHERE (LMS.C120M01A.CUSTPOS IS NULL OR LMS.C120M01A.CUSTPOS = 'C'  ) AND "
						+ findName + findCustId + "  AND MAINID IN  ("); // --客戶名稱
			}

			// 個金額度明細表
			if (hasCntrDoc == true) {
				rightCount = rightCount + 1;
				clsOth3.append(" SELECT DISTINCT LMS.L120M01C.REFMAINID FROM LMS.L120M01C WHERE LMS.L120M01C.MAINID IN ("); // --串額度明細表

				if (!StringUtils.isBlank(fxLnSubject)) {
					// 企個金科目非共用同一檔案，故其中有一個符合就算
					rightCount = rightCount + 1;
					clsOth4.append(" SELECT DISTINCT MAINID FROM LMS.l140S02a,lms.C900M01D WHERE lms.C900M01D.subjCode = LMS.L140S02A.subjCode AND lms.C900M01D.SUBJNM LIKE ?515 AND MAINID IN ("); // --個金授信科目
				}

				if (!StringUtils.isBlank(fxRateText)) {
					// 企個金利率、費率非共用同一檔案，故其中有一個符合就算
					rightCount = rightCount + 1;
					clsOth5.append(" SELECT DISTINCT MAINID FROM LMS.L140S02A WHERE (rateDesc LIKE ?516 OR freeRateDesc LIKE ?5161 )  AND MAINID IN  ("); // --企金利(費)率
				}
			}

			clsOth6.append(" UNION ALL ");

			// 組合SQL 企個金共用
			clsCondtion.append(clsOth6).append(clsOth5).append(clsOth4)
					.append(clsOth3).append(clsOth2).append(clsOth1)
					.append(cls120);

			// 補左括號
			for (int i = 1; i <= rightCount; i++) {
				clsCondtion.append(")");
			}

		}

		// 最外層用MAINID串回L120M01A
		String finalSql = null;
		if (hasCntrDoc == true) {
			finalSql = "SELECT DISTINCT LMS.L120M01A.* FROM LMS.L120M01A,LMS.L120M01C WHERE LMS.L120M01A.DELETEDTIME IS NULL AND LMS.L120M01A.MAINID = LMS.L120M01C.MAINID AND LMS.L120M01C.REFMAINID IN ("
					+ comCondtion + " " + clsCondtion + ") WITH UR";
		} else {
			finalSql = "SELECT DISTINCT LMS.L120M01A.* FROM LMS.L120M01A WHERE LMS.L120M01A.DELETEDTIME IS NULL AND MAINID IN ("
					+ comCondtion + " " + clsCondtion + ") WITH UR";
		}

		// System.out.println("finalSql=" + finalSql);
		logger.info("finalSql=" + finalSql);

		String sql = finalSql;
		Query query = entityManager.createNativeQuery(sql); // 排除掉最後面的AND

		if (!StringUtils.isBlank(fxTypCd)) {
			query.setParameter(1, fxTypCd);
			if (hasClsDoc == true) {
				query.setParameter(501, fxTypCd);
			}
		}

		if (!StringUtils.isBlank(fxDocType)) {
			query.setParameter(2, fxDocType);
			if (hasClsDoc == true) {
				query.setParameter(502, fxDocType);
			}
		}

		if (!StringUtils.isBlank(fxDocKind)) {
			query.setParameter(3, fxDocKind);
			if (hasClsDoc == true) {
				query.setParameter(503, fxDocKind);
			}
		}

		if (!StringUtils.isBlank(fxDocCode)) {
			query.setParameter(4, fxDocCode);
			if (hasClsDoc == true) {
				query.setParameter(504, fxDocCode);
			}
		}

		if (!StringUtils.isBlank(fxUpdaterName)
				&& !StringUtils.isBlank(fxUpdater)) {
			query.setParameter(5, fxUpdater);
			if (hasClsDoc == true) {
				query.setParameter(505, fxUpdater);
			}
		}

		if (!StringUtils.isBlank(fxCaseBrId)) {
			query.setParameter(6, fxCaseBrId);
			if (hasClsDoc == true) {
				query.setParameter(506, fxCaseBrId);
			}
		}

		if (!StringUtils.isBlank(fxCustId)) {
			query.setParameter(7, fxCustId);
			if (hasClsDoc == true) {
				query.setParameter(507, fxCustId);
			}
		}

		if (!StringUtils.isBlank(fxDocRslt)) {
			query.setParameter(8, fxDocRslt);
			if (hasClsDoc == true) {
				query.setParameter(508, fxDocRslt);
			}
		}

		if (!StringUtils.isBlank(fxGroupId)) {
			if (BranchTypeEnum.授管處.getCode().equals(unitType)
					|| BranchTypeEnum.國金部.getCode().equals(unitType)) {
				query.setParameter(9, "");
				if (hasClsDoc == true) {
					query.setParameter(509, "");
				}
			} else {
				query.setParameter(9, fxGroupId);
				if (hasClsDoc == true) {
					query.setParameter(509, fxGroupId);
				}
			}
		}

		if (!StringUtils.isBlank(fxCustName)) {
			query.setParameter(10, fxCustName);
			if (hasClsDoc == true) {
				query.setParameter(510, fxCustName);
			}
		}

		if (!StringUtils.isBlank(fxBusCode)) {
			query.setParameter(11, fxBusCode);
		}

		if (!StringUtils.isBlank(fxReportOther)) {
			query.setParameter(12, fxReportOther);
		}

		if (!StringUtils.isBlank(fxReportReason1)) {
			query.setParameter(13, fxReportReason1);
		}

		if (!StringUtils.isBlank(fxAreaOption)) {
			query.setParameter(14, fxAreaOption);
		}

		if (!StringUtils.isBlank(fxLnSubject)) {
			query.setParameter(15, fxLnSubject);
			if (hasClsDoc == true) {
				query.setParameter(515, fxLnSubject);
			}
		}

		if (!StringUtils.isBlank(fxRateText)) {
			query.setParameter(16, fxRateText);
			if (hasClsDoc == true) {
				query.setParameter(516, fxRateText);
				query.setParameter(5161, fxRateText);
			}
		}

		if (!StringUtils.isBlank(fxOtherCondition)) {
			query.setParameter(17, fxOtherCondition);
			if (hasClsDoc == true) {
				query.setParameter(517, fxOtherCondition);
			}
		}

		if (!StringUtils.isBlank(fxCollateral)) {
			query.setParameter(18, fxCollateral);
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			query.setParameter(19, fxCaseDateS);
			if (hasClsDoc == true) {
				query.setParameter(519, fxCaseDateS);
			}
		}

		if (!StringUtils.isBlank(fxCaseDateName)
				&& !StringUtils.isBlank(fxCaseDateS)
				&& !StringUtils.isBlank(fxCaseDateE)) {
			query.setParameter(20, fxCaseDateE);
			if (hasClsDoc == true) {
				query.setParameter(520, fxCaseDateE);
			}
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			query.setParameter(21, fxEndDateS);
			if (hasClsDoc == true) {
				query.setParameter(521, fxEndDateS);
			}
		}

		if (!StringUtils.isBlank(fxEndDateS)
				&& !StringUtils.isBlank(fxEndDateE)) {
			query.setParameter(22, fxEndDateE);
			if (hasClsDoc == true) {
				query.setParameter(522, fxEndDateE);
			}
		}

		// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
		if (!StringUtils.isBlank(fxCaseLvl)) {
			query.setParameter(23, fxCaseLvl);
			if (hasClsDoc == true) {
				query.setParameter(523, fxCaseLvl);
			}
		}

		// query.setFirstResult(search.getFirstResult());
		// query.setMaxResults(search.getMaxResults());
		List<Object[]> resultList = query.getResultList();
		return resultList;

	}

	@Override
	public Page<L120M01A> findL120m01aListByL120m01atmp1UserIdForSearch(
			ISearch search, String userId) {

		if (userId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS,
					"l120m01atmp1.notesUp", userId);
		}

		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		// printSeqMap.put("endDate", false);
		// printSeqMap.put("custId", false);
		// printSeqMap.put("cntrNo", false);
		// search.setOrderBy(printSeqMap);
		return findPage(search);
	}

	@Override
	public L120M01A findBycaseYearBridSeq(Integer caseYear, String caseBrid,
			Integer caseSeq) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "caseYear", caseYear);
		search.addSearchModeParameters(SearchMode.EQUALS, "caseBrId", caseBrid);
		search.addSearchModeParameters(SearchMode.EQUALS, "caseSeq", caseSeq);
		return findUniqueOrNone(search);
	}

	/**
	 * 依照APPROVETIME 找期間核准的簽報書
	 * 
	 * @param docstatus
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	@Override
	public List<L120M01A> findByDocStatusAndApproveTime(String[] docstatus,
			String bgnDate, String endDate) {
		ISearch search = createSearchTemplete();
		String[] reasonStr = { bgnDate, endDate };
		search.addSearchModeParameters(SearchMode.BETWEEN, "approveTime",
				reasonStr);
		search.addSearchModeParameters(SearchMode.IN, "docStatus", docstatus);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		List<L120M01A> list = createQuery(L120M01A.class, search)
				.getResultList();
		return list;
	}

}