/* 
 * L140M01C_BF.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 額度授信科目資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01C_BF", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "loanTP" }))
public class L140M01C_BF extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 科目代碼
	 * <p/>
	 * LNSubject_A(_BF)<br/>
	 * LNSubject_B(_BF)<br/>
	 * LNSubject_C(_BF)<br/>
	 * LNSubject_D(_BF)<br/>
	 * LNSubject_E(_BF)<br/>
	 * LNSubject_F(_BF)<br/>
	 * LNSubject_G(_BF)<br/>
	 * LNSubject_H(_BF)<br/>
	 * LNSubject_I(_BF)<br/>
	 * LNSubject_J(_BF)<br/>
	 * LNSubject_K(_BF)<br/>
	 * LNSubject_L(_BF)<br/>
	 * LNSubject_M(_BF)<br/>
	 * LNSubject_N(_BF)
	 */
	@Size(max = 4)
	@Column(name = "LOANTP", length = 4, columnDefinition = "VARCHAR(4)")
	private String loanTP;

	/** 科目順序 **/
	@Size(max = 2)
	@Column(name = "SUBJSEQ", length = 2, columnDefinition = "VARCHAR(2)")
	private String subjSeq;

	/** 科目補充說明 **/
	@Size(max = 60)
	@Column(name = "SUBJDSCR", length = 60, columnDefinition = "VARCHAR(60)")
	private String subjDscr;

	/**
	 * 清償期限－天數
	 * <p/>
	 * 30、60、90、180、365<br/>
	 * 101/10/24調整，改為自行輸入<br/>
	 * DECIMAL(3,0)(DECIMAL(5,0)
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "LMTDAYS", columnDefinition = "DECIMAL(5,0)")
	private Integer lmtDays;

	/** 清償期限－詳其他敘作條件 **/
	@Size(max = 1)
	@Column(name = "LMTOTHER", length = 1, columnDefinition = "CHAR(01)")
	private String lmtOther;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 科目顯示順序
	 */
	@Column(name = "SEQNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer seqNum;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得科目代碼
	 * <p/>
	 * LNSubject_A(_BF)<br/>
	 * LNSubject_B(_BF)<br/>
	 * LNSubject_C(_BF)<br/>
	 * LNSubject_D(_BF)<br/>
	 * LNSubject_E(_BF)<br/>
	 * LNSubject_F(_BF)<br/>
	 * LNSubject_G(_BF)<br/>
	 * LNSubject_H(_BF)<br/>
	 * LNSubject_I(_BF)<br/>
	 * LNSubject_J(_BF)<br/>
	 * LNSubject_K(_BF)<br/>
	 * LNSubject_L(_BF)<br/>
	 * LNSubject_M(_BF)<br/>
	 * LNSubject_N(_BF)
	 */
	public String getLoanTP() {
		return this.loanTP;
	}

	/**
	 * 設定科目代碼
	 * <p/>
	 * LNSubject_A(_BF)<br/>
	 * LNSubject_B(_BF)<br/>
	 * LNSubject_C(_BF)<br/>
	 * LNSubject_D(_BF)<br/>
	 * LNSubject_E(_BF)<br/>
	 * LNSubject_F(_BF)<br/>
	 * LNSubject_G(_BF)<br/>
	 * LNSubject_H(_BF)<br/>
	 * LNSubject_I(_BF)<br/>
	 * LNSubject_J(_BF)<br/>
	 * LNSubject_K(_BF)<br/>
	 * LNSubject_L(_BF)<br/>
	 * LNSubject_M(_BF)<br/>
	 * LNSubject_N(_BF)
	 **/
	public void setLoanTP(String value) {
		this.loanTP = value;
	}

	/** 取得科目順序 **/
	public String getSubjSeq() {
		return this.subjSeq;
	}

	/** 設定科目順序 **/
	public void setSubjSeq(String value) {
		this.subjSeq = value;
	}

	/** 取得科目補充說明 **/
	public String getSubjDscr() {
		return this.subjDscr;
	}

	/** 設定科目補充說明 **/
	public void setSubjDscr(String value) {
		this.subjDscr = value;
	}

	/**
	 * 取得清償期限－天數
	 * <p/>
	 * 30、60、90、180、365<br/>
	 * 101/10/24調整，改為自行輸入<br/>
	 * DECIMAL(3,0)(DECIMAL(5,0)
	 */
	public Integer getLmtDays() {
		return this.lmtDays;
	}

	/**
	 * 設定清償期限－天數
	 * <p/>
	 * 30、60、90、180、365<br/>
	 * 101/10/24調整，改為自行輸入<br/>
	 * DECIMAL(3,0)(DECIMAL(5,0)
	 **/
	public void setLmtDays(Integer value) {
		this.lmtDays = value;
	}

	/** 取得清償期限－詳其他敘作條件 **/
	public String getLmtOther() {
		return this.lmtOther;
	}

	/** 設定清償期限－詳其他敘作條件 **/
	public void setLmtOther(String value) {
		this.lmtOther = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 設定科目顯示順序 **/
	public void setSeqNum(Integer seqNum) {
		this.seqNum = seqNum;
	}

	/** 取得科目顯示順序 **/
	public Integer getSeqNum() {
		return seqNum;
	}
}
