package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140M04B model.
 * </pre>
 * 
 * @since 2011/10/4
 * <AUTHOR> <PERSON>
 * @version <ul>
 *          <li>2011/10/4,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140M04B-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name="C140M04B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140M04B extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;

	@Column(name="COMP_KIND", length=30)
	private String compKind;

	@Column(name="EDIT_MODE", length=1)
	private String editMode;

	@Column(name="INPUT_TS", length=1)
	private String inputTs;

	@Column(name="IS_GRP_COMP", length=1)
	private String isGrpComp;

	@Column(nullable=false, length=32)
	private String uid;

	@Column(name="PRO_CP", length=1)
	private String proCp;

	@Column(name="PROBUSS_ID", length=10)
	private String probussId;

	@Column(length = 1)
	private String probussDupNo;

	@Column(name = "PROBUSS_NAME", length = 120)
	private String probussName;

	@Column(length=1)
	private String tmpch1;

	@Column(length=1)
	private String tmpch2;

	@Column(length = 1)
	private String proCountry;
	
	@Column(length = 10)
	private String probussJcicTaxNO;
	
	@Column(length = 1)
	private String LoanMode14120;
	
	//bi-directional many-to-one association to C140JSON
	@ToStringExclude
	@OneToMany(mappedBy = "c140m04b", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private List<C140JSON> c140jsons;
	
//	// bi-directional many-to-one association to C140S07B
//	@OneToMany(mappedBy = "c140m04b",cascade=CascadeType.PERSIST,fetch=FetchType.LAZY)
//	private List<C140S07B> c140s07bs;
	
	// bi-directional many-to-one association to C140M01A
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
        @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	public String getCompKind() {
		return this.compKind;
	}

	public void setCompKind(String compKind) {
		this.compKind = compKind;
	}

	public String getEditMode() {
		return this.editMode;
	}

	public void setEditMode(String editMode) {
		this.editMode = editMode;
	}

	public String getInputTs() {
		return this.inputTs;
	}

	public void setInputTs(String inputTs) {
		this.inputTs = inputTs;
	}

	public String getIsGrpComp() {
		return this.isGrpComp;
	}

	public void setIsGrpComp(String isGrpComp) {
		this.isGrpComp = isGrpComp;
	}

	public String getUid() {
		return this.uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public String getProCp() {
		return this.proCp;
	}

	public void setProCp(String proCp) {
		this.proCp = proCp;
	}

	public String getProbussId() {
		return this.probussId;
	}

	public void setProbussId(String probussId) {
		this.probussId = probussId;
	}

	public String getProbussDupNo() {
		return probussDupNo;
	}

	public void setProbussDupNo(String probussDupNo) {
		this.probussDupNo = probussDupNo;
	}

	
	public String getProbussName() {
		return this.probussName;
	}

	public void setProbussName(String probussName) {
		this.probussName = probussName;
	}

	public String getTmpch1() {
		return this.tmpch1;
	}

	public void setTmpch1(String tmpch1) {
		this.tmpch1 = tmpch1;
	}

	public String getTmpch2() {
		return this.tmpch2;
	}

	public void setTmpch2(String tmpch2) {
		this.tmpch2 = tmpch2;
	}

	public List<C140JSON> getC140jsons() {
		return this.c140jsons;
	}

	public void setC140jsons(List<C140JSON> c140jsons) {
		this.c140jsons = c140jsons;
	}
	
//	public List<C140S07B> getC140s07bs() {
//		return this.c140s07bs;
//	}

//	public void setC140s07bs(List<C140S07B> c140s07bs) {
//		this.c140s07bs = c140s07bs;
//	}
	
	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}

	public String getProCountry() {
		return proCountry;
	}

	public void setProCountry(String proCountry) {
		this.proCountry = proCountry;
	}

	public String getProbussJcicTaxNO() {
		return probussJcicTaxNO;
	}

	public void setProbussJcicTaxNO(String probussJcicTaxNO) {
		this.probussJcicTaxNO = probussJcicTaxNO;
	}

	public String getLoanMode14120() {
		return LoanMode14120;
	}

	public void setLoanMode14120(String loanMode14120) {
		LoanMode14120 = loanMode14120;
	}
	
}