package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;

/**
 * <pre>
 * 非房貸評分調整表
 * </pre>
 * 
 * @since 2013/09/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/7,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131s06")
public class CLS1131S06Page extends AbstractOutputPage {

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {

		setNeedHtml(true); // need html

		setJavascript(new String[] { "pagejs/cls/CLS1131S06Page.js" });

		return "&nbsp;";
	}

	// UPGRADE: 待確認是否需要ViewName
	@Override
	protected String getViewName() {
		return null;
	}

}
