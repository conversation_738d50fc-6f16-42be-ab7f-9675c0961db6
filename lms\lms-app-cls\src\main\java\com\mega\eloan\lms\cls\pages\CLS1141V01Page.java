/* 
 *CLS1141V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金 授信簽報書 -編製中
 * </pre>
 * 
 * @since 2012/9/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/9/27,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1141v01")
public class CLS1141V01Page extends AbstractEloanInnerView {

	@Autowired
	CLSService clsService;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕
		list.add(LmsButtonEnum.View);
		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			list.add(LmsButtonEnum.Add);
			list.add(LmsButtonEnum.Modify);
			list.add(LmsButtonEnum.Delete);
			list.add(LmsButtonEnum.ChangeCaseFormat);
		}
		list.add(LmsButtonEnum.Filter);
		addToButtonPanel(model, list);
		
		if(true){
			boolean active_brmp = false;
			
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String ssoUnitNo = user.getSsoUnitNo();
			String unitNo = user.getUnitNo();
			if(Util.equals("900", ssoUnitNo) || Util.equals("943", ssoUnitNo)){
				active_brmp = true;
			}else{
				if(Util.equals("229", unitNo) && clsService.is_function_on_codetype("cls_brmp_pilot")){
					active_brmp = true;
				}
				Map<String, String> map= clsService.get_codeType_codeDesc2WithOrder("LMS_FUNC_ON_FLAG","zh_TW");
				if(clsService.is_function_on_codetype("cls_brmp_pilot")){
					if (map.containsKey("cls_brmp_pilot")) {
						String val = Util.trim(map.get("cls_brmp_pilot"));
						if(val.contains(unitNo)){
							active_brmp = true;
						}
					}
				}
				if(clsService.is_function_on_codetype("cls_brmp_all")){
					active_brmp = true;
				}
			}
			model.addAttribute("_div_brmp", active_brmp);
		}
		
		renderJsI18N(CLS1141V01Page.class);
		renderJsI18N(LMSCommomPage.class);

		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);

		//J-111-0227
		if( clsService.is_function_on_codetype("CtrTypeA_V202209")){
			model.addAttribute("trPurpose202209", clsService.is_function_on_codetype("CtrTypeA_V202209"));
			model.addAttribute("trPurpose", false);
		} else if (clsService.is_function_on_codetype("CtrTypeA_V202206")) {
			model.addAttribute("trPurpose202209", false);
			model.addAttribute("trPurpose", clsService.is_function_on_codetype("CtrTypeA_V202206"));
		}

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1141V01Page');");
	}

}
