/* 
 * LNF195.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;


/** 歡喜理財家房貸關聯檔 **/
public class LNF195 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 
	 * 分行別<p/>
	 * NOT NULL
	 */
	@Column(name="LNF195_BR_NO", length=3, columnDefinition="CHAR(03)",unique = true)
	private String lnf195_br_no;

	/** 
	 * 不循環額度序號<p/>
	 * NOT NULL
	 */
	@Column(name="LNF195_CONTRACT_N", length=12, columnDefinition="CHAR(12)",unique = true)
	private String lnf195_contract_n;

	/** 
	 * 循環額度序號<p/>
	 * NOT NULL
	 */
	@Column(name="LNF195_CONTRACT_Y", length=12, columnDefinition="CHAR(12)")
	private String lnf195_contract_y;

	/** 
	 * E-LOAN 狀態<p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Column(name="LNF195_ELN_STATUS", length=14, columnDefinition="CHAR(14)")
	private String lnf195_eln_status;

	/** 
	 * A-LOAN 狀態<p/>
	 * NOT NULL WITH DEFAULT
	 */
	@Column(name="LNF195_ALN_STATUS", length=11, columnDefinition="CHAR(11)")
	private String lnf195_aln_status;

	/** 
	 * 資料維護日期<p/>
	 * NOT NULL
	 */
	@Column(name="LNF195_TIMESTAMP", columnDefinition="TIMESTAMP")
	private Date lnf195_timestamp;

	/** 
	 * 取得分行別<p/>
	 * NOT NULL
	 */
	public String getLnf195_br_no() {
		return this.lnf195_br_no;
	}
	/**
	 *  設定分行別<p/>
	 *  NOT NULL
	 **/
	public void setLnf195_br_no(String value) {
		this.lnf195_br_no = value;
	}

	/** 
	 * 取得不循環額度序號<p/>
	 * NOT NULL
	 */
	public String getLnf195_contract_n() {
		return this.lnf195_contract_n;
	}
	/**
	 *  設定不循環額度序號<p/>
	 *  NOT NULL
	 **/
	public void setLnf195_contract_n(String value) {
		this.lnf195_contract_n = value;
	}

	/** 
	 * 取得循環額度序號<p/>
	 * NOT NULL
	 */
	public String getLnf195_contract_y() {
		return this.lnf195_contract_y;
	}
	/**
	 *  設定循環額度序號<p/>
	 *  NOT NULL
	 **/
	public void setLnf195_contract_y(String value) {
		this.lnf195_contract_y = value;
	}

	/** 
	 * 取得E-LOAN 狀態<p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf195_eln_status() {
		return this.lnf195_eln_status;
	}
	/**
	 *  設定E-LOAN 狀態<p/>
	 *  NOT NULL WITH DEFAULT
	 **/
	public void setLnf195_eln_status(String value) {
		this.lnf195_eln_status = value;
	}

	/** 
	 * 取得A-LOAN 狀態<p/>
	 * NOT NULL WITH DEFAULT
	 */
	public String getLnf195_aln_status() {
		return this.lnf195_aln_status;
	}
	/**
	 *  設定A-LOAN 狀態<p/>
	 *  NOT NULL WITH DEFAULT
	 **/
	public void setLnf195_aln_status(String value) {
		this.lnf195_aln_status = value;
	}

	/** 
	 * 取得資料維護日期<p/>
	 * NOT NULL
	 */
	public Date getLnf195_timestamp() {
		return this.lnf195_timestamp;
	}
	/**
	 *  設定資料維護日期<p/>
	 *  NOT NULL
	 **/
	public void setLnf195_timestamp(Date value) {
		this.lnf195_timestamp = value;
	}
}
