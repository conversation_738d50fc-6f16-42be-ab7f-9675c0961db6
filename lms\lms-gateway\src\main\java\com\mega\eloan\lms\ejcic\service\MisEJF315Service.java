/* 
 * MisEJF315Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * MIS.EJF315 -> MIS.DATADATE
 * </pre>
 * 
 * @since 2011/11/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/11,CP,new
 *          </ul>
 */
public interface MisEJF315Service {
	/**
	 * <li>0-BANID-所屬公司統編-VC(11)- <li>1-ID-查詢對象-VC(11)-IDN <li>
	 * 2-PRODID-查詢產品別-VC(02)-P1~P7 <li>3-ITEMNAME-查詢項目-VC(06)- <li>
	 * 4-DATADATE-最近更新日-VC(09)-YYY/MM/DD <li>5-QDATE-查詢日期-VC(09)-YYY/MM/DD <li>
	 * 6-QEMPCODE-查詢員工代碼-VC(11)- <li>7-QEMPNAME-查詢員工名稱-VC(12)- <li>
	 * 8-QBRANCH-所屬分行代碼-VC(03)-
	 */

	final String[] LnunidCols = { "BANID", "ID", "PRODID", "ITEMNAME",
			"DATADATE", "QDATE", "QEMPCODE", "QEMPNAME", "QBRANCH" };

	/**
	 * 依指定 custId 取得 "金融聯合徵信中心專線查詢系統" 紀錄
	 * 
	 * @param custId
	 *            String 10碼
	 * @return Map<String, Object>
	 */
	Map<String, Object> getQueryEJDataDate(String custId);

	/**
	 * 依指定 custId 取得 "金融聯合徵信中心專線查詢系統" 紀錄
	 * 
	 * @param custId
	 *            String 10碼
	 * @return Map<String, Object>
	 */
	Map<String, Object> getQueryEJDataDateP2(String custId);

	/**
	 * 依指定 custId 取得 "金融聯合徵信中心專線查詢系統" 紀錄
	 * 
	 * @param custId
	 *            String 10碼
	 * @return Map<String, Object>
	 */
	Map<String, Object> getQueryEJDataDateP3P4(String custId);

	/**
	 * 依指定 custId 取得 "金融聯合徵信中心專線查詢系統" PRODID = 'P1', ITEMNAME = 'BAI001'紀錄
	 * 
	 * @param custId
	 *            String 10碼
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findByIdP1Bai001(String custId);

	/**
	 * 輸入統編，取得各產品別聯徵資料日期
	 * 
	 * @param custIds
	 *            輸入統編，取得各產品別聯徵資料日期
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findByIds(String[] custIds);

	/**
	 * 傳入統編或身份證字號、產品代碼、產品名稱。 <br/>
	 * 若傳入的產品代碼為空白，則回傳以日期降冪排列之第一筆日期(即最近日期)。 <br/>
	 * 若傳入的產品代碼不為空白，則回傳該產品代碼以日期降冪排序的第一筆資料。
	 * 
	 * @param custId
	 *            輸入統編
	 * @param prdIds
	 *            產品代碼
	 * @param itemNms
	 *            查詢項目
	 * @return List
	 */
	List<Map<String, Object>> findByIdAndPIdAndItNm(String custId,
			String[] prdIds, String[] itemNms);

	/**
	 * 取得最新資料日期
	 * 
	 * @param custId
	 *            輸入統編
	 * @param prdIds
	 *            產品代碼
	 * @param itemNms
	 *            查詢項目
	 * @return 最新資料日期(yyy/MM/dd)
	 */
	String getMaxDateByIdAndPIdAndItNm(String custId, String[] prdIds,
			String[] itemNms);
	
	/**
	 * 取得最新查詢日期
	 * 
	 * @param custId
	 *            輸入統編
	 * @param prdIds
	 *            產品代碼
	 * @param itemNms
	 *            查詢項目
	 * @return 最新查詢日期(yyy/MM/dd)
	 */
	String getMaxQDateByIdAndPIdAndItNm(String custId, String[] prdIds,
			String[] itemNms);
	
	/**
	 * 查詢不包含所傳入prodIds之qDate & prodId資訊
	 * 傳入統編或身份證字號、產品代碼、產品名稱。 <br/>
	 * 若傳入的產品代碼為空白，則回傳以日期降冪排列之第一筆日期(即最近日期)。 <br/>
	 * 若傳入的產品代碼不為空白，則回傳"不等於"該產品代碼以日期降冪排序的第一筆資料。
	 * 
	 * @param custId
	 *            輸入統編
	 * @param prdIds
	 *            產品代碼
	 * @param itemNms
	 *            查詢項目
	 * @return List
	 */
	List<Map<String, Object>> findByIdAndNotInPIdAndItNm(String custId, String[] prdIds, String[] itemNms);	
}
