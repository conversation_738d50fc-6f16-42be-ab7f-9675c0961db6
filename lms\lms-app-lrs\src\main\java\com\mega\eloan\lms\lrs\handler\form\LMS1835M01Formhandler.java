/* 
 * LMS1835M01Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.form;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.eloandb.service.Dw_elf411ovsService;
import com.mega.eloan.lms.lrs.service.LMS1835Service;
import com.mega.eloan.lms.mfaloan.service.MisELF411Service;

/**
 * <pre>
 * 產生企金戶新增 / 增額名單
 * </pre>
 * 
 * @since 2011/11/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/17,jessica,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1835m01formhandler")
public class LMS1835M01Formhandler extends AbstractFormHandler {

	@Resource
	Dw_elf411ovsService dwElf411ovsService;

	@Resource
	LMS1835Service service1835;

	@Resource
	DocCheckService docCheckService;

	@Autowired
	DocFileService fileService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	RetrialService retrialService;
	
	@Resource
	MisELF411Service misELF411Service;
	
	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult delete(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] mainIdList = params.getStringArray("mainId");
		service1835.delete(mainIdList);
		// EFD0019=INFO|刪除成功|
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage("EFD0019"));
		return result;
	}
	
	/**
	 * <pre>
	 * 產生 EXCEL
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws Exception 
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult transportExcel(PageParameters params)
			throws Exception {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String dataDate = params.getString("dataDate");
		String brNo = params.getString("brNo");
		
		boolean overSea = retrialService.overSeaProgram(); 
		// 搜尋的日期
		Date date = null;
		// 預設值為主機資料庫中最新月份之資料
		if (dataDate.isEmpty()) {
			date = LMSUtil.getExMonthDay(0, true, true);
			
			if(overSea){
				// 找尋411OVS中最大資料日期 (CYC_MN)(YYYY-mm-01)
				Map<String,Object> dataMap = dwElf411ovsService.findELF411ForMaxDate();
				if (dataMap != null) {
					// 找到最新月份
					date = (Date) dataMap.get("DATADATE");
				}	
			}else{
				date = CrsUtil.elf490YM_to_adDate_d( misELF411Service.getMaxDataYM());
			}			
		} else {
			String dateStr = dataDate + "-01";
			date = Util.parseDate(dateStr);
		}

		String listName = UtilConstants.NEWADD.新作
				+ UtilConstants.NEWADD.增額 + UtilConstants.NEWADD.逾放轉正;
		// 重產則會刪除該分行所有資料
//		service1835.delete(mainId);
		boolean addResult = service1835.transportExcel(overSea, brNo, date,listName);
		if(addResult){
			// EFD0018=INFO|執行成功|
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage("EFD0018"));
		}else{
			
		}
		return result;
	}

}
