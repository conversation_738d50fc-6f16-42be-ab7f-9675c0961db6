package com.mega.eloan.lms.cls.handler.grid;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 線上申貸原始資料
 * </pre>
 * 
 * @since 2015/04/28
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/04/28,EL03738,new
 * 
 *          </ul>
 */
@Scope("request")
@Controller("cls3502gridhandler")
public class CLS3502GridHandler extends AbstractGridHandler {

	@Resource
	BranchService branchService;

	@Resource
	CLS1220Service service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	RelatedAccountService relatedAccountService;

	@Resource
	RetrialService retrialService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	CodeTypeService codeTypeService;

}