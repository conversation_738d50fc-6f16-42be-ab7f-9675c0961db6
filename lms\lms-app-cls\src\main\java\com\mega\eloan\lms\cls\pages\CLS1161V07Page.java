package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 整批自動開戶
 * </pre>
 * 
 * @since 2017/03/05
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/03/05,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1161v07")
public class CLS1161V07Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.View,
				LmsButtonEnum.Create);
		// build i18n
		renderJsI18N(CLS1161M03Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1161V05Page');");
	}

}
