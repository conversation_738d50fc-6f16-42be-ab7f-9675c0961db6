/**
 * 異常通報表共用元件js
 */
var lmsM02Json;
if (!lmsM02Json) {
	lmsM02Json = {
		docType : responseJSON.docType,
		docCode : responseJSON.docCode,
		docKind : responseJSON.docKind,
		mainId : responseJSON.mainId,
		docStatus : responseJSON.mainDocStatus,
		areaDocstatus : responseJSON.areaDocstatus,
		unitNo : userInfo.unitNo,
		page : responseJSON.page,
		typCd : responseJSON.typCd,
		/**
		 *  更新異常通報Grid
		 */	
		rUnNormalGrid : function(){
			$("#unNormalGrid").jqGrid("setGridParam", {
				postData : {
					formAction : "queryL130S01a",
					mainId : responseJSON.mainId,
					rowNum : 10
				},
				search : true
			}).trigger("reloadGrid");		
		},
		/**
		 *  異常通報Grid
		 */	
		unNormalGrid : function(){
			var gridSelCes1 = $("#unNormalGrid").iGrid({
				handler : 'lms1201gridhandler',
				height : 175,
				sortname : 'seqKind|seqNo|seqShow',
				sortorder: 'asc|asc|asc',
				postData : {
					formAction : "queryL130S01a",
					mainId : responseJSON.mainId
				},
				caption: "&nbsp;",
				hiddengrid : false,
				//rownumbers : true,
				//rowNum : 10,
				multiselect: true,
				hideMultiselect:false,
				shrinkToFit: false,
				colModel : [ {
					colHeader : i18n.lmscommom["other.msg84"], //other.msg84=項目
					align : "left",
					width : 50, // 設定寬度
					sortable : true, // 是否允許排序
					name : 'seqNo' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg85"], //other.msg85=項目名稱
					align : "left",
					width : 300, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'seqName' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg86"], //other.msg86=擬/已辦
					align : "left",
					width : 100, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'seqKind' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg87"], //other.msg87=營運中心核定
					align : "left",
					width : 50, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'areaDecide' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg88"], //other.msg88=授管處核定
					align : "left",
					width : 50, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'headDecide' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg89"], //other.msg89=處理日期
					align : "left",
					width : 100, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'runDate' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg90"], //other.msg90=幣別
					align : "left",
					width : 50, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'setCurr' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg91"], //other.msg91=金額（仟元）
					align : "left",
					width : 100, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'setAmt' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg92"], //other.msg92=停權
					align : "left",
					width : 50, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'headMonth' // col.id
				}, {
					colHeader : i18n.lmscommom["other.msg93"], //other.msg93=說明
					align : "left",
					width : 100, // 設定寬度
					sortable : true, // 是否允許排序
					// formatter : 'click',
					// onclick : function,
					name : 'docDscr' // col.id
				}, {
					colHeader : "seqShow",
					name : 'seqShow',
					hidden : true
				}, {
					colHeader : "bigKind",
					name : 'bigKind',
					hidden : true
				}, {
					colHeader : "mainId",
					name : 'mainId',
					hidden : true
				}, {
					colHeader : "oid",
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
					var data = gridSelCes1.getRowData(rowid);
					lmsM02Json.thickGridContent(data);
				}
			});		
		},
		/**
		 *  引進集團代號與名稱
		 */		
		getGrp : function(){
			var custId="";
			var dupNo="";
			if(responseJSON.typCd == "5"){
				custId =$("#showBorrowData").find("#custId").html();
				dupNo =$("#showBorrowData").find("#dupNo").html();
			}else{
			    custId = (responseJSON.docType == "1")? $("#showBorrowData").find("#custId").html(): $("#mainCustId").val();
			    dupNo = (responseJSON.docType == "1")? $("#showBorrowData").find("#dupNo").html() : $("#mainDupNo").val();			
			}
			
			if((custId != undefined && custId != null && custId != "") 
			&& (dupNo != undefined && dupNo != null && dupNo != "")){
			    $.ajax({
			        type: "POST",
			        handler: "lms1301formhandler",
					action : "showGrpData",
			        data: {
			            custId: custId,
			            dupNo: dupNo
			        },
			        success: function(responseData){
			            $("#unNormalForm #grpId").val(responseData.L120S01aForm.groupNo);
						$("#unNormalForm #grpName").val(responseData.L120S01aForm.groupName);
			        }
			    });				
			}else{
				// other.msg94=查無主要借款人統編或重覆序號，請確認簽報書是否登錄主要借款人
				CommonAPI.showMessage(i18n.lmscommom["other.msg94"]);
			}			
		},
		/**
		 *  引進異常通報表帳務資料
		 */		
		getUnNormalData : function(){
			// other.msg95=是否確定要引進帳務資料？
			CommonAPI.confirmMessage(i18n.lmscommom["other.msg95"], function(b){
			if (b) {					
		      		$.ajax({
		    			type : "POST",
		    			handler : "lms1301formhandler",
						action : "getUnNormalData",
		    			data : 
		    			{
		    				mainId : responseJSON.mainId
		    			},
		    			success:function(responseData){
							$("#unNormalForm").setData(responseData.unNormalForm, false);
		    			}
					});
				}				
			});			
		},
		/**
		 *  參貸行thickBox界面
		 */		
		thickBranch : function(){
			// 將外層已勾選的checkbox設定到內層thickbox上
			lmsM02Json.setCheckbox("unNormalForm", "tBranchForm");
			$("#thickBranch").thickbox({     // 使用選取的內容進行彈窗
			   title : i18n.lmscommom["other.msg96"], // other.msg96=異常通報參貸行登錄
			   width :900,
			   height : 450,
			   modal : true,
			   i18n:i18n.def,
			   readOnly: false,
			   buttons: {
			             "saveData": function() {
							// 異常通報參貸行前端js設定
							var list = "";
							var sign = ",";
							$("#tBranchForm").find("input[name='branchIds']:checked").each(function(i){
								list += ((list == "") ? "" : sign) + $(this).val();
							});						 	
			          		$.ajax({
				    			type : "POST",
				    			handler : "lms1301formhandler",
								action : "saveListL130s01b",
				    			data : 
				    			{
									branchs : list,
				    				mainId : responseJSON.mainId
				    			},
				    			success:function(responseData){
									// 將內層已勾選的checkbox設定到外層上
									lmsM02Json.setCheckbox("tBranchForm", "unNormalForm");
									$.thickbox.close();
									$.thickbox.close();
									CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
				    			}
							});
			             },
			             "close": function() {
			            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
			 					if(res){
			 						$.thickbox.close();
			 					}
			 		        });						 	
			             }
			        }
			    });			
		},
		/**
		 *  設定參貸行checkbox
		 *  註：將form1內所勾選的checkbox設定到form2上
		 */		
		setCheckbox : function(form1, form2){
			// 將form2初始化
			$("#" + form2).find("input[name='branchIds']:checkbox").attr("checked",false);
			$("#" + form1).find("input[name='branchIds']:checkbox:checked").each(function(i){
				var $this1 = $(this);
				$("#" + form2).find("input[name='branchIds']:checkbox").each(function(j){
					var $this2 = $(this);
					if($this1.val() == $this2.val()){
						$this2.attr("checked",true);
						// each break用法
						return false;
					}
				});
			});			
		},
		/**
		 *  異常通報登錄已辦事項ThickBox
		 */	
		thickUnNormal : function(){
			lmsM02Json.rUnNormalGrid();
			// 若為授管處或營運中心則會增加"批覆"按鈕
			var thickJson = {};
			if((lmsM02Json.unitNo == "918" || lmsM02Json.unitNo == "920" || lmsM02Json.unitNo == "931" || lmsM02Json.unitNo == "922"
					|| lmsM02Json.unitNo == "932" || lmsM02Json.unitNo == "933" || lmsM02Json.unitNo == "934"
					|| lmsM02Json.unitNo == "935") && userInfo.unitType != "5" ){
						thickJson = {
						 "other.msg100": function(){  // other.msg100=批覆
						 	lmsM02Json.thickDecide();
						 },
						 "other.msg101": function() { // other.msg101=關閉
			            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
			 					if(res){
			 						$.thickbox.close();
			 					}
			 		        });
			             }
					   };
					}else{
	
						thickJson = {
							"other.msg101": function(){ // other.msg101=關閉
								API.confirmMessage(i18n.def['flow.exit'], function(res){
									if (res) {
										$.thickbox.close();
									}
								});
							}
						};				
					}
			$("#thickUnNormal").thickbox({     // 使用選取的內容進行彈窗
			   title : i18n.lmscommom["other.msg97"], //other.msg97=異常通報登錄
			   width :990,
			   height : 460,
			   modal : true,
			   i18n:i18n.lmscommom,
			   readOnly: false,
			   buttons: $.extend({
			             "other.msg98": function() { // other.msg98=新增事項
							lmsM02Json.thickAdd();
			             },
			             "other.msg99": function() { // other.msg99=刪除新增事項
							CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
							if (b) {
									var rows = $("#unNormalGrid").getGridParam('selarrrow');
									var list = "";
									var sign = ",";
									for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
										if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
											var data = $("#unNormalGrid").getRowData(rows[i]);
											list += ((list == "") ? "" : sign ) + data.oid;
										}
									}
									if (list == "") {
										CommonAPI.showMessage(i18n.def["grid_selector"]);
										return;
									}													
					          		$.ajax({
						    			type : "POST",
						    			handler : "lms1301formhandler",
										action : "deleteL130s01a",
						    			data : 
						    			{
											oids : list,
											unitType : userInfo.unitType
						    			},
						    			success:function(responseData){
											$("#unNormalGrid").trigger("reloadGrid");
											$.thickbox.close();
							          		// 查詢組合字串
											lmsM02Json.thickOpenUnNormal(true);
						    			}
									});
								}				
							});
			             }
			           },thickJson)
			    });		
		},
		/**
		 *  查詢合併字串
		 */		
		queryCombine : function(responseData){
			$.ajax({
				type : "POST",
				handler : "lms1301formhandler",
				action : "queryL130m01b",
				data : 
				{
					mainId : responseJSON.mainId,
					unitType : userInfo.unitType
				},
				success:function(json){
					var unitNo = userInfo.unitNo;
					 
			        if (unitNo == "920"
					 || unitNo == "922"
					 || unitNo == "931"
					 || unitNo == "932"
					 || unitNo == "933"
					 || unitNo == "934"
					 || unitNo == "935") {
					 	
						// 營運中心
						$("#willIdea2").html(json.willIdea);
						$("#willIdeaB").html(json.willIdea);
			        }else if (unitNo == "918" && userInfo.unitType != "5") {
			            // 授管處
						
						$("#willIdea3").html(json.willIdea);
						$("#willIdeaC").html(json.willIdea);
			        }else{
						
						// 分行
						$("#unNormalForm #willIdea").html(json.willIdea);
						$("#willIdea1").html(json.willIdea);
					}
					$.thickbox.close();
					CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
				}
			});			
		},
		/**
		 *  異常通報批覆ThickBox
		 */	
		thickDecide : function(){
			$("#thickDecide").thickbox({     // 使用選取的內容進行彈窗
			   title : i18n.lmscommom["other.msg102"], // other.msg102=異常通報批覆
			   width :400,
			   height : 200,
			   modal : true,
			   i18n:i18n.def,
			   valign : "bottom",
			   align : "center",
			   readOnly: false,
			   buttons: {
			             "sure": function() {
						 	var radioVal = $("#tDecideForm").find("input[name='rDecide']:radio:checked").val();
							var rows = $("#unNormalGrid").getGridParam('selarrrow');
							var list = "";
							var sign = ",";
							for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
								if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
									var data = $("#unNormalGrid").getRowData(rows[i]);
									list += ((list == "") ? "" : sign ) + data.oid;
								}
							}
							if (list == "") {
								CommonAPI.showMessage(i18n.def["grid_selector"]);
								return;
							}
							$.thickbox.close();
							if(radioVal == "1" || radioVal == "2"){
				          		$.ajax({
					    			type : "POST",
					    			handler : "lms1301formhandler",
									action : "decideL130s01a",
					    			data : 
					    			{
										oids : list,
										kind : radioVal
					    			},
					    			success:function(responseData){
										$("#unNormalGrid").trigger("reloadGrid");
										//$.thickbox.close();
						          		// 查詢組合字串
										lmsM02Json.thickOpenUnNormal("decide");
					    			}
								});
							}else {
								// other.msg103=請選擇批覆種類
								CommonAPI.showMessage(i18n.lmscommom["other.msg103"]);
								return;
							}
			             },
			             "cancel": function() {
			            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
			 					if(res){
			 						$.thickbox.close();
			 					}
			 		        });
			             }
			           }
			    });		
		},
		/**
		 *  異常通報新增事項挑選種類ThickBox
		 */	
		thickAdd : function(){
			// 授管處或營運中心會多顯示'停權'選單
			if((lmsM02Json.unitNo == "918" || lmsM02Json.unitNo == "920" || lmsM02Json.unitNo == "931" || lmsM02Json.unitNo == "922"
				|| lmsM02Json.unitNo == "932" || lmsM02Json.unitNo == "933" || lmsM02Json.unitNo == "934"
				|| lmsM02Json.unitNo == "935")&& userInfo.unitType != "5"){
					$("#tAddForm .headOrArea").show();
				}
			$("#thickAdd").thickbox({     // 使用選取的內容進行彈窗
			   title : i18n.lmscommom["other.msg104"],  //other.msg104=異常通報登錄新增事項
			   width :400,
			   height : 200,
			   modal : true,
			   i18n:i18n.def,
			   valign : "bottom",
			   align : "center",
			   readOnly: false,
			   buttons: {
			             "sure": function() {
						 	var radioVal = $("input[name='seqKind']:radio:checked").val();
							$.thickbox.close();
							if(radioVal == "1"){
								// 呼叫一般事項
								lmsM02Json.thickAddSeq1();
							}else if(radioVal == "2"){
								// 呼叫解除異常通報
								lmsM02Json.thickAddSeq2();
							}else if(radioVal == "3"){
								// 呼叫停權
								lmsM02Json.thickAddSeq3();
							}
			             },
			             "cancel": function() {
			            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
			 					if(res){
			 						$.thickbox.close();
			 					}
			 		        });
			             }
			           }
			    });		
		},
		/**
		 *  異常通報新增事項ThickBox(一般事項)
		 */	
		thickAddSeq1 : function(){
			
		  //J-110-0250_05097_B1001 Web e-Loan信保案件異常通報預設勾選依信保基金規定辦理項目
		  var promiseCase = $("input[name='promiseCase']:radio:checked")
			.val();
		  if (promiseCase == undefined || promiseCase == null
				|| promiseCase == "") {
			 
//			MegaApi.showErrorMessage("「信保基金保證案件」"+
//					i18n.def['common.001']);
//			return;
		  }
		  
          $.ajax({
    			type : "POST",
    			handler : "lms1301formhandler",
				action : "getUnNormal",
    			data : 
    			{
    				mainId : responseJSON.mainId,
					unitType : userInfo.unitType,
					promiseCase : promiseCase
    			},				
    			success:function(responseData){
					$("#tAddSeq1Form #seq").setItems({
						size : 1,
		                item: responseData.seq,
		                format: "{value} - {key}"
					});
					
					//J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
					//預設勾選
					var default_seq = responseData.default_seq;
					if(default_seq != undefined && default_seq != null && default_seq != "" ){
						var default_seqArr = default_seq.split(",");
		                for (var i in default_seqArr) {
		                	$( "[name=seq][value="+default_seqArr[i]+"]").attr("checked" , true );
		                }			
					}
					 
					
					$("#thickAddSeq1").thickbox({     // 使用選取的內容進行彈窗
					   title : i18n.lmscommom["other.msg105"],	//other.msg105=一般事項
					   width :750,
					   height : 400,
					   modal : true,
					   i18n:i18n.def,
					   valign : "bottom",
					   align : "center",	   
					   readOnly: false,
					   buttons: {
					             "sure": function() {
								 	// 將使用者勾選異常通報事項存成陣列
								 	var seqNos = [];
								 	$("#tAddSeq1Form").find("[name'seq']:checkbox:checked").each(function(){
										seqNos.push($(this).val());
									});
					          		$.ajax({
						    			type : "POST",
						    			handler : "lms1301formhandler",
										action : "addUnNormal",
						    			data : 
						    			{
											seqNos : seqNos,
						    				mainId : responseJSON.mainId,
											unitType : userInfo.unitType
						    			},
						    			success:function(responseData){
											var unitNo = userInfo.unitNo;
									        if (unitNo == "920"
											 || unitNo == "922"
											 || unitNo == "931"
											 || unitNo == "932"
											 || unitNo == "933"
											 || unitNo == "934"
											 || unitNo == "935") {
												// 營運中心
												$("#willIdea2").html(responseData.willIdea);
												$("#willIdeaB").html(responseData.willIdea);
									        }else if (unitNo == "918" && userInfo.unitType != "5") {
									            // 授管處
												$("#willIdea3").html(responseData.willIdea);
												$("#willIdeaC").html(responseData.willIdea);
									        }else{
												// 分行
												$("#unNormalForm #willIdea").html(responseData.willIdea);
												$("#willIdea1").html(responseData.willIdea);
											}
											$("#unNormalGrid").trigger("reloadGrid");
											$.thickbox.close();
											$.thickbox.close();
											CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
						    			}
									});
					             },				             
					             "cancel": function() {
					            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
					 					if(res){
					 						$.thickbox.close();
					 					}
					 		        });
					             }
					           }
					    });					
    			}
			});		
		},
		/**
		 *  異常通報新增事項ThickBox(解除異常通報)
		 */	
		thickAddSeq2 : function(){
          $.ajax({
    			type : "POST",
    			handler : "lms1301formhandler",
				action : "getUnNormal",
    			data : 
    			{
    				mainId : responseJSON.mainId,
					isOther : true,
					type : "B"
    			},				
    			success:function(responseData){
					$("#tAddSeq2Form #seq2").setItems({
						size : 1,
		                item: responseData.seq,
		                format: "{value} - {key}"
					});
					setTimeout(function(){
					$("#thickAddSeq2").thickbox({     // 使用選取的內容進行彈窗
					   title : i18n.lmscommom["other.msg106"],	//other.msg106=解除異常通報
					   width :750,
					   height : 400,
					   modal : true,
					   i18n:i18n.def,
					   valign : "bottom",
					   align : "center",	   
					   readOnly: false,
					   buttons: {
					             "sure": function() {
								 	// 將使用者勾選異常通報事項存成陣列
								 	var seqNos = [];
								 	$("#tAddSeq2Form").find("[name'seq2']:checkbox:checked").each(function(){
										seqNos.push($(this).val());
									});									
					          		$.ajax({
						    			type : "POST",
						    			handler : "lms1301formhandler",
										action : "addUnNormal",
						    			data : 
						    			{
											seqNos : seqNos,
						    				mainId : responseJSON.mainId,
											unitType : userInfo.unitType
						    			},
						    			success:function(responseData){
											var unitNo = userInfo.unitNo;
									        if (unitNo == "920"
											 || unitNo == "922"
											 || unitNo == "931"
											 || unitNo == "932"
											 || unitNo == "933"
											 || unitNo == "934"
											 || unitNo == "935") {
												// 營運中心
												$("#willIdea2").html(responseData.willIdea);
												$("#willIdeaB").html(responseData.willIdea);
									        }else if (unitNo == "918" && userInfo.unitType != "5") {
									            // 授管處
												$("#willIdea3").html(responseData.willIdea);
												$("#willIdeaC").html(responseData.willIdea);
									        }else{
												// 分行
												$("#unNormalForm #willIdea").html(responseData.willIdea);
												$("#willIdea1").html(responseData.willIdea);
											}											
											$("#unNormalGrid").trigger("reloadGrid");
											$.thickbox.close();
											$.thickbox.close();
											CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
						    			}
									});
					             },				             
					             "cancel": function() {
					            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
					 					if(res){
					 						$.thickbox.close();
					 					}
					 		        });
					             }
					           }
					    });						
					},300);					
    			}
			});		
		},
		/**
		 *  異常通報新增事項ThickBox(停權)
		 */	
		thickAddSeq3 : function(){
          $.ajax({
    			type : "POST",
    			handler : "lms1301formhandler",
				action : "getUnNormal",
    			data : 
    			{
    				mainId : responseJSON.mainId,
					isOther : true,
					type : "C"
    			},				
    			success:function(responseData){
					$("#tAddSeq3Form #seq3").setItems({
						size : 1,
		                item: responseData.seq,
		                format: "{value} - {key}"
					});
					setTimeout(function(){
					$("#thickAddSeq3").thickbox({     // 使用選取的內容進行彈窗
					   title : i18n.lmscommom["other.msg107"],	//other.msg107=停權
					   width :780,
					   height : 400,
					   modal : true,
					   i18n:i18n.def,
					   valign : "bottom",
					   align : "center",	   
					   readOnly: false,
					   buttons: {
					             "sure": function() {
								 	// 將使用者勾選異常通報事項存成陣列
								 	var seqNos = [];
								 	$("#tAddSeq3Form").find("[name'seq3']:checkbox:checked").each(function(){
										seqNos.push($(this).val());
									});									
					          		$.ajax({
						    			type : "POST",
						    			handler : "lms1301formhandler",
										action : "addUnNormal",
						    			data : 
						    			{
											seqNos : seqNos,
						    				mainId : responseJSON.mainId,
											unitType : userInfo.unitType
						    			},
						    			success:function(responseData){
											var unitNo = userInfo.unitNo;
									        if (unitNo == "920"
											 || unitNo == "922"
											 || unitNo == "931"
											 || unitNo == "932"
											 || unitNo == "933"
											 || unitNo == "934"
											 || unitNo == "935") {
												// 營運中心
												$("#willIdea2").html(responseData.willIdea);
												$("#willIdeaB").html(responseData.willIdea);
									        }else if (unitNo == "918" && userInfo.unitType != "5" ) {
									            // 授管處
												$("#willIdea3").html(responseData.willIdea);
												$("#willIdeaC").html(responseData.willIdea);
									        }											
											$("#unNormalGrid").trigger("reloadGrid");
											$.thickbox.close();
											$.thickbox.close();
											CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
						    			}
									});
					             },				             
					             "cancel": function() {
					            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
					 					if(res){
					 						$.thickbox.close();
					 					}
					 		        });
					             }
					           }
					    });						
					},300);					
    			}
			});		
		},
		/**
		 *  異常通報明細表ThickBox
		 */	
		thickGridContent : function(data){
			$(".hideStop").show();
			var seqNoHead = data.seqNo.substring(0,1);
			var isOther = false;
			var type = null;
			if(seqNoHead == "B" || seqNoHead == "C"){
				isOther = true;
				type = seqNoHead;
			}else if(seqNoHead == "A"){
				$(".hideStop").hide();
			}
			// 開始依據分行別將部份欄位設成唯讀狀態
			var unitNo = userInfo.unitNo;
	        if (unitNo == "920"
			 || unitNo == "922"
			 || unitNo == "931"
			 || unitNo == "932"
			 || unitNo == "933"
			 || unitNo == "934"
			 || unitNo == "935") {
				// 營運中心
				$(".hideSub").attr("disabled",false);
				$(".hideHead").attr("disabled",false);
				$(".hideArea").attr("disabled",true);
	        }else if (unitNo == "918" && userInfo.unitType != "5") {
	            // 授管處
				$(".hideSub").attr("disabled",false);
				$(".hideArea").attr("disabled",false);
				$(".hideHead").attr("disabled",true);				
	        }else {
				// 分行
				$(".hideArea").attr("disabled",false);
				$(".hideHead").attr("disabled",false);
				$(".hideSub").attr("disabled",true);
				// 異常通報明細，分行端的編製中，總處的核定CHECK BOX需隱藏
				if(responseJSON.mainDocStatus == "01O"){
					$(".hideSub2").hide();	
				}
	        }
	        
	        if(data.seqNo=="A01" || data.seqNo=="A32" ){//A01-暫停動用授信額度
	        	$("#tr_row_L130S02A").show();
	        	
	        	var my_post_data = {
    					formAction : "queryL130S02A",
						mainId : responseJSON.mainId, 
						seqNo : data.seqNo
    				};
	        	var grid_id = "gridL130S02A_A01";
	        	if(data.seqNo=="A32"){
	        		grid_id = "gridL130S02A_A32";
	        	}
	        	
	        	var obj_col_ctlItem = {
					colHeader : i18n.lmsm02b['L130S02A.ctlItem'],
					align : "left", width : 200, sortable : true,
					name : 'ctlItem'
				};
	        	
	        	if(data.seqNo=="A01"){
	        		// 授管處：不用開放編輯 endDate 的功能
	        		// obj_col_ctlItem = $.extend(obj_col_ctlItem, {formatter:'click', onclick:editL130S02A });	        		
	        	}else if(data.seqNo=="A32"){
	        		
	        	}
	        	
	        	if($("#"+grid_id+".ui-jqgrid-btable").length >0){
	        		$("#"+grid_id).jqGrid("setGridParam", {
	    				postData : my_post_data,
	    				search : true
	    			}).trigger("reloadGrid");	        		
	        	}else{
	        		$("#"+grid_id).iGrid({
						handler : 'lms1201gridhandler',
						height : 100,
						sortname : 'ctlType|ctlItem',
						sortorder: 'asc|asc',
						postData : my_post_data,
						multiselect: true,
						shrinkToFit: false,
						colModel : [ {
							colHeader : i18n.lmsm02b['L130S02A.ctlType'],
							align : "left", width : 120, sortable : true,
							name : 'ctlType'						
						}, obj_col_ctlItem, {
							colHeader : i18n.lmsm02b['L130S02A.ctlName'],
							align : "left", width : 200, sortable : true,
							name : 'ctlName'
						}, {
							colHeader : "endDate",
							name : 'endDate',
							hidden : true
						}, {
							colHeader : "oid",
							name : 'oid',
							hidden : true
						}, {
							colHeader : "seqNo",
							name : 'seqNo',
							hidden : true
						} ]
					});
	        	}	
	        	
	        	if(data.seqNo=="A01"){
	        		$("#hs_gridL130S02A_A01").show();
	        		$("#hs_gridL130S02A_A32").hide();
	        	}else if(data.seqNo=="A32"){
	        		$("#hs_gridL130S02A_A01").hide();
	        		$("#hs_gridL130S02A_A32").show();
	        	}else{
	        		$("#hs_gridL130S02A_A01").hide();
	        		$("#hs_gridL130S02A_A32").hide();
	        	}
	        }else{
	        	$("#tr_row_L130S02A").hide();
	        }
	        
			// 開始查詢異常通報明細內容
      		$.ajax({
    			type : "POST",
    			handler : "lms1301formhandler",
				action : "queryL130s01a",
    			data : 
    			{
    				oid : data.oid,
					isOther : isOther,
					type : type
    			},
    			success:function(responseData){
					var jqObj_tGridContentForm = $("#tGridContentForm");
					var jqObj_seqKind = jqObj_tGridContentForm.find("#seqKind");
					var jqObj_setCurr = jqObj_tGridContentForm.find("#setCurr");
					var tGridContentForm = responseData.tGridContentForm;
					var seqKind = tGridContentForm.seqKind;
					var seqKindVal = encodeURI(tGridContentForm.seqKindVal);
					var setCurr = tGridContentForm.setCurr;

					jqObj_tGridContentForm.setData(tGridContentForm);
					jqObj_seqKind.setItems({
		                item: seqKind,
		                format: "{value} - {key}"
					});
					jqObj_setCurr.setItems({
		                item: setCurr,
		                format: "{value} - {key}"
					});
					jqObj_seqKind.val(seqKindVal);
					// 開啟明細thickBox界面
					$("#thickGridContent").thickbox({     // 使用選取的內容進行彈窗
					   title : i18n.lmscommom["other.msg108"],	//other.msg108=異常通報明細表
					   width :950,
					   height : 450,
					   modal : true,
					   i18n:i18n.def,
					   //valign : "bottom",
					   //align : "center",	   
					   readOnly: false,
					   buttons: {
					             "saveData": function() {
								 	// 呼叫檢核方法進行檢核
								 	if(lmsM02Json.checkData()){
										// 檢核不通過就中止
										return;	
									}
									// 開始進行儲存作業
									if($("#tGridContentForm").valid()){
						          		$.ajax({
							    			type : "POST",
							    			handler : "lms1301formhandler",
											action : "saveL130s01a",
							    			data : 
							    			{
												oid : data.oid,
							    				tGridContentForm : JSON.stringify($("#tGridContentForm").serializeData())
							    			},
							    			success:function(responseData){
												$("#unNormalGrid").trigger("reloadGrid");
												//$.thickbox.close();
								          		// 查詢組合字串
												lmsM02Json.queryCombine(responseData);
							    			}
										});										
									}
					             },				             
					             "close": function() {
					            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
					 					if(res){
					 						$.thickbox.close();
					 					}
					 		        });
					             }
					           }
					    });					
    			}
			});		
		},
		/**
		 *  異常通報明細儲存前的檢核
		 */		
		checkData : function(){
			var seqKind = $("#tGridContentForm #seqKind option:selected").val();
			var runDate = $("#tGridContentForm #runDate").val();
			var docDscr = $("#tGridContentForm #docDscr").val();			
			// 開始依據分行別進行檢核作業
			var unitNo = userInfo.unitNo;
	        if (unitNo == "920"
			 || unitNo == "922"
			 || unitNo == "931"
			 || unitNo == "932"
			 || unitNo == "933"
			 || unitNo == "934"
			 || unitNo == "935") {
				// 營運中心
	        }else if (unitNo == "918" && userInfo.unitType != "5") {
	            // 授管處				
	        }else {
				// 分行
				if(seqKind == '' || seqKind == undefined || seqKind == null){
					CommonAPI.showMessage("欄位[擬/已辦事項]不得為空白");
					return true;
				}else if(seqKind == "1"){
					if(runDate == '' || runDate == undefined || runDate == null){
						CommonAPI.showMessage("已辦時，欄位[處理日期]不得為空白");
						return true;					
					}
					if(docDscr == '' || docDscr == undefined || docDscr == null){
						CommonAPI.showMessage("已辦時，欄位[說明]不得為空白");
						return true;					
					}				
				}
	        }
			return false;
		},
		/**
		 *  異常通報類別ThickBox
		 */	
		thickClass : function(){
          $.ajax({
    			type : "POST",
    			handler : "lms1301formhandler",
				action : "getUnNormalClass",
    			success:function(responseData){
					$("#tClassForm #sMdClass").setItems({
		                item: responseData.sMdClass,
		                format: "{value}-{key}"
					});
					$("#thickClass").thickbox({     // 使用選取的內容進行彈窗
					   title : i18n.lmscommom["other.msg109"],	//other.msg109=異常通報類別
					   width :780,
					   height : 200,
					   modal : true,
					   i18n:i18n.def,
					   valign : "bottom",
					   align : "center",	   
					   readOnly: false,
					   buttons: {
					             "sure": function() {
								 	var $mdClass = $("#tClassForm").find("#sMdClass :selected");
								 	$("#unNormalForm").find("#pMdClass").html($mdClass.html());
									$("#unNormalForm").find("#mdClass").val($mdClass.val());
									$.thickbox.close();
					             },				             
					             "cancel": function() {
					            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
					 					if(res){
					 						$.thickbox.close();
					 					}
					 		        });
					             }
					           }
					    });					
    			}
			});		
		},
		/**
		 *  查詢往來異常通報資料
		 */		
		getLnfe0851 : function(){
      		$.ajax({
    			type : "POST",
    			handler : "lms1301formhandler",
				action : "getLnfe0851",
    			data : 
    			{
    				mainId : responseJSON.mainId
    			},
    			success:function(responseData){
					$("#unNormalForm").setData(responseData.unNormalForm);
    			}
			});			
		},
		/**
		 *查詢異常通報需後續追蹤事項後開啟 
		 */		
		thickOpenUnNormal : function(noThickBox){
			// 預設參數為分行
			var branchKind = "1";
			if(lmsM02Json.unitNo == "918" && userInfo.unitType != "5"){
				// 授管處
				branchKind = "3"
				
			}else if(lmsM02Json.unitNo == "920" || lmsM02Json.unitNo == "931" || lmsM02Json.unitNo == "922"
					|| lmsM02Json.unitNo == "932" || lmsM02Json.unitNo == "933" || lmsM02Json.unitNo == "934"
					|| lmsM02Json.unitNo == "935"){
				// 營運中心
				branchKind = "2";		
			}
			if(branchKind == "1"){
				
				
				// J-110-0250_05097_B1001 Web e-Loan信保案件異常通報預設勾選依信保基金規定辦理項目
				  var promiseCase = $("input[name='promiseCase']:radio:checked")
							.val();
				  if (promiseCase == undefined || promiseCase == null
							|| promiseCase == "") {
						 
						MegaApi.showErrorMessage("「信保基金保證案件」"+
								i18n.def['common.001']);
						return;
				  }
				
				
				// 分行
		  		$.ajax({
					type : "POST",
					handler : "lms1301formhandler",
					action : "queryL130m01b",
					data : 
					{
						branchKind : "1",
						mainId : responseJSON.mainId
					},
					success:function(json){
						// 設定分行合併字串
						$("#willIdea1").html(json.willIdea);
						$("#willIdea").html(json.willIdea);
						// 設定授管處批覆給分行看意見
						$("#willIdea4").html(json.willIdea4);
						//$.thickbox.close();
						if(noThickBox == "decide"){
							CommonAPI.showMessage(i18n.lmscommom["other.msg110"]);	//other.msg110=執行成功
						}else if(!noThickBox){
							// 開啟 thickBox
							openUnNormal();
						}else{
							CommonAPI.showMessage(i18n.lmscommom["other.msg111"]);	//other.msg111=刪除成功
						}
					}
				});
			}else{
				// 不管是授管處還是營運中心都會查詢分行合併內容
		  		$.ajax({
					type : "POST",
					handler : "lms1301formhandler",
					action : "queryL130m01b",
					data : 
					{
						branchKind : "1",
						mainId : responseJSON.mainId
					},
					success:function(json){
						// 設定分行合併字串
						$("#willIdea1").html(json.willIdea);
						// 設定授管處批覆給分行看意見
						$("#willIdea4").html(json.willIdea4);
						//$.thickbox.close();
						// 不管是授管處還是營運中心都會查詢營運中心合併內容
						$.ajax({
							type : "POST",
							handler : "lms1301formhandler",
							action : "queryL130m01b",
							data : 
							{
								branchKind : "2",
								mainId : responseJSON.mainId
							},
							success:function(json){			
									
								// 設定營運中心合併字串
								$("#willIdea2").html(json.willIdea);
								$("#willIdeaB").html(json.willIdea);
								//$.thickbox.close();
								// 若參數為授管處(3)						
								if(branchKind == "3"){
						      		$.ajax({
						    			type : "POST",
						    			handler : "lms1301formhandler",
										action : "queryL130m01b",
						    			data : 
						    			{
											branchKind : "3",
						    				mainId : responseJSON.mainId
						    			},
						    			success:function(json){
											// 設定授管處合併字串
											$("#willIdea3").html(json.willIdea);
											$("#willIdeaC").html(json.willIdea);
											//$.thickbox.close();
											if(noThickBox == "decide"){
												CommonAPI.showMessage(i18n.lmscommom["other.msg110"]);	//other.msg110=執行成功
											}else if(!noThickBox){
												// 開啟 thickBox
												openUnNormal();
											}else{
												CommonAPI.showMessage(i18n.lmscommom["other.msg111"]);	//other.msg111=刪除成功
											}
						    			}
									});							
								}else{
									if(noThickBox == "decide"){
										CommonAPI.showMessage(i18n.lmscommom["other.msg110"]);	//other.msg110=執行成功
									}else if(!noThickBox){
										// 開啟 thickBox
										openUnNormal();
									}else{
										CommonAPI.showMessage(i18n.lmscommom["other.msg111"]);	//other.msg111=刪除成功
									}									
								}					
							}
						});
					}
				});				
			}			
		},
		/**
		 *異常通報案件登錄批覆內容
		 */		
		thickOpenDecide : function(){
      		$.ajax({
    			type : "POST",
    			handler : "lms1301formhandler",
				action : "queryL130m01b",
    			data : 
    			{
					branchKind : "3",
    				mainId : responseJSON.mainId
    			},
    			success:function(json){
					// 設定批覆給分行看的合併字串
					$("#willIdea3a").html(json.willIdea);
		      		$.ajax({
		    			type : "POST",
		    			handler : "lms1301formhandler",
						action : "queryL130m01bHead",
		    			data : 
		    			{
		    				mainId : responseJSON.mainId
		    			},
		    			success:function(json){
							$("#headDscr").val(json.headDscr);
							$("#thickOpenDecide").thickbox({
						       title : i18n.lmscommom["other.msg112"],	//other.msg112=異常通報案件登錄批覆內容
						       width :600,
						       height : 400,
						       modal : true,
							   i18n:i18n.def,
							   valign : "bottom",
							   align : "center",	   
							   readOnly: false,
						       buttons: {	   	            
						                 "sure": function() {
										 	// 進行儲存
								      		$.ajax({
								    			type : "POST",
								    			handler : "lms1301formhandler",
												action : "saveL130m01b",
								    			data : 
								    			{
								    				mainId : responseJSON.mainId,
													headDscr : $("#headDscr").val()
								    			},
								    			success:function(json){
													$.thickbox.close();
													$.thickbox.close();
													CommonAPI.showMessage(json.NOTIFY_MESSAGE);
								    			}
											});
						                 },
						                 "cancel": function() {
						                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
						     					if(res){
						     						$.thickbox.close();
						     					}
						     		        });
						                 }				 
						               }
						        });					
		    			}
					});
    			}
			});			
		},
		/**
		 *  查詢往來異常通報資料
		 */		
		setShowAllBranch : function(){
			var td1 ="<td style='border-right:none;'>";
			var td2 ="<td style='border-right:none;'>";
			var td3 ="<td style='border-right:none;'>";
			var td4 ="<td style='border-right:none;'>";
      		$.ajax({
    			type : "POST",
				async: false,
    			handler : "lmscommonformhandler",
				action : "queryAllBranch",
    			data :{}, 
    			success:function(obj){
					$("#showAllBranch1").empty();
					$("#showAllBranch2").empty();
					for (var i = 0; i < obj.size; i++) {
					 	
						if(i % 4 == 1){
							td2 += "<label><input type='checkbox' name='branchIds' value='"+ obj.item[i].brCode +"' />"+ obj.item[i].brCode +"."+obj.item[i].brName + "</label><br/>";
						}else if(i % 4 == 2){
							td3 += "<label><input type='checkbox' name='branchIds' value='"+ obj.item[i].brCode +"' />"+ obj.item[i].brCode +"."+obj.item[i].brName + "</label><br/>";
						}else if(i % 4 == 3){
							td4 += "<label><input type='checkbox' name='branchIds' value='"+ obj.item[i].brCode +"' />"+ obj.item[i].brCode +"."+obj.item[i].brName + "</label><br/>";
						}else {
							td1 += "<label><input type='checkbox' name='branchIds' value='"+ obj.item[i].brCode +"' />"+ obj.item[i].brCode +"."+obj.item[i].brName + "</label><br/>";
						}
                    }
					td1 += "</td>";
					td2 += "</td>";
					td3 += "</td>";
					td4 += "</td>";
					$("#showAllBranch1").append("<tr>" + td1 + td2+ td3+ td4+ "</tr>");       
					$("#showAllBranch2").append("<tr>" + td1 + td2+ td3+ td4+ "</tr>");
					 
    			}
			});			
		},		
		btnAddL130S02AFree : function(){
			var seqNo = $("#tGridContentForm #seqNo").val() || '';

			$("#tL130S02AFormFree").find("input[name=choose_endDate]").val("9999-12-31");
			
			if(seqNo=="A01"){
				$("#tr_row_L130S02AFormFree_endDate").hide();
			}else if(seqNo=="A32"){
				$("#tr_row_L130S02AFormFree_endDate").hide();
			}
			
			$("#thickL130S02AFormFree").thickbox({
    			title: '', width: 470, height: 250, modal: true, i18n:i18n.def, readOnly: false,
    			buttons: {
    				"saveData": function() {
    					var tabForm = $("#tL130S02AFormFree");
    					if (! tabForm.valid()) {
                            return;
                        }
    					    					
    					$.ajax({type:"POST", handler:"lms1301formhandler", action : "addL130S02AFree"
    			          	, formId : ""
    			          	, data: {'mainId': responseJSON.mainId
    			          		, 'seqNo': seqNo
    			          		, 'l130s01a_oid': $("form#tGridContentForm").attr("formoid")    			          		
    			          		, 'ctlType': tabForm.find("[name=choose_ctlType]").val()
    		          			, 'ctlItem': tabForm.find("[name=choose_ctlItem]").val()
 		          				, 'endDate': tabForm.find("[name=choose_endDate]").val()
    			          	},
    				    	success:function(){
    				    		if(seqNo=="A01"){
    				    			$("#gridL130S02A_A01").trigger('reloadGrid');
    				    		}else if(seqNo=="A32"){
    				    			$("#gridL130S02A_A32").trigger('reloadGrid');	
    				    		}    				    		    							
    				    	}
    			        });
    			    },
    			    "close": function() {
    			        $.thickbox.close();					 	
    			    }
    			}
    		});
		},
		btnAddL130S02AInCase : function(){
			choose_ctlTypeItem().done(function(param){
				var tabForm = $("#tL130S02AFormA");
				//清空 form
				tabForm.reset();
				
				tabForm.injectData(param);
				tabForm.find("input[name=endDate]").val("9999-12-31");
				
				var seqNo = $("#tGridContentForm #seqNo").val() || '';
				
		        if(seqNo=="A01"){
		        	$("#tr_row_L130S02AFormA_endDate").hide();
		        }else if(seqNo=="A32"){
		        	$("#tr_row_L130S02AFormA_endDate").hide();
		        }
		        //原本會顯示 thickbox，讓經辦輸入 endDate
		        //而授管處：不用顯示，帶入9999-12-31即可
				//TODO $("#thickL130S02AFormA").thickbox({
	    			
				$.ajax({type:"POST", handler:"lms1301formhandler", action : "addL130S02A"
		          	, formId : ""
		          	, data: $.extend({'mainId': responseJSON.mainId
		          			, 'seqNo':seqNo
		          			, 'l130s01a_oid': $("form#tGridContentForm").attr("formoid") 
		          		}, tabForm.serializeData() ),
			    	success:function(){
			    		if(seqNo=="A01"){
			    			$("#gridL130S02A_A01").trigger('reloadGrid');
			    		}else if(seqNo=="A32"){
			    			$("#gridL130S02A_A32").trigger('reloadGrid');	
			    		}    							
			    	}
		        });
				
			});
			
		},
		btnDelL130S02A : function(){
			var seqNo = $("#tGridContentForm #seqNo").val() || '';
			if(seqNo=="A01" || seqNo=="A32"){
				var grid_id = "gridL130S02A_A01";
				if(seqNo=="A32"){
					grid_id = "gridL130S02A_A32";
				}
				
				var rows = $("#"+grid_id).getGridParam('selarrrow');
				var oid_arr = [];
				for (var i=0;i<rows.length;i++){
					var data = $("#"+grid_id).getRowData(rows[i]);
					oid_arr.push( data.oid );
					
				}
				if (oid_arr.length==0) {
					CommonAPI.showMessage(i18n.def["grid_selector"]);
					return;
				}
				
	      		$.ajax({
	    			type : "POST",
	    			handler : "lms1301formhandler",
					action : "deleteL130S02A",
	    			data : { 'oids' : oid_arr.join("|")
	    				, 'l130s01a_oid': $("form#tGridContentForm").attr("formoid") 
	    			},
	    			success:function(responseData){
	    				$("#"+grid_id).trigger('reloadGrid');		    		
	    			}
				});
			}
			
			
		},
	    //J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
	    hideAbnormalPanelbyCaseType: function(){
	         var miniFlag = responseJSON.miniFlag;
	         var caseType = responseJSON.caseType;
	         var caseTypeA = responseJSON.caseTypeA;
	         if(responseJSON.docType == "1" && responseJSON.docCode == "4"  ){
	        	 if(miniFlag == "Y" && caseType =="00A"  && caseTypeA =="A01" ){
	        		 return true;
	             }else{
	            	 return false; 
	             }
	         }else{
	        	 return false; 
	         }
	    },
	   //J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
	    isAbnormalAuthLvlAreaSign: function(){
	    	if (responseJSON.docType == "1" && responseJSON.docCode == "4"  && responseJSON.docKind == "1" && responseJSON.authLvl == "3" ) {
	    		var miniFlag = responseJSON.miniFlag;
	            var caseType = responseJSON.caseType;
	            var caseTypeA = responseJSON.caseTypeA;
	    		//企金、異常通報、營運中心授權內案件，小規模營業人
	    		if(miniFlag == "Y" && caseType =="00A"  ){
					return true;
				}else{
					return false; 
	            }
	    	}else{
	       	    return false; 
	        }
	    },
	    //J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
	    showCaseTypeA: function(docType,docCode,miniFlag,caseType){
	    	
	    	if(responseJSON.docType == "1" && responseJSON.docCode == "4" && miniFlag == "Y" && caseType =="00A"  ){
	        	//異常通報
	    		return true;
	    	}else{
	    		return false;
	    	}    
	        
	    },
	    //J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
	    hidePanelbyCaseType_caseTypeA_00A: function(docType,docCode,miniFlag,caseType){
	    	 var miniFlag = responseJSON.miniFlag;
	         var caseType = responseJSON.caseType;
	         if(responseJSON.docType == "1" && responseJSON.docCode == "4"  ){
	        	 if(miniFlag == "Y" && caseType =="00A"  ){
	        		 //caseTypeA是小規模的才隱藏頁籤
	        		 return true;
	             }else{
	            	 return false; 
	             }
	         }else{
	        	 return false; 
	         }
	    },
	    //J-112-0134 產製關係戶清單
	    genLms1201r49 : function (genAction) {
			$.ajax({
	            handler : "lms1301formhandler",
	            data : {
	                formAction : "genLms1201r49",
	                oid : responseJSON.oid,
	                genAction : genAction
	            },
	            success: function(json){
                    // 於畫面上渲染關係戶清單下載用tag
                    $.each(json.attch, function(spanId, arr) {
                    	var spanIdStr = encodeURI(spanId);
                        var dyna = [];
                        $.each(arr, function(idx, jsonItem) {
                        	var inProcess = false;
                        	if(jsonItem.flag !== null && Boolean(jsonItem.flag)) {
                        		if(jsonItem.flag == "P") {
                        			inProcess = true;
                        		}
                        	}
                        	dyna.push("<span style='color:blue;text-decoration:underline' class='" 
                        			+ (inProcess ? "color-red" : "linkDocFile") + "' oid='" + jsonItem.oid + "'>"
                        			+ (inProcess ? "產生中...請等候" : jsonItem.srcFileName) + "</span>&nbsp;&nbsp;&nbsp;&nbsp;"
                        			+ "(" + jsonItem.uploadTime + ")");
                        });
                        var spanIdJqDomobj = $("#" + spanIdStr);
                        spanIdJqDomobj.html(dyna.join("<br/>"));
                        spanIdJqDomobj.find('span.linkDocFile').click(function() {
                        	var oid = jQuery(this).attr("oid");
                        	$.capFileDownload({
                        		handler:"simplefiledwnhandler",
                        		data : {
                        			fileOid : oid
                        		}
                        	});				
                        });
                    });
                	if (genAction == 'Y') {
                		if (json.success === 'Y') {
                        	API.showMessage("執行完成");
                		} else {
                        	API.showMessage("產製失敗");
                		}
                	}
	            }
			})
	    }
	};
 	// 呼叫異常通報Grid
	//lmsM02Json.unNormalGrid();
	if(lmsM02Json.docType == "2"){
		// 個金案件不需要顯示負責人相關資料，所以要隱藏
		$("#unNormalForm .hPerson").hide();
	}	
}

/**
 * 開啟異常通報需後續追蹤事項ThickBox
 */
function openUnNormal(){
	$("#thickOpenUnNormal").thickbox({     // 使用選取的內容進行彈窗
       title : i18n.lmscommom["other.msg113"],	//other.msg113=異常通報需後續追蹤事項
       width :900,
       height : 460,
       modal : true,
	   i18n:i18n.def,
	   readOnly: false,
       buttons: {            
                 "close": function() {
                	 API.confirmMessage(i18n.def['flow.exit'], function(res){
     					if(res){
     						$.thickbox.close();
     					}
     		        });
                 }
               }
        });	
}
function choose_ctlTypeItem(){
	var my_dfd = $.Deferred();    	
	choose_ctlType().done(function(param){
		var ctlType = param.ctlType;
		var ctlItem = '';
		var ctlName = '';
		
		var resultJSON = {'ctlType':ctlType, 'ctlType_Select':ctlType};
		
		if(ctlType=='1'){
			var my_post_data = {
					formAction : "queryL130S02A_cntrNo",
					mainId : responseJSON.mainId
				};
			var grid_id = "gridL130S02A_cntrNo";
			if($("#"+grid_id+".ui-jqgrid-btable").length >0){
        		$("#"+grid_id).jqGrid("setGridParam", {
    				postData : my_post_data,
    				search : true
    			}).trigger("reloadGrid");	        		
        	}else{
        		$("#"+grid_id).iGrid({
					handler : 'lms1201gridhandler',
					height : 230,
					sortname : 'custId|dupNo',
					sortorder: 'asc|asc',
					postData : my_post_data,
					multiselect: true,
					shrinkToFit: false,
					colModel : [ {
						colHeader : i18n.lmscommom['L120M01A.custid'],
						align : "left", width : 120, sortable : false,
						name : 'idDup'						
					}, {
						colHeader : i18n.lmsm02b['L130S02A.ctlName'],
						align : "left", width : 200, sortable : false,
						name : 'name'
					}, {
						colHeader : i18n.lmscommom['other.msg137'],
						align : "left", width : 100, sortable : false,
						name : 'cntrNo'
					} ]
				});
        	}
			
			$("#thickL130S02A_ctlType1").thickbox({ // 使用選取的內容進行彈窗
		        title: i18n.lmsm02b['L130S02A.ctlType.1'],
		        width: 520, height: 380, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
		        buttons: {
		            "sure": function(){
		            	var rowId_arr = $("#"+grid_id).getGridParam('selarrrow');
		            	var cntrNo_arr = [];
		           	 	for (var i = 0; i < rowId_arr.length; i++) {
		        			var data = $("#"+grid_id).getRowData(rowId_arr[i]);
		        			cntrNo_arr.push(data.cntrNo);    			
		                }
		           	 	
		            	if(cntrNo_arr.length>0){
		            		$.thickbox.close();
		            		
		            		ctlItem = cntrNo_arr.join("|");
		            		ctlName = '';
		            		
		            		my_dfd.resolve( $.extend(resultJSON, {'ctlItem':ctlItem, 'ctlName':ctlName}) );		            		
		            	} else {
		          			MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def['grid.selrow']);		          			
		          		}
		                
		            },
		            "cancel": function(){
		                $.thickbox.close();
		            }
		        }
		    });
		}else if(ctlType=='2'){
			var my_post_data = {
					formAction : "queryL130S02A_idDup",
					mainId : responseJSON.mainId
				};
			var grid_id = "gridL130S02A_idDup";
			if($("#"+grid_id+".ui-jqgrid-btable").length >0){
        		$("#"+grid_id).jqGrid("setGridParam", {
    				postData : my_post_data,
    				search : true
    			}).trigger("reloadGrid");	        		
        	}else{
        		$("#"+grid_id).iGrid({
					handler : 'lms1201gridhandler',
					height : 85,
					sortname : 'custId|dupNo',
					sortorder: 'asc|asc',
					postData : my_post_data,
					multiselect: false,
					shrinkToFit: false,
					colModel : [ {
						colHeader : i18n.lmscommom['L120M01A.custid'],
						align : "left", width : 120, sortable : false,
						name : 'idDup'						
					}, {
						colHeader : i18n.lmsm02b['L130S02A.ctlName'],
						align : "left", width : 200, sortable : false,
						name : 'name'
					} ]
				});
        	}
			
			$("#thickL130S02A_ctlType2").thickbox({ // 使用選取的內容進行彈窗
		        title: i18n.lmsm02b['L130S02A.ctlType.2'],
		        width: 380, height: 230, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
		        buttons: {
		            "sure": function(){
		            	var selrow = $("#"+grid_id).getGridParam('selrow');
		            	if(selrow){
		            		$.thickbox.close();
		            		
		            		var rtnData = $("#"+grid_id).getRowData(selrow);
		            		ctlItem = rtnData.idDup;
		            		ctlName = rtnData.name;
		            		
		            		my_dfd.resolve( $.extend(resultJSON, {'ctlItem':ctlItem, 'ctlName':ctlName}) );		            		
		            	} else {
		          			MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def['grid.selrow']);		          			
		          		}
		                
		            },
		            "cancel": function(){
		                $.thickbox.close();
		            }
		        }
		    });
		}else if(ctlType=='3'){
			//用 LMSM02Panel.html 中的 unNormalForm 的 grpId, grpName
			
    		ctlItem = $("#unNormalForm #grpId").val();
    		ctlName = $("#unNormalForm #grpName").val();
    		if($.trim(ctlItem)==""){
    			my_dfd.reject();
    			CommonAPI.showMessage("尚未引進集團");
    		}else{
    			my_dfd.resolve( $.extend(resultJSON, {'ctlItem':ctlItem, 'ctlName':ctlName}) );	
    		}    		
		}		
	});
	return my_dfd.promise();
}
function choose_ctlType(){
	var my_dfd = $.Deferred();    	
	var _id = "_div_chooseL130S02A_ctlType";
	var _form = _id+"_form";
	if ($("#"+_id).size() == 0){
		var dyna = [];
		dyna.push("<div id='"+_id+"' style='display:none;' >");
		dyna.push("<form id='"+_form+"'>");
		dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lmsm02b["L130S02A.ctlType.1"]+"</label></p>");
		dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.lmsm02b["L130S02A.ctlType.2"]+"</label></p>");
		dyna.push("		<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lmsm02b["L130S02A.ctlType.3"]+"</label></p>");
		dyna.push("</form>");
		
		dyna.push("</div>");
		
	     $('body').append(dyna.join(""));
	}
	//clear data
	$("#"+_form).reset();
	
	$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lmsm02b['L130S02A.ctlType'],
        width: 380, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
        buttons: {
            "sure": function(){
                if (!$("#"+_form).valid()) {
                    return;
                }
                $.thickbox.close();
                
                var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                my_dfd.resolve({'ctlType': val});
            },
            "cancel": function(){
                $.thickbox.close();
                my_dfd.reject();
            }
        }
    });
	return my_dfd.promise();	
}
function editL130S02A(cellvalue, options, rowObject){	
	$.ajax({type:"POST", handler:"lms1301formhandler", action : "queryL130S02A"
      	, data: {oid: rowObject.oid},
    	success:function(json_editL130S02A){
    		var tabForm = $("#tL130S02AFormU"); 
    		tabForm.find('[name=ctlTypeU]').val(json_editL130S02A.ctlType);
    		tabForm.find('[name=ctlItemU]').val(json_editL130S02A.ctlItem);
    		tabForm.find('[name=ctlNameU]').val(json_editL130S02A.ctlName);
    		tabForm.find('[name=endDateU]').val(json_editL130S02A.endDate);
    		//若 html 有2個 id 都是 endDate
    		//會造成 datepicker 沒有把 user 選取的日期值 set 回 <input type='text'>
    		//所以把 name 改成 endDateU
    		$("#thickL130S02AFormU").thickbox({
    			title: '', width: 400, height: 250, modal: true, i18n:i18n.def, readOnly: false,
    			buttons: {
    				"saveData": function() {		
    					if (! tabForm.valid()) {
                            return;
                        }
    					$.thickbox.close();
    					
    			        $.ajax({type:"POST", handler:"lms1301formhandler", action : "updateL130S02A"
    			          	, formId : ""
    			          	, data: {	oid: json_editL130S02A.oid
    			          			, endDate: tabForm.find("[name=endDateU]").val()
    			          	},
    				    	success:function(){
    				    		if(rowObject.seqNo=="A01"){
    				    			$("#gridL130S02A_A01").trigger('reloadGrid');	
    				    		}else if(rowObject.seqNo=="A32"){
    				    			$("#gridL130S02A_A32").trigger('reloadGrid');
    				    		}
    				    		    							
    				    	}
    			        });
    			    },
    			    "cancel": function() {
    			        $.thickbox.close();					 	
    			    }
    			}
    		});
    	}
    });		
}

$(document).ready(function() {
/*
	var $unNormalForm = $("#unNormalForm");
	var promiseCase = $unNormalForm.find("[name='promiseCase']:radio:checked").val();
	// 當為信保案件時，保證成數才要顯示
	if(promiseCase == "Y"){
		$unNormalForm.find(".showPro").show();
	}else{
		$unNormalForm.find(".showPro").hide();
	}
*/
	if(lmsM02Json.docType == "2"){
		// 個金案件不需要顯示負責人相關資料，所以要隱藏
		$("#unNormalForm .hPerson").hide();
	}
	 
	lmsM02Json.setShowAllBranch();
	
	if(lmsM02Json.typCd == "5"){
		//海外不顯示引進帳務資訊按鈕
		$("#btnGetUnNormalData").hide();
	}else{
		$("#btnGetUnNormalData").show();
	}
	
	
	//J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
	if (lmsM02Json.hideAbnormalPanelbyCaseType()){
		//小規模異常通報不要顯示:該戶在同業之總餘額、同業擬（已）採取之措施 、陳報及說明事項 
		$(".showForNoneCaseTypeC").hide();
	}else{
		$(".showForNoneCaseTypeC").show();
	}
	
	
	
	
	
});