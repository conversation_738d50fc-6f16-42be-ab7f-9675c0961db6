package com.mega.eloan.lms.dw.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DwRoclistEcusService;

@Service
public class DwRoclistEcusServiceImpl extends AbstractDWJdbc implements
		DwRoclistEcusService {
	public List<?> findDW_ROCLIST_ECUS_Main3(String custId, String dupCode) {
		return this.getJdbc().queryForList(
				"DWADMDW_ROCLIST_ECUS.selDW_ROCLIST_ECUS_Main3",
				new Object[] {
						custId,
						// 配合DB查詢條件重複序號為0都設定為空字串
						("0".equals(dupCode) ? "" : dupCode), custId,
						("0".equals(dupCode) ? "" : dupCode), custId,
						("0".equals(dupCode) ? "" : dupCode)

				});
	}
}
