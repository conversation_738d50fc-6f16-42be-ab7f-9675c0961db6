package com.mega.eloan.lms.dw.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dw.service.DwBGMOPEN1YService;

/**
 * 全國營業(稅籍)登記(停業以外之非營業中)資料 DWADM.OTS_DW_BGMOPEN1Y
 */
@Service
public class DwBGMOPEN1YServiceImpl extends AbstractDWJdbc implements
		DwBGMOPEN1YService {

	/**
	 * 取得全國營業(稅籍)登記(停業以外之非營業中)資料
	 * 
	 * @param custId
	 * @return
	 */
	@Override
	public Map<String, Object> getDataFromTaxation(String custId) {
		return this.getJdbc().queryForMap("OTS_DW_BGMOPEN1YByCustId",
				new String[] { custId });
	}
}
