package com.mega.eloan.lms.cls.handler.form;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.gwclient.PLOAN001;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1220M04Page;
import com.mega.eloan.lms.cls.pages.CLS1220V08Page;
import com.mega.eloan.lms.cls.panels.CLS1220S06Panel;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01B;
import com.mega.eloan.lms.model.C122M01C;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.eloan.lms.model.C122M01G;
import com.mega.eloan.lms.model.C122S01C;
import com.mega.eloan.lms.model.C122S01F;
import com.mega.eloan.lms.model.C122S01G;
import com.mega.eloan.lms.model.C122S01H;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 線上信貸
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1220m04formhandler")
@DomainClass(C122M01A.class)
public class CLS1220M04FormHandler extends AbstractFormHandler {

	@Resource
	CLS1220Service service;

	@Resource
	UserInfoService userInfoService;	
	
	@Resource
	BranchService branchService;

	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;

	@Resource
	DocLogService docLogService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	CLS1220M10FormHandler cls1220m10formhandler;
	
	@Resource
	CodeTypeService codetypeService;
	
	Properties prop_cls1220m04 = MessageBundleScriptCreator.getComponentResource(CLS1220M04Page.class);
	Properties prop_cls1220v08 = MessageBundleScriptCreator.getComponentResource(CLS1220V08Page.class);
	Properties prop_abstractEloan = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
	Properties prop_cls1220s06 = MessageBundleScriptCreator.getComponentResource(CLS1220S06Panel.class);
	
	/**
	 * 查詢文件
	 * 
	 * @param params
	 *            PageParameters
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int page = Util.parseInt(params.getString("page"));

		C122M01A meta = null;
		C122M01A parent_meta = null;
		if (mainOid != null) {
			meta = service.getC122M01A_byOid(mainOid); 
			if(meta!=null){
				parent_meta = service.getPloanParentMeta(meta);
			}
		}
		String itemType = "0";
		C122M01B c122m01b = service.getC122M01B_byMainIdItemType(meta.getMainId(), itemType);
		PLOAN001 ploan_obj = null;
		if(c122m01b!=null 
				&& (Util.equals(UtilConstants.C122_ApplyKind.P, meta.getApplyKind()) || Util.equals(UtilConstants.C122_ApplyKind.Q, meta.getApplyKind())) 
				&& Util.equals("PLOAN001", c122m01b.getJsonVoClass())){
			ObjectMapper objectMapper = new ObjectMapper();
			try {
				ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN001.class);
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));				
			}							
		}
		//===================
		String IncomType = Util.trim(meta.getIncomType());
		switch (page) {
		case 1: // CLS1220S01PanelP.html
			LMSUtil.addMetaToResult(result, meta, new String[]{  "ownBrId", "custId", "dupNo", "orgBrId"
					, "custName", "docStatus", "agreeQueryEJTs", "agreeQueryEJVer",  "agreeQueryEJIp"
					, "applyTS", "ploanCaseNo", "applyCurr", "notifyMemo", "createTime", "updateTime"
					, "statFlag", "ploanCasePos", "isClosed", "stkhQueryEJTs", "maturity","maturityM"
					, "usePlan"
					});
				
			if(Util.equals(IncomType, UtilConstants.C122_IncomType.線上)){//線上進件才會有IP
				String applyIpRefg = "0";
				if(Util.isNotEmpty(meta.getApplyIpFreq())){
					if(meta.getApplyIpFreq().compareTo(BigDecimal.ZERO) !=0){
						BigDecimal ApplyIpFreq = meta.getApplyIpFreq().stripTrailingZeros();
						String ApplyIpFreqStr = ApplyIpFreq.toPlainString();
						applyIpRefg = ApplyIpFreqStr;
					}
				}
//				(在30日內共進件次數：N次)
				String newApplyIp = Util.trim(meta.getApplyIPAddr()) + " (在30日內共進件次數：" + applyIpRefg + " 次)";
				result.set("applyIPAddr", newApplyIp);
			}else{
				result.set("applyIPAddr", "");
			}
			
			//H-111-0199 iXML
			List<C122M01G> c122m01gList = service.findC122M01GbyMainId(meta.getMainId());
			if(c122m01gList!=null && c122m01gList.size()>0){
				result.set("showIxml", "Y");
			}else{
				result.set("showIxml", "");
			}
			
			result.set("ownBrName", branchService.getBranchName(meta.getOwnBrId()));
			result.set("applyAmt", NumConverter.addComma(meta.getApplyAmt()==null?BigDecimal.ZERO:meta.getApplyAmt()));
			result.set("creator", Util.trim(meta.getCreator())+" "+Util.trim(new UserNameFormatter(userInfoService).reformat(meta.getCreator())));
			result.set("updater", Util.trim(meta.getUpdater())+" "+Util.trim(new UserNameFormatter(userInfoService).reformat(meta.getUpdater())));
			
			result.set("incomType", Util.trim(meta.getIncomType()));
			
			//青創欄位對應調整
			if(Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind()) || Util.equals(UtilConstants.C122_ApplyKind.J, meta.getApplyKind())){
				//同意聯徵時間(agreeQueryEJTs)-青創同意聯徵時間就是C122S01C簡訊OTP時間(OTP_Time) 但有時間不代表OTP驗證成功，需C122S01C的OTP_STATUS=4且OTP_ERRORCODE=4001才為成功
				C122S01C c122s01c=service.getC122S01C(meta.getMainId());
				if(c122s01c==null){
					result.set("agreeQueryEJTs", "");
					result.set("agreeQueryEJIp", "");
				}else{
					if( !(Util.equals("4", c122s01c.getOTP_STATUS()) && Util.equals("4001", c122s01c.getOTP_ERRORCODE())
							&& Util.equals("00", c122s01c.getOTP_DATARESULT()) && Util.equals("00", c122s01c.getOTP_ACCTRESULT())
							&& Util.equals("01", c122s01c.getOTP_TYPERESULT())	) ){
						result.set("agreeQueryEJTs", "");
						result.set("agreeQueryEJIp", "");
					}
				}
			}
			
			//中鋼特定欄位
			result.set("stkhBank33", Util.equals("1", Util.trim(meta.getStkhBank33()))? prop_cls1220m04.getProperty("C122M01A.yes") : Util.equals("0", Util.trim(meta.getStkhBank33()))? prop_cls1220m04.getProperty("C122M01A.no") : "");
			result.set("stkhFh44", Util.equals("1", Util.trim(meta.getStkhFh44()))? prop_cls1220m04.getProperty("C122M01A.yes") : Util.equals("0", Util.trim(meta.getStkhFh44()))? prop_cls1220m04.getProperty("C122M01A.no") : "");
			result.set("stkhFh45", Util.equals("1", Util.trim(meta.getStkhFh45()))? prop_cls1220m04.getProperty("C122M01A.yes") : Util.equals("0", Util.trim(meta.getStkhFh45()))? prop_cls1220m04.getProperty("C122M01A.no") : "");
			result.set("stkhRelFg", Util.equals("1", Util.trim(meta.getStkhRelFg()))? prop_cls1220m04.getProperty("C122M01A.yes") : Util.equals("0", Util.trim(meta.getStkhRelFg()))? prop_cls1220m04.getProperty("C122M01A.no") : "");
			result.set("stkhCoFg", Util.equals("1", Util.trim(meta.getStkhCoFg()))? prop_cls1220m04.getProperty("C122M01A.yes") : Util.equals("0", Util.trim(meta.getStkhCoFg()))? prop_cls1220m04.getProperty("C122M01A.no") : "");
			result.set("needW8BEN", Util.equals("Y", Util.trim(meta.getNeedW8BEN()))? prop_cls1220m04.getProperty("C122M01A.yes") : Util.equals("N", Util.trim(meta.getNeedW8BEN()))? prop_cls1220m04.getProperty("C122M01A.no") : "");
			result.set("needCRS", Util.equals("Y", Util.trim(meta.getNeedCRS()))? prop_cls1220m04.getProperty("C122M01A.yes") : Util.equals("N", Util.trim(meta.getNeedCRS()))? prop_cls1220m04.getProperty("C122M01A.no") : "");
			String rateAdjNotify = Util.trim(meta.getRateAdjNotify());
			if(Util.equals(rateAdjNotify, "2")){
				result.set("rateAdjNotify",prop_cls1220m04.getProperty("C122M01A.rateAdjNotify.2"));
			}else if(Util.equals(rateAdjNotify, "3")){
				result.set("rateAdjNotify",prop_cls1220m04.getProperty("C122M01A.rateAdjNotify.3"));
			}else{
				result.set("rateAdjNotify","");
			}

			
			//信貸申請書面，未出現分行清單=> 但日後可能會改派案件
			String orgBrId = Util.trim(meta.getOrgBrId());
			result.set("orgBrName", Util.isNotEmpty(orgBrId)?branchService.getBranchName(orgBrId):"");
			if(true){
				Map<String, String> cache_map = new HashMap<String, String>();
				result.set("loanBrNo", service.build_loanBrNo(cache_map, meta));
				result.set("payrollTransfersBrNo", service.build_payrollTransfersBrNo(cache_map, meta));
			}
			
			if(ploan_obj!=null && ploan_obj.getBasicInfo()!=null){
				result.set("ploan_basicInfo_avgTransactionAmt", Util.trim(ploan_obj.getBasicInfo().getAvgTransactionAmt()));
				
				String ploan_basicInfo_serviceAssociateDeptCode = Util.trim(ploan_obj.getBasicInfo().getServiceAssociateDeptCode());
				String ploan_basicInfo_relationWithBorrower = Util.trim(ploan_obj.getBasicInfo().getRelationWithBorrower());
				String ploan_basicInfo_liveWithBorrower = Util.trim(ploan_obj.getBasicInfo().getLiveWithBorrower());
				String ploan_basicInfo_guarantyReason = Util.trim(ploan_obj.getBasicInfo().getGuarantyReason());
				String ploan_basicInfo_otherGuarantyReason = Util.trim(ploan_obj.getBasicInfo().getOtherGuarantyReason());
				result.set("ploan_basicInfo_serviceAssociateDeptCode", service.getDesc_ploan_basicInfo_serviceAssociateDeptCode(ploan_basicInfo_serviceAssociateDeptCode));
				result.set("ploan_basicInfo_serviceAssociateCode", Util.trim(meta.getMarketingStaff())); //==ploan_obj.getBasicInfo().getServiceAssociateCode()
				result.set("ploan_basicInfo_relationWithBorrower", ploan_basicInfo_relationWithBorrower);
				result.set("ploan_basicInfo_liveWithBorrower", ploan_basicInfo_liveWithBorrower);
				result.set("ploan_basicInfo_guarantyReason", ploan_basicInfo_guarantyReason);
				result.set("ploan_basicInfo_otherGuarantyReason", ploan_basicInfo_otherGuarantyReason);
			}
			if(ploan_obj!=null && ploan_obj.getLoanInfo()!=null){
				result.set("ploan_loanInfo_notificationMethod", Util.trim(ploan_obj.getLoanInfo().getNotificationMethod()));
			}
			
			//案件狀態
			String docstatus = Util.trim(meta.getDocStatus());
			Map<String, String> _DocStatusNewDescMap = service.get_DocStatusNewDescMap();
			result.set("docStatusDesc", _DocStatusNewDescMap.get(docstatus));
			
			//借款年限-月數
			String month =  Util.trim(meta.getMaturityM());
			if(Util.equals(month, "")){
				result.set("maturityM", "0");
			}
			//補件通知次數
			String message = "";
			if(Util.equals(meta.getDocStatus(), UtilConstants.C122_DocStatus.補件通知)){
				List<C122S01F> c122s01fList = service.findC122S01F_A02(meta.getMainId(), UtilConstants.C122_DocStatus.補件通知);
				message = prop_cls1220m04.getProperty("label.notice.01") + String.valueOf(c122s01fList.size()) + prop_cls1220m04.getProperty("label.notice.02");
			}
			
			//不承做原因
//			String count_A02 = "";
			if(Util.equals(meta.getDocStatus(), UtilConstants.C122_DocStatus.不承作)){
				List<C122S01G> c122s01gList = service.findC122S01G(meta.getMainId(), UtilConstants.C122_DocStatus.不承作);
				if(c122s01gList !=null && c122s01gList.size()>0){
					for(int i=0;i<c122s01gList.size();i++){
						C122S01G c122s01g = c122s01gList.get(i);
						String seqno = String.valueOf(i+1);
						message = message + seqno + "." + c122s01g.getCodeValue() + "-" + c122s01g.getCodeDesc() + "<br/>";
					}
				}
			}
			//取消原因
			if(Util.equals(meta.getDocStatus(), UtilConstants.C122_DocStatus.取消)){
				List<C122S01G> c122s01gList = service.findC122S01G(meta.getMainId(), UtilConstants.C122_DocStatus.取消);
				if(c122s01gList !=null && c122s01gList.size()>0){
					for(int i=0;i<c122s01gList.size();i++){
						C122S01G c122s01g = c122s01gList.get(i);
						String seqno = String.valueOf(i+1);
						message = message + seqno + "." + c122s01g.getCodeValue() + "-" + c122s01g.getCodeDesc() + "<br/>";
					}
				}
			}
			
			result.set("message", message);

			//J-113-0200 web eLoan公教好想貸
			CapAjaxFormResult usePlanItem = clsService
					.findByCodeTypeWithoutDesc2WithOrderBy("ploan_plan","usePlan",true);
			if (usePlanItem == null) {
				usePlanItem = new CapAjaxFormResult();
			}
			result.set("usePlanItem", usePlanItem);
			if(Util.isNotEmpty(meta.getPloanPlan())){
				CodeType c = codetypeService.findByCodeTypeAndCodeValue("ploan_plan",meta.getPloanPlan());
				List<CodeType> keyPlan = codetypeService.findByCodeTypeAndCodeDescs("ploan_plan", Util.trim(c.getCodeDesc()), "usePlan",
						null, LMSUtil.getLocale().toString());

				for (CodeType cc:keyPlan) {
					//自動比對同行銷方案名稱，帶出主要行銷專案代碼
					if (meta.getUsePlan()==null) {
						result.set("usePlan", cc.getCodeValue());
					}
				}
				//調整行銷方案有，但使用專案沒有也要帶預設值
				if (keyPlan.size()==0) {
					if (meta.getUsePlan()==null) {
						result.set("usePlan", "N");
					}
				}
			}
			else{
				//如果沒有行銷方案，且使用專案也是null就給預設值
				if (meta.getUsePlan()==null) {
					result.set("usePlan", "N");
				}
			}
			
			
			break;
		case 2:
			C120S01A c120s01a = service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01a==null){
				c120s01a = new C120S01A(); 
			}
			LMSUtil.addMetaToResult(result, c120s01a, new String[] {"birthday", "edu", "marry", "child", "mTel", "email"
					, "coCity", "coZip", "coAddr", "fCity", "coTarget", "fZip", "fAddr", "fTarget", "houseStatus"});
			result.set("marry", ClsUtil.convert_nb_to_eloan_marry(c120s01a));
			if(Util.equals(IncomType, "1")){
				result.set("ploan_basicInfo_homePhone", Util.trim(c120s01a.getCoTel()));
			}else{
				if(ploan_obj!=null && ploan_obj.getBasicInfo()!=null){
					String homePhone = Util.trim(ploan_obj.getBasicInfo().getHomePhone());
					if(Util.isEmpty(homePhone)){
						// homePhone = prop_cls1220m04.getProperty("ploanObj.homePhone.whenEmpty");
					}
					result.set("ploan_basicInfo_homePhone", homePhone);
				}
			}
			
			result.set("incomType", IncomType);
			break;
		case 3:
			C120S01B c120s01b = service.findC120S01B(meta.getMainId(), meta.getCustId(), meta.getDupNo());
			if(c120s01b==null){
				c120s01b = new C120S01B(); 
			}
			LMSUtil.addMetaToResult(result, c120s01b, new String[]{  "juId", "comName"
					, "comCity", "comZip", "comAddr", "comTarget" 
					, "comTel"
					, "jobType1", "jobType2"
					, "seniority", "payAmt", "othAmt", "snrM"	});
			if(ploan_obj!=null && ploan_obj.getBasicInfo()!=null){
				result.set("ploan_basicInfo_titleType", Util.trim(ploan_obj.getBasicInfo().getTitleType()));	
			}
			result.set("snrY", ClsUtility.get_inject_snrY(c120s01b.getSeniority()));
			result.set("incomType", Util.trim(meta.getIncomType()));
			break;
		case 4: //進件管理那一頁
			C122M01F c122m01f = service.findC122M01F(meta.getMainId());
			LMSUtil.addMetaToResult(result, c122m01f, new String[]{  "introduceSrc", "megaEmpNo", "agntNo", "agntChain", "dealContractNo", "megaCode"
					, "subUnitNo", "subEmpNo", "subEmpNm", "introCustId", "introDupNo", "introCustName", "batchCodeSbr"
					, "introBuildDerName", "introBuildCaseName" 
					, "laaName", "laaMtel", "laaYear", "laaWord", "laaNo", "laaOfficeId", "laaOffice", "laaDesc"
					, "estFlag", "estAddressCity", "estAddressArea", "estAddressVillage", "estAddressStreet", "estAddressSection", "estAddressLane"
					, "estAddressAlley", "estAddressNo", "estAddressFloor", "estAddressLastNo", "estAddressRoom", "estUnitName", "evaMegaEmpNo", "evaMegaEmpName"
					});
			
			String IncomTypeStr = "";
			if(Util.equals(IncomType, "1")){ //人工進件
				IncomTypeStr = prop_cls1220s06.getProperty("C122M01A.IncomType.00");
			}else{//線上進件
				IncomTypeStr = prop_cls1220s06.getProperty("C122M01A.IncomType.01");
			}
			//青創線上進件 若[引介來源]及[有無擔保品]沒有值，預設顯示[無]
			if(Util.equals(IncomType, "2") && 
					(Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind()) || 
							Util.equals(UtilConstants.C122_ApplyKind.J, meta.getApplyKind()))){
				if(Util.isNotEmpty(c122m01f)){
					if(Util.isEmpty(c122m01f.getIntroduceSrc())){
						result.set("introduceSrc", "N");//[引介來源]
					}
					if(Util.isEmpty(c122m01f.getEstFlag())){
						result.set("estFlag", "N");//[有無擔保品]
					}
				}
			}
			
			result.set("IncomTypeStr", IncomTypeStr);
			result.set("incomType", Util.trim(meta.getIncomType()));
			break;
		case 5: //青創
			C122S01C c122s01c=service.getC122S01C(meta.getMainId());
			result.add(defaultResult_c122m01a(meta, result));
			result.add(defaultResult_c122s01c(c122s01c, meta.getApplyKind(), result));
			break;
		default:
			break;			
		}		
		
		return defaultResult( params, parent_meta, meta, result);
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C122M01A parent_meta, C122M01A meta,
			CapAjaxFormResult result) {		
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		result.set("titInfo", getTitInfo(parent_meta, meta));
		result.set("ploanCaseId", Util.trim(meta.getPloanCaseId()));
		result.set("applyKind", Util.trim(meta.getApplyKind()));
		return result;
	}
	
	private String getTitInfo(C122M01A parent_meta, C122M01A meta) {
		StringBuffer title = new StringBuffer();
		if(parent_meta==null){
			title.append(CapString.trimNull(meta.getCustId()));
			title.append(' ');
			title.append(CapString.trimNull(meta.getDupNo()));
			title.append(' ');
			title.append(CapString.trimNull(meta.getCustName()));
		}else{
			title.append(CapString.trimNull(parent_meta.getCustId()));
			title.append(' ');
			title.append(CapString.trimNull(parent_meta.getDupNo()));
			title.append(' ');
			title.append(CapString.trimNull(parent_meta.getCustName()));
			title.append("（").append(prop_cls1220m04.getProperty("C122M01A.ploanCaseNo")).append("：").append(parent_meta.getPloanCaseId()).append("）");
			title.append(prop_cls1220m04.getProperty("label.ploanRelateCase")).append(" ");
			title.append(CapString.trimNull(meta.getCustId()));
			title.append(' ');
			title.append(CapString.trimNull(meta.getDupNo()));
			title.append(' ');
			title.append(CapString.trimNull(meta.getCustName()));
		}
		
		//if(Util.equals(ClsConstants.C122M01A_IsClosed.X, meta.isClosed())){  ==> isClosed() 回傳 boolean
		if(Util.equals(ClsConstants.C122M01A_StatFlag.已作廢, meta.getStatFlag())){
			title.append("(已作廢)");
		}
		return title.toString();
	}
	
	private IResult defaultResult_c122m01a(C122M01A meta, CapAjaxFormResult result) throws CapException {
		if (meta == null) {
			return result;
		}
		for (Field field : CapBeanUtil.getField(C122M01A.class, true)) {
			if (field.getType() == String.class
					|| field.getType() == BigDecimal.class
					|| field.getType() == Timestamp.class
					|| field.getType() == Date.class) {
				if (!"SPLIT".equals(field.getName())) {
					String fieldName = field.getName();
					String fieldVal = null;
					Object o = meta.get(fieldName);
					if (o != null) {
						if (field.getType() == BigDecimal.class) {
							BigDecimal b = (BigDecimal) o;
							fieldVal = b.stripTrailingZeros().toPlainString();
						} else if (field.getType() == Date.class) {
							Date d = (Date) o;
							fieldVal = CapDate.formatDate(d, "yyyy-MM-dd");
						} else {
							fieldVal = CapString.trimNull(o);
						}
						result.set(fieldName, fieldVal);
					}
				}
			}
		}
		return result;
	}
	
	private IResult defaultResult_c122s01c(C122S01C c122s01c, String applyKind, CapAjaxFormResult result) throws CapException {
		if (c122s01c == null) {
			c122s01c = new C122S01C();
			c122s01c.setApply_type(Util.equals(UtilConstants.C122_ApplyKind.I, applyKind) ? "1" : "2" );
		}
		for (Field field : CapBeanUtil.getField(C122S01C.class, true)) {
			if (field.getType() == String.class
					|| field.getType() == BigDecimal.class
					|| field.getType() == Timestamp.class
					|| field.getType() == Date.class) {
				if (!"SPLIT".equals(field.getName())) {
					String fieldName = field.getName();
					String fieldVal = null;
					
					Object o = c122s01c.get(fieldName);
					if (o != null) {
						if (field.getType() == BigDecimal.class) {
							BigDecimal b = (BigDecimal) o;
							fieldVal = b.stripTrailingZeros().toPlainString();
						} else if (field.getType() == Date.class) {
							Date d = (Date) o;
							fieldVal = CapDate.formatDate(d, "yyyy-MM-dd");
						} else {
							fieldVal = CapString.trimNull(o);
						}
						result.set(fieldName, fieldVal);
					}
				}
			}
		}
		result.set("n_cnumber2", CapString.trimNull(c122s01c.getN_cnumber()));//統一編號-控制畫面顯示用
		result.set("e3_4_", CapString.trimNull(c122s01c.getE3_4()));//營業毛利-控制畫面顯示用
		result.set("e3_6_", CapString.trimNull(c122s01c.getE3_6()));//營業淨利-控制畫面顯示用
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}
	
	private IResult _saveAction(PageParameters params, String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		boolean checkCredit = Util.notEquals("N", params.getString("checkCredit"));//檢查是否已發動徵信(派案、發動徵信或結案時不需檢查)
		//-------------------
		String KEY = "saveOkFlag";
		StringBuilder IncompleteMsg = new StringBuilder();
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);		
		String page = params.getString(EloanConstants.PAGE);
		C122M01A meta = null;
		try{
			meta = service.getC122M01A_byOid(mainOid);
			String IncomType = meta.getIncomType();
			
			C122M01F c122m01f = service.findC122M01F(meta.getMainId());
			if(c122m01f == null){ //若無對應的C122M01F，先產出一個空檔
				c122m01f = new C122M01F();
				c122m01f.setMainId(meta.getMainId());
				c122m01f.setCustId(meta.getCustId());
				c122m01f.setDupNo(meta.getDupNo());
				c122m01f.setCustName(meta.getCustName());
				c122m01f.setOwnBrId(meta.getOwnBrId());
				if(Util.equals(IncomType, "2") && 
						(Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind()) || 
								Util.equals(UtilConstants.C122_ApplyKind.J, meta.getApplyKind()))){
					//若為青創線上進件-[引介來源]及[有無擔保品]，預設[無]
					c122m01f.setIntroduceSrc("N");
					c122m01f.setEstFlag("N");
				}
				service.save(c122m01f);		
			}
			
			if ("01".equals(page)) {
				CapBeanUtil.map2Bean(params, meta, new String[] {"notifyMemo", "statFlag","usePlan"
				});		
				
			}
			
			if ("02".equals(page)) {
				if(Util.equals("1",IncomType)){ //基本只有人工進件的會給更新，這邊還是做個判斷
					C120S01A c120s01a = new C120S01A();
					c120s01a = service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
					if(c120s01a==null){
						c120s01a = new C120S01A(); 
					}
					CapBeanUtil.map2Bean(params, c120s01a, new String[] {"birthday", "edu", "marry", "child", "mTel", "email"
							, "coCity", "coZip", "coAddr", "fCity", "coTarget", "fZip", "fAddr", "fTarget", "houseStatus"});
					
					c120s01a.setCoTel(Util.trim(params.getString("ploan_basicInfo_homePhone"))); //住宅電話寫到通訊電話欄位
					service.save(c120s01a);	
					
					if(Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind()) ||
							Util.equals(UtilConstants.C122_ApplyKind.J, meta.getApplyKind())){
						//青創案件要更新至青創表單
						C122S01C c122s01c = service.getC122S01C(meta.getMainId());
						if(c122s01c == null){
							c122s01c = genC122s01c(meta);
						}
						CopyC120s01aToC122s01c(c120s01a, c122s01c);
						service.save(c122s01c);
					}
				}		
			}
			if ("03".equals(page)) {
				if(Util.equals("1",IncomType)){ //基本只有人工進件的會給更新，這邊還是做個判斷
					C120S01B c120s01b = service.findC120S01B(meta.getMainId(), meta.getCustId(), meta.getDupNo());
					if(c120s01b==null){
						c120s01b = new C120S01B(); 
					}
					CapBeanUtil.map2Bean(params, c120s01b, new String[] {"juId", "comName", "comCity", "comZip", "comAddr", "comTarget" 
							, "comTel", "jobType1", "jobType2", "seniority", "payAmt", "othAmt", "snrM"});	
					
					//換算年資
					int snrM = 0;
					int snrY = 0;
					if(Util.notEquals(Util.trim(params.getString("snrM")), "")) snrM = Integer.parseInt(params.getString("snrM"));
					if(Util.notEquals(Util.trim(params.getString("snrY")), "")) snrY = Integer.parseInt(params.getString("snrY"));
					BigDecimal seniority = BigDecimal.ZERO;
					seniority = ClsUtility.seniorityYM_encode(snrY,snrM);
					c120s01b.setSeniority(seniority);
					
					if(Util.equals(Util.trim(c120s01b.getJobType2()), "null")){ //前台這個下拉選單，沒有set到會回傳文字的null
						c120s01b.setJobType2("");
					}
					service.save(c120s01b);	
					
					if(Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind()) ||
							Util.equals(UtilConstants.C122_ApplyKind.J, meta.getApplyKind())){
						//青創案件要更新至青創表單
						C122S01C c122s01c = service.getC122S01C(meta.getMainId());
						if(c122s01c == null){
							c122s01c = genC122s01c(meta);
						}
						CopyC120s01bToC122s01c(c120s01b, c122s01c);
						service.save(c122s01c);
					}
				}		
			}
			
			if ("04".equals(page)) { //進件管理頁面-儲存這邊安全起見綁一下flag存
				
				CapBeanUtil.map2Bean(params, c122m01f, new String[] {"introduceSrc", "megaEmpNo", "agntNo", "agntChain", "dealContractNo", "megaCode"
						, "subUnitNo", "subEmpNo", "subEmpNm", "introCustId", "introDupNo", "introCustName", "batchCodeSbr", "introBuildDerName", "introBuildCaseName" 
						, "laaName", "laaMtel", "laaYear", "laaWord", "laaNo", "laaOfficeId", "laaOffice", "laaDesc"
						, "estFlag", "estAddressCity", "estAddressArea", "estAddressVillage", "estAddressStreet", "estAddressSection", "estAddressLane"
						, "estAddressAlley", "estAddressNo", "estAddressFloor", "estAddressLastNo", "estAddressRoom", "estUnitName", "evaMegaEmpNo", "evaMegaEmpName"
				});	
				
				//組合擔保品座落位置
				String estAddress = "";
				String estFlag = Util.trim(params.getString("estFlag"));
				if(Util.equals(estFlag, "4")){
					estAddress = cls1220m10formhandler.collateralAddrInfo(params);
				}
				c122m01f.setEstAddress(estAddress);
				
				if(Util.equals(Util.trim(c122m01f.getSubUnitNo()), "null")){ //前台這個下拉選單，沒有set到會回傳文字的null
					c122m01f.setSubUnitNo("");
				}
				if(Util.equals(Util.trim(c122m01f.getEstAddressArea()), "null")){
					c122m01f.setEstAddressArea("");
				}
				// J-112-0006 地政士重複派案註記，信貸案不需檢查，直接壓空值
				c122m01f.setIsSameMegaEmpLaaCase("");
				
				service.save(c122m01f);
			}
			
			if ("05".equals(page)) {//青創
				String alertMsg = "";
				//處理資料
				// for 六、本次申請之貸款用途與金額-週轉性支出可選多筆 (一百萬以下)
				if(Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind())){
					String[] a3_7 = params.getStringArray("a3_7");
					if (a3_7 != null) {
						params.put("a3_7", StringUtils.join(a3_7, ","));
					}
				}
				
				//線上/線下皆可更新custName(在主要TABLE)
				CapBeanUtil.map2Bean(params, meta, new String[] {"custName"});
				C122S01C c122s01c = service.getC122S01C(meta.getMainId());
				if(c122s01c==null){
					c122s01c = genC122s01c(meta);
				}
				CapBeanUtil.map2Bean(params, c122s01c, new String[] {
						"n_cnumber","cname","n_cdate","ctype","ctype_ctxt","a0","a1","bank1","bank2"
						,"a1_1_","a1_ctxt2","a2","a2_1","a2_2","a2_3","a3","a3_1","a3_2","a3_3","a3_4"
						,"a3_5","a3_6","a3_7","a3_8","a3_9","a3_10","a3_11","a3_12","bday","tel","email"
						,"elevel","citya","cityareaa","address","cform","cityb","cityareab","address2"
						,"enjob","enyear","eninput","marital","matename","enselect1","enselect2","input2"
						,"input3","input4","infsl","enbank1","enbank2","enaccount","sname1","sjob1"
						,"syear1","sname2","sjob2","syear2","sname3","sjob3","syear3","sname4","sjob4"
						,"syear4","class_ctxt","class_unit","class_hour","class2_ctxt","class2_unit"
						,"class2_hour","class3_ctxt","class3_unit","class3_hour","class4_unit","class4_ctxt"
						,"class4_hour","class5_unit","class5_ctxt","class5_hour","class6_unit","class6_ctxt"
						,"class6_hour","city","cityarea","n_address","n_tel","city2","cityarea2","n_address2"
						,"n_phone","city3","cityarea3","n_addressctxt","n_fax","n_ctxt","q1","q2","q3","q4"
						,"q5","q6","q7","q8","q9","q10","q11","q12","q13","q14","q15","q16","q17","q18","q19"
						,"q20","agree1","agree2","a1_1","a1_2","a1_3","a1_5","mateid","house","c1","c2","c3"
						,"d1_1","d1_2","d1_3","d1_4","d1_5","d1_6","d1_7","d1_8","d1_9","d1_10","d1_11","d1_12"
						,"d1_13","d1_14","d1_15","d1_16","d1_17","d1_18","d1_19","d1_20","d1_subtotal","d2_1"
						,"d2_2","d2_3","d2_4","d2_5","d2_6","d2_7","d2_8","d2_9","d2_10","d2_11","d2_12","d2_13"
						,"d2_14","d2_15","d2_16","d2_17","d2_18","d2_19","d2_20","d2_subtotal","total","e1_1"
						,"e1_2","e1_3","e1_4","e2_1","e2_2","e2_3","e2_4","e2_5","e2_6","e3_1","e3_2","e3_3"
						,"e3_4","e3_5","e3_6","e3_7","e3_8","e3_9","e3_10","e3_11"});
				
				if(c122s01c.getN_cdate()!=null){
					Calendar fiveYears = Calendar.getInstance();
					fiveYears.add(Calendar.YEAR, -5);
					Calendar reqDate = Calendar.getInstance();
					reqDate.setTime(c122s01c.getN_cdate());
					if(fiveYears.after(reqDate)){//設立日期不得大於五年!
						alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.04") + "<br/>";
					}
				}
				if(c122s01c.getBday()!=null){
					Calendar Yearsold20 = Calendar.getInstance();
					Yearsold20.add(Calendar.YEAR, -18);  //J-111-0626 配合民法修正調整申辦年齡控管為18歲
					Calendar Yearsold45 = Calendar.getInstance();
					Yearsold45.add(Calendar.YEAR, -45);
					Calendar reqDate = Calendar.getInstance();
					reqDate.setTime(c122s01c.getBday());
					if(reqDate.after(Yearsold20) || reqDate.before(Yearsold45)){//Message.dataCheck.05 = 借款人年齡需介於20~45歲!
						//J-111-0626 配合民法修正調整申辦年齡控管為18歲  Message.dataCheck.05_N = 借款人年齡需介於20~45歲!
						alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.05_N") + "<br/>";
						
					}
				}
				
				if (Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind())) {//一百萬以下-申貸金額不得大於100萬
	                if ((Util.isNotEmpty(c122s01c.getA3_1()) &&
	                		c122s01c.getA3_1().compareTo(new BigDecimal(1000000)) == 1) || //準備金及開辦費用
	                	(Util.isNotEmpty(c122s01c.getA3_4()) &&
	    	                c122s01c.getA3_4().compareTo(new BigDecimal(1000000)) == 1)	|| //週轉性支出
	    	            (Util.isNotEmpty(c122s01c.getA3_9()) &&
	    	                c122s01c.getA3_9().compareTo(new BigDecimal(1000000)) == 1) ) {//資本性支出
	                	alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.10") + "<br/>";//申貸金額不得大於100萬
	                }
	            }
				
				if (Util.equals(UtilConstants.C122_ApplyKind.J, meta.getApplyKind())) {//一百萬以上
	                if (Util.isNotEmpty(c122s01c.getA3_1()) &&
	                		c122s01c.getA3_1().compareTo(new BigDecimal(2000000)) == 1) {//準備金及開辦費用不得大於200萬
	                	alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.06") + "<br/>";
	                }
	                if (Util.isNotEmpty(c122s01c.getA3_4()) &&
	                		c122s01c.getA3_4().compareTo(new BigDecimal(4000000)) == 1) {//週轉性支出不得大於400萬
	                	alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.07") + "<br/>";
	                }
	                if (Util.isNotEmpty(c122s01c.getA3_9()) &&
	                		c122s01c.getA3_9().compareTo(new BigDecimal(12000000)) == 1) {//資本性支出不得大於1200萬
	                	alertMsg=alertMsg+ prop_cls1220m04.getProperty("Message.dataCheck.08");
	                }
	            }
				
				//青創線下案件要同步更新其他頁籤的資料
				if(Util.equals("1",IncomType)){//線下案件
					c122m01f = service.findC122M01F(meta.getMainId());
					C120S01A c120s01a = service.findC120S01A(meta.getMainId(), meta.getCustId(), meta.getDupNo());
					C120S01B c120s01b = service.findC120S01B(meta.getMainId(), meta.getCustId(), meta.getDupNo());
					if(c120s01a!=null){//線下案件要同步更新[基本資料]頁籤相關欄位
						CopyC122s01cToC120s01a(c122s01c,c120s01a);
						service.save(c120s01a);
					}
					if(c120s01b!=null){//線下案件要同步更新[服務單位]頁籤相關欄位
						CopyC122s01cToC120s01b(c122s01c, c120s01b);
						service.save(c120s01b);
					}
				}
				service.save(c122s01c);//青創
				
				if(!Util.equals(alertMsg, "")){
					result.set("alertMsg", alertMsg);
				}
			}
			
			//J-112-0006  配合開放待派案也可以看到[徵信]、[補件通知]的button，在儲存時增加檢核
			String docStatus = Util.trim(meta.getDocStatus());
			if(docStatus.startsWith("A") && checkCredit){//為[進件作業]類別且不為派案、發動徵信或結案時 就檢核
				
				List<C122S01H> c122s01hList = service.findC122S01H(meta.getMainId(),
						UtilConstants.C122s01h_flowId.借保人資料);
				if (c122s01hList.size() == 0 ) { //資料不存在 
					IncompleteMsg.append(
							prop_cls1220m04.getProperty("Message.dataCheck.14"));//尚未執行進件管理之徵信作業
				}
			}
			
			if(Util.isNotEmpty(IncompleteMsg)){
				result.set("IncompleteMsg", IncompleteMsg.toString());
			}
			
			String statFlag = Util.trim(meta.getStatFlag());
			if(Util.isNotEmpty(statFlag) && !CrsUtil.inCollection(statFlag, new String[]{"0", "1", "2", "5"})){
				meta.setApplyStatus(UtilConstants.C122_ApplyStatus.不承做);
			}
			
			//~~~			
			service.save(meta);	
			result.set(KEY, true);
			
		}catch(Exception e){
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}	
		
		result.add(query(params));
		
		return result;
	}
	
	private C122S01C genC122s01c(C122M01A meta){
		C122S01C c122s01c=new C122S01C();
		c122s01c.setMainId(meta.getMainId());
		
		if(Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind())){//一百萬以下
			c122s01c.setApply_type("1");
		}
		if(Util.equals(UtilConstants.C122_ApplyKind.J, meta.getApplyKind())){//一百萬以上
			c122s01c.setApply_type("2");
		}
		return c122s01c;
	}
	
	private void CopyC120s01aToC122s01c(C120S01A c120s01a, C122S01C c122s01c){
		c122s01c.setBday(c120s01a.getBirthday());
		String edu="";//學歷
		if(Util.isNotEmpty(c120s01a.getEdu())){
				int eduInt=Integer.parseInt(c120s01a.getEdu());
				switch (eduInt){//轉換為[青創]頁籤的代碼
					case 1://國小
					case 2://國中
						edu= "1";//國中以下
						break;
					case 3:
						edu= "2";
						break;
					case 4:
						edu= "3";
						break;
					case 5:
						edu= "4";
						break;
					case 6:
						edu= "5";
						break;
					case 7:
						edu= "6";
						break;
				}
			
		}
		c122s01c.setElevel(edu);
		c122s01c.setTel(CapString.trimNull(c120s01a.getMTel()));
		c122s01c.setEmail(CapString.trimNull(c120s01a.getEmail()));
		c122s01c.setCitya(CapString.trimNull(c120s01a.getFCity())); //戶籍地址-縣市
		c122s01c.setCityareaa(CapString.trimNull(c120s01a.getFZip()));//戶籍地址-區市鄉鎮
		c122s01c.setAddress(CapString.trimNull(c120s01a.getFAddr()));//戶籍地址-地址
		c122s01c.setCityb(CapString.trimNull(c120s01a.getCoCity()));//通訊地址-縣市
		c122s01c.setCityareab(CapString.trimNull(c120s01a.getCoZip()));//通訊地址-區市鄉鎮
		c122s01c.setAddress2(CapString.trimNull(c120s01a.getCoAddr()));//通訊地址-地址
		if(Util.equals(c120s01a.getFCity(), c120s01a.getCoCity()) &&
				Util.equals(c120s01a.getFZip(), c120s01a.getCoZip())&&
				Util.equals(c120s01a.getFAddr(), c120s01a.getCoAddr())){
			c122s01c.setCform("Y");//同戶籍(通訊地址與戶籍地址相同)
		}else{
			c122s01c.setCform("");
		}
	}
	
	private void CopyC120s01bToC122s01c(C120S01B c120s01b, C122S01C c122s01c){
		c122s01c.setN_cnumber(CapString.trimNull(c120s01b.getJuId()));
		c122s01c.setCname(CapString.trimNull(c120s01b.getComName()));
		c122s01c.setCity(CapString.trimNull(c120s01b.getComCity()));
		c122s01c.setCityarea(CapString.trimNull(c120s01b.getComZip()));
		c122s01c.setN_address(CapString.trimNull(c120s01b.getComAddr()));
		c122s01c.setN_tel(CapString.trimNull(c120s01b.getComTel()));
	}
	
	private void CopyC122s01cToC120s01a(C122S01C c122s01c, C120S01A c120s01a)throws CapException {
		CodeTypeFormatter cityFormat = new CodeTypeFormatter(
				codetypeService, "counties", CodeTypeFormatter.ShowTypeEnum.Desc);

		try {
			String eLevel = "";//學歷(青創-教育程度)
			if (Util.isNotEmpty(c122s01c.getElevel())){
				int eLevelInt=Integer.parseInt(c122s01c.getElevel());
				switch (eLevelInt){//轉換為[基本資料]頁籤的代碼
					case 1://國中/小
						eLevel= "02";//國中(一律轉為國中)
						break;
					case 2://高中職
						eLevel= "03";
						break;
					case 3://專科
						eLevel= "04";
						break;
					case 4://大學
						eLevel= "05";
						break;
					case 5://碩士
						eLevel= "06";
						break;
					case 6://博士
						eLevel= "07";
						break;
				}
			}
			StringBuilder addr1 = new StringBuilder();//戶籍地址-全部文字
			String citya = cityFormat.reformat(CapString.trimNull(c122s01c.getCitya()));
			String cityAreaa = new CodeTypeFormatter(codetypeService,
					"counties" + CapString.trimNull(c122s01c.getCitya()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
					.getCityareaa()));
			addr1.append(citya).append(cityAreaa).append(CapString.trimNull(c122s01c.getAddress()));
		
			StringBuilder addr2 = new StringBuilder();//通訊地址
			String cityb = cityFormat.reformat(CapString.trimNull(c122s01c.getCityb()));
			String cityAreab = new CodeTypeFormatter(codetypeService,
					"counties" + CapString.trimNull(c122s01c.getCityb()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
					.getCityareab()));
			addr2.append(cityb).append(cityAreab).append(CapString.trimNull(c122s01c.getAddress2()));
		
			c120s01a.setBirthday(c122s01c.getBday());//生日
			c120s01a.setEdu(eLevel);//學歷
			c120s01a.setMTel(CapString.trimNull(c122s01c.getTel()));//行動電話(青創-連絡電話)
			c120s01a.setEmail(CapString.trimNull(c122s01c.getEmail()));//email
			c120s01a.setFCity(CapString.trimNull(c122s01c.getCitya()));//戶籍地址-縣市
			c120s01a.setFZip(CapString.trimNull(c122s01c.getCityareaa()));//戶籍地址-區市鄉鎮
			c120s01a.setFAddr(CapString.trimNull(c122s01c.getAddress()));//戶籍地址-地址
			c120s01a.setFTarget(addr1.toString());//戶籍地址-全部文字
			c120s01a.setCoCity(CapString.trimNull(c122s01c.getCityb()));//通訊地址-縣市
			c120s01a.setCoZip(CapString.trimNull(c122s01c.getCityareab()));//通訊地址-區市鄉鎮
			c120s01a.setCoAddr(CapString.trimNull(c122s01c.getAddress2()));//通訊地址-地址
			c120s01a.setCoTarget(addr2.toString());//通訊地址-全部文字
		} catch (CapFormatException e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
	}
	
	private void CopyC122s01cToC120s01b(C122S01C c122s01c, C120S01B c120s01b) throws CapException {
		CodeTypeFormatter cityFormat = new CodeTypeFormatter(
				codetypeService, "counties", CodeTypeFormatter.ShowTypeEnum.Desc);
		try{
			StringBuilder busAddr= new StringBuilder();//營業地址
			String city = cityFormat.reformat(CapString.trimNull(c122s01c.getCity()));
			String cityArea = new CodeTypeFormatter(codetypeService,
					"counties" + CapString.trimNull(c122s01c.getCity()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
					.getCityarea()));
			busAddr.append(city).append(cityArea).append(CapString.trimNull(c122s01c.getN_address()));
			c120s01b.setJuId(CapString.trimNull(c122s01c.getN_cnumber()));
			c120s01b.setComName(CapString.trimNull(c122s01c.getCname()));
			c120s01b.setComCity(CapString.trimNull(c122s01c.getCity()));
			c120s01b.setComZip(CapString.trimNull(c122s01c.getCityarea()));
			c120s01b.setComAddr(CapString.trimNull(c122s01c.getN_address()));
			c120s01b.setComTarget(busAddr.toString());//營業地址-全部文字
			c120s01b.setComTel(CapString.trimNull(c122s01c.getN_tel()));
		}catch (CapFormatException e) {
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult showJSON(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		
		String mainId = params.getString(EloanConstants.MAIN_ID);
		C122M01A meta = null;
		try{
			meta = service.getC122M01A_byMainId(mainId);
			
			List<C122M01B> c122m01b_list = service.getC122M01B_byMainId(meta.getMainId());
			for(C122M01B c122m01b : c122m01b_list){
				result.set("c122m01b_"+c122m01b.getItemType()+"_"+c122m01b.getJsonVoClass(), c122m01b.getJsonData());
			}
		}catch(Exception e){
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException(e, getClass());
		}	
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult changeOwnBrId_then_mail(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String newBrNo = Util.trim(params.getString("newBrNo"));
		String memo = Util.trim(params.getString("memo"));
		C122M01A meta = service.getC122M01A_byOid(mainOid);
		if(meta==null){
			throw new CapMessageException("["+mainOid+"]not found", getClass());
		}		
		if(Util.isEmpty(memo)){
			throw new CapMessageException(prop_cls1220v08.getProperty("changeOwnBrId_memo")+"不可空白", getClass());
		}	
		if(Util.equals(meta.getOwnBrId(), newBrNo)){
			throw new CapMessageException("未更新分行別", getClass());
		}
		if(Util.equals(meta.getStatFlag(), "2")){
			//C122M01A.statFlag=申貸案件狀態
			//C122M01A.statFlag.applyKindP.2=已核貸
			throw new CapMessageException("不可改派("+prop_cls1220m04.getProperty("C122M01A.statFlag")+"："+prop_cls1220m04.getProperty("C122M01A.statFlag.applyKindP.2")+")", getClass());
		}
		if(LMSUtil.cmpDate(CapDate.addMonth(meta.getApplyTS(), 1), "<", CapDate.getCurrentTimestamp())){
			throw new CapMessageException("不可改派("+prop_cls1220m04.getProperty("C122M01A.applyTS")+"："+TWNDate.toAD(meta.getApplyTS())+")", getClass());			
		}
		
		C122M01C c122m01c = service.changeOwnBrId(meta, newBrNo, memo);
		if(c122m01c!=null){
			service.changeOwnBrId_notifyT1(meta.getPloanCaseId(), meta.getApplyKind(), c122m01c, meta.getCustId(), meta.getCustName());
		}
		
		return result;
	}	
	
	/**
	 * <pre>
	 * 透過分行別查詢 團體消貸可選擇的行銷方案
	 * </pre>
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public IResult getPloanPlanList(PageParameters params) {
		String branch = MegaSSOSecurityContext.getUnitNo();
		CapAjaxFormResult r = new CapAjaxFormResult();
		r.putAll(service.getPloanPlanList(branch));
		return r;
	}
	
}	
