var init120s08 = {
	fhandle : "lms1201formhandler",
	versionDate : "2022-02-22",
	defButton : {
		"close" : function() {
			$.thickbox.close();
		}
	}
};
var API_2022 = {
    /** 明細檔 */
    openDetailBox : function(type, docOid, data) {
		//showRiskAdd,showCompetition

		var verNo = init120s08.versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		var $inputL120s08Box = $("#inputL120s08Box_"+verNo);
        var curr = $L120S08Form.find('#curr').val();
		
		$(".showN").show();
		$("#showTitleSY").show();
		$("#showTitleSN").hide();
		if (curr == "TWD") {
//			$(".showN").show();
//			$("#showTitleSY").show();
//			$("#showTitleSN").hide();
			$("#showMsgTWD").show();
			$("#showMsgUSD").hide();
		} else {
//			$(".showN").hide();
//			$("#showTitleSY").hide();
//			$("#showTitleSN").show();
			$("#showMsgTWD").hide();
			$("#showMsgUSD").show();
		}

		var docOid = "";
		if (data) {
			docOid = data.oid;
		}

		if (type == 'new') {
			$L120S08Form.find('#memo').val(''); 
		}		

		var buttons = {};
		if (!thickboxOptions.readOnly) {
			buttons["calculate"] = function(){
			    if (!API_2022.btnCaculate(init120s08.versionDate)) {
		            CommonAPI.showErrorMessage(i18n.lms1401s03["L120S08A.error16"]);
		        }
			};
			buttons["sure"] = function() {
				var $form = $L120S08Form;

				// 先計算可以先將數字欄位塞0
				if (!API_2022.btnCaculate(init120s08.versionDate)) {
					//L120S08A.error14=申請減碼項目之每項減碼數字不符規定
					CommonAPI.showErrorMessage(i18n.lms1401s03["L120S08A.error14"]);
					return false;
				}

				if (!$form.valid()) {
					return false;
				}

				$.ajax({
					handler : init120s08.fhandle,
					data : {// 把資料轉成json
						formAction : "saveL120s08Data_20220222",
						docOid : docOid,
						mainId : responseJSON.mainId,
						curr : curr,
						versionDate:init120s08.versionDate,
					    verNo:verNo,
						L120S08Form : JSON.stringify($L120S08Form.serializeData())
					}
				}).done(function(data) {
					$.thickbox.close();
					L120s08BoxAPI._triggerMainGrid();
				}); // close ajax
			};

		} else {
			$L120S08Form.readOnlyChilds(true);
			$L120S08Form.find("button").hide();
		}

		buttons["cancel"] = function() {
			$.thickbox.close();
		};

		$inputL120s08Box.thickbox({
			// L120S08A.title2=合理性分析表
			title : i18n.lms1401s03['L120S08A.title2'],
			width : 1100,
			height : 600,
			modal : true,
			readOnly : thickboxOptions.readOnly,
			align : "center",
			i18n : i18n.def,
			valign : "bottom",
			buttons : buttons
		});
	},
	/**
	 * 引進關係戶往來彙總二維表集團評等與貢獻度合計 return true ,false
	 */
	btnApplyGroup : function(versionDate) {

		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		
		var nowDate = new Date();
		var MM = nowDate.getMonth();
		var YY = nowDate.getFullYear();
		var SMM;
		var SYY;
		if (MM == 0) {
			MM = 12;
		}

		if (MM == 12) {
			SMM = MM - 5;
			YY = YY - 1;
			SYY = YY;
		} else if (MM > 5 && MM < 12) {
			SMM = MM - 5;
			SYY = YY;
		} else {
			SMM = MM + 12 - 5;
			SYY = YY - 1;
		}

		var $tL120S08Form1a = $("#tL120S08Form1a_"+verNo);
		$tL120S08Form1a.find("#queryDateE0").val(YY);
		$tL120S08Form1a.find("#queryDateE1").val(MM);

		$("#inputSearch_"+verNo).thickbox({ // 使用選取的內容進行彈窗
			title : i18n.lms1401s03["L120S08A.fieldTitle48"]+"/"+i18n.lms1401s03["L120S08A.fieldTitle49"], // L120S08A.title4=貢獻度查詢期間
			width : 600,
			height : 300,
			modal : true,
			align : 'center',
			valign : 'bottom',
			i18n : i18n.def,
			buttons : {
				"sure" : function() {					 
					if ($tL120S08Form1a.valid()) {
						if ($tL120S08Form1a.find("#queryDateE1").val() < 1
								|| $tL120S08Form1a.find("#queryDateE1").val() > 12) {
							CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error07"]);
							return;
						} else if ($tL120S08Form1a.find("#queryDateE0").val() <= 0) {
							CommonAPI.showMessage(i18n.lms1401s03["L120S08A.error08"]);
							return;
						} else {							
							var selectKind = $tL120S08Form1a.find("#selectKind").val();							
							$.thickbox.close();
							
							var qCustId = $L120S08Form.find("#custId").val();
							var qDupNo = $L120S08Form.find("#dupNo").val();

							$.ajax({
								handler : init120s08.fhandle,
								type : "POST",
								dataType : "json",
								data : {
									formAction : "queryL120S08BDWData_20210501",
									mainId : responseJSON.mainid,
									queryDateE0 : $tL120S08Form1a.find("#queryDateE0").val(),
									queryDateE1 : $tL120S08Form1a.find("#queryDateE1").val(),
									qCustId:qCustId,
									qDupNo:qDupNo,
									kind : selectKind
								}
							}).done(function(obj120) {
								var kindStr = "";
								if(selectKind == "1"){
									kindStr = "P";
								}else{
									kindStr = "G";
								}
								$L120S08Form.find("#oneYearOutLoan"+kindStr+"_dscr").val(obj120.oneYearOutLoan);
								$L120S08Form.find("#oneYearFnD"+kindStr+"_dscr").val(obj120.oneYearFnD);
								$L120S08Form.find("#oneYearWealth"+kindStr+"_dscr").val(obj120.oneYearWealth);
							});
						}
					}
				},
				"cancel" : function() {
					$.thickbox.close();
				}
			}
		});
	},
	/** 計算 */
	btnCaculate : function(versionDate) {
		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);		
		
		if (!$L120S08Form.find("#baseRate1").val() 
				|| !$L120S08Form.find("#baseRate2").val() 
				|| !$L120S08Form.find("#baseRate3").val()) {
			// L120S08A.error12=請先輸入
			// L120S08A.baseRate=基礎放款利利率
			CommonAPI.showMessage(i18n.lms1401s03['L120S08A.error12']
					+ i18n.lms1401s03["L120S08A.baseRate"]);
			return false;
		}
		
		var hasError = false;

		// 檢查有擔無擔是否要計算
        var hasSShort = false;   // 擔保短期
        var hasSLong = false;   // 擔保中長期
        var hasNShort = false;   // 非擔保短期
        var hasNLong = false;   // 非擔保中長期
        $L120S08Form.find(".plus, .minus").find("input[id*='_short_disYearRateS']").each(function() {
            if ($(this).val()) {
            	hasSShort = true;
            }
        });
        $L120S08Form.find(".plus, .minus").find("input[id*='_long_disYearRateS']").each(function() {
            if ($(this).val()) {
            	hasSLong = true;
            }
        });
        $L120S08Form.find(".plus, .minus").find("input[id*='_short_disYearRateN']").each(function() {
            if ($(this).val()) {
            	hasNShort = true;
            }
        });
        $L120S08Form.find(".plus, .minus").find("input[id*='_long_disYearRateN']").each(function() {
            if ($(this).val()) {
            	hasNLong = true;
            }
        });

		// 數值欄位若無值先補0 避免計算錯誤
		$L120S08Form.find(".caculate").find("input:not([id*='_dscr'])").each(function() {
		    var thisId = this.id;
		    if (thisId.match(/_disYearRateS$/)) {   // 擔保
		    	if(hasSShort){
		    		if (thisId.match(/_short_disYearRateS$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}else if(!thisId.match(/_long_disYearRateS$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}
		    	}
		    	if(hasSLong){
		    		if (thisId.match(/_long_disYearRateS$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}else if(!thisId.match(/_short_disYearRateS$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}
		    	}
		    } else if (thisId.match(/_disYearRateN$/)) {    // 非擔保
		    	if(hasNShort){
		    		if (thisId.match(/_short_disYearRateN$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}else if(!thisId.match(/_long_disYearRateN$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}
		    	}
		    	if(hasNLong){
		    		if (thisId.match(/_long_disYearRateN$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}else if(!thisId.match(/_short_disYearRateN$/)){
		    			if ($(this).val()) {
		    			} else {
		    				$(this).val(0);
		    			}
		    		}
		    	}
		    } else {
		        if ($(this).val()) {
                } else {
                    $(this).val(0);
                }
		    }
		});

		var baseRate1 = parseFloat(0);	// 資金成本率
		baseRate1 = parseFloat($L120S08Form.find("#baseRate1").val(), 10);
		var baseRate2 = parseFloat(0);	// 營運成本率
		baseRate2 = parseFloat($L120S08Form.find("#baseRate2").val(), 10);
		var baseRate3 = parseFloat(0);	// 預期損失率
		baseRate3 = parseFloat($L120S08Form.find("#baseRate3").val(), 10);
		
		var baseRate = parseFloat(0);	// 基礎放款利利率		
		baseRate = baseRate1 + baseRate2 + baseRate3;
		
		// =============================加碼=========================================
		var subTotalA_S_short = parseFloat(0);	// 小計(A)-擔保_短期
		var subTotalA_S_long = parseFloat(0);	// 小計(A)-擔保_中長期
		var subTotalA_N_short = parseFloat(0);	// 小計(A)-非擔保_短期
		var subTotalA_N_long = parseFloat(0);	// 小計(A)-非擔保_中長期
		
		
		// 擔保短期
		$L120S08Form.find("#subTotalA_short_disYearRateS").val('');
		if(hasSShort){
			// 排掉有擔中長期的不計算
            $L120S08Form.find(".plus").find("input[id*='_disYearRateS']").not("[id*='_long_disYearRateS']").each(function() {
                var eachTotalA_S = parseFloat(0);
                if ($(this).val()) {
                    eachTotalA_S = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalA_S = parseFloat(0);
                }
                subTotalA_S_short = subTotalA_S_short + eachTotalA_S;
            });
            subTotalA_S_short = baseRate + subTotalA_S_short;
            $L120S08Form.find("#subTotalA_short_disYearRateS").val(subTotalA_S_short.toFixed(5));
		}
		
		// 擔保中長期
		$L120S08Form.find("#subTotalA_long_disYearRateS").val('');
		if(hasSLong){
			// 排掉有擔短期的不計算
            $L120S08Form.find(".plus").find("input[id*='_disYearRateS']").not("[id*='_short_disYearRateS']").each(function() {
                var eachTotalA_S = parseFloat(0);
                if ($(this).val()) {
                    eachTotalA_S = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalA_S = parseFloat(0);
                }
                subTotalA_S_long = subTotalA_S_long + eachTotalA_S;
            });
            subTotalA_S_long = baseRate + subTotalA_S_long;
            $L120S08Form.find("#subTotalA_long_disYearRateS").val(subTotalA_S_long.toFixed(5));
		}
		
		// 非擔保短期
		$L120S08Form.find("#subTotalA_short_disYearRateN").val('');
		if(hasNShort){
			// 排掉無擔中長期的不計算
            $L120S08Form.find(".plus").find("input[id*='_disYearRateN']").not("[id*='_long_disYearRateN']").each(function() {
                var eachTotalA_N = parseFloat(0);
                if ($(this).val()) {
                    eachTotalA_N = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalA_N = parseFloat(0);
                }
                subTotalA_N_short = subTotalA_N_short + eachTotalA_N;
            });
            subTotalA_N_short = baseRate + subTotalA_N_short;
            $L120S08Form.find("#subTotalA_short_disYearRateN").val(subTotalA_N_short.toFixed(5));
		}
		
		// 非擔保中長期
		$L120S08Form.find("#subTotalA_long_disYearRateN").val('');
		if(hasNLong){
			// 排掉無擔短期的不計算
            $L120S08Form.find(".plus").find("input[id*='_disYearRateN']").not("[id*='_short_disYearRateN']").each(function() {
                var eachTotalA_N = parseFloat(0);
                if ($(this).val()) {
                    eachTotalA_N = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalA_N = parseFloat(0);
                }
                subTotalA_N_long = subTotalA_N_long + eachTotalA_N;
            });
            subTotalA_N_long = baseRate + subTotalA_N_long;
            $L120S08Form.find("#subTotalA_long_disYearRateN").val(subTotalA_N_long.toFixed(5));
		}
		
		// =============================減碼=========================================
		var subTotalB_S = parseFloat(0);	// 小計(B)-擔保
		var subTotalB_N = parseFloat(0);	// 小計(B)-非擔保
		
		// 擔保
		$L120S08Form.find("#subTotalB_disYearRateS").val('');
		if(hasSShort || hasSLong){
            $L120S08Form.find(".minus").find("input[id*='_disYearRateS']").each(function() {
                var eachTotalB_S = parseFloat(0);
                if ($(this).val()) {
                    eachTotalB_S = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalB_S = parseFloat(0);
                }
                subTotalB_S = subTotalB_S + eachTotalB_S;
            });
            $L120S08Form.find("#subTotalB_disYearRateS").val(subTotalB_S.toFixed(5));
		}
		
		// 非擔保
		$L120S08Form.find("#subTotalB_disYearRateN").val('');
		if(hasNShort || hasNLong){
            $L120S08Form.find(".minus").find("input[id*='_disYearRateN']").each(function() {
                var eachTotalB_N = parseFloat(0);
                if ($(this).val()) {
                    eachTotalB_N = parseFloat($(this).val(), 10);
                } else {
                    $(this).val(0);
                    eachTotalB_N = parseFloat(0);
                }
                subTotalB_N = subTotalB_N + eachTotalB_N;
            });
            $L120S08Form.find("#subTotalB_disYearRateN").val(subTotalB_N.toFixed(5));
		}

		// =============================本案(A-B)=========================================
		var caseRate_S_short = parseFloat(0);// 有擔短期
		var caseRate_S_long = parseFloat(0);// 有擔中長期
        var caseRate_N_short = parseFloat(0);// 無擔短期
        var caseRate_N_long = parseFloat(0);// 無擔中長期

        $L120S08Form.find("#caseRate_short_disYearRateS").val('');
		if(hasSShort){
			caseRate_S_short = subTotalA_S_short - subTotalB_S;
		    $L120S08Form.find("#caseRate_short_disYearRateS").val(caseRate_S_short.toFixed(5));
		}

		$L120S08Form.find("#caseRate_long_disYearRateS").val('');
		if(hasSLong){
			caseRate_S_long = subTotalA_S_long - subTotalB_S;
		    $L120S08Form.find("#caseRate_long_disYearRateS").val(caseRate_S_long.toFixed(5));
		}
		
		$L120S08Form.find("#caseRate_short_disYearRateN").val('');
		if(hasNShort){
            caseRate_N_short = subTotalA_N_short - subTotalB_N;
            $L120S08Form.find("#caseRate_short_disYearRateN").val(caseRate_N_short.toFixed(5));
		}
		
		$L120S08Form.find("#caseRate_long_disYearRateN").val('');
		if(hasNLong){
            caseRate_N_long = subTotalA_N_long - subTotalB_N;
            $L120S08Form.find("#caseRate_long_disYearRateN").val(caseRate_N_long.toFixed(5));
		}
		// =============================預估=========================================
		var ftpRate_S_short = parseFloat(0);	// FTP-擔保_短期
		ftpRate_S_short = parseFloat($L120S08Form.find("#ftpRate_short_disYearRateS").val(), 10);
		var ftpRate_S_long = parseFloat(0);	// FTP-擔保_中長期
		ftpRate_S_long = parseFloat($L120S08Form.find("#ftpRate_long_disYearRateS").val(), 10);
		/*
		if(isNaN(ftpRate_S) && hasS){   // 非數字 預設0
		    ftpRate_S = parseFloat(0);
		}
		*/
		var ftpRate_N_short = parseFloat(0);	// FTP-非擔保_短期
		ftpRate_N_short = parseFloat($L120S08Form.find("#ftpRate_short_disYearRateN").val(), 10);
		var ftpRate_N_long = parseFloat(0);	// FTP-非擔保_中長期
		ftpRate_N_long = parseFloat($L120S08Form.find("#ftpRate_long_disYearRateN").val(), 10);
		/*
		if(isNaN(ftpRate_N) && hasN){   // 非數字 預設0
            ftpRate_N = parseFloat(0);
        }
        */

		var minusBaseRate2 = parseFloat(0);	// 營運成本率
		minusBaseRate2 = parseFloat($L120S08Form.find("#minusBaseRate2").val(), 10);
		/*
		if(isNaN(minusBaseRate2)){   // 非數字 預設0
            minusBaseRate2 = parseFloat(0);
        }
        */
		
		// 呆帳損失率 = 預期損失率+企業模型評等各等級擬制呆帳損失差異率
		var lostDiffRateS = parseFloat(0);		// 企業模型評等各等級擬制呆帳損失差異率(擔保)
		lostDiffRateS = parseFloat($L120S08Form.find("#lostDiffRate_disYearRateS").val(), 10);
		var lostDiffRateN = parseFloat(0);		// 企業模型評等各等級擬制呆帳損失差異率(無擔保)
		lostDiffRateN = parseFloat($L120S08Form.find("#lostDiffRate_disYearRateN").val(), 10);
		var lostDiffRate = parseFloat(0);
		if(lostDiffRateN == 0 || isNaN(lostDiffRateN)){
		    if(!isNaN(lostDiffRateS)){
			    lostDiffRate = lostDiffRateS;
			}
		} else {
			lostDiffRate = lostDiffRateN;
		}
		var badDebtLostRate = parseFloat(0);
		$L120S08Form.find("#badDebtLostRate").val('');
		if(!isNaN(lostDiffRate) && !isNaN(baseRate3)){
            badDebtLostRate = baseRate3 + lostDiffRate;
            $L120S08Form.find("#badDebtLostRate").val(badDebtLostRate.toFixed(5));
		}

		var estIncomeRate_S_short = parseFloat(0);	// 預估收益率-擔保_短期
		$L120S08Form.find("#estIncomeRate_short_disYearRateS").val('');
		if(hasSShort){
            estIncomeRate_S_short = caseRate_S_short - ftpRate_S_short - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeRate_short_disYearRateS").val(estIncomeRate_S_short.toFixed(5));
		}
		var estIncomeRate_S_long = parseFloat(0);	// 預估收益率-擔保_中長期
		$L120S08Form.find("#estIncomeRate_long_disYearRateS").val('');
		if(hasSLong){
            estIncomeRate_S_long = caseRate_S_long - ftpRate_S_long - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeRate_long_disYearRateS").val(estIncomeRate_S_long.toFixed(5));
		}
		
		var estIncomeRate_N_short = parseFloat(0);	// 預估收益率-非擔保_短期
		$L120S08Form.find("#estIncomeRate_short_disYearRateN").val('');
		if(hasNShort){
            estIncomeRate_N_short = caseRate_N_short - ftpRate_N_short - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeRate_short_disYearRateN").val(estIncomeRate_N_short.toFixed(5));
		}
		var estIncomeRate_N_short = parseFloat(0);	// 預估收益率-非擔保_中長期
		$L120S08Form.find("#estIncomeRate_long_disYearRateN").val('');
		if(hasNLong){
            estIncomeRate_N_long = caseRate_N_long - ftpRate_N_long - minusBaseRate2 - badDebtLostRate;
            $L120S08Form.find("#estIncomeRate_long_disYearRateN").val(estIncomeRate_N_long.toFixed(5));
		}
			
		if (hasError) {
			return false;
		} else {
			return true;
		}
	},
	btnClearWithTerm : function(sType, versionDate, term) {
		var verNo = versionDate.replace(/-/g, "");
		var L120S08FormName = "L120S08Form_"+verNo;
		var $L120S08Form = $("#"+L120S08FormName);
		
		$("form[id='"+L120S08FormName+"'] input[id*='_" + term + "_disYearRate" + sType + "']").each(function() {
			$(this).val('');
		});
		
		// J-110-0498 優化eloan企金授信系統之額度明細表利率合理性分析編輯功能
		// 開舊版起來因為是跑each，沒有符合的span也不會出錯
		$("form[id='"+L120S08FormName+"'] span[id*='_" + term + "_disYearRate" + sType + "_rmk']").each(function() {
			$(this).val('');
		});
	}
};

initDfd.done(function(json) {
    $("#btnApplyRateDscr").click(function() {
        L120s08BoxAPI.btnApplyRateDscr(init120s08.versionDate);
    });
    $("#btnApplyGroup").click(function() {
        API_2022.btnApplyGroup(init120s08.versionDate);
    });
    $("#btnClearS").click(function() {
        L120s08BoxAPI.btnClear('S', init120s08.versionDate);
    });
    $("#btnClearN").click(function() {
        L120s08BoxAPI.btnClear('N', init120s08.versionDate);
    });
    
    $("#btnClearSShort").click(function() {
    	API_2022.btnClearWithTerm('S', init120s08.versionDate, 'short');
    });
    $("#btnClearSLong").click(function() {
    	API_2022.btnClearWithTerm('S', init120s08.versionDate, 'long');
    });
    $("#btnClearNShort").click(function() {
    	API_2022.btnClearWithTerm('N', init120s08.versionDate, 'short');
    });
    $("#btnClearNLong").click(function() {
    	API_2022.btnClearWithTerm('N', init120s08.versionDate, 'long');
    });

    L120s08BoxAPI.gridviewConnomOtherSelect();
    
    $("#ftpRateGrid").iGrid({
        handler: "lms1201gridhandler",//inits.ghandle,
        needPager: false,
        multiselect: false,
        sortname: 'insCdCnm',
        sortorder: 'asc',
        postData : {
        	formAction: "queryFtpRate"
		},
        colModel: [{
            colHeader: i18n.lms1401s03["L120S08A.fieldTitle1"],//"項目",
            name: 'insCdCnm',
            align: "center",
            width: 80,
            sortable: false
        }, {
            colHeader: " ",
            name: 'insRt',
            align: "center",
            width: 20,
            sortable: false
        }]
    });
});

function impFtpRate(type, term) {
    // type： S-擔保   N-非擔保       供後續回壓欄位用
    var verNo = init120s08.versionDate.replace(/-/g, "");
    var L120S08FormName = "L120S08Form_"+verNo;
    var $L120S08Form = $("#"+L120S08FormName);
    var curr = $L120S08Form.find('#curr').val();

    $("#ftpRateGrid").jqGrid("setGridParam", {
        postData: {
        	curr: curr
        },
        search: true
    }).trigger("reloadGrid");
    
    $("#impFtpRateThickBox").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.def['grid_selector'],
        width: 500,
        height: 250,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            	var rowId = $("#ftpRateGrid").getGridParam('selrow');
            	if (rowId) {
            		var data = $("#ftpRateGrid").getRowData(rowId);
            		$L120S08Form.find("#ftpRate_" + term + "_disYearRate" + type).val(data.insRt);
            		$.thickbox.close();
            	} else {
                    CommonAPI.showMessage(i18n.def['grid.selrow']);
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}
