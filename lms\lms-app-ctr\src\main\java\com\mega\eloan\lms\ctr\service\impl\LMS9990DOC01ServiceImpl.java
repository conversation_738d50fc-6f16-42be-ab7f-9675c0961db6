/* 
 * LMS9990DOC01ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.ctr.constants.CtrConstants;
import com.mega.eloan.lms.ctr.pages.LMS9990M01Page;
import com.mega.eloan.lms.ctr.service.LMS9990Service;
import com.mega.eloan.lms.enums.L140M01NEnum;
import com.mega.eloan.lms.enums.L140M01NEnum.RateKindEnum;
import com.mega.eloan.lms.enums.L140M01NEnum.RateTypeEnum;
import com.mega.eloan.lms.enums.L140M01NEnum.SecNoOpEnum;
import com.mega.eloan.lms.mfaloan.service.LNLNF070Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L140S09A;
import com.mega.eloan.lms.model.L140S09B;
import com.mega.eloan.lms.model.L999M01A;
import com.mega.eloan.lms.model.L999M01B;
import com.mega.eloan.lms.model.L999M01C;
import com.mega.eloan.lms.model.L999M01D;
import com.mega.eloan.lms.model.L999S01A;
import com.mega.eloan.lms.model.L999S01B;
import com.mega.eloan.lms.model.L999S02A;
import com.mega.eloan.lms.model.L999S04A;
import com.mega.eloan.lms.model.L999S04B;
import com.mega.eloan.lms.model.L999S07A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產Word Service
 * </pre>
 * 
 * @since 2012/03/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/14,Ice
 *          </ul>
 */
@Service("lms9990doc01service")
public class LMS9990DOC01ServiceImpl extends AbstractFormHandler implements
		FileDownloadService {
	@Resource
	LMS9990Service lms9990Service;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	UserInfoService userInfoService;
	@Resource
	LMSService lmsService;
	
	@Resource
	MisCustdataService misCustdataService;
	
	@Resource
	MisMislnratService misMislnratService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	LNLNF070Service lnlnf070Service;


	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9990DOC01ServiceImpl.class);
	// SHIFT+ENTER效果
	private final static String 換行符號 = "<w:br/>";
	private static final String CODE_UTF_8 = "UTF-8";

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.creatDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			if (baos == null) {
				baos = new ByteArrayOutputStream();
			}
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}

		}
		return null;
	}

	/**
	 * 產生word
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return word
	 */
	public OutputStream creatDoc(PageParameters params) {
		OutputStream outputStream = null;
		String contractType = params.getString("contractType");
		/**
		 * 約據書種類
		 * <p/>
		 * 01.綜合授信契約書<br/>
		 * 02.連帶保證書<br/>
		 * 03.授信約定書<br/>
		 * 04.中長期契約書<br/>
		 * 05.股東債權同意書<br/>
		 * 06.本票授權契約書
		 */
		try {
			if (CtrConstants.ContractType.綜合授信契約書.equals(contractType)) {
				outputStream = this
						.getL999M01AW01(params, contractType);
			} else if (CtrConstants.ContractType.綜合授信契約書_直.equals(contractType)) {
				outputStream = this
						.getL999M01AW01(params, contractType);
			} else if (CtrConstants.ContractType.綜合授信契約書_橫式直
					.equals(contractType)) {
				outputStream = this
						.getL999M01AW01(params, contractType);
			} else if (CtrConstants.ContractType.連帶保證書.equals(contractType)) {
				outputStream = this.getL999M01AW02(params);
			} else if (CtrConstants.ContractType.授信約定書.equals(contractType)) {
				outputStream = this.getL999M01AW03(params);
			} else if (CtrConstants.ContractType.中長期契約書.equals(contractType)) {
				outputStream = this.getL999M01AW04(params);
			} else if (CtrConstants.ContractType.股東債權同意書.equals(contractType)) {
				outputStream = this.getL999M01AW05(params);
			} else if (CtrConstants.ContractType.本票授權契約書.equals(contractType)) {
				outputStream = this.getL999M01AW06(params);
			} else if (CtrConstants.ContractType.開發信用狀約定書.equals(contractType)) {
				outputStream = this.getL999M01AW07(params);
			} else if (CtrConstants.ContractType.應收帳款承購約定書.equals(contractType)) {
				outputStream = this.getL999M01AW08(params);
			} else if(Util.equals(contractType, "W03")){
				outputStream = this.getLMS_W03(params);
			} else if(Util.equals(contractType, "W04")){
				outputStream = this.getLMS_W04(params);
			} else if(Util.equals(contractType, "W01")){
				outputStream = this.getLMS_W01(params);
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return outputStream;
	}

	/**
	 * 取得01.兆豐綜合授信契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW01(PageParameters params,
			String contractType) throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		Map<String, String> yesNoMap = null;
		String content = null;
		L999M01A l999m01a = null;
		L999M01B l999m01bA = null;
		L999M01B l999m01bB = null;
		L999S01A l999s01a = null;
		L999S01B l999s01bA = null;
		L999S01B l999s01bB = null;
		L999S01B l999s01bC = null;
		L999S01B l999s01bD = null;
		L999S01B l999s01bE = null;
		L999S01B l999s01bF = null;
		L999S01B l999s01bG = null;
		L999S01B l999s01bH = null;
		L999M01D l999m01d = null;
		L999M01D l999m01dC = null;
		Map<String, String> currMap = null;
		Map<String, String> unitMap = null;
		try {
			currMap = codeTypeService.findByCodeType("Common_Currcy");
			if (currMap == null) {
				currMap = new LinkedHashMap<String, String>();
			}
			unitMap = codeTypeService.findByCodeType("CurrUnit");
			if (unitMap == null) {
				unitMap = new LinkedHashMap<String, String>();
			}
			yesNoMap = codeTypeService.findByCodeType("Common_YesNo1");
			if (yesNoMap == null)
				yesNoMap = new LinkedHashMap<String, String>();
			// 讀檔

			String DocName = CtrConstants.LMS999XMLFile.綜合授信契約書;

			if (CtrConstants.ContractType.綜合授信契約書.equals(contractType)) {
				DocName = CtrConstants.LMS999XMLFile.綜合授信契約書;
			} else if (CtrConstants.ContractType.綜合授信契約書_直.equals(contractType)) {
				DocName = CtrConstants.LMS999XMLFile.綜合授信契約書_直;
			} else if (CtrConstants.ContractType.綜合授信契約書_橫式直
					.equals(contractType)) {
				DocName = CtrConstants.LMS999XMLFile.綜合授信契約書_橫式直;
			}

			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/ctr/" + DocName);
			// 撈資料
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}
			l999m01bA = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.銀行甲方);
			if (l999m01bA == null) {
				l999m01bA = new L999M01B();
			}

			l999m01bB = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.借款人乙方);
			if (l999m01bB == null) {
				l999m01bB = new L999M01B();
			}
			l999m01d = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.特別條款);
			if (l999m01d == null) {
				l999m01d = new L999M01D();
			}

			l999s01a = lms9990Service.findL999s01aByMainId(mainId);
			/**
			 * 借款種類
			 * <p/>
			 * A.購料借款<br/>
			 * B.外銷借款<br/>
			 * C.營運週轉借款<br/>
			 * D.貼現<br/>
			 * E.透支<br/>
			 * F.委任票據保證<br/>
			 * G.委任票據承兌<br/>
			 * H.委任保證
			 */
			if (l999s01a == null)
				l999s01a = new L999S01A();
			l999s01bA = lms9990Service.findL999s01bByMainIdItemType(mainId,
					CtrConstants.L999S01BItemType.購料借款);
			if (l999s01bA == null)
				l999s01bA = new L999S01B();
			l999s01bB = lms9990Service.findL999s01bByMainIdItemType(mainId,
					CtrConstants.L999S01BItemType.外銷借款);
			if (l999s01bB == null)
				l999s01bB = new L999S01B();
			l999s01bC = lms9990Service.findL999s01bByMainIdItemType(mainId,
					CtrConstants.L999S01BItemType.營運週轉借款);
			if (l999s01bC == null)
				l999s01bC = new L999S01B();
			l999s01bD = lms9990Service.findL999s01bByMainIdItemType(mainId,
					CtrConstants.L999S01BItemType.貼現);
			if (l999s01bD == null)
				l999s01bD = new L999S01B();
			l999s01bE = lms9990Service.findL999s01bByMainIdItemType(mainId,
					CtrConstants.L999S01BItemType.透支);
			if (l999s01bE == null)
				l999s01bE = new L999S01B();
			l999s01bF = lms9990Service.findL999s01bByMainIdItemType(mainId,
					CtrConstants.L999S01BItemType.委任票據保證);
			if (l999s01bF == null)
				l999s01bF = new L999S01B();
			l999s01bG = lms9990Service.findL999s01bByMainIdItemType(mainId,
					CtrConstants.L999S01BItemType.委任票據承兌);
			if (l999s01bG == null)
				l999s01bG = new L999S01B();
			l999s01bH = lms9990Service.findL999s01bByMainIdItemType(mainId,
					CtrConstants.L999S01BItemType.委任保證);
			if (l999s01bH == null)
				l999s01bH = new L999S01B();
			l999m01dC = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.授信總額度);
			if (l999m01dC == null) {
				l999m01dC = new L999M01D();
			}
			map = this.setL999m01aData(map, l999m01a);
			map = this.setL999m01bAData(map, l999m01bA);
			map = this.setL999m01dData(map, l999m01d);
			map = this.setL999m01bBData(map, l999m01bB);
			map = this.setL999s01aData(map, l999s01a, l999m01dC);
			map = this.setL999s01bData(map, l999s01bA,
					CtrConstants.L999S01BItemType.購料借款, yesNoMap, currMap,
					unitMap);
			map = this.setL999s01bData(map, l999s01bB,
					CtrConstants.L999S01BItemType.外銷借款, yesNoMap, currMap,
					unitMap);
			map = this.setL999s01bData(map, l999s01bC,
					CtrConstants.L999S01BItemType.營運週轉借款, yesNoMap, currMap,
					unitMap);
			map = this.setL999s01bData(map, l999s01bD,
					CtrConstants.L999S01BItemType.貼現, yesNoMap, currMap,
					unitMap);
			map = this.setL999s01bData(map, l999s01bE,
					CtrConstants.L999S01BItemType.透支, yesNoMap, currMap,
					unitMap);
			map = this.setL999s01bData(map, l999s01bF,
					CtrConstants.L999S01BItemType.委任票據保證, yesNoMap, currMap,
					unitMap);
			map = this.setL999s01bData(map, l999s01bG,
					CtrConstants.L999S01BItemType.委任票據承兌, yesNoMap, currMap,
					unitMap);
			map = this.setL999s01bData(map, l999s01bH,
					CtrConstants.L999S01BItemType.委任保證, yesNoMap, currMap,
					unitMap);

			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得02.兆豐連帶保證書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW02(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		Map<String, String> courtCodeMap = null;
		String content = null;
		L999M01A l999m01a = null;
		L999S02A l999s02a = null;
		L999M01B l999m01b1 = null;
		List<L999M01C> l999m01cList = null;
		Properties prop = null;
		try {
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M01Page.class);
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.LMS999XMLFile.連帶保證書);
			// 撈資料
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}

			l999s02a = lms9990Service.findL999s02aByMainId(mainId);
			if (l999s02a == null) {
				l999s02a = new L999S02A();
			}

			l999m01b1 = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.銀行甲方);
			if (l999m01b1 == null) {
				l999m01b1 = new L999M01B();
			}

			l999m01cList = lms9990Service.findL999m01cByMainId(mainId);
			courtCodeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.台灣法院清單);
			if (courtCodeMap == null) {
				courtCodeMap = new LinkedHashMap<String, String>();
			}

			map = this.setL999m01aData(map, l999m01a);
			map = this.setL999m01aOtherData(map, l999m01a, courtCodeMap);
			map = this.setL999m01bAData(map, l999m01b1);
			map = this.setL999s02aData(map, l999s02a);
			map = this.setL999m01cListData2(map, l999m01cList, prop);
			// 寫入資料
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得03.兆豐授信約定書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW03(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;

		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		Map<String, String> courtCodeMap = null;
		String content = null;
		L999M01A l999m01a = null;
		L999M01B l999m01bB = null;
		L999M01D l999m01d = null;
		try {
			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.LMS999XMLFile.授信約定書);
			// 撈資料
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null)
				l999m01a = new L999M01A();
			l999m01bB = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.借款人乙方);
			if (l999m01bB == null) {
				l999m01bB = new L999M01B();
			}

			l999m01d = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.特別條款);
			if (l999m01d == null) {
				l999m01d = new L999M01D();
			}

			courtCodeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.台灣法院清單);
			if (courtCodeMap == null)
				courtCodeMap = new LinkedHashMap<String, String>();
			map = this.setL999m01dData(map, l999m01d);
			map = this.setL999m01aData(map, l999m01a);
			map = this.setL999m01aOtherData(map, l999m01a, courtCodeMap);
			map = this.setL999m01bBData(map, l999m01bB);

			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得04.兆豐中長期契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW04(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		Map<String, String> courtCodeMap = null;
		String content = null;
		L999M01A l999m01a = null;
		L999M01B l999m01bA = null;
		L999M01B l999m01bB = null;
		L999S04A l999s04a = null;
		L999S01A l999s01a = null;
		L999M01D l999m01d = null;
		L999M01D l999m01dC = null;
		List<L999M01C> l999m01cList = null;
		List<L999S04B> l999s04bList = null;
		Properties prop = null;
		try {
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M01Page.class);
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.LMS999XMLFile.中長期契約書);
			// 撈資料
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null)
				l999m01a = new L999M01A();
			l999m01bA = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.銀行甲方);
			if (l999m01bA == null) {
				l999m01bA = new L999M01B();
			}

			l999m01bB = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.借款人乙方);
			if (l999m01bB == null) {
				l999m01bB = new L999M01B();
			}

			l999s04a = lms9990Service.findL999s04aByMainId(mainId);
			if (l999s04a == null) {
				l999s04a = new L999S04A();
			}

			l999s01a = lms9990Service.findL999s01aByMainId(mainId);
			if (l999s01a == null) {
				l999s01a = new L999S01A();
			}

			l999m01dC = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.授信總額度);
			if (l999m01dC == null) {
				l999m01dC = new L999M01D();
			}
			l999m01cList = lms9990Service.findL999m01cByMainId(mainId);
			l999s04bList = lms9990Service.findL999s04bByMainId(mainId);
			courtCodeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.台灣法院清單);
			if (courtCodeMap == null) {
				courtCodeMap = new LinkedHashMap<String, String>();
			}
			l999m01d = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.特別條款);
			if (l999m01d == null) {
				l999m01d = new L999M01D();
			}
			String custName = Util.trim(l999m01a.getCustId()) + " "
					+ Util.trim(l999m01a.getDupNo()) + " "
					+ Util.trim(l999m01a.getCustName());

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			IBranch branch = branchService.getBranch(user.getUnitNo());
			String branchName = branch.getBrName();
			String bossname = Util.trim(userInfoService.getUserName(Util
					.trim(branch.getBrnMgr())));
			// L999M01B的資料
			// 甲方是
			map.put("L999S04B.CUSTDATA1", "兆豐國際商業銀行股份有限公司" + branchName);
			map.put("L999S04B.CUSTDATA2", custName);
			map.put("L999S04B.CUSTDATA3", custName);
			// 主要資料內容 & 基準利率及調整
			map = this.setL999m01aData(map, l999m01a);
			// 授信內容及條件
			map = this.setL999s04bListData(map, l999s04bList);

			map = this.setL999m01bAData(map, l999m01bB);
			map = this.setL999m01bBData(map, l999m01bB);

			map = this.setL999m01cListData(map, l999m01cList, prop);

			map = this.setL999s01aData(map, l999s01a, l999m01dC);
			// 設定 同意 或 不同意 和兆豐所屬公司 台灣法院清單
			map = this.setL999m01aOtherData(map, l999m01a, courtCodeMap);
			// 本約正副本份數
			map = this.setL999s04aData(map, l999s04a);
			// 其他商議條款
			map = this.setL999m01dData(map, l999m01d);
			map.put("L999M01A.BRANCHNAME", branchName);
			map.put("L999M01A.BOSS", bossname);
			map.put("L999M01A.ADDR", Util.trim(branch.getAddr()));
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得05.股東債權同意書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW05(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		// 取得XML範本檔案名稱
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L999M01A l999m01a = null;
		try {
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.LMS999XMLFile.股東債權同意書);
			// 撈資料
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}

			map = this.setL999m01aData(map, l999m01a);
			map.put("L999M01A.custId", Util.trim(l999m01a.getCustId()) + " "
					+ Util.trim(l999m01a.getDupNo()));
			map.put("L999M01A.ADDR", Util.trim(l999m01a.getAddr()));
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得06.本票授權契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW06(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;

		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L999M01A l999m01a = null;
		try {
			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.LMS999XMLFile.本票授權契約書);
			// 撈資料
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}
			map = this.setL999m01aData(map, l999m01a);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			IBranch branch = branchService.getBranch(user.getUnitNo());
			String branchName = branch.getBrName();
			map.put("L999M01A.BRANCHNAME", branchName);
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 取得07.開發信用狀約定書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	// @DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	// public OutputStream getL999M01AW07(PageParameters params, Component
	// parent)
	// throws CapException {
	// ByteArrayOutputStream baos = null;
	//
	// String mainId = params.getString(EloanConstants.MAIN_ID);
	// Map<String, String> map = new LinkedHashMap<String, String>();
	// String content = null;
	// L999M01A l999m01a = null;
	// try {
	// // 取得XML範本檔案名稱
	// // 讀檔
	// content = Util.getFileContent(Util.trim(PropUtil
	// .getProperty("loadFile.dir"))
	// + "word/ctr/"
	// + CtrConstants.LMS999XMLFile.開發信用狀約定書);
	// // 撈資料
	// l999m01a = lms9990Service.findL999m01aByMainId(mainId);
	// if (l999m01a == null) {
	// l999m01a = new L999M01A();
	// }
	// map = this.setL999m01aData(map, l999m01a);
	// baos = this.writeWordContent(content, map);
	//
	// } catch (FileNotFoundException e) {
	// logger.error(e.getMessage());
	// throw new CapMessageException(getMessage(e.getMessage()),
	// getClass());
	// } catch (IOException e) {
	// logger.error(e.getMessage());
	// throw new CapMessageException(getMessage(e.getMessage()),
	// getClass());
	// } catch (Exception e) {
	// logger.error(e.getMessage());
	// throw new CapMessageException(getMessage(e.getMessage()),
	// getClass());
	// } finally {
	// if (map != null) {
	// map.clear();
	// }
	// }
	// return baos;
	// }

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW07(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;
		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		Map<String, String> courtCodeMap = null;
		String content = null;
		L999M01A l999m01a = null;
		L999S07A l999s07a = null;
		L999M01B l999m01b1 = null;
		L999M01D l999m01d = null;
		List<L999M01C> l999m01cList = null;
		Properties prop = null;
		try {
			prop = MessageBundleScriptCreator
					.getComponentResource(LMS9990M01Page.class);
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.LMS999XMLFile.開發信用狀約定書);
			// 撈資料
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}

			l999s07a = lms9990Service.findL999s07aByMainId(mainId);
			if (l999s07a == null) {
				l999s07a = new L999S07A();
			}

			l999m01d = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.特別條款);
			if (l999m01d == null) {
				l999m01d = new L999M01D();
			}

			l999m01b1 = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.銀行甲方);
			if (l999m01b1 == null) {
				l999m01b1 = new L999M01B();
			}

			l999m01cList = lms9990Service.findL999m01cByMainId(mainId);
			courtCodeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.台灣法院清單);
			if (courtCodeMap == null) {
				courtCodeMap = new LinkedHashMap<String, String>();
			}

			map = this.setL999m01aData(map, l999m01a);
			map = this.setL999m01aOtherDataW07(map, l999m01a, l999s07a);
			map = this.setL999m01bBData(map, l999m01b1);
			map = this.setL999s07aData(map, l999s07a);
			map = this.setL999m01dData(map, l999m01d);
			map = this.setL999m01cListData2(map, l999m01cList, prop);
			// 寫入資料
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 設定 中長期契約書授信內容
	 * 
	 * @param map
	 *            共用的map
	 * @param l999s04bList
	 *            中長期契約書授信內容及條件檔
	 * 
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999s04bListData(Map<String, String> map,
			List<L999S04B> l999s04bList) {
		map.put("L999S04B.itemContentA", "");
		map.put("L999S04B.itemContentB", "");
		map.put("L999S04B.itemContentC", "");
		map.put("L999S04B.itemContentD", "");
		map.put("L999S04B.itemContentE", "");
		map.put("L999S04B.itemContentF", "");
		map.put("L999S04B.itemContentG", "");
		map.put("L999S04B.itemContentH", "");
		map.put("L999S04B.itemContentI", "");
		map.put("L999S04B.itemContentJ", "");
		for (L999S04B l999s04b : l999s04bList) {
			map.put("L999S04B.itemContent" + l999s04b.getItemType(),
					Util.nullToSpace(l999s04b.getItemContent()));
		}
		return map;
	}

	/**
	 * 設定本約正本 副本份數
	 * 
	 * @param map
	 *            共用map
	 * @param l999s04a
	 *            中長期契約書檔
	 * 
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999s04aData(Map<String, String> map,
			L999S04A l999s04a) {
		map.put("L999S04A.totOriginal",
				Util.nullToSpace(l999s04a.getTotOriginal()));
		map.put("L999S04A.totCopy", Util.nullToSpace(l999s04a.getTotCopy()));
		map.put("L999S04A.aOriginal", Util.nullToSpace(l999s04a.getAOriginal()));
		map.put("L999S04A.aCopy", Util.nullToSpace(l999s04a.getACopy()));
		map.put("L999S04A.bOriginal", Util.nullToSpace(l999s04a.getBOriginal()));
		map.put("L999S04A.bCopy", Util.nullToSpace(l999s04a.getBCopy()));
		return map;
	}

	/**
	 * 設定字第號,基準利率
	 * 
	 * @param map
	 *            共用map
	 * @param l999m01a
	 *            約據書主檔
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999m01aData(Map<String, String> map,
			L999M01A l999m01a) {
		map.put("L999M01A.contractWord",
				Util.nullToSpace(l999m01a.getContractWord()));
		map.put("L999M01A.contractNo",
				Util.nullToSpace(l999m01a.getContractNo()));
		map.put("L999M01A.custId", Util.nullToSpace(l999m01a.getCustId()));
		map.put("L999M01A.custName", StringEscapeUtils.escapeXml(Util
				.nullToSpace(l999m01a.getCustName())));
		map.put("L999M01A.contractRate",
				Util.nullToSpace(l999m01a.getContractRate()));
		map.put("L999M01A.addr", Util.nullToSpace(l999m01a.getAddr()));
		return map;
	}

	/**
	 * 設定 同意 或 不同意 和 資料保密_同意項目 兆豐所屬公司
	 * 
	 * @param map
	 *            共用map
	 * @param l999m01a
	 *            約據書主檔
	 * @param courtCodeMap
	 *            法院清單
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999m01aOtherData(Map<String, String> map,
			L999M01A l999m01a, Map<String, String> courtCodeMap) {
		String dataUseFlag = Util.trim(l999m01a.getDataUseFlag());
		/**
		 * 同意欄位 (未勾)wEAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 不同意欄位 (未勾)wEAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 */
		if (UtilConstants.DEFAULT.是.equals(dataUseFlag)) {
			map.put("wEAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			// 4UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA

		} else if (UtilConstants.DEFAULT.否.equals(dataUseFlag)) {
			map.put("wEAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			// 4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		}

		/**
		 * <pre>
		 * 兆豐證券股份有限公司
		 * (未勾)/////1EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐產物保險股份有限公司
		 * (未勾)/////2EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐票券金融股份有限公司
		 * (未勾)/////3EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐人身保險代理人股份有限公司
		 * (未勾)/////4EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐國際證券投資信託股份有限公司
		 * (未勾)/////5EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐資產管理股份有限公司
		 * (未勾)/////6EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐創業投資股份有限公司
		 * (未勾)/////7EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 上述所有公司
		 * (未勾)/////8EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * </pre>
		 */

		map.put("L999M01A.courtCode",
				Util.nullToSpace(courtCodeMap.get(l999m01a.getCourtCode())));
		Integer dataUseItem = l999m01a.getDataUseItem();
		map.put("wUAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("1EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("2EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("3EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("4EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("5EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("6EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("7EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("8EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		if (dataUseItem == null) {

		} else if (dataUseItem == 0) {
			map.put("8EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		} else {
			if (Util.valueOfCheckBoxOption(dataUseItem, 1)) {
				// wUAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				// 1EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				map.put("1EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 2)) {
				map.put("2EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 3)) {
				map.put("3EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 4)) {
				map.put("4EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 5)) {
				map.put("5EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 6)) {
				map.put("6EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 7)) {
				map.put("7EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
		}
		return map;
	}

	/**
	 * 設定 企金約據書項目描述檔
	 * 
	 * @param map
	 *            共用map
	 * @param l999m01d
	 *            企金約據書項目描述檔
	 * 
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999m01dData(Map<String, String> map,
			L999M01D l999m01d) {
		map.put("L999M01D.ITEMCONTENTA",
				this.replaceByBr(l999m01d.getItemContent()));
		return map;
	}

	/**
	 * 設定 立約人 (甲方)兆豐國際商業銀行股份有限公司<br/>
	 * 
	 * 地址,負責人
	 * 
	 * @param map
	 *            共用map
	 * @param l999m01b
	 *            企金約據書立約人檔
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999m01bAData(Map<String, String> map,
			L999M01B l999m01b) {
		map.put("L999M01B.chairmanA", Util.nullToSpace(l999m01b.getChairman()));
		map.put("L999M01B.addrA", Util.nullToSpace(l999m01b.getAddr()));
		return map;
	}

	/**
	 * 設定 2.借款人(乙方)
	 * 
	 * @param map
	 *            共用map
	 * @param l999m01b
	 *            企金約據書立約人檔
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999m01bBData(Map<String, String> map,
			L999M01B l999m01b) {
		map.put("L999M01B.custNameB", Util.nullToSpace(l999m01b.getCustName()));
		map.put("L999M01B.custIdB", Util.nullToSpace(l999m01b.getCustId())
				+ " " + Util.nullToSpace(l999m01b.getDupNo()));
		map.put("L999M01B.chairmanB", Util.nullToSpace(l999m01b.getChairman()));
		map.put("L999M01B.chairmanIdB",
				Util.nullToSpace(l999m01b.getChairmanId()) + " "
						+ Util.nullToSpace(l999m01b.getChairmanDupNo()));
		map.put("L999M01B.addrB", Util.nullToSpace(l999m01b.getAddr()));
		return map;
	}

	/**
	 * 設定 企金約據書連保人(保證人)檔
	 * 
	 * @param map
	 *            共用map
	 * @param l999m01cList
	 *            企金約據書連保人(保證人)檔
	 * @param prop
	 *            語系檔
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999m01cListData(Map<String, String> map,
			List<L999M01C> l999m01cList, Properties prop) {
		StringBuffer str = new StringBuffer();
		for (L999M01C l999m01c : l999m01cList) {
			str.append(prop.getProperty("L999M01C.word.custName"))
					.append(l999m01c.getCustName())
					.append(prop.getProperty("L999M01C.word.signature"))
					.append(換行符號);
			str.append(prop.getProperty("L999M01C.word.addr"))
					.append(l999m01c.getAddr()).append(換行符號);
			str.append(prop.getProperty("L999M01C.word.custId"))
					.append(l999m01c.getCustId() + " " + l999m01c.getDupNo())
					.append(換行符號).append(換行符號);
		}
		map.put("L999M01B.DATA", str.toString());
		return map;
	}

	/**
	 * 設定 企金約據書連保人(保證人)檔
	 * 
	 * @param map
	 *            共用map
	 * @param l999m01cList
	 *            企金約據書連保人(保證人)檔
	 * @param prop
	 *            語系檔
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999m01cListData2(Map<String, String> map,
			List<L999M01C> l999m01cList, Properties prop) {
		for (int i = 1; i <= 4; i++) {
			if (l999m01cList.size() >= i) {
				L999M01C l999m01c = l999m01cList.get(i - 1);
				map.put("L999M01C.NAME" + i, l999m01c.getCustName());
				map.put("L999M01C.ADDR" + i, l999m01c.getAddr());
				map.put("L999M01C.ID" + i,
						l999m01c.getCustId() + l999m01c.getDupNo());
			} else {
				map.put("L999M01C.NAME" + i, "");
				map.put("L999M01C.ADDR" + i, "");
				map.put("L999M01C.ID" + i, "");
			}

		}
		return map;
	}

	/**
	 * 設定 綜合授信契約書檔 -授信種類
	 * 
	 * @param map
	 *            共用map
	 * @param l999s01a
	 *            綜合授信契約書檔
	 * 
	 * @return 置換的key map
	 */
	private Map<String, String> setL999s01aData(Map<String, String> map,
			L999S01A l999s01a, L999M01D l999m01d) {
		/**
		 * 
		 <pre>
		 * 購料借款 
		 * (未勾)/////1AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 外銷借款 
		 * (未勾)/////3AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 營運週轉借款 
		 * (未勾)/////5AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 貼現
		 * (未勾)/////7AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 透支
		 * (未勾)/////2AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 委任票據保證 
		 * (未勾)/////4AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 委任票據呈兌 
		 * (未勾)/////6AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 委任保證 
		 * (未勾)/////8AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * </pre>
		 */
		String itemType = Util.nullToSpace(l999s01a.getItemType());
		map.put("L999S01A.itemTypeCount",
				Util.nullToSpace(l999s01a.getItemType()).length() + "");
		map.put("L999S01A.totLoanAmt",
				this.replaceByBr(l999m01d.getItemContent()));
		map.put("L999S01A.usedSDate",
				Util.nullToSpace(TWNDate.toTW(l999s01a.getUsedSDate())));
		map.put("L999S01A.usedEDate",
				Util.nullToSpace(TWNDate.toTW(l999s01a.getUsedEDate())));
		map.put("L999S01A.dMonth1", Util.nullToSpace(l999s01a.getDMonth1()));
		map.put("L999S01A.dMonth2", Util.nullToSpace(l999s01a.getDMonth2()));
		map.put("L999S01A.dRate1", Util.nullToSpace(l999s01a.getDRate1()));
		map.put("L999S01A.dRate2", Util.nullToSpace(l999s01a.getDRate2()));
		map.put("L999S01A.dRateAdd1", Util.nullToSpace(l999s01a.getDRateAdd1()));
		map.put("L999S01A.dRateAdd2", Util.nullToSpace(l999s01a.getDRateAdd2()));

		map.put("1AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				itemType.indexOf(CtrConstants.L999S01BItemType.購料借款) == -1 ? "2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
						: "4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("3AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				itemType.indexOf(CtrConstants.L999S01BItemType.外銷借款) == -1 ? "2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
						: "4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("5AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				itemType.indexOf(CtrConstants.L999S01BItemType.營運週轉借款) == -1 ? "2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
						: "4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("7AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				itemType.indexOf(CtrConstants.L999S01BItemType.貼現) == -1 ? "2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
						: "4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("2AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				itemType.indexOf(CtrConstants.L999S01BItemType.透支) == -1 ? "2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
						: "4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("4AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				itemType.indexOf(CtrConstants.L999S01BItemType.委任票據保證) == -1 ? "2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
						: "4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("6AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				itemType.indexOf(CtrConstants.L999S01BItemType.委任票據承兌) == -1 ? "2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
						: "4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		map.put("8AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				itemType.indexOf(CtrConstants.L999S01BItemType.委任保證) == -1 ? "2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
						: "4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		return map;
	}

	/**
	 * 更換\r為<br/>
	 * 
	 * @param str
	 *            文字
	 * @return 替換結果
	 */
	private String replaceByBr(String str) {
		return str == null ? "" : str.replaceAll("\n", 換行符號);
	}

	/**
	 * 連帶保證書檔
	 * 
	 * @param map
	 *            共用的 保證期間
	 * @param l999s02a
	 *            連帶保證書檔
	 * @return 要置換 的key
	 * 
	 */
	private Map<String, String> setL999s02aData(Map<String, String> map,
			L999S02A l999s02a) {
		map.put("L999S02A.guaSDateY", Util.nullToSpace(l999s02a.getGuaSDateY()));
		map.put("L999S02A.guaSDateM", Util.nullToSpace(l999s02a.getGuaSDateM()));
		map.put("L999S02A.guaSDateD", Util.nullToSpace(l999s02a.getGuaSDateD()));
		map.put("L999S02A.guaEDateY", Util.nullToSpace(l999s02a.getGuaEDateY()));
		map.put("L999S02A.guaEDateM", Util.nullToSpace(l999s02a.getGuaEDateM()));
		map.put("L999S02A.guaEDateD", Util.nullToSpace(l999s02a.getGuaEDateD()));
		map.put("L999S02A.guaAmt", NumConverter.addComma(l999s02a.getGuaAmt()));
		return map;
	}

	/**
	 * 設定綜合授信契約書借款種類檔 是、非 循環使用
	 * 
	 * @param map
	 *            共用的map
	 * @param l999s01b
	 *            綜合授信契約書借款種類檔
	 * @param itemType
	 *            借款種類
	 * @param yesNoMap
	 *            是或否的codetype
	 * @return 要置換的key
	 */
	private Map<String, String> setL999s01bData(Map<String, String> map,
			L999S01B l999s01b, String itemType, Map<String, String> yesNoMap,
			Map<String, String> currMap, Map<String, String> unitMap) {
		map.put("L999S01B.item01" + itemType,
				Util.nullToSpace(l999s01b.getItem01()));
		// 0 - 非 ；1 - 是
		map.put("L999S01B.item02" + itemType,
				"0".equals(l999s01b.getItem02()) ? yesNoMap.get(l999s01b
						.getItem02()) : "");
		// 修改原本金額為數字欄位 改為Item15自行輸入欄位
		String item03Str = this.replaceByBr(l999s01b.getItem15());
		map.put("L999S01B.item03" + itemType, item03Str);

		map.put("L999S01B.item04" + itemType,
				this.replaceByBr(l999s01b.getItem04()));
		map.put("L999S01B.item05" + itemType,
				Util.nullToSpace(l999s01b.getItem05()));
		map.put("L999S01B.item06" + itemType,
				Util.nullToSpace(l999s01b.getItem06()));
		map.put("L999S01B.item07" + itemType,
				Util.nullToSpace(l999s01b.getItem07()));
		map.put("L999S01B.item08" + itemType,
				Util.nullToSpace(l999s01b.getItem08()));
		map.put("L999S01B.item09" + itemType,
				Util.nullToSpace(l999s01b.getItem09()));
		map.put("L999S01B.item10" + itemType,
				Util.nullToSpace(l999s01b.getItem10()));
		map.put("L999S01B.item11" + itemType,
				this.replaceByBr(l999s01b.getItem11()));
		map.put("L999S01B.item12" + itemType,
				this.replaceByBr(l999s01b.getItem12()));
		map.put("L999S01B.item13" + itemType,
				this.replaceByBr(l999s01b.getItem13()));
		map.put("L999S01B.item14" + itemType,
				this.replaceByBr(l999s01b.getItem14()));
		return map;
	}

	/**
	 * 為map的key 前後加上特殊符號
	 * 
	 * @param map
	 * @return
	 */
	private Map<String, String> addMarkByMapKey(Map<String, String> map) {
		Map<String, String> temp = new LinkedHashMap<String, String>();
		String tempKey = "";
		for (String key : map.keySet()) {
			tempKey = key;
			if (!key.endsWith("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA")) {
				tempKey = "[" + tempKey + "]";
			}
			temp.put(tempKey, map.get(key));
		}
		return temp;
	}

	/**
	 * 寫入word資料
	 * 
	 * @param content
	 *            xml 內容
	 * @param map
	 *            對應欄位值
	 * @return ByteArrayOutputStream
	 * @throws CapMessageException
	 */
	private ByteArrayOutputStream writeWordContent(String content,
			Map<String, String> map) throws CapMessageException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		content = Util.replaceWordContent(content, this.addMarkByMapKey(map));

		OutputStreamWriter outWriter = null;
		try {
			outWriter = new OutputStreamWriter(baos, CODE_UTF_8);
			outWriter.write(content);
			outWriter.close();
			return baos;
		} catch (UnsupportedEncodingException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException i) {
			logger.error(i.getMessage());
			throw new CapMessageException(getMessage(i.getMessage()),
					getClass());
		}

	}

	/**
	 * 取得08.應收帳款承購約定書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getL999M01AW08(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = null;

		String mainId = params.getString(EloanConstants.MAIN_ID);
		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		L999M01A l999m01a = null;
		try {
			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir"))
					+ "word/ctr/"
					+ CtrConstants.LMS999XMLFile.應收帳款承購約定書);
			// 撈資料
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}
			map = this.setL999m01aData(map, l999m01a);
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}

	/**
	 * 開發信用狀約定書
	 * 
	 * @param map
	 *            共用的 保證期間
	 * @param l999s02a
	 *            連帶保證書檔
	 * @return 要置換 的key
	 * 
	 */
	private Map<String, String> setL999s07aData(Map<String, String> map,
			L999S07A l999s07a) {
		map.put("l999s07a.dueSDateY", Util.nullToSpace(l999s07a.getDueSDateY()));
		map.put("l999s07a.dueSDateM", Util.nullToSpace(l999s07a.getDueSDateM()));
		map.put("l999s07a.dueSDateD", Util.nullToSpace(l999s07a.getDueSDateD()));

		map.put("l999s07a.accNo", Util.nullToSpace(l999s07a.getAccNo()));
		map.put("l999s07a.coverLoanCurr",
				Util.nullToSpace(l999s07a.getCoverLoanCurr()));
		map.put("l999s07a.coverLoanAmt",
				Util.nullToSpace(l999s07a.getCoverLoanAmt()));

		map.put("l999s07a.useSDateY", Util.nullToSpace(l999s07a.getUseSDateY()));
		map.put("l999s07a.useSDateM", Util.nullToSpace(l999s07a.getUseSDateM()));
		map.put("l999s07a.useSDateD", Util.nullToSpace(l999s07a.getUseSDateD()));
		map.put("l999s07a.useEDateY", Util.nullToSpace(l999s07a.getUseEDateY()));
		map.put("l999s07a.useEDateM", Util.nullToSpace(l999s07a.getUseEDateM()));
		map.put("l999s07a.useEDateD", Util.nullToSpace(l999s07a.getUseEDateD()));

		map.put("l999s07a.totLoanAmt",
				Util.nullToSpace(l999s07a.getTotLoanAmt()));
		map.put("l999s07a.guaPercent",
				Util.nullToSpace(l999s07a.getGuaPercent()));

		map.put("l999s07a.pSignDateY",
				Util.nullToSpace(l999s07a.getPSignDateY()));
		map.put("l999s07a.pSignDateM",
				Util.nullToSpace(l999s07a.getPSignDateM()));
		map.put("l999s07a.pSignDateD",
				Util.nullToSpace(l999s07a.getPSignDateD()));

		map.put("l999s07a.pContractWord",
				Util.nullToSpace(l999s07a.getPContractWord()));
		map.put("l999s07a.pContractNo",
				Util.nullToSpace(l999s07a.getPContractNo()));

		return map;
	}

	/**
	 * 設定 同意 或 不同意 和 資料保密_同意項目 兆豐所屬公司
	 * 
	 * @param map
	 *            共用map
	 * @param l999m01a
	 *            約據書主檔
	 * @param l999s07a
	 *            開發信用狀約定書
	 * @return 要置換的key Map
	 */
	private Map<String, String> setL999m01aOtherDataW07(
			Map<String, String> map, L999M01A l999m01a, L999S07A l999s07a) {
		String dataUseFlag = Util.trim(l999m01a.getDataUseFlag());
		/**
		 * 同意欄位 (未勾) wEAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 不同意欄位 (未勾)wEAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 */
		if (UtilConstants.DEFAULT.是.equals(dataUseFlag)) {
			map.put("2UAAAAUAAYAQwBoAGUAYwBrADEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			// 4UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA

		} else if (UtilConstants.DEFAULT.否.equals(dataUseFlag)) {
			map.put("2UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			// 4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		}

		/**
		 * <pre>
		 * 兆豐證券股份有限公司
		 * (未勾)/////1EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐產物保險股份有限公司
		 * (未勾)/////2EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐票券金融股份有限公司
		 * (未勾)/////3EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐人身保險代理人股份有限公司
		 * (未勾)/////4EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐國際證券投資信託股份有限公司
		 * (未勾)/////5EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐資產管理股份有限公司
		 * (未勾)/////6EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐創業投資股份有限公司
		 * (未勾)/////7EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 上述所有公司
		 * (未勾)/////8EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * </pre>
		 */

		Integer dataUseItem = l999m01a.getDataUseItem();
		map.put("2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAYAQwBoAGUAYwBrADQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAYAQwBoAGUAYwBrADUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");//

		map.put("2UAAAAUAAYAQwBoAGUAYwBrADYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");//

		map.put("2UAAAAUAAYAQwBoAGUAYwBrADcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",//
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAYAQwBoAGUAYwBrADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",//
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAYAQwBoAGUAYwBrADkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",//
				"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",//
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		if (dataUseItem == null) {

		} else if (dataUseItem == 0) {
			map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		} else {
			if (Util.valueOfCheckBoxOption(dataUseItem, 1)) {
				// wUAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				// 1EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				map.put("2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 2)) {
				map.put("2UAAAAUAAYAQwBoAGUAYwBrADQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 3)) {
				map.put("2UAAAAUAAYAQwBoAGUAYwBrADUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 4)) {
				map.put("2UAAAAUAAYAQwBoAGUAYwBrADYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 5)) {
				map.put("2UAAAAUAAYAQwBoAGUAYwBrADcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 6)) {
				map.put("2UAAAAUAAYAQwBoAGUAYwBrADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 7)) {
				map.put("2UAAAAUAAYAQwBoAGUAYwBrADkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
		}

		// 連保人保密**************************************************************************

		dataUseFlag = Util.trim(l999s07a.getDataUseFlagGua());
		/**
		 * 同意欄位 (未勾) wEAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 不同意欄位 (未勾)wEAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 */
		if (UtilConstants.DEFAULT.是.equals(dataUseFlag)) {
			map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAMQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			// 4UAAAAUAAYAQwBoAGUAYwBrADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA

		} else if (UtilConstants.DEFAULT.否.equals(dataUseFlag)) {
			map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAMgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			// 4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		}

		/**
		 * <pre>
		 * 兆豐證券股份有限公司
		 * (未勾)/////1EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐產物保險股份有限公司
		 * (未勾)/////2EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐票券金融股份有限公司
		 * (未勾)/////3EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐人身保險代理人股份有限公司
		 * (未勾)/////4EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐國際證券投資信託股份有限公司
		 * (未勾)/////5EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐資產管理股份有限公司
		 * (未勾)/////6EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 兆豐創業投資股份有限公司
		 * (未勾)/////7EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * 上述所有公司
		 * (未勾)/////8EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * (已勾)/////4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		 * </pre>
		 */

		dataUseItem = l999s07a.getDataUseItemGua();
		map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAMwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",// 1
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAcAQwBoAGUAYwBrADEANAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",//
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAcAQwBoAGUAYwBrADEANQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",//
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAcAQwBoAGUAYwBrADEANgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",//
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAcAQwBoAGUAYwBrADEANwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", //
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", //
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", //
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		map.put("2UAAAAUAAcAQwBoAGUAYwBrADIAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", //
		"2UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");

		if (dataUseItem == null) {

		} else if (dataUseItem == 0) {
			map.put("2UAAAAUAAcAQwBoAGUAYwBrADIAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
					"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
		} else {
			if (Util.valueOfCheckBoxOption(dataUseItem, 1)) {
				// wUAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				// 1EAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
				map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAMwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 2)) {
				map.put("2UAAAAUAAcAQwBoAGUAYwBrADEANAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 3)) {
				map.put("2UAAAAUAAcAQwBoAGUAYwBrADEANQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 4)) {
				map.put("2UAAAAUAAcAQwBoAGUAYwBrADEANgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 5)) {
				map.put("2UAAAAUAAcAQwBoAGUAYwBrADEANwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 6)) {
				map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
			if (Util.valueOfCheckBoxOption(dataUseItem, 7)) {
				map.put("2UAAAAUAAcAQwBoAGUAYwBrADEAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
						"4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
			}
		}

		return map;
	}
	
	/**
	 * 取得03.兆豐授信約定書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getLMS_W03(PageParameters params)
			throws CapException {
		String templateName = "LMS_W03_2003.xml";

		ByteArrayOutputStream baos = null;
		String mainId = Util.nullToSpace(params.getString("tabFormMainId")); // 額度明細表mainId

		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		try {
			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);
			
			// default
			map.put("L140M01A.custName", "");
			map.put("L120S01A.custName", "");
			map.put("L120S01A.custId", "");
			map.put("L120S01B.cmpAddr", "");
			map.put("L120S01B.chairman", "");
			map.put("L120S01B.chairmanId", "");
			
			// 撈資料
			L140M01A l140m01a = null;
			l140m01a = lms9990Service.findL140M01ABymainId(mainId);
			if (l140m01a == null)
				l140m01a = new L140M01A();
			L120M01C l120m01c = l140m01a.getL120m01c();
			if (l120m01c == null)
				l120m01c = new L120M01C();
			L120M01A l120m01a = lms9990Service.findL120M01ABymainId(l120m01c.getMainId());
			if (l120m01a == null)
				l120m01a = new L120M01A();
			
			L120S01A l120s01a = null;
			l120s01a = lms9990Service.findL120s01aByUniqueKey(l120m01a.getMainId(),
					l140m01a.getCustId(), l140m01a.getDupNo());
			if (l120s01a == null)
				l120s01a = new L120S01A();

			L120S01B l120s01b = null;
			l120s01b = lms9990Service.findL120s01bByUniqueKey(l120m01a.getMainId(),
					l140m01a.getCustId(), l140m01a.getDupNo());
			if (l120s01b == null)
				l120s01b = new L120S01B();

			// 塞值
			map.put("L140M01A.custName", l140m01a.getCustName());
			Map<String, Object> map0024 = misCustdataService.findByIdDupNo(Util.trim(l120s01a.getCustId()), Util.trim(l120s01a.getDupNo()));
			if (map0024 != null) {
				map.put("L120S01A.custName", Util.trim(map0024.get("CNAME")));
				map.put("L120S01A.custId", Util.trim(map0024.get("CUSTID")) + " " + Util.trim(map0024.get("DUPNO")));
				map.put("L120S01B.cmpAddr", Util.trim(map0024.get("FULLADDR")));
			} else {
				map.put("L120S01A.custName", Util.nullToSpace(l120s01a.getCustName()));
				map.put("L120S01A.custId", Util.nullToSpace(l120s01a.getDupNo()) + " " + Util.nullToSpace(l120s01a.getDupNo()));
				map.put("L120S01B.cmpAddr", Util.nullToSpace(l120s01b.getCmpAddr()));
			}
			map.put("L120S01B.chairman", Util.nullToSpace(l120s01b.getChairman()));
			map.put("L120S01B.chairmanId", Util.nullToSpace(l120s01b.getChairmanId()) 
					+ " " + Util.nullToSpace(l120s01b.getChairmanDupNo()));
			
			map = this.replaceMapContStr(map); // 取代特定文字
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (IOException e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
			throw new CapMessageException(e.getMessage(), getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}
	
	/**
	 * 取得04.兆豐中長期契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getLMS_W04(PageParameters params)
			throws CapException {
		String templateName = "LMS_W04_2003.xml";

		ByteArrayOutputStream baos = null;
		String mainId = Util.nullToSpace(params.getString("tabFormMainId")); // 額度明細表mainId

		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		Map<String, String> currMap = null;
		Map<String, String> rateGetIntMap = null;
		try {
			currMap = codeTypeService.findByCodeType("Common_Currcy");
			if (currMap == null)
				currMap = new LinkedHashMap<String, String>();
			rateGetIntMap = codeTypeService
					.findByCodeType("lms1401s0204_rateGetInt");
			if (rateGetIntMap == null)
				rateGetIntMap = new LinkedHashMap<String, String>();
			
			// 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
			HashMap<String, String> moneyMap = new HashMap<String, String>();
			moneyMap.put("1", "TWD");
			moneyMap.put("2", "USD");
			moneyMap.put("3", "JPY");
			moneyMap.put("4", "EUR");
			moneyMap.put("5", "CNY");
			moneyMap.put("6", "AUD");
			moneyMap.put("7", "HKD");
			moneyMap.put("Z", "OTH");
			
			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);
			
			// default
			map.put("L140M01A.custName", "");	// 立約人
			map.put("proxy", "");				// 甲方
			map.put("party", "");				// 乙方
			map.put("party2", "　　　");			// 因...需要
			map.put("itemA", "");				// 授信用途
			map.put("itemB", "");				// 授信金額
			map.put("itemC", "");				// 動用方式及條件
			map.put("itemD", "");				// 撥款方式
			map.put("itemE", "");				// 償還期限及方式
			map.put("itemF", "");				// 利息手續費計付
			map.put("itemG", "");				// 違約金及遲延利息計付
			map.put("itemH", "");				// 期前清償違約金計付
			map.put("itemI", "");				// 承諾費
			map.put("itemJ", "");				// 其他個別商議條件
			map.put("baseRate", "");			// 基準利率
			map.put("proxyBoss", "");			// 甲方負責人
			map.put("proxyAddr", "");			// 甲方地址
			
			// 撈資料
			L140M01A l140m01a = null;
			l140m01a = lms9990Service.findL140M01ABymainId(mainId);
			if (l140m01a == null)
				l140m01a = new L140M01A();
			
			if (l140m01a.getL140m01b() != null) {
                for (L140M01B l140m01b : l140m01a.getL140m01b()) {
                    // 樣板格式
                    if(Util.equals(Util.trim(l140m01b.getItemType()), UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件)
                            && Util.equals(Util.trim(l140m01b.getFormatType()), "2")) {
                    	map = this.getW04Item(map, l140m01a, currMap);
                    }
                }
			}
			
			List<Map<String, Object>> rows = misMislnratService.findMislnratByLRRate("S8", "TWD");
			String baseRate = "";
			if(rows.size() > 0){
				baseRate = rows.get(0).get("LR_RATE").toString();
			}
			String baseRateStr = new NumericFormatter("##.#####").reformat(baseRate);
			
			List<L140M01F> l140m01fs = lms9990Service.findL140m01fByMainId(mainId);

			// 利息計付
			if (l140m01fs != null && !l140m01fs.isEmpty()) {
				String firstLoanTP = this.getFirstLoanTP(new String[] {
						"H", "I", "N" }, l140m01a);
				for (L140M01F l140m01f : l140m01fs) {
					String[] TPList = Util.trim(l140m01f.getLoanTPList())
							.split(UtilConstants.Mark.SPILT_MARK);
					if (Arrays.asList(TPList).contains(firstLoanTP)) {
						String[] arrStr = this.getInterest(l140m01a, l140m01f,
								"", moneyMap, rateGetIntMap, currMap);
						StringBuffer sbAll = new StringBuffer();
						int listCount = 0;
						if (arrStr[0].length() > 0) {
							listCount++;
							sbAll.append(
									Util.toFullCharString(Integer
											.toString(listCount))
											+ "、"
											+ currMap.get("USD") + "：").append(
									arrStr[0]);
						}
						if (arrStr[1].length() > 0) {
							listCount++;
							sbAll.append((sbAll.length() > 0 ? 換行符號 : ""));
							sbAll.append(
									Util.toFullCharString(Integer
											.toString(listCount))
											+ "、"
											+ currMap.get("TWD") + "：").append(
									arrStr[1]);
						}
						if (arrStr[2].length() > 0) {
							listCount++;
							sbAll.append((sbAll.length() > 0 ? 換行符號 : ""));
							sbAll.append(arrStr[2]);
						}
						if (listCount < 2) {
							String allStr = sbAll.toString();
							allStr = allStr.replaceFirst(
									Util.toFullCharString(Integer
											.toString(listCount)) + "、", "");
							sbAll.setLength(0);
							sbAll.append(allStr);
						} else {
							if(sbAll.length() > 0){
								sbAll.insert(0, 換行符號);
							}
						}
						map.put("item" + "F", sbAll.toString()); // CtrConstants.L999S04BItemType.利息手續費計付
					}
				}
			}
			
			// 塞值
			map.put("L140M01A.custName", l140m01a.getCustName());
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			IBranch branch = branchService.getBranch(user.getUnitNo());
			String branchName = branch.getBrName();
			map.put("proxy", "兆豐國際商業銀行股份有限公司" + branchName);
			map.put("party", Util.trim(l140m01a.getCustId()) + " " + Util.trim(l140m01a.getDupNo()) + " " + Util.trim(l140m01a.getCustName()));
			String bossname = Util.trim(userInfoService.getUserName(Util.trim(branch.getBrnMgr())));
			map.put("proxyBoss", bossname);
			map.put("proxyAddr", Util.trim(branch.getAddr()));
//			map.put("baseRate", NumConverter.numberToChinese(Util.parseBigDecimal(baseRateStr).toString()));
			map.put("baseRate", Util.parseBigDecimal(baseRateStr).toString());
			
			map = this.replaceMapContStr(map); // 取代特定文字
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}
	
	public Map<String, String> getW04Item(Map<String, String> map, L140M01A l140m01a, Map<String, String> currMap) throws CapMessageException {
//		String regex = "\\d*";	// 擷取數字
		HashSet<String> loanTPs = new HashSet<String>();	// 屬於中長期樣版的科目
		String[] bizCatA = new String[] { "H", "I", "N" };	// 屬於中長期的樣板代號
        // 取得樣版與科目對應
		String bizCat_LoanTP = Util.trim(lmsService.getSysParamDataValue("LMS_BIZCAT_LOANTP"));
		if (Util.notEquals(bizCat_LoanTP, "")) {
			JSONObject jsonBizCat_LoanTP = JSONObject.fromObject("{" + bizCat_LoanTP + "}");
			if (jsonBizCat_LoanTP != null) {
				for (String bizCat : bizCatA) {
					String loanTPList = Util.trim(jsonBizCat_LoanTP.get(bizCat));
					if (Util.isNotEmpty(loanTPList)) {
						String[] loanTPArr = StringUtils.split(loanTPList, "|");
						for (String loanTP : loanTPArr) {
							loanTPs.add(loanTP);
						}
					}
				}
			}
		}

		boolean hasLoanTP = false;
		if (l140m01a.getL140m01c() != null) {
			for (L140M01C l140m01c : l140m01a.getL140m01c()) {
				if (loanTPs.contains(l140m01c.getLoanTP())) {
					hasLoanTP = true;
					break;
				}
			}
		}
		if(hasLoanTP) {
			HashMap<String, String[]> type = new HashMap<String, String[]>();	// 對應樣版項目
			type.put(CtrConstants.L999S04BItemType.授信用途, new String[] { "H01", "I01", "N01" });
			type.put(CtrConstants.L999S04BItemType.動用方式及條件, new String[] { "H04", "I04", "N03" });
			type.put(CtrConstants.L999S04BItemType.撥款方式, new String[] { "H08", "I08", "N08" });
			type.put(CtrConstants.L999S04BItemType.償還期限及方式, new String[] { "H05", "I05", "N04" });
//			type.put(CtrConstants.L999S04BItemType.利息手續費計付, new String[] { "H09", "I09", "N09" });
			type.put(CtrConstants.L999S04BItemType.違約金及遲延利息計付, new String[] { "H09", "I09", "N09" });
			type.put(CtrConstants.L999S04BItemType.期前清償違約金計付, new String[] { "H10", "I10", "N10" });
			type.put(CtrConstants.L999S04BItemType.承諾費, new String[] { "H11", "I11", "N11" });

			HashMap<String, String[]> usedItem = new HashMap<String, String[]>();	// 已經串過的資料
			usedItem.put("H", new String[] { "01", "04", "05", "08", "09", "10", "11" });
			usedItem.put("I", new String[] { "01", "04", "05", "08", "09", "10", "11" });
			usedItem.put("N", new String[] { "01", "03", "04", "08", "09", "10", "11" });

			for(String itemType : type.keySet()){
				int seq = 1;
				StringBuffer sb = new StringBuffer();
				String[] codeArr = type.get(itemType);
				for (String code : codeArr) {
					String bizCat = (code.length() >= 1 ? code.substring(0, 1) : code);
					String bizItem = (code.length() >= 3 ? code.substring(1, 3) : code);
					List<L140S09A> l140s09as = lmsService.findL140s09a(l140m01a.getMainId(), "", bizCat, bizItem);
					for (L140S09A l140s09a : l140s09as) {
						List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util.trim(l140s09a.getOid()));
						for (L140S09B l140s09b : l140s09bs) {
							String str = l140s09b.getCont();
//							Pattern p = Pattern.compile(regex);
//							Matcher m = p.matcher(str);
//							while (m.find()) {
//								if (!"".equals(m.group())){
//									str = str.replaceFirst(m.group(), NumConverter.toChineseNumber(m.group()));
////									logger.debug("***************************");
////									NumConverter.toChineseNumber(m.group());
////									logger.debug("*******" + m.group() + "*******" + NumConverter.toChineseNumber(m.group()));
////									logger.debug("***************************");
////									System.out.println("come here:" + m.group());
//								}
//							}
							sb.append((sb.length() > 0 ? 換行符號 : ""));
//							sb.append(seq++).append(". ").append(str);
							sb.append(seq > 1 ? (seq + ". ") : "").append(str);
						}
					}
				}
				// 動用方式及條件 要多串 "動用先決條件"
				if (Util.equals(itemType, "C")) {
					StringBuffer sbC = new StringBuffer();
					int seqC = 0;
					String[] itemList = new String[] { "H03", "I03" };
					for (String item : itemList) {
						String bizCat = (item.length() >= 1 ? item.substring(0,
								1) : item);
						String bizItem = (item.length() >= 3 ? item.substring(
								1, 3) : item);
						List<L140S09A> l140s09as = lmsService.findL140s09a(
								l140m01a.getMainId(), "", bizCat, bizItem);
						for (L140S09A l140s09a : l140s09as) {
							List<L140S09B> l140s09bs = lmsService
									.findL140s09bByMainId(Util.trim(l140s09a
											.getOid()));
							for (L140S09B l140s09b : l140s09bs) {
								String str = l140s09b.getCont();
//								Pattern p = Pattern.compile(regex);
//								Matcher m = p.matcher(str);
//								while (m.find()) {
//									if (!"".equals(m.group())) {
//										str = str.replaceFirst(m.group(),
//												NumConverter.toChineseNumber(m
//														.group()));
//									}
//								}
								sbC.append((sbC.length() > 0 ? 換行符號 : ""));
								seqC++;
								sbC.append(seqC > 1 ? ("(" + seqC + ")") : "")
										.append(str);
							}
						}
					}
					if (sbC.length() > 0) {
						seq++;
						sbC.insert(0, seq + ". 動用先決條件："
								+ (seqC > 1 ? (換行符號 + "(1)") : ""));
						sb.append((sb.length() > 0 ? 換行符號 : "")).append(sbC);
					}
				}
				if (sb.length() > 0 && seq > 1) {
					sb.insert(0, 換行符號 + "1. ");
				}
				map.put("item" + itemType, sb.toString());
			}

//			// 剩下的資料都丟到  其他個別商議條件
			// 承諾事項 & 其他未盡事宜
			HashMap<String, String[]> elseItem = new HashMap<String, String[]>();
			elseItem.put("H", new String[] { "06", "07", "12" });
			elseItem.put("I", new String[] { "06", "07", "12" });
			elseItem.put("N", new String[] { "05", "07" });
			StringBuffer sbElse = new StringBuffer();
			int seqAll = 1;
			for (String bizCat : bizCatA) {
//				int seq = 1;
				List<L140S09A> l140s09as = lmsService.findL140s09a(l140m01a.getMainId(), "", bizCat, "");
				for (L140S09A l140s09a : l140s09as) {
//					String[] bizItemArr = usedItem.get(bizCat);
					// 其他個別商議條件
					String[] bizItemArr = elseItem.get(bizCat);
					String bizItemStr = l140s09a.getBizItem();
//					if(!Arrays.asList(bizItemArr).contains(bizItemStr)){
					// 剩下的資料都丟到 其他個別商議條件
					if (Arrays.asList(bizItemArr).contains(bizItemStr)) {
						List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util.trim(l140s09a.getOid()));
						for (L140S09B l140s09b : l140s09bs) {
							String str = l140s09b.getCont();
//							Pattern p = Pattern.compile(regex);
//							Matcher m = p.matcher(str);
//							while (m.find()) {
//								if (!"".equals(m.group())){
//									str = str.replaceFirst(m.group(), NumConverter.toChineseNumber(m.group()));
//								}
//							}
							sbElse.append((sbElse.length() > 0 ? 換行符號 : ""));
//							sbElse.append(seq++).append(". ").append(str);
							sbElse.append(seqAll++).append(". ").append(str);
						}
					}
				}
			}
			if(sbElse.length() > 0){
				sbElse.insert(0, 換行符號);
			}
			map.put("item" + CtrConstants.L999S04BItemType.其他個別商議條件, sbElse.toString());

			// 授信金額
//			String content = Util.trim(l140m01a.getCurrentApplyCurr()) + " " + NumConverter.addComma(Util.parseBigDecimal(l140m01a.getCurrentApplyAmt()));
			String content = MapUtils.getString(currMap, Util.trim(l140m01a.getCurrentApplyCurr()), 
					Util.trim(l140m01a.getCurrentApplyCurr())) + " "
					+ NumConverter.toChineseNumber(Util.parseBigDecimal(l140m01a
							.getCurrentApplyAmt())) + "元整";
			map.put("item" + CtrConstants.L999S04BItemType.授信金額, content);
		}

        return map;
    }
	
	/**
	 * 取得01.兆豐綜合授信契約書Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getLMS_W01(PageParameters params)
			throws CapException {
		String templateName = "LMS_W01_2003.xml";

		ByteArrayOutputStream baos = null;
		String mainId = Util.nullToSpace(params.getString("tabFormMainId")); // 額度明細表mainId

		Map<String, String> map = new LinkedHashMap<String, String>();
		String content = null;
		
		Map<String, String> yesNoMap = null;
		Map<String, String> currMap = null;
		Map<String, String> rateGetIntMap = null;
		Map<String, String> monTypeMap = null;
		Map<String, Object> brMap = null;
		try {
			DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,###,###,##0.####");
			yesNoMap = codeTypeService.findByCodeType("Common_YesNo1");
			if (yesNoMap == null)
				yesNoMap = new LinkedHashMap<String, String>();
			currMap = codeTypeService.findByCodeType("Common_Currcy");
			if (currMap == null)
				currMap = new LinkedHashMap<String, String>();
			rateGetIntMap = codeTypeService.findByCodeType("lms1401s0204_rateGetInt");
			if (rateGetIntMap == null)
				rateGetIntMap = new LinkedHashMap<String, String>();
			monTypeMap = codeTypeService.findByCodeType("lms1405s0204_monType");
			if (monTypeMap == null)
				monTypeMap = new LinkedHashMap<String, String>();
			
			// 1新台幣、2美金、3日幣、4歐元、5人民幣、6澳幣、7港幣、Z雜幣
	        HashMap<String, String> moneyMap = new HashMap<String, String>();
	        moneyMap.put("1", "TWD");
	        moneyMap.put("2", "USD");
	        moneyMap.put("3", "JPY");
	        moneyMap.put("4", "EUR");
	        moneyMap.put("5", "CNY");
	        moneyMap.put("6", "AUD");
	        moneyMap.put("7", "HKD");
	        moneyMap.put("Z", "OTH");
			
			// 取得XML範本檔案名稱
			// 讀檔
			content = Util.getFileContent(Util.trim(PropUtil
					.getProperty("loadFile.dir")) + "word/" + templateName);
			
			// default
			map.put("L140M01A.custName", "");
			map.put("L120S01A.custName", "");
			map.put("L120S01A.custId", "");
			map.put("L120S01B.cmpAddr", "");
			map.put("L120S01B.chairman", "");
			map.put("brBoss", "");
			map.put("brAddr", "");
			map.put("itemTypeCount", "");
			
			String[] defaultArr = new String[] { 
					CtrConstants.L999S01BItemType.購料借款, CtrConstants.L999S01BItemType.外銷借款,
					CtrConstants.L999S01BItemType.營運週轉借款, CtrConstants.L999S01BItemType.貼現,
					CtrConstants.L999S01BItemType.透支, CtrConstants.L999S01BItemType.委任票據保證,
					CtrConstants.L999S01BItemType.委任票據承兌, CtrConstants.L999S01BItemType.委任保證 };
			for(String type : defaultArr){
				map.put("item" + type, "");
				map.put("reUse" + type, "");
				map.put("loanAmt" + type, "");	// D.E 為新台幣
				map.put("interest" + type, "");
			}
			map.put("totLoanAmt", "");
			map.put("useDeadline", "");
			map.put("baseRate", "");
			map.put("dMonth1", "");
			map.put("dMonth2", "");
			map.put("dRate1", "");
			map.put("dRate2", "");
			map.put("dRateAdd1", "");
			map.put("dRateAdd2", "");
			// 利息計付 A.購料借款 特別分開幣別
			map.put("interestA_USD", "");
			map.put("interestA_TWD", "");
			map.put("interestA_OTH", "");
			map.put("rateGetInt", "");
			map.put("paStr", "");
			map.put("periodB05_01_1", "");
			map.put("periodB05_01_2", "");
			map.put("periodB05_01_3", "");
			map.put("periodB05_02_1", "");
			map.put("useWayB02_01_1", "");
			map.put("useWayB02_01_2", "");
			map.put("lmtDaysB", "");
			map.put("lmtPeriodC", "");
			map.put("useWayC", "");
			map.put("lmtDaysD", "");
			map.put("useWayO02_01_1", "");
			map.put("purposeG01_01_2", "");
			map.put("lmtPeriodE", "");
			map.put("cpStr", "");
			map.put("feeH", "");
			map.put("rangeH", "");
			map.put("wayH", "");		
			map.put("OtherTerms", "");
			map.put("seqOT", "0");
			
			// 撈資料
			L140M01A l140m01a = null;
			l140m01a = lms9990Service.findL140M01ABymainId(mainId);
			if (l140m01a == null)
				l140m01a = new L140M01A();
			L120M01C l120m01c = l140m01a.getL120m01c();
			if (l120m01c == null)
				l120m01c = new L120M01C();
			L120M01A l120m01a = lms9990Service.findL120M01ABymainId(l120m01c.getMainId());
			if (l120m01a == null)
				l120m01a = new L120M01A();
			
			L120S01A l120s01a = null;
			l120s01a = lms9990Service.findL120s01aByUniqueKey(l120m01a.getMainId(),
					l140m01a.getCustId(), l140m01a.getDupNo());
			if (l120s01a == null)
				l120s01a = new L120S01A();
			
			L120S01B l120s01b = null;
			l120s01b = lms9990Service.findL120s01bByUniqueKey(l120m01a.getMainId(),
					l140m01a.getCustId(), l140m01a.getDupNo());
			if (l120s01b == null)
				l120s01b = new L120S01B();
			
			HashMap<String, String[]> hasItem = this.getHasItem(l140m01a);
			HashMap<String, String> hasItemYN = new HashMap<String, String>();
			HashMap<String, String> hasItemFirst = new HashMap<String, String>();
			for(String item : hasItem.keySet()){
				String[] data = hasItem.get(item);
				String yn = data[0];
				String first = data[1];
				hasItemYN.put(item, yn);
				hasItemFirst.put(item, first);
			}
			
			List<Map<String, Object>> rows = misMislnratService.findMislnratByLRRate("S8", "TWD");
			String baseRate = "";
			if(rows.size() > 0){
				baseRate = rows.get(0).get("LR_RATE").toString();
			}
			String baseRateStr = new NumericFormatter("##.#####").reformat(baseRate);
			
			String reuse = Util.trim(l140m01a.getReUse());
			int reuseInt = Util.parseInt(reuse) -1;
			String reuseStr = (Util.equals(reuse, UtilConstants.Cntrdoc.ReUse.不循環使用) ? 
					yesNoMap.get(Integer.toString(reuseInt)) : "");
			
			List<L140M01F> l140m01fs = lms9990Service.findL140m01fByMainId(mainId);
			
			// 塞值
			map.put("L140M01A.custName", l140m01a.getCustName());
			Map<String, Object> map0024 = misCustdataService.findByIdDupNo(
					Util.trim(l120s01a.getCustId()), Util.trim(l120s01a.getDupNo()));
			if (map0024 != null) {
				map.put("L120S01A.custName", Util.trim(map0024.get("CNAME")));
				map.put("L120S01A.custId", Util.trim(map0024.get("CUSTID")) + " " + Util.trim(map0024.get("DUPNO")));
				map.put("L120S01B.cmpAddr", Util.trim(map0024.get("FULLADDR")));
			} else {
				map.put("L120S01A.custName", Util.nullToSpace(l120s01a.getCustName()));
				map.put("L120S01A.custId", Util.nullToSpace(l120s01a.getDupNo()) + " " + Util.nullToSpace(l120s01a.getDupNo()));
				map.put("L120S01B.cmpAddr", Util.nullToSpace(l120s01b.getCmpAddr()));
			}
			map.put("L120S01B.chairman", Util.nullToSpace(l120s01b.getChairman()));
			IBranch branch = branchService.getBranch(l120m01a.getCaseBrId());
			map.put("brBoss", userInfoService.getUserName(branch.getBrnMgr()));
			brMap = misdbBASEService.findSYNBANK(l120m01a.getCaseBrId());
			if (brMap != null) {
				map.put("brAddr", Util.trim(brMap.get("BRNADDR")));
			}
			
			int hasCount = 0;
			for(String item : hasItemYN.keySet()){
				String has = hasItemYN.get(item);
				if(Util.equals(has, "Y")){
					hasCount++;
					//     /////1AAAAAUAAcAVABlAHgAdAA4ADYAOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==
					// 4UAAAAUAAYAQwBoAGUAYwBrADMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
					map.put("item" + item, "■");
					map.put("reUse" + item, reuseStr);
					
					String loanAmt = "";
					String tempCurr = "";
					String firstLoanTP = hasItemFirst.get(item);
					List<L140M01D> l140m01ds = lms9990Service.findL140m01dByMainIdAndLmtTypeAndSubject(
							mainId, "1", firstLoanTP);
					L140M01D l140m01d = null;		// 有資料取限額	無資料用現請額度
					if (l140m01ds != null && !l140m01ds.isEmpty()) {
						l140m01d = l140m01ds.get(0);
						if(Util.equals(item, CtrConstants.L999S01BItemType.貼現) || Util.equals(item, CtrConstants.L999S01BItemType.透支)) {
							// D.E 範本上寫死為新台幣
						} else {
//							tempCurr = Util.trim(l140m01d.getLmtCurr()) + " ";
							tempCurr = Util.trim(l140m01d.getLmtCurr());
						}
//						loanAmt = tempCurr + NumConverter.addComma(Util.parseBigDecimal(l140m01d.getLmtAmt()));
						loanAmt = MapUtils.getString(currMap, tempCurr, tempCurr) + " " 
									+ NumConverter.toChineseNumber(Util.parseBigDecimal(l140m01d.getLmtAmt())) + "元整";
					} else {
						l140m01d = new L140M01D();
						if(Util.equals(item, CtrConstants.L999S01BItemType.貼現) || Util.equals(item, CtrConstants.L999S01BItemType.透支)) {
							// D.E 範本上寫死為新台幣
						} else {
//							tempCurr = Util.trim(l140m01a.getCurrentApplyCurr()) + " ";
							tempCurr = Util.trim(l140m01a.getCurrentApplyCurr());
						}
//						loanAmt = tempCurr + NumConverter.addComma(Util.parseBigDecimal(l140m01a.getCurrentApplyAmt()));
						loanAmt = MapUtils.getString(currMap, tempCurr, tempCurr) + " " 
									+ NumConverter.toChineseNumber(Util.parseBigDecimal(l140m01a.getCurrentApplyAmt())) + "元整";
					}
					map.put("loanAmt" + item, loanAmt);
					
					// 利息計付
					if (l140m01fs != null && !l140m01fs.isEmpty()) {
						for (L140M01F l140m01f : l140m01fs) {
							String[] TPList = Util.trim(l140m01f.getLoanTPList()).split(UtilConstants.Mark.SPILT_MARK);
							if(Arrays.asList(TPList).contains(firstLoanTP)){
								/*
								List<L140M01N> l140m01ns = lms9990Service.findL140m01nByMainIdAndRateSeqOrderBy(l140m01a.getMainId(), l140m01f.getRateSeq());
								StringBuffer sbUSD = new StringBuffer();
								int seqUSD = 0;
								StringBuffer sbTWD = new StringBuffer();
								int seqTWD = 0;
								StringBuffer sbOTH = new StringBuffer();
								int seqOTH = 0;
								boolean isA = (Util.equals(item, "A") ? true : false);	// 購料借款
								String[] rgiArr = new String[] { "", "", "" };// rateGetInt		TWD > USD > OTH
								Set<String> typeSet = new HashSet<String>();
								for (L140M01N l140m01n : l140m01ns) {
									String rateType = Util.trim(l140m01n.getRateType());
									String rateCurr = MapUtils.getString(moneyMap, rateType, "OTH");
									typeSet.add(rateCurr);
								}
								int typeCount = typeSet.size();
								boolean nextLevel = (typeCount > 1 ? true : false);
								for (L140M01N l140m01n : l140m01ns) {
									String rateType = Util.trim(l140m01n.getRateType());
									String rateCurr = MapUtils.getString(moneyMap, rateType, "OTH");
									String rateDscr = Util.trim(l140m01n.getRateDscr());
									String rateGetInt = Util.trim(l140m01n.getRateGetInt());
									if(Util.equals(rateCurr, "USD")){
										seqUSD++;
										sbUSD.append((sbUSD.length() > 0 ? 換行符號 : ""));
										sbUSD.append((seqUSD > 1 ? ((isA||nextLevel) ? ("(" + seqUSD + ")") : (seqUSD + ". ")) : "")).append(rateDscr);
										rgiArr[1] = rgiArr[1] + (seqOTH > 1 ? "、" : "") + rateGetIntMap.get(rateGetInt);
									} else if(Util.equals(rateCurr, "TWD")) {
										seqTWD++;
										sbTWD.append((sbTWD.length() > 0 ? 換行符號 : ""));
										sbTWD.append((seqTWD > 1 ? ((isA||nextLevel) ? ("(" + seqTWD + ")") : (seqTWD + ". ")) : "")).append(rateDscr);
										rgiArr[0] = rgiArr[0] + (seqTWD > 1 ? "、" : "") + rateGetIntMap.get(rateGetInt);
									} else {
										seqOTH++;
										sbOTH.append((sbOTH.length() > 0 ? 換行符號 : ""));
										sbOTH.append((seqOTH > 1 ? ((isA||nextLevel) ? ("(" + seqOTH + ")") : (seqOTH + ". ")) : "")).append(
												(Util.equals(rateCurr, "OTH") ? "雜幣" : currMap.get(rateCurr)) + "：").append(rateDscr);
										rgiArr[2] = rgiArr[2] + (seqOTH > 1 ? "、" : "") + rateGetIntMap.get(rateGetInt);
									}
								}
								if(seqUSD > 1){
//									sbUSD.insert(0, 換行符號 + "1. ");
									sbUSD.insert(0, ((isA||nextLevel) ? ((isA ? "" : 換行符號) + "(1)") : "1. "));
								}
								if(seqTWD > 1){
//									sbTWD.insert(0, 換行符號 + "1. ");
									sbTWD.insert(0, ((isA||nextLevel) ? ((isA ? "" : 換行符號) + "(1)") : "1. "));
								}
								if(seqOTH > 1){
//									sbOTH.insert(0, 換行符號 + "1. ");
									sbOTH.insert(0, ((isA||nextLevel) ? ((isA ? "" : 換行符號) + "(1)") : "1. "));
								}
								*/

								String[] arrStr = this.getInterest(l140m01a, l140m01f, item, moneyMap, rateGetIntMap, currMap);
								
								if(Util.equals(item, CtrConstants.L999S01BItemType.購料借款)){
//									map.put("interestA_USD", sbUSD.toString());
//									map.put("interestA_TWD", sbTWD.toString());
//									map.put("interestA_OTH", sbOTH.toString());
//									String rgiStr = rgiArr[0] + (rgiArr[0].length() > 0 ? "；" : "") + rgiArr[1] + (rgiArr[1].length() > 0 ? "；" : "") + rgiArr[2];
									map.put("interestA_USD", arrStr[0]);
									map.put("interestA_TWD", arrStr[1]);
									map.put("interestA_OTH", arrStr[2]);
									String rgiStr = arrStr[3] + (arrStr[3].length() > 0 ? "；" : "") + arrStr[4] + (arrStr[4].length() > 0 ? "；" : "") + arrStr[5];
									map.put("rateGetInt", rgiStr);
								} else {
									StringBuffer sbAll = new StringBuffer();
									int listCount = 0;
//									if(sbUSD.length() > 0){
									if (arrStr[0].length() > 0) {
										listCount++;
										sbAll.append(Util.toFullCharString(Integer.toString(listCount)) + "、" + currMap.get("USD") + "：").append(arrStr[0]);
									}
//									if(sbTWD.length() > 0){
									if (arrStr[1].length() > 0) {
										listCount++;
										sbAll.append((sbAll.length() > 0 ? 換行符號 : ""));
										sbAll.append(Util.toFullCharString(Integer.toString(listCount)) + "、" + currMap.get("TWD") + "：").append(arrStr[1]);
									}
//									if(sbOTH.length() > 0){
									if (arrStr[2].length() > 0) {
										listCount++;
										sbAll.append((sbAll.length() > 0 ? 換行符號 : ""));
//										sbAll.append(Util.toFullCharString(Integer.toString(listCount)) + "、其他外幣：").append(sbOTH);
										sbAll.append(arrStr[2]);
									}
									if(listCount < 2){
										String allStr = sbAll.toString();
										allStr = allStr.replaceFirst(Util.toFullCharString(Integer.toString(listCount)) + "、", "");
										sbAll.setLength(0);
										sbAll.append(allStr);
									} else {
//										sbAll.insert(0, 換行符號);
									}
									map.put("interest" + item, sbAll.toString());
								}
								
								if(Util.equals(item, CtrConstants.L999S01BItemType.購料借款) 
										|| Util.equals(item, CtrConstants.L999S01BItemType.委任票據保證)){
									L140M01H l140m01h = lms9990Service.findL140m01hByUniqueKey(l140m01a.getMainId(), l140m01f.getRateSeq());
									if (l140m01h != null) {
										String paType = l140m01h.getPaType();
										if(Util.isNotEmpty(paType) && Util.notEquals(paType, "c") 
												&& Util.equals(item, CtrConstants.L999S01BItemType.購料借款)){
											String pa = "";
											if(Util.equals(paType, "1")){
//												pa = "年費率" + NumConverter.numberToChinese(df.format(Util.parseBigDecimal(l140m01h.getPa1Rate()))) 
//													+ "％，以每3個月為一期，按期計收，" + (Util.equals(Util.trim(l140m01h.getPa1MD()), "5") ? 
//															monTypeMap.get(Util.trim(l140m01h.getPa1MD())).replace("X", NumConverter.numberToChinese(l140m01h.getPa1Mon())) : 
//																monTypeMap.get(Util.trim(l140m01h.getPa1MD())));
												pa = "年費率" + df.format(Util.parseBigDecimal(l140m01h.getPa1Rate()))
													+ "％，以每3個月為一期，按期計收，" + (Util.equals(Util.trim(l140m01h.getPa1MD()), "5") ? 
															monTypeMap.get(Util.trim(l140m01h.getPa1MD())).replace("X", Integer.toString(l140m01h.getPa1Mon())) 
															: monTypeMap.get(Util.trim(l140m01h.getPa1MD())));
											} else if(Util.equals(paType, "2")){
//												pa = "年費率" + NumConverter.numberToChinese(df.format(Util.parseBigDecimal(l140m01h.getPa2Rate()))) 
//													+ "％，按實際承兌日數計收，" + (Util.equals(Util.trim(l140m01h.getPa2MD()), "5") ? 
//														monTypeMap.get(Util.trim(l140m01h.getPa2MD())).replace("X", NumConverter.numberToChinese(l140m01h.getPa2Mon())) : 
//															monTypeMap.get(Util.trim(l140m01h.getPa2MD())));
												pa = "年費率" + df.format(Util.parseBigDecimal(l140m01h.getPa2Rate()))
													+ "％，按實際承兌日數計收，" + (Util.equals(Util.trim(l140m01h.getPa2MD()), "5") ? 
														monTypeMap.get(Util.trim(l140m01h.getPa2MD())).replace("X", Integer.toString(l140m01h.getPa2Mon()))
														: monTypeMap.get(Util.trim(l140m01h.getPa2MD())));
											} else if(Util.equals(paType, "3")){
												pa = "依規定計收";
											} else if(Util.equals(paType, "4")){
												pa = l140m01h.getPaDes();
											}
											map.put("paStr", pa);
										}
										
										String cpType = l140m01h.getCpType();
										if(Util.isNotEmpty(cpType) && Util.notEquals(cpType, "c") 
												&& Util.equals(item, CtrConstants.L999S01BItemType.委任票據保證)){
											String cp = "";
											if(Util.equals(cpType, "1")){
//												cp = "年費率" + NumConverter.numberToChinese(df.format(Util.parseBigDecimal(l140m01h.getCp1Rate())))
//													+ "％，每筆最低收費 新台幣" + NumConverter.numberToChinese(l140m01h.getCp1Fee()) + "元。";
												cp = "年費率" + df.format(Util.parseBigDecimal(l140m01h.getCp1Rate()))
													+ "％，每筆最低收費 新台幣" + l140m01h.getCp1Fee()+ "元。";
											} else if(Util.equals(cpType, "2")){
//												cp = "年費率" + NumConverter.numberToChinese(df.format(Util.parseBigDecimal(l140m01h.getCp2Rate1()))) 
//													+ "％，若由本行簽證承銷，則保證費率為年費率" + NumConverter.numberToChinese(
//															df.format(Util.parseBigDecimal(l140m01h.getCp2Rate2()))) + "。";
												cp = "年費率" + df.format(Util.parseBigDecimal(l140m01h.getCp2Rate1()))
													+ "％，若由本行簽證承銷，則保證費率為年費率"
													+ df.format(Util.parseBigDecimal(l140m01h.getCp2Rate2())) + "。";
											} else if(Util.equals(cpType, "3")){
												cp = l140m01h.getCpDes();
											}
											map.put("cpStr", cp);
										}
									}
								}
								
								break;
							}
						}
					}
					
					map = this.setItemData(map, item, firstLoanTP, l140m01a);
				} else {
					map.put("item" + item, "□");
				}
			}
			map.put("itemTypeCount", Integer.toString(hasCount));
//			map.put("totLoanAmt", Util.trim(l140m01a.getCurrentApplyCurr()) + " " 
//					+ NumConverter.addComma(Util.parseBigDecimal(l140m01a.getCurrentApplyAmt())));
			map.put("totLoanAmt", MapUtils.getString(currMap, Util.trim(l140m01a.getCurrentApplyCurr()), 
					Util.trim(l140m01a.getCurrentApplyCurr())) + " " + 
					NumConverter.toChineseNumber(Util.parseBigDecimal(l140m01a .getCurrentApplyAmt())) + "元整");
			map.put("useDeadline", LMSUtil.getUseDeadline(Util.nullToSpace(l140m01a.getUseDeadline()), 
					Util.nullToSpace(l140m01a.getDesp1()), MessageBundleScriptCreator.getComponentResource(LMSCommomPage.class)));
//			map.put("baseRate", NumConverter.numberToChinese(Util.parseBigDecimal(baseRateStr).toString()));
			map.put("baseRate", Util.parseBigDecimal(baseRateStr).toString());
			map.put("dMonth1", NumConverter.numberToChinese("6"));
			map.put("dMonth2", NumConverter.numberToChinese("6"));
			map.put("dRate1", NumConverter.numberToChinese("10"));
			map.put("dRate2", NumConverter.numberToChinese("20"));
			map.put("dRateAdd1", NumConverter.numberToChinese("1"));
			map.put("dRateAdd2", NumConverter.numberToChinese("1.5"));
			
			// 其他未盡事項
			StringBuffer sbOtherTerms = new StringBuffer();
			int seqOT = 0;
			if(map.get("OtherTerms") != null){
				sbOtherTerms.append(map.get("OtherTerms"));
			}
			if(map.get("seqOT") != null){
				seqOT = Integer.parseInt(map.get("seqOT"));
			}
			if(sbOtherTerms.length() > 0){
				if(seqOT > 1){
					sbOtherTerms.insert(0, "三、其他商議條款：" + 換行符號
							+ (sbOtherTerms.toString().startsWith("1. ") ? "" : "1. "));
				} else {
					sbOtherTerms.insert(0, "三、其他商議條款：");
				}
			}
			map.put("OtherTerms", sbOtherTerms.toString());
//			map.put("seqOT", Integer.toString(seqOT));
			if(map.get("loanAmtG") != null){
				map.remove("loanAmtG");
			}
			if(map.get("interestG") != null){
				map.remove("interestG");
			}
			if(map.get("seqOT") != null){
				map.remove("seqOT");
			}
			
			map = this.replaceMapContStr(map); // 取代特定文字
			baos = this.writeWordContent(content, map);

		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			logger.error(e.getMessage());
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} finally {
			if (map != null) {
				map.clear();
			}
		}
		return baos;
	}
	
	public HashMap<String, String[]> getHasItem(L140M01A l140m01a){
		HashMap<String, String[]> result = new HashMap<String, String[]>();
		HashMap<String, String[]> type = this.initTypeMap();	// 對應樣版

		HashMap<String, String[]> bizCatMap = new HashMap<String, String[]>();	// 對應樣版的科目
		bizCatMap.put(CtrConstants.L999S01BItemType.購料借款, new String[]{});
		bizCatMap.put(CtrConstants.L999S01BItemType.外銷借款, new String[]{});
		bizCatMap.put(CtrConstants.L999S01BItemType.營運週轉借款, new String[]{});
		bizCatMap.put(CtrConstants.L999S01BItemType.貼現, new String[] {});
		bizCatMap.put(CtrConstants.L999S01BItemType.透支, new String[]{});
		bizCatMap.put(CtrConstants.L999S01BItemType.委任票據保證, new String[]{});
		bizCatMap.put(CtrConstants.L999S01BItemType.委任票據承兌, new String[] { "701", "702", "801", "802" });
		bizCatMap.put(CtrConstants.L999S01BItemType.委任保證, new String[]{});

		// 取得樣版與科目對應
		String bizCat_LoanTP = Util.trim(lmsService.getSysParamDataValue("LMS_BIZCAT_LOANTP"));
		if (Util.notEquals(bizCat_LoanTP, "")) {
			JSONObject jsonBizCat_LoanTP = JSONObject.fromObject("{" + bizCat_LoanTP + "}");
			if (jsonBizCat_LoanTP != null) {
				for(String typeKey : type.keySet()){
					HashSet<String> loanTPs = new HashSet<String>();
					String[] bizCatArr = type.get(typeKey);
					for(String bc : bizCatArr){
						String loanTPList = Util.trim(jsonBizCat_LoanTP.get(bc));
						if (Util.isNotEmpty(loanTPList)) {
							String[] loanTPArr = StringUtils.split(loanTPList, "|");
							for (String loanTP : loanTPArr) {
								loanTPs.add(loanTP);
							}
						}
					}
					bizCatMap.put(typeKey, loanTPs.toArray(new String[loanTPs.size()]));
				}
			}
		}

		List<L140M01C> l140m01cs = lms9990Service.findL140m01cListByMainId(l140m01a.getMainId());
		for(String itemType : bizCatMap.keySet()){
			result.put(itemType, new String[] { "N", "" });
			String[] codeArr = bizCatMap.get(itemType);
			for (L140M01C l140m01c : l140m01cs) {//l140m01a.getL140m01c()) {
				String key = Util.getLeftStr(l140m01c.getLoanTP(), 3);
				if(Arrays.asList(codeArr).contains(key)){
					result.put(itemType, new String[] { "Y", l140m01c.getLoanTP() });
					break;
				}
			}
		}

		return result;
	}
	
	public HashMap<String, String[]> initTypeMap(){
		HashMap<String, String[]> type = new HashMap<String, String[]>();	// 對應樣版
		type.put(CtrConstants.L999S01BItemType.購料借款, new String[] { "B" });
		type.put(CtrConstants.L999S01BItemType.外銷借款, new String[] { "C" });
		type.put(CtrConstants.L999S01BItemType.營運週轉借款, new String[] { "A" });
		type.put(CtrConstants.L999S01BItemType.貼現, new String[] { "O" });
		type.put(CtrConstants.L999S01BItemType.透支, new String[] { "G" });
		type.put(CtrConstants.L999S01BItemType.委任票據保證, new String[] { "F" });
//		type.put(CtrConstants.L999S01BItemType.委任票據承兌, new String[] { "" });
		type.put(CtrConstants.L999S01BItemType.委任保證, new String[] { "D", "E" });
		return type;
	}
	
	public Map<String, String> setItemData(Map<String, String> map, String item, String firstLoanTP,
			L140M01A l140m01a){
		DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,###,###,##0.####");
		HashMap<String, String[]> type = this.initTypeMap();	// 對應樣版
		if(Util.equals(item, CtrConstants.L999S01BItemType.購料借款)){			
			List<L140S09A> l140s09as_B03 = lmsService.findL140s09a(l140m01a.getMainId(), "", "B", "03");
			for (L140S09A l140s09a : l140s09as_B03) {
				List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util.trim(l140s09a.getOid()));
				for (L140S09B l140s09b : l140s09bs) {
					String contNo = l140s09b.getContNo();
					if(Util.equals(contNo, "01")){
						String str = Util.trim(l140s09b.getPeriodB03_01_1());
						if(str.endsWith("天")){
							str = str.substring(0, str.length() - 1);
																		 
						}
						map.put("periodB05_01_1", str);
						map.put("periodB05_01_2", str);
						map.put("periodB05_01_3", str);
						map.put("periodB05_02_1", str);
					}
//					if(Util.equals(contNo, "01")){
//						map.put("periodB05_01_1", NumConverter.toChineseNumber(Util.trim(l140s09b.getPeriodB05_01_1())));
//						map.put("periodB05_01_2", NumConverter.toChineseNumber(Util.trim(l140s09b.getPeriodB05_01_2())));
//						map.put("periodB05_01_3", NumConverter.toChineseNumber(Util.trim(l140s09b.getPeriodB05_01_3())));
//					} else if(Util.equals(contNo, "02")){
//						map.put("periodB05_02_1", NumConverter.toChineseNumber(Util.trim(l140s09b.getPeriodB05_02_1())));
//					}
				}
			}
			// 檢查清償期限是否有樣版資料，若無則使用科目清償期限
			if (Util.isEmpty(map.get("periodB05_01_1"))) {
				L140M01C l140m01c = lms9990Service.findL140m01cByUniqueKey(
						l140m01a.getMainId(), firstLoanTP);
				if (Util.notEquals(l140m01c.getLmtOther(), "1")
						&& Util.isNotEmpty(Util.trim(l140m01c.getLmtDays()))) {
//					String str = NumConverter.toChineseNumber(Util.trim(l140m01c.getLmtDays()));
					String str = Util.trim(l140m01c.getLmtDays());
					map.put("periodB05_01_1", str);
					map.put("periodB05_01_2", str);
					map.put("periodB05_01_3", str);
					map.put("periodB05_02_1", str);
				}
			}
			
			List<L140S09A> l140s09as_B02 = lmsService.findL140s09a(l140m01a.getMainId(), "", "B", "02");
			for (L140S09A l140s09a : l140s09as_B02) {
				List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util.trim(l140s09a.getOid()));
				for (L140S09B l140s09b : l140s09bs) {
					String contNo = l140s09b.getContNo();
					if(Util.equals(contNo, "01")){
						map.put("useWayB02_01_1", NumConverter.numberToChinese(df.format(Util.parseBigDecimal(l140s09b.getUseWayB02_01_1()))));
						map.put("useWayB02_01_2", NumConverter.toChineseNumber(Util.trim(l140s09b.getUseWayB02_01_2())));
					}
				}
			}
		} else if(Util.equals(item, CtrConstants.L999S01BItemType.外銷借款) 
					|| Util.equals(item, CtrConstants.L999S01BItemType.貼現)){
			L140M01C l140m01c = lms9990Service.findL140m01cByUniqueKey(l140m01a.getMainId(), firstLoanTP);
			if(Util.notEquals(l140m01c.getLmtOther(), "1") && Util.isNotEmpty(Util.trim(l140m01c.getLmtDays()))){
				map.put("lmtDays" + item, NumConverter.toChineseNumber(Util.trim(l140m01c.getLmtDays())));
			}
			
			if(Util.equals(item, CtrConstants.L999S01BItemType.貼現)){
				List<L140S09A> l140s09as_O02 = lmsService.findL140s09a(l140m01a.getMainId(), "", "O", "02");
				for (L140S09A l140s09a : l140s09as_O02) {
					List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util.trim(l140s09a.getOid()));
					for (L140S09B l140s09b : l140s09bs) {
						String contNo = l140s09b.getContNo();
						if(Util.equals(contNo, "01")){
							map.put("useWayO02_01_1", NumConverter.numberToChinese(df.format(Util.parseBigDecimal(l140s09b.getUseWayO02_01_1()))));
						}
					}
				}
			}
		} else if(Util.equals(item, CtrConstants.L999S01BItemType.營運週轉借款)){
			List<L140S09A> l140s09as_A04 = lmsService.findL140s09a(l140m01a.getMainId(), "", "A", "04");
//			map.put("lmtPeriod" + item, this.getCont(l140s09as_A04));
			String[] arrA04 = this.getCont(l140s09as_A04, 0);
			map.put("lmtPeriod" + item, arrA04[0]);
			
			List<L140S09A> l140s09as_A02 = lmsService.findL140s09a(l140m01a.getMainId(), "", "A", "02");
//			map.put("useWay" + item, this.getCont(l140s09as_A02));
			String[] arrA02 = this.getCont(l140s09as_A02, 0);
			map.put("useWay" + item, arrA02[0]);
		} else if(Util.equals(item, CtrConstants.L999S01BItemType.透支)){
			List<L140S09A> l140s09as_G01 = lmsService.findL140s09a(l140m01a.getMainId(), "", "G", "01");
			for (L140S09A l140s09a : l140s09as_G01) {
				List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util.trim(l140s09a.getOid()));
				for (L140S09B l140s09b : l140s09bs) {
					String contNo = l140s09b.getContNo();
					if(Util.equals(contNo, "01")){
						map.put("purposeG01_01_2", NumConverter.numberToChinese(Util.trim(l140s09b.getPurposeG01_01_2())));
					}
				}
			}
			
			List<L140S09A> l140s09as_G04 = lmsService.findL140s09a(l140m01a.getMainId(), "", "G", "04");
//			map.put("lmtPeriod" + item, this.getCont(l140s09as_G04));
			String[] arr = this.getCont(l140s09as_G04, 0);
			map.put("lmtPeriod" + item, arr[0]);
		} else if(Util.equals(item, CtrConstants.L999S01BItemType.委任保證)){
			// 兩個以上樣板
			String[] bizCatArr = type.get(CtrConstants.L999S01BItemType.委任保證);
			StringBuffer sbFee = new StringBuffer();
			StringBuffer sbRange = new StringBuffer();
			StringBuffer sbWay = new StringBuffer();
			int feeCount = 0;
			int rangeCount = 0;
			int wayCount = 0;
			for(String bizCat : bizCatArr){
				Map<String, String> feeMap = new HashMap<String, String>();
				feeMap.put("D", "06");
				feeMap.put("E", "04");
				List<L140S09A> l140s09asFee = lmsService.findL140s09a(l140m01a.getMainId(), "", bizCat, feeMap.get(bizCat));
				sbFee.append((sbFee.length() > 0 ? 換行符號 : ""));
//				sbFee.append(this.getCont(l140s09asFee));
				String[] feeArr = this.getCont(l140s09asFee, feeCount);
				sbFee.append(feeArr[0]);
				feeCount = feeCount + Integer.parseInt(feeArr[1]);
				
				Map<String, String> rangeMap = new HashMap<String, String>();
				rangeMap.put("D", "07");
				rangeMap.put("E", "05");
				List<L140S09A> l140s09asRange = lmsService.findL140s09a(l140m01a.getMainId(), "", bizCat, rangeMap.get(bizCat));
				sbRange.append((sbRange.length() > 0 ? 換行符號 : ""));
//				sbRange.append(this.getCont(l140s09asRange));
				String[] rangeArr = this.getCont(l140s09asRange, rangeCount);
				sbRange.append(rangeArr[0]);
				rangeCount = rangeCount + Integer.parseInt(rangeArr[1]);
				
				Map<String, String> wayMap = new HashMap<String, String>();
				wayMap.put("D", "08");
				wayMap.put("E", "06");
				List<L140S09A> l140s09asWay = lmsService.findL140s09a(l140m01a.getMainId(), "", bizCat, wayMap.get(bizCat));
				sbWay.append((sbWay.length() > 0 ? 換行符號 : ""));
//				sbWay.append(this.getCont(l140s09asWay));
				String[] wayArr = this.getCont(l140s09asWay, wayCount);
				sbWay.append(wayArr[0]);
				wayCount = wayCount + Integer.parseInt(wayArr[1]);
			}
			if(feeCount > 1){
				if(!sbFee.toString().startsWith("1. ")){
					sbFee.insert(0, "1. ");
				}
			}
			if(rangeCount > 1){
				if(!sbRange.toString().startsWith("1. ")){
					sbRange.insert(0, "1. ");
				}
			}
			if(wayCount > 1){
				if(!sbWay.toString().startsWith("1. ")){
					sbWay.insert(0, "1. ");
				}
			}
			map.put("fee" + item, sbFee.toString());
			map.put("range" + item, sbRange.toString());
			map.put("way" + item, sbWay.toString());
		}
		
		// 其他未盡事項
		if(Util.notEquals(item, "G")){		// 排除 委任票據承兌
			Map<String, String> initOthMap = new HashMap<String, String>(); // 樣版的未盡事項bizItem
			initOthMap.put("A", "03");
			initOthMap.put("B", "04");
			initOthMap.put("C", "02");
			initOthMap.put("D", "05");
			initOthMap.put("E", "03");
			initOthMap.put("F", "02");
			initOthMap.put("G", "03");
			initOthMap.put("H", "07");
			initOthMap.put("I", "07");
			initOthMap.put("J", "06");
			initOthMap.put("N", "07");
			initOthMap.put("O", "03");
			StringBuffer sbOtherTerms = new StringBuffer();
			int seqOT = 0;
			if(map.get("OtherTerms") != null){
				sbOtherTerms.append(map.get("OtherTerms"));
			}
			if(map.get("seqOT") != null){
				seqOT = Integer.parseInt(map.get("seqOT"));
			}
			String[] bizCatArr = type.get(item);	// 取得該類別對應之樣版
			for (String bizCat : bizCatArr) {
				List<L140S09A> l140s09as = lmsService.findL140s09a(l140m01a.getMainId(), "", bizCat, initOthMap.get(bizCat));
				String[] contArr = this.getCont(l140s09as, seqOT);
				sbOtherTerms.append(((sbOtherTerms.length() > 0  && contArr[0].length() > 0) ? 換行符號 : ""));
				sbOtherTerms.append(contArr[0]);
				seqOT = seqOT + Integer.parseInt(contArr[1]);
			}
			map.put("OtherTerms", sbOtherTerms.toString());
			map.put("seqOT", Integer.toString(seqOT));
		}
		
		return map;
	}
	
//	public String getCont(List<L140S09A> l140s09as){
	public String[] getCont(List<L140S09A> l140s09as, int startSeq){
//		String regex = "\\d*";	// 擷取數字
		String[] strArr = new String[2];
		StringBuffer sb = new StringBuffer();
		int seq = 0;
//		if(startSeq > 0){
//			seq = seq+startSeq;
//		}
		int s09aNum = l140s09as.size();
		boolean firstRount = true;
		for (L140S09A l140s09a : l140s09as) {
			List<L140S09B> l140s09bs = lmsService.findL140s09bByMainId(Util.trim(l140s09a.getOid()));
			int s09bNum = l140s09bs.size();
			if (firstRount && startSeq > 0 && s09aNum > 0 && s09bNum > 0) {
				seq = seq + startSeq;
				firstRount = false;
			}
			for (L140S09B l140s09b : l140s09bs) {
				String str = l140s09b.getCont();
//				Pattern p = Pattern.compile(regex);
//				Matcher m = p.matcher(str);
//				while (m.find()) {
//					if (!"".equals(m.group())){
//						str = str.replaceFirst(m.group(), NumConverter.toChineseNumber(m.group()));
//					}
//				}
				sb.append((sb.length() > 0 ? 換行符號 : ""));
				seq++;
//				sb.append( (s09aNum > 1 || s09bNum > 1) ? (seq + ". ") : "").append(str);
				sb.append( (s09aNum > 1 || s09bNum > 1 || seq > 1 ) ? (seq + ". ") : "").append(str);
			}
		}

//		return sb.toString();
		strArr[0] = sb.toString();
		strArr[1] = Integer.toString(seq);
		return strArr;
	}
	
	public Map<String, String> replaceMapContStr(Map<String, String> map) {
		for (String key : map.keySet()) {
			String contStr = map.get(key);
			contStr = contStr.replaceAll("借戶", "乙方");
			contStr = contStr.replaceAll("借款人", "乙方");
			contStr = contStr.replaceAll("本行", "甲方");
			map.put(key, contStr);
		}
		return map;
	}
	
	public String getFirstLoanTP(String[] bizCatArr, L140M01A l140m01a) {
		String firstLoanTP = "";

		HashSet<String> loanTPs = new HashSet<String>(); // 屬於樣版的科目

		// 取得樣版與科目對應
		String bizCat_LoanTP = Util.trim(lmsService
				.getSysParamDataValue("LMS_BIZCAT_LOANTP"));
		if (Util.notEquals(bizCat_LoanTP, "")) {
			JSONObject jsonBizCat_LoanTP = JSONObject.fromObject("{"
					+ bizCat_LoanTP + "}");
			if (jsonBizCat_LoanTP != null) {
				for (String bizCat : bizCatArr) {
					String loanTPList = Util
							.trim(jsonBizCat_LoanTP.get(bizCat));
					if (Util.isNotEmpty(loanTPList)) {
						String[] loanTPArr = StringUtils.split(loanTPList, "|");
						for (String loanTP : loanTPArr) {
							loanTPs.add(loanTP);
						}
					}
				}
			}
		}

		List<L140M01C> l140m01cs = lms9990Service.findL140m01cListByMainId(l140m01a
				.getMainId());
		for (L140M01C l140m01c : l140m01cs) {
			String key = Util.getLeftStr(l140m01c.getLoanTP(), 3);
			if (loanTPs.contains(key)) {
				firstLoanTP = l140m01c.getLoanTP();
				break;
			}
		}

		return firstLoanTP;
	}
	
	public String[] getInterest(L140M01A l140m01a, L140M01F l140m01f,
			String item, HashMap<String, String> moneyMap,
			Map<String, String> rateGetIntMap, Map<String, String> currMap) {
		String[] strArr = new String[6];

		List<L140M01N> l140m01ns = lms9990Service
				.findL140m01nByMainIdAndRateSeqOrderBy(l140m01a.getMainId(),
						l140m01f.getRateSeq());
		StringBuffer sbUSD = new StringBuffer();
		int seqUSD = 0;
		StringBuffer sbTWD = new StringBuffer();
		int seqTWD = 0;
		StringBuffer sbOTH = new StringBuffer();
		int seqOTH = 0;
		boolean isA = (Util.equals(item, "A") ? true : false); // 購料借款
		String[] rgiArr = new String[] { "", "", "" };// rateGetInt TWD > USD > OTH
		Set<String> typeSet = new HashSet<String>();
		for (L140M01N l140m01n : l140m01ns) {
			String rateType = Util.trim(l140m01n.getRateType());
			String rateCurr = MapUtils.getString(moneyMap, rateType, "OTH");
			typeSet.add(rateCurr);
		}
		int typeCount = typeSet.size();
		boolean nextLevel = (typeCount > 1 ? true : false);
		int seqCurr = 0;
		for (L140M01N l140m01n : l140m01ns) {
			String rateType = Util.trim(l140m01n.getRateType());
			String rateCurr = MapUtils.getString(moneyMap, rateType, "OTH");
			String rateDscr = this.setL140M01NStr(l140m01n);//Util.trim(l140m01n.getRateDscr());
			String rateGetInt = Util.trim(l140m01n.getRateGetInt());
			if (Util.equals(rateCurr, "USD")) {
				if(seqUSD == 0){
					seqCurr++;
				}
				seqUSD++;
				sbUSD.append((sbUSD.length() > 0 ? 換行符號 : ""));
				sbUSD.append((seqUSD > 1 ? ((isA || nextLevel) ? ("(" + seqUSD + ")")
								: (seqUSD + ". "))
								: "")).append(rateDscr);
				rgiArr[1] = rgiArr[1] + (seqOTH > 1 ? "、" : "")
						+ rateGetIntMap.get(rateGetInt);
			} else if (Util.equals(rateCurr, "TWD")) {
				if(seqTWD == 0){
					seqCurr++;
				}
				seqTWD++;
				sbTWD.append((sbTWD.length() > 0 ? 換行符號 : ""));
				sbTWD.append(
						(seqTWD > 1 ? ((isA || nextLevel) ? ("(" + seqTWD + ")")
								: (seqTWD + ". "))
								: "")).append(rateDscr);
				rgiArr[0] = rgiArr[0] + (seqTWD > 1 ? "、" : "")
						+ rateGetIntMap.get(rateGetInt);
			} else {
				seqOTH++;
				sbOTH.append((sbOTH.length() > 0 ? 換行符號 : ""));
				if(isA){
					sbOTH.append(
							(seqOTH > 1 ? ((isA || nextLevel) ? ("(" + seqOTH + ")")
									: (seqOTH + ". "))
									: ""))
							.append((Util.equals(rateCurr, "OTH") ? "雜幣" : currMap
									.get(rateCurr)) + "：").append(rateDscr);
				} else {
					seqCurr++;
					sbOTH.append(Util.toFullCharString(Integer.toString(seqCurr)) + "、"	
							+ ((Util.equals(rateCurr, "OTH") ? "雜幣" : currMap
									.get(rateCurr)) + "：")).append(rateDscr);
				}
				rgiArr[2] = rgiArr[2] + (seqOTH > 1 ? "、" : "")
						+ rateGetIntMap.get(rateGetInt);
			}
		}
		if (seqUSD > 1) {
			sbUSD.insert(0, ((isA || nextLevel) ? ((isA ? "" : 換行符號) + "(1)")
					: "1. "));
		}
		if (seqTWD > 1) {
			sbTWD.insert(0, ((isA || nextLevel) ? ((isA ? "" : 換行符號) + "(1)")
					: "1. "));
		}
		if (isA && seqOTH > 1) {
			sbOTH.insert(0, ((isA || nextLevel) ? ((isA ? "" : 換行符號) + "(1)")
					: "1. "));
		}

		strArr[0] = sbUSD.toString();
		strArr[1] = sbTWD.toString();
		strArr[2] = sbOTH.toString();
		strArr[3] = rgiArr[0];
		strArr[4] = rgiArr[1];
		strArr[5] = rgiArr[2];

		return strArr;
	}
	
	// 組成 利率 文字 參考 LMS1401ServiceImpl.java setL140M01NStr
	public String setL140M01NStr(L140M01N l140m01n) {
		String rateDscr = "";

		String[] currs = new String[] { "TWD", "USD", "JPY", "EUR", "AUD",
				"HKD", "CNY", "Z" };
		HashMap<String, LinkedHashMap<String, String>> rateMapALL = misMislnratService
				.findBaseRateByCurrs(currs);
		LinkedHashMap<String, String> rateMap = null;
		HashMap<String, String> rateMap2 = lnlnf070Service.getRateBy070();
		String rateType = l140m01n.getRateType();

		// 用來抓070的規則 臺幣為00開頭 其他幣別皆為 99
		String tempBase = "1".equals(rateType) ? "00" : "99";

		if ("Z".equals(rateType)) {
			rateMap = new LinkedHashMap<String, String>(
					codeTypeService.findByCodeType("L140M01N_OtherCurr"));
		} else {
			rateMap = rateMapALL.get(RateTypeEnum.coverToCurr(rateType));
		}
		LinkedHashMap<String, String> PrRatMap = lnlnf070Service
				.getPrRate(RateTypeEnum.coverToCurr(rateType));
		Map<String, CapAjaxFormResult> totalCodeType = codeTypeService
				.findByCodeType(new String[] { "lms1405s0204_count",
						"lms1401s0204_ratePeriod", "lms1405s0204_rateKind",
						"lms1401s0204_rateGetInt",
						"lms1401s0204_rateLimitType",
						"lms1401s0204_rateChgKind",
						"lms1401s0204_primeRatePlan" });

		CapAjaxFormResult countMap = totalCodeType.get("lms1405s0204_count");

		CapAjaxFormResult ratePeriodMap = totalCodeType
				.get("lms1401s0204_ratePeriod");
		CapAjaxFormResult rateKindMap = totalCodeType
				.get("lms1405s0204_rateKind");
		CapAjaxFormResult rateGetIntMap = totalCodeType
				.get("lms1401s0204_rateGetInt");
		CapAjaxFormResult rateLimitTypeMap = totalCodeType
				.get("lms1401s0204_rateLimitType");
		CapAjaxFormResult rateChgKindMap = totalCodeType
				.get("lms1401s0204_rateChgKind");

		String otherMemo = "";
		String recvRate = "";
		String secNoStr = this.getSecNoStr(l140m01n);

		String rateBase = Util.trim(l140m01n.getRateBase());
		StringBuffer intBase = new StringBuffer(0);
		String intBaseU012 = "";
		BigDecimal disYearRate = l140m01n.getDisYearRate();
		if ("01".equals(rateBase)) {
			String 自訂利率基礎key = Util.trim(l140m01n.getPrRate());
			String 自訂利率基礎文字 = Util.trim(PrRatMap.get(自訂利率基礎key));
			if ("U01".equals(自訂利率基礎key) || "U02".equals(自訂利率基礎key)) {
				intBaseU012 = this.forU01U02Str(countMap, l140m01n, intBase);
			} else {
				String 自訂天期key = Util.trim(l140m01n.getRatePeriod());
				String 自訂天期文字 = Util.trim(ratePeriodMap.get(自訂天期key));

				intBase.append("按");
				if ("B000".equals(自訂天期key) || "B001".equals(自訂天期key)) {
					// 無 | B000<br/>
					// 借款同天期|B001<br/>
					if ("B001".equals(自訂天期key)) {
						intBase.append(自訂天期文字);
					}
					// 無自訂利率天期
					intBase.append(自訂利率基礎文字);
				} else {
					// String peroidStr = "";
					String tmpRatePXStr = "";
					String[] spliteRatePeriod = 自訂天期key
							.split(UtilConstants.Mark.SPILT_MARK);
					StringBuffer peroidStrTemp = new StringBuffer(0);
					String mark = "";
					int countRatePeriod = spliteRatePeriod.length;
					for (String px : spliteRatePeriod) {
						--countRatePeriod;
						if (countRatePeriod == 0) {
							mark = "或";
						} else {
							mark = "、";
						}
						if (自訂利率基礎文字.indexOf("６１６５") > 0
								|| 自訂利率基礎文字.indexOf("５１３２８") > 0) {
							// l1401s02p04.msg006=天期
							if ("M".equals(Util.getLeftStr(px, 1))) {
								tmpRatePXStr = Util
										.toFullCharString(Integer.valueOf(Util
												.getRightStr(px, 2))
												* 30
												+ "天期");
								if ("M3".equals(Util.getLeftStr(px, 2))) {
									// l1401s02p04.msg007=平均
									tmpRatePXStr += "平均";
								}

								peroidStrTemp
										.append(peroidStrTemp.length() > 0 ? mark
												: "");
								peroidStrTemp.append(tmpRatePXStr);

							} else {
								peroidStrTemp
										.append(peroidStrTemp.length() > 0 ? mark
												: "");
								peroidStrTemp.append(Util.trim(ratePeriodMap
										.get(px)));
							}
						} else {
							peroidStrTemp
									.append(peroidStrTemp.length() > 0 ? mark
											: "");
							peroidStrTemp.append(Util.trim(ratePeriodMap
									.get(px)));
						}
					}

					intBase.append(自訂利率基礎文字);
					intBase.append(peroidStrTemp);
				}

				if ("C01".equals(自訂利率基礎key) || "U09".equals(自訂利率基礎key)) {
					// 固定利率時，直接顯示固定利率XX%
					intBase.setLength(0);
					// l1401s02p04.msg008=按固定利率
					intBase.append("按固定利率");
					BigDecimal attRate = l140m01n.getAttRate();
					intBase.append(NumConverter.addComma(attRate, ",##0.0####"));
					intBase.append("％");
				} else {
					if (Util.isNotEmpty(l140m01n.getDisYearOp())
							&& Util.isNotEmpty(disYearRate)
							&& BigDecimal.ZERO.compareTo(disYearRate) != 0) {
						String DisYearOpStr = Util.trim(countMap.get(l140m01n
								.getDisYearOp()));
						intBase.append(DisYearOpStr);
						intBase.append(NumConverter.addComma(disYearRate, ",##0.0####"));
						intBase.append("％");
					}
				}
			}
		} else if ("@2".equals(rateBase)) {
			intBase.append(Util.trim(l140m01n.getOtherRateDrc()));
		} else {
			String[] rateBaseArray = rateBase
					.split(UtilConstants.Mark.SPILT_MARK);
			String groupName = Util.trim(l140m01n.getGroupName());

			if (rateBaseArray.length <= 1
					&& UtilConstants.DEFAULT.是.equals(l140m01n.getRateSetAll())) {
				if (Util.isNotEmpty(groupName)) {
					intBase.append("按借款同天期").append(groupName);
				}
			} else {
				String rateName = "";
				int conutrateBaseArray = rateBaseArray.length;
				// 先將所有值拿出來排序
				LinkedHashMap<String, String> tempMap = new LinkedHashMap<String, String>();
				String rateBaseStr = "";
				for (String key : rateBaseArray) {
					if (rateMap.containsKey(key)) {
						rateBaseStr = rateMap.get(key);
					} else if (rateMap2.containsKey(tempBase + key)) {
						rateBaseStr = rateMap2.get(tempBase + key);
					} else {
						rateBaseStr = key;
					}
					tempMap.put(key, rateBaseStr);
				}

				for (String key : tempMap.keySet()) {

					rateName = tempMap.get(key);
					--conutrateBaseArray;
					if (intBase.length() == 0) {
						intBase.append("按");

						if (Util.isEmpty(groupName)) {
							intBase.append(rateName);
						} else {
							intBase.append(groupName);
							intBase.append(rateName.replace(groupName, ""));
						}
					} else {
						if (conutrateBaseArray != 0) {
							intBase.append("、");
						} else {
							intBase.append("或");
						}
						if (Util.isEmpty(groupName)) {
							intBase.append(rateName);
						} else {
							intBase.append(rateName.replace(groupName, ""));
						}
					}
				}
			}
			if (Util.isNotEmpty(l140m01n.getDisYearOp())
					&& Util.isNotEmpty(disYearRate)
					&& BigDecimal.ZERO.compareTo(disYearRate) != 0) {
				String DisYearOpStr = Util.trim(countMap.get(l140m01n
						.getDisYearOp())) + NumConverter.addComma(disYearRate, ",##0.0####") + "％";
				intBase.append(DisYearOpStr);
			}
			// l1401s02p04.msg010=目前為
			if (UtilConstants.DEFAULT.是.equals(l140m01n.getReRateSelAll())) {
				String nowRate = "(目前為"
						+ NumConverter.addComma(l140m01n.getReRateMin(), ",##0.0####");
				BigDecimal reRateMax = l140m01n.getReRateMax();
				if (Util.isNotEmpty(reRateMax)) {
					nowRate = nowRate + "~" + NumConverter.addComma(reRateMax, ",##0.0####");
				}
				nowRate = nowRate + "％)";
				intBase.append(nowRate);
			}
		}

		String rateMemo = Util.trim(l140m01n.getRateMemo());

		if (Util.isNotEmpty(rateMemo)) {
			rateMemo = "，" + rateMemo;
		}
		StringBuffer intChg = new StringBuffer();
		String rateGetInt = Util.trim(l140m01n.getRateGetInt());
		RateKindEnum rateKind = RateKindEnum.getEnum(l140m01n.getRateKind());
		if (rateKind != null) {
			switch (rateKind) {
			case 固定利率:
				// N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改 U09為固定利率比照C01
				if (!"5".equals(rateGetInt)
						&& !("C01".equals(l140m01n.getPrRate()) || "U09"
								.equals(l140m01n.getPrRate()))) {
					// 固定利率+本息併付 不顯示固定利率
					intChg.append("，").append(
							rateKindMap.get(l140m01n.getRateKind()));
				}
				break;
			case 機動利率:

				break;
			case 定期浮動:
				// * 月 | M<br/>
				// * 年 | Y<br/>
				// l1401s02p04.msg011=每
				// l1401s02p04.msg012=個
				// l1401s02p04.msg013=利率調整乙次

				String rateChgKind = Util.trim(l140m01n.getRateChgKind());
				intChg.append("，")
						.append("每")
						.append(NumConverter.toChineseNumber(l140m01n
								.getRateChg1()));
				String rateChgKindStr = Util.trim(rateChgKindMap
						.get(rateChgKind));
				if ("Y".equals(rateChgKind)) {
					intChg.append(rateChgKindStr).append("利率調整乙次");
				} else if ("M".equals(rateChgKind)) {
					intChg.append("個").append(rateChgKindStr).append("利率調整乙次");
				} else {
					intChg.setLength(0);
				}
			}
		}

		recvRate = "";
		if (Util.isNotEmpty(rateGetInt)) {
			if ("1".equals(rateGetInt)) {
				recvRate = "";
			} else {
				recvRate = "，" + rateGetIntMap.get(rateGetInt);
			}
		}
		otherMemo = Util.trim(l140m01n.getUionMemo());
		if (Util.isNotEmpty(otherMemo)) {
			otherMemo = "，" + otherMemo;
		}
		String taxStr = "";
		String rateTax = Util.trim(l140m01n.getRateTax());
		// l1401s02p04.msg014=除以
		if (Util.isEmpty(rateTax)) {
			taxStr = "";
		} else if ("1".equals(rateTax)) {

			if (Util.equals(Util.trim(l140m01n.getRateTaxCodeDecideFuture()),
					"Y")) {
			} else {
				taxStr = "除以" + NumConverter.addComma(l140m01n.getRateTaxCode(), ",##0.0####");
			}

		} else {
			taxStr = "";
		}

		// l1401s02p04.msg026=目前all-in利率約為
		StringBuffer allInStr = new StringBuffer("");
		if (UtilConstants.DEFAULT.是.equals(l140m01n.getAllInRateSelAll())) {
			allInStr.append("(").append("目前all-in利率約為")
					.append(NumConverter.addComma(l140m01n.getAllInRateMinAf(), ",##0.0####"));
			BigDecimal reRateMax = l140m01n.getAllInRateMaxAf();
			if (Util.isNotEmpty(reRateMax)) {
				allInStr.append("~").append(NumConverter.addComma(reRateMax, ",##0.0####"));
			}
			allInStr.append("％)");
		}

		StringBuffer limitStr = new StringBuffer(0);
		String rateLimitType = Util.trim(l140m01n.getRateLimitType());
		String rateLimitRate = NumConverter.addComma(l140m01n.getRateLimitRate(), ",##0.0####");
		String rateLimit = Util.trim(l140m01n.getRateLimit());
		if ("0".equals(rateLimitType)) {

		} else if ("1".equals(rateLimitType)) {
			// l1401s02p04.msg015=惟稅前不得低於
			limitStr.append("，").append("惟稅前不得低於")
					.append(rateLimitRate).append("％");
		} else if ("11".equals(rateLimitType)) {
			// l1401s02p04.msg016=惟稅後不得低於
			limitStr.append("，").append("惟稅後不得低於")
					.append(rateLimitRate).append("％");
		} else if ("12".equals(rateLimitType)) {
			// l1401s02p04.msg017=惟不得低於聯行息加
			limitStr.append("，").append("惟不得低於聯行息加")
					.append(rateLimitRate).append("％");
		} else if ("13".equals(rateLimitType)) {
			String rateLimitCode = Util.trim(l140m01n.getRateLimitCode());
			String rateLimitCodeStr = "";
			if (rateMap.containsKey(rateLimitCode)) {
				rateLimitCodeStr = Util.trim(rateMap.get(rateLimitCode));
			} else if (rateMap2.containsKey(tempBase + rateLimitCode)) {
				rateLimitCodeStr = Util.trim(rateMap2.get(tempBase
						+ rateLimitCode));
			} else {
				rateLimitCodeStr = rateLimitCode;
			}

			if (!"2".equals(rateLimit)) {
				// l1401s02p04.msg018=惟不得低於
				limitStr.append("，").append("惟不得低於");
			} else {
				// l1401s02p04.msg019=與
				limitStr.append("與");
			}

			String rateLimitCountPr = NumConverter.addComma(l140m01n.getRateLimitCountPr(), ",##0.0####");
			if ("01".equals(rateLimitCode)) {
				limitStr.append(rateLimitCountPr).append("％");
			} else {
				String lowRateGroupName = Util.trim(l140m01n
						.getRateLimitMarket());

				if (UtilConstants.DEFAULT.是.equals(l140m01n
						.getRateLimitSetAll())) {
					if (Util.isNotEmpty(lowRateGroupName)) {
						// l1401s02p04.msg021=同天期
						limitStr.append("同天期").append(lowRateGroupName);
					}
				} else {
					limitStr.append(rateLimitCodeStr);
				}

				BigDecimal rateLimitCountRate = l140m01n
						.getRateLimitCountRate();
				if (Util.isNotEmpty(l140m01n.getRateLimitCountType())
						&& Util.isNotEmpty(rateLimitCountRate)
						&& BigDecimal.ZERO.compareTo(rateLimitCountRate) != 0) {
					String RateLimitCount = countMap.get(l140m01n
							.getRateLimitCountType())
							+ ""
							+ NumConverter.addComma(rateLimitCountRate, ",##0.0####") + "％";
					limitStr.append(RateLimitCount);
				}
			}

			if ("1".equals(l140m01n.getRateLimitTax())) {
				String RateLimitStr = "除以"
						+ NumConverter.addComma(l140m01n.getRateLimitTaxRate(), ",##0.0####");
				limitStr.append(RateLimitStr);
			}

			if ("2".equals(rateLimit)) {
				// l1401s02p04.msg022=孰高計收
				limitStr.append("孰高計收");
			}

		} else {
			limitStr.append("，").append(rateLimitTypeMap.get(rateLimitType));
		}

		String taxOtherStr = "";
		if ("1".equals(rateTax)) {
			if (Util.equals(Util.trim(l140m01n.getRateTaxCodeDecideFuture()),
					"Y")) {
				// l1401s02p04.msg027=稅負由借款人負擔
				taxOtherStr = "，稅負由借款人負擔";
			}
		}

		// 第二組下限利率
		StringBuffer limitStr2 = new StringBuffer(0);
//		StringBuffer limitStrDB22 = new StringBuffer(0);
		String rateLimitType2 = Util.equals(
				Util.trim(l140m01n.getRateLimitType2()), "") ? "0" : Util
				.trim(l140m01n.getRateLimitType2());
		String rateLimitRate2 = NumConverter.addComma(l140m01n.getRateLimitRate2(), ",##0.0####");
		String rateLimit2 = Util.trim(l140m01n.getRateLimit2());

		if ("0".equals(rateLimitType2)) {

		} else if ("1".equals(rateLimitType2)) {
			// l1401s02p04.msg015=惟稅前不得低於
			limitStr2.append("，或")
					.append("惟稅前不得低於")
					.append(rateLimitRate2).append("％");
		} else if ("11".equals(rateLimitType2)) {
			// l1401s02p04.msg016=惟稅後不得低於
			limitStr2.append("，或")
					.append("惟稅後不得低於")
					.append(rateLimitRate2).append("％");
		} else if ("12".equals(rateLimitType2)) {
			// l1401s02p04.msg017=惟不得低於聯行息加
			limitStr2.append("，或")
					.append("惟不得低於聯行息加")
					.append(rateLimitRate2).append("％");
		} else if ("13".equals(rateLimitType2)) {
			String rateLimitCode2 = Util.trim(l140m01n.getRateLimitCode2());
			String rateLimitCodeStr2 = "";

			if (rateMap.containsKey(rateLimitCode2)) {
				rateLimitCodeStr2 = Util.trim(rateMap.get(rateLimitCode2));
			} else if (rateMap2.containsKey(tempBase + rateLimitCode2)) {
				rateLimitCodeStr2 = Util.trim(rateMap2.get(tempBase
						+ rateLimitCode2));
			} else {
				rateLimitCodeStr2 = rateLimitCode2;
			}

			if (!"2".equals(rateLimit2)) {
				// l1401s02p04.msg018_1=不得低於
				// l1401s02p04.msg028=亦
				limitStr2.append("，").append("亦").append("不得低於");
			} else {
				// l1401s02p04.msg019=與
				// l1401s02p04.msg029=且
				limitStr2.append("，").append("且").append("與");
			}

			String rateLimitCountPr2 = NumConverter.addComma(l140m01n.getRateLimitCountPr2(), ",##0.0####");
			if ("01".equals(rateLimitCode2)) {
				limitStr2.append(rateLimitCountPr2).append("％");
			} else {
				String lowRateGroupName2 = Util.trim(l140m01n
						.getRateLimitMarket2());

				if (UtilConstants.DEFAULT.是.equals(l140m01n
						.getRateLimitSetAll2())) {
					if (Util.isNotEmpty(lowRateGroupName2)) {
						// l1401s02p04.msg021=同天期;
						limitStr2.append("同天期").append(lowRateGroupName2);
					}
				} else {
					limitStr2.append(rateLimitCodeStr2);
				}

				BigDecimal rateLimitCountRate2 = l140m01n
						.getRateLimitCountRate2();
				if (Util.isNotEmpty(l140m01n.getRateLimitCountType2())
						&& Util.isNotEmpty(rateLimitCountRate2)
						&& BigDecimal.ZERO.compareTo(rateLimitCountRate2) != 0) {
					String RateLimitCount2 = countMap.get(l140m01n
							.getRateLimitCountType2())
							+ ""
							+ NumConverter.addComma(rateLimitCountRate2, ",##0.0####") + "％";
					limitStr2.append(RateLimitCount2);
				}
			}

			if ("1".equals(l140m01n.getRateLimitTax2())) {
				String RateLimitStr2 = "除以"
						+ NumConverter.addComma(l140m01n.getRateLimitTaxRate2(), ",##0.0####");
				limitStr2.append(RateLimitStr2);
			}

			if ("2".equals(rateLimit2)) {
				// l1401s02p04.msg022=孰高計收
				limitStr2.append("孰高計收");
			}
		} else {
			limitStr2.append("，").append(rateLimitTypeMap.get(rateLimitType2));
		}

		
		/* ============================================
		 * 不組 "優惠利率" 文字
		 * ============================================
		 * */
		
		StringBuffer RateTxt = new StringBuffer(0);
		RateTxt.append(secNoStr).append(intBase);
		if (!"@2".equals(rateBase)) {
			if ("2".equals(rateLimit)) {
				RateTxt.append(taxStr).append(limitStr).append(limitStr2)
						.append(intBaseU012).append(rateMemo).append(intChg)
						.append(recvRate).append(taxOtherStr).append(otherMemo)
						.append("。").append(allInStr);
			} else {
				RateTxt.append(taxStr).append(intBaseU012).append(rateMemo)
						.append(intChg).append(recvRate).append(limitStr)
						.append(taxOtherStr).append(limitStr2)
						.append(otherMemo).append("。")
						.append(allInStr);
			}
		}
		rateDscr = RateTxt.toString();
		
		return rateDscr;
	}

	/**
	 * 取得段數文字
	 */
	// 參考 LMS1401ServiceImpl.java getSecNoStr
	private String getSecNoStr(L140M01N l140m01n) {
		StringBuffer secNoStr = new StringBuffer();
		String secNo = Util.trim(l140m01n.getSecNo());
		SecNoOpEnum SecNoOp = L140M01NEnum.SecNoOpEnum.getEnum(Util
				.trim(l140m01n.getSecNoOp()));
		// l1401s02p04.msg001=第{0}段
		secNoStr.append(MessageFormat.format("第{0}段", secNo));
		switch (SecNoOp) {
		case 全案:
			secNoStr.setLength(0);
			break;
		case 自動用日起迄月:
			// l1401s02p04.msg002=(自動用日起第{0}-{1}月)：
			secNoStr.append(MessageFormat.format("(自動用日起第{0}-{1}月)：",
					l140m01n.getSecBegMon(), l140m01n.getSecEndMon()));
			break;
		case YYYYMMDD:
			secNoStr.append("(");
			secNoStr.append(CapDate.formatDate(l140m01n.getSecBegDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			secNoStr.append("-");
			secNoStr.append(CapDate.formatDate(l140m01n.getSecEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			secNoStr.append(")：");
			break;
		case 自簽約日起迄月:
			// l1401s02p04.msg003=(自簽約日起第{0}-{1}月)：
			secNoStr.append(MessageFormat.format("(自簽約日起第{0}-{1}月)：",
					l140m01n.getSecBegMon(), l140m01n.getSecEndMon()));
			break;
		case 自動用日起至迄日:
			// l1401s02p04.msg030=(自動用日起至{0})：
			secNoStr.append(MessageFormat.format("(自動用日起至{0})：", 
					CapDate.formatDate(l140m01n.getSecEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD)));
			break;
		case 自簽約日起至迄日:
			// l1401s02p04.msg031=(自簽約日起至{0})：
			secNoStr.append(MessageFormat.format("(自簽約日起至{0})：", 
					CapDate.formatDate(l140m01n.getSecEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD)));
			break;
		case YYYYMMDD至迄日:
			// l1401s02p04.msg032=(自{0}至迄日)：
			secNoStr.append(MessageFormat.format("(自{0}至迄日)：",
					CapDate.formatDate(l140m01n.getSecBegDate(),
					UtilConstants.DateFormat.YYYY_MM_DD)));
			break;
		}
		return secNoStr.toString();
	}
	
	/**
	 * U01 U02 自訂利率
	 */
	// 參考 LMS1401ServiceImpl.java forU01U02Str
	private String forU01U02Str(CapAjaxFormResult countMap, L140M01N l140m01n,
			StringBuffer intBase) {
		String prRate = Util.trim(l140m01n.getPrRate());
		String usdMarkStr = "";
		String intBaseU012 = "";
		String theFullName = "";
		if ("1".equals(Util.trim(l140m01n.getUsdMarket()))) {
			usdMarkStr = "SIBOR";
			theFullName = "ＳＩＢＯＲ";
		} else {
			usdMarkStr = "LIBOR";
			theFullName = "ＬＩＢＯＲ";
		}

		if ("U01".equals(prRate) || "U02".equals(prRate)) {
			if (UtilConstants.DEFAULT.是.equals(l140m01n.getUsdSetAll())) {
				intBase.append("按借款同天期").append(usdMarkStr);
			} else {
				String usdMarketRate = Util.trim(l140m01n.getUsdMarketRate());
				String[] usdMarketRateArray = usdMarketRate
						.split(UtilConstants.Mark.SPILT_MARK);
				LinkedHashMap<String, String> rowData = lnlnf070Service
						.getCode();
				int intCount = 0;
				for (String key : usdMarketRateArray) {
					++intCount;
					String keyName = rowData.get(key);
					if (intCount != 1) {
						keyName = keyName.replace(theFullName, "");
					}
					if (Util.isEmpty(intBase)) {
						intBase.append("按").append(keyName);
					} else {
						if (intCount < usdMarketRateArray.length) {
							intBase.append("、").append(keyName);
						} else {
							intBase.append("或").append(keyName);
						}
					}
				}
			}
		}

		BigDecimal usdDesRate = l140m01n.getUsdDesRate();
		if (Util.isNotEmpty(usdDesRate)
				&& BigDecimal.ZERO.compareTo(usdDesRate) != 0) {
			if ("U01".equals(prRate)) {
				// l1401s02p04.msg023=與TAIFX差額逾{0}％部分由借戶負擔
				intBaseU012 = "，"
						+ usdMarkStr
						+ MessageFormat.format("與TAIFX差額逾{0}％部分由借戶負擔",
								NumConverter.addComma(usdDesRate, ",##0.0####"));
			}
			if ("U02".equals(prRate)) {
				// l1401s02p04.msg024=惟市場利率大幅波動時，另以市場利率議定
				intBaseU012 = "，惟市場利率大幅波動時，另以市場利率議定";
			}
		}

		return intBaseU012;
	}
}
