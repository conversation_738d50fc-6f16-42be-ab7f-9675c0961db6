---------------------------------------------------------
-- LMS.L999M01B 企金約據書立約人檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L999M01B;
CREATE TABLE LMS.L999M01B (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	TYPE          CHAR(1)       not null,
	CUS<PERSON><PERSON>        VARCHAR(10)   ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)       ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      VARCHAR(120) ,
	<PERSON>NO          CHAR(3)      ,
	BRNAME        CHAR(63)     ,
	CHAIRMANID    VARCHAR(10)  ,
	CHAIRMANDUPNO CHAR(1)      ,
	CHAIRMAN      VARCHAR(60)  ,
	ADDRZIP       DECIMAL(5)   ,
	ADDRCITY      VARCHAR(12)  ,
	ADDRTOWN      VARCHAR(12)  ,
	ADDR          VARCHAR(300) ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L999M01B PRIMARY KEY(OID)
) IN  EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL999M01B01;
CREATE UNIQUE INDEX LMS.XL999M01B01 ON LMS.L999M01B   (MAINID, TYPE, CUSTID, DUPNO);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L999M01B IS '企金約據書立約人檔';
COMMENT ON LMS.L999M01B (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	TYPE          IS '立約人種類', 
	CUSTID        IS '立約人統一編號', 
	DUPNO         IS '立約人重覆序號', 
	CUSTNAME      IS '立約人名稱', 
	BRNO          IS '分行代碼', 
	BRNAME        IS '分行名稱', 
	CHAIRMANID    IS '負責人統編', 
	CHAIRMANDUPNO IS '負責人統編重複碼', 
	CHAIRMAN      IS '負責人姓名', 
	ADDRZIP       IS '戶籍地址郵遞區號', 
	ADDRCITY      IS '戶籍地址(縣市)', 
	ADDRTOWN      IS '戶籍地址(區鄉鎮市)', 
	ADDR          IS '戶籍地址', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
