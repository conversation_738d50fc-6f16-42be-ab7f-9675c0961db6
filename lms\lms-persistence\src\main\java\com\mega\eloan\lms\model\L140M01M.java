
/* 
 * L140M01M.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 維護央行購屋/空地/建屋貸款註記資訊 
 *  (補充說明: 此TABLE共有四組擔保品座落相關欄位，
 *  1. 土建融:locationCity、locationCd、site3No、site4No
 *  2. 建案完工未出售:remainLoanLocationCity、remainLoanLocationCd、remainLoanSite3No、remainLoanSite4No
 *  3. 前次建案完工未出售:bremainLoanLocationCity、bremainLoanLocationCd、bremainLoanSite3No、bremainLoanSite4No
 *  4. 央行購住(此處會送ELF383):cityId、 areaId、sit3No、sit4No)
 *  
 *  J-112-0098:土建融下擔保品座落，新增國內/海外判斷欄位、海外座落欄位
 * 
 * 
 * **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01M", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L140M01M extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 央行購住狀態
	 * <p/>
	 * 1|屬於自然人購屋貸款，且所有權取得日期在99/6/25後者(涵蓋全國各地區，惟不包括謄本登記主要用途為商業用、店鋪等不含「住」字樣者)<br/>
	 * 5|屬於公司法人購屋貸款，且所有權取得日期在99/12/31以後者 (涵蓋全國各地區，惟不包括謄本登記主要用途為商業用、店鋪等不含「住」字樣者)<br/>
	 * 2|屬於自然人 / 公司法人土地抵押貸款<br/>
	 * 4|非屬央行自然人購屋 / 公司法人購屋 / 土地抵押貸款報表填報對象<br/>
	 * (依不同版本的 version)  select * from com.bcodeType where codetype like 'L140M01M_cbcCase%' or codetype in ('L140M01M_cbcCase','L140M01M_cbcCase_20201208')
	 */
	@Size(max = 1)
	@Column(name = "CBCCASE", length = 1, columnDefinition = "CHAR(1)")
	private String cbcCase;

	/** 貸款成數 **/
	@Digits(integer = 3, fraction = 2)
	@Column(name = "PAYPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal payPercent;

	/**
	 * 縣市代碼
	 * <p/>
	 * ※2013-01-09 變更欄位大小為<br/>
	 * VARCHAR(1)<br/>
	 * codetype=counties
	 */
	@Size(max = 1)
	@Column(name = "CITYID", length = 1, columnDefinition = "VARCHAR(1)")
	private String cityId;

	/**
	 * 鄉鎮市區代碼
	 * <p/>
	 * Codetype=counties+ L140M01M. cityId
	 */
	@Size(max = 3)
	@Column(name = "AREAID", length = 3, columnDefinition = "VARCHAR(3)")
	private String areaId;

	/**
	 * 借款人聯徵名下有幾戶 - 用途別為[1]之其他房貸資料
	 * <p/>
	 * 有一戶|Y<br/>
	 * 無|N<br/>
	 * 二戶(含)以上 |B
	 */
	@Size(max = 1)
	@Column(name = "CUSTYN", length = 1, columnDefinition = "CHAR(1)")
	private String custYN;

	/**
	 * 擔保品購價(台幣元)
	 * <p/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 0)
	@Column(name = "APPAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal appAmt;

	/**
	 * 建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	@Size(max = 1)
	@Column(name = "BUILDYN", length = 1, columnDefinition = "CHAR(1)")
	private String buildYN;

	/**
	 * 有無與其他帳號共用同一擔保品
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	@Size(max = 1)
	@Column(name = "COMMONYN", length = 1, columnDefinition = "CHAR(1)")
	private String commonYN;

	/**
	 * 有無授權外核准得分批撥款「擔保品總貸款額度」
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	@Size(max = 1)
	@Column(name = "SHARECOLLYN", length = 1, columnDefinition = "CHAR(1)")
	private String shareCollYN;

	/**
	 * 授權外核准得分批撥款「擔保品總貸款額度」
	 * <p/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 0)
	@Column(name = "SHARECOLLAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal shareCollAmt;

	/**
	 * 控管類別之理由(依不同版本的 version)  <br/>
	 * select * from com.bcodeType where codetype like 'L140M01M_plusReason%' or codetype in ('L140M01M_plusReason','L140M01M_plusReason_20201208')
	 */
	@Size(max = 1)
	@Column(name = "PLUSREASON", length = 1, columnDefinition = "CHAR(1)")
	private String plusReason;

	/**
	 * 非央行控管種類原因
	 * <p/>
	 * 當非央行控管種類 為「其他」時要顯示此欄位
	 */
	@Size(max = 150)
	@Column(name = "PLUSREASONMEMO", length = 150, columnDefinition = "VARCHAR(150)")
	private String plusReasonMeMo;

	/**
	 * 擔保品鑑價(台幣元) ELF500_EST_AMT
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 0)
	@Column(name = "NOWAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal nowAMT;

	/**
	 * 擔保品估值(台幣元) ELF500_LAWVAL
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 單位：元
	 */
	@Digits(integer = 13, fraction = 0)
	@Column(name = "VALUEAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal valueAMT;

	/**
	 * 是否屬都市計畫劃定之區域
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 有|Y<br/>
	 * 無|N
	 */
	@Size(max = 1)
	@Column(name = "HOUSEYN", length = 1, columnDefinition = "CHAR(1)")
	private String houseYN;

	/**
	 * 都市計畫劃定之使用分區
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|住宅區<br/>
	 * 2|商業區<br/>
	 * 0|其他<br/>
	 * codeType=
	 */
	@Size(max = 1)
	@Column(name = "HOUSETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String houseType;

	/**
	 * 是否借款用途
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|興建房屋<br/>
	 * 2|自然人理財週轉 <br/>
	 * 3|營運週轉<br/>
	 * codeType=
	 */
	@Size(max = 1)
	@Column(name = "PURPOSETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String purposeType;

	/**
	 * 是否擔保品性質別
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|房地建地<br/>
	 * 2|空地<br/>
	 * 3|其他<br/>
	 * codeType=
	 */
	@Size(max = 1)
	@Column(name = "CMSTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cmsType;

	/**
	 * 是否保留一成估值
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 有|Y<br/>
	 * 無|N<br/>
	 * codeType=
	 */
	@Size(max = 1)
	@Column(name = "KEEPYN", length = 1, columnDefinition = "CHAR(1)")
	private String keepYN;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 檢核結果，Y才上傳383(for企金使用) */
	@Column(name = "CHECKYN", length = 1, columnDefinition = "CHAR(1)")
	private String checkYN;

	/**
	 * 擔保品MEMO
	 */
	@Size(max = 180)
	@Column(name = "SLNDTAX", length = 180, columnDefinition = "VARCHAR(180)")
	private String sLndTax;

	/**
	 * 是否屬土建融案
	 */
	@Size(max = 1)
	@Column(name = "LANDBUILDYN", length = 1, columnDefinition = "CHAR(1)")
	private String landBuildYN;

	/**
	 * 產品種類
	 */
	@Size(max = 2)
	@Column(name = "PRODCLASS", length = 2, columnDefinition = "CHAR(2)")
	private String prodClass;

	/**
	 * 預計取得建照日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "BUILDDATE", columnDefinition = "DATE")
	private Date buildDate;

	/**
	 * 預計撥款至動工期間月數
	 */
	@Digits(integer = 3, fraction = 0)
	@Column(name = "WAITMONTH", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal waitMonth;

	/**
	 * 土地使用分區
	 */
	@Size(max = 1)
	@Column(name = "LANDTYPE", length = 11, columnDefinition = "CHAR(1)")
	private String landType;

	/**
	 * 土地面積
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "AREALAND", columnDefinition = "DECIMAL(13,2)")
	private BigDecimal areaLand;

	
	/**
	 * 擔保品座落國內/國外
	 * 1:國內
	 * 2:國外
	 */
	@Size(max = 1)
	@Column(name = "LOCATIONDOMESTIC", length = 1, columnDefinition = "CHAR(1)")
	private String locationDomestic;
	
	/**
	 * 擔保品座落縣市
	 */
	@Size(max = 1)
	@Column(name = "LOCATIONCITY", length = 1, columnDefinition = "CHAR(1)")
	private String locationCity;
	
	/**
	 * 擔保品座落
	 */
	@Size(max = 3)
	@Column(name = "LOCATIONCD", length = 3, columnDefinition = "CHAR(3)")
	private String locationCd;

	/**
	 * 座落區段
	 */
	@Digits(integer = 4, fraction = 0)
	@Column(name = "SITE3NO", columnDefinition = "DECIMAL(4,0)")
	private BigDecimal site3No;

	/**
	 * 座落區村里
	 */
	@Size(max = 11)
	@Column(name = "SITE4NO", length = 11, columnDefinition = "CHAR(11)")
	private String site4No;
	
	/**
	 * 座落區(國外專用)
	 */
	@Size(max = 300)
	@Column(name = "LOCATIONTARGET", length = 300, columnDefinition = "VARCHAR(300)")
	private String locationTarget;

	/**
	 * 預約額度序號
	 */
	@Size(max = 12)
	@Column(name = "LANDBUILDCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String landBuildCntrno;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/**
	 * 本案是否為建案完工未出售房屋之融資
	 */
	@Size(max = 1)
	@Column(name = "REMAINLOANYN", length = 1, columnDefinition = "CHAR(1)")
	private String remainLoanYN;

	/**
	 * 建案完工未出售房屋之融資-案件種類
	 */
	@Size(max = 1)
	@Column(name = "REMAINLOANCLASS", length = 1, columnDefinition = "CHAR(1)")
	private String remainLoanClass;

	/**
	 * 擔保品座落縣市
	 */
	@Size(max = 1)
	@Column(name = "REMAINLOANLOCATIONCITY", length = 1, columnDefinition = "VARCHAR(1)")
	private String remainLoanLocationCity;

	/**
	 * 擔保品座落
	 */
	@Size(max = 3)
	@Column(name = "REMAINLOANLOCATIONCD", length = 3, columnDefinition = "VARCHAR(3)")
	private String remainLoanLocationCd;

	/**
	 * 座落區段
	 */
	@Digits(integer = 4, fraction = 0)
	@Column(name = "REMAINLOANSITE3NO", columnDefinition = "DECIMAL(4,0)")
	private BigDecimal remainLoanSite3No;

	/**
	 * 座落區村里
	 */
	@Size(max = 11)
	@Column(name = "REMAINLOANSITE4NO", length = 11, columnDefinition = "CHAR(11)")
	private String remainLoanSite4No;

	/**
	 * 前次是否為建案完工未出售房屋之融資
	 */
	@Size(max = 1)
	@Column(name = "BREMAINLOANYN", length = 1, columnDefinition = "CHAR(1)")
	private String bremainLoanYN;

	/**
	 * 建案完工未出售房屋之融資-案件種類
	 */
	@Size(max = 1)
	@Column(name = "BREMAINLOANCLASS", length = 1, columnDefinition = "CHAR(1)")
	private String bremainLoanClass;

	/**
	 * 擔保品座落縣市
	 */
	@Size(max = 1)
	@Column(name = "BREMAINLOANLOCATIONCITY", length = 1, columnDefinition = "VARCHAR(1)")
	private String bremainLoanLocationCity;

	/**
	 * 擔保品座落
	 */
	@Size(max = 3)
	@Column(name = "BREMAINLOANLOCATIONCD", length = 3, columnDefinition = "VARCHAR(3)")
	private String bremainLoanLocationCd;

	/**
	 * 座落區段
	 */
	@Digits(integer = 4, fraction = 0)
	@Column(name = "BREMAINLOANSITE3NO", columnDefinition = "DECIMAL(4,0)")
	private BigDecimal bremainLoanSite3No;

	/**
	 * 座落區村里
	 */
	@Size(max = 11)
	@Column(name = "BREMAINLOANSITE4NO", length = 11, columnDefinition = "CHAR(11)")
	private String bremainLoanSite4No;

	/** 擔保品屋齡 **/
	@Column(name = "HOUSE_AGE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal house_age;

	/** 擔保品放款值 **/
	@Column(name = "LOANAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal loanAmt;

	/**
	 * 額度序號是否在空地貸款控制檔中
	 */
	@Column(name = "ISCLEARLAND", length = 1, columnDefinition = "CHAR(1)")
	private String isClearLand;

	/**
	 * 控管類別
	 */
	@Column(name = "CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String ctlType;

	/**
	 * 初次核定預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "FSTDATE", columnDefinition = "DATE")
	private Date fstDate;

	/**
	 * 最新核定(動審)預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "LSTDATE", columnDefinition = "DATE")
	private Date lstDate;

	/**
	 * 是否變更預計動工日
	 */
	@Column(name = "ISCHGSTDATE", length = 1, columnDefinition = "CHAR(1)")
	private String isChgStDate;

	/**
	 * 變更預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CSTDATE", columnDefinition = "DATE")
	private Date cstDate;

	/**
	 * 變更預計動工日原因
	 */
	@Column(name = "CSTREASON", length = 2, columnDefinition = "CHAR(2)")
	private String cstReason;

	/**
	 * 是否調降利率
	 */
	@Column(name = "ISCHGRATE", length = 1, columnDefinition = "CHAR(1)")
	private String isChgRate;

	/**
	 * 輸入本次採行措施
	 */
	@Column(name = "ADOPTFG", length = 30, columnDefinition = "VARCHAR(30)")
	private String adoptFg;

	/**
	 * 再加減碼幅度
	 */
	@Digits(integer = 4, fraction = 5)
	@Column(name = "RATEADD", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal rateAdd;

	/**
	 * 借款人ROA
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "CUSTROA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal custRoa;

	/**
	 * 關係人ROA
	 */
	@Digits(integer = 13, fraction = 2)
	@Column(name = "RELROA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal relRoa;

	/**
	 * ROA查詢起日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ROABGNDATE", columnDefinition = "DATE")
	private Date roaBgnDate;

	/**
	 * ROA查詢迄日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ROAENDDATE", columnDefinition = "DATE")
	private Date roaEndDate;

	/**
	 * 是否符合本行規定
	 */
	@Column(name = "ISLEGAL", length = 1, columnDefinition = "CHAR(1)")
	private String isLegal;

	/**
	 * 初貸餘屋戶數
	 */
	@Column(name = "FIRSTLOANUNSOLDHOUSEQUANTITY", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal firstLoanUnsoldHouseQuantity;
	
	/**
	 * 目前餘屋戶數
	 */
	@Column(name = "CURRENTUNSOLDHOUSEQUANTITY", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal currentUnsoldHouseQuantity;

	/**
	 * 本案授信期間年限-月
	 */
	@Column(name = "MONTHOFCREDITPERIOD", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal monthOfCreditPeriod;
	
	/**
	 * 本案最終授信期限止日(續約後)
	 */
	@Column(name = "FINALCREDITPERIODENDDATE", columnDefinition = "DATE")
	private Date finalCreditPeriodEndDate;
	
	/**
	 * 前次初貸餘屋戶數
	 */
	@Column(name = "PREFIRSTLOANUNSOLDHOUSEQTY", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal preFirstLoanUnsoldHouseQty;
	
	/**
	 * 前次目前餘屋戶數
	 */
	@Column(name = "PRECURRENTUNSOLDHOUSEQUANTITY", columnDefinition = "DECIMAL(5,0)")
	private BigDecimal preCurrentUnsoldHouseQuantity;

	/**
	 * 前次本案授信期間年限-月
	 */
	@Column(name = "PREMONTHOFCREDITPERIOD", columnDefinition = "DECIMAL(2,0)")
	private BigDecimal preMonthOfCreditPeriod;
	
	/**
	 * 前次本案最終授信期限止日(續約後)
	 */
	@Column(name = "PREFINALCREDITPERIODENDDATE", columnDefinition = "DATE")
	private Date preFinalCreditPeriodEndDate;
	
	/**
	 * 為紀錄央行於2020-12-08對辦理不動產抵押貸款業務規定版本, 值域{'', '20201208_ver1'}
	 * LMSUtil.java 有定義
	 * ●    public final static String L140M01M_VERSION = "20201208_ver1";
	 */
	@Column(name = "VERSION", columnDefinition = "VARCHAR(20)")
	private String version;
	
	/**
	 * 2020-12-08 央行對辦理不動產抵押貸款業務限制理由
	 */
	@Column(name = "REALESTATELOANLIMITREASON", columnDefinition = "CHAR(1)")
	public String realEstateLoanLimitReason;
	
	/**
	 * 是否為購置第3戶(含)以上高價住宅
	 */
	@Column(name = "IS3RDHIGNHOUSE", columnDefinition = "CHAR(1)")
	private String is3rdHignHouse;
	
	/**
	 * 新作及續約不為最新版原因說明
	 */
	@Column(name = "NOTLATESTVERDESC", columnDefinition = "VARCHAR(300)")
	private String notLatestVerDesc;

	
	/**
	 * 是否續約/提前續約:是/否
	 */
	@Column(name = "ISRENEW", columnDefinition = "CHAR(1)")
	private String isRenew;
	
	/**
	 * 是否以新額度償還舊額度(含轉貸):是/否
	 */
	@Column(name = "ISPAYOLDQUOTA", columnDefinition = "CHAR(1)")
	private String isPayOldQuota;
	
	/**
	 * 原(舊)額度
	 */
	@Digits(integer = 13, fraction = 0)
	@Column(name = "OLDQUOTA", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal oldQuota;
	
	/**
	 * 本額度償還原(舊)額度之金額
	 */
	@Digits(integer = 13, fraction = 0)
	@Column(name = "PAYOLDAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal payOldAmt;
	
	/**
	 * 償還之舊額度類型: 土建融, 原餘屋貸款
	 */
	@Column(name = "PAYOLDAMTITEM", columnDefinition = "CHAR(1)")
	private String payOldAmtItem;
	
	/**
	 * 符合以下任一點，即非央行管制之餘屋貸款，請改選「非屬央行自然人或公司法人之不動產抵押貸款管控對象
	 * 1.屋齡逾5年
	 * 2.建物所有權部之登記原因，非第一次登記
	 * 3.建物謄本、測量成果圖、使用執照之主要用途均無「住」字樣
	 */
	@Column(name = "ISMATCHUNSOLDHOUSEITEM", columnDefinition = "CHAR(1)")
	private String isMatchUnsoldHouseItem;
	
	
	/**
	 * 本案是否為買賣案件
	 */
	@Column(name = "ISSALECASE", columnDefinition = "CHAR(1)")
	private String isSaleCase;
	
	/**
	 * 實際動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ACTSTARTDATE", columnDefinition = "DATE")
	private Date actStartDate;
	
	/**
	 * 央行購住最新預計動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CBCONTROLLSTDATE", columnDefinition = "DATE")
	private Date cbControlLstDate;
	
	/** 時價 **/
	@Digits(integer = 13, fraction = 2)
	@Column(name = "TIMEVAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal timeVal;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得央行購住狀態
	 * <p/>
	 * 1|屬於自然人購屋貸款，且所有權取得日期在99/6/25後者(涵蓋全國各地區，惟不包括謄本登記主要用途為商業用、店鋪等不含「住」字樣者)<br/>
	 * 5|屬於公司法人購屋貸款，且所有權取得日期在99/12/31以後者 (涵蓋全國各地區，惟不包括謄本登記主要用途為商業用、店鋪等不含「住」字樣者)<br/>
	 * 2|屬於自然人 / 公司法人土地抵押貸款<br/>
	 * 4|非屬央行自然人購屋 / 公司法人購屋 / 土地抵押貸款報表填報對象<br/>
	 * codeType=L140M01M_cbcCase
	 */
	public String getCbcCase() {
		return this.cbcCase;
	}

	/**
	 * 設定央行購住狀態
	 * <p/>
	 * 1|屬於自然人購屋貸款，且所有權取得日期在99/6/25後者(涵蓋全國各地區，惟不包括謄本登記主要用途為商業用、店鋪等不含「住」字樣者)<br/>
	 * 5|屬於公司法人購屋貸款，且所有權取得日期在99/12/31以後者 (涵蓋全國各地區，惟不包括謄本登記主要用途為商業用、店鋪等不含「住」字樣者)<br/>
	 * 2|屬於自然人 / 公司法人土地抵押貸款<br/>
	 * 4|非屬央行自然人購屋 / 公司法人購屋 / 土地抵押貸款報表填報對象<br/>
	 * codeType=L140M01M_cbcCase
	 **/
	public void setCbcCase(String value) {
		this.cbcCase = value;
	}

	/** 取得貸款成數 **/
	public BigDecimal getPayPercent() {
		return this.payPercent;
	}

	/** 設定貸款成數 **/
	public void setPayPercent(BigDecimal value) {
		this.payPercent = value;
	}

	/**
	 * 取得縣市代碼
	 * <p/>
	 * ※2013-01-09 變更欄位大小為<br/>
	 * VARCHAR(2)<br/>
	 * codetype=counties
	 */
	public String getCityId() {
		return this.cityId;
	}

	/**
	 * 設定縣市代碼
	 * <p/>
	 * ※2013-01-09 變更欄位大小為<br/>
	 * VARCHAR(2)<br/>
	 * codetype=counties
	 **/
	public void setCityId(String value) {
		this.cityId = value;
	}

	/**
	 * 取得鄉鎮市區代碼
	 * <p/>
	 * Codetype=counties+ L140M01M. cityId
	 */
	public String getAreaId() {
		return this.areaId;
	}

	/**
	 * 設定鄉鎮市區代碼
	 * <p/>
	 * Codetype=counties+ L140M01M. cityId
	 **/
	public void setAreaId(String value) {
		this.areaId = value;
	}

	/**
	 * 取得借款人聯徵名下有無 - 用途別為[1]之其他房貸資料
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	public String getCustYN() {
		return this.custYN;
	}

	/**
	 * 設定借款人聯徵名下有無 - 用途別為[1]之其他房貸資料
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 **/
	public void setCustYN(String value) {
		this.custYN = value;
	}

	/**
	 * 取得擔保品購價(台幣元)
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getAppAmt() {
		return this.appAmt;
	}

	/**
	 * 設定擔保品購價(台幣元)
	 * <p/>
	 * 單位：元
	 **/
	public void setAppAmt(BigDecimal value) {
		this.appAmt = value;
	}

	/**
	 * 取得建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	public String getBuildYN() {
		return this.buildYN;
	}

	/**
	 * 設定建物謄本登記用途是否屬「住」類，或「住商」類等無營業登記
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 **/
	public void setBuildYN(String value) {
		this.buildYN = value;
	}

	/**
	 * 取得有無與其他帳號共用同一擔保品
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	public String getCommonYN() {
		return this.commonYN;
	}

	/**
	 * 設定有無與其他帳號共用同一擔保品
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 **/
	public void setCommonYN(String value) {
		this.commonYN = value;
	}

	/**
	 * 取得有無授權外核准得分批撥款「擔保品總貸款額度」
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 */
	public String getShareCollYN() {
		return this.shareCollYN;
	}

	/**
	 * 設定有無授權外核准得分批撥款「擔保品總貸款額度」
	 * <p/>
	 * 有|Y<br/>
	 * 無|N
	 **/
	public void setShareCollYN(String value) {
		this.shareCollYN = value;
	}

	/**
	 * 取得授權外核准得分批撥款「擔保品總貸款額度」
	 * <p/>
	 * 單位：元
	 */
	public BigDecimal getShareCollAmt() {
		return this.shareCollAmt;
	}

	/**
	 * 設定授權外核准得分批撥款「擔保品總貸款額度」
	 * <p/>
	 * 單位：元
	 **/
	public void setShareCollAmt(BigDecimal value) {
		this.shareCollAmt = value;
	}

	/**
	 * 取得非央行控管種類
	 * <p/>
	 * 1|建物所有權取得日期在99/6/25以前 (自然人使用) <br/>
	 * 7|建物所有權取得日期在99/12/31以前 (公司法人使用) LMS、CLS WEB NEW<br/>
	 * 2|建物登記用途無「住」字樣、或「住商」類等有營業登記 <br/>
	 * 3|擔保品非屬建物 <br/>
	 * 4|非屬購屋貸款 <br/>
	 * 6|轉換產品種類 LMS WEB NEW<br/>
	 * 5|其他 <br/>
	 * codeType=L140M01M_plusReason
	 */
	public String getPlusReason() {
		return this.plusReason;
	}

	/**
	 * 設定非央行控管種類
	 * <p/>
	 * 1|建物所有權取得日期在99/6/25以前 (自然人使用) <br/>
	 * 7|建物所有權取得日期在99/12/31以前 (公司法人使用) LMS、CLS WEB NEW<br/>
	 * 2|建物登記用途無「住」字樣、或「住商」類等有營業登記 <br/>
	 * 3|擔保品非屬建物 <br/>
	 * 4|非屬購屋貸款 <br/>
	 * 6|轉換產品種類 LMS WEB NEW<br/>
	 * 5|其他 <br/>
	 * codeType=L140M01M_plusReason
	 **/
	public void setPlusReason(String value) {
		this.plusReason = value;
	}

	/**
	 * 取得非央行控管種類原因
	 * <p/>
	 * 當非央行控管種類 為「其他」時要顯示此欄位
	 */
	public String getPlusReasonMeMo() {
		return this.plusReasonMeMo;
	}

	/**
	 * 設定非央行控管種類原因
	 * <p/>
	 * 當非央行控管種類 為「其他」時要顯示此欄位
	 **/
	public void setPlusReasonMeMo(String value) {
		this.plusReasonMeMo = value;
	}

	/**
	 * 取得擔保品鑑價(台幣元) ELF500_EST_AMT
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 單位：元
	 */
	public BigDecimal getNowAMT() {
		return this.nowAMT;
	}

	/**
	 * 設定擔保品鑑價(台幣元) ELF500_EST_AMT
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 單位：元
	 **/
	public void setNowAMT(BigDecimal value) {
		this.nowAMT = value;
	}

	/**
	 * 取得擔保品估值(台幣元) ELF500_LAWVAL
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 單位：元
	 */
	public BigDecimal getValueAMT() {
		return this.valueAMT;
	}

	/**
	 * 設定擔保品估值(台幣元) ELF500_LAWVAL
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 單位：元
	 **/
	public void setValueAMT(BigDecimal value) {
		this.valueAMT = value;
	}

	/**
	 * 取得是否屬都市計畫劃定之區域
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 有|Y<br/>
	 * 無|N
	 */
	public String getHouseYN() {
		return this.houseYN;
	}

	/**
	 * 設定是否屬都市計畫劃定之區域
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 有|Y<br/>
	 * 無|N
	 **/
	public void setHouseYN(String value) {
		this.houseYN = value;
	}

	/**
	 * 取得都市計畫劃定之使用分區
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|住宅區<br/>
	 * 2|商業區<br/>
	 * 0|其他<br/>
	 * codeType=
	 */
	public String getHouseType() {
		return this.houseType;
	}

	/**
	 * 設定都市計畫劃定之使用分區
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|住宅區<br/>
	 * 2|商業區<br/>
	 * 0|其他<br/>
	 * codeType=
	 **/
	public void setHouseType(String value) {
		this.houseType = value;
	}

	/**
	 * 取得是否借款用途
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|興建房屋<br/>
	 * 2|自然人理財週轉 <br/>
	 * 3|營運週轉<br/>
	 * codeType=
	 */
	public String getPurposeType() {
		return this.purposeType;
	}

	/**
	 * 設定是否借款用途
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|興建房屋<br/>
	 * 2|自然人理財週轉 <br/>
	 * 3|營運週轉<br/>
	 * codeType=
	 **/
	public void setPurposeType(String value) {
		this.purposeType = value;
	}

	/**
	 * 取得是否擔保品性質別
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|房地建地<br/>
	 * 2|空地<br/>
	 * 3|其他<br/>
	 * codeType=
	 */
	public String getCmsType() {
		return this.cmsType;
	}

	/**
	 * 設定是否擔保品性質別
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 1|房地建地<br/>
	 * 2|空地<br/>
	 * 3|其他<br/>
	 * codeType=
	 **/
	public void setCmsType(String value) {
		this.cmsType = value;
	}

	/**
	 * 取得是否保留一成估值
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 有|Y<br/>
	 * 無|N<br/>
	 * codeType=
	 */
	public String getKeepYN() {
		return this.keepYN;
	}

	/**
	 * 設定是否保留一成估值
	 * <p/>
	 * ※2013-01-23 新增欄位<br/>
	 * 有|Y<br/>
	 * 無|N<br/>
	 * codeType=
	 **/
	public void setKeepYN(String value) {
		this.keepYN = value;
	}

	/**
	 * 都市計畫劃定之使用分區-其他
	 * <p/>
	 * ※2013-04-29 新增欄位 40個中文字
	 */
	@Size(max = 120)
	@Column(name = "HOUSEOTHER", length = 120, columnDefinition = "VARCHAR(120)")
	private String houseOther;

	/**
	 * 都市計畫劃定之使用分區-其他
	 * <p/>
	 * ※2013-04-29 新增欄位 40個中文字
	 */
	public String getHouseOther() {
		return houseOther;
	}

	/**
	 * 都市計畫劃定之使用分區-其他
	 * <p/>
	 * ※2013-04-29 新增欄位 40個中文字
	 */
	public void setHouseOther(String houseOther) {
		this.houseOther = houseOther;
	}

	/**
	 * 是否擔保品性質別-其他
	 * <p/>
	 * ※2013-04-29 新增欄位 40個中文字
	 */
	@Size(max = 120)
	@Column(name = "CMSOTHER", length = 120, columnDefinition = "VARCHAR(120)")
	private String cmsOther;

	/**
	 * 是否擔保品性質別-其他
	 * <p/>
	 * ※2013-04-29 新增欄位 40個中文字
	 */
	public String getCmsOther() {
		return cmsOther;
	}

	/**
	 * 是否擔保品性質別-其他
	 * <p/>
	 * ※2013-04-29 新增欄位 40個中文字
	 */
	public void setCmsOther(String cmsOther) {
		this.cmsOther = cmsOther;
	}

	/**
	 * 是否為央行受限戶
	 * <p/>
	 * ※2013-04-29 新增欄位
	 */
	@Size(max = 1)
	@Column(name = "ISLIMITCUST", length = 1, columnDefinition = "CHAR(1)")
	private String isLimitCust;

	/**
	 * 是否為央行受限戶
	 * <p/>
	 * ※2013-04-29 新增欄位
	 */
	public String getIsLimitCust() {
		return isLimitCust;
	}

	/**
	 * 是否為央行受限戶
	 * <p/>
	 * ※2013-04-29 新增欄位
	 */
	public void setIsLimitCust(String isLimitCust) {
		this.isLimitCust = isLimitCust;
	}

	/**
	 * 是否為高價住宅
	 * <p/>
	 * ※2013-04-29 新增欄位
	 */
	@Size(max = 1)
	@Column(name = "ISHIGHHOUSE", length = 1, columnDefinition = "CHAR(1)")
	private String isHighHouse;

	/**
	 * 是否為高價住宅
	 * <p/>
	 * ※2013-04-29 新增欄位
	 */
	public String getIsHighHouse() {
		return isHighHouse;
	}

	/**
	 * 是否為高價住宅
	 * <p/>
	 * ※2013-04-29 新增欄位
	 */
	public void setIsHighHouse(String isHighHouse) {
		this.isHighHouse = isHighHouse;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 擔保品座落-段
	 */
	@Digits(integer = 4, fraction = 0)
	@Column(name = "SIT3NO", columnDefinition = "DECIMAL(4,0)")
	private BigDecimal sit3No;

	/**
	 * 擔保品座落-村
	 * <p/>
	 * 
	 */
	@Size(max = 11)
	@Column(name = "SIT4NO", length = 11, columnDefinition = "VARCHAR(11)")
	private String sit4No;

	/**
	 * 擔保品座落-段
	 */
	public BigDecimal getSit3No() {
		return sit3No;
	}

	/**
	 * 擔保品座落-段
	 */
	public void setSit3No(BigDecimal sit3No) {
		this.sit3No = sit3No;
	}

	/**
	 * 擔保品座落-村
	 * <p/>
	 * 
	 */
	public String getSit4No() {
		return sit4No;
	}

	/**
	 * 擔保品座落-村
	 * <p/>
	 * 
	 */
	public void setSit4No(String sit4No) {
		this.sit4No = sit4No;
	}

	/** 檢核結果，Y才上傳383(for企金使用) */
	public void setCheckYN(String checkYN) {
		this.checkYN = checkYN;
	}

	/** 檢核結果，Y才上傳383(for企金使用) */
	public String getCheckYN() {
		return checkYN;
	}

	/**
	 * 設定擔保品MEMO
	 */
	public void setsLndTax(String value) {
		this.sLndTax = value;
	}

	/**
	 * 取得擔保品MEMO
	 */
	public String getsLndTax() {
		return sLndTax;
	}

	/**
	 * 設定是否屬土建融案
	 */
	public void setLandBuildYN(String value) {
		this.landBuildYN = value;
	}

	/**
	 * 取得是否屬土建融案
	 */
	public String getLandBuildYN() {
		return landBuildYN;
	}

	/**
	 * 設定產品種類
	 */
	public void setProdClass(String value) {
		this.prodClass = value;
	}

	/**
	 * 取得產品種類
	 */
	public String getProdClass() {
		return prodClass;
	}

	/**
	 * 設定預計取得建照日期
	 */
	public void setBuildDate(Date value) {
		this.buildDate = value;
	}

	/**
	 * 取得預計取得建照日期
	 */
	public Date getBuildDate() {
		return buildDate;
	}

	/**
	 * 設定預計撥款至動工期間月數
	 */
	public void setWaitMonth(BigDecimal value) {
		this.waitMonth = value;
	}

	/**
	 * 取得預計撥款至動工期間月數
	 */
	public BigDecimal getWaitMonth() {
		return waitMonth;
	}

	/**
	 * 設定擔保品座落
	 */
	public void setLocationCd(String value) {
		this.locationCd = value;
	}

	/**
	 * 取得擔保品座落
	 */
	public String getLocationCd() {
		return locationCd;
	}

	/**
	 * 設定座落區段
	 */
	public void setSite3No(BigDecimal value) {
		this.site3No = value;
	}

	/**
	 * 取得座落區段
	 */
	public BigDecimal getSite3No() {
		return site3No;
	}

	/**
	 * 設定座落區村里
	 */
	public void setSite4No(String value) {
		this.site4No = value;
	}

	/**
	 * 取得座落區村里
	 */
	public String getSite4No() {
		return site4No;
	}

	/**
	 * 設定土地使用分區
	 */
	public void setLandType(String value) {
		this.landType = value;
	}

	/**
	 * 取得土地使用分區
	 */
	public String getLandType() {
		return landType;
	}

	/**
	 * 設定土地面積
	 */
	public void setAreaLand(BigDecimal value) {
		this.areaLand = value;
	}

	/**
	 * 取得土地面積
	 */
	public BigDecimal getAreaLand() {
		return areaLand;
	}

	/**
	 * 設定擔保品座落國內/國外
	 */
	public void setLocationDomestic(String value) {
		this.locationDomestic = value;
	}

	/**
	 * 取得擔保品座落國內/國外
	 */
	public String getLocationDomestic() {
		return locationDomestic;
	}
	
	/**
	 * 設定擔保品座落縣市
	 */
	public void setLocationCity(String value) {
		this.locationCity = value;
	}

	/**
	 * 取得擔保品座落縣市
	 */
	public String getLocationCity() {
		return locationCity;
	}

	/**
	 * 設定預約額度序號
	 */
	public void setLandBuildCntrno(String value) {
		this.landBuildCntrno = value;
	}

	/**
	 * 取得預約額度序號
	 */
	public String getLandBuildCntrno() {
		return landBuildCntrno;
	}
	
	/** 設定 座落區(國外專用) **/
	public void setLocationTarget(String locationTarget) {
		this.locationTarget = locationTarget;
	}

	/** 取得 座落區(國外專用)**/
	public String getLocationTarget() {
		return locationTarget;
	}

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 */
	public String getChkYN() {
		return chkYN;
	}

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 */
	public void setChkYN(String chkYN) {
		this.chkYN = chkYN;
	}

	/**
	 * 設定本案是否為建案完工未出售房屋之融資
	 */
	public void setRemainLoanYN(String remainLoanYN) {
		this.remainLoanYN = remainLoanYN;
	}

	/**
	 * 取得本案是否為建案完工未出售房屋之融資
	 */
	public String getRemainLoanYN() {
		return remainLoanYN;
	}

	/**
	 * 設定建案完工未出售房屋之融資-案件種類
	 */
	public void setRemainLoanClass(String remainLoanClass) {
		this.remainLoanClass = remainLoanClass;
	}

	/**
	 * 取得建案完工未出售房屋之融資-案件種類
	 */
	public String getRemainLoanClass() {
		return remainLoanClass;
	}

	/**
	 * 設定擔保品座落縣市
	 */
	public void setRemainLoanLocationCity(String remainLoanLocationCity) {
		this.remainLoanLocationCity = remainLoanLocationCity;
	}

	/**
	 * 取得擔保品座落縣市
	 */
	public String getRemainLoanLocationCity() {
		return remainLoanLocationCity;
	}

	/**
	 * 設定擔保品座落
	 */
	public void setRemainLoanLocationCd(String remainLoanLocationCd) {
		this.remainLoanLocationCd = remainLoanLocationCd;
	}

	/**
	 * 取得擔保品座落
	 */
	public String getRemainLoanLocationCd() {
		return remainLoanLocationCd;
	}

	/**
	 * 設定座落區段
	 */
	public void setRemainLoanSite3No(BigDecimal remainLoanSite3No) {
		this.remainLoanSite3No = remainLoanSite3No;
	}

	/**
	 * 取得座落區段
	 */
	public BigDecimal getRemainLoanSite3No() {
		return remainLoanSite3No;
	}

	/**
	 * 設定座落區村里
	 */
	public void setRemainLoanSite4No(String remainLoanSite4No) {
		this.remainLoanSite4No = remainLoanSite4No;
	}

	/**
	 * 取得座落區村里
	 */
	public String getRemainLoanSite4No() {
		return remainLoanSite4No;
	}

	/**
	 * 設定 前次是否為建案完工未出售房屋之融資
	 */
	public void setBremainLoanYN(String bremainLoanYN) {
		this.bremainLoanYN = bremainLoanYN;
	}

	/**
	 * 取得 前次是否為建案完工未出售房屋之融資
	 */
	public String getBremainLoanYN() {
		return bremainLoanYN;
	}

	/**
	 * 設定 前次建案完工未出售房屋之融資-案件種類
	 */
	public void setBremainLoanClass(String bremainLoanClass) {
		this.bremainLoanClass = bremainLoanClass;
	}

	/**
	 * 取得 前次建案完工未出售房屋之融資-案件種類
	 */
	public String getBremainLoanClass() {
		return bremainLoanClass;
	}

	/**
	 * 設定 前次擔保品座落縣市
	 */
	public void setBremainLoanLocationCity(String bremainLoanLocationCity) {
		this.bremainLoanLocationCity = bremainLoanLocationCity;
	}

	/**
	 * 取得 前次擔保品座落縣市
	 */
	public String getBremainLoanLocationCity() {
		return bremainLoanLocationCity;
	}

	/**
	 * 設定 前次擔保品座落
	 */
	public void setBremainLoanLocationCd(String bremainLoanLocationCd) {
		this.bremainLoanLocationCd = bremainLoanLocationCd;
	}

	/**
	 * 取得 前次擔保品座落
	 */
	public String getBremainLoanLocationCd() {
		return bremainLoanLocationCd;
	}

	/**
	 * 設定 前次座落區段
	 */
	public void setBremainLoanSite3No(BigDecimal bremainLoanSite3No) {
		this.bremainLoanSite3No = bremainLoanSite3No;
	}

	/**
	 * 取得 前次座落區段
	 */
	public BigDecimal getBremainLoanSite3No() {
		return bremainLoanSite3No;
	}

	/**
	 * 設定 前次座落區村里
	 */
	public void setBremainLoanSite4No(String bremainLoanSite4No) {
		this.bremainLoanSite4No = bremainLoanSite4No;
	}

	/**
	 * 取得 前次座落區村里
	 */
	public String getBremainLoanSite4No() {
		return bremainLoanSite4No;
	}

	/** 取得擔保品屋齡 **/
	public BigDecimal getHouse_age() {
		return house_age;
	}

	/** 設定擔保品屋齡 **/
	public void setHouse_age(BigDecimal new_house_age) {
		this.house_age = new_house_age;
	}

	/** 取得擔保品放款值 **/
	public BigDecimal getLoanAmt() {
		return loanAmt;
	}

	/** 設定擔保品放款值 **/
	public void setLoanAmt(BigDecimal new_loanAmt) {
		this.loanAmt = new_loanAmt;
	}

	/** 設定額度序號是否在空地貸款控制檔中 **/
	public void setIsClearLand(String isClearLand) {
		this.isClearLand = isClearLand;
	}

	/** 取得額度序號是否在空地貸款控制檔中 **/
	public String getIsClearLand() {
		return isClearLand;
	}

	/** 設定控管類別 **/
	public void setCtlType(String ctlType) {
		this.ctlType = ctlType;
	}

	/** 取得控管類別 **/
	public String getCtlType() {
		return ctlType;
	}

	/** 設定初次核定預計動工日 **/
	public void setFstDate(Date fstDate) {
		this.fstDate = fstDate;
	}

	/** 取得初次核定預計動工日 **/
	public Date getFstDate() {
		return fstDate;
	}

	/** 設定最新核定(動審)預計動工日 **/
	public void setLstDate(Date lstDate) {
		this.lstDate = lstDate;
	}

	/** 取得最新核定(動審)預計動工日 **/
	public Date getLstDate() {
		return lstDate;
	}

	/** 設定是否變更預計動工日 **/
	public void setIsChgStDate(String isChgStDate) {
		this.isChgStDate = isChgStDate;
	}

	/** 取得是否變更預計動工日 **/
	public String getIsChgStDate() {
		return isChgStDate;
	}

	/** 設定變更預計動工日 **/
	public void setCstDate(Date cstDate) {
		this.cstDate = cstDate;
	}

	/** 取得變更預計動工日 **/
	public Date getCstDate() {
		return cstDate;
	}

	/** 設定變更預計動工日原因 **/
	public void setCstReason(String cstReason) {
		this.cstReason = cstReason;
	}

	/** 取得變更預計動工日原因 **/
	public String getCstReason() {
		return cstReason;
	}

	/** 設定是否調降利率 **/
	public void setIsChgRate(String isChgRate) {
		this.isChgRate = isChgRate;
	}

	/** 取得是否調降利率 **/
	public String getIsChgRate() {
		return isChgRate;
	}

	/** 設定輸入本次採行措施 **/
	public void setAdoptFg(String adoptFg) {
		this.adoptFg = adoptFg;
	}

	/** 取得輸入本次採行措施 **/
	public String getAdoptFg() {
		return adoptFg;
	}

	/** 設定再加減碼幅度 **/
	public void setRateAdd(BigDecimal rateAdd) {
		this.rateAdd = rateAdd;
	}

	/** 取得再加減碼幅度 **/
	public BigDecimal getRateAdd() {
		return rateAdd;
	}

	/** 設定借款人ROA **/
	public void setCustRoa(BigDecimal custRoa) {
		this.custRoa = custRoa;
	}

	/** 取得借款人ROA **/
	public BigDecimal getCustRoa() {
		return custRoa;
	}

	/** 設定關係人ROA **/
	public void setRelRoa(BigDecimal relRoa) {
		this.relRoa = relRoa;
	}

	/** 取得關係人ROA **/
	public BigDecimal getRelRoa() {
		return relRoa;
	}

	/** 設定ROA查詢起日 **/
	public void setRoaBgnDate(Date roaBgnDate) {
		this.roaBgnDate = roaBgnDate;
	}

	/** 取得ROA查詢起日 **/
	public Date getRoaBgnDate() {
		return roaBgnDate;
	}

	/** 設定ROA查詢迄日 **/
	public void setRoaEndDate(Date roaEndDate) {
		this.roaEndDate = roaEndDate;
	}

	/** 取得ROA查詢迄日 **/
	public Date getRoaEndDate() {
		return roaEndDate;
	}

	/** 設定是否符合本行規定 **/
	public void setIsLegal(String isLegal) {
		this.isLegal = isLegal;
	}

	/** 取得是否符合本行規定 **/
	public String getIsLegal() {
		return isLegal;
	}

	public BigDecimal getFirstLoanUnsoldHouseQuantity() {
		return firstLoanUnsoldHouseQuantity;
	}

	public void setFirstLoanUnsoldHouseQuantity(
			BigDecimal firstLoanUnsoldHouseQuantity) {
		this.firstLoanUnsoldHouseQuantity = firstLoanUnsoldHouseQuantity;
	}

	public BigDecimal getCurrentUnsoldHouseQuantity() {
		return currentUnsoldHouseQuantity;
	}

	public void setCurrentUnsoldHouseQuantity(BigDecimal currentUnsoldHouseQuantity) {
		this.currentUnsoldHouseQuantity = currentUnsoldHouseQuantity;
	}

	public BigDecimal getMonthOfCreditPeriod() {
		return monthOfCreditPeriod;
	}

	public void setMonthOfCreditPeriod(BigDecimal monthOfCreditPeriod) {
		this.monthOfCreditPeriod = monthOfCreditPeriod;
	}

	public Date getFinalCreditPeriodEndDate() {
		return finalCreditPeriodEndDate;
	}

	public void setFinalCreditPeriodEndDate(Date finalCreditPeriodEndDate) {
		this.finalCreditPeriodEndDate = finalCreditPeriodEndDate;
	}

	public BigDecimal getPreFirstLoanUnsoldHouseQty() {
		return preFirstLoanUnsoldHouseQty;
	}

	public void setPreFirstLoanUnsoldHouseQty(BigDecimal preFirstLoanUnsoldHouseQty) {
		this.preFirstLoanUnsoldHouseQty = preFirstLoanUnsoldHouseQty;
	}

	public BigDecimal getPreCurrentUnsoldHouseQuantity() {
		return preCurrentUnsoldHouseQuantity;
	}

	public void setPreCurrentUnsoldHouseQuantity(
			BigDecimal preCurrentUnsoldHouseQuantity) {
		this.preCurrentUnsoldHouseQuantity = preCurrentUnsoldHouseQuantity;
	}

	public BigDecimal getPreMonthOfCreditPeriod() {
		return preMonthOfCreditPeriod;
	}

	public void setPreMonthOfCreditPeriod(BigDecimal preMonthOfCreditPeriod) {
		this.preMonthOfCreditPeriod = preMonthOfCreditPeriod;
	}

	public Date getPreFinalCreditPeriodEndDate() {
		return preFinalCreditPeriodEndDate;
	}

	public void setPreFinalCreditPeriodEndDate(Date preFinalCreditPeriodEndDate) {
		this.preFinalCreditPeriodEndDate = preFinalCreditPeriodEndDate;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getRealEstateLoanLimitReason() {
		return realEstateLoanLimitReason;
	}

	public void setRealEstateLoanLimitReason(String realEstateLoanLimitReason) {
		this.realEstateLoanLimitReason = realEstateLoanLimitReason;
	}

	public String getIs3rdHignHouse() {
		return is3rdHignHouse;
	}

	public void setIs3rdHignHouse(String is3rdHignHouse) {
		this.is3rdHignHouse = is3rdHignHouse;
	}

	public String getNotLatestVerDesc() {
		return notLatestVerDesc;
	}

	public void setNotLatestVerDesc(String notLatestVerDesc) {
		this.notLatestVerDesc = notLatestVerDesc;
	}

	public String getIsRenew() {
		return isRenew;
	}

	public void setIsRenew(String isRenew) {
		this.isRenew = isRenew;
	}

	public String getIsPayOldQuota() {
		return isPayOldQuota;
	}

	public void setIsPayOldQuota(String isPayOldQuota) {
		this.isPayOldQuota = isPayOldQuota;
	}

	public BigDecimal getOldQuota() {
		return oldQuota;
	}

	public void setOldQuota(BigDecimal oldQuota) {
		this.oldQuota = oldQuota;
	}

	public BigDecimal getPayOldAmt() {
		return payOldAmt;
	}

	public void setPayOldAmt(BigDecimal payOldAmt) {
		this.payOldAmt = payOldAmt;
	}

	public String getIsMatchUnsoldHouseItem() {
		return isMatchUnsoldHouseItem;
	}

	public void setIsMatchUnsoldHouseItem(String isMatchUnsoldHouseItem) {
		this.isMatchUnsoldHouseItem = isMatchUnsoldHouseItem;
	}

	public String getPayOldAmtItem() {
		return payOldAmtItem;
	}

	public void setPayOldAmtItem(String payOldAmtItem) {
		this.payOldAmtItem = payOldAmtItem;
	}

	public String getIsSaleCase() {
		return isSaleCase;
	}

	public void setIsSaleCase(String isSaleCase) {
		this.isSaleCase = isSaleCase;
	}

	public Date getActStartDate() {
		return actStartDate;
	}

	public void setActStartDate(Date actStartDate) {
		this.actStartDate = actStartDate;
	}

	public Date getCbControlLstDate() {
		return cbControlLstDate;
	}

	public void setCbControlLstDate(Date cbControlLstDate) {
		this.cbControlLstDate = cbControlLstDate;
	}

	public BigDecimal getTimeVal() {
		return timeVal;
	}

	public void setTimeVal(BigDecimal timeVal) {
		this.timeVal = timeVal;
	}
}
