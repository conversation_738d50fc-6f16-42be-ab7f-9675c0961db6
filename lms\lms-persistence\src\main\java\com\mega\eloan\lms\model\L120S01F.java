/* 
 * L120S01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金存放款外匯往來檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S01F", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L120S01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 存款－最近半年起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DPBDATE", columnDefinition="DATE")
	private Date dpBDate;

	/** 存款－最近半年迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DPEDATE", columnDefinition="DATE")
	private Date dpEDate;

	/** 
	 * 存款－幣別<p/>
	 * 101/03/01新增<br/>
	 *  TWD(預設)
	 */
	@Column(name="DPAVGCURR", length=3, columnDefinition="CHAR(3)")
	private String dpAvgCurr;

	/** 
	
	/** 
	 * 存款－平均存款餘額<p/>
	 * 仟元
	 */
	@Column(name="DPAVGAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal dpAvgAmt;

	/** 
	 * 存款－（單位）<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="DPAVGUNIT", columnDefinition="DECIMAL(13,0)")
	private Integer dpAvgUnit;
	
	/** 
	 * 國外匯款－年度<p/>
	 * YYYY
	 */
	@Column(name="FXYEAR", columnDefinition="DECIMAL(4,0)")
	private Integer fxYear;

	/** 國外匯款－幣別 **/
	@Column(name="FXCURR", length=3, columnDefinition="CHAR(3)")
	private String fxCurr;

	/** 
	 * 國外匯款－年度實績<p/>
	 * 仟元
	 */
	@Column(name="FXAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal fxAmt;

	/** 
	 * 國外匯款－年度單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="FXUNIT", columnDefinition="DECIMAL(13,0)")
	private Integer fxUnit;
	
	/** 
	 * 國外匯款－起始年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="FXBDATE", columnDefinition="DATE")
	private Date fxBDate;

	/** 
	 * 國外匯款－截止年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="FXEDATE", columnDefinition="DATE")
	private Date fxEDate;

	/** 國外匯款－起迄年月幣別 **/
	@Column(name="FX2CURR", length=3, columnDefinition="CHAR(3)")
	private String fx2Curr;

	/** 
	 * 國外匯款－起迄年月實績<p/>
	 * 仟元
	 */
	@Column(name="FX2AMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal fx2Amt;

	/** 
	 * 國外匯款－起迄年月單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="FX2UNIT", columnDefinition="DECIMAL(13,0)")
	private Integer fx2Unit;
	
	/** 
	 * 進口－年度<p/>
	 * YYYY
	 */
	@Column(name="IMYEAR", columnDefinition="DECIMAL(4,0)")
	private Integer imYear;

	/** 
	 * 進口－幣別<p/>
	 * 仟元
	 */
	@Column(name="IMCURR", length=3, columnDefinition="CHAR(3)")
	private String imCurr;

	/** 進口－年度實績 **/
	@Column(name="IMAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal imAmt;

	/** 
	 * 進口－年度單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="IMUNIT", columnDefinition="DECIMAL(13,0)")
	private Integer imUnit;
	
	/** 
	 * 進口－起始年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="IMBDATE", columnDefinition="DATE")
	private Date imBDate;

	/** 
	 * 進口－截止年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="IMEDATE", columnDefinition="DATE")
	private Date imEDate;

	/** 進口－起迄年月幣別 **/
	@Column(name="IM2CURR", length=3, columnDefinition="CHAR(3)")
	private String im2Curr;

	/** 進口－起迄年月實績 **/
	@Column(name="IM2AMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal im2Amt;

	/** 
	 * 進口－起迄年月單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="IM2UNIT", columnDefinition="DECIMAL(13,0)")
	private Integer im2Unit;
	
	/** 
	 * 出口－年度<p/>
	 * YYYY
	 */
	@Column(name="EXYEAR", columnDefinition="DECIMAL(4,0)")
	private Integer exYear;

	/** 
	 * 出口－幣別<p/>
	 * 仟元
	 */
	@Column(name="EXCURR", length=3, columnDefinition="CHAR(3)")
	private String exCurr;

	/** 出口－年度實績 **/
	@Column(name="EXAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal exAmt;

	/** 
	 * 出口－年度單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="EXUNIT", columnDefinition="DECIMAL(13,0)")
	private Integer exUnit;
	
	/** 
	 * 出口－起始年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="EXBDATE", columnDefinition="DATE")
	private Date exBDate;

	/** 
	 * 出口－截止年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="EXEDATE", columnDefinition="DATE")
	private Date exEDate;

	/** 出口－起迄年月幣別 **/
	@Column(name="EX2CURR", length=3, columnDefinition="CHAR(3)")
	private String ex2Curr;

	/** 出口－起迄年月實績 **/
	@Column(name="EX2AMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal ex2Amt;

	/** 
	 * 出口－起迄年月單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="EX2UNIT", columnDefinition="DECIMAL(13,0)")
	private Integer ex2Unit;
	
	/** 
	 * 授信契約期間平均動用率<p/>
	 * 999.99%
	 */
	@Column(name="AVGURATE", columnDefinition="DECIMAL(5,2)")
	private Double avgURate;

	/** 
	 * 貢獻度－起始年月<p/>
	 * 日一律為’01’，YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CNTRBDATE", columnDefinition="DATE")
	private Date cntrBDate;

	/** 
	 * 貢獻度－截止年月<p/>
	 * 日一律為’01’，YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CNTREDATE", columnDefinition="DATE")
	private Date cntrEDate;

	/** 
	 * 貢獻度(幣別)<p/>
	 * 101/03/01新增<br/>
	 *  TWD(預設)
	 */
	@Column(name="CNTRCURR", length=3, columnDefinition="CHAR(3)")
	private String cntrCurr;
	
	/** 
	 * 貢獻度<p/>
	 * 幣別單位TWD仟元
	 */
	@Column(name="CNTRAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal cntrAmt;

	/** 
	 * 貢獻度(單位)<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="CNTRUNIT", columnDefinition="DECIMAL(13,0)")
	private Integer cntrUnit;
	
	
	/** 
	 * 本行轉投資等非授信業務金額(幣別)<p/>
	 * 101/03/01新增<br/>
	 *  TWD(預設)<br/>
	 *  (gfnGetLNFE0811)
	 */
	@Column(name="NONLOANCURR", length=3, columnDefinition="CHAR(3)")
	private String nonLoanCurr;
	
	/** 
	 * 本行轉投資等非授信業務金額<p/>
	 * 幣別單位TWD仟元<br/>
	 *  (gfnGetLNFE0811)
	 */
	@Column(name="NONLOANAMT", columnDefinition="DECIMAL(15,0)")
	private BigDecimal nonLoanAmt;

	/** 
	 * 本行轉投資等非授信業務金額(單位)<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 *  仟萬元：10000000
	 */
	@Column(name="NONLOANUNIT", columnDefinition="DECIMAL(13,0)")
	private Integer nonLoanUnit;
	
	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得存款－最近半年起日 **/
	public Date getDpBDate() {
		return this.dpBDate;
	}
	/** 設定存款－最近半年起日 **/
	public void setDpBDate(Date value) {
		this.dpBDate = value;
	}

	/** 取得存款－最近半年迄日 **/
	public Date getDpEDate() {
		return this.dpEDate;
	}
	/** 設定存款－最近半年迄日 **/
	public void setDpEDate(Date value) {
		this.dpEDate = value;
	}

	/** 
	 * 取得存款－幣別<p/>
	 * 101/03/01新增<br/>
	 *  TWD(預設)
	 */
	public String getDpAvgCurr() {
		return this.dpAvgCurr;
	}
	/**
	 *  設定存款－幣別<p/>
	 *  101/03/01新增<br/>
	 *  TWD(預設)
	 **/
	public void setDpAvgCurr(String value) {
		this.dpAvgCurr = value;
	}
	
	/** 
	 * 取得存款－平均存款餘額<p/>
	 * 仟元
	 */
	public BigDecimal getDpAvgAmt() {
		return this.dpAvgAmt;
	}
	/**
	 *  設定存款－平均存款餘額<p/>
	 *  仟元
	 **/
	public void setDpAvgAmt(BigDecimal value) {
		this.dpAvgAmt = value;
	}

	/** 
	 * 取得存款－（單位）<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getDpAvgUnit() {
		return this.dpAvgUnit;
	}
	/**
	 *  設定存款－（單位）<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setDpAvgUnit(Integer value) {
		this.dpAvgUnit = value;
	}
	
	/** 
	 * 取得國外匯款－年度<p/>
	 * YYYY
	 */
	public Integer getFxYear() {
		return this.fxYear;
	}
	/**
	 *  設定國外匯款－年度<p/>
	 *  YYYY
	 **/
	public void setFxYear(Integer value) {
		this.fxYear = value;
	}

	/** 取得國外匯款－幣別 **/
	public String getFxCurr() {
		return this.fxCurr;
	}
	/** 設定國外匯款－幣別 **/
	public void setFxCurr(String value) {
		this.fxCurr = value;
	}

	/** 
	 * 取得國外匯款－年度實績<p/>
	 * 仟元
	 */
	public BigDecimal getFxAmt() {
		return this.fxAmt;
	}
	/**
	 *  設定國外匯款－年度實績<p/>
	 *  仟元
	 **/
	public void setFxAmt(BigDecimal value) {
		this.fxAmt = value;
	}

	/** 
	 * 取得國外匯款－年度單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getFxUnit() {
		return this.fxUnit;
	}
	/**
	 *  設定國外匯款－年度單位<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setFxUnit(Integer value) {
		this.fxUnit = value;
	}
	
	/** 
	 * 取得國外匯款－起始年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	public Date getFxBDate() {
		return this.fxBDate;
	}
	/**
	 *  設定國外匯款－起始年月<p/>
	 *  日一律為”01”，YYYY-MM-01
	 **/
	public void setFxBDate(Date value) {
		this.fxBDate = value;
	}

	/** 
	 * 取得國外匯款－截止年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	public Date getFxEDate() {
		return this.fxEDate;
	}
	/**
	 *  設定國外匯款－截止年月<p/>
	 *  日一律為”01”，YYYY-MM-01
	 **/
	public void setFxEDate(Date value) {
		this.fxEDate = value;
	}

	/** 取得國外匯款－起迄年月幣別 **/
	public String getFx2Curr() {
		return this.fx2Curr;
	}
	/** 設定國外匯款－起迄年月幣別 **/
	public void setFx2Curr(String value) {
		this.fx2Curr = value;
	}

	/** 
	 * 取得國外匯款－起迄年月實績<p/>
	 * 仟元
	 */
	public BigDecimal getFx2Amt() {
		return this.fx2Amt;
	}
	/**
	 *  設定國外匯款－起迄年月實績<p/>
	 *  仟元
	 **/
	public void setFx2Amt(BigDecimal value) {
		this.fx2Amt = value;
	}

	/** 
	 * 取得國外匯款－起迄年月單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getFx2Unit() {
		return this.fx2Unit;
	}
	/**
	 *  設定國外匯款－起迄年月單位<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setFx2Unit(Integer value) {
		this.fx2Unit = value;
	}
	
	/** 
	 * 取得進口－年度<p/>
	 * YYYY
	 */
	public Integer getImYear() {
		return this.imYear;
	}
	/**
	 *  設定進口－年度<p/>
	 *  YYYY
	 **/
	public void setImYear(Integer value) {
		this.imYear = value;
	}

	/** 
	 * 取得進口－幣別<p/>
	 * 仟元
	 */
	public String getImCurr() {
		return this.imCurr;
	}
	/**
	 *  設定進口－幣別<p/>
	 *  仟元
	 **/
	public void setImCurr(String value) {
		this.imCurr = value;
	}

	/** 取得進口－年度實績 **/
	public BigDecimal getImAmt() {
		return this.imAmt;
	}
	/** 設定進口－年度實績 **/
	public void setImAmt(BigDecimal value) {
		this.imAmt = value;
	}

	/** 
	 * 取得進口－年度單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getImUnit() {
		return this.imUnit;
	}
	/**
	 *  設定進口－年度單位<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setImUnit(Integer value) {
		this.imUnit = value;
	}
	
	/** 
	 * 取得進口－起始年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	public Date getImBDate() {
		return this.imBDate;
	}
	/**
	 *  設定進口－起始年月<p/>
	 *  日一律為”01”，YYYY-MM-01
	 **/
	public void setImBDate(Date value) {
		this.imBDate = value;
	}

	/** 
	 * 取得進口－截止年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	public Date getImEDate() {
		return this.imEDate;
	}
	/**
	 *  設定進口－截止年月<p/>
	 *  日一律為”01”，YYYY-MM-01
	 **/
	public void setImEDate(Date value) {
		this.imEDate = value;
	}

	/** 取得進口－起迄年月幣別 **/
	public String getIm2Curr() {
		return this.im2Curr;
	}
	/** 設定進口－起迄年月幣別 **/
	public void setIm2Curr(String value) {
		this.im2Curr = value;
	}

	/** 取得進口－起迄年月實績 **/
	public BigDecimal getIm2Amt() {
		return this.im2Amt;
	}
	/** 設定進口－起迄年月實績 **/
	public void setIm2Amt(BigDecimal value) {
		this.im2Amt = value;
	}

	/** 
	 * 取得進口－起迄年月單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getIm2Unit() {
		return this.im2Unit;
	}
	/**
	 *  設定進口－起迄年月單位<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setIm2Unit(Integer value) {
		this.im2Unit = value;
	}
	
	/** 
	 * 取得出口－年度<p/>
	 * YYYY
	 */
	public Integer getExYear() {
		return this.exYear;
	}
	/**
	 *  設定出口－年度<p/>
	 *  YYYY
	 **/
	public void setExYear(Integer value) {
		this.exYear = value;
	}

	/** 
	 * 取得出口－幣別<p/>
	 * 仟元
	 */
	public String getExCurr() {
		return this.exCurr;
	}
	/**
	 *  設定出口－幣別<p/>
	 *  仟元
	 **/
	public void setExCurr(String value) {
		this.exCurr = value;
	}

	/** 取得出口－年度實績 **/
	public BigDecimal getExAmt() {
		return this.exAmt;
	}
	/** 設定出口－年度實績 **/
	public void setExAmt(BigDecimal value) {
		this.exAmt = value;
	}

	/** 
	 * 取得出口－年度單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getExUnit() {
		return this.exUnit;
	}
	/**
	 *  設定出口－年度單位<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setExUnit(Integer value) {
		this.exUnit = value;
	}
	
	/** 
	 * 取得出口－起始年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	public Date getExBDate() {
		return this.exBDate;
	}
	/**
	 *  設定出口－起始年月<p/>
	 *  日一律為”01”，YYYY-MM-01
	 **/
	public void setExBDate(Date value) {
		this.exBDate = value;
	}

	/** 
	 * 取得出口－截止年月<p/>
	 * 日一律為”01”，YYYY-MM-01
	 */
	public Date getExEDate() {
		return this.exEDate;
	}
	/**
	 *  設定出口－截止年月<p/>
	 *  日一律為”01”，YYYY-MM-01
	 **/
	public void setExEDate(Date value) {
		this.exEDate = value;
	}

	/** 取得出口－起迄年月幣別 **/
	public String getEx2Curr() {
		return this.ex2Curr;
	}
	/** 設定出口－起迄年月幣別 **/
	public void setEx2Curr(String value) {
		this.ex2Curr = value;
	}

	/** 取得出口－起迄年月實績 **/
	public BigDecimal getEx2Amt() {
		return this.ex2Amt;
	}
	/** 設定出口－起迄年月實績 **/
	public void setEx2Amt(BigDecimal value) {
		this.ex2Amt = value;
	}

	/** 
	 * 取得出口－起迄年月單位<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getEx2Unit() {
		return this.ex2Unit;
	}
	/**
	 *  設定出口－起迄年月單位<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setEx2Unit(Integer value) {
		this.ex2Unit = value;
	}
	
	/** 
	 * 取得授信契約期間平均動用率<p/>
	 * 999.99%
	 */
	public Double getAvgURate() {
		return this.avgURate;
	}
	/**
	 *  設定授信契約期間平均動用率<p/>
	 *  999.99%
	 **/
	public void setAvgURate(Double value) {
		this.avgURate = value;
	}

	/** 
	 * 取得貢獻度－起始年月<p/>
	 * 日一律為’01’，YYYY-MM-01
	 */
	public Date getCntrBDate() {
		return this.cntrBDate;
	}
	/**
	 *  設定貢獻度－起始年月<p/>
	 *  日一律為’01’，YYYY-MM-01
	 **/
	public void setCntrBDate(Date value) {
		this.cntrBDate = value;
	}

	/** 
	 * 取得貢獻度－截止年月<p/>
	 * 日一律為’01’，YYYY-MM-01
	 */
	public Date getCntrEDate() {
		return this.cntrEDate;
	}
	/**
	 *  設定貢獻度－截止年月<p/>
	 *  日一律為’01’，YYYY-MM-01
	 **/
	public void setCntrEDate(Date value) {
		this.cntrEDate = value;
	}

	/** 
	 * 取得貢獻度(幣別)<p/>
	 * 101/03/01新增<br/>
	 *  TWD(預設)
	 */
	public String getCntrCurr() {
		return this.cntrCurr;
	}
	/**
	 *  設定貢獻度(幣別)<p/>
	 *  101/03/01新增<br/>
	 *  TWD(預設)
	 **/
	public void setCntrCurr(String value) {
		this.cntrCurr = value;
	}
	
	/** 
	 * 取得貢獻度<p/>
	 * 幣別單位TWD仟元
	 */
	public BigDecimal getCntrAmt() {
		return this.cntrAmt;
	}
	/**
	 *  設定貢獻度<p/>
	 *  幣別單位TWD仟元
	 **/
	public void setCntrAmt(BigDecimal value) {
		this.cntrAmt = value;
	}

	/** 
	 * 取得本行轉投資等非授信業務金額<p/>
	 * 幣別單位TWD仟元<br/>
	 *  (gfnGetLNFE0811)
	 */
	public BigDecimal getNonLoanAmt() {
		return this.nonLoanAmt;
	}
	
	/** 
	 * 取得貢獻度(單位)<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getCntrUnit() {
		return this.cntrUnit;
	}
	/**
	 *  設定貢獻度(單位)<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setCntrUnit(Integer value) {
		this.cntrUnit = value;
	}

	/** 
	 * 取得本行轉投資等非授信業務金額(幣別)<p/>
	 * 101/03/01新增<br/>
	 *  TWD(預設)<br/>
	 *  (gfnGetLNFE0811)
	 */
	public String getNonLoanCurr() {
		return this.nonLoanCurr;
	}
	/**
	 *  設定本行轉投資等非授信業務金額(幣別)<p/>
	 *  101/03/01新增<br/>
	 *  TWD(預設)<br/>
	 *  (gfnGetLNFE0811)
	 **/
	public void setNonLoanCurr(String value) {
		this.nonLoanCurr = value;
	}
	
	/**
	 *  設定本行轉投資等非授信業務金額<p/>
	 *  幣別單位TWD仟元<br/>
	 *  (gfnGetLNFE0811)
	 **/
	public void setNonLoanAmt(BigDecimal value) {
		this.nonLoanAmt = value;
	}

	/** 
	 * 取得本行轉投資等非授信業務金額(單位)<p/>
	 * 101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 */
	public Integer getNonLoanUnit() {
		return this.nonLoanUnit;
	}
	/**
	 *  設定本行轉投資等非授信業務金額(單位)<p/>
	 *  101/03/01新增<br/>
	 *  元：1<br/>
	 *  千元：1000(預設)<br/>
	 *  萬元：10000<br/>
	 *  百萬元：1000000
	 **/
	public void setNonLoanUnit(Integer value) {
		this.nonLoanUnit = value;
	}
	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 建構子 **/
	public L120S01F() {}

	/** 建構子
	* @param mainId (文件編號)
	* @param custId (身分證統編)
	* @param dupNo (身分證統編重複碼)
	**/
	public L120S01F (String mainId,String custId,String dupNo){
		this.mainId = mainId;
		this.custId = custId;
		this.dupNo = dupNo;
	}
}
