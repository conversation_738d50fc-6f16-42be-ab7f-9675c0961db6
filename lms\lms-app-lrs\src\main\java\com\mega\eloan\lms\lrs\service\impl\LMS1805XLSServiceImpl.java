package com.mega.eloan.lms.lrs.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.L180M01BDao;
import com.mega.eloan.lms.lrs.pages.LMS1805M01Page;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service("lms1805xlsservice")
public class LMS1805XLSServiceImpl implements FileDownloadService {
	
	@Resource
	L180M01ADao l180m01aDao;
	
	@Resource
	L180M01BDao l180m01bDao;
	
	@Resource
	BranchService branch;
	
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1805XLSServiceImpl.class);
	
	/** 設定type9 營業單位授信報案考核彙總表 xls的title -原始資料
	 * @param sheet
	 * @param format14Left 字型
	 * @param x X軸位置
	 * @param y Y軸位置
	 * @return
	 * @throws RowsExceededException
	 * @throws WriteException
	 */
	 private HSSFSheet setExcelTitle(HSSFSheet sheet, HSSFCellStyle format12Center, int x, int y, Properties prop) {
	        HSSFRow row0 = sheet.getRow(0);
	        if (row0 == null) row0 = sheet.createRow(0);
	        row0.setHeight((short) 600);

	        HSSFRow row2 = sheet.getRow(2);
	        if (row2 == null) row2 = sheet.createRow(2);
	        row2.setHeight((short) 450);

	        HSSFRow rowY = sheet.getRow(y);
	        if (rowY == null) rowY = sheet.createRow(y);
	        rowY.setHeight((short) 600);

	        sheet.setColumnWidth(x + 0, 7 * 256);
	        sheet.setColumnWidth(x + 1, 19 * 256);
	        sheet.setColumnWidth(x + 2, 22 * 256);
	        sheet.setColumnWidth(x + 3, 19 * 256);
	        sheet.setColumnWidth(x + 4, 19 * 256);

	        HSSFCell cell_x0_y = rowY.createCell(x + 0);
	        cell_x0_y.setCellValue("");
	        cell_x0_y.setCellStyle(format12Center);

	        HSSFCell cell_x1_y = rowY.createCell(x + 1);
	        cell_x1_y.setCellValue(prop.getProperty("branchId"));
	        cell_x1_y.setCellStyle(format12Center);

	        HSSFCell cell_x2_y = rowY.createCell(x + 2);
	        cell_x2_y.setCellValue(prop.getProperty("branchName"));
	        cell_x2_y.setCellStyle(format12Center);

	        HSSFCell cell_x3_y = rowY.createCell(x + 3);
	        cell_x3_y.setCellValue(prop.getProperty("preCount"));
	        cell_x3_y.setCellStyle(format12Center);

	        HSSFCell cell_x4_y = rowY.createCell(x + 4);
	        cell_x4_y.setCellValue(prop.getProperty("createExcel2.title06"));
	        cell_x4_y.setCellStyle(format12Center);

	        return sheet;
	    }
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		ByteArrayOutputStream baos = null;
        HSSFWorkbook workbook = null;
        HSSFSheet sheet = null;
        HSSFFont font10 = null;
        HSSFFont font14 = null;
        HSSFCellStyle format14Center = null;
        HSSFCellStyle format12Center = null;
        HSSFCellStyle format12Left = null;
        HSSFCellStyle format12Right = null;
        HSSFCellStyle format12LeftNO = null;
        HSSFCellStyle format12RightNO = null;
        HSSFCellStyle format10LeftNO = null;
        HSSFCellStyle format10RightNO = null;
		Properties lms1805m01prop = null;
		try {
			baos = new ByteArrayOutputStream();
            workbook = new HSSFWorkbook();

            lms1805m01prop = MessageBundleScriptCreator.getComponentResource(LMS1805M01Page.class);
            sheet = workbook.createSheet(lms1805m01prop.getProperty("listPre")); // POI sheet creation, index 0 is default

            font14 = workbook.createFont();
            font14.setFontName("新細明體");
            font14.setFontHeightInPoints((short) 14);
            font14.setBold(true);

            font10 = workbook.createFont();
            font10.setFontName("新細明體");
            font10.setFontHeightInPoints((short) 10);
            font10.setBold(false);

            format12Center = workbook.createCellStyle();
            format12Center.setFont(font10);
            format12Center.setAlignment(HorizontalAlignment.CENTER);
            format12Center.setVerticalAlignment(VerticalAlignment.TOP);
            format12Center.setWrapText(true);
            format12Center.setBorderTop(BorderStyle.THIN);
            format12Center.setBorderBottom(BorderStyle.THIN);
            format12Center.setBorderLeft(BorderStyle.THIN);
            format12Center.setBorderRight(BorderStyle.THIN);

            format12Left = workbook.createCellStyle();
            format12Left.setFont(font10);
            format12Left.setAlignment(HorizontalAlignment.LEFT);
            format12Left.setVerticalAlignment(VerticalAlignment.TOP);
            format12Left.setWrapText(true);
            format12Left.setBorderTop(BorderStyle.THIN);
            format12Left.setBorderBottom(BorderStyle.THIN);
            format12Left.setBorderLeft(BorderStyle.THIN);
            format12Left.setBorderRight(BorderStyle.THIN);

            format12Right = workbook.createCellStyle();
            format12Right.setFont(font10);
            format12Right.setAlignment(HorizontalAlignment.RIGHT);
            format12Right.setVerticalAlignment(VerticalAlignment.TOP);
            format12Right.setWrapText(true);
            format12Right.setBorderTop(BorderStyle.THIN);
            format12Right.setBorderBottom(BorderStyle.THIN);
            format12Right.setBorderLeft(BorderStyle.THIN);
            format12Right.setBorderRight(BorderStyle.THIN);

            format12LeftNO = workbook.createCellStyle();
            format12LeftNO.setFont(font10);
            format12LeftNO.setAlignment(HorizontalAlignment.LEFT);
            format12LeftNO.setVerticalAlignment(VerticalAlignment.TOP);
            format12LeftNO.setWrapText(false);

            format12RightNO = workbook.createCellStyle();
            format12RightNO.setFont(font10);
            format12RightNO.setAlignment(HorizontalAlignment.RIGHT);
            format12RightNO.setVerticalAlignment(VerticalAlignment.TOP);
            format12RightNO.setWrapText(false);

            format14Center = workbook.createCellStyle();
            format14Center.setFont(font14);
            format14Center.setAlignment(HorizontalAlignment.CENTER);
            format14Center.setVerticalAlignment(VerticalAlignment.TOP);
            format14Center.setWrapText(false);
            format14Center.setBorderTop(BorderStyle.THIN);
            format14Center.setBorderBottom(BorderStyle.THIN);
            format14Center.setBorderLeft(BorderStyle.THIN);
            format14Center.setBorderRight(BorderStyle.THIN);

            format10LeftNO = workbook.createCellStyle();
            format10LeftNO.setFont(font10);
            format10LeftNO.setAlignment(HorizontalAlignment.LEFT);
            format10LeftNO.setVerticalAlignment(VerticalAlignment.TOP);
            format10LeftNO.setWrapText(false);

            format10RightNO = workbook.createCellStyle();
            format10RightNO.setFont(font10);
            format10RightNO.setAlignment(HorizontalAlignment.RIGHT);
            format10RightNO.setVerticalAlignment(VerticalAlignment.TOP);
            format10RightNO.setWrapText(false);
			
			//標題
            MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

            HSSFRow row0 = sheet.getRow(0);
            if (row0 == null) row0 = sheet.createRow(0);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
            HSSFCell titleCell = row0.createCell(0);
            titleCell.setCellValue(branch.getBranchName(user.getUnitNo()) + lms1805m01prop.getProperty("listPre"));
            titleCell.setCellStyle(format14Center);
            
            //指定年月
            HSSFRow row2 = sheet.getRow(2);
            if (row2 == null) row2 = sheet.createRow(2);

            String dataDate = params.getString("dataDate");
            HSSFCell assignYMCell = row2.createCell(0);
            if (!Util.isEmpty(dataDate)) {
                assignYMCell.setCellValue(lms1805m01prop.getProperty("assignYM") + "：" + dataDate);
            } else {
                assignYMCell.setCellValue(lms1805m01prop.getProperty("assignYM") + "：" + TWNDate.toAD(CapDate.getCurrentTimestamp()).substring(0, 7));
            }
            assignYMCell.setCellStyle(format12LeftNO);

            sheet.addMergedRegion(new CellRangeAddress(2, 2, 3, 4));
            HSSFCell searchDateCell = row2.createCell(3);
            searchDateCell.setCellValue(lms1805m01prop.getProperty("searchDate") + "：" + TWNDate.toAD(CapDate.getCurrentTimestamp()));
            searchDateCell.setCellStyle(format12RightNO);
            
            this.setExcelTitle(sheet, format12Center, 0, 3, lms1805m01prop);
            
            List<IBranch> bank = branch.getBranchOfGroup(user.getUnitNo());
            bank.add(branch.getBranch(user.getUnitNo()));
            
            for(int i=0,size=bank.size();i<size;i++){
            	IBranch ibranch = bank.get(i);
                ISearch isearch = l180m01aDao.createSearchTemplete();
                isearch.addSearchModeParameters(SearchMode.EQUALS, "dataDate", CapDate.parseDate(dataDate + "-01"));
                isearch.addSearchModeParameters(SearchMode.EQUALS, "docStatus", RetrialDocStatusEnum.編製中.getCode());
                isearch.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
                isearch.addSearchModeParameters(SearchMode.EQUALS, "branchId", ibranch.getBrNo());
                isearch.setMaxResults(Integer.MAX_VALUE);
                List<L180M01A> l180m01as = l180m01aDao.find(isearch);
                
                List<L180M01B> l180m01bs = new ArrayList<L180M01B>();
                if(!Util.isEmpty(l180m01as) && l180m01as.size() != 0){
                    List<String> mainIds = new ArrayList<String>();
                    for(L180M01A l180m01a : l180m01as){
                    	mainIds.add(l180m01a.getMainId());
                    }
                    ISearch searchDe = l180m01bDao.createSearchTemplete();
                    searchDe.addSearchModeParameters(SearchMode.IN, "mainId", mainIds.toArray());
                    searchDe.setMaxResults(Integer.MAX_VALUE);
                    l180m01bs = l180m01bDao.find(searchDe);
                }
                
                int count = 0;
                for(L180M01B l180m01b: l180m01bs){
                	if("1".equals(l180m01b.getDocStatus1())){
                		count++;
                	}
                }
                HSSFRow branchDataRow = sheet.getRow(4 + i);
                if (branchDataRow == null) branchDataRow = sheet.createRow(4 + i);
                branchDataRow.setHeight((short) 450);

                HSSFCell itemsCell = branchDataRow.createCell(0);
                itemsCell.setCellValue((i + 1) + "");
                itemsCell.setCellStyle(format12Center);

                HSSFCell branchIdCell = branchDataRow.createCell(1);
                branchIdCell.setCellValue(Util.nullToSpace(ibranch.getBrNo()));
                branchIdCell.setCellStyle(format12Center);

                HSSFCell branchNMCell = branchDataRow.createCell(2);
                branchNMCell.setCellValue(Util.nullToSpace(branch.getBranchName(ibranch.getBrNo())));
                branchNMCell.setCellStyle(format12Left);

                HSSFCell docStatusYCell = branchDataRow.createCell(3);
                docStatusYCell.setCellValue(Util.nullToSpace(count));
                docStatusYCell.setCellStyle(format12Right);

                HSSFCell docStatusNCell = branchDataRow.createCell(4);
                docStatusNCell.setCellValue(Util.nullToSpace(l180m01bs.size() - count));
                docStatusNCell.setCellStyle(format12Right);
            }
            
            workbook.write(baos); 
            return baos.toByteArray();
            
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
                try {
                    baos.close();
                } catch (IOException ex) {
                    LOGGER.error("[getContent] Exception closing ByteArrayOutputStream!!", ex.getMessage());
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException ex) {
                    LOGGER.error("[getContent] Exception closing workbook!!", ex.getMessage());
                }
            }
		}
		return null;
	}
}
