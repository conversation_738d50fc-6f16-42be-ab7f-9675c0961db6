package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF411;

public interface MisELF411Service {
	public List<ELF411> find_ELF411_NEWADD_notEmpty(String dataym, String branch);

	public List<ELF411> find_ELF411_NEWADD_notEmpty(String dataym,
			String branch, String custId, String dupNo);

	public String getMaxDataYM();

	public List<ELF411> getData(String branch, String dataym);

	public boolean isELF411Ready(String dataym);

	public List<Map<String, Object>> gfnGenerateCTL_FLMS180R11(String branch,
			String begDATAYM, String endDATAYM);

	/**
	 * 取得最小實地覆審基準日 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * 
	 * @param brNo
	 * @param contracts
	 * @return
	 */
	public List<Map<String, Object>> getMinDataYMByBrNoAndContract(String brNo,
			String[] contracts);
	
	/**
	 * 取得最大實地覆審基準日
	 * J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * @param brNo
	 * @param contracts
	 * @return
	 */
	public List<Map<String, Object>> getMaxDataYMByBrNoAndContract(String brNo,
			String[] contracts) ;
}
