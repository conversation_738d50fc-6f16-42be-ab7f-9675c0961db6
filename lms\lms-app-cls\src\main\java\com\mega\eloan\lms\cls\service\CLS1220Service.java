package com.mega.eloan.lms.cls.service;

/* 
 * CLS1021Service.java
 * 
 * Copyright (c) 2011-2013 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01B;
import com.mega.eloan.lms.model.C122M01C;
import com.mega.eloan.lms.model.C122M01D;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.eloan.lms.model.C122M01G;
import com.mega.eloan.lms.model.C122S01A;
import com.mega.eloan.lms.model.C122S01B;
import com.mega.eloan.lms.model.C122S01C;
import com.mega.eloan.lms.model.C122S01E;
import com.mega.eloan.lms.model.C122S01F;
import com.mega.eloan.lms.model.C122S01G;
import com.mega.eloan.lms.model.C122S01H;
import com.mega.eloan.lms.model.C122S01Y;
import com.mega.eloan.lms.model.C900M01N;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L161S01D;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;


/**
 * <pre>
 *  線上申貸原始資料
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,GaryChang,new
 *          </ul>
 */
public interface CLS1220Service extends AbstractService {

	/**
	 * 
	 * @param search
	 *            提供給grid handler的搜尋條件
	 * @return Page<C122M01A>
	 */
	Page<C122M01A> getC122V01(ISearch search);
	Page<C122M01A> getC122M01A(ISearch search);
	List<C122M01A> getC122M01AList(ISearch search);
	Page<C122M01C> getC122M01C(ISearch search);
	Page<C122S01A> getC122S01A(ISearch search);
	Page<C122S01B> getC122S01B(ISearch search);
	Page<L161S01D> getL161S01D(ISearch search);
	C122M01A getC122M01A_byOid(String oid);
	C122M01A getC122M01A_byMainId(String mainId);
	public C122M01B getC122M01B_byMainIdItemType(String mainId, String itemType);
	public List<C122M01B> getC122M01B_byMainId(String mainId);
	
	public C122M01C getC122M01C_byMainIdSeq(String mainId, int seq);
	public List<C122M01C> getC122M01C_byMainIdOrderBySeqAsc(String mainId);
	public List<C122M01C> getC122M01C_byMainIdOrderBySeqDesc(String mainId);
	public List<C122M01D> getC122M01D_byMainIdOrderBySeqAsc(String mainId);
	public C122M01C changeOwnBrId(C122M01A parent_meta, String newBrNo, String memo) throws CapMessageException;
	public void changeOwnBrId_notifyT1(String ploanCaseId, String applyKind, C122M01C c122m01c, String custId, String custName);
	public JSONObject ploan_update_brNo(String ploanCaseId, String newBrNo);
	public JSONObject ploan_discard_loanCase(String empNo, String ploanCaseId);
	
	C122S01A getC122S01A_byOid(String oid);
	C122S01A getC122S01A_mainIdBatchNo(String mainId, int batchNo);
	C122S01B getC122S01B_byOid(String oid);
	C122S01B getC122S01B_byUK(String mainId, Integer batchNo, String cntrNoMainId, Integer seq);
	Map<String, String> getC122S01A_batchNo(String mainId, boolean addNew);
	L140S02A getL140S02A_byOid(String oid);
	L140S02A getL140S02A_byUK(String mainId, Integer seq);
	
	void save(GenericBean... entity);
	void daoSaveC122M01A(C122M01A meta);
	void daoSaveC122S01A(C122S01A model);
	public Map<String, String> get_ApplyDocStatusDescMap();
	public Map<String, String> get_PloanStatFlagDescMap(String applyKind);
	public Map<String, String> get_DocStatusDescMap();
	public Map<String, String> get_ApplyKindB_statFlagDescMap();
	
	public C120S01A findC120S01A(String mainId, String custId, String dupNo);
	public C120S01B findC120S01B(String mainId, String custId, String dupNo);
	public C120S01C findC120S01C(String mainId, String custId, String dupNo);
	
	public C122M01A findLatestInProgressC122M01A(String ownBrId, String[] applyKind_arr, String custId, String dupNo);
		
	public void addC122S01A_B(C122M01A meta, C122S01A c122s01a, List<L140S02A> l140s02a_oid_list, List<String> promptMsg);	
	
	public int maxBatchNoInC122S01A(String mainId);
	
	public void delC122S01B(C122M01A meta, List<String> c122s01b_oid_list);
	
	public List<C122M01A> queryUnMatchReason(String custId, String dupNo);
	
	public L140M01A findL140M01A(String mainId);
	
	C122S01B getC122S01B_byMainId(String mainId);

	public String getDesc_ploan_basicInfo_serviceAssociateDeptCode(String s);
	public C122M01A getPloanParentMeta(C122M01A meta);
	public String build_loanBrNo(Map<String, String> cache_map, C122M01A model);
	public String build_payrollTransfersBrNo(Map<String, String> cache_map, C122M01A model);

	public List<C122M01A> findC122M01A_by_ploanCaseId(String ploanCaseId);
	/**
	 * 取得中鋼母、子公司的行銷方案(C開頭)
	 * 可傳入分行代號，未來可以針對不同分行要做的團貸做判斷
	 * @param brno
	 * @return
	 */
	public Map<String, String> getPloanPlanList(String brno);
	
	public List<C122S01E> findTodayRecord(String mainId);
	
	//J110-0354_B1001 進件管理
	public Map<String, String> get_ApplyKindDescMap();
	public Map<String, String> get_IncomTypeMap();
	public Map<String, String> get_DocStatusNewDescMap();
	public List<Map<String, Object>> getRealEstateAgentInfo(String l140m01a_mainId);
	
	public C122M01F findC122M01F(String mainId);
	public List<C122S01Y> findC122S01YbyMainId(String mainId);
	public C122S01Y findC122S01Y(String oid);
	
	public List<C122M01G> findC122M01GbyMainId(String mainId);
	
	public List<C101M01A> findC101M01A(String ownBrId, String custId, String dupNo);
	
	public List<C122S01F> findC122S01F_A02(String mainId, String docStatus);
	Page<C122S01G> getC122S01GPage(ISearch search);
	public C122S01G getC122S01G(String oid);
	void deleteC122S01G(C122S01G c122s01g);
	public List<C122S01G> findC122S01G(String mainId, String docStatus);
	public List<C122S01G> findC122S01GbyMainid(String mainId);
	void saveC122S01G(C122S01G c122s01g);
	Page<C122S01H> getC122S01HPage(ISearch search);
	public Map<String, String> get_FlowIdDescMap();
	public C122S01H getC122S01H(String oid);
	void deleteC122S01H(C122S01H c122s01h);
	public List<C122S01H> findC122S01H(String mainId, String flowId);
	public C122S01H findC122S01HbyRefMinId(String refMainId, String flowId);
	public void changeOrgBrId(C122M01A c122m01a,String newBrNo) throws CapMessageException;
	public List<C122M01A> findPloanCSCNeedSendFARpaList(String createTime, String[] ploanPlan);
	public List<C122M01A> findPloanCSCNeedQueryWiseNewsList(String createTime, String[] ploanPlan);
	
	public C122S01C getC122S01C(String mainId);
	
	/**
	 * 判斷是否顯示一鍵分案按鈕
	 * @param brNo
	 * @return
	 */
	public boolean showOneButtonAssignCase(String brNo);
	
	/**
	 * 查詢C900M01N Page
	 * @param search
	 * @return
	 */
	public Page<C900M01N> findC900m01nPage(ISearch search);
	
	/**
	 * 刪除該分行已離職人員C900M01N 資料
	 * @param search
	 * @return
	 */
	public void deleteC900m01nIsLeave(String brNo);
	
	/**
	 * 查詢人員 Page
	 * @param search
	 * @return
	 */
	public Page<ElsUser> findUserPage(ISearch search);
	
	/**
	 * 查詢該分行C900M01N Map
	 * @param brNo
	 * @return
	 */
	public Map<String, C900M01N> findC900m01nByBrNo(String brNo);
	
	/**
	 * 新增C900M01M 可被派案的人員
	 * @param brNo
	 * @param userIds
	 * @return
	 */
	public String saveAssignCaseEmps(String brNo, JSONArray assignArray) throws CapMessageException;
	
	/**
	 * 確認是否要值行待分案
	 * @param brNo
	 * @return
	 */
	public Map<String, String> checkHaveNeedAssignCase(String brNo, String[] assignEmpNos) throws CapMessageException;
	
	/**
	 * 查詢該分行被派案人員的各案件數
	 * @param brNo
	 * @param assignEmpNos
	 * @return
	 */
	public List<Map<String, Object>> queryAssignCaseCount(String brNo, String[] assignEmpNos, String lastC122m01aOid) throws CapMessageException;

	/**
	 * 執行一鍵分案
	 * @param brNo
	 * @param userIds
	 * @return
	 */
	public Map<String, Object> oneButtonAssignCase(String brNo, JSONArray assignCaseArray) throws CapMessageException;
	
	/**
	 * 檢查引介來源，若為地政士引介，需檢查是否有填寫地政士
	 * @param mainId
	 * @return
	 */
	public String checkIntroduceSrcLaa(String mainId);
	
	/**
	 * 房貸+地政士引介案件取得前一案及後一案相同地政士的負責經辦
	 * @param brId
	 * @param mainId
	 * @return
	 */
	public List<String> getSameMegaEmpForLaaCase(String brId, String mainId);
	
	/**
	 * 房貸案更新經辦重複分派之地政士案件註記
	 * @param brId
	 * @param mainId
	 * @return
	 */
	public void updateIsSameMegaEmpLaaCaseFlag(String brId, String mainId);
	
	/**
	 * 檢核房貸案件於指派或改派時該執行人員是否有甲級授權(含)以上
	 * @param mainId
	 * @return
	 */
	public boolean checkAssignCaseAccessForHouseLoan(String mainId);
	
	byte[] getWordContent(C122M01A meta, C122S01C c122s01c);
	/**
	 * 依申貸項目與PLOAN案件ID(彙總案件編號)查詢從債務人
	 * @param applyKind
	 * @param ploanCaseId
	 * @return
	 */
	List<C122M01A> findMetaApplyKind_relateCase(String applyKind,
			String ploanCaseId);
	
	/**
	 * 取得未上傳文件數位化或未完成的DocFile
	 * @param mainId
	 * @return
	 * @throws CapException 
	 */
	List<DocFile> getAttchUndoneMEGAImageDocFiles(String mainId) throws CapException;
	
	/**
	 * 取得已完成上傳的數位文件化清單
	 * @param mainId
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> getAttchDoneMegaImageList(String mainId) throws CapException;

	/**
	 * 更新被分派分行清單
	 * @param gridRowDataList
	 * @return void
	 * @throws CapException
	 */
	public void updateAssigneeBrchList(List<Map<String, Object>> gridRowDataList) throws CapException;

	/**
	 * J-113-0237 配合消金處，於ELOAN端消金業務處及企金業務處新增青創追蹤報表
	 * @param sDate
	 * @param eDate
	 * @return
	 */
    List<Object[]> findSMEAList(Date sDate, Date eDate);
}