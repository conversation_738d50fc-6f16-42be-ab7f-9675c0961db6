package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisELFMOW1Service {

	/**
	 * 信用評等內部資料 (有財報迄日)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String,Object>> findelfmow1ByCustIdYMoney(String custId, String dupNo);

	/**
	 * 信用評等內部資料 (無財報迄日，以評等日)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String,Object>> findelfmow1ByCustIdNMoney(String custId, String dupNo);

	public Map<String, Object> findLrs1(String custId, String dupNo);
	public Map<String, Object> findLrs2(String custId, String dupNo);
}
