package com.mega.eloan.lms.base.pages;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.lowagie.text.Document;
import com.lowagie.text.Paragraph;
import com.lowagie.text.pdf.PdfWriter;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LrsUtil;

import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;

@Controller
@RequestMapping("/simple/FileProcessingPageByLoc")
public class FileProcessingPageByLoc extends AbstractFileDownloadPage {
	
	public FileProcessingPageByLoc() {
		super();
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		//((WebResponse)super.getResponse()).setHeader("Set-Cookie", "zfile=true; ");
	}
	
	@Override
	protected byte[] getContent(PageParameters params) throws Exception {
		byte[] r = null;
		
		String[] file_loc_arr = Util.trim(params.getString("file_loc")).split("\\|");
		int file_loc_size = (file_loc_arr==null?0:file_loc_arr.length);
		String file_loc = "";		
		if(true){						
			if(file_loc_size>1){
				String dir = PropUtil.getProperty("docFile.dir");//docFile.dir=/elnfs
				String systemId = PropUtil.getProperty("systemId");//systemId=LMS
				String file_separator = "/";
				String zfile_oid = IDGenerator.getRandomCode();			
				String zfile_loc = dir+(file_separator)+systemId+(file_separator)+zfile_oid+LrsUtil.TEMP_PDF_EXT;
				//把 N 個 pdf 給 merge成1個
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
				if(true){
					List<File> input_file_list = new ArrayList<File>();
					if(true){						
						for(int i=0;i<file_loc_size;i++){
							input_file_list.add( new File(file_loc_arr[i]));
						}	
					}
					
					List<InputStream> streamOfPDFFile = new ArrayList<InputStream>();
					if(true){				
						for(File f: input_file_list){
							streamOfPDFFile.add(new FileInputStream(f));					
						}	
					}
					PdfTools.mergeReWritePagePdf(streamOfPDFFile, outputStream);
					//寫入檔案				
					FileUtils.forceMkdir(new File(FilenameUtils.getFullPath(zfile_loc)));
					FileUtils.writeByteArrayToFile(new File(zfile_loc), outputStream.toByteArray());
					IOUtils.closeQuietly(outputStream);	
					
					if(true){
						for(InputStream stream: streamOfPDFFile){
							IOUtils.closeQuietly(stream);	
						}
						for(File file: input_file_list){
							if(file.getName().endsWith(LrsUtil.TEMP_PDF_EXT)){
								FileUtils.deleteQuietly(file);
							}
						}		
					}
				}
				//===
				file_loc = zfile_loc;
			}else if(file_loc_size==1){
				file_loc = file_loc_arr[0];
			}	
		}
		
		file_loc = Util.trim(file_loc);
		File file = null;
		if(Util.isNotEmpty(file_loc)){
			file = new File(file_loc);	
		}		
		
		if(file!=null && file.exists()){
			InputStream stream = new FileInputStream(file);
			r = IOUtils.toByteArray(stream);
			IOUtils.closeQuietly(stream);
			
			//刪 temp file
			if(file.getName().endsWith(LrsUtil.TEMP_PDF_EXT)){
				FileUtils.deleteQuietly(file);	
			}
		}else{
			//產生 in-memory 的 PDF
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			Document document = new Document();
			PdfWriter.getInstance(document, baos);
			document.open();
			document.add(new Paragraph("not exist"));
			document.close();
			
			r = baos.toByteArray();
			IOUtils.closeQuietly(baos);
		}		
		return r;
	}
	
	@Override
	public String getDownloadFileName() {
		return this.fileDownloadName;
	}

	@Override
	public String getFileDownloadServiceName() {
		return this.serviceName;
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
