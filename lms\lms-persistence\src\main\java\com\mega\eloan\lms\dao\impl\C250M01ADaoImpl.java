/* 
 * C250M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C250M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C250M01A;

/** 可疑代辦案件註記作業主檔 **/
@Repository
public class C250M01ADaoImpl extends LMSJpaDao<C250M01A, String>
	implements C250M01ADao {

	@Override
	public C250M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C250M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C250M01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C250M01A> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<C250M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C250M01A> findByIndex02(String custId, String dupNo){
		ISearch search = createSearchTemplete();
		List<C250M01A> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<C250M01A> findByCntrNo_custId_dupNo_yyyymm_status(String cntrNo, String custId, String dupNo, String yyyymm, String status){
		
		ISearch search = createSearchTemplete();
		List<C250M01A> list = null;
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (yyyymm != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "yyyymm", yyyymm);
		if (status != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "status", status);
		
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		
		return list;
	}
}