package com.mega.eloan.lms.lms.handler.form;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C123M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
/**
 * <pre>
 * 加拿大消金信用評等表
 * </pre>
 * 
 * @since 2017/10/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/10/1,EL09301,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1045m01formhandler")
@DomainClass(C123M01A.class)
public class LMS1045M01FormHandler extends AbstractFormHandler {

	@Resource
	BranchService branchService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	NumberService numberService;
	
	@Resource 
	FlowSimplifyService flowSimplifyService;
	
	public IResult echo_custId(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		result.set("cntrNo", Util.trim(params.getString("cntrNo")));
		return result;
	}
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}

	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String KEY = "saveOkFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C123M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			try{	
				meta =  clsService.findC123M01A_oid(mainOid);
				List<GenericBean> saveList = new ArrayList<GenericBean>();

				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());
				meta.setDeletedTime(null);
				
				meta.setBeaconScore(params.getString("selectBEACON"));
				meta.setDebtRatio(params.getString("selectServiceRatio"));
				meta.setLtvRatio(params.getString("selectLTV"));
				meta.setEmployYear(params.getString("selectYears"));
				meta.setIncome(params.getString("selectStability"));
				meta.setPropertyType(params.getString("selectTypes"));
				meta.setAssessment(params.getString("selectAssessment"));
				meta.setComments(params.getString("Comments"));
				
				saveList.add(meta);
				clsService.save(saveList);
		
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}		
		result.add(queryc123m01a(params));
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newc123m01a(PageParameters params)	
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String c123m01aMainid = "";
		c123m01aMainid = IDGenerator.getUUID();
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		C123M01A meta = new C123M01A();
		if(true){
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			meta.setOwnBrId(user.getUnitNo());
			
			meta.setMainId(c123m01aMainid);
			meta.setCustId(params.getString("custId"));
			meta.setDupNo(params.getString("dupNo"));
			meta.setCustName(params.getString("custName"));
			meta.setTypCd(TypCdEnum.海外.getCode());
			meta.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			meta.setDeletedTime(new Timestamp(DateUtils.addDays(CapDate.getCurrentTimestamp(), 1).getTime()));	//btnSave時要清
			meta.setCaseYear(Integer.parseInt(StringUtils.substring(TWNDate.toAD(nowTS), 0, 4)));
			meta.setCaseBrId(user.getUnitNo());
			meta.setCaseSeq(Integer.parseInt(numberService.getNumberWithMax(C123M01A.class, user.getUnitNo(), null, 99999)));
			
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branchService.getBranch(user.getUnitNo());
			caseNum.append(
					Util.toSemiCharString(meta.getCaseYear().toString()))
					.append(Util.trim(ibranch.getNameABBR()))
					.append("消金評等第")
					.append(Util.toSemiCharString(Util.addZeroWithValue(Util.trim(meta.getCaseSeq()), 5)))
					.append("號");
			meta.setCaseNo(caseNum.toString());

			meta.setRandomCode("");
			
			meta.setCreator(user.getUserId());
			meta.setCreateTime(CapDate.getCurrentTimestamp());
			if(true){
				meta.setUpdater(meta.getCreator());
				meta.setUpdateTime(meta.getCreateTime());	
			}

			clsService.save(meta);	
		}

		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		String branchName = meta==null?"":branchService.getBranchName(meta.getCaseBrId());
		result.set("branchName", meta.getCaseBrId() + " "+ branchName);
		result.set("custId", Util.trim(meta.getCustId()));
		result.set("dupNo", Util.trim(meta.getDupNo()));
		result.set("custName", Util.trim(meta.getCustName()));
		
		set_titleInfo(result, meta);
		
		return result; 
	}
		
	private void set_titleInfo(CapAjaxFormResult result, C123M01A meta){
		result.set("titleInfo", Util.trim(Util.trim(meta.getCaseNo())+" "+Util.trim(meta.getCustId())
				+"-"+Util.trim(meta.getDupNo())+" "+Util.trim(meta.getCustName())));
	}
	
	private void CalculationScore(CapAjaxFormResult result, C123M01A meta){
		int TotalScore = Integer.parseInt(Util.trim(meta.getBeaconScore()))+Integer.parseInt(Util.trim(meta.getDebtRatio()))+
							Integer.parseInt(Util.trim(meta.getLtvRatio()))+Integer.parseInt(Util.trim(meta.getEmployYear()))+
							Integer.parseInt(Util.trim(meta.getIncome()))+Integer.parseInt(Util.trim(meta.getPropertyType()))+
							Integer.parseInt(Util.trim(meta.getAssessment()));

		String RiskRating = "";
		if(TotalScore < 50){
			RiskRating = "10";
		} else if(TotalScore >= 50 && TotalScore <= 54){
			RiskRating = "9";
		} else if(TotalScore >= 55 && TotalScore <= 59){
			RiskRating = "8";
		} else if(TotalScore >= 60 && TotalScore <= 64){
			RiskRating = "7";
		} else if(TotalScore >= 65 && TotalScore <= 69){
			RiskRating = "6";
		} else if(TotalScore >= 70 && TotalScore <= 74){
			RiskRating = "5";
		} else if(TotalScore >= 75 && TotalScore <= 79){
			RiskRating = "4";
		} else if(TotalScore >= 80 && TotalScore <= 84){
			RiskRating = "3";
		} else if(TotalScore >= 85 && TotalScore <= 89){
			RiskRating = "2";
		} else if(TotalScore >= 90){
			RiskRating = "1";
		}
		
		String EvaluationResults = "";
		if(Util.equals(RiskRating, "10")){
			EvaluationResults = "Loss";
		} else if(Util.equals(RiskRating, "9")){
			EvaluationResults = "Doubtful";
		} else if(Util.equals(RiskRating, "8") || Util.equals(RiskRating, "7")){
			EvaluationResults = "Substandard";
		} else if(Util.equals(RiskRating, "6") || Util.equals(RiskRating, "5")){
			EvaluationResults = "Especially Mentioned";
		} else {
			EvaluationResults = "Satisfactory";
		}
		
		String FrequencyOfCreditReview = "";
		if(Util.equals(EvaluationResults, "Loss")){
			FrequencyOfCreditReview = "--";
		} else if(Util.equals(EvaluationResults, "Doubtful") || Util.equals(EvaluationResults, "Substandard")){
			FrequencyOfCreditReview = "Quarterly";
		} else if(Util.equals(EvaluationResults, "Especially Mentioned")){
			FrequencyOfCreditReview = "Semi-Annually";
		} else if(Util.equals(EvaluationResults, "Satisfactory")){
			FrequencyOfCreditReview = "Annually";
		}
		
		result.set("TotalScore", TotalScore);
		result.set("RiskRating", RiskRating);
		result.set("EvaluationResults", EvaluationResults);
		result.set("Frequency", FrequencyOfCreditReview);
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryc123m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString("mainOid");
		
		C123M01A meta = null;
		C123M01A c123m01aOld = null;

		if (Util.isNotEmpty(mainOid)) {
			List<C123M01A> c123m01aOld_list = clsService.findLastRecord(user.getUnitNo(), params.getString("custId"), params.getString("dupNo"), mainOid,CreditDocStatusEnum.海外_已核准.getCode());
			if(c123m01aOld_list != null && c123m01aOld_list.size()>0){
				c123m01aOld = c123m01aOld_list.get(0);
				if(c123m01aOld != null){
					result.set("showLastBEACON",c123m01aOld.getBeaconScore()==null ? "--" : Util.trim(c123m01aOld.getBeaconScore()));
					result.set("showLastServiceRatio",c123m01aOld.getDebtRatio()==null ? "--" : Util.trim(c123m01aOld.getDebtRatio()));
					result.set("showLastLTV",c123m01aOld.getLtvRatio()==null ? "--" : Util.trim(c123m01aOld.getLtvRatio()));
					result.set("showLastYears",c123m01aOld.getEmployYear()==null ? "--" : Util.trim(c123m01aOld.getEmployYear()));
					result.set("showLastStability",c123m01aOld.getIncome()==null ? "--" : Util.trim(c123m01aOld.getIncome()));
					result.set("showLastTypes",c123m01aOld.getPropertyType()==null ? "--" : Util.trim(c123m01aOld.getPropertyType()));
					result.set("showLastAssessment",c123m01aOld.getAssessment()==null ? "--" : Util.trim(c123m01aOld.getAssessment()));
					result.set("Comments",c123m01aOld.getComments());
				}
			}
			meta = clsService.findC123M01A_oid(mainOid);
			if(meta != null){
				result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
				result.set("createTime", Util.trim(meta.getCreateTime()));
				result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
				result.set("updateTime", Util.trim(meta.getUpdateTime()));
				result.set("ownBrIdDesc", meta.getOwnBrId() + branchService.getBranchName(meta.getOwnBrId()));
				result.set("docStatus", meta.getDocStatus());	
				
				result.set("selectBEACON",meta.getBeaconScore()!=null?Util.trim(meta.getBeaconScore()):"0");
				result.set("selectServiceRatio",meta.getDebtRatio()!=null?Util.trim(meta.getDebtRatio()):"0");
				result.set("selectLTV",meta.getLtvRatio()!=null?Util.trim(meta.getLtvRatio()):"0");
				result.set("selectYears",meta.getEmployYear()!=null?Util.trim(meta.getEmployYear()):"0");
				result.set("selectStability",meta.getIncome()!=null?Util.trim(meta.getIncome()):"0");
				result.set("selectTypes",meta.getPropertyType()!=null?Util.trim(meta.getPropertyType()):"0");
				result.set("selectAssessment",meta.getAssessment()!=null?Util.trim(meta.getAssessment()):"0");
				if(Util.isNotEmpty(Util.trim(meta.getComments()))){result.set("Comments",meta.getComments());}
				
				if(meta.getDeletedTime() == null){CalculationScore(result, meta);}
				
				result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
				result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
				result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
				result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
				
				set_titleInfo(result, meta);
			}
		}		
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String KEY = "saveOkFlag";
		
		String mainOid = params.getString("mainOid");
		String docStatus = params.getString("docStatus");
		
		result.set(KEY, false);
		
		C123M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC123M01A_oid(mainOid);
			
			if(meta!=null){				
				List<DocOpener> docOpeners = docCheckService.findByMainId(meta.getMainId());
				for(DocOpener docOpener : docOpeners){
					if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
						HashMap<String, String> hm = new HashMap<String, String>();
						hm.put("userId", docOpener.getOpener());
						hm.put("userName",
								userInfoService.getUserName(docOpener.getOpener()));
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0009", hm), getClass());
					}
				}
				
				//判斷編制中刪除，其它 更新DeletedTime
				if(Util.equals(FlowDocStatusEnum.海外_編製中.getCode(), docStatus)){
					flowSimplifyService.flowCancel(meta.getOid());
					clsService.delC123M01A(meta);
					// 刪除文件異動記錄
					docLogService.deleteLog(meta.getOid());
				}
				result.set(KEY, true);
			}			
		}	
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C123M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC123M01A_oid(mainOid);
			
			String errMsg = "";

			String docStatus = Util.trim(meta.getDocStatus());
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), docStatus)){
				nextStatus = CreditDocStatusEnum.海外_待覆核.getCode();
				_DocLogEnum = DocLogEnum.FORWARD;
			}else if(Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), docStatus)){
				//核定、退回
				if(Util.equals("核定", decisionExpr)){					
					if(Util.equals(user.getUserId(), meta.getUpdater())){
						errMsg = RespMsgHelper.getMessage("EFD0053");
					}else{
						nextStatus = CreditDocStatusEnum.海外_已核准.getCode();
						_DocLogEnum = DocLogEnum.ACCEPT;	
					}
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), docStatus)){	

			}
						
			if(Util.isNotEmpty(errMsg)){				
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
			if(true){
				if(Util.equals(nextStatus, CreditDocStatusEnum.海外_已核准.getCode())){
					meta.setApprover(user.getUserId());
					meta.setApproveTime(CapDate.getCurrentTimestamp());
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_待覆核.getCode())){
					meta.setApprover(user.getUserId());
					meta.setApproveTime(CapDate.getCurrentTimestamp());
				}
				meta.setDocStatus(nextStatus);
				clsService.daoSave(meta);

				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	private CapAjaxFormResult defaultResult(PageParameters params, C123M01A meta,
			CapAjaxFormResult result) throws CapException {		
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		return result;
	}
}