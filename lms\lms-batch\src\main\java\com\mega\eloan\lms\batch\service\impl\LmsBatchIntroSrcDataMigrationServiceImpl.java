package com.mega.eloan.lms.batch.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;

/**
 * <pre>
 * 
 * </pre>
 * 
 * @since 2022/12/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/8/13,007625,new
 *          </ul>
 */
@Service("lmsBatchIntroSrcDataMigrationServiceImpl")
public class LmsBatchIntroSrcDataMigrationServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(LmsBatchIntroSrcDataMigrationServiceImpl.class);

	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	EloandbBASEService eloandbBASEService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {

		JSONObject result = new JSONObject();
		
		String contractNo = null;
		String megaEmpNo = null;
		String agntNo = null;
		String agntChain = null;
		String megaCode = null;
		String subUnitNo = null;
		String subEmpNo = null;
		String subEmpNm = null;
		String introSrc = null;
		String megaEmpBrn = null;
		
		try {
			
			List<Map<String, Object>> list = this.misdbBASEService.getIntroductionSrcIsNotNullData();
			Map<String, String> intrSrcBrNoMap = this.eloandbBASEService.getC122m01aIntroductionBranchNoByApprovedSigningBook();
			
			Map<String, Map<String, Object>> prepareInsertMap = new HashMap<String, Map<String, Object>>();
			for(Map<String, Object> m : list){
				
				String cntrNo = String.valueOf(m.get("ELF500_CNTRNO"));
				String introductSrc = String.valueOf(m.get("ELF500_INTRO_SRC"));
				
				if("1".equals(introductSrc)){
					
					String marketingBrNo = m.get("LNF033_COM_BR") == null ? null : (String)m.get("LNF033_COM_BR") ;
					if(StringUtils.isBlank(marketingBrNo) && StringUtils.isNotBlank(intrSrcBrNoMap.get(cntrNo))){
						m.put("LNF033_COM_BR", intrSrcBrNoMap.get(cntrNo));
					}
				}
				
				prepareInsertMap.put(cntrNo, m);
			}
			
			for(String cntrNo : prepareInsertMap.keySet()){
				contractNo = cntrNo;
				Map<String, Object> m = prepareInsertMap.get(cntrNo);
				megaEmpNo = m.get("ELF500_MEGA_EMPNO") == null ? "" : (String)m.get("ELF500_MEGA_EMPNO");
				agntNo = m.get("ELF500_AGNT_NO") == null ? "" : (String)m.get("ELF500_AGNT_NO");
				agntChain = m.get("ELF500_AGNT_CHAIN") == null ? "" : (String)m.get("ELF500_AGNT_CHAIN");
				megaCode = m.get("ELF500_MEGA_CODE") == null ? "" : (String)m.get("ELF500_MEGA_CODE");
				subUnitNo = m.get("ELF500_SUB_UNITNO") == null ? "" : (String)m.get("ELF500_SUB_UNITNO");
				subEmpNo = m.get("ELF500_SUB_EMPNO") == null ? "" : (String)m.get("ELF500_SUB_EMPNO");
				subEmpNm = m.get("ELF500_SUB_EMPNM") == null ? "" : (String)m.get("ELF500_SUB_EMPNM");
				introSrc = m.get("ELF500_INTRO_SRC") == null ? "" : (String)m.get("ELF500_INTRO_SRC");
				megaEmpBrn = m.get("LNF033_COM_BR") == null ? "" : (String)m.get("LNF033_COM_BR");
				
				this.misdbBASEService.insertLNF13E(cntrNo, megaEmpNo, agntNo, agntChain, megaCode, subUnitNo, subEmpNo, subEmpNm, introSrc, megaEmpBrn);
				LOGGER.debug("引介來源新增 LNF13E資料成功 ===> contractNo: {}, megaEmpNo: {}, agntNo: {}, agntChain: {}, megaCode: {}, subUnitNo: {}, subEmpNo: {}, subEmpNm: {}, introSrc: {}, megaEmpBrn: {}", 
								new String[]{cntrNo, megaEmpNo, agntNo, agntChain, megaCode, subUnitNo, subEmpNo, subEmpNm, introSrc, megaEmpBrn});
			}
			
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "lmsBatchIntroductionSrcMigrationServiceImpl 執行成功！");

		} catch (Exception ex) {
			LOGGER.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE, "lmsBatchIntroductionSrcMigrationServiceImpl 執行失敗！==>" + "contractNo:" + contractNo + ",megaEmpNo:" + megaEmpNo + ",agntNo:" + agntNo + ",agntChain:" + agntChain + 
										",megaCode:" + megaCode + ",subUnitNo:" + subUnitNo + ",subEmpNo:" + subEmpNo + ",subEmpNm:" + subEmpNm + ",introSrc:" + introSrc + ",megaEmpBrn:" + megaEmpBrn + "-" + ex.getLocalizedMessage());

		}

		return result;
	}

}
