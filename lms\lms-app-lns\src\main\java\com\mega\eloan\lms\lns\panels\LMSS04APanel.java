/* 
 * LMS1205S04Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 案由(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS04APanel extends Panel {

	public LMSS04APanel(String id) {
		super(id);
	}
	
	public LMSS04APanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

}
