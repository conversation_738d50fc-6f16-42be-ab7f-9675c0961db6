
package com.mega.eloan.lms.cls.panels;

import org.kordamp.json.JSONObject;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.ContractDocUtil;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.C340M01A;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * J-113-0050 線上房貸增貸對保契約書-約據及央行管制切結書
 * </pre>
 * 
 */
public class CLS3401S082Panel extends Panel {

	private C340M01A meta;

	private JSONObject jsonObject;

	public CLS3401S082Panel(String id) {
		super(id);
	}

	public CLS3401S082Panel(String id, boolean updatePanelName, C340M01A meta,
			JSONObject jsonObject) {
		super(id, updatePanelName);
		this.meta = meta;
		this.jsonObject = jsonObject;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		new DocLogPanel("_docLog").processPanelData(model, params);

		boolean is_send_ploan = (meta!=null && Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), meta.getDocStatus() ));
		model.addAttribute("showPloanColumn", is_send_ploan);
		
		boolean showOtherInfoTitle = false;
		if(jsonObject!=null){
			showOtherInfoTitle = Util.isNotEmpty(Util.trim(jsonObject.optString("otherInfoTitle"))); 
		}
		model.addAttribute("showOtherInfoTitle", showOtherInfoTitle);

		// UPGRADE: 前端須配合改Thymeleaf的樣式
//		Label _NumberLabel = new Label("S02_Chinese_Number", ContractDocUtil.list_Chinese_Number());
//		_NumberLabel.setEscapeModelStrings(false);
//		add(_NumberLabel);
		model.addAttribute("S02_Chinese_Number", ContractDocUtil.list_Chinese_Number());
	}

	/**/
	private static final long serialVersionUID = 1L;
}
