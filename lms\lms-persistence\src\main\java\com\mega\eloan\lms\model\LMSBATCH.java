/* 
 * LMSBATCH.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 授信批次報表檔 **/
@Entity
@Table(name = "LMSBATCH", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class LMSBATCH extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 分行代碼 **/
	@Column(name = "BRANCH", length = 3, columnDefinition = "CHAR(3)")
	private String branch;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "DATE")
	private Date dataDate;

	/** 起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "BGNDATE", columnDefinition = "DATE")
	private Date bgnDate;

	/** 迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "DATE")
	private Date endDate;

	/** 報表代號 **/
	@Column(name = "RPTNO", length = 10, columnDefinition = "VARCHAR(10)")
	private String rptNo;

	/** 報表名稱 **/
	@Column(name = "RPTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String rptName;

	/**
	 * 報表亂碼
	 * <p/>
	 * 產檔時一併產生
	 */
	@Column(name = "RANDOMCODE", length = 32, columnDefinition = "VARCHAR(32)")
	private String randomCode;

	/**
	 * 是否為最新報表
	 * <p/>
	 * Y/N
	 */
	@Column(name = "NOWRPT", length = 1, columnDefinition = "CHAR(01)")
	private String nowRpt;

	/** 批次時間 **/
	@Column(name = "BTHDATE", columnDefinition = "TIMESTAMP")
	private Date bthDate;

	/**
	 * 備註或其他需使用的值
	 * <p/>
	 * name=value;name=value
	 */
	@Column(name = "REMARKS", length = 600, columnDefinition = "varchar(600)")
	private String remarks;

	/**
	 * 資料修改人(行編)
	 * <p/>
	 * 員編
	 */
	@Column(name = "UPDATER", length = 6, columnDefinition = "VARCHAR(6)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}

	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得分行代碼 **/
	public String getBranch() {
		return this.branch;
	}

	/** 設定分行代碼 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 取得資料日期 **/
	public Date getDataDate() {
		return this.dataDate;
	}

	/** 設定資料日期 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得起日 **/
	public Date getBgnDate() {
		return this.bgnDate;
	}

	/** 設定起日 **/
	public void setBgnDate(Date value) {
		this.bgnDate = value;
	}

	/** 取得迄日 **/
	public Date getEndDate() {
		return this.endDate;
	}

	/** 設定迄日 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 取得報表代號 **/
	public String getRptNo() {
		return this.rptNo;
	}

	/** 設定報表代號 **/
	public void setRptNo(String value) {
		this.rptNo = value;
	}

	/** 取得報表名稱 **/
	public String getRptName() {
		return this.rptName;
	}

	/** 設定報表名稱 **/
	public void setRptName(String value) {
		this.rptName = value;
	}

	/**
	 * 取得是否為最新報表
	 * <p/>
	 * Y/N
	 */
	public String getNowRpt() {
		return this.nowRpt;
	}

	/**
	 * 設定是否為最新報表
	 * <p/>
	 * Y/N
	 **/
	public void setNowRpt(String value) {
		this.nowRpt = value;
	}

	/** 取得批次時間 **/
	public Date getBthDate() {
		return this.bthDate;
	}

	/** 設定批次時間 **/
	public void setBthDate(Date value) {
		this.bthDate = value;
	}

	/**
	 * 取得備註或其他需使用的值
	 * <p/>
	 * name=value;name=value
	 */
	public String getRemarks() {
		return this.remarks;
	}

	/**
	 * 設定備註或其他需使用的值
	 * <p/>
	 * name=value;name=value
	 **/
	public void setRemarks(String value) {
		this.remarks = value;
	}

	/**
	 * 取得資料修改人(行編)
	 * <p/>
	 * 員編
	 */
	public String getUpdater() {
		return this.updater;
	}

	/**
	 * 設定資料修改人(行編)
	 * <p/>
	 * 員編
	 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定資料修改日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * @param randomCode the randomCode to set
	 */
	public void setRandomCode(String randomCode) {
		this.randomCode = randomCode;
	}

	/**
	 * @return the randomCode
	 */
	public String getRandomCode() {
		return randomCode;
	}
}
