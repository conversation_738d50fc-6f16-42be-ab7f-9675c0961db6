/* 
 * C004M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 政策性留學生貸款報送資料 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C004M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C004M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** unid **/
	@Size(max=32)
	@Column(name="UNID", length=32, columnDefinition="CHAR(32)")
	private String unid;

	/** 
	 * 報表類別<p/>
	 * S1、S2、S3、S4<br/>
	 *  Q1、Q2、Q3
	 */
	@Size(max=2)
	@Column(name="RPTTYPE", length=2, columnDefinition="VARCHAR(02)")
	private String rptType;

	/** 資料起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="BGNDATE", columnDefinition="DATE")
	private Date bgnDate;

	/** 資料迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 確定報送日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="RPTDATE", columnDefinition="DATE")
	private Date rptDate;

	/** 報表名稱 **/
	@Size(max=128)
	@Column(name="RPTNAME", length=128, columnDefinition="VARCHAR(128)")
	private String rptName;

	/** 附加檔案路徑 **/
	@Size(max=128)
	@Column(name="FILEPATH", length=128, columnDefinition="VARCHAR(128)")
	private String filePath;

	/** 附加檔案名稱 **/
	@Size(max=36)
	@Column(name="FILENAME", length=36, columnDefinition="VARCHAR(36)")
	private String fileName;

	/** 備註 **/
	@Size(max=256)
	@Column(name="RMK", length=256, columnDefinition="VARCHAR(256)")
	private String rmk;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得unid **/
	public String getUnid() {
		return this.unid;
	}
	/** 設定unid **/
	public void setUnid(String value) {
		this.unid = value;
	}

	/** 
	 * 取得報表類別<p/>
	 * S1、S2、S3、S4<br/>
	 *  Q1、Q2、Q3
	 */
	public String getRptType() {
		return this.rptType;
	}
	/**
	 *  設定報表類別<p/>
	 *  S1、S2、S3、S4<br/>
	 *  Q1、Q2、Q3
	 **/
	public void setRptType(String value) {
		this.rptType = value;
	}

	/** 取得資料起日 **/
	public Date getBgnDate() {
		return this.bgnDate;
	}
	/** 設定資料起日 **/
	public void setBgnDate(Date value) {
		this.bgnDate = value;
	}

	/** 取得資料迄日 **/
	public Date getEndDate() {
		return this.endDate;
	}
	/** 設定資料迄日 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 取得確定報送日期 **/
	public Date getRptDate() {
		return this.rptDate;
	}
	/** 設定確定報送日期 **/
	public void setRptDate(Date value) {
		this.rptDate = value;
	}

	/** 取得報表名稱 **/
	public String getRptName() {
		return this.rptName;
	}
	/** 設定報表名稱 **/
	public void setRptName(String value) {
		this.rptName = value;
	}

	/** 取得附加檔案路徑 **/
	public String getFilePath() {
		return this.filePath;
	}
	/** 設定附加檔案路徑 **/
	public void setFilePath(String value) {
		this.filePath = value;
	}

	/** 取得附加檔案名稱 **/
	public String getFileName() {
		return this.fileName;
	}
	/** 設定附加檔案名稱 **/
	public void setFileName(String value) {
		this.fileName = value;
	}

	/** 取得備註 **/
	public String getRmk() {
		return this.rmk;
	}
	/** 設定備註 **/
	public void setRmk(String value) {
		this.rmk = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
