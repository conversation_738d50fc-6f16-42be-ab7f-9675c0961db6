var initDfd = initDfd || new $.Deferred();
initDfd.done(function(json){
	
	//J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	if( json.lock ){		
		$("#addFiles").addClass(" ui-state-disabled ").prop("disabled", true);
		$("#deleteFiles").addClass(" ui-state-disabled ").prop("disabled", true);
	}else{
		if(json['initControl_lockDoc']){
			//$("#addFiles").addClass(" ui-state-disabled ").attr("disabled", "true");
			$("#deleteFiles").addClass(" ui-state-disabled ").prop("disabled", true);
		}
	}
		 
	
	
	
	//檔案上傳grid
    var gridfile = $("#gridfile").iGrid({
        handler: 'lms2411gridhandler',
        height: 80,
        postData: {
            formAction: "queryfile",
            fieldId: "crs",
            mainId: json.mainId
        },
        rowNum: 15,
        multiselect: true,
        colModel: [{
            colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
            name: 'srcFileName',
            width: 120,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: download
        }, {
            colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
            name: 'fileDesc',
            width: 140,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
            name: 'uploadTime',
            width: 140,
            align: "center",
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }]
    });	
    
    //檔案下載
    function download(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }	
    
  //===================================================
	$("#btn_conFlag_defaultVal").click(function(){
		
		$("#tabForm").injectData({'conFlag':'1', 'conFlag2A':'N', 'conFlag2B':'N', 'conFlag2C':'N'});
	});	
	
});
