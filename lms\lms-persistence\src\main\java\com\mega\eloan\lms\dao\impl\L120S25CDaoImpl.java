/* 
 * L120S25CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S25CDao;
import com.mega.eloan.lms.model.L120S25C;

/** BIS評估表參數檔 **/
@Repository
public class L120S25CDaoImpl extends LMSJpaDao<L120S25C, String> implements
		L120S25CDao {

	@Override
	public L120S25C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S25C> findByParamTypeDate(String paramType, String paramDate) {
		ISearch search = createSearchTemplete();
		List<L120S25C> list = null;
		if (paramType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "paramType",
					paramType);
		if (paramDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "paramDate",
					paramDate);

		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("paramDate", true);
		search.setOrderBy(printSeqMap);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S25C> findByParamTypeKeyDate(String paramType,
			String paramKey, String paramDate) {
		ISearch search = createSearchTemplete();
		List<L120S25C> list = null;
		if (paramType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "paramType",
					paramType);
		if (paramKey != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "paramKey",
					paramKey);
		if (paramDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "paramDate",
					paramDate);

		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("paramDate", true);
		search.setOrderBy(printSeqMap);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
}