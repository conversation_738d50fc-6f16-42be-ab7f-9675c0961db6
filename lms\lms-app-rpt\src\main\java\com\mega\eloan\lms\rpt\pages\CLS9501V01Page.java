package com.mega.eloan.lms.rpt.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**
 * <pre>
 * 收集批覆書額度資訊
 * </pre>
 * 
 * @since 2013/01/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/23,Vector,new
 *          </ul>
 */

@Controller
@RequestMapping(path = "/rpt/cls9501v01")
public class CLS9501V01Page extends AbstractEloanInnerView {

	public CLS9501V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		addToButtonPanel(model, LmsButtonEnum.Change,LmsButtonEnum.CreateReport);

		renderJsI18N(CLS9501V01Page.class);
		
		model.addAttribute("loadScript", "loadScript('pagejs/rpt/CLS9501V01Page');");
	}
}
