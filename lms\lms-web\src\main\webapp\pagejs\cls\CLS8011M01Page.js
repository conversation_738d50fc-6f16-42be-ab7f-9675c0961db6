//var initDfd = $.Deferred();
var initDfd = initDfd ||$.Deferred();
var _handler = "cls8011m01formhandler";

$(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	var initControl_lockDoc = false;
	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		formPostData:{'pageItemType': $("#tabForm").find("#pageItemType").val() },
		loadSuccess:function(json){			
			
			// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button")) {
				initControl_lockDoc = true;				
			}
			json['initControl_lockDoc'] = initControl_lockDoc;
			
			if(json['initControl_lockDoc']){
				tabForm.lockDoc();	
			}
			//---
			//init page
			if(json.page=='01'){
							$("#storageLocation").append("<option value=''>"+i18n.def.comboSpace+"</option>");
							$("#storageLocation").append("<option value='00'>"+i18n.cls8011m01['C801M01A.storageLocation.00']+"</option");
							$("#storageLocation").append("<option value='01'>"+i18n.cls8011m01['C801M01A.storageLocation.01']+"</option");
							
							$("#caseBossId").setItems({ item: json.boss_list, space: true, format:'{value} : {key}' });
							$("#caseApprId").setItems({ item: json.staff_list, space: true, format:'{value} : {key}' });
							$("#caseRecheckId").setItems({ item: json.staff_list, space: true, format:'{value} : {key}' });
							$("#custodianId").setItems({ item: json.staff_list, space: true, format:'{value} : {key}' });
							
							if(json.added_caseBossId){
								addDisableItem("caseBossId", json.added_caseBossId);
							}		
							if(json.added_caseApprId){
								addDisableItem("caseApprId", json.added_caseApprId);
							}								
							if(json.added_caseRecheckId){
								addDisableItem("caseRecheckId", json.added_caseRecheckId);
							}
							if(json.added_CustodianId){
								addDisableItem("custodianId", json.added_CustodianId);
							}
						}else if(json.page=='02' || json.page=='03' || json.page=='04'){
							
						}
						
						//---		
						if(json.docType == "1"){
							$(".showChairMan").show();
						}else{
							$(".showChairMan").hide();
						}
						tabForm.injectData(json);
						initDfd.resolve(json);	
				}});
	
	btnPanel.find("#btnSave").click(function(){		
		saveAction().done(function(json){
			if(json.saveOkFlag){
				API.showMessage(i18n.def.saveSuccess);
			}
        });
	}).end().find("#btnPrint").click(function(){
		if(initControl_lockDoc) {
			printC801M01A( $("#mainOid").val() +"^"+ $("#mainId").val() );
		}else{
			saveAction().done(function(json){
				if(json.saveOkFlag){					
					printC801M01A( json.mainOid+"^"+json.mainId );
				}
	        });
		}
	}).end().find("#btnImpLatestItem").click(function(){
			API.confirmMessage("是否清除目前項目，並引入最新項目", function(result){
	            if (result) {
	            	var oids = [];
	           	 	oids.push( $("#mainOid").val() ); 
	            	$.ajax({
	    				type: "POST",
	    		        handler: _handler,
	    		           data: { formAction: "impLatestItem", 'oids': oids },
	    		           }).done(function(json_impLatestItem){
	    		        	   API.showMessage(i18n.def.runSuccess);
	    		        	   
	    		        	   var page = responseJSON.page;
	    		        	   if(page=='02' || page=='03' || page=='04'){
		    		        	   	var tData = {'mainDocStatus': $("#mainDocStatus").val()
		    		        	   		, 'mainId': $("#mainId").val()
		    		        	   		, 'mainOid': $("#mainOid").val()
		    		        	   	};
		    		        	   	$.form.submit({ url: page , data: tData });
	    		        	   }
	    		           
	    			});    	
	        	}
	    	});
		
	}).end().find("#btnToEdit").click(function(){
		var oids = [];
   	 	oids.push( $("#mainOid").val() ); 
       	$.ajax({
             handler: _handler,
             data: {
                 formAction: "to_edit",
                 'oids': oids
             }
        }).done(function(){
       	 API.triggerOpener();//gridview.reloadGrid 
         window.close();    
        });
	}).end().find("#btnEditOK").click(function(){
		saveAction().done(function(json){
			if(json.saveOkFlag){
				var oids = [];
		   	 	oids.push( $("#mainOid").val() ); 
		       	$.ajax({
		             handler: _handler,
		             data: {
		                 formAction: "to_finish",
		                 'oids': oids
		             }
		        }).done(function(){
		        	 API.triggerOpener();//gridview.reloadGrid 
		             window.close();    
		        });
			}
        });		       	
	});	
	
	var addDisableItem = function(id_select, jsonObj){
			$("#"+id_select).setItems({ item: jsonObj, clear: false, space: false, format:'{value} : {key}' });
			
			$.each(jsonObj, function(k,v){
				$.each($("#"+id_select+" option[value='"+k+"']"), function(idx,obj){
					$(obj).prop( "disabled",true );			       
				});     
			});
		}
		
	});

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},
	sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                ( opts||{} )
            )                
        }).done(function(json){
        	tabForm.injectData(json);
        	//更新 opener 的 Grid
            CommonAPI.triggerOpener("gridview", "reloadGrid");
        });
	}else{
		return $.Deferred();
	}
}

/**
 * 同時印多份: 
 * oid_arr.push(data.oid+"^"+data.mainId);
 * rptOid: oid_arr.join("|")
 */
function printC801M01A(rptOid){       
   $.form.submit({
       url: "../../simple/FileProcessingService",
       target: "_blank",
       data: {
           'rptOid': rptOid,
           'fileDownloadName': "cls8011r01.pdf",                       
           serviceName: "cls8011r01rptservice"            
       }
   });
}
