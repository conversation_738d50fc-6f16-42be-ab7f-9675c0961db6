/* 
 * C160S01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C160S01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160S01B;

/** 主從債務人資料表檔 **/
@Repository
public class C160S01BDaoImpl extends LMSJpaDao<C160S01B, String>
	implements C160S01BDao {

	@Override
	public C160S01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C160S01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C160S01B> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public List<C160S01B> findByMainIdRefMainId(String mainId,String refmainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId",refmainId);
		List<C160S01B> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public C160S01B findByUniqueKey(String mainId, String refmainId, String custId, String dupNo
			, String cntrNo, String rId, String rDupNo, String rType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refmainId", refmainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "rId", rId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rDupNo", rDupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C160S01B> findByMainIdCntrno(String mainId, String Cntrno) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", Cntrno);
		List<C160S01B> list = createQuery(search).getResultList();
		return list;
	}
}