/* 
 * LMS0015V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 待辦案件
 * </pre>
 * 
 * @since 2012/1/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/12,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms0011v00")
public class LMS0011V00Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button

		// add(new CreditButtonPanel("_buttonPanel", null,
		// CreditButtonEnum.Filter, CreditButtonEnum.CaseToChange));
		addToButtonPanel(model, LmsButtonEnum.Filter);
		renderJsI18N(LMS0011V00Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS0011V00Page');");
	}

}
