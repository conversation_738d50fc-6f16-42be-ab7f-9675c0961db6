$(document).ready(function(){
    var grid = $("#gridview").iGrid({
        handler: 'lms1825gridhandler',
        height: 350,
        sortname: 'genDate',
        postData: {
            docStatus: viewstatus,
            formAction: "query"
        },
        colModel: [{
            colHeader: i18n.lms1825v01['L181M01a.genDate'],
            name: 'genDate',
            align: "center",
            formatter: 'click',
            width: 75,
            sortable: true,
            onclick: openDoc
        }, {
            colHeader: i18n.lms1825v01['L181M01a.baseDate'],
            name: 'baseDate',
            width: 90,
            align: "center",
            sortable: true,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            }
        }, {
            colHeader: i18n.lms1825v01['L181M01a.updater'],
            name: 'updater',
            width: 100,
            sortable: true,
            align: "center"
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '../lrs/lms1825m01',
            data: {
                formAction: "query",
                //					transactionCode : transactionCode,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus
            },
            target: rowObject.oid
        });
    };
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selrow');
        var list = $("#gridview").getRowData(rows).oid;
        if ((list ? false : true)) {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1825formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteMark",
                        oid: list
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        })
    }).end().find("#btnAdd").click(function(){
        $.form.submit({
            url: '../lrs/lms1825m01',
            data: {
                mainDocStatus: viewstatus
            },
            target: "_blank"
        });
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
    });
});
