initDfd.done(function(json){
    /**
     * 共同行銷
     */
    $("#queryJoinMarketing").click(function(){
        $.ajax({
            handler: inits.fhandle,
            action: "queryJoinMarketing",
            data: {
                mainId: $("#mainId").val()
            },
            success: function(obj){
                $("#joinMarketing").val(obj.joinMarketing);
                $("#joinMarketingDate").val(obj.joinMarketingDate);
            }
        });
    });
    //全部已收 //全部免付
    $("#allCast,#allCancel").click(function(){
        $(this).attr("id") === "allCast" ? $(".s1").val("1") : $(".s1").val("0");
    });
    
    /**
     * J-111-0207 重新引進[信保案件負責人客戶基本資料檔是否已建有配偶資料]
     */
    $("#reloadSmeMateInfo").click(function(){
    	$.ajax({
            handler: inits.fhandle,
            action: "reloadSmeMateInfo",
            data: {
                mainId: $("#mainId").val()
            },
            success: function(obj){
            	$("#NoSmeMateInfoText").injectData({'NoSmeMateInfoText':''},false);
            	if(obj.smeMateInfo){
            		$("#smeMateInfo").val(obj.smeMateInfo);
            		if(obj.smeMateInfo == "2"){
            			$("#smeMateInfoText,#smeMateInfo").css("color", "red");
            			$("#smeMateInfoText,#smeMateInfo").css("font-weight", "bold");
            			$("#NoSmeMateInfoText").injectData({'NoSmeMateInfoText':obj.noMateBorrower},false);
            		}else{
            			$("#smeMateInfoText,#smeMateInfo").css("color", "");
            			$("#smeMateInfoText,#smeMateInfo").css("font-weight", "");
            		}
            		
            	}     
            }
        });
    });
    
    /**
     * 重新引進
     */
    $("#reloadAll").click(function(){
        $.ajax({
            handler: inits.fhandle,
            action: "reloadL160M01C",
            data: {
                mainId: $("#mainId").val()
            },
            success: function(obj){
                $(".creatTr").html("");
                creatL160M01C(obj);
            }
        });
    });
    
	$("#allDetails").click(function(){
		$("#C801M01AGrid").trigger("reloadGrid");
	    $('#openC801M01A').thickbox({
	        title: i18n.lms1601m01['C801M01A.title'],// C801M01A.title=個金個人資料清冊
	        width: 800,
	        height: 300,
	        align: 'center',
	        valign: 'bottom',
	        i18n: i18n.def,
	        buttons: {
	            'close': function(){
	                $.thickbox.close();
	            }
	        }
	    });
	});

//	$("#importRPA").click(function(){
//		//引進RPA查詢結果，先不用引進
//		$.ajax({
//            handler: "lms1601m01formhandler",
//            data: {
//                formAction: "checkRpaData"
//            },
//            success: function(result){
//				if (result.confirmMsg != "") {
//					return CommonAPI.showErrorMessage('仍有查詢中項目，請稍後。');					
//				} else {
//					$.ajax({
//						type : "POST",
//						handler : "lms1601m01formhandler",
//						data : {
//							formAction : "importRPA"
//						},
//						success:function(responseData){
//							//TODO reload
//							//$("#gridviewRpaInfo").trigger("reloadGrid");
//						}
//					});
//				}
//            }
//        });
//	});
	
    var grid = $("#C801M01AGrid").iGrid({
	    height: 90,
	    handler: "cls8011gridhandler",
	    action: "queryC801M01AFromLMS",
	    rownumbers: true,
	    colModel: [{		
	        width: 40, // 設定寬度
	        colHeader: i18n.lms1601m01['C801M01A.caseNo'],//C801M01A.caseNo=檔案編號
	        name: "caseNo",
	        align: "center"
	    }, {
	        width: 30, // 設定寬度
	        colHeader: i18n.lms1601m01['C801M01A.custId'],//C801M01A.custId=統一編號
	        name: "custId",
	        align: "center"
	    }, {
	        width: 20, // 設定寬度
	        colHeader: i18n.lms1601m01['C801M01A.custName'],//C801M01A.custName=客戶名稱
	        name: "custName",
	        align: "center"
	    }, {
	        width: 30, // 設定寬度
	        colHeader: i18n.lms1601m01['C801M01A.cntrNo'],//C801M01A.cntrNo=額度序號
	        name: "cntrNo",
	        align: "center"
	    }, {
	        width: 40, // 設定寬度
	        colHeader: i18n.lms1601m01['C801M01A.loanNo'],//C801M01A.loanNo=放款帳號
	        name: "loanNo",
	        align: "center"
	    }, {
	        name: "oid",
	        hidden: "true"
		}, {
	        name: "mainId",
	        hidden: "true"
	    }, {
	        name: "docStatus",
	        hidden: "true"
	    }],
	    ondblClickRow: function(rowid){
	        var data = grid.getRowData(rowid);
	        openDoc(null, null, data);
	    }
	});

    //重新弔進
	$("#pullinC801M01ABt").click(function(){
		$.ajax({
	        handler: 'lms1601m01formhandler',
	        action: 'getC801M01AByL140M01A',
	        success: function(response){
				$("#C801M01AGrid").trigger("reloadGrid");
	            MegaApi.showPopMessage(i18n.def['confirmTitle'], i18n.def['runSuccess']);            
	        }
	    });
	});
	
	$("#delC801M01ABt").click(function(){
		var data = grid.getSingleData();
	    if (data) {    	
	        MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
	            if (action) {
	            	var oids = [];
	            	oids.push(data.oid);
	            	
	                $.ajax({
	                    handler: 'cls8011m01formhandler',
	                    action: 'deleteC801m01a',
	                    data: {
	                    	'oids':oids
	                    },
	                    success: function(response){
	                    	$("#C801M01AGrid").trigger("reloadGrid");
	                        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
	                    }
	                });
	            }
	        });
	    }
	});   

	/**
	 * 開啟文件
	 */
	var openDoc = function(cellvalue, options, rowObject){
	
		$.form.submit({
				url:'../../cls/cls8011m01/01',
				data:{
					mainOid: rowObject.oid,
					mainId: rowObject.mainId,
	            mainDocStatus: rowObject.docStatus
				},
				target:rowObject.oid
		});
	
	}
	
    $('#useSelect,#lnSelect,#tType').change(function(){
        switch ($(this).attr("id")) {
            case "useSelect": //動用期限切換 
            	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
                $("#the" + DOMPurify.sanitize($(this).val())).show().siblings(".the").hide().find(":input").val("");
                break;
            case "lnSelect": //授信期限切換
            	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
                $("#two" + DOMPurify.sanitize($(this).val())).show().siblings(".two").hide().find(":input").val("");
                break;
            case "tType"://授信契約書
            	//J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
            	//$(this).val() == "1" ? $("#long").hide() : $("#long").show();
            	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
                if(DOMPurify.sanitize($(this).val()) == "1"){
                	$("#long").hide() ;
                	$("#show_not_tType3").show();
                }else if(DOMPurify.sanitize($(this).val()) == "2"){
                	$("#long").show();
                	$("#show_not_tType3").show();
                }else{
                	$("#long").hide();       
                	$("#show_not_tType3").find(":input").val("");
                	$("#show_not_tType3").hide();
                }
                break;
        }
    });
    
    //L160M01A.message46=貸放手續齊全，擬准予動用
    $("#getWord").click(function(){
        $("#comm").val(i18n.lms1601m01['L160M01A.message46']);
    })
    
    /**
     * 查詢主債務人+從債務人名單
     */
    function queryCustList(){
        var result = {};
        $.ajax({
            async: false,
            handler: inits.fhandle,
            action: 'queryCust',
            formId: 'empty',
            data: {
                mainId: $("#mainId").val()
            },
            success: function(response){
                $.extend(result, response.cust);
            }
        });
        return result;
    }
    //黑名單查詢
    $("#selecttheblack").click(function(){
        BlackNameAction.query({
            cust: queryCustList(),
            callback: function(response){
                $('#blackDataDate').val(util.getToday());
				var regex = /<br\s*[\/]?>/gi;
                $('#blackListTxtOK').val(BlackNameAction.parseMessage().replace(regex, "\n"));
				
            }
        });
        //queryBlackBox();
        //        $.ajax({
        //            handler: inits.fhandle,
        //            data: {
        //                formAction: "queryBlackInit",
        //                mainId: $("#mainId").val()
        //            },
        //            success: function(obj){
        //                $.thickbox.close();
        //                if (obj.showBox) {
        //                    queryBlackBox();
        //                } else {
        //                    $("#blackDataDate").val(obj.blackDataDate);
        //                    $("#blackListTxtOK").val(obj.blackListTxtOK);
        //                }
        //                
        //                
        //            }
        //        });
    });
    
    //黑名單查詢視窗
    function queryBlackBox(){
        $("#blackbox").thickbox({
            // L160M01A.blackListTxtErr = 黑名單查詢
            title: i18n.lms1601m01['L160M01A.blackListTxtErr'],
            width: 400,
            height: 120,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    //只能打英文的 reg
                    var engNameReg = /\w/;
                    if (!$("#blackName").val().match(engNameReg)) {
                    
                        //L160M01A.message40=英文名稱輸入錯誤，必須為半形英文字
                        return CommonAPI.showErrorMessage(i18n.lms1601m01["L160M01A.message40"]);
                    }
                    
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "queryBlackPage",
                            name: $("#blackName").val()
                        },
                        success: function(obj){
                            $.thickbox.close();
                            $("#blackDataDate").val(obj.blackDataDate);
                            $("#blackListTxtOK").val(obj.blackListTxtOK);
                        }
                    });
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    
    //上傳檔案按鈕
    $("#uploadFile").click(function(){
        var limitFileSize = 3145728;
        MegaApi.uploadDialog({
            //動用審核表
            fieldId: "useDoc",
            fieldIdHtml: "size='30'",
            fileDescId: "fileDesc",
            fileDescHtml: "size='30' maxlength='30'",
            subTitle: i18n.def('insertfileSize', {
                'fileSize': (limitFileSize / 1048576).toFixed(2)
            }),
            limitSize: limitFileSize,
            width: 320,
            height: 190,
            data: {
                mainId: $("#mainId").val(),
                sysId: "LMS"
            },
            success: function(){
                gridfile.trigger("reloadGrid");
            }
        });
    });
    
    //刪除檔案按鈕
    $("#deleteFile").click(function(){
        var select = gridfile.getGridParam('selarrrow');
        if (select == "") {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var data = [];
                for (var i in select) {
                    data.push(gridfile.getRowData(select[i]).oid);
                }
                
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteUploadFile",
                        oids: data
                    },
                    success: function(obj){
                        gridfile.trigger("reloadGrid");
                    }
                });
            } else {
                return;
            }
        });
    });
    
    
    //檔案上傳grid
    var gridfile = $("#gridfile").iGrid({
        handler: inits.ghaddle,
        height: 150,
        postData: {
            formAction: "queryfile",
            //動用審核表
            fieldId: "useDoc",
            mainId: responseJSON.mainId
        },
        rowNum: 15,
        multiselect: true,
        colModel: [{
            colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
            name: 'srcFileName',
            width: 120,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: download
        }, {
            colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
            name: 'fileDesc',
            width: 140,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
            name: 'uploadTime',
            width: 140,
            align: "center",
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }]
    });
    
    
    //檔案下載
    function download(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }
	
	
});
