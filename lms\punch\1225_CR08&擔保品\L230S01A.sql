---------------------------------------------------------
-- LMS.L230S01A 簽約未動用額度資訊檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
DROP TABLE LMS.L230S01A;
CREATE TABLE LMS.L230S01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	CUSTID        VARCHAR(10)  ,
	<PERSON><PERSON><PERSON><PERSON>         CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      VARCHAR(120) ,
	OWNBRID       CHAR(3)      ,
	<PERSON>OC<PERSON>AT<PERSON>     VARCHAR(3)   ,
	CREATO<PERSON>       CHAR(6)      ,
	CREATE<PERSON><PERSON>    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,
	CASEBRID      CHAR(3)      ,
	CASENO        VARCHAR(62)  ,
	CASEDATE      DATE         ,
	SRCMAINID     VARCHAR(32)  ,
	CNTRNO        CHAR(12)     ,
	LNSUBJECT     VARCHAR(300) ,
	PROPERTY      VARCHAR(30)  ,
	CURRENTAPPLYCURR CHAR(3)      ,
	CURRENTAPPLYAMT DECIMAL(15,0),
	NUSEMEMO      CHAR(1)      ,
	SIGNDATE      DATE         ,
	REASON        VARCHAR(40)  ,
	REASONDRC     VARCHAR(300) ,
	DATADATE      DATE         ,

 constraint P_L230S01A  PRIMARY KEY  (OID)
) in EL_DATA_8KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
DROP INDEX LMS.XL230S01A01;
CREATE INDEX LMS.XL230S01A01 ON LMS.L230S01A   (MAINID, SRCMAINID);
CREATE INDEX LMS.XL230S01A02 ON LMS.L230S01A   (SRCMAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L230S01A IS '簽約未動用額度資訊檔';
COMMENT ON LMS.L230S01A (
	OID           IS 'oid', 
	MAINID        IS 'mainId', 
	CUSTID        IS '統一編號', 
	DUPNO         IS '重覆序號', 
	CUSTNAME      IS '客戶名稱', 
	OWNBRID       IS '編製單位代號', 
	DOCSTATUS     IS '目前文件狀態', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期', 
	CASEBRID      IS '案件號碼-分行', 
	CASENO        IS '案件號碼', 
	CASEDATE      IS '簽案日期', 
	SRCMAINID     IS '原案額度明細表MainId', 
	CNTRNO        IS '額度序號', 
	LNSUBJECT     IS '授信科目', 
	PROPERTY      IS '性質', 
	CURRENTAPPLYCURR IS '現請額度－幣別', 
	CURRENTAPPLYAMT IS '現請額度－金額', 
	NUSEMEMO      IS '狀態註記(新案才要維護)', 
	SIGNDATE      IS '已簽約日期/已動用日期', 
	REASON        IS '不簽約原因', 
	REASONDRC     IS '不簽約原因說明', 
	DATADATE      IS '核准日'
);
