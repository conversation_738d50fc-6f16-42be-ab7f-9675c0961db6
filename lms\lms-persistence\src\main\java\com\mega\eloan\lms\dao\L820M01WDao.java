/* 
 * L820M01WDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L820M01W;

/** RPA發查明家事公告細檔 **/
public interface L820M01WDao extends IGenericDao<L820M01W> {

	L820M01W findByOid(String oid);
	
	List<L820M01W> findByMainId(String mainId);
	
	L820M01W findByUniqueKey(String mainId, String branchNo, String empNo, String dataCustomerNo);

	List<L820M01W> findByIndex01(String mainId, String branchNo, String empNo, String dataCustomerNo);
	
	public List<L820M01W> findBy(String mainId, String custId);
}