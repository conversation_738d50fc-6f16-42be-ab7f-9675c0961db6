package com.mega.eloan.lms.mfaloan.bean;

import java.lang.reflect.Field;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Date;

import javax.persistence.Column;
import javax.validation.constraints.Size;

import org.springframework.jdbc.core.RowMapper;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.utils.CapBeanUtil;

/** 銀行法72-2資料檔 **/
public class ELF515 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "ELF515_CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String elf515_cntrno;

	/** 類別 **/
	@Column(name = "ELF515_TYPE", length = 3, columnDefinition = "CHAR(3)")
	private String elf515_type;

	private String elf515_instalment;

	/** 母戶預約額度序號 **/
	@Column(name = "ELF515_CNTRNO_M", length = 12, columnDefinition = "CHAR(12)")
	private String elf515_cntrno_m;

	/** 國內/海外 */
	@Column(name = "ELF515_INTER_OUTER", length = 1, columnDefinition = "CHAR(1)")
	private String elf515_inter_outer;

	/** 座落-縣市(代碼) */
	@Column(name = "ELF515_SITE1", length = 1, columnDefinition = "CHAR(1)")
	private String elf515_site1;

	/** 座落-鄉鎮市區(代碼) */
	@Column(name = "ELF515_SITE2", length = 3, columnDefinition = "CHAR(3)")
	private String elf515_site2;

	/** 座落-段(代碼) */
	@Column(name = "ELF515_SITE3", length = 4, columnDefinition = "CHAR(4)")
	private String elf515_site3;

	/** 座落-小段(代碼) */
	@Column(name = "ELF515_SITE4", length = 11, columnDefinition = "CHAR(11)")
	private String elf515_site4;

	/** 地址說明 */
	@Column(name = "ELF515_ADDRESS", length = 100, columnDefinition = "CHAR(100)")
	private String elf515_address;

	/** 建築物興建購置使用情形 */
	@Column(name = "ELF515_BUILD_STATE", length = 1, columnDefinition = "CHAR(1)")
	private String elf515_build_state;

	/** 建築物興建購置使用情形預計/完工年月日 */
	@Column(name = "ELF515_BUILD_DATE", columnDefinition = "DATE")
	Date elf515_build_date;

	/** 小類1 **/
	@Column(name = "ELF515_SUB_TYPE1", length = 11, columnDefinition = "CHAR(11)")
	private String elf515_sub_type1;

	/** 小類2 **/
	@Column(name = "ELF515_SUB_TYPE2", length = 11, columnDefinition = "CHAR(11)")
	private String elf515_sub_type2;

	/** 小類3 **/
	@Column(name = "ELF515_SUB_TYPE3", length = 11, columnDefinition = "CHAR(11)")
	private String elf515_sub_type3;

	/** 小類4 **/
	@Column(name = "ELF515_SUB_TYPE4", length = 11, columnDefinition = "CHAR(11)")
	private String elf515_sub_type4;

	/** 小類5 **/
	@Column(name = "ELF515_SUB_TYPE5", length = 11, columnDefinition = "CHAR(11)")
	private String elf515_sub_type5;

	/** 小類6 **/
	@Column(name = "ELF515_SUB_TYPE6", length = 11, columnDefinition = "CHAR(11)")
	private String elf515_sub_type6;

	/** 小類7 **/
	@Column(name = "ELF515_SUB_TYPE7", length = 11, columnDefinition = "CHAR(11)")
	private String elf515_sub_type7;

	/** 內容1 **/
	@Column(name = "ELF515_DATA1", length = 100, columnDefinition = "VARCHAR(100)")
	private String elf515_data1;

	/** 內容2 **/
	@Column(name = "ELF515_DATA2", length = 100, columnDefinition = "VARCHAR(100)")
	private String elf515_data2;

	/** 內容3 **/
	@Column(name = "ELF515_DATA3", length = 100, columnDefinition = "VARCHAR(100)")
	private String elf515_data3;

	/** 內容4 **/
	@Column(name = "ELF515_DATA4", length = 100, columnDefinition = "VARCHAR(100)")
	private String elf515_data4;

	/** 內容5 **/
	@Column(name = "ELF515_DATA5", length = 100, columnDefinition = "VARCHAR(100)")
	private String elf515_data5;

	/** 最近異動櫃員代號 **/
	@Column(name = "ELF515_EMPL_NO", length = 6, columnDefinition = "VARCHAR(6)")
	private String elf515_empl_no;

	/** 最近異動主管代號 **/
	@Column(name = "elf515_supv_no", length = 6, columnDefinition = "VARCHAR(6)")
	private String elf515_supv_no;

	/** 建檔來源ELOAN/ALOAN **/
	@Size(max = 5)
	@Column(name = "ELF515_CREATEUNIT", length = 5, columnDefinition = "CHAR(5)")
	private String elf515_createUnit;

	/** 最近異動來源ELOAN/ALOAN **/
	@Column(name = "ELF515_MODIFYUNIT", length = 5, columnDefinition = "CHAR(5)")
	private String elf515_modifyUnit;

	/** 建檔時間 **/
	@Column(name = "ELF515_CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp elf515_createTime;

	/** 最近異動時間 **/
	@Column(name = "ELF515_MODIFYTIME", columnDefinition = "TIMESTAMP")
	private Timestamp elf515_modifyTime;

	public String getElf515_cntrno() {
		return elf515_cntrno;
	}

	public void setElf515_cntrno(String elf515_cntrno) {
		this.elf515_cntrno = elf515_cntrno;
	}

	public String getElf515_type() {
		return elf515_type;
	}

	public void setElf515_type(String elf515_type) {
		this.elf515_type = elf515_type;
	}

	public String getElf515_sub_type1() {
		return elf515_sub_type1;
	}

	public void setElf515_sub_type1(String elf515_sub_type1) {
		this.elf515_sub_type1 = elf515_sub_type1;
	}

	public String getElf515_sub_type2() {
		return elf515_sub_type2;
	}

	public void setElf515_sub_type2(String elf515_sub_type2) {
		this.elf515_sub_type2 = elf515_sub_type2;
	}

	public String getElf515_sub_type3() {
		return elf515_sub_type3;
	}

	public void setElf515_sub_type3(String elf515_sub_type3) {
		this.elf515_sub_type3 = elf515_sub_type3;
	}

	public String getElf515_sub_type4() {
		return elf515_sub_type4;
	}

	public void setElf515_sub_type4(String elf515_sub_type4) {
		this.elf515_sub_type4 = elf515_sub_type4;
	}

	public String getElf515_sub_type5() {
		return elf515_sub_type5;
	}

	public void setElf515_sub_type5(String elf515_sub_type5) {
		this.elf515_sub_type5 = elf515_sub_type5;
	}

	public String getElf515_sub_type6() {
		return elf515_sub_type6;
	}

	public void setElf515_sub_type6(String elf515_sub_type6) {
		this.elf515_sub_type6 = elf515_sub_type6;
	}

	public String getElf515_sub_type7() {
		return elf515_sub_type7;
	}

	public void setElf515_sub_type7(String elf515_sub_type7) {
		this.elf515_sub_type7 = elf515_sub_type7;
	}

	public String getElf515_cntrno_m() {
		return elf515_cntrno_m;
	}

	public void setElf515_cntrno_m(String elf515_cntrno_m) {
		this.elf515_cntrno_m = elf515_cntrno_m;
	}

	public String getElf515_data1() {
		return elf515_data1;
	}

	public void setElf515_data1(String elf515_data1) {
		this.elf515_data1 = elf515_data1;
	}

	public String getElf515_data2() {
		return elf515_data2;
	}

	public void setElf515_data2(String elf515_data2) {
		this.elf515_data2 = elf515_data2;
	}

	public String getElf515_data3() {
		return elf515_data3;
	}

	public void setElf515_data3(String elf515_data3) {
		this.elf515_data3 = elf515_data3;
	}

	public String getElf515_data4() {
		return elf515_data4;
	}

	public void setElf515_data4(String elf515_data4) {
		this.elf515_data4 = elf515_data4;
	}

	public String getElf515_data5() {
		return elf515_data5;
	}

	public void setElf515_data5(String elf515_data5) {
		this.elf515_data5 = elf515_data5;
	}

	public String getElf515_empl_no() {
		return elf515_empl_no;
	}

	public void setElf515_empl_no(String elf515_empl_no) {
		this.elf515_empl_no = elf515_empl_no;
	}

	public String getElf515_supv_no() {
		return elf515_supv_no;
	}

	public void setElf515_supv_no(String elf515_supv_no) {
		this.elf515_supv_no = elf515_supv_no;
	}

	public String getElf515_createUnit() {
		return elf515_createUnit;
	}

	public void setElf515_createUnit(String elf515_createUnit) {
		this.elf515_createUnit = elf515_createUnit;
	}

	public String getElf515_modifyUnit() {
		return elf515_modifyUnit;
	}

	public void setElf515_modifyUnit(String elf515_modifyUnit) {
		this.elf515_modifyUnit = elf515_modifyUnit;
	}

	public Timestamp getElf515_createTime() {
		return elf515_createTime;
	}

	public void setElf515_createTime(Timestamp elf515_createTime) {
		this.elf515_createTime = elf515_createTime;
	}

	public Timestamp getElf515_modifyTime() {
		return elf515_modifyTime;
	}

	public void setElf515_modifyTime(Timestamp elf515_modifyTime) {
		this.elf515_modifyTime = elf515_modifyTime;
	}

	public String getElf515_instalment() {
		return elf515_instalment;
	}

	public void setElf515_instalment(String elf515_instalment) {
		this.elf515_instalment = elf515_instalment;
	}

	public String getElf515_inter_outer() {
		return elf515_inter_outer;
	}

	public void setElf515_inter_outer(String elf515_inter_outer) {
		this.elf515_inter_outer = elf515_inter_outer;
	}

	public String getElf515_site1() {
		return elf515_site1;
	}

	public void setElf515_site1(String elf515_site1) {
		this.elf515_site1 = elf515_site1;
	}

	public String getElf515_site2() {
		return elf515_site2;
	}

	public void setElf515_site2(String elf515_site2) {
		this.elf515_site2 = elf515_site2;
	}

	public String getElf515_site3() {
		return elf515_site3;
	}

	public void setElf515_site3(String elf515_site3) {
		this.elf515_site3 = elf515_site3;
	}

	public String getElf515_site4() {
		return elf515_site4;
	}

	public void setElf515_site4(String elf515_site4) {
		this.elf515_site4 = elf515_site4;
	}

	public String getElf515_address() {
		return elf515_address;
	}

	public void setElf515_address(String elf515_address) {
		this.elf515_address = elf515_address;
	}

	public String getElf515_build_state() {
		return elf515_build_state;
	}

	public void setElf515_build_state(String elf515_build_state) {
		this.elf515_build_state = elf515_build_state;
	}

	public Date getElf515_build_date() {
		return elf515_build_date;
	}

	public void setElf515_build_date(Date elf515_build_date) {
		this.elf515_build_date = elf515_build_date;
	}

	public class ELF515RM implements RowMapper<ELF515> {
		public ELF515 mapRow(ResultSet rs, int rowNum) throws SQLException {

			Field[] cols = CapBeanUtil.getField(ELF515.class, false);
			ELF515 elf515 = new ELF515();

			for (Field field : cols) {
				try {
					elf515.set(field.getName(), rs.getObject(field.getName()));
				} catch (CapException e) {
					e.printStackTrace();
				}
			}

			return elf515;
		}
	}

}
