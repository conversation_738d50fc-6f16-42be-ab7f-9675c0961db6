package com.mega.eloan.lms.cls.report.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.pages.CLS1021M01Page;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.impl.C102M01ADaoImpl;
import com.mega.eloan.lms.dao.impl.C102M01BDaoImpl;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C102M01B;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生動審表PDF
 * 
 * <AUTHOR> 2012/12/26
 * 
 */
@Service("cls1021r01rptservice")
public class CLS1021R01RptServiceImpl extends AbstractReportService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1021R01RptServiceImpl.class);
	@Resource
	EloandbBASEService eloanDbService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	C102M01ADaoImpl c102m01adao;
	@Resource
	C102M01BDaoImpl c102m01bdao;
	@Resource
	L120M01FDao l120m01fDao;
	@Resource
	BranchService branch;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;

	@Override
	public String getReportTemplateFileName() {
		LOGGER.info("into getReportTemplateFileName");
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/cls/CLS1021R01_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {
		LOGGER.info("into setReportData");
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1021M01Page.class);
		
		Properties prop_CLS1151S01Page = MessageBundleScriptCreator.getComponentResource(CLS1151S01Page.class);
		
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		// 
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 簽報書mainId
		String caseMainId = Util.trim(params.getString("caseMainId"));
		// C102M01A．購置房屋擔保放款風險權數檢核表主檔
		C102M01A c102m01a = null;
		// C102M01B．購置房屋擔保放款風險權數檢核表簽章欄檔
		List<C102M01B> c102m01blist = null;

		String branchName = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();
			c102m01a = c102m01adao.findByOid(mainOid);
			if (c102m01a == null) {
				c102m01a = new C102M01A();
			}
			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			// 當來源是簽報書印的簽章欄不同
			if (Util.isNotEmpty(caseMainId)) {
				List<L120M01F> l120m01fList = l120m01fDao
						.findByMainId(caseMainId);
				for (L120M01F l120m01f : l120m01fList) {
					// 要加上人員代碼
					if (UtilConstants.BRANCHTYPE.分行.equals(l120m01f
							.getBranchType())) {
						String type = Util.trim(l120m01f.getStaffJob());
						String userId = Util.trim(l120m01f.getStaffNo());
						String value = Util
								.trim(lmsService.getUserName(userId));
						if ("L1".equals(type)) {
							apprid = userId + " " + value;
						} else if ("L3".equals(type)) {
							bossid = bossid + userId + " " + value + "<br/>";
						} else if ("L4".equals(type)) {
							recheckid = userId + " " + value;
						} else if ("L5".equals(type)) {
							managerid = userId + " " + value;
						}
					}
				}
			} else {
				c102m01blist = c102m01bdao.findByMainId(mainId);

				for (C102M01B c102m01b : c102m01blist) {
					// 要加上人員代碼
					String type = Util.trim(c102m01b.getStaffJob());
					String userId = Util.trim(c102m01b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) {
						bossid = bossid + userId + " " + value + "<br/>";
					} else if ("L4".equals(type)) {
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) {
						managerid = userId + " " + value;
					}
				}
			}

			String c102m01a_rptId = Util.trim(c102m01a.getRptId());
			String titleStrByRptId = prop_CLS1151S01Page.getProperty("C102M01A.title01"); //購置房屋擔保放款風險權數檢核表
			String rskFlag_hid_row = "N";
			if(Util.equals(LMSUtil.C102M01A_RPTID_V202208, c102m01a_rptId)){
				titleStrByRptId = prop_CLS1151S01Page.getProperty("C102M01A.title01.V202208");
				rskFlag_hid_row = "Y";
			}else if(Util.equals(LMSUtil.C102M01A_RPTID_V20171231, c102m01a_rptId)){
				titleStrByRptId = prop_CLS1151S01Page.getProperty("C102M01A.title01"); 
			}
			
			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(c102m01a.getOwnBrId())));
			String custId = Util.nullToSpace(c102m01a.getCustId()) + " "
					+ Util.nullToSpace(c102m01a.getDupNo());
			String chk11 = Util.nullToSpace(prop.getProperty("COMMON1."
					+ c102m01a.getChk11()));
			String chk12 = Util.nullToSpace(prop.getProperty("COMMON1."
					+ c102m01a.getChk12()));
			String chk13 = Util.nullToSpace(prop.getProperty("COMMON1."
					+ c102m01a.getChk13()));
			String chk14 = Util.nullToSpace(prop.getProperty("COMMON1."
					+ c102m01a.getChk14()));
			String selfchk = Util.nullToSpace(prop.getProperty("COMMON1."
					+ c102m01a.getSelfChk()));
			
			String custname = Util.nullToSpace(c102m01a.getCustName());
			String aloanac = Util.nullToSpace(c102m01a.getALoanAC());
			String cntrno = Util.nullToSpace(c102m01a.getCntrNo());
			String aloandate = Util.nullToSpace(Util.getDate(c102m01a
					.getALoanDate()));

			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("C102M01A.Mainid",
					Util.trim(c102m01a.getMainId()));
			rptVariableMap.put("C102M01A.CUSTID", custId);
			rptVariableMap.put("C102M01A.CHK11", chk11);
			rptVariableMap.put("C102M01A.CHK12", chk12);
			rptVariableMap.put("C102M01A.CHK13", chk13);
			rptVariableMap.put("C102M01A.CHK14", chk14);
			rptVariableMap.put("C102M01A.SELFCHK", selfchk);
			rptVariableMap.put("C102M01A.RSKFLAG", LMSUtil.build_rskFlag_printing(prop, c102m01a));
			ClsUtil.setRpt_CLS1021R01_data(rptVariableMap, rskFlag_hid_row, c102m01a_rptId, titleStrByRptId);
			rptVariableMap.put("C102M01A.CUSTNAME", custname);
			rptVariableMap.put("C102M01A.ALOANAC", aloanac);
			rptVariableMap.put("C102M01A.CNTRNO", cntrno);
			rptVariableMap.put("C102M01A.ALOANDATE", aloandate);
			rptVariableMap.put("C102M01B.APPRID", apprid);
			rptVariableMap.put("C102M01B.RECHECKID", recheckid);
			rptVariableMap.put("C102M01B.BOSSID", bossid);
			rptVariableMap.put("C102M01B.MANAGERID", managerid);
			rptVariableMap.put("C102M01B.TITLEMARK", "");
			rptVariableMap.put("C102M01A_RskFlagEnd", "");
			
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);

		} finally {

		}
	}
}
