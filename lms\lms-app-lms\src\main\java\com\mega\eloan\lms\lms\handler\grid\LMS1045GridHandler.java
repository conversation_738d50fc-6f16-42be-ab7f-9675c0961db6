package com.mega.eloan.lms.lms.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lms.service.LMS1035Service;
import com.mega.eloan.lms.model.C123M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1045gridhandler")
public class LMS1045GridHandler extends AbstractGridHandler {

	@Resource
	CLSService clsService;

	@Resource
	RetrialService retrialService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	DocFileService docFileService;

	@Resource
	LMS1035Service lms1035Service;

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryc123data(ISearch pageSetting,
			PageParameters params) throws CapException {		
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		String search_custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(search_custId)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					search_custId);
		}
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());
		
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		
		List<C123M01A> src_list = (List<C123M01A>) clsService.findPage(C123M01A.class, pageSetting).getContent();
		for (C123M01A model : src_list) {
			Map<String, Object> row = new HashMap<String, Object>();
			row.put("oid", model.getOid());
			row.put("mainId", model.getMainId());
			row.put("caseNo", model.getCaseNo());
			row.put("custId", model.getCustId());
			row.put("dupNo", model.getDupNo());
			row.put("custName", model.getCustName());
			row.put(EloanConstants.MAIN_OID, Util.trim(model.getOid()));
			row.put(EloanConstants.MAIN_DOC_STATUS, Util.trim(model.getDocStatus()));
			row.put(EloanConstants.MAIN_ID, Util.trim(model.getMainId()));
			list.add(row);
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
}