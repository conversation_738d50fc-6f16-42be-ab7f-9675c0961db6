package com.mega.eloan.lms.model;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import java.math.BigDecimal;

/** 收入明細表 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C120S01W", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C120S01W extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;
	
	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	@Column(name="KEYSTRING", length=10, columnDefinition="VARCHAR(10)")
	private String keyString;

	@Column(name="VALUESTRING", length=10, columnDefinition="VARCHAR(300)")
	private String valueString;

	/**收入類別 A經常性，B非經常性*/
	@Column(name="INCOMETYPE", length=1, columnDefinition="CHAR(1)")
	private String incomeType;
	/**收入項目*/
	@Column(name="INCOMEITEM", length=1, columnDefinition="CHAR(3)")
	private String incomeItem;
	/**收入公式*/
	@Column(name="FORMULATYPE", length=2, columnDefinition="VARCHAR(2)")
	private String formulaType;
	/**金額1*/
	@Column(name="VALUE01", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value01;
	/**金額2*/
	@Column(name="VALUE02", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value02;
	/**金額3*/
	@Column(name="VALUE03", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value03;
	/**金額4*/
	@Column(name="VALUE04", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value04;
	/**金額5*/
	@Column(name="VALUE05", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value05;
	/**金額6*/
	@Column(name="VALUE06", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value06;
	/**金額7*/
	@Column(name="VALUE07", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value07;
	/**金額8*/
	@Column(name="VALUE08", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value08;
	/**金額9*/
	@Column(name="VALUE09", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value09;
	/**金額10*/
	@Column(name="VALUE10", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value10;
	/**金額11*/
	@Column(name="VALUE11", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value11;
	/**金額12*/
	@Column(name="VALUE12", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal value12;
	/**常態性獎金*/
	@Column(name="BONUS", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal bonus;
	/**淨利率*/
	@Column(name="INPROFIT", length=3, columnDefinition="DECIMAL(3)")
	private BigDecimal inProfit;
	/**折算率*/
	@Column(name="DISRATE", length=3, columnDefinition="DECIMAL(3)")
	private BigDecimal disRate;
	/**持股*/
	@Column(name="HOLDING", length=5, columnDefinition="DECIMAL(5,2)")
	private BigDecimal holding;
	/**年化後金額*/
	@Column(name="VALUEYEAR", length=13, columnDefinition="DECIMAL(13)")
	private BigDecimal valueYear;
	/**依近12個月實際收入計算*/
	@Column(name="ACTUALLYINCOMESUM", length=1, columnDefinition="CHAR(1)")
	private String actuallyIncomeSum;


	@Override
	public String getOid() {
		return oid;
	}

	@Override
	public void setOid(String oid) {
		this.oid = oid;
	}

	@Override
	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public String getKeyString() {
		return keyString;
	}

	public void setKeyString(String keyString) {
		this.keyString = keyString;
	}

	public String getValueString() {
		return valueString;
	}

	public void setValueString(String valueString) {
		this.valueString = valueString;
	}

	public String getIncomeType() {
		return incomeType;
	}

	public void setIncomeType(String incomeType) {
		this.incomeType = incomeType;
	}

	public String getIncomeItem() {
		return incomeItem;
	}

	public void setIncomeItem(String incomeItem) {
		this.incomeItem = incomeItem;
	}

	public String getFormulaType() {
		return formulaType;
	}

	public void setFormulaType(String formulaType) {
		this.formulaType = formulaType;
	}

	public BigDecimal getValue01() {
		return value01;
	}

	public void setValue01(BigDecimal value01) {
		this.value01 = value01;
	}

	public BigDecimal getValue02() {
		return value02;
	}

	public void setValue02(BigDecimal value02) {
		this.value02 = value02;
	}

	public BigDecimal getValue03() {
		return value03;
	}

	public void setValue03(BigDecimal value03) {
		this.value03 = value03;
	}

	public BigDecimal getValue04() {
		return value04;
	}

	public void setValue04(BigDecimal value04) {
		this.value04 = value04;
	}

	public BigDecimal getValue05() {
		return value05;
	}

	public void setValue05(BigDecimal value05) {
		this.value05 = value05;
	}

	public BigDecimal getValue06() {
		return value06;
	}

	public void setValue06(BigDecimal value06) {
		this.value06 = value06;
	}

	public BigDecimal getValue07() {
		return value07;
	}

	public void setValue07(BigDecimal value07) {
		this.value07 = value07;
	}

	public BigDecimal getValue08() {
		return value08;
	}

	public void setValue08(BigDecimal value08) {
		this.value08 = value08;
	}

	public BigDecimal getValue09() {
		return value09;
	}

	public void setValue09(BigDecimal value09) {
		this.value09 = value09;
	}

	public BigDecimal getValue10() {
		return value10;
	}

	public void setValue10(BigDecimal value10) {
		this.value10 = value10;
	}

	public BigDecimal getValue11() {
		return value11;
	}

	public void setValue11(BigDecimal value11) {
		this.value11 = value11;
	}

	public BigDecimal getValue12() {
		return value12;
	}

	public void setValue12(BigDecimal value12) {
		this.value12 = value12;
	}

	public BigDecimal getBonus() {
		return bonus;
	}

	public void setBonus(BigDecimal bonus) {
		this.bonus = bonus;
	}

	public BigDecimal getInProfit() {
		return inProfit;
	}

	public void setInProfit(BigDecimal inProfit) {
		this.inProfit = inProfit;
	}

	public BigDecimal getDisRate() {
		return disRate;
	}

	public void setDisRate(BigDecimal disRate) {
		this.disRate = disRate;
	}

	public BigDecimal getHolding() {
		return holding;
	}

	public void setHolding(BigDecimal holding) {
		this.holding = holding;
	}

	public BigDecimal getValueYear() {
		return valueYear;
	}

	public void setValueYear(BigDecimal valueYear) {
		this.valueYear = valueYear;
	}

	public String getActuallyIncomeSum() {
		return actuallyIncomeSum;
	}

	public void setActuallyIncomeSum(String actuallyIncomeSum) {
		this.actuallyIncomeSum = actuallyIncomeSum;
	}
}
