$(function(){
    var grid = $("#gridview").iGrid({
        handler: 'lms1605gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        postData: {
            formAction: "queryL160m01a",
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: 'createTime|custId',
        sortorder: 'desc|desc',
        multiselect: true,
        colModel: [{
            colHeader: i18n.lms1605m01['L160M01A.mainCustId'],//"主要借款人統編",
            name: 'custId',
            width: 80,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms1605m01['L160M01A.mainCust'],//"主要借款人",
            name: 'custName',
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.caseNo'],//"案號",
            name: 'caseNo',
            width: 150,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.cntrNo'],//"動用額度序號",
            name: 'allCanPay',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms1605m01['L160M01A.creatorPerson'],//"分行經辦",
            name: 'apprId',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.allCanPay'],//"先行動用",
            name: 'useType',
            width: 60,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1605m01['L160M01A.willFinishDate'],//"預定補全日",
            name: 'blackDataDate',
            width: 80,
            sortable: false,
            align: "center"
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',//'../lms/lms1605m01/01'
            data: {
                //formAction : "queryL160m01a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows.length == 0) {
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                $.ajax({
                    handler: "lms1605m01formhandler",
                    data: {
                        formAction: "deleteL160m01a",
                        oids: data
                    }
                }).done(
					function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
				);
            }
        });
    }).end().find("#btnAdd").click(function(){
        $.ajax({
            handler: "lms1605m01formhandler",
            data: {
                formAction: "newl160m01a"
            }
        }).done(
			function(obj){
                $.form.submit({
                    url: '../lms/lms1605m01/01',
                    data: {
                        formAction: "queryL160m01a",
                        oid: obj.oid,
                        mainOid: obj.oid,
                        mainDocStatus: viewstatus,
                        txCode: txCode
                    },
                    target: "_blank"
                });
            }
		);
    }).end().find("#btnModify").click(function(){
        var id = $("#gridview").getGridParam('selarrrow');
        if (id.length == 0) {
        
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        if (id.length > 1) {
            CommonAPI.showMessage(i18n.lms1605m01["L160M01a.error1"]);
        } else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selarrrow');
        if (id == "") {
        
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            return CommonAPI.showMessage(i18n.lms1605m01["L160M01A.error1"]);
        } else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    });
});
