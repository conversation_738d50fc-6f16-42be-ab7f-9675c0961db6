/* 
 * C160S01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 動審表匯入明細檔 **/
/*
J-107-0248 這個JavaBean的欄位名稱如調整, excelFields.properties 裡面的欄位值, 也要一併調
	在測試時
	
	select * from lms.c160m02a m inner join lms.c120s01q q on m.mainid=q.mainId
	where q.ownbrid='002' and q.JCICQDATE>='2018-05-01'
	
	=> 找出適合的 data 後, 用【orm-lms.xml 裡的  name="c120s01q.max_GrdCDate_jcic_etch_2m" 】去驗證	
 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C160S01D", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "staffNo", "custId" }))
public class C160S01D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 職員 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Size(max = 6, min = 6, message = "長度要等於6位", groups = Check.class)
	@Column(name = "STAFFNO", length = 6, columnDefinition = "CHAR(6)")
	private String staffNo;

	/** 借款人統編 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Size(max = 11, min = 11, message = "長度要等於11位", groups = Check.class)
	@Column(name = "CUSTID", length = 11, columnDefinition = "VARCHAR(11)")
	private String custId;

	/** 借款人名稱 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 授信額度合計金額
	 * <p/>
	 * 元
	 */
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "LOANTOTAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal LoanTotAmt;

	/** 期限-月 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "MONTH", columnDefinition = "DECIMAL(2,0)")
	private Integer Month;

	/** 授信期間-起始日期 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "LNFROMDATE", columnDefinition = "DATE")
	private Date lnFromDate;

	/** 授信期間-截止日期 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "LNENDDATE", columnDefinition = "DATE")
	private Date lnEndDate;

	/** 償還方式 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Size(max = 1)
	@Pattern(regexp = "[23]", message = "只限2或3", groups = Check.class)
	@Column(name = "PAYWAY", length = 1, columnDefinition = "CHAR(1)")
	private String payWay;

	/**
	 * 每期攤還本金
	 * <p/>
	 * 元
	 */
	@Digits(integer = 15, fraction = 0, groups = Check.class)
	@Column(name = "PAYWAYAMT", columnDefinition = "DECIMAL(15,0)")
	private BigDecimal payWayAmt;

	/** 自動進帳 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Size(max = 1)
	@Pattern(regexp = "[YN]", message = "只限Y或N", groups = Check.class)
	@Column(name = "AUTORCT", length = 1, columnDefinition = "CHAR(01)")
	private String autoRct;

	/** 進帳日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "RCTDATE", columnDefinition = "DATE")
	private Date rctDate;

	/** 進帳帳號 **/
	@Size(max = 11)
	@Column(name = "ACCNO", length = 11, columnDefinition = "VARCHAR(16)")
	private String accNo;

	/** 自動扣帳 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Size(max = 1)
	@Pattern(regexp = "[YN]", message = "只限Y或N", groups = Check.class)
	@Column(name = "AUTOPAY", length = 1, columnDefinition = "CHAR(01)")
	private String autoPay;

	/** 扣款帳號 **/
	@Size(max = 16)
	@Column(name = "ATPAYNO", length = 16, columnDefinition = "VARCHAR(16)")
	private String atpayNo;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "VARCHAR(12)")
	private String cntrNo;

	/** 從債務人統編 **/
	@Size(max = 11)
	@Column(name = "RID1", length = 11, columnDefinition = "VARCHAR(11)")
	private String rId1;

	/** 從債務人名稱 **/
	@Size(max = 120)
	@Column(name = "RNAME1", length = 120, columnDefinition = "VARCHAR(120)")
	private String rName1;

	/** 關係類別細項 **/
	@Size(max = 2)
	@Column(name = "RKINDD1", length = 2, columnDefinition = "VARCHAR(2)")
	private String rKindD1;

	/** 相關身份 **/
	@Size(max = 1)
	@Column(name = "RTYPE1", length = 1, columnDefinition = "VARCHAR(1)")
	private String rType1;

	/** 從債務人統編 **/
	@Size(max = 11)
	@Column(name = "RID2", length = 11, columnDefinition = "VARCHAR(11)")
	private String rId2;

	/** 從債務人名稱 **/
	@Size(max = 120)
	@Column(name = "RNAME2", length = 120, columnDefinition = "VARCHAR(120)")
	private String rName2;

	/** 關係類別細項 **/
	@Size(max = 2)
	@Column(name = "RKINDD2", length = 2, columnDefinition = "VARCHAR(2)")
	private String rKindD2;

	/** 相關身份 **/
	@Size(max = 1)
	@Column(name = "RTYPE2", length = 1, columnDefinition = "VARCHAR(1)")
	private String rType2;

	/** 動用期間-起始日期 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "USEFROMDATE", columnDefinition = "DATE")
	private Date useFromDate;

	/** 動用期間-截止日期 **/
	@NotNull(groups = Check.class)
	@NotEmpty(groups = Check.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "USEENDDATE", columnDefinition = "DATE")
	private Date useEndDate;

	/** 結果 **/
	@Size(max = 500)
	@Column(name = "RESULT", length = 500, columnDefinition = "VARCHAR(500)")
	private String result;

	/**
	 * 年薪
	 * <p/>
	 * 萬元
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "ANNUITY", columnDefinition = "DECIMAL(5,0)")
	private Integer annuity;

	/**
	 * 其他所得
	 * <p/>
	 * 萬元
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "OTHINCOME", columnDefinition = "DECIMAL(5,0)")
	private Integer othincome;

	/**
	 * 是否已上傳MIS
	 * <p/>
	 * Y:是 N:否<br/>
	 * 2013/03/19新增
	 */
	@Column(name = "MISFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String misflag;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 國別 **/
//	@NotNull(groups = Check.class)
//	@NotEmpty(groups = Check.class)	
	@Size(max = 2)
	@Column(name = "NTCODE", length = 2, columnDefinition = "CHAR(2)")
	private String ntCode;
	
	/** 從債務人1國別 **/
	@Size(max = 2)
	@Column(name = "RNTCODE1", length = 2, columnDefinition = "CHAR(2)")
	private String rNtCode1;
	
	/** 從債務人2國別 **/
	@Size(max = 2)
	@Column(name = "RNTCODE2", length = 2, columnDefinition = "CHAR(2)")
	private String rNtCode2;
	
	/** 職業大類_小類_職稱 **/
	@Size(max = 4)
	@Column(name = "JOBCLASS", length = 4, columnDefinition = "CHAR(4)")
	private String jobClass;
	
	@Column(name = "C122M01A_MAINID", length = 32, columnDefinition = "VARCHAR(32)")
	private String c122m01a_mainId;
	
	/** 產線上對保契約{P:應產生, Y:已產生} **/
	@Column(name = "GEN_CTRTYPE_C", length = 1, columnDefinition = "CHAR(1)")
	private String gen_ctrType_C;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得職員 **/
	public String getStaffNo() {
		return this.staffNo;
	}

	/** 設定職員 **/
	public void setStaffNo(String value) {
		this.staffNo = value;
	}

	/** 取得借款人統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定借款人統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得借款人名稱 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定借款人名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得授信額度合計金額
	 * <p/>
	 * 元
	 */
	public BigDecimal getLoanTotAmt() {
		return this.LoanTotAmt;
	}

	/**
	 * 設定授信額度合計金額
	 * <p/>
	 * 元
	 **/
	public void setLoanTotAmt(BigDecimal value) {
		this.LoanTotAmt = value;
	}

	/** 取得期限-月 **/
	public Integer getMonth() {
		return this.Month;
	}

	/** 設定期限-月 **/
	public void setMonth(Integer value) {
		this.Month = value;
	}

	/** 取得授信期間-起始日期 **/
	public Date getLnFromDate() {
		return this.lnFromDate;
	}

	/** 設定授信期間-起始日期 **/
	public void setLnFromDate(Date value) {
		this.lnFromDate = value;
	}

	/** 取得授信期間-截止日期 **/
	public Date getLnEndDate() {
		return this.lnEndDate;
	}

	/** 設定授信期間-截止日期 **/
	public void setLnEndDate(Date value) {
		this.lnEndDate = value;
	}

	/** 取得償還方式 **/
	public String getPayWay() {
		return this.payWay;
	}

	/** 設定償還方式 **/
	public void setPayWay(String value) {
		this.payWay = value;
	}

	/**
	 * 取得每期攤還本金
	 * <p/>
	 * 元
	 */
	public BigDecimal getPayWayAmt() {
		return this.payWayAmt;
	}

	/**
	 * 設定每期攤還本金
	 * <p/>
	 * 元
	 **/
	public void setPayWayAmt(BigDecimal value) {
		this.payWayAmt = value;
	}

	/** 取得自動進帳 **/
	public String getAutoRct() {
		return this.autoRct;
	}

	/** 設定自動進帳 **/
	public void setAutoRct(String value) {
		this.autoRct = value;
	}

	/** 取得進帳日期 **/
	public Date getRctDate() {
		return this.rctDate;
	}

	/** 設定進帳日期 **/
	public void setRctDate(Date value) {
		this.rctDate = value;
	}

	/** 取得進帳帳號 **/
	public String getAccNo() {
		return this.accNo;
	}

	/** 設定進帳帳號 **/
	public void setAccNo(String value) {
		this.accNo = value;
	}

	/** 取得自動扣帳 **/
	public String getAutoPay() {
		return this.autoPay;
	}

	/** 設定自動扣帳 **/
	public void setAutoPay(String value) {
		this.autoPay = value;
	}

	/** 取得扣款帳號 **/
	public String getAtpayNo() {
		return this.atpayNo;
	}

	/** 設定扣款帳號 **/
	public void setAtpayNo(String value) {
		this.atpayNo = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得從債務人統編 **/
	public String getRId1() {
		return this.rId1;
	}

	/** 設定從債務人統編 **/
	public void setRId1(String value) {
		this.rId1 = value;
	}

	/** 取得從債務人名稱 **/
	public String getRName1() {
		return this.rName1;
	}

	/** 設定從債務人名稱 **/
	public void setRName1(String value) {
		this.rName1 = value;
	}

	/** 取得關係類別細項 **/
	public String getRKindD1() {
		return this.rKindD1;
	}

	/** 設定關係類別細項 **/
	public void setRKindD1(String value) {
		this.rKindD1 = value;
	}

	/** 取得相關身份 **/
	public String getRType1() {
		return this.rType1;
	}

	/** 設定相關身份 **/
	public void setRType1(String value) {
		this.rType1 = value;
	}

	/** 取得從債務人統編 **/
	public String getRId2() {
		return this.rId2;
	}

	/** 設定從債務人統編 **/
	public void setRId2(String value) {
		this.rId2 = value;
	}

	/** 取得從債務人名稱 **/
	public String getRName2() {
		return this.rName2;
	}

	/** 設定從債務人名稱 **/
	public void setRName2(String value) {
		this.rName2 = value;
	}

	/** 取得關係類別細項 **/
	public String getRKindD2() {
		return this.rKindD2;
	}

	/** 設定關係類別細項 **/
	public void setRKindD2(String value) {
		this.rKindD2 = value;
	}

	/** 取得相關身份 **/
	public String getRType2() {
		return this.rType2;
	}

	/** 設定相關身份 **/
	public void setRType2(String value) {
		this.rType2 = value;
	}

	/** 取得動用期間-起始日期 **/
	public Date getUseFromDate() {
		return this.useFromDate;
	}

	/** 設定動用期間-起始日期 **/
	public void setUseFromDate(Date value) {
		this.useFromDate = value;
	}

	/** 取得動用期間-截止日期 **/
	public Date getUseEndDate() {
		return this.useEndDate;
	}

	/** 設定動用期間-截止日期 **/
	public void setUseEndDate(Date value) {
		this.useEndDate = value;
	}

	/** 取得結果 **/
	public String getResult() {
		return this.result;
	}

	/** 設定結果 **/
	public void setResult(String value) {
		this.result = value;
	}

	/**
	 * 取得年薪
	 * <p/>
	 * 萬元
	 */
	public Integer getAnnuity() {
		return this.annuity;
	}

	/**
	 * 設定年薪
	 * <p/>
	 * 萬元
	 **/
	public void setAnnuity(Integer value) {
		this.annuity = value;
	}

	/**
	 * 取得其他所得
	 * <p/>
	 * 萬元
	 */
	public Integer getOthincome() {
		return this.othincome;
	}

	/**
	 * 設定其他所得
	 * <p/>
	 * 萬元
	 **/
	public void setOthincome(Integer value) {
		this.othincome = value;
	}

	/**
	 * 取得是否已上傳MIS
	 * <p/>
	 * Y:是 N:否<br/>
	 * 2013/03/19新增
	 */
	public String getMisflag() {
		return this.misflag;
	}

	/**
	 * 設定是否已上傳MIS
	 * <p/>
	 * Y:是 N:否<br/>
	 * 2013/03/19新增
	 **/
	public void setMisflag(String value) {
		this.misflag = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得國別 **/
	public String getNtCode() {
		return ntCode;
	}
	/** 設定國別 **/
	public void setNtCode(String ntCode) {
		this.ntCode = ntCode;
	}
	
	/** 取得從債務人1國別 **/
	public String getRNtCode1() {
		return rNtCode1;
	}
	/** 設定從債務人1國別 **/
	public void setRNtCode1(String rNtCode1) {
		this.rNtCode1 = rNtCode1;
	}
	
	/** 取得從債務人2國別 **/
	public String getRNtCode2() {
		return rNtCode2;
	}
	/** 設定從債務人1國別 **/
	public void setRNtCode2(String rNtCode2) {
		this.rNtCode2 = rNtCode2;
	}

	/** 取得職業大類_小類_職稱 **/
	public String getJobClass() {
		return jobClass;
	}
	/** 設定職業大類_小類_職稱 **/
	public void setJobClass(String jobClass) {
		this.jobClass = jobClass;
	}
	
	/** 取得 */
	public String getC122m01a_mainId() {
		return c122m01a_mainId;
	}
	/** 設定 */
	public void setC122m01a_mainId(String c122m01a_mainId) {
		this.c122m01a_mainId = c122m01a_mainId;
	}
	
	/** 取得產線上對保契約{P:應產生, Y:已產生} */
	public String getGen_ctrType_C() {
		return gen_ctrType_C;
	}
	/** 設定產線上對保契約{P:應產生, Y:已產生} */
	public void setGen_ctrType_C(String gen_ctrType_C) {
		this.gen_ctrType_C = gen_ctrType_C;
	}

}
