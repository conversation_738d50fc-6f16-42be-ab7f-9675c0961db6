package com.mega.eloan.lms.eloandb.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.eloandb.service.Dw_elf411ovsService;

@Service
public class Dw_elf411ovsServiceImpl extends AbstractEloandbJdbc implements
		Dw_elf411ovsService {

	@Override
	public List<Map<String, Object>> findELF411ByNewDate(String branch,
			String custId, String dupNo, String newDate) {
		return this.getJdbc().queryForList("DW_ELF411OVS.selCntrNo",
				new Object[] { branch, custId, dupNo, newDate });
	}

	@Override
	public List<Map<String, Object>> findELF411ByCycMn(String dataDate,
			String branch, String custId, String dupNo) {
		return this.getJdbc().queryForList("DW_ELF411OVS.selByCycMn",
				new Object[] { dataDate, branch, custId, dupNo });
	}

	@Override
	public Map<String, Object> findELF411ForMaxDate() {

		return this.getJdbc().queryForMap("DW_ELF411OVS.selForMaxDate",
				new Object[] {});
	}

	@Override
	public List<Map<String, Object>> findELF411ByMaxDate(String brNo,
			Date dataDate) {

		return this.getJdbc().queryForList("DW_ELF411OVS.selByMaxDate",
				new Object[] { brNo, dataDate });
	}

	@Override
	public List<Map<String, Object>> find411ovsByBranchForReportType3(
			String brNo, String beDate, String enDate) {
		return this.getJdbc().queryForList(
				"DW_ELF411OVS.selByBeDateAndEndDate",
				new Object[] { beDate, enDate, brNo, brNo });
	}

}
