initDfd.done(function(json){
    
    if($("#ploanUploadGrid").length > 0){
    	$("#ploanUploadGrid").iGrid({
            handler: 'cls3401gridhandler',                
            height: 70,                
            shrinkToFit: false, 
	        needPager: false,
	        postData : {
				formAction : "queryPloanUploadGrid",
				mainId:responseJSON.mainId
			},
			colModel : [ {
				colHeader :i18n.cls3401m02['label.ploanUploadGrid.srcFileName'],//檔案名稱,
				name : 'srcFileName', width : 180, align: "left", sortable : false,
				formatter : 'click', onclick : attch_openDoc
			}, {
				colHeader : i18n.cls3401m02['label.ploanUploadGrid.fileDesc'],//檔案說明
				name : 'fileDesc', width : 200, sortable : false
			}, {
				colHeader : i18n.cls3401m02['label.ploanUploadGrid.uploadTime'],//上傳時間
				name : 'uploadTime', width : 140, sortable : false
			}, {
				name : 'oid',
				hidden : true
			}],
            ondblClickRow: function(rowid){
            }
    	});    
    }
});


function attch_openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}