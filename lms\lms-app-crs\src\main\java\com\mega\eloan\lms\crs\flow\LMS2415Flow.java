package com.mega.eloan.lms.crs.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 個金覆審
 * </pre>
 * 
 * @since 2012/2/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/14,jessica,new
 *          </ul>
 */
@Component
public class LMS2415Flow extends AbstractFlowHandler {
	
	@Resource
	C241M01EDao c241m01eDao;
	@Resource
	C241M01ADao c241m01aDao;
	
	/** 尋找C241M01E
	 * @param mainId
	 * @param branchType
	 * @param staffJob
	 * @return
	 */
	private C241M01E setC241M01EData(String mainId,String branchType,String staffJob){
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C241M01A c241m01a = c241m01aDao.findByMainId(mainId);
		C241M01E c241m01e = c241m01eDao.findByBranchTypeStaffJob(mainId,c241m01a.getCustId(),c241m01a.getDupNo(), branchType, staffJob);
		if(c241m01e == null) {
			c241m01e = new C241M01E();
			c241m01e.setMainId(mainId);
			c241m01e.setBranchType(branchType);
			c241m01e.setStaffJob(staffJob);
			c241m01e.setCustId(c241m01a.getCustId());
			c241m01e.setDupNo(c241m01a.getDupNo());
		}
		c241m01e.setBranchId(user.getUnitNo());
		c241m01e.setStaffNo(user.getUserId());
		c241m01e.setCreator(user.getUserId());
		c241m01e.setUpdater(user.getUserId());
		c241m01e.setCreateTime(CapDate.getCurrentTimestamp());
		c241m01e.setUpdateTime(CapDate.getCurrentTimestamp());
		return c241m01e;
	}

	@Transition(node = "海外_編製中", value = "呈主管")
	public void flow01(FlowInstance instance) {
		// 寫入文件核定者
		String mainId = (String) instance.getAttribute("mainId");
		//2. 覆審單位 L1. 分行經辦(授管處/營運中心)
		C241M01E c241m01e = this.setC241M01EData(mainId,"1", "L1");
		c241m01eDao.save(c241m01e);
	}

	@Transition(node = "海外_待覆核", value = "確認")
	public void flow02(FlowInstance instance) {
		// 寫入文件核定者
	}

	@Transition(node = "決策", value = "退回")
	public void flow03(FlowInstance instance) {
		String mainId = (String) instance.getAttribute("mainId");
		C241M01A c241m01a = c241m01aDao.findByMainId(mainId);
		C241M01E c241m01e = c241m01eDao.findByBranchTypeStaffJob(mainId,c241m01a.getCustId(),c241m01a.getDupNo(), "1", "L1");
		if(c241m01e != null)
			c241m01eDao.delete(c241m01e);
	}

	@Transition(node = "決策", value = "核定")
	public void flow04(FlowInstance instance) {
		// 寫入文件核定者
		String mainId = (String) instance.getAttribute("mainId");
		//2. 覆審單位 L1. 分行經辦(授管處/營運中心)
		C241M01E c241m01e = this.setC241M01EData(mainId,"1", "L4");
		c241m01eDao.save(c241m01e);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C241M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return RetrialDocStatusEnum.class;
	}
}