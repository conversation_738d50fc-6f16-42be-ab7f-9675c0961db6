/* 
 * L120S01PDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S01PDao;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S09A;

/** 相關人資料檔 **/
@Repository
public class L120S01PDaoImpl extends LMSJpaDao<L120S01P, String> implements
		L120S01PDao {

	@Override
	public L120S01P findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S01P> findByMainIdWithRType(String mainId, String rType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (rType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S01P> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S01P> findByMainIdWithoutRType(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S01P> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L120S01P findByUniqueKey(String mainId, String custId, String dupNo,
			String rId, String rDupNo, String rType, String rName) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (rId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rId", rId);
		if (rDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rDupNo", rDupNo);
		if (rType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		if (rName != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rName", rName);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120S01P> findByMainIdAndCustIdWithRType(String mainId,
			String custId, String dupNo, String rType) {
		ISearch search = createSearchTemplete();
		List<L120S01P> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (rType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S01P> findByMainIdAndCustIdWithoutRType(String mainId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L120S01P> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L120S01P findByRNameWithRType(String mainId, String custId,
			String dupNo, String rType, String rName) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (rType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		if (rName != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rName", rName);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120S01P> findL120S01PListByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}

	@Override
	public L120S01P findMaxQDateByMainIdAndCustIdWithRType(String mainId,
			String custId, String dupNo, String rType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		search.addOrderBy("queryDateS", true);

		return findUniqueOrNone(search);
	}

	@Override
	public L120S01P findMaxSeqNumByMainIdAndCustIdWithRType(String mainId,
			String custId, String dupNo, String rType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);
		search.addOrderBy("seqNum", true);

		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S01P> findByMainIdAndCustIdWithRTypeForBuildStr(
			String mainId, String custId, String dupNo, String rType) {
		ISearch search = createSearchTemplete();
		List<L120S01P> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (rType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rType", rType);

		search.addOrderBy("type", true);
		search.addOrderBy("seqNum", true);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

}