<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
	   	<script type="text/javascript">
      		loadScript('pagejs/base/LMSS21APage');
	    </script>
		<form id="LMS1205S21Form01">
		  <!--J-106-0XXX-001  Web e-Loan企金授信新增主要還款來源國等相關欄位-->
		  <fieldset >
	        <legend><b><th:block th:text="#{'l120m01i.paySourceCountry'}">本案主要還款來源國(主要客戶國籍)之現況說明</th:block></b></legend>
			     <!--
		        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
		         <tr>
		            <td class="hd1" width="20%"><th:block th:text="#{'l120m01i.isFreezeFactCountry'}">是否屬凍結額度國家</th:block>&nbsp;&nbsp;</td>
					<td width="20%">
						<label>
	                        <input type="radio" id="isFreezeFactCountry" name="isFreezeFactCountry" value="Y" />
	                        <th:block th:text="#{'yes'}">是</th:block>
	                    </label>
	                    <label>
	                        <input type="radio" name="isFreezeFactCountry" value="N" />
	                        <th:block th:text="#{'no'}">否</th:block>
	                    </label>
					</td>
		            <td class="hd1" width="30%">
		            	<div class="showFreezeFactCountry" style="display:none; margin-top:5px;">
			                 <th:block th:text="#{'l120m01i.freezeFactCountry'}">凍結額度國家</th:block>
							 <BR>
						     <button type="button" id="selectFreezeFactCountryBt">
		                        <span class="text-only"><th:block th:text="#{'l120m01i.logIn'}">登錄</th:block></span>
		                     </button>
						</div>
		            </td>
					<td width="30%">
						<div class="showFreezeFactCountry" style="display:none; margin-top:5px;">
							 <textarea id="freezeFactCountryShow" name="freezeFactCountryShow"  class="caseReadOnly" cols="60" rows="3" readonly></textarea>
							 <BR>
							 <input type="hidden" id="freezeFactCountry" name="freezeFactCountry" />
						 </div>
		            </td>
		          </tr>	
		          <tr>
		            <td class="hd1" width="20%"><th:block th:text="#{'l120m01i.isNoFactCountry'}">是否屬未核配額度國家</th:block>&nbsp;&nbsp;</td>
					<td width="20%">
						<label>
	                        <input type="radio" id="isNoFactCountry" name="isNoFactCountry" value="Y" />
	                        <th:block th:text="#{'yes'}">是</th:block>
	                    </label>
	                    <label>
	                        <input type="radio" name="isNoFactCountry" value="N" />
	                        <th:block th:text="#{'no'}">否</th:block>
	                    </label>
					</td>
		            <td class="hd1" width="30%">
		            	<div class="showNoFactCountry" style="display:none; margin-top:5px;">
			                 <th:block th:text="#{'l120m01i.noFactCountry'}">未核配額度國家</th:block>
			                 <BR>
						     <button type="button" id="selectNoFactCountryBt">
		                        <span class="text-only"><th:block th:text="#{'l120m01i.logIn'}">登錄</th:block></span>
		                     </button>
						 </div>
		            </td>
					<td width="30%"> 
					     <div class="showNoFactCountry" style="display:none; margin-top:5px;">
							 <textarea id="noFactCountryShow" name="noFactCountryShow"  class="caseReadOnly" cols="60" rows="3" readonly></textarea>
							 <BR>
							 <input type="hidden" id="noFactCountry" name="noFactCountry" />
						 </div>
		            </td>
		          </tr>
				  
				  
		        </table>
				-->
				<BR>
				<th:block th:text="#{'l120m01d.itemDscrOMemo'}">※若額度明細表欄位「是否屬未核配額度國家」或「是否屬凍結額度國家」其中有一為是時才需輸入</th:block>
				<div class="badCountryContent">
					<fieldset>
						<legend>
							<b><th:block th:text="#{'l120m01d.itemDscrO'}">現況說明</th:block></b>
						</legend>
						<div style="width: 900px">
									<b class="star"><th:block th:text="#{'lms.ckeditRemark1'}">註1:建議字型16</th:block></b><br/>
									<b class="star"><th:block th:text="#{'lms.ckeditRemark2'}">註2:|←字型16時建議換行</th:block></b><br/>
									　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←
							<textarea class="ickeditor" id="itemDscrO" name="itemDscrO" ></textarea><br/>
									　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　|←
						</div>
					</fieldset>
				</div>
	      </fieldset>
		  
		</form>
		<!--<script type="text/javascript" src="pagejs/lns/LMS1205S04Page.js"></script>-->
		
		
	</th:block>
</body>
</html>
