/* 
 * LMS1200DOCServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.NGFlagHelper;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lms.pages.LMS1205V01Page;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.SheetSettings;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Colour;
import jxl.format.PageOrientation;
import jxl.format.PaperSize;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import tw.com.iisi.cap.dao.utils.AbstractSearchSetting;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產EXCEL Service
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/12/9,Miller Lin
 *          </ul>
 */
@Service("lms1200xlsservice")
public class LMS1200XLSServiceImpl extends AbstractFormHandler implements
		FileDownloadService {
	@Resource
	LMS1205Service service1205;

	@Resource
	LMS1405Service service1405;

	@Resource
	BranchService branch;

	@Resource
	UserInfoService userservice;

	@Resource
	CodeTypeService codeService;

	@Resource
	MisLNF022Service lnLnf022Service;

	@Resource
	LMSService lmsService;
	
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1200XLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {

		ByteArrayOutputStream baos = null;

		try {
			baos = this.genExcel_1(params);

			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}
	
	private ByteArrayOutputStream genExcel_1(PageParameters params) {
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1205V01Page.class);
		Properties popM01 = MessageBundleScriptCreator
			.getComponentResource(LMS1205M01Page.class);
		ByteArrayOutputStream baos = null;
		List<L120M01A> listMap = null;
		WritableFont font12 = null;
		WritableCellFormat format12Center = null;
		WritableCellFormat format12CenterNO = null;
		WritableCellFormat format12Left = null;
		WritableCellFormat format12Right = null;
		WritableCellFormat format12LeftNO = null;
		WritableCellFormat format12RightNO = null;

		Map<String, String> returnCodeMap = null;
		Map<String, String> agntNoCodeMap = null;
		Locale locale = null;

		try {
			baos = new ByteArrayOutputStream();
			WritableWorkbook book = Workbook.createWorkbook(baos);
			WritableSheet sheet = book.createSheet("查詢結果", 0);
			SheetSettings settings = sheet.getSettings();
			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			settings.setOrientation(PageOrientation.LANDSCAPE); // PageOrientation.LANDSCAPE
																// 橫向
			settings.setPaperSize(PaperSize.A4);
			// 縮放比例頁寬
			settings.setFitWidth(1);
			// 縮放比例頁高
			settings.setFitHeight(5000);
			// 設定字型與格式
			// other.msg60=新細明體
			font12 = new WritableFont(WritableFont.createFont(pop2
					.getProperty("other.msg60")), 12, WritableFont.NO_BOLD);
			format12Center = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE);
			format12Left = LMSUtil.setCellFormat(format12Left, font12,
					Alignment.LEFT);
			format12Right = LMSUtil.setCellFormat(format12Right, font12,
					Alignment.RIGHT);
			format12CenterNO = LMSUtil.setCellFormat(format12Center, font12,
					Alignment.CENTRE, false, false);
			format12LeftNO = LMSUtil.setCellFormat(format12LeftNO, font12,
					Alignment.LEFT, false, false);
			format12RightNO = LMSUtil.setCellFormat(format12RightNO, font12,
					Alignment.RIGHT, false, false);

			book.setColourRGB(Colour.BLUE2,0xB3, 0xCB, 0xE2);
			book.setColourRGB(Colour.YELLOW2,255, 242, 204);
			
			WritableCellFormat yellow = new WritableCellFormat(format12Left);// 單元格樣式
			yellow.setBackground(Colour.YELLOW2);	
			
            WritableCellFormat bule = new WritableCellFormat(format12Left);// 單元格樣式
            bule.setBackground(Colour.BLUE2);
            
            String ownBrId = MegaSSOSecurityContext.getUnitNo();
			String txCode = Util.trim(params.getString("txCode"));
			String docStatus = Util.trim(params.getString("mainDocStatus"));
			String typCd = Util.trim(params.getString("typCd"));
			String docType = Util.trim(params.getString("docType"));
			String docKind = Util.trim(params.getString("docKind"));
			String docCode = Util.trim(params.getString("docCode"));
			String custId = Util.trim(params.getString("custId"));
			String custName = Util.trim(params.getString("custName"));
			String updater = Util.trim(params.getString("updater"));
			String approveDateS = Util.nullToSpace(Util.trim(params
					.getString("approveDateS")));
			String approveDateE = Util.nullToSpace(Util.trim(params
					.getString("approveDateE")));
			String caseBrId = Util.nullToSpace(params.getString("caseBrId"));
			boolean isReject = params.getBoolean("isReject");
			
			CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
			.getEnum(docStatus);
			if (docStatusEnum == null) {
				docStatusEnum = CreditDocStatusEnum.海外_編製中;
			}
			ISearch pageSetting = new GridSearch();
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String unitType = user.getUnitType();

			List<String> l120m01a_maindId_list = lmsService
			.getL120M01AMainIdByFilterForm(params);
			if (Util.equals(custId, "") && Util.equals(custName, "")) {
				if (l120m01a_maindId_list.size() > 0) {
					pageSetting.addSearchModeParameters(SearchMode.IN, "mainId",
							l120m01a_maindId_list);
				}
			} else {
				// 篩選條件有統編、戶名時
				if (l120m01a_maindId_list.size() > 0) {

					if (Util.isNotEmpty(custId) && Util.isNotEmpty(custName)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN, "mainId",
										l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.AND,
										new SearchModeParameter(SearchMode.EQUALS,
												"custId", custId),
										new SearchModeParameter(SearchMode.LIKE,
												"custName", custName + "%")));
					} else {
						if (Util.isNotEmpty(custId)) {
							pageSetting.addSearchModeParameters(SearchMode.OR,
									new SearchModeParameter(SearchMode.IN,
											"mainId", l120m01a_maindId_list),
									new SearchModeParameter(SearchMode.EQUALS,
											"custId", custId));
						}
						if (Util.isNotEmpty(custName)) {
							pageSetting.addSearchModeParameters(SearchMode.OR,
									new SearchModeParameter(SearchMode.IN,
											"mainId", l120m01a_maindId_list),
									new SearchModeParameter(SearchMode.LIKE,
											"custName", custName + "%"));
						}
					}

				} else {
					if (Util.isNotEmpty(custId)) {
						pageSetting.addSearchModeParameters(SearchMode.EQUALS,
								"custId", custId);
					}
					if (Util.isNotEmpty(custName)) {
						pageSetting.addSearchModeParameters(SearchMode.LIKE,
								"custName", custName + "%");
					}

				}
			}
			if (Util.isNotEmpty(Util.trim(params.getString("custId")))) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
						Util.trim(params.getString("custId")));
			}
			Date fromDate = null;
			Date endDate = null;
			if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
				fromDate = Util.parseDate(Util.nullToSpace(params
						.getString("fromDate")));
			}
			if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
				endDate = Util.parseDate(Util.nullToSpace(params
						.getString("endDate") + " 23:59:59"));
			}

			if (fromDate != null && endDate != null) {
				Object[] reason = { fromDate, endDate };
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
						reason);
			}
			
			if (!Util.isEmpty(caseBrId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
						caseBrId);
			}
			
			if (!Util.isEmpty(ownBrId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
						ownBrId);
			}
			
			// 篩選婉卻/變更格式選項 Miller added at 2012/12/17
			if (isReject) {
				// 3婉卻變更格式
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docRslt",
						"3");
			}
			
			switch (docStatusEnum) {
			case 授管處_審查中:
				// 審查中在提授審會時不能不能出現此案件
				if (fromDate == null && endDate == null) {
					pageSetting
							.addSearchModeParameters(
									SearchMode.OR,
									new SearchModeParameter(
											SearchMode.OR,
											new SearchModeParameter(
													SearchMode.OR,
													new SearchModeParameter(
															SearchMode.OR,
															new SearchModeParameter(
																	SearchMode.OR,
																	new SearchModeParameter(
																			SearchMode.EQUALS,
																			"hqMeetFlag",
																			null),
																	new SearchModeParameter(
																			SearchMode.EQUALS,
																			"hqMeetFlag",
																			"A")),
															new SearchModeParameter(
																	SearchMode.EQUALS,
																	"hqMeetFlag", "B")),
													new SearchModeParameter(
															SearchMode.EQUALS,
															"hqMeetFlag", "C")),
											new SearchModeParameter(SearchMode.EQUALS,
													"hqMeetFlag", "")),
									new SearchModeParameter(SearchMode.EQUALS,
											"hqMeetFlag", "0"));
				}
				
				if(Util.equals(txCode, "339058")){
					// 限定只顯示異常通報案件
					pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
							UtilConstants.Casedoc.DocCode.異常通報);
				}
				else{
					pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
							docStatus);
				}
				break;
			default:
				String[] _docStatus = docStatus
						.split(UtilConstants.Mark.SPILT_MARK);
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						_docStatus);
				break;
			}
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"l120a01a.authUnit", user.getUnitNo());
			// 判定是否已註記被刪除
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
					null);
			if (!"S".equals(unitType) && !"A".equals(unitType)) {
				// 當非授管處或營運中心時
				// 限定只顯示海外授信案件
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
						UtilConstants.Casedoc.typCd.海外);
			}
			if (Util.isNotEmpty(docType)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
						docType);
			}
			if (Util.isNotEmpty(docKind)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
						docKind);
			}
			if (Util.isNotEmpty(docCode)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
						docCode);
			}
			if (Util.isNotEmpty(custName)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
						custName + "%");
			}
			if (Util.isNotEmpty(updater)) {

				if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"hqAppraiser", updater);
				} else if (UtilConstants.BankNo.中區營運中心.equals(user.getUnitNo())
						|| UtilConstants.BankNo.中部區域授信中心.equals(user.getUnitNo())
						|| UtilConstants.BankNo.北一區營運中心.equals(user.getUnitNo())
						|| UtilConstants.BankNo.北二區營運中心.equals(user.getUnitNo())
						|| UtilConstants.BankNo.南區營運中心.equals(user.getUnitNo())
						|| UtilConstants.BankNo.南部區域授信中心.equals(user.getUnitNo())
						|| UtilConstants.BankNo.桃竹苗區營運中心.equals(user.getUnitNo())) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"areaAppraiser", updater);
				} else {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"updater", updater);
				}

			}
			if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
				pageSetting.addSearchModeParameters(
						SearchMode.BETWEEN,
						"endDate",
						new Object[] { Util.parseDate(approveDateS),
								Util.parseDate(approveDateE) });
			}
			NGFlagHelper.addSearchParamsAT918(pageSetting, user);
			
			String execelTitle="Eloan案件簽報書";
			if(Util.equals(txCode, "339051")){
				execelTitle="稽催Eloan案件簽報書--審核中";
				// 當user使用經辦名稱等來排序時，因為不是唯一性，所以誇頁的排序可能會告成重覆資料，所以再加個oid來排
				pageSetting.addOrderBy("sendLastTime",false);
				pageSetting.addOrderBy("endDate",true);
				pageSetting.addOrderBy("caseNo",false);
				pageSetting.addOrderBy("oid",false);
			}
			else if(Util.equals(txCode, "339058")){
				execelTitle="稽催Eloan案件簽報書--異常通報";
				pageSetting.addOrderBy("sendLastTime",false);
				pageSetting.addOrderBy("caseDate",true);
				pageSetting.addOrderBy("caseNo",false);
				pageSetting.addOrderBy("oid",false);
			}
			
			Page<? extends GenericBean> page = service1205.findPage(L120M01A.class,
					pageSetting);
			listMap = (List<L120M01A>) page.getContent();

			sheet.mergeCells(0, 0, 9, 0);
			Label labelT1 = new Label(0, 0, execelTitle, format12CenterNO);
			sheet.addCell(labelT1);
			
			Label labelT2 = new Label(10, 0, "製表日:", format12CenterNO);
			sheet.addCell(labelT2);
			
			Label labelT3 = new Label(11, 0, Util.getDate(new Date()), format12CenterNO);
			sheet.addCell(labelT3);

			// 設定行寬
			sheet.setColumnView(0, 6);//項次
			sheet.setColumnView(1, 12);//分行經辦
			sheet.setColumnView(2, 20);//單位別
			sheet.setColumnView(3, 20);//放行日期
			sheet.setColumnView(4, 20);//簽案日期
			sheet.setColumnView(5, 20);//主要借款人統編
			sheet.setColumnView(6, 14);//主要借款人
			sheet.setColumnView(7, 20);//案號
			sheet.setColumnView(8, 20);//文件名稱
			sheet.setColumnView(9, 14);//狀態
			sheet.setColumnView(10, 20);//經辦簽註
			sheet.setColumnView(11, 20);//覆核簽章

			String[] title = { "項次","分行經辦","單位別","放行日期","簽案日期","主要借款人統編","主要借款人"
					,"案號","文件名稱","狀態","經辦簽註進度","覆核簽章"};
			for (int j = 0; j < title.length; j++) {
				Label labelT5 = null;
				labelT5 = new Label(j, 1, title[j], format12Center);
				sheet.addCell(labelT5);
			}
			Label labelContent = null;
			if (listMap.size()>0) {
				for (int i = 0, k = 2 , j = 1; i < listMap.size(); i++, k++, j++) {
					L120M01A model = listMap.get(i);
					
					// 項次
					labelContent = new Label(0, k, Integer.toString(j), format12Center);
					sheet.addCell(labelContent);
					
					//分行經辦
					String xlsHqAppraiser = !Util.isEmpty(userservice
							.getUserName(model.getHqAppraiser())) ? userservice
									.getUserName(model.getHqAppraiser()) : Util
									.trim(model.getHqAppraiser());
					labelContent = new Label(1, k, xlsHqAppraiser, format12Center);
					sheet.addCell(labelContent);
					
					//單位別
					String xlsCaseBrId = (Util.nullToSpace(model.getCaseBrId()))
					+ " "
					+ branch.getBranchName(Util.nullToSpace(model
							.getCaseBrId()));
					labelContent = new Label(2, k, xlsCaseBrId, format12Center);
					sheet.addCell(labelContent);
					
					// 放行日期
					String xlsSendLastTime=Util.getDate(model.getSendLastTime());
					labelContent = new Label(3, k, xlsSendLastTime, format12Center);
					sheet.addCell(labelContent);
					
					// 簽案日期
					String xlsCaseDate = Util.getDate(model.getCaseDate());
					labelContent = new Label(4, k, xlsCaseDate, format12Center);
					sheet.addCell(labelContent);

					// 主要借款人統編
					String xlsCustId = model.getCustId();
					labelContent = new Label(5, k, xlsCustId, format12Center);
					sheet.addCell(labelContent);
					
					// 主要借款人
					String xlsCustName = model.getCustName();
					labelContent = new Label(6, k, xlsCustName, format12Center);
					sheet.addCell(labelContent);
					
					//案號
					String xlsCaseNo = model.getCaseNo();
					labelContent = new Label(7, k, xlsCaseNo, format12Center);
					sheet.addCell(labelContent);
					
					//文件名稱
					StringBuffer xlsDocName = new StringBuffer();
					String xlsDocKind = this.getCaseType(model, popM01, xlsDocName);
					labelContent = new Label(8, k, xlsDocKind, format12Center);
					sheet.addCell(labelContent);
					
					//狀態
					CreditDocStatusEnum xlsdocStatusEnum = CreditDocStatusEnum
					.getEnum(model.getDocStatus());
					labelContent = new Label(9, k, xlsdocStatusEnum.name().replace("授管處_", "").replace("海外_", ""), format12Center);
					sheet.addCell(labelContent);
					
					//經辦簽註
					labelContent = new Label(10, k, "", format12Center);
					sheet.addCell(labelContent);
					
					//覆核簽章
					labelContent = new Label(11, k, "", format12Center);
					sheet.addCell(labelContent);
				}
				sheet.mergeCells(4, listMap.size()+1+2, 6, listMap.size()+1+2);
				labelContent = new Label(4, listMap.size()+1+2, "製表：",format12Left);
				sheet.addCell(labelContent);
				
				sheet.mergeCells(7, listMap.size()+1+2, 9, listMap.size()+1+2);
				labelContent = new Label(7, listMap.size()+1+2, "覆核：",format12Left);
				sheet.addCell(labelContent);
				
				sheet.mergeCells(10, listMap.size()+1+2, 11, listMap.size()+1+2);
				labelContent = new Label(10, listMap.size()+1+2, "單位主管：",format12Left);
				sheet.addCell(labelContent);
			}
			book.write();
			book.close();

			//return null;
			return baos;
		} catch (Exception ex) {
			LOGGER.error("[genExcel_1] Exception!!", ex.getMessage());
		} finally {

			if (returnCodeMap != null) {
				returnCodeMap.clear();
			}

			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[genExcel_1] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}
	
	private class GridSearch extends AbstractSearchSetting {

		private static final long serialVersionUID = 1L;

	}
	
	/**
	 * 取得 案件類別名稱
	 * 
	 * @param model
	 *            簽報書主檔
	 * @param pop
	 *            語系檔
	 * @param temp
	 *            暫存的stringBuffer
	 * 
	 * @return
	 */
	private String getCaseType(L120M01A model, Properties pop, StringBuffer temp) {
		temp.setLength(0);
		String areaTitle = null;
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (UtilConstants.Casedoc.DocType.企金.equals(model.getDocType())) {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				// L1205G.grid1=企金營運中心授權內
				// L1205G.grid1a=企金分行授權內
				if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(Util
						.trim(model.getAuthLvl()))) {
					temp.append(pop.getProperty("L1205G.grid1"));
				} else {
					if (UtilConstants.Casedoc.AuthLvl.總行授權內.equals(Util
							.trim(model.getAuthLvl()))) {
						// 企金總行授權內
						temp.append(pop.getProperty("L1205G.grid14"));

					} else {

						temp.append(pop.getProperty("L1205G.grid1a"));
					}

				}
			} else {
				areaTitle = queryAreaTitle(model);
				if (Util.isNotEmpty(areaTitle)) {
					// l120m01a.title0a=企金
					temp.append(pop.getProperty("l120m01a.title0a")).append(
							areaTitle);
				} else {
					temp.append(pop.getProperty("L1205G.grid2"));
				}
			}
		} else {
			if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {
				// L1205G.grid12=個金營運中心授權內
				// L1205G.grid12a=個金分行授權內
				if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(Util
						.trim(model.getAuthLvl()))) {
					temp.append(pop.getProperty("L1205G.grid12"));
				} else {

					if (UtilConstants.Casedoc.AuthLvl.總行授權內.equals(Util
							.trim(model.getAuthLvl()))) {
						// 個金總行授權內
						temp.append(pop.getProperty("L1205G.grid15"));
					} else {
						temp.append(pop.getProperty("L1205G.grid12a"));
					}

				}
			} else {
				areaTitle = queryAreaTitle(model);
				if (Util.isNotEmpty(areaTitle)) {
					// l120m01a.title0b=個金
					temp.append(pop.getProperty("l120m01a.title0b")).append(
							areaTitle);
				} else {
					temp.append(pop.getProperty("L1205G.grid13"));
				}
			}
		}
		// L1205G.grid9=一般
		// L1205G.grid10=其他
		// L1205G.grid11=陳復/陳述案
		temp.append("(");
		if (UtilConstants.Casedoc.DocCode.一般.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid9"));
		} else if (UtilConstants.Casedoc.DocCode.其他.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid10"));
		} else if (UtilConstants.Casedoc.DocCode.異常通報.equals(Util.trim(model
				.getDocCode()))) {
			// other.msg59=異常通報案件
			temp.append(pop2.getProperty("other.msg59"));
		} else if (UtilConstants.Casedoc.DocCode.團貸案件.equals(Util.trim(model
				.getDocCode()))) {
			// other.msg134=團貸
			temp.append(pop2.getProperty("other.msg134"));
		} else {
			temp.append(pop.getProperty("L1205G.grid11"));
		}
		temp.append(")");
		return temp.toString();
	}
	
	/**
	 * 取得國內屬營運中心制分行的標題名稱
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return
	 * @throws CapException
	 */
	private String queryAreaTitle(L120M01A l120m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch tBranch = branch.getBranch((l120m01a != null) ? Util
				.trim(l120m01a.getCaseBrId()) : user.getUnitNo());
		String docKind = Util.trim(l120m01a.getDocKind());
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (tBranch != null) {
			String brnGroup = Util.trim(tBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				if (UtilConstants.Casedoc.DocKind.授權外.equals(docKind)) {
					/*
					 * 因為海外分行不屬於營運中心制，所以提醒第四階段，國內屬營運中心制分行時TITLE顯示會有差異
					 * 國內營運中心制分行，分行授權外案件會顯示營運中心授權外案件簽報書
					 */
					// other.msg131=營運中心授權外
					return pop.getProperty("other.msg131");
				}
			}
		}
		return null;
	}
}
