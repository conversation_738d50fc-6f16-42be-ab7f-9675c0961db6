var tabBorderColor = "#AAA";
//overwrite tabs
;(function($){
    require(['jqueryui'], function(){
      if ($.fn.tabs) {
        var pageIcon = "<div class=\"scroll-icon\"><div class=\"tab-next\"><span class=\"ui-icon ui-icon-circle-triangle-e\"></span></div><div class=\"tab-prev\"><span class=\"ui-icon ui-icon-circle-triangle-w\"></span></div></div>";
        var _tabs = $.fn.tabs;
        !$(".tabBorderColor").length && $("<div class=\"tabBorderColor\" />").appendTo("body");
          $.fn.tabs = function() {
            var args = arguments;
            this.each(function() {
              var $this = $(this);
              if (!$this.find("ul:first").is(".ui-tabs-nav")) {

                _tabs.apply($this.find("ul:first").css({
                // width: $(this).width()
                // $(this).outerWidth() || 600//,"padding-left":20,"padding-right":20
                }).wrap("<div class='tabs-warp'/>").wrap("<div class=\"tabs-scroll-warp\" style=\"overflow: hidden; position: relative;\" />").end(), args);

                $this.on("fitsize.tab", fn.tabFitSize).trigger("fitsize.tab");
                $this.on('tabsbeforeactivate', function(event, ui) {
					
				  // UPGRADETODO TEST 先執行beforeActivate邏輯
				  var tabsInstance = $this.data("ui-tabs");
				  if (tabsInstance && typeof tabsInstance.options.beforeActivate === "function") {
				     tabsInstance.options.beforeActivate.call($this[0], event, ui);
				  }
					
                  // #359 修正快速切換頁籤時，發生空白錯誤
                  var hoverli = $(this).find("ul > li.ui-state-hover");
                  if(hoverli.length && !$(this).find("ul > li.ui-state-focus").length){
                    hoverli = hoverli.addClass("ui-state-focus");
                  }
                  
                  if ($(this).find("ul > li.ui-state-focus > a ").attr("goto")) {
                    $.blockUI({
                      fadeIn : 0,
                      fadeOut : 0,
                      message : i18n.def.tabchange + '  <img src="' + webroot + '/img/ajax-loader.gif"/>'
                    });
                    event.preventDefault();
                    setCloseConfirm(false);
                    var dfd = $.Deferred();
                    dfd.done(function(json) {
                      Properties.window.isChangeTab = true;
                      var tData = $.extend({}, responseJSON || {}, {
                        mainOid : $("#mainOid").val(),
                        mainDocStatus : $("#mainDocStatus").val(),
                        mainId : $("#mainId").val()
                      });
                      delete tData.page;delete tData._pa;delete tData.formAction;delete tData.Auth;
                      $.form.submit({
                        url : ui.newTab.find("a").attr("goto"),
                        data : tData
                      });
                    });
                    if (!window._ignoreTempSave && window.tempSave && window.tempSave.handler) {
                      if ((window.tempSave.beforeCheck ? window.tempSave.beforeCheck() : true)) {
                        if (window.tempSave.asyncBeforeCheck) {
                          window.tempSave.asyncBeforeCheck().done(function(value) {
                            if (value) {
                              if (!checkRequiredSave()) {
                                $.unblockUI();
                                return false;
                              }
                              window.tempSave(dfd, event);
                            } else {
                              $.unblockUI();
                              return false;
                            }
                          })
                        } else {
                          if (!checkRequiredSave()) {
                            $.unblockUI();
                            return false;
                          }
                          window.tempSave(dfd, event);
                        }
                      } else {
                        $.unblockUI();
                        return false;
                      }
                    } else {
                      dfd.resolve();
                    }
                  }
                });

                // add title
                $this.find("ul:first > li > a ").each(function() {
                  var $$this = $(this);
                  !$$this.attr("title") && $$this.attr("title", trimStr($$this.text()));
                  $$this = null;
                });
                /**
                 * <pre>
                 * tab 行為 (index n 切到 m，第一個 tab n = 0)
                 * 情境一：n -> m, m != 0
                 * a click -> tabs css 生效 -> tabs beforeactivate event (new: m, old: n) -> tabs activate event
                 * -> li click -> (載入畫面，先切回 0、再切到 m) -> tabs beforeactivate event (new: m, old: 0) -> tabs activate event
                 *
                 * 情境二：n -> m, m = 0
                 * a click -> tabs css 生效 -> tabs beforeactivate event (new: 0, old: n) -> tabs activate event
                 * -> li click -> (載入畫面，切到 0)
                 *
                 * 情境三：n -> m, n = m != 0
                 * a click -> li click -> (載入畫面，先切回 0、再切到 m) -> tabs beforeactivate event (new: m, old: 0) -> tabs activate event
                 *
                 * 情境四：n -> m, n = m = 0
                 * a click -> li click
                 *
                 * 依據上述四個情境，歸納幾點：
                 * 1. 若在 li click 控管，此時所有 tabs 相關資訊都已變動，無法透過任何 css 或 attr 判斷是否在原本的 tab
                 * 2. 若在 beforeactivate 控管，情境三、四不會先經過此 event，會直接觸發 li click 裡的載入頁面行為
                 * 3. 嘗試 unbind a click event 讓 li click 變成第一個觸發點，會造成事先已經載入內容的 tab 無法正常呈現
                 * 4. 使用 responseJSON.page vs. css 判斷是否是相同 tab
                 * 5. 若在 li click 使用 4 控管，會導致 li click 無法切換 tab
                 * 6. 若在 a click 使用 4 控管，此作法無法避免直接點 li 造成的重新載入問題
                 * 7. 5 & 6 只能擇一。選擇 6，行為與現行系統相同
                 * </pre>
                 */

                if (window.hasOwnProperty('responseJSON') && responseJSON.page) { // 若是 FORM 表單的頁籤的話, 應該有 responseJSON 物件, 才有需要檢查是否為同一頁
                  $this.find("ul:first > li > a ").click(function(e) {
                    var ul = $("body").find(".tabs:first  ul:first"), current_tab = ul.find("li > a[goto=" + responseJSON.page + "]").parent();
                    var orig = current_tab.attr('aria-controls'); // 根據 responseJSON 判斷原本的 tab，參考 mega.eloan.properties 作法
                    var dest = $("ul> .ui-tabs-active").attr('aria-controls'); // 切換後的 tab
                    if (orig == dest) {
                      e.preventDefault();
                      e.stopPropagation();
                    }
                  });
                }

                // change css
                $this.removeClass("ui-widget-content ui-corner-all").find("ul:first").removeClass("ui-widget-content ui-widget-header ui-corner-all").end().find(".tabCtx-warp").addClass(
                    "ui-widget-content");
                // corner css
                $this.find(".ui-corner-top").corner("cc:#FFF top 6px");
                // "ul:first > li" 相關行為整合至 tabsbeforeactivate 處理
                // $this.find("ul:first > li").click(function(e){
                // var $this = $(this);
                // if($(e.target).is("div")) {
                // return false;
                // }
                // if ($(this).find("a").attr("goto")) {
                // $.blockUI({
                // fadeIn: 0,
                // fadeOut: 0,
                // message: i18n.def.tabchange + ' <img src="'+webroot+'/img/ajax-loader.gif"/>'
                // });
                // e.preventDefault();
                // setCloseConfirm(false);
                // setTimeout(function(){
                // var tData = $.extend({}, responseJSON || {}, {
                // mainOid: $("#mainOid").val() || responseJSON.mainOid,
                // mainDocStatus: $("#mainDocStatus").val() || responseJSON.mainDocStatus,
                // mainId: $("#mainId").val() || responseJSON.mainId
                // });
                // delete tData.page;delete tData._pa;delete tData.formAction;delete tData.Auth;
                // $.form.submit({
                // url: $this.find("a").attr("goto"),
                // data: tData
                // });
                // if (!getIgonreTempSave() && window.tempSave && window.tempSave.handler) {
                // if ((window.tempSave.beforeCheck ? window.tempSave.beforeCheck() : true)) {
                // if (window.tempSave.asyncBeforeCheck) {
                // window.tempSave.asyncBeforeCheck().done(function(value){
                // if (value) {
                // if (!checkRequiredSave()) {
                // $.unblockUI();
                // return;
                // }
                // // window.tempSave(dfd);
                // }
                // else {
                // $.unblockUI();
                // return;
                // }
                // })
                // } else {
                // if (!checkRequiredSave()) {
                // $.unblockUI();
                // return;
                // }
                // // window.tempSave(dfd);
                // }
                // }
                // else {
                // $.unblockUI();
                // }
                // return false;
                // }
                // else {
                // dfd.resolve();
                // }
                // },50);
                // return false;
                // }
                // });
              } else {
                _tabs.apply($this, args);
                if (args[1] && args[1] == 'active') {
                  fn.scrollToTab.call($this);
                }
              }
            });
            return this;
          }
        
        var fn = {
            tabFitSize: function(){
                if ($(this).is(":visible")) {
          
                    var $this = $(this), $div;
                    if ($this.is(".tabs")) {
                        $div = $this;
                        $this = $div.find("ul:first");
                    }
                    else if ($this.is("ul")) {
                        $div = $this.closest(".tabs");
                    }
                    else {
                        return;
                    }
                    // fix firefox fitwidth
                    var blockWidth = $this.parent().css("marginRight",0).width();
                    var a = 0;
                    $this.removeClass("ui-tab-fit").children("li").each(function(){
                        a += ($(this).width() + 6);
                    });
                    $this.width(a < blockWidth ? blockWidth : a);
                    $this.parent().width(blockWidth);
                    if (a > blockWidth && !$div.find(".scroll-icon").length) {
                        $div.find(".tabs-scroll-warp:first").css({
                            "marginRight": 40,
                            width: blockWidth - 40
                        }).end().find(".tabs-warp:first").append(pageIcon).children(".scroll-icon").find(".tab-next span").click(fn.nextTab.bind($div.find(".tabs-scroll-warp:first"))).end().find(".tab-prev span").click(fn.prevTab.bind($div.find(".tabs-scroll-warp:first")));
                    }
                }
                return $(this);
            },
            changeTab: function(prev){
                prev = !!prev;
                try {
                    var index = this.find('li.ui-tabs-active').prevAll().length + ((prev) ? -1 : 1);
                    if (prev && index >= 0 || !prev) {
                        var li = this.find('li').eq(index);
                        if (li.length) {
                            li.find("a[goto]").length && li.click() || this.tabs('active', index);
                        }
                    }
                } 
                catch (e) {
                    ilog.debug(e);
                }
            },
            nextTab: function(){
                var left = this.prop("scrollLeft") + 500;
                this.scrollTo(left, 100, {
                    'axis': 'x',
                    'margin': true,
                    'easing': 'swing'
                });
                //changeTab.call(this, false)
            },
            prevTab: function(){
                var left = this.prop("scrollLeft") - 500;
                this.scrollTo(left < 0 ? 0 : left, 100, {
                    'axis': 'x',
                    'margin': true,
                    'easing': 'swing'
                });
                //changeTab.call(this, true)
            },
            scrollToTab: function(){
                var obj = this.find(".tabs-scroll-warp");
                obj.scrollTo(this.find('li.ui-tabs-active'), 500, {
                    'axis': 'x',
                    'margin': true,
                    'easing': 'swing',
                    offset: {
                        left: (this.width() - this.find('li.ui-tabs-active').width() - 100) * -1
                    }
                });
            }
        };
        
    }
    });
})(jQuery);

