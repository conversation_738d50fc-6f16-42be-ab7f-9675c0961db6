/* 
 * L140S07A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 敘作條件異動資訊 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S07A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140S07A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 順序 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SEQNUM", columnDefinition="DECIMAL(3,0)")
	private Integer seqNum;

	/** 項目 **/
//	@Lob
//	@Basic(fetch = FetchType.LAZY)
//	@Column(name="ITEM", columnDefinition="CLOB")
//	private String item;
	@Column(name = "ITEM", length = 50, columnDefinition = "VARCHAR(50)")
	private String item;

	/** 變更前內容 **/
	@Column(name = "BEFTEXT", length = 300, columnDefinition = "VARCHAR(300)")
	private String befText;

	/** 變更後內容 **/
	@Column(name = "AFTTEXT", length = 300, columnDefinition = "VARCHAR(300)")
	private String aftText;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得順序 **/
	public Integer getSeqNum() {
		return this.seqNum;
	}
	/** 設定順序 **/
	public void setSeqNum(Integer value) {
		this.seqNum = value;
	}

	/** 取得項目 **/
	public String getItem() {
		return this.item;
	}
	/** 設定項目 **/
	public void setItem(String value) {
		this.item = value;
	}

	/** 取得變更前內容 **/
	public String getBefText() {
		return this.befText;
	}
	/** 設定變更前內容 **/
	public void setBefText(String value) {
		this.befText = value;
	}

	/** 取得變更後內容 **/
	public String getAftText() {
		return this.aftText;
	}
	/** 設定變更後內容 **/
	public void setAftText(String value) {
		this.aftText = value;
	}
}
