package com.mega.eloan.lms.mfaloan.bean;

import org.apache.wicket.markup.html.form.Check;
import org.springframework.jdbc.core.RowMapper;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.utils.CapBeanUtil;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;

/** ESG綠色企業資料 **/
public class ELF606 extends GenericBean {

	private static final long serialVersionUID = 1L;
	
	/** 證券代號 **/
	@Column(name = "ELF606_stkNo", length = 10, columnDefinition = "CHAR(6)", nullable=false,unique = true)
	private String elf606_StkNo;

	/** 收件日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF606_receiveDate", columnDefinition="DATE", nullable=false,unique = true)
	private Date elf606_ReceiveDate;

	/** 資料來源 **/
	@Column(name="ELF606_source", length=30, columnDefinition="CHAR(30)", nullable=false,unique = true)
	private String elf606_Source;

	/** 證券統編 **/
	@Column(name="ELF606_stkCMP", length=10, columnDefinition="CHAR(10)", nullable=false)
	private String elf606_StkCMP;

	/** 公司名稱 **/
	@Column(name="ELF606_stkCMPNM", length=100, columnDefinition="CHAR(100)")
	private String elf606_StkCMPNM;

	/** Sustainalytics ESG 風險評分(100-0, 0分最佳) **/
	@Column(name="ELF606_esgScore", columnDefinition="DECIMAL(6,2)")
	private BigDecimal elf606_EsgScore;

	/** MSCI ESG 評級 (0-5, 5級最佳)  **/
	@Column(name="ELF606_msciLevel", columnDefinition="CHAR(5)")
	private String elf606_MsciLevel;

	/** FTSE Russell ESG 評級 (0-5, 5級最佳)  **/
	@Column(name="ELF606_esgLevel", columnDefinition="DECIMAL(6,2)")
	private BigDecimal elf606_EsgLevel;

	/** ISS 環境揭露評級 (1-10, 1級最佳)  **/
	@Column(name="ELF606_issLevel1", columnDefinition="DECIMAL(6,2)")
	private BigDecimal elf606_IssLevel1;

	/** ISS 社會揭露評級 (1-10, 1級最佳) **/
	@Column(name="ELF606_issLevel2", columnDefinition="DECIMAL(6,2)")
	private BigDecimal elf606_IssLevel2;

	/** 台灣公司治理評鑑  (前5%最佳)  **/
	@Column(name="ELF606_governance", columnDefinition="CHAR(100)")
	private String elf606_Governance;

	/**
	 * 刪除註記日期
	 */
	@Column(name="ELF606_deletedTime",columnDefinition = "TIMESTAMP")
	private Timestamp elf606_DeletedTime;

	/** 建立人員號碼 **/
	@Column(name="ELF606_creator", length=6, columnDefinition="CHAR(6)")
	private String elf606_Creator;

	/** 建立日期 **/
	@Column(name="ELF606_createTime",columnDefinition = "TIMESTAMP")
	private Timestamp elf606_CreateTime;

	/** ISS ESG評級 (A-D，A最佳)  **/
	@Column(name="ELF606_issLevel", columnDefinition="CHAR(5)")
	private String elf606_IssLevel;

	/** S&P Global ESG評分 **/
	@Column(name="ELF606_spScore", columnDefinition="DECIMAL(6,2)")
	private BigDecimal elf606_SpScore;

	/** Moody's ESG評分(0-100，100分最佳) **/
	@Column(name="ELF606_moody", columnDefinition="DECIMAL(6,2)")
	private BigDecimal elf606_Moody;

	/** SinoPac+ ESG 評等 (A+~C, A+最佳)**/
	@Column(name="ELF606_sinoPac", columnDefinition="CHAR(5)")
	private String elf606_SinoPac;

	/** 台灣永續評鑑 (AAA~D, AAA最佳)  **/
	@Column(name="ELF606_sustainability", columnDefinition="CHAR(5)")
	private String elf606_Sustainability;

	public String getElf606_StkNo() {
		return this.elf606_StkNo;
	}
	public void setElf606_StkNo(String value) {
		this.elf606_StkNo = value;
	}

	public String getElf606_Source() {
		return this.elf606_Source;
	}
	public void setElf606_Source(String value) {
		this.elf606_Source = value;
	}

	public String getElf606_StkCMP() {
		return this.elf606_StkCMP;
	}
	public void setElf606_StkCMP(String value) {
		this.elf606_StkCMP = value;
	}

	public String getElf606_StkCMPNM() {
		return this.elf606_StkCMPNM;
	}
	public void setElf606_StkCMPNM(String value) {
		this.elf606_StkCMPNM = value;
	}

	public BigDecimal getElf606_EsgScore() {
		return this.elf606_EsgScore;
	}
	public void setElf606_EsgScore(BigDecimal value) {
		this.elf606_EsgScore = value;
	}

	public String getElf606_MsciLevel() {
		return this.elf606_MsciLevel;
	}
	public void setElf606_MsciLevel(String value) {
		this.elf606_MsciLevel = value;
	}

	public BigDecimal getElf606_EsgLevel() {
		return this.elf606_EsgLevel;
	}
	public void setElf606_EsgLevel(BigDecimal value) {
		this.elf606_EsgLevel = value;
	}

	public BigDecimal getElf606_IssLevel1() {
		return this.elf606_IssLevel1;
	}
	public void setElf606_IssLevel1(BigDecimal value) {
		this.elf606_IssLevel1 = value;
	}

	public BigDecimal getElf606_IssLevel2() {
		return this.elf606_IssLevel2;
	}
	public void setElf606_IssLevel2(BigDecimal value) {
		this.elf606_IssLevel2 = value;
	}

	public String getElf606_Governance() {
		return this.elf606_Governance;
	}
	public void setElf606_Governance(String value) {
		this.elf606_Governance = value;
	}

	/** 取得收件日期 **/
	public Date getElf606_ReceiveDate() {
		return this.elf606_ReceiveDate;
	}
	/** 設定收件日期 **/
	public void setElf606_ReceiveDate(Date value) {
		this.elf606_ReceiveDate = value;
	}

	public Timestamp getElf606_DeletedTime() {
		return this.elf606_DeletedTime;
	}
	public void setElf606_DeletedTime(Timestamp value) {
		this.elf606_DeletedTime = value;
	}

	/** 取得建立人員號碼 **/
	public String getElf606_Creator() {
		return this.elf606_Creator;
	}
	/** 設定建立人員號碼 **/
	public void setElf606_Creator(String value) {
		this.elf606_Creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getElf606_CreateTime() {
		return this.elf606_CreateTime;
	}
	/** 設定建立日期 **/
	public void setElf606_CreateTime(Timestamp value) {
		this.elf606_CreateTime = value;
	}

	public String getElf606_IssLevel() {
		return this.elf606_IssLevel;
	}
	public void setElf606_IssLevel(String value) {
		this.elf606_IssLevel = value;
	}

	public BigDecimal getElf606_SpScore() {
		return this.elf606_SpScore;
	}
	public void setElf606_SpScore(BigDecimal value) {
		this.elf606_SpScore = value;
	}

	public BigDecimal getElf606_Moody() {
		return this.elf606_Moody;
	}
	public void setElf606_Moody(BigDecimal value) {
		this.elf606_Moody = value;
	}

	public String getElf606_SinoPac() {
		return elf606_SinoPac;
	}
	public void setElf606_SinoPac(String elf606_SinoPac) {
		this.elf606_SinoPac = elf606_SinoPac;
	}

	public String getElf606_Sustainability() {
		return elf606_Sustainability;
	}
	public void setElf606_Sustainability(String elf606_Sustainability) {
		this.elf606_Sustainability = elf606_Sustainability;
	}
}
