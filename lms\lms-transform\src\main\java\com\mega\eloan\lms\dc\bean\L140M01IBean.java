package com.mega.eloan.lms.dc.bean;

import java.io.Serializable;

import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

public class L140M01IBean extends BaseBean implements Serializable {

	private static final long serialVersionUID = -6085210798568744508L;

	private String type = "";// 連保人類型
	private String rId = "";// 連保人統編
	private String rDupNo = "";// 連保人統編重覆碼
	private String rName = "";// 連保人名稱
	private String rKindM = "";// 關係類別
	private String rKindD = "";// 關係類別細項
	private String rCountry = "";// 國別

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getrId() {
		return rId;
	}

	public void setrId(String rId) {
		this.rId = rId;
	}

	public String getrDupNo() {
		return rDupNo;
	}

	public void setrDupNo(String rDupNo) {
		this.rDupNo = rDupNo;
	}

	public String getrName() {
		return rName;
	}

	public void setrName(String rName) {
		this.rName = rName;
	}

	public String getrKindM() {
		return rKindM;
	}

	public void setrKindM(String rKindM) {
		this.rKindM = rKindM;
	}

	public String getrKindD() {
		return rKindD;
	}

	public void setrKindD(String rKindD) {
		this.rKindD = rKindD;
	}

	public String getrCountry() {
		return rCountry;
	}

	public void setrCountry(String rCountry) {
		this.rCountry = rCountry;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append(Util.nullToSpace(this.getOid()))
				.append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getMainId()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getType()).trim()).append(
				TextDefine.FILE_DELIM);
		// 2013-02-20:rId不可trim否則8個空白會被刪除
		sb.append(this.getrId()).append(TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getrDupNo()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getrName()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getrKindM()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getrKindD()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(Util.nullToSpace(this.getrCountry()).trim()).append(
				TextDefine.FILE_DELIM);
		sb.append(this.getCreator()).append(TextDefine.FILE_DELIM);
		sb.append(this.getCreateTime()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdater()).append(TextDefine.FILE_DELIM);
		sb.append(this.getUpdateTime());
		return sb.toString();
	}

}
