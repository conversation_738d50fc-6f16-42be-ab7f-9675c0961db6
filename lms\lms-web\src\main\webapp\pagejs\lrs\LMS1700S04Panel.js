var initDfd = initDfd || new $.Deferred();
var initAll = initAll || new $.Deferred();

initDfd.done(function(json){
	build_l170m01d(json);	
	
	//在 build 完頁面後,若 M01 是 lockDoc, 也跟著 lock  
	if(json['initControl_lockDoc']){
		$("#tabForm").lockDoc();
	}

	//===================================================
	$("#btn_l170m01d_defaultVal").click(function(){
		// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
		if (json.ctlType == "C") {
			$.ajax({
				type: "POST",
				handler: _handler,
				data: $.extend({
					formAction: "l170m01d_defaultVal",
					mainOid: $("#mainOid").val()
				}, json),
			}).done(function(responseData){
				var tabForm = $("#tabForm");

				var map = responseData.defVal;
				$.each(map, function(def_key, def_val){
					//因為「前次覆審有無應行改善事項？」會影響欄位 hide/show
					//所以後面加上 trigger('change')
					$("[name=_chkResult_" + encodeURI(def_key) + "][value=" + def_val + "]").attr("checked", "checked").trigger('change');
				});

				if (responseData.msg) {
					API.showMessage(responseData.msg);
				}
			});
		}
		else {
			defVal_hasCMS().done(function(json){
				$.ajax({
					type: "POST",
					handler: _handler,
					data: $.extend({
						formAction: "l170m01d_defaultVal",
						mainOid: $("#mainOid").val()
					}, json),
				}).done(function(responseData){
					var tabForm = $("#tabForm");

					var map = responseData.defVal;
					$.each(map, function(def_key, def_val){
						//因為「前次覆審有無應行改善事項？」會影響欄位 hide/show
						//所以後面加上 trigger('change')
						$("[name=_chkResult_" + encodeURI(def_key) + "][value=" + def_val + "]").attr("checked", "checked").trigger('change');
					});

					if (responseData.msg) {
						API.showMessage(responseData.msg);
					}
				});
			});
		}		
	});	
	//===================================================
	function defVal_hasCMS(){
		var my_dfd = $.Deferred();    	
		var _id = "_div_defVal_hasCMS";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("  <p>是否有擔保品？</u>");
			dyna.push("	 <p><label><input type='radio' name='decisionExpr' value='Y' class='required' />"+i18n.lms1700m01["label.Y2"]+"</label></p>");
			dyna.push("	 <p><label><input type='radio' name='decisionExpr' value='N' class='required' />"+i18n.lms1700m01["label.N2"]+"</label></p>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: '',
	        width: 280, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    
                    $.thickbox.close();
                    
                    my_dfd.resolve({'hasCMS':val});
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
    	
    	    		
    	return my_dfd.promise();
	}
	//===================================================
	// J-111-0560 配合授信審查處，Web-eloan授信管理系統，覆審作業聯徵資料PPA已查詢部份,增加一鍵查詢功能，自動比對債票信及卡信資料
	$("#btn_getEjcicReusltRecord").click(function(){
		$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "getEjcicReusltRecord",
               mainOid: $("#mainOid").val()
           },
	   }).done(function(responseData){
		   var tabForm = $("#tabForm");
		   tabForm.injectData(responseData);
	   })
	});
	//===================================================
	function build_l170m01d(json){
		{
			var dyna = [];		
			
			$.each(['A', 'B', 'C', 'D'], function(idx_itemType, itemType) {
				var itemTypeDesc = json.l170m01d_title[itemType];
				var arr = json.l170m01d_list[itemType];
				var l170m01d_chkText_maxlength = json.l170m01d_chkText_maxlength; 
				var	l170m01d_chkText_maxlengthC = json.l170m01d_chkText_maxlengthC;
				if(arr && arr.length>0){
					var atFirst = true;
					//每一個 itemType 包含的項目
					$.each(arr, function(idx, jsonItem) {
						//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
						var is_z_html_loc = false;
						var is_y_html_loc = false;
						var is_x_html_loc = false;
						if(jsonItem._z_html_loc=="Y"){
							is_z_html_loc = true;
						}
						if(jsonItem._y_html_loc=="Y"){
							is_y_html_loc = true;
						}
						if(jsonItem._x_html_loc=="Y"){
							is_x_html_loc = true;
						}
						
						//不要只取 oid, 可能和 responseJSON 內的值重複
						var is_html_loc = "";
						if(is_z_html_loc){
							is_html_loc = "\' class=\"z_html_loc\" \'";
						}else if(is_y_html_loc){
							is_html_loc = "\' class=\"y_html_loc\" \'";
						}else if(is_x_html_loc){
							is_html_loc = "\' class=\"x_html_loc\" \'";	
						}else{
							is_html_loc = '';
						}
						
//						dyna.push("<tr style='vertical-align:top' l170m01d_oid='l170m01d_"+jsonItem.oid+"' l170m01d_itemSeq='"+jsonItem.itemSeq+"'"
//								+(is_z_html_loc?' class=\"z_html_loc\" ':'')+ " >");
								
						
								
						if(atFirst==true){
							
							var _rowSpan = arr.length;
							// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
							if(itemType=="B" && json.ctlType != "C"){
								//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
								_rowSpan = arr.length+json.l170m01d_list['Z'].length+json.l170m01d_list['Y'].length+json.l170m01d_list['X'].length;
							}
							//dyna.push("   <td rowspan='"+_rowSpan+"'>"+(to_vertical_str(itemTypeDesc))+"</td>");
							dyna.push("<tr class='hd2'><td colspan='4' nowrap align='center'>"+(to_horizontal_str(itemTypeDesc))+"</td></tr>");
							atFirst = false;
						}
						
						dyna.push("<tr style='vertical-align:top' l170m01d_oid='l170m01d_"+jsonItem.oid+"' l170m01d_itemSeq='"+jsonItem.itemSeq+"'"
						+  is_html_loc  + " >");

						// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
						// J-107-0290_09301_B1001 CTLTYPE_主辦覆審 第18項拆項 N025 
						//J-108-0888_05097_B1001
						// 須同步更新 RetrialServiceImpl.java function getItemSeqRight(
						if (json.ctlType == "A" && json.rptId >= "Ver20190701") { 
							
							if(jsonItem.itemSeqShow == ""){ 
								dyna.push("   <td align='right'>&nbsp;&nbsp;&nbsp;&nbsp;<div class='item' style='display:none'>" + jsonItem.itemNo + ":" + jsonItem.chkResult + "</div></td>"); 
							}else if( jsonItem.itemNo =="N029" || jsonItem.itemNo =="N031" ){ 
								// 第18項拆項 N025 ->N029  第12項拆項 N012->N031 
//								N025 辦理應收帳款承購是否依規定或核定條件辦理？
//								N029 應收帳款承購買方之付款是否無逾期30日以上之情形？
//
//								N012 工程預付款及/或履約保證其工程進度及履約情形是否正常
//								N031 XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
								
								dyna.push("   <td align='right'>&nbsp;&nbsp;&nbsp;&nbsp;<div class='item' style='display:none'>" + jsonItem.itemNo + ":" + jsonItem.chkResult + "</div></td>"); 	
								
							}else{
								dyna.push("   <td align='right' nowrap>&nbsp;&nbsp;" + (jsonItem.itemSeqShow) + "&nbsp;&nbsp;<div class='item' style='display:none'>" + jsonItem.itemNo + ":" + jsonItem.chkResult + "</div></td>");
							}
							 
						} else if (json.ctlType == "A" && json.rptId >= "Ver20190201") { 
							if(jsonItem.itemSeq == 19){ 
								dyna.push("   <td align='right'>&nbsp;&nbsp;&nbsp;&nbsp;<div class='item' style='display:none'>" + jsonItem.itemNo + ":" + jsonItem.chkResult + "</div></td>"); 
							} else if(jsonItem.itemSeq > 19){ 
								dyna.push("   <td align='right'>&nbsp;&nbsp;" + (jsonItem.itemSeq-1) + "&nbsp;&nbsp;<div class='item' style='display:none'>" + jsonItem.itemNo + ":" + jsonItem.chkResult + "</div></td>"); 	
							} else { 
								dyna.push("   <td align='right'>&nbsp;&nbsp;" + jsonItem.itemSeq + "&nbsp;&nbsp;<div class='item' style='display:none'>" + jsonItem.itemNo + ":" + jsonItem.chkResult + "</div></td>"); 
							} 
						} else if (json.ctlType != "C") {
							dyna.push("   <td align='right'>&nbsp;&nbsp;" + jsonItem.itemSeq + "&nbsp;&nbsp;<div class='item' style='display:none'>" + jsonItem.itemNo + ":" + jsonItem.chkResult + "</div></td>");
						} else {
							dyna.push("   <td align='right'>&nbsp;&nbsp;" + jsonItem.itemSeqShow + "&nbsp;&nbsp;<div class='item' style='display:none'>" + jsonItem.itemNo + ":" + jsonItem.chkResult + "</div></td>");
						}
						dyna.push("   <td>"+jsonItem.itemContent);
						if(jsonItem.ptItem=='Y'){
							dyna.push("<div style='margin-top:12px;'>");

							dyna.push(((json['initControl_lockDoc'])?"<span>應負責經理：</span>":"<input type='button' value='應負責經理：' class='btnUpdateItemNoPtMgrId'>")
								+"<span class='color-blue ptMgrIdName' id='ptMgrIdName_"+(jsonItem.itemNo)+"'>"
								+jsonItem.ptMgrId+" "+jsonItem._ptMgrName
								+"</span>"
							);	
							
							dyna.push("</div>");
						}
						// J-108-0268 逾期情形
						if(jsonItem._overDue=='Y'){
							dyna.push("<div style='margin-top:12px;'>");
							dyna.push(((json['initControl_lockDoc'])?"<span>逾期情形：</span>":"<input type='button' value='查詢逾期情形' class='btnOverDue'>")
								+"<span class='overDueText' id='overDueText'>"+jsonItem.overDueText+"</span>");	
							dyna.push("</div>");
						}
						// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
						if(json.ctlType == "C" && jsonItem._u_note){
							dyna.push("<br><span class='color-red'>"+jsonItem._u_note+"</span>");
						} else if(jsonItem._u_note){    // J-109-0336 檢視事項及承諾事項之管控機制
                            dyna.push("<br><span class='color-red'>"+jsonItem._u_note+"</span>");
                        }

						dyna.push("   </td>");
						{//======================
						 //覆審結果 	
							//[]是 []否 [] 一
							var _name_chkResult = "_chkResult_"+(jsonItem.itemNo);
							var fmt = jsonItem._chkResult_fmt;
							dyna.push("   <td nowrap>");		
							build_radio(dyna, _name_chkResult, fmt, jsonItem.chkResult);						
							dyna.push("   </td>");	
						}
						{//======================
						 //內容說明 
							var _td_border = "";
							if(jsonItem._td_border && jsonItem._td_border=="Y"){
								_td_border = " style='border:2px solid red;' "
							}						
							dyna.push("<td "+_td_border+">");
							//處理prefix
							if(jsonItem._prefix && jsonItem._prefix.length>0){
								dyna.push("<div><span style='font-size:x-small;' class='color-red'>"+jsonItem._prefix+"</span></div>");
							}
							{//處理chkPreReview
								if(jsonItem._chkPreReview_fmt){
									var _chkPreReview_fmt = jsonItem._chkPreReview_fmt;
									var _name_chkPreReview = "_chkPreReview_"+(jsonItem.itemNo);
									dyna.push("<div style='white-space: nowrap;'>");								
									build_radio(dyna, _name_chkPreReview, _chkPreReview_fmt, jsonItem.chkPreReview);
									dyna.push("</div>");
								}	
							}
							
							var _name_chkText = "_chkText_"+(jsonItem.itemNo);
							
							dyna.push("<textarea name='"+_name_chkText+"' id='"+_name_chkText+"' maxlength='"+l170m01d_chkText_maxlength+"' maxlengthC='"+l170m01d_chkText_maxlengthC+"' class='my_taClass' rows='1' ></textarea>");
							
							dyna.push("&nbsp;</td>");	
						}						
						dyna.push("</tr>");
						
						if(is_z_html_loc){
							//Z_電腦建檔資料
							$.each(json.l170m01d_list['Z'], function(z_idx, z_jsonItem) {
								var fmt = z_jsonItem._chkResult_fmt;
								
								dyna.push("<tr id='tr_"+z_jsonItem.itemNo+"' class='z_chkItem'>");
								dyna.push("   <td><div class='item' style='display:none'>"+z_jsonItem.itemNo+":"+z_jsonItem.chkResult+"</div>"+"&nbsp;"+"</td>");
								if(fmt==""){
									dyna.push("<td colspan='3' nowrap>");
									dyna.push(z_jsonItem.itemContent);
									if(z_jsonItem._u_note){
										dyna.push("<br><span class='color-red'>"+z_jsonItem._u_note+"</span>");
									}
									if(z_jsonItem.itemNo=="ZA00"){
										dyna.push("&nbsp;&nbsp;&nbsp;");
										dyna.push("<button type='button' id='btn_all_z_y'>全部為是</button>&nbsp;");
										dyna.push("<button type='button' id='btn_all_z_n'>全部為否</button>&nbsp;");
										dyna.push("<button type='button' id='btn_all_z__'>全部清除</button>&nbsp;");
									}
									dyna.push("</td>");
								}else{
								    // J-111-0405 更動覆審系統內以下15式覆審報告表之文字內容。
								    if(z_jsonItem._u_note){
								        dyna.push("   <td>"+z_jsonItem.itemContent);
								        dyna.push("<br><span class='color-red'>"+z_jsonItem._u_note+"</span>");
								        dyna.push("</td>");
								    } else {
								        dyna.push("   <td >"+z_jsonItem.itemContent+"</td>");
								    }
									if(true){//覆審結果 	
										var _name_chkResult = "_chkResult_"+(z_jsonItem.itemNo);
										
										dyna.push("   <td nowrap>");		
										build_radio(dyna, _name_chkResult, fmt, z_jsonItem.chkResult);						
										dyna.push("   </td>");	
									}									
									dyna.push("   <td>"+"&nbsp;"+"</td>");	
								}
								
								dyna.push("</tr>");	
							});	
							
						}
						
						//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
						if(is_y_html_loc){
							//Y_履約條件
							$.each(json.l170m01d_list['Y'], function(y_idx, y_jsonItem) {
								var fmt = y_jsonItem._chkResult_fmt;
								
								dyna.push("<tr id='tr_"+y_jsonItem.itemNo+"' class='y_chkItem'>");
								dyna.push("   <td><div class='item' style='display:none'>"+y_jsonItem.itemNo+":"+y_jsonItem.chkResult+"</div>"+"&nbsp;"+"</td>");
								if(fmt==""){
									dyna.push("<td colspan='3' nowrap>");
									dyna.push(y_jsonItem.itemContent);
									if(y_jsonItem._u_note){
										dyna.push("<br><span class='color-red'>"+y_jsonItem._u_note+"</span>");
									}
									if(y_jsonItem.itemNo=="YA00"){
										dyna.push("&nbsp;&nbsp;&nbsp;");
										dyna.push("<button type='button' id='btn_all_y_y'>全部為是</button>&nbsp;");
										dyna.push("<button type='button' id='btn_all_y_n'>全部為否</button>&nbsp;");
										dyna.push("<button type='button' id='btn_all_y__'>全部清除</button>&nbsp;");
									}
									dyna.push("</td>");
								}else{
								    if(y_jsonItem._u_note){ // J-109-0336 檢視事項及承諾事項之管控機制
                                        dyna.push("   <td>"+y_jsonItem.itemContent+"<br><span class='color-red'>"+y_jsonItem._u_note+"</span></td>");
                                    } else {
                                        dyna.push("   <td>"+y_jsonItem.itemContent+"</td>");
                                    }
									if(true){//覆審結果 	
										var _name_chkResult = "_chkResult_"+(y_jsonItem.itemNo);
										
										dyna.push("   <td nowrap>");		
										build_radio(dyna, _name_chkResult, fmt, y_jsonItem.chkResult);						
										dyna.push("   </td>");	
									}									
//									dyna.push("   <td>"+"&nbsp;"+"</td>");
									dyna.push("   <td>");	
									var _name_chkText_y = "_chkText_"+(y_jsonItem.itemNo);
							
							        //處理prefix
									if(y_jsonItem._prefix && y_jsonItem._prefix.length>0){
										dyna.push("<div><span style='font-size:x-small;' class='color-red'>"+y_jsonItem._prefix+"</span></div>");
									}
									dyna.push("<textarea name='"+_name_chkText_y+"' id='"+_name_chkText_y+"' maxlength='"+l170m01d_chkText_maxlength+"' maxlengthC='"+l170m01d_chkText_maxlengthC+"'  class='my_taClass' rows='1' ></textarea>");
									
									dyna.push("&nbsp;</td>");		
								}
								
								dyna.push("</tr>");	
							});	
							
						}
						
						//J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
						if(is_x_html_loc){
							//X_土建融
							$.each(json.l170m01d_list['X'], function(x_idx, x_jsonItem) {
								var fmt = x_jsonItem._chkResult_fmt;
								
								dyna.push("<tr id='tr_"+x_jsonItem.itemNo+"' class='x_chkItem'>");
								dyna.push("   <td><div class='item' style='display:none'>"+x_jsonItem.itemNo+":"+x_jsonItem.chkResult+"</div>"+"&nbsp;"+"</td>");
								if(fmt==""){
									dyna.push("<td colspan='3' nowrap>");
									dyna.push(x_jsonItem.itemContent);
									if(x_jsonItem._u_note){
										dyna.push("<br><span class='color-red'>"+x_jsonItem._u_note+"</span>");
									}
									if(x_jsonItem.itemNo=="XA00"){
										dyna.push("&nbsp;&nbsp;&nbsp;");
										dyna.push("<button type='button' id='btn_all_x_y'>全部為是</button>&nbsp;");
										dyna.push("<button type='button' id='btn_all_x_n'>全部為否</button>&nbsp;");
										dyna.push("<button type='button' id='btn_all_x__'>全部清除</button>&nbsp;");
									}
									dyna.push("</td>");
								}else{
									dyna.push("   <td nowrap>"+x_jsonItem.itemContent+"</td>");
									if(true){//覆審結果 	
										var _name_chkResult = "_chkResult_"+(x_jsonItem.itemNo);
										
										dyna.push("   <td nowrap>");		
										build_radio(dyna, _name_chkResult, fmt, x_jsonItem.chkResult);						
										dyna.push("   </td>");	
									}									
//									dyna.push("   <td>"+"&nbsp;"+"</td>");
									dyna.push("   <td>");	
									var _name_chkText_x = "_chkText_"+(x_jsonItem.itemNo);
							
							        //處理prefix
									if(x_jsonItem._prefix && x_jsonItem._prefix.length>0){
										dyna.push("<div><span style='font-size:x-small;' class='color-red'>"+x_jsonItem._prefix+"</span></div>");
									}
									if (x_jsonItem.itemNo == "XA1A") {
										dyna.push("<textarea name='" + _name_chkText_x + "' id='" + _name_chkText_x + "' maxlength='" + l170m01d_chkText_maxlength + "' maxlengthC='" + l170m01d_chkText_maxlengthC + "'  class='my_taClass' rows='1' ></textarea>");
									}
									dyna.push("&nbsp;</td>");		
								}
								
								dyna.push("</tr>");	
							});	
							
						}
						
						
						
					});	
				}else{
					//itemType 下的項目若為0,不顯示
				}
			});
			$("#l170m01d_content").append(dyna.join("\n"));

            //J-106-0123-001 Web e-Loan國內企金覆審增加覆審項目「立約當日是否依規定查詢銀行法及金控法44條利害關係人之資料後再行簽約」
			//J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
			$.each(['A', 'B', 'C','Y','X', 'D'], function(idx_itemType, itemType) {
				var itemTypeDesc = json.l170m01d_title[itemType];
				var arr = json.l170m01d_list[itemType];
				var l170m01d_chkText_maxlength = json.l170m01d_chkText_maxlength; 
				var	l170m01d_chkText_maxlengthC = json.l170m01d_chkText_maxlengthC;
				if(arr && arr.length>0){
					$.each(arr, function(idx, jsonItem) {
						var _name_chkText = "_chkText_"+(jsonItem.itemNo);
						$("textarea.my_taClass[name="+_name_chkText+"]").val(jsonItem.chkText);
					});
				}
			});
			
			$(".btnUpdateItemNoPtMgrId").click(function(){
				//為讓 ajax 能存取
				var $tr = $(this).closest("tr");
				
				var l170m01d_oid = $tr.attr("l170m01d_oid");
				var l170m01d_itemSeq = $tr.attr("l170m01d_itemSeq");
				var exist_val = $tr.find(".ptMgrIdName").val();
				//======
				var options = {'title':"第"+l170m01d_itemSeq+"項" ,'exist_val': exist_val};

				var my_dfd = $.Deferred();				
				my_dfd.done(function(json){					
					$.ajax({
						type: "POST",
						handler: _handler,
						data: { formAction: "update_itemNo_ptMgrId"
	        	            , 'model_oid':l170m01d_oid
	        	            , 'ptMgrId':json.ptMgrId
	        	        },
					}).done(function(json){
						$tr.find(".ptMgrIdName").val(json.id+" "+json.name);
					});
		    	});    		
				
				RetrialPtMgrIdPanelAction.open(options, my_dfd);
				
			});
			
			// J-108-0268 逾期情形
			$(".btnOverDue").click(function(){
				$.ajax({
					type: "POST",
		            handler: _handler,	//LMS1700M01Formhandler
		            data: {
						formAction: "getOverDueData",
						mainOid: $("#mainOid").val()
					},
				}).done(function(json){
					$(".overDueText").val(json.overDueText);
				});
			});
			
			$("input[name=_chkResult_N017]:radio").change(function(){
				if ($("#_chkResult_N017_v_N").is(":checked")) {
					$("input[name=_chkPreReview_N017]").removeAttr("checked");
					$("#_chkText_N017").val("");
					
					$("input[name=_chkPreReview_N017]").closest("td").children().hide();
					
				}else{
					$("input[name=_chkPreReview_N017]").closest("td").children().show();
					
				}
			});

			$("input[name=_chkResult_B015]:radio").change(function(){
                if ($("#_chkResult_B015_v_N").is(":checked")) {
                    $("input[name=_chkPreReview_B015]").removeAttr("checked");
                    $("#_chkText_B015").val("");

                    $("input[name=_chkPreReview_B015]").closest("td").children().hide();

                }else{
                    $("input[name=_chkPreReview_B015]").closest("td").children().show();

                }
            });
		}
		
		//=====================
		if(true){//Z_電腦建檔
			$("#btn_all_z_y").click(function(){
				$(".z_chkItem").find("input[type=radio][value=Y]").attr("checked", "checked");
				$("input[name=_chkResult_ZA23]").trigger('change');
				$("input[name=_chkResult_ZB1A]").trigger('change');
				$("input[name=_chkResult_ZB2A]").trigger('change');
				$("input[name=_chkResult_ZB30]").trigger('change');
				$("input[name=_chkResult_ZB3E]").trigger('change');
				$("input[name=_chkResult_ZB34]").trigger('change');
				$("input[name=_chkResult_ZB3N]").trigger('change');
				$("input[name=_chkResult_ZB3Q]").trigger('change');
				$("input[name=_chkResult_ZC10]").trigger('change');
			});
			$("#btn_all_z_n").click(function(){
				$(".z_chkItem").find("input[type=radio][value=N]").attr("checked", "checked");
				$("input[name=_chkResult_ZA23]").trigger('change');
				$("input[name=_chkResult_ZB1A]").trigger('change');
				$("input[name=_chkResult_ZB2A]").trigger('change');
				$("input[name=_chkResult_ZB30]").trigger('change');
				$("input[name=_chkResult_ZB3E]").trigger('change');
				$("input[name=_chkResult_ZB34]").trigger('change');
				$("input[name=_chkResult_ZB3N]").trigger('change');
				$("input[name=_chkResult_ZB3Q]").trigger('change');				
				$("input[name=_chkResult_ZC10]").trigger('change');
				$(".z_chkItem").find("input[type=radio][value=N]").attr("checked", "checked");
			});
			$("#btn_all_z__").click(function(){
				$(".z_chkItem").find("input[type=radio]").removeAttr("checked");
				$("input[name=_chkResult_ZA23]").trigger('change');
				$("input[name=_chkResult_ZB1A]").trigger('change');
				$("input[name=_chkResult_ZB2A]").trigger('change');
				$("input[name=_chkResult_ZB30]").trigger('change');
				$("input[name=_chkResult_ZB3E]").trigger('change');
				$("input[name=_chkResult_ZB34]").trigger('change');
				$("input[name=_chkResult_ZB3N]").trigger('change');
				$("input[name=_chkResult_ZB3Q]").trigger('change');
				$("input[name=_chkResult_ZC10]").trigger('change');
			});	
			//---
			$("input[name=_chkResult_ZA23]:radio").change(function(){
				
				var elmArr = ["ZA2A","ZA2B","ZA2C","ZA2D","ZA2E","ZA2F"];
				//因為 unchecked 時,不能自動 trigger change
				if ($("#_chkResult_ZA23_v_N").is(":checked")) {
					$.each(elmArr, function(idx, itemNo) {
						$("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
		           	});
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).hide();
		           	});
				}else{
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).show();
		           	});
				}
			});		
			//---
			$("input[name=_chkResult_ZB1A]:radio").change(function(){
				var elmArr = ["ZB11","ZB12","ZB13"];
				if(json.rptId >= "Ver20240601"){
					elmArr.push("ZB14");
					elmArr.push("ZB15");
				}
				if ($("#_chkResult_ZB1A_v_N").is(":checked")) {
					$.each(elmArr, function(idx, itemNo) {
						$("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
		           	});
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).hide();
		           	});
				}else{
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).show();
		           	});
				}
			});
			$("input[name=_chkResult_ZB2A]:radio").change(function(){
                var elmArr = ["ZB21"];
                if ($("#_chkResult_ZB2A_v_N").is(":checked")) {
                    $.each(elmArr, function(idx, itemNo) {
                        $("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
                    });
                    $.each(elmArr, function(idx, itemNo) {
                        $("#tr_"+itemNo).hide();
                    });
                }else{
                    $.each(elmArr, function(idx, itemNo) {
                        $("#tr_"+itemNo).show();
                    });
                }
            });
			
			//J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			$("input[name=_chkResult_ZB30]:radio").change(function(){
				
				var elmArr = ["ZB31","ZB3A","ZB3B",
				              "ZB32","ZB3C","ZB3D",
				              "ZB33","ZB3E","ZB3F","ZB3G","ZB3H","ZB3I","ZB3J",
				              "ZB34","ZB3K","ZB3L","ZB3M",
				              "ZB35","ZB3N","ZB3O","ZB3P","ZB3Q","ZB3R","ZB3S"];
				
				if ($("#_chkResult_ZB30_v_K").is(":checked")) {
					$.each(elmArr, function(idx, itemNo) {
						$("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
		           	});
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).hide();
		           	});
				}else{
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).show();
		           	});
					
					$("input[name=_chkResult_ZB3E]").trigger('change');
					$("input[name=_chkResult_ZB34]").trigger('change');
					$("input[name=_chkResult_ZB3N]").trigger('change');
					$("input[name=_chkResult_ZB3Q]").trigger('change');
				}
				
			});
			
			
			$("input[name=_chkResult_ZB3E]:radio").change(function(){

				 var elmArr = ["ZB3F","ZB3G","ZB3H","ZB3I"];			
				 var elmArrHide = "";
                 var elmArrShow = "";
				
				
				if ($("#_chkResult_ZB3E_v_Y").is(":checked")) {
                    elmArrHide = ["ZB3G","ZB3H","ZB3I"];
                    elmArrShow = ["ZB3F"];                    					
				}else if ($("#_chkResult_ZB3E_v_N").is(":checked")){
                    elmArrHide = ["ZB3F"];
                    elmArrShow = ["ZB3G","ZB3H","ZB3I"];
				}else if ($("#_chkResult_ZB3E_v_K").is(":checked")){
                    elmArrHide = elmArr;
				}else{
					elmArrShow = elmArr;
				}
				
				if(elmArrHide !== ""){
				   $.each(elmArrHide, function(idx, itemNo) {
					   $("#tr_"+itemNo).hide();
	           	  });
				   
				  $.each(elmArrHide, function(idx, itemNo) {
					   $("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
		          }); 
				}
				
				if(elmArrShow !== ""){
					$.each(elmArrShow, function(idx, itemNo) {
						$("#tr_"+itemNo).show();
		           	});
				}

				
			});
			
			
			$("input[name=_chkResult_ZB34]:radio").change(function(){
				
				var elmArr = ["ZB3K","ZB3L","ZB3M"];
				
				if ($("#_chkResult_ZB34_v_N").is(":checked")) {					
					$.each(elmArr, function(idx, itemNo) {
						$("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
		           	});
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).hide();
		           	});
				}else{
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).show();
		           	});		
				}
			});
			
			$("input[name=_chkResult_ZB3N]:radio").change(function(){
				
				var elmArr = ["ZB3O","ZB3P"];
				
				if ($("#_chkResult_ZB3N_v_N").is(":checked")) {
					$.each(elmArr, function(idx, itemNo) {
						$("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
		           	});
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).hide();
		           	});
				}else{
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).show();
		           	});
				}
			});
			
			
			$("input[name=_chkResult_ZB3Q]:radio").change(function(){
				
				var elmArr = ["ZB3R","ZB3S"];
				
				if ($("#_chkResult_ZB3Q_v_N").is(":checked")) {
					$.each(elmArr, function(idx, itemNo) {
						$("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
		           	});
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).hide();
		           	});
				}else{
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).show();
		           	});
				}
			});
			
			// J-113-0204  新增及修正說明文句
			$("input[name=_chkResult_ZC10]:radio").change(function(){
				
				var elmArr = ["ZC1A"];
				
				if ($("#_chkResult_ZC10_v_N").is(":checked") || 
					(
					  !$("#_chkResult_ZC10_v_Y").is(":checked") && 
					  !$("#_chkResult_ZC10_v_N").is(":checked") && 
					  !$("#_chkResult_ZC10_v_K").is(":checked") 
					)					
				) {
					$.each(elmArr, function(idx, itemNo) {
						$("input[name=_chkResult_"+itemNo+"]").removeAttr("checked");
		           	});
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).show();
		           	});
				}else{
					$.each(elmArr, function(idx, itemNo) {
						$("#tr_"+itemNo).hide();
		           	});
				}
			});	
			
		}
		
		
		//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		if (true) {//Y_履約條件
			$("#btn_all_y_y").click(function(){
				$(".y_chkItem").find("input[type=radio][value=Y]").attr("checked", "checked");
				$("input[name=_chkResult_YA1A]").trigger('change');
				$("input[name=_chkResult_YB1A]").trigger('change');
				//J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
				$("input[name=_chkResult_YA11],input[name=_chkResult_YA12]").trigger('change');
				$("input[name=_chkResult_YB11],input[name=_chkResult_YB12]").trigger('change');
			});
			$("#btn_all_y_n").click(function(){
				$(".y_chkItem").find("input[type=radio][value=N]").attr("checked", "checked");
				$("input[name=_chkResult_YA1A]").trigger('change');
				$("input[name=_chkResult_YB1A]").trigger('change');
				//J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
				$("input[name=_chkResult_YA11],input[name=_chkResult_YA12]").trigger('change');
				$("input[name=_chkResult_YB11],input[name=_chkResult_YB12]").trigger('change');
			});
			$("#btn_all_y__").click(function(){
				$(".y_chkItem").find("input[type=radio]").removeAttr("checked");
				$("input[name=_chkResult_YA1A]").trigger('change');
				$("input[name=_chkResult_YB1A]").trigger('change');
				//J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
				$("input[name=_chkResult_YA11],input[name=_chkResult_YA12]").trigger('change');
				$("input[name=_chkResult_YB11],input[name=_chkResult_YB12]").trigger('change');
			});
			//---
			$("input[name=_chkResult_YA1A]:radio").change(function(){
				var elmArr = ["YA11", "YA12", "YA13"];
				//因為 unchecked 時,不能自動 trigger change
				if ($("#_chkResult_YA1A_v_N").is(":checked")) {
					$.each(elmArr, function(idx, itemNo){
						$("input[name=_chkResult_" + itemNo + "]").removeAttr("checked");
					});
					$.each(elmArr, function(idx, itemNo){
						$("#tr_" + itemNo).hide();
					});
				}
				else {
					$.each(elmArr, function(idx, itemNo){
						$("#tr_" + itemNo).show();
					});
				}
			});
			//---
			$("input[name=_chkResult_YB1A]:radio").change(function(){
				var elmArr = ["YB11", "YB12", "YB13"];
				//因為 unchecked 時,不能自動 trigger change
				if ($("#_chkResult_YB1A_v_N").is(":checked")) {
					$.each(elmArr, function(idx, itemNo){
						$("input[name=_chkResult_" + itemNo + "]").removeAttr("checked");
					});
					$.each(elmArr, function(idx, itemNo){
						$("#tr_" + itemNo).hide();
					});
				}
				else {
					$.each(elmArr, function(idx, itemNo){
						$("#tr_" + itemNo).show();
					});
				}
			});
			
			//J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
			//「應檢視事項」與「承諾事項」前兩項有勾否的，才顯示第三項需要輸入
			// J-109-0336 檢視事項及承諾事項之管控機制 - 第三項 always 都顯示
			// J-109-0336_002 檢視事項及承諾事項之管控機制 - YA12 為"否" 出現 YA13； YB11.YB12任一為"否" 出現 YB13

			//---
//			$("input[name=_chkResult_YA11]:radio,input[name=_chkResult_YA12]:radio").change(function(){
			$("input[name=_chkResult_YA12]:radio").change(function(){
				var elmArr = ["YA13"];
				//因為 unchecked 時,不能自動 trigger change
//				if ($("#_chkResult_YA11_v_N").is(":checked") || $("#_chkResult_YA12_v_N").is(":checked")) {
				if ($("#_chkResult_YA12_v_N").is(":checked")) {
					$.each(elmArr, function(idx, itemNo){
						$("#tr_" + itemNo).show();
					});
				}
				else {
					$.each(elmArr, function(idx, itemNo){
						$("input[name=_chkResult_" + itemNo + "]").removeAttr("checked");
					});
					$.each(elmArr, function(idx, itemNo){
						$("#tr_" + itemNo).hide();
					});
				}
			});
			//---
			$("input[name=_chkResult_YB11]:radio,input[name=_chkResult_YB12]:radio").change(function(){
				var elmArr = ["YB13"];
				//因為 unchecked 時,不能自動 trigger change
				if ($("#_chkResult_YB11_v_N").is(":checked") || $("#_chkResult_YB12_v_N").is(":checked")) {
					$.each(elmArr, function(idx, itemNo){
						$("#tr_" + itemNo).show();
					});
				}
				else {
					$.each(elmArr, function(idx, itemNo){
						$("input[name=_chkResult_" + itemNo + "]").removeAttr("checked");
					});
					$.each(elmArr, function(idx, itemNo){
						$("#tr_" + itemNo).hide();
					});
				}
			});
			
		}	
		
        //J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
		if(true){//X_土建融
			$("#btn_all_x_y").click(function(){
				$(".x_chkItem").find("input[type=radio][value=Y]").attr("checked", "checked");
				
			});
			$("#btn_all_x_n").click(function(){
				$(".x_chkItem").find("input[type=radio][value=N]").attr("checked", "checked");
				
			});
			$("#btn_all_x__").click(function(){
				$(".x_chkItem").find("input[type=radio]").removeAttr("checked");
				
			});	
			
			
		}
		
		
		//=====================
		if(true){//特別處理 textarea 的高度
			$("textarea.my_taClass").css('overflow-y','hidden').css('width', '240px').bind("keyup focus", expandText );
	        //在初始化時,若 textarea 有N列,展開
	        $.each( $("textarea.my_taClass"), function (idx, element) {
	            if ( $(element).val().length > 0) {                
	                $(element).trigger('focus');            
	            }            
	        });	
		}
		
		//=====================
		//處理要隱藏的項目
		$("input[name=_chkResult_N017]").trigger('change');
		
		$("input[name=_chkResult_ZA23]").trigger('change');
		$("input[name=_chkResult_ZB1A]").trigger('change');
		$("input[name=_chkResult_ZB2A]").trigger('change');
		//J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
		$("input[name=_chkResult_ZB30]").trigger('change');
		$("input[name=_chkResult_ZB3E]").trigger('change');
		$("input[name=_chkResult_ZB34]").trigger('change');
		$("input[name=_chkResult_ZB3N]").trigger('change');
		$("input[name=_chkResult_ZB3Q]").trigger('change');
		$("input[name=_chkResult_ZC10]").trigger('change');
		
		//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		$("input[name=_chkResult_YA1A]").trigger('change');
		$("input[name=_chkResult_YB1A]").trigger('change');
		
		//J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
		$("input[name=_chkResult_XA1A]").trigger('change');
		
		
		//J-105-0287-003 修改Web e-Loan國內企金授信覆審系統履行條件之檢核判斷
		$("input[name=_chkResult_YA11],input[name=_chkResult_YA12]").trigger('change');
		$("input[name=_chkResult_YB11],input[name=_chkResult_YB12]").trigger('change');
		
        //J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
        $.ajax({
           type: "POST",
		   async: false ,
           handler: _handler,
           data: $.extend({
               formAction: "getl170m01_defaultVal",
               mainOid: $("#mainOid").val()
           }, json),
	   }).done(function(responseData){
		   if(responseData.realRpFg == "Y"){
		      $('.x_chkItem').show();
		   }else{
			  $('.x_chkItem').hide();
		   }
	   });
	}	
	
	function build_radio(dyna, radioName, srcStr, chooseVal){
		$.each(srcStr.split("|"), function(idx, val_item) {
			var radioVal = val_item.substring(0, 1);
			var attr = (chooseVal==radioVal)?" checked ":"";
			dyna.push("<label><input name='"+radioName+"' id='"+(radioName+"_v_"+radioVal)+"' type='radio' value='"+radioVal +"' "+attr+">"+i18n.lms1700m01[("label."+val_item)]+"</label>");
		});
	}

	function to_vertical_str(src){
		var charArr = [];						
		for (var idx_charc = 0; idx_charc < src.length; idx_charc++)
		{
			charArr.push( src.charAt(idx_charc) );						    
		}
		return charArr.join("<br/>");
	}	
	function to_horizontal_str(src){
		var charArr = [];						
		for (var idx_charc = 0; idx_charc < src.length; idx_charc++)
		{
			charArr.push( src.charAt(idx_charc) );						    
		}
		return charArr.join("&nbsp;&nbsp;&nbsp;&nbsp;");
	}	
});

//http://perplexed.co.uk/596_expanding_textarea_as_you_type.htm
var expandText = function(){    
    var el = this;
    //if(el.tagName!=="textarea"){return;}
    // has the scroll height changed?, we do this because we can successfully change the height
    var prvLen = el.preValueLength;
    el.preValueLength = el.value.length;
    if(el.scrollHeight===el.prvScrollHeight&&el.prvOffsetHeight===el.offsetHeight&&el.value.length>=prvLen){    	
        return;
    }
    while(el.rows>1 && el.scrollHeight<el.offsetHeight){
        el.rows--;
    }
    var h=0;
    while(el.scrollHeight > el.offsetHeight && h!==el.offsetHeight && (h=el.offsetHeight) ){
        el.rows++;
    }    
    el.rows++;    
    el.prvScrollHeight = el.scrollHeight;
    el.prvOffsetHeight = el.offsetHeight;     
};

function showItemNo(){
	$(".item").show().css("color","blue");
}