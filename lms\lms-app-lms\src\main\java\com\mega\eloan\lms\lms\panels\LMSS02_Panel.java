package com.mega.eloan.lms.lms.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 借款人基本資料(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS02_Panel extends Panel {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS02_Panel(String id) {
		super(id);
		// add(new LMSS02Panel01("lmss02panel01"));
		// add(new LMSS02Panel02("lmss02panel02"));
		// add(new LMSS02Panel03("lmss02panel03"));
		// add(new LMSS02Panel04("lmss02panel04"));
	}
}
