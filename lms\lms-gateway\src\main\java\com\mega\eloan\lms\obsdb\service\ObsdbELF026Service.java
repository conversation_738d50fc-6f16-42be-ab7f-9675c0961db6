package com.mega.eloan.lms.obsdb.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <pre>
 * 海外泰行個金徵信	ELF026-泰行個金徵信評等檔
 * </pre>
 */
public interface ObsdbELF026Service {

	/**
	 BR_CD	分行別 (key)
	 CUST_KEY	主借款人統一編號 (key)
	 SUBJCODE	科目 (key)
	 CNTRNO	本件額度序號 (key)
	 CHKDATE	覆核日期 (key)
	 CUSTID	客戶統一編號 (key)
	 DUPNO	重複序號 (key)
	 NOTEID	NOTES文件編號 (key)
	 
	 FINAL_RATING_FLAG	本案為最終採用之關係人評等
	 FR	最終評等
	 RATING_DATE	評等日期
	 RATING_ID	評等文件編號
	 LOAN_CODE	授信科目
	 MOWTYPE	評等模型類別
	 MOWVER1	模型版本-大版
	 MOWVER2	模型版本-小版
	 LOAN_PERIOD	授信期間(年)
	 */
	
	public void insertForInside(String br_cd, String cust_key, String subjcode, String cntrno, int chkdate, 
			String custid, String dupno, String noteid, String final_rating_flag,
			int fr, int rating_date, String rating_id, String loan_code, String mowtype,
			int mowver1, int mowver2, BigDecimal loan_period);

	public void delByKey(String br_cd, String cust_key, String subjcode, String cntrno, int chkdate, 
			String custid, String dupno, String noteid);
	
	
	


}
