package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.L140MM6A;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 共同行銷維護作業- 編製中
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller
@RequestMapping(path = "/fms/lms7800m01/{page}")						
public class LMS7800M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	public LMS7800M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);

		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
				AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, CreditDocStatusEnum.海外_待覆核,
				CreditDocStatusEnum.先行動用_待覆核));

		renderJsI18N(LMS7800M01Page.class);
	
		new DocLogPanel("_docLog").processPanelData(model, params);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L140MM6A.class;
	}
}