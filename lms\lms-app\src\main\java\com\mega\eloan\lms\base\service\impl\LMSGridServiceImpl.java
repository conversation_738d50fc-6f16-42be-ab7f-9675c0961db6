/* 
 * COLGridServiceImpl.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.base.service.LMSGridService;
import com.mega.eloan.lms.dao.LMSRPTDao;

/**
 * <pre>
 * Common Grid Service
 * </pre>
 * 
 * @since 2012/3/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/3/30,iristu,new
 *          </ul>
 */
@Service
public class LMSGridServiceImpl implements LMSGridService {

	@Resource
	LMSRPTDao gridDao;

	
	@Override
	public <T> Page<T> findPage(Class<T> clazz, ISearch search) {
		return gridDao.findPage(clazz, search);
	}

}
