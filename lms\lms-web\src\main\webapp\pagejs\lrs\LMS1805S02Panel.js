function cancel(){
    var rows = $("#lms180s02").getGridParam('selarrrow');
    var list = new Array();
    for (var i = 0; i < rows.length; i++) {
        if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
            var data = $("#lms180s02").getRowData(rows[i]);
            if (data.docStatus1 == "N") {
                CommonAPI.showMessage(i18n.lms1805m01["retrial"]);
                return;
            }
            list[i] = data.oid;
        }
    }
    if (list == "") {
        CommonAPI.showMessage(i18n.def["fileSelect"]);
        return;
    }
    $("input[type='radio'][name='ELF412_NCKDFLAG']").removeAttr("checked");
    $("#nckdDate").hide();
    $("#openbox3").thickbox({
        //'不覆審原因'
        title: i18n.lms1805m01['reason'],
        width: 550,
        height: 350,
        modal: false,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var nckdflag = $("input[name='ELF412_NCKDFLAG']:checked").val();
                if (nckdflag == 'undefined' || nckdflag == 0 ||
                nckdflag == null) {
                    CommonAPI.showMessage(i18n.lms1805m01["noReason"]);
                    return;
                }
                if (nckdflag == 8) {
                	var newNextDt = $.trim($("#nckdDate").find("#newNextDt").val());
                    if (!newNextDt || newNextDt == '') {
                        CommonAPI.showMessage(i18n.lms1805m01["noNextDate"]);
                        return;
                    }
                }
                $.thickbox.close();
                $.ajax({
                    handler: "lms1805formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "cancel",
                        txCode: responseJSON.txCode,
                        elf412Nckdflag: nckdflag,
                        newNextNwDt: $("#newNextDt").val(),
                        list: list
                    },
                    success: function(obj){
                        $("#lms180s02").trigger("reloadGrid");
                    }
                });
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}

function recancel(){
    var rows = $("#lms180s02").getGridParam('selarrrow');
    var list = new Array();
    var enterDate = false;
    if (rows == 'undefined' || rows == null || rows == "") {
        CommonAPI.showMessage(i18n.def["fileSelect"]);
        return;
    }
    for (var i = 0; i < rows.length; i++) {
        if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
            var data = $("#lms180s02").getRowData(rows[i]);
            if (data.docStatus1 == "Y") {
                CommonAPI.showMessage(i18n.lms1805m01["noRetrial"]);
                return;
            }
            list[i] = data.oid;
            if(!data.elfLRDate || data.elfLRDate == ""){
            	enterDate = true;
            }
        }
    }
    $("#newLRDate").val('');
    if(enterDate){
        $("#recancel").thickbox({
            //'上次覆審日期'
            title: i18n.lms1805m01['eLF412_LRDATE'],
            width: 300,
            height: 130,
            modal: false,
            align: "center",
            valign: "bottom",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	if($("#recancelForm").valid()){
                        $.ajax({
                            handler: "lms1805formhandler",
                            type: "POST",
                            dataType: "json",
                            data: {
                                formAction: "recancel",
                                newLRDate: $("#newLRDate1").val(),
                                txCode: responseJSON.txCode,
                                list: list
                            },
                            success: function(obj){
                                $("#lms180s02").trigger("reloadGrid");
                            }
                        });
                        $.thickbox.close();
                	}
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }else{
    	$.ajax({
            handler: "lms1805formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "recancel",
                newLRDate: $("#newLRDate1").val(),
                txCode: responseJSON.txCode,
                list: list
            },
            success: function(obj){
                $("#lms180s02").trigger("reloadGrid");
            }
        });
    }
}

function newList(){
    //FROM清除
    $("#l1805s02Form").reset();
    $("#openbox2").thickbox({
        //'新稱覆審明細'
        title: i18n.lms1805m01['newDetails'],
        width: 450,
        height: 150,
        align: "center",
        valign: "bottom",
        modal: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                $.ajax({
                    handler: "lms1805formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "newList",
                        txCode: responseJSON.txCode,
                        id: $("#Id").val(),
                        dupno: $("#Dupno").val(),
                        dataDate: $("#dataDate").val(),
                        branchId: responseJSON.branchId,
                        mainId: $("#mainId").val()
                    },
                    success: function(){
                        $("#lms180s02").trigger("reloadGrid");
                    }
                });
                $.thickbox.close();
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}

function getNumber(){
    $.ajax({
        handler: "lms1805formhandler",
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getNumber",
            txCode: responseJSON.txCode,
            oid: responseJSON.mainOid
        },
        success: function(){
            $("#lms180s02").trigger("reloadGrid");
            CommonAPI.triggerOpener("gridview", "reloadGrid");
        }
    });
}

function report(){
    $("#reportGrid").thickbox({
        title: i18n.lms1805m01['chooseCTLreport'],
        width: 450,
        height: 350,
        align: "center",
        valign: "bottom",
        modal: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var list = $("#l170m01a").getGridParam('selrow');
                if (list == 'undefined' || list == null || list == "") {
                    CommonAPI.showMessage(i18n.def["fileSelect"]);
                    return;
                }
                var dataOid = $("#l170m01a").getRowData(list);
                $.ajax({
                    handler: "lms1805formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "reportCTL",
                        exOid : dataOid.oid,
                        l180m01aOid : responseJSON.oid
                    },
                    success: function(){
                        $("#lms180s02").trigger("reloadGrid");
                    }
                });
                $.thickbox.close();
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
	
	var grid = $("#l170m01a").iGrid({
	        handler: 'lms1805gridhandler',
	        height: 200,
	        postData: {
	            oid: $("#oid").val(),
	            formAction: "queryL170M01A"
	        },
	        sortname: 'dataDate',
	        rowNum: 15,
	        colModel: [{
	            // "資料年月",
	            colHeader: i18n.lms1805m01['L180M01A.dataDate'],
	            name: 'dataDate',
	            width: 50,
	            align: "center",
	            sortable: true
	        }, {
	            // "產生日期",
	            colHeader: i18n.lms1805m01['L180M01A.generateDate'],
	            name: 'generateDate',
	            width: 50,
	            align: "center",
	            sortable: true
	        }, {
	            // "預約覆審日期",
	            colHeader: i18n.lms1805m01['L180M01A.defaultCTLDate'],
	            name: 'defaultCTLDate',
	            align: "center",
	            width: 50,
	            sortable: true
	        }, {
	            colHeader: "oid",
	            name: 'oid',
	            hidden: true
	        }]
	    });
}

// EXCEL下載
$("#excelFile").click(function(){

    $.capFileDownload({
        //  handler: "simplefiledwnhandler",
        data: {
            fileOid: $("#excelFile").val()
        
        }
    });
})
function downLoad(mode){
    $.capFileDownload({
        //  handler: "simplefiledwnhandler",
        data: {
            fileOid: mode
        }
    });
}

function gridview22(){
    if (responseJSON.mainDocStatus == "010") {
        var obj = CommonAPI.loadCombos("lms1815m01_elfNCkdFlag");
        $("#ELF412_NCKDFLAG").setItems({
            item: obj.lms1815m01_elfNCkdFlag,
            format: "{key}",    //"{value}.{key}",
            size: 1
        });
        $("input[name=ELF412_NCKDFLAG]").click(function(){
        	var nckdFlag = $(this).val();
        	if(nckdFlag == 8){
        		$("#nckdDate").show();
        	}else{
        		$("#nckdDate").hide();
        	}
        });
    }
    $("#lms180s02").iGrid({
        handler: 'lms1805gridhandler',
        height: 350,
        sortname: 'docStatus1|projectSeq|elfLRDate|custId',
        sortorder : 'asc|asc|asc|asc',
        rowNum: 15,
        multiselect: true,
        postData: {
            formAction: "queryList"
        },
        colModel: [{
            // "序號",
            colHeader: i18n.lms1805m01['L180M01B.projectSeq'],
            name: 'projectSeq',
            width: 30,
            align: "center",
            sortable: true
        }, {
            // "需覆審",
            colHeader: i18n.lms1805m01['needRetrial'],
            name: 'docStatus1',
            width: 50,
            align: "center",
            sortable: true
        }, {
            // "上次覆審日",
            colHeader: i18n.lms1805m01['eLF412_LRDATE'],
            name: 'elfLRDate',
            align: "center",
            width: 80
        }, {
            // "借款人統編",
            colHeader: i18n.lms1805m01['eLF412_CUSTID'],
            name: 'custId',
            align: "center",
            width: 85,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            // "借款人名稱",
            colHeader: i18n.lms1805m01['cNAME'],
            name: 'elfCName',
            align: "left",
            width: 155
        }, {
            // "主要客戶",
            colHeader: i18n.lms1805m01['eLF412_MAINCUST'],
            name: 'elfMainCust',
            align: "center",
            width: 65
        }, {
            // "異常戶",
            colHeader: i18n.lms1805m01['eLF412_MDFLAG'],
            name: 'elfMDFlag',
            align: "center",
            width: 50
        }, {
            // "額度共管",
            colHeader: i18n.lms1805m01['eLF412_DBUOBU'],
            name: 'elfDBUOBU',
            align: "center",
            width: 55
        }, {
            // "週期",
            colHeader: i18n.lms1805m01['eLF412_RCKDLINE'],
            name: 'elfRCkdLine',
            align: "center",
            width: 45
        }, {
            // "新作/增額",
            colHeader: i18n.lms1805m01['eLF412_NEWADD'],
            name: 'elfNewAdd',
            align: "center",
            width: 55
        }, {
            // "不覆審代碼",
            colHeader: i18n.lms1805m01['eLF412_NCKDFLAG'],
            name: 'newNCkdFlag',
            align: "center",
            width: 55
        }, {
            //文件狀態
            colHeader: " ",
            name: 'createBY',
            align: "center",
            width: 30
        }, {
            //"分行覆審報告表新增註記"
            colHeader: i18n.lms1805m01['L180M01B.newBy170M01'],
            name: 'newBy170M01',
            align: "center",
            width: 70
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }]
    });
    
    function printFn(){
    	$("#custGrid").jqGrid("setGridParam", {
			postData : {
				formAction : "queryL120M01A",
				custId : $("#custId").html()
			},
			page : 1,
			//gridPage : 1,
			search: true
		}).trigger("reloadGrid");
    	$("#printCust").thickbox({
			title : i18n.def['print'],
			width : 500,
			height : 350,
			model : true,
			align : "center",
			valign : "bottom",
			i18n : i18n.def,
			buttons : {
				"sure" : function(){
					var rows = $("#custGrid").getGridParam('selrow');
					var list = "";
					if (rows != 'undefined' && rows != null && rows != 0) {
						var data = $("#custGrid").getRowData(rows);
						list = data.mainId;
					}
					if (list == "") {
						CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
						return;
					}
					$("#printGrid").jqGrid("setGridParam", {
			            postData: {
			            	formAction : "queryPrint",
			                mainId: list
			            },
						page : 1,
						//gridPage : 1,
			            search: true
			        }).trigger("reloadGrid");
					$("#printView").thickbox({
						title : i18n.def['print'],
						width : 500,
						height : 350,
						model : true,
						i18n : i18n.def,
						buttons : {
							"print" : function(){
								var pdfName = "";
			                    var count = 0;
			                    var content = "";
			                    var id = $("#printGrid").getGridParam('selarrrow');
			                    for (var i = 0; i < id.length; i++) {
			                        if (id[i] != "") {
			                            var datas = $("#printGrid").getRowData(id[i]);
			                            content = content + datas.rpt + "^" + datas.oid + "^" + datas.custId + "^" + datas.dupNo + "^" + datas.cntrNo + "^" + datas.refMainId + "|";
			                            pdfName = datas.rptNo + ".pdf";
			                            count++;
			                        }
			                    }
			                    
			                    if (content.length != 0) {
			                        content = content.substring(0, content.length - 1);
			                    }
			                    if (count == 0) {
			                        CommonAPI.showMessage(i18n.def['grid.selrow']);
			                    } else {
			                        if (count != 1) {
			                            pdfName = "LMS1205R01.pdf";
			                        }
			                        $.form.submit({
			                            url: "../../simple/FileProcessingService",
			                            target: "_blank",
			                            data: {
			                                mainId: list,
			                                rptOid: content,
			                                fileDownloadName: pdfName,
			                                serviceName: "lms1205r01rptservice"
			                            }
			                        });
			                    }
							},
							"cancel" : function(){
								$.thickbox.close();
							}
						}
					});
				},
				"cancel" : function(){
					$.thickbox.close();
				}
			}
		});
    }
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $("#L180M01BForm").reset();
        var btn01A = API.createJSON([{
            key: i18n.lms1805m01['save'],
            value: function(){
                $.ajax({
                    type: "POST",
                    handler: "lms1805formhandler",
                    data: {
                        formAction: "saveList",
                        mainOid: rowObject.oid
                    },
                    success: function(){
                        $("#lms180s02").trigger("reloadGrid");
                    }
                });
            }
        }, {
            key: i18n.lms1805m01['printReport'],
            value: function(){
                printFn();
            }
        }, {
            key: i18n.lms1805m01['close'],
            value: function(){
                $.thickbox.close();
            }
        }]);
        var btnEls = API.createJSON([{
            key: i18n.lms1805m01['printReport'],
            value: function(){
                printFn();
            }
        }, {
            key: i18n.lms1805m01['close'],
            value: function(){
                $.thickbox.close();
            }
        }]);
        
        var btns = {};
        if (responseJSON.mainDocStatus == "010") {
            btns = btn01A;
        } else {
            btns = btnEls;
        }
        
        $.ajax({
            handler: "lms1805formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "queryList",
                mainOid: rowObject.oid
            },
            success: function(obj){
                $("body .reset").html('');
                $("#_ListPanel").injectData(obj);
            	if(obj.nCkdFlag && obj.nCkdFlag == "8"){
            		$("#nextDateTr").show();
            	}else{
            		$("#nextDateTr").hide();
            	}
                $("#_ListPanel").thickbox({
                    //'覆審名單明細表'
                    title: i18n.lms1805m01['produceDetails'],
                    width: 800,
                    height: 535,
                    modal: false,
                    buttons: btns
                });
            }
        });
    };
    $("#custGrid").iGrid({
        handler: 'lms1805gridhandler',
        height: 200,
        postData: {
            formAction: ""
        },
        sortname: 'custId',
        rowNum: 15,
        colModel: [{
            // "統編",
            colHeader: i18n.lms1805m01['sap110.custId'],
            name: 'custId',
            width: 50,
            align: "center",
            sortable: true
        }, {
            // "主要借款人",
            colHeader: i18n.lms1805m01['sap110.custName'],
            name: 'custName',
            align: "center",
            width: 50,
            sortable: true
        }, {
            colHeader: "DBU/OBU",
            name: 'typCd',
            align: "center",
            width: 50,
            sortable: true
        }, {
            // "簽案日期",
            colHeader: i18n.lms1805m01['L120M01A.caseDate'],
            name: 'caseDate',
            align: "center",
            width: 50,
            sortable: true
        }, {
            // "核准日期",
            colHeader: i18n.lms1805m01['L120M01A.approveTime'],
            name: 'approveTime',
            align: "center",
            width: 50,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }]
    });
    
    
    $("#printGrid").iGrid({
	    handler: 'lms1805gridhandler',
	    height: 270,
		rownumbers:true,
		multiselect: true,
		hideMultiselect:false,
		caption: "&nbsp;",
		hiddengrid : false,
		sortname : 'rptNo',
	    postData: {
            printCondition : "H"
	    },
	    colModel: [{
	        colHeader: i18n.lms1805m01['cNAME'],// "借款人名稱",
	        name: 'custName',
	        width: 120,
	        sortable: true
	    }, {
	        colHeader: i18n.lms1805m01['print.rptNo'],// "報表編號",
	        name: 'rptNo',
	        align: "center",
	        width: 40,
	        sortable: true
	    }, {
	        colHeader: i18n.lms1805m01['print.rptName'],// "報表名稱",
	        name: 'rptName',
	        width: 70,
	        sortable: true
	    }, {
	        colHeader: i18n.lms1805m01['print.cntrNo'],// "額度序號",
	        name: 'cntrNo',
	        align: "center",
	        width: 50,
	        sortable: true
	    }, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}, {
			colHeader : "rpt",
			name : 'rpt',
			hidden : true
		}, {
			colHeader : "custId",
			name : 'custId',
			hidden : true
		}, {
			colHeader : "dupNo",
			name : 'dupNo',
			hidden : true
		}, {
			colHeader : "refMainId",
			name : 'refMainId',
			hidden : true
		}]
	});
}
