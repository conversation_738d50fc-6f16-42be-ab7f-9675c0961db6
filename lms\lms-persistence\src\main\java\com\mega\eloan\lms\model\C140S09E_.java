package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140S09E database table.
 * </pre>
 * @since  2011/10/27
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/27,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140S09E.class)
public class C140S09E_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140S09E, BigDecimal> gFin1;
	public static volatile SingularAttribute<C140S09E, BigDecimal> gFin2;
	public static volatile SingularAttribute<C140S09E, BigDecimal> gFin3;
	public static volatile SingularAttribute<C140S09E, BigDecimal> gFin4;
	public static volatile SingularAttribute<C140S09E, BigDecimal> gFin5;
	public static volatile SingularAttribute<C140S09E, BigDecimal> gFin6;
	public static volatile SingularAttribute<C140S09E, String> gId;
	public static volatile SingularAttribute<C140S09E, String> gNa;
	public static volatile SingularAttribute<C140S09E, BigDecimal> gRto1;
	public static volatile SingularAttribute<C140S09E, BigDecimal> gRto2;
	public static volatile SingularAttribute<C140S09E, BigDecimal> gRto3;
	public static volatile SingularAttribute<C140S09E, C140M01A> c140m01a;
}
