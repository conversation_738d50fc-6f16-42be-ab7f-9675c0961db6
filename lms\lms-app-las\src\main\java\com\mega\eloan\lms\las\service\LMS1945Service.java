package com.mega.eloan.lms.las.service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.lms.model.L192M01A;

/**
 * 分行 稽核工作底稿 介面
 * 
 * <AUTHOR>
 * 
 */
public interface LMS1945Service extends ICapService {
	
	/**
	 * 取得工作底稿grid
	 * 
	 * @param search
	 *            ISearch
	 * @return Page<L192M01A>
	 */
	Page<L192M01A> get1945V01(ISearch search);
}
