package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 日本消金評等模型 **/
@NamedEntityGraph(name = "C121M01B-entity-graph", attributeNodes = { @NamedAttributeNode("c120m01a") })
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C121M01B", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class C121M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 編製單位代號 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 評等文件ID **/
	@Size(max=32)
	@Column(name="RATINGID", length=32, columnDefinition="CHAR(32)")
	private String ratingId;

	/** 
	 * 評等建立日期<p/>
	 * 建立初始評等的日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="GRDCDATE", columnDefinition="DATE")
	private Date grdCDate;

	/** 評等調整日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GRDTDATE", columnDefinition="DATE")
	private Date grdTDate;

	/** 完成最終評等日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="RATINGDATE", columnDefinition="DATE")
	private Date ratingDate;

	/** 聯徵查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="JCICQDATE", columnDefinition="DATE")
	private Date jcicQDate;

	/** 模型版本 **/
	@Size(max=3)
	@Column(name="VARVER", length=3, columnDefinition="VARCHAR(3)")
	private String varVer;

	/** 初始評等 **/
	@Size(max=2)
	@Column(name="PRATING", length=2, columnDefinition="VARCHAR(2)")
	private String pRating;

	/** 
	 * 獨立評等<p/>
	 * (考慮聯徵負面資訊後)
	 */
	@Size(max=2)
	@Column(name="SRATING", length=2, columnDefinition="VARCHAR(2)")
	private String sRating;

	/** 
	 * 支援評等<p/>
	 * (考慮聯徵J10評分後)<br/>
	 *  當代碼016~022，支援評等降為DF。
	 */
	@Size(max=2)
	@Column(name="SPRTRATING", length=2, columnDefinition="VARCHAR(2)")
	private String sprtRating;

	/** 調整評等 **/
	@Size(max=2)
	@Column(name="ADJRATING", length=2, columnDefinition="VARCHAR(2)")
	private String adjRating;

	/** 
	 * 最終評等<p/>
	 * (考量主觀評等)
	 */
	@Size(max=2)
	@Column(name="FRATING", length=2, columnDefinition="VARCHAR(2)")
	private String fRating;

	/** 原始最終評等 **/
	@Size(max=2)
	@Column(name="ORGFR", length=2, columnDefinition="VARCHAR(2)")
	private String orgFr;

	/** 
	 * 註記不需調整<p/>
	 * 1是/2否
	 */
	@Size(max=1)
	@Column(name="NOADJ", length=1, columnDefinition="CHAR(1)")
	private String noAdj;

	/** 
	 * 調整狀態<p/>
	 * 1.調升 2.調降 3.回復
	 */
	@Size(max=1)
	@Column(name="ADJUSTSTATUS", length=1, columnDefinition="CHAR(1)")
	private String adjustStatus;

	/** 
	 * 調整註記<p/>
	 * 1.淨資產2.職業3.其它
	 */
	@Size(max=1)
	@Column(name="ADJUSTFLAG", length=1, columnDefinition="CHAR(1)")
	private String adjustFlag;

	/** 調整理由 **/
	@Size(max=300)
	@Column(name="ADJUSTREASON", length=300, columnDefinition="VARCHAR(300)")
	private String adjustReason;

	/** M1出生日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="RAW_M1", columnDefinition="Date")
	private Date raw_m1;

	/** P2年薪幣別 **/
	@Size(max=3)
	@Column(name="RAW_PAYCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_payCurr;

	/** P2年薪金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="RAW_PAYAMT", columnDefinition="DEC(13,0)")
	private BigDecimal raw_payAmt;

	/** P2其它收入幣別 **/
	@Size(max=3)
	@Column(name="RAW_OTHERCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_otherCurr;

	/** P2其它收入金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="RAW_OTHERAMT", columnDefinition="DEC(13,0)")
	private BigDecimal raw_otherAmt;

	/** 家庭所得幣別 **/
	@Size(max=3)
	@Column(name="RAW_HINCOMECURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_hincomeCurr;

	/** 本次新做案下不動產租金收入幣別 **/
	@Size(max=3)
	@Column(name="RAW_RINCOMECURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_rincomeCurr;

	/** 財富管理-本行幣別 **/
	@Size(max=3)
	@Column(name="RAW_INVMBALCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_invMBalCurr;

	/** 財富管理-它行幣別 **/
	@Size(max=3)
	@Column(name="RAW_INVOBALCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_invOBalCurr;

	/** 金融機構存款往來情形幣別 **/
	@Size(max=3)
	@Column(name="RAW_BRANAMTCURR", length=3, columnDefinition="VARCHAR(3)")
	private String raw_branAmtCurr;
	
	/** 轉換匯率(年薪) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_PAY", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_pay;

	/** 轉換匯率(其他收入) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_OTH", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_oth;

	/** 轉換匯率(家庭所得) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_HINCOME", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_hincome;

	/** 轉換匯率(本次新做案下不動產租金收入) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_RINCOME", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_rincome;

	/** 轉換匯率(財富管理-本行) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_INVMBAL", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_invMBal;

	/** 轉換匯率(財富管理-它行) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_INVOBAL", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_invOBal;

	/** 轉換匯率(金融機構存款往來情形) **/
	@Digits(integer=9, fraction=5, groups = Check.class)
	@Column(name="EXRATE_BRANAMT", columnDefinition="DEC(9,5)")
	private BigDecimal exRate_branAmt;

	/** A5月份數 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="RAW_A5", columnDefinition="DEC(4,0)")
	private Integer raw_a5;

	/** 
	 * (同國內)CHKITEM1<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM1", length=1, columnDefinition="CHAR(1)")
	private String chkItem1;

	/** 退票 **/
	@Size(max=1)
	@Column(name="CHKITEM1A", length=1, columnDefinition="CHAR(1)")
	private String chkItem1a;

	/** 拒往 **/
	@Size(max=1)
	@Column(name="CHKITEM1B", length=1, columnDefinition="CHAR(1)")
	private String chkItem1b;

	/** 信用卡強停 **/
	@Size(max=1)
	@Column(name="CHKITEM1C", length=1, columnDefinition="CHAR(1)")
	private String chkItem1c;

	/** 催收呆帳 **/
	@Size(max=1)
	@Column(name="CHKITEM1D", length=1, columnDefinition="CHAR(1)")
	private String chkItem1d;

	/** 
	 * (同國內)CHKITEM2<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM2", length=1, columnDefinition="CHAR(1)")
	private String chkItem2;

	/** 
	 * (同國內)CHKITEM4<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM4", length=1, columnDefinition="CHAR(1)")
	private String chkItem4;

	/** 
	 * (同國內)CHKITEM5<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM5", length=1, columnDefinition="CHAR(1)")
	private String chkItem5;

	/** 
	 * (同國內)CHKITEM7<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM7", length=1, columnDefinition="CHAR(1)")
	private String chkItem7;

	/** 
	 * 近12個月授信帳戶有出現遲延還款<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM9", length=1, columnDefinition="CHAR(1)")
	private String chkItem9;

	/** 
	 * 近12個月信用卡繳款狀況出現全額逾期未繳2次(含)以上<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM10", length=1, columnDefinition="CHAR(1)")
	private String chkItem10;

	/** 
	 * 申貸查詢時現金卡有借款餘額(新台幣仟元)<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM11", length=1, columnDefinition="CHAR(1)")
	private String chkItem11;

	/** 
	 * 申貸查詢時無擔保授信(扣除學生助學貸款)餘額超過新台幣1,000仟元<p/>
	 * 1:有, 2:無, 3:NA<br/>
	 *  用c120s01e.balQdata26 - c120s01e.balQdata28
	 */
	@Size(max=1)
	@Column(name="CHKITEM12", length=1, columnDefinition="CHAR(1)")
	private String chkItem12;

	/** 
	 * 近3個月新業務申請查詢總家數超過3次(含)以上<p/>
	 * 1:有, 2:無, 3:NA
	 */
	@Size(max=1)
	@Column(name="CHKITEM13", length=1, columnDefinition="CHAR(1)")
	private String chkItem13;

	/** 累加風險點數 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="SUMRISKPT", columnDefinition="DEC(2,0)")
	private Integer sumRiskPt;

	/** 
	 * J10信用評分種類<p/>
	 * A：實際評分(J10_SCORE應有值請檢核)<br/>
	 *  B：無法評分(代碼為001~015)<br/>
	 *  C：無法評分，且揭露理由為有信用不良紀錄，且目前無正常授信帳戶或有效信用卡正卡(代碼為016~022)<br/>
	 *  D：固定評分(代碼為023~043)<br/>
	 *  N：無評分<br/>
	 *  NA：N.A(未徵提或無聯徵信用報告)
	 */
	@Size(max=2)
	@Column(name="J10_SCORE_FLAG", length=2, columnDefinition="VARCHAR(2)")
	private String j10_score_flag;

	/** J10信用評分 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="J10_SCORE", columnDefinition="DEC(3,0)")
	private Integer j10_score;

	/** 將Weighted Score相加 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="SCR_CORE", columnDefinition="DEC(8,5)")
	private BigDecimal scr_core;

	/** 核心模型分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_CORE", columnDefinition="DEC(8,5)")
	private BigDecimal std_core;

	/** 年齡 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="ITEM_M1", columnDefinition="DEC(3,0)")
	private Integer item_m1;

	/** 年齡 0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_M1", columnDefinition="DEC(3,0)")
	private BigDecimal scr_m1;

	/** 年齡std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_M1", columnDefinition="DEC(8,5)")
	private BigDecimal std_m1;

	/** 年齡權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_M1", columnDefinition="DEC(5,2)")
	private BigDecimal weight_m1;

	/** 職業 **/
	@Size(max=2)
	@Column(name="ITEM_M5", length=2, columnDefinition="VARCHAR(2)")
	private String item_m5;

	/** 職業0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_M5", columnDefinition="DEC(3,0)")
	private BigDecimal scr_m5;

	/** 職業std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_M5", columnDefinition="DEC(8,5)")
	private BigDecimal std_m5;

	/** 職業權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_M5", columnDefinition="DEC(5,2)")
	private BigDecimal weight_m5;

	/** 年資 **/
	@Column(name="ITEM_M7", columnDefinition="DEC(5,2)")
	private BigDecimal item_m7;

	/** 年資0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_M7", columnDefinition="DEC(3,0)")
	private BigDecimal scr_m7;

	/** 年資std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_M7", columnDefinition="DEC(8,5)")
	private BigDecimal std_m7;

	/** 年資權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_M7", columnDefinition="DEC(5,2)")
	private BigDecimal weight_m7;

	/** 年收入 **/
	@Digits(integer=14, fraction=3, groups = Check.class)
	@Column(name="ITEM_P2", columnDefinition="DEC(14,3)")
	private BigDecimal item_p2;

	/** 年收入0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_P2", columnDefinition="DEC(3,0)")
	private BigDecimal scr_p2;

	/** 年收入std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_P2", columnDefinition="DEC(8,5)")
	private BigDecimal std_p2;

	/** 年收入權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_P2", columnDefinition="DEC(5,2)")
	private BigDecimal weight_p2;

	/** 契約年限 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="ITEM_A5", columnDefinition="DEC(5,2)")
	private BigDecimal item_a5;

	/** 契約年限0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_A5", columnDefinition="DEC(3,0)")
	private BigDecimal scr_a5;

	/** 契約年限std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_A5", columnDefinition="DEC(8,5)")
	private BigDecimal std_a5;

	/** 契約年限權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_A5", columnDefinition="DEC(5,2)")
	private BigDecimal weight_a5;

	/** 擔保品地點及種類 **/
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ITEM_Z1", columnDefinition="DEC(1,0)")
	private Integer item_z1;

	/** 擔保品地點及種類0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_Z1", columnDefinition="DEC(3,0)")
	private BigDecimal scr_z1;

	/** 擔保品地點及種類std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_Z1", columnDefinition="DEC(8,5)")
	private BigDecimal std_z1;

	/** 擔保品地點及種類權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_Z1", columnDefinition="DEC(5,2)")
	private BigDecimal weight_z1;

	/** 市場環境及變現性 **/
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ITEM_Z2", columnDefinition="DEC(1,0)")
	private Integer item_z2;

	/** 市場環境及變現性 0~100分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_Z2", columnDefinition="DEC(3,0)")
	private BigDecimal scr_z2;

	/** 市場環境及變現性std **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="STD_Z2", columnDefinition="DEC(8,5)")
	private BigDecimal std_z2;

	/** 市場環境及變現性權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_Z2", columnDefinition="DEC(5,2)")
	private BigDecimal weight_z2;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="VARCHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="VARCHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 違約機率(預估3年期) **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="DR_3YR", columnDefinition="DEC(8,5)")
	private BigDecimal dr_3yr;

	/** 違約機率(預估1年期) **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="DR_1YR", columnDefinition="DEC(8,5)")
	private BigDecimal dr_1yr;

	/** 升降等-依風險點數{升等1；降等-1} **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ADJ_PTS", columnDefinition="DEC(2,0)")
	private Integer adj_pts;

	/** 升降等-聯徵{升等1；降等-1；DF: 99} **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ADJ_J10", columnDefinition="DEC(2,0)")
	private Integer adj_j10;
	
	/** 學歷 **/
	@Size(max=2)
	@Column(name="ITEM_EDU", length=2, columnDefinition="VARCHAR(2)")
	private String item_edu;

	/** 學歷分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_EDU", columnDefinition="DEC(3,0)")
	private BigDecimal scr_edu;

	/** 學歷權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_EDU", columnDefinition="DEC(5,2)")
	private BigDecimal weight_edu;
	
	/** 學歷權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_EDU", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_edu;
	
	/** 個人負債比 **/
	@Digits(integer=7, fraction=4, groups = Check.class)
	@Column(name="ITEM_DRATE", columnDefinition="DEC(7,4)")
	private BigDecimal item_drate;

	/** 個人負債比分數 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SCR_DRATE", columnDefinition="DEC(3,0)")
	private BigDecimal scr_drate;
	
	/** 個人負債比權重 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="WEIGHT_DRATE", columnDefinition="DEC(5,2)")
	private BigDecimal weight_drate;
	
	/** 個人負債比權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_DRATE", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_drate;
	
	/** 年齡權重分數分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_M1", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_m1;
	
	/** 年資權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_M7", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_m7;
	
	/** 契約年限權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_A5", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_a5;
	
	/** 職業權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_M5", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_m5;
	
	/** 年收入權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_P2", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_p2;
	
	/** 擔保品種類權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_Z1", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_z1;
	
	/** 市場環境及變現性權重分數 **/
	@Digits(integer=8, fraction=5, groups = Check.class)
	@Column(name="WEIGHT_SCR_Z2", columnDefinition="DEC(8,5)")
	private BigDecimal weight_scr_z2;
	
	
	/**
	 * 預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 */
	@Digits(integer = 9, fraction = 4, groups = Check.class)
	@Column(name = "PD", columnDefinition = "DEC(9,4)")
	private BigDecimal pd;
	
	/** 
	 * 截距<p/>
	 * 日本房貸2.0
	 */
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="INTERCEPT", columnDefinition="DECIMAL(6,4)")
	private BigDecimal interCept;

	/** 
	 * 斜率<p/>
	 * 日本房貸2.0
	 */
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="SLOPE", columnDefinition="DECIMAL(6,4)")
	private BigDecimal slope;
	

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得編製單位代號 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定編製單位代號 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得評等文件ID **/
	public String getRatingId() {
		return this.ratingId;
	}
	/** 設定評等文件ID **/
	public void setRatingId(String value) {
		this.ratingId = value;
	}

	/** 
	 * 取得評等建立日期<p/>
	 * 建立初始評等的日期
	 */
	public Date getGrdCDate() {
		return this.grdCDate;
	}
	/**
	 *  設定評等建立日期<p/>
	 *  建立初始評等的日期
	 **/
	public void setGrdCDate(Date value) {
		this.grdCDate = value;
	}

	/** 取得評等調整日期 **/
	public Date getGrdTDate() {
		return this.grdTDate;
	}
	/** 設定評等調整日期 **/
	public void setGrdTDate(Date value) {
		this.grdTDate = value;
	}

	/** 取得完成最終評等日期 **/
	public Date getRatingDate() {
		return this.ratingDate;
	}
	/** 設定完成最終評等日期 **/
	public void setRatingDate(Date value) {
		this.ratingDate = value;
	}

	/** 取得聯徵查詢日期 **/
	public Date getJcicQDate() {
		return this.jcicQDate;
	}
	/** 設定聯徵查詢日期 **/
	public void setJcicQDate(Date value) {
		this.jcicQDate = value;
	}

	/** 取得模型版本 **/
	public String getVarVer() {
		return this.varVer;
	}
	/** 設定模型版本 **/
	public void setVarVer(String value) {
		this.varVer = value;
	}

	/** 取得初始評等 **/
	public String getPRating() {
		return this.pRating;
	}
	/** 設定初始評等 **/
	public void setPRating(String value) {
		this.pRating = value;
	}

	/** 
	 * 取得獨立評等<p/>
	 * (考慮聯徵負面資訊後)
	 */
	public String getSRating() {
		return this.sRating;
	}
	/**
	 *  設定獨立評等<p/>
	 *  (考慮聯徵負面資訊後)
	 **/
	public void setSRating(String value) {
		this.sRating = value;
	}

	/** 
	 * 取得支援評等<p/>
	 * (考慮聯徵J10評分後)<br/>
	 *  當代碼016~022，支援評等降為DF。
	 */
	public String getSprtRating() {
		return this.sprtRating;
	}
	/**
	 *  設定支援評等<p/>
	 *  (考慮聯徵J10評分後)<br/>
	 *  當代碼016~022，支援評等降為DF。
	 **/
	public void setSprtRating(String value) {
		this.sprtRating = value;
	}

	/** 取得調整評等 **/
	public String getAdjRating() {
		return this.adjRating;
	}
	/** 設定調整評等 **/
	public void setAdjRating(String value) {
		this.adjRating = value;
	}

	/** 
	 * 取得最終評等<p/>
	 * (考量主觀評等)
	 */
	public String getFRating() {
		return this.fRating;
	}
	/**
	 *  設定最終評等<p/>
	 *  (考量主觀評等)
	 **/
	public void setFRating(String value) {
		this.fRating = value;
	}

	/** 取得原始最終評等 **/
	public String getOrgFr() {
		return this.orgFr;
	}
	/** 設定原始最終評等 **/
	public void setOrgFr(String value) {
		this.orgFr = value;
	}

	/** 
	 * 取得註記不需調整<p/>
	 * 1是/2否
	 */
	public String getNoAdj() {
		return this.noAdj;
	}
	/**
	 *  設定註記不需調整<p/>
	 *  1是/2否
	 **/
	public void setNoAdj(String value) {
		this.noAdj = value;
	}

	/** 
	 * 取得調整狀態<p/>
	 * 1.調升 2.調降 3.回復
	 */
	public String getAdjustStatus() {
		return this.adjustStatus;
	}
	/**
	 *  設定調整狀態<p/>
	 *  1.調升 2.調降 3.回復
	 **/
	public void setAdjustStatus(String value) {
		this.adjustStatus = value;
	}

	/** 
	 * 取得調整註記<p/>
	 * 1.淨資產2.職業3.其它
	 */
	public String getAdjustFlag() {
		return this.adjustFlag;
	}
	/**
	 *  設定調整註記<p/>
	 *  1.淨資產2.職業3.其它
	 **/
	public void setAdjustFlag(String value) {
		this.adjustFlag = value;
	}

	/** 取得調整理由 **/
	public String getAdjustReason() {
		return this.adjustReason;
	}
	/** 設定調整理由 **/
	public void setAdjustReason(String value) {
		this.adjustReason = value;
	}

	/** 取得M1出生日 **/
	public Date getRaw_m1() {
		return this.raw_m1;
	}
	/** 設定M1出生日 **/
	public void setRaw_m1(Date value) {
		this.raw_m1 = value;
	}

	/** 取得P2年薪幣別 **/
	public String getRaw_payCurr() {
		return this.raw_payCurr;
	}
	/** 設定P2年薪幣別 **/
	public void setRaw_payCurr(String value) {
		this.raw_payCurr = value;
	}

	/** 取得P2年薪金額 **/
	public BigDecimal getRaw_payAmt() {
		return this.raw_payAmt;
	}
	/** 設定P2年薪金額 **/
	public void setRaw_payAmt(BigDecimal value) {
		this.raw_payAmt = value;
	}

	/** 取得P2其它收入幣別 **/
	public String getRaw_otherCurr() {
		return this.raw_otherCurr;
	}
	/** 設定P2其它收入幣別 **/
	public void setRaw_otherCurr(String value) {
		this.raw_otherCurr = value;
	}

	/** 取得P2其它收入金額 **/
	public BigDecimal getRaw_otherAmt() {
		return this.raw_otherAmt;
	}
	/** 設定P2其它收入金額 **/
	public void setRaw_otherAmt(BigDecimal value) {
		this.raw_otherAmt = value;
	}

	/** 取得家庭所得幣別 **/
	public String getRaw_hincomeCurr() {
		return this.raw_hincomeCurr;
	}
	/** 設定家庭所得幣別 **/
	public void setRaw_hincomeCurr(String value) {
		this.raw_hincomeCurr = value;
	}

	/** 取得本次新做案下不動產租金收入幣別 **/
	public String getRaw_rincomeCurr() {
		return this.raw_rincomeCurr;
	}
	/** 設定本次新做案下不動產租金收入幣別 **/
	public void setRaw_rincomeCurr(String value) {
		this.raw_rincomeCurr = value;
	}

	/** 取得財富管理-本行幣別 **/
	public String getRaw_invMBalCurr() {
		return this.raw_invMBalCurr;
	}
	/** 設定財富管理-本行幣別 **/
	public void setRaw_invMBalCurr(String value) {
		this.raw_invMBalCurr = value;
	}

	/** 取得財富管理-它行幣別 **/
	public String getRaw_invOBalCurr() {
		return this.raw_invOBalCurr;
	}
	/** 設定財富管理-它行幣別 **/
	public void setRaw_invOBalCurr(String value) {
		this.raw_invOBalCurr = value;
	}

	/** 取得金融機構存款往來情形幣別 **/
	public String getRaw_branAmtCurr() {
		return this.raw_branAmtCurr;
	}
	/** 設定金融機構存款往來情形幣別 **/
	public void setRaw_branAmtCurr(String value) {
		this.raw_branAmtCurr = value;
	}
	
	/** 取得轉換匯率(年薪) **/
	public BigDecimal getExRate_pay() {
		return this.exRate_pay;
	}
	/** 設定轉換匯率(年薪) **/
	public void setExRate_pay(BigDecimal value) {
		this.exRate_pay = value;
	}

	/** 取得轉換匯率(其他收入) **/
	public BigDecimal getExRate_oth() {
		return this.exRate_oth;
	}
	/** 設定轉換匯率(其他收入) **/
	public void setExRate_oth(BigDecimal value) {
		this.exRate_oth = value;
	}

	/** 取得轉換匯率(家庭所得) **/
	public BigDecimal getExRate_hincome() {
		return this.exRate_hincome;
	}
	/** 設定轉換匯率(家庭所得) **/
	public void setExRate_hincome(BigDecimal value) {
		this.exRate_hincome = value;
	}

	/** 取得轉換匯率(本次新做案下不動產租金收入) **/
	public BigDecimal getExRate_rincome() {
		return this.exRate_rincome;
	}
	/** 設定轉換匯率(本次新做案下不動產租金收入) **/
	public void setExRate_rincome(BigDecimal value) {
		this.exRate_rincome = value;
	}

	/** 取得轉換匯率(財富管理-本行) **/
	public BigDecimal getExRate_invMBal() {
		return this.exRate_invMBal;
	}
	/** 設定轉換匯率(財富管理-本行) **/
	public void setExRate_invMBal(BigDecimal value) {
		this.exRate_invMBal = value;
	}

	/** 取得轉換匯率(財富管理-它行) **/
	public BigDecimal getExRate_invOBal() {
		return this.exRate_invOBal;
	}
	/** 設定轉換匯率(財富管理-它行) **/
	public void setExRate_invOBal(BigDecimal value) {
		this.exRate_invOBal = value;
	}

	/** 取得轉換匯率(金融機構存款往來情形) **/
	public BigDecimal getExRate_branAmt() {
		return this.exRate_branAmt;
	}
	/** 設定轉換匯率(金融機構存款往來情形) **/
	public void setExRate_branAmt(BigDecimal value) {
		this.exRate_branAmt = value;
	}

	/** 取得A5月份數 **/
	public Integer getRaw_a5() {
		return this.raw_a5;
	}
	/** 設定A5月份數 **/
	public void setRaw_a5(Integer value) {
		this.raw_a5 = value;
	}

	/** 
	 * 取得(同國內)CHKITEM1<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem1() {
		return this.chkItem1;
	}
	/**
	 *  設定(同國內)CHKITEM1<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem1(String value) {
		this.chkItem1 = value;
	}

	/** 取得退票 **/
	public String getChkItem1a() {
		return this.chkItem1a;
	}
	/** 設定退票 **/
	public void setChkItem1a(String value) {
		this.chkItem1a = value;
	}

	/** 取得拒往 **/
	public String getChkItem1b() {
		return this.chkItem1b;
	}
	/** 設定拒往 **/
	public void setChkItem1b(String value) {
		this.chkItem1b = value;
	}

	/** 取得信用卡強停 **/
	public String getChkItem1c() {
		return this.chkItem1c;
	}
	/** 設定信用卡強停 **/
	public void setChkItem1c(String value) {
		this.chkItem1c = value;
	}

	/** 取得催收呆帳 **/
	public String getChkItem1d() {
		return this.chkItem1d;
	}
	/** 設定催收呆帳 **/
	public void setChkItem1d(String value) {
		this.chkItem1d = value;
	}

	/** 
	 * 取得(同國內)CHKITEM2<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem2() {
		return this.chkItem2;
	}
	/**
	 *  設定(同國內)CHKITEM2<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem2(String value) {
		this.chkItem2 = value;
	}

	/** 
	 * 取得(同國內)CHKITEM4<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem4() {
		return this.chkItem4;
	}
	/**
	 *  設定(同國內)CHKITEM4<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem4(String value) {
		this.chkItem4 = value;
	}

	/** 
	 * 取得(同國內)CHKITEM5<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem5() {
		return this.chkItem5;
	}
	/**
	 *  設定(同國內)CHKITEM5<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem5(String value) {
		this.chkItem5 = value;
	}

	/** 
	 * 取得(同國內)CHKITEM7<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem7() {
		return this.chkItem7;
	}
	/**
	 *  設定(同國內)CHKITEM7<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem7(String value) {
		this.chkItem7 = value;
	}

	/** 
	 * 取得近12個月授信帳戶有出現遲延還款<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem9() {
		return this.chkItem9;
	}
	/**
	 *  設定近12個月授信帳戶有出現遲延還款<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem9(String value) {
		this.chkItem9 = value;
	}

	/** 
	 * 取得近12個月信用卡繳款狀況出現全額逾期未繳2次(含)以上<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem10() {
		return this.chkItem10;
	}
	/**
	 *  設定近12個月信用卡繳款狀況出現全額逾期未繳2次(含)以上<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem10(String value) {
		this.chkItem10 = value;
	}

	/** 
	 * 取得申貸查詢時現金卡有借款餘額(新台幣仟元)<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem11() {
		return this.chkItem11;
	}
	/**
	 *  設定申貸查詢時現金卡有借款餘額(新台幣仟元)<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem11(String value) {
		this.chkItem11 = value;
	}

	/** 
	 * 取得申貸查詢時無擔保授信(扣除學生助學貸款)餘額超過新台幣1,000仟元<p/>
	 * 1:有, 2:無, 3:NA<br/>
	 *  用c120s01e.balQdata26 - c120s01e.balQdata28
	 */
	public String getChkItem12() {
		return this.chkItem12;
	}
	/**
	 *  設定申貸查詢時無擔保授信(扣除學生助學貸款)餘額超過新台幣1,000仟元<p/>
	 *  1:有, 2:無, 3:NA<br/>
	 *  用c120s01e.balQdata26 - c120s01e.balQdata28
	 **/
	public void setChkItem12(String value) {
		this.chkItem12 = value;
	}

	/** 
	 * 取得近3個月新業務申請查詢總家數超過3次(含)以上<p/>
	 * 1:有, 2:無, 3:NA
	 */
	public String getChkItem13() {
		return this.chkItem13;
	}
	/**
	 *  設定近3個月新業務申請查詢總家數超過3次(含)以上<p/>
	 *  1:有, 2:無, 3:NA
	 **/
	public void setChkItem13(String value) {
		this.chkItem13 = value;
	}

	/** 取得累加風險點數 **/
	public Integer getSumRiskPt() {
		return this.sumRiskPt;
	}
	/** 設定累加風險點數 **/
	public void setSumRiskPt(Integer value) {
		this.sumRiskPt = value;
	}

	/** 
	 * 取得J10信用評分種類<p/>
	 * A：實際評分(J10_SCORE應有值請檢核)<br/>
	 *  B：無法評分(代碼為001~015)<br/>
	 *  C：無法評分，且揭露理由為有信用不良紀錄，且目前無正常授信帳戶或有效信用卡正卡(代碼為016~022)<br/>
	 *  D：固定評分(代碼為023~043)<br/>
	 *  N：無評分<br/>
	 *  NA：N.A(未徵提或無聯徵信用報告)
	 */
	public String getJ10_score_flag() {
		return this.j10_score_flag;
	}
	/**
	 *  設定J10信用評分種類<p/>
	 *  A：實際評分(J10_SCORE應有值請檢核)<br/>
	 *  B：無法評分(代碼為001~015)<br/>
	 *  C：無法評分，且揭露理由為有信用不良紀錄，且目前無正常授信帳戶或有效信用卡正卡(代碼為016~022)<br/>
	 *  D：固定評分(代碼為023~043)<br/>
	 *  N：無評分<br/>
	 *  NA：N.A(未徵提或無聯徵信用報告)
	 **/
	public void setJ10_score_flag(String value) {
		this.j10_score_flag = value;
	}

	/** 取得J10信用評分 **/
	public Integer getJ10_score() {
		return this.j10_score;
	}
	/** 設定J10信用評分 **/
	public void setJ10_score(Integer value) {
		this.j10_score = value;
	}

	/** 取得將Weighted Score相加 **/
	public BigDecimal getScr_core() {
		return this.scr_core;
	}
	/** 設定將Weighted Score相加 **/
	public void setScr_core(BigDecimal value) {
		this.scr_core = value;
	}

	/** 取得核心模型分數 **/
	public BigDecimal getStd_core() {
		return this.std_core;
	}
	/** 設定核心模型分數 **/
	public void setStd_core(BigDecimal value) {
		this.std_core = value;
	}

	/** 取得年齡 **/
	public Integer getItem_m1() {
		return this.item_m1;
	}
	/** 設定年齡 **/
	public void setItem_m1(Integer value) {
		this.item_m1 = value;
	}

	/** 取得年齡 0~100分數 **/
	public BigDecimal getScr_m1() {
		return this.scr_m1;
	}
	/** 設定年齡 0~100分數 **/
	public void setScr_m1(BigDecimal value) {
		this.scr_m1 = value;
	}

	/** 取得年齡std **/
	public BigDecimal getStd_m1() {
		return this.std_m1;
	}
	/** 設定年齡std **/
	public void setStd_m1(BigDecimal value) {
		this.std_m1 = value;
	}

	/** 取得年齡權重 **/
	public BigDecimal getWeight_m1() {
		return this.weight_m1;
	}
	/** 設定年齡權重 **/
	public void setWeight_m1(BigDecimal value) {
		this.weight_m1 = value;
	}

	/** 取得職業 **/
	public String getItem_m5() {
		return this.item_m5;
	}
	/** 設定職業 **/
	public void setItem_m5(String value) {
		this.item_m5 = value;
	}

	/** 取得職業0~100分數 **/
	public BigDecimal getScr_m5() {
		return this.scr_m5;
	}
	/** 設定職業0~100分數 **/
	public void setScr_m5(BigDecimal value) {
		this.scr_m5 = value;
	}

	/** 取得職業std **/
	public BigDecimal getStd_m5() {
		return this.std_m5;
	}
	/** 設定職業std **/
	public void setStd_m5(BigDecimal value) {
		this.std_m5 = value;
	}

	/** 取得職業權重 **/
	public BigDecimal getWeight_m5() {
		return this.weight_m5;
	}
	/** 設定職業權重 **/
	public void setWeight_m5(BigDecimal value) {
		this.weight_m5 = value;
	}

	/** 取得年資 **/
	public BigDecimal getItem_m7() {
		return this.item_m7;
	}
	/** 設定年資 **/
	public void setItem_m7(BigDecimal value) {
		this.item_m7 = value;
	}

	/** 取得年資0~100分數 **/
	public BigDecimal getScr_m7() {
		return this.scr_m7;
	}
	/** 設定年資0~100分數 **/
	public void setScr_m7(BigDecimal value) {
		this.scr_m7 = value;
	}

	/** 取得年資std **/
	public BigDecimal getStd_m7() {
		return this.std_m7;
	}
	/** 設定年資std **/
	public void setStd_m7(BigDecimal value) {
		this.std_m7 = value;
	}

	/** 取得年資權重 **/
	public BigDecimal getWeight_m7() {
		return this.weight_m7;
	}
	/** 設定年資權重 **/
	public void setWeight_m7(BigDecimal value) {
		this.weight_m7 = value;
	}

	/** 取得年收入 **/
	public BigDecimal getItem_p2() {
		return this.item_p2;
	}
	/** 設定年收入 **/
	public void setItem_p2(BigDecimal value) {
		this.item_p2 = value;
	}

	/** 取得年收入0~100分數 **/
	public BigDecimal getScr_p2() {
		return this.scr_p2;
	}
	/** 設定年收入0~100分數 **/
	public void setScr_p2(BigDecimal value) {
		this.scr_p2 = value;
	}

	/** 取得年收入std **/
	public BigDecimal getStd_p2() {
		return this.std_p2;
	}
	/** 設定年收入std **/
	public void setStd_p2(BigDecimal value) {
		this.std_p2 = value;
	}

	/** 取得年收入權重 **/
	public BigDecimal getWeight_p2() {
		return this.weight_p2;
	}
	/** 設定年收入權重 **/
	public void setWeight_p2(BigDecimal value) {
		this.weight_p2 = value;
	}

	/** 取得契約年限 **/
	public BigDecimal getItem_a5() {
		return this.item_a5;
	}
	/** 設定契約年限 **/
	public void setItem_a5(BigDecimal value) {
		this.item_a5 = value;
	}

	/** 取得契約年限0~100分數 **/
	public BigDecimal getScr_a5() {
		return this.scr_a5;
	}
	/** 設定契約年限0~100分數 **/
	public void setScr_a5(BigDecimal value) {
		this.scr_a5 = value;
	}

	/** 取得契約年限std **/
	public BigDecimal getStd_a5() {
		return this.std_a5;
	}
	/** 設定契約年限std **/
	public void setStd_a5(BigDecimal value) {
		this.std_a5 = value;
	}

	/** 取得契約年限權重 **/
	public BigDecimal getWeight_a5() {
		return this.weight_a5;
	}
	/** 設定契約年限權重 **/
	public void setWeight_a5(BigDecimal value) {
		this.weight_a5 = value;
	}

	/** 取得擔保品地點及種類 **/
	public Integer getItem_z1() {
		return this.item_z1;
	}
	/** 設定擔保品地點及種類 **/
	public void setItem_z1(Integer value) {
		this.item_z1 = value;
	}

	/** 取得擔保品地點及種類0~100分數 **/
	public BigDecimal getScr_z1() {
		return this.scr_z1;
	}
	/** 設定擔保品地點及種類0~100分數 **/
	public void setScr_z1(BigDecimal value) {
		this.scr_z1 = value;
	}

	/** 取得擔保品地點及種類std **/
	public BigDecimal getStd_z1() {
		return this.std_z1;
	}
	/** 設定擔保品地點及種類std **/
	public void setStd_z1(BigDecimal value) {
		this.std_z1 = value;
	}

	/** 取得擔保品地點及種類權重 **/
	public BigDecimal getWeight_z1() {
		return this.weight_z1;
	}
	/** 設定擔保品地點及種類權重 **/
	public void setWeight_z1(BigDecimal value) {
		this.weight_z1 = value;
	}

	/** 取得市場環境及變現性 **/
	public Integer getItem_z2() {
		return this.item_z2;
	}
	/** 設定市場環境及變現性 **/
	public void setItem_z2(Integer value) {
		this.item_z2 = value;
	}

	/** 取得市場環境及變現性 0~100分數 **/
	public BigDecimal getScr_z2() {
		return this.scr_z2;
	}
	/** 設定市場環境及變現性 0~100分數 **/
	public void setScr_z2(BigDecimal value) {
		this.scr_z2 = value;
	}

	/** 取得市場環境及變現性std **/
	public BigDecimal getStd_z2() {
		return this.std_z2;
	}
	/** 設定市場環境及變現性std **/
	public void setStd_z2(BigDecimal value) {
		this.std_z2 = value;
	}

	/** 取得市場環境及變現性權重 **/
	public BigDecimal getWeight_z2() {
		return this.weight_z2;
	}
	/** 設定市場環境及變現性權重 **/
	public void setWeight_z2(BigDecimal value) {
		this.weight_z2 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得違約機率(預估3年期) **/
	public BigDecimal getDr_3yr() {
		return this.dr_3yr;
	}
	/** 設定違約機率(預估3年期) **/
	public void setDr_3yr(BigDecimal value) {
		this.dr_3yr = value;
	}

	/** 取得違約機率(預估1年期) **/
	public BigDecimal getDr_1yr() {
		return this.dr_1yr;
	}
	/** 設定違約機率(預估1年期) **/
	public void setDr_1yr(BigDecimal value) {
		this.dr_1yr = value;
	}

	/** 取得升降等-依風險點數{升等1；降等-1} **/
	public Integer getAdj_pts() {
		return this.adj_pts;
	}
	/** 設定升降等-依風險點數{升等1；降等-1} **/
	public void setAdj_pts(Integer value) {
		this.adj_pts = value;
	}

	/** 取得升降等-聯徵{升等1；降等-1；DF: 99} **/
	public Integer getAdj_j10() {
		return this.adj_j10;
	}
	/** 設定升降等-聯徵{升等1；降等-1；DF: 99} **/
	public void setAdj_j10(Integer value) {
		this.adj_j10 = value;
	}
	
	/** 取得學歷 **/
	public String getItem_edu() {
		return this.item_edu;
	}
	/** 設定學歷 **/
	public void setItem_edu(String value) {
		this.item_edu = value;
	}

	/** 取得學歷分數 **/
	public BigDecimal getScr_edu() {
		return this.scr_edu;
	}
	/** 設定學歷分數 **/
	public void setScr_edu(BigDecimal value) {
		this.scr_edu = value;
	}
	
	/** 取得學歷權重 **/
	public BigDecimal getWeight_edu() {
		return this.weight_edu;
	}
	/** 設定學歷權重 **/
	public void setWeight_edu(BigDecimal value) {
		this.weight_edu = value;
	}
	
	/** 取得學歷權重分數 **/
	public BigDecimal getWeight_scr_edu() {
		return this.weight_scr_edu;
	}
	/** 設定學歷權重分數 **/
	public void setWeight_scr_edu(BigDecimal weight_scr_edu) {
		this.weight_scr_edu = weight_scr_edu;
	}

	/** 取得個人負債比 **/
	public BigDecimal getItem_drate() {
		return this.item_drate;
	}
	/** 設定個人負債比 **/
	public void setItem_drate(BigDecimal value) {
		this.item_drate = value;
	}

	/** 取得個人負債比分數 **/
	public BigDecimal getScr_drate() {
		return this.scr_drate;
	}
	/** 設定個人負債比分數 **/
	public void setScr_drate(BigDecimal value) {
		this.scr_drate = value;
	}
	
	/** 取得個人負債比權重 **/
	public BigDecimal getWeight_drate() {
		return this.weight_drate;
	}
	/** 設定個人負債比權重 **/
	public void setWeight_drate(BigDecimal value) {
		this.weight_drate = value;
	}
	
	/** 取得個人負債比權重分數 **/
	public BigDecimal getWeight_scr_drate() {
		return this.weight_scr_drate;
	}
	/** 設定個人負債比權重分數 **/
	public void setWeight_scr_drate(BigDecimal weight_scr_drate) {
		this.weight_scr_drate = weight_scr_drate;
	}
	
	/** 取得年齡權重分數**/
	public BigDecimal getWeight_scr_m1() {
		return this.weight_scr_m1;
	}
	/** 設定年齡權重分數 **/
	public void setWeight_scr_m1(BigDecimal weight_scr_m1) {
		this.weight_scr_m1 = weight_scr_m1;
	}

	/** 取得年資權重分數**/
	public BigDecimal getWeight_scr_m7() {
		return this.weight_scr_m7;
	}
	/** 設定年資權重分數 **/
	public void setWeight_scr_m7(BigDecimal weight_scr_m7) {
		this.weight_scr_m7 = weight_scr_m7;
	}
	
	/** 取得契約年限權重分數**/
	public BigDecimal getWeight_scr_a5() {
		return this.weight_scr_a5;
	}
	/** 設定契約年限權重分數 **/
	public void setWeight_scr_a5(BigDecimal weight_scr_a5) {
		this.weight_scr_a5 = weight_scr_a5;
	}
	
	/** 取得職業權重分數**/
	public BigDecimal getWeight_scr_m5() {
		return this.weight_scr_m5;
	}
	/** 設定職業權重分數 **/
	public void setWeight_scr_m5(BigDecimal weight_scr_m5) {
		this.weight_scr_m5 = weight_scr_m5;
	}
	
	/** 取得年收入權重分數**/
	public BigDecimal getWeight_scr_p2() {
		return this.weight_scr_p2;
	}
	/** 設定年收入權重分數 **/
	public void setWeight_scr_p2(BigDecimal weight_scr_p2) {
		this.weight_scr_p2 = weight_scr_p2;
	}
	
	/** 取得擔保品種類權重分數**/
	public BigDecimal getWeight_scr_z1() {
		return this.weight_scr_z1;
	}
	/** 設定擔保品種類權重分數 **/
	public void setWeight_scr_z1(BigDecimal weight_scr_z1) {
		this.weight_scr_z1 = weight_scr_z1;
	}
	
	/** 取得市場環境及變現性權重分數 **/
	public BigDecimal getWeight_scr_z2() {
		return this.weight_scr_z2;
	}
	/** 設定市場環境及變現性權重分數 **/
	public void setWeight_scr_z2(BigDecimal weight_scr_z2) {
		this.weight_scr_z2 = weight_scr_z2;
	}
	
	
	/**
	 * 取得預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 */
	public BigDecimal getPd() {
		return this.pd;
	}

	/**
	 * 設定預測壞率
	 * <p/>
	 * 計算：小數點4 位，四捨五入<br/>
	 * (1/(1+Exp(scrNum13 – A/B)))
	 **/
	public void setPd(BigDecimal value) {
		this.pd = value;
	}
	
	/** 
	 * 取得截距<p/> 
	 * 日本房貸2.0
	 */
	public BigDecimal getInterCept() {
		return this.interCept;
	}
	/**
	 *  設定截距<p/> 
	 *  日本房貸2.0
	 **/
	public void setInterCept(BigDecimal value) {
		this.interCept = value;
	}

	/** 
	 * 取得斜率<p/> 
	 * 日本房貸2.0
	 */
	public BigDecimal getSlope() {
		return this.slope;
	}
	/**
	 *  設定斜率<p/> 
	 *  日本房貸2.0
	 **/
	public void setSlope(BigDecimal value) {
		this.slope = value;
	}
	
	/**
	 * join
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C120M01A c120m01a;

	public void setC120m01a(C120M01A c120m01a) {
		this.c120m01a = c120m01a;
	}

	public C120M01A getC120m01a() {
		return c120m01a;
	}
}
