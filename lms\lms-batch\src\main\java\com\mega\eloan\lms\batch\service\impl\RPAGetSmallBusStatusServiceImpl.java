package com.mega.eloan.lms.batch.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.GWLogger;
import com.mega.eloan.common.gwclient.GWType;
import com.mega.eloan.common.service.GWLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * RPA 回傳勞工紓困貸款案件狀態
 * </pre>
 * <p>
 * LOCAL Test URL example ： http://localhost/ces-web/app/schedulerRPA
 * <p>
 * Post Request : {"serviceId":"getSmallBusStatus", "vaildIP":"N",
 * "request":{"responseCode":"1","custId":"13724746","brNo":"007",
 * "rpaUserId","078001"}}
 * <p>
 * SIT http://*************/ces-web/app/schedulerRPA
 * 
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/2,EL07623,new
 *          </ul>
 * @since 2021/6/2
 */
@Service("getSmallBusStatus")
public class RPAGetSmallBusStatusServiceImpl extends AbstractCapService
		implements WebBatchService {

	private static Logger logger = LoggerFactory
			.getLogger(RPAGetSmallBusStatusServiceImpl.class);

	@Resource
	LMS1201Service service1201;

	@Resource
	SysParameterService sysParameterService;

	@Resource
	GWLogService gwLogService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Value("${systemId}")
	private String sysId;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag;
		logger.info("getSmallBusStatus 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		GWLogger gwlogger = new GWLogger(GWType.GWTYPE_RPA, gwLogService,
				sysParameterService);

		JSONObject req = json.getJSONObject("request");
		String errorMsg = "";
		String custId = req.optString("custId", "");
		String brNo = req.optString("brNo", "");
		String rpaUserId = req.optString("rpaUserId", "");
		String responseCode = req.getString("responseCode");

		gwlogger.logBegin(sysId, custId, "getSmallBusStatus", req.toString(),
				System.currentTimeMillis());

		List<L120M01A> l120m01aList = service1201
				.findByBranchCustIdIsSmallBuss(brNo, custId);
		if (!l120m01aList.isEmpty()) {
			if (l120m01aList.size() == 1) {
				saveRPAStatus(l120m01aList.get(0), responseCode);
			} else {
				boolean findDoc = false;
				for (L120M01A l120m01a : l120m01aList) {
					if (rpaUserId.equals(l120m01a.getUpdater())) {
						logger.info("custId:{},", new Object[] { custId,
								responseCode });
						findDoc = true;
						saveRPAStatus(l120m01a, responseCode);
						break;
					}
				}
				if (!findDoc) {
					errorMsg = "查無此小規格央行C方案案件簽報書:" + custId + "!";
				}
			}
		} else {
			errorMsg = "查無此小規格央行C方案案件簽報書:" + custId + "!";
		}

		// *************************************************************************************

		// 找到最新一筆線上進件資料並更新狀態
		Map<String, Object> cesC240m01aMap = eloandbBASEService
				.findCesC240m01aLastByOwnBrIdAndCustId(brNo, custId);

		if (cesC240m01aMap != null && !cesC240m01aMap.isEmpty()) {
			// 有最新一筆線上進件
			String orgstatus4Br = Util.trim(MapUtils.getShort(cesC240m01aMap,
					"STATUS4BR"));
			String c240oid = Util.trim(MapUtils
					.getString(cesC240m01aMap, "OID"));

			if (Util.notEquals(c240oid, "")) {

				if (Util.notEquals(orgstatus4Br,
						UtilConstants.c240m01aStatus4Br.簽報書已核准)
						&& Util.notEquals(orgstatus4Br,
								UtilConstants.c240m01aStatus4Br.簽報書已婉卻)
						&& Util.notEquals(orgstatus4Br,
								UtilConstants.c240m01aStatus4Br.動審表已核准)) {
					// 如果目前線上進件最新狀態為簽報書已核准或動審表已核准:B22，就不要再被簽報書蓋掉了

					// 更新CES.C240M01A STATUS4BR
					if (Util.notEquals(responseCode, "")) {
						eloandbBASEService.updateC240m01aStatus4BrByOid(
								UtilConstants.SYS_SHORT_CODE.LMS
										+ responseCode.substring(1), c240oid);
					}
				}

			}

		}

		logger.info("getSmallBusStatus 結束========================");

		GWException gwException = null;
		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
			gwException = new GWException(errorMsg, getClass(),
					GWException.GWTYPE_RPA);
		}

		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
		} else {
			logger.info("執行成功");
		}

		mag = JSONObject
				.fromObject("{\"rc\": 0, \"rcmsg\": \"SUCCESS\", \"message\":\"執行成功\"}");
		gwlogger.logEnd(mag.toString(), gwException, "0");
		return mag;
	}

	private void saveRPAStatus(L120M01A l120m01a, String responseCode) {
		l120m01a.setApplyStatus(responseCode);
		l120m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		service1201.saveWithoutUpdater(l120m01a);
	}

}
