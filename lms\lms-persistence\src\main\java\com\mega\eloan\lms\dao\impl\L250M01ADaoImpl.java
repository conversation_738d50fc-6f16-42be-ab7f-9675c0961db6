package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L250M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L250M01A;

@Repository
public class L250M01ADaoImpl extends LMSJpaDao<L250M01A, String> implements
		L250M01ADao {

	@Override
	public L250M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L250M01A> getSimulationData(String srcMainId, String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "srcMainId",
				srcMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "l250m01b.cntrNo",
				cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				"05O");
		return find(search);

	}

}