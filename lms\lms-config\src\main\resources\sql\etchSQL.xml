<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<util:map id="eTchSql" map-class="java.util.HashMap" key-type="java.lang.String" >
		<entry key="SUCCKEYMAP.find4111ById">
			<value><![CDATA[SELECT * FROM MIS.SUCC_KEY_MAP WHERE QKEY1 = ? AND TXID = '4111' ]]></value>
		</entry>		
		<entry key="SUCCKEYMAP.find4112ById">
			<value><![CDATA[SELECT * FROM MIS.SUCC_KEY_MAP WHERE QKEY1 = ? AND TXID = '4111' ]]></value>
		</entry>
		<entry key="SUCCKEYMAP.findLastQDateById">
			<value><![CDATA[SELECT QKEY1,TXID,QDATE FROM (SELECT DD.QKEY1,DD.TXID,DD.QDATE,ROW_NUMBER() OVER(PARTITION BY DD.QKEY1,DD.TXID ORDER BY DD.QDATE DESC) AS GNUM 
			FROM MIS.SUCC_KEY_MAP DD WHERE DD.QKEY1 = ? ) T WHERE T.GNUM = 1]]></value>
		</entry>
		<entry key="MSG001.findById">
			<value><![CDATA[SELECT * FROM MIS.MSG_001 WHERE QID = ? ]]></value>
		</entry>
		<entry key="MSG001.findCntAmtById">
			<value><![CDATA[SELECT Sum(ued_dcc + ums_bcc + sd_bcc + cpe_bcc) AS totcnt, Sum(ued_bca + ums_bca + sd_bca + cpe_bca) AS totamt, Sum(por_ued_bcc + por_ums_bcc + por_sd_bcc + por_cpe_bcc) AS porcnt, Sum(por_ued_bca + por_ums_bca + por_sd_bca + por_cpe_bca) AS poramt, reject_date, qry_name FROM MIS.MSG_001 WHERE qid = ? GROUP BY qry_name, reject_date ]]></value>
		</entry>
		<!-- MIS.MSG_001 資料日期&查詢日期 -->
		<entry key="MIS.MSG_001.getDate">
			<value>
				select QDATE,END_DATE from MIS.MSG_001 where QID = ? and TXID = ?
				order by QDATE desc,END_DATE desc
			</value>
		</entry>
		<!-- MIS.MSG_001 資料查詢日期 -->
		<entry key="MIS.MSG_001.getQDate">
			<value>select max(QDATE) as QDATE from MIS.MSG_001 where QID = ? and TXID = ?</value>
		</entry>
		<!-- MIS.MSG_004 退票日期 -->
		<entry key="MIS.MSG_004.getBcDate">
			<value>select bc_date from MIS.MSG_004 where QID = ? order by bc_date DESC</value>
		</entry>
		<!-- MIS.MSG_001 大額退票、拒絕往來情形 -->
		<entry key="MIS.MSG_001.getData">
			<value>select ued_dcc, ums_bcc, sd_bcc, cpe_bcc, reject_date,por_ued_bcc,por_ums_bcc,por_sd_bcc,por_cpe_bcc from MIS.MSG_001 where QID = ? </value>
		</entry>
		<!-- MIS.MSG_001 退票日期 -->
		<entry key="MIS.MSG_001.getRejectDate">
			<value>SELECT reject_date from MIS.MSG_001 where QID = ?</value>
		</entry>
		<!-- MIS.MSG_001 退票查詢-S_ConnectETCH -->
		<entry key="MIS.MSG_001.getETCHInfo1">
			<value>
				select 
				  sum(ued_dcc+ ums_bcc+ sd_bcc+ cpe_bcc) as TOTCNT, 
				  sum(ued_bca + ums_bca + sd_bca + cpe_bca) AS TOTAMT, 
				  sum(por_ued_bcc + por_ums_bcc + por_sd_bcc+ por_cpe_bcc) as PORCNT, 
				  sum(por_ued_bca + por_ums_bca + por_sd_bca + por_cpe_bca) as PORAMT, 
				  reject_date, end_date, qdate 
				from MIS.MSG_001 
				where QID = ? 
				group by qdate, end_date ,REJECT_DATE 
				order by Qdate
			</value>
		</entry>
		
		<!-- MIS.H_RESULT 查詢結果HTML -->
		<entry key="MIS.H_RESULT.getHtml">
			<value>
				select b.* from MIS.SUCC_KEY_MAP a, MIS.H_RESULT b
				where a.QKEY1 = ? and a.TXID = ? and a.QDATE = ?
				and a.SEQ_ID = b.SEQ_ID and a.QDATE = b.QDATE and a.TXID = b.TXID 
			</value>
		</entry>
		
		<entry key="MSG001.findDetailAndBanBygId">
			<value><![CDATA[SELECT SUM(UED_DCC + UMS_BCC + SD_BCC + CPE_BCC) AS TOTCNT,SUM(UED_BCA + UMS_BCA + SD_BCA + CPE_BCA) AS TOTAMT,
	          SUM(POR_UED_BCC + POR_UMS_BCC + POR_SD_BCC + POR_CPE_BCC) AS PORCNT,SUM(POR_UED_BCA + POR_UMS_BCA + POR_SD_BCA + POR_CPE_BCA) AS PORAMT,REJECT_DATE,QRY_NAME
			  FROM MIS.MSG_001 WHERE QID=? GROUP BY QRY_NAME, REJECT_DATE ]]></value>
		</entry>
		<entry key="MIS.SUCC_KEY_MAP.getLatestEtch4111QueryLog">
			<value>
				<![CDATA[
					select * from mis.SUCC_KEY_MAP where QKEY1 = ? and  TXID = '4111' order by QDATE desc
			 	]]>
			 </value>
		</entry>
		<entry key="MIS.MSG_001.getEtchInqueryDataBySeqId_TxId_Qdate_Qid">
			<value>
				<![CDATA[
					select * from mis.msg_001 WHERE SEQ_ID = ? and txid = ? and qdate = ? and QID = ?
			 	]]>
			 </value>
		</entry>
		<entry key="MIS.MSG_001.getEtchInqueryDataByTxId_Qdate_Qid">
			<value>
				<![CDATA[
					select * from mis.msg_001 WHERE txid = ? and qdate = ? and QID = ?
			 	]]>
			 </value>
		</entry>
	</util:map>
</beans>
