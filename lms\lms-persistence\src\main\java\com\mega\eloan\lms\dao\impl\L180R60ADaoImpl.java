/* 
 * L180R60ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L180R60ADao;
import com.mega.eloan.lms.model.L180R60A;

/** 振興券兌換客戶統計檔 **/
@Repository
public class L180R60ADaoImpl extends LMSJpaDao<L180R60A, String> implements
		L180R60ADao {

	@Override
	public L180R60A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		search.setMaxResults(Integer.MAX_VALUE);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180R60A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L180R60A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L180R60A> findByIndex01(String caseBrId, String custId) {
		ISearch search = createSearchTemplete();
		List<L180R60A> list = null;
		if (caseBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					caseBrId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		
		search.setMaxResults(Integer.MAX_VALUE);
		
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R60A> findByIndex02(String custId) {
		ISearch search = createSearchTemplete();
		List<L180R60A> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		
		search.setMaxResults(Integer.MAX_VALUE);
		
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R60A> findAll() {
		ISearch search = createSearchTemplete();
		search.setMaxResults(Integer.MAX_VALUE);
		List<L180R60A> list = createQuery(search).getResultList();
		
		return list;
	}

}