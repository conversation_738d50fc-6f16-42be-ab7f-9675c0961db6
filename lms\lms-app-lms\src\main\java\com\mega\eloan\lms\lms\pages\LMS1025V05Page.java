package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**
 * <pre>
 * 評等對照
 * </pre>
 * 
 * @since 2016/4/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2016/4/12,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1025v05")
public class LMS1025V05Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		//MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		addToButtonPanel(model, LmsButtonEnum.CreateExcel, LmsButtonEnum.View, LmsButtonEnum.Delete, LmsButtonEnum.UPCls);
		
		renderJsI18N(LMS1025V05Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS1025V05Page');");
	}

	// @Override
	// public String[] getJavascriptPath() {
	// return new String[] { "pagejs/lms/LMS1025V05Page.js" };
	// }

}