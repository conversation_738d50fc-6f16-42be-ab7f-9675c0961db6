package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLMS338NService;

@Service
public class MisLMS338NServiceImpl extends AbstractMFAloanJdbc implements
		MisLMS338NService {

	@Override
	public List<Map<String, Object>> findLMS338NByCustId(String custId,
			String dupNo, String ovUnitNo, String type1, String type2,
			String type3, String type4, String type5, String type6) {
		// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
		return this.getJdbc().queryForList(
				"LMS388.selByCrdtypeAndFinyear",
				new Object[] { custId, dupNo, ovUnitNo, type1, type2, type3,
						type4, type5, type6 });
	}

	@Override
	public Map<?, ?> findGradeByCrdtype(String custId, String dupNo,
			String crdtype, String brNo) {
		return this.getJdbc().queryForMap("LMS388.selByCrdtype",
				new Object[] { custId, dupNo, crdtype, brNo });
	}

	@Override
	public List<?> findCrdtypeByCustId(String custId, String dupNo, String brNo) {
		return this.getJdbc().queryForList("LMS388.selCrdtypeByCustId",
				new Object[] { custId, dupNo, brNo });
	}

	@Override
	public List<Map<String, Object>> findCrdtypeByCustIdForLgdGuarantor(
			String custId, String dupNo) {
		return this.getJdbc().queryForList(
				"LMS338N.selByCustIdForLgdGuarantor",
				new Object[] { custId, dupNo });
	}

}
