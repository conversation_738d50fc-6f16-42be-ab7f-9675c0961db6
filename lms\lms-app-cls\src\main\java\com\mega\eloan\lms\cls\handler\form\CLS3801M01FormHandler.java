package com.mega.eloan.lms.cls.handler.form;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS3801M01Page;
import com.mega.eloan.lms.cls.service.CLS3801Service;
import com.mega.eloan.lms.model.C103M01A;
import com.mega.eloan.lms.model.C103M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 訪談紀錄表
 * </pre>
 * 
 */
@Scope("request")
@Controller("cls3801m01formhandler")
public class CLS3801M01FormHandler extends AbstractFormHandler {

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMSService lmsService;

	@Resource
	CLS3801Service cls3801Service;

	@Resource
	DocLogService docLogService;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocCheckService docCheckService;
	
	@Resource
	BranchService branchService;

	MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	Properties prop = MessageBundleScriptCreator
			.getComponentResource(CLS3801M01Page.class);

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		return _saveAction(params, "Y");
	}

	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params) throws CapException {
		return _saveAction(params, "N");
	}

	private CapAjaxFormResult _saveAction(PageParameters params,
			String tempSave) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		String KEY = "saveOkFlag";
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);

		C103M01A c103m01a = null;

		if (Util.isNotEmpty(mainOid)) {
			try {
				c103m01a = cls3801Service.findModelByOid(C103M01A.class,
						mainOid);

				// 舊戶目前往來業務
				String[] oldCustBusiness = params
						.getStringArray("oldCustBusiness");
				if (oldCustBusiness != null) {
					params.put("oldCustBusiness",
							StringUtils.join(oldCustBusiness, "|"));
				}
				// 本行潛在商機
				String[] potentialMain = params.getStringArray("potentialMain");
				if (potentialMain != null) {
					params.put("potentialMain",
							StringUtils.join(potentialMain, "|"));
				}
				// 共銷潛在商機
				String[] potentialCommon = params
						.getStringArray("potentialCommon");
				if (potentialCommon != null) {
					params.put("potentialCommon",
							StringUtils.join(potentialCommon, "|"));
				}
				// 共銷潛在商機-產險種類
				String[] insurance = params.getStringArray("insurance");
				if (potentialMain != null) {
					params.put("insurance", StringUtils.join(insurance, "|"));
				}

				String alertMsg = "";

				if (Util.notEquals("Y",
						SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {

					CapBeanUtil.map2Bean(params, c103m01a);

					// 檢核資料
					alertMsg = checkDataC103M01A(c103m01a);

					cls3801Service.save(c103m01a);
					result.set(KEY, true);

					if (Util.isNotEmpty(alertMsg)) {
						result.set("alertMsg", alertMsg);
					}
				}
			} catch (Exception e) {
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}
		}
		return defaultResultC103M01A(params, c103m01a, result);
	}

	private String checkDataC103M01A(C103M01A c103m01a) {
		StringBuffer sb = new StringBuffer();
		if (c103m01a != null) {
			if (Util.isEmpty(c103m01a.getTransPotential())
					&& (Util.isNotEmpty(c103m01a.getPotentialMain())
							|| Util.isNotEmpty(c103m01a.getPotentialMainOther())
							|| Util.isNotEmpty(c103m01a.getPotentialCommon()) || Util
							.isNotEmpty(c103m01a.getInsurance()))) {
				sb.append(prop.getProperty("msg.03"));
			}
		}
		return sb.toString();
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params) throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}

	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params) throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));

		C103M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = cls3801Service.findModelByOid(C103M01A.class, mainOid);

			String errMsg = "";
			String docStatus = Util.trim(meta.getDocStatus());
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if (Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), docStatus)) {
				if (Util.isEmpty(errMsg)) {

				}
				String[] formSelectBoss = params.getStringArray("selectBoss");
				String manager = "";//Util.trim(params.getString("manager"));
				IBranch ibranch = branchService.getBranch(user.getUnitNo());
				if (ibranch != null) {
					// 取得單位主管
					manager = Util.trim(ibranch.getBrnMgr());
				}
				
				if (formSelectBoss == null || formSelectBoss.length == 0) {
					errMsg = "請輸入簽章欄";
				}
				if (Util.isEmpty(errMsg)) {
					save_c103m01e(meta.getMainId(), user, formSelectBoss,
							manager);

					nextStatus = CreditDocStatusEnum.海外_待覆核.getCode();
					_DocLogEnum = DocLogEnum.FORWARD;
				}
			} else if (Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(),
					docStatus)) {
				// 核定、退回
				if (Util.equals("核定", decisionExpr)) {
					if (Util.isEmpty(errMsg)) {

					}
					// ===============
					if (Util.isEmpty(errMsg)) {
						if (Util.equals(user.getUserId(), meta.getUpdater())) {
							/*
							 * select * from com.berrorcode where code='EFD0053'
							 * 覆核人員不可與「經辦人員或其它覆核人員」為同一人
							 */
							errMsg = RespMsgHelper.getMessage("EFD0053");
						} else {
							nextStatus = CreditDocStatusEnum.海外_已核准.getCode();
							_DocLogEnum = DocLogEnum.ACCEPT;
						}
					}

				} else if (Util.equals("退回", decisionExpr)) {
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			} else if (Util.equals(CreditDocStatusEnum.海外_已核准.getCode(),
					docStatus)) {

			}

			if (Util.isNotEmpty(errMsg)) {
				throw new CapMessageException(errMsg, getClass());
			} else {
				if (Util.isEmpty(nextStatus)) {
					throw new CapMessageException("流程異常[" + docStatus + "]",
							getClass());
				}
			}
			// =============================================
			if (true) {

				if (Util.equals(nextStatus,
						CreditDocStatusEnum.海外_已核准.getCode())) {
					Timestamp nowTS = CapDate.getCurrentTimestamp();
					meta.setApprover(user.getUserId());
					meta.setApproveTime(nowTS);

					if (true) { // L4
						List<C103M01E> exist_m01e_list = cls3801Service
								.findC103M01E(meta.getMainId());
						C103M01E managerC103M01E = get_single_staffJob(
								exist_m01e_list,
								UtilConstants.STAFFJOB.執行覆核主管L4);
						if (managerC103M01E == null) {
							managerC103M01E = new C103M01E();

							managerC103M01E.setMainId(meta.getMainId());
							managerC103M01E
									.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
							managerC103M01E.setBranchType(user.getUnitType());
							managerC103M01E.setBranchId(user.getUnitNo());
						}
						managerC103M01E.setStaffNo(meta.getApprover());
						managerC103M01E.setCreator(user.getUserId());
						managerC103M01E.setCreateTime(CapDate
								.getCurrentTimestamp());

						cls3801Service.save(managerC103M01E);
					}

				} else if (Util.equals(nextStatus,
						CreditDocStatusEnum.海外_編製中.getCode())) {
					meta.setApprover(null);
					meta.setApproveTime(null);

					if (true) { // L4
						List<C103M01E> exist_m01e_list = cls3801Service
								.findC103M01E(meta.getMainId());
						C103M01E c103m01e = get_single_staffJob(
								exist_m01e_list,
								UtilConstants.STAFFJOB.執行覆核主管L4);
						if (c103m01e != null) {
							cls3801Service.delete(c103m01e);
						}
					}
				} else if (Util.equals(nextStatus,
						CreditDocStatusEnum.海外_待覆核.getCode())) {
					meta.setApprover(null);
					meta.setApproveTime(null);
				}
				meta.setDocStatus(nextStatus);
				cls3801Service.save(meta);

				if (_DocLogEnum != null) {
					docLogService.record(meta.getOid(), _DocLogEnum);
				}
			}
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(),
					user.getUserId());
		}
		return defaultResultC103M01A(params, meta, result);
	}

	private void save_c103m01e(String mainId, MegaSSOUserDetails user,
			String[] formSelectBoss, String manager) {

		List<C103M01E> exist_m01e_list = cls3801Service.findC103M01E(mainId);
		List<C103M01E> del_m01e = new ArrayList<C103M01E>();
		List<C103M01E> save_m01e = new ArrayList<C103M01E>();

		if (true) {
			List<C103M01E> L3_list = get_list_staffJob(exist_m01e_list,
					UtilConstants.STAFFJOB.授信主管L3);
			Set<String> existSet = new HashSet<String>();
			Set<String> newSet = new HashSet<String>();
			for (C103M01E exist : L3_list) {
				String empNo = Util.trim(exist.getStaffNo());
				existSet.add(empNo);
			}
			for (String people : formSelectBoss) {
				newSet.add(Util.trim(people));
			}
			Set<String> delSet = LMSUtil.elm_onlyLeft(existSet, newSet);
			Set<String> addSet = LMSUtil.elm_onlyLeft(newSet, existSet);
			if (true) {
				for (C103M01E c103m01e : exist_m01e_list) {
					if (delSet.contains(c103m01e.getStaffNo())) {
						del_m01e.add(c103m01e);
					}
				}
				Integer seq = 1;
				for (String empNo : formSelectBoss) {
					if (!addSet.contains(empNo)) {
						continue;
					}
					C103M01E c103m01e = new C103M01E();
					c103m01e.setMainId(mainId);
					// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
					c103m01e.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
					c103m01e.setBranchType(user.getUnitType());
					c103m01e.setBranchId(user.getUnitNo());
					// =========
					c103m01e.setCreator(user.getUserId());
					c103m01e.setCreateTime(CapDate.getCurrentTimestamp());
					c103m01e.setStaffNo(empNo);
					c103m01e.setSeq(BigDecimal.valueOf(seq++));
					// =========
					save_m01e.add(c103m01e);
				}
			}
		}
		if (Util.isNotEmpty(Util.trim(manager))) {
			C103M01E managerC103M01E = get_single_staffJob(exist_m01e_list,
					UtilConstants.STAFFJOB.單位授權主管L5);
			if (managerC103M01E == null) {
				managerC103M01E = new C103M01E();

				managerC103M01E.setMainId(mainId);
				managerC103M01E.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
				managerC103M01E.setBranchType(user.getUnitType());
				managerC103M01E.setBranchId(user.getUnitNo());
			}
			managerC103M01E.setStaffNo(manager);
			managerC103M01E.setCreator(user.getUserId());
			managerC103M01E.setCreateTime(CapDate.getCurrentTimestamp());

			save_m01e.add(managerC103M01E);
		}
		if (true) {
			C103M01E apprC103M01E = get_single_staffJob(exist_m01e_list,
					UtilConstants.STAFFJOB.經辦L1);
			if (apprC103M01E == null) {
				apprC103M01E = new C103M01E();

				apprC103M01E.setMainId(mainId);
				apprC103M01E.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
				apprC103M01E.setBranchType(user.getUnitType());
				apprC103M01E.setBranchId(user.getUnitNo());
			}
			apprC103M01E.setStaffNo(user.getUserId());
			apprC103M01E.setCreator(user.getUserId());
			apprC103M01E.setCreateTime(CapDate.getCurrentTimestamp());

			save_m01e.add(apprC103M01E);
		}

		for (C103M01E c103m01e : save_m01e) {
			cls3801Service.save(c103m01e);
		}
		for (C103M01E c103m01e : del_m01e) {
			cls3801Service.delete(c103m01e);
		}
	}

	private C103M01E get_single_staffJob(List<C103M01E> exist_m01e_list,
			String staffJob) {
		List<C103M01E> list = get_list_staffJob(exist_m01e_list, staffJob);
		if (list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	private List<C103M01E> get_list_staffJob(List<C103M01E> exist_m01e_list,
			String staffJob) {
		List<C103M01E> r = new ArrayList<C103M01E>();
		for (C103M01E c310m01e : exist_m01e_list) {
			if (Util.equals(staffJob, c310m01e.getStaffJob())) {
				r.add(c310m01e);
			}
		}
		return r;
	}

	// -------------------------
	@DomainAuth(value = AuthType.Modify)
	public IResult newC103M01A(PageParameters params) throws CapException {
		C103M01A c103m01a = new C103M01A();

		CapBeanUtil.map2Bean(params, c103m01a, new String[] { "custId",
				"dupNo", "custName" });

		CapAjaxFormResult result = new CapAjaxFormResult();

		c103m01a.setTypCd(TypCdEnum.None.getCode());
		c103m01a.setMainId(IDGenerator.getUUID());
		c103m01a.setUid(IDGenerator.getUUID());
		c103m01a.setRandomCode(IDGenerator.getRandomCode());
		c103m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		c103m01a.setUnitType(UnitTypeEnum.分行.getCode());
		c103m01a.setDocURL(CapWebUtil.getDocUrl(CLS3801M01Page.class));
		c103m01a.setTxCode(Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE)));
		c103m01a.setOwnBrId(user.getUnitNo());
		c103m01a.setInterDate(CapDate.getCurrentTimestamp());//訪談日期預設當日
		c103m01a.setCreator(user.getUserId());
		c103m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c103m01a.setIsClosed(UtilConstants.DEFAULT.否);
		cls3801Service.save(c103m01a);

		result = DataParse.toResult(c103m01a);

		result.set("Success", true);
		return defaultResultC103M01A(params, c103m01a, result);

	}

	@DomainAuth(value = AuthType.Modify)
	public IResult queryC103M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C103M01A c103m01a = null;

		if (Util.isNotEmpty(mainOid)) {
			c103m01a = cls3801Service.findModelByOid(C103M01A.class, mainOid);
		} else {
			c103m01a = new C103M01A();
		}

		result = DataParse.toResult(c103m01a);

		if (true) {
			List<C103M01E> c103m01e_list = cls3801Service.findC103M01E(c103m01a
					.getMainId());
			if (!Util.isEmpty(c103m01e_list)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (C103M01E c103m01e : c103m01e_list) {
					// 要加上人員代碼
					String type = Util.trim(c103m01e.getStaffJob());
					String userId = Util.trim(c103m01e.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
		}

		return defaultResultC103M01A(params, c103m01a, result);
	}

	private CapAjaxFormResult defaultResultC103M01A(PageParameters params,
			C103M01A c103m01a, CapAjaxFormResult result) throws CapException {

		result.set("custInfo", StrUtils.concat(
				CapString.trimNull(c103m01a.getCustId()), " ",
				CapString.trimNull(c103m01a.getDupNo()), " ",
				CapString.trimNull(c103m01a.getCustName())));
		result.set(EloanConstants.PAGE,
				Util.trim(params.getString(EloanConstants.PAGE)));

		// common 要求塞值欄位
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(c103m01a.getOid()));
		result.set(EloanConstants.MAIN_ID, Util.trim(c103m01a.getMainId()));
		result.set(EloanConstants.MAIN_UID, Util.trim(c103m01a.getUid()));
		result.set(EloanConstants.DOC_STATUS,
				CapString.trimNull(c103m01a.getDocStatus()));
		result.set(EloanConstants.MAIN_DOC_STATUS,
				CapString.trimNull(c103m01a.getDocStatus()));

		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult deleteC103M01A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");

		if (oids.length > 0) {
			for (int i = 0; i < oids.length; i++) {
				C103M01A c103m01a = cls3801Service.findModelByOid(
						C103M01A.class, oids[i]);
				if (c103m01a != null) {
					c103m01a.setDeletedTime(CapDate.getCurrentTimestamp());
					cls3801Service.daoSave(c103m01a);
				}
			}
		}
		return result;
	}

}
