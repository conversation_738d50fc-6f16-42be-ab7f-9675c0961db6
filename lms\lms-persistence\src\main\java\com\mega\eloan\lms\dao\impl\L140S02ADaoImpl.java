/* 
 * L140S02ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S02A;

/** 個金產品種類檔 **/
@Repository
public class L140S02ADaoImpl extends LMSJpaDao<L140S02A, String> implements
		L140S02ADao {

	@Override
	public L140S02A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S02A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		{
			search.setOrderBy(uiSeq_seqMap());
		}
		List<L140S02A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S02A> findByMainIdOrderBySeq(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		{
			Map<String, Boolean> orderByMap = new HashMap<String, Boolean>();
			orderByMap.put("seq", false);			
			search.setOrderBy(orderByMap);
		}
		List<L140S02A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140S02A findByUniqueKey(String mainId, Integer seq) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140S02A> findByIndex01(String mainId, Integer seq) {
		ISearch search = createSearchTemplete();
		List<L140S02A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140S02A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L140S02A> list = createQuery(L140S02A.class, search)
				.getResultList();

		return list;
	}

	@Override
	public List<L140S02A> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L140S02A> list = createQuery(L140S02A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140S02A> findByMainIdAndLoanNo(String mainId, String[] loanNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "loanNo", loanNo);
		List<L140S02A> list = createQuery(L140S02A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140S02A> findByMainIdAndSecNos(String mainId, Integer[] secNos) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "secNo", secNos);
		List<L140S02A> list = createQuery(L140S02A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140S02A> findByMainIdAndSeqs(String mainId, Integer[] seqs) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "seq", seqs);
		List<L140S02A> list = createQuery(L140S02A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140S02A> findByMainIdAndSecNosIsNotNull(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "secNo", "");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "secNo", 0);
		List<L140S02A> list = createQuery(L140S02A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140S02A> findByMainIdAndCreatSrc(String mainId, String creatSrc) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "creatSrc", creatSrc);
		List<L140S02A> list = createQuery(L140S02A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140S02A> findByMainIds(String[] mainIds) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "mainId", mainIds);
		List<L140S02A> list = createQuery(L140S02A.class, search)
				.getResultList();
		return list;
	}
	

	private Map<String, Boolean> uiSeq_seqMap(){
		Map<String, Boolean> orderByMap = new HashMap<String, Boolean>();
		orderByMap.put("uiSeq", false);
		orderByMap.put("seq", false);
		return orderByMap;
	}
}