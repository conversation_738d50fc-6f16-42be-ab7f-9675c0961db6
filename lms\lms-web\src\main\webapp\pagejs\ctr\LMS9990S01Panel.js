var gridViewBox;
var initDfd = initDfd || $.Deferred();
initDfd.done(function(json){
    var LMS999S01Action = {
        /***輸入查詢ID視窗***/
        excuteQuery: function(){
            LMS999S01Action.filterGrid({
                srcMainId: json.srcMainId
            });
        },
        //grid資料篩選
        filterGrid: function(sendData){
            $("#gridViewBox").jqGrid("setGridParam", {
                postData: $.extend({}, sendData),
                search: true
            }).trigger("reloadGrid");
        },//開啟M02
        openDoc: function(cellvalue, options, rowObject){
            var actionUrl = "";
            switch (rowObject.uid) {
                case "01":
                case "08":
                case "09":
                    actionUrl = '../lms9990m02/01';
                    break;
                case "02":
                    actionUrl = '../lms9990m03/01';
                    break;
                case "03":
                    actionUrl = '../lms9990m04/01';
                    break;
                case "04":
                    actionUrl = '../lms9990m05/01';
                    break;
				case "07":
                    actionUrl = '../lms9990m09/01';
                    break;	
                case "05":
                case "06":
                case "A1":
                    actionUrl = "";
                    break;
            }
            //05,06是直接印報表
            if (actionUrl != "") {
                $.form.submit({
                    url: actionUrl,
                    data: {
                        mainId: rowObject.mainId,
                        mainDocStatus: rowObject.docStatus,
                        mainOid: rowObject.oid,
                        //由於l999m01a的contractType帶為中文資訊 uid又一定無值 所以用contractType的英文代號存入uid帶入
                        contractType: rowObject.uid,
                        txCode: $("#txCode").val()
                    },
                    target: rowObject.oid
                });
            }
            else {
                //TODO 05,06的處理    
                $.capFileDownload({
                    handler: "lmsdownloadformhandler",
                    data: {
                        mainId: rowObject.mainId,
                        contractType: rowObject.uid,
                        fileDownloadName: "LMS9990W" + rowObject.uid + ".doc",
                        serviceName: "lms9990doc01service"
                    }
                });
            }
        }
        
    };
    
    LMS999S01Action.excuteQuery();
    
	    gridViewBox = $("#gridViewBox").iGrid({
	        handler: 'lms9990gridhandler',
	        height: 235,
	        sortname: 'createTime',
	        sortorder: 'desc',
	        postData: {
	            formAction: "queryL999M01AList",
	            srcMainId: json.srcMainId
	        },
	        multiselect: false,
	        rowNum: 10,
	        colModel: [{
	            colHeader: i18n.lms9990m01['L999M01AM01.createTime'],// 建立日期
	            name: 'createTime',
	            width: 120,
	            align: "center",
	            sortable: true,
	            formatter: 'click',
	            onclick: LMS999S01Action.openDoc
	        }, {
	            colHeader: i18n.lms9990m01['L999M01AM01.contractNo'],//編號
	            name: 'contractNo',
	            width: 200,
	            align: "left",
	            sortable: true
	        }, {
	            colHeader: i18n.lms9990m01['L999M01AM01.contractType'],//種類
	            name: 'contractType',
	            width: 140,
	            align: "center",
	            sortable: true
	        }, {
	            colHeader: i18n.lms9990m01['L999M01AM01.updateTime'],//異動日期
	            name: 'updateTime',
	            width: 140,
	            align: "center",
	            sortable: true
	        }, {
	            colHeader: i18n.lms9990m01['L999M01AM01.updater'],//異動人員",
	            name: 'updater',
	            width: 140,
	            align: "center",
	            sortable: true
	        }, {
	            name: 'mainId',
	            hidden: true
	        }, {
	            name: 'oid',
	            hidden: true
	        }, {
	            name: 'docStatus',
	            hidden: true
	        }, {
	            name: 'uid',
	            hidden: true
	        }],
	        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	            var data = gridViewBox.getRowData(rowid);
	            LMS999S01Action.openDoc(null, null, data);
	        }
	    });
    //刪除約據書
    $("#deleteL999M01A").click(function(){
        var $grid = gridViewBox;
        //單筆
        var rowData = $grid.getSingleData();
        if (rowData) {
            //confirmDelete=是否確定刪除?
            API.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    $.ajax({
                        handler: "lms9990m01formhandler",
                        formId: "empty",
                        action: "deleteL999M01A",
                        data: {
                            oid: rowData.oid
                        },
                    }).done(function(obj){
						$grid.reload();						
					});
                }
            });
        }
    });
});
