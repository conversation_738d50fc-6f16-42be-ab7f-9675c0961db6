/*
 * IDataObject.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.model;

import java.io.Serializable;
import java.lang.reflect.Field;

/**
 * <pre>
 * interface IDataObject.
 * Oid定義規範
 * </pre>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/7,iristu,new
 *          </ul>
 */
public interface IDataObject extends Serializable {

    /**
     * oid 在資料庫定義的長度, 此長度影響 {@link #getOidWithFullLength()} 時自動補空白與否
     * <p>
     * 
     * @return
     * @see #getOidWithFullLength()
     */
    public static final int OID_DEF_LENGTH = 32;

    /**
     * Gets the oid.
     * 
     * @return the oid
     */
    String getOid();

    /**
     * Sets the oid.
     * 
     * @param oid
     *            the new oid
     */
    void setOid(String oid);

    /**
     * 針對 {@link IDataObject} 的子類別補足 oid 長度
     * <p>
     * 
     * @param oid
     * @param clz
     * @return
     */
    public static Serializable getOidWithFullLength(Serializable oid, Class<?> clz) {

        // null 不處理
        if (oid == null)
            return oid;

        // 只處理 oid 字串 ... 目前都是定義為字串
        if (!String.class.isAssignableFrom(oid.getClass()))
            return oid;

        // 只處理 IDataObject
        if (!IDataObject.class.isAssignableFrom(clz))
            return oid;

        // 取出 entity 設定的 長度, 如果沒有設定, 就由 IDataObject 取
        int oidDefLength;
        try {
            Field field = clz.getDeclaredField("OID_DEF_LENGTH");
            oidDefLength = field.getInt(null);
        } catch (NoSuchFieldException | IllegalArgumentException | IllegalAccessException e) {
            oidDefLength = OID_DEF_LENGTH;
        }

        // 長度已足夠, 不處理
        String oidStr = (String) oid;
        if (oidStr.length() >= oidDefLength)
            return oidStr;

        // 此 method 真正要處理的部份 --- 補足長度 ---
        StringBuilder s = new StringBuilder(oidStr);
        while (s.length() < oidDefLength)
            s.append(' ');

        return s.toString();
    }
}
