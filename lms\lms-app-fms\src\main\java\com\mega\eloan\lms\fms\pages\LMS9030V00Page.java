package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.pages.LMSCommomPage;

/**<pre>
 * 簽報書移轉單位
 * </pre>
 * @since  2013/1/25
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/1/25,<PERSON>,new
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms9030v00")
public class LMS9030V00Page extends AbstractEloanInnerView {

	public LMS9030V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.DOC_EDITING);
		// 加上Button

		renderJsI18N(LMS9030V00Page.class);
		renderJsI18N(LMSCommomPage.class);
	}// ;

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS9030V00Page.js" };
	}
}
