/* 
 * PTEAMAPP.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 團貸年度總額度檔 **/
public class PTEAMAPP extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 公司統一編號
	 * <p/>
	 * 公司統一編號
	 */
	@Column(name = "CUSTID", length = 10, columnDefinition = "CHAR(10)", unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(01)", unique = true)
	private String dupno;

	/**
	 * 年度
	 * <p/>
	 * 年度
	 */
	@Column(name = "YEAR", length = 3, columnDefinition = "CHAR(3)", unique = true)
	private String year;

	/**
	 * 額度申請序號
	 * <p/>
	 * 額度申請序號
	 */
	@Column(name = "AMTAPPNO", length = 2, columnDefinition = "CHAR(2)", unique = true)
	private String amtappno;

	/**
	 * 總額度有效起日
	 * <p/>
	 * 總額度有效起日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "EFFFROM", columnDefinition = "Date")
	private Date efffrom;

	/**
	 * 總額度有效迄日
	 * <p/>
	 * 總額度有效迄日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "EFFEND", columnDefinition = "Date")
	private Date effend;

	/**
	 * 總申請額度
	 * <p/>
	 * 總申請額度
	 */
	@Column(name = "TOTAMT", columnDefinition = "DECIMAL(12)")
	private BigDecimal totamt;

	/**
	 * 剩餘額度
	 * <p/>
	 * 剩餘額度
	 */
	@Column(name = "OVERAMT", columnDefinition = "DECIMAL(12)")
	private BigDecimal overamt;

	/**
	 * 資料修改人(行員代號)
	 * <p/>
	 * 資料修改人(行員代號)
	 */
	@Column(name = "UPDATER", length = 8, columnDefinition = "CHAR(08)")
	private String updater;

	/**
	 * 資料修改日期
	 * <p/>
	 * 資料修改日期
	 */
	@Column(name = "TMESTAMP", columnDefinition = "TIMESTAMP")
	private Date tmestamp;

	/** 團貸編號 **/
	@Column(name = "GRPCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String grpcntrno;

	/** 團貸戶戶名 **/
	@Column(name = "PROJECTNM", length = 82, columnDefinition = "CHAR(82)")
	private String projectnm;

	/** 子戶id **/
	@Column(name = "SUBCOMPID", length = 10, columnDefinition = "CHAR(10)")
	private String subcompid;

	/** 子戶戶名 **/
	@Column(name = "SUBCOMPNM", length = 82, columnDefinition = "CHAR(82)")
	private String subcompnm;

	/** 團貸總戶編號 **/
	@Column(name = "MGRPCNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String mgrpcntrno;

	/** 建案名稱 **/
	@Column(name = "BUILDNAME", length = 62, columnDefinition = "CHAR(62)")
	private String buildname;

	/** 報案分行 **/
	@Column(name = "ISSUEBRNO", length = 3, columnDefinition = "CHAR(3)")
	private String issuebrno;

	/** 縣市 **/
	@Column(name = "SITE1", length = 22, columnDefinition = "CHAR(22)")
	private String site1;

	/** 鄉鎮市區 **/
	@Column(name = "SITE2", length = 22, columnDefinition = "CHAR(22)")
	private String site2;

	/** 段 **/
	@Column(name = "SITE3", length = 22, columnDefinition = "CHAR(22)")
	private String site3;

	/** 小段 **/
	@Column(name = "SITE4", length = 22, columnDefinition = "CHAR(22)")
	private String site4;
	
	/**
	 * 取消日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CANCEL_DATE", columnDefinition = "Date")
	private Date cancel_date;
	
	/**
	 * 敘作理由
	 */
	@Column(name = "TODOREASON", columnDefinition = "CHAR(1)")
	private String toDoReason;
	
	/**
	 * 不動產分區
	 */
	@Column(name = "ESTATEAREA", columnDefinition = "VARCHAR(2)")
	private String estateArea;
	
	/**
	 * 土融額度序號
	 */
	@Column(name = "LANDCNTRNO", columnDefinition = "CHAR(12)")
	private String landCntrNo;
	
	/**
	 * 建融額度序號
	 */
	@Column(name = "CONSCNTRNO", columnDefinition = "CHAR(12)")
	private String consCntrNo;
	
	/**
	 * 預計完工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "EXPFINIDATE", columnDefinition = "Date")
	private Date expFiniDate;
	
	/**
	 * 整批房貸額度不再動用註記
	 */
	@Column(name = "NOUSINGMARK", columnDefinition = "CHAR(1)")
	private String noUsingMark;

	/**
	 * 取得公司統一編號
	 * <p/>
	 * 公司統一編號
	 */
	public String getCustid() {
		return this.custid;
	}

	/**
	 * 設定公司統一編號
	 * <p/>
	 * 公司統一編號
	 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}

	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/**
	 * 取得年度
	 * <p/>
	 * 年度
	 */
	public String getYear() {
		return this.year;
	}

	/**
	 * 設定年度
	 * <p/>
	 * 年度
	 **/
	public void setYear(String value) {
		this.year = value;
	}

	/**
	 * 取得額度申請序號
	 * <p/>
	 * 額度申請序號
	 */
	public String getAmtappno() {
		return this.amtappno;
	}

	/**
	 * 設定額度申請序號
	 * <p/>
	 * 額度申請序號
	 **/
	public void setAmtappno(String value) {
		this.amtappno = value;
	}

	/**
	 * 取得總額度有效起日
	 * <p/>
	 * 總額度有效起日
	 */
	public Date getEfffrom() {
		return this.efffrom;
	}

	/**
	 * 設定總額度有效起日
	 * <p/>
	 * 總額度有效起日
	 **/
	public void setEfffrom(Date value) {
		this.efffrom = value;
	}

	/**
	 * 取得總額度有效迄日
	 * <p/>
	 * 總額度有效迄日
	 */
	public Date getEffend() {
		return this.effend;
	}

	/**
	 * 設定總額度有效迄日
	 * <p/>
	 * 總額度有效迄日
	 **/
	public void setEffend(Date value) {
		this.effend = value;
	}

	/**
	 * 取得總申請額度
	 * <p/>
	 * 總申請額度
	 */
	public BigDecimal getTotamt() {
		return this.totamt;
	}

	/**
	 * 設定總申請額度
	 * <p/>
	 * 總申請額度
	 **/
	public void setTotamt(BigDecimal value) {
		this.totamt = value;
	}

	/**
	 * 取得剩餘額度
	 * <p/>
	 * 剩餘額度
	 */
	public BigDecimal getOveramt() {
		return this.overamt;
	}

	/**
	 * 設定剩餘額度
	 * <p/>
	 * 剩餘額度
	 **/
	public void setOveramt(BigDecimal value) {
		this.overamt = value;
	}

	/**
	 * 取得資料修改人(行員代號)
	 * <p/>
	 * 資料修改人(行員代號)
	 */
	public String getUpdater() {
		return this.updater;
	}

	/**
	 * 設定資料修改人(行員代號)
	 * <p/>
	 * 資料修改人(行員代號)
	 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/**
	 * 取得資料修改日期
	 * <p/>
	 * 資料修改日期
	 */
	public Date getTmestamp() {
		return this.tmestamp;
	}

	/**
	 * 設定資料修改日期
	 * <p/>
	 * 資料修改日期
	 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}

	/** 取得團貸編號 **/
	public String getGrpcntrno() {
		return this.grpcntrno;
	}

	/** 設定團貸編號 **/
	public void setGrpcntrno(String value) {
		this.grpcntrno = value;
	}

	/** 取得團貸戶戶名 **/
	public String getProjectnm() {
		return this.projectnm;
	}

	/** 設定團貸戶戶名 **/
	public void setProjectnm(String value) {
		this.projectnm = value;
	}

	/** 取得子戶id **/
	public String getSubcompid() {
		return this.subcompid;
	}

	/** 設定子戶id **/
	public void setSubcompid(String value) {
		this.subcompid = value;
	}

	/** 取得子戶戶名 **/
	public String getSubcompnm() {
		return this.subcompnm;
	}

	/** 設定子戶戶名 **/
	public void setSubcompnm(String value) {
		this.subcompnm = value;
	}

	/** 取得團貸總戶編號 **/
	public String getMgrpcntrno() {
		return this.mgrpcntrno;
	}

	/** 設定團貸總戶編號 **/
	public void setMgrpcntrno(String value) {
		this.mgrpcntrno = value;
	}

	/** 取得建案名稱 **/
	public String getBuildname() {
		return this.buildname;
	}

	/** 設定建案名稱 **/
	public void setBuildname(String value) {
		this.buildname = value;
	}

	/** 取得報案分行 **/
	public String getIssuebrno() {
		return this.issuebrno;
	}

	/** 設定報案分行 **/
	public void setIssuebrno(String value) {
		this.issuebrno = value;
	}

	/** 取得縣市 **/
	public String getSite1() {
		return this.site1;
	}

	/** 設定縣市 **/
	public void setSite1(String value) {
		this.site1 = value;
	}

	/** 取得鄉鎮市區 **/
	public String getSite2() {
		return this.site2;
	}

	/** 設定鄉鎮市區 **/
	public void setSite2(String value) {
		this.site2 = value;
	}

	/** 取得段 **/
	public String getSite3() {
		return this.site3;
	}

	/** 設定段 **/
	public void setSite3(String value) {
		this.site3 = value;
	}

	/** 取得小段 **/
	public String getSite4() {
		return this.site4;
	}

	/** 設定小段 **/
	public void setSite4(String value) {
		this.site4 = value;
	}

	/**
	 * 設定取消日
	 */
	public void setCancel_date(Date cancel_date) {
		this.cancel_date = cancel_date;
	}

	/**
	 * 取得取消日
	 */
	public Date getCancel_date() {
		return cancel_date;
	}

	public String getToDoReason() {
		return toDoReason;
	}

	public void setToDoReason(String toDoReason) {
		this.toDoReason = toDoReason;
	}

	public String getEstateArea() {
		return estateArea;
	}

	public void setEstateArea(String estateArea) {
		this.estateArea = estateArea;
	}

	public String getLandCntrNo() {
		return landCntrNo;
	}

	public void setLandCntrNo(String landCntrNo) {
		this.landCntrNo = landCntrNo;
	}

	public String getConsCntrNo() {
		return consCntrNo;
	}

	public void setConsCntrNo(String consCntrNo) {
		this.consCntrNo = consCntrNo;
	}

	public Date getExpFiniDate() {
		return expFiniDate;
	}

	public void setExpFiniDate(Date expFiniDate) {
		this.expFiniDate = expFiniDate;
	}

	public String getNoUsingMark() {
		return noUsingMark;
	}

	public void setNoUsingMark(String noUsingMark) {
		this.noUsingMark = noUsingMark;
	}
}
