package com.mega.eloan.lms.crs.service.impl;


import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.common.CrsRuleVO;
import com.mega.eloan.lms.crs.pages.LMS2401M01Page;
import com.mega.eloan.lms.crs.pages.LMS2405M01Page;
import com.mega.eloan.lms.crs.pages.LMS2411M01Page;
import com.mega.eloan.lms.crs.pages.LMS2420M01Page;
import com.mega.eloan.lms.crs.service.LMS2400Service;
import com.mega.eloan.lms.crs.service.LMS2401Service;
import com.mega.eloan.lms.dao.C240A01ADao;
import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.dao.C240M01ZDao;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01BDao;
import com.mega.eloan.lms.dao.C241M01CDao;
import com.mega.eloan.lms.dao.C241M01GDao;
import com.mega.eloan.lms.dao.C241M01ZDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.service.MisELF491Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C240M01Z;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.eloan.lms.model.C241M01G;
import com.mega.eloan.lms.model.C241M01Z;
import com.mega.eloan.lms.model.C242M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;


@Service("LMS2400Service")
public class LMS2400ServiceImpl extends AbstractCapService implements
		LMS2400Service {
	private static final Logger logger = LoggerFactory.getLogger(LMS2400ServiceImpl.class);

	@Resource
	BranchService branchService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	C240M01ADao c240m01aDao;
	
	@Resource
	C240M01ZDao c240m01zDao;
	
	@Resource
	C241M01ADao c241m01aDao;
	
	@Resource
	C241M01BDao c241m01bDao;

	@Resource
	C241M01CDao c241m01cDao;
		
	@Resource
	C240A01ADao c240a01aDao;

	@Resource
	MisMISLN20Service misMISLN20Service;
	
	@Resource
	MisLNF030Service misLNF030Service;
	
	@Resource
	MisELF491Service misELF491Service;
	
	@Resource
	MisdbBASEService misdbBaseService;
		
	@Resource
	MisELLNGTEEService misELLNGTEEService;
	
	@Resource
	MisElCUS25Service misElCUS25Service;
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Autowired
	DocFileService docFileService;
		
	@Resource
	RetrialService retrialService;
	
	@Resource
	LMS2401Service lms2401Service;
		
	@Resource
	UserInfoService userInfoService;
	
	@Resource 
	FlowSimplifyService flowSimplifyService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	EmailClient emailClient;
	
	@Resource
	CodeTypeService codeTypeService;

	@Resource
	C241M01ZDao c241m01zDao;
	
	@Resource
	C241M01GDao c241m01gDao;
	
	
	@Override
	public Map<String, Object> checkAlreadyHave(String branch, String dateYM) {
		Date date = CapDate.parseDate(CrsUtil.getDataEndDate(dateYM));
		List<C240M01A> c240m01as = c240m01aDao.findByBranchAndDataDate(branch,
				date);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("success", true);
		if (CollectionUtils.isNotEmpty(c240m01as) ) {			
			for (Meta c240m01a : c240m01as) {
				if (Util.equals(RetrialDocStatusEnum.編製中.getCode(), c240m01a.getDocStatus())){
					map.put("success", false);
					break;
				}
			}
		}
		return map;
	}
	
	@Override
	public void c240m01a_ExpectedRetrialDate(C240M01A c240m01a, Date defaultDate)
			throws CapException {
		for(C241M01A c241m01a: retrialService.findC241M01A_C240M01A(c240m01a.getMainId())){
			//上傳過,不替換  實際覆審日
			if( CrsUtil.haveBeenUpload(c241m01a)){
				continue;
			}
			if(CrsUtil.isNull_or_ZeroDate(c241m01a.getShouldReviewDate())
					&& CrsUtil.isOnly8_1(c241m01a)){
				c241m01a.setShouldReviewDate(defaultDate);
			}
			if(CrsUtil.isNull_or_ZeroDate(c241m01a.getShouldReviewDate())
					&& Util.equals(CrsUtil.C241M01A_NCREATEDATA_USR, c241m01a.getNCreatData())){
				c241m01a.setShouldReviewDate(defaultDate);
			}
			
			/*
			 * notes 的程式,不覆審的案件, 也不清空 RetrialDate
			 */			
			c241m01a.setRetrialDate(defaultDate);
			retrialService.save(c241m01a);
		}
		retrialService.save(c240m01a);
	}
	
	
	public void _saveNoCTL(C240M01A meta, List<String> oidArr, 
			String nCkdFlag, String nCkdDetail, 
			String nckdFlagO_date, List<C241M01A> skipArr_upload, List<C241M01A> skipArr_R98)
	throws CapException{
		Timestamp nowTimestamp = CapDate.getCurrentTimestamp();	
				
		Map<String, String> nckdFlagMap = retrialService.get_crs_NckdFlagMap();	
		List<C241M01A> list = new ArrayList<C241M01A>();
		Properties prop_lms2401m01 = MessageBundleScriptCreator.getComponentResource(LMS2401M01Page.class);
		for(String oid : oidArr){
			C241M01A c241m01a = retrialService.findC241M01A_oid(oid);
			if(c241m01a==null){
				continue;
			}
			
			// 如果已經有上傳日期註記時，則不可做不覆審註記
			if (CrsUtil.haveBeenUpload(c241m01a)) {
				skipArr_upload.add(c241m01a);
				continue;
			}
			
			// J-107-0192 異常通報案件，不可註記為不覆審
			if(CrsUtil.notAllowedNckdFlag_R98(nCkdFlag) && CrsUtil.parseRule(c241m01a.getRetrialKind()).keySet().contains(CrsUtil.R98)){
				List<Map<String, Object>> lnf0854Map = misdbBaseService.findLnfe0854UnClosedByCustIdAndBrNo(
						c241m01a.getCustId(), c241m01a.getDupNo(), meta.getBranchId());
				if (lnf0854Map != null && !lnf0854Map.isEmpty()) {
					boolean bool_val = true;
					if(Util.equals(CrsUtil.NCKDFLAG_O, nCkdFlag)){
						ELF491 elf491 = misELF491Service.findByPk(meta.getBranchId(), c241m01a.getCustId(), c241m01a.getDupNo());
						if(elf491!=null 
								&& CrsUtil.isNOT_null_and_NOTZeroDate(elf491.getElf491_crdate()) 
								&& LMSUtil.cmp_yyyyMM(elf491.getElf491_crdate(), ">", c241m01a.getShouldReviewDate())){
							bool_val = false;
						}
					}
					if(bool_val){
						skipArr_R98.add(c241m01a);
						continue;
					}	
				}
			}
			
			if (CrsUtil.C241M01A_NCREATEDATA_SYS.equals(c241m01a.getNCreatData())) {
				
				c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_N);
				
				c241m01a.setNCkdFlag(nCkdFlag);
				c241m01a.setNckdDetail(nCkdDetail);
				
				if(Util.equals(CrsUtil.NCKDFLAG_O, nCkdFlag)){
					//label.NCKDFLAG_O_MENO=本案已存在於{0}覆審工作底稿中
					c241m01a.setNCkdMemo(MessageFormat.format(prop_lms2401m01.getProperty("label.NCKDFLAG_O_MENO"), Util.trim(nckdFlagO_date)));
				}else{
					c241m01a.setNCkdMemo(nckdFlagMap.get(c241m01a.getNCkdFlag()));	
				}				
				
				c241m01a.setNCkdDate(nowTimestamp);
				// 為避免還原時又要重捉資料，故保留當時的值
				c241m01a.setRetrialDateOld(c241m01a.getRetrialDate());
				
				list.add(c241m01a);				
			} else {				
				lms2401Service.deleteC241_classes(c241m01a);				
			}				
		}
		for(C241M01A bean:list){
			retrialService.save(bean);		
		}
		{//依 c241m01a 上傳 elf491
			List<ELF491> elf491_list = new ArrayList<ELF491>();
			List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
			
			retrialService.up491_at_save_NoCtl_ReCtl(elf491_list, c241m01z_list, meta.getBranchId(), list);
			
			retrialService.upELF491_DelThenInsert(elf491_list);
			retrialService.saveC241M01Z(c241m01z_list);
		}		
		//---
		lms2401Service.c240m01a_reQuantity_thisSamplingCount(meta);		
		retrialService.save(meta);
		
	}
	
	@Override
	public void saveNoCTL(C240M01A meta, List<String> oidArr, 
			String nCkdFlag, String nCkdDetail, List<C241M01A> skipArr_upload, List<C241M01A> skipArr_R98)throws CapException{
		_saveNoCTL(meta, oidArr, nCkdFlag, nCkdDetail, null, skipArr_upload, skipArr_R98);
	}
	
	@Override
	public void saveNoCTL_O(C240M01A meta, C240M01A cmpMeta)throws CapException{
		Set<String> retrialYSet = new HashSet<String>();
		for(C241M01A cmp_c241m01a : retrialService.findC241M01A_C240M01A(cmpMeta.getMainId())){
			//一般戶 且 [要覆審]
			if(Util.equals("Y", cmp_c241m01a.getRetrialYN()) && CrsUtil.isCaseN(cmp_c241m01a)){
				retrialYSet.add(LMSUtil.getCustKey_len10custId(cmp_c241m01a.getCustId(), cmp_c241m01a.getDupNo()));
			}
		}
		
		List<String> oidArr = new ArrayList<String>();
		for(C241M01A c241m01a : retrialService.findC241M01A_C240M01A(meta.getMainId())){
			if(Util.equals("Y", c241m01a.getRetrialYN()) 
					&& retrialYSet.contains(LMSUtil.getCustKey_len10custId(c241m01a.getCustId(), c241m01a.getDupNo()))){
				oidArr.add(c241m01a.getOid());
			}
		}
		List<C241M01A> skipArr_upload = new ArrayList<C241M01A>();
		List<C241M01A> skipArr_R98 = new ArrayList<C241M01A>();
		String nCkdDetail = "";
		_saveNoCTL(meta, oidArr, CrsUtil.NCKDFLAG_O, nCkdDetail, TWNDate.toAD(cmpMeta.getExpectedRetrialDate()), skipArr_upload, skipArr_R98);
	}
	
	@Override
	public void saveReCTL(C240M01A meta, List<String> oidArr) throws CapException{
		
		List<C241M01A> list = new ArrayList<C241M01A>();
		for(String oid : oidArr){
			C241M01A c241m01a = retrialService.findC241M01A_oid(oid);
			if(c241m01a==null){
				continue;
			}
			
			c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);
			c241m01a.setNCkdFlag(null);
			c241m01a.setNckdDetail("");
			c241m01a.setNCkdDate(null);
			c241m01a.setNCkdMemo(null);
			// 為避免還原時又要重捉資料，故保留當時的值
			if(CrsUtil.isNOT_null_and_NOTZeroDate(c241m01a.getRetrialDateOld())){
				c241m01a.setRetrialDate(c241m01a.getRetrialDateOld());	
			}
			
			list.add(c241m01a);
		}
		for(C241M01A bean:list){
			retrialService.save(bean);
		}
		{//依 c241m01a 上傳 elf491
			List<ELF491> elf491_list = new ArrayList<ELF491>();
			List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
			
			retrialService.up491_at_save_NoCtl_ReCtl(elf491_list, c241m01z_list, meta.getBranchId(), list);
			
			retrialService.upELF491_DelThenInsert(elf491_list);
			retrialService.saveC241M01Z(c241m01z_list);
		}
		//---
		lms2401Service.c240m01a_reQuantity_thisSamplingCount(meta);		
		retrialService.save(meta);		
	}
	
	@Override
	public boolean produce_attch(C240M01A c240m01a, String fieldId) throws Exception {
		boolean st = false;
		
		String srcFileName = CrsUtil.getExcelName(c240m01a, fieldId);
		List<String> existDocOid = new ArrayList<String>();
		try {			
			DocFile docFile = new DocFile();
			docFile.setBranchId(c240m01a.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(c240m01a.getMainId());
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setSrcFileName(srcFileName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(docFileService.getSysId());
			docFile.setData(new byte[] {});
			{
				for(DocFile exist :docFileService.findByIDAndName(docFile.getMainId(), docFile.getFieldId(), docFile.getSrcFileName())){
					existDocOid.add(exist.getOid());
				}
			}
			docFileService.save(docFile, false);
			
			// Excel Success
			if(Util.equals(fieldId,CrsUtil.ATTCH_C240M01A_0)){
				st = _produceExcel(c240m01a, docFile);	
			}else if(Util.equals(fieldId,CrsUtil.ATTCH_C240M01A_1)){
				st = _producePreExcel(c240m01a, docFile);
			}else if(Util.equals(fieldId,CrsUtil.ATTCH_C240M01A_2)){
				st = _produceChkExcel(c240m01a, docFile);
			}			

			for(String docOid : existDocOid){
				retrialService.delfile(docOid);
			}
		} finally {

		}
		return st;
	}
	
	private String[] _produce_0_row(){
		String[] arr = new String[28];
		arr[0] = "";//序號	
		arr[1] = "";//授信戶ID
		arr[2] = "";//戶名	
		arr[3] = "";//共同借款人	
		arr[4] = "";//會計科子細目代號	
		arr[5] = "";//會計科子細目名稱	
		arr[6] = "";//額度序號	
		arr[7] = "";//幣別	
		arr[8] = "";//授信額度	
		arr[9] = "";//幣別	
		arr[10] = "";//授信餘額	
		arr[11] = "";//循環別	
		arr[12] = "";//動用起迄日	
		arr[13] = "";//帳務檔建立日期	
		arr[14] = "";//授信契約起迄日	
		arr[15] = "";//融資業務分類代號	
		arr[16] = "";//融資業務分類名稱	
		arr[17] = "";//產品種類代號	
		arr[18] = "";//產品種類名稱	
		arr[19] = "";//擔保品類別	
		arr[20] = "";//行員別		
		arr[21] = "";//案號(for 第九類)	
		arr[22] = "";//覆審類別	
		arr[23] = "";//新/舊	                       
		arr[24] = "";//逾期天數
		arr[25] = "";//團體消貸案號
		arr[26] = "";//前次覆審日期	
		arr[27] = "";//最遲應覆審期限(yyyy/MM)
		return arr;	
	}
	private void _produce_0_row_C241M01A(String[] arr , C241M01A c241m01a, int seq, Map<String, String> yesNoMap, Properties prop_lms2405m01 ){
		arr[0] = Util.addZeroWithValue(seq, 3);//序號	
		arr[1] = Util.trim(c241m01a.getCustId())+" "+ Util.trim(c241m01a.getDupNo());//授信戶ID
		arr[2] = Util.trim(c241m01a.getCustName());//戶名
		arr[20] = Util.trim(yesNoMap.get(Util.nullToSpace(c241m01a.getStaff())));//行員別
		arr[22] = Util.trim(c241m01a.getRetrialKind());//覆審類別
		String newadd = "";
		if ("Y".equals(c241m01a.getNewCase())) {
			newadd = prop_lms2405m01.getProperty("new");
		} else {
			newadd = prop_lms2405m01.getProperty("old");
		}
		arr[23] = Util.trim(newadd);//新/舊
		arr[25] = Util.trim(c241m01a.getGrpCntrNo());//團體消貸案號
		arr[26] = Util.trim(TWNDate.toAD(c241m01a.getLastRetrialDate()));//前次覆審日期	
		arr[27] = Util.trim(TWNDate.toAD(c241m01a.getShouldReviewDate()));//最遲應覆審期限(yyyy/MM)
	}
	private void _produce_0_row_C241M01B(String[] arr , C241M01B c241m01b, Map<String, String> lnPurposeMap, Map<String, String> prodKindNameMap){
		arr[3] = Util.trim(c241m01b.getCoBorrower());//共同借款人	
		arr[4] = Util.trim(c241m01b.getActcd());//會計科子細目代號	
		arr[5] = Util.trim(c241m01b.getSubjectName());//會計科子細目名稱	
		arr[6] = Util.trim(c241m01b.getQuotaNo());//額度序號	
		arr[7] = Util.trim(c241m01b.getQuotaCurr());//幣別	
		arr[8] = CrsUtil.amtDivide1000(c241m01b.getQuotaAmt());//授信額度	
		arr[9] = Util.trim(c241m01b.getBalCurr());//幣別	
		arr[10] = CrsUtil.amtDivide1000(c241m01b.getBalAmt());//授信餘額	
		arr[11] = Util.trim(c241m01b.getReVolve());//循環別	
		arr[12] = Util.trim(TWNDate.toAD(c241m01b.getUseFDate()))+ "~"+ Util.trim(TWNDate.toAD(c241m01b.getUseEDate()));//動用起迄日	
		arr[13] = Util.trim(TWNDate.toAD(c241m01b.getLNF030CrtDate()));//帳務檔建立日期
		
		Date[] loanUseArr = CrsUtil.get_Use_Loan_PeriodDate(c241m01b);
		arr[14] = Util.trim(TWNDate.toAD(loanUseArr[0]))+ "~"+ Util.trim(TWNDate.toAD(loanUseArr[1]));
			
		arr[15] = Util.trim(c241m01b.getLnBusiness());//融資業務分類代號	
		arr[16] = Util.trim(lnPurposeMap.get(Util.trim(c241m01b.getLnBusiness())));//融資業務分類名稱	
		arr[17] = Util.trim(c241m01b.getLnType());//產品種類代號
		arr[18] = Util.trim(prodKindNameMap.get(Util.trim(c241m01b.getLnType())));//產品種類名稱	
		arr[19] = Util.trim(c241m01b.getGuaranteeKind());//擔保品類別
		arr[21] = Util.trim(c241m01b.getPrivateNo());//案號(for 第九類)
		arr[24] = Util.trim(c241m01b.getOverDueDate());//逾期天數
	}
	private boolean _produceExcel(C240M01A c240m01a,
			 DocFile docFile) throws Exception {
		String filename = null;
		File file = null;
		File file2 = null;
		filename = LMSUtil.getUploadFilePath(c240m01a.getOwnBrId(), c240m01a.getMainId(), docFile.getFieldId());
		String xlsOid = docFile.getOid();
		file = new File(filename);
		file.mkdirs();

		Properties prop_lms2405m01 = MessageBundleScriptCreator
				.getComponentResource(LMS2405M01Page.class);
		
		Label label = null;
		try {
			String path = PropUtil.getProperty("loadFile.dir")+ "excel/"+"C240M01AExcel.xls";
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(path);
			if (urlRpt == null)
				throw new Exception("get File fail");
			file = new File(urlRpt.toURI());
			Workbook workbook = Workbook.getWorkbook(file);
			file2 = new File(filename + "/" + xlsOid + ".xls");
			WritableWorkbook test = Workbook.createWorkbook(file2, workbook);

			WritableSheet sheet = test.getSheet(0);

			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(true);
				cellFormatL.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellFormatC = new WritableCellFormat(headFont12);
			{
				cellFormatC.setAlignment(Alignment.CENTRE);
				cellFormatC.setWrap(true);
				cellFormatC.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellFormatR = new WritableCellFormat(headFont12);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatR.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellNFormatL = new WritableCellFormat(headFont12);
			{
				cellNFormatL.setWrap(true);
				cellNFormatL.setAlignment(Alignment.LEFT);	
			}
			
			WritableCellFormat cellNFormatR = new WritableCellFormat(headFont12);
			{
				cellNFormatR.setWrap(true);
				cellNFormatR.setAlignment(Alignment.RIGHT);	
			}
			
			// 分行代碼
			label = new Label(0, 1, prop_lms2405m01.getProperty("branchNO")
					+ Util.trim(c240m01a.getBranchId())
					+ " "
					+ branchService.getBranchName(c240m01a.getBranchId()), cellNFormatL);
			sheet.addCell(label);

			
			String dataDate1 = "";			
			String dataDate2 = "";
			if (Util.isNotEmpty(c240m01a.getDataEndDate())) {//本次覆審資料截至
				dataDate1 = Util.trim(TWNDate.toAD(c240m01a.getDataEndDate()));
				dataDate2 = Util.trim(TWNDate.toAD(c240m01a.getDataEndDate()));
			}
			//【資料日期】新案：截至dataDate1止敘做／舊案：最遲應覆審期限dataDate2止(註)
			label = new Label(0, 2, prop_lms2405m01.getProperty("excelTitle1")
					.replace("dataDate1", dataDate1)
					.replace("dataDate2", dataDate2), cellNFormatL);
			sheet.addCell(label);

			List<C241M01A> list = retrialService.grid_C241M01A_default(c240m01a.getMainId());			
			int total = 0;
			
			Map<String, String> yesNoMap = retrialService.get_common_codeTypeMap("Common_YesNo");			
			Map<String, String> lnPurposeMap = retrialService.get_crs_lnPurposeMap();
			Map<String, String> prodKindNameMap = retrialService.get_crs_prodKindMap();
			
			int sizeP = 0;
			int sizeC = 0;
			List<String[]> rows = new ArrayList<String[]>();
					
			for (int i_list=0; i_list< list.size() ; i_list++) {
				C241M01A c241m01a = list.get(i_list);	
				List<C241M01B> c241m01bs = retrialService.findC241M01B_c241m01a(c241m01a);
				int cnt = (Math.max(1, c241m01bs.size()));
				if(Util.equals("Y", c241m01a.getRetrialYN())){
					sizeP++;
					sizeC += cnt;
				}				
				for(int i_row=0; i_row<cnt ; i_row++){
					String[] arr = _produce_0_row();
					
					//---
					if(i_row==0){
						_produce_0_row_C241M01A(arr, c241m01a, (i_list+1), yesNoMap, prop_lms2405m01);
					}
					if(c241m01bs.size()>0){
						_produce_0_row_C241M01B(arr, c241m01bs.get(i_row), lnPurposeMap, prodKindNameMap);
					}
					rows.add(arr);
				}
			}
			//==============================
			int i = 0;
			for(String[] arr: rows){	
				int colLen = arr.length;
				for(int i_col = 0; i_col <colLen ; i_col++){
					//數值欄位(向右對齊)EX: 授信額度、餘額、逾期天數
					boolean alignRight = (i_col==8||i_col==10||i_col==24);
					sheet.addCell(new Label( i_col, total + i + 4, arr[i_col], alignRight?cellFormatR:cellFormatL));	
										
				}
				//---	
				i++;
			}

			sheet.mergeCells(0, total + i + 6, 3, total + i + 6);
			//預定覆審：{sizeP}戶 {sizeC}件
			label = new Label(0, total + i + 6,
					prop_lms2405m01.getProperty("preCTL") 
						+ sizeP + prop_lms2405m01.getProperty("door") 
						+ sizeC + prop_lms2405m01.getProperty("item"), cellNFormatL);
			sheet.addCell(label);

			//報表亂碼
			sheet.mergeCells(18, total + i + 6, 22, total + i + 6);
			label = new Label(18, total + i + 6,
					prop_lms2405m01.getProperty("randomCode") + "："
							+ c240m01a.getRandomCode(), cellNFormatR);
			sheet.addCell(label);

			sheet.mergeCells(0, total + i + 7, 22, total + i + 7);
			
			//lms2405m01.comment1=註一：本系統覆審名單資料庫自99/12起開始產生。
			label = new Label(0, total + i + 7,
					prop_lms2405m01.getProperty("lms2405m01.comment1"), cellNFormatL);
			sheet.addCell(label);

			//lms2405m01.comment2=註二：新案係指截至資料產生之前一個月止，尚未覆審之各月份新敘做案件；舊案係指曾覆審之舊案，其最遲應再覆審期限落在下次覆審日之前一個月(含)以前之各月份者。
			sheet.mergeCells(0, total + i + 8, 22, total + i + 8);
			label = new Label(0, total + i + 8,
					prop_lms2405m01.getProperty("lms2405m01.comment2"), cellNFormatL);
			sheet.addCell(label);

			test.write();
			test.close();

		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e) );
			throw e;
		} 
		return true;
	}
	
	private String[] _produce_1Pre_row(){
		String[] arr = new String[11];
		arr[0] = "";//序號
		arr[1] = "";//授信戶ID
		arr[2] = "";//戶名	
		arr[3] = "";//額度序號		
		arr[4] = "";//科目		
		arr[5] = "";//幣別		
		arr[6] = "";//授信額度		
		arr[7] = "";//契約起日		
		arr[8] = "";//前次覆審日期	
		arr[9] = "";//最遲應覆審期限		
		arr[10] = "";//備註
		return arr;	
	}
	
	private void _produce_1Pre_row_C241M01A(String[] arr , C241M01A c241m01a, int seq){
		arr[0] = Util.addZeroWithValue(seq, 3);//序號	
		arr[1] = Util.trim(c241m01a.getCustId())+" "+ Util.trim(c241m01a.getDupNo());//授信戶ID
		arr[2] = Util.trim(c241m01a.getCustName());//戶名
		arr[8] = Util.trim(TWNDate.toAD(c241m01a.getLastRetrialDate()));//前次覆審日期	
		arr[9] = Util.trim(TWNDate.toAD(c241m01a.getShouldReviewDate()));//最遲應覆審期限(yyyy/MM)
	}
	private void _produce_1Pre_row_C241M01B(String[] arr , C241M01B c241m01b){
		arr[3] = Util.trim(c241m01b.getQuotaNo());//額度序號		
		arr[4] = Util.trim(c241m01b.getSubjectName());//科目		
		arr[5] = Util.trim(c241m01b.getQuotaCurr());//幣別		
		arr[6] = CrsUtil.amtDivide1000(c241m01b.getQuotaAmt());//授信額度
		arr[7] = Util.trim(TWNDate.toAD(CrsUtil.get_Use_Loan_FDate(c241m01b)));		
	}	
	private boolean _producePreExcel(C240M01A c240m01a, DocFile docFile) throws Exception {

		String filename = null;
		File file = null;
		File file2 = null;
		filename = LMSUtil.getUploadFilePath(c240m01a.getOwnBrId(), c240m01a.getMainId(), docFile.getFieldId());
		String xlsOid = docFile.getOid();
		file = new File(filename);
		file.mkdirs();
		Label label = null;
		
		try {
			String tmS = "";
			{
				RetrialDocStatusEnum docStatusEnum = RetrialDocStatusEnum.getEnum(c240m01a.getDocStatus());			
				if (docStatusEnum == RetrialDocStatusEnum.編製中) {
					tmS = "--->(編製中)";
				}else if (docStatusEnum == RetrialDocStatusEnum.待覆核) {
					tmS = "--->(待覆核)";
				}	
			}	
			
			String path = "";
			if(Util.isNotEmpty(tmS)){//編製中, 待覆核:  多出 前次覆審日期	最遲應覆審日
				path = PropUtil.getProperty("loadFile.dir")+ "excel/"+"C240M01APre.xls";
			}else{
				path = PropUtil.getProperty("loadFile.dir")+ "excel/"+"C240M01Pre.xls";
			}
			
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(path);
			if (urlRpt == null)
				throw new Exception("get File fail");
			file = new File(urlRpt.toURI());
			Workbook workbook = Workbook.getWorkbook(file);
			file2 = new File(filename + "/" + xlsOid + ".xls");
			WritableWorkbook test = Workbook.createWorkbook(file2, workbook);

			WritableSheet sheet = test.getSheet(0);

			Properties prop_lms2405m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2405M01Page.class);
			Properties prop_lms2401m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2401M01Page.class);
			WritableFont _ming9 = new WritableFont(
					WritableFont.createFont("細明體"), 9);
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableFont headFont18 = new WritableFont(
					WritableFont.createFont("標楷體"), 18);
			WritableCellFormat NcellFormat18 = new WritableCellFormat(headFont18);
			{
				NcellFormat18.setAlignment(Alignment.CENTRE);	
			}			
			WritableCellFormat cellFormatC = new WritableCellFormat(headFont12);
			{
				cellFormatC.setWrap(true);
				cellFormatC.setAlignment(Alignment.CENTRE);
				cellFormatC.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}			

			WritableCellFormat cellFormatR = new WritableCellFormat(_ming9);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatR.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellFormatL = new WritableCellFormat(_ming9);
			{
				cellFormatL.setWrap(true);
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat NcellFormatR = new WritableCellFormat(headFont12);
			{
				NcellFormatR.setWrap(true);
				NcellFormatR.setAlignment(Alignment.RIGHT);	
			}
			
			WritableCellFormat NcellFormatL = new WritableCellFormat(headFont12);
			{
				NcellFormatL.setWrap(true);
				NcellFormatL.setAlignment(Alignment.LEFT);	
			}
					
			// 分行名稱
			label = new Label(0, 0, Util.nullToSpace(branchService
					.getBranchName(c240m01a.getBranchId()))
					+ "【"
					+ prop_lms2405m01.getProperty("listPreExcel") + "】"+tmS,
					NcellFormat18);
			sheet.addCell(label);
			// 分行代碼
			label = new Label(0, 1, prop_lms2405m01.getProperty("branchNO")
					+ Util.nullToSpace(c240m01a.getBranchId()), NcellFormatL);
			sheet.addCell(label);
			// 最遲覆審年月
			{
				String label_dataEndDate = "";
				if(Util.isNotEmpty(tmS)){
					label_dataEndDate = prop_lms2401m01.getProperty("label.excel.dataEndDate.1");
				}else{
					label_dataEndDate = prop_lms2401m01.getProperty("label.excel.dataEndDate.2");
				}
				label = new Label(4, 1, label_dataEndDate
						+ "："
						+ (Util.isEmpty(c240m01a.getDataEndDate()) ? "" : TWNDate
								.toAD(c240m01a.getDataEndDate()).substring(0, 7)),
						NcellFormatR);
				sheet.addCell(label);	
			}
			

			// 覆審日期
			{
				String label_expectedRetrialDate = "";
				if(Util.isNotEmpty(tmS)){
					label_expectedRetrialDate = prop_lms2401m01.getProperty("label.excel.expectedRetrialDate.1");
				}else{
					label_expectedRetrialDate = prop_lms2401m01.getProperty("label.excel.expectedRetrialDate.2");
				}
				label = new Label(0, 2, label_expectedRetrialDate
						+ "："
						+ (Util.isEmpty(c240m01a.getExpectedRetrialDate()) ? ""
								: TWNDate.toAD(c240m01a.getExpectedRetrialDate())),
						NcellFormatL);
				sheet.addCell(label);	
			}
			
			// 產生日期
			sheet.mergeCells(4, 2, 6, 2);
			label = new Label( 4, 2,
					prop_lms2405m01.getProperty("date")+ "："
							+ CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD),
					NcellFormatL);
			sheet.addCell(label);

			List<C241M01A> list = retrialService.grid_C241M01A_default(c240m01a.getMainId());			
			int total = 0;
			int sizeP = 0;
			int sizeC = 0;
			List<String[]> rows = new ArrayList<String[]>();
			for (int i_list=0; i_list< list.size() ; i_list++) {
				C241M01A c241m01a = list.get(i_list);
				if(Util.isNotEmpty(tmS)){ //編製中,待覆核					
				}else{
					//已覆核, 已傳送
					if(Util.notEquals("Y", c241m01a.getRetrialYN())){
						continue;
					}
				}
				List<C241M01B> c241m01bs = _filter(c241m01a, retrialService.findC241M01B_c241m01a(c241m01a));
				int cnt = (Math.max(1, c241m01bs.size()));
				if(Util.equals("Y", c241m01a.getRetrialYN())){
					sizeP++;
					sizeC += cnt;
				}				
				for(int i_row=0; i_row<cnt ; i_row++){
					String[] arr = _produce_1Pre_row();					
					//---
					if(i_row==0){
						int seq = 0;
						if(Util.isNotEmpty(tmS)){//編製中,待覆核
							seq = (i_list+1);							
						}else{
							//已覆核, 已傳送. 抓 projectNo
							seq = Util.parseInt(CrsUtil.seqPart2nd_FromProjectNo(c241m01a.getProjectNo()));
						}
						_produce_1Pre_row_C241M01A(arr, c241m01a, seq);
					}
					if(c241m01bs.size()>0){
						_produce_1Pre_row_C241M01B(arr, c241m01bs.get(i_row));
					}
					arr[10] = _produce_note((i_row==0?c241m01a:null), (c241m01bs.size()>0)?c241m01bs.get(i_row):null);
					rows.add(arr);
				}
			}			
			//==============================
			int i = 0;
			for(String[] arr: rows){	
				int colLen = arr.length;
				if(Util.isNotEmpty(tmS)){//編製中,待覆核
					for(int i_col = 0; i_col <colLen ; i_col++){
						//數值欄位(向右對齊)EX: 授信額度
						boolean alignRight = (i_col==6);
						sheet.addCell(new Label( i_col, total + i + 4, arr[i_col], alignRight?cellFormatR:cellFormatL));
					}
				}else{
					//缺 index 8-前次覆審日期,9-最遲應覆審期限
					/*
					arr[0] = "";//序號
					arr[1] = "";//授信戶ID
					arr[2] = "";//戶名	
					arr[3] = "";//額度序號		
					arr[4] = "";//科目		
					arr[5] = "";//幣別		
					arr[6] = "";//授信額度		
					arr[7] = "";//契約起日
					arr[10] = "";//備註
					*/
					//抓 0~7
					for(int i_col = 0; i_col <8 ; i_col++){
						//數值欄位(向右對齊)EX: 授信額度
						boolean alignRight = (i_col==6);
						sheet.addCell(new Label( i_col, total + i + 4, arr[i_col], alignRight?cellFormatR:cellFormatL));
					}
					//指定8的值
					sheet.addCell(new Label( 8, total + i + 4, arr[10], cellFormatL));
				}
				//---	
				i++;
			}

			total = total + i;
			//預定覆審：{sizeP}戶 {sizeC}件
			sheet.mergeCells(0, total + 6, 2, total + 6);
			label = new Label(0, total + 6, prop_lms2405m01.getProperty("preCTL")
					+ sizeP + prop_lms2405m01.getProperty("door") 
					+ sizeC + prop_lms2405m01.getProperty("item"), NcellFormatL);
			sheet.addCell(label);

			// 報表亂碼
			sheet.mergeCells(4, total + 6, (Util.isNotEmpty(tmS)?10:8), total + 6);
			label = new Label(4, total + 6,
					prop_lms2405m01.getProperty("randomCode") + "："
							+ Util.trim(c240m01a.getRandomCode()),
					NcellFormatR);
			sheet.addCell(label);

			test.write();
			test.close();

		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e) );
			throw e;
		}
		return true;
	}
	
	private List<C241M01B> _filter(C241M01A c241m01a, List<C241M01B> src){
		List<C241M01B> r = new ArrayList<C241M01B>();
		if(CollectionUtils.isNotEmpty(src)){
			//TODO 參考 gfnDataProcessToExcelToBranch. 過濾 DUFromDate<100/02/01 及特定會計科目 及 類別9
			String[] condActArr = new String[]{"13506200","13506300","14501500"
					,"14502000","13500100","14501000","13505000","14502500","13502000"};
			Date d1 = CapDate.parseDate("2011-02-01");
			Date d2 = CapDate.parseDate("2007-04-01");
			
			Set<String> ruleSet = CrsUtil.parseRule(c241m01a.getRetrialKind()).keySet();
			boolean hasR9 = ruleSet.contains(CrsUtil.R9);
			boolean noR99 = !ruleSet.contains(CrsUtil.R99);
			
			for(C241M01B c241m01b : src){
				/*
				 '如果曾經覆審過且科目屬於case所列的範圍則不出在名單中
				 */
				if(CrsUtil.isNOT_null_and_NOTZeroDate(c241m01b.getDateOfReview())
						&& hasR9==false
						&& CrsUtil.inCollection(c241m01b.getActcd(), condActArr)						
						&&( CrsUtil.isNOT_null_and_NOTZeroDate(c241m01b.getLoanFDate()) && LMSUtil.cmpDate(c241m01b.getLoanFDate(),"<", d1) )
					){
					continue;
				}
				
				/*
				 '假如該額度契約起日小於96/04則不帶入
				 */
				if((CrsUtil.isNOT_null_and_NOTZeroDate(c241m01b.getUseFDate()) && LMSUtil.cmpDate(c241m01b.getUseFDate(),"<", d2))
					&& noR99 && c241m01b.getOverDueDate()==0 ){
					continue;
				}
				r.add(c241m01b);
			}
		}
		return r; 
	}
		
	private String[] _produce_2Chk_row(){
		String[] arr = new String[13];
		
		arr[0] = "";//序號	
		arr[1] = "";//覆審序號	
		arr[2] = "";//授信戶ID		
		arr[3] = "";//戶名			
		arr[4] = "";//額度序號			
		arr[5] = "";//會計科目			
		arr[6] = "";//幣別			
		arr[7] = "";//授信額度			
		arr[8] = "";//契約起日		
		arr[9] = "";//是否上傳			
		arr[10] = "";//不覆審註記
		arr[11] = "";//覆審人員	
		arr[12] = "";//備註
		return arr;	
	}
	
	private void _produce_2Chk_row_C241M01A(String[] arr , C241M01A c241m01a, int seq
			, String c240m01a_hqAppraiserId, String hqAppraiserName){
		arr[0] = Util.addZeroWithValue(seq, 3);//序號
		arr[1] = CrsUtil.seqPart2nd_FromProjectNo(c241m01a.getProjectNo());//覆審序號	
		arr[2] = Util.trim(c241m01a.getCustId())+" "+ Util.trim(c241m01a.getDupNo());//授信戶ID		
		arr[3] = Util.trim(c241m01a.getCustName());//戶名			
				
		arr[9] = CrsUtil.haveBeenUpload(c241m01a)?"Y":"N";//是否上傳			
		arr[10] = Util.trim(c241m01a.getNCkdFlag());//不覆審註記		
		
		String retrialL1 = "";
		if(CrsUtil.haveBeenUpload(c241m01a)){
			C241M01E c241m01e = retrialService.findC241M01E_first_L1(c241m01a, "2");
			retrialL1 = (c241m01e==null?hqAppraiserName:c241m01e.getStaffName());
		}
		arr[11] = retrialL1;//覆審人員
	}
	private void _produce_2Chk_row_C241M01B(String[] arr , C241M01B c241m01b){
		arr[4] = Util.trim(c241m01b.getQuotaNo());//額度序號			
		arr[5] = Util.trim(c241m01b.getSubjectName());//會計科目			
		arr[6] = Util.trim(c241m01b.getQuotaCurr());//幣別			
		arr[7] = CrsUtil.amtDivide1000(c241m01b.getQuotaAmt());//授信額度			
		arr[8] = Util.trim(TWNDate.toAD(CrsUtil.get_Use_Loan_FDate(c241m01b)));		
	}

	private List<C241M01A> _filter_ChkExcel(C240M01A c240m01a, List<C241M01A> srcList){
		List<C241M01A> r = new ArrayList<C241M01A>();
		for(C241M01A model: srcList){
			if(Util.isNotEmpty(Util.trim(model.getProjectNo()))
				|| (CrsUtil.isNOT_null_and_NOTZeroDate(model.getShouldReviewDate()) && LMSUtil.cmpDate(model.getShouldReviewDate(), "<=", c240m01a.getDataEndDate()))
			){
				if(Util.equals(CrsUtil.NCKDFLAG_O, model.getNCkdFlag())){
					
				}else{
					r.add(model);	
				}				
			}
		}
		return r;
	}
	private boolean _produceChkExcel(C240M01A c240m01a, DocFile docFile) throws Exception {

		String filename = null;
		File file = null;
		File file2 = null;
		filename = LMSUtil.getUploadFilePath(c240m01a.getOwnBrId(), c240m01a.getMainId(), docFile.getFieldId());
		String xlsOid = docFile.getOid();
		file = new File(filename);
		file.mkdirs();
		Label label = null;
		
		try {			
			String path = PropUtil.getProperty("loadFile.dir")+ "excel/"+"C240M01AChk.xls";
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(path);
			if (urlRpt == null)
				throw new Exception("get File fail");
			file = new File(urlRpt.toURI());
			Workbook workbook = Workbook.getWorkbook(file);
			file2 = new File(filename + "/" + xlsOid + ".xls");
			WritableWorkbook test = Workbook.createWorkbook(file2, workbook);

			WritableSheet sheet = test.getSheet(0);

			Properties prop_lms2405m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2405M01Page.class);
			
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableFont headFont11 = new WritableFont(
					WritableFont.createFont("標楷體"), 11);
			WritableFont headFont9 = new WritableFont(
					WritableFont.createFont("標楷體"), 9);
			WritableCellFormat NcellFormat12 = new WritableCellFormat(
					headFont12);
			NcellFormat12.setAlignment(Alignment.CENTRE);
			WritableCellFormat NcellFormat11 = new WritableCellFormat(
					headFont11);
			WritableCellFormat NcellFormat9 = new WritableCellFormat(headFont9);
			WritableCellFormat NcellFormatC = new WritableCellFormat(headFont9);
			{
				NcellFormatC.setWrap(true);
				NcellFormatC.setAlignment(Alignment.CENTRE);	
			}
			
			WritableCellFormat NcellFormatR = new WritableCellFormat(headFont9);
			{
				NcellFormatR.setWrap(true);
				NcellFormatR.setAlignment(Alignment.RIGHT);	
			}
			
			WritableCellFormat NcellFormatL = new WritableCellFormat(headFont9);
			{
				NcellFormatL.setWrap(true);
				NcellFormatL.setAlignment(Alignment.LEFT);	
			}
			
			WritableCellFormat cellFormatC = new WritableCellFormat(headFont9);
			{
				cellFormatC.setWrap(true);
				cellFormatC.setAlignment(Alignment.CENTRE);
				cellFormatC.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellFormatR = new WritableCellFormat(headFont9);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatR.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont9);
			{
				cellFormatL.setWrap(true);
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}			
						
			// 分行名稱
			label = new Label(0, 0, Util.nullToSpace(branchService
					.getBranchName(c240m01a.getBranchId()))
					+ "【"
					+ prop_lms2405m01.getProperty("listChkExcel") + "】",
					NcellFormat12);
			sheet.addCell(label);
			// 分行代碼
			label = new Label(0, 1, prop_lms2405m01.getProperty("branchNO")
					+ Util.nullToSpace(c240m01a.getBranchId()), NcellFormat11);
			sheet.addCell(label);
			// 覆審日期
			label = new Label(0, 2, prop_lms2405m01.getProperty("CTLDate")
					+ "："
					+ (Util.isEmpty(c240m01a.getExpectedRetrialDate()) ? ""
							: TWNDate.toAD(c240m01a.getExpectedRetrialDate())
									.substring(0, 10)), NcellFormat9);
			sheet.addCell(label);


			List<C241M01A> list = _filter_ChkExcel(c240m01a, retrialService.grid_C241M01A_default(c240m01a.getMainId()));
			int total = 0;
			int sizeP = 0;
			int sizeC = 0;
			List<String[]> rows = new ArrayList<String[]>();
			String hqAppraiserName = Util.trim(userInfoService.getUserName(c240m01a.getHqAppraiserId()));
				
			for (int i_list=0; i_list< list.size() ; i_list++) {
				C241M01A c241m01a = list.get(i_list);
				List<C241M01B> c241m01bs = retrialService.findC241M01B_c241m01a(c241m01a);
				int cnt = (Math.max(1, c241m01bs.size()));
				if(Util.equals("Y", c241m01a.getRetrialYN())){
					sizeP++;
					
					//cnt 影響後面的 i_row 呈現 (依 loanNo)
					//另新增 cntrNocnt  (依 cntrNo)
					int cntrNocnt = cnt;
					if(true){
						//932劉炳宏襄理比對 實際覆審戶數及件數統計表 及 消金授信覆審驗證名單
						//發現○戶○件，「件數」一個是用額度，一個是用帳號
						//建議統一用 "額度"(考量企金)
						Set<String> cntrNoSet = new HashSet<String>();
						for(C241M01B c241m01b: c241m01bs){
							cntrNoSet.add(Util.trim(c241m01b.getQuotaNo()));
						}
						cntrNocnt = (Math.max(1, cntrNoSet.size()));//團貸母戶,也要算1筆
					}
					sizeC += cntrNocnt;
				}
				for(int i_row=0; i_row<cnt ; i_row++){
					String[] arr = _produce_2Chk_row();
					
					//---
					if(i_row==0){
						_produce_2Chk_row_C241M01A(arr, c241m01a, (i_list+1), c240m01a.getHqAppraiserId(), hqAppraiserName);
					}
					if(c241m01bs.size()>0){
						_produce_2Chk_row_C241M01B(arr, c241m01bs.get(i_row));
					}
					arr[12] = _produce_note((i_row==0?c241m01a:null), (c241m01bs.size()>0)?c241m01bs.get(i_row):null);
					rows.add(arr);
				}
			}			
			//==============================
			int i = 0;			
			for(String[] arr: rows){	
				int colLen = arr.length;
				for(int i_col = 0; i_col <colLen ; i_col++){
					//數值欄位(向右對齊)EX: 授信額度
					boolean alignRight =(i_col==7);
					sheet.addCell(new Label( i_col, total + i + 4, arr[i_col], alignRight?cellFormatR:cellFormatL));
				}
				//---	
				i++;
			}

			//實際戶數：{sizeP}戶 {sizeC}件
			sheet.mergeCells(0, total + i + 6, 3, total + i + 6);
			label = new Label(0, total + i + 6,
					"※"+
					prop_lms2405m01.getProperty("realDoor") + "：" 
					+ sizeP + prop_lms2405m01.getProperty("door") 
					+ sizeC + prop_lms2405m01.getProperty("item"), NcellFormatL);
			sheet.addCell(label);
			// 產生人員
			sheet.mergeCells(9, total + i + 6, 12, total + i + 6);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			label = new Label(9, total + i + 6,
					prop_lms2405m01.getProperty("producer") + "："
							+ Util.trim(userInfoService.getUserName(user.getUserId())),
					NcellFormatR);
			sheet.addCell(label);
			// 報表亂碼
			sheet.mergeCells(8, total + i + 7, 12, total + i + 7);
			label = new Label(8, total + i + 7,
					prop_lms2405m01.getProperty("randomCode") + "："
							+ Util.nullToSpace(c240m01a.getRandomCode()),
					NcellFormatR);
			sheet.addCell(label);


			test.write();
			test.close();

		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e) );
			throw e;
		}
		return true;
	}
	
	@Override
	public boolean produceEjcicExcel(C240M01A c240m01a) throws Exception {
		boolean st = false;
		try {
			List<String> cvs_3a = new ArrayList<String>();
			List<String> cvs_3b = new ArrayList<String>();
			HashSet<String> custIdSet = new HashSet<String>();
			for(C241M01A c241m01a:retrialService.grid_C241M01A_default(c240m01a.getMainId())){
				if(Util.notEquals("Y", c241m01a.getRetrialYN())){
					continue;
				}
				for(C241M01B c241m01b:retrialService.findC241M01B_YnReviewY_c241m01a(c241m01a)){
					//---
					//主借款人
					custIdSet.add(c241m01b.getCustId());
					
					//從債務人
					List<Map<String, Object>> lngteeList = misELLNGTEEService.findByCrs(c241m01b.getCustId(), c241m01b.getDupNo(), c241m01b.getQuotaNo());
					for(Map<String, Object> map :lngteeList){
						custIdSet.add(Util.trim(map.get("LNGEID")));
					}
				}
			}
			
			for(String custId : custIdSet){
				if(custId.length()==10){
					if(Util.notEquals("Z", StringUtils.substring(custId, 2, 3 ))){
						cvs_3a.add(custId);
					}else{
						String jcicTxtNo = "";
						for(Map<String, Object> map : misElCUS25Service.findById(custId)){
							jcicTxtNo = Util.trim(map.get("CM25_JCIC_TAXNO"));
						}
						
						if(Util.isNotEmpty(jcicTxtNo)){
							cvs_3b.add(jcicTxtNo);
						}
					}
				}else{
					cvs_3b.add(custId);
				}
			}
			
			
			_produce_csv(c240m01a, cvs_3a, CrsUtil.ATTCH_C240M01A_3A);
			_produce_csv(c240m01a, cvs_3b, CrsUtil.ATTCH_C240M01A_3B);
			
			st = true;
		} finally {

		}

		return st;
	}
	
	@Override
	public boolean produce_grpDetail_attch(C241M01A c241m01a, String fieldId) throws Exception {
		boolean st = false;
		
		String srcFileName = CrsUtil.getExcelName(retrialService.findC240M01A_C241M01A(c241m01a), fieldId);
		List<String> existDocOid = new ArrayList<String>();
		try {			
			DocFile docFile = new DocFile();
			docFile.setBranchId(c241m01a.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(c241m01a.getMainId());
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setSrcFileName(srcFileName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(docFileService.getSysId());
			docFile.setData(new byte[] {});
			{
				for(DocFile exist :docFileService.findByIDAndName(docFile.getMainId(), docFile.getFieldId(), docFile.getSrcFileName())){
					existDocOid.add(exist.getOid());
				}
			}
			docFileService.save(docFile, false);
			
			// Excel Success
			if(Util.equals(fieldId,CrsUtil.ATTCH_C241M01A_GRPDTL)){
				st = _produce_grpDetail_attch(c241m01a, docFile);
			}			

			for(String docOid : existDocOid){
				retrialService.delfile(docOid);
			}
		} finally {

		}
		return st;
	}
	
	private boolean _produce_grpDetail_attch(C241M01A c241m01a,
			 DocFile docFile) throws Exception {
		String filename = null;
		File file = null;
		File file2 = null;
		filename = LMSUtil.getUploadFilePath(c241m01a.getOwnBrId(), c241m01a.getMainId(), docFile.getFieldId());
		String xlsOid = docFile.getOid();
		file = new File(filename);
		file.mkdirs();
		
		Label label = null;
		try {
			String path = PropUtil.getProperty("loadFile.dir")+ "excel/"+"C241M01AGrp.xls";
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(path);
			if (urlRpt == null)
				throw new Exception("get File fail");
			file = new File(urlRpt.toURI());
			Workbook workbook = Workbook.getWorkbook(file);
			file2 = new File(filename + "/" + xlsOid + ".xls");
			WritableWorkbook test = Workbook.createWorkbook(file2, workbook);

			WritableSheet sheet = test.getSheet(0);

			Properties prop_lms2411m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2411M01Page.class);
			
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableFont headFont11 = new WritableFont(
					WritableFont.createFont("標楷體"), 11);
			WritableFont headFont9 = new WritableFont(
					WritableFont.createFont("標楷體"), 9);
			WritableCellFormat NcellFormat12 = new WritableCellFormat(
					headFont12);
			NcellFormat12.setAlignment(Alignment.CENTRE);
			WritableCellFormat NcellFormat11 = new WritableCellFormat(
					headFont11);
			WritableCellFormat NcellFormat9 = new WritableCellFormat(headFont9);
			WritableCellFormat NcellFormatC = new WritableCellFormat(headFont9);
			{
				NcellFormatC.setWrap(true);
				NcellFormatC.setAlignment(Alignment.CENTRE);	
			}
			
			WritableCellFormat NcellFormatR = new WritableCellFormat(headFont9);
			{
				NcellFormatR.setWrap(true);
				NcellFormatR.setAlignment(Alignment.RIGHT);	
			}
			
			WritableCellFormat NcellFormatL = new WritableCellFormat(headFont9);
			{
				NcellFormatL.setWrap(true);
				NcellFormatL.setAlignment(Alignment.LEFT);	
			}
			
			WritableCellFormat cellFormatC = new WritableCellFormat(headFont9);
			{
				cellFormatC.setWrap(true);
				cellFormatC.setAlignment(Alignment.CENTRE);
				cellFormatC.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellFormatR = new WritableCellFormat(headFont9);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatR.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont9);
			{
				cellFormatL.setWrap(true);
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}			
						
			// 分行名稱
			label = new Label(0, 0, Util.nullToSpace(branchService
					.getBranchName(c241m01a.getOwnBrId()))
					+ "【"
					+ prop_lms2411m01.getProperty("genxls.listGrpDetail") + "】",
					NcellFormat12);
			sheet.addCell(label);
			// 分行代碼
			label = new Label(0, 1, prop_lms2411m01.getProperty("genxls.branchNO")
					+ Util.trim(c241m01a.getOwnBrId()), NcellFormat11);
			sheet.addCell(label);
			//團貸核准序號
			label = new Label(4, 1, prop_lms2411m01.getProperty("genxls.grpCntrNo")
					+ Util.trim(c241m01a.getGrpCntrNo()), NcellFormat11);
			sheet.addCell(label);
			//覆審日期
			label = new Label(0, 2, prop_lms2411m01.getProperty("genxls.CTLDate")
					+ "："
					+ Util.trim( TWNDate.toAD(c241m01a.getRetrialDate())), NcellFormat9);			
			sheet.addCell(label);
			//名單產生日期
			label = new Label(6, 2, prop_lms2411m01.getProperty("genxls.genFileDate")
					+ "："
					+ ( TWNDate.toAD(CapDate.getCurrentTimestamp())), NcellFormat9);			
			sheet.addCell(label);

			List<Map<String, Object>> list = misPTEAMAPPService.selCrsGroupDetail_orderByLoanBalDesc(c241m01a.getOwnBrId(), c241m01a.getGrpCntrNo());
			List<String[]> rows = new ArrayList<String[]>();
			Map<String, String> prodKindNameMap = retrialService.get_crs_prodKindMap();
			for (int i_list=0; i_list< list.size() ; i_list++) {
				Map<String, Object> map = list.get(i_list);
				String[] arr = new String[7];
				String custId = Util.trim(Util.getLeftStr(Util.trim(map.get("LNF020_CUST_ID")), 10));
				String dupNo = Util.getRightStr(Util.trim(map.get("LNF020_CUST_ID")), 1);
				String custName = Util.trim(map.get("CNAME"));
				
				arr[0] = Util.addZeroWithValue((i_list+1), 3);//序號
				arr[1] = custId+" "+dupNo;//授信戶ID
				arr[2] = custName;//戶名
				arr[3] = Util.trim(map.get("LNF020_CONTRACT"));//額度序號	
				arr[4] = CrsUtil.amtDivide1000(CrsUtil.parseBigDecimal(map.get("LNF020_FACT_AMT")));//授信額度
				
				String lnType = Util.trim(map.get("LNF030_LOAN_CLASS"));
				String lnTypeDesc = Util.trim(prodKindNameMap.get(Util.trim(lnType)));//產品種類名稱	
				
				arr[5] = lnType+(Util.isNotEmpty(lnTypeDesc)?(" "+lnTypeDesc):"");//產品種類
				arr[6] = CrsUtil.amtDivide1000(CrsUtil.parseBigDecimal(map.get("LNF030_LOAN_BAL")));//授信餘額
				rows.add(arr);
			}			
			//==============================
			int i = 0;		
			int total = 0;
			for(String[] arr: rows){	
				int colLen = arr.length;
				for(int i_col = 0; i_col <colLen ; i_col++){
					//數值欄位(向右對齊)EX: 授信額度
					boolean alignRight =(i_col==4 || i_col==6 );
					sheet.addCell(new Label( i_col, total + i + 4, arr[i_col], alignRight?cellFormatR:cellFormatL));
				}
				//---	
				i++;
			}
			
			// 產生人員
			sheet.mergeCells(5, total + i + 6, 6, total + i + 6);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			label = new Label(5, total + i + 6,
					prop_lms2411m01.getProperty("genxls.producer") + "："
							+ Util.trim(userInfoService.getUserName(user.getUserId())),
					NcellFormatL);
			sheet.addCell(label);
			// 報表亂碼
			sheet.mergeCells(5, total + i + 7, 6, total + i + 7);
			label = new Label(5, total + i + 7,
					prop_lms2411m01.getProperty("genxls.randomCode") + "："
							+ Util.nullToSpace(c241m01a.getRandomCode()),
					NcellFormatL);
			sheet.addCell(label);


			test.write();
			test.close();


		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e) );
			throw e;
		} 
		return true;
	}
	
	
	private void _produce_csv(C240M01A c240m01a, List<String> r, String fieldId) throws Exception{
		try{
			
			String srcFileName = CrsUtil.getExcelName(c240m01a, fieldId);
			List<String> existDocOid = new ArrayList<String>();
			
			DocFile docFile = new DocFile();
			docFile.setBranchId(c240m01a.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(c240m01a.getMainId());
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setSrcFileName(srcFileName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(docFileService.getSysId());
			
			{//處理 data
				StringBuffer sb = new StringBuffer();			
				for(String line: r){
					sb.append(line).append(CapConstants.LINE_BREAK);
				}	
				byte[] byteArray = null;
				try {
					byteArray = sb.toString().getBytes("Big5");
				} catch (UnsupportedEncodingException e) {
					byteArray = sb.toString().getBytes();
				}
				// 儲存產出檔案
				docFile.setData(byteArray);
			}
			
			{
				for(DocFile exist :docFileService.findByIDAndName(docFile.getMainId(), docFile.getFieldId(), docFile.getSrcFileName())){
					existDocOid.add(exist.getOid());
				}
			}
			
			if(r.size()>0){
				docFileService.save(docFile, true);//true-writefile
			}
			
			for(String docOid : existDocOid){
				retrialService.delfile(docOid);
			}	
		}catch(Exception e){
			logger.error(StrUtils.getStackTrace(e));
			throw e;
		}
	}
		
	private String _produce_note(C241M01A c241m01a, C241M01B c241m01b){
		ArrayList<String> r = new ArrayList<String>();
		if(c241m01b!=null && Util.isNotEmpty(c241m01b.getMajorMemo())){
			r.add(Util.trim(c241m01b.getMajorMemo()));
		}
		if(c241m01a!=null && Util.isNotEmpty(c241m01a.getNCkdFlag())){
			r.add(Util.trim(c241m01a.getNCkdMemo()));			
		}
		return Util.trim(StringUtils.join(r, "\n"));
	}
	

	@Override	
	public String c241m01a_docStatusDesc(C241M01A c241m01a){
		if(Util.isNotEmpty(c241m01a.getDocStatus())){
			Properties prop_lms2411m01 = MessageBundleScriptCreator
				.getComponentResource(LMS2411M01Page.class);
			
			String docStatus = Util.trim(c241m01a.getDocStatus());
			if(Util.equals(docStatus, RetrialDocStatusEnum.區中心_編製中.getCode())){
				String msg = checkIncompleteMsg_fast(c241m01a);
				if(Util.isNotEmpty(msg)){
					return prop_lms2411m01.getProperty("label.status_1");//覆審組編製中	
				}else{
					return prop_lms2411m01.getProperty("label.status_2");//覆審組已維護覆審事項	
				}
			}else if(Util.equals(docStatus, RetrialDocStatusEnum.區中心_待覆核.getCode())){
				if(CrsUtil.isCaseS(c241m01a)){
					return prop_lms2411m01.getProperty("label.status_7");//待覆審主管覆核
				}else{
					return prop_lms2411m01.getProperty("label.status_3");//覆審組待傳送分行
				}
			}else if(Util.equals(docStatus, RetrialDocStatusEnum.編製中.getCode())){
				return prop_lms2411m01.getProperty("label.status_4");//受檢單位編製中
			}else if(Util.equals(docStatus, RetrialDocStatusEnum.待覆核.getCode())){
				return prop_lms2411m01.getProperty("label.status_5");//待受檢單位主管覆核
			}else if(Util.equals(docStatus, RetrialDocStatusEnum.已覆核未核定.getCode())){
				return prop_lms2411m01.getProperty("label.status_6");//受檢單位已登錄完畢
			}else if(Util.equals(docStatus, RetrialDocStatusEnum.已覆核已核定.getCode())){
				return prop_lms2411m01.getProperty("label.status_9");//編製完成
			}else{
				return c241m01a.getDocStatus();	
			}
		}
		return ""; 
	}
	
	@Override
	public String c242m01a_docStatusDesc(C242M01A c242m01a){
		if(Util.isNotEmpty(c242m01a.getDocStatus())){
			Properties prop_lms2420m01 = MessageBundleScriptCreator
				.getComponentResource(LMS2420M01Page.class);
			
			String docStatus = Util.trim(c242m01a.getDocStatus());
			if(Util.equals(docStatus, RetrialDocStatusEnum.預約單_未處理.getCode())){
				return prop_lms2420m01.getProperty("lms2420m01.status.1");
			}else if(Util.equals(docStatus, RetrialDocStatusEnum.預約單_處理失敗.getCode())){
				return prop_lms2420m01.getProperty("lms2420m01.status.4");
			}else if(Util.equals(docStatus, RetrialDocStatusEnum.預約單_處理成功.getCode())){
				return prop_lms2420m01.getProperty("lms2420m01.status.3");
			}else{
				return c242m01a.getDocStatus();	
			}
		}
		return "";
	}
	
	@Override
	public String checkIncompleteMsg(C241M01A meta){
		List<String> errMsg = new ArrayList<String>();
		Properties prop_lms2411m01 = MessageBundleScriptCreator.getComponentResource(LMS2411M01Page.class);
		List<C241M01B> c241m01b_list = retrialService.findC241M01B_YnReviewY_c241m01a(meta);		
		List<C241M01C> c241m01c_list = retrialService.findC241M01C_c241m01a(meta);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		if (CrsUtil.docKindN_since_R11(meta) && Util.isEmpty(Util.trim(meta.getRealCkFg()))) {
			meta.setRealCkFg(CrsUtil.match_R11_syndType(meta.getOwnBrId(), c241m01b_list));
		}
		
		{//chk_02
			if(CrsUtil.isCaseG_Parent(meta)){
				//母戶無帳務資料
			}else{
				if(CollectionUtils.isEmpty(c241m01b_list)){
					//ui_lms2411.msg12=帳務資料皆為「不覆審」
					errMsg.add( prop_lms2411m01.getProperty("ui_lms2411.msg12") );
				}					
			}
			
			List<String> chk_02 = _checkIncompleteMsg_02(prop_lms2411m01, meta);
			if(CollectionUtils.isNotEmpty(chk_02)){
				errMsg.addAll(chk_02);
			}	
		}
		{//chk_03
			List<String> chk_03 = _checkIncompleteMsg_03(prop_lms2411m01, meta, c241m01c_list);
			if(CollectionUtils.isNotEmpty(chk_03)){
				errMsg.addAll(chk_03);
			}	
		}
		{//chk_04
			List<String> chk_04 = _checkIncompleteMsg_04(prop_lms2411m01, meta);
			if(CollectionUtils.isNotEmpty(chk_04)){
				errMsg.addAll(chk_04);
			}	
		}

		// J-110-0308 覆審考核表
		if (Util.isEmpty(Util.nullToSpace(meta.getNeedPa())) && CrsUtil.showC241M01G(user)) {
			//是否有須扣分情事不得空白
			errMsg.add(prop_lms2411m01.getProperty("ui_lms2411.msg34"));
		}
		
		{//chk_05
			List<String> chk_05 = _checkIncompleteMsg_05_c241m01g(prop_lms2411m01, meta);
			if(CollectionUtils.isNotEmpty(chk_05)){
				errMsg.addAll(chk_05);
			}	
		}
		
		// J-108-0268 逾期情形
		boolean chkOverDue = this.chkOverDue(meta, c241m01c_list);
		if(chkOverDue){
			// ui_lms2411.msg33=覆審項目：借款本息繳納是否正常？ --> 請查詢逾期情形
			// 2020/05/05 建霖說 DW資料來源不含消金戶，後續有待與授審處討論，先拿掉功能跟檢核
//			errMsg.add(prop_lms2411m01.getProperty("ui_lms2411.msg33"));
		}
		
		if(errMsg.size()>0){
			return StringUtils.join(errMsg, "<br/>");
		}else{
			return "";
		}
	}
	
	@Override
	public String checkIncompleteMsg_fast(C241M01A meta){		
		Properties prop_lms2411m01 = MessageBundleScriptCreator.getComponentResource(LMS2411M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		if(true){//chk_04
			List<String> chk_04 = _checkIncompleteMsg_04(prop_lms2411m01, meta);
			if(CollectionUtils.isNotEmpty(chk_04)){
				return "IncompleteMsg";
			}	
		}		
		
		if(true){//chk_02
			List<String> chk_02 = _checkIncompleteMsg_02(prop_lms2411m01, meta);
			if(CollectionUtils.isNotEmpty(chk_02)){
				return "IncompleteMsg";
			}
			
			if(CrsUtil.isCaseG_Parent(meta)){
				//母戶無帳務資料
			}else{
				List<C241M01B> c241m01b_list = retrialService.findC241M01B_YnReviewY_c241m01a(meta);
				if(CollectionUtils.isEmpty(c241m01b_list)){
					return "IncompleteMsg";
				}	
			}	
		}
		if(true){//chk_03
			List<C241M01C> c241m01c_list = retrialService.findC241M01C_c241m01a(meta);
			List<String> chk_03 = _checkIncompleteMsg_03(prop_lms2411m01, meta, c241m01c_list);
			if(CollectionUtils.isNotEmpty(chk_03)){
				return "IncompleteMsg";
			}	
		}

		if (Util.isEmpty(Util.nullToSpace(meta.getNeedPa())) && CrsUtil.showC241M01G(user)) {
			return "IncompleteMsg";
		}

		if(true){//chk_05
			List<String> chk_05 = _checkIncompleteMsg_05_c241m01g(prop_lms2411m01, meta);
			if(CollectionUtils.isNotEmpty(chk_05)){
				return "IncompleteMsg";
			}	
		}
		return "";
	}
	
	private List<String> _checkIncompleteMsg_02(Properties prop_lms2411m01, C241M01A meta){
		List<String> r = new ArrayList<String>(); 
		if(true){
			if(CrsUtil.isNull_or_ZeroDate(meta.getRetrialDate())){
				r.add(    prop_lms2411m01.getProperty("ui_lms2411.msg09")
						+ prop_lms2411m01.getProperty("C241M01A.retrialDate"));
			}
			
			if(meta.getRetrialDate()!=null
					&& (CrsUtil.isCaseN(meta)|| CrsUtil.isCaseG___N_Detail(meta))){
				if(LMSUtil.cmpDate(meta.getRetrialDate(), ">=", TWNDate.valueOf(CrsUtil.R11_ON_DATE) )){
					if(Util.equals(meta.getRptId(), CrsUtil.V_N_20111201) 
							||Util.equals(meta.getRptId(), CrsUtil.V_N_201412) ){
						//之前格式
						r.add("覆審日期("+TWNDate.toAD(meta.getRetrialDate())+")自 "
								+CrsUtil.R11_ON_DATE+"起，應引入最新的覆審報告表版本");
					}	
				}
			}
			if (CrsUtil.docKindN_since_R11(meta)) {
				//自 土建融實地覆審上線後 
				if(Util.equals("Y", meta.getRealCkFg())){
					//maybe A/B
				}else if(Util.equals("O", meta.getRealCkFg())){
					//maybe A/B
				}else if(Util.equals("N", meta.getRealCkFg())){
					//should only A
					if(Util.equals(CrsUtil.DOCFMT_土建融實地覆審, meta.getDocFmt())){
						r.add("帳務未含土建融，不應選擇「授信案件（含土建融實地覆審）覆審報告表	」");
					}
				}else {
					//若無 帳務含土建融[]是 []否 
					//執行「重引帳務資料」
					r.add( prop_lms2411m01.getProperty("ui_lms2411.msg09")
								+ prop_lms2411m01.getProperty("label.realCkFg"));
				}													
			}
			
			if(CrsUtil.isCaseP(meta)){
				if(Util.isEmpty(Util.trim(meta.getCustId()))){
					r.add(    prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01.getProperty("label.sellerInfo"));
				}
				if(Util.isEmpty(Util.trim(meta.getDupNo()))){
					r.add(    prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01.getProperty("label.sellerInfo")+"-"+ prop_lms2411m01.getProperty("C241M01A.dupNo"));
				}
				if(Util.isEmpty(Util.trim(meta.getCustName()))){
					r.add(    prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01.getProperty("label.sellerInfo")+"-"+ prop_lms2411m01.getProperty("label.name"));
				}
				if(Util.isEmpty(Util.trim(meta.getBuyerId()))){
					r.add(    prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01.getProperty("label.buyerInfo"));					
				}

				if(Util.isEmpty(Util.trim(meta.getBuyerDup()))){
					r.add(    prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01.getProperty("label.buyerInfo")+"-"+ prop_lms2411m01.getProperty("C241M01A.dupNo"));
				}
				if(Util.isEmpty(Util.trim(meta.getBuyerName()))){
					r.add(    prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01.getProperty("label.buyerInfo")+"-"+ prop_lms2411m01.getProperty("label.name"));
				}
				if(Util.isEmpty(Util.trim(meta.getPbAcct()))){
					r.add(    prop_lms2411m01.getProperty("ui_lms2411.msg09")
							+ prop_lms2411m01.getProperty("C241M01A.pbAcct"));
				}
			}
		}
		if(true){			
			String msg_c241m01b = _checkIncompleteMsg_02_c241m01b(prop_lms2411m01, meta);
			if(Util.isNotEmpty(Util.trim(msg_c241m01b))){
				r.add(msg_c241m01b);
			}
		}
		return r;
	}
	
	private String _checkIncompleteMsg_02_c241m01b(Properties prop_lms2411m01, C241M01A meta){
		if(clsService.is_function_on_codetype("crs_skip_lost_collKind")){
			
		}else{
			List<C241M01B> c241m01b_list = retrialService.findC241M01B_YnReviewY_c241m01a(meta);
			for(C241M01B c241m01b : c241m01b_list){
				//若同一個人含N個分行的額度，預設｛其它分行的YnReview｝='N'
				if (Util.equals("Y", c241m01b.getYnReview())	
					&& (Util.isEmpty(Util.trim(c241m01b.getGuaranteeKind())) && Util.isEmpty(Util.trim(c241m01b.getGuaranteeName())))
					&& (CrsUtil.getSubjCodeFromLNF030_LOAN_NO(c241m01b.getLoanNo()).startsWith("2")
							|| CrsUtil.getSubjCodeFromLNF030_LOAN_NO(c241m01b.getLoanNo()).startsWith("4")
							|| CrsUtil.getSubjCodeFromLNF030_LOAN_NO(c241m01b.getLoanNo()).startsWith("6"))  ){
					//擔保科目，無擔保品
					//在 testing 環境，因為沒有一直倒檔，可能  CMS.C100S03A 缺少資料
					//為避免 上傳覆審控制檔 時，因缺少 cms，導致 R1, R2 一直出現 => 增加檢核訊息
					return c241m01b.getQuotaNo()+"含擔保科目，但缺少擔保品";
				}
			}
		}
		
		return "";
	}

	private List<String> _checkIncompleteMsg_03(Properties prop_lms2411m01, C241M01A meta, List<C241M01C> c241m01c_list){
		List<String> r = new ArrayList<String>(); 
		{
			TreeSet<Integer> emptySet = new TreeSet<Integer>();
			LinkedHashSet<String> yItemEmptySet = new LinkedHashSet<String>();
			LinkedHashSet<String> zItemEmptySet = new LinkedHashSet<String>();
			
			Map<String, C241M01C> map = retrialService.toMap_keyAs_itemNo(c241m01c_list);
			if(CrsUtil.isCaseS(meta)){
				TreeMap<Integer, List<C241M01C>> tree_map = CrsUtil.caseS_to_seq(meta.getRptId(), map);
				for (Integer itemSeq : tree_map.keySet()) {
					for(C241M01C c241m01c : tree_map.get(itemSeq)){
						if(Util.isEmpty(Util.trim(c241m01c.getChkResult()))){
							emptySet.add(itemSeq);
						}	
					}
				}
			}else{
				for(C241M01C c241m01c : c241m01c_list){
					if(Util.equals(CrsUtil.Z_電腦建檔資料, c241m01c.getItemType())){
						String[] fmt = StringUtils.split(CrsUtil.chkResult_fmt(c241m01c), "|");
						String chkItem = Util.trim(c241m01c.getChkItem());
						if(fmt.length==0){
							//屬 title 類								
						}else if(fmt.length>0 && Util.isEmpty(Util.trim(c241m01c.getChkResult()))){
							String postfix_str = "";							
							if(Util.equals(CrsUtil.ZB1A, c241m01c.getItemNo()) 
									|| Util.equals(CrsUtil.ZB2A, c241m01c.getItemNo())){								
								String desc_1st = fmt.length>0?prop_lms2411m01.getProperty("label."+fmt[0]):"";			
								String desc_2nd = fmt.length>1?prop_lms2411m01.getProperty("label."+fmt[1]):"";
								
								String fmt_str = " "+desc_1st+"|"+desc_2nd+" ";
								if(Util.equals(CrsUtil.ZB1A, c241m01c.getItemNo())){
									postfix_str = fmt_str + CrsUtil.Z_DESC_ZB1A_N;
								}else if(Util.equals(CrsUtil.ZB2A, c241m01c.getItemNo())){
									postfix_str = fmt_str + CrsUtil.Z_DESC_ZB2A_N; 
								}
							}
							zItemEmptySet.add(chkItem+postfix_str);
						}
					}else if(Util.equals(CrsUtil.Y_項目附屬選項, c241m01c.getItemType())){
						if(Util.isEmpty(Util.trim(c241m01c.getChkResult()))){
							yItemEmptySet.add(c241m01c.getChkItem());
						}
					}else{
						if(Util.isEmpty(Util.trim(c241m01c.getChkResult()))){
							emptySet.add(c241m01c.getItemSeq());
						}	
					}				
				}
			}
			if(CollectionUtils.isNotEmpty(emptySet)){
				//ui_lms2411.msg02=覆審項目：{0}未輸入
				r.add( MessageFormat.format(prop_lms2411m01.getProperty("ui_lms2411.msg02"), StringUtils.join(emptySet, "、") ));
			}
			
			if(CollectionUtils.isNotEmpty(yItemEmptySet)){
				//ui_lms2411.msg02=覆審項目：{0}未輸入
				r.add( MessageFormat.format(prop_lms2411m01.getProperty("ui_lms2411.msg02"), StringUtils.join(yItemEmptySet, "、") ));
			}else{
				C241M01C c241m01c_Y_NY10 = map.get(CrsUtil.Y_NY10);
				if(c241m01c_Y_NY10!=null && Util.isNotEmpty(c241m01c_Y_NY10.getChkResult())){
					C241M01C c241m01c_Y_NY1A = map.get(CrsUtil.Y_NY1A);
					C241M01C c241m01c_Y_NY1B = map.get(CrsUtil.Y_NY1B);
					String err_Y_NY10 = _check_Y_NY10(c241m01c_Y_NY10, c241m01c_Y_NY1A, c241m01c_Y_NY1B);
					if(Util.isNotEmpty(err_Y_NY10)){
						r.add(err_Y_NY10);
					}
				}
			}			
		
			if(CollectionUtils.isNotEmpty(zItemEmptySet)){
				//ui_lms2411.msg13=電腦建檔資料：{0}未輸入
				r.add( MessageFormat.format(prop_lms2411m01.getProperty("ui_lms2411.msg13"), StringUtils.join(zItemEmptySet, "、") ));
			}else{
				C241M01C c241m01c_ZB1A = map.get(CrsUtil.ZB1A);
				if(c241m01c_ZB1A!=null && Util.isNotEmpty(c241m01c_ZB1A.getChkResult())){
					C241M01C c241m01c_ZB11 = map.get(CrsUtil.ZB11);
					C241M01C c241m01c_ZB12 = map.get(CrsUtil.ZB12);
					C241M01C c241m01c_ZB13 = map.get(CrsUtil.ZB13);
					String err_ZB1A = _check_ZB1A(c241m01c_ZB1A, c241m01c_ZB11, c241m01c_ZB12, c241m01c_ZB13);
					if(Util.isNotEmpty(err_ZB1A)){
						r.add(err_ZB1A);
					}
				}
				//=============
				C241M01C c241m01c_ZB2A = map.get(CrsUtil.ZB2A);
				if(c241m01c_ZB2A!=null && Util.isNotEmpty(c241m01c_ZB2A.getChkResult())){
					C241M01C c241m01c_ZB21 = map.get(CrsUtil.ZB21);
					String err_ZB2A = _check_ZB2A(c241m01c_ZB2A, c241m01c_ZB21);
					if(Util.isNotEmpty(err_ZB2A)){
						r.add(err_ZB2A);
					}
				}
			}
			
			if(true){
				C241M01C c241m01c = map.get(CrsUtil.N012);
				if(c241m01c!=null){
					if(CrsUtil.docKindN_since_R11(meta)){
						C241M01C c241m01c_Y_NY1A = map.get(CrsUtil.Y_NY1A);
						if(c241m01c_Y_NY1A!=null 
								&& (Util.equals("Y", c241m01c_Y_NY1A.getChkResult())||Util.equals("N", c241m01c_Y_NY1A.getChkResult()) )
								&&Util.isEmpty(Util.trim(c241m01c_Y_NY1A.getChkText()))){
							//（勾選是或否均應說明辦理情形）
							r.add(MessageFormat.format(prop_lms2411m01.getProperty("ui_lms2411.msg26")
									, "第"+Util.parseInt(c241m01c.getItemSeq())+"項."+c241m01c_Y_NY1A.getChkItem() ));
						}
					}else{
						if(Util.equals("Y", c241m01c.getChkResult())
								&& Util.isEmpty(Util.trim(c241m01c.getChkText()))){
							//ui_lms2411.msg25={0}若勾「是」，請填寫內容說明 
							r.add(MessageFormat.format(prop_lms2411m01.getProperty("ui_lms2411.msg25")
									, "第"+Util.parseInt(c241m01c.getItemSeq())+"項."
										+Util.trim(c241m01c.getChkItem()) ) );
						}		
					}
				}
			}
		}
		
		return r;
	}
	
	private List<String> _checkIncompleteMsg_05_c241m01g(Properties prop_lms2411m01, C241M01A meta){
		List<String> r = new ArrayList<String>();		
		if(Util.equals("Y", meta.getNeedPa())){	
			if(clsService.is_function_on_codetype("crs_chk_c241m01g")){
				Set<C241M01G> c241m01gs = meta.getC241m01gs();
				int c241m01gs_size = (c241m01gs != null?c241m01gs.size():0);
				
				if (c241m01gs_size > 0 ) {
					Set<String> notKeyin_itemName = new HashSet<String>();
					for (C241M01G c241m01g : c241m01gs) {
						String itemName = Util.trim(c241m01g.getItemName());
						String itemType = Util.trim(c241m01g.getItemType());
						if(Util.equals("YN", itemType)){
							if(Util.isEmpty(Util.trim(c241m01g.getItemYn()))){
								notKeyin_itemName.add(itemName);
							}else if(Util.equals("Y", c241m01g.getItemYn())){
								
							}
						}else if(Util.equals("CNT", itemType)){
							if(c241m01g.getItemCnt()==null || c241m01g.getItemCnt()==0){
								notKeyin_itemName.add(itemName);
							}else{
								
							}
						}else{
							
						}
					}
					if(c241m01gs_size>0 && c241m01gs_size==notKeyin_itemName.size()){
						r.add("考評項目尚未輸入任一項目");
					}
				}	
			}
		}
		
		return r;
	}

	private String _check_Y_NY10(C241M01C c241m01c_Y_NY10, C241M01C c241m01c_Y_NY1A, C241M01C c241m01c_Y_NY1B){
		if(Util.isNotEmpty(c241m01c_Y_NY10.getChkResult())){
			Properties prop_lms2411m01 = MessageBundleScriptCreator.getComponentResource(LMS2411M01Page.class);
			
			if(Util.equals("N", c241m01c_Y_NY10.getChkResult())){
				Map<String, List<String>> errMap = new HashMap<String, List<String>>();
				//當 無 事項, 底下的 item 應該都是 Y|N|K 中的K
				_check_NY1A_B(prop_lms2411m01, c241m01c_Y_NY1A, errMap);
				_check_NY1A_B(prop_lms2411m01, c241m01c_Y_NY1B, errMap);
				
				List<String> errMsg = new ArrayList<String>();
				for(String k:errMap.keySet()){
					errMsg.add(StringUtils.join(errMap.get(k), "、")+"不應為 "+k+" ");
				}
				
				if(errMsg.size()>0){
					return CrsUtil.get_chkItem_Y_NY10(c241m01c_Y_NY10, prop_lms2411m01, false)
						+":"+StringUtils.join(errMsg, "。");	
				}				
			}
		}		
		return "";
	}
	
	private void _check_NY1A_B(Properties prop_lms2411m01, C241M01C c241m01c, Map<String, List<String>> errMap){
		if(Util.equals("Y", c241m01c.getChkResult()) || Util.equals("N", c241m01c.getChkResult())){
			String key = CrsUtil.get_chkItem_valDesC(c241m01c, prop_lms2411m01);
			if(!errMap.containsKey(key)){
				errMap.put(key, new ArrayList<String>());
			}
			errMap.get(key).add(c241m01c.getChkItem());
		}
	}
	
	private String _check_ZB1A(C241M01C c241m01c_ZB1A, C241M01C c241m01c_ZB11, C241M01C c241m01c_ZB12, C241M01C c241m01c_ZB13){
		
		if(Util.isNotEmpty(c241m01c_ZB1A.getChkResult())){
			Properties prop_lms2411m01 = MessageBundleScriptCreator.getComponentResource(LMS2411M01Page.class);
			
			if(Util.equals("N", c241m01c_ZB1A.getChkResult())){
				Map<String, List<String>> errMap = new HashMap<String, List<String>>();
				_check_Z_detailItemYN_but_parentCondtionN(prop_lms2411m01, c241m01c_ZB11, errMap);
				_check_Z_detailItemYN_but_parentCondtionN(prop_lms2411m01, c241m01c_ZB12, errMap);
				_check_Z_detailItemYN_but_parentCondtionN(prop_lms2411m01, c241m01c_ZB13, errMap);
				
				List<String> errMsg = new ArrayList<String>();
				for(String k:errMap.keySet()){
					errMsg.add(StringUtils.join(errMap.get(k), "、")+"不應為 "+k+" ");
				}
				
				if(errMsg.size()>0){
					return CrsUtil.get_chkItem_ZB1A(c241m01c_ZB1A, prop_lms2411m01, false)+":"+StringUtils.join(errMsg, "。");	
				}				
			}
		}		
		return "";
	}

	private String _check_ZB2A(C241M01C c241m01c_ZB2A, C241M01C c241m01c_ZB21){
		
		if(Util.isNotEmpty(c241m01c_ZB2A.getChkResult())){
			Properties prop_lms2411m01 = MessageBundleScriptCreator.getComponentResource(LMS2411M01Page.class);
			
			if(Util.equals("N", c241m01c_ZB2A.getChkResult())){
				Map<String, List<String>> errMap = new HashMap<String, List<String>>();
				_check_Z_detailItemYN_but_parentCondtionN(prop_lms2411m01, c241m01c_ZB21, errMap);
				
				/*
				 (授信戶案下「無」額度之融資業務分類A-LOAN註記為「#」或海外AS-400註記為「A0#」):不動產暨72-2相關資訊註記欄位不應為 是  
				 */
				List<String> errMsg = new ArrayList<String>();
				for(String k:errMap.keySet()){
					errMsg.add(StringUtils.join(errMap.get(k), "、")+"不應為 "+k+" ");
				}
				
				if(errMsg.size()>0){
					return CrsUtil.get_chkItem_ZB2A(c241m01c_ZB2A, prop_lms2411m01, false)+":"+StringUtils.join(errMsg, "。");	
				}				
			}
		}		
		return "";
	}

	/** 當 parent 的項目選擇「無」 <br/>
	 * 下一層的各個子項，應該只能選「不適用」（不能夠去選「是」或「否」正確）
	 */
	private void _check_Z_detailItemYN_but_parentCondtionN(Properties prop_lms2411m01, C241M01C c241m01c, Map<String, List<String>> errMap){
		if(Util.equals("Y", c241m01c.getChkResult()) || Util.equals("N", c241m01c.getChkResult())){
			String key = CrsUtil.get_chkItem_valDesC(c241m01c, prop_lms2411m01);
			if(!errMap.containsKey(key)){
				errMap.put(key, new ArrayList<String>());
			}
			errMap.get(key).add(c241m01c.getChkItem());
		}
	}	
	
	private List<String> _checkIncompleteMsg_04(Properties prop_lms2411m01, C241M01A meta){
		List<String> r = new ArrayList<String>(); 
		if(Util.isEmpty(Util.trim(meta.getConFlag()))){
			//ui_lms2411.msg03=請輸入覆審結果為正常或異常
			r.add( prop_lms2411m01.getProperty("ui_lms2411.msg03") );
		}else{
			if(Util.equals("2", meta.getConFlag()) && Util.isEmpty(Util.trim(meta.getCondition()))){
				//ui_lms2411.msg04=覆審異常時，請輸入覆審意見
				r.add( prop_lms2411m01.getProperty("ui_lms2411.msg04") );
			}	
		}	
		
		if(CrsUtil.isCaseS(meta)){
			//ui_lms2411.msg09=請輸入
			if(Util.isEmpty(Util.trim(meta.getConFlag2A()))){
				r.add( prop_lms2411m01.getProperty("ui_lms2411.msg09")
					+ prop_lms2411m01.getProperty("label.conFlag2A.Y")
					+ prop_lms2411m01.getProperty("label.conFlag2A.N")
					+ prop_lms2411m01.getProperty("label.conFlag2A.desc"));	
			}
			if(Util.isEmpty(Util.trim(meta.getConFlag2B()))){
				r.add( prop_lms2411m01.getProperty("ui_lms2411.msg09")
						+ prop_lms2411m01.getProperty("label.conFlag2B.Y")
						+ prop_lms2411m01.getProperty("label.conFlag2B.N")
						+ prop_lms2411m01.getProperty("label.conFlag2B.desc2"));
			}
			if(Util.isEmpty(Util.trim(meta.getConFlag2C()))){
				r.add( prop_lms2411m01.getProperty("ui_lms2411.msg09")
						+ prop_lms2411m01.getProperty("label.conFlag2C.Y")
						+ prop_lms2411m01.getProperty("label.conFlag2C.N")
						+ prop_lms2411m01.getProperty("label.conFlag2C.desc1"));	
			}
			if(Util.equals("1", meta.getConFlag()) && Util.equals("Y", meta.getConFlag2A())){
				r.add( prop_lms2411m01.getProperty("label.conFlag2A.desc")+"="+prop_lms2411m01.getProperty("label.conFlag2A.Y")
						+"，"
						+prop_lms2411m01.getProperty("label.conFlag.1")+"="+prop_lms2411m01.getProperty("label.conFlag.Y"));
			}
			if(Util.equals("1", meta.getConFlag()) && Util.equals("Y", meta.getConFlag2B())){
				r.add( prop_lms2411m01.getProperty("label.conFlag2B.desc2")+"="+prop_lms2411m01.getProperty("label.conFlag2B.Y")
						+"，"
						+prop_lms2411m01.getProperty("label.conFlag.1")+"="+prop_lms2411m01.getProperty("label.conFlag.Y"));
			}
		}
		return r;
	}

	@Override
	public void batch_toEnd(String userId, String unitNo){
		String decisionExpr = "to_已覆核已核定";
		
		ISearch search = c241m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "typCd", TypCdEnum.DBU.getCode());
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", RetrialDocStatusEnum.已覆核未核定.getCode());
		//流程在從覆審組→受檢單位編製中, 會先上傳 ELF491
		//search.addSearchModeParameters(SearchMode.IS_NULL, "approveTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "conFlag", "1");//覆審正常
		search.setMaxResults(Integer.MAX_VALUE);
		
		for(C241M01A c241m01a : c241m01aDao.find(search)){
			if(CrsUtil.hasR99(c241m01a) && CrsUtil.hasR99_Cycle(c241m01a)==false){
				//若包含 99, 不適用整批上傳
				//需決定 specifyCycle
				continue;
			}
			
			try{
				flowSimplifyService.flowNext(c241m01a.getOid(), decisionExpr, userId, unitNo);			
				tempDataService.deleteByMainId(c241m01a.getMainId());	
			}catch(Exception e){
				logger.error("【"+c241m01a.getOwnBrId()+","+c241m01a.getCustId()+","+c241m01a.getCustName()+"】"+StrUtils.getStackTrace(e));
			}
			
		}
	}

	@Override
	public void update490_491abnormal(List<String> branchList, Date sysMonth_1st){
		for(String branch: branchList){
			if(retrialService.overSeaProgram(branch)){
				continue;
			}
			
			try{
				lms2401Service.update490(branch, sysMonth_1st);	
			}catch(Exception e){
				logger.error("【"+branch+"】"+StrUtils.getStackTrace(e) );
			}
		}
	
		//比照 notes 的程式 gfnDB2Abnormal
		lms2401Service.update491_abnormal();;
	}	

	@Override
	public boolean produce(String branch, String dateYM, String userId,
			String unitNo, String unitType, List<String> existBrSkipList, String latest__aprdcdate) throws CapException {
		List<String> instIdList = new ArrayList<String>();
				
		Date dataEndDate = CapDate.parseDate(CrsUtil.getDataEndDate(dateYM));
		if(isC240M01AInCompiling(unitNo, branch, dataEndDate)){
			//略過同一 endDate 且仍在 編製中 的分行
			existBrSkipList.add(branch);
			return true;
		}
		boolean r = lms2401Service.produce(instIdList, branch, dateYM, userId, unitNo, unitType, latest__aprdcdate);
		if(r){			
			for(String instId : instIdList){
				flowSimplifyService.flowStart("LMS2401Flow", instId , userId, unitNo);
			}	
		}
		
		return r;
	}

	@Override
	public boolean produce8_1(C240M01A c240m01a) throws CapException {
		return lms2401Service.produce8_1(c240m01a);
	}

	@Override
	public boolean produceNew(C240M01A c240m01a, String custId, String dupNo,
			String cName) throws CapException {
		return lms2401Service.produceNew(c240m01a, custId, dupNo, cName);
	}

	@Override
	public boolean produceNewGrpDetail(C240M01A c240m01a, C241M01A c241m01a_grpMain, String custId, String dupNo,
			String cName) throws CapException {
		return lms2401Service.produceNewGrpDetail(c240m01a, c241m01a_grpMain, custId, dupNo, cName);
	}
	
	@Override
	public boolean produce95_1(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException{
		return lms2401Service.produce95_1(c240m01a, custId, dupNo, cName);
	}
	
	@Override
	public boolean produce96(C240M01A c240m01a, String custId, String dupNo, String cName, String pa_ym, String pa_trg)throws CapException{
		return lms2401Service.produce96(c240m01a, custId, dupNo, cName, pa_ym, pa_trg);
	}
	
	@Override
	public boolean produce_R1R2S(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException{
		return lms2401Service.produce_R1R2S(c240m01a, custId, dupNo, cName);
	}
	
	@Override
	public boolean produce_R14(C240M01A c240m01a, String custId, String dupNo, String cName)throws CapException{
		return lms2401Service.produce_R14(c240m01a, custId, dupNo, cName);
	}
	
	@Override
	public boolean is_only_projectCreditLoan(String brNo, String custId, String dupNo){
		return lms2401Service.is_only_projectCreditLoan(brNo, custId, dupNo);	
	}
	
	@Override
	public boolean produce_projectCreditLoan(C240M01A c240m01a, String custId, String dupNo, String cName, boolean is_only_projectCreditLoan)throws CapException{
		return lms2401Service.produce_projectCreditLoan(c240m01a, custId, dupNo, cName, is_only_projectCreditLoan);
	}
	
	@Override
	public void deriveFrom99(C240M01A c240m01a, List<C241M01A> srclist,
			String userId) throws CapException {
		lms2401Service.deriveFrom99(c240m01a, srclist, userId);
		
	}
	
	@Override
	public void update_ptMgrId(C240M01A c240m01a, List<String> oid_list, String ptMgrId){
		String mgrId = "";
		if(true){
			IBranch branch = branchService.getBranch(c240m01a.getBranchId());
			mgrId = Util.trim(branch.getBrnMgr());	
		}	
		
		if(Util.isNotEmpty(ptMgrId)){
			mgrId = ptMgrId;
		}
		for (C241M01A c241m01a : retrialService.findC241M01A_oid(oid_list)) {
			String docStatus = Util.trim(c241m01a.getDocStatus());
			if( Util.isEmpty(docStatus) || 
					Util.equals(docStatus, RetrialDocStatusEnum.區中心_編製中.getCode())){
				//need update
				
				/*
				可能已上傳 mis 過
				之後, 又要依 分行|授管處意見 修改「應負責經理」
				*/
			}else{				
				continue;
			}
			List<C241M01C> existItemList = retrialService.findC241M01C_c241m01a(c241m01a);
			retrialService.gfnSetData_ptCrs(mgrId, existItemList);
			retrialService.saveC241M01C(existItemList);
		}
	}
	
	@Override
	public int update_docFmt(C240M01A c240m01a, List<String> oid_list){
		int r = 0;
		for (C241M01A c241m01a : retrialService.findC241M01A_oid(oid_list)) {
			
			if (CrsUtil.isCaseN(c241m01a) || CrsUtil.isCaseG___N_Detail(c241m01a) || CrsUtil.isCaseS(c241m01a)
					|| CrsUtil.isCaseH(c241m01a) || CrsUtil.isCaseG___H_Detail(c241m01a)) {
				if(_do_update_docFmt(c241m01a)){
					++r;
				}
			} else if (CrsUtil.isCaseG_Parent(c241m01a)) {
				//更新團貸母戶的版本
				if(_do_update_docFmt(c241m01a)){
					++r;
				}
				
				for (C241M01A grp_detail : retrialService.grid_C241M01A_byGrpCntrNo(
						c240m01a.getMainId(), c241m01a.getGrpCntrNo())) {
					// 團貸(子)
					if(_do_update_docFmt(grp_detail)){
						++r;
					}	
				}
			} else if (CrsUtil.isCaseP(c241m01a)) {
				continue;
			}
				
		}
		return r;
	}
	
	private boolean _do_update_docFmt(C241M01A c241m01a){
		String docStatus = Util.trim(c241m01a.getDocStatus());
		if( Util.isEmpty(docStatus) || 
				Util.equals(docStatus, RetrialDocStatusEnum.區中心_編製中.getCode())){
			String rptId = retrialService.getLatestRetrialItemVer(c241m01a);
			if(Util.equals(rptId, c241m01a.getRptId())){
				//version 相同, 是否要把文字要蓋過去?
				//目前先不用
				//2022-04-06 版本相同還是更新覆審項目
				upd_c241m01c_latestVer(c241m01a);
				return true;
			}else{
				upd_c241m01c_latestVer(c241m01a);
				return true;
			}	
//			return false;
		}else{				
			return false;
		}
	}
	
	@Override
	public boolean produce97(C240M01A meta, List<Map<String, Object>> list
			, Map<String, C241M01A> existMap
			, String lnf660_m_contract, String lnf660_loan_class
			, String comId, String comDupNo, String comName)throws CapException{
		return lms2401Service.produce97(meta, list, existMap
				, lnf660_m_contract, lnf660_loan_class, comId, comDupNo, comName);
				
	}
	
	@Override
	public List<Map<String, Object>> escrowList(String type, String lnf660_loan_class
			, String comId, String comName
			, String contract_M, String lnf034_CP_BANK_CD
			, String sDate){
		Date sysDate = CapDate.parseDate(CapDate.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
		Date _12m = CapDate.addMonth(sysDate, -12);
		Date _4m = CapDate.addMonth(sysDate, -4);	
		
		String gDate = null;
		if(Util.equals("1", type)){
			gDate = "9999-12-31";
		}else if(Util.equals("2", type)){
			sDate = "0001-01-01";
			//逾一年
			gDate = TWNDate.toAD(_12m);
		}else if(Util.equals("3", type)){
			sDate = "0001-01-01";
			//逾四月
			gDate = TWNDate.toAD(_4m);
		}
		
		
		List<Map<String, Object>> srcList = new ArrayList<Map<String, Object>>();
		if(lnf660_loan_class.startsWith("H")){
			srcList.addAll(misMISLN20Service.findLNF034_H(contract_M, lnf034_CP_BANK_CD, sDate, gDate));
		}else{
			srcList.addAll(misMISLN20Service.findLNF034_OTHER(contract_M, lnf034_CP_BANK_CD, sDate, gDate));
		}
		
		/*
		 ● B 抓 030, 034 會抓到一樣的結果--->B抓030
	     ● H 的 1個 030(NT:50), 底下有 2個 034(NT 10,40) ---->H抓034 
		 */
		boolean ln34 = true;
		/*
		1-價金履約保證覆審案件
		2-自開出價金履約保證書之日起，逾一年仍未結案案件
		3-自開出價金履約保證書之日起，逾四個月未結案之案件
		*/
		if(Util.equals("1", type)){
		}else{
			if(lnf660_loan_class.startsWith("H")){
			}else{
				ln34 = false;
			}
		}
		return _proc_escrowList(srcList, lnf660_loan_class
				,  comId,  comName,  contract_M,  ln34,  _12m ,  _4m );
	}
	
	@Override
	public List<Map<String, Object>> escrowList_lcNo(String lnf660_loan_class
			, String comId, String comName
			, String contract_M, String lnf034_CP_BANK_CD, String lcNo){
		
		Date sysDate = CapDate.parseDate(CapDate.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
		Date _12m = CapDate.addMonth(sysDate, -12);
		Date _4m = CapDate.addMonth(sysDate, -4);	
		
		List<Map<String, Object>> srcList = new ArrayList<Map<String, Object>>();
		if(lnf660_loan_class.startsWith("H")){
			srcList.addAll(misMISLN20Service.findLNF034_H_lcNo(contract_M, lnf034_CP_BANK_CD, lcNo));
		}else{
			srcList.addAll(misMISLN20Service.findLNF034_OTHER_lcNo(contract_M, lnf034_CP_BANK_CD, lcNo));
		}
		
		boolean ln34 = lnf660_loan_class.startsWith("H");
		
		return _proc_escrowList(srcList, lnf660_loan_class
				,  comId,  comName,  contract_M,  ln34,  _12m ,  _4m );
	}
	
	private List<Map<String, Object>> _proc_escrowList(List<Map<String, Object>> srcList, String lnf660_loan_class
			, String comId, String comName, String contract_M, boolean ln34, Date _12m , Date _4m ){		
		
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		
		for(Map<String, Object> o :srcList){
			Map<String, Object> row = new HashMap<String, Object>();
			
			row.put("LNF020_CUST_ID", Util.trim(o.get("LNF020_CUST_ID")));
			row.put("LNF020_FACT_AMT", CrsUtil.parseBigDecimal(o.get("LNF020_FACT_AMT")));
			
			//動用起迄日, 以 LNF034 為主
			//判斷 已逾期到日 [ ]無, [ ]1年, [ ]4月, 要用「開出保證書起日」判斷
			Date _lnf034_DATE = Util.parseDate(o.get("LNF034_BEG_DATE"));
			row.put("LNF020_BEG_DATE", _lnf034_DATE);
			row.put("LNF020_END_DATE", Util.parseDate(o.get("LNF034_DUE_DATE")));
			row.put("LNF020_CONTRACT", Util.trim(o.get("LNF020_CONTRACT")));
			row.put("LNF020_SWFT", Util.trim(o.get("LNF020_SWFT")));
			row.put("LNF020_CANCEL_DATE", Util.parseDate(o.get("LNF020_CANCEL_DATE")));
			row.put("S_CNAME", Util.trim(o.get("S_CNAME")));
			row.put("LNF660_M_CONTRACT", contract_M);
			row.put("LNF660_LOAN_CLASS", lnf660_loan_class);
			row.put("LNF030_LOAN_NO", Util.trim(o.get("LNF034_LOAN_NO")));
			row.put("LNF030_SWFT", Util.trim(o.get("LNF034_SWFT")));
			row.put("LNF030_LOAN_BAL", CrsUtil.parseBigDecimal(o.get("LNF034_LOAN_AMT")));
			row.put("LNF034_LC_NO", Util.trim(o.get("LNF034_LC_NO")));

			//LNF034_SHEET_NO 長度是10
			/*
			 * H:LNF020_CUST_ID 為仲介ID, LNF034_SHEET_NO=賣方Id
			 * B:LNF020_CUST_ID 為賣方ID, LNF034_SHEET_NO=''
			 */
			row.put("LNF034_SHEET_NO", Util.trim(o.get("LNF034_SHEET_NO")));
			row.put("LNF034_CP_SELF_AMT", CrsUtil.parseBigDecimal(o.get("LNF034_CP_SELF_AMT")));
			
			
			row.put("LNF030_MEMO", Util.trim(o.get(ln34?"LNF034_LC_NO":"LNF030_MEMO")));
			
			row.put("com_borrower", comName);
			row.put("Overdue", decide_overdueP(_lnf034_DATE, _12m, _4m));
			//---
			//UI欄位
			row.put("comId", Util.trim(Util.getLeftStr(comId, 10)));
			row.put("comName", comName);
			row.put("escrow_bal", row.get("LNF030_LOAN_BAL"));
			row.put("custId", Util.trim(o.get("S_CUSTID")));
			row.put("dupNo", Util.trim(o.get("S_DUPNO")));
			row.put("custName", Util.trim(row.get("S_CNAME")));
			if(Util.isEmpty(Util.trim(row.get("custId")))){
				if(lnf660_loan_class.startsWith("H")){
					//LNF034_SHEET_NO 只有10碼
					String str = Util.trim(o.get("LNF034_SHEET_NO"));
					String sellCustId = StringUtils.substring(str, 0, 10);
					String sellDup = Util.isNotEmpty(sellCustId)?"0":"";
					row.put("custId", sellCustId);
					row.put("dupNo", sellDup);
				}else{
					//LNF020_CUST_ID 有11碼
					String str = Util.trim(o.get("LNF020_CUST_ID"));
					String sellCustId = StringUtils.substring(str, 0, 10);
					String sellDup = StringUtils.substring(str, 10, 1);
					row.put("custId", sellCustId);
					row.put("dupNo", sellDup);
				}				
			}
			row.put("escrow_memo", row.get("LNF030_MEMO"));
			//---
			r.add(row);
		}
		
		return r;
	}
	
	@Override
	public void daoSave(C241M01A c241m01a){
		c241m01aDao.save(c241m01a);
	}
	
	private String decide_overdueP(Date d, Date _12m, Date _4m){
		String r = "0";
		if(CrsUtil.isNOT_null_and_NOTZeroDate(d)){
			if(LMSUtil.cmpDate(d, "<=", _12m)){
				return "12";
			}else if(LMSUtil.cmpDate(d, "<=", _4m)){
				return "4";
			}
		}
		return r;
	}

	@Override
	public void deleteC240M01ZByPk(Date d, String brNo){
		C240M01Z o = c240m01zDao.findByUniqueKey(d, brNo);
		if(o!=null){
			c240m01zDao.delete(o);
		}
	}
	
	@Override
	public boolean isC240M01AInCompiling(String ownBrId, String branchId, Date dataEndDate){	
		ISearch search = c240m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataEndDate", dataEndDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", RetrialDocStatusEnum.編製中.getCode());
		
		List<C240M01A> c240m01as = c240m01aDao.find(search);
		return CollectionUtils.isNotEmpty(c240m01as);		
	}
	
	@Override
	public void replaceWithBef(C241M01A meta, C241M01A befMeta){
		List<C241M01B> befB = retrialService.findC241M01B_c241m01a(befMeta);
		List<C241M01C> befC = retrialService.findC241M01C_c241m01a(befMeta);
		HashMap<String, String> map_loanNo_majorMemo = new HashMap<String, String>();
		HashMap<String, String> map_loanNo_guaranteeName = new HashMap<String, String>();
		for(C241M01B model : befB){
			map_loanNo_majorMemo.put(model.getLoanNo(), model.getMajorMemo());
			map_loanNo_guaranteeName.put(model.getLoanNo(), model.getGuaranteeName());
		}
		HashMap<String, String> map_itemNo_chkText = new HashMap<String, String>();
		for(C241M01C model : befC){
			map_itemNo_chkText.put(model.getItemNo(), model.getChkText());
		}
		
		List<C241M01B> currentB = retrialService.findC241M01B_c241m01a(meta);
		List<C241M01C> currentC = retrialService.findC241M01C_c241m01a(meta);
		//---
		for(C241M01B model: currentB){
			String k = model.getLoanNo();
			if(map_loanNo_majorMemo.containsKey(k)){
				model.setMajorMemo(map_loanNo_majorMemo.get(k));
				if(model.getGuaranteeKind().indexOf("權利質權")>=0 
						&& model.getGuaranteeName().indexOf("股票")>=0){
					//由系統組字，即包含 鑑價數量
					//另外，有的覆審人員會加 [A]YYY.MM.DD.收盤價@14.00 , [B]擔保維持率160%  
					//當[1]股數異動 [2]收盤價變動
				}else{
					model.setGuaranteeName(map_loanNo_guaranteeName.get(k));	
				}
			}
			
		}
		for(C241M01C model: currentC){
			String k = model.getItemNo();
			if(map_itemNo_chkText.containsKey(k)){
				model.setChkText(map_itemNo_chkText.get(k));
			}
		}
		//---
		c241m01bDao.save(currentB);	
		c241m01cDao.save(currentC);
		retrialService.save(meta);
	}
	
	@Override
	public boolean mail_processingOverDays(int overDays, boolean skipBrT1){
		Date sysDate = CapDate.parseDate(CapDate.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
		String d = Util.trim(TWNDate.toAD(CapDate.shiftDays(sysDate, overDays*-1)));
		
		Set<String> mail_br_list = _mailList("lms2401v01_mailSitesB");
		Set<String> mail_area_list = _mailList("lms2401v01_mailSitesA");
		
		Set<String> fnCheckArea = eloandbBASEService.findCrsMailBr(d, true);
		Set<String> fnCheckBranch = eloandbBASEService.findCrsMailBr(d, false);
		
		for(String brNo : fnCheckBranch){
			List<String> toAddr = new ArrayList<String>();
			if(skipBrT1==false){
				toAddr.add(brNo+"<EMAIL>");				
			}
			toAddr.addAll(mail_br_list);
			//---
			String subject = "【"+brNo +" " + branchService.getBranchName(brNo) + "】已逾二個星期尚未回覆" 
				+ "  " + d + "  " + "個金覆審報告表之通知!!";
			emailClient.send(toAddr.toArray(new String[toAddr.size()]), subject, "");			
		}		
		
		for(String brNo : fnCheckArea){
			List<String> toAddr = new ArrayList<String>();
			toAddr.addAll(mail_area_list);
			//---
			String subject = "【"+brNo +" " + branchService.getBranchName(brNo) + "】個金覆審：中心逾二個星期尚核定。";
			emailClient.send(toAddr.toArray(new String[toAddr.size()]), subject, "");			
		}	
		return true;
	}
	
	@Override
	public boolean mail_crs_notify(String version, String data_type, String data_ym){
		Set<String> toAddr = _mailList("crs_notify_IT");
		Map<String, List<Map<String, Object>>> data_map = new TreeMap<String, List<Map<String, Object>>>(); 
		if(Util.equals("1", data_type)){
			for(Map<String, Object> map : misdbBaseService.get_ELF491_notify_cntrNo_lack_loanNo(data_ym+"-01")){
				String LNF020_CONTRACT = Util.trim(MapUtils.getString(map, "LNF020_CONTRACT"));
				if(true){
					//第1步驟先抓出有銷戶的 loanNo
					//第2步驟去判斷, loanNo 所屬的 cntrNo 之下, 是否有未銷戶的 loanNo (可能, 同一額度底下, A帳號換B帳號)
					int cancel_cnt = 0;
					int active_cnt = 0;
					for(MISLN30 misLn30 : misLNF030Service.selByCntrNo(LNF020_CONTRACT)){
						if(misLn30.getLnf030_cancel_date()==null){
							active_cnt++;
						}else{
							cancel_cnt++;
						}
					}
					if(active_cnt>0){
						continue;
					}
				}
				String BRNGROUP = Util.trim(MapUtils.getString(map, "BRNGROUP"));
				if(!data_map.containsKey(BRNGROUP)){
					data_map.put(BRNGROUP, new ArrayList<Map<String, Object>>());
				}
				data_map.get(BRNGROUP).add(map);
			}
			
		}else if(Util.equals("2", data_type)){
			for(Map<String, Object> map : misdbBaseService.get_ELF491_notify_over_crDate(data_ym+"-01")){
				String BRNGROUP = Util.trim(MapUtils.getString(map, "BRNGROUP"));
				if(!data_map.containsKey(BRNGROUP)){
					data_map.put(BRNGROUP, new ArrayList<Map<String, Object>>());
				}
				data_map.get(BRNGROUP).add(map);
			}
			
		}else if(Util.equals("3", data_type)){
			String beg_date = data_ym+"-01";
			String end_date = TWNDate.toAD(CapDate.shiftDays(CapDate.addMonth(CapDate.parseDate(beg_date), 1), -1)); 
			for(Map<String, Object> map : misdbBaseService.get_ELF491_notify_lack_lnf09g(beg_date, end_date)){
				String BRNGROUP = Util.trim(MapUtils.getString(map, "BRNGROUP"));
				if(!data_map.containsKey(BRNGROUP)){
					data_map.put(BRNGROUP, new ArrayList<Map<String, Object>>());
				}
				data_map.get(BRNGROUP).add(map);
			}			
		}
		if(data_map.size()>0){
			String title = "個金覆審通知";
			if(Util.equals("1", data_type)){
				title += "【額度未銷戶，但帳號已銷戶】=> 請向分行確認, 是否需做後續處理, 以免下個月的應覆審日期被提前";
			}else if(Util.equals("2", data_type)){
				title += "【已逾覆審期限】";
			}else if(Util.equals("3", data_type)){
				title += "【應覆審，但尚未上傳LNF09G】";
			}
			
			String env = "";
			if(is_test(version)){
				env = "【測試】";
			}
			StringBuffer strBuf =  new StringBuffer();
			for(String keyBr : data_map.keySet()){
				List<Map<String, Object>> tr_list = data_map.get(keyBr);
				strBuf.append("<div style='margin-bottom:12px;'>");
				strBuf.append("【"+keyBr+"】");
				if(Util.equals("1", data_type)){
					strBuf.append("<table border='1'>");
					if(true){
						strBuf.append("<tr>");
						strBuf.append("<td>分行別</td>");
						strBuf.append("<td>統編</td>");
						strBuf.append("<td>額度序號</td>");
						strBuf.append("</tr>");
					}
					for(Map<String, Object> row: tr_list){
						strBuf.append("<tr>");
						strBuf.append("<td>").append(MapUtils.getString(row, "ELF339_BRNO")).append("&nbsp;</td>");
						strBuf.append("<td>").append(MapUtils.getString(row, "LNF020_CUST_ID")).append("&nbsp;</td>");
						strBuf.append("<td>").append(MapUtils.getString(row, "LNF020_CONTRACT")).append("&nbsp;</td>");
						strBuf.append("</tr>");
					}
					strBuf.append("</table>");
				}else if(Util.equals("2", data_type)){
					strBuf.append("<table border='1'>");
					if(true){
						strBuf.append("<tr>");
						strBuf.append("<td>分行別</td>");
						strBuf.append("<td>統編</td>");
						strBuf.append("<td>重複序號</td>");
						strBuf.append("<td>上次覆審日</td>");
						strBuf.append("<td>應覆審日</td>");
						strBuf.append("<td>覆審規則</td>");
						strBuf.append("</tr>");
					}
					for(Map<String, Object> row: tr_list){
						strBuf.append("<tr>");
						strBuf.append("<td>").append(MapUtils.getString(row, "ELF491_BRANCH")).append("&nbsp;</td>");
						strBuf.append("<td>").append(MapUtils.getString(row, "ELF491_CUSTID")).append("&nbsp;</td>");
						strBuf.append("<td>").append(MapUtils.getString(row, "ELF491_DUPNO")).append("&nbsp;</td>");
						strBuf.append("<td>").append(MapUtils.getString(row, "ELF491_LRDATE")).append("&nbsp;</td>");
						strBuf.append("<td>").append(MapUtils.getString(row, "ELF491_CRDATE")).append("&nbsp;</td>");
						strBuf.append("<td>").append(MapUtils.getString(row, "ELF491_REMOMO")).append("&nbsp;</td>");
						strBuf.append("</tr>");
					}
					strBuf.append("</table>");
					
				}else if(Util.equals("3", data_type)){
					strBuf.append("<table border='1'>");
					if(true){
						strBuf.append("<tr>");
						strBuf.append("<td>分行別</td>");
						strBuf.append("<td>筆數</td>");
						strBuf.append("</tr>");
					}
					for(Map<String, Object> row: tr_list){
						strBuf.append("<tr>");
						strBuf.append("<td>").append(MapUtils.getString(row, "ELF491_BRANCH")).append("&nbsp;</td>");
						strBuf.append("<td>").append(MapUtils.getString(row, "CNT")).append("&nbsp;</td>");
						strBuf.append("</tr>");
					}
					strBuf.append("</table>");
				}
				strBuf.append("</div>");
			}
			emailClient.send(false, null, toAddr.toArray(new String[toAddr.size()]), env+title, strBuf.toString());
		}
		return true;
	} 
	
	private boolean is_test(String version){
		return version.startsWith("@");
	}
	
	@Override
	public void deleteC241M01Z(C241M01Z o){
		c241m01zDao.delete(o);
	}
	
	@Override
	public List<C241M01Z> findUnProc(int size){
		return c241m01zDao.findProc("U", size);
	}
	
	@Override
	public List<C241M01Z> findProc(int size){
		return c241m01zDao.findProc("P", size);
	}
	
	
	
	@Override
	public void saveUnmatchR1R2R4(ELF491 elf491, C241M01Z c241m01z, CrsRuleVO crsRuleVO){
		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
		//---
		lms2401Service.chkR1R2R4_copy_to_c241m01z(elf491, c241m01z);
		retrialService.up491_at_unmatchR1R2R4(elf491_list, c241m01z_list, c241m01z, elf491, "nRView", crsRuleVO.getC241M01Z_reasonDesc());
		//---
		retrialService.upELF491_DelThenInsert(elf491_list);
		retrialService.saveC241M01Z(c241m01z_list);
	}

	@Override
	public void test_calc(String flag, CrsRuleVO crsRuleVO, String rule_no_new, String rule_no){
		lms2401Service.test_calc(flag, crsRuleVO, rule_no_new, rule_no);
	}
	
	private Set<String> _mailList(String codeType){
		Set<String> r = new HashSet<String>();
		for(CodeType obj : codeTypeService.findByCodeTypeList(codeType)){
			String target = Util.trim(obj.getCodeDesc());
			if(Util.isNotEmpty(target)){
				r.add(target);
			}
		}
		return r;
	}

	@Override
	public void upd_c241m01c_latestVer(C241M01A c241m01a){
		List<C241M01C> c241m01cs = new ArrayList<C241M01C>();
		List<C241M01C> delC241M01CList = new ArrayList<C241M01C>();
		retrialService.importRetrialItemToC241M01C(c241m01a, c241m01cs, delC241M01CList);
		
		for(C241M01C c241m01c : delC241M01CList){
			retrialService.del(c241m01c);
		}
		
		for(C241M01C c241m01c : c241m01cs){
			retrialService.save(c241m01c);
		}
		retrialService.save(c241m01a);
	}
	
	// J-108-0268 逾期情形
	@Override
	public boolean chkOverDue(C241M01A meta,List<C241M01C> c241m01c_list){
		boolean chk = false;
		if((CrsUtil.isCaseN(meta) || CrsUtil.isCaseG___N_Detail(meta))){
			for(C241M01C c241m01c : c241m01c_list){
				if(Util.equals(CrsUtil.N009, c241m01c.getItemNo())){
					if(meta.getOvQryDt() == null){
						chk = true;
						break;
					}
				}
			}
		}
		return chk;
	}
	
	@Override
	public void deleteC241M01GSByMeta(C241M01A c241m01a) {
		c241m01gDao.delete(new ArrayList<C241M01G>(c241m01a.getC241m01gs()));
	}
}
