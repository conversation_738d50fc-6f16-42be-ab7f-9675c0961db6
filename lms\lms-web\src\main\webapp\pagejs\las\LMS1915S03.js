initDfd.done(function(json){
	
	var gist = $("#gist")[0];
	var processComm = $("#processComm")[0];
	
	
	var gistValue = $("#gist").val();
	//alert($("#gist").attr("maxlength"));
	var max = parseInt($("#gist").attr("maxlength"),10);
	max = 150;
	var finalvalue = 0;
	
	var result = 0;
	for(var i = 0; i< gistValue.length;i++){
		var tmpvalue = gistValue.charCodeAt(i);
		if( tmpvalue >=32 && tmpvalue <=127){
			
		}else{
			result = result + 2;
		}
		
		result = result + 1;
		
		//alert("result:" + result);
		if(result>max){
			finalvalue = i;
			//alert("finalValue : " + finalvalue );
			break;
		}
		if(i==gistValue.length-1){
			finalvalue = i+1;
		}
	}
	
	//alert(finalvalue);
	createSelection(processComm, 2, 5);
	createSelection(gist, 0, finalvalue);
	
	
	//alert(processComm);
	//createSelection(processComm, 2, 5);


    $("<option></option>").text(i18n.lms1915m01['lms1915s06.009']).prependTo("#selectnames");
    $('#selectnames').find("option:eq(0)").attr("selected", true);
    
    $('#phrase').click(function(){
        $('#select1').show();
    });
    
    $('#selectnames').change(function(){
        if ($(this).find("option:eq(0)").attr("selected")) {
            return false;
        }
        var txt = $(this).find(":selected").text();
        $('#processComm').val(txt);
        
        //IE 會再觸發一次onChange事件，所以在第一行做特別處理。FireFox則無此問題
        $(this).find("option:eq(0)").attr("selected", true);
        $('#select1').hide();
    });
	
	function createSelection(field, start, end) {
		
        if( field.createTextRange ) {
            var selRange = field.createTextRange();
            selRange.collapse(true);
            selRange.moveStart('character', start);
            selRange.moveEnd('character', end);
            selRange.select();
        } else if( field.setSelectionRange ) {
            field.setSelectionRange(start, end);
        } else if( field.selectionStart ) {
            field.selectionStart = start;
            field.selectionEnd = end;
        }
        field.focus();
    }   
	
	
});
