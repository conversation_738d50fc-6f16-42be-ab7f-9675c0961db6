/* 
 * C004M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C004M01A;

/** 政策性留學生貸款報送資料 **/
public interface C004M01ADao extends IGenericDao<C004M01A> {

	C004M01A findByOid(String oid);
	
	List<C004M01A> findByMainId(String mainId);
	
	C004M01A findByUniqueKey(String mainId);

	List<C004M01A> findByIndex01(String mainId);

	List<C004M01A> findByIndex02(String rptType, Date bgnDate);
}