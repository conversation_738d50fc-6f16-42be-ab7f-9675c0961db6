/* 
 * C241A01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C241A01A;

/** 覆審報告表授權檔 **/
public interface C241A01ADao extends IGenericDao<C241A01A> {

	C241A01A findByOid(String oid);
	
	List<C241A01A> findByMainId(String mainId);
	
	C241A01A findByUniqueKey(String mainId, String ownUnit, String authType, String authUnit);

	List<C241A01A> findByIndex01(String mainId, String ownUnit, String authType, String authUnit);

	/**
	 * 利用C240M01A mainId 刪除
	 * @param mainId
	 */
	void deleteByC240M01AMainid(String mainId);
}