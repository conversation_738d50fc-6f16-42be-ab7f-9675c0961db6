/* 
 * C122M01GDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.util.CapDate;

import com.mega.eloan.lms.model.C122M01G;
import com.mega.eloan.lms.dao.C122M01GDao;
import com.mega.eloan.lms.dao.LMSJpaDao;

/** 進件管理資料檔 **/
@Repository
public class C122M01GDaoImpl extends LMSJpaDao<C122M01G, String>
	implements C122M01GDao {

	@Override
	public C122M01G findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C122M01G> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C122M01G> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C122M01G findByUniqueKey(String mainId){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C122M01G> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<C122M01G> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C122M01G> findOnlineCasesUnsentToJCICForIxmlCertifcate() {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IS_NULL, "sendFTPTime", "");// 尚未被送至FTP Server
		search.addSearchModeParameters(SearchMode.IS_NULL, "sendFTPStatus", "");// 尚未被押進件聯徵後狀態
		return createQuery(C122M01G.class, search).getResultList();
	}

	@Override
	public List<C122M01G> findOnlineCasesToRetryIxmlCertifcate(
			String startTime, String endTime, String jcicStatusCode,
			Boolean onlyUnsent) {
		ISearch search = createSearchTemplete();
		String[] timeInterval = { startTime, endTime };
		search.addSearchModeParameters(SearchMode.BETWEEN, "createTime", timeInterval);
		search.addSearchModeParameters(SearchMode.EQUALS, "sendFTPStatus", jcicStatusCode);
		if (onlyUnsent) {
			search.addSearchModeParameters(SearchMode.IS_NULL, "sendFTPTime", "");
			search.addSearchModeParameters(SearchMode.IS_NULL, "sendFTPStatus", "");
		}
		return createQuery(C122M01G.class, search).getResultList();
	}

	@Override
	public List<C122M01G> findUndoneQueryJcic(String startDate, String endDate) {
		ISearch search = createSearchTemplete();
		// 1.沒有發查過
		// 2.沒有資料傳回的
		search.addSearchModeParameters(SearchMode.IS_NULL, "queryJCICTime",
				null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "receivedJCICTime",
				null);
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS,
				"receivedFTPTime", startDate);
		search.addSearchModeParameters(SearchMode.LESS_EQUALS,
				"receivedFTPTime", endDate);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C122M01G> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C122M01G> findDoneQueryJcic(String startDate, String endDate) {
        ISearch search = createSearchTemplete();
        search.addSearchModeParameters(SearchMode.GREATER_EQUALS,
                "queryJCICTime", startDate);
        search.addSearchModeParameters(SearchMode.LESS_EQUALS,
                "queryJCICTime", endDate);
        search.addSearchModeParameters(SearchMode.IS_NULL, "receivedJCICTime",
                null);
        search.setMaxResults(Integer.MAX_VALUE);
        List<C122M01G> list = createQuery(search).getResultList();
        return list;
	}
}