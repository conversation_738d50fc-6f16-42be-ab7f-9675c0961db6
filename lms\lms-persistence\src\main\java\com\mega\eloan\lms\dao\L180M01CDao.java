/* 
 * L180M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L180M01C;

/** 覆審名單明細額度序號檔 **/
public interface L180M01CDao extends IGenericDao<L180M01C> {

	L180M01C findByOid(String oid);

	List<L180M01C> findByMainId(String mainId, String ctlType);

	L180M01C findByUniqueKey(String mainId, String custId, String dupNo,
			String elfCntrType, String elfCustCoId, String elfCntrNo,
			String ctlType);

	List<L180M01C> findByIndex01(String mainId, String custId, String dupNo,
			String elfCntrType, String elfCustCoId, String elfCntrNo,
			String ctlType);

	void deleteL180M01CList(String mainId, String custId, String dupNo,
			String ctlType);

	List<L180M01C> findByUniqueKey2(String mainId, String custId, String dupNo,
			String ctlType);

	List<L180M01C> findByCntrNo(String CntrNo, String ctlType);

	List<L180M01C> findByCustIdDupId(String custId, String DupNo, String ctlType);
}