/* 
 * L260M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import org.apache.wicket.markup.html.form.Check;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 貸後管理控制檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L260M01C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L260M01C extends GenericBean implements IDataObject, IDocObject {

//
	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 新產生時：getUUID()
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 序號(系統UNID) **/
	@Size(max=60)
	@Column(name="UNID", length=60, columnDefinition="CHAR(60)")
	private String unid;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 放款帳號 **/
	@Size(max=20)
	@Column(name="LOANNO", length=20, columnDefinition="CHAR(20)")
	private String loanNo;

	/** 業務別 **/
	@Size(max=2)
	@Column(name="LOANKIND", length=2, columnDefinition="CHAR(2)")
	private String loanKind;

	/** 
	 * 類別<p/>
	 * 複選 以｜分隔
	 */
	@Size(max=20)
	@Column(name="FOLLOWKIND", length=20, columnDefinition="VARCHAR(20)")
	private String followKind;

	/** 追蹤事項通知內容 **/
	@Size(max=800)
	@Column(name = "FOLLOWCONTENT", length = 800, columnDefinition = "VARCHAR(800)")
	private String followContent;

	/** 
	 * 追蹤方式<p/>
	 * 1-特定日期 / 2-循環週期
	 */
	@Size(max=1)
	@Column(name="FOLLOWWAY", length=1, columnDefinition="CHAR(1)")
	private String followWay;

	/** 循環追蹤週期BY月 **/
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "FOLLOWCYCLE", columnDefinition = "DECIMAL(2,0)")
	private BigDecimal followCycle;

	/** 循環追蹤起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="FOLLOWBGNDATE", columnDefinition="DATE")
	private Date followBgnDate;

	/** 循環追蹤止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="FOLLOWENDDATE", columnDefinition="DATE")
	private Date followEndDate;

	/**
	 * 下次追蹤日<p/>
	 * 限未來日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="NEXTFOLLOWDATE", columnDefinition="DATE")
	private Date nextFollowDate;

	/** 
	 * 應辦理追蹤對象<p/>
	 * 01-帳務人員,02-AO人員
	 */
	@Size(max=2)
	@Column(name="STAFF", length=2, columnDefinition="CHAR(2)")
	private String staff;

	/** 帳務行員代號 **/
	@Size(max=6)
	@Column(name="FO_STAFFNO", length=6, columnDefinition="CHAR(6)")
	private String fo_staffNo;

	/** AO行員代號 **/
	@Size(max=6)
	@Column(name="AO_STAFFNO", length=6, columnDefinition="CHAR(6)")
	private String ao_staffNo;

	/** 
	 * 狀態<p/>
	 * N-新增、U-修改、C-解除、
	 * P-新增(用來判斷為user新增，上傳中心時轉為N)
	 */
	@Size(max=1)
	@Column(name="STATUS", length=1, columnDefinition="CHAR(1)")
	private String status;

	/**
	 * 顯示狀態<p/>
	 * 顯示、列印、排序 用
	 * N.U -> 未來日 => 1-待追蹤
	 *     -> 過去日 => 2-已追蹤
	 *  C  ==========> 3-已解除 (下次追蹤日顯示 N.A.)
	 */
	@Size(max=1)
	@Column(name="STATUSFORSHOW", length=1, columnDefinition="CHAR(1)")
	private String statusForShow;

	/**
	 * 是否通過檢核<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="CHECKYN", length=1, columnDefinition="CHAR(1)")
	private String checkYN;

    /**
     * 案件註記<p/>
     * 01:謄本
     * 02:實價登錄
     */
    @Size(max=2)
    @Column(name="CASEMARK", length=2, columnDefinition="VARCHAR(2)")
    private String caseMark;
    
    /** 分項UID **/
    @Size(max = 32)
    @Column(name="FROM601SUID", length=32, columnDefinition="CHAR(32)")
    private String from601SUid;
    
    /** 分項核准時間 **/
    @Column(name="FROM601SAPPTIME", columnDefinition="TIMESTAMP")
    private Date from601SApptime;
      
    /** 分項序號 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
    @Column(name="FROM601SSEQNO", columnDefinition="DECIMAL(3,0)")
    private BigDecimal from601SSeqno;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 新產生時：getUUID()
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  新產生時：getUUID()
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  每次儲存：getRandomCode()
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得序號(系統UNID) **/
	public String getUnid() {
		return this.unid;
	}
	/** 設定序號(系統UNID) **/
	public void setUnid(String value) {
		this.unid = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得放款帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}
	/** 設定放款帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/** 取得業務別 **/
	public String getLoanKind() {
		return this.loanKind;
	}
	/** 設定業務別 **/
	public void setLoanKind(String value) {
		this.loanKind = value;
	}

	/** 
	 * 取得類別<p/>
	 * 複選 以｜分隔
	 */
	public String getFollowKind() {
		return this.followKind;
	}
	/**
	 *  設定類別<p/>
	 *  複選 以｜分隔
	 **/
	public void setFollowKind(String value) {
		this.followKind = value;
	}

	/** 取得追蹤事項通知內容 **/
	public String getFollowContent() {
		return this.followContent;
	}
	/** 設定追蹤事項通知內容 **/
	public void setFollowContent(String value) {
		this.followContent = value;
	}

	/** 
	 * 取得追蹤方式<p/>
	 * 1-特定日期 / 2-循環週期
	 */
	public String getFollowWay() {
		return this.followWay;
	}
	/**
	 *  設定追蹤方式<p/>
	 *  1-特定日期 / 2-循環週期
	 **/
	public void setFollowWay(String value) {
		this.followWay = value;
	}

	/** 取得循環追蹤週期BY月 **/
	public BigDecimal getFollowCycle() {
		return this.followCycle;
	}
	/** 設定循環追蹤週期BY月 **/
	public void setFollowCycle(BigDecimal value) {
		this.followCycle = value;
	}

	/** 取得循環追蹤起日 **/
	public Date getFollowBgnDate() {
		return this.followBgnDate;
	}
	/** 設定循環追蹤起日 **/
	public void setFollowBgnDate(Date value) {
		this.followBgnDate = value;
	}

	/** 取得循環追蹤止日 **/
	public Date getFollowEndDate() {
		return this.followEndDate;
	}
	/** 設定循環追蹤止日 **/
	public void setFollowEndDate(Date value) {
		this.followEndDate = value;
	}

	/**
	 * 取得下次追蹤日<p/>
	 * 限未來日
	 */
	public Date getNextFollowDate() {
		return this.nextFollowDate;
	}
	/**
	 *  設定下次追蹤日<p/>
	 *  限未來日
	 **/
	public void setNextFollowDate(Date value) {
		this.nextFollowDate = value;
	}

	/** 
	 * 取得應辦理追蹤對象<p/>
	 * 01-帳務人員,02-AO人員
	 */
	public String getStaff() {
		return this.staff;
	}
	/**
	 *  設定應辦理追蹤對象<p/>
	 *  01-帳務人員,02-AO人員
	 **/
	public void setStaff(String value) {
		this.staff = value;
	}

	/** 取得帳務行員代號 **/
	public String getFo_staffNo() {
		return this.fo_staffNo;
	}
	/** 設定帳務行員代號 **/
	public void setFo_staffNo(String value) {
		this.fo_staffNo = value;
	}

	/** 取得AO行員代號 **/
	public String getAo_staffNo() {
		return this.ao_staffNo;
	}
	/** 設定AO行員代號 **/
	public void setAo_staffNo(String value) {
		this.ao_staffNo = value;
	}

	/** 
	 * 取得狀態<p/>
	 * N-新增、U-修改、C-解除、P-新增(用來判斷為user新增，上傳中心時轉為N)
	 */
	public String getStatus() {
		return this.status;
	}
	/**
	 *  設定狀態<p/>
	 *  N-新增、U-修改、C-解除、P-新增(用來判斷為user新增，上傳中心時轉為N)
	 **/
	public void setStatus(String value) {
		this.status = value;
	}

	/**
	 *  設定顯示狀態<p/>
	 * 	顯示、列印、排序 用
	 * 	N.U -> 未來日 => 1-待追蹤
	 *      -> 過去日 => 2-已追蹤
	 *   C  ==========> 3-已解除 (下次追蹤日顯示 N.A.)
	 */
	public String getStatusForShow() {
		return this.statusForShow;
	}
	/**
	 *  設定顯示狀態<p/>
	 * 	顯示、列印、排序 用
	 * 	N.U -> 未來日 => 1-待追蹤
	 *      -> 過去日 => 2-已追蹤
	 *   C  ==========> 3-已解除 (下次追蹤日顯示 N.A.)
	 **/
	public void setStatusForShow(String value) {
		this.statusForShow = value;
	}

	/**
	 * 取得是否通過檢核<p/>
	 * Y/N
	 */
	public String getCheckYN() {
		return this.checkYN;
	}
	/**
	 *  設定是否通過檢核<p/>
	 *  Y/N
	 **/
	public void setCheckYN(String value) {
		this.checkYN = value;
	}

    /**
     * 取得案件註記<p/>
     * 01:謄本
     * 02:實價登錄
     */
    public String getCaseMark() {
        return this.caseMark;
    }
    /**
     *  設定案件註記<p/>
     * 01:謄本
     * 02:實價登錄
     **/
    public void setCaseMark(String value) {
        this.caseMark = value;
    }
    
    /**
     * 取得分項UID 
     * @return
     */
	public String getFrom601SUid() {
		return from601SUid;
	}
	/**
	 * 設定分項UID
	 * @param from601sUid
	 */
	public void setFrom601SUid(String from601sUid) {
		from601SUid = from601sUid;
	}
	
	/**
	 * 取得分項核准時間
	 * @return
	 */
	public Date getFrom601SApptime() {
		return from601SApptime;
	}
	/**
	 * 設定分項核准時間
	 * @param from601sApptime
	 */
	public void setFrom601SApptime(Date from601sApptime) {
		from601SApptime = from601sApptime;
	}
	
	/**
	 * 取得分項序號
	 * @return
	 */
	public BigDecimal getFrom601SSeqno() {
		return from601SSeqno;
	}
	/**
	 * 設定分項序號
	 * @param from601sSeqno
	 */
	public void setFrom601SSeqno(BigDecimal from601sSeqno) {
		from601SSeqno = from601sSeqno;
	}
       
}
