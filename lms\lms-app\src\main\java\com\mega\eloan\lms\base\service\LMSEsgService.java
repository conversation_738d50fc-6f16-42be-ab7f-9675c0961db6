/* 
 * MicroEntService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2024/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2024/02,009301,new
 *          </ul>
 */
public interface LMSEsgService extends ICapService {

	public CapAjaxFormResult applyEsgFa(String mainId, String custId, String dupNo);

	public CapAjaxFormResult applyCesEsg(String mainId, String custId, String dupNo);

	public CapAjaxFormResult applyEsgSbti(String mainId, String custId, String dupNo);

	public CapAjaxFormResult applySustainEval(String mainId, String custId, String dupNo);
}