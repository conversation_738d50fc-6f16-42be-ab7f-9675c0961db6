/* 
 * L140S05A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 條件變更資訊 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S05A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class L140S05A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 利率(本位幣)_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_INTRATEL", length=1, columnDefinition="CHAR(1)")
	private String quant_intRateL;

	/** 利率(本位幣)_質化 **/
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="INTRATEL_R", columnDefinition="DECIMAL(6,4)")
	private BigDecimal intRateL_R;

	/** 利率(本位幣)_幣別 **/
	@Size(max=3)
	@Column(name="INTRATEL_C", length=3, columnDefinition="CHAR(3)")
	private String intRateL_C;

	/** 
	 * 利率(外幣)_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_INTRATEF", length=1, columnDefinition="CHAR(1)")
	private String quant_intRateF;

	/** 利率(外幣)_質化 **/
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="INTRATEF_R", columnDefinition="DECIMAL(6,4)")
	private BigDecimal intRateF_R;

	/** 利率(外幣)_幣別 **/
	@Size(max=3)
	@Column(name="INTRATEF_C", length=3, columnDefinition="CHAR(3)")
	private String intRateF_C;

	/** 
	 * 費率(本位幣)_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_RATEL", length=1, columnDefinition="CHAR(1)")
	private String quant_rateL;

	/** 費率(本位幣)_質化 **/
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="RATEL_R", columnDefinition="DECIMAL(6,4)")
	private BigDecimal rateL_R;

	/** 費率(本位幣)_幣別 **/
	@Size(max=3)
	@Column(name="RATEL_C", length=3, columnDefinition="CHAR(3)")
	private String rateL_C;

	/** 
	 * 費率(外幣)_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_RATEF", length=1, columnDefinition="CHAR(1)")
	private String quant_rateF;

	/** 費率(外幣)_質化 **/
	@Digits(integer=6, fraction=4, groups = Check.class)
	@Column(name="RATEF_R", columnDefinition="DECIMAL(6,4)")
	private BigDecimal rateF_R;

	/** 費率(外幣)_幣別 **/
	@Size(max=3)
	@Column(name="RATEF_C", length=3, columnDefinition="CHAR(3)")
	private String rateF_C;

	/** 
	 * 寬限期_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_GRACE", length=1, columnDefinition="CHAR(1)")
	private String quant_grace;

	/** 寬限期_質化 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="GRACE", columnDefinition="DECIMAL(5,0)")
	private Integer grace;

	/** 
	 * 動用期間_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_DRAWDOWN", length=1, columnDefinition="CHAR(1)")
	private String quant_drawdown;

	/** 動用期間_質化 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="DRAWDOWN", columnDefinition="DECIMAL(5,0)")
	private Integer drawdown;

	/** 
	 * 授信期間_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_LOAN", length=1, columnDefinition="CHAR(1)")
	private String quant_loan;

	/** 授信期間_質化 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="LOAN", columnDefinition="DECIMAL(5,0)")
	private Integer loan;

	/** 
	 * 擔保品_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_COLL", length=1, columnDefinition="CHAR(1)")
	private String quant_coll;

	/** 
	 * 保證人_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_GUARANTOR", length=1, columnDefinition="CHAR(1)")
	private String quant_guarantor;

	/** 
	 * 承諾事項_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_COMMITMENTS", length=1, columnDefinition="CHAR(1)")
	private String quant_commitments;

	/** 
	 * 預計動工日_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_ESTCOMDATE", length=1, columnDefinition="CHAR(1)")
	private String quant_estComDate;

	/** 預計動工日_質化 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="ESTCOMDATE", columnDefinition="DECIMAL(5,0)")
	private Integer estComDate;

	/** 
	 * 其他_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	@Size(max=1)
	@Column(name="QUANT_OTHERS", length=1, columnDefinition="CHAR(1)")
	private String quant_others;

	/** 其他_質化 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="OTHERS", columnDefinition="CLOB")
	private String others;
	
	/** 上傳用文字 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="COND_CHG", columnDefinition="CLOB")
	private String cond_chg;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得利率(本位幣)_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_intRateL() {
		return this.quant_intRateL;
	}
	/**
	 *  設定利率(本位幣)_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_intRateL(String value) {
		this.quant_intRateL = value;
	}

	/** 取得利率(本位幣)_質化 **/
	public BigDecimal getIntRateL_R() {
		return this.intRateL_R;
	}
	/** 設定利率(本位幣)_質化 **/
	public void setIntRateL_R(BigDecimal value) {
		this.intRateL_R = value;
	}

	/** 取得利率(本位幣)_幣別 **/
	public String getIntRateL_C() {
		return this.intRateL_C;
	}
	/** 設定利率(本位幣)_幣別 **/
	public void setIntRateL_C(String value) {
		this.intRateL_C = value;
	}

	/** 
	 * 取得利率(外幣)_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_intRateF() {
		return this.quant_intRateF;
	}
	/**
	 *  設定利率(外幣)_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_intRateF(String value) {
		this.quant_intRateF = value;
	}

	/** 取得利率(外幣)_質化 **/
	public BigDecimal getIntRateF_R() {
		return this.intRateF_R;
	}
	/** 設定利率(外幣)_質化 **/
	public void setIntRateF_R(BigDecimal value) {
		this.intRateF_R = value;
	}

	/** 取得利率(外幣)_幣別 **/
	public String getIntRateF_C() {
		return this.intRateF_C;
	}
	/** 設定利率(外幣)_幣別 **/
	public void setIntRateF_C(String value) {
		this.intRateF_C = value;
	}

	/** 
	 * 取得費率(本位幣)_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_rateL() {
		return this.quant_rateL;
	}
	/**
	 *  設定費率(本位幣)_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_rateL(String value) {
		this.quant_rateL = value;
	}

	/** 取得費率(本位幣)_質化 **/
	public BigDecimal getRateL_R() {
		return this.rateL_R;
	}
	/** 設定費率(本位幣)_質化 **/
	public void setRateL_R(BigDecimal value) {
		this.rateL_R = value;
	}

	/** 取得費率(本位幣)_幣別 **/
	public String getRateL_C() {
		return this.rateL_C;
	}
	/** 設定費率(本位幣)_幣別 **/
	public void setRateL_C(String value) {
		this.rateL_C = value;
	}

	/** 
	 * 取得費率(外幣)_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_rateF() {
		return this.quant_rateF;
	}
	/**
	 *  設定費率(外幣)_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_rateF(String value) {
		this.quant_rateF = value;
	}

	/** 取得費率(外幣)_質化 **/
	public BigDecimal getRateF_R() {
		return this.rateF_R;
	}
	/** 設定費率(外幣)_質化 **/
	public void setRateF_R(BigDecimal value) {
		this.rateF_R = value;
	}

	/** 取得費率(外幣)_幣別 **/
	public String getRateF_C() {
		return this.rateF_C;
	}
	/** 設定費率(外幣)_幣別 **/
	public void setRateF_C(String value) {
		this.rateF_C = value;
	}

	/** 
	 * 取得寬限期_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_grace() {
		return this.quant_grace;
	}
	/**
	 *  設定寬限期_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_grace(String value) {
		this.quant_grace = value;
	}

	/** 取得寬限期_質化 **/
	public Integer getGrace() {
		return this.grace;
	}
	/** 設定寬限期_質化 **/
	public void setGrace(Integer value) {
		this.grace = value;
	}

	/** 
	 * 取得動用期間_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_drawdown() {
		return this.quant_drawdown;
	}
	/**
	 *  設定動用期間_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_drawdown(String value) {
		this.quant_drawdown = value;
	}

	/** 取得動用期間_質化 **/
	public Integer getDrawdown() {
		return this.drawdown;
	}
	/** 設定動用期間_質化 **/
	public void setDrawdown(Integer value) {
		this.drawdown = value;
	}

	/** 
	 * 取得授信期間_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_loan() {
		return this.quant_loan;
	}
	/**
	 *  設定授信期間_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_loan(String value) {
		this.quant_loan = value;
	}

	/** 取得授信期間_質化 **/
	public Integer getLoan() {
		return this.loan;
	}
	/** 設定授信期間_質化 **/
	public void setLoan(Integer value) {
		this.loan = value;
	}

	/** 
	 * 取得擔保品_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_coll() {
		return this.quant_coll;
	}
	/**
	 *  設定擔保品_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_coll(String value) {
		this.quant_coll = value;
	}

	/** 
	 * 取得保證人_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_guarantor() {
		return this.quant_guarantor;
	}
	/**
	 *  設定保證人_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_guarantor(String value) {
		this.quant_guarantor = value;
	}

	/** 
	 * 取得承諾事項_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_commitments() {
		return this.quant_commitments;
	}
	/**
	 *  設定承諾事項_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_commitments(String value) {
		this.quant_commitments = value;
	}

	/** 
	 * 取得預計動工日_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_estComDate() {
		return this.quant_estComDate;
	}
	/**
	 *  設定預計動工日_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_estComDate(String value) {
		this.quant_estComDate = value;
	}

	/** 取得預計動工日_質化 **/
	public Integer getEstComDate() {
		return this.estComDate;
	}
	/** 設定預計動工日_質化 **/
	public void setEstComDate(Integer value) {
		this.estComDate = value;
	}

	/** 
	 * 取得其他_量化<p/>
	 * A.增加、M.減少、C.更換、X.N/A
	 */
	public String getQuant_others() {
		return this.quant_others;
	}
	/**
	 *  設定其他_量化<p/>
	 *  A.增加、M.減少、C.更換、X.N/A
	 **/
	public void setQuant_others(String value) {
		this.quant_others = value;
	}

	/** 取得其他_質化 **/
	public String getOthers() {
		return this.others;
	}
	/** 設定其他_質化 **/
	public void setOthers(String value) {
		this.others = value;
	}
	
	/** 取得上傳用文字 **/
	public String getCond_chg() {
		return this.cond_chg;
	}
	/** 設定上傳用文字 **/
	public void setCond_chg(String value) {
		this.cond_chg = value;
	}
}
