package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

import com.mega.eloan.lms.mfaloan.bean.ELF491B;

/**
 * <pre>
 * 防杜代辦覆審控制檔
 * </pre>
 * 
 * @since 2019/4/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/4/17,EL08034,new
 *          </ul>
 */
public interface MisELF491BService {
	public List<ELF491B> findByBrNoIdDup(String brNo, String custId, String dupNo);
	public List<ELF491B> findByArea_PA_YM(String brno_area, String pa_ym);
}
