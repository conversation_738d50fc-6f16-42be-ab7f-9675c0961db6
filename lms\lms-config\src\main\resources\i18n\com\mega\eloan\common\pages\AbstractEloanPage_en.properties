systemname=Credit Management <br/> System
page.title=Mega Bank eLoan System
yes=Yes
no=No
have=Exist
nohave=None
nil=None
null=N.A.
close=Close
cancel=Cancel
noData=Data not found, please search again.
sessionTimeout=Your login session has expired\n Please log in from the employee intranet
loginOther=You already login at someplace
login.branch=Login Branch
plsSel=Please Select
PaginationText=Page: page {0} of {1}
doc.status=Case Status
noAuth=No Auth
findDataCnt=Find: {0}
#--------Edit Button Nav ----------------------#
# here is default value ,
# \u82e5\u8981\u4f9dPage\u8b8a\u66f4\u5b57\u773c\uff0c\u8acb\u65bc\u81ea\u5df1\u7684WebPage/Panel\u8986\u84cb\u539f\u6709\u7684key\u5373\u53ef
#----------------------------------------------#
button.filter=Filter
button.search=Inquiry
button.add=Add
button.modify=Edit
button.delete=Delete
button.save=Save
button.cancel=Exit Without Saving
button.view=Retrieve
button.edit=Edit
button.send=Submit For Supervisor's Approval
button.send2=Submit For Supervisor's Approval
button.accept=Approval
button.return=Return
button.confirm=Confirm
button.exit=Exit
button.print=Print
button.previewprint=Print preview
button.create=Generate
button.copy=Copy
button.deliver=Send
button.calc=Calculations
button.check=Verifier
button.reUpMIS=Re-upload MIS
button.checknotes=Input Opinions
button.pullin=Import
button.StanLang=Standard Term
button.changebrn=Change Submitting Branch
button.acptdeliver=Supervisor's Approval & Send
button.pullinAgain=Re-import
button.pullinReport=Import Current Data
button.PullinBusiness=Generate Credit Application Approval Summary By Business Unit
button.SummaryReport=Summary Report
button.SendDocTypeReport=Send To Credit Administration Division
button.CreateReport=Generate Report
button.LongError=\u767b\u9304\u70ba\u8cc7\u6599\u6709\u8aa4
button.SendHqTypeReport=Send To Head Office
button.LongViewMemo=Imput/Retrieve Approval Remarks
button.trans=Sender Branch
button.ProduceList=Generate List
button.ProduceExcel=Generate Excel File
button.btnBatchAprvProd69=\u52de\u5de5\u7d13\u56f0\u6848\u6574\u6279\u8986\u6838
button.btnBatchSelectAprvProd69=\u52de\u5de5\u7d13\u56f0\u6848\u6574\u6279\u52fe\u9078
button.Maintain=Credit Review Control File Maintenance
button.InsertExcelData=Generate New Corporate Account/Limit Increase List
button.InsertInExcelData=Generate list of corporate accounts that are not subjected to credit review
button.InsertUnExcelData=Generate a list of corporate accounts which did not complete credit reviews within the specified deadline
button.InsertLateExcelData=Generate New Customer/Limit Increase Overdue Checklist
button.InsertRecExcelData=Generate Recent Credit Review Checklist
button.AllSend=Batch Approval
button.ExceptRetrialDate=Change Credit Review Date
button.ReturnToCompiling=\u9000\u56de\u7de8\u88fd\u4e2d
button.ProduceEvaluateTbl=\u7522\u751f\u8986\u5be9\u8003\u6838\u8868
button.SendBtt=\u91cd\u65b0\u4e0a\u50b3\u540d\u55ae\u5230BTT
button.SendRetrialReport=Send Credit Review Worksheet
button.CallCenterSendCreditCase=\u50b3\u9001\u4fe1\u8cb8\u6848\u4ef6
button.changecaseformat=Change Case Format
button.changecaseformat1=\u6388\u6b0a\u5916\u8b8a\u66f4\u6388\u6b0a\u5167
button.remover=Refer To Inspected Unit For Input
button.ProducePaper=Credit Review Worksheet
button.SendNextG=Send To Internal Audit
button.OpenLmsCase=Open Credit Application Approval Summary
button.Change=Credit Term Amendment/Renewal
button.TableSend=Send Credit Facility Report To Affiliated Banks
button.PrintBook=Print Credit Approval Notice
button.PrintNote=\u8cb8\u653e\u524d\u7167\u6703\u501f\u6b3e\u4eba\u4f5c\u696d\u6aa2\u6838\u8868
button.CaseCopy=Copy Case
button.login1=Input Credit Review Committee Meeting Date
button.login2=Input Collection Committee Meeting Date
button.login3=Input Board Of Managing Directors Meeting Date
button.login4=Input Audit Committee Meeting Date
button.creDoc1=Managing Director's Draft
button.getCase=Incoming
button.PrintArea=Print Business Center's Opinion
button.SendAllToG=Send Batch To Internal Audit
button.PrintAll=Batch Print
button.ToReviewAll=Send Batch To Supervisor For Approval
button.ReviewAll=Batch Approval
button.PrintAllAudit=Batch Print Worksheet
button.PrintAllBill=Batch Print Bank Statement
button.ApprCreditAndCase=Approval Credit Facility Report And Case Report
button.casetochange=Reassign Cases
button.login=Register
button.sendcase=Proposal
button.backcase=Withdraw/Appeal
button.backcase2=Withdraw
button.backunit=Return to branch for correction
button.backArea=Return To Regional Center For Correction
button.openlms=Open Credit Application Approval Summary
button.rebackunit=Resend To Branch
button.backInHead=Return For Correction
button.backSea=\u9000\u56de\u6703\u7c3d\u610f\u898b
button.cancelCheck=Cancel Approval
button.backToHeadFirst=Return to Review In Progress
button.send3=Submit For Supervisor's Release
button.send4=Maintain the Inspected Unit's Review Progress
button.Update491=Completed and upload the Credit Review Control File
flow.flowLogMemo=Approval Remarks
flow.qirdtitle=Approval History
flowCheck.SameMarkerChecker=The verifier cannot be the same person as "the handling officer or other verifiers"
flowCheck.statusError=Document status has been changed, please load in the updated information.
button.btnUnlock=unlocked documents
button.noNeedCheck=\u514d\u6279\u8986
button.ReturnPage=Return
button.upload=Upload
button.ChangeRow=\u4fee\u6539\u5df2\u9078\u53d6\u8cc7\u6599
button.modifyCaseLvl=\u4fee\u6539\u5be9\u6838\u5c64\u7d1a
button.UPCls=\u4e0a\u50b3\u623f\u8cb8\u8a55\u5206\u5361
button.editOK=\u7de8\u88fd\u5b8c\u6210
button.queryStop=\u67e5\u8a62\u76ee\u524d\u505c\u6b0a\u8d77\u8fc4
button.allDetails=\u7522\u751f\u8cc7\u6599\u6e05\u518a
button.importRPA=\u5f15\u5165RPA\u67e5\u8a62\u7d50\u679c
button.CntrNoControl=Contract Control
button.UpdCustId=\u4fee\u6539\u5ba2\u6236\u7d71\u7de8
button.remove=Remove
button.UpTransferId=\u4e0a\u50b3\u6d77\u5916\u5206\u884c\u79fb\u8f49\u540d\u55ae
#J-107-0390_05097_B1001 \u5206\u884c\u6b0a\u9650\u4e4b\u6388\u4fe1\u6848\u4ef6\u82e5\u65bc\u8986\u6838\u5f8c\u6b32\u4fee\u6539,\u5f97\u6388\u6b0a\u4e3b\u7ba1\u5f97\u9000\u56de\u81f3\u7de8\u88fd\u4e2d
button.backApprove=Returned reviewed cases
button.printContract=\u5217\u5370\u5408\u7d04\u66f8\u7b49\u76f8\u95dc\u7c3d\u7d04\u6587\u4ef6
button.preview =Preview
#J-109-0304_10702_B1005 Web e-Loan\u623f\u4ef2\u56de\u994b\u91d1\u6574\u6279\u8f38\u5165
button.SaveRebate=\u5132\u5b58\u5f15\u4ecb\u734e\u91d1
button.changeVer=\u7248\u672c\u8b8a\u66f4
#J-110-0373 \u4e2d\u92fc\u6d88\u8cb8
button.CreateCSCExcel=\u7522\u751f\u4e2d\u92fc\u6d88\u8cb8Excel
#J-110-0354 \u9032\u4ef6\u7ba1\u7406\u6539\u6d3e
button.CaseToChangeSignEmp=\u6539\u6d3e\u7c3d\u6848\u884c\u54e1
button.CaseReturn=\u72c0\u614b\u6062\u5fa9
#J-110-0308 \u8986\u5be9\u8003\u6838\u8868
button.ProduceRankingBoard=\u7522\u751f\u6392\u540d\u8868
button.SendToCtrlDept=\u50b3\u9001\u6388\u7ba1\u8655
#J-111-0551_05097_B1006 Web e-Loan\u6388\u4fe1\u4e4b\u4fe1\u7528\u98a8\u96aa\u7ba1\u7406\u9075\u5faa\u6aa2\u6838\u8868\u53ca\u501f\u6b3e\u4eba\u66a8\u95dc\u4fc2\u6236\u8207\u672c\u884c\u6388\u4fe1\u5f80\u4f86\u60c5\u5f62\u53ca\u5229\u6f64\u8ca2\u737b\u5ea6\u7d0d\u5165\u5728\u9014\u6848\u4ef6\u4e4b\u984d\u5ea6
button.approveUnestablshExl=Query The Approved Credit Line of Unestablished Account
#J-111-0602 \u6b61\u559c\u4fe1\u8cb8eLoan\u6d41\u7a0b\u7cbe\u9032\u4fee\u6539-\u5fb5\u5be9\u5206\u6848\u6d41\u7a0b\u8b8a\u52d5
button.OneButtonAssignCase=\u4e00\u9375\u5206\u6848
#\u67E5\u8A62\u5BA2\u6236\u7533\u8CB8\u7D00\u9304
button.QueryCustLoanRecord=\u67E5\u8A62\u5BA2\u6236\u7533\u8CB8\u7D00\u9304
button.BatchTask=Whole Batch Job
button.BatchApproved=Whole Batch Approve
button.SendToExamUnit=Send To Exam Unit
#-------\u6587\u4ef6\u8cc7\u8a0a----------------------#
doc.id=Unified Business Number
doc.custId=Unified Business Number
doc.custName=Name
doc.idDup=Repeat Serial No.
doc.docinfo=Document Information
doc.baseInfo=Basic Profile
doc.branchName=Branch name
doc.docStatus=Document Status
doc.docUpdateLog=Document Revision History
doc.getUpdateLog=Retrieve Document Revision History
doc.creator=Document Creator
doc.lastUpdater=Last Editor
doc.docCode=Random Report Code
doc.sentTime=Time Sent
doc.checkProgress=Approval Result & Decision
doc.caseNo=Case No.
#------\u5340\u90e8\u5225----------------------#
typCd.title=Region/Department
typCd.1=DBU
typCd.4=OBU
typCd.5=Overseas
header.username=Originating Staff:
header.position=Authority Level:
header.loginTime=Input Date:
header.systems=Related Systems:
header.locale=Language:
txn.success=Transaction Successful
newCase.Y=New Loan
newCase.N=Existing Case
docCheck.otherOpen=This document is currently opened by [$\{userId\}-$\{userName\}], and hence the system will open it as read-only.
docCheck.froceOpen=This document is currently opened by [$\{userId\}-$\{userName\}]; for full access right to this document, please press confirm.
flow.oddStatus=Document status has been changed, please exit first.
caseNumber=$\{year\}$\{branchName\}(Mega)$\{kind\}-$\{number\} 
#--------\u6848\u4ef6\u96b8\u5c6c\u90e8\u9580 ----------------------#
caseDept.1=Corporate Banking Department
caseDept.2=Personal Banking Department
caseDept.3=Corporate Banking + Personal Banking
#--------\u8fa6\u7406\u55ae\u4f4d\u985e\u5225 ----------------------#
unitType.1=Handled By The Bank
unitType.2=Regional Headquarter
unitType.3=Credit Division
unitType.4=Credit Administration Division
#--------\u6587\u4ef6\u7570\u52d5\u8a18\u9304 ----------------------#
docLog.C=File Creation
docLog.R=Retrieve
docLog.S=Edit
docLog.D=Delete
docLog.Q=Inquiry
docLog.M=Send
docLog.A=Verifier
docLog.B=Return
docLog.J=Reject
docLog.E=Transaction
docLog.F=Submit To Supervisor
docLog.P=Print
docLog.K=Copy
docLog.k=Copy
docLog.O=Others
docLog.H=History
docLog.X=\u6388\u6b0a\u5916\u8f49\u6388\u6b0a\u5167
docLog.Z=Case Closed
#--------\u6587\u4ef6\u72c0\u614b\u8868 ----------------------#
docStatus.010=Compiling In Progress
docStatus.01O=Compiling In Progress
docStatus.210=Compiling In Progress
docStatus.220=Pending Approval
docStatus.020=Pending Approval
docStatus.02O=Pending Approval
docStatus.0DO=Pending Approval
docStatus.230=Approved
docStatus.03O=Approved
docStatus.030=Approval
docStatus.04O=Pending Approval
docStatus.04J=Pending Approval
#J-108-0166 \u793e\u6703\u8207\u74b0\u5883\u98a8\u96aa\u8a55\u4f30\u6539\u7248  \u62d2\u8cb8
docStatus.040=Reject
docStatus.06O=Reject
docStatus.05O=Approved
docStatus.060=Case Closure
docStatus.0A0=\u5df2\u7522\u751f\u8986\u5be9\u540d\u55ae\u5831\u544a\u6a94
docStatus.0C0=\u5f85\u89e3\u9664
docStatus.0D0=\u5df2\u89e3\u9664
#--------\u6388\u7ba1\u8655----#
docStatus.L1H=Review In Progress
docStatus.L6H=Pending Release
#--------\u5340\u57df\u4e2d\u5fc3----#
docStatus.L1C=Review In Progress
docStatus.L2C=Pending Release
docStatus.07O=Pending Documents
docStatus.240=Reject
docStatus.270=Pending Documents
docStatus.2D0=Deleted
docStatus.2H0=History
docStatus.21H=Compiling In Progress
docStatus.22H=Pending Approval
docStatus.23H=Approved
docStatus.2HH=History
docStatus.2AH=Group Pending Initial Rating
docStatus.21S=Compiling In Progress
docStatus.22S=Pending Approval
docStatus.23S=Acceptance Granted
docStatus.24S=Reject
docStatus.2DS=Deleted
docStatus.22A=Pending Approval
docStatus.23A=Approval
docStatus.24A=Reject
docStatus.2DA=Deleted
docStatus.31B=Compiling In Progress
docStatus.32B=Pending Approval
docStatus.33B=Approved
docStatus.30G=Compiling In Progress
docStatus.31G=Compiling In Progress
docStatus.32G=Pending Approval
docStatus.33G=Approved
#--------\u6703\u7c3d----#
docStatus.LWC=Circulating
docStatus.LXC=Pending Release
docStatus.LYC=Circulated
docStatus.L3G=Proposal Pending Input
docStatus.L4G=Proposal Pending Approval
docStatus.L3M=Proposal Pending Input
docStatus.L4M=Proposal Pending Approval
docStatus.L3M_JP=Manager Branch Pending Input
docStatus.L4M_JP=Manager Branch Pending Approval
docStatus.03B=Proposal Pending Input
docStatus.04B=Proposal Pending Approval
docStatus.LCH=Pending Appeal
docStatus.0EO=Case Withdrawal

docStatus.01K=\u7de8\u88fd\u4e2d(\u6703\u7c3d\u5f8c\u4fee\u6539)
docStatus.02K=\u5f85\u8986\u6838(\u6703\u7c3d\u5f8c\u4fee\u6539)
docStatus.03K=\u5df2\u6703\u7c3d
docStatus.04K=\u5f85\u8986\u6838(\u5df2\u6703\u7c3d)

#J-107-0390_05097_B1001 \u5206\u884c\u6b0a\u9650\u4e4b\u6388\u4fe1\u6848\u4ef6\u82e5\u65bc\u8986\u6838\u5f8c\u6b32\u4fee\u6539,\u5f97\u6388\u6b0a\u4e3b\u7ba1\u5f97\u9000\u56de\u81f3\u7de8\u88fd\u4e2d
docStatus.DEL=Deleted
docStatus.BKL=Approved return

#--------\u64d4\u4fdd\u54c1\u6587\u4ef6\u72c0\u614b----#
status.11B=Compiling In Progress
status.12B=Pending Approval
status.13B=Approved
status.14B=Pending set
status.15B=Has been set
status.16B=Pending deregister
status.17B=Deregistered
status.18B=Inter-Branch returned
status.19B=Inter-Branch returned not adopted
status.1AB=On behalf of the valuation compiling progressing
status.1BB=On behalf of the valuation pending Approval
status.1CB=On Behalf of valuation completed
status.1AC=Business Center Compiling In Progress
status.1BC=Business Center Pending Approval
status.1CC=Business Center Approved
status.1DC=Business Center Returned
status.1FC=Business Center Returned not adoption & Pending Approval
status.1GC=Business Center Returned not adoption & Approval
status.17S=Penging Forced Liquidation
status.19S=Forced Liquidation
status.1AS=Supplementation
status.1BS=Insufficient Collateral maintenance ratio
status.1HC=Regional Credit Management Center to be received
status.1IC=The regional credit management center reviews pending documents
status.1JC=The regional credit management center review is preparing
status.1KC=Review by the regional credit management center to be reviewed
status.1LC=Regional credit management center review has been reviewed
status.1MC=The review of the regional credit management center has been sent back
#--------\u7c3d\u7ae0\u6b04----#
checkPerson.L1=\u7d93\u8fa6
checkPerson.L2=\u5e33\u6236\u7ba1\u7406\u54e1
checkPerson.L3=\u6388\u4fe1\u4e3b\u7ba1
checkPerson.L4=\u6388\u4fe1/\u8986\u6838\u4e3b\u7ba1
checkPerson.L5=\u55ae\u4f4d/\u6388\u6b0a\u4e3b\u7ba1
checkPerson.L9=\u55ae\u4f4d\u4e3b\u7ba1

#---- \u500b\u91d1\u6587\u4ef6\u72c0\u614b -----
CLSDocStatus.01O=Compiling In Progress
CLSDocStatus.02O=Pending Approval
CLSDocStatus.03O=Approved
CLSDocStatus.04O=\u5148\u884c\u52d5\u7528-\u5f85\u8986\u6838
CLSDocStatus.05O=\u5148\u884c\u52d5\u7528-\u5df2\u8986\u6838


#---- \u76f8\u95dc\u5fb5\u4fe1\u6cbf\u7528\u8a0a\u606f----#
ces1401.41162=EFD0038\uff1a\u4e3b\u6a5f\u8cc7\u6599\u5eab\u4e2d\u7121\u8a72\u5ba2\u6236\u4e4b\u806f\u5fb5\u4e2d\u5fc3\u4fe1\u7528\u5361\u76f8\u95dc\u8cc7\u8a0a!(\u8acb\u78ba\u5b9a\u662f\u5426\u5df2\u57f7\u884c\u904e\u7d44\u5408\u67e5\u8a62)
ces1401.41152=\u7121\u4fdd\u8b49\u50b5\u52d9\u91d1\u984d\u3002
ces1401.41154=$\{ID\} \u65bc\u672c\u884c0024\u5efa\u6a94\u4e4b\u806f\u5fb5\u865b\u64ec\u7d71\u7de8\u70ba $\{ID2\}\uff0c\u662f\u5426\u6b63\u78ba\uff1f
ces1401.41156=\u672a\u67e5\u8a62\u806f\u5fb5EJCIC\u7522\u54c1\u3010\u7d44\u5408\u4e8c\u3011
ces1401.41157=\u81ea 099/10/01 \u8d77\u67e5\u8a62\u806f\u5fb5EJCIC\u7522\u54c1\u3010\u7d44\u5408\u4e8c\u3011\u81ea\u52d5\u7d0d\u5165K33 \u67e5\u8a62\uff0c\u7136\u76ee\u524d\u8cc7\u6599\u5eab\u4e2d<br/>\u806f\u5fb5\u67e5\u8a62\u65e5\u671f\u70ba $\{DD2\} \uff0c\u8acb\u91cd\u65b0\u67e5\u8a62\u7522\u54c1\u3010\u7d44\u5408\u4e8c\u3011\u65b9\u80fd\u5f15\u9032\u6700\u65b0\u4fe1\u7528\u5361\u8cc7\u6599\uff01
ces1401.41158=\u672a\u67e5\u8a62\u806f\u5fb5EJCIC\u7522\u54c1\u3010\u7d44\u5408\u4e09\u3011 \u3001 \u3010\u7d44\u5408\u56db\u3011\u6216 \u3010\u7d44\u5408\u4e03\u3011
ces120mg.015=\u8cc7\u6599\u67e5\u8a62\u5b8c\u6210
ces1401.4280=\u4e3b\u6a5f\u8cc7\u6599\u5eab\u4e2d\u7121\u8a72\u516c\u53f8\u4e4b\u903e\u671f\u50ac\u6536\u53ca\u5446\u5e33\u8cc7\u8a0a\uff01
ces140.msg035=\u7968\u4fe1\u8cc7\u6599\u5f15\u9032\u6210\u529f\uff01
ces1401.4280=\u4e3b\u6a5f\u8cc7\u6599\u5eab\u4e2d\u7121\u8a72\u516c\u53f8\u4e4b\u903e\u671f\u50ac\u6536\u53ca\u5446\u5e33\u8cc7\u8a0a\uff01
ces140.msg035=\u7968\u4fe1\u8cc7\u6599\u5f15\u9032\u6210\u529f\uff01
ces1401.msg033=EFD0038\uff1a\u8a72\u4e3b\u6a5f\u8cc7\u6599\u5eab\u4e2d\u7121\u8a72\u8ca0\u8cac\u4eba\u6216\u9023\u4fdd\u4eba\u5b58\u6b3e\u8cc7\u6599
ces1401.41M01=\u9918\u984d\u65e5\u671f\uff1a
ces1401.41163=\u5176\u4ed6\uff0c\u5171$\{rcrd\}\u7b46
ces1401.41143=\u903e\u671f\u672a\u9084\u7e3d\u91d1\u984d\uff1a$\{totPass\}
ces1401.41144=\u7121\u903e\u671f\u672a\u9084\u91d1\u984d\u3002
ces1401.41151=\u7121\u501f\u6b3e\u91d1\u984d\u3002
ces1401.41149=\u4e3b\u6a5f\u8cc7\u6599\u5eab\u7121\u8a72\u5ba2\u6236\u500b\u4eba\u501f\u6b3e\u8cc7\u6599\uff01
ces1401.41166=\u5171\u540c\u50b5\u52d9\u6388\u4fe1\u672a\u903e\u671f\u91d1\u984d $\{totLoan\} \u4edf\u5143
ces1401.41167=\u903e\u671f\u672a\u9084\u91d1\u984d( $\{totPass\} \u4edf\u5143)\u3002
ces1401.41168=\u7121\u903e\u671f\u672a\u9084\u91d1\u984d\u3002
ces1401.41169=\u4e3b\u6a5f\u8cc7\u6599\u5eab\u4e2d\u7121\u8a72\u5ba2\u6236\u500b\u4eba\u5171\u540c\u50b5\u52d9\u8cc7\u6599\uff01
ces1401.41145=\u672a\u903e\u671f\u7e3d\u91d1\u984d\uff1a$\{BAL\}
ces1401.41146=\u7121\u672a\u903e\u671f\u7e3d\u91d1\u984d\u3002
ces1401.41147=\u903e\u671f\u7e3d\u91d1\u984d\uff1a$\{PASS\}
ces1401.41148=\u7121\u903e\u671f\u7e3d\u91d1\u984d\u3002
ces1401.41150=\u4e3b\u6a5f\u8cc7\u6599\u5eab\u4e2d\u7121\u8a72\u5ba2\u6236\u500b\u4eba\u4fdd\u8b49\u50b5\u52d9\u8cc7\u6599\uff01
ces140.msg036=\u627e\u4e0d\u5230\u8a72\u95dc\u9375\u5b57\u4e4b\u5167\u5bb9 ! (\u7121$\{noBankMsg\}\u8a72\u91d1\u878d\u6a5f\u69cb\u4e4b\u8cc7\u6599)
c120.none=\u7121
ces1205.0708=\u5176\u4ed6
ces1405.msg007=\u8acb\u5148\u8f38\u5165\u96c6\u5718\u4ee3\u865f
ces1405.31M01=\u7968\u4ea4\u6240\u8cc7\u6599\u5eab
ces1405.21M03=\u8b49\u5238\u9055\u7d04\u4ea4\u5272
ces1401.msg010=\u7d71\u4e00\u7de8\u865f\u672a\u8f38\u5165
ces1401.msg011=\u500b\u4eba\u6536\u652f\u91d1\u984d\u55ae\u4f4d/\u5e63\u5225\u672a\u8f38\u5165
ces1401.41141=\u3010\u672c\u570b\u9280\u884c\u3011
ces1401.41142=\u3010\u5916\u570b\u9280\u884c\u5728\u53f0\u5206\u884c\u3011
ces1401.9183=1.\u570b\u5167\u6388\u4fe1\uff1a\u806f\u5fb5\u4e2d\u5fc3\u8cc7\u6599\u5eab($\{qDate\}) 2.\u672c\u884cOBU\u6388\u4fe1\uff1aXXX(YYYY-MM-DD) 3.\u672c\u884c\u6d77\u5916\u5206\u884c\u6388\u4fe1\uff1aXXX(YYYY-MM-DD)
ces1401.09M01=\u4e3b\u6a5f\u8ca1\u52d9\u8cc7\u6599\u5eab
ces1405.11M01=\u2026\u7b49$\{population\}\u4f4d\u3002
ces1405.normal0=(\u975e\u5236\u5f0f)
bank.tit=\u5146\u8c50\u570b\u969b\u5546\u696d\u9280\u884c
ces1401.9192=\u5c1a\u6709\u4ee5\u4e0b\u4f01\u696d\u672a\u9032\u884c\u7d44\u5408\u67e5\u8a62\u4f5c\u696d\uff1a<br/>$\{IDs\}
option.001=\u6709
option.002=\u7121
ces1401.9189=\u8a72\u96c6\u5718\u4e4b\u8f44\u4e0b\u516c\u53f8$\{option\}\u9000\u7968\u7d00\u9304\uff01
ces1401.9190=\u8a72\u96c6\u5718\u4e4b\u8f44\u4e0b\u516c\u53f8$\{option\}\u62d2\u7d55\u5f80\u4f86\u7d00\u9304\uff01
ces1401.9191=\u8a72\u96c6\u5718\u4e4b\u8f44\u4e0b\u516c\u53f8$\{option\}\u903e\u671f\u3001\u50ac\u6536\u3001\u5446\u5e33\u8cc7\u8a0a\uff01
ces1401.9192=\u5c1a\u6709\u4ee5\u4e0b\u4f01\u696d\u672a\u9032\u884c\u7d44\u5408\u67e5\u8a62\u4f5c\u696d\uff1a<br/>$\{IDs\}
ces1401.9193=\u6de8\u503c
ces1401.msg029=EFD0015\uff1a$\{noticeMsg\}
ces1401.9186=EFD0018\uff1a\u96c6\u5718\u4f01\u696d\u7968\u4fe1\u8cc7\u8a0a\u5f15\u9032\u4f5c\u696d\uff0c\u57f7\u884c\u6210\u529f\uff01
ces1401.32M31=\u806f\u5fb5\u4e2d\u5fc3/\u7968\u4ea4\u6240\u8cc7\u6599\u5eab 
pcType.1=\u8ca0\u8cac\u4eba
pcType.2=\u9023\u4fdd\u4eba
pcType.3=\u8ca0\u8cac\u4eba/\u9023\u4fdd\u4eba
pcSex.1=\u7537
pcSex.2=\u5973
#\u9650\u5b9aCKEDIT\u4e00\u884c\u5b57\u6578\u6709\u591a\u9577\u7684\u5099\u8a3b
lms.ckeditRemark1=\u8a3b1:\u5efa\u8b70\u5b57\u578b16
lms.ckeditRemark2=\u8a3b2:|\u2190\u5b57\u578b16\u6642\u5efa\u8b70\u63db\u884c
lms.ckeditRemark3=\u8a3b1:|\u2190\u5efa\u8b70\u63db\u884c
#\u64d4\u4fdd\u54c1\u6587\u4ef6\u8aaa\u660e
dscr.0=\u90e8\u4efd\u5857\u92b7\u91cd\u9451\u4f30
dscr.1=\u91cd\u9451\u4f30
dscr.2=\u91cd\u8a2d\u5b9a
dscr.3=\u8907\u88fd\u4fee\u6539\u4e2d
dscr.4=\u9000\u56de\u806f\u884c
dscr.5=\u56de\u5340\u57df\u71df\u904b\u4e2d\u5fc3
dscr.6=\u4e3b\u7ba1\u9000\u56de
dscr.7=\u90e8\u4efd\u5857\u92b7
dscr.8=\u90e8\u4efd\u5857\u92b7\u4e2d
dscr.9=\u8907\u88fd\u4fee\u6539
dscr.A=\u91cd\u9451\u4f30\u4e2d
dscr.B=\u91cd\u8a2d\u5b9a\u4e2d
dscr.C=\u59d4\u8a17\u91cd\u9451\u4f30
dscr.D=\u59d4\u8a17\u91cd\u9451\u4f30\u4e2d

CLS1141.NoneProd69error=\u975e\u7d13\u56f0\u4fe1\u4fdd\u6848\u4ef6
#J-113-0306 \u6848\u4ef6\u7c3d\u5831\u66f8\u9001\u5448\u5340\u57df\u4e2d\u5fc3\u5be9\u6838\u5f8c\uff0c\u82e5\u88ab\u9000\u4ef6\uff0c\u5728\u300c\u5f85\u88dc\u4ef6/\u64a4\u4ef6\u300d\u4e2d\u4e4b\u88ab\u64a4\u4ef6\u4e4b\u6848\u4ef6\uff0c\u80fd\u5426\u8a2d\u8a08\u53ef\u4ee5\u518d\u6488\u5230\u7de8\u88fd\u4e2d\u91cd\u65b0\u7c3d\u5831\uff0c\u4ee5\u589e\u9032\u4f5c\u696d\u6548\u7387
button.ReBackApproveUnit=Returned reviewed cases