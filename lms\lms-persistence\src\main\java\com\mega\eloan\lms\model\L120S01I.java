/* 
 * L120S01I.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotNull;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 個金服務單位檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S01I", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class L120S01I extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 服務單位名稱 **/
	@Column(name="COMNAME", length=60, columnDefinition="VARCHAR(60)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String comName;

	/** 
	 * 服務單位地址<p/>
	 * 64個全型字
	 */
	@Column(name="COMADDR", length=192, columnDefinition="VARCHAR(192)")
	private String comAddr;

	/** 服務單位電話 **/
	@Column(name="COMTEL", length=20, columnDefinition="VARCHAR(20)")
	private String comTel;

	/** 到職日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="WORKDATE", columnDefinition="DATE")
	private Date workDate;

	/** 
	 * 職業別大類<p/>
	 * 詳註一
	 */
	@Column(name="JOBTYPE1", length=2, columnDefinition="CHAR(2)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String jobType1;

	/** 
	 * 職業別細項<p/>
	 * 詳註一
	 */
	@Column(name="JOBTYPE2", length=1, columnDefinition="CHAR(1)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String jobType2;

	/** 
	 * 職稱<p/>
	 * (單選)<br/>
	 *  上市或上櫃公司之負責人|g<br/>
	 *  上市或上櫃公司之總經理、副總或執行長|h<br/>
	 *  上市或上櫃公司之董監事及主管及人員|a<br/>
	 *  非上市或上櫃公司之負責人|i<br/>
	 *  非上市或上櫃公司之總經理、副總或執行長|j<br/>
	 *  非上市或上櫃公司之董監事及主管及人員|b<br/>
	 *  其他機構主管級人員|c<br/>
	 *  一般職員|d<br/>
	 *  服務人員|e<br/>
	 *  其他|f
	 */
	@Column(name="JOBTITLE", length=1, columnDefinition="CHAR(1)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String jobTitle;

	/** 年資 **/
	@Column(name="SENIORITY", columnDefinition="DECIMAL(2,0)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private Integer seniority;

	/** 年薪(幣別) **/
	@Column(name="PAYCURR", length=3, columnDefinition="VARCHAR(3)")
	private String payCurr;

	/** 
	 * 年薪(金額)<p/>
	 * (提供報送聯徵DBR22倍用)
	 */
	@Column(name="PAYAMT", columnDefinition="DECIMAL(13,0)")
	private Long payAmt;

	/** 
	 * 其他收入項目<p/>
	 * 100/12/08新增<br/>
	 *  (複選) 01|02|…<br/>
	 *  01.薪資所得<br/>
	 *  02.利息所得<br/>
	 *  03.營利所得<br/>
	 *  04.租賃所得<br/>
	 *  05.權利金所得<br/>
	 *  06.自力耕作、漁、牧、林、礦所得<br/>
	 *  07.執行業務所得<br/>
	 *  08.著作人、稿費、版稅、鐘點費等<br/>
	 *  09.財產交易所得<br/>
	 *  10.競技、競賽及機會中獎獎金<br/>
	 *  11.退職所得<br/>
	 *  12.其他所得<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	@Column(name="OTHTYPE", length=36, columnDefinition="VARCHAR(36)")
	private String othType;

	/** 
	 * 其他收入(幣別)<p/>
	 * 100/12/08新增<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	@Column(name="OTHCURR", length=3, columnDefinition="VARCHAR(3)")
	private String othCurr;

	/** 
	 * 其他收入(金額)<p/>
	 * 100/12/08新增<br/>
	 *  (提供報送聯徵DBR22倍用)<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	@Column(name="OTHAMT", columnDefinition="DECIMAL(13,0)")
	private Long othAmt;

	/** 經歷 **/
	@Column(name="EXPERIENCE", length=60, columnDefinition="VARCHAR(60)")
	private String experience;

	/** 
	 * 個人所得證明文件<p/>
	 * (單選)<br/>
	 *  個人綜合所得申報資料|1<br/>
	 *  扣繳憑單|2<br/>
	 *  薪資轉帳存摺|3<br/>
	 *  勞保薪資|4<br/>
	 *  租賃契約|5<br/>
	 *  其他收入證明|6
	 */
	@Column(name="INDOC", length=1, columnDefinition="CHAR(1)")
	@NotNull(message="{required.message}", groups=Check.class)
	@NotEmpty(message="{required.message}", groups=Check.class)
	private String inDoc;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得服務單位名稱 **/
	public String getComName() {
		return this.comName;
	}
	/** 設定服務單位名稱 **/
	public void setComName(String value) {
		this.comName = value;
	}

	/** 
	 * 取得服務單位地址<p/>
	 * 64個全型字
	 */
	public String getComAddr() {
		return this.comAddr;
	}
	/**
	 *  設定服務單位地址<p/>
	 *  64個全型字
	 **/
	public void setComAddr(String value) {
		this.comAddr = value;
	}

	/** 取得服務單位電話 **/
	public String getComTel() {
		return this.comTel;
	}
	/** 設定服務單位電話 **/
	public void setComTel(String value) {
		this.comTel = value;
	}

	/** 取得到職日期 **/
	public Date getWorkDate() {
		return this.workDate;
	}
	/** 設定到職日期 **/
	public void setWorkDate(Date value) {
		this.workDate = value;
	}

	/** 
	 * 取得職業別大類<p/>
	 * 詳註一
	 */
	public String getJobType1() {
		return this.jobType1;
	}
	/**
	 *  設定職業別大類<p/>
	 *  詳註一
	 **/
	public void setJobType1(String value) {
		this.jobType1 = value;
	}

	/** 
	 * 取得職業別細項<p/>
	 * 詳註一
	 */
	public String getJobType2() {
		return this.jobType2;
	}
	/**
	 *  設定職業別細項<p/>
	 *  詳註一
	 **/
	public void setJobType2(String value) {
		this.jobType2 = value;
	}

	/** 
	 * 取得職稱<p/>
	 * (單選)<br/>
	 *  上市或上櫃公司之負責人|g<br/>
	 *  上市或上櫃公司之總經理、副總或執行長|h<br/>
	 *  上市或上櫃公司之董監事及主管及人員|a<br/>
	 *  非上市或上櫃公司之負責人|i<br/>
	 *  非上市或上櫃公司之總經理、副總或執行長|j<br/>
	 *  非上市或上櫃公司之董監事及主管及人員|b<br/>
	 *  其他機構主管級人員|c<br/>
	 *  一般職員|d<br/>
	 *  服務人員|e<br/>
	 *  其他|f
	 */
	public String getJobTitle() {
		return this.jobTitle;
	}
	/**
	 *  設定職稱<p/>
	 *  (單選)<br/>
	 *  上市或上櫃公司之負責人|g<br/>
	 *  上市或上櫃公司之總經理、副總或執行長|h<br/>
	 *  上市或上櫃公司之董監事及主管及人員|a<br/>
	 *  非上市或上櫃公司之負責人|i<br/>
	 *  非上市或上櫃公司之總經理、副總或執行長|j<br/>
	 *  非上市或上櫃公司之董監事及主管及人員|b<br/>
	 *  其他機構主管級人員|c<br/>
	 *  一般職員|d<br/>
	 *  服務人員|e<br/>
	 *  其他|f
	 **/
	public void setJobTitle(String value) {
		this.jobTitle = value;
	}

	/** 取得年資 **/
	public Integer getSeniority() {
		return this.seniority;
	}
	/** 設定年資 **/
	public void setSeniority(Integer value) {
		this.seniority = value;
	}

	/** 取得年薪(幣別) **/
	public String getPayCurr() {
		return this.payCurr;
	}
	/** 設定年薪(幣別) **/
	public void setPayCurr(String value) {
		this.payCurr = value;
	}

	/** 
	 * 取得年薪(金額)<p/>
	 * (提供報送聯徵DBR22倍用)
	 */
	public Long getPayAmt() {
		return this.payAmt;
	}
	/**
	 *  設定年薪(金額)<p/>
	 *  (提供報送聯徵DBR22倍用)
	 **/
	public void setPayAmt(Long value) {
		this.payAmt = value;
	}

	/** 
	 * 取得其他收入項目<p/>
	 * 100/12/08新增<br/>
	 *  (複選) 01|02|…<br/>
	 *  01.薪資所得<br/>
	 *  02.利息所得<br/>
	 *  03.營利所得<br/>
	 *  04.租賃所得<br/>
	 *  05.權利金所得<br/>
	 *  06.自力耕作、漁、牧、林、礦所得<br/>
	 *  07.執行業務所得<br/>
	 *  08.著作人、稿費、版稅、鐘點費等<br/>
	 *  09.財產交易所得<br/>
	 *  10.競技、競賽及機會中獎獎金<br/>
	 *  11.退職所得<br/>
	 *  12.其他所得<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	public String getOthType() {
		return this.othType;
	}
	/**
	 *  設定其他收入項目<p/>
	 *  100/12/08新增<br/>
	 *  (複選) 01|02|…<br/>
	 *  01.薪資所得<br/>
	 *  02.利息所得<br/>
	 *  03.營利所得<br/>
	 *  04.租賃所得<br/>
	 *  05.權利金所得<br/>
	 *  06.自力耕作、漁、牧、林、礦所得<br/>
	 *  07.執行業務所得<br/>
	 *  08.著作人、稿費、版稅、鐘點費等<br/>
	 *  09.財產交易所得<br/>
	 *  10.競技、競賽及機會中獎獎金<br/>
	 *  11.退職所得<br/>
	 *  12.其他所得<br/>
	 *  ※國內DBU/OBU才需填寫
	 **/
	public void setOthType(String value) {
		this.othType = value;
	}

	/** 
	 * 取得其他收入(幣別)<p/>
	 * 100/12/08新增<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	public String getOthCurr() {
		return this.othCurr;
	}
	/**
	 *  設定其他收入(幣別)<p/>
	 *  100/12/08新增<br/>
	 *  ※國內DBU/OBU才需填寫
	 **/
	public void setOthCurr(String value) {
		this.othCurr = value;
	}

	/** 
	 * 取得其他收入(金額)<p/>
	 * 100/12/08新增<br/>
	 *  (提供報送聯徵DBR22倍用)<br/>
	 *  ※國內DBU/OBU才需填寫
	 */
	public Long getOthAmt() {
		return this.othAmt;
	}
	/**
	 *  設定其他收入(金額)<p/>
	 *  100/12/08新增<br/>
	 *  (提供報送聯徵DBR22倍用)<br/>
	 *  ※國內DBU/OBU才需填寫
	 **/
	public void setOthAmt(Long value) {
		this.othAmt = value;
	}

	/** 取得經歷 **/
	public String getExperience() {
		return this.experience;
	}
	/** 設定經歷 **/
	public void setExperience(String value) {
		this.experience = value;
	}

	/** 
	 * 取得個人所得證明文件<p/>
	 * (單選)<br/>
	 *  個人綜合所得申報資料|1<br/>
	 *  扣繳憑單|2<br/>
	 *  薪資轉帳存摺|3<br/>
	 *  勞保薪資|4<br/>
	 *  租賃契約|5<br/>
	 *  其他收入證明|6
	 */
	public String getInDoc() {
		return this.inDoc;
	}
	/**
	 *  設定個人所得證明文件<p/>
	 *  (單選)<br/>
	 *  個人綜合所得申報資料|1<br/>
	 *  扣繳憑單|2<br/>
	 *  薪資轉帳存摺|3<br/>
	 *  勞保薪資|4<br/>
	 *  租賃契約|5<br/>
	 *  其他收入證明|6
	 **/
	public void setInDoc(String value) {
		this.inDoc = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
