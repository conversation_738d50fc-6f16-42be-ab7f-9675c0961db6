package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1131R04RptService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 借保人徵信資料
 * </pre>
 * 
 * @since 2012/11/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/12,Fantasy,new
 *          <li>2013/06/15,調整主借款人和配偶之利害關係人顯示排序問題
 *          <li>2013/07/11,Rex,修改顯示評等
 *          <li>2013/07/17,Rex,修改判斷非兆豐行庫改用判斷其帳戶為14碼的排除加總
 *          </ul>
 */
@Service("cls1131r04rptservice")
public class CLS1131R04RptServiceImpl implements FileDownloadService,
		CLS1131R04RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1131R04RptServiceImpl.class);

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
//			 baos = (ByteArrayOutputStream) this.generateReport(params);
//			 return baos.toByteArray();
			// J-109-0148 改為產生JSONObject
			return this.generateJSONObject(params);
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {

		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;

		try {
			locale = LMSUtil.getLocale();
			rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator generator = new ReportGenerator(
					"report/cls/CLS1131R04_" + locale.toString() + ".rpt");

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			Map<String, Object> stakeholderMap = (Map<String, Object>) params
					.get("stakeholderMap");
			Map<String, String> map = new HashMap<String, String>();
			List<Map<String, String>> list = new ArrayList<Map<String, String>>();

			map.put("CommonBean1.field01", "1");// 序號
			map.put("CommonBean1.field02",
					(String) stakeholderMap.get("custId"));// 統一編號
			map.put("CommonBean1.field03", (String) stakeholderMap.get("dupNo"));// 重複序號
			map.put("CommonBean1.field04", (String) stakeholderMap.get("name"));// 姓名
			map.put("CommonBean1.field05",
					(String) stakeholderMap.get("remain"));// 本行銀行法利害關係人
			map.put("CommonBean1.field06", (String) stakeholderMap.get("law44"));// 金控法第44條利害關係人
			map.put("CommonBean1.field07", (String) stakeholderMap.get("law45"));// 金控法第45條利害關係人
			map.put("CommonBean1.field08", (String) stakeholderMap.get("other"));// 實質關係人(授信以外交易)
			map.put("CommonBean1.field09",
					(String) stakeholderMap.get("director"));// 公司法與本行董事具有控制從屬關係公司
			list.add(map);

			generator.setRowsData(list);
			rptVariableMap.put("queryPerson", user.getUserName());
			generator.setVariableData(rptVariableMap);

			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 建立JSON
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws FileNotFoundException
	 */
	@SuppressWarnings("unchecked")
	public byte[] generateJSONObject(PageParameters params) throws Exception {

		Map<String, String> rptVariableMap = null;
		JSONObject json = new JSONObject();

		try {
			rptVariableMap = new LinkedHashMap<String, String>();

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			Map<String, Object> stakeholderMap = (Map<String, Object>) params
					.get("stakeholderMap");
			Map<String, String> map = new HashMap<String, String>();
			List<Map<String, String>> list = new ArrayList<Map<String, String>>();

			map.put("td0", "1");// 序號
			map.put("td1", (String) stakeholderMap.get("custId"));// 統一編號
			map.put("td2", (String) stakeholderMap.get("dupNo"));// 重複序號
			map.put("td3", (String) stakeholderMap.get("name"));// 姓名
			map.put("td4", (String) stakeholderMap.get("remain"));// 本行銀行法利害關係人
			map.put("td5", (String) stakeholderMap.get("law44"));// 金控法第44條利害關係人
			map.put("td6", (String) stakeholderMap.get("law45"));// 金控法第45條利害關係人
			map.put("td7", (String) stakeholderMap.get("other"));// 實質關係人(授信以外交易)
			map.put("td8", (String) stakeholderMap.get("director"));// 公司法與本行董事具有控制從屬關係公司
			list.add(map);

			rptVariableMap.put("queryPerson", user.getUserName());
			json.put("date",
					CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
			json.put("list", list);
			json.putAll(rptVariableMap);

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return json.toString().getBytes("utf-8");

	}
}
