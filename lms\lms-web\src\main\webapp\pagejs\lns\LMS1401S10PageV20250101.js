// 整支js為依照畫面欄位順序做排序
var l120s24bGrid;
// 合格金融擔保品grid
function drawL120S24BGrid(){
	l120s24bGrid = $("#gridviewL120S24B").iGrid({
		handler: 'lms1201gridhandler',
		height: 150,
		postData: {
			formAction: "queryL120s24bList",
			refOid_s24b: responseJSON.openL120s24aOid
		},
		rownumbers: true,
		multiselect : true,
		needPager: false,
		colModel: [{
			colHeader: i18n.lms1401s10["L120S24B.quaColl_s24b"],// 合格金融擔保品
			align: "left",
			width: 40, // 設定寬度
			formatter: 'click',
			onclick: loadS24BDoc,
			name: 'quaColl_s24b'
		}, {
			colHeader: i18n.lms1401s10["L120S24B.collCurr_s24b"],// 擔保品幣別
			align: "left",
			width: 20, // 設定寬度
			name: 'collCurr_s24b'
		}, {
			colHeader: i18n.lms1401s10["L120S24B.currSym_s24b"],// 幣別對稱<BR/>(擔保品幣別與額度相同)
			align: "left",
			width: 20, // 設定寬度
			name: 'currSym_s24b'
		}, {
			colHeader: i18n.lms1401s10["L120S24B.collAmt_s24b"],// 擔保品價值<BR/>(TWD/元)
			align: "right",
			width: 40, // 設定寬度
			name: 'collAmt_s24b'
		}, {
			colHeader: i18n.lms1401s10["L120S24B.disCollAmt_s24b"],// 折扣後擔保品價值<BR/>(TWD/元)
			align: "right",
			width: 40, // 設定寬度
			name: 'disCollAmt_s24b'
		}, {
			colHeader: "oid",
			name: 'oid',
			hidden: true
		}],
		ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = $("#gridviewL120S24B").getRowData(rowid);
			loadS24BDoc(null, null, data);
		}
	}).trigger("reloadGrid");
}

function drawBowrrowerClassGrid(){
	l120s24a_centralGov_div = $("#l120s24a_centralGov_div").iGrid({
		handler: 'lms1201gridhandler',
		height: 150,
		postData: {
			formAction: "queryL120s24aCentralGov"
		},
		rownumbers: true,
		multiselect : false,
		needPager: false,
		colModel: [{
			colHeader: i18n.lms1401s10["L120S24A.grid.bowrrowerClass.id"],// ID
			align: "left",
			width: 20, // 設定寬度
			name: 'id'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.bowrrowerClass.name"],// 戶名
			align: "left",
			width: 40, // 設定寬度
			name: 'name'
		}]
	}).trigger("reloadGrid");
	
	l120s24a_nonPro_div = $("#l120s24a_nonPro_div").iGrid({
		handler: 'lms1201gridhandler',
		height: 300,
		postData: {
			formAction: "queryL120s24aNonPro"
		},
		rownumbers: true,
		multiselect : false,
		needPager: false,
		colModel: [{
			colHeader: i18n.lms1401s10["L120S24A.grid.bowrrowerClass.id"],// ID
			align: "left",
			width: 20, // 設定寬度
			name: 'id'
		}, {
			colHeader: i18n.lms1401s10["L120S24A.grid.bowrrowerClass.name"],// 戶名
			align: "left",
			width: 40, // 設定寬度
			name: 'name'
		}]
	}).trigger("reloadGrid");
}

/* 移到LMS1401S10Panel.js
var L140S10Page = {
		initL120s24aPage : function(){
			// H.本額度有提供不動產擔保品會影響的欄位都先隱藏，等user選了"是"or"否"再show/hide
			$("#l120S24ADetailForm").find(".hasEstate_Y").hide();
			// P.本額度是否有合格金融擔保品，合格金融擔保品按鈕+grid區塊
			// $("#l120S24ADetailForm").find("#hasQuaColl_div").hide();
			// 四、保證資訊整個區塊先隱藏
			// $("#l120S24ADetailForm").find("#gutArea4Div").hide();
			// R-1是否有下列類型保證，四、保證資訊下的欄位都先隱藏
			$("#l120S24ADetailForm").find(".hasGut_Y").hide();
			// 保證類別_其他說明欄位先隱藏
			$("#l120S24ADetailForm").find("#gutClass_other").hide();
			// V.有徵提評等較佳之連帶保證人，連帶保證人下的欄位都先隱藏
			$("#l120S24ADetailForm").find(".hasGuar_Y").hide();

			// 因為codetype好像沒有讓選項是value - key的方式，只好抄別隻的塞法，要在setData前做好
			var obj = CommonAPI.loadCombos(["CountryCode", "Common_Currcy"]);
			//國別
			$(".country_s24a").setItems({
				item: obj.CountryCode,
				format: "{value} - {key}"
			});
			//幣別
			$(".money_s24a").setItems({
				item: obj.Common_Currcy,
				format: "{value} - {key}"
			});
			
			// 從js塞，因為key是數字的情況如果用combotype設定，整個順序會被打亂
			var itema = API.loadOrderCombosAsList("gutClass_s24a")["gutClass_s24a"];
			$("#gutClass_s24a").setItems({ size: "1", item: itema, clear: true});
		},
		triggerSelect : function(){
			$("#l120S24ADetailForm").find('#bowrrowerClass_s24a').trigger('change');
			// 讓是否有ccf、ccf值、現請額度都放好後，才進行change再重算一次$calCcfAmt_s24a
			$("#l120S24ADetailForm").find('#ccf_s24a').trigger('change');
			
			// 為了在所有值都寫入後去觸發area2ShowHide()
			// 雖然最早的setData的時候會不斷的觸發area2ShowHide()
			// 但是那個時候的各欄位的值都有點不太齊全
			$("#l120S24ADetailForm").find('#LTVClass_s24a_N').trigger('change');
			
			$("#l120S24ADetailForm").find('#gutClass_s24a').trigger('change');
		},
		setHTMLData : function(data){
			// 畫面顯示的部分，會用到data裡來判斷
			// 連帶保證人風險權數旁邊的"計算"是否要出現，算是補救用的button
			if(data.l120s24a_show_guarrw_cal && data.l120s24a_show_guarrw_cal == 'Y'){
				$("#l120S24ADetailForm").find("#guarRW_s24a_cal_div").show();
			}

			if(data.lms_rw_show_special_branch_div){
				if(jQuery.inArray(userInfo.ssoUnitNo, data.lms_rw_show_special_branch_div) !== -1){
					$("#l120S24ADetailForm").find("#special_branch_s24a_div").show();
				}	
			}
			
			// 補上畫面幾處要塞幣別的地方
			if(data.currentApplyCurr_s24a){
				$("#l120S24ADetailForm").find('.currentApplyCurr_s24a_unit').val(data.currentApplyCurr_s24a);
				
				// 如果原幣別不為TWD則要多出現折合台幣的tr  還需要嗎??
				var currentApplyAmt_TWDArea = $("#l120S24ADetailForm").find('#currentApplyAmt_TWDArea');
				if(data.currentApplyCurr_s24a != 'TWD'){
					currentApplyAmt_TWDArea.show();
				}else{
					currentApplyAmt_TWDArea.hide();
				}
			}
			
			// 這兩個說明欄位特別要用.html來塞，因為裡面有斷行的一些排版 
			if(data.l120s24a_bowrrowerClassTop){
				$("#l120s24a_bowrrowerClassTop").html(DOMPurify.sanitize(data.l120s24a_bowrrowerClassTop));
			}
			if(data.l120s24a_bowrrowerClassBottom){
				$("#l120s24a_bowrrowerClassBottom").html(DOMPurify.sanitize(data.l120s24a_bowrrowerClassBottom));
			}
			
			// 補資料的部分，怕有些setData完之後，因為trigger change事件而導致畫面欄位被清掉
			if(data.ccfAmt_s24a){
				$("#l120S24ADetailForm").find('#ccfAmt_s24a').val(data.ccfAmt_s24a);
			}
			// .clear1會清的欄位
			if(data.beforeDeductRW_s24a){
				$("#l120S24ADetailForm").find('#beforeDeductRW_s24a').val(data.beforeDeductRW_s24a);
			}
			// .clear2會清的欄位
			if(data.gutDeptName_s24a){
				$("#l120S24ADetailForm").find("#gutDeptName_s24a").val(data.gutDeptName_s24a);
			}
			if(data.gutDeptRW_s24a){
				$("#l120S24ADetailForm").find("#gutDeptRW_s24a").val(data.gutDeptRW_s24a);
			}
			// .clear3會清的欄位
			if(data.guarName_s24a){
				$("#l120S24ADetailForm").find("#guarName_s24a").val(data.guarName_s24a);
			}
			if(data.guarCrdGrade_s24a){
				$("#l120S24ADetailForm").find("#guarCrdGrade_s24a").val(data.guarCrdGrade_s24a);
			}
			if(data.guarCrdText_s24a){
				$("#l120S24ADetailForm").find("#guarCrdText_s24a").html(data.guarCrdText_s24a);
			}
			if(data.guarRW_s24a){
				$("#l120S24ADetailForm").find("#guarRW_s24a").val(data.guarRW_s24a);
			}
			
			// .clear0 .clear1、.clear2、.clear3會清的欄位-計算區塊的所有欄位
			if(data.calCurrentApplyAmt_s24a){
				$("#l120S24ADetailForm").find("#calCurrentApplyAmt_s24a").val(data.calCurrentApplyAmt_s24a);
			}
			if(data.calCcf_s24a){
				$("#l120S24ADetailForm").find("#calCcf_s24a").val(data.calCcf_s24a);
			}
			if(data.calCcfAmt_s24a){
				$("#l120S24ADetailForm").find("#calCcfAmt_s24a").val(data.calCcfAmt_s24a);
			}
			if(data.calBeforeDeductRW_s24a){
				$("#l120S24ADetailForm").find("#calBeforeDeductRW_s24a").val(data.calBeforeDeductRW_s24a);
			}
			if(data.calDisCollAmt_s24a){
				$("#l120S24ADetailForm").find("#calDisCollAmt_s24a").val(data.calDisCollAmt_s24a);
			}
			if(data.calDisCollExposureAmt_s24a){
				$("#l120S24ADetailForm").find("#calDisCollExposureAmt_s24a").val(data.calDisCollExposureAmt_s24a);
			}
			if(data.calGutDeptRW_s24a){
				$("#l120S24ADetailForm").find("#calGutDeptRW_s24a").val(data.calGutDeptRW_s24a);
			}
			if(data.calGutPercent_s24a){
				$("#l120S24ADetailForm").find("#calGutPercent_s24a").val(data.calGutPercent_s24a);
			}
			if(data.calHasGutPartAmt_s24a){
				$("#l120S24ADetailForm").find("#calHasGutPartAmt_s24a").val(data.calHasGutPartAmt_s24a);
			}
			if(data.calHasGutDeptRWA_s24a){
				$("#l120S24ADetailForm").find("#calHasGutDeptRWA_s24a").val(data.calHasGutDeptRWA_s24a);
			}
			if(data.calGuarRW_s24a){
				$("#l120S24ADetailForm").find("#calGuarRW_s24a").val(data.calGuarRW_s24a);
			}
			if(data.calNoGutPartAmt_s24a){
				$("#l120S24ADetailForm").find("#calNoGutPartAmt_s24a").val(data.calNoGutPartAmt_s24a);
			}
			if(data.calNoGutPartRW_s24a){
				$("#l120S24ADetailForm").find("#calNoGutPartRW_s24a").val(data.calNoGutPartRW_s24a);
			}
			if(data.calNoGutRWA_s24a){
				$("#l120S24ADetailForm").find("#calNoGutRWA_s24a").val(data.calNoGutRWA_s24a);
			}
			if(data.calDeductRWA_s24a){
				$("#l120S24ADetailForm").find("#calDeductRWA_s24a").val(data.calDeductRWA_s24a);
			}
			if(data.calDeductRW_s24a){
				$("#l120S24ADetailForm").find("#calDeductRW_s24a").val(data.calDeductRW_s24a);
			}
		},
		readOnlyL120s24aPage : function(data){
			if (checkReadonly_s24a() || thickboxOptions.readOnly) {
				// 畫面唯讀
		        $("#l120S24ADetailForm").readOnlyChilds(true);
		        $("#l120S24BDetailForm").readOnlyChilds(true);
		        // 按鈕隱藏
		        $("#l120S24ADetailForm").find("button:not(.forview)").hide();
		    }
		}
}
*/


// 事件綁定
// 匯率的引入，去飲最新的匯率資料，並更新DB的資料時間
$("#l120S24ADetailForm").find("#rateLoanToLoc_s24a_import").click(function(){
	// 有無缺漏欄位，完全由ajax送後端來判斷即可
	$.ajax({
		handler: 'lms1401s10formhandler',
		type: "POST",
		action : "importRateLoanToLoc_s24a",
		dataType: "json",
		data:{
			mainId: responseJSON.mainId,
			oid: responseJSON.openL120s24aOid
		}
	}).done(function(json) {
		$("#l120S24ADetailForm").find("#rateLoanToLoc_s24a").val(json.rateLoanToLoc_s24a);// 匯率
		$("#l120S24ADetailForm").find("#rateDT_s24a").val(json.rateDT_s24a);// 匯率資料日期
	});
});

// 借款人外部評等(簽報書引入) ->重新引進按鈕
$("#l120S24ADetailForm").find("#crdGrade_s24a_reImport").click(function(){
	$.ajax({
		handler: 'lms1401s10formhandler',
		type: "POST",
		action : "reImportCrdGrade_s24a",
		dataType: "json",
		data:{
			mainId: responseJSON.mainId,
			oid: responseJSON.openL120s24aOid
		}
	}).done(function(json) {
		// 借款人外部評等(簽報書引入)
		$("#l120S24ADetailForm").find("#crdText_s24a").html(DOMPurify.sanitize(json.crdText_s24a));
	});
});

// 借款人類別->說明按鈕，跳出說明thickbox明
$("#l120S24ADetailForm").find("#bowrrowerClass_s24a_explain").click(function(){
	$("#bowrrowerClassExplainDiv").thickbox({
		title: i18n.lms1401s10["L120S24A.bowrrowerClass"], //借款人類別 說明
		width: 700,
		height: 850,
		modal : true,
		align: "center",
		valign: "bottom",
		buttons:  API.createJSON([{
			// 離開
			key: i18n.lms1401s10["LMS1401S10Form01.close"],
			value: function(){
				$.thickbox.close();
			}
		}])
	});
});

// 借款人類別
$("#l120S24ADetailForm").find('#bowrrowerClass_s24a').change(function(){
	var bowrrowerClass_s24a_val = $(this).val(); 
	// 二、有不動產擔保品採LTV法【僅C借款人類別 =4、5、6需填寫，其餘可鎖死或直接隱藏】
	var $hasEstate_s24a = $("#l120S24ADetailForm").find("input[name='hasEstate_s24a']");
	if(bowrrowerClass_s24a_val == '1' || bowrrowerClass_s24a_val == '2' || bowrrowerClass_s24a_val == '3' || bowrrowerClass_s24a_val == '7'){
		// 透過把第二區塊的H.本額度有徵提不動產擔保品->選擇否並readOnly
		// 讓user一樣看得到第二區塊，但是下面選項都隱藏起來
		$("#l120S24ADetailForm").find("input[name='hasEstate_s24a']:radio[value='N']").click();// 要放在readOnly前!!
		$hasEstate_s24a.readOnly(true);
	}else{
		$hasEstate_s24a.readOnly(false);
	}
});

//特殊融資類別->說明按鈕，跳出說明thickbox明
$("#l120S24ADetailForm").find("#specialFinRiskType_s24a_explain").click(function(){
	$("#specialFinRiskTypeExplainDiv").thickbox({
		title: i18n.lms1401s10["L120S24A.specialFinRiskType"], //特殊融資類別 說明
		width: 700,
		height: 180,
		modal : true,
		align: "center",
		valign: "bottom",
		buttons:  API.createJSON([{
			// 離開
			key: i18n.lms1401s10["LMS1401S10Form01.close"],
			value: function(){
				$.thickbox.close();
			}
		}])
	});
});

// B.現請額度
$("#l120S24ADetailForm").find("#currentApplyAmt_s24a").change(function(){
	calCcfAmt();// 重新計算G.純表外之信用相當額
})

// E.本額度為純保證額度->如果此欄位選否，則將F.表外項目信用轉換係數(CCF) 切換select物件show/hide
$("#l120S24ADetailForm").find("[id='onlyGuar_s24a']").change(function(){
	var $ccf_s24a_yes = $("#l120S24ADetailForm").find(".ccf_s24a_yes");
	var $ccf_s24a_none = $("#l120S24ADetailForm").find(".ccf_s24a_none");
	
	var onlyGuar_s24a_val = $("#l120S24ADetailForm").find("input[name='onlyGuar_s24a']:radio:checked").val();
	if(onlyGuar_s24a_val && onlyGuar_s24a_val === 'N'){
		$ccf_s24a_yes.hide();
		$ccf_s24a_none.show();
	}else{
		$ccf_s24a_yes.show();
		$ccf_s24a_none.hide();
	}
	calCcfAmt();// 重新計算G.純表外之信用相當額
});

// F.表外項目信用轉換係數(CCF)
$("#l120S24ADetailForm").find("#ccf_s24a").change(function(){
	calCcfAmt();// 重新計算G.純表外之信用相當額
	$("#l120S24ADetailForm").find("#calCcf_s24a").val($(this).val());// 塞到計算的(2)CCF
})

// F.表外項目信用轉換係數(CCF)->說明按鈕，跳出說明thickbox明
$("#l120S24ADetailForm").find("#ccf_s24a_explain").click(function(){
	$("#CCFExplainDiv").thickbox({
		title: i18n.lms1401s10["L120S24A.CCFTitle"], //表外項目信用轉換係數 說明
		width: 650,
		height: 750,
		modal : true,
		align: "center",
		valign: "bottom",
		buttons:  API.createJSON([{
			// 離開
			key: i18n.lms1401s10["LMS1401S10Form01.close"],
			value: function(){
				$.thickbox.close();
			}
		}])
	});
});

// G.純表外之信用相當額  計算function
function calCcfAmt(){
	var onlyGuar_s24a_val = $("#l120S24ADetailForm").find("input[name='onlyGuar_s24a']:radio:checked").val();
	var currentApplyAmt_s24a = util.delComma($("#l120S24ADetailForm").find("#currentApplyAmt_s24a").val());
	var ccf_s24a_val = util.delComma($("#l120S24ADetailForm").find("#ccf_s24a").val());
	
	var $ccfAmt_s24a = $("#l120S24ADetailForm").find("#ccfAmt_s24a");// G.純表外之信用相當額
	
	// E=是，G=B*F；J=否，G=B
	if(!onlyGuar_s24a_val){
		// E.本額度為純保證額度還沒選擇時，先預設放入現請額度
		// $ccfAmt_s24a.val(util.addComma(currentApplyAmt_s24a));
		$ccfAmt_s24a.val('');
	}else if(onlyGuar_s24a_val && onlyGuar_s24a_val === 'N'){
		// 如果E.本額度為純保證額度為"否"
		// 則值為B.現請額度
		$ccfAmt_s24a.val(util.addComma(currentApplyAmt_s24a));
	}else{
		// 如果E.本額度為純保證額度為"是"
		// 則值為B.現請額度 * F.表外項目信用轉換係數(CCF)
		if(!ccf_s24a_val){
			// F.表外項目信用轉換係數(CCF)還沒選擇時，先預設放入現請額度
			$ccfAmt_s24a.val(util.addComma(currentApplyAmt_s24a));
		}else{
			$ccfAmt_s24a.val(util.addComma(roundDecimal(currentApplyAmt_s24a * ccf_s24a_val / 100, 2)));
		}
	}
	
	var $calCcfAmt_s24a = $("#l120S24ADetailForm").find("#calCcfAmt_s24a");// 塞到計算的(3)純表外之信用相當額 (1)*(2)
	// $calCcfAmt_s24a.val($ccfAmt_s24a.val());
}

//!!!!!!!!!!!
//.clear0為所有會影響較獨立的欄位，但會影響計算區塊總結果的欄位，只要有任一個地方異動，就要最下方計算區塊的值清掉
$("#l120S24ADetailForm").find(".clear0").change(function(){
	// 計算區塊-全清空
	$("#l120S24ADetailForm").find(".calField").val('');
});
//!!!!!!!!!!!

// H.本額度有徵提不動產擔保品
$("#l120S24ADetailForm").find("[id='hasEstate_s24a']").change(function(){
	area2ShowHide('hasEstate_s24a');
});

// I.央行管制暴險
$("#l120S24ADetailForm").find("[id='isCBControl_s24a']").change(function(){
	area2ShowHide('isCBControl_s24a');
});

// J.分類
$("#l120S24ADetailForm").find("#LTVClass_s24a_N").change(function(){
	area2ShowHide('LTVClass_s24a_N');
});

// 本額度之不動產擔保品為土地
$("#l120S24ADetailForm").find("[id='estateIsLand_s24a']").change(function(){
	area2ShowHide('estateIsLand_s24a');
});

// 是否為農地、林地
$("#l120S24ADetailForm").find("#isFarmWood_s24a").change(function(){
	area2ShowHide('isFarmWood_s24a');
});

// 區塊二的大連動
function area2ShowHide(element){
	// 大層的I.央行管制暴險【選否，I~O都免輸入】
	// 小層的H.本額度有徵提不動產擔保品【且免鍵J~N】
	// 兩個會有重疊的問題要一起判斷
	var isCBControl_s24a_val = $("#l120S24ADetailForm").find("input[name='isCBControl_s24a']:radio:checked").val();
	// 下拉選單-是
	var $LTVClass_s24a_Y = $("#l120S24ADetailForm").find("#LTVClass_s24a_Y");
	// 下拉選單-否
	var $LTVClass_s24a_N = $("#l120S24ADetailForm").find("#LTVClass_s24a_N");

	// 選否，I~O都免輸入
	var hasEstate_s24a_val = $("#l120S24ADetailForm").find("input[name='hasEstate_s24a']:radio:checked").val();
	if(hasEstate_s24a_val == 'N'){
		$("#l120S24ADetailForm").find(".hasEstate_Y").hide();
	}else if(hasEstate_s24a_val == 'Y'){
		$("#l120S24ADetailForm").find("#isCBControl_s24a_tr").show();
		$LTVClass_s24a_Y.hide();
		
		if(isCBControl_s24a_val == 'Y'){
			// show下拉選單-是  且免鍵J~N
			$LTVClass_s24a_Y.show();
			$LTVClass_s24a_N.hide();
			$("#l120S24ADetailForm").find(".isCBControl_N").hide();
		}else if(isCBControl_s24a_val == 'N'){
			$LTVClass_s24a_Y.hide();
			$LTVClass_s24a_N.show();
			$("#l120S24ADetailForm").find(".isCBControl_N").show();
			
			// 1.住宅、2.商用才需填寫  K.類別LTVType_s24a
			var LTVClass_s24a_N_val = $LTVClass_s24a_N.val();
			var $LTVType_s24a = $("#l120S24ADetailForm").find(".LTVType_s24a");
			var $estateIsLand_s24a_tr = $("#l120S24ADetailForm").find("#estateIsLand_s24a_tr");
			var $estateIsLand_Y = $("#l120S24ADetailForm").find(".estateIsLand_Y");
			var $isFarmWoodYNeedBuss = $("#l120S24ADetailForm").find("#isFarmWoodYNeedBuss");// 農林地需選商用的文字提示
			var $LTV_s24a_tr = $("#l120S24ADetailForm").find("#LTV_s24a_tr");
			
			if(LTVClass_s24a_N_val == '1' || LTVClass_s24a_N_val == '2'){
				$LTVType_s24a.show();
				$estateIsLand_s24a_tr.show();
				$LTV_s24a_tr.show();
				
				// 是否為土地
				var estateIsLand_s24a_val = $("#l120S24ADetailForm").find("input[name='estateIsLand_s24a']:radio:checked").val();
				// 是否為農地、林地
				var isFarmWood_s24a_val = $("#l120S24ADetailForm").find("#isFarmWood_s24a").val();
				// 土地Y, 農林Y->合格，要輸LTV
				// 土地Y, 農林N->非合格，不用輸LTV
				// 土地N->合格，要輸LTV
				// M.LTV 【L=否，本欄免輸入鎖死；L=是，需輸入數字，小數點兩位，0~1000】
				if(estateIsLand_s24a_val == 'Y'){
					$estateIsLand_Y.show();
					
					if(isFarmWood_s24a_val == 'Y'){
						$LTV_s24a_tr.show();
						// 2022.10.19 芝榮表示農地、林地屬合格商用不動產暴險，幫他自動選商用，儲存那邊也需要調整
						if(element == 'isFarmWood_s24a'){
							$LTVClass_s24a_N.val('2');// 固定選商用，他後續還要自己改就是計算會被擋
						}
						$isFarmWoodYNeedBuss.show();
					}else{
						$LTV_s24a_tr.hide();
						$isFarmWoodYNeedBuss.hide();
					}
				}else{
					$estateIsLand_Y.hide();
					$LTV_s24a_tr.show();
				}
			}else{
				$LTVType_s24a.hide();
				$estateIsLand_s24a_tr.hide();
				$estateIsLand_Y.hide();
				$LTV_s24a_tr.hide();
			}
		}
	}
	
	 
	// I.央行管制暴險=是，四、保證資訊無需填寫
	var $extractBetterGuar_s24a = $("#l120S24ADetailForm").find("input[name='extractBetterGuar_s24a']");
	var $hasGutClass_s24a = $("#l120S24ADetailForm").find("input[name='hasGutClass_s24a']");
	// 不屬LTV法，一樣可以敲保證資訊相關資料
	if(hasEstate_s24a_val == 'Y' && isCBControl_s24a_val == 'Y'){
		// 透過把第四區塊的R-1是否有下列類型保證、V.有徵提評等較佳之連帶保證人->選擇否並readOnly
		// 讓user一樣看得到第四區塊，但是下面選項都隱藏起來
		$("#l120S24ADetailForm").find("input[name='hasGutClass_s24a']:radio[value='N']").click();// 要放在readOnly前!!
		$hasGutClass_s24a.readOnly(true);
		$("#l120S24ADetailForm").find("input[name='extractBetterGuar_s24a']:radio[value='N']").click();// 要放在readOnly前!!
		$extractBetterGuar_s24a.readOnly(true);
		//$("#l120S24ADetailForm").find("#gutArea4Div").hide();
	}else{
		$hasGutClass_s24a.readOnly(false);
		$extractBetterGuar_s24a.readOnly(false);
		// $("#l120S24ADetailForm").find("#gutArea4Div").show();
	}
	
	//J-112-0562 調整ADC案件保證人/連帶保證人風險權數抵減規則
	var LTVClass_s24a_N_val = $LTVClass_s24a_N.val();
	if(LTVClass_s24a_N_val == '3' || LTVClass_s24a_N_val == '4'){
		var $hasGutClass_s24a = $("#l120S24ADetailForm").find("input[name='hasGutClass_s24a']");
		var $extractBetterGuar_s24a = $("#l120S24ADetailForm").find("input[name='extractBetterGuar_s24a']");
		$hasGutClass_s24a.readOnly(true);
		$("#l120S24ADetailForm").find("input[name='hasGutClass_s24a']:radio[value='N']").prop("checked", true);
		$hasGutClass_s24a.trigger("change");
		$extractBetterGuar_s24a.readOnly(true);
		$("#l120S24ADetailForm").find("input[name='extractBetterGuar_s24a']:radio[value='N']").prop("checked", true);
		$extractBetterGuar_s24a.trigger("change");
		//保證資訊相關資料清除
		$(".clearGutArea").val("");
	}
	
}

//I.央行管制暴險->說明按鈕，跳出說明thickbox明
$("#l120S24ADetailForm").find("#isCBControl_s24a_explain").click(function(){
	$("#isCBControlExplainDiv").thickbox({
		title: i18n.lms1401s10["LMS1401S10Form01.explain.title"],// 說明
		width: 800,
		height: 500,
		modal : true,
		align: "center",
		valign: "bottom",
		buttons:  API.createJSON([{
			// 離開
			key: i18n.lms1401s10["LMS1401S10Form01.close"],
			value: function(){
				$.thickbox.close();
			}
		}])
	});
});

// J.分類->說明按鈕，跳出說明thickbox明
$("#l120S24ADetailForm").find("#LTVClass_s24a_explain").click(function(){
	$("#LTVClassExplainDiv").thickbox({
		title: i18n.lms1401s10["LMS1401S10Form01.explain.title"],// 說明
		width: 400,
		height: 100,
		modal : true,
		align: "center",
		valign: "bottom",
		buttons:  API.createJSON([{
			// 離開
			key: i18n.lms1401s10["LMS1401S10Form01.close"],
			value: function(){
				$.thickbox.close();
			}
		}])
	});
});

// K.類別->說明按鈕，跳出說明thickbox明
$("#l120S24ADetailForm").find("#LTVType_s24a_explain").click(function(){
	$("#LTVTypeExplainDiv").thickbox({
		title: i18n.lms1401s10["LMS1401S10Form01.explain.title"],// 說明
		width: 600,
		height: 100,
		modal : true,
		align: "center",
		valign: "bottom",
		buttons:  API.createJSON([{
			// 離開
			key: i18n.lms1401s10["LMS1401S10Form01.close"],
			value: function(){
				$.thickbox.close();
			}
		}])
	});
});

// M.LTV->說明按鈕，跳出說明thickbox明
$("#l120S24ADetailForm").find("#LTV_s24a_explain").click(function(){
	$("#LTVExplainDiv").thickbox({
		title: i18n.lms1401s10["LMS1401S10Form01.explain.title"],// 說明
		width: 600,
		height: 200,
		modal : true,
		align: "center",
		valign: "bottom",
		buttons:  API.createJSON([{
			// 離開
			key: i18n.lms1401s10["LMS1401S10Form01.close"],
			value: function(){
				$.thickbox.close();
			}
		}])
	});
});

//!!!!!!!!!!!
// .clear1為所有會影響"本額度抵減前風險權數"的欄位，只要有任一個地方異動，就要把值清掉
$("#l120S24ADetailForm").find(".clear1").change(function(){
	$("#l120S24ADetailForm").find("#beforeDeductRW_s24a").val('');
	// 計算區塊-全清空
	$("#l120S24ADetailForm").find(".calField").val('');
});
//!!!!!!!!!!!

// O.風險權數  計算function
// 同時計算隱藏的借款人風險權數、交易對手無擔保暴險風險權數、 LTV法風險權數
$("#l120S24ADetailForm").find("#beforeDeductRW_s24a_cal").click(function(){
	var l120S24ADetailForm = $("#l120S24ADetailForm");
	// 有借款人類別即可計算借款人風險權數、交易對手無擔保暴險風險權數
	var bowrrowerClass_s24a_val = l120S24ADetailForm.find("#bowrrowerClass_s24a").val();// 借款人類別
	var hasEstate_s24a_val = l120S24ADetailForm.find("input[name='hasEstate_s24a']:radio:checked").val();

	// 為了計算LTV法風險權數
	//	依I~N對應附表三，取小數點兩位
	// 要傳以下欄位給後端->管制、分類、類別、LTV、是否合格、RW
	var isCBControl_s24a_val = $("#l120S24ADetailForm").find("input[name='isCBControl_s24a']:radio:checked").val();// I.央行管制暴險
	var LTVClass_s24a_Y_val = l120S24ADetailForm.find('#LTVClass_s24a_Y').val();// I.央行管制暴險->是 J.分類
	var LTVClass_s24a_N_val = l120S24ADetailForm.find('#LTVClass_s24a_N').val();// I.央行管制暴險->否 J.分類
	var LTVType_s24a_val = l120S24ADetailForm.find("input[name='LTVType_s24a']:radio:checked").val();// K.類別
	var estateIsLand_s24a_val = l120S24ADetailForm.find("input[name='estateIsLand_s24a']:radio:checked").val();// 本額度之不動產擔保品為土地
	var isFarmWood_s24a_val = l120S24ADetailForm.find("#isFarmWood_s24a").val();// L.該筆不動產為農地、林地
	var LTV_s24a_val = l120S24ADetailForm.find('#LTV_s24a').val();// M.LTV
	
	// 有無缺漏欄位，完全由ajax送後端來判斷即可
	$.ajax({
		handler: 'lms1405s10formhandler',
		type: "POST",
		action : "calBeforeDeductRW_s24a",
		dataType: "json",
		data:{
			mainId: responseJSON.mainId,
			oid: responseJSON.openL120s24aOid,
			bowrrowerClass_s24a: bowrrowerClass_s24a_val,
			hasEstate_s24a: hasEstate_s24a_val,
			isCBControl_s24a: isCBControl_s24a_val,
			LTVClass_s24a_Y: LTVClass_s24a_Y_val,
			LTVClass_s24a_N: LTVClass_s24a_N_val,
			LTVType_s24a: LTVType_s24a_val,
			estateIsLand_s24a: estateIsLand_s24a_val,
			isFarmWood_s24a: isFarmWood_s24a_val,
			LTV_s24a: LTV_s24a_val
		}
	}).done(function(json) {
		// D.借款人風險權數
		$("#l120S24ADetailForm").find("#bowrrowerRW_s24a").val(json.bowrrowerRW_s24a);

		// N.交易對手無擔保暴險風險權數
		$("#l120S24ADetailForm").find("#opponentRW_s24a").val(json.opponentRW_s24a);

		// N-1. LTV法風險權數   
		$("#l120S24ADetailForm").find("#LTVRW_s24a").val(json.LTVRW_s24a);

		// O.風險權數
		$("#l120S24ADetailForm").find("#beforeDeductRW_s24a").val(json.beforeDeductRW_s24a_cal);

		// 計算區塊-(4)抵減前風險權數
		// $("#l120S24ADetailForm").find("#calBeforeDeductRW_s24a").val(json.beforeDeductRW_s24a_cal);
	});
});

// P.本額度是否有合格金融擔保品
$("#l120S24ADetailForm").find("[id='hasQuaColl_s24a']").change(function(){
	var hasQuaColl_s24a_val = $(this).val();
	var $hasQuaColl_div = $("#l120S24ADetailForm").find("#hasQuaColl_div");
	if(hasQuaColl_s24a_val == 'N'){
		$hasQuaColl_div.hide();
	}else{
		$hasQuaColl_div.show();
	}
	
});

// 合格金融擔保品grid reload用method
function l120s24bGridReload(){
	$("#gridviewL120S24B").jqGrid("setGridParam", {
		postData: {
			refOid_s24b: responseJSON.openL120s24aOid
		}
	}).trigger("reloadGrid");
}

// 撈已存在的L120S24B詳細欄位資料
function loadS24BDoc(cellvalue, options, data){
	$.ajax({
		handler: 'lms1401s10formhandler',
		action: "queryL120s24b",
		data: {
			mainId: responseJSON.mainId,
			oid: data.oid
		}
	}).done(function(json) {
		openS24BDoc(data);
	});
}

// 新增L120S24B，直接開thickbox不用load資料
function addL120S24B(){
	openS24BDoc();
}

// 開啟thickbox，可能是新增or編輯
function openS24BDoc(data){
	$("#l120S24BDetailForm").reset();
	var thisL120s24bOid = ''; 
	if(data){
		$("#l120S24BDetailForm").setData(data);
		// trigger合格擔保品的種類，因為可能要show擔保品評等
		$("#l120S24BDetailForm").find("#quaColl_s24b").trigger('change');
		thisL120s24bOid = data.oid;
	}
	// thickboxOptions.customButton = [i18n.lmss07a06["L1205S07Page06.saveData"], i18n.lmss07a06["L1205S07Page06.close"]];
	$("#L120S24BDetailDiv").thickbox({
		title: i18n.lms1401s10["L120S24B.quaCollDetail"],// 合格金融擔保品
		width: 700,
		height: 700,
		modal : true,
		buttons:  API.createJSON([{
			// 儲存
			key: i18n.lms1401s10["LMS1401S10Form01.saveData"],
			value: function(){
				if(!$("#l120S24BDetailForm").valid()){
					return false;
				}
				$.ajax({
					handler: 'lms1401s10formhandler',
					type: "POST",
					action : "saveL120s24b_2025",
					dataType: "json",
					data:{
						mainId: responseJSON.mainId,
						oid: thisL120s24bOid,
						refOid_s24b: responseJSON.openL120s24aOid,// 存進L120S24B.refOid_s24b
						l120S24BDetailForm: JSON.stringify($("#l120S24BDetailForm").serializeData())
					}
				}).done(function(json) {
					thisL120s24bOid = json.oid;
					var l120S24BDetailForm = $("#l120S24BDetailForm");
					// 幣別對稱
					l120S24BDetailForm.find('#currSym_s24b').val(json.currSym_s24b);
					// 折扣後擔保品價值
					l120S24BDetailForm.find('#disCollAmt_s24b').val(json.disCollAmt_s24b);


					var l120S24ADetailForm = $("#l120S24ADetailForm");
					l120S24ADetailForm.find('#totalCollAmt_s24a').val(json.totalCollAmt_s24a);
					l120S24ADetailForm.find('#totalDisCollAmt_s24a').val(json.totalDisCollAmt_s24a);

					// 計算區塊-(5)合格擔保品抵減金額
					// l120S24ADetailForm.find("#calDisCollAmt_s24a").val(json.totalDisCollAmt_s24a);
					// trigger合格擔保品抵減金額的span觸發clear0清除下方計算區塊效果
					$("#l120S24ADetailForm").find('#totalDisCollAmt_s24a').trigger('change');

					API.showMessage(i18n.def['saveSuccess']);
					l120s24bGridReload();
				});
			}
		}, {
			// 離開
			key: i18n.lms1401s10["LMS1401S10Form01.close"],
			value: function(){
				$.thickbox.close();
			}
		}])
	});
}

// 刪除L120S24B
function deleteL120S24B(){
	var rows = $("#gridviewL120S24B").getGridParam('selarrrow');
	var data = [];
	
	if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
		return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	}
	
	// confirmDelete=是否確定刪除?
	CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
		if (b) {
			for (var i in rows) {
				data.push($("#gridviewL120S24B").getRowData(rows[i]).oid);
			}
			
			$.ajax({
				handler: 'lms1401s10formhandler',
				type: "POST",
				action : "deleteL120s24bs",
				data: {
					mainId: responseJSON.mainId,// 簽報書mainId
					refOid_s24b: responseJSON.openL120s24aOid,// L120S24A.OID
					oids: data//要刪除的L120S24B OIDs
				}
			}).done(function(json) {
				var l120S24ADetailForm = $("#l120S24ADetailForm");
				// 合格擔保品合計區塊
				l120S24ADetailForm.find('#totalCollAmt_s24a').val(json.totalCollAmt_s24a);
				l120S24ADetailForm.find('#totalDisCollAmt_s24a').val(json.totalDisCollAmt_s24a);

				// 計算區塊-(5)合格擔保品抵減金額
				// l120S24ADetailForm.find("#calDisCollAmt_s24a").val(json.totalCollAmt_s24a);
				// trigger合格擔保品抵減金額的span觸發clear0清除下方計算區塊效果
				$("#l120S24ADetailForm").find('#totalDisCollAmt_s24a').trigger('change');
				l120s24bGridReload();
			});
		}
	});
}

// 合格金融擔保品->擔保品評等change事件
// 開啟擔保品thickbox的時候也會要觸發change事件for顯示
$("#l120S24BDetailForm").find("#quaColl_s24b").change(function(){
	var quaColl_s24b_val = $(this).val(); 
	
	var $collCrdGrade_s24b_tr = $("#l120S24BDetailForm").find("#collCrdGrade_s24b_tr");
	// 若擔保品為國庫券、公債、金融債券、公司債券，要出現擔保品評等那一列，其餘擔保品則隱藏
	// 3:國庫券、4:公債、5:金融債券、6:公司債券
	if(quaColl_s24b_val == '3' || quaColl_s24b_val == '4' || quaColl_s24b_val == '5' || quaColl_s24b_val == '6'){
		$collCrdGrade_s24b_tr.show();
	}else{
		$collCrdGrade_s24b_tr.hide();
	}
});

// R-1是否有下列類型保證
// R-1保證類型的SELECT下拉選單
$("#l120S24ADetailForm").find("#gutClass_s24a, [id='hasGutClass_s24a']").change(function(){
	var hasGutClass_s24a_val = $("#l120S24ADetailForm").find("input[name='hasGutClass_s24a']:radio:checked").val();
	var $hasGut_Y = $("#l120S24ADetailForm").find(".hasGut_Y");
	
	// select下拉選單值
	var gutClass_s24a_val = $("#l120S24ADetailForm").find("#gutClass_s24a").val();
	// 其他的描述欄位
	var $gutClass_other = $("#l120S24ADetailForm").find("#gutClass_other");
	// R.保證機構國別那一列
	var $gutDeptCountry_s24a_tr = $("#l120S24ADetailForm").find("#gutDeptCountry_s24a_tr");
	// S.保證銀行代碼那兩列
	var $gutDeptID_s24a_tr = $("#l120S24ADetailForm").find(".gutDeptID_s24a_tr");
	// U.保證機構風險權數
	var $gutDeptRW_s24a = $("#l120S24ADetailForm").find("#gutDeptRW_s24a");
	// 保證機構風險權數的計算按紐
	var $gut_s24a_cal = $("#l120S24ADetailForm").find("#gut_s24a_cal");
	
	if(hasGutClass_s24a_val == 'Y'){
		$hasGut_Y.show();

		// 細微的保證類型判斷
		$gutClass_other.hide();
		$gutDeptCountry_s24a_tr.hide();
		$gutDeptID_s24a_tr.hide();
		$gutDeptRW_s24a.readOnly(true);
		$gut_s24a_cal.show();
		// 選擇選項8其他(如:高鐵三方保證等)，都是在有保證類型的情況下才做判斷
		// 出現其他的描述欄位，保證機構風險權數分行填入說明。
		// 計算按鈕隱藏，因為他要自己手key
		if(gutClass_s24a_val == '8'){
			$gutClass_other.show();
			$gutDeptRW_s24a.readOnly(false);
			$gut_s24a_cal.hide();
		}else if(gutClass_s24a_val == '5' || gutClass_s24a_val == '6'){
			$gutDeptCountry_s24a_tr.show();
		}else if(gutClass_s24a_val == '7'){
			$gutDeptID_s24a_tr.show();
		}
		// 細微的保證類型判斷		
	}else{
		$hasGut_Y.hide();
	}
});

//!!!!!!!!!!!
//.clear2為所有會影響"U.保證機構風險權數"的欄位，只要有任一個地方異動，就要把值清掉
$("#l120S24ADetailForm").find(".clear2").change(function(){
	$("#l120S24ADetailForm").find("#gutDeptName_s24a").val('');
	$("#l120S24ADetailForm").find("#gutDeptRW_s24a").val('');
	// 計算區塊-全清空
	$("#l120S24ADetailForm").find(".calField").val('');
});
//!!!!!!!!!!!

// U.保證機構風險權數 計算  計算function
$("#l120S24ADetailForm").find("#gut_s24a_cal").click(function(){
	var l120S24ADetailForm = $("#l120S24ADetailForm");

	var gutClass_s24a_val = l120S24ADetailForm.find("#gutClass_s24a").val();//R-1保證類型
	// var gutClassOtherDesc_s24a_val = l120S24ADetailForm.find("#gutClassOtherDesc_s24a").val();//R-1保證類型-其他說明
	var gutDeptCountry_s24a_val = l120S24ADetailForm.find("#gutDeptCountry_s24a").val();//R.保證機構國別
	var gutDeptID_s24a_val = l120S24ADetailForm.find("#gutDeptID_s24a").val();//S.保證銀行代碼
	// var gutPercent_s24_val = l120S24ADetailForm.find("#gutPercent_s24").val();//T.保證成數
	
	// 有無缺漏欄位，完全由ajax送後端來判斷即可
	$.ajax({
		handler: 'lms1401s10formhandler',
		type: "POST",
		action : "calGutDeptRW_s24a",
		dataType: "json",
		data:{
			mainId: responseJSON.mainId,
			oid: responseJSON.openL120s24aOid,
			gutClass_s24a: gutClass_s24a_val,
			gutDeptCountry_s24a: gutDeptCountry_s24a_val,
			gutDeptID_s24a: gutDeptID_s24a_val
		}
	}).done(function(json) {
		// 保證銀行名稱
		if(json.gutDeptName){
			$("#l120S24ADetailForm").find("#gutDeptName_s24a").val(json.gutDeptName);
		}
		// U.保證機構風險權數
		$("#l120S24ADetailForm").find("#gutDeptRW_s24a").val(json.gutDeptRW);

		// 計算區塊-(7)保證機構風險權數
		// $("#l120S24ADetailForm").find("#calGutDeptRW_s24a").val(json.gutDeptRW);
	});
});

// V.有徵提評等較佳之連帶保證人
$("#l120S24ADetailForm").find("[id='extractBetterGuar_s24a']").change(function(){
	var extractBetterGuar_s24a_val = $(this).val();
	var $hasGuar_Y = $("#l120S24ADetailForm").find(".hasGuar_Y");
	if(extractBetterGuar_s24a_val == 'Y'){
		$hasGuar_Y.show();
	}else{
		$hasGuar_Y.hide();
	}
});

//!!!!!!!!!!!
// .clear3為所有會影響"Y.連帶保證人風險權數"的欄位，只要有任一個地方異動，就要把值清掉
$("#l120S24ADetailForm").find(".clear3").change(function(){
	// 連帶保證人名稱
	$("#l120S24ADetailForm").find("#guarName_s24a").val('');
	// X.連帶保證人外部信評
	$("#l120S24ADetailForm").find("#guarCrdText_s24a").val('');
	// Y.連帶保證人風險權數
	$("#l120S24ADetailForm").find("#guarRW_s24a").val('');
	// 計算區塊-全清空
	$("#l120S24ADetailForm").find(".calField").val('');
});
//!!!!!!!!!!!

// 連帶保證人名稱->引入按鈕，會帶出連帶保證人名稱、X.連帶保證人外部信評
$("#l120S24ADetailForm").find("#guar_s24a_import").click(function(){
	var l120S24ADetailForm = $("#l120S24ADetailForm");

	var guarId_s24a_val = l120S24ADetailForm.find("#guarId_s24a").val();//W.連帶保證保證人ID
	
	// 有無缺漏欄位，完全由ajax送後端來判斷即可
	$.ajax({
		handler: 'lms1401s10formhandler',
		type: "POST",
		action : "findGuar_s24a",
		dataType: "json",
		data:{
			mainId: responseJSON.mainId,
			oid: responseJSON.openL120s24aOid,
			guarId_s24a: guarId_s24a_val
		}
	}).done(function(json) {
		// 連帶保證人名稱
		$("#l120S24ADetailForm").find("#guarName_s24a").val(json.guarName_s24a);
		// X.連帶保證人外部信評
		$("#l120S24ADetailForm").find("#guarCrdText_s24a").html(DOMPurify.sanitize(json.guarCrdText_s24a));
		// Y.連帶保證人風險權數
		$("#l120S24ADetailForm").find("#guarRW_s24a").val(json.guarRW_s24a);

		// 計算區塊-(11)連帶保證人風險權數
		// $("#l120S24ADetailForm").find("#calGuarRW_s24a").val(json.guarRW_s24a);
	});
});


//連帶保證人風險權數->計算按鈕，只帶出連保人風險權數+連帶保證人的外部信評文字
$("#l120S24ADetailForm").find("#guarRW_s24a_cal").click(function(){
	var l120S24ADetailForm = $("#l120S24ADetailForm");
	
	// 有無缺漏欄位，完全由ajax送後端來判斷即可
	$.ajax({
		handler: 'lms1401s10formhandler',
		type: "POST",
		action : "calGuarRW_s24a",
		dataType: "json",
		data:{
			oid: responseJSON.openL120s24aOid
		}
	}).done(function(json) {
		// X.連帶保證人外部信評
		$("#l120S24ADetailForm").find("#guarCrdText_s24a").html(DOMPurify.sanitize(json.guarCrdText_s24a));
		// Y.連帶保證人風險權數
		$("#l120S24ADetailForm").find("#guarRW_s24a").val(json.guarRW_s24a);
	});
});

// 五、抵減後風險權數 ->計算按鈕
$("#l120S24ADetailForm").find("#calDeductRW_s24a_cal").click(function(){
	$.ajax({
		handler: 'lms1401s10formhandler',
		type: "POST",
		action : "calDeductRW_s24a",
		dataType: "json",
		data:{
			mainId: responseJSON.mainId,
			oid: responseJSON.openL120s24aOid,
			l120S24ADetailForm: JSON.stringify($("#l120S24ADetailForm").serializeData())
		}
	}).done(function(json) {
		// 塞回計算區塊欄位，json內只有計算區塊的資料
		var l120S24ADetailForm = $("#l120S24ADetailForm");
		l120S24ADetailForm.find("#calCurrentApplyAmt_s24a").val(json.calCurrentApplyAmt_s24a);
		l120S24ADetailForm.find("#calCcf_s24a").val(json.calCcf_s24a);
		l120S24ADetailForm.find("#calCcfAmt_s24a").val(json.calCcfAmt_s24a);
		l120S24ADetailForm.find("#calBeforeDeductRW_s24a").val(json.calBeforeDeductRW_s24a);
		l120S24ADetailForm.find("#calDisCollAmt_s24a").val(json.calDisCollAmt_s24a);
		l120S24ADetailForm.find("#calDisCollExposureAmt_s24a").val(json.calDisCollExposureAmt_s24a);
		l120S24ADetailForm.find("#calGutDeptRW_s24a").val(json.calGutDeptRW_s24a);
		l120S24ADetailForm.find("#calGutPercent_s24a").val(json.calGutPercent_s24a);
		l120S24ADetailForm.find("#calHasGutPartAmt_s24a").val(json.calHasGutPartAmt_s24a);
		l120S24ADetailForm.find("#calHasGutDeptRWA_s24a").val(json.calHasGutDeptRWA_s24a);
		l120S24ADetailForm.find("#calGuarRW_s24a").val(json.calGuarRW_s24a);
		l120S24ADetailForm.find("#calNoGutPartAmt_s24a").val(json.calNoGutPartAmt_s24a);
		l120S24ADetailForm.find("#calNoGutPartRW_s24a").val(json.calNoGutPartRW_s24a);
		l120S24ADetailForm.find("#calNoGutRWA_s24a").val(json.calNoGutRWA_s24a);
		l120S24ADetailForm.find("#calDeductRWA_s24a").val(json.calDeductRWA_s24a);
		l120S24ADetailForm.find("#calDeductRW_s24a").val(json.calDeductRW_s24a);
	});
})

function roundDecimal(val, precision) {
  return Math.round(Math.round(val * Math.pow(10, (precision || 0) + 1)) / 10) / Math.pow(10, (precision || 0));
}

var initDfd;
pageJsInit(function() {
  $(function() {
		initDfd = initDfd || $.Deferred();
		initDfd.done(function(){
			drawL120S24BGrid();
			drawBowrrowerClassGrid();
		});
	})
});