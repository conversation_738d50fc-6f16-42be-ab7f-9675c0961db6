package com.mega.eloan.lms.rpt.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

import com.inet.report.ReportException;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.model.LMSBATCH;
import com.mega.eloan.lms.rpt.report.LMS9511R04RptService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * 產生授信已敘做PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms9511r04rptservice")
public class LMS9511R04RptServiceImpl extends AbstractCapService implements
		LMS9511R04RptService {
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS9511R04RptServiceImpl.class);

	@Resource
	BranchService branch;

	@Resource
	DocFileDao docFileDao;

	@Resource
	DocFileService fileService;

	public final static String YYYY_MM_DD2 = "yyyyMMdd";

	/*
	 * 產生PDF
	 */
	@Override
	public String generateReport(List<Map<String, Object>> data, int action,
			LMSBATCH batchtbl) throws IOException {
		OutputStream outputStream = null;
		OutputStream outputStreamToFile = null;
		Locale locale = null;
		String randomCode = IDGenerator.getRandomCode();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		int subLine = 7;
		Properties propEloanPage = null;
		boolean vaPrintResult = true;
		ByteArrayOutputStream baos = null;
		String fieldId = "rpt";
		String desc = null;

		try {
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			// zh_TW: 正體中文
			// zh_CN: 簡體中文
			// en: 英文
			locale = LMSUtil.getLocale();
			outputStream = null;
			switch (action) {

			case 5:
				outputStream = this.genCLSRPT((List<Map<String, Object>>) data,
						locale, action, randomCode,
						TWNDate.toAD(batchtbl.getBgnDate()),
						TWNDate.toAD(batchtbl.getEndDate()), user.getUnitNo());
				desc = batchtbl.getRptName();
				break;
			case 6:
				outputStream = this.genCLSRPT((List<Map<String, Object>>) data,
						locale, action, randomCode,
						TWNDate.toAD(batchtbl.getBgnDate()),
						TWNDate.toAD(batchtbl.getEndDate()), user.getUnitNo());
				fieldId = fieldId + "-"
						+ batchtbl.getRemarks().split(";")[0].split("=")[1];
				desc = batchtbl.getRemarks().split(";")[1].split("=")[1];
			}

			if (outputStream != null) {
				pdfNameMap.put(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()),
						subLine);
			} else {
				pdfNameMap.put(null, subLine);
			}
			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine, vaPrintResult);
			}
			baos = (ByteArrayOutputStream) (outputStream);
			DocFile file = new DocFile();
			file.setMainId(batchtbl.getMainId());
			file.setData(baos != null ? baos.toByteArray() : null);
			file.setFileDesc(desc);
			file.setCrYear(CapDate.getCurrentDate("yyyy"));
			file.setFieldId(fieldId);
			file.setSrcFileName(batchtbl.getRptName() + ".pdf");
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setBranchId(batchtbl.getBranch());
			file.setContentType("application/pdf");
			file.setSysId("LMS");
			fileService.save(file);
			file = docFileDao.find(file);
			return file.getOid();

		} catch (Exception e) {
			LOGGER.error("[getContent] Exception!!", e.getMessage());
		} finally {
			if (outputStreamToFile != null) {
				outputStreamToFile.close();
				outputStreamToFile = null;
			}
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	public OutputStream genCLSRPT(List<?> list, Locale locale, int action,
			String randomCode, String startDate, String endDate, String brno)
			throws FileNotFoundException, ReportException, IOException,
			Exception {
		OutputStream outputStream = null;
		ReportGenerator generator = null;
		/*
		 * Properties prop = MessageBundleScriptCreator
		 * .getComponentResource(LMS1405S02Panel.class);
		 */
		try {
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

			generator = new ReportGenerator("report/rpt/CLS180R"
					+ String.format("%02d", action) + "_" + locale.toString()
					+ ".rpt");
			switch (action) {

			case 5:
				titleRows = this.setCLS180R05DataTitleRows(titleRows,
						(List<Map<String, Object>>) list);
				rptVariableMap = this.setCLS180R05DataRptVariableMap(
						rptVariableMap, startDate, brno);
				break;
			case 6:
				titleRows.add(Util.setColumnMap());
				rptVariableMap = this.setCLS180R06DataRptVariableMap(
						rptVariableMap, (Map<String, Object>) list.get(0));
			}
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();

		} finally {

		}
		return outputStream;
	}

	/*
	 * private String formatBigDecimal(BigDecimal number) { if (number == null)
	 * { return ""; } else if ("0".equals(String.valueOf(number))) { return "";
	 * } else { return NumConverter.addComma(number); } }
	 */

	// done
	private Map<String, String> setCLS180R05DataRptVariableMap(
			Map<String, String> rptVariableMap, String startDate, String brno)
			throws CapException {
		try {
			rptVariableMap.put("CLS180R05.year", startDate.split("-")[0]);
			rptVariableMap.put("CLS180R05.month", startDate.split("-")[1]);
			rptVariableMap.put("CLS180R05.brno",
					brno + branch.getBranchName(brno));
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	private List<Map<String, String>> setCLS180R05DataTitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list)
			throws CapException {
		try {
			Map<String, String> mapInTitleRows = null;
			mapInTitleRows = Util.setColumnMap();
			if (!list.isEmpty()) {
				for (int i = 0; i < list.size(); i++) {
					Map<String, Object> pivot = list.get(i);
					mapInTitleRows = Util.setColumnMap();
					// 序號
					mapInTitleRows.put("ReportBean.column01",
							Util.trim(pivot.get("NUM")));
					// 統一編號
					mapInTitleRows.put("ReportBean.column02",
							Util.trim(pivot.get("CUSTID")));
					// 客戶姓名
					mapInTitleRows.put("ReportBean.column03",
							Util.trim(pivot.get("CUSTNAME")));
					// 扣分
					mapInTitleRows.put("ReportBean.column04",
							Util.trim(pivot.get("AFCOUNT")));

					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}

		return titleRows;
	}

	private Map<String, String> setCLS180R06DataRptVariableMap(
			Map<String, String> rptVariableMap, Map<String, Object> datas)
			throws CapException {
		try {
			String[] cols = { "CLS180R06.prodName", "CLS180R06.bgnDate",
					"CLS180R06.endDate", "CLS180R06.amtBase",
					"CLS180R06.approveNum", "CLS180R06.approveAmt",
					"CLS180R06.rejectNum", "CLS180R06.appropriationNum",
					"CLS180R06.appropriationAmt", "CLS180R06.pendingNum" };
			for (int i = 0; i < cols.length; i++) {
				rptVariableMap.put(cols[i], Util.trim(datas.get(cols[i])));
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	@Override
	public void save(GenericBean... entity) {

	}

	@Override
	public void delete(GenericBean... entity) {

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {

		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {

		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {

		return null;
	}

}
