<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="_lms7405s01panel">
        <script type="text/javascript">
       		loadScript('pagejs/lns/LMS1401S02Panel');
        </script>
            <div id="lms1405s02PanelBt" style="display:none">
                <button type="button" id="newCheckPageBt" class="noHideBt">
                    <span class="text-only"><th:block th:text="#{'btn.newCheckPage'}"><!-- 產生批覆表--></th:block></span>
                </button>
                <button type="button" id="countValue" class="noHideBt">
                    <span class="text-only"><th:block th:text="#{'btn.count'}">計算授信額度合計</th:block></span>
                </button>
                <button type="button" id="checkBt" class="noHideBt">
                    <span class="text-only"><th:block th:text="#{'btn.check'}"><!-- 批覆--></th:block></span>
                </button>
                <button type="button" id="printviewBt" class="noHideBt">
                    <span class="text-only"><th:block th:text="#{'btn.print'}"><!--設定列印順序--></th:block></span>
                </button>
                <button type="button" id="removeAll" class="noHideBt">
                    <span class="text-only"><th:block th:text="#{'btn.removeAll'}"><!--全部刪除--></th:block></span>
                </button>
                <!-- <button type="button" id="parentBt" class="noHideBt"><span class="text-only"><th:block th:text="#{'btn.parent'}">匯入母行法人代表提案意見</th:block></span></button>-->
            </div>
            <div id="checkBtBox"  width="100%" class="tb2" style="display:none">
                <table>
                    <tr>
                        <td>
                            <label>
                                <input name="checkBtRadio" type="radio" value="1" checked/>
                                <!--<th:block th:text="#{'btn.check1'}"><!-- 核定-></th:block>-->
								<th:block th:text="#{'btn.check7'}"><!-- 核定(同意所請)--></th:block>
                            </label>
                        </td>
                        <td>
                            <label>
                                <input name="checkBtRadio" type="radio" value="4" />
                                <!--<th:block th:text="#{'btn.check2'}"><!-- 婉卻-></th:block>-->
								<th:block th:text="#{'btn.check8'}"><!-- 緩議(不同意所請)--></th:block>
                            </label>
                        </td>
						<td>
                            <label>
                                <input name="checkBtRadio" type="radio" value="2" />
                                <th:block th:text="#{'btn.check2'}"><!-- 婉卻--></th:block>
                            </label>
                        </td>
                        <td>
                            <label>
                                <input name="checkBtRadio" type="radio" value="3" />
                                <th:block th:text="#{'btn.check3'}"><!-- 全部還原--></th:block>
                            </label>
                        </td>
                    </tr>
					<!--J-112-0426_05097_B1001 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。-->
					<tr id="showCheckBtEsg">
						<td>
                            <th:block th:text="#{'title.checkBtEsg'}">涉及ESG風險授信案件之審查註記</th:block>：
                        </td>
						<td colspan=3 > 
						    　
                            <select id="checkBtEsg" name="checkBtEsg" 　class="required"></select> 
							<br>
							<span class="text-red"><th:block th:text="#{'title.checkBtEsgMemo_1'}">title.checkBtEsgMemo_1=請注意!若額度明細表多筆，僅部分額度因涉及ESG風險有條件通過(含減額、限制性條件)或緩議或婉卻，請勿以整批作業。</th:block></span>
                        </td>
					</tr>	
                </table>
				
				
                <div id="beforeCheckGridview"></div>
            </div>
			
			<!--J-108-0316_05097_B1001 Web e-Loan修改總處營	業單位授權外簽報流程-->
			<div id ="showRandomCodeSbr"  style="display:none" >
				<span>
					<b class="text-blue">
						<br/>
						送核批號： <span id="randomCodeSbr"></span>
						<br/>
						<br/>
					</b>
				</span>
			</div>
				
			<span class="text-red"><th:block th:text="#{'L140M01a.message01'}"><!-- 請依額度明細表【性質】依下列順序來設定列印順序(由左到右代表列印順序由前到後)--></th:block>：
                <br/>
                【<th:block th:text="#{'L140M01a.message02'}"><!-- 報價、新做、增額、紓困、協議清償、減額、變更條件、續約、提前續約、展期、流用、取消、不變--></th:block>】
                <br/>
                <th:block th:text="#{'L140M01a.message72'}"><!--L140M01a.message72=檢核欄V為通過檢核且經過計算，O為 通過檢核 但尚未計算，X為尚未通過檢核--></th:block>
            </span>
            <div id="gridviewC_2"></div>
            <div id="opendocBox" style="display:none">
                <div id="loadPanel" openFlag="true"></div>
            </div><!-- 特殊登錄案件 -->
            <div id="special" class="content" style="display:none;">
            <!-- <div id="special" class="content"> -->
                <form id="L782M01AForm" name="L782M01AForm">
                    <fieldset>
                        <legend>
                            <strong><th:block th:text="#{'doc.baseInfo'}"><!-- 基本資訊--></th:block></strong>
                        </legend>
                        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td width="20%" class="hd1">
                                        <th:block th:text="#{'doc.branchName'}"><!-- 分行名稱 --></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input id ="branchId" name="branchId" style="display:none"/>
                                        <span id ="branchName"></span>
                                    </td>
                                    <td width="20%" class="hd1">
                                        <th:block th:text="#{'L782M01A.dispatchDate'}"><!-- 發文日期--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="30%">
                                        <input type='text' id='dispatchDate' name='dispatchDate' class='date required' size='8' />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <th:block th:text="#{'L782M01A.custName'}"><!-- 客戶名稱--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <span id="specialCustId"></span><th:block th:text="#{'L782M01A.dupNo'}"><!-- 重覆序號--></th:block>：<span id="specialDupNo"></span>
                                        <br/>
                                        (<span class="color-red">DBU</span>)<span id="specialCustName"></span>
                                    </td>
                                    <td class="hd1">
                                        <th:block th:text="#{'L782M01A.loanTP'}"><!-- 科目--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td valign="middle">
                                        <select id ="SplieloanTP" name="SplieloanTP" class="required"></select>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <th:block th:text="#{'L782M01A.applyAmt'}"><!-- 額度--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td width="40%">
                                        <select id="applyCurr" name="applyCurr" class="money required"></select>
                                        <!-- 幣別 -->
                                        <input type='text' id='applyAmt' name='applyAmt' size="18" maxlength="22" integer="13" fraction="2" class="numeric required"/>
                                    </td>
                                    <td class="hd1">
                                        <th:block th:text="#{'L782M01A.caseType'}"><!-- 歸　　類--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <!--<select id="caseType" name="caseType" combokey="lms1405m01_SpecialCaseType" />-->
                                        <select id="caseType" name="caseType" class="required"></select>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <th:block th:text="#{'L782M01A.inteRate'}"><!-- 利費率/其他--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td colspan="3">
                                        <textarea id="inteRate" name="inteRate" cols="60" maxlength="900" maxlengthC="300"></textarea>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <th:block th:text="#{'L782M01A.disp1'}"><!-- 備註說明--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td colspan="3">
                                        <textarea id="disp1" name="disp1" cols="60" maxlength="900" maxlengthC="300"></textarea>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="hd1">
                                        <th:block th:text="#{'L140M01a.creator'}"><!-- 建立人員--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <span id="SplieCreator"></span>
                                    </td>
                                    <td class="hd1">
                                        <th:block th:text="#{'L140M01a.updater'}"><!-- 最後異動者--></th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <span id="SplieUpdater"></span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </fieldset>
                </form>
            </div><!--close 特殊登錄案件 -->
			
			<!--計算總額 調整 --舊的已作廢 -->
			<!--
	            <div id="countEditBox" style="display:none">
	                
	                <form id="countEditForm" name="countEditForm">
	                    <table class="tb2">
	                        <thead id="countEditTitle">
							</thead>
	                        <tbody id="countEditBody">
	                        </tbody>
	                    </table>
	                </form>
	            </div>
			-->
			<!--J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核--> 
			<div id="countEditBox" style="display:none">
                <!--計算總額 調整 -->
                <form id="countEditForm" name="countEditForm">
                	<!--J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核-->
                	<div id="suggestModeTextDiv"></div>
					<br>
					<!--J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核-->
                	<div class="tabs">
						<ul>
							<li>
								<a href="#tab-1_1"><b>1.<th:block th:text="#{'L140M01a.saveCount_2'}">計算授信額度合計</th:block></b></a>
							</li>
							<li id="tab2" style="float:left;display:none" >
								<a href="#tab-1_2"><b>2.<th:block th:text="#{'L140M01a.saveCount_3'}">計算授信授權額度</th:block></b></a>
							</li>
							<li id="tab3" style="float:left;display:none"  >
								<a href="#tab-1_3"><b>3.<th:block th:text="#{'L140M01a.saveCount_4'}">計算全案授信授權額度</th:block></b></a>
							</li>
						</ul>
					
					    <div class="tabCtx-warp">
					    	<div id="tab-1_1"> 
			                	<!--J-111-0461_05097_B1006 授信額度合計新增單獨另計授權及各組LGD合計檢核-->
			                	<!--STEP 1 授信額度合計-->
								<fieldset>
									<legend>
			                          <b><th:block th:text="#{'L140M01a.saveCount_2'}">計算授信額度合計</th:block></b>
			                        </legend>
				                    <table class="tb2">
										<!--J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計-->
										<thead id="countEditTitle1">
										</thead>
				                        <tbody id="countEditBody1">
				                        </tbody>
				                    </table>
								</fieldset>
							</div>
							<div id="tab-1_2"> 
								<!--STEP 2-->
								<fieldset>
									<legend>
			                          <b><th:block th:text="#{'L140M01a.saveCount_3'}">計算授信授權額度</th:block></b>
			                        </legend>
									 <th:block th:text="#{'L140M01a.saveCount_5'}">計算授信授權額度</th:block>: &nbsp;&nbsp;<button type='button' id='printLgdDetail'><span class='text-only'>查詢</span></button>
									 <br>
								     <table class="tb2">
										<!--J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計-->
										<thead id="countEditTitle2">
										</thead>
				                        <tbody id="countEditBody2">
				                        </tbody>
				                    </table>
								 
									<!--STEP 3-->
									<table class="tb2">
										<!--J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計-->
										<thead id="countEditTitle3">
										</thead>
				                        <tbody id="countEditBody3">
				                        </tbody>
				                    </table>
								</fieldset>
							</div>
							<div id="tab-1_3"> 
								<!--STEP 4-->
								 <fieldset>
									<legend>
			                          <b><th:block th:text="#{'L140M01a.saveCount_4'}">計算全案授信授權額度</th:block></b>
			                        </legend>
									 <div id="showCountEditTitle4"  >	<!--  style="display:none" -->
										 <table class="tb2">
											<!--J-112-0037_05097_B1004 Web eloan企個金國內、外授信管理系統配合LGD逾越授權檢核修改額度明細表合計-->
											<thead id="countEditTitle4">
											</thead>
					                        <tbody id="countEditBody4">
					                        </tbody>
					                    </table>
									</div>
								</fieldset>
							</div>
							
						</div>	<!--<div class="tabCtx-warp">-->
					</div><!--<div class="tabs">-->		
					<br>
					<!--J-111-0461_05097_B1005 授信額度合計新增單獨另計授權及各組LGD合計檢核-->
					<!--
					          調整合計檢核:<br>
						1.調整後LGD各組合計不得大於原始計算之合計。<br>
						2.授信額度合計=LGD合計+單獨另計授權額度合計。<br>
						3.總授信授權額度合計=LGD合計+出口瑕疵額度合計。(本檢核適用於畫面上有顯示「總授信授權額度合計」欄位時)<br>
					-->
					
					<div>
						<p id="countEditMsg"></p>
					</div>
                </form>
			</div>
				
            <!--close 計算總額 調整 -->
			<div id="countEditBoxCurr" style="display:none">
                <!--計算總額 選擇幣別˙-->
                <select id="countEditBoxCurrSelect" name="countEditBoxCurrSelect" combokey="Common_Currcy"></select>
				<br/>
				<br/>				
				<div id="showSaveRateFg" style="display:none">
					<span><th:block th:text="#{'L140M01a.message139'}"><!---各額度幣別兌換合計計算幣別匯率：--></th:block></span>
					<br/>
					<br/>
					<span id="nowCurrRate"></span>
	                <br/>
					<br/>
                    <label>
                        <input type="checkbox" id="saveRateFg" name="saveRateFg" value="Y" class="nodisabled" />
                        <th:block th:text="#{'L140M01a.message138'}"><!--匯率是否寫入各借款人項下第一筆額度明細表(同一借款人有多幣別額度時)--></th:block>
                    </label>
					<br/>
					<br/>
					<span>
                     <th:block th:text="#{'L140M01a.message140'}"><!---可圈選上方利率內容後複製(Ctrl+C)與貼上(Ctrl+V)--></th:block>
                    </span>
                </div>
				
            </div>
            <!--close 計算總額 選哲幣別 -->
            <div id="printview" style="display:none;">
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="hd1">
                            <th:block th:text="#{'L140M01a.message15'}"><!-- 注意事項--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <span class="text-red">(1)<th:block th:text="#{'L140M01a.message16'}"><!-- 請將該簽報書所有的額度明細表登錄完畢後再執行此功能--></th:block>。</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="hd1">
                            <th:block th:text="#{'L140M01a.message17'}"><!--操作說明--></th:block>&nbsp;&nbsp;
                        </td>
                        <td>
                            <span class="text-red">(1)<th:block th:text="#{'L140M01a.message18'}"><!-- 設定各額度明細表之列印順序--></th:block>。</span>
                            <br/>
                            <span class="text-red">(2)<th:block th:text="#{'L140M01a.message19'}"><!--執行【寫回額度明細表】回寫所設定之列印順序至各額度明細表中--></th:block>。</span>
                            <br/>
                            <span class="text-red">(3)<th:block th:text="#{'L140M01a.message20'}"><!-- 回到簽報書執行列印--></th:block>。</span>
                        </td>
                    </tr>
                </table>
                <span class="text-red"><th:block th:text="#{'L140M01a.message01'}"><!-- 請依額度明細表【性質】依下列順序來設定列印順序(由左到右代表列印順序由前到後)--></th:block>：
                    <br/>
                    【<th:block th:text="#{'L140M01a.message02'}"><!-- 報價、新做、增額、紓困、協議清償、減額、變更條件、續約、提前續約、展期、流用、取消、不變--></th:block>】
                    <br/>
                </span>
                <div id="gridviewprint"></div>
            </div>
            <div id="rejectCauseBox" style="display:none">
                <!-- 婉卻原因 -->
                <form id="rejectCauseForm" name="rejectCauseForm">
                    <table width="100%" class="tb2">
                        <tr>
                            <td colspan="2">
                                <th:block th:text="#{'L140M01a.message51'}"><!--請選擇婉卻代碼及原因，若原因為其他，請填入理由。--></th:block>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" width="70%">
                                <th:block th:text="#{'L140M01a.cesRjtCause'}"><!--婉卻原因--></th:block>
                            </td>
                            <td>
                                <select id="cesRjtCauseEnt" name="cesRjtCauseEnt" combokey="RejtCode" class="nodisabled"></select>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" width="70%">
                                <th:block th:text="#{'L140M01a.cesRjtReason'}"><!--婉卻理由--></th:block>
                            </td>
                            <td>
                                <textarea id="cesRjtReasonEnt" name="cesRjtReasonEnt" class="nodisabled max txt_mult" maxlengthC="350" cols="40" rows="5"></textarea>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <!--close 婉卻原因 -->
            <div id="l140m01eAmtBox" style="display:none;">
                <table border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <span id="l140m01eAmtMsg"></span>
                            <br/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div id="l140m01eAmtGrid"></div>
                        </td>
                    </tr>
                </table>
            </div>
            <!--
			因為改成另外一個頁面所以註解掉
			<script type="text/javascript" src="pagejs/lns/LMS1401S02Panel02.js"></script>
            <script type="text/javascript" src="pagejs/lns/LMS1401S02Panel03.js"></script>
            <script type="text/javascript" src="pagejs/lns/LMS1401S02Panel04.js"></script>
            <script type="text/javascript" src="pagejs/lns/LMS1401S02Panel05.js"></script>
            <script type="text/javascript" src="pagejs/lns/LMS1401S02Panel06.js"></script>
			-->
        </th:block>
    </body>
</html>
