/*
 * LMS1401S09FormHandler.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S23A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 額度明細表 - RWA
 * </pre>
 * 
 * @since 2022/08/
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/08/,009301,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1401s09formhandler")
public class LMS1401S09FormHandler extends AbstractFormHandler {

	@Resource
	LMSService lmsService;

	@Resource
	LMS1201Service lms1201Service;

	@Resource
	LMS1401Service lms1401Service;

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importL120S23A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String[] oids = params.getStringArray("oids");
		List<L120S23A> l120s23aList = new ArrayList<L120S23A>();
		if (oids.length > 0) {
			for (String oid : oids) {
				L140M01A l140m01a = lms1401Service.findModelByOid(
						L140M01A.class, oid);
				if (l140m01a != null) {
					L120S23A l120s23a = new L120S23A();
					this.l140m01aToL120s23a(mainId, l140m01a, l120s23a, true);
					l120s23aList.add(l120s23a);
				}
			}
		}
		if (l120s23aList.size() > 0) {
			// 先清空總計欄位
			this.cleanTotCol(mainId, true);
			// insert
			lms1201Service.saveL120s23aList(l120s23aList);
			// 算rwa總計欄位
			this.calcRwaTwdTot(mainId, result);
		}

		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(),
		// UtilConstants.AJAX_RSP_MSG.執行成功));

		return result;
	}

	public void calcRwa(L120S23A l120s23a) {
		if (l120s23a != null) {
			BigDecimal rwa = null;
			if (l120s23a.getRwaApplyAmt() != null
					&& l120s23a.getRwaRItemD() != null) {
				// 抵減後風險權數 單位為 % ，要 / 100
				rwa = l120s23a
						.getRwaApplyAmt()
						.multiply(l120s23a.getRwaRItemD())
						.divide(new BigDecimal(100), 2,
								BigDecimal.ROUND_HALF_UP);
			}
			l120s23a.setRwa(rwa);
			BigDecimal rwaTwd = null;
			if (l120s23a.getRwa() != null && l120s23a.getToTwdRate() != null) {
				rwaTwd = l120s23a.getRwa().multiply(l120s23a.getToTwdRate())
						.setScale(5, BigDecimal.ROUND_HALF_UP);
			}
			l120s23a.setRwaTwd(rwaTwd);
		}
	}

	public void cleanTotCol(String mainId, boolean cleanRwa) {
		List<L120S23A> l120s23aList = lms1201Service
				.findL120s23aByMainId(mainId);
		if (l120s23aList != null && !l120s23aList.isEmpty()) {
			for (L120S23A l120s23a : l120s23aList) {
				if (cleanRwa) {
					l120s23a.setRwaTwdTot(null);
				}
				l120s23a.setWRorwaTwd(null);
				l120s23a.setRwaResult(null);
			}
			lms1201Service.saveL120s23aList(l120s23aList);
		}
	}

	public void calcRwaTwdTot(String mainId, CapAjaxFormResult result) {
		if (result == null) {
			result = new CapAjaxFormResult();
		}
		BigDecimal rwaTwdTot = BigDecimal.ZERO;
		List<L120S23A> l120s23aList = lms1201Service
				.findL120s23aByMainId(mainId);
		if (l120s23aList != null && !l120s23aList.isEmpty()) {
			// step 1. 加總
			for (L120S23A l120s23a : l120s23aList) {
				if (l120s23a != null && Util.isNotEmpty(l120s23a)) {
					BigDecimal rwaTwd = (l120s23a.getRwaTwd() == null ? BigDecimal.ZERO
							: l120s23a.getRwaTwd());
					rwaTwdTot = rwaTwdTot.add(rwaTwd);
				}
			}
			// 先試算所有額度合計之RWA是否有超過等值TWD5億元，若未超過就毋需再填寫收益率及FTP
			boolean needRorwa = false;
			// step 2. 更新欄位
			for (L120S23A l120s23a : l120s23aList) {
				l120s23a.setRwaTwdTot(rwaTwdTot);
				needRorwa = lmsService.chkNeedRorwa(l120s23a);
				if (!needRorwa) {
					// 清空 收益率及FTP 相關欄位
					// l120s23a.setIncomeRate(null);
					// l120s23a.setFtpRate(null);
					// l120s23a.setRateSpread(null);
					// l120s23a.setRorwa(null);
					l120s23a.setWRorwaTwd(null);
					l120s23a.setRwaResult("N");
				} else {
					l120s23a.setWRorwaTwd(null);
					l120s23a.setRwaResult("X");
				}
			}
			result.set("isNeedRorwa", needRorwa ? "Y" : "N");
			lms1201Service.saveL120s23aList(l120s23aList);
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteL120S23A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			lms1201Service.deleteL120s23aList(oids);
			// 先清空總計欄位
			this.cleanTotCol(mainId, true);
			// 算rwa總計欄位
			this.calcRwaTwdTot(mainId, result);
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120S23A(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString(EloanConstants.OID));

		L120S23A l120s23a = lms1201Service.findL120s23aByOid(oid);
		if (l120s23a == null) {
			l120s23a = new L120S23A();
		}
		result = DataParse.toResult(l120s23a, DataParse.Delete, new String[] {
				EloanConstants.OID, EloanConstants.MAIN_ID, "rwaCustId",
				"rwaDupNo", "rwaCntrNo", "rwaProPerty", "creator",
				"createTime", "updater", "updateTime" });
		result.set("oidL120S23A", CapString.trimNull(l120s23a.getOid()));
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult showRorwa(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		List<L120S23A> l120s23aList = lms1201Service
				.findL120s23aByMainId(mainId);
		result.set("isNeedRorwa", this.isNeedRorwa(l120s23aList) ? "Y" : "N");
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult showPanelLms140s09(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lmsService
				.findModelByMainId(L120M01A.class, mainId);
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		result.set("showPanelLms140s09",
				lmsService.showPanelLms140s09(l120m01a) ? "Y" : "N");
		return result;
	}

	// 先試算所有額度合計之RWA是否有超過等值TWD5億元，若未超過就毋需再填寫收益率及FTP
	public boolean isNeedRorwa(List<L120S23A> l120s23aList) {
		boolean isNeedRorwa = false;
		if (l120s23aList != null && !l120s23aList.isEmpty()
				&& l120s23aList.size() > 0) {
			L120S23A tmp = l120s23aList.get(0);
			if (tmp != null && tmp.getRwaTwdTot() != null) {
				if (lmsService.chkNeedRorwa(tmp)) {
					isNeedRorwa = true;
				}
			}
		}
		return isNeedRorwa;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL120S23A(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String oid = Util.trim(params.getString("oid"));
		String cellvalue = Util.nullToSpace(params
				.getString("cellvalue", "RWA"));
		String errMsg = "";
		if (Util.isNotEmpty(oid)) {
			L120S23A l120s23a = lms1201Service.findL120s23aByOid(oid);
			if (l120s23a != null) {
				if (Util.equals(cellvalue, "RWA")) {
					String currentApplyCurr = Util.nullToSpace(params
							.getString("rwaApplyCurr"));
					l120s23a.setRwaApplyCurr(currentApplyCurr);
					BigDecimal applyAmt = Util.parseBigDecimal(NumConverter
							.delCommaString(params.getString("rwaApplyAmt")));
					l120s23a.setRwaApplyAmt(applyAmt);
					BigDecimal toTwdRate = Util.parseBigDecimal(NumConverter
							.delCommaString(params.getString("toTwdRate")));
					l120s23a.setToTwdRate(toTwdRate);
					BigDecimal rItemD = Util.parseBigDecimal(NumConverter
							.delCommaString(params.getString("rwaRItemD")));
					l120s23a.setRwaRItemD(rItemD);
					// 先清空總計欄位
					this.cleanTotCol(l120s23a.getMainId(), true);
					// 計算RWA
					this.calcRwa(l120s23a);
					lms1201Service.save(l120s23a);
					// 算rwa總計欄位
					this.calcRwaTwdTot(l120s23a.getMainId(), result);
					// 因為 抵減後風險權數 影響 RORWA，若有值要重算
					if (lmsService.chkNeedRorwa(l120s23a)) {
						this.calcRorwa(l120s23a);
						lms1201Service.save(l120s23a);
						this.calcRorwaTwdTot(l120s23a.getMainId());
					}
				} else if (Util.equals(cellvalue, "RORWA")) {
					BigDecimal incomeRate = Util.parseBigDecimal(NumConverter
							.delCommaString(params.getString("incomeRate")));
					l120s23a.setIncomeRate(incomeRate);
					BigDecimal ftpRate = Util.parseBigDecimal(NumConverter
							.delCommaString(params.getString("ftpRate")));
					l120s23a.setFtpRate(ftpRate);
					BigDecimal rateSpread = null;
					if (incomeRate != null && ftpRate != null) {
						rateSpread = incomeRate.subtract(ftpRate).setScale(5,
								BigDecimal.ROUND_HALF_UP);
						l120s23a.setRateSpread(rateSpread);
					}
					l120s23a.setRateSpread(rateSpread);
					this.calcRorwa(l120s23a);
					/*
					 * BigDecimal rorwa = null; if(rateSpread != null &&
					 * l120s23a.getRItemD() != null){ if
					 * (l120s23a.getRItemD().compareTo(BigDecimal.ZERO) != 0) {
					 * // 因為DB欄位 rorwa 單位為 % ，這邊除完 rateSpread 跟 rItemD
					 * 兩個%互消抵銷，所以要 * 100 rorwa =
					 * rateSpread.divide(l120s23a.getRItemD(), 7,
					 * BigDecimal.ROUND_HALF_UP) .multiply(new BigDecimal(100));
					 * } } l120s23a.setRorwa(rorwa);
					 */
					lms1201Service.save(l120s23a);
					// 先清空總計欄位
					this.cleanTotCol(l120s23a.getMainId(), false);
					// 算rorwa總計欄位
					this.calcRorwaTwdTot(l120s23a.getMainId());
				}
			}
		}
		if (Util.isNotEmpty(errMsg)) {
			result.set("msg", errMsg);
		} else {
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
			// .getMainMessage(this.getComponent(),
			// UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		return result;
	}

	public void calcRorwa(L120S23A l120s23a) {
		if (l120s23a != null) {
			BigDecimal rorwa = null;
			if (l120s23a.getRateSpread() != null
					&& l120s23a.getRwaRItemD() != null) {
				if (l120s23a.getRwaRItemD().compareTo(BigDecimal.ZERO) != 0) {
					// 因為DB欄位 rorwa 單位為 % ，這邊除完 rateSpread 跟 rItemD 兩個%互消抵銷，所以要
					// * 100
					rorwa = l120s23a
							.getRateSpread()
							.divide(l120s23a.getRwaRItemD(), 7,
									BigDecimal.ROUND_HALF_UP)
							.multiply(new BigDecimal(100));
				}
			}
			l120s23a.setRorwa(rorwa);
		}
	}

	public void calcRorwaTwdTot(String mainId) {
		// 本次新做增額額度加權RWA授信報酬率 = ( 匯率*RWA_TWD*RORWA +... ) / 本次新做增額換算為新台幣RWA合計數
		BigDecimal rorwaTwdTot = BigDecimal.ZERO;
		List<L120S23A> l120s23aList = lms1201Service
				.findL120s23aByMainId(mainId);
		if (l120s23aList != null && !l120s23aList.isEmpty()) {
			BigDecimal rwaTwdTot = BigDecimal.ZERO; // 分母
			BigDecimal tot = BigDecimal.ZERO; // 分子
			L120S23A tmp = l120s23aList.get(0);
			if (tmp != null && tmp.getRwaTwdTot() != null) {
				rwaTwdTot = tmp.getRwaTwdTot();
			}
			if (rwaTwdTot.compareTo(BigDecimal.ZERO) == 0) {

			} else {
				// step 1. 加總分子
				for (L120S23A l120s23a : l120s23aList) {
					if (l120s23a != null && Util.isNotEmpty(l120s23a)) {
						BigDecimal toTwdRate = (l120s23a.getToTwdRate() == null ? BigDecimal.ZERO
								: l120s23a.getToTwdRate());
						BigDecimal rwa = (l120s23a.getRwa() == null ? BigDecimal.ZERO
								: l120s23a.getRwa());
						// rorwa 單位為 % ，要 / 100
						BigDecimal rorwa = (l120s23a.getRorwa() == null ? BigDecimal.ZERO
								: l120s23a.getRorwa());
						BigDecimal tmpTot = toTwdRate
								.multiply(rwa)
								.multiply(rorwa)
								.divide(new BigDecimal(100), 2,
										BigDecimal.ROUND_HALF_UP);
						tot = tot.add(tmpTot);
					}
				}

				if (tot.compareTo(BigDecimal.ZERO) == 0) {

				} else {
					// 因為DB欄位 rorwaTwdTot 單位為 % ，所以要 * 100
					rorwaTwdTot = tot.divide(rwaTwdTot, 7,
							BigDecimal.ROUND_HALF_UP).multiply(
							new BigDecimal(100));
				}
			}
			// step 2. 更新欄位
			for (L120S23A l120s23a : l120s23aList) {
				l120s23a.setWRorwaTwd(rorwaTwdTot);
				boolean isNeedRorwa = lmsService.chkNeedRorwa(l120s23a);
				if (isNeedRorwa) {
					boolean isNeedElf442 = lmsService.chkRwaStandard(l120s23a);
					if (isNeedElf442) {
						l120s23a.setRwaResult("Y");
					} else {
						l120s23a.setRwaResult("N");
					}
				} else {
					l120s23a.setRwaResult("N");
				}
			}
			lms1201Service.saveL120s23aList(l120s23aList);
		}
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL120s23aTot(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		DecimalFormat df = new DecimalFormat("###,###,###,###,###.#####");
		result.set("rwaTwdTot", "");
		result.set("wRorwaTwd", "");
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		List<L120S23A> l120s23aList = lms1201Service
				.findL120s23aByMainId(mainId);
		if (l120s23aList != null && !l120s23aList.isEmpty()) {
			L120S23A tmp = l120s23aList.get(0);
			if (tmp != null) {
				result.set(
						"rwaTwdTot",
						(tmp.getRwaTwdTot() == null ? "" : df.format(tmp
								.getRwaTwdTot().setScale(0,
										BigDecimal.ROUND_HALF_UP))));
				result.set(
						"wRorwaTwd",
						(tmp.getWRorwaTwd() == null ? "" : df.format(tmp
								.getWRorwaTwd())));
			}
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult calcAllRWA(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		List<L120S23A> l120s23aList = lms1201Service
				.findL120s23aByMainId(mainId);
		if (l120s23aList != null && !l120s23aList.isEmpty()) {
			for (L120S23A l120s23a : l120s23aList) {
				l120s23a.setRwaTwdTot(null);
				this.calcRwa(l120s23a);
			}
			lms1201Service.saveL120s23aList(l120s23aList);
			// 算rwa總計欄位
			this.calcRwaTwdTot(mainId, result);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult calcAllRORWA(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		List<L120S23A> l120s23aList = lms1201Service
				.findL120s23aByMainId(mainId);
		if (l120s23aList != null && !l120s23aList.isEmpty()) {
			for (L120S23A l120s23a : l120s23aList) {
				l120s23a.setWRorwaTwd(null);
				l120s23a.setRwaResult(null);
				this.calcRorwa(l120s23a);
			}
			lms1201Service.saveL120s23aList(l120s23aList);
			// 算rorwa總計欄位
			this.calcRorwaTwdTot(mainId);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult importRwaList(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lmsService
				.findModelByMainId(L120M01A.class, mainId);
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		result.set("needImportRwa", "N");
		if (lmsService.showPanelLms140s09(l120m01a)) {
			if (!(UtilConstants.unitType.授管處.equals(unit.getUnitType()) || UtilConstants.unitType.營運中心
					.equals(unit.getUnitType()))) {
				result.set("needImportRwa", "Y");
				List<L120S23A> delS23aList = new ArrayList<L120S23A>();
				List<L120S23A> addS23aList = new ArrayList<L120S23A>();
				List<L120S23A> updS23aList = new ArrayList<L120S23A>();
				// L120S23A為空 則 自動產生 抵減後風險權數要非0%
				List<L120S23A> l120s23aList = lms1201Service
						.findL120s23aByMainId(mainId);
				HashMap<String, String> s23aMap = new HashMap<String, String>();
				if (l120s23aList != null && !l120s23aList.isEmpty()) {
					for (L120S23A temp : l120s23aList) {
						if (Util.isNotEmpty(Util.trim(temp.getRwaCntrNo()))) {
							if (!s23aMap.containsKey(Util.nullToSpace(temp
									.getRwaCntrNo().toUpperCase()))) {
								s23aMap.put(Util.nullToSpace(temp
										.getRwaCntrNo().toUpperCase()), Util
										.nullToSpace(temp.getOid()));
							}
						}
					}
				}

				List<L140M01A> l140m01aList = lms1401Service
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (!l140m01aList.isEmpty() && l140m01aList != null) {
					for (L140M01A l140m01a : l140m01aList) {
						String cntrNo140 = Util.nullToSpace(l140m01a
								.getCntrNo().toUpperCase());
						if (lmsService.chkCntrNoNeedRwa(l140m01a)) {
							if (l140m01a.getRItemD() != null
									&& (UtilConstants.DEFAULT.是.equals(l140m01a
											.getChkYN()) || UtilConstants.DEFAULT.否
											.equals(l140m01a.getChkYN()))) {
								// 是=>已計算 否=>完成檢核
								if (s23aMap.containsKey(cntrNo140)) {
									// 已有L120S23A 更新相關欄位
									List<L120S23A> updList = lms1201Service
											.findL120s23aByMainIdCntrNo(mainId,
													cntrNo140);
									if (updList != null && !updList.isEmpty()) {
										for (L120S23A updTemp : updList) {
											this.l140m01aToL120s23a(mainId,
													l140m01a, updTemp, false);
											updS23aList.add(updTemp);
										}
									}
								} else {
									// 沒有L120S23A 新增一筆
									L120S23A l120s23a = new L120S23A();
									this.l140m01aToL120s23a(mainId, l140m01a,
											l120s23a, true);
									addS23aList.add(l120s23a);
								}
							}
						} else {
							// 不需要做RWA 但有L120S23A 要刪除
							if (s23aMap.containsKey(cntrNo140)) {
								List<L120S23A> delList = lms1201Service
										.findL120s23aByMainIdCntrNo(mainId,
												cntrNo140);
								if (delList != null && !delList.isEmpty()) {
									for (L120S23A delTemp : delList) {
										delS23aList.add(delTemp);
									}
								}
							}
						}
					}
				} else {
					// 沒有額度明細表 L120S23A要清空
					if (l120s23aList != null && !l120s23aList.isEmpty()) {
						delS23aList = l120s23aList;
					}
				}

				int count = 0;
				// 刪除不需要的
				if (delS23aList.size() > 0) {
					lms1201Service.deleteL120s23as(delS23aList);
					count = count + delS23aList.size();
				}

				// 新增
				if (addS23aList.size() > 0) {
					lms1201Service.saveL120s23aList(addS23aList);
					count = count + addS23aList.size();
				}

				// 更新
				if (updS23aList.size() > 0) {
					lms1201Service.saveL120s23aList(updS23aList);
					count = count + updS23aList.size();
				}

				if (count > 0) {
					// 先清空總計欄位
					this.cleanTotCol(mainId, true);
					// 算rwa總計欄位
					this.calcRwaTwdTot(mainId, result);
				}
			}
		}

		return result;
	}

	public void l140m01aToL120s23a(String mainId, L140M01A l140m01a,
			L120S23A l120s23a, boolean isNew) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		if (l120s23a == null) {
			l120s23a = new L120S23A();
		}

		if (l140m01a != null) {
			if (isNew) {
				l120s23a.setMainId(mainId);
			}
			l120s23a.setRwaCustId(Util.nullToSpace(l140m01a.getCustId()));
			l120s23a.setRwaDupNo(Util.nullToSpace(l140m01a.getDupNo()));
			if (isNew) {
				l120s23a.setRwaCntrNo(Util.nullToSpace(l140m01a.getCntrNo()));
			}
			l120s23a.setRwaProPerty(Util.nullToSpace(l140m01a.getProPerty()));
			String[] strArr = lmsService.getRwaApplyAmt(l140m01a);
			if (strArr.length >= 3) {
				l120s23a.setRwaApplyCurr(Util.nullToSpace(strArr[0]));
				l120s23a.setRwaApplyAmt(LMSUtil.toBigDecimal(Util
						.nullToSpace(strArr[1])));
				l120s23a.setToTwdRate(LMSUtil.toBigDecimal(Util
						.nullToSpace(strArr[2])));
			} else {
				l120s23a.setRwaApplyCurr(Util.nullToSpace(l140m01a
						.getCurrentApplyCurr()));
				l120s23a.setRwaApplyAmt(null);
				l120s23a.setToTwdRate(null);
			}
			// 2022/08/04 建霖 引進條件 => 抵減後風險權數為null當100
			l120s23a.setRwaRItemD((l140m01a.getRItemD() == null ? BigDecimal
					.valueOf(100) : l140m01a.getRItemD()));
			// 計算RWA
			this.calcRwa(l120s23a);
			if (isNew) {
				l120s23a.setCreator(user.getUserId());
				l120s23a.setCreateTime(CapDate.getCurrentTimestamp());
			}
		}
	}
}
