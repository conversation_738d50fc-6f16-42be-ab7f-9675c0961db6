/* 
 * L170M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 最近三次財務及業務資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L170M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L170M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 金額幣別 **/
	@Column(name = "CURR", length = 3, columnDefinition = "VARCHAR(3)")
	private String curr;

	/**
	 * 金額單位
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,0)(DECIMAL(12,0)<br/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000
	 */
	@Column(name = "UNIT", columnDefinition = "DECIMAL(12,0)")
	private BigDecimal unit;

	/** 期間(起) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "FROMDATE1", columnDefinition = "DATE")
	private Date fromDate1;

	/** 期間(迄) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE1", columnDefinition = "DATE")
	private Date endDate1;

	/** 營業收入 **/
	@Column(name = "AMT11", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt11;

	/** 營業利益 **/
	@Column(name = "AMT12", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt12;

	/** 稅前損益 **/
	@Column(name = "AMT13", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt13;

	/**
	 * 自訂比率項目名稱1
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：20.負債比率
	 */
	@Column(name = "RATIONO1", length = 2, columnDefinition = "CHAR(2)")
	private String ratioNo1;

	/**
	 * 自訂比率項目名稱2
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：11.流動比率
	 */
	@Column(name = "RATIONO2", length = 2, columnDefinition = "CHAR(2)")
	private String ratioNo2;

	/**
	 * 自訂比率項目名稱3
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：12.速動比率
	 */
	@Column(name = "RATIONO3", length = 2, columnDefinition = "CHAR(2)")
	private String ratioNo3;

	/**
	 * 自訂比率項目名稱4
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)預設：22.固定長期適合率
	 */
	@Column(name = "RATIONO4", length = 2, columnDefinition = "CHAR(2)")
	private String ratioNo4;

	/** 日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "RATEDATE1", columnDefinition = "DATE")
	private Date rateDate1;

	/**
	 * 自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE11", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate11;

	/**
	 * 自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE12", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate12;

	/**
	 * 自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE13", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate13;

	/**
	 * 自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE14", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate14;

	/** 期間(起) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "FROMDATE2", columnDefinition = "DATE")
	private Date fromDate2;

	/** 期間(迄) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE2", columnDefinition = "DATE")
	private Date endDate2;

	/** 營業收入 **/
	@Column(name = "AMT21", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt21;

	/**
	 * 營業利益
	 * <p/>
	 * 100/11/11修正錯字
	 */
	@Column(name = "AMT22", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt22;

	/** 稅前損益 **/
	@Column(name = "AMT23", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt23;

	/** 日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "RATEDATE2", columnDefinition = "DATE")
	private Date rateDate2;

	/**
	 * 自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE21", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate21;

	/**
	 * 自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE22", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate22;

	/**
	 * 自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE23", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate23;

	/**
	 * 自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE24", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate24;

	/** 期間(起) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "FROMDATE3", columnDefinition = "DATE")
	private Date fromDate3;

	/** 期間(迄) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE3", columnDefinition = "DATE")
	private Date endDate3;

	/** 營業收入 **/
	@Column(name = "AMT31", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt31;

	/** 營業利益 **/
	@Column(name = "AMT32", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt32;

	/** 稅前損益 **/
	@Column(name = "AMT33", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal amt33;

	/** 日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "RATEDATE3", columnDefinition = "DATE")
	private Date rateDate3;

	/**
	 * 自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE31", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate31;

	/**
	 * 自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE32", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate32;

	/**
	 * 自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE33", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate33;

	/**
	 * 自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	@Column(name = "RATE34", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rate34;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得金額幣別 **/
	public String getCurr() {
		return this.curr;
	}

	/** 設定金額幣別 **/
	public void setCurr(String value) {
		this.curr = value;
	}

	/**
	 * 取得金額單位
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,0)(DECIMAL(12,0)<br/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000
	 */
	public BigDecimal getUnit() {
		return this.unit;
	}

	/**
	 * 設定金額單位
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,0)(DECIMAL(12,0)<br/>
	 * 元：1<br/>
	 * 千元：1000<br/>
	 * 萬元：10000<br/>
	 * 百萬元：1000000
	 **/
	public void setUnit(BigDecimal value) {
		this.unit = value;
	}

	/** 取得期間(起) **/
	public Date getFromDate1() {
		return this.fromDate1;
	}

	/** 設定期間(起) **/
	public void setFromDate1(Date value) {
		this.fromDate1 = value;
	}

	/** 取得期間(迄) **/
	public Date getEndDate1() {
		return this.endDate1;
	}

	/** 設定期間(迄) **/
	public void setEndDate1(Date value) {
		this.endDate1 = value;
	}

	/** 取得營業收入 **/
	public BigDecimal getAmt11() {
		return this.amt11;
	}

	/** 設定營業收入 **/
	public void setAmt11(BigDecimal value) {
		this.amt11 = value;
	}

	/** 取得營業利益 **/
	public BigDecimal getAmt12() {
		return this.amt12;
	}

	/** 設定營業利益 **/
	public void setAmt12(BigDecimal value) {
		this.amt12 = value;
	}

	/** 取得稅前損益 **/
	public BigDecimal getAmt13() {
		return this.amt13;
	}

	/** 設定稅前損益 **/
	public void setAmt13(BigDecimal value) {
		this.amt13 = value;
	}

	/**
	 * 取得自訂比率項目名稱1
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：20.負債比率
	 */
	public String getRatioNo1() {
		return this.ratioNo1;
	}

	/**
	 * 設定自訂比率項目名稱1
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：20.負債比率
	 **/
	public void setRatioNo1(String value) {
		this.ratioNo1 = value;
	}

	/**
	 * 取得自訂比率項目名稱2
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：11.流動比率
	 */
	public String getRatioNo2() {
		return this.ratioNo2;
	}

	/**
	 * 設定自訂比率項目名稱2
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：11.流動比率
	 **/
	public void setRatioNo2(String value) {
		this.ratioNo2 = value;
	}

	/**
	 * 取得自訂比率項目名稱3
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：12.速動比率
	 */
	public String getRatioNo3() {
		return this.ratioNo3;
	}

	/**
	 * 設定自訂比率項目名稱3
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)<br/>
	 * 預設：12.速動比率
	 **/
	public void setRatioNo3(String value) {
		this.ratioNo3 = value;
	}

	/**
	 * 取得自訂比率項目名稱4
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)預設：22.固定長期適合率
	 */
	public String getRatioNo4() {
		return this.ratioNo4;
	}

	/**
	 * 設定自訂比率項目名稱4
	 * <p/>
	 * 100/10/27新增(代碼說明詳註1)預設：22.固定長期適合率
	 **/
	public void setRatioNo4(String value) {
		this.ratioNo4 = value;
	}

	/** 取得日期 **/
	public Date getRateDate1() {
		return this.rateDate1;
	}

	/** 設定日期 **/
	public void setRateDate1(Date value) {
		this.rateDate1 = value;
	}

	/**
	 * 取得自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate11() {
		return this.rate11;
	}

	/**
	 * 設定自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate11(BigDecimal value) {
		this.rate11 = value;
	}

	/**
	 * 取得自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate12() {
		return this.rate12;
	}

	/**
	 * 設定自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate12(BigDecimal value) {
		this.rate12 = value;
	}

	/**
	 * 取得自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate13() {
		return this.rate13;
	}

	/**
	 * 設定自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate13(BigDecimal value) {
		this.rate13 = value;
	}

	/**
	 * 取得自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate14() {
		return this.rate14;
	}

	/**
	 * 設定自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate14(BigDecimal value) {
		this.rate14 = value;
	}

	/** 取得期間(起) **/
	public Date getFromDate2() {
		return this.fromDate2;
	}

	/** 設定期間(起) **/
	public void setFromDate2(Date value) {
		this.fromDate2 = value;
	}

	/** 取得期間(迄) **/
	public Date getEndDate2() {
		return this.endDate2;
	}

	/** 設定期間(迄) **/
	public void setEndDate2(Date value) {
		this.endDate2 = value;
	}

	/** 取得營業收入 **/
	public BigDecimal getAmt21() {
		return this.amt21;
	}

	/** 設定營業收入 **/
	public void setAmt21(BigDecimal value) {
		this.amt21 = value;
	}

	/**
	 * 取得營業利益
	 * <p/>
	 * 100/11/11修正錯字
	 */
	public BigDecimal getAmt22() {
		return this.amt22;
	}

	/**
	 * 設定營業利益
	 * <p/>
	 * 100/11/11修正錯字
	 **/
	public void setAmt22(BigDecimal value) {
		this.amt22 = value;
	}

	/** 取得稅前損益 **/
	public BigDecimal getAmt23() {
		return this.amt23;
	}

	/** 設定稅前損益 **/
	public void setAmt23(BigDecimal value) {
		this.amt23 = value;
	}

	/** 取得日期 **/
	public Date getRateDate2() {
		return this.rateDate2;
	}

	/** 設定日期 **/
	public void setRateDate2(Date value) {
		this.rateDate2 = value;
	}

	/**
	 * 取得自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate21() {
		return this.rate21;
	}

	/**
	 * 設定自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate21(BigDecimal value) {
		this.rate21 = value;
	}

	/**
	 * 取得自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate22() {
		return this.rate22;
	}

	/**
	 * 設定自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate22(BigDecimal value) {
		this.rate22 = value;
	}

	/**
	 * 取得自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate23() {
		return this.rate23;
	}

	/**
	 * 設定自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate23(BigDecimal value) {
		this.rate23 = value;
	}

	/**
	 * 取得自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate24() {
		return this.rate24;
	}

	/**
	 * 設定自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate24(BigDecimal value) {
		this.rate24 = value;
	}

	/** 取得期間(起) **/
	public Date getFromDate3() {
		return this.fromDate3;
	}

	/** 設定期間(起) **/
	public void setFromDate3(Date value) {
		this.fromDate3 = value;
	}

	/** 取得期間(迄) **/
	public Date getEndDate3() {
		return this.endDate3;
	}

	/** 設定期間(迄) **/
	public void setEndDate3(Date value) {
		this.endDate3 = value;
	}

	/** 取得營業收入 **/
	public BigDecimal getAmt31() {
		return this.amt31;
	}

	/** 設定營業收入 **/
	public void setAmt31(BigDecimal value) {
		this.amt31 = value;
	}

	/** 取得營業利益 **/
	public BigDecimal getAmt32() {
		return this.amt32;
	}

	/** 設定營業利益 **/
	public void setAmt32(BigDecimal value) {
		this.amt32 = value;
	}

	/** 取得稅前損益 **/
	public BigDecimal getAmt33() {
		return this.amt33;
	}

	/** 設定稅前損益 **/
	public void setAmt33(BigDecimal value) {
		this.amt33 = value;
	}

	/** 取得日期 **/
	public Date getRateDate3() {
		return this.rateDate3;
	}

	/** 設定日期 **/
	public void setRateDate3(Date value) {
		this.rateDate3 = value;
	}

	/**
	 * 取得自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate31() {
		return this.rate31;
	}

	/**
	 * 設定自訂比率項目1
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate31(BigDecimal value) {
		this.rate31 = value;
	}

	/**
	 * 取得自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate32() {
		return this.rate32;
	}

	/**
	 * 設定自訂比率項目2
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate32(BigDecimal value) {
		this.rate32 = value;
	}

	/**
	 * 取得自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate33() {
		return this.rate33;
	}

	/**
	 * 設定自訂比率項目3
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate33(BigDecimal value) {
		this.rate33 = value;
	}

	/**
	 * 取得自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 */
	public BigDecimal getRate34() {
		return this.rate34;
	}

	/**
	 * 設定自訂比率項目4
	 * <p/>
	 * 100/10/27配合徵信調整<br/>
	 * DECIMAL(7,2)(DECIMAL(9,2)
	 **/
	public void setRate34(BigDecimal value) {
		this.rate34 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
