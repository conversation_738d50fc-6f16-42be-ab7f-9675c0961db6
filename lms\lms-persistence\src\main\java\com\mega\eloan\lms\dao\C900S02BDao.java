package com.mega.eloan.lms.dao;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.lms.model.C900S02B;

/** 消金掃描對象編號檔 **/
public interface C900S02BDao extends IGenericDao<C900S02B> {

	public C900S02B findByOid(String oid);
	/**
	 * @param brNo
	 * @param clientIP
	 * @param empNo
	 * @param sysType {CLS-消金}
	 * @param sysFunc {1-個金徵信, 6-動審表一般, 7-動審表整批, 8-動審表中鋼, 9-異常}
	 * @return
	 */
	public C900S02B getNextSeq(String brNo, String clientIP, String empNo, String sysType, String sysFunc) throws CapException;
	
	public void delete_old_C900S02B();
}