$(function(){
    //	$("#approveDateE").bind('change', function() {  
    //		if ($.trim($("#approveDateS").val()) != "" && 
    //			$.trim($("#approveDateE").val()) != "" &&
    //			$("#approveDateS").val() > $("#approveDateE").val() ) {
    //				CommonAPI.showErrorMessage(i18n.lms1205v01["l120v05.message02"]);				
    //		}		
    //	}); 
	
	
	
	if($('#docKind').val() == '2'){
		$('#showCaseLvl').show();
	}else{
		$('#showCaseLvl').hide();
		$('#caseLvl').val('');
	}
	
	
	// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
	$('#docKind').change(function(){
		if($(this).val() == "2"){
			$('#showCaseLvl').show();
		}else{
			$('#showCaseLvl').hide();
			$('#caseLvl').val('');
		}
		 
	});
	
	// J-109-0431_05097_B1001 Web e-Loan授信系統已核准受理案件篩選條件新增案件審核層級
	$('#caseLvl').change(function(){
		var docKind =$('#docKind').val();
		
		if(  docKind == "" || docKind == undefined || docKind == null ){	
			CommonAPI.showMessage("案件審核層級(Authority Type)不得空白");
			$('#caseLvl').val('');
			return false;
		}
	});
	
	
	
	var $filterForm = $("#filterForm");
	
	$filterForm.find("#fxFlag").click(function(){
        
	   if ($filterForm.find("#fxFlag").attr('checked')) {
	   	  //啟用進階查詢
		  $filterForm.find("#s91t1f2").show();
	   }else{
		  $filterForm.find("#s91t1f2").hide();
	   }
    })
	
    if($("#caseBrId").length>0){	    
		//授管處或營運中心
	    if (userInfo.unitType == "2" || userInfo.unitType == "4") {
	        $.ajax({
	            type: "POST",
	            handler: "codetypehandler",
	            data: {
	                formAction: "allBranchByUnitType"
	            },
	            success: function(responseData){
	                var json = {
	                    format: "{value} - {key}",
	                    item: responseData
	                };
	                var caseBrId = $("#caseBrId");
	                caseBrId.setItems(json);
	                
	            }
	        });
	    }else if ((userInfo.unitNo == "940" || userInfo.unitNo == "943") && txCode == "337005" ) {
	        $.ajax({
	            type: "POST",
	            handler: "codetypehandler",
				async: false ,
	            data: {
	                formAction: "branchByUnitTypeExceptHeadOffice"
	            },
	            success: function(responseData){
	                var json = {
	                    format: "{value} - {key}",
	                    item: responseData
	                };
	                
	                //$("#caseBrId").setItems(json);
	                var caseBrId = $("#caseBrId");
	                caseBrId.setItems(json);
	                
	            }
	        });
	    }else if ((userInfo.unitNo == "0A7") && txCode == "331005" ) {
	    	//J-112-0JJJ_05097_B1001 Web e-Loan日本地區分行簽報書新增管理行授權內案件權限及相關修改
//	    	[上午 09:56] 羅翊鴻(東京分行,科長)
//	    	因為目前架構下無法於東京分行登入情形下篩選 大阪分行授權外或管理行授權案件
//	    	請問這部分有可能可以加入篩選機制嗎
	    	
	        $.ajax({
	            type: "POST",
	            handler: "codetypehandler",
				async: false ,
	            data: {
	                formAction: "allBranchByCountryHead"
	            },
	            success: function(responseData){
	                var json = {
	                    format: "{value} - {key}",
	                    item: responseData
	                };
	                
	                //$("#caseBrId").setItems(json);
	                var caseBrId = $("#caseBrId");
	                caseBrId.setItems(json);
	                
	            }
	        });    
	    }else {
			$("#caseBrId").closest("tr").remove();
			/*
			var text = '{"'+userInfo.unitNo+'":"'+userInfo.unitCName+'"}';
			var strJSON = JSON.parse(text);
	        var json = {
	            format: "{value} - {key}",
	            item: strJSON
	        };
	        $("#caseBrid").setItems(json);
	        */
	    }
    }
    
});

/**
 * 回傳 yyyy-MM-dd
 * @param {Object} n_month
 */
function getBefore_N_MonthDate(n_month){
    var sysdate = CommonAPI.getToday().split("-");
    var tDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
    tDate.setMonth(tDate.getMonth() - n_month);
    return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
}
