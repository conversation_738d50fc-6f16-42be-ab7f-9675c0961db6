package com.mega.eloan.lms.lns.handler.form;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lns.pages.LMS2501M01Page;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.model.L250M01B;
import com.mega.eloan.lms.model.L250S04A;
import com.mega.eloan.lms.model.VLUSEDOC01;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lms2501m01formhandler")
@DomainClass(L250M01A.class)
public class LMS2501M01FormHandler extends AbstractFormHandler {

	@Resource
	BranchService branchService;

	@Resource
	LMS2501Service lms2501Service;

	@Resource
	LMS1201Service lms1201Service;

	@Resource
	DocCheckService docCheckService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMSService lmsService;

	@Resource
	LMS1601Service lms1601Service;

	Properties pop = MessageBundleScriptCreator
			.getComponentResource(LMS2501M01Page.class);

	public IResult saveDoc(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString("page"));

		result.add(saveDocByPage(page, params));

		return result;
	}

	protected IResult saveDocByPage(int page, PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L250M01A meta = null;
		// 處理 transaction
		meta = prepareData(page, params);

		// 因為畫面不切換，所以要將畫面需要的更新資料回傳。
		switch (page) {
		case 1:
			result.set("docStatus",
					getMessage("docStatus." + meta.getDocStatus()));
			break;
		case 2:
			break;

		default:
			break;

		}

		// common 要求塞值欄位
		result.set(EloanConstants.MAIN_OID, meta.getOid());
		result.set(EloanConstants.MAIN_ID, meta.getMainId());
		result.set(EloanConstants.MAIN_UID, meta.getUid());
		// result.set(EloanConstants.DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "Y");
		int page = Util.parseInt(params.getString("page"));
		result.add(tempSaveByPage(page, params));

		return result;
	}

	protected IResult tempSaveByPage(int page, PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		L250M01A meta = null;

		// 處理transaction
		meta = prepareData(page, params);

		result.set(EloanConstants.MAIN_OID, meta.getOid());
		result.set(EloanConstants.MAIN_ID, meta.getMainId());
		result.set(EloanConstants.MAIN_UID, meta.getUid());
		result.set(EloanConstants.DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());

		return result;
	}

	protected L250M01A prepareData(int page, PageParameters params) throws CapException {

		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class,
				params.getString(EloanConstants.MAIN_OID));
		JSONObject tabFormJson = null;
		switch (page) {
		case 1:
			tabFormJson = JSONObject.fromObject(params.getString("tabForm"));
			DataParse.toBean(tabFormJson, meta);
			meta = lms2501Service.updateMetaInfoTab(meta);
			break;
		case 2:
			String listData = params.getString("listData");
			JSONArray ja = JSONArray.fromObject(listData);
			meta = lms2501Service.updateCheckListTab(meta, ja);
			break;
		default:
			break;
		}

		return meta;
	}

	@SuppressWarnings("unchecked")
//	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)

	public IResult initForm(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);

		L250M01A meta = null;

		if ((mainOid == null || mainOid.length() == 0)
				&& (mainId == null || mainId.length() == 0)) {

			meta = new L250M01A();
			meta.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());

			// 開啟新案帶入起案的分行和目前文件狀態
			result.set(
					"docStatus",
					this.getMessage("docStatus."
							+ CreditDocStatusEnum.海外_編製中.getCode()));
			result.set("ownBrId", user.getUnitNo());
			result.set(
					"ownBrName",
					StrUtils.concat(" ",
							branchService.getBranchName(user.getUnitNo())));

		} else {
			meta = lms2501Service.findModelByOid(L250M01A.class, mainOid);
			int page = Util.parseInt(params.getString("page"));

			switch (page) {
			case 1:
				result = DataParse.toResult(meta);

				result.set("apprId", lmsService.getUserName(meta.getApprId()));
				result.set("managerId",
						lmsService.getUserName(meta.getManagerId()));
				result.set("reCheckId",
						lmsService.getUserName(meta.getReCheckId()));
				result.set("bossId", lmsService.getUserName(meta.getBossId()));

				List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
						.findListByMainId(L250M01B.class, meta.getMainId());

				if (CollectionUtils.isNotEmpty(l250m01bs)) {
					StringBuffer sb = new StringBuffer();
					if ("Y".equals(meta.getAllCanPay())) {

						sb.append("全部動用");

					} else {
						for (L250M01B l250m01b : l250m01bs) {
							String cntrNo = l250m01b.getCntrNo();
							sb.append(cntrNo + ",");
						}
						sb.delete(sb.length() - 1, sb.length());
					}

					result.set("cntrNos", sb.toString());
				}

				result.set(
						"ownBrId",
						StrUtils.concat(user.getUnitNo(), " ",
								branchService.getBranchName(user.getUnitNo())));
				result.set("docStatus",
						getMessage("docStatus." + meta.getDocStatus()));
				result.set("typCd", getMessage("typCd." + meta.getTypCd()));
				break;
			case 2:
				JSONArray listData = lms2501Service.getSavedList(meta);
				result.set("listData", listData.toString());
				break;
			default:
				break;
			}

			result.set("showTypCd", getMessage("typCd." + meta.getTypCd()));
			result.set("showCustId", StrUtils.concat(meta.getCustId(), " ",
					meta.getDupNo(), " ", meta.getCustName()));

		}

		// 文件唯讀判斷
		// result.set(EloanConstants.DOC_READONLY, CreditDocStatusEnum.海外_編製中
		// .getCode().equals(meta.getDocStatus()) ? false : true);

		// common 要求塞值欄位
		result.set(EloanConstants.MAIN_OID, meta.getOid());
		result.set(EloanConstants.MAIN_ID, meta.getMainId());
		result.set(EloanConstants.MAIN_UID, meta.getUid());
		// result.set(EloanConstants.DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult reNewMetaDoc(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A meta = null;

		if (StringUtils.isEmpty(oid)) {
			meta = new L250M01A();
			meta.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			meta.setRptId("LMS");
			meta.setOwnBrId(user.getUnitNo());
			meta.setMainId(IDGenerator.getUUID());
			meta.setApprId(user.getUserId());

			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			meta.setTxCode(txCode);
			meta.setDocURL(CapWebUtil.getDocUrl(LMS2501M01Page.class));

		} else {
			meta = lms2501Service.findModelByOid(L250M01A.class, oid);
		}

		String caseMainId = params.getString("caseMainId");
		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(caseMainId);
		meta.setCustId(l120m01a.getCustId());
		meta.setDupNo(l120m01a.getDupNo());
		meta.setCustName(l120m01a.getCustName());
		meta.setTypCd(l120m01a.getTypCd());
		meta.setCaseYear(l120m01a.getCaseYear());
		meta.setCaseDate(l120m01a.getCaseDate());
		meta.setCaseNo(l120m01a.getCaseNo());
		meta.setCaseSeq(l120m01a.getCaseSeq());
		meta.setRandomCode(IDGenerator.getRandomCode());
		meta.setSrcMainId(l120m01a.getMainId());

		String itemType = lmsService.checkL140M01AItemType(l120m01a);
		// String useBrId = user.getUnitNo();
		List<VLUSEDOC01> vusedoc01s = null;
		boolean cntrNoAll = params.getAsBoolean("cntrNoAll", false);

		// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
		List<String> brnoList = new ArrayList<String>();
		brnoList.add(user.getUnitNo());
		String[] useBrId = brnoList.toArray(new String[brnoList.size()]);

		if (cntrNoAll) {
			meta.setAllCanPay("Y");
			vusedoc01s = lms1601Service.getDoCntrNo(caseMainId, useBrId,
					itemType);
		} else {
			meta.setAllCanPay("N");
			String[] selectCntrNo = params.getStringArray("cntrNos");
			vusedoc01s = lms1601Service.getDoCntrNo(caseMainId, selectCntrNo,
					useBrId, itemType);
		}

		List<L250M01B> newL250m01bs = new ArrayList<L250M01B>();
		// 避免重複的額度序號
		HashMap<String, String> cntrNoMap = new HashMap<String, String>();
		for (VLUSEDOC01 vusedoc01 : vusedoc01s) {
			String cntrNo = Util.trim(vusedoc01.getUseCntrNo());
			// 需為該分行額度序號才可動用
			// if (!user.getUnitNo().equals(cntrNo.substring(0, 3))) {

			// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
			// 進出口小行可以幫大行作動審表
			if (!brnoList.contains(cntrNo.substring(0, 3))) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS2501M01Page.class);
				HashMap<String, String> param = new HashMap<String, String>();
				param.put("msg", pop.getProperty("l250m01a.message.08"));
				// l250m01a.message.08=不得動用額度序號非本行之額度！
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
			if (cntrNoMap.containsKey(cntrNo)) {
				continue;
			}

			cntrNoMap.put(cntrNo, "");

			L250M01B l250m01b = new L250M01B();
			l250m01b.setCntrNo(cntrNo);
			l250m01b.setMainId(meta.getMainId());
			l250m01b.setReMainId(vusedoc01.getMainId());
			l250m01b.setCreator(user.getUserId());
			l250m01b.setCreateTime(CapDate.getCurrentTimestamp());
			l250m01b.setUpdater(user.getUserId());
			l250m01b.setUpdateTime(CapDate.getCurrentTimestamp());
			newL250m01bs.add(l250m01b);

		}

		lms2501Service.save(meta, newL250m01bs);
		params.put(EloanConstants.MAIN_OID, meta.getOid());

		return initForm(params);

	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult genList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class, oid);
		lms2501Service.removeLmsCheckList(meta);
		int currentVersion = lms2501Service.getVersion("LMS");
		meta.setVersion(currentVersion);
		lms2501Service.save(meta);

		String list = lms2501Service.genLMSCheckList();
		result.set("checkList", list);

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult deleteMeta(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class, oid);
		Map<String, String> lockedUser = docCheckService.listLockedDocUser(meta
				.getMainId());

		if (lockedUser == null) {
			lms2501Service.deleteLmsMeta(meta);
		} else {
			String message = getPopMessage("EFD0055", lockedUser);
			result.set("deleteMessage", message);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult checkBeforSend(PageParameters params) throws CapException {
		Map<String, String> messageSet = new HashMap<String, String>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class, oid);

		int currentVersion = lms2501Service.getVersion("LMS");
		int metaVersion = meta.getVersion();

		if (currentVersion != metaVersion) {
			throw new CapMessageException("檢核表非最新項目，請重新引進", this.getClass());
		}

		List<L250S04A> l250s04as = lms2501Service.getL250s04as(meta);

		if (CollectionUtils.isEmpty(l250s04as)) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", "檢核表未填寫"), getClass());
		} else {
			for (L250S04A l250s04a : l250s04as) {
				String sub1Title = Util.trim(l250s04a.getSub1Title());
				String value = Util.trim(l250s04a.getValue());
				if ("".equals(value)) {
					messageSet.put("colName", "【檢核表】" + sub1Title);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", messageSet), getClass());
				}

				String sub1RejectVal = Util.trim(l250s04a.getSub1RejectVal());
				String[] rejectMatch = sub1RejectVal.split(",");

				if (ArrayUtils.contains(rejectMatch, value)) {

					// messageSet.put("colName", );
					throw new CapMessageException("【檢核表】" + sub1Title
							+ "欄位值勾選錯誤", getClass());
				}
			}
		}

		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));

		// J-108-0217_10702_B1001 檢核若為專案為08特定金錢信託受益權自行設質擔保授信時，需選取語音檔
		List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
				.findListByMainId(L250M01B.class, meta.getMainId());
		for (L250M01B l250m01b : l250m01bs) {
			String reMainId = l250m01b.getReMainId();
			L140M01A l140m01a = lms2501Service.findByMainId(reMainId);
			String custName = l140m01a.getCustName();
			String projClass = l140m01a.getProjClass();

			if (Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)
					&& Util.isEmpty(l250m01b.getIVRFlag())) {
				throw new CapMessageException(
						RespMsgHelper.getMessage("EFD0005", custName + pop.getProperty("cls3301v00.error.01")),
						getClass());
			}
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify + AuthType.Accept, CheckDocStatus = false)
	public IResult flowAction(PageParameters params) throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A mainModel = lms2501Service.findModelByOid(L250M01A.class, oid);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String account = Util.trim(params.getString("account"));
		if (!Util.isEmpty(account)) {
			// String manager = Util.trim(params.getString("manager"));
			mainModel.setBossId(account);
			// mainModel.setManagerId(manager);
		}

		if (!Util.isEmpty(mainModel)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {

					if (!"back".equals(params.getString("flowAction"))) {
						if (user.getUserId().equals(mainModel.getApprId())) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						}
					}
					if ("ok".equals(params.getString("flowAction"))) {
						mainModel.setApproveTime(CapDate.getCurrentTimestamp());
						mainModel.setApprover(user.getUserId());
						mainModel.setReCheckId(user.getUserId());
					}
				}
				lms2501Service.flowAction(mainModel.getOid(), mainModel,
						params.containsKey("flowAction"),
						params.getString("flowAction", ""));
			} catch (FlowMessageException t1) {
				logger.error(
						"[flowAction] lms2301Service.flowAction FlowException!!",
						t1);
				if (t1.getExtraMessage() == null
						|| t1.getExtraMessage().isEmpty()) {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
				} else {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage(), t1.getExtraMessage()),
							getClass());
				}
			} catch (Throwable t1) {
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}
		return new CapAjaxFormResult();
	}

	// J-108-0217_10702_B1001 模擬動審IVR語音系統查詢
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult addIVRList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		List<String> datas = Arrays.asList(params.getStringArray("rows"));
		if (!Util.isEmpty(datas) && datas.size() > 0) {
			lms2501Service.saveIVRFlag(
					params.getString(EloanConstants.MAIN_OID), datas);
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult deleteIVRList(PageParameters params)	throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString(EloanConstants.MAIN_OID);
		String datas = params.getString("record_FileName");
		String deleteCustId = params.getString("deleteCustId");

		lms2501Service.deleteIVRFlag(oid, deleteCustId, datas);

		return result;
	}
}
