var gridDfd = $.Deferred();
var inits = {
    ghandler: "lms1411gridhandler"
};
$(document).ready(function(){
    gridDfd.done(grid);
    if (viewstatus == "01O" || viewstatus == "05O") {
        openFilterBox();
    } else {
        gridDfd.resolve("queryL141m01a");
    }
    
    function grid(action, doLocalFirst){
       var theGrid =  $("#gridview").iGrid({
            handler: inits.ghandler,
            height: 350,
            width: 785,
            autowidth: false,
            sortname: 'createTime|caseBrId',
            sortorder: 'desc|asc',
            rowNum: 15,
            localFirst: true,
            postData: {
                formAction: action,
                docStatus: viewstatus,
                endDate: $("#endDate").val(),
                fromDate: $("#fromDate").val()
            },
            colModel: [{
                colHeader: i18n.lms1415m01['L141M01A.ownBrId'],// 聯行
                name: 'caseBrId',
                width: 100,
                align: "left",
                sortable: true,
                formatter: 'click',
                onclick: openDoc
            }, {
                colHeader: i18n.lms1415m01['L141M01A.caseDate'],// 聯行簽案日期
                name: 'caseDate',
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.lms1415m01['L141M01A.mainCustId'],// 主要借款人統編,
                name: 'custId',
                width: 70,
                sortable: true
            }, {
                colHeader: i18n.lms1415m01['L141M01A.mainCustName'],// "主要借款人",
                name: 'custName',
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.lms1415m01['L141M01A.caseNo'],// 案號,
                name: 'caseNo',
                width: 150,
                sortable: true,
                align: "left"
            }, {
                colHeader: i18n.lms1415m01['L141M01A.coAppraiser'],// 聯行經辦
                name: 'coAppraiser',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'docURL',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridview").getRowData(rowid);
                openDoc(null, null, data);
            }
        });
        if(!doLocalFirst){
            theGrid.trigger("reloadGrid");
        }

    }
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryL141m01a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    function dateObjtoStr( tDate){
    	return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
    }
    
    //篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        //初始化
        //$filterForm.reset();
        {//set default value
        	var sysdate = CommonAPI.getToday().split("-");
        	var endDate = new Date(sysdate[0], sysdate[1]-1, sysdate[2]);
        	var fromDate = new Date(sysdate[0] , sysdate[1]-1, sysdate[2]);
        	fromDate.setMonth(fromDate.getMonth() - 12);
        	 	
        	$("#fromDate").val(dateObjtoStr(fromDate));
        	$("#endDate").val(dateObjtoStr(endDate));
        }
        $("#filterBox").thickbox({
            //page.title=LMS1415M01 聯行額度明細表
            title: i18n.lms1415m01['page.title'],
            width: 600,
            height: 300,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$filterForm.valid()) {
                        return false;
                    }
                    
                    if ($.trim($("#endDate").val()) == "" || $.trim($("#fromDate").val()) == "") {
                        //請輸入日期
                        return CommonAPI.showErrorMessage(i18n.lms1415v01["L141M01A.message03"]);
                    }
                    
                    var end = $("#endDate").val().split("-");
                    var from = $("#fromDate").val().split("-");
                    var endData = new Date(end[0], end[1], end[2]);
                    var fromData = new Date(from[0], from[1], from[2]);
                    
                    if (fromData > endData) {
                    
                        //L160M01A.error11=起始日期不能大於結束日期
                        return CommonAPI.showErrorMessage(i18n.lms1415v01["L141M01A.message02"]);
                    }

                    //alert("gridDfd.isResolved():" + gridDfd.isResolved());
                    if (gridDfd.state() !== "resolved") {
                        gridDfd.resolve("queryL141m01a3", true);
                    } else {

                    }
                    $("#gridview").jqGrid("setGridParam", {
                    	postData:  $.extend(
                         $("#filterForm").serializeData()
                         ,{
                            formAction: "queryL141m01a3",
                            docStatus: viewstatus/* in #filterForm,
                            endDate: $("#endDate").val(),
                            fromDate: $("#fromDate").val(),
                            custId: $("#custId").val()*/
                         }
                        )
                        ,
                        search: true
                    }).trigger("reloadGrid");

                    $.thickbox.close();
                },
                "cancel": function(){
                    gridDfd.resolve("queryL141m01a");
                    $.thickbox.close();
                }
            }
        });
    }
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selrow');
        
        if (!rows) {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var oid = $("#gridview").getRowData(rows).oid;
                
                $.ajax({
                    handler: "lms1415m01formhandler",
                    data: {
                        formAction: "deleteL141m01a",
                        oid: oid
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            } else {
                return;
            }
        });
        
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    });
});
