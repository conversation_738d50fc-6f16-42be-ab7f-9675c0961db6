/* 
 * LMS1205S15Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 營運中心意見
 * </pre>
 * 
 * @since 2011/11/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/4,<PERSON>,new
 *          </ul>
 */
public class CLS1201S15Panel extends Panel {

	public CLS1201S15Panel(String id) {
		super(id);
	}

	public CLS1201S15Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
