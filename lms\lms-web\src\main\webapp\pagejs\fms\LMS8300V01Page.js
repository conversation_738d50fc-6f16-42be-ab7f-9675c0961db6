var pageAction = {
	handler : 'lms8300m01formhandler',
	ghandler : 'lms8300gridhandler',
	grid : null,
	build : function(obj) {
		var gridview_colModel = [ {
			colHeader : i18n.lms8300v01["L830M01B.maintainType"], // 維護種類
			align : "left",
			width : 100,
			sortable : true,
			name : 'maintainType',
			formatter : 'click',
			onclick : function(cellvalue, options, rowObject){
				pageAction.openDoc(rowObject);
			}
		}, {
			colHeader : i18n.lms8300v01["L830M01B.origAOid"], // 原帳戶管理員
			align : "left",
			width : 100,
			sortable : true,
			name : 'origAOid'
		},{
			colHeader : i18n.lms8300v01["L830M01B.newAOid"], // 新帳戶管理員
			align : "left",
			width : 100,
			sortable : true,
			name : 'newAOid'
		}, {
			colHeader : i18n.lms8300v01['L830M01B.creator'],// "操作經辦",
			name : 'updater',
			width : 80,
			sortable : true,
			align : "center"
		}, {
			colHeader : i18n.lms8300v01["L830M01B.createTime"], // 建立日期 / 查詢日期
			align : "left",
			width : 80, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'createTime',
			formatter : 'date',
			formatoptions : {
				srcformat : 'Y-m-d H:i:s',
				newformat : 'Y-m-d H:i'
			}
		}, {
			name : 'oid',
			hidden : true
		}, {
			name : 'mainId',
			hidden : true
		}, {
			name : 'docURL',
			hidden : true
		} ];

		pageAction.grid = $("#gridview").iGrid({
			handler : pageAction.ghandler,
			height : 350,
			width : 785,
			autowidth : false,
			action : "queryL830m01B",
			postData : {
				docStatus : viewstatus
			},
			rowNum : 15,
			sortname : "createTime",
			sortorder : "desc|desc",
			rownumbers : true,
			colModel : gridview_colModel,
			ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				var data = $("#gridview").getRowData(rowid);
				pageAction.openDoc(data);
			}
		});

		$("#buttonPanel").find("#btnDelete").click(function(){
			var data = pageAction.grid.getSingleData();
			if (data){
				MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
					if (action){
						$.ajax({
							handler : pageAction.handler,
							action : 'deleteL830m01b_a',
							data : data,
							success:function(responseData){
								pageAction.reloadGrid();
								MegaApi.showPopMessage(i18n.def["confirmTitle"],i18n.def["confirmDeleteSuccess"]);
							}
						});
					}
				});
			}
		}).end().find("#btnSingleMaintain").click(function(){ //單筆維護(新增)
			var data = {};
			data.isNew = true;
			data.mainId = "";
			data.docstatus = viewstatus;
			LMS8300M01.openSingleMaintain(data);	
		}).end().find("#btnBatchMaintain").click(function(){ //批次維護	(新增)				
			var data = {};
			data.isNew = true;
			data.mainId = "";
			data.docstatus = viewstatus;
			LMS8300M01.openBatchMaintain(data);		
		}).end().find("#btnView").click(function(){
			//單筆維護跟批次維護
			var data = pageAction.grid.getSingleData();
			pageAction.openDoc(data);
		});
	},
	/**
	 * 重整資料表
	 */
	reloadGrid : function(data) {
		if (data) {
			pageAction.grid.jqGrid("setGridParam", {
				postData : data,
				page : 1,
				search : true
			}).trigger("reloadGrid");
		} else {
			pageAction.grid.trigger('reloadGrid');
		}
	},
	/**
	 * 開啟明細
	 */
	openDoc : function(data) {
		//開文件
		if(data){
			data.readonly = false;
			LMS8300M01.viewData(data);
		}
	}
}
$(document).ready(function(){
	$('#ClsCustInfo').load(webroot+'/app/fms/lms8300m01', function(){
		LMS8300M01.setEmp();
    });
});

$(function() {
	var obj;//預留
	pageAction.build(obj);
});
