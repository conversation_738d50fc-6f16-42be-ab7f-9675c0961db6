/* 
 * CLS1131S01XPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.beans.factory.annotation.Autowired;

import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1220Service;

/**
 * 個金徵信作業>系統初評
 */
public class CLS1131S02CPanel extends Panel {

	private static final long serialVersionUID = 1L;

	@Autowired
	CLSService clsService;

	@Autowired
	CLS1220Service cls122Service;

	public CLS1131S02CPanel(String id) {
		super(id);
	}

	/**
	 * @param id
	 */
	public CLS1131S02CPanel(String id, String mainId) {
		super(id);
//		JSONObject inJs = new JSONObject();
//		JSONObject outJs = new JSONObject();
//		L120S19A l120s19aOutJs = null;
//
//		//C122.mainId串到C101.mainId
//		List<C122S01H> c122s01hList = cls122Service.findC122S01H(mainId, UtilConstants.C122s01h_flowId.借保人資料);
//		if (c122s01hList.size()>0) {
//			C122S01H c122S01H = c122s01hList.get(0);
//			mainId = c122S01H.getRemainId();
//		}
//
//		ObjectMapper objectMapper = new ObjectMapper();
//		L120S19A l120s19a_latestInput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_autoCheck_input);
//		L120S19A l120s19a_latestOutput = clsService.findL120S19A_byMainId_itemType_latest_itemVersion(mainId, ClsConstants.L120S19A_ItemTypeCode.BRMP_autoCheck_output);
//		if (Util.isNotEmpty(l120s19a_latestInput)) {
//			inJs = JSONObject.fromObject(l120s19a_latestInput.getJsonData());
//		}
//		if (Util.isNotEmpty(l120s19a_latestOutput)) {
//			outJs = JSONObject.fromObject(l120s19a_latestOutput.getJsonData());
//			l120s19aOutJs = l120s19a_latestOutput;
//		}
//		Brmp005I brmp005I = new Brmp005I();
//		Brmp005O brmp005O = new Brmp005O();
//		try {
//			brmp005I = objectMapper.readValue(inJs.toString(), Brmp005I.class);
//			brmp005O = objectMapper.readValue(outJs.toString(), Brmp005O.class);
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//
//		List<Brmp005O.Brmp005O_result_policyObj> policyResult = brmp005O.getResult().getPolicyResult();
//		DataView<JSONObject> griPolicyResultView = this.getPolicyResultView("_grid_policyResult", policyResult,brmp005O.getResult().getPolicyFactor());
//
//		add(new Label("stat", brmp005O.getStat()));
//		add(new Label("systemUuid", brmp005O.getSystemUuid()));
//		add(new Label("uuid", brmp005O.getUuid()));
//		add(new Label("packageVersion", brmp005O.getPackageVersion()));
//		add(new Label("errorCode", brmp005O.getErrorCode()));
//		add(new Label("errorMsg", brmp005O.getErrorMsg()));
//
//		add(griPolicyResultView);
	}

//	private DataView<JSONObject> getPolicyResultView(final String id, List<Brmp005O.Brmp005O_result_policyObj> lists, Object policyFactor) {
//		final Map<String, String> statusDesc = new HashMap<String, String>();
//		statusDesc.put("0", "符合");
//		statusDesc.put("1", "未符合");
//		statusDesc.put("M", "Missing");
//		statusDesc.put("S", "不適用");
//
//		// 未符合>Missing>不適用>正常符合
//		final Map<String, String> newStatusOrder = new HashMap<String, String>();
////		newStatusOrder.put("0", "4");
////		newStatusOrder.put("1", "1");
////		newStatusOrder.put("M", "2");
////		newStatusOrder.put("S", "3");
//		newStatusOrder.put("R", "3");
//		newStatusOrder.put("A", "2");
//		newStatusOrder.put("", "1");
//
//		//消金處有定義排序順序
//		Collections.sort(lists, new Comparator<Brmp005O.Brmp005O_result_policyObj>() {
//			@Override
//			public int compare(Brmp005O.Brmp005O_result_policyObj o1, Brmp005O.Brmp005O_result_policyObj o2) {
//				// 未符合>Missing>不適用>正常符合
//				if (newStatusOrder.get(o1.getActionType()).compareTo(newStatusOrder.get(o2.getActionType())) == 0) {
//					// 再依代碼排序
//					return o1.getPolicyCode().compareTo(o2.getPolicyCode());
//				} else {
//					return newStatusOrder.get(o1.getActionType()).compareTo(newStatusOrder.get(o2.getActionType()));
//				}
//			}
//		});
//
//		final JSONObject factorObj = JSONObject.fromObject(Util.trim(policyFactor));
//
//		DataView dataView = new DataView(id, new ListDataProvider(lists)) {
//			int count = 0;
//
//			@Override
//			protected void populateItem(Item item) {
//				count++;
//				Brmp005O.Brmp005O_result_policyObj modelObject = (Brmp005O.Brmp005O_result_policyObj) item.getModelObject();
//				String policyCode = modelObject.getPolicyCode();
//				String policyDescription = modelObject.getPolicyDescription();
//				String showWord = modelObject.getShowWord();
//				String actionType = modelObject.getActionType();
//
//				String aTom = modelObject.getaTom();
//				if (Util.isNotEmpty(aTom)) {
//
//					if (factorObj!=null) {
//						JSONObject atom = factorObj.optJSONObject(aTom);
//						Iterator<String> keys = atom.keys();
//						while(keys.hasNext()) {
//							String key = keys.next();
//							if (!Util.isEmpty(key)) {
//								showWord = showWord.replace("{"+ aTom+"."+key +"}",atom.optString(key));
//							}
//						}
//					}
//				}
//
//				Label policyCodeLabel = new Label("policyCode", policyCode);
//				Label policyDescriptionLabel = new Label("policyDescription", policyDescription);
//				Label showWordLabel = new Label("showWord", showWord);
//
//				String statusDescription = "";
////				if ("0".equals(status)) {
////					statusDescription = "<input type='radio' disabled='disabled' checked />符合 <input type='radio' disabled='disabled' />未符合";
////				} else if ("1".equals(status) && "A".equals(actionType)) {
////					statusDescription = "<input type='radio' disabled='disabled' />符合 <input type='radio' disabled='disabled' checked /><span style='color:red'>未符合(請留意辦理)</>";
////				} else if ("1".equals(status) && "R".equals(actionType)) {
////					statusDescription = "<input type='radio' disabled='disabled' />符合 <input type='radio' disabled='disabled' checked /><span style='color:red'>未符合(非經報總處不得辦理)</>";
////				} else {
////					statusDescription = statusDesc.containsKey(status) ? statusDesc.get(status) : status;
////				}
//
////				Label statusLabel = new Label("status", statusDescription);
////				statusLabel.setEscapeModelStrings(false);
////				Label actionTypeLabel = new Label("actionType", actionType);
//
//
//				//item.add(policyCodeLabel.add(new SimpleAttributeModifier("id", policyCode)));
//				item.add(policyCodeLabel.setMarkupId(policyCode).setOutputMarkupId(true));
//				item.add(policyDescriptionLabel);
//				item.add(showWordLabel);
////				item.add(statusLabel);
//
//			}
//		};
//		return dataView;
//	}
//
//	private String expressValue(String result) {
//		if ("1".equals(result)) {
//			return "Y";
//		} else if ("0".equals(result)) {
//			return "N";
//		}
//
//		return result;
//	}
//
//	private String expressNumber(String result) {
//		if (Util.isEmpty(result)) {
//			return "N.A.";
//		} else {
//			return NumConverter.addComma(result);
//		}
//	}
}
