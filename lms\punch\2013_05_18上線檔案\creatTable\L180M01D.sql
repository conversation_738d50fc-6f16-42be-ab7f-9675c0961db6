CREATE TABLE LMS.L180M01D
	(
	OID CHARACTER (32) NOT NULL,
	PID CHARACTER (32) NOT NULL,
	DBUOBUTYPE CHARACTER (1) NOT NULL,
	DBUOBUID VARCHAR (10) NOT NULL,
	<PERSON><PERSON><PERSON>B<PERSON>DUPNO CHARACTER (1) NOT NULL,
	DBUOBUNAME VARCHAR (120),
	CONSTRAINT P_L180M01D PRIMARY KEY (OID)
	)
	IN EL_DATA_4KTS index in EL_INDEX_4KTS;
	
CREATE UNIQUE INDEX LMS.XL180M01D01 ON LMS.L180M01D   (PID, DBUOBUID, DBUOBUDUPNO);

COMMENT ON TABLE LMS.L180M01D IS '覆審名單共用客戶資料檔';

COMMENT ON COLUMN LMS.L180M01D.DBUOBUTYPE IS '0為DBU，1為OBU';
COMMENT ON COLUMN LMS.L180M01D.DBUOBUID IS '統編';
COMMENT ON COLUMN LMS.L180M01D.DBUOBUDUPNO IS '額度序號';
COMMENT ON COLUMN LMS.L180M01D.DBUOBUNAME IS '姓名';