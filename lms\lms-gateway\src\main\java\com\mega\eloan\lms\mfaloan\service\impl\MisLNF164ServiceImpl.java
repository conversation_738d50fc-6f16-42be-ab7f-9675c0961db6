/* 
 * MisLNF164ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Types;
import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLNF164Service;

/**
 * <pre>
 * 利率條件LN.LNF164(EJCIC.T)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
@Service
public class MisLNF164ServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF164Service {

	@Override
	public void insert(List<Object[]> dataList) {
		this.getJdbc().batchUpdate(
				"LNF164.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR }, dataList);

	}

}
