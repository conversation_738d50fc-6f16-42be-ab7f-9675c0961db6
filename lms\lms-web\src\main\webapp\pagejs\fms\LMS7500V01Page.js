$(document).ready(function(){

    var grid = $("#gridview").iGrid({
        handler: 'lms7500gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL140mm3a",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "custId",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{
	        colHeader: i18n.lms7500v01["L140MM3A.custId"], //借款戶統一編號
	        align: "left", width: 100, sortable: true, name: 'custId',
	        formatter: 'click', onclick: openDoc
	    }, {
	        colHeader: i18n.lms7500v01["L140MM3A.custName"], //借款戶名稱
	        align: "left", width: 100, sortable: true, name: 'custName'
	    },{
            colHeader: i18n.lms7500v01['L140MM3A.cntrNo'],//"額度序號",
            name: 'cntrNo',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms7500v01['L140MM3A.creator'],//"分行經辦",
            name: 'updater',
            width: 80,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.lms7500v01["L140MM3A.createTime"], // 建立日期
                align: "left",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'createTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });

	var GetCntrnoGrid = $('#GetCntrnoGrid').iGrid({
        handler: 'lms7500gridhandler', //設定handler
        height: 300, //設定高度
        action: 'queryGetCntrno', //執行的Method
        postData: {
            
        },
        needPager: false,
        rownumbers: true,
        colModel: [{
            colHeader: i18n.lms7500m01["cntrNo"], // 額度序號
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'cntrNo'
        }, {
        	name: 'sDate',
        	hidden: true
    	}],
        loadComplete: function () {
        	if( GetCntrnoGrid.getGridParam("records")>0){
        		
        	}else{
				var custId = GetCntrnoGrid.getGridParam("postData")['custId'];
				if(custId && custId.length>1){
					$.thickbox.close();
					API.showErrorMessage(i18n.lms7500m01["cntrNoError"]);	
				}            		
        	}
        }  
    });
	
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({			
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryL140mm3a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode,
				cntrNo: rowObject.cntrNo
            },
            target: rowObject.oid
        });
    }
	
	
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
                $.ajax({
                    handler: "lms7500m01formhandler",
                    data: {
                        formAction: "deleteL140mm3a",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
    }).end().find("#btnAdd").click(function(){
		chose_custId().done(function(resultFrom_chose_custId){
   	 		chose_cntrNo(resultFrom_chose_custId).done(function(resultFrom_chose_cntrNo){
				$.ajax({
					handler: "lms7500m01formhandler",
					action: 'checkCntrno',
					data: {
						custId: resultFrom_chose_cntrNo.custId,
						dupNo: resultFrom_chose_cntrNo.dupNo,
						custName: resultFrom_chose_cntrNo.custName,
						cntrNo: resultFrom_chose_cntrNo.cntrNo,
						sDate: resultFrom_chose_cntrNo.sDate
					},
					success: function(obj){
						if(obj.isMom){
							return CommonAPI.showMessage(i18n.lms7500m01["L140MM3A.message03"]);
						} else {
							//return CommonAPI.showMessage("此為子戶");
							$.ajax({
			                    handler: "lms7500m01formhandler",
			                    action : 'newl140mm3a',
								data : {
									cntrNo: resultFrom_chose_cntrNo.cntrNo,
								    custId: resultFrom_chose_cntrNo.custId,
									dupNo: resultFrom_chose_cntrNo.dupNo,
									custName: resultFrom_chose_cntrNo.custName          
			   	 				},
			                    success: function(obj){
				        			$.form.submit({
			                    		url: '../fms/lms7500m01/01',
			                    		data: {
			                        		formAction: "queryL140mm3a",
			                        		oid: obj.oid,
			                        		mainOid: obj.oid,
			                        		mainDocStatus: viewstatus,
			                        		txCode: txCode,
											cntrNo: resultFrom_chose_cntrNo.cntrNo,
											custId: resultFrom_chose_cntrNo.custId,
											dupNo: resultFrom_chose_cntrNo.dupNo,
											custName: resultFrom_chose_cntrNo.custName,
											mainId: obj.mainId
			                    		},
			                    		target: obj.oid
			               	 		});
								}
				    		});
						}
						$.thickbox.close();
					}
				});
	    	});
	    });
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
			// L140M01M.error1=此功能不能多選
            CommonAPI.showMessage(i18n.lms7500m01["L140MM3A.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    });
	
	function chose_custId(){	
		var my_dfd = $.Deferred();
		AddCustAction.open({
	    		handler: 'lms7500m01formhandler',
				action : 'echo_custId',
				data : {
	            },
				callback : function(json){					
	            	// 關掉 AddCustAction 的 
	            	$.thickbox.close();					
					my_dfd.resolve( json );					
				}
			});
		return my_dfd.promise();
	}
	
	function chose_cntrNo(resultFrom_chose_custId){
		var my_dfd = $.Deferred();		
		GetCntrnoGrid.jqGrid("setGridParam", {
            postData: {
                'custId': resultFrom_chose_custId.custId
				,'dupNo': resultFrom_chose_custId.dupNo
            },
            search: true
        }).trigger("reloadGrid");
		
		$("#GetCntrnoThickBox").thickbox({
	       title: i18n.lms7500m01["checkSelect"]+i18n.lms7500m01["cntrNo"], 
		   width: 400,
		   height: 450,
		   align: "center",
		   valign: "bottom",
           modal: false, 
		   i18n: i18n.def,
		   buttons: {
                "sure": function(){
					 var data = GetCntrnoGrid.getSingleData();
                     if (data) {
						 $.thickbox.close();
                    	 var cntrNo = data.cntrNo;
						 var sDate = data.sDate;
        				 my_dfd.resolve($.extend(resultFrom_chose_custId, {'cntrNo':cntrNo, 'sDate':sDate} ));
                     }     	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }	
		});	
		
		return my_dfd.promise();
	}
});

