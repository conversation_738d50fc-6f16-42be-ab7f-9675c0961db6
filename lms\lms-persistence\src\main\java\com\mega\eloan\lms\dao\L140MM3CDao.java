package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140MM3C;


public interface L140MM3CDao extends IGenericDao<L140MM3C> {

	List<L140MM3C> findByMainId(String mainId);

	L140MM3C findByOid(String oid);

	List<L140MM3C> findCurrentByMainId(String mainId);

	List<L140MM3C> findLastByMainId(String mainId);

	L140MM3C findByMainIdEstateType(String mainId, String estateType);

	List<L140MM3C> findLastestByMainIdEstateType(String mainId);

}