package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01R;

/** 個金卡友貸信用評等表 **/
public interface C120S01RDao extends IGenericDao<C120S01R> {

	C120S01R findByOid(String oid);
	
	List<C120S01R> findByMainId(String mainId);
	
	C120S01R findByUniqueKey(String mainId, String ownBrId, String custId, String dupNo);

	List<C120S01R> findByIndex01(String mainId, String ownBrId, String custId, String dupNo);
	
	List<C120S01R> findByCustIdDupId(String custId,String dupNo);
	
	int deleteByOid(String oid);
	
	
}