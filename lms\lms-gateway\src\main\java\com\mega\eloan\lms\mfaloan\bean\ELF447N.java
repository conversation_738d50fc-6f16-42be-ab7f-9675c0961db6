/* 
 * LNF447N.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;

/** 上傳文件狀態檔 **/
public class ELF447N extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 文件編號 UNID **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "ELF447N_UNID", length = 50, columnDefinition = "CHAR(50)", nullable = false, unique = true)
	private String elf447n_unid;

	/** 案件編號 **/
	@Column(name = "ELF447N_PROJECT_NO", length = 40, columnDefinition = "CHAR(40)", nullable = false)
	private String elf447n_project_no;

	/**
	 * 報案類別
	 * <p/>
	 * 1. 授信 ( 預設 ) 2. 應收帳款 3. 衍生性金融商品 <br/>
	 * 4. 交換票據抵用<br/>
	 * 5. 整批團貸<br/>
	 * Z. 其他
	 */
	@Column(name = "ELF447N_CLASS", length = 1, columnDefinition = "CHAR(1)", nullable = false)
	private String elf447n_class;

	/** 借款人統編 **/
	@Column(name = "ELF447N_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable = false, unique = true)
	private String elf447n_custid;

	/** 重複序號 **/
	@Column(name = "ELF447N_DUPNO", length = 1, columnDefinition = "CHAR(01)", nullable = false, unique = true)
	private String elf447n_dupno;

	/**
	 * 文件狀態
	 * <p/>
	 * 0- 預約 ( 預約核定日起六個月有效) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 ( 核定日起六個月有效 )<br/>
	 * 3- 已簽約(簽約日起六個月有效) A- 已撤銷 B- 已婉卻 C- 已退件 <br/>
	 * D- 不簽約(由簽約未動用報送來)<br/>
	 * Z- 未簽約(e-Loan用，不上傳DB)<br/>
	 * Y- 已動用(動用但不需做動審表)
	 */
	@Column(name = "ELF447N_STATUS", length = 1, columnDefinition = "CHAR(01)")
	private String elf447n_status;

	/**
	 * 狀態日
	 * <p/>
	 * 0- 預約核定日 1- 報核日 2- 核定日<br/>
	 * 3- 簽約日 A- 撤銷日 B- 婉卻日<br/>
	 * D- 不簽約日<br/>
	 * Y- 動用日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF447N_STATUS_DT", columnDefinition = "DATE")
	private Date elf447n_status_dt;

	/** 額度序號 **/
	@Column(name = "ELF447N_CONTRACT", length = 12, columnDefinition = "CHAR(12)", unique = true)
	private String elf447n_contract;

	/** 主辦分行代號 **/
	@Column(name = "ELF447N_BRANCH", length = 3, columnDefinition = "CHAR(03)")
	private String elf447n_branch;

	/** 目前案件所在分行代號 **/
	@Column(name = "ELF447N_PROCESS_BR", length = 3, columnDefinition = "CHAR(03)")
	private String elf447n_process_br;

	/**
	 * 額度控管種類
	 * <p/>
	 * 10.一般 | 10<br/>
	 * 20.信保 | 20<br/>
	 * 30.聯貸 | 30<br/>
	 * 40.合作母 | 40<br/>
	 * 41.合作子 | 41
	 */
	@Column(name = "ELF447N_FACT_TYPE", length = 2, columnDefinition = "CHAR(02)")
	private String elf447n_fact_type;

	/**
	 * 系統類別
	 * <p/>
	 * 1- 企金 2- 消金
	 */
	@Column(name = "ELF447N_SYSTYPE", length = 3, columnDefinition = "CHAR(3)")
	private String elf447n_systype;

	/**
	 * 授權等級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 */
	@Column(name = "ELF447N_CASELEVEL", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_caselevel;

	/** 資料修改日期 **/
	@Column(name = "ELF447N_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Date elf447n_tmestamp;

	/**
	 * 授信性質別
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 */
	@Column(name = "ELF447N_PROPERTY", length = 20, columnDefinition = "CHAR(20)")
	private String elf447n_property;

	/** 現請額度金額 **/
	@Column(name = "ELF447N_CURAMT", columnDefinition = "DECIMAL(15, 2)")
	private BigDecimal elf447n_curamt;

	/** 現請額度幣別 **/
	@Column(name = "ELF447N_CURR", length = 3, columnDefinition = "CHAR(3)")
	private String elf447n_curr;

	/** 前請額度金額 **/
	@Column(name = "ELF447N_OLDAMT", columnDefinition = "DECIMAL(15, 2)")
	private BigDecimal elf447n_oldamt;

	/** 前請額度幣別 **/
	@Column(name = "ELF447N_OLDCURR", length = 3, columnDefinition = "CHAR(3)")
	private String elf447n_oldcurr;

	/** 集團代碼 **/
	@Column(name = "ELF447N_GRPNO", length = 4, columnDefinition = "CHAR(4)")
	private String elf447n_grpno;

	/** 風險國別 **/
	@Column(name = "ELF447N_RISK_CNTRY", length = 2, columnDefinition = "CHAR(2)")
	private String elf447n_risk_cntry;

	/** 風險區域別 **/
	@Column(name = "ELF447N_RISK_AREA", length = 2, columnDefinition = "CHAR(2)")
	private String elf447n_risk_area;

	/** 行業對象別 **/
	@Column(name = "ELF447N_BUS_CD", length = 6, columnDefinition = "CHAR(6)")
	private String elf447n_bus_cd;

	/** 行業對象別細分類 **/
	@Column(name = "ELF447N_BUS_SUB_CD", length = 2, columnDefinition = "CHAR(2)")
	private String elf447n_bus_sub_cd;

	/**
	 * 團貸案名稱
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	@Column(name = "ELF447N_BUILD_NAME", length = 62, columnDefinition = "CHAR(62)")
	private String elf447n_build_name;

	/**
	 * 土地座落 (縣市)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	@Column(name = "ELF447N_SITE1", length = 22, columnDefinition = "CHAR(22)")
	private String elf447n_site1;

	/**
	 * 土地座落 (鄉鎮)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	@Column(name = "ELF447N_SITE2", length = 22, columnDefinition = "CHAR(22)")
	private String elf447n_site2;

	/**
	 * 土地座落 (段)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	@Column(name = "ELF447N_SITE3", length = 22, columnDefinition = "CHAR(22)")
	private String elf447n_site3;

	/**
	 * 土地座落 (小段)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	@Column(name = "ELF447N_SITE4", length = 22, columnDefinition = "CHAR(22)")
	private String elf447n_site4;

	/**
	 * 未動用原因維護日
	 * <p/>
	 * 由簽約未動用報送來
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF447N_NUSEDATE", columnDefinition = "DATE")
	private Date elf447n_nusedate;

	/**
	 * 未動用原因
	 * <p/>
	 * 由簽約未動用報送來
	 */
	@Column(name = "ELF447N_NUSEMEMO", length = 804, columnDefinition = "VCHAR(804)")
	private String elf447n_nusememo;

	/** 首段敘做利率 **/
	@Column(name = "ELF447N_INT_RATE", columnDefinition = "DECIMAL(8, 6)")
	private BigDecimal elf447n_int_rate;

	/** 利率 MEMO **/
	@Column(name = "ELF447N_INT_MEMO", length = 560, columnDefinition = "VCHAR(560)")
	private String elf447n_int_memo;

	/** 產品種類 **/
	@Column(name = "ELF447N_PROD_CLASS", length = 255, columnDefinition = "VCHAR(255)")
	private String elf447n_prod_class;

	/** 動用科目 **/
	@Column(name = "ELF447N_ACT_CODE", length = 255, columnDefinition = "VCHAR(255)")
	private String elf447n_act_code;

	/** 用途別 **/
	@Column(name = "ELF447N_PURPOSE", length = 255, columnDefinition = "VCHAR(255)")
	private String elf447n_purpose;

	/** 期間說明 **/
	@Column(name = "ELF447N_DURATION", length = 255, columnDefinition = "VCHAR(255)")
	private String elf447n_duration;

	/**
	 * 是否屬興建住宅
	 * <p/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N
	 */
	@Column(name = "ELF447N_RESIDENCE", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_residence;

	/**
	 * 不簽約原因
	 * <p/>
	 * (可複選)<br/>
	 * 01 額度<br/>
	 * 02 利率<br/>
	 * 03 擔保品<br/>
	 * 04 徵提(連帶)保證人<br/>
	 * 05 動用率限制<br/>
	 * 06 還款期間、還款條件<br/>
	 * 07 維持存款平均餘額條件<br/>
	 * 08 提供定存設質條件<br/>
	 * 09 限制用途條件<br/>
	 * 10 借款戶本身因素，主動婉拒訂約。<br/>
	 * 99 借款戶或連保人因素，本行主動婉卻訂約。<br/>
	 * 01;02;03;04;05;06;07;…
	 */
	@Column(name = "ELF447N_NSIGN_CODE", length = 40, columnDefinition = "CHAR(40)")
	private String elf447n_nsign_code;

	/**
	 * 不簽約原因
	 * <p/>
	 * (點選第99項時，於100文字以內由營業單位鍵入簡要說明)
	 */
	@Column(name = "ELF447N_NSIGN_MEMO", length = 204, columnDefinition = "CHAR(204)")
	private String elf447n_nsign_memo;

	/** 土地面積 */
	@Column(name = "ELF447N_LAND_AREA", columnDefinition = "DECIMAL(13, 2)")
	private BigDecimal elf447n_land_area;

	/** 預計取得建照日期 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF447N_BUILD_DATE", columnDefinition = "DATE")
	private Date elf447n_build_date;

	/** 預計撥款至動工期間月數 */
	@Column(name = "ELF447N_WAIT_MONTH", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal elf447n_wait_month;

	/** 擔保品座落區 */
	@Column(name = "ELF447N_LOCATE_CD", length = 3, columnDefinition = "CHAR(3)")
	private String elf447n_locate_cd;

	/** 座落區段 */
	@Column(name = "ELF447N_SITE3NO", columnDefinition = "DECIMAL(4, 0)")
	private BigDecimal elf447n_site3no;

	/** 座落區村里 */
	@Column(name = "ELF447N_SITE4NO", length = 11, columnDefinition = "CHAR(11)")
	private String elf447n_site4no;

	/** 土地使用分區 */
	@Column(name = "ELF447N_LAND_TYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_land_type;

	/** 授信性質別 */
	@Column(name = "ELF447N_PROPERTIES", length = 50, columnDefinition = "CHAR(50)")
	private String elf447n_properties;

	/** 實地覆審分行 */
	@Column(name = "ELF447N_REVIEWBR", length = 3, columnDefinition = "CHAR(3)")
	private String elf447n_reviewBr;

	/** 交易目的{1: 避險 2: 非避險} */
	@Column(name = "ELF447N_ISHEDGE", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_isHedge;

	/** 非避險額度－額外信用增強 */
	@Column(name = "ELF447N_ENHANCEAMT", columnDefinition = "DECIMAL(15, 2)")
	private BigDecimal elf447n_enhanceAmt;

	/** 專案種類 */
	@Column(name = "ELF447N_PROJ_CLASS", length = 2, columnDefinition = "CHAR(2)")
	private String elf447n_proj_class;

	/** 簽報書核准日期 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF447N_ENDDATE", columnDefinition = "DATE")
	private Date elf447n_endDate;

	/** 實收資本額幣別 */
	@Column(name = "ELF447N_RGTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String elf447n_rgtCurr;

	/** 實收資本額金額 */
	@Column(name = "ELF447N_RGTAMT", columnDefinition = "DECIMAL(15, 2)")
	private BigDecimal elf447n_rgtAmt;

	/** 實收資本額單位 */
	@Column(name = "ELF447N_RGTUNIT", columnDefinition = "DECIMAL(13, 0)")
	private BigDecimal elf447n_rgtUnit;

	/** 條件變更資訊 Condition Change **/
	@Column(name = "ELF447N_COND_CHG", length = 560, columnDefinition = "VCHAR(560)")
	private String elf447n_cond_chg;

	/** 未依銀行內部規定 internal regulations **/
	@Column(name = "ELF447N_INTREG", length = 140, columnDefinition = "VCHAR(140)")
	private String elf447n_intReg;

	/** 本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困 */
	@Column(name = "ELF447N_ISRESCUE", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_isRescue;

	/** 紓困貸款類別 */
	@Column(name = "ELF447N_RESCUEITEM", length = 3, columnDefinition = "CHAR(3)")
	private String elf447n_rescueItem;

	/** 減收利率 */
	@Column(name = "ELF447N_RESCUERATE", columnDefinition = "DECIMAL(7, 5)")
	private BigDecimal elf447n_rescueRate;

	/** 逾越授權註記_授信 */
	@Column(name = "ELF447N_OVAUTH_LN", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_ovauth_ln;

	/** 逾越授權註記_出口 */
	@Column(name = "ELF447N_OVAUTH_EX", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_ovauth_ex;

	/** 逾越授權註記_單獨劃分授信 */
	@Column(name = "ELF447N_OVAUTH_AL", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_ovauth_al;

	/** 不計入授信項目代號 */
	@Column(name = "ELF447N_LNNOFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elf447n_lnNoFlag;

	/** 現請額度金額_有擔保 */
	@Column(name = "ELF447N_CURAMT_S", columnDefinition = "DECIMAL(15, 2)")
	private BigDecimal elf447n_curAmt_S;

	/** 現請額度金額_無擔保 */
	@Column(name = "ELF447N_CURAMT_N", columnDefinition = "DECIMAL(15, 2)")
	private BigDecimal elf447n_curAmt_N;

	/** 取得文件編號 UNID **/
	public String getElf447n_unid() {
		return this.elf447n_unid;
	}

	/** 設定文件編號 UNID **/
	public void setElf447n_unid(String value) {
		this.elf447n_unid = value;
	}

	/** 取得案件編號 **/
	public String getElf447n_project_no() {
		return this.elf447n_project_no;
	}

	/** 設定案件編號 **/
	public void setElf447n_project_no(String value) {
		this.elf447n_project_no = value;
	}

	/**
	 * 取得報案類別
	 * <p/>
	 * 1. 授信 ( 預設 ) 2. 應收帳款 3. 衍生性金融商品 <br/>
	 * 4. 交換票據抵用<br/>
	 * 5. 整批團貸<br/>
	 * Z. 其他
	 */
	public String getElf447n_class() {
		return this.elf447n_class;
	}

	/**
	 * 設定報案類別
	 * <p/>
	 * 1. 授信 ( 預設 ) 2. 應收帳款 3. 衍生性金融商品 <br/>
	 * 4. 交換票據抵用<br/>
	 * 5. 整批團貸<br/>
	 * Z. 其他
	 **/
	public void setElf447n_class(String value) {
		this.elf447n_class = value;
	}

	/** 取得借款人統編 **/
	public String getElf447n_custid() {
		return this.elf447n_custid;
	}

	/** 設定借款人統編 **/
	public void setElf447n_custid(String value) {
		this.elf447n_custid = value;
	}

	/** 取得重複序號 **/
	public String getElf447n_dupno() {
		return this.elf447n_dupno;
	}

	/** 設定重複序號 **/
	public void setElf447n_dupno(String value) {
		this.elf447n_dupno = value;
	}

	/**
	 * 取得文件狀態
	 * <p/>
	 * 0- 預約 ( 預約核定日起六個月有效) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 ( 核定日起六個月有效 )<br/>
	 * 3- 已簽約(簽約日起六個月有效) A- 已撤銷 B- 已婉卻 C- 已退件 <br/>
	 * D- 不簽約(由簽約未動用報送來)<br/>
	 * Z- 未簽約(e-Loan用，不上傳DB)<br/>
	 * Y- 已動用(動用但不需做動審表)
	 */
	public String getElf447n_status() {
		return this.elf447n_status;
	}

	/**
	 * 設定文件狀態
	 * <p/>
	 * 0- 預約 ( 預約核定日起六個月有效) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 ( 核定日起六個月有效 )<br/>
	 * 3- 已簽約(簽約日起六個月有效) A- 已撤銷 B- 已婉卻 C- 已退件 <br/>
	 * D- 不簽約(由簽約未動用報送來)<br/>
	 * Z- 未簽約(e-Loan用，不上傳DB)<br/>
	 * Y- 已動用(動用但不需做動審表)
	 **/
	public void setElf447n_status(String value) {
		this.elf447n_status = value;
	}

	/**
	 * 取得狀態日
	 * <p/>
	 * 0- 預約核定日 1- 報核日 2- 核定日<br/>
	 * 3- 簽約日 A- 撤銷日 B- 婉卻日<br/>
	 * D- 不簽約日<br/>
	 * Y- 動用日
	 */
	public Date getElf447n_status_dt() {
		return this.elf447n_status_dt;
	}

	/**
	 * 設定狀態日
	 * <p/>
	 * 0- 預約核定日 1- 報核日 2- 核定日<br/>
	 * 3- 簽約日 A- 撤銷日 B- 婉卻日<br/>
	 * D- 不簽約日<br/>
	 * Y- 動用日
	 **/
	public void setElf447n_status_dt(Date value) {
		this.elf447n_status_dt = value;
	}

	/** 取得額度序號 **/
	public String getElf447n_contract() {
		return this.elf447n_contract;
	}

	/** 設定額度序號 **/
	public void setElf447n_contract(String value) {
		this.elf447n_contract = value;
	}

	/** 取得主辦分行代號 **/
	public String getElf447n_branch() {
		return this.elf447n_branch;
	}

	/** 設定主辦分行代號 **/
	public void setElf447n_branch(String value) {
		this.elf447n_branch = value;
	}

	/** 取得目前案件所在分行代號 **/
	public String getElf447n_process_br() {
		return this.elf447n_process_br;
	}

	/** 設定目前案件所在分行代號 **/
	public void setElf447n_process_br(String value) {
		this.elf447n_process_br = value;
	}

	/**
	 * 取得額度控管種類
	 * <p/>
	 * 10.一般 | 10<br/>
	 * 20.信保 | 20<br/>
	 * 30.聯貸 | 30<br/>
	 * 40.合作母 | 40<br/>
	 * 41.合作子 | 41
	 */
	public String getElf447n_fact_type() {
		return this.elf447n_fact_type;
	}

	/**
	 * 設定額度控管種類
	 * <p/>
	 * 10.一般 | 10<br/>
	 * 20.信保 | 20<br/>
	 * 30.聯貸 | 30<br/>
	 * 40.合作母 | 40<br/>
	 * 41.合作子 | 41
	 **/
	public void setElf447n_fact_type(String value) {
		this.elf447n_fact_type = value;
	}

	/**
	 * 取得系統類別
	 * <p/>
	 * 1- 企金 2- 消金
	 */
	public String getElf447n_systype() {
		return this.elf447n_systype;
	}

	/**
	 * 設定系統類別
	 * <p/>
	 * 1- 企金 2- 消金
	 **/
	public void setElf447n_systype(String value) {
		this.elf447n_systype = value;
	}

	/**
	 * 取得授權等級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 */
	public String getElf447n_caselevel() {
		return this.elf447n_caselevel;
	}

	/**
	 * 設定授權等級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 **/
	public void setElf447n_caselevel(String value) {
		this.elf447n_caselevel = value;
	}

	/** 取得資料修改日期 **/
	public Date getElf447n_tmestamp() {
		return this.elf447n_tmestamp;
	}

	/** 設定資料修改日期 **/
	public void setElf447n_tmestamp(Date value) {
		this.elf447n_tmestamp = value;
	}

	/**
	 * 取得授信性質別
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 */
	public String getElf447n_property() {
		return this.elf447n_property;
	}

	/**
	 * 設定授信性質別
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 **/
	public void setElf447n_property(String value) {
		this.elf447n_property = value;
	}

	/** 取得現請額度金額 **/
	public BigDecimal getElf447n_curamt() {
		return this.elf447n_curamt;
	}

	/** 設定現請額度金額 **/
	public void setElf447n_curamt(BigDecimal value) {
		this.elf447n_curamt = value;
	}

	/** 取得現請額度幣別 **/
	public String getElf447n_curr() {
		return this.elf447n_curr;
	}

	/** 設定現請額度幣別 **/
	public void setElf447n_curr(String value) {
		this.elf447n_curr = value;
	}

	/** 取得前請額度金額 **/
	public BigDecimal getElf447n_oldamt() {
		return this.elf447n_oldamt;
	}

	/** 設定前請額度金額 **/
	public void setElf447n_oldamt(BigDecimal value) {
		this.elf447n_oldamt = value;
	}

	/** 取得前請額度幣別 **/
	public String getElf447n_oldcurr() {
		return this.elf447n_oldcurr;
	}

	/** 設定前請額度幣別 **/
	public void setElf447n_oldcurr(String value) {
		this.elf447n_oldcurr = value;
	}

	/** 取得集團代碼 **/
	public String getElf447n_grpno() {
		return this.elf447n_grpno;
	}

	/** 設定集團代碼 **/
	public void setElf447n_grpno(String value) {
		this.elf447n_grpno = value;
	}

	/** 取得風險國別 **/
	public String getElf447n_risk_cntry() {
		return this.elf447n_risk_cntry;
	}

	/** 設定風險國別 **/
	public void setElf447n_risk_cntry(String value) {
		this.elf447n_risk_cntry = value;
	}

	/** 取得風險區域別 **/
	public String getElf447n_risk_area() {
		return this.elf447n_risk_area;
	}

	/** 設定風險區域別 **/
	public void setElf447n_risk_area(String value) {
		this.elf447n_risk_area = value;
	}

	/** 取得行業對象別 **/
	public String getElf447n_bus_cd() {
		return this.elf447n_bus_cd;
	}

	/** 設定行業對象別 **/
	public void setElf447n_bus_cd(String value) {
		this.elf447n_bus_cd = value;
	}

	/** 取得行業對象別細分類 **/
	public String getElf447n_bus_sub_cd() {
		return this.elf447n_bus_sub_cd;
	}

	/** 設定行業對象別細分類 **/
	public void setElf447n_bus_sub_cd(String value) {
		this.elf447n_bus_sub_cd = value;
	}

	/**
	 * 取得團貸案名稱
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	public String getElf447n_build_name() {
		return this.elf447n_build_name;
	}

	/**
	 * 設定團貸案名稱
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 **/
	public void setElf447n_build_name(String value) {
		this.elf447n_build_name = value;
	}

	/**
	 * 取得土地座落 (縣市)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	public String getElf447n_site1() {
		return this.elf447n_site1;
	}

	/**
	 * 設定土地座落 (縣市)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 **/
	public void setElf447n_site1(String value) {
		this.elf447n_site1 = value;
	}

	/**
	 * 取得土地座落 (鄉鎮)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	public String getElf447n_site2() {
		return this.elf447n_site2;
	}

	/**
	 * 設定土地座落 (鄉鎮)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 **/
	public void setElf447n_site2(String value) {
		this.elf447n_site2 = value;
	}

	/**
	 * 取得土地座落 (段)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	public String getElf447n_site3() {
		return this.elf447n_site3;
	}

	/**
	 * 設定土地座落 (段)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 **/
	public void setElf447n_site3(String value) {
		this.elf447n_site3 = value;
	}

	/**
	 * 取得土地座落 (小段)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 */
	public String getElf447n_site4() {
		return this.elf447n_site4;
	}

	/**
	 * 設定土地座落 (小段)
	 * <p/>
	 * 比照MIS.PTEAMAPP的格式和長度
	 **/
	public void setElf447n_site4(String value) {
		this.elf447n_site4 = value;
	}

	/**
	 * 取得未動用原因維護日
	 * <p/>
	 * 由簽約未動用報送來
	 */
	public Date getElf447n_nusedate() {
		return this.elf447n_nusedate;
	}

	/**
	 * 設定未動用原因維護日
	 * <p/>
	 * 由簽約未動用報送來
	 **/
	public void setElf447n_nusedate(Date value) {
		this.elf447n_nusedate = value;
	}

	/**
	 * 取得未動用原因
	 * <p/>
	 * 由簽約未動用報送來
	 */
	public String getElf447n_nusememo() {
		return this.elf447n_nusememo;
	}

	/**
	 * 設定未動用原因
	 * <p/>
	 * 由簽約未動用報送來
	 **/
	public void setElf447n_nusememo(String value) {
		this.elf447n_nusememo = value;
	}

	/** 取得首段敘做利率 **/
	public BigDecimal getElf447n_int_rate() {
		return this.elf447n_int_rate;
	}

	/** 設定首段敘做利率 **/
	public void setElf447n_int_rate(BigDecimal value) {
		this.elf447n_int_rate = value;
	}

	/** 取得利率 MEMO **/
	public String getElf447n_int_memo() {
		return this.elf447n_int_memo;
	}

	/** 設定利率 MEMO **/
	public void setElf447n_int_memo(String value) {
		this.elf447n_int_memo = value;
	}

	/** 取得產品種類 **/
	public String getElf447n_prod_class() {
		return this.elf447n_prod_class;
	}

	/** 設定產品種類 **/
	public void setElf447n_prod_class(String value) {
		this.elf447n_prod_class = value;
	}

	/** 取得動用科目 **/
	public String getElf447n_act_code() {
		return this.elf447n_act_code;
	}

	/** 設定動用科目 **/
	public void setElf447n_act_code(String value) {
		this.elf447n_act_code = value;
	}

	/** 取得用途別 **/
	public String getElf447n_purpose() {
		return this.elf447n_purpose;
	}

	/** 設定用途別 **/
	public void setElf447n_purpose(String value) {
		this.elf447n_purpose = value;
	}

	/** 取得期間說明 **/
	public String getElf447n_duration() {
		return this.elf447n_duration;
	}

	/** 設定期間說明 **/
	public void setElf447n_duration(String value) {
		this.elf447n_duration = value;
	}

	/**
	 * 取得是否屬興建住宅
	 * <p/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N
	 */
	public String getElf447n_residence() {
		return this.elf447n_residence;
	}

	/**
	 * 設定是否屬興建住宅
	 * <p/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N
	 **/
	public void setElf447n_residence(String value) {
		this.elf447n_residence = value;
	}

	/**
	 * 取得不簽約原因
	 * <p/>
	 * (可複選)<br/>
	 * 01 額度<br/>
	 * 02 利率<br/>
	 * 03 擔保品<br/>
	 * 04 徵提(連帶)保證人<br/>
	 * 05 動用率限制<br/>
	 * 06 還款期間、還款條件<br/>
	 * 07 維持存款平均餘額條件<br/>
	 * 08 提供定存設質條件<br/>
	 * 09 限制用途條件<br/>
	 * 10 借款戶本身因素，主動婉拒訂約。<br/>
	 * 99 借款戶或連保人因素，本行主動婉卻訂約。<br/>
	 * 01;02;03;04;05;06;07;…
	 */
	public String getElf447n_nsign_code() {
		return this.elf447n_nsign_code;
	}

	/**
	 * 設定不簽約原因
	 * <p/>
	 * (可複選)<br/>
	 * 01 額度<br/>
	 * 02 利率<br/>
	 * 03 擔保品<br/>
	 * 04 徵提(連帶)保證人<br/>
	 * 05 動用率限制<br/>
	 * 06 還款期間、還款條件<br/>
	 * 07 維持存款平均餘額條件<br/>
	 * 08 提供定存設質條件<br/>
	 * 09 限制用途條件<br/>
	 * 10 借款戶本身因素，主動婉拒訂約。<br/>
	 * 99 借款戶或連保人因素，本行主動婉卻訂約。<br/>
	 * 01;02;03;04;05;06;07;…
	 **/
	public void setElf447n_nsign_code(String value) {
		this.elf447n_nsign_code = value;
	}

	/**
	 * 取得不簽約原因
	 * <p/>
	 * (點選第99項時，於100文字以內由營業單位鍵入簡要說明)
	 */
	public String getElf447n_nsign_memo() {
		return this.elf447n_nsign_memo;
	}

	/**
	 * 設定不簽約原因
	 * <p/>
	 * (點選第99項時，於100文字以內由營業單位鍵入簡要說明)
	 **/
	public void setElf447n_nsign_memo(String value) {
		this.elf447n_nsign_memo = value;
	}

	/** 設定土地面積 */
	public void setElf447n_land_area(BigDecimal value) {
		this.elf447n_land_area = value;
	}

	/** 取得土地面積 */
	public BigDecimal getElf447n_land_area() {
		return this.elf447n_land_area;
	}

	/** 設定預計取得建照日期 */
	public void setElf447n_build_date(Date value) {
		this.elf447n_build_date = value;
	}

	/** 取得預計取得建照日期 */
	public Date getElf447n_build_date() {
		return this.elf447n_build_date;
	}

	/** 設定預計撥款至動工期間月數 */
	public void setElf447n_wait_month(BigDecimal value) {
		this.elf447n_wait_month = value;
	}

	/** 取得預計撥款至動工期間月數 */
	public BigDecimal getElf447n_wait_month() {
		return this.elf447n_wait_month;
	}

	/** 設定擔保品座落區 */
	public void setElf447n_locate_cd(String value) {
		this.elf447n_locate_cd = value;
	}

	/** 取得擔保品座落區 */
	public String getElf447n_locate_cd() {
		return this.elf447n_locate_cd;
	}

	/** 設定座落區段 */
	public void setElf447n_site3no(BigDecimal value) {
		this.elf447n_site3no = value;
	}

	/** 取得座落區段 */
	public BigDecimal getElf447n_site3no() {
		return this.elf447n_site3no;
	}

	/** 設定座落區村里 */
	public void setElf447n_site4no(String value) {
		this.elf447n_site4no = value;
	}

	/** 取得座落區村里 */
	public String getElf447n_site4no() {
		return this.elf447n_site4no;
	}

	/** 設定土地使用分區 */
	public void setElf447n_land_type(String value) {
		this.elf447n_land_type = value;
	}

	/** 取得土地使用分區 */
	public String getElf447n_land_type() {
		return this.elf447n_land_type;
	}

	/** 授信性質別 */
	public String getElf447n_properties() {
		return elf447n_properties;
	}

	/** 授信性質別 */
	public void setElf447n_properties(String elf447n_properties) {
		this.elf447n_properties = elf447n_properties;
	}

	/** 實地覆審分行 */
	public String getElf447n_reviewBr() {
		return elf447n_reviewBr;
	}

	/** 實地覆審分行 */
	public void setElf447n_reviewBr(String elf447n_reviewBr) {
		this.elf447n_reviewBr = elf447n_reviewBr;
	}

	/** 交易目的{1: 避險 2: 非避險} */
	public String getElf447n_isHedge() {
		return elf447n_isHedge;
	}

	/** 交易目的{1: 避險 2: 非避險} */
	public void setElf447n_isHedge(String elf447n_isHedge) {
		this.elf447n_isHedge = elf447n_isHedge;
	}

	/** 非避險額度－額外信用增強 */
	public BigDecimal getElf447n_enhanceAmt() {
		return elf447n_enhanceAmt;
	}

	/** 非避險額度－額外信用增強 */
	public void setElf447n_enhanceAmt(BigDecimal elf447n_enhanceAmt) {
		this.elf447n_enhanceAmt = elf447n_enhanceAmt;
	}

	/** 專案種類 */
	public String getElf447n_proj_class() {
		return elf447n_proj_class;
	}

	/** 專案種類 */
	public void setElf447n_proj_class(String elf447n_proj_class) {
		this.elf447n_proj_class = elf447n_proj_class;
	}

	/** 簽報書核准日期 */
	public Date getElf447n_endDate() {
		return elf447n_endDate;
	}

	/** 簽報書核准日期 */
	public void setElf447n_endDate(Date elf447n_endDate) {
		this.elf447n_endDate = elf447n_endDate;
	}

	/** 實收資本額幣別 */
	public String getElf447n_rgtCurr() {
		return elf447n_rgtCurr;
	}

	/** 實收資本額幣別 */
	public void setElf447n_rgtCurr(String elf447n_rgtCurr) {
		this.elf447n_rgtCurr = elf447n_rgtCurr;
	}

	/** 實收資本額金額 */
	public BigDecimal getElf447n_rgtAmt() {
		return elf447n_rgtAmt;
	}

	/** 實收資本額金額 */
	public void setElf447n_rgtAmt(BigDecimal elf447n_rgtAmt) {
		this.elf447n_rgtAmt = elf447n_rgtAmt;
	}

	/** 實收資本額單位 */
	public BigDecimal getElf447n_rgtUnit() {
		return elf447n_rgtUnit;
	}

	/** 實收資本額單位 */
	public void setElf447n_rgtUnit(BigDecimal elf447n_rgtUnit) {
		this.elf447n_rgtUnit = elf447n_rgtUnit;
	}

	/** 取得條件變更資訊 Condition Change **/
	public String getElf447n_cond_chg() {
		return this.elf447n_cond_chg;
	}

	/** 設定條件變更資訊 Condition Change **/
	public void setElf447n_cond_chg(String value) {
		this.elf447n_cond_chg = value;
	}

	/** 取得未依銀行內部規定 internal regulations **/
	public String getElf447n_intReg() {
		return this.elf447n_intReg;
	}

	/** 設定未依銀行內部規定 internal regulations **/
	public void setElf447n_intReg(String value) {
		this.elf447n_intReg = value;
	}

	public void setElf447n_isRescue(String elf447n_isRescue) {
		this.elf447n_isRescue = elf447n_isRescue;
	}

	public String getElf447n_isRescue() {
		return elf447n_isRescue;
	}

	public void setElf447n_rescueItem(String elf447n_rescueItem) {
		this.elf447n_rescueItem = elf447n_rescueItem;
	}

	public String getElf447n_rescueItem() {
		return elf447n_rescueItem;
	}

	public void setElf447n_rescueRate(BigDecimal elf447n_rescueRate) {
		this.elf447n_rescueRate = elf447n_rescueRate;
	}

	public BigDecimal getElf447n_rescueRate() {
		return elf447n_rescueRate;
	}

	public void setElf447n_ovauth_ln(String elf447n_ovauth_ln) {
		this.elf447n_ovauth_ln = elf447n_ovauth_ln;
	}

	public String getElf447n_ovauth_ln() {
		return elf447n_ovauth_ln;
	}

	public void setElf447n_ovauth_ex(String elf447n_ovauth_ex) {
		this.elf447n_ovauth_ex = elf447n_ovauth_ex;
	}

	public String getElf447n_ovauth_ex() {
		return elf447n_ovauth_ex;
	}

	public void setElf447n_ovauth_al(String elf447n_ovauth_al) {
		this.elf447n_ovauth_al = elf447n_ovauth_al;
	}

	public String getElf447n_ovauth_al() {
		return elf447n_ovauth_al;
	}

	public void setElf447n_lnNoFlag(String elf447n_lnNoFlag) {
		this.elf447n_lnNoFlag = elf447n_lnNoFlag;
	}

	public String getElf447n_lnNoFlag() {
		return elf447n_lnNoFlag;
	}

	public void setElf447n_curAmt_S(BigDecimal elf447n_curAmt_S) {
		this.elf447n_curAmt_S = elf447n_curAmt_S;
	}

	public BigDecimal getElf447n_curAmt_S() {
		return elf447n_curAmt_S;
	}

	public void setElf447n_curAmt_N(BigDecimal elf447n_curAmt_N) {
		this.elf447n_curAmt_N = elf447n_curAmt_N;
	}

	public BigDecimal getElf447n_curAmt_N() {
		return elf447n_curAmt_N;
	}
}
