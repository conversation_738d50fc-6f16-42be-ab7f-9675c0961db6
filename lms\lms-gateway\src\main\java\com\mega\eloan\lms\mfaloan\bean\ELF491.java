package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 覆審控制檔 **/
public class ELF491 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/**
	 * 分行代號
	 */
	@Column(name = "ELF491_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable=false,unique = true)
	private String elf491_branch;
	
	/**
	 * 借款人統一編號 <br/>
	 * ● 類別 8-1【LNBD9455】 select * from mis.elf491 where elf491_custid='XXXXXXXXXX' and ELF491_DUPNO='X'  {10碼X：當年度, 10碼Y：前一年, 10碼Z：前二年} <br/>
	 * ● 不動產十足擔保抽樣 【LNBD9455】select * from mis.elf491 where elf491_custid='AAAAAAAAAA' and ELF491_DUPNO='A' {10碼A：, 10碼B：, 10碼C：} <br/>
	 */
	@Column(name = "ELF491_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable=false,unique = true)
	private String elf491_custid;
	
	/**
	 * 重複序號
	 */
	@Column(name = "ELF491_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable=false,unique = true)
	private String elf491_dupno;
	
	/**
	 * 主要授信戶
	 */
	@Column(name = "ELF491_MAINCUST", length = 1, columnDefinition = "CHAR(1)")	
	private String elf491_maincust;
	
	/**
	 * 上上次覆審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491_LLRDATE", columnDefinition = "DATE")
	private Date elf491_llrdate;
	
	/**
	 * 上次複審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491_LRDATE", columnDefinition = "DATE")
	private Date elf491_lrdate;
	
	/**
	 * 下次複審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491_CRDATE", columnDefinition = "DATE")
	private Date elf491_crdate;
	
	/**
	 * 不覆審代碼
	 */
	@Column(name = "ELF491_NCKDFLAG", length = 2, columnDefinition = "CHAR(2)")	
	private String elf491_nckdflag;
	
	/**
	 * 不覆審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491_NCKDDATE", columnDefinition = "DATE")
	private Date elf491_nckddate;
	
	/**
	 * 不覆審備註
	 */
	@Column(name = "ELF491_NCKDMEMO", length = 202, columnDefinition = "CHAR(202)")
	private String elf491_nckdmemo;
	
	/**
	 * 銷戶日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491_CANCELDT", columnDefinition = "DATE")
	private Date elf491_canceldt;
	
	/**
	 * 資料修改人
	 */
	@Column(name = "ELF491_UPDATER", length = 8, columnDefinition = "CHAR(8)")
	private String elf491_updater;
	
	/** 
	 * 資料更新日
	 */
	@Column(name = "ELF491_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf491_tmestamp;
	
	/**
	 * (企金覆審)主管機關指定覆審案件<br/>
	 * (個金覆審)異常通報記錄次數，覆審報告表把 elf491_uckdline 帶至  c241m01a.uckdLine <br/>
	 * c241m01a.uckdLine 之後(1)上傳elf491時累加 (2)寫入 elf492_uckdline
	 */
	@Column(name = "ELF491_UCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf491_uckdline;
	
	/**
	 * (企金覆審)主管機關通知日期<br/>
	 * (個金覆審)異常通報 LNFE0854_SDATE
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491_UCKDDT", columnDefinition = "DATE")
	private Date elf491_uckddt;
	
	/**
	 * 覆審報告書種類{N:新案, O:舊案}
	 */
	@Column(name = "ELF491_REPORTKIND", length = 1, columnDefinition = "CHAR(1)")
	private String elf491_reportkind;
	
	/**
	 * 覆審MEMO
	 */
	@Column(name = "ELF491_REMOMO", length = 30, columnDefinition = "CHAR(30)")
	private String elf491_remomo;
	
	/**
	 * 新案註記
	 */
	@Column(name = "ELF491_NEWFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elf491_newflag;
	/**
	 * 本年度8-1已覆審註記
	 */
	@Column(name = "ELF491_8_1_FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elf491_8_1_flag;
	
	/**
	 * 上年度8-1已覆審註記
	 */
	@Column(name = "ELF491_8_1_FLAG_O", length = 1, columnDefinition = "CHAR(1)")
	private String elf491_8_1_flag_o;

	/**
	 * 上次土建融實地覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF491_LASTREALDT", columnDefinition = "DATE")
	private Date elf491_lastRealDt;
	
	/** 文件編號UNID */
	@Column(name = "ELF491_UNID", length = 32, columnDefinition = "CHAR(32)")
	private String elf491_unid;
	
	public String getElf491_branch() {
		return elf491_branch;
	}

	public void setElf491_branch(String elf491_branch) {
		this.elf491_branch = elf491_branch;
	}

	public String getElf491_custid() {
		return elf491_custid;
	}

	public void setElf491_custid(String elf491_custid) {
		this.elf491_custid = elf491_custid;
	}

	public String getElf491_dupno() {
		return elf491_dupno;
	}

	public void setElf491_dupno(String elf491_dupno) {
		this.elf491_dupno = elf491_dupno;
	}

	public String getElf491_maincust() {
		return elf491_maincust;
	}

	public void setElf491_maincust(String elf491_maincust) {
		this.elf491_maincust = elf491_maincust;
	}

	public Date getElf491_llrdate() {
		return elf491_llrdate;
	}

	public void setElf491_llrdate(Date elf491_llrdate) {
		this.elf491_llrdate = elf491_llrdate;
	}

	public Date getElf491_lrdate() {
		return elf491_lrdate;
	}

	public void setElf491_lrdate(Date elf491_lrdate) {
		this.elf491_lrdate = elf491_lrdate;
	}

	public Date getElf491_crdate() {
		return elf491_crdate;
	}

	public void setElf491_crdate(Date elf491_crdate) {
		this.elf491_crdate = elf491_crdate;
	}

	public String getElf491_nckdflag() {
		return elf491_nckdflag;
	}

	public void setElf491_nckdflag(String elf491_nckdflag) {
		this.elf491_nckdflag = elf491_nckdflag;
	}

	public Date getElf491_nckddate() {
		return elf491_nckddate;
	}

	public void setElf491_nckddate(Date elf491_nckddate) {
		this.elf491_nckddate = elf491_nckddate;
	}

	public String getElf491_nckdmemo() {
		return elf491_nckdmemo;
	}

	public void setElf491_nckdmemo(String elf491_nckdmemo) {
		this.elf491_nckdmemo = elf491_nckdmemo;
	}

	public Date getElf491_canceldt() {
		return elf491_canceldt;
	}

	public void setElf491_canceldt(Date elf491_canceldt) {
		this.elf491_canceldt = elf491_canceldt;
	}

	public String getElf491_updater() {
		return elf491_updater;
	}

	public void setElf491_updater(String elf491_updater) {
		this.elf491_updater = elf491_updater;
	}

	public Timestamp getElf491_tmestamp() {
		return elf491_tmestamp;
	}

	public void setElf491_tmestamp(Timestamp elf491_tmestamp) {
		this.elf491_tmestamp = elf491_tmestamp;
	}

	public String getElf491_uckdline() {
		return elf491_uckdline;
	}

	public void setElf491_uckdline(String elf491_uckdline) {
		this.elf491_uckdline = elf491_uckdline;
	}

	public Date getElf491_uckddt() {
		return elf491_uckddt;
	}

	public void setElf491_uckddt(Date elf491_uckddt) {
		this.elf491_uckddt = elf491_uckddt;
	}

	public String getElf491_reportkind() {
		return elf491_reportkind;
	}

	public void setElf491_reportkind(String elf491_reportkind) {
		this.elf491_reportkind = elf491_reportkind;
	}

	public String getElf491_remomo() {
		return elf491_remomo;
	}

	public void setElf491_remomo(String elf491_remomo) {
		this.elf491_remomo = elf491_remomo;
	}

	public String getElf491_newflag() {
		return elf491_newflag;
	}

	public void setElf491_newflag(String elf491_newflag) {
		this.elf491_newflag = elf491_newflag;
	}

	public String getElf491_8_1_flag() {
		return elf491_8_1_flag;
	}

	public void setElf491_8_1_flag(String elf491_8_1_flag) {
		this.elf491_8_1_flag = elf491_8_1_flag;
	}

	public String getElf491_8_1_flag_o() {
		return elf491_8_1_flag_o;
	}

	public void setElf491_8_1_flag_o(String elf491_8_1_flag_o) {
		this.elf491_8_1_flag_o = elf491_8_1_flag_o;
	}

	public Date getElf491_lastRealDt() {
		return elf491_lastRealDt;
	}

	public void setElf491_lastRealDt(Date elf491_lastRealDt) {
		this.elf491_lastRealDt = elf491_lastRealDt;
	}

	public String getElf491_unid() {
		return elf491_unid;
	}

	public void setElf491_unid(String elf491_unid) {
		this.elf491_unid = elf491_unid;
	}		
}
