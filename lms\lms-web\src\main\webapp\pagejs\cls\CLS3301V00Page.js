pageJsInit(function() {
	var Action = {
		baseUrl: "../cls/cls3301v00",
		//1.海外 2.企金 3.個金
		createType: "3",
		i18nkey: i18n.cls3301v00,
		// 篩選
		openFilterBox: function() {
			var $filterForm = $("#filterForm");
			// 初始化
			$filterForm.reset();
			var thickTitle = "";
			var sysdate = CommonAPI.getToday().split("-");
			var endDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
			var fromDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);

			fromDate.setMonth(fromDate.getMonth() - 3);

			$("#fromDate").val(fromDate.getFullYear() + "-" + (fromDate.getMonth() < 9 ? "0" : "") + (fromDate.getMonth() + 1) + "-" + (fromDate.getDate() < 10 ? "0" : "") + fromDate.getDate());
			$("#endDate").val(endDate.getFullYear() + "-" + (endDate.getMonth() < 9 ? "0" : "") + (endDate.getMonth() + 1) + "-" + (endDate.getDate() < 10 ? "0" : "") + endDate.getDate());


			$("#filterBox").thickbox({
				// l120v05.title01=已核准受理查詢
				title: thickTitle,
				width: 500,
				height: 200,
				modal: true,
				valign: "bottom",
				align: "center",
				i18n: i18n.def,
				buttons: {
					"sure": function() {
						if (!$("#filterForm").valid()) {
							return;
						}
						if ($.trim($("#custId").val()) == "") {
							//cls3301v00.error01=請輸入統編
							return CommonAPI.showErrorMessage(i18n.cls3301v00["cls3301v00.error01"]);
						}
						if ($.trim($("#endDate").val()) == "" || $.trim($("#fromDate").val()) == "") {
							//cls3301v00.error02=請輸入日期
							return CommonAPI.showErrorMessage(i18n.cls3301v00["cls3301v00.error02"]);
						}

						var start = $("#fromDate").val().split("-");
						var end = $("#endDate").val().split("-");
						var startDate = new Date(start[0], start[1], start[2]);
						var endData = new Date(end[0], end[1], end[2]);
						if (startDate > endData) {
							//cls3301v00.error03=起始日期不能大於結束日期
							return CommonAPI.showErrorMessage(i18n.cls3301v00["cls3301v00.error03"]);
						}

						if ($.trim($("#endDate").val()) != "" && $.trim($("#fromDate").val()) != "") {
							//cls3301v00.error04=日期區間不能大於一年
							if (parseInt(Math.abs(startDate - endData) / 1000 / 60 / 60 / 24) > 365) {
								return CommonAPI.showErrorMessage(i18n.cls3301v00["cls3301v00.error04"]);
							}
						}
						grid();
						$.thickbox.close();
					},
					"cancel": function() {
						$filterForm.reset();
						//grid();
						$.thickbox.close();
					}
				}
			});
		},
		openDoc: function(cellvalue, options, rowObject) {
			$.form.submit({
				url: rowObject.record_Url,
				target: "_blank",
				data: {
					lightID: rowObject.record_LightId,
					UserID: rowObject.record_UserID,
					RECORD_FILENAME: rowObject.record_FileName2
				}
			});
		}
	};
	$(function() {
		Action.openFilterBox();
		var grid = $("#gridview").iGrid({
			handler: 'cls3301gridhandler',
			height: 347,
			postData: {
				formAction: "query",
				mainDocStatus: viewstatus,
				rowNum: 15
			},
			rowNum: 15,
			colModel: [{
				colHeader: i18n.cls3301v00['cls3301v00.custId'],//'統編',
				name: 'custId',
				align: "center",
				sortable: false,
				width: 50
			}, {
				colHeader: i18n.cls3301v00['cls3301v00.record_Bran_Name'],//'分行',
				name: 'record_Bran_Name',
				align: "center",
				sortable: false,
				width: 50
			}, {
				colHeader: i18n.cls3301v00['cls3301v00.record_FileName'],//'錄音檔名',
				name: 'record_FileName',
				formatter: 'click',
				onclick: Action.openDoc,
				sortable: false,
				width: 100
			}, {
				colHeader: i18n.cls3301v00['cls3301v00.record_User_Code'],//'建檔人員',
				name: 'record_User_Name',
				align: "center",
				sortable: false,
				width: 50
			}, {
				colHeader: i18n.cls3301v00['cls3301v00.record_Create_Date'],//'建檔日期',
				name: 'record_Create_Date',
				align: "center",
				sortable: false,
				width: 80
			}, {
				colHeader: i18n.cls3301v00['cls3301v00.mgr_User_Code'],//'覆核主管',
				name: 'mgr_User_Name',
				align: "center",
				sortable: false,
				width: 50
			}, {
				colHeader: i18n.cls3301v00['cls3301v00.mgr_Sign_Date'],//'主管覆核日期',
				name: 'mgr_Sign_Date',
				align: "center",
				sortable: false,
				width: 80
			}, {
				colHeader: i18n.cls3301v00['cls3301v00.record_Url'],//"RECORD_URL",
				name: 'record_Url',
				hidden: true
			}, {
				colHeader: i18n.cls3301v00['cls3301v00.record_FileName'],//'錄音檔名但無副檔名',
				name: 'record_FileName2',
				hidden: true
			}, {
				name: 'record_UserID',
				hidden: true
			}, {
				name: 'record_LightId',
				hidden: true
			}]
			,
			ondblClickRow: function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				var data = $("#gridview").getRowData(rowid);
				Action.openDoc(null, null, data);
			}
		});
		$("#buttonPanel").find("#btnFilter").click(function() {
			Action.openFilterBox();
		})
	});

	function grid() {
		$("#gridview").jqGrid("setGridParam", {
			postData: $.extend($("#filterForm").serializeData(), {
				handler: Action.ghandle,
				formAction: $("#gridview").getGridParam("postData").formAction,
				docStatus: viewstatus,
				mainDocStatus: viewstatus,
				rowNum: 15
			})
		}).trigger("reloadGrid");
	}
});