/* 
 *MisELLNGTEEService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <pre>
 * 主從債務人檔 ELLNGTEE(MIS.ELV40101)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
public interface MisELLNGTEEService {

	/**
	 * 取得主從債務人資料
	 * 
	 * @param custId
	 *            客戶統一編號
	 * 
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            分行別
	 * @return
	 */
	public List<Map<String, Object>> getByCustIdCntrNo(String custId,
			String dupNo, String cntrNo);

	/**
	 * 取得 保證人資料
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @param brNo
	 *            分行別
	 * @return
	 */
	// @SuppressWarnings("rawtypes")
	// public Map findMisEllngtee(String custId, String dupNo, String brNo);

	/**
	 * 動審表 -主從債務人檔 ELLNGTEE 刪除該 分行號碼 、客戶編號、重覆序號、額度序號已存在的
	 * 
	 * @param brNo
	 *            分行別
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 */
	public void delEllngteeByUniqueKey(String brNo, String custId,
			String dupNo, String cntrNo);

	/**
	 * 動審表 -主從債務人檔 ELLNGTEE 新增
	 * 
	 * @param dataList
	 *            <pre>
	 *  brNo  分行別
	 *  custid  主債務人統編
	 *  dupNo 主債務人統編重複序號
	 *  cntrNo  額度序號
	 *  lngeFlag 相關身分
	 *  lngeId 從債務人統編
	 *  dupNo1 從債務人統編重複序號
	 *  lngenm 從債務人姓名
	 *  ntCode  國家別
	 *  lngere 與主債務人關係
	 *  upddt 更新日
	 *  updater 資料修改人
	 *  releasedt 董監事任期止日
	 *  grtamt 擔保限額
	 *  localid 當地客戶識別ID
	 * </pre>
	 */
	public void insert(List<Object[]> dataList);

	/**
	 * (企金)覆審報告表文件資訊-重新引進保證人
	 * 
	 * @param custId
	 *            客戶統一編號
	 * 
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            分行別
	 * @return
	 */
	// public List<Map<String, Object>> getRltGuarantorByCustIdCntrNo(
	// String custId, String dupNo, String cntrNo);

	/**
	 * (個金)查詢主從債務人
	 * 
	 * @param id
	 *            統編
	 * @param dupno
	 *            重覆序號
	 * @return
	 */
	public List<Map<String, Object>> findMisellngteeSelSell(String id,
			String dupno);

	/**
	 * 個金針對舊案引入之額度明細表，請於借保人頁面上方顯示訊息-->
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public List<Map<String, Object>> findByCntrNo(String cntrNo);

	/**
	 * (個金)查詢主從債務人引進內容
	 * 
	 * @param id
	 *            統編
	 * @param dupno
	 *            重覆序號
	 * @return
	 */
	public List<Map<String, Object>> findMisellngteeForSM(String brNo,
			String id, String dupno, String cntrNo);

	/**
	 * 查詢關聯戶
	 * 
	 * @param lgneId
	 * @param dupNo
	 * @param cancelDate
	 * @return
	 */
	public Set<String> findByLgneIdSet(String lgneId, String dupNo,
			String cancelDate);

	/**
	 * 查詢關聯戶
	 * 
	 * @param lgneId
	 * @param dupNo
	 * @param cancelDate
	 * @return
	 */
	public List<Map<String, Object>> findByLgneId(String lgneId, String dupNo,
			String cancelDate);

	/**
	 * 查詢關聯戶
	 * 
	 * @param custId
	 * @param dupNo
	 * @param changeDate
	 * @return
	 */
	// List<Map<String, Object>> findByCustId(String custId, String dupNo,
	// String cancelDate);

	/**
	 * 查詢關聯戶
	 * 
	 * @param Cntrno
	 * @return
	 */
	// public List<Map<String, Object>> findByCntrno(String cntrNo);

	/**
	 * 查詢關聯戶
	 * 
	 * @param custId
	 * @param dupNo
	 * @param changeDate
	 * @return
	 */
	List<Map<String, Object>> findByCustId2(String id, String cancelDate);

	/**
	 * 異常通報傳送從債務人資訊給卡務
	 * 
	 * @param id
	 * @param dupno
	 * @return
	 */
	public List<Map<String, Object>> findMisellngteeNotCancelAllLngeid(
			String id, String dupno);

	public List<Map<String, Object>> findByCrs(String id, String dupNo,
			String cntrNo);

	public List<Map<String, Object>> findByLrs(String id, String dupNo,
			String cntrNo);

	/**
	 * 抓 [C:共同借款人]
	 */
	public String get_lrs_Rlt_Borrower(String branch, String custId,
			String dupNo);

	/**
	 * 抓 [G:連帶保證人 N:ㄧ般保證人]
	 */
	public String get_lrs_Rlt_Guarantor(String branch, String custId,
			String dupNo);

	public void clearPriorfg(String brNo, String custId, String dupNo,
			String cntrNo);

	public void updatePriorfg(String brNo, String custId, String dupNo,
			String cntrNo, String lnGeId, String dupNo1, String lngeFlag,
			BigDecimal priorFg);

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> getAllByCustIdCntrNo(String custId,
			String dupNo, String cntrNo);

	/**
	 * RPAQueryEllngteeServiceImpl 使用
	 */
	public List<Map<String, Object>> findMisEllngteeNotCancelAllLngeidForRpa(
			String id, String dupno);

	/**
	 * G-113-0036 主從債務人，根據客戶、額度序號取得所有主從債務人資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 */
	public List<Map<String, Object>> getAllDataByCustIdCntrNo(String custId,
			String dupNo, String cntrNo);
}
