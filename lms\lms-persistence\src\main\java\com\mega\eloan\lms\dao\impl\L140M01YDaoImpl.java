
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140M01YDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140M01Y;

/** 額度特殊註記檔 **/
@Repository
public class L140M01YDaoImpl extends LMSJpaDao<L140M01Y, String>
	implements L140M01YDao {

	@Override
	public L140M01Y findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L140M01Y findByUk(String mainId, String refType, String refValue, String refModel, String refMainId, String refOid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refType", refType);
		search.addSearchModeParameters(SearchMode.EQUALS, "refValue", refValue);
		search.addSearchModeParameters(SearchMode.EQUALS, "refModel", refModel);
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refOid", refOid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140M01Y> findByMainIdOrderDefault(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//~~~
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("refType");
		search.addOrderBy("refValue");
		search.addOrderBy("refModel");
		search.addOrderBy("refMainId");
		search.addOrderBy("refOid");
		//~~~
		List<L140M01Y> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140M01Y> findByMainIdRefTypeOrderDefault(String mainId, String refType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refType", refType);
		//~~~
		search.setMaxResults(Integer.MAX_VALUE);
		search.addOrderBy("refType");
		search.addOrderBy("refValue");
		search.addOrderBy("refModel");
		search.addOrderBy("refMainId");
		search.addOrderBy("refOid");
		//~~~
		List<L140M01Y> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140M01Y> findByRef(String refType, String refMainId, String refOid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "refType", refType);
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refOid", refOid);
		List<L140M01Y> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140M01Y findByRefmainId(String refmainid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refmainid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public L140M01Y findByMainId_RefType_RefModel(String mainId, String refType, String refModel) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "refType", refType);
		search.addSearchModeParameters(SearchMode.EQUALS, "refModel", refModel);
		return findUniqueOrNone(search);
	}

}