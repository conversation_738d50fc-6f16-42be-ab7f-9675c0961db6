package com.mega.eloan.lms.lns.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.enums.CodeTypeEnum;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L160M01CDao;
import com.mega.eloan.lms.dao.L161S01ADao;
import com.mega.eloan.lms.dao.L161S01BDao;
import com.mega.eloan.lms.dao.L162S01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.pages.LMS1601M01Page;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.eloan.lms.model.L160M01D;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

/**
 * 產生動審表PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms1601r01rptservice")
public class LMS1601R01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1601R01RptServiceImpl.class);
	@Resource
	EloandbBASEService eloanDbService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	LMS1601Service service1601;

	@Resource
	BranchService branch;
	@Resource
	CodeTypeService codeTypeService;

	@Resource
	MisCustdataService misCustdataService;
	@Resource
	L162S01ADao l162s01aDao;

	@Resource
	L160M01CDao l160m01cDao;
	@Resource
	L161S01BDao l161s01bDao;
	@Resource
	L161S01ADao l161s01aDao;

	@Resource
	LMS1201Service service1201;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	LMS1401Service lms1401Service;

	@Resource
	LMSService lmsService;
	@Resource
	AMLRelateService amlRelateService;

	@Resource
	ICustomerService iCustomerService;

	private static ThreadLocal<DecimalFormat> dfMoney = new ThreadLocal<DecimalFormat>();
	private static ThreadLocal<DecimalFormat> dfRate = new ThreadLocal<DecimalFormat>();

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 */
	public OutputStream generateReport(PageParameters params) throws Exception {
		String mainId = params.getString("mainId");
		String type = params.getString("type", "R01");
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
		// 洗錢防制檢核表
		Map<InputStream, Integer> pdfNameMap5 = new LinkedHashMap<InputStream, Integer>();

		// 額度動用資訊一覽表
		Map<InputStream, Integer> pdfNameMap6 = new LinkedHashMap<InputStream, Integer>();

		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;
		Locale locale = null;
		Properties propEloanPage = null;
		int subLine = 1;
		Properties rptProperties = null;

		try {

			locale = LMSUtil.getLocale();
			rptProperties = MessageBundleScriptCreator
					.getComponentResource(LMS1601R01RptServiceImpl.class);
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			if ("R01".equals(type)) {
				outputStream = this
						.genLMS1601R01(mainId, locale, rptProperties);
			} else if ("R02".equals(type)) {
				outputStream = this.genLMS1601R02(params, mainId, locale,
						propEloanPage);
			} else if ("R03".equals(type)) {
				outputStream = this
						.genLMS1601R03(mainId, locale, rptProperties);
			} else if ("R04".equals(type)) {
				String kind = Util.trim(params.getString("kind"));
				outputStream = this.genLMS1601R04(mainId, locale,
						rptProperties, kind);
			}

			pdfNameMap.put(new ByteArrayInputStream(
					((ByteArrayOutputStream) outputStream).toByteArray()),
					subLine);

			if ("R01".equals(type)) {
				List<L120S09A> listL120s09a = amlRelateService
						.findL120s09aByMainIdWithOrder(mainId);
				if (listL120s09a != null && !listL120s09a.isEmpty()) {
					// 加印洗錢防制檢核表
					outputStream = this.genLMS1205R33(mainId, locale,
							"report/lns/LMS1201R33_" + locale.toString()
									+ ".rpt", rptProperties);
					// 列印頁次位置
					subLine = 8;
					pdfNameMap5.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);
				}

			}

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			if ("R01".equals(type)) {

				// 加印額度動用資訊一覽表
				outputStream = this.genLMS1601R05(mainId, locale,
						"report/lns/LMS1601R05_" + locale.toString() + ".rpt",
						rptProperties);
				// 列印頁次位置
				subLine = 8;

				if (outputStream != null) {
					pdfNameMap6.put(
							new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()), subLine);

				} else {
					// 都沒有空地貸款註記(舊案)，就不要列印
					pdfNameMap6 = null;
				}

			}

			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}
			if (pdfNameMap5 != null && pdfNameMap5.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap5, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}
			if (pdfNameMap6 != null && pdfNameMap6.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap6, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine);
				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));
			}

			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		} finally {
			if (list != null) {
				list.clear();
			}
			if (pdfNameMap != null) {
				pdfNameMap.clear();
			}
			// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
			if (pdfNameMap5 != null) {
				pdfNameMap5.clear();
			}

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			if (pdfNameMap6 != null) {
				pdfNameMap6.clear();
			}

		}
		return outputStream;
	}

	/**
	 * 先行動用待辦事項控制表
	 * 
	 * @param mainId
	 * @param locale
	 * @param rptProperties
	 * @return
	 * @throws Exception
	 */
	public OutputStream genLMS1601R04(String mainId, Locale locale,
			Properties rptProperties, String kind) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		OutputStream outputStream = null;
		ReportGenerator generator = new ReportGenerator(
				"report/lns/LMS1601R04_" + locale.toString() + ".rpt");

		try {
			dfMoney.set(new DecimalFormat("#,###,###,###,##0.##"));
			dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			rptVariableMap.put("queryDate",
					CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM));

			List<L160M01A> l160m01as = new ArrayList<L160M01A>();

			// add 個金動審表 by fantasy 2013/04/24
			if ("CLS".equals(kind)) {
				String[] docStatusArray = new String[] {
						CLSDocStatusEnum.已核准.getCode(),
						CLSDocStatusEnum.先行動用_已覆核.getCode() };
				l160m01as.addAll(service1601.findClsFirstUse(docStatusArray,
						user.getUnitNo()));
			} else {
				String[] docStatusArray = new String[] {
						CreditDocStatusEnum.海外_已核准.getCode(),
						CreditDocStatusEnum.先行動用_已覆核.getCode() };
				l160m01as.addAll(service1601.findFirstUse(docStatusArray,
						user.getUnitNo()));
			}

			titleRows = this.setL160M01ADataList(titleRows, locale, l160m01as);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	/**
	 * 設定L160M01A資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L160M01A List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL160M01ADataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L160M01A> list) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		int count = 1;
		L163S01A l163s01a = null;
		StringBuffer temp = new StringBuffer();
		for (L160M01A l160m01a : list) {
			l163s01a = l160m01a.getL163S01A();
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", String.valueOf(count));

			mapInTitleRows.put("ReportBean.column02",
					Util.getDate(l160m01a.getApproveTime()));

			mapInTitleRows.put("ReportBean.column03",
					Util.getDate(l163s01a.getWillFinishDate()));
			mapInTitleRows.put("ReportBean.column04", LMSUtil.concat(temp,
					Util.nullToSpace(l160m01a.getCustId()),
					Util.nullToSpace(l160m01a.getDupNo())));
			mapInTitleRows.put("ReportBean.column05",
					Util.nullToSpace(l160m01a.getCustName()));
			mapInTitleRows.put("ReportBean.column06",
					Util.nullToSpace(l163s01a.getWaitingItem()));
			mapInTitleRows.put("ReportBean.column07",
					Util.nullToSpace(l160m01a.getApprId()));
			mapInTitleRows.put("ReportBean.column08", Util
					.nullToSpace(userInfoService.getUserName(l160m01a
							.getApprId())));
			count++;
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

	/**
	 * 產生動審表PDF主從債務人資料表檔
	 * 
	 * @param mainId
	 * @param locale
	 * @param rptProperties
	 * @return
	 * @throws Exception
	 */
	public OutputStream genLMS1601R03(String mainId, Locale locale,
			Properties rptProperties) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		OutputStream outputStream = null;
		ReportGenerator generator = new ReportGenerator(
				"report/lns/LMS1601R03_" + locale.toString() + ".rpt");
		Map<String, CapAjaxFormResult> codeMap = null;
		// L160M01A．動用審核表主檔
		L160M01A l160m01a = null;
		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null) {
				locale = Locale.getDefault();
			}
			dfMoney.set(new DecimalFormat("#,###,###,###,##0.##"));
			dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));
			String[] codeType = { "Relation_type1", "Relation_type2",
					"Relation_type31", "Relation_type32", "lms1605s03_rType",
					CodeTypeEnum.國家代碼.getCode() };
			codeMap = codeTypeService.findByCodeType(codeType,
					locale.toString());
			l160m01a = service1601.findL160M01AByMaindId(mainId);
			List<L162S01A> l162s01as = l162s01aDao
					.findByMainIdOrderByGrid(mainId);

			String custId = l160m01a.getCustId();
			String dupNo = l160m01a.getDupNo();
			LinkedList<L162S01A> keyManList = new LinkedList<L162S01A>();
			LinkedList<L162S01A> otherList = new LinkedList<L162S01A>();
			for (L162S01A l162m01a : l162s01as) {
				// 當為主借款人排序要在前面
				if (l162m01a.getCustId().equals(custId)
						&& l162m01a.getDupNo().equals(dupNo)) {
					keyManList.add(l162m01a);
				} else {
					otherList.add(l162m01a);
				}
			}
			keyManList.addAll(otherList);
			titleRows = this.setL162S01ADataList(titleRows, locale, keyManList,
					codeMap);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	/**
	 * 設定主從債務人資料表檔 資料(L162S01A)
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L161S01B List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL162S01ADataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L162S01A> list, Map<String, CapAjaxFormResult> codeMap) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		int count = 1;
		StringBuffer temp = new StringBuffer();
		for (L162S01A l162s01a : list) {

			String custId = l162s01a.getCustId();
			String dupNo = l162s01a.getDupNo();
			LOGGER.info(custId + " " + dupNo + " " + l162s01a.getRType());

			Map<String, Object> map = iCustomerService.findByIdDupNo(custId,
					dupNo);

			// Map<String, Object> map =
			// misCustdataService.findCustdataSelCname(
			// custId, dupNo);
			if (map == null) {
				map = new HashMap<String, Object>();
			}
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", String.valueOf(count));
			mapInTitleRows.put(
					"ReportBean.column02",
					LMSUtil.concat(temp, custId, " ", dupNo, " ", "\r",
							Util.trim(map.get("CNAME"))));

			mapInTitleRows.put("ReportBean.column03",
					Util.nullToSpace(l162s01a.getCntrNo()));
			mapInTitleRows.put(
					"ReportBean.column04",
					LMSUtil.concat(temp, l162s01a.getRId(), " ",
							l162s01a.getRDupNo()));
			mapInTitleRows.put("ReportBean.column05",
					Util.nullToSpace(l162s01a.getRName()));

			String rKindD = Util.trim(l162s01a.getRKindD());
			switch (Util.parseInt(l162s01a.getRKindM())) {
			case 1:

				mapInTitleRows.put("ReportBean.column06", rKindD + " "
						+ codeMap.get("Relation_type1").get(rKindD));
				break;
			case 2:
				mapInTitleRows.put(
						"ReportBean.column06",
						rKindD
								+ " "
								+ Util.trim(codeMap.get("Relation_type2").get(
										rKindD)));
				break;
			case 3:
				char[] kind = rKindD.toCharArray();
				String kind1 = (String) codeMap.get("Relation_type31").get(
						String.valueOf(kind[0]));
				String kind2 = (String) codeMap.get("Relation_type32").get(
						String.valueOf(kind[1]));
				mapInTitleRows.put("ReportBean.column06",
						LMSUtil.concat(temp, rKindD, " ", kind1, " - ", kind2));
				break;

			}
			mapInTitleRows.put(
					"ReportBean.column07",
					(String) codeMap.get(CodeTypeEnum.國家代碼.getCode()).get(
							l162s01a.getRCountry()));
			String rtype = Util.trim(l162s01a.getRType());
			// 相關身分
			mapInTitleRows.put("ReportBean.column08", rtype + " "
					+ codeMap.get("lms1605s03_rType").get(rtype));

			mapInTitleRows.put("ReportBean.column09",
					Util.getDate(l162s01a.getDueDate()));

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			mapInTitleRows.put("ReportBean.column10",
					Util.trim(l162s01a.getPriority()));
			mapInTitleRows
					.put("ReportBean.column11",
							Util.equals(Util.trim(l162s01a.getGuaNaExposure()),
									"") ? ""
									: (Util.equals(Util.trim(l162s01a
											.getGuaNaExposure()), "Y") ? prop
											.getProperty("yes") : prop
											.getProperty("no")));
			count++;
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

	/**
	 * 列印 動審表 - 聯貸案參貸比率一覽表
	 * 
	 * @param mainId
	 * @param locale
	 * @param rptProperties
	 * @return
	 * @throws Exception
	 */
	public OutputStream genLMS1601R02(PageParameters params, String mainId,
			Locale locale, Properties rptProperties) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = null;
		OutputStream outputStream = null;
		ReportGenerator generator = new ReportGenerator(
				"report/lns/LMS1601R02_" + locale.toString() + ".rpt");
		List<InputStream> list = new LinkedList<InputStream>();
		Properties propEloanPage = null;
		propEloanPage = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		int subLine = 1;
		String rptOid = Util.nullToSpace(params.getString("uids"));
		String mode = Util.nullToSpace(params.getString("mode"));
		String[] dataSplit = rptOid.split(",");
		for (String uid : dataSplit) {
			// L160M01A．動用審核表主檔
			L160M01A l160m01a = service1601.findL160M01AByMaindId(mainId);
			// L160M01B．動審表額度序號資料
			List<L161S01B> l161s01bList = null;
			try {
				titleRows = new LinkedList<Map<String, String>>();

				locale = LocaleContextHolder.getLocale();
				if (locale == null) {
					locale = Locale.getDefault();
				}

				dfMoney.set(new DecimalFormat("#,###,###,###,##0.##"));
				dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));

				L161S01A l161s01a = service1601.findL161m01aByMainIdUid(mainId,
						uid);
				l161s01bList = l161s01a.getL161s01bs();

				if (l161s01bList != null && !l161s01bList.isEmpty()) {

					if (Util.equals(mode, "OLD")) {

						// 案號

						rptVariableMap.put("CASENO",
								Util.nullToSpace(l160m01a.getCaseNo()));

						rptVariableMap.put("GIST",
								Util.nullToSpace(l161s01a.getGist()));
						rptVariableMap.put("CURR",
								Util.nullToSpace(l161s01a.getQuotaCurr()));
						rptVariableMap.put("QUERYEDATE",
								addThousand(l161s01a.getQuotaAmt()));
						rptVariableMap.put("CASEDATE",
								prop.getProperty("L160M01A.signDate") + "："
										+ this.getDate(l161s01a.getSignDate()));

						LOGGER.info(l161s01a.getCntrNo());

						rptVariableMap.put("CNTRNO",
								Util.nullToSpace(l161s01a.getCntrNo()));

						titleRows = this.setL161S01BDataList(titleRows, locale,
								l161s01bList, rptProperties);

					} else {
						if (Util.equals(l161s01a.getUnitMega(), "Y")
								|| Util.equals(l161s01a.getUnitCase(), "Y")) {
							// 案號

							rptVariableMap.put("CASENO",
									Util.nullToSpace(l160m01a.getCaseNo()));

							if (Util.equals(l161s01a.getCoBank(), "Y")) {
								rptVariableMap.put("GIST",
										Util.nullToSpace(l161s01a.getGist()));
								rptVariableMap.put("CURR", Util
										.nullToSpace(l161s01a.getQuotaCurr()));
								rptVariableMap.put("QUERYEDATE",
										addThousand(l161s01a.getQuotaAmt()));
								rptVariableMap.put(
										"CASEDATE",
										prop.getProperty("L160M01A.signDate")
												+ "："
												+ this.getDate(l161s01a
														.getSignDate()));
							} else {
								rptVariableMap.put("GIST", "");
								rptVariableMap.put("CURR", Util
										.nullToSpace(l161s01a
												.getCurrentApplyCurr()));
								rptVariableMap.put("QUERYEDATE",
										addThousand(l161s01a
												.getCurrentApplyAmt()));
								rptVariableMap.put("CASEDATE", "");
							}

							LOGGER.info(l161s01a.getCntrNo());

							rptVariableMap.put("CNTRNO",
									Util.nullToSpace(l161s01a.getCntrNo()));

							titleRows = this.setL161S01BDataList(titleRows,
									locale, l161s01bList, rptProperties);

						}
					}

				}
				generator.setLang(locale);
				generator.setVariableData(rptVariableMap);
				generator.setRowsData(titleRows);

				outputStream = generator.generateReport();

				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));

				// pdfNameMap.put(new ByteArrayInputStream(
				// ((ByteArrayOutputStream) outputStream).toByteArray()),
				// subLine);

			} finally {
				if (rptVariableMap != null) {
					rptVariableMap.clear();
				}
			}
		}

		// if (pdfNameMap != null && pdfNameMap.size() > 0) {
		// outputStream = new ByteArrayOutputStream();
		// PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
		// propEloanPage.getProperty("PaginationText"), true, locale,
		// subLine);
		// list.add(new ByteArrayInputStream(
		// ((ByteArrayOutputStream) outputStream).toByteArray()));
		// }

		// outputStream = new ByteArrayOutputStream();
		// PdfTools.mergeReWritePagePdf(list, outputStream);

		PdfTools.mergeReWritePagePdf(list, outputStream);

		return outputStream;
	}

	/**
	 * 設定L161S01B資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L161S01B List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL161S01BDataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L161S01B> list, Properties prop) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		int count = 1;
		StringBuffer temp = new StringBuffer();
		for (L161S01B l161s01b : list) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", String.valueOf(count));
			setBrankName(mapInTitleRows, l161s01b, temp);
			mapInTitleRows.put("ReportBean.column04", UtilConstants.DEFAULT.是
					.equals(l161s01b.getSlMaster()) ? prop.getProperty("yes")
					: "");
			mapInTitleRows.put("ReportBean.column05",
					Util.nullToSpace(l161s01b.getSlAccNo()));
			mapInTitleRows.put("ReportBean.column06",
					this.addThousand(l161s01b.getSlAmt()));
			count++;
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

	/**
	 * 處理銀行名稱顯示
	 * 
	 * @param mapInTitleRows
	 * @param l161s01b
	 *            聯貸案參貸比率一覽表明細檔
	 * @param temp
	 *            暫存文字用
	 * @return
	 */
	private Map<String, String> setBrankName(
			Map<String, String> mapInTitleRows, L161S01B l161s01b,
			StringBuffer temp) {

		switch (Util.parseInt(l161s01b.getSlBankType())) {
		case 1:
		case 2:
			// mapInTitleRows.put(
			// "ReportBean.column02",
			// temp.append(l161s01b.getSlBank()).append(" ")
			// .append(l161s01b.getSlBankCN()).toString());
			// temp.setLength(0);
			// mapInTitleRows.put(
			// "ReportBean.column03",
			// temp.append(l161s01b.getSlBranch()).append(" ")
			// .append(l161s01b.getSlBranchCN()).toString());
			// temp.setLength(0);

			String str1 = new StringBuffer(l161s01b.getSlBank()).append(" ")
					.append(l161s01b.getSlBankCN()).toString();

			String str2 = new StringBuffer(l161s01b.getSlBranch()).append(" ")
					.append(l161s01b.getSlBranchCN()).toString();

			if (Util.notEquals(Util.trim(str1), "")) {
				temp.append(str1);
			}

			if (Util.notEquals(Util.trim(str2), "")) {
				if (Util.equals(Util.trim(temp.toString()), "")) {
					temp.append(str2);
				} else {
					char[] newLine = { 13, 10 };
					temp.append(newLine).append(str2);
				}
			}
			mapInTitleRows.put("ReportBean.column07", temp.toString());
			temp.setLength(0);

			break;
		case 3:
		case 4:
		case 5:
		case 6:
		case 7:
		case 8:
		case 9:
		case 10:
		case 11:
		case 12:
		case 99:
			// mapInTitleRows.put(
			// "ReportBean.column02",
			// temp.append(l161s01b.getSlBank()).append(" ")
			// .append(l161s01b.getSlBankCN()).toString());
			// temp.setLength(0);
			// mapInTitleRows.put("ReportBean.column03", "");

			mapInTitleRows.put(
					"ReportBean.column07",
					temp.append(l161s01b.getSlBank()).append(" ")
							.append(l161s01b.getSlBankCN()).toString());
			temp.setLength(0);
			break;

		}

		return mapInTitleRows;
	}

	/**
	 * 轉換成千分位
	 * 
	 * @param object
	 *            將數字塞入千分位
	 * @return String String
	 */
	private String addThousand(Object object) {
		return object == null ? "0" : dfMoney.get().format(object);
	}

	/**
	 * 產生LMS1601R01的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public OutputStream genLMS1601R01(String mainId, Locale locale,
			Properties rptProperties) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

		OutputStream outputStream = null;
		// reportTools.setTestMethod(true);
		LOGGER.info("into setReportData");
		Properties prop = null;
		prop = MessageBundleScriptCreator
				.getComponentResource(LMS1201R01RptServiceImpl.class);
		Properties prop1601 = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		// L160M01A．動用審核表主檔
		L160M01A l160m01a = null;
		// L160M01B．動審表額度序號資料
		List<L160M01B> l160m01bList = null;
		// L160M01C．動審表查核項目資料
		List<L160M01C> l160m01cList = new ArrayList<L160M01C>();

		L163S01A l163s01a = null;
		String branchName = null;
		List<Map<String, Object>> custNameList = null;

		l160m01a = service1601.findL160M01AByMaindId(mainId);
		if (l160m01a == null)
			l160m01a = new L160M01A();

		String isSmallBuss = Util.trim(l160m01a.getIsSmallBuss());
		// J-112-0148 疫後振興
		boolean isRescueOnlyCaseF = lmsService.isRescueOnlyCaseF(l160m01a);
		boolean isRescueOnlyCaseJ03HeadItem1 = lmsService.isRescueOnlyCaseJ03HeadItem1(l160m01a);
		String flag = "";
		if (isRescueOnlyCaseF) {
			flag = "_F";
		} else if (Util.equals(isSmallBuss, "Y")) {
			flag = "_C";
		} else if (isRescueOnlyCaseJ03HeadItem1) {
			flag = "_J03";
		}

		ReportGenerator generator = new ReportGenerator("report/lns/LMS1601R01"
				+ flag + "_" + locale.toString() + ".rpt");

		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();

			l163s01a = service1601.findL163m01aByMainId(mainId);
			if (l163s01a == null)
				l163s01a = new L163S01A();

			// branchName = Util.nullToSpace(branch.getBranchName(Util
			// .nullToSpace(l160m01a.getCaseBrId())));

			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l160m01a.getOwnBrId())));

			l160m01bList = (List<L160M01B>) service1601.findListByMainId(
					L160M01B.class, l160m01a.getMainId());
			List<L160M01C> l160m01cList1 = l160m01cDao.findByIndex01(mainId,
					UtilConstants.Usedoc.itemType.國內企金, null);
			for (L160M01C l160m01c : l160m01cList1) {
				l160m01cList.add(l160m01c);
			}
			List<L160M01C> l160m01cList2 = l160m01cDao.findByIndex01(mainId,
					UtilConstants.Usedoc.itemType.自訂項目, null);

			for (L160M01C l160m01c : l160m01cList2) {
				l160m01cList.add(l160m01c);
			}

			custNameList = eloanDbService.findL140M01AByMainId(l160m01a
					.getMainId());
			// 分行名稱
			rptVariableMap.put("BRANCHNAME", branchName);

			rptVariableMap.put("SMALLBUSSCHKLISTDATE", "");
			if (Util.equals(Util.trim(l160m01a.getIsSmallBuss()), "Y")) {
				if (l160m01a.getSmallBussChkListDate() != null) {
					rptVariableMap.put("SMALLBUSSCHKLISTDATE", CapDate
							.formatDate(l160m01a.getSmallBussChkListDate(),
									"yyyy-MM-dd"));
				}

			}

			rptVariableMap = this.setL160M01AData(rptVariableMap, l160m01a,
					prop, prop1601);

			if (UtilConstants.DEFAULT.是.equals(l160m01a.getAllCanPay())) {
				// L160M01A.message3=簽報書項下額度明細表全部動用
				rptVariableMap.put("L160M01B.CNTRNO",
						prop1601.getProperty("L160M01A.message3"));
			} else {
				rptVariableMap = this.setL160M01BData(rptVariableMap,
						l160m01bList);
			}
			rptVariableMap = this.setL163S01AData(rptVariableMap, l163s01a,
					l160m01a);
			rptVariableMap = this.setL160M01ACustNameData(rptVariableMap,
					custNameList, l160m01a);

			if (Util.equals(isSmallBuss, "Y") || isRescueOnlyCaseF || isRescueOnlyCaseJ03HeadItem1) {
				titleRows = this.setL160M01CDataList_TypeC(titleRows,
						l160m01cList, prop);
			} else {
				titleRows = this.setL160M01CDataList(titleRows, l160m01cList,
						prop);
			}

			// J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表
			String ivrString = "錄音檔：<br>";
			boolean ivrFlag = false;
			for (L160M01B l160m01b : l160m01bList) {
				if (!Util.isEmpty(l160m01b.getIVRFlag())) {
					String reMainId = l160m01b.getReMainId();
					L140M01A l140m01a = lms1401Service
							.findL140m01aByMainId(reMainId);
					String custName = l140m01a.getCustName();
					ivrString += Util.trim(custName + " "
							+ l160m01b.getIVRFlag())
							+ "<br>";
					ivrFlag = true;
				}
			}
			if (ivrFlag) {
				rptVariableMap.put("ivrFlag", ivrString);
			} else {
				rptVariableMap.put("ivrFlag", null);
			}

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;

	}

	/**
	 * 塞入變數MAP資料使用(L160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL160M01ACustNameData(
			Map<String, String> rptVariableMap, List<Map<String, Object>> list,
			L160M01A l160m01a) {
		StringBuffer str = new StringBuffer();
		HashMap<String, String> temp = new HashMap<String, String>();
		String custName = "";
		String mainCustName = "";
		for (Map<String, Object> map : list) {
			custName = Util.trim(map.get("CUSTNAME"));
			if (temp.containsKey(custName)) {
				continue;
			} else {
				temp.put(custName, "");
			}
			if (custName.equals(Util.trim(l160m01a.getCustName()))) {
				mainCustName = custName;
			} else {
				str.append(str.length() > 0 ? "、" : "");
				str.append(custName);
			}
		}

		if (Util.equals(mainCustName, "")) {
			// 有可能比對不到，因為裡面有無法辨識的亂碼字元
			rptVariableMap.put(
					"L160M01A.CUSTNAME",
					mainCustName + (str.length() > 0 ? "" : "")
							+ str.toString());
		} else {
			rptVariableMap.put(
					"L160M01A.CUSTNAME",
					mainCustName + (str.length() > 0 ? "、" : "")
							+ str.toString());
		}

		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL160M01AData(
			Map<String, String> rptVariableMap, L160M01A l160m01a,
			Properties prop, Properties prop1601) {
		rptVariableMap.put("L160M01A.USETYPE",
				Util.nullToSpace(l160m01a.getUseType()));
		rptVariableMap.put("L160M01A.RANDOMCODE",
				Util.nullToSpace(l160m01a.getRandomCode()));
		rptVariableMap.put("L160M01A.CASENO",
				Util.nullToSpace(l160m01a.getCaseNo()));
		// rptVariableMap.put("L160M01A.CUSTNAME",
		// Util.nullToSpace(l160m01a.getCustName()));

		rptVariableMap.put(
				"L160M01A.TTYPE",
				" "
						+ prop1601.getProperty("L160M01A.tType"
								+ l160m01a.getTType()) + " ");

		rptVariableMap.put(
				"L160M01A.GUDATE",
				l160m01a.getGuFromDate() != null
						|| l160m01a.getGuEndDate() != null ? this
						.getDate(l160m01a.getGuFromDate())
						+ "~"
						+ this.getDate(l160m01a.getGuEndDate()) : "");
		rptVariableMap.put("L160M01A.GUFORMDATE",
				this.getDate(l160m01a.getGuFromDate()));
		rptVariableMap.put("L160M01A.GUENDDATE",
				this.getDate(l160m01a.getGuEndDate()));
		rptVariableMap.put("L160M01A.GUCURR",
				Util.nullToSpace(l160m01a.getGuCurr()) + " ");
		rptVariableMap.put("L160M01A.GUAMT",
				NumConverter.addComma(l160m01a.getGuAmt()));
		rptVariableMap.put(
				"L160M01A.SIGNDATE",
				l160m01a.getSignDate() != null ? this.getDate(l160m01a
						.getSignDate()) : "");

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容

		rptVariableMap.put("L160M01A.TTYPE_ORG", l160m01a.getTType());

		String useSelect = l160m01a.getUseSelect();
		String useString = "";
		String lnString = "";
		String lnSelect = l160m01a.getLnSelect();

		if (Util.notEquals(Util.trim(l160m01a.getTType()), "3")) {

			if (!Util.isEmpty(useSelect)) {
				// 動用期限判斷
				switch (Integer.valueOf(useSelect)) {
				case 1:
					useString = this.getDate(l160m01a.getUseFromDate()) + " ~ "
							+ this.getDate(l160m01a.getUseEndDate());
					break;
				case 2:

					// L160M01A.useMonth1=自首動日起
					// L160M01A.useMonth2=個月
					useString = prop1601.getProperty("L160M01A.useMonth1")
							+ " " + Util.trim(l160m01a.getUseMonth()) + " "
							+ prop1601.getProperty("L160M01A.useMonth2");
					break;
				case 3:
					useString = l160m01a.getUseOther();
					break;
				}
			}
			rptVariableMap.put("L160M01A.USEFORMDATE", useString);

			// 授信期限判斷，當取得授信契約書期別 為中長期
			if (!Util.isEmpty(lnSelect) && "2".equals(l160m01a.getTType())) {
				switch (Integer.valueOf(lnSelect)) {
				case 1:
					lnString = this.getDate(l160m01a.getLnFromDate()) + " ~ "
							+ this.getDate(l160m01a.getLnEndDate());
					break;
				case 2:
					// L160M01A.useMonth1=自首動日起
					// L160M01A.lnYear=年
					// L160M01A.useMonth2=個月
					lnString = prop1601.getProperty("L160M01A.useMonth1") + " "
							+ Util.trim(l160m01a.getLnYear()) + " "
							+ prop1601.getProperty("L160M01A.lnYear") + " "
							+ Util.trim(l160m01a.getLnMonth()) + " "
							+ prop1601.getProperty("L160M01A.useMonth2");
					break;
				case 3:
					lnString = l160m01a.getLnOther();
					break;
				}
			}
			rptVariableMap.put("L160M01A.LNDATE", lnString);
			rptVariableMap.put("L160M01A.LNFORMDATE",
					this.getDate(l160m01a.getLnFromDate()));
			rptVariableMap.put("L160M01A.LNENDDATE",
					this.getDate(l160m01a.getLnEndDate()));
		} else {
			// 詳額度動用資訊一覽表
			rptVariableMap.put("L160M01A.USEFORMDATE", "");
			rptVariableMap.put("L160M01A.LNDATE", "");
			rptVariableMap.put("L160M01A.LNFORMDATE", "");
			rptVariableMap.put("L160M01A.LNENDDATE", "");
		}

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		rptVariableMap.put("L160M01A.BLACKLISTTXTOK", "");
		rptVariableMap.put("L160M01A.BLACKDATADATE", "");
		if (Util.notEquals(Util.trim(l160m01a.getBlackListTxtOK()), "")) {
			rptVariableMap.put("L160M01A.BLACKLISTTXTOK",
					Util.nullToSpace(l160m01a.getBlackListTxtOK()));
			rptVariableMap.put("L160M01A.BLACKDATADATE",
					this.getDate(l160m01a.getBlackDataDate()));
		} else {

			List<L120S09A> listL120s09a = amlRelateService
					.findL120s09aByMainId(l160m01a.getMainId());
			// 有AML/CFT檢核表
			if (listL120s09a != null && !listL120s09a.isEmpty()) {
				Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
				Map<String, String> queryCustMap = new HashMap<String, String>();
				ArrayList<String> mainIds = new ArrayList<String>();
				for (L160M01B l160m01b : l160m01bs) {
					mainIds.add(l160m01b.getReMainId());
				}

				List<L140M01A> l140m01as = lms1401Service
						.findL140m01aListByMainIdList(mainIds
								.toArray(new String[mainIds.size()]));
				for (L140M01A l140m01a : l140m01as) {
					String custId = Util.trim(l140m01a.getCustId());
					String dupNo = Util.trim(l140m01a.getDupNo());
					String custName = Util.trim(l140m01a.getCustName());
					String key = custId + "-" + dupNo;
					queryCustMap.put(key, custName);
				}

				StringBuilder temp = new StringBuilder("");
				String blackDataDate = "";
				for (String key : queryCustMap.keySet()) {
					String[] custKey = key.split("-");
					String tCustId = custKey[0];
					String tDupNo = custKey[1];
					// 新案
					listL120s09a = amlRelateService.findListL120s09aByCustId(
							l160m01a.getMainId(), tCustId, tDupNo);

					if (listL120s09a != null && !listL120s09a.isEmpty()) {
						for (L120S09A l120s09a : listL120s09a) {

							// Properties prop1605 = MessageBundleScriptCreator
							// .getComponentResource(LMS1605M01Page.class);

							blackDataDate = Util.trim(TWNDate.toAD(l120s09a
									.getQueryDateS()));

							String custId = l120s09a.getCustId().toUpperCase();
							String dupNo = l120s09a.getDupNo();
							String eName = l120s09a.getCustEName();
							String cName = Util.trim(l120s09a.getCustName());

							if (Util.notEquals(l120s09a.getBlackListCode(), "")) {
								String msg = "";
								if (Util.equals(
										l120s09a.getBlackListCode(),
										UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
									// L160M01A.message39=未列於黑名單
									msg = prop1601
											.getProperty("L160M01A.message39");

								} else if (Util
										.equals(l120s09a.getBlackListCode(),
												UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
									// L160M01A.message107=是黑名單
									msg = prop1601
											.getProperty("L160M01A.message107");

								} else if (Util
										.equals(l120s09a.getBlackListCode(),
												UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
									// L160M01A.message41=可能是黑名單
									msg = prop1601
											.getProperty("L160M01A.message41");

								}

								temp.append(temp.length() > 0 ? "\r" : "");
								temp.append(custId).append(" ").append(dupNo)
										.append(" ").append(cName);
								if (Util.isNotEmpty(eName)) {
									temp.append("【").append(eName).append("】 ");
								}

								temp.append(" ").append(msg);
							}

							break;
						}
					}

				}

				if (Util.notEquals(Util.trim(temp.toString()), "")) {
					// 資料查詢日期：
					rptVariableMap.put("L160M01A.BLACKDATADATE", blackDataDate);
					rptVariableMap.put("L160M01A.BLACKLISTTXTOK",
							Util.nullToSpace(temp.toString()));
				}

			}

		}

		rptVariableMap.put("L160M01A.COMM",
				Util.nullToSpace(l160m01a.getComm()));
		rptVariableMap.put("L160M01A.SIGN",
				Util.nullToSpace(l160m01a.getSign()));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (!Util.isEmpty(l160m01a.getL160M01D())
				&& !l160m01a.getL160M01D().isEmpty()) {
			String appid = "";
			String recheckid = "";
			String managerid = "";
			String mainAppid = "";
			String mainRecheckid = "";
			// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
			StringBuilder bossId = new StringBuilder("");
			for (L160M01D l160m01d : l160m01a.getL160M01D()) {
				// 要加上人員代碼
				String type = Util.trim(l160m01d.getStaffJob());
				String userId = Util.trim(l160m01d.getStaffNo());
				String value = Util.trim(this.getUserName(userId));
				if ("L1".equals(type)) {
					appid = value;
				} else if ("L3".equals(type)) {
					bossId.append(bossId.length() > 0 ? "<br/>" : "");
					bossId.append(value);
				} else if ("L4".equals(type)) {
					recheckid = value;

				} else if ("L5".equals(type)) {
					managerid = value;

				} else if ("L6".equals(type)) {
					mainAppid = value;

				} else if ("L7".equals(type)) {
					mainRecheckid = value;

				}
			}

			if (Util.isEmpty(appid)) {
				appid = this.getUserName(user.getUserId());
			}
			if (Util.isEmpty(recheckid)) {
				recheckid = this.getUserName(l160m01a.getReCheckId());
			}
			rptVariableMap.put("L160M01A.APPRID", appid);
			rptVariableMap.put("L160M01A.BOSSID", bossId.toString());
			rptVariableMap.put("L160M01A.RECHECKID", recheckid);
			rptVariableMap.put("L160M01A.MANAGERID", managerid);
			// 總行經辦
			rptVariableMap.put("L160M01A.MAINAPPID", mainAppid);
			// 總行覆核
			rptVariableMap.put("L160M01A.MAINRECHECKID", mainRecheckid);
		} else {

			String apprid = l160m01a.getApprId();
			if (Util.isEmpty(apprid)) {
				apprid = user.getUserId();
			}
			String userName = this.getUserName(apprid);
			rptVariableMap.put("L160M01A.APPRID", userName);
			rptVariableMap.put("L160M01A.BOSSID", "");
			rptVariableMap.put("L160M01A.RECHECKID", "");
			rptVariableMap.put("L160M01A.MANAGERID", "");
			// 總行經辦
			rptVariableMap.put("L160M01A.MAINAPPID", "");
			// 總行覆核
			rptVariableMap.put("L160M01A.MAINRECHECKID", "");

		}
		rptVariableMap.put("L160M01A.CASEDATE",
				this.getDate(l160m01a.getCaseDate()));

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// rptVariableMap.put("L160M01A.BLACKDATADATE",
		// this.getDate(l160m01a.getBlackDataDate()));

		// 共同行銷
		rptVariableMap.put("L160M01A.JOINMARKETINGDATE",
				this.getDate(l160m01a.getJoinMarketingDate()));
		rptVariableMap.put("L160M01A.JOINMARKETING",
				Util.trim(l160m01a.getJoinMarketing()));
		return rptVariableMap;

	}

	/**
	 * 塞入變數MAP資料使用(L160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setL163S01AData(
			Map<String, String> rptVariableMap, L163S01A l163s01a,
			L160M01A l160m01a) {
		rptVariableMap.put("L163S01A.WAITINGITEM",
				Util.nullToSpace(l163s01a.getWaitingItem()));
		rptVariableMap.put("L163S01A.TWILLFINISHDATE",
				this.getDate(l163s01a.getWillFinishDate()));
		rptVariableMap.put("L163S01A.FINISHDATE",
				this.getDate(l163s01a.getFinishDate()));
		rptVariableMap.put("L163S01A.ITEMTRACE",
				Util.nullToSpace(l163s01a.getItemTrace()));
		// managerid為0時表示自行輸入
		String managerid = "";
		if ("0".equals(l163s01a.getManagerId())) {
			managerid = l163s01a.getManagerNm();
		} else {
			managerid = this.getUserName(Util.nullToSpace(l163s01a
					.getManagerId()));
		}
		rptVariableMap.put("L163S01A.MANAGERID", managerid);
		rptVariableMap.put("L163S01A.BOSSID",
				this.getUserName(Util.nullToSpace(l163s01a.getBossId())));

		String apprid = l163s01a.getAppraiserId();
		if (Util.isEmpty(apprid)) {
			apprid = l160m01a.getApprId();
		}

		String appId = this.getUserName(Util.nullToSpace(apprid));
		if (Util.isEmpty(appId)) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			appId = this.getUserName(Util.nullToSpace(user.getUserId()));
		}

		rptVariableMap.put("L163S01A.APPRAISERID", appId);
		return rptVariableMap;
	}

	/**
	 * 取得日期(XXXX-XX-XX)
	 * 
	 * @param date
	 *            日期
	 * @return 日期
	 */
	private String getDate(Date date) {
		String str = null;
		if (date == null) {
			str = "";
		} else {
			str = TWNDate.toAD(date);
		}
		return str;
	}

	/**
	 * 設定L160M01B資料
	 * 
	 * @param rptVariableMap
	 *            javabeanMap
	 * @param list
	 *            L160M01B List
	 * @return rptVariableMap javabeanMap
	 */
	private Map<String, String> setL160M01BData(
			Map<String, String> rptVariableMap, List<L160M01B> list) {
		int count = 1;
		StringBuffer str = new StringBuffer();
		for (L160M01B l160m01b : list) {
			str.append(Util.nullToSpace(l160m01b.getCntrNo()).replace("　", " ")
					.trim());
			if (count != list.size()) {
				str.append("、");
			}
			count++;
		}
		rptVariableMap.put("L160M01B.CNTRNO", str.toString());
		return rptVariableMap;
	}

	/**
	 * 設定L160M01C資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L160M01C List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL160M01CDataList(
			List<Map<String, String>> titleRows, List<L160M01C> list,
			Properties prop) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column07", "F");
		titleRows.add(mapInTitleRows);
		int count = 1;
		boolean result = true;
		for (L160M01C l160m01c : list) {
			if (!Util.isEmpty(Util.trim(l160m01c.getItemContent()))) {
				if (count % 3 == 1) {
					result = false;
					mapInTitleRows = Util.setColumnMap();
					mapInTitleRows.put("ReportBean.column07", "Y");
					mapInTitleRows.put("ReportBean.column01",
							this.getItemCheck(l160m01c.getItemCheck(), prop));
					mapInTitleRows.put("ReportBean.column02", count + "."
							+ l160m01c.getItemContent());
				} else if (count % 3 == 2) {
					result = false;
					mapInTitleRows.put("ReportBean.column03",
							this.getItemCheck(l160m01c.getItemCheck(), prop));
					mapInTitleRows.put("ReportBean.column04", count + "."
							+ l160m01c.getItemContent());
				} else if (count % 3 == 0) {
					result = true;
					mapInTitleRows.put("ReportBean.column05",
							this.getItemCheck(l160m01c.getItemCheck(), prop));
					mapInTitleRows.put("ReportBean.column06", count + "."
							+ l160m01c.getItemContent());
					titleRows.add(mapInTitleRows);
				}
				count++;
			}
		}
		if (!result) {
			titleRows.add(mapInTitleRows);
		}
		if (titleRows.size() == 1) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column07", "Y");
			titleRows.add(mapInTitleRows);
		}
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column07", "N");
		titleRows.add(mapInTitleRows);
		return titleRows;
	}

	/**
	 * 設定L160M01C資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L160M01C List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL160M01CDataList_TypeC(
			List<Map<String, String>> titleRows, List<L160M01C> list,
			Properties prop) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column07", "F");
		titleRows.add(mapInTitleRows);
		int count = 1;
		boolean result = true;
		for (L160M01C l160m01c : list) {
			if (!Util.isEmpty(Util.trim(l160m01c.getItemContent()))) {

				result = true;
				mapInTitleRows = Util.setColumnMap();
				mapInTitleRows.put("ReportBean.column07", "Y");

				mapInTitleRows.put("ReportBean.column01",
						this.getItemCheck(l160m01c.getItemCheck(), prop));

				// mapInTitleRows.put("ReportBean.column01", "");

				mapInTitleRows.put("ReportBean.column02", count + "."
						+ l160m01c.getItemContent());
				titleRows.add(mapInTitleRows);

				// if (count % 3 == 1) {
				// result = false;
				// mapInTitleRows = Util.setColumnMap();
				// mapInTitleRows.put("ReportBean.column07", "Y");
				// mapInTitleRows.put("ReportBean.column01",
				// this.getItemCheck(l160m01c.getItemCheck(), prop));
				// mapInTitleRows.put("ReportBean.column02", count + "."
				// + l160m01c.getItemContent());
				// } else if (count % 3 == 2) {
				// result = false;
				// mapInTitleRows.put("ReportBean.column03",
				// this.getItemCheck(l160m01c.getItemCheck(), prop));
				// mapInTitleRows.put("ReportBean.column04", count + "."
				// + l160m01c.getItemContent());
				// } else if (count % 3 == 0) {
				// result = true;
				// mapInTitleRows.put("ReportBean.column05",
				// this.getItemCheck(l160m01c.getItemCheck(), prop));
				// mapInTitleRows.put("ReportBean.column06", count + "."
				// + l160m01c.getItemContent());
				// titleRows.add(mapInTitleRows);
				// }
				count++;
			}
		}
		if (!result) {
			titleRows.add(mapInTitleRows);
		}
		if (titleRows.size() == 1) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column07", "Y");
			titleRows.add(mapInTitleRows);
		}
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column07", "N");
		titleRows.add(mapInTitleRows);
		return titleRows;
	}

	/**
	 * 顯示需要呈現ITEMCHECK的資料
	 * 
	 * @param itemCheck
	 *            itemCheck
	 * @return 呈現到報表文字
	 */
	private String getItemCheck(String itemCheck, Properties prop) {
		String str = null;
		if ("0".equals(itemCheck)) {
			str = prop.getProperty("L160M01C.ITEMCHECK0");
		} else if ("1".equals(itemCheck)) {
			str = "V";
		} else if ("2".equals(itemCheck)) {
			str = "X";
		} else {
			str = "";
		}
		return str;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 產生LMS1205R33的PDF 洗錢防制檢核表 J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @param path
	 *            rpt路徑
	 * @return outputstream outputstream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 * @throws ReportException
	 *             reportException
	 */
	public OutputStream genLMS1205R33(String mainId, Locale locale,
			String path, Properties prop) throws FileNotFoundException,
			ReportException, IOException, Exception {

		Properties prop1601 = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator(path);
		OutputStream outputStream = null;

		String branchName = null;

		L120M01A l120m01a = null;
		L160M01A l160m01a = null;

		// L120S09A．洗錢防制明細檔
		List<L120S09A> listL120s09a = null;

		Map<String, String> caseLvlMap = null;
		Map<String, String> typCdMap = null;
		Map<String, String> blackListCodeMap = null;
		Map<String, String> custRelationMap = null;

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		Map<String, String> sasNcResultMap = null;

		try {

			l160m01a = service1601.findL160M01AByMaindId(mainId);

			l120m01a = this.findL120m01aByL160m01a(l160m01a);
			if (l120m01a == null)
				l120m01a = new L120M01A();

			listL120s09a = amlRelateService
					.findL120s09aByMainIdWithOrder(mainId);

			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l160m01a.getOwnBrId())));

			caseLvlMap = codetypeservice.findByCodeType("lms1205m01_caseLvl",
					locale.toString());
			typCdMap = codetypeservice.findByCodeType("TypCd",
					locale.toString());
			blackListCodeMap = codetypeservice.findByCodeType("BlackListCode",
					locale.toString());
			custRelationMap = codetypeservice.findByCodeType(
					"BlackListRelation", locale.toString());

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			sasNcResultMap = codetypeservice.findByCodeType("SAS_NC_Result",
					locale.toString());

			if (caseLvlMap == null)
				caseLvlMap = new LinkedHashMap<String, String>();
			if (typCdMap == null)
				typCdMap = new LinkedHashMap<String, String>();
			if (blackListCodeMap == null)
				blackListCodeMap = new LinkedHashMap<String, String>();
			if (custRelationMap == null)
				custRelationMap = new LinkedHashMap<String, String>();
			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			if (sasNcResultMap == null)
				sasNcResultMap = new LinkedHashMap<String, String>();

			// 分行名稱
			rptVariableMap.put("BRANCHNAME", branchName);

			// L160M01B．動審表額度序號資料
			List<L160M01B> l160m01bList = (List<L160M01B>) service1601
					.findListByMainId(L160M01B.class, l160m01a.getMainId());
			if (UtilConstants.DEFAULT.是.equals(l160m01a.getAllCanPay())) {
				// L160M01A.message3=簽報書項下額度明細表全部動用
				rptVariableMap.put("L160M01B.CNTRNO",
						prop1601.getProperty("L160M01A.message3"));
			} else {
				rptVariableMap = this.setL160M01BData(rptVariableMap,
						l160m01bList);
			}

			// L160M01A.CUSTNAME
			List<Map<String, Object>> custNameList = null;
			custNameList = eloanDbService.findL140M01AByMainId(l160m01a
					.getMainId());
			rptVariableMap = this.setL160M01ACustNameData(rptVariableMap,
					custNameList, l160m01a);

			// 黑名單查詢日期
			L120S09A l120s09a = amlRelateService
					.findL120s09aMaxQDateByMainId(mainId);
			Date blackListQDate = l120s09a.getQueryDateS();
			rptVariableMap.put("BLACKLISTQDATE", CapDate.formatDate(
					blackListQDate, UtilConstants.DateFormat.YYYY_MM_DD));

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			rptVariableMap.put("L120S09B.NCRESULT", "");
			rptVariableMap.put("L120S09B.REFNO", "");
			rptVariableMap.put("L120S09B.UNIQUEKEY", "");
			rptVariableMap.put("L120S09B.NCCASEID", "");
			
			L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
			if (l120s09b != null) {
				rptVariableMap.put("L120S09B.NCRESULT",
						sasNcResultMap.get(Util.trim(l120s09b.getNcResult())));
				rptVariableMap.put("L120S09B.REFNO",
						Util.trim(l120s09b.getRefNo()));
				rptVariableMap.put("L120S09B.UNIQUEKEY",
						Util.trim(l120s09b.getUniqueKey()));
				rptVariableMap.put("L120S09B.NCCASEID",
						Util.trim(l120s09b.getNcCaseId()));
			}

			rptVariableMap = this.setL120M01AData(rptVariableMap, l120m01a,
					typCdMap, caseLvlMap, prop);

			titleRows = this.setL120S09aData(titleRows, listL120s09a,
					blackListCodeMap, custRelationMap, prop, rptVariableMap);

			// generator.setLang(java.util.Locale.TAIWAN);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (titleRows != null) {
				titleRows.clear();
			}
			if (caseLvlMap != null) {
				caseLvlMap.clear();
			}
			if (typCdMap != null) {
				typCdMap.clear();
			}
			if (blackListCodeMap != null) {
				blackListCodeMap.clear();
			}
			if (custRelationMap != null) {
				custRelationMap.clear();
			}

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			if (sasNcResultMap != null) {
				sasNcResultMap.clear();
			}

		}
		return outputStream;
	}

	/**
	 * 塞入L120S09A．洗錢防制明細檔
	 * 
	 * @param titleRows
	 *            多筆值資料 list代表每一列 map代表每一欄
	 * @param listL120s09a
	 *            List<L120S09A>資料
	 * @param blackListCodeMap
	 *            jobTitleMap
	 * @param custRelationMap
	 *            jobType1Map
	 * @param prop
	 *            prop
	 * @return List<Map<String, String>> list
	 */
	private List<Map<String, String>> setL120S09aData(
			List<Map<String, String>> titleRows, List<L120S09A> listL120s09a,
			Map<String, String> blackListCodeMap,
			Map<String, String> custRelationMap, Properties prop,
			Map<String, String> rptVariableMap) {

		int totCount = listL120s09a.size();
		int count = 0;
		// J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能
		String isFactoring_Without_Recourse_Buyer_Status = "3"; // 預設為不適用
		Timestamp chkTime = null;
		// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
		List<String> cesSns = new ArrayList<String>();
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊 
		StringBuilder cmfwarnpResults_1 = new StringBuilder(); //查詢結果=1(有)
		StringBuilder cmfwarnpResults_3 = new StringBuilder(); //查詢結果=3(不適用)
		Properties lmss20a_pop = MessageBundleScriptCreator
							.getComponentResource(LMSS20APanel.class);
		for (L120S09A l120s09a : listL120s09a) {
			count = count + 1;

			if (chkTime == null) {
				chkTime = l120s09a.getUpdateTime();
				if (chkTime == null) {
					chkTime = l120s09a.getCreateTime();
				}
			}

			String custRelation = l120s09a.getCustRelation();

			titleRows = this.setL120S09aDetailData(titleRows, l120s09a,
					blackListCodeMap, custRelationMap, count, totCount, prop);

			// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
			if (Util.equals(Util.trim(l120s09a.getPassPeps()), "Y")) {
				String cesSn = Util.trim(l120s09a.getCesSn());
				if (Util.notEquals(cesSn, "")) {
					if (!cesSns.contains(cesSn)) {
						cesSns.add(cesSn);
					}
				}
			}

			if (Util.notEquals(isFactoring_Without_Recourse_Buyer_Status, "1")) {

				String blackListCode = Util.trim(l120s09a.getBlackListCode());
				String[] prorertyArray = custRelation.split(",");
				for (String data : prorertyArray) {
					if (UtilConstants.Casedoc.L120s09aBlackListCtlTarget.應收帳款買方無追索
							.equals(data)) {
						if (Util.equals(
								blackListCode,
								UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "1"; // 有

						} else if (Util
								.equals(blackListCode,
										UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "1"; // 有

						} else if (Util
								.equals(blackListCode,
										UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
							isFactoring_Without_Recourse_Buyer_Status = "2"; // 無
						}
						break;
					}
				}
			}
			// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊 
			if(UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.有
					.equals(Util.trim(l120s09a.getCmfwarnpResult()))){
				if(Util.isNotEmpty(cmfwarnpResults_1)){//有超過1筆
					cmfwarnpResults_1.append("</br>");
				}
				cmfwarnpResults_1.append(MessageFormat.format(
						lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg2"),
						Util.trim(l120s09a.getCustId()),
						Util.trim(l120s09a.getCustName()),
						Util.trim(l120s09a.getCmfwarnpQueryResultInfo())));
			}
			if(UtilConstants.Casedoc.L120s09aCmfwarnpResultCode.不適用
					.equals(Util.trim(l120s09a.getCmfwarnpResult()))){
				if(Util.isNotEmpty(cmfwarnpResults_3)){//有超過1筆
					cmfwarnpResults_3.append("</br>");
				}
				cmfwarnpResults_3.append(MessageFormat.format(
						lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg4"),
						Util.trim(l120s09a.getCustId()),
						Util.trim(l120s09a.getCustName()),
						(l120s09a.getCmfwarnpQueryTime() == null ? "" : 
							CapDate.formatDate(l120s09a.getCmfwarnpQueryTime(),UtilConstants.DateFormat.YYYY_MM_DD) )));
				
			}

		}

		// J-106-0057-001 Web e-Loan授信管理系統新增「應收帳款承購無追索權-買方黑名單查詢」功能
		// FACTORINGWITHOUTRECOURSEBUYER
		// 1=有 2=無 3=不適用
		if (chkTime != null) {

			String printFactoringTimeStr = Util.trim(lmsService
					.getSysParamDataValue("LMS_J1060057001_ON"));

			Timestamp startTime = CapDate.getCurrentTimestamp();

			if (Util.notEquals(printFactoringTimeStr, "")) {
				startTime = CapDate
						.convertStringToTimestamp(printFactoringTimeStr);
			}

			if (!chkTime.before(startTime)) {
				rptVariableMap
						.put("FACTORINGWITHOUTRECOURSEBUYER",
								Util.nullToSpace(this.showYNPic6(
										Util.nullToSpace(isFactoring_Without_Recourse_Buyer_Status),
										prop)));// 黑名單查詢結果
			} else {
				rptVariableMap.put("FACTORINGWITHOUTRECOURSEBUYER", "");// 黑名單查詢結果
			}
		} else {
			rptVariableMap.put("FACTORINGWITHOUTRECOURSEBUYER", "");// 黑名單查詢結果
		}

		// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
		StringBuilder cesSnStr = new StringBuilder();
		if (cesSns != null && !cesSns.isEmpty()) {
			for (String snStr : cesSns) {
				cesSnStr.append(cesSnStr.length() > 0 ? "、" : "");
				cesSnStr.append(snStr);
			}
		}
		if (Util.isNotEmpty(cesSnStr.toString())) {
			rptVariableMap.put("L120S09A.CESSNLIST", cesSnStr.toString());
		} else {
			rptVariableMap.put("L120S09A.CESSNLIST","");
		}
		
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		StringBuilder cmfwarnpResultDesc = new StringBuilder();
		if (Util.isNotEmpty(cmfwarnpResults_1)) {
			cmfwarnpResultDesc.append(lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg1"))
				.append("</br>").append(cmfwarnpResults_1.toString());
		}
		if (Util.isNotEmpty(cmfwarnpResults_3)) {
			if(Util.isNotEmpty(cmfwarnpResultDesc)){
				cmfwarnpResultDesc.append("</br>");
			}
			cmfwarnpResultDesc.append(lmss20a_pop.getProperty("L120S09a.cmfwarnpResult.Msg3"))
				.append("</br>").append(cmfwarnpResults_3.toString());
		}
		rptVariableMap.put("L120S09A.CmfwarnpResultDesc", cmfwarnpResultDesc.toString());// 受告誡處分查詢結果

		return titleRows;
	}

	private List<Map<String, String>> setL120S09aDetailData(
			List<Map<String, String>> titleRows, L120S09A l120s09a,
			Map<String, String> blackListCodeMap,
			Map<String, String> custRelationMap, int count, int totCount,
			Properties prop) {

		// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
		Properties propLmss20a = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);

		Map<String, String> map = Util.setColumnMap();

		map.put("ReportBean.column01",
				Util.nullToSpace(l120s09a.getCustId() + l120s09a.getDupNo()));// ID
		map.put("ReportBean.column02", Util.nullToSpace(l120s09a.getCustName()));// 戶名

		map.put("ReportBean.column03",
				Util.nullToSpace(l120s09a.getCustEName()));// 英文戶名

		StringBuilder sb = new StringBuilder();
		String[] newItem = Util.trim(l120s09a.getCustRelation()).split(",");
		// 對陣列進行排序

		int i, j;
		String tmp;
		for (i = newItem.length - 1; i >= 0; i = i - 1) {
			for (j = 0; j < i; j = j + 1) {
				// if (newItem[j] > newItem[i])// 換（"小於"是由大到小）
				if (Util.parseInt((String) newItem[j]) > Util
						.parseInt((String) newItem[i]))// 換（"小於"是由大到小）
				{
					tmp = newItem[j];
					newItem[j] = newItem[i];
					newItem[i] = tmp;
				}
			}
		}

		for (String s : newItem) {
			if (sb.length() > 0)
				sb.append("、");
			sb.append(Util.trim(Util.trim(custRelationMap.get(s))));
		}
		map.put("ReportBean.column04", Util.nullToSpace(sb.toString()));// 相關身分

		map.put("ReportBean.column05",
				Util.nullToSpace(this.showYNPicAml(
						Util.nullToSpace(l120s09a.getBlackListCode()), prop)));// 黑名單查詢結果

		// J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
		// 風險等級
		if (Util.notEquals(Util.trim(l120s09a.getLuvRiskLevel()), "")) {
			map.put("ReportBean.column06",
					propLmss20a.getProperty("L120S09a.riskLvl_"
							+ Util.trim(l120s09a.getLuvRiskLevel())));
		} else {
			map.put("ReportBean.column06", "");
		}

		// e-Loan(企金、消金)徵信及授信管理系統，請增加可輸入借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別之欄位；另徵信、簽報及動審AML/CFT頁籤，請將"國別"資料一併納入執行掃描制裁、管制名單(黑名單)。
		// 國別
		if (Util.notEquals(Util.trim(l120s09a.getCountry()), "")) {
			map.put("ReportBean.column07", Util.trim(l120s09a.getCountry()));
		} else {
			map.put("ReportBean.column07", "");
		}

		// I-111-0089 調整授信審查處子系統e-loan，除徵信系統外，其餘排除掃瞄PEPs名單。
		map.put("ReportBean.column08",
				Util.equals(Util.trim(l120s09a.getPassPeps()), "Y") ? "*" : "");

		// 控制標題列
		if (count == 1) {
			map.put("ReportBean.column15", "AML01");
		} else {
			map.put("ReportBean.column15", "");
		}

		// 控制結尾列
		if (count == totCount) {
			map.put("ReportBean.column16", "AML99");
		} else {
			map.put("ReportBean.column16", "");
		}

		titleRows.add(map);
		return titleRows;
	}

	/**
	 * 取得欄位文字□是□可能是□不是
	 * 
	 * @param type
	 *            00=不是02=是 04=可能是
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPicAml(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("02".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("AML.CON02"));
		if ("04".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("AML.CON04"));
		if ("00".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("AML.CON00"));
		return str.toString();
	}

	/**
	 * 塞入變數MAP資料使用(L120M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @param typCdMap
	 *            typCdMap
	 * @param caseLvlMap
	 *            caseLvlMap
	 * @param prop
	 *            prop
	 * @return rptVariableMap 存放變數MAP
	 */
	private Map<String, String> setL120M01AData(
			Map<String, String> rptVariableMap, L120M01A l120m01a,
			Map<String, String> typCdMap, Map<String, String> caseLvlMap,
			Properties prop) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}
		rptVariableMap.put("L120M01A.CASENO",
				Util.nullToSpace(l120m01a.getCaseNo()));
		rptVariableMap.put("L120M01A.TYPCD",
				Util.nullToSpace(typCdMap.get(l120m01a.getTypCd())));
		rptVariableMap.put("L120M01A.CASEBRID",
				Util.nullToSpace(l120m01a.getCaseBrId()));
		rptVariableMap.put("L120M01A.CASEDATE",
				Util.nullToSpace(TWNDate.toAD(l120m01a.getCaseDate())));
		rptVariableMap.put("L120M01A.GIST",
				Util.nullToSpace(l120m01a.getGist()));
		rptVariableMap.put("L120M01A.ITEMOFBUSI",
				Util.nullToSpace(l120m01a.getItemOfBusi()));
		rptVariableMap.put("L120M01A.CESCUSTID",
				Util.nullToSpace(l120m01a.getCesCustId()));
		rptVariableMap.put("L120M01A.CESDUPNO",
				Util.nullToSpace(l120m01a.getCesDupNo()));
		rptVariableMap.put("L120M01A.CUSTID",
				Util.nullToSpace(l120m01a.getCustId()));
		rptVariableMap.put("L120M01A.DUPNO",
				Util.nullToSpace(l120m01a.getDupNo()));
		rptVariableMap.put("L120M01A.CUSTNAME",
				Util.nullToSpace(l120m01a.getCustName()));
		rptVariableMap.put("L120M01A.RANDOMCODE",
				Util.nullToSpace(l120m01a.getRandomCode()));
		rptVariableMap.put("L120M01A.PURPOSE",
				this.getPurpose(Util.nullToSpace(l120m01a.getPurpose()), prop));
		rptVariableMap.put("L120M01A.PURPOSEOTH",
				Util.nullToSpace(l120m01a.getPurposeOth()));
		rptVariableMap.put("L120M01A.RESOURCE", this.getResource(
				Util.nullToSpace(l120m01a.getResource()), prop));
		rptVariableMap.put("L120M01A.RESOURCEOTH",
				Util.nullToSpace(l120m01a.getResourceOth()));
		rptVariableMap.put("L120M01A.LONGCASEFLAG",
				Util.nullToSpace(l120m01a.getLongCaseFlag()));
		// other.msg187=詳授信期間財務預估及產業概況表
		// other.msg188=詳其他
		rptVariableMap.put(
				"L120M01A.LONGCASEDSCR",
				"1".equals(Util.nullToSpace(l120m01a.getLongCaseDscr())) ? pop
						.getProperty("other.msg187") : "2".equals(Util
						.nullToSpace(l120m01a.getLongCaseDscr())) ? pop
						.getProperty("other.msg188") : "");
		rptVariableMap.put(
				"L120M01A.CASELVL",
				!"".equals(Util.nullToSpace(l120m01a.getCaseLvl())
						.replace("　", "").replace(" ", "").trim()) ? "("
						+ Util.nullToSpace(caseLvlMap.get(Util
								.nullToSpace(l120m01a.getCaseLvl())
								.replace("　", " ").trim())) + ")" : "");
		if ("2".equals(Util.nullToSpace(l120m01a.getDocCode()))) {
			rptVariableMap.put("L120M01A.DOCCODENAME",
					Util.nullToSpace(prop.getProperty("L120M01A.DOCCODE2")));
		} else if ("3".equals(Util.nullToSpace(l120m01a.getDocCode()))) {
			rptVariableMap.put("L120M01A.DOCCODENAME",
					Util.nullToSpace(prop.getProperty("L120M01A.DOCCODE3")));
		} else if ("4".equals(Util.nullToSpace(l120m01a.getDocCode()))) {
			rptVariableMap.put("L120M01A.DOCCODENAME",
					Util.nullToSpace(prop.getProperty("L120M01A.DOCCODE4")));
		} else {
			rptVariableMap.put("L120M01A.DOCCODENAME", "");
		}
		rptVariableMap.put("L120M01A.APPROVETIME",
				Util.nullToSpace(TWNDate.toAD(l120m01a.getEndDate())));
		rptVariableMap.put("L120M01A.RPTTITLEAREA1",
				Util.nullToSpace(l120m01a.getRptTitleArea1()));
		rptVariableMap
				.put("L120M01A.RPTTITLE1", this.formatRptTitle1(Util
						.nullToSpace(l120m01a.getRptTitle1())));
		rptVariableMap.put("L120M01A.RPTTITLE2",
				Util.nullToSpace(l120m01a.getRptTitle2()));
		rptVariableMap.put("L120M01A.CUSTID",
				Util.nullToSpace(l120m01a.getCustId()));
		rptVariableMap.put("L120M01A.DUPNO",
				Util.nullToSpace(l120m01a.getDupNo()));
		rptVariableMap.put("L120M01A.CUSTNAME",
				Util.nullToSpace(l120m01a.getCustName()));
		rptVariableMap.put("L120M01A.CASEBRID",
				Util.nullToSpace(l120m01a.getCaseBrId()));
		rptVariableMap.put("L120M01A.CASENO",
				Util.nullToSpace(l120m01a.getCaseNo()));
		rptVariableMap.put("L120M01A.AREACHK",
				Util.nullToSpace(l120m01a.getAreaChk()));
		rptVariableMap
				.put("L120M01A.AUTHLVL", Util.trim(l120m01a.getAuthLvl()));

		// 國內屬營運中心制分行標題名稱
		rptVariableMap.put("AREATITLE",
				Util.nullToSpace(queryAreaTitle(l120m01a)));
		return rptVariableMap;
	}

	/**
	 * 取得國內屬營運中心制分行的標題名稱
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return
	 * @throws CapException
	 */
	private String queryAreaTitle(L120M01A l120m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch tBranch = branch.getBranch((l120m01a != null) ? Util
				.trim(l120m01a.getCaseBrId()) : user.getUnitNo());
		String docKind = Util.trim(l120m01a.getDocKind());
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (tBranch != null) {
			String brnGroup = Util.trim(tBranch.getBrnGroup());
			if (UtilConstants.BankNo.中部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北一區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南部區域授信中心.equals(brnGroup)
					|| UtilConstants.BankNo.北二區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.中區營運中心.equals(brnGroup)
					|| UtilConstants.BankNo.南區營運中心.equals(brnGroup)) {
				if (UtilConstants.Casedoc.DocKind.授權外.equals(docKind)) {
					/*
					 * 因為海外分行不屬於營運中心制，所以提醒第四階段，國內屬營運中心制分行時TITLE顯示會有差異
					 * 國內營運中心制分行，分行授權外案件會顯示營運中心授權外案件簽報書
					 */
					// other.msg131=營運中心授權外
					return pop.getProperty("other.msg131");
				}
			}
		}
		return null;
	}

	/**
	 * 取得借款用途
	 * 
	 * @param purpose
	 *            借款用途
	 * @return 借款用途
	 */
	private String getPurpose(String purpose, Properties prop) {
		StringBuffer str = new StringBuffer();
		if (purpose.indexOf("1") >= 0) {
			str.append(prop.getProperty("L120M01A.PURPOSE1"));
			str.append("，");
		}

		if (purpose.indexOf("2") >= 0) {
			str.append(prop.getProperty("L120M01A.PURPOSE2"));
			str.append("，");
		}

		if (purpose.indexOf("3") >= 0) {
			str.append(prop.getProperty("L120M01A.PURPOSE3"));
			str.append("，");
		}

		if (str.length() > 0) {
			str.deleteCharAt(str.length() - 1);
		}

		return str.toString();
	}

	/**
	 * 取得還款財源
	 * 
	 * @param resource
	 *            還款財源
	 * @return 還款財源
	 */
	private String getResource(String resource, Properties prop) {
		StringBuffer str = new StringBuffer();
		if (resource.indexOf("1") >= 0) {
			str.append(prop.getProperty("L120M01A.RESOURCE1"));
			str.append("，");
		}

		if (resource.indexOf("2") >= 0) {
			str.append(prop.getProperty("L120M01A.RESOURCE2"));
			str.append("，");
		}

		if (resource.indexOf("3") >= 0) {
			str.append(prop.getProperty("L120M01A.RESOURCE3"));
			str.append("，");
		}

		if (str.length() > 0) {
			str.deleteCharAt(str.length() - 1);
		}

		return str.toString();
	}

	/**
	 * 從動審表取得簽報書 J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param l160m01a
	 * @return
	 */
	public L120M01A findL120m01aByL160m01a(L160M01A l160m01a) {
		L120M01A l120m01a = null;

		Map<String, String> cntrCustIdMap = new HashMap<String, String>();
		Set<L160M01B> l160m01bs = l160m01a.getL160m01b(); // 這次動用的額度
		if (l160m01bs != null && !l160m01bs.isEmpty()) {
			for (L160M01B l160m01b : l160m01bs) {
				L140M01A l140m01a = lms1401Service
						.findL140m01aByMainId(l160m01b.getReMainId());
				if (l140m01a != null) {

					L120M01C l120m01c = l140m01a.getL120m01c();
					l120m01a = service1201.findL120m01aByMainId(l120m01c
							.getMainId());
					if (l120m01a != null) {
						break;
					}

				}
			}
		}
		return l120m01a;
	}

	/**
	 * 取得欄位文字□有□無□不適用
	 * 
	 * @param type
	 *            1=有 2=無 3=不適用
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPic6(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("1".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("COMMON.CON1"));
		if ("2".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("COMMON.CON2"));
		if ("3".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		// AML.NONFACTORING=非應收帳款承購業務
		str.append(prop.getProperty("COMMON.CON3") + "("
				+ (prop.getProperty("AML.NONFACTORING")) + ")");
		return str.toString();
	}

	/**
	 * 額度動用資訊一覽表
	 * 
	 * @param mainId
	 * @param locale
	 * @param rptProperties
	 * @return
	 * @throws Exception
	 */
	public OutputStream genLMS1601R05(String mainId, Locale locale,
			String path, Properties rptProperties) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		OutputStream outputStream = null;
		ReportGenerator generator = new ReportGenerator(path);
		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		int chapter = 0;
		Properties prop1601 = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		L160M01A l160m01a = service1601.findL160M01AByMaindId(mainId);
		List<L161S01B> l161s01bList = null;
		try {
			dfMoney.set(new DecimalFormat("#,###,###,###,##0.##"));
			dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));

			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			rptVariableMap.put("queryDate",
					CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM));

			String branchName = null;
			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l160m01a.getOwnBrId())));
			rptVariableMap.put("BRANCHNAME", branchName);

			rptVariableMap.put("L160M01A.RANDOMCODE",
					Util.nullToSpace(l160m01a.getRandomCode()));
			rptVariableMap.put("L160M01A.CASENO",
					Util.nullToSpace(l160m01a.getCaseNo()));

			// L160M01A.CUSTNAME
			List<Map<String, Object>> custNameList = null;
			custNameList = eloanDbService.findL140M01AByMainId(l160m01a
					.getMainId());
			rptVariableMap = this.setL160M01ACustNameData(rptVariableMap,
					custNameList, l160m01a);

			// L160M01B．動審表額度序號資料
			List<L160M01B> l160m01bList = null;
			l160m01bList = (List<L160M01B>) service1601.findListByMainId(
					L160M01B.class, l160m01a.getMainId());
			if (UtilConstants.DEFAULT.是.equals(l160m01a.getAllCanPay())) {
				// L160M01A.message3=簽報書項下額度明細表全部動用
				rptVariableMap.put("L160M01B.CNTRNO",
						prop1601.getProperty("L160M01A.message3"));
			} else {
				rptVariableMap = this.setL160M01BData(rptVariableMap,
						l160m01bList);
			}

			List<L161S01A> l161s01aList = l161s01aDao
					.findByMainIdWithOrder(mainId);
			if (l161s01aList != null && !l161s01aList.isEmpty()) {

				// 子報表設定
				// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
				chapter = chapter + 1;
				Map<String, String> subVariableData = new HashMap<String, String>();

				List<Map<String, String>> subTitleRows = new LinkedList<Map<String, String>>();

				subTitleRows = this.setL161S01ADataList(subTitleRows, locale,
						l161s01aList, rptProperties);

				if (subTitleRows == null) {
					subVariableData.put("hasClearLand", "");
				} else {
					subVariableData.put("hasClearLand", "Y");
				}

				// J-109-0153 紓困案資訊
				// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
				chapter = chapter + 1;
				Map<String, String> subVariableData2 = new HashMap<String, String>();
				boolean hasRescue = false;
				for (L161S01A l161s01a : l161s01aList) {
					if (hasRescue) {
						break;
					}
					if (Util.notEquals(Util.trim(l161s01a.getIsRescue()), "")) {
						hasRescue = true;
					}
				}
				if (hasRescue) {
					subVariableData2.put("hasRescue", "Y");
				} else {
					subVariableData2.put("hasRescue", "");
				}
				List<Map<String, String>> subTitleRows2 = new LinkedList<Map<String, String>>();
				subTitleRows2 = this.setL161S01ADataList2(subTitleRows2,
						locale, l161s01aList, rptProperties);

				// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
				if (Util.equals(Util.trim(l160m01a.getTType()), "3")) {
					chapter = chapter + 1; // 3
					rptVariableMap.put("L160M01A.TTYPE_IS_3", "Y");
					rptVariableMap.put("L160M01A.TTYPE_CHAPTER", prop1601
							.getProperty("common." + Util.trim(chapter)));
				} else {

					rptVariableMap.put("L160M01A.TTYPE_IS_3", "N");
					rptVariableMap.put("L160M01A.TTYPE_CHAPTER", "");
				}

				Map<String, String> subVariableData3 = new HashMap<String, String>();
				List<Map<String, String>> subTitleRows3 = new LinkedList<Map<String, String>>();
				subTitleRows3 = this
						.setL161S01ADataList3(subTitleRows3, locale,
								l161s01aList, rptProperties, prop1601, l160m01a);

				if (subTitleRows3 == null) {
					subVariableData3.put("hasTtype3", "");
				} else {
					subVariableData3.put("hasTtype3", "Y");
				}

				// JJ-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
				// 是否收取企金授信作業手續費
				chapter = chapter + 1; // 4 是否收取企金授信作業手續費
				rptVariableMap.put("L160M01A.TTYPE_IS_4", "Y");
				rptVariableMap.put("L160M01A.TTYPE_CHAPTER.4",
						prop1601.getProperty("common." + Util.trim(chapter)));

				Map<String, String> subVariableData4 = new HashMap<String, String>();
				List<Map<String, String>> subTitleRows4 = new LinkedList<Map<String, String>>();
				subTitleRows4 = this
						.setL161S01ADataList4(subTitleRows4, locale,
								l161s01aList, rptProperties, prop1601, l160m01a);

				if (subTitleRows4 == null) {
					subVariableData4.put("hasTtype4", "");
				} else {
					subVariableData4.put("hasTtype4", "Y");
				}
				
				// J-112-0435_12473_B1001 新增顯示額度控管種類、案件性質及聯貸資訊
				chapter = chapter + 1; // 額度相關資訊
				rptVariableMap.put("L160M01A.TTYPE_IS_5", "Y");
				rptVariableMap.put("L160M01A.TTYPE_CHAPTER.5",
						prop1601.getProperty("common." + Util.trim(chapter)));

				Map<String, String> subVariableData5 = new HashMap<String, String>();
				List<Map<String, String>> subTitleRows5 = new LinkedList<Map<String, String>>();
				subTitleRows5 = this.setL161S01ADataList5(subTitleRows5, locale, l161s01aList, rptProperties, prop1601, l160m01a);

				if (subTitleRows5 == null) {
					subVariableData5.put("hasTtype5", "");
				} else {
					subVariableData5.put("hasTtype5", "Y");
				}

				// **********************************************************************

				if (subTitleRows == null && subTitleRows2 == null
						&& subTitleRows3 == null && subTitleRows4 == null) {
					// 如果舊案一比空地貸款註記都沒有，就不要印LMS1601R05
					if (rptVariableMap != null) {
						rptVariableMap.clear();
					}

					return null;
				}

				SubReportParam subReportParam = new SubReportParam();
				subReportParam.setData(0, subVariableData, subTitleRows);
				subReportParam.setData(1, subVariableData2, subTitleRows2);
				// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
				subReportParam.setData(2, subVariableData3, subTitleRows3);
				// subReportParam.setData(1, subVariableData1, subTitleRows1);

				// JJ-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
				subReportParam.setData(3, subVariableData4, subTitleRows4);
				
				// J-112-0435_12473_B1001 新增顯示額度控管種類、案件性質及聯貸資訊
				subReportParam.setData(4, subVariableData5, subTitleRows5);

				generator.setLang(locale);
				generator.setVariableData(rptVariableMap);
				generator.setRowsData(titleRows);
				generator.setSubReportParam(subReportParam);

				outputStream = generator.generateReport();

			} else {
				outputStream = null;
			}

		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	/**
	 * 設定L161S01B資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L161S01B List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL161S01ADataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L161S01A> list, Properties prop) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		Map<String, String> newMapInTitleRows = null;
		int count = 1;
		StringBuffer temp = new StringBuffer();
		boolean hasClearLand = false;
		for (L161S01A l161s01a : list) {

			mapInTitleRows = Util.setColumnMap();
			newMapInTitleRows = Util.setColumnMap(); // 欄位名稱轉 ReportBean Column
			// J-108-0083_05097_B1001
			// 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制*****************************************
			String isClearLand = Util.trim(l161s01a.getIsClearLand());
			if (Util.notEquals(isClearLand, "")) {
				hasClearLand = true;
				mapInTitleRows.put("L140M01M.CNTRNO",
						Util.trim(l161s01a.getCntrNo()));
				DecimalFormat clearDf = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.#####");

				mapInTitleRows.put("L140M01M.ISCLEARLAND", isClearLand);
				mapInTitleRows.put("L140M01M.ISCLEARLANDDSCR",
						this.showYNPic4(isClearLand, prop));

				if (Util.equals(isClearLand, "Y")) {

					// 控管類別
					Map<String, String> ctlTypeMap = codetypeservice
							.findByCodeType("lms7600_ctlType");
					mapInTitleRows.put(
							"L140M01M.CTLTYPE",
							MapUtils.getString(ctlTypeMap,
									Util.trim(l161s01a.getCtlType()), ""));

					mapInTitleRows
							.put("L140M01M.FSTDATE",
									l161s01a.getFstDate() == null ? ""
											: Util.trim(CapDate.formatDate(
													l161s01a.getFstDate(),
													UtilConstants.DateFormat.YYYY_MM_DD)));

					mapInTitleRows
							.put("L140M01M.LSTDATE",
									l161s01a.getLstDate() == null ? ""
											: Util.trim(CapDate.formatDate(
													l161s01a.getLstDate(),
													UtilConstants.DateFormat.YYYY_MM_DD)));

					String isChgStDate = Util.trim(l161s01a.getIsChgStDate());
					if (Util.notEquals(isChgStDate, "")) {
						mapInTitleRows.put("L140M01M.ISCHGSTDATE", isChgStDate);

						mapInTitleRows.put("L140M01M.ISCHGSTDATEDSCR",
								this.showYNPic4(isChgStDate, prop));

					} else {
						mapInTitleRows.put("L140M01M.ISCHGSTDATE", "");
					}

					if (Util.equals(isChgStDate, "Y")) {

						mapInTitleRows
								.put("L140M01M.CSTDATE",
										l161s01a.getCstDate() == null ? ""
												: Util.trim(CapDate.formatDate(
														l161s01a.getCstDate(),
														UtilConstants.DateFormat.YYYY_MM_DD)));

						// 變更預計動工日原因
						Map<String, String> cstReasonMap = codetypeservice
								.findByCodeType("lms7600_cstReason");
						mapInTitleRows
								.put("L140M01M.CSTREASON", MapUtils.getString(
										cstReasonMap,
										Util.trim(l161s01a.getCstReason()), ""));

						// 輸入本次採行措施

						Map<String, String> adoptFgMap = codetypeservice
								.findByCodeType("lms7600_adoptFg");
						StringBuffer adoptStr = new StringBuffer("");
						for (String adoptKey : adoptFgMap.keySet()) {
							if (Util.trim(l161s01a.getAdoptFg()).contains(
									adoptKey)) {
								adoptStr.append(showYNPic7(
										Util.nullToSpace("Y"), prop));

							} else {
								adoptStr.append(showYNPic7(
										Util.nullToSpace("N"), prop));
							}
							adoptStr.append(adoptKey + ".");
							adoptStr.append(MapUtils.getString(adoptFgMap,
									adoptKey, ""));
							adoptStr.append("　");
						}

						mapInTitleRows.put("L140M01M.ADOPTFG",
								adoptStr.toString());
					} else {
						mapInTitleRows.put("L140M01M.CSTDATE", "");
						mapInTitleRows.put("L140M01M.CSTREASON", "");
						mapInTitleRows.put("L140M01M.ADOPTFG", "");
					}

					String isChgRate = Util.trim(l161s01a.getIsChgRate());
					if (Util.notEquals(isChgRate, "")) {
						mapInTitleRows.put("L140M01M.ISCHGRATE",
								Util.trim(l161s01a.getIsChgRate()));

						mapInTitleRows.put("L140M01M.ISCHGRATEDSCR", this
								.showYNPic4(Util.trim(l161s01a.getIsChgRate()),
										prop));

					} else {
						mapInTitleRows.put("L140M01M.ISCHGRATE",
								Util.trim(l161s01a.getIsChgRate()));
					}

					if (Util.trim(l161s01a.getAdoptFg()).contains("3")
							|| Util.equals(Util.trim(l161s01a.getIsChgRate()),
									"Y")) {

						mapInTitleRows.put("L140M01M.SHOWCHGRATE", "Y");

						mapInTitleRows.put(
								"L140M01M.RATEADD",
								l161s01a.getRateAdd() == null ? "" : clearDf
										.format(Util.parseDouble(Util
												.trim(l161s01a.getRateAdd()))));

						mapInTitleRows.put(
								"L140M01M.CUSTROA",
								l161s01a.getCustRoa() == null ? "" : clearDf
										.format(Util.parseDouble(Util
												.trim(l161s01a.getCustRoa()))));

						mapInTitleRows.put(
								"L140M01M.RELROA",
								l161s01a.getRelRoa() == null ? "" : clearDf
										.format(Util.parseDouble(Util
												.trim(l161s01a.getRelRoa()))));

						mapInTitleRows
								.put("L140M01M.ROABGNDATE",
										l161s01a.getRoaBgnDate() == null ? ""
												: Util.trim(CapDate.formatDate(
														l161s01a.getRoaBgnDate(),
														UtilConstants.DateFormat.YYYY_MM_DD)));

						mapInTitleRows
								.put("L140M01M.ROAENDDATE",
										l161s01a.getRoaEndDate() == null ? ""
												: Util.trim(CapDate.formatDate(
														l161s01a.getRoaEndDate(),
														UtilConstants.DateFormat.YYYY_MM_DD)));

					} else {
						mapInTitleRows.put("L140M01M.SHOWCHGRATE", "N");
						mapInTitleRows.put("L140M01M.RATEADD", "");
						mapInTitleRows.put("L140M01M.CUSTROA", "");
						mapInTitleRows.put("L140M01M.RELROA", "");
						mapInTitleRows.put("L140M01M.ROABGNDATE", "");
						mapInTitleRows.put("L140M01M.ROAENDDATE", "");
					}

					if (Util.equals(Util.trim(l161s01a.getIsChgStDate()), "Y")
							|| Util.equals(Util.trim(l161s01a.getIsChgRate()),
									"Y")) {
						mapInTitleRows
								.put("L140M01M.ISLEGAL", this.showYNPic4(
										Util.trim(l161s01a.getIsLegal()), prop));
					} else {
						mapInTitleRows.put("L140M01M.ISLEGAL", "");
					}

				} else {
					mapInTitleRows.put("L140M01M.CTLTYPE", "");
					mapInTitleRows.put("L140M01M.FSTDATE", "");
					mapInTitleRows.put("L140M01M.LSTDATE", "");
					mapInTitleRows.put("L140M01M.ISCHGSTDATE", "");
					mapInTitleRows.put("L140M01M.CSTDATE", "");
					mapInTitleRows.put("L140M01M.CSTREASON", "");
					mapInTitleRows.put("L140M01M.ISCHGRATE", "");
					mapInTitleRows.put("L140M01M.ADOPTFG", "");
					mapInTitleRows.put("L140M01M.RATEADD", "");
					mapInTitleRows.put("L140M01M.CUSTROA", "");
					mapInTitleRows.put("L140M01M.RELROA", "");
					mapInTitleRows.put("L140M01M.ROABGNDATE", "");
					mapInTitleRows.put("L140M01M.ROAENDDATE", "");
					mapInTitleRows.put("L140M01M.ISLEGAL", "");

					mapInTitleRows.put("L140M01M.SHOWCHGRATE", "N");
					mapInTitleRows.put("L140M01M.ISCHGSTDATEDSCR", "");
					mapInTitleRows.put("L140M01M.ISCHGRATEDSCR", "");
				}
			}

			newMapInTitleRows = columnToReportBeanNameFor1601R05(mapInTitleRows);

			if (Util.notEquals(isClearLand, "")) {
				// 舊案沒有空地貸款註記 不用印
				count++;
				titleRows.add(newMapInTitleRows);
			}

		}

		if (hasClearLand) {
			return titleRows;
		} else {
			return null;
		}

	}

	private Map<String, String> columnToReportBeanNameFor1601R05(
			Map<String, String> mapInTitleRows) {

		if (mapInTitleRows == null) {
			return null;
		}

		Map<String, String> newMapInTitleRows = Util.setColumnMap(); // 轉換後欄位名稱
		newMapInTitleRows.put("ReportBean.column01",
				MapUtils.getString(mapInTitleRows, "L140M01M.CNTRNO", "")); // L140M01M.CNTRNO
																			// 額度序號
		newMapInTitleRows.put("ReportBean.column02", MapUtils.getString(
				mapInTitleRows, "L140M01M.ISCLEARLANDDSCR", "")); // L140M01M.ISCLEARLANDDSCR
		newMapInTitleRows.put("ReportBean.column03",
				MapUtils.getString(mapInTitleRows, "L140M01M.FSTDATE", ""));
		newMapInTitleRows.put("ReportBean.column04",
				MapUtils.getString(mapInTitleRows, "L140M01M.LSTDATE", ""));
		newMapInTitleRows.put("ReportBean.column05", MapUtils.getString(
				mapInTitleRows, "L140M01M.ISCHGSTDATEDSCR", ""));
		newMapInTitleRows.put("ReportBean.column06",
				MapUtils.getString(mapInTitleRows, "L140M01M.CSTDATE", ""));
		newMapInTitleRows.put("ReportBean.column07",
				MapUtils.getString(mapInTitleRows, "L140M01M.CSTREASON", ""));
		newMapInTitleRows.put("ReportBean.column08",
				MapUtils.getString(mapInTitleRows, "L140M01M.ADOPTFG", ""));
		newMapInTitleRows.put("ReportBean.column09", MapUtils.getString(
				mapInTitleRows, "L140M01M.ISCHGRATEDSCR", ""));
		newMapInTitleRows.put("ReportBean.column10",
				MapUtils.getString(mapInTitleRows, "L140M01M.RATEADD", ""));
		newMapInTitleRows.put("ReportBean.column11",
				MapUtils.getString(mapInTitleRows, "L140M01M.CUSTROA", ""));
		newMapInTitleRows.put("ReportBean.column12",
				MapUtils.getString(mapInTitleRows, "L140M01M.RELROA", ""));
		newMapInTitleRows.put("ReportBean.column13",
				MapUtils.getString(mapInTitleRows, "L140M01M.ROABGNDATE", ""));
		newMapInTitleRows.put("ReportBean.column14",
				MapUtils.getString(mapInTitleRows, "L140M01M.ROAENDDATE", ""));
		newMapInTitleRows.put("ReportBean.column15",
				MapUtils.getString(mapInTitleRows, "L140M01M.ISLEGAL", ""));
		newMapInTitleRows.put("ReportBean.column16",
				MapUtils.getString(mapInTitleRows, "L140M01M.CTLTYPE", "")); // L140M01M.CTLTYPE

		if (mapInTitleRows.containsKey("L140M01M.ISCLEARLAND")) {
			newMapInTitleRows.put("ReportBean.column17", MapUtils.getString(
					mapInTitleRows, "L140M01M.ISCLEARLAND", "")); // L140M01M.ISCLEARLAND
		}

		newMapInTitleRows.put("ReportBean.column18",
				MapUtils.getString(mapInTitleRows, "L140M01M.ISCHGSTDATE", "")); // L140M01M.ISCHGSTDATE
		newMapInTitleRows.put("ReportBean.column19",
				MapUtils.getString(mapInTitleRows, "L140M01M.ISCHGRATE", "")); // L140M01M.ISCHGRATE
		newMapInTitleRows.put("ReportBean.column20",
				MapUtils.getString(mapInTitleRows, "L140M01M.SHOWCHGRATE", "")); // L140M01M.SHOWCHGRATE

		return newMapInTitleRows;
	}

	private List<Map<String, String>> setL161S01ADataList2(
			List<Map<String, String>> titleRows, Locale locale,
			List<L161S01A> list, Properties prop) {
		Map<String, String> lms140_rescueItem = codetypeservice.findByCodeType(
				"lms140_rescueItem", locale.toString());
		Map<String, String> lms140_rescueItemSub = codetypeservice
				.findByCodeType("lms140_rescueItemSub", locale.toString());
		// J-109-0811_05097_B1001 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
		Map<String, String> lms140_cbRefinDt = codetypeservice.findByCodeType(
				"lms140_cbRefinDt", locale.toString());

		for (L161S01A l161s01a : list) {
			Map<String, String> map = new LinkedHashMap<String, String>();
			map.put("ReportBean.column01", Util.trim(l161s01a.getCntrNo()));
			String isRescue = Util.trim(l161s01a.getIsRescue());
			map.put("ReportBean.column101", isRescue);
			map.put("ReportBean.column02", this.showYNPic4(isRescue, prop));
			String rescueItem = Util.trim(l161s01a.getRescueItem());
			// J-112-0148 疫後振興
			boolean isResueItemCaseF = lmsService.isResueItemCaseF(rescueItem);
			boolean isResueItemCaseJ = lmsService.isResueItemCaseJ(rescueItem);
			boolean isResueItemCaseL = lmsService.isResueItemCaseL(rescueItem);
			String column19Name = "本案是否屬因應嚴重特殊傳染性肺炎影響事業資金紓困";
			String column20Name = "紓困貸款類別";
			if (isResueItemCaseF) {
				column19Name = "本案是否屬疫後振興及低碳智慧納管貸款措施";
				column20Name = "疫後振興及低碳智慧納管貸款類別";
			} else if (isResueItemCaseJ) {
				column19Name = "本案是否屬因應天然災害貸款措施";
				column20Name = "天然災害貸款類別";
			} else if ("K01".equals(rescueItem)) {
				column19Name = "本案是否屬因應天然災害貸款/振興貸款措施";
				column20Name = "貸款類別";
			} else if (isResueItemCaseL) {
				column19Name = "本案是否屬政策性貸款";
				column20Name = "貸款類別";
			}
			map.put("ReportBean.column19", column19Name);
			map.put("ReportBean.column20", column20Name);
			String rescueItemSub = Util.trim(l161s01a.getRescueItemSub());
			map.put("ReportBean.column03",
					rescueItem + "." + lms140_rescueItem.get(rescueItem));
			map.put("ReportBean.column04",
					l161s01a.getRescueDate() == null ? "" : Util.trim(CapDate
							.formatDate(l161s01a.getRescueDate(),
									UtilConstants.DateFormat.YYYY_MM_DD)));
			if (isResueItemCaseF || isResueItemCaseJ || "K01".equals(rescueItem) || isResueItemCaseL) {
				map.put("ReportBean.column05", "");
			} else {
				map.put("ReportBean.column05", this.showYNPic4(
						Util.trim(l161s01a.getIsCbRefin()), prop));
			}

			// J-109-0811_05097_B1001
			// 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
			if (Util.equals(isRescue, "Y")
					&& Util.equals(Util.trim(l161s01a.getIsCbRefin()), "Y")) {
				map.put("ReportBean.column13",
						(Util.equals(Util.trim(l161s01a.getCbRefinDt()), "") ? ""
								: lms140_cbRefinDt.get(Util.trim(l161s01a
										.getCbRefinDt()))));
			} else {
				map.put("ReportBean.column13", "");
			}

			map.put("ReportBean.column103", lmsService.isResueItemNeedRescueNo(
					rescueItem, rescueItemSub) ? "Y" : "N");
			map.put("ReportBean.column06", Util.trim(l161s01a.getRescueNo()));
			map.put("ReportBean.column104", lmsService.isResueItemNeedEmpCount(
					rescueItem, rescueItemSub) ? "Y" : "N");
			map.put("ReportBean.column07", Util.trim(l161s01a.getEmpCount()));
			if (lmsService.isResueItemRescueRate(rescueItem, rescueItemSub)) {
				BigDecimal rescueRate = l161s01a.getRescueRate();
				DecimalFormat rescueRateDf = new DecimalFormat("#0.#####");
				map.put("ReportBean.column08", rescueRate == null ? "N.A."
						: rescueRateDf.format(rescueRate));
			} else {
				map.put("ReportBean.column08", "");
			}
			boolean matchJ04_J05 = rescueItem.matches("J04|J05");
			if (lmsService.isResueItemOldCase(rescueItem) || matchJ04_J05) {
				map.put("ReportBean.column102", "Y");
				map.put("ReportBean.column09", this.showYNPic4(
						Util.trim(l161s01a.getIsExtendSixMon()), prop));
				map.put("ReportBean.column10",
						l161s01a.getRescueIbDate() == null ? "" : Util
								.trim(CapDate.formatDate(
										l161s01a.getRescueIbDate(),
										UtilConstants.DateFormat.YYYY_MM_DD)));
				DecimalFormat resureAmtDf = new DecimalFormat(
						"###,###,###,###,###,###,###,##0.##");
				String resureAmt = (l161s01a.getRescueAmt() == null ? ""
						: resureAmtDf.format(l161s01a.getRescueAmt()));
				String rescueCurrTitle = "截至109.03.12前既有之本行貸款餘額：";
				// J-111-0112_05097_B1002 Web
				// e-Loan企金授信管理系統新增111年經濟部紓困方案
				// rescueCurrTitle = (Util.equals(rescueItem, "A04") ?
				// "截至110.06.03前既有之本行貸款餘額："
				// : rescueCurrTitle);

				if (Util.equals(rescueItem, "A04")) {
					rescueCurrTitle = "截至110.06.03前既有之本行貸款餘額：";
				} else if (Util.equals(rescueItem, "A08")) {
					rescueCurrTitle = "截至111.06.01前既有之本行貸款餘額：";
				} else if (matchJ04_J05) {
					rescueCurrTitle = "截至113.4.3前既有之本行貸款餘額：";
				}

				String isExtendSixMonStr = "是否展延到期日(原則短期半年/中長期寬限1年)";
				String rescueIbDateStr = "首次合意展延日(即簽約日亦為減讓利息之生效日)";
				if (matchJ04_J05) {
					isExtendSixMonStr = "是否展延到期日";
					rescueIbDateStr = "合意減息日(即簽約日亦為減讓利息之生效日)";
				}
				map.put("ReportBean.column22", isExtendSixMonStr);
				map.put("ReportBean.column23", rescueIbDateStr);

				map.put("ReportBean.column11",
						rescueCurrTitle + Util.trim(l161s01a.getRescueCurr())
								+ resureAmt + "元");
			} else {
				map.put("ReportBean.column102", "");
				map.put("ReportBean.column09", "");
				map.put("ReportBean.column10", "");
				map.put("ReportBean.column11", "");
			}
			if (lmsService.needRescueItemSub(rescueItem)) {
				map.put("ReportBean.column12",
						lms140_rescueItemSub.get(rescueItemSub));
			} else {
				map.put("ReportBean.column12", "");
			}

			// J-110-0288_05097_B1001 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
			if (lmsService.isResueItemNeedNdfGutPercent(rescueItem)) {
				if (!Util.isEmpty(l161s01a.getRescueNdfGutPercent())) {

					String ndfGutPercent = l161s01a.getRescueNdfGutPercent() == null ? ""
							: LMSUtil.calcZero(l161s01a
									.getRescueNdfGutPercent()) + "％";

					map.put("ReportBean.column14", ndfGutPercent);

				} else {
					map.put("ReportBean.column14", "");
				}

			} else {
				map.put("ReportBean.column14", "");
				// J-110-0465_05097_B1001 Web e-Loan國內企金授信動審表新增額度編號
				// map.put("ReportBean.column16", "");
			}

			// J-112-0148_05097_B1002 Web
			// e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
			if (service1601.isResueItemRescueSn(rescueItem)) {
				// J-110-0465_05097_B1001 Web e-Loan國內企金授信動審表新增額度編號
				if (!Util.isEmpty(l161s01a.getRescueSn())) {
					String rescueSn = Util.trim(l161s01a.getRescueSn());

					map.put("ReportBean.column16", rescueSn);
				} else {
					map.put("ReportBean.column16", "");
				}
			} else {
				map.put("ReportBean.column16", "");
			}

			// J-110-0288_05097_B1002 Web
			// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
			if (lmsService.isResueItemNeedIsTurnoverDecreased(rescueItem)) {
				if (!Util.isEmpty(l161s01a.getIsTurnoverDecreased())) {
					Properties l160mpro = MessageBundleScriptCreator
							.getComponentResource(LMS1601M01Page.class);
					String isTurnoverDecreased = Util.trim(l161s01a
							.getIsTurnoverDecreased());
					// 是否符合110年5~12月營業額減少達15%：
					// L160M01A.isTurnoverDecreased_A07=是否符合110年5~12月營業額減少達15%
					// L160M01A.isTurnoverDecreased_A11=是否符合111年1~12月營業額減少達15%
					String propertyKey = "L160M01A.isTurnoverDecreased_"
							+ rescueItem;
					map.put("ReportBean.column15",
							(l160mpro.containsKey(propertyKey) ? l160mpro
									.getProperty(propertyKey)
									: "是否符合110年5~12月營業額減少達15%")
									+ "："
									+ this.showYNPic_1(isTurnoverDecreased,
											prop));

				} else {
					map.put("ReportBean.column15", "");
				}
			} else {
				map.put("ReportBean.column15", "");
			}

			// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
			map.put("ReportBean.column17", "");
			map.put("ReportBean.column18", "");
			if (Util.equals(isRescue, "Y")
					&& service1601.needRescueChgRateFg(rescueItem)) {

				Properties l160mpro = MessageBundleScriptCreator
						.getComponentResource(LMS1601M01Page.class);

				String rescueChgRateFg = "";
				Date rescueChgRateSingDate = null;
				BigDecimal rescueChgRate = null;
				Date rescueChgRateEffectDate = null;

				rescueChgRateFg = Util.trim(l161s01a.getRescueChgRateFg());
				map.put("ReportBean.column17",
						this.showYNPic4(rescueChgRateFg, prop));

				if (Util.equals(rescueChgRateFg, "Y")) {

					StringBuffer chgRateBuff = new StringBuffer("");

					rescueChgRateSingDate = l161s01a.getRescueChgRateSingDate();
					if (rescueChgRateSingDate != null) {
						if (Util.notEquals(Util.trim(chgRateBuff.toString()),
								"")) {
							chgRateBuff.append("，");
						}
						chgRateBuff.append(l160mpro
								.getProperty("L161S01A.rescueChgRateSingDate"));
						chgRateBuff.append("：");
						chgRateBuff.append(CapDate.formatDate(
								rescueChgRateSingDate, "yyyy-MM-dd"));

					}

					rescueChgRate = l161s01a.getRescueChgRate();
					if (rescueChgRate != null) {

						if (Util.notEquals(Util.trim(chgRateBuff.toString()),
								"")) {
							chgRateBuff.append("，");
						}
						chgRateBuff.append(l160mpro
								.getProperty("L161S01A.rescueChgRate"));
						chgRateBuff.append("：");

						DecimalFormat rescueChgRateDf = new DecimalFormat(
								"##0.#####");
						String rescueChgRateStr = (rescueChgRate == null ? ""
								: rescueChgRateDf.format(rescueChgRate));

						chgRateBuff.append(rescueChgRateStr);
					}

					rescueChgRateEffectDate = l161s01a
							.getRescueChgRateEffectDate();
					if (rescueChgRateEffectDate != null) {
						// L161S01A.rescueChgRateEffectDate=客戶利率調升日
						if (Util.notEquals(Util.trim(chgRateBuff.toString()),
								"")) {
							chgRateBuff.append("，");
						}
						chgRateBuff
								.append(l160mpro
										.getProperty("L161S01A.rescueChgRateEffectDate"));
						chgRateBuff.append("：");
						chgRateBuff.append(CapDate.formatDate(
								rescueChgRateEffectDate, "yyyy-MM-dd"));
					}

					if (Util.notEquals(Util.trim(chgRateBuff.toString()), "")) {
						map.put("ReportBean.column18",
								Util.trim(chgRateBuff.toString()));
					}

				}

			}

			// 掛件文號改成動態顯示名稱
			// 掛件文號
			String rescueNoName = service1601.reloadRescueItemData(rescueItem);
			map.put("ReportBean.column21", rescueNoName);

			titleRows.add(map);
		}

		return titleRows;
	}

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 取得欄位文字□是□否
	 * 
	 * @param type
	 *            Y=是 N=否
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPic4(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("Y".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("yes")); // 是
		if ("N".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("no")); // 否

		return str.toString();
	}

	// J-106-0195-001 Web e-Loan授信管理系統異常通報簽報書新增本行重大偶發事件通報作業要點相關欄位
	// 取得欄位文字□ ■
	private String showYNPic7(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("Y".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}

		return str.toString();
	}

	/**
	 * J-109-0322_05097_B1001 Web e-Loan企消金授信簽報登錄決議輸入授審會會期, 於列印決議內容時將會期資訊改以西元年呈現
	 * 
	 * @param rptTitle1
	 *            授審會會期
	 * @return
	 */
	private String formatRptTitle1(String rptTitle1) {

		rptTitle1 = Util.trim(rptTitle1);

		if (Util.equals(rptTitle1, "")) {
			return rptTitle1;
		}

		String rtnRptTitle = rptTitle1;

		int yearIndex = rptTitle1.indexOf("年");

		if (yearIndex == 3) {
			String printAdYear = Util.trim(lmsService
					.getSysParamDataValue("LMS_RPTTITLE1_PRINT_AD_YEAR"));
			if (Util.equals(printAdYear, "Y")) {
				rtnRptTitle = (Util.parseInt(Util.getLeftStr(rptTitle1, 3)) + 1911)
						+ Util.getRightStr(rptTitle1, rptTitle1.length() - 3);
			}
		}

		return rtnRptTitle;
	}
	
	/**
	 * J-112-0435_12473_B1001 新增顯示額度控管種類、案件性質及聯貸資訊
	 * 
	 * @param titleRows
	 * @param locale
	 * @param list
	 * @param rptProperties
	 * @param prop1601
	 * @param l160m01a
	 * @return
	 */
	private List<Map<String, String>> setL161S01ADataList5(
			List<Map<String, String>> titleRows, Locale locale,
			List<L161S01A> list, Properties rptProperties, Properties prop1601,
			L160M01A l160m01a) {
		
		String[] codeType = {"lms1405m01_snoKind", "lms1605m01_caseType"};
		Map<String, CapAjaxFormResult> codeMap = codeTypeService.findByCodeType(codeType, locale.toString());
		
		for (L161S01A l161s01a : list) {
			Map<String, String> map = new LinkedHashMap<String, String>();
			List<String> l161s01aData_list = new ArrayList<String>();
			String snoKind = Util.trim(l161s01a.getSnoKind());
			String caseType = Util.trim(l161s01a.getCaseType());
			String unitCase = Util.trim(l161s01a.getUnitCase());
			String uCMainBranch = Util.trim(l161s01a.getUCMainBranch());
			String uCntBranch = Util.trim(l161s01a.getUCntBranch());
			String uCMSBranch = Util.trim(l161s01a.getUCMSBranch());
			String uHideName = Util.trim(l161s01a.getUHideName());
			String uArea = Util.trim(l161s01a.getUArea());
			
			//L140M01a.snoKind=額度控管種類
			l161s01aData_list.add(prop1601.getProperty("L140M01a.snoKind")
					+ "："
					+ codeMap.get("lms1405m01_snoKind").get(snoKind));
			//L160M01A.caseType=案件性質
			l161s01aData_list.add(prop1601.getProperty("L160M01A.caseType")
					+ "："
					+ codeMap.get("lms1605m01_caseType").get(caseType));
			//L120M01b.unitCase=本案是否有同業聯貸案額度
			l161s01aData_list.add(prop1601.getProperty("L120M01b.unitCase")
					+ "："
					+ this.showYNPic4(unitCase, rptProperties));
			//同業聯貸資訊
			if("Y".equals(unitCase)){
				//L120M01b.uCMainBranch=本行是否為主辦行
				l161s01aData_list.add(prop1601.getProperty("L120M01b.uCMainBranch")
						+ "："
						+ this.showYNPic4(uCMainBranch, rptProperties));
				//L120M01b.uCntBranch=本分行是否為額度管理行
				l161s01aData_list.add(prop1601.getProperty("L120M01b.uCntBranch")
						+ "："
						+ this.showYNPic4(uCntBranch, rptProperties));
				//L120M01b.uCMSBranch=本行是否為擔保品管理行
				l161s01aData_list.add(prop1601.getProperty("L120M01b.uCMSBranch")
						+ "："
						+ this.showYNPic4(uCMSBranch, rptProperties));
				//L120M01b.uHideName=本案是否為隱名參貸
				l161s01aData_list.add(prop1601.getProperty("L120M01b.uHideName")
						+ "："
						+ this.showYNPic4(uHideName, rptProperties));
				//L120M01b.uArea=本案為國內聯貸/國際聯貸
				String uAreaResult = "□" + prop1601.getProperty("L120M01b.uArea1") + "□" + prop1601.getProperty("L120M01b.uArea2");
				if("A".equals(uArea)){
					uAreaResult = "■" + prop1601.getProperty("L120M01b.uArea1") + "□" + prop1601.getProperty("L120M01b.uArea2");
				} else if ("B".equals(uArea)){
					uAreaResult = "□" + prop1601.getProperty("L120M01b.uArea1") + "■" + prop1601.getProperty("L120M01b.uArea2");
				}
				l161s01aData_list.add(prop1601.getProperty("L120M01b.uArea")
						+ "："
						+ uAreaResult);
			}
			
			String l161s01aData = StringUtils.join(l161s01aData_list, "<br/>");

			map.put("ReportBean.column01", Util.trim(l161s01a.getCntrNo()));
			map.put("ReportBean.column02", l161s01aData);
			titleRows.add(map);
		}

		return titleRows;
	}

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 取得欄位文字□是□否
	 * 
	 * @param type
	 *            Y=是 N=否
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPic_1(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("Y".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("yes")); // 是
		if ("N".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("no")); // 否
		if ("X".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("COMMON.CON3")); // 不適用
		return str.toString();
	}

	/**
	 * J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
	 * 
	 * @param titleRows
	 * @param locale
	 * @param list
	 * @param prop
	 * @param l160m01a
	 * @return
	 */
	private List<Map<String, String>> setL161S01ADataList3(
			List<Map<String, String>> titleRows, Locale locale,
			List<L161S01A> list, Properties rptProperties, Properties prop1601,
			L160M01A l160m01a) {

		if (Util.equals(Util.trim(l160m01a.getTType()), "3")) {
			for (L161S01A l161s01a : list) {
				Map<String, String> map = new LinkedHashMap<String, String>();
				String useSelect = l161s01a.getUseSelect_s01a();
				String useString = "";
				String lnString = "";
				String lnSelect = l161s01a.getLnSelect_s01a();

				if (!Util.isEmpty(useSelect)) {
					// 動用期限判斷
					switch (Integer.valueOf(useSelect)) {
					case 1:
						useString = this
								.getDate(l161s01a.getUseFromDate_s01a())
								+ " ~ "
								+ this.getDate(l161s01a.getUseEndDate_s01a());
						break;
					case 2:

						// L160M01A.useMonth1=自首動日起
						// L160M01A.useMonth2=個月
						useString = prop1601.getProperty("L160M01A.useMonth1")
								+ " " + Util.trim(l161s01a.getUseMonth_s01a())
								+ " "
								+ prop1601.getProperty("L160M01A.useMonth2");
						break;
					case 3:
						useString = l161s01a.getUseOther_s01a();
						break;
					}
				}

				// 授信期限判斷，當取得授信契約書期別 為中長期
				if (!Util.isEmpty(lnSelect)
						&& "2".equals(l161s01a.getTType_s01a())) {
					switch (Integer.valueOf(lnSelect)) {
					case 1:
						lnString = this.getDate(l161s01a.getLnFromDate_s01a())
								+ " ~ "
								+ this.getDate(l161s01a.getLnEndDate_s01a());
						break;
					case 2:
						// L160M01A.useMonth1=自首動日起
						// L160M01A.lnYear=年
						// L160M01A.useMonth2=個月
						lnString = prop1601.getProperty("L160M01A.useMonth1")
								+ " " + Util.trim(l161s01a.getLnYear_s01a())
								+ " " + prop1601.getProperty("L160M01A.lnYear")
								+ " " + Util.trim(l161s01a.getLnMonth_s01a())
								+ " "
								+ prop1601.getProperty("L160M01A.useMonth2");
						break;
					case 3:
						lnString = l161s01a.getLnOther_s01a();
						break;
					}
				}

				map.put("ReportBean.column01", Util.trim(l161s01a.getCntrNo()));
				map.put("ReportBean.column02",
						prop1601.getProperty("L160M01A.tType"
								+ l161s01a.getTType_s01a())
								+ " ");
				map.put("ReportBean.column03", useString);
				map.put("ReportBean.column04", lnString);

				titleRows.add(map);
			}

		} else {

		}

		return titleRows;
	}

	/**
	 * J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
	 * 
	 * @param titleRows
	 * @param locale
	 * @param list
	 * @param prop
	 * @param l160m01a
	 * @return
	 */
	private List<Map<String, String>> setL161S01ADataList4(
			List<Map<String, String>> titleRows, Locale locale,
			List<L161S01A> list, Properties rptProperties, Properties prop1601,
			L160M01A l160m01a) {

		for (L161S01A l161s01a : list) {
			Map<String, String> map = new LinkedHashMap<String, String>();
			String isOperationFee = Util.trim(l161s01a.getIsOperationFee());
			String operationFeeCurr = Util.trim(l161s01a.getOperationFeeCurr());
			BigDecimal operationFeeAmt = l161s01a.getOperationFeeAmt() != null ? l161s01a
					.getOperationFeeAmt() : BigDecimal.ZERO;
			String operationFeeDueDate = l161s01a.getOperationFeeDueDate() != null ? CapDate
					.formatDate(l161s01a.getOperationFeeDueDate(),
							UtilConstants.DateFormat.YYYY_MM_DD) : "0001-01-01";

			map.put("ReportBean.column01", Util.trim(l161s01a.getCntrNo()));

			map.put("ReportBean.column02",
					this.showYNPic4(isOperationFee, prop1601));
			map.put("ReportBean.column06", isOperationFee);

			map.put("ReportBean.column03", operationFeeCurr);
			map.put("ReportBean.column04",
					NumConverter.addComma(operationFeeAmt)
							+ prop1601.getProperty("other.money"));
			map.put("ReportBean.column05", operationFeeDueDate);

			titleRows.add(map);
		}

		return titleRows;
	}	
}
