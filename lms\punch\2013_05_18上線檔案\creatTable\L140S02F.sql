---------------------------------------------------------
-- LMS.L140S02F 房屋貸款檔
---------------------------------------------------------


---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140S02F;
CREATE TABLE LMS.L140S02F (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	SEQ           DECIMAL(5,0)  not null,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>    CHAR(1)      ,
	AP<PERSON>ATE       DATE         ,
	NOHOUSE       CHAR(1)      ,
	SELLERID      VARCHAR(11)  ,
	SELLERNAME    VARCHAR(120) ,
	<PERSON><PERSON>NUMBER     VARCHAR(20)  ,
	HOMEREGISTERDATE  DATE         ,
	CUSTTYPE      CHAR(1)      ,
	KGAGREEYN     CHAR(1)      ,
	KGAGREENO     VARCHAR(9)   ,
	KGAGREEDT     DATE         ,
	KGENDDATE     DATE         ,
	KGENDCODE     VARCHAR(300) ,
	KGINTDATE     DATE         ,
	TAGNO         VARCHAR(7)   ,
	TAGYEAR       DECIMAL(4,0) ,
	TAGRENO       VARCHAR(8)   ,
	TAGUNIT       CHAR(1)      ,
	ASSAPPYEAR    DECIMAL(4,0) ,
	ASSBOOKNO     VARCHAR(14)  ,
	ASSORIRATE    DECIMAL(7,4) ,
	RMBINSFLAG    CHAR(1)      ,
	RMBINTFLAG    CHAR(1)      ,
	RMBINTTERM    DECIMAL(5,0) ,
	INSFLAG       CHAR(1)      ,
	INSLOANBAL    DECIMAL(13,0),
	UPTYPE        CHAR(1)      ,
	UPBEG         DECIMAL(5,0) ,
	UPEND         DECIMAL(5,0) ,
	UPRATE        DECIMAL(5,2) ,
	PCONBEG1      DECIMAL(3,0) ,
	PCONEND1      DECIMAL(3,0) ,
	PCALCON1      DECIMAL(5,2) ,
	PCONBEG2      DECIMAL(3,0) ,
	PCONEND2      DECIMAL(3,0) ,
	PCALCON2      DECIMAL(5,2) ,
	TNF           VARCHAR(5)   ,
	TNFOTHER      VARCHAR(60)  ,
	ISCREDIT      CHAR(1)      ,
	IMPORTID      VARCHAR(6)   ,
	ISTAKFEE      CHAR(1)      ,
	TCONEND       DECIMAL(3,0) ,
	RATEPLAN      CHAR(2)      ,
	PRODPLAN      CHAR(2)      ,
	INSCAMT       DECIMAL(15,0),
	CMSSRCOID     CHAR(32)     ,
	REFMAINID     CHAR(32)     ,
	REPEAT        CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140S02F PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140S02F01;
CREATE UNIQUE INDEX LMS.XL140S02F01 ON LMS.L140S02F   (MAINID, SEQ);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140S02F IS '房屋貸款檔';
COMMENT ON LMS.L140S02F (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SEQ           IS '序號', 
	FAVCHGCASE    IS '轉貸前後為相同性質之政策性貸款', 
	APPDATE       IS '優惠房貸額度申請日', 
	NOHOUSE       IS '新中古屋', 
	SELLERID      IS '售屋者統編', 
	SELLERNAME    IS '售屋者姓名', 
	CHKNUMBER     IS '核准編號/核發證明編號', 
	HOMEREGISTERDATE  IS '產權登記日期', 
	CUSTTYPE      IS '客戶類別', 
	KGAGREEYN     IS '是否為高雄市首購貸款', 
	KGAGREENO     IS '高市首購核准編號', 
	KGAGREEDT     IS '高市首購核准日', 
	KGENDDATE     IS '高市終止補貼日', 
	KGENDCODE     IS '高市終止補貼原因', 
	KGINTDATE     IS '高市首購補貼起日', 
	TAGNO         IS '中籤編號', 
	TAGYEAR       IS '中籤年份', 
	TAGRENO       IS '收件編號', 
	TAGUNIT       IS '中籤單簽發單位', 
	ASSAPPYEAR    IS '輔購市府核准年度', 
	ASSBOOKNO     IS '名冊編號', 
	ASSORIRATE    IS '原貸行庫利率', 
	RMBINSFLAG    IS '是否搭配房貸壽險', 
	RMBINTFLAG    IS '是否搭配房貸壽險利率優惠方案', 
	RMBINTTERM    IS '搭配房貸壽險利率優惠方案-期數', 
	INSFLAG       IS '是否保費融資', 
	INSLOANBAL    IS '是否保費融資-金額', 
	UPTYPE        IS '房貸選擇權設定－種類', 
	UPBEG         IS '房貸選擇權設定－起期', 
	UPEND         IS '房貸選擇權設定－迄期', 
	UPRATE        IS '房貸選擇權設定－利率', 
	PCONBEG1      IS '提前還本管制設定第一段－起期', 
	PCONEND1      IS '提前還本管制設定第一段－迄期', 
	PCALCON1      IS '提前還本管制設定第一段－違約金計算條件', 
	PCONBEG2      IS '提前還本管制設定第二段－起期', 
	PCONEND2      IS '提前還本管制設定第二段－迄期', 
	PCALCON2      IS '提前還本管制設定第二段－違約金計算條件', 
	TNF           IS '提前還本違約金免收條件', 
	TNFOTHER      IS '其他說明', 
	ISCREDIT      IS '房貸資訊(一)－抵利/連動式', 
	IMPORTID      IS '房貸資訊(一)－引介行員代號', 
	ISTAKFEE      IS '房貸資訊(一)－是否代付費用', 
	TCONEND       IS '房貸資訊(一)－代付費用管制迄期', 
	RATEPLAN      IS '房貸資訊(二)－房貸利率方案', 
	PRODPLAN      IS '房貸資訊(二)－房貸產品方案', 
	INSCAMT       IS '房貸資訊(二)－借款繳保費之金額', 
	CMSSRCOID     IS '擔保品資料檔oid', 
	REFMAINID     IS '購置房屋擔保放款風險權數檢核表',
	REPEAT        IS '是否重新辦理',
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
