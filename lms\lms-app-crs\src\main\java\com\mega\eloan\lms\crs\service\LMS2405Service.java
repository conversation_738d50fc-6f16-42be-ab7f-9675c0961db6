/* 
 * LMS2405Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.service;

import java.util.Date;
import java.util.List;
import java.util.Map;



import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C240M01Z;

public interface LMS2405Service extends ICapService {

	/**
	 * 查詢LMS.BDOC是否有此筆資料
	 * 
	 * @param mainId
	 * @param FieldId
	 * @return
	 */
	List<DocFile> findDocFile(String mainId, String FieldId);

	/**
	 * 刪除名單主檔
	 * 
	 * @param oid
	 */
	void deleteC240M01A(String oid);

	/**
	 * 利用Oid做搜尋
	 * 
	 * @param <T>
	 * @param clazz
	 * @param oid
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	<T extends GenericBean> T findModelByOid(Class clazz, String oid);

	/**
	 * 搜尋
	 * 
	 * @param clazz
	 * @param search
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * 單筆儲存
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	/**
	 * 單筆刪除
	 * 
	 * @param clazz
	 * @param oid
	 */
	@SuppressWarnings("rawtypes")
	void delete(Class clazz, String oid);

	// // UploadFileInfo
	// List<UploadFileInfo> getUploadFileInfoList(ISearch search);
	//
	// int getUploadFileInfoCount(ISearch search);
	/**
	 * 啟動流程
	 * 
	 * @param oid
	 *            oid(instid)
	 * @param flow_code
	 *            流程代號
	 */
	public void startFlow(String oid, String flow_code);

	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType) throws Throwable;

	/**
	 * 刪除覆審類別有8-1
	 * 
	 * @param oid
	 */
	void deleteC241M01A(String oid);

	/**
	 * 主檔給號
	 * 
	 * @param oid
	 * @return
	 * @throws CapMessageException 
	 */
	boolean getNumber(String oid, boolean reGet) throws CapMessageException;

	/**
	 * 明細給號
	 * 
	 * @param mainId
	 */
	void projectSeq(String mainId);

	/**
	 * 更新控制檔
	 * 
	 * @param date
	 */
	void update490(String branch, Date date);

	/**
	 * 產生名單
	 * 
	 * @param branch
	 * @param dateYM
	 */
	boolean produce(String branch, String dateYM) throws CapException;

	/**
	 * 產生資料
	 * 
	 * @param oid
	 */
	Map<String, String> produceList(C240M01A c240m01a) throws CapException;

	/**
	 * 新增明細
	 * 
	 * @param oid
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> produceNew(String oid, String custId, String dupNo)
			throws CapException;

	/**
	 * 產生覆審工作底稿EXCEL
	 * 
	 * @param oid
	 * @return
	 * @throws Exception 
	 */
	boolean produceExcel(String oid) throws Exception;

	/**
	 * 刪除不覆審客戶
	 * 
	 * @param oid
	 */
	int deleteC241M01AAndC240M01B(String oid);

	/**
	 * 產生預定覆審名單EXCEL
	 * 
	 * @param oid
	 * @return
	 * @throws Exception 
	 */
	boolean producePreExcel(String oid) throws Exception;

	/**
	 * 產生驗證覆審名單
	 * 
	 * @param oid
	 * @return
	 * @throws CapException 
	 */
	boolean produceChkExcel(String oid) throws CapException;

	/**
	 * 上主檔刪除註記
	 * 
	 * @param oid
	 */
	void deleteMainMark(String oid);

	/**
	 * 檢查是否已有相同名單
	 * 
	 * @param branch
	 * @param dateYM
	 * @return
	 */
	Map<String, Object> checkAlreadyHave(String branch, String dateYM);

	/**
	 * 計算需覆審的為幾筆
	 * @param mainId
	 */
	int saveCTLCount(String mainId);

	/**
	 * 舊案計算覆審日
	 * @param map490
	 * 		RULE_NO(String) 覆審規則
	 * 		CUST_ID(String) 客戶ID
	 * 		DUPNO(String) 重複序號
	 * 		BR_NO(String) 分行代號
	 * 		CYC_MN(Date) 資料日期
	 * @return
	 * 		finalDate(Date) 覆審日
	 * 		rateDate(Date) 匯率日期
	 * 		twdRt(BigDecimal) 本位幣轉台幣匯率
	 * 		locRt(String) 轉本位幣匯率
	 */
	@SuppressWarnings("rawtypes")
	Map<String, Object> cauFinalDate(Map map490);

	/**
	 * 新案計算覆審日
	 * @param map490
	 * 		RULE_NO_NW(String) 覆審規則
	 * 		CYC_MN(Date) 資料日期
	 * 		CUST_ID(String) 客戶ID
	 * 		DUPNO(String) 重複序號
	 * 		BR_NO(String) 分行代號
	 * @return 
	 * 		finalDate(Date) 覆審日
	 * 		rateDate(Date) 匯率日期
	 * 		twdRt(BigDecimal) 本位幣轉台幣匯率
	 * 		locRt(String) 轉本位幣匯率
	 */
	@SuppressWarnings("rawtypes")
	Map<String, Object> cauFinalDateNew(Map map490);

	/**
	 * 利用mainId找C240M01A
	 * @param mainId
	 * @return
	 */
	C240M01A findC240M01AMByMainId(String mainId);

	/**
	 * 更新資料檔
	 * @param cycmn
	 * @param brno
	 * @return
	 */
	C240M01Z findCycmnBrno(Date cycmn, String brno);

	boolean findstaff(String custId);

}
