//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
var LMS1205S05Form06_grpFlag="";
var initDfd = initDfd || $.Deferred();
initDfd.done(function(){
	setCloseConfirm(true);
	var pageUrl = "../../lms/lms1205S05A";
	loadHtml(pageUrl, "5a");
	$("#lms1205s05a").click(function(){
		pageUrl = "../../lms/lms1205S05A";
		loadHtml(pageUrl, "5a");
	});
	$("#lms1205s05b").click(function(){
		pageUrl = "../../lms/lms1205S05B";
		loadHtml(pageUrl, "5b");
	});
	$("#lms1205s05c").click(function(){
		pageUrl = "../../lms/lms1205S05C";
		loadHtml(pageUrl, "5c");
	});
	$("#lms1205s05d").click(function(){
		pageUrl = "../../lms/lms1205S05D";
		loadHtml(pageUrl, "5d");		
	});
	$("#lms1205s05e").click(function(){
		pageUrl = "../../lms/lms1205S05E";
		loadHtml(pageUrl, "5e");
	});
	$("#lms1205s05f").click(function(){
		pageUrl = "../../lms/lms1205S05F";
		loadHtml(pageUrl, "5f");
	});
	$("#lms1205s05g").click(function(){
		pageUrl = "../../lms/lms1205S05G";
		loadHtml(pageUrl, "5g");
	});	
	
	var s61 = $("#tab-61"),table1 = s61.find("#table1"), s61radio2Div = s61.find("#s61radio2Div"), 
	s61t1=s61radio2Div.find("#s61t1"),s61t2=s61radio2Div.find("#s61t2"),
	s61t1t1=s61t1.find("#s61t1t1"),s61t2t1=s61t2.find("#s61t2t1"),
	pdMode="";
	
	table1.find("input[name=toM2]").click(function(){
		table1.find(".s61radio1-3")[$(this).val()=='3' ? "show" : "hide"]();
	}).end().find("input[name=editMode1]").click(function(){
		s61radio2Div.show();
		s61t1.find("#s61t1t" + $(this).val()).show().siblings("[id^=s61t1t]").hide();			
		s61t2.find("#s61t2t" + $(this).val()).show().siblings("[id^=s61t2t]").hide();;
	});
	
	s61radio2Div.find("input[name=ch6LstockInv]").click(function(){        		
		s61radio2Div.find("#ch6LstockInv-1")[$(this).val()=='1' ? "show" : "hide"]();
	});	
	
	//生產方式
	s61.find('input[name^="pd_Mode1"]').click(function(){
		pdMode="";
		s61.find('input[name^="pd_Mode1"]').each(function(){
			pdMode+=$(this).is(":checked")?"1":"0";
		});
		pdMode+="000000";
		$("#pd_Mode").val(pdMode);
	});

	$("#s61t1t1").find("#s61t1t1btnCal").click(function(){
		var inputs,ttlScore,idx,rate = ""; 
		for (var j=1;j<4;j++) {
			idx=1+(j*3);
			//產值加總
			inputs = s61t1t1.find("input[id^=pd][id$=_" + idx.toString() +"]");
			ttlScore=0;       		       	
    		inputs.each(function() {  
    			ttlScore += parseInt(($(this).val() ? $(this).val() : 0), 10);
    		});             		
    		s61t1t1.find("#tpd_qn"+ j.toString()).val(ttlScore); 	
    		
    		//計算產值比率
    		for(var k=1;k<6;k++){
    			if(ttlScore != 0){
    				var cellVal=s61t1t1.find("#pd"+ k.toString() + "_" +idx.toString()).val(); 
        			rate=((cellVal/ttlScore)*100).toFixed(2);
    			}
    			
    			s61t1t1.find("#pd"+ k.toString() + "_" + (idx+1).toString()).val(rate);
    		}            	       		            		
		}  
		
		//計算產值成長率
		for (var j=2;j<4;j++) {
			var c1=s61t1t1.find("#tpd_qn" + (j-1).toString()).val(); 
			var gdp = 0;
			if(c1 != "0"){
				var c2=s61t1t1.find("#tpd_qn" + j.toString()).val();
				gdp=(((c2-c1)/c1)*100).toFixed(2);
			}
			s61t1t1.find("#tpd_gp" + j.toString()).val(gdp);	
		}        		        		
	}).end().find("#s61t1t1btnlef").click(function(){
		
		if(s61t1t1.find("input[name^='pd'][name$='6'][value!=''],input[name^='pd'][name$='7'][value!='']").length == 0){
			return;
		}
		
		for (var i=1;i<6;i++) {
			for (var j=3;j<12;j++) {
				var c1="#pd" + i.toString() + "_" + j.toString();
				var c2="#pd" + i.toString() + "_" + (j+3).toString();
			       				
				if(s61t1t1.find(c2).length){
					var c2val=s61t1t1.find(c2).val();
    				s61t1t1.find(c1).val(c2val);         						        					
				}else{
					s61t1t1.find(c1).val("");	
				}				
			}						
		}
		
		//左移總計及產值成長率
		for (var i=1;i<4;i++) {
			var c1,c2,c2val;
			var shiftCol = new Array("#tpd_qn", "#tpd_gp", "#pdYa");
			for(var j=0;j<shiftCol.length;j++){
				var col = shiftCol[j];
				c1=col + i.toString();
				c2=col + (i+1).toString();
				if(s61t1t1.find(c2).length){ 
					c2val=s61t1t1.find(c2).val();
					s61t1t1.find(c1).val(c2val);
				}else{
					s61t1t1.find(c1).val("");	
				}
			}
		}
	}); 

	//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
//	if ($("#LMS1205S05Form06").find("input[name='grpFlag']:radio:checked").val() == "Y") {
//		chkShowHide_group(this, "Y");
//	}else if ($("#LMS1205S05Form06").find("input[name='grpFlag']:radio:checked").val() == "A") {
//		chkShowHide_group(this, "A");
//	}else if ($("#LMS1205S05Form06").find("input[name='grpFlag']:radio:checked").val() == "N") {
//		chkShowHide_group(this, "N");	
//	}else {
//		chkShowHide_group(this, "N");
//	}
	
	if($("#LMS1205S05Form07").find("input[name='rltFlag']:radio:checked").val()=="Y"){
		chkShowHide_rel(this,true);
	}else{
		chkShowHide_rel(this,false);
	}
});

function loadHtml(pageUrl, page){
	responseJSON["s05page"]=page;
	if($("#lms1205s05_panel").find("#tab-"+page).attr("openFlag") == "true"){
		$("#lms1205s05_panel").find("#tab-"+page).load(pageUrl, function(){
			setTimeout(function() {
			//查詢分頁資料
			$.ajax({		
				handler : "lms1205formhandler",
				type : "POST",
				dataType : "json",
				data : 
				{
					formAction : "queryL120S05",
					mainId : responseJSON.mainId,
					page: page }
				}).done(function(jsonInit) {
					var $LMS1205S05Form01 = $("#LMS1205S05Form01");
					var $LMS1205S05Form02 = $("#LMS1205S05Form02");
					var $LMS1205S05Form03 = $("#LMS1205S05Form03");
					var $LMS1205S05Form04 = $("#LMS1205S05Form04");
					var $LMS1205S05Form05 = $("#LMS1205S05Form05");
					//var $LMS1205S05Form06 = $("#LMS1205S05Form06");
					var $LMS1205S05Form07 = $("#LMS1205S05Form07");
					
					if(page == "5a"){
						$LMS1205S05Form01.reset();
						$LMS1205S05Form01.setData(jsonInit.LMS1205S05Form01,false);
					}else if(page == "5b"){
						$LMS1205S05Form02.reset();
						$LMS1205S05Form02.setData(jsonInit.LMS1205S05Form02,false);
						//開始設定checkBox
						if(!jsonInit.noCesCustId){	
							if(jsonInit.LMS1205S05Form02.runFlag == "Y"){
									$LMS1205S05Form02.find("#runFlag:checkbox").prop("checked",true);
									$LMS1205S05Form02.find("#tabs-2_show").show();
							}else{
									$LMS1205S05Form02.find("#runFlag:checkbox").prop("checked",false);
									$LMS1205S05Form02.find("#tabs-2_show").hide();
							}						
						}else{
								$LMS1205S05Form02.find("#runFlag:checkbox").prop("checked",false);
								$LMS1205S05Form02.find("#tabs-2_show").hide();							
						}
						//setCkeditor2("idDscr1",jsonInit.LMS1205S05Form02.idDscr1);
						//fixIECKEditorCache4ModelPopup("LMS1205S05Form02","idDscr1",true);
						$LMS1205S05Form02.readOnlyChilds(true);
					}else if(page == "5c"){
						$LMS1205S05Form03.reset();
			            $LMS1205S05Form03.find(".classfinItem").each(function(x){
			                // 初始化財務比率Table
			                $(this).hide();
			            });						
						$LMS1205S05Form03.setData(jsonInit.LMS1205S05Form03,false);
						if(!jsonInit.noCesCustId){
							if(jsonInit.LMS1205S05Form03.finFlag == "Y"){
								$LMS1205S05Form03.find("#finFlag:checkbox").prop("checked",true);
								$LMS1205S05Form03.find("#tabs-3_show").show();
							}else{
								$LMS1205S05Form03.find("#finFlag:checkbox").prop("checked",false);
								$LMS1205S05Form03.find("#tabs-3_show").hide();
							}
						}else{
							$LMS1205S05Form03.find("#finFlag:checkbox").prop("checked",false);
							$LMS1205S05Form03.find("#tabs-3_show").hide();							
						}
						
						$("#tabs-3_showTable tr:not('.head')").remove();
						if((jsonInit.LMS1205S05Form03.hasOwnProperty("finRatioName"))){
							var text = "";
							$.each(jsonInit.LMS1205S05Form03.finRatioName, function(key, value){
								//先畫出html，再將值塞進去
								text += "<tr id='' class='classfinItem'>"  +"\n";
								text += "<td class='hd1' style='text-align: center'>"+value+"</td>"  +"\n";
								text += "<td align='center'><input type='text' name='finRatio_C_"+key+"' class='field canEdit2 rt' id='finRatio_C_" + key+ "' readonly='readonly' /></td>" +"\n"
								text += "<td align='center'><input type='text' name='finRatio_B_"+key+"' class='field canEdit2 rt' id='finRatio_B_" + key+ "' readonly='readonly' /></td>" +"\n"
								text += "<td align='center'><input type='text' name='finRatio_A_"+key+"' class='field canEdit2 rt' id='finRatio_A_" + key+ "' readonly='readonly' /></td>" +"\n"
								text += "</tr>"
							});
							$("#tabs-3_showTable").append(text);
							
							if((jsonInit.LMS1205S05Form03.hasOwnProperty("finRatioC"))){
								$.each(jsonInit.LMS1205S05Form03.finRatioC, function(key, value){
									$("#finRatio_C_" + DOMPurify.sanitize(key)).val(value);
								});
							}
							if((jsonInit.LMS1205S05Form03.hasOwnProperty("finRatioB"))){
								$.each(jsonInit.LMS1205S05Form03.finRatioB, function(key, value){
									$("#finRatio_B_" + DOMPurify.sanitize(key)).val(value);
								});
							}
							if((jsonInit.LMS1205S05Form03.hasOwnProperty("finRatioA"))){
								$.each(jsonInit.LMS1205S05Form03.finRatioA, function(key, value){
									$("#finRatio_A_" + DOMPurify.sanitize(key)).val(value);
								});
							}
								
						}
						//setCkeditor2("idDscr2",jsonInit.LMS1205S05Form03.idDscr2);
						//fixIECKEditorCache4ModelPopup("LMS1205S05Form03","idDscr2",true);
						$LMS1205S05Form03.readOnlyChilds(true);
					}else if(page == "5d"){
						$LMS1205S05Form04.setData(jsonInit.LMS1205S05Form04,false);
						$LMS1205S05Form04.find("[name='longCaseFlag']:radio").each(function(i){
							var $this = $(this);
							if($this.val() == jsonInit.LMS1205S05Form04.longCaseFlag){
								if($this.val() == "Y"){
									$('#chk_radio1-1').show(); 
									$('#chk_radio1-2').show(); 
									$('#chk_radio1-2a1').show(); 
									$('#chk_radio1-2b').show();			
									$("select[name='longCaseDscr']").trigger("change");						
								}else{
									$('#chk_radio1-1').hide(); 
									$('#chk_radio1-2').hide(); 
									$('#chk_radio1-2a1').hide(); 
									$('#chk_radio1-2b').hide();									
								}
								$this.prop("checked",true);
							}
						});
					}else if(page == "5e"){
						$LMS1205S05Form05.reset();
						$LMS1205S05Form05.setData(jsonInit.LMS1205S05Form05,false);
						$LMS1205S05Form05.readOnlyChilds(true);
					}else if(page == "5f"){
						//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
						LMS1205S05Form06_grpFlag=jsonInit.LMS1205S05Form06.grpFlag;
						if(jsonInit.LMS1205S05Form06.grpFlag=="Y"){
							$("#grpShowContent1").load("../../lms/lms1205S05Fa",function(){
								
									//J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
									$(".toolTip").qtip({
										content: {text: $(this).attr("title")},
										style: {tip: {corner: 'topLeft'}}  //topLeft
									})
									$(".toolTip").mouseleave(function(){
										var tipid = $(this).data("qtip").id;
										$("#ui-tooltip-" + tipid).hide();
									})
								
								    var $LMS1205S05Form06 = $("#LMS1205S05Form06");
					             	initLoad_LMS1201S05Page06();
					             	
					             	//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
					    			var grpYear = jsonInit.LMS1205S05Form06.grpYear;
					    			var grpGrrd = jsonInit.LMS1205S05Form06.grpGrrd;
					    			if(grpYear){
					    				//判斷2017以後為新版，之前為舊版
					    				if(parseInt(grpYear, 10) >= 2017){
					    					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
					    			        //評等等級
					    			        $("#grpGrrd").setItems({
					    			            item: obj.GroupGrade2017,
					    			            format: "{key}"
					    			        });
					    	
					    				}else{
					    					var obj = CommonAPI.loadCombos(["GroupGrade"]);
					    			        //評等等級
					    			        $("#grpGrrd").setItems({
					    			            item: obj.GroupGrade,
					    			            format: "{key}"
					    			        });
					    				}
					    	
					    			}else{
					    				var obj = CommonAPI.loadCombos(["GroupGrade"]);
					    		        
					    		        //評等等級
					    		        $("#grpGrrd").setItems({
					    		            item: obj.GroupGrade,
					    		            format: "{key}"
					    		        });
					    			}
					    			
									$LMS1205S05Form06.reset();
									$LMS1205S05Form06.setData(jsonInit.LMS1205S05Form06,false);
									$LMS1205S05Form06.find("input[name='grpFlag'][value='Y']:radio").prop("checked",true );
									$LMS1205S05Form06.find('#groupButton').show();
									$LMS1205S05Form06.find('#groupContext').show();
									
									$LMS1205S05Form06.find("#endYear").val(RemoveStringComma($LMS1205S05Form06.find("#endYear").val()));
									if(jsonInit.LMS1205S05Form06._hLmtAmt){
										$LMS1205S05Form06.find("#hLmtAmt").hide();
									}else{
										$LMS1205S05Form06.find("#hLmtAmt").show();
									}
									if(jsonInit.LMS1205S05Form06._hGcrdAmt){
										$LMS1205S05Form06.find("#hGcrdAmt").hide();
									}else{
										$LMS1205S05Form06.find("#hGcrdAmt").show();
									}
			
									$LMS1205S05Form06.find(".groupDesc").hide();
									if(jsonInit.LMS1205S05Form06.grpGrrd =="6" || jsonInit.LMS1205S05Form06.grpGrrd == "7"){
										$LMS1205S05Form06.find("#grpGrdd"+ jsonInit.LMS1205S05Form06.grpGrrd +"Desc").show();
									}
								
									
									//J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
									if(jsonInit.LMS1205S05Form06.bondFlag =="1" ){
										//舊案
										$LMS1205S05Form06.find(".showBondFlag1").show();
										$LMS1205S05Form06.find(".showBondFlag2").hide();
									}else if(jsonInit.LMS1205S05Form06.bondFlag =="2" ){
										$LMS1205S05Form06.find(".showBondFlag1").hide();
										$LMS1205S05Form06.find(".showBondFlag2").show();	
									}else{
										$LMS1205S05Form06.find(".showBondFlag1").hide();
										$LMS1205S05Form06.find(".showBondFlag2").show();	
									}
									
									//J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
									//alert("loadHtml");
									if(jsonInit.LMS1205S05Form06.showGrpRate =="Y"){
										$('.showGrpRate').show();	
									}else{
										$('.showGrpRate').hide();
									}
							    
							});
							
						 
						}else if(jsonInit.LMS1205S05Form06.grpFlag=="A"){
							$("#grpShowContent1").load("../../lms/lms1205S05Fb",function(){
								    var $LMS1205S05Form06 = $("#LMS1205S05Form06");
									initLoad_LMS1201S05Page06();
									$LMS1205S05Form06.reset();
									$LMS1205S05Form06.setData(jsonInit.LMS1205S05Form06,false);
									$LMS1205S05Form06.find("input[name='grpFlag'][value='A']:radio").prop("checked",true );
									$LMS1205S05Form06.find('#groupButton').show();
									$LMS1205S05Form06.find('#groupContext').show();
						
									$LMS1205S05Form06.find("#endYear").val(RemoveStringComma($LMS1205S05Form06.find("#endYear").val()));
									if(jsonInit.LMS1205S05Form06._hLmtAmt){
										$LMS1205S05Form06.find("#hLmtAmt").hide();
									}else{
										$LMS1205S05Form06.find("#hLmtAmt").show();
									}
									if(jsonInit.LMS1205S05Form06._hGcrdAmt){
										$LMS1205S05Form06.find("#hGcrdAmt").hide();
									}else{
										$LMS1205S05Form06.find("#hGcrdAmt").show();
									}
			
									$LMS1205S05Form06.find(".groupDesc").hide();
									if(jsonInit.LMS1205S05Form06.grpGrrd =="6" || jsonInit.LMS1205S05Form06.grpGrrd == "7"){
										$LMS1205S05Form06.find("#grpGrdd"+ jsonInit.LMS1205S05Form06.grpGrrd +"Desc").show();
									}
																	
							});
							
						}else{
							//非集團企業
							$("#grpShowContent1").load("../../lms/lms1205S05Fa",function(){
								
									//J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
									$(".toolTip").qtip({
										content: {text: $(this).attr("title")},
										style: {tip: {corner: 'topLeft'}}  //topLeft
									})
									$(".toolTip").mouseleave(function(){
										var tipid = $(this).data("qtip").id;
										$("#ui-tooltip-" + tipid).hide();
									})
									
								    var $LMS1205S05Form06 = $("#LMS1205S05Form06");
									initLoad_LMS1201S05Page06();
									
									//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
					    			var grpYear = jsonInit.LMS1205S05Form06.grpYear;
					    			var grpGrrd = jsonInit.LMS1205S05Form06.grpGrrd;
					    			if(grpYear){
					    				//判斷2017以後為新版，之前為舊版
					    				if(parseInt(grpYear, 10) >= 2017){
					    					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
					    			        //評等等級
					    			        $("#grpGrrd").setItems({
					    			            item: obj.GroupGrade2017,
					    			            format: "{key}"
					    			        });
					    	
					    				}else{
					    					var obj = CommonAPI.loadCombos(["GroupGrade"]);
					    			        //評等等級
					    			        $("#grpGrrd").setItems({
					    			            item: obj.GroupGrade,
					    			            format: "{key}"
					    			        });
					    				}
					    	
					    			}else{
					    				var obj = CommonAPI.loadCombos(["GroupGrade"]);
					    		        
					    		        //評等等級
					    		        $("#grpGrrd").setItems({
					    		            item: obj.GroupGrade,
					    		            format: "{key}"
					    		        });
					    			}
					    			
									$LMS1205S05Form06.reset();
									$LMS1205S05Form06.setData(jsonInit.LMS1205S05Form06,false);
									$LMS1205S05Form06.find("input[name='grpFlag'][value='N']:radio").prop("checked",true );
									$LMS1205S05Form06.find('#groupButton').hide();
									$LMS1205S05Form06.find('#groupContext').hide();
									
									$LMS1205S05Form06.find("#endYear").val(RemoveStringComma($LMS1205S05Form06.find("#endYear").val()));
									if(jsonInit.LMS1205S05Form06._hLmtAmt){
										$LMS1205S05Form06.find("#hLmtAmt").hide();
									}else{
										$LMS1205S05Form06.find("#hLmtAmt").show();
									}
									if(jsonInit.LMS1205S05Form06._hGcrdAmt){
										$LMS1205S05Form06.find("#hGcrdAmt").hide();
									}else{
										$LMS1205S05Form06.find("#hGcrdAmt").show();
									}
			
									$LMS1205S05Form06.find(".groupDesc").hide();
									if(jsonInit.LMS1205S05Form06.grpGrrd =="6" || jsonInit.LMS1205S05Form06.grpGrrd == "7"){
										$LMS1205S05Form06.find("#grpGrdd"+ jsonInit.LMS1205S05Form06.grpGrrd +"Desc").show();
									}
									
									//J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
									$LMS1205S05Form06.find(".showBondFlag1").hide();
									$LMS1205S05Form06.find(".showBondFlag2").show();	
									
									//J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
									//alert("loadHtml");
									if(jsonInit.LMS1205S05Form06.showGrpRate =="Y"){
										$('.showGrpRate').show();	
									}else{
										$('.showGrpRate').hide();
									}

							});
							
						}
																
					}else{
						$LMS1205S05Form07.reset();
						$LMS1205S05Form07.setData(jsonInit.LMS1205S05Form07,false);
						if(jsonInit.LMS1205S05Form07.rltFlag == "Y"){
							$LMS1205S05Form07.find("input[name='rltFlag']:radio:eq(0)").prop("checked",true);
							$LMS1205S05Form07.find('#relButton').show();
							$LMS1205S05Form07.find('#relContext').show();
						}else{
							$LMS1205S05Form07.find("input[name='rltFlag']:radio:eq(0)").prop("checked",false);
							$LMS1205S05Form07.find('#relButton').hide();
							$LMS1205S05Form07.find('#relContext').hide();
						}
					}
					$("#showBorrowData").reset();
					$("#showBorrowData").setData(jsonInit.showBorrowData,false);	
					$("#lms1205s05_panel").find("#tab-"+page).attr("openFlag","false");

					if(page == "5a"){
					    $("#LMS1205S05Form01").find("button").hide();
					    $("#LMS1205S05Form01").find("#itemDscr01").prop("readonly", true);
					}
					if(responseJSON.readOnly == "true"){
						var $LMS1205S05Form01 = $("#LMS1205S05Form01");
						var $LMS1205S05Form02 = $("#LMS1205S05Form02");
						var $LMS1205S05Form03 = $("#LMS1205S05Form03");
						var $LMS1205S05Form04 = $("#LMS1205S05Form04");
						var $LMS1205S05Form05 = $("#LMS1205S05Form05");
						var $LMS1205S05Form06 = $("#LMS1205S05Form06");
						var $LMS1205S05Form07 = $("#LMS1205S05Form07");				
						if(page == "5a"){
							$LMS1205S05Form01.readOnlyChilds(true);
							$LMS1205S05Form01.find("button").hide();
							$LMS1205S05Form01.find("#itemDscr01").val(jsonInit.LMS1205S05Form01.itemDscr01);
							//setCkeditor2("itemDscr01",jsonInit.LMS1205S05Form01.itemDscr01);
						}else if(page == "5b"){
							$LMS1205S05Form02.readOnlyChilds(true);
							$LMS1205S05Form02.find("button").hide();
						}else if(page == "5c"){
							$LMS1205S05Form03.readOnlyChilds(true);
							$LMS1205S05Form03.find("button").hide();
						}else if(page == "5d"){
							$LMS1205S05Form04.readOnlyChilds(true);
							$LMS1205S05Form04.find("button").hide();
							if(responseJSON.mainDocStatus == "05O"){
								// 建霖說已核准案件中長期財務預估缺按鈕可以打開看登錄的內容，Miller add 2012/07/18
								$LMS1205S05Form04.find("#openRadio12a1").show();
							}
							$LMS1205S05Form04.find("#itemDscr02").val(jsonInit.LMS1205S05Form04.itemDscr02);
							//setCkeditor2("itemDscr02",jsonInit.LMS1205S05Form04.itemDscr02);					
						}else if(page == "5e"){
							$LMS1205S05Form05.readOnlyChilds(true);
							$LMS1205S05Form05.find("button").hide();
						}else if(page == "5f"){
							$LMS1205S05Form06.readOnlyChilds(true);
							$LMS1205S05Form06.find("button").hide();
							$LMS1205S05Form06.find(".grpBtn").show();
						}else if(page == "5g"){
							$LMS1205S05Form07.readOnlyChilds(true);
							$LMS1205S05Form07.find("button").hide();
							$LMS1205S05Form07.find(".relBtn").show();
						}				
					}					
			});
			}, 500);
			// 控制ThickBox唯讀狀態
			thickReadOnly();			
		});				
	}
}

function getCesA(title){
	cesGrid1();
	var grpSrc = $("#getData option:selected").val();
	//引進徵信資料按鈕ThickBox
	$("#getCesA").thickbox({     
		// 使用選取的內容進行彈窗
		title : i18n.lmss07["L1205S07.thickbox17"],
		width : 640,
		height : 320,
		modal : true,
		align : "center",
		valign : "bottom",	
		i18n:i18n.lmss07,
		buttons: {
			"L1205S07.thickbox1": function() {
				var $LMS1205S05Form06 = $("#LMS1205S05Form06");
				$LMS1205S05Form06.reset();
				var row1 = $("#cesGrid1").getGridParam('selrow'); 
				var list1 = "";
				var data1 = $("#cesGrid1").getRowData(row1);
				list1 = data1.mainId;
				list1 = (list1 == undefined ? "" : list1);
				$.thickbox.close();
				if(list1 != ""){
					$.ajax({		//查詢主要借款人資料
						handler : "lms1205formhandler",
						type : "POST",
						dataType : "json",
						data : 
						{
							formAction : "getJdbcData",
							cesMainId : list1,
							LMS1205S05Form06 : JSON.stringify($LMS1205S05Form06.serializeData()),
							mainId : responseJSON.mainId,
							grpSrc: grpSrc }
						}).done(function(json) {
							var $LMS1205S05Form06 = $("#LMS1205S05Form06");
							
							//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
			    			var grpYear = json.LMS1205S05Form06.grpYear;
			    			var grpGrrd = json.LMS1205S05Form06.grpGrrd;
			    			if(grpYear){
			    				//判斷2017以後為新版，之前為舊版
			    				if(parseInt(grpYear, 10) >= 2017){
			    					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
			    			        //評等等級
			    			        $("#grpGrrd").setItems({
			    			            item: obj.GroupGrade2017,
			    			            format: "{key}"
			    			        });
			    	
			    				}else{
			    					var obj = CommonAPI.loadCombos(["GroupGrade"]);
			    			        //評等等級
			    			        $("#grpGrrd").setItems({
			    			            item: obj.GroupGrade,
			    			            format: "{key}"
			    			        });
			    				}
			    	
			    			}else{
			    				var obj = CommonAPI.loadCombos(["GroupGrade"]);
			    		        
			    		        //評等等級
			    		        $("#grpGrrd").setItems({
			    		            item: obj.GroupGrade,
			    		            format: "{key}"
			    		        });
			    			}
			    			
							$LMS1205S05Form06.find("#grpSrc").html(title);
							$LMS1205S05Form06.setData(json.LMS1205S05Form06);
							if(json.LMS1205S05Form06._hLmtAmt){
								$LMS1205S05Form06.find("#hLmtAmt").hide();
							}else{
								$LMS1205S05Form06.find("#hLmtAmt").show();
							}
							if(json.LMS1205S05Form06._hGcrdAmt){
								$LMS1205S05Form06.find("#hGcrdAmt").hide();
							}else{
								$LMS1205S05Form06.find("#hGcrdAmt").show();
							}
							//$("#LMS1205S05Form06").find("#grpSrc").html(grpSrc);
							if(json.LMS1205S05Form06.grpFlag == "Y"){
								//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
								//$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(0)").attr("checked",true);
								$LMS1205S05Form06.find("input[name='grpFlag'][value='Y']:radio").prop("checked",true );
								$('#groupButton').show();
								$('#groupContext').show();
								$LMS1205S05Form06.find("#endYear").val(RemoveStringComma($LMS1205S05Form06.find("#endYear").val()));
							}else{
								//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
								//$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(1)").attr("checked",true);
								$LMS1205S05Form06.find("input[name='grpFlag'][value='N']:radio").prop("checked",true );
								$('#groupButton').hide();
								$('#groupContext').hide();								
							}				
							
							//J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
							//alert("getCesA");
							if(json.LMS1205S05Form06.showGrpRate =="Y"){
								$('.showGrpRate').show();	
							}else{
								$('.showGrpRate').hide();
							}
							
							//J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
							if(json.LMS1205S05Form06.bondFlag =="1" ){
								//舊案
								$LMS1205S05Form06.find(".showBondFlag1").show();
								$LMS1205S05Form06.find(".showBondFlag2").hide();
							}else if(json.LMS1205S05Form06.bondFlag =="2" ){
								$LMS1205S05Form06.find(".showBondFlag1").hide();
								$LMS1205S05Form06.find(".showBondFlag2").show();	
							}else{
								$LMS1205S05Form06.find(".showBondFlag1").hide();
								$LMS1205S05Form06.find(".showBondFlag2").show();	
							}
							
							
							$("#Panel06Gridview01").trigger("reloadGrid");	//更新Grid內容
							$("#Panel06Gridview02").trigger("reloadGrid");	//更新Grid內容
					});
				}else{
					CommonAPI.showMessage(i18n.lmss07["L1205S07.alert2"]);
				}
			},								             
			"L1205S07.thickbox2": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

function chkShowHide_group(obj,chkShow) {
	//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「信信用風險管理遵循檢核表」。
//	if (obj.checked && chkShow) {
//		$('#groupButton').show();
//		$('#groupContext').show();
//	} else {
//		$('#groupButton').hide();
//		$('#groupContext').hide();
//	}

    if (obj.checked && chkShow == "Y") {
		//l120s05.confirm2=現有集團企業資料會被清除，是否繼續？
		
		API.confirmMessage(i18n.lms1205s05['l120s05.confirm2'], function(res){
			
			if(res){
				
				$("#grpShowContent1").load("../../lms/lms1205S05Fa", function(){
					
					//J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
					$(".toolTip").qtip({
						content: {text: $(this).attr("title")},
						style: {tip: {corner: 'topLeft'}}  //topLeft
					})
					$(".toolTip").mouseleave(function(){
						var tipid = $(this).data("qtip").id;
						$("#ui-tooltip-" + tipid).hide();
					})
					
					var $LMS1205S05Form06 = $("#LMS1205S05Form06");
					initLoad_LMS1201S05Page06();
					
					//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
	    			var grpYear = $LMS1205S05Form06.find("#grpYear").html();
	    			var grpGrrd = $LMS1205S05Form06.find("#grpGrrd").html();
	    			
	    			if(grpYear){
	    				//判斷2017以後為新版，之前為舊版
	    				if(parseInt(grpYear, 10) >= 2017){
	    					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
	    			        //評等等級
	    			        $("#grpGrrd").setItems({
	    			            item: obj.GroupGrade2017,
	    			            format: "{key}"
	    			        });
	    	
	    				}else{
	    					var obj = CommonAPI.loadCombos(["GroupGrade"]);
	    			        //評等等級
	    			        $("#grpGrrd").setItems({
	    			            item: obj.GroupGrade,
	    			            format: "{key}"
	    			        });
	    				}
	    	
	    			}else{
	    				var obj = CommonAPI.loadCombos(["GroupGrade"]);
	    		        
	    		        //評等等級
	    		        $("#grpGrrd").setItems({
	    		            item: obj.GroupGrade,
	    		            format: "{key}"
	    		        });
	    			}
	    			
					$LMS1205S05Form06.reset();
					$LMS1205S05Form06.find("input[name='grpFlag'][value='Y']:radio").prop("checked",true );
					LMS1205S05Form06_grpFlag="Y";
					$('#groupButton').show();
				    $('#groupContext').show();
				    //J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
				    $('.showGrpRate').hide();
				    
				    //J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
				    if($LMS1205S05Form06.find("#bondFlag").val() =="1" ){
						//舊案
						$LMS1205S05Form06.find(".showBondFlag1").show();
						$LMS1205S05Form06.find(".showBondFlag2").hide();
					}else if($LMS1205S05Form06.find("#bondFlag").val() =="2" ){
						$LMS1205S05Form06.find(".showBondFlag1").hide();
						$LMS1205S05Form06.find(".showBondFlag2").show();	
					}else{
						$LMS1205S05Form06.find(".showBondFlag1").hide();
						$LMS1205S05Form06.find(".showBondFlag2").show();	
					}
				    
				});
			}else{
				 $("#LMS1205S05Form06").find("input[name='grpFlag'][value='"+LMS1205S05Form06_grpFlag+"']:radio").prop("checked",true );
			}
        });

	}else if (obj.checked && chkShow == "A") {
		    
		//l120s05.confirm2=現有集團企業資料會被清除，是否繼續？
		API.confirmMessage(i18n.lms1205s05['l120s05.confirm2'], function(res){
			if(res){
				$("#grpShowContent1").load("../../lms/lms1205S05Fb", function(){
					var $LMS1205S05Form06 = $("#LMS1205S05Form06");
					initLoad_LMS1201S05Page06();
					$LMS1205S05Form06.reset();
					$LMS1205S05Form06.find("input[name='grpFlag'][value='A']:radio").prop("checked",true );
					LMS1205S05Form06_grpFlag="A";
					$('#groupButton').show();
				    $('#groupContext').show();
				});
			}else{
				 $("#LMS1205S05Form06").find("input[name='grpFlag'][value='"+LMS1205S05Form06_grpFlag+"']:radio").prop("checked",true );
			}
        });	

	}else if (obj.checked && chkShow == "N") {	
	        
	        var $LMS1205S05Form06 = $("#LMS1205S05Form06");
		    //$("#grpShowContent1").load("../../lms/lms1201S05Fa", function(){
			//var $LMS1205S05Form06 = $("#LMS1205S05Form06");
			//initLoad_LMS1201S05Page06();
			$LMS1205S05Form06.reset();
			$LMS1205S05Form06.find("input[name='grpFlag'][value='N']:radio").prop("checked",true );
			LMS1205S05Form06_grpFlag="N";
			$('#groupButton').hide();
			$('#groupContext').hide();
		//});

    }else{
		    
		    var $LMS1205S05Form06 = $("#LMS1205S05Form06");
		//$("#grpShowContent1").load("../../lms/lms1201S05Fa", function(){
			//var $LMS1205S05Form06 = $("#LMS1205S05Form06");
			//initLoad_LMS1201S05Page06();
			$LMS1205S05Form06.reset();
			$LMS1205S05Form06.find("input[name='grpFlag'][value='N']:radio").prop("checked",true );
			$('#groupButton').hide();
			$('#groupContext').hide();
		//});
	}	

}

/**
 * 顯示隱藏關係企業引進按鈕
 * @param obj
 * @param chkShow
 */
function chkShowHide_rel(obj,chkShow) {
    if (obj.checked && chkShow) {
    	$('#relButton').show();
        $('#relContext').show();
    } else {
    	$('#relButton').hide();
        $('#relContext').hide();
    }
  }

/**
 * 集團企業修改功能
 * @param obj
 * @param chkShow
 */
function editContent() {
	var $gformEditable = $("#gformEditable");
	// 將唯讀內容塞值到可編輯內容
	var $LMS1205S05Form06 = $('#LMS1205S05Form06');
	var grpSrc = $LMS1205S05Form06.find("#grpSrc").html();	
	//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
	var grpFlag = $LMS1205S05Form06.find("input[name='grpFlag']:radio:checked").val();
	if(grpFlag=="Y"){
		if(grpSrc == undefined || grpSrc == null || grpSrc == ""){
			// 尚未引進集團企業資料，無法執行本功能
			CommonAPI.showMessage(i18n.lms1205s05["l120s05.error2"]);
		    return;
		}
	}
	
	var editCol = [];
	$gformEditable.find("input").each(function(){
		var $input = $(this);
		var id = $input.attr('id');
		var rid = id.substring(1);
		editCol.push(rid);
		var $item = $LMS1205S05Form06.find('#'+rid);
		$input.val($item.val());
	});
	// 手動針對特別欄位設值
	$gformEditable.find("#_grpNo").val($LMS1205S05Form06.find("#grpNo").val());
	$gformEditable.find("#_inqDate").val($LMS1205S05Form06.find("#inqDate").val());
	$gformEditable.find("#_grpOver option").each(function(i){
		if($LMS1205S05Form06.find("#grpOver").html() == $(this).html()){
			$(this).prop("selected",true);
		}
	});
	
	
	// J-107-0087-001 Web
	// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
	if(grpFlag=="Y"){
		var grpGrrd = $LMS1205S05Form06.find("#grpGrrd").val();
		var grpYear = $LMS1205S05Form06.find("#grpYear").val();
		
		if(grpYear){
			//判斷2017以後為新版，之前為舊版
			if(parseInt(grpYear, 10) >= 2017){
				var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
		        //評等等級
		        $("#_grpGrrd").setItems({
		            item: obj.GroupGrade2017,
		            format: "{key}"
		        });
		        
		        if(grpGrrd == "" || grpGrrd == null || grpGrrd == undefined){
		    		$gformEditable.find("#_grpGrrd option:eq(0)").prop("selected",true);
		    	}else{
		    				
		    		$gformEditable.find("#_grpGrrd option").each(function(i){
		    			if(grpGrrd == $(this).val()){
		    				$(this).prop("selected",true);
		    			}
		    		});
		    	}

			}else{
				var obj = CommonAPI.loadCombos(["GroupGrade"]);
		        //評等等級
		        $("#_grpGrrd").setItems({
		            item: obj.GroupGrade,
		            format: "{key}"
		        });
		        
		        if(grpGrrd == "" || grpGrrd == null || grpGrrd == undefined){
		    		$gformEditable.find("#_grpGrrd option:eq(0)").prop("selected",true);
		    	}else{
		    				
		    		$gformEditable.find("#_grpGrrd option").each(function(i){
		    			if(grpGrrd == $(this).val()){
		    				$(this).prop("selected",true);
		    			}
		    		});
		    	}
			}

		}else{
			var obj = CommonAPI.loadCombos(["GroupGrade"]);
	        
	        //評等等級
	        $("#_grpGrrd").setItems({
	            item: obj.GroupGrade,
	            format: "{key}"
	        });
	        
	        if(grpGrrd == "" || grpGrrd == null || grpGrrd == undefined){
	    		$gformEditable.find("#_grpGrrd option:eq(0)").prop("selected",true);
	    	}else{
	    				
	    		$gformEditable.find("#_grpGrrd option").each(function(i){
	    			if(grpGrrd == $(this).val()){
	    				$(this).prop("selected",true);
	    			}
	    		});
	    	}

		}
		
	}
	
	editCol.push("grpNo");
	editCol.push("inqDate");
	editCol.push("grpOver");
	$gformEditable.find("#_endYear").val(RemoveStringComma($gformEditable.find("#_endYear").val()));
	
	//J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
	if($LMS1205S05Form06.find("#bondFlag").val() =="1" ){
		//舊案
		$gformEditable.find(".showBondFlag1").show();
		$gformEditable.find(".showBondFlag2").hide();
	}else if($LMS1205S05Form06.find("#bondFlag").val() =="2" ){
		$gformEditable.find(".showBondFlag1").hide();
		$gformEditable.find(".showBondFlag2").show();	
	}else{
		$gformEditable.find(".showBondFlag1").hide();
		$gformEditable.find(".showBondFlag2").show();	
	}
	
	
	$("#grpEditable").thickbox({     // 使用選取的內容進行彈窗
		title : i18n.lms1205s05["l120s05.thickbox14"],
		width : 800,
		height : 480,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.def,
		buttons: {
			"sure": function() {
			    $gformEditable.find(".number").each(function(i){
			        $(this).val(RemoveStringComma($(this).val()));
			    });				
				if($gformEditable.valid()){					
					// 修改功能
					$.ajax({		//查詢主要借款人資料
						handler : "lms1205formhandler",
						type : "POST",
						dataType : "json",
						data : 
						{
							formAction : "saveL120S05A",
							mainId : responseJSON.mainId,
							editCol: editCol,
							gformEditable : JSON.stringify($gformEditable.serializeData()),
							_grpOver: $gformEditable.find("#_grpOver option:selected").val()
						}
						}).done(function(json) {
							$LMS1205S05Form06.setData(json.LMS1205S05Form06, false);
							if(json.LMS1205S05Form06._hLmtAmt){
								$LMS1205S05Form06.find("#hLmtAmt").hide();
							}else{
								$LMS1205S05Form06.find("#hLmtAmt").show();
							}
							if(json.LMS1205S05Form06._hGcrdAmt){
								$LMS1205S05Form06.find("#hGcrdAmt").hide();
							}else{
								$LMS1205S05Form06.find("#hGcrdAmt").show();
							}							
							$.thickbox.close();
							$.thickbox.close();
							CommonAPI.showMessage(json.NOTIFY_MESSAGE);
					});					
				}		
			},            
			"cancel": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							// 初始化修改欄位
							$("#gformEditable").reset();
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

/**
 * 關係企業修改功能
 * @param obj
 * @param chkShow
 */
function editContent2() {
	var $rformEditable = $("#rformEditable");
	// 將唯讀內容塞值到可編輯內容
	var $LMS1205S05Form07 = $('#LMS1205S05Form07');
	var inqDate = $LMS1205S05Form07.find("#inqDate").html();	
	if(inqDate == undefined || inqDate == null || inqDate == ""){
		// 尚未引進關係企業資料，無法執行本功能
		CommonAPI.showMessage(i18n.lms1205s05["l120s05.error3"]);
		return;
	}
	var editCol = [];
	$rformEditable.find("input").each(function(){
		var $input = $(this);
		var id = $input.attr('id');
		var rid = id.substring(1);
		editCol.push(rid);
		var $item = $LMS1205S05Form07.find('#'+rid);
		$input.val($item.html());
	});
	// 手動針對特別欄位設值
	$rformEditable.find("#_rltOver option").each(function(i){
		if($LMS1205S05Form07.find("#rltOver").html() == $(this).html()){
			$(this).prop("selected",true);
		}
	});
	editCol.push("rltOver");
	$("#relEditable").thickbox({     // 使用選取的內容進行彈窗
		title : i18n.lms1205s05["l120s05.thickbox14"],
		width : 800,
		height : 480,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.def,
		buttons: {
			"sure": function() {
			    $rformEditable.find(".number").each(function(i){
			        $(this).val(RemoveStringComma($(this).val()));
			    });				
				if ($rformEditable.valid()) {					
					// 修改功能
					$.ajax({		//查詢主要借款人資料
						handler : "lms1205formhandler",
						type : "POST",
						dataType : "json",
						data : 
						{
							formAction : "saveL120S05C",
							mainId : responseJSON.mainId,
							editCol: editCol,
							rformEditable : JSON.stringify($rformEditable.serializeData()),
							_rltOver: $rformEditable.find("#_rltOver option:selected").val()
						}
						}).done(function(json) {
							$LMS1205S05Form07.setData(json.LMS1205S05Form07, false);
							$.thickbox.close();
							$.thickbox.close();
							CommonAPI.showMessage(json.NOTIFY_MESSAGE);
					});				
				}		
			},            
			"cancel": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							// 初始化修改欄位
							$("#rformEditable").reset();
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

function getRelData(){
	var $LMS1205S05Form07 = $("#LMS1205S05Form07");
	$.ajax({		//查詢主要借款人資料
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			LMS1205S05Form07 : JSON.stringify($LMS1205S05Form07.serializeData())
		},		
		action : "getJdbcRelData"
		}).done(function(json) {
			var $LMS1205S05Form07 = $("#LMS1205S05Form07");
			$LMS1205S05Form07.setData(json.LMS1205S05Form07);
			if(json.LMS1205S05Form07.rltFlag == "Y"){
				$LMS1205S05Form07.find("input[name='rltFlag']:radio:eq(0)").prop("checked",true);
		    	$('#relButton').show();
		      	$('#relContext').show();
			}else{
				$LMS1205S05Form07.find("input[name='rltFlag']:radio:eq(1)").prop("checked",true);
		    	$('#relButton').hide();
		      	$('#relContext').hide();								
			}
			$("#Panel07Gridview01").trigger("reloadGrid");	//更新Grid內容
			$("#Panel07Gridview02").trigger("reloadGrid");	//更新Grid內容
	});	
}

function A_1_3g(){
	$("#openbox").thickbox({     // 使用選取的內容進行彈窗
		title : i18n.lms1205s05["l120s05.thickbox6"],
		width : 800,
		height : 300,
		modal : true,
		align : 'center',
		valign: 'bottom',
		i18n:i18n.lms1205s05,
		buttons: {
			"l120s05.thickbox1": function() {
				var $LMS1205S05Form06 = $("#LMS1205S05Form06");
				if($("#getData option:selected").val()=="1"){					
					var title = $("#getData option:selected").html();
					var grpSrcN = $("#getData option:selected").html();
					$.thickbox.close();	
					getCesA(title);
				}else{
					var grpSrcN = $("#getData option:selected").html();
					$.thickbox.close();	
					$.ajax({		//查詢主要借款人資料
						handler : "lms1205formhandler",
						type : "POST",
						dataType : "json",
						data : 
						{
							formAction : "getJdbcData",
							cesMainId : "",
							LMS1205S05Form06 : JSON.stringify($LMS1205S05Form06.serializeData()),
							mainId : responseJSON.mainId,
							grpSrc: $("#getData option:selected").val()
						}
						}).done(function(json) {
							var $LMS1205S05Form06 = $("#LMS1205S05Form06");
							$LMS1205S05Form06.find("#grpSrc")
							.html($("#getData option:selected").html());
							
							//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
			    			var grpYear = json.LMS1205S05Form06.grpYear;
			    			var grpGrrd = json.LMS1205S05Form06.grpGrrd;
			    			 
			    			if(grpYear){
			    				//判斷2017以後為新版，之前為舊版
			    				if(parseInt(grpYear, 10) >= 2017){
			    					var obj = CommonAPI.loadCombos(["GroupGrade2017"]); 
			    			        //評等等級
			    			        $("#grpGrrd").setItems({
			    			            item: obj.GroupGrade2017,
			    			            format: "{key}"
			    			        });
			    	
			    				}else{
			    					var obj = CommonAPI.loadCombos(["GroupGrade"]);
			    			        //評等等級
			    			        $("#grpGrrd").setItems({
			    			            item: obj.GroupGrade,
			    			            format: "{key}"
			    			        });
			    				}
			    	
			    			}else{
			    				var obj = CommonAPI.loadCombos(["GroupGrade"]);
			    		        
			    		        //評等等級
			    		        $("#grpGrrd").setItems({
			    		            item: obj.GroupGrade,
			    		            format: "{key}"
			    		        });
			    			}
			    			
							$LMS1205S05Form06.setData(json, false);
							$LMS1205S05Form06.find("#grpSrc").html(grpSrcN);
                            //J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
							//$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(0)").attr("checked",true);
//							$LMS1205S05Form06.find("input[name='grpFlag'][value='Y']:radio").attr("checked",true );
							if(json.LMS1205S05Form06._hLmtAmt){
								$LMS1205S05Form06.find("#hLmtAmt").hide();
							}else{
								$LMS1205S05Form06.find("#hLmtAmt").show();
							}
							if(json.LMS1205S05Form06._hGcrdAmt){
								$LMS1205S05Form06.find("#hGcrdAmt").hide();
							}else{
								$LMS1205S05Form06.find("#hGcrdAmt").show();
							}
							
							if(json.LMS1205S05Form06.grpFlag == "Y"){
								//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
								//$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(0)").attr("checked",true);
								$LMS1205S05Form06.find("input[name='grpFlag'][value='Y']:radio").prop("checked",true );
								$('#groupButton').show();
								$('#groupContext').show();
								$LMS1205S05Form06.find("#endYear").val(RemoveStringComma($LMS1205S05Form06.find("#endYear").val()));
							}else{
								//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
								//$LMS1205S05Form06.find("input[name='grpFlag']:radio:eq(1)").attr("checked",true);
								$LMS1205S05Form06.find("input[name='grpFlag'][value='N']:radio").prop("checked",true );
								$('#groupButton').hide();
								$('#groupContext').hide();								
							}							
							
							//J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊
							//alert("A_1_3g");
							if(json.LMS1205S05Form06.showGrpRate =="Y"){
								$('.showGrpRate').show();	
							}else{
								$('.showGrpRate').hide();
							}
							
							//J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
							//J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍
							if(json.LMS1205S05Form06.bondFlag =="1" ){
								//舊案
								$LMS1205S05Form06.find(".showBondFlag1").show();
								$LMS1205S05Form06.find(".showBondFlag2").hide();
							}else if(json.LMS1205S05Form06.bondFlag =="2" ){
								$LMS1205S05Form06.find(".showBondFlag1").hide();
								$LMS1205S05Form06.find(".showBondFlag2").show();	
							}else{
								$LMS1205S05Form06.find(".showBondFlag1").hide();
								$LMS1205S05Form06.find(".showBondFlag2").show();	
							}
							
							$("#Panel06Gridview01").trigger("reloadGrid");	//更新Grid內容
							$("#Panel06Gridview02").trigger("reloadGrid");	//更新Grid內容
					});	
				}		
			},            
			"l120s05.thickbox2": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
//A.符合銀行法標準「董事長 (或代表公司董事、執行業務股東) 或總經理與他公司之董事長 (或代表公司董事、執行業務股東) 或總經理為同一人或具有配偶之關係者」。
function A_1_3g_A(){
	var $LMS1205S05Form06 = $("#LMS1205S05Form06");
	$.ajax({		
	    //查詢主要借款人資料
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "getJdbcDataRelGroup",
			cesMainId : "",
			LMS1205S05Form06 : JSON.stringify($LMS1205S05Form06.serializeData()),
			mainId : responseJSON.mainId,
			grpSrc: $("#getData option:selected").val()
		}
		}).done(function(json) {
			var $LMS1205S05Form06 = $("#LMS1205S05Form06");
			$LMS1205S05Form06.setData(json.LMS1205S05Form06, false);

			if(json.LMS1205S05Form06.grpFlag == "A"){
				$LMS1205S05Form06.find("input[name='grpFlag'][value='A']:radio").prop("checked",true );
		    	$('#groupButton').show();
		      	$('#groupContext').show();
			}else{
				$LMS1205S05Form06.find("input[name='grpFlag'][value='N']:radio").prop("checked",true );
		    	$('#groupButton').hide();
		      	$('#groupContext').hide();								
			}
			
			$("#Panel06Gridview01").trigger("reloadGrid");	//更新Grid內容
			$("#Panel06Gridview02").trigger("reloadGrid");	//更新Grid內容
	});	
}

function ShowOrHide(button_act,sh_item) {
	var chk_value=$('#'+button_act).html();
	//alert(chk_value);
	if (chk_value==i18n.lms1205s05["l120s05.btn8"]) {
		$('#'+sh_item).show();
		$('#'+button_act).html(i18n.lms1205s05["l120s05.btn9"]);
	} else {
		$('#'+sh_item).hide();
		$('#'+button_act).html(i18n.lms1205s05["l120s05.btn8"]);
	}
	if(sh_item == 'GroupDetail1') {
		uPanel06Gridview01();
	}else if(sh_item == 'GroupDetail2') {
		uPanel06Gridview02();
	}else if(sh_item == 'GroupDetail3'){
		uPanel07Gridview01();
	}else if(sh_item == 'GroupDetail4'){
		uPanel07Gridview02();
	}
}

/**
 * 隱藏顯示財務比率項目
 * @param obj
 * @param idName
 */
function showHide(obj,idName) {	//隱藏顯示財務比率項目
	if (obj.checked) {
		$('#'+idName).show();
	} else {
		$('#'+idName).hide();
	}
}

/**
 * 檢查財務比率有無資料，有資料則顯示
 */
function checkExist(){
	$("#LMS1205S05Form03").find("#tabs-3_show span").each(function(b){
		if($(this).html()!=""){			
			$(this).parent().parent().show();
		}
	});
}

function buildL120s01eKind2(json){
	$("#tabs-3_showTable tr:not('.head')").remove();
	if((json.LMS1205S05Form03.hasOwnProperty("finRatioName"))){
		var text = "";
		$.each(json.LMS1205S05Form03.finRatioName, function(key, value){
			//先畫出html，再將值塞進去
			text += "<tr id='' class='classfinItem'>"  +"\n";
			text += "<td class='hd1' style='text-align: center'>"+value+"</td>"  +"\n";
			text += "<td align='center'><input type='text' name='finRatio_C_"+key+"' class='field canEdit2 rt' id='finRatio_C_" + key+ "' readonly='readonly' /></td>" +"\n"
			text += "<td align='center'><input type='text' name='finRatio_B_"+key+"' class='field canEdit2 rt' id='finRatio_B_" + key+ "' readonly='readonly' /></td>" +"\n"
			text += "<td align='center'><input type='text' name='finRatio_A_"+key+"' class='field canEdit2 rt' id='finRatio_A_" + key+ "' readonly='readonly' /></td>" +"\n"
			text += "</tr>"
		});
		$("#tabs-3_showTable").append(text);
		
		if((json.LMS1205S05Form03.hasOwnProperty("finRatioC"))){
			$.each(json.LMS1205S05Form03.finRatioC, function(key, value){
				$("#finRatio_C_" + key).val(value);
			});
		}
		if((json.LMS1205S05Form03.hasOwnProperty("finRatioB"))){
			$.each(json.LMS1205S05Form03.finRatioB, function(key, value){
				$("#finRatio_B_" + key).val(value);
			});
		}
		if((json.LMS1205S05Form03.hasOwnProperty("finRatioA"))){
			$.each(json.LMS1205S05Form03.finRatioA, function(key, value){
				$("#finRatio_A_" + key).val(value);
			});
		}
		
		$("#finRatioYear_C").val("").val(json.LMS1205S05Form03.finRatioYear_C);
		$("#finRatioYear_B").val("").val(json.LMS1205S05Form03.finRatioYear_B);
		$("#finRatioYear_A").val("").val(json.LMS1205S05Form03.finRatioYear_A);
	}	
}

function openperson(){
	$.ajax({
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "checkBorrow",
			mainId : responseJSON.mainId
		}
		}).done(function(json) {
			uGridborrow();
			$("#openperson").thickbox({     // 使用選取的內容進行彈窗
				title : i18n.lms1205s05["l120s05.thickbox7"],
				width : 600,
				height : 500,
				modal : true,
				valign: "bottom",
				align: "center",
				i18n:i18n.def,
				buttons: {             
					"sure": function() {
						var gridId = $("#gridborrow").getGridParam('selrow');
						var gridData = $("#gridborrow").getRowData(gridId);
						var $LMS1205S05Form02 = $("#LMS1205S05Form02");
						var $LMS1205S05Form03 = $("#LMS1205S05Form03");
						var $LMS1205S05Form05 = $("#LMS1205S05Form05");
						
						if(gridId == null){
							CommonAPI.showMessage(i18n.lms1205s05["l120s05.alert2"]);
						}else{
							$.thickbox.close();
							$.ajax({		//查詢主要借款人資料
								handler : "lms1205formhandler",
								type : "POST",
								dataType : "json",
								data : 
								{
									formAction : "queryToGetData",
									oid : gridData.oid,
									idDscr1 : "",
									idDscr2 : ""									
								}
								}).done(function(json) {
									

									
						            $("#LMS1205S05Form03").find(".classfinItem").each(function(x){
						                // 初始化財務比率Table
						                $(this).hide();
						            });									
									if($LMS1205S05Form02.length > 0){
										//$LMS1205S05Form02.reset();
										$LMS1205S05Form02.setData(json.LMS1205S05Form02,false);
										
										//idDscr1 FIREFOX 塞值後顯示為空白，但值其實有塞進來，是因為
										//jquery-watch.js  FIREFOX 沒有支援o.watch，所以實作的部分setTimeout 100秒與Data有衝突，所以此JS 先setTimeout110再塞值就OK
										setTimeout(function(){
											$LMS1205S05Form02.find("#idDscr1").val(json.formIdDscr1.idDscr1);
										}, 110);
										
										//setCkeditor2("idDscr1",json.formIdDscr1.idDscr1);
									}
									if($LMS1205S05Form03.length > 0){
										//$LMS1205S05Form03.reset();
										$LMS1205S05Form03.setData(json.LMS1205S05Form03,false);
										//idDscr1 FIREFOX 塞值後顯示為空白，但值其實有塞進來，是因為
										//jquery-watch.js  FIREFOX 沒有支援o.watch，所以實作的部分setTimeout 100秒與Data有衝突，所以此JS 先setTimeout110再塞值就OK
										setTimeout(function(){
											$LMS1205S05Form03.find("#idDscr2").val(json.formIdDscr2.idDscr2);
										}, 110);
										//setCkeditor2("idDscr2",json.formIdDscr2.idDscr2);
									}
									
									buildL120s01eKind2(json);
									
									if($LMS1205S05Form05.length > 0){
										$LMS1205S05Form05.setData(json.LMS1205S05Form05);
									}						
									//開始設定checkBox
									if(json.LMS1205S05Form02.runFlag == "Y"){	
											$LMS1205S05Form02.find("#runFlag:checkbox").prop("checked",true);
											$LMS1205S05Form02.find("#tabs-2_show").show();
									}else{	
											$LMS1205S05Form02.find("#runFlag:checkbox").prop("checked",false);
											$LMS1205S05Form02.find("#tabs-2_show").hide();
									}
									if(json.LMS1205S05Form03.finFlag == "Y"){
										$LMS1205S05Form03.find("#finFlag:checkbox").prop("checked",true);
										$LMS1205S05Form03.find("#tabs-3_show").show();
									}else{
										$LMS1205S05Form03.find("#finFlag:checkbox").prop("checked",false);
										$LMS1205S05Form03.find("#tabs-3_show").hide();
									}
									
									
									
//									//開始設定分析與評估
//									setCkeditor2("idDscr1",json.LMS1205S05Form02.idDscr1);
//									setCkeditor2("idDscr2",json.LMS1205S05Form03.idDscr2);
//									$LMS1205S05Form02.find("#idDscr1").val(json.LMS1205S05Form02.idDscr1);
//									$LMS1205S05Form03.find("#idDscr2").val(json.LMS1205S05Form03.idDscr2);
/*
									if(json.LMS1205S05Form03.showItems != null 
									&& json.LMS1205S05Form03.showItems != undefined 
									&& json.LMS1205S05Form03.showItems != ""){
										for(o in json.LMS1205S05Form03.showItems){
											$LMS1205S05Form03.find("#"+ json.LMS1205S05Form03.showItems[o]).show();
										}							
									}
*/									
							});
						}
					},
					"cancel": function() {					 
						 API.confirmMessage(i18n.def['flow.exit'], function(res){
								if(res){
									$.thickbox.close();
								}
					        });
					}
				}			
			});
	});		
}

function openthe2(list){
	$.ajax({									
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "getC140m01a2",
			page : "61",
			mainId : responseJSON.mainId,
			cesOid : list.oid,
			cesMainId : list.mainId }
		}).done(function(json) {
			$("#tabForm01").reset();
			$("#tabForm01").setData(json,false);
			thickboxCes6();
	});		
/*
	cesGrid1();
	$("#openbox1").thickbox({     // 使用選取的內容進行彈窗
      title : i18n.lms1205s05["l120s05.thickbox8"],
      width : 640,
      height : 380,
      modal : true,
	  align : 'center',
	  valign: 'bottom',
      i18n:i18n.def,
      buttons: {               
	          "sure": function() {
					var row = $("#gridbox1").getGridParam('selrow'); 
					var list = "";
					var data = $("#gridbox1").getRowData(row);
					list = data;
					list = (list == undefined ? "" : list);
					if(list.oid != "" && list.oid != undefined && list.oid != null){
						$.ajax({									
							handler : "lms1205formhandler",
							type : "POST",
							dataType : "json",
							data : 
							{
								formAction : "getC140m01a2",
								page : "61",
								mainId : responseJSON.mainId,
								cesOid : list.oid,
								cesMainId : list.mainId
							},
							success : function(json) {
								$("#tabForm01").reset();
								$("#tabForm01").setData(json,false);
								thickboxCes6();
							}
						});
						$.thickbox.close();
					}else{
						CommonAPI.showMessage(i18n.lmss07["L1205S07.alert2"]);
					}
	              },            
	          "cancel": function() {
	        	  API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
	              }
              }
       });
*/
}

function thickboxCes6(){
	$("#thickboxCes6").thickbox({     // 使用選取的內容進行彈窗
      title : i18n.lms1205s05["l120s05.thickbox8"],
      width : 960,
      height : 480,
      modal : true,
      i18n:i18n.lms1205s05,
      buttons: {                          
	          "l120s05.thickbox9": function() {
	        	  API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
	              }
              }
       });
}

function openthe1(list){
	$.ajax({		//查詢主要借款人資料
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "findBusi",
			mainId : responseJSON.mainId,
			cesMainId : list
		}
		}).done(function(json) {
			$("#LMS1205S05Form01").find("#itemOfBusi").val(json.LMS1205S05Form01.itemOfBusi);
	});		
/*
	cesGrid1();
	$("#openbox1").thickbox({     // 使用選取的內容進行彈窗
      title : i18n.lms1205s05["l120s05.thickbox10"],
      width : 640,
      height : 380,
      modal : true,
	  align : 'center',
	  valign: 'bottom',
      i18n:i18n.def,
      buttons: {               
	          "sure": function() {
					var row = $("#gridbox1").getGridParam('selrow'); 
					var list = "";
					var data = $("#gridbox1").getRowData(row);
					list = data.mainId;
					list = (list == undefined ? "" : list);
					if(list != ""){
						$.ajax({		//查詢主要借款人資料
							handler : "lms1205formhandler",
							type : "POST",
							dataType : "json",
							data : 
							{
								formAction : "findBusi",
								mainId : responseJSON.mainId,
								cesMainId : list
							},
							success : function(json) {
								$("#LMS1205S05Form01").find("#itemOfBusi").val(json.LMS1205S05Form01.itemOfBusi);
							}
						});
						$.thickbox.close();
					}else{
						CommonAPI.showMessage(i18n.lmss07["L1205S07.alert2"]);
					}
	              },            
	          "cancel": function() {
	        	  API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
	              }
              }
       });
*/
}

function freeFormate() {
	
	 $("#freeFormate").thickbox({     // 使用選取的內容進行彈窗
		   title : i18n.lms1205s05["l120s05.thickbox12"],
		   width : 800,
		   height : 460,
		   modal : true,
		   i18n:i18n.def,
		   buttons: {
		             "saveData": function() {
		            	 //儲存
		            	 	$.ajax({									
								handler : "lms1205formhandler",
								type : "POST",
								dataType : "json",
								data : 
								{
									formAction : "saveFreeFormat",
									mainId : responseJSON.mainId,
									itemDscrC : getCkeditor("itemDscrC")
								}
								}).done(function(json) {
							});
		             },
		             "del": function() {
		            	 //刪除
		 				CommonAPI.confirmMessage(i18n.lms1205s05["l120s05.confirm1"],function(b){
							if(b){
								//是的function
			            	 	$.thickbox.close();
			            	 	$.ajax({									
									handler : "lms1205formhandler",
									type : "POST",
									dataType : "json",
									data : 
									{
										formAction : "deleteFreeFormat",
										mainId : responseJSON.mainId
									}
									}).done(function(json) {
								});
							}else{
								//否的function
								CommonAPI.showMessage(i18n.lms1205s05["l120s05.alert3"]);
							}
						})
			         },
		              "close": function() {
		            	  API.confirmMessage(i18n.def['flow.exit'], function(res){
		  					if(res){
		  						$.thickbox.close();
		  					}
		  		        });
		             }
		           }			  
		    });
	 }

function openname(){
	$.ajax({									
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "getCusSel",
			mainId : responseJSON.mainId
		}
		}).done(function(json) {
		      var selJson = {
			       		item : json.selCus,
			       		format : "{key}",
			       		space: false
			       	};
		      $("#LMS1205S05Form04").find("#selCus").setItems(selJson);
		      $("#hidetitle").hide();
		      $("#hiderow").show();
			 $("#thickselkind").thickbox({     // 使用選取的內容進行彈窗
				   title : i18n.lms1205s05["l120s05.thickbox7"],
				   width : 520,
				   height : 260,
				   modal : true,
				   valign : "bottom",
				   align : "center",
				   i18n:i18n.def,
				   buttons: {
			             	  "sure": function() {
			             		//開啟徵信報告選擇畫面
		        				//依照使用者選擇借款人查詢徵信報告
		             			var selOid = $("#selCus option:selected").val();
		        				$.thickbox.close();
		        				getCes3();
		        				cesGrid2(selOid);
			                 },            
			                  "cancel": function() {
			                	  API.confirmMessage(i18n.def['flow.exit'], function(res){
			      					if(res){
			      						$.thickbox.close();
			      					}
			      		        });
			                 }
				           }			  
				    });
	});
}


//*******************************************************************************************************
//J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
//*******************************************************************************************************
//LMS1201S05Page06 load
function initLoad_LMS1201S05Page06(){
	
	var $LMS1205S05Form06 = $("#LMS1205S05Form06");
	
	if(responseJSON.readOnly == "true"){
		$LMS1205S05Form06.readOnlyChilds(true);
		$LMS1205S05Form06.find("button").hide();
		$LMS1205S05Form06.find(".grpBtn").show();
	}
	
	Panel06Gridview01();
	Panel06Gridview02();
	
	//J-105-0167-001 Web e-Loan 企金授信案件簽報書第八之3增列集團企業(應予注意集團)有關集團評等之「財務警訊項目資訊」 
	var isGrpFinAlertCanShow = false;
	$.ajax({
        async: false,
        handler: "lms1205formhandler",
        data: {
            formAction: "chkGrpFinAlertCanShow" }
        }).done(function(obj){
            if (obj.canShow == "Y") {
                isGrpFinAlertCanShow = true;
            }
			if (isGrpFinAlertCanShow == true) {
	            $(".showGrpFinAlert").show();
	        }
	        else {
	            $(".showGrpFinAlert").hide();
	        }
    });
			
	var cesGrid1 = $("#cesGrid1").iGrid({		//借款人基本資料GridView
		handler : 'lms1205gridhandler',
		height : 175,
		sortname : 'custName',
		postData : {
			formAction : "queryL120s01e1Grp",
			mainId : responseJSON.mainId,
			rowNum:5
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		rowNum:5,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lms1205s05["l120s05.grid25"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		},{
			colHeader : i18n.lms1205s05["l120s05.grid26"], //建立日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'createTime' //col.id
		}, {
			colHeader : i18n.lmss02["l120s02.grid1"], //資信簡表編號
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'sn' //col.id
		}, {
			colHeader : i18n.lms1205s05["l120s05.grid27"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		},  {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
}

//LMS1205S05Page06 load
function cesGrid1(){
	   $("#cesGrid1").jqGrid("setGridParam", {
		postData : {
			formAction : "queryL120s01e1Grp",
			mainId : responseJSON.mainId,
			rowNum:5
		},
		search: true
	   }).trigger("reloadGrid");
}

//LMS1205S05Page06 load
function Panel06Gridview01(){
	$("#Panel06Gridview01").iGrid({
		handler: 'lms1205gridhandler',
		height: 120, //for 10 筆
		sortname : 'custName',
		postData : {
			formAction : "queryL120s05b",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rowNum:10,
		rownumbers:true,
		colModel: [{
			colHeader: i18n.lms1205s05["l120s05.grid1"],//"【集團企業】明細<br />(戶名最多顯示長度24個全形字)"
			name: 'custName',
			width: 180,
			sortable: false,
			align:"left"
		}, {
			colHeader: i18n.lms1205s05["l120s05.grid2"],//"授信總額度(海外)"
			name: 'totAmtB',
			width: 100,
			sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
		}, {
			colHeader: i18n.lms1205s05["l120s05.grid3"],//"授信總額度(台灣)"
			name: 'totAmtA',
			width: 100,
			sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
		}, {
			colHeader: i18n.lms1205s05["l120s05.grid4"],//"無擔保授信額度(海外)"
			name: 'crdAmtB',
			width: 100,
			sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
		}, {
			colHeader: i18n.lms1205s05["l120s05.grid5"],//"無擔保授信額度(台灣)"
			name: 'crdAmtA',
			width: 100,
			sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
		}, {
			colHeader: i18n.lms1205s05["l120s05.grid6"],//"扣除一～四項<br />授信總額度"
			name: 'excAmt',
			width: 100,
			sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
		}, {
			colHeader: i18n.lms1205s05["l120s05.grid7"],//"扣除一～四項<br />無擔保授信額度"
			name: 'excrdAmt',
			width: 100,
			sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
		}, {
			name: 'oid',
			hidden: true
		}],
		ondblClickRow: function(rowid){
		}
	});
}

//LMS1205S05Page06 load
function uPanel06Gridview01(){
	   $("#Panel06Gridview01").jqGrid("setGridParam", {
		postData : {
			formAction : "queryL120s05b",
			mainId : responseJSON.mainId,
			rowNum:10
		},
		search: true
	   }).trigger("reloadGrid");
}

//LMS1205S05Page06 load
function Panel06Gridview02(){
	$("#Panel06Gridview02").iGrid({
		handler: 'lms1205gridhandler',
		height: 120, //for 10 筆
		sortname : 'custName',
		postData : {
			formAction : "queryL120s05b",
			mainId : responseJSON.mainid,
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rowNum:10,
		rownumbers:true,
		colModel: [{
			colHeader: i18n.lms1205s05["l120s05.grid8"],//"【集團企業】明細<br />(戶名最多顯示長度24個全形字)"
			name: 'custName',
			width: 180,
			sortable: false,
			align:"left"
		}, {
			colHeader: i18n.lms1205s05["l120s05.grid9"],//"金融商品",
			name: 'gpComAmt',
			width: 60,
			sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
		}, {
			colHeader: i18n.lms1205s05["l120s05.grid10"],//"曝險總額",
			name: 'gpRiskAmt',
			width: 60,
			sortable: false,
			formatter : function(data) {
				if(data == null){
					return "";
				}else{
					// 加入撇節符號
					return util.addComma(data);	
				}
			},			
			align:"right"
		}, {
			name: 'oid',
			hidden: true
		}],
		ondblClickRow: function(rowid){
		}
	});
}
//LMS1205S05Page06 load
function uPanel06Gridview02(){
	   $("#Panel06Gridview02").jqGrid("setGridParam", {
		postData : {
			formAction : "queryL120s05b",
			mainId : responseJSON.mainId,
			rowNum:10
		},
		search: true
	   }).trigger("reloadGrid");
}

