/* 
 * L140S02MDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S02M;

/** 個金產品種類檔 **/
public interface L140S02MDao extends IGenericDao<L140S02M> {

	L140S02M findByOid(String oid);

	List<L140S02M> findByMainId(String mainId);

	List<L140S02M> findByOids(String[] oids);
}