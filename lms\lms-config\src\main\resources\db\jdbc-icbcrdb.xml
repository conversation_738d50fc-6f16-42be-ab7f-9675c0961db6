<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<!-- JNDI Datasource -->
	<bean id="icbcrdb-db" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${icbcrdb.jndiName}</value>
		</property>
	</bean>

	<!-- <bean id="icbcrdb-db" class="org.springframework.jdbc.datasource.SingleConnectionDataSource"> 
		<property name="driverClassName" value="${mis.jdbc.driver}" /> <property 
		name="url" value="${mis.jdbc.url}" /> <property name="username" value="${mis.jdbc.username}" 
		/> <property name="password" value="${mis.jdbc.password}" /> <property name="suppressClose" 
		value="true" /> </bean> -->

	<bean id="icbcrdbTxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="icbcrdb-db" />
	</bean>

	<tx:advice id="icbcrdbTxAdvice" transaction-manager="icbcrdbTxManager">
		<tx:attributes>
			<!-- all methods below are read-only -->
			<tx:method name="list*"  read-only="true"
				propagation="NOT_SUPPORTED" />
			<tx:method name="find*"  read-only="true"
				propagation="NOT_SUPPORTED" />
			<tx:method name="get*"  read-only="true"
				propagation="NOT_SUPPORTED" />

			<!-- other methods use the default transaction settings (see below) -->
			<tx:method name="*" timeout="45" rollback-for="Throwable"
				propagation="REQUIRED" />
			<!-- timeout in seconds -->
		</tx:attributes>
	</tx:advice>

	<aop:config proxy-target-class="true">
		<aop:pointcut id="mfaloanServiceOperation"
			expression="execution(* com.mega.eloan.lms.mfaloan.service.*.*(..))  and !@annotation(tw.com.iisi.cap.annotation.NonTransactional) " />   <!--expression="execution(* com.mega.eloan.lms.mfaloan.service.*.*(..)) || execution(* com.mega.eloan.lms..service.*.*(..)) " />  -->
		<aop:advisor advice-ref="icbcrdbTxAdvice" pointcut-ref="mfaloanServiceOperation" />
	</aop:config>

</beans>
