package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;

@Service
public class MisELCUS25ServiceImpl extends AbstractMFAloanJdbc implements
		MisElCUS25Service {

	public Map<String, Object> getMiselcus25(String custId, String dupNo) {

		return this.getJdbc().queryForMap("MISELCUS25.FindByCustIdDupNo",
				new Object[] { custId, dupNo });
	}

	public List<Map<String, Object>> findMiselcus25(String custId, String dupNo) {
		return this.getJdbc().queryForList("ELCUS25.findByIdDupNo",
				new Object[] { custId, dupNo });
	}
	
	@Override
	public List<Map<String, Object>> findById(String custId){
		return getJdbc().queryForList("CMFCUS25.findByObuId", new Object[]{custId});
	}	
	
	@Override
	public Map<String, Object> findByPk(String custId, String dupNo){		
		return this.getJdbc().queryForMap("ELCUS25.findAllByIdDupNo",
				new Object[] { custId, dupNo });
	}
	
	/**
	 * 取其聯徵虛擬統編
	 * 
	 * @param custId
	 *            String
	 * @return String
	 */
	@Override
	public String getEjcicId_RpsVersion(String custId) {
		// A.若畫面輸入之 統編長度為10 且 (前兩碼非數字 & 第三碼為Z)
		if (custId.length() == 10 && !custId.matches("^[0-9][0-9]")
				&& "Z".equalsIgnoreCase(custId.substring(2, 3))) {
			// 介面_取得聯徵虛擬統編(CMFCUS25)
			List<Map<String, Object>> ids = findById(custId);
			if (!CollectionUtils.isEmpty(ids)) {
				for (Map<String, Object> m : ids) {
					String id = MapUtils.getString(m, "CM25_JCIC_TAXNO");
					return CapString.isEmpty(id) ? custId : id;
				}
			}
		}
		return custId;
	}
	
	/**
	 * J-111-0207 eloan國內企金管理系統，動用檢核表增加一項目:「信保案件負責人客戶基本資料檔是否已建有配偶資料」
	 * 用借款人查詢負責人
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> getMiselcus25_Principal(String custId, String dupNo) {

		return this.getJdbc().queryForMap("MISELCUS25.findPrincipal",
				new Object[] { custId, dupNo });
	}
}
