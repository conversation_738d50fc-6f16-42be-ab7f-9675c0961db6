/* 
 * LMS1605S03Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 動用審核表 - 相關報表
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
public class LMS1605S03Panel extends Panel {

	public LMS1605S03Panel(String id) {
		super(id);
		// 是否為聯貸案
		// String unitLoanCase = params.getString("unitLoanCase");
		// Util.showParams(params);
		// if (UtilConstants.DEFAULT.是.equals(unitLoanCase)) {
		// add(new LMS1605S03Panel01("_LMS160PanelS03_01"));
		// add(new LMS1605S03Panel02("_LMS160PanelS03_02"));
		// } else {
		// add(new Label("_LMS160PanelS03_01"));
		// add(new LMS1605S03Panel02("_LMS160PanelS03_02"));
		// }

	}

	public LMS1605S03Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new LMS1605S03Panel01("_LMS160PanelS03_01").processPanelData(model, params);
		new LMS1605S03Panel02("_LMS160PanelS03_02").processPanelData(model, params);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
