/* 
 * L185M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.util.CapDate;

import com.mega.eloan.lms.dao.L185M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L185M01A;


/** 覆審管理報表檔 **/
@Repository
public class L185M01ADaoImpl extends LMSJpaDao<L185M01A, String> implements
		L185M01ADao {

	@Override
	public L185M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L185M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L185M01A> findByIndex01(Date dataDate, Date dataEDate,
			String branchId, int i) {
		ISearch search = createSearchTemplete();
		List<L185M01A> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
				CapDate.formatDate(dataDate, "yyyy-MM-dd"));
		search.addSearchModeParameters(SearchMode.EQUALS, "dataEDate",
				CapDate.formatDate(dataEDate, "yyyy-MM-dd"));
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "rptType",
				String.valueOf(i));
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}
	@Override
	public List<L185M01A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L185M01A> list = createQuery(L185M01A.class,search).getResultList();
		return list;
	}
}