/* 
 * L140M04A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 查核事項檔明細檔(來源lms.c900s01a) **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M04A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "checkCode" }))
public class L140M04A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * mainId
	 * <p/>
	 * 簽報書mainId
	 */
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 查核事項編號 **/
	@Size(max = 10)
	@Column(name = "CHECKCODE", length = 10, columnDefinition = "VARCHAR(10)")
	private String checkCode;

	/** 顯示順序 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "CHECKSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer checkseq;

	/**
	 * 查核事項項目 select * from com.bcodetype where codetype='C900S01A_checkType'
	 * <p/>
	 * 1|用途<br/>
	 * 2|對象及條件<br/>
	 * 3|徵信<br/>
	 * 4|貸款金額<br/>
	 * 5|額度限制<br/>
	 * 6|貸款期限<br/>
	 * 7|擔保品<br/>
	 * 8|保證人<br/>
	 * 9|其他<br/>
	 * 10|應具備條件<br/>
	 * 11|利息補貼
	 */
	@Size(max = 2)
	@Column(name = "CHECKTYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String checkType;

	/** 查核內容 **/
	@Size(max = 1024)
	@Column(name = "CHECKCONTENT", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String checkContent;

	/**
	 * 適用產品
	 * <p/>
	 * 格式：02,03,04,…
	 */
	@Size(max = 256)
	@Column(name = "PRODTYPE", length = 256, columnDefinition = "VARCHAR(256)")
	private String prodType;

	/**
	 * 查核結果
	 * <p/>
	 * 是|Y<br/>
	 * 否|N
	 * 不適用 |O
	 */
	@Size(max = 1)
	@Column(name = "CHECKYN", length = 1, columnDefinition = "CHAR(1)")
	private String checkYN;

	/** 備註 **/
	@Size(max = 1024)
	@Column(name = "CHECKRMK", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String checkRmk;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得mainId
	 * <p/>
	 * 簽報書mainId
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定mainId
	 * <p/>
	 * 簽報書mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得查核事項編號 **/
	public String getCheckCode() {
		return this.checkCode;
	}

	/** 設定查核事項編號 **/
	public void setCheckCode(String value) {
		this.checkCode = value;
	}

	/** 取得顯示順序 **/
	public Integer getCheckseq() {
		return this.checkseq;
	}

	/** 設定顯示順序 **/
	public void setCheckseq(Integer value) {
		this.checkseq = value;
	}

	/**
	 * 取得查核事項項目
	 * <p/>
	 * 1|用途<br/>
	 * 2|對象及條件<br/>
	 * 3|徵信<br/>
	 * 4|貸款金額<br/>
	 * 5|額度限制<br/>
	 * 6|貸款期限<br/>
	 * 7|擔保品<br/>
	 * 8|保證人<br/>
	 * 9|其他<br/>
	 * 10|應具備條件<br/>
	 * 11|利息補貼
	 */
	public String getCheckType() {
		return this.checkType;
	}

	/**
	 * 設定查核事項項目
	 * <p/>
	 * 1|用途<br/>
	 * 2|對象及條件<br/>
	 * 3|徵信<br/>
	 * 4|貸款金額<br/>
	 * 5|額度限制<br/>
	 * 6|貸款期限<br/>
	 * 7|擔保品<br/>
	 * 8|保證人<br/>
	 * 9|其他<br/>
	 * 10|應具備條件<br/>
	 * 11|利息補貼
	 **/
	public void setCheckType(String value) {
		this.checkType = value;
	}

	/** 取得查核內容 **/
	public String getCheckContent() {
		return this.checkContent;
	}

	/** 設定查核內容 **/
	public void setCheckContent(String value) {
		this.checkContent = value;
	}

	/**
	 * 取得適用產品
	 * <p/>
	 * 格式：02,03,04,…
	 */
	public String getProdType() {
		return this.prodType;
	}

	/**
	 * 設定適用產品
	 * <p/>
	 * 格式：02,03,04,…
	 **/
	public void setProdType(String value) {
		this.prodType = value;
	}

	/**
	 * 取得查核結果
	 * <p/>
	 * 是|Y<br/>
	 * 否|N
	 * 不適用 |O
	 */
	public String getCheckYN() {
		return this.checkYN;
	}

	/**
	 * 設定查核結果
	 * <p/>
	 * 是|Y<br/>
	 * 否|N
	 * 不適用 |O
	 **/
	public void setCheckYN(String value) {
		this.checkYN = value;
	}

	/** 取得備註 **/
	public String getCheckRmk() {
		return this.checkRmk;
	}

	/** 設定備註 **/
	public void setCheckRmk(String value) {
		this.checkRmk = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
