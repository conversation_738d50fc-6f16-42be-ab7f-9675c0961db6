package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C900S02FDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S02F;

/** 同一通訊指標相同借戶明細檔  **/
@Repository
public class C900S02FDaoImpl extends LMSJpaDao<C900S02F, String>
	implements C900S02FDao {

	@Override
	public C900S02F findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C900S02F> findByMainId_order(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(true){
			search.addOrderBy("rel_custId");
			search.addOrderBy("rel_dupNo");
		}
		search.setMaxResults(Integer.MAX_VALUE);
		return createQuery(search).getResultList();
	}
}