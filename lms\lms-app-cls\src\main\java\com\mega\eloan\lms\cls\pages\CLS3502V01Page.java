package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;

/**
 * <pre>
 * 線上勞工紓困貸款
 * </pre>
 * 
 * @since 2020/5/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/5/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls3502v01")
public class CLS3502V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CLSDocStatusEnum.編製中);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.ProduceExcel);

		// build i18n
		renderJsI18N(CLS3502V01Page.class);

	}

}
