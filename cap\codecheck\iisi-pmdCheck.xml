<?xml version="1.0"?>

<ruleset name="IISI-PMD Rules" xmlns="http://pmd.sf.net/ruleset/1.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd"
	xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd">

	<description>
  </description>
	<!-- imports.xml -->
	<rule name="DuplicateImports" since="0.5"
		message="Avoid duplicate imports such as ''{0}''" class="net.sourceforge.pmd.rules.imports.DuplicateImportsRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/imports.html#DuplicateImports">
		<description>
    Avoid duplicate import statements.
    </description>
		<priority>4</priority>
		<example>
<![CDATA[
import java.lang.String;
import java.lang.*;
public class Foo {}
]]>
    </example>
	</rule>

	<rule name="DontImportJavaLang" since="0.5"
		message="Avoid importing anything from the package 'java.lang'" class="net.sourceforge.pmd.rules.imports.DontImportJavaLang"
		externalInfoUrl="http://pmd.sourceforge.net/rules/imports.html#DontImportJavaLang">
		<description>
Avoid importing anything from the package 'java.lang'.  These classes are automatically imported (JLS 7.5.3).
    </description>
		<priority>4</priority>
		<example>
<![CDATA[
// this is bad
import java.lang.String;
public class Foo {}

// --- in another source code file...

// this is bad
import java.lang.*;

public class Foo {}
]]>
    </example>
	</rule>

	<rule name="UnusedImports" since="1.0"
		message="Avoid unused imports such as ''{0}''" class="net.sourceforge.pmd.rules.imports.UnusedImportsRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/imports.html#UnusedImports">
		<description>
    Avoid unused import statements.
    </description>
		<priority>4</priority>
		<example>
<![CDATA[
// this is bad
import java.io.File;
public class Foo {}
]]>
    </example>
	</rule>

	<rule name="ImportFromSamePackage" since="1.02"
		message="No need to import a type that lives in the same package"
		class="net.sourceforge.pmd.rules.imports.ImportFromSamePackageRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/imports.html#ImportFromSamePackage">
		<description>
 No need to import a type that lives in the same package.
     </description>
		<priority>3</priority>
		<example>
 <![CDATA[
 package foo;
 import foo.Buz; // no need for this
 import foo.*; // or this
 public class Bar{}
 ]]>
     </example>
	</rule>

	<rule name="TooManyStaticImports" since="4.1"
		class="net.sourceforge.pmd.rules.XPathRule" message="Too many static imports may lead to messy code"
		externalInfoUrl="http://pmd.sourceforge.net/rules/imports.html#TooManyStaticImports">
		<description><![CDATA[
If you overuse the static import feature, it can make your program unreadable and 
unmaintainable, polluting its namespace with all the static members you import. 
Readers of your code (including you, a few months after you wrote it) will not know 
which class a static member comes from (Sun 1.5 Language Guide).
		 ]]></description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value><![CDATA[
.[count(ImportDeclaration[@Static = 'true']) > $maximumStaticImports]
	             ]]></value>
			</property>
			<property name="maximumStaticImports"
				description="All static imports can be disallowed by setting this to 0">
				<value>4</value>
			</property>
		</properties>
		<example><![CDATA[
import static Lennon;
import static Ringo;
import static George;
import static Paul;
import static Yoko; // Too much !
		  ]]></example>
	</rule>

	<!-- unusedcode.xml -->
	<rule name="UnusedPrivateField" since="0.1"
		message="Avoid unused private fields such as ''{0}''." class="net.sourceforge.pmd.rules.UnusedPrivateFieldRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/unusedcode.html#UnusedPrivateField">
		<description>
Detects when a private field is declared and/or assigned a value, but not used.
    </description>
		<priority>3</priority>
		<example>
<![CDATA[
public class Something {
  private static int FOO = 2; // Unused
  private int i = 5; // Unused
  private int j = 6;
  public int addOne() {
    return j++;
  }
}
]]>
    </example>
	</rule>

	<rule name="UnusedLocalVariable" since="0.1"
		message="Avoid unused local variables such as ''{0}''." class="net.sourceforge.pmd.rules.UnusedLocalVariableRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/unusedcode.html#UnusedLocalVariable">
		<description>
Detects when a local variable is declared and/or assigned, but not used.
    </description>
		<priority>3</priority>

		<example>
<![CDATA[
public class Foo {
 public void doSomething() {
  int i = 5; // Unused
 }
}
]]>
    </example>
	</rule>

	<!-- basic.xml -->
	<rule name="EmptyCatchBlock" since="0.1" message="Avoid empty catch blocks"
		class="net.sourceforge.pmd.rules.XPathRule" externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#EmptyCatchBlock">
		<description>
Empty Catch Block finds instances where an exception is caught,
but nothing is done.  In most circumstances, this swallows an exception
which should either be acted on or reported.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
    <![CDATA[
//CatchStatement
 [count(Block/BlockStatement) = 0 and ($allowCommentedBlocks != 'true' or Block/@containsComment = 'false')]
 [FormalParameter/Type/ReferenceType
   /ClassOrInterfaceType[@Image != 'InterruptedException' and @Image != 'CloneNotSupportedException']
 ]
 ]]>
             </value>
			</property>
			<property name="allowCommentedBlocks"
				description="Empty blocks containing comments will be skipped">
				<value>false</value>
			</property>
		</properties>
		<example>
  <![CDATA[
public void doSomething() {
  try {
    FileInputStream fis = new FileInputStream("/tmp/bugger");
  } catch (IOException ioe) {
      // not good
  }
}
 ]]>
      </example>
	</rule>

	<rule name="EmptyWhileStmt" since="0.2" message="Avoid empty 'while' statements"
		class="net.sourceforge.pmd.rules.XPathRule" externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#EmptyWhileStmt">
		<description>
Empty While Statement finds all instances where a while statement
does nothing.  If it is a timing loop, then you should use Thread.sleep() for it; if
it's a while loop that does a lot in the exit expression, rewrite it to make it clearer.
       </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//WhileStatement/Statement[./Block[count(*) = 0]  or ./EmptyStatement]
]]>
              </value>
			</property>
		</properties>
		<example>
  <![CDATA[
public class Foo {
 void bar(int a, int b) {
  while (a == b) {
   // empty!
  }
 }
}
 ]]>
       </example>
	</rule>


	<rule name="EmptyTryBlock" since="0.4" message="Avoid empty try blocks"
		class="net.sourceforge.pmd.rules.XPathRule" externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#EmptyTryBlock">
		<description>
Avoid empty try blocks - what's the point?
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//TryStatement/Block[1][count(*) = 0]
]]>
              </value>
			</property>
		</properties>
		<example>
  <![CDATA[
public class Foo {
 public void bar() {
  try {
  } catch (Exception e) {
    e.printStackTrace();
  }
 }
}
]]>
      </example>
	</rule>

	<rule name="EmptyFinallyBlock" since="0.4" message="Avoid empty finally blocks"
		class="net.sourceforge.pmd.rules.XPathRule" externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#EmptyFinallyBlock">
		<description>
Avoid empty finally blocks - these can be deleted.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//FinallyStatement[count(Block/BlockStatement) = 0]
 ]]>
              </value>
			</property>
		</properties>
		<example>
  <![CDATA[
public class Foo {
 public void bar() {
  try {
    int x=2;
   } finally {
    // empty!
   }
 }
}
 ]]>
      </example>
	</rule>


	<rule name="EmptySwitchStatements" since="1.0"
		message="Avoid empty switch statements" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#EmptySwitchStatements">
		<description>
Avoid empty switch statements.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//SwitchStatement[count(*) = 1]
 ]]>
              </value>
			</property>
		</properties>
		<example>
  <![CDATA[
public class Foo {
 public void bar() {
  int x = 2;
  switch (x) {
   // once there was code here
   // but it's been commented out or something
  }
 }
}]]>
      </example>
	</rule>


	<rule name="JumbledIncrementer" since="1.0"
		message="Avoid modifying an outer loop incrementer in an inner loop for update expression"
		class="net.sourceforge.pmd.rules.XPathRule" externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#JumbledIncrementer">
		<description>
Avoid jumbled loop incrementers - it's usually a mistake, and it's confusing even if it's what's intended.
     </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
 <![CDATA[
//ForStatement
 [
  ForUpdate/StatementExpressionList/StatementExpression/PostfixExpression/PrimaryExpression/PrimaryPrefix/Name/@Image
  =
  ancestor::ForStatement/ForInit//VariableDeclaratorId/@Image
 ]
 ]]>
             </value>
			</property>
		</properties>
		<example>
 <![CDATA[
public class JumbledIncrementerRule1 {
  public void foo() {
   for (int i = 0; i < 10; i++) {
    for (int k = 0; k < 20; i++) {
     System.out.println("Hello");
    }
   }
  }
 }
 ]]>
     </example>
	</rule>

	<rule name="ForLoopShouldBeWhileLoop" since="1.02"
		message="This for loop could be simplified to a while loop" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#ForLoopShouldBeWhileLoop">
		<description>
Some for loops can be simplified to while loops - this makes them more concise.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
                <![CDATA[
//ForStatement
 [count(*) > 1]
 [not(ForInit)]
 [not(ForUpdate)]
 [not(Type and Expression and Statement)]
 ]]>
            </value>
			</property>
		</properties>
		<example>
  <![CDATA[
public class Foo {
 void bar() {
  for (;true;) true; // No Init or Update part, may as well be: while (true)
 }
}
 ]]>
      </example>
	</rule>


	<rule name="UnnecessaryConversionTemporary" since="0.1"
		message="Avoid unnecessary temporaries when converting primitives to Strings"
		class="net.sourceforge.pmd.rules.UnnecessaryConversionTemporary"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#UnnecessaryConversionTemporary">
		<description>
Avoid unnecessary temporaries when converting primitives to Strings
      </description>
		<priority>3</priority>
		<example>
  <![CDATA[
public String convert(int x) {
  // this wastes an object
  String foo = new Integer(x).toString();
  // this is better
  return Integer.toString(x);
}
 ]]>
      </example>
	</rule>
<!--
	<rule name="OverrideBothEqualsAndHashcode" since="0.4"
		message="Ensure you override both equals() and hashCode()" class="net.sourceforge.pmd.rules.OverrideBothEqualsAndHashcode"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#OverrideBothEqualsAndHashcode">
		<description>
Override both public boolean Object.equals(Object other), and public int Object.hashCode(), or override neither.  Even if you are inheriting a hashCode() from a parent class, consider implementing hashCode and explicitly delegating to your superclass.
      </description>
		<priority>3</priority>
		<example>
  <![CDATA[
// this is bad
public class Bar {
  public boolean equals(Object o) {
      // do some comparison
  }
}

// and so is this
public class Baz {
  public int hashCode() {
      // return some hash value
  }
}

// this is OK
public class Foo {
  public boolean equals(Object other) {
      // do some comparison
  }
  public int hashCode() {
      // return some hash value
  }
}
 ]]>
      </example>
	</rule>
-->
	<rule name="DoubleCheckedLocking" since="1.04"
		message="Double checked locking is not thread safe in Java." class="net.sourceforge.pmd.rules.DoubleCheckedLocking"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#DoubleCheckedLocking">
		<description>
Partially created objects can be returned by the Double Checked Locking pattern when used in Java.
An optimizing JRE may assign a reference to the baz variable before it creates the object the
  reference is intended to point to.  For more details see http://www.javaworld.com/javaworld/jw-02-2001/jw-0209-double.html.
      </description>
		<priority>1</priority>
		<example>
  <![CDATA[
public class Foo {
  Object baz;
  Object bar() {
    if(baz == null) { //baz may be non-null yet not fully created
      synchronized(this){
        if(baz == null){
          baz = new Object();
        }
      }
    }
    return baz;
  }
}
 ]]>
      </example>
	</rule>

	<rule name="ReturnFromFinallyBlock" since="1.05"
		message="Avoid returning from a finally block" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#ReturnFromFinallyBlock">
		<description>
Avoid returning from a finally block - this can discard exceptions.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//FinallyStatement//ReturnStatement
]]>
              </value>
			</property>
		</properties>
		<example>
  <![CDATA[
public class Bar {
 public String foo() {
  try {
   throw new Exception( "My Exception" );
  } catch (Exception e) {
   throw e;
  } finally {
   return "A. O. K."; // Very bad.
  }
 }
}
]]>
      </example>
	</rule>

	<rule name="EmptySynchronizedBlock" since="1.3"
		message="Avoid empty synchronized blocks" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#EmptySynchronizedBlock">
		<description>
  Avoid empty synchronized blocks - they're useless.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//SynchronizedStatement/Block[1][count(*) = 0]
]]>
              </value>
			</property>
		</properties>
		<example>
<![CDATA[
public class Foo {
 public void bar() {
  synchronized (this) {
   // empty!
  }
 }
}
]]>
      </example>
	</rule>

	<rule name="EmptyStaticInitializer" since="1.5"
		message="Empty static initializer was found" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#EmptyStaticInitializer">
		<description>
An empty static initializer was found.
       </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//Initializer[@Static='true']/Block[count(*)=0]
]]>
                 </value>
			</property>
		</properties>
		<example>
   <![CDATA[
public class Foo {
 static {
  // empty
 }
 }
]]>
       </example>
	</rule>

	<rule name="EmptyStatementNotInLoop" since="1.5"
		message="An empty statement (semicolon) not part of a loop" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#EmptyStatementNotInLoop">
		<description>
An empty statement (aka a semicolon by itself) that is not used
as the sole body of a for loop or while loop is probably a bug.  It
could also be a double semicolon, which is useless and should be
removed.
       </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//EmptyStatement
 [not(
       ../../../ForStatement
       or ../../../WhileStatement
       or ../../../BlockStatement/ClassOrInterfaceDeclaration
       or ../../../../../../ForStatement/Statement[1]
        /Block[1]/BlockStatement[1]/Statement/EmptyStatement
       or ../../../../../../WhileStatement/Statement[1]
        /Block[1]/BlockStatement[1]/Statement/EmptyStatement)
 ]
]]>
                </value>
			</property>
		</properties>
		<example>
<![CDATA[
public class MyClass {
   public void doit() {
      // this is probably not what you meant to do
      ;
      // the extra semicolon here this is not necessary
      System.out.println("look at the extra semicolon");;
   }
}
]]>
       </example>
	</rule>

	<rule name="BooleanInstantiation" since="1.2"
		message="Avoid instantiating Boolean objects; reference Boolean.TRUE or Boolean.FALSE or call Boolean.valueOf() instead."
		class="net.sourceforge.pmd.rules.basic.BooleanInstantiation"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#BooleanInstantiation">
		<description>
Avoid instantiating Boolean objects; you can reference Boolean.TRUE, Boolean.FALSE, or call Boolean.valueOf() instead.
   </description>
		<priority>2</priority>
		<example>
   <![CDATA[
public class Foo {
 Boolean bar = new Boolean("true"); // just do a Boolean bar = Boolean.TRUE;
 Boolean buz = Boolean.valueOf(false); // just do a Boolean buz = Boolean.FALSE;
}
   ]]>
   </example>
	</rule>


	<rule name="AvoidDecimalLiteralsInBigDecimalConstructor" since="3.4"
		message="Avoid creating BigDecimal with a decimal (float/double) literal. Use a String literal"
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#AvoidDecimalLiteralsInBigDecimalConstructor">
		<description>
     One might assume that "new BigDecimal(.1)" is exactly equal
     to .1, but it is actually equal
     to .1000000000000000055511151231257827021181583404541015625.
     This is so because .1 cannot be represented exactly as a double
     (or, for that matter, as a binary fraction of any finite length).
     Thus, the long value that is being passed in to the constructor
     is not exactly equal to .1, appearances notwithstanding.

     The (String) constructor, on the other hand, is perfectly predictable:
     'new BigDecimal(".1")' is exactly equal to .1, as one
     would expect.  Therefore, it is generally recommended that the (String)
     constructor be used in preference to this one.
  </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//AllocationExpression[ClassOrInterfaceType[@Image="BigDecimal"]
and
./Arguments/ArgumentList
/Expression/PrimaryExpression/PrimaryPrefix/Literal[(not
(ends-with
(@Image,'"'))) and contains(@Image,".")]]
 ]]>
    </value>
			</property>
		</properties>
		<example>
<![CDATA[
import java.math.BigDecimal;
public class Test {

    public static void main(String[] args) {
      // this would trigger the rule
     BigDecimal bd=new BigDecimal(1.123);
      // this wouldn't trigger the rule
     BigDecimal bd=new BigDecimal("1.123");
      // this wouldn't trigger the rule
     BigDecimal bd=new BigDecimal(12);
    }
}
]]>
  </example>
	</rule>


	<rule name="UselessOperationOnImmutable" since="3.5"
		message="An operation on an Immutable object (String, BigDecimal or BigInteger) won't change the object itself"
		class="net.sourceforge.pmd.rules.UselessOperationOnImmutable"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#UselessOperationOnImmutable">
		<description>
    An operation on an Immutable object (String, BigDecimal or BigInteger) won't change the object itself. The
    result of the operation is a new object. Therefore, ignoring the operation result is an error.
      </description>
		<priority>3</priority>
		<example>
    <![CDATA[
import java.math.*;
class Test {
 void method1() {
  BigDecimal bd=new BigDecimal(10);
  bd.add(new BigDecimal(5)); // this will trigger the rule
 }
 void method2() {
  BigDecimal bd=new BigDecimal(10);
  bd = bd.add(new BigDecimal(5)); // this won't trigger the rule
 }
}
    ]]>
      </example>
	</rule>

	<rule name="MisplacedNullCheck" since="3.5"
		message="The null check here is misplaced; if the variable is null there'll be a NullPointerException"
		class="net.sourceforge.pmd.rules.XPathRule" externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#MisplacedNullCheck">
		<description>
    The null check here is misplaced. if the variable is null you'll get a NullPointerException.
    Either the check is useless (the variable will never be "null") or it's incorrect.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
    <![CDATA[
//Expression
    /*[self::ConditionalOrExpression or self::ConditionalAndExpression]
     /descendant::PrimaryExpression/PrimaryPrefix
      /Name[starts-with(@Image,
      concat(ancestor::PrimaryExpression/following-sibling::EqualityExpression
       [./PrimaryExpression/PrimaryPrefix/Literal/NullLiteral]
     /PrimaryExpression/PrimaryPrefix
      /Name[count(../../PrimarySuffix)=0]/@Image,"."))
    ]
    ]]>
        </value>
			</property>
		</properties>
		<example>
    <![CDATA[
public class Foo {
 void bar() {
  if (a.equals(baz) && a != null) {}
 }
}
    ]]>
      </example>
		<example><![CDATA[
public class Foo {
 void bar() {
  if (a.equals(baz) || a == null) {}
 }
}
   ]]></example>
	</rule>

	<rule name="BrokenNullCheck" since="3.8"
		message="Method call on object which may be null" class="net.sourceforge.pmd.rules.basic.BrokenNullCheck"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#BrokenNullCheck">
		<description>
The null check is broken since it will throw a NullPointerException itself.
It is likely that you used || instead of &amp;&amp; or vice versa.
     </description>
        <priority>2</priority>
        <example>
<![CDATA[
class Foo {
 String bar(String string) {
  // should be &&
  if (string!=null || !string.equals(""))
    return string;
  // should be ||
  if (string==null && string.equals(""))
    return string;
 }
}
        ]]>
        </example>
    </rule>

	<rule name="BigIntegerInstantiation" since="3.9"
		message="Don't create instances of already existing
BigInteger and BigDecimal (ZERO, ONE, TEN)"
		class="net.sourceforge.pmd.rules.basic.BigIntegerInstantiation"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#BigIntegerInstantiation">
		<description>
Don't create instances of already existing BigInteger
(BigInteger.ZERO, BigInteger.ONE) and for 1.5 on,
BigInteger.TEN and BigDecimal (BigDecimal.ZERO,
BigDecimal.ONE, BigDecimal.TEN)
  </description>
		<priority>3</priority>
		<example>
<![CDATA[
public class Test {

 public static void main(String[] args) {
   BigInteger bi=new BigInteger(1);
   BigInteger bi2=new BigInteger("0");
   BigInteger bi3=new BigInteger(0.0);
   BigInteger bi4;
   bi4=new BigInteger(0);
 }
}
]]>
  </example>
	</rule>

	<rule name="AvoidUsingOctalValues" since="3.9"
		message="Do not start a literal by 0 unless it's an octal value"
		class="net.sourceforge.pmd.rules.basic.AvoidUsingOctalValues"
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#AvoidUsingOctalValues">
		<description>
    	<![CDATA[
		    Integer literals should not start with zero.
		    Zero means that the rest of literal will be interpreted as an octal value.
    	]]>
    </description>
		<priority>3</priority>
		<example>
		    <![CDATA[
		public class Foo {
		  int i = 012; // set i with 10 not 12
		  int j = 010; // set j with 8 not 10
		  k = i * j; // set k with 80 not 120
		}
		    ]]>
    </example>
	</rule>

	<rule name="CheckResultSet" since="4.1"
		class="net.sourceforge.pmd.rules.XPathRule"
		message="Always check the return of one of the navigation method (next,previous,first,last) of a ResultSet."
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#CheckResultSet">
		<description>
            <![CDATA[
	            Always check the return of one of the navigation method (next,previous,first,last) of a ResultSet. Indeed,
	            if the value return is 'false', the developer should deal with it !
            ]]>
        </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
		        	<![CDATA[
//Type/ReferenceType/ClassOrInterfaceType[
        (@Image = 'ResultSet')
        and
        (../../../descendant::Name[ends-with(@Image,'executeQuery')])
        and
        (
	(not (contains(
                        (./ancestor::Block/descendant::WhileStatement/descendant::Name/attribute::Image),
                        concat(../../../VariableDeclarator/VariableDeclaratorId/attribute::Image,'.next')
		)  ) )
	and ( not ( contains(
                        (./ancestor::Block/descendant::IfStatement/descendant::Name/attribute::Image),
                        concat(../../../VariableDeclarator/VariableDeclaratorId/attribute::Image,'.next')
		) ) )
	and (not (contains(
                        (./ancestor::Block/descendant::WhileStatement/descendant::Name/attribute::Image),
                        concat(../../../VariableDeclarator/VariableDeclaratorId/attribute::Image,'.previous')
		)  ) )
	and ( not ( contains(
                        (./ancestor::Block/descendant::IfStatement/descendant::Name/attribute::Image),
                        concat(../../../VariableDeclarator/VariableDeclaratorId/attribute::Image,'.previous')
		) ) )
	and ( not ( contains(
                        (./ancestor::Block/descendant::IfStatement/descendant::Name/attribute::Image),
                        concat(../../../VariableDeclarator/VariableDeclaratorId/attribute::Image,'.last')
		) ) )
	and ( not ( contains(
                        (./ancestor::Block/descendant::IfStatement/descendant::Name/attribute::Image),
                        concat(../../../VariableDeclarator/VariableDeclaratorId/attribute::Image,'.first')
		) ) )

         )
]
		        	]]>
            	</value>
			</property>
		</properties>
		<example>
            <![CDATA[
            // This is NOT appropriate !
            Statement stat = conn.createStatement();
            ResultSet rst = stat.executeQuery("SELECT name FROM person");
            rst.next(); // what if it returns a 'false' ?
            String firstName = rst.getString(1);

            // This is appropriate...
            Statement stat = conn.createStatement();
            ResultSet rst = stat.executeQuery("SELECT name FROM person");
            if (rst.next())
            {
                String firstName = rst.getString(1);
            }
            else
            {
                // here you deal with the error ( at least log it)
            }
            ]]>
        </example>
	</rule>

	<rule name="AvoidMultipleUnaryOperators" since="4.2"
		class="net.sourceforge.pmd.rules.basic.AvoidMultipleUnaryOperators"
		message="Using multiple unary operators may be a bug, and/or is confusing."
		externalInfoUrl="http://pmd.sourceforge.net/rules/basic.html#AvoidMultipleUnaryOperators">
		<description>
            <![CDATA[
					Using multiple unary operators may be a bug, and/or is confusing.
					Check the usage is not a bug, or consider simplifying the expression.
            ]]>
        </description>
		<priority>2</priority>
		<properties>
			<property name="xpath">
				<value>
		        	<![CDATA[
//UnaryExpression[
		./UnaryExpression
		or ./UnaryExpressionNotPlusMinus
		or ./PrimaryExpression/PrimaryPrefix/Expression/UnaryExpression
		or ./PrimaryExpression/PrimaryPrefix/Expression/UnaryExpressionNotPlusMinus
	]
|
//UnaryExpressionNotPlusMinus[
		./UnaryExpression
		or ./UnaryExpressionNotPlusMinus
		or ./PrimaryExpression/PrimaryPrefix/Expression/UnaryExpression
		or ./PrimaryExpression/PrimaryPrefix/Expression/UnaryExpressionNotPlusMinus
	]
		        	]]>
            	</value>
			</property>
		</properties>
		<example>
            <![CDATA[
            // These are typo bugs, or at best needlessly complex and confusing:
            int i = - -1;
            int j = + - +1;
            int z = ~~2;
            boolean b = !!true;
            boolean c = !!!true;

            // These are better:
            int i = 1;
            int j = -1;
            int z = 2;
            boolean b = true;
            boolean c = false;

            // And these just make your brain hurt:
            int i = ~-2;
            int j = -~7;
            ]]>
        </example>
	</rule>

	<!-- strings.xml -->
	<rule name="StringInstantiation" since="1.0"
		message="Avoid instantiating String objects; this is usually unnecessary."
		class="net.sourceforge.pmd.rules.strings.StringInstantiation"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#StringInstantiation">
		<description>
Avoid instantiating String objects; this is usually unnecessary.
    </description>
		<priority>2</priority>
		<example>
<![CDATA[
public class Foo {
 private String bar = new String("bar"); // just do a String bar = "bar";
}
]]>
    </example>
	</rule>

	<rule name="StringToString" since="1.0"
		message="Avoid calling toString() on String objects; this is unnecessary."
		class="net.sourceforge.pmd.rules.strings.StringToStringRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#StringToString">
		<description>
    Avoid calling toString() on String objects; this is unnecessary.
    </description>
		<priority>3</priority>
		<example>
<![CDATA[
public class Foo {
 private String baz() {
  String bar = "howdy";
  return bar.toString();
 }
}
]]>
    </example>
	</rule>

	<rule name="InefficientStringBuffering" since="3.4"
		message="Avoid concatenating nonliterals in a StringBuffer constructor or append()."
		class="net.sourceforge.pmd.rules.strings.InefficientStringBuffering"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#InefficientStringBuffering">
		<description>
Avoid concatenating non literals in a StringBuffer constructor or append().
    </description>
		<priority>3</priority>
		<example>
<![CDATA[
public class Foo {
 void bar() {
  // Avoid this
  StringBuffer sb=new StringBuffer("tmp = "+System.getProperty("java.io.tmpdir"));
  // use instead something like this
  StringBuffer sb = new StringBuffer("tmp = ");
  sb.append(System.getProperty("java.io.tmpdir"));
 }
}
]]>
    </example>
	</rule>

	<rule name="UnnecessaryCaseChange" since="3.3"
		message="Using equalsIgnoreCase() is cleaner than using toUpperCase/toLowerCase().equals()."
		class="net.sourceforge.pmd.rules.strings.UnnecessaryCaseChange"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#UnnecessaryCaseChange">
		<description>
Using equalsIgnoreCase() is faster than using toUpperCase/toLowerCase().equals()
       </description>
		<priority>3</priority>
		<example>
                 <![CDATA[
 public class Foo {
  public boolean bar(String buz) {
    // should be buz.equalsIgnoreCase("baz")
    return buz.toUpperCase().equals("baz");
    // another unnecessary toUpperCase()
    // return buz.toUpperCase().equalsIgnoreCase("baz");
  }
 }
                 ]]>
       </example>
	</rule>

	<rule name="UseStringBufferLength" since="3.4"
		message="This is an inefficient use of StringBuffer.toString; call StringBuffer.length instead."
		class="net.sourceforge.pmd.rules.strings.UseStringBufferLength"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#UseStringBufferLength">
		<description>
 Use StringBuffer.length() to determine StringBuffer length rather than using StringBuffer.toString().equals("")
          or StringBuffer.toString().length() ==.
      </description>
		<priority>3</priority>
		<example>
  <![CDATA[
public class Foo {
 void bar() {
  StringBuffer sb = new StringBuffer();
  // this is bad
  if(sb.toString().equals("")) {}
  // this is good
  if(sb.length() == 0) {}
 }
}

  ]]>
      </example>
	</rule>

	<rule name="AppendCharacterWithChar" since="3.5"
		message="Avoid appending characters as strings in StringBuffer.append."
		class="net.sourceforge.pmd.rules.strings.AppendCharacterWithChar"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#AppendCharacterWithChar">
		<description>
Avoid concatenating characters as strings in StringBuffer.append.
    </description>
		<priority>3</priority>
		<example>
<![CDATA[
public class Foo {
 void bar() {
  StringBuffer sb=new StringBuffer();
  // Avoid this
  sb.append("a");

  // use instead something like this
  StringBuffer sb=new StringBuffer();
  sb.append('a');
 }
}
]]>
    </example>
	</rule>

	<rule name="ConsecutiveLiteralAppends" since="3.5"
		message="StringBuffer.append is called {0} consecutive times with literal Strings. Use a single append with a single String."
		class="net.sourceforge.pmd.rules.strings.ConsecutiveLiteralAppends"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#ConsecutiveLiteralAppends">
		<description>
Consecutively calling StringBuffer.append with String literals
    </description>
		<priority>3</priority>
		<properties>
			<property name="threshold" description="The report threshold"
				value="1" />
		</properties>
		<example>
<![CDATA[
public class Foo {
 private void bar() {
   StringBuffer buf = new StringBuffer();
   buf.append("Hello").append(" ").append("World"); //bad
   buf.append("Hello World");//good
 }
}
]]>
    </example>
	</rule>

<!--
	<rule name="UseIndexOfChar" since="3.5"
		message="String.indexOf(char) is faster than String.indexOf(String)."
		class="net.sourceforge.pmd.rules.strings.UseIndexOfChar"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#UseIndexOfChar">
		<description>
Use String.indexOf(char) when checking for the index of a single character; it executes faster.
    </description>
		<priority>3</priority>
		<example>
<![CDATA[
public class Foo {
 void bar() {
  String s = "hello world";
  // avoid this
  if (s.indexOf("d") {}
  // instead do this
  if (s.indexOf('d') {}
 }
}
]]>
    </example>
	</rule>
-->
	<rule name="UselessStringValueOf" since="3.8"
		message="No need to call String.valueOf to append to a string." class="net.sourceforge.pmd.rules.strings.UselessStringValueOf"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#UselessStringValueOf">
		<description>
No need to call String.valueOf to append to a string; just use the valueOf() argument directly.
      </description>
		<priority>3</priority>
		<example>
<![CDATA[
public String convert(int i) {
  String s;
  s = "a" + String.valueOf(i); // Bad
  s = "a" + i; // Better
  return s;
}
]]>
          </example>
	</rule>

	<rule name="UseEqualsToCompareStrings" since="4.1"
		message="Use equals() to compare strings instead of ''=='' or ''!=''"
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/strings.html#UseEqualsToCompareStrings">
		<description>
Using '==' or '!=' to compare strings only works if intern version is used on both sides
    </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//EqualityExpression/PrimaryExpression
[(PrimaryPrefix/Literal
   [starts-with(@Image, '"')]
   [ends-with(@Image, '"')]
and count(PrimarySuffix) = 0)]
]]>
            </value>
			</property>
		</properties>
		<example>
<![CDATA[
class Foo {
  boolean test(String s) {
    if (s == "one") return true; //Bad
    if ("two".equals(s)) return true; //Better
    return false;
  }
}
]]>
    </example>
	</rule>

	<!-- design.xml -->
	<rule name="SimplifyBooleanReturns" since="0.9"
		message="Avoid unnecessary if..then..else statements when returning a boolean"
		class="net.sourceforge.pmd.rules.SimplifyBooleanReturns"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#SimplifyBooleanReturns">
		<description>
Avoid unnecessary if..then..else statements when returning a boolean.
    </description>
		<priority>3</priority>
		<example>
<![CDATA[
public class Foo {
  private int bar =2;
  public boolean isBarEqualsTo(int x) {
    // this bit of code
    if (bar == x) {
     return true;
    } else {
     return false;
    }
    // can be replaced with a simple
    // return bar == x;
  }
}
]]>
    </example>
	</rule>
<!--
	<rule name="ConstructorCallsOverridableMethod" since="1.04"
		message="Overridable {0} called during object construction" class="net.sourceforge.pmd.rules.ConstructorCallsOverridableMethod"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#ConstructorCallsOverridableMethod">
		<description>
Calling overridable methods during construction poses a risk of invoking methods on an
incompletely constructed object and can be difficult to discern.
It may leave the sub-class unable to construct its superclass or forced to
replicate the construction process completely within itself, losing the ability to call
super().  If the default constructor contains a call to an overridable method,
the subclass may be completely uninstantiable.   Note that this includes method calls
throughout the control flow graph - i.e., if a constructor Foo() calls a private method
bar() that calls a public method buz(), this denotes a problem.
      </description>
		<priority>1</priority>
		<example>
  <![CDATA[
public class SeniorClass {
  public SeniorClass(){
      toString(); //may throw NullPointerException if overridden
  }
  public String toString(){
    return "IAmSeniorClass";
  }
}
public class JuniorClass extends SeniorClass {
  private String name;
  public JuniorClass(){
    super(); //Automatic call leads to NullPointerException
    name = "JuniorClass";
  }
  public String toString(){
    return name.toUpperCase();
  }
}
  ]]>
      </example>
	</rule>
-->
	<rule name="CloseResource" since="1.2.2"
		message="Ensure that resources like this {0} object are closed after use"
		class="net.sourceforge.pmd.rules.CloseResource" externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#CloseResource">
		<description>
Ensure that resources (like Connection, Statement, and ResultSet objects) are always closed after use.
    </description>
		<priority>3</priority>
		<properties>
			<property name="types" value="Connection,Statement,ResultSet" />
		</properties>
		<example>
<![CDATA[
public class Bar {
 public void foo() {
  Connection c = pool.getConnection();
  try {
    // do stuff
  } catch (SQLException ex) {
    // handle exception
  } finally {
    // oops, should close the connection using 'close'!
    // c.close();
  }
 }
}
]]>
    </example>
	</rule>

	<rule name="NonStaticInitializer" since="1.5"
		message="Non-static initializers are confusing" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#NonStaticInitializer">
		<description>
A nonstatic initializer block will be called any time a constructor
is invoked (just prior to invoking the constructor).  While this
is a valid language construct, it is rarely used and is confusing.
       </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//Initializer[@Static='false']
]]>
                 </value>
			</property>
		</properties>
		<example>
   <![CDATA[
public class MyClass {
 // this block gets run before any call to a constructor
 {
  System.out.println("I am about to construct myself");
 }
}
   ]]>
       </example>
	</rule>

	<rule name="DefaultLabelNotLastInSwitchStmt" since="1.5"
		message="The default label should be the last label in a switch statement"
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#DefaultLabelNotLastInSwitchStmt">
		<description>
By convention, the default label should be the last label in a switch statement.
       </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
<![CDATA[
//SwitchStatement
 [not(SwitchLabel[position() = last()][@Default='true'])]
 [SwitchLabel[@Default='true']]
]]>
                 </value>
			</property>
		</properties>
		<example>
   <![CDATA[
public class Foo {
 void bar(int a) {
  switch (a) {
   case 1:  // do something
      break;
   default:  // the default case should be last, by convention
      break;
   case 2:
      break;
  }
 }
}   ]]>
       </example>
	</rule>
<!--
	<rule name="OptimizableToArrayCall" since="1.8"
		message="This call to Collection.toArray() may be optimizable" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#OptimizableToArrayCall">
		<description>
A call to Collection.toArray can use the Collection's size vs an empty Array of the desired type.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
                  <![CDATA[
//PrimaryExpression
[PrimaryPrefix/Name[ends-with(@Image, 'toArray')]]
[
PrimarySuffix/Arguments/ArgumentList/Expression
 /PrimaryExpression/PrimaryPrefix/AllocationExpression
 /ArrayDimsAndInits/Expression/PrimaryExpression/PrimaryPrefix/Literal[@Image='0']
]

                  ]]>
              </value>
			</property>
		</properties>
		<example>
  <![CDATA[
class Foo {
 void bar(Collection x) {
   // A bit inefficient
   x.toArray(new Foo[0]);
   // Much better; this one sizes the destination array, avoiding
   // a reflection call in some Collection implementations
   x.toArray(new Foo[x.size()]);
 }
}
  ]]>
      </example>
	</rule>
-->
	<rule name="EqualsNull" since="1.9"
		message="Avoid using equals() to compare against null" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#EqualsNull">
		<description>
Inexperienced programmers sometimes confuse comparison concepts
and use equals() to compare to null.
        </description>
		<priority>1</priority>
		<properties>
			<property name="xpath">
				<value>
    <![CDATA[
//PrimaryExpression
 [
PrimaryPrefix/Name[ends-with(@Image, 'equals')]
or
PrimarySuffix[ends-with(@Image, 'equals')]
]
[PrimarySuffix/Arguments/ArgumentList[count(Expression)=1]
  /Expression/PrimaryExpression/PrimaryPrefix
   /Literal/NullLiteral]
    ]]>
                </value>
			</property>
		</properties>
		<example>
       <![CDATA[
class Bar {
   void foo() {
       String x = "foo";
       if (x.equals(null)) { // bad!
        doSomething();
       }
   }
}
    ]]>
        </example>
	</rule>

	<rule name="InstantiationToGetClass" since="2.0"
		message="Avoid instantiating an object just to call getClass() on it; use the .class public member instead"
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#InstantiationToGetClass">
		<description>
Avoid instantiating an object just to call getClass() on it; use the .class public member instead.
      </description>
		<priority>4</priority>
		<properties>
			<property name="xpath">
				<value>
                <![CDATA[
//PrimarySuffix
 [@Image='getClass']
 [parent::PrimaryExpression
  [PrimaryPrefix/AllocationExpression]
  [count(PrimarySuffix) = 2]
 ]
     ]]>
            </value>
			</property>
		</properties>
		<example>
    <![CDATA[
public class Foo {
 // Replace this
 Class c = new String().getClass();
 // with this:
 Class c = String.class;
}
    ]]>
        </example>
	</rule>

	<rule name="IdempotentOperations" since="2.0"
		message="Avoid idempotent operations (like assigning a variable to itself)."
		class="net.sourceforge.pmd.rules.IdempotentOperations"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#IdempotentOperations">
		<description>
Avoid idempotent operations - they are have no effect.
      </description>
		<priority>3</priority>

		<example>
      <![CDATA[
public class Foo {
 public void bar() {
  int x = 2;
  x = x;
 }
}
      ]]>
      </example>
	</rule>
	<rule name="MissingStaticMethodInNonInstantiatableClass" since="3.0"
		message="Class cannot be instantiated and does not provide any static methods or fields"
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#MissingStaticMethodInNonInstantiatableClass">
		<description>
A class that has private constructors and does not have any static methods or fields cannot be used.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
    <![CDATA[
//ClassOrInterfaceDeclaration[@Nested='false'][
( count(./ClassOrInterfaceBody/ClassOrInterfaceBodyDeclaration/ConstructorDeclaration)>0
  and count(./ClassOrInterfaceBody/ClassOrInterfaceBodyDeclaration/ConstructorDeclaration) = count(./ClassOrInterfaceBody/ClassOrInterfaceBodyDeclaration/ConstructorDeclaration[@Private='true']) )
and
count(.//MethodDeclaration[@Static='true'])=0
and
count(.//FieldDeclaration[@Private='false'][@Static='true'])=0
]
    ]]>
              </value>
			</property>
		</properties>
		<example>
<![CDATA[
/* This class is unusable, since it cannot be
 instantiated (private constructor),
 and no static method can be called.
 */
public class Foo {
 private Foo() {}
 void foo() {}
}

]]>
      </example>
	</rule>


	<rule name="AvoidSynchronizedAtMethodLevel" since="3.0"
		message="Use block level rather than method level synchronization"
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#AvoidSynchronizedAtMethodLevel">
		<description>
  Method level synchronization can backfire when new code is added to the method.  Block-level
  synchronization helps to ensure that only the code that needs synchronization gets it.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
    <![CDATA[
//MethodDeclaration[@Synchronized='true']
    ]]>
              </value>
			</property>
		</properties>
		<example>
<![CDATA[
public class Foo {
 // Try to avoid this
 synchronized void foo() {
 }
 // Prefer this:
 void bar() {
  synchronized(this) {
  }
 }
}
]]>
      </example>
	</rule>

	<rule name="AvoidInstanceofChecksInCatchClause" since="3.0"
		message="An instanceof check is being performed on the caught exception.  Create a separate catch clause for this exception type."
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#AvoidInstanceofChecksInCatchClause">
		<description>
Each caught exception type should be handled in its own catch clause.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
    <![CDATA[
//CatchStatement/FormalParameter
 /following-sibling::Block//InstanceOfExpression/PrimaryExpression/PrimaryPrefix
  /Name[
   @Image = ./ancestor::Block/preceding-sibling::FormalParameter
    /VariableDeclaratorId/@Image
  ]
    ]]>
              </value>
			</property>
		</properties>
		<example>
<![CDATA[
try { // Avoid this
 // do something
} catch (Exception ee) {
 if (ee instanceof IOException) {
  cleanup();
 }
}
try {  // Prefer this:
 // do something
} catch (IOException ee) {
 cleanup();
}
]]>
      </example>
	</rule>

	<rule name="AbstractClassWithoutAbstractMethod" since="3.0"
		message="This abstract class does not have any abstract methods"
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#AbstractClassWithoutAbstractMethod">
		<description>
The abstract class does not contain any abstract methods. An abstract class suggests
an incomplete implementation, which is to be completed by subclasses implementing the
abstract methods. If the class is intended to be used as a base class only (not to be instantiated
direcly) a protected constructor can be provided prevent direct instantiation.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value><![CDATA[
//ClassOrInterfaceDeclaration
 [@Abstract='true'
  and count( .//MethodDeclaration[@Abstract='true'] )=0 ]
  [count(ImplementsList)=0]
  [count(.//ExtendsList)=0]
              ]]>
              </value>
			</property>
		</properties>
		<example>
<![CDATA[
public abstract class Foo {
 void int method1() { ... }
 void int method2() { ... }
 // consider using abstract methods or removing
 // the abstract modifier and adding protected constructors
}
]]>
      </example>
	</rule>

	<rule name="SimplifyConditional" since="3.1"
		message="No need to check for null before an instanceof" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#SimplifyConditional">
		<description>
No need to check for null before an instanceof; the instanceof keyword returns false when given a null argument.
          </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
                      <![CDATA[
//Expression
 [ConditionalOrExpression
 [EqualityExpression[@Image='==']
  //NullLiteral
  and
  UnaryExpressionNotPlusMinus
   [@Image='!']//InstanceOfExpression[PrimaryExpression
     //Name/@Image = ancestor::ConditionalOrExpression/EqualityExpression
      /PrimaryExpression/PrimaryPrefix/Name/@Image]]
or
ConditionalAndExpression
 [EqualityExpression[@Image='!=']//NullLiteral
 and
InstanceOfExpression
 [PrimaryExpression[count(PrimarySuffix[@ArrayDereference='true'])=0]
  //Name/@Image = ancestor::ConditionalAndExpression
   /EqualityExpression/PrimaryExpression/PrimaryPrefix/Name/@Image]]]
 ]]>
                  </value>
			</property>
		</properties>
		<example>
      <![CDATA[
class Foo {
 void bar(Object x) {
  if (x != null && x instanceof Bar) {
   // just drop the "x != null" check
  }
 }
}      ]]>
           </example>
	</rule>

	<rule name="PositionLiteralsFirstInComparisons" since="3.3"
		message="Position literals first in String comparisons" class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#PositionLiteralsFirstInComparisons">
		<description>
 Position literals first in String comparisons - that way if the String is null you won't get a NullPointerException, it'll just return false.
  </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
              <![CDATA[
//PrimaryExpression[
        PrimaryPrefix[Name
                [
	(ends-with(@Image, '.equals'))
                ]
        ]
        [
                   (../PrimarySuffix/Arguments/ArgumentList/Expression/PrimaryExpression/PrimaryPrefix/Literal)
	and
	( count(../PrimarySuffix/Arguments/ArgumentList/Expression) = 1 )
        ]
]
[not(ancestor::Expression/ConditionalAndExpression//EqualityExpression[@Image='!=']//NullLiteral)]
[not(ancestor::Expression/ConditionalOrExpression//EqualityExpression[@Image='==']//NullLiteral)]

          ]]>
          </value>
			</property>
		</properties>
		<example>
<![CDATA[
class Foo {
 boolean bar(String x) {
  return x.equals("2"); // should be "2".equals(x)
 }
}

]]>
  </example>
	</rule>

	<rule name="UncommentedEmptyMethod" since="3.4" message="Document empty method"
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#UncommentedEmptyMethod">
		<description>
Uncommented Empty Method finds instances where a method does not contain
statements, but there is no comment. By explicitly commenting empty methods
it is easier to distinguish between intentional (commented) and unintentional
empty methods.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
    <![CDATA[
//MethodDeclaration/Block[count(BlockStatement) = 0 and @containsComment = 'false']
 ]]>
             </value>
			</property>
		</properties>
		<example>
  <![CDATA[
public void doSomething() {
}
 ]]>
      </example>
	</rule>
	<rule name="AvoidConstantsInterface" since="3.5"
		message="An Interface should be used only to model a behaviour; consider converting this to a class."
		class="net.sourceforge.pmd.rules.XPathRule"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#AvoidConstantsInterface">
		<description>
     An interface should be used only to model a behaviour of a
    class: using an interface as a container of constants is a poor usage pattern.
      </description>
		<priority>3</priority>
		<properties>
			<property name="xpath">
				<value>
    <![CDATA[
//ClassOrInterfaceDeclaration[@Interface="true"]
    [
     count(.//MethodDeclaration)=0
     and
     count(.//FieldDeclaration)>0
    ]
    ]]>
        </value>
			</property>
		</properties>
		<example>
    <![CDATA[
    public interface ConstantsInterface {
     public static final int CONSTANT1=0;
     public static final String CONSTANT2="1";
    }
    ]]>
      </example>
	</rule>
	<rule name="UseCollectionIsEmpty" since="3.9"
		message="Substitute calls to size() == 0 (or size() != 0) with calls to isEmpty()"
		class="net.sourceforge.pmd.rules.design.UseCollectionIsEmpty"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#UseCollectionIsEmpty">
		<description>
The isEmpty() method on java.util.Collection is provided to see if a collection has any elements.
Comparing the value of size() to 0 merely duplicates existing behavior.
      </description>
		<priority>3</priority>
		<example>
    <![CDATA[
	public class Foo {
		void good() {
        	List foo = getList();
			if (foo.isEmpty()) {
				// blah
			}
    	}

	    void bad() {
    	    List foo = getList();
				if (foo.size() == 0) {
					// blah
				}
	    	}
	}
    ]]>
      </example>
	</rule>
<!--
	<rule name="EmptyMethodInAbstractClassShouldBeAbstract" since="4.1"
		class="net.sourceforge.pmd.rules.XPathRule"
		message="An empty method in an abstract class should be abstract instead"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#EmptyMethodInAbstractClassShouldBeAbstract">
		<description>
An empty method in an abstract class should be abstract instead, as developer may rely on this empty implementation
rather than code the appropriate one.
        </description>
		<priority>1</priority>
		<properties>
			<property name="xpath">
				<value>
                <![CDATA[
                    //ClassOrInterfaceDeclaration[@Abstract = 'true']
                        /ClassOrInterfaceBody
                        /ClassOrInterfaceBodyDeclaration
                        /MethodDeclaration[@Abstract = 'false' and @Native = 'false']
                        [
                            ( boolean(./Block[count(./BlockStatement) =  1]/BlockStatement/Statement/ReturnStatement/Expression/PrimaryExpression/PrimaryPrefix/Literal/NullLiteral) = 'true' )
                            or
                            ( boolean(./Block[count(./BlockStatement) =  1]/BlockStatement/Statement/ReturnStatement/Expression/PrimaryExpression/PrimaryPrefix/Literal[@Image = '0']) = 'true' )
                    		or
							( boolean(./Block[count(./BlockStatement) =  1]/BlockStatement/Statement/ReturnStatement/Expression/PrimaryExpression/PrimaryPrefix/Literal[string-length(@Image) = 2]) = 'true' )
							or
							(
								(
									(boolean(./Block/BlockStatement/Statement/ReturnStatement/Expression/PrimaryExpression/PrimaryPrefix/Literal[@Image = '']) = 'true' )
								)
								and
								( count (./Block/*) = 1 )
							)
                            or
                            ( count (./Block/*) = 0 )
                        ]
                ]]>
             </value>
			</property>
		</properties>
		<example>
        	<![CDATA[
				public abstract class ShouldBeAbstract
				{
				    public Object couldBeAbstract()
				    {
					// Should be abstract method ?
					return null;
				   	}

				    public void couldBeAbstract()
				    {
				    }
				}
	     	]]>
    	</example>
	</rule>
-->
	<rule name="SingularField" since="3.1"
		message="Perhaps ''{0}'' could be replaced by a local variable."
		class="net.sourceforge.pmd.rules.design.SingularField"
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#SingularField">
		<description>
      		<![CDATA[
This field is used in only one method and the first usage is assigning a value
to the field. This probably means that the field can be changed to a local variable.
			]]>
      </description>
		<priority>3</priority>
		<properties>
			<!--
				Disabled by default because these options generate false positives
			-->
			<property name="CheckInnerClasses" description="Check inner classes">
				<value>false</value>
			</property>
			<property name="DisallowNotAssignment"
				description="Disallow violations where the first usage is not an assignment">
				<value>false</value>
			</property>
		</properties>
		<example><![CDATA[
public class Foo {
    private int x;  //Why bother saving this?
    public void foo(int y) {
     x = y + 5;
     return x;
    }
}
   ]]></example>
	</rule>
 
	<rule name="AbstractClassWithoutAnyMethod" since="4.2"
		class="net.sourceforge.pmd.rules.XPathRule"
		message="No abstract method which means that the  keyword is most likely used to prevent instantiation. use a private or protected constructor instead."
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#AbstractClassWithoutAnyMethod">
		<description>
            <![CDATA[
			If the abstract class does not provides any methods, it may be just a data container that is not to be instantiated. In this case, it's probably
			better to use a private or a protected constructor in order to prevent instantiation than make the class misleadingly abstract.
            ]]>
        </description>
		<priority>4</priority>
		<properties>
			<property name="xpath">
				<value>
                    <![CDATA[
//ClassOrInterfaceDeclaration[
	(@Abstract = 'true')
	and
	(count(//MethodDeclaration) + count(//ConstructorDeclaration) = 0)
]
                    ]]>
                </value>
			</property>
		</properties>
		<example>
            <![CDATA[
public class abstract Example {
	String field;
	int otherField;
}
            ]]>
        </example>
	</rule>

	<rule name="TooFewBranchesForASwitchStatement" since="4.2"
		class="net.sourceforge.pmd.rules.XPathRule"
		message="A switch with less than 3 branches is inefficient, use a if statement instead."
		externalInfoUrl="http://pmd.sourceforge.net/rules/design.html#TooFewBranchesForASwitchStatement">
		<description>
		    <![CDATA[
			     Swith are designed complex branches, and allow branches to share treatement. Using a switch for only a few 
			     branches is ill advised, as switches are not as easy to understand as if. In this case, it's most likely
			     is a good idea to use a if statement instead, at least to increase code readability.
			     ]]>
	    </description>
		<priority>1</priority>
		<properties>
			<property name="minimumNumberCaseForASwitch" description="Minimum number of branches for a switch"
				value="3" />
			<property name="xpath">
				<value>
				    <![CDATA[
					     //SwitchStatement[
					     (count(.//SwitchLabel) < $minimumNumberCaseForASwitch)
								      ]
								      ]]>
					     </value>
			</property>
		</properties>
		<example>
				     <![CDATA[
// With a minimumNumberCaseForASwitch of 3	    
public class Foo {
	public void bar() {
		switch (condition) {
			case ONE:
				instruction;
				break;
			default:
				break; // not enough for a 'switch' stmt, a simple 'if' stmt would have been more appropriate
		}
	}
}
					      ]]>
			     </example>
	</rule>

</ruleset>
