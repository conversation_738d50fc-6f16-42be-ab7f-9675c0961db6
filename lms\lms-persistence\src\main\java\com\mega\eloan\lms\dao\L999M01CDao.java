/* 
 * L999M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L999M01C;


/** 企金約據書連保人(保證人)檔 **/
public interface L999M01CDao extends IGenericDao<L999M01C> {

	L999M01C findByOid(String oid);

	List<L999M01C> findByMainId(String mainId);

	L999M01C findByUniqueKey(String mainId, String custId, String dupNo,String custPos);

	List<L999M01C> findByIndex01(String mainId, String custId, String dupNo,String custPos);
	List<L999M01C> findByCustIdDupId(String custId,String DupNo);
	List<L999M01C> findByOids(String[] oids);
}