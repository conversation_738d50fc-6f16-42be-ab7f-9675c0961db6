pageJsInit(function() {
	$(function() {
		//初始化作業
		//設定經辦下拉選單(派案-指定簽案行員)
		$.ajax({
			type: "POST",
			handler: "cls1220m10formhandler",
			action: "getMegaEmpInfo"
		}).done(function(responseData) {
			if (responseData.Success) {
				var signMegaEmp = $("#SignMegaEmp");
				signMegaEmp.setItems({
					item: responseData.bossListAO,
					space: true,
					format: "{value} {key}"
				});
				var signMegaEmpExcel = $("#SignMegaEmpExcel");
				signMegaEmpExcel.setItems({
					item: responseData.bossListAO,
					space: true,
					format: "{value} {key}"
				});

			}
		});

		var grid = $("#gridview").iGrid({
			handler: 'cls1222gridhandler',
			height: 350,
			width: 800,
			autowidth: false,
			action: "queryView_Q",
			postData: {
				docStatus: viewstatus
			},
			rowNum: 15,
			sortname: "applyTS",
			sortorder: "desc",
			multiselect: false,
			colModel: [
				{ name: 'oid', hidden: true }
				, { name: 'mainId', hidden: true }
				, { name: 'docStatus', hidden: true }
				, { name: 'ownBrId', hidden: true }
				, { name: 'applyKind', hidden: true }
				, { name: 'incomType', hidden: true }
				, {
					//身分證統編
					colHeader: i18n.cls1220v11['C122M01A.custId'],
					width: 90,
					sortable: true,
					name: 'custId',
					formatter: 'click',
					onclick: openDoc
				}, {
					//借款人姓名
					colHeader: i18n.cls1220v11['C122M01A.custName'],
					align: "left",
					width: 90,
					sortable: true,
					name: 'custName'
				}, {
					//線上進件時間 >> 進件時間
					colHeader: i18n.cls1220v11['C122M01A.applyTS'],
					align: "left",
					width: 100,
					name: 'applyTS'
				}, {
					//進件類型
					colHeader: i18n.cls1220v11['C122M01A.incomType'],
					align: "left",
					width: 60,
					sortable: false,
					name: 'incomTypeDesc'
				}, {
					//案件類型
					colHeader: i18n.cls1220v11['C122M01A.applyKind'],
					align: "left",
					width: 60,
					sortable: false,
					name: 'applyKindDesc'
				}, {
					//申貸案件狀態
					colHeader: i18n.cls1220v11['C122M01A.docStatus'],
					align: "left",
					width: 60,
					sortable: false,
					name: 'docStatusDesc'
				}, {
					//備註
					colHeader: i18n.cls1220v11['C122M01A.notifyMemo'],
					align: "left",
					width: 140,
					name: 'notifyMemo'
				}, {
					//案件編號
					colHeader: i18n.cls1220v11['C122M01A.ploanCaseNo'],
					align: "left",
					width: 100,
					name: 'ploanCaseId'
				}, {
					//行銷方案
					colHeader: i18n.cls1220v11['csc_ploanPlan'],
					align: "left",
					width: 60,
					name: 'ploanPlan'
				}, {
					//J-112-0006 取消顯示[最後異動者]，新增[地政士]
					//最後異動者
					//    colHeader : i18n.cls1220v11['C122M01A.updater'],
					//	align : "left",
					//	width : 60,
					//	name : 'updater'
					//地政士
					colHeader: i18n.cls1220v11['C122M01A.laaName'],
					align: "left",
					width: 60,
					name: 'laaName'
				}, {
					//簽案人員
					colHeader: i18n.cls1220v11['C122M01A.signMegaEmpName'],
					align: "left",
					width: 60,
					name: 'c122m01f.signMegaEmpName'
				}]
		});


		function openDoc(cellvalue, options, rowObject) {
			var applyKind = rowObject.applyKind;
			var murl = '';
			switch (applyKind) {
				case 'H':
					murl = '../lms/cls1220m01/01';
					break;
				case 'C':
					murl = '../lms/cls1220m02/01';
					break;
				case 'B':
					murl = '../lms/cls1220m03/01';
					break;
				case 'E':
					murl = '../lms/cls1220m05/01';
					break;
				case 'F':
					murl = '../lms/cls1220m05/01';
					break;
				case 'P':
					murl = '../lms/cls1220m04/01';
					break;
				case 'Q':
					murl = '../lms/cls1220m04/01';
					break;
				case 'I':
				case 'J':
					murl = '../lms/cls1220m04/01';
					break;
				case 'O':
					murl = '../lms/cls1220m05/01';
					break;
			}

			$.form.submit({
				url: murl,
				data: {
					mainOid: rowObject.oid,
					mainId: rowObject.mainId,
					mainDocStatus: rowObject.docStatus,
					mainApplyKind: rowObject.applyKind,
					mainIncomType: rowObject.incomType,
					docStatus: viewstatus
				},
				target: rowObject.oid
			});
		};

		$("#buttonPanel").find('#btnView').click(function() {
			var selrow = grid.getGridParam('selrow');
			if (selrow) {
				openDoc('', '', grid.getRowData(selrow));
			}
			else {
				CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
			}
		}).end().find("#btnFilter").click(function() {
			$("#filterBoxForm").reset();
			$("#incomTypeTr").hide();
			$("#filterBoxDiv").thickbox({ // 使用選取的內容進行彈窗
				title: i18n.def.query,
				width: 590,
				height: 350,
				align: "center",
				valign: "bottom",
				modal: false,
				i18n: i18n.def,
				buttons: {
					"sure": function() {
						$.thickbox.close();
						//~~~~
						grid.jqGrid("setGridParam", {
							postData: $.extend({ docStatus: viewstatus }, $("#filterBoxForm").serializeData()),
							search: true
						}).trigger("reloadGrid");
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}).end().find("#btnCreateExl").click(function() {
			$("#excelBoxForm").reset();
			$("#incomTypeTrExcel").hide();
			$("#purchaseHouseTrExcel").hide();
			$("#excelBoxDiv").thickbox({ // 使用選取的內容進行彈窗
				title: i18n.def.query,
				width: 590,
				height: 350,
				align: "center",
				valign: "bottom",
				modal: false,
				i18n: i18n.def,
				buttons: {
					"sure": function() {
						//起訖日為必填
						if ($("#excelBoxForm").find("#applyTS_begExcel").val() == '' || $("#excelBoxForm").find("#applyTS_endExcel").val() == '') {
							CommonAPI.showErrorMessage("資料起訖日必填！");
						} else {
							//開始產生EXCEL報表
							$.ajax({
								type: "POST",
								handler: "cls1220m10formhandler",
								action: "makeExcelFileNmae",
								data: {
									applyTS_beg: $("#excelBoxForm").find("#applyTS_begExcel").val(),
									applyTS_end: $("#excelBoxForm").find("#applyTS_endExcel").val(),
									applyKind: $("#excelBoxForm").find("#applyKindExcel").val(),
									purchaseHouse: $("#excelBoxForm").find("#purchaseHouseExcel").val()
								}
							}).done(function(responseData) {
								if (responseData.Success) {
									$.form.submit({
										url: __ajaxHandler,
										target: "_blank",
										data: {
											_pa: 'lmsdownloadformhandler',
											'custId': $("#excelBoxForm").find("#custIdExcel").val(),
											'ploanCaseId': $("#excelBoxForm").find("#ploanCaseIdExcel").val(),
											'applyKind': $("#excelBoxForm").find("#applyKindExcel").val(),
											'incomType': $("#excelBoxForm").find("#incomTypeExcel").val(),
											'applyTS_beg': $("#excelBoxForm").find("#applyTS_begExcel").val(),
											'applyTS_end': $("#excelBoxForm").find("#applyTS_endExcel").val(),
											'SdocStatus': $("#excelBoxForm").find("#SdocStatusExcel").val(),
											'SignMegaEmp': $("#excelBoxForm").find("#SignMegaEmpExcel").val(),
											'ploanPlan': $("#excelBoxForm").find("#ploanPlanExcel").val(),
											'purchaseHouse': $("#excelBoxForm").find("#purchaseHouseExcel").val(),
											'fileDownloadName': responseData.fileNmae,
											'serviceName': "cls1220r11rptservice"
										}
									});
									$.thickbox.close();
								}
							});
						}
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}).end().find("#btnCaseReturn").click(function() {
			//[不承作]跟[取消]的案件才可回復
			var selrow = grid.getGridParam('selrow');
			if (selrow) {
				var data = grid.getRowData(selrow);
				//待派案之案件不得改派，請於確認案件內容後於執行派案
				if (data.docStatus == 'G00' || data.docStatus == 'I00') {
					$.ajax({
						type: "POST",
						handler: "cls1220m10formhandler",
						action: "caseReturn",
						data: {
							mainOid: data.oid,
							mainId: data.mainId
						}
					}).done(function(responseData) {
						if (responseData.Success) {
							//							$.thickbox.close();
							grid.trigger("reloadGrid");
						}
					});
				} else {
					CommonAPI.showErrorMessage("作廢及已動審之案件不得回復");
				}
			}
		});

		$("#applyKind").change(function() {
			var applyKind = $("#applyKind").val();
			if (applyKind == 'E' || applyKind == 'P' || applyKind == 'I' || applyKind == 'J') {
				$("#incomTypeTr").show();
			} else {
				$("#incomType").val('');
				$("#incomTypeTr").hide();
			}
		});

		$("#applyKindExcel").change(function() {
			var applyKind = $("#applyKindExcel").val();
			if (applyKind == 'E' || applyKind == 'O') { //案件類型新增[其他]
				$("#purchaseHouseTrExcel").show();
			} else {
				$("#purchaseHouseExcel").val('');
				$("#purchaseHouseTrExcel").hide();
			}
			if (applyKind == 'E' || applyKind == 'P' || applyKind == 'I' || applyKind == 'J') {
				$("#incomTypeTrExcel").show();
			} else {
				$("#incomTypeExcel").val('');
				$("#incomTypeTrExcel").hide();
			}
		});

	});
});