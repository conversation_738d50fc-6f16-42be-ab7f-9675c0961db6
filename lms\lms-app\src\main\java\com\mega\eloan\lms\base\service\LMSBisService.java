/*
 * LMSService.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import java.math.BigDecimal;
import java.util.List;


import org.kordamp.json.JSONObject;

import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.model.L120S25A;
import com.mega.eloan.lms.model.L120S25C;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.ICapService;

/**
 * <pre>
 * J-110-0986_05097_B1001 於簽報書新增LGD欄位
 * </pre>
 *
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */
/**
 * <AUTHOR>
 * 
 */
public interface LMSBisService extends ICapService {
	/**
	 * save
	 * 
	 * @param entity
	 */
	void save(GenericBean... entity);

	@SuppressWarnings("rawtypes")
	Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * delete
	 * 
	 * @param entity
	 */
	void delete(GenericBean... entity);

	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId);

	public List<L120S25C> findL120s25cByParamTypeKeyDate(String paramType,
			String paramKey, String paramDate);

	public void deleteListL120s25c(List<L120S25C> list);

	public void impBisEstimatedReturn(String kind,
			String mainId, String queryDateE, JSONObject jsonData,
			BranchRate branchRate, String qCustId, String qDupNo,
			Boolean isKindA) throws CapException;

	/**
	 * J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
	 * 
	 * 判斷BIS版本
	 * 
	 * @param l120s25a
	 * @param sign
	 * @param baseVersion
	 * @return
	 */
	public boolean compareBisVersion(L120S25A l120s25a, String sign,
			BigDecimal baseVersion);

	/**
	 * J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
	 * 
	 * @param listL120s25a
	 * @return
	 */
	public boolean showBisTotalNotNa(List<L120S25A> listL120s25a);

}