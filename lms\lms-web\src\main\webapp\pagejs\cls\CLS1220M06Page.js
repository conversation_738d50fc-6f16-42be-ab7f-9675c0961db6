var initDfd = window.initDfd || $.Deferred();
$(function(){
    var _cls1220m06handler = "cls1220m06formhandler";
    tabForm_m06 = $("#tabForm");
  
    tabForm_m06.find("#includeTaxData").click(function(){
        var custId = $("#custId").val();
        var custCompanyId = $("#n_cnumber").val();
        var dupNo = $("#dupNo").val();
        if (!custCompanyId) {
            API.showMessage("請先填寫統一編號!");
        }else{
        	//先檢查公司統編負責人是否與本案申請人的ID一致，不一致或無資料要出提示訊息
        	$.ajax({
                handler: _cls1220m06handler,
                action: "checkCustIdWithCompanyId",
                data: {
                    custId: custId,
                    custCompanyId: custCompanyId
                }
            }).done(function(json2){     	
                if(json2.checkResult=="OK"){
                    queryCustData(custId,custCompanyId,dupNo);
                }else{
                    //公司統編負責人與本案申請ID不一致
                    API.showMessage(json2.message);
                }
            });
        }

    });
    
    
    function queryCustData(custId,custCompanyId,dupNo) {
        //引入稅籍資料
        $.ajax({
            handler: _cls1220m06handler,
            action: "queryCustDataforNonNB",
            data: {
                custId: custId,
                custCompanyId: custCompanyId,
                dupNo: dupNo ? dupNo : "0"
            }
        }).done(function(json2){
            if(json2.ctype){
                tabForm_m06.find("input[name='ctype'][value='" + json2.ctype +"']").trigger('click');
            }
            tabForm_m06.injectData(json2);
            tabForm_m06.find("#city").trigger('change');
            tabForm_m06.find("#citya").trigger('change');
            setTimeout(function(){
                // 因待區/市/鄉/鎮下拉選單載入

                tabForm_m06.find("#cityarea").val(json2.cityarea);
                tabForm_m06.find("#cityareaa").val(json2.cityareaa);
            }, 1200);
            
        });
    }
    
	tabForm_m06.find("[name=ctype]").click(function(){ // 事業體名稱 其它
		tabForm_m06.find("#ctype_ctxt").readOnly(this.id != 'ctype5');
    }).end().find("#a1").click(function(){ // 100萬以上,曾經獲貸「青年創業及啟動金貸款」，獲貸
    	tabForm_m06.find("#a1_1,#a1_2,#a1_3").readOnly(!$(this).is(':checked'));
    	tabForm_m06.find("#dpBankNameLink")[$(this).is(':checked')?'show':'hide']();
    }).end().find("#a11").click(function(){ //   100萬以下,曾經獲貸「青年創業及啟動金貸款」，獲貸
    	tabForm_m06.find("#dpBankNameLink")[$(this).is(':checked')?'show':'hide']();
    	tabForm_m06.find("input[name=a1_1_]").prop('disabled', !$(this).is(':checked'));
    }).end().find("#a2").click(function(){ // 100萬以上,曾獲貸其他政府辦理之創業貸款
    	tabForm_m06.find("#a1_5").readOnly(!$(this).is(':checked'));
    //}).end().find("#a12").click(function(){ // 100萬以下,曾獲貸其他政府辦理之創業貸款
    //	tabForm_m06.find("#a1_ctxt2").readOnly(!$(this).is(':checked'));
    //}).end().find("input[name=a2]").click(function(){ // 曾獲貸其他政府辦理之創業貸款
    //    var isOver100 = "2" == tabForm_m06.find("#apply_type").val(); // 100萬以上
    //    var endS = parseInt(this.id.substr(2));
    //    var isChecked = $(this).is(':checked');
    //    if(isOver100){
    //        var endS2 = endS * 2;
    //        tabForm_m06.find("#a3_" + endS + ",#a4_" + (endS2-1) + ",#a4_" + endS2).readOnly(!isChecked);
    //    }else{
    //    	tabForm_m06.find("#a2_" + endS).readOnly(!isChecked);
    //    }
    }).end().find("#n_cnumber").blur(function(){// 100 萬元以下需同步統編
    	tabForm_m06.find("#n_cnumber2").val($(this).val());
    }).end().find("#cform").click(function(){ // 同戶籍
        if($(this).is(':checked')){
        	tabForm_m06.find("#cityb,#cityareab,#address2").readOnly(true);
        	tabForm_m06.find("#cityb").val(tabForm_m06.find("#citya").val());
        	tabForm_m06.find("#cityareab").setOptions("counties"+tabForm_m06.find("#cityb").val(),false);
        	tabForm_m06.find("#cityareab").val(tabForm_m06.find("#cityareaa").val());
        	tabForm_m06.find("#address2").val(tabForm_m06.find("#address").val());
        }else{
        	tabForm_m06.find("#cityb,#cityareab,#address2").readOnly(false);
        }
    }).end().find("#citya,#cityb,#city,#city2,#city3").change(function(){ // 縣/市
        var ends = this.id.substr(4);
        tabForm_m06.find("#cityarea"+ ends).setOptions("counties"+$(this).val(),false);
    }).end().find("#a31,#a32,#a33").click(function(){ // 100萬以下 六、 本次申請之貸款用途與金額
        var endS = parseInt(this.id.substr(2));
		switch (this.id) {
            case "a31":
            	tabForm_m06.find("#a3_1,#a3_2,#a3_3").readOnly(!$(this).is(':checked'));
            	tabForm_m06.find("#a3_4,#a3_5,#a3_6,[name=a3_7]").readOnly($(this).is(':checked'));
            	tabForm_m06.find("#a3_9,#a3_10,#a3_11,#a3_12").readOnly($(this).is(':checked'));
				break;
			case "a32":
				tabForm_m06.find("#a3_1,#a3_2,#a3_3").readOnly($(this).is(':checked'));
				tabForm_m06.find("#a3_4,#a3_5,#a3_6,[name=a3_7]").readOnly(!$(this).is(':checked'));
				tabForm_m06.find("#a3_9,#a3_10,#a3_11,#a3_12").readOnly($(this).is(':checked'));
                break;
            case "a33":
            	tabForm_m06.find("#a3_1,#a3_2,#a3_3").readOnly($(this).is(':checked'));
            	tabForm_m06.find("#a3_4,#a3_5,#a3_6,[name=a3_7]").readOnly($(this).is(':checked'));
            	tabForm_m06.find("#a3_9,#a3_10,#a3_11,#a3_12").readOnly(!$(this).is(':checked'));
                break;
            default:
		}
	}).end().find("[name=a3_7]").click(function(){ // 100萬以下 六、 本次申請之貸款用途與金額
		tabForm_m06.find("#a3_8").readOnly(!$("#a3_75").is(':checked'))
	}).end().find("#a0,#a1,#a2").click(function(){ // 100萬以上 首次申請
        if('a0' == this.id){
        	tabForm_m06.find("#a1,#a2").prop("disabled",$(this).is(':checked'));
        }else if ('a1'==this.id || 'a2' == this.id) {
        	tabForm_m06.find("#a0").prop("disabled",tabForm_m06.find("#a1,#a2").is(':checked'));
        }
    }).end().find("input[id^=d1_].numeric:not([readonly])").change(function(){ // 生財器具備或生產設備 計算
        var endS = parseInt(this.id.substr(3));
        var num = Math.floor(parseInt(endS)/4) * 4;
        var doms = $(this).parents().eq(2);
        var quantity = doms.find("#d1_" + (num + 2)).val();
        var unitPrice = doms.find("#d1_" + (num + 3)).val();
        if(quantity&&unitPrice && !isNaN(quantity) && !isNaN(unitPrice)){
            doms.find("#d1_" + (num + 4)).val(parseInt(quantity) * parseInt(unitPrice));
            var total = 0;
            for(var i = 4;i<=20;i+=4){
            	if(''!==(doms.find("#d1_" + i).val())){
            		total += parseInt(doms.find("#d1_" + i).val());
            	}
            }
            tabForm_m06.find("#d1_subtotal").val(total);
            if(tabForm_m06.find("#d2_subtotal").val()!==''){
            	tabForm_m06.find("#total").val(total + parseInt(tabForm_m06.find("#d2_subtotal").val()));
            }else{
            	tabForm_m06.find("#total").val(total);
            }
        }

	}).end().find("input[id^=d2_].numeric:not([readonly])").change(function(){ // 週轉金 計算
        var endS = parseInt(this.id.substr(3));
        var num = Math.floor(endS / 3) * 3;
        var doms = $(this).parents().eq(2);
        var quantity = doms.find("#d2_" + (endS > 15 ? 18 : (endS > 12 ? 14 : (num + 1)))).val();
        var unitPrice = doms.find("#d2_" + (endS > 15 ? 19 : (endS > 12 ? 15 : (num + 2)))).val();
        if(quantity&&unitPrice && !isNaN(quantity) && !isNaN(unitPrice)){
            doms.find("#d2_" + (endS > 16 ? 20 : (endS > 12 ? 16 : (num + 3)))).val(parseInt(quantity) * parseInt(unitPrice));
            var total = 0;
            for(var i = 3;i<=20;i+=3){
            	if(''!==(doms.find("#d2_" + (i > 16 ? 20 : (i > 12 ? 16 : i))).val())){
            		total += parseInt(doms.find("#d2_" + (i > 16 ? 20 : (i > 12 ? 16 : i))).val());
            	}	
            }
            tabForm_m06.find("#d2_subtotal").val(total);
            if(tabForm_m06.find("#d1_subtotal").val()!==''){
            	tabForm_m06.find("#total").val(parseInt(tabForm_m06.find("#d1_subtotal").val()) + total);
            }else{
            	tabForm_m06.find("#total").val(total);
            }
            
        }
	}).end().find("input[id^=e3_].numeric:not([readonly])").change(function(){ // 營業毛利/營業淨利 計算
		var val = $(this).val();
		if(val && isNaN(val)){
			return ;
		}
		if('e3_1' == this.id){
			tabForm_m06.find("#e3_2").val($(this).val());
		}
		var e3_4Val = parseInt(tabForm_m06.find("#e3_2").val()) - parseInt(tabForm_m06.find("#e3_3").val());
		tabForm_m06.find("#e3_4,#e3_4_").val(e3_4Val);
		var e3_6Val = e3_4Val - parseInt(tabForm_m06.find("#e3_5").val())
		tabForm_m06.find("#e3_6,#e3_6_").val(e3_6Val);
		var e3_9Val = e3_6Val + parseInt(tabForm_m06.find("#e3_7").val()) - parseInt(tabForm_m06.find("#e3_8").val());
		tabForm_m06.find("#e3_9").val(e3_9Val);
	}).end().find("#dpBankNameLink").click(function(){ // 查詢分行代碼
        QueryBranch.open({
        	removeKey : [ '02', '03', '04','05','06','07','08','09','10','11','12','99' ],// 刪除不需要的選項
            fn: function(data){
            	tabForm_m06.find("#bank1").val(data.bankCodeCn);
            	var brn = data.branchName;
            	var index=brn.indexOf("銀行");
            	if(index > -1){
            		brn = brn.substring(index + 2);
            	}else {
            		index = brn.indexOf("合作社");
            		if (index > -1) {
            			brn = brn.substring(index + 3);
            		}
            	}
            	if(!brn) {
            		tabForm_m06.find("#bank1").val(data.branchName);
            	}
            	tabForm_m06.find("#bank2").val(brn);
            }
        });
	})
});
