/* 
 * ScoreServiceJPImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service.impl;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;


import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;

import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.base.service.ScoreServiceSea;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
/**
 * <pre>
 * 評分 ServiceImpl
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
@Service("ScoreServiceSea")
public class ScoreServiceSeaImpl extends AbstractEloandbJdbc implements
		ScoreServiceSea {

	
	private static final Logger logger = LoggerFactory.getLogger(ScoreServiceJPImpl.class);

	private static final int OVERSEA_SCALE = 5;

	@Resource
	EjcicService ejcicService;

	@Resource
	EtchService etchService;

	@Resource
	DwdbBASEService dwdbBASEService;

	@Resource
	MisdbBASEService misdbBaseService;
	
	@Resource
	ScoreService scoreService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	
	public void setOverSeaRatingFlag(L120M01A model) {
		//取得澳洲模型3.0啟用時間
		CodeType activeDate_3_0_AU = codeTypeService.findByCodeTypeAndCodeValue("ActiveDate_AU", OverSeaUtil.V3_0_LOAN_AU, "zh_TW");
		String V3_0_AU_ACTIVEDATE = activeDate_3_0_AU.getCodeDesc2();
		
		//取得亞洲模型2.0啟用時間
		CodeType activeDate_2_0_TH = codeTypeService.findByCodeTypeAndCodeValue("ActiveDate_TH", OverSeaUtil.V2_0_LOAN_TH, "zh_TW");
		String V2_0_TH_ACTIVEDATE = activeDate_2_0_TH.getCodeDesc2();
		
		
		// XXX 當為【海外】【消金】簽報書，才寫入 RatingFlag
		if (LMSUtil.isOverSea_CLS(model)) {
			// 個金簽報書
			if (LMSUtil.get_JP_BRNO_SET().contains(model.getCaseBrId())
					&& LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
							CapDate.parseDate(OverSeaUtil.OVERSEA_JP_CLSMODEL_ONDATE))) {
				model.setRatingFlag(OverSeaUtil.L120M01A_RatingFlag_JP);
			} 
			/*澳洲模型3.0，同時適用於[澳洲]、[法國]、[加拿大]三個國家。因RatingFlag定義為[採用模型註記]，所以三個國家都還是先使用[AU]
			 * 澳洲 >> 原本就有評等，因此啟用時簽套用之前的啟用時間2015-10-02
			 * 法國、加拿大 >> 3.0開始才有評等，3.0啟用後才填寫RatingFlag
			 * */
				else if (LMSUtil.get_AU_BRNO_SET().contains(model.getCaseBrId())
					&& LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
							CapDate.parseDate(OverSeaUtil.OVERSEA_AU_CLSMODEL_ONDATE))) {
				model.setRatingFlag(OverSeaUtil.L120M01A_RatingFlag_AU);
			} else if(LMSUtil.get_FR_BRNO_SET().contains(model.getCaseBrId())
					&& LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
							CapDate.parseDate(V3_0_AU_ACTIVEDATE))){ //V3_0_AU_ACTIVEDATE=3.0模型啟用時間
				model.setRatingFlag(OverSeaUtil.L120M01A_RatingFlag_AU);
			} else if(LMSUtil.get_CA_BRNO_SET().contains(model.getCaseBrId())
					&& LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
							CapDate.parseDate(V3_0_AU_ACTIVEDATE))){ //V3_0_AU_ACTIVEDATE=3.0模型啟用時間
				model.setRatingFlag(OverSeaUtil.L120M01A_RatingFlag_AU);
			} 
			/*亞洲模型2.0，同時適用於[泰國]、[越南]兩個國家。因RatingFlag定義為[採用模型註記]，所以三個國家都還是先使用[TH]
			 * 泰國 >> 原本就有評等，因此啟用時簽套用之前的啟用時間2015-10-02
			 * 越南 >> 2.0開始才有評等，2.0啟用後才填寫RatingFlag
			 * */
			  else if (LMSUtil.get_TH_BRNO_SET().contains(model.getCaseBrId())
					&& LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
							CapDate.parseDate(OverSeaUtil.OVERSEA_TH_CLSMODEL_ONDATE))) {
				model.setRatingFlag(OverSeaUtil.L120M01A_RatingFlag_TH);
			} else if (LMSUtil.get_VN_BRNO_SET().contains(model.getCaseBrId())
					&& LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=",
							CapDate.parseDate(V2_0_TH_ACTIVEDATE))){
				model.setRatingFlag(OverSeaUtil.L120M01A_RatingFlag_TH);
			}
		}
	}
	

}