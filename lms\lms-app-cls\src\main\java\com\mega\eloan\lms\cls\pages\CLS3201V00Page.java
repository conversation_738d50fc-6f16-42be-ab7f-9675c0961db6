
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

/**
 * <pre>
 * 歡喜信貸客群查詢
 * </pre>
 * 
 * @since 2023/11/02
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls3201v00")
public class CLS3201V00Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		//setGridViewStatus(FlowDocStatusEnum.DOC_EDITING);
		model.addAttribute("_buttonPanel", "");
		renderJsI18N(CLS3201V00Page.class);

		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS3201V00Page');");
	}

}
