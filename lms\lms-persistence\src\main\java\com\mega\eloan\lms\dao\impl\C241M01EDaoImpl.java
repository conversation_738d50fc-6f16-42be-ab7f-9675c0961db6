/* 
 * C241M01EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C241M01E;


/** 覆審報告表簽章欄檔 **/
@Repository
public class C241M01EDaoImpl extends LMSJpaDao<C241M01E, String> implements
		C241M01EDao {

	@Override
	public C241M01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C241M01E> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C241M01E> list = createQuery(C241M01E.class, search)
				.getResultList();
		return list;
	}

	@Override
	public C241M01E findByBranchTypeStaffJob(String mainId, String custId,
			String dupNo, String branchType, String staffJob) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
				branchType);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		return findUniqueOrNone(search);
	}

	@Override
	public C241M01E findByUniqueKey(String mainId, String custId, String dupNo,
			String branchType, String branchId, String staffNo, String staffJob) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
				branchType);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffNo", staffNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		return findUniqueOrNone(search);
	}
	@Override
	public List<C241M01E> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C241M01E> list = createQuery(C241M01E.class,search).getResultList();
		return list;
	}
	@Override
	public List<C241M01E> findByIndex01(String mainId, String custId,
			String dupNo, String branchType, String branchId, String staffNo,
			String staffJob) {
		ISearch search = createSearchTemplete();
		List<C241M01E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (branchType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
					branchType);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					branchId);
		if (staffNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffNo",
					staffNo);
		if (staffJob != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob",
					staffJob);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(C241M01E.class, search).getResultList();
		}
		return list;
	}
}