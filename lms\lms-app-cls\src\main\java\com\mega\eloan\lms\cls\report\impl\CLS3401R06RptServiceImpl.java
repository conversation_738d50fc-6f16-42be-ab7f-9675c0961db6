package com.mega.eloan.lms.cls.report.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.JSONUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.ContractDocConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ContractDocService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS3401M06Page;
import com.mega.eloan.lms.cls.report.CLS3401R06RptService;
import com.mega.eloan.lms.cls.service.CLS3401Service;
import com.mega.eloan.lms.model.C340M01A;
import com.mega.eloan.lms.model.C340M01C;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;


@Service("cls3401r06rptservice")
public class CLS3401R06RptServiceImpl extends AbstractReportService implements CLS3401R06RptService  {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS3401R06RptServiceImpl.class);
	
	@Resource
	LMSService lmsService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	CLSService clsService;

	@Resource
	CLS3401Service cls3401service;

	@Resource
	ContractDocService contractDocService;
	
	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		return "report/cls/CLS3401R06_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3401M06Page.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		try {
			C340M01A meta = clsService.findC340M01A_oid(mainOid);
			String cntrNo = meta.getC340m01bs().get(0).getCntrNo();
			String ownBrId = meta.getOwnBrId();
			C340M01C c340m01c = meta.getC340m01cs().get(0);
			String content = "";
			if (c340m01c == null || CapString.isEmpty(c340m01c.getJsonData())) {
				content = "{}";
			} else {
				content = c340m01c.getJsonData();
			}
			JSONObject jsContent = JSONObject.fromObject(content);
			if(true){
				List<String> target_acctNo_list = new ArrayList<String>();
				List<Object> acctNo_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsContent, ContractDocConstants.CtrTypeL.PLOAN_ACCTNO_FLAG_LIST);
				if(acctNo_list.size()==1 && Util.equals("[]", StringUtils.join(acctNo_list, ","))){
					//放款帳號, 若只有[]
				}else{
					for(Object acctNo : acctNo_list){
						target_acctNo_list.add(Util.trim(acctNo));
					}	
				}
				
				jsContent.put(ContractDocConstants.CtrTypeA.PLOAN_ACCTNO_LIST, StringUtils.join(target_acctNo_list, ","));	
				jsContent.remove("loanAcct_and_Flag");
			}
			
			rptVariableMap.putAll(JSONUtil.parseJsonToStringMap(jsContent));
			if(true){
				List<String> loanPurpose_list = new ArrayList<String>();
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_C))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.C"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_G))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.G"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_H))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.H"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_J))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.J"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_K))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.K"));
				}
				if(Util.equals("Y", jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_F))){
					loanPurpose_list.add(prop.getProperty("loanPurpose.F"));
				}
				String loanPurpose_otherDesc = Util.trim(jsContent.optString(ContractDocConstants.CtrTypeA.PLOAN_LOANPURPOSE_OTHERDESC));
				if(Util.isEmpty(loanPurpose_otherDesc)){
					//	
				}else{
					loanPurpose_list.add(prop.getProperty("loanPurpose.otherDesc")+"："+loanPurpose_otherDesc);
				}
				rptVariableMap.put("loanPurpose", StringUtils.join(loanPurpose_list, "、"));
			}
			
			if(true){
				List<Object> otherInfoDesc_list = LMSUtil.get_notEmpty_One_or_Multiple_Data(jsContent, ContractDocConstants.CtrTypeA.PLOAN_OTHERINFODESC);
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_OTHERINFODESC, StringUtils.join(otherInfoDesc_list, "\r\n"));
			}
			
			if(true){
				String courtName = rptVariableMap.get(ContractDocConstants.CtrTypeA.PLOAN_COURT_NAME);
				if(Util.isEmpty(courtName)){
					courtName = "　　"; //2個全型空白
				}
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_COURT_NAME
						, prop.getProperty("label.countName.prefix")+" "+courtName+" "+prop.getProperty("label.countName.postfix"));	
			}			
			
			if(true){
				rptVariableMap.put(ContractDocConstants.CtrTypeA.PLOAN_LENDING_PLAN_OPTION
						, LMSUtil.getDesc(contractDocService.get_ploan_lendingPlanInfo_showOption(), rptVariableMap.get(ContractDocConstants.CtrTypeA.PLOAN_LENDING_PLAN_OPTION)));
			}
			rptVariableMap.put("rptId_desc", rptId_desc(meta));
			rptVariableMap.put("rateDesc", StringUtils.join(contractDocService.geCtrTypeA_rateDesc(jsContent), "\n"));
			rptVariableMap.put("cntrNo", cntrNo);
			rptVariableMap.put("caseNo", meta.getCaseNo());
			rptVariableMap.put("custName", meta.getCustId() + " " + meta.getCustName());
			rptVariableMap.put("branchName", StrUtils.concat(ownBrId, " ", branchService.getBranchName(ownBrId)));
			rptVariableMap.put("ploanCtrNo", Util.trim(meta.getPloanCtrNo()));

			rptGenerator.setVariableData(rptVariableMap);
		} catch(Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
		}
		
	}

	private String rptId_desc(C340M01A meta) {
		String ctrType = Util.trim(meta.getCtrType());
		String rptId = Util.trim(meta.getRptId());
		if(Util.equals(ctrType, ContractDocConstants.C340M01A_CtrType.Type_L)){
			if(Util.equals(ContractDocConstants.C340M01A_RptId.CtrTypeL_V202106, rptId)){
				return "2021.06版本";
			}
		}
		return rptId;
	}

}
