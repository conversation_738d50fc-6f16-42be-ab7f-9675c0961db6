package tw.com.jcs.flow.provider.impl;

import java.util.UUID;

import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.provider.IdProvider;

/**
 * <pre>
 * UUIDIdProvider
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public class UUIDIdProvider implements IdProvider {

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.provider.IdProvider#getNextId()
     */
    @Override
    public Object getNextId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.provider.IdProvider#getNextId(java.lang.Object[])
     */
    @Override
    public Object getNextId(Object[] parameter) {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.flow.provider.IdProvider#getNextId(tw.com.jcs.flow.FlowInstance)
     */
    @Override
    public Object getNextId(FlowInstance parent) {
        return UUID.randomUUID().toString().replace("-", "");
    }

}
