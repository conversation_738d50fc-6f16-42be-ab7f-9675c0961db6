var Action = {
    ghandler: "cls1161gridhandler",
    fhandler: "cls1161m04formhandler"
};
$(document).ready(function(){
    
    var L250M01AGrid = $("#gridview").iGrid({
        handler: Action.ghandler,
        height: 350,
        sortname: 'createTime',
        sortorder: 'desc',
        postData: {
            formAction: "queryL250M01A",
            docStatus: viewstatus//viewstatus,
        },
        rowNum: 15,
        // multiselect: true, //選項前多checkbox
        colModel: [{
            colHeader: i18n.cls1161v08['custId'],//統一編號
            align: "left",
            width: 100,
            sortable: true,
            name: 'custId',
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.cls1161v08['custName'],//客戶名稱
            align: "left",
            width: 100,
            sortable: true,
            name: 'custName'
        }, {
            colHeader: i18n.cls1161v08['caseNo'], //案件號碼
            align: "left",
            width: 150,
            sortable: true,
            name: 'caseNo'
        }, {
            colHeader: i18n.cls1161v08['appraiser'], //經辦姓名
            align: "left",
            width: 100,
            sortable: true,
            name: 'apprId'
        }, {
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L250M01AGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $.thickbox.close();
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',//'../lms/lms2501m01/01',
            formHandler: Action.fhandler,
            data: {
                formAction: "queryL250m01a",
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    };
    
    $("#buttonPanel").find("#btnAdd").click(function(){
    
        $.form.submit({
            url: "../cls/cls1161m04/01",
            data: {
                txCode: txCode
            },
            target: '_blank'
        });
        
        
    }).end().find("#btnModify,#btnView").click(function(){
        var row = $("#gridview").getGridParam('selrow');
        var list = "";
        var seldata = $("#gridview").getRowData(row);
        list = seldata.oid;
        if (!list) {
            // TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        openDoc(null, null, seldata);
    }).end().find("#btnDelete").click(function(){
        var selrow = L250M01AGrid.getGridParam('selrow');
        if (selrow) {
            var ret = L250M01AGrid.getRowData(selrow);
            API.flowConfirmAction({
                message: i18n.def.action_003,
                handler: Action.fhandler,
                action: "deleteMeta",
                data: {
                    mainOid: ret.oid,
                    mainDocStatus: ret.docStatus
                },
                success: function(rsJs){
                    if (rsJs.deleteMessage) {
                        API.showErrorMessage(rsJs.deleteMessage);
                    }
                    else {
                        API.showPopMessage(i18n.def['confirmDeleteSuccess']);
                        L250M01AGrid.trigger("reloadGrid");
                    }
                }
            });
        }
        else {
            API.showErrorMessage(i18n.def["grid.selrow"]);
        }
    });
    
    
    
});
