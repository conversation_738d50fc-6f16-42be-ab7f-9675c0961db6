/* 
 * L120S04EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S04EDao;
import com.mega.eloan.lms.model.L120S04E;

/** 往來實績彙總表利潤貢獻度業務別占比檔 **/
@Repository
public class L120S04EDaoImpl extends LMSJpaDao<L120S04E, String>
	implements L120S04EDao {

	@Override
	public L120S04E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S04E> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S04E> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S04E> findByIndex01(String mainId, String keyCustId, String keyDupNo, String docKind){
		ISearch search = createSearchTemplete();
		List<L120S04E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (keyCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
		if (keyDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		if (docKind != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docKind", docKind);
		search.addOrderBy("docDate", true);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S04E> findByMainIdDocKind(String mainId, String[] docKind) {
		ISearch search = createSearchTemplete();
		List<L120S04E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (docKind != null)
			search.addSearchModeParameters(SearchMode.IN, "docKind", docKind);
		search.addOrderBy("keyCustId", true);
		search.addOrderBy("keyDupNo", true);
		search.addOrderBy("docDate", true);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S04E> findByMainIdKeyCustIdDupNo(String mainId, String keyCustId, String keyDupNo){
		ISearch search = createSearchTemplete();
		List<L120S04E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (keyCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
		if (keyDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		search.addOrderBy("docDate", true);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S04E> findByMainIdKeyCustIdDupNoDocKind(
			String mainId, String keyCustId, String keyDupNo, String [] docKind) {
		ISearch search = createSearchTemplete();
		List<L120S04E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (keyCustId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
		if (keyDupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		if (docKind != null)
			search.addSearchModeParameters(SearchMode.IN, "docKind", docKind);
		search.addOrderBy("docKind", true);
		search.addOrderBy("docDate", true);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}