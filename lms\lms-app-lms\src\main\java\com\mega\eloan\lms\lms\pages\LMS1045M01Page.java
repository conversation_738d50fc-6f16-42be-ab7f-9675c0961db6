
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.C123M01A;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 加拿大消金信用評等模型
 * </pre>
 * 
 * @since 2017
 * <AUTHOR>
 * @version <ul>
 *          <li>2017,EL09301,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1045m01/{page}")
public class LMS1045M01Page extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待覆核));
		
		renderJsI18N(LMS1045M01Page.class);
	}

	public Class<? extends Meta> getDomainClass() {
		return C123M01A.class;
	}

}
