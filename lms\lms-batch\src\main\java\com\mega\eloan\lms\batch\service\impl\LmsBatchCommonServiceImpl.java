package com.mega.eloan.lms.batch.service.impl;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipOutputStream;
import org.apache.wicket.Session;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.mega.eloan.common.aml.AmlStrategy;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.gwclient.SSOWebServiceClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.model.SmsContent;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ElDeleteFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.MEGAImageService;
import com.mega.eloan.common.service.SmsService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.NetUtils;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.common.RO412;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.ZipUtils;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.batch.service.LmsBatchCommonService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.report.CLS1220R14RptService;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C122M01GDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L120S14ADao;
import com.mega.eloan.lms.dao.L120S14BDao;
import com.mega.eloan.lms.dao.L120S14CDao;
import com.mega.eloan.lms.dao.L120S14DDao;
import com.mega.eloan.lms.dao.L120S14EDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01CDao;
import com.mega.eloan.lms.dao.L140M01EDao;
import com.mega.eloan.lms.dao.L140M01QDao;
import com.mega.eloan.lms.dao.L140MM5ADao;
import com.mega.eloan.lms.dao.L140MM5CDao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L160M01BDao;
import com.mega.eloan.lms.dao.L180R42ADao;
import com.mega.eloan.lms.dao.L180R46ADao;
import com.mega.eloan.lms.dao.L180R54ADao;
import com.mega.eloan.lms.dao.L186M01ADao;
import com.mega.eloan.lms.dao.L260M01DDao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS8000M01Page;
import com.mega.eloan.lms.fms.report.impl.LMS8000R01RptServiceImpl;
import com.mega.eloan.lms.fms.service.LMS8000Service;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.bean.ELF691;
import com.mega.eloan.lms.mfaloan.bean.LNF07A;
import com.mega.eloan.lms.mfaloan.service.LNLNF07AService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;
import com.mega.eloan.lms.mfaloan.service.MisELF447nService;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF691Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElcsecntService;
import com.mega.eloan.lms.mfaloan.service.MisEllnseekservice;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF02OService;
import com.mega.eloan.lms.mfaloan.service.MisLnunIdService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisQuotapprService;
import com.mega.eloan.lms.mfaloan.service.MisQuotsubService;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01G;
import com.mega.eloan.lms.model.C900M01M;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S14A;
import com.mega.eloan.lms.model.L120S14B;
import com.mega.eloan.lms.model.L120S14C;
import com.mega.eloan.lms.model.L120S14D;
import com.mega.eloan.lms.model.L120S14E;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140MM5A;
import com.mega.eloan.lms.model.L140MM5C;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L180R42A;
import com.mega.eloan.lms.model.L180R46A;
import com.mega.eloan.lms.model.L180R54A;
import com.mega.eloan.lms.model.L186M01A;
import com.mega.eloan.lms.model.L260M01D;
import com.mega.eloan.lms.obsdb.service.ObsdbBASEService;
import com.mega.eloan.lms.obsdb.service.ObsdbELF461Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF506Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF601Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import net.lingala.zip4j.exception.ZipException;
import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Service
public class LmsBatchCommonServiceImpl implements LmsBatchCommonService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    CodeTypeService codetypeService;
    @Resource
    LMSService lmsService;

    @Resource
    MisCustdataService misCustdataService;

    @Resource
    MisdbBASEService misdbBASEService;

    @Resource
    BranchService branchService;

    @Resource
    L140M01QDao l140m01qdao;

    @Resource
    L140M01ADao l140m01aDao;

    @Resource
    L140M01EDao l140m01eDao;

    @Resource
    MisELF506Service misELF506Service;

    @Resource
    ObsdbELF506Service obsdbELF506Service;

    @Resource
    ObsdbELF601Service obsdbELF601Service;

    @Resource
    EloandbBASEService eloandbService;

    @Resource
    MisRatetblService rateService;

    @Resource
    L120M01ADao l120m01aDao;

    @Resource
    L160M01ADao l160m01aDao;

    @Resource
    L160M01BDao l160m01bDao;

    @Resource
    MisQuotsubService misQuotsubService;

    @Resource
    MisELF447nService misELF447nService;

    @Resource
    RetrialService retrialService;

    @Resource
    MisELF412Service misELF412Service;

    @Resource
    MisELF412BService misELF412BService;

    @Resource
    DwdbBASEService dwdbBASEService;

    @Resource
    ObsdbBASEService obsDBService;

    @Resource
    LNLNF07AService lnLnf07aService;

    @Resource
    L180R42ADao l180r42aDao;

    @Resource
    MisMISLN20Service misLNF020Service;

    @Resource
    ElDeleteFileService elDFService;

    @Resource
    L140MM5ADao l140mm5aDao;

    @Resource
    L140MM5CDao l140mm5cDao;

    @Resource
    MisLNF022Service misLNF022Service;

    @Resource
    MisLNF02OService misLNF02OService;

    @Resource
    EmailClient emailClient;

    @Resource
    private SysParameterService sysParameterService;

    @Resource
    L180R46ADao l180r46aDao;

    @Resource
    UserInfoService userInfoService;

    @Resource
    L120S01BDao l120s01bDao;

    @Resource
    MisElcsecntService misElcsecntService;
    @Resource
    C120S01ADao c120s01aDao;
    @Resource
    L120S01ADao l120s01aDao;

    @Resource
    MisLnunIdService misLnunIdService;

    @Resource
    SmsService smsService;

    @Resource
    L180R54ADao l180r54aDao;

    @Resource
    L186M01ADao l186m01aDao;

    @Resource
    L140M01CDao l140m01cDao;

    @Resource
    MisEllnseekservice misEllnseekservice;
    @Resource
    ObsdbELF461Service obsdbELF461Service;

    @Resource
    DocFileService docFileService;

    @Resource
    ICustomerService customerService;

    @Resource
    L260M01DDao l260m01dDao;

    @Resource
    MisELLNGTEEService misELLNGTEEService;

    @Resource
    MisQuotapprService misQuotapprService;

    @Resource
    L120S14ADao l120s14aDao;

    @Resource
    L120S14BDao l120s14bDao;

    @Resource
    L120S14CDao l120s14cDao;

    @Resource
    L120S14DDao l120s14dDao;

    @Resource
    L120S14EDao l120s14eDao;

    @Resource
    AMLRelateService amlRelateService;

    @Resource
    C120M01ADao c120m01aDao;

    @Resource
    C120S01EDao c120s01eDao;

    @Resource
    EjcicService ejcicService;

    @Resource
    C122M01ADao c122m01aDao;

    @Resource
    C122M01GDao c122m01gDao;

    @Resource
    MisELF691Service misELF691Service;

    @Resource
    MEGAImageService mEGAImageService;

    @Resource
    CLSService clsService;

    @Resource
    CLS1220R14RptService cls1220R14RptService;

    @Resource
    LMS8000Service lms8000Service;

    @Resource
    LMS8000R01RptServiceImpl lms8000R01RptService;
    @Resource
    DocFileDao docFileDao;
    @Resource
    SSOWebServiceClient ssoWebServiceClient;

    /**
     * J-104-0XXX-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 新舊授信對象別資料轉換
     */
    @Override
    public String doLmsBatch0002() {
        StringBuffer errMsg = new StringBuffer("");

        // 1.取得ELOAN要轉檔資料(額度序號、CUSTID、性質)存到
        LinkedHashMap<String, String> custIdWithCharCdMap = new LinkedHashMap<String, String>();

        List rows = eloandbService.find140AFrom140QLoanTargetIsNull();

        Iterator it = rows.iterator();
        while (it.hasNext()) {
            Map dataMap = (Map) it.next();
            String mainId = Util.trim((String) dataMap.get("MAINID"));
            String custId = Util.trim((String) dataMap.get("CUSTID"));
            String dupNo = Util.trim((String) dataMap.get("DUPNO"));
            String cntrNo = Util.trim((String) dataMap.get("CNTRNO"));

            String cnLoanFg = Util.trim((String) dataMap.get("CNLOANFG")); // 是否屬「本行對大陸地區授信業務管理要點」定義之放款
            String directFg = Util.trim((String) dataMap.get("DIRECTFG")); // 舊授信對象
            String iGolFlag = Util.trim((String) dataMap.get("IGOLFLAG")); // 內保外貸
            String cnBusKind = Util.trim((String) dataMap.get("CNBUSKIND")); // 企業類別

            String bcnLoanFg = Util.trim((String) dataMap.get("BCNLOANFG")); // 是否屬「本行對大陸地區授信業務管理要點」定義之放款(前次)
            String bdirectFg = Util.trim((String) dataMap.get("BDIRECTFG")); // 舊授信對象(前次)
            String biGolFlag = Util.trim((String) dataMap.get("BIGOLFLAG")); // 內保外貸(前次)
            String bcnBusKind = Util.trim((String) dataMap.get("BCNBUSKIND")); // 企業類別(前次)

            String fullCustId = custId + "-" + dupNo;

            String charCd = "1Z"; // 授管說預設1Z
            String loanTarget = "";
            String bloanTarget = "";

            if (Util.notEquals(custId, "")) {
                if (!custIdWithCharCdMap.containsKey(fullCustId)) {
                    Map<String, Object> custData = (Map<String, Object>) misCustdataService
                            .findCharCdByIdDupNo(custId, dupNo);
                    if (custData != null && !custData.isEmpty()) {
                        charCd = Util.trim((String) custData.get("CHARCD"));
                    }
                    custIdWithCharCdMap.put(fullCustId, charCd);
                } else {
                    charCd = Util.trim(custIdWithCharCdMap.get(fullCustId));
                }
            }

            // 本案
            if (Util.equals(cnLoanFg, "Y")) {
                loanTarget = lmsService.directFgToLoanTarget(directFg,
                        iGolFlag, cnBusKind, charCd);
            }
            // 前案
            if (Util.equals(bcnLoanFg, "Y")) {
                bloanTarget = lmsService.directFgToLoanTarget(bdirectFg,
                        biGolFlag, bcnBusKind, charCd);
            }

            eloandbService.updateL140M01QLoanTargetByMainId(mainId, loanTarget,
                    bloanTarget, charCd);

        }

        return errMsg.toString();

    }

    /**
     * J-104-0XXX-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 新舊授信對象別資料轉換
     */
    @Override
    @NonTransactional
    public String doLmsBatch0003() {
        StringBuffer errMsg = new StringBuffer("");
        // 上傳MIS DB 與 海外AS400

        // 1.取得ELOAN要轉檔資料(額度序號、CUSTID、性質)存到
        LinkedHashMap<String, String> cntrNoWithCustIdMap = new LinkedHashMap<String, String>();
        LinkedHashMap<String, String> custIdWithCharCdMap = new LinkedHashMap<String, String>();

        List rows = eloandbService.find140QByAllCnLoanFgIsY();

        Iterator it = rows.iterator();
        while (it.hasNext()) {
            Map dataMap = (Map) it.next();
            String custId = Util.trim((String) dataMap.get("CUSTID"));
            String dupNo = Util.trim((String) dataMap.get("DUPNO"));
            String cntrNo = Util.trim((String) dataMap.get("CNTRNO"));

            String fullCustId = custId + "-" + dupNo;

            if (Util.notEquals(custId, "") && Util.notEquals(cntrNo, "")) {
                if (!cntrNoWithCustIdMap.containsKey(cntrNo)) {
                    cntrNoWithCustIdMap.put(cntrNo, fullCustId);
                }

                if (!custIdWithCharCdMap.containsKey(fullCustId)) {
                    String charCd = "";
                    Map<String, Object> custData = (Map<String, Object>) misCustdataService
                            .findCharCdByIdDupNo(custId, dupNo);
                    if (custData != null && !custData.isEmpty()) {
                        charCd = Util.trim((String) custData.get("CHARCD"));
                    }
                    custIdWithCharCdMap.put(fullCustId, charCd);
                }
            }

        }

        // 開始更新MIS與AS400資料
        errMsg.append(updateDB2FromEloan(cntrNoWithCustIdMap,
                custIdWithCharCdMap));

        return errMsg.toString();
    }

    @NonTransactional
    String updateDB2FromEloan(Map<String, String> cntrNoWithCustIdMap,
                              Map<String, String> custIdWithCharCdMap) {

        // 上傳海外
        if (cntrNoWithCustIdMap == null || cntrNoWithCustIdMap.isEmpty()) {
            return "無e-Loan額度與借款人ID對應資料";
        }
        if (custIdWithCharCdMap == null || custIdWithCharCdMap.isEmpty()) {
            return "無e-Loanr借款人ID與性質別對應資料";
        }

        String directFg = ""; // 舊授信對象
        String iGolFlag = ""; // 內保外貸
        String cnBusKind = ""; // 企業類別

        // 因為國內與海外簽報書都會寫MIS.ELF506，所以以MIS.ELF506當頭
        List<Map<String, Object>> rows = misELF506Service
                .findLoanTargetISEmpty();

        for (Map<String, Object> dataMap : rows) {
            String cntrNo = Util.trim(((String) dataMap.get("ELF506_CNTRNO")));
            if (Util.equals(cntrNo, "")) {
                continue;
            }

            String custId = "";
            String dupNo = "";
            String charCd = "1Z";
            String fullCustId = "";

            if (cntrNoWithCustIdMap.containsKey(cntrNo)) {
                fullCustId = Util.trim(cntrNoWithCustIdMap.get(cntrNo));
            }

            // e-Loan沒有該額度統編資料，就不轉了
            if (Util.equals(fullCustId, "")) {
                continue;
            }

            custId = fullCustId.split("-")[0];
            dupNo = fullCustId.split("-")[1];

            if (custIdWithCharCdMap.containsKey(fullCustId)) {
                // 性質別沒有還是照樣轉
                charCd = Util.trim(custIdWithCharCdMap.get(fullCustId));
            }

            directFg = Util.trim(((String) dataMap.get("ELF506_DIRECT_FG"))); // 舊授信對象
            iGolFlag = Util.trim(((String) dataMap.get("ELF506_IGOL_FLAG"))); // 內保外貸
            cnBusKind = Util.trim(((String) dataMap.get("ELF506_CN_BUS_KIND"))); // 企業類別

            String loanTarget = lmsService.directFgToLoanTarget(directFg,
                    iGolFlag, cnBusKind, charCd);

            if (Util.notEquals(loanTarget, "")) {
                // 更新MIS
                Map<String, Object> elf506 = misELF506Service
                        .getByCntrNo(cntrNo);

                // 有資料才UPDATE
                if (elf506 != null && !elf506.isEmpty()) {
                    misELF506Service.updateLoanTargetByCntrNo(loanTarget,
                            cntrNo);
                }

                // 更新AS400
                String brnId = cntrNo.substring(0, 3);
                IBranch tBranch = branchService.getBranch(brnId);
                if (tBranch != null) {
                    if (UtilConstants.BrNoType.國外.equals(tBranch.getBrNoFlag())) {

                        Map<String, Object> elf506As400 = obsdbELF506Service
                                .getByCntrNo(brnId, cntrNo);

                        // 有資料才UPDATE
                        if (elf506As400 != null && !elf506As400.isEmpty()) {
                            obsdbELF506Service.updateLoanTargetByCntrNo(brnId,
                                    loanTarget, cntrNo);
                        }

                    }
                }

            }

        }

        return "";

    }

    /**
     * J-104-0XXX-001 Web e-Loan授信管理系統Y01轉Y04 ELF506 大陸地區授信業務控管註記新增新授信對象別
     * 新舊授信對象別資料轉換
     */
    @Override
    @NonTransactional
    public String doLmsBatch0004(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        // 上傳MIS DB 與 海外AS400

        String type = request.getString("type");
        String exDate = request.getString("exDate");
        String fBranch = request.getString("fBranch");
        String tBranch = request.getString("tBranch");
        String version = request.getString("version");

        // 1.取得ELOAN要轉檔資料
        // 因為ELF506 沒有ID 欄位，所以只能先從ELOAN找到該ID在簽報書中的所有額度序號，再用該額度序號更新ELF506
        LinkedHashMap<String, String> cntrNoWithCustIdMap = new LinkedHashMap<String, String>();

        List rows = eloandbService.findLms140QByComBrToBr01_Elf506Y01ToY04(
                exDate, fBranch, tBranch);

        Iterator it = rows.iterator();
        while (it.hasNext()) {
            Map dataMap = (Map) it.next();
            String custId = Util.trim((String) dataMap.get("CUSTID"));
            String dupNo = Util.trim((String) dataMap.get("DUPNO"));
            String cntrNo = Util.trim((String) dataMap.get("CNTRNO"));

            if (Util.equals(custId, "") || Util.equals(dupNo, "")
                    || Util.equals(cntrNo, "")) {
                continue;
            }

            String fullCustId = custId + "-" + dupNo;

            if (Util.notEquals(custId, "") && Util.notEquals(cntrNo, "")) {
                if (!cntrNoWithCustIdMap.containsKey(cntrNo)) {
                    cntrNoWithCustIdMap.put(cntrNo, fullCustId);
                }
            }

        }

        // 因為異常通報有ID，所以可以直接更新

        // 調整為先上傳更新ID到LN.LNF07A，再用SQL整批更新
        // 1.更新MIS LNFE0854、0855、0856
        // String updateCustId = "THZ0074868";
        // String updateDupNo = "0";
        // try {
        // misdbBASEService.updateELF085XMdbrnoByCustId(tBranch, updateCustId,
        // updateDupNo, fBranch);
        // } catch (Exception e) {
        // System.out.println(e.toString());
        // // 若無資料不處理
        // }

        // 2.更新ELF506/ELF515 (MIS與AS400資料)
        errMsg.append(updateDB2FromEloan04(exDate, fBranch, tBranch, version,
                cntrNoWithCustIdMap));

        return errMsg.toString();
    }

    @NonTransactional
    String updateDB2FromEloan04(String exDate, String fBranch, String tBranch,
                                String version, Map<String, String> cntrNoWithCustIdMap) {

        // 上傳海外
        if (cntrNoWithCustIdMap == null || cntrNoWithCustIdMap.isEmpty()) {
            return "無e-Loan額度與借款人ID對應資料";
        }

        for (String cntrNo : cntrNoWithCustIdMap.keySet()) {
            if (Util.equals(cntrNo, "")) {
                continue;
            }
            String fullCustId = "";
            if (cntrNoWithCustIdMap.containsKey(cntrNo)) {
                fullCustId = Util.trim(cntrNoWithCustIdMap.get(cntrNo));
            }
            if (Util.equals(fullCustId, "")) {
                continue;
            }

            String custId = fullCustId.split("-")[0];
            String dupNo = fullCustId.split("-")[1];

            String y04CntrNo = tBranch + cntrNo.substring(3, 12);
            String modifyUnit = "";
            modifyUnit = version;
            IBranch iBranch = branchService.getBranch(fBranch);

            // ELF506**********************************************************************************

            // 檢查MIS ELF506 有沒有 Y04 開頭的額度序號
            Map<String, Object> elf506 = misELF506Service
                    .getByCntrNo(y04CntrNo);

            // 已經有新額度序號的資料就不UPDATE
            if (elf506 != null && !elf506.isEmpty()) {
                continue;
            }

            elf506 = null;
            elf506 = misELF506Service.getByCntrNo(cntrNo);
            // 無原額度資料也不UPDATE
            if (elf506 == null || elf506.isEmpty()) {
                continue;
            }

            // String ELF506_MODIFYUNIT = Util.trim(((String) elf506
            // .get("ELF506_MODIFYUNIT")));
            // if (Util.equals(ELF506_MODIFYUNIT, "")) {
            // modifyUnit = ELF506_MODIFYUNIT.substring(0, 5) + "-" + version;
            // } else {
            // modifyUnit = "ELOAN-" + version;
            // }

            misELF506Service
                    .updateCntrNoByCntrNo(y04CntrNo, cntrNo, modifyUnit);

            // 更新AS400
            if (iBranch != null) {
                if (UtilConstants.BrNoType.國外.equals(iBranch.getBrNoFlag())) {

                    Map<String, Object> elf506As400 = obsdbELF506Service
                            .getByCntrNo(fBranch, y04CntrNo);

                    // 已經有新額度序號的資料就不UPDATE
                    if (elf506As400 != null && !elf506As400.isEmpty()) {
                        continue;
                    }

                    elf506As400 = null;
                    elf506As400 = obsdbELF506Service.getByCntrNo(fBranch,
                            cntrNo);
                    // 無原額度資料也不UPDATE
                    if (elf506As400 == null || elf506As400.isEmpty()) {
                        continue;
                    }

                    // 有資料才UPDATE
                    obsdbELF506Service.updateCntrNoByCntrNo(fBranch, y04CntrNo,
                            cntrNo, modifyUnit);

                }
            }

            // ELF515**********************************************************************************

            // 檢查MIS ELF515 有沒有 Y04 開頭的額度序號
            Map<String, Object> elf515 = misdbBASEService
                    .findElf515ByCntrNoForBt(y04CntrNo);

            // 已經有新額度序號的資料就不UPDATE
            if (elf515 != null && !elf515.isEmpty()) {
                continue;
            }

            elf515 = null;
            elf515 = misdbBASEService.findElf515ByCntrNoForBt(cntrNo);
            // 無原額度資料也不UPDATE
            if (elf515 == null || elf515.isEmpty()) {
                continue;
            }

            // String ELF506_MODIFYUNIT = Util.trim(((String) elf506
            // .get("ELF506_MODIFYUNIT")));
            // if (Util.equals(ELF506_MODIFYUNIT, "")) {
            // modifyUnit = ELF506_MODIFYUNIT.substring(0, 5) + "-" + version;
            // } else {
            // modifyUnit = "ELOAN-" + version;
            // }

            misdbBASEService.updateElf515CntrNoByCntrNoForBt(y04CntrNo, cntrNo,
                    modifyUnit);

            // 更新AS400
            if (iBranch != null) {
                if (UtilConstants.BrNoType.國外.equals(iBranch.getBrNoFlag())) {

                    Map<String, Object> elf515As400 = obsDBService
                            .findElf515ByCntrNoForBt(fBranch, y04CntrNo);

                    // 已經有新額度序號的資料就不UPDATE
                    if (elf515As400 != null && !elf515As400.isEmpty()) {
                        continue;
                    }

                    elf515As400 = null;
                    elf515As400 = obsDBService.findElf515ByCntrNoForBt(fBranch,
                            cntrNo);
                    // 無原額度資料也不UPDATE
                    if (elf515As400 == null || elf515As400.isEmpty()) {
                        continue;
                    }

                    // 有資料才UPDATE
                    obsDBService.updateElf515ByCntrNoByCntrNoForBt(fBranch,
                            y04CntrNo, cntrNo, modifyUnit);

                }
            }

            // 更新完後，AS400 SP搬擋案

        }

        return "";

    }

    /**
     * J-105-0144-001 Web e-Loan海外授信管理系統，修改Enbridge
     * Inc.簽報書之借款人統編.，由原「USZ01803120」改為「CAZ00584030」。 簽報書重新上傳MIS
     */
    @Override
    @NonTransactional
    public String doLmsBatch0005(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        String mainId = Util.trim(request.getString("mainId"));
        if (Util.equals(mainId, "")) {
            errMsg.append("錯誤!!簽報書MAINID不得為空白");
            return errMsg.toString();
        }

        L120M01A meta = l120m01aDao.findByMainId(mainId);

        if (meta == null) {
            errMsg.append("錯誤!!找不到簽報書：" + mainId);
            return errMsg.toString();
        }

        if (LMSUtil.isClsCase(meta)) {
            errMsg.append("錯誤!!簽報書不得為 國內個金案件：" + mainId);
            return errMsg.toString();
        }

        String itemType = lmsService.checkL140M01AItemType(meta);
        String docstatus = meta.getDocStatus();

        try {
            lmsService.uploadELLNSEEK(meta);

            // 新增 核准額度資料檔 MIS.ELF447n
            lmsService.gfnInsertELF447N(meta, itemType, docstatus,
                    meta.getCaseBrId());
            lmsService.upLoadMIS(meta);
            lmsService.upLnunid(meta);
        } catch (Exception e) {
            errMsg.append(e.toString());
        }

        return errMsg.toString();
    }

    /**
     * 匯入MIS每月月底匯率資料 J-105-0185-001
     * 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
     */
    @Override
    @NonTransactional
    public String doLmsBatch0006(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        String queryDataYMD = Util.trim(request.getString("DATAYMD"));
        if (Util.equals(queryDataYMD, "")) {
            // 如果沒有帶參數，預設抓上月底最後一天的
            try {
                final String DATE_FORMAT_STR = "yyyy-MM-dd";
                String dataStartDate = CapDate.formatDate(
                        LMSUtil.getExMonthFirstDay(-1), DATE_FORMAT_STR);
                String dataEndDate = CapDate.formatDate(
                        LMSUtil.getExMonthLastDay(-1), DATE_FORMAT_STR);

                Map<String, Object> dataYMDMap = rateService
                        .findLastDateBetweenRATEYMDRange(dataStartDate,
                                dataEndDate);

                queryDataYMD = Util.trim(MapUtils.getString(dataYMDMap,
                        "MAXDATAYMD", ""));
                if (Util.equals(queryDataYMD, "")) {
                    errMsg.append("無法取得MIS RATETBL " + dataStartDate + "~"
                            + dataEndDate + " 最大利率日期");
                    System.out.println(errMsg.toString());
                    return errMsg.toString();
                }

            } catch (Exception e) {
                System.out.println(e.toString());
                // 若無資料不處理
            }

        }

        List<Map<String, Object>> rate = rateService
                .findByDateForList(queryDataYMD);
        if (rate == null || rate.isEmpty()) {
            errMsg.append("無法取得MIS RATETBL DATAYMD=「" + queryDataYMD
                    + "」之 利率資料");
            System.out.println(errMsg.toString());
            return errMsg.toString();
        }

        // 清除ELOAN DB 重覆日期之利率資料
        try {
            eloandbService.RATETBL_deleteByDateYmd(queryDataYMD);
        } catch (Exception e) {
            errMsg.append("清除LMS.RATETBL 日期" + queryDataYMD + "利率資料失敗：");
            errMsg.append(e.toString());
            System.out.println(errMsg.toString());
            return errMsg.toString();
        }

        // 匯入MIS利率資料到ELOAN DB LMS.RATETBL
        String CURR = "";
        String DATAYMD = "";
        BigDecimal ENDRATE = BigDecimal.ZERO;
        String RATEYMD = "";

        for (Map<String, Object> rateKey : rate) {
            CURR = MapUtils.getString(rateKey, "CURR", "");
            DATAYMD = MapUtils.getString(rateKey, "DATAYMD", "");
            ENDRATE = Util.parseBigDecimal(MapUtils.getString(rateKey,
                    "ENDRATE", "0"));
            RATEYMD = MapUtils.getString(rateKey, "RATEYMD", "");
            eloandbService.RATETBL_insert(CURR, DATAYMD, ENDRATE, RATEYMD);
        }

        // 最後一筆塞TWD
        if (Util.notEquals(DATAYMD, "")) {
            CURR = "TWD";
            ENDRATE = BigDecimal.ONE;
            eloandbService.RATETBL_insert(CURR, DATAYMD, ENDRATE, RATEYMD);
        }
        return errMsg.toString();
    }

    /**
     * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan SLMS-00041
     * e-Loan交換票據抵用科目調整補上傳a-Loan
     */
    @Override
    @NonTransactional
    public String doLmsBatch0007(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        // 存放已經處理過的額度與統編資訊
        LinkedHashMap<String, String> cntrNoWithCustIdMap = new LinkedHashMap<String, String>();

        // 1.取得ELOAN要轉檔資料
        List rows = eloandbService.findZ15CntrDoc();

        Iterator it = rows.iterator();
        while (it.hasNext()) {
            Map dataMap = (Map) it.next();
            String custId = Util.trim((String) dataMap.get("CUSTID"));
            String dupNo = Util.trim((String) dataMap.get("DUPNO"));
            String cntrNo = Util.trim((String) dataMap.get("CNTRNO"));

            String caseYear = Util.trim(MapUtils.getString(dataMap, "CASEYEAR",
                    "0"));
            String caseBrId = Util.trim(MapUtils.getString(dataMap, "CASEBRID",
                    ""));
            String caseSeq = Util.trim(MapUtils.getString(dataMap, "CASESEQ",
                    "0"));
            String docType = Util.trim(MapUtils.getString(dataMap, "DOCTYPE",
                    ""));
            String documentNo = "";
            String sDate = "";

            if (Util.equals(custId, "") || Util.equals(dupNo, "")
                    || Util.equals(cntrNo, "")) {
                continue;
            }

            String schema = "";

            if (UtilConstants.Casedoc.DocType.個金.equals(docType)) {
                schema = UtilConstants.CaseSchema.個金;
            } else {
                schema = UtilConstants.CaseSchema.企金;
            }

            String fullCustId = custId + "-" + dupNo;

            if (Util.notEquals(custId, "") && Util.notEquals(cntrNo, "")) {

                // 該額度沒有處理過
                if (!cntrNoWithCustIdMap.containsKey(cntrNo)) {

                    // 取得該額度ELF383最新一筆
                    Map<String, Object> quotapprMap = misdbBASEService
                            .findLastQuotapprOrderBySDate(custId, dupNo, cntrNo);

                    if (quotapprMap != null && !quotapprMap.isEmpty()) {
                        documentNo = Util.trim(MapUtils.getString(quotapprMap,
                                "DOCUMENTNO", ""));
                        sDate = Util.trim(MapUtils.getString(quotapprMap,
                                "SDATE", ""));
                        // 判斷QUOTAPPR 最新一筆有無案號
                        if (Util.notEquals(documentNo, "")) {
                            String eloanDocumentNo = "";
                            // 有案號
                            // 判斷案號與本筆ELOAN是否相符
                            if (StringUtils.length(documentNo) == 12) {
                                // 舊案號 12碼 099005LMS001

                                eloanDocumentNo = StrUtils.concat(
                                        Util.parseInt(caseYear) - 1911,
                                        caseBrId,
                                        schema,
                                        Util.addZeroWithValue(
                                                Util.trim(caseSeq), 3));
                            } else {
                                // 新案號14碼 105005LMS00001
                                eloanDocumentNo = StrUtils.concat(
                                        Util.parseInt(caseYear) - 1911,
                                        caseBrId,
                                        schema,
                                        Util.addZeroWithValue(
                                                Util.trim(caseSeq), 5));
                            }

                            if (Util.equals(documentNo, eloanDocumentNo)) {
                                // 相符

                                // 更新QUOTSUB***********************************************************
                                errMsg.append(updateDB2FromEloan07(quotapprMap,
                                        cntrNoWithCustIdMap));
                                // ***********************************************************************

                            } else {
                                // 不符
                                // 讀下一筆
                                continue;
                            }

                        } else {
                            // 更新QUOTSUB***********************************************************
                            errMsg.append(updateDB2FromEloan07(quotapprMap,
                                    cntrNoWithCustIdMap));
                            // ***********************************************************************

                        }
                    } else {
                        // 沒有最新一筆QUOTAPPR
                        // 此額度序號處理結束
                        cntrNoWithCustIdMap.put(cntrNo, fullCustId);
                    }

                }
            }

        }

        return errMsg.toString();

    }

    @NonTransactional
    String updateDB2FromEloan07(Map<String, Object> quotapprMap,
                                Map<String, String> cntrNoWithCustIdMap) {

        String doKind = "";
        String custId = Util
                .trim(MapUtils.getString(quotapprMap, "CUSTID", ""));

        String dupNo = Util.trim(MapUtils.getString(quotapprMap, "DUPNO", ""));
        String cntrNo = Util
                .trim(MapUtils.getString(quotapprMap, "CNTRNO", ""));
        String sDate = Util.trim(MapUtils.getString(quotapprMap, "SDATE", ""));
        String fullCustId = custId + "-" + dupNo;

        List<Map<String, Object>> quotsubList = misdbBASEService
                .findLastQuotsubByCustIdCntrNoAndSDate(custId, dupNo, cntrNo,
                        sDate);

        if (quotsubList == null || quotsubList.isEmpty()) {
            doKind = "1";// INSERT Z15
        } else {
            doKind = "2";// UPDATE 102 為 Z15
        }

        boolean not102 = false;
        for (Map<String, Object> quotsubMap : quotsubList) {

            String loanTp = Util.trim(MapUtils.getString(quotsubMap, "LOANTP",
                    ""));

            if (Util.notEquals(loanTp, "102")) {
                not102 = true;
                break;
            }
        }

        if (not102) {
            // 除了102還有其他科目，就不上傳Z15
            return "";
        }

        if (Util.equals(doKind, "1")) {
            misQuotsubService.insert(custId, dupNo, cntrNo, sDate, "Z15", "0",
                    "", Util.parseDouble(0), "", Util.parseDouble(0), "N",
                    "J5304", Util.parseBigDecimal(999), "");
        } else {
            String newLoanTp = "Z15";
            String orgLoanTp = "102";
            misdbBASEService.updateQuotsubLoanTp(custId, dupNo, cntrNo, sDate,
                    orgLoanTp, newLoanTp);
        }

        cntrNoWithCustIdMap.put(cntrNo, fullCustId);

        return "";

    }

    @Override
    @NonTransactional
    public String doLmsBatch0008(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        // 存放已經處理過的額度與統編資訊
        LinkedHashMap<String, String> cntrNoWithCustIdMap = new LinkedHashMap<String, String>();

        // 1.取得ELOAN要轉檔資料
        // 7151 7152 7153 7154 7155
        List<Map<String, Object>> _715xrow = eloandbService
                .find715_CntrDoc(false);

        // only 715
        List<Map<String, Object>> _715row = eloandbService
                .find715_CntrDoc(true);

        logger.info("---------------------開始7151~1755轉檔步驟-----------------");
        for (Map<String, Object> data : _715xrow) {
            String custId = Util.trim((String) data.get("CUSTID"));
            String dupNo = Util.trim((String) data.get("DUPNO"));
            String cntrNo = Util.trim((String) data.get("CNTRNO"));

            String caseYear = Util.trim(MapUtils.getString(data, "CASEYEAR",
                    "0"));
            String caseBrId = Util.trim(MapUtils
                    .getString(data, "CASEBRID", ""));
            String caseSeq = Util
                    .trim(MapUtils.getString(data, "CASESEQ", "0"));
            String docType = Util.trim(MapUtils.getString(data, "DOCTYPE", ""));
            if (Util.equals(custId, "") || Util.equals(dupNo, "")
                    || Util.equals(cntrNo, "")) {
                continue;
            }
            String schema = UtilConstants.Casedoc.DocType.個金.equals(docType) ? UtilConstants.CaseSchema.個金
                    : UtilConstants.CaseSchema.企金;
            if (Util.notEquals(custId, "") && Util.notEquals(cntrNo, "")) {

                if (!cntrNoWithCustIdMap.containsKey(cntrNo)) {
                    // 取得該額度ELF383最新一筆
                    Map<String, Object> quotapprMap = misdbBASEService
                            .findLastQuotapprOrderBySDate(custId, dupNo, cntrNo);
                    if (quotapprMap != null && !quotapprMap.isEmpty()) {
                        String documentNo = Util.trim(MapUtils.getString(
                                quotapprMap, "DOCUMENTNO", ""));
                        if (Util.notEquals(documentNo, "")) {
                            String eloanDocumentNo = "";
                            // 有案號
                            // 判斷案號與本筆ELOAN是否相符
                            if (StringUtils.length(documentNo) == 12) {
                                // 舊案號 12碼 099005LMS001

                                eloanDocumentNo = StrUtils.concat(
                                        Util.parseInt(caseYear) - 1911,
                                        caseBrId,
                                        schema,
                                        Util.addZeroWithValue(
                                                Util.trim(caseSeq), 3));
                            } else {
                                // 新案號14碼 105005LMS00001
                                eloanDocumentNo = StrUtils.concat(
                                        Util.parseInt(caseYear) - 1911,
                                        caseBrId,
                                        schema,
                                        Util.addZeroWithValue(
                                                Util.trim(caseSeq), 5));
                            }

                            if (Util.equals(documentNo, eloanDocumentNo)) {
                                // 相符

                                // 更新QUOTSUB***********************************************************
                                updateDB2FromEloan08(quotapprMap);
                                logger.info("update mis.quotappr, cntrno :"
                                        + cntrNo + ",custId:" + custId);
                                // ***********************************************************************

                                cntrNoWithCustIdMap.put(cntrNo, cntrNo);

                            } else {
                                // 不符
                                // 讀下一筆
                                continue;
                            }
                        }
                    }
                }
            }
        }

        // 715 科目要讀雅鴻的檔
        logger.info("---------------------開始715轉檔步驟-----------------");
        for (Map<String, Object> data : _715row) {
            String custId = Util.trim((String) data.get("CUSTID"));
            String dupNo = Util.trim((String) data.get("DUPNO"));
            String cntrNo = Util.trim((String) data.get("CNTRNO"));
            String cntrNoMainId = Util.trim((String) data.get("CNTRMAINID"));

            String caseYear = Util.trim(MapUtils.getString(data, "CASEYEAR",
                    "0"));
            String caseBrId = Util.trim(MapUtils
                    .getString(data, "CASEBRID", ""));
            String caseSeq = Util
                    .trim(MapUtils.getString(data, "CASESEQ", "0"));
            String docType = Util.trim(MapUtils.getString(data, "DOCTYPE", ""));
            if (Util.equals(custId, "") || Util.equals(dupNo, "")
                    || Util.equals(cntrNo, "")) {
                continue;
            }
            String schema = UtilConstants.Casedoc.DocType.個金.equals(docType) ? UtilConstants.CaseSchema.個金
                    : UtilConstants.CaseSchema.企金;
            if (Util.notEquals(custId, "") && Util.notEquals(cntrNo, "")) {

                Map<String, Object> exffait = misdbBASEService.findImexEXFFAIT(
                        custId, dupNo, cntrNo);

                // boolean doTest =
                // "07AA037EB0DA0032482579FA00114523".equals(cntrNoMainId);
                boolean doTest = false;

                if ((exffait != null && !exffait.isEmpty()) || doTest) {
                    String exffait_lnap_code = MapUtils.getString(exffait,
                            "EXFFAIT_LNAP_CODE", "9502");
                    logger.info("找到對應715科目，update l140m01c");
                    eloandbService.updateL140m01cLoanTp(cntrNoMainId, "715",
                            exffait_lnap_code);

                    if (!cntrNoWithCustIdMap.containsKey(cntrNo)) {
                        // 取得該額度ELF383最新一筆
                        Map<String, Object> quotapprMap = misdbBASEService
                                .findLastQuotapprOrderBySDate(custId, dupNo,
                                        cntrNo);
                        if (quotapprMap != null && !quotapprMap.isEmpty()) {
                            String documentNo = Util.trim(MapUtils.getString(
                                    quotapprMap, "DOCUMENTNO", ""));
                            String sDate = Util.trim(MapUtils.getString(
                                    quotapprMap, "SDATE", ""));
                            if (Util.notEquals(documentNo, "")) {
                                String eloanDocumentNo = "";
                                // 有案號
                                // 判斷案號與本筆ELOAN是否相符
                                if (StringUtils.length(documentNo) == 12) {
                                    // 舊案號 12碼 099005LMS001

                                    eloanDocumentNo = StrUtils.concat(
                                            Util.parseInt(caseYear) - 1911,
                                            caseBrId,
                                            schema,
                                            Util.addZeroWithValue(
                                                    Util.trim(caseSeq), 3));
                                } else {
                                    // 新案號14碼 105005LMS00001
                                    eloanDocumentNo = StrUtils.concat(
                                            Util.parseInt(caseYear) - 1911,
                                            caseBrId,
                                            schema,
                                            Util.addZeroWithValue(
                                                    Util.trim(caseSeq), 5));
                                }

                                if (Util.equals(documentNo, eloanDocumentNo)) {
                                    // 相符

                                    // 更新QUOTSUB***********************************************************
                                    updateDB2FromEloan08(quotapprMap);
                                    logger.info("update mis.quotappr, cntrno :"
                                            + cntrNo + ",custId:" + custId);
                                    // ***********************************************************************

                                    cntrNoWithCustIdMap.put(cntrNo, cntrNo);

                                } else {
                                    // 不符
                                    // 讀下一筆
                                    continue;
                                }
                            }
                        }
                    }
                }
            }
        }
        return errMsg.toString();

    }

    @NonTransactional
    private void updateDB2FromEloan08(Map<String, Object> quotapprMap) {

        String custId = Util
                .trim(MapUtils.getString(quotapprMap, "CUSTID", ""));

        String dupNo = Util.trim(MapUtils.getString(quotapprMap, "DUPNO", ""));
        String cntrNo = Util
                .trim(MapUtils.getString(quotapprMap, "CNTRNO", ""));
        String sDate = Util.trim(MapUtils.getString(quotapprMap, "SDATE", ""));

        String newLoanTp = "950";
        String orgLoanTp = "715";
        misdbBASEService.updateQuotsubLoanTp(custId, dupNo, cntrNo, sDate,
                orgLoanTp, newLoanTp);

    }

    /**
     * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能 將ELF412 ELF412_REALCKFG 搬到
     * ELF412_REALCKFG_BK 並清除ELF412_REALCKFG 將ELF412 ELF412_REALDT 搬到
     * ELF412_REALDT_BK 並清除ELF412_REALDT
     */
    @Override
    @NonTransactional
    public String doLmsBatch0009(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        // UPDATE MIS.ELF412 SET ELF412_REALCKFG_BK = ELF412_REALCKFG,
        // ELF412_REALDT_BK = ELF412_REALDT WHERE ELF412_REALCKFG = 'Y'
        // UPDATE MIS.ELF412 SET ELF412_REALCKFG='', ELF412_REALDT= NULL

        misdbBASEService.updateElf412_realCkFgAndRealDt1();
        misdbBASEService.updateElf412_realCkFgAndRealDt2();

        return errMsg.toString();

    }

    @Override
    @NonTransactional
    public String doLmsBatch0010(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        // 1.取得ELOAN要轉檔資料
        List<Map<String, Object>> rowElf412 = misdbBASEService
                .batchForDoLmsBatch0010_queryElf412();
        Timestamp nowTS = CapDate.getCurrentTimestamp();
        logger.info("---------------------doLmsBatch0010-開始轉檔步驟-----------------");
        for (Map<String, Object> data412 : rowElf412) {
            String custId = Util.trim((String) data412.get("ELF412_CUSTID"));
            String dupNo = Util.trim((String) data412.get("ELF412_DUPNO"));
            String brNo = Util.trim((String) data412.get("ELF412_BRANCH"));

            if (Util.equals(custId, "") || Util.equals(brNo, "")) {
                continue;
            }

            List<Map<String, Object>> rowLnf022 = misdbBASEService
                    .batchForDoLmsBatch0010_queryLnf022(brNo, custId, dupNo);
            for (Map<String, Object> data022 : rowLnf022) {
                String cntrNo = Util.trim((String) data022.get("CONTRACT"));
                if (Util.equals(cntrNo, "")) {
                    continue;
                }
                String cntrBranch = Util.getLeftStr(cntrNo, 3);

                Map<String, Object> elf447n = misELF447nService
                        .findByMaxChkDate(custId, dupNo, cntrNo);

                if (elf447n != null && !elf447n.isEmpty()) {
                    String caseLvl = MapUtils.getString(elf447n,
                            "ELF447N_CASELEVEL", "");
                    if (retrialService
                            .chkNeedRealReview(custId, dupNo, caseLvl)) {
                        // 參貸同業主辦之聯合授信案件不用覆審
                        String LNF020_SYND_TYPE = Util.trim(MapUtils.getString(
                                elf447n, "LNF020_SYND_TYPE", ""));
                        if (Util.notEquals(LNF020_SYND_TYPE, "2")) {

                            String reviewBrNo = MapUtils.getString(elf447n,
                                    "ELF447N_REVIEWBR", "");
                            if (Util.equals(reviewBrNo, "")) {
                                reviewBrNo = cntrBranch;
                            }

                            if (Util.equals(cntrBranch, reviewBrNo)) {
                                ELF412B elf412b = misELF412BService.findByPk(
                                        brNo, custId, dupNo);

                                if (elf412b == null) {

                                    String unid = MapUtils.getString(elf447n,
                                            "ELF447N_UNID", "");
                                    Date chkDate = Util.parseDate(MapUtils
                                            .getString(elf447n,
                                                    "ELF447_CHKDATE", ""));

                                    elf412b = new ELF412B();
                                    elf412b.setElf412b_branch(brNo);
                                    elf412b.setElf412b_custId(custId);
                                    elf412b.setElf412b_dupNo(dupNo);
                                    elf412b.setElf412b_newAdd("N");

                                    String tChkDate = Util.trim(MapUtils
                                            .getString(elf447n,
                                                    "ELF447_CHKDATE", ""));
                                    if (Util.notEquals(tChkDate, "")) {
                                        if (LMSUtil
                                                .cmpDate(
                                                        CapDate.parseDate(tChkDate),
                                                        "<=",
                                                        CapDate.parseDate("2017-07-01"))) {
                                            elf412b.setElf412b_newDate("010607");
                                        } else {
                                            // 轉西元年月日 2009-11-01 -> 009811
                                            String yyyy = tChkDate.substring(0,
                                                    4);
                                            String mm = tChkDate
                                                    .substring(5, 7);
                                            String dd = tChkDate.substring(8,
                                                    10);

                                            String minDataYMC = StringUtils
                                                    .right("0000"
                                                            + (Integer
                                                            .parseInt(yyyy) - 1911)
                                                            + mm, 6);

                                            elf412b.setElf412b_newDate(minDataYMC);

                                        }

                                    } else {
                                        elf412b.setElf412b_newDate("010607");
                                    }

                                    elf412b.setElf412b_tmestamp(nowTS);

                                    // elf412b.setElf412b_newAdd("");
                                    // elf412b.setElf412b_newDate("");
                                    // elf412b.setElf412b_lrDate(CapDate
                                    // .parseDate("2017-07-01"));
                                    // elf412b.setElf412b_rckdLine("A");

                                    // ---
                                    elf412b.setElf412b_nckdFlag("");
                                    elf412b.setElf412b_nckdDate(CapDate
                                            .parseDate(CapDate.ZERO_DATE));
                                    // elf412b.setElf412b_nckdMemo("");
                                    elf412b.setElf412b_nextNwDt(CapDate
                                            .parseDate(CapDate.ZERO_DATE));
                                    elf412b.setElf412b_nextLtDt(CapDate
                                            .parseDate(CapDate.ZERO_DATE));

                                    elf412b.setElf412b_newRptId(unid);
                                    elf412b.setElf412b_newRptDt(chkDate);
                                    elf412b.setElf412b_newDraId("");
                                    elf412b.setElf412b_newDraDt(null);

                                    // J-106-0145-004 Web e-Loan
                                    // 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
                                    retrialService
                                            .gfnCTL_Caculate_ELF412B(elf412b);

                                    if (Util.equals(Util.trim(elf412b
                                            .getElf412b_oldRptId()), "")) {
                                        elf412b.setElf412b_oldRptId(unid);
                                        elf412b.setElf412b_oldRptDt(chkDate);
                                    }
                                    if (Util.equals(Util.trim(elf412b
                                            .getElf412b_oldDraId()), "")) {
                                        elf412b.setElf412b_oldDraId("");
                                        elf412b.setElf412b_oldDraDt(null);
                                    }

                                    if (elf412b.getElf412b_llrDate() == null) {
                                        elf412b.setElf412b_llrDate(CapDate
                                                .parseDate(CapDate.ZERO_DATE));
                                    }
                                    if (elf412b.getElf412b_lrDate() == null) {
                                        elf412b.setElf412b_lrDate(CapDate
                                                .parseDate(CapDate.ZERO_DATE));
                                    }

                                    if (elf412b.getElf412b_nckdDate() == null) {
                                        elf412b.setElf412b_nckdDate(CapDate
                                                .parseDate(CapDate.ZERO_DATE));
                                    }
                                    if (elf412b.getElf412b_cancelDt() == null) {
                                        elf412b.setElf412b_cancelDt(CapDate
                                                .parseDate(CapDate.ZERO_DATE));
                                    }
                                    if (elf412b.getElf412b_uckdDt() == null) {
                                        elf412b.setElf412b_uckdDt(CapDate
                                                .parseDate(CapDate.ZERO_DATE));
                                    }
                                    if (elf412b.getElf412b_nextNwDt() == null) {
                                        elf412b.setElf412b_nextNwDt(CapDate
                                                .parseDate(CapDate.ZERO_DATE));
                                    }
                                    if (elf412b.getElf412b_nextLtDt() == null) {
                                        elf412b.setElf412b_nextLtDt(CapDate
                                                .parseDate(CapDate.ZERO_DATE));
                                    }

                                    elf412b.setElf412b_isAllNew("G");

                                    retrialService
                                            .upELF412B_DelThenInsert(elf412b);

                                    break;
                                }

                            }

                        }
                    }
                }

            }

        }

        return errMsg.toString();

    }

    /**
     * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0011(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");

        try {
            misdbBASEService.doLmsBatch0011();
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();

    }

    /**
     * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0012(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");

        String kind = Util.trim(request.getString("kind"));

        try {
            if (Util.equals(kind, "1")) {
                misdbBASEService.doLmsBatch0012();
            } else if (Util.equals(kind, "2")) {
                dwdbBASEService.doLmsBatch0012();
            } else if (Util.equals(kind, "3")) {
                obsDBService.doLmsBatch0012("Z01");
                obsDBService.doLmsBatch0012("Z03");
            } else if (Util.equals(kind, "4")) {
                // FOR DW 轉檔備援
                dwdbBASEService.doLmsBatch0012_1();
            } else if (Util.equals(kind, "5")) {
                // FOR DW 往來實績
                dwdbBASEService.doLmsBatch0012_2();
            } else {
                errMsg.append("參數kind錯誤");
            }
        } catch (Exception e) {

            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();

    }

    /**
     * G-107-0115-001 Web e-Loan 授信系統配合巴箇行整併調整簽報書相關資料
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0013(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");

        String kind = Util.trim(request.getString("kind"));

        try {
            if (Util.equals(kind, "1")) {
                misdbBASEService.doLmsBatch0013();
            } else if (Util.equals(kind, "2")) {
                dwdbBASEService.doLmsBatch0013();
            } else if (Util.equals(kind, "3")) {
                obsDBService.doLmsBatch0013("0A6");
            } else if (Util.equals(kind, "4")) {
                // FOR DW 轉檔備援
                dwdbBASEService.doLmsBatch0013_1();
            } else if (Util.equals(kind, "5")) {
                // FOR DW 往來實績
                dwdbBASEService.doLmsBatch0013_2();
            } else {
                errMsg.append("參數kind錯誤");
            }
        } catch (Exception e) {

            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();

    }

    /**
     * J-107-0263-001 Web e-Loan 授信系統配合國金部企金覆審清單檢核機制修改資料
     *
     *
     */
    @Override
    @NonTransactional
    public String doLmsBatch0014() {
        StringBuffer errMsg = new StringBuffer("");
        try {
            misdbBASEService.doLmsBatch0014();
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();
    }

    /**
     * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
     *
     * 本功能適用該ID下該間分行案件全部移轉 上傳轉換ID到LNF07A
     */
    @Override
    @NonTransactional
    public String doLmsBatch0015(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        // 上傳MIS DB 與 海外AS400

        String type = request.getString("type");
        String exDate = request.getString("exDate");
        String fBranch = request.getString("fBranch");
        String tBranch = request.getString("tBranch");
        String version = request.getString("version");
        String kind = Util.trim(request.getString("kind"));

        String lnf07a_key_1 = "ELOAN-BRTOBR-01";

        try {
            if (Util.equals(kind, "1")) {
                // 更新MIS與AS400的ELF506-BY ELOAN額度序號
                errMsg.append(updateDB2FromEloan15_1(exDate, fBranch, tBranch,
                        version));
            } else if (Util.equals(kind, "2")) {
                // 上傳轉換名單到MIS LNF07A
                errMsg.append(updateDB2FromEloan15_2(lnf07a_key_1, exDate,
                        fBranch, tBranch));
            } else if (Util.equals(kind, "3")) {
                // 更新MIS相關TABLE，依LNF07A CUSTID 名單
                // 1.LNFE0854~LNFE0856 分行
                // 2.ELLNGTEE ID下Y02的額度序號為Y05
                errMsg.append(updateDB2FromEloan15_3(lnf07a_key_1, exDate,
                        fBranch, tBranch));
            } else if (Util.equals(kind, "4")) {
                // 更新ELOAN相關如
                // 1.CNTRNO
                // 2.簽報書授權
                errMsg.append(updateDB2FromEloan15_4(exDate, fBranch, tBranch,
                        version));
            } else if (Util.equals(kind, "5")) {
                // 非必要執行，正常1/3就會有Y05資料
                // 更新DW相關TABLE如LNCNTROVS，
                // 上傳轉換名單到MIS LNF07A
                errMsg.append(updateDB2FromEloan15_5(exDate, fBranch, tBranch));
            } else {
                errMsg.append("參數kind錯誤");
            }
        } catch (Exception e) {

            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        // // 1.更新MIS LNFE0854、0855、0856
        // String updateCustId = "THZ0074868";
        // String updateDupNo = "0";
        // try {
        // misdbBASEService.updateELF085XMdbrnoByCustId(tBranch, updateCustId,
        // updateDupNo, fBranch);
        // } catch (Exception e) {
        // System.out.println(e.toString());
        // // 若無資料不處理
        // }

        // 2.更新LNF07A

        return errMsg.toString();
    }

    /**
     * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
     * 更新MIS與AS400的ELF506-BY ELOAN額度序號
     *
     * @param exDate
     * @param fBranch
     * @param tBranch
     * @param version
     * @return
     */
    @NonTransactional
    String updateDB2FromEloan15_1(String exDate, String fBranch,
                                  String tBranch, String version) {
        StringBuffer errMsg = new StringBuffer("");
        // 上傳MIS DB 與 海外AS400

        // 1.取得ELOAN要轉檔資料
        // 因為ELF506 沒有ID 欄位，所以只能先從ELOAN找到該ID在簽報書中的所有額度序號，再用該額度序號更新ELF506
        LinkedHashMap<String, String> cntrNoWithCustIdMap = new LinkedHashMap<String, String>();

        List rows = eloandbService.findLms140QByComBrToBr01_Elf506Y01ToY04(
                exDate, fBranch, tBranch);

        Iterator it = rows.iterator();
        while (it.hasNext()) {
            Map dataMap = (Map) it.next();
            String custId = Util.trim((String) dataMap.get("CUSTID"));
            String dupNo = Util.trim((String) dataMap.get("DUPNO"));
            String cntrNo = Util.trim((String) dataMap.get("CNTRNO"));

            if (Util.equals(custId, "") || Util.equals(dupNo, "")
                    || Util.equals(cntrNo, "")) {
                continue;
            }

            String fullCustId = custId + "-" + dupNo;

            if (Util.notEquals(custId, "") && Util.notEquals(cntrNo, "")) {
                if (!cntrNoWithCustIdMap.containsKey(cntrNo)) {
                    cntrNoWithCustIdMap.put(cntrNo, fullCustId);
                }
            }

        }

        // 2.更新ELF506/ELF515 (MIS與AS400資料)
        // 上傳海外
        if (cntrNoWithCustIdMap == null || cntrNoWithCustIdMap.isEmpty()) {
            return "無e-Loan額度與借款人ID對應資料";
        }

        for (String cntrNo : cntrNoWithCustIdMap.keySet()) {
            if (Util.equals(cntrNo, "")) {
                continue;
            }
            String fullCustId = "";
            if (cntrNoWithCustIdMap.containsKey(cntrNo)) {
                fullCustId = Util.trim(cntrNoWithCustIdMap.get(cntrNo));
            }
            if (Util.equals(fullCustId, "")) {
                continue;
            }

            String custId = fullCustId.split("-")[0];
            String dupNo = fullCustId.split("-")[1];

            String y04CntrNo = tBranch + cntrNo.substring(3, 12);
            String modifyUnit = "";
            modifyUnit = version;
            IBranch iBranch = branchService.getBranch(fBranch);

            // ELF506**********************************************************************************

            // 檢查MIS ELF506 有沒有 Y04 開頭的額度序號
            Map<String, Object> elf506 = misELF506Service
                    .getByCntrNo(y04CntrNo);

            // 已經有新額度序號的資料就不UPDATE
            if (elf506 != null && !elf506.isEmpty()) {
                continue;
            }

            elf506 = null;
            elf506 = misELF506Service.getByCntrNo(cntrNo);
            // 無原額度資料也不UPDATE
            if (elf506 == null || elf506.isEmpty()) {
                continue;
            }

            // String ELF506_MODIFYUNIT = Util.trim(((String) elf506
            // .get("ELF506_MODIFYUNIT")));
            // if (Util.equals(ELF506_MODIFYUNIT, "")) {
            // modifyUnit = ELF506_MODIFYUNIT.substring(0, 5) + "-" + version;
            // } else {
            // modifyUnit = "ELOAN-" + version;
            // }

            misELF506Service
                    .updateCntrNoByCntrNo(y04CntrNo, cntrNo, modifyUnit);

            // 更新AS400
            if (iBranch != null) {
                if (UtilConstants.BrNoType.國外.equals(iBranch.getBrNoFlag())) {

                    Map<String, Object> elf506As400 = obsdbELF506Service
                            .getByCntrNo(fBranch, y04CntrNo);

                    // 已經有新額度序號的資料就不UPDATE
                    if (elf506As400 != null && !elf506As400.isEmpty()) {
                        continue;
                    }

                    elf506As400 = null;
                    elf506As400 = obsdbELF506Service.getByCntrNo(fBranch,
                            cntrNo);
                    // 無原額度資料也不UPDATE
                    if (elf506As400 == null || elf506As400.isEmpty()) {
                        continue;
                    }

                    // 有資料才UPDATE
                    obsdbELF506Service.updateCntrNoByCntrNo(fBranch, y04CntrNo,
                            cntrNo, modifyUnit);

                }
            }

            // ELF515**********************************************************************************

            // 檢查MIS ELF515 有沒有 Y04 開頭的額度序號
            Map<String, Object> elf515 = misdbBASEService
                    .findElf515ByCntrNoForBt(y04CntrNo);

            // 已經有新額度序號的資料就不UPDATE
            if (elf515 != null && !elf515.isEmpty()) {
                continue;
            }

            elf515 = null;
            elf515 = misdbBASEService.findElf515ByCntrNoForBt(cntrNo);
            // 無原額度資料也不UPDATE
            if (elf515 == null || elf515.isEmpty()) {
                continue;
            }

            // String ELF506_MODIFYUNIT = Util.trim(((String) elf506
            // .get("ELF506_MODIFYUNIT")));
            // if (Util.equals(ELF506_MODIFYUNIT, "")) {
            // modifyUnit = ELF506_MODIFYUNIT.substring(0, 5) + "-" + version;
            // } else {
            // modifyUnit = "ELOAN-" + version;
            // }

            misdbBASEService.updateElf515CntrNoByCntrNoForBt(y04CntrNo, cntrNo,
                    modifyUnit);

            // 更新AS400
            if (iBranch != null) {
                if (UtilConstants.BrNoType.國外.equals(iBranch.getBrNoFlag())) {

                    Map<String, Object> elf515As400 = obsDBService
                            .findElf515ByCntrNoForBt(fBranch, y04CntrNo);

                    // 已經有新額度序號的資料就不UPDATE
                    if (elf515As400 != null && !elf515As400.isEmpty()) {
                        continue;
                    }

                    elf515As400 = null;
                    elf515As400 = obsDBService.findElf515ByCntrNoForBt(fBranch,
                            cntrNo);
                    // 無原額度資料也不UPDATE
                    if (elf515As400 == null || elf515As400.isEmpty()) {
                        continue;
                    }

                    // 有資料才UPDATE
                    obsDBService.updateElf515ByCntrNoByCntrNoForBt(fBranch,
                            y04CntrNo, cntrNo, modifyUnit);

                }
            }

            // 更新完後，AS400 SP搬擋案

        }

        return errMsg.toString();
    }

    /**
     * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
     *
     * 上傳轉換名單到MIS LNF07A
     *
     * @param lnf07a_key_1
     * @param exDate
     * @param fBranch
     * @param tBranch
     * @return
     */
    @NonTransactional
    String updateDB2FromEloan15_2(String lnf07a_key_1, String exDate,
                                  String fBranch, String tBranch) {

        // 1.取得ELOAN要轉檔資料
        LinkedHashMap<String, String> cntrNoWithCustIdMap = new LinkedHashMap<String, String>();

        List rows = eloandbService.findBrToBr01List(exDate, fBranch, tBranch);

        Iterator it = rows.iterator();
        while (it.hasNext()) {
            Map dataMap = (Map) it.next();
            String custId = Util.trim((String) dataMap.get("CUSTID"));
            String dupNo = Util.trim((String) dataMap.get("DUPNO"));

            if (Util.equals(custId, "") || Util.equals(dupNo, "")) {
                continue;
            }

            String fullCustId = custId + "-" + dupNo;

            if (!cntrNoWithCustIdMap.containsKey(fullCustId)) {
                cntrNoWithCustIdMap.put(fullCustId, fullCustId);
            }

        }

        if (cntrNoWithCustIdMap == null || cntrNoWithCustIdMap.isEmpty()) {
            return "無e-Loan額度與借款人ID對應資料";
        }

        // lnf07a_key_1 = "ELOAN-BRTOBR-01";
        String lnf07a_key_2 = exDate; // 2019-01-01
        String lnf07a_explain = "Y02春武里轉Y05羅勇";

        // 先刪除LNF07A舊資料
        List<LNF07A> lnf07a_list_del = new ArrayList<LNF07A>();
        LNF07A lnf07a_del = new LNF07A();
        lnf07a_del.setLnf07a_key_1(lnf07a_key_1);
        lnf07a_del.setLnf07a_key_2(lnf07a_key_2);
        lnf07a_list_del.add(lnf07a_del);
        if (lnf07a_list_del.size() > 0) {
            lnLnf07aService.delete_by_key1_key2(lnf07a_list_del);
        }

        // 開始上傳LNF07A
        List<LNF07A> lnf07a_list_insert = new ArrayList<LNF07A>();
        for (String cntrNo : cntrNoWithCustIdMap.keySet()) {
            if (Util.equals(cntrNo, "")) {
                continue;
            }
            String fullCustId = "";
            if (cntrNoWithCustIdMap.containsKey(cntrNo)) {
                fullCustId = Util.trim(cntrNoWithCustIdMap.get(cntrNo));
            }
            if (Util.equals(fullCustId, "")) {
                continue;
            }

            String custId = fullCustId.split("-")[0];
            String dupNo = fullCustId.split("-")[1];

            LNF07A lnf07a = new LNF07A();
            // ==========
            String lnf07a_key_3 = custId;
            String lnf07a_key_4 = dupNo;
            String lnf07a_key_5 = "";// 保留-
            String lnf07a_content_1 = fBranch;
            String lnf07a_content_2 = tBranch;
            String lnf07a_content_3 = ""; // 保留-可放舊額度序號
            String lnf07a_content_4 = ""; // 保留-可放新額度序號
            String lnf07a_content_5 = ""; // 保留-

            lnf07a.setLnf07a_key_1(lnf07a_key_1);
            lnf07a.setLnf07a_key_2(lnf07a_key_2);
            lnf07a.setLnf07a_key_3(lnf07a_key_3);
            lnf07a.setLnf07a_key_4(lnf07a_key_4);
            lnf07a.setLnf07a_key_5(lnf07a_key_5);
            lnf07a.setLnf07a_explain(lnf07a_explain);
            lnf07a.setLnf07a_content_1(lnf07a_content_1);
            lnf07a.setLnf07a_content_2(lnf07a_content_2);
            lnf07a.setLnf07a_content_3(lnf07a_content_3);
            lnf07a.setLnf07a_content_4(lnf07a_content_4);
            lnf07a.setLnf07a_content_5(lnf07a_content_5);
            // ==========
            lnf07a_list_insert.add(lnf07a);

        }

        if (lnf07a_list_insert.size() > 0) {
            lnLnf07aService.insert(lnf07a_list_insert);
        }

        return "";

    }

    /**
     * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
     *
     * 更新MIS相關TABLE，依LNF07A CUSTID 名單
     *
     *
     * 1.LNFE0854~LNFE0856 分行 2.ELLNGTEE 下Y02的額度序號為Y05 3.ELF447N 4.ELF447
     *
     * @param lnf07a_key_1
     * @param exDate
     * @param fBranch
     * @param tBranch
     * @return
     */
    @NonTransactional
    String updateDB2FromEloan15_3(String lnf07a_key_1, String exDate,
                                  String fBranch, String tBranch) {

        misdbBASEService.doLmsBatch0015_3(lnf07a_key_1, exDate, fBranch,
                tBranch);

        return "";

    }

    /**
     * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
     *
     * // 更新ELOAN相關如 //1.CNTRNO //2.簽報書授權
     *
     * @param exDate
     * @param fBranch
     * @param tBranch
     * @param version
     * @return
     */
    @NonTransactional
    String updateDB2FromEloan15_4(String exDate, String fBranch,
                                  String tBranch, String version) {

        // 春武里簽報書新增羅勇分行授權
        eloandbService.insertL120A01AFromBRTOBR01(exDate, fBranch, tBranch,
                version);

        // 春武里簽報書修改CASEBRID
        eloandbService.updateL120M01ACaseBrIdFromBRTOBR01(exDate, fBranch,
                tBranch, version);

        // 春武里額度序號修改為羅勇分行-額度明細表額度序號
        eloandbService.updateL140m01aFromBRTOBR01(exDate, fBranch, tBranch,
                version);

        // 春武里額度序號修改為羅勇分行-聯貸額度序號
        eloandbService.updateL140m01eFromBRTOBR01(exDate, fBranch, tBranch,
                version);

        return "";

    }

    /**
     * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
     *
     * 上傳轉換名單到MIS LNF07A
     *
     * @param exDate
     * @param fBranch
     * @param tBranch
     * @return
     */
    @NonTransactional
    String updateDB2FromEloan15_5(String exDate, String fBranch, String tBranch) {

        // 1.取得ELOAN要轉檔資料
        LinkedHashMap<String, String> cntrNoWithCustIdMap = new LinkedHashMap<String, String>();

        List rows = eloandbService.findBrToBr01List(exDate, fBranch, tBranch);

        Iterator it = rows.iterator();
        while (it.hasNext()) {
            Map dataMap = (Map) it.next();
            String custId = Util.trim((String) dataMap.get("CUSTID"));
            String dupNo = Util.trim((String) dataMap.get("DUPNO"));

            if (Util.equals(custId, "") || Util.equals(dupNo, "")) {
                continue;
            }

            String fullCustId = custId + "-" + dupNo;

            if (cntrNoWithCustIdMap.containsKey(fullCustId)) {

                // ID已經轉過就不轉了
                continue;
            } else {
                cntrNoWithCustIdMap.put(fullCustId, "");
            }

            // 修改DW分行與額度

            dwdbBASEService.doLmsBatch0015_5(fBranch, tBranch, custId, dupNo);

        }

        return "";

    }

    /**
     * J-107-0357_05097_B1002 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0016(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");
        String kind = Util.trim(request.getString("kind"));
        try {
            if (Util.equals(kind, "2")) {
                // J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
                // INSERT MIS.ELF412C
                misdbBASEService.doLmsBatch0016_2();
            } else {
                // J-107-0357_05097_B1002 Web
                // e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
                // UPDATE MIS.QUOTAPPR
                misdbBASEService.doLmsBatch0016_1();
            }

        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();
    }

    /**
     * J-108-0023_05097_B1001 修改web e-loan授信覆審系統，南京東路分行授信戶覆審案件為不覆審。
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0017(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");
        String kind = Util.trim(request.getString("kind"));
        try {
            misdbBASEService.doLmsBatch0017();

        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();
    }

    /**
     * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0018(JSONObject request) {
        //
        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");

        String dataStartDate = Util.trim(request.getString("dataStartDate"));
        String dataEndDate = Util.trim(request.getString("dataEndDate"));

        if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
            final String DATE_FORMAT_STR = "yyyy-MM-dd";
            dataStartDate = CapDate.formatDate(LMSUtil.getExMonthFirstDay(-1),
                    DATE_FORMAT_STR);
            dataEndDate = CapDate.formatDate(LMSUtil.getExMonthLastDay(-1),
                    DATE_FORMAT_STR);

        }

        // 如果沒有帶參數，預設抓上月底最後一天的
        try {

            List rows = eloandbService.findAllNewCustFromL140m01a(
                    dataStartDate, dataEndDate);

            Map<String, Map<String, String>> processBorr0024 = new TreeMap<String, Map<String, String>>();
            Map<String, String> processBorrIsNew = new TreeMap<String, String>();
            Map<String, Map<String, Object>> processRate = new TreeMap<String, Map<String, Object>>();
            Map<String, String> processRateMaxDate = new TreeMap<String, String>();

            Iterator it = rows.iterator();
            while (it.hasNext()) {
                Map dataMap = (Map) it.next();

                String clType = "";
                String busCd = "";

                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String ENDDATE = Util.trim(MapUtils.getString(dataMap,
                        "ENDDATE"));
                String LNTYPE = Util
                        .trim(MapUtils.getString(dataMap, "LNTYPE"));

                Calendar today = Calendar.getInstance();
                today.setTime(Util.parseDate(ENDDATE));
                today.set(Calendar.DAY_OF_MONTH,
                        today.getActualMinimum(Calendar.DAY_OF_MONTH));

                String rateStartDate = CapDate.formatDate(today.getTime(),
                        "yyyy-MM-dd");

                today.setTime(Util.parseDate(ENDDATE));
                today.set(Calendar.DAY_OF_MONTH,
                        today.getActualMaximum(Calendar.DAY_OF_MONTH));

                String rateEndDate = CapDate.formatDate(today.getTime(),
                        "yyyy-MM-dd");

                String fullRateDate = rateStartDate + "^" + rateEndDate;
                Map<String, Object> rate = null;
                String queryDataYMD = "";

                if (processRate.containsKey(fullRateDate)) {
                    rate = processRate.get(fullRateDate);
                    queryDataYMD = MapUtils.getString(processRateMaxDate,
                            fullRateDate, "0001-01-01");
                } else {
                    // 月底匯率--取得最後一天匯率
                    Map<String, Object> dataYMDMap = rateService
                            .findLastDateBetweenRATEYMDRange(rateStartDate,
                                    rateEndDate);
                    queryDataYMD = Util.trim(MapUtils.getString(dataYMDMap,
                            "MAXDATAYMD", ""));   //抓不到匯率就以當日最新匯率替代

                    if (Util.equals(queryDataYMD, "")) {

                        queryDataYMD = "0000000";
                        // errMsg.append("無法取得" + rateStartDate + "~"
                        // + rateEndDate + "最新匯率日期");
                        // return errMsg.toString();
                    }

                    rate = rateService.findByDate(queryDataYMD);
                    if (rate == null || rate.isEmpty()) {
                        errMsg.append("無日期為" + queryDataYMD + "之匯率檔");
                        return errMsg.toString();
                    }

                    processRate.put(fullRateDate, rate);
                    processRateMaxDate.put(fullRateDate, queryDataYMD);

                }

                // 判斷是否有判斷過新戶
                // 同一個客戶在每個月新作案件，是否為新客戶之狀態可能不同，所以KEY要含ENDDATE的年月
                String fullCustIdIsNew = CUSTID + "-" + DUPNO + "-"
                        + Util.getLeftStr(ENDDATE, 7);
                String isNew = "Y";
                if (processBorrIsNew.containsKey(fullCustIdIsNew)) {
                    isNew = processBorrIsNew.get(fullCustIdIsNew);

                } else {

                    // 判斷是否有非新戶額度
                    // 依e-Loan產生之報表，再排除非新戶(最近一年有往來)
                    //
                    //
                    // 非新戶的條件如下(最近一年有往來)定義如下:
                    // 假設核准日為2018/10/01
                    //
                    // 1.額度建檔日期介於 2017/10/01~2018/09/30
                    // 或
                    // 2.額度銷戶日期介於 2017/10/01~2018/09/30
                    // 或
                    // 3.額度建檔日期小於 2017/10/01，額度銷戶日期大於2018/09/30
                    // (代表2017/10/01-2018/09/30 之間仍有額度)
                    // 或
                    // 4.額度建檔日期小於 2017/10/01，額度於產生報表時尚未銷戶
                    // (代表2017/10/01-2018/09/30 之間仍有額度)

                    // **************************************************************************************
                    // 企金處陳嘉雄說:最近一年有沒有往來要改成以核准年月的上一個月底往前算一年，也就是核准日
                    // 2019-04-20，則最近一年為2018-04-01~2019-03-31
                    Date baseEndDate = Util.parseDate(ENDDATE); // 核准日是2019-04-20

                    String minDate = Util.getLeftStr(CapDate.formatDate(
                                    CapDate.addMonth(baseEndDate, -12), "yyyy-MM-dd"),
                            7)
                            + "-01"; // 最近一年最小日 2018-04-01
                    String tmpThisEndDate = Util.getLeftStr(
                            CapDate.formatDate(baseEndDate, "yyyy-MM-dd"), 7)
                            + "-01"; // 先取得2019-04-01，再減一天為 2019-03-31
                    String maxDate = CapDate.formatDate(CapDate.shiftDays(
                            Util.parseDate(tmpThisEndDate), -1), "yyyy-MM-dd"); // 最近一年最大日
                    // 2018-03-31

                    // Calendar qDate = Calendar.getInstance();
                    // qDate.setTime(Util.parseDate(ENDDATE));
                    // qDate.add(Calendar.MONTH, -12);
                    // qDate.set(Calendar.DAY_OF_MONTH,
                    // qDate.getActualMinimum(Calendar.DAY_OF_MONTH));

                    // String minDate = CapDate.formatDate(qDate.getTime(),
                    // "yyyy-MM-dd");
                    //
                    // qDate.setTime(Util.parseDate(ENDDATE));
                    // qDate.add(Calendar.DATE, -1);
                    // qDate.set(Calendar.DAY_OF_MONTH,
                    // qDate.getActualMaximum(Calendar.DAY_OF_MONTH));

                    // String maxDate = CapDate.formatDate(qDate.getTime(),
                    // "yyyy-MM-dd");

                    // 判斷新戶**************************************************

                    // 搜尋一年內曾經有出現之額度(含已銷戶)

                    // if (Util.equals(CUSTID, "05717502")) {
                    // System.out.println("05717502");
                    // }
                    List<Map<String, Object>> listLnf020 = misLNF020Service
                            .findIsNewCust(CUSTID, DUPNO, minDate, maxDate);

                    if (listLnf020 != null && !listLnf020.isEmpty()) {

                        // 一年內有往來

                        // 預設為非新戶
                        isNew = "N";// 預設為N
                        boolean hasNotExcept = false; // 沒有有不能排除的

                        // 有非新戶額度，要再判斷有沒有需排除科目
                        for (Map<String, Object> lnf020 : listLnf020) {

                            String LNF020_CONTRACT = MapUtils.getString(lnf020,
                                    "LNF020_CONTRACT", "");
                            String LNF020_CANCEL_DATE = MapUtils.getString(
                                    lnf020, "LNF020_CANCEL_DATE", "");

                            if (Util.equals(LNF020_CANCEL_DATE, "")
                                    && Util.notEquals(LNF020_CONTRACT, "")) {
                                // 還沒銷戶，抓LNF022
                                // 有非排除額度(LNF022_DEP_CODE = '09' OR
                                // LNF022_DEP_SUB_CD = '02'，代表此額度不能排除

                                Map<String, Object> lnf022Map = misLNF022Service
                                        .findByCntrNoFetchOne(LNF020_CONTRACT);

                                if (lnf022Map != null && !lnf022Map.isEmpty()) {
                                    String LNF022_DEP_CODE = MapUtils
                                            .getString(lnf022Map,
                                                    "LNF022_DEP_CODE", "");
                                    String LNF022_DEP_SUB_CD = MapUtils
                                            .getString(lnf022Map,
                                                    "LNF022_DEP_SUB_CD", "");
                                    if (!(Util.equals(LNF022_DEP_CODE, "09") || Util
                                            .equals(LNF022_DEP_SUB_CD, "02"))) {
                                        hasNotExcept = true;
                                    }
                                } else {
                                    // 找不到LNF022 再抓 LNF02O，再沒有就是不能排除
                                    // hasNotExcept = true;
                                    Map<String, Object> lnf02OMap = misLNF02OService
                                            .findByCntrNoFetchOne(LNF020_CONTRACT);

                                    if (lnf02OMap != null
                                            && !lnf02OMap.isEmpty()) {

                                        String LNF02O_DEP_CODE = MapUtils
                                                .getString(lnf02OMap,
                                                        "LNF02O_DEP_CODE", "");
                                        String LNF02O_DEP_SUB_CD = MapUtils
                                                .getString(lnf02OMap,
                                                        "LNF02O_DEP_SUB_CD", "");
                                        if (!(Util
                                                .equals(LNF02O_DEP_CODE, "09") || Util
                                                .equals(LNF02O_DEP_SUB_CD, "02"))) {
                                            hasNotExcept = true;
                                        }
                                    } else {
                                        // 找不到LNF02O 就是不能排除
                                        hasNotExcept = true;
                                    }
                                }

                            } else {
                                // 已經銷戶，抓LNF02O
                                // 抓銷戶前一個月的資料

                                Map<String, Object> lnf02OMap = misLNF02OService
                                        .findByCntrNoFetchOne(LNF020_CONTRACT);

                                if (lnf02OMap != null && !lnf02OMap.isEmpty()) {

                                    // int TCOUNT = Util.parseInt(MapUtils
                                    // .getString(lnf02OMap,
                                    // "TCOUNT", "0"));
                                    //
                                    // if (TCOUNT > 0) {
                                    //
                                    // hasNotExcept = true;
                                    //
                                    // }

                                    String LNF02O_DEP_CODE = MapUtils
                                            .getString(lnf02OMap,
                                                    "LNF02O_DEP_CODE", "");
                                    String LNF02O_DEP_SUB_CD = MapUtils
                                            .getString(lnf02OMap,
                                                    "LNF02O_DEP_SUB_CD", "");
                                    if (!(Util.equals(LNF02O_DEP_CODE, "09") || Util
                                            .equals(LNF02O_DEP_SUB_CD, "02"))) {
                                        hasNotExcept = true;
                                    }
                                } else {
                                    // 找不到LNF02O 就是不能排除
                                    hasNotExcept = true;
                                }

                            }

                            if (hasNotExcept) {
                                // 有不可排除額度，代表非新客戶，則設成N，結束搜尋
                                isNew = "N";
                                break;
                            }

                        }

                        if (hasNotExcept) {
                            // 有不可排除額度，則設成N
                            isNew = "N";

                        } else {
                            // 額度都可排除，則為新客戶，設成Y
                            isNew = "Y";
                        }

                    } else {

                        // 一年內沒有往來
                        isNew = "Y";
                    }

                    processBorrIsNew.put(fullCustIdIsNew, isNew);

                }

                // 判斷是否有抓過0024企業規模與行業對項別
                // 0024
                // 同一借款人資料會一樣，所以每個人抓一次就好*********************************************************
                String fullCustId0024 = CUSTID + "-" + DUPNO;
                if (processBorr0024.containsKey(fullCustId0024)) {
                    Map<String, String> processMap0024 = (Map<String, String>) processBorr0024
                            .get(fullCustId0024);
                    if (processMap0024 != null && !processMap0024.isEmpty()) {
                        clType = Util.trim(MapUtils.getString(processMap0024,
                                "clType"));
                        busCd = Util.trim(MapUtils.getString(processMap0024,
                                "busCd"));

                    } else {
                        errMsg.append("無 " + fullCustId0024 + " 0024資料");
                        return errMsg.toString();
                    }

                } else {

                    // 但還是要先塞processBorr，免得後面相同ID還要再處理
                    Map<String, String> custMap = new TreeMap<String, String>();

                    Map<String, Object> custDataMap = misCustdataService
                            .findCltypeAndBusCdById(CUSTID, DUPNO);
                    if (custDataMap != null && !custDataMap.isEmpty()) {

                        String tClType = Util.trim(MapUtils.getString(
                                custDataMap, "clType"));
                        busCd = Util.trim(MapUtils.getString(custDataMap,
                                "busCd"));

                        clType = this.getCustClass(busCd, tClType);
                    }

                    custMap.put("clType", clType);
                    custMap.put("busCd", busCd);

                    processBorr0024.put(fullCustId0024, custMap);

                }
                // ****************************************************************************************************

                // 非新戶不用寫到L180R42A
                // if (Util.notEquals(isNew, "Y")) {
                // continue;
                // }

                // 個人戶非33/34
                // 不用寫檔(SQL已經有排除、再排除一次)********************************************
                if ((Util.equals(busCd, "060000") || Util.equals(busCd,
                        "130300"))
                        && (Util.notEquals(LNTYPE, "33") && Util.notEquals(
                        LNTYPE, "34"))) {
                    continue;
                }

                String RPTMAINID = Util.trim(MapUtils.getString(dataMap,
                        "RPTMAINID"));

                String DOCTYPE = Util.trim(MapUtils.getString(dataMap,
                        "DOCTYPE"));
                String DOCKIND = Util.trim(MapUtils.getString(dataMap,
                        "DOCKIND"));
                String DOCCODE = Util.trim(MapUtils.getString(dataMap,
                        "DOCCODE"));
                String TYPCD = Util.trim(MapUtils.getString(dataMap, "TYPCD"));
                String CASEBRID = Util.trim(MapUtils.getString(dataMap,
                        "CASEBRID"));
                String CASENO = Util
                        .trim(MapUtils.getString(dataMap, "CASENO"));
                String CASELVL = Util.trim(MapUtils.getString(dataMap,
                        "CASELVL"));
                String CNTRMAINID = Util.trim(MapUtils.getString(dataMap,
                        "CNTRMAINID"));

                String CUSTNAME = Util.trim(MapUtils.getString(dataMap,
                        "CUSTNAME"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String CNTRBRID = Util.trim(MapUtils.getString(dataMap,
                        "CNTRBRID"));
                String PROPERTY = Util.trim(MapUtils.getString(dataMap,
                        "PROPERTY"));
                String SBJPROPERTY = Util.trim(MapUtils.getString(dataMap,
                        "SBJPROPERTY"));
                String HEADITEM1 = Util.trim(MapUtils.getString(dataMap,
                        "HEADITEM1"));
                String HEADITEM2 = Util.trim(MapUtils.getString(dataMap,
                        "HEADITEM2"));
                String HEADITEM3 = Util.trim(MapUtils.getString(dataMap,
                        "HEADITEM3"));
                String CURRENTAPPLYCURR = Util.trim(MapUtils.getString(dataMap,
                        "CURRENTAPPLYCURR"));

                BigDecimal CURRENTAPPLYAMT = Util.equals(Util.trim(MapUtils
                        .getString(dataMap, "CURRENTAPPLYAMT")), "") ? BigDecimal.ZERO
                        : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                        dataMap, "CURRENTAPPLYAMT")));
                String INCAPPLYTOTCURR = Util.trim(MapUtils.getString(dataMap,
                        "INCAPPLYTOTCURR"));
                BigDecimal INCAPPLYTOTAMT = Util.equals(Util.trim(MapUtils
                        .getString(dataMap, "INCAPPLYTOTAMT")), "") ? BigDecimal.ZERO
                        : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                        dataMap, "INCAPPLYTOTAMT")));
                String INCASSTOTCURR = Util.trim(MapUtils.getString(dataMap,
                        "INCASSTOTCURR"));
                BigDecimal INCASSTOTAMT = Util.equals(
                        Util.trim(MapUtils.getString(dataMap, "INCASSTOTAMT")),
                        "") ? BigDecimal.ZERO : Util.parseBigDecimal(Util
                        .trim(MapUtils.getString(dataMap, "INCASSTOTAMT")));
                String LOANTOTCURR = Util.trim(MapUtils.getString(dataMap,
                        "LOANTOTCURR"));
                BigDecimal LOANTOTAMT = Util.equals(
                        Util.trim(MapUtils.getString(dataMap, "LOANTOTAMT")),
                        "") ? BigDecimal.ZERO : Util.parseBigDecimal(Util
                        .trim(MapUtils.getString(dataMap, "LOANTOTAMT")));
                String ASSURETOTCURR = Util.trim(MapUtils.getString(dataMap,
                        "ASSURETOTCURR"));
                BigDecimal ASSURETOTAMT = Util.equals(
                        Util.trim(MapUtils.getString(dataMap, "ASSURETOTAMT")),
                        "") ? BigDecimal.ZERO : Util.parseBigDecimal(Util
                        .trim(MapUtils.getString(dataMap, "ASSURETOTAMT")));

                // 最新一筆L230M01A 已核准額度辦理報送作業
                String STATUS = "";
                Map<String, Object> l230Map = eloandbService
                        .findL230S01LastBySrcMainId(CNTRMAINID, "05O");
                if (l230Map != null && !l230Map.isEmpty()) {
                    STATUS = Util.trim(l230Map.get("NUSEMEMO"));
                }

                // eLoan L180R42A是否已存在
                L180R42A l180r42a = l180r42aDao.findByMainIdAndCntrMainId(
                        RPTMAINID, CNTRMAINID);
                if (l180r42a == null) {
                    l180r42a = new L180R42A();
                    l180r42a.setMainId(RPTMAINID);

                    l180r42a.setCreator("SLMS72");
                    l180r42a.setCreateTime(CapDate.getCurrentTimestamp());

                } else {
                    l180r42a.setUpdater("SLMS72");
                    l180r42a.setUpdateTime(CapDate.getCurrentTimestamp());
                }

                l180r42a.setCntrMainId(CNTRMAINID);
                l180r42a.setEndDate(CapDate.parseDate(ENDDATE));
                l180r42a.setDocType(DOCTYPE);
                l180r42a.setDocKind(DOCKIND);
                l180r42a.setDocCode(DOCCODE);
                l180r42a.setTypCd(TYPCD);
                l180r42a.setCustId(CUSTID);
                l180r42a.setDupNo(DUPNO);
                l180r42a.setCustName(CUSTNAME);
                l180r42a.setCaseBrId(CASEBRID);
                l180r42a.setCaseNo(CASENO);
                l180r42a.setCaseLvl(CASELVL);
                l180r42a.setCntrNo(CNTRNO);
                l180r42a.setCntrBrId(CNTRBRID);
                l180r42a.setProperty(PROPERTY);
                l180r42a.setSbjProperty(SBJPROPERTY);
                l180r42a.setHeadItem1(HEADITEM1);
                l180r42a.setHeadItem2(HEADITEM2);
                l180r42a.setHeadItem3(HEADITEM3);
                l180r42a.setCurrentApplyCurr(CURRENTAPPLYCURR);
                l180r42a.setCurrentApplyAmt(CURRENTAPPLYAMT);
                l180r42a.setIncApplyTotCurr(INCAPPLYTOTCURR);
                l180r42a.setIncApplyTotAmt(INCAPPLYTOTAMT);
                l180r42a.setIncAssTotCurr(INCASSTOTCURR);
                l180r42a.setIncAssTotAmt(INCASSTOTAMT);
                l180r42a.setLoanTotCurr(LOANTOTCURR);
                l180r42a.setLoanTotAmt(LOANTOTAMT);
                l180r42a.setAssureTotCurr(ASSURETOTCURR);
                l180r42a.setAssureTotAmt(ASSURETOTAMT);
                l180r42a.setCltType(clType);
                l180r42a.setBusCode(busCd);
                l180r42a.setDeletedTime(null);
                l180r42a.setEndRate(Util.parseBigDecimal(MapUtils.getString(
                        rate, INCAPPLYTOTCURR, "0")));
                l180r42a.setRateYmd(queryDataYMD);
                l180r42a.setIsNew(isNew);
                l180r42a.setLnType(LNTYPE);
                l180r42a.setStatus(STATUS);
                l180r42a.setCurrEndRate(Util.parseBigDecimal(MapUtils
                        .getString(rate, CURRENTAPPLYCURR, "0")));
                lmsService.save(l180r42a);

            }

        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }

        return errMsg.toString();
    }

    /**
     * J-108-0086 電子文件清理
     *
     * @param request
     */
    @Override
    public String doLmsBatch0019(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        // branch 特定分行 country 特定國別下分行 all 所有分行
        String option = request.getString("option");
        // 超過 ? 年
        // String time = request.getString("time");

        StringBuffer brNos = new StringBuffer("");
        String brList = "";
        StringBuffer times = new StringBuffer("");
        String timeList = "";

        List<Map<String, Object>> dwList = new ArrayList<Map<String, Object>>();

        String adjJsonStr = "";
        JSONObject jsonAdj = new JSONObject();

        // 複數以 , 分隔
        if (Util.equals("branch", option)) {
            // "0A2":"20","0A1":"5" =>設定檔中沒有加{ }符號，因為設定檔儲存再開啟會變成顯示為Object
            adjJsonStr = Util.trim(lmsService
                    .getSysParamDataValue("COM_ELDF_BRANCH"));
            if (Util.equals(adjJsonStr, "")) {
                return errMsg.append("參數錯誤").toString();
            }
            // 程式內再加{ }符號
            jsonAdj = JSONObject.fromObject("{" + adjJsonStr + "}");
            if (jsonAdj == null) {
                return errMsg.append("參數錯誤").toString();
            }
            for (Object key : jsonAdj.keySet()) {
                if (Util.equals(Util.trim(brNos.toString()), "")) {
                    brNos.append(key.toString());
                    times.append(jsonAdj.optString(key.toString()));
                } else {
                    brNos.append(",").append(key.toString());
                    times.append(",").append(jsonAdj.optString(key.toString()));
                }
            }
            brList = brNos.toString();
            timeList = times.toString();
        } else if (Util.equals("country", option)) {
            // "US":"20" =>設定檔中沒有加{ }符號，因為設定檔儲存再開啟會變成顯示為Object
            adjJsonStr = Util.trim(lmsService
                    .getSysParamDataValue("COM_ELDF_COUNTRY"));
            if (Util.equals(adjJsonStr, "")) {
                return errMsg.append("參數錯誤").toString();
            }
            // 程式內再加{ }符號
            jsonAdj = JSONObject.fromObject("{" + adjJsonStr + "}");
            if (jsonAdj == null) {
                return errMsg.append("參數錯誤").toString();
            }

            StringBuffer countrys = new StringBuffer("");
            String countryList = "";
            for (Object key : jsonAdj.keySet()) {
                if (Util.equals(Util.trim(brNos.toString()), "")) {
                    countrys.append(key.toString());
                } else {
                    countrys.append(",").append(key.toString());
                }
            }
            countryList = countrys.toString();

            for (String xx : countryList.split(",")) {
                String txx = Util.equals(Util.trim(xx), "NULL") ? "" : xx;
                if (Util.isNotEmpty(txx)) {
                    List<IBranch> iBranchs = branchService
                            .getBranchByCountryType(txx);
                    for (IBranch ibranch : iBranchs) {
                        String brno = Util.trim(ibranch.getBrNo());
                        if (Util.notEquals(brno, "")) {
                            if (Util.equals(Util.trim(brNos.toString()), "")) {
                                brNos.append(brno);
                                times.append(jsonAdj.optString(txx));
                            } else {
                                brNos.append(",").append(brno);
                                times.append(",")
                                        .append(jsonAdj.optString(txx));
                            }
                        }
                    }
                }
            }
            brList = brNos.toString();
            timeList = times.toString();
        } else {
            List<IBranch> iBranchs = branchService.getAllBranch();
            StringBuffer brs = new StringBuffer("");
            for (IBranch ibranch : iBranchs) {
                String brno = Util.trim(ibranch.getBrNo());
                if (Util.notEquals(brno, "")) {
                    if (Util.equals(Util.trim(brs.toString()), "")) {
                        brs.append(brno);
                        times.append("20");
                    } else {
                        brs.append(",").append(brno);
                        times.append(",").append("20");
                    }
                }
            }
            brList = brs.toString();
            timeList = times.toString();
        }

        try {
            String zero = "00000000000";
            String brId = "";
            String custId = "";
            String dupNo = "";
            String custName = "";
            Date CLOSE_DATE = new Date();
            String dataYM = CapDate.getCurrentDate("yyyy-MM");

            // INSERT INTO L140MM5A 依分行塞入
            String[] branchNo = brList.split(",");
            String[] overTime = timeList.split(",");
            for (int b = 0; b < brList.split(",").length; b++) {
                int CNT = 0;

                String mainId = IDGenerator.getUUID();
                L140MM5A l140mm5a = new L140MM5A();
                l140mm5a.setMainId(mainId);
                l140mm5a.setRandomCode(IDGenerator.getRandomCode());
				// UPGRADE: 待確認，URL怎麼取得
				// l140mm5a.setDocURL(LMS7700M01Page.class.getAnnotation(MountPath.class).path());
                l140mm5a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
                l140mm5a.setCreator("system");
                l140mm5a.setCreateTime(CapDate.getCurrentTimestamp());

                l140mm5a.setDataYM(dataYM);
                l140mm5a.setOwnBrId(branchNo[b]);

                // dw
                dwList = dwdbBASEService.getElDeleteFileListSingle(overTime[b],
                        branchNo[b]);

                if (dwList != null && Util.isNotEmpty(dwList)
                        && dwList.size() > 0) {
                    for (Map<String, Object> map : dwList) {
                        brId = map.get("BR_CD").toString();
                        String CUST_KEY = (Util.trim(map.get("CUST_KEY")
                                .toString()) + zero).substring(0, 11);
                        custId = CUST_KEY.substring(0, 10);
                        dupNo = CUST_KEY.substring(10);
                        custName = map.get("CUST_NM").toString();
                        CLOSE_DATE = Util.isEmpty(map.get("CMST_CLOSE_DATE")
                                .toString()) ? null : CapDate.parseDate(map
                                .get("CMST_CLOSE_DATE").toString());

                        List<String> have = new ArrayList<String>();
                        List<Map<String, Object>> elList = new ArrayList<Map<String, Object>>();
                        elList = eloandbService.doLmsBatch0019(custId, dupNo,
                                brId);
                        if (elList != null && Util.isNotEmpty(elList)
                                && elList.size() > 0) {
                            String CES = "";
                            String CMS = "";
                            String COL = "";
                            String LMS = "";
                            String RPS = "";

                            for (Map<String, Object> elMap : elList) {
                                String sys = elMap.get("SYS_SCHEMA").toString();
                                if (Util.notEquals("0", elMap.get("CNT"))) {
                                    have.add(sys);
                                    if (Util.equals("CES", sys)) {
                                        CES = "Y";
                                    } else if (Util.equals("CMS", sys)
                                            || Util.equals("CMH", sys)) {
                                        CMS = "Y";
                                    } else if (Util.equals("COL", sys)) {
                                        COL = "Y";
                                    } else if (Util.equals("LMS", sys)) {
                                        LMS = "Y";
                                    } else if (Util.equals("RPS", sys)) {
                                        RPS = "Y";
                                    }
                                }
                            }

                            if (have != null && have.size() > 0) {
                                L140MM5C l140mm5c = new L140MM5C();
                                l140mm5c.setMainId(mainId);
                                l140mm5c.setOwnBrId(brId);
                                l140mm5c.setCustId(custId);
                                l140mm5c.setDupNo(dupNo);
                                l140mm5c.setCustName(custName);
                                l140mm5c.setDataFrom("1"); // 1:系統 2:人工
                                l140mm5c.setCloseDate(CLOSE_DATE);
                                l140mm5c.setCreator("system");
                                l140mm5c.setCreateTime(CapDate
                                        .getCurrentTimestamp());

                                l140mm5c.setCes(CES);
                                l140mm5c.setCms(CMS);
                                l140mm5c.setCol(COL);
                                l140mm5c.setLms(LMS);
                                l140mm5c.setRps(RPS);
                                l140mm5cDao.save(l140mm5c);

                                CNT++;
                            }
                        }
                    }
                }

                l140mm5a.setCnt(CNT);
                l140mm5aDao.save(l140mm5a);

                // lmsService.sendCommonEmail(mainId, "1");
                lmsService.sendCommonEmail(l140mm5a, "1");
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }

        return errMsg.toString();
    }

    /**
     * J-108-0116 企金處共同行銷
     *
     * @param request
     */
    @Override
    public String doLmsBatch0020(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        Map<String, CapAjaxFormResult> codeTypes = codetypeService
                .findByCodeType(new String[] { "lms7800_typeA",
                        "lms7800_typeB", "lms7800_typeC" });

        try {
            StringBuffer subject = new StringBuffer();
            subject.append("【提醒！請維護簽報案件共同行銷資訊】");
            List<Map<String, Object>> userList = eloandbService
                    .notifyMail_LMS180R46("1", "");
            for (Map<String, Object> userMap : userList) {
                String infoAppraiser = Util.trim(MapUtils.getString(userMap,
                        "INFOAPPRAISER", ""));
                String caseBrId = Util.trim(MapUtils.getString(userMap,
                        "CASEBRID", ""));

                List<Map<String, Object>> caseList = eloandbService
                        .notifyMail_LMS180R46("2", infoAppraiser);
                StringBuffer body = new StringBuffer();
                for (Map<String, Object> caseMap : caseList) {
                    String caseNo = Util.trim(MapUtils.getString(caseMap,
                            "CASENO", ""));
                    String cntrNo = Util.trim(MapUtils.getString(caseMap,
                            "CNTRNO", ""));
                    String type = Util.trim(MapUtils.getString(caseMap, "TYPE",
                            ""));
                    int cnt = MapUtils.getInteger(caseMap, "CNT", 0);
                    body.append("額度序號：")
                            .append(cntrNo)
                            .append("，")
                            .append("共同行銷類型：")
                            .append(Util.trim(codeTypes
                                    .get("lms7800_type" + type).get(type)
                                    .toString())).append("，").append("案件號碼：")
                            .append(caseNo).append("，").append("已通知次數：")
                            .append(++cnt).append("\r\n");

                    String oid = Util.trim(MapUtils.getString(caseMap, "OID",
                            ""));

                    L180R46A l180r46a = l180r46aDao.findByOid(oid);
                    l180r46a.setCnt(cnt);
                    l180r46a.setNotifyTime(CapDate.getCurrentTimestamp());
                    l180r46aDao.save(l180r46a);
                }

                // Send E-Mail
                String hostAddr = sysParameterService
                        .getParamValue(SysParamConstants.MAIL_ADDRESS_HOST);
                boolean isTestEmail = true; // 是否為測試信件

                if (Util.equals(Util.trim(hostAddr), "")) {
                    isTestEmail = "true".equals(PropUtil
                            .getProperty("isTestEmail")) ? true : false; // 是否為測試信件
                } else {
                    if (StringUtils.indexOf(hostAddr, "@notes.") >= 0) { // Production
                        isTestEmail = false;
                    } else {
                        isTestEmail = true;
                    }
                }

                // User 資料
                ElsUser eluser = userInfoService.getUser(infoAppraiser);
                boolean userNotExist = false;
                if (eluser == null || Util.isEmpty(eluser)) {
                    userNotExist = true;
                }

                List<String> recv = new ArrayList<String>();
                recv.clear();
                if (isTestEmail) {
                    recv.add("<EMAIL>");
                } else if (userNotExist) { // 不在 COM.BELSUSR 中
                    recv.add(caseBrId + "<EMAIL>");
                } else if (Util.notEquals(eluser.getBrno(), caseBrId)) { // 是否還在該單位
                    recv.add(caseBrId + "<EMAIL>");
                } else if (Util.equals("Y", eluser.getLeaveFlag())) { // 是否在職
                    recv.add(caseBrId + "<EMAIL>");
                } else if (Util.isNotEmpty(infoAppraiser)) {
                    String sendUsrId = infoAppraiser.substring(1);
                    recv.add(sendUsrId + hostAddr);
                } else {
                    recv.add(caseBrId + "<EMAIL>");
                }

                int recvSize = recv.size();
                String[] aRecv = new String[recvSize];
                Object[] oRecv = recv.toArray();
                // 將傳送者Email型別Object[]改為String[]
                for (int j = 0; j < recvSize; j++) {
                    aRecv[j] = Util.trim(oRecv[j]);
                }
                logger.debug("toAddr:{},subject:{},body:{}", new Object[] {
                        aRecv, subject.toString(), body.toString() });
                emailClient.send(true, null, aRecv, subject.toString(),
                        body.toString());
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }

        return errMsg.toString();
    }

    /**
     * J-108-0116 企金處共同行銷 塞 MIS.SYNBANK 資料
     */
    @Override
    @NonTransactional
    public String doLmsBatch0021() {
        StringBuffer errMsg = new StringBuffer("");
        try {
            misdbBASEService.doLmsBatch0021();
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();
    }

    @Override
    public void doLmsBatch0022() {
        misdbBASEService.doLmsBatch0022();
    }

    /**
     * 取得客戶類別--邏輯同a-Loan新彙集檔
     *
     * @param buscd
     *            行業對象別代碼0024
     * @param cltype
     *            企業規模0024
     * @return
     */
    public String getCustClass(String buscd, String cltype) {

        String custClass = "";

        if (Util.equals(Util.getLeftStr(buscd, 2), "02")) {
            custClass = "5";
        } else if (Util.equals(Util.getLeftStr(buscd, 2), "03")) {
            custClass = "6";
        } else if (Util.equals(Util.getLeftStr(buscd, 2), "05")) {
            custClass = "7";
        } else if (Util.equals(Util.getLeftStr(buscd, 2), "06")) {
            custClass = "2";
        } else {
            if (Util.equals(cltype, "1")) {
                custClass = "1";
            } else if (Util.equals(cltype, "2")) {
                custClass = "8";
            } else {
                custClass = "4";
            }
        }

        if (Util.equals(buscd, "130300")) {
            custClass = "2";
        }

        return custClass;

    }

    /**
     * J-104-0XXX-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別 新舊授信對象別資料轉換
     */
    @Override
    @NonTransactional
    public String doLmsBatch0023(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");

        String dataStartDate = Util.trim(request.getString("dataStartDate"));
        String dataEndDate = Util.trim(request.getString("dataEndDate"));

        if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
            errMsg.append("無法取得完整資料期間 " + dataStartDate + "~" + dataEndDate
                    + " ");
            System.out.println(errMsg.toString());
            return errMsg.toString();

        }

        List<Map<String, Object>> listL120m01a = eloandbService
                .findL120m01aByEndDate(dataStartDate, dataEndDate);

        if (listL120m01a != null && !listL120m01a.isEmpty()) {

            for (Map<String, Object> l120Map : listL120m01a) {
                String docType = Util.trim(MapUtils.getObject(l120Map,
                        "DOCTYPE"));
                String ELF447N_UNID = "";
                String ELF447N_CUSTID = "";
                String ELF447N_DUPNO = "";
                String ELF447N_ENDDATE = null;
                String ELF447N_RGTCURR = "";
                BigDecimal ELF447N_RGTAMT = BigDecimal.ZERO;
                BigDecimal ELF447N_RGTUNIT = BigDecimal.ONE;

                ELF447N_UNID = Util.trim(MapUtils.getObject(l120Map, "MAINID"));
                ELF447N_ENDDATE = Util.trim(MapUtils.getObject(l120Map,
                        "ENDDATE"));

                if (Util.equals(docType, "2")) {
                    // 消金

                    ELF447N_CUSTID = "";
                    ELF447N_DUPNO = "";
                    ELF447N_RGTCURR = "";
                    ELF447N_RGTAMT = BigDecimal.ZERO;
                    ELF447N_RGTUNIT = BigDecimal.ONE;

                    List<Map<String, Object>> rows447n = misELF447nService
                            .findByUnidAndCustId(ELF447N_UNID, ELF447N_CUSTID,
                                    ELF447N_DUPNO);
                    if (rows447n != null && !rows447n.isEmpty()) {
                        misELF447nService.updateByUnidAndCustId(ELF447N_UNID,
                                ELF447N_CUSTID, ELF447N_DUPNO, ELF447N_ENDDATE,
                                ELF447N_RGTCURR, ELF447N_RGTAMT,
                                ELF447N_RGTUNIT);
                    }
                } else {
                    // 企金

                    List<L120S01B> listL120s01b = l120s01bDao
                            .findByMainId(ELF447N_UNID);

                    if (listL120s01b != null && !listL120s01b.isEmpty()) {

                        for (L120S01B l120s01b : listL120s01b) {
                            ELF447N_CUSTID = Util.trim(l120s01b.getCustId());
                            ELF447N_DUPNO = Util.trim(l120s01b.getDupNo());

                            if (Util.equals(ELF447N_CUSTID, "")
                                    || Util.equals(ELF447N_DUPNO, "")) {
                                errMsg.append("無法取得企金簽報書借款人基本資料: "
                                        + ELF447N_UNID + "：" + ELF447N_CUSTID
                                        + " " + ELF447N_DUPNO + " ");
                                System.out.println(errMsg.toString());
                                return errMsg.toString();

                            }

                            if (l120s01b != null) {
                                ELF447N_RGTCURR = Util.trim(l120s01b
                                        .getCptlCurr());
                                ELF447N_RGTAMT = Util.parseBigDecimal(l120s01b
                                        .getCptlAmt() == null ? 0 : l120s01b
                                        .getCptlAmt());
                                ELF447N_RGTUNIT = Util.parseBigDecimal(l120s01b
                                        .getCptlUnit() == null ? 1 : l120s01b
                                        .getCptlUnit());
                            }

                            if (ELF447N_RGTUNIT == null
                                    || BigDecimal.ZERO.compareTo(Util
                                    .parseBigDecimal(l120s01b
                                            .getCptlUnit())) == 0) {
                                ELF447N_RGTUNIT = BigDecimal.ONE;
                            }

                            // 因為國內與海外簽報書都會寫MIS.ELF506，所以以MIS.ELF506當頭
                            List<Map<String, Object>> rows447n = misELF447nService
                                    .findByUnidAndCustId(ELF447N_UNID,
                                            ELF447N_CUSTID, ELF447N_DUPNO);
                            if (rows447n != null && !rows447n.isEmpty()) {
                                misELF447nService.updateByUnidAndCustId(
                                        ELF447N_UNID, ELF447N_CUSTID,
                                        ELF447N_DUPNO, ELF447N_ENDDATE,
                                        ELF447N_RGTCURR, ELF447N_RGTAMT,
                                        ELF447N_RGTUNIT);
                            }

                        }

                    }

                }

            }

        }

        return errMsg.toString();
    }

    /**
     * J-108-0242_05097_B1001 Web e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
     */
    @Override
    @NonTransactional
    public String doLmsBatch0024(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");

        String dataStartDate = Util.trim(request.getString("dataStartDate"));
        String dataEndDate = Util.trim(request.getString("dataEndDate"));

        if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
            errMsg.append("無法取得完整資料期間 " + dataStartDate + "~" + dataEndDate
                    + " ");
            System.out.println(errMsg.toString());
            return errMsg.toString();

        }

        // 核准年度(民國年)
        String tYY = (LMSUtil.checkSubStr(dataStartDate, 0, 4)) ? CapDate
                .convertDateToTaiwanYear(dataStartDate.substring(0, 4))
                : UtilConstants.Mark.SPACE;
        // 核准月
        String tMM = (LMSUtil.checkSubStr(dataEndDate, 5, 7)) ? dataEndDate
                .substring(5, 7) : UtilConstants.Mark.SPACE;

        List<Map<String, Object>> listElcsecnt = misElcsecntService
                .selectElcsecnt_doLmsBatch0024(tYY, tMM);

        Map<String, Object> maxData = dwdbBASEService
                .findOTS_CSFACT_LIST_maxCynMn();
        String maxCycMn = "";

        if (maxData != null && !maxData.isEmpty()) {
            maxCycMn = Util.trim(MapUtils.getString(maxData, "CYC_MN", ""));
        }

        if (listElcsecnt != null && !listElcsecnt.isEmpty()) {

            for (Map<String, Object> elf404Map : listElcsecnt) {

                String BRNO = "";
                String CTYPE = "";
                String CASENO = "";
                String CNTRNO = null;
                String CASEDEPT = "";
                String NEWCUST = "";
                String APPRYY = "";
                String APPRMM = "";

                // 分行別CaseBrId
                BRNO = Util.trim(MapUtils.getObject(elf404Map, "BRNO"));

                // 授權內/授權外 DocKind
                CTYPE = Util.trim(MapUtils.getObject(elf404Map, "CTYPE"));

                // 案號/審核書流水號 CaseNo
                CASENO = Util.trim(MapUtils.getObject(elf404Map, "CASENO"));

                // 額度序號
                CNTRNO = Util.trim(MapUtils.getObject(elf404Map, "CNTRNO"));

                // 案件隸屬部門 DocType
                // 1. 企金部
                // 2. 個金部
                CASEDEPT = Util.trim(MapUtils.getObject(elf404Map, "CASEDEPT"));

                APPRYY = Util.trim(MapUtils.getObject(elf404Map, "APPRYY"));

                APPRMM = Util.trim(MapUtils.getObject(elf404Map, "APPRMM"));

                // 分行別
                // String tBrno = Util.trim(l120m01a.getCaseBrId());
                // // 案件隸屬單位 ( 1.法金處案件 2.個金處案件 )
                // String tCaseDept = l120m01a.getDocType();
                // // 案號
                // String tCaseNo = LMSUtil.getUploadCaseNo(l120m01a, false);
                // String tCaseNo2 = LMSUtil.getUploadCaseNo(l120m01a, true);
                // // 授權類別
                // String tCType = l120m01a.getDocKind(-);

                if (Util.equals(BRNO, "") || Util.equals(CNTRNO, "")
                        || Util.equals(CTYPE, "") || Util.equals(CASENO, "")
                        || Util.equals(CASEDEPT, "")) {
                    continue;
                }

                BigDecimal caseSeq = Util.equals(CASENO, "") ? null : Util
                        .parseBigDecimal(Util.getRightStr(CASENO, 5));

                List<Map<String, Object>> listL120m01a = eloandbService
                        .doLmsBatch0024_01(CNTRNO, BRNO, dataStartDate,
                                dataEndDate, CTYPE, CASEDEPT, caseSeq);

                String mainId = "";
                String custId = "";
                String dupNo = "";
                String CASEDATE = "";

                if (listL120m01a != null && !listL120m01a.isEmpty()) {

                    for (Map<String, Object> l120Map : listL120m01a) {
                        mainId = Util.trim(MapUtils.getObject(l120Map,
                                "RPTMAINID"));
                        custId = Util.trim(MapUtils
                                .getObject(l120Map, "CUSTID"));
                        dupNo = Util.trim(MapUtils.getObject(l120Map, "DUPNO"));
                        CASEDATE = Util.trim(MapUtils.getObject(l120Map,
                                "CASEDATE"));
                        if (Util.equals(mainId, "") || Util.equals(custId, "")
                                || Util.equals(dupNo, "")) {
                            continue;
                        }
                        break;
                    }

                } else {
                    // 找不到簽報書........放棄更新這筆

                    continue;
                }

                if (Util.equals(mainId, "") || Util.equals(custId, "")
                        || Util.equals(dupNo, "")) {
                    continue;
                }

                if (Util.equals(CASEDEPT, "2")) {
                    // 消金
                    C120S01A c120s01a = c120s01aDao.findByUniqueKey(mainId,
                            custId, dupNo);
                    if (c120s01a != null) {
                        NEWCUST = Util.trim(c120s01a.getNewCustFlag());
                    }
                } else {
                    // 企金
                    L120S01A l120s01a = l120s01aDao.findByUniqueKey(mainId,
                            custId, dupNo);
                    if (l120s01a != null) {
                        NEWCUST = Util.trim(l120s01a.getNewCustFlag());
                    }
                }

                if (Util.equals(NEWCUST, "")) {
                    // 還是空白簽報書沒有註記資料
                    // 改找DW
                    // 用簽案日期的上個月
                    // 2019-09-02 -> 2019-09-01
                    String tCaseDate = Util.getLeftStr(CASEDATE, 7) + "-01";
                    // 2019-09-01 -> 2019-08-01
                    String newCaseDate = CapDate.formatDate(
                            CapDate.addMonth(Util.parseDate(tCaseDate), -1),
                            "yyyy-MM-dd");

                    // 但有可能DW還沒有8月資料，則改採DW最新資料日期
                    // ELOAN 2019-08-01 > DW 2019-07-01 ->改用 DW
                    // 2019-07-01
                    if (LMSUtil.cmpDate(Util.parseDate(newCaseDate), ">",
                            Util.parseDate(maxCycMn))) {
                        newCaseDate = maxCycMn;
                    }

                    if (Util.notEquals(newCaseDate, "")) {
                        Map<String, Object> custData = dwdbBASEService
                                .findOTS_CSFACT_LIST_by_custKey(custId, dupNo,
                                        newCaseDate);
                        if (custData == null || custData.isEmpty()) {
                            // 沒資料代表最近一年都沒有往來
                            NEWCUST = "Y";
                        } else {
                            NEWCUST = "N";
                        }
                    }
                }

                if (Util.notEquals(NEWCUST, "")) {
                    // UPDATE ELCSECNT

                    misElcsecntService.updateElcsecnt_doLmsBatch0024(BRNO,
                            APPRYY, APPRMM, CTYPE, CASEDEPT, CASENO, CNTRNO,
                            NEWCUST);

                }

            }

        }

        return errMsg.toString();
    }

    /**
     * M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
     */
    @Override
    @NonTransactional
    public String doLmsBatch0025(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");

        String dataStartDate = Util.trim(request.getString("dataStartDate"));
        String dataEndDate = Util.trim(request.getString("dataEndDate"));

        if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
            final String DATE_FORMAT_STR = "yyyy-MM-dd";
            dataStartDate = CapDate.formatDate(LMSUtil.getExMonthFirstDay(-1),
                    DATE_FORMAT_STR);
            dataEndDate = CapDate.formatDate(LMSUtil.getExMonthLastDay(-1),
                    DATE_FORMAT_STR);

        }

        // 931.931合併flag
        boolean startFlag = "Y".equals(sysParameterService
                .getParamValue("J1100104_StartFlag"));

        // 1.授信審查(企、消金) doLmsBatch0025_01
        if (true) {
            List<Map<String, Object>> list01 = eloandbService
                    .doLmsBatch0025_01(dataStartDate, dataEndDate);
            String CNTRNO = "";
            String CASEBRID = "";
            String BRNAME = "";
            String CASETYPE = "";
            String CASELVL = "";
            String COLLTYPE = "";
            String CASENO = "";
            String ENDDATE = "";
            String MAINID = "";
            String CNTRMAINID = "";
            String AREABRID = "";
            String APPROVEDATE = "";
            String gMainId = "";
            String SNOKIND = "";
            String CURRENTAPPLYCURR = "";
            BigDecimal CURRENTAPPLYAMT = BigDecimal.ZERO;

            if (list01 != null && !list01.isEmpty()) {
                for (Map<String, Object> map01 : list01) {
                    CNTRNO = MapUtils.getString(map01, "CNTRNO", "");
                    CASEBRID = MapUtils.getString(map01, "CASEBRID", "");
                    BRNAME = MapUtils.getString(map01, "BRNAME", "");
                    CASETYPE = MapUtils.getString(map01, "CASETYPE", "");
                    CASELVL = MapUtils.getString(map01, "CASELVL", "");
                    COLLTYPE = MapUtils.getString(map01, "COLLTYPE", "");
                    CASENO = MapUtils.getString(map01, "CASENO", "");
                    ENDDATE = MapUtils.getString(map01, "ENDDATE", "");
                    MAINID = MapUtils.getString(map01, "MAINID", "");
                    CNTRMAINID = MapUtils.getString(map01, "CNTRMAINID", "");

                    // 931與932合併
                    AREABRID = MapUtils.getString(map01, "AREABRID", "");
                    if (startFlag && "932".equals(AREABRID)) {
                        AREABRID = "931";
                    }

                    APPROVEDATE = MapUtils.getString(map01, "APPROVEDATE", "");
                    SNOKIND = MapUtils.getString(map01, "SNOKIND", "");
                    CURRENTAPPLYCURR = MapUtils.getString(map01,
                            "CURRENTAPPLYCURR", "");
                    CURRENTAPPLYAMT = Util.parseBigDecimal(Util.trim(MapUtils
                            .getString(map01, "CURRENTAPPLYAMT", "0")));

                    if (Util.notEquals(gMainId, MAINID)) {
                        // 刪除DW 資料日期為dataStartDate~dataEndDate之資料
                        dwdbBASEService.delete_DW_COSTSHARE_LMS01(MAINID);
                        gMainId = MAINID;
                    }

                    // 上傳DW BY 額度
                    dwdbBASEService.DW_COSTSHARE_LMS01_INSERT(CNTRNO, CASEBRID,
                            CASETYPE, CASELVL, COLLTYPE, CASENO, ENDDATE,
                            MAINID, CNTRMAINID, AREABRID, APPROVEDATE,
                            "LMS092", null);

                    // M-108-0296_05097_B1004 Web e-Loan配合總處經費分攤提供所需資料
                    // 上傳聯貸比率***************************************************************

                    dwdbBASEService.delete_DW_COSTSHARE_LMS04(CNTRMAINID);

                    List<L140M01E> l140m01es = l140m01eDao
                            .findByMainId(CNTRMAINID);
                    if (l140m01es != null && !l140m01es.isEmpty()) {
                        for (L140M01E l140m01e : l140m01es) {
                            if (l140m01e != null) {
                                String SHAREBRID = l140m01e.getShareBrId();
                                String SHAREFLAG = l140m01e.getShareFlag();
                                BigDecimal SHARERATE1 = l140m01e
                                        .getShareRate1();
                                BigDecimal SHARERATE2 = l140m01e
                                        .getShareRate2();
                                BigDecimal SHAREAMT = l140m01e.getShareAmt();
                                BigDecimal TOTALAMT = l140m01e.getTotalAmt();
                                String SHARENO = l140m01e.getShareNo();

                                dwdbBASEService.DW_COSTSHARE_LMS04_INSERT(
                                        CNTRMAINID, CNTRNO, SHAREBRID,
                                        SHAREFLAG, SHARERATE1, SHARERATE2,
                                        SHAREAMT, TOTALAMT, SHARENO, SNOKIND,
                                        CURRENTAPPLYCURR, CURRENTAPPLYAMT,
                                        "LMS092", null);

                            }
                        }
                    }

                }
            }

        }

        // 2.授信覆審(企金) doLmsBatch0025_02
        if (true) {
            List<Map<String, Object>> list02 = eloandbService
                    .doLmsBatch0025_02(dataStartDate, dataEndDate);
            String CNTRNO = "";
            String OWNBRID = "";
            String BRNAME = "";
            String CASETYPE = "";
            String CASELVL = "";
            String COLLTYPE = "";
            String RETRIALDATE = "";
            String PROJECTNO = "";
            String ENDDATE = "";
            String CASENO = "";
            String MAINID = "";
            String RPTMAINID = "";
            String gMainId = "";
            if (list02 != null && !list02.isEmpty()) {
                for (Map<String, Object> map02 : list02) {
                    CNTRNO = MapUtils.getString(map02, "CNTRNO", "");

                    // 931與932合併
                    OWNBRID = MapUtils.getString(map02, "OWNBRID", "");
                    if (startFlag && "932".equals(OWNBRID)) {
                        OWNBRID = "931";
                    }

                    BRNAME = MapUtils.getString(map02, "BRNAME", "");
                    CASETYPE = MapUtils.getString(map02, "CASETYPE", "");
                    CASELVL = MapUtils.getString(map02, "CASELVL", "");
                    COLLTYPE = MapUtils.getString(map02, "COLLTYPE", "");
                    RETRIALDATE = MapUtils
                            .getString(map02, "RETRIALDATE", null);

                    PROJECTNO = MapUtils.getString(map02, "PROJECTNO", "");
                    ENDDATE = MapUtils.getString(map02, "ENDDATE", null);
                    CASENO = MapUtils.getString(map02, "CASENO", "");
                    MAINID = MapUtils.getString(map02, "MAINID", "");
                    RPTMAINID = MapUtils.getString(map02, "RPTMAINID", "");

                    if (Util.notEquals(gMainId, MAINID)) {
                        // 刪除DW 資料日期為dataStartDate~dataEndDate之資料
                        dwdbBASEService.delete_DW_COSTSHARE_LMS02(MAINID);
                        gMainId = MAINID;
                    }

                    // 上傳DW
                    dwdbBASEService.DW_COSTSHARE_LMS02_INSERT(CNTRNO, OWNBRID,
                            CASETYPE, CASELVL, COLLTYPE, RETRIALDATE,
                            PROJECTNO, ENDDATE, CASENO, MAINID, RPTMAINID,
                            "LMS092", null);
                }
            }

        }

        // 3.授信覆審(消金) doLmsBatch0025_03
        if (true) {
            List<Map<String, Object>> list03 = eloandbService
                    .doLmsBatch0025_03(dataStartDate, dataEndDate);
            String CNTRNO = "";
            String OWNBRID = "";
            String BRNAME = "";
            String CASETYPE = "";
            String CASELVL = "";
            String COLLTYPE = "";
            String RETRIALDATE = "";
            String PROJECTNO = "";
            String ENDDATE = "";
            String CASENO = "";
            String MAINID = "";
            String RPTMAINID = "";
            String gMainId = "";
            if (list03 != null && !list03.isEmpty()) {
                for (Map<String, Object> map03 : list03) {
                    CNTRNO = MapUtils.getString(map03, "CNTRNO", "");

                    // 931與932合併
                    OWNBRID = MapUtils.getString(map03, "OWNBRID", "");
                    if (startFlag && "932".equals(OWNBRID)) {
                        OWNBRID = "931";
                    }

                    BRNAME = MapUtils.getString(map03, "BRNAME", "");
                    CASETYPE = MapUtils.getString(map03, "CASETYPE", "");
                    CASELVL = MapUtils.getString(map03, "CASELVL", "");
                    COLLTYPE = MapUtils.getString(map03, "COLLTYPE", "");
                    RETRIALDATE = MapUtils
                            .getString(map03, "RETRIALDATE", null);

                    PROJECTNO = MapUtils.getString(map03, "PROJECTNO", "");
                    ENDDATE = MapUtils.getString(map03, "ENDDATE", null);
                    CASENO = MapUtils.getString(map03, "CASENO", "");
                    MAINID = MapUtils.getString(map03, "MAINID", "");
                    RPTMAINID = MapUtils.getString(map03, "RPTMAINID", "");

                    if (Util.notEquals(gMainId, MAINID)) {
                        // 刪除DW 資料日期為dataStartDate~dataEndDate之資料
                        dwdbBASEService.delete_DW_COSTSHARE_LMS02(MAINID);
                        gMainId = MAINID;
                    }

                    // 上傳DW
                    dwdbBASEService.DW_COSTSHARE_LMS02_INSERT(CNTRNO, OWNBRID,
                            CASETYPE, CASELVL, COLLTYPE, RETRIALDATE,
                            PROJECTNO, ENDDATE, CASENO, MAINID, RPTMAINID,
                            "LMS092", null);
                }
            }

        }

        // 4.授信審查(企、消金) by 簽報書 doLmsBatch0025_04
        if (true) {
            List<Map<String, Object>> list01 = eloandbService
                    .doLmsBatch0025_04(dataStartDate, dataEndDate);
            String CASENO = "";
            String CASEBRID = "";
            String BRNAME = "";
            String CASETYPE = "";
            String CASELVL = "";
            String DOCCODE = "";
            String ENDDATE = "";
            String MAINID = "";
            String AREABRID = "";
            String APPROVEDATE = "";
            String gMainId = "";

            if (list01 != null && !list01.isEmpty()) {
                for (Map<String, Object> map01 : list01) {

                    CASENO = MapUtils.getString(map01, "CASENO", "");
                    CASEBRID = MapUtils.getString(map01, "CASEBRID", "");
                    BRNAME = MapUtils.getString(map01, "BRNAME", "");
                    CASETYPE = MapUtils.getString(map01, "CASETYPE", "");
                    CASELVL = MapUtils.getString(map01, "CASELVL", "");
                    DOCCODE = MapUtils.getString(map01, "DOCCODE", "");
                    ENDDATE = MapUtils.getString(map01, "ENDDATE", "");
                    MAINID = MapUtils.getString(map01, "MAINID", "");

                    // 931與932合併
                    AREABRID = MapUtils.getString(map01, "AREABRID", "");
                    if (startFlag && "932".equals(AREABRID)) {
                        AREABRID = "931";
                    }

                    APPROVEDATE = MapUtils.getString(map01, "APPROVEDATE", "");

                    if (Util.notEquals(gMainId, MAINID)) {
                        // 刪除DW 資料日期為dataStartDate~dataEndDate之資料
                        dwdbBASEService.delete_DW_COSTSHARE_LMS03(MAINID);
                        gMainId = MAINID;
                    }

                    // 上傳DW
                    dwdbBASEService.DW_COSTSHARE_LMS03_INSERT(CASENO, CASEBRID,
                            CASETYPE, CASELVL, DOCCODE, ENDDATE, MAINID,
                            AREABRID, APPROVEDATE, "LMS092", null);

                }
            }

        }

        return errMsg.toString();
    }

    /**
     * Oracle reject list 測試
     */
    @Override
    @NonTransactional
    public String doLmsBatch0026(JSONObject request) {
        /*
         * StringBuffer errMsg = new StringBuffer(""); String type =
         * request.getString("type");
         *
         * String dataStartDate = Util.trim(request.getString("dataStartDate"));
         * String dataEndDate = Util.trim(request.getString("dataEndDate"));
         *
         * if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
         * final String DATE_FORMAT_STR = "yyyy-MM-dd"; dataStartDate =
         * CapDate.formatDate(LMSUtil.getExMonthFirstDay(-1), DATE_FORMAT_STR);
         * dataEndDate = CapDate.formatDate(LMSUtil.getExMonthLastDay(-1),
         * DATE_FORMAT_STR);
         *
         * }
         *
         * // {"CALLING_SYSTEM":"06","TRANSACTION_ID":"LMS","MAINID":
         * "0ef8bacdddff41b6bff6bd5e6b8a90e5"
         * ,"REGDT":"2020-02-24","REGBR":"005",
         * "REGTELLER":"005097","UPDATER":"005097"
         * ,"CLSCASE":"L","REJECT_LIST":[{
         * "OID":"1c511ed4f3f94d899eab7b9422563d18"
         * ,"REFUSECD":"A998","REFUSEDS":
         * "２０１８－０５－１８票信異常紀錄","STATUSCD":"1"},{"OID"
         * :"f354973855824339bed0e4d78be37e8e"
         * ,"REFUSECD":"A998","REFUSEDS":"房貸信用評等過低"
         * ,"STATUSCD":"1"},{"OID":"1ecd4202a7184acba3c3dee96aa1999c"
         * ,"REFUSECD":"A998","REFUSEDS":"疑似人頭戶","STATUSCD":"1"},{"OID":
         * "8cc1af67563241549db2366abdeb9162"
         * ,"REFUSECD":"A998","REFUSEDS":"疑似人頭戶","STATUSCD":"1"}]}
         *
         * List<Map<String, Object>> rejectList = misLnunIdService
         * .findRejectWithRegDt(dataStartDate, dataEndDate);
         *
         * JSONObject param = new JSONObject(); param.put("CALLING_SYSTEM",
         * "06"); param.put("TRANSACTION_ID", "LMS"); param.put("MAINID",
         * IDGenerator.getUUID()); param.put("REGDT", "2020-02-24");
         * param.put("REGBR", "005"); param.put("REGTELLER", "005097");
         * param.put("UPDATER", "005097"); param.put("CLSCASE", "L");
         *
         * JSONArray sendList = new JSONArray();
         *
         * for (Map<String, Object> rejectMap : rejectList) {
         *
         * JSONObject jsReject = new JSONObject(); jsReject.put("OID",
         * IDGenerator.getUUID()); jsReject.put("CUSTID",
         * MapUtils.getString(rejectMap, "CUSTID")); jsReject.put("DUPNO",
         * MapUtils.getString(rejectMap, "DUPNO"));
         *
         * String REFUSECD = MapUtils.getString(rejectMap, "REFUSECD"); String
         * REFUSEDS = ""; if (Util.equals(REFUSECD, "23")) { REFUSECD = "A998";
         * REFUSEDS = MapUtils.getString(rejectMap, "REFUSEDS"); } else {
         * REFUSECD = "L0" + REFUSECD; REFUSEDS = ""; }
         *
         * jsReject.put("REFUSECD", REFUSECD); jsReject.put("REFUSEDS",
         * REFUSEDS); jsReject.put("STATUSCD", MapUtils.getString(rejectMap,
         * "STATUSCD"));
         *
         * sendList.add(jsReject);
         *
         * }
         *
         * param.put("REJECT_LIST", sendList);
         *
         * return param.toString();
         */
        return "";
    }

    /**
     * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0027(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");

        try {
            misdbBASEService.doLmsBatch0027();
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();

    }

    /**
     * J-109-0135 紓困案簡訊發送
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0028(JSONObject request) {

        String errMsg = "";

        String sysId = request.getString("SYSID");
        String businessType = request.getString("BUSINESS_TYPE");
        String params = null;
        try {
            this.logger.info("[doWork]SYSID參數:" + sysId);
            this.logger.info("[doWork]BUSINESS_TYPE參數:" + businessType);

            if (StringUtils.trimToNull(sysId) == null) {
                throw new IllegalArgumentException("請設定 SYSID 參數!");
            }

            if (StringUtils.trimToNull(businessType) == null) {
                throw new IllegalArgumentException("請設定 BUSINESS_TYPE 參數!");
            }

            params = StrUtils.concat("參數: SYSID=", sysId, ", BUSINESS_TYPE=",
                    businessType, "\n");

            StringBuilder retMsg = new StringBuilder();
            StringBuilder msg = new StringBuilder();
            Map<String, Integer> result = smsService.sendMessagesBySysId(sysId,
                    businessType);
            msg.append("系統=[");
            msg.append(sysId);
            msg.append("] ");
            msg.append("業務別=[");
            msg.append(businessType);
            msg.append("] 傳送簡訊:成功=[");
            msg.append(result.get("SUCCESS"));
            msg.append("],失敗=[");
            msg.append(result.get("FAIL"));
            msg.append("]");
            msg.append("\n");
            retMsg.append(msg);
            logger.info("[doLmsBatch0028] 執行結果：" + retMsg.toString());
            logger.info("[doLmsBatch0028] ===END=== ");

            errMsg = StrUtils.concat(params, "執行結果: ", retMsg);

        } catch (Exception ex) {
            errMsg = "Exception:" + ex.toString();
            this.logger.info(errMsg);
        }

        return errMsg;

    }

    /**
     * J-109-0135 紓困案簡訊發送結果查詢
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0029(JSONObject request) {

        String errMsg = "";

        String sysId = request.getString("SYSID");
        String businessType = request.getString("BUSINESS_TYPE");
        String params = null;
        try {
            this.logger.info("[doWork]SYSID參數:" + sysId);
            this.logger.info("[doWork]BUSINESS_TYPE參數:" + businessType);

            if (StringUtils.trimToNull(sysId) == null) {
                throw new IllegalArgumentException("請設定 SYSID 參數!");
            }

            if (StringUtils.trimToNull(businessType) == null) {
                throw new IllegalArgumentException("請設定 BUSINESS_TYPE 參數!");
            }

            params = StrUtils.concat("參數: SYSID=", sysId, ", BUSINESS_TYPE=",
                    businessType, "\n");

            StringBuilder retMsg = new StringBuilder();
            StringBuilder msg = new StringBuilder();
            List<SmsContent> result = smsService
                    .queryMsgsUnQueryBySysIdBussType(sysId, businessType);
            msg.append("系統=[");
            msg.append(sysId);
            msg.append("] 查詢簡訊:成功=[");
            msg.append(result.size());
            msg.append("]");
            msg.append("\n");
            retMsg.append(msg);
            logger.info("[doLmsBatch0029] 執行結果：" + retMsg.toString());
            logger.info("[doLmsBatch0029] ===END=== ");

            errMsg = StrUtils.concat(params, "執行結果: ", retMsg);

        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return errMsg;

    }

    /**
     * J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0030(JSONObject request) {
        //
        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");

        String dataStartDate = Util.trim(request.getString("dataStartDate"));
        String dataEndDate = Util.trim(request.getString("dataEndDate"));

        // 如果沒有帶參數，預設抓上月底最後一天的
        // 每月執行版本
        // if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
        // final String DATE_FORMAT_STR = "yyyy-MM-dd";
        // dataStartDate = CapDate.formatDate(LMSUtil.getExMonthFirstDay(-1),
        // DATE_FORMAT_STR);
        // dataEndDate = CapDate.formatDate(LMSUtil.getExMonthLastDay(-1),
        // DATE_FORMAT_STR);
        //
        // }

        // 搭配LMS180R55，改為每日執行版本
        if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
            final String DATE_FORMAT_STR = "yyyy-MM-dd";
            String currentDate = CapDate.getCurrentDate(DATE_FORMAT_STR);
            dataStartDate = CapDate.shiftDaysString(currentDate,
                    DATE_FORMAT_STR, -1);
            dataEndDate = currentDate;

        }

        try {

            // J-109-0235_05097_B1009 Web e-loan國內企金授信新增兆元振興融資方案
            // BranchRate branchRate = lmsService.getBranchRate("940");

            List rows = eloandbService.findAllReviveFromL140m01a(dataStartDate,
                    dataEndDate);

            Map<String, Map<String, String>> processBorr0024 = new TreeMap<String, Map<String, String>>();
            Map<String, String> processBorrIsNew = new TreeMap<String, String>();
            Map<String, Map<String, Object>> processRate = new TreeMap<String, Map<String, Object>>();
            Map<String, String> processRateMaxDate = new TreeMap<String, String>();

            // J-109-0235_05097_B1009 Web e-loan國內企金授信新增兆元振興融資方案
            // 利率GLOBAL
            String gRateYmd = "";
            String gRateStartDate = "";
            String gRateEndDate = "";
            Map<String, Object> gRate = null;
            final String DATE_FORMAT_STR = "yyyy-MM-dd"; // 2020-08-19

            Iterator it = rows.iterator();
            while (it.hasNext()) {
                Map dataMap = (Map) it.next();

                String clType = "";
                String busCd = "";

                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String CASEDATE = Util.trim(MapUtils.getString(dataMap,
                        "CASEDATE"));
                String ENDDATE = Util.trim(MapUtils.getString(dataMap,
                        "ENDDATE"));
                String LNTYPE = Util
                        .trim(MapUtils.getString(dataMap, "LNTYPE"));
                String PROJCLASS = Util.trim(MapUtils.getString(dataMap,
                        "PROJCLASS"));
                String REUSE = Util.trim(MapUtils.getString(dataMap, "REUSE"));

                /*
                 * Calendar today = Calendar.getInstance();
                 * today.setTime(Util.parseDate(ENDDATE));
                 * today.set(Calendar.DAY_OF_MONTH,
                 * today.getActualMinimum(Calendar.DAY_OF_MONTH));
                 *
                 * String rateStartDate = CapDate.formatDate(today.getTime(),
                 * "yyyy-MM-dd");
                 *
                 * today.setTime(Util.parseDate(ENDDATE));
                 * today.set(Calendar.DAY_OF_MONTH,
                 * today.getActualMaximum(Calendar.DAY_OF_MONTH));
                 *
                 * String rateEndDate = CapDate.formatDate(today.getTime(),
                 * "yyyy-MM-dd");
                 *
                 * String fullRateDate = rateStartDate + "^" + rateEndDate;
                 * Map<String, Object> rate = null; String queryDataYMD = "";
                 *
                 * if (processRate.containsKey(fullRateDate)) { rate =
                 * processRate.get(fullRateDate); queryDataYMD =
                 * MapUtils.getString(processRateMaxDate, fullRateDate,
                 * "0001-01-01"); } else { // 月底匯率--取得最後一天匯率 Map<String, Object>
                 * dataYMDMap = rateService
                 * .findLastDateBetweenRATEYMDRange(rateStartDate, rateEndDate);
                 * queryDataYMD = Util.trim(MapUtils.getString(dataYMDMap,
                 * "MAXDATAYMD", ""));
                 *
                 * if (Util.equals(queryDataYMD, "")) { errMsg.append("無法取得" +
                 * rateStartDate + "~" + rateEndDate + "最新匯率日期"); return
                 * errMsg.toString(); }
                 *
                 * rate = rateService.findByDate(queryDataYMD); if (rate == null
                 * || rate.isEmpty()) { errMsg.append("無日期為" + queryDataYMD +
                 * "之匯率檔"); return errMsg.toString(); }
                 *
                 * processRate.put(fullRateDate, rate);
                 * processRateMaxDate.put(fullRateDate, queryDataYMD);
                 *
                 * }
                 */

                // ****************************************************************************************************

                String RPTMAINID = Util.trim(MapUtils.getString(dataMap,
                        "RPTMAINID"));

                String DOCTYPE = Util.trim(MapUtils.getString(dataMap,
                        "DOCTYPE"));
                String DOCKIND = Util.trim(MapUtils.getString(dataMap,
                        "DOCKIND"));
                String DOCCODE = Util.trim(MapUtils.getString(dataMap,
                        "DOCCODE"));
                String TYPCD = Util.trim(MapUtils.getString(dataMap, "TYPCD"));
                String CASEBRID = Util.trim(MapUtils.getString(dataMap,
                        "CASEBRID"));
                String CASENO = Util
                        .trim(MapUtils.getString(dataMap, "CASENO"));
                String CASELVL = Util.trim(MapUtils.getString(dataMap,
                        "CASELVL"));
                String CNTRMAINID = Util.trim(MapUtils.getString(dataMap,
                        "CNTRMAINID"));

                String CUSTNAME = Util.trim(MapUtils.getString(dataMap,
                        "CUSTNAME"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String CNTRBRID = Util.trim(MapUtils.getString(dataMap,
                        "CNTRBRID"));
                String PROPERTY = Util.trim(MapUtils.getString(dataMap,
                        "PROPERTY"));
                String SBJPROPERTY = Util.trim(MapUtils.getString(dataMap,
                        "SBJPROPERTY"));
                String HEADITEM1 = Util.trim(MapUtils.getString(dataMap,
                        "HEADITEM1"));
                String HEADITEM2 = Util.trim(MapUtils.getString(dataMap,
                        "HEADITEM2"));
                String HEADITEM3 = Util.trim(MapUtils.getString(dataMap,
                        "HEADITEM3"));

                String LV2CURR = Util.trim(MapUtils.getString(dataMap,
                        "LV2CURR"));

                BigDecimal LV2AMT = Util.equals(
                        Util.trim(MapUtils.getString(dataMap, "LV2AMT")), "") ? BigDecimal.ZERO
                        : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                        dataMap, "LV2AMT")));

                String CURRENTAPPLYCURR = Util.trim(MapUtils.getString(dataMap,
                        "CURRENTAPPLYCURR"));

                BigDecimal CURRENTAPPLYAMT = Util.equals(Util.trim(MapUtils
                        .getString(dataMap, "CURRENTAPPLYAMT")), "") ? BigDecimal.ZERO
                        : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                        dataMap, "CURRENTAPPLYAMT")));
                String INCAPPLYTOTCURR = Util.trim(MapUtils.getString(dataMap,
                        "INCAPPLYTOTCURR"));
                BigDecimal INCAPPLYTOTAMT = Util.equals(Util.trim(MapUtils
                        .getString(dataMap, "INCAPPLYTOTAMT")), "") ? BigDecimal.ZERO
                        : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                        dataMap, "INCAPPLYTOTAMT")));
                String INCASSTOTCURR = Util.trim(MapUtils.getString(dataMap,
                        "INCASSTOTCURR"));
                BigDecimal INCASSTOTAMT = Util.equals(
                        Util.trim(MapUtils.getString(dataMap, "INCASSTOTAMT")),
                        "") ? BigDecimal.ZERO : Util.parseBigDecimal(Util
                        .trim(MapUtils.getString(dataMap, "INCASSTOTAMT")));
                String LOANTOTCURR = Util.trim(MapUtils.getString(dataMap,
                        "LOANTOTCURR"));
                BigDecimal LOANTOTAMT = Util.equals(
                        Util.trim(MapUtils.getString(dataMap, "LOANTOTAMT")),
                        "") ? BigDecimal.ZERO : Util.parseBigDecimal(Util
                        .trim(MapUtils.getString(dataMap, "LOANTOTAMT")));
                String ASSURETOTCURR = Util.trim(MapUtils.getString(dataMap,
                        "ASSURETOTCURR"));
                BigDecimal ASSURETOTAMT = Util.equals(
                        Util.trim(MapUtils.getString(dataMap, "ASSURETOTAMT")),
                        "") ? BigDecimal.ZERO : Util.parseBigDecimal(Util
                        .trim(MapUtils.getString(dataMap, "ASSURETOTAMT")));

                // J-109-0275_05097_B1001 Web e-Loan企金授信優化兆元振興融資方案系統欄位及報表
                String HASCOMM = "";
                String COMMSNO = "";
                BigDecimal REVIVEAMT = null;
                String SNOKIND = "";

                L140M01A l140m01a = l140m01aDao.findByMainId(CNTRMAINID);
                if (l140m01a == null) {
                    l140m01a = new L140M01A();
                }

                // eLoan L180R542A是否已存在
                boolean alreadExist = false;
                L180R54A l180r54a = l180r54aDao.findByMainIdAndCntrMainId(
                        RPTMAINID, CNTRMAINID);
                if (l180r54a == null) {
                    l180r54a = new L180R54A();
                    l180r54a.setMainId(RPTMAINID);

                    l180r54a.setCreator("LMS104");
                    l180r54a.setCreateTime(CapDate.getCurrentTimestamp());

                } else {
                    alreadExist = true;
                    l180r54a.setUpdater("LMS104");
                    l180r54a.setUpdateTime(CapDate.getCurrentTimestamp());
                }

                // J-109-0235_05097_B1009 Web e-loan國內企金授信新增兆元振興融資方案
                // [下午2:05] 黃建霖
                // 兆元振興的匯率，是以簽報書核准日期的上一個月月底最後的匯率 對吧，簽報書核准日期為8/19，匯率用的是7/31的對吧
                // ​[下午2:07] 吳怡貞
                // 對 麻煩了

                String newEndDate = Util.getLeftStr(ENDDATE, 7) + "-01"; // 2020-08-01

                String rateEndDate = CapDate.shiftDaysString(newEndDate,
                        DATE_FORMAT_STR, -1); // 2020-07-31
                String rateStartDate = Util.getLeftStr(rateEndDate, 7) + "-01"; // 2020-07-01

                if (Util.notEquals(rateStartDate, gRateStartDate)
                        || Util.notEquals(rateEndDate, gRateEndDate)) {

                    Map<String, Object> dataYMDMap = rateService
                            .findLastDateBetweenRATEYMDRange(rateStartDate,
                                    rateEndDate);

                    String queryDataYMD = Util.trim(MapUtils.getString(
                            dataYMDMap, "MAXDATAYMD", ""));
                    if (Util.equals(queryDataYMD, "")) {
                        errMsg.append("無法取得MIS RATETBL " + rateStartDate + "~"
                                + rateEndDate + " 最大利率日期");
                        System.out.println(errMsg.toString());
                        logger.info("doLmsBatch0030 excute result:"
                                + errMsg.toString());
                        return errMsg.toString();
                    }

                    gRate = rateService.findByDate(queryDataYMD);
                    if (gRate == null || gRate.isEmpty()) {
                        errMsg.append("無日期為" + queryDataYMD + "之匯率檔");
                        logger.info("doLmsBatch0030 excute result:"
                                + errMsg.toString());
                        return errMsg.toString();
                    }

                    gRateYmd = StrUtils
                            .concat((Util.parseInt(Util.getLeftStr(
                                            queryDataYMD, 3)) + 1911),
                                    "-",
                                    Util.addZeroWithValue(Util.trim(StringUtils
                                            .substring(queryDataYMD, 3, 5)), 2),
                                    "-", Util.addZeroWithValue(Util.trim(Util
                                            .getRightStr(queryDataYMD, 2)), 2));

                    gRateStartDate = rateStartDate;
                    gRateEndDate = rateEndDate;

                }

                String RateYmd = gRateYmd;

                BigDecimal endRate = Util.equals(INCAPPLYTOTCURR, "") ? BigDecimal.ONE
                        : Util.parseBigDecimal(gRate.get(INCAPPLYTOTCURR));
                BigDecimal currEndRate = Util.equals(CURRENTAPPLYCURR, "") ? BigDecimal.ONE
                        : Util.parseBigDecimal(gRate.get(CURRENTAPPLYCURR));
                BigDecimal lv2Rate = Util.equals(LV2CURR, "") ? BigDecimal.ONE
                        : Util.parseBigDecimal(gRate.get(LV2CURR));

                // 如果該筆資料已經存在，且幣別與本次相同，則用已存在資料的匯率，免得數字會異動
                // if (alreadExist) {
                // if (Util.equals(INCAPPLYTOTCURR,
                // Util.trim(l180r54a.getIncApplyTotCurr()))
                // && l180r54a.getEndRate() != null) {
                // endRate = l180r54a.getEndRate();
                // }
                // if (Util.equals(CURRENTAPPLYCURR,
                // Util.trim(l180r54a.getCurrentApplyCurr()))
                // && l180r54a.getCurrEndRate() != null) {
                // currEndRate = l180r54a.getCurrEndRate();
                // }
                // if (Util.equals(LV2CURR, Util.trim(l180r54a.getLV2Curr()))
                // && l180r54a.getLv2Rate() != null) {
                // lv2Rate = l180r54a.getLv2Rate();
                // }
                // }

                // 借款人基本資料*********************************************************
                // 新戶
                String isNew = "N";

                L120M01C l120m01c = l140m01a.getL120m01c();
                L120M01A l120m01a = null;
                if (l120m01c != null) {
                    L120S01B l120s01b = l120s01bDao.findByUniqueKey(
                            l120m01c.getMainId(), CUSTID, DUPNO);
                    L120S01A l120s01a = l120s01aDao.findByUniqueKey(
                            l120m01c.getMainId(), CUSTID, DUPNO);
                    if (l120s01b != null) {
                        busCd = Util.trim(l120s01b.getBusCode());
                        clType = Util.trim(l120s01b.getCustClass());
                    }
                    if (l120s01a != null) {
                        isNew = Util.trim(l120s01a.getNewCustFlag());
                    }

                    l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
                }

                if (l120m01a == null) {
                    l120m01a = new L120M01A();
                }

                // 沒資料改抓0024
                if (Util.equals(clType, "")) {

                    Map<String, Object> custDataMap = misCustdataService
                            .findCltypeAndBusCdById(CUSTID, DUPNO);
                    if (custDataMap != null && !custDataMap.isEmpty()) {

                        String tClType = Util.trim(MapUtils.getString(
                                custDataMap, "CLTYPE"));
                        busCd = Util.trim(MapUtils.getString(custDataMap,
                                "BUSCD"));

                        clType = this.getCustClass(busCd, tClType);
                    }

                }

                // 沒資料改抓DW
                if (Util.equals(isNew, "")) {
                    isNew = lmsService.applyIsNewCust(CUSTID, DUPNO);
                }

                // 最新一筆L230M01A 已核准額度辦理報送作業
                String STATUS = "";
                // Map<String, Object> l230Map = eloandbService
                // .findL230S01LastBySrcMainId(CNTRMAINID, "05O");
                // if (l230Map != null && !l230Map.isEmpty()) {
                // STATUS = Util.trim(l230Map.get("NUSEMEMO"));
                // }

                String ISREVIVE = Util.trim(MapUtils.getString(dataMap,
                        "ISREVIVE"));
                String REVIVETARGET = Util.trim(MapUtils.getString(dataMap,
                        "REVIVETARGET"));
                String REVIVECOREINDUSTRY = Util.trim(MapUtils.getString(
                        dataMap, "REVIVECOREINDUSTRY"));
                String REVIVECHAIN = Util.trim(MapUtils.getString(dataMap,
                        "REVIVECHAIN"));
                String REVIVELOANPURPOSE = Util.trim(MapUtils.getString(
                        dataMap, "REVIVELOANPURPOSE"));

                String lnSubject = "";
                StringBuffer lnSubjectBuff = new StringBuffer("");

                List<L140M01C> l140m01cs = l140m01cDao.findByMainId(l140m01a
                        .getMainId());
                if (l140m01cs != null && !l140m01cs.isEmpty()) {
                    for (L140M01C l140m01c : l140m01cs) {
                        lnSubjectBuff.append(
                                (Util.equals(lnSubjectBuff.toString(), "") ? ""
                                        : ",")).append(
                                Util.trim(l140m01c.getLoanTP()));
                    }
                }
                if (Util.notEquals(lnSubjectBuff, "")) {
                    lnSubject = lnSubjectBuff.toString();
                }

                String increaseFlag = "N";
                String reportFlag = "N";

                // J-109-0419_05097_B1002 兆元振興方案相關報表格式調整
                String reportFlag_113 = "N";
                String reportFlag_113_reUse = "N";
                String reportFlag_517 = "N";
                String reportFlag_517_reUse = "N";

                BigDecimal reportAmt = BigDecimal.ZERO;
                boolean endDateOk = true; // 2020/7/1以後的才算
                // ----改成統計報表時再判斷起算日期的條件
                boolean propertyOk = false;
                boolean subjectOk = false;
                boolean currOk = false;

                if (l140m01a != null) {
                    // J-109-0275_05097_B1001 Web e-Loan企金授信優化兆元振興融資方案系統欄位及報表
                    if (Util.notEquals(Util.trim(l140m01a.getCommSno()), "")) {
                        HASCOMM = "Y";
                        COMMSNO = Util.trim(l140m01a.getCommSno());
                    } else {
                        HASCOMM = "N";
                        COMMSNO = "";
                    }

                    REVIVEAMT = l140m01a.getReviveAmt();

                    SNOKIND = Util.trim(l140m01a.getSnoKind());

                    // 計算報送金額****************************************************************************
                    // J-109-0419_05097_B1001 兆元振興方案新增額度為不循環時才符合報送之判斷
                    reportFlag = lmsService.chkReviveConformReport(l140m01a,
                            l120m01a, "", true);

                    if ((LMSUtil.isContainValue(
                            Util.trim(l140m01a.getProPerty()),
                            UtilConstants.Cntrdoc.Property.增額))
                            && (!LMSUtil.isContainValue(PROPERTY,
                            UtilConstants.Cntrdoc.Property.新做))) {

                        increaseFlag = "Y";

                    }

                    reportAmt = BigDecimal.ZERO;

                    // J-109-0275_05097_B1001 Web e-Loan企金授信優化兆元振興融資方案系統欄位及報表
                    if (Util.equals(HASCOMM, "Y") && REVIVEAMT != null) {

                        // 有共用時，以計入兆元振興額度為準
                        if (Util.equals(CURRENTAPPLYCURR, "TWD")
                                || Util.equals(CURRENTAPPLYCURR, "")) {
                            reportAmt = l140m01a.getReviveAmt();
                        } else {
                            reportAmt = l140m01a.getReviveAmt() == null ? null
                                    : l140m01a.getReviveAmt().multiply(
                                    currEndRate);
                        }

                    } else {

                        if (Util.equals(increaseFlag, "Y")) {
                            // 增額*****************************************

                            // 現請(轉台幣)-前准(轉台幣)

                            if (Util.notEquals(LV2CURR, "")
                                    && LV2AMT.compareTo(BigDecimal.ZERO) > 0) {
                                // 前准額度金額有資料時
                                if (Util.equals(CURRENTAPPLYCURR, LV2CURR)) {

                                    // 前准幣別與現請幣別相同，先直接相減，再轉成台幣
                                    BigDecimal diff = CURRENTAPPLYAMT
                                            .subtract(LV2AMT);
                                    if (diff.compareTo(BigDecimal.ZERO) < 0) {
                                        reportAmt = BigDecimal.ZERO;
                                    } else {
                                        if (Util.equals(CURRENTAPPLYCURR, "TWD")
                                                || (Util.equals(
                                                CURRENTAPPLYCURR, ""))) {
                                            reportAmt = diff;
                                        } else {
                                            reportAmt = diff
                                                    .multiply(currEndRate);
                                        }
                                    }

                                } else {

                                    // 前准幣別與現請幣別不相同，先轉成台幣，再直接相減
                                    BigDecimal tCURRENTAPPLYAMT = BigDecimal.ZERO;
                                    if (Util.equals(CURRENTAPPLYCURR, "TWD")
                                            || (Util.equals(CURRENTAPPLYCURR,
                                            ""))) {
                                        tCURRENTAPPLYAMT = CURRENTAPPLYAMT;
                                    } else {
                                        tCURRENTAPPLYAMT = CURRENTAPPLYAMT
                                                .multiply(currEndRate);
                                    }

                                    BigDecimal tLV2AMT = BigDecimal.ZERO;
                                    if (Util.equals(LV2CURR, "TWD")
                                            || (Util.equals(LV2CURR, ""))) {
                                        tLV2AMT = LV2AMT;
                                    } else {
                                        tLV2AMT = LV2AMT.multiply(lv2Rate);
                                    }

                                    // 已經換算為台幣的差額
                                    BigDecimal diff = tCURRENTAPPLYAMT
                                            .subtract(tLV2AMT);

                                    if (diff.compareTo(BigDecimal.ZERO) < 0) {
                                        reportAmt = BigDecimal.ZERO;
                                    } else {
                                        reportAmt = diff;
                                    }

                                }
                            } else {
                                // 前准額度幣別或金額沒有資料時，就直接用現請額度替代
                                if (Util.equals(CURRENTAPPLYCURR, "TWD")
                                        || (Util.equals(CURRENTAPPLYCURR, ""))) {
                                    reportAmt = CURRENTAPPLYAMT;
                                } else {
                                    reportAmt = CURRENTAPPLYAMT
                                            .multiply(currEndRate);
                                }
                            }

                            // 如果為負，直接變成0
                            if (reportAmt.compareTo(BigDecimal.ZERO) < 0) {
                                reportAmt = BigDecimal.ZERO;
                            }

                        } else {
                            // 非增額(新作、續約、提前續約)*****************************************
                            // 現請(轉台幣)

                            if (Util.equals(CURRENTAPPLYCURR, "TWD")
                                    || Util.equals(CURRENTAPPLYCURR, "")) {
                                reportAmt = CURRENTAPPLYAMT;
                            } else {
                                reportAmt = CURRENTAPPLYAMT
                                        .multiply(currEndRate);

                            }

                        }
                    }

                    // REVIVETARGET_01=517項，REVIVETARGET_02=113項
                    reportFlag_113 = lmsService.chkReviveConformReport(
                            l140m01a, l120m01a, "REVIVETARGET_02", false);
                    reportFlag_113_reUse = lmsService.chkReviveConformReport(
                            l140m01a, l120m01a, "REVIVETARGET_02", true);
                    reportFlag_517 = lmsService.chkReviveConformReport(
                            l140m01a, l120m01a, "REVIVETARGET_01", false);
                    reportFlag_517_reUse = lmsService.chkReviveConformReport(
                            l140m01a, l120m01a, "REVIVETARGET_01", true);

                }

                l180r54a.setCntrMainId(CNTRMAINID);
                l180r54a.setCaseDate(CapDate.parseDate(CASEDATE));
                l180r54a.setEndDate(CapDate.parseDate(ENDDATE));
                l180r54a.setDocType(DOCTYPE);
                l180r54a.setDocKind(DOCKIND);
                l180r54a.setDocCode(DOCCODE);
                l180r54a.setTypCd(TYPCD);
                l180r54a.setCustId(CUSTID);
                l180r54a.setDupNo(DUPNO);
                l180r54a.setCustName(CUSTNAME);
                l180r54a.setCaseBrId(CASEBRID);
                l180r54a.setCaseNo(CASENO);
                l180r54a.setCaseLvl(CASELVL);
                l180r54a.setCntrNo(CNTRNO);
                l180r54a.setCntrBrId(CNTRBRID);
                l180r54a.setProperty(PROPERTY);
                l180r54a.setSbjProperty(SBJPROPERTY);
                l180r54a.setHeadItem1(HEADITEM1);
                l180r54a.setHeadItem2(HEADITEM2);
                l180r54a.setHeadItem3(HEADITEM3);

                l180r54a.setLV2Curr(LV2CURR);
                l180r54a.setLV2Amt(LV2AMT);

                l180r54a.setCurrentApplyCurr(CURRENTAPPLYCURR);
                l180r54a.setCurrentApplyAmt(CURRENTAPPLYAMT);
                l180r54a.setIncApplyTotCurr(INCAPPLYTOTCURR);
                l180r54a.setIncApplyTotAmt(INCAPPLYTOTAMT);
                l180r54a.setIncAssTotCurr(INCASSTOTCURR);
                l180r54a.setIncAssTotAmt(INCASSTOTAMT);
                l180r54a.setLoanTotCurr(LOANTOTCURR);
                l180r54a.setLoanTotAmt(LOANTOTAMT);
                l180r54a.setAssureTotCurr(ASSURETOTCURR);
                l180r54a.setAssureTotAmt(ASSURETOTAMT);
                l180r54a.setCltType(clType);
                l180r54a.setBusCode(busCd);
                l180r54a.setDeletedTime(null);

                l180r54a.setEndRate(endRate);

                // [下午2:05] 黃建霖
                // 兆元振興的匯率，是以簽報書核准日期的上一個月月底最後的匯率 對吧，簽報書核准日期為8/19，匯率用的是7/31的對吧
                // ​[下午2:07] 吳怡貞
                // 對 麻煩了

                // J-109-0235_05097_B1009 Web e-loan國內企金授信新增兆元振興融資方案
                // l180r54a.setRateYmd(CapDate.getCurrentDate("yyyy-MM-dd"));
                l180r54a.setRateYmd(RateYmd);

                l180r54a.setIsNew(isNew);
                l180r54a.setLnType(LNTYPE);
                l180r54a.setProjClass(PROJCLASS);
                l180r54a.setLnSubject(lnSubject);
                l180r54a.setIsRevive(ISREVIVE);
                l180r54a.setReviveTarget(REVIVETARGET);
                l180r54a.setReviveCoreIndustry(REVIVECOREINDUSTRY);
                l180r54a.setReviveChain(REVIVECHAIN);
                l180r54a.setReviveLoanPurpose(REVIVELOANPURPOSE);

                // l180r54a.setCurrEndRate(Util.parseBigDecimal(MapUtils
                // .getString(rate, CURRENTAPPLYCURR, "0")));

                l180r54a.setCurrEndRate(currEndRate);
                l180r54a.setLv2Rate(lv2Rate);

                l180r54a.setIncreaseFlag(increaseFlag);
                l180r54a.setReportFlag(reportFlag);
                l180r54a.setReportAmt(reportAmt);

                // J-109-0275_05097_B1001 Web e-Loan企金授信優化兆元振興融資方案系統欄位及報表
                l180r54a.setHasComm(HASCOMM);
                l180r54a.setCommSno(COMMSNO);
                l180r54a.setReviveAmt(REVIVEAMT);
                l180r54a.setSnoKind(SNOKIND);

                // J-109-0419_05097_B1002 兆元振興方案相關報表格式調整
                l180r54a.setReUse(REUSE);
                l180r54a.setReportFlag_113(reportFlag_113);
                l180r54a.setReportFlag_113_reUse(reportFlag_113_reUse);
                l180r54a.setReportFlag_517(reportFlag_517);
                l180r54a.setReportFlag_517_reUse(reportFlag_517_reUse);

                lmsService.save(l180r54a);

                // 更新國內、海外ELF383

                this.uploadELLNSEEK_ForRevive(l120m01a, l140m01a);

            }

        } catch (Exception e) {
            // System.out.println(e.toString());
            errMsg.append(e.toString());
        }

        logger.info("doLmsBatch0030 excute result:" + errMsg);

        return errMsg.toString();
    }

    public void uploadELLNSEEK_ForRevive(L120M01A meta, L140M01A l140m01a) {
        String itemType = lmsService.checkL140M01AItemType(meta);
        List<L140M01A> l140m01as = l140m01aDao
                .findL140m01aListByL120m01cMainId(meta.getMainId(), itemType,
                        null);
        // 授權等級
        String approLvl = "";
        // 1 -分行授權內 2 -營運中心授權內
        // 3 -分行授權外 4 -營運中心授權外
        // 5 - 子行授權內 ...這樣比較明確
        String docKind = meta.getDocKind();
        String authLvl = meta.getAuthLvl();
        if (UtilConstants.Casedoc.DocKind.授權內.equals(docKind)) {
            if (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(authLvl)) {
                approLvl = UtilConstants.UploadType.ELLNSEEK_approLvl.分行授權內;
            } else if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(authLvl)) {
                approLvl = UtilConstants.UploadType.ELLNSEEK_approLvl.營運中心授權內;
            } else {
                approLvl = UtilConstants.UploadType.ELLNSEEK_approLvl.子行授權內;
            }
        } else if (UtilConstants.Casedoc.DocKind.授權外.equals(docKind)) {
            // 當為總處分行沒過營運中心的就是分行授權外
            if (LMSUtil.isSpecialBranch(meta.getCaseBrId())
                    && (!UtilConstants.Casedoc.AreaChk.送審查.equals(meta
                    .getAreaChk()) || (UtilConstants.Casedoc.AreaChk.送審查
                    .equals(meta.getAreaChk()) && Util.isEmpty(Util
                    .trim(meta.getAreaBrId()))))) {
                approLvl = UtilConstants.UploadType.ELLNSEEK_approLvl.分行授權外;
            } else {
                approLvl = UtilConstants.UploadType.ELLNSEEK_approLvl.營運中心授權外;
            }
        }
        String gutcDate = null;
        String projNO = "";
        String property = "";
        String cntrNo = "";
        String custId = meta.getCustId();// 簽報書主要借款人id
        String dupNo = meta.getDupNo();// 簽報書主要借款人 dupno
        String brNo = ""; // 額度明細表 分行號碼
        String status = ""; // 文件狀態
        String apprYY = CapDate.getCurrentDate("yyyy");// 年份
        String apprMM = CapDate.getCurrentDate("MM");// 月份

        // J-108-0302 是否符合出口實績規範
        String experf_fg = ""; // 是否符合出口實績規範
        String flaw_fg = ""; // 瑕疵額度控管方式
        BigDecimal flaw_amt = BigDecimal.ZERO; // 瑕疵額度限額
        String[] sysExperf = StringUtils.split(
                Util.trim(lmsService.getSysParamDataValue("EXPERF")), ",");

        MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
        String updater = user.getUserId();

        projNO = "";
        cntrNo = l140m01a.getCntrNo();
        brNo = l140m01a.getOwnBrId();
        custId = l140m01a.getCustId();
        dupNo = l140m01a.getDupNo();
        property = "";
        status = lmsService.converDocstatus(l140m01a.getDocStatus());
        if (UtilConstants.DEFAULT.是.equals(l140m01a.getHeadItem1())) {
            if (Util.isNotEmpty(l140m01a.getGutCutDate())) {
                // 檢查 信保案件之信保動用截止日
                String tproperty = "999";
                Boolean notCheck = false;
                // 如果當性質為不變或者是取消
                for (String x : l140m01a.getProPerty().split(
                        UtilConstants.Mark.SPILT_MARK)) {
                    if (UtilConstants.Cntrdoc.Property.不變.equals(x)) {
                        notCheck = true;
                        tproperty = UtilConstants.Cntrdoc.Property.不變;
                        break;
                    } else if (UtilConstants.Cntrdoc.Property.取消.equals(x)) {
                        notCheck = true;
                        tproperty = UtilConstants.Cntrdoc.Property.取消;
                        break;
                    }

                    if (Integer.valueOf(x) < Integer.valueOf(tproperty)) {
                        tproperty = x;
                    }
                }

                // if (!notCheck) {
                gutcDate = CapDate.formatDate(l140m01a.getGutCutDate(),
                        UtilConstants.DateFormat.YYYY_MM_DD);
                property = tproperty;
                projNO = LMSUtil.getUploadCaseNo(meta);
                // }
            }
        } else {
            gutcDate = null;
        }
        String BRNID = meta.getCaseBrId();

        String isGuaOldCase = Util.trim(l140m01a.getIsGuaOldCase());
        String byNewOld = Util.equals(isGuaOldCase, "Y") ? Util.trim(l140m01a
                .getByNewOld()) : "";

        /** -------------動用期限------------- */
        String lnuseNo = Util.trim(l140m01a.getUseDeadline());
        String useFmDt = "0001-01-01";
        String useEnDt = "0001-01-01";
        int useFtMn = 0;
        /** -------------動用期限------------- */
        // J-104-0284-001 是否收取帳務管理費
        String hasAmFee = "N";

        List<Map<String, Object>> ellnseeks = misEllnseekservice.findByKey(
                custId, dupNo, cntrNo);

        if (FlowDocStatusEnum.已核准.getCode().equals(l140m01a.getDocStatus())) {
            // 額度明細表已核准已額度明細表資料上傳
            if ("1".equals(lnuseNo)) {
                String[] desp1 = Util.trim(l140m01a.getDesp1()).split("~");
                if (desp1.length == 2) {
                    String date1 = Util.trim(desp1[0]);
                    if (Util.isNotEmpty(date1)) {
                        useFmDt = date1;
                    }

                    String date2 = Util.trim(desp1[1]);
                    if (Util.isNotEmpty(date2)) {
                        useEnDt = date2;
                    }
                }
            } else if ("2".equals(lnuseNo) || "3".equals(lnuseNo)
                    || "4".equals(lnuseNo) || "6".equals(lnuseNo)
                    || "7".equals(lnuseNo)) {
                useFtMn = Util.parseInt(Util.trim(l140m01a.getDesp1()));

            } else if ("8".equals(lnuseNo)) {
                // J-110-0320 為符營業單位陳報團貸實務作業，於ELOAN系統新增團貸動用期限選項
                // 自核准日起~YYYY-MM-DD
                String[] desp1 = Util.trim(l140m01a.getDesp1()).split("~");
                if (desp1.length == 2) {
                    String date = Util.trim(desp1[1]);
                    if (Util.isNotEmpty(date)) {
                        useFmDt = CapDate.formatDate(meta.getEndDate(),
                                "yyyy-MM-dd");
                        useEnDt = date;
                    }
                }
            } else if (UtilConstants.Usedoc.upType.取消.equals(l140m01a
                    .getProPerty()) || "0".equals(lnuseNo)) {
                useEnDt = "0001-01-01";
            }

            // J-104-0264-001 是否收取帳務管理費
            hasAmFee = Util.equals(Util.trim(l140m01a.getHasAmFee()), "") ? "N"
                    : Util.trim(l140m01a.getHasAmFee());

            // J-108-0302 是否符合出口實績規範
            HashMap<String, String> newList = LMSUtil.getItemList(l140m01a);
            for (String key : newList.keySet()) {
                if (Arrays.asList(sysExperf).contains(key)) {
                    experf_fg = Util.trim(l140m01a.getExperf_fg());
                    flaw_fg = Util.trim(l140m01a.getFlaw_fg());
                    flaw_amt = Util.parseBigDecimal(l140m01a.getFlaw_amt());
                    break;
                }
            }
        } else {
            // 額度明細表未核准，則如果ALOAN有資料則以目前ALOAN資料上傳
            if (!ellnseeks.isEmpty()) {
                for (Map<String, Object> ellnseek : ellnseeks) {
                    lnuseNo = Util.trim(ellnseek.get("LNUSENO"));

                    useFmDt = Util.equals(Util.trim(Util.nullToSpace(ellnseek
                            .get("USEFMDT"))), "") ? "0001-01-01" : Util
                            .trim(Util.nullToSpace(ellnseek.get("USEFMDT")));

                    useEnDt = Util.equals(Util.trim(Util.nullToSpace(ellnseek
                            .get("USEENDT"))), "") ? "0001-01-01" : Util
                            .trim(Util.nullToSpace(ellnseek.get("USEENDT")));

                    useFtMn = Util.equals(Util.trim(ellnseek.get("USEFTMN")),
                            "") ? 0 : Integer.valueOf(Util.trim(ellnseek
                            .get("USEFTMN")));

                    hasAmFee = Util.trim(ellnseek.get("HASAMFEE"));

                    // J-108-0302 是否符合出口實績規範
                    experf_fg = Util.equals(Util.trim(Util.nullToSpace(ellnseek
                            .get("EXPERF_FG"))), "") ? "" : Util.trim(Util
                            .nullToSpace(ellnseek.get("EXPERF_FG")));
                    flaw_fg = Util.equals(Util.trim(Util.nullToSpace(ellnseek
                            .get("FLAW_FG"))), "") ? "" : Util.trim(Util
                            .nullToSpace(ellnseek.get("FLAW_FG")));
                    flaw_amt = Util.equals(Util.trim(ellnseek.get("FLAW_AMT")),
                            "0") ? BigDecimal.ZERO : BigDecimal.valueOf(Double
                            .valueOf(Util.trim(ellnseek.get("FLAW_AMT"))));

                    break;
                }

            }
        }

        // J-109-0235_05097_B1007 Web e-loan國內企金授信新增兆元振興融資方案
        // J-109-0419_05097_B1001 兆元振興方案新增額度為不循環時才符合報送之判斷
        String ELF461_ISREVIVE_Y = Util.trim(lmsService.chkReviveConformReport(
                l140m01a, meta, "", true));
        String ELF461_MAINID = Util.trim(l140m01a.getMainId());

        // J-109-0152 保證機構是否為經外國中央政府所設立信用保證機構或經濟合作發展組織(OECD)公布之官方輸出信用機構
        String ELF461_ISOFCLCGA = Util.trim(l140m01a.getIsOfficialCga());
        String ELF461_CGA_COUNTRY = Util.trim(l140m01a.getCga_country());
        String ELF461_CGA_CRDTYPE = Util.trim(l140m01a.getCga_crdType());
        String ELF461_CGA_CRDAREA = Util.trim(l140m01a.getCga_crdArea());
        String ELF461_CGA_CRDPRED = Util.trim(l140m01a.getCga_crdPred());
        String ELF461_CGA_CRDGRAD = Util.trim(l140m01a.getCga_crdGrade());
        BigDecimal ELF461_CGA_RSKRTO = Util.parseBigDecimal(l140m01a
                .getCga_rskRatio());
        BigDecimal ELF461_CGA_GRADSCR = BigDecimal.ZERO;
        ELF461_CGA_GRADSCR = misdbBASEService.findLnf25cByType7(
                ELF461_CGA_CRDTYPE, ELF461_CGA_CRDGRAD);

        // J-109-0239_05097_B1001 Web
        // e-Loane-Loan授信管理系統案件簽報書之額度明細表新增「特殊融資或不動產ADC融資暴險註記」
        boolean needSpecialFinRisk = true;
        try {
            needSpecialFinRisk = lmsService.needShowIsSpecialFinRisk(meta,
                    l140m01a);
        } catch (Exception e) {

            logger.info("doLmsBatch0030 uploadELLNSEEK_ForRevive error:"
                    + e.toString());

        }

        String isSpecialFinRisk = "";
        String specialFinRiskType = "";
        String isCmsAdcRisk = Util.trim(l140m01a.getIsCmsAdcRisk());
        // J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
        String isProjectFinOperateStag = "";
        // J-112-0417 新增高品質專案融資分類
        String isHighQualityProjOpt_1 = "";//高品質專案融資_選項1
        String isHighQualityProjOpt_2 = "";//高品質專案融資_選項2
        String isHighQualityProjOpt_3 = "";//高品質專案融資_選項3
        String isHighQualityProjOpt_4 = "";//高品質專案融資_選項4
        String isHighQualityProjOpt_5 = "";//高品質專案融資_選項5
        String isHighQualityProjResult = "";//高品質專案融資_最終結果
        if (needSpecialFinRisk) {
            isSpecialFinRisk = Util.trim(l140m01a.getIsSpecialFinRisk());
            specialFinRiskType = Util.trim(l140m01a.getSpecialFinRiskType());
            // J-110-0382_05097_B1001 Web e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
            if (Util.equals(specialFinRiskType, "1")) {
                // 專案融資
                isProjectFinOperateStag = Util.trim(l140m01a
                        .getIsProjectFinOperateStag());
                //高品質專案融資分類
                isHighQualityProjOpt_1 = Util.trim(l140m01a.getIsHighQualityProjOpt_1());
                isHighQualityProjOpt_2 = Util.trim(l140m01a.getIsHighQualityProjOpt_2());
                isHighQualityProjOpt_3 = Util.trim(l140m01a.getIsHighQualityProjOpt_3());
                isHighQualityProjOpt_4 = Util.trim(l140m01a.getIsHighQualityProjOpt_4());
                isHighQualityProjOpt_5 = Util.trim(l140m01a.getIsHighQualityProjOpt_5());
                isHighQualityProjResult = Util.trim(l140m01a.getIsHighQualityProjResult());
            }
        }

        // J-109-0351_05097_B1001 e-Loan企金「青年創業及啟動金貸款」簽報書修改
        String lnType = "";
        String yoPurpose = "";
        String subSidyut = "";

        if (LMSUtil.isClsCase(meta) || LMSUtil.isOverSea_CLS(meta)) {
            // 消金

        } else {
            // 企金
            lnType = Util.trim(l140m01a.getLnType());
            yoPurpose = "";
            subSidyut = "";
            boolean needChkLnType61 = lmsService
                    .needChkLnType61ApplyDateAfter20200801(
                            Util.trim(l140m01a.getLnType()),
                            l140m01a.getApplyDate());

            if (needChkLnType61) {
                yoPurpose = Util.trim(l140m01a.getYoPurpose());
                subSidyut = Util.trim(l140m01a.getSubSidyut());
            }
        }
        //G-113-0145 授信新做額度於eloan簽報核准後，自動傳送AS400執行3X02，以利央行RDT報表傳送。
        //只針對海外泰國地區分行
        String appNo = Util.trim(l140m01a.getAppNo());//申請案件號碼
        String contType = Util.trim(l140m01a.getLoanAndContType());
        String curr = Util.trim(l140m01a.getCurrentApplyCurr());//新做額度幣別
        BigDecimal curAmt = l140m01a.getCurrentApplyAmt();//新做額度金額
        String currL = Util.trim(l140m01a.getCurrentApplyCurr());//最後額度幣別
        BigDecimal curAmtL = l140m01a.getCurrentApplyAmt();//最後額度金額
        String finalappDate = "0";
        if(Util.isNotEmpty(l140m01a.getApproveTime())){
            finalappDate = CapDate.formatDate(l140m01a.getApproveTime(),
                    UtilConstants.DateFormat.YYYY_MM_DD).replaceAll("\\D", "");
        }

        if (ellnseeks.isEmpty()) {
            misEllnseekservice.insert(custId, dupNo, cntrNo, brNo, status,
                    apprYY, apprMM, approLvl, updater, gutcDate, projNO,
                    property, byNewOld, lnuseNo, useFmDt, useEnDt, useFtMn,
                    hasAmFee, experf_fg, flaw_fg, flaw_amt, ELF461_ISREVIVE_Y,
                    ELF461_MAINID, ELF461_ISOFCLCGA, ELF461_CGA_COUNTRY,
                    ELF461_CGA_CRDTYPE, ELF461_CGA_CRDAREA, ELF461_CGA_CRDPRED,
                    ELF461_CGA_CRDGRAD, ELF461_CGA_RSKRTO, ELF461_CGA_GRADSCR,
                    isSpecialFinRisk, specialFinRiskType, isCmsAdcRisk, lnType,
                    yoPurpose, subSidyut, isProjectFinOperateStag,
                    isHighQualityProjOpt_1, isHighQualityProjOpt_2,
                    isHighQualityProjOpt_3, isHighQualityProjOpt_4,
                    isHighQualityProjOpt_5, isHighQualityProjResult,
                    curr, curAmt, currL, curAmtL);
        } else {
            misEllnseekservice.updateOnlyRevive(custId, dupNo, cntrNo,
                    ELF461_ISREVIVE_Y, ELF461_MAINID);
        }
        BigDecimal now = LMSUtil.covertAs400Time(CapDate.getCurrentTimestamp());

        BigDecimal tempgutcDate = null;
        if (gutcDate == null) {
            tempgutcDate = BigDecimal.ZERO;
        } else {
            tempgutcDate = new BigDecimal(gutcDate.toString().replaceAll("\\D",
                    ""));
        }
        // '２０１２春武里（兆）授字第００３２８號'
        if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(
                meta.getCaseBrId()).getBrNoFlag())) {
            if (obsdbELF461Service.findByKey(BRNID, custId, dupNo, cntrNo)
                    .isEmpty()) {
                obsdbELF461Service.insert(BRNID, custId, dupNo, cntrNo, brNo,
                        status, apprYY, apprMM, approLvl, updater,
                        tempgutcDate, projNO, property, now, ELF461_ISREVIVE_Y,
                        ELF461_MAINID, ELF461_ISOFCLCGA, ELF461_CGA_COUNTRY,
                        ELF461_CGA_CRDTYPE, ELF461_CGA_CRDAREA,
                        ELF461_CGA_CRDPRED, ELF461_CGA_CRDGRAD,
                        ELF461_CGA_RSKRTO, ELF461_CGA_GRADSCR,
                        isSpecialFinRisk, specialFinRiskType, isCmsAdcRisk,
                        isProjectFinOperateStag, isHighQualityProjOpt_1,
                        isHighQualityProjOpt_2, isHighQualityProjOpt_3,
                        isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult,
                        finalappDate, appNo, contType,
                        curr, curAmt, currL, curAmtL);
            } else {
                obsdbELF461Service.updateOnlyRevive(BRNID, custId, dupNo,
                        cntrNo, ELF461_ISREVIVE_Y, ELF461_MAINID);
            }
        }

        // 額度前三碼分行也要上傳
        if (Util.notEquals(cntrNo, "")) {
            String cntrBrNo = Util.getLeftStr(cntrNo, 3);
            if (Util.notEquals(cntrBrNo, BRNID)) {
                if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(
                        cntrBrNo).getBrNoFlag())) {
                    if (obsdbELF461Service.findByKey(cntrBrNo, custId, dupNo,
                            cntrNo).isEmpty()) {
                        obsdbELF461Service.insert(cntrBrNo, custId, dupNo,
                                cntrNo, brNo, status, apprYY, apprMM, approLvl,
                                updater, tempgutcDate, projNO, property, now,
                                ELF461_ISREVIVE_Y, ELF461_MAINID,
                                ELF461_ISOFCLCGA, ELF461_CGA_COUNTRY,
                                ELF461_CGA_CRDTYPE, ELF461_CGA_CRDAREA,
                                ELF461_CGA_CRDPRED, ELF461_CGA_CRDGRAD,
                                ELF461_CGA_RSKRTO, ELF461_CGA_GRADSCR,
                                isSpecialFinRisk, specialFinRiskType,
                                isCmsAdcRisk, isProjectFinOperateStag,
                                isHighQualityProjOpt_1, isHighQualityProjOpt_2,
                                isHighQualityProjOpt_3, isHighQualityProjOpt_4,
                                isHighQualityProjOpt_5, isHighQualityProjResult,
                                finalappDate, appNo, contType,
                                curr, curAmt, currL, curAmtL);
                    } else {
                        obsdbELF461Service
                                .updateOnlyRevive(cntrBrNo, custId, dupNo,
                                        cntrNo, ELF461_ISREVIVE_Y,
                                        ELF461_MAINID);
                    }
                }
            }
        }

    }

    /**
     * J-108-0345 貸後管理 最新一筆維護資料之附件檔案
     */
    @Override
    @NonTransactional
    public String doLmsBatch0031(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        try {
            List<Map<String, Object>> list = eloandbService
                    .findL260M01DLatest();
            // UNID 撈 elf602 然後 塞oid進新欄位
            if (list != null && !list.isEmpty()) {
                for (Map<String, Object> map : list) {
                    String UNID = Util.trim(MapUtils.getObject(map, "UNID"));
                    String OID = Util.trim(MapUtils.getObject(map, "OID"));
                    misdbBASEService.updateLatestELF602(OID, UNID);
                }
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();
    }

    /**
     * 定期檢視 BDOCFILE
     */
    @Override
    @NonTransactional
    public String doLmsBatch0032(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        int delCount = 0;
        try {
            List<Map<String, Object>> list = eloandbService
                    .findBDocFileByL260M01D();
            if (list != null && !list.isEmpty()) {
                for (Map<String, Object> map : list) {
                    String fid = Util.trim(MapUtils.getObject(map, "OID"));
                    if (docFileService.delete(fid)) {
                        delCount++;
                    }
                }
            }
            logger.info("doLmsBatch0032 excute result Count:" + delCount);
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();
    }

    /**
     * M-109-0210_05097_B1001 Web e-Loan企金授信配合DW產生額度未引入帳務系統之報表，上傳授信科目到ELF447N
     */
    @Override
    @NonTransactional
    public String doLmsBatch0033(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");

        String dataStartDate = Util.trim(request.getString("dataStartDate"));
        String dataEndDate = Util.trim(request.getString("dataEndDate"));

        if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
            errMsg.append("無法取得完整資料期間 " + dataStartDate + "~" + dataEndDate
                    + " ");
            System.out.println(errMsg.toString());
            return errMsg.toString();

        }

        List<Map<String, Object>> listL120m01a = eloandbService
                .findL120m01aByEndDate(dataStartDate, dataEndDate);

        if (listL120m01a != null && !listL120m01a.isEmpty()) {

            for (Map<String, Object> l120Map : listL120m01a) {
                String docType = Util.trim(MapUtils.getObject(l120Map,
                        "DOCTYPE"));
                String ELF447N_UNID = "";
                String ELF447N_CUSTID = "";
                String ELF447N_DUPNO = "";
                String ELF447N_ACT_CODE = "";
                String ELF447N_CONTRACT = "";

                ELF447N_UNID = Util.trim(MapUtils.getObject(l120Map, "MAINID"));

                L120M01A meta = l120m01aDao.findByMainId(ELF447N_UNID);

                if (Util.equals(docType, "1") || LMSUtil.isOverSea_CLS(meta)) {

                    // 企金 或海外消金

                    String itemType = lmsService.checkL140M01AItemType(meta);

                    List<L140M01A> l140m01as = l140m01aDao
                            .findL140m01aListByL120m01cMainId(meta.getMainId(),
                                    itemType, null);

                    if (l140m01as != null && !l140m01as.isEmpty()) {

                        for (L140M01A l140m01a : l140m01as) {
                            ELF447N_CUSTID = Util.trim(l140m01a.getCustId());
                            ELF447N_DUPNO = Util.trim(l140m01a.getDupNo());
                            ELF447N_CONTRACT = Util.trim(l140m01a.getCntrNo());
                            ELF447N_ACT_CODE = Util.trimSizeInOS390(
                                    Util.trim(l140m01a.getLnSubject()), 255);

                            // 因為國內與海外簽報書都會寫MIS.ELF506，所以以MIS.ELF506當頭
                            Map<String, Object> map447n = misELF447nService
                                    .findByUnidAndCntrNo(ELF447N_UNID,
                                            ELF447N_CONTRACT);
                            if (map447n != null && !map447n.isEmpty()) {
                                misELF447nService
                                        .updateActCodeByUnidCustIdCntrNo(
                                                ELF447N_UNID, ELF447N_CUSTID,
                                                ELF447N_DUPNO,
                                                ELF447N_CONTRACT,
                                                ELF447N_ACT_CODE);
                            }

                        }

                    }

                }

            }

        }

        return errMsg.toString();
    }

    /**
     * J-109-0290 實地覆審提醒mail
     */
    @Override
    @NonTransactional
    public String doLmsBatch0034(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        String baseDateStr = Util.trim(request.getString("baseDate"));
        Date baseDate = null;
        if (Util.isEmpty(baseDateStr)) {
            // batch 無指定日 => batch Date當月 +3個月-1天 為基準日
            // ex. 8月x日 跑 基準日為 11月1日-1 => 10月31日
            // 因為 gfnCTL_Caculate_DueDate 計算下次覆審日 是比對 月份
            // LMSUtil.cmp_yyyyMM(baseDate, ">=", calcDate)
            Date thisMonth = CapDate.parseDate(CapDate
                    .getCurrentDate(UtilConstants.DateFormat.YYYY_MM) + "-01");
            baseDateStr = CapDate.formatDate(thisMonth,
                    UtilConstants.DateFormat.YYYY_MM_DD);
            baseDate = CapDate.shiftDays(
                    CapDate.addMonth(Util.parseDate(baseDateStr), 3), -1);
        } else {
            // 如果有指定基準日
            baseDate = Util.parseDate(baseDateStr);
        }
        // logger.info("批次日：" + CapDate.formatDate(new Date(),
        // UtilConstants.DateFormat.YYYY_MM_DD)
        // + "，基準日：" + CapDate.formatDate(baseDate,
        // UtilConstants.DateFormat.YYYY_MM_DD));
        try {
            StringBuffer subject = new StringBuffer();
            subject.append("【提醒！屬董事會（或常董會）權限核定之企業戶授信案件未來三個月屆期應辦理實地覆審名單】");
            // 取得所有分行
            List<IBranch> allBranch = branchService.getAllBranch();
            if (!allBranch.isEmpty()) {
                for (IBranch branch : allBranch) {
                    String brNo = Util.trim(branch.getBrNo());
                    if (Util.isNotEmpty(brNo)) {
                        int bodyCnt = 0;
                        StringBuffer body = new StringBuffer();
                        List<ELF412B> allList = misELF412BService
                                .findByBranch(brNo);

                        // step 1.取得名單
                        // 參考 L180R19B 逾期未覆審報表有排除 有不覆審註記的案件
                        TreeMap<String, ELF412B> needMap = new TreeMap<String, ELF412B>();
                        for (ELF412B elf412b : allList) {
                            String nckdFlag = Util.trim(elf412b
                                    .getElf412b_nckdFlag());
                            if (LrsUtil.isNckdFlag_EMPTY_8(nckdFlag)
                                    || Util.equals("0", nckdFlag)) {
                                needMap.put(LMSUtil.getCustKey_len10custId(
                                        elf412b.getElf412b_custId(),
                                        elf412b.getElf412b_dupNo()), elf412b);
                            }
                        }

                        // step 2. 計算下次覆審日
                        for (String custIdDup : needMap.keySet()) {
                            ELF412B elf412b = needMap.get(custIdDup);
                            RO412 ro412 = new RO412(elf412b);

                            Date nextDate = retrialService
                                    .gfnCTL_Caculate_DueDate("4", baseDate,
                                            null, ro412, null);
                            if (nextDate == null) {
                                continue;
                            } else {
                                Map<String, Object> custMap = customerService
                                        .findByIdDupNo(
                                                elf412b.getElf412b_custId(),
                                                elf412b.getElf412b_dupNo());
                                String custName = Util.trim(MapUtils.getString(
                                        custMap, "CNAME"));
                                body.append("客戶：")
                                        .append(custIdDup)
                                        .append("　　")
                                        .append(custName)
                                        .append("，最遲應覆審日：")
                                        .append(CapDate
                                                .formatDate(
                                                        nextDate,
                                                        UtilConstants.DateFormat.YYYY_MM_DD))
                                        .append("\r\n");
                                bodyCnt++;
                            }
                        }

                        if (bodyCnt > 0) {
                            // step 3. Send E-Mail
                            String hostAddr = sysParameterService
                                    .getParamValue(SysParamConstants.MAIL_ADDRESS_HOST);
                            boolean isTestEmail = true; // 是否為測試信件

                            if (Util.equals(Util.trim(hostAddr), "")) {
                                isTestEmail = "true".equals(PropUtil
                                        .getProperty("isTestEmail")) ? true
                                        : false; // 是否為測試信件
                            } else {
                                if (StringUtils.indexOf(hostAddr, "@notes.") >= 0) { // Production
                                    isTestEmail = false;
                                } else {
                                    isTestEmail = true;
                                }
                            }

                            List<String> recv = new ArrayList<String>();
                            recv.clear();
                            if (isTestEmail) {
                                recv.add("<EMAIL>");
                            } else {
                                recv.add(brNo + "<EMAIL>");
                            }

                            int recvSize = recv.size();
                            String[] aRecv = new String[recvSize];
                            Object[] oRecv = recv.toArray();
                            // 將傳送者Email型別Object[]改為String[]
                            for (int j = 0; j < recvSize; j++) {
                                aRecv[j] = Util.trim(oRecv[j]);
                            }
                            logger.debug("toAddr:{},subject:{},body:{}",
                                    new Object[] { aRecv, subject.toString(),
                                            body.toString() });
                            emailClient.send(true, null, aRecv,
                                    subject.toString(), body.toString());
                        }
                        logger.info("分行：" + brNo + "，資料筆數：" + bodyCnt);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }

        return errMsg.toString();
    }

    /**
     * J-108-0345 貸後管理 最新一筆維護資料之證明文件說明
     */
    @Override
    @NonTransactional
    public String doLmsBatch0035(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        try {
            List<Map<String, Object>> list = misdbBASEService
                    .queryElf602HadMaintainList();
            if (list != null && !list.isEmpty()) {
                for (Map<String, Object> map : list) {
                    // L260M01D 的 oid
                    String oid = Util.trim(MapUtils.getObject(map,
                            "ELF602_FIELDMAINID"));
                    String unid = Util.trim(MapUtils.getObject(map,
                            "ELF602_UNID"));
                    String fileDesc = "";
                    if (Util.isNotEmpty(oid)) {
                        L260M01D l260m01d = l260m01dDao.findByOid(oid);
                        if (l260m01d != null) {
                            fileDesc = (l260m01d.getFileDesc() == null ? ""
                                    : l260m01d.getFileDesc());
                        }
                        misdbBASEService.updateLatestELF602FileDesc(fileDesc,
                                unid);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();
    }

    /**
     * 更新主從債務人最佳信用品質保證人的國別註記
     */
    @Override
    @NonTransactional
    public String doLmsBatch0036(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        String maxCycMn = "";
        if (true) {
            Map<String, Object> maxData = dwdbBASEService
                    .findDW_BSL2ECRSCRMaxCycMn();

            if (maxData != null && !maxData.isEmpty()) {
                maxCycMn = Util.trim(MapUtils.getString(maxData, "CYC_MN", ""));
            }
        }

        if (Util.equals(maxCycMn, "")) {
            maxCycMn = "2020-11-01";
        }

        Map<String, Object> lnf25cType1 = new TreeMap<String, Object>();
        List<Map<String, Object>> lnf25cListType1 = misdbBASEService
                .getLnf25cAllMowScoreMappingByType("1");
        if (lnf25cListType1 != null && !lnf25cListType1.isEmpty()) {
            for (Map<String, Object> lnf25cMap1 : lnf25cListType1) {
                String LNF25C_MAP_VALUE1 = Util.trim(MapUtils.getString(
                        lnf25cMap1, "LNF25C_MAP_VALUE1"));
                String LNF25C_MAP_VALUE2 = Util.trim(MapUtils.getString(
                        lnf25cMap1, "LNF25C_MAP_VALUE2"));
                lnf25cType1.put(LNF25C_MAP_VALUE1, LNF25C_MAP_VALUE2);
            }

        }

        Map<String, Object> lnf25cType4 = new TreeMap<String, Object>();
        List<Map<String, Object>> lnf25cListType4 = misdbBASEService
                .getLnf25cAllMowScoreMappingByType("4");
        if (lnf25cListType4 != null && !lnf25cListType4.isEmpty()) {
            for (Map<String, Object> lnf25cMap4 : lnf25cListType4) {
                String LNF25C_MAP_VALUE1 = Util.trim(MapUtils.getString(
                        lnf25cMap4, "LNF25C_MAP_VALUE1"));
                String LNF25C_MAP_VALUE2 = Util.trim(MapUtils.getString(
                        lnf25cMap4, "LNF25C_MAP_VALUE2"));
                lnf25cType4.put(LNF25C_MAP_VALUE1, LNF25C_MAP_VALUE2);
            }

        }

        List<Map<String, Object>> dataList = misdbBASEService
                .getLnf020WithEllngtee();

        if (dataList == null || dataList.isEmpty()) {
            errMsg.append("無資料需更新");
            return errMsg.toString();
        }

        String gBrNo = "";
        String gCustId = "";
        String gDupNo = "";
        String gCntrNo = "";

        ArrayList<Map<String, Object>> groupList = new ArrayList<Map<String, Object>>();

        for (Map<String, Object> dataMap : dataList) {

            String brNo = MapUtils.getString(dataMap, "BRNO");
            String custId = MapUtils.getString(dataMap, "CUSTID");
            String dupNo = MapUtils.getString(dataMap, "DUPNO");
            String cntrNo = MapUtils.getString(dataMap, "CNTRNO");

            if (Util.equals(gCustId, "")) {
                gBrNo = brNo;
                gCustId = custId;
                gDupNo = dupNo;
                gCntrNo = cntrNo;
            } else if (Util.notEquals(brNo, gBrNo)
                    || Util.notEquals(custId, gCustId)
                    || Util.notEquals(dupNo, gDupNo)
                    || Util.notEquals(cntrNo, gCntrNo)) {

                // 換新一個群組
                if (groupList != null && !groupList.isEmpty()) {
                    errMsg.append(processEllngteeData(groupList, maxCycMn,
                            lnf25cType1, lnf25cType4));
                    if (Util.notEquals(errMsg.toString(), "")) {
                        return errMsg.toString();
                    }
                }

                // *********************************************
                gBrNo = brNo;
                gCustId = custId;
                gDupNo = dupNo;
                gCntrNo = cntrNo;
                groupList.clear();
                groupList = new ArrayList<Map<String, Object>>();
            }

            groupList.add(dataMap);

        }

        // 最後一個群組
        if (groupList != null && !groupList.isEmpty()) {
            errMsg.append(processEllngteeData(groupList, maxCycMn, lnf25cType1,
                    lnf25cType4));
            if (Util.notEquals(errMsg.toString(), "")) {
                return errMsg.toString();
            }
        }

        return errMsg.toString();
    }

    private String processEllngteeData(
            ArrayList<Map<String, Object>> groupList, String maxCycMn,
            Map<String, Object> lnf25cType1, Map<String, Object> lnf25cType4) {
        StringBuffer errMsg = new StringBuffer("");

        BigDecimal gDefaultScore999 = Util.parseBigDecimal("999");

        // J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
        // 配合中央銀行國家風險統計表修訂，風險移轉原則由徵提1個以上保證人時，按「約定保證比率或按保證人人數均分填報」改為按「約定保證比率或保證人信用品質高低填報」。
        // 1.ELOAN資料建檔及授信簽報主從債務人針對保證人(一般保證人或連帶保證人)，若該等保證人保證比例皆為100%時，必須就保證人中註記一戶為信用品質最優者
        // 2.對於現有名單中非屬約定保證比率者，以系統依據下列優先順序指定信用品質最優者，保證人信評最佳者-->保證人與最終風險國別相同者-->保證人與借款人相同國別者-->保證人之國家評等最優者
        // 3.國內(ALOAN)及海外(AS400)依據約定保證比率或保證人信用品質高低認定移轉暴險

        // 1.只有借款人為企業戶且保證人為企業戶時才需要填列優先順序，由經辦自行決定之順序，合理的值域為1~990。
        // 2.ELOAN授信動審表、資料建檔主從債務人建檔，提供引進預設排序按紐，依保證人有評等設定優先順序，評等採用部分，優先使用外部評等，其次為內部評等(借款人最新一筆評等，不限評等日期須兩年內)，如果有順序之保證比例相加已達100，後面若尚有無評等之保證人時，就不強迫一定要填順序。
        // 3.由批次整批塞預設值時，ELF401_PRIORITY 按照優先順序值域如下:
        // Step1->991.保證人國別都相同或只有一個保證人
        // Step2->992.裡面有非100的，則以最大的當代表............????????.....要不要再調整
        // Step3->993.保證人RATING最高
        // Step4->994.任一保證人國別與額度最終風險國別相同
        // Step5->995.任一保證人國別與借款人相同
        // Step6->996.保證人國家RATING最高
        // Step6->997.無法判斷(非以上STEP)
        //

        // 如果裡面已經有Y，代表分行維護過，就不要更新該群組***********************************************
        for (Map<String, Object> dataMap : groupList) {
            String LNF020_CONTRACT = Util.trim(MapUtils.getString(dataMap,
                    "LNF020_CONTRACT"));
            String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
            String CUSTID = Util.trim(MapUtils.getString(dataMap, "CUSTID"));
            String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
            String CNTRNO = Util.trim(MapUtils.getString(dataMap, "CNTRNO"));
            String LNGEID = Util.trim(MapUtils.getString(dataMap, "LNGEID"));
            String DUPNO1 = Util.trim(MapUtils.getString(dataMap, "DUPNO1"));
            String LNGENM = Util.trim(MapUtils.getString(dataMap, "LNGENM"));
            String NTCODE = Util.trim(MapUtils.getString(dataMap, "NTCODE"));
            String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(dataMap,
                    "CM25_MOTHER_NATION"));
            String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(dataMap,
                    "NEW_MOTHER_NATION"));
            String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                    "LNF020_RISK_AREA"));
            BigDecimal GRTRT = Util.equals(
                    Util.trim(MapUtils.getString(dataMap, "GRTRT", "")), "") ? BigDecimal.ZERO
                    : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                    dataMap, "GRTRT", "")));
            String LNGEFLAG = Util
                    .trim(MapUtils.getString(dataMap, "LNGEFLAG"));

            BigDecimal PRIORITY = Util.equals(
                    Util.trim(MapUtils.getString(dataMap, "PRIORITY", "")), "") ? BigDecimal.ZERO
                    : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                    dataMap, "PRIORITY", "")));

            if (PRIORITY.compareTo(BigDecimal.ZERO) > 0
                    && PRIORITY.compareTo(Util.parseBigDecimal("990")) < 0) {
                // 0:沒有順位
                // 990以上:系統首次塞值
                // 大於0或是小於990 代表分行維護過，就不要更新該群組，結束
                return errMsg.toString();
            }

        }

        // STEP0.所有保證人的保證比例都是0 或
        //
        // 100才要執行下列判斷**************************************************************************************
        if (false) {
            // 金爺說不論有沒有比例，或比例多少，都要PRIORITY，所以此段檢核不執行
            for (Map<String, Object> dataMap : groupList) {

                String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String LNGEID = Util
                        .trim(MapUtils.getString(dataMap, "LNGEID"));
                String DUPNO1 = Util
                        .trim(MapUtils.getString(dataMap, "DUPNO1"));
                String NTCODE = Util.trim(MapUtils.getString(dataMap, "NTCODE",
                        "TW"));
                String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "CM25_MOTHER_NATION"));
                String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "NEW_MOTHER_NATION"));
                String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                        "LNF020_RISK_AREA"));
                BigDecimal GRTRT = Util
                        .equals(Util.trim(MapUtils.getString(dataMap, "GRTRT",
                                "")), "") ? BigDecimal.ZERO : Util
                        .parseBigDecimal(Util.trim(MapUtils.getString(dataMap,
                                "GRTRT", "")));
                String LNGEFLAG = Util.trim(MapUtils.getString(dataMap,
                        "LNGEFLAG"));

                if (GRTRT.compareTo(BigDecimal.ZERO) != 0
                        && GRTRT.compareTo(Util.parseBigDecimal("100")) != 0) {

                    // 保證人保證比例不是都100，就不用更新了
                    return errMsg.toString();

                }

            }
        }
        // STEP
        // 1.保證人國別都相同或只有一個保證人**************************************************************
        String gNtCode = "";
        boolean sameNtCode = true;
        if (true) {
            for (Map<String, Object> dataMap : groupList) {

                String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String LNGEID = Util
                        .trim(MapUtils.getString(dataMap, "LNGEID"));
                String DUPNO1 = Util
                        .trim(MapUtils.getString(dataMap, "DUPNO1"));
                String NTCODE = Util.trim(MapUtils.getString(dataMap, "NTCODE",
                        "TW"));
                String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "CM25_MOTHER_NATION"));
                String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "NEW_MOTHER_NATION"));
                String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                        "LNF020_RISK_AREA"));
                BigDecimal GRTRT = Util
                        .equals(Util.trim(MapUtils.getString(dataMap, "GRTRT",
                                "")), "") ? BigDecimal.ZERO : Util
                        .parseBigDecimal(Util.trim(MapUtils.getString(dataMap,
                                "GRTRT", "")));
                String LNGEFLAG = Util.trim(MapUtils.getString(dataMap,
                        "LNGEFLAG"));

                if (Util.equals(gNtCode, "")) {
                    gNtCode = NTCODE;
                } else {
                    if (Util.notEquals(gNtCode, NTCODE)) {
                        // 國別有不同，就走下一個判斷條件STEP
                        sameNtCode = false;
                        break;
                    }
                }

            }

            if (sameNtCode) {
                // 更新第一筆
                for (Map<String, Object> dataMap : groupList) {

                    String BRNO = Util
                            .trim(MapUtils.getString(dataMap, "BRNO"));
                    String CUSTID = Util.trim(MapUtils.getString(dataMap,
                            "CUSTID"));
                    String DUPNO = Util.trim(MapUtils.getString(dataMap,
                            "DUPNO"));
                    String CNTRNO = Util.trim(MapUtils.getString(dataMap,
                            "CNTRNO"));
                    String LNGEID = Util.trim(MapUtils.getString(dataMap,
                            "LNGEID"));
                    String DUPNO1 = Util.trim(MapUtils.getString(dataMap,
                            "DUPNO1"));
                    String NTCODE = Util.trim(MapUtils.getString(dataMap,
                            "NTCODE"));
                    String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(
                            dataMap, "CM25_MOTHER_NATION"));
                    String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(
                            dataMap, "NEW_MOTHER_NATION"));
                    String LNF020_RISK_AREA = Util.trim(MapUtils.getString(
                            dataMap, "LNF020_RISK_AREA"));
                    BigDecimal GRTRT = Util
                            .equals(Util.trim(MapUtils.getString(dataMap,
                                    "GRTRT", "")), "") ? BigDecimal.ZERO : Util
                            .parseBigDecimal(Util.trim(MapUtils.getString(
                                    dataMap, "GRTRT", "")));
                    String LNGEFLAG = Util.trim(MapUtils.getString(dataMap,
                            "LNGEFLAG"));

                    BigDecimal PRIORITY = Util.parseBigDecimal("991");

                    // 更新資料
                    errMsg.append(updateEllngteePriorFg(BRNO, CUSTID, DUPNO,
                            CNTRNO, LNGEID, DUPNO1, LNGEFLAG, PRIORITY));
                    return errMsg.toString();

                }
            }
        }

        // STEP
        // 2.裡面有非100的，則以最大的當代表**************************************************************
        // X不執行
        if (false) {

            for (Map<String, Object> dataMap : groupList) {

                String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String LNGEID = Util
                        .trim(MapUtils.getString(dataMap, "LNGEID"));
                String DUPNO1 = Util
                        .trim(MapUtils.getString(dataMap, "DUPNO1"));
                String NTCODE = Util
                        .trim(MapUtils.getString(dataMap, "NTCODE"));
                String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "CM25_MOTHER_NATION"));
                String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "NEW_MOTHER_NATION"));
                String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                        "LNF020_RISK_AREA"));
                BigDecimal GRTRT = Util
                        .equals(Util.trim(MapUtils.getString(dataMap, "GRTRT",
                                "")), "") ? BigDecimal.ZERO : Util
                        .parseBigDecimal(Util.trim(MapUtils.getString(dataMap,
                                "GRTRT", "")));
                String LNGEFLAG = Util.trim(MapUtils.getString(dataMap,
                        "LNGEFLAG"));

                BigDecimal PRIORITY = Util.parseBigDecimal("992");

                if (GRTRT.compareTo(BigDecimal.ZERO) > 0
                        && GRTRT.compareTo(Util.parseBigDecimal("100")) < 0) {

                    // 更新資料
                    errMsg.append(updateEllngteePriorFg(BRNO, CUSTID, DUPNO,
                            CNTRNO, LNGEID, DUPNO1, LNGEFLAG, PRIORITY));
                    return errMsg.toString();
                }

            }
        }

        // STEP 3. 保證人RATING
        // 最高************************************************************************************
        if (true) {

            Map<String, Object> bestGuarantorRisk = new TreeMap<String, Object>();
            BigDecimal gMinGuarantorScore = gDefaultScore999;
            for (Map<String, Object> dataMap : groupList) {

                String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String LNGEID = Util
                        .trim(MapUtils.getString(dataMap, "LNGEID"));
                String DUPNO1 = Util
                        .trim(MapUtils.getString(dataMap, "DUPNO1"));
                String NTCODE = Util
                        .trim(MapUtils.getString(dataMap, "NTCODE"));
                String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "CM25_MOTHER_NATION"));
                String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "NEW_MOTHER_NATION"));
                String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                        "LNF020_RISK_AREA"));
                BigDecimal GRTRT = Util
                        .equals(Util.trim(MapUtils.getString(dataMap, "GRTRT",
                                "")), "") ? BigDecimal.ZERO : Util
                        .parseBigDecimal(Util.trim(MapUtils.getString(dataMap,
                                "GRTRT", "")));
                String LNGEFLAG = Util.trim(MapUtils.getString(dataMap,
                        "LNGEFLAG"));

                // 外部評等
                Map<String, Object> dwMap = dwdbBASEService
                        .findDW_BSL2ECRSCRByCustKeyAndCycMn(LNGEID, DUPNO1,
                                maxCycMn);
                if (dwMap != null && !dwMap.isEmpty()) {
                    BigDecimal SCORE = Util.equals(
                            Util.trim(MapUtils.getString(dwMap, "SCORE", "")),
                            "") ? gDefaultScore999 : Util.parseBigDecimal(Util
                            .trim(MapUtils.getString(dwMap, "SCORE", "")));
                    // 分數越小越好
                    if (SCORE.compareTo(gMinGuarantorScore) < 0) {
                        gMinGuarantorScore = SCORE;
                        bestGuarantorRisk = dataMap;
                    }
                } else {
                    // 若沒有外部評等，則抓內部評等
                    Map<String, Object> mowMap = misdbBASEService
                            .getMowtbl1LastFrByCustIdForCountryRisk(LNGEID,
                                    DUPNO1);

                    if (mowMap != null && !mowMap.isEmpty()) {
                        String MOWTYPE = Util.trim(MapUtils.getString(mowMap,
                                "MOWTYPE"));
                        String FR = Util.trim(MapUtils.getString(mowMap, "FR"));
                        String LNF25C_MAP_VALUE2 = "";
                        if (Util.notEquals(FR, "")) {
                            if (Util.equals(MOWTYPE, "F")
                                    || Util.equals(MOWTYPE, "5")
                                    || Util.equals(MOWTYPE, "O")) {
                                LNF25C_MAP_VALUE2 = Util.trim(lnf25cType1
                                        .get(FR));
                            } else {
                                LNF25C_MAP_VALUE2 = Util.trim(lnf25cType4
                                        .get(FR));
                            }
                        }

                        if (Util.notEquals(LNF25C_MAP_VALUE2, "")) {

                            if (Util.notEquals(LNF25C_MAP_VALUE2, "")) {
                                BigDecimal LNF25C_SCORE = Util
                                        .parseBigDecimal(LNF25C_MAP_VALUE2);
                                // 分數越小越好
                                if (LNF25C_SCORE.compareTo(gMinGuarantorScore) < 0) {
                                    gMinGuarantorScore = LNF25C_SCORE;
                                    bestGuarantorRisk = dataMap;
                                }
                            }

                        }

                    }

                }

            }

            // 有最高的保證人評等
            if (gMinGuarantorScore.compareTo(gDefaultScore999) < 0
                    && bestGuarantorRisk != null
                    && !bestGuarantorRisk.isEmpty()) {
                Map<String, Object> dataMap = bestGuarantorRisk;

                String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String LNGEID = Util
                        .trim(MapUtils.getString(dataMap, "LNGEID"));
                String DUPNO1 = Util
                        .trim(MapUtils.getString(dataMap, "DUPNO1"));
                String NTCODE = Util
                        .trim(MapUtils.getString(dataMap, "NTCODE"));
                String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "CM25_MOTHER_NATION"));
                String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "NEW_MOTHER_NATION"));
                String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                        "LNF020_RISK_AREA"));
                BigDecimal GRTRT = Util
                        .equals(Util.trim(MapUtils.getString(dataMap, "GRTRT",
                                "")), "") ? BigDecimal.ZERO : Util
                        .parseBigDecimal(Util.trim(MapUtils.getString(dataMap,
                                "GRTRT", "")));
                String LNGEFLAG = Util.trim(MapUtils.getString(dataMap,
                        "LNGEFLAG"));

                BigDecimal PRIORITY = Util.parseBigDecimal("993");

                errMsg.append(updateEllngteePriorFg(BRNO, CUSTID, DUPNO,
                        CNTRNO, LNGEID, DUPNO1, LNGEFLAG, PRIORITY));
                return errMsg.toString();

            }
        }

        // STEP
        // 4.任一保證人國別與額度最終風險國別相同**************************************************************
        for (Map<String, Object> dataMap : groupList) {

            String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
            String CUSTID = Util.trim(MapUtils.getString(dataMap, "CUSTID"));
            String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
            String CNTRNO = Util.trim(MapUtils.getString(dataMap, "CNTRNO"));
            String LNGEID = Util.trim(MapUtils.getString(dataMap, "LNGEID"));
            String DUPNO1 = Util.trim(MapUtils.getString(dataMap, "DUPNO1"));
            String NTCODE = Util.trim(MapUtils.getString(dataMap, "NTCODE"));
            String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(dataMap,
                    "CM25_MOTHER_NATION"));
            String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(dataMap,
                    "NEW_MOTHER_NATION"));
            String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                    "LNF020_RISK_AREA"));
            BigDecimal GRTRT = Util.equals(
                    Util.trim(MapUtils.getString(dataMap, "GRTRT", "")), "") ? BigDecimal.ZERO
                    : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                    dataMap, "GRTRT", "")));
            String LNGEFLAG = Util
                    .trim(MapUtils.getString(dataMap, "LNGEFLAG"));

            BigDecimal PRIORITY = Util.parseBigDecimal("994");

            if (Util.equals(LNF020_RISK_AREA, NTCODE)) {

                // 更新資料
                errMsg.append(updateEllngteePriorFg(BRNO, CUSTID, DUPNO,
                        CNTRNO, LNGEID, DUPNO1, LNGEFLAG, PRIORITY));
                return errMsg.toString();
            }

        }

        // STEP
        // 5.任一保證人國別與借款人相同**************************************************************
        for (Map<String, Object> dataMap : groupList) {

            String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
            String CUSTID = Util.trim(MapUtils.getString(dataMap, "CUSTID"));
            String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
            String CNTRNO = Util.trim(MapUtils.getString(dataMap, "CNTRNO"));
            String LNGEID = Util.trim(MapUtils.getString(dataMap, "LNGEID"));
            String DUPNO1 = Util.trim(MapUtils.getString(dataMap, "DUPNO1"));
            String NTCODE = Util.trim(MapUtils.getString(dataMap, "NTCODE"));
            String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(dataMap,
                    "CM25_MOTHER_NATION"));
            String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(dataMap,
                    "NEW_MOTHER_NATION"));
            String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                    "LNF020_RISK_AREA"));
            BigDecimal GRTRT = Util.equals(
                    Util.trim(MapUtils.getString(dataMap, "GRTRT", "")), "") ? BigDecimal.ZERO
                    : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                    dataMap, "GRTRT", "")));
            String LNGEFLAG = Util
                    .trim(MapUtils.getString(dataMap, "LNGEFLAG"));

            BigDecimal PRIORITY = Util.parseBigDecimal("995");

            if (Util.equals(NEW_MOTHER_NATION, NTCODE)) {

                // 更新資料
                errMsg.append(updateEllngteePriorFg(BRNO, CUSTID, DUPNO,
                        CNTRNO, LNGEID, DUPNO1, LNGEFLAG, PRIORITY));
                return errMsg.toString();
            }

        }

        // STEP 6. 保證人國家RATING
        // 最高************************************************************************************
        if (true) {
            Map<String, Object> bestCountryRisk = new TreeMap<String, Object>();
            BigDecimal gMinCountryScore = gDefaultScore999;
            for (Map<String, Object> dataMap : groupList) {

                String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String LNGEID = Util
                        .trim(MapUtils.getString(dataMap, "LNGEID"));
                String DUPNO1 = Util
                        .trim(MapUtils.getString(dataMap, "DUPNO1"));
                String NTCODE = Util
                        .trim(MapUtils.getString(dataMap, "NTCODE"));
                String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "CM25_MOTHER_NATION"));
                String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "NEW_MOTHER_NATION"));
                String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                        "LNF020_RISK_AREA"));
                BigDecimal GRTRT = Util
                        .equals(Util.trim(MapUtils.getString(dataMap, "GRTRT",
                                "")), "") ? BigDecimal.ZERO : Util
                        .parseBigDecimal(Util.trim(MapUtils.getString(dataMap,
                                "GRTRT", "")));
                String LNGEFLAG = Util.trim(MapUtils.getString(dataMap,
                        "LNGEFLAG"));

                Map<String, Object> dwMap = dwdbBASEService
                        .findDW_BSL2ECRSCRByCustKeyAndCycMn(NTCODE, "",
                                maxCycMn);
                if (dwMap != null && !dwMap.isEmpty()) {
                    BigDecimal SCORE = Util.equals(
                            Util.trim(MapUtils.getString(dwMap, "SCORE", "")),
                            "") ? gDefaultScore999 : Util.parseBigDecimal(Util
                            .trim(MapUtils.getString(dwMap, "SCORE", "")));
                    // 分數越小越好
                    if (SCORE.compareTo(gMinCountryScore) < 0) {
                        gMinCountryScore = SCORE;
                        bestCountryRisk = dataMap;
                    }
                }

            }

            // 有最高的國家評等
            if (gMinCountryScore.compareTo(gDefaultScore999) < 0
                    && bestCountryRisk != null && !bestCountryRisk.isEmpty()) {
                Map<String, Object> dataMap = bestCountryRisk;

                String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
                String CUSTID = Util
                        .trim(MapUtils.getString(dataMap, "CUSTID"));
                String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
                String CNTRNO = Util
                        .trim(MapUtils.getString(dataMap, "CNTRNO"));
                String LNGEID = Util
                        .trim(MapUtils.getString(dataMap, "LNGEID"));
                String DUPNO1 = Util
                        .trim(MapUtils.getString(dataMap, "DUPNO1"));
                String NTCODE = Util
                        .trim(MapUtils.getString(dataMap, "NTCODE"));
                String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "CM25_MOTHER_NATION"));
                String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(
                        dataMap, "NEW_MOTHER_NATION"));
                String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                        "LNF020_RISK_AREA"));
                BigDecimal GRTRT = Util
                        .equals(Util.trim(MapUtils.getString(dataMap, "GRTRT",
                                "")), "") ? BigDecimal.ZERO : Util
                        .parseBigDecimal(Util.trim(MapUtils.getString(dataMap,
                                "GRTRT", "")));
                String LNGEFLAG = Util.trim(MapUtils.getString(dataMap,
                        "LNGEFLAG"));

                BigDecimal PRIORITY = Util.parseBigDecimal("996");

                errMsg.append(updateEllngteePriorFg(BRNO, CUSTID, DUPNO,
                        CNTRNO, LNGEID, DUPNO1, LNGEFLAG, PRIORITY));
                return errMsg.toString();

            }
        }

        // STEP 7.
        // 無法判斷(非以上STEP1-6條件)************************************************************************************
        for (Map<String, Object> dataMap : groupList) {

            String BRNO = Util.trim(MapUtils.getString(dataMap, "BRNO"));
            String CUSTID = Util.trim(MapUtils.getString(dataMap, "CUSTID"));
            String DUPNO = Util.trim(MapUtils.getString(dataMap, "DUPNO"));
            String CNTRNO = Util.trim(MapUtils.getString(dataMap, "CNTRNO"));
            String LNGEID = Util.trim(MapUtils.getString(dataMap, "LNGEID"));
            String DUPNO1 = Util.trim(MapUtils.getString(dataMap, "DUPNO1"));
            String NTCODE = Util.trim(MapUtils.getString(dataMap, "NTCODE"));
            String CM25_MOTHER_NATION = Util.trim(MapUtils.getString(dataMap,
                    "CM25_MOTHER_NATION"));
            String NEW_MOTHER_NATION = Util.trim(MapUtils.getString(dataMap,
                    "NEW_MOTHER_NATION"));
            String LNF020_RISK_AREA = Util.trim(MapUtils.getString(dataMap,
                    "LNF020_RISK_AREA"));
            BigDecimal GRTRT = Util.equals(
                    Util.trim(MapUtils.getString(dataMap, "GRTRT", "")), "") ? BigDecimal.ZERO
                    : Util.parseBigDecimal(Util.trim(MapUtils.getString(
                    dataMap, "GRTRT", "")));
            String LNGEFLAG = Util
                    .trim(MapUtils.getString(dataMap, "LNGEFLAG"));

            BigDecimal PRIORITY = Util.parseBigDecimal("997");

            // 更新資料
            errMsg.append(updateEllngteePriorFg(BRNO, CUSTID, DUPNO, CNTRNO,
                    LNGEID, DUPNO1, LNGEFLAG, PRIORITY));
            return errMsg.toString();

        }

        return errMsg.toString();
    }

    private String updateEllngteePriorFg(String brNo, String custId,
                                         String dupNo, String cntrNo, String lnGeId, String dupNo1,
                                         String lngeFlag, BigDecimal priority) {
        StringBuffer errMsg = new StringBuffer("");

        misELLNGTEEService.clearPriorfg(brNo, custId, dupNo, cntrNo);

        misELLNGTEEService.updatePriorfg(brNo, custId, dupNo, cntrNo, lnGeId,
                dupNo1, lngeFlag, priority);

        return errMsg.toString();
    }

    /**
     * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
     */
    @Override
    @NonTransactional
    public String doLmsBatch0037(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");
        String exeYear = request.getString("exeYear");

        HashMap<String, String> yearMap = new HashMap<String, String>();

        yearMap.put("2020-01", "2020-12");
        yearMap.put("2019-01", "2019-12");
        yearMap.put("2018-01", "2018-12");

        for (String yearKey : yearMap.keySet()) {

            if (Util.notEquals(exeYear, yearKey)) {
                continue;
            }

            String bgnMonth = yearKey;
            String endMonth = yearMap.get(bgnMonth);

            List<Map<String, Object>> dataSourceList = misdbBASEService
                    .doLmsBatch0037_01(bgnMonth, endMonth);

            String TYPCD = "";
            String BRANCH = "";
            String CUSTID = "";
            String DUPNO = "";
            String COLLNO = "";
            String CURR = "";
            BigDecimal TIMEVAL = BigDecimal.ZERO;
            BigDecimal LAWVAL = BigDecimal.ZERO;
            BigDecimal APPAMT = BigDecimal.ZERO;
            String COLLSN = "";
            String SITE1 = "";
            String SITE2 = "";
            String SITE3 = "";
            String SITE4 = "";
            String SITE5 = "";
            String SITE6 = "";
            String SITE7 = "";
            String SITE8 = "";
            String SITE9 = "";
            String SITE10 = "";
            String SITE11 = "";
            String SITE12 = "";
            BigDecimal AREA = BigDecimal.ZERO;
            String USE = "";
            String SITE1NO = "";
            String SITE2NO = "";
            String SITE3NO = "";
            String USECD = "";
            String USETYPE = "";

            String upCntrNo = "";
            int upCount = 0;
            String upCity = "";
            String upIsZu = "";
            String upAddr = "";
            BigDecimal upTimeVal = BigDecimal.ZERO;

            BigDecimal doCount = BigDecimal.ZERO;

            boolean gFinalUp = false;

            LinkedHashMap<String, String> gCmsDoc = null;

            if (dataSourceList != null && !dataSourceList.isEmpty()) {

                for (Map<String, Object> dataSourceMap : dataSourceList) {

                    String cntrNo = Util.trim(MapUtils.getObject(dataSourceMap,
                            "LNA2021_CONTRACT"));
                    String dataYm = endMonth;

                    if (Util.equals(cntrNo, "")) {
                        continue;
                    }

                    gFinalUp = false;

                    doCount = doCount.add(BigDecimal.ONE);
                    logger.info("NOW DO=>" + dataYm + "-" + cntrNo + "："
                            + doCount.toString());

                    // 1.整理COLL0102建物檔*****************************************************

                    List<Map<String, Object>> coll0102s = misdbBASEService
                            .doLmsBatch0037_02(cntrNo, dataYm);

                    upCntrNo = "";
                    upCount = 0;
                    upCity = "";
                    upIsZu = "";
                    upAddr = "";
                    upTimeVal = BigDecimal.ZERO;

                    gCmsDoc = new LinkedHashMap<String, String>();

                    if (coll0102s != null && !coll0102s.isEmpty()) {

                        for (Map<String, Object> coll0102 : coll0102s) {

                            TYPCD = Util.trim(MapUtils.getObject(coll0102,
                                    "TYPCD"));
                            BRANCH = Util.trim(MapUtils.getObject(coll0102,
                                    "BRANCH"));
                            CUSTID = Util.trim(MapUtils.getObject(coll0102,
                                    "CUSTID"));
                            DUPNO = Util.trim(MapUtils.getObject(coll0102,
                                    "DUPNO"));
                            COLLNO = Util.trim(MapUtils.getObject(coll0102,
                                    "COLLNO"));
                            CURR = Util.trim(MapUtils.getObject(coll0102,
                                    "CURR"));
                            TIMEVAL = Util.equals(Util.trim(MapUtils.getObject(
                                    coll0102, "TIMEVAL")), "") ? BigDecimal.ZERO
                                    : Util.parseBigDecimal(Util.trim(MapUtils
                                    .getObject(coll0102, "TIMEVAL")));
                            LAWVAL = Util.equals(Util.trim(MapUtils.getObject(
                                    coll0102, "LAWVAL")), "") ? BigDecimal.ZERO
                                    : Util.parseBigDecimal(Util.trim(MapUtils
                                    .getObject(coll0102, "LAWVAL")));
                            APPAMT = Util.equals(Util.trim(MapUtils.getObject(
                                    coll0102, "APPAMT")), "") ? BigDecimal.ZERO
                                    : Util.parseBigDecimal(Util.trim(MapUtils
                                    .getObject(coll0102, "APPAMT")));
                            COLLSN = Util.trim(MapUtils.getObject(coll0102,
                                    "COLLSN"));
                            SITE1 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE1"));
                            SITE2 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE2"));
                            SITE3 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE3"));
                            SITE4 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE4"));
                            SITE5 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE5"));
                            SITE6 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE6"));
                            SITE7 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE7"));
                            SITE8 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE8"));
                            SITE9 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE9"));
                            SITE10 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE10"));
                            SITE11 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE11"));
                            SITE12 = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE12"));
                            AREA = Util.equals(Util.trim(MapUtils.getObject(
                                    coll0102, "AREA")), "") ? BigDecimal.ZERO
                                    : Util.parseBigDecimal(Util.trim(MapUtils
                                    .getObject(coll0102, "AREA")));
                            USE = Util
                                    .trim(MapUtils.getObject(coll0102, "USE"));
                            SITE1NO = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE1NO"));
                            SITE2NO = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE2NO"));
                            SITE3NO = Util.trim(MapUtils.getObject(coll0102,
                                    "SITE3NO"));

                            // 每個估價報告書要加總TIMEVAL
                            String cmsKey = TYPCD + "-" + BRANCH + "-" + CUSTID
                                    + "-" + DUPNO + "-" + COLLNO;

                            if (!gCmsDoc.containsKey(cmsKey)) {
                                gCmsDoc.put(cmsKey, cmsKey);

                                BigDecimal tTimeVal = BigDecimal.ZERO;
                                if (TIMEVAL.compareTo(BigDecimal.ZERO) == 0) {
                                    if (APPAMT.compareTo(BigDecimal.ZERO) == 0) {
                                        if (LAWVAL.compareTo(BigDecimal.ZERO) == 0) {

                                        } else {
                                            tTimeVal = LAWVAL;
                                        }
                                    } else {
                                        tTimeVal = APPAMT;
                                    }
                                } else {
                                    tTimeVal = TIMEVAL;
                                }
                                upTimeVal = upTimeVal.add(tTimeVal);

                            }

                            // 有建物明細
                            if (Util.notEquals(SITE1NO, "")) {

                                // 第一筆建物明細(upCntrNo 為空白)當代表
                                if (Util.equals(upCntrNo, "")) {

                                    upCntrNo = cntrNo;
                                    upCity = SITE1NO;
                                    upIsZu = "";
                                    if (Util.equals(USE, "03")
                                            || Util.equals(USE, "05")
                                            || Util.equals(USE, "06")) {
                                        upIsZu = "Y";
                                    }

                                    StringBuffer tAddr = new StringBuffer("");
                                    if (Util.notEquals(SITE1, "")) {
                                        tAddr.append(SITE1);
                                    }
                                    if (Util.notEquals(SITE2, "")) {
                                        tAddr.append(SITE2);
                                    }
                                    if (Util.notEquals(SITE3, "")) {
                                        tAddr.append(SITE3);
                                    }
                                    if (Util.notEquals(SITE4, "")) {
                                        tAddr.append(SITE4).append("鄰");
                                    }
                                    if (Util.notEquals(SITE5, "")) {
                                        tAddr.append(SITE5);
                                    }
                                    if (Util.notEquals(SITE6, "")) {
                                        tAddr.append(SITE6).append("段");
                                    }
                                    if (Util.notEquals(SITE7, "")) {
                                        tAddr.append(SITE7).append("巷");
                                    }
                                    if (Util.notEquals(SITE8, "")) {
                                        tAddr.append(SITE8).append("弄");
                                    }
                                    if (Util.notEquals(SITE9, "")) {
                                        tAddr.append(SITE9).append("號");
                                    }
                                    if (Util.notEquals(SITE10, "")) {
                                        tAddr.append(SITE10).append("樓");
                                    }
                                    if (Util.notEquals(SITE11, "")) {
                                        tAddr.append("之").append(SITE11);
                                    }
                                    if (Util.notEquals(SITE12, "")) {
                                        tAddr.append(SITE12).append("室");
                                    }
                                    upAddr = tAddr.toString();
                                }

                                upCount = upCount + 1;
                            }

                        }
                    }

                    // 上傳DB建物部分
                    if (Util.notEquals(upCntrNo, "")) {
                        // 上傳DB upTimeVal
                        logger.info("insert coll0102 upCntrNo :" + upCntrNo
                                + ",upCount:" + upCount + ",upCity:" + upCity
                                + ",upIsZu:" + upIsZu + ",upAddr:" + upAddr
                                + ",upTimeVal:" + upTimeVal + ",dataYm:"
                                + dataYm);
                        // 擔保品 - 建物 - 活 MIS.ELFBL
                        String dbName = "ELFBL";
                        misdbBASEService.insertDoLmsBatch0037(dbName, upCntrNo,
                                upCity, upTimeVal, upAddr, upCount, upIsZu,
                                dataYm);

                        if (!gFinalUp) {
                            gFinalUp = true;
                            // 上傳最終整理檔
                            String dbNameA = "ELFAA";
                            misdbBASEService.insertDoLmsBatch0037(dbNameA,
                                    upCntrNo, upCity, upTimeVal, upAddr,
                                    upCount, upIsZu, dataYm);
                        }
                    }

                    // 2.整理COLL0101土地檔*****************************************************

                    List<Map<String, Object>> coll0101s = misdbBASEService
                            .doLmsBatch0037_03(cntrNo, dataYm);

                    upCntrNo = "";
                    upCount = 0;
                    upCity = "";
                    upIsZu = "";
                    upAddr = "";
                    upTimeVal = BigDecimal.ZERO;

                    gCmsDoc = new LinkedHashMap<String, String>();

                    if (coll0101s != null && !coll0101s.isEmpty()) {

                        for (Map<String, Object> coll0101 : coll0101s) {

                            TYPCD = Util.trim(MapUtils.getObject(coll0101,
                                    "TYPCD"));
                            BRANCH = Util.trim(MapUtils.getObject(coll0101,
                                    "BRANCH"));
                            CUSTID = Util.trim(MapUtils.getObject(coll0101,
                                    "CUSTID"));
                            DUPNO = Util.trim(MapUtils.getObject(coll0101,
                                    "DUPNO"));
                            COLLNO = Util.trim(MapUtils.getObject(coll0101,
                                    "COLLNO"));
                            CURR = Util.trim(MapUtils.getObject(coll0101,
                                    "CURR"));
                            TIMEVAL = Util.equals(Util.trim(MapUtils.getObject(
                                    coll0101, "TIMEVAL")), "") ? BigDecimal.ZERO
                                    : Util.parseBigDecimal(Util.trim(MapUtils
                                    .getObject(coll0101, "TIMEVAL")));
                            LAWVAL = Util.equals(Util.trim(MapUtils.getObject(
                                    coll0101, "LAWVAL")), "") ? BigDecimal.ZERO
                                    : Util.parseBigDecimal(Util.trim(MapUtils
                                    .getObject(coll0101, "LAWVAL")));
                            APPAMT = Util.equals(Util.trim(MapUtils.getObject(
                                    coll0101, "APPAMT")), "") ? BigDecimal.ZERO
                                    : Util.parseBigDecimal(Util.trim(MapUtils
                                    .getObject(coll0101, "APPAMT")));
                            COLLSN = Util.trim(MapUtils.getObject(coll0101,
                                    "COLLSN"));
                            SITE1 = Util.trim(MapUtils.getObject(coll0101,
                                    "SITE1"));
                            SITE2 = Util.trim(MapUtils.getObject(coll0101,
                                    "SITE2"));
                            SITE3 = Util.trim(MapUtils.getObject(coll0101,
                                    "SITE3"));
                            SITE4 = Util.trim(MapUtils.getObject(coll0101,
                                    "SITE4"));

                            AREA = Util.equals(Util.trim(MapUtils.getObject(
                                    coll0101, "AREA")), "") ? BigDecimal.ZERO
                                    : Util.parseBigDecimal(Util.trim(MapUtils
                                    .getObject(coll0101, "AREA")));
                            USECD = Util.trim(MapUtils.getObject(coll0101,
                                    "USECD"));
                            USETYPE = Util.trim(MapUtils.getObject(coll0101,
                                    "USETYPE"));
                            SITE1NO = Util.trim(MapUtils.getObject(coll0101,
                                    "SITE1NO"));
                            SITE2NO = Util.trim(MapUtils.getObject(coll0101,
                                    "SITE2NO"));
                            SITE3NO = Util.trim(MapUtils.getObject(coll0101,
                                    "SITE3NO"));

                            // 每個估價報告書要加總TIMEVAL
                            String cmsKey = TYPCD + "-" + BRANCH + "-" + CUSTID
                                    + "-" + DUPNO + "-" + COLLNO;
                            if (!gCmsDoc.containsKey(cmsKey)) {
                                gCmsDoc.put(cmsKey, cmsKey);

                                BigDecimal tTimeVal = BigDecimal.ZERO;
                                if (TIMEVAL.compareTo(BigDecimal.ZERO) == 0) {
                                    if (APPAMT.compareTo(BigDecimal.ZERO) == 0) {
                                        if (LAWVAL.compareTo(BigDecimal.ZERO) == 0) {

                                        } else {
                                            tTimeVal = LAWVAL;
                                        }
                                    } else {
                                        tTimeVal = APPAMT;
                                    }
                                } else {
                                    tTimeVal = TIMEVAL;
                                }
                                upTimeVal = upTimeVal.add(tTimeVal);

                            }

                            // 有土地明細
                            if (Util.notEquals(SITE1NO, "")) {

                                // 第一筆土地物明細(upCntrNo 為空白)當代表
                                if (Util.equals(upCntrNo, "")) {

                                    upCntrNo = cntrNo;
                                    upCity = SITE1NO;
                                    upIsZu = "";
                                    if (Util.equals(USECD, "1")) {
                                        // 1.都市土地
                                        if (Util.equals(USETYPE, "01")
                                                || Util.equals(USETYPE, "09")) {
                                            upIsZu = "Y";
                                        }
                                    } else if (Util.equals(USECD, "2")) {
                                        // 2.非都市土地
                                        if (Util.equals(USETYPE, "09")) {
                                            upIsZu = "Y";
                                        }
                                    }

                                    StringBuffer tAddr = new StringBuffer("");
                                    if (Util.notEquals(SITE1, "")) {
                                        tAddr.append(SITE1);
                                    }
                                    if (Util.notEquals(SITE2, "")) {
                                        tAddr.append(SITE2);
                                    }
                                    if (Util.notEquals(SITE3, "")) {
                                        tAddr.append(SITE3).append("段");
                                    }
                                    if (Util.notEquals(SITE4, "")) {
                                        tAddr.append(SITE4).append("小段");
                                    }

                                    upAddr = tAddr.toString();
                                }

                                upCount = upCount + 1;
                            }

                        }

                        if (upCount > 1) {
                            upAddr = upAddr + "等" + Util.trim(upCount) + "筆";
                        }

                    }

                    // 上傳DB土地部分
                    if (Util.notEquals(upCntrNo, "")) {
                        // 上傳DB upTimeVal
                        logger.info("insert coll0101 upCntrNo :" + upCntrNo
                                + ",upCount:" + upCount + ",upCity:" + upCity
                                + ",upIsZu:" + upIsZu + ",upAddr:" + upAddr
                                + ",upTimeVal:" + upTimeVal + ",dataYm:"
                                + dataYm);
                        // 擔保品 - 土地 - 活 MIS.ELFLL
                        String dbName = "ELFLL";
                        misdbBASEService.insertDoLmsBatch0037(dbName, upCntrNo,
                                upCity, upTimeVal, upAddr, upCount, upIsZu,
                                dataYm);

                        if (!gFinalUp) {
                            gFinalUp = true;
                            // 上傳最終整理檔

                            String dbNameA = "ELFAA";
                            misdbBASEService.insertDoLmsBatch0037(dbNameA,
                                    upCntrNo, upCity, upTimeVal, upAddr,
                                    upCount, upIsZu, dataYm);
                        }
                    }

                    if (Util.equals(dataYm, "2020-12")) {
                        // 3.整理CXLL0102建物檔(塗銷)*****************************************************

                        List<Map<String, Object>> cxll0102s = misdbBASEService
                                .doLmsBatch0037_04(cntrNo, dataYm);

                        upCntrNo = "";
                        upCount = 0;
                        upCity = "";
                        upIsZu = "";
                        upAddr = "";
                        upTimeVal = BigDecimal.ZERO;

                        gCmsDoc = new LinkedHashMap<String, String>();

                        if (cxll0102s != null && !cxll0102s.isEmpty()) {

                            for (Map<String, Object> cxll0102 : cxll0102s) {

                                TYPCD = Util.trim(MapUtils.getObject(cxll0102,
                                        "TYPCD"));
                                BRANCH = Util.trim(MapUtils.getObject(cxll0102,
                                        "BRANCH"));
                                CUSTID = Util.trim(MapUtils.getObject(cxll0102,
                                        "CUSTID"));
                                DUPNO = Util.trim(MapUtils.getObject(cxll0102,
                                        "DUPNO"));
                                COLLNO = Util.trim(MapUtils.getObject(cxll0102,
                                        "COLLNO"));
                                CURR = Util.trim(MapUtils.getObject(cxll0102,
                                        "CURR"));
                                TIMEVAL = Util.equals(Util.trim(MapUtils
                                        .getObject(cxll0102, "TIMEVAL")), "") ? BigDecimal.ZERO
                                        : Util.parseBigDecimal(Util
                                        .trim(MapUtils.getObject(
                                                cxll0102, "TIMEVAL")));
                                LAWVAL = Util.equals(Util.trim(MapUtils
                                        .getObject(cxll0102, "LAWVAL")), "") ? BigDecimal.ZERO
                                        : Util.parseBigDecimal(Util
                                        .trim(MapUtils.getObject(
                                                cxll0102, "LAWVAL")));
                                APPAMT = Util.equals(Util.trim(MapUtils
                                        .getObject(cxll0102, "APPAMT")), "") ? BigDecimal.ZERO
                                        : Util.parseBigDecimal(Util
                                        .trim(MapUtils.getObject(
                                                cxll0102, "APPAMT")));
                                COLLSN = Util.trim(MapUtils.getObject(cxll0102,
                                        "COLLSN"));
                                SITE1 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE1"));
                                SITE2 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE2"));
                                SITE3 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE3"));
                                SITE4 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE4"));
                                SITE5 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE5"));
                                SITE6 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE6"));
                                SITE7 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE7"));
                                SITE8 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE8"));
                                SITE9 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE9"));
                                SITE10 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE10"));
                                SITE11 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE11"));
                                SITE12 = Util.trim(MapUtils.getObject(cxll0102,
                                        "SITE12"));
                                AREA = Util.equals(Util.trim(MapUtils
                                        .getObject(cxll0102, "AREA")), "") ? BigDecimal.ZERO
                                        : Util.parseBigDecimal(Util
                                        .trim(MapUtils.getObject(
                                                cxll0102, "AREA")));
                                USE = Util.trim(MapUtils.getObject(cxll0102,
                                        "USE"));
                                SITE1NO = Util.trim(MapUtils.getObject(
                                        cxll0102, "SITE1NO"));
                                SITE2NO = Util.trim(MapUtils.getObject(
                                        cxll0102, "SITE2NO"));
                                SITE3NO = Util.trim(MapUtils.getObject(
                                        cxll0102, "SITE3NO"));

                                // 每個估價報告書要加總TIMEVAL
                                String cmsKey = TYPCD + "-" + BRANCH + "-"
                                        + CUSTID + "-" + DUPNO + "-" + COLLNO;
                                if (!gCmsDoc.containsKey(cmsKey)) {
                                    gCmsDoc.put(cmsKey, cmsKey);

                                    BigDecimal tTimeVal = BigDecimal.ZERO;
                                    if (TIMEVAL.compareTo(BigDecimal.ZERO) == 0) {
                                        if (APPAMT.compareTo(BigDecimal.ZERO) == 0) {
                                            if (LAWVAL
                                                    .compareTo(BigDecimal.ZERO) == 0) {

                                            } else {
                                                tTimeVal = LAWVAL;
                                            }
                                        } else {
                                            tTimeVal = APPAMT;
                                        }
                                    } else {
                                        tTimeVal = TIMEVAL;
                                    }
                                    upTimeVal = upTimeVal.add(tTimeVal);

                                }

                                // 有建物明細
                                if (Util.notEquals(SITE1NO, "")) {

                                    // 第一筆建物明細(upCntrNo 為空白)當代表
                                    if (Util.equals(upCntrNo, "")) {

                                        upCntrNo = cntrNo;
                                        upCity = SITE1NO;
                                        upIsZu = "";
                                        if (Util.equals(USE, "03")
                                                || Util.equals(USE, "05")
                                                || Util.equals(USE, "06")) {
                                            upIsZu = "Y";
                                        }

                                        StringBuffer tAddr = new StringBuffer(
                                                "");
                                        if (Util.notEquals(SITE1, "")) {
                                            tAddr.append(SITE1);
                                        }
                                        if (Util.notEquals(SITE2, "")) {
                                            tAddr.append(SITE2);
                                        }
                                        if (Util.notEquals(SITE3, "")) {
                                            tAddr.append(SITE3);
                                        }
                                        if (Util.notEquals(SITE4, "")) {
                                            tAddr.append(SITE4).append("鄰");
                                        }
                                        if (Util.notEquals(SITE5, "")) {
                                            tAddr.append(SITE5);
                                        }
                                        if (Util.notEquals(SITE6, "")) {
                                            tAddr.append(SITE6).append("段");
                                        }
                                        if (Util.notEquals(SITE7, "")) {
                                            tAddr.append(SITE7).append("巷");
                                        }
                                        if (Util.notEquals(SITE8, "")) {
                                            tAddr.append(SITE8).append("弄");
                                        }
                                        if (Util.notEquals(SITE9, "")) {
                                            tAddr.append(SITE9).append("號");
                                        }
                                        if (Util.notEquals(SITE10, "")) {
                                            tAddr.append(SITE10).append("樓");
                                        }
                                        if (Util.notEquals(SITE11, "")) {
                                            tAddr.append("之").append(SITE11);
                                        }
                                        if (Util.notEquals(SITE12, "")) {
                                            tAddr.append(SITE12).append("室");
                                        }
                                        upAddr = tAddr.toString();
                                    }

                                    upCount = upCount + 1;
                                }
                            }
                        }

                        // 上傳DB建物部分
                        if (Util.notEquals(upCntrNo, "")) {
                            // 上傳DB upTimeVal
                            logger.info("insert cxll0102 upCntrNo :" + upCntrNo
                                    + ",upCount:" + upCount + ",upCity:"
                                    + upCity + ",upIsZu:" + upIsZu + ",upAddr:"
                                    + upAddr + ",upTimeVal:" + upTimeVal
                                    + ",dataYm:" + dataYm);
                            // 擔保品 - 建物 - 死 MIS.ELFBD
                            String dbName = "ELFBD";
                            misdbBASEService.insertDoLmsBatch0037(dbName,
                                    upCntrNo, upCity, upTimeVal, upAddr,
                                    upCount, upIsZu, dataYm);

                            if (!gFinalUp) {
                                gFinalUp = true;

                                // 上傳最終整理檔
                                String dbNameA = "ELFAA";
                                misdbBASEService.insertDoLmsBatch0037(dbNameA,
                                        upCntrNo, upCity, upTimeVal, upAddr,
                                        upCount, upIsZu, dataYm);
                            }
                        }

                        // 4.整理CXLL0101土地檔(塗銷)*****************************************************

                        List<Map<String, Object>> cxll0101s = misdbBASEService
                                .doLmsBatch0037_05(cntrNo, dataYm);

                        upCntrNo = "";
                        upCount = 0;
                        upCity = "";
                        upIsZu = "";
                        upAddr = "";
                        upTimeVal = BigDecimal.ZERO;

                        gCmsDoc = new LinkedHashMap<String, String>();

                        if (cxll0101s != null && !cxll0101s.isEmpty()) {

                            for (Map<String, Object> cxll0101 : cxll0101s) {

                                TYPCD = Util.trim(MapUtils.getObject(cxll0101,
                                        "TYPCD"));
                                BRANCH = Util.trim(MapUtils.getObject(cxll0101,
                                        "BRANCH"));
                                CUSTID = Util.trim(MapUtils.getObject(cxll0101,
                                        "CUSTID"));
                                DUPNO = Util.trim(MapUtils.getObject(cxll0101,
                                        "DUPNO"));
                                COLLNO = Util.trim(MapUtils.getObject(cxll0101,
                                        "COLLNO"));
                                CURR = Util.trim(MapUtils.getObject(cxll0101,
                                        "CURR"));
                                TIMEVAL = Util.equals(Util.trim(MapUtils
                                        .getObject(cxll0101, "TIMEVAL")), "") ? BigDecimal.ZERO
                                        : Util.parseBigDecimal(Util
                                        .trim(MapUtils.getObject(
                                                cxll0101, "TIMEVAL")));
                                LAWVAL = Util.equals(Util.trim(MapUtils
                                        .getObject(cxll0101, "LAWVAL")), "") ? BigDecimal.ZERO
                                        : Util.parseBigDecimal(Util
                                        .trim(MapUtils.getObject(
                                                cxll0101, "LAWVAL")));
                                APPAMT = Util.equals(Util.trim(MapUtils
                                        .getObject(cxll0101, "APPAMT")), "") ? BigDecimal.ZERO
                                        : Util.parseBigDecimal(Util
                                        .trim(MapUtils.getObject(
                                                cxll0101, "APPAMT")));
                                COLLSN = Util.trim(MapUtils.getObject(cxll0101,
                                        "COLLSN"));
                                SITE1 = Util.trim(MapUtils.getObject(cxll0101,
                                        "SITE1"));
                                SITE2 = Util.trim(MapUtils.getObject(cxll0101,
                                        "SITE2"));
                                SITE3 = Util.trim(MapUtils.getObject(cxll0101,
                                        "SITE3"));
                                SITE4 = Util.trim(MapUtils.getObject(cxll0101,
                                        "SITE4"));

                                AREA = Util.equals(Util.trim(MapUtils
                                        .getObject(cxll0101, "AREA")), "") ? BigDecimal.ZERO
                                        : Util.parseBigDecimal(Util
                                        .trim(MapUtils.getObject(
                                                cxll0101, "AREA")));
                                USECD = Util.trim(MapUtils.getObject(cxll0101,
                                        "USECD"));
                                USETYPE = Util.trim(MapUtils.getObject(
                                        cxll0101, "USETYPE"));
                                SITE1NO = Util.trim(MapUtils.getObject(
                                        cxll0101, "SITE1NO"));
                                SITE2NO = Util.trim(MapUtils.getObject(
                                        cxll0101, "SITE2NO"));
                                SITE3NO = Util.trim(MapUtils.getObject(
                                        cxll0101, "SITE3NO"));

                                // 每個估價報告書要加總TIMEVAL
                                String cmsKey = TYPCD + "-" + BRANCH + "-"
                                        + CUSTID + "-" + DUPNO + "-" + COLLNO;
                                if (!gCmsDoc.containsKey(cmsKey)) {
                                    gCmsDoc.put(cmsKey, cmsKey);

                                    BigDecimal tTimeVal = BigDecimal.ZERO;
                                    if (TIMEVAL.compareTo(BigDecimal.ZERO) == 0) {
                                        if (APPAMT.compareTo(BigDecimal.ZERO) == 0) {
                                            if (LAWVAL
                                                    .compareTo(BigDecimal.ZERO) == 0) {

                                            } else {
                                                tTimeVal = LAWVAL;
                                            }
                                        } else {
                                            tTimeVal = APPAMT;
                                        }
                                    } else {
                                        tTimeVal = TIMEVAL;
                                    }
                                    upTimeVal = upTimeVal.add(tTimeVal);

                                }

                                // 有土地明細
                                if (Util.notEquals(SITE1NO, "")) {
                                    // 第一筆土地物明細(upCntrNo 為空白)當代表
                                    if (Util.equals(upCntrNo, "")) {

                                        upCntrNo = cntrNo;
                                        upCity = SITE1NO;
                                        upIsZu = "";
                                        if (Util.equals(USECD, "1")) {
                                            // 1.都市土地
                                            if (Util.equals(USETYPE, "01")
                                                    || Util.equals(USETYPE,
                                                    "09")) {
                                                upIsZu = "Y";
                                            }
                                        } else if (Util.equals(USECD, "2")) {
                                            // 2.非都市土地
                                            if (Util.equals(USETYPE, "09")) {
                                                upIsZu = "Y";
                                            }
                                        }

                                        StringBuffer tAddr = new StringBuffer(
                                                "");
                                        if (Util.notEquals(SITE1, "")) {
                                            tAddr.append(SITE1);
                                        }
                                        if (Util.notEquals(SITE2, "")) {
                                            tAddr.append(SITE2);
                                        }
                                        if (Util.notEquals(SITE3, "")) {
                                            tAddr.append(SITE3).append("段");
                                        }
                                        if (Util.notEquals(SITE4, "")) {
                                            tAddr.append(SITE4).append("小段");
                                        }

                                        upAddr = tAddr.toString();
                                    }

                                    upCount = upCount + 1;
                                }
                            }

                            if (upCount > 1) {
                                upAddr = upAddr + "等" + Util.trim(upCount)
                                        + "筆";
                            }

                        }

                        // 上傳DB土地部分
                        if (Util.notEquals(upCntrNo, "")) {
                            // 上傳DB upTimeVal
                            logger.info("insert cxll0101 upCntrNo :" + upCntrNo
                                    + ",upCount:" + upCount + ",upCity:"
                                    + upCity + ",upIsZu:" + upIsZu + ",upAddr:"
                                    + upAddr + ",upTimeVal:" + upTimeVal
                                    + ",dataYm:" + dataYm);

                            // 擔保品 - 土地 - 死 MIS.ELFLD
                            String dbName = "ELFLD";
                            misdbBASEService.insertDoLmsBatch0037(dbName,
                                    upCntrNo, upCity, upTimeVal, upAddr,
                                    upCount, upIsZu, dataYm);

                            if (!gFinalUp) {
                                gFinalUp = true;
                                // 上傳最終整理檔
                                String dbNameA = "ELFAA";
                                misdbBASEService.insertDoLmsBatch0037(dbNameA,
                                        upCntrNo, upCity, upTimeVal, upAddr,
                                        upCount, upIsZu, dataYm);
                            }
                        }
                    }

                }

            }
        }
        return errMsg.toString();
    }

    /**
     * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
     */
    @Override
    @NonTransactional
    public String doLmsBatch0038(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");
        String type = request.getString("type");

        return errMsg.toString();
    }

    /**
     * J-109-0496 貸後管理 理財商品贖回追蹤
     */
    @Override
    @NonTransactional
    public String doLmsBatch0039(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        try {
            // 1. 取得本日要寫入追蹤的理財商品贖回資料：
            // 貸後追蹤確認的理財商品檔(OTS_DW_LNWM_CFM) 若在
            // 貸後管理理財商品存量檔(OTS_DW_LNWM_MNT)有贖回資料 且還沒處理過的資料
            List<Map<String, Object>> list = dwdbBASEService
                    .queryDWLNWMCFMList();
            if (list != null && !list.isEmpty()) {

                // 商品類別中文對應
                Map<String, String> typemap = codetypeService.findByCodeType(
                        "postLoan_proType", "zh_TW");
                // 保險狀態中文對應
                Map<String, String> tranTypemap = codetypeService
                        .findByCodeType("postLoan_tranType", "zh_TW");

                String custId, custDupNo, proType, bankProCode, proTypeN, proCode, accNo, lstSellDt, lstSellCurCd, lstSellAmt, lstSellBrCd, tranType, tranTypeN, dataDt, count, STAFF, datasrc, fodate, fodate1, fodate2;
                String unId = "";
                String content = "";
                BigDecimal lstSellAmtB;
                ELF602 newelf602 = new ELF602();
                ELF602 oldelf602 = new ELF602();
                Date date = new Date();
                DateFormat dateformat1 = new SimpleDateFormat("yyMMddHHmmss");
                DateFormat dateformat2 = new SimpleDateFormat("yyyy-MM-dd");
                String date1 = "";
                String date2 = CapDate.getCurrentDate("yyyy-MM-dd");
                List<Map<String, Object>> unidlist = null;

                // 避免KEY重複
                int loopCount = 0;

                // 2. 有資料則寫入ELF602
                for (Map<String, Object> map : list) {

                    // 避免KEY重複
                    loopCount ++;
                    loopCount = loopCount*1000;
                    date = new Date(date.getTime()+loopCount);
                    date1 = dateformat1.format(date);

                    custId = Util.trim(MapUtils.getObject(map, "CUST_ID")); // 客戶ID
                    custDupNo = Util.trim(MapUtils
                            .getObject(map, "CUST_DUP_NO")); // 客戶重複碼
                    proType = Util.trim(MapUtils.getObject(map, "PRO_TYPE")); // 商品類別
                    bankProCode = Util.trim(MapUtils.getObject(map,
                            "BANK_PRO_CODE")); // 商品代號
                    proTypeN = typemap.get(proType) != null ? typemap
                            .get(proType) : proType; // 商品類別中文
                    proCode = Util.trim(MapUtils
                            .getObject(map, "BANK_PRO_CODE")); // 商品代號
                    accNo = Util.trim(MapUtils.getObject(map, "ACC_NO")); // 憑證編號/定存帳號/受理編號
                    lstSellDt = Util.trim(MapUtils
                            .getObject(map, "LST_SELL_DT")); // 最近一次贖回/到期入帳日期
                    lstSellCurCd = Util.trim(MapUtils.getObject(map,
                            "LST_SELL_CUR_CD")); // 最近一次贖回/到期交易幣別
                    lstSellAmtB = Util.parseToBigDecimal(
                            MapUtils.getObject(map, "LST_SELL_AMT")).setScale(
                            0, BigDecimal.ROUND_HALF_UP);
                    lstSellAmt = lstSellAmtB.toString(); // 最近一次贖回/到期入帳金額(台幣)
                    lstSellBrCd = Util.trim(MapUtils.getObject(map,
                            "LST_SELL_BR_CD")); // 最近一次贖回交易分行
                    tranType = Util.trim(MapUtils.getObject(map, "TRAN_TYPE")); // 最近一次贖回/到期交易類別(狀態)
                    tranTypeN = tranTypemap.get(tranType) != null ? tranTypemap
                            .get(tranType) : tranType; // 狀態中文
                    dataDt = Util.trim(MapUtils.getObject(map, "DATA_DT")); // MNT資料日
                    count = Util.trim(MapUtils.getObject(map, "COUNT")); // CFM
                    // COUNT

                    // STAFF 抓申購追蹤時(最近一筆)的應辦理追蹤對象，若讀不到帶"01"
                    STAFF = "01";
                    // DATASRC 放申購追蹤時(最近一筆)的UNID
                    datasrc = "";

                    // 追蹤事項通知內容放贖回資訊，保險僅有保單狀態
                    if (("1").equals(count)) {
                        unidlist = dwdbBASEService.queryDWLNWMCFMUNID(custId,
                                custDupNo, proType, bankProCode, accNo);
                        for (Map<String, Object> map1 : unidlist) {
                            unId = Util.trim(MapUtils.getObject(map1, "UNID"));
                        }
                        oldelf602 = misdbBASEService.getElf602ByUnid(unId);
                        fodate = (oldelf602 != null) ? dateformat2
                                .format(oldelf602.getElf602_fo_date()) : "";
                        content = "理財商品贖回追蹤，" + "通知日：" + fodate + "共" + count
                                + "筆追蹤紀錄，客戶：" + custId + "，" + proTypeN
                                + "-商品代號：" + proCode + "，編號：" + accNo + "，狀態："
                                + tranTypeN;
                        STAFF = (oldelf602 != null) ? oldelf602
                                .getElf602_staff() : "01";
                        datasrc = unId;
                    } else {
                        unidlist = dwdbBASEService.queryDWLNWMCFMUNID(custId,
                                custDupNo, proType, bankProCode, accNo);
                        fodate1 = "0001-01-01";
                        fodate2 = "0001-01-01";
                        for (Map<String, Object> map1 : unidlist) {
                            unId = Util.trim(MapUtils.getObject(map1, "UNID"));
                            oldelf602 = misdbBASEService.getElf602ByUnid(unId);
                            fodate = (oldelf602 != null) ? dateformat2
                                    .format(oldelf602.getElf602_fo_date()) : "";
                            if ("0001-01-01".equals(fodate1)) {
                                fodate1 = fodate;
                            }
                            if (fodate1.compareTo(fodate) > 0) {
                                fodate1 = fodate;
                            }
                            if (fodate.compareTo(fodate2) > 0) {
                                fodate2 = fodate;
                                STAFF = (oldelf602 != null) ? oldelf602
                                        .getElf602_staff() : "01";
                                datasrc = unId;
                            }
                        }

                        content = "理財商品贖回追蹤，通知日：" + fodate1 + "到" + fodate2
                                + "共" + count + "筆追蹤紀錄，客戶：" + custId + "，"
                                + proTypeN + "-商品代號：" + proCode + "，編號："
                                + accNo + "，狀態：" + tranTypeN;
                    }
                    if (!("INS").equals(proType)) {
                        content = content + "，近一次贖回日期：" + lstSellDt
                                + "，近一次贖回金額(台幣)：" + lstSellAmt;
                    }

                    newelf602.setElf602_unid(custId + proType + accNo + date1); // 序號
                    // (UNID
                    // =
                    // CustId
                    // +
                    // ProType
                    // +
                    // AccNo
                    // +
                    // "yyMMddHHmmss")
                    newelf602.setElf602_cntrno(Util.trim(MapUtils.getObject(
                            map, "CNTRNO"))); // 額度序號
                    newelf602.setElf602_loan_no(Util.trim(MapUtils.getObject(
                            map, "LOANNO"))); // 放款帳號
                    newelf602.setElf602_loan_kind("FM"); // 業務別(Financial
                    // management)
                    newelf602.setElf602_fo_kind(null); // 類別
                    newelf602.setElf602_fo_content(content); // 追蹤事項通知內容
                    newelf602.setElf602_staff(STAFF); // 應辦理追蹤對象
                    newelf602.setElf602_fo_date(new java.sql.Date(date
                            .getTime())); // 追蹤事項通知日期
                    newelf602.setElf602_chkdate(null); // 檢核日期
                    newelf602.setElf602_conform_fg(""); // 符合註記
                    newelf602.setElf602_fo_memo(null); // 追蹤說明
                    newelf602.setElf602_status("1"); // 狀態：1-未辦理
                    newelf602.setElf602_datasrc(datasrc); // 資料寫入來源
                    newelf602.setElf602_unusual_fg(""); // 還款來源異常註記
                    newelf602.setElf602_unusualdesc(null); // 理由敘述
                    newelf602.setElf602_isnotional(null); // 是否承做
                    newelf602.setElf602_isaml(null); // 是否申報疑似洗錢
                    newelf602.setElf602_upd_date(null); // 最近維護日期
                    newelf602.setElf602_upd_teller(""); // 最近維護經辦
                    newelf602.setElf602_upd_supvno(""); // 最近維護主管
                    newelf602.setElf602_full_content(Util
                            .toFullCharString(content)); // 追蹤事項通知內容（全形）
                    newelf602.setElf602_fieldMainId(null); // 附加文件 mainId
                    newelf602.setElf602_fileDesc(null); // 證明文件說明
                    newelf602.setElf602_custid(custId);
                    newelf602.setElf602_dupno(custDupNo);
                    String cntrNo = Util.trim(MapUtils.getObject(map, "CNTRNO"));
                    String brNo = ((cntrNo.length() < 3) ? "" : cntrNo.substring(0,3));
                    newelf602.setElf602_br_no(brNo);
                    misdbBASEService.insertELF602(newelf602);

                    // 4. 回寫貸後追蹤確認的理財商品檔(OTS_DW_LNWM_CFM)資訊更新
                    dwdbBASEService.DW_LNWM_CFM_Update(lstSellDt, lstSellCurCd,
                            lstSellAmtB, lstSellBrCd, tranType, dataDt, date2,
                            unId, custId, custDupNo, proType, bankProCode,
                            accNo);

                }
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();

    }

    /**
     * 南京東路客戶移轉國外部
     */
    /**
     * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
     *
     * 本功能適用該ID下該間分行案件全部移轉 上傳轉換ID到LNF07A
     */
    @Override
    @NonTransactional
    public String doLmsBatch0040(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        // LN.LNF078T TO COM.LNF078T

        String exDate = Util.trim(request.getString("exDate"));
        String kind = Util.trim(request.getString("kind"));
        String fromBranch = Util.trim(request.getString("fromBranch"));
        String toBranch = Util.trim(request.getString("toBranch"));
        String aLoanDate = Util.trim(request.getString("aLoanDate"));
        try {
            if (Util.equals(kind, "1")) {
                // LN.LNF078T TO COM.LNF078T
                if (Util.equals(exDate, "")) {
                    errMsg.append("參數exDate錯誤");
                } else {
                    errMsg.append(insertLnf078T(exDate, aLoanDate));
                }

            } else if (Util.equals(kind, "2")) {
                // 修改額度序號
                errMsg.append(chgCntrNoByLnf078T(exDate));
            } else if (Util.equals(kind, "3")) {
                // 修改帳號
                errMsg.append(chgLoanNoByLnf078T(exDate));
            } else if (Util.equals(kind, "4")) {
                // 增加授權
                errMsg.append(addAuthUnitByLnf078T(exDate, fromBranch, toBranch));
            } else if (Util.equals(kind, "5")) {
                // 修改CASEBRID
                errMsg.append(chgCaseBrIdByLnf078T(exDate, fromBranch, toBranch));
            } else if (Util.equals(kind, "6")) {
                // 修改C801M01A(財產清冊) LOANNO
                errMsg.append(updateC801m01aLoanNoByLnf078T(exDate));

            } else if (Util.equals(kind, "7")) {
                // 修改OWNBRID
                errMsg.append(chgOwnerBrIdByLnf078T(exDate));

            } else if (Util.equals(kind, "A")) {
                // 修改額度序號
                errMsg.append(chgCntrNoByLnf078T(exDate));
                // 修改帳號
                errMsg.append(chgLoanNoByLnf078T(exDate));
                // 增加授權
                errMsg.append(addAuthUnitByLnf078T(exDate, fromBranch, toBranch));
                // 修改CASEBRID
                errMsg.append(chgCaseBrIdByLnf078T(exDate, fromBranch, toBranch));
                // 修改C801M01A(財產清冊) LOANNO
                errMsg.append(updateC801m01aLoanNoByLnf078T(exDate));
                // 修改OWNBRID
                errMsg.append(chgOwnerBrIdByLnf078T(exDate));
            } else if (Util.equals(kind, "B")) {
                // 2.修改額度序號2
                errMsg.append(chgCntrNoByLnf078T(exDate));
                // 3.修改帳號3
                errMsg.append(chgLoanNoByLnf078T(exDate));
                // 4.增加授權4
                errMsg.append(addAuthUnitByLnf078T(exDate, fromBranch, toBranch));
                errMsg.append(addAuthUnitByLnf078T(exDate, toBranch, fromBranch));
                // 5.修改CASEBRID 5
                errMsg.append(chgCaseBrIdByLnf078T(exDate, fromBranch, toBranch));
                errMsg.append(chgCaseBrIdByLnf078T(exDate, toBranch, fromBranch));
                // 7.修改OWNBRID7
                errMsg.append(chgOwnerBrIdByLnf078T(exDate));
                // 6.修改C801M01A(財產清冊) LOANNO
                errMsg.append(updateC801m01aLoanNoByLnf078T(exDate));

            } else {
                errMsg.append("參數kind錯誤");
            }
        } catch (Exception e) {

            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();
    }

    @NonTransactional
    public String insertLnf078T(String exDate, String aLoanDate) {
        StringBuffer errMsg = new StringBuffer("");

        List<Map<String, Object>> lnf078ts = misdbBASEService
                .findLnf078T(aLoanDate);
        if (lnf078ts == null || lnf078ts.isEmpty()) {
            errMsg.append("無法取得LN.LNF078T資料");
            System.out.println(errMsg.toString());
            return errMsg.toString();
        }

        // 清除ELOAN DB 重覆exDate資料
        try {
            eloandbService.LNF078T_deleteByExDate(exDate);
        } catch (Exception e) {
            errMsg.append("清除COM.LNF078T EXDATE " + exDate + "資料失敗：");
            errMsg.append(e.toString());
            System.out.println(errMsg.toString());
            return errMsg.toString();
        }

        String CONTRACT_O = "";
        String LOAN_NO_O = "";
        String CUST_ID = "";
        String CONTRACT = "";
        String LOAN_NO = "";
        String CUSTID = "";
        String DUPNO = "";
        String BRANCH_O = "";
        String BRANCH = "";
        String EXDATE = "";

        for (Map<String, Object> lnf078t : lnf078ts) {

            CONTRACT_O = MapUtils.getString(lnf078t, "LNF078_CONTRACT_O", "");
            LOAN_NO_O = MapUtils.getString(lnf078t, "LNF078_LOAN_NO_O", "");
            CUST_ID = MapUtils.getString(lnf078t, "LNF078_CUST_ID", "");
            CONTRACT = MapUtils.getString(lnf078t, "LNF078_CONTRACT", "");
            LOAN_NO = MapUtils.getString(lnf078t, "LNF078_LOAN_NO", "");
            CUSTID = Util.trim(Util.getLeftStr(CUST_ID, 10));
            DUPNO = Util.trim(Util.getRightStr(CUST_ID, 1));
            BRANCH_O = Util.trim(Util.getLeftStr(CONTRACT_O, 3));
            BRANCH = Util.trim(Util.getLeftStr(CONTRACT, 3));
            EXDATE = exDate;

            eloandbService.LNF078T_insert(CONTRACT_O, LOAN_NO_O, CUST_ID,
                    CONTRACT, LOAN_NO, CUSTID, DUPNO, BRANCH_O, BRANCH, EXDATE);
        }

        return errMsg.toString();
    }

    /**
     * 南京東路客戶移轉國外部
     *
     * 修改額度序號
     *
     * @param exDate
     * @return
     */
    @NonTransactional
    public String chgCntrNoByLnf078T(String exDate) {
        StringBuffer errMsg = new StringBuffer("");

        Map<String, String> chgCntrNoMap = codetypeService.findByCodeType(
                "lnf078t_chgCntrNo", "zh_TW");

        StringBuffer exCountBuf = new StringBuffer("");

        if (chgCntrNoMap != null && !chgCntrNoMap.isEmpty()) {
            for (String chgCntrNoKey : chgCntrNoMap.keySet()) {
                String table = StringUtils.split(chgCntrNoKey, "-")[0];
                String column = StringUtils.split(chgCntrNoKey, "-")[1];

                int exCount = eloandbService.updateCntrNoByLnf078t(table,
                        column, exDate);

                exCountBuf.append("TABLE：【" + table + "】，欄位：【" + column
                        + "】，更新筆數：【" + exCount + "】筆\r");

            }
        }

        logger.info("分行客戶移轉 【額度序號】chgCntrNoByLnf078T執行結果 :\r"
                + exCountBuf.toString());

        return errMsg.toString();
    }

    /**
     * 南京東路客戶移轉國外部
     *
     * 修改額度序號
     *
     * @param exDate
     * @return
     */
    @NonTransactional
    public String chgLoanNoByLnf078T(String exDate) {
        StringBuffer errMsg = new StringBuffer("");

        Map<String, String> chgCntrNoMap = codetypeService.findByCodeType(
                "lnf078t_chgLoanNo", "zh_TW");

        StringBuffer exCountBuf = new StringBuffer("");

        if (chgCntrNoMap != null && !chgCntrNoMap.isEmpty()) {
            for (String chgCntrNoKey : chgCntrNoMap.keySet()) {
                String table = StringUtils.split(chgCntrNoKey, "-")[0];
                String column = StringUtils.split(chgCntrNoKey, "-")[1];

                int exCount = eloandbService.updateLoanNoByLnf078t(table,
                        column, exDate);

                exCountBuf.append("TABLE：【" + table + "】，欄位：【" + column
                        + "】，更新筆數：【" + exCount + "】筆\r");

            }
        }

        logger.info("分行客戶移轉 【帳號】chgLoanNoByLnf078T執行結果 :\r"
                + exCountBuf.toString());

        return errMsg.toString();
    }

    /**
     * 南京東路客戶移轉國外部
     *
     * 增加授權
     *
     * @param exDate
     * @return
     */
    @NonTransactional
    public String addAuthUnitByLnf078T(String exDate, String fromBranch,
                                       String toBranch) {
        StringBuffer errMsg = new StringBuffer("");

        List<CodeType> codeTypelist = codetypeService.findByCodeTypeList(
                "lnf078t_addAuthUnit", "zh_TW");

        StringBuffer exCountBuf = new StringBuffer("");

        if (codeTypelist != null && !codeTypelist.isEmpty()) {
            for (CodeType codeType : codeTypelist) {

                String authTable = Util.trim(codeType.getCodeValue());
                String mainTable = Util.trim(codeType.getCodeDesc2());
                String docStatus = Util.trim(codeType.getCodeDesc3());

//				if (Util.notEquals(docStatus, "")) {
//					docStatus = "'"
//							+ StringUtils.replace(docStatus, ",", "','") + "'";
//				}

                int exCount = eloandbService.addAuthUnitByLnf078t(authTable,
                        mainTable, docStatus.split(","), fromBranch, toBranch, exDate);

                exCountBuf.append("TABLE：【" + authTable + "】，更新筆數：【" + exCount
                        + "】筆\r");

            }
        }

        logger.info("分行客戶移轉 【增加授權】addAuthUnitByLnf078T執行結果 :\r"
                + exCountBuf.toString());

        return errMsg.toString();
    }

    /**
     * 南京東路客戶移轉國外部
     *
     * 修改CASEBRID
     *
     * @param exDate
     * @return
     */
    @NonTransactional
    public String chgCaseBrIdByLnf078T(String exDate, String fromBranch,
                                       String toBranch) {
        StringBuffer errMsg = new StringBuffer("");

        StringBuffer exCountBuf = new StringBuffer("");
        String table = "L120M01A";
        String column = "CASEBRID";
        String docStatus = "05O";

//		if (Util.notEquals(docStatus, "")) {
//			docStatus = "'" + StringUtils.replace(docStatus, ",", "','") + "'";
//		}

        int exCount = eloandbService.chgCaseBrIdByLnf078t(docStatus.split(","),
                fromBranch, toBranch, exDate);

        exCountBuf.append("TABLE：【" + table + "】，欄位：【" + column + "】，更新筆數：【"
                + exCount + "】筆\r");

        logger.info("分行客戶移轉 【修改CASEBRID】chgCaseBrIdByLnf078T執行結果 :\r"
                + exCountBuf.toString());

        return errMsg.toString();
    }

    /**
     * 南京東路客戶移轉國外部
     *
     * 修改C801M01A(財產清冊) LOANNO
     *
     * @param exDate
     * @return
     */
    @NonTransactional
    public String updateC801m01aLoanNoByLnf078T(String exDate) {
        StringBuffer errMsg = new StringBuffer("");
        StringBuffer exCountBuf = new StringBuffer("");
        String table = "C801M01A";
        String column = "LOANNO";

        List<Map<String, Object>> lnf078ts = eloandbService
                .LNF078T_selAllByExDate(exDate);
        if (lnf078ts == null || lnf078ts.isEmpty()) {
            errMsg.append("無法取得COM.LNF078T資料");
            System.out.println(errMsg.toString());
            return errMsg.toString();
        }

        int exCount = 0;
        for (Map<String, Object> lnf078t : lnf078ts) {
            String LOAN_NO = Util.trim(MapUtils.getString(lnf078t, "LOAN_NO"));
            String LOAN_NO_O = Util.trim(MapUtils.getString(lnf078t,
                    "LOAN_NO_O"));
            if (Util.notEquals(LOAN_NO, "") && Util.notEquals(LOAN_NO_O, "")) {
                exCount = exCount
                        + eloandbService.updateC801m01aLoanNoByLnf078t(LOAN_NO,
                        LOAN_NO_O);
            }

        }

        exCountBuf.append("TABLE：【" + table + "】，欄位：【" + column + "】，更新筆數：【"
                + exCount + "】筆\r");

        logger.info("分行客戶移轉 【修改C801M01A(財產清冊) LOANNO】updateC801m01aLoanNoByLnf078T執行結果 :\r"
                + exCountBuf.toString());

        return errMsg.toString();
    }

    /**
     * 南京東路客戶移轉國外部
     *
     * 增加授權
     *
     * @param exDate
     * @return
     */
    @NonTransactional
    public String chgOwnerBrIdByLnf078T(String exDate) {
        StringBuffer errMsg = new StringBuffer("");

        List<CodeType> codeTypelist = codetypeService.findByCodeTypeList(
                "lnf078t_chgOwnBrId", "zh_TW");

        StringBuffer exCountBuf = new StringBuffer("");

        if (codeTypelist != null && !codeTypelist.isEmpty()) {
            for (CodeType codeType : codeTypelist) {

                String authTable = Util.trim(codeType.getCodeValue());
                String column = Util.trim(codeType.getCodeDesc2());
                String docStatus = Util.trim(codeType.getCodeDesc3());

                String[] docStatusArr = docStatus.split(",");

//				if (Util.notEquals(docStatus, "")) {
//					docStatus = "'"
//							+ StringUtils.replace(docStatus, ",", "','") + "'";
//				}

                int exCount = eloandbService.chgOwnBrIdByLnf078t(authTable,
                        column, docStatusArr, exDate);

                exCountBuf.append("TABLE：【" + authTable + "】，欄位：【" + column
                        + "】，更新筆數：【" + exCount + "】筆\r");

            }
        }

        logger.info("分行客戶移轉 【修改OWNBRID】chgOwnerBrIdByLnf078T執行結果 :\r"
                + exCountBuf.toString());

        return errMsg.toString();
    }

    /**
     * J-110-0182_05097_B1001 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0041(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");

        try {
            int exCount = misQuotapprService.updateIsRescue_elByIsRescue();

            logger.info("doLmsBatch0041 【修改ISRESCUE_EL】執行結果 : " + exCount
                    + "筆。");

        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();

    }

    /**
     * J-108-0245_05097_B1001 Web e-Loan 配合雪梨行授信戶TECO AUSTRALIA PTY
     * LTD原ID-TWZ0008288修改為AUZ0042888
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0042(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");
        String bgnDate = Util.trim(request.getString("bgnDate"));

        try {

            String orgCustId = "TWZ0008288";
            String orgDupNo = "0";
            String newCustId = "AUZ0042888";
            String newDupNo = "0";
            boolean useNew0024Name = false;
            String newCustName = "TECO AUSTRALIA PTY LTD";

            List<L120M01A> l120m01as = l120m01aDao.findByCustIdDupId(orgCustId,
                    orgDupNo);

            for (L120M01A l120m01a : l120m01as) {
                if (Util.equals(l120m01a.getCaseBrId(), "0B9")
                        && Util.equals(l120m01a.getDocStatus(), "05O")
                        && l120m01a.getDeletedTime() == null) {

                    if (Util.notEquals(bgnDate, "")
                            && l120m01a.getEndDate() != null) {

                        if (Util.parseDate(bgnDate).compareTo(
                                l120m01a.getEndDate()) > 0) {
                            continue;
                        }

                        // 只改雪梨行已核准簽報書
                        lmsService.updCustId(l120m01a, orgCustId, orgDupNo,
                                newCustId, newDupNo, useNew0024Name,
                                newCustName);

                    }
                }

            }

        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();

    }

    /**
     * 小規增補合約 - L120S14 各Table補 cntrNo
     */
    @Override
    public String doLmsBatch0043(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");
        try {

            List<L120S14A> s14aListAll = new ArrayList<L120S14A>();
            List<L120S14B> s14bListAll = new ArrayList<L120S14B>();
            List<L120S14C> s14cListAll = new ArrayList<L120S14C>();
            List<L120S14D> s14dListAll = new ArrayList<L120S14D>();
            List<L120S14E> s14eListAll = new ArrayList<L120S14E>();
            // 小規合約書
            List<Map<String, Object>> s14aList = eloandbService
                    .getCntrNoByTable("L120S14A");
            if (s14aList != null && !s14aList.isEmpty()) {
                for (Map<String, Object> s14aMap : s14aList) {
                    String s14aSN = Util
                            .trim(MapUtils.getObject(s14aMap, "SN"));
                    String s14aMainId = Util.trim(MapUtils.getObject(s14aMap,
                            "MAINID"));
                    String s14aCntrno = Util.trim(MapUtils.getObject(s14aMap,
                            "L140A_CNTRNO"));
                    if (Util.notEquals(s14aSN, "1") || Util.isEmpty(s14aMainId)
                            || Util.isEmpty(s14aCntrno)) { // 只取第一筆額度明細表
                        continue;
                    }

                    List<L120S14A> s14aListA = l120s14aDao
                            .findByMainId(s14aMainId);
                    if (s14aListA != null && !s14aListA.isEmpty()) {
                        for (L120S14A l120s14aA : s14aListA) {
                            if (l120s14aA != null) {
                                l120s14aA.setCntrNo(s14aCntrno);
                                s14aListAll.add(l120s14aA);
                            }
                        }
                    }
                    List<L120S14B> s14bListA = l120s14bDao
                            .findByMainId(s14aMainId);
                    if (s14bListA != null && !s14bListA.isEmpty()) {
                        for (L120S14B l120s14bA : s14bListA) {
                            if (l120s14bA != null) {
                                l120s14bA.setCntrNo(s14aCntrno);
                                s14bListAll.add(l120s14bA);
                            }
                        }
                    }
                    List<L120S14C> s14cListA = l120s14cDao
                            .findByMainId(s14aMainId);
                    if (s14cListA != null && !s14cListA.isEmpty()) {
                        for (L120S14C l120s14cA : s14cListA) {
                            if (l120s14cA != null) {
                                l120s14cA.setCntrNo(s14aCntrno);
                                s14cListAll.add(l120s14cA);
                            }
                        }
                    }
                    List<L120S14D> s14dListA = l120s14dDao
                            .findByMainId(s14aMainId);
                    if (s14dListA != null && !s14dListA.isEmpty()) {
                        for (L120S14D l120s14dA : s14dListA) {
                            if (l120s14dA != null) {
                                l120s14dA.setCntrNo(s14aCntrno);
                                s14dListAll.add(l120s14dA);
                            }
                        }
                    }
                }
            }

            // 青創合約書
            List<Map<String, Object>> s14eList = eloandbService
                    .getCntrNoByTable("L120S14E");
            if (s14eList != null && !s14eList.isEmpty()) {
                for (Map<String, Object> s14eMap : s14eList) {
                    String s14eSN = Util
                            .trim(MapUtils.getObject(s14eMap, "SN"));
                    String s14eMainId = Util.trim(MapUtils.getObject(s14eMap,
                            "MAINID"));
                    String s14eCntrno = Util.trim(MapUtils.getObject(s14eMap,
                            "L140A_CNTRNO"));
                    if (Util.notEquals(s14eSN, "1") || Util.isEmpty(s14eMainId)
                            || Util.isEmpty(s14eCntrno)) {
                        continue;
                    }

                    List<L120S14E> s14eListE = l120s14eDao
                            .findByMainId(s14eMainId);
                    if (s14eListE != null && !s14eListE.isEmpty()) {
                        for (L120S14E l120s14eE : s14eListE) {
                            if (l120s14eE != null) {
                                l120s14eE.setCntrNo(s14eCntrno);
                                s14eListAll.add(l120s14eE);
                            }
                        }
                    }
                    List<L120S14B> s14bListE = l120s14bDao
                            .findByMainId(s14eMainId);
                    if (s14bListE != null && !s14bListE.isEmpty()) {
                        for (L120S14B l120s14bE : s14bListE) {
                            if (l120s14bE != null) {
                                l120s14bE.setCntrNo(s14eCntrno);
                                s14bListAll.add(l120s14bE);
                            }
                        }
                    }
                    List<L120S14C> s14cListE = l120s14cDao
                            .findByMainId(s14eMainId);
                    if (s14cListE != null && !s14cListE.isEmpty()) {
                        for (L120S14C l120s14cE : s14cListE) {
                            if (l120s14cE != null) {
                                l120s14cE.setCntrNo(s14eCntrno);
                                s14cListAll.add(l120s14cE);
                            }
                        }
                    }
                    List<L120S14D> s14dListE = l120s14dDao
                            .findByMainId(s14eMainId);
                    if (s14dListE != null && !s14dListE.isEmpty()) {
                        for (L120S14D l120s14dE : s14dListE) {
                            if (l120s14dE != null) {
                                l120s14dE.setCntrNo(s14eCntrno);
                                s14dListAll.add(l120s14dE);
                            }
                        }
                    }
                }

                l120s14aDao.save(s14aListAll);
                l120s14bDao.save(s14bListAll);
                l120s14cDao.save(s14cListAll);
                l120s14dDao.save(s14dListAll);
                l120s14eDao.save(s14eListAll);
                logger.info("執行筆數結果 :\r" + s14aListAll.size() + " === "
                        + s14bListAll.size() + " === " + s14cListAll.size()
                        + " === " + s14dListAll.size() + " === "
                        + s14eListAll.size());
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }
        return errMsg.toString();
    }

    /**
     * J-110-0209 更新QUOTAPPR RESCUEITEM_EL
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0045(JSONObject request) {

        StringBuffer errMsg = new StringBuffer("");

        try {
            int exCount = misQuotapprService.updateRescueItem_elByRescueItem();

            logger.info("doLmsBatch0045 【修改RESCUEITEM_EL】執行結果 : " + exCount
                    + "筆。");

        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();

    }

    /**
     * 整批重送KYC婉卻
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0046(JSONObject request) {

        // http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"lmsbatch0002serviceimpl",request:{type:"46","dataStartDate":"2020-01-03-16.36.16.443000","dataEndDate":"2020-01-03-16.36.16.443000"}}

        StringBuffer errMsg = new StringBuffer("");

        String dataStartDate = Util.trim(request.getString("dataStartDate"));
        String dataEndDate = Util.trim(request.getString("dataEndDate"));
        String[] docStatusArray = new String[] {
                CreditDocStatusEnum.海外_已核准.getCode(),
                CreditDocStatusEnum.海外_婉卻.getCode() };

        if (Util.equals(dataStartDate, "") || Util.equals(dataStartDate, "")) {
            errMsg.append("資料起迄日不得空白(doLmsBatch0046)");
            return errMsg.toString();
        }

        try {
            List<L120M01A> l120m01alist = l120m01aDao
                    .findByDocStatusAndApproveTime(docStatusArray,
                            dataStartDate, dataEndDate);
            if (l120m01alist != null && !l120m01alist.isEmpty()) {
                for (L120M01A l120m01a : l120m01alist) {
                    lmsService.upLnunid(l120m01a, false);
                }
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());

        }

        return errMsg.toString();

    }

    /**
     * 整批重送紐行ORACLE掃描
     *
     * @param request
     */
    @Override
    @NonTransactional
    public String doLmsBatch0047(JSONObject request) {

        // http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"lmsbatch0002serviceimpl",request:{type:"47","dataStartDate":"2021-05-01","dataEndDate":"2020-05-31"}}

        StringBuffer errMsg = new StringBuffer("");

        String dataStartDate = Util.trim(request.getString("dataStartDate"));
        String dataEndDate = Util.trim(request.getString("dataEndDate"));

        if (Util.equals(dataStartDate, "") || Util.equals(dataStartDate, "")) {
            errMsg.append("資料起迄日不得空白(doLmsBatch0047)");
            return errMsg.toString();
        }

        AmlStrategy as = amlRelateService.getAmlStrategy("0A2");
        int count = 0;

        List<String> uniKeys = new ArrayList<String>();
        List<Map<String, Object>> elList = new ArrayList<Map<String, Object>>();
        elList = eloandbService.doLmsBatch0047(dataStartDate, dataEndDate);
        if (elList != null && !elList.isEmpty()) {
            for (Map<String, Object> elMap : elList) {
                String UNIQUEKEY = MapUtils.getString(elMap, "UNIQUEKEY");
                if (Util.notEquals(UNIQUEKEY, "")) {
                    // 等贊介
                    uniKeys.add(UNIQUEKEY);
                }
            }
        }

        if (uniKeys != null && !uniKeys.isEmpty()) {
            // as.reSend(uniKeys);
            count = uniKeys.size();
        }

        logger.info("doLmsBatch0047 執行結果 : " + count + "筆。");

        return errMsg.toString();

    }

    /**
     * J-110-0272 配合授審處「本行授信覆審作業須知」修訂，修改E-Loan企金與個金授信管理系統之「授信覆審作業」，如以下修改內容。
     * 產生企金抽樣覆審名單
     */
    @Override
    @NonTransactional
    public String doLmsBatch0048(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        // 是否清除 L186M01A 同 dataDate 的資料
        String clean = Util.trim(request.getString("clean"));
        String dataDateStr = Util.trim(request.getString("dataDate"));
        Date dataDate = null;
        if (Util.isEmpty(dataDateStr)) {
            // batch 無指定日 => 當年度01月01號
            dataDate = CapDate.getDate(TWNDate.toAD(new Date()).substring(0, 4) + "-01-01","yyyy-MM-dd");
        } else {
            // 如果有指定基準日
            dataDate = Util.parseDate(dataDateStr);
        }
        String randomType = Util.trim(request.getString("randomType"));
        if (Util.isEmpty(randomType)) {
            randomType = LrsUtil.RANDOMTYPE_A_有效額度NTD1000w信保七成以上或十足擔保之含有循環動用案件;
        }
        logger.info("doLmsBatch0048 參數：【clean】" + clean + "、【dataDate】" + dataDate + "、【randomType】" + randomType);
        List<String> brList = new ArrayList<String>();
        try {
            // 是否指定特殊分行
            String sBr = Util.trim(lmsService
                    .getSysParamDataValue("LMS_LRS_RANDOMTYPE_SBR"));

            // 999 代表 全行
            if (Util.notEquals(sBr, "") && Util.notEquals(sBr, "999")) {
                for (String xx : sBr.split(",")) {
                    brList.add(xx);
                }
            } else {
                brList.addAll(retrialService.getBranch("900").keySet());
            }
            logger.info("doLmsBatch0048 參數：【brList】" + brList);
            for (String branch : brList) {
                if(Util.equals(clean, "Y")){
                    List<L186M01A> l186m01aList = l186m01aDao.findByBrAndDateAndType(dataDate, branch, randomType);
                    if (!l186m01aList.isEmpty()) {
                        l186m01aDao.delete(l186m01aList);
                    }
                }
                for (ELF412 elf412 : misELF412Service.findByBranch(branch)) {
                    elf412.setElf412_randomType("");	// 先清空 去年抽樣(有中有覆審會清空、沒中還會有值) 今年可能不是抽樣
                    // 上次覆審日為去年1/1~12/31 且 不覆審代碼為空或8.本次暫不覆審或13
                    if (LrsUtil.isNckdFlag_EMPTY_8(elf412.getElf412_nckdFlag())) {
                        if (!CrsUtil.isNull_or_ZeroDate(elf412.getElf412_lrDate())) {
                            String yyyyStr = TWNDate.toAD(CapDate.addYears(dataDate, -1)).substring(0, 4);
                            Date bgnDate = CapDate.parseDate(yyyyStr + "-01-01");
                            Date endDate = CapDate.parseDate(yyyyStr + "-12-31");
                            if (LMSUtil.cmpDate(elf412.getElf412_lrDate(), ">=", bgnDate)
                                    && LMSUtil.cmpDate(elf412.getElf412_lrDate(), "<=", endDate)) {
                                String custId = Util.trim(elf412.getElf412_custId());
                                String dupNo = Util.trim(elf412.getElf412_dupNo());
                                String endDateStr = TWNDate.toAD(endDate);
                                if(retrialService.isSamplingType(custId, dupNo, endDateStr)){
                                    elf412.setElf412_randomType(randomType);

                                    L186M01A l186m01a = retrialService.findL186M01AByUniqueKey(dataDate, Util.trim(elf412.getElf412_branch()),
                                            Util.trim(elf412.getElf412_custId()), Util.trim(elf412.getElf412_dupNo()), randomType);
                                    if (l186m01a == null) {
                                        l186m01a = new L186M01A();
                                        l186m01a.setDataDate(dataDate);
                                        l186m01a.setBranchId(Util.trim(elf412.getElf412_branch()));
                                        l186m01a.setCustId(Util.trim(elf412.getElf412_custId()));
                                        l186m01a.setDupNo(Util.trim(elf412.getElf412_dupNo()));
                                        l186m01a.setRandomType(randomType);
                                    }
                                    l186m01a.setUpdateTime(CapDate.getCurrentTimestamp());
                                    retrialService.save(l186m01a);
                                }
                            }
                        }
                    }
                    elf412.setElf412_tmestamp(CapDate.getCurrentTimestamp());
                    retrialService.upELF412_DelThenInsert(elf412);
                }
            }
        } catch (Exception e) {
            System.out.println(e.toString());
            errMsg.append(e.toString());
        }

        return errMsg.toString();
    }

    /**
     * J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
     * 將先行動用的動審表資料，寫入貸後管理追蹤事項檔，相關通知機制依現有貸後管理追蹤機制辦理
     */
    @Override
    @NonTransactional
    public String doLmsBatch0051(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        // 是否清除 L186M01A 同 dataDate 的資料
        String startDate = Util.trim(request.getString("startDate"));
        String endDate = Util.trim(request.getString("endDate"));
        if (!Util.isEmpty(startDate) && !Util.isEmpty(endDate)) {
            // 如果有指定基準日，以傳入的日期為準(補跑批次用)
            // 起訖都要指定才給他指定
            startDate = startDate +" 00:00:00";
            endDate = endDate +" 23:59:59";
        } else {
            // batch 無指定日 => 批次執行當天
            String today = CapDate.getCurrentDate("yyyy-MM-dd");
            startDate = today +" 00:00:00";
            endDate = today +" 23:59:59";

        }
        logger.info("doLmsBatch0051 參數：【startDate】" + startDate + "【endDate】" + endDate);
        List<String> brList = new ArrayList<String>();
        try {

            // -------------當日先行動用 start-------------
            // 狀態為 先行動用_已覆核("03O") && 系統執行時間=今天
            // && 有先行動用註記(L160M01A.USETYPE=Y) && 沒有辦妥日(L163S01A.FinishDate is null)
            List<Map<String, Object>> todayNeedElf601List =  eloandbService.findL160m01ANeedElf601(startDate, endDate);
            logger.info("doLmsBatch0051 狀態為 先行動用_已覆核(03O) && 系統執行時間=今天   todayNeedElf601List size: " + todayNeedElf601List.size() + "筆");

            for (Map<String, Object> needElf601Map: todayNeedElf601List) {
                String oid = Util.trim(needElf601Map.get("OID"));// L160M01A.OID
                if(Util.isEmpty(oid) || oid.length() != 32){
                    logger.info("doLmsBatch0051 needElf601Map中oid為空值或不足32碼" + "，oid :" + oid);
                    continue; // 怕資料有問題抓到oid為空白，會造成後面誤刪或新增資料
                }


                String mainId = Util.trim(needElf601Map.get("MAINID"));// L160M01A.MAINID
                String brNo = Util.trim(needElf601Map.get("OWNBRID"));
                //	==> L160M01A.OID + _BATCH_   + Timestamp(到秒) 塞 ELF601_UNID ( KEY )
                String actApproveTimeString = CapDate.convertTimestampToString((Timestamp)needElf601Map.get("SYSACTAPPROVETIME"),"yyyyMMddHHmmss");
                String unid = oid + "_BATCH_"+ actApproveTimeString;// UNID有底線_BATCH_則一定是這支批次產生的ELF601，以及相關的ELF602
                String unidForLike = oid + "_BATCH_";// OID+_BATCH_則一定是這支批次產生的ELF601，以及相關的ELF602，且可以抓到同一筆動審表下的多筆
                boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService.getBranch(brNo).getBrNoFlag());

                // 此筆先行動用核准，要寫入ELF601的資料 start
                ELF601 elf601Insert = new ELF601();
                elf601Insert.setElf601_unid(unid);// 序號(UNID)

                List<L160M01B> l160m01bList = l160m01bDao.findByMainId(mainId);// 動審表額度序號資料
                String cntrnoStringForContent = "";// 額度序號，for後面通知內容用
                if(l160m01bList != null && !l160m01bList.isEmpty()){
                    if(l160m01bList.size() == 1){
                        // 只有一筆
                        String cntrNo = Util.trim(l160m01bList.get(0).getCntrNo());
                        // "XXX 先行動用待辦事項：" + 待辦事項
                        cntrnoStringForContent = cntrNo + " 先行動用待辦事項：";
                        elf601Insert.setElf601_cntrno(cntrNo);
                    }else if(l160m01bList.size() > 1){
                        // 有多筆
                        String cntrNo = Util.trim(l160m01bList.get(0).getCntrNo());
                        // "XXX...等先行動用待辦事項：" + 待辦事項
                        cntrnoStringForContent = cntrNo + "...等先行動用待辦事項：";
                        elf601Insert.setElf601_cntrno("");// 若有多筆額度序號，ELF601此欄位留空即可
                    }
                }
                elf601Insert.setElf601_loan_no("");// 放款帳號，放空白
                elf601Insert.setElf601_loan_kind("LN");// 業務別
                elf601Insert.setElf601_fo_kind("8");// 類別-8其他

                String waitingItem = Util.trim(needElf601Map.get("WAITINGITEM"));
                elf601Insert.setElf601_fo_content(Util.isEmpty(waitingItem) ? "" : cntrnoStringForContent + waitingItem);// 追蹤事項通知內容
                elf601Insert.setElf601_fo_way("1");// 追蹤方式
                elf601Insert.setElf601_fo_cycle(BigDecimal.valueOf(0));// 循環追蹤週期（月）
                // 下次追蹤日: 預定補全-15天， 不足15天下次追蹤日為隔天
                Date willFinishDate = (Date) needElf601Map.get("willFinishDate");
                Date willMinus15Day = CapDate.shiftDays(willFinishDate, -15);// 預定補全日-15天=下次追蹤日
                // 下次追蹤日-限未來日
                // 2022-03-27 -15天，下次追蹤日為2022-03-12
                if(CapDate.getCurrentTimestamp().after(willMinus15Day)){
                    // 今天比下次追蹤日還晚，代表不足15天
                    Date nextDate = CapDate.shiftDays(CapDate.getCurrentTimestamp(), 1);
                    elf601Insert.setElf601_fo_next_date(new java.sql.Date(nextDate.getTime()));// 下次追蹤日
                }else{
                    elf601Insert.setElf601_fo_next_date(new java.sql.Date(willMinus15Day.getTime()));// 下次追蹤日
                }
                elf601Insert.setElf601_staff("02");// 應辦理追蹤對象  02-OA人員
                String apprId = Util.trim(needElf601Map.get("APPRID"));
                String reCheckId = Util.trim(needElf601Map.get("RECHECKID"));
                elf601Insert.setElf601_fo_staffNo("");// 應辦理追蹤對象行編-授信人員
                elf601Insert.setElf601_ao_staffNo(apprId);// 應辦理追蹤對象行編-AO人員
                elf601Insert.setElf601_status("N");// 狀態  N-新增
                elf601Insert.setElf601_cre_date(CapDate.getCurrentTimestamp());// 建檔日期
                elf601Insert.setElf601_cre_teller(apprId);// 建檔經辦  from動審表
                elf601Insert.setElf601_cre_supvno(reCheckId);// 建檔主管  from動審表
                elf601Insert.setElf601_upd_date(CapDate.getCurrentTimestamp());// 建檔日期
                elf601Insert.setElf601_upd_teller(apprId);// 建檔經辦  from動審表
                elf601Insert.setElf601_upd_supvno(reCheckId);// 建檔主管  from動審表
                elf601Insert.setElf601_full_content(Util.trimSizeInOS390(
                        Util.toFullCharString(elf601Insert.getElf601_fo_content()), 402));// 追蹤事項通知內容（全形）
                String custId = Util.trim(needElf601Map.get("CUSTID"));
                elf601Insert.setElf601_custid(custId);
                String dupNo = Util.trim(needElf601Map.get("DUPNO"));
                elf601Insert.setElf601_dupno(dupNo);
                elf601Insert.setElf601_br_no(brNo);
                elf601Insert.setElf601_case_mark("");// 設定案件註記，要給空字串，不然海外DB會ERROR
                // 此筆先行動用核准，要寫入ELF601的資料 end

                // 刪除同動審表oid相關的ELF601、ELF602，並新增ELF601 start
                if(isOverSea){
                    // 同一筆動審表且同一核准時間產生的ELF601，照理講不會有才對
                    ELF601 elf601Delete = obsdbELF601Service.getElf601ByUnid(brNo, unid);
                    if (elf601Delete != null && Util.isNotEmpty(elf601Delete)) {
                        logger.info("doLmsBatch0051 deleteELF601 ByUnid，brNo: " + brNo + ", unid: " + unid);
                        obsdbELF601Service.deleteELF601(brNo, unid);
                    }
                    // 同一筆動審表且同一核准時間產生的ELF602，照理講不會有才對
                    List<ELF602> elf602DeleteList = obsdbELF601Service.getElf602ByDatasrc(brNo, unid);
                    for(ELF602 elf602Delete : elf602DeleteList){
                        if (elf602Delete != null && Util.isNotEmpty(elf602Delete)) {
                            logger.info("doLmsBatch0051 deleteELF602 ByDatasrc，brNo: " + brNo + ", datasrc: " + unid);
                            obsdbELF601Service.deleteELF602(brNo, elf602Delete.getElf602_unid());
                        }
                    }

                    // 同一筆動審表且不同核准時間產生的ELF601，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
                    List<ELF601> elf601LikeDeleteList = obsdbELF601Service.getElf601ByUnidLike(brNo, unidForLike + "%");
                    for(ELF601 elf601LikeDelete : elf601LikeDeleteList){
                        if (elf601LikeDelete != null && Util.isNotEmpty(elf601LikeDelete) && elf601LikeDelete.getElf601_unid().contains(unidForLike)) {
                            logger.info("doLmsBatch0051 deleteELF601 ByUnidLike，brNo: " + brNo + ", unidForLike: " + unidForLike);
                            obsdbELF601Service.deleteELF601(brNo, elf601LikeDelete.getElf601_unid());
                        }
                    }
                    // 同一筆動審表且不同核准時間產生的ELF602，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
                    List<ELF602> elf602LikeDeleteList = obsdbELF601Service.getElf602ByDatasrcLike(brNo, unidForLike + "%");
                    for(ELF602 elf602LikeDelete : elf602LikeDeleteList){
                        if (elf602LikeDelete != null && Util.isNotEmpty(elf602LikeDelete) && elf602LikeDelete.getElf602_datasrc().contains(unidForLike)) {
                            logger.info("doLmsBatch0051 deleteELF602 ByDatasrcLike，brNo: " + brNo + ", datasrcLike: " + unidForLike);
                            obsdbELF601Service.deleteELF602(brNo, elf602LikeDelete.getElf602_unid());
                        }
                    }

                    logger.info("doLmsBatch0051 insertELF601，brNo: " + brNo + ", unid: " + elf601Insert.getElf601_unid());
                    obsdbELF601Service.insertELF601(brNo, elf601Insert);
                } else {
                    // 同一筆動審表且同一核准時間產生的ELF601，照理講不會有才對
                    ELF601 elf601Delete = misdbBASEService.getElf601ByUnid(unid);
                    if(elf601Delete != null && Util.isNotEmpty(elf601Delete)){
                        logger.info("doLmsBatch0051 deleteELF601 ByUnid， unid: " + unid);
                        misdbBASEService.deleteELF601(unid);
                    }

                    // 同一筆動審表且同一核准時間產生的ELF602，照理講不會有才對
                    List<ELF602> elf602DeleteList = misdbBASEService.getElf602ByDatasrc(unid);
                    for(ELF602 elf602Delete : elf602DeleteList){
                        if (elf602Delete != null && Util.isNotEmpty(elf602Delete)) {
                            logger.info("doLmsBatch0051 deleteELF602 ByDatasrc，datasrc: " + unid);
                            misdbBASEService.deleteELF602(elf602Delete.getElf602_unid());
                        }
                    }

                    // 同一筆動審表且不同核准時間產生的ELF601，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
                    List<ELF601> elf601LikeDeleteList = misdbBASEService.getElf601ByUnidLike(unidForLike + "%");
                    for(ELF601 elf601LikeDelete : elf601LikeDeleteList){
                        if (elf601LikeDelete != null && Util.isNotEmpty(elf601LikeDelete) && elf601LikeDelete.getElf601_unid().contains(unidForLike)) {
                            logger.info("doLmsBatch0051 deleteELF601 ByUnidLike，unidForLike: " + unidForLike);
                            misdbBASEService.deleteELF601(elf601LikeDelete.getElf601_unid());
                        }
                    }
                    // 同一筆動審表且不同核准時間產生的ELF602，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
                    List<ELF602> elf602LikeDeleteList = misdbBASEService.getElf602ByDatasrcLike(unidForLike + "%");
                    for(ELF602 elf602LikeDelete : elf602LikeDeleteList){
                        if (elf602LikeDelete != null && Util.isNotEmpty(elf602LikeDelete) && elf602LikeDelete.getElf602_datasrc().contains(unidForLike)) {
                            logger.info("doLmsBatch0051 deleteELF602 ByDatasrcLike，datasrcLike: " + unidForLike);
                            misdbBASEService.deleteELF602(elf602LikeDelete.getElf602_unid());
                        }
                    }

                    logger.info("doLmsBatch0051 insertELF601，unid: " + elf601Insert.getElf601_unid());
                    misdbBASEService.insertELF601(elf601Insert);
                }
                // 刪除同動審表oid相關的ELF601、ELF602，並新增ELF601 end
            }
            // -------------當日先行動用 end-------------

            //	-------------當日補全 start-------------
            //	狀態為 海外_已核准("05O") && 系統執行時間=今天
            // 	當日補全是指當天執行了"登錄"補全
            //	&& 有辦妥日(L163S01A.FinishDate is  not null)
            //	&& 當天 < 預定補全日
            List<Map<String, Object>> todayElf601CompleteList =  eloandbService.findL160m01AElf601Complete(startDate, endDate);
            logger.info("doLmsBatch0051 狀態為 海外_已核准(05O) && 系統執行時間=今天 todayElf601CompleteList size: " + todayElf601CompleteList.size() + "筆");

            for (Map<String, Object> completeElf601Map: todayElf601CompleteList) {
                String oid = Util.trim(completeElf601Map.get("OID"));// L160M01A.OID
                if(Util.isEmpty(oid) || oid.length() != 32){
                    logger.info("doLmsBatch0051 needElf601Map中oid為空值或不足32碼" + "，oid :" + oid);
                    continue; // 怕資料有問題抓到oid為空白，會造成後面誤刪或新增資料
                }
                String brNo = Util.trim(completeElf601Map.get("OWNBRID"));
                Date finishdate = (Date)completeElf601Map.get("FINISHDATE");
                //	==> L160M01A.OID + _BATCH_ + Timestamp(到秒) 塞 ELF601_UNID ( KEY )
                Timestamp actApproveTime = (Timestamp)completeElf601Map.get("SYSACTAPPROVETIME");
                String unidForLike = oid + "_BATCH_";// OID+_BATCH_則一定是這支批次產生的ELF601，以及相關的ELF602，且可以抓到同一筆動審表下的多筆
                boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService.getBranch(brNo).getBrNoFlag());




                if(isOverSea){
                    // ELF601：當天 < 下次追蹤日
                    // set ELF601_STATUS = 'C' (C-解除)
                    // 代表aLoan尚未產生ELF602，要將ELF601設為解除避免產生ELF602!
                    // 該筆動審表產生的ELF601，用like是因為actApproveTime在核准辦妥的時候更動過了，會抓不到當初產生的那一筆ELF601
                    // 只好用L160M01A.OID去like找出
                    List<ELF601> elf601NeedCList = obsdbELF601Service.getElf601ByUnidLike(brNo, unidForLike + "%");
                    for(ELF601 elf601NeedC : elf601NeedCList){
                        if (elf601NeedC != null && Util.isNotEmpty(elf601NeedC)
                                && actApproveTime.before(elf601NeedC.getElf601_fo_next_date())) {
                            logger.info("doLmsBatch0051 updateELF601Status，brNo: " + brNo + ", unid: " + elf601NeedC.getElf601_unid());
                            obsdbELF601Service.updateELF601Status(brNo, elf601NeedC.getElf601_unid(), "C");
                        }
                    }
                    // 該筆動審表產生的ELF602
                    // ELF602：如果已產生的話 && ELF602_STATUS IN ('1','2')
                    // set ELF602_STATUS = '3' (3-已完成), ELF602_FO_MEMO = '已於yyyy-MM-dd'提前補全
                    List<ELF602> elf602NeedCList = obsdbELF601Service.getElf602ByDatasrcLike(brNo, unidForLike + "%");
                    for(ELF602 elf602NeedC : elf602NeedCList){
                        if (elf602NeedC != null && Util.isNotEmpty(elf602NeedC) &&
                                ("1".equals(elf602NeedC.getElf602_status()) || "2".equals(elf602NeedC.getElf602_status()))  ) {
                            logger.info("doLmsBatch0051 updateELF602StatusAndMemo，brNo: " + brNo + ", unid: " + elf602NeedC.getElf602_unid());
                            String memo = "已於" + CapDate.formatDate(finishdate, "yyyy-MM-dd") + "提前補全";
                            obsdbELF601Service.updateELF602StatusAndMemo(brNo, elf602NeedC.getElf602_unid(),
                                    "3", memo);
                        }
                    }
                } else {
                    // ELF601：當天 < 下次追蹤日
                    // set ELF601_STATUS = 'C' (C-解除)
                    // 代表aLoan尚未產生ELF602，要將ELF601設為解除避免產生ELF602!
                    // 該筆動審表產生的ELF601，用like是因為actApproveTime在核准辦妥的時候更動過了，會抓不到當初產生的那一筆ELF601
                    // 只好用L160M01A.OID去like找出
                    List<ELF601> elf601NeedCList = misdbBASEService.getElf601ByUnidLike(unidForLike + "%");
                    for(ELF601 elf601NeedC : elf601NeedCList){
                        if (elf601NeedC != null && Util.isNotEmpty(elf601NeedC)
                                && actApproveTime.before(elf601NeedC.getElf601_fo_next_date())) {
                            logger.info("doLmsBatch0051 updateELF601Status，unid: " + elf601NeedC.getElf601_unid());
                            misdbBASEService.updateELF601Status(elf601NeedC.getElf601_unid(), "C");
                        }
                    }
                    // 該筆動審表產生的ELF602
                    // ELF602：如果已產生的話 && ELF602_STATUS IN ('1','2')
                    // set ELF602_STATUS = '3' (3-已完成), ELF602_FO_MEMO = '已於yyyy-MM-dd'提前補全
                    List<ELF602> elf602NeedCList =  misdbBASEService.getElf602ByDatasrcLike(unidForLike + "%");
                    for(ELF602 elf602NeedC : elf602NeedCList){
                        if (elf602NeedC != null && Util.isNotEmpty(elf602NeedC) &&
                                ("1".equals(elf602NeedC.getElf602_status()) || "2".equals(elf602NeedC.getElf602_status()))  ) {
                            logger.info("doLmsBatch0051 updateELF602StatusAndMemo，unid: " + elf602NeedC.getElf602_unid());
                            String memo = "已於" + CapDate.formatDate(finishdate, "yyyy-MM-dd") + "提前補全";
                            misdbBASEService.updateELF602StatusAndMemo(elf602NeedC.getElf602_unid(),
                                    "3", memo);
                        }
                    }
                }

            }
            // -------------當日補全 start-------------
            logger.info("doLmsBatch0051 end!!");
        } catch (Exception e) {
            logger.error(e.getMessage());
            errMsg.append(e);
        }

        return errMsg.toString();
    }

    /**
     * 發送IXML聯徵T50-T52查詢
     */
    @Override
    public String doLmsBatch0052(JSONObject request) {
        StringBuilder errMsg = new StringBuilder();
        String startDate = Util.trim(request.getString("startDate"));
        String endDate = Util.trim(request.getString("endDate"));
        String currentDate = CapDate.getCurrentDate("yyyy-MM-dd");
        Timestamp currentT = CapDate.getCurrentTimestamp();
        Date currentD = new Date();
        String[] qItems = ClsConstants.IXMLQueryData.qItems;
        String per = ClsConstants.IXMLQueryData.per; // 查詢理由
        String staffNo = ClsConstants.IXMLQueryData.staffNo; // 消金虛擬行員00ZCB3 - IXML查詢
        String fieldId = "IXML";
        String ip = NetUtils.getHostAddress();
        try {
            logger.info("doLmsBatch0052 start!!");
            // 近一日未發查資料，避免驗章時間點卡在23:5X，批次抓不到
            if (Util.isEmpty(startDate)) {
                startDate = CapDate.shiftDaysString(currentDate, "yyyy-MM-dd",
                        -1);
            }
            if (Util.isEmpty(endDate)) {
                endDate = currentDate;
            }
            startDate = startDate + " 00:00:00.0";
            endDate = endDate + " 23:59:59.99";
            Set<String> elf691Keys = new HashSet<String>();
            List<Map<String, Object>> ejf369s = ejcicService.findEJF369();
            List<C122M01G> c122m01gs = c122m01gDao.findUndoneQueryJcic(
                    startDate, endDate);
            for (C122M01G c122m01g : c122m01gs) {
                JSONObject resJson = DataParse.toJSON(c122m01g.getReceivedFTPJsonData());
                if (!"0000".equals(resJson.optString("code"))) {
                    // code非 0000 皆為不成功
                    continue;
                }
                JSONObject jwsJson = DataParse.toJSON(c122m01g.getJsonData());
                String idnBan = jwsJson.optJSONObject("context").optString("idnBan");
                String payload = jwsJson.optJSONObject("jwsSigneddata").optString("payload");
                JSONObject payloadJson = DataParse.toJSON(new String(Base64.decodeBase64(payload)));
                String bankCode = payloadJson.optJSONObject("header").optString("bankCode");
                String qBranch = "";
                if (Util.isNotEmpty(bankCode) && bankCode.length() == 7) {
                    // 1.先取4567碼JCIC查詢對應總行代碼
                    String brId = getEJF369Brid(ejf369s, bankCode.substring(3, 7));
                    if (Util.isNotEmpty(brId)) {
                        qBranch = brId;
                    } else {
                        // 2.若沒有對應的代碼，取第456碼為分行代碼
                        qBranch = bankCode.substring(3, 6);
                    }
                }
                //J-113-0279 判斷保證人移除T52
                C122M01A c122m01a = c122m01g.getC122m01a();
                if (c122m01a!=null) {
                    if (Util.isNotEmpty(c122m01a.getPloanCasePos())) {
                        List<String> list = new ArrayList<String>(Arrays.asList(qItems));
                        list.remove("T52");
                        qItems = list.toArray(new String[0]);
                    }
                }
                // 2.塞資料到DB
                for (String qItem : qItems) {
                    ELF691 elf691 = new ELF691();
                    elf691.setElf691_custid(idnBan);
                    elf691.setElf691_dupno("");
                    elf691.setElf691_q_item(qItem);
                    elf691.setElf691_q_reason(per);
                    elf691.setElf691_brn(qBranch);
                    elf691.setElf691_staffno(staffNo);
                    elf691.setElf691_tmestamp(currentT);
                    elf691.setElf691_key2("");
                    elf691.setElf691_ip(ip);
                    elf691.setElf691_ej_tmestamp(null);
                    // T50:ID+民國年+Q
                    // EX:V279964212107Q
                    if ("T50".equals(qItem)) {
                        elf691.setElf691_key1(idnBan + getT50QueryTWDYear() + "Q");
                    } else {
                        elf691.setElf691_key1(idnBan + "Q");
                    }
                    // 1.是否有重複資料
                    String elf691Key = elf691.getElf691_custid()
                            + elf691.getElf691_dupno()
                            + elf691.getElf691_q_item()
                            + elf691.getElf691_brn()
                            + elf691.getElf691_key1()
                            + elf691.getElf691_key2();
                    if (elf691Keys.contains(elf691Key)) {
                        continue;
                    } else {
                        elf691Keys.add(elf691Key);
                    }
                    // 2.是否已存在今日已送查詢資料
                    List<ELF691> exsitELF691s = misELF691Service.getQueryRecord(
                            elf691.getElf691_custid(),
                            elf691.getElf691_dupno(),
                            elf691.getElf691_q_item(),
                            elf691.getElf691_brn(),
                            elf691.getElf691_key1(),
                            elf691.getElf691_key2(),
                            currentDate);
                    if (exsitELF691s != null && !exsitELF691s.isEmpty()) {
                        continue;
                    }
                    // 2.是否已完成查詢有收檔
                    List<DocFile> exsistFiles = docFileService.findByIDAndName(
                            c122m01g.getMainId(), fieldId, qItem + ".html");
                    if (exsistFiles != null && !exsistFiles.isEmpty()) {
                        continue;
                    }
                    misELF691Service.insert(elf691);
                }
                // 3.寫入發查時間
                // 已查過的寫入重試次數
                if (c122m01g.getQueryJCICTime() != null) {
                    if (c122m01g.getRetry() == null) {
                        c122m01g.setRetry(1);
                    } else {
                        c122m01g.setRetry(c122m01g.getRetry() + 1);
                    }
                }
                c122m01g.setQueryJCICTime(currentD);
                c122m01g.setUpdater("BAT052");
                c122m01g.setUpdateTime(currentD);
            }
            c122m01gDao.save(c122m01gs);
            logger.info("doLmsBatch0052 end!!");
        } catch (Exception e) {
            errMsg.append(e.toString());
        }

        return errMsg.toString();
    }

    /**
     * <pre>取得T50查調所得資料年度</pre>
     * 現在日期<5/1就抓 now-2年  的資料，>=5/1 就抓 now-1年 的資料
     * @return
     */
    private String getT50QueryTWDYear() {
        // 測試環境用107
        boolean isTest = NetUtils.getHostAddress().startsWith("192.168");
        if (isTest) {
            return "107";
        }
        String currentDate = CapDate.getCurrentDate("yyyy-MM-dd");
        String queryYear = null;
        int overdays = CapDate.calculateDays(
                CapDate.getCurrentDate("yyyy-MM-dd"),
                CapDate.getCurrentDate("yyyy-05-01"),
                "yyyy-MM-dd");
        if (overdays < 0) {
            // <5/1就抓 now-2年  的資料，
            queryYear = String.valueOf(TWNDate.valueOf(
                    currentDate).get(Calendar.YEAR) - 2);
        } else {
            // >=5/1 就抓 now-1年 的資料
            queryYear = String.valueOf(TWNDate.valueOf(
                    currentDate).get(Calendar.YEAR) - 1);
        }
        return queryYear;
    }

    /**
     * 取得IXML聯徵T50-T52查詢結果
     */
    @Override
    public Map<DocFile, C122M01A> doLmsBatch0053(JSONObject request) {
        StringBuilder errMsg = new StringBuilder();
        String currentDate = CapDate.getCurrentDate("yyyy-MM-dd");
        Timestamp currentT = CapDate.getCurrentTimestamp();
        Date currentD = new Date();
        String startDate = Util.trim(request.getString("startDate")); // 特別指定開始日期
        String endDate = Util.trim(request.getString("endDate")); // 特別指定結束日期
        String[] qItems = ClsConstants.IXMLQueryData.qItems;
        String ip = NetUtils.getHostAddress();
        String fieldId = "IXML";
        List<String> brList = new ArrayList<String>();
        Map<DocFile, C122M01A> docFileMetaMap = new HashMap<DocFile, C122M01A>();
        try {
            logger.info("doLmsBatch0053 start!!");
            if (Util.isEmpty(startDate)) {
                startDate = "0001-01-01";
            }
            if (Util.isEmpty(endDate)) {
                endDate = currentDate;
            }
            startDate = startDate + " 00:00:00.0";
            endDate = endDate + " 23:59:59.99";
            List<C122M01G> c122m01gs = c122m01gDao.findDoneQueryJcic(startDate,
                    endDate);
            List<Map<String, Object>> ejf369s = ejcicService.findEJF369();
            // 1.抓送查紀錄
            for (C122M01G c122m01g : c122m01gs) {
                JSONObject jwsJson = DataParse.toJSON(c122m01g.getJsonData());
                String idnBan = jwsJson.optJSONObject("context").optString("idnBan");
                String payload = jwsJson.optJSONObject("jwsSigneddata").optString("payload");
                JSONObject payloadJson = DataParse.toJSON(new String(Base64.decodeBase64(payload)));
                String bankCode = payloadJson.optJSONObject("header").optString("bankCode");
                String qBranch = "";
                if (Util.isNotEmpty(bankCode) && bankCode.length() == 7) {
                    // 1.先取4567碼JCIC查詢對應總行代碼
                    String brId = getEJF369Brid(ejf369s, bankCode.substring(3, 7));
                    if (Util.isNotEmpty(brId)) {
                        qBranch = brId;
                    } else {
                        // 2.若沒有對應的代碼，取第456碼為分行代碼
                        qBranch = bankCode.substring(3, 6);
                    }
                }
                String qDate = CapDate.formatDate(c122m01g.getQueryJCICTime(), "YYY/MM/DD");
                C122M01A c122m01a = c122m01g.getC122m01a();
                if (c122m01a == null) {
                    continue;
                }
                //J-113-0279 判斷保證人移除T52
                if (Util.isNotEmpty(c122m01a.getPloanCasePos())) {
                    List<String> list = new ArrayList<String>(Arrays.asList(qItems));
                    list.remove("T52");
                    qItems = list.toArray(new String[0]);
                }
                Map<String, Boolean> qItemDoneMap = new HashMap<String, Boolean>();
                for (String qItem : qItems) {
                    qItemDoneMap.put(qItem, false);
                    // 2.已送查詢資料是否完成
                    // 2.是否已有收檔
//					List<DocFile> exsistFiles = docFileService.findByIDAndName(
//							c122m01a.getMainId(), fieldId, qItem + ".html");
                    // J-113-0068 消金進件管理ixml功能中聯徵資料T50~T52於收回檔案時轉pdf存入
                    List<DocFile> exsistFiles = null;
                    if(clsService.is_function_on_codetype("J-113-0068")){
                        exsistFiles = docFileService.findByIDAndName(
                                c122m01a.getMainId(), fieldId, qItem + ".pdf");
                    }else{
                        exsistFiles = docFileService.findByIDAndName(
                                c122m01a.getMainId(), fieldId, qItem + ".html");
                    }
                    if (exsistFiles != null && !exsistFiles.isEmpty()) {
                        qItemDoneMap.put(qItem, true);
                        continue;
                    }
                    // 2.用查詢紀錄查詢聯徵DB資料是否有結果
                    Map<String, Object> tas500Data = ejcicService
                            .findTAS500(idnBan, qItem, qDate, qBranch);
                    if (tas500Data == null || tas500Data.isEmpty()) {
                        continue;
                    }
                    String query_ymd = MapUtils.getString(tas500Data,
                            "QUERY_YMD");
                    String sn = MapUtils.getString(tas500Data, "SN");
                    if (Util.isEmpty(query_ymd) || Util.isEmpty(sn)) {
                        continue;
                    }
                    // 3.取得HTML
                    Map<String, Object> htmlData = ejcicService
                            .findBT3FILEByIdQDate("H" + qItem,
                                    c122m01a.getCustId(), qDate, qBranch);
                    if (Util.isEmpty(htmlData)) {
                        continue;
                    }
                    String qRpt = Util.trim(MapUtils.getObject(htmlData, "QRPT"));
                    String qEmpCode = MapUtils.getString(htmlData, "QEMPCODE");
                    // 增加浮水印
                    // O-112-0233 調整浮水印，qBranch若在EJF369有對應的VDEPTID就改用VDEPTID
                    String watermark = UtilConstants.兆豐銀行代碼 +
                            ejcicService.findEJF369VDEPTID(qBranch) + " " + qEmpCode + " " + ip;
//					byte[] qRptData = addWatermark(qRpt, watermark).getBytes("big5");
//					DocFile docFile = saveDocFileAndReturn(c122m01a, qRptData, fieldId,
//							"text/html", qItem, "html", "", "9");
                    // J-113-0068 消金進件管理ixml功能中聯徵資料T50~T52於收回檔案時轉pdf存入
                    byte[] qRptData = null;
                    DocFile docFile = null;
                    if(clsService.is_function_on_codetype("J-113-0068")){
                        qRptData = cls1220R14RptService.gen_jcic_pdf_withWaterMark(qRpt, watermark);
                        docFile = saveDocFileAndReturn(c122m01a, qRptData, fieldId,
                                "application/pdf", qItem, "pdf", "", "9");
                    }else{
                        qRptData = addWatermark(qRpt, watermark).getBytes("big5");
                        docFile = saveDocFileAndReturn(c122m01a, qRptData, fieldId,
                                "text/html", qItem, "html", "", "9");
                    }
                    docFileMetaMap.put(docFile, c122m01a);
                    qItemDoneMap.put(qItem, true);
                }
                // 4.存檔
                // 所有查詢都完成且有收檔，更新收檔時間
                List<Boolean> values = new ArrayList<Boolean>(qItemDoneMap.values());
                if (!values.contains(false)) {
                    c122m01g.setReceivedJCICTime(currentD);
                    c122m01g.setUpdater("BAT053");
                    c122m01g.setUpdateTime(currentD);
                    c122m01gDao.save(c122m01g);
                }
            }
            logger.info("doLmsBatch0053 end!!");
        } catch (Exception e) {
            errMsg.append(e.toString());
        }

        return docFileMetaMap;
    }

    /**
     * 查詢聯徵特別編碼BY 分行代碼
     * @param ejf369s
     * @param brId
     * @return
     */
    private String getEJF369JCICCode(List<Map<String, Object>> ejf369s, String brId) {
        for (Map<String, Object> map : ejf369s) {
            String deptId = Util.trim(MapUtils.getObject(map, "EJF369_DEPTID"));
            String vdeptId = Util.trim(MapUtils.getObject(map, "EJF369_VDEPTID"));
            if (deptId.equals(brId)) {
                return vdeptId;
            }
        }
        return "";
    }

    /**
     * 查詢總行代碼BY 聯徵特別編碼
     * @param ejf369s
     * @param jsicCode
     * @return
     */
    private String getEJF369Brid(List<Map<String, Object>> ejf369s, String jsicCode) {
        for (Map<String, Object> map : ejf369s) {
            String deptId = Util.trim(MapUtils.getObject(map, "EJF369_DEPTID"));
            String vdeptId = Util.trim(MapUtils.getObject(map, "EJF369_VDEPTID"));
            if (vdeptId.equals(jsicCode)) {
                return deptId;
            }
        }
        return "";
    }

    /**
     * DocFile存檔
     * @param meta
     * @param bytes
     * @param fieldId
     * @param contentType
     * @param attchFileName
     * @param attchFileExt
     * @param attchMemo
     * @param flag
     * @return
     */
    private DocFile saveDocFileAndReturn(C122M01A meta, byte[] bytes, String fieldId,
                                         String contentType, String attchFileName, String attchFileExt,
                                         String attchMemo, String flag) {
        DocFile docFile = new DocFile();
        docFile.setBranchId(meta.getOwnBrId());
        docFile.setContentType(contentType);
        docFile.setMainId(meta.getMainId());
        docFile.setPid(null);
        docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
        docFile.setFieldId(fieldId);
        docFile.setDeletedTime(null);
        docFile.setSrcFileName(attchFileName + "." + attchFileExt);
        docFile.setUploadTime(CapDate.getCurrentTimestamp());
        docFile.setSysId("LMS");
        docFile.setFileSize(bytes.length);
        docFile.setData(bytes);
        docFile.setFileDesc(Util.isNotEmpty(attchMemo) ? attchMemo: attchFileName);
        docFile.setFlag(flag);
        docFileService.save(docFile);
        return docFile;
    }

    /**
     */
    @Override
    public String doLmsBatch0054(Map<DocFile, C122M01A> docFileMetaMap) {
        StringBuilder errMsg = new StringBuilder();
        try {
            logger.info("doLmsBatch0054 start!!");
            // 5.上傳文件數位化
            for (Entry<DocFile, C122M01A> entry : docFileMetaMap.entrySet()) {
                eLoanUploadImageFile(entry.getKey(), entry.getValue());
            }
            logger.info("doLmsBatch0054 end!!");
        } catch (Exception e) {
            errMsg.append(e.toString());
        }

        return errMsg.toString();
    }

    /**
     * 貸後通知範圍:
     * 貸後調查通知對象                           維護內容
     * 1.SBT議合法產業別(註1)&大型企業&本行尚有額度  SBT登錄狀態(註2)
     * 2.3510電力供應業&本行尚有額度               減排資料
     *                                         (若非再生能源發電者，亦維護「排碳資訊」)
     * 3.服務商業建築產業別(註1)&大型企業&非短期額度  服務、商業自有不動產樓地板面積
     * &有餘額
     * 4.國內營業單位公司&僅中長期額度&非上市櫃公司者  排碳資訊(註3)
     *
     * 上述均發送予有效額度最大分行，並於每年1月1日通知貸後名單。
     * 註1：「SBT議合法產業別、服務商業建築產業別」另有對應行業別名單。
     * 註2：即使Targets set，仍有可能Removed，故採逐年維護。
     * 註3：短期額度，會於聯徵問卷維護，免通知；上市櫃公司有資料庫，免通知，另海外營業單位部分，另行規劃。
     *
     * http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"lmsbatch0002serviceimpl",request:{type:"55",foDate:"2024-09-23"}}
     *
     * @param request
     * @return
     */
    @Override
    public String doLmsBatch0055(JSONObject request) {
        StringBuilder errMsg = new StringBuilder();
        logger.info("doLmsBatch0055 begin!!");
        String foDate = Util.trim(request.optString("foDate")); // 特別指定FODate
        try {
            List<ELF602> overseaElf602List = new ArrayList<ELF602>();
            List<ELF602> elf602List = new ArrayList<ELF602>();
            for (int type = 1; type <= 4; type++) {
                List<Map<String, Object>> list = dwdbBASEService.findDW_ESGLOANLIST_SSByType(String.valueOf(type));
                logger.info("doLmsBatch0055 DW_ESGLOANLIST_SSByCYCMN type"+type+" list size:"+ list.size()+"筆");
                for (Map<String, Object> map : list) {
                    String brNo = Util.trim(map.get("BR_NO"));
                    String custId = Util.trim(map.get("CUSTID"));
                    // 因為AS400來的重覆碼有空白的,所以要先判斷空白時放0
                    String dupNo = CapString.isEmpty(Util.trim(map.get("DUPNO"))) ? "0" : Util.trim(map.get("DUPNO"));
                    //	==> L160M01A.OID + _BATCH_   + Timestamp(到秒) 塞 ELF601_UNID ( KEY )
                    String oid = IDGenerator.getUUID();
                    String unid = custId + "_0055BATCH" +CapDate.getCurrentDate("yyyyMM") + type +"_" + oid;// UNID有底線_BATCH_則一定是這支批次產生的ELF602
                    String contract = Util.trim(MapUtils.getObject(map, "CONTRACT"));
                    String content = "";//追蹤事項通知內容
                    switch (type) {
                        case 1://1.SBT議合法產業別(註1)&大型企業&本行尚有額度
                            content = "1.請至「徵信系統-ESG資料暨貸後調查表」→「貸後調查」→維護「SBT登錄狀態」。請於114.1.9（四）前完成";
                            break;
                        case 2://2.3510電力供應業&本行尚有額度
                            content = "2.請至「徵信系統-ESG資料暨貸後調查表」→「貸後調查」→維護「9-2減排資料」。請於114.1.9（四）前完成";
                            break;
                        case 3: // 3.服務商業建築產業別(註1)&大型企業&非短期額度&有餘額
                            content = "3.請至「徵信系統-ESG資料暨貸後調查表」→「貸後調查」→維護「服務、商業自有不動產樓地板面積」。請於114.1.9（四）前完成";
                            break;
                        case 4: //4.國內營業單位公司&僅中長期額度&非上市櫃公司者
                            content = "4.請至「徵信系統-ESG資料暨貸後調查表」→「貸後調查」→維護「排碳資訊」。請於114.1.9（四）前完成";
                            break;

                    }
                    boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService.getBranch(brNo).getBrNoFlag());
                    ELF602 newelf602 = new ELF602();
                    // 此筆先行動用核准，要寫入ELF601的資料 start
                    newelf602.setElf602_unid(unid); // 序號
                    newelf602.setElf602_cntrno(contract); // 額度序號
                    newelf602.setElf602_loan_no(""); // 放款帳號
                    newelf602.setElf602_loan_kind("LN"); // 業務別(Financial
                    // management)
                    newelf602.setElf602_fo_kind("8"); // 類別-其它
                    newelf602.setElf602_fo_content(content); // 追蹤事項通知內容
                    newelf602.setElf602_staff("01"); // 應辦理追蹤對象
                    java.sql.Date today = new java.sql.Date(new Date().getTime());
                    if(Util.isEmpty(foDate)) {
                        newelf602.setElf602_fo_date(today); // 追蹤事項通知日期
                    }else{
                        newelf602.setElf602_fo_date(new java.sql.Date(CapDate.getDate(foDate, "yyyy-MM-dd").getTime()));
                    }
                    newelf602.setElf602_chkdate(today); // 檢核日期
                    newelf602.setElf602_conform_fg(""); // 符合註記
                    newelf602.setElf602_fo_memo(""); // 追蹤說明
                    newelf602.setElf602_status("1"); // 狀態：1-未辦理
                    newelf602.setElf602_datasrc(unid); // 資料寫入來源
                    newelf602.setElf602_unusual_fg(""); // 還款來源異常註記
                    newelf602.setElf602_unusualdesc(""); // 理由敘述
                    newelf602.setElf602_isnotional(""); // 是否承做
                    newelf602.setElf602_isaml(""); // 是否申報疑似洗錢
                    newelf602.setElf602_upd_date(null); // 最近維護日期
                    newelf602.setElf602_upd_teller(""); // 最近維護經辦
                    newelf602.setElf602_upd_supvno(""); // 最近維護主管
                    newelf602.setElf602_full_content(Util
                            .toFullCharString(content)); // 追蹤事項通知內容（全形）
                    newelf602.setElf602_fieldMainId(""); // 附加文件 mainId
                    newelf602.setElf602_fileDesc(""); // 證明文件說明
                    newelf602.setElf602_custid(custId);
                    newelf602.setElf602_dupno(dupNo);
                    newelf602.setElf602_br_no(brNo);
                    newelf602.setElf602_case_mark("");

                    // 新增ELF602 start
                    if (isOverSea) {
                        overseaElf602List.add(newelf602);
                    } else {
                        elf602List.add(newelf602);
                    }
                }
            }
            obsdbELF601Service.insertElf602(overseaElf602List);
            misdbBASEService.insertELF602(elf602List);
            logger.info("doLmsBatch0055 end!!");
        } catch (Exception e) {
            logger.error(e.getMessage());
            errMsg.append(e);
        }

        return errMsg.toString();
    }

    /**
     * J-113-0402
     * 檢查
     * 貸後通知範圍:
     * 依本行「企業授信ESG風險評級作業須知」第七條，為利追蹤ESG風險評級辦理頻率，新增貸後通知及報表(LLDLN350.LLWLN350)內容如下：
     * 1.請每月於授信管理系統「貸後管理追蹤檢核表」，針對ESG風險評級即將屆期之案件進行通知(中、低ESG風險至少每五年辦理一次、高ESG風險至少每年辦理一次，
     *   例如中風險最近一次評等日期112.12.10，屆期日為117.12.10，貸後通知117.11月)，且於本行尚有有效額度者，發送額度最大分行，通知分行重新辦理評級。
     * 2.追蹤事項通知內容提示「ESG風險評級即將屆期，請重新辦理ESG風險評級」。
     *
     * http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"lmsbatch0002serviceimpl",request:{type:"56",foDate:"2024-09-23"}}
     *
     * @param request
     * @return
     */
    @Override
    public String doLmsBatch0056(JSONObject request) {
        StringBuilder errMsg = new StringBuilder();
        final String DATE_FORMATE = "yyyy-MM-dd";
        logger.info("doLmsBatch0056 begin!!");
        String foDate = Util.trim(request.optString("foDate")); // 特別指定FODate
        try {
            if(CapString.isEmpty(foDate)){
                Calendar today = Calendar.getInstance();
                today.set(Calendar.DAY_OF_MONTH, 1);
                SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMATE);
                foDate = sdf.format(today.getTime());
            }else if (!CapDate.isMatchPattern(foDate,DATE_FORMATE)){
                throw new CapMessageException("日期格式有誤:"+ foDate,getClass());
            }
            List<ELF602> overseaElf602List = new ArrayList<ELF602>();
            List<ELF602> elf602List = new ArrayList<ELF602>();
            // foDate加一個月到期案件
            List<Map<String, Object>> list = dwdbBASEService.findDW_ESGLOANLIST_SSByRiskRating(foDate);
            logger.info("doLmsBatch0056 DW_ESGLOANLIST_SSByRiskRating list size:"+ list.size()+"筆");
            for (Map<String, Object> map : list) {
                String brNo = Util.trim(map.get("BR_NO"));
                String custId = Util.trim(map.get("CUSTID"));
                // 因為AS400來的重覆碼有空白的,所以要先判斷空白時放0
                String dupNo = CapString.isEmpty(Util.trim(map.get("DUPNO"))) ? "0" : Util.trim(map.get("DUPNO"));

                String oid = IDGenerator.getUUID();
                String unid = custId + "_0056BATCH" +CapDate.getCurrentDate("yyyyMM") + "_" + oid;// UNID有底線_BATCH_則一定是這支批次產生的ELF602
                String contract = Util.trim(MapUtils.getObject(map, "CONTRACT"));
                String content = "ESG風險評級即將屆期，請重新辦理ESG風險評級";//追蹤事項通知內容
                boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService.getBranch(brNo).getBrNoFlag());
                ELF602 newelf602 = new ELF602();
                // 此筆先行動用核准，要寫入ELF601的資料 start
                newelf602.setElf602_unid(unid); // 序號
                newelf602.setElf602_cntrno(contract); // 額度序號
                newelf602.setElf602_loan_no(""); // 放款帳號
                newelf602.setElf602_loan_kind("LN"); // 業務別(Financial
                // management)
                newelf602.setElf602_fo_kind("8"); // 類別-其它
                newelf602.setElf602_fo_content(content); // 追蹤事項通知內容
                newelf602.setElf602_staff("01"); // 應辦理追蹤對象
                java.sql.Date today = new java.sql.Date(new Date().getTime());
                if(Util.isEmpty(foDate)) {
                    newelf602.setElf602_fo_date(today); // 追蹤事項通知日期
                }else{
                    newelf602.setElf602_fo_date(new java.sql.Date(CapDate.getDate(foDate, "yyyy-MM-dd").getTime()));
                }
                newelf602.setElf602_chkdate(today); // 檢核日期
                newelf602.setElf602_conform_fg(""); // 符合註記
                newelf602.setElf602_fo_memo(""); // 追蹤說明
                newelf602.setElf602_status("1"); // 狀態：1-未辦理
                newelf602.setElf602_datasrc(unid); // 資料寫入來源
                newelf602.setElf602_unusual_fg(""); // 還款來源異常註記
                newelf602.setElf602_unusualdesc(""); // 理由敘述
                newelf602.setElf602_isnotional(""); // 是否承做
                newelf602.setElf602_isaml(""); // 是否申報疑似洗錢
                newelf602.setElf602_upd_date(null); // 最近維護日期
                newelf602.setElf602_upd_teller(""); // 最近維護經辦
                newelf602.setElf602_upd_supvno(""); // 最近維護主管
                newelf602.setElf602_full_content(Util.toFullCharString(content)); // 追蹤事項通知內容（全形）
                newelf602.setElf602_fieldMainId(""); // 附加文件 mainId
                newelf602.setElf602_fileDesc(""); // 證明文件說明
                newelf602.setElf602_custid(custId);
                newelf602.setElf602_dupno(dupNo);
                newelf602.setElf602_br_no(brNo);
                newelf602.setElf602_case_mark("");

                // 新增ELF602 start
                if (isOverSea) {
                    overseaElf602List.add(newelf602);
                } else {
                    elf602List.add(newelf602);
                }
            }

            obsdbELF601Service.insertElf602(overseaElf602List);
            misdbBASEService.insertELF602(elf602List);
            logger.info("doLmsBatch0056 end!!");
        } catch (Exception e) {
            logger.error(e.getMessage());
            errMsg.append(e);
        }

        return errMsg.toString();
    }

    public static void copyFile(File srcFile, File destFile) throws IOException {
        InputStreamReader reader = null;
        OutputStreamWriter writer = null;
        try {
            // Ensure the parent directories exist
            if (destFile.getParentFile() != null) {
                destFile.getParentFile().mkdirs();
            }
            reader = new InputStreamReader(new FileInputStream(srcFile), "BIG5");
            writer = new OutputStreamWriter(new FileOutputStream(destFile), "BIG5");

            char[] buffer = new char[1024];
            int length;
            while ((length = reader.read(buffer)) > 0) {
                writer.write(buffer, 0, length);
            }
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void zipDirectory(File dir, String zipDirName) throws ZipException, IOException {
        FileOutputStream fos = new FileOutputStream(zipDirName);
        ZipOutputStream zos = new ZipOutputStream(fos);
        zos.setEncoding("BIG5"); // 設置 Zip 文件使用 BIG5 編碼
        addDirToZipArchive(zos, dir, "");
        zos.close();
        fos.close();
    }

    private void addDirToZipArchive(ZipOutputStream zos, File fileToZip, String parentDirectoryName) throws IOException {
        if (fileToZip == null || !fileToZip.exists()) {
            return;
        }

        String zipEntryName = parentDirectoryName + fileToZip.getName();
        if (fileToZip.isDirectory()) {
            for (File file : fileToZip.listFiles()) {
                addDirToZipArchive(zos, file, zipEntryName + "/");
            }
        } else {
            BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(fileToZip), "ISO-8859-1"));
            ZipEntry zipEntry = new ZipEntry(zipEntryName);
            zos.putNextEntry(zipEntry);
            char[] buffer = new char[1024];
            int length;
            while ((length = reader.read(buffer)) >= 0) {
                zos.write(length);
            }
            zos.closeEntry();
            reader.close();
        }
    }
    /**
     * J-113-0490,007623 公司訪談紀錄表批次調閱,企金上傳CUSTID抓取全行貸後公司訪談紀錄表並將附件zip回傳
     * http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"lmsbatch0002serviceimpl",request:{type:"57",custId:"28840928,86870786,73251209",userId:"007623"}}
     * @param request
     * @return
     */
    @Override
    public String doLmsBatch0057(JSONObject request) {
        StringBuilder errMsg = new StringBuilder();
        logger.info("doLmsBatch0057 begin!!");
        final String batchFolder = "/elnfs/LMS/940/batch0057/";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String executeTime = sdf.format(CapDate.getCurrentTimestamp());
        File tmpFolderPath = new File( batchFolder + executeTime + File.separator);
        String batExecuter = request.optString("userId");
        String custIds = Util.trim(request.optString("custId")); // custId用逗號分隔
        String body = "<table><tr><td colspan='2'>於{0}批次執行完成，請於Notes上直接下載結果檔。</td></tr>" +
                "<tr><td colspan='2'><a href=\"{1}\" target='_blank'>下載ZIP</a></tr>" +
                "<tr><td colspan='2'>查詢ID有:{2}。</td></tr></table>";

        try {
            // 檢查並刪除超過一個月的目錄或文件
            File batchDir = new File(batchFolder);
            if (batchDir.exists() && batchDir.isDirectory()) {
                File[] files = batchDir.listFiles();
                if (files != null) {
                    long currentTime = System.currentTimeMillis();
                    for (File file : files) {
                        if (file.isDirectory() || file.isFile()) {
                            long lastModified = file.lastModified();
                            long diff = currentTime - lastModified;
                            long diffDays = TimeUnit.MILLISECONDS.toDays(diff);
                            if (diffDays > 30) { // 超過30天
                                if (file.isDirectory()) {
                                    FileUtils.deleteDirectory(file);
                                } else {
                                    file.delete();
                                }
                            }
                        }
                    }
                }
            }
            if(!CapString.isEmpty(custIds)) {
				PageParameters pageParameters = new CapMvcParameters();
                pageParameters.put("searchType","filter");
                pageParameters.put("custId",custIds);
                pageParameters.put(EloanConstants.DOC_STATUS,CreditDocStatusEnum.海外_已核准.getCode());
                pageParameters.put("followUpType","D"); // 追蹤紀錄
                pageParameters.put("followKind","10");// 公司訪談紀錄表
                pageParameters.put("for940","Y");// 公司訪談紀錄表
                List<Map<String,Object>> list = lms8000Service.findByFilter(pageParameters);
                if(!list.isEmpty()) {
                    FileOutputStream fos = null;
                    OutputStream outputStream= null;
                    InputStream inputStream = null;
                    Properties prop = MessageBundleScriptCreator.getComponentResource(LMS8000M01Page.class);
                    Locale locale = (Session.get() != null) ? Session.get().getLocale()
                            : LocaleContextHolder.getLocale();
                    if (locale == null)
                        locale = Locale.getDefault();
                    try {
                        if(!tmpFolderPath.exists()){
                            tmpFolderPath.mkdirs();
                        }

                        for (Map<String,Object> m : list) {
                            String custId = MapUtils.getString(m,"custId");
                            String oid = MapUtils.getString(m,"coid");
                            String randomCode_S01D = MapUtils.getString(m,"randomCode_S01D");
							PageParameters params = new CapMvcParameters();
                            params.put("oidL260M01D", oid);
                            params.put("type", "R06");
                            params.put("fileDownloadName", "lms8000R06.pdf");
                            params.put("randomCode_S01D", randomCode_S01D);

                            outputStream = lms8000R01RptService.genLMS8000R06(params,prop,locale);
                            String approveTime = CapDate.convertTimestampToString((Timestamp) m.get("approveTime"),"yyyyMMdd");

                            File pdfFile = new File(tmpFolderPath.getAbsolutePath() + File.separator + custId + File.separator + approveTime + File.separator + "lms8000R06.pdf");
                            pdfFile.getParentFile().mkdirs(); // Ensure the parent directories exist

                            fos = new FileOutputStream(pdfFile);
                            inputStream = new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray());
                            IOUtils.copy(inputStream, fos);

                            ISearch searchSetting = docFileDao.createSearchTemplete();
                            searchSetting.addSearchModeParameters(SearchMode.OR,
                                    new SearchModeParameter(SearchMode.EQUALS, "mainId", oid),
                                    new SearchModeParameter(SearchMode.EQUALS, "pid", oid));

                            // pageSetting.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
                            searchSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
                                    "postLoanCertified");
							List<DocFile> fileList = docFileDao.find(searchSetting);
                            for (DocFile docFile : fileList) {
                                File file = docFileService.getRealFile(docFile);
                                if(file.exists()){
                                    File copyToPath = new File(tmpFolderPath.getAbsolutePath() + File.separator +
                                            custId + File.separator + approveTime + File.separator + file.getName());
                                    FileUtils.copyFile(file,copyToPath);
                                }
                            }
                        }
                        String zipPath = batchFolder + File.separator + executeTime + ".zip";
                        File zipFile = new File(zipPath);

                        // 使用 zipDirectory 方法壓縮目錄
                        //zipDirectory(tmpFolderPath, zipPath);
                        ZipUtils.zip(tmpFolderPath.getAbsolutePath(),zipPath,true,"");
                        DocFile docFile = new DocFile();
                        docFile.setBranchId("940");
                        docFile.setMainId(executeTime);
                        docFile.setContentType("application/zip");
                        docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
                        docFile.setFieldId("bat0057");
                        docFile.setSysId("LMS");
                        docFile.setFileDesc("企金處J1130490公司訪談紀錄表");
                        docFile.setFileSize(zipFile.length());
                        docFile.setUploadTime(CapDate.getCurrentTimestamp());
                        docFile.setSrcFileName("企金處公司訪談紀錄表.zip");
                        docFile.setData(FileUtils.readFileToByteArray(zipFile));
                        docFileService.save(docFile);
                        String localIp = NetUtils.getHostAddress();
                        boolean isTest = localIp.startsWith("192.168");

                        String downloadLink = (isTest?(localIp.endsWith("53.85")?"https://eloansit.megabank-t.com.tw/lms-web/":
                                localIp.endsWith("53.86")?"https://eloanuat.megabank-t.com.tw/lms-web/":"http://localhost/lms-web/"):
                                "https://eloan.megabank.com.tw/lms-web/") + "app/simple/FileProcessingPageForLmsBat0057?fileOid=" + docFile.getOid();
                        String ssoUrl = getUrlToLoginSSO(batExecuter,downloadLink);
                        String[] maillist = new String[1];
                        if(isTest){
                            maillist[0] = "<EMAIL>";
                        }else{
                            maillist[0] = batExecuter + "@notes.megabank.com.tw";
                        }
                        String bodyHtml = MessageFormat.format(body,executeTime,ssoUrl,custIds);
                        emailClient.send(false,"<EMAIL>",maillist,"公司訪談紀錄表批次調閱",
                                bodyHtml);
                    } catch (IOException e) {
                        logger.error(e.getMessage(),getClass());
                        e.printStackTrace();
                    } finally {
                        IOUtils.closeQuietly(inputStream);
                        IOUtils.closeQuietly(outputStream);
                        IOUtils.closeQuietly(fos);
                    }
                }else{
                    errMsg.append("custId 無資料");
                    logger.info("doLmsBatch0057 end!!");
                }
            }else{
                errMsg.append("custId 無資料");
                logger.info("doLmsBatch0057 end!!");
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            errMsg.append(e);
        }

        return errMsg.toString();
    }

    @Override
    @NonTransactional
    public String doLmsBatch0058(JSONObject request) {
        StringBuffer errMsg = new StringBuffer("");

        // 抓L120M01A的CREATETIME 在區間裡的資料
        String startDate = Util.trim(request.getString("startDate"));
        String endDate = Util.trim(request.getString("endDate"));
        if (!Util.isEmpty(startDate) && !Util.isEmpty(endDate)) {
            // 如果有指定基準日，以傳入的日期為準(補跑批次用)
            // 起訖都要指定才給他指定
            startDate = startDate +" 00:00:00";
            endDate = endDate +" 23:59:59";
        } else {
            // batch 無指定日 => 批次執行當天
            String today = CapDate.getCurrentDate("yyyy-MM-dd");
            startDate = today +" 00:00:00";
            endDate = today +" 23:59:59";

        }
        logger.info("doLmsBatch0058 參數：【startDate】" + startDate + "【endDate】" + endDate);
        List<String> brList = new ArrayList<String>();
        try {

            List<Map<String, Object>> noL120m01lList =  eloandbService.findL120m01aWithNoL120m01l(startDate, endDate);
            logger.info("doLmsBatch0058 noL120m01lList size: " + noL120m01lList.size() + "筆");

            for (Map<String, Object> noL120m01lMap: noL120m01lList) {
                String mainId = Util.trim(noL120m01lMap.get("MAINID"));// L120M01A.MAINID
                lmsService.reCalSuggestCaseLvlCheck(mainId, "batch");
            }
            logger.info("doLmsBatch0058 end!!");
        } catch (Exception e) {
            logger.error(e.getMessage());
            errMsg.append(e);
        }

        return errMsg.toString();
    }

    final String SSO_WEB_SERVICE_XML = "<?xml version=\"1.0\" encoding=\"utf-8\"?><soap12:Envelope xmlns:xsi" +
            "=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" " +
            "xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\"><soap12:Body><GetUrlToLoginSSO xmlns=\"http" + "://tempuri.org/\"><empID>{0}</empID><apID>EL</apID><sourceUrl>{1}</sourceUrl></GetUrlToLoginSSO" + "></soap12" + ":Body></soap12:Envelope>";
    /**
     * 取得SSO Web Service URL
     *
     * @param userId
     * @param url
     * @return
     * @throws Exception
     */
    private String getUrlToLoginSSO(String userId, String url) throws Exception {
        JSONObject json = new JSONObject();
        String xml = MessageFormat.format(SSO_WEB_SERVICE_XML, new Object[] { userId, url });
        logger.info("SSO_WEB_SERVICE:{}", xml);
        json.put(SSOWebServiceClient.REQ_XML, xml);
        String response = null;
        try {
            response = ssoWebServiceClient.doSend(json);
        } catch (Exception e) {
            throw new CapException("取得SSO URL錯誤!!", e, getClass());
        }
        // sample :<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://www.w3
        // .org/2003/05/soap-envelope" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3
        // .org/2001/XMLSchema"><soap:Body><GetUrlToLoginSSOResponse xmlns="http://tempuri
        // .org/"><GetUrlToLoginSSOResult>http://192.168.51.224/EMPSSO/?fn=WS&amp;
        // parm=kG2czOag0m4iOHFt7uHqtdVeBWZ9xHvMK4F%2fsVLxeaU%3d</GetUrlToLoginSSOResult></GetUrlToLoginSSOResponse
        // ></soap:Body></soap:Envelope>
        logger.info("SSO_Responae:{}", response);
        ByteArrayInputStream bi = null;
        try {
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            bi = new ByteArrayInputStream(response.getBytes());
            Document doc = dBuilder.parse(bi);
            NodeList nList = doc.getElementsByTagName("GetUrlToLoginSSOResult");
            Node node = nList.item(0);
            return node.getTextContent();
        } catch (Exception e) {
            throw new CapException("取得處理SSO URL回傳XML錯誤!!", e, getClass());
        } finally {
            if (bi != null) {
                bi.close();
            }
        }
    }
    /**
     * 編制上傳JSON&傳送至文件數位化FTP
     * @param docFile
     * @param meta
     * @throws CapException
     */
    private void eLoanUploadImageFile(DocFile docFile, C122M01A meta) throws CapException {
        String mainId = meta.getMainId();
        String caseno = meta.getPloanCaseNo();
        String branch = meta.getOwnBrId();
        String borrower = meta.getCustId();
        String applicationDate = CapDate.convertTimestampToString(meta.getApplyTS(), "yyyyMMdd");
        String stakeholderID = meta.getCustId();
        String userCode = "BAT053";
        String applyKind = meta.getApplyKind();
        // 查詢主借人
        // 依主檔的種類查詢主債務人
        String queryApplyKind = "";
        if (Util.equals(UtilConstants.C122_ApplyKind.Q, applyKind)) {
            // 信貸
            queryApplyKind = UtilConstants.C122_ApplyKind.P;
        } else if (Util.equals(UtilConstants.C122_ApplyKind.F, applyKind)) {
            // 房貸
            queryApplyKind = UtilConstants.C122_ApplyKind.E;
        }
        if (!CapString.isEmpty(queryApplyKind)) {
            C122M01A mainCaseC122M01As = c122m01aDao.findPloanParentByApplyKind_ploanCaseId(
                    queryApplyKind, meta.getPloanCaseId());
            if (Util.isNotEmpty(mainCaseC122M01As)) {
                caseno = mainCaseC122M01As.getPloanCaseNo();
                branch = mainCaseC122M01As.getOwnBrId();
                borrower = mainCaseC122M01As.getCustId();
                applicationDate = CapDate.convertTimestampToString(mainCaseC122M01As.getApplyTS(), "yyyyMMdd");
            }
        }

        String docFileOid = docFile.getOid();
        C900M01M c900m01m = new C900M01M();
        try {
            // 不可接受檔案類型
            if (!mEGAImageService.isAvalibleFileType(docFile)) {
                return;
            }
            String formId = "";
            if (docFile.getSrcFileName().startsWith("T50")) {
                formId = "ECL00010";
            } else if (docFile.getSrcFileName().startsWith("T51")) {
                formId = "ECL00011";
            } else if (docFile.getSrcFileName().startsWith("T52")) {
                formId = "ECL00012";
            }
            if (!CapString.isEmpty(formId) && !CapString.isEmpty(docFileOid)) {
                // 上傳指定項目
                c900m01m.setApplicationDate(applicationDate);
                c900m01m.setBorrower(borrower);
                c900m01m.setBranch(branch);
                c900m01m.setCaseNo(caseno);
                c900m01m.setDocFileOid(docFileOid);
                c900m01m.setFormId(formId);
                c900m01m.setMainId(mainId);
                c900m01m.setStakeholderID(stakeholderID);
                c900m01m.setUpdateTime(CapDate.getCurrentTimestamp());
                c900m01m.setUserCode("BAT053");
            }
            // 依上傳紀錄檔內容與文件類型執行上傳文件數位化作業
            clsService.eLoanUploadImageFile(c900m01m, meta);
        } catch (Exception e) {
            logger.error(StrUtils.getStackTrace(e));
            throw new CapException(e, getClass());
        }
    }

    /**
     * 聯徵HTML加浮水印
     * @param html
     * @param watermark
     * @return
     */
    private String addWatermark(String html, String watermark) {
        StringBuilder sb = new StringBuilder();
        String style = "<style>\r\n" +
                "	p.watermark {\r\n" +
                "		font-size:38;\r\n" +
                "		color=#FCFCFC;\r\n" +
                "		z-index:-1;\r\n" +
                "		position:absolute;\r\n" +
                "		/*filter:Alpha(Opacity=30, FinishOpacity=30, Style=2);*/\r\n" +
                "		filter: Alpha(Opacity=30);/* for IE */\r\n" +
                "		Opacity:0.3;/* for Firefox */\r\n" +
                "	}\r\n" +
                "</style>\r\n";
        String script = "<script>\r\n" +
                "	var sameIdIndex = 0;\r\n" +
                "	function reNameSameId(id) {\r\n" +
                "		var e = document.getElementById(id);\r\n" +
                "		if (e) {\r\n" +
                "			document.getElementById(id).setAttribute(\"id\", id + sameIdIndex);\r\n" +
                "			sameIdIndex++;\r\n" +
                "			reNameSameId(id);\r\n" +
                "		}\r\n" +
                "	}\r\n" +
                "	function watermark(msg){\r\n" +
                "		//var lastone = document.getElementById('wmitest1');\r\n" +
                "		reNameSameId('wmitest1');\r\n" +
                "		var lastone = document.getElementById('wmitest1' + (sameIdIndex -1));\r\n" +
                "		var str=\"\";\r\n" +
                "		var tH=lastone.offsetTop;   \r\n" +
                "	\r\n" +
                "		var iT=Math.round(tH/1056);\r\n" +
                "		if(iT<1) iT=1;\r\n" +
                "	 \r\n" +
                "		var kk=200;\r\n" +
                "		var to=0;\r\n" +
                "		var iO=0;\r\n" +
                "		\r\n" +
                "		while (to < tH){\r\n" +
                "			to=kk*iO;\r\n" +
                "			if (to < tH){\r\n" +
                "			   str=str+\"<P class='watermark' style='top:\"+\r\n" +
                "				   (kk*iO)+\"px;left:\"+(10)+\"px\\'>\"+msg+\"</P>\";\r\n" +
                "			   iO++;\r\n" +
                "			}\r\n" +
                "		}\r\n" +
                "		lastone.innerHTML=str;\r\n" +
                "	}\r\n" +
                "	window.onload = function(){watermark('" + watermark + "');};\r\n" +
                "</script>";
        String div = "<div id=\"wmitest1\"></div>";
        sb.append(html.split("<BODY>", 2)[0]).append("<BODY>").append(style).append(script).append(html.split("<BODY>", 2)[1]);
        html = sb.toString();
        sb = new StringBuilder();
        sb.append(html.split("</BODY>", 2)[0]).append(div).append("</BODY>").append(html.split("</BODY>", 2)[1]);
        return sb.toString();
    }
}
