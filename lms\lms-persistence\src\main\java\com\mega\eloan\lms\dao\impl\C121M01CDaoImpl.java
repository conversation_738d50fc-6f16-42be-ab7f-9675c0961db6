package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C121M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01C;

/** 澳洲消金評等表 **/
@Repository
public class C121M01CDaoImpl extends LMSJpaDao<C121M01C, String>
	implements C121M01CDao {

	@Override
	public C121M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C121M01C> findByMainId(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C121M01C> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C121M01C> findByC121M01A(C121M01A meta) {
		return findByMainId(meta.getMainId());
	}
	
	@Override
	public C121M01C findByUk(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C121M01C findByC120M01A(C120M01A c120m01a){
		return findByUk(c120m01a.getMainId(), c120m01a.getCustId(), c120m01a.getDupNo());
	}
}