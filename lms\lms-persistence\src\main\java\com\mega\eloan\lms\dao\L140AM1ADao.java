/* 
 * L140AM1ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140AM1A;

/** 央行註記異動作業授權檔 **/
public interface L140AM1ADao extends IGenericDao<L140AM1A> {

	L140AM1A findByOid(String oid);
	
	List<L140AM1A> findByMainId(String mainId);
	
	L140AM1A findByUniqueKey(String mainId, String ownUnit, String authType, String authUnit);

	List<L140AM1A> findByIndex01(String mainId, String ownUnit, String authType, String authUnit);
}