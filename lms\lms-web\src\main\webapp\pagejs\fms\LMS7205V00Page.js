var count = 1;
//var dfd = new $.Deferred();
//dfd.resolve();
$(document).ready(
		function() {
		var grid = $("#gridview").iGrid({
			handler : 'lms7205gridhandler',
			height : 350,
			sortname : 'patternNM',
			postData : {
				docStatus : viewstatus,
				formAction: "queryL720m01a",
				rowNum:15
			},
			rowNum:15,
			multiselect : true,
			colModel : [  {
				colHeader : i18n.lms7205v00['L720M01A.patternNM'],//"範本名稱",
				name : 'patternNM',
				align : "left",
				width : 200,
				sortable : true,
				formatter : 'click',
				onclick : openDoc
			}, {
				colHeader : i18n.lms7205v00['L720M01A.updater'],//"最後異動人員",
				name : 'updater',
				width : 200,
				sortable : true
			}, {
				colHeader : "oid",
				name : 'oid',
				hidden : true
			} ],
	        ondblClickRow: function(rowid){		//當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	            var data = grid.getRowData(rowid);
	            openDoc(null,null,data);
	        }
		});
		$("#buttonPanel").find("#btnDelete").click(function() {		//當使用者點選刪除按鈕時處理刪除工作
			var rows = $("#gridview").getGridParam('selarrrow');
			var list = "";
			var sign = ",";
			for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
				if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
					var data = $("#gridview").getRowData(rows[i]);
					list += ((list == "") ? "" : sign ) + data.oid;
				}
			}
			if (list == "") {
				return CommonAPI.showMessage(i18n.lms7205v00['l720v00.alert1']);
				}
			$.ajax({
				type : "POST",
				handler : "lms7205formhandler",
				data : 
				{
					formAction : "deleteL720m01a",
					listOid : list,
					sign : sign
				},
				success:function(json){
					CommonAPI.triggerOpener("gridview","reloadGrid");
					$("#gridview").trigger("reloadGrid");//更新Grid內容						
				}
			});			
		}).end().find("#btnAdd").click(function() {		//當使用者點選新增按鈕時檢查沒有和Grid重複的BranchId及Subject		
			thickBoxOpen("");
		}).end().find("#btnModify").click(function(){		//當使用者點選修改按鈕時讀取已有資料進行修改
			var rows = $("#gridview").getGridParam('selarrrow');
			var list = "";
			var sign = ",";
			for (var i=0;i<rows.length;i++){	//將所有已選擇的資料存進變數list裡面
				if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
					var data = $("#gridview").getRowData(rows[i]);
					list += ((list == "") ? "" : sign ) + data.oid;
				}
			}
			if (list == "") {
				return CommonAPI.showMessage(i18n.lms7205v00['l720v00.alert1']);
			}
			if(rows.length!=1)
			{
				return CommonAPI.showMessage(i18n.lms7205v00['l720v00.alert2']);			
			}
			else
			{
				openDoc(null,null,data);
			}
		});
});

function openDoc(cellvalue, options, rowObject) {	//讀取已有的資料以進行修改
	ilog.debug(rowObject);
	$.ajax({
		type : "POST",
		handler : "lms7205formhandler",
		data : 
		{
			formAction : "queryL720m01a",
			oid : rowObject.oid
		},
		success:function(responseData){
			//alert(JSON.stringify(responseData));
			$("#L720M01AForm").reset();
			//$("#L720M01AForm").find("#pattern").val(""); //罪魁禍首
			$("#L720M01AForm").setData(responseData.L720M01AForm,false);
			$("#L720M01AForm").find("#patternNM").attr("disabled",true);
//			fixIECKEditorCache4ModelPopup("L720M01AForm","pattern",false);
			thickBoxOpen(responseData.L720M01AForm.oid);
			setTimeout(function(){
				setCkeditor2("pattern",responseData.L720M01AForm.pattern);
			},100);			
		}
	});
};

// 注意：如果CKeditor在Thickbox裡一定要先打開ThickBox再執行此方法
/**
 * 設值ckeditor(更改內容)
 * name : ckeditor name
 * val : 需要設定的值
 */
function setCkeditor2(name, val, width, height){
    var oEditor = CKEDITOR.instances[name];
    if (oEditor) {
		if(width != undefined && height != undefined){
			oEditor.resize(width, height);
		}
        oEditor.setData(val);
    }
}

function thickBoxOpen(oid){			//負責處理打開ThickBox功能
	if(oid == ""){
		$("#L720M01AForm").reset();
		$("#L720M01AForm").find("#pattern").val("");
//		fixIECKEditorCache4ModelPopup("L720M01AForm","pattern",false);
//		$.ajax({
//			type : "POST",
//			handler : "lms7205formhandler",
//			data : 
//			{
//				formAction : "getUpdater"
//			},
//			success:function(json){
//				$("#L720M01AForm").find("#cupdater").val(json.cupdater);
//				$("#L720M01AForm").find("#updater").val(json.updater);
//			}
//		});
	}
	var openThickbox = $("#theword").thickbox({	// 使用選取的內容進行彈窗
		title : i18n.lms7205v00['l720v00.index01'],
		width : 1000,
		height : 480,
		modal: false,
		i18n : i18n.def,
		buttons : {
				"saveData" : function(showMsg){
					$.ajax({
						type : "POST",
						handler : "lms7205formhandler",
						data : 
						{
							formAction : "checkL720m01a",
							patternNM : $("#patternNM").val(),
							exist : false
						},
						success:function(json){
							//alert(JSON.stringify(json));
							if(json.exist == true && oid == ""){
								return CommonAPI.showMessage(i18n.lms7205v00['l720v00.alert3']);
							}else{
								if($("#L720M01AForm").valid()){
									$.ajax({
										type : "POST",
										handler : "lms7205formhandler",
										data : 
										{
											formAction : "saveL720m01a",
											oid : oid,
											pattern : $("#pattern").val()
										},
										success:function(json){
											CommonAPI.triggerOpener("gridview","reloadGrid");
											$("#gridview").trigger("reloadGrid");//更新Grid內容	
											$("#L720M01AForm").reset();
										}
									});
									$.thickbox.close();
								}
							}				
						}
					});
			},
				"close" : function(){
            	 API.confirmMessage(i18n.def['flow.exit'], function(res){
 					if(res){
						$("#L720M01AForm").reset();	
						$("#L720M01AForm").find("#patternNM").attr("disabled",false);
						$("#L720M01AForm").find("#cupdater").val("");
						$("#L720M01AForm").find("#updater").val("");							
 						$.thickbox.close();
 					}
 		        });
			}
		}
	});
}