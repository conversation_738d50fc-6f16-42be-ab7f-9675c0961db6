package com.mega.eloan.lms.lms.panels;

import com.mega.eloan.common.panels.Panel;

public class LMSS02A_Panel extends Panel {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS02A_Panel(String id) {
		super(id);
		// add(new LMSS02APanel01("lmss02apanel01"));
		// add(new LMSS02APanel02("lmss02apanel02"));
		// add(new LMSS02APanel03("lmss02apanel03"));
		// add(new LMSS02APanel04("lmss02apanel04"));
		// add(new LMSS02APanel05("lmss02apanel05"));
	}
}
