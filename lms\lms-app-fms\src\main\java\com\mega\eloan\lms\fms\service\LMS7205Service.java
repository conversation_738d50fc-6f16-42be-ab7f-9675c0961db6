/* 
 * LMS1205Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L720M01A;

import tw.com.iisi.cap.dao.utils.ISearch;

/**
 * 使用者自定表格範本 Service
 * 
 * <AUTHOR>
 * 
 */
public interface LMS7205Service extends AbstractService {

	// 使用者自定表格範本
	/**
	 * 透過Oid取得資料
	 * 
	 * @param oid
	 * @return
	 */
	L720M01A findL720m01aByOid(String oid);

	/**
	 * 刪除多筆資料
	 * 
	 * @param oidArray
	 */
	void deleteListL720m01a(String[] oidArray);

	/**
	 * 透過獨特Key取得資料
	 * 
	 * @param patternNm
	 * @return
	 */
	L720M01A findL720m01aByUniqueKey(String patternNm);

	/**
	 * 取得所有資料
	 * 
	 * @param search
	 * @return
	 */
	List<L720M01A> findL720m01aList(ISearch search);
	List<L720M01A> findL720m01aList();
}
