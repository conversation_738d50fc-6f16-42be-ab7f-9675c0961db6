/* 
 * LMS9515Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L810M01A;

public interface LMS9541V02Service extends AbstractService {
	final String[] CITYLIST = { "台北市", "基隆市", "新北市", "宜蘭縣", "桃園縣", "新竹市",
			"新竹縣", "苗栗縣", "台中市", "彰化縣", "南投縣", "雲林縣", "嘉義市", "嘉義縣", "台南市",
			"高雄市", "屏東縣", "台東縣", "花蓮縣", "澎湖縣", "金門縣", "連江縣" };

	final String[] CITYCOLS = { "area", "houses", "sumup", "favor", "normal",
			"acHouses", "acSumup", "acFavor", "acNormal", "newHouse",
			"oldHouse", "overage" };

	final String[] BRNOCOLS = { "brno", "houses", "sumup", "favor", "normal",
			"favorRate", "acHouses", "acSumup", "acFavor", "acNormal",
			"acFavorRate" };

	final String[] RPTNAMES = { "", "八仟億優惠房貸額度控制表", "三千億優惠房貸額度控制表",
			"九十七年度二千億優惠房貸額度控制表", "青年安心成家優惠貸款-購置住宅", "全部優惠房貸" };

	final int scale = 5;// 小數點後第N位

	public boolean isRepeat(L810M01A data);

	// use MIS-RDB
	public List<Map<String, Object>> getCityData(String kindNo);

	public List<Map<String, Object>> getBrnoData(String kindNo,String endDate);

	public String findElghtappByKindno(String kindNo);
	
	public Map<String, Object> findTot(String kindNo,String type);
}
