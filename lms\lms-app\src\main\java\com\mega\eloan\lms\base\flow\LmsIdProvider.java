package com.mega.eloan.lms.base.flow;

import java.util.UUID;

import org.springframework.stereotype.Component;

import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.provider.IdProvider;

@Component
public class LmsIdProvider implements IdProvider {

	@Override
	public Object getNextId() {
		return UUID.randomUUID().toString().replace("-", "");
	}

	@Override
	public Object getNextId(Object[] parameter) {
		return UUID.randomUUID().toString().replace("-", "");
	}

	@Override
	public Object getNextId(FlowInstance parent) {
		return UUID.randomUUID().toString().replace("-", "");
	}

}
