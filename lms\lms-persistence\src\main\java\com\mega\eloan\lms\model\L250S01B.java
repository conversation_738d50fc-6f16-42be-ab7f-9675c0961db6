package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import tw.com.iisi.cap.model.GenericBean;

/** 消金模擬動審檢核表設定檔(產出至lms.l250s02b)此處是「共用的設定檔」無 mainId    **/
@Entity
@Table(name = "L250S01B")
public class L250S01B extends GenericBean {
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;
	private Integer type;
	private Integer group;
	private Integer groupOrder;
	private Integer subItem;
	private Integer subOrder;
	private String subTitle;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	/**
	 * 種類(1:青年安程成家，歡喜房貸。2:行家理財，不動產，定存，股票擔保)
	 * 
	 * @return
	 */
	public Integer getType() {
		return type;
	}

	/**
	 * 種類(1:青年安程成家，歡喜房貸。2:行家理財，不動產，定存，股票擔保)
	 * 
	 * @param type
	 */
	public void setType(Integer type) {
		this.type = type;
	}

	/**
	 * 大類
	 * 
	 * @return
	 */
	public Integer getGroup() {
		return group;
	}

	/**
	 * 大類
	 * 
	 * @param group
	 */
	public void setGroup(Integer group) {
		this.group = group;
	}

	/**
	 * 大類順序
	 * 
	 * @return
	 */
	public Integer getGroupOrder() {
		return groupOrder;
	}

	/**
	 * 大類順序
	 * 
	 * @param groupOrder
	 */
	public void setGroupOrder(Integer groupOrder) {
		this.groupOrder = groupOrder;
	}

	/**
	 * 小類項目
	 * 
	 * @return
	 */
	public Integer getSubItem() {
		return subItem;
	}

	/**
	 * 小類項目
	 * 
	 * @param subItem
	 */
	public void setSubItem(Integer subItem) {
		this.subItem = subItem;
	}

	/**
	 * 小類順序
	 * 
	 * @return
	 */
	public Integer getSubOrder() {
		return subOrder;
	}

	/**
	 * 小類順序
	 * 
	 * @param subOrder
	 */
	public void setSubOrder(Integer subOrder) {
		this.subOrder = subOrder;
	}

	/**
	 * 小類中文
	 * @return
	 */
	public String getSubTitle() {
		return subTitle;
	}

	/**
	 * 小類中文
	 * 
	 * @param subTitle
	 */
	public void setSubTitle(String subTitle) {
		this.subTitle = subTitle;
	}

}
