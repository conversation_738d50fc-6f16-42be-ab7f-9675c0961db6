$(function(){
	gridborrow();
});

function uGridborrow(){
    $("#gridborrow").jqGrid("setGridParam", {
        postData: {
            formAction: "queryL120s01aToGetData",
            rowNum: 10
        },
        search: true
    }).trigger("reloadGrid");
}

function gridborrow(){
	$("#gridborrow").iGrid({
		handler: 'lms1201gridhandler',
		//width: "100px",
		height: 350,
		sortname : 'custId',
		postData : {
			formAction : "queryL120s01aToGetData",
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		colModel: [{
			colHeader: i18n.lms1201s05["l120s05.grid21"],//"身分證統編"
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'custId' //col.id
		},{
			colHeader : i18n.lms1201s05["l120s05.grid22"],//"借款人姓名"
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		},{
			colHeader: i18n.lms1201s05["l120s05.grid23"],//"最後異動日期"
			align : "center",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'updateTime' //col.id
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}],
		ondblClickRow: function(rowid){
		}
	});
}