package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** FTP_IMWM0017檔  **/
@Entity
@Table(name="C900S03A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900S03A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;
	
	@Column(name="FN", length=10, columnDefinition="CHAR(10)")
	private String fn;

	@Column(name="GENDATE", length=8, columnDefinition="CHAR(8)")
	private String genDate;
	
	@Column(name="GENTIME", length=6, columnDefinition="CHAR(6)")
	private String genTime;
	
	@Digits(integer=9, fraction=0)
	@Column(name="IDX", columnDefinition="DEC(9,0)")
	private BigDecimal idx;
	
	@Column(name="BRNO", length=3, columnDefinition="CHAR(3)")
	private String brNo;
	
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)")
	private String custId;
	
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** ATM跨行提款免收次數 **/
	@Column(name="WITHDRAW_CNT", length=2, columnDefinition="CHAR(2)")
	private String withdraw_cnt;
	
	/** 自動化設備跨行轉帳免收次數 **/ 
	@Column(name="TRANSFER_CNT", length=2, columnDefinition="CHAR(2)")
	private String transfer_cnt;
	
	public String getOid() {
		return this.oid;
	}
	
	public void setOid(String value) {
		this.oid = value;
	}

	public String getFn() {
		return fn;
	}

	public void setFn(String fn) {
		this.fn = fn;
	}

	public String getGenDate() {
		return genDate;
	}

	public void setGenDate(String genDate) {
		this.genDate = genDate;
	}

	public String getGenTime() {
		return genTime;
	}

	public void setGenTime(String genTime) {
		this.genTime = genTime;
	}

	public BigDecimal getIdx() {
		return idx;
	}

	public void setIdx(BigDecimal idx) {
		this.idx = idx;
	}

	public String getBrNo() {
		return brNo;
	}

	public void setBrNo(String brNo) {
		this.brNo = brNo;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public String getWithdraw_cnt() {
		return withdraw_cnt;
	}

	public void setWithdraw_cnt(String withdraw_cnt) {
		this.withdraw_cnt = withdraw_cnt;
	}

	public String getTransfer_cnt() {
		return transfer_cnt;
	}

	public void setTransfer_cnt(String transfer_cnt) {
		this.transfer_cnt = transfer_cnt;
	}	
}
