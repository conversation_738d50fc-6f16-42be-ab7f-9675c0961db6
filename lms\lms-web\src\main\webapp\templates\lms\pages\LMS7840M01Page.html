<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
	<th:block th:fragment="innerPageBody">
		<script type="text/javascript">
			loadScript('pagejs/lms/LMS7840M01Page');
		</script>
            <div class="button-menu funcContainer" id="buttonPanel">
                <button id="btnSearch">
					<span class="ui-icon ui-icon-jcs-102" ></span>
                    <th:block th:text="#{button.search}">
                        <!--開始查詢-->
                    </th:block>
                </button>
				<button id="btnProduceExcel">
					<span class="ui-icon ui-icon-jcs-110" ></span>
                    <th:block th:text="#{button.ProduceExcel}">
                        <!--產生Excel-->
                    </th:block>
                </button>
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{button.exit}">
                        <!--離開-->
                    </th:block>
                </button>
            </div>
           <div class="tit2 color-black">
				<th:block th:text="#{L784M01A.title}"><!-- 簽報記錄查詢--></th:block>：
				<!--(<span id="typCd" class="text-red"/>)<span id="custId" /> <span id="dupNo" /> <span id="custName" />-->
			</div>
	

	        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	          <tbody>
	              <tr>
	                  <td width="20%" class="hd1"><th:block th:text="#{L784M01A.queryDate}"><!-- 查詢日期 --></th:block>&nbsp;&nbsp;</td>
					  <td width="30%"><span id="queryDate"  ></span></td>
	                  <td width="20%" class="hd1"><th:block th:text="#{L784M01A.queryResult}"><!-- 查詢結果 --></th:block>&nbsp;&nbsp;</td>
	                  <td width="30%">
	                  	<select name="queryResult" id="queryResult" disabled="true">
	                  		<option value="" >&nbsp;&nbsp;</option>
                            <option value="S" ><th:block th:text="#{L784M01A.Result_S}">排程中</th:block></option>
                            <option value="E" ><th:block th:text="#{L784M01A.Result_E}">執行中</th:block></option>
                            <option value="Y" ><th:block th:text="#{L784M01A.Result_Y}">執行成功</th:block></option>
                            <option value="N" ><th:block th:text="#{L784M01A.Result_N}">執行失敗</th:block></option>
                        </select>
						
						<span id="showExeCount" style="display:none">
						    &nbsp;&nbsp;&nbsp;&nbsp;<th:block th:text="#{L784M01A.exeCount}"><!-- 結果筆數 --></th:block>&nbsp;&nbsp;：<span id="exeCount"  />
						</span>
					  </td>
	              </tr>
				  <tr>
	                  <td width="20%" class="hd1"><th:block th:text="#{L784M01A.queryConditon}"><!-- 查詢條件 --></th:block>&nbsp;&nbsp;</td>
					  <td width="30%"><span id="queryConditon"  ></span></td>
					  <td width="20%" class="hd1"><th:block th:text="#{L784M01A.queryErrMsg}"><!-- 錯誤訊息 --></th:block>&nbsp;&nbsp;</td>
	                  <td width="30%">
	                  	<button type="button" id="openExecMsgBt">
	                        <span class="text-only"><th:block th:text="#{button.open}">調閱</th:block></span>
	                    </button>
	                  	<!--<span id="queryErrMsg" style="display:none" />-->

					  </td>
	              </tr>
	          </tbody>
	        </table>
          <fieldset>
                    <legend><th:block th:text="#{L784M01A.title}"><!--簽報案件查詢--></th:block></legend>
					<div id="gridview" ></div>
					<br>
			 	 <span id="logMainId"  ></span>
          </fieldset>
		  
		   
		  
		  <div id="filterBox7840" style="display:none">
                <form id="filterForm">
                	<div class="tabs" id="s91tab">
                	   <ul>
                       <li><a id = "a91t1" href="#s91t1"><b>基本查詢<!--<th:block th:text="ces1401.9103">一、授信往來情形</th:block>--></b></a></li>
					   <li><a id = "a91t2" href="#s91t2"><b>進階查詢<!--<th:block th:text="ces1401.9163">二、集團企業信用狀況</th:block>--></b></a></li>
                       </ul>
					   
					   <div class="tabCtx-warp">
						    <div id="s91t1">
				                 <fieldset id="s91t1f1">
					                 <legend>基本查詢<!--<th:block th:text="ces1405.9106">集團企業授信往來情形</th:block>--></legend>
																
					                    <table class="tb2" width="100%">
					                        <tbody>
					                        	<tr>
					                        		<td class="hd1" style="width:32%">
					                                    <th:block th:text="#{L784M01A.typCd}">區部別</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                	<select id="typCd" name="typCd" space="true" combokey="TypCd"></select>
					                                </td>
					                            </tr>
												<tr>
					                                <td class="hd1">
					                                    <th:block th:text="#{L784M01A.docType}">企/個金</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                    <select id="docType" name="docType" space="true" combokey="L120M01A_docType"></select>
					                                </td>
					                            </tr>
												<tr>
					                                <td class="hd1">
					                                    <th:block th:text="#{L784M01A.docKind}">授權別</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                	<select id="docKind" name="docKind" space="true" combokey="L120M01A_docKind"></select>                                   
					                                </td>
					                            </tr>
												<tr>
					                                <td class="hd1">
					                                    <th:block th:text="#{L784M01A.docCode}">案件別</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                	<select id="docCode" name="docCode" space="true" combokey="L120M01A_docCode"></select>
					                                </td>
					                            </tr>
												<tr>
													<td class="hd1">
					                                    	<th:block th:text="#{L784M01A.caseBrId}">分行別</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                	<select id="caseBrId" name="caseBrId"></select>
					                                </td>
												</tr>	
												
												<tr>
					                                <td class="hd1">
					                                    <th:block th:text="#{L784M01A.updater}">分行經辦</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                    <select id="updater" name="updater" space="true" comboaction='userByBranch'></select>
					                                </td>
					                            </tr>
					                            <tr>
					                                <td class="hd1">
					                                    <th:block th:text="#{L784M01A.caseDate}">簽案日期</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                    <input id="fromDate" name="fromDate"  class="date"  type="text"   size="8" maxlength="10" />
					                                    ~
					                                    <input id="endDate" name="endDate"  class="date"  type="text"   size="8" maxlength="10" />
					                                    <span class="text-red">ex:YYYY-MM-DD</span>
					                                </td>
					                            </tr>
					                            <tr>
					                                <td class="hd1">
					                                    <th:block th:text="#{L784M01A.approveTime}">核准日期</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                    <input id="approveDateS" name="approveDateS"  class="date"  type="text"   size="8" maxlength="10" />
					                                    ~
					                                    <input id="approveDateE" name="approveDateE"  class="date"  type="text"   size="8" maxlength="10" />
					                                    <span class="text-red">ex:YYYY-MM-DD</span>
					                                </td>
					                            </tr>
												
												<tr>
					                                <td class="hd1">
					                                    <th:block th:text="#{L784M01A.cntrNo}">額度序號</th:block>：&nbsp;&nbsp;
					                                </td>
					                                <td>
					                                    <input id="cntrNo" name="cntrNo" type="text" size="12" maxlength="12"  class="trim alphanum"/>
					                                </td>
					                            </tr>
												
					                        </tbody>
					                    </table>
								</fieldset><!--s91t1f1 基本查詢-->
						   </div>
						   <div id="s91t2" >
						   	<!--
						   	 <label><input type="checkbox" id="fxFlag" name="fxFlag" value="Y"/><th:block th:text="L784M01A.useAdvanceSearch">啟用進階查詢</th:block></label><br />
							 -->
							 <span class="text-red">＊</span><th:block th:text="#{L784M01A.fxIsCls}">是否為查詢國內個金案件</th:block>？
							 <label><input type="radio" id="fxIsCls" name="fxIsCls" value="Y" /><th:block th:text="#{yes}">是</th:block></label>
							 <label><input type="radio" name="fxIsCls" value="N" checked/><th:block th:text="#{no}">否</th:block></label><br />
							 
			                 <fieldset id="s91t1f2X">
			                 <legend>進階查詢<!--<th:block th:text="ces1405.9106">集團企業授信往來情形</th:block>--></legend>
								<!-- 啟用進階查詢-->						
				                    <table class="tb2" width="100%">
				                        <tbody>
				                        	<tr>
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxCurr}">額度幣別</th:block>：&nbsp;&nbsp;
													<BR>
												    <button type="button" id="selectFxCurrBt">
								                       <span class="text-only"><th:block th:text="#{button.select}">選擇</th:block></span>
								                    </button>
				                                </td>
				                                <td style="width:68%">
				                                    <!--<input id="fxCurr" name="fxCurr" type="text" size="20" maxlength="120" maxlengthC="40" />   hidden-->
													<textarea id="fxCurrShow" name="fxCurrShow"  class="caseReadOnly" cols="60" rows="2" readonly></textarea>
													<input type="hidden" id="fxCurr" name="fxCurr" />
				                                </td>
				                            </tr>	
				                        	<tr class="showForLms" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxLnSubject}">授信科子目</th:block>：&nbsp;&nbsp;
													<BR>
													<button type="button" id="selectFxLnSubjectBt">
								                        <span class="text-only"><th:block th:text="#{button.select}">選擇</th:block></span>
								                    </button>
				                                </td>
				                                <td style="width:68%">
				                                    <!--<input id="fxLnSubject" name="fxLnSubject" type="text" size="20" maxlength="120" maxlengthC="40" />-->
													<textarea id="fxLnSubjectShow" name="fxLnSubjectShow"  class="caseReadOnly" cols="60" rows="2" readonly></textarea>
													<input type="hidden" id="fxLnSubject" name="fxLnSubject" />
													
				                                </td>
				                            </tr>	
											
											<tr class="showForLms" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxLmtDays}">清償期限</th:block>：&nbsp;&nbsp;
				                                </td>
				                                <td style="width:68%">
				                                    <input id="fxLmtDays1" name="fxLmtDays1" type="text" size="10" maxlength="3" class="numeric" integer="3"   /><th:block th:text="#{L784M01A.day}">天</th:block>～
													<input id="fxLmtDays2" name="fxLmtDays2" type="text" size="10" maxlength="3" class="numeric" integer="3"   /><th:block th:text="#{L784M01A.day}">天</th:block>
				                                </td>
				                            </tr>

											<tr class="showForLms" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxRateText1}">利率(結構化)</th:block>：&nbsp;&nbsp;
													<BR>
													<button type="button" id="selectFxRateText1Bt">
								                        <span class="text-only"><th:block th:text="#{button.select}">選擇</th:block></span>
								                    </button>
				                                </td>
				                                <td style="width:68%">
				                                    <!--<input id="fxRateText1" name="fxRateText1" type="text" size="20" maxlength="120" maxlengthC="40" />-->
													<textarea id="fxRateText1Show" name="fxRateText1Show"  class="caseReadOnly" cols="60" rows="2" readonly></textarea>
													<input type="hidden" id="fxRateText1" name="fxRateText1" />
				                                </td>
				                            </tr>
											<!--J-107-0069-001 e-Loan授信系統「同類授信對象」之搜尋條件請增加「模型評等等級」，並請開放區域營運中心可代營業單位搜尋全行符合條件之同類授信對象。-->
											<tr class="showForLms" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxCrGrade}">授信戶模型評等等級</th:block>：&nbsp;&nbsp;
				                                </td>
				                                <td style="width:68%">
				                                    <select name="fxCrGrade" id="fxCrGrade">
						                                <option value="" >--請選擇--</option>
						                                <option value="1" >1</option>
						                                <option value="2" >2</option>
						                                <option value="3" >3</option>
														<option value="4" >4</option>
														<option value="5" >5</option>
														<option value="6" >6</option>
														<option value="7" >7</option>
														<option value="8" >8</option>
														<option value="9" >9</option>
														<option value="10" >10</option>
														<option value="11" >11</option>
														<option value="12" >12</option>
														<option value="13" >13</option>
														<option value="A" >A</option>
														<option value="B" >B</option>
														<option value="C" >C</option>
														<option value="D" >D</option>
														<option value="E" >E</option>
						                            </select>
													&nbsp;&nbsp;<th:block th:text="#{L784M01A.level}">級</th:block>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
													<div id="showCrKind">
														<th:block th:text="#{L784M01A.fxCrKind}">企業類別</th:block>：&nbsp;&nbsp;
														<select name="fxCrKind" id="fxCrKind">
															<option value="" >--請選擇--</option>
							                                <option value="1" ><th:block th:text="#{L784M01A.fxCrKind_1}">大型企業</th:block></option>
							                                <option value="2" ><th:block th:text="#{L784M01A.fxCrKind_2}">其他企業</th:block></option>
							                            </select>
													</div>
				                                </td>
				                            </tr>
											<!--個金專區 BGN************************************************************************************************-->
											<tr class="showForCls" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxProdKind}">產品種類</th:block>：&nbsp;&nbsp;
													<BR>
													<button type="button" id="selectFxProdKindBt">
								                        <span class="text-only"><th:block th:text="#{button.select}">選擇</th:block></span>
								                    </button>
				                                </td>
				                                <td style="width:68%">
				                                    <!--<input id="fxLnSubject" name="fxLnSubject" type="text" size="20" maxlength="120" maxlengthC="40" />-->
													<textarea id="fxProdKindShow" name="fxProdKindShow"  class="caseReadOnly" cols="60" rows="2" readonly></textarea>
													<input type="hidden" id="fxProdKind" name="fxProdKind" />
													
				                                </td>
				                            </tr>	
											
											<tr class="showForCls" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxLnSubjectCls}">科目</th:block>：&nbsp;&nbsp;
													<BR>
													<button type="button" id="selectFxLnSubjectClsBt">
								                        <span class="text-only"><th:block th:text="#{button.select}">選擇</th:block></span>
								                    </button>
				                                </td>
				                                <td style="width:68%">
				                                    <!--<input id="fxLnSubject" name="fxLnSubject" type="text" size="20" maxlength="120" maxlengthC="40" />-->
													<textarea id="fxLnSubjectClsShow" name="fxLnSubjectClsShow"  class="caseReadOnly" cols="60" rows="2" readonly></textarea>
													<input type="hidden" id="fxLnSubjectCls" name="fxLnSubjectCls" />
													
				                                </td>
				                            </tr>	
											<tr class="showForCls" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxLnMonth}">清償期間</th:block>：&nbsp;&nbsp;
				                                </td>
				                                <td style="width:68%">
				                                    <input id="fxLnMonth1" name="fxLnMonth1" type="text" size="10" maxlength="3" class="numeric" integer="3"   /><th:block th:text="#{L784M01A.month}">月</th:block>～
													<input id="fxLnMonth2" name="fxLnMonth2" type="text" size="10" maxlength="3" class="numeric" integer="3"   /><th:block th:text="#{L784M01A.month}">月</th:block>
				                                </td>
				                            </tr>
											
											<tr class="showForCls" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxRateTextCls}">利率</th:block>：&nbsp;&nbsp;
													<BR>
													<button type="button" id="selectFxRateTextClsBt">
								                        <span class="text-only"><th:block th:text="#{button.select}">選擇</th:block></span>
								                    </button>
				                                </td>
				                                <td style="width:68%">
				                                    <!--<input id="fxRateText1" name="fxRateText1" type="text" size="20" maxlength="120" maxlengthC="40" />-->
													<textarea id="fxRateTextClsShow" name="fxRateTextClsShow"  class="caseReadOnly" cols="60" rows="2" readonly></textarea>
													<input type="hidden" id="fxRateTextCls" name="fxRateTextCls" />
				                                </td>
				                            </tr>
											
											<!--個金專區 END************************************************************************************************ -->
											
											<tr>
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxGuarantor}">連保人</th:block>：&nbsp;&nbsp;
				                                </td>
				                                <td style="width:68%">
				                                    <!--<input id="fxGuarantor" name="fxGuarantor" type="text" size="20" maxlength="120" maxlengthC="40" />-->
													
													<label>
						                                <input id="fxGuarantor" name="fxGuarantor" type="radio" value="N"   />
						                                <th:block th:text="#{nohave}"><!-- 無--></th:block>
						                            </label>
						                            <label>
						                                <input name="fxGuarantor" type="radio" value="Y"   />
						                                <th:block th:text="#{have}"><!-- 有--></th:block>
						                            </label>
							
				                                </td>
				                            </tr>	
											
											<tr>
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxCollateral1}">擔保品(擔保品管理系統)</th:block>：&nbsp;&nbsp;
													<BR>
													<button type="button" id="selectFxCollateral1Bt">
								                        <span class="text-only"><th:block th:text="#{button.select}">選擇</th:block></span>
								                    </button>
				                                </td>
				                                <td style="width:68%">
				                                    <!--<input id="fxCollateral1" name="fxCollateral1" type="text" size="20" maxlength="120" maxlengthC="40" />-->
													<textarea id="fxCollateral1Show" name="fxCollateral1Show"  class="caseReadOnly" cols="60" rows="2" readonly></textarea>
													<input type="hidden" id="fxCollateral1" name="fxCollateral1" />
				                                </td>
				                            </tr>	
											<!--J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件-->
											<tr class="showFxOnlyLand" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxOnlyLand}">不動產只有土地無建物</th:block>：&nbsp;&nbsp;
													<BR>
				                                </td>
				                                <td style="width:68%">
				                                	<label>
						                                <input name="fxOnlyLand" type="radio" value="Y"   />
						                                <th:block th:text="#{yes}">是</th:block>
						                            </label>
				                                    <label>
						                                <input id="fxOnlyLand" name="fxOnlyLand" type="radio" value="N"   />
						                                <th:block th:text="#{no}">否</th:block>
						                            </label>
				                                </td>
				                            </tr>	
											<!--J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件-->
											<tr class="showFxBldUse" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxBldUse}">建物主要用途</th:block>：&nbsp;&nbsp;
													<BR>
													<button type="button" id="selectFxBldUseBt">
								                        <span class="text-only"><th:block th:text="#{button.select}">選擇</th:block></span>
								                    </button>
				                                </td>
				                                <td style="width:68%">
				                                    <textarea id="fxBldUseShow" name="fxBldUseShow"  class="caseReadOnly" cols="60" rows="2" readonly></textarea>
													<input type="hidden" id="fxBldUse" name="fxBldUse" />
				                                </td>
				                            </tr>	
											
											
											<tr  class="showForLms" style="display:none">
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxBusCode}">行業對象別</th:block>：&nbsp;&nbsp;
				                                </td>
				                               <td style="width:68%">
				                                    <input id="fxBusCode" name="fxBusCode" type="text" size="8" maxlength="6"   />
													<!--<th:block th:text="#{L784M01A.fxBusCodeMemo}">(企金專用)</th:block>：&nbsp;&nbsp;-->
				                                </td>
				                            </tr>
											<tr>
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxRateText}">利率(關鍵字)</th:block>：&nbsp;&nbsp;
				                                </td>
				                                <td style="width:68%">
				                                    <input id="fxRateText" name="fxRateText" type="text" size="20" maxlength="120" maxlengthC="40" />
				                                </td>
				                            </tr>			
											<tr>
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxCollateral}">擔保品(關鍵字)</th:block>：&nbsp;&nbsp;
				                                </td>
				                                <td style="width:68%">
				                                    <input id="fxCollateral" name="fxCollateral" type="text" size="20" maxlength="120" maxlengthC="40" />
				                                </td>
				                            </tr>											
				                            <tr>
				                                <td class="hd1" style="width:32%">
				                                    <th:block th:text="#{L784M01A.fxOtherCondition}">其他敘做條件(關鍵字)</th:block>：&nbsp;&nbsp;
				                                </td>
				                                <td style="width:68%">
				                                    <input id="fxOtherCondition" name="fxOtherCondition" type="text" size="20" maxlength="120" maxlengthC="40" />
				                                </td>
				                            </tr>
				                        </tbody>
				                    </table>
								</fieldset><!--s91t1f2 進階查詢-->
							</div>
					   </div>		
					</div>	
					
					
					
                </form>
            </div>
		  
		   <div id="selectPeopleBox" style="display:none">
			   <select id="custIdSelect" ></select>
		  </div>
		  
		    <div id="loginFxCurr" style="display:none; margin-top:5px;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
			                <input type="checkbox" name="fxCurr_1" id="fxCurr_1" />
			            </td>
					</tr>
				</table>	
			</div>
			
			
			<div id="loginFxLnSubject" style="display:none; margin-top:5px;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
			                <input type="checkbox" name="fxLnSubject_1" id="fxLnSubject_1" />
			            </td>
					</tr>
				</table>	
			</div>
			
			<div id="loginFxRateText1" style="display:none; margin-top:5px;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
			                <input type="checkbox" name="fxRateText1_1" id="fxRateText1_1" />
			            </td>
					</tr>
				</table>	
			</div>
			
			
			<div id="loginFxCollateral1" style="display:none; margin-top:5px;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
			                <input type="checkbox" name="fxCollateral1_1" id="fxCollateral1_1" />
			            </td>
					</tr>
				</table>	
			</div>
			
			<!--J-112-0449_05097_B1001 Web e-Loan企金額度明細表新增主要用途查詢條件-->
			<div id="loginFxBldUse" style="display:none; margin-top:5px;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
			                <input type="checkbox" name="fxBldUse_1" id="fxBldUse_1" />
			            </td>
					</tr>
				</table>	
			</div>
			
			<div id="loginExecMsg" style="display:none; margin-top:5px;">
				<span id="execMsg"> </span>
			</div>
			
			
			<div id="loginFxProdKind" style="display:none; margin-top:5px;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
			                <input type="checkbox" name="fxProdKind_1" id="fxProdKind_1" />
			            </td>
					</tr>
				</table>	
			</div>
			
			<div id="loginFxLnSubjectCls" style="display:none; margin-top:5px;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
			                <input type="checkbox" name="fxLnSubjectCls_1" id="fxLnSubjectCls_1" />
			            </td>
					</tr>
				</table>	
			</div>
			
			<div id="loginFxRateTextCls" style="display:none; margin-top:5px;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td>
			                <input type="checkbox" name="fxRateTextCls_1" id="fxRateTextCls_1" />
			            </td>
					</tr>
				</table>	
			</div>
			
		</th:block>
    </body>
</html>
