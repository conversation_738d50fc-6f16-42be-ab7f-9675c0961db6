package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF386Service;

/**
 * <pre>
 * 消金核准明細檔  ELF386
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
@Service
public class ObsdbELF386ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF386Service {

	@Override
	public void insertElf386(String brno, String custId, String dupNo,
			String cntrNo, String staffNo, String cusBan, String cusNm,
			String sDate, String isMates, String degreeCd, String pos,
			BigDecimal seniority, long hIncome, int dRate, long oDep,
			String oBusiness, String cmsStatus, String credit, int yRate,
			String isPfund, long invMbal, long invObal, long loanPct,
			String isBins, long yPay, long oMoney, String updater,
			BigDecimal timeNow) {
		this.getJdbc(brno).update(
				"ELF386.insert",
				new Object[] { custId, dupNo, cntrNo, staffNo, cusBan, cusNm,
						sDate, isMates, degreeCd, pos, seniority, hIncome,
						dRate, oDep, oBusiness, cmsStatus, credit, yRate,
						isPfund, invMbal, invObal, loanPct, isBins, yPay,
						oMoney, updater, timeNow});
	}

	@Override
	public void delElf386(String brno, String custId, String dupNo, String cntrNo) {
		this.getJdbc(brno).update("ELF386.delete",
				new Object[] { custId, dupNo, cntrNo });
		
	}
}
