package com.mega.eloan.lms.mfaloan.service.impl;



import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


import com.mega.eloan.lms.mfaloan.service.MisDRCXBNMService;


@Service
public class MisDRCXBNMServiceImpl extends AbstractMFAloanJdbc implements
MisDRCXBNMService {
	@SuppressWarnings("unused")
	private static final Logger logger = LoggerFactory
	.getLogger(MisDRCXBNMServiceImpl.class);
	
	@Override
	public String getDRCXBNMCheck(String brno) {
		List<Map<String, Object>>  datalist=getJdbc().queryForList("DRCXBNM.selCheck",
				new String[] { brno });
		if(datalist.size()>0){
			return (String)datalist.get(0).get("CHECK");
		}else{
			return "0";
		}
	}

	

	

	
}
