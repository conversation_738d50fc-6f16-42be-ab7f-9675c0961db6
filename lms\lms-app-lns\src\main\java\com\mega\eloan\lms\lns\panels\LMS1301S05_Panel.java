package com.mega.eloan.lms.lns.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

public class LMS1301S05_Panel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMS1301S05_Panel(String id) {
		super(id);
		//add(new LMSS07APanel02("lmss07panel02"));
		//add(new LMSS07APanel03("lmss07panel03"));
		//add(new LMSS07APanel04("lmss07panel04"));
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new LMSS05APanel("LMSS05APanel").processPanelData(model, params);
		new LMS1201S06Panel("lms1205s06panel").processPanelData(model, params);
		new LMS1301S05Panel01("lms1305s05panel01").processPanelData(model, params);
	}
}
