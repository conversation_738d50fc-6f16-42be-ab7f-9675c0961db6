# Common properties
jawr.gzip.ie6.on=false
jawr.charset.name=UTF-8

#------DEVELOPMENT USE--
#jawr.debug.on=true
#jawr.gzip.on=false
#jawr.js.use.cache=false
#jawr.css.use.cache=false
#----------------------

#------PRODUCTION USE--
jawr.debug.on=false
jawr.gzip.on=true
jawr.js.use.cache=true
jawr.css.use.cache=true
#----------------------

#--------------------#
#     JS Library     #
#--------------------#

# Javascript properties and mappings
jawr.js.bundle.basedir=/js

# All files within /js/lib will be together in a bundle. 
# The remaining scripts will be served sepparately. 
jawr.js.bundle.baseJs.id=/bundles/baseJs.jsx
jawr.js.bundle.baseJs.composite=true
#jawr.js.bundle.baseJs.child.names=jquery, jqueryui, json, validate, qtip, dotimeout, blockui, base64, jqgrid, jqscrollto,corner, jstree, uniform,ajaxfileupload
jawr.js.bundle.baseJs.child.names=jquery, jqueryui, jqueryuii18n, jquerywatch, json, validate, qtip, dotimeout, blockui, base64, jqgrid, jqscrollto,corner, uniform,ajaxfileupload,purify

#jQuery Library
jawr.js.bundle.jquery.id=/bundles/jquery.jsx
#jawr.js.bundle.jquery.mappings=jquery/production/jquery-1.5.2.min.js
jawr.js.bundle.jquery.mappings=jquery/development/jquery-1.5.2.js

#jQuery UI Library
jawr.js.bundle.jqueryui.id=/bundles/jqueryui.jsx
jawr.js.bundle.jqueryui.mappings=/jquery/ui/jquery-ui-1.8.4.custom.min.js

#jQuery UI i18n
jawr.js.bundle.jqueryuii18n.id=/bundles/jqueryuii18n.jsx
jawr.js.bundle.jqueryuii18n.mappings=/jquery/ui/jquery-ui-i18n.js

#jQuery watch Library
jawr.js.bundle.jquerywatch.id=/bundles/jquerywatch.jsx
jawr.js.bundle.jquerywatch.mappings=/jquery/plugin/jquery-watch.js

#jQuery bgiframe Library
jawr.js.bundle.jquerybgiframe.id=/bundles/jquerybgiframe.jsx
jawr.js.bundle.jquerybgiframe.mappings=/jquery/plugin/jquery.bgiframe.min.js

#json Library 
jawr.js.bundle.json.id=/bundles/json.jsx
jawr.js.bundle.json.mappings=/js/json/json2.js

#jqgrid Library
jawr.js.bundle.jqgrid.id=/bundles/jqgrid.jsx
#jawr.js.bundle.jqgrid.mappings=/jquery/plugin/jqgrid381/js/i18n/grid.locale-en.js,/jquery/plugin/jqgrid381/js/jquery.jqGrid.min.js
#jawr.js.bundle.jqgrid.mappings=/jquery/plugin/jqgrid412/js/i18n/grid.locale-en.js,/jquery/plugin/jqgrid412/js/jquery.jqGrid.src.js
jawr.js.bundle.jqgrid.mappings=/jquery/plugin/jqgrid440/js/i18n/grid.locale-en.js,/jquery/plugin/jqgrid440/js/jquery.jqGrid.src.js

#jstree Library
jawr.js.bundle.jstree.id=/bundles/jstree.jsx
jawr.js.bundle.jstree.mappings=/jquery/plugin/jsTreev102/jquery.jstree.js


#jquery.scrollTo Plugin
jawr.js.bundle.jqscrollto.id=/bundles/jqscrollto.jsx
jawr.js.bundle.jqscrollto.mappings=/jquery/plugin/scrollto/jquery.scrollto.js

#charts Library
#jawr.js.bundle.charts.id=/bundles/charts.js
#jawr.js.bundle.charts.mappings=/js/charts/charts.js

#jquery.corner Plugin
jawr.js.bundle.corner.id=/bundles/corner.jsx
jawr.js.bundle.corner.mappings=/jquery/plugin/jquery.corner.js

#validate Library
jawr.js.bundle.validate.id=/bundles/validate.jsx
jawr.js.bundle.validate.mappings=/jquery/plugin/validate/jquery.validate.1.7.js

#qtip Library
jawr.js.bundle.qtip.id=/bundles/qtip.jsx
jawr.js.bundle.qtip.mappings=/jquery/plugin/qtipv200/jquery.qtip.js


#doTimeout Library
jawr.js.bundle.dotimeout.id=/bundles/dotimeout.jsx
jawr.js.bundle.dotimeout.mappings=/jquery/plugin/dotimeout/jquery.ba-do-timeout.js

#blockui Library
jawr.js.bundle.blockui.id=/bundles/blockui.jsx
jawr.js.bundle.blockui.mappings=/jquery/plugin/blockui/jquery.blockUI.js

#uniform Library
jawr.js.bundle.uniform.id=/bundles/uniform.jsx
jawr.js.bundle.uniform.mappings=/jquery/plugin/uniform/jquery.uniform.min.js

#base64 Library add by fantasy 2011/04/12
jawr.js.bundle.base64.id=/bundles/base64.jsx
jawr.js.bundle.base64.mappings=/jquery/plugin/base64/jquery.base64.js

#thickbox
jawr.js.bundle.thickbox.id=/bundles/thickbox.jsx
jawr.js.bundle.thickbox.mappings=/js/thickbox/thickbox.js

#ckeditor Libary
jawr.js.bundle.ckeditorbase.id=/bundles/ckeditorbase.jsx
jawr.js.bundle.ckeditorbase.mappings=/js/ckeditor-3.5.2/ckeditor.js
jawr.js.bundle.ckeditorbase.bundlepostprocessors=none

#ckeditoradapters Libary
jawr.js.bundle.ckeditoradapters.id=/bundles/ckeditoradapters.jsx
jawr.js.bundle.ckeditoradapters.mappings=js/ckeditor-3.5.2/adapters/jquery.js

#jquery conrner
jawr.js.bundle.jquerycorner.id=/bundles/jquerycorner.jsx
jawr.js.bundle.jquerycorner.mappings=/jquery/plugin/jquery.corner.js

#yui 2.9.0 yahoo Libary
jawr.js.bundle.yahoodomevent.id=/bundles/yahoo-dom-event.jsx
jawr.js.bundle.yahoodomevent.mappings=/js/yui/yahoo-dom-event/yahoo-dom-event.js

#yui 2.9.0 element Libary
jawr.js.bundle.element.id=/bundles/element.jsx
jawr.js.bundle.element.mappings=/js/yui/element/element.js

#yui 2.9.0 event-delegate Libary
jawr.js.bundle.eventdelegate.id=/bundles/event-delegate.jsx
jawr.js.bundle.eventdelegate.mappings=/js/yui/event-delegate/event-delegate.js

#yui 2.9.0 cookie Libary
jawr.js.bundle.cookie.id=/bundles/cookie.jsx
jawr.js.bundle.cookie.mappings=/js/yui/cookie/cookie.js

#yui 2.9.0 swfstore Libary
jawr.js.bundle.swfstore.id=/bundles/swfstore.jsx
jawr.js.bundle.swfstore.mappings=/js/yui/swfstore/swfstore.js

#yui 2.9.0 swf Libary
jawr.js.bundle.swf.id=/bundles/swf.jsx
jawr.js.bundle.swf.mappings=/js/yui/swf/swf.js

#yui 2.9.0 storage Libary
jawr.js.bundle.storage.id=/bundles/storage.jsx
jawr.js.bundle.storage.mappings=/js/yui/storage/storage.js

#yui 2.9.0 yui swfstore init Libary
jawr.js.bundle.yuiswfinit.id=/bundles/yuiswfinit.jsx
jawr.js.bundle.yuiswfinit.mappings=/js/common/common.yuiswf.init.js

# The remaining scripts will be served sepparately. 
jawr.js.bundle.yuiswfstore.id=/bundles/yuiswfstore.jsx
jawr.js.bundle.yuiswfstore.composite=true
jawr.js.bundle.yuiswfstore.child.names=yahoodomevent,element,eventdelegate,cookie,swf,swfstore,yuiswfinit

#DOMPurify 3.0.6
jawr.js.bundle.purify.id=/bundles/purify.jsx
jawr.js.bundle.purify.mappings=/js/purify/3.0.6/purify.js

#---------------------#
# Cap Common Setting  #
#---------------------#
jawr.locale.resolver=tw.com.iisi.cap.jawr.CapLocaleResolverImpl
jawr.custom.generators=tw.com.iisi.cap.jawr.CapDefI18NGenerator

jawr.js.bundle.capi18n.id=/bundles/capi18n.jsx
jawr.js.bundle.capi18n.mappings=capi18n:/i18n/tw/com/iisi/cap/i18n/JsI18nAjaxHandler

# All files within /js/lib will be together in a bundle. 
# The remaining scripts will be served sepparately. 
jawr.js.bundle.capcommonJs.id=/bundles/capcommonJs.jsx
jawr.js.bundle.capcommonJs.composite=true
#jawr.js.bundle.capcommonJs.child.names=thickbox,cproperties,citem,common,ilog,ijqgrid,ijstree,itabs,cvalidate,properties
jawr.js.bundle.capcommonJs.child.names=capi18n,thickbox,cproperties,citem,common,ilog,ijqgrid,itabs,cvalidate,properties,samplejs

#common properties
jawr.js.bundle.cproperties.id=/bundles/cproperties.jsx
jawr.js.bundle.cproperties.mappings=/js/common/common.properties.js

#common Libary
jawr.js.bundle.common.id=/bundles/common.jsx
jawr.js.bundle.common.mappings=/js/common/common.js

#common properties
jawr.js.bundle.citem.id=/bundles/citem.jsx
jawr.js.bundle.citem.mappings=/js/common/common.item.js

#common validateSetting
jawr.js.bundle.cvalidate.id=/bundles/cvalidate.jsx
jawr.js.bundle.cvalidate.mappings=/js/common/common.validate.js

#common message
jawr.js.bundle.ilog.id=/bundles/message.jsx
jawr.js.bundle.ilog.mappings=/js/common/common.message.js

#ajax fileupload
jawr.js.bundle.ajaxfileupload.id=/bundles/ajaxfileupload.jsx
jawr.js.bundle.ajaxfileupload.mappings=/jquery/plugin/ajaxfileupload/ajaxfileupload.js

#common jqgrid Libary
jawr.js.bundle.ijqgrid.id=/bundles/ijqgrid.jsx
jawr.js.bundle.ijqgrid.mappings=/js/common/common.jqgrid.js

#common jstree Libary
jawr.js.bundle.ijstree.id=/bundles/ijstree.jsx
jawr.js.bundle.ijstree.mappings=/js/common/common.jstree.js


#common Libary
jawr.js.bundle.properties.id=/bundles/properties.jsx
jawr.js.bundle.properties.mappings=/js/common/mega.eloan.properties.js

#common ckeditor Libary
jawr.js.bundle.ckeditorfilter.id=/bundles/ckeditorfilter.jsx
jawr.js.bundle.ckeditorfilter.mappings=/js/common/common.ckeditor.filter.js

#common ckeditor Libary
jawr.js.bundle.ckeditorinit.id=/bundles/ckeditorinit.jsx
jawr.js.bundle.ckeditorinit.mappings=/js/common/common.ckeditor.init.js

# The remaining scripts will be served sepparately. 
jawr.js.bundle.ckeditor.id=/bundles/ckeditor.jsx
jawr.js.bundle.ckeditor.composite=true
jawr.js.bundle.ckeditor.child.names=ckeditorfilter,ckeditorbase,ckeditoradapters,ckeditorinit


#common ckeditor Libary
jawr.js.bundle.itabs.id=/bundles/itabs.jsx
jawr.js.bundle.itabs.mappings=/js/common/common.tabcrollable.js

jawr.js.bundle.samplejs.id=/bundles/samplejs.jsx
jawr.js.bundle.samplejs.mappings=/js/common/mega.eloan.sample.js

#--------------------#
#     CSS  Library   #
#--------------------#
# CSS properties and mappings
jawr.css.bundle.basedir=/css

#IISI Portal Base
jawr.css.bundle.base.id=/bundles/base.cssx
jawr.css.bundle.base.mappings=/css/base/base.css

#YUI Reset
jawr.css.bundle.yui.id=/bundles/yuireset.cssx
jawr.css.bundle.yui.mappings=/css/base/yui_reset.css

#FG Button
jawr.css.bundle.fgbutton.id=/bundles/fgbutton.cssx
jawr.css.bundle.fgbutton.mappings=/css/base/fgbutton.css

#jqGrid Library
jawr.css.bundle.jqgrid.id=/bundles/jqgrid.cssx
jawr.css.bundle.jqgrid.mappings=/jquery/plugin/jqgrid381/css/ui.jqgrid.css


#jstree Library
jawr.css.bundle.jstree.id=/bundles/jstree.cssx
jawr.css.bundle.jstree.mappings=/jquery/plugin/jsTreev102/themes/default/style.css

#qtip Library
jawr.css.bundle.qtip.id=/bundles/qtip.cssx
jawr.css.bundle.qtip.mappings=/jquery/plugin/qtipv200/jquery.qtip.css

#uniform Library
jawr.css.bundle.uniform.id=/bundles/uniform.cssx
jawr.css.bundle.uniform.mappings=/jquery/plugin/uniform/css/uniform.default.css

#thickbox Library
jawr.css.bundle.thickbox.id=/bundles/thickbox.cssx
jawr.css.bundle.thickbox.mappings=/js/thickbox/thickbox.css

#--------------------#
#     CSS Settings   #
#--------------------#

#Widget
jawr.css.bundle.widget.id=/bundles/widget.cssx
jawr.css.bundle.widget.mappings=/css/base/widget.css

#commmon message
jawr.css.bundle.ilog.id=/bundles/ilog.cssx
jawr.css.bundle.ilog.mappings=/css/base/common.message.css


jawr.css.bundle.baseCss.id=/bundles/baseCss.cssx
jawr.css.bundle.baseCss.composite=true
#jawr.css.bundle.baseCss.child.names=yui,jqui,jstree,qtip,uniform,fgbutton,thickbox,base,common
jawr.css.bundle.baseCss.child.names=yui,jqui,qtip,fgbutton,thickbox,base,common,elheader

jawr.css.bundle.coreCss.id=/bundles/coreCss.cssx
jawr.css.bundle.coreCss.composite=true
jawr.css.bundle.coreCss.child.names=jqgrid,ilog,capform

#------theme--------------------------

#jQuery UI CSS
jawr.css.bundle.jqui.id=/bundles/jqueryui.cssx
jawr.css.bundle.jqui.mappings=/css/theme/gray/jquery-ui-1.8.11.custom.css

jawr.css.bundle.login.id=/bundles/login.cssx
jawr.css.bundle.login.mappings=/css/theme/gray/login.css

jawr.css.bundle.capform.id=/bundles/capform.cssx
jawr.css.bundle.capform.mappings=/css/theme/gray/form.css

jawr.css.bundle.common.id=/bundles/common.cssx
jawr.css.bundle.common.mappings=/css/theme/gray/common.css

jawr.css.bundle.menu.id=/bundles/menu.cssx
jawr.css.bundle.menu.mappings=/css/theme/gray/menu.css

jawr.css.bundle.elheader.id=/bundles/elheader.cssx
jawr.css.bundle.elheader.mappings=/css/theme/gray/header.css

# CSS files will be all bundled together automatically
#jawr.css.factory.use.singlebundle=true
#jawr.css.factory.singlebundle.bundlename=/bundles/all.css
