/* 
 * L120S09A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 洗錢防制明細檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S09A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S09A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 文件產生方式
	 * <p/>
	 * 系統產生 | SYS<br/>
	 * 人工產生 | PEO
	 */
	@Size(max = 3)
	@Column(name = "CREATEBY", length = 3, columnDefinition = "CHAR(3)")
	private String createBY;

	/** 統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 中文戶名 **/
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 英文戶名 **/
	@Size(max = 120)
	@Column(name = "CUSTENAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custEName;

	/** 區部別 **/
	@Size(max = 1)
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 與借款人關係
	 * <p/>
	 * (多選)<br/>
	 * 借戶 | 1<br/>
	 * 借戶負責人 | 2<br/>
	 * 共同借款人 | 3<br/>
	 * 連保人 | 4<br/>
	 * 擔保品提供人 | 5<br/>
	 * 關係企業 | 6<br/>
	 * 實質受益人|7<br/>
	 * 一般保證人|8
	 */
	@Size(max = 20)
	@Column(name = "CUSTRELATION", length = 20, columnDefinition = "VARCHAR(20)")
	private String custRelation;

	/**
	 * 資料查詢日
	 * <p/>
	 * YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "QUERYDATES", columnDefinition = "DATE")
	private Date queryDateS;

	/**
	 * 黑名單查詢結果
	 * <p/>
	 * 00<br/>
	 * 02<br/>
	 * 04
	 */
	@Size(max = 2)
	@Column(name = "BLACKLISTCODE", length = 2, columnDefinition = "CHAR(02)")
	private String blackListCode;

	/** 列印順序 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "SEQNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer seqNum;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/**
	 * 備註(真的有中的名單)
	 * <p/>
	 * 100/12/05新增<br/>
	 */
	@Column(name = "MEMO", length = 240, columnDefinition = "VARCHAR(240)")
	private String memo;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 與借款人關係Grid 排序(非DB欄位) **/
	@Transient
	private String custRelationIndex;

	/** 掃描對象序號 **/
	@Size(max = 3)
	@Column(name = "CHECKSEQ", length = 3, columnDefinition = "CHAR(3)")
	private String checkSeq;

	/**
	 * 名單命中狀態
	 * <p/>
	 * 100/12/05新增<br/>
	 */
	@Column(name = "CHECKRESULT", length = 80, columnDefinition = "VARCHAR(80)")
	private String checkResult;

	/**
	 * 命中疑似名單小類
	 * <p/>
	 * 100/12/05新增<br/>
	 */
	@Column(name = "HITLIST", length = 240, columnDefinition = "VARCHAR(240)")
	private String hitList;

	/** 0024調查結果 **/
	@Size(max = 2)
	@Column(name = "CM1AMLSTATUS", length = 2, columnDefinition = "VARCHAR(2)")
	private String cm1AmlStatus;

	/** 風險等級 
	 * J-107-0059-001 Web e-Loan 授信簽報書與動審表之AML頁籤及列印檢核表時，增加引進風險等級
	 * **/
	@Size(max = 1)
	@Column(name = "LUVRISKLEVEL", length = 1, columnDefinition = "CHAR(1)")
	private String luvRiskLevel;

	/** 國別 **/
	/* ELF581_NATION 只有2碼, 而 COM.BELAMLITEM 的 Country 是 VARCHAR(128) */
	@Size(max = 2)
	@Column(name = "COUNTRY", length = 2, columnDefinition = "CHAR(2)")
	private String country;

	/** 是否需送掃PEPS：Y不用掃PEPS **/
	@Size(max = 1)
	@Column(name = "PASSPEPS", length = 1, columnDefinition = "VARCHAR(1)")
	private String passPeps;

	/** 徵信案號：徵信報告或資信簡表案號 **/
	@Size(max = 60)
	@Column(name = "CESSN", length = 60, columnDefinition = "VARCHAR(60)")
	private String cesSn;

	/** 徵信AML案件調查結果 **/
	@Size(max = 3)
	@Column(name = "CESNCRESULT", length = 3, columnDefinition = "VARCHAR(3)")
	private String cesNcResult;
	
	/**
	 * 「受告誡處分」結果
	 * 1.有
	 * 2.無
	 * 3.不適用
	 */
	@Size(max = 1)
	@Column(name = "CMFWARNPRESULT", length = 1, columnDefinition = "VARCHAR(1)")
	private String cmfwarnpResult;
	
	/**
	 * 「受告誡處分」查詢結果資訊
	 * 當cmfwarnpResult=1，存的是「受告誡處分」警告的起日
	 * 當cmfwarnpResult=2，為空值
	 * 當cmfwarnpResult=3，存的是ODS當下的錯誤狀態
	 */
	@Size(max = 10)
	@Column(name = "CMFWARNPQUERYRESULTINFO", length = 10, columnDefinition = "VARCHAR(10)")
	private String cmfwarnpQueryResultInfo;
	
	/** 「受告誡處分」查詢日期 **/
	@Column(name = "CMFWARNPQUERYTIME", columnDefinition = "TIMESTAMP")
	private Timestamp cmfwarnpQueryTime;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得文件產生方式
	 * <p/>
	 * 系統產生 | SYS<br/>
	 * 人工產生 | PEO
	 */
	public String getCreateBY() {
		return this.createBY;
	}

	/**
	 * 設定文件產生方式
	 * <p/>
	 * 系統產生 | SYS<br/>
	 * 人工產生 | PEO
	 **/
	public void setCreateBY(String value) {
		this.createBY = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得中文戶名 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定中文戶名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得英文戶名 **/
	public String getCustEName() {
		return this.custEName;
	}

	/** 設定英文戶名 **/
	public void setCustEName(String value) {
		this.custEName = value;
	}

	/** 取得區部別 **/
	public String getTypCd() {
		return this.typCd;
	}

	/** 設定區部別 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得與借款人關係
	 * <p/>
	 * (多選)<br/>
	 * 借戶 | 1<br/>
	 * 借戶負責人 | 2<br/>
	 * 共同借款人 | 3<br/>
	 * 連保人 | 4<br/>
	 * 擔保品提供人 | 5<br/>
	 * 關係企業 | 6<br/>
	 * 實質受益人|7
	 */
	public String getCustRelation() {
		return this.custRelation;
	}

	/**
	 * 設定與借款人關係
	 * <p/>
	 * (多選)<br/>
	 * 借戶 | 1<br/>
	 * 借戶負責人 | 2<br/>
	 * 共同借款人 | 3<br/>
	 * 連保人 | 4<br/>
	 * 擔保品提供人 | 5<br/>
	 * 關係企業 | 6<br/>
	 * 實質受益人|7
	 **/
	public void setCustRelation(String value) {
		this.custRelation = value;
	}

	/**
	 * 取得資料查詢日
	 * <p/>
	 * YYYY-MM-01
	 */
	public Date getQueryDateS() {
		return this.queryDateS;
	}

	/**
	 * 設定資料查詢日
	 * <p/>
	 * YYYY-MM-01
	 **/
	public void setQueryDateS(Date value) {
		this.queryDateS = value;
	}

	/**
	 * 取得黑名單查詢結果
	 * <p/>
	 * 00<br/>
	 * 02<br/>
	 * 04
	 */
	public String getBlackListCode() {
		return this.blackListCode;
	}

	/**
	 * 設定黑名單查詢結果
	 * <p/>
	 * 00<br/>
	 * 02<br/>
	 * 04
	 **/
	public void setBlackListCode(String value) {
		this.blackListCode = value;
	}

	/** 取得列印順序 **/
	public Integer getSeqNum() {
		return this.seqNum;
	}

	/** 設定列印順序 **/
	public void setSeqNum(Integer value) {
		this.seqNum = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/05新增<br/>
	 * Y/N<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 設定與借款人關係Grid 排序
	 * 
	 * @param custRelationIndex
	 */
	public void setCustRelationIndex(String custRelationIndex) {
		this.custRelationIndex = custRelationIndex;
	}

	/**
	 * 取得與借款人關係Grid 排序
	 * 
	 * @return
	 */
	public String getCustRelationIndex() {

		return this.custRelationIndex;
	}

	/**
	 * 設定備註
	 * 
	 */
	public void setMemo(String memo) {
		this.memo = memo;
	}

	/**
	 * 取得備註
	 * 
	 */
	public String getMemo() {
		return memo;
	}

	/**
	 * 設定掃描對象序號
	 * 
	 */
	public void setCheckSeq(String checkSeq) {
		this.checkSeq = checkSeq;
	}

	/**
	 * 取得掃描對象序號
	 * 
	 */
	public String getCheckSeq() {
		return checkSeq;
	}

	/**
	 * 設定0024調查結果
	 * 
	 */
	public void setCm1AmlStatus(String cm1AmlStatus) {
		this.cm1AmlStatus = cm1AmlStatus;
	}

	/**
	 * 取得0024調查結果
	 * 
	 */
	public String getCm1AmlStatus() {
		return cm1AmlStatus;
	}

	/**
	 * 設定名單命中狀態
	 * 
	 */
	public void setCheckResult(String checkResult) {
		this.checkResult = checkResult;
	}

	/**
	 * 取得名單命中狀態
	 * 
	 */
	public String getCheckResult() {
		return checkResult;
	}

	/**
	 * 設定命中疑似名單小類
	 * 
	 */
	public void setHitList(String hitList) {
		this.hitList = hitList;
	}

	/**
	 * 取得命中疑似名單小類
	 * 
	 */
	public String getHitList() {
		return hitList;
	}

	/**
	 * 設定風險等級
	 * 
	 */
	public void setLuvRiskLevel(String luvRiskLevel) {
		this.luvRiskLevel = luvRiskLevel;
	}

	/**
	 * 取得風險等級
	 * 
	 */
	public String getLuvRiskLevel() {
		return luvRiskLevel;
	}

	/** 取得國別 **/
	public String getCountry() {
		return country;
	}
	/** 設定國別 **/
	public void setCountry(String country) {
		this.country = country;
	}

	/** 取得是否需送掃PEPS：Y不用掃PEPS **/
	public String getPassPeps() {
		return passPeps;
	}
	/** 設定是否需送掃PEPS：Y不用掃PEPS **/
	public void setPassPeps(String passPeps) {
		this.passPeps = passPeps;
	}

	/** 取得徵信案號：徵信報告或資信簡表案號 **/
	public String getCesSn() {
		return cesSn;
	}
	/** 設定徵信案號：徵信報告或資信簡表案號 **/
	public void setCesSn(String cesSn) {
		this.cesSn = cesSn;
	}

	/** 取得徵信AML案件調查結果 **/
	public String getCesNcResult() {
		return cesNcResult;
	}
	/** 設定徵信AML案件調查結果 **/
	public void setCesNcResult(String cesNcResult) {
		this.cesNcResult = cesNcResult;
	}

	/** 取得受告誡處分結果 **/
	public String getCmfwarnpResult() {
		return cmfwarnpResult;
	}
	/** 設定受告誡處分結果 **/
	public void setCmfwarnpResult(String cmfwarnpResult) {
		this.cmfwarnpResult = cmfwarnpResult;
	}

	/** 取得「受告誡處分」查詢結果資訊 **/
	public String getCmfwarnpQueryResultInfo() {
		return cmfwarnpQueryResultInfo;
	}
	/** 設定「受告誡處分」查詢結果資訊 **/
	public void setCmfwarnpQueryResultInfo(String cmfwarnpQueryResultInfo) {
		this.cmfwarnpQueryResultInfo = cmfwarnpQueryResultInfo;
	}

	/** 取得「受告誡處分」查詢日期 **/
	public Timestamp getCmfwarnpQueryTime() {
		return cmfwarnpQueryTime;
	}
	/** 設定「受告誡處分」查詢日期 **/
	public void setCmfwarnpQueryTime(Timestamp cmfwarnpQueryTime) {
		this.cmfwarnpQueryTime = cmfwarnpQueryTime;
	}

}
