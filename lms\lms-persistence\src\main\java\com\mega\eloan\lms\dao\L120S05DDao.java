/* 
 * L120S05DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S05D;


/** 借款人集團授信明細檔 **/
public interface L120S05DDao extends IGenericDao<L120S05D> {

	L120S05D findByOid(String oid);
	
	List<L120S05D> findByMainId(String mainId);
	
	L120S05D findByUniqueKey(String mainId, String custId, String dupCode);

	List<L120S05D> findByIndex01(String mainId, String custId, String dupCode);

	List<L120S05D> findByCustIdDupId(String custId,String DupNo);
	
	int delModel(String mainId);
}