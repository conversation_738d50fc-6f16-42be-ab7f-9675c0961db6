package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140S09F database table.
 * </pre>
 * @since  2011/10/27
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/27,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140S09F.class)
public class C140S09F_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140S09F, String> linv11;
	public static volatile SingularAttribute<C140S09F, String> linv110;
	public static volatile SingularAttribute<C140S09F, String> linv111;
	public static volatile SingularAttribute<C140S09F, BigDecimal> linv112;
	public static volatile SingularAttribute<C140S09F, String> linv12;
	public static volatile SingularAttribute<C140S09F, BigDecimal> linv13;
	public static volatile SingularAttribute<C140S09F, String> linv14;
	public static volatile SingularAttribute<C140S09F, BigDecimal> linv15;
	public static volatile SingularAttribute<C140S09F, String> linv16;
	public static volatile SingularAttribute<C140S09F, BigDecimal> linv17;
	public static volatile SingularAttribute<C140S09F, String> linv18;
	public static volatile SingularAttribute<C140S09F, BigDecimal> linv19;
	public static volatile SingularAttribute<C140S09F, C140M01A> c140m01a;
}
