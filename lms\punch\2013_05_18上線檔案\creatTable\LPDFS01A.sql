---------------------------------------------------------
-- LMS.LPDFS01A 授信 PDF 舊案明細檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.LPDFS01A;
CREATE TABLE LMS.LPDFS01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	R<PERSON><PERSON><PERSON><PERSON>       CHAR(32)     ,
	RPTSEQ        DECIMAL(3,0) ,
	RP<PERSON><PERSON><PERSON>       DECIMAL(3,0) ,
	RP<PERSON>YP<PERSON>       VARCHAR(20)  ,
	RP<PERSON><PERSON><PERSON>       VARCHAR(120) ,
	CNTRNO        VARCHAR(12)  ,
	RPTFILE       VARCHAR(128) ,
	RANDOMCODE    CHAR(32)     ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_LPDFS01A PRIMARY KEY(OID)
) IN EL_DATA_4KTS
  INDEX IN EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XLPDFS01A01;
CREATE INDEX LMS.XLPDFS01A01 ON LMS.LPDFS01A   (MAINID, RPTUNID, RPTSEQ, RPTTYPE, RPTFILE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.LPDFS01A IS '授信 PDF 舊案明細檔';
COMMENT ON LMS.LPDFS01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	RPTUNID       IS '報表文件ID', 
	RPTSEQ        IS '顯示順序', 
	RPTDISP       IS '顯示註記', 
	RPTTYPE       IS '報表編號', 
	RPTNAME       IS '報表名稱(描述)', 
	CNTRNO        IS '額度序號', 
	RPTFILE       IS '報表檔檔案位置', 
	RANDOMCODE    IS '報表亂碼', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
