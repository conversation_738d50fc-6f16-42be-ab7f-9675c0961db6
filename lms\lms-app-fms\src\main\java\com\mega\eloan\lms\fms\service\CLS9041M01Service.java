/* 
 *CLS9041Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.model.C004M01A;
import com.mega.eloan.lms.model.C004S01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;

/**
 * <pre>
 * 政策性留學生貸款送保彙報
 * </pre>
 * 
 * @since 2012/11/05
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
/* Use MIS-RDB & R6 */
public interface CLS9041M01Service {

	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch pageSetting);

	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid);

	public void save(GenericBean... entity);

	public void delete(GenericBean... entity);

	public void deleteQ(String mainId);

	public void saveQ(List<C004S01A> qList);

	public List<C004S01A> getQ(String unid);

	public Page<? extends GenericBean> findFile(String mainId);

	public void deleteFile(String oid);

	public List<Map<String, Object>> getMisData(String beginDate,
			String endDate, String rptType);

	public Map<String, Object> findBrnoAndStuidS2(String beginDate, String endDate);
	
	public Map<String, Object> findBrnoAndStuidS3(String beginDate, String endDate);
	
	public boolean sendRpt(String unid,Date date);

	public List<Map<String, Object>> getMisDataNewS1(String beginDate, String endDate,
			String rptType);
	/**
	 * update ln.lnf192 set LNF192_SEND_DATE=? where LNF192_BR_NO = ? AND LNF192_STUDENT_ID = ?
	 */
	public void updateLNF192S1(String sendDate, String brNo, String studentId,
			String begDate, String endDate);
	/**
	 * update ln.lnf192 set LNF192_OV_DATE_S=? where LNF192_STUDENT_ID=? AND LNF192_BEG_DATE=?
	 */
	public void updateLNF192S2(String sendDate, String custId,
			String begDate);
	
	public List<C004M01A> findC004M01AByUnid(String unid);
}
