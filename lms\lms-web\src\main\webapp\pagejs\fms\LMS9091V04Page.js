$(document).ready(function(){
	
	$("#btn_calc").click(function(){
		if($("#tabForm").valid()){
			 $.form.submit({
      	        url: "../simple/FileProcessingService",
      	        target: "_blank",
      	        data: $.extend( $("#tabForm").serializeData(), 
      	        		{'fileDownloadName': "data.pdf",
      	            	'serviceName': "lms9091r04rptservice"            
      	            	}
      	        	)
      	     });
		}
		
	});
	
	$("#traditionalLoanAmount, #quotaLoanAmount").change(function(){
		computeTotalLoanAmount();
	});
	
	$("#collateralLoanValue").change(function(){
		computeCollateralLoanValue();
	});
});

var traditionalLoanAmount = 0;
var quotaLoanAmount = 0;
var totalLoanAmt = 0;
function computeTotalLoanAmount(){
	
	var traObj = $("#traditionalLoanAmount");
	var quoObj = $("#quotaLoanAmount");
	traditionalLoanAmount = traObj == undefined || isNaN(parseInt(traObj.val())) ? 0 : parseInt(traObj.val());
	quotaLoanAmount = quoObj == undefined || isNaN(parseInt(quoObj.val())) ? 0 : parseInt(quoObj.val());
	totalLoanAmt = traditionalLoanAmount + quotaLoanAmount;
	$("#totalLoanAmt").val(totalLoanAmt);
	computeCollateralLoanValue();
}

function computeCollateralLoanValue(){
	
	var collObj = $("#collateralLoanValue");
	var collValue = collObj == undefined || isNaN(parseInt(collObj.val())) ? 0 : parseInt(collObj.val());
	
	if(totalLoanAmt > 0 && collValue > 0){
		traPercent = Math.floor(traditionalLoanAmount/totalLoanAmt*10000)/10000;
		quoPercent = Math.floor(quotaLoanAmount/totalLoanAmt*10000)/10000;
		$("#tra_collateralLoanValue").val(collValue * traPercent);
		$("#quo_collateralLoanValue").val(collValue * quoPercent);
	}
}