package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 消金新案紀錄檔 **/
public class ELF498 extends GenericBean{

	private static final long serialVersionUID = 1L;

	/**
	 * 分行代號
	 */
	@Column(name = "ELF498_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable=false,unique = true)
	private String elf498_branch;
	
	/**
	 * 借款人統一編號
	 */
	@Column(name = "ELF498_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable=false,unique = true)
	private String elf498_custid;
	
	/**
	 * 重複序號
	 */
	@Column(name = "ELF498_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable=false,unique = true)
	private String elf498_dupno;
	
	/**
	 * 新案資料日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF498_NEWDATE", columnDefinition = "DATE", nullable=false,unique = true)
	private Date elf498_newdate;
	
	/**
	 * 最遲應覆審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF498_LATESTDATE", columnDefinition = "DATE")
	private Date elf498_latestdate;
	
	/**
	 * 實際覆審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF498_ACTUALDATE", columnDefinition = "DATE")
	private Date elf498_actualdate;
	
	/**
	 * 覆審類別
	 */
	@Column(name = "ELF498_KIND", length = 100, columnDefinition = "CHAR(100)")	
	private String elf498_kind;
		
	/**
	 * 額度序號
	 */
	@Column(name = "ELF498_TOTALSNO", length = 200, columnDefinition = "CHAR(200)")
	private String elf498_totalsno;
	
	/**
	 * 備註
	 */
	@Column(name = "ELF498_REMARK", length = 200, columnDefinition = "CHAR(200)")
	private String elf498_remark;

	/**
	 * 資料修改人
	 */
	@Column(name = "ELF498_UPDATER", length = 8, columnDefinition = "CHAR(8)")
	private String elf498_updater;
	
	/** 
	 * 資料更新日
	 */
	@Column(name = "ELF498_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf498_tmestamp;

	public String getElf498_branch() {
		return elf498_branch;
	}

	public void setElf498_branch(String elf498_branch) {
		this.elf498_branch = elf498_branch;
	}

	public String getElf498_custid() {
		return elf498_custid;
	}

	public void setElf498_custid(String elf498_custid) {
		this.elf498_custid = elf498_custid;
	}

	public String getElf498_dupno() {
		return elf498_dupno;
	}

	public void setElf498_dupno(String elf498_dupno) {
		this.elf498_dupno = elf498_dupno;
	}

	public Date getElf498_newdate() {
		return elf498_newdate;
	}

	public void setElf498_newdate(Date elf498_newdate) {
		this.elf498_newdate = elf498_newdate;
	}

	public Date getElf498_latestdate() {
		return elf498_latestdate;
	}

	public void setElf498_latestdate(Date elf498_latestdate) {
		this.elf498_latestdate = elf498_latestdate;
	}

	public Date getElf498_actualdate() {
		return elf498_actualdate;
	}

	public void setElf498_actualdate(Date elf498_actualdate) {
		this.elf498_actualdate = elf498_actualdate;
	}

	public String getElf498_kind() {
		return elf498_kind;
	}

	public void setElf498_kind(String elf498_kind) {
		this.elf498_kind = elf498_kind;
	}

	public String getElf498_totalsno() {
		return elf498_totalsno;
	}

	public void setElf498_totalsno(String elf498_totalsno) {
		this.elf498_totalsno = elf498_totalsno;
	}

	public String getElf498_remark() {
		return elf498_remark;
	}

	public void setElf498_remark(String elf498_remark) {
		this.elf498_remark = elf498_remark;
	}

	public String getElf498_updater() {
		return elf498_updater;
	}

	public void setElf498_updater(String elf498_updater) {
		this.elf498_updater = elf498_updater;
	}

	public Timestamp getElf498_tmestamp() {
		return elf498_tmestamp;
	}

	public void setElf498_tmestamp(Timestamp elf498_tmestamp) {
		this.elf498_tmestamp = elf498_tmestamp;
	}
}
