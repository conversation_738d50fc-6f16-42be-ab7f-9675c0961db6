/* 
 * MisEJF357Service.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * BAM303->EJV35701->EJF357授信保證資料(B31之從債務資料)
 * </pre>
 * 
 * @since 2012/3/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/3/22,<PERSON>,new
 *          </ul>
 */
public interface MisEJF357Service {

//	/**
//	 * 查詢BAM303 從債務餘額
//	 * 
//	 * @param custId
//	 *            客戶統編
//	 * @return Map
//	 */
//	Map<String, Object> getLoanAmtById(String custId);
//
//	/**
//	 * 查詢 從債務餘額
//	 * @param compId String
//	 * @param yyy String
//	 * @param mm String
//	 * @return Map<String, Object>
//	 */
//	Map<String, Object> getLoanAmtByIdAndDate(String compId, String yyy,
//			String mm);
//
//	/**
//	 * 查詢 該筆 "從債務" 授信目前的 逾期金額
//	 * @param compId String
//	 * @return Map<String, Object>
//	 */
//	Map<String, Object> getPassDueAmtById(String compId);
//	
	/**
	 * 引入保證債務資料
	 * @param id 客戶統一編號
	 * @param prodId 查詢產品別
	 * @return Map
	 */
	List<Map<String, Object>> findGuaranteeDebt(String id, String prodId);
//	
//	/**
//	 * 查詢聯徵BAM303資料。
//	 * @param qDate 介面_取得聯徵資料日期，查詢日期，格式為 YYY/MM/DD
//	 * @param compIds 統編，可輸入多筆
//	 * @param bcs 機構代碼，可輸入多筆
//	 * @return List<Map<String, Object>>
//	 */
//	List<Map<String, Object>> findLoanAmountAndBalance(String qDate, List<String> compIds, List<String> bcs);
}
