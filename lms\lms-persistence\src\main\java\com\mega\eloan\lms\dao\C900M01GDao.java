/* 
 * C900M01GDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01G;

/** 團貸分戶明細檔 **/
public interface C900M01GDao extends IGenericDao<C900M01G> {

	C900M01G findByOid(String oid);
	
	List<C900M01G> findByMainId(String mainId);
	
	C900M01G findByUniqueKey(String cntrNo, String grpcntrno);

	List<C900M01G> findByIndex01(String cntrNo, String GRPCNTRNO);

	C900M01G findByCntrNo(String cntrNo);
	
	List<C900M01G> findByUsedDetail(String grpcntrno);
}