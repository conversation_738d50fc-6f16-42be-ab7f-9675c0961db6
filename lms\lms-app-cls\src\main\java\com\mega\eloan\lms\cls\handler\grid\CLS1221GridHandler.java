package com.mega.eloan.lms.cls.handler.grid;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.MEGAImageApiEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.gwclient.PLOAN001;
import com.mega.eloan.common.gwclient.PLOAN001.PLOAN001_loanInfo_relationObj;
import com.mega.eloan.common.gwclient.PLOAN001.PLOAN001_loanInfo_servedObj;
import com.mega.eloan.common.gwclient.PLOAN002;
import com.mega.eloan.common.gwclient.PLOAN002.PLOAN002_loanInfo_relationObj;
import com.mega.eloan.common.gwclient.PLOAN002.PLOAN002_loanInfo_servedObj;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.MEGAImageService;
import com.mega.eloan.common.service.MEGAImageService.取得影像清單_ScanType;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01B;
import com.mega.eloan.lms.model.C122M01C;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C122S01B;
import com.mega.eloan.lms.model.C122S01H;
import com.mega.eloan.lms.model.C900M01M;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.ADDateTimeFormatter;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  線上申貸原始資料
 * </pre>
 * 
 * @since 2015/04/28
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/04/28,EL03738,new
 *        
 *          </ul>
 */
@Scope("request")
@Controller("cls1221gridhandler")
public class CLS1221GridHandler extends AbstractGridHandler {

	@Resource
	BranchService branchService;
	
	@Resource
	CLS1220Service service;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	RelatedAccountService relatedAccountService;
	
	@Resource
	RetrialService retrialService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	MEGAImageService mEGAImageService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	CLSService clsService;
	
	public CapGridResult queryView(ISearch pageSetting, PageParameters params)
			throws CapException {
		// 建立主要Search 條件
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		String docStatus = Util.trim(params.getString("docStatus"));
		if(Util.equals(docStatus, CLSDocStatusEnum.編製中.getCode())
			|| Util.equals(docStatus, CLSDocStatusEnum.待覆核.getCode())){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);	
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", UtilConstants.C122_ApplyKind.H);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addSearchModeParameters(SearchMode.NOT_LIKE, "docStatus", "A%");
		if(true){
			String custId = Util.trim(params.getString("custId"));
			if(Util.isNotEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);		
			}
			String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			String applyTS_end = Util.trim(params.getString("applyTS_end"));
			if(Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end) ){
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"applyTS", new Object[] { Util.parseDate(applyTS_beg),
								Util.parseDate(applyTS_end + " 23:59:59") });
			}
			String applyStatus = Util.trim(params.getString("applyStatus"));
			if(Util.isNotEmpty(applyStatus)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyStatus",applyStatus);		
			}
		}
		// 取得資料
		Page<C122M01A> page = service.getC122V01(pageSetting);			
		
		final Map<String, String> _ApplyDocStatusDescMap = service.get_ApplyDocStatusDescMap();
		
		Map<String, String> _DocStatusDescMap = service.get_DocStatusDescMap();
		for(C122M01A model : page.getContent()){
			String isClosed = LMSUtil.getDesc(_DocStatusDescMap, model.getDocStatus());
			model.setIsClosed(isClosed);//借 欄位 isClosed 放 docStatusCN
		}
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyTS", new ADDateTimeFormatter("yyyy-MM-dd"));
		formatter.put("applyStatus", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyStatus = Util.trim(in);
				return LMSUtil.getDesc(_ApplyDocStatusDescMap, applyStatus);
			}
			
		});
		formatter.put("updater", new UserNameFormatter(userInfoService));
		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}
	
	public CapGridResult queryView_C(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		String docStatus = Util.trim(params.getString("docStatus"));
		if(Util.equals(docStatus, CLSDocStatusEnum.編製中.getCode())
			|| Util.equals(docStatus, CLSDocStatusEnum.待覆核.getCode())){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);	
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", UtilConstants.C122_ApplyKind.C);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addSearchModeParameters(SearchMode.NOT_LIKE, "docStatus", "A%");
		if(true){
			String custId = Util.trim(params.getString("custId"));
			if(Util.isNotEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);		
			}
			String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			String applyTS_end = Util.trim(params.getString("applyTS_end"));
			if(Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end) ){
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"applyTS", new Object[] { Util.parseDate(applyTS_beg),
								Util.parseDate(applyTS_end + " 23:59:59") });
			}
			String applyStatus = Util.trim(params.getString("applyStatus"));
			if(Util.isNotEmpty(applyStatus)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyStatus",applyStatus);		
			}
		}
		// 取得資料
		Page<C122M01A> page = service.getC122M01A(pageSetting);			
		
		final Map<String, String> _ApplyDocStatusDescMap = service.get_ApplyDocStatusDescMap();
		
		Map<String, String> _DocStatusDescMap = service.get_DocStatusDescMap();
		for(C122M01A model : page.getContent()){
			String isClosed = LMSUtil.getDesc(_DocStatusDescMap, model.getDocStatus());
			model.setIsClosed(isClosed);//借 欄位 isClosed 放 docStatusCN
		}
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyTS", new ADDateTimeFormatter("yyyy-MM-dd"));
		formatter.put("applyStatus", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyStatus = Util.trim(in);
				return LMSUtil.getDesc(_ApplyDocStatusDescMap, applyStatus);
			}
			
		});
		formatter.put("updater", new UserNameFormatter(userInfoService));
		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}
	
	public CapGridResult queryView_B(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", UtilConstants.C122_ApplyKind.B);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		if(true){
			String custId = Util.trim(params.getString("custId"));
			if(Util.isNotEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);		
			}
			String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			String applyTS_end = Util.trim(params.getString("applyTS_end"));
			if(Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end) ){
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"applyTS", new Object[] { Util.parseDate(applyTS_beg),
								Util.parseDate(applyTS_end + " 23:59:59") });
			}			
			String statFlag = Util.trim(params.getString("statFlag"));
			if(Util.isNotEmpty(statFlag)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "statFlag", statFlag);		
			}
		}
		// 取得資料
		Page<C122M01A> page = service.getC122M01A(pageSetting);			
		
		final Map<String, String> _statFlagDescMap = service.get_ApplyKindB_statFlagDescMap();
		
//		Map<String, String> _DocStatusDescMap = service.get_DocStatusDescMap();
//		for(C122M01A model : page.getContent()){
//			String isClosed = LMSUtil.getDesc(_DocStatusDescMap, model.getDocStatus());
//			model.setIsClosed(isClosed);//借 欄位 isClosed 放 docStatusCN
//		}
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyTS", new ADDateTimeFormatter("yyyy-MM-dd"));
		formatter.put("statFlag", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String statFlag = Util.trim(in);
				return LMSUtil.getDesc(_statFlagDescMap, statFlag);
			}
			
		});
		formatter.put("updater", new UserNameFormatter(userInfoService));
		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}
	
	public CapGridResult queryAttch(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		List<DocFile> newDocFiles = service.getAttchUndoneMEGAImageDocFiles(mainId);
		Page<DocFile> page = new Page<DocFile>(newDocFiles, newDocFiles.size(), newDocFiles.size(), 1);
		final Map<String, String> docFileFlagMap = ClsUtility.ploan_attch_DocFileFlagDesc();
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("flag", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String docStatus = Util.trim(in);
				return LMSUtil.getDesc(docFileFlagMap, docStatus);
			}
			
		});
		formatter.put("fileSrc", new IBeanFormatter(){
			private static final long serialVersionUID = 1L;

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				DocFile docFile = (DocFile) in;
				String fileSrc = "";
				if (CapString.trimNull(docFile.getFieldId()).startsWith("userUpload")) {
					String userId = docFile.getFieldId().replace("userUpload", "");
					String userName = userInfoService.getUserName(userId);
					fileSrc = userId + userName;
				} else {
					fileSrc = "客戶自行上傳";
				}
				return fileSrc;
			}
			
		});
		
		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);
	}
	
	public CapMapGridResult queryMegaImageList(ISearch pageSetting, PageParameters params) throws CapException {
		List<Map<String, Object>> megaImageList = new ArrayList<Map<String, Object>>();
		CapMapGridResult m01Result = new CapMapGridResult(megaImageList, megaImageList.size());
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		try {
			megaImageList = service.getAttchDoneMegaImageList(mainId);
			m01Result = new CapMapGridResult(megaImageList, megaImageList.size());
			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			m01Result.setDataReformatter(formatter);
		} catch (Exception e) {
			throw new CapException("[mainId="+mainId+"]轉換資料發生錯誤", getClass());
		}
		return m01Result;
	}
	
	public CapMapGridResult queryDocFileListById(ISearch pageSetting, PageParameters params) throws CapException {
		List<Map<String, Object>> docFileList = new ArrayList<Map<String, Object>>();
		CapMapGridResult m01Result = new CapMapGridResult(docFileList, docFileList.size());
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		String custId = params.getString("custId");
		try {
			C122M01A meta = service.getC122M01A_byMainId(mainId);
			if (meta != null) {
				Map<String, String> docFileFlagMap = ClsUtility.ploan_attch_DocFileFlagDesc();
				// 本地檔案塞入列表
				List<C900M01M> c900m01ms = clsService.findC900M01MByCaseNoId(meta.getPloanCaseId(), custId);
				for (C900M01M c900m01m : c900m01ms) {
					// 不顯示主借人的檔案
					if (c900m01m.getBorrower().equals(c900m01m.getStakeholderID())) {
						continue;
					}
					String docFileOid = c900m01m.getDocFileOid();
					DocFile docFile = docFileService.findByOidAndSysId(docFileOid, "LMS");
					if (docFile != null && docFile.getDeletedTime() == null) {
						String fileSrc = "";
						if (CapString.trimNull(docFile.getFieldId()).startsWith("userUpload")) {
							String userId = docFile.getFieldId().replace("userUpload", "");
							String userName = userInfoService.getUserName(userId);
							fileSrc = userId + userName;
						} else {
							fileSrc = "客戶自行上傳";
						}
						Map<String, Object> docMap = new HashMap<String, Object>();
						docMap.put("flag", LMSUtil.getDesc(docFileFlagMap, docFile.getFlag()));
						docMap.put("srcFileName", docFile.getSrcFileName());
						docMap.put("uploadTime", docFile.getUploadTime());
						docMap.put("fileSrc", fileSrc);
						docMap.put("fieldId", docFile.getFieldId());
						docMap.put("oid", docFile.getOid());
						docMap.put("docFileOid", docFileOid);
						docFileList.add(docMap);
					}
				}
				// 文件數位化資料非ELOAN上傳資料塞入列表
				List<Map<String, Object>> megaImageList = mEGAImageService
						.getMEGAImageList(MEGAImageApiEnum.取得影像清單, mainId, meta.getPloanCaseId());
				Map<String, String> formIdcodeMap = codeTypeService.findByCodeType("MEGAIMAGE_FormId_ALL");
				for (Map<String, Object> map : megaImageList) {
					Map<String, Object> docMap = new HashMap<String, Object>();
					String scanType = CapString.trimNull(map.get("ScanType"));
					if (取得影像清單_ScanType.eLoan上傳文件.equals(scanType)) {
						// ScanType不為eCL - eLoan上傳文件
						continue;
					}
					String borrower = CapString.trimNull(map.get("Borrower"));
					String stakeholderID = CapString.trimNull(map.get("StakeholderID"));
					if (borrower.equals(stakeholderID) || stakeholderID.isEmpty()) {
						// 不顯示主借人的檔案
						continue;
					}
					String formId = CapString.trimNull(map.get("FormId"));
					String formIdDesc = "";
					if (formId.length() > 3) {
						char[] formIdChars = formId.toCharArray();
						formIdChars[3] = '0';
						formIdDesc = formIdcodeMap.get(String.valueOf(formIdChars));
					}
					String srcFileName = formIdDesc;
					String fileNameCustId = "";
					if (borrower.equals(stakeholderID) || stakeholderID.isEmpty()) {
						// 主借款人
						fileNameCustId = borrower;
					} else {
						// 從債務人
						fileNameCustId = stakeholderID;
					}
					if (!fileNameCustId.equals(custId)) {
						continue;
					}
					srcFileName = fileNameCustId + "_" + srcFileName;
					String docId = CapString.trimNull(map.get("DocId"));
					String docFileOid = CapString.trimNull(map.get("DocFileOid"));
					if (CapString.isEmpty(docFileOid)) {
						continue;
					}
					String scanDateTime = CapString.trimNull(map.get("ScanDateTime"));
					Timestamp uploadTime = null;
					if (!CapString.isEmpty(scanDateTime)) {
						scanDateTime = scanDateTime.replace("/", "-");
						uploadTime = CapDate.convertStringToTimestamp(scanDateTime);
					}
					docMap.put("flag", formId);
					docMap.put("srcFileName", srcFileName);
					docMap.put("uploadTime", uploadTime);
					docMap.put("fileSrc", "文件數位化系統");
					docMap.put("fieldId", "MEGAImage");
					docMap.put("oid", docId);
					docMap.put("docFileOid", docFileOid);
					docFileList.add(docMap);
				}
			}
			m01Result = new CapMapGridResult(docFileList, docFileList.size());
			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			m01Result.setDataReformatter(formatter);
		} catch (Exception e) {
			logger.error(e.getMessage());
			logger.error(StrUtils.getStackTrace(e));
			throw new CapException("[mainId="+mainId+"]轉換資料發生錯誤", getClass());
		}
		return m01Result;
	}

	public CapGridResult queryCntrInfo(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		int batchNo = Util.parseInt(params.getString("batchNo"));		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "batchNo", batchNo);
		//在js 用 sortname/sortorder
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		Page<C122S01B> page = service.getC122S01B(pageSetting);
		
		Map<String, String> prodKindMap = retrialService.get_crs_prodKindMap();
		for(C122S01B c122s01b : page.getContent()){
			c122s01b.setProdKind(LMSUtil.getDesc(prodKindMap, c122s01b.getProdKind()));
			c122s01b.setLink("連結");
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
	
	public CapMapGridResult queryImpCntrInfo(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		
		List<Map<String, Object>> list = eloandbBASEService.findL140InfoSinceWebApplyTS(c122m01a.getOwnBrId()
				, c122m01a.getCustId(), c122m01a.getDupNo(), c122m01a.getApplyTS());
		Map<String, String> codeMap = codeTypeService.findByCodeType("L140S02A_property");
		Map<String, String> prodKindMap = retrialService.get_crs_prodKindMap();
		for(Map<String, Object> m:list){
			m.put("prodKind", LMSUtil.getDesc(prodKindMap, MapUtils.getString(m, "prodKind")));
			L140S02A l140s02a = new L140S02A();
			l140s02a.setProperty(Util.trim(MapUtils.getString(m, "property")));
			l140s02a.setSubProperty(Util.trim(MapUtils.getString(m, "subProperty")));
			m.put("property", LMSUtil.cntrNoProperty_desc_orderBy(l140s02a, "、", codeMap));
		}
		return new CapMapGridResult(list, list.size());
	}
	
	public CapGridResult queryUnMatchReasonGrid(ISearch pageSetting, PageParameters params) throws CapException {
		String custId = Util.trim(params.getString("custId"));
		Date beginDate = CapDate.parseDate(Util.trim(params.getString("beginDate")));
		Date endDate = CapDate.parseDate(Util.trim(params.getString("endDate")));
		if(beginDate==null){
			beginDate = CapDate.getCurrentTimestamp();
		}
		if(endDate==null){
			endDate = CapDate.getCurrentTimestamp();
		}
		if(Util.equals("Y", params.getString("flag"))){
			//當 flag==Y，查才資料
		}else{
			//不然清空前端 grid 的資料列(用查到0筆的方式)
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", "");
		}
		//參考 service.queryUnMatchReason(custId, dupNo);
		if(Util.isNotEmpty(custId)){
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId", custId+"%");	
		}
		pageSetting.addSearchModeParameters(SearchMode.LIKE, "docStatus", "A%");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docStatus", "A00");
		pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
				"applyTS", new Object[] { Util.parseDate(TWNDate.toAD(beginDate) + " 00:00:00"),
						Util.parseDate(TWNDate.toAD(endDate) + " 23:59:59") });
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		if(true){
			pageSetting.addOrderBy("applyTS", true);	
		}						
		Page<C122M01A> page = service.getC122M01A(pageSetting);
		
		final Map<String, String> notMatchMap = getNotMatchDesc();
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("docStatus", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String docStatus = Util.trim(in);
				return LMSUtil.getDesc(notMatchMap, docStatus);
			}
			
		});
		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);		
	}
	
	private Map<String, String> getNotMatchDesc(){
		Map<String, String> r = new HashMap<String, String>();
		r.put("A01", "非本行規範之房貸戶");
		r.put("A02", "有連保人/保證人");
		r.put("A03", "央行受限戶");
		r.put("A04", "可增貸金額未達最低金額");
		return r;
	}

	public CapGridResult query_applyKindB_History(ISearch pageSetting, PageParameters params) throws CapException {
		//pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId); => 查到客戶是否誤選其它分行
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", UtilConstants.C122_ApplyKind.B);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		String custId = Util.trim(params.getString("custId"));
		if(Util.equals("Y", params.getString("flag"))){
			//當 flag==Y，查才資料
			if(Util.isEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", "");	
			}else{
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId", custId+"%");
			}	
		}else{
			//不然清空前端 grid 的資料列(用查到0筆的方式)
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", "");
		}
		
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		if(true){
			pageSetting.addOrderBy("custId", false);	
		}						
		Page<C122M01A> page = service.getC122M01A(pageSetting);
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyTS", new ADDateTimeFormatter("yyyy-MM-dd"));
		formatter.put("ownBrId", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName));
		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);		
	}

	public CapGridResult query_applyKindP_History(ISearch pageSetting, PageParameters params) throws CapException {
		//pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId); => 查到客戶是否誤選其它分行
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", UtilConstants.C122_ApplyKind.P);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		String custId = Util.trim(params.getString("custId"));
		if(Util.equals("Y", params.getString("flag"))){
			//當 flag==Y，查才資料
			if(Util.isEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", "");	
			}else{
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId", custId+"%");
			}	
		}else{
			//不然清空前端 grid 的資料列(用查到0筆的方式)
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", "");
		}
		
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		if(true){
			pageSetting.addOrderBy("applyTS", true);
			pageSetting.addOrderBy("custId", false);	
		}						
		Page<C122M01A> page = service.getC122M01A(pageSetting);
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyTS", new ADDateTimeFormatter("yyyy-MM-dd"));
		formatter.put("ownBrId", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName));
		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);		
	}
	
	public CapGridResult query_applyKindE_History(ISearch pageSetting,
			PageParameters params) throws CapException {
		//pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId); => 查到客戶是否誤選其它分行
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", UtilConstants.C122_ApplyKind.E);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		String custId = Util.trim(params.getString("custId"));
		if(Util.equals("Y", params.getString("flag"))){
			//當 flag==Y，查才資料
			if(Util.isEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", "");	
			}else{
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId", custId+"%");
			}	
		}else{
			//不然清空前端 grid 的資料列(用查到0筆的方式)
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", "");
		}
		
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		if(true){
			pageSetting.addOrderBy("applyTS", true);	
			pageSetting.addOrderBy("custId", false);	
		}						
		Page<C122M01A> page = service.getC122M01A(pageSetting);
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyTS", new ADDateTimeFormatter("yyyy-MM-dd"));
		formatter.put("ownBrId", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName));
		return new CapGridResult(page.getContent(), page.getTotalRow(), formatter);		
	}
		
	public CapGridResult queryView_P(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", UtilConstants.C122_ApplyKind.P);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "IncomType", UtilConstants.C122_IncomType.線上);
		if(true){
			String custId = Util.trim(params.getString("custId"));
			if(Util.isNotEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);		
			}			
			
			String ploanCaseId = Util.trim(params.getString("ploanCaseId"));
			if(Util.isNotEmpty(ploanCaseId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId", ploanCaseId);		
			}
		
			String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			String applyTS_end = Util.trim(params.getString("applyTS_end"));
			if(Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end) ){
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"applyTS", new Object[] { Util.parseDate(applyTS_beg),
								Util.parseDate(applyTS_end + " 23:59:59") });
			}			
			String statFlag = Util.trim(params.getString("statFlag"));
			if(Util.isNotEmpty(statFlag)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "statFlag", statFlag);		
			}
			String ploanPlan = Util.trim(params.getString("ploanPlan"));
			if(Util.isNotEmpty(ploanPlan)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanPlan", ploanPlan);		
			}
		}
		// 取得資料
		Page<C122M01A> page = service.getC122M01A(pageSetting);				
		List<C122M01A> list = (List<C122M01A>) page.getContent();
		Map<String, String> cache_map = new HashMap<String, String>();
		for(C122M01A model : list){
			model.setLoanBrNo(service.build_loanBrNo(cache_map, model));
			model.setPayrollTransfersBrNo(service.build_payrollTransfersBrNo(cache_map, model));
		}	
			
		final Map<String, String> _ApplyDocStatusDescMap = service.get_ApplyDocStatusDescMap();
		final Map<String, String> _PloanStatFlagDescMap = service.get_PloanStatFlagDescMap(UtilConstants.C122_ApplyKind.P);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyStatus", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyStatus = Util.trim(in);
				return LMSUtil.getDesc(_ApplyDocStatusDescMap, applyStatus);
			}
			
		});
		formatter.put("statFlag", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String statFlag = Util.trim(in);
				return LMSUtil.getDesc(_PloanStatFlagDescMap, statFlag);
			}
			
		});
		formatter.put("updater", new UserNameFormatter(userInfoService));
		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}
	
	public CapGridResult queryView_E(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", UtilConstants.C122_ApplyKind.E);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "IncomType", UtilConstants.C122_IncomType.線上);
		if(true){
			String custId = Util.trim(params.getString("custId"));
			if(Util.isNotEmpty(custId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);		
			}				
			
			String ploanCaseId = Util.trim(params.getString("ploanCaseId"));
			if(Util.isNotEmpty(ploanCaseId)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId", ploanCaseId);		
			}	
		
			String applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			String applyTS_end = Util.trim(params.getString("applyTS_end"));
			if(Util.isNotEmpty(applyTS_beg) && Util.isNotEmpty(applyTS_end) ){
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"applyTS", new Object[] { Util.parseDate(applyTS_beg),
								Util.parseDate(applyTS_end + " 23:59:59") });
			}			
			String statFlag = Util.trim(params.getString("statFlag"));
			if(Util.isNotEmpty(statFlag)){
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "statFlag", statFlag);		
			}
		}
		// 取得資料
		Page<C122M01A> page = service.getC122M01A(pageSetting);			
		List<C122M01A> list = (List<C122M01A>) page.getContent();
		Map<String, String> cache_map = new HashMap<String, String>();
		for(C122M01A model : list){
			model.setLoanBrNo(service.build_loanBrNo(cache_map, model));
			model.setPayrollTransfersBrNo(service.build_payrollTransfersBrNo(cache_map, model));
		}
		
		final Map<String, String> _ApplyDocStatusDescMap = service.get_ApplyDocStatusDescMap();
		final Map<String, String> _PloanStatFlagDescMap = service.get_PloanStatFlagDescMap(UtilConstants.C122_ApplyKind.E);
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("applyStatus", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyStatus = Util.trim(in);
				return LMSUtil.getDesc(_ApplyDocStatusDescMap, applyStatus);
			}
			
		});
		formatter.put("statFlag", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String statFlag = Util.trim(in);
				return LMSUtil.getDesc(_PloanStatFlagDescMap, statFlag);
			}
			
		});
		formatter.put("updater", new UserNameFormatter(userInfoService));
		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	/**
	 * 查詢C122M01E團體消貸名單控制檔
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryViewC122M01E(ISearch pageSetting,
			PageParameters params) throws CapException {
		String ownBrId = MegaSSOSecurityContext.getUnitNo();
		String grpCntrNo = Util.trim(params.getString("grpCntrNo"));

		if (Util.isNotEmpty(grpCntrNo)) {			
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"grpCntrNo", grpCntrNo);
		}
		if (Util.isNotEmpty(ownBrId)) {			
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"ownBrId", ownBrId);
		}
		// 12個9為測試的團貸母戶編號，不呈現在grid中
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "grpCntrNo", "999999999999");
		
		Page<? extends GenericBean> page = service.findPage(C122M01E.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
	
	private void add_ploan_relationData(List<Map<String, Object>> list, int idx, String relationName,
			String relationTypeName, String relationIdNo, Map<String, String> code_desc_map){
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("oid", Util.trim(idx));
		map.put("relationName", Util.trim(relationName)); //同一關係人姓名
		map.put("relationTypeName", LMSUtil.getDesc(code_desc_map, Util.trim(relationTypeName)));
		map.put("relationIdNo", Util.trim(relationIdNo));
		list.add(map);
	}
	public CapMapGridResult queryApplyKindPE_relationData(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		
		String itemType = "0";
		C122M01B c122m01b = service.getC122M01B_byMainIdItemType(mainId, itemType);
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try{
			if(c122m01b!=null){
				Map<String, String> code_desc_map = codeTypeService.findByCodeType("ploan_relationTypeName");
				//===
				int idx = 0;
				if(Util.equals(UtilConstants.C122_ApplyKind.P, c122m01a.getApplyKind())
						|| Util.equals(UtilConstants.C122_ApplyKind.Q, c122m01a.getApplyKind())){
					if(Util.equals("PLOAN001", c122m01b.getJsonVoClass())){
						ObjectMapper objectMapper = new ObjectMapper();
						PLOAN001 ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN001.class);
						for(PLOAN001_loanInfo_relationObj relate_obj : ploan_obj.getLoanInfo().getRelationData()){
							if(Util.equals("0", relate_obj.getRelationTypeName())){
								add_ploan_relationData(list, idx++, relate_obj.getRelationName(), relate_obj.getRelationTypeName(), relate_obj.getRelationIdNo(), code_desc_map);	
							}							
						}
						for(PLOAN001_loanInfo_relationObj relate_obj : ploan_obj.getLoanInfo().getRelationData()){
							if(Util.equals("0", relate_obj.getRelationTypeName())){
							}else{
								add_ploan_relationData(list, idx++, relate_obj.getRelationName(), relate_obj.getRelationTypeName(), relate_obj.getRelationIdNo(), code_desc_map);	
							}							
						}
					}
				}else if(Util.equals(UtilConstants.C122_ApplyKind.E, c122m01a.getApplyKind())
						|| Util.equals(UtilConstants.C122_ApplyKind.F, c122m01a.getApplyKind())){
					if(Util.equals("PLOAN002", c122m01b.getJsonVoClass())){
						ObjectMapper objectMapper = new ObjectMapper();
						PLOAN002 ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN002.class);
						for(PLOAN002_loanInfo_relationObj relate_obj : ploan_obj.getLoanInfo().getRelationData()){
							if(Util.equals("0", relate_obj.getRelationTypeName())){
								add_ploan_relationData(list, idx++, relate_obj.getRelationName(), relate_obj.getRelationTypeName(), relate_obj.getRelationIdNo(), code_desc_map);
							}
						}
						for(PLOAN002_loanInfo_relationObj relate_obj : ploan_obj.getLoanInfo().getRelationData()){
							if(Util.equals("0", relate_obj.getRelationTypeName())){
							}else{
								add_ploan_relationData(list, idx++, relate_obj.getRelationName(), relate_obj.getRelationTypeName(), relate_obj.getRelationIdNo(), code_desc_map);
							}
						}
					}
				}
			}
		}catch (Exception e) {			
			throw new CapException("[mainId="+mainId+"]轉換資料發生錯誤", getClass());
		}
		return new CapMapGridResult(list, list.size());
	}

	private void add_ploan_servedData(List<Map<String, Object>> list, int idx, String companyRepresentativeType, String servedTitle,
			String companyName, String taxNo, String comment, Map<String, String> code_desc_map){
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("oid", Util.trim(idx));
		map.put("companyRepresentativeType", LMSUtil.getDesc(code_desc_map, Util.trim(companyRepresentativeType)));
		map.put("servedTitle", Util.trim(servedTitle));
		map.put("companyName", Util.trim(companyName));
		map.put("taxNo", Util.trim(taxNo));
		map.put("comment", Util.trim(comment));
		list.add(map);
	}
	public CapMapGridResult queryApplyKindPE_servedData(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		
		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		
		String itemType = "0";
		C122M01B c122m01b = service.getC122M01B_byMainIdItemType(mainId, itemType);
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try{
			if(c122m01b!=null){
				Map<String, String> code_desc_map = codeTypeService.findByCodeType("ploan_businessRepresentativeType");
				//===
				int idx = 0;
				if(Util.equals(UtilConstants.C122_ApplyKind.P, c122m01a.getApplyKind())
						|| Util.equals(UtilConstants.C122_ApplyKind.Q, c122m01a.getApplyKind())){
					if(Util.equals("PLOAN001", c122m01b.getJsonVoClass())){
						ObjectMapper objectMapper = new ObjectMapper();
						PLOAN001 ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN001.class);
						List<PLOAN001_loanInfo_servedObj> servedData_list = ploan_obj.getLoanInfo().getServedData();
						for(PLOAN001_loanInfo_servedObj obj : servedData_list){
							if(Util.equals("2", obj.getCompanyRepresentativeType())){
								add_ploan_servedData(list, idx++, obj.getCompanyRepresentativeType(), obj.getServedTitle(), obj.getCompanyName(), obj.getTaxNo(), obj.getComment(), code_desc_map);
							}
						}
						for(PLOAN001_loanInfo_servedObj obj : servedData_list){
							if(Util.equals("3", obj.getCompanyRepresentativeType())){
								add_ploan_servedData(list, idx++, obj.getCompanyRepresentativeType(), obj.getServedTitle(), obj.getCompanyName(), obj.getTaxNo(), obj.getComment(), code_desc_map);
							}
						}
						for(PLOAN001_loanInfo_servedObj obj : servedData_list){
							if(Util.equals("2", obj.getCompanyRepresentativeType()) || Util.equals("3", obj.getCompanyRepresentativeType())){
							}else{
								add_ploan_servedData(list, idx++, obj.getCompanyRepresentativeType(), obj.getServedTitle(), obj.getCompanyName(), obj.getTaxNo(), obj.getComment(), code_desc_map);
							}
						}
					}
				}else if(Util.equals(UtilConstants.C122_ApplyKind.E, c122m01a.getApplyKind())
						|| Util.equals(UtilConstants.C122_ApplyKind.F, c122m01a.getApplyKind())){
					if(Util.equals("PLOAN002", c122m01b.getJsonVoClass())){
						ObjectMapper objectMapper = new ObjectMapper();
						PLOAN002 ploan_obj = objectMapper.readValue(JSONObject.fromObject(c122m01b.getJsonData()).toString(), PLOAN002.class);
						List<PLOAN002_loanInfo_servedObj> servedData_list = ploan_obj.getLoanInfo().getServedData();
						for(PLOAN002_loanInfo_servedObj obj : servedData_list){
							if(Util.equals("2", obj.getCompanyRepresentativeType())){
								add_ploan_servedData(list, idx++, obj.getCompanyRepresentativeType(), obj.getServedTitle(), obj.getCompanyName(), obj.getTaxNo(), obj.getComment(), code_desc_map);
							}
						}
						for(PLOAN002_loanInfo_servedObj obj : servedData_list){
							if(Util.equals("3", obj.getCompanyRepresentativeType())){
								add_ploan_servedData(list, idx++, obj.getCompanyRepresentativeType(), obj.getServedTitle(), obj.getCompanyName(), obj.getTaxNo(), obj.getComment(), code_desc_map);
							}
						}
						for(PLOAN002_loanInfo_servedObj obj : servedData_list){
							if(Util.equals("2", obj.getCompanyRepresentativeType()) || Util.equals("3", obj.getCompanyRepresentativeType())){
							}else{
								add_ploan_servedData(list, idx++, obj.getCompanyRepresentativeType(), obj.getServedTitle(), obj.getCompanyName(), obj.getTaxNo(), obj.getComment(), code_desc_map);
							}
						}
					}
				}
			}
		}catch (Exception e) {			
			throw new CapException("[mainId="+mainId+"]轉換資料發生錯誤", getClass());
		}
		return new CapMapGridResult(list, list.size());
	}
	
	public CapGridResult queryApplyKindPE_relateCase(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String applyKind = Util.trim(params.getString("applyKind"));
		String ploanCaseId = Util.trim(params.getString("ploanCaseId"));
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "applyKind", applyKind);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId", ploanCaseId);
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "ploanCaseNo", ploanCaseId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		
		// 取得資料
		Page<C122M01A> page = service.getC122M01A(pageSetting);			
		/*
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for(C122M01A model : page.getContent()){
			Map<String, Object> row = new HashMap<String, Object>();
			row.put("custId", model.getCustId());
			row.put("custName", model.getCustName());
			row.put("ploanCasePos", model.getPloanCasePos());
		}
		return new CapMapGridResult(list, list.size());*/
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("ploanCasePos", new CodeTypeFormatter(codeTypeService, "ploan_casePos"));
		// 身分別
		formatter.put("ploanCasePosCode", new IBeanFormatter() {
			private static final long serialVersionUID = 1L;

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				return meta.getPloanCasePos();
			}
		});
		final Map<String, String> _DocStatusNewDescMap = service.get_DocStatusNewDescMap();
		formatter.put("flowdocStatus", new IBeanFormatter() {
			private static final long serialVersionUID = 1L;
			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				List<C122S01H> c122s01hList = service.findC122S01H(meta.getMainId(),
						UtilConstants.C122s01h_flowId.借保人資料);
				String flowdocStatus = "";
				if (c122s01hList.size() >0) {
					C122S01H c122s01h = c122s01hList.get(0);
					flowdocStatus = Util.trim(c122s01h.getFlowdocStatus());
					flowdocStatus = LMSUtil.getDesc(_DocStatusNewDescMap, flowdocStatus);
				}
				return flowdocStatus;
			}
		});
		formatter.put("remainId", new IBeanFormatter() {
			private static final long serialVersionUID = 1L;
			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				List<C122S01H> c122s01hList = service.findC122S01H(meta.getMainId(),
						UtilConstants.C122s01h_flowId.借保人資料);
				String remainId = "";
				if (c122s01hList.size() >0) {
					C122S01H c122s01h = c122s01hList.get(0);
					remainId = c122s01h.getRemainId();
				}
				return remainId;
			}
		});
		formatter.put("flowId", new IBeanFormatter() {
			private static final long serialVersionUID = 1L;
			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				C122M01A meta = (C122M01A) in;
				List<C122S01H> c122s01hList = service.findC122S01H(meta.getMainId(),
						UtilConstants.C122s01h_flowId.借保人資料);
				String flowId = "";
				if (c122s01hList.size() >0) {
					C122S01H c122s01h = c122s01hList.get(0);
					flowId = c122s01h.getFlowId();
				}
				return flowId;
			}
		});
		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	public CapMapGridResult queryApplyKindPE_L140M01A(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for(Map<String, Object> rtnRow : eloandbBASEService.query_cntrNo_for_C122M01A_ApplyKindPE(mainId)){
			String oid = Util.trim(MapUtils.getString(rtnRow, "OID"));
			String tabMainId = Util.trim(MapUtils.getString(rtnRow, "TABMAINID"));
			String cntrNo = Util.trim(MapUtils.getString(rtnRow, "CNTRNO")); 
			String caseMainId = Util.trim(MapUtils.getString(rtnRow, "CASEMAINID"));
			String caseNo = Util.toSemiCharString(Util.trim(MapUtils.getString(rtnRow, "CASENO_RAW")));
			String caseDate = Util.trim(TWNDate.toAD((Date)MapUtils.getObject(rtnRow, "CASEDATE")));
			String endDate = Util.trim(TWNDate.toAD((Date)MapUtils.getObject(rtnRow, "ENDDATE")));
			String docStatus = Util.toSemiCharString(Util.trim(MapUtils.getString(rtnRow, "DOCSTATUS")));
			String chkYN = CreditDocStatusEnum.海外_已核准.getCode().equals(docStatus)?"V":"△";
			
			Map<String, Object> r = new HashMap<String, Object>();
			r.put("cntrNo", cntrNo);
			r.put("caseNo", caseNo);
			r.put("caseDate", caseDate);
			r.put("endDate", endDate);
			r.put("chkYN", chkYN);
			r.put("tabMainId", tabMainId);
			r.put("caseMainId", caseMainId);
			list.add(r);
		}
		return new CapMapGridResult(list, list.size());
	}

	public CapGridResult queryC122M01C(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString("mainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);		

		Page<C122M01C> page = service.getC122M01C(pageSetting);	
		
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("brNoBef", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName));
		formatter.put("brNoAft", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName));
		formatter.put("creator", new UserNameFormatter(userInfoService));
		formatter.put("createTime", new ADDateTimeFormatter("yyyy-MM-dd HH:mm"));
		
		// 第三個參數為formatting
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}
}
