/* 
 * CLS1151S03Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.cls.common.ClsUtil;

import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 個金額度明細表 - 擔保品
 * </pre>
 * 
 * @since 2012/12/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/7,REX,new
 *          </ul>
 */
public class CLS1151S03Panel extends Panel {

	private static final long serialVersionUID = 1L;

	/**
	 * @param id
	 */
	public CLS1151S03Panel(String id) {
		super(id);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		boolean rebuild_cost_since_1090101 = false;
		if(LMSUtil.cmpDate(CapDate.getCurrentTimestamp(), ">=", CapDate.parseDate(ClsUtil.REBUILDCOST_V1090101))){
			rebuild_cost_since_1090101 = true;
		}
		/*
		  	因可能在2019-12-31之前簽案，但要到2020-01之後才撥款
		  	顯示 btn，讓分行經辦有選擇的機會，可以先選擇用 109-01-01 後生效的版本，去計算「重置成本」 
		 */
		model.addAttribute("showUtil20191231", !rebuild_cost_since_1090101);
	}
}
