/* 
 * LMS1855GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.grid;

import java.util.List;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.dao.L185M01ADao;
import com.mega.eloan.lms.lrs.service.LMS1855Service;
import com.mega.eloan.lms.model.L185M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 覆審管理報表
 * </pre>
 * 
 * 
 * @since 2012/2/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/15,jessica,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1855gridhandler")
public class LMS1855GridHandler extends AbstractGridHandler {

	@Resource
	BranchService branch;

	@Resource
	LMS1855Service service;

	@Resource
	L185M01ADao dao;

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult queryL185M01a(ISearch pageSetting,
			PageParameters params) throws CapException {

		// "1"= 逾期未覆審名單
		// "2"= 企金戶未出現於覆審名單
		// "3"= 撥貸後半年內辦理覆審檢核表
		// "4"= 授信覆審明細檢核表

		String searchAction = Util.nullToSpace(params.getString("searchAction"));
		String brNos = Util.nullToSpace(params.getString("brNos"));
		String[] brNosTemp = brNos.split("\\^");
		if("3".equals(searchAction)){
			// 產生日期資料基準日 (日期起日)
			// 產生日期資料基準日(起日)
			String searchStartCreateDate =  Util.nullToSpace(params.getString("searchStartCreateDate"));
			String searchEndCreateDate =  Util.nullToSpace(params.getString("searchEndCreateDate"));
			if("".equals(searchStartCreateDate)){
				searchStartCreateDate =  TWNDate.toAD(LMSUtil.getExMonthFirstDay(-1)).substring(0, 7);
			}
			if("".equals(searchEndCreateDate)){
				searchEndCreateDate =  TWNDate.toAD(LMSUtil.getExMonthFirstDay(-1)).substring(0, 7);
			}
			searchEndCreateDate = searchEndCreateDate + "-" + CapDate.getDayOfMonth(searchEndCreateDate.split("-")[0], searchEndCreateDate.split("-")[1]);
			searchStartCreateDate = searchStartCreateDate + "-01";
			Object[] reason = {searchStartCreateDate,searchEndCreateDate};
			// 資料基準日 (YYYY-MM-01)
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "createTime",
					reason);
		}else{
			// 產生日期資料基準日 (日期起日)
			// 產生日期資料基準日(起日)
			String searchDate =  Util.nullToSpace(params.getString("searchDataDate"));
			if("".equals(searchDate)){
				searchDate =  TWNDate.toAD(LMSUtil.getExMonthFirstDay(-1)).substring(0, 7);
			}
			String searchEDate = searchDate + "-" + CapDate.getDayOfMonth(searchDate.split("-")[0], searchDate.split("-")[1]);
			searchDate = searchDate + "-01";
			Object[] reason = {searchDate,searchEDate};
			// 資料基準日 (YYYY-MM-01)
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "dataDate",
					reason);
		}
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 授權(共同)
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l185a01a.authUnit", user.getUnitNo());
		// 報表種類(共同)
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptType",
				searchAction);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addSearchModeParameters(SearchMode.IN, "ownBrId", brNosTemp);

		pageSetting.addOrderBy("createTime",true);
		pageSetting.addOrderBy("branchId",false);
		Page page = service.findPage(L185M01A.class, pageSetting);

		List<L185M01A> list = page.getContent();
		for (L185M01A model : list) {
			model.setOwnBrId(model.getOwnBrId() + Util.trim(branch.getBranchName(model.getOwnBrId())));
			model.setBranchId(model.getBranchId() + Util.trim(branch.getBranchName(model.getBranchId())));
		}
		return new CapGridResult(list, page.getTotalRow());

		// 加入格式化
		// CapGridResult result = new CapGridResult(page.getContent(),
		// page.getTotalRow());
		// Map<String, IFormatter> dataReformatter = new HashMap<String,
		// IFormatter>();
		// 1.分行/自辦 2.區域營運中心 3.徵信處
		// dataReformatter.put("unitType", new I18NFormatter(getComponent(),
		// "unitType."));

	}

	/**
	 * 查詢Grid 覆審管理報表(歷史資料)
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult queryL185M01aH(ISearch pageSetting, PageParameters params) throws CapException {

		String searchAction = Util
				.nullToSpace(params.getString("searchAction"));
		String brNos = Util.nullToSpace(params.getString("brNos"));
		String[] brNosTemp = brNos.split("\\^");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 授權(共同)
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l185a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptType",
				searchAction);
		pageSetting.addSearchModeParameters(SearchMode.IN, "ownBrId", brNosTemp);
		String startDate = Util.nullToSpace(params.getString("startDate"));
		String endDate = Util.nullToSpace(params.getString("endDate"));
		if (!"".equals(startDate) || !"".equals(endDate)) {
			startDate = startDate + "-01";
			endDate = endDate + "-" + CapDate.getDayOfMonth(endDate.split("-")[0], endDate.split("-")[1]);
			Object[] reason = { startDate, endDate };
			if("3".equals(searchAction)){
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "createTime",
						reason);
			}else{
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "dataDate",reason);
			}
		}
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		pageSetting.addOrderBy("createTime",true);
		pageSetting.addOrderBy("branchId",false);

		Page page = service.findPage(L185M01A.class, pageSetting);
		List<L185M01A> list = page.getContent();
		for (L185M01A model : list) {
//			model.setOwnBrId(model.getOwnBrId() + user.getUnitCName());
			model.setOwnBrId(model.getOwnBrId() + Util.trim(branch.getBranchName(model.getOwnBrId())));
			model.setBranchId(model.getBranchId() + Util.trim(branch.getBranchName(model.getBranchId())));
		}
		return new CapGridResult(list, page.getTotalRow());
	}
}
