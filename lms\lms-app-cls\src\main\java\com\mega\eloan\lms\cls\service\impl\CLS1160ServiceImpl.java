package com.mega.eloan.lms.cls.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.MISRows;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.base.service.CLS8011Service;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.base.service.ProdService.ProdKindEnum;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1160Service;
import com.mega.eloan.lms.dao.C102M01ADao;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01CDao;
import com.mega.eloan.lms.dao.C120S01DDao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C120S01HDao;
import com.mega.eloan.lms.dao.C120S01IDao;
import com.mega.eloan.lms.dao.C120S01JDao;
import com.mega.eloan.lms.dao.C120S01QDao;
import com.mega.eloan.lms.dao.C160A01ADao;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.C160M01CDao;
import com.mega.eloan.lms.dao.C160M01DDao;
import com.mega.eloan.lms.dao.C160M01EDao;
import com.mega.eloan.lms.dao.C160M01FDao;
import com.mega.eloan.lms.dao.C160M02ADao;
import com.mega.eloan.lms.dao.C160M03ADao;
import com.mega.eloan.lms.dao.C160S01ADao;
import com.mega.eloan.lms.dao.C160S01BDao;
import com.mega.eloan.lms.dao.C160S01CDao;
import com.mega.eloan.lms.dao.C160S01DDao;
import com.mega.eloan.lms.dao.C160S01EDao;
import com.mega.eloan.lms.dao.C160S01FDao;
import com.mega.eloan.lms.dao.C160S01GDao;
import com.mega.eloan.lms.dao.C160S03ADao;
import com.mega.eloan.lms.dao.C900M01DDao;
import com.mega.eloan.lms.dao.C900M01FDao;
import com.mega.eloan.lms.dao.C900M01GDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01GDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01EDao;
import com.mega.eloan.lms.dao.L140M01LDao;
import com.mega.eloan.lms.dao.L140M01MDao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dao.L140M01RDao;
import com.mega.eloan.lms.dao.L140M03ADao;
import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L140S02BDao;
import com.mega.eloan.lms.dao.L140S02CDao;
import com.mega.eloan.lms.dao.L140S02DDao;
import com.mega.eloan.lms.dao.L140S02EDao;
import com.mega.eloan.lms.dao.L140S02FDao;
import com.mega.eloan.lms.dao.L140S02GDao;
import com.mega.eloan.lms.dao.L140S02HDao;
import com.mega.eloan.lms.dao.L140S02IDao;
import com.mega.eloan.lms.dao.L140S02JDao;
import com.mega.eloan.lms.dao.L140S02LDao;
import com.mega.eloan.lms.dao.L141M01ADao;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;
import com.mega.eloan.lms.mfaloan.bean.DW_RKADJUST;
import com.mega.eloan.lms.mfaloan.bean.DW_RKAPPLICANT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKCNTRNO;
import com.mega.eloan.lms.mfaloan.bean.DW_RKCREDIT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKJCIC;
import com.mega.eloan.lms.mfaloan.bean.DW_RKPROJECT;
import com.mega.eloan.lms.mfaloan.bean.DW_RKSCORE;
import com.mega.eloan.lms.mfaloan.bean.ELF431;
import com.mega.eloan.lms.mfaloan.bean.ELF447N;
import com.mega.eloan.lms.mfaloan.bean.ELF453;
import com.mega.eloan.lms.mfaloan.bean.ELF461;
import com.mega.eloan.lms.mfaloan.bean.ELF500;
import com.mega.eloan.lms.mfaloan.bean.ELF501;
import com.mega.eloan.lms.mfaloan.bean.ELF502;
import com.mega.eloan.lms.mfaloan.bean.ELF504;
import com.mega.eloan.lms.mfaloan.bean.ELF508;
import com.mega.eloan.lms.mfaloan.bean.ELF509;
import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.bean.ELF675;
import com.mega.eloan.lms.mfaloan.bean.ELLNGTEE;
import com.mega.eloan.lms.mfaloan.bean.IQUOTAPP;
import com.mega.eloan.lms.mfaloan.bean.LNF010;
import com.mega.eloan.lms.mfaloan.bean.LNF130;
import com.mega.eloan.lms.mfaloan.bean.LNF13E;
import com.mega.eloan.lms.mfaloan.bean.LNF164;
import com.mega.eloan.lms.mfaloan.bean.LNF195;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.bean.PEXTLIMT;
import com.mega.eloan.lms.mfaloan.bean.PQUOTNF;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.bean.PTEAMDTL;
import com.mega.eloan.lms.mfaloan.bean.PTEMAPPN;
import com.mega.eloan.lms.mfaloan.bean.QUOTAINF;
import com.mega.eloan.lms.mfaloan.bean.QUOTSUB;
import com.mega.eloan.lms.mfaloan.bean.QUOTUNIO;
import com.mega.eloan.lms.mfaloan.bean.STUDATA;
import com.mega.eloan.lms.mfaloan.service.LNLNF130Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisDRCXBNMService;
import com.mega.eloan.lms.mfaloan.service.MisELF386Service;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;
import com.mega.eloan.lms.mfaloan.service.MisELF501Service;
import com.mega.eloan.lms.mfaloan.service.MisELF504Service;
import com.mega.eloan.lms.mfaloan.service.MisELF509Service;
import com.mega.eloan.lms.mfaloan.service.MisELF517Service;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisIquotappService;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisQuotsubService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C102M01A;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01H;
import com.mega.eloan.lms.model.C120S01I;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C160A01A;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C160M01C;
import com.mega.eloan.lms.model.C160M01D;
import com.mega.eloan.lms.model.C160M01E;
import com.mega.eloan.lms.model.C160M01F;
import com.mega.eloan.lms.model.C160M02A;
import com.mega.eloan.lms.model.C160M03A;
import com.mega.eloan.lms.model.C160S01A;
import com.mega.eloan.lms.model.C160S01B;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C160S01E;
import com.mega.eloan.lms.model.C160S01F;
import com.mega.eloan.lms.model.C160S01G;
import com.mega.eloan.lms.model.C160S03A;
import com.mega.eloan.lms.model.C801M01A;
import com.mega.eloan.lms.model.C801M01B;
import com.mega.eloan.lms.model.C900M01F;
import com.mega.eloan.lms.model.C900M01G;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140M03A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02B;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.eloan.lms.model.L140S02D;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.eloan.lms.model.L140S02F;
import com.mega.eloan.lms.model.L140S02I;
import com.mega.eloan.lms.model.L140S02J;
import com.mega.eloan.lms.model.L140S02L;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 動用審核表ServiceImpl
 * </pre>
 * 
 * @since 2012/12/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/21,Fantasy,new
 *          <li>2013/06/25,Fantasy,fix save C160M01A update delete time
 *          <li>2013/08/08,Rex,#648修改團貸上傳LNF010問題
 *          </ul>
 */

@Service
@Qualifier("CLS1160Service")
public class CLS1160ServiceImpl extends AbstractEloandbJdbc implements
		CLS1160Service {
	//TODO 此 class 的 method(EX: findListByMainId) 被 CLS1161ServiceImpl 繼承
	//當 CLS1161FormHandler 去 call cls1161Service 時
	//某些程式, 沒有被定義在 CLS1161Service, 而是被定義在 CLS1160Service
	private static final Logger logger = LoggerFactory
			.getLogger(CLS1160ServiceImpl.class);

	private static final String BR_901 = "901";
	
	@Resource
	L120M01ADao l120m01aDao;
	@Resource
	L120M01GDao l120m01gDao;
	@Resource
	L140M01ADao l140m01aDao;
	@Resource
	L141M01ADao l141m01aDao;
	@Resource
	L140M01ODao l140m01oDao;
	@Resource
	L140S01ADao l140s01aDao;
	@Resource
	L140S02ADao l140s02aDao;
	@Resource
	L140S02GDao l140s02gDao;
	@Resource
	L140S02HDao l140s02hDao;
	@Resource
	C102M01ADao c102m01aDao;
	@Resource
	C160A01ADao c160a01aDao;
	@Resource
	C160M01ADao c160m01aDao;
	@Resource
	C160M01BDao c160m01bDao;
	@Resource
	C160M01CDao c160m01cDao;
	@Resource
	C160M01DDao c160m01dDao;
	@Resource
	C160M01EDao c160m01eDao;
	@Resource
	C160M01FDao c160m01fDao;
	@Resource
	C160S01ADao c160s01aDao;
	@Resource
	C160M02ADao c160m02aDao;
	
	@Resource
	C160M03ADao c160m03aDao;
	
	@Resource
	C160S01BDao c160s01bDao;
	@Resource
	C160S01CDao c160s01cDao;
	@Resource
	C160S01DDao c160s01dDao;
	@Resource
	C160S01EDao c160s01eDao;
	@Resource
	C160S01FDao c160s01fDao;
	@Resource
	C160S01GDao c160s01gDao;
	
	@Resource
	C160S03ADao c160s03aDao;
	
	@Resource
	L140M01LDao l140m01lDao;
	@Resource
	L140M01MDao l140m01mDao;
	@Resource
	L140M01EDao l140m01eDao;
	@Resource
	L140M03ADao l140m03aDao;
	@Resource
	L140S02BDao l140s02bDao;
	@Resource
	L140S02CDao l140s02cDao;
	@Resource
	L140S02DDao l140s02dDao;
	@Resource
	L140S02EDao l140s02eDao;
	@Resource
	L140S02FDao l140s02fDao;
	@Resource
	L140S02IDao l140s02iDao;
	@Resource
	L140S02JDao l140s02jDao;
	@Resource
	C120M01ADao c120m01aDao;
	@Resource
	C120S01ADao c120s01aDao;	
	@Resource
	C120S01BDao c120s01bDao;
	@Resource
	C120S01CDao c120s01cDao;
	@Resource
	C120S01DDao c120s01dDao;
	@Resource
	C120S01EDao c120s01eDao;
	@Resource
	C120S01HDao c120s01hDao;
	@Resource
	C120S01IDao c120s01iDao;
	@Resource
	C120S01JDao c120s01jDao;
	@Resource
	C120S01QDao c120s01qDao;
	@Resource
	C900M01DDao c900m01dDao;
	@Resource
	C900M01FDao c900m01fDao;
	@Resource
	C900M01GDao c900m01gDao;

	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;

	@Resource
	FlowService flowService;

	@Resource
	MisdbBASEService misdbBaseService;

	@Resource
	DwdbBASEService dwdbBaseService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	EloandbcmsBASEService eloandbcmsbaseservice;

	@Resource
	BranchService branchService;

	@Resource
	MisDRCXBNMService misdrcxbnmseervice;

	@Resource
	MisQuotsubService misquotsubseervice;
	
	@Resource
	MisELF386Service misELF386Service;
	@Resource
	MisELF501Service mislf501seervic;
	@Resource
	MisELF500Service mislf500seervic;
	
	@Resource
	MisELF504Service misELF504Service;

	@Resource
	LNLNF130Service lnLNF130Service;
	
	@Resource
	MisIquotappService misiquotappservice;
	@Resource
	ProdService prodservice;
	@Resource
	MisGrpcmpService misgrpcmpservice;
	@Resource
	MisCustdataService misCustdataService;
	@Resource
	LMSService lmsservice;
	@Resource
	L140M01RDao l140m01rDao;
	@Resource
	CLS8011Service cls8011Service;
	@Resource
	MisELF509Service mislf509seervic;
	@Resource
	MisELF517Service misElf517Service;
	@Resource
	CLS1131Service cls1131Service;
	@Resource
	CLSService clsService;

	@Resource
	MisMISLN20Service misMISLN20Service;
	@Resource
	MisLNF030Service misLNF030Service;
	
	@Resource
	L140S02LDao l140s02lDao;
	
	@Override
	public ISearch createSearchTemplete() {
		if (c160m01aDao != null) {
			return c160m01aDao.createSearchTemplete();
		} else {
			return null;
		}
	}

	@Override
	public List<? extends GenericBean> findList(Class<?> clazz, ISearch search) {
		if (clazz == C160A01A.class) {
			return c160a01aDao.find(search);
		} else if (clazz == C160M01A.class) {
			return c160m01aDao.find(search);
		} else if (clazz == C160M01B.class) {
			return c160m01bDao.find(search);
		} else if (clazz == C160M01C.class) {
			return c160m01cDao.find(search);
		} else if (clazz == C160M01D.class) {
			return c160m01dDao.find(search);
		} else if (clazz == C160M01E.class) {
			return c160m01eDao.find(search);
		} else if (clazz == C160M01F.class) {
			return c160m01fDao.find(search);
		} else if (clazz == C160S01A.class) {
			return c160s01aDao.find(search);
		} else if (clazz == C160S01B.class) {
			return c160s01bDao.find(search);
		} else if (clazz == C160S01C.class) {
			return c160s01cDao.find(search);
		} else if (clazz == C160S01D.class) {
			return c160s01dDao.find(search);
		} else if (clazz == C160S01E.class) {
			return c160s01eDao.find(search);
		} else if (clazz == C160S01F.class) {
			return c160s01fDao.find(search);
		} else if (clazz == C160S01G.class) {
			return c160s01gDao.find(search);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}
					model.set("updater", user.getUserId());
					model.set("updateTime", CapDate.getCurrentTimestamp());
				} catch (CapException e) {
					logger.error("[CLS1160ServiceImpl.save]", e);
				}

				if (model instanceof C160A01A) {
					c160a01aDao.save(((C160A01A) model));
				} else if (model instanceof C160M01A) {
					C160M01A c160m01a = (C160M01A) model;
					if (Util.isEmpty(c160m01a.getOid())) {
						c160m01aDao.save(c160m01a);
						// 起案
						flowService.start(ClsConstants.Flow.動審表,
								c160m01a.getOid(), user.getUserId(),
								user.getUnitNo());
					} else {
						c160m01a.setDeletedTime(null);
						c160m01aDao.save(c160m01a);
					}
				} else if (model instanceof C160M01B) {
					c160m01bDao.save(((C160M01B) model));
				} else if (model instanceof C160M01C) {
					c160m01cDao.save(((C160M01C) model));
				} else if (model instanceof C160M01D) {
					c160m01dDao.save(((C160M01D) model));
				} else if (model instanceof C160M01E) {
					c160m01eDao.save(((C160M01E) model));
				} else if (model instanceof C160M01F) {
					c160m01fDao.save(((C160M01F) model));
				} else if (model instanceof C160S01A) {
					c160s01aDao.save(((C160S01A) model));
				} else if (model instanceof C160M02A) {
					c160m02aDao.save(((C160M02A) model));
				} else if (model instanceof C160M03A) {
					c160m03aDao.save(((C160M03A) model));
				} else if (model instanceof C160S01B) {
					c160s01bDao.save(((C160S01B) model));
				} else if (model instanceof C160S01C) {
					c160s01cDao.save(((C160S01C) model));
				} else if (model instanceof C160S01D) {
					c160s01dDao.save(((C160S01D) model));
				} else if (model instanceof C160S01E) {
					c160s01eDao.save(((C160S01E) model));
				} else if (model instanceof C160S01F) {
					c160s01fDao.save(((C160S01F) model));
				} else if (model instanceof C160S01G) {
					c160s01gDao.save(((C160S01G) model));
				} else if (model instanceof C160S03A) {
					c160s03aDao.save(((C160S03A) model));
				} else if (model instanceof L140M01R) {
					l140m01rDao.save(((L140M01R) model));				
				} else if (model instanceof C120M01A) {					
					c120m01aDao.save(((C120M01A) model));
				} else if (model instanceof C120S01B) {					
					c120s01bDao.save(((C120S01B) model));
				} else if (model instanceof C120S01E) {
					c120s01eDao.save(((C120S01E) model));
				} else if (model instanceof C120S01Q) {
					c120s01qDao.save(((C120S01Q) model));
				} else if (model instanceof C900M01G) {
					c900m01gDao.save(((C900M01G) model));
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model instanceof C160A01A) {
				c160a01aDao.delete(((C160A01A) model));
			} else if (model instanceof C160M01A) {
				c160m01aDao.delete(((C160M01A) model));
			} else if (model instanceof C160M01B) {
				c160m01bDao.delete(((C160M01B) model));
			} else if (model instanceof C160M01C) {
				c160m01cDao.delete(((C160M01C) model));
			} else if (model instanceof C160M01D) {
				c160m01dDao.delete(((C160M01D) model));
			} else if (model instanceof C160M01E) {
				c160m01eDao.delete(((C160M01E) model));
			} else if (model instanceof C160M01F) {
				c160m01fDao.delete(((C160M01F) model));
			} else if (model instanceof C160S01A) {
				c160s01aDao.delete(((C160S01A) model));
			} else if (model instanceof C160S01B) {
				c160s01bDao.delete(((C160S01B) model));
			} else if (model instanceof C160S01C) {
				c160s01cDao.delete(((C160S01C) model));
			} else if (model instanceof C160S01D) {
				c160s01dDao.delete(((C160S01D) model));
			} else if (model instanceof C160S01E) {
				c160s01eDao.delete(((C160S01E) model));
			} else if (model instanceof C160S01F) {
				c160s01fDao.delete(((C160S01F) model));
			} else if (model instanceof C160S01G) {
				c160s01gDao.delete(((C160S01G) model));
			} else if (model instanceof C160S03A) {
				c160s03aDao.delete(((C160S03A) model));
			} else if (model instanceof L140M01R) {
				l140m01rDao.delete(((L140M01R) model));
			} else if (model instanceof C900M01G) {
				c900m01gDao.delete(((C900M01G) model));
			} else if (model instanceof C120M01A) {
				c120m01aDao.delete(((C120M01A) model));
			} else if (model instanceof C120S01A) {
				c120s01aDao.delete(((C120S01A) model));
			} else if (model instanceof C120S01B) {
				c120s01bDao.delete(((C120S01B) model));
			} else if (model instanceof C120S01C) {
				c120s01cDao.delete(((C120S01C) model));
			} else if (model instanceof C120S01D) {
				c120s01dDao.delete(((C120S01D) model));
			} else if (model instanceof C120S01E) {
				c120s01eDao.delete(((C120S01E) model));
			} else if (model instanceof C120S01H) {
				c120s01hDao.delete(((C120S01H) model));
			} else if (model instanceof C120S01I) {
				c120s01iDao.delete(((C120S01I) model));
			} else if (model instanceof C120S01Q) {
				c120s01qDao.delete(((C120S01Q) model));
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C160A01A.class) {
			return c160a01aDao.findPage(search);
		} else if (clazz == C160M01A.class) {
			return c160m01aDao.findPage(search);
		} else if (clazz == C160M01B.class) {
			return c160m01bDao.findPage(search);
		} else if (clazz == C160M01C.class) {
			return c160m01cDao.findPage(search);
		} else if (clazz == C160M01D.class) {
			return c160m01dDao.findPage(search);
		} else if (clazz == C160M01E.class) {
			return c160m01fDao.findPage(search);
		} else if (clazz == C160M01F.class) {
			return c160m01dDao.findPage(search);
		} else if (clazz == C160S01A.class) {
			return c160s01aDao.findPage(search);
		} else if (clazz == C160M02A.class) {
			return c160m02aDao.findPage(search);
		} else if (clazz == C160M03A.class) {
			return c160m03aDao.findPage(search);	
		} else if (clazz == C160S01B.class) {
			return c160s01bDao.findPage(search);
		} else if (clazz == C160S01C.class) {
			return c160s01cDao.findPage(search);
		} else if (clazz == C160S01D.class) {
			return c160s01dDao.findPage(search);
		} else if (clazz == C160S01E.class) {
			return c160s01eDao.findPage(search);
		} else if (clazz == C160S01F.class) {
			return c160s01fDao.findPage(search);
		} else if (clazz == C160S01G.class) {
			return c160s01gDao.findPage(search);
		} else if (clazz == C160S03A.class) {
			return c160s03aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return (T) findModelByOid(clazz, oid, false);
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T extends GenericBean> T findModelByOid(Class<?> clazz, String oid,
			boolean create) {
		if (clazz == C160A01A.class) {
			C160A01A model = Util.isEmpty(oid) ? null : c160a01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160A01A() : model);
		} else if (clazz == C160M01A.class) {
			C160M01A model = Util.isEmpty(oid) ? null : c160m01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160M01A() : model);
		} else if (clazz == C160M01B.class) {
			C160M01B model = Util.isEmpty(oid) ? null : c160m01bDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160M01B() : model);
		} else if (clazz == C160M01C.class) {
			C160M01C model = Util.isEmpty(oid) ? null : c160m01cDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160M01C() : model);
		} else if (clazz == C160M01D.class) {
			C160M01D model = Util.isEmpty(oid) ? null : c160m01dDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160M01D() : model);
		} else if (clazz == C160M01E.class) {
			C160M01E model = Util.isEmpty(oid) ? null : c160m01eDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160M01E() : model);
		} else if (clazz == C160M01F.class) {
			C160M01F model = Util.isEmpty(oid) ? null : c160m01fDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160M01F() : model);
		} else if (clazz == C160S01A.class) {
			C160S01A model = Util.isEmpty(oid) ? null : c160s01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S01A() : model);
		} else if (clazz == C160M02A.class) {
			C160M02A model = Util.isEmpty(oid) ? null : c160m02aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S01A() : model);
		} else if (clazz == C160M03A.class) {
			C160M03A model = Util.isEmpty(oid) ? null : c160m03aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160M03A() : model);
		} else if (clazz == C160S01B.class) {
			C160S01B model = Util.isEmpty(oid) ? null : c160s01bDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S01B() : model);
		} else if (clazz == C160S01C.class) {
			C160S01C model = Util.isEmpty(oid) ? null : c160s01cDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S01C() : model);
		} else if (clazz == C160S01D.class) {
			C160S01D model = Util.isEmpty(oid) ? null : c160s01dDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S01D() : model);
		} else if (clazz == C160S01E.class) {
			C160S01E model = Util.isEmpty(oid) ? null : c160s01eDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S01E() : model);
		} else if (clazz == C160S01F.class) {
			C160S01F model = Util.isEmpty(oid) ? null : c160s01fDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S01F() : model);
		} else if (clazz == C160S01G.class) {
			C160S01G model = Util.isEmpty(oid) ? null : c160s01gDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S01G() : model);
		} else if (clazz == C160S03A.class) {
			C160S03A model = Util.isEmpty(oid) ? null : c160s03aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160S03A() : model);
		}
		// 案件簽報書
		else if (clazz == L120M01A.class) {
			L120M01A model = Util.isEmpty(oid) ? null : l120m01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new L120M01A() : model);
		}
		//
		else if (clazz == L140M01A.class) {
			L140M01A model = Util.isEmpty(oid) ? null : l140m01aDao
					.findByOid(oid);
			return (T) (create && model == null ? new L140M01A() : model);
		}else if (clazz == C160M02A.class){
			C160M02A model = Util.isEmpty(oid) ? null : c160m02aDao
					.findByOid(oid);
			return (T) (create && model == null ? new C160M02A() : model);
		}

		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == C160A01A.class) {
			return c160a01aDao.findByMainId(mainId);
			// } else if (clazz == C160M01A.class) {
			// return c160m01aDao.findByMainId(mainId);
		} else if (clazz == C160M01B.class) {
			return c160m01bDao.findByMainId(mainId);
		} else if (clazz == C160M01C.class) {
			return c160m01cDao.findByMainId(mainId);
		} else if (clazz == C160M01D.class) {
			return c160m01dDao.findByMainId(mainId);
		} else if (clazz == C160M01E.class) {
			return c160m01eDao.findByMainId(mainId);
		} else if (clazz == C160M01F.class) {
			return c160m01fDao.findByMainId(mainId);
		} else if (clazz == C160S01A.class) {
			return c160s01aDao.findByMainId(mainId);
		} else if (clazz == C160S01B.class) {
			return c160s01bDao.findByMainId(mainId);
		} else if (clazz == C160S01C.class) {
			return c160s01cDao.findByMainId(mainId);
		} else if (clazz == C160S01D.class) {
			return c160s01dDao.findByMainId(mainId);
		} else if (clazz == C160S01E.class) {
			return c160s01eDao.findByMainId(mainId);
		} else if (clazz == C160S01F.class) {
			return c160s01fDao.findByMainId(mainId);
		} else if (clazz == C160S01G.class) {
			return c160s01gDao.findByMainId(mainId);
		}

		return null;
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T extends GenericBean> T findModelByMainId(Class<?> clazz,
			String mainId) {
		if (clazz == C160M01A.class) {
			return (T) c160m01aDao.findByMainId(mainId);
		} else if (clazz == C160M01B.class) {
			// c160m01bDao.find
			// return (T) (create && model == null ? new C160M01B() : model);
		} else if (clazz == C160M01C.class) {
			// C160M01C model = Util.isEmpty(oid) ? null : c160m01cDao
			// .findByOid(oid);
			// return (T) (create && model == null ? new C160M01C() : model);
		} else if (clazz == C160M01D.class) {
			return (T) c160m01dDao.findByUniqueKey(mainId);
		} else if (clazz == C160M01F.class) {
			C160M01F model = c160m01fDao.findByUniqueKey(mainId);
			return (T) (model == null ? new C160M01F() : model);
		} else if (clazz == C160M03A.class) {
				return (T) c160m03aDao.findByMainId(mainId);
		}
		// 案件簽報書
		else if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByMainId(mainId);
		}
		// 額度明細表
		else if (clazz == L140M01A.class) {
			return (T) l140m01aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(List<? extends GenericBean> list) {
		if (list != null) {
			for (GenericBean bean : list) {
				save(bean);
			}
		}
	}

	@Override
	public void delete(List<? extends GenericBean> list) {
		if (list != null) {
			for (GenericBean bean : list) {
				delete(bean);
			}
		}
	}

	@Override
	public void save(PageParameters params, List<? extends GenericBean> list) {
		// it's not temp save, record log and delete temp data
		if (!UtilConstants.DEFAULT.是.equals(SimpleContextHolder
				.get(EloanConstants.TEMPSAVE_RUN))) {
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			tempDataService.deleteByMainId(mainId);
			String mainOid = Util.trim(params
					.getString(EloanConstants.MAIN_OID));
			if (!UtilConstants.DEFAULT.否.equals(SimpleContextHolder
					.get("reloadSaveLog")))
				docLogService.record(mainOid, DocLogEnum.SAVE);
		}
		save(list);
	}

	@Override
	public void uploadMIS_DW(C160M01A c160m01a) throws CapMessageException {
		if (Util.equals(c160m01a.getCaseType(), "3")) {// 整批匯入上傳
			List<C160S01D> c160s01dList = c160s01dDao.findByMainId(c160m01a
					.getMainId());
			for (C160S01D c160s01d : c160s01dList) {
				uploadMIS(c160m01a, c160s01d);
				uploadDW(c160m01a, c160s01d);
			}
		} else {// 一般及團貸上傳
			uploadMIS(c160m01a, null);
		}
	}

	// -----------------------上傳MIS-------------------------
	// @Override
	public void uploadMIS(C160M01A c160m01a, C160S01D c160s01d)
			throws CapMessageException {
		if (c160m01a != null) {
			if (Util.isEmpty(c160s01d)) { // 一般及團貸上傳
				MegaSSOUserDetails users = MegaSSOSecurityContext
						.getUserDetails();
				String user = Util.getRightStr(users.getUserId(), 5);

				// 動審表簽章欄檔取得人員職稱
				List<C160M01E> c160m01elist = c160m01eDao.findByMainId(c160m01a
						.getMainId());
				String apprId = "";
				String bossId = "";
				String reCheckId = "";
				// L1. 分行經辦
				// L3. 分行授信主管
				// L4. 分行覆核主管
				// L5. 經副襄理
				for (C160M01E c160m01e : c160m01elist) {
					String StaffJob = Util.trim(c160m01e.getStaffJob());// 取得人員職稱
					String StaffNo = Util.trim(c160m01e.getStaffNo());// 取得行員代碼
					if (Util.equals(StaffJob, "L1")) {// 分行經辦
						apprId = StaffNo;
					} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
						reCheckId = StaffNo;
					} else if (Util.equals(StaffJob, "L3")
							&& Util.isEmpty(bossId)) {// 分行授信主管(只取第一筆)
						bossId = StaffNo;
					}
				}
				// 若人員職稱為空值改取c160m01a上的人員資料
				if (Util.isEmpty(apprId)) {
					apprId = c160m01a.getApprId();
				}
				if (Util.isEmpty(reCheckId)) {
					reCheckId = c160m01a.getReCheckId();
				}
				if (!Util.isEmpty(reCheckId)) {
					user = Util.getRightStr(reCheckId, 5);
				}
				// 目前系統時間
				String sDate = CapDate.formatDate(new Date(),
						UtilConstants.DateFormat.YYYY_MM_DD);
				// 宣告
				MISRows<ELF500> misRows500 = new MISRows<ELF500>(ELF500.class);
				MISRows<ELF501> misRows501 = new MISRows<ELF501>(ELF501.class);
				MISRows<ELF502> misRows502 = new MISRows<ELF502>(ELF502.class);
				MISRows<ELF509> misRows509 = new MISRows<ELF509>(ELF509.class);
				MISRows<ELF508> misRows508 = new MISRows<ELF508>(ELF508.class);
				MISRows<ELF600> misRows600 = new MISRows<ELF600>(ELF600.class);
				MISRows<LNF13E> misRowsLNF13E = new MISRows<LNF13E>(LNF13E.class);
				
				MISRows<LNF164> misRows164 = new MISRows<LNF164>(LNF164.class);
				MISRows<ELLNGTEE> misRowsELLNGTEE = new MISRows<ELLNGTEE>(
						ELLNGTEE.class);
				MISRows<IQUOTAPP> misRowsIQUOTAPP = new MISRows<IQUOTAPP>(
						IQUOTAPP.class);
				MISRows<QUOTAINF> misRowsQUOTAINF = new MISRows<QUOTAINF>(
						QUOTAINF.class);
				MISRows<QUOTSUB> misRowsQUOTSUB = new MISRows<QUOTSUB>(
						QUOTSUB.class);
				MISRows<PQUOTNF> misRowsPQUOTNF = new MISRows<PQUOTNF>(
						PQUOTNF.class);
				MISRows<LNF010> misRowsLNF010 = new MISRows<LNF010>(
						LNF010.class);
				MISRows<LNF195> misRowsLNF195 = new MISRows<LNF195>(
						LNF195.class);
				MISRows<STUDATA> misRowsSTUDATA = new MISRows<STUDATA>(
						STUDATA.class);
				MISRows<QUOTUNIO> misRowsQUOTUNIO = new MISRows<QUOTUNIO>(
						QUOTUNIO.class);
				MISRows<PTEMAPPN> misRowsPTEMAPPN = new MISRows<PTEMAPPN>(
						PTEMAPPN.class);
				MISRows<PTEAMDTL> misRowsPTEAMDTL = new MISRows<PTEAMDTL>(
						PTEAMDTL.class);
				// MISRows<PTEAMAPP> misRowsPTEAMAPP = new MISRows<PTEAMAPP>(
				// PTEAMAPP.class);
				MISRows<ELF447N> misRowsELF447N = new MISRows<ELF447N>(
						ELF447N.class);
				MISRows<ELF453> misRowsELF453 = new MISRows<ELF453>(
						ELF453.class);
				List<ELF500> ELF500List = new ArrayList<ELF500>();
				List<ELF501> ELF501List = new ArrayList<ELF501>();
				List<ELF502> ELF502List = new ArrayList<ELF502>();
				List<ELF509> ELF509List = new ArrayList<ELF509>();
				List<ELF600> ELF600List = new ArrayList<ELF600>();

				List<LNF130> prepareELF504_LNF130List = new ArrayList<LNF130>();
				List<LNF130> LNF130List = new ArrayList<LNF130>();
				List<LNF164> LNF164List = new ArrayList<LNF164>();
				List<ELLNGTEE> ELLNGTEEList = new ArrayList<ELLNGTEE>();
				List<IQUOTAPP> IQUOTAPPList = new ArrayList<IQUOTAPP>();
				List<QUOTAINF> QUOTAINFList = new ArrayList<QUOTAINF>();
				List<QUOTSUB> QUOTSUBList = new ArrayList<QUOTSUB>();
				List<PQUOTNF> PQUOTNFList = new ArrayList<PQUOTNF>();
				List<LNF010> LNF010List = new ArrayList<LNF010>();
				List<LNF195> LNF195List = new ArrayList<LNF195>();
				List<STUDATA> STUDATAList = new ArrayList<STUDATA>();
				List<ELF447N> ELF447NList = new ArrayList<ELF447N>();
				List<LNF13E> LNF13EList = new ArrayList<LNF13E>();
				
				List<C900M01G> C900M01GList = new ArrayList<C900M01G>();

				// 動審表mainId
				String c160mainId = c160m01a.getMainId();
				// 授信簽報書主檔
				L120M01A l120m01a = l120m01aDao.findByMainId(c160m01a
						.getSrcMainId());
				// 額度明明細檔
				List<C160M01B> c160m01blist = c160m01bDao
						.findByMainId(c160mainId);
				HashMap<String, String> lnf010Temp = new HashMap<String, String>();
				// Bean寫入資料
				String caseType = c160m01a.getCaseType();

				logger.info("{}=======>{}", "Start", "ELF509>");
				ELF509List = upELF509(ELF509List, c160m01a, apprId, reCheckId);

				List<ELF508> ELF508List = new ArrayList<ELF508>();
				logger.info("{}=======>{}", "Start", "ELF508>");
				ELF508List = upELF508(ELF508List, c160m01blist.get(0));
				
				for (C160M01B c160m01b : c160m01blist) {
					// 簽報書額度明細主檔
					L140M01A l140m01a = l140m01aDao.findByMainId(c160m01b
							.getRefmainId());
					
					L140M01M l140m01m = this.l140m01mDao.findByMainId(l140m01a.getMainId());

					if (l140m01a != null) {
						L120M01C l120m01c = l140m01a.getL120m01c();
						if (l120m01c != null) {

							// 由於團貸案對應的簽報書來源可能不同,一般案也通用此方法
							String custId = Util.trim(l140m01a.getCustId());
							String dupNo = Util.trim(l140m01a.getDupNo());
							lnf010Temp
									.put(custId + dupNo, l120m01c.getMainId());
						}
						
						L120M01A l120m01aNow = null;
						String l120m01aNowL1 = "";
						String l120m01aNowL3 = "";
						String l120m01aNowL5 = "";
						if(true){
							/*
							 動審表一般：會選擇１份簽報書內的額度，可直接抓 c160m01a.srcMainId
							 動審表團貸：但依團貸母戶，列出底下Ｎ筆的團貸子戶。來源是Ｎ份簽報書。
							*/
							if (Util.equals(UtilConstants.Usedoc.caseType2.團貸,
									c160m01a.getCaseType())) {
								l120m01aNow = l120m01aDao.findByMainId(l140m01a.getL120m01c()
										.getMainId());
							} else {
								l120m01aNow = l120m01a;
							}
						
							if(true){
								//ref LMS1601ServiceImpl:: uploadIquotapp(...)
								for (L120M01F l120m01f : clsService.findL120M01F_mainId(l120m01aNow.getMainId())) {
									String staffJob = l120m01f.getStaffJob();
									String staffNo = l120m01f.getStaffNo();
									if (UtilConstants.BRANCHTYPE.分行.equals(l120m01f.getBranchType())) {
										if (UtilConstants.STAFFJOB.經辦L1.equals(staffJob)) {
											l120m01aNowL1 = staffNo;
										}
										if (UtilConstants.STAFFJOB.授信主管L3.equals(staffJob)) {
											l120m01aNowL3 = staffNo;
										}
										if (UtilConstants.STAFFJOB.單位授權主管L5.equals(staffJob)) {
											l120m01aNowL5 = staffNo;
										}
									}
								}
								
								if(Util.isEmpty(l120m01aNowL1)){
									l120m01aNowL1 = apprId;
								}
								if(Util.isEmpty(l120m01aNowL3)){
									l120m01aNowL3 = reCheckId;
								}
								if(Util.isEmpty(l120m01aNowL5)){
									l120m01aNowL5 = bossId;
								}
							}
						}

						logger.info("{}=======>{}", "Start", "SETDATA");
						
						/*
						 * 當[1]一次動用多個額度 or [2]團貸的時候
						 * 會有N筆 cntrNo => 亦即會有 N 筆 elf500
						 * 
						 * 第1個額度 ELF500List{input:0, ouput:1}
						 * 第2個額度 ELF500List{input:1, ouput:2}
						 * 所以不能把 upELF500(ELF500List 的第1個輸入參數拿掉
						 */
						logger.info("{}=======>{}", "Start", "ELF500>");
						ELF500List = upELF500_when_caseType1_2(ELF500List, c160m01a, c160m01b,
								l120m01aNow, l140m01a, apprId, reCheckId, sDate);
						logger.info("{}=======>{}", "Start", "ELF600>");
						ELF600List = this.lmsservice.genELF600ObjectWhenNoRecord(ELF600List, l140m01m, l140m01a.getCntrNo(), l140m01a.getCustId(), l140m01a.getDupNo());
						logger.info("{}=======>{}", "Start", "ELF501>");
						ELF501List = upELF501_when_caseType1_2(ELF501List, c160m01a, c160m01b,
								l140m01a, apprId, reCheckId, sDate);
						logger.info("{}=======>{}", "Start", "ELF502>");
						ELF502List = upELF502(ELF502List, c160m01a, c160m01b,
								l140m01a, sDate);
						logger.info("{}=======>{}", "Start", "LNF130>");
						LNF130List = upLNF130(LNF130List, c160m01a, c160m01b,
								l140m01a, sDate, true);
						prepareELF504_LNF130List = upLNF130(prepareELF504_LNF130List, c160m01a, c160m01b,
								l140m01a, sDate, false);
						logger.info("{}=======>{}", "Start", "LNF164>");
						LNF164List = upLNF164(LNF164List, c160m01a, c160m01b,
								l140m01a, sDate);
						logger.info("{}=======>{}", "Start", "ELLNGTEE>");
						ELLNGTEEList = upELLNGTEE(ELLNGTEEList, c160m01a,
								c160m01b, l140m01a, sDate, user);
						logger.info("{}=======>{}", "Start", "IQUOTAPP>");
						IQUOTAPPList = upIQUOTAPP(IQUOTAPPList, c160m01a,
								c160m01b, l140m01a, l120m01aNowL1, l120m01aNowL5, sDate, user);
						logger.info("{}=======>{}", "Start", "QUOTAINF>");
						QUOTAINFList = upQUOTAINF(QUOTAINFList, c160m01a,
								c160m01b, l140m01a, l120m01aNowL3, sDate, user);
						logger.info("{}=======>{}", "Start", "QUOTSUB>");
						QUOTSUBList = upQUOTSUB(QUOTSUBList, c160m01a,
								c160m01b, l140m01a, sDate, user);
						logger.info("{}=======>{}", "Start", "PQUOTNF>");
						PQUOTNFList = upPQUOTNF(PQUOTNFList, c160m01a,
								c160m01b, l140m01a, sDate, user);
						logger.info("{}=======>{}", "Start", "LNF195>");
						LNF195List = upLNF195(LNF195List, c160m01a, c160m01b,
								l140m01a, sDate);
						logger.info("{}=======>{}", "Start", "STUDATA>");
						STUDATAList = upSTUDATA(STUDATAList, c160m01a,
								c160m01b, l120m01aNow, l140m01a, sDate, user);
						logger.info("{}=======>{}", "StartUpDate", "ELF431>");
						this.upELF431(c160m01a, c160m01b, l140m01a, user);
						logger.info("{}=======>{}", "Start", "ELF447N>");
						ELF447NList = upELF447N(ELF447NList, c160m01a,
								c160m01b, l120m01aNow, l140m01a);
						
						LNF13EList = upLNF13E(LNF13EList, l140m01a);

						// J-104-0097-001
						// 配合業報指示，有關建商餘屋貸款之餘額列入控管，申請於e-Loan管理系統授信簽報書之額度明細表增加管控註記
						// 12.-建案完工未出售房屋之融資檔MIS.ELF517
						String cntrNo = l140m01a.getCntrNo();
						try {
							this.uploadELF517(c160m01a, l140m01a, l120m01aNow,
									cntrNo);
						} catch (CapException e) {
							throw new CapMessageException(e.getMessage(),
									getClass());
						}

						// 聯貸上傳TABLE
						if (Util.equals(l140m01a.getSnoKind(),
								UtilConstants.Cntrdoc.snoKind.聯貸)) {
							logger.info("{}=======>{}", "Start", "QUOTUNIO");
							List<QUOTUNIO> QUOTUNIOList = new ArrayList<QUOTUNIO>();
							QUOTUNIOList = upQUOTUNIO(QUOTUNIOList, c160m01a,
									c160m01b, sDate, user);
							misRowsQUOTUNIO.setValues(QUOTUNIOList);
						}
						// 團貸上傳TABLE
						if (Util.equals(caseType,
								UtilConstants.Usedoc.caseType2.團貸) ||
							Util.equals(caseType, 
								UtilConstants.Usedoc.caseType2.一般)) {//線上對保開放撥款他行，一般案件也要視情況上傳ELF453
							List<ELF453> ELF453List = new ArrayList<ELF453>();
							ELF453List = upELF453(ELF453List, c160m01a,
									c160m01b, l140m01a, sDate, l120m01aNow);
							misRowsELF453.setValues(ELF453List);
						}
						logger.info("{}=======>{}", "END", "SETDATA");
					}
				}//end for loop(...)
				
				// 團貸上傳TABLE
				if (Util.equals(caseType, UtilConstants.Usedoc.caseType2.團貸)) {
					List<PTEMAPPN> PTEMAPPNList = new ArrayList<PTEMAPPN>();
					logger.info("{}=======>{}", "Start", "PTEMAPPN>");
					PTEMAPPNList = upPTEMAPPN(PTEMAPPNList, c160m01a, sDate,
							user);
					misRowsPTEMAPPN.setValues(PTEMAPPNList);
					List<PTEAMDTL> PTEAMDTLList = new ArrayList<PTEAMDTL>();
					logger.info("{}=======>{}", "Start", "PTEAMDTL>");
					PTEAMDTLList = upPTEAMDTL(PTEAMDTLList, c160m01a, user);
					misRowsPTEAMDTL.setValues(PTEAMDTLList);
				}
				// 設定LNF010
				logger.info("{}=======>{}", "Start", "LNF010>");
				LNF010List = this.upLNF010(LNF010List, c160m01a, sDate, user,
						lnf010Temp);
				logger.info("{}=======>{}", "END", "SETDATA");
				
				
				List<ELF504> elf504_list = new ArrayList<ELF504>();
				if(true){
					Map<String, List<LNF130>> map = category_by_cntrNo(prepareELF504_LNF130List);
					for(String cntrNo : map.keySet()){
						boolean expired_eloan_data = false;
						List<LNF130> current_lnf130List = map.get(cntrNo);
						//========						
						if(true){
							int l140s02a_new_cnt = 0;
							int l140s02a_loanNo_cnt = 0;
							for(LNF130 newobjLNF130: current_lnf130List){
								if(Util.equals(newobjLNF130.getLnf130_br_no(), BR_901)){
									l140s02a_loanNo_cnt++;	
								}else{			
									/*
									 * 若已有 loanNo 但改科目，上傳的 loanNo=00051, brNo=044
									 */
									if(isChgProdSubj(newobjLNF130.getLnf130_loan_no())){
										l140s02a_loanNo_cnt++;	
									}else{
										l140s02a_new_cnt++;	
									}	
								}
							}
							
							if(l140s02a_new_cnt>0 && l140s02a_loanNo_cnt==0){
								//偵測 a-loan 是否有 "未銷戶" 帳號
								//若有的話，表示 a-loan 的資料比 e-loan更新
								if(fetch_not_cancel_lnf030(cntrNo).size()>0){
									expired_eloan_data = true;
								}
							}					
						}
						//========
						if(expired_eloan_data==false){
							/**
							 * (A).測試環境［a-loan倒檔 vs e-loan倒檔］的時間差
							 * (B).正式環境，已覆核的動審表，人工 click 重上傳
							 * 當 a-loan 都已開戶，而 e-loan 仍 keep 住 N 個月前的 00001 的狀態
							 * 可能造成 ELF504 同時有 loanNo ，也有 00001
							 */

							elf504_list.addAll(buildELF504(current_lnf130List, ELF500List, c160m01a));	
						}else{
							/*
							 * 可能簽案時 P7,定期浮動，但在 a-loan改機動
							 * 之後把「原簽報書」退回，再重做動審表(在 l140s02a.secNo仍==1)
							 */
							List<ELF504> tmp_list = buildELF504(current_lnf130List, ELF500List, c160m01a);							
							if(tmp_list.size()>0){
								String id = Util.trim(StringUtils.substring(tmp_list.get(0).getElf504_cust_id(), 0, 10));
								String dup = Util.trim(StringUtils.substring(tmp_list.get(0).getElf504_cust_id(), 10, 11));
								String current_document_no = Util.trim(tmp_list.get(0).getElf504_documentno());
								//~~~~~~
								ELF500 db_elf500 = mislf500seervic.findByCustIdAndCntrno1(id, dup, cntrNo);								
								if(db_elf500!=null 
										&& Util.equals(db_elf500.getElf500_document_no(), current_document_no)){
									
									Set<String> set_loan_no = new HashSet<String>();
									Map<String, String> map_loan_no = new HashMap<String, String>();
									for(ELF504 elf504: tmp_list){
										String oldval = Util.trim(elf504.getElf504_loan_no());
										set_loan_no.add(oldval);
									}
									for(String oldval : set_loan_no){
										String newval = oldval;
										//=======
										if(oldval.length()==5){
											//elf501_seq==c160s01c.caseSeq==l140s02a.secNo											
											//把(文字)00001 轉成 (數字)1
											int elf501_seq = Util.parseInt(oldval);
											ELF501 elf501 = mislf501seervic.findByUniqueKey1(id, dup, cntrNo, elf501_seq);
											if(elf501!=null){
												newval = Util.trim(elf501.getElf501_loan_no());
												logger.info("replace elf504["+current_document_no+" , "+cntrNo+"]"
														+"from【"+oldval+"】to【"+newval+"】");
											}
										}
										//=======
										map_loan_no.put(oldval, newval);
									}
									for(ELF504 elf504: tmp_list){
										String oldval = Util.trim(elf504.getElf504_loan_no());
										if(map_loan_no.containsKey(oldval)){
											String newval = map_loan_no.get(oldval);
											elf504.setElf504_loan_no(newval);	
										}										
									}
								}
							}							
							elf504_list.addAll(tmp_list);							
						}						
					}	
				}				
				//==================================================
				misRows500.setValues(ELF500List);
				misRows501.setValues(ELF501List);
				misRows502.setValues(ELF502List);
				misRows509.setValues(ELF509List);
				misRows508.setValues(ELF508List);
				
				misRows164.setValues(LNF164List);
				misRowsELLNGTEE.setValues(ELLNGTEEList);
				misRowsIQUOTAPP.setValues(IQUOTAPPList);
				misRowsQUOTAINF.setValues(QUOTAINFList);
				misRowsQUOTSUB.setValues(QUOTSUBList);
				misRowsPQUOTNF.setValues(PQUOTNFList);
				misRowsLNF010.setValues(LNF010List);
				misRowsLNF195.setValues(LNF195List);
				misRowsSTUDATA.setValues(STUDATAList);
				misRowsELF447N.setValues(ELF447NList);
				misRowsLNF13E.setValues(LNF13EList);
				
				logger.info("{}=======>{}", "Start", "UPDATEMIS");
				// 轉為SQL上傳MIS
				upMisToServer(misRows500, "MIS");
				upMisToServer(misRows501, "MIS");
				upMisToServer(misRows502, "MIS");
				if(!ELF600List.isEmpty()){
					misRows600.setValues(ELF600List);
					upMisToServer(misRows600, "MIS");
				}
				
				if(true){
					//~~~~~~~~
					delMisToServerToELF504(elf504_list);
					//~~~~~~~~
					MISRows<ELF504> misRows504 = new MISRows<ELF504>(ELF504.class);
					misRows504.setValues(elf504_list);					
					upMisToServer(misRows504, "MIS");
				
					//~~~~~~~~
					//LNF130 用 [該分行,901]去區分[新做、重簽] 
					//~~~~~~~~
					MISRows<LNF130> misRows130 = new MISRows<LNF130>(LNF130.class);
					misRows130.setValues(LNF130List);
					upMisToServer(misRows130, "LN");
				}
				upMisToServer(misRows509, "MIS");
				upMisToServer(misRows508, "MIS");
				
				upMisToServer(misRows164, "LN");
				upMisToServer(misRowsIQUOTAPP, "MIS");
				upMisToServer(misRowsQUOTAINF, "MIS");

				upMisToServer(misRowsLNF195, "LN");
				upMisToServer(misRowsLNF010, "LN");
				upMisToServer(misRowsSTUDATA, "MIS");
				upMisToServer(misRowsPTEMAPPN, "MIS");
				// upMisToServer(misRowsPTEAMAPP, "MIS");
				upMisToServer(misRowsQUOTUNIO, "MIS");
				upMisToServer(misRowsPQUOTNF, "MIS");
				upMisToServer(misRowsPTEAMDTL, "MIS");
				upMisToServer(misRowsELF447N, "MIS");
				//J-111-0227 開放撥款他行
				upMisToServer(misRowsELF453, "MIS");

				// HashSet<String> ellngteeCntrnos = new HashSet<String>();
				// for (ELLNGTEE ellngtee : ELLNGTEEList) {
				// ellngteeCntrnos.add(Util.trim(ellngtee.getCntrno()));
				// }
				for (C160M01B c160m01b : c160m01blist) {
					delMisToServerToELLNGTEE(c160m01b.getCntrNo());
					delMisToServerToQUOTSUB(c160m01b.getCntrNo()); // 2014-10-23
																	// 先將原本在QUOTSUB中資料清除
				}
				upMisToServer(misRowsELLNGTEE, "MIS");
				upMisToServer(misRowsQUOTSUB, "MIS");
				upMisToServer(misRowsLNF13E, "LN");
				
				if (Util.equals(caseType, "2")
						&& Util.isNotEmpty(c160m01a.getLoanMasterNo())) {

					this.upC900M01G_when_caseType2(c160m01a, C900M01GList, reCheckId);
					// uploadPTEAMAPP(c160m01a);
				}
			} else {
				// 動審表整批匯入上傳MIS
				if (!Util.equals(c160s01d.getMisflag(), "Y")) { // 是否上傳過MIS上傳過的資料不重覆上傳
					MegaSSOUserDetails users = MegaSSOSecurityContext
							.getUserDetails();
					String user = Util.getRightStr(users.getUserId(), 5);
					// 動審表簽章欄檔取得人員職稱
					List<C160M01E> c160m01elist = c160m01eDao
							.findByMainId(c160m01a.getMainId());
					String apprId = "";
					String bossId = "";
					String reCheckId = "";
					// L1. 分行經辦
					// L3. 分行授信主管
					// L4. 分行覆核主管
					// L5. 經副襄理
					for (C160M01E c160m01e : c160m01elist) {
						String StaffJob = Util.trim(c160m01e.getStaffJob());// 取得人員職稱
						String StaffNo = Util.trim(c160m01e.getStaffNo());// 取得行員代碼
						if (Util.equals(StaffJob, "L1")) {// 分行經辦
							apprId = StaffNo;
						} else if (Util.equals(StaffJob, "L4")) {// 分行覆核主管
							reCheckId = StaffNo;
						} else if (Util.equals(StaffJob, "L3")
								&& Util.isEmpty(bossId)) {// 分行授信主管(只取第一筆)
							bossId = StaffNo;
						}
					}
					// 若人員職稱為空值改取c160m01a上的人員資料
					if (Util.isEmpty(apprId)) {
						apprId = c160m01a.getApprId();
					}
					if (Util.isEmpty(reCheckId)) {
						reCheckId = c160m01a.getReCheckId();
					}
					if (!Util.isEmpty(reCheckId)) {
						user = Util.getRightStr(reCheckId, 5);
					}
					// 個金簽報書主檔
					L120M01A l120m01a = l120m01aDao.findByMainId(c160m01a
							.getSrcMainId());
					l120m01a = Util.isEmpty(l120m01a) ? new L120M01A()
							: l120m01a;
					// 個金動審表匯入主檔
					C160M01F c160m01f = c160m01fDao.findByUniqueKey(c160m01a
							.getMainId());
					c160m01f = Util.isEmpty(c160m01f) ? new C160M01F()
							: c160m01f;
					// 宣告
					MISRows<ELF500> misRows500 = new MISRows<ELF500>(
							ELF500.class);
					MISRows<ELF501> misRows501 = new MISRows<ELF501>(
							ELF501.class);
					MISRows<ELF675> misRows675 = new MISRows<ELF675>(
							ELF675.class);
					MISRows<LNF164> misRows164 = new MISRows<LNF164>(
							LNF164.class);
					MISRows<LNF010> misRows010 = new MISRows<LNF010>(
							LNF010.class);
					MISRows<ELLNGTEE> misRowsELLNGTEE = new MISRows<ELLNGTEE>(
							ELLNGTEE.class);
					MISRows<IQUOTAPP> misRowsIQUOTAPP = new MISRows<IQUOTAPP>(
							IQUOTAPP.class);
					MISRows<QUOTAINF> misRowsQUOTAINF = new MISRows<QUOTAINF>(
							QUOTAINF.class);
					MISRows<QUOTSUB> misRowsQUOTSUB = new MISRows<QUOTSUB>(
							QUOTSUB.class);
					MISRows<PTEMAPPN> misRowsPTEMAPPN = new MISRows<PTEMAPPN>(
							PTEMAPPN.class);
					MISRows<ELF447N> misRowsELF447N = new MISRows<ELF447N>(
							ELF447N.class);
					MISRows<PTEAMDTL> misRowsPTEAMDTL = new MISRows<PTEAMDTL>(
							PTEAMDTL.class);
					List<ELF500> ELF500List = new ArrayList<ELF500>();
					List<ELF501> ELF501List = new ArrayList<ELF501>();
					List<ELF675> ELF675List = new ArrayList<ELF675>();
					List<LNF130> LNF130List = new ArrayList<LNF130>();
					List<LNF164> LNF164List = new ArrayList<LNF164>();
					List<LNF010> LNF010List = new ArrayList<LNF010>();
					List<ELLNGTEE> ELLNGTEEList = new ArrayList<ELLNGTEE>();
					List<IQUOTAPP> IQUOTAPPList = new ArrayList<IQUOTAPP>();
					List<QUOTAINF> QUOTAINFList = new ArrayList<QUOTAINF>();
					List<QUOTSUB> QUOTSUBList = new ArrayList<QUOTSUB>();
					List<PTEMAPPN> PTEMAPPNList = new ArrayList<PTEMAPPN>();
					List<ELF447N> ELF447NList = new ArrayList<ELF447N>();
					List<PTEAMDTL> PTEAMDTLList = new ArrayList<PTEAMDTL>();

					List<C900M01G> C900M01GList = new ArrayList<C900M01G>();
					// Bean寫入資料
					logger.info("{}=======>{}", "Start", "SETDATA");
					logger.info("{}=======>{}", "Start", "ELF500>");
					ELF500List = upELF500Case3(ELF500List, c160m01a, c160m01f,
							c160s01d);
					logger.info("{}=======>{}", "Start", "ELF501>");
					ELF501List = upELF501Case3(ELF501List, c160m01a, c160m01f,
							c160s01d);
					
					logger.info("{}=======>{}", "Start", "ELF675>");
					String subjCode3 = prodservice.getSubject8to3(c160m01f.getSubjCode());
					ELF675List = upELF675Case3(ELF675List, c160m01a, c160m01f, c160s01d, subjCode3);
					
					logger.info("{}=======>{}", "Start", "LNF130>");
					LNF130List = upLNF130Case3(LNF130List, c160m01a, c160m01f,
							c160s01d);
					logger.info("{}=======>{}", "Start", "LNF164>");
					LNF164List = upLNF164Case3(LNF164List, c160m01a, c160m01f,
							c160s01d);
					logger.info("{}=======>{}", "Start", "LNF010>");
					LNF010List = upLNF010Case3(LNF010List, c160m01a, c160m01f,
							c160s01d);
					logger.info("{}=======>{}", "Start", "ELLNGTEE>");
					ELLNGTEEList = upELLNGTEECase3(ELLNGTEEList, c160m01a,
							c160m01f, c160s01d, user);
					logger.info("{}=======>{}", "Start", "IQUOTAPP>");
					IQUOTAPPList = upIQUOTAPPCase3(IQUOTAPPList, c160m01a,
							c160m01f, c160s01d, user);
					logger.info("{}=======>{}", "Start", "QUOTAINF>");
					QUOTAINFList = upQUOTAINFCase3(QUOTAINFList, c160m01a,
							c160m01f, c160s01d);
					logger.info("{}=======>{}", "Start", "QUOTSUB>");
					QUOTSUBList = upQUOTSUBCase3(QUOTSUBList, c160m01a,
							c160m01f, c160s01d);
					logger.info("{}=======>{}", "Start", "PTEMAPPN>");
					PTEMAPPNList = upPTEMAPPNCase3(PTEMAPPNList, c160m01a,
							c160m01f);
					logger.info("{}=======>{}", "Start", "ELF447N>");
					ELF447NList = upELF447NCase3(ELF447NList, c160m01a,
							c160m01f, c160s01d, l120m01a);
					logger.info("{}=======>{}", "Start", "PTEAMDTL>");
					PTEAMDTLList = upPTEAMDTL(PTEAMDTLList, c160m01a, user);

					List<ELF504> elf504_list = buildELF504(LNF130List, ELF500List, c160m01a);
					//==================================================
					misRows500.setValues(ELF500List);
					misRows501.setValues(ELF501List);					
					misRows675.setValues(ELF675List);
					misRows164.setValues(LNF164List);
					misRows010.setValues(LNF010List);
					misRowsELLNGTEE.setValues(ELLNGTEEList);
					misRowsIQUOTAPP.setValues(IQUOTAPPList);
					misRowsQUOTAINF.setValues(QUOTAINFList);
					misRowsQUOTSUB.setValues(QUOTSUBList);
					misRowsPTEMAPPN.setValues(PTEMAPPNList);
					misRowsELF447N.setValues(ELF447NList);
					misRowsPTEAMDTL.setValues(PTEAMDTLList);
					logger.info("{}=======>{}", "END", "SETDATA");
					// 轉為SQL上傳MIS
					logger.info("{}=======>{}", "Start", "UPDATEMIS");
					upMisToServer(misRows500, "MIS");
					upMisToServer(misRows501, "MIS");
					upMisToServer(misRows675, "MIS");
					if(true){	
						//002中鋼整批匯入
						
						//~~~~~~~~
						//delMisToServerToELF504(elf504_list);
						//~~~~~~~~
						MISRows<ELF504> misRows504 = new MISRows<ELF504>(ELF504.class);
						misRows504.setValues(elf504_list);					
						upMisToServer(misRows504, "MIS");	
					
						MISRows<LNF130> misRows130 = new MISRows<LNF130>(LNF130.class);
						misRows130.setValues(LNF130List);
						upMisToServer(misRows130, "LN");
					}
					
					upMisToServer(misRows164, "LN");
					upMisToServer(misRows010, "LN");
					upMisToServer(misRowsELLNGTEE, "MIS");
					upMisToServer(misRowsIQUOTAPP, "MIS");
					upMisToServer(misRowsQUOTAINF, "MIS");
					upMisToServer(misRowsQUOTSUB, "MIS");
					upMisToServer(misRowsPTEMAPPN, "MIS");
					upMisToServer(misRowsELF447N, "MIS");
					upMisToServer(misRowsPTEAMDTL, "MIS");
					logger.info("{}=======>{}", "END", "UPDATEMIS");
					// upMisPTEAMAPPCase3(c160m01a,50);
					// 若上傳成功則將Misflag設定為Y
					c160s01d.setMisflag("Y");
					save(c160s01d);

					this.upC900M01G_when_caseType3(c160m01a, C900M01GList, reCheckId);
				}
			}
		}
	}

	public void checkAndCreateC900M01G(L140M01A l140m01a, C160S01D c160s01d,
			C160M01A c160m01a, String status, List<GenericBean> saveList) {
		String cntrNo = "";
		String soureMainid = "";
		String custId = "";
		String dupNo = "";
		String tCurr = "";
		BigDecimal tAmt = new BigDecimal(0);
		Date approveDate = null;
		String grpcntrno = "";

		if (c160s01d != null) {
			grpcntrno = Util.trim(c160m01a.getLoanMasterNo());
			cntrNo = Util.trim(c160s01d.getCntrNo());
			String tCid = Util.trim(c160s01d.getCustId());
			if (tCid.length() == 11) {
				custId = Util.getLeftStr(tCid, 10);
			} else {
				custId = Util.getLeftStr(tCid, 8);
			}
			dupNo = Util.getRightStr(tCid, 1);

			tCurr = "TWD";
			tAmt = c160s01d.getLoanTotAmt();
			approveDate = c160s01d.getCreateTime();

		} else {
			if (l140m01a != null) {
				cntrNo = Util.trim(l140m01a.getCntrNo());
				soureMainid = Util.trim(l140m01a.getMainId());
				custId = Util.trim(l140m01a.getCustId());
				dupNo = Util.trim(l140m01a.getDupNo());
				tCurr = Util.trim(l140m01a.getCurrentApplyCurr());
				tAmt = l140m01a.getCurrentApplyAmt();
				approveDate = l140m01a.getApproveTime();
				// String grpcntrno = Util.trim(c160m01a.getLoanMasterNo());

				L140M03A l140m03a = l140m03aDao.findByMainId(soureMainid);
				grpcntrno = Util.trim(l140m03a.getGrpCntrNo());
			}
		}

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		C900M01G c900m01g = new C900M01G();
		String c900m01gMainId = IDGenerator.getUUID();
		c900m01g.setMainId(c900m01gMainId);
		c900m01g.setCreateTime(CapDate.getCurrentTimestamp());
		c900m01g.setCreator(user.getUserId());
		c900m01g.setUpdater(user.getUserId());
		c900m01g.setUpdateTime(CapDate.getCurrentTimestamp());
		c900m01g.setCustId(custId);
		c900m01g.setDupNo(dupNo);

		c900m01g.setGrpcntrno(grpcntrno);
		c900m01g.setCntrNo(cntrNo);
		c900m01g.setStatus(status);
		if ("1".equals(status)) {
			c900m01g.setApplyCurr(tCurr);
			c900m01g.setApplyAmt(tAmt);
		} else if ("2".equals(status)) {
			c900m01g.setApproveCurr(tCurr);
			c900m01g.setApproveAmt(tAmt);

			c900m01g.setApplyCurr(tCurr);
			c900m01g.setApplyAmt(tAmt);
			c900m01g.setApproveDate(approveDate);
		} else if ("3".equals(status)) {
			c900m01g.setLoanCurr(tCurr);
			c900m01g.setLoanAmt(tAmt);

			c900m01g.setLoanDate(CapDate.getCurrentTimestamp());
		}

		saveList.add(c900m01g);
	}

	//UPDATE團貸明細檔
	@Override
	public void upC900M01G_when_caseType2(C160M01A c160m01a, List<C900M01G> data,
			String reCheckId) {
		String mainId = Util.trim(c160m01a.getMainId());
		List<GenericBean> saveList = new ArrayList<GenericBean>();
		List<C160M01B> c160m01bList = clsService.findC160M01B_mainId(mainId);
		String refMainId = "";

		// 為了補上版前簽核中的動審中團貸分戶明細資料
		for (C160M01B c160m01b : c160m01bList) {
			refMainId = Util.trim(c160m01b.getRefmainId());
			L140M01A l140m01a = findModelByMainId(L140M01A.class, refMainId);
			if (l140m01a != null) {
				String cntrNo = Util.trim(l140m01a.getCntrNo());
				C900M01G c900m01gOld = c900m01gDao.findByCntrNo(cntrNo);
				if (c900m01gOld == null) {
					this.checkAndCreateC900M01G(l140m01a, null, c160m01a, "2",
							saveList);
				}
			}
		}
		save(saveList);

		for (C160M01B c160m01b : c160m01bList) {
			refMainId = Util.trim(c160m01b.getRefmainId());
			L140M01A l140m01a = findModelByMainId(L140M01A.class, refMainId);
			if (l140m01a != null) {
				String tCurr = Util.trim(l140m01a.getCurrentApplyCurr());
				BigDecimal tAmt = l140m01a.getCurrentApplyAmt();

				String cntrNo = Util.trim(l140m01a.getCntrNo());
				C900M01G c900m01gOld = c900m01gDao.findByCntrNo(cntrNo);
				if (c900m01gOld != null) {
					c900m01gOld.setStatus("3");
					c900m01gOld.setLoanCurr(tCurr);
					c900m01gOld.setLoanAmt(tAmt);
					c900m01gOld.setLoanDate(c160m01a.getApproveTime());
					c900m01gOld.setUpdater(reCheckId);
					c900m01gOld.setUpdateTime(CapDate.getCurrentTimestamp());
					data.add(c900m01gOld);
				}
			}
		}

	}

	private void upC900M01G_when_caseType3(C160M01A c160m01a, List<C900M01G> data,
			String reCheckId) {
		String mainId = Util.trim(c160m01a.getMainId());
		List<GenericBean> saveList = new ArrayList<GenericBean>();
		String cntrNo = "";
		List<C160S01D> c160s01dList = c160s01dDao.findByMainId(mainId);
		for (C160S01D c160s01d : c160s01dList) {
			if (c160s01d != null) {
				cntrNo = Util.trim(c160s01d.getCntrNo());
				C900M01G c900m01gOld = c900m01gDao.findByCntrNo(cntrNo);
				if (c900m01gOld == null) {
					this.checkAndCreateC900M01G(null, c160s01d, c160m01a, "2",
							saveList);
				}
			}
		}
		save(saveList);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (C160S01D c160s01d : c160s01dList) {
			if (c160s01d != null) {
				String tCurr = "TWD";
				BigDecimal tAmt = c160s01d.getLoanTotAmt();
				// String loanMasterNo = Util.trim(c160m01a.getLoanMasterNo());
				String approvedNo = Util.trim(c160m01a.getApprovedNo());
				cntrNo = Util.trim(c160s01d.getCntrNo());
				C900M01G c900m01gOld = c900m01gDao.findByCntrNo(cntrNo);
				if (c900m01gOld != null) {
					// c900m01gOld.setGrpcntrno(loanMasterNo);
					c900m01gOld.setGrpcntrno(approvedNo);
					c900m01gOld.setStatus("3");
					c900m01gOld.setLoanCurr(tCurr);
					c900m01gOld.setLoanAmt(tAmt);
					c900m01gOld.setLoanDate(CapDate.getCurrentTimestamp());
					c900m01gOld.setUpdater(user.getUserId());
					c900m01gOld.setUpdateTime(CapDate.getCurrentTimestamp());
					data.add(c900m01gOld);
				}
			}
		}
	}

	@SuppressWarnings("unused")
	private void uploadPTEAMAPP(C160M01A c160m01a) throws CapMessageException {
		if (c160m01a != null) {

			BigDecimal c160s01csum = null;
			String mainId = Util.trim(c160m01a.getMainId());
			Map<String, Object> map = this.getJdbc().queryForMap(
					"getC160S01C_LoanTotAmt", new Object[] { mainId });
			c160s01csum = (map != null ? Util.parseBigDecimal(map
					.get("LoanTotAmt")) : new BigDecimal("0"));
			// List<Map<String, Object>> pteamappmaplist = misdbBaseService
			// .findPteamappData(Util.trim(c160m01a.getCustId()), Util
			// .trim(c160m01a.getDupNo()), Util.addZeroWithValue(
			// Util.parseInt(CapDate.formatDate(
			// c160m01a.getApproveTime(), "yyyy")) - 1911,
			// 3), Util.getRightStr(
			// Util.trim(c160m01a.getPackNo()), 2));
			// TODO 2014-04-14由原本的ID+年度+批號找出團貸資訊改用ID+總戶序號
			List<Map<String, Object>> pteamappmaplist = misdbBaseService
					.findPteamappDataByGrpcntrno(Util.trim(c160m01a
							.getLoanMasterNo()));

			if (!pteamappmaplist.isEmpty()) {
				for (Map<String, Object> pteamappmap : pteamappmaplist) {

					logger.info("c160s01csum ->" + c160s01csum);
					logger.info("OVERAMT ->" + pteamappmap.get("OVERAMT"));

					BigDecimal count = Arithmetic.sub(Util.parseBigDecimal(Util
							.trim(pteamappmap.get("OVERAMT"))), c160s01csum);
					if (count.compareTo(BigDecimal.ZERO) < 0) {
						// 'EFD3051','zh_TW','ERROR','已超過團貸總戶申請額度，請確認額度資訊!!'
						throw new CapMessageException(RespMsgHelper.getMessage("EFD3051"), getClass());
					}
					;
					pteamappmap.put("OVERAMT", count);// 剩餘額度
				}
			}
			MISRows<PTEAMAPP> misRowsPTEAMAPP = new MISRows<PTEAMAPP>(
					PTEAMAPP.class);
			misRowsPTEAMAPP.setValuesByMap(pteamappmaplist);
			logger.info("{}=======>{}", "Start", "UPDATEMISPTEAMAPP");
			upMisToServer(misRowsPTEAMAPP, "MIS");
			logger.info("{}=======>{}", "END", "UPDATEMISPTEAMAPP");
		}
	}

	public void uploadMISPTEAMAPP(C160M01A c160m01a, BigDecimal c160s01dsum) {
		if (c160m01a != null) {
			String mainId = Util.trim(c160m01a.getMainId());
			C160M01F c160m01f = c160m01fDao.findByUniqueKey(mainId);
			c160m01f = (Util.isEmpty(c160m01f) ? new C160M01F() : c160m01f);
			if (c160s01dsum == null) {
				Map<String, Object> map = this.getJdbc().queryForMap(
						"getC160S01D_LoanTotAmt", new Object[] { mainId });
				c160s01dsum = (map != null ? Util.parseBigDecimal(map
						.get("LoanTotAmt")) : new BigDecimal("0"));
			}

			List<Map<String, Object>> pteamappmaplist = misdbBaseService
					.findPteamappData(Util.trim(c160m01f.getCustId()), Util
							.trim(c160m01f.getDupNo()), Util.addZeroWithValue(
							Util.parseInt(CapDate.formatDate(
									c160m01f.getApproveTime(), "yyyy")) - 1911,
							3), Util.getRightStr(
							Util.trim(c160m01a.getPackNo()), 2));
			if (!pteamappmaplist.isEmpty()) {
				for (Map<String, Object> pteamappmap : pteamappmaplist) {
					BigDecimal count = Arithmetic.sub(Util.parseBigDecimal(Util
							.trim(pteamappmap.get("OVERAMT"))), c160s01dsum);
					pteamappmap.put("OVERAMT", count);
				}
			}
			MISRows<PTEAMAPP> misRowsPTEAMAPP = new MISRows<PTEAMAPP>(
					PTEAMAPP.class);
			misRowsPTEAMAPP.setValuesByMap(pteamappmaplist);
			logger.info("{}=======>{}", "Start", "UPDATEMISPTEAMAPP");
			upMisToServer(misRowsPTEAMAPP, "MIS");
			logger.info("{}=======>{}", "END", "UPDATEMISPTEAMAPP");
		}
	}

	private List<PTEAMDTL> upPTEAMDTL(List<PTEAMDTL> data, C160M01A c160m01a,
			String user) {
		List<C160M01B> c160m01blist = c160m01bDao.findByMainId(Util
				.trim(c160m01a.getMainId()));
		BigDecimal c160s01csum = new BigDecimal(0);
		for (C160M01B c160m01b : c160m01blist) {
			List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
					c160m01a.getMainId(), c160m01b.getRefmainId());
			for (C160S01C c160s01c : c160s01clist) {
				c160s01csum = c160s01csum.add(c160s01c.getApprovedAmt());
			}
		}
		PTEAMDTL pteamdtl = new PTEAMDTL();
		pteamdtl.setCustid(Util.trim(c160m01a.getCustId()));// 公司統一編號
		pteamdtl.setDupno(Util.trim(c160m01a.getDupNo()));// 重覆序號
		pteamdtl.setYear(Util.trim(c160m01a.getParentYear()));// 年度
		pteamdtl.setAmtappno(Util.trim(c160m01a.getLoanPackNo()));// 額度申請序號
		pteamdtl.setLotno(Util.trim(c160m01a.getPackNo()));// 申請批號
		pteamdtl.setAppamt(c160s01csum);// 申請額度
		pteamdtl.setSecamt(c160s01csum);// 核准額度
		pteamdtl.setUpdater(user);// 資料修改人
		pteamdtl.setAprvdate(CapDate.getCurrentTimestamp());// 核准動用日
		pteamdtl.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
		pteamdtl.setGrpcntrno(Util.trim(c160m01a.getLoanMasterNo()));// 團貸編號
		data.add(pteamdtl);
		return data;
	}

	public List<QUOTUNIO> upQUOTUNIO(List<QUOTUNIO> data, C160M01A c160m01a,
			C160M01B c160m01b, String sDate, String user) {
		L140M01A l140m01a = l140m01aDao.findByMainId(c160m01b.getRefmainId());
		String CNTRNO = Util.trim(l140m01a.getCntrNo());// 額度序號
		for (L140M01E l140m01e : l140m01a.getL140m01e()) {
			StringBuffer branchCode = new StringBuffer();
			IBranch branchData = branchService.getBranch(l140m01e
					.getShareBrId());
			// 當為海外分行其前三碼為999
			if (UtilConstants.BrNoType.國外.equals(branchData.getBrNoFlag())) {
				branchCode.append("999").append(
						misdbBaseService.findICBCBRByBrId(l140m01e
								.getShareBrId()));
			} else {
				branchCode.append("017").append(l140m01e.getShareBrId());
			}
			BigDecimal LNAMT = (Util.isEmpty(l140m01e.getShareAmt()) ? new BigDecimal(
					0) : l140m01e.getShareAmt());// 參貸金額
			String UNLNTYPE = UtilConstants.Usedoc.unioType.自行聯貸;// 聯貸性質
			String UNITNO = branchCode.toString();// 參貸行庫
			String MAINBH = (Util.getLeftStr(l140m01a.getCntrNo(), 3).equals(
					l140m01e.getShareBrId()) ? UtilConstants.DEFAULT.是 : "");// 主辦行
			QUOTUNIO quotunio = new QUOTUNIO();
			quotunio.setUnlntype(UNLNTYPE);
			quotunio.setCntrno(CNTRNO);
			quotunio.setUnitno(UNITNO);
			quotunio.setMainbh(MAINBH);
			quotunio.setLnamt(LNAMT);
			quotunio.setUpdater(user);// 資料修改人（行員代號）
			quotunio.setTmestamp(CapDate.getCurrentTimestamp()); // 資料修改日期
			data.add(quotunio);
		}
		return data;
	}

	private List<PTEMAPPN> upPTEMAPPNCase3(List<PTEMAPPN> data,
			C160M01A c160m01a, C160M01F c160m01f) {
		String BRANCH = Util.trim(c160m01a.getCaseBrId());// 分行別
		String CUSTID = Util.trim(c160m01f.getCustId());// 公司統一編號
		String DUPNO = Util.trim(c160m01f.getDupNo());// 重複序號
		Integer YEAR = (Util.parseInt(CapDate.formatDate(
				c160m01f.getApproveTime(), "yyyy")) - 1911);// 年度
		String AMTAPPNO = Util.addZeroWithValue(c160m01a.getPackNo(), 4);// 額度申請批號
		PTEMAPPN ptemappn = new PTEMAPPN();
		ptemappn.setBranch(BRANCH);
		ptemappn.setCustid(CUSTID);
		ptemappn.setDupno(DUPNO);
		ptemappn.setYear(YEAR.toString());
		ptemappn.setAmtappno(AMTAPPNO);
		ptemappn.setUpdater(Util.trim(c160m01f.getCName()));// 資料修改人(行員代號)
		ptemappn.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
		data.add(ptemappn);
		return data;
	}

	private List<PTEMAPPN> upPTEMAPPN(List<PTEMAPPN> data, C160M01A c160m01a,
			String sDate, String user) {
		String BRANCH = Util.trim(c160m01a.getCaseBrId());// 分行別
		String CUSTID = Util.trim(c160m01a.getCustId());// 公司統一編號
		String DUPNO = Util.trim(c160m01a.getDupNo());// 重複序號
		// c160m01a.parentYear是民國年，不應再減1911
		String YEAR = Util.trim(c160m01a.getParentYear());// 年度
		String AMTAPPNO = Util.addZeroWithValue(c160m01a.getPackNo(), 4);// 額度申請批號
		PTEMAPPN ptemappn = new PTEMAPPN();
		ptemappn.setBranch(BRANCH);
		ptemappn.setCustid(CUSTID);
		ptemappn.setDupno(DUPNO);
		ptemappn.setYear(YEAR);
		ptemappn.setAmtappno(AMTAPPNO);
		ptemappn.setUpdater(user);// 資料修改人(行員代號)
		ptemappn.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
		data.add(ptemappn);
		return data;
	}

//	@SuppressWarnings("unused")
//	private List<PTEAMAPP> upPTEAMAPP(List<PTEAMAPP> data, C160M01A c160m01a,
//			C160M01B c160m01b, L120M01A l120m01a, L140M01A l140m01a,
//			String sDate, String user) {
//		L140M01L l140m01l = l140m01lDao.findByUniqueKey(l140m01a.getMainId());
//		l140m01l = (Util.isEmpty(l140m01l) ? new L140M01L() : l140m01l);
//		L140M03A l140m03a = l140m03aDao.findByUniqueKey(l140m01a.getMainId());
//		l140m03a = (Util.isEmpty(l140m03a) ? new L140M03A() : l140m03a);
//		c160m01a.parentYear是民國年，不應再減1911
//		String YEAR = Util.trim(c160m01a.getParentYear() - 1911);
//		String AMTAPPNO = Util.trim(c160m01a.getPackNo());
//		String SUBCOMPID = Util.trim(c160m01a.getChildrenId());
//		String SUBCOMPNM = Util.trimSizeInOS390(
//				Util.trim(c160m01a.getChildrenName()), 82);
//		String CUSTID = Util.trim(l140m01a.getCustId());
//		String DUPNO = Util.trim(l140m01a.getDupNo());
//		Date EFFFROM = l140m01l.getExpBDate();
//		Date EFFEND = l140m01l.getExpEDate();
//		BigDecimal TOTAMT = l140m01l.getLoanAmt();
//		String PROJECTNM = Util.trimSizeInOS390(
//				Util.trim(l140m01l.getCaseName()), 82);
//		String BUILDNAME = Util.trimSizeInOS390(
//				Util.trim(l140m01l.getCaseName()), 62);
//		String MGRPCNTRNO = Util.trim(l140m01l.getLoanMasterNo());
//		String ISSUEBRNO = Util.trim(l140m01l.getCaseBrId());
//		Map<String, String> counties = codetypeservice
//				.findByCodeType("counties");
//		CodeType addcodetype = codetypeservice.findByCodeTypeAndCodeValue(
//				"counties" + l140m01l.getCityId(), l140m01l.getAreaId());
//		String SITE1 = counties.get(l140m01l.getCityId());
//		String SITE2 = addcodetype.getCodeDesc();
//		Map<String, Object> Ir48data = eloandbcmsbaseservice
//				.getC101M09ByCityAreaIr48(addcodetype.getCodeDesc3(),
//						addcodetype.getCodeDesc2(), l140m01l.getIr48());
//		String SITE3 = (String) Ir48data.get("SECTION1");
//		String SITE4 = (String) Ir48data.get("SECTION2");
//		String GRPCNTRNO = Util.trim(l140m03a.getGrpCntrNo());
//		BigDecimal OVERAMT = l140m01a.getBLAmt();
//		PTEAMAPP pteamapp = new PTEAMAPP();
//		pteamapp.setCustid(CUSTID);
//		pteamapp.setDupno(DUPNO);
//		pteamapp.setYear(YEAR);
//		pteamapp.setAmtappno(AMTAPPNO);
//		pteamapp.setEfffrom(EFFFROM);
//		pteamapp.setEffend(EFFEND);
//		pteamapp.setTotamt(TOTAMT);
//		pteamapp.setOveramt(OVERAMT);
//		pteamapp.setUpdater(user);
//		pteamapp.setTmestamp(CapDate.getCurrentTimestamp());
//		pteamapp.setGrpcntrno(GRPCNTRNO);
//		pteamapp.setProjectnm(PROJECTNM);
//		pteamapp.setSubcompid(SUBCOMPID);
//		pteamapp.setSubcompnm(SUBCOMPNM);
//		pteamapp.setMgrpcntrno(MGRPCNTRNO);
//		pteamapp.setBuildname(BUILDNAME);
//		pteamapp.setIssuebrno(ISSUEBRNO);
//		pteamapp.setSite1(SITE1);
//		pteamapp.setSite2(SITE2);
//		pteamapp.setSite3(SITE3);
//		pteamapp.setSite4(SITE4);
//		data.add(pteamapp);
//		return data;
//	}

	private List<ELF453> upELF453(List<ELF453> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a,
			String sDate,L120M01A l120m01aNow) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 個金產品種類檔
		List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
				c160m01b.getMainId(), c160m01b.getRefmainId());
		String ELF453_RID = Util.trim(l140m01a.getCustId());// 委繳戶統一編號
		String ELF453_USERNO = Util.trim(l140m01a.getCntrNo());// 用戶號碼
		String ELF453_PRJNO = getUploadCaseNo(l120m01aNow,
				Util.trim(l140m01a.getCntrNo()));//消金案號
		
		//J-111-0227 配合開放入帳及扣款他行帳號調整欄位值
		for (C160S01C c160s01c : c160s01clist) {
			boolean isAppOtherBank = false;//撥款他行
			boolean isAppOtherBankEDDA = false;//撥款他行+同意eDDA (ACH還款)
			if(Util.isNotEmpty(c160s01c.getAppOtherBranchNo()) && 
					Util.isNotEmpty(c160s01c.getAppOtherAccount())){
				isAppOtherBank = true;
				if( Util.equals(Util.trim(c160s01c.getPloanIsNeedACH()), "Y") ){
					isAppOtherBankEDDA = true;
				}
			}
			if (Util.equals(c160s01c.getPayType(), "01") || isAppOtherBank) {
				
				String ELF453_TIX = "803";// 交易代號
				String ELF453_PBANK = "0170000" ;// 發動行代號-固定0170000
				TWNDate twdate = new TWNDate();
				twdate.setTime(sDate);
				String ELF453_CID = "********";// 發動者統一編號-固定********，取消c160s01c.getAchServId()
				String ELF453_RBANK = Util.trim(c160s01c.getAchBranchNo());//Util.trim(c160s01c.getAchBankNo());// 收受行代號 
				String ELF453_RCLNO = Util
						.nullToSpace(c160s01c.getAchAccount());// 委繳戶帳號
				String ELF453_NOTE = ELF453_USERNO;// 發動者專用區-邏輯調整，取消原本c160s01c.getAchServId()+c160m01a.getPackNo()邏輯
				String ELF453_FILLER = "";//備用
				String ELF453_IBANK = Util.trim(c160s01c.getAppOtherBranchNo());// 進帳行代號
				String ELF453_ICLNO = Util.trim(c160s01c.getAppOtherAccount());// 進帳戶帳號
				String ELF453_IID = ELF453_RID;// 進帳戶統一編號

				if(Util.isNotEmpty(ELF453_RCLNO) && ELF453_RCLNO.length() < 16){
					ELF453_RCLNO = Util.addZeroWithValue(ELF453_RCLNO, 16);
				}
				
				if(Util.isNotEmpty(ELF453_ICLNO) && ELF453_ICLNO.length() < 16){
					ELF453_ICLNO = Util.addZeroWithValue(ELF453_ICLNO, 16);
				}
				
				if(isAppOtherBank){//若為撥款他行需調整的欄位邏輯
					if(isAppOtherBankEDDA){//ACH還款(eDDA)
						ELF453_TIX = "824";
						ELF453_FILLER = "EDDA";
					}else{//自行繳款
						ELF453_TIX = "";//僅撥款他行不填
						ELF453_RID = "";//僅撥款他行不填
						ELF453_RBANK = "";//僅撥款他行不填
						ELF453_RCLNO = "";//僅撥款他行不填
						if(Util.equals(c160m01a.getCaseType(), UtilConstants.Usedoc.caseType2.團貸)){
							ELF453_NOTE = Util.trim(c160m01a.getCustId())+ Util.trim(c160m01a.getPackNo());
						}
					}
				}else{//不是撥款他行但是可以進入else的產品就一定是團貸案件走ACH(一般案件未開放選擇ACH)，要給團貸母戶的ID+批號
					ELF453_NOTE = Util.trim(c160m01a.getCustId())+ Util.trim(c160m01a.getPackNo());
				}
				
				ELF453 elf453 = new ELF453();
				
				elf453.setElf453_tix(ELF453_TIX);// 交易代號
				elf453.setElf453_cid(ELF453_CID);
				elf453.setElf453_rbank(ELF453_RBANK);
				elf453.setElf453_rclno(ELF453_RCLNO);
				elf453.setElf453_rid(ELF453_RID);
				elf453.setElf453_userno(ELF453_USERNO);
				elf453.setElf453_admark("A");// 新增或取消
				elf453.setElf453_date(Util.addZeroWithValue(twdate.toTW(), 8));// 日期
				elf453.setElf453_pbank(ELF453_PBANK);
				elf453.setElf453_note(ELF453_NOTE);
				elf453.setElf453_type("N");// 交易型態
				elf453.setElf453_filler(ELF453_FILLER);//備用
				elf453.setElf453_prjno(ELF453_PRJNO);//消金案號
				if(isAppOtherBank){//撥款他行必填欄位
					elf453.setElf453_ibank(ELF453_IBANK);
					elf453.setElf453_iclno(ELF453_ICLNO);
					elf453.setElf453_iid(ELF453_IID);
				}
				
				data.add(elf453);
			}
		}
		return data;
	}

	private List<STUDATA> upSTUDATA(List<STUDATA> data, C160M01A c160m01a,
			C160M01B c160m01b, L120M01A l120m01a, L140M01A l140m01a,
			String sDate, String user) {
		// 個金基本資料檔
		C120S01A c120s01a = c120s01aDao.findByUniqueKey(l120m01a.getMainId(),
				l120m01a.getCustId(), l120m01a.getDupNo());
		c120s01a = Util.isEmpty(c120s01a) ? new C120S01A() : c120s01a;
		// 個金產品種類檔
		List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
				c160m01a.getMainId(), c160m01b.getRefmainId());
		// 主從債務人資料表檔
		List<C160S01B> c160s01blist = c160s01bDao.findByMainIdRefMainId(
				c160m01a.getMainId(), c160m01b.getRefmainId());

		String BRANCH = Util.trim(l140m01a.getOwnBrId());// 分行代號
		String CUSTID = Util.trim(l140m01a.getCustId());// 申貸人身分證字號
		String DUPNO = Util.trim(l140m01a.getDupNo());// 申貸人重複序號
		String CNTRNO = Util.trim(l140m01a.getCntrNo());// 額度序號
		String LNGENM1 = "";// 保證人1姓名
		String LNGEID1 = "";// 保證人1身分證字號
		String LNGENM2 = "";// 保證人2姓名
		String LNGEID2 = "";// 保證人2身分證字號
		String STUREL2 = "";// 保證人1與學生關係
		String STUREL3 = "";// 保證人2與學生關係
		int lngcount = 1;
		for (C160S01B c160s01b : c160s01blist) {
			if (lngcount == 1) {
				LNGENM1 = Util.trim(c160s01b.getRName());
				LNGEID1 = Util.getLeftStr(c160s01b.getRId(), 10);
				STUREL2 = getSTUREL(c160s01b.getRKindD());
				lngcount++;
			} else {
				LNGENM2 = Util.trim(c160s01b.getRName());
				LNGEID2 = Util.getLeftStr(c160s01b.getRId(), 10);
				STUREL3 = getSTUREL(c160s01b.getRKindD());
			}
		}
		String ZIP = Util.getLeftStr(Util.trim(c120s01a.getFZip()), 3);// 學生戶籍地址郵遞區號
		String ADDR1 = Util.trim(c120s01a.getFAddr());// 學生戶籍地址
		for (C160S01C c160s01c : c160s01clist) {
			if (Util.equals(c160s01c.getProdKind(), ProdKindEnum.政策性留學生貸款_36.getCode())) {
				// 留學貸款檔
				L140S02I l140s02i = l140s02iDao.findByUniqueKey(
						c160s01c.getRefmainId(), c160s01c.getSeq());
				l140s02i = (Util.isEmpty(l140s02i) ? new L140S02I() : l140s02i);
				String STUID = Util.trim(l140s02i.getStdCustId());// 學生身分證字號
				String STUDUPNO = Util.trim(l140s02i.getStdDupNo());// 學生重複序號
				String ICMTYPE = Util.trim(l140s02i.getStdICType());// 家庭所得類別
				String GRATSCHL = Util.trim(l140s02i.getEduSchl());// 國內最高學歷代碼
				String GRATDEP = Util.trim(l140s02i.getEduDep());// 畢業科系代碼
				String SCHLLNO = Util.trim(l140s02i.getStdCountry());// 出國地區
				String SCHLNM = Util.trim(l140s02i.getFeduSchl());// 就讀學校名稱
				String DEPTNO = Util.trim(l140s02i.getFeduDep());// 就讀科系代碼
				String EDUCLS = Util.trim(l140s02i.getEduKind());// 教育階段
				String STUREL1 = "0";// 借款人與學生關係
				STUDATA studata = new STUDATA();
				studata.setStuid(STUID);
				studata.setType("1");// 通知事項
				studata.setStudupno(STUDUPNO);
				studata.setIcmtype(ICMTYPE);
				studata.setBranch(BRANCH);
				studata.setCustid(CUSTID);
				studata.setDupno(DUPNO);
				studata.setCntrno(CNTRNO);
				studata.setZip(ZIP);
				studata.setAddr1(Util.trimSizeInOS390(ADDR1, 80));
				studata.setGratschl(GRATSCHL);
				studata.setGratdep(GRATDEP);
				studata.setSchllno(SCHLLNO);
				studata.setSchlnm(SCHLNM);
				studata.setDeptno(DEPTNO);
				studata.setEducls(EDUCLS);
				studata.setSturel1(STUREL1);
				studata.setSturel2(STUREL2);
				studata.setSturel3(STUREL3);
				studata.setUpdater(user);// 資料修改(行員代號)
				studata.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
				studata.setLngenm1(Util.trimSizeInOS390(LNGENM1, 12));
				studata.setLngeid1(LNGEID1);
				studata.setLngenm2(Util.trimSizeInOS390(LNGENM2, 12));
				studata.setLngeid2(LNGEID2);
				data.add(studata);
				if (true) {
					/*
					 * STUDATA 的 PK 為 BRANCH + CUSTID + DUPNO + CNTRNO
					 * 若1個額度下，有2個產品都是36，只能上傳 1筆STUDATA
					 */
					break;
				}
			}
		}
		return data;
	}

	private List<LNF010> upLNF010Case3(List<LNF010> data, C160M01A c160m01a,
			C160M01F c160m01f, C160S01D c160s01d) {
		String LNF010_CUST_ID = Util.trim(c160s01d.getCustId());// 客戶統編
		BigDecimal LNF010_SALARY = Util.parseBigDecimal(c160s01d.getAnnuity());// 年薪(萬元/年)
		BigDecimal LNF010_OTH_REVENUE = Util.parseBigDecimal(c160s01d
				.getOthincome());// 其他所得(萬元/年)
		String LNF010_UPDATE_YN = "Y";// 報送註記
		String LNF010_UPDATER = Util.trim(c160m01f.getCName());// 更新人員
		String LNF010_REVENUE_FG = "Y";// 收入註記
		
		String lnf010_job_class = Util.trim(c160s01d.getJobClass());
		
		if(Util.isEmpty(Util.trim(lnf010_job_class))){ //若無，抓既有的LNF010資料，不要本來LNF010_JOB_CLASS有值，但中鋼整批消貸上傳後把值清空
			List<Map<String, Object>> lnf010_list = misELF386Service.getLNF010data(LNF010_CUST_ID);
			if(lnf010_list.size()>0){
				Map<String, Object> lnf010_map = lnf010_list.get(0);
				lnf010_job_class = Util.trim(MapUtils.getString(lnf010_map, "LNF010_JOB_CLASS"));
			}
		}
		
		LNF010 lnf010 = new LNF010();
		lnf010.setLnf010_cust_id(LNF010_CUST_ID);
		lnf010.setLnf010_update_ts(CapDate.getCurrentTimestamp());// 更新時間
		lnf010.setLnf010_create_dt(CapDate.getCurrentTimestamp());
		lnf010.setLnf010_salary(LNF010_SALARY);
		lnf010.setLnf010_oth_revenue(LNF010_OTH_REVENUE);
		lnf010.setLnf010_job_class(lnf010_job_class);
		lnf010.setLnf010_update_yn(LNF010_UPDATE_YN);
		lnf010.setLnf010_updater(LNF010_UPDATER);
		lnf010.setLnf010_revenue_fg(LNF010_REVENUE_FG);
		data.add(lnf010);
		return data;
	}

	private List<LNF010> upLNF010(List<LNF010> data, C160M01A c160m01a,
			String sDate, String user, HashMap<String, String> lnf010Temp) {

		// 2013/08/08,Rex,#648修改團貸上傳LNF010問題
		for (String csutKey : lnf010Temp.keySet()) {
			String l120m01aMainid = lnf010Temp.get(csutKey);
			String custId = "";
			String dupNo = "";
			if (csutKey.length() == 11) {
				custId = Util.getLeftStr(csutKey, 10);
			} else {
				custId = Util.getLeftStr(csutKey, 8);
			}
			dupNo = Util.getRightStr(csutKey, 1);
			// 個金服務單位檔
			C120S01B c120s01b = c120s01bDao.findByUniqueKey(l120m01aMainid,
					custId, dupNo);
			if (c120s01b != null) {
				String LNF010_UPDATER = user;// 資料更新者
				BigDecimal LNF010_SALARY = CheckPayAmt(c120s01b.getPayAmt());// 年薪(萬元/年)
				BigDecimal LNF010_OTH_REVENUE = CheckPayAmt(c120s01b
						.getOthAmt());// 其他所得(萬元/年)
				String LNF010_JOB_CLASS = Util.trim(c120s01b.getJobType1())
						+ Util.trim(c120s01b.getJobType2())
						+ Util.trim(c120s01b.getJobTitle());// 職業別
				LNF010 lnf010 = new LNF010();
				lnf010.setLnf010_cust_id(csutKey);
				lnf010.setLnf010_update_ts(CapDate.getCurrentTimestamp());// 更新時間
				lnf010.setLnf010_create_dt(CapDate.getCurrentTimestamp());
				lnf010.setLnf010_revenue_fg("Y");
				lnf010.setLnf010_salary(LNF010_SALARY);
				lnf010.setLnf010_oth_revenue(LNF010_OTH_REVENUE);
				lnf010.setLnf010_job_class(LNF010_JOB_CLASS);
				lnf010.setLnf010_update_yn("Y");// 報送註記
				lnf010.setLnf010_updater(LNF010_UPDATER);
				data.add(lnf010);
			}
		}

		return data;
	}

	private void upELF431(C160M01A c160m01a, C160M01B c160m01b,
			L140M01A l140m01a, String user) {
		String c160mainid = Util.trim(c160m01a.getMainId());
		String BRNO = Util.trim(l140m01a.getOwnBrId());
		String CUSTID = Util.trim(l140m01a.getCustId());
		String DUPNO = Util.trim(l140m01a.getDupNo());
		String CNTRNO = Util.trim(l140m01a.getCntrNo());
		List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
				c160mainid, c160m01b.getRefmainId());
		for (C160S01C c160s01c : c160s01clist) {
			int prodKind = Util.parseInt(c160s01c.getProdKind());
			// R6為以下產品代碼才UpDATE 431
			// 28,35,56,59
			switch (prodKind) {
			case 28:
			case 35:
			case 56:
			case 59:
				String KINDNO = new String();
				switch (prodKind) {
				case 28:
					KINDNO = "1";
					break;
				case 35:
					KINDNO = "2";
					break;
				case 56:
					KINDNO = "3";
					break;
				case 59:
					KINDNO = "4";
					break;
				}
				List<Map<String, Object>> elf431list = new ArrayList<Map<String, Object>>();
				elf431list = misdbBaseService.findELF431Data(BRNO, CUSTID,
						DUPNO, CNTRNO, KINDNO);
				// 從ELF431上取得資料後，更新特定欄位再上傳
				if (!elf431list.isEmpty()) {
					for (Map<String, Object> elf431m : elf431list) {
						BigDecimal APPMONEY = c160m01b.getLoanTotAmt();
						BigDecimal ELF431_FAVCNTNO = c160s01c.getLoanAmt();
						BigDecimal ELF431_ORCNTNO1 = APPMONEY
								.subtract(ELF431_FAVCNTNO);
						elf431m.put("ELF431_FAVCNTNO",
								ELF431_FAVCNTNO.toString());// 優惠房貸額度(動撥金額)
						elf431m.put("ELF431_ORCNTNO1",
								ELF431_ORCNTNO1.toString());// 一般房貸額度
						elf431m.put("ELF431_APPRDATE",
								CapDate.getCurrentTimestamp());// 撥款日(ELOAN 撥款日)
						elf431m.put("ELF431_TMESTAMP",
								CapDate.getCurrentTimestamp());// 資料修改日期
						elf431m.put("ELF431_UPDATER", user);// 資料修改人(行員代號)
					}
					MISRows<ELF431> misRowsELF431 = new MISRows<ELF431>(
							ELF431.class);
					misRowsELF431.setValuesByMap(elf431list);
					logger.info("{}=======>{}", "Start", "UPDATEMISELF431");
					upMisToServer(misRowsELF431, "MIS");
					logger.info("{}=======>{}", "END", "UPDATEMISELF431");
				}
			}
		}
	}

	private List<ELF447N> upELF447N(List<ELF447N> data, C160M01A c160m01a,
			C160M01B c160m01b, L120M01A l120m01aNow, L140M01A l140m01a) {
		L140M01M l140m01m = l140m01mDao.findByMainId(Util.trim(l140m01a
				.getMainId()));

		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();
		String ELF447N_UNID = l120m01aNow.getMainId(); // 簽報書文件編號mainId
		String ELF447N_CUSTID = custId; // 借款人統編
		String ELF447N_DUPNO = dupNo; // 重複序號
		String ELF447N_STATUS = "3"; // CHAR(01) NOT NULL,文件狀態 0- 預約 ( 預約到期日內有效
										// ) 1- 報核 ( 報核日起三個月有效 ) 2- 已核定 (
										// 核定日起六個月有效 )A- 已撤銷B- 已婉卻C- 已退件
		String ELF447N_CONTRACT = c160m01b.getCntrNo(); // 額度序號
		ELF447N elf447n = misdbBaseService.findELF447NByUKey(ELF447N_UNID,
				ELF447N_CUSTID, ELF447N_DUPNO, ELF447N_CONTRACT);
		if (!Util.isEmpty(elf447n)) {
			Timestamp ELF447N_TMESTAMP = CapDate.getCurrentTimestamp();// 資料修改日期
			String ELF447N_PROPERTY = ""; // 授信性質別
			BigDecimal ELF447N_CURAMT = l140m01a.getCurrentApplyAmt(); // 現請額度金額
			String ELF447N_CURR = l140m01a.getCurrentApplyCurr(); // 現請額度幣別
			String tproperty = "999";
			for (String x : l140m01a.getProPerty().split(
					UtilConstants.Mark.SPILT_MARK)) {
				if (UtilConstants.Cntrdoc.Property.新做.equals(x)
						|| UtilConstants.Cntrdoc.Property.續約.equals(x)
						|| UtilConstants.Cntrdoc.Property.增額.equals(x)) {
					if (Integer.valueOf(x) < Integer.valueOf(tproperty)) {
						tproperty = x;
					}
				}
			}
			if ("999".equals(tproperty)) {
				for (String x : l140m01a.getProPerty().split(
						UtilConstants.Mark.SPILT_MARK)) {
					if (Integer.valueOf(x) < Integer.valueOf(tproperty)) {
						tproperty = x;
					}
				}
			}
			ELF447N_PROPERTY = tproperty;
			// 利率說明
			String ELF447N_INT_MEMO = "";
			// 是否興建住宅
			String ELF447N_RESIDENTIAL = "";
			// 利率
			BigDecimal ELF447N_INT_Rate = BigDecimal.ZERO;

			BigDecimal ELF447N_LAND_AREA = BigDecimal.ZERO; // ' 土地面積 ',
			Date ELF447N_BUILD_DATE = CapDate.getDate("0001/01/01",
					"yyyy/MM/dd");
			BigDecimal ELF447N_WAIT_MONTH = BigDecimal.ZERO; // ' 預計撥款至動工期間月數 ',
			String ELF447N_LOCATE_CD = ""; // ' 擔保品座落區 ',
			BigDecimal ELF447N_SITE3NO = BigDecimal.ZERO; // ' 座落區段 ',
			String ELF447N_SITE4NO = ""; // ' 座落區村里 ',
			String ELF447N_LAND_TYPE = ""; // ' 土地使用分區 ');

			if (l140m01m != null) {
				ELF447N_LAND_AREA = l140m01m.getAreaLand(); // ' 土地面積 ',

				ELF447N_BUILD_DATE = (Util.isEmpty(l140m01m.getBuildDate()) ? CapDate
						.getDate("0001/01/01", "yyyy/MM/dd") : l140m01m
						.getBuildDate());// 預計取得建照日期(為空值時設定為0001/01/01)

				ELF447N_WAIT_MONTH = l140m01m.getWaitMonth(); // ' 預計撥款至動工期間月數
																// ',
				ELF447N_LOCATE_CD = l140m01m.getLocationCd(); // ' 擔保品座落區 ',
				ELF447N_SITE3NO = l140m01m.getSite3No(); // ' 座落區段 ',
				ELF447N_SITE4NO = l140m01m.getSite4No(); // ' 座落區村里 ',
				ELF447N_LAND_TYPE = l140m01m.getLandType(); // ' 土地使用分區 ');
			}

			// 產品
			String ELF447N_PROD_CLASS = "";
			// 科目八碼
			String ELF447N_ACT_CODE = "";
			// 用途別
			String ELF447N_PURPOSE = "";
			if (LMSUtil.isClsCase(l120m01aNow)) {
				// 國內個金
				Set<String> prodKindSet = new HashSet<String>();
				Set<String> subjCodeSet = new HashSet<String>();
				Set<String> lnPursSet = new HashSet<String>();
				// 個金產品種類檔
				List<C160S01C> c160s01cs = c160s01cDao.findByMainIdRefMainid(
						c160m01a.getMainId(), c160m01b.getRefmainId());
				int count = 0;
				for (C160S01C c160s01c : c160s01cs) {
					count++;
					L140S02A l140s02a = l140s02aDao.findByUniqueKey(
							c160s01c.getRefmainId(), c160s01c.getSeq());
					if (count == 1) {
						// 取得第一筆產品種類的首段適用利率、利率、是否屬興建住宅
						L140S02C l140s02c = l140s02cDao.findByUniqueKey(
								c160s01c.getRefmainId(), c160s01c.getSeq());
						if (l140s02c != null) {
							ELF447N_INT_Rate = l140s02c.getSubmitRate();
						}
						ELF447N_INT_MEMO = Util.trimSizeInOS390(
								c160s01c.getRateDesc(), 560);
						ELF447N_RESIDENTIAL = l140s02a.getResidential();
					}
					// -------設定不重覆的產品種類、會計科目代碼 、用途別----------
					prodKindSet.add(c160s01c.getProdKind());
					subjCodeSet.add(c160s01c.getSubjCode());
					lnPursSet.add(l140s02a.getLnPurs());
					// ---------------------------------------------------------
				}
				// -------取出不重覆的產品種類、會計科目代碼 、用途別----------
				StringBuffer prodKindStr = new StringBuffer();
				for (String prod : prodKindSet) {
					prodKindStr
							.append(prodKindStr.length() > 0 ? UtilConstants.Mark.MARK
									: "");
					prodKindStr.append(prod);
				}
				StringBuffer subjCodeStr = new StringBuffer();
				for (String subjCode : subjCodeSet) {
					subjCodeStr
							.append(subjCodeStr.length() > 0 ? UtilConstants.Mark.MARK
									: "");
					subjCodeStr.append(subjCode);
				}
				StringBuffer lnPursStr = new StringBuffer();
				for (String lnPurs : lnPursSet) {
					lnPursStr
							.append(lnPursStr.length() > 0 ? UtilConstants.Mark.MARK
									: "");
					lnPursStr.append(lnPurs);
				}
				ELF447N_PROD_CLASS = Util.trimSizeInOS390(
						prodKindStr.toString(), 255);
				ELF447N_ACT_CODE = Util.trimSizeInOS390(subjCodeStr.toString(),
						255);
				ELF447N_PURPOSE = Util.trimSizeInOS390(lnPursStr.toString(),
						255);
				// ---------------------------------------------------------
			} else {
				ELF447N_RESIDENTIAL = Util.trim(l140m01a.getResidential());
				// 企金
				for (L140M01B l140m01b : l140m01a.getL140m01b()) {
					if (UtilConstants.Cntrdoc.l140m01bItemType.利費率
							.equals(l140m01b.getItemType())) {
						ELF447N_INT_MEMO = Util.trimSizeInOS390(
								l140m01b.getItemDscr(), 560);
						break;
					}
				}
			}
			elf447n.setElf447n_status(ELF447N_STATUS);
			elf447n.setElf447n_tmestamp(ELF447N_TMESTAMP);
			elf447n.setElf447n_property(ELF447N_PROPERTY);
			elf447n.setElf447n_curamt(ELF447N_CURAMT);
			elf447n.setElf447n_curr(ELF447N_CURR);
			elf447n.setElf447n_int_rate(ELF447N_INT_Rate);
			elf447n.setElf447n_int_memo(ELF447N_INT_MEMO);
			elf447n.setElf447n_prod_class(ELF447N_PROD_CLASS);
			elf447n.setElf447n_act_code(ELF447N_ACT_CODE);
			elf447n.setElf447n_purpose(ELF447N_PURPOSE);
			elf447n.setElf447n_residence(ELF447N_RESIDENTIAL);

			elf447n.setElf447n_land_area(ELF447N_LAND_AREA); // ' 土地面積 ',
			elf447n.setElf447n_build_date(ELF447N_BUILD_DATE); // ' 預計取得建照日期 ',
			elf447n.setElf447n_wait_month(ELF447N_WAIT_MONTH); // ' 預計撥款至動工期間月數
																// ',
			elf447n.setElf447n_locate_cd(ELF447N_LOCATE_CD); // ' 擔保品座落區 ',
			elf447n.setElf447n_site3no(ELF447N_SITE3NO); // ' 座落區段 ',
			elf447n.setElf447n_site4no(ELF447N_SITE4NO); // ' 座落區村里 ',
			elf447n.setElf447n_land_type(ELF447N_LAND_TYPE); // ' 土地使用分區 ');
			data.add(elf447n);
		}
		return data;
	}

	private List<ELF447N> upELF447NCase3(List<ELF447N> data, C160M01A c160m01a,
			C160M01F c160m01f, C160S01D c160s01d, L120M01A l120m01a) {
		String ELF447N_UNID = Util.trim(l120m01a.getMainId());// 文件編號 UNID
		String ELF447N_CLASS = "1";// 報案類別
		String ELF447N_CUSTID = Util.getLeftStr(
				Util.trim(c160s01d.getCustId()), 10);// 借款人統編
		String ELF447N_DUPNO = Util.getRightStr(
				Util.trim(c160s01d.getCustId()), 1);// 重複序號
		String ELF447N_STATUS = "Y";// 文件狀態
		Date ELF447N_STATUS_DT = c160m01f.getFirstDate();// 狀態日
		String ELF447N_CONTRACT = Util.trim(c160s01d.getCntrNo());// 額度序號
		String ELF447N_PROJECT_NO = getUploadCaseNo(l120m01a, ELF447N_CONTRACT);// 案件編號
		String ELF447N_BRANCH = Util.trim(c160m01a.getCaseBrId());// 主辦分行代號
		String ELF447N_PROCESS_BR = Util.trim(c160m01a.getOwnBrId());// 目前案件所在分行代號
		String ELF447N_FACT_TYPE = "51";// 額度控管種類
		String ELF447N_SYSTYPE = "2";// 系統類別
		String ELF447N_CASELEVEL = "9";// 授權等級
		String ELF447N_PROPERTY = "1";// 授信性質別
		BigDecimal ELF447N_CURAMT = c160s01d.getLoanTotAmt();// 現請額度金額
		String ELF447N_CURR = "TWD";// 現請額度幣別
		String ELF447N_RISK_CNTRY = "TW";// 風險國別
		String ELF447N_RISK_AREA = "0";// 風險區域別
		String ELF447N_BUS_CD = "600000";// 行業對象別
		String ELF447N_BUILD_NAME = Util.trim(c160m01f.getCustName());// 團貸案名稱
		String ELF447N_PROD_CLASS = "7";// 產品種
		String ELF447N_ACT_CODE = Util.trim(c160m01f.getSubjCode());// 動用科目
		String ELF447N_PURPOSE = "4";// 用途別

		BigDecimal ELF447N_LAND_AREA = BigDecimal.ZERO; // ' 土地面積 ',
		Date ELF447N_BUILD_DATE = CapDate.getDate("0001/01/01", "yyyy/MM/dd");
		BigDecimal ELF447N_WAIT_MONTH = BigDecimal.ZERO; // ' 預計撥款至動工期間月數 ',
		String ELF447N_LOCATE_CD = ""; // ' 擔保品座落區 ',
		BigDecimal ELF447N_SITE3NO = BigDecimal.ZERO; // ' 座落區段 ',
		String ELF447N_SITE4NO = ""; // ' 座落區村里 ',
		String ELF447N_LAND_TYPE = ""; // ' 土地使用分區 ');

		// String ELF447N_RESIDENTIAL="N";
		ELF447N elf447n = new ELF447N();
		elf447n.setElf447n_unid(ELF447N_UNID);
		elf447n.setElf447n_project_no(ELF447N_PROJECT_NO);
		elf447n.setElf447n_class(ELF447N_CLASS);
		elf447n.setElf447n_custid(ELF447N_CUSTID);
		elf447n.setElf447n_dupno(ELF447N_DUPNO);
		elf447n.setElf447n_status(ELF447N_STATUS);
		elf447n.setElf447n_status_dt(ELF447N_STATUS_DT);
		elf447n.setElf447n_contract(ELF447N_CONTRACT);
		elf447n.setElf447n_branch(ELF447N_BRANCH);
		elf447n.setElf447n_process_br(ELF447N_PROCESS_BR);
		elf447n.setElf447n_fact_type(ELF447N_FACT_TYPE);
		elf447n.setElf447n_systype(ELF447N_SYSTYPE);
		elf447n.setElf447n_caselevel(ELF447N_CASELEVEL);
		elf447n.setElf447n_tmestamp(CapDate.getCurrentTimestamp());
		elf447n.setElf447n_property(ELF447N_PROPERTY);
		elf447n.setElf447n_curamt(ELF447N_CURAMT);
		elf447n.setElf447n_curr(ELF447N_CURR);
		elf447n.setElf447n_risk_cntry(ELF447N_RISK_CNTRY);
		elf447n.setElf447n_risk_area(ELF447N_RISK_AREA);
		elf447n.setElf447n_bus_cd(ELF447N_BUS_CD);
		elf447n.setElf447n_build_name(ELF447N_BUILD_NAME);
		elf447n.setElf447n_prod_class(ELF447N_PROD_CLASS);
		elf447n.setElf447n_act_code(ELF447N_ACT_CODE);
		elf447n.setElf447n_purpose(ELF447N_PURPOSE);
		// elf447n.setElf447n_residential(ELF447N_RESIDENTIAL);

		elf447n.setElf447n_land_area(ELF447N_LAND_AREA); // ' 土地面積 ',
		elf447n.setElf447n_build_date(ELF447N_BUILD_DATE); // ' 預計取得建照日期 ',
		elf447n.setElf447n_wait_month(ELF447N_WAIT_MONTH); // ' 預計撥款至動工期間月數 ',
		elf447n.setElf447n_locate_cd(ELF447N_LOCATE_CD); // ' 擔保品座落區 ',
		elf447n.setElf447n_site3no(ELF447N_SITE3NO); // ' 座落區段 ',
		elf447n.setElf447n_site4no(ELF447N_SITE4NO); // ' 座落區村里 ',
		elf447n.setElf447n_land_type(ELF447N_LAND_TYPE); // ' 土地使用分區 ');
		data.add(elf447n);
		return data;
	}

	private List<QUOTSUB> upQUOTSUBCase3(List<QUOTSUB> data, C160M01A c160m01a,
			C160M01F c160m01f, C160S01D c160s01d) {
		String CUSTID = Util.getLeftStr(Util.trim(c160s01d.getCustId()), 10);// 借款人統編
		String DUPNO = Util.getRightStr(Util.trim(c160s01d.getCustId()), 1);// 重複序號
		String CNTRNO = Util.trim(c160s01d.getCntrNo());// 額度序號
		Date SDATE = c160m01f.getApproveTime();// 日期
		String LOANTP = "321";// 科(子)目-簡碼
		String CHGFLAG = "1";// 科(子)目變更註記
		String OLDCURR = "";// 原限額幣別
		BigDecimal OLDQUOTA = new BigDecimal(0);// 原限額
		String NEWCURR = "TWD";// 新限額幣別
		BigDecimal NEWQUOTA = c160s01d.getLoanTotAmt();// 新限額
		String LNGU = "N";// 擔保代號
		String UPDATER = Util.trim(c160m01f.getCName());// 資料修改人（行員代號）
		QUOTSUB quotsub = new QUOTSUB();
		quotsub.setCustid(CUSTID);
		quotsub.setDupno(DUPNO);
		quotsub.setCntrno(CNTRNO);
		quotsub.setSdate(SDATE);
		quotsub.setLoantp(LOANTP);
		quotsub.setChgflag(CHGFLAG);
		quotsub.setOldcurr(OLDCURR);
		quotsub.setOldquota(OLDQUOTA);
		quotsub.setNewcurr(NEWCURR);
		quotsub.setNewquota(NEWQUOTA);
		quotsub.setLngu(LNGU);
		quotsub.setUpdater(UPDATER);
		quotsub.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
		data.add(quotsub);
		return data;
	}

	/**
	 * ELF384(QUOTSUB)科(子)目及其限額檔
	 * 
	 */
	private List<QUOTSUB> upQUOTSUB(List<QUOTSUB> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String sDate, String user) {
		String CUSTID = Util.trim(l140m01a.getCustId());// 借款人統編
		String DUPNO = Util.trim(l140m01a.getDupNo());// 重複序號
		String CNTRNO = Util.trim(l140m01a.getCntrNo());// 額度序號

		String LOANTP = "";// 科(子)目-簡碼
		String OLDCURR = "";// 原限額幣別
		BigDecimal OLDQUOTA = new BigDecimal(0);// 原限額
		String NEWCURR = "";
		BigDecimal NEWQUOTA = new BigDecimal(0);
		String LNGU = "";
		String CHGFLAG = "";

		String l140m01aMainId = Util.trim(l140m01a.getMainId());
		List<C900M01F> c900m01fList = c900m01fDao.findByIndex02(l140m01aMainId,
				"1");

		if (c900m01fList == null || c900m01fList.size() == 0) {
			// 個金產品種類檔
			List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
					c160m01b.getMainId(), c160m01b.getRefmainId());
			Map<String, Object> SubjCode = new HashMap<String, Object>();// 科子目動撥金額、幣別、承作性質統計用
			List<String> stringlist = new ArrayList<String>();// 科子目統計用
			for (C160S01C c160s01c : c160s01clist) {
				String trmpsubjcode = prodservice.getSubject8to3(Util
						.trim(c160s01c.getSubjCode()));// 科目代碼轉碼八轉三
				if (Util.isEmpty(SubjCode.get(trmpsubjcode))) {
					SubjCode.put(trmpsubjcode, c160s01c.getLoanAmt());// 設定動撥金額(Key=科目代碼)
					SubjCode.put(trmpsubjcode + "Curr", c160s01c.getLoanCurr());// 設定動撥幣別(Key=科目代碼+Curr)
					SubjCode.put(trmpsubjcode + "ChgFlag",
							chgQuotsubChgFlag(c160s01c.getProperty()));// 設定轉碼承作性質(Key=科目代碼+ChgFlag)
					stringlist.add(trmpsubjcode);
				} else {
					Integer oldchgflag = Util.parseInt(SubjCode
							.get(trmpsubjcode + "ChgFlag"));// 舊承作性質
					Integer newchgflag = Util
							.parseInt(chgQuotsubChgFlag(c160s01c.getProperty()));// 新承作性質
					if (newchgflag < oldchgflag) {// 承作性質取最小值
						SubjCode.put(trmpsubjcode + "ChgFlag",
								newchgflag.toString());
					}
					BigDecimal Sjc160s01camt = (BigDecimal) SubjCode
							.get(trmpsubjcode);// 取得原有動撥金額
					BigDecimal c160s01camt = c160s01c.getLoanAmt();// 取得動撥金額
					SubjCode.put(trmpsubjcode, Sjc160s01camt.add(c160s01camt));// 加總動撥金額且覆蓋原有動撥金額
				}
			}
			for (String subjs : stringlist) {
				LNGU = prodservice.isGuarantee(prodservice
						.getSubject3to8(subjs)) ? "S" : "N";// 擔保代號
				NEWCURR = (String) SubjCode.get(subjs + "Curr");// 新限額幣別
				NEWQUOTA = (BigDecimal) SubjCode.get(subjs);// 新限額
				CHGFLAG = (String) SubjCode.get(subjs + "ChgFlag");// 科(子)目變更註記
				if (Util.equals(CHGFLAG, "5")) {// 若為科(子)目變更註記其他(5)則轉為0
					CHGFLAG = "0";
				}

				LOANTP = subjs;// 科(子)目-簡碼
				// ------------------------------取得MIS上QUOTSUB資料--------------------------------
				Map<String, Object> MaxSDate = misquotsubseervice
						.selByUniqueKeyMaxSdate2(CUSTID, DUPNO, CNTRNO, LOANTP);
				if (!Util.isEmpty(MaxSDate)) {
					if (Util.isNotEmpty(MaxSDate.get("MSDATE"))) {
						Map<String, Object> maplist = misquotsubseervice
								.selByUniqueKey2(CUSTID, DUPNO, CNTRNO, LOANTP,
										MaxSDate.get("MSDATE").toString());
						OLDCURR = maplist.get("NEWCURR").toString();// 將MIS上新限額幣別寫入舊限額幣別
						OLDQUOTA = Util
								.parseBigDecimal(maplist.get("NEWQUOTA"));// 將MIS上新限額寫入舊限額
					}
				}
				QUOTSUB quotsub = new QUOTSUB();
				quotsub.setCustid(CUSTID);
				quotsub.setDupno(DUPNO);
				quotsub.setCntrno(CNTRNO);
				quotsub.setSdate(CapDate.getCurrentTimestamp());// 日期
				quotsub.setLoantp(LOANTP);
				quotsub.setOldcurr(OLDCURR);
				quotsub.setOldquota(OLDQUOTA);
				quotsub.setNewcurr(NEWCURR);
				quotsub.setNewquota(NEWQUOTA);
				quotsub.setLngu(LNGU);
				quotsub.setChgflag(CHGFLAG);
				quotsub.setUpdater(user);// 資料修改人（行員代號）
				quotsub.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
				data.add(quotsub);
			}
		} else {
			for (C900M01F c900m01f : c900m01fList) {
				LOANTP = Util.trim(c900m01f.getSubject());
				NEWCURR = Util.trim(c900m01f.getLmtCurr());
				NEWQUOTA = (BigDecimal) c900m01f.getLmtAmt();

				if (Integer.valueOf(LOANTP.substring(0, 1)) % 2 == 1) {
					LNGU = "N";
				} else {
					LNGU = "S";
				}

				// LNGU = prodservice.isGuarantee(LOANTP) ? "S" : "N";// 擔保代號
				CHGFLAG = "1";

				QUOTSUB quotsub = new QUOTSUB();
				quotsub.setCustid(CUSTID);
				quotsub.setDupno(DUPNO);
				quotsub.setCntrno(CNTRNO);
				quotsub.setSdate(CapDate.getCurrentTimestamp());// 日期
				quotsub.setLoantp(LOANTP);
				quotsub.setOldcurr(OLDCURR);
				quotsub.setOldquota(OLDQUOTA);
				quotsub.setNewcurr(NEWCURR);
				quotsub.setNewquota(NEWQUOTA);
				quotsub.setLngu(LNGU);
				quotsub.setChgflag(CHGFLAG);
				quotsub.setUpdater(user);// 資料修改人（行員代號）
				quotsub.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
				data.add(quotsub);
			}
		}

		return data;

	}

	private List<LNF195> upLNF195(List<LNF195> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String sDate) {
		List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
				c160m01b.getMainId(), c160m01b.getRefmainId());
		String LNF195_BR_NO = Util.trim(l140m01a.getOwnBrId());// 分行別
		String LNF195_CONTRACT_Y = Util.trim(l140m01a.getCntrNo());// 循環額度序號
		for (C160S01C c160s01c : c160s01clist) {
			L140S02A l140s02a = l140s02aDao.findByUniqueKey(
					c160s01c.getRefmainId(), c160s01c.getSeq());
			l140s02a = Util.isEmpty(l140s02a) ? new L140S02A() : l140s02a;
			if (Util.equals(l140s02a.getChkUsed(), "Y")) {
				// 流用(長擔)額度序號檔
				List<L140S02B> l140s02blist = l140s02bDao.findByMainIdSeq(
						c160m01b.getRefmainId(), c160s01c.getSeq());
				for (L140S02B l140s02b : l140s02blist) {
					String LNF195_CONTRACT_N = Util.trim(l140s02b.getCntrNo());// 不循環額度序號
					LNF195 lnf195 = new LNF195();
					lnf195.setLnf195_br_no(LNF195_BR_NO);
					lnf195.setLnf195_contract_n(LNF195_CONTRACT_N);
					lnf195.setLnf195_contract_y(LNF195_CONTRACT_Y);
					lnf195.setLnf195_eln_status("1");// E-LOAN 狀態
					lnf195.setLnf195_timestamp(CapDate.getCurrentTimestamp());// 資料維護日期
					data.add(lnf195);
				}
			}
		}
		return data;
	}

	@SuppressWarnings("unused")
	private List<ELF461> upELF461(List<ELF461> data, C160M01A c160m01a,
			C160M01B c160m01b, L120M01A l120m01a, L140M01A l140m01a,
			String sDate, String user) {
		ELF461 elf461 = new ELF461();
		String ELF461_CUSTID = Util.trim(l140m01a.getCustId());
		String ELF461_DupNo = Util.trim(l140m01a.getDupNo());
		String ELF461_CNTRNO = Util.trim(l140m01a.getCntrNo());
		String ELF461_BRNO = Util.trim(l140m01a.getOwnBrId());
		String ELF461_STATUS = getELF461Status(Util.trim(l140m01a
				.getDocStatus()));
		TWNDate ELF461_APPRDATE = new TWNDate();
		ELF461_APPRDATE.setTime(l140m01a.getApproveTime().getTime());
		String ELF461_APPRYY = Util.addZeroWithValue(
				ELF461_APPRDATE.get(Calendar.YEAR), 4);
		String ELF461_APPRMM = ELF461_APPRDATE.getDate("MM");
		String ELF461_CTYPE = Util.trim(l120m01a.getDocKind());
		Date ELF461_GUTCDATE = l140m01a.getGutCutDate();
		String ELF461_PROJNO = Util.trim(l120m01a.getCaseNo());
		String ELF461_PROPERTY = Util.trim(l140m01a.getProPerty());
		elf461.setElf461_custid(ELF461_CUSTID);
		elf461.setELF461_DupNo(ELF461_DupNo);
		elf461.setElf461_cntrno(ELF461_CNTRNO);
		elf461.setElf461_brno(ELF461_BRNO);
		elf461.setElf461_status(ELF461_STATUS);
		elf461.setElf461_appryy(ELF461_APPRYY);
		elf461.setElf461_apprmm(ELF461_APPRMM);
		elf461.setElf461_ctype(ELF461_CTYPE);
		elf461.setElf461_updater(user);
		elf461.setElf461_tmestamp(CapDate.getCurrentTimestamp());
		elf461.setElf461_gutcdate(ELF461_GUTCDATE);
		elf461.setElf461_projno(ELF461_PROJNO);
		elf461.setElf461_property(ELF461_PROPERTY);
		data.add(elf461);
		return data;
	}

	private List<PQUOTNF> upPQUOTNF(List<PQUOTNF> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String sDate, String user) {
		if (!Util.isEmpty(l140m01a)) {
			String BRNO = Util.trim(l140m01a.getOwnBrId());// 分行別
			String CUSTID = Util.trim(l140m01a.getCustId());// 借款人統編
			String DUPNO = Util.trim(l140m01a.getDupNo());// 重複序號
			String CNTRNO = Util.trim(l140m01a.getCntrNo());// 額度序號
			List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
					c160m01a.getMainId(), c160m01b.getRefmainId());

			String PRODC = "";
			String MEMO1 = "";
			String LOANNO = "";

			HashSet<String> tnfOtherSet = new HashSet<String>();

			for (C160S01C c160s01c : c160s01clist) {
				L140S02F l140s02f = l140s02fDao.findByUniqueKey(
						l140m01a.getMainId(), c160s01c.getSeq());
				l140s02f = (Util.isEmpty(l140s02f) ? new L140S02F() : l140s02f);

				L140S02A l140s02a = l140s02aDao.findByUniqueKey(
						c160s01c.getRefmainId(), c160s01c.getSeq());

				if (Util.isNotEmpty(l140s02a.getLoanNo())) {
					LOANNO = l140s02a.getLoanNo();
				} else {
					LOANNO = Util.addZeroWithValue(c160s01c.getCaseSeq(), 5);
				}

				// ----------------------------------免收條件-------------------------------
				char[] nfeecons = new char[] { ' ', ' ', ' ', ' ', ' ', ' ',
						' ', ' ', ' ' };

				if (Util.isNotEmpty(l140s02f.getTnf())) {
					for (String Tnf : l140s02f.getTnf().split(
							UtilConstants.Mark.SPILT_MARK)) {
						if (Util.equals(Tnf, 1)) {
							nfeecons[0] = '1';
						} else if (Util.equals(Tnf, 2)) {
							nfeecons[1] = '1';
						} else if (Util.equals(Tnf, 3)) {
							nfeecons[8] = '1';
						}
					}
					PRODC = c160s01c.getProdKind();
				}
				if (Util.isNotEmpty(l140s02f.getTnfOther())) {
					tnfOtherSet.add(l140s02f.getTnfOther());
					PRODC = c160s01c.getProdKind();
				}

				String NFEECON = new String(nfeecons, 0, nfeecons.length);// 免收條件
				if (Util.equals(Util.trim(NFEECON), "")) {
					NFEECON = "";
				}
				// ---
				MEMO1 = StringUtils.join(tnfOtherSet, ",");

				if (!Util.isEmpty(NFEECON) || !Util.isEmpty(MEMO1)) {// 選擇免收條件或免收條件其他不為空白時上傳
					PQUOTNF pquotnf = new PQUOTNF();
					pquotnf.setLoanNo(LOANNO);

					pquotnf.setBrno(BRNO);
					pquotnf.setCustid(CUSTID);
					pquotnf.setDupno(DUPNO);
					pquotnf.setCntrno(CNTRNO);
					pquotnf.setProdc(PRODC);
					pquotnf.setNfeecon(NFEECON);
					pquotnf.setMemo1(Util.trimSizeInOS390(MEMO1, 40));
					pquotnf.setUpdater(user); // 資料修改人(行員代號) // Updater
					pquotnf.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
																		// TMESTAMP
					data.add(pquotnf);
				}
			}

		}
		return data;
	}

	private List<QUOTAINF> upQUOTAINFCase3(List<QUOTAINF> data,
			C160M01A c160m01a, C160M01F c160m01f, C160S01D c160s01d) {
		String CUSTID = Util.getLeftStr(Util.trim(c160s01d.getCustId()), 10);// 借款人統一編號
		String DUPNO = Util.getRightStr(Util.trim(c160s01d.getCustId()), 1);// 重複序號
		String BRANCH = Util.trim(c160m01a.getCaseBrId());// 分行代號
		String CNTRNO = Util.trim(c160s01d.getCntrNo());// 額度序號
		TWNDate twdate = new TWNDate();
		twdate.setTime(Util.getDate(c160m01f.getFirstDate()));
		String APPDATE = twdate.toTW('/');// 申請日
		twdate = new TWNDate();
		twdate.setTime(Util.getDate(c160m01f.getApproveTime()));
		String APRDATE = twdate.toTW('/');// 核准日
		String CUSTNAME = Util.trim(c160s01d.getCustName());// 主要借款人姓名
		String BOSSID = Util.trim(c160m01f.getOmgrNo());// 核准主管代號
		String BOSSNAME = Util.trim(c160m01f.getOmgrName());// 核准主管姓名
		String ACCNO = Util.trim(c160s01d.getAccNo());// 進帳方式－1.存款帳號
		BigDecimal RCTAMT1 = c160s01d.getLoanTotAmt();// 進帳方式－1.進帳金額
		String UNID = Util.trim(c160m01a.getOid());// 審核書/簽報書文件ID
		String NEWCASE = "Y";// 新貸
		String UPDATER = Util.trim(c160m01f.getCName());// 資料修改人（行員代號）
		QUOTAINF quotainf = new QUOTAINF();
		quotainf.setCustid(CUSTID);
		quotainf.setDupno(DUPNO);
		quotainf.setBranch(BRANCH);
		quotainf.setCntrno(CNTRNO);
		quotainf.setAppdate(APPDATE);
		quotainf.setAprdate(APRDATE);
		quotainf.setCustname(CUSTNAME);
		quotainf.setBossid(BOSSID);
		quotainf.setBossname(Util.trimSizeInOS390(
				userInfoService.getUserName(BOSSNAME), 100));
		quotainf.setAccno(ACCNO);
		quotainf.setRctamt1(RCTAMT1);
		quotainf.setUnid(UNID);
		quotainf.setNewcase(NEWCASE);
		quotainf.setUpdater(UPDATER);
		quotainf.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
		quotainf.setRctSwft1("TWD");
		//~~~~
		data.add(quotainf);
		return data;
	}

	private List<QUOTAINF> upQUOTAINF(List<QUOTAINF> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String bossId, String sDate,
			String user) {
		String UNID = Util.trim(c160m01a.getMainId());// 審核書/簽報書文件ID
		String CUSTID = Util.trim(l140m01a.getCustId());// 借款人統一編號
		String DUPNO = Util.trim(l140m01a.getDupNo());// 重複序號
		String BRANCH = Util.trim(l140m01a.getOwnBrId());// 分行代號
		String CNTRNO = Util.trim(l140m01a.getCntrNo());// 額度序號
		String CUSTNAME = Util.trim(l140m01a.getCustName());// 主要借款人姓名
		String BOSSID = LMSUtil.forFiveBossId(Util.addZeroWithValue(bossId, 5));// 核准主管代號
		String BOSSNAME = userInfoService.getUserName(bossId);// 核准主管姓名
		// ---------------------申請日及核准日(須轉為民國年)---------------------------
		TWNDate twdate = new TWNDate();
		twdate.setTime(Util.getDate(l140m01a.getCaseDate()));
		String APPDATE = twdate.toTW('/');// 申請日
		twdate = new TWNDate();
		twdate.setTime(Util.getDate(l140m01a.getApproveTime()));
		String APRDATE = twdate.toTW('/');// 核准日
		// -----------------------C160S01C產品種類檔只取第一筆進行上傳-----------------------
		List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
				c160m01a.getMainId(), c160m01b.getRefmainId());
		C160S01C c160s01c = new C160S01C();
		if (c160s01clist.size() > 0) {
			c160s01c = c160s01clist.get(0);
		}
		String ACCNO = c160s01c.getAccNo();// 進帳方式－1.存款帳號
		BigDecimal RCTAMT1 = (Util.isEmpty(c160s01c.getRctAMT()) ? new BigDecimal(
				0) : c160s01c.getRctAMT());// 進帳方式－1.進帳金額
		String NEWCASE = Util.equals(c160s01c.getProperty(), "1") ? "Y" : "N";// 新貸(若該承作性質為新作則新貸
																				// 設定為Y否則為N)
		// 寫入Bean
		QUOTAINF quotainf = new QUOTAINF();
		quotainf.setCustid(CUSTID);
		quotainf.setDupno(DUPNO);
		quotainf.setBranch(BRANCH);
		quotainf.setCntrno(CNTRNO);
		quotainf.setAppdate(APPDATE);
		quotainf.setAprdate(APRDATE);
		quotainf.setCustname(Util.trimSizeInOS390(CUSTNAME, 100));
		quotainf.setBossid(BOSSID);
		quotainf.setBossname(BOSSNAME);
		quotainf.setAccno(ACCNO);
		quotainf.setRctamt1(RCTAMT1);
		quotainf.setUnid(UNID);
		quotainf.setNewcase(NEWCASE);
		quotainf.setUpdater(user);// 資料修改人（行員代號）
		quotainf.setTmestamp(CapDate.getCurrentTimestamp()); // 資料修改日期
		quotainf.setRctSwft1(c160s01c.getLoanCurr());
		//~~~~
		data.add(quotainf);
		return data;
	}

	private List<IQUOTAPP> upIQUOTAPPCase3(List<IQUOTAPP> data,
			C160M01A c160m01a, C160M01F c160m01f, C160S01D c160s01d, String user) {

		String QUOTANO = Util.trim(c160s01d.getCntrNo());// 額度序號
		String CUSTID = Util.getLeftStr(Util.trim(c160s01d.getCustId()), 10);// 身份證／統編
		String DUPNO = Util.getRightStr(Util.trim(c160s01d.getCustId()), 1);// 重複序號
		String ICBCNO = Util.trim(c160m01f.getCName());// 授信經辦行員代號
		String OMGRNAME = Util.trim(c160m01f.getOmgrName());// 初放主管姓名
		String FMGRNAME = Util.trim(c160m01f.getFrgrName());// / 敘作主管姓名
		String APPROLVL = "9";// 授權等級
		// ------------------------------------------
		IQUOTAPP iquotapp = new IQUOTAPP();
		iquotapp.setQuotano(QUOTANO);
		iquotapp.setCustid(CUSTID);
		iquotapp.setDupno(DUPNO);
		iquotapp.setIcbcno(Util.trimSizeInOS390(ICBCNO, 5));//
		iquotapp.setOmgrname(Util.trimSizeInOS390(OMGRNAME, 100));
		iquotapp.setFmgrname(Util.trimSizeInOS390(FMGRNAME, 100));
		iquotapp.setApprolvl(APPROLVL);
		iquotapp.setUpdater(user);// 資料修改人（行員代號）
		iquotapp.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
		data.add(iquotapp);
		return data;
	}

	private List<IQUOTAPP> upIQUOTAPP(List<IQUOTAPP> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String apprId, String bossId,
			String sDate, String user) {
		if (!Util.isEmpty(l140m01a)) {
			String QUOTANO = Util.trim(l140m01a.getCntrNo());// 額度序號
			String CUSTID = Util.trim(l140m01a.getCustId());// 身份證／統編
			String DUPNO = Util.trim(l140m01a.getDupNo());// 重複序號
			String ICBCNO = Util.trim(apprId);// 授信經辦行員代號
			String FMGRNAME = Util.trim(userInfoService.getUserName(bossId));// 敘作主管姓名
			String APPROLVL = Util.trim(c160m01a.getCaseLvl());// 授權等級 APPROLVL
			// --------------------初放主管姓名---------------------
			String OMGRNAME = Util.trim(userInfoService.getUserName(bossId));// 初放主管姓名
			IQUOTAPP iquotapptemp = misiquotappservice
					.findIQUOTAPPByUKey(QUOTANO);
			if (!Util.isEmpty(iquotapptemp)) {// 先查詢IQUOTAPP上是否有同一筆額度序號資料
				if (!Util.isEmpty(iquotapptemp.getOmgrname())) {// 若有則看初放主管姓名是否有值
					OMGRNAME = iquotapptemp.getOmgrname();// 有值則沿用MIS上資料
				}
			}
			// ------------------------------------------
			IQUOTAPP iquotapp = new IQUOTAPP();
			iquotapp.setQuotano(QUOTANO);
			iquotapp.setCustid(CUSTID);
			iquotapp.setDupno(DUPNO);
			iquotapp.setIcbcno(LMSUtil.forFiveBossId(ICBCNO));
			iquotapp.setCname(Util.trimSizeInOS390(
					userInfoService.getUserName(ICBCNO), 100));// 授信經辦姓名
			iquotapp.setOmgrname(Util.trimSizeInOS390(OMGRNAME, 100));
			iquotapp.setFmgrname(Util.trimSizeInOS390(FMGRNAME, 100));
			iquotapp.setApprolvl(APPROLVL);
			iquotapp.setUpdater(user);// 資料修改人（行員代號）
			iquotapp.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
			data.add(iquotapp);

		}
		return data;
	}

	private List<ELLNGTEE> upELLNGTEECase3(List<ELLNGTEE> data,
			C160M01A c160m01a, C160M01F c160m01f, C160S01D c160s01d, String user) {
		String BRNO = Util.trim(c160m01a.getCaseBrId());// 分行別
		String CNTRNO = Util.trim(c160s01d.getCntrNo());// 額度序號
		String CUSTID = Util.getLeftStr(Util.trim(c160s01d.getCustId()), 10);// 客戶統一編號
		String DUPNO = Util.getRightStr(Util.trim(c160s01d.getCustId()), 1);// 重複序號
		if (Util.isNotEmpty(c160s01d.getRId1())) {
			String LNGEFLAG = Util.trim(c160s01d.getRType1());// 相關身分
			String LNGEID = Util.getLeftStr(Util.trim(c160s01d.getRId1()), 10);// 身份證號
			String DUPNO1 = Util.getRightStr(Util.trim(c160s01d.getRId1()), 1);// 重複序號
			String LNGENM = Util.trim(c160s01d.getRName1());// 名稱
			String LNGERE = Util.trim(c160s01d.getRKindD1());// 與主債務人關係

			BigDecimal GRTRT = new BigDecimal("0");
			if (!"S".equals(LNGEFLAG)) {
				GRTRT = new BigDecimal("100");
			}

			ELLNGTEE ellngtee = new ELLNGTEE();
			ellngtee.setBrno(BRNO);
			ellngtee.setCustid(CUSTID);
			ellngtee.setDupno(DUPNO);
			ellngtee.setCntrno(CNTRNO);
			ellngtee.setLngeflag(LNGEFLAG);
			ellngtee.setLngeid(LNGEID);
			ellngtee.setDupno1(DUPNO1);
			ellngtee.setLngenm(LNGENM);
			ellngtee.setLngere(LNGERE);
			ellngtee.setLngekind("");// 從債務人類別
			ellngtee.setUpdater(user);// 資料修改人（行員代號）
			ellngtee.setNtcode("TW");// 國家別
			ellngtee.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
			ellngtee.setLngeout("Y");// 是否對外報送
			ellngtee.setGrtrt(GRTRT);
			ellngtee.setUpddt(CapDate.getCurrentTimestamp());
			data.add(ellngtee);
		}
		if (Util.isNotEmpty(c160s01d.getRId2())) {
			String LNGEFLAG = Util.trim(c160s01d.getRType2());
			String LNGEID = Util.getLeftStr(Util.trim(c160s01d.getRId2()), 10);
			String DUPNO1 = Util.getRightStr(Util.trim(c160s01d.getRId2()), 1);
			String LNGENM = Util.trim(c160s01d.getRName2());
			String LNGERE = Util.trim(c160s01d.getRKindD2());

			BigDecimal GRTRT = new BigDecimal("0");
			if (!"S".equals(LNGEFLAG)) {
				GRTRT = new BigDecimal("100");
			}

			ELLNGTEE ellngtee = new ELLNGTEE();
			ellngtee.setBrno(BRNO);
			ellngtee.setCustid(CUSTID);
			ellngtee.setDupno(DUPNO);
			ellngtee.setCntrno(CNTRNO);
			ellngtee.setLngeflag(LNGEFLAG);
			ellngtee.setLngeid(LNGEID);
			ellngtee.setDupno1(DUPNO1);
			ellngtee.setLngenm(LNGENM);
			ellngtee.setLngere(LNGERE);
			ellngtee.setLngekind("");
			ellngtee.setUpdater(user);
			ellngtee.setNtcode("TW");
			ellngtee.setTmestamp(CapDate.getCurrentTimestamp());
			ellngtee.setLngeout("Y");
			ellngtee.setGrtrt(GRTRT);
			ellngtee.setUpddt(CapDate.getCurrentTimestamp());
			data.add(ellngtee);
		}
		return data;
	}

	private List<ELLNGTEE> upELLNGTEE(List<ELLNGTEE> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String sDate, String user) {
		if (!Util.isEmpty(l140m01a)) {
			String BRNO = Util.trim(l140m01a.getOwnBrId());// 分行別
			String CUSTID = Util.trim(l140m01a.getCustId());// 客戶統一編號
			String DUPNO = Util.trim(l140m01a.getDupNo());// 重複序號
			String CNTRNO = Util.trim(l140m01a.getCntrNo());// 額度序號
			// 主從債務人資料表檔
			List<C160S01B> c160s01blist = c160s01bDao.findByMainIdRefMainId(
					c160m01a.getMainId(), c160m01b.getRefmainId());
			for (C160S01B c160s01b : c160s01blist) {
				String LNGEFLAG = Util.trim(c160s01b.getRType());// 相關身分
				String LNGEID = Util.trim(c160s01b.getRId());// 身份證號
				String DUPNO1 = Util.trim(c160s01b.getRDupNo());// 重複序號
				String LNGENM = Util.trimSizeInOS390(
						Util.trim(c160s01b.getRName()), 40);// TODO 名稱(Q:是否需由40放大到100？   A:曉曉回覆，會，等大蓉上線後)
				String LNGEKIND = Util.trim(c160s01b.getRKindM());// 從債務人類別
				String NTCODE = Util.trim(c160s01b.getRCountry());// 國家別
				String LNGERE = Util.trim(c160s01b.getRKindD());// 與主債務人關係
				String REASONCD = Util.trim(c160s01b.getReson());// 徵提保證人原因代碼
				String REASONMEMO = Util.trimSizeInOS390(
						Util.trim(c160s01b.getResonOther()), 202);// 徵提保證人理由說明

				BigDecimal GRTRT = new BigDecimal("100");
				if(Util.equals(UtilConstants.lngeFlag.擔保品提供人, c160s01b.getRType())){
					GRTRT = new BigDecimal("0");
				}else if(Util.equals(UtilConstants.lngeFlag.共同借款人, c160s01b.getRType())){
					// still 100
				}else{
					if(c160s01b.getGuaPercent()!=null && 
							c160s01b.getGuaPercent().compareTo(BigDecimal.ZERO)!=0){
						GRTRT = c160s01b.getGuaPercent(); 
					}	
				}				

				ELLNGTEE ellngtee = new ELLNGTEE();
				ellngtee.setBrno(BRNO);
				ellngtee.setCustid(CUSTID);
				ellngtee.setDupno(DUPNO);
				ellngtee.setCntrno(CNTRNO);
				ellngtee.setLngeflag(LNGEFLAG);
				ellngtee.setLngeid(LNGEID);
				ellngtee.setDupno1(DUPNO1);
				ellngtee.setLngenm(LNGENM);
				ellngtee.setLngekind(LNGEKIND);
				ellngtee.setNtcode(NTCODE);
				ellngtee.setLngere(LNGERE);
				ellngtee.setLngeout("Y");// 是否對外報送
				ellngtee.setUpdater(user);// 資料修改人（行員代號）
				ellngtee.setTmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
				ellngtee.setReasoncd(REASONCD);
				ellngtee.setReasonmemo(REASONMEMO);
				ellngtee.setGrtrt(GRTRT);
				ellngtee.setUpddt(CapDate.getCurrentTimestamp());// 更新日
				data.add(ellngtee);
			}
		}
		return data;
	}

	@SuppressWarnings("unused")
	private List<PEXTLIMT> upPEXTLIMT(List<PEXTLIMT> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String sDate, String user) {

		if (!Util.isEmpty(l140m01a)) {
			String l140mainid = Util.trim(l140m01a.getMainId());
			List<L140S02A> l140s02alist = l140s02aDao.findByMainId(l140mainid);
			String CNTRNO = Util.trim(l140m01a.getCntrNo());
			TWNDate twdate = new TWNDate();
			twdate.setTime(sDate);
			String APPDATE = Util.getLeftStr(twdate.toTW('/'), 6);
			for (L140S02A l140s02a : l140s02alist) {
				L140S02E l140s02e = l140s02eDao.findByUniqueKey(l140mainid,
						l140s02a.getSeq());
				l140s02e = (Util.isEmpty(l140s02e) ? new L140S02E() : l140s02e);
				Integer EXTFROM = (Util.isEmpty(l140s02e.getNowFrom()) ? 0
						: l140s02e.getNowFrom());
				Integer EXTEND = (Util.isEmpty(l140s02e.getNowEnd()) ? 0
						: l140s02e.getNowEnd());
				int TEXTEN = EXTEND - EXTFROM;
				TEXTEN = (Util.equals(TEXTEN, 0) ? 0 : TEXTEN);
				int SECEXTEN = TEXTEN;
				PEXTLIMT pextlimt = new PEXTLIMT();
				pextlimt.setCntrno(CNTRNO);
				pextlimt.setAppdate(APPDATE);
				pextlimt.setExtfrom(EXTFROM);
				pextlimt.setExtend(EXTEND);
				pextlimt.setTexten(TEXTEN);
				pextlimt.setSecexten(SECEXTEN);
				pextlimt.setUpdater(user);
				pextlimt.setTmestamp(CapDate.getCurrentTimestamp());
				data.add(pextlimt);
			}
		}
		return data;
	}

	private List<LNF164> upLNF164Case3(List<LNF164> data, C160M01A c160m01a,
			C160M01F c160m01f, C160S01D c160s01d) {
		String LNF164_BR_NO = Util.trim(c160m01a.getOwnBrId());// 分行別
		String LNF164_CONTRACT = Util.trim(c160s01d.getCntrNo());// 額度序號
		String LNF164_CUST_ID = Util.trim(c160s01d.getCustId());// 客戶編號
		String LNF164_KIND = "3";// 契約種類
		String LNF164_SWFT = "TWD"; // 放款幣別
		String LNF164_LNAP_CODE = "321";// 放款科目
		String LNF164_INTRT_TYPE = "7";// 收息方式
		LNF164 lnf164 = new LNF164();
		lnf164.setLnf164_br_no(LNF164_BR_NO);
		lnf164.setLnf164_contract(LNF164_CONTRACT);
		lnf164.setLnf164_cust_id(LNF164_CUST_ID);
		lnf164.setLnf164_kind(LNF164_KIND);
		lnf164.setLnf164_swft(LNF164_SWFT);
		lnf164.setLnf164_lnap_code(LNF164_LNAP_CODE);
		lnf164.setLnf164_intrt_type(LNF164_INTRT_TYPE);
		lnf164.setLnf164_int_kind("N");
		lnf164.setLnf164_intchg_type("");
		lnf164.setLnf164_int_type("");
		lnf164.setLnf164_intchg_cycl("");
		lnf164.setLnf164_int_base("");
		lnf164.setLnf164_int_memo("");
		lnf164.setLnf164_int_spread(new BigDecimal(0));
		lnf164.setLnf164_timestamp(CapDate.getCurrentTimestamp());
		data.add(lnf164);
		return data;
	}

	private List<LNF164> upLNF164(List<LNF164> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String sDate) {
		if (!Util.isEmpty(l140m01a)) {
			String l140mainid = Util.trim(l140m01a.getMainId());
			String c160mainid = Util.trim(c160m01a.getMainId());
			List<L140S02A> l140s02alist = l140s02aDao.findByMainId(l140mainid);
			if (!l140s02alist.isEmpty()) {
				L140S02A l140s02a = l140s02alist.get(0);
				if (!Util.isEmpty(l140s02a)) {
					Integer l140seq = l140s02a.getSeq();
					// 個金產品種類檔
					C160S01C c160s01c = c160s01cDao.findByUniqueKey(c160mainid,
							l140seq, l140mainid);
					c160s01c = (Util.isEmpty(c160s01c) ? new C160S01C()
							: c160s01c);
					// 分段利率主檔
					L140S02C l140s02c = l140s02cDao.findByUniqueKey(l140mainid,
							l140seq);
					l140s02c = (Util.isEmpty(l140s02c) ? new L140S02C()
							: l140s02c);
					// 分段利率明細檔
					L140S02D l140s02d = l140s02dDao.findByUniqueKey(l140mainid,
							l140seq, 1);
					l140s02d = (Util.isEmpty(l140s02d) ? new L140S02D()
							: l140s02d);
					String LNF164_BR_NO = Util.trim(l140m01a.getOwnBrId());// 分行別
					String LNF164_CONTRACT = Util.trim(l140m01a.getCntrNo());// 額度序號
					String LNF164_CUST_ID = Util.trim(l140m01a.getCustId())
							+ Util.trim(l140m01a.getDupNo());// 客戶編號
					String LNF164_KIND = "3";// 契約種類
					String LNF164_SWFT = Util.trim(c160s01c.getLoanCurr());// 放款幣別
					String LNF164_INT_MEMO = Util.trim(c160s01c.getRateDesc());// 利率條件中文敘述
					String LNF164_INTRT_TYPE = getLNF164IntrtType(Util
							.trim((l140s02c.getRIntWay())));// 收息方式
					String LNF164_INT_BASE = Util.trim(l140s02d.getBaseDesc());// 加碼基礎
					BigDecimal LNF164_INT_SPREAD = Util.isEmpty(l140s02d
							.getPmRate()) ? BigDecimal.ZERO : l140s02d
							.getPmRate();// 加減碼
					String LNF164_INT_TYPE = l140s02d.getRateFlag();// 利率方式
					// -------------------------放款科目-----------------------
					String LNF164_LNAP_CODE = Util.trim(l140s02a.getSubjCode());
					LNF164_LNAP_CODE = prodservice
							.getSubject8to3(LNF164_LNAP_CODE);// 八碼轉三碼
					// ----------------------利率變動方式---------------------
					String LNF164_INTCHG_TYPE = new String();// 利率變動方式
					switch (Util.parseInt(Util.trim(l140s02d.getRateChgWay2()))) {
					case 1:
						LNF164_INTCHG_TYPE = "M";// 月
						break;
					case 3:
						LNF164_INTCHG_TYPE = "S";// 季
						break;
					case 6:
						LNF164_INTCHG_TYPE = "H";// 半年
						break;
					case 9:
						LNF164_INTCHG_TYPE = "N";// 九個月
						break;
					case 12:
						LNF164_INTCHG_TYPE = "Y";// 年
						break;
					}
					// ------------------------
					LNF164 lnf164 = new LNF164();
					lnf164.setLnf164_br_no(LNF164_BR_NO);
					lnf164.setLnf164_contract(LNF164_CONTRACT);
					lnf164.setLnf164_cust_id(LNF164_CUST_ID);
					lnf164.setLnf164_kind(LNF164_KIND);
					lnf164.setLnf164_swft(LNF164_SWFT);
					lnf164.setLnf164_lnap_code(LNF164_LNAP_CODE);
					lnf164.setLnf164_intrt_type(LNF164_INTRT_TYPE);
					lnf164.setLnf164_int_kind("N");// 利率條件是否字述
					lnf164.setLnf164_int_base(Util.trimSizeInOS390(
							LNF164_INT_BASE, 100));
					lnf164.setLnf164_int_spread(LNF164_INT_SPREAD);
					lnf164.setLnf164_int_type(LNF164_INT_TYPE);
					lnf164.setLnf164_intchg_type(LNF164_INTCHG_TYPE);
					lnf164.setLnf164_int_memo(Util.trimSizeInOS390(
							LNF164_INT_MEMO, 200));
					lnf164.setLnf164_timestamp(CapDate.getCurrentTimestamp());
					lnf164.setLnf164_intchg_cycl(Util.trim(l140s02d
							.getRateChgWay()));
					data.add(lnf164);
				}
			}
		}
		return data;
	}

	private List<LNF130> upLNF130Case3(List<LNF130> data, C160M01A c160m01a,
			C160M01F c160m01f, C160S01D c160s01d) {
		if (Util.equals(c160m01a.getCaseType(), "3")) {
			List<C160S01G> c160s01glist = c160s01gDao.findByMainId(c160m01f
					.getMainId());
			for (C160S01G c160s01g : c160s01glist) {
				String LNF130_BR_NO = Util.trim(c160m01a.getCaseBrId());// 分行別
				String LNF130_CUST_ID = Util.trim(c160s01d.getCustId());// 借款人身分證字號
				String LNF130_CONTRACT = Util.trim(c160s01d.getCntrNo());// 額度序號
				Integer LNF130_BEG_TERM = c160s01g.getBgnNum();// 利率第一段起期
				Integer LNF130_END_TERM = c160s01g.getEndNum();// 利率第一段迄期
				String LNF130_INT_CODE = Util.trim(c160s01g.getRateType());// 利率第一段代碼
				BigDecimal LNF130_INT_SPRD = c160s01g.getPmRate();// 利率第一段加減碼
				String LNF130_INT_TYPE = Util.trim(c160s01g.getRateFlag());// 利率第一段利率方式
				String LNF130_INTCHG_TYPE = Util.trim(c160s01g.getRateChgWay());// 利率第一段變動方式
				String LNF130_INTCHG_CYCL = Util
						.trim(c160s01g.getRateChgWay2());// 利率第一段變動週期
				String LNF130_SCTYPE = "3";
				String LNF130_UNDOCID = Util.trim(c160m01a.getOid());// oid
				String LNF130_LOAN_CLASS = Util.trim(c160m01f.getProdKind());// 產品種類
				LNF130 lnf130 = new LNF130();
				lnf130.setLnf130_br_no(LNF130_BR_NO);
				lnf130.setLnf130_cust_id(LNF130_CUST_ID);
				lnf130.setLnf130_contract(LNF130_CONTRACT);
				lnf130.setLnf130_loan_no("00001");// 放款帳號
				lnf130.setLnf130_beg_term(LNF130_BEG_TERM);
				lnf130.setLnf130_end_term(LNF130_END_TERM);
				lnf130.setLnf130_int_code(LNF130_INT_CODE);
				lnf130.setLnf130_int_sprd(LNF130_INT_SPRD);
				lnf130.setLnf130_int_type(LNF130_INT_TYPE);
				lnf130.setLnf130_intchg_type(LNF130_INTCHG_TYPE);
				lnf130.setLnf130_intchg_cycl(LNF130_INTCHG_CYCL);
				lnf130.setLnf130_sctype(LNF130_SCTYPE);
				lnf130.setLnf130_undocid(LNF130_UNDOCID);
				lnf130.setLnf130_loan_class(LNF130_LOAN_CLASS);
				lnf130.setLnf130_tmestamp(CapDate.getCurrentTimestamp());// ELOAN寫入時間
				data.add(lnf130);
			}
		}
		return data;
	}
	
	private List<ELF504> buildELF504(List<LNF130> lnf130List, List<ELF500> elf500List, C160M01A c160m01a) {
		List<ELF504> r = new ArrayList<ELF504>();
		
		Map<String, ELF500> map500 = new HashMap<String, ELF500>();
		for(ELF500 elf500: elf500List){
			map500.put(elf500.getElf500_cntrno(), elf500);			
		}
		for(LNF130 lnf130: lnf130List){			
			String cntrNo = Util.trim(lnf130.getLnf130_contract());
			ELF500 elf500 = map500.get(cntrNo);

			ELF504 elf504 = new ELF504();
			//===========
			if(Util.equals(BR_901, lnf130.getLnf130_br_no())){
				/*
				LNF130
					新　　作　　　　　, 會在 LNF130 帶 ［分行 , 授信帳號00001］
					短期重簽（未開戶）, 會在 LNF130 帶 ［分行 , 授信帳號00001］
					短期重簽（已開戶）, 會在 LNF130 帶 ［分行901, 授信帳號-real-］
				
				ELF504
					新　　作　　　　　,會在 ELF504 帶 ［分行 , 授信帳號00001］
					短期重簽（未開戶）,會在 ELF504 帶 ［分行 , 授信帳號00001］
					短期重簽（已開戶）,會在 ELF504 帶 ［分行 , 授信帳號-real-］
				*/
				elf504.setElf504_br_no(Util.getLeftStr(cntrNo, 3));
			}else{
				elf504.setElf504_br_no(lnf130.getLnf130_br_no());	
			}
			
			elf504.setElf504_cust_id(lnf130.getLnf130_cust_id());
			elf504.setElf504_cntrno(cntrNo);
			elf504.setElf504_loan_no(lnf130.getLnf130_loan_no());
			elf504.setElf504_beg_term(lnf130.getLnf130_beg_term());
			elf504.setElf504_end_term(lnf130.getLnf130_end_term());
			elf504.setElf504_int_code(lnf130.getLnf130_int_code());
			elf504.setElf504_int_sprd(lnf130.getLnf130_int_sprd());
			elf504.setElf504_int_type(lnf130.getLnf130_int_type());
			elf504.setElf504_intchg_type(lnf130.getLnf130_intchg_type());
			elf504.setElf504_intchg_cycl(lnf130.getLnf130_intchg_cycl());
			elf504.setElf504_sctype(lnf130.getLnf130_sctype());
			elf504.setElf504_undocid(lnf130.getLnf130_undocid());
			elf504.setElf504_loan_class(lnf130.getLnf130_loan_class());
			elf504.setElf504_tmestamp(lnf130.getLnf130_tmestamp());
			elf504.setElf504_dec_flag(lnf130.getLnf130_dec_flag());
			elf504.setElf504_dec_sprd(lnf130.getLnf130_dec_sprd());
			elf504.setElf504_int_01_ptr(lnf130.getLnf130_int_01_ptr());			
			//===========
			/*
			 * 若在12/14覆核時，指定 日期 12/05 為
			 * ApproveTime 會寫入 2016-12-05 00:00:00
			 */
			elf504.setElf504_sdate(c160m01a.getApproveTime());
			if(true){
				String documentNo = "";
				if(elf500!=null){
					documentNo = Util.trim(elf500.getElf500_document_no());
				}
				elf504.setElf504_documentno(documentNo);	
			}
			//===========
			r.add(elf504);
		}
		return r;
	}
	
	private List<LNF130> upLNF130(List<LNF130> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String sDate, boolean skip_loanNo_intway2) {
		if (!Util.isEmpty(l140m01a)) {
			//C160M01B 是額度檔
			List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
					c160m01b.getMainId(), c160m01b.getRefmainId());
			String LNF130_BR_NO = Util.getLeftStr(
					Util.trim(l140m01a.getCntrNo()), 3);// 分行別
			String LNF130_CUST_ID = Util.trim(l140m01a.getCustId())
					+ Util.trim(l140m01a.getDupNo());// 客戶統編
			String LNF130_CONTRACT = Util.trim(l140m01a.getCntrNo());// 額度序號
			String LNF130_UNDOCID = Util.trim(l140m01a.getMainId());// 文件ID
			for (C160S01C c160s01c : c160s01clist) {
				// 個金產品種類檔
				L140S02A l140s02a = l140s02aDao.findByUniqueKey(
						c160s01c.getRefmainId(), c160s01c.getSeq());
				l140s02a = (Util.isEmpty(l140s02a) ? new L140S02A() : l140s02a);
				// 分段利率主檔
				L140S02C l140s02c = l140s02cDao.findByUniqueKey(
						c160m01b.getRefmainId(), l140s02a.getSeq());
				l140s02c = (Util.isEmpty(l140s02c) ? new L140S02C() : l140s02c);

				String LNF130_LOAN_NO = Util.trim(l140s02a.getLoanNo());// 放款帳號
				String LNF130_LOAN_NO_SRC = Util.trim(l140s02a.getSrcLoanNo()); // 原始帳號

				String LNF130_LOAN_CLASS = Util.trim(c160s01c.getProdKind());// 產品種類
				String LNF130_DEC_FLAG = Util.trim(l140s02c.getDecFlag());// 省息遞減註記
				// 分段利率明細檔
				List<L140S02D> l140s02dlist = l140s02dDao
						.findByMainidSeqIsUseBox(l140s02a.getMainId(),
								l140s02a.getSeq(), "Y");
				
				// TODO 若放款帳號有值則分行別設定為901 → 在 L56A-8 把 LNF130_BR_NO=901 帶到畫面上
				if (!Util.isEmpty(LNF130_LOAN_NO)) {
					LNF130_BR_NO = BR_901;
				}
				
				// 當無帳號為新案需上5碼流水號
				if (Util.isEmpty(LNF130_LOAN_NO)) {
					if (LNF130_LOAN_NO_SRC.equals(LNF130_LOAN_NO)) {
						// 2013-06-19,Rex,修改抓取流水號，getSeq()改為getSecNo()
						LNF130_LOAN_NO = Util.addZeroWithValue(
								l140s02a.getSecNo(), 5);
					} else {
						//XXX 因應改科目後MIS.ELF501要多上傳一筆新案上去...
						/*
						 若在 l140s02a 針對 帳務(LNF030) 同時去改會計科目,自用住宅註記 ,風險權數(45%/100%)
						在改了會計科目後, 新的 seq 會加 50
						
						ELF501_SEQ_NO	ELF501_LOAN_NO    ELF501_OWN_HOUSE  ELF501_RISK_RATING 
						-------------   ----------------  -------------     -------------
							 1			<USER>               <GROUP>           45
							51	                                     N          100    
						
						造成
						●另外 insert 一筆ELF501_SEQ_NO==51 的資料
						●原本ELF501_SEQ_NO==1 的資料未被 update
							(在 L219 去查詢時, ELF501_OWN_HOUSE, ELF501_RISK_RATING 都是舊的)							
												
						*/
						Integer tSecNo = l140s02a.getSecNo() + 50;
						LNF130_LOAN_NO = Util.addZeroWithValue(tSecNo, 5);
					}
				}
				for (L140S02D l140s02d : l140s02dlist) {
					if(skip_loanNo_intway2){
					// 舊案的期付金不要上(放款帳號不為空值且計息方式 為"2"時)
					if (Util.isNotEmpty(Util.trim(l140s02a.getLoanNo()))
							&& UtilConstants.L140S02CIntway.期付金.equals(l140s02c
									.getIntWay())) {
						continue;// 跳過此筆資料
					}
					}
					Integer LNF130_BEG_TERM = l140s02d.getBgnNum();// 起期
					Integer LNF130_END_TERM = l140s02d.getEndNum();// 迄期
					String rateType = Util.trim(l140s02d.getRateType());
					String LNF130_INT_CODE = rateType;// 利率代碼
					BigDecimal LNF130_INT_SPRD = Util.equals(rateType, CrsUtil.RATE_TYPE_01) ? Util
							.parseBigDecimal(l140s02d.getRateUser()) : Util
							.parseBigDecimal(l140s02d.getPmRate());// 利率加減碼
					// 當加減碼為負數 上傳時要加上負號
					if (Util.notEquals(rateType, CrsUtil.RATE_TYPE_01)) {
						if ("M".equals(l140s02d.getPmFlag())) {
							LNF130_INT_SPRD = LNF130_INT_SPRD.negate();
						}
					}
					String LNF130_INT_TYPE = Util.trim(l140s02d.getRateFlag());// 利率方式
					String LNF130_INTCHG_TYPE = new String();// 資料來源
					String LNF130_INTCHG_CYCL = new String();
					String LNF130_INT_01_PTR = Util.trim(l140s02d
							.getRateUserType());
					// 利率方式變動方式週期上傳規則
					if (Util.equals(l140s02d.getRateFlag(), "3")) {
						if (!Util.equals(l140s02d.getRateChgWay(), "1")) {
							LNF130_INTCHG_TYPE = "M";
							LNF130_INTCHG_CYCL = "6";
						} else {
							switch (Util.parseInt(Util.trimSpace(l140s02d
									.getRateChgWay2()))) {
							case 1:
								LNF130_INTCHG_TYPE = "M";
								LNF130_INTCHG_CYCL = "1";
								break;
							case 3:
								LNF130_INTCHG_TYPE = "M";
								LNF130_INTCHG_CYCL = "3";
								break;
							case 6:
								LNF130_INTCHG_TYPE = "M";
								LNF130_INTCHG_CYCL = "6";
								break;
							case 9:
								LNF130_INTCHG_TYPE = "M";
								LNF130_INTCHG_CYCL = "9";
								break;
							case 12:
								LNF130_INTCHG_TYPE = "Y";
								LNF130_INTCHG_CYCL = "1";
								break;
							}
						}
					} else {
						LNF130_INTCHG_TYPE = "";
						LNF130_INTCHG_CYCL = "";
					}
					LNF130 lnf130 = new LNF130();
					lnf130.setLnf130_br_no(LNF130_BR_NO);
					lnf130.setLnf130_cust_id(LNF130_CUST_ID);
					lnf130.setLnf130_contract(LNF130_CONTRACT);
					lnf130.setLnf130_loan_no(LNF130_LOAN_NO);
					lnf130.setLnf130_beg_term(LNF130_BEG_TERM);
					lnf130.setLnf130_end_term(LNF130_END_TERM);
					lnf130.setLnf130_int_code(LNF130_INT_CODE);
					lnf130.setLnf130_int_sprd(LNF130_INT_SPRD);
					lnf130.setLnf130_int_type(LNF130_INT_TYPE);
					lnf130.setLnf130_intchg_type(LNF130_INTCHG_TYPE);
					lnf130.setLnf130_intchg_cycl(LNF130_INTCHG_CYCL);
					lnf130.setLnf130_sctype("2");//
					lnf130.setLnf130_undocid(LNF130_UNDOCID);
					lnf130.setLnf130_loan_class(LNF130_LOAN_CLASS);
					lnf130.setLnf130_tmestamp(CapDate.getCurrentTimestamp());
					lnf130.setLnf130_dec_flag(LNF130_DEC_FLAG);
					lnf130.setLnf130_dec_sprd(new BigDecimal(0));
					lnf130.setLnf130_int_01_ptr(LNF130_INT_01_PTR);
					data.add(lnf130);
				}
			}
		}
		return data;
	}

	private List<ELF502> upELF502(List<ELF502> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String sDate) {
		String c160mainid = Util.trim(c160m01a.getMainId());

		List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
				c160mainid, c160m01b.getRefmainId());
		if (Util.isNotEmpty(l140m01a)) {
			String ELF502_CNTRNO = Util.trim(l140m01a.getCntrNo());// 本件額度序號
			// 2013-06-19,Rex,修改抓取流水號，getSeq()改為getSecNo()
			String ELF502_CUSTID = Util.trim(l140m01a.getCustId());// 借款人身分證統一編號
			String ELF502_DUPNO = Util.trim(l140m01a.getDupNo());// 重複序號
			for (C160S01C c160s01c : c160s01clist) {
				Integer ELF502_SEQ_NO = c160s01c.getCaseSeq();// 案件序號
				Integer c160seq = c160s01c.getSeq();
				if (ELF502_SEQ_NO == null) {
					ELF502_SEQ_NO = c160seq;
				}
				// 代償轉貸借新還舊主檔
				C160S01E c160s01e = c160s01eDao.findByMainIdRefMainIdSeq(
						c160mainid, c160seq, c160m01b.getRefmainId());
				c160s01e = (Util.isEmpty(c160s01e) ? new C160S01E() : c160s01e);
				if (Util.equals(c160s01e.getChgOther(), "Y")) {// 本筆借款是否用來償還其他筆貸款
																// 為是(Y)時才上傳
					List<C160S01F> c160s01flist = c160s01fDao
							.findByMainIdSeqRefMainid(c160mainid, c160seq,
									c160s01e.getRefmainId());
					for (C160S01F c160s01f : c160s01flist) {
						L140S02F l140s02f = l140s02fDao.findByUniqueKey(
								c160s01f.getRefmainId(), c160seq);
						l140s02f = (Util.isEmpty(l140s02f) ? new L140S02F()
								: l140s02f);
						if (Util.isEmpty(ELF502_SEQ_NO)) {
							ELF502_SEQ_NO = c160s01c.getSeq();
						}
						Date ELF502_CHGDATE = (Util.isEmpty(c160s01e
								.getOnlentDate()) ? CapDate.getDate(
								"0001/01/01", "yyyy/MM/dd") : c160s01e
								.getOnlentDate());// 轉貸日期(為空值時設定為0001/01/01)
						String ELF502_ORIBANCD = Util.trim(c160s01f
								.getBranchNo());// 轉入之原金融機構代碼
						BigDecimal ELF502_CHGLNBAL = (Util.isEmpty(c160s01f
								.getSubAmt()) ? new BigDecimal(0) : c160s01f
								.getSubAmt());// 轉貸剩餘貸款本金
						Date ELF502_ORI_B_DATE = c160s01f.getOLNAppDate();// 轉貸原貸款貸放日期
						Date ELF502_ORIPDATE = (Util.isEmpty(c160s01f
								.getOLNEndDate()) ? CapDate.getDate(
								"0001/01/01", "yyyy/MM/dd") : c160s01f
								.getOLNEndDate());// 原貸款到期日(為空值時設定為0001/01/01)
						String ELF502_SAME_POLICY = Util.trim(l140s02f
								.getFavChgCase());// 轉貸前後是否相同政策貸款
						String ELF502_CHGREASON = Util.trim(c160s01f
								.getSubReason());// 代償他行房貸原因
						String ELF502_CHGMEMO = Util.trim(c160s01f
								.getSubReaOth());// 代償原因－其他
						ELF502 elf502 = new ELF502();
						elf502.setElf502_custid(ELF502_CUSTID);
						elf502.setElf502_dupno(ELF502_DUPNO);
						elf502.setElf502_cntrno(ELF502_CNTRNO);
						elf502.setElf502_seq_no(ELF502_SEQ_NO);
						elf502.setElf502_chgdate(ELF502_CHGDATE);
						elf502.setElf502_oribancd(ELF502_ORIBANCD);
						elf502.setElf502_chglnbal(ELF502_CHGLNBAL);
						elf502.setElf502_ori_b_date(ELF502_ORI_B_DATE);
						elf502.setElf502_oripdate(ELF502_ORIPDATE);
						elf502.setElf502_same_policy(ELF502_SAME_POLICY);
						elf502.setElf502_chgreason(ELF502_CHGREASON);
						elf502.setElf502_chgmemo(ELF502_CHGMEMO);
						data.add(elf502);
					}
				}
				// 上傳前會先依照額度序號及案件序號刪除檔案後再上傳
				delMisToServerToELF502(ELF502_CNTRNO, ELF502_SEQ_NO.toString());
			}

		}
		return data;
	}

	private List<ELF501> upELF501_when_caseType1_2(List<ELF501> data, C160M01A c160m01a,
			C160M01B c160m01b, L140M01A l140m01a, String apprId,
			String reCheckId, String sDate) {
		String c160mainid = c160m01a.getMainId();
		// 額度明細表
		if (!Util.isEmpty(l140m01a)) {
			String l140mainid = Util.trim(l140m01a.getMainId());
			List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
					c160m01b.getMainId(), c160m01b.getRefmainId());
			for (C160S01C c160s01c : c160s01clist) {
				// 簽報書產品種類檔
				L140S02A l140s02a = l140s02aDao.findByUniqueKey(l140mainid,
						c160s01c.getSeq());
				l140s02a = (Util.isEmpty(l140s02a) ? new L140S02A() : l140s02a);
				Integer l140seq = l140s02a.getSeq();
				// 分段利率主檔
				L140S02C l140s02c = l140s02cDao.findByUniqueKey(l140mainid,
						l140seq);
				l140s02c = (Util.isEmpty(l140s02c) ? new L140S02C() : l140s02c);
				// 償還方式檔
				L140S02E l140s02e = l140s02eDao.findByUniqueKey(l140mainid,
						l140seq);
				l140s02e = (Util.isEmpty(l140s02e) ? new L140S02E() : l140s02e);
				// 房屋貸款檔
				L140S02F l140s02f = l140s02fDao.findByUniqueKey(l140mainid,
						l140seq);
				l140s02f = (Util.isEmpty(l140s02f) ? new L140S02F() : l140s02f);
				// 外勞貸款檔
				L140S02J l140s02j = l140s02jDao.findByUniqueKey(l140mainid,
						l140seq);
				l140s02j = (Util.isEmpty(l140s02j) ? new L140S02J() : l140s02j);
				// 擔保品資料明細檔
				List<C160S01A> c160s01alist = c160s01aDao
						.findByMainIdRefMainId(c160mainid, l140mainid);
				C160S01A c160s01a = new C160S01A();
				if (c160s01alist.size() > 0) {
					c160s01a = c160s01alist.get(0);
				}
				c160s01a = (Util.isEmpty(c160s01a) ? new C160S01A() : c160s01a);

				// 代償轉貸借新還舊主檔
				C160S01E c160s01e = c160s01eDao.findByMainIdRefMainIdSeq(
						c160mainid, l140seq, l140mainid);
				c160s01e = (Util.isEmpty(c160s01e) ? new C160S01E() : c160s01e);
				// 代償轉貸借新還舊明細檔
				List<C160S01F> c160s01flist = c160s01fDao
						.findByMainIdSeqRefMainid(c160mainid, l140seq,
								l140mainid);
				// 購置房屋擔保放款風險權數檢核表
				C102M01A c102m01a = c102m01aDao.findByMainId2(Util
						.trim(l140s02f.getRefMainId()));
				c102m01a = (Util.isEmpty(c102m01a) ? new C102M01A() : c102m01a);
				// 塞值
				String ELF501_CUSTID = Util.trim(l140m01a.getCustId());// 客戶統編
				String ELF501_DUPNO = Util.trim(l140m01a.getDupNo());// 重覆序號
				String ELF501_CNTRNO = Util.trim(l140m01a.getCntrNo());// 額度序號
				String ELF501_SWFT = Util.trim(l140m01a.getCurrentApplyCurr());// 放款幣別

				String ELF501_LOAN_NO = Util.trim(l140s02a.getLoanNo());// 對應放款帳號
				String ELF501_LOAN_NO_SRC = Util.trim(l140s02a.getSrcLoanNo()); // 原始帳號

				String ELF501_LNTYPE = Util.trim(l140s02a.getProdKind());// 產品種類
				String ELF501_LOANTP = Util.trim(l140s02a.getSubjCode());// 科目代碼
				String ELF501_FNDSRE = Util.trim(l140s02a.getFFund());// 資金來源
				String ELF501_FUND_TYPE2 = Util.getRightStr(
						Util.trim(l140s02a.getDFund()), 3);// 資金來源小類
				String ELF501_LNPURS = Util.trim(l140s02a.getLnPurs());// 用途別
				String ELF501_LNPURS_SUB = Util.trim(l140s02a.getWorkingFundPurpose());//週轉金用途細項
				String ELF501_LN_PURPOSE = Util.trim(l140s02a.getLnPurpose());// 融資業務分類
				String ELF501_RESIDENTIAL = Util
						.trim(l140s02a.getResidential());// 是否屬興建房屋
				// ------------------由授信期限轉換成月份-----------------------
				/*
				  	只有 notes-eloan 轉檔上來的舊案
				  	才會有 LnSelect==1 的情況
				  	－－－－－－－－
				  	當團貸母戶（以918開頭的額度序號）
				  	 LnSelect= null
				 */
				int LnMonth = 0;
				switch (Util.parseInt(l140s02a.getLnSelect())) {
				case 1:
					if (!Util.isEmpty(l140s02a.getLnFromDate())
							&& !Util.isEmpty(l140s02a.getLnEndDate())) {
						LnMonth = CapDate.calculateMonths(
								Util.getDate(l140s02a.getLnEndDate()),
								Util.getDate(l140s02a.getLnFromDate()),
								"yyyy-MM-dd", "yyyy-MM-dd");
					}
					break;
				case 2:
				case 3:
				case 4:
					LnMonth = l140s02a.getLnYear() * 12 + l140s02a.getLnMonth();
					break;
				}
				int ELF501_MONTHCNT = LnMonth;
				// ---------------------流水號--------------------------
				Integer ELF501_SEQ_NO = c160s01c.getCaseSeq();
				if (Util.isEmpty(ELF501_SEQ_NO)) {
					ELF501_SEQ_NO = c160s01c.getSeq();
				}
				/**
				 * ---------------------償還方式及收息方式------------------------- 償還方式
				 * 1.本息平均攤還(按月繳款) 2.本息平均攤還(雙週繳款) 3.本金平均攤還(按月繳款，每期攤還本金：元)
				 * 4.本金平均攤還(雙週繳款，每期攤還本金：元) 5.貸款本金及貸款期間全部利息自第４年起平均攤還。(921貸款專案適用)
				 * 6.其他（請自行輸入） 7.按月付息，契約到期清償本金(房貸還本週轉適用)
				 */
				String ELF501_AVGPAY = "";// 償還方式
				String payWay = Util.trim(l140s02e.getPayWay());
				if ("1".equals(payWay) || "2".equals(payWay)) {
					ELF501_AVGPAY = "2";// 本息平均攤還
				} else if ("3".equals(payWay) || "4".equals(payWay)) {
					ELF501_AVGPAY = "3";// 本金平均攤還
				} else if ("7".equals(payWay)) {
					ELF501_AVGPAY = "4";// 按月付息
				}
				String ELF501_PAYCLE = new String();// 收息方式
				if (Util.equals(payWay, "2") || Util.equals(payWay, "4")) {
					ELF501_PAYCLE = "2";// 雙周繳款
				} else if (Util.equals(payWay, "1") || Util.equals(payWay, "3")) {
					ELF501_PAYCLE = "1";// 按月繳款
				}

				String ELF501_GINTTYPE = Util.trim(l140s02c.getIntWay());// 計息方式
				String ELF501_HINTTYPE = Util.trim(l140s02c.getRIntWay());// 收息方式
				String ELF501_OVBAL_TYPE = "";
				if (!Util.isEmpty(ELF501_HINTTYPE)) {
					if (Util.equals(ELF501_HINTTYPE, "6")) {// 收息方式等於期付金時
						ELF501_GINTTYPE = "2";// 期付金
					} else {
						if (Util.equals(ELF501_GINTTYPE,
								UtilConstants.L140S02CIntway.透支end)) {
							ELF501_GINTTYPE = "6";// 透支
							ELF501_OVBAL_TYPE = "0";
						} else if (Util.equals(ELF501_GINTTYPE,
								UtilConstants.L140S02CIntway.透支top)) {
							ELF501_GINTTYPE = "6";// 透支
							ELF501_OVBAL_TYPE = "1";
						} else {
							ELF501_GINTTYPE = "1";// 按月計息
						}
					}
				} else {
					if (Util.equals(l140s02a.getProdKind(),
							ProdKindEnum.企金科目.getCode())) {
						ELF501_GINTTYPE = "5";
						ELF501_HINTTYPE = "7";
					} else {
						if (Util.equals(l140s02e.getPayWay(), "7")) {
							ELF501_GINTTYPE = "1";// 按月計息
						} else {
							ELF501_GINTTYPE = "2";// 期付金
						}
					}
				}

				if (Util.isEmpty(ELF501_AVGPAY)) {// ELF501_AVGPAY等於空白時
					if (Util.equals(ELF501_GINTTYPE, "2")) {// 計息方式等於期付金時
						ELF501_AVGPAY = "2";
						ELF501_PAYCLE = "1";
					} else if (Util.equals(ELF501_GINTTYPE, "1")
							|| Util.equals(ELF501_GINTTYPE, "5")) {
						ELF501_AVGPAY = "4";
					}
				}
				// ------------------------風險權數-------------------------
				String ELF501_OWN_HOUSE = "N";// 是否自用住宅預設為N
				Integer ELF501_RISK_RATING = null;// 風險權數預設為null
				if (Util.isNotEmpty(c102m01a)) {
					ELF501_OWN_HOUSE = (Util.isEmpty(Util.trim(c102m01a
							.getSelfChk())) ? "N" : Util.trim(c102m01a
							.getSelfChk()));
					ELF501_RISK_RATING = LMSUtil.build_ELF501_RISK_RATING(c102m01a, ELF501_OWN_HOUSE, ELF501_LN_PURPOSE);										
				}
				// --------------------------------------------------------------------
				BigDecimal ELF501_EHPAYCPT = (Util.isEmpty(l140s02e
						.getPayWayAmt()) ? new BigDecimal(0) : l140s02e
						.getPayWayAmt());// 每期攤還本金

				String ELF501_TAXNO = Util.trim(c160s01a.getTaxNo());// 稅籍編號
				// --------------------------稅籍地址----------------------------------
				String ELF501_TAXADDR = Util
						.getLeftStr(Util.toFullCharString(Util.trim(c160s01a
								.getTaxAddr())), 50);// 稅籍地址
				if (Util.equals(Util.getLeftStr(ELF501_TAXADDR, 2), "１、")) {// 前兩個字為"１、"時
					ELF501_TAXADDR = Util.getRightStr(ELF501_TAXADDR,
							ELF501_TAXADDR.length() - 2);// 去除前兩個字
				}
				// -----------------------------------------------------------------
				String ELF501_IMPORTID = Util.getRightStr(
						l140s02f.getImportId(), 5);// 引介行員代號
				if(true){ // J-107-0091
					if(Util.isEmpty(ELF501_IMPORTID) && Util.isNotEmpty(Util.trim(l140m01a.getAgntNo()))){
						ELF501_IMPORTID = Util.trim(l140m01a.getAgntNo());
					}
					if(Util.isEmpty(ELF501_IMPORTID) && Util.isNotEmpty(Util.trim(l140m01a.getMegaEmpNo()))){
						ELF501_IMPORTID = Util.getRightStr(Util.trim(l140m01a.getMegaEmpNo()), 5); //六碼行編→五編
					}					
				}
				String ELF501_EFCTBH = Util.trim(c160s01c.getEfctBH());// 行銷分行
				String ELF501_INSC_NO = "";// 行銷代號(固定上傳空白)
				String ELF501_AUTORCT = Util.trim(c160s01c.getAutoRct());// 自動進帳
				Date ELF501_RCTDATE = c160s01c.getRctDate();// 進帳日期
				String ELF501_ACCNO = Util.trim(c160s01c.getAccNo());// 存款帳號(進帳帳號)
				BigDecimal ELF501_RCTAMT = c160s01c.getRctAMT();// 進帳金額				
				String ELF501_AUTOPAY = Util.trim(c160s01c.getAutoPay());// 自動扣帳
				String ELF501_ATPAYNO = Util.trim(c160s01c.getAtpayNo());// 扣帳帳號
				
				//撥款進帳他行 欄位設定
				if(	Util.isNotEmpty(c160s01c.getAppOtherBranchNo()) && 
						Util.isNotEmpty(c160s01c.getAppOtherAccount())){//撥款他行
					ELF501_AUTORCT = "";//撥款他行-自動進帳不可為Y
					ELF501_RCTDATE = null;//撥款他行-不可有進帳日期
				}
				//ACH扣帳他行 欄位設定
				if(Util.equals(c160s01c.getPayType(), "01")){
					ELF501_AUTOPAY  = "";//扣帳他行-自動扣帳不可為Y
					ELF501_ATPAYNO ="";//扣帳他行-本行扣帳帳號須為空白
				}
				
				String ELF501_INTDAY_BASE = "2";// 計息天數基礎(固定上傳2)
				String ELF501_INTCUR_BASE = "2";// 利息出帳幣別(固定上傳2)
				Integer ELF501_EXTFROM = c160s01c.getNowFrom();// 寬限期(起)
				Integer ELF501_EXTEND = c160s01c.getNowEnd();// 寬限期(迄)
				String ELF501_EXTTP = "";
				String ELF501_EXTITEM = "";
				Integer ELF501_CPEXTFM = null;
				Integer ELF501_CPEXTED = null;
				Integer ELF501_INEXTFM = null;
				Integer ELF501_INEXTED = null;
				Integer ELF501_INDLINE = null;
				
				if(Util.equals("Y", l140s02e.getAdjCheck())){
					ELF501_EXTTP = Util.trim(l140s02e.getAdjKind());// 展延種類
					ELF501_EXTITEM = Util.trim(l140s02e.getAdjItem());// 展延項目
					ELF501_CPEXTFM = l140s02e.getAdjCapSNum();// 本金展延起始期
					ELF501_CPEXTED = l140s02e.getAdjCapENum();// 本金展延截止期
					ELF501_INEXTFM = l140s02e.getAdjIntSNum();// 利息展期起始期
					ELF501_INEXTED = l140s02e.getAdjIntENum();// 利息展延截止期
					ELF501_INDLINE = l140s02e.getAmorENum();// 應收利息攤還截止期					
				}
				
				BigDecimal ELF501_OVERCHG = (Util.isEmpty(c160s01c
						.getDRateAdd()) ? new BigDecimal(0) : c160s01c
						.getDRateAdd());// 計收遲延利息加碼
				Integer ELF501_OVERMM1 = c160s01c.getDMonth1();// 逾期在個月以內部份
				BigDecimal ELF501_PERCT1 = (BigDecimal) (Util.isEmpty(c160s01c
						.getDRate1()) ? new BigDecimal(0) : new BigDecimal(
						c160s01c.getDRate1()));// 違約金計算條件１－百分比
				Integer ELF501_OVERMM2 = c160s01c.getDMonth2();// 逾期超過個月部份
				BigDecimal ELF501_PERCT2 = (Util.isEmpty(c160s01c.getDRate2()) ? new BigDecimal(
						0) : new BigDecimal(c160s01c.getDRate2()));// 違約金計算條件２－百分比
				String ELF501_CNTRNO_TYPE = Util.trim(c160s01c.getCtrType());
				String ELF501_DISAS_TYPE = Util.trim(l140s02a.getDisasType());
				
				String ELF501_ISCREDIT = Util.trim(l140s02f.getIsCredit());// 連動式房貸註記
				Integer ELF501_PCONBEG = l140s02f.getPConBeg1();// 提前還本管制起期
				Integer ELF501_PCONEND = l140s02f.getPConEnd1();// 提前還本管制迄期
				BigDecimal ELF501_CALCONDI = l140s02f.getPCalCon1();// 提前還本違約金計算條件
				Integer ELF501_PCONEND2 = l140s02f.getPConEnd2();// 提前還本管制迄期2
				Integer ELF501_PCONBEG2 = l140s02f.getPConBeg2();// 提前還本管制起期2
				BigDecimal ELF501_CALCONDI2 = l140s02f.getPCalCon2();// 提前還本違約金計算條件2
				String ELF501_ISTAKFEE = Util.trim(l140s02f.getIsTakFee());// 是否代付費用
				Integer ELF501_TCONEND = l140s02f.getTconend();// 代付費用管制迄期
				// ----------------------轉貸前後為相同性質之政策性貸款(為空值時塞N)-------------------------
				String ELF501_SAME_POLICY = Util.trim(l140s02f.getFavChgCase());// 轉貸前後為相同性質之政策性貸款
				ELF501_SAME_POLICY = (Util.equals(ELF501_SAME_POLICY, "") ? "N"
						: ELF501_SAME_POLICY);
				BigDecimal ELF501_CHGLNBAL = c160s01fDao.getSubAmtSum(
						c160mainid, l140seq, l140mainid);// 轉貸剩餘貸款本金
				// ------------------------C160S01E代償資訊----------------------------

				String ELF501_CHGCASE = Util.trim(c160s01e.getChgOther());// 是否轉貸戶
				Date ELF501_CHGDATE = c160s01e.getOnlentDate();// 轉貸日期
				String ELF501_ORIBANCD = new String();// 轉入之原金融機構代碼
				Date ELF501_ORIPDATE = null;// 原貸款到期日
				Date ELF501_ORI_B_DATE = null;// 轉貸原貸款貸放日期
				String ELF501_CHGREASON = new String();// 代償他行房貸原因
				String ELF501_CHGMEMO = new String();// 代償原因－其他

				// ------------------------C160S01F代償轉貸借新還舊明細檔----------------------
				boolean firstdata = true;
				if (Util.equals(ELF501_CHGCASE, "Y")) {
					for (C160S01F c160s01f : c160s01flist) {
						if (firstdata) {
							ELF501_ORIPDATE = c160s01f.getOLNEndDate();
							ELF501_ORI_B_DATE = c160s01f.getOLNAppDate();
							ELF501_ORIBANCD = c160s01f.getBranchNo();// 只抓第一筆
							ELF501_CHGREASON = c160s01f.getSubReason();// 只抓第一筆
							ELF501_CHGMEMO = c160s01f.getSubReaOth();// 只抓第一筆
							firstdata = false;
						} else if (!Util.isEmpty(c160s01f.getOLNEndDate())) {
							if (Util.isEmpty(ELF501_ORIPDATE)
									|| CapDate.calculateDays(ELF501_ORIPDATE,
											c160s01f.getOLNEndDate()) > 1) {// 取最小值
								ELF501_ORIPDATE = c160s01f.getOLNEndDate();
							}
							if (Util.isEmpty(ELF501_ORI_B_DATE)
									|| CapDate.calculateDays(ELF501_ORI_B_DATE,
											c160s01f.getOLNAppDate()) < 0) {// 取最大值
								ELF501_ORIPDATE = c160s01f.getOLNEndDate();
							}
						}
					}
				}
				// ---------------------------為空值時設定為0001/01/01----------------------
				ELF501_ORIPDATE = (Util.isEmpty(ELF501_ORIPDATE) ? CapDate
						.getDate("0001/01/01", "yyyy/MM/dd") : ELF501_ORIPDATE);
				ELF501_ORI_B_DATE = (Util.isEmpty(ELF501_ORI_B_DATE) ? CapDate
						.getDate("0001/01/01", "yyyy/MM/dd")
						: ELF501_ORI_B_DATE);
				String ELF501_PAYTYPE = Util.addZeroWithValue(
						Util.trim(c160s01c.getPayType()), 2);// 扣帳方式
				String ELF501_DECFLAG = Util.trim(l140s02c.getDecFlag());// 省息遞減註記
				// 當省息遞減為空白 塞入0
				if ("".equals(ELF501_DECFLAG)) {
					ELF501_DECFLAG = "0";
				}
				String ELF501_UPTYPE = Util.trim(l140s02f.getUpType());// 選擇權項目
				Integer ELF501_UPBEG = l140s02f.getUpBeg();// 選擇權起期
				Integer ELF501_UPEND = l140s02f.getUpEnd();// 選擇權迄期
				BigDecimal ELF501_UPRATE = l140s02f.getUpRate();// 選擇權利率
				String ELF501_RATE_PLAN = Util.trim(l140s02f.getRatePlan());// 利率方案
				String ELF501_PROD_PLAN = Util.trim(l140s02f.getProdPlan());// 產品方案
				BigDecimal ELF501_INSC_AMT = l140s02f.getInscAmt();// 借款繳保費之金額
				String ELF501_RMBINS_FLAG = Util.trim(l140s02f.getRmbinsFlag());// 是否搭配房貸壽險(Y/N)
				String ELF501_RMBINT_FLAG = Util.trim(l140s02f.getRmbintFlag());// 是否搭配房貸壽險利率優惠方案(Y/N)
				Integer ELF501_RMBINT_TERM = l140s02f.getRmbintTerm();// 搭配房貸壽險利率優惠方案之期數(999)
				String ELF501_INS_FLAG = Util.trim(l140s02f.getInsFlag());// 是否保費融資(Y/N)
				BigDecimal ELF501_INS_LOANBAL = l140s02f.getInsLoanbal();// 保費融資金額
				String ELF501_ASGNNO = Util.trim(l140s02f.getTagNo());// 勞貸中籤編號
				String tagYear = Util.trim(l140s02f.getTagYear());// 勞貸中籤年份
				String ELF501_ASGNYY = "0".equals(tagYear) ? "" : tagYear;
				String ELF501_REVNO = Util.trim(l140s02f.getTagReNo());// 勞貸收件編號
				String assAppYear = Util.trim(l140s02f.getAssAppYear());// 輔購核准年度
				String ELF501_APRVYY = "0".equals(assAppYear) ? "" : assAppYear;
				String ELF501_LISTNO = Util.trim(l140s02f.getAssBookNo());// 輔購名冊編號
				String ELF501_ALLOWNO = Util.trim(l140s02f.getChkNumber());// 內政部整合住宅方案
				String ELF501_APPLY_NO = Util.trim(l140s02f.getChkNumber());// 安心成家貸款申請編號
				String ELF501_KG_AREA = null;
				if ("E".equals(Util.trim(l140s02f.getKgAgreeYN()))
						|| "B".equals(Util.trim(l140s02f.getKgAgreeYN()))) {
					ELF501_KG_AREA = Util.trim(l140s02f.getKgAgreeYN());// 縣(市)政府首購貸款
				}

				String ELF501_KG_AGREE_NO = Util.trim(l140s02f.getKgAgreeNo());// 縣(市)政府首購貸款核准編號
				Date ELF501_KG_AGREE_DT = l140s02f.getKgAgreeDt();// 縣(市)政府首購貸款核准日期
				Date ELF501_KG_END_DATE = l140s02f.getKgEndDate();// 縣(市)政府首購貸款終止補貼日
				String ELF501_KG_END_CODE = Util.trim(l140s02f.getKgEndCode());// 縣(市)政府首購貸款終止補貼原因
				Date ELF501_KG_INT_DATE = l140s02f.getKgIntDate();// 縣(市)政府首購貸款補貼起日
				String ELF501_INTRID = getcmpId(
						Util.trim(l140s02j.getIntroId()),
						Util.trim(l140s02j.getIntroDupNo()));// 外勞仲介公司統編
				BigDecimal ELF501_INTRRATE = l140s02j.getIntroRate();// 外勞仲介獎金比例
				String ELF501_STOCK_RATE = Util.trim(l140m01a.getMRateType());// 計算擔保維持率註記(為null時設定為N)
				ELF501_STOCK_RATE = Util.isEmpty(ELF501_STOCK_RATE) ? "N"
						: ELF501_STOCK_RATE;
				String ELF501_TRANS_CODE = Util.trim(l140s02a.getTransCode());// 開戶原因
				String ELF501_ORG_LOAN_NO = Util.trim(l140s02a.getOrgLoanNo());// 承接之前放款帳號
				// BigDecimal ELF501_LNAP_AMT = c160s01c.getLoanAmt();//
				// 帳號(科目)限額
				BigDecimal ELF501_LNAP_AMT = c160s01c.getApprovedAmt();// 帳號(科目)限額
				BigDecimal ELF501_TAXRATE = l140s02c.getTaxRate();// 扣稅負擔值(為null時設定為0)
				ELF501_TAXRATE = Util.isEmpty(ELF501_TAXRATE) ? BigDecimal.ONE
						: ELF501_TAXRATE;
				BigDecimal ELF501_AGENCY_AMT = c160s01c.getAgencyAMT();// 開辦費
				if(CrsUtil.is_67(ELF501_LNTYPE) || CrsUtil.is_70(ELF501_LNTYPE)){
					/*
					 	開辦費 已改為 L140M01R
					  	在 ELF501 沒有另外開欄位放 '每期利息上限金額'
					*/
					ELF501_AGENCY_AMT = c160s01c.getRmIntMax();
				}
				
				Timestamp ELF501_ONLNTIME = null;// A-Loan 執行時間 on Line
				Timestamp ELF501_BTHTIME = null;// A-Loan 執行時間 Batch
				String ELF501_GRADE = LMSUtil.getFinalGradeUpUse(Util
						.trim(l140s02a.getGrade1()));// 評等
				Timestamp ELF501_TMESTAMP = CapDate.getCurrentTimestamp();
				// -----

				// 因應改科目後MIS.ELF501要多上傳一筆新案上去...
				if (Util.isNotEmpty(ELF501_LOAN_NO_SRC)) {
					if (!ELF501_LOAN_NO_SRC.equals(ELF501_LOAN_NO)) {
						ELF501_SEQ_NO = ELF501_SEQ_NO + 50;
					}
				}

				if (Util.isEmpty(ELF501_LOAN_NO)) {
					ELF501 MISELF501 = mislf501seervic.findByUniqueKey1(
							ELF501_CUSTID, ELF501_DUPNO, ELF501_CNTRNO,
							ELF501_SEQ_NO);
					if (!Util.isEmpty(MISELF501)) {
						if (!Util.isEmpty(MISELF501.getElf501_loan_no())) {
							ELF501_LOAN_NO = Util.trim(MISELF501
									.getElf501_loan_no());
							ELF501_ONLNTIME = MISELF501.getElf501_onlntime();
							ELF501_BTHTIME = MISELF501.getElf501_bthtime();
							ELF501_TMESTAMP = MISELF501.getElf501_tmestamp();
						}
					}
				} else {
					ELF501 MISELF501 = mislf501seervic.findByUniqueKey2(
							ELF501_CUSTID, ELF501_DUPNO, ELF501_CNTRNO,
							ELF501_LOAN_NO);
					if (!Util.isEmpty(MISELF501)) {
						ELF501_SEQ_NO = MISELF501.getElf501_seq_no();
						ELF501_LOAN_NO = Util.trim(MISELF501
								.getElf501_loan_no());
						ELF501_ONLNTIME = MISELF501.getElf501_onlntime();
						ELF501_BTHTIME = MISELF501.getElf501_bthtime();
						ELF501_TMESTAMP = MISELF501.getElf501_tmestamp();
					}
				}
				String ELF501_TELLER = Util.getRightStr(apprId, 5);
				String ELF501_SVNPVNO = Util.getRightStr(reCheckId, 5);
				// 寫入Bean
				ELF501 elf501 = new ELF501();
				elf501.setElf501_custid(ELF501_CUSTID);
				elf501.setElf501_dupno(ELF501_DUPNO);
				elf501.setElf501_cntrno(ELF501_CNTRNO);
				elf501.setElf501_seq_no(ELF501_SEQ_NO);
				elf501.setElf501_swft(ELF501_SWFT);
				elf501.setElf501_loan_no(ELF501_LOAN_NO);
				elf501.setElf501_lntype(ELF501_LNTYPE);
				elf501.setElf501_loantp(ELF501_LOANTP);
				elf501.setElf501_fndsre(ELF501_FNDSRE);
				elf501.setElf501_fund_type2(ELF501_FUND_TYPE2);
				elf501.setElf501_lnpurs(ELF501_LNPURS);
				elf501.setElf501_lnpurs_sub(ELF501_LNPURS_SUB);
				elf501.setElf501_ln_purpose(ELF501_LN_PURPOSE);
				elf501.setElf501_residential(ELF501_RESIDENTIAL);
				elf501.setElf501_own_house(ELF501_OWN_HOUSE);
				elf501.setElf501_risk_rating(ELF501_RISK_RATING);
				elf501.setElf501_monthcnt(ELF501_MONTHCNT);
				elf501.setElf501_ginttype(ELF501_GINTTYPE);
				elf501.setElf501_hinttype(ELF501_HINTTYPE);
				elf501.setElf501_avgpay(ELF501_AVGPAY);
				elf501.setElf501_paycle(ELF501_PAYCLE);
				elf501.setElf501_ehpaycpt(ELF501_EHPAYCPT);
				elf501.setElf501_taxno(ELF501_TAXNO);
				elf501.setElf501_taxaddr(ELF501_TAXADDR);
				elf501.setElf501_importid(ELF501_IMPORTID);
				elf501.setElf501_efctbh(ELF501_EFCTBH);
				elf501.setElf501_insc_no(ELF501_INSC_NO);
				elf501.setElf501_autorct(ELF501_AUTORCT);
				elf501.setElf501_rctdate(ELF501_RCTDATE);
				elf501.setElf501_accno(ELF501_ACCNO);
				elf501.setElf501_rctamt(ELF501_RCTAMT);
				elf501.setElf501_autopay(ELF501_AUTOPAY);
				elf501.setElf501_atpayno(ELF501_ATPAYNO);
				elf501.setElf501_intday_base(ELF501_INTDAY_BASE);
				elf501.setElf501_intcur_base(ELF501_INTCUR_BASE);
				elf501.setElf501_extfrom(ELF501_EXTFROM);
				elf501.setElf501_extend(ELF501_EXTEND);
				elf501.setElf501_exttp(ELF501_EXTTP);
				elf501.setElf501_extitem(ELF501_EXTITEM);
				elf501.setElf501_cpextfm(ELF501_CPEXTFM);
				elf501.setElf501_cpexted(ELF501_CPEXTED);
				elf501.setElf501_inextfm(ELF501_INEXTFM);
				elf501.setElf501_inexted(ELF501_INEXTED);
				elf501.setElf501_indline(ELF501_INDLINE);
				elf501.setElf501_overchg(ELF501_OVERCHG);
				elf501.setElf501_overmm1(ELF501_OVERMM1);
				elf501.setElf501_perct1(ELF501_PERCT1);
				elf501.setElf501_overmm2(ELF501_OVERMM2);
				elf501.setElf501_perct2(ELF501_PERCT2);
				elf501.setElf501_iscredit(ELF501_ISCREDIT);
				elf501.setElf501_pconbeg(ELF501_PCONBEG);
				elf501.setElf501_pconend(ELF501_PCONEND);
				elf501.setElf501_calcondi(ELF501_CALCONDI);
				elf501.setElf501_pconend2(ELF501_PCONEND2);
				elf501.setElf501_pconbeg2(ELF501_PCONBEG2);
				elf501.setElf501_calcondi2(ELF501_CALCONDI2);
				elf501.setElf501_istakfee(ELF501_ISTAKFEE);
				elf501.setElf501_tconend(ELF501_TCONEND);
				elf501.setElf501_chgcase(ELF501_CHGCASE);
				elf501.setElf501_chglnbal(ELF501_CHGLNBAL);
				// ----------------C160S01F-----------------
				elf501.setElf501_oripdate(ELF501_ORIPDATE);
				elf501.setElf501_chgdate(ELF501_CHGDATE);
				elf501.setElf501_oribancd(ELF501_ORIBANCD);
				elf501.setElf501_same_policy(ELF501_SAME_POLICY);
				elf501.setElf501_ori_b_date(ELF501_ORI_B_DATE);
				elf501.setElf501_chgreason(ELF501_CHGREASON);
				elf501.setElf501_chgmemo(ELF501_CHGMEMO);
				// ----------------------------------------
				elf501.setElf501_paytype(ELF501_PAYTYPE);
				elf501.setElf501_uptype(ELF501_UPTYPE);
				elf501.setElf501_upbeg(ELF501_UPBEG);
				elf501.setElf501_upend(ELF501_UPEND);
				elf501.setElf501_uprate(ELF501_UPRATE);
				elf501.setElf501_decflag(ELF501_DECFLAG);
				elf501.setElf501_rate_plan(ELF501_RATE_PLAN);
				elf501.setElf501_prod_plan(ELF501_PROD_PLAN);
				elf501.setElf501_insc_amt(ELF501_INSC_AMT);
				elf501.setElf501_rmbins_flag(ELF501_RMBINS_FLAG);
				elf501.setElf501_rmbint_flag(ELF501_RMBINT_FLAG);
				elf501.setElf501_rmbint_term(ELF501_RMBINT_TERM);
				elf501.setElf501_ins_flag(ELF501_INS_FLAG);
				elf501.setElf501_ins_loanbal(ELF501_INS_LOANBAL);
				elf501.setElf501_asgnno(ELF501_ASGNNO);
				elf501.setElf501_asgnyy(ELF501_ASGNYY);
				elf501.setElf501_revno(ELF501_REVNO);
				elf501.setElf501_aprvyy(ELF501_APRVYY);
				elf501.setElf501_listno(ELF501_LISTNO);
				elf501.setElf501_allowno(ELF501_ALLOWNO);
				elf501.setElf501_apply_no(ELF501_APPLY_NO);
				elf501.setElf501_kg_area(ELF501_KG_AREA);
				elf501.setElf501_kg_agree_no(ELF501_KG_AGREE_NO);
				elf501.setElf501_kg_agree_dt(ELF501_KG_AGREE_DT);
				elf501.setElf501_kg_end_date(ELF501_KG_END_DATE);
				elf501.setElf501_kg_end_code(ELF501_KG_END_CODE);
				elf501.setElf501_kg_int_date(ELF501_KG_INT_DATE);
				elf501.setElf501_intrid(ELF501_INTRID);
				elf501.setElf501_intrrate(ELF501_INTRRATE);
				elf501.setElf501_stock_rate(ELF501_STOCK_RATE);
				elf501.setElf501_trans_code(ELF501_TRANS_CODE);
				elf501.setElf501_org_loan_no(ELF501_ORG_LOAN_NO);
				// elf501.setElf501_updater(Util.getRightStr(user.getUserId(),5));
				elf501.setElf501_tmestamp(ELF501_TMESTAMP);
				elf501.setElf501_eloantimes(CapDate.getCurrentTimestamp());
				elf501.setElf501_lnap_amt(ELF501_LNAP_AMT);
				elf501.setElf501_teller(ELF501_TELLER);
				elf501.setElf501_supvno(ELF501_SVNPVNO);
				elf501.setElf501_taxrate(Util.parseBigDecimal(ELF501_TAXRATE));
				elf501.setElf501_agency_amt(ELF501_AGENCY_AMT);
				elf501.setElf501_onlntime(ELF501_ONLNTIME);
				elf501.setElf501_bthtime(ELF501_BTHTIME);
				elf501.setElf501_grade(ELF501_GRADE);
				elf501.setElf501_ovbal_type(ELF501_OVBAL_TYPE);
				elf501.setElf501_cntrno_type(ELF501_CNTRNO_TYPE);
				elf501.setElf501_disas_type(ELF501_DISAS_TYPE);
				elf501.setElf501_mega_code(Util.trim(l140s02a.getMegaCode())); 
				if(true){ // J-107-0091
					if(Util.isEmpty(elf501.getElf501_mega_code()) && Util.isNotEmpty(Util.trim(l140m01a.getMegaCode()))){
						elf501.setElf501_mega_code(Util.trim(l140m01a.getMegaCode()));	
					}					
				}
				elf501.setElf501_agnt_fbfg(Util.trim(l140s02a.getAgntFbfg()));
				elf501.setElf501_1st_rt_dt(c160s01c.getPmt_1st_rt_dt());
				elf501.setElf501_esggtype(Util.trim(l140s02a.getEsggtype()));
				//J-113-0397 個金簽報書新增社會責任授信欄位
				elf501.setElf501_socialKind(Util.trim(l140s02a.getSocialKind()));
				elf501.setElf501_socialTa(Util.trim(l140s02a.getSocialTa()));
				elf501.setElf501_socialResp(Util.trim(l140s02a.getSocialResp()));
				//~~~~~~~~~~~~~~~
				data.add(elf501);
			}
		}
		return data;
	}

	private List<ELF501> upELF501Case3(List<ELF501> data, C160M01A c160m01a,
			C160M01F c160m01f, C160S01D c160s01d) {
		String ELF501_CUSTID = Util.getLeftStr(Util.trim(c160s01d.getCustId()),
				10);// 借款人身分證字號
		String ELF501_DUPNO = Util.getRightStr(Util.trim(c160s01d.getCustId()),
				1);// 重覆碼
		String ELF501_CNTRNO = Util.trim(c160s01d.getCntrNo());// 額度序號
		Integer ELF501_SEQ_NO = 1;// 額度案件序號
		String ELF501_SWFT = "TWD";// 幣別
		String ELF501_LNTYPE = Util.trim(c160m01f.getProdKind());// 產品種類
		String ELF501_LOANTP = Util.trim(c160m01f.getSubjCode());// 科目代碼
		BigDecimal ELF501_LNAP_AMT = c160s01d.getLoanTotAmt();// 帳號(科目)限額
		String ELF501_FNDSRE = Util.trim(c160m01f.getFFund());// 資金來源
		String ELF501_FUND_TYPE2 = "101";// 資金來源小類
		String ELF501_LNPURS = Util.trim(c160m01f.getLnPurs());// 用途別
		String ELF501_LN_PURPOSE = "P";// 融資業務分類
		String ELF501_RESIDENTIAL = "N";// 是否屬興建房屋
		String ELF501_GINTTYPE = Util.trim(c160m01f.getIntWay());// 計息方式
		String ELF501_HINTTYPE = Util.trim(c160m01f.getRIntWay());// 收息方式
		String ELF501_AVGPAY = Util.trim(c160s01d.getPayWay());// 償還方式
		String ELF501_PAYCLE = Util.trim(c160m01f.getPayWay());// 繳款週期/雙週
		String ELF501_APPLY_NO = Util.trim(c160s01d.getStaffNo());
		String ELF501_AUTORCT = Util.trim(c160s01d.getAutoRct());// 自動進帳
		Date ELF501_RCTDATE = c160s01d.getRctDate();// 進帳日期
		String ELF501_ACCNO = Util.trim(c160s01d.getAccNo());// 存款帳號
		BigDecimal ELF501_RCTAMT = c160s01d.getLoanTotAmt();// 進帳金額
		String ELF501_AUTOPAY = Util.trim(c160s01d.getAutoPay());// 自動扣帳
		String ELF501_ATPAYNO = Util.trim(c160s01d.getAtpayNo());// 扣帳帳號
		String ELF501_INTDAY_BASE = "2";// 計息天數基礎
		String ELF501_INTCUR_BASE = "2";// 利息出帳幣別
		BigDecimal ELF501_OVERCHG = c160m01f.getDRateAdd();// 計收遲延利息加碼
		Integer ELF501_OVERMM1 = 6;// 逾期在個月以內部份
		BigDecimal ELF501_PERCT1 = Util.parseBigDecimal(c160m01f.getDRate1());// 違約金計算條件１－百分比
		Integer ELF501_OVERMM2 = 6;// 逾期超過個月部份
		BigDecimal ELF501_PERCT2 = new BigDecimal(20);// 違約金計算條件２－百分比
		String ELF501_CHGCASE = "N";// 是否轉貸戶
		String ELF501_PAYTYPE = "00";// 扣帳方式
		String ELF501_TELLER = Util.addZeroWithValue(
				Util.trim(c160m01f.getCName()), 5);// 櫃員代號

		ELF501 elf501 = new ELF501();
		elf501.setElf501_custid(ELF501_CUSTID);
		elf501.setElf501_dupno(ELF501_DUPNO);
		elf501.setElf501_cntrno(ELF501_CNTRNO);
		elf501.setElf501_seq_no(ELF501_SEQ_NO);
		elf501.setElf501_swft(ELF501_SWFT);
		elf501.setElf501_lntype(ELF501_LNTYPE);
		elf501.setElf501_loantp(ELF501_LOANTP);
		elf501.setElf501_lnap_amt(ELF501_LNAP_AMT);
		elf501.setElf501_fndsre(ELF501_FNDSRE);
		elf501.setElf501_fund_type2(ELF501_FUND_TYPE2);
		elf501.setElf501_lnpurs(ELF501_LNPURS);
		elf501.setElf501_ln_purpose(ELF501_LN_PURPOSE);
		elf501.setElf501_residential(ELF501_RESIDENTIAL);
		elf501.setElf501_ginttype(ELF501_GINTTYPE);
		elf501.setElf501_hinttype(ELF501_HINTTYPE);
		elf501.setElf501_avgpay(ELF501_AVGPAY);
		elf501.setElf501_paycle(ELF501_PAYCLE);
		elf501.setElf501_autorct(ELF501_AUTORCT);
		elf501.setElf501_rctdate(ELF501_RCTDATE);
		elf501.setElf501_accno(ELF501_ACCNO);
		elf501.setElf501_rctamt(ELF501_RCTAMT);
		elf501.setElf501_autopay(ELF501_AUTOPAY);
		elf501.setElf501_atpayno(ELF501_ATPAYNO);
		elf501.setElf501_intday_base(ELF501_INTDAY_BASE);
		elf501.setElf501_intcur_base(ELF501_INTCUR_BASE);
		elf501.setElf501_overchg(ELF501_OVERCHG);
		elf501.setElf501_overmm1(ELF501_OVERMM1);
		elf501.setElf501_perct1(ELF501_PERCT1);
		elf501.setElf501_overmm2(ELF501_OVERMM2);
		elf501.setElf501_perct2(ELF501_PERCT2);
		elf501.setElf501_chgcase(ELF501_CHGCASE);
		elf501.setElf501_paytype(ELF501_PAYTYPE);
		elf501.setElf501_teller(ELF501_TELLER);
		elf501.setElf501_tmestamp(CapDate.getCurrentTimestamp());// 資料修改日期
		elf501.setElf501_eloantimes(CapDate.getCurrentTimestamp());// ELOAN寫入時間
		elf501.setElf501_apply_no(ELF501_APPLY_NO);
		elf501.setElf501_ovbal_type("");
		elf501.setElf501_cntrno_type("2");//ref 中鋼團貸 918110400203,科目:321 
		elf501.setElf501_disas_type("");
		elf501.setElf501_mega_code("");
		//~~~~~~~~~~~~~~~
		data.add(elf501);
		return data;
	}

	private List<ELF500> upELF500_when_caseType1_2(List<ELF500> data, C160M01A c160m01a,
			C160M01B c160m01b, L120M01A l120m01aNow, L140M01A l140m01a,
			String apprId, String reCheckId, String sDate) {
		String c160mainId = c160m01a.getMainId();

		// 2013-09-06團貸案上傳MIS.ELF500的ELF500_GRANTNO錯誤問....
		if (!Util.isEmpty(l140m01a)) {
			
			String l140mainid = l140m01a.getMainId();
			String l140custid = l140m01a.getCustId();
			String l140dupno = l140m01a.getDupNo();
			String l120mainid = l120m01aNow.getMainId();
			// 借款人主檔
			C120M01A c120m01a = c120m01aDao.findByUniqueKey(l120mainid,
					l140custid, l140dupno);
			// 個金基本資料檔
			C120S01A c120s01a = c120s01aDao.findByUniqueKey(l120mainid,
					l140custid, l140dupno);
			// 個金服務單位檔
			C120S01B c120s01b = c120s01bDao.findByUniqueKey(l120mainid,
					l140custid, l140dupno);
			// 個金償債能力檔
			C120S01C c120s01c = c120s01cDao.findByUniqueKey(l120mainid,
					l140custid, l140dupno);
			// 個金相關查詢檔
			C120S01E c120s01e = c120s01eDao.findByUniqueKey(l120mainid,
					l140custid, l140dupno);
			// 個金產品種類檔
			List<C160S01C> c160s01clist = c160s01cDao.findByMainIdRefMainid(
					c160mainId, l140mainid);
			// 個金額度明細表主檔補充資料檔
			L140M03A l140m03a = l140m03aDao.findByUniqueKey(l140mainid);
			// 維護央行購屋/空地/建屋貸款註記資訊
			L140M01M l140m01m = l140m01mDao.findByMainId(l140mainid);
			// 個金借保人檔
			List<C160S01B> c160s01blist = c160s01bDao.findByMainIdRefMainId(
					c160mainId, l140mainid);
			// 個金產品種類檔
			List<L140S02A> l140s02alist = l140s02aDao.findByMainId(l140mainid);

			String ELF500_CUSTID = Util.trim(l140custid);// 客戶統編
			String ELF500_DUPNO = Util.trim(l140dupno);// 重覆序號
			String ELF500_STAFFNO = "";// 職工編號
			String ELF500_CNTRNO = Util.trim(l140m01a.getCntrNo());// 額度序號
			String ELF500_GRANTNO = Util.trim(l120m01aNow.getCaseLvl());// 案件審層級
			String ELF500_LNCICL = (Util.equals(Util.trim(l140m01a.getReUse()),
					"2") ? "Y" : "N");// 是否循環使用
			String ELF500_SWFT = Util.trim(l140m01a.getCurrentApplyCurr());// 現請額度-幣別

			// 2014-10-23改用C160M01B上傳 BigDecimal ELF500_FACTAMT =
			// (Util.isEmpty(l140m01a.getCurrentApplyAmt()) ? BigDecimal.ZERO :
			// l140m01a.getCurrentApplyAmt());// 現請額度-金額
			BigDecimal ELF500_FACTAMT = (Util.isEmpty(c160m01b.getLoanTotAmt()) ? BigDecimal.ZERO
					: c160m01b.getLoanTotAmt());// 現請額度-金額

			String ELF500_DOCUMENT_NO = getUploadCaseNo(l120m01aNow,
					ELF500_CNTRNO);
			String ELF500_GRP_CNTRNO = "";
			String ELF500_IDENTITY_NO = "";
			String ELF500_WELFARE_CMTE = "";
			String ELF500_NOTICE_TYPE = "";
			String ELF500_COMPANY_ID = "";
			String ELF500_COMPANY_NM = "";
			String ELF500_CMBULL_KEY = "";
			Date ELF500_REGISTER_DT = null;
			Date ELF500_APPLY_DATE = null;
			BigDecimal ELF500_CAPITAL_AMT = new BigDecimal(0);
			String ELF500_ASSIST_FLAG = "";
			String ELF500_CONTROL_CD = "";
			String ELF500_PLUS_REASON = "";
			BigDecimal ELF500_LTV_RATE = new BigDecimal(0);
			String ELF500_LOCATION_CD = "";
			String ELF500_JCIC_MARK = "";
			BigDecimal ELF500_APP_AMT = new BigDecimal(0);
			String ELF500_COCOLL_FG = "";
			BigDecimal ELF500_SUM_FACTAMT = new BigDecimal(0);
			String ELF500_PART_FUND = "";
			String ELF500_PLUS_MEMO = "";
			String ELF500_REG_PURPOSE = "";
			String ELF500_COMPANY_AREA = "";
			String ELF500_POS = "";
			String ELF500_PLAN_AREA = "";
			String ELF500_P_USETYPE = "";
			String ELF500_P_LOANUSE = "";
			String ELF500_COLL_CHAR = "";
			String ELF500_KEEP_LAWVAL = "";
			BigDecimal ELF500_YPAY = new BigDecimal(0);
			BigDecimal ELF500_HINCOME = new BigDecimal(0);
			BigDecimal ELF500_OMONEY = new BigDecimal(0);
			BigDecimal ELF500_EST_AMT = new BigDecimal(0);
			BigDecimal ELF500_LAWVAL = new BigDecimal(0);
			String ELF501_RESTRICT = "";
			String ELF501_HP_HOUSE = "";
			String ELF500_COLL_CHAR_M = "";
			BigDecimal ELF500_SITE3NO = new BigDecimal(0);
			String ELF500_SITE4NO = "";
			// -------------------------授信、動用期間及信保成數----------------------
			// ※依該份額度明細表所有的產品最大期間當作期授信期間。
			String ELF500_LLNNO = "";
			Date ELF500_CNTFROM = new Date();
			Date ELF500_CNTEND = new Date();
			Integer ELF500_LLNMON = 0;
			String ELF500_LNUSENO = "";
			Date ELF500_USEFMDT = new Date();
			Date ELF500_USEENDT = new Date();
			Date ELF500_TMESTAMP = CapDate.getCurrentTimestamp();
			Integer ELF500_USEFTMN = 0;
			String ELF500_STATUS = "";
			Date ELF500_ALOANTIMES = null;

			String ELF500_EDU = "";
			String ELF500_MARRY = "";
			Integer ELF500_CHILD = 0;
			BigDecimal ELF500_SENIORITY = BigDecimal.ZERO;

			BigDecimal ELF500_DRATE = new BigDecimal(0);
			BigDecimal ELF500_YRATE = new BigDecimal(0);
			String ELF500_CREDIT = "";
			String ELF500_ISPERIODFUND = "";
			String ELF500_BUSI = "";
			String ELF500_INVMBALCURR = "";
			BigDecimal ELF500_INVMBALAMT = new BigDecimal(0);
			String ELF500_INVOBALCURR = "";
			BigDecimal ELF500_INVOBALAMT = new BigDecimal(0);
			String ELF500_BRANCURR = "";
			BigDecimal ELF500_BRANAMT = new BigDecimal(0);
			String ELF500_CMSSTATUS = "";
			Date ELF500_EJCICQDATE = null;
			String ELF500_REPAYFUND = "N";
			String ELF500_PROJ_CLASS = "";
			String ELF500_ITW_CODE = "";
			String ELF500_SIXCOR_CODE = "";
			BigDecimal elf500_house_age = BigDecimal.ZERO;
			BigDecimal elf500_loanAmt = BigDecimal.ZERO; 
			String ELF500_VERSION = null;
			String ELF500_HLOANLIMIT = null;
			String ELF500_HLOANLIMIT_2 = null;
			Date ELF500_ENDDATE = null;
			String ELF500_ISRENEW = "";
			String ELF500_ISPAY_OLD = "";
			BigDecimal ELF500_OLD_QUOTA = null;// J-111-0534：隨此欄位於畫面上被棄用，已不需放值於DB
			BigDecimal ELF500_PAY_OLD_AMT = BigDecimal.ZERO;
			String ELF500_PAY_OLD_ITEM = "";
			String ELF500_ISMATCH_ITEM = "";
			String ELF500_ISSALE_CASE = "";
			Date ELF500_LSTDATE = null;
			BigDecimal ELF500_TIMEVAL = BigDecimal.ZERO;
			
			BigDecimal ELF500_CINSPENT = (Util
					.isEmpty(l140m01a.getGutPercent()) ? new BigDecimal(0)
					: l140m01a.getGutPercent());
			//J-111-0135 新增信保基金核准之保證手續費率
			BigDecimal ELF500_CGF_RATE = (Util
					.isEmpty(l140m01a.getCgfRate()) ? new BigDecimal(0)
					: l140m01a.getCgfRate());
			// J-113-0417 配合修改LLMLN998消金授信案件例外管理報表，ELF500新增寫入家庭所得
			BigDecimal ELF500_FINCOME = new BigDecimal(0);
			// J-113-0323 配合0403花蓮震災新增房貸信保註記
			String ELF500_SYND_IPFD = "";
			
			int count = 0;			
			boolean has_69 = false;
			for (C160S01C c160s01c : c160s01clist) {
				if (count == 0) {// 第一筆資料直接塞值
					ELF500_CNTFROM = c160s01c.getLnStartDate();
					ELF500_CNTEND = c160s01c.getLnEndDate();
					ELF500_USEFMDT = c160s01c.getUseStartDate();
					ELF500_USEENDT = c160s01c.getUseEndDate();
					count++;
				} else {
					// 取得授信期間-起始日期最小值
					ELF500_CNTFROM = getMinDate(c160s01c.getLnStartDate(), ELF500_CNTFROM);
					
					// 取得授信期間-截止日期最大值
					ELF500_CNTEND = getMaxDate(c160s01c.getLnEndDate(), ELF500_CNTEND);
					
					// 取得動用期間-起始日期最小值
					ELF500_USEFMDT = getMinDate(c160s01c.getUseStartDate(), ELF500_USEFMDT);
					
					// 取得動用期間-截止日期最大值
					ELF500_USEENDT = getMaxDate(c160s01c.getUseEndDate(), ELF500_USEENDT);
				}
				if (Util.equals(c160s01c.getProdKind(), ProdKindEnum.政策性留學生貸款_36.getCode())) {// 當產品種類有一個為學生貸款時
					// 信保成數設定為80
					ELF500_CINSPENT = new BigDecimal(80);
				}
				if (Util.equals(c160s01c.getProdKind(), ProdKindEnum.勞工紓困貸款_69.getCode())) {					
					has_69 = true;
				}
			}
			if(has_69){
				ELF500_CINSPENT = new BigDecimal(100);
			}
			if(ELF500_CINSPENT==null){
				ELF500_CINSPENT = BigDecimal.ZERO;
			}
			// --------------------------共同借款人註記--------------------------------
			// 若主從債務人裡有一個共同借款人(C)則共同借款人註記為Y
			String CustPosCheck = "N";
			for (C160S01B c160s01b : c160s01blist) {
				if (Util.equals(c160s01b.getRType(), "C")) {
					CustPosCheck = "Y";
				}
			}
			String ELF500_COBORFLG = CustPosCheck;// 共同借款人註記
			// --------------------------ＤＢＲ２２倍無擔保額度------------------------
			BigDecimal ddr2factsum = new BigDecimal(0);
			for (L140S02A l140s02a : l140s02alist) {// 取簽報書額度明細表內產品種類DBR22倍規範額度總和
				if (!Util.isEmpty(l140s02a.getDBR22Amt())) {
					ddr2factsum = ddr2factsum.add(l140s02a.getDBR22Amt());
				}
			}
			BigDecimal ELF500_DBR22_FACT = ddr2factsum;
			// --------------------------額度控管種類----------------------------------
			String ELF500_FACTTYPE = Util.trim(l140m01a.getSnoKind());
			if (Util.equals(ELF500_FACTTYPE, "10")) {// 10.一般
				ELF500_FACTTYPE = "51";// 一般消金戶
			} else if (Util.equals(ELF500_FACTTYPE, "20")) {// 20.信保
				ELF500_FACTTYPE = "50";// 消金信保母戶
			}
			if (!Util.isEmpty(c120s01e)) {
				ELF500_EJCICQDATE = (Date) c120s01e.getEJcicQDate();
			}
			
			//在途的案件，如何處理？
			boolean cls_is_relate = clsService.cls_is_relate(l120m01aNow, l140m01a);
			String ELF500_RELATE = "";
			if(cls_is_relate){
				ELF500_RELATE = "A"; 
				//~~~~~~~~~~~~~~~~~~~~~~~~~~
				if(Util.isEmpty(Util.trim(l140m01a.getUnsecureFlag()))){
					//當 UnsecureFlag 空白
					if(clsService.cls_need_l140m01a_unsecureFlag(l120m01aNow, l140m01a)){
						//控管的功能上版前，即使「無擔保科目」應輸入 unsecureFlag
						//但 當時 UI無欄位可供輸入
						
						/////////////////////////////////////////////////////////////////
						//以下的 block 是原本的邏輯
						if(true){
							ELF500_RELATE = "N";
							if (!Util.isEmpty(c120s01e)) {
								ELF500_EJCICQDATE = (Date) c120s01e.getEJcicQDate();

								// 若本行利害關係人、金控法利害關係人(44條)、金控法利害關係人(45條)有一項為是(1)時
								if (Util.equals(Util.trim(c120s01e.getIsQdata2()), "1")
										|| Util.equals(Util.trim(c120s01e.getIsQdata3()), "1")
										|| Util.equals(Util.trim(c120s01e.getIsQdata16()), "1")) {
									// 簽案時利害關係人註記為Y
									ELF500_RELATE = "Y";
								}
							}
						}
						/////////////////////////////////////////////////////////////////
					}else{
						//可能是 有擔科目，所以不必填 unsecureFlag
					}
				}else{
					//當 UnsecureFlag 有值，表示簽案當時，是在控管的功能上版後
					//要做 無擔科目，被系統要求要輸入 unsecureFlag
				}		
			}else{
				ELF500_RELATE = "B";
			}
			
			// ---------------------------動審表種類為團貸時上傳-------------------------
			String ELF500_CMPID = new String();
			String ELF500_LOTNO = new String();
			if (Util.equals(c160m01a.getCaseType(), "2")) {
				// 團貸總戶之額度序號
				ELF500_GRP_CNTRNO = Util
						.nullToSpace(c160m01a.getLoanMasterNo());
				if (Util.equals(ELF500_GRP_CNTRNO, "")) {// 若為空白則改取本案核准之編號
					ELF500_GRP_CNTRNO = Util.trim(c160m01a.getApprovedNo());
				}
				ELF500_CMPID = getcmpId(c160m01a.getCustId(),
						c160m01a.getDupNo());
				ELF500_LOTNO = Util.addZeroWithValue(
						Util.trim(c160m01a.getPackNo()), 4);
				ELF500_STAFFNO = Util.trim(c120m01a.getStaffNo());// 職工編號
			}

			if (!Util.isEmpty(l140m03a)) {
				ELF500_NOTICE_TYPE = Util.trim(l140m03a.getNoticeType());
				ELF500_COMPANY_ID = Util.trim(l140m03a.getCompanyId());
				ELF500_COMPANY_NM = Util.trim(l140m03a.getCompanyName());
				ELF500_REGISTER_DT = (Date) l140m03a.getRegisterDate();
				ELF500_CAPITAL_AMT = (Util.isEmpty(l140m03a.getCapitalAMT()) ? new BigDecimal(
						0) : l140m03a.getCapitalAMT());
				ELF500_ASSIST_FLAG = Util.trim(l140m03a.getAssisTypeYN());
				ELF500_COMPANY_AREA = Util.trim(l140m03a.getCompanyArea());
				ELF500_CMBULL_KEY = Util.trim(l140m03a.getCompanyIndustryCode());//青創同一事業體行業代碼
				ELF500_APPLY_DATE = (Date) l140m03a.getApplicationDate(); // 2014-01-20
																			// 增加上傳青創申請日期
				ELF500_REPAYFUND = Util.equals("Y",l140m03a.getRepayFund())?"Y":"N";
				ELF500_IDENTITY_NO = Util.trim(l140m03a.getIdentityNo());
				ELF500_WELFARE_CMTE = Util.isEmpty(l140m03a.getWelfare_cmte()) ? ELF500_WELFARE_CMTE : l140m03a.getWelfare_cmte();//J-108-0238 台電消貸明細  增加扣薪福委會代號
			}
			if (!Util.isEmpty(l140m01m)) {
				ELF500_SITE3NO = l140m01m.getSit3No();
				ELF500_SITE4NO = Util.trim(l140m01m.getSit4No());
				ELF501_RESTRICT = Util.trim(l140m01m.getIsLimitCust());
				ELF501_HP_HOUSE = Util.trim(l140m01m.getIsHighHouse());
				ELF500_COLL_CHAR_M = Util.trimSizeInOS390(
						Util.trim(l140m01m.getCmsOther()), 20);
				ELF500_CONTROL_CD = Util.trim(l140m01m.getCbcCase());
				ELF500_PLUS_REASON = Util.trim(l140m01m.getPlusReason());
				ELF500_LTV_RATE = (Util.isEmpty(l140m01m.getPayPercent()) ? new BigDecimal(
						0) : l140m01m.getPayPercent());
				ELF500_LOCATION_CD = LMSUtil.getUploadLocationCd(Util.trim(l140m01m
						.getAreaId()));

				// if ("B".equals(Util.trim(l140m01m.getCustYN()))) {
				// ELF500_JCIC_MARK="Y";
				// } else {
				ELF500_JCIC_MARK = Util.trim(l140m01m.getCustYN());
				// }

				ELF500_APP_AMT = (Util.isEmpty(l140m01m.getAppAmt()) ? new BigDecimal(
						0) : l140m01m.getAppAmt());
				ELF500_COCOLL_FG = Util.trim(l140m01m.getCommonYN());
				ELF500_PART_FUND = Util.trim(l140m01m.getShareCollYN());
				// 如果shareCollYN & commonYN皆等於N時 ，但有金額時要將金額清掉
				if ("N".equals(Util.trim(l140m01m.getCommonYN()))
						&& "N".equals(Util.trim(l140m01m.getShareCollYN()))) {
					ELF500_SUM_FACTAMT = BigDecimal.ZERO;
				} else {
					ELF500_SUM_FACTAMT = l140m01m.getShareCollAmt();
				}

				ELF500_PLUS_MEMO = Util.trimSizeInOS390(Util
						.toFullCharString(Util.trim(l140m01m
								.getPlusReasonMeMo())), 62);
				ELF500_REG_PURPOSE = Util.trim(l140m01m.getBuildYN());
				ELF500_PLAN_AREA = Util.trim(l140m01m.getHouseYN());
				ELF500_P_USETYPE = Util.trim(l140m01m.getHouseType());
				ELF500_P_LOANUSE = Util.trim(l140m01m.getPurposeType());
				ELF500_COLL_CHAR = Util.trim(l140m01m.getCmsType());
				ELF500_KEEP_LAWVAL = Util.trim(l140m01m.getKeepYN());
				ELF500_EST_AMT = l140m01m.getNowAMT() == null ? BigDecimal.ZERO : l140m01m.getNowAMT() ;
				ELF500_LAWVAL = l140m01m.getValueAMT();

				if (!"5".equals(ELF500_PLUS_REASON)) {
					// 非屬央行控管對象者需要選擇一個理由, 當理由不是其它時，要將取得非央行控管種類原因 備註清除
					ELF500_PLUS_MEMO = "";
				}

				// 將值清除，避免畫面上殘留的值塞到DB
				if ("2".equals(ELF500_CONTROL_CD)) {
					//ELF500_APP_AMT = BigDecimal.ZERO;
					ELF500_EST_AMT = BigDecimal.ZERO;
				} else if ("4".equals(ELF500_CONTROL_CD)) {
					if(LMSUtil.is_cls_prod_67(l140m01m)){
						
					}else{
						ELF500_APP_AMT = BigDecimal.ZERO;	
					}					
					ELF500_EST_AMT = BigDecimal.ZERO;
					ELF500_LAWVAL = BigDecimal.ZERO;
				}

				if(LMSUtil.is_cls_prod_67(l140m01m)){
					elf500_house_age = (Util.isEmpty(l140m01m.getHouse_age())?BigDecimal.ZERO:l140m01m.getHouse_age());
					elf500_loanAmt = (Util.isEmpty(l140m01m.getLoanAmt())?BigDecimal.ZERO:l140m01m.getLoanAmt());
				}
				
				ELF500_VERSION = l140m01m.getVersion() == null ? "" : l140m01m.getVersion() ;
				ELF500_HLOANLIMIT = l140m01m.getRealEstateLoanLimitReason() == null ? "" : l140m01m.getRealEstateLoanLimitReason();
				ELF500_HLOANLIMIT_2 = l140m01m.getIs3rdHignHouse() == null ? "" : l140m01m.getIs3rdHignHouse();
				ELF500_ENDDATE = this.lmsservice.getApprovalDateByLnf447nForIs3rdHighHouseRule(l140m01a.getCntrNo());
				ELF500_HLOANLIMIT_2 = this.lmsservice.proccessHloanLimitValueForIs3rdHighHouseRule(ELF500_VERSION, ELF500_HLOANLIMIT, ELF500_HLOANLIMIT_2);
				
				ELF500_ISRENEW = l140m01m.getIsRenew() == null ? ELF500_ISRENEW : l140m01m.getIsRenew();
				ELF500_ISPAY_OLD = l140m01m.getIsPayOldQuota() == null ? ELF500_ISPAY_OLD : l140m01m.getIsPayOldQuota();
				ELF500_OLD_QUOTA = null;// J-111-0534：隨此欄位於畫面上被棄用，已不需放值於DB
				ELF500_PAY_OLD_AMT = l140m01m.getPayOldAmt() == null ? ELF500_PAY_OLD_AMT : l140m01m.getPayOldAmt();
				ELF500_PAY_OLD_ITEM = l140m01m.getPayOldAmtItem() == null ? ELF500_PAY_OLD_ITEM : l140m01m.getPayOldAmtItem();
				ELF500_ISMATCH_ITEM = l140m01m.getIsMatchUnsoldHouseItem() == null ? ELF500_ISMATCH_ITEM : l140m01m.getIsMatchUnsoldHouseItem();
				ELF500_ISSALE_CASE = l140m01m.getIsSaleCase() == null ? ELF500_ISSALE_CASE : l140m01m.getIsSaleCase();
				ELF500_LSTDATE = l140m01m.getCbControlLstDate();
				ELF500_TIMEVAL = l140m01m.getTimeVal() == null ? ELF500_TIMEVAL : l140m01m.getTimeVal();
			}
			// ---------------------主借款人基本資料--------------------
			if (!Util.isEmpty(c120s01b)) {
				ELF500_POS = Util.trim(c120s01b.getJobType1())
						+ Util.trim(c120s01b.getJobType2())
						+ Util.trim(c120s01b.getJobTitle());// 職業
				ELF500_YPAY = CheckPayAmt(c120s01b.getPayAmt());// 年收入
				ELF500_OMONEY = CheckPayAmt(c120s01b.getOthAmt());// 其他收入

				ELF500_SENIORITY = c120s01b.getSeniority();// 年資
			}
			if (!Util.isEmpty(c120s01a)) {
				ELF500_EDU = Util.trim(c120s01a.getEdu());
				ELF500_MARRY = Util.trim(c120s01a.getMarry());
				ELF500_CHILD = c120s01a.getChild();

				ELF500_CMSSTATUS = c120s01a.getCmsStatus();
			}
			if (!Util.isEmpty(c120s01c)) {
				ELF500_HINCOME = CheckPayAmt(c120s01c.getYFamAmt());// 家庭收入
				ELF500_FINCOME = CheckPayAmt(c120s01c.getFincome());// 家庭年收入(上面c120s01c.YFamAmt在個金徵信畫面是夫妻年收入)

				ELF500_DRATE = c120s01c.getDRate();
				ELF500_YRATE = c120s01c.getYRate();
				ELF500_CREDIT = c120s01c.getCredit();
				ELF500_ISPERIODFUND = c120s01c.getIsPeriodFund();
				ELF500_BUSI = c120s01c.getBusi();
				ELF500_INVMBALCURR = c120s01c.getInvMBalCurr();
				ELF500_INVMBALAMT = c120s01c.getInvMBalAmt();
				ELF500_INVOBALCURR = c120s01c.getInvOBalCurr();
				ELF500_INVOBALAMT = c120s01c.getInvOBalAmt();
				ELF500_BRANCURR = c120s01c.getBranCurr();
				ELF500_BRANAMT = c120s01c.getBranAmt();

			}
			// ---------------若ELF500_TMESTAMP有值則保留上面的值-------
			ELF500 elf500temp = mislf500seervic.findByCustIdAndCntrno1(
					ELF500_CUSTID, ELF500_DUPNO, ELF500_CNTRNO);
			if (!Util.isEmpty(elf500temp)) {
				if (!Util.isEmpty(elf500temp.getElf500_tmestamp())) {
					ELF500_TMESTAMP = elf500temp.getElf500_tmestamp();
					ELF500_STATUS = elf500temp.getElf500_status();
					ELF500_ALOANTIMES = elf500temp.getElf500_aloantimes();
					// 因應非新做/增額/減額而做動審表時造成用當時餘額更新elf500,故只有上述三情形者才會更新
					/*
					 	例如：本來的房貸1000萬，後來還到 952萬時，客戶來申請 變更條件
					 	再次簽案時，新的現請額度會被改成 952萬
					 	但不應該把 當時餘額(952萬) 給上傳到 ELF500_FACTAMT
					 	－－－－－－－－－－－－－－－－－－－－－
					 	若循環的 case, 性質=變更條件,非增額、減額,是否應蓋掉？
					 	－－－－－－－－－－－－－－－－－－－－－
					 	風控處‧授信戶KRI關鍵指標出現A-LOAN建檔額度大於E-LOAN
							個人戶是抓 ELF500_FACTAMT
					 */
					boolean useExistElf500 = true;
					String[] propArr = Util.trim(l140m01a.getProPerty()).split(
							"|");
					for (String raw_prop : propArr) {
						String prop = Util.trim(raw_prop);
						if (UtilConstants.Cntrdoc.Property.新做.equals(prop) 
								|| UtilConstants.Cntrdoc.Property.增額.equals(prop)
								|| UtilConstants.Cntrdoc.Property.減額.equals(prop)) {
							useExistElf500 = false;
							break;
						}
					}
					
					if(true){
						HashSet<String> allowProp = new HashSet<String>();
						allowProp.add(UtilConstants.Cntrdoc.Property.變更條件);
						allowProp.add(UtilConstants.Cntrdoc.Property.變更條件+"|"+UtilConstants.Cntrdoc.Property.續約);
						allowProp.add(UtilConstants.Cntrdoc.Property.續約+"|"+UtilConstants.Cntrdoc.Property.變更條件);
						//===
						if(allowProp.contains(Util.trim(l140m01a.getProPerty())) 
								&& Util.equals("2", l140m01a.getReUse())
								&& ELF500_CNTFROM!=null && ELF500_CNTEND!=null
								&& LMSUtil.cmpDate(CapDate.addMonth(ELF500_CNTFROM, 12), ">=", ELF500_CNTEND)){
							useExistElf500 = false;
						}						
					}
					
					if (useExistElf500 && elf500temp.getElf500_factamt() != null) {
						ELF500_FACTAMT = elf500temp.getElf500_factamt();
					}
				}
			}
			String ELF500_TELLER = Util.getRightStr(apprId, 5);// 櫃員代號
			String ELF500_SUPVNO = Util.getRightStr(reCheckId, 5);// 主管代號

			if (Util.equals(l140m01a.getIsStartUp(), "N")
					&& Util.isNotEmpty(Util.trim(l140m01a.getItwCode()))) {
				//上傳 專案種類 05 新創產業
				ELF500_PROJ_CLASS = ClsUtil.PROJ_CLASS_05;
				ELF500_ITW_CODE = Util.trim(l140m01a.getItwCode());
			}
			if (Util.equals(l140m01a.getIsCoreBuss(), "N")) {
				String itwCodeCoreBuss = Util.trim(l140m01a.getItwCodeCoreBuss());
				itwCodeCoreBuss = "00".equals(itwCodeCoreBuss) ? "" : itwCodeCoreBuss;
				ELF500_SIXCOR_CODE = itwCodeCoreBuss;
			}
			if(true){
				boolean is_67 = false;
				boolean is_68 = false;
				boolean is_69 = false;
				boolean is_70 = false;
				for (C160S01C c160s01c : c160s01clist) {
					
					if(CrsUtil.is_67(c160s01c.getProdKind())){ //是否 以房養老 產品
						is_67 = true;
					}
					if(CrsUtil.is_68(c160s01c.getProdKind())){ //是否 行家理財中期循環 產品
						is_68 = true;
					}
					if(CrsUtil.is_69(c160s01c.getProdKind())){ //是否 勞工紓困貸款 產品
						is_69 = true;
					}
					if(CrsUtil.is_70(c160s01c.getProdKind())){ //是否 以房養老累積型 產品
						is_70 = true;
					}
				}
				if(true){
					//在 CLS1151M01FormHandler :: chk_mix_prodKind(...) 已先檢核過，不能混簽 
					if(is_67){
						ELF500_PROJ_CLASS = ProdKindEnum.以房養老_67.getCode();	
					}				
					if(is_68){
						// J-113-0008 配合行家理財-中期循環貸款-尊榮方案開辦新增專案代碼A1
						// 若專案代碼有選擇A1尊榮方案，要下A1
						if(CrsUtil.is_A1(Util.trim(l140m01a.getProjClass()))){
							ELF500_PROJ_CLASS = Util.trim(l140m01a.getProjClass());
						}else{
							ELF500_PROJ_CLASS = ProdKindEnum.行家理財中期循環_68.getCode();	
						}
					}				
					if(is_69){
						ELF500_PROJ_CLASS = ProdKindEnum.勞工紓困貸款_69.getCode();	
					}		
					if(is_70){
						ELF500_PROJ_CLASS = ProdKindEnum.以房養老累積型_70.getCode();	
					}	
				}
			}
			if(Util.isEmpty(ELF500_PROJ_CLASS)){
				String l140m01a_projClass = Util.trim(l140m01a.getProjClass());
				if(Util.isNotEmpty(l140m01a_projClass) && clsService.get_codeTypeWithOrder("L140M01A_PROJCLASS_CLS").containsKey(l140m01a_projClass)){
					ELF500_PROJ_CLASS = l140m01a_projClass;
				}				
			}
			
			String elf500_mega_empno = "";
			String elf500_agnt_no = "";
			String elf500_agnt_chain = "";
			String elf500_mega_code = "";
			String elf500_sub_unitno = "";
			String elf500_sub_empno = "";
			String elf500_sub_empnm = "";
			String elf500_intro_src = "";
			String elf500_agntfbfg = "";
			String elf500_market_note = "";
			String elf500_suspln_rson = "";
			String elf500_suspln_flag = "";
			String elf500_suspln_cntr = "";
			
			if(true){ // J-107-0091
				elf500_mega_empno = Util.trim(l140m01a.getMegaEmpNo());
				if(Util.isNotEmpty(elf500_mega_empno) && elf500_mega_empno.length()<6){
					elf500_mega_empno = Util.getRightStr("000000"+elf500_mega_empno, 6);
				}
				elf500_agnt_no = Util.trim(l140m01a.getAgntNo());
				elf500_agnt_chain = Util.trim(l140m01a.getAgntChain());
				elf500_mega_code = Util.trim(l140m01a.getMegaCode());
				elf500_sub_unitno = Util.trim(l140m01a.getSubUnitNo());
				elf500_sub_empno = Util.trim(l140m01a.getSubEmpNo());
				elf500_sub_empnm = Util.trimSizeInOS390(Util.trim(l140m01a.getSubEmpNm()), 22);
				elf500_intro_src = Util.trim(l140m01a.getIntroduceSrc());
				elf500_market_note = Util.trim(l140m01a.getMarketingNotes());
				elf500_suspln_rson = Util.trim(l140m01a.getSuspectedPackLoanReason());
				elf500_suspln_flag = Util.trim(l140m01a.getSuspectedPackLoanFlag());
				elf500_suspln_cntr = Util.trim(l140m01a.getSuspectedPackLoanCntrNo());
				
				String introductionSource = l140m01a.getIntroduceSrc();
				if("2".equals(introductionSource) || "4".equals(introductionSource)){

					boolean isGetCommission = false;
					for (L140S02A l140s02a : l140s02alist) {
						String ynGetCommission = l140s02a.getAgntFbfg();
						if (Util.isNotEmpty(ynGetCommission)) {
							isGetCommission = isGetCommission || "Y".equals(ynGetCommission) ? true : false;
						}
					}
					elf500_agntfbfg = isGetCommission ? "Y" : "N";
				}
				
				
				if(true){
					//避免copy時, 殘留在L140S02A的資料
					/*
					String raw_mega_empno = "";
					String raw_agnt_no = "";
					String raw_mega_code = "";
					for(L140S02A l140s02a : l140s02alist){
						String _importId = Util.trim(l140s02a.getImportId());
						String _megaCode = Util.trim(l140s02a.getMegaCode());
						
						if(Util.isNotEmpty(_importId)){
							if(CrsUtil.inCollection(_importId, new String[]{ "11111", "22222", "55555" })){
								raw_agnt_no = _importId;
							}else if(Util.equals(_importId, "11112")){
								raw_agnt_no = "11111";
							}else{
								//員工編號前補0至六碼
								raw_mega_empno = Util.getRightStr("000000"+_importId, 6);
								if(Util.equals(raw_mega_empno, "000000")){
									raw_mega_empno = "";
								}
							}							
						}
						if(Util.isNotEmpty(_megaCode)){ //引介子公司
							raw_mega_code = _megaCode;
						}
					}	
					//~~~~~~~~~~~~~~~					
					if(Util.isEmpty(elf500_mega_empno)){
						elf500_mega_empno = raw_mega_empno;
					}
					if(Util.isEmpty(elf500_agnt_no)){
						elf500_agnt_no = raw_agnt_no;
					}
					if(Util.isEmpty(elf500_mega_code)){
						elf500_mega_code = raw_mega_code;
					}
					*/
				}
				
			}
			
			//J-113-0323配合0403花蓮震災新增房貸信保註記
			//有房貸信保註記才判斷
			if(Util.equals("Y", Util.trim(l140m01a.getIsHouseLoanGu()))){
				for (L140S02A l140s02a : l140s02alist) {
					String prodKind = Util.trim(l140s02a.getProdKind());
					if(CrsUtil.is_prodKind_in_63_64_65(prodKind) &&
							Util.equals(Util.trim(l140s02a.getDisasType()),
									UtilConstants.L140S02ADisasType.民113_0403花蓮地震震災)){
						if(CrsUtil.is_65(prodKind)){
							ELF500_SYND_IPFD = "R";
						}else{
							ELF500_SYND_IPFD = "H";
						}
					}
				}
				if(Util.isEmpty(ELF500_SYND_IPFD)){
					//代表產品種類不是63/64/65
					ELF500_SYND_IPFD = clsService.getUseHouseLoanGu(l140m01a);
				}
				ELF500_CINSPENT = (Util
						.isEmpty(l140m01a.getHualienGutPer()) ? new BigDecimal(0)
						: l140m01a.getHualienGutPer());
			}
			
			// 將變數數塞bean
			ELF500 elf500 = new ELF500();
			elf500.setElf500_custid(ELF500_CUSTID);
			elf500.setElf500_dupno(ELF500_DUPNO);
			elf500.setElf500_cntrno(ELF500_CNTRNO);
			elf500.setElf500_grantno(ELF500_GRANTNO);
			elf500.setElf500_lncicl(ELF500_LNCICL);
			elf500.setElf500_swft(ELF500_SWFT);
			elf500.setElf500_factamt(ELF500_FACTAMT);
			elf500.setElf500_facttype(ELF500_FACTTYPE);
			elf500.setElf500_cinspent(ELF500_CINSPENT);
			elf500.setElf500_coborflg(ELF500_COBORFLG);
			elf500.setElf500_llnno(ELF500_LLNNO);
			elf500.setElf500_cntfrom(ELF500_CNTFROM);
			elf500.setElf500_cntend(ELF500_CNTEND);
			elf500.setElf500_llnmon(ELF500_LLNMON);
			elf500.setElf500_lnuseno(ELF500_LNUSENO);
			elf500.setElf500_usefmdt(ELF500_USEFMDT);
			elf500.setElf500_useendt(ELF500_USEENDT);
			elf500.setElf500_useftmn(ELF500_USEFTMN);
			elf500.setElf500_relate(ELF500_RELATE);
			elf500.setElf500_document_no(ELF500_DOCUMENT_NO);
			elf500.setElf500_grp_cntrno(ELF500_GRP_CNTRNO);
			elf500.setElf500_cmpid(ELF500_CMPID);
			elf500.setElf500_lotno(ELF500_LOTNO);
			elf500.setElf500_staffno(ELF500_STAFFNO);
			elf500.setElf500_dbr22_fact(ELF500_DBR22_FACT);
			elf500.setElf500_notice_type(ELF500_NOTICE_TYPE);
			elf500.setElf500_company_id(ELF500_COMPANY_ID);
			elf500.setElf500_company_nm(ELF500_COMPANY_NM);
			elf500.setElf500_cmbull_key(ELF500_CMBULL_KEY);
			elf500.setElf500_company_area(ELF500_COMPANY_AREA);
			elf500.setElf500_register_dt(ELF500_REGISTER_DT);
			elf500.setElf500_apply_date(ELF500_APPLY_DATE); // 2014-01-20
															// 增加上傳青創申請日期
			elf500.setElf500_capital_amt(ELF500_CAPITAL_AMT);
			elf500.setElf500_assist_flag(ELF500_ASSIST_FLAG);
			elf500.setElf500_control_cd(ELF500_CONTROL_CD);
			elf500.setElf500_plus_reason(ELF500_PLUS_REASON);
			elf500.setElf500_ltv_rate(ELF500_LTV_RATE);
			elf500.setElf500_location_cd(ELF500_LOCATION_CD);
			elf500.setElf500_jcic_mark(ELF500_JCIC_MARK);
			elf500.setElf500_app_amt(ELF500_APP_AMT);
			elf500.setElf500_cocoll_fg(ELF500_COCOLL_FG);
			elf500.setElf500_sum_factamt(ELF500_SUM_FACTAMT);
			elf500.setElf500_part_fund(ELF500_PART_FUND);
			elf500.setElf500_plus_memo(ELF500_PLUS_MEMO);
			elf500.setElf500_reg_purpose(ELF500_REG_PURPOSE);
			elf500.setElf500_eloantimes(CapDate.getCurrentTimestamp());
			elf500.setElf500_aloantimes(ELF500_ALOANTIMES);
			elf500.setElf500_teller(ELF500_TELLER);
			elf500.setElf500_supvno(ELF500_SUPVNO);
			elf500.setElf500_risk_area((Util.isEmpty(l140m01a.getRiskArea()) ? "TW"
					: l140m01a.getRiskArea()));
			elf500.setElf500_identity_no(ELF500_IDENTITY_NO);
			elf500.setElf500_pos(ELF500_POS);

			elf500.setElf500_edu(ELF500_EDU);
			elf500.setElf500_marry(ELF500_MARRY);
			elf500.setElf500_child(ELF500_CHILD);
			elf500.setElf500_seniority(ELF500_SENIORITY);

			elf500.setElf500_ypay(Util.parseInt(ELF500_YPAY));
			elf500.setElf500_hincome(Util.parseInt(ELF500_HINCOME));
			elf500.setElf500_omoney(Util.parseInt(ELF500_OMONEY));
			elf500.setElf500_plan_area(ELF500_PLAN_AREA);
			elf500.setElf500_p_usetype(ELF500_P_USETYPE);
			elf500.setElf500_p_loanuse(ELF500_P_LOANUSE);
			elf500.setElf500_coll_char(ELF500_COLL_CHAR);
			elf500.setElf500_keep_lawval(ELF500_KEEP_LAWVAL);
			elf500.setElf500_est_amt(ELF500_EST_AMT);
			elf500.setElf500_lawval(ELF500_LAWVAL);
			elf500.setElf500_restrict(ELF501_RESTRICT);
			elf500.setElf500_hp_house(ELF501_HP_HOUSE);
			elf500.setElf500_coll_char_m(ELF500_COLL_CHAR_M);
			elf500.setElf500_site3no(Util.parseInt(ELF500_SITE3NO));
			elf500.setElf500_site4no(ELF500_SITE4NO);
			elf500.setElf500_tmestamp(ELF500_TMESTAMP);
			elf500.setElf500_status(ELF500_STATUS);

			elf500.setElf500_drate(ELF500_DRATE);
			elf500.setElf500_yrate(ELF500_YRATE);
			elf500.setElf500_credit(ELF500_CREDIT);
			elf500.setElf500_isperiodfund(ELF500_ISPERIODFUND);
			elf500.setElf500_busi(ELF500_BUSI);
			elf500.setElf500_invmbalcurr(ELF500_INVMBALCURR);
			elf500.setElf500_invmbalamt(ELF500_INVMBALAMT);
			elf500.setElf500_invobalcurr(ELF500_INVOBALCURR);
			elf500.setElf500_invobalamt(ELF500_INVOBALAMT);
			elf500.setElf500_brancurr(ELF500_BRANCURR);
			elf500.setElf500_branamt(ELF500_BRANAMT);
			elf500.setElf500_cmsstatus(ELF500_CMSSTATUS);
			elf500.setElf500_ejcicqdate(ELF500_EJCICQDATE);
			elf500.setElf500_repayfund(ELF500_REPAYFUND);
			elf500.setElf500_except(LMSUtil.fetch_l140m01a_except(l140m01a));
			elf500.setElf500_unsecurefla(Util.trim(l140m01a.getUnsecureFlag()));
			elf500.setElf500_notvalid("N");
			elf500.setElf500_proj_class(ELF500_PROJ_CLASS);
			elf500.setElf500_itw_code(ELF500_ITW_CODE);
			elf500.setElf500_sixcor_code(ELF500_SIXCOR_CODE);
			elf500.setElf500_house_age(elf500_house_age);
			elf500.setElf500_loanAmt(elf500_loanAmt);
			elf500.setElf500_mega_empno(elf500_mega_empno);
			elf500.setElf500_agnt_no(elf500_agnt_no);
			elf500.setElf500_agnt_chain(elf500_agnt_chain);
			elf500.setElf500_mega_code(elf500_mega_code);
			elf500.setElf500_sub_unitno(elf500_sub_unitno);
			elf500.setElf500_sub_empno(elf500_sub_empno);
			elf500.setElf500_sub_empnm(elf500_sub_empnm);
			elf500.setElf500_welfare_cmte(ELF500_WELFARE_CMTE);
			elf500.setElf500_version(ELF500_VERSION);
			elf500.setElf500_hLoanLimit(ELF500_HLOANLIMIT);
			elf500.setElf500_intro_src(elf500_intro_src);
			elf500.setElf500_agntfbfg(elf500_agntfbfg);
			elf500.setElf500_market_note(elf500_market_note);
			elf500.setElf500_suspln_rson(elf500_suspln_rson);
			elf500.setElf500_suspln_flag(elf500_suspln_flag);
			elf500.setElf500_suspln_cntr(elf500_suspln_cntr);
			elf500.setElf500_hLoanLimit_2(ELF500_HLOANLIMIT_2);
			elf500.setElf500_endDate(ELF500_ENDDATE);
			elf500.setElf500_isRenew(ELF500_ISRENEW);
			elf500.setElf500_isPay_old(ELF500_ISPAY_OLD);
			elf500.setElf500_old_quota(ELF500_OLD_QUOTA);
			elf500.setElf500_pay_old_amt(ELF500_PAY_OLD_AMT);
			elf500.setElf500_pay_old_item(ELF500_PAY_OLD_ITEM);
			elf500.setElf500_isMatch_item(ELF500_ISMATCH_ITEM);
			elf500.setElf500_isSale_case(ELF500_ISSALE_CASE);
			elf500.setElf500_lstDate(ELF500_LSTDATE);
			elf500.setElf500_timeval(ELF500_TIMEVAL);
			elf500.setElf500_cgf_rate(ELF500_CGF_RATE);
			elf500.setElf500_fincome(Util.parseInt(ELF500_FINCOME));
			elf500.setElf500_synd_ipfd(ELF500_SYND_IPFD);
			data.add(elf500);
		}
		return data;
	}

	private List<ELF675> upELF675Case3(List<ELF675> data, C160M01A c160m01a,
			C160M01F c160m01f, C160S01D c160s01d, String subjCode3) {
		String mainId = c160s01d.getMainId();
		String cntrNo = Util.trim(c160s01d.getCntrNo());
		String custId = c160s01d.getCustId().substring(0, 10);
		String dupNo = c160s01d.getCustId().substring(10);
		C120S01Q c120s01q = findC120S01QByUniqueKey(mainId, Util.trim(StringUtils.substring(cntrNo, 0, 3)),
				custId, dupNo);
		if(c120s01q==null){
			return data;
		}
		//==========================
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		int l140s02a_seq = 1;
		String ACCT_KEY = "";
		ACCT_KEY = Util.isEmpty(ACCT_KEY) ? Util.addZeroWithValue(l140s02a_seq, 5) : ACCT_KEY;
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		
		Date jcicDate = null;
		BigDecimal BASE_A = null;
		BigDecimal BASE_B = null;
		BigDecimal BASE_S = null;
		BigDecimal BASE_SCORE = null;
		BigDecimal TOTAL_SCORE = null;
		BigDecimal INITIAL_SCORE = null;
		BigDecimal PREDICT_BAD_RATE = null;
		Integer INITIAL_RATING = null;
		Integer FINAL_RATING = null;
		String JCIC_WARNING_FLAG = null;
		BigDecimal DR = null;
		BigDecimal DR_1YR = null;

		Timestamp nowTS = CapDate.getCurrentTimestamp();
		String c_flag = "";
		if (true) {
			if (!Util.isEmpty(c120s01q.getVarVer())) {
				String VarVer[] = c120s01q.getVarVer().split("\\.");
				MOWVER1 = Util.parseInt(VarVer[0]);
				MOWVER2 = Util.parseInt(VarVer[1]);
			}
			jcicDate = c120s01q.getJcicQDate();
			c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q);

			BASE_A = c120s01q.getVarA();
			BASE_B = c120s01q.getVarB();
			BASE_S = c120s01q.getVarC();
			BASE_SCORE = c120s01q.getScrNum12();
			TOTAL_SCORE = c120s01q.getScrNum11();
			INITIAL_SCORE = c120s01q.getScrNum13();
			PREDICT_BAD_RATE = c120s01q.getPd();
			INITIAL_RATING = Util.parseInt(c120s01q.getGrade1());
			FINAL_RATING = Util.parseInt(c120s01q.getGrade3());
			JCIC_WARNING_FLAG = "N";
			if (Util.equals(Util.nullToSpace(c120s01q.getChkItem1()), "Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem2()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem3()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem4()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem5()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem6()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem7()),
							"Y")
					|| Util.equals(Util.nullToSpace(c120s01q.getChkItem8()),
							"Y")) {
				JCIC_WARNING_FLAG = "Y";
			}

//			String subjCode3 = Util.trim(prodService.getSubject8to3(c160m01f.getSubjCode()));
			boolean isShortPeriodCase = subjCode3.startsWith("1")
					|| subjCode3.startsWith("2");

			DR = isShortPeriodCase ? c120s01q.getDr_2YR() : c120s01q
					.getDr_3YR();
			DR_1YR = isShortPeriodCase ? c120s01q.getDr_1YR_S() : c120s01q
					.getDr_1YR_L();
		}

		String CUST_KEY = c120s01q.getCustId();
		String FINAL_RATING_FLAG = "Y"; //在整批貸款時
		
		ELF675 elf675 = new ELF675();
		elf675.setElf675_contract(cntrNo);
		elf675.setElf675_brn(c120s01q.getOwnBrId());
		elf675.setElf675_noteid(Util.trim(c160s01d.getOid())); //ref upDW_RKSCORE(...)			
		elf675.setElf675_custid(c120s01q.getCustId());
		elf675.setElf675_dupno(c120s01q.getDupNo());
		elf675.setElf675_mowtype("N");
		elf675.setElf675_mowver1(MOWVER1);
		elf675.setElf675_mowver2(MOWVER2);
		elf675.setElf675_jcic_date(jcicDate);
		elf675.setElf675_loan_no(ACCT_KEY);
		elf675.setElf675_cust_key(CUST_KEY);
		elf675.setElf675_lngeflag("M");
		elf675.setElf675_base_a(BASE_A);
		elf675.setElf675_base_b(BASE_B);
		elf675.setElf675_base_s(BASE_S);
		elf675.setElf675_base_score(BASE_SCORE);
		elf675.setElf675_total_score(TOTAL_SCORE);
		elf675.setElf675_init_score(INITIAL_SCORE);
		elf675.setElf675_predict_rat(PREDICT_BAD_RATE);
		elf675.setElf675_init_rating(INITIAL_RATING);
		elf675.setElf675_adj_rating(INITIAL_RATING - FINAL_RATING);
		elf675.setElf675_final_rate(FINAL_RATING);
		elf675.setElf675_jcic_warnfg(JCIC_WARNING_FLAG);
		elf675.setElf675_final_ratfg(FINAL_RATING_FLAG);
		elf675.setElf675_del_reason("");
		elf675.setElf675_reject_txt("");
		elf675.setElf675_docstatus("3"); //參考 LMSServiceImpl :: DwDOCSTATUS(...)
		elf675.setElf675_dr(DR);
		elf675.setElf675_dr_1yr(DR_1YR);
		elf675.setElf675_data_src_dt(nowTS);
		elf675.setElf675_updater(user.getUserId());
		elf675.setElf675_tmestamp(nowTS);
		elf675.setElf675_c_flag(c_flag);
		elf675.setElf675_coll_house("N");
		
		data.add(elf675);
		//==========================
		return data;
	}
	
	private Date getMinDate(Date d1, Date d2){
		if(d1==null && d2==null){
			return null;
		}else if(d1!=null && d2==null){
			return d1;
		}else if(d1==null && d2!=null){
			return d2;
		}else{
			return LMSUtil.cmpDate(d1, "<", d2)?d1:d2;
		}
	}
	private Date getMaxDate(Date d1, Date d2){
		if(d1==null && d2==null){
			return null;
		}else if(d1!=null && d2==null){
			return d1;
		}else if(d1==null && d2!=null){
			return d2;
		}else{
			return LMSUtil.cmpDate(d1, ">", d2)?d1:d2;
		}		
	}
	private List<ELF500> upELF500Case3(List<ELF500> data, C160M01A c160m01a,
			C160M01F c160m01f, C160S01D c160s01d) {
		String ELF500_CUSTID = Util.getLeftStr(Util.trim(c160s01d.getCustId()),
				10);// 借款人身分證字號
		String ELF500_DUPNO = Util.getRightStr(Util.trim(c160s01d.getCustId()),
				1);// 重覆碼
		String ELF500_CNTRNO = Util.trim(c160s01d.getCntrNo());// 額度序號
		String ELF500_SWFT = "TWD";// 幣別
		String ELF500_FACTTYPE = "51";// 控管種類
		BigDecimal ELF500_FACTAMT = c160s01d.getLoanTotAmt();// 申請金額
		Date ELF500_USEFMDT = c160s01d.getUseFromDate();// 動用期限－起始日期
		Date ELF500_USEENDT = c160s01d.getUseEndDate(); // 動用期限－終止日期
		Date ELF500_CNTFROM = c160s01d.getLnFromDate();// 授信期間 ( 起 )
		Date ELF500_CNTEND = c160s01d.getLnEndDate();// 授信期間 ( 迄 )
		String ELF500_GRANTNO = "9";// 授權等級
		String ELF500_LNCICL = Util.trim(c160m01f.getReUse());// 循環
		String ELF500_COBORFLG = Util.trim(c160m01f.getCommsCust());// 共同借款人註記
		String ELF500_RISK_AREA = "TW";// 風險國別
		String ELF500_DOCUMENT_NO = Util.getRightStr(
				Util.getLeftStr(ELF500_CNTRNO, 7), 3)
				+ Util.getLeftStr(ELF500_CNTRNO, 3)
				+ "CLS"
				+ Util.getRightStr(ELF500_CNTRNO, 4);// 契約核准編號,當 caseType==3, 由額度序號而來(非簽報書)
		String ELF500_GRP_CNTRNO = Util.trim(c160m01a.getChildrenSeq());// 團貸總戶之額度序號
		if (Util.equals(ELF500_GRP_CNTRNO, "")) {
			ELF500_GRP_CNTRNO = Util.trim(c160m01a.getApprovedNo());
		}
		String ELF500_CMPID = getcmpId(Util.trim(c160m01f.getCustId()),
				Util.trim(c160m01f.getDupNo()));// 服務公司/建設公司統編
		String ELF500_LOTNO = Util.trim(c160m01a.getPackNo());// 批號
		String ELF500_STAFFNO = Util.trim(c160s01d.getStaffNo()); // 職工編號
		Integer ELF500_YPAY = c160s01d.getAnnuity();// 年薪
		Integer ELF500_OMONEY = c160s01d.getOthincome();// 其他所得
		String ELF500_TELLER = Util.addZeroWithValue(
				Util.trim(c160m01f.getCName()), 5);// 櫃員代號
		Date Elf500_ENDDATE = //簽報書核准日 (用中鋼消貸簽案的額度序號取得)
			this.lmsservice.getApprovalDateByLnf447nForIs3rdHighHouseRule(Util.trim(c160m01a.getApprovedNo()));
		if(Util.isEmpty(Elf500_ENDDATE)){//如果沒有值就給當天(該欄位不允許null)
			Elf500_ENDDATE = new Date();
		}
		Date ELF500_TMESTAMP = new Date();
		ELF500 elf500temp = mislf500seervic.findByCustIdAndCntrno1(
				ELF500_CUSTID, ELF500_DUPNO, ELF500_CNTRNO);
		if (!Util.isEmpty(elf500temp)) {
			if (!Util.isEmpty(elf500temp.getElf500_tmestamp())) {
				ELF500_TMESTAMP = elf500temp.getElf500_tmestamp();
			}
		}
		ELF500 elf500 = new ELF500();
		elf500.setElf500_custid(ELF500_CUSTID);
		elf500.setElf500_dupno(ELF500_DUPNO);
		elf500.setElf500_cntrno(ELF500_CNTRNO);
		elf500.setElf500_swft(ELF500_SWFT);
		elf500.setElf500_facttype(ELF500_FACTTYPE);
		elf500.setElf500_factamt(ELF500_FACTAMT);
		elf500.setElf500_usefmdt(ELF500_USEFMDT);
		elf500.setElf500_useendt(ELF500_USEENDT);
		elf500.setElf500_cntfrom(ELF500_CNTFROM);
		elf500.setElf500_cntend(ELF500_CNTEND);
		elf500.setElf500_grantno(ELF500_GRANTNO);
		elf500.setElf500_lncicl(ELF500_LNCICL);
		elf500.setElf500_coborflg(ELF500_COBORFLG);
		elf500.setElf500_risk_area(ELF500_RISK_AREA);
		elf500.setElf500_identity_no("");
		elf500.setElf500_dbr22_fact(ELF500_FACTAMT); // DBR22
		elf500.setElf500_document_no(ELF500_DOCUMENT_NO);
		elf500.setElf500_grp_cntrno(ELF500_GRP_CNTRNO);
		elf500.setElf500_cmpid(ELF500_CMPID);
		elf500.setElf500_lotno(ELF500_LOTNO);
		elf500.setElf500_staffno(ELF500_STAFFNO); // 職工編號
		elf500.setElf500_ypay(ELF500_YPAY);
		elf500.setElf500_omoney(ELF500_OMONEY);
		elf500.setElf500_eloantimes(CapDate.getCurrentTimestamp());// ELOAN寫入時間
																	// ELF500_ELOANTIMES
		elf500.setElf500_teller(Util.getRightStr(ELF500_TELLER, 5));
		elf500.setElf500_tmestamp(ELF500_TMESTAMP);
		elf500.setElf500_except("C");
		elf500.setElf500_notvalid("N");
		// J-112-0390 中鋼消貸動審無法覆核  經確認，要把這三個欄位給值
		elf500.setElf500_version(UtilConstants.L140m01mVersion.VERSION_LASTEST);
		elf500.setElf500_hLoanLimit("4");
		elf500.setElf500_endDate(Elf500_ENDDATE);
		
		data.add(elf500);
		return data;
	}

	@Override
	public <T> void upMisToServer(MISRows<T> misRows, String TableType) {
		if (!Util.isEmpty(misRows.getKeyValues())) {
			int DelCount = misdbBaseService.delete(
					misRows.getKeyMsgFmtParam(TableType),
					misRows.getKeyValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Delete:"
					+ DelCount);
			misdbBaseService.insert(misRows.getMsgFmtParam(TableType),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		}
	}

	private <T> void delMisToServerToELLNGTEE(String outkey) {
		if (!Util.isEmpty(Util.trim(outkey))) {
			misdbBaseService.delete(
					new Object[] { "MIS.ELLNGTEE", "CNTRNO =? " },
					new String[] { outkey });
		}
	}

	private <T> void delMisToServerToQUOTSUB(String outkey) {
		if (!Util.isEmpty(Util.trim(outkey))) {
			misdbBaseService.delete(
					new Object[] { "MIS.QUOTSUB", "CNTRNO =? " },
					new String[] { outkey });
		}
	}

	private <T> void delMisToServerToELF502(String ELF502_CNTRNO,
			String ELF502_SEQ_NO) {
		if (!Util.isEmpty(Util.trim(ELF502_CNTRNO))
				&& !Util.isEmpty(Util.trim(ELF502_SEQ_NO))) {
			misdbBaseService.delete(new Object[] { "MIS.ELF502",
					"ELF502_CNTRNO =? AND ELF502_SEQ_NO=?" }, new String[] {
					ELF502_CNTRNO, ELF502_SEQ_NO });
		}
	}

	private <T> void delMisToServerToELF504(List<ELF504> elf504_list) {
		boolean contain00051 = false;
		for(ELF504 elf504 : elf504_list){
			if(isChgProdSubj(elf504.getElf504_loan_no())){
				contain00051 = true;
				break;
			}
		}
		if(contain00051){
			Set<String> procSet = new HashSet<String>();
			for(ELF504 elf504 : elf504_list){
				String key = Util.trim(elf504.getElf504_br_no())+"-"
					+ Util.trim(elf504.getElf504_cntrno());
				procSet.add(key);
			}
			
			for(String key: procSet){
				String[] arr = StringUtils.split(key, "-");
				String br_no = arr[0];
				String contract = arr[1];
				//=====			
				misdbBaseService.delete(new Object[] { "MIS.ELF504",
						"ELF504_BR_NO=? AND ELF504_CNTRNO=? " }, new String[] {
						br_no, contract });
			}
			
		}else{
			Set<String> procSet = new HashSet<String>();
			for(ELF504 elf504 : elf504_list){
				String key = Util.trim(elf504.getElf504_br_no())+"-"
					+ Util.trim(elf504.getElf504_cntrno())+"-"
					+ Util.trim(elf504.getElf504_loan_no())
					;
				procSet.add(key);
			}
			
			for(String key: procSet){
				String[] arr = StringUtils.split(key, "-");
				String br_no = arr[0];
				String contract = arr[1];
				String loan_no = arr[2];
				//=====			
				misdbBaseService.delete(new Object[] { "MIS.ELF504",
						"ELF504_BR_NO=? AND ELF504_CNTRNO=? AND ELF504_LOAN_NO=?" }, new String[] {
						br_no, contract, loan_no });
			}
		}
	}
	
	@Override
	public <T> void updateMisToServer(MISRows<T> misRows, String TableType) {
		// 上傳更新MIS
		misdbBaseService.update(misRows.getUpdateMsgFmtParam(TableType),
				misRows.getUpdateTypes(), misRows.getUpDateValuesLst());
	}

	public String getSTUREL(String rKindD) {
		if (Util.equals(rKindD, "X0")) {
			rKindD = "0";
		} else if (Util.equals(rKindD, "XB")) {
			rKindD = "1";
		} else if (Util.equals(rKindD, "XD") || Util.equals(rKindD, "XJ")) {
			rKindD = "2";
		} else if (Util.equals(rKindD, "XI")) {
			rKindD = "3";
		} else if (Util.equals(rKindD, "XA")) {
			rKindD = "4";
		} else if (Util.equals(rKindD, "XK")) {
			rKindD = "5";
		} else if (Util.equals(rKindD, "XL")) {
			rKindD = "6";
		} else if (Util.equals(rKindD, "XC") || Util.equals(rKindD, "XE")
				|| Util.equals(rKindD, "XF") || Util.equals(rKindD, "XG")
				|| Util.equals(rKindD, "XH")) {
			rKindD = "9";
		} else {
			rKindD = "";
		}
		return rKindD;
	}

	public String getELF461Status(String docStatus) {
		if (Util.equals(docStatus, CLSDocStatusEnum.編製中)) {
			docStatus = "1";
		} else if (Util.equals(docStatus, CLSDocStatusEnum.待覆核)) {
			docStatus = "2";
		} else if (Util.equals(docStatus, CLSDocStatusEnum.已核准)) {
			docStatus = "3";
		} else {
			docStatus = "";
		}
		return docStatus;
	}

	public String getLNF164IntrtType(String IntrtType) {
		switch (Util.parseInt(IntrtType)) {
		case 1:
			IntrtType = "A";
			break;
		case 2:
			IntrtType = "B";
			break;
		case 3:
			IntrtType = "C";
			break;
		case 4:
			IntrtType = "D";
			break;
		case 6:
			IntrtType = "7";
			break;
		}
		return IntrtType;
	}

	private String chgQuotsubChgFlag(String pro) {
		switch (Util.parseInt(pro)) {
		case 1:// 新做|1
			pro = "1";
			break;
		case 8:// 取消|8
			pro = "2";
			break;
		case 5:// 增額|5
			pro = "3";
			break;
		case 6:// 減額|6
			pro = "4";
			break;
		default:// 其他
			pro = "5";
			break;
		}
		return pro;
	}

	@Override
	public List<? extends GenericBean> findModelByMainIdAndRefMainId(
			Class<?> clazz, String mainId, String refMainId, Integer seqNo) {
		if (clazz == C160S01A.class) {
			return c160s01aDao.findByMainIdRefMainId(mainId, refMainId);
		} else if (clazz == C160S01B.class) {
			return c160s01bDao.findByMainIdRefMainId(mainId, refMainId);
		} else if (clazz == C160S01C.class) {
			return c160s01cDao.findByMainIdRefMainid(mainId, refMainId);
		} else if (clazz == C160S01E.class) {
			c160s01eDao.findByMainIdRefMainIdSeq(mainId, refMainId, seqNo);
		} else if (clazz == C160S01F.class) {
			c160s01fDao.findByMainIdSeqRefMainid(mainId, seqNo, refMainId);
		}

		return null;
	}

	private String getcmpId(String custId, String DupNo) {
		return Util.trim(custId)
				+ (Util.equals(Util.trim(custId).length(), 8) ? "  " : "")
				+ Util.trim(DupNo);
	}

	private String getUploadCaseNo(L120M01A l120m01a, String CntrNo) {
		String CaseNo = "";
		if (!Util.isEmpty(l120m01a.getCaseSeq())) {
			CaseNo = Util
					.trimSizeInOS390(LMSUtil.getUploadCaseNo(l120m01a), 40);
		} else {
			CaseNo = Util.getRightStr(Util.getLeftStr(CntrNo, 7), 3)
					+ Util.getLeftStr(CntrNo, 3) + "CLS"
					+ Util.getRightStr(CntrNo, 4);
		}
		return CaseNo;
	}

	private BigDecimal CheckPayAmt(BigDecimal PayAmt) {
		if (!Util.isEmpty(PayAmt)) {
			return LMSUtil.getUploadYFamAmt(PayAmt);
		} else {
			PayAmt = new BigDecimal(0);
		}
		return PayAmt;
	}

	@Override
	public List<L140M01R> getL140M01RbyMainId(String mainId) {
		return l140m01rDao.findByMainId(mainId);
	}

	@Override
	public void reNewL140M01R(List<L140M01R> oldList, List<L140M01R> newList) {
		if (oldList != null) {
			delete(oldList);
		}

		if (newList != null) {
			save(newList);
		}
	}

	@Override
	public void reNewL140M01R(String[] mainIds, String mainId)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 先只刪除動審表中由簽報書中引入之各項費用值
		List<L140M01R> l140m01rn = getL140M01RbyMainId(mainId);
		String feeSrc = "";
		if (l140m01rn != null) {
			for (L140M01R l140m01rnew : l140m01rn) {
				feeSrc = l140m01rnew.getFeeSrc();
				if ("1".equals(feeSrc)) {
					delete(l140m01rnew);
				}
			}
		}

		List<L140M01R> newL140m01rs = new ArrayList<L140M01R>();
		for (String omainId : mainIds) {
			List<L140M01R> l140m01rs = getL140M01RbyMainId(omainId);
			for (L140M01R l140m01rold : l140m01rs) {
				feeSrc = l140m01rold.getFeeSrc();
				if ("0".equals(feeSrc)) {
					L140M01R l140m01r = new L140M01R();
					DataParse.copy(l140m01rold, l140m01r);

					l140m01r.setCreator(user.getUserId());
					l140m01r.setCreateTime(CapDate.getCurrentTimestamp());
					l140m01r.setMainId(mainId);
					l140m01r.setFeeSrc("1");

					newL140m01rs.add(l140m01r);
				}
				// result.add(l140m01r.toJSONString(columns, reformat)));
			}
		}
		save(newL140m01rs);
	}

	/**
	 * 動審表呈案前設定各項費用資料之SEQ
	 */
	@Override
	public void setL140M01RSeq(String sourceMainId) {
		HashMap<String, Integer> cntrnoMap = new HashMap<String, Integer>();
		List<L140M01R> l140m01rs = l140m01rDao.findByMainId(sourceMainId);

		int maxSeq = 0;
		Integer secNo = 0;
		String tCaseNo = "";

		for (L140M01R l140m01r : l140m01rs) {
			tCaseNo = Util.trim(l140m01r.getCaseNo());
			if (cntrnoMap.containsKey(tCaseNo)) {
				maxSeq = cntrnoMap.get(tCaseNo);
			} else {
				cntrnoMap.put(tCaseNo, maxSeq);
			}

			secNo = l140m01r.getFeeSeq();
			if (Util.isNotEmpty(secNo) && secNo != 0) {
				if (secNo > maxSeq) {
					maxSeq = secNo;
					cntrnoMap.put(tCaseNo, maxSeq);
				}
			}
		}

		for (L140M01R l140m01r : l140m01rs) {
			secNo = l140m01r.getFeeSeq();
			if (Util.isEmpty(secNo) || secNo == 0) {
				tCaseNo = Util.trim(l140m01r.getCaseNo());
				l140m01r.setFeeSeq(cntrnoMap.get(tCaseNo) + 1);
				cntrnoMap.put(tCaseNo, cntrnoMap.get(tCaseNo) + 1);
			}
		}
		l140m01rDao.save(l140m01rs);
	}

	@Override
	public String checkMisToUpdateL140M01R(L140M01R l140m01r) {

		String tELF509_CASE_NO = "";// 簽案案件編號
		String tELF509_FEE_ITEM = "";// 費用代碼
		Integer tELF509_SEQ_NO = 0;// 案件序號

		tELF509_CASE_NO = StrUtils.concat(l140m01r.getCaseYear() - 1911,
				l140m01r.getCaseBrId(), "CLS",
				Util.addZeroWithValue(l140m01r.getCaseSeq(), 3));
		tELF509_FEE_ITEM = l140m01r.getFeeNo();
		tELF509_SEQ_NO = l140m01r.getFeeSeq();

		ELF509 elf509o = mislf509seervic.findByUniqueKey1(tELF509_CASE_NO,
				tELF509_FEE_ITEM, tELF509_SEQ_NO);

		if (!Util.isEmpty(elf509o)) {
			if (elf509o.getElf509_fee_amt_re().compareTo(BigDecimal.ZERO) > 0) {
				return "N";
			}
		}
		return "Y";

	}

	private List<ELF509> upELF509(List<ELF509> data, C160M01A c160m01a,
			String apprId, String reCheckId) {
		if (!Util.isEmpty(c160m01a)) {
			List<L140M01R> l140m01rs = getL140M01RbyMainId(c160m01a.getMainId());
			for (L140M01R l140m01r : l140m01rs) {
				L120M01A l120m01a = null;

				String tELF509_CASE_NO = "";// 簽案案件編號
				String tELF509_FEE_ITEM = "";// 費用代碼
				Integer tELF509_SEQ_NO = 0;// 案件序號
				String tELF509_FEE_SWFT = "";// 費用幣別
				BigDecimal tELF509_FEE_AMT = BigDecimal.ZERO;// 簽案之費用金額
				BigDecimal tELF509_FEE_AMT_RE = BigDecimal.ZERO;// 已收費用金額
				String tELF509_STATUS = "";// 狀態碼
				String tELF509_CUSTID = "";// 借款人身分證統一編號
				String tELF509_DUPNO = "";// 重複序號
				String tELF509_CNTRNO = "";// 本件額度序號
				String tELF509_LOAN_NO = "";// 授信帳號
				String tELF509_TELLER = "";// 櫃員
				String tELF509_SUPVNO = "";// 主管
				// String tELF509_ELOAN_TIME = "";// E-LOAN寫入時間
				Date tELF509_ALOAN_TIME = CapDate.parseDate("0001-01-01");// A-LOAN寫入時間

				if (!"".equals(Util.trim(l140m01r.getCaseYear()))
						&& !"".equals(Util.trim(l140m01r.getCaseBrId()))
						&& !"".equals(Util.trim(l140m01r.getCaseSeq()))) {
					l120m01a = l120m01aDao.findBycaseYearBridSeq(
							l140m01r.getCaseYear(), l140m01r.getCaseBrId(),
							l140m01r.getCaseSeq());
				}

				if (l120m01a == null) {
					l120m01a = l120m01aDao
							.findByMainId(c160m01a.getSrcMainId());
				}

				tELF509_CASE_NO = StrUtils.concat(
						l140m01r.getCaseYear() - 1911, l140m01r.getCaseBrId(),
						"CLS", Util.addZeroWithValue(l140m01r.getCaseSeq(), 5));
				tELF509_FEE_ITEM = l140m01r.getFeeNo();
				tELF509_SEQ_NO = l140m01r.getFeeSeq();

				ELF509 elf509o = mislf509seervic.findByUniqueKey1(
						tELF509_CASE_NO, tELF509_FEE_ITEM, tELF509_SEQ_NO);

				if (!Util.isEmpty(elf509o)) {
					if (elf509o.getElf509_fee_amt_re().compareTo(
							BigDecimal.ZERO) > 0) {
						continue;
					}
					tELF509_ALOAN_TIME = elf509o.getElf509_aloan_time() == null ? CapDate
							.parseDate("0001-01-01") : elf509o
							.getElf509_aloan_time();
				}

				tELF509_FEE_SWFT = l140m01r.getFeeSwft();
				tELF509_FEE_AMT = l140m01r.getFeeAmt();
				tELF509_FEE_AMT_RE = BigDecimal.ZERO;
				tELF509_STATUS = "";
				tELF509_CUSTID = Util.trim(l120m01a.getCustId());
				tELF509_DUPNO = Util.trim(l120m01a.getDupNo());
				tELF509_CNTRNO = "";
				tELF509_LOAN_NO = "";
				tELF509_TELLER = apprId;
				tELF509_SUPVNO = reCheckId;
				// tELF509_ELOAN_TIME = "";
				// tELF509_ALOAN_TIME = "";

				ELF509 elf509 = new ELF509();
				elf509.setElf509_case_no(tELF509_CASE_NO);// 簽案案件編號
				elf509.setElf509_fee_item(tELF509_FEE_ITEM);// 費用代碼
				elf509.setElf509_seq_no(tELF509_SEQ_NO);// 案件序號
				elf509.setElf509_fee_swft(tELF509_FEE_SWFT);// 費用幣別
				elf509.setElf509_fee_amt(tELF509_FEE_AMT);// 簽案之費用金額
				elf509.setElf509_fee_amt_re(tELF509_FEE_AMT_RE);// 已收費用金額
				elf509.setElf509_status(tELF509_STATUS);// 狀態碼
				elf509.setElf509_custid(tELF509_CUSTID);// 借款人身分證統一編號
				elf509.setElf509_dupno(tELF509_DUPNO);// 重複序號
				elf509.setElf509_cntrno(tELF509_CNTRNO);// 本件額度序號
				elf509.setElf509_loan_no(tELF509_LOAN_NO);// 授信帳號
				elf509.setElf509_teller(tELF509_TELLER);// 櫃員
				elf509.setElf509_supvno(tELF509_SUPVNO);// 主管
				elf509.setElf509_eloan_time(CapDate.getCurrentTimestamp());// E-LOAN寫入時間
				elf509.setElf509_aloan_time(tELF509_ALOAN_TIME);// A-LOAN寫入時間

				data.add(elf509);
			}
		}
		return data;
	}

	private List<ELF508> upELF508(List<ELF508> data, C160M01B c160m01b) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (!Util.isEmpty(c160m01b)) {
			
			L140S02A l140s02a = null;
			for(L140S02A l140s02a_row : l140s02aDao.findByMainId(c160m01b.getRefmainId())){
				String prodKind = l140s02a_row.getProdKind();
				
				if (CrsUtil.is_prodKind_in_63_64_65(prodKind)) {
					l140s02a = l140s02a_row;
				}
			}
			
			if(l140s02a!=null){
				L140S02L l140s02l = l140s02lDao.findByUniqueKey(l140s02a.getMainId(), l140s02a.getSeq());
				if(l140s02l!=null){
					String elf508_brno =  user.getUnitNo();	
					String elf508_cust_id = LMSUtil.getCustKey_len10custId(c160m01b.getCustId(), c160m01b.getDupNo());
					String elf508_disas_type = l140s02a.getDisasType();	//Key    01:0206震災
					String elf508_hold_no = l140s02l.getHold_no();			//Key
					String elf508_owner_id = l140s02l.getOwner_id();		//Key
					String elf508_cntrno = l140s02a.getCntrNo();
					String elf508_loan_type = "";
					
					String prodKind = l140s02a.getProdKind();
					if (Util.equals(ProdKindEnum.天然及重大災害受災戶購屋_63.getCode(), prodKind)){
						elf508_loan_type = "1";
					}else if (Util.equals(ProdKindEnum.天然及重大災害受災戶重建_64.getCode(), prodKind)){
						elf508_loan_type = "2";
					}else if (Util.equals(ProdKindEnum.天然及重大災害受災戶修繕_65.getCode(), prodKind)){
						elf508_loan_type = "3";
					}
							
					Date elf508_app_date = CapDate.parseDate(CapDate.formatDate(l140s02l.getApp_date(), UtilConstants.DateFormat.YYYY_MM_DD));
					BigDecimal elf508_app_amt = l140s02a.getLoanAmt();
					String elf508_house_adr = l140s02l.getHouse_adr();
					String elf508_owner_nm = l140s02l.getOwner_nm();
					String elf508_ownsp_id = l140s02l.getOwnsp_id();
					String elf508_ownsp_nm = l140s02l.getOwnsp_nm();
					String elf508_spouse_id = l140s02l.getSpouse_id();
					String elf508_spouse_nm = l140s02l.getSpouse_nm();
					String elf508_coll_ln = l140s02l.getColl_ln();
					String elf508_coll_bn = l140s02l.getColl_bn();
					String elf508_coll_addr = l140s02l.getColl_addr();
					String elf508_set_hold = l140s02l.getSet_hold();
					
					ELF508 elf508 = new ELF508();
					elf508.setElf508_brno(elf508_brno);
					elf508.setElf508_cust_id(elf508_cust_id);
					elf508.setElf508_disas_type(elf508_disas_type);
					elf508.setElf508_hold_no(elf508_hold_no);
					elf508.setElf508_owner_id(elf508_owner_id);
					elf508.setElf508_cntrno(elf508_cntrno);
					elf508.setElf508_loan_type(elf508_loan_type);
					elf508.setElf508_app_date(elf508_app_date);
					elf508.setElf508_app_amt(elf508_app_amt);
					elf508.setElf508_house_adr(elf508_house_adr);
					elf508.setElf508_owner_nm(elf508_owner_nm);
					elf508.setElf508_ownsp_id(elf508_ownsp_id);
					elf508.setElf508_ownsp_nm(elf508_ownsp_nm);
					elf508.setElf508_spouse_id(elf508_spouse_id);
					elf508.setElf508_spouse_nm(elf508_spouse_nm);
					elf508.setElf508_coll_ln(elf508_coll_ln);
					elf508.setElf508_coll_bn(elf508_coll_bn);
					elf508.setElf508_coll_addr(elf508_coll_addr);
					elf508.setElf508_set_hold(elf508_set_hold);
					elf508.setElf508_eloan_date(CapDate.getCurrentTimestamp());
					
					data.add(elf508);
				}	
			}
		}
		return data;
	}
	
	@Override
	public void reNewC801M01A(String[] cntrNos, String mainId)
			throws CapException {

		for (String CNTRNO : cntrNos) {
			String COMMA = "、";
			String tCoName = "";
			String tGuName = "";
			String tType = "";

			C160M01B c160m01b = c160m01bDao.findByMainidCntrno(mainId, CNTRNO);
			List<C160S01B> c160s01bs = c160s01bDao.findByMainIdCntrno(mainId,
					CNTRNO);

			for (C160S01B c160s01b : c160s01bs) {
				tType = c160s01b.getRType();
				if ("C".equals(tType)) {
					tCoName = tCoName + Util.trim(c160s01b.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("G".equals(tType)) {
					tGuName = tGuName + Util.trim(c160s01b.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("N".equals(tType)) {
					tGuName = tGuName + Util.trim(c160s01b.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("S".equals(tType)) {
					tGuName = tGuName + Util.trim(c160s01b.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("D".equals(tType)) {
					tGuName = tGuName + Util.trim(c160s01b.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("E".equals(tType)) {
					tGuName = tGuName + Util.trim(c160s01b.getRName()) + "("
							+ tType + ")" + COMMA;
				} else if ("L".equals(tType)) {
					tGuName = tGuName + Util.trim(c160s01b.getRName()) + "("
							+ tType + ")" + COMMA;
				}
			}
			if (!CapString.isEmpty(tCoName)) {
				tCoName = tCoName.substring(0,
						tCoName.length() - COMMA.length());
			}
			if (!CapString.isEmpty(tGuName)) {
				tGuName = tGuName.substring(0,
						tGuName.length() - COMMA.length());
			}

			C801M01A c801m01a = cls8011Service.findC801M01A_cntrNo(CNTRNO);
			String custId = c160m01b.getCustId();
			String dupNo = c160m01b.getDupNo();
			String custName = c160m01b.getCustName();
			List<C801M01B> newC801m01bs = null;

			if (c801m01a == null) {
				c801m01a = cls8011Service.initModel(custId, dupNo, custName,
						CNTRNO);
				newC801m01bs = cls8011Service.genLatest(c801m01a.getMainId());
			} else {
				// cls8011Service.deleteC801M01B(c801m01a.getMainId());
				List<C801M01B> orgC801m01bs = cls8011Service
						.findC801M01B_mainId(c801m01a.getMainId());
				if (orgC801m01bs == null || orgC801m01bs.isEmpty()) {
					newC801m01bs = cls8011Service.genLatest(c801m01a
							.getMainId());
				}
			}
			c801m01a.setCoCustName(tCoName);
			c801m01a.setGuarantor(tGuName);
			c801m01a.setDocType("2"); // 個金
			// ---
			cls8011Service.setStatusEdit(c801m01a);
			// ---
			if (newC801m01bs != null && !newC801m01bs.isEmpty()) {
				cls8011Service.saveData(c801m01a, newC801m01bs);
			} else {
				cls8011Service.save(c801m01a);
			}

		}

	}

	private void uploadELF517(C160M01A c160m01a, L140M01A l140m01a,
			L120M01A l120m01aNow, String cntrNo) throws CapException {

		boolean hasElf517 = false;
		Map<String, Object> cntrNoData = misElf517Service.findByCntrNo(cntrNo);

		if (cntrNoData != null && !cntrNoData.isEmpty()) {
			BigDecimal count = LMSUtil.toBigDecimal(cntrNoData.get("COUNT"));
			if (count.compareTo(BigDecimal.ZERO) > 0) {
				hasElf517 = true;
			}
		}

		L140M01M l140m01m = lmsservice.findModelByMainId(L140M01M.class,
				l140m01a.getMainId());
		if (l140m01m == null) {
			return;
		}

		String documentNo = getUploadCaseNo(l120m01aNow, cntrNo);
		this.lmsservice.insertOrUpdateELF517ForUnsoldHouseFinanceingData(documentNo, l140m01m, hasElf517, cntrNo);
	}
	
	public C120S01Q findC120S01QByUniqueKey(String mainId, String ownBrId,
			String custId, String dupNo) {
		return c120s01qDao.findByUniqueKey(mainId, ownBrId, custId, dupNo);
	}	
	
	/**
	 * 上傳DW
	 * 
	 */
	@Override
	public <T> void upDwToServer(MISRows<T> misRows, String TableType) {
		if (!Util.isEmpty(misRows.getKeyValues())) {
			int DelCount = dwdbBaseService.delete(
					misRows.getKeyMsgFmtParam(TableType),
					misRows.getKeyValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Delete:"
					+ DelCount);
			dwdbBaseService.insert(misRows.getMsgFmtParam(TableType),
					misRows.getTypes(), misRows.getValues());
			logger.info("{}=======>{}", misRows.getTableNm(), "Insert");
		}
	}
	
	@Override
	public void uploadDW(C160M01A c160m01a, C160S01D c160s01d) {
		MISRows<DW_RKSCORE> misRowsDW_RKSCORE = new MISRows<DW_RKSCORE>(
				DW_RKSCORE.class);
		MISRows<DW_RKCREDIT> misRowsDW_RKCREDIT = new MISRows<DW_RKCREDIT>(
				DW_RKCREDIT.class);
		MISRows<DW_RKJCIC> misRowsDW_RKJCIC = new MISRows<DW_RKJCIC>(
				DW_RKJCIC.class);
		MISRows<DW_RKPROJECT> misRowsDW_RKPROJECT = new MISRows<DW_RKPROJECT>(
				DW_RKPROJECT.class);
		MISRows<DW_RKADJUST> misRowsDW_RKADJUST = new MISRows<DW_RKADJUST>(
				DW_RKADJUST.class);
		MISRows<DW_RKCNTRNO> misRowsDW_RKCNTRNO = new MISRows<DW_RKCNTRNO>(
				DW_RKCNTRNO.class);
		MISRows<DW_RKAPPLICANT> misRowsDW_RKAPPLICANT = new MISRows<DW_RKAPPLICANT>(
				DW_RKAPPLICANT.class);
		List<DW_RKSCORE> DW_RKSCOREList = new ArrayList<DW_RKSCORE>();
		List<DW_RKCREDIT> DW_RKCREDITList = new ArrayList<DW_RKCREDIT>();
		List<DW_RKJCIC> DW_RKJCICList = new ArrayList<DW_RKJCIC>();
		List<DW_RKPROJECT> DW_RKPROJECTList = new ArrayList<DW_RKPROJECT>();
		List<DW_RKADJUST> DW_RKADJUSTList = new ArrayList<DW_RKADJUST>();
		List<DW_RKCNTRNO> DW_RKCNTRNOList = new ArrayList<DW_RKCNTRNO>();
		List<DW_RKAPPLICANT> DW_RKAPPLICANTList = new ArrayList<DW_RKAPPLICANT>();
		String mainId = c160s01d.getMainId();
		String custId = c160s01d.getCustId().substring(0, 10);
		String dupNo = c160s01d.getCustId().substring(10);
		C120S01Q s01q = findC120S01QByUniqueKey(mainId, c160m01a.getOwnBrId(),
				custId, dupNo);
		C120S01E s01e = cls1131Service.findModelByKey(C120S01E.class, mainId,
				custId, dupNo);
		C120S01B s01b = cls1131Service.findModelByKey(C120S01B.class, mainId,
				custId, dupNo);
		
		String company_id = "";
		if(Util.equals(c160m01a.getCaseType(), UtilConstants.Usedoc.caseType2.整批匯入)){
			if(Util.equals("Y", c160m01a.getIsUseChildren())){
				company_id = Util.trim(c160m01a.getChildrenId());
			}else{
				company_id = Util.trim(c160m01a.getCustId());
			}
		}
		
		/* 
		 當C160M01A.caseType=3整批匯入 時，沒有[個人負債比, 家庭負債比]
		● 之前沒有 copy C120M01A
		● 為了上傳 primary_card, additional_card, business_or_p_card 才在 CLS1161FormHandler :: importExcelStep2
		=========
		另一個可能的作法
		  由 c120s01q.srcMainId 抓到 C160M02A.MAINID, 再去抓 c120m01a
		*/
		C120M01A c120m01a = cls1131Service.findModelByKey(C120M01A.class, mainId,
				custId, dupNo);
		
		DW_RKSCOREList = this.upDW_RKSCORE(DW_RKSCOREList, c160s01d, s01q);
		DW_RKCREDITList = this.upDW_RKCREDIT(DW_RKCREDITList, c160s01d, s01q);
		DW_RKJCICList = this.upDW_RKJCIC(DW_RKJCICList, c160s01d, s01e, s01q);
		DW_RKCNTRNOList = this.upDW_RKCNTRNO(DW_RKCNTRNOList, c160s01d, s01q);
		DW_RKAPPLICANTList = this.upDW_RKAPPLICANT(DW_RKAPPLICANTList, c160s01d, c120m01a, s01b, s01q, company_id);
		DW_RKPROJECTList = this.upDW_RKPROJECT(DW_RKPROJECTList, c160s01d, s01q);
		DW_RKADJUSTList = this.upDW_RKADJUST(DW_RKADJUSTList, c160s01d, s01q);

		misRowsDW_RKSCORE.setValues(DW_RKSCOREList);
		misRowsDW_RKCREDIT.setValues(DW_RKCREDITList);
		misRowsDW_RKJCIC.setValues(DW_RKJCICList);
		misRowsDW_RKPROJECT.setValues(DW_RKPROJECTList);
		misRowsDW_RKADJUST.setValues(DW_RKADJUSTList);
		misRowsDW_RKCNTRNO.setValues(DW_RKCNTRNOList);
		misRowsDW_RKAPPLICANT.setValues(DW_RKAPPLICANTList);

		this.upDwToServer(misRowsDW_RKSCORE, "DWADM");
		this.upDwToServer(misRowsDW_RKCREDIT, "DWADM");
		this.upDwToServer(misRowsDW_RKJCIC, "DWADM");
		this.upDwToServer(misRowsDW_RKPROJECT, "DWADM");
		this.upDwToServer(misRowsDW_RKADJUST, "DWADM");
		this.upDwToServer(misRowsDW_RKCNTRNO, "DWADM");
		this.upDwToServer(misRowsDW_RKAPPLICANT, "DWADM");

	}

	private Integer parseIntColumn(Object s) {
		if (s == null) {
			return null;
		} else {
			return Util.parseInt(s);
		}
	}

	public List<DW_RKSCORE> upDW_RKSCORE(List<DW_RKSCORE> data,
			C160S01D c160s01d, C120S01Q c120s01q) {

		String BR_CD = Util.trim(c120s01q.getOwnBrId());
		String NOTEID = Util.trim(c160s01d.getOid());
		String CUSTID = Util.trim(c120s01q.getCustId());
		String DUPNO = Util.trim(c120s01q.getDupNo());
		String ACCT_KEY = "1";
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// 非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		BigDecimal R10_REVOL_RATE = null;
		BigDecimal R10_REVOL_RATE_SCORE = null;
		BigDecimal D07_LN_NOS_TAMT = null;
		BigDecimal D07_LN_NOS_TAMT_SCORE = null;
		BigDecimal D15_CC6_AVG_RC = null;
		BigDecimal D15_CC6_AVG_RC_SCORE = null;
		BigDecimal P19_CC12_PCODE_A_TIMES = null;
		String MARRIAGE = null;
		Integer CHILDREN = null;
		String OCCUPATION_1 = null;
		BigDecimal OCCUPATION_1_SCORE = null;
		String EDUCATION = null;
		Integer N06_INQ12_NAPP_BANK = null;
		BigDecimal N06_INQ12_NAPP_BANK_SCORE = null;
		Date RATING_DATE = null;
		BigDecimal R01_CC12_REVOL_RATE = null;
		BigDecimal R01_CC12_REVOL_RATE_SCORE = null;
		BigDecimal HINCOME_REG = null;
		BigDecimal HINCOME_REG_SCORE = null;
		BigDecimal P69_CC12_DELAY_RC_TIMES = null;
		BigDecimal P25_CC6_PCODE_A_TIMES = null;
		BigDecimal P25_CC6_PCODE_A_TIMES_SCORE = null;
		BigDecimal MARR_EDU_SCORE = null;
		BigDecimal P69_P19_SCORE = null;

		BigDecimal YPAY = null;
		BigDecimal YPAY_SCORE = null;
		BigDecimal SENIORITY = null;
		BigDecimal SENIORITY_SCORE = null;
		BigDecimal EDUCATION_N_SCORE = null;
		BigDecimal D63_LN_NOS_BANK = null;
		BigDecimal D63_SCORE = null;
		BigDecimal A21_CC6_RC_USE_MONTH = null;
		BigDecimal A21_SCORE = null;
		BigDecimal A11_CC6_RC_USE_BANK = null;
		BigDecimal A11_SCORE = null;
		BigDecimal D53_LN_6_TIMES_FLAG = null;
		BigDecimal D53_SCORE = null;
		BigDecimal PINCOME = null;
		BigDecimal PINCOME_SCORE = null;
		BigDecimal P68_CC6_DELAY_RC_TIMES = null;
		BigDecimal P68_P19_SCORE = null;
		String c_flag = "";
		BigDecimal DRATE_SCORE = null;
		
		if (!Util.isEmpty(c120s01q.getVarVer())) {
			String VarVer[] = c120s01q.getVarVer().split("\\.");
			MOWVER1 = Util.parseInt(VarVer[0]);
			MOWVER2 = Util.parseInt(VarVer[1]);
		}
		jcicDate = c120s01q.getJcicQDate();
		mowtype = "N";

		RATING_DATE = c120s01q.getGrdCDate();
		c_flag = ClsScoreUtil.upDW_column_C_FLAG(c120s01q);
		
		if (Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN,c120s01q.getVarVer())
				|| Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN,c120s01q.getVarVer())) {
			PINCOME = c120s01q.getPIncome();
			PINCOME_SCORE = c120s01q.getScrPIncome();
			SENIORITY = c120s01q.getSeniority();
			SENIORITY_SCORE = c120s01q.getScrseniority();
			D63_LN_NOS_BANK = c120s01q.getNochkItem01();
			D63_SCORE = c120s01q.getNoscrItem01();
			A11_CC6_RC_USE_BANK = c120s01q.getNochkItem03();
			A11_SCORE = c120s01q.getNoscrItem03();
			D07_LN_NOS_TAMT = c120s01q.getNochkItemD07();
			D07_LN_NOS_TAMT_SCORE = c120s01q.getNoscrItemD07();
			String str_N06 = null;
			if (c120s01q.getNochkItemN06() != null) {
				str_N06 = NumConverter.addComma(c120s01q.getNochkItemN06());
			}
			N06_INQ12_NAPP_BANK = parseIntColumn(str_N06);
			N06_INQ12_NAPP_BANK_SCORE = c120s01q.getNoscrItemN06();
			P68_CC6_DELAY_RC_TIMES = c120s01q.getNochkItemP68();
			P19_CC12_PCODE_A_TIMES = c120s01q.getNochkItemP19();
			P68_P19_SCORE = c120s01q.getNoscrItemP68P19();
		}else if (Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN,c120s01q.getVarVer()) 
				|| Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN,c120s01q.getVarVer())) {	
			PINCOME = c120s01q.getPIncome();
			PINCOME_SCORE = c120s01q.getScrPIncome();
			DRATE_SCORE = c120s01q.getNoscrItemDrate();
			D07_LN_NOS_TAMT = c120s01q.getNochkItemD07();
			D07_LN_NOS_TAMT_SCORE = c120s01q.getNoscrItemD07();
			String str_N06 = null;
			if (c120s01q.getNochkItemN06() != null) {
				str_N06 = LMSUtil.pretty_numStr(c120s01q.getNochkItemN06());
			}
			N06_INQ12_NAPP_BANK = parseIntColumn(str_N06);
			N06_INQ12_NAPP_BANK_SCORE = c120s01q.getNoscrItemN06();
			
			P19_CC12_PCODE_A_TIMES = c120s01q.getNochkItemP19();
			P69_CC12_DELAY_RC_TIMES = c120s01q.getNochkItemP69();
			P69_P19_SCORE = c120s01q.getNoscrItemP69P19();
			
			R01_CC12_REVOL_RATE = c120s01q.getNochkItemR01();
			R01_CC12_REVOL_RATE_SCORE = c120s01q.getNoscrItemR01();
			
			P25_CC6_PCODE_A_TIMES = c120s01q.getNochkItemP25();
			P25_CC6_PCODE_A_TIMES_SCORE = c120s01q.getNoscrItemP25();	
		}

		String CUST_KEY = Util.trim(c120s01q.getCustId());
		String LNGEFLAG = "M"; // M:主借人

		String DOCSTATUS = "3";

		DW_RKSCORE dw_rkscore = new DW_RKSCORE();
		dw_rkscore.setBr_cd(BR_CD);
		dw_rkscore.setNoteid(NOTEID);
		dw_rkscore.setCustid(CUSTID);
		dw_rkscore.setDupno(DUPNO);
		dw_rkscore.setAcct_key(ACCT_KEY);
		dw_rkscore.setMowtype(mowtype);
		dw_rkscore.setMowver1(MOWVER1);
		dw_rkscore.setMowver2(MOWVER2);
		dw_rkscore.setJcic_date(jcicDate);
		dw_rkscore.setCust_key(CUST_KEY);
		dw_rkscore.setLngeflag(LNGEFLAG);
		// dw_rkscore.setCc_revol_permit_limit(CC_REVOL_PERMIT_LIMIT);
		dw_rkscore.setR10_revol_rate(R10_REVOL_RATE);
		dw_rkscore.setR10_revol_rate_score(R10_REVOL_RATE_SCORE);
		dw_rkscore.setD07_ln_nos_tamt(D07_LN_NOS_TAMT);
		dw_rkscore.setD07_ln_nos_tamt_score(D07_LN_NOS_TAMT_SCORE);
		dw_rkscore.setD15_cc6_avg_rc(D15_CC6_AVG_RC);
		dw_rkscore.setD15_cc6_avg_rc_score(D15_CC6_AVG_RC_SCORE);
		dw_rkscore
				.setP19_cc12_pcode_a_times(parseIntColumn(P19_CC12_PCODE_A_TIMES));
		dw_rkscore.setMarriage(parseIntColumn(MARRIAGE));
		dw_rkscore.setChildren(CHILDREN);
		dw_rkscore.setOccupation_1(parseIntColumn(OCCUPATION_1));
		dw_rkscore.setOccupation_1_score(OCCUPATION_1_SCORE);
		dw_rkscore.setEducation(parseIntColumn(EDUCATION));
		dw_rkscore.setN06_inq12_napp_bank(N06_INQ12_NAPP_BANK);
		dw_rkscore.setN06_inq12_napp_bank_score(N06_INQ12_NAPP_BANK_SCORE);
		dw_rkscore.setRating_date(RATING_DATE);
		dw_rkscore.setDocstatus(DOCSTATUS);
		dw_rkscore.setData_src_dt(CapDate.getCurrentTimestamp());
		dw_rkscore.setR01_cc12_revol_rate(R01_CC12_REVOL_RATE);
		dw_rkscore.setR01_cc12_revol_rate_score(R01_CC12_REVOL_RATE_SCORE);
		dw_rkscore.setHincome_reg(HINCOME_REG);
		dw_rkscore.setHincome_reg_score(HINCOME_REG_SCORE);
		dw_rkscore
				.setP69_cc12_delay_rc_times(parseIntColumn(P69_CC12_DELAY_RC_TIMES));
		dw_rkscore
				.setP25_cc6_pcode_a_times(parseIntColumn(P25_CC6_PCODE_A_TIMES));
		dw_rkscore.setP25_cc6_pcode_a_times_score(P25_CC6_PCODE_A_TIMES_SCORE);
		dw_rkscore.setMarr_edu_score(MARR_EDU_SCORE);
		dw_rkscore.setP69_p19_score(P69_P19_SCORE);

		dw_rkscore.setYpay(YPAY);
		dw_rkscore.setYpay_score(YPAY_SCORE);
		dw_rkscore.setSeniority(SENIORITY);
		dw_rkscore.setSeniority_score(SENIORITY_SCORE);
		dw_rkscore.setEducation_n_score(EDUCATION_N_SCORE);
		dw_rkscore.setD63_ln_nos_bank(D63_LN_NOS_BANK);
		dw_rkscore.setD63_score(D63_SCORE);
		dw_rkscore.setA21_cc6_rc_use_month(A21_CC6_RC_USE_MONTH);
		dw_rkscore.setA21_score(A21_SCORE);
		dw_rkscore.setA11_cc6_rc_use_bank(A11_CC6_RC_USE_BANK);
		dw_rkscore.setA11_score(A11_SCORE);
		dw_rkscore.setD53_ln_6_times_flag(D53_LN_6_TIMES_FLAG);
		dw_rkscore.setD53_score(D53_SCORE);
		dw_rkscore.setPincome(PINCOME);
		dw_rkscore.setPincome_score(PINCOME_SCORE);
		dw_rkscore.setP68_cc6_delay_rc_times(P68_CC6_DELAY_RC_TIMES);
		dw_rkscore.setP68_p19_score(P68_P19_SCORE);
		dw_rkscore.setC_flag(c_flag);
		dw_rkscore.setDrate_score(DRATE_SCORE);
		data.add(dw_rkscore);
		return data;

	}

	/**
	 * DW_RKCREDIT個人信用評分評等紀錄 List
	 * 
	 */
	public List<DW_RKCREDIT> upDW_RKCREDIT(List<DW_RKCREDIT> data,
			C160S01D c160s01d, C120S01Q c120s01q) {
		String BR_CD = Util.trim(c120s01q.getOwnBrId());
		String NOTEID = Util.trim(c160s01d.getOid());
		String CUSTID = Util.trim(c120s01q.getCustId());
		String DUPNO = Util.trim(c120s01q.getDupNo());
		String ACCT_KEY = "1";
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// 非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		BigDecimal BASE_A = null;
		BigDecimal BASE_B = null;
		BigDecimal BASE_S = null;
		BigDecimal BASE_SCORE = null;
		BigDecimal TOTAL_SCORE = null;
		BigDecimal INITIAL_SCORE = null;
		BigDecimal PREDICT_BAD_RATE = null;
		Integer INITIAL_RATING = null;
		Integer ADJ_RATING = null;
		Integer FINAL_RATING = null;
		String JCIC_WARNING_FLAG = null;
		BigDecimal DR = null;
		BigDecimal DR_1YR = null;
		if (!Util.isEmpty(c120s01q.getVarVer())) {
			String VarVer[] = c120s01q.getVarVer().split("\\.");
			MOWVER1 = Util.parseInt(VarVer[0]);
			MOWVER2 = Util.parseInt(VarVer[1]);
		}
		jcicDate = c120s01q.getJcicQDate();
		mowtype = "N";
		BASE_A = c120s01q.getVarA();
		BASE_B = c120s01q.getVarB();
		BASE_S = c120s01q.getVarC();
		BASE_SCORE = c120s01q.getScrNum12();
		TOTAL_SCORE = c120s01q.getScrNum11();
		INITIAL_SCORE = c120s01q.getScrNum13();
		PREDICT_BAD_RATE = c120s01q.getPd();
		INITIAL_RATING = Util.parseInt(c120s01q.getGrade1());
		FINAL_RATING = Util.parseInt(c120s01q.getGrade3());
		JCIC_WARNING_FLAG = "N";
		if (Util.equals(Util.nullToSpace(c120s01q.getChkItem1()), "Y")
				|| Util.equals(Util.nullToSpace(c120s01q.getChkItem2()), "Y")
				|| Util.equals(Util.nullToSpace(c120s01q.getChkItem3()), "Y")
				|| Util.equals(Util.nullToSpace(c120s01q.getChkItem4()), "Y")
				|| Util.equals(Util.nullToSpace(c120s01q.getChkItem5()), "Y")
				|| Util.equals(Util.nullToSpace(c120s01q.getChkItem6()), "Y")
				|| Util.equals(Util.nullToSpace(c120s01q.getChkItem7()), "Y")
				|| Util.equals(Util.nullToSpace(c120s01q.getChkItem8()), "Y")) {
			JCIC_WARNING_FLAG = "Y";
		}

		// 借款期限(月) <= 12為短期
		boolean isShortPeriodCase = c160s01d.getMonth() <= 12;

		DR = isShortPeriodCase ? c120s01q.getDr_2YR() : c120s01q.getDr_3YR();
		DR_1YR = isShortPeriodCase ? c120s01q.getDr_1YR_S() : c120s01q
				.getDr_1YR_L();

		ADJ_RATING = INITIAL_RATING - FINAL_RATING;

		String CUST_KEY = Util.trim(c120s01q.getCustId());
		String LNGEFLAG = "M";

		String DOCSTATUS = "3";

		String FINAL_RATING_FLAG = "Y";

		DW_RKCREDIT dw_rkcredit = new DW_RKCREDIT();
		dw_rkcredit.setBr_cd(BR_CD);
		dw_rkcredit.setNoteid(NOTEID);
		dw_rkcredit.setCustid(CUSTID);
		dw_rkcredit.setDupno(DUPNO);
		dw_rkcredit.setAcct_key(ACCT_KEY);
		dw_rkcredit.setMowtype(mowtype);
		dw_rkcredit.setMowver1(MOWVER1);
		dw_rkcredit.setMowver2(MOWVER2);
		dw_rkcredit.setJcic_date(jcicDate);
		dw_rkcredit.setCust_key(CUST_KEY);
		dw_rkcredit.setLngeflag(LNGEFLAG);
		dw_rkcredit.setBase_a(BASE_A);
		dw_rkcredit.setBase_b(BASE_B);
		dw_rkcredit.setBase_s(BASE_S);
		dw_rkcredit.setBase_score(BASE_SCORE);
		dw_rkcredit.setTotal_score(TOTAL_SCORE);
		dw_rkcredit.setInitial_score(INITIAL_SCORE);
		dw_rkcredit.setPredict_bad_rate(PREDICT_BAD_RATE);
		dw_rkcredit.setInitial_rating(INITIAL_RATING);
		dw_rkcredit.setAdj_rating(ADJ_RATING);
		dw_rkcredit.setFinal_rating(FINAL_RATING);
		dw_rkcredit.setJcic_warning_flag(JCIC_WARNING_FLAG);
		dw_rkcredit.setFinal_rating_flag(FINAL_RATING_FLAG);
		dw_rkcredit.setDocstatus(DOCSTATUS);
		dw_rkcredit.setDelete_reason("");
		dw_rkcredit.setReject_othereason_text("");
		dw_rkcredit.setData_src_dt(CapDate.getCurrentTimestamp());
		dw_rkcredit.setDr(DR);
		dw_rkcredit.setDr_1yr(DR_1YR);

		data.add(dw_rkcredit);
		return data;
	}

	/**
	 * DW_RKJCIC聯徵特殊負面資訊List
	 * 
	 */
	public List<DW_RKJCIC> upDW_RKJCIC(List<DW_RKJCIC> data, C160S01D c160s01d,
			C120S01E c120s01e, C120S01Q c120s01q) {

		String BR_CD = Util.trim(c120s01q.getOwnBrId());
		String NOTEID = Util.trim(c160s01d.getOid());
		String CUSTID = Util.trim(c120s01q.getCustId());
		String DUPNO = Util.trim(c120s01q.getDupNo());
		String ACCT_KEY = "1";
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// 非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		Date CHECK_QDATE = null;
		String EVER_BAD_CHECK = null;
		String REJECT_YN = null;
		String CREDIT_FORCE_STOP = null;
		String BAD_DEBT = null;
		String NEGO_LAW = null;
		String NEGO_BANK = null;
		String OTHER_WARNING = null;
		String LN12_PAY_DELAY_TIMES = null;
		String CC12_REVOL_PAY_DELAY_TIMES = null;
		String CC12_MINPAY_DELAY_TIMES = null;
		String CC12_TOTPAY_DELAY_TIMES = null;
		String CC12_CASH_ADV_TIMES = null;
		String LN12_CASH_TIMES = null;

		if (!Util.isEmpty(c120s01q.getVarVer())) {
			String VarVer[] = c120s01q.getVarVer().split("\\.");
			MOWVER1 = Util.parseInt(VarVer[0]);
			MOWVER2 = Util.parseInt(VarVer[1]);
		}
		jcicDate = c120s01q.getJcicQDate();
		mowtype = "N";

		CHECK_QDATE = c120s01q.getEtchQDate();
		EVER_BAD_CHECK = Util.trim(c120s01q.getChkItem1a());
		REJECT_YN = Util.trim(c120s01q.getChkItem1b());
		CREDIT_FORCE_STOP = Util.trim(c120s01q.getChkItem1c());
		BAD_DEBT = Util.trim(c120s01q.getChkItem1d());
		NEGO_LAW = Util.trim(c120s01q.getChkItem2a());
		NEGO_BANK = Util.trim(c120s01q.getChkItem2b());
		OTHER_WARNING = Util.trim(c120s01q.getChkItem2c());
		LN12_PAY_DELAY_TIMES = Util.trim(c120s01q.getChkItem3());
		CC12_REVOL_PAY_DELAY_TIMES = Util.trim(c120s01q.getChkItem4());
		CC12_MINPAY_DELAY_TIMES = Util.trim(c120s01q.getChkItem5());
		CC12_TOTPAY_DELAY_TIMES = Util.trim(c120s01q.getChkItem6());
		CC12_CASH_ADV_TIMES = Util.trim(c120s01q.getChkItem7());
		LN12_CASH_TIMES = Util.trim(c120s01q.getChkItem8());

		String CUST_KEY = Util.trim(c120s01q.getCustId());
		String LNGEFLAG = "M";
		Date END_DATE = new Date();
		if (!Util.isEmpty(c120s01e)) {
			END_DATE = c120s01e.getEChkDDate();
		}

		String DOCSTATUS = "3";

		DW_RKJCIC dw_rkjcic = new DW_RKJCIC();
		dw_rkjcic.setBr_cd(BR_CD);
		dw_rkjcic.setNoteid(NOTEID);
		dw_rkjcic.setCustid(CUSTID);
		dw_rkjcic.setDupno(DUPNO);
		dw_rkjcic.setAcct_key(ACCT_KEY);
		dw_rkjcic.setMowtype(mowtype);
		dw_rkjcic.setMowver1(MOWVER1);
		dw_rkjcic.setMowver2(MOWVER2);
		dw_rkjcic.setJcic_date(jcicDate);
		dw_rkjcic.setCust_key(CUST_KEY);
		dw_rkjcic.setLngeflag(LNGEFLAG);

		dw_rkjcic.setCheck_qdate(CHECK_QDATE);
		dw_rkjcic.setEnd_date(END_DATE);

		dw_rkjcic.setLn12_pay_delay_times(LN12_PAY_DELAY_TIMES);
		dw_rkjcic.setCc12_revol_pay_delay_times(CC12_REVOL_PAY_DELAY_TIMES);
		dw_rkjcic.setCc12_minpay_delay_times(CC12_MINPAY_DELAY_TIMES);
		dw_rkjcic.setCc12_totpay_delay_times(CC12_TOTPAY_DELAY_TIMES);
		dw_rkjcic.setCc12_cash_adv_times(CC12_CASH_ADV_TIMES);
		dw_rkjcic.setLn12_cash_times(LN12_CASH_TIMES);
		dw_rkjcic.setDocstatus(DOCSTATUS);
		dw_rkjcic.setData_src_dt(CapDate.getCurrentTimestamp());

		dw_rkjcic.setEver_bad_check(EVER_BAD_CHECK);
		dw_rkjcic.setReject_yn(REJECT_YN);
		dw_rkjcic.setCredit_force_stop(CREDIT_FORCE_STOP);
		dw_rkjcic.setBad_debt(BAD_DEBT);
		dw_rkjcic.setNego_law(NEGO_LAW);
		dw_rkjcic.setNego_bank(NEGO_BANK);
		dw_rkjcic.setOther_warning(OTHER_WARNING);

		data.add(dw_rkjcic);
		return data;
	}

	/**
	 * DW_RKAPPLICANT申請人基本資料List
	 * 
	 */
	private List<DW_RKAPPLICANT> upDW_RKAPPLICANT(List<DW_RKAPPLICANT> data,
			C160S01D c160s01d, C120M01A c120m01a, C120S01B c120s01b, C120S01Q c120s01q, 
			String company_id) {
		String BR_CD = Util.trim(c120s01q.getOwnBrId());
		String NOTEID = Util.trim(c160s01d.getOid());
		String CUSTID = Util.trim(c120s01q.getCustId());
		String DUPNO = Util.trim(c120s01q.getDupNo());
		String ACCT_KEY = "1";
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// 非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		if (!Util.isEmpty(c120s01q.getVarVer())) {
			String VarVer[] = c120s01q.getVarVer().split("\\.");
			MOWVER1 = Util.parseInt(VarVer[0]);
			MOWVER2 = Util.parseInt(VarVer[1]);
		}
		jcicDate = c120s01q.getJcicQDate();
		mowtype = "N";

		String CUST_KEY = Util.trim(c120s01q.getCustId());
		String LNGEFLAG = "M";
		Date DOB = null;// c120s01a.getBirthday();
		//String EDUCATION = null;// Util.trim(c120s01a.getEdu());
		//String MARRIAGE = null;// Util.trim(c120s01a.getMarry());
		Integer CHILDREN = null;// c120s01a.getChild();
		BigDecimal SENIORITY = c120s01b.getSeniority();
		String POS = Util.trim(c120s01b.getJobType1())
				+ Util.trim(c120s01b.getJobType2())
				+ Util.trim(c120s01b.getJobTitle());
		BigDecimal YPAY = c120s01b.getPayAmt();
		String OMONEY = Util.trim(c120s01b.getOthType());
		BigDecimal OMONEY_AMT = c120s01b.getOthAmt();
		BigDecimal HINCOME = null;// LMSUtil.getUploadYFamAmt(c120s01c.getYFamAmt());

		// 2013/07/10,Fantasy,個人負債比率和家庭負債比率 Interger改為BigDecimal
		// Integer DRATE = c120s01c.getDRate();
		// Integer YRATE = c120s01c.getYRate();
		BigDecimal DRATE = null;// c120s01c.getDRate();
		BigDecimal YRATE = null;// c120s01c.getYRate();
		BigDecimal FRATE = null;// c120s01c.getFRate();
		String CERTIFICATE = null;// Util.trim(c120s01b.getInDoc());
		String HCERTIFICATE = null;// Util.trim(c120s01c.getYIncomeCert());
		String LAWADR_ZIP_CD = null;// Util.trim(c120s01a.getFZip());
		String ADR_ZIP_CD = null;// Util.trim(c120s01a.getCoZip());
		String DBCREDIT = null;// Util.trim(c120s01c.getCredit());
		
		String ISPFund = null;// Util.trim(c120s01c.getIsPeriodFund());
		String OBUSINESS = null;// _convertBusi(c120s01c.getBusi());

		BigDecimal INVMBAL = null;// c120s01c.getInvMBalAmt();
		BigDecimal INVOBAL = null;// c120s01c.getInvOBalAmt();
		BigDecimal ODEP = null;// c120s01c.getBranAmt();
		String CMSSTATUS = null;// Util.trim(c120s01a.getCmsStatus());

		String DOCSTATUS = "3";

		String YPAY_SWFT = Util.trim(c120s01b.getPayCurr());
		String OMONEY_AMT_SWFT = Util.trim(c120s01b.getOthCurr());
		String HINCOME_SWFT = null;// Util.trim(c120s01c.getYFamCurr());
		String INVMBAL_SWFT = null;// Util.trim(c120s01c.getInvMBalCurr());
		String INVOBAL_SWFT = null;// Util.trim(c120s01c.getInvOBalCurr());
		String ODEP_SWFT = null;// Util.trim(c120s01c.getBranCurr());

		DW_RKAPPLICANT dw_rkapplicant = new DW_RKAPPLICANT();
		dw_rkapplicant.setBr_cd(BR_CD);
		dw_rkapplicant.setNoteid(NOTEID);
		dw_rkapplicant.setCustid(CUSTID);
		dw_rkapplicant.setDupno(DUPNO);
		dw_rkapplicant.setAcct_key(ACCT_KEY);
		dw_rkapplicant.setMowtype(mowtype);
		dw_rkapplicant.setMowver1(MOWVER1);
		dw_rkapplicant.setMowver2(MOWVER2);

		dw_rkapplicant.setJcic_date(jcicDate);
		dw_rkapplicant.setCust_key(CUST_KEY);
		dw_rkapplicant.setLngeflag(LNGEFLAG);
		dw_rkapplicant.setDob(DOB);
		dw_rkapplicant.setEducation(null);
		dw_rkapplicant.setMarriage(null);
		dw_rkapplicant.setChildren(CHILDREN);
		dw_rkapplicant.setSeniority(SENIORITY);
		dw_rkapplicant.setPos(POS);
		dw_rkapplicant.setYpay(YPAY);
		dw_rkapplicant.setOmoney(OMONEY);
		dw_rkapplicant.setOmoney_amt(OMONEY_AMT);
		dw_rkapplicant.setHincome(HINCOME);
		// dw_rkapplicant.setDrate(Util.parseBigDecimal(DRATE));
		// dw_rkapplicant.setYrate(Util.parseBigDecimal(YRATE));
		dw_rkapplicant.setDrate(DRATE);
		dw_rkapplicant.setYrate(YRATE);
		dw_rkapplicant.setFrate(FRATE);
		dw_rkapplicant.setCertificate(CERTIFICATE);
		dw_rkapplicant.setHcertificate(HCERTIFICATE);
		dw_rkapplicant.setLawadr_zip_cd(LAWADR_ZIP_CD);
		dw_rkapplicant.setAdr_zip_cd(ADR_ZIP_CD);
		dw_rkapplicant.setDbcredit(DBCREDIT);
		dw_rkapplicant.setIspfund(ISPFund);
		dw_rkapplicant.setObusiness(OBUSINESS);
		dw_rkapplicant.setInvmbal(INVMBAL);
		dw_rkapplicant.setInvobal(INVOBAL);
		dw_rkapplicant.setOdep(ODEP);
		dw_rkapplicant.setCmsstatus(CMSSTATUS);
		dw_rkapplicant.setDocstatus(DOCSTATUS);
		dw_rkapplicant.setData_src_dt(CapDate.getCurrentTimestamp());

		dw_rkapplicant.setYpay_swft(YPAY_SWFT);
		dw_rkapplicant.setOmoney_amt_swft(OMONEY_AMT_SWFT);
		dw_rkapplicant.setHincome_swft(HINCOME_SWFT);
		dw_rkapplicant.setInvmbal_swft(INVMBAL_SWFT);
		dw_rkapplicant.setInvobal_swft(INVOBAL_SWFT);
		dw_rkapplicant.setOdep_swft(ODEP_SWFT);
		if(c120m01a!=null){
			dw_rkapplicant.setPrimary_card(c120m01a.getPrimary_card());
			dw_rkapplicant.setAdditional_card(c120m01a.getAdditional_card());
			dw_rkapplicant.setBusiness_or_p_card(c120m01a.getBusiness_or_p_card());
		}
		dw_rkapplicant.setCompany_id(Util.trim(company_id));
		dw_rkapplicant.setTotal_capital(null);
		dw_rkapplicant.setPaidup_capital(null);
		dw_rkapplicant.setCompany_type("");
		data.add(dw_rkapplicant);
		return data;
	}

	/**
	 * DW_RKCNTRNO額度檔List
	 * 
	 */
	public List<DW_RKCNTRNO> upDW_RKCNTRNO(List<DW_RKCNTRNO> data,
			C160S01D c160s01d, C120S01Q c120s01q) {
		String BR_CD = Util.trim(c120s01q.getOwnBrId());
		String NOTEID = Util.trim(c160s01d.getOid());
		String CUSTID = Util.trim(c120s01q.getCustId());
		String DUPNO = Util.trim(c120s01q.getDupNo());
		String ACCT_KEY = "1";
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// 非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		String LOANCURR = "";
		BigDecimal LOANAMT = null;
		String PROPERTY = null;
		Integer LNYEAR = null;
		Integer LNMONTH = null;
		String PRODKIND = null;
		String SUBJCODE = null;
		String RESIDENTIAL = null;

		LOANCURR = "TWD";
		LOANAMT = c160s01d.getLoanTotAmt();
		// C160M01F．個金動審表匯入主檔
		C160M01F c160m01f = findModelByMainId(C160M01F.class,
				c160s01d.getMainId());

		PROPERTY = null;
		LNYEAR = c160s01d.getMonth() / 12;
		LNMONTH = c160s01d.getMonth()%12;

		PRODKIND = c160m01f.getProdKind();// l140s02a.getProdKind();
		SUBJCODE = c160m01f.getSubjCode();// l140s02a.getSubjCode();
		RESIDENTIAL = null;// l140s02a.getResidential();

		if (!Util.isEmpty(c120s01q.getVarVer())) {
			String VarVer[] = c120s01q.getVarVer().split("\\.");
			MOWVER1 = Util.parseInt(VarVer[0]);
			MOWVER2 = Util.parseInt(VarVer[1]);
		}
		jcicDate = c120s01q.getJcicQDate();
		mowtype = "N";

		String CUST_KEY = Util.trim(c120s01q.getCustId());
		String LNGEFLAG = "M";
		String CNTRNO = Util.trim(c160s01d.getCntrNo());

		String DOCSTATUS = "3";

		DW_RKCNTRNO dw_rkcntrno = new DW_RKCNTRNO();
		dw_rkcntrno.setBr_cd(BR_CD);
		dw_rkcntrno.setNoteid(NOTEID);
		dw_rkcntrno.setCustid(CUSTID);
		dw_rkcntrno.setDupno(DUPNO);
		dw_rkcntrno.setAcct_key(ACCT_KEY);
		dw_rkcntrno.setMowtype(mowtype);
		dw_rkcntrno.setMowver1(Util.parseInt(MOWVER1));
		dw_rkcntrno.setMowver2(Util.parseInt(MOWVER2));
		dw_rkcntrno.setJcic_date(jcicDate);
		dw_rkcntrno.setCust_key(CUST_KEY);
		dw_rkcntrno.setLngeflag(LNGEFLAG);
		dw_rkcntrno.setCntrno(CNTRNO);
		dw_rkcntrno.setDocstatus(DOCSTATUS);
		dw_rkcntrno.setData_src_dt(CapDate.getCurrentTimestamp());

		dw_rkcntrno.setLoancurr(LOANCURR);
		dw_rkcntrno.setLoanamt(LOANAMT);
		dw_rkcntrno.setProperty(PROPERTY);
		dw_rkcntrno.setLnyear(LNYEAR);
		dw_rkcntrno.setLnmonth(LNMONTH);
		dw_rkcntrno.setProdkind(PRODKIND);
		dw_rkcntrno.setSubjcode(SUBJCODE);
		dw_rkcntrno.setResidential(RESIDENTIAL);
		dw_rkcntrno.setC_flag(ClsScoreUtil.upDW_column_C_FLAG(c120s01q));
		dw_rkcntrno.setColl_house("N");
		data.add(dw_rkcntrno);
		return data;
	}

	/**
	 * DW_RKPROJECT敘做方案 List
	 * 
	 */
	public List<DW_RKPROJECT> upDW_RKPROJECT(List<DW_RKPROJECT> data,
			C160S01D c160s01d, C120S01Q c120s01q) {

		String UPDATE_AMT_FLAG = "N";
		String UPDATE_RATE_FLAG = "N";

		String BR_CD = Util.trim(c120s01q.getOwnBrId());
		String NOTEID = Util.trim(c160s01d.getOid());
		String CUSTID = Util.trim(c120s01q.getCustId());
		String DUPNO = Util.trim(c120s01q.getDupNo());
		String ACCT_KEY = "1";
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// 非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;

		if (!Util.isEmpty(c120s01q.getVarVer())) {
			String VarVer[] = c120s01q.getVarVer().split("\\.");
			MOWVER1 = Util.parseInt(VarVer[0]);
			MOWVER2 = Util.parseInt(VarVer[1]);
		}
		jcicDate = c120s01q.getJcicQDate();
		mowtype = "N";

		String CUST_KEY = Util.trim(c120s01q.getCustId());
		String LNGEFLAG = "M";
		String CASE_LEVEL = null;// Util.trim(l120m01a.getCaseLvl());
		String REASON_FLAG = null;// Util.trim(l120m01a.getCaseLvlReason());

		String AUTH_FLAG = null;// (Util.equals(Util.trim(l120m01a.getDocRslt()),
								// "1") ? "Y": "N");
		String CONTINUE_CD = null;// LMSUtil.getFinalGradeUpUse(l140s02a.getModelKind(),
									// Util.trim(l140s02a.getGrade1()));
		String REJECT_FLAG = null;// Util.trim(l140m01a.getCesRjtCause());
		Date CHKDATE = null;// l120m01a.getEndDate();

		String DOCSTATUS = "3";

		DW_RKPROJECT dw_rkproject = new DW_RKPROJECT();
		dw_rkproject.setBr_cd(BR_CD);
		dw_rkproject.setNoteid(NOTEID);
		dw_rkproject.setCustid(CUSTID);
		dw_rkproject.setDupno(DUPNO);
		dw_rkproject.setAcct_key(ACCT_KEY);
		dw_rkproject.setMowtype(mowtype);
		dw_rkproject.setMowver1(MOWVER1);
		dw_rkproject.setMowver2(MOWVER2);
		dw_rkproject.setJcic_date(jcicDate);
		dw_rkproject.setCust_key(CUST_KEY);
		dw_rkproject.setLngeflag(LNGEFLAG);
		dw_rkproject.setContinue_cd(CONTINUE_CD);
		dw_rkproject.setCase_level(CASE_LEVEL);
		dw_rkproject.setReason_flag(REASON_FLAG);
		dw_rkproject.setAuth_flag(AUTH_FLAG);
		dw_rkproject.setReject_flag(REJECT_FLAG);
		dw_rkproject.setUpdate_amt_flag(UPDATE_AMT_FLAG);
		dw_rkproject.setUpdate_rate_flag(UPDATE_RATE_FLAG);
		dw_rkproject.setChkdate(CHKDATE);
		dw_rkproject.setDocstatus(DOCSTATUS);
		dw_rkproject.setData_src_dt(CapDate.getCurrentTimestamp());
		data.add(dw_rkproject);
		return data;
	}

	/**
	 * DW_RKADJUST個人信用評等調整 List
	 * 
	 */
	public List<DW_RKADJUST> upDW_RKADJUST(List<DW_RKADJUST> data,
			C160S01D c160s01d, C120S01Q c120s01q) {
		String BR_CD = Util.trim(c120s01q.getOwnBrId());
		String NOTEID = Util.trim(c160s01d.getOid());
		String CUSTID = Util.trim(c120s01q.getCustId());
		String DUPNO = Util.trim(c120s01q.getDupNo());
		String ACCT_KEY = "1";
		Integer MOWVER1 = new Integer(0);
		Integer MOWVER2 = new Integer(0);

		// 非房貸(C120S01Q)信用模型
		String mowtype = "";
		Date jcicDate = null;
		String upgrade_reason_text = "";
		Integer upgrade_level = new Integer(0);
		String downgrade_reason_text = "";
		Integer downgrade_level = new Integer(0);

		String UPGRADE_REASON_FLAG = "";
		String UPGRADE_REASON_TEXT = "";
		String DOWNGRADE_REASON_TEXT = "";

		if (!Util.isEmpty(c120s01q.getVarVer())) {
			String VarVer[] = c120s01q.getVarVer().split("\\.");
			MOWVER1 = Util.parseInt(VarVer[0]);
			MOWVER2 = Util.parseInt(VarVer[1]);
		}
		jcicDate = c120s01q.getJcicQDate();
		mowtype = "N";

		UPGRADE_REASON_FLAG = Util.trim(c120s01q.getAdjustFlag());
		UPGRADE_REASON_TEXT = Util.trim(c120s01q.getAdjustReason());
		DOWNGRADE_REASON_TEXT = Util.trim(c120s01q.getAdjustReason());

		
		if (Util.equals(c120s01q.getAdjustStatus(), "1")) {
			upgrade_reason_text = UPGRADE_REASON_TEXT;
			upgrade_level = Util.parseInt(c120s01q.getGrade2());
		} else if (Util.equals(c120s01q.getAdjustStatus(), "2")) {
			downgrade_reason_text = DOWNGRADE_REASON_TEXT;
			downgrade_level = Util.parseInt(c120s01q.getGrade2());
		} else {
			upgrade_level = 0;
			downgrade_level = 0;
		}

		String CUST_KEY = Util.trim(c120s01q.getCustId());
		String LNGEFLAG = "M";		
		String DOCSTATUS = "3";

		DW_RKADJUST dw_rkadjust = new DW_RKADJUST();
		dw_rkadjust.setBr_cd(BR_CD);
		dw_rkadjust.setNoteid(NOTEID);
		dw_rkadjust.setCustid(CUSTID);
		dw_rkadjust.setDupno(DUPNO);
		dw_rkadjust.setAcct_key(ACCT_KEY);
		dw_rkadjust.setMowtype(mowtype);
		dw_rkadjust.setMowver1(MOWVER1);
		dw_rkadjust.setMowver2(MOWVER2);
		dw_rkadjust.setJcic_date(jcicDate);
		dw_rkadjust.setCust_key(CUST_KEY);
		dw_rkadjust.setLngeflag(LNGEFLAG);
		dw_rkadjust.setUpgrade_reason_flag(UPGRADE_REASON_FLAG);

		dw_rkadjust.setUpgrade_reason_text(upgrade_reason_text);
		dw_rkadjust.setUpgrade_level(upgrade_level);
		dw_rkadjust.setDowngrade_reason_text(downgrade_reason_text);
		dw_rkadjust.setDowngrade_level(downgrade_level);

		dw_rkadjust.setDocstatus(DOCSTATUS);
		dw_rkadjust.setData_src_dt(CapDate.getCurrentTimestamp());
		data.add(dw_rkadjust);
		return data;
	}

	@Override
	public void complement_ELF504(C160M01A c160m01a) throws CapMessageException {
		if (Util.equals(c160m01a.getCaseType(), "3")) { 
			// 整批匯入上傳
		} else {
			//============================
			// 一般及團貸上傳
			//============================
			List<LNF130> raw_LNF130List = new ArrayList<LNF130>();
			List<ELF500> ELF500List = new ArrayList<ELF500>();
			// 目前系統時間
			String sDate = CapDate.formatDate(new Date(), UtilConstants.DateFormat.YYYY_MM_DD);
			
			L120M01A l120m01a = l120m01aDao.findByMainId(c160m01a.getSrcMainId());
						
			for (C160M01B c160m01b : c160m01bDao.findByMainId(c160m01a.getMainId())) {		
				L140M01A l140m01a = l140m01aDao.findByMainId(c160m01b.getRefmainId());

				if (l140m01a != null) {
					
					L120M01A l120m01aNow = null;
					String elf500_document_no = "";
					if(true){
						/*
						 動審表一般：會選擇１份簽報書內的額度，可直接抓 c160m01a.srcMainId
						 動審表團貸：但依團貸母戶，列出底下Ｎ筆的團貸子戶。來源是Ｎ份簽報書。
						*/
						if (Util.equals(UtilConstants.Usedoc.caseType2.團貸,
								c160m01a.getCaseType())) {
							l120m01aNow = l120m01aDao.findByMainId(l140m01a.getL120m01c()
									.getMainId());
						} else {
							l120m01aNow = l120m01a;
						}
						if(l120m01aNow!=null){
							elf500_document_no = getUploadCaseNo(l120m01aNow, l140m01a.getCntrNo());
						}
					}
						
					raw_LNF130List = upLNF130(raw_LNF130List, c160m01a, c160m01b,
							l140m01a, sDate, true);
					
					ELF500 mock_elf500 = new ELF500();
					mock_elf500.setElf500_cntrno(l140m01a.getCntrNo());
					mock_elf500.setElf500_document_no(elf500_document_no);
					ELF500List.add(mock_elf500);
				}
			}
			
			List<LNF130> lnf130_list = new ArrayList<LNF130>();
			List<ELF504> elf504_list = new ArrayList<ELF504>();
			if(true){
				complement_LNF130_ELF504( lnf130_list , elf504_list 
						, category_by_cntrNo(raw_LNF130List), ELF500List, c160m01a);	
			}	
			if(true){					
				if(elf504_list.size()>0){
					//~~~~~~~~
					delMisToServerToELF504(elf504_list);
					//~~~~~~~~
					MISRows<ELF504> misRows504 = new MISRows<ELF504>(ELF504.class);
					misRows504.setValues(elf504_list);					
					upMisToServer(misRows504, "MIS");
				}
				if(lnf130_list.size()>0){
					//~~~~~~~~
					//LNF130 用 [該分行,901]去區分[新做、重簽] 
					//~~~~~~~~
					MISRows<LNF130> misRows130 = new MISRows<LNF130>(LNF130.class);
					misRows130.setValues(lnf130_list);
					upMisToServer(misRows130, "LN");	
				}					
			}
		}
	}
	
	private Map<String, List<LNF130>> category_by_cntrNo(List<LNF130> raw_LNF130List){
		//依 cntrNo 來分組
		Map<String, List<LNF130>> r = new HashMap<String, List<LNF130>>(); 
		for(LNF130 lnf130: raw_LNF130List){
			String cntrNo = lnf130.getLnf130_contract();
			if(!r.containsKey(cntrNo)){
				r.put(cntrNo, new ArrayList<LNF130>());
			}
			r.get(cntrNo).add(lnf130);
		}
		return r;
	}

	private void complement_LNF130_ELF504(List<LNF130> lnf130_list , List<ELF504> elf504_list
			, Map<String, List<LNF130>> raw_cntrNo_lnf130Map , List<ELF500> ELF500List, C160M01A c160m01a){
		/*
		 在 ELF504 無額度的話 or 有額度但仍是 00001(動審，未開戶)
		 	a-loan 未開戶, 無LNF130 ===> 全新案
		 	a-loan 未開戶, 有LNF130 ===> 只上傳 ELF504【動審、未撥款 , V2動審 replace V1動審】
			a-loan 已開戶(舊案)     ===> 只上傳 ELF504【LNF130 更可能 a-loan 再變動】
		
		 在 ELF504 的帳號, 已由 00001 => 14碼
		        當 e-loan 簽案是 00001 (非重簽)=> 不上傳 LNF130, ELF504
		        當 e-loan 簽案是 14碼 (重簽)=> 上傳 LNF130, ELF504 上傳的 LNF130_BR_NO 會傳 901

		*/
		for(String cntrNo: raw_cntrNo_lnf130Map.keySet()){
			boolean needUpLNF130 = false;
			boolean needUpELF504 = false;
			//---------
			if(true){
				List<LNF130> db_lnf130_list = lnLNF130Service.findByCntrNo(cntrNo);
				if(db_lnf130_list.size()==0){
					//全新案件
					needUpLNF130 = true;
					needUpELF504 = true;
				}else{
					List<ELF504> db_elf504_list = misELF504Service.findByCntrNo(cntrNo);
					if(db_elf504_list.size()==0){
						//有 LNF130，無ELF504
						
						/*
						此時的 lnf130 的 loanNo 可能是 14碼的帳號，也可能是 00001
						要考慮
						(1)ELF504 剛上線時，補舊資料(上傳 web e-Loan 的動審表)
						(2)在 notes e-Loan 時的額度，在  web e-loan 變更條件 重簽
						        無法補傳 notes 動審表，無法補舊的 notes ELF504 資料
							        
						        而在  web e-loan 調整後的內容，應同時上傳 LNF130, ELF504
						   =>【complement vs 新的 web e-loan 動審表覆核】的 logic 應該不同
						*/
						
						//若 lnf130 的 loanNo='00000000000000'
						//也不上傳
						needUpLNF130 = false; //LNF130 在歷次動審覆核時已更新【更可能 a-loan 再變動】
						needUpELF504 = true;
					}else{
						//有 LNF130，有ELF504

						//因一個產品會有N個分段利率，在此抓 distinct loanNo
						Set<String> db_elf504_ln = new HashSet<String>();									
						Set<String> db_elf504_lms = new HashSet<String>();
						
						for(ELF504 elf504: db_elf504_list){
							String loanNo = Util.trim(elf504.getElf504_loan_no());
							if(loanNo.length()==14){
								db_elf504_ln.add(loanNo);	
							}else{
								db_elf504_lms.add(loanNo);
							}
						}
						if(db_elf504_ln.size()==0 ){
							if(db_elf504_lms.size()>0){
								//在 ELF504 內的資料，都未被 a-loan 回寫，仍停在 00001
								
								//【動審、未撥款 , V2動審 replace V1動審】
								//【LNF130 更可能 a-loan 再變動】
								needUpLNF130 = false;
								needUpELF504 = true;
							}else{
								//size 都==0
							}
						}else{ 
							//ELF504 曾被 a-loan 回寫（已由 00001 => 14碼）
							int l140s02a_new_cnt = 0;
							int l140s02a_loanNo_cnt = 0;
							for(LNF130 newobjLNF130: raw_cntrNo_lnf130Map.get(cntrNo)){
								if(Util.equals(newobjLNF130.getLnf130_br_no(), BR_901)){
									l140s02a_loanNo_cnt++;	
								}else{
									l140s02a_new_cnt++;	
								}
							}
							
							if(l140s02a_loanNo_cnt>0){
								//(重簽、上傳的 LNF130_BR_NO 會傳 901)
								needUpLNF130 = true;
								needUpELF504 = true;									
							}else{
								if(l140s02a_new_cnt>0){
									needUpLNF130 = false;
									needUpELF504 = false;
								}else{
									//size 都==0
								}
							}
						}
					}
				}
			}				
			//---------
			if(needUpLNF130){
				lnf130_list.addAll(raw_cntrNo_lnf130Map.get(cntrNo));								
			}
			if(needUpELF504){
				List<ELF504> list = buildELF504(raw_cntrNo_lnf130Map.get(cntrNo), ELF500List, c160m01a);
				if(true){
					//透過XXX欄位，區分［在 ELF504 剛上線時，補舊資料］vs［e-loan覆核］
				}
				elf504_list.addAll(list);	
			}
		}		
	}
	private boolean isChgProdSubj(String raw_loanNo){
		String loanNo = Util.trim(raw_loanNo);
		if(loanNo.length()==5 && Util.parseInt(loanNo)>50){
			return true;
		}
		return false;
	}
	
	private List<MISLN30> fetch_not_cancel_lnf030(String cntrNo){
		List<MISLN30> r = new ArrayList<MISLN30>();
		for(MISLN30 lnf030 : misLNF030Service.selByCntrNo(cntrNo)){
			if(lnf030.getLnf030_cancel_date()==null){
				r.add(lnf030);
			}
		}
		return r;
	}
	
	private List<LNF13E> upLNF13E(List<LNF13E> data, L140M01A l140m01a) {
		
		if(LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()), UtilConstants.Cntrdoc.Property.新做)){
			LNF13E lnf13e = new LNF13E();
			String introduceSrc = l140m01a.getIntroduceSrc();
			lnf13e.setLnf13e_contract(l140m01a.getCntrNo());
			lnf13e.setLnf13e_intro_src(introduceSrc);//引介來源
			lnf13e.setLnf13e_mega_empno(l140m01a.getMegaEmpNo());//引介行員代號
			lnf13e.setLnf13e_agnt_no(l140m01a.getAgntNo());//引介房仲代號
			lnf13e.setLnf13e_agnt_chain(l140m01a.getAgntChain());//引介房仲連鎖店類型
			lnf13e.setLnf13e_mega_code(l140m01a.getMegaCode());//引介子公司代號
			lnf13e.setLnf13e_sub_unitno(l140m01a.getSubUnitNo());//引介子公司分支代號
			lnf13e.setLnf13e_sub_empno(l140m01a.getSubEmpNo());//引介子公司員工編號
			lnf13e.setLnf13e_sub_empnm(l140m01a.getSubEmpNm());//引介子公司員工姓名
			lnf13e.setLnf13e_megaemp_brn(l140m01a.getMegaEmpBrNo());//引介分行
			lnf13e.setLnf13e_intro_name("9".equals(introduceSrc) || "A".equals(introduceSrc) ? l140m01a.getIntroCustName() : l140m01a.getIntroducerName());//引介人姓名
			lnf13e.setLnf13e_license_y(StringUtils.isNotBlank(l140m01a.getLicenseYear()) ? new BigDecimal(l140m01a.getLicenseYear()) : BigDecimal.ZERO);// 房仲證書(明)字號-年
			lnf13e.setLnf13e_license_w(l140m01a.getLicenseWord());// 房仲證書(明)字號-年登字
			lnf13e.setLnf13e_license_no(l140m01a.getLicenseNumber());// 房仲證書(明)字號-編號
			data.add(lnf13e);
		}
		
		return data;
	}
}