/* 
 * C241M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C241M01A;

/** 覆審報告表主檔 **/
@Repository
public class C241M01ADaoImpl extends LMSJpaDao<C241M01A, String>
	implements C241M01ADao {

	@Override
	public C241M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C241M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
			
	@Override
	public List<C241M01A> findByDocStatus(String docStatus){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		List<C241M01A> list = createQuery(C241M01A.class,search).getResultList();
		return list;
	}
	
	@Override
	public void deleteByC240M01AMainid(String mainId){
		Query query = getEntityManager().createNamedQuery("C241M01A.delByC240m01aMainid");
		query.setParameter("mainId", mainId); // 設置參數
		query.executeUpdate();
	}
}