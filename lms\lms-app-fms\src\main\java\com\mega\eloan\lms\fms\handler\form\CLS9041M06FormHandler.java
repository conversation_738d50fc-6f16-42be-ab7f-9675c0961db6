/* 
 * CLS9041FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.fms.service.CLS9041M06Service;
import com.mega.eloan.lms.model.C004M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 政策性留學生貸款送保彙報
 * </pre>
 * 
 * @since 2012/11/01
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/05,Vector Lo,new
 *          </ul>
 */
@Scope("request")
@Controller("cls9041m06formhandler")
public class CLS9041M06FormHandler extends AbstractFormHandler {

	@Resource
	CLS9041M06Service service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	DocFileService docFileService;

	@Resource
	BranchService branchservice;

	@Resource
	ICustomerService iCustomerService;
	
	/**
	 * 新增報送資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 * @throws IOException
	 */
	public IResult addC004M01A(PageParameters params)
			throws CapException, IOException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 依本日日期抓資料的日期範圍
		Calendar now = Calendar.getInstance();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int year = now.get(Calendar.YEAR), month = now.get(Calendar.MONTH) + 1;// 起始為0
		String beginDate, endDate;
		if (month <= 4) {// 1~4月->找去年5/1到10/31
			year--;
			beginDate = year + "-05-01";
			endDate = year + "-10-31";
		} else if (month >= 11) {// 11~12月->找今年5/1到10/31
			beginDate = year + "-05-01";
			endDate = year + "-10-31";
		} else {// 5~10月->找去年11/1到今年4/30
			beginDate = (year - 1) + "-11-01";
			endDate = year + "-04-30";			
		}
		// 開始加
		String mainId = IDGenerator.getUUID();
		C004M01A c004m01a = new C004M01A();
		
		List<Map<String, Object>> data = service.getMisData(beginDate, endDate);
		List<Map<String, Object>> stuData = service.getStuData(beginDate, endDate);

		/* 開始做xls */
		String ext = ".xls";
		String[] fileName = { "留學生補貼利息明細表v" + TWNDate.toAD(new Date())+ext,
				"留學生貸款補貼息分配表v" + TWNDate.toAD(new Date())+ext };
		Map<String, BigDecimal[]> subTotal = new LinkedHashMap<String, BigDecimal[]>();
		byte[][] tables = createXls(data, stuData, now.get(Calendar.YEAR)-1911,month, subTotal);
		for (int i = 0; i < tables.length; i++) {
			DocFile file = new DocFile();
			file.setMainId(mainId);
			file.setData(tables[i]);
			file.setCrYear("" + year);
			file.setFieldId("fms");
			file.setTotPages(1);
			file.setSrcFileName(fileName[i]);
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setBranchId(user.getUnitNo());
			file.setContentType("application/excel");
			file.setSysId("LMS");

			docFileService.save(file);
		}
		if(true){
			DocFile file = new DocFile();
			file.setMainId(mainId);
			file.setData(createReceiptDoc(beginDate, endDate, now.get(Calendar.YEAR)-1911,month, subTotal));
			file.setCrYear("" + year);
			file.setFieldId("receipt");
			file.setTotPages(1);
			file.setSrcFileName("收據.doc");
			file.setUploadTime(CapDate.getCurrentTimestamp());
			file.setBranchId(user.getUnitNo());
			file.setContentType("application/word");
			file.setSysId("LMS");

			docFileService.save(file);	
		}
		
		
		c004m01a.setMainId(mainId);

		c004m01a.setRptName("政策性留學生貸款補貼息相關報表");
		c004m01a.setBgnDate(TWNDate.valueOf(beginDate).toDate());
		c004m01a.setEndDate(TWNDate.valueOf(endDate).toDate());
		service.save(c004m01a);

		return result;
	}

	/**
	 * 刪除資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteC004M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.nullToSpace(params.getString("mainId"));
		List<DocFile> oldFile = docFileService.findByIDAndPid(mainId, null);
		for (DocFile file : oldFile) {
			file.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		service.delete(service.findModelByOid(C004M01A.class,
				params.getString("oid")));
		return result;
	}
	
	private byte[] createReceiptDoc(String beginDate, String endDate
			, int year, int month
			, Map<String, BigDecimal[]> subTotal) 
	throws IOException{
		BigDecimal sumTotAmt = BigDecimal.ZERO;		
		for (String brno : subTotal.keySet()) {
			BigDecimal totamount = null;
			
			if(true){
				BigDecimal[] counts = subTotal.get(brno);
				totamount = counts[0].setScale(0, BigDecimal.ROUND_HALF_UP);				
				//===
				sumTotAmt = sumTotAmt.add(totamount);
			}
		}	 
		
		String content = Util.getFileContent(Util.trim(PropUtil
				.getProperty("loadFile.dir")) + "word/fms/CLS9041W06.xml");		
		if(true){
			Map<String, String> paramMap = new HashMap<String, String>();
			paramMap.put("@@@TotMoney@@@", NumConverter.toChineseNumberFull(sumTotAmt));
			paramMap.put("@@@BegYearMonth@@@", (Integer.parseInt(beginDate.substring(0, 4))-1911)+"年"+(beginDate.substring(5, 7))+"月份");
			paramMap.put("@@@EndYearMonth@@@", (Integer.parseInt(endDate.substring(0, 4))-1911)+"年"+(endDate.substring(5, 7))+"月份");
			paramMap.put("@@@CurrentYearMonth@@@", "  "+year+" 年 "+month +" 月        日");
			content = Util.replaceWordContent(content, paramMap);
		}
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
		outWriter.write(content);
		outWriter.close();
		return baos.toByteArray();
	}
	
	/**
	 * 製作 "留學生補貼利息明細表"
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	private byte[][] createXls(List<Map<String, Object>> data,
			List<Map<String, Object>> stuData, int year, int month, Map<String, BigDecimal[]> subTotal ) {
		
		byte[][] xls = new byte[2][];
		ByteArrayOutputStream[] baos = new ByteArrayOutputStream[2];
		WritableWorkbook[] book = new WritableWorkbook[2];
		WritableSheet[] sheet = new WritableSheet[2];
		try {
			for (int m = 0; m < xls.length; m++) {
				baos[m] = new ByteArrayOutputStream();
				book[m] = Workbook.createWorkbook(baos[m]);
				sheet[m] = book[m].createSheet("1", 0);
			}
			
			String tWord = "";
			int modifyYear = 0; 
			if(month>=11){
				tWord = "下";
			}else if((month>=1 && month<=4)){
				tWord = "下";
				modifyYear = -1;
			}else{
				tWord = "上";
			}
			String titlePeriod = (year+modifyYear)+"年度"+tWord+"半年度辦理政策性留學生貸款補貼息";			
		
			xls_detail(sheet[0], data, stuData, subTotal, titlePeriod+"明細表");
		
			xls_summary(sheet[1], subTotal, titlePeriod+"分配表");
			
			for (int k = 0; k < xls.length; k++) {
				book[k].write();
				book[k].close();
				xls[k] = baos[k].toByteArray();
			}
			return xls;
		} catch (Exception ex) {
			logger.error("Exception!!", ex);
		}
		return null;
	}
	
	private String _key_custId_studId(Map<String, Object> m, String k1, String k2){
		return Util.trim(MapUtils.getString(m, k1))+"|"+Util.trim(MapUtils.getString(m, k2));
	}
	
	private void xls_detail(WritableSheet sheet
			, List<Map<String, Object>> data, List<Map<String, Object>> stuData
			, Map<String, BigDecimal[]> subTotal, String titleStr) 
	throws RowsExceededException, WriteException{
		WritableFont font10 = new WritableFont(WritableFont.createFont("新細明體"), 10);
		WritableCellFormat toLeftNoBorder = null;
		WritableCellFormat toLeft = null;
		WritableCellFormat toRight = null;
		if(true){
			toLeftNoBorder = LMSUtil.setCellFormat(toLeftNoBorder, font10, Alignment.LEFT, false, false);
			toLeft = LMSUtil.setCellFormat(toLeft, font10, Alignment.LEFT, true, false);			
			toRight = LMSUtil.setCellFormat(toRight, font10, Alignment.RIGHT, true, false);	
		}			
		//=============================
		
		int rowIdx = 0;
		sheet.mergeCells(0, rowIdx, 13, rowIdx);
		sheet.addCell(new Label(0, rowIdx, titleStr, toLeftNoBorder));
		rowIdx++;
		sheet.addCell(new Label(11, rowIdx, "製表日期", toLeftNoBorder));
		sheet.addCell(new Label(12, rowIdx, TWNDate.toAD(CapDate.getCurrentTimestamp()), toLeftNoBorder));
		rowIdx++;
		sheet.addCell(new Label(11, rowIdx, "金額單位：", toLeftNoBorder));
		sheet.addCell(new Label(12, rowIdx, "新台幣元", toLeftNoBorder));
		rowIdx++;
		// 分行別-借款人姓名-身分證字號-留學生姓名-身分證字號-攻讀學位-就讀學校-寬限期-撥款日期-貸款總額-補貼利率-貸款日數-補貼利息
		String[] title1 = { "分行別", "借款人姓名", "身分證字號", "留學生姓名", "身分證字號",
				"攻讀學位", "就讀學校", "寬限期", "撥款日期", "貸款總額", "補貼利率", "貸款日數",
				"補貼利息" };
		int width_educls = 7;
		int[] width1 = { 7, 10, 10, 10, 10, width_educls, width_educls, 25, 6, 10, 10, 10, 10, 10 };
		for (int j = 0, index = 0; j < title1.length; j++, index++) {
			
			switch (j) {
			case 5:
				sheet.mergeCells(index, rowIdx, index + 1, rowIdx);
				
				sheet.addCell(new Label(index, rowIdx, title1[j], toLeft));
				
				sheet.addCell(new Label(index, rowIdx+1, "碩士", toLeft));
				index++;
				sheet.addCell(new Label(index, rowIdx+1, "博士", toLeft));
				break;
			case 9:
			case 10:
			case 11:
			case 12:				
				sheet.addCell(new Label(index, rowIdx, title1[j], toLeft));
				sheet.addCell(new Label(index, rowIdx+1, "(" + (j - 8) + ")",toLeft));
				break;
			default:
				sheet.mergeCells(index, rowIdx, index, rowIdx+1);				
				sheet.addCell(new Label(index, rowIdx, title1[j], toLeft));
				break;
			}
			sheet.setColumnView(j, width1[j]);
		}
		rowIdx+=2;
		// -------------資料內容------------	
		Map<String, List<Map<String, Object>>> cat_data = new LinkedHashMap<String, List<Map<String, Object>>>();
		for(Map<String, Object> pivot: data){
			String brNo = Util.trim(pivot.get("BRNO"));
			if(!cat_data.containsKey(brNo)){
				cat_data.put(brNo, new ArrayList<Map<String, Object>>());
			}
			cat_data.get(brNo).add(pivot);			
		}
		//XXX 只用CUSTID,STUID不夠. 若讀完碩士後,續讀博士. 
		//正常應抓到博士的資料,但有可能因條件不足,導致判斷異常, 仍然抓到碩士的資料
		//要再去抓 LNF192 去加強
		Map<String, Map<String, Object>> cat_studataSingle = new HashMap<String, Map<String, Object>>();
		Map<String, TreeMap<String, Map<String, Object>>> cat_studataMultiple = new HashMap<String, TreeMap<String, Map<String, Object>>>();
		cat_stuData(cat_studataSingle, cat_studataMultiple, stuData);
		
		BigDecimal sumAllBrNo = BigDecimal.ZERO;
		Map<String, String> cacheMap = new HashMap<String, String>();
		Set<String> cntMaster = new HashSet<String>();
		Set<String> cntPhd = new HashSet<String>();
		for(String brNo : cat_data.keySet()){
			BigDecimal sumByBrNo = new BigDecimal(0).setScale(0,
					BigDecimal.ROUND_HALF_UP);
			for(Map<String, Object> pivot : cat_data.get(brNo)){
				// 分行別
				Label label = new Label(0, rowIdx, brNo, toLeft);
				sheet.addCell(label);
				
				String pivot_custId = Util.trim(pivot.get("CUSTID"));
				String pivot_custDup = Util.trim(pivot.get("CUSTDUP"));
				String pivot_studId = Util.trim(pivot.get("STUDID"));
				String pivot_allowTerm = Util.trim(pivot.get("ALLOW_TERM"));
				String pivot_begDate = Util.trim(pivot.get("BEG_DATE"));
				
				String cnt_dataStr = pivot_custId+"|"+pivot_studId+"|"+pivot_allowTerm+"|"+pivot_begDate;
				// 借款人身分證字號
				label = new Label(2, rowIdx, pivot_custId, toLeft);
				sheet.addCell(label);
				// stu身分證字號
				label = new Label(4, rowIdx, pivot_studId, toLeft);
				sheet.addCell(label);
				// 寬限期
				label = new Label(8, rowIdx, pivot_allowTerm, toRight);
				sheet.addCell(label);
				// 撥款日期
				label = new Label(9, rowIdx, pivot_begDate, toLeft);
				sheet.addCell(label);
				// 貸款總額
				BigDecimal value = Util.parseBigDecimal(
						Util.trim(pivot.get("LOAN_BAL"))).setScale(0,
						BigDecimal.ROUND_HALF_UP);
				label = new Label(10, rowIdx, _toNum(value), toRight);
				sheet.addCell(label);
				// 補貼利率
				BigDecimal rate = Util.parseBigDecimal(
						Util.trim(pivot.get("RATE"))).setScale(4,
						BigDecimal.ROUND_HALF_UP);
				label = new Label(11, rowIdx, rate.toString() + "%", toRight);
				sheet.addCell(label);
				// 貸款日數（LNF193_INT_DAYS）
				BigDecimal tmonth = (BigDecimal)MapUtils.getObject(pivot, "tmonth");
				label = new Label(12, rowIdx, _toNum(tmonth), toRight);
				sheet.addCell(label);
				if(true){
					String key = _key_custId_studId(pivot, "CUSTID", "STUDID"); 
					String _name = "";
					String _stuNm = "";
					String _degreeM = "";
					String _degreeD = "";
					String _schlNm = "";
					if(true){
						Map<String, Object> stu = null;
						if(cat_studataSingle.containsKey(key)){
							stu = cat_studataSingle.get(key);	
						}else{
							TreeMap<String, Map<String, Object>> m_data = cat_studataMultiple.get(key);
							String matchCntrNo = m_data.lastKey(); //default
							if(true){
								//因lnf193 沒有 cntrNo, 而 lnf192 有, 再去抓lnf192來判斷	
								Map<String, Object> lnf192 = service.getLNN192(pivot_custId, pivot_custDup,pivot_studId,pivot_begDate);
								String _lnf192_contract = Util.trim(MapUtils.getString(lnf192, "LNF192_CONTRACT"));
								if(Util.isNotEmpty(_lnf192_contract)){									
									matchCntrNo = _lnf192_contract;
								}
							}							
							stu = m_data.get(matchCntrNo);
						}
						if(stu==null){
							stu = new HashMap<String, Object>();
						}
						
						if (Util.equals(stu.get("CUSTID"), stu.get("STUID"))){
							_name = Util.trim(stu.get("STUNM"));
						}else{
							_name = Util.trim(stu.get("CNAME"));
						}
						
						_stuNm = Util.trim(stu.get("STUNM"));

						if (stu.get("EDUCLS").equals("3")) {
							_degreeM = "V";
							cntMaster.add(cnt_dataStr);
						} else {
							_degreeD = "V";
							cntPhd.add(cnt_dataStr);
						}
						_schlNm = Util.trim(stu.get("SCHLNM"));
					}
					
					if(true){
						//XXX 原本 Notes 的邏輯，就有一些 Name 抓不到. 再去抓 0024 來補強 
						if(Util.isEmpty(_name)){
							_name = getCustName(cacheMap, pivot_custId, pivot_custDup);
						}
						if(Util.isEmpty(_stuNm) 
								&& Util.equals(pivot_custId, pivot_studId) && Util.isNotEmpty(_name)){
							_stuNm = _name;
						}
					}
					
					label = new Label(1, rowIdx, _name, toLeft);
					sheet.addCell(label);
					
					label = new Label(3, rowIdx, _stuNm, toLeft);
					sheet.addCell(label);
				

					label = new Label(5, rowIdx, _degreeM, toLeft);
					sheet.addCell(label);
					label = new Label(6, rowIdx, _degreeD, toLeft);
					sheet.addCell(label);
					
					label = new Label(7, rowIdx, _schlNm, toLeft);
					sheet.addCell(label);
				}
				// 補貼利息
				if (pivot.get("SUMINT") != null) {
					BigDecimal sumint = Util.parseBigDecimal(
							pivot.get("SUMINT")).setScale(0,
							BigDecimal.ROUND_HALF_UP);
					label = new Label(13, rowIdx, _toNum(sumint), toRight);
					sheet.addCell(label);
					sumByBrNo = sumByBrNo.add(sumint);
					if (true) {
						if(!subTotal.containsKey(brNo)){
							BigDecimal[] values = new BigDecimal[2];
							values[0] = BigDecimal.ZERO;
							values[1] = BigDecimal.ZERO;
							subTotal.put(brNo, values);
						}
						
						if(true){
							BigDecimal[] values = subTotal.get(brNo);
							values[0] = values[0].add(sumint);
							//LNF193_TIME	貸款期間(1:中期 2:長期)
							if ("2".equals(pivot.get("tTime"))) {
								values[1] = values[1].add(sumint);
							}
							subTotal.put(brNo, values);
						}						
					}
				}
				//========
				rowIdx++;
			}
			sumAllBrNo = sumAllBrNo.add(sumByBrNo);
			// 合併單元格
			if(true){				
				for(int i=0;i<13;i++){
					sheet.addCell(new Label(i, rowIdx, "", toRight));	
				}
				sheet.addCell(new Label(13, rowIdx, _toNum(sumByBrNo), toRight));
				rowIdx++;
			}			
		}
		if(true){
			sheet.addCell(new Label(0, rowIdx, "合計", toLeft));
			for(int i=1;i<13;i++){
				sheet.addCell(new Label(i, rowIdx, "", toLeft));	
			}
			
			/*
			 XXX 在合計時，增加碩士/博士的人次
			 同1個ID+撥款日算乙次
			 (若期間基礎利率有變動, 在LNF193同1個ID+撥款日, 會出現2筆而且補貼利率不同, 只能算1次)
			 */
			int i_cntMaster = cntMaster.size();
			int i_cntPhd = cntPhd.size();
			sheet.addCell(new Label(3, rowIdx, (i_cntMaster+i_cntPhd)+"人次", toRight));
			sheet.addCell(new Label(5, rowIdx, i_cntMaster+"人次", toRight));
			sheet.addCell(new Label(6, rowIdx, i_cntPhd+"人次", toRight));
			
			sheet.addCell(new Label(13, rowIdx, _toNum(sumAllBrNo), toRight));
			rowIdx++;
		}
	}

	private String getCustName(Map<String, String> map, String custId, String dupNo){
		String key = custId+"-"+dupNo;
		if(!map.containsKey(key)){
			String _name = "";
			Map<String, Object> latestData = iCustomerService.findByIdDupNo(custId, dupNo);
			if(latestData!=null){			
				_name = Util.trim(latestData.get("CNAME"));	
			}
			//--------
			map.put(key, _name);
		}
		return Util.trim(map.get(key));
	}
	
	private void cat_stuData(Map<String, Map<String, Object>> cat_studataSingle
		, Map<String, TreeMap<String, Map<String, Object>>> cat_studataMultiple
		, List<Map<String, Object>> stuData){
		Map<String, Integer> myMap = new HashMap<String, Integer>();
		
		for(Map<String, Object> o: stuData){
			String key = _key_custId_studId(o, "CUSTID", "STUID"); 
			Integer cnt = myMap.containsKey(key)?myMap.get(key):0;
			cnt++;
			//---------
			myMap.put(key, cnt);
		}
		
		for(Map<String, Object> o: stuData){
			String key = _key_custId_studId(o, "CUSTID", "STUID"); 
			String cntrNo = Util.trim(o.get("CNTRNO"));
			
			Integer cnt = myMap.get(key);
			if(cnt>1){
				TreeMap<String, Map<String, Object>> cntrNo_objMap = new TreeMap<String, Map<String, Object>>();
				if(cat_studataMultiple.containsKey(key)){
					cntrNo_objMap = cat_studataMultiple.get(key);
				}
				
				cntrNo_objMap.put(cntrNo, o);
				cat_studataMultiple.put(key, cntrNo_objMap);	
			}else{
				cat_studataSingle.put(key, o);
			}			
		}
	}
	
	private void xls_summary(WritableSheet sheet, Map<String, BigDecimal[]> subTotal, String titleStr) 
	throws RowsExceededException, WriteException{
		WritableFont font10 = new WritableFont(WritableFont.createFont("新細明體"), 10);
		WritableCellFormat toLeftNoBorder = null;
		WritableCellFormat toLeft = null;
		WritableCellFormat toRight = null;
		WritableCellFormat toRightNoBorder = null;
		if(true){
			toLeftNoBorder = LMSUtil.setCellFormat(toLeftNoBorder, font10, Alignment.LEFT, false, false);
			toLeft = LMSUtil.setCellFormat(toLeft, font10, Alignment.LEFT, true, false);			
			toRight = LMSUtil.setCellFormat(toRight, font10, Alignment.RIGHT, true, false);	
			toRightNoBorder = LMSUtil.setCellFormat(toRightNoBorder, font10, Alignment.RIGHT, false, false);
		}			
		//=============================
		
		int rowIdx = 0;
		sheet.mergeCells(0, rowIdx, 6, rowIdx);
		sheet.addCell(new Label(0, rowIdx, titleStr, toLeftNoBorder));
		rowIdx++;
		rowIdx++;
		sheet.addCell(new Label(5, rowIdx, "金額單位：", toLeftNoBorder));
		sheet.addCell(new Label(6, rowIdx, "新台幣元", toLeftNoBorder));
		rowIdx++;
		
		if(true){			
			Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
			titleMap.put("分行", 30);
			titleMap.put("金額", 10);
			titleMap.put("聯往報單金額", 10);
			titleMap.put("中期有擔", 10);
			titleMap.put("中期無擔", 10);
			titleMap.put("長期有擔", 10);
			titleMap.put("長期無擔", 10);
			
			int titleIdx = 0;
			for(String title : titleMap.keySet()){
				int col_width = titleMap.get(title);
				
				sheet.setColumnView(titleIdx, col_width);				
				sheet.addCell(new Label(titleIdx, rowIdx, title, toLeft));
				//---	
				titleIdx++;
			}			
		}
		BigDecimal 八成 = new BigDecimal(0.8);
		BigDecimal sumTotAmt = BigDecimal.ZERO;
		rowIdx++;
		for (String brno : subTotal.keySet()) {
			BigDecimal totamount = null;
			BigDecimal lnamt = null;
			if(true){
				BigDecimal[] counts = subTotal.get(brno);
				totamount = counts[0].setScale(0, BigDecimal.ROUND_HALF_UP);
				lnamt = counts[1].setScale(0, BigDecimal.ROUND_HALF_UP);
				//===
				sumTotAmt = sumTotAmt.add(totamount);
			}
			
			// 分行
			String fullBrno = brno + branchservice.getBranchName(brno);
			sheet.addCell(new Label(0, rowIdx, fullBrno, toLeft));
			// 金額
			sheet.addCell(new Label(1, rowIdx, _toNum(totamount), toRight));
			// 聯往報單金額
			sheet.addCell(new Label(2, rowIdx, _toNum(totamount), toRight));
			// 長期有擔 = 0.8 * tTime不為2
			
			BigDecimal _H = lnamt.multiply(八成).setScale(0,BigDecimal.ROUND_HALF_UP);
			sheet.addCell(new Label(5, rowIdx, _toNum(_H), toRight));
			
			BigDecimal _I = lnamt.subtract(_H);
			// 長期無擔=另外的0.2
			sheet.addCell(new Label(6, rowIdx, _toNum(_I), toRight));
			// 中期有擔=(全-長有擔)*0.8
			BigDecimal _F = totamount.subtract(lnamt).multiply(八成).setScale(0, BigDecimal.ROUND_HALF_UP);
			sheet.addCell(new Label(3, rowIdx, _toNum(_F), toRight));
			// 中期無擔			
			sheet.addCell(new Label(4, rowIdx, _toNum(totamount.subtract(_H).subtract(_I).subtract(_F)), toRight));
			//==========
			rowIdx++;
		}
		if(true){
			rowIdx++;
			//撥付分行合計 
			//補貼金額
			sheet.addCell(new Label(0, rowIdx, "撥付分行合計 ", toLeftNoBorder));
			sheet.addCell(new Label(1, rowIdx, _toNum(sumTotAmt), toRightNoBorder));
			rowIdx++;
			sheet.addCell(new Label(0, rowIdx, "補貼金額  ", toLeftNoBorder));
			sheet.addCell(new Label(1, rowIdx, _toNum(sumTotAmt), toRightNoBorder));
		}
	}
	
	private String _toNum(BigDecimal v){
		return v.toString();
	}
	/**
	 * 刪資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult deleteFile(PageParameters params) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.nullToSpace(params.getString("oid"));
		service.deleteFile(oid);
		return result;
	}
}
