/* 
 * CLS1141S03Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.DataView;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.dao.L140MC1BDao;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140MC1B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金案件簽報書 - 額度明細表
 * </pre>
 * 
 * @since 2012/12/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/7,REX,new
 *          </ul>
 */
public class CLS1141S03Panel extends Panel {

	@Autowired
	CLSService clsService;

	@Autowired
	L140MC1BDao l140MC1BDao;

	@Autowired
	private CodeTypeService codeTypeService;

	@Autowired
	ProdService prodService;

	private static final String DEF_LOCALE = "zh_TW";
	
	// 個人負債比(%), 家庭負債比率(%), 家庭之負債佔資產比率(%), 個人之負債佔資產比率(%)
	private static String[] percentageArray = new String[]{"dRate", "fDebtAssetRatio", "fRate", "iDebtAssetRatio"};
	
	//單一方案成數, 全案最高核貸成數, 初始核貸成數, 檢核二最終核貸成數
	private static String[] ratioArray = new String[]{"check1ProdkindRatio", "check2ApprovedRatio", "initialRatio", "check2FinalRatio"};
	
	// 分項金額(元), 現請額度(元), 餘額(元), 擔保品本案總計時價(元), 擔保品土地應計增值稅(元), 擔保品扣除寬限期預提折舊金額(元), 年收入(萬元), 其他收入(萬元), 保費融資金額
	private static String[] dollarAarry = new String[]{"BLAmt", "currentApplyAmt", "disAmt", "inAmt", "lndTax", "loanAmt", "othAmt", "payAmt", "insLoanbal"};
	
	// 聯徵B42共同債務查詢－擔保品類別, 有近一年有二戶以上授信借貸結案紀錄, 有近三年有二戶以上授信借貸結案紀錄
	private static String[] haveNoNaAarry = new String[]{"qryMutualDebtB42", "over2DataPastY", "over2DataPast3y", "qryCollateralB42"};
	
	private L120M01A l120m01a;
	
	public CLS1141S03Panel(String id) {
		super(id);
	}

	public CLS1141S03Panel(String id, boolean updatePanelName, L120M01A l120m01a) {
		super(id, updatePanelName);
		this.l120m01a = l120m01a;
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		// UPGRADE: 前端須配合改Thymeleaf的樣式
//		add(_getComp("showPteamappGrid", LMSUtil.isParentCase(l120m01a)));
//		add(_getComp("showFastCntrNoButton", !LMSUtil.isParentCase(l120m01a)));
		model.addAttribute("showPteamappGrid", LMSUtil.isParentCase(l120m01a));
		model.addAttribute("showFastCntrNoButton", !LMSUtil.isParentCase(l120m01a));

		//J-113-0227 web eLoan房貸成數結果畫面
		if (true) {
			// UPGRADE: 前端須配合改Thymeleaf的樣式
//			Label housePlan_ALabel = new Label("housePlan_A", "");
//			Label housePlan_BLabel = new Label("housePlan_B", "");
//			Label housePlan_CLabel = new Label("housePlan_C", "");
//			Label housePlan_DLabel = new Label("housePlan_D", "");
//			Label housePlan_ELabel = new Label("housePlan_E", "");
//			Label housePlan_FLabel = new Label("housePlan_F", "");
			List<L140MC1B> housePlanResult = l140MC1BDao.findByCaseMainid(l120m01a.getMainId());
			List<CodeType> CodeTypes = codeTypeService.findByCodeTypeList("houseLoanPlanDetail", DEF_LOCALE);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			List<CodeType> showRows = new ArrayList<CodeType>();
			//判斷總處單位可看欄位及分行可看欄位才Insert Row
			for (CodeType codeType : CodeTypes) {
				String[] codeDesc2 = codeType.getCodeDesc2().split("\\|");
				String headOnly = codeDesc2[0];
				if ((headOnly.equals("headOnly") && user.getSsoUnitNo().startsWith("9"))
						|| !headOnly.equals("headOnly")) {
					showRows.add(codeType);
				}
			}
			if (housePlanResult.size() > 0) {
				//判斷是否可顯示Plan欄位
				for (L140MC1B l140mc1b : housePlanResult) {
					if (Util.isNotEmpty(l140mc1b.getProdKindType())) {
						// UPGRADE: 前端須配合改Thymeleaf的樣式
						if (l140mc1b.getProdKindType().equals("A")) {
//							housePlan_ALabel = new Label("housePlan_A", "true");
							model.addAttribute("housePlan_A", "true");
						}
						if (l140mc1b.getProdKindType().equals("B")) {
//							housePlan_BLabel = new Label("housePlan_B", "true");
							model.addAttribute("housePlan_B", "true");
						}
						if (l140mc1b.getProdKindType().equals("C")) {
//							housePlan_CLabel = new Label("housePlan_C", "true");
							model.addAttribute("housePlan_C", "true");
						}
						if (l140mc1b.getProdKindType().equals("D")) {
//							housePlan_DLabel = new Label("housePlan_D", "true");
							model.addAttribute("housePlan_D", "true");
						}
						if (l140mc1b.getProdKindType().equals("E")) {
//							housePlan_ELabel = new Label("housePlan_E", "true");
							model.addAttribute("housePlan_E", "true");
						}
						if (l140mc1b.getProdKindType().equals("F")) {
//							housePlan_FLabel = new Label("housePlan_F", "true");
							model.addAttribute("housePlan_F", "true");
						}
					}
				}
			}
			
			String version = null;
			for(L140MC1B l140mc1b : housePlanResult){
				version = !CapString.isEmpty(l140mc1b.getVersion()) ? l140mc1b.getVersion() : version;
			}

			// UPGRADE: 前端須配合改Thymeleaf的樣式
			// add(new Label("mortgageCheck_version", version));
			model.addAttribute("mortgageCheck_version", version);
			
			DataView<JSONObject> gridhousePlanResultView = this.getHouseResultView("_grid_housePlanResult", housePlanResult, showRows);
			gridhousePlanResultView.processData(model, params);
			// UPGRADE: 前端須配合改Thymeleaf的樣式
//			add(housePlan_ALabel);
//			add(housePlan_BLabel);
//			add(housePlan_CLabel);
//			add(housePlan_DLabel);
//			add(housePlan_ELabel);
//			add(housePlan_FLabel);
		}
	}

	private static final long serialVersionUID = 1L;

	// UPGRADE: 前端須配合改Thymeleaf的樣式
//	private Component _getComp(String id, boolean visible) {
//		Label r = new Label(id, "");
//		r.setVisible(visible);
//		return r;
//	}

	private DataView<JSONObject> getHouseResultView(final String id, final List<L140MC1B> lists, List<CodeType> CodeTypes) {

		final Map<String, String> purposeTypeMap = codeTypeService.findByCodeType("cms1010_bldUse");
		final Map<String, String> haveNoMap = codeTypeService.findByCodeType("HaveNoNa");
		
		final Map<String, String> msgMap = codeTypeService.findByCodeType("MRCheck_Error_Msg");
		msgMap.putAll(codeTypeService.findByCodeType("MRCheck_Tips_Msg"));
		
		final Map<String, String> realEstateHouseLoanPlanDesc = new HashMap<String, String>();
		realEstateHouseLoanPlanDesc.put("1", "投資型");
		realEstateHouseLoanPlanDesc.put("2", "自住型");

		return null;
		// UPGRADETODO: 待確認如何調整
		/*DataView dataView = new DataView(id, new ListDataProvider(CodeTypes)) {
			int count = 0;

			@Override
			protected void populateItem(Item item) {

				count++;
				CodeType modelObject = (CodeType) item.getModelObject();
				String[] codeDesc2 = modelObject.getCodeDesc2().split("\\|");
				String headOnly = codeDesc2[0];

				String source = codeDesc2[1];
				String condition = modelObject.getCodeDesc();
				String value = "";
				String desc = modelObject.getCodeDesc3();
				String prodKindType = "";
				boolean checkHeadOnly = modelObject.getCodeDesc2().equals("only") ? true : false;
				Label sourceLabel = new Label("L140MC1B.source", source);
				Label conditionLabel = new Label("L140MC1B.condition", condition);
				Label descLabel = new Label("L140MC1B.desc", desc);
				Label housePlan_ALabel = new Label("L140MC1B.housePlan_A", prodKindType);
				Label housePlan_BLabel = new Label("L140MC1B.housePlan_B", prodKindType);
				Label housePlan_CLabel = new Label("L140MC1B.housePlan_C", prodKindType);
				Label housePlan_DLabel = new Label("L140MC1B.housePlan_D", prodKindType);
				Label housePlan_ELabel = new Label("L140MC1B.housePlan_E", prodKindType);
				Label housePlan_FLabel = new Label("L140MC1B.housePlan_F", prodKindType);

				for (L140MC1B l140mc1b:lists) {
					try {
						prodKindType =l140mc1b.getProdKindType();
						if (modelObject.getCodeValue().equals("jobType")) {
							String jobType1 = Util.trim(l140mc1b.get("jobType1"));
							String jobType2 = Util.trim(l140mc1b.get("jobType2"));

							Map<String, String> jobType1Map = null;
							jobType1Map = codeTypeService.findByCodeType("lms1205s01_jobType1",
									LMSUtil.getLocale().toString());
							String code2 = getJobType2CodeValue(CapString.trimNull(jobType1));

							Map<String, String> map = codeTypeService
									.findByCodeType(code2);

							String jobTypeString = jobType1Map.get(Util.trim(jobType1));
							if (map != null)
								jobTypeString += " "+map.get(Util.trim(jobType2));

							value = jobTypeString;
						}
						else if(modelObject.getCodeValue().equals("jobTitle")){
							Map<String, String> jobTitleMap = null;
							jobTitleMap = codeTypeService.findByCodeType("lms1205s01_jobTitle",
									LMSUtil.getLocale().toString());
							String jobTitleString = jobTitleMap.get(Util.trim(l140mc1b.get(modelObject.getCodeValue())));

							value = jobTitleString;
						}
						else if(modelObject.getCodeValue().equals("prodKind")){
							Map<String, String> prodKindMap = prodService.getProdKindName();
							String prodKindNm = Util.trim(l140mc1b.get(modelObject.getCodeValue())) + prodKindMap.get(Util.trim(l140mc1b.get(modelObject.getCodeValue())));
							value = prodKindNm;
						}
						else if(modelObject.getCodeValue().equals("reUse")){
							CodeType c = codeTypeService.findByCodeTypeAndCodeValue(
									"lms1405s0202_reUse", Util.trim(l140mc1b.get(modelObject.getCodeValue())));
							if (c !=null ) {
								value = c.getCodeDesc();
							}
						}
						else if(modelObject.getCodeValue().equals("proPerty")){
							CodeType c = codeTypeService.findByCodeTypeAndCodeValue(
									"lms1405s02_proPerty", Util.trim(l140mc1b.get(modelObject.getCodeValue())));
							if (c !=null ) {
								value = c.getCodeDesc();
							}
						}
						else if(modelObject.getCodeValue().equals("countyCityCode")){
							CodeType c = codeTypeService.findByTypeAndDesc2(
									"counties", Util.trim(l140mc1b.get(modelObject.getCodeValue())));
							if (c !=null ) {
								value = c.getCodeDesc();
							}
						}
						else if(modelObject.getCodeValue().equals("dstrType")){
							CodeType c = codeTypeService.findByCodeTypeAndCodeValue(
									"cms1010_dstrType", Util.trim(l140mc1b.get(modelObject.getCodeValue())));
							if (c !=null ) {
								value = c.getCodeDesc();
							}
						}
						else if(modelObject.getCodeValue().equals("realEstateHouseLoanPlan")){
							value = realEstateHouseLoanPlanDesc.get( Util.trim(l140mc1b.get(modelObject.getCodeValue())));
						}
						else if(modelObject.getCodeValue().equals("bldActualUse")){ // 建物實際用途
							value = purposeTypeMap.get( Util.trim(l140mc1b.get(modelObject.getCodeValue())));
						}
						else if(modelObject.getCodeValue().equals("bldUse")){ // 主要用途(登記用途)
							value = purposeTypeMap.get( Util.trim(l140mc1b.get(modelObject.getCodeValue())));
						}
						else if(l140mc1b.get(modelObject.getCodeValue()) instanceof Date){
							value = Util.trim(CapDate.formatDate( (Date)l140mc1b.get(modelObject.getCodeValue()), "yyyy-MM-dd"));
						}
						else if(Arrays.asList(CLS1141S03Panel.percentageArray).contains(modelObject.getCodeValue())){
							value = Util.trim(l140mc1b.get(modelObject.getCodeValue()));
							value = StringUtils.isBlank(value) ? "-" : value + "%";
						}
						else if(Arrays.asList(CLS1141S03Panel.ratioArray).contains(modelObject.getCodeValue())){
							Object obj = l140mc1b.get(modelObject.getCodeValue());
							value = obj == null ? "-" : ((BigDecimal)obj).multiply(new BigDecimal(10)).setScale(2, RoundingMode.UP).toPlainString();
						}
						else if(Arrays.asList(CLS1141S03Panel.dollarAarry).contains(modelObject.getCodeValue())){
							value = Util.trim(l140mc1b.get(modelObject.getCodeValue()));
							value = StringUtils.isBlank(value) ? "-" : NumConverter.addComma(value);
						}
						else if(Arrays.asList(CLS1141S03Panel.haveNoNaAarry).contains(modelObject.getCodeValue())){
							value = haveNoMap.get( Util.trim(l140mc1b.get(modelObject.getCodeValue())));
						}
						else{
							value = Util.trim(l140mc1b.get(modelObject.getCodeValue()));
							value = StringUtils.isBlank(value) ? "-" : value;
						}
					} catch (Exception e) {
						String erro=e.toString();
					}
					if (Util.isNotEmpty(prodKindType)) {
						if (prodKindType.equals("A")) {
							housePlan_ALabel = new Label("L140MC1B.housePlan_A", value);
						}
						if (prodKindType.equals("B")) {
							housePlan_BLabel = new Label("L140MC1B.housePlan_B", value);
						}
						if (prodKindType.equals("C")) {
							housePlan_CLabel = new Label("L140MC1B.housePlan_C", value);
						}
						if (prodKindType.equals("D")) {
							housePlan_DLabel = new Label("L140MC1B.housePlan_D", value);
						}
						if (prodKindType.equals("E")) {
							housePlan_ELabel = new Label("L140MC1B.housePlan_E", value);
						}
						if (prodKindType.equals("F")) {
							housePlan_FLabel = new Label("L140MC1B.housePlan_F", value);
						}
					}
				}

//				String statusDescription = "";
//				if ("A".equals(prodKindType)) {
//					statusDescription = "<input type='radio' disabled='disabled' checked />符合 <input type='radio' disabled='disabled' />未符合";
//				} else if ( "B".equals(prodKindType)) {
//					statusDescription = "<input type='radio' disabled='disabled' />符合 <input type='radio' disabled='disabled' checked /><span style='color:red'>未符合(請留意辦理)</>";
//				} else if ("C".equals(prodKindType)) {
//					statusDescription = "<input type='radio' disabled='disabled' />符合 <input type='radio' disabled='disabled' checked /><span style='color:red'>未符合(非經報總處不得辦理)</>";
//				} else {
//					statusDescription = "";
//				}

//				Label statusLabel = new Label("status", statusDescription);
//				statusLabel.setEscapeModelStrings(false);
//				Label actionTypeLabel = new Label("actionType", prodKindType);

				//item.add(policyCodeLabel.add(new SimpleAttributeModifier("id", policyCode)));
				item.add(sourceLabel);
				item.add(conditionLabel);
				item.add(descLabel);
				item.add(housePlan_ALabel);
				item.add(housePlan_BLabel);
				item.add(housePlan_CLabel);
				item.add(housePlan_DLabel);
				item.add(housePlan_ELabel);
				item.add(housePlan_FLabel);

//				if (policyCode.startsWith("PH")) {
//					item.setVisible(false);
//				}
			}
		};
		return dataView;*/
	}
	private String getJobType2CodeValue(String code) {
		// code = jobType1;
		if (("01").equals(code)){
			code = "lms1205s01_jobType2a";
		} else if (("02").equals(code)) {
			code = "lms1205s01_jobType2b";
		} else if (("03").equals(code)) {
			code = "lms1205s01_jobType2c";
		} else if (("04").equals(code)) {
			code = "lms1205s01_jobType2d";
		} else if (("05").equals(code)) {
			code = "lms1205s01_jobType2e";
		} else if (("06").equals(code)) {
			code = "lms1205s01_jobType2f";
		} else if (("07").equals(code)) {
			code = "lms1205s01_jobType2g";
		} else if (("08").equals(code)) {
			code = "lms1205s01_jobType2h";
		} else if (("09").equals(code)) {
			code = "lms1205s01_jobType2i";
		} else if (("10").equals(code)) {
			code = "lms1205s01_jobType2j";
		} else if (("11").equals(code)) {
			code = "lms1205s01_jobType2k";
		} else if (("12").equals(code)) {
			code = "lms1205s01_jobType2l";
		} else if (("13").equals(code)) {
			code = "lms1205s01_jobType2m";
		} else if (("14").equals(code)) {
			code = "lms1205s01_jobType2n";
		} else {
			code = "";
		}
		return code;
	}
}
