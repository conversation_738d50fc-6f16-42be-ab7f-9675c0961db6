var pageAction = {
    handler: 'lms7850m01formhandler',
    grid: null,
    build: function(){
        pageAction.grid = $("#gridview").iGrid({
            handler: 'lms7850gridhandler',
            action: "queryL785m01a",
            width: 785,
            height: 350,
            sortname: 'updater|caseNo',
            sortorder: 'desc|asc',
            shrinkToFit: true,
            autowidth: false,
            postData: {
                mainDocStatus: viewstatus,
                rowNum: 15
            },
            rowNum: 15,
            //multiselect : true,
            colModel: [{
                colHeader: "oid",
                name: 'oid',
                hidden: true //是否隱藏
            }, {
                colHeader: "mainId",
                hidden: true, //是否隱藏
                name: 'mainId'
            }, {
                colHeader: "docURL",
                hidden: true, //是否隱藏
                name: 'docURL'
            }, {
                colHeader: i18n.lms7850v01["mainGrid.index3"], //經辦人員
                align: "left",
                width: 70,
                sortable: true,
                name: 'updater'
            }, {
                colHeader: i18n.lms7850v01["mainGrid.index4"], //查詢日期
                align: "center",
                width: 70,
                sortable: true,
                name: 'caseDate'
            }, {
                colHeader: i18n.lms7850v01["mainGrid.index5"], //案件號碼
                align: "left",
                width: 200,
                sortable: true,
                name: 'caseNo'
            }, {
                colHeader: i18n.lms7850v01["mainGrid.index8"], //篩選條件
                align: "left",
                width: 200,
                sortable: false,
                name: 'itemDscrShow'    	
            }],
            ondblClickRow: function(rowid){//同修改
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openBox(null, null, data);
            }
        });
        //build addThick selectes
        
        //build button 
        //新增
        $("#buttonPanel").find("#btnAdd").click(function(){
        	var url = '../lms/lms7850m01/00' ;
        	$.ajax({
                handler: pageAction.handler,
                data: {
                    formAction: "newl785m01a"
                },
                success: function(obj){
                	
                	$.form.submit({
                        url: url,
                        data: {
                            mainDocStatus: viewstatus,
                            mainId: obj.mainId,
                            mainOid: obj.oid,
                            docURL: obj.docURL,
                            oid: obj.oid
                        },
                        target: "_blank"
                    });
                	
            
                }
            });
        	
        }).end().find("#btnView").click(function(){
            //調閱	    
            var row = pageAction.grid.getGridParam('selrow');
            if (!row) {
                // action_004=請先選擇需「調閱」之資料列
                return CommonAPI.showMessage(i18n.def["action_004"]);
            }
            else {
                var result = $("#gridview").getRowData(row);
                pageAction.openBox(null, null, result);
            }
        }).end().find("#btnDelete").click(function(){
            //刪除
            var row = pageAction.grid.getGridParam('selrow');
            if (!row) {
                // includeId.selData=請選擇一筆資料!!
                return CommonAPI.showMessage(i18n.def["includeId.selData"]);
            }
            else {
                // confirmDelete=是否確定刪除?
                CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                    if (b) {
                        var result = $("#gridview").getRowData(row);
                        pageAction.startDel(null, null, result);
                    }
                });
            }
        });
    },
    /**
     * 開始刪除資料
     */
    startDel: function(cellvalue, options, rowObject){
        $.ajax({
            handler: pageAction.handler,
            action: 'startDel',
            data: {
                mainId: rowObject.mainId
            },
            success: function(response){
                // 更新Grid
                pageAction.reloadGrid();
            }
        });
    },
    /**
     * 開啟視窗
     */
    openBox: function(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        var url = '../lms/lms7850m01/00' ;
    	$.form.submit({
            url: url,
            data: {
                mainDocStatus: viewstatus,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                docURL: rowObject.docURL,
                oid: rowObject.oid
            },
            target: "_blank"
        });        
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var row = pageAction.grid.getGridParam('selrow');
        var data;
        if (row) {
            data = pageAction.grid.getRowData(row);
        }
        else {
            MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
        }
        return data;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                posinputata: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        }
        else {
            pageAction.grid.trigger('reloadGrid');
        }
    }
}

$(function(){
    pageAction.build();
    
});
