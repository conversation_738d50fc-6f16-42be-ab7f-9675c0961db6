/* 
 * LMS1815M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.form;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.eloandb.service.Dw_elf412ovsService;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.lrs.pages.LMS1815M01Page;
import com.mega.eloan.lms.lrs.service.LMS1815Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.model.L181M01A;
import com.mega.eloan.lms.model.L181M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 覆審控制檔交易
 * </pre>
 * 
 * @since 2011/9/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/22,irene,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1815formhandler")
@DomainClass(L181M01A.class)
public class LMS1815M01FormHandler extends AbstractFormHandler {

	@Resource
	LMSService lmsService;

	@Resource
	LMS1815Service service;

	@Resource
	BranchService branch;

	@Resource
	MisCustdataService LmsCustdataService;

	@Resource
	Lms412Service Lms412Service;

	@Resource
	TempDataService tempDataService;

	@Resource
	Dw_elf412ovsService dw412Service;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	DocCheckService docCheckService;

	@Resource
	RetrialService retrialService;

	static final String DATEFORMAT = "yyyy-MM-dd";

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult tempSave(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString("page"));

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));
		L181M01A l181m01a = (L181M01A) collectionData(params);

		if (page == 1) {
			result.set(EloanConstants.OID, l181m01a.getOid());
			result.set(EloanConstants.MAIN_OID, l181m01a.getOid());
			result.set(EloanConstants.MAIN_ID, l181m01a.getMainId());
		}

		return result;
	}

	private GenericBean collectionData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		Properties lms1815m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1815M01Page.class);
		String custid = "";
		int page = Util.parseInt(params.getString("page"));
		String oid = params.getString(EloanConstants.MAIN_OID);
		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);
		if (l181m01a == null) {
			l181m01a = new L181M01A();
		} else {
			custid = l181m01a.getCustId();
		}
		List<L181M01B> l181m01bList = service.findL180m02bByMainId(Util
				.trim(l181m01a.getMainId()));
		if (page == 1) {
			String formL181m01a = params.getString("L180M02AForm");
			JSONObject jsonL1815m01a = JSONObject.fromObject(formL181m01a);
			DataParse.toBean(jsonL1815m01a, l181m01a);
			// 畫面回傳回人員中文名
			l181m01a.setElfUpdater(user.getUserId());

			if (Util.isEmpty(Util.trim(l181m01a.getCustId()))) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("colName", lms1815m01.getProperty("custInfo"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", map), getClass());
			}
			l181m01a.setTypCd(TypCdEnum.海外.getCode());
			if (Util.isEmpty(oid) || Util.isEmpty(custid)) {
				service.saveNew(l181m01a, l181m01bList);
			} else {
				service.save(l181m01a);
			}
		} else if (page == 2) {
			List<L181M01B> l181m01bs = service.findL180m02bByMainId(l181m01a
					.getMainId());
			for (L181M01B l181m01b : l181m01bs) {
				if ("2".equals(Util.trim(l181m01b.getType()))) {
					String formL180m02b = params.getString("L180M02BForm");
					JSONObject jsonL1805m02b = JSONObject
							.fromObject(formL180m02b);
					DataParse.toBean(jsonL1805m02b, l181m01b);

					if (!Util.isEmpty(l181m01b.getElfRCkdLine())
							&& l181m01b.getElfRCkdLine().length() >= 2) {
						l181m01b.setElfRCkdLine(l181m01b.getElfRCkdLine()
								.substring(0, 1));
					}
					if (!l181m01b.getElfMDFlag().isEmpty()
							&& l181m01b.getElfMDFlag().length() >= 3) {
						l181m01b.setElfMDFlag(l181m01b.getElfMDFlag()
								.substring(0, 2).replace(".", ""));
					}
					if (!l181m01b.getElfNCkdFlag().isEmpty()
							&& l181m01b.getElfNCkdFlag().length() >= 3) {
						l181m01b.setElfNCkdFlag(l181m01b.getElfNCkdFlag()
								.substring(0, 2).replace(".", ""));
					}
					service.save(l181m01b);
				}
			}
		}

		return l181m01a;
	}

	/**
	 * 查詢
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryMain(PageParameters params)
			throws CapException {

		Properties abstractEloan = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Properties lms1815m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1815M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util
				.nullToSpace(params.getString(EloanConstants.MAIN_OID));
		int page = Util.parseInt(params.getString("page"));
		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);

		result.set(EloanConstants.OID, oid);
		result.set(EloanConstants.MAIN_OID, oid);
		result.set(EloanConstants.MAIN_DOC_STATUS,
				params.getString(EloanConstants.MAIN_DOC_STATUS));
		result.set("uCase", UtilConstants.DEFAULT.否);
		if (l181m01a != null) {
			result.set(EloanConstants.MAIN_ID, l181m01a.getMainId());
			result.set("titInfo",
					abstractEloan.getProperty("typCd." + l181m01a.getTypCd()));
			result.set("uCase", Util.trim(l181m01a.getUCase()));
			if ("0".equals(l181m01a.getDupNo())) {
				result.set("custInfo", Util.nullToSpace(l181m01a.getCustId())
						+ " " + Util.nullToSpace(l181m01a.getCustName()));
			} else {
				result.set("custInfo", Util.nullToSpace(l181m01a.getCustId())
						+ " " + Util.nullToSpace(l181m01a.getDupNo()) + " "
						+ Util.nullToSpace(l181m01a.getCustName()));
			}
		}
		switch (page) {
		case 1:
			if (l181m01a != null) {
				result.set(
						"typCdName",
						abstractEloan.getProperty("typCd."
								+ l181m01a.getTypCd()));
				result.set(
						"docStatusName",
						abstractEloan.getProperty("docStatus."
								+ l181m01a.getDocStatus()));
				IBranch ibranch = branch.getBranch(l181m01a.getElfBranch());
				if (ibranch != null) {
					result.set("elfBranchName", ibranch.getBrName());
				}
				result.set(
						"creator",
						Util.trim(l181m01a.getCreator())
								+ " "
								+ Util.trim(lmsService.getUserName(l181m01a
										.getCreator())) + "("
								+ TWNDate.toFullAD(l181m01a.getCreateTime())
								+ ")");
				result.set(
						"updater",
						Util.trim(l181m01a.getUpdater())
								+ " "
								+ Util.trim(lmsService.getUserName(l181m01a
										.getUpdater())) + "("
								+ TWNDate.toFullAD(l181m01a.getUpdateTime())
								+ ")");
				result.set("apprId", Util.trim(lmsService.getUserName(l181m01a
						.getCreator())));
				if (!Util.isEmpty(l181m01a.getApprover())) {
					result.set("approverCN", Util.trim(lmsService
							.getUserName(l181m01a.getApprover())));
				}
				CapAjaxFormResult l180m02aForm = DataParse.toResult(l181m01a);
				if (!Util.isEmpty(Util.trim(l181m01a.getElfCState()))) {
					l180m02aForm.set("cState",
							lms1815m01.get("cstate" + l181m01a.getElfCState())
									.toString());
				}
				result.set("L180M02AForm", l180m02aForm);
			}

			break;
		case 2:
			if (l181m01a != null) {
				List<L181M01B> l180m02bs = service
						.findL180m02bByMainId(l181m01a.getMainId());
				for (L181M01B l180m02b : l180m02bs) {
					// 主管機關指定覆審案件
					l180m02b.setElfUCkdLINE(Util.trim(l180m02b.getElfUCkdLINE()));
					// 主要授信戶
					l180m02b.setElfMainCust(Util.trim(l180m02b.getElfMainCust()));
					// DBUOBU是否有共管
					l180m02b.setElfDBUOBU(Util.trim(l180m02b.getElfDBUOBU()));
					if ("1".equals(Util.trim(l180m02b.getType()))) {
						result.set("result1", DataParse.toResult(l180m02b));
					} else {
						l180m02b.setElfCrdTTbl(Util.trim(l180m02b
								.getElfCrdTTbl()));
						l180m02b.setElfMowTbl1(Util.trim(l180m02b
								.getElfMowTbl1()));
						if (l180m02b.getElfNewDate() != null) {
							l180m02b.setElfNewDate(Util.trim(l180m02b
									.getElfNewDate()));
						}
						result.set("L180M02BForm", DataParse.toResult(l180m02b));
					}
				}
			}
			break;
		}
		result.set("page", page);
		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult newMain(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		L181M01A l181m01a = new L181M01A();
		l181m01a.setMainId(IDGenerator.getUUID());
		l181m01a.setTypCd(TypCdEnum.海外.getCode());
		l181m01a.setDeletedTime(CapDate.getCurrentTimestamp());

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		l181m01a.setCtlType(LrsUtil.CTLTYPE_主辦覆審);
		service.save(l181m01a);

		result.set(EloanConstants.MAIN_OID, l181m01a.getOid());
		result.set(EloanConstants.MAIN_ID, l181m01a.getMainId());
		result.set(EloanConstants.MAIN_DOC_STATUS, l181m01a.getDocStatus());

		return result;
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		Properties abstractEloan = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Properties lms1815m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1815M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString("page"));
		String oid = Util
				.nullToSpace(params.getString(EloanConstants.MAIN_OID));

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);
		String custid = "";
		if (l181m01a == null) {
			l181m01a = new L181M01A();
		} else {
			custid = l181m01a.getCustId();
		}
		List<L181M01B> l181m01bList = service.findL180m02bByMainId(Util
				.trim(l181m01a.getMainId()));
		switch (page) {
		case 1:
			String formL181m01a = params.getString("L180M02AForm");
			JSONObject jsonL1815m01a = JSONObject.fromObject(formL181m01a);
			jsonL1815m01a.remove("createTime");
			// DataParse.toBean(jsonL1815m01a, l181m01a);
			l181m01a.setUCase(jsonL1815m01a.getString("uCase"));
			l181m01a.setUCaseRole(jsonL1815m01a.getString("uCaseRole"));
			l181m01a.setTypCd(TypCdEnum.海外.getCode());
			l181m01a.setElfUpdater(user.getUserId());
			if (Util.isEmpty(Util.trim(l181m01a.getCustId()))) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("colName", lms1815m01.getProperty("custInfo"));
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", map), getClass());
			}
			l181m01a.setDeletedTime(null);
			if (Util.isEmpty(oid) || Util.isEmpty(custid)) {
				service.saveNew(l181m01a, l181m01bList);// 新增L181M01B的資料的地方
			} else {
				service.save(l181m01a);
			}
			CapAjaxFormResult l181m01aResult = DataParse.toResult(l181m01a);
			if (!Util.isEmpty(Util.trim(l181m01a.getElfCState()))) {
				l181m01aResult.set("cState",
						lms1815m01.get("cstate" + l181m01a.getElfCState())
								.toString());
			}
			result = l181m01aResult;

			result.set("typCdName",
					abstractEloan.getProperty("typCd." + l181m01a.getTypCd()));
			result.set(
					"docStatusName",
					abstractEloan.getProperty("docStatus."
							+ l181m01a.getDocStatus()));
			IBranch ibranch = branch.getBranch(l181m01a.getElfBranch());
			if (ibranch != null) {
				result.set("elfBranchName", ibranch.getBrName());
			}

			result.set(EloanConstants.MAIN_OID, l181m01a.getOid());
			result.set(EloanConstants.MAIN_DOC_STATUS, l181m01a.getDocStatus());

			result.set("titInfo",
					abstractEloan.getProperty("typCd." + l181m01a.getTypCd()));
			if ("0".equals(l181m01a.getDupNo())) {
				result.set("custInfo",
						l181m01a.getCustId() + " " + l181m01a.getCustName());
			} else {
				result.set("custInfo",
						l181m01a.getCustId() + " " + l181m01a.getDupNo() + " "
								+ l181m01a.getCustName());
			}
			result.set(
					"creator",
					(!Util.isEmpty(lmsService.getUserName(l181m01a.getCreator())) ? lmsService
							.getUserName(l181m01a.getCreator()) : l181m01a
							.getCreator())
							+ (Util.isEmpty(l181m01a.getCreateTime()) ? ""
									: "("
											+ CapDate.parseToString(l181m01a
													.getCreateTime()) + ")"));
			result.set(
					"updater",
					(!Util.isEmpty(lmsService.getUserName(l181m01a.getUpdater())) ? lmsService
							.getUserName(l181m01a.getUpdater()) : l181m01a
							.getUpdater())
							+ (Util.isEmpty(l181m01a.getUpdateTime()) ? ""
									: "("
											+ CapDate.parseToString(l181m01a
													.getUpdateTime()) + ")"));

			break;
		case 2:
			l181m01a.setDeletedTime(null);
			List<L181M01B> l181m01bs = service.findL180m02bByMainId(l181m01a
					.getMainId());
			for (L181M01B l181m01b : l181m01bs) {
				if ("2".equals(Util.trim(l181m01b.getType()))) {
					String formL180m02b = params.getString("L180M02BForm");
					JSONObject jsonL1805m02b = JSONObject
							.fromObject(formL180m02b);
					String elfNewDate = Util.trim(jsonL1805m02b
							.get("elfNewDate"));
					if (!Util.isEmpty(elfNewDate) && elfNewDate.length() > 5) {
						int year = Integer.valueOf(elfNewDate.substring(0, 4));
						int month = Integer.valueOf(elfNewDate.substring(4));
						if (elfNewDate.length() != 6 || year < 1 || month < 1
								|| month > 12) {
							throw new CapMessageException(
									lms1815m01.getProperty("err.formatNewDate"),
									getClass());
						}
					}
					DataParse.toBean(jsonL1805m02b, l181m01b);

					if (!l181m01b.getElfRCkdLine().isEmpty()) {
						if (l181m01b.getElfRCkdLine().length() >= 1) {
							String elfRCkdLine = l181m01b.getElfRCkdLine()
									.substring(0, 1);
							l181m01b.setElfRCkdLine(elfRCkdLine);

						}
					}
					if (!l181m01b.getElfMDFlag().isEmpty()) {
						if (l181m01b.getElfMDFlag().length() >= 2) {
							l181m01b.setElfMDFlag(l181m01b.getElfMDFlag()
									.substring(0, 2).replace(".", ""));
						}
					}
					if (!l181m01b.getElfNCkdFlag().isEmpty()) {
						if (l181m01b.getElfNCkdFlag().length() >= 2) {
							l181m01b.setElfNCkdFlag(l181m01b.getElfNCkdFlag()
									.substring(0, 2).replace(".", ""));
						}
					}

					if (!"8".equals(l181m01b.getElfNCkdFlag())) {
						l181m01b.setElfNextNwDt(null);
					}

					service.save(l181m01b, l181m01a);
				}
			}
			break;
		default:
			l181m01a.setDeletedTime(null);
			service.save(l181m01a);
			break;
		}

		if (params.getAsBoolean("showMsg", true)) {
			// EFD0017=儲存成功
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0017"));
		}
		return result;
	}

	/**
	 * 上刪除註記
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		String oid = Util.nullToSpace(params.getString("list"));
		return _delData(oid, true);
	}

	/**
	 * 刪除
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteMain(PageParameters params)
			throws CapException {
		String oid = Util.nullToSpace(params.getString("list"));
		return _delData(oid, false);
	}

	private IResult _delData(String oid, boolean justOnDelTime)
			throws CapMessageException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);
		if (l181m01a != null) {
			List<DocOpener> docOpeners = docCheckService.findByMainId(l181m01a
					.getMainId());
			for (DocOpener docOpener : docOpeners) {
				if (OpenTypeCode.Writing.getCode().equals(
						docOpener.getOpenType())) {
					HashMap<String, String> hm = new HashMap<String, String>();
					hm.put("userId", docOpener.getOpener());
					hm.put("userName",
							userInfoService.getUserName(docOpener.getOpener()));
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0009", hm), getClass());
				}
			}

			if (justOnDelTime) {
				l181m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				service.save(l181m01a);
			} else {
				service.deleteL180m02a(oid);
			}
		}
		// EFD0019=刪除成功
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));

		return result;

	}

	/**
	 * 查詢借款人
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult queryCustId(PageParameters params)
			throws CapException {

		Properties lms1815m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1815M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID, "");
		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);
		String branchId = params.getString("branchId");
		branchId = branchId.toUpperCase();
		String custId = params.getString("custId");
		String dupNo = params.getString("dupNo");
		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		// 海外只有主辦
		String ctlType = Util.trim(params.getString("ctlType",
				LrsUtil.CTLTYPE_主辦覆審));
		custId = custId.toUpperCase();
		IBranch ibranch = branch.getBranch(branchId);
		if (ibranch != null) {
			String b = branch.getBranch(branchId).getUnitType();
			// P海外分行(當地有總行)Q海外總行(澳洲/加拿大)R海外總行(泰國)
			if (BranchTypeEnum.海外分行.getCode().equals(b) || "P".equals(b)
					|| "Q".equals(b) || "R".equals(b)) {

				L181M01A oldl180m02a = service.fingL180m02aByBranch(branchId,
						custId, dupNo, ctlType);
				// 做檢核，資料庫是否已有這筆資料
				if (oldl180m02a != null
						&& branchId.equals(oldl180m02a.getElfBranch())
						&& (RetrialDocStatusEnum.編製中.getCode().equals(
								oldl180m02a.getDocStatus()) || RetrialDocStatusEnum.待覆核
								.getCode().equals(oldl180m02a.getDocStatus()))) {
					throw new CapMessageException(
							lms1815m01.getProperty("alreadyHave"), getClass());
				}

				List<Map<String, Object>> rows = Lms412Service
						.findLms412ByCustId(branchId, custId, dupNo);
				if (rows.size() == 0) {
					result.set("sucess", "N");
				} else {
					for (Map<String, Object> lms412 : rows) {
						if (l181m01a == null) {
							l181m01a = new L181M01A();
						}
						l181m01a.setUCase(Util.trim(lms412.get("UCASE")));
						l181m01a.setUCaseRole(Util.trim(lms412.get("UCASEROLE")));
						l181m01a.setTypCd(TypCdEnum.海外.name());
						l181m01a.setCustId(Util.trim(lms412.get("CUSTID")));
						l181m01a.setDupNo(Util.trim(lms412.get("DUPNO")));
						l181m01a.setCustName(Util.trim(lms412.get("CNAME")));

						l181m01a.setElfBranch(branchId);
						l181m01a.setElfDataDt((Date) lms412.get("DATADT"));
						l181m01a.setElfLLRDate((Date) lms412.get("LLRDATE"));
						l181m01a.setElfOCkdLine(Util.trim(lms412
								.get("OCKDLINE")));
						l181m01a.setElfCState(Util.trim(lms412.get("CSTATE")));
						l181m01a.setElfCancelDt((Date) lms412.get("CANCELDT"));
						l181m01a.setElfUpdDate((Date) lms412.get("UPDDATE"));
						l181m01a.setElfUpdater(Util.trim(lms412.get("UPDATER")));
						l181m01a.setElfTmeStamp((Date) lms412.get("TMESTAMP"));

						result = DataParse.toResult(l181m01a);
						if (!Util.isEmpty(l181m01a.getElfCState())) {
							result.set(
									"cState",
									lms1815m01.getProperty("cstate"
											+ l181m01a.getElfCState()));
						}
						result.set("sucess", "Y");
					}
				}
			}
		} else {
			// 輸入分行錯誤(無此分行)
			throw new CapMessageException(
					lms1815m01.getProperty("wrongBranch"), getClass());
		}
		return result;
	}

	@DomainAuth(AuthType.Modify)
	public IResult queryCustData(PageParameters params)
			throws CapException {
		Properties abstractEloan = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String branchId = params.getString("branchId");
		branchId = branchId.toUpperCase();
		String custId = params.getString("custId");
		String dupno = params.getString("dupNo");
		List<Map<String, Object>> list = LmsCustdataService.findCustDataCname(
				custId, dupno);
		Map<String, Object> custData = null;
		if (!Util.isEmpty(list) && list.size() > 0) {
			custData = list.get(0);
		}
		L181M01A l180m02a = new L181M01A();
		if (custData != null) {
			l180m02a.setCustId(custId);
			l180m02a.setDupNo(dupno);
			l180m02a.setCustName(Util.trimSpace((String) custData.get("CNAME")));
			l180m02a.setElfBranch(branchId);
		} else {
			l180m02a.setCustId(custId);
			l180m02a.setDupNo(dupno);
			l180m02a.setElfBranch(branchId);
		}
		CapAjaxFormResult l180m02aForm = DataParse.toResult(l180m02a);
		l180m02aForm.set("typCdName", abstractEloan.getProperty("typCd.5"));
		result.set("L180M02AForm", l180m02aForm);
		result.set("ElfBranchName", Util.trim(branch.getBranchName(branchId)));
		return result;
	}

	/**
	 * 試算下次覆審日
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult cauNextDate(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);

		L181M01A l180m02a = service.findModelByOid(L181M01A.class, oid);

		Date nextDate = new Date();
		if ("02".equals(params.getString("page"))) {
			Map<String, Object> map = new HashMap<String, Object>();
			String formL181m01b = params.getString("L180M02BForm");
			JSONObject jsonL181m01b = JSONObject.fromObject(formL181m01b);
			map.put("NCKDFLAG",
					(Util.isEmpty(jsonL181m01b.getString("elfNCkdFlag")) ? ""
							: jsonL181m01b.getString("elfNCkdFlag").substring(
									0, 1)));
			String lrdate = "0001-01-01";
			if (!Util.isEmpty(jsonL181m01b.get("elfLRDate"))) {
				lrdate = Util.addZeroWithValue(
						jsonL181m01b.getString("elfLRDate"), 10);
			}
			map.put("LRDATE", CapDate.parseDate(lrdate));
			map.put("MAINCUST", jsonL181m01b.getString("elfMainCust"));
			map.put("MOWTBL1", jsonL181m01b.getString("elfMowTbl1"));
			map.put("MOWTYPE", jsonL181m01b.getString("elfMowType"));
			map.put("CRDTTBL", jsonL181m01b.getString("elfCrdTTbl"));
			map.put("NEWDATE", jsonL181m01b.getString("elfNewDate"));
			map.put("MDFLAG",
					(Util.isEmpty(jsonL181m01b.getString("elfMDFlag")) ? ""
							: jsonL181m01b.getString("elfMDFlag").substring(0,
									1)));
			map.put("RCKDLINE",
					(Util.isEmpty(jsonL181m01b.getString("elfRCkdLine")) ? ""
							: jsonL181m01b.getString("elfRCkdLine").substring(
									0, 1)));
			map.put("MDDT",
					CapDate.parseDate(jsonL181m01b.getString("elfMDDt")));
			map.put("UCKDLINE", jsonL181m01b.getString("elfUCkdLINE"));
			map.put("UCKDDT",
					CapDate.parseDate(jsonL181m01b.getString("elfUCkdDt")));

			// J-108-0078_05097_B1001 Web e-Loan企金授信覆審系統修改首次往來之新授信戶應辦理覆審之期限
			map.put("ISALLNEW", jsonL181m01b.getString("elfIsAllNew"));
			map.put("BRANCH",
					l180m02a != null ? l180m02a.getElfBranch() : user
							.getUnitNo());

			nextDate = service.cauNextDate(map);
		} else {
			nextDate = service.cauNextDate(oid);
		}
		if (CapDate.parseDate("0001-01-01").equals(nextDate)) {
			nextDate = CapDate.getCurrentTimestamp();
		}
		result.set("nextDate", nextDate);
		return result;
	}

	/**
	 * 整批覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Accept)
	public IResult flowCases(PageParameters params)
			throws CapException {

		CapAjaxFormResult r = new CapAjaxFormResult();
		String[] oids = params.getStringArray("list");
		Map<String, Object> successOrNot = service.flowCases(oids,
				params.getBoolean("flowAction"));
		if (successOrNot.get("success") == null) {
			successOrNot.put("success", false);
		}
		if ("Y".equals(Util.trim(successOrNot.get("special")))
				&& (Boolean) successOrNot.get("success")) {
			// 執行成功
			r.set(CapConstants.AJAX_NOTIFY_MESSAGE,
					RespMsgHelper.getMainMessage("EFD0018")
							+ "<BR/>"
							+ RespMsgHelper.getMainMessage("EFD0053"));
		} else if ("Y".equals(Util.trim(successOrNot.get("special")))) {
			// 執行成功
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		} else if ((Boolean) successOrNot.get("success")) {
			// 執行成功
			r.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage("EFD0018"));
		} else {
			// 失敗分行
			throw new CapMessageException(
					(String) successOrNot.get("failBank"), getClass());
		}
		return r;
	}

	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult checkMain(PageParameters params)
			throws CapException {

		Properties lms1815m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1815M01Page.class);
		CapAjaxFormResult r = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);
		// 儲存
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		if (l181m01a == null) {
			l181m01a = new L181M01A();
		}
		// 做檢核
		List<L181M01B> l181m01bs = service.findL180m02bByMainId(l181m01a
				.getMainId());
		for (L181M01B l181m01b : l181m01bs) {
			if ("2".equals(Util.trim(l181m01b.getType()))) {
				// 主管機關
				if (UtilConstants.DEFAULT.是.equals(Util.trim(l181m01b
						.getElfUCkdLINE()))
						&& Util.isEmpty(l181m01b.getElfUCkdDt())) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check8"), getClass());
				} else if ((!UtilConstants.DEFAULT.是.equals(Util.trim(l181m01b
						.getElfUCkdLINE())))
						&& Util.isNotEmpty(l181m01b.getElfUCkdDt())) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check12"), getClass());
				}
				// 週期
				String rckdline = Util.trim(l181m01b.getElfRCkdLine());
				Date minDate = CapDate.parseDate("0001-01-01");
				Date lrDate = l181m01b.getElfLRDate();
				if (rckdline == null || rckdline.isEmpty()) {
					throw new CapMessageException(
							lms1815m01.getProperty("noElfRCkdLine"), getClass());
					// 覆審週期為A
				} else if ("A".equals(rckdline)) {
					// 上次覆審日(除E、G不需檢查，其他都要檢查)
					if (lrDate == null || minDate.equals(lrDate)) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfLRDate"),
								getClass());
					}
					// 覆審週期為B 主要授信戶
				} else if ("B".equals(rckdline)) {
					if (lrDate == null || minDate.equals(lrDate)) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfLRDate"),
								getClass());
						// 主要授信戶
					} else if (Util.isEmpty(l181m01b.getElfMainCust())) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfMainCust"),
								getClass());
					}
					// 覆審週期為C 新作增額相關欄位需填寫
				} else if ("C".equals(rckdline)) {
					if (l181m01b.getElfNewAdd() == null
							|| Util.trim(l181m01b.getElfNewAdd()).isEmpty()) {
						throw new CapMessageException(
								lms1815m01.getProperty("noNewAdd"), getClass());
						// 新作/增額年月
					} else if (Util.equals(l181m01b.getElfNewDate(), "")
							|| minDate.equals(l181m01b.getElfNewDate())) {
						throw new CapMessageException(
								lms1815m01.getProperty("noNewDate"), getClass());
					} else if (Util.equals(l181m01b.getElfIsAllNew(), "")) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfIsAllNew"),
								getClass());
					} else if (Util.equals(l181m01b.getElfIsAllNew(), "Y")) {
						throw new CapMessageException(
								lms1815m01.getProperty("err.check13") + "「I」",
								getClass());
					}
					// I 首次往來之新貸戶
					// J-108-0078_05097_B1001
					// 配合授審處E-LOAN企金、消金「授信覆審系統」修改首次往來之新授信戶(下稱純新貸戶)應辦理覆審之期限如下修改內容。
				} else if ("I".equals(rckdline)) {
					if (l181m01b.getElfNewAdd() == null
							|| Util.trim(l181m01b.getElfNewAdd()).isEmpty()) {
						throw new CapMessageException(
								lms1815m01.getProperty("noNewAdd"), getClass());
						// 新作/增額年月
					} else if (Util.equals(l181m01b.getElfNewDate(), "")
							|| minDate.equals(l181m01b.getElfNewDate())) {
						throw new CapMessageException(
								lms1815m01.getProperty("noNewDate"), getClass());
					} else if (Util.equals(l181m01b.getElfIsAllNew(), "")) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfIsAllNew"),
								getClass());
					} else if (Util.notEquals(l181m01b.getElfIsAllNew(), "Y")) {
						throw new CapMessageException(
								lms1815m01.getProperty("err.check13") + "「C」",
								getClass());
					}
					// 覆審週期為D F
				} else if ("D".equals(rckdline) || "F".equals(rckdline)) {
					if (lrDate == null || minDate.equals(lrDate)) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfLRDate"),
								getClass());
						// 異常通報代碼
					} else if (l181m01b.getElfMDFlag() == null
							|| Util.trim(l181m01b.getElfMDFlag()).isEmpty()) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfMDFlag"),
								getClass());
						// 異常通報日期
					} else if (l181m01b.getElfMDDt() == null
							|| minDate.equals(l181m01b.getElfMDDt())) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfMDDt"), getClass());
					}
					// 覆審週期為E G
				} else if ("E".equals(rckdline) || "F".equals(rckdline)) {
					// 異常通報代碼
					if (l181m01b.getElfMDFlag() == null
							|| Util.trim(l181m01b.getElfMDFlag()).isEmpty()) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfMDFlag"),
								getClass());
						// 異常通報日期
					} else if (l181m01b.getElfMDDt() == null
							|| minDate.equals(l181m01b.getElfMDDt())) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfMDDt"), getClass());
					}
					// 覆審週期為H
				} else if ("H".equals(rckdline)) {
					if (lrDate == null || minDate.equals(lrDate)) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfLRDate"),
								getClass());
						// 主管機關指定覆審案件
					} else if (Util.isEmpty(l181m01b.getElfUCkdLINE())) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfUCkdLINE"),
								getClass());
						// 主管機關通知日期
					} else if (l181m01b.getElfUCkdDt() == null
							|| minDate.equals(l181m01b.getElfUCkdDt())) {
						throw new CapMessageException(
								lms1815m01.getProperty("noElfUCkdDt"),
								getClass());
					}
				}
				// 信用評等
				if (!Util.isEmpty(Util.trim(l181m01b.getElfMowType()))
						&& Util.isEmpty(Util.trim(l181m01b.getElfMowTbl1()))) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check1"), getClass());
				} else if (!Util.isEmpty(Util.trim(l181m01b.getElfMowTbl1()))
						&& Util.isEmpty(Util.trim(l181m01b.getElfMowType()))) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check2"), getClass());
				}
				// 新作/增額/逾放轉正
				if (Util.isNotEmpty(l181m01b.getElfNewAdd())
						&& Util.isEmpty(l181m01b.getElfNewDate())) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check3"), getClass());
				} else if (Util.isNotEmpty(l181m01b.getElfNewDate())
						&& Util.isEmpty(l181m01b.getElfNewAdd())) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check4"), getClass());
				}
				// 異常通報
				if (Util.isNotEmpty(l181m01b.getElfMDFlag())
						&& Util.isEmpty(l181m01b.getElfMDDt())) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check5"), getClass());
				} else if (Util.isEmpty(l181m01b.getElfMDFlag())
						&& !(Util.isEmpty(l181m01b.getElfMDDt()) || Util
								.isEmpty(Util.trim(l181m01b.getElfProcess())))) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check6"), getClass());
				}
				// 不覆審代碼
				if (Util.isEmpty(l181m01b.getElfNCkdFlag())
						&& !Util.isEmpty(Util.trim(l181m01b.getElfNCkdMemo()))) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check7"), getClass());
				} else if ("7".equals(l181m01b.getElfNCkdFlag())) {
					String status = service.findCstate(l181m01a);
					if (!Util.isEmpty(status)) {
						throw new CapMessageException(
								lms1815m01.getProperty("err.check9"),
								getClass());
					}
				} else if ("8".equals(l181m01b.getElfNCkdFlag())
						&& Util.isEmpty(l181m01b.getElfNextNwDt())) {
					throw new CapMessageException(
							lms1815m01.getProperty("err.check10"), getClass());
				}
			}
		}

		r.set("success", true);

		return r;
	}

	/**
	 * 呈主管覆核
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {

		CapAjaxFormResult r = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);
		// 檢查經辦和主管是否為同一人
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean decition = params.getAsBoolean("flowAction", false);
		if (decition && user.getUserId().equals(l181m01a.getUpdater())) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}

		// 跑流程
		if (l181m01a != null) {
			try {
				service.flowAction(l181m01a.getOid(), l181m01a,
						params.containsKey("flowAction"), decition);
			} catch (Throwable t1) {
				logger.error("LMS1815M01FormHandler flowAction EXCEPTION!!", t1);
			}
		}
		return r;
	}

	public IResult queryBranch(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		TreeMap<String, String> tm = retrialService.getBranch(user.getUnitNo());

		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("item", new CapAjaxFormResult(tm));
		result.set("itemOrder", new ArrayList<String>(tm.keySet()));
		return result;
	}

	/**
	 * 戶況
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult findCstate(PageParameters params)
			throws CapException {

		Properties lms1815m01 = MessageBundleScriptCreator
				.getComponentResource(LMS1815M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID, "");
		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);
		if (l181m01a != null) {
			String status = service.findCstate(l181m01a);
			if (!Util.isEmpty(status)) {
				result.set("cState", lms1815m01.get("cstate" + status)
						.toString());
				result.set("elfCState", status);
				result.set("returnVal", lms1815m01.get("cstate" + status)
						.toString());
				result.set("elfCancelDt", "");
			} else {
				result.set("cState", lms1815m01.get("cstate0").toString());
				result.set("elfCState", "0");
			}
		} else {
			throw new CapMessageException(lms1815m01.getProperty("noL181M01A"),
					getClass());
		}
		return result;
	}

	/**
	 * 異動更新為銷戶預設值
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult resetCstatus(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID, "");
		L181M01A l181m01a = service.findModelByOid(L181M01A.class, oid);
		if (l181m01a != null) {
			List<L181M01B> l181m01bs = service.findL180m02bByMainId(l181m01a
					.getMainId());
			for (L181M01B l181m01b : l181m01bs) {
				if ("2".equals(l181m01b.getType())) {
					l181m01b.setElfRCkdLine("A");
					l181m01b.setElfUCkdLINE(null);
					l181m01b.setElfUCkdDt(null);
					l181m01b.setElfMDFlag(null);
					l181m01b.setElfMDDt(null);
					l181m01b.setElfProcess(null);
					l181m01b.setElfNewDate(null);
					l181m01b.setElfNewAdd(null);
					l181m01b.setElfNCkdFlag("7");
					l181m01b.setElfNCkdDate(l181m01a.getElfCancelDt());
					service.save(l181m01b);
					if ("02".equals(params.getString("page"))) {
						if (!Util.isEmpty(Util.trim(l181m01b.getElfRCkdLine()))) {
							Map<String, String> map = codeTypeService
									.findByCodeType("lms1815m01_elfRCkdLine");
							if (!Util.isEmpty(map)) {
								l181m01b.setElfRCkdLine(map.get(Util
										.trim(l181m01b.getElfRCkdLine())));
							}
						}
						if (!Util.isEmpty(Util.trim(l181m01b.getElfNCkdFlag()))) {
							Map<String, String> map = codeTypeService
									.findByCodeType("lms1815m01_elfNCkdFlag");
							if (!Util.isEmpty(map)) {
								l181m01b.setElfNCkdFlag(map.get(Util
										.trim(l181m01b.getElfNCkdFlag())));
							}
						}
						if (!Util.isEmpty(Util.trim(l181m01b.getElfMDFlag()))) {
							Map<String, String> map = codeTypeService
									.findByCodeType("lms1815m01_elfMDFlag");
							if (!Util.isEmpty(map)) {
								l181m01b.setElfMDFlag(map.get(Util
										.trim(l181m01b.getElfMDFlag())));
							}
						}
						result = DataParse.toResult(l181m01b);
					}
				}
			}
		}
		return result;
	}
}
