package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.model.C900M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

@Controller
@RequestMapping(path = "/fms/cls9061m01/{page}")
public class CLS9061M01Page extends AbstractEloanForm {

	public CLS9061M01Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {	
		new DocLogPanel("_docLog").processPanelData(model, params);
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		addAclLabel(model, new AclLabel("_btnSave", params, getDomainClass(),
				AuthType.Modify	, FlowDocStatusEnum.編製中));
		
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
				AuthType.Accept, FlowDocStatusEnum.待覆核));
		
		//總處單位，才可退回編製中
		if(Util.trim(user.getUnitNo()).startsWith("9")){
			addAclLabel(model, new AclLabel("_btnAPPROVED", params, getDomainClass(),
					AuthType.Modify, FlowDocStatusEnum.已核准));	
		}else{
			// UPGRADE: 前端須配合改Thymeleaf的樣式
			// Label o = new Label("_btnAPPROVED", "");
			// o.setVisible(false);
			// add(o);
			model.addAttribute("_btnAPPROVED", false);

		}	
		
		renderJsI18N(CLS9061M01Page.class);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {		
		return C900M01E.class;
	}
}
