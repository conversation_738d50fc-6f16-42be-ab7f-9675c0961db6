/* 
 * MisEJF419Service.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * MIS.BAM087 ->MIS.EJV41901
 * </pre>
 * 
 * @since 2011/11/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/11,CP,new
 *          </ul>
 */
public interface MisEJF419Service {

	/**
	 * MIS.BAM087 <li>0-BANID-VARCHAR(11) <li>1-ID-VARCHAR(11) <li>
	 * 2-PRODID-VARCHAR(2) <li>
	 * 3-DATA_YYY-Char(3)-資料年度 <li>4-DATA_MM-Char(2)-資料月份 <li>
	 * 5-BANK_CODE-Char(7)-行庫代號 <li>6-BANK_NAME-Char(40)-行庫名稱 <li>
	 * 7-ACCOUNT_CODE-Char(1)-科目別(對照表) <li>
	 * 8-ACCOUNT_CODE2-Char(1)-科目別註記(對照表) <li>
	 * 9-PURPOSE_CODE-Char(1)-用途別(對照表) <li>
	 * 10-CONTRACT_AMT1-Num(10)-綜合額度金額(千元) <li>
	 * 11-CONTRACT_AMT-Num(10)-分項額度金額(千元) <li>12-LOAN_AMT-Num(10)-未逾期金額(千元) <li>
	 * 13-PASS_DUE_AMT-Num(10)-逾期未還金額(千元) <li>
	 * 14-PAY_CODE_12-Char(12)-12期還款記錄 <li>15-IS_KIND-Char(2)-擔保品類別(對照表) <li>
	 * 16-PROJECT_CODE-Char(2)-政府專案貸款分類代碼(對照表) <li>
	 * 17-CO_LOAN-Char(1)-共同借款註記(*表有共同借款) <li>
	 * 18-UN_MARK-Char(1)-聯貸註記(A表國內,B表國際聯貸) <li>
	 * 19-U_YYYMMDD-Char(8)-聯貸日期西元YYYYMMDD <li>20-U_RATE-Num(3)-參貸比例 <li>
	 * 21-IB_MARK-Char(1)-資金流向註記(*表流向非法人組織) <li>
	 * 22-IAB_BAN-Char(8)-資金流向非法人組織統編 <li>23-IAB_NAME-Char(60)-資金流向非法人組織名稱 <li>
	 * 24-CONTRACT_MARK-Char(1)-額度特別註記(*表最高階額度所有者ID不同) <li>
	 * 25-CONTRACT_CODE-Char(50)-本階額度代碼 <li>
	 * 26-CONTRACT_CODE1-Char(50)-最高階額度代碼 <li>
	 * 27-CON_BAN-Char(10)-最高階額度所屬公司統編 <li>28-CON_NAME-Char(60)-最高階額度所屬公司名稱 <li>
	 * 29-ACT_Y_MARK-Char(1)-Ｙ科目之額度註記(*表有現金卡日報資料) <li>
	 * 30-CONTRACT_AMT_Y-Num(10)-現金卡日報Ｙ科目之可動用額度(千元) <li>31-QDATE-VARCHAR(9) <li>
	 * 32-QEMPCODE-VARCHAR(11) <li>33-QEMPNAME-VARCHAR(20) <li>
	 * 34-QBRANCH-VARCHAR(3)
	 */
	final String[] LnunidCols = { "BANID", "ID", "PRODID", "DATA_YYY",
			"DATA_MM", "BANK_CODE", "BANK_NAME", "ACCOUNT_CODE",
			"ACCOUNT_CODE2", "PURPOSE_CODE", "CONTRACT_AMT1", "CONTRACT_AMT",
			"LOAN_AMT", "PASS_DUE_AMT", "PAY_CODE_12", "IS_KIND",
			"PROJECT_CODE", "CO_LOAN", "UN_MARK", "U_YYYMMDD", "U_RATE",
			"IB_MARK", "IAB_BAN", "IAB_NAME", "CONTRACT_MARK", "CONTRACT_CODE",
			"CONTRACT_CODE1", "CON_BAN", "CON_NAME", "ACT_Y_MARK",
			"CONTRACT_AMT_Y", "QDATE", "QEMPCODE", "QEMPNAME", "QBRANCH" };

	/**
	 * 取得聯徵資料庫全體金融機構借款餘額
	 * 
	 * @param custId
	 *            String 10碼
	 * @param prodid
	 *            String 2碼
	 * @param yyyMMdd
	 *            String 民國年月日 9碼 yyy/MM/dd
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> getELLNF(String custId, String prodid,
			String yyyMMdd);

	/**
	 * 取得聯徵資料庫過去一年是否動用現金卡循環額度
	 * 
	 * @param custId
	 *            String 10碼
	 * @param prodid
	 *            String 2碼
	 * @param yyyMMdd
	 *            String 民國年月日 9碼 yyy/MM/dd
	 * @return int
	 */
	int countAccountCodeYAmtPos(String custId, String prodid, String yyyMMdd);

	/**
	 * 聯徵資料庫全體金融機構借款資料年度月份
	 * 
	 * @param custId
	 *            String 10碼
	 * @param prodid
	 *            String 2碼
	 * @param yyyMMdd
	 *            String 民國年月日 9碼 yyy/MM/dd
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> getELLNFYYYYMM(String custId, String prodid,
			String yyyMMdd);

	/**
	 * 聯徵資料庫申貸戶之主債務資料
	 * 
	 * @param custId
	 *            String
	 * @param prodid
	 *            String
	 * @param yyyMMdd
	 *            String 民國年月日 9碼 yyy/MM/dd
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findLoanInfo(String custId, String prodid,
			String yyyMMdd);

	/**
	 * 查詢該單位之保證債務金額 P1
	 * 
	 * @param custId
	 *            String
	 * @param bc
	 *            String
	 * @param prodid
	 *            String
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> getPromiseBussLoanPromiseValP1(String custId,
			String bc, String prodid);

	/**
	 * 查詢該單位之保證債務金額非 P1
	 * 
	 * @param custId
	 *            String
	 * @param bc
	 *            String
	 * @param prodid
	 *            String
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> getPromiseBussLoanPromiseValNotP1(String custId,
			String bc, String prodid);

	/**
	 * 查詢聯徵BAM087資料。
	 * 
	 * @param qDate
	 *            介面_取得聯徵資料日期，查詢日期，格式為 YYY/MM/DD
	 * @param compIds
	 *            統編，可輸入多筆
	 * @param prodIds
	 *            產品別，可輸入多筆
	 * @param bcs
	 *            機構代碼，可輸入多筆
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findLoanAmountAndBalance(String qDate,
			List<String> compIds, List<String> prodIds, List<String> bcs);

	/**
	 * 查詢聯徵BAM087資料。(透過查詢DataDates,組合qDate與轄下公司id)
	 * 
	 * @param compIds
	 *            統編，可輸入多筆
	 * @param prodIds
	 *            產品別，可輸入多筆
	 * @param bcs
	 *            機構代碼，可輸入多筆
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findLoanAmountAndBalance(List<String> compIds,
			List<String> prodIds, List<String> bcs);

	/**
	 * 查詢【組合產品一】之授信資料
	 * 
	 * @param compId
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> getQryDate(String compId);

	/**
	 * 查詢 主債務餘額資料
	 * 
	 * @param compId
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> getLoanAmtById(String compId);

	/**
	 * 查詢 主債務餘額資料
	 * 
	 * @param compId
	 *            String
	 * @param yyy
	 *            String
	 * @param mm
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> getLoanAmtByIdAndDate(String compId, String yyy,
			String mm);

	/**
	 * 查詢 查詢授信餘額
	 * 
	 * @param compId
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> getLoanAmtAndDueAmtById(String compId);

	/**
	 * 查詢 查詢授信額度1
	 * 
	 * @param compId
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> getConAmtByIdStep1(String compId);

	/**
	 * 查詢 查詢授信額度2
	 * 
	 * @param compId
	 *            String
	 * @return Map<String, Object>
	 */
	Map<String, Object> getConAmtByIdStep2(String compId);

	/**
	 * 查詢 聯徵資料中 ( table : BAM087) 查詢授信資訊
	 * 
	 * @param compId
	 *            String
	 * @param yyy
	 *            String 民國年，三碼，不補零
	 * @param mm
	 *            月，兩碼，補零
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> getBam087_1(String compId, String yyy, String mm);

	/**
	 * 引入借款資料(主債務)
	 * 
	 * @param id
	 *            客戶統一編號
	 * @param prodId
	 *            查詢產品別
	 * @return Map<String, Object>
	 */
	List<Map<String, Object>> findBorrowerInfo(String id, String prodId);

	/**
	 * 引進過去一年是否動用現金卡循環額度
	 * 
	 * @param id
	 *            統一編號
	 * @param qDate
	 *            查詢日期 YYY/MM/DD
	 * @param prodId
	 *            查詢產品別
	 * @return Map
	 */
	Map<String, Object> findUseCashCardCycleAmtCount(String id, String qDate,
			String prodId);

	/**
	 * 保證公司 引進授信資料</br> 查詢 聯徵資料中 (BAM087) 查出 個人主債務資料, 並依銀行名稱加分行名稱,列出前三大餘額佔最多的行庫
	 * <li>若聯徵代碼為空則</li>
	 * <ul>
	 * PROMISE_BUSS_ID = 保證公司統編
	 * </ul>
	 * <li>否則</li>
	 * <ul>
	 * PROMISE_BUSS_ID = 保證公司之聯徵虛擬統編
	 * </ul>
	 * 
	 * @param id
	 *            客戶統一編號
	 * @param prodId
	 *            查詢產品別
	 * @param isQueryJPQ
	 *            若查詢JPQ授信資料,傳入true
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findGuaranteeCorpLoanInfo(String id,
			String[] prodId, boolean isQueryJPQ);

	/**
	 * 引進申貸戶信用資料
	 * <nl>
	 * <li>SQL1-1 查詢退票異常紀錄 (新)Map</li>
	 * <li>SQL1-2 查詢退票(新)List</li>
	 * <li>SQL2 查詢拒絕往來紀錄(新)Map</li>
	 * <li>SQL3 查詢逾期、催收、呆帳紀錄 (已開發)List</li>
	 * <dl>
	 * <li>請傳入查詢方式</li>
	 * <ol>
	 * <li>傳入0，即查詢SQL1~SQL4</li>
	 * <li>傳入1，即查詢SQL1-1&SQL1-2</li>
	 * <li>傳入2，即查詢SQL2</li>
	 * <li>傳入3，即查詢SQL3</li>
	 * </ol>
	 * </dl>
	 * </nl>
	 * 
	 * @param id
	 *            客戶統一編號
	 * @param type
	 *            查詢方式
	 * @return Map
	 */
	Map<String, Object> findLoanUserCreditData(String id, int type);

	/**
	 * 引入聯徵中心明細
	 * <ol>
	 * 目前使用的交易
	 * <nl>
	 * 徵信報告Ch9-2
	 * </nl>
	 * </ol>
	 * 
	 * @param groupId
	 *            集團代號
	 * @param compId
	 *            公司統編(申貸戶統編)
	 * @return list
	 */
	List<Map<String, Object>> findJCICDetailsFor92(String groupId, String compId);

	/**
	 * 查詢集團企業大額退票、拒絕往來、逾期催收與呆帳情形
	 * <ol>
	 * 目前使用的交易
	 * <nl>
	 * 徵信報告Ch9-3
	 * </nl>
	 * </ol>
	 * 
	 * @param gId
	 *            集團代號
	 * @return map
	 */
	Map<String, Object> findRefundsExchangeBadDebidBygId(String gId);

	/**
	 * 主債存放逾期總額 <br/>
	 * 金融機構借款往來產品別為P1、P8
	 * 
	 * @param qDate
	 *            查詢日期
	 * @param compId
	 *            統編
	 * @return Map
	 */
	Map<String, Object> getCompSecondLoanDataInfo(String qDate, String compId);

	/**
	 * 可查詢依據統編之JPQ授信餘額、JPQ訂約金額 <br/>
	 * 金融機構借款往來產品別為P1、P8
	 * 
	 * @param qDate
	 *            查詢日期
	 * @param compId
	 *            統編
	 * @return Map
	 */
	Map<String, Object> getCompSecondLoanDataInfoJPQ(String qDate, String compId);

	/**
	 * 介面_Ch3_3查詢董監事逾催呆_1主債-姓名_訂約_授信_逾期 (BAM087) <li>查詢餘額</li>
	 * 
	 * @param compId
	 *            String
	 * @return Map
	 */
	Map<String, Object> getBam087ByP3P9(String compId);

	/**
	 * 介面_Ch3_3查詢董監事逾催呆_1主債-姓名_訂約_授信_逾期 (BAM087) <li>查詢JPQ額度</li>
	 * 
	 * @param compId
	 *            String
	 * @return Map
	 */
	Map<String, Object> getBam087JPQByP3P9(String compId);

	/**
	 * 介面_Ch4_2保證公司_3授信往來情形_1引進授信資料 (BAM087) 之SQL4
	 * 
	 * @param id
	 *            String
	 * @param bankCode
	 *            String
	 * @return list
	 */
	List<Map<String, Object>> findCorpLoanInfo(String id, String[] bankCode);

	/**
	 * 介面_Ch7_1財務分析_2授信明細表_3取得授信往來行庫借款餘額排名清單
	 * 
	 * @param id
	 *            String
	 * @return list
	 */
	List<Map<String, Object>> findLoanBalOfBAK(String id);

//	/**
//	 * 介面_Ch9_2引進聯徵中心明細 (單筆資料)
//	 * 
//	 * @param compId
//	 *            String
//	 * @return Map
//	 */
//	Map<String, Object> getJCICDetailsFor92(String compId);

	/**
	 * 取得聯徵中心明細(金額) for CES140
	 * @param compId
	 *            客戶統編
	 * @param realId
	 *            查詢聯徵客戶統編
	 * @return Map
	 */
	Map<String, Object> getCreditInfo(String compId, String realId);

}
