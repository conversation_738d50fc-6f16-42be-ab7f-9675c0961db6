//做為畫面init完成後使用
var initDfd = window.initDfd || $.Deferred();
var _handler="cls1220m04formhandler";
$(function(){

	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
    $.form.init({
        formId: "tabForm",
        formHandler: _handler,
        formAction: "query",
        loadSuccess: function(json){    	
        	if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
				tabForm.lockDoc();
			} //若操作者 只有 EL00 權限，要能切換頁籤 setIgnoreTempSave(true)
        	  	
        	if(true){
        		//當 html 中的 element 中有 codeType
            	tabForm.buildItem();		
        	}

        	if(json.page == '01'){
        	    // 使用專案
                $("#usePlan").setItems({
                    // i18n : i18n.samplehome,
                    item : json.usePlanItem,
                    format : "{key}" // ,
                // value :
                });
        	}

        	tabForm.injectData(json);
        	
        	if(json.page == '05'){
        		loadCls1220m06Setting(tabForm,json);
        	}

        	initDfd.resolve(json);
        	ilog.debug("@CLS1220M04Page.js, mainId= " + responseJSON.mainId);
        	if(json.isClosed=="X"){
				tabForm.lockDoc();
        		$("#btnSave").addClass(" ui-state-disabled ").prop("disabled", "true");
        		$("#btnDiscardLoan").addClass(" ui-state-disabled ").prop("disabled", "true");
        	}
        }
    });
    
    //設定經辦下拉選單(派案-指定簽案行員)
	/*$.ajax({
		type : "POST",
		handler: "cls1220m10formhandler",
	    action: "getMegaEmpInfo",
		success:function(responseData){
			if(responseData.Success){ 
				$("#SignMegaEmp").setItems({
                    item: responseData.bossListAO,
                    space: true,
                    format: "{value} {key}"
                });
			}
		}
	});*/
	if(responseJSON.noOpenDoc){
		$(".ordinary").hide();
	}


	var saveAction = function(opts){
        if (tabForm.valid()) {
        	return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    page: responseJSON.page,
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                )
            }).done(function(json){
				tabForm.injectData(json);
				//更新 opener 的 Grid
				API.triggerOpener();
			});
        	
        } else {
            return $.Deferred();
        }
    }

	var sendAction = function(){
		//設定經辦下拉選單(派案-指定簽案行員)，配合地政士案件於派案時需將重複派案人員從清單內移除，改成點派案時再撈簽案行員
		$.ajax({
			type : "POST",
			handler: "cls1220m10formhandler",
		    action: "getMegaEmpInfo"
		}).done(function(responseData){
			if(responseData.Success){ 
				var signMegaEmp = $("#SignMegaEmp");
				signMegaEmp.setItems({
					item: responseData.bossListAO,
					space: true,
					format: "{value} {key}"
				});
			}
			
			//跳派案視窗選擇派案人員
			var sendDiv = $("#SendDiv");
			sendDiv.thickbox({
				title: i18n.cls1220m04['button.send'], width: 350, height: 100, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
				buttons: {
					"sure": function(){
						//根據有無擔保品欄位顯示
						$.ajax({
							type : "POST",
							handler : "cls1220m10formhandler",
							data : {
								formAction : "sendToMegaEmp",
								mainOid : responseJSON.mainOid,
								mainId: responseJSON.mainId,
								SignMegaEmp: $("select#SignMegaEmp").val(),
								evaMegaEmpNo: $('#evaMegaEmpNo').val(),
								evaMegaEmpName: $('#evaMegaEmpName').val(),
								estUnitName: $('#estUnitName').val()
							}
						}).done(function(responseData){
							if(responseData.Success){
								$.thickbox.close();
								CommonAPI.triggerOpener("gridview", "reloadGrid");
								window.close();
//		    							CommonAPI.showMessage(responseData.Message);
							}else{
								CommonAPI.showErrorMessage(responseData.Message);
							}
						});
					},
					"cancel": function(){
						$.thickbox.close();
					}
				}
			});
		});
    }
	
	 var prepare_ploan_discardLoan = function(){
	    	var my_dfd = $.Deferred();
	    	$.ajax({
	            type: "POST", handler: "cls1220m01formhandler",
	            data:{ formAction: "prepare_ploan_discardLoan", mainId: responseJSON.mainId}
	        }).done(function(json){
				if(json.cfmMsg && json.cfmMsg!=""){
					API.confirmMessage(json.cfmMsg, function(b){
						if (b) {
							my_dfd.resolve();
						}
					});
				}else{
					my_dfd.resolve();
				}
			});
	    	return my_dfd;
	    }
	 
	
	btnPanel.find("#btnSave").click(function(){
		saveCheck05(responseJSON.page);
    	saveAction({'allowIncomplete':'Y','checkSave':'Y'}).done(function(json){
    		if(json.saveOkFlag){
    			var dyna = [];
    			if(true){
    				dyna.push(i18n.def.saveSuccess);
    			}	
    			if(json.IncompleteMsg){
    				dyna.push(json.IncompleteMsg);
    			}	
    			if(json.alertMsg){
    				dyna.push(json.alertMsg);
    			}
    			API.showMessage(dyna.join("<br/>-------------------<br/>"));
    		}
    	});
    }).end().find("#btnDiscardLoan").click(function(){
    	prepare_ploan_discardLoan().done(function(){
    		$.ajax({
                type: "POST", handler: "cls1220m01formhandler",
                data:{ formAction: "run_ploan_discardLoan", mainId: responseJSON.mainId}        		
            }).done(function(json){
				//        			$("#btnSave").addClass(" ui-state-disabled ").attr("disabled", "true");            		
				//        			$("#btnDiscardLoan").addClass(" ui-state-disabled ").attr("disabled", "true");
									//更新 opener 的 Grid
				//                	API.triggerOpener();
									$.thickbox.close();
									CommonAPI.triggerOpener("gridview", "reloadGrid");
									window.close();
								});	
    	});
    	
    }).end().find("#btnPrint").click(function(){
    	if(responseJSON.mainApplyKind == 'I' || responseJSON.mainApplyKind == 'J'){//下載青創申辦文件
    		if($("#buttonPanel").find("#btnSave").is("button")){
    			CommonAPI.confirmMessage(i18n.cls1220m04['button.sendMessage'], function(b){ //執行將自動儲存資料，是否繼續此動作?
        			if (b) {
        				saveCheck05(responseJSON.page);
        				saveAction({'allowIncomplete':'Y','checkSave':'Y'}).done(function(json){
        					if(json.alertMsg){
        						var dyna = '';
        						dyna = i18n.def.saveSuccess + "<br/>-------------------<br/>" + json.alertMsg;
        	    				CommonAPI.showPopMessage('', dyna, printCls1220m06);
        	    			}else{
        	    				printCls1220m06();
        	    			}
        				});
        			}
        		});
    		}else{
    			printCls1220m06();
    		}
    	}else{
    		print();
    	}
    }).end().find("#btnSend").click(function(){ //派案
    	CommonAPI.confirmMessage(i18n.cls1220m04['button.sendMessage'], function(b){ //執行將自動儲存資料，是否繼續此動作?
    		if (b) {
    			//因主管也可以改派案資料，所以派案前先儲存
    			saveCheck05(responseJSON.page);
                saveAction({'allowIncomplete':'Y','checkSave':'Y','checkCredit':'N'}).done(function(json){
                	//存完檔案先檢查資料
            		if(json.saveOkFlag){
            			var dyna = [];
            			if(true){
            				$.ajax({
                				type : "POST",
                				handler : "cls1220m10formhandler",
                				data : {
                					formAction : "checkSendData",
                					mainOid : responseJSON.mainOid,
                					mainId: responseJSON.mainId
                				}
                			}).done(function(responseData){
								var Success = responseData.Success;
								if(responseData.Success){
									sendAction();	
								}else{
									CommonAPI.showErrorMessage(responseData.Message);
								}
								API.triggerOpener();
							});		
//            				dyna.push(i18n.def.saveSuccess);
            			}	
            			if(json.IncompleteMsg){
            				dyna.push(json.IncompleteMsg);
            				API.showMessage(dyna.join("<br/>-------------------<br/>"));
            			}	
            		}
                   });
               }
          });
    });

});


function showJSON(){
	$.ajax({
        type: "POST", handler: 'cls1220m04formhandler', data:{ formAction: "showJSON", mainId: responseJSON.mainId}  
        , success: function(json){}
    });
}

function printCls1220m06(){
    if (responseJSON.mainOid) {
        $.capFileDownload({
            handler: "cls1220m06filedownloadhandler",
            data: {
                oid: responseJSON.mainOid
            }
        });
    }
    $.thickbox.close();
}

function loadCls1220m06Setting(tabForm,sJson){
	var _editSta=($("#buttonPanel").find("#btnSave").is("button") || (sJson.lock !== undefined &&!sJson.lock));
	tabForm.find("#caseType_CN").val("2" == sJson.IncomType ? "線上" : "線下");
	tabForm.find("#includeTaxData")["1" == sJson.IncomType && _editSta ? "show" : "hide"]();

	if( "2" == sJson.IncomType && !(( sJson.OTP_STATUS === "4" && sJson.OTP_ERRORCODE === "4001" 
		&& sJson.OTP_DATARESULT === "00" && sJson.OTP_ACCTRESULT === "00" && sJson.OTP_TYPERESULT === "01" ) || 
		sJson.agreeQueryEJTs_patch != null)//新增 線上同意查EJ 時間(補件)判斷, 有值代表同意
	){
		tabForm.find("#otpCheck").show();
	}else{
		tabForm.find("#otpCheck").hide();
	}
	
	if(true) {
        var radioId = "1" == sJson.apply_type ? ['atype', 'ctype', 'a1_1_', 'a3', 'elevel',
        'q1', 'q2', 'q3', 'q4', 'q5', 'q6', 'q7', 'q8', 'q9', 'q10', 'q11', 'q12', 'q13', 'q14', 'q15', 'q16',
        'q17', 'q18', 'q19', 'q20'] : ['atype', 'ctype', 'a0', 'a1', 'a2', 'a1_1', 'a3', 'a3_7', 'a3', 'elevel'];
        for(var i=0;i<radioId.length;i++){
            var value = radioId[i];
            tabForm.find("input[name=" + value + "][value='" + sJson[value] + "']:visible").prop('checked',true);
        }
        
        if ("1" == sJson.apply_type){
        	var checkBoxId = ['a3_7'];
        	for(var i=0;i<checkBoxId.length;i++){
        		var v =checkBoxId[i];
                if(sJson[v]) {
                    var arr = sJson[v].split(",");
                    for(var j=0;j<arr.length;j++){
                        var value = arr[j];
                        tabForm.find("input[name='" + v + "'][value=" + value + "]").prop('checked', true);
                        //tabForm.find("input[name='" +v+ "'][value=" + value + "]").trigger('click');
                    }
                }
            }
        }
        var cityArray= ["#citya","#cityb","#city","#city2","#city3"];
        for(var i=0;i<cityArray.length;i++){
        	var value = cityArray[i];
        	tabForm.find(value).trigger('change');
        	var cityareaId = "cityarea" + value.substring(5);
        	tabForm.find("#"+cityareaId).val(sJson[cityareaId]);
        }
        var checkedShowId = "1" == sJson.apply_type ? {
            'ctype5': 'ctype_ctxt',
            //'a11': 'dpBankNameLink,a1_1Y,a1_1N',
            //'a12': 'a1_ctxt2',
            //'a21': 'a2_1',
            //'a22': 'a2_2',
            //'a23': 'a2_3',
            'a31': 'a3_1,a3_2,a3_3',
            'a32': 'a3_4,a3_5,a3_6,a3_71,a3_72,a3_73,a3_74,a3_75',
            'a3_75': 'a3_8',
            'a33': 'a3_9,a3_10,a3_11,a3_12'
        } : {
            'ctype5': 'ctype_ctxt',
            'a1': 'a1_1,a1_2,a1_3,dpBankNameLink',
            'a2': 'a1_5',
            'a31': 'a3_1,a3_2,a3_3',
            'a32': 'a3_4,a3_5,a3_6',
            'a33': 'a3_9,a3_10,a3_11'
        };
        
        if (_editSta) {
            for (var key in checkedShowId) {
                var value = checkedShowId[key];
                var checked = tabForm.find("input[id=" + key + "]").is(':checked');
                var arr = value.split(',');
                for (var i = 0; i < arr.length; i++) {
                	if('dpBankNameLink'==arr[i]){
                		tabForm.find("#dpBankNameLink")[checked ?'show':'hide']();
                	}
                    tabForm.find("#" + arr[i]).readOnly(!checked);
                }
            }
        }
    }
	
    if ("1" == sJson.IncomType) {//線下
        if (_editSta) {//可編輯時
        	tabForm.find("#custName,#n_cnumber,#cname,#bday,#tel,#email,#n_tel,#n_phone,#n_fax").readOnly(false);
            tabForm.find("#custName,#n_cnumber,#cname").addClass('required');
        }
    }
}

function saveCheck05(pageId){
	if( pageId == '05'){
		$("input[type=hidden][name=a1]").remove();
        $("input[type=hidden][name=a2]").remove();
        $("input[type=hidden][name=a3_7]").remove();
	}
}

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		saveCheck05(responseJSON.page);
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();	
	}
});