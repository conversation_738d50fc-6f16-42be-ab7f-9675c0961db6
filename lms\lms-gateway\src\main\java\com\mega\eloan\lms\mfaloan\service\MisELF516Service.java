/* 
 *MisELF500Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

import com.mega.eloan.lms.mfaloan.bean.ELF516;

/**
 * <pre>
 * 可疑代辦案件註記檔 MIS.ELF516
 */
public interface MisELF516Service {

	/**
	 * 查詢額度相關資料
	 */
	public List<ELF516> findByCustId(String custId, String dupNo);

	public List<ELF516> findByCustIdBrNoYYYYMM(String brNo, String custId, String dupNo, String yyyyMM_beg, String yyyyMM_end);
	
	public ELF516 findByCustIdAndCntrnoAndYYYYMM(String custId, String dupNo,
			String cntrNo, String yyyyMM);

	public void updateC250M01A(String LNFLAG, String OTHERMEMO, String loanNo,  
			String updater, String approver, String branchComm, String otherDesc, String yyyyMM, String cntrNo);
	
	public List<ELF516> findForCLS180R18(String brno_area, String yyyyMM_beg, String yyyyMM_end, String lngeFlag);
}
