/* 
 * L140S02C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 分段利率主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S02C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq" }))
public class L140S02C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/**
	 * 首段適用利率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 1.如果第一段有值則由第 一段的值直接帶入<br/>
	 * 2.如果第一段無值而是用其他說明時則在按確定時要求輸入一個值存入。
	 */
	@Digits(integer = 2, fraction = 4)
	@Column(name = "SUBMITRATE", columnDefinition = "DECIMAL(6,4)")
	private BigDecimal submitRate;

	/** 已還期數 **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "PAYNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer payNum;

	/** 前期利率說明 **/
	@Size(max = 4500)
	@Column(name = "PREDSCR", length = 4500, columnDefinition = "VARCHAR(4500)")
	private String preDscr;

	/**
	 * 2013-05-09 新增欄位<br/>
	 * Ex: 0.946/0.95<br/>
	 * (預設值1；值域<=1；不可負的；個人只限按月計息戶；整數1位小數3位)<br/>
	 */
	@Digits(integer = 2, fraction = 5)
	@Column(name = "TAXRATE", columnDefinition = "DECIMAL(7,5)")
	private BigDecimal taxRate;

	/**
	 * 2013-05-09 新增欄位<br/>
	 * Ex: 0.946/0.95<br/>
	 * (預設值1；值域<=1；不可負的；個人只限按月計息戶；整數1位小數3位)<br/>
	 */
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	/**
	 * 2013-05-09 新增欄位<br/>
	 * Ex: 0.946/0.95<br/>
	 * (預設值1；值域<=1；不可負的；個人只限按月計息戶；整數1位小數3位)<br/>
	 */
	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	/**
	 * 計息方式(單選)：{1:按月收息, 2:期付金, P:透支end, Q:透支top} <br/>
	 * codetype=L140S02C_intWay <br/>
	 * ------------------------ <br/>
	 * Ｑ：為何產品68的作業要點有寫到 "憑借款支用書於額度內撥付約定之借款人帳戶，依動用當日最終放款餘額按日計息。" => 但在 e-loan 卻只有按月計息，沒有按日計息的選項？ <br/>
	 * Ａ： "計息方式" 是A-LOAN 的業務分類，如 :按月計息、期付金、本息並付......　 <br/>
 	 * 	●　若計息方式=按月計息，於實際計算利息時會細分: 實際天日/365 、  實際天日/365、每月 30/360(詳細可請教計息專家) <br/>
	 *  ●　像 "期付金" 下又分為月繳、雙週繳，畫面也不會呈現"月繳、雙週繳"字句 <br/>
	 * ------------------------ <br/>
	 * Ｑ：所以=>計息天數基礎是:天數/365,按月計息,機動,即為按日計息。是這樣嗎? <br/>
	 * Ａ：可以這麼解釋，依動用當日最終放款餘額按日計息 <br/>
	   * 　　還有..只要要收息的...都是每天計算利息的<br/>
	 * ------------------------ <br/>
	 */
	@Size(max = 1)
	@Column(name = "INTWAY", length = 1, columnDefinition = "CHAR(1)")
	private String intWay;

	/**
	 * 收息方式
	 * <p/>
	 * 單選：<br/>
	 * 1按月收息<br/>
	 * 2三個月收息一次<br/>
	 * 3半年收息一次<br/>
	 * 4按年收息<br/>
	 * 6期付金<br/>
	 * codetype=L140S02C_rIntWay
	 */
	@Size(max = 1)
	@Column(name = "RINTWAY", length = 1, columnDefinition = "CHAR(1)")
	private String rIntWay;

	/**
	 * 省息遞減
	 * <p/>
	 * 單選：<br/>
	 * 0.無<br/>
	 * A.第一期間為前2年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * B.第一期間為前3年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * C.第一期間為前5年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * D.第一期間為前2年，第二段搭配省息遞減利率0.24%，最多遞減1次<br/>
	 * E.第一期間為前3年，第二段搭配省息遞減利率0.24%，最多遞減1次<br/>
	 * F.第一期間為前5年，第二段搭配省息遞減利率0.24%，最多遞減1次
	 */
	@Size(max = 1)
	@Column(name = "DECFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String decFlag;

	/**
	 * 是否輸入其他
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "ISINPUTDESC", length = 1, columnDefinition = "CHAR(1)")
	private String isInputDesc;

	/**
	 * 取得是否輸入其他
	 * <p/>
	 * Y/N
	 */
	public String getIsInputDesc() {
		return isInputDesc;
	}

	/**
	 * 設定 是否輸入其他
	 * <p/>
	 * Y/N
	 */
	public void setIsInputDesc(String isInputDesc) {
		this.isInputDesc = isInputDesc;
	}

	/**
	 * 其他
	 * <p/>
	 * (此欄位內容不會上傳中心主機，若不為報請總額度之利率，請勿登錄此欄！)
	 */
	@Size(max = 3087)
	@Column(name = "DESC", length = 3087, columnDefinition = "VARCHAR(3087)")
	private String desc;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/**
	 * 取得首段適用利率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 1.如果第一段有值則由第 一段的值直接帶入<br/>
	 * 2.如果第一段無值而是用其他說明時則在按確定時要求輸入一個值存入。
	 */
	public BigDecimal getSubmitRate() {
		return this.submitRate;
	}

	/**
	 * 設定首段適用利率
	 * <p/>
	 * ※2012--10-25 新增<br/>
	 * 1.如果第一段有值則由第 一段的值直接帶入<br/>
	 * 2.如果第一段無值而是用其他說明時則在按確定時要求輸入一個值存入。
	 **/
	public void setSubmitRate(BigDecimal value) {
		this.submitRate = value;
	}

	/** 取得已還期數 **/
	public Integer getPayNum() {
		return this.payNum;
	}

	/** 設定已還期數 **/
	public void setPayNum(Integer value) {
		this.payNum = value;
	}

	/** 取得前期利率說明 **/
	public String getPreDscr() {
		return this.preDscr;
	}

	/** 設定前期利率說明 **/
	public void setPreDscr(String value) {
		this.preDscr = value;
	}

	/**
	 * 取得計息方式(單選)：{1:按月收息, 2:期付金, P:透支end, Q:透支top}
	 */
	public String getIntWay() {
		return this.intWay;
	}

	/**
	 * 設定計息方式(單選)：{1:按月收息, 2:期付金, P:透支end, Q:透支top}	 
	 **/
	public void setIntWay(String value) {
		this.intWay = value;
	}

	/**
	 * 取得收息方式
	 * <p/>
	 * 單選：<br/>
	 * 1按月收息<br/>
	 * 2三個月收息一次<br/>
	 * 3半年收息一次<br/>
	 * 4按年收息<br/>
	 * 6期付金
	 */
	public String getRIntWay() {
		return this.rIntWay;
	}

	/**
	 * 設定收息方式
	 * <p/>
	 * 單選：<br/>
	 * 1按月收息<br/>
	 * 2三個月收息一次<br/>
	 * 3半年收息一次<br/>
	 * 4按年收息<br/>
	 * 6期付金
	 **/
	public void setRIntWay(String value) {
		this.rIntWay = value;
	}

	/**
	 * 取得省息遞減
	 * <p/>
	 * 單選：<br/>
	 * 0.無<br/>
	 * A.第一期間為前2年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * B.第一期間為前3年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * C.第一期間為前5年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * D.第一期間為前2年，第二段搭配省息遞減利率0.24%，最多遞減1次<br/>
	 * E.第一期間為前3年，第二段搭配省息遞減利率0.24%，最多遞減1次<br/>
	 * F.第一期間為前5年，第二段搭配省息遞減利率0.24%，最多遞減1次
	 */
	public String getDecFlag() {
		return this.decFlag;
	}

	/**
	 * 設定省息遞減
	 * <p/>
	 * 單選：<br/>
	 * 0.無<br/>
	 * A.第一期間為前2年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * B.第一期間為前3年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * C.第一期間為前5年，第二段搭配省息遞減利率0.12%，最多遞減6次<br/>
	 * D.第一期間為前2年，第二段搭配省息遞減利率0.24%，最多遞減1次<br/>
	 * E.第一期間為前3年，第二段搭配省息遞減利率0.24%，最多遞減1次<br/>
	 * F.第一期間為前5年，第二段搭配省息遞減利率0.24%，最多遞減1次
	 **/
	public void setDecFlag(String value) {
		this.decFlag = value;
	}

	/**
	 * 取得其他
	 * <p/>
	 * (此欄位內容不會上傳中心主機，若不為報請總額度之利率，請勿登錄此欄！)
	 */
	public String getDesc() {
		return this.desc;
	}

	/**
	 * 設定其他
	 * <p/>
	 * (此欄位內容不會上傳中心主機，若不為報請總額度之利率，請勿登錄此欄！)
	 **/
	public void setDesc(String value) {
		this.desc = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
