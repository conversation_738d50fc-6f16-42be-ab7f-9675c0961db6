/* 
 * C122M01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
// import com.mega.eloan.common.model.IDocObject; 此 table 無 mainId，不符合 IDocObject 要求的 oid, mainId
// import com.mega.eloan.common.model.listener.DocumentModifyListener; 此 table 不寫入 lms.BTempData

/** 團體消貸名單控制檔 **/
@Entity
@Table(name="C122M01E", uniqueConstraints = @UniqueConstraint(columnNames = {"grpCntrNo","custId"}))
public class C122M01E extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 團貸母戶編號<p/>
	 * 12碼，例如：************<br/>
	 *  select * from mis.pteamapp where grpcntrno='************'【中鋼第46次消費性貸款】 <br/>
	 *  +以12個9代表測試 => 上PROD後，測試是否可收到BillHunter寄的信<br/>
	 *  + 下載時，弄1個下拉選單(排除12個9)
	 */
	@Size(max=12)
	@Column(name="GRPCNTRNO", length=12, columnDefinition="CHAR(12)")
	private String grpCntrNo;

	/** 分行別 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 身分證號(10碼) **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * DUPNO<p/>
	 * 保留此欄位, 放空白, 免得日後要加
	 */
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 姓名<p/>
	 * 這裡的姓名,可能是全新客戶, 連0024都沒有
	 */
	@Size(max=150)
	@Column(name="CUSTNAME", length=150, columnDefinition="VARCHAR(150)")
	private String custName;

	/** 
	 * 職工編號<p/>
	 * 前端6碼<br/>
	 *  ref既有的 C120M01A.staffNo ,
	 */
	@Size(max=20)
	@Column(name="EMPNO", length=20, columnDefinition="VARCHAR(20)")
	private String empNo;

	/** 
	 * 手機<p/>
	 * 10碼
	 */
	@Size(max=10)
	@Column(name="MTEL", length=10, columnDefinition="CHAR(10)")
	private String mTel;

	/** 
	 * 申貸金額<p/>
	 * 上傳的單位:萬元；匯出的單位:元<br/>
	 *  DB存的金額(元)
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="APPLYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal applyAmt;

	/** 
	 * 撥款方式<p/>
	 * Appropriation 的縮寫{C:支票, B:銀行或郵局}中鋼有(本行存款帳號, 郵局匯款, 支票)本行存款帳號, 郵局匯款 都放B支票 放Ｃ
	 */
	@Size(max=1)
	@Column(name="APPNWAY", length=1, columnDefinition="CHAR(1)")
	private String appnWay;

	/** 
	 * 預先指定撥款銀行<p/>
	 * 本行存款帳號, 放017<br/>
	 *  郵局存款帳號,放700
	 */
	@Size(max=3)
	@Column(name="APPNBANKCODE", length=3, columnDefinition="CHAR(3)")
	private String appnBankCode;

	/** 
	 * 存款帳號<p/>
	 * 當本行時, 11碼<br/>
	 *  若是郵局匯款, 長度會是14碼
	 */
	@Size(max=16)
	@Column(name="DPACCT", length=16, columnDefinition="VARCHAR(16)")
	private String dpAcct;

	/** 
	 * 對應的線上進件資料<p/>
	 * 在 PLOAN 進件後，MegaGW 會寫入此欄位
	 */
	@Size(max=32)
	@Column(name="REFMAINID", length=32, columnDefinition="CHAR(32)")
	private String refMainId;

	/** 
	 * 英文姓名<p/>
	 * 2021/11/01新增
	 */
	@Size(max=100)
	@Column(name="ENGNAME", length=100, columnDefinition="VARCHAR(100)")
	private String engName;

	/** 
	 * 生日(民國年)<p/>
	 * 2021/11/01新增民國年
	 */
	@Size(max=8)
	@Column(name="BIRTHDAY", length=8, columnDefinition="VARCHAR(8)")
	private String birthday;

	/** 
	 * email<p/>
	 * 2021/11/01新增
	 */
	@Size(max=150)
	@Column(name="EMAIL", length=150, columnDefinition="VARCHAR(150)")
	private String email;

	/** 
	 * 職稱<p/>
	 * 2021/11/01新增
	 */
	@Size(max=50)
	@Column(name="JOBPOSITION", length=50, columnDefinition="VARCHAR(50)")
	private String jobPosition;

	/** 
	 * 住宅電話_區碼<p/>
	 * 2021/11/01新增
	 */
	@Size(max=3)
	@Column(name="HOMEPHONECODE", length=3, columnDefinition="VARCHAR(3)")
	private String homePhoneCode;

	/** 
	 * 住宅電話<p/>
	 * 2021/11/01新增
	 */
	@Size(max=16)
	@Column(name="HOMEPHONE", length=16, columnDefinition="VARCHAR(16)")
	private String homePhone;

	/** 
	 * 戶籍地址_郵遞區碼<p/>
	 * 2021/11/01新增
	 */
	@Size(max=6)
	@Column(name="FADDRPOSTALCODE", length=6, columnDefinition="VARCHAR(6)")
	private String fAddrPostalCode;

	/** 
	 * 戶籍地址<p/>
	 * 2021/11/01新增<br/>
	 *  保留中鋼原始資料(含全形空白)
	 */
	@Size(max=900)
	@Column(name="FADDR", length=900, columnDefinition="VARCHAR(900)")
	private String fAddr;

	/** 
	 * 通訊地址_郵遞區碼<p/>
	 * 2021/11/01新增
	 */
	@Size(max=6)
	@Column(name="COADDRPOSTALCODE", length=6, columnDefinition="VARCHAR(6)")
	private String coAddrPostalCode;

	/** 
	 * 通訊地址<p/>
	 * 2021/11/01新增<br/>
	 *  保留中鋼原始資料(含全形空白)
	 */
	@Size(max=900)
	@Column(name="COADDR", length=900, columnDefinition="VARCHAR(900)")
	private String coAddr;

	/** 
	 * 服務單位電話_區碼<p/>
	 * 2021/11/01新增
	 */
	@Size(max=3)
	@Column(name="COMPANYPHONECODE", length=3, columnDefinition="VARCHAR(3)")
	private String companyPhoneCode;

	/** 
	 * 服務單位電話_總機_分機<p/>
	 * 2021/11/01新增
	 */
	@Size(max=16)
	@Column(name="COMPANYPHONE", length=16, columnDefinition="VARCHAR(16)")
	private String companyPhone;

	/** 
	 * 現職年薪<p/>
	 * 2021/11/01新增<br/>
	 *  上傳的單位:萬元；匯出的單位:元<br/>
	 *  DB存的金額(元)
	 */
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="PAYAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal payAmt;

	/** 
	 * 年資_單位年<p/>
	 * 2021/11/01新增
	 */
	@Size(max=3)
	@Column(name="SNRY", length=3, columnDefinition="VARCHAR(3)")
	private String snrY;

	/** 
	 * 年資_單位月<p/>
	 * 2021/11/01新增
	 */
	@Size(max=2)
	@Column(name="SNRM", length=2, columnDefinition="VARCHAR(2)")
	private String snrM;

	/** 
	 * 支票領取地點<p/>
	 * 2021/11/04新增
	 */
	@Size(max=150)
	@Column(name="CHECKPLACE", length=150, columnDefinition="VARCHAR(150)")
	private String checkPlace;
	
	/** 任職公司(某一個團貸案，可能該公司、子公司、孫公司都可申貸，以此欄位來識別，客戶屬於哪些(子/孫)公司) */
	@Column(name="COMPANYID", length=10, columnDefinition="VARCHAR(10)")
	private String companyId;
	
	/** 認股金額 */
	@Column(name="SUBSCRIBEAMT", columnDefinition="DECIMAL(9,0)")
	private BigDecimal subscribeAmt;
	
	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 
	 * 建立時間<p/>
	 * 在 megaGW 會用 createTime判斷
	 */
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得團貸母戶編號<p/>
	 * 12碼，例如：************<br/>
	 *  select * from mis.pteamapp where grpcntrno='************'【中鋼第46次消費性貸款】 <br/>
	 *  +以12個9代表測試 => 上PROD後，測試是否可收到BillHunter寄的信<br/>
	 *  + 下載時，弄1個下拉選單(排除12個9)
	 */
	public String getGrpCntrNo() {
		return this.grpCntrNo;
	}
	/**
	 *  設定團貸母戶編號<p/>
	 *  12碼，例如：************<br/>
	 *  select * from mis.pteamapp where grpcntrno='************'【中鋼第46次消費性貸款】 <br/>
	 *  +以12個9代表測試 => 上PROD後，測試是否可收到BillHunter寄的信<br/>
	 *  + 下載時，弄1個下拉選單(排除12個9)
	 **/
	public void setGrpCntrNo(String value) {
		this.grpCntrNo = value;
	}

	/** 取得分行別 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定分行別 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得身分證號(10碼) **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證號(10碼) **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得DUPNO<p/>
	 * 保留此欄位, 放空白, 免得日後要加
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定DUPNO<p/>
	 *  保留此欄位, 放空白, 免得日後要加
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得姓名<p/>
	 * 這裡的姓名,可能是全新客戶, 連0024都沒有
	 */
	public String getCustName() {
		return this.custName;
	}
	/**
	 *  設定姓名<p/>
	 *  這裡的姓名,可能是全新客戶, 連0024都沒有
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得職工編號<p/>
	 * 前端6碼<br/>
	 *  ref既有的 C120M01A.staffNo ,
	 */
	public String getEmpNo() {
		return this.empNo;
	}
	/**
	 *  設定職工編號<p/>
	 *  前端6碼<br/>
	 *  ref既有的 C120M01A.staffNo ,
	 **/
	public void setEmpNo(String value) {
		this.empNo = value;
	}

	/** 
	 * 取得手機<p/>
	 * 10碼
	 */
	public String getMTel() {
		return this.mTel;
	}
	/**
	 *  設定手機<p/>
	 *  10碼
	 **/
	public void setMTel(String value) {
		this.mTel = value;
	}

	/** 
	 * 取得申貸金額<p/>
	 * 上傳的單位:萬元；匯出的單位:元<br/>
	 *  DB存的金額(元)
	 */
	public BigDecimal getApplyAmt() {
		return this.applyAmt;
	}
	/**
	 *  設定申貸金額<p/>
	 *  上傳的單位:萬元；匯出的單位:元<br/>
	 *  DB存的金額(元)
	 **/
	public void setApplyAmt(BigDecimal value) {
		this.applyAmt = value;
	}

	/** 
	 * 取得撥款方式<p/>
	 * Appropriation 的縮寫{C:支票, B:銀行或郵局}中鋼有(本行存款帳號, 郵局匯款, 支票)本行存款帳號, 郵局匯款 都放B支票 放Ｃ
	 */
	public String getAppnWay() {
		return this.appnWay;
	}
	/**
	 *  設定撥款方式<p/>
	 *  Appropriation 的縮寫{C:支票, B:銀行或郵局}中鋼有(本行存款帳號, 郵局匯款, 支票)本行存款帳號, 郵局匯款 都放B支票 放Ｃ
	 **/
	public void setAppnWay(String value) {
		this.appnWay = value;
	}

	/** 
	 * 取得預先指定撥款銀行<p/>
	 * 本行存款帳號, 放017<br/>
	 *  郵局存款帳號,放700
	 */
	public String getAppnBankCode() {
		return this.appnBankCode;
	}
	/**
	 *  設定預先指定撥款銀行<p/>
	 *  本行存款帳號, 放017<br/>
	 *  郵局存款帳號,放700
	 **/
	public void setAppnBankCode(String value) {
		this.appnBankCode = value;
	}

	/** 
	 * 取得存款帳號<p/>
	 * 當本行時, 11碼<br/>
	 *  若是郵局匯款, 長度會是14碼
	 */
	public String getDpAcct() {
		return this.dpAcct;
	}
	/**
	 *  設定存款帳號<p/>
	 *  當本行時, 11碼<br/>
	 *  若是郵局匯款, 長度會是14碼
	 **/
	public void setDpAcct(String value) {
		this.dpAcct = value;
	}

	/** 
	 * 取得對應的線上進件資料<p/>
	 * 在 PLOAN 進件後，MegaGW 會寫入此欄位
	 */
	public String getRefMainId() {
		return this.refMainId;
	}
	/**
	 *  設定對應的線上進件資料<p/>
	 *  在 PLOAN 進件後，MegaGW 會寫入此欄位
	 **/
	public void setRefMainId(String value) {
		this.refMainId = value;
	}

	/** 
	 * 取得英文姓名<p/>
	 * 2021/11/01新增
	 */
	public String getEngName() {
		return this.engName;
	}
	/**
	 *  設定英文姓名<p/>
	 *  2021/11/01新增
	 **/
	public void setEngName(String value) {
		this.engName = value;
	}

	/** 
	 * 取得生日(民國年)<p/>
	 * 2021/11/01新增民國年
	 */
	public String getBirthday() {
		return this.birthday;
	}
	/**
	 *  設定生日(民國年)<p/>
	 *  2021/11/01新增民國年
	 **/
	public void setBirthday(String value) {
		this.birthday = value;
	}

	/** 
	 * 取得email<p/>
	 * 2021/11/01新增
	 */
	public String getEmail() {
		return this.email;
	}
	/**
	 *  設定email<p/>
	 *  2021/11/01新增
	 **/
	public void setEmail(String value) {
		this.email = value;
	}

	/** 
	 * 取得職稱<p/>
	 * 2021/11/01新增
	 */
	public String getJobPosition() {
		return this.jobPosition;
	}
	/**
	 *  設定職稱<p/>
	 *  2021/11/01新增
	 **/
	public void setJobPosition(String value) {
		this.jobPosition = value;
	}

	/** 
	 * 取得住宅電話_區碼<p/>
	 * 2021/11/01新增
	 */
	public String getHomePhoneCode() {
		return this.homePhoneCode;
	}
	/**
	 *  設定住宅電話_區碼<p/>
	 *  2021/11/01新增
	 **/
	public void setHomePhoneCode(String value) {
		this.homePhoneCode = value;
	}

	/** 
	 * 取得住宅電話<p/>
	 * 2021/11/01新增
	 */
	public String getHomePhone() {
		return this.homePhone;
	}
	/**
	 *  設定住宅電話<p/>
	 *  2021/11/01新增
	 **/
	public void setHomePhone(String value) {
		this.homePhone = value;
	}

	/** 
	 * 取得戶籍地址_郵遞區碼<p/>
	 * 2021/11/01新增
	 */
	public String getFAddrPostalCode() {
		return this.fAddrPostalCode;
	}
	/**
	 *  設定戶籍地址_郵遞區碼<p/>
	 *  2021/11/01新增
	 **/
	public void setFAddrPostalCode(String value) {
		this.fAddrPostalCode = value;
	}

	/** 
	 * 取得戶籍地址<p/>
	 * 2021/11/01新增<br/>
	 *  保留中鋼原始資料(含全形空白)
	 */
	public String getFAddr() {
		return this.fAddr;
	}
	/**
	 *  設定戶籍地址<p/>
	 *  2021/11/01新增<br/>
	 *  保留中鋼原始資料(含全形空白)
	 **/
	public void setFAddr(String value) {
		this.fAddr = value;
	}

	/** 
	 * 取得通訊地址_郵遞區碼<p/>
	 * 2021/11/01新增
	 */
	public String getCoAddrPostalCode() {
		return this.coAddrPostalCode;
	}
	/**
	 *  設定通訊地址_郵遞區碼<p/>
	 *  2021/11/01新增
	 **/
	public void setCoAddrPostalCode(String value) {
		this.coAddrPostalCode = value;
	}

	/** 
	 * 取得通訊地址<p/>
	 * 2021/11/01新增<br/>
	 *  保留中鋼原始資料(含全形空白)
	 */
	public String getCoAddr() {
		return this.coAddr;
	}
	/**
	 *  設定通訊地址<p/>
	 *  2021/11/01新增<br/>
	 *  保留中鋼原始資料(含全形空白)
	 **/
	public void setCoAddr(String value) {
		this.coAddr = value;
	}

	/** 
	 * 取得服務單位電話_區碼<p/>
	 * 2021/11/01新增
	 */
	public String getCompanyPhoneCode() {
		return this.companyPhoneCode;
	}
	/**
	 *  設定服務單位電話_區碼<p/>
	 *  2021/11/01新增
	 **/
	public void setCompanyPhoneCode(String value) {
		this.companyPhoneCode = value;
	}

	/** 
	 * 取得服務單位電話_總機_分機<p/>
	 * 2021/11/01新增
	 */
	public String getCompanyPhone() {
		return this.companyPhone;
	}
	/**
	 *  設定服務單位電話_總機_分機<p/>
	 *  2021/11/01新增
	 **/
	public void setCompanyPhone(String value) {
		this.companyPhone = value;
	}

	/** 
	 * 取得現職年薪<p/>
	 * 2021/11/01新增<br/>
	 *  上傳的單位:萬元；匯出的單位:元<br/>
	 *  DB存的金額(元)
	 */
	public BigDecimal getPayAmt() {
		return this.payAmt;
	}
	/**
	 *  設定現職年薪<p/>
	 *  2021/11/01新增<br/>
	 *  上傳的單位:萬元；匯出的單位:元<br/>
	 *  DB存的金額(元)
	 **/
	public void setPayAmt(BigDecimal value) {
		this.payAmt = value;
	}

	/** 
	 * 取得年資_單位年<p/>
	 * 2021/11/01新增
	 */
	public String getSnrY() {
		return this.snrY;
	}
	/**
	 *  設定年資_單位年<p/>
	 *  2021/11/01新增
	 **/
	public void setSnrY(String value) {
		this.snrY = value;
	}

	/** 
	 * 取得年資_單位月<p/>
	 * 2021/11/01新增
	 */
	public String getSnrM() {
		return this.snrM;
	}
	/**
	 *  設定年資_單位月<p/>
	 *  2021/11/01新增
	 **/
	public void setSnrM(String value) {
		this.snrM = value;
	}

	/** 
	 * 取得支票領取地點<p/>
	 * 2021/11/04新增
	 */
	public String getCheckPlace() {
		return this.checkPlace;
	}
	/**
	 *  設定支票領取地點<p/>
	 *  2021/11/04新增
	 **/
	public void setCheckPlace(String value) {
		this.checkPlace = value;
	}
	
	/** 取得任職公司 */
	public String getCompanyId() {
		return companyId;
	}
	/** 設定任職公司 */
	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}
	/** 取得認股金額 */
	public BigDecimal getSubscribeAmt() {
		return subscribeAmt;
	}
	/** 設定認股金額 */
	public void setSubscribeAmt(BigDecimal subscribeAmt) {
		this.subscribeAmt = subscribeAmt;
	}
	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 
	 * 取得建立時間<p/>
	 * 在 megaGW 會用 createTime判斷
	 */
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/**
	 *  設定建立時間<p/>
	 *  在 megaGW 會用 createTime判斷
	 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
