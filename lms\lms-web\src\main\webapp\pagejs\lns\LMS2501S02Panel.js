var initDfd = $.Deferred(), inits = {
    fhandle: "",
    //使用動審表使用的gridhandler
    ghaddle: "lms1601gridhandler"
};

initDfd.done(function(json){
    ilog.debug("the Data : " + json.listData.length);
    if (json.listData.length) {
        var html = "";
        var testJson = json.listData;
        for (var i = 0; i < testJson.length; i++) {
            //alert(testJson[i].group);
            
            var group = testJson[i].group;
            html = html + "<tr><td colspan='5' class='hd2'><span class='field' name='group_" + DOMPurify.sanitize(group) + "' id='group_" + DOMPurify.sanitize(group) + "'>" + DOMPurify.sanitize(testJson[i].groupTitle) + " </span></td></tr>";
            
            var showHead = testJson[i].showHead;
            if ("Y" == showHead) {            	
                var yesTitle = testJson[i].yesTitle;
                var noTitle = testJson[i].noTitle;
                var naTitle = testJson[i].naTitle;
                html = html + "<tr><td width='6%'><span class='field' name='yesTitle_" + DOMPurify.sanitize(group) + "'>" + DOMPurify.sanitize(yesTitle) + "</span></td><td width='6%'><span class='field' name='noTitle_" + DOMPurify.sanitize(group) + "'>" + DOMPurify.sanitize(noTitle) + "</span></td><td width='6%'><span class='field' name='naTitle_" + DOMPurify.sanitize(group) + "'>" + DOMPurify.sanitize(naTitle) + "</span></td><td></td></tr>"
            }
            
            var testJson2 = testJson[i].subItems;
            
            for (var j = 0; j < testJson2.length; j++) {
                // alert(testJson2[j].subTitle);
                var subItem = testJson2[j].subItem;
				
                var checkValue = testJson2[j].checkValue;               
                var checkHtml = "";
                
                testJson2[j].yes == "Y" ? checkHtml = checkHtml + "<tr><td><input type='radio' value='Y' name='sub_" + DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "' " + (DOMPurify.sanitize(checkValue) == "Y" ? "checked" : "") + " /></td>" : checkHtml = checkHtml + "<tr><td></td>";
                testJson2[j].no == "Y" ? checkHtml = checkHtml + "<td><input type='radio' value='N' name='sub_" + DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "' " + (DOMPurify.sanitize(checkValue) == "N" ? "checked" : "") + " /></td>" : checkHtml = checkHtml + "<td></td>";
                //testJson2[j].tbd == "Y" ? checkHtml = checkHtml + "<td><input type='radio' value='T' name='sub_" + group + "_" + subItem + "' " + (checkValue == "T" ? "checked" : "") + " /></td>" : checkHtml = checkHtml + "<td></td>";
                testJson2[j].na == "Y" ? checkHtml = checkHtml + "<td><input type='radio' value='NA' name='sub_" + DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "' " + (DOMPurify.sanitize(checkValue) == "NA" ? "checked" : "") + " /></td>" : checkHtml = checkHtml + "<td></td>";
                checkHtml = checkHtml + "<td><span class='field' name='subTitle_" + DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "' id='subTitle_" + DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "'>" + DOMPurify.sanitize(testJson2[j].subTitle) + "</span><input type='hidden' name='subRejectVal_" + DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "' value='" + DOMPurify.sanitize(testJson2[j].subRejectVal) + "'></td></tr>";
                html = html + checkHtml;
            }
            
        }
        
        $("#checkListTable").append(html)
        
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
            $("input[name^='sub_'][type='radio']").prop("disabled", true)
        }
        
    }
    
    
    $("#importNewList").click(function(){
        //l250m01a.message.13=引入最新檢核項目將會清除現有查核項目資料，是否繼續？
        promptUI(i18n.lms2501m01['l250m01a.message.13']).done(function(){
            $("#checkListTable").empty();
            $.ajax({
                handler: "lms2501m01formhandler",
                data: {
                    formAction: "genList"
                }
			}).done(function(obj){
				ilog.debug(obj.checkList);

				/* ① 後端可能給字串，先轉成陣列 -------------- */
				var testJson = obj.checkList;
				if (typeof testJson === "string") {
				    try { testJson = JSON.parse(testJson); } catch (e) { testJson = []; }
				}
				/* ② 保險：如果不是陣列直接結束 ------------ */
				if (!Array.isArray(testJson) || !testJson.length) { return; }

				var html = "";
				for (var i = 0; i < testJson.length; i++) {

				    var group      = testJson[i].group;
				    var showHead   = testJson[i].showHead;
				    var testJson2  = testJson[i].subItems || [];   // ← subItems 可能為 undefined

				    html += "<tr><td colspan='5' class='hd2'><span class='field' " +
				            "name='group_" + DOMPurify.sanitize(group) + "' " +
				            "id='group_"   + DOMPurify.sanitize(group) + "'>" +
				            DOMPurify.sanitize(testJson[i].groupTitle) + " </span></td></tr>";

				    if (showHead === "Y") {
				        html += "<tr><td width='6%'><span class='field' name='yesTitle_" + DOMPurify.sanitize(group) + "'>" +
				                DOMPurify.sanitize(testJson[i].yesTitle) + "</span></td>" +
				                "<td width='6%'><span class='field' name='noTitle_"   + DOMPurify.sanitize(group) + "'>" +
				                DOMPurify.sanitize(testJson[i].noTitle)  + "</span></td>" +
				                "<td width='6%'><span class='field' name='naTitle_"   + DOMPurify.sanitize(group) + "'>" +
				                DOMPurify.sanitize(testJson[i].naTitle)  + "</span></td><td></td></tr>";
				    }

				    /* -------- subItems 迴圈 -------- */
				    for (var j = 0; j < testJson2.length; j++) {
				        var subItem    = testJson2[j].subItem;
				        var checkValue = testJson2[j].checkValue;
				        var checkHtml  = "";

				        testJson2[j].yes == "Y"
				            ? checkHtml += "<tr><td><input type='radio' value='Y' name='sub_" +
				                           DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "'" +
				                           (DOMPurify.sanitize(checkValue) == "Y"  ? " checked" : "") + " /></td>"
				            : checkHtml += "<tr><td></td>";

				        testJson2[j].no == "Y"
				            ? checkHtml += "<td><input type='radio' value='N' name='sub_" +
				                           DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "'" +
				                           (DOMPurify.sanitize(checkValue) == "N" ? " checked" : "") + " /></td>"
				            : checkHtml += "<td></td>";

				        testJson2[j].na == "Y"
				            ? checkHtml += "<td><input type='radio' value='NA' name='sub_" +
				                           DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(subItem) + "'" +
				                           (DOMPurify.sanitize(checkValue) == "NA" ? " checked" : "") + " /></td>"
				            : checkHtml += "<td></td>";

				        checkHtml += "<td><span class='field' name='subTitle_" + DOMPurify.sanitize(group) + "_" +
				                     DOMPurify.sanitize(subItem) + "' id='subTitle_" + DOMPurify.sanitize(group) + "_" +
				                     DOMPurify.sanitize(subItem) + "'>" +
				                     DOMPurify.sanitize(testJson2[j].subTitle) +
				                     "</span><input type='hidden' name='subRejectVal_" + DOMPurify.sanitize(group) + "_" +
				                     DOMPurify.sanitize(subItem) + "' value='" +
				                     DOMPurify.sanitize(testJson2[j].subRejectVal) + "'></td></tr>";

				        html += checkHtml;
				    }
				}
				$("#checkListTable").append(html);
                
				
				
            });
        });
        
        
    });
    
    var promptUI = function(msg){
        var deferred = $.Deferred();
        if ($("#checkListTable").find("tr").length > 0) {
            API.confirmMessage(msg, function(result){
                if (result) {
                    deferred.resolve();
                }
            });
        }
        else {
            deferred.resolve();
        }
        return deferred.promise();
    }
    
    
    $("#allCheckY").click(function(){
        $("input[type='radio']").prop('checked', false);
        $("input[type='radio'][value='Y']").prop("checked", true)
    });
    
    $("#allCheckN").click(function(){
        $("input[type='radio']").prop('checked', false);
        $("input[type='radio'][value='N']").prop("checked", true)
    });
    
    $("#allCheckNA").click(function(){
        $("input[type='radio']").prop('checked', false);
        $("input[type='radio'][value='NA']").prop("checked", true)
    });
    
    (function(){
        //**增加radio可以點選一次就取消的功能
        $("input[type='radio']:checked").each(function(){
            $(this).prop('previousValue', 'checked');
        });
        
        $("input[type='radio']").on("click", function(){
            var previousValue = $(this).prop('previousValue');
            var name = $(this).prop('name');
            name = DOMPurify.sanitize(name);
            
            if (previousValue == 'checked') {
                $(this).prop('checked', false);
                $(this).prop('previousValue', false);
            }
            else {
                $("input[name=" + name + "]:radio").prop('previousValue', false);
                $(this).prop('previousValue', 'checked');
            }
        });
    })();
    
    
    
});
