<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">
				loadScript('pagejs/lrs/LMS1700S01Panel');
			</script>
			<!-- ====================================================================== -->
            <fieldset>
                <legend>
                    <th:block th:text="#{'doc.baseInfo'}">基本資訊</th:block>
                </legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                <tbody>
                    <tr>
                        <td width="15%" class="hd1"  >
                            <th:block th:text="#{'doc.branchName'}">分行名稱</th:block>
                        </td>
                        <td width="35%"  >
                            <span id="ownBrId"></span> &nbsp; <span id="ownBrName"></span>
                        </td>
                        <td width="15%" class="hd1">
                            <th:block th:text="#{'L170M01A.retrialDate'}">覆審日期</th:block>
                        </td>
                        <td width="35%">
                            <input type="text" size="8" maxlength="10" class="date" _requiredLength="10" id="retrialDate" name="retrialDate" />
                        </td>
                    </tr>
					<tr>
                        <td class="hd1">
                            <th:block th:text="#{'doc.docStatus'}">文件狀態</th:block>
                        </td>
                        <td >
                            <span id="status"></span> &nbsp;
                        </td>
                        <td  class="hd1">
                            <th:block th:text="#{'L170M01A.lastRetrialDate'}">上次覆審日期</th:block>
                        </td>
                        <td>
                            <input type="text" size="8" maxlength="10" class="date" _requiredLength="10" id="lastRetrialDate" name="lastRetrialDate" />
                        </td>
                    </tr>
					 
					<tr>
						<!--J-105-0287-001 修改Web e-Loan國內企金授信覆審系統-->
                        <td class="hd1" >
                        	<!--J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能-->
                        	<div class="showRealRpFg">
                               <th:block th:text="#{'L170M01A.realRpFg'}">本案是否為實地覆審報告表</th:block>
							</div>
                        </td>
                        <td >
                        	<!--J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能-->
                        	<div class="showRealRpFg">
	                            <input type="radio" id="realRpFg" name="realRpFg" value="Y"><th:block th:text="#{'yes'}">是</th:block>&nbsp;&nbsp;&nbsp;&nbsp;
								<input type="radio" id="realRpFg" name="realRpFg" value="N"><th:block th:text="#{'no'}">否</th:block>
								<br>
								<br>
								<table>
									<tr>
										<td width="50%">
											<th:block th:text="#{'L170M01A.realCkFg'}">覆審控制檔實地覆審註記</th:block>
											<br/><button type="button" id="btn_impRealCkData">引進</button>
										</td>
										<td width="50%">
											<input type="radio" id="realCkFg" name="realCkFg" value="Y" disabled="disabled"><th:block th:text="#{'yes'}">是</th:block> 
								            <input type="radio" name="realCkFg" value="N" disabled="disabled"><th:block th:text="#{'no'}">否</th:block>
											<input type="radio" name="realCkFg" value="X" disabled="disabled">N.A.
										</td>
									</tr>
									<tr>
										<td>
											<th:block th:text="#{'L170M01A.realDt'}">最近一次實地覆審日期</th:block>
										</td>
										<td>
											<span id="realDt" class="field"></span> 
										</td>
									</tr>
								</table>	
							</div> 
                        </td>
                        <td  class="hd1">
                            <th:block th:text="#{'L170M01A.projectNo'}">覆審案號</th:block>
                        </td>
                        <td>
                            <span id="projectNo"></span> &nbsp;
							<div><span class='color-red'>自行新增案件於編製完成上傳覆審控制檔後才自動給號</span></div>
                        </td>
                    </tr>
					<tr>
                        <td class="hd1" valign="top">
							<span><th:block th:text="#{'label.now'}">本次</th:block></span>
                        	<th:block th:text="#{'label.CreditGrade'}">信用評等</th:block>
                        </td>
                        <td valign="top">
                        	<button type="button" id="btn_impCreditGrade">
								引進信評資料
							</button>
                            <br/>
							<select id="CreditType" name="CreditType" space="true"></select>
							&nbsp;&nbsp;&nbsp;
							<select id="CreditGrade" name="CreditGrade" space="true"></select>
                        </td>
                        <td  class="hd1">
                            <th:block th:text="#{'label.custInfo'}">主要借款人</th:block>
							<br/><button type="button" id="btn_impData">引進資料</button>
                        </td>
                        <td>
                            <span id="custId"></span>&nbsp;
							<th:block th:text="#{'doc.idDup'}">重覆序號</th:block>：<span id="dupNo"></span>&nbsp;
							(<select id="typCd" name="typCd"></select>)&nbsp;
							<div id="custName"></div>&nbsp;
                        </td>
                    </tr>
					<tr>    
						<td class="hd1" valign="top">
                        	<span class="color-red"><th:block th:text="#{'label.ex'}">前次</th:block></span>
							<th:block th:text="#{'label.CreditGrade'}">信用評等</th:block>
                        </td>
                        <td valign="top">
							<select id="exCreditType" name="exCreditType" space="true"></select>
							&nbsp;&nbsp;&nbsp;
							<select id="exCreditGrade" name="exCreditGrade" space="true"></select>
                        </td>                   
                        <td  class="hd1">
                            <th:block th:text="#{'L170M01A.chairman'}">負責人</th:block>
							<br/><button type="button" id="btn_impChairman">引進</button>
                        </td>
                        <td>
                            <input type="text" id="chairman" name="chairman" maxlength='60' maxlengthC='20' size='45'>&nbsp;							
                        </td>
                    </tr>
					<tr>
                        <td class="hd1" valign="top">
                        	<span><th:block th:text="#{'label.now'}">本次</th:block></span>
                            <th:block th:text="#{'label.MowGrade'}">信用風險內部評等</th:block>
                        </td>
                        <td valign="top">
            				<select id="MowType" name="MowType" space="true"></select>
							&nbsp;&nbsp;&nbsp;
							<select id="MowGrade" name="MowGrade" space="true"></select>
                        </td>
                        <td  class="hd1">
                            <th:block th:text="#{'label.tradeType'}">0024行業別</th:block>
							<br/><button type="button" id="btn_impTradeType">引進</button>
                        </td>
                        <td valign="top">
                            <span id="tradeType"></span>&nbsp;
							<div>(<span id="busCd_bussKind"></span>)</div>
                        </td>
                    </tr>
					<tr>
						<td class="hd1" valign="top">
							<span class="color-red"><th:block th:text="#{'label.ex'}">前次</th:block></span>
							<th:block th:text="#{'label.MowGrade'}">信用風險內部評等</th:block>
                        </td>
                        <td valign="top">
            				<select id="exMowType" name="exMowType" space="true"></select>
							&nbsp;&nbsp;&nbsp;
							<select id="exMowGrade" name="exMowGrade" space="true"></select>
                        </td>
                        <td  class="hd1">
                        	資信簡表行業別
							<br/><button type="button" id="btn_impCesBizMode">引進 Web徵信</button>
                        </td>
                        <td >
                            <span id="cesBizInfo"></span>&nbsp;
                        </td>
                    </tr>
					<tr>
                        <td class="hd1" rowspan="2" valign="top">
                        	<span><th:block th:text="#{'label.now'}">本次</th:block></span>
                            <th:block th:text="#{'label.Fcrd'}">外部評等類別</th:block>
                        </td>
                        <td rowspan="2" valign="top">
                        	<table width='100%'>
                        		<tr>
                        			<td class="noborder" width='25%'><th:block th:text="#{'label.FcrdType'}">類別</th:block></td>
									<td class="noborder"><select id="FcrdType" name="FcrdType" space="true"></select></td>
								</tr>
								<tr class="tr_fcrd">
									<td class="noborder"><th:block th:text="#{'label.FcrdArea'}">地區別</th:block></td>
									<td class="noborder"><select id="FcrdArea" name="FcrdArea" space="true"></select></td>
								</tr>
								<tr class="tr_fcrd">
									<td class="noborder"><th:block th:text="#{'label.FcrdPred'}">期間別</th:block></td>
									<td class="noborder"><select id="FcrdPred" name="FcrdPred" space="true"></select></td>
								</tr>
								<tr class="tr_fcrd">
									<td class="noborder">
										<th:block th:text="#{'label.FcrdGrad'}">評等等級</th:block>
										<br/><button type="button" id="btn_fcrdGrad"><span class="text-only"><th:block th:text="#{'login'}">登錄</th:block></span></button>
									</td>
									<td class="noborder">
										<span id="FcrdGrad" name="FcrdGrad" class="field" ></span>
									</td>
								</tr>
							</table>	
                        </td>
                        <td  class="hd1">
                            <th:block th:text="#{'label.mLoanPerson'}">符合授信額度標準</th:block>
							<br/><button type="button" id="btn_impmLoanPerson">引進</button>
                        </td>
                        <td>
                            <input type="radio" id="mLoanPerson" name="mLoanPerson" value="Y"><th:block th:text="#{'yes'}">是</th:block>&nbsp;&nbsp;&nbsp;&nbsp;
							<input type="radio" id="mLoanPerson" name="mLoanPerson" value="N"><th:block th:text="#{'no'}">否</th:block>
                        </td>
                    </tr>
					<tr>
                        <td class="hd1">
                        	<th:block th:text="#{'L170M01A.mLoanPersonA'}">主要授信戶</th:block>
							<br/><button type="button" id="btn_impmLoanPersonA">引進</button>
                        </td>
                        <td >
                            <input type="radio" id="mLoanPersonA" name="mLoanPersonA" value="Y"><th:block th:text="#{'yes'}">是</th:block>&nbsp;&nbsp;&nbsp;&nbsp;
							<input type="radio" id="mLoanPersonA" name="mLoanPersonA" value="N"><th:block th:text="#{'no'}">否</th:block>
                        </td>
                    </tr>
					<tr>
						<td class="hd1" rowspan="2" valign="top">
							<span class="color-red"><th:block th:text="#{'label.ex'}">前次</th:block></span>
                            <th:block th:text="#{'label.Fcrd'}">外部評等類別</th:block>
                        </td>
                        <td rowspan="2" valign="top">
                        	<table width='100%'>
                        		<tr>
                        			<td class="noborder" width='25%'><th:block th:text="#{'label.FcrdType'}">類別</th:block></td>
									<td class="noborder"><select id="exFcrdType" name="exFcrdType" space="true"></select></td>
								</tr>
								<tr class="tr_exfcrd">
									<td class="noborder"><th:block th:text="#{'label.FcrdArea'}">地區別</th:block></td>
									<td class="noborder"><select id="exFcrdArea" name="exFcrdArea" space="true"></select></td>
								</tr>
								<tr class="tr_exfcrd">
									<td class="noborder"><th:block th:text="#{'label.FcrdPred'}">期間別</th:block></td>
									<td class="noborder"><select id="exFcrdPred" name="exFcrdPred" space="true"></select></td>
								</tr>
								<tr class="tr_exfcrd">
									<td class="noborder">
										<th:block th:text="#{'label.FcrdGrad'}">評等等級</th:block>
										<br/><button type="button" id="btn_exfcrdGrad"><span class="text-only"><th:block th:text="#{'login'}">登錄</th:block></span></button>
									</td>
									<td class="noborder">
										<span id="exFcrdGrad" name="exFcrdGrad" class="field" ></span>
									</td>
								</tr>
							</table>	
                        </td>
                        <td  class="hd1">
                            <th:block th:text="#{'L170M01A.rltGuarantor'}">保證人</th:block>
							<br/><button type="button" id="btn_impRltGuarantor">引進</button>
                        </td>
                        <td>
                        	<input type="radio" id="freeG" name="freeG" value="Y" class='required'><th:block th:text="#{'L170M01A.freeG.Y'}">人工輸入</th:block>
							<input type="radio" id="freeG" name="freeG" value="N" class='required'><th:block th:text="#{'L170M01A.freeG.N'}">系統引進</th:block>
							<div id="div_M01H_G_FREE" style='display:none;'>
                        		<textarea id="rltGuarantor_free" name="rltGuarantor_free" style="width:95%;height:80px" maxlength='900' maxlengthC='300'>
								</textarea>
							</div>
							<div id="div_M01H_G_SYS" style='display:none;'>
                            	<span id="rltGuarantor" ></span>&nbsp;
							</div>
                        </td>
                    </tr>
					<tr>
                        <td  class="hd1">
                            <th:block th:text="#{'label.rltBorrower'}">共同借款人</th:block>
							<br/><button type="button" id="btn_impRltBorrower" style='font-size:10px;'>引進</button>
                        </td>
                        <td>
                        	<input type="radio" id="freeC" name="freeC" value="Y" class='required'><th:block th:text="#{'L170M01A.freeC.Y'}">人工輸入</th:block>
							<input type="radio" id="freeC" name="freeC" value="N" class='required'><th:block th:text="#{'L170M01A.freeC.N'}">系統引進</th:block>
							<div id="div_M01H_C_FREE" style='display:none;'>
                        		<textarea id="rltBorrower_free" name="rltBorrower_free" style="width:95%;height:80px" maxlength='900' maxlengthC='300'>
								</textarea>
							</div>
							<div id="div_M01H_C_SYS" style='display:none;'>
                            	<span id="rltBorrower" ></span>
							</div>	
							&nbsp;
                        </td>
                    </tr>
				</tbody>
				</table>
				<th:block th:if="${show_nckdInfo}">
					<div>
						<span class='text-red'><b><th:block th:text="#{'label.nckdInfo'}">免覆審註記</th:block>：</b></span>
				 		<span id="nckdInfo"  class='text-red' ></span>&nbsp;
					</div>
				</th:block>
			</fieldset>
            <!-- ====================================================================== -->
            <fieldset>
                <legend>
                    <th:block th:text="#{'doc.docUpdateLog'}">文件異動紀錄</th:block>
                </legend>
			
                <div class="funcContainer">
                	<!-- 文件異動記錄 -->
                    <div th:insert="~{common/panels/DocLogPanel :: DocLogPanel}"></div>  
                </div>
				
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'doc.creator'}">文件建立者</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="30%">
                                <span id="creator"></span>(<span id="createTime"></span>)
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.lastUpdater'}">最後異動者</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="updater"></span>(<span id="updateTime"></span>)
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                            </td>
                            <td>
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.docCode'}">
                                    報表亂碼
                                </th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="randomCode"></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </fieldset>
            
			<!-- ================================================== -->	
			
			<div id="_div_fcrdGrad" style="display:none">
				<div id="grid_fcrdGrad">
				</div>
			</div>
			
        </th:block>
    </body>
</html>
