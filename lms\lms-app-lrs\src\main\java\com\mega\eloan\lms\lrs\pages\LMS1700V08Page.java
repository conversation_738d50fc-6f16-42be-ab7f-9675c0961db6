package com.mega.eloan.lms.lrs.pages;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.lrs.panels.LMS1700FilterPanel;

import tw.com.jcs.auth.AuthType;

/**
 * 已覆核未核定
 */
@Controller
@RequestMapping("/lrs/lms1700v08")
public class LMS1700V08Page extends AbstractEloanInnerView {

	public LMS1700V08Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(RetrialDocStatusEnum.已覆核未核定);
		
		List<EloanPageFragment> list = new ArrayList<>();
		
		//J-111-0154 覆審報告表結果提供 篩選 功能
		list.addAll(Arrays.asList(LmsButtonEnum.Filter, LmsButtonEnum.Print));
		
		if (this.getAuth(AuthType.Accept)) {
			list.add(LmsButtonEnum.AllSend);
		}		
		
		addToButtonPanel(model, list);
		renderJsI18N(LMS1700V01Page.class);
		renderJsI18N(LMS1700M01Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "require(['pagejs/lrs/LMS1700FilterPanel'],function(){loadScript('pagejs/lrs/LMS1700V01Page');});");
		setupIPanel(new LMS1700FilterPanel(PANEL_ID, true), model, params);
	}
	
	protected void addPanel(ModelMap model, PageParameters params, String panelId) {
		new LMS1700FilterPanel(panelId, true).processPanelData(model, params);
	}
	
}
