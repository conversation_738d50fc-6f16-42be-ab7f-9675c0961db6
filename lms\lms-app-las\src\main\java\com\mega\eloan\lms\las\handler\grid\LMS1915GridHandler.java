package com.mega.eloan.lms.las.handler.grid;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.las.service.LMS1905Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192S01A;

@Scope("request")
@Controller("lms1915gridhandler")
public class LMS1915GridHandler extends AbstractGridHandler {

	@Resource
	LMS1905Service lms1915Service;

	@Resource
	MisPTEAMAPPService misPteamappService;

	@Resource
	MisdbBASEService misdbBASEService;

	public CapMapGridResult getBorrows(ISearch pageSetting,
			PageParameters params) throws CapException {

		CapMapGridResult gridResult = new CapMapGridResult();

		String branch = params.getString("brId");
		String custId = params.getString("custId");
		String test = params.getString("test");

		List<Map<String, Object>> gridList = new LinkedList<Map<String, Object>>();

		List<Map<String, Object>> mainData = misPteamappService
				.getDataByCustId(custId);

		for (Map<String, Object> data : mainData) {
			Map<String, Object> detail = new HashMap<String, Object>();
			String grpCntrNo = (String) data.get("grpCntrNo");
			String projectNm = (String) data.get("projectNm");
			detail.put("grpCntrNo", grpCntrNo);
			detail.put("projectNm", projectNm);
			gridList.add(detail);
		}
		gridResult.setColumns(new String[] { "grpCntrNo", "projectNm" });
		gridResult.setRowData(gridList);
		gridResult.setRecords(gridList.size());
		return gridResult;
	}

	public CapMapGridResult getBorrows2(ISearch pageSetting,
			PageParameters params) throws CapException {

		CapMapGridResult gridResult = new CapMapGridResult();

		String grpCntrNo = params.getString("grpCntrNo");
		String brNo = params.getString("brNo");

		List<Map<String, Object>> gridList = new LinkedList<Map<String, Object>>();

		List<Map<String, Object>> mainData = misdbBASEService
				.findGroupLoanByContractBrNo(grpCntrNo, brNo);

		for (Map<String, Object> data : mainData) {
			Map<String, Object> detail = new HashMap<String, Object>();
			String custId = (String) data.get("LNF030_CUST_ID");
			String name = (String) data.get("cName");
			String bal = CapString.trimNull(((BigDecimal) data.get("LNF030_LOAN_BAL")));
			detail.put("custId", custId);
			detail.put("name", name);
			detail.put("bal", bal);
			gridList.add(detail);
		}
		gridResult.setColumns(new String[] { "custId", "name", "bal" });
		gridResult.setRowData(gridList);
		gridResult.setRecords(gridList.size());
		return gridResult;
	}

	/**
	 * 取得團貸業務工作底稿的 保證人和借款人資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL192M01B(ISearch pageSetting,
			PageParameters params) throws CapException {

		logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));

		String custType = params.getString("custType");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custType",
				custType);

		Page<L192M01B> page = lms1915Service.getL192M01B(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得團貸業務工作底稿的 保證人資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL192M01B2(ISearch pageSetting,
			PageParameters params) throws CapException {

		logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));

		String custType = params.getString("custType");
		String mainCustId = params.getString("mainCustId");
		String mainDupNo = params.getString("mainDupNo");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custType",
				custType);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainCustId",
				mainCustId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainDupNo",
				mainDupNo);

		Page<L192M01B> page = lms1915Service.getL192M01B(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得團貸工作底稿 - 申請內容(帳務資料)(BY mainCustId)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL192S01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainCustId",
				params.getString("mainCustId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainDupNo",
				params.getString("mainDupNo"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "l192m01b.custType", "1");

		Page<L192S01A> page = lms1915Service.getL192S01A(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
}
