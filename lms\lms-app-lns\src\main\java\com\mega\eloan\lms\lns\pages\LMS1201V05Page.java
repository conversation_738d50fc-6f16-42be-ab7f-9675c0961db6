/* 
 * LMS1201V05Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import java.util.ArrayList;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;

/**
 * <pre>
 * 授信簽報書呈授管處 / 營運中心
 * </pre>
 * 
 * @since 2011/11/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/9,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1201v05")
public class LMS1201V05Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		// if (this.getAuth(params, AuthType.Accept)) {
		// // 主管權限時要顯示的按鈕...
		// add(new CreditButtonPanel("_buttonPanel", null,
		// CreditButtonEnum.View, CreditButtonEnum.Filter));
		// } else {
		// // 否則需要顯示的按鈕
		// // 加上Button
		// add(new CreditButtonPanel("_buttonPanel", null,
		// CreditButtonEnum.View, CreditButtonEnum.Change,
		// CreditButtonEnum.TableSend, CreditButtonEnum.PrintBook,
		// CreditButtonEnum.Filter));
		// }

		// 加上Button
		ArrayList<LmsButtonEnum> btns = new ArrayList<LmsButtonEnum>();
		// 主管跟經辦都會出現的按鈕
		btns.add(LmsButtonEnum.View);
		btns.add(LmsButtonEnum.Filter);
		btns.add(LmsButtonEnum.ModifyCaseLvl);
		
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.Change);
			btns.add(LmsButtonEnum.TableSend);
			btns.add(LmsButtonEnum.PrintBook);
		}
		
		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
			// J-107-0390_05097_B1001 分行權限之授信案件若於覆核後欲修改,得授權主管得退回至編製中
			btns.add(LmsButtonEnum.BackApprove);

		}
		
		addToButtonPanel(model, btns.toArray(new LmsButtonEnum[] {}));
		// 套用哪個i18N檔案
		renderJsI18N(LMS1201V01Page.class);
		
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS1201V01Page');");
		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);
	}// ;

}
