<html xmlns:th="http://www.thymeleaf.org">
    <body>
        <!--頁面的Header-->
        <div th:fragment="header">
	        <script>
				function getHDLink(){                
	                if (typeof helpDeskUser == 'undefined') {
	                    helpDeskUser = '';
	                }
	                document.getElementById('hdlink').href = validPath(webroot) +
	                '/app/helpdesk?input={"type":2,"uid":"' + helpDeskUser + '","errcode":"EFD99999","pmap":{}}';
	            }                    
	        </script>
	        <ul class="block header">
	            <li><span class="header_logo"></span></li>
	            <li>
					<span class="system_info">
						<th:block th:utext="#{AbstractEloanPage.systemname}"></th:block>(<span th:text="${branch}"></span>)<br/>
						<span class="subinfo" th:text="${subSystemName}"/></span>
					</span>
				</li>
	            <li>
	            	<span class="system_msg ui-corner-all">
	                	<table style="font-size: 12px;">
	                        <tr>
	                            <td class="title"><b>‧</b><span th:remove="tag" th:text="#{EloanHome.header.username}">登錄行員：</span></td>
	                            <td class="info"><span th:text="${username}" style="width: 120px;">翁小芳（03738)</span></td>
	                            <td class="title"><b>‧</b><span th:remove="tag" th:text="#{EloanHome.header.position}">授權等級：</span></td>
	                            <td class="info"><a class="tip" href="#"><span th:text="${position}">乙級主管</span>
								<p><span th:utext="${ssoRoleList}">SSO角色清單</span></p></a></td>
	                        </tr>
	                        <tr>
	                            <td class="title"><b>‧</b><span th:remove="tag" th:text="#{EloanHome.header.loginTime}">登錄日期：</span></td>
	                            <td class="info"><span th:text="${loginTime}">2011/03/31 10:20:30</span></td>
								<td class="title" colspan="2"><b>‧</b><a href="#" onclick="getHDLink();" id="hdlink" target="_blank"><B>HelpDesk系統</B></a></td>														
	                        </tr>
							<div th:remove="tag" th:if="${isShowSysLocAndLang2_visible}">
	                        <tr>
	                            <td class="title"><b>‧</b><span th:remove="tag" th:text="#{EloanHome.header.systems}">相關系統：</span></td>
	                            <td class="info" >
	                                <select name="elSystem" id="elSystem">
	                                </select>								
	                            </td>
	   							<div th:remove="tag" th:if="${locale_visible}">
		                            <td class="title"><b>‧</b><span class="hide" th:remove="tag" th:text="#{EloanHome.header.locale}">語 系：</span></td>
		                            <td class="info">
		                                <select id="site_locale" name="site_locale">
		                                    <option value="zh_TW">繁體中文</option>
		                                    <option value="zh_CN">简体中文</option>
		                                    <option value="en">English</option>
		                                </select>
		                            </td>
	                            </div>
	                        </tr>
	                    </table>
					</span>
	            </li>
	        </ul>
	        <div class="clear"></div>
		</div>
    	<div th:fragment="headerEmpty"></div>
    </body>
</html>