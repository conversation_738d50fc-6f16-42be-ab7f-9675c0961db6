/* 
 * LMS1205GridHandler.java 
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.grid;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.MEGAImageApiEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.CustIdFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.MEGAImageService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CMSDocStatusEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.RelatedAccountPanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1141M01Page;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.cls.panels.CLS1201S27Panel;
import com.mega.eloan.lms.cls.panels.CLSS07APanel;
import com.mega.eloan.lms.cls.report.impl.CLS1141R01RptServiceImpl;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbCOLBASEService;
import com.mega.eloan.lms.enums.PeriodTypeEnum;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.model.C100M01;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C140M01A;
import com.mega.eloan.lms.model.C140M04A;
import com.mega.eloan.lms.model.C140S04A;
import com.mega.eloan.lms.model.C140S04B;
import com.mega.eloan.lms.model.C140S04C;
import com.mega.eloan.lms.model.C140S09A;
import com.mega.eloan.lms.model.C140S09B;
import com.mega.eloan.lms.model.C140S09C;
import com.mega.eloan.lms.model.C140S09D;
import com.mega.eloan.lms.model.C140S09E;
import com.mega.eloan.lms.model.C140S09F;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01R;
import com.mega.eloan.lms.model.L120S03A;
import com.mega.eloan.lms.model.L120S04A;
import com.mega.eloan.lms.model.L120S05B;
import com.mega.eloan.lms.model.L120S05D;
import com.mega.eloan.lms.model.L120S06A;
import com.mega.eloan.lms.model.L120S18A;
import com.mega.eloan.lms.model.L120S19B;
import com.mega.eloan.lms.model.L130S01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01P;
import com.mega.eloan.lms.model.L140M01R;
import com.mega.eloan.lms.model.L140MC2A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L720M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書Grid
 * </pre>
 * 
 * @since 2011/8/1
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/8/1,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("cls1141gridhandler")
public class CLS1141GridHandler extends AbstractGridHandler {

	@Resource
	LMSService lmsService;
	@Resource
	CLS1141Service cls1141Service;

	@Resource
	CodeTypeService codeservice;

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branchService;

	@Resource
	DocFileService docfileservice;
	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	RelatedAccountService relatedAccountService;

	private final String DATEYYYYMMDD = "yyyy-MM-dd";

	@Resource
	EloandbCOLBASEService colService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	CLSService clsService;

	@Resource
	AMLRelateService amlRelateService;
	
	@Resource
	MEGAImageService mEGAImageService;

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		String custId = Util.trim(params.getString("custId"));
		String typCd = Util.trim(params.getString("typCd"));
		// String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));

		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		// J-104-0066-001 Web e-Loan授信系統授管處可以看到全行授權內、外簽報書。
		// 去篩選簽報書統編、額度、MAINID 符合者
		List<String> l120m01a_maindId_list = lmsService
				.getL120M01AMainIdByFilterForm(params); // 額度明細表符合 統編、額度、MAINID

		if (l120m01a_maindId_list.size() > 20000) {
			if (Util.isNotEmpty(custName)) {
				// 筆數太多會掛掉
				// 戶名符合條件筆數太多l120m01a_maindId_list.size()，請增加篩選條件後再執行本作業。
				throw new CapMessageException("簽報書基本查詢條件符合筆數太多" + "("
						+ Util.trim(l120m01a_maindId_list.size()) + "筆)"
						+ "，請增加簽報書篩選條件後再執行本作業", getClass());

			}

		}

		// 之簽報書MAINID
		// if (Util.equals(custId, "")) {
		// if (l120m01a_maindId_list.size() > 0) {
		// pageSetting.addSearchModeParameters(SearchMode.IN, "mainId",
		// l120m01a_maindId_list);
		// }
		// } else {
		// // 篩選條件有統編時
		// if (l120m01a_maindId_list.size() > 0) {
		// pageSetting.addSearchModeParameters(SearchMode.OR,
		// new SearchModeParameter(SearchMode.IN, "mainId",
		// l120m01a_maindId_list),
		// new SearchModeParameter(SearchMode.EQUALS, "custId",
		// custId));
		// } else {
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
		// "custId", custId);
		// }
		// }

		if (Util.equals(custId, "") && Util.equals(custName, "")) {
			if (l120m01a_maindId_list.size() > 0) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "mainId",
						l120m01a_maindId_list);
			}
		} else {
			// 篩選條件有統編、戶名時
			if (l120m01a_maindId_list.size() > 0) {

				if (Util.isNotEmpty(custId) && Util.isNotEmpty(custName)) {
					pageSetting.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.IN, "mainId",
									l120m01a_maindId_list),
							new SearchModeParameter(SearchMode.AND,
									new SearchModeParameter(SearchMode.EQUALS,
											"custId", custId),
									new SearchModeParameter(SearchMode.LIKE,
											"custName", custName + "%")));
				} else {
					if (Util.isNotEmpty(custId)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN,
										"mainId", l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.EQUALS,
										"custId", custId));
					}
					if (Util.isNotEmpty(custName)) {
						pageSetting.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.IN,
										"mainId", l120m01a_maindId_list),
								new SearchModeParameter(SearchMode.LIKE,
										"custName", custName + "%"));
					}
				}

			} else {
				if (Util.isNotEmpty(custId)) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"custId", custId);
				}
				if (Util.isNotEmpty(custName)) {
					pageSetting.addSearchModeParameters(SearchMode.LIKE,
							"custName", custName + "%");
				}

			}
		}

		// 當為授管處簽案時要加上此條件
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& (UnitTypeEnum.分行.equals(UnitTypeEnum.convertToUnitType(user
						.getUnitType())) || UnitTypeEnum.國金部
						.equals(UnitTypeEnum.convertToUnitType(user
								.getUnitType())))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					UtilConstants.BankNo.授管處);
		}
		switch (docStatusEnum) {
		case 海外_編製中:
			if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
				String[] showDoc = new String[] {
						CreditDocStatusEnum.海外_編製中.getCode(),
						CreditDocStatusEnum.會簽後修改編製中.getCode() };
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						showDoc);
				pageSetting
						.addSearchModeParameters(SearchMode.OR,
								new SearchModeParameter(SearchMode.EQUALS,
										"hqMeetFlag", "0"),
								new SearchModeParameter(SearchMode.OR,
										new SearchModeParameter(
												SearchMode.IS_NULL,
												"hqMeetFlag", ""),
										new SearchModeParameter(
												SearchMode.EQUALS,
												"hqMeetFlag",
												UtilConstants.Mark.SPACE)));
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			break;
		case 海外_待覆核:
			if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
				String[] showDoc = new String[] {
						CreditDocStatusEnum.海外_待覆核.getCode(),
						CreditDocStatusEnum.會簽後修改待覆核.getCode(),
						CreditDocStatusEnum.總處營業單位待覆核.getCode() };
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						showDoc);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			break;
		case 海外_呈總行:

			// 當登錄分行為澳洲 ，分行呈總行文件狀態 後為提會待登錄 或提會帶覆核
			// IBranch branchtype = branch.getBranch(user.getUnitNo());
			// if (UtilConstants.Country.澳洲.equals(branchtype.getCountryType())
			// || UtilConstants.Country.加拿大.equals(branchtype
			// .getCountryType())
			// || UtilConstants.Country.泰國.equals(branchtype
			// .getCountryType())) {
			String[] showDoc = new String[] {
					CreditDocStatusEnum.海外_待覆核.getCode(),
					CreditDocStatusEnum.海外_總行提會待登錄.getCode(),
					CreditDocStatusEnum.海外_總行提會待覆核.getCode(),
					CreditDocStatusEnum.泰國_提會待登錄.getCode(),
					CreditDocStatusEnum.泰國_提會待覆核.getCode() };
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					showDoc);
			// } else {
			// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
			// "docStatus", CreditDocStatusEnum.海外_待覆核.getCode());
			// }
			break;
		case 海外_呈授管處:
			// 當狀態為海外_呈授管處時要變更搜尋條件要文件狀態為C或H結尾都要顯示，並且join授權檔
			pageSetting
					.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%H"),
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%C"));
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "docStatus", CreditDocStatusEnum.授管處_待陳復.getCode());
			break;
		case 特殊分行提授審會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行授審會);
			break;
		case 特殊分行提催收會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行逾審會);
			break;
		case 特殊分行提常董會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行常董會);
			break;
		case 特殊分行提審計委員會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行審計委員會);
			break;
		case 總處營業單位已會簽:
			// 已會簽在提授審會時不能不能出現此案件
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "hqMeetFlag",
							null), new SearchModeParameter(SearchMode.EQUALS,
							"hqMeetFlag", "0"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			break;
		case 營運中心_呈總處:
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "docStatus",
					"%H");

			break;

		case 營運中心_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "S"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "A")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));

			break;

		case 授管處_待收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"hqReceiveDate", "");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
			break;
		case 授管處_已收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"hqReceiveDate", "");
			break;
		case 授管處_審查中:
			// 審查中在提授審會時不能不能出現此案件
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "hqMeetFlag",
							null), new SearchModeParameter(SearchMode.EQUALS,
							"hqMeetFlag", "0"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			break;
		case 授管處_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "C"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "S")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));
			break;
		case 海外_已核准:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
			// 如果登錄的是總行要多看到 泰國的提會待登錄、和泰國提會待覆核的案件
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				// 已核准案件要排除陳覆陳述案，
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						new String[] { CreditDocStatusEnum.海外_已核准.getCode(),
								CreditDocStatusEnum.泰國_提會待登錄.getCode(),
								CreditDocStatusEnum.泰國_提會待覆核.getCode() });
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			break;
		case 海外_陳復案_陳述案:
			// 已核准 且 docCode = 3
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;

		case 授管處_免批覆案件:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;
		case 營運中心_所有提會案件:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"rptTitleArea1", "");
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"rptTitleArea1", "");
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}
		// 限定只顯示個金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);

		// if (Util.isNotEmpty(custId)) {
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
		// custId);
		// }

		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
		}
		if (Util.isNotEmpty(docKind)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		if (Util.isNotEmpty(docCode)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					docCode);
		}
		// if (Util.isNotEmpty(custName)) {
		// pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
		// custName + "%");
		// }
		if (Util.isNotEmpty(updater)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "updater",
					updater);
		}
		// if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
		// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
		// "approveTime", new Object[] { Util.parseDate(approveDateS),
		// Util.parseDate(approveDateE + " 23:59:59") });
		// }

		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(
					SearchMode.BETWEEN,
					"endDate",
					new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE) });
		}

		// Miller added at 2012/12/17
		boolean isReject = params.getBoolean("isReject");

		Date fromDate = null;
		Date endDate = null;
		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}
		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };

			if (docStatus.equals("03K|01K|02K|04K")) {
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"approveTime", reason);

			} else {
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"caseDate", reason);

			}

		}

		// if (CreditDocStatusEnum.海外_編製中.getCode().equals(docStatus)) {
		// // 排除掉異常通報案件
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
		// "docCode", UtilConstants.Casedoc.DocCode.異常通報);
		// }
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01A.class, pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)會簽Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult querySpectial(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 限定只顯示個金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);
//		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "areaChk",
//				UtilConstants.Casedoc.AreaChk.送會簽);
		pageSetting.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.EQUALS, "areaChk",
						UtilConstants.Casedoc.AreaChk.送會簽),
				new SearchModeParameter(SearchMode.EQUALS, "areaChk",
						UtilConstants.Casedoc.AreaChk.送初審));
		pageSetting.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.EQUALS, "docStatus",
						CreditDocStatusEnum.授管處_審查中.getCode()),
				new SearchModeParameter(SearchMode.EQUALS, "docStatus",
						CreditDocStatusEnum.授管處_待放行.getCode()));

		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01A.class, pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L120M01AGrid 資料(異常通報用)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a1(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		String custId = Util.trim(params.getString("custId"));
		String typCd = Util.trim(params.getString("typCd"));
		// String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		// String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));

		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 當為授管處簽案時要加上此條件
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& UnitTypeEnum.分行.equals(UnitTypeEnum.convertToUnitType(user
						.getUnitType()))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					UtilConstants.BankNo.授管處);
		}

		// 限定只顯示個金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);
		// 限定只顯示異常通報案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
				UtilConstants.Casedoc.DocCode.異常通報);
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
		}
		if (Util.isNotEmpty(docKind)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		if (Util.isNotEmpty(custName)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
					custName + "%");
		}
		if (Util.isNotEmpty(updater)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "updater",
					updater);
		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE + " 23:59:59") });
		}

		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01A.class, pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			// l120m01a.edit=修改中
			model.setReEstFlag("Y".equals(model.getReEstFlag()) ? pop
					.getProperty("l120m01a.edit") : "");
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getAreaAppraiser())) ? userservice
						.getUserName(model.getAreaAppraiser()) : Util
						.trim(model.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
			}
			// 「授管處負責經辦(hqAppraiser)」對應出員工姓名(不要出現null)
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(!Util.isEmpty(userservice
						.getUserName(model.getHqAppraiser())) ? userservice
						.getUserName(model.getHqAppraiser()) : Util.trim(model
						.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			// 當「營運中心放行時間(areaSendInfo)」有值時，顯示「營運中心放行時間」；其他則顯示「核准日期(approveTime)」
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setAreaSendInfo(model.getAreaSendInfo());
			} else {
				model.setAreaSendInfo(model.getApproveTime());
			}
			if (!Util.isEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L120M01AGrid 資料(授審、催收、常董)用
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120m01a3(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		String docStatus = params.getString("mainDocStatus");
		String custId = Util.trim(params.getString("custId"));
		String typCd = Util.trim(params.getString("typCd"));
		// String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));

		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		switch (docStatusEnum) {
		case 特殊分行提授審會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行授審會);
			break;
		case 特殊分行提催收會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行逾審會);
			break;
		case 特殊分行提常董會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行常董會);
			break;
		case 特殊分行提審計委員會:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.會簽後修改編製中.getCode()),
					new SearchModeParameter(SearchMode.EQUALS, "docStatus",
							CreditDocStatusEnum.總處營業單位已會簽.getCode()));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", UtilConstants.Casedoc.HqMeetFlag.特殊分行審計委員會);
			break;
		default:
			String kind = params.getString("kind");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"hqMeetFlag", kind);
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
				user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		// 限定只顯示個金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
		}
		if (Util.isNotEmpty(docKind)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		if (Util.isNotEmpty(docCode)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					docCode);
		}
		if (Util.isNotEmpty(custName)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
					custName + "%");
		}
		if (Util.isNotEmpty(updater)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "updater",
					updater);
		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE + " 23:59:59") });
		}

		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01A.class, pageSetting);
		try {
			for (int w = 0; w < params.getInt("rowNum"); w++) {
				L120M01A model = (L120M01A) page.getContent().get(w);
				StringBuilder strB = new StringBuilder();
				StringBuilder allCust = new StringBuilder();
				allCust.append(model.getCustId()).append(" ")
						.append(model.getDupNo());
				strB.append(
						("1".equals(model.getDocKind()) && "1".equals(model
								.getDocType())) ? pop
								.getProperty("L1205G.grid1") : ("2"
								.equals(model.getDocKind()) && "1".equals(model
								.getDocType())) ? pop
								.getProperty("L1205G.grid2") : ("1"
								.equals(model.getDocKind()) && "2".equals(model
								.getDocType())) ? pop
								.getProperty("L1205G.grid12") : pop
								.getProperty("L1205G.grid13")).append("(")
						.append(docCodeName(model.getDocCode())).append(")");
				model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
				model.setCustId(allCust.toString());
				// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
				model.setDocRslt(model.getDocKind());
				model.setDocKind(this.getCaseType(model, pop, strB));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
				model.setUpdater(!Util.isEmpty(userservice.getUserName(model
						.getUpdater())) ? userservice.getUserName(model
						.getUpdater()) : model.getUpdater());
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
				model.setHqAppraiser(getPerName(Util.trim(model
						.getHqAppraiser())));
				if (!Util.isEmpty(model.getCaseBrId())) {
					model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
							+ " "
							+ branchService.getBranchName(Util
									.nullToSpace(model.getCaseBrId())));
				}
			}
		} catch (java.lang.IndexOutOfBoundsException ex) {
			for (int w = 0; w < (page.getTotalRow() - params.getInt("rowNum")
					* (page.getPageSize() - 1)); w++) {
				L120M01A model = (L120M01A) page.getContent().get(w);
				model.setCustId(model.getCustId() + " " + model.getDupNo());
				StringBuilder strB = new StringBuilder();
				StringBuilder allCust = new StringBuilder();
				allCust.append(model.getCustId()).append(" ")
						.append(model.getDupNo());
				strB.append(
						("1".equals(model.getDocKind()) && "1".equals(model
								.getDocType())) ? pop
								.getProperty("L1205G.grid1") : ("2"
								.equals(model.getDocKind()) && "1".equals(model
								.getDocType())) ? pop
								.getProperty("L1205G.grid2") : ("1"
								.equals(model.getDocKind()) && "2".equals(model
								.getDocType())) ? pop
								.getProperty("L1205G.grid12") : pop
								.getProperty("L1205G.grid13")).append("(")
						.append(docCodeName(model.getDocCode())).append(")");
				model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
				model.setCustId(allCust.toString());
				// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
				model.setDocRslt(model.getDocKind());
				model.setDocKind(this.getCaseType(model, pop, strB));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
				model.setUpdater(!Util.isEmpty(userservice.getUserName(model
						.getUpdater())) ? userservice.getUserName(model
						.getUpdater()) : model.getUpdater());
				model.setAreaAppraiser(getPerName(Util.trim(model
						.getAreaAppraiser())));
				model.setHqAppraiser(getPerName(Util.trim(model
						.getHqAppraiser())));
				if (!Util.isEmpty(model.getCaseBrId())) {
					model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
							+ " "
							+ branchService.getBranchName(Util
									.nullToSpace(model.getCaseBrId())));
				}
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L121M01AGrid 資料(海外聯貸案Grid)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL121m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		Date fromDate = null;
		Date endDate = null;

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
		}

		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}
		switch (docStatusEnum) {
		case 營運中心_海外聯貸案_已會簽:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_已會簽.getCode());
			break;
		case 營運中心_海外聯貸案_待放行:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_待放行.getCode());
			break;
		case 營運中心_海外聯貸案_會簽中:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"areaDocstatus",
					CreditDocStatusEnum.營運中心_海外聯貸案_會簽中.getCode());
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01A.class, pageSetting);
		try {
			for (int w = 0; w < params.getInt("rowNum"); w++) {
				L120M01A model = (L120M01A) page.getContent().get(w);
				StringBuilder strB = new StringBuilder();
				StringBuilder allCust = new StringBuilder();
				allCust.append(model.getCustId()).append(" ")
						.append(model.getDupNo());
				strB.append(
						"1".equals(model.getDocKind()) ? pop
								.getProperty("L1205G.grid1") : pop
								.getProperty("L1205G.grid2")).append("(")
						.append(docCodeName(model.getDocCode())).append(")");
				model.setCustId(allCust.toString());
				// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
				model.setDocRslt(model.getDocKind());
				model.setDocKind(this.getCaseType(model, pop, strB));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getAreaDocstatus())
								.getCode()));
				model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
				model.setUpdater(!Util.isEmpty(userservice.getUserName(model
						.getUpdater())) ? userservice.getUserName(model
						.getUpdater()) : model.getUpdater());

				if (!Util.isEmpty(model.getCaseBrId())) {
					model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
							+ " "
							+ branchService.getBranchName(Util
									.nullToSpace(model.getCaseBrId())));
				}
			}
		} catch (java.lang.IndexOutOfBoundsException ex) {
			for (int w = 0; w < (page.getTotalRow() - params.getInt("rowNum")
					* (page.getPageSize() - 1)); w++) {
				L120M01A model = (L120M01A) page.getContent().get(w);
				model.setCustId(model.getCustId() + " " + model.getDupNo());
				StringBuilder strB = new StringBuilder();
				StringBuilder allCust = new StringBuilder();
				allCust.append(model.getCustId()).append(" ")
						.append(model.getDupNo());
				strB.append(
						"1".equals(model.getDocKind()) ? pop
								.getProperty("L1205G.grid1") : pop
								.getProperty("L1205G.grid2")).append("(")
						.append(docCodeName(model.getDocCode())).append(")");
				model.setCustId(allCust.toString());
				// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
				model.setDocRslt(model.getDocKind());
				model.setDocKind(this.getCaseType(model, pop, strB));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getAreaDocstatus())
								.getCode()));
				model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
				model.setUpdater(!Util.isEmpty(userservice.getUserName(model
						.getUpdater())) ? userservice.getUserName(model
						.getUpdater()) : model.getUpdater());

				if (!Util.isEmpty(model.getCaseBrId())) {
					model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
							+ " "
							+ branchService.getBranchName(Util
									.nullToSpace(model.getCaseBrId())));
				}
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 篩選L120M01AGrid 外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a2(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		Date fromDate = null;
		Date endDate = null;
		String typCd = Util.trim(params.getString("typCd"));
		// String docType = Util.trim(params.getString("docType"));
		String docKind = Util.trim(params.getString("docKind"));
		String docCode = Util.trim(params.getString("docCode"));
		String custName = Util.trim(params.getString("custName"));
		String updater = Util.trim(params.getString("updater"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));

		if (!Util.isEmpty(Util.nullToSpace(params.getString("fromDate")))) {
			fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
		}
		if (!Util.isEmpty(Util.nullToSpace(params.getString("endDate")))) {
			endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
		}

		if (fromDate != null && endDate != null) {
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
					reason);
			// if (docStatus.equals(CreditDocStatusEnum.海外_已核准.getCode())) {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "approveTime", reason);
			// } else {
			// pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
			// "caseDate", reason);
			// }
		}

		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}
		// 當為授管處簽案時要加上此條件
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& (UnitTypeEnum.分行.equals(UnitTypeEnum.convertToUnitType(user
						.getUnitType())) || UnitTypeEnum.國金部
						.equals(UnitTypeEnum.convertToUnitType(user
								.getUnitType())))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					UtilConstants.BankNo.授管處);
		}

		switch (docStatusEnum) {
		case 海外_呈總行:

			// 當登錄分行為澳洲 ，分行呈總行文件狀態 後為提會待登錄 或提會帶覆核
			IBranch branchtype = branchService.getBranch(user.getUnitNo());
			if (UtilConstants.Country.澳洲.equals(branchtype.getCountryType())
					|| UtilConstants.Country.加拿大.equals(branchtype
							.getCountryType())) {
				String[] showDoc = new String[] {
						CreditDocStatusEnum.海外_待覆核.getCode(),
						CreditDocStatusEnum.海外_總行提會待登錄.getCode(),
						CreditDocStatusEnum.海外_總行提會待覆核.getCode() };
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						showDoc);
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", CreditDocStatusEnum.海外_待覆核.getCode());
			}
			break;
		case 海外_呈授管處:
			// 當狀態為海外_呈授管處時要變更搜尋條件要文件狀態為C或H結尾都要顯示，並且join授權檔
			pageSetting
					.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%H"),
							new SearchModeParameter(SearchMode.LIKE,
									"docStatus", "%C"));
			// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
			// "docStatus", CreditDocStatusEnum.授管處_待陳復.getCode());
			break;
		case 營運中心_呈總處:
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "docStatus",
					"%H");

			break;

		case 營運中心_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "S"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "A")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));

			break;

		case 授管處_待收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"hqReceiveDate", "");
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
			break;
		case 授管處_已收案件:
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"hqReceiveDate", "");
			break;
		case 授管處_審查中:
			// 審查中在提授審會時不能不能出現此案件
			pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
					"hqMeetFlag", null);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
			break;
		case 授管處_待更正:
			pageSetting.addSearchModeParameters(SearchMode.OR,
					new SearchModeParameter(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"backUnit", "C"), new SearchModeParameter(
									SearchMode.EQUALS, "backUnit", "S")),
					new SearchModeParameter(SearchMode.EQUALS, "returnFromBH",
							"1"));
			break;
		case 海外_已核准:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
			// 如果登錄的是總行要多看到 泰國的提會待登錄、和泰國提會待覆核的案件
			if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
				// 已核准案件要排除陳覆陳述案，
				pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
						new String[] { CreditDocStatusEnum.海外_已核准.getCode(),
								CreditDocStatusEnum.泰國_提會待登錄.getCode(),
								CreditDocStatusEnum.泰國_提會待覆核.getCode() });
			} else {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", docStatus);
			}
			break;
		case 海外_陳復案_陳述案:
			// 已核准 且 docCode = 3
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;

		case 授管處_免批覆案件:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.海外_已核准.getCode());
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					UtilConstants.Casedoc.DocCode.陳復陳述案);
			break;
		case 營運中心_所有提會案件:
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
					"rptTitleArea1", "");
			pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
					"rptTitleArea1", "");
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		if (Util.notEquals(user.getUnitType(), "S")
				&& Util.notEquals(user.getUnitType(), "A")) {
			// 當非授管處或營運中心時
			// 排除掉海外授信案件
			pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
					UtilConstants.Casedoc.typCd.海外);
		}

		// 限定只顯示個金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.個金);

		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
		}
		if (Util.isNotEmpty(docKind)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docKind",
					docKind);
		}
		if (Util.isNotEmpty(docCode)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docCode",
					docCode);
		}
		if (Util.isNotEmpty(custName)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
					custName + "%");
		}
		if (Util.isNotEmpty(updater)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "updater",
					updater);
		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE + " 23:59:59") });
		}

		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("userid", new UserNameFormatter(userservice)); // 使用者名稱格式化
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			StringBuilder strB = new StringBuilder();
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			strB.append(
					"1".equals(model.getDocKind()) ? pop
							.getProperty("L1205G.grid1") : pop
							.getProperty("L1205G.grid2")).append("(")
					.append(docCodeName(model.getDocCode())).append(")");
			model.setCustId(allCust.toString());
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			// 這邊將授權別設定到本案最後批示結果(讓外面JS使用)
			model.setDocRslt(model.getDocKind());
			model.setDocKind(this.getCaseType(model, pop, strB));
			model.setDocStatus(getMessage("docStatus."
					+ CreditDocStatusEnum.getEnum(model.getDocStatus())
							.getCode()));
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
		}

		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢L120S01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("keyMan", true);
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120S01A.class, pageSetting);
		// Page<L120S01A> page = service.getPage(pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S01A model = list.get(i);
			model.setCustId(model.getCustId() + " " + model.getDupNo());
			model.setCustPos(findCustPos(model));
			CodeType code1 = new CodeType();
			CodeType code2 = new CodeType();
			String custRlt = Util.trim(model.getCustRlt());
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			if ("Y".equals(model.getKeyMan())) {
				model.setKeyMan("*");
			} else {
				model.setKeyMan("");
			}
			if (!Util.isEmpty(custRlt)) {
				String locale = LocaleContextHolder.getLocale().toString();
				if (!(custRlt.contains("X"))) {
					// 其他綜合關係
					code1 = codeservice.findByCodeTypeAndCodeValue(
							"Relation_type31", custRlt.substring(0, 1), locale);
					code2 = codeservice.findByCodeTypeAndCodeValue(
							"Relation_type32", custRlt.substring(1, 2), locale);
				} else {
					if (custRlt.endsWith("X")) {
						// 企業關係人
						code1 = codeservice.findByCodeTypeAndCodeValue(
								"Relation_type1", custRlt, locale);
					} else {
						// 親屬關係
						code1 = codeservice.findByCodeTypeAndCodeValue(
								"Relation_type2", custRlt, locale);
					}
				}
			}
			if (code1 != null && code2 != null) {
				if (!Util.isEmpty(code2.getCodeDesc())) {
					StringBuilder strB = new StringBuilder();
					strB.append(code1.getCodeDesc()).append("-")
							.append(code2.getCodeDesc());
					// code2有資料則設定code1+code2
					model.setCustRlt(strB.toString());
				} else {
					if (!Util.isEmpty(code1.getCodeDesc())) {
						// code1有資料則設定code1
						model.setCustRlt(code1.getCodeDesc());
					} else {
						// 無資料則設為空
						model.setCustRlt("");
					}
				}
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 依客戶統編查詢L120S01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s01aById(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String custId = Util.nullToSpace(params.getString("custId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = relatedAccountService
				.queryL120s01aById(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S01AGrid 資料(借款人引入)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01aToGetData(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120S01A.class, pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢資信簡表(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e1(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		L120S01A l120s01a = cls1141Service.findModelByOid(L120S01A.class,
				thisOid);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainId1(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表(徵信)-- 集團
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e1Grp(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		String authBrId = user.getUnitNo();
		String custId = Util.trim(l120m01a.getCustId());
		String dupNo = Util.trim(l120m01a.getDupNo());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainId1(authBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e2(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		L120S01A l120s01a = cls1141Service.findModelByOid(L120S01A.class,
				thisOid);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainId2(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告2(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s01e3(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String oid = Util.nullToSpace(params.getString("oid"));
		String mainId = Util.nullToSpace(params.getString("mainId"));
		L120S01A l120s01a = cls1141Service.findModelByOid(L120S01A.class, oid);
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainId);
		String caseBrId = "";
		String custId = "";
		String dupNo = "";
		if (l120m01a != null) {
			caseBrId = Util.trim(l120m01a.getCaseBrId());
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainId2(caseBrId,
				custId, dupNo, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告--依照使用者輸入之統編(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIds(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String custId = Util.nullToSpace(params.getString("custId"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainId(caseBrId,
				custId, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIds2(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainId2s(
				caseBrId, mainid, mainid, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢徵信報告 MainId(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdss2(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainId2ss(
				caseBrId, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
			map.put("creator",
					Util.isEmpty(map.get("creator")) ? "" : withIdName(Util
							.trim(Util.nullToSpace(map.get("creator")))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIda(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainIda(caseBrId,
				mainid, mainid, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdb(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		String custId = Util.nullToSpace(params.getString("custId"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainIdb(caseBrId,
				custId, pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信簡表 MainId(範圍)(徵信報告用，不限制文件狀態)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesMainIdc(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(mainid);
		String caseBrId = Util.trim(l120m01a.getCaseBrId());
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getCesMainIdc(caseBrId,
				pageSetting);
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢簽報書敘述說明檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120m01d(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "itemType", "C");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01D.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S03AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s03a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLSS07APanel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("cntrNo");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120S03A.class, pageSetting);

		List<L120S03A> list = (List<L120S03A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S03A model = list.get(i);
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			// 非信保
			if ("2".equals(model.getCrdFlag())) {
				model.setCrdFlag(pop.getProperty("L1205S07.index2"));
				model.setCrdRatio(null);
				model.setRskAmt2(model.getRskAmt1());
				model.setRskr2(model.getRskr1());
				model.setCamt2(model.getCamt1());
				model.setBisr2(model.getBisr1());
				model.setCostr2(model.getCostr1());
			} else {
				model.setCrdFlag(pop.getProperty("L1205S07.index1"));
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S04AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s04a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				Util.trim(params.getString("mainId")));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyCustId",
				Util.trim(params.getString("keyCustId")));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo",
				Util.trim(params.getString("keyDupNo")));
		// 第三個參數為formatting
		Page<? extends GenericBean> page = relatedAccountService
				.queryL120s04a(pageSetting);

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(RelatedAccountPanel.class);
		List<L120S04A> list = (List<L120S04A>) page.getContent();

		Collections.sort(list, new Comparator<L120S04A>() {

			@Override
			public int compare(L120S04A object1, L120S04A object2) {
				// TODO Auto-generated method stub
				int cr = 0;
				String[] resStr1 = Util.trim(object1.getCustRelation()).split(
						",");
				Arrays.sort(resStr1);
				String[] resStr2 = Util.trim(object2.getCustRelation()).split(
						",");
				Arrays.sort(resStr2);

				int a = resStr2[0].compareTo(resStr1[0]);

				String prtFlag1 = object1.getPrtFlag();
				String prtFlag2 = object2.getPrtFlag();
				int prtFlag = prtFlag2.compareTo(prtFlag1);

				if (prtFlag != 0) {
					cr = (prtFlag > 0) ? -1 : 5;
				} else if (a != 0) {
					cr = (a > 0) ? -2 : 4;
				} else {
					long b = (object2.getProfit() == null ? 0 : object2
							.getProfit())
							- (object1.getProfit() == null ? 0 : object1
									.getProfit());
					if (b != 0) {
						cr = (b > 0) ? 3 : -3;
					} else {
						int c = object2.getCustId().compareTo(
								object1.getCustId());
						if (c != 0) {
							cr = (c > 0) ? -4 : 2;
						} else {
							// String oid1 = object1.getOid();
							// String oid2 = object2.getOid();
							// int oidFlag = oid2.compareTo(oid2);
							// if(oidFlag != 0){
							// cr = (oidFlag > 0)? -5:1;
							// }
						}
					}
				}

				return cr;
			}
		});

		for (int i = 0; i < list.size(); i++) {
			L120S04A model = list.get(i);
			if (Util.isEmpty(model.getCustId())) {
				model.setCustId("");
			} else {
				model.setCustId(model.getCustId() + " " + model.getDupNo());
			}
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			if ("1".equals(model.getPrtFlag())) {
				model.setPrtFlag("V");
			} else {
				model.setPrtFlag("X");
			}

			StringBuilder sb = new StringBuilder();
			String[] strs = Util.trim(model.getCustRelation()).split(",");
			// 對陣列進行排序
			Arrays.sort(strs);
			String custRelationIndex = "";
			for (String s : strs) {
				if (Util.equals(Util.trim(custRelationIndex), "")) {
					custRelationIndex = s;
				}
				if (sb.length() > 0)
					sb.append("/");
				sb.append(Util.trim(prop.getProperty("L1205S07.checkbox" + s)));
			}
			model.setCustRelationIndex(sb.toString());
			model.setCustRelation(custRelationIndex);

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S04AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL120s04b(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.trim(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyCustId",
				Util.trim(params.getString("keyCustId")));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo",
				Util.trim(params.getString("keyDupNo")));
		// 第三個參數為formatting
		Page<? extends GenericBean> page = relatedAccountService
				.queryL120s04b(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢L120S05BGrid 資料(集團)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05b(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120S05B.class, pageSetting);
		List<L120S05B> list = (List<L120S05B>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S05B model = list.get(i);
			StringBuilder strBuf = new StringBuilder();
			strBuf.append(model.getCustId()).append(model.getDupNo())
					.append(" ").append(model.getCustName());
			model.setCustName(strBuf.toString());
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S05DGrid 資料(關係企業)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s05d(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120S05D.class, pageSetting);
		List<L120S05D> list = (List<L120S05D>) page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢L120S06AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL120s06a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLSS07APanel.class);
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120S06A.class, pageSetting);
		List<L120S06A> list = (List<L120S06A>) page.getContent();
		for (int i = 0; i < list.size(); i++) {
			L120S06A model = list.get(i);
			if (UtilConstants.DEFAULT.是.equals(model.getChkYN())) {
				model.setChkYN("V");
			} else {
				model.setChkYN("X");
			}
			StringBuilder str1 = new StringBuilder();
			StringBuilder str2 = new StringBuilder();
			str1.append(model.getCustId()).append(" ").append(model.getDupNo())
					.append(" ").append(model.getCustName());
			str2.append(model.getCustId2()).append(" ")
					.append(model.getDupNo2()).append(" ")
					.append(model.getCustName2());
			model.setCustId(str1.toString());
			model.setCustId2(str2.toString());
			if ("1".equals(model.getPrintMode())) {
				model.setPrintMode(pop.getProperty("L1205S07.grid1"));
			} else {
				model.setPrintMode(pop.getProperty("L1205S07.grid2"));
			}
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢額度明細表Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01aLoanItemCompare(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.nullToSpace(params.getString("itemType"));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(CLS1151S01Page.class);
		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);
		// 檢核欄V為通過檢核且經過計算，O為 通過檢核 但尚未計算，X為尚未通過檢核
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		List<L140M01A> l140m01alist = (List<L140M01A>) page.getContent();
		for (L140M01A l140m01a : l140m01alist) {
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}

			// 用來暫放文件狀態
			l140m01a.setApprover(l140m01a.getDocStatus());
			if (UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
					.getDataSrc())) {
				// L140M01a.together=聯行
				l140m01a.setDataSrc(pop2.getProperty("L140M01a.together"));
			} else {
				l140m01a.setDataSrc("");
			}

		}
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢L140SM01AGrid 資料(對照)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryList_for_cls_l120s06b_type2_orderByRate(
			ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String prodKind = Util.trim(params.getString("prodKind"));
		String subj = Util.trim(params.getString("subj"));
		String lnPurs = Util.trim(params.getString("lnPurs"));

		Page<Map<String, Object>> page = cls1141Service.queryList_for_cls_l120s06b_type2_orderByRate(prodKind,
				subj, lnPurs, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryL140m01a2(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("custId");
		String custId = Util.trim(params.getString("custId"));
		String caseBrid = Util.trim(params.getString("textBrid"));
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getLihai(custId,
				caseBrid, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
	
	/**
	 * 根據後端相關身份值找出相對應的名稱
	 * 
	 * @param model
	 *            L120S01A
	 * @return String
	 */
	public String findCustPos(L120S01A model) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		if (Util.trim(model.getCustPos()).length() != 0) {
			switch (model.getCustPos().toCharArray()[0]) {
			case 'C':
				return pop.getProperty("L1205G.grid4");
			case 'D':
				return pop.getProperty("L1205G.grid5");
			case 'G':
				return pop.getProperty("L1205G.grid6");
			case 'N':
				return pop.getProperty("L1205G.grid7");
			case 'S':
				return pop.getProperty("L1205G.grid8");
			default:
				return "";
			}
		}
		return "";
	}

	/**
	 * 查詢L120S01AGrid 資料(原始)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01aOrigin(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainid = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120S01A.class, pageSetting);
		// Page<L120S01A> page = service.getPage(pageSetting);
		List<L120S01A> list = (List<L120S01A>) page.getContent();
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 依照案件別代碼取得相對應案件別名稱
	 * 
	 * @param doccode
	 *            String
	 * @return Properties
	 */
	public String docCodeName(String doccode) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		if (UtilConstants.Casedoc.DocCode.一般.equals(doccode)) {
			return pop.getProperty("L1205G.grid9");
		} else if (UtilConstants.Casedoc.DocCode.團貸案件.equals(doccode)) {
			return pop.getProperty("L1205G.grid10");
		} else {
			return pop.getProperty("L1205G.grid11");
		}
	}

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String fieldId = params.getString("fieldId");
		// String keyCustId = Util.trim(params.getString("keyCustId"));
		// String keyDupNo = Util.trim(params.getString("keyDupNo"));
		boolean needCngName = params.getBoolean("needCngName");
		boolean needBranch = params.getBoolean("needBranch");
		if (Util.equals("upFileS12", fieldId)) {
			// 在918的頁籤【會簽/會議決議】最下面有附加檔案的 grid
			needCngName = false;
			needBranch = false;
		}
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		/**
		 * 1. 由系統產生一個 file 2. 下載file,改內容,重上傳
		 * 
		 * 此時, 在 /elnfs 有 2 個檔案 在 DB 也有 2 筆記錄select * from lms.bdocfile where
		 * oid in ( 'AAA','BBB') 但在 lms.bdocfile 的 DELETEDTIME. 1筆有刪除時間, 另1筆null
		 * → 用 $.capFileDownload(...) 去下載時, 只抓得到最新的
		 * 
		 * SimpleFileUploadHandler.java search : "deleteDup"
		 * 
		 * 所以多加上條件 deletedTime is null 讓 grid 呈現的 data row,都是可以下載的
		 */
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 此 method 去查 lms.bdocfile. 該table 只有 mainId,fieldId
		Page<DocFile> page = relatedAccountService.queryfile(pageSetting,
				needCngName, needBranch);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢需列印的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryPrint(ISearch pageSetting,
			PageParameters params) throws CapException {
		String printCondition = Util.nullToSpace(params
				.getString("printCondition"));
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = cls1141Service.getBorrows(mainId,
				printCondition, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢 登錄主要負責人連保人資信狀況資料 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryViewA(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01e.docType", "4");
		Page page = cls1141Service.findPage(C140M04A.class, pageSetting);
		Map formatter = new HashMap();
		formatter.put("pcTitle", new CodeTypeFormatter(codeservice, "Title2"));
		formatter.put("pcType", new I18NFormatter("pcType."));
		formatter.put("pcSex", new I18NFormatter("pcSex."));
		List<C140M04A> c140m04as = (List<C140M04A>) page.getContent();
		List<L120M01E> listL120m01e = (List<L120M01E>) cls1141Service
				.findListByMainId(L120M01E.class, mainId);
		if (!listL120m01e.isEmpty()) {
			for (L120M01E l120m01e : listL120m01e) {
				for (C140M04A model : c140m04as) {
					if (model.equals(l120m01e.getC140m04a())) {
						if (l120m01e != null) {
							String custId = model.getL120m01e().getDocCustId();
							String dupNo = model.getL120m01e().getDocDupNo();
							model.getL120m01e().setDocCustId(
									custId + " " + dupNo);
						}
						break;
					}
				}
			}
		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 經營事業 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SA(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = cls1141Service.getC140S04APage(pageSetting);
		Map formatter = new HashMap();
		for (C140S04A model : (List<C140S04A>) page.getContent()) {
			if (!Util.isEmpty(model.getInvCap21())) {
				model.setInvCap21(model.getInvCap21().setScale(0));
			}
			if (!Util.isEmpty(model.getAmtUnitST())) {
				model.setAmtUnitST(model.getAmtUnitST().setScale(0));
			}
		}
		formatter.put("invCap11", new CodeTypeFormatter(codeservice,
				"Common_Currcy"));
		formatter.put("invCap21",
				new CodeTypeFormatter(codeservice, "CurrUnit"));
		formatter.put("amtUnitST", new CodeTypeFormatter(codeservice,
				"CurrUnit"));
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 本人之土地 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SB(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = cls1141Service.getC140S04BPage(pageSetting);
		Map formatter = new HashMap();
		formatter.put("landUse", new landUse1());
		formatter.put("landLevel", new CodeTypeFormatter(codeservice,
				"LandLevel"));
		formatter.put("landRate", new landRate());
		formatter.put("landMp", new landMp());
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 第四章 本人之建物 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryView41SC(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		Page page = cls1141Service.getC140S04CPage(pageSetting);
		Map formatter = new HashMap();
		formatter.put("buUse", new CodeTypeFormatter(codeservice, "BuUse"));
		formatter.put("buStru", new CodeTypeFormatter(codeservice, "BuStru"));
		formatter.put("buMp", new buMp());
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	/**
	 * 查詢 Grid 資料。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryView(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));
		String uid = CapString.trimNull(params.getString("gridUid"));
		String type = CapString.trimNull(params.getString("gridType"));

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);

		CapGridResult result = new CapGridResult();

		// 取得資料
		if ("A".equals(type)) {
			Page<C140S09A> page = cls1141Service.getC140S09APage(pageSetting);
			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			formatter.put("gBal", new gBal());
			result = new CapGridResult(page.getContent(), page.getTotalRow(),
					formatter);
		} else if ("B".equals(type)) {
			Page<C140S09B> page = cls1141Service.getC140S09BPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("C".equals(type)) {
			Page<C140S09C> page = cls1141Service.getC140S09CPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("D".equals(type)) {
			Page<C140S09D> page = cls1141Service.getC140S09DPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("E".equals(type)) {
			Page<C140S09E> page = cls1141Service.getC140S09EPage(pageSetting);
			result = new CapGridResult(page.getContent(), page.getTotalRow());
		} else if ("F".equals(type)) {
			Page<C140S09F> page = cls1141Service.getC140S09FPage(pageSetting);

			Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
			formatter.put("linv14", new CodeTypeFormatter(codeservice,
					"Common_Currcy", CodeTypeFormatter.ShowTypeEnum.Val_Desc));
			result = new CapGridResult(page.getContent(), page.getTotalRow(),
					formatter);
		}

		return result;
	}

	/**
	 * gBal formatter
	 */
	class gBal implements IBeanFormatter {
		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S09A c140s09a = (C140S09A) in;
			String GMbkBal = c140s09a.getGMbkBal() == null ? "" : c140s09a
					.getGMbkBal().toPlainString();
			String GObuBal = c140s09a.getGObuBal() == null ? "" : c140s09a
					.getGObuBal().toPlainString();
			String GOvsBal = c140s09a.getGOvsBal() == null ? "" : c140s09a
					.getGOvsBal().toPlainString();
			return CapMath.add(new String[] { GMbkBal, GObuBal, GOvsBal });
		}
	}

	/**
	 * buMp formatter (buMp + "/" + buMm)
	 */
	class buMp implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04C meta = (C140S04C) in;

			return new StringBuffer()
					.append(CapString.trimNull(meta.getBuMp())).append(" / ")
					.append(new NumericFormatter().reformat(meta.getBuMm()))
					.toString();
		}
	}

	/**
	 * landUse1 formatter
	 */
	class landUse1 implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) {
			C140S04B meta = (C140S04B) in;

			if (!CapString.isEmpty(meta.getLandUse1())) {
				CodeType landUse1 = codeservice.findByCodeTypeAndCodeValue(
						"LandUse1", meta.getLandUse1());
				CodeType landUse2 = codeservice.findByCodeTypeAndCodeValue(
						"LandUse2" + meta.getLandUse1(), meta.getLandUse2());
				return new StringBuffer(landUse1.getCodeDesc()).append("/")
						.append(landUse2.getCodeDesc()).toString();
			}

			return EloanConstants.EMPTY_STRING;
		}
	}

	/**
	 * landRate formatter
	 */
	class landRate implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04B meta = (C140S04B) in;
			StringBuffer sb = new StringBuffer();

			if (meta.getLandRateC() != null) {
				sb.append(meta.getLandRateC());
			}

			sb.append('/');

			if (meta.getLandRateD() != null) {
				sb.append(meta.getLandRateD());
			}

			return sb.toString();
		}
	}

	/**
	 * landMp formatter (landMp + "/" + landMm)
	 */
	class landMp implements IBeanFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			C140S04B meta = (C140S04B) in;

			return new StringBuffer()
					.append(CapString.trimNull(meta.getLandMp())).append(" / ")
					.append(new NumericFormatter().reformat(meta.getLandMm()))
					.toString();
		}
	}

	/**
	 * 查詢待列印的一般財務報表。
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryIncludeFSS(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		C140M01A model = cls1141Service.getC140M01AByMainId(mainId);

		String fssCustId = Util.trim(model.getCustId());
		String fssDupNo = Util.trim(model.getDupNo());

		String getBranch = CapString.trimNull(params.getString("qryBranch"));
		if (CapString.isEmpty(getBranch)) {
			getBranch = MegaSSOSecurityContext.getUnitNo();
		}
		if (!CapString.isEmpty(fssCustId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					fssCustId);
		}
		if (!CapString.isEmpty(fssDupNo)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					fssDupNo);
		}
		String type = CapString.trimNull(params.getString("type"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		if (!CapString.isEmpty(params.getString("fssConso", null))) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "conso",
					params.getString("fssConso"));
		}

		if (params.getAsBoolean("fssPeriodType", false)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"periodType", PeriodTypeEnum.YEAR.getCode());
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"f101a01as.authUnit", getBranch);
		pageSetting.addSearchModeParameters(SearchMode.IN,
				"f101a01as.authType",
				new String[] { DocAuthTypeEnum.MODIFY.getCode(),
						DocAuthTypeEnum.VIEW_TRANSFER.getCode() });

		Page<Map<String, Object>> page = cls1141Service.getFss(getBranch,
				fssCustId, fssDupNo, pageSetting);

		// formatter
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("custId", new CustIdFormatter());
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("approver", new UserNameFormatter(userservice));
		map.put("conso", new IFormatter() {
			private static final long serialVersionUID = 1L;

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				return "1".equals(in) ? "V" : "";
			}
		});
		map.put("source", new CodeTypeFormatter(codeservice, "FssSource"));
		return new CapMapGridResult(page.getContent(), page.getTotalRow(), map);
	}

	/**
	 * Mow模型評等Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryMowTrust(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String thisOid = Util.nullToSpace(params.getString("thisOid"));
		L120S01A l120s01a = cls1141Service.findModelByOid(L120S01A.class,
				thisOid);
		String custId = "";
		String dupNo = "";
		if (l120s01a != null) {
			custId = Util.trim(l120s01a.getCustId());
			dupNo = Util.trim(l120s01a.getDupNo());
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = cls1141Service.getMowTrust(custId,
				dupNo, pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢列印營運中心意見
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryPrintArea(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				"hqReceiveDate", "");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				UtilConstants.Field.目前編製行, UtilConstants.BankNo.授管處);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "areaChk", "3");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01A.class, pageSetting);
		StringBuilder allCust = new StringBuilder();
		StringBuilder docName = new StringBuilder();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			allCust.setLength(0);
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo()).append(" ")
					.append(model.getCustName());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());
			model.setDocKind(this.getCaseType(model, pop, docName));
			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				// 用UID暫存文件狀態
				model.setUid(Util.trim(model.getDocStatus()));
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(this.getUserName(model.getUpdater()));
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(this.getUserName(model
						.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(Util.trim(model.getAreaAppraiser()));
			}
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(this.getUserName(model.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setApproveTime(model.getAreaSendInfo());
			}

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得 案件類別名稱
	 * 
	 * @param model
	 *            簽報書主檔
	 * @param pop
	 *            語系檔
	 * @param temp
	 *            暫存的stringBuffer
	 * 
	 * @return
	 */
	private String getCaseType(L120M01A model, Properties pop,
			StringBuilder temp) {
		String areaTitle = null;
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		temp.setLength(0);

		if (UtilConstants.Casedoc.DocKind.授權內.equals(model.getDocKind())) {

			String authLvl = Util.trim(model.getAuthLvl());
			if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(authLvl)) {
				// l120m01a.tag3=營運中心
				temp.append(pop.getProperty("l120m01a.tag3"));
			} else if (UtilConstants.Casedoc.AuthLvl.總行授權內.equals(authLvl)) {
				// lmss01.legend2=母行
				temp.append(pop.getProperty("lmss01.legend2"));
			} else {
				// lmss01.legend1=分行
				temp.append(pop.getProperty("lmss01.legend1"));
			}
			// L1205G.grid12=授權內
			temp.append(pop.getProperty("L1205G.grid12"));
		} else {
			areaTitle = this.queryAreaTitle(model);
			if (Util.isNotEmpty(areaTitle)) {
				temp.append(areaTitle);
			} else {
				// L1205G.grid13=授權外
				temp.append(pop.getProperty("L1205G.grid13"));
			}

		}
		// L1205G.grid9=一般
		// L1205G.grid10=團貸
		// L1205G.grid11=陳復/陳述案
		temp.append("(");
		if (UtilConstants.Casedoc.DocCode.一般.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid9"));
		} else if (UtilConstants.Casedoc.DocCode.團貸案件
				.equals(model.getDocCode())) {
			temp.append(pop.getProperty("L1205G.grid10"));
		} else {
			if (UtilConstants.Casedoc.DocCode.異常通報.equals(Util.trim(model
					.getDocCode()))) {
				// other.msg59=異常通報案件
				temp.append(pop2.getProperty("other.msg59"));
			} else {
				temp.append(pop.getProperty("L1205G.grid11"));
			}
		}
		temp.append(")");
		return temp.toString();
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL120s01rList(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				mainId);
		Page<? extends GenericBean> page = lmsService.findPage(L120S01R.class, pageSetting);

        CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());

        return result;
	}
	
	/**
	 * 取得國內屬營運中心制分行的標題名稱
	 * 
	 * @param l120m01a
	 *            簽報書主檔
	 * @return
	 * @throws CapException
	 */
	private String queryAreaTitle(L120M01A l120m01a) {
		IBranch tBranch = branchService.getBranch(l120m01a.getCaseBrId());
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		if (tBranch != null) {
			String brnGroup = Util.trim(tBranch.getBrnGroup());
			if (Util.isNotEmpty(brnGroup)) {
				String unitType = branchService.getBranch(brnGroup)
						.getUnitType();
				if (BranchTypeEnum.營運中心.getCode().equals(unitType)) {

					/*
					 * 因為海外分行不屬於營運中心制，所以提醒第四階段，國內屬營運中心制分行時TITLE顯示會有差異
					 * 國內營運中心制分行，分行授權外案件會顯示營運中心授權外案件簽報書
					 */
					// other.msg131=營運中心授權外案件簽報書
					return pop.getProperty("other.msg131");
				}
			}
		}
		// clsL120M01A.error050=授權外
		// other.msg136=分行
		return pop.getProperty("other.msg136")
				+ pop.getProperty("clsL120M01A.error050");
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userservice.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL720m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("patternNM");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L720M01A.class, pageSetting);

		List<L720M01A> list = (List<L720M01A>) page.getContent();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				L720M01A model = list.get(i);
				StringBuilder fullUp = new StringBuilder();
				fullUp.append(getPerName(model.getUpdater())).append(" (")
						.append(TWNDate.toFullTW(model.getUpdateTime()))
						.append(")");
				model.setUpdater(fullUp.toString());
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢異常通報事項Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL130S01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = params.getString(EloanConstants.MAIN_ID);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L130S01A.class, pageSetting);

		List<L130S01A> list = (List<L130S01A>) page.getContent();
		// 格式化Grid內容
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				L130S01A model = list.get(i);
				String seqKind = Util.trim(model.getSeqKind());
				if (UtilConstants.seqKind.擬辦.equals(seqKind)) {
					model.setSeqKind("擬辦");
				} else if (UtilConstants.seqKind.已辦.equals(seqKind)) {
					model.setSeqKind("已辦");
				} else if (UtilConstants.seqKind.其他.equals(seqKind)) {
					model.setSeqKind("其他");
				}
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id) {
		return (!Util.isEmpty(userservice.getUserName(id)) ? userservice
				.getUserName(id) : id);
	}

	/**
	 * 將指定字串轉成 (字串 + " " + 字串對應名稱)格式
	 * 
	 * @param str
	 *            指定字串
	 * @return 轉換後格式
	 */
	private String withIdName(String str) {
		StringBuilder sb = new StringBuilder();
		sb.append(Util.trim(str)).append(" ")
				.append(getPerName(Util.trim(str)));
		return sb.toString();
	}

	/**
	 * 查詢擔保品 MainId(by 分行、by 簽報書主要借款人、by 擔保品大類)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCmsMainId(ISearch pageSetting,
			PageParameters params) throws CapException {
		Page<Map<String, Object>> page = null;

		// Grid種類
		String type = Util.trim(params.getString("type"));

		if (type.equals("1")) {
			// 建立主要Search 條件
			String branchId = Util.trim(params.getString("branchId"));
			// 第三個參數為formatting
			page = cls1141Service.getCmsMainId(branchId, pageSetting);
		} else if (type.equals("2")) {
			// 建立主要Search 條件
			String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
			// 第三個參數為formatting
			page = cls1141Service.getCmsMainId2(mainId, pageSetting);
		} else if (type.equals("3")) {
			// 建立主要Search 條件
			String collTyp1 = Util.trim(params.getString("collTyp1"));
			// 第三個參數為formatting
			page = cls1141Service.getCmsMainId3(collTyp1, pageSetting);
		}

		if (page == null) {
			// 查無資料
			return new CapMapGridResult(new ArrayList<Map<String, Object>>(), 0);
		}

		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			sb.append(Util.trim(map.get("custId"))).append(" ")
					.append(Util.trim(map.get("dupNo"))).append(" ")
					.append(Util.trim(map.get("custName")));
			map.put("collKind",
					codeservice.getDescOfCodeType("cms1090_collTyp1",
							Util.trim(map.get("collTyp1"))));
			map.put("custName", sb.toString());
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? "" : CMSDocStatusEnum
							.getMessage(Util.trim(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
			map.put("megaAmt", Util.isEmpty(map.get("megaAmt")) ? ""
					: NumConverter.delComma(Util.trim(map.get("megaAmt"))));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 清除擔保品Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult beforeClearCMS(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID, ""));
		// 建立主要Search 條件
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.L120m01eDocType.擔保品估價報告書);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls1141Service.findPage(
				L120M01E.class, pageSetting);

		// List<L120M01E> list = (List<L120M01E>) page.getContent();
		// if (!list.isEmpty()) {
		// for (int i = 0; i < list.size(); i++) {
		// L120M01E model = list.get(i);
		// StringBuilder fullUp = new StringBuilder();
		// fullUp.append(getPerName(model.getUpdater())).append(" (")
		// .append(TWNDate.toFullTW(model.getUpdateTime()))
		// .append(")");
		// model.setUpdater(fullUp.toString());
		// }
		// }
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢擔保品
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            <pre>
	 *            colltyp1 擔保品大類 
	 *            custId 客戶統編 
	 *            dupNo 重覆序號 
	 *            branch 分行代號
	 * </pre>
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCMS(ISearch pageSetting, PageParameters params) throws CapException {
		String colltyp1 = Util.trim(params.getString("collTyp1", ""));
		String custId = Util.trim(params.getString("cmsCustId", ""));
		String dupNo = Util.trim(params.getString("cmsDupNo", ""));
		String branch = Util.trim(params.getString("branchId", "005"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "collTyp1",
				colltyp1);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		pageSetting.addSearchModeParameters(
				SearchMode.IN,
				EloanConstants.DOC_STATUS,
				new String[] { CMSDocStatusEnum.分行_編製中.getCode(),
						CMSDocStatusEnum.分行_待覆核.getCode(),
						CMSDocStatusEnum.分行_已覆核.getCode(),
						CMSDocStatusEnum.分行_待設質.getCode(),
						CMSDocStatusEnum.分行_已設質.getCode(),
						CMSDocStatusEnum.分行_待塗銷.getCode(),
						CMSDocStatusEnum.聯行傳回.getCode(),
						CMSDocStatusEnum.代鑑價編製中.getCode(),
						CMSDocStatusEnum.代鑑價待覆核.getCode(),
						CMSDocStatusEnum.代鑑價已完成.getCode(),
						CMSDocStatusEnum.營運中心_編製中.getCode(),
						CMSDocStatusEnum.營運中心_待覆核.getCode(),
						CMSDocStatusEnum.營運中心_已覆核.getCode(),
						CMSDocStatusEnum.營運中心_已傳回.getCode(),
						CMSDocStatusEnum.營運中心_待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核待收件.getCode(),
						CMSDocStatusEnum.營運中心_覆核編制中.getCode(),
						CMSDocStatusEnum.營運中心_覆核待覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已覆核.getCode(),
						CMSDocStatusEnum.營運中心_覆核已傳回.getCode(),
						CMSDocStatusEnum.待斷頭.getCode(),
						CMSDocStatusEnum.已斷頭.getCode(),
						CMSDocStatusEnum.補提.getCode(),
						CMSDocStatusEnum.擔保率不足.getCode(),
						CMSDocStatusEnum.授管處_編製中.getCode(),
						CMSDocStatusEnum.授管處_待覆核.getCode(),
						CMSDocStatusEnum.授管處_已覆核.getCode()

				});
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		Page<? extends GenericBean> page = lmsService.findPage(C100M01.class,
				pageSetting);
		List<C100M01> c100m01s = (List<C100M01>) page.getContent();
		for (C100M01 model : c100m01s) {
			model.setAppraiserName(userservice.getUserName(model.getAppraiser()));
		}
		
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("branch", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name));
		dataReformatter.put("collTyp1", new CodeTypeFormatter(codeservice,
				"lmsUseCms_collTyp1"));
		dataReformatter.put("docStatus", new I18NFormatter("status."));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢 逾催案件報告表 Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCaseInfo(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = CapString.trimNull(params.getString("gridMainId"));

		List<Map<String, Object>> s104m01aList = colService
				.getS104M01AList(mainId);

		CapMapGridResult m01Result = new CapMapGridResult(s104m01aList,
				s104m01aList.size());

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("grantType", new CodeTypeFormatter(codeservice,
				"grantType"));

		for (Map<String, Object> map : s104m01aList) {
			map.put("custValue", map.get("custId") + " " + map.get("dupNo"));
		}

		m01Result.setDataReformatter(formatter);
		m01Result.setColumns(new String[] { "mainId", "pid", "branchId",
				"custId", "dupNo", "custName", "caseNo", "grantType" });

		return m01Result;

	}

	/**
	 * 查詢 逾催案件報告表之借款人輸入 Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryBorrower(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainid = Util.nullToSpace(params.getString("mainId"));
		String oid = Util.nullToSpace(params.getString("oid"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainid);
		L120M01A l120m01a = cls1141Service.findL120m01aByOid(oid);

		Page<? extends GenericBean> page = cls1141Service.findPage(
				C120M01A.class, pageSetting);
		List<C120M01A> list = (List<C120M01A>) page.getContent();

		List<Map<String, Object>> m01List = new ArrayList<Map<String, Object>>();
		for (C120M01A model : list) {

			Map<String, Object> m01Map = new HashMap<String, Object>();
			m01Map.put("keyMan", "Y".equals(model.getKeyMan()) ? "*" : "");
			m01Map.put("custNo", model.getCustId() + " " + model.getDupNo());
			m01Map.put("custName", model.getCustName());
			m01Map.put("custId", model.getCustId());
			m01Map.put("dupNo", model.getDupNo());
			m01Map.put("oid", model.getOid());
			m01Map.put("mainId", model.getMainId());
			m01Map.put("caseNo", l120m01a.getCaseNo());
			m01Map.put("caseDate", l120m01a.getCaseDate());
			m01Map.put("colOid", IDGenerator.getUUID());
			m01List.add(m01Map);
		}

		CapMapGridResult m01Result = new CapMapGridResult(m01List, 1);
		m01Result.setColumns(new String[] { "keyMan", "custNo", "custName",
				"custId", "dupNo", "oid", "mainId", "caseNo", "caseDate",
				"colOid" });
		return m01Result;
	}

	/**
	 * 授管處列印該簽報書資信簡表Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult printCesGrid(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<L120M01E> list = clsService.findL120M01E_mainId(mainId);
		List<String> ces140MainIds = new ArrayList<String>();
		List<String> ces120MainIds = new ArrayList<String>();
		for (L120M01E l120m01e : list) {
			if (UtilConstants.Casedoc.L120m01eDocType.徵信報告書.equals(Util
					.trim(l120m01e.getDocType()))) {
				ces140MainIds.add(Util.trim(l120m01e.getDocOid()));
			} else if (UtilConstants.Casedoc.L120m01eDocType.資信簡表.equals(Util
					.trim(l120m01e.getDocType()))) {
				ces120MainIds.add(Util.trim(l120m01e.getDocOid()));
			}
		}
		// 第三個參數為formatting
		Page<Map<String, Object>> page = lmsService.getCesPrint(ces140MainIds,
				ces120MainIds, pageSetting);
		List<Map<String, Object>> listData = page.getContent();
		for (Map<String, Object> map : listData) {
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			sb.append(Util.trim(map.get("cesFDate"))).append(" ")
					.append(Util.trim(map.get("sn"))).append(" ")
					.append(Util.trim(map.get("custName")));
			map.put("docDscr", sb.toString());
			map.put("docStatus",
					Util.isEmpty(map.get("docStatus")) ? ""
							: getMessage("docStatus."
									+ Util.nullToSpace(map.get("docStatus"))));
			map.put("createTime",
					Util.isEmpty(map.get("createTime")) ? "" : CapDate.getDate(
							Util.trim(Util.nullToSpace(map.get("createTime"))),
							DATEYYYYMMDD));
			map.put("approveTime",
					Util.isEmpty(map.get("approveTime")) ? "" : CapDate
							.getDate(Util.trim(Util.nullToSpace(map
									.get("approveTime"))), DATEYYYYMMDD));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢額度明細表已引進之擔保品
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL140M01OByL120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> codeMap = codeservice
				.findByCodeType("lmsUseCms_collTyp1");
		// 第三個參數為formatting
		Page<Map<String, Object>> page = eloandbBASEService
				.findL140M01OByMainId(pageSetting, mainId);
		List<Map<String, Object>> content = page.getContent();
		for (Map<String, Object> data : content) {
			data.put("BRANCH",
					branchService.getBranchName(Util.trim(data.get("BRANCH"))));
			data.put("COLLTYP1", codeMap.get(Util.trim(data.get("COLLTYP1"))));
			data.put("DOCSTATUS",
					getMessage("status." + Util.trim(data.get("DOCSTATUS"))));
		}

		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	@Resource
	MisPTEAMAPPService misPTEAMAPPService;

	/**
	 * 搜尋團貸年度總額度檔
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult PTEAMAPPQuery(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		list.addAll(misPTEAMAPPService.getPTEAMAPPDataByEFFEND(custId,
				Util.isEmpty(dupNo) ? "0" : dupNo));

		return new CapMapGridResult(list, list.size());
	}

	/**
	 * 取得各項費用資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140M01R(ISearch pageSetting,
			PageParameters params) throws CapException {
		logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				params.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "feeSrc",
				"3");

		Page<? extends GenericBean> page = cls1141Service.findPage(
				L140M01R.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		CodeTypeFormatter ctf = new CodeTypeFormatter(codeTypeService,
				"cls1141_feeNo");
		formatter.put("feeNo", ctf);
		formatter.put("feeSphere", new I18NFormatter("L140M01R."));

		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return capGridResult;
	}

	/**
	 * 查詢個金借保人檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String findId = Util.trim(params.getString("findId"));
		if (Util.isNotEmpty(findId)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
					findId + "%");
		}
		// 查這份文件的MinId
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				caseMainId);
		Page<? extends GenericBean> page = cls1141Service.findPage(
				C120M01A.class, pageSetting);
		// 排除額度明細表本身借款人
		List<C120M01A> c120m01as = (List<C120M01A>) page.getContent();
		List<C120M01A> newC120M01as = new ArrayList<C120M01A>();
		for (C120M01A c120m01a : c120m01as) {
			newC120M01as.add(c120m01a);
		}

		CapGridResult result = new CapGridResult(newC120M01as,
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("custId", new CustIdFormatter());
		// dataReformatter.put("custPos", new CodeTypeFormatter(codeTypeService,
		// "L140S01A_custPos")); //
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢個金額度明細表檔
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL140M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 查這份文件的MinId
		// String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String findId = Util.trim(params.getString("findId"));
		if (Util.isNotEmpty(findId)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
					findId + "%");
		}
		// 查這份文件的MinId
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		List<L140M01A> l140m01as = clsService.findL140M01A_byL120m01cMainIdForPrint(caseMainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);
		List<L140M01A> newL140M01as = new ArrayList<L140M01A>();
		for (L140M01A l140m01a : l140m01as) {
			newL140M01as.add(l140m01a);
		}

		Page<? extends GenericBean> page = cls1141Service.findPage(
				L140M01A.class, pageSetting);

		CapGridResult result = new CapGridResult(newL140M01as,
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("cntrno", new CustIdFormatter());
		// dataReformatter.put("custPos", new CodeTypeFormatter(codeTypeService,
		// "L140S01A_custPos")); //
		result.setDataReformatter(dataReformatter);
		return result;
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryPrint_docDscrA(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.L120m01eDocType.團貸母戶額度明細表);

		Page<? extends GenericBean> s_page = cls1141Service.findPage(
				L120M01E.class, pageSetting);

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<L140M01A> l140m01a_list = new ArrayList<L140M01A>();

		Properties rptProperties = MessageBundleScriptCreator
				.getComponentResource(CLS1141R01RptServiceImpl.class);

		for (L120M01E l120m01e : (List<L120M01E>) s_page.getContent()) {
			if (StringUtils.isBlank(l120m01e.getDocURL())) {
				continue;
			}

			L140M01A l140m01a = cls1141Service.findModelByOid(L140M01A.class,
					l120m01e.getDocOid());
			if (l140m01a == null) {
				continue;
			}
			l140m01a_list.add(l140m01a);
		}

		// L140M01A．額度明細表主檔
		for (L140M01A l140m01a : l140m01a_list) {
			L120M01C l120m01c = l140m01a.getL120m01c();
			String itemType = "";
			if (l120m01c != null) {
				itemType = l120m01c.getItemType();
				if (UtilConstants.Cntrdoc.ItemType.額度明細表.equals(itemType)) {
					itemType = rptProperties.getProperty("TITLE.RPTNAME8");
				} else {
					itemType = rptProperties.getProperty("TITLE.RPTNAME9");
				}
			}
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("custName",
					l140m01a.getCustName() + " " + l140m01a.getCustId() + " "
							+ l140m01a.getDupNo());
			data.put("custId", l140m01a.getCustId());
			data.put("dupNo", l140m01a.getDupNo());
			data.put("rptName", itemType);
			data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
			data.put("oid", Util.nullToSpace(l140m01a.getOid()));
			data.put("rpt", ClsConstants.RptNo.額度明細表); //"R12"
			data.put("rptNo", "CLS1151R01"); //會把前次核准的批覆書內容，當成本次的額度明細表資料。(CLS1151R01 vs CLS1151R02)
			// ---
			list.add(data);
		}
		// L140M01A．產品資訊附表
		for (L140M01A l140m01a : l140m01a_list) {
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("custName",
					l140m01a.getCustName() + " " + l140m01a.getCustId() + " "
							+ l140m01a.getDupNo());
			data.put("custId", l140m01a.getCustId());
			data.put("dupNo", l140m01a.getDupNo());
			data.put("rptName", rptProperties.getProperty("TITLE.RPTNAME30"));
			data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
			data.put("oid", Util.nullToSpace(l140m01a.getOid()));
			data.put("rpt", ClsConstants.RptNo.產品資訊_附表); //"R12_F"
			data.put("rptNo", "CLS1151R03");
			// ---
			list.add(data);
		}

		// L140M01P．敘做條件異動比較表
		for (L140M01A l140m01a : l140m01a_list) {
			List<L140M01P> l140m01plist = (List<L140M01P>) cls1141Service
					.findListByMainId(L140M01P.class, l140m01a.getMainId());
			if (CollectionUtils.isNotEmpty(l140m01plist)) {
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("custName",
						l140m01a.getCustName() + " " + l140m01a.getCustId()
								+ " " + l140m01a.getDupNo());
				data.put("custId", l140m01a.getCustId());
				data.put("dupNo", l140m01a.getDupNo());
				data.put("rptName",
						rptProperties.getProperty("TITLE.RPTNAME33"));
				data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
				data.put("oid", Util.nullToSpace(l140m01a.getOid()));
				data.put("rpt", ClsConstants.RptNo.敘做條件異動比較表); //"R93"
				data.put("rptNo", "CLS1151R04");
				// ---
				list.add(data);
			}
		}

		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢額度明細表已引進之擔保品
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL120s09bNcResultDone(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		Map<String, String> codeMap = codeservice
				.findByCodeType("SAS_NC_Result");

		String custId = "";
		String dupNo = "";
		L120M01A l120m01a = lmsService
				.findModelByMainId(L120M01A.class, mainId);

		if (l120m01a == null) {
			// 動審表
			C160M01A c160m01a = clsService.findC160M01A_mainId(mainId);
			if (c160m01a != null) {
				l120m01a = amlRelateService.findModelByMainId(L120M01A.class,
						c160m01a.getSrcMainId());
			}
		}

		if (l120m01a == null) {
			l120m01a = new L120M01A();
		}

		List<String> ncResultStrList = new ArrayList<String>();

		String sysCaseFinish = Util.trim(lmsService
				.getSysParamDataValue("COM_J1060238_AML_CASEFINISH_Y"));
		if (Util.notEquals(sysCaseFinish, "")) {
			for (String xx : sysCaseFinish.split(",")) {
				String txx = Util.equals(Util.trim(xx), "NULL") ? "" : xx;
				if (Util.notEquals(txx, "")) {
					ncResultStrList.add(txx);
				}

			}
		}

		String dataEndDate = CapDate.getCurrentDate("yyyy-MM-dd");
		String dataStartDate = CapDate
				.formatDate(CapDate.shiftDays(Util.parseDate(dataEndDate), 0),
						"yyyy-MM-dd");

		List<Map<String, Object>> l120s09bs = eloandbBASEService
				.findL120s09bNcResultDoneByMainIdAndCustId(
						l120m01a.getCustId(), l120m01a.getDupNo(),
						dataStartDate, dataEndDate, l120m01a.getCaseBrId(),
						mainId, ncResultStrList.toArray(new String[0]));

		int start = pageSetting.getFirstResult();
		int pagNumber = pageSetting.getMaxResults();
		int end = start + pagNumber > l120s09bs.size() ? start
				+ (l120s09bs.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = l120s09bs.get(b);
			rowData.put("NCRESULTDSCR",
					codeMap.get(Util.trim(rowData.get("NCRESULT"))));
			beanListnew.add(rowData);
		}

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				beanListnew, l120s09bs.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());

		// List<Map<String, Object>> content = page.getContent();
		// for (Map<String, Object> data : content) {
		// data.put("BRANCH",
		// branchService.getBranchName(Util.trim(data.get("BRANCH"))));
		// data.put("COLLTYP1", codeMap.get(Util.trim(data.get("COLLTYP1"))));
		// data.put("DOCSTATUS",
		// getMessage("status." + Util.trim(data.get("DOCSTATUS"))));
		// }
		//
		// return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapGridResult queryL120S19B(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString("mainId"));
		String type = Util.trim(params.getString("type"));
		//=========
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		
		Page<? extends GenericBean> page = clsService.findPage(L120S19B.class, pageSetting);
		
		final Map<String, String> _roleDescMap = _L120S19B_roleDescMap();
		//=========
		CapGridResult result =  new CapGridResult(page.getContent(), page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("creator", new UserNameFormatter(userservice)); // 使用者名稱格式化
		dataReformatter.put("role", new IFormatter(){
			@Override
			public String reformat(Object in) throws CapFormatException {
				String applyStatus = Util.trim(in);
				return LMSUtil.getDesc(_roleDescMap, applyStatus);
			}
			
		});
		result.setDataReformatter(dataReformatter);
		
		return result;
	}
	
	private Map<String, String> _L120S19B_roleDescMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_CLS1201S27Panel = MessageBundleScriptCreator.getComponentResource(CLS1201S27Panel.class);
		m.put("AO", Util.trim(prop_CLS1201S27Panel.get("L120S19B.role.AO")));
		m.put("RV", Util.trim(prop_CLS1201S27Panel.get("L120S19B.role.RV")));
		return m;
	}
	
	//ref 企金 lms1201gridhandler :: queryL120s11aMainCust(...)
	public CapGridResult queryRelatedEconomicMainCust(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId2",ClsUtility.L120S18A_CUSTID_9999999999);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", ClsUtility.L120S18A_DUPNO_9);
		if(true){
			pageSetting.addOrderBy("printSeq", false);
		}

		Page<? extends GenericBean> page = clsService.findPage(L120S18A.class,
				pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}
	
	//ref 企金 lms1201gridhandler :: queryL120s11aByCustId(...)
	public CapGridResult queryRelatedEconomicDetail(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if(true){
			pageSetting.addOrderBy("itemSeq", false);	
		}
		
		Page<? extends GenericBean> page = clsService.findPage(L120S18A.class, pageSetting);

		CapGridResult result =  new CapGridResult(page.getContent(), page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("appt", new CodeTypeFormatter(codeTypeService, "ploan_relationTypeName"));
		result.setDataReformatter(dataReformatter);

		return result;
	}
	
	/**
	 * 查詢 文件數位化影像清單 Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws Exception
	 */
	public CapMapGridResult queryMEGAImageList(ISearch pageSetting,
			PageParameters params) throws Exception {
		String mainId = Util.nullToSpace(params.getString("mainId"));
		List<Map<String, Object>> megaImageList = new ArrayList<Map<String, Object>>();
		L120M01A l120m01a = lmsService.findModelByMainId(L120M01A.class, mainId);
		Map<String, String> custPosDesc = codeTypeService.findByCodeType("L140S01A_custPos");
		Map<String, String> codeFormIdMap = codeTypeService.findByCodeType("MEGAIMAGE_FormId_ALL");
		Map<String, String> custIdPosDesc = new HashMap<String, String>();
		C120M01A mainC120M01A = cls1141Service.findC120M01AByMainIdAndKeyMan(mainId);
		custIdPosDesc.put(mainC120M01A.getCustId(), "主借人");
		// 從債務人
		// 各額度明細表內的借保人資料
		Map<String, Set<String>> relatedCustPos = new HashMap<String, Set<String>>();
		List<L140M01A> l140m01as = cls1141Service.findl140m01aByl120m01cMainid(mainC120M01A.getMainId());
		for (L140M01A l140m01a : l140m01as) {
			List<L140S01A> l140s01as = clsService.findL140S01A(l140m01a);
			for (L140S01A l140s01a : l140s01as) {
				Set<String> custPosList = relatedCustPos.get(l140s01a.getCustId());
				if (custPosList == null) {
					custPosList = new HashSet<String>();
				}
				if (!CapString.trimNull(l140s01a.getCustId()).equals(CapString.trimNull(mainC120M01A.getCustId()))) {
					custPosList.add(CapString.trimNull(custPosDesc.get(l140s01a.getCustPos())));
					relatedCustPos.put(l140s01a.getCustId(), custPosList);
				}
			}
		}
		for (Entry<String, Set<String>> relateCase : relatedCustPos.entrySet()) {
			custIdPosDesc.put(relateCase.getKey(), StringUtils.join(relateCase.getValue(), "、"));
		}
		
		// 用簽報書編號查詢文件數位化資料
		List<String> caseNos = new ArrayList<String>(clsService.genMEGAImageCaseNoByBean(l120m01a));
		// 為了之後排序使用，調整案號順序
		// 簽報書案號轉文件數位化OD編號
		String l120CaseNo = clsService.tranL120M01ACaseNoToMEGAImageCaseNo(
				l120m01a.getCaseYear(), l120m01a.getOwnBrId(),
				l120m01a.getCaseSeq());
		// 調整順序，簽報書用案號調為第一，其餘案號依字母數字順序由小至大
		if (caseNos.contains(l120CaseNo)) {
			caseNos.remove(l120CaseNo);
		}
		// 查詢時一律加入簽報用OD案號
		caseNos.add(0, l120CaseNo);
		for (String caseNo : caseNos) {
			List<Map<String, Object>> list = mEGAImageService.getMEGAImageList(MEGAImageApiEnum.取得影像清單, mainId, caseNo);
			Map<String, String> clsQueryMap = mEGAImageService.getEloanQueryImageListParam(MEGAImageApiEnum.連動影像查詢, mainId, caseNo,
					l120m01a.getCustId());
			// 加入查詢影像的URL
			for (Map<String, Object> map : list) {
				map.putAll(clsQueryMap);
				String stakeholderID = CapString.trimNull(map.get("StakeholderID"));
				String borrower = CapString.trimNull(map.get("Borrower"));
				if (!borrower.equals(stakeholderID)) {
					borrower = CapString.trimNull(map.get("Borrower"));
				}
				String relationshipDesc = CapString.trimNull(custIdPosDesc.get(stakeholderID));
				map.put("Relationship", relationshipDesc);
				String formId = CapString.trimNull(map.get("FormId"));
				String formIdDesc = "";
				if (formId.startsWith("CXO") || formId.startsWith("CLO") || formId.startsWith("ECL")) {
					char[] formIdChars = formId.toCharArray();
					formIdChars[3] = '0';
					formIdDesc = formId + "-" + codeFormIdMap.get(String.valueOf(formIdChars));
				} else {
					formIdDesc = formId += "-" + codeFormIdMap.get(formId);
				}
				map.put("FormId", formIdDesc);
			}
			megaImageList.addAll(list);
		}
		CapMapGridResult m01Result = new CapMapGridResult(megaImageList, megaImageList.size());
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("Branch", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName));
		m01Result.setDataReformatter(formatter);
		return m01Result;
	}
	
	
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL120m01dForHittingSameBorrowerInfoDetail(ISearch pageSetting, PageParameters params) throws CapException {
		String l120m01a_mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		List<Map<String, Object>> detailList = new ArrayList<Map<String, Object>>();
		
		List<L140M01A> l140m01aList = this.clsService.findL140m01aListByL120m01cMainIdAndProperty(l120m01a_mainId, UtilConstants.Cntrdoc.ItemType.額度明細表, UtilConstants.Cntrdoc.Property.新做);
		for(L140M01A l140m01a : l140m01aList){
			
			List<L140MC2A> l140mc2aList = this.cls1141Service.findL140MC2ABy(l140m01a.getMainId());
			
			for(L140MC2A entity : l140mc2aList){
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("name", entity.getCustName());
				map.put("id", entity.getCustId());
				map.put("address", entity.getAddress());
				map.put("cellphone", entity.getCellphone());
				map.put("telphone", entity.getTelphone());
				map.put("email", entity.getEmail());
				detailList.add(map);
			}
		}
		
		if(detailList.isEmpty()){
			
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.MAIN_ID, l120m01a_mainId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "itemType", UtilConstants.Casedoc.L120m01dItemType.比對與其他借款人相同資訊描述);
			Page<? extends GenericBean> page = clsService.findPage(L120M01D.class, pageSetting);
			
			List<L120M01D> list = (List<L120M01D>) page.getContent();
			
			for (L120M01D model : list) {
				
				String hisSameBorrowerInfoDetail = model.getItemDscr();
				if(StringUtils.isNotBlank(hisSameBorrowerInfoDetail)){
					
					String[] array = hisSameBorrowerInfoDetail.split(",");
					
					for(String str : array){
						Map<String, Object> map = new HashMap<String, Object>();
						String[] infoArray = str.split("、");
						map.put("name", infoArray[0]);
						map.put("id", infoArray[1]);
						map.put("address", "X".equals(infoArray[2]) ? "" : infoArray[2]);
						map.put("cellphone", "X".equals(infoArray[3]) ? "" : infoArray[3]);
						map.put("telphone", "X".equals(infoArray[4]) ? "" : infoArray[4]);
						map.put("email", "X".equals(infoArray[5]) ? "" : infoArray[5]);
						detailList.add(map);
					}
				}
			}
		}

		CapMapGridResult result = new CapMapGridResult(detailList, detailList.size());
		return result;
	}
}