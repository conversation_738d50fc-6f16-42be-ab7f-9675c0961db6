<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">
				loadScript('pagejs/crs/LMS2411S04Panel');
			</script>
			<!-- ====================================================================== -->
			<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	            <tbody>
	                <tr>
	                    <td class="hd1"><th:block th:text="#{'([^'}"]+)">分行名稱</th:block>&nbsp;&nbsp;</td><td><span id="branchName" ></span>&nbsp;</td>
						<td class="hd1"><th:block th:text="#{'([^'}"]+)">是否為行員</th:block>&nbsp;&nbsp;</td><td><span id="staff" ></span>&nbsp;</td>                        
	                	<td class="hd1"><th:block th:text="#{'([^'}"]+)">借款人</th:block>&nbsp;&nbsp;</td><td><span id="custId"></span>&nbsp; <span id="dupNo"></span> 　<span id="custName"></span>&nbsp;</td>                            
	                </tr>
	            </tbody>
	        </table>
			<!-- ================================================== -->
			<table width="100%" border="0" cellspacing="0" cellpadding="0">
				<tr><td colspan="7">
					<fieldset>
		                <legend>
		                    <th:block th:text="#{'([^'}"]+)">覆審意見</th:block>
		                </legend>    	
			           	<div>
			           		<table border='0'>
			           			<tr style='vertical-align: top;'>
			           				<td class="hd2 noborder">
			           						<div>	
							           		一、 <label><input type="radio" value="1" name="conFlag" id="conFlag" ><th:block th:text="#{'([^'}"]+)">是</th:block></label>&nbsp;
											    <label><input type="radio" value="2" name="conFlag" id="conFlag" ><th:block th:text="#{'([^'}"]+)">否</th:block></label>&nbsp;&nbsp;
												<th:block th:text="#{'([^'}"]+)">覆審正常</th:block>
											</div>
											<div>	
											二、 <label><input type="radio" value="Y" name="conFlag2A" id="conFlag2A" ><th:block th:text="#{'([^'}"]+)">是</th:block></label>&nbsp;
											    <label><input type="radio" value="N" name="conFlag2A" id="conFlag2A" ><th:block th:text="#{'([^'}"]+)">否</th:block></label>&nbsp;&nbsp;
												<th:block th:text="#{'([^'}"]+)">為代辦貸款案件。</th:block>
											</div>
											<div>	
											三、<th:block th:text="#{'([^'}"]+)">業務人員</th:block>&nbsp;&nbsp;
											    <label><input type="radio" value="N" name="conFlag2B" id="conFlag2B" ><th:block th:text="#{'([^'}"]+)">無</th:block></label>&nbsp;
											    <label><input type="radio" value="Y" name="conFlag2B" id="conFlag2B" ><th:block th:text="#{'([^'}"]+)">有</th:block></label>&nbsp;&nbsp;
												<th:block th:text="#{'([^'}"]+)">與代辦業者掛勾之不法情事。</th:block>
											</div>
											<div>	
											四、<th:block th:text="#{'([^'}"]+)">其他異常或違規事項</th:block>&nbsp;&nbsp;
											    <label><input type="radio" value="N" name="conFlag2C" id="conFlag2C" ><th:block th:text="#{'([^'}"]+)">無</th:block></label>&nbsp;
											    <label><input type="radio" value="Y" name="conFlag2C" id="conFlag2C" ><th:block th:text="#{'([^'}"]+)">有</th:block></label>&nbsp;&nbsp;
												<th:block th:text="#{'([^'}"]+)">(若勾「有」應做說明)</th:block>
											</div>
			           				</td>
			           				<td class="hd2 noborder">
			           					<th:block th:if="${show_default_btn}">
			           						<button type="button" id="btn_conFlag_defaultVal">
												<span class="text-only"><th:block th:text="#{'([^'}"]+)">預設值</th:block></span>
											</button>
										</th:block>
										&nbsp;
			           				</td>
								</tr>
			           		</table>	
			           		
			           		<p>
							<textarea id="condition" name="condition" style="width:800px;height:50px" maxlength='9000' maxlengthC='3000'>
								
							</textarea>
							</p>
							<span class='tit2'><th:block th:text="#{'([^'}"]+)">編製完成日期</th:block>：</span> <span id="upDate" ></span>&nbsp;&nbsp;<span class='tit2'><th:block th:text="#{'([^'}"]+)">上傳人員</th:block>：</span> <span id="approver" ></span>
							
			           	</div>
						
					</fieldset>	
				</td></tr>
				<!-- ================================================== -->	
				<tr class="tb2" style='vertical-align:top;'>
					<td class="hd2 noborder" width="10%"><span class="color-blue"><th:block th:text="#{'([^'}"]+)">覆審單位</th:block></span></td>
					<td class="hd2 noborder rt" width="10%"><th:block th:text="#{'([^'}"]+)">經副襄理</th:block>：</td>
                    <td class="hd2 noborder" width="15%"><span id="manager2" ></span>&nbsp;</td>
					
					<td class="hd2 noborder rt" width="10%"><th:block th:text="#{'([^'}"]+)">覆審主管</th:block>：</td>
					<td class="hd2 noborder" width="15%"><span id="reCheck2" ></span>&nbsp;</td>
					
                    <td class="hd2 noborder rt" width="10%"><th:block th:text="#{'([^'}"]+)">覆審人員</th:block>：</td>
					<td class="hd2 noborder" width="30%"><span id="appraiser2" ></span>&nbsp;</td>
                </tr>
				<!-- ================================================== -->
				<tr><td colspan="7">						
					<fieldset>
		                <legend>
		                    <th:block th:text="#{'([^'}"]+)">附加檔案</th:block>
		                </legend>    	
			           	<button type="button" id="addFiles" class="forview" >
							<span class="text-only"><th:block th:text="#{'([^'}"]+)" </th:block></span>
						</button>	
						<button type="button" id="deleteFiles" class="forview" >
							<span class="text-only"><th:block th:text="#{'([^'}"]+)" </th:block></span>
						</button>
						<div id="gridfile" ></div>			
					</fieldset>		
				</td></tr>
			</table>

        </th:block>
    </body>
</html>
