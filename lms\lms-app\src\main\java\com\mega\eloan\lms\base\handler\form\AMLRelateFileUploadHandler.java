package com.mega.eloan.lms.base.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.response.MegaErrorResult;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Cell;
import jxl.CellType;
import jxl.DateCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 評等對照 上傳
 * </pre>
 * 
 * @since 2016/4/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2016/4/12,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("amlrelatefileuploadhandler")
public class AMLRelateFileUploadHandler extends FileUploadHandler {

	@Autowired
	DocFileService fileService;
	@Resource
	AMLRelateService amlRelateService;
	@Resource
	LMSService lmsService;
	@Resource
	BranchService branchSrv;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	ICustomerService customerSrv;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MultipartFile uFile = params.getFile(params.getString("fieldId"));

		String uid = params.getString(EloanConstants.MAIN_UID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String custRelation = params.getString("custRelation");

		boolean isGetImgDimension = params.getBoolean("getImgDimension");
		String sysId = params.getString("sysId", fileService.getSysId());

		// 設定上傳檔案資訊
		String fileName = uFile.getName();
		String fieldId = Util.trim(params.getString("fieldId"));

		Map<String, String> countryMap = codeTypeService
				.findByCodeType("CountryCode");

		if (params.containsKey("fileSize")) {
			if (uFile.getSize() > params.getLong("fileSize", 1048576)) {
				// EFD0063=ERROR|上送的檔案已超過$\{fileSize\}M的限制大小，無法執行上傳動作。|
				Map<String, String> msg = new HashMap<String, String>();
				msg.put("fileSize",
						CapMath.divide(params.getString("fileSize"), "1048576")); // 1M*1024*1024
				MegaErrorResult result = new MegaErrorResult();
				result.putError(params, new CapMessageException(RespMsgHelper.getMessage("EFD0063", msg), getClass()));
				return result;
			}
		}

		// 留存舊的資料(英文戶名)
		Map<String, String> oldAmlListIdMap = new HashMap<String, String>();
		Map<String, String> oldAmlListNameMap = new HashMap<String, String>();
		List<L120S09A> l120s09as = amlRelateService
				.findL120s09aByMainId(mainId);
		if (l120s09as != null && !l120s09as.isEmpty()) {
			for (L120S09A l120s09a : l120s09as) {
				String tCustId = Util.trim(l120s09a.getCustId());
				String tDupNo = Util.trim(l120s09a.getDupNo());
				String tCustName = Util.toSemiCharString(String.valueOf(Util
						.trim(l120s09a.getCustName())));
				String tCustEName = Util.toSemiCharString(Util.trim(l120s09a
						.getCustEName()));
				String fullKey = tCustId + tDupNo;
				if (Util.notEquals(fullKey, "")) {
					if (!oldAmlListIdMap.containsKey(fullKey)) {
						oldAmlListIdMap.put(fullKey, tCustEName);
					}
				}

				if (Util.notEquals(tCustName, "")) {
					if (!oldAmlListNameMap.containsKey(tCustName)) {
						oldAmlListNameMap.put(tCustName, tCustEName);
					}
				}

			}

		}

		String[] strs = amlRelateService.getSortCustRelation(custRelation
				.split(","));
		// 對陣列進行排序
		if (strs.length > 0) {
			// Arrays.sort(strs);
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			for (String str : strs) {
				sb.append((sb.length() > 0) ? "," : UtilConstants.Mark.SPACE)
						.append(str);
			}
			custRelation = sb.toString();
		}

		// 檢核是否輸入統編
		boolean needCustId = false;
		boolean hasSUP1 = false; // 有負責人
		boolean hasRelateCorp = false; // 有關係企業
		boolean hasRealMan = false; // 有實質受益人
		// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
		boolean needCountry = false;

		for (int i = 0; i < strs.length; i++) {

			if (Util.notEquals(strs[i], "")
					&& amlRelateService.isCustRelationNeedCustId(strs[i])) {
				// 負責人實質受益人可以不用輸入ID
				needCustId = true;
			}

			// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
			if (Util.notEquals(strs[i], "")
					&& amlRelateService.isCustRelationNeedCountry(strs[i])) {
				// 借款人(含共同借款人)及保證人(含一般保證、連帶保證、擔保品提供人)國別
				needCountry = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.負責人)) {
				// 負責人
				hasSUP1 = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.關係企業)) {
				// 關係企業
				hasRelateCorp = true;
			}

			if (Util.equals(strs[i],
					UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人)) {
				// 實質受益人
				hasRealMan = true;
			}

		}

		// 開始匯入EXCEL****************************************************************************
		Workbook workbook = null;
		String errMsg = "";
		InputStream is = null;
		String fileKey = "";
		int[] dimension = { -1, -1 };
		try {

			is = uFile.getInputStream();
			workbook = Workbook.getWorkbook(is);
			Sheet sheet = workbook.getSheet(0);
			int totalCol = sheet.getColumns();
			if (totalCol == 0) {
				// L120S09a.message14=匯入之黑名單EXCEL格式錯誤。
				throw new CapMessageException(
						pop.getProperty("L120S09a.message14"), getClass());
			}
			for (int row = 1; row < sheet.getRows(); row++) {
				int column = 0;

				String custId = "";
				String dupNo = "";
				String custCName = "";
				String custEName = "";
				String country = "";

				if (++column <= totalCol) {
					custId = StringUtils.upperCase(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					dupNo = Util.trim(getContents(sheet
							.getCell(column - 1, row)));
				}

				if (++column <= totalCol) {
					custCName = Util.toSemiCharString(Util
							.trim(getContents(sheet.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					custEName = Util.toSemiCharString(Util
							.trim(getContents(sheet.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					country = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (Util.notEquals(custId, "")) {
					if (StringUtils.length(custId) > 10) {
						// L120S09a.message15=「{0}」欄位內容錯誤。
						throw new CapMessageException(MessageFormat.format(
								pop.getProperty("L120S09a.message15"),
								pop.getProperty("L120S09a.custId")), getClass());
					}
				}

				if (Util.notEquals(dupNo, "")) {
					if (StringUtils.length(dupNo) != 1) {
						// L120S09a.message15=「{0}」欄位內容錯誤。
						throw new CapMessageException(MessageFormat.format(
								pop.getProperty("L120S09a.message15"),
								pop.getProperty("L120S09a.dupNo")), getClass());
					}
				}

				if (needCustId) {
					if (Util.equals(custId, "")) {
						// 「本案關係人統編」欄位不得為空白
						throw new CapMessageException(MessageFormat.format(
								pop.getProperty("L120S09a.message07"),
								pop.getProperty("L120S09a.custId")), getClass());
					}
					if (Util.equals(dupNo, "")) {
						// 「重覆序號」欄位不得為空白
						throw new CapMessageException(MessageFormat.format(
								pop.getProperty("L120S09a.message07"),
								pop.getProperty("L120S09a.dupNo")), getClass());
					}
				} else {
					if (Util.equals(custId, "") && Util.equals(dupNo, "")
							&& Util.equals(custCName, "")) {
						// 「戶名」欄位不得為空白
						throw new CapMessageException(MessageFormat.format(
								pop.getProperty("L120S09a.message07"),
								pop.getProperty("L120S09a.custName")),
								getClass());

					}
				}

				// J-107-0248_05097_B1001 Web e-Loan企金授信管理系統AML/CFT增加國別管制名單掃描功能
				if (Util.equals(country, "")) {
					if (Util.notEquals(custId, "") && Util.notEquals(dupNo, "")) {
						Map<String, String> cust = customerSrv
								.findRegPlaceHouseholdReg(custId, dupNo);
						if (cust != null && !cust.isEmpty()) {
							String ntCode = MapUtils.getString(cust,
									"regPlace", "");
							if (Util.notEquals(ntCode, "")) {
								country = ntCode;
							}

						}
					}
				}

				if (needCountry) {
					if (Util.equals(country, "")) {
						// 「國別」欄位不得為空白
						throw new CapMessageException(MessageFormat.format(
								pop.getProperty("L120S09a.message07"),
								pop.getProperty("L120S09a.country")),
								getClass());
					} else {
						if (!countryMap.containsKey(country)) {

							// AML.error025=e-Loan系統查無此國別「{0}」資料
							throw new CapMessageException(MessageFormat.format(
									pop.getProperty("AML.error025"),
									Util.trim(country)), getClass());

						}
					}
				}

				// J-106-0238-001
				// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
				boolean needEngName = false; // 需要英文戶名 (借款人或共同借款人)
				needEngName = amlRelateService.isL120s09ANeedEngName(
						custRelation, "");
				if (needEngName) {
					if (Util.equals(custEName, "")) {
						StringBuffer noEngNameCust = new StringBuffer("");

						noEngNameCust.append(Util.trim(custId)).append("  ")
								.append(Util.trim(custCName));

						if (Util.notEquals(Util.trim(noEngNameCust.toString()),
								"")) {
							// AML.error018=欲掃描之名單「{0}」必須要有英文戶名。
							throw new CapMessageException(MessageFormat.format(
									pop.getProperty("AML.error018"),
									Util.trim(noEngNameCust.toString())),
									getClass());
						}
					}

				}

				if (Util.notEquals(custCName, "")) {
					custCName = Util.trimSizeInOS390(custCName, 76);
				}

				if (Util.notEquals(custEName, "")) {
					if (Util.strLength(custEName) > 76) {
						throw new CapMessageException(Util.trim(custEName)
								+ "英文戶名太長", getClass());
					}
				}

				amlRelateService.reSetL120S09A(mainId, custId, dupNo,
						custCName, custRelation, custEName, country);

			}

			// 引進名單結束************************************************************************************************************************************

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			// 如果是新模式切回舊模式，要把L120S09B(如果有的話，NCRESULT 清掉)，才不會判斷錯誤(但L120S09B不要刪掉)
			L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
			if (l120s09b != null) {
				l120s09b.setNcResult("");
				l120s09b.setUniqueKey("");
				l120s09b.setQueryDateS(null);
				lmsService.save(l120s09b);
			}

			// 引進名單後，撈英文戶名*********************************

			// 引進英文戶名
			L120M01A l120m01a = amlRelateService.findModelByMainId(
					L120M01A.class, mainId);

			if (l120m01a == null) {
				// 動審表
				L160M01A l160m01a = amlRelateService.findModelByMainId(
						L160M01A.class, mainId);
				if (l160m01a != null) {
					// 簽報書
					l120m01a = amlRelateService
							.findL120m01aByL160m01a(l160m01a);
				}
			}

			// 取得簽報書AND徵信報告AML資料
			Map<String, String> lmsAndCesAmlListIdCNameMap = new HashMap<String, String>();
			Map<String, String> lmsAndCesAmlListIdENameMap = new HashMap<String, String>();
			Map<String, String> lmsAndCesAmlListNameMap = new HashMap<String, String>();
			amlRelateService.getLMSAndCesDocAMLCustNameMap(l120m01a,
					lmsAndCesAmlListIdCNameMap, lmsAndCesAmlListIdENameMap,
					lmsAndCesAmlListNameMap);

			String newCustName = "";
			String newCustEName = "";

			List<L120S09A> newl120s09as = amlRelateService
					.findL120s09aByMainId(mainId);
			if (newl120s09as != null && !newl120s09as.isEmpty()) {
				for (L120S09A l120s09a : newl120s09as) {
					String xCustRelation = l120s09a.getCustRelation();
					String custId = l120s09a.getCustId();
					String dupNo = l120s09a.getDupNo();
					String custName = l120s09a.getCustName();
					String custEName = l120s09a.getCustEName();

					// 1.抓最新的MIS英文名稱
					Map<String, String> nameMap = amlRelateService
							.queryL120s09aNewCustNameForAML("1", mainId,
									l120m01a, xCustRelation, custId, dupNo,
									custName, custEName);

					newCustName = nameMap.get("newCustName");
					newCustEName = nameMap.get("newCustEName");

					// 2.抓簽報書或徵信報告*******************************************************
					// 2.1 BY CUSTID
					if (Util.equals(newCustName, "")
							|| Util.equals(newCustEName, "")) {
						if (Util.notEquals(custId + dupNo, "")) {

							if (Util.equals(newCustName, "")) {
								if (lmsAndCesAmlListIdCNameMap
										.containsKey(custId + dupNo)) {
									newCustName = Util
											.trim(lmsAndCesAmlListIdCNameMap
													.get(custId + dupNo));

									if (Util.notEquals(newCustName, "")) {
										// 有找到新的中文戶名
										newCustName = Util
												.toSemiCharString(Util
														.trim(newCustName));
									}
								}
							}

							if (Util.equals(newCustEName, "")) {
								if (lmsAndCesAmlListIdENameMap
										.containsKey(custId + dupNo)) {
									newCustEName = Util
											.trim(lmsAndCesAmlListIdENameMap
													.get(custId + dupNo));

									if (Util.notEquals(newCustEName, "")) {
										// 有找到新的英文戶名
										newCustEName = Util
												.toSemiCharString(Util
														.trim(newCustEName));
									}
								}
							}

						}
					}

					// 2.2 BY CUSTNAME
					if (Util.equals(newCustEName, "")) {
						if (Util.notEquals(custName, "")) {
							if (lmsAndCesAmlListNameMap.containsKey(custName)) {
								newCustEName = Util
										.trim(lmsAndCesAmlListNameMap
												.get(custName));
								if (Util.notEquals(newCustEName, "")) {
									// 有找到新的英文戶名
									newCustEName = Util.toSemiCharString(Util
											.trim(newCustEName));

								}
							}
						}
					}

					if (Util.equals(newCustName, "")
							&& Util.notEquals(newCustEName, "")) {
						// 有英文名稱，沒有中文名稱(例如實質受益人)
						if (Util.equals(custName, "")) {
							newCustName = newCustEName;
						}
					}

					if (Util.notEquals(newCustName, "")
							|| Util.notEquals(newCustEName, "")) {
						if (Util.notEquals(newCustName, "")) {
							l120s09a.setCustName(Util.toSemiCharString(Util
									.trim(newCustName)));
						}
						if (Util.notEquals(newCustEName, "")) {
							l120s09a.setCustEName(Util.toSemiCharString(Util
									.trim(newCustEName)));
						}
						lmsService.save(l120s09a);
					}
				}

			}

			workbook.close();

		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} catch (BiffException be) {
			logger.error(be.getMessage(), be);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
					if (workbook != null) {
						workbook.close();
						workbook = null;
					}
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}

		}

		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}

		return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
				.set("fileKey", fileKey).set("imgWidth", dimension[0])
				.set("imgHeight", dimension[1]);
	}

	private String getContents(Cell cell) {
		DateCell dCell = null;
		if (cell.getType() == CellType.DATE) {
			dCell = (DateCell) cell;
			// System.out.println("Value of Date Cell is: " + dCell.getDate());
			// ==> Value of Date Cell is: Thu Apr 22 02:00:00 CEST 2088
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			// System.out.println(sdf.format(dCell.getDate()));
			// ==> 2088-04-22
			return sdf.format(dCell.getDate());
		}
		// possibly manage other types of cell in here if needed for your goals
		// read more:
		// http://www.quicklyjava.com/reading-excel-file-in-java-datatypes/#ixzz2fYIkHdZP
		return cell.getContents();
	}
}
