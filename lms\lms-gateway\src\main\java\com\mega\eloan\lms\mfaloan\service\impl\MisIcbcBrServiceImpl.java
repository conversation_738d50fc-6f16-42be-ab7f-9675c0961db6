package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisIcbcBrService;

@Service
public class MisIcbcBrServiceImpl extends AbstractMFAloanJdbc implements
		MisIcbcBrService {

	@Override
	public Map<String, Object> getBankInfo(String brNo) {
		//SELECT * FROM BRNO,BRNM,ADDR,TEL,ENAME FROM MIS.ICBCBR WHERE BRNO = ? 
		return getJdbc().queryForMap("ICBCBR.getBrInfo", new String[] { brNo });
	}

}
