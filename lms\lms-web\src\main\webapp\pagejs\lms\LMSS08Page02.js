initDfd.done(function() { 
	
	var s91 = $("#tabForm08"), tabForm = s91.find("#table1");
	var s91t1f1gridDfd = $.Deferred();
	var s91t1 = s91.find("#s91t1");/*授信往來*/
	var s91t1f1grid, s91t1f2d1grid, s91t1f2d2grid, s91t1f2d3grid
	var s91t1f1form = $("#s91t1f1Form"), 
		s91t1f2d1form = $("#s91t1f2d1Form"),
		s91t1f2d2form = $("#s91t1f2d2Form"), 
		s91t1f2d3form = $("#s91t1f2d3Form");
	var s91t3Form = $("#s91t3Form");
	var init92 = 1, init93 = 1, init94 = 1;
    s91.find("#s91tab").tabs({ //當select 時才去load grid's data
        selected: 0,
        select: function(event, ui){
            if (init92 && ui.index == 1) {
                init92 = 0;
                s91t1f2d1grid.trigger("reloadGrid");
                s91t1f2d2grid.trigger("reloadGrid");
                s91t1f2d3grid.trigger("reloadGrid");
            }
            else 
                if (init93 && ui.index == 2 && $("#ch9FinInfor1").val() == '1') {
                    init93 = 0;
                    s91t3grid.trigger("reloadGrid");
                }
                else 
                    if (init94 && ui.index == 3 && $("#ch9LlandInv1").val() == '1') {
                        init94 = 0;
                        s91t4grid.trigger("reloadGrid");
                    }
            return true;
        }
    });
	
	$.extend(API, {
		//查詢票信及債信狀況
		open : function(settings){
			var s = $.extend({
				subOid: "",
		        validAction: function(form){ // 儲存前動作
		        	return true;
	            }
		    }, settings || {});
			var form = s['form'], dialog = s['dialog'],grid = s['grid'],subOid = s['subOid'];
			form.reset();
			$("#subOid").val(subOid);
			dialog.thickbox({
	    		modal: true,height: s['height'],width: s['width'],
	            open : function(){
	            	if(subOid != ""){
	            		$.ajax({
	                        handler: "lms1205formhandler",action:"query2",
	                        data: {
	                        	type:s['page'],subOid: subOid
	                        },
	                        success: function(json){
	                        	form.injectData(json);
	                        }
	                    });
	            	}
	            	form.find("select[id^=show]").each(function(){
	            		$(this).val(s91.find("#" + $(this).attr('name')).val());
	            	});
	            },
	            buttons: API.createJSON([{
	                key: i18n.def.saveData,
	                value: function(){
	                    if (form.valid() && s.validAction && s.validAction.apply(this, [form])) {
	                        $.ajax({
	                            handler: "lms1205formhandler",action: "saveS",
	                            data: $.extend(form.serializeData(), {
	                            	page : s['page'],
	                            	mainId: responseJSON.mainId,
	                                subOid: $("#subOid").val(),
	                                mainOid: responseJSON.mainId
	                            }),
	                            success: function(json){
	                            	grid.trigger("reloadGrid");$.thickbox.close();
	                            }
	                        });
	                    }
	                }
	            }, {
	                key: i18n.def.close,
	                value: function(){$.thickbox.close()}
	            }])
	        });
		}
	});
	
	s91.find("input[name=toM4]").click(function(){//填列對象
		tabForm.find(".toM4-3")[($(this).val()=='3') ? "show":"hide"]();
	}).end().find("input[name=isGroupCompany1]").click(function(){//填列方式
		tabForm.find("#isGroupCompany1-" + $(this).val()).show().siblings("[id^=isGroupCompany1]").hide();
	}).end().find("#GroupCompanyID1").blur(function(){//集團代號
		if($(this).val() != ''){
			$(this).val(CommonAPI.fillString($(this).val(), 4, false, '0'));
		}
	}).end().find("#s91t1btnQry").click(function(){//集團代號-引進名稱
		var id = s91.find("#GroupCompanyID1");
//		if(id.addClass("required").valid()){
		if(id.val() != null && id.val() != undefined && id.val() != ""){
					s91.find("#GroupCompanyName1").val("");
					$.ajax({
		                handler: "lms1205formhandler",
		                data: {GRPID: id.val(),formAction: "getCompanyName"},
		                success: function(json){
		                	s91.find("#GroupCompanyName1").val(json.GRPNM).end().find("#GroupCompanyID1").val(json.GRPID);
		                }
		            });	
		}else{
		    $.ajax({
		        type: "POST",
		        handler: _handler,
		        data: {
		            formAction: "showGrpData",
		            custId: $("#showBorrowData").find("#custId").val(),
		            dupNo: $("#showBorrowData").find("#dupNo").val()
		        },
		        success: function(responseData){
					if(responseData.L120S01aForm.groupName == "N.A."){
						 CommonAPI.showErrorMessage(i18n.msg('EFD0038').replace(/\${msg}/,$("#showBorrowData").find("#custId").val() + i18n.lmss08['ces1405.9167']));										
/*
						 CommonAPI.showErrorMessage(i18n.msg('EFD0038', {
							          'msg': $("#showBorrowData").find("#custId").val() + i18n.lmss08['ces1405.9167']
							    }));
*/						
					}else{
						s91.find("#GroupCompanyName1").val(responseData.L120S01aForm.groupName).end().find("#GroupCompanyID1").val(responseData.L120S01aForm.groupNo);	
					}					
		        }
		    });	
		}
//		}
//		id.removeClass("required");
	});
	
	/*授信往來-----------------------*/
	s91t1.find("#s91t1f1btnAdd").click(function(){ //新增
		opensA('');
		$("#show_curr5").val("TWD");
		$("#show_curr5").val("1000");
    }).end().find("#s91t1f1btnDel").click(function(){//刪除
		deleteS(s91t1f1grid, 'A');		
    }).end().find("#s91t1f1btnImp").click(function(){//引進集團名單
		$.ajax({
			handler: "lms1205formhandler",
			data : {GroupCompanyID1: s91.find("#GroupCompanyID1").val(), mainId : responseJSON.mainId,formAction : "s91ImpGrpList"},
			success : function(d){
                s91t1f1grid.trigger("reloadGrid");
			}
		});
	})
	
	var s91t2 = s91.find("#s91t2");
	s91t2.end().find("#s91t1f2d1btnAdd").click(function(){
    /*集團企業信用狀況---------------*/
    	opensB('');    	
    }).end().find("#s91t1f2d2btnAdd").click(function(){
    	opensC('');    	
    }).end().find("#s91t1f2d3btnAdd").click(function(){
    	opensD('');    	
    }).end().find("#s91t1f2d1btnDel").click(function(){
    	deleteS(s91t1f2d1grid, 'B');	
    }).end().find("#s91t1f2d2btnDel").click(function(){
    	deleteS(s91t1f2d2grid, 'C');
    }).end().find("#s91t1f2d3btnDel").click(function(){
    	deleteS(s91t1f2d3grid, 'D');
    }).end().find("#s91t1f2btnQry").click(function(){
		var rec = s91t1f1grid.getGridParam("records");
		if(rec == 0){
			API.showMessage(i18n.msg('EFD2057'));
			//並無企業退票、拒絕往來紀錄
		}else{
			$.ajax({
                handler: "lms1205formhandler",
                data: {
                	formAction : "findMSG001",
                	mainId : responseJSON.mainId
                },
                success: function(json){
                	s91t1.injectData(json);
					s91t1f2d1grid.trigger("reloadGrid");
					s91t1f2d2grid.trigger("reloadGrid");
                }
            });
		}		
	});
	
	/*財務資訊----------------------*/
    var s91t3 = s91.find("#s91t3");
    s91t3.find("#s91t3btnAdd").click(function(){
        opensE('');
    }).end().find("#s91t3btnDel").click(function(){
        deleteS(s91t3grid, 'E');
    }).end().find("#s91t3btnQry").click(function(){
        var rec = s91t1f1grid.getGridParam("records");
        if (rec == 0) {
            API.showMessage(i18n.lmss08['ces1405.EFD2058']);
        }
        else {
            var grpFinYear = s91t3.find("#grpFinYear");
            if (grpFinYear.val()) {
				API.confirmMessage(i18n.lmss08['ces1405.EFD2059'], function(result){
                    if (result) {
                        $.ajax({
                            handler: "lms1205formhandler",
                            data: {
                                formAction: "findFintbl",
                                mainId: responseJSON.mainId,
                                grpFinYear: grpFinYear.val()
                            },
                            success: function(json){
                                s91t3.find("#ch9_GFin_SrcDate").val(json.ch9_GFin_SrcDate);
                                s91t3grid.trigger("reloadGrid");
                                if (json.noDATA == 'true') {
                                    API.showMessage(i18n.lmss08['ces1405.EFD2060']);
                                }
                            }
                        });
                    }
                });
            }
            else {
                //ces1401.91msg03=請先輸入財報年度！
                API.showMessage(i18n.lmss08['ces1405.91msg03']);
            }
            
        }
    }).end().find("input[name=ch9FinInfor1]").click(function(){
        s91t3.find("#ch9FinInfor1-1")[($(this).val() == '1') ? "show" : "hide"]();
        if (init93 && $(this).val() == '1') {
            s91t3grid.trigger("reloadGrid");
        }
    });
	
	//集團授信往來明細-------------
	s91t1f1form.find("#s91t1f1d1btnQry").click(function(){
	//引進企業基本資料
		API.includeId({
			addNew: false,
			defaultValue:s91t1f1form.find("#gId").val(),
			autoResponse: { // 是否自動回填資訊 
                id: "gId", //   統一編號欄位ID
                dupno: "dupNo", //   重覆編號欄位ID
                name: "gNa" //   客戶名稱欄位ID
            },
			btnAction: function(id,dupno,name){
				s91t1f1form.find("#gId").val(id);
				s91t1f1form.find("#dupNo").val(dupno);
				$.ajax({
	                handler: "lms1205formhandler",
	                data: {
	                	formAction : "getGrpNm",
	                	GRPID: id,
	                	DUPNO: dupno,
						GRPNM: name
	                },
	                success: function(json){
	                	s91t1f1form.injectData(json);
						if(JSON.stringify(json) == "{}"){
							API.showPopMessage(i18n.msg['EFD0052']);
						}
	                }
	            });
			}
		});
	}).end().find("#s91t1f1d1btnQry2").click(function(){ //引進行內授信資料
		API.showPopMessage(i18n.msg['EFD2056']);
	});
	
	function opensA(subOid){//授信往來明細
		API.open({
			subOid : subOid,
			dialog : $("#s91t1f1dialog"),
			form : s91t1f1form,
			grid : s91t1f1grid,
			page : 'A',
			height : 480,
			width : 600
//            validAction: function(form){
//            	if(form.find(".numeric[value!='']").size() > 0){
//            		if(form.find(".numeric[name^='gOvs'][value!='']").size() > 0){
//            			return true;
//            		}else{
//            			CommonAPI.showErrorMessage(i18n.abstracteloan['EFD2054']);
//            		}
//            	}else{
//            		CommonAPI.showErrorMessage(i18n.abstracteloan['EFD2055']);
//            	}
//            	return false;
//            }
		});
	}
	
	function opensB(subOid){//集團企業有退票紀錄者
		API.open({
			subOid : subOid,
			dialog : $("#s91t1f2d1dialog"),
			form : s91t1f2d1form,
			grid : s91t1f2d1grid,
			page : 'B',
			height : 250,
			width : 650
		});
	}
	
	function opensC(subOid){//集團企業有拒絕往來紀錄者
		API.open({
			subOid : subOid,
			dialog : $("#s91t1f2d2dialog"),
			form : s91t1f2d2form,
			grid : s91t1f2d2grid,
			page : 'C',
			height : 200,
			width : 650
		});
	}
	
	function opensD(subOid){//集團企業有逾期、催收、呆帳紀錄者
		API.open({
			subOid : subOid,
			dialog : $("#s91t1f2d3dialog"),
			form : s91t1f2d3form,
			grid : s91t1f2d3grid,
			page : 'D',
			height : 250,
			width : 500
		});
	}
	
	function opensE(subOid){//集團企業財務資訊明細
        API.open({
            subOid: subOid,
            dialog: $("#s91t3dialog"),
            form: s91t3Form,
            grid: s91t3grid,
            page: 'E',
            height: 430,
            width: 800
        });
    }
	
	function deleteS(grid, type){
		var selrow = grid.getGridParam('selrow');
        if (selrow) {
        	var ret = grid.getRowData(selrow);
        	API.flowConfirmAction({
                message: i18n.def["action_003"],
                handler: "lms1205formhandler",
                data: {
                 formAction : "deleteS2",
               	 type: type,
               	 deleteSubOid: ret.oid
                 },
                success: function(){
               	 CommonAPI.showPopMessage(i18n.def["confirmDeleteSuccess"]);
               	 grid.trigger("reloadGrid");
                }
            });
        }
        else {
            CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
        }
	}
	
	var s91t1f1grid = s91t1.find("#s91t1f1grid").iGrid({/*集團企業授信往來情形*/
		height:150,needPager:false,autowidth:true,localFirst:false,
		handler: 'lms1205gridhandler',
        postData: {gridMainId:responseJSON.mainId,gridUid:responseJSON.mainId,gridType:'A',formAction: 'queryView'},
		colModel: [{ /*"集團企業名稱"*/
			colHeader: i18n.lmss08["ces1405.9109"],name:'gNa',width:180 
		},{ /*"負責人"*/
			colHeader: i18n.lmss08["pcType.1"],name:'gHolder',width:50 
	    },{ /*"全體銀行授信餘額(TWD仟元) "*/
	    	colHeader: i18n.lmss08["ces1405.9110"],name:'gAbkBal',width:150,formatter:'number', align:'right', sortable:false,
			formatoptions:{decimalSeparator:',',thousandsSeparator:',',decimalPlaces:0}        			
		},{ //"本行授信餘額(TWD仟元) ",
            colHeader: i18n.lmss08["ces1405.9111"],name:'gBal',width:150,formatter:'number',align:'right', sortable:false,
			formatoptions:{decimalSeparator:',',thousandsSeparator:',',decimalPlaces:0}        			
		},{ name: 'oid', hidden: true }], 
		ondblClickRow: function(rowid){ opensA(s91t1f1grid.getRowData(rowid).oid); }
    });
    // tabForm.find("#s91t2"); var s91t1 = s91.find("#s91t1");
	var s91t1f2d1grid = s91t2.find("#s91t1f2d1grid").iGrid({/*集團企業有退票紀錄者*/
		height:150,needPager:false,autowidth:true,localFirst:false,
		handler: 'lms1205gridhandler',
        postData: {gridMainId:responseJSON.mainId,gridUid:responseJSON.mainId,gridType: 'B',formAction: 'queryView'},
        colModel: [{ /*"企業名稱"*/ 
        	colHeader: i18n.lmss08["ces1405.A1G001"],name:'grtNa1',width:180 
        }, { /*"最近一次<br/>退票日期"*/
        	colHeader: i18n.lmss08["ces1405.9129"],name:'grtD',align:'center',width: 90 
        }, { /*"退票未清償註記<br/>總張數"*/
        	colHeader: i18n.lmss08["ces1405.9130"],name:'grtNt1',width:100,formatter:'number', align:'right',
			formatoptions:{decimalSeparator:'',thousandsSeparator:',', decimalPlaces: 0}
        }, {/*"退票未清償註記<br/>總金額"*/
        	colHeader: i18n.lmss08["ces1405.9131"],name: 'grtNm1', width: 150, formatter:'number',align:'right', 
			formatoptions:{decimalSeparator:'.',thousandsSeparator:',', decimalPlaces: 2}
        }, {/*"退票清償註記<br/>總張數"*/
        	colHeader: i18n.lmss08["ces1405.9132"],name: 'grtYt1', width: 100, formatter:'number',align:'right', 
			formatoptions:{decimalSeparator:',',thousandsSeparator:',', decimalPlaces: 0}
        }, {/*"退票清償註記<br/>總金額"*/
        	colHeader: i18n.lmss08["ces1405.9133"],name: 'grtYm1', width: 150, formatter:'number', align:'right',
			formatoptions:{decimalSeparator:'.',thousandsSeparator:',', decimalPlaces: 2}
        }, { name: 'oid', hidden: true }], 
        ondblClickRow: function(rowid){ opensB(s91t1f2d1grid.getRowData(rowid).oid); }
    });
	var s91t1f2d2grid = s91t2.find("#s91t1f2d2grid").iGrid({/*集團企業有拒絕往來紀錄者*/
		height:100,needPager:false,autowidth:true,localFirst:false,
		handler: 'lms1205gridhandler',
        postData: {gridMainId:responseJSON.mainId,gridUid:responseJSON.mainId,gridType: 'C',formAction: 'queryView'},
        colModel: [{ /*"企業名稱"*/
        		colHeader: i18n.lmss08["ces1405.A1G001"],name:'grdNa1',width: 100 
        	},{ /*"拒絕往來日期"*/
        		colHeader: i18n.lmss08["ces1405.31M22"],name:'grdD1',align:'center',width:40 
        	},{ name: 'oid', hidden: true }],
        ondblClickRow: function(rowid){ opensC(s91t1f2d2grid.getRowData(rowid).oid); }
    });
	var s91t1f2d3grid = s91t2.find("#s91t1f2d3grid").iGrid({/*集團企業有逾期、催收、呆帳紀錄者*/
		height:100,needPager:false,autowidth:true,localFirst:false,
		handler: 'lms1205gridhandler',
        postData: {gridMainId:responseJSON.mainId,gridUid:responseJSON.mainId,gridType:'D',formAction: 'queryView'},
		colModel: [{ /*"企業名稱"*/
				colHeader: i18n.lmss08["ces1405.A1G001"], name: 'gbaNa1', width: 180 
			},{/*"訂約金額"*/
	            colHeader: i18n.lmss08["ces1405.31M23"],name:'gbaAgm1',align:'right',formatter:'number',width:150, 
				formatoptions:{decimalSeparator:',',thousandsSeparator:',', decimalPlaces: 0}
	        },{/*"授信餘額"*/
	            colHeader: i18n.lmss08["ces1405.31M24"],name:'gbaRamt',align:'right',formatter:'number',width: 150, 
				formatoptions:{decimalSeparator:',',thousandsSeparator:',', decimalPlaces: 0}
	        },{/*"逾期金額"*/
	            colHeader: i18n.lmss08["ces1405.31M25"],name:'gbaOdm1',align:'right',formatter:'number',width:150, 
				formatoptions:{decimalSeparator:',',thousandsSeparator:',', decimalPlaces: 0}
	        },{ name: 'oid', hidden: true }],
        ondblClickRow: function(rowid){ opensD(s91t1f2d3grid.getRowData(rowid).oid); }
    });
	var s91t3grid = s91t3.find("#s91t3grid").iGrid({/*集團企業財務資訊明細*/
        height: 150,
        needPager: false,
        autowidth: true,
        localFirst: true,
        handler: 'lms1205gridhandler',
        action: 'queryView',
        //postData: {gridMainId:'',gridUid:'',gridType: 'E'},
        postData: {
            gridMainId: responseJSON.mainId,
            gridUid: responseJSON.mainId,
            gridType: 'E'
        },
        colModel: [{/*"集團企業名稱統一編號"*/
            colHeader: i18n.lmss08["ces1405.9136"],
            name: 'gId',
            width: 150
        }, { /*"集團企業名稱"*/
            colHeader: i18n.lmss08["ces1405.9109"],
            name: 'gNa',
            width: 200
        }, {
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            opensE(s91t3grid.getRowData(rowid).oid);
        }
    });
});