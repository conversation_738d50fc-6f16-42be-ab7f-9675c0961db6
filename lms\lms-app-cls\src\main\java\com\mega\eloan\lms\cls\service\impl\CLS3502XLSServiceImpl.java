package com.mega.eloan.lms.cls.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.pages.CLS3502V01Page;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 異常通報產Excel
 * </pre>
 * 
 * @since 2012/12/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/3,Miller
 *          </ul>
 */
@Service("cls3052xlsservice")
public class CLS3502XLSServiceImpl implements FileDownloadService {

	@Resource
	EloandbBASEService eloanDbBaseService;

	@Resource
	BranchService branch;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	UserInfoService userInfoService;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS3502XLSServiceImpl.class);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {

		ByteArrayOutputStream baos = null;
		String queryKind = Util.trim(params.getString("queryKind"));

		try {
			baos = Util.equals(queryKind, "1") ? this.genExcel_1(params) : this
					.genExcel_2(params);

			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex.getMessage());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}

	/**
	 * 信保整批貸款回饋檔查詢-申請書
	 * 
	 * @param params
	 * @return
	 */
	private ByteArrayOutputStream genExcel_1(PageParameters params) {
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS3502V01Page.class);
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> listMap = null;
		HSSFWorkbook book = null;
		HSSFFont font12 = null;
		HSSFCellStyle format12Center = null;
		HSSFCellStyle format12CenterNO = null;
		HSSFCellStyle format12Left = null;
		HSSFCellStyle format12Right = null;
		HSSFSheet sheet = null;
		
		String custId = null;
		String ownBrId = null;
		String applyTS_beg = null;
		String applyTS_end = null;

		Map<String, String> descripionMap = new LinkedHashMap<String, String>();
		descripionMap.put("ApproveNo", "案件編號");
		descripionMap.put("BankNo", "銀行代碼");
		descripionMap.put("Agreement", "本行已對個資當事人進行「信保基金蒐集、處理及利用個人資料告知書」聲明");
		descripionMap.put("IsCreditCheck", "本行依移送信用保證之程序規定辦理，填寫申請書前已先行辦妥徵信");
		descripionMap.put("ManagerNo", "授信經理人代號");
		descripionMap.put("Typ", "申請項目");
		descripionMap.put("QualiCode", "保證對象資格");
		descripionMap.put("IsCreditAbnormal", "申請人之票、債信情形");
		descripionMap.put("IdNo", "借款人身分證字號");
		descripionMap.put("Borrower", "借款人姓名");
		descripionMap.put("BorrowerBirthDay", "借款人出生日期");
		descripionMap.put("ApplyLoanDay", "申貸受理日");
		descripionMap.put("ApplyLoan", "申請額度");
		descripionMap.put("Tel1", "聯絡電話_區碼");
		descripionMap.put("Tel2", "聯絡電話_號碼");
		descripionMap.put("OwnerCellphone1", "申請人行動電話區碼");
		descripionMap.put("OwnerCellphone2", "申請人行動電話號碼");
		descripionMap.put("IsEmail", "有無電子郵箱");
		descripionMap.put("Email", "個人電子郵箱");
		descripionMap.put("Zip", "申請人_郵遞區號");
		descripionMap.put("City", "申請人_縣市");
		descripionMap.put("Dist", "申請人_鄉鎮市區");
		descripionMap.put("VillageName", "申請人_里村名");
		descripionMap.put("Village", "申請人_里村");
		descripionMap.put("Neighborhood", "申請人_鄰");
		descripionMap.put("RoadName", "申請人_路/街名");
		descripionMap.put("Road", "申請人_路");
		descripionMap.put("Sec", "申請人_段");
		descripionMap.put("Lane", "申請人_巷");
		descripionMap.put("Alley", "申請人_弄");
		descripionMap.put("No1", "申請人_號");
		descripionMap.put("No2", "申請人_之號");
		descripionMap.put("Floor1", "申請人_樓");
		descripionMap.put("Floor2", "申請人_之樓");
		descripionMap.put("Room", "申請人_室");
		descripionMap.put("ImporterName", "申請書鍵入者姓名");
		descripionMap.put("ImporterCellphone1", "申請書鍵入者行動電話區碼");
		descripionMap.put("ImporterCellphone2", "申請書鍵入者行動電話號碼");
		descripionMap.put("ImporterTel1", "申請書鍵入者電話區碼");
		descripionMap.put("ImporterTel2", "申請書鍵入者電話號碼");
		descripionMap.put("ImporterTelExt", "申請書鍵入者電話分機");
		descripionMap.put("ImporterEmail", "申請書鍵入者電子信箱");
		descripionMap.put("IsImporter", "是否為申請書鍵入者");
		descripionMap.put("StaffName", "銀行經辦姓名");
		descripionMap.put("StaffCellphone1", "銀行經辦行動電話區碼");
		descripionMap.put("StaffCellphone2", "銀行經辦行動電話號碼");
		descripionMap.put("StaffTel1", "銀行經辦電話區碼");
		descripionMap.put("StaffTel2", "銀行經辦電話號碼");
		descripionMap.put("StaffTelExt", "銀行經辦電話分機");
		descripionMap.put("StaffEmail", "銀行經辦電子信箱");

		Map<String, String> returnCodeMap = null;
		Locale locale = null;

		try {
			baos = new ByteArrayOutputStream();
			book = new HSSFWorkbook();
			sheet = book.createSheet("查詢結果");
			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			HSSFPrintSetup printSetUp = sheet.getPrintSetup();
			//設定橫向列印
			printSetUp.setLandscape(true);
			printSetUp.setPaperSize(PrintSetup.A4_PAPERSIZE);
			sheet.setFitToPage(true);
			// 縮放比例頁寬一頁
			printSetUp.setFitWidth((short)1);
			// 縮放比例頁高(5000頁)
			printSetUp.setFitHeight((short)5000);
			// 設定字型與格式、other.msg60=新細明體
			font12 = book.createFont();
			// 設定字體名稱
			font12.setFontName(pop2.getProperty("other.msg60")); 
			// 設定字體大小
			font12.setFontHeightInPoints((short) 12);
			// 設定非粗體
			font12.setBold(false);                               
			
			format12Center = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.CENTER);
			format12Left = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.LEFT);
			format12Right = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.RIGHT);
			format12CenterNO = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.CENTER, false, false);
			
			ownBrId = MegaSSOSecurityContext.getUnitNo();

			custId = StringEscapeUtils.escapeHtml(Util.trim(params.getString("custId")));
			applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			applyTS_end = Util.trim(params.getString("applyTS_end"));

			// 總處單位可以看全部，分行單位只能看自己
			if (Util.notEquals(ownBrId, "")) {
				if (Util.equals(Util.getLeftStr(ownBrId, 1), "9")) {
					ownBrId = "";
				}
			}

			listMap = eloanDbBaseService.findC124m01aByCustIdBatchDate(ownBrId,
					custId, applyTS_beg, applyTS_end);

			locale = LMSUtil.getLocale();
			returnCodeMap = codetypeservice.findByCodeType(
					"cls3501_dataStatus", locale.toString());
			
			HSSFRow row0 = sheet.createRow(0);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));   //合併儲存格(起始列、結束列、起始欄、結束欄)
			//POI升級後無Label，call共用addCell新增儲存格
			LmsExcelUtil.addCell(row0, 0, "信保基金整批回饋檔查詢-申請書", format12CenterNO);
			
			HSSFRow row1 = sheet.createRow(1);
			row1.setHeightInPoints(16.8f);
			//合併第一列，0-2欄
			CellRangeAddress region = null;
			region = new CellRangeAddress(1, 1, 0, 2);
			sheet.addMergedRegion(region);
			//設定合併區的外框線
			setMergeCellBorder(region, sheet);
			LmsExcelUtil.addCell(row1, 0, "身分證統編："
					+ (Util.equals(Util.trim(custId), "") ? "N.A." : custId), format12Left);
			
			//合併第一列，3-12欄
			region = new CellRangeAddress(1, 1, 3, 12);
			sheet.addMergedRegion(region);
			setMergeCellBorder(region, sheet);
			LmsExcelUtil.addCell(row1, 3, "申請書傳送信保日期："
					+ (Util.equals(Util.trim(applyTS_beg), "") ? "N.A."
							: CapDate.formatDate(Util.parseDate(applyTS_beg),
									"yyyy-MM-dd"))
					+ "－"
					+ (Util.equals(Util.trim(applyTS_end), "") ? "N.A."
							: CapDate.formatDate(Util.parseDate(applyTS_end),
									"yyyy-MM-dd")), format12Right);

			// 設定行寬
			int[] colWidths = {16, 16, 6, 40, 20, 20, 16, 20, 40, 10, 10, 20, 12};
			for(int i = 0 ; i < colWidths.length ; i++) {
				sheet.setColumnWidth(i, colWidths[i] * 256); //width單位是 1/256 個字元寬度
			}

			String[] title = { "傳送日期", "身分證統編", "重複序號", "客戶名稱", "案件編號", "保證案號",
					"回覆日", "回覆結果", "處理說明", "送件分行", "申請書鍵入者員編", "申請書鍵入者姓名", "分機" };
			HSSFRow row2 = sheet.createRow(2);
			for (int j = 0 ; j < title.length ; j++) {
				LmsExcelUtil.addCell(row2, j, title[j], format12Center);
			}
			
			if (!listMap.isEmpty()) {
				for (int i = 0, k = 3; i < listMap.size(); i++, k++) {
					HSSFRow dataRow = sheet.createRow(k);
					
					// 通知單申請日期
					String BATCHDATE = "";
					if (listMap.get(i).get("BATCHDATE") != null) {
						BATCHDATE = Util.trim(listMap.get(i).get("BATCHDATE"));
						BATCHDATE = Util.equals(BATCHDATE, "") ? "" : CapDate
								.formatDate(CapDate.parseDate(BATCHDATE),
										"yyyy-MM-dd");
					}
					LmsExcelUtil.addCell(dataRow, 0, BATCHDATE, format12Center);

					// 身分證統編
					String CUSTID = "";
					if (listMap.get(i).get("CUSTID") != null) {
						CUSTID = Util.trim(listMap.get(i).get("CUSTID"));
					}
					LmsExcelUtil.addCell(dataRow, 1, CUSTID, format12Center);

					// 重複序號
					String DUPNO = "";
					if (listMap.get(i).get("DUPNO") != null) {
						DUPNO = Util.trim(listMap.get(i).get("DUPNO"));

					}
					LmsExcelUtil.addCell(dataRow, 2, DUPNO, format12Center);

					// 客戶名稱
					String CUSTNAME = "";
					if (listMap.get(i).get("CUSTNAME") != null) {
						CUSTNAME = Util.trim(listMap.get(i).get("CUSTNAME"));
					}
					LmsExcelUtil.addCell(dataRow, 3, CUSTNAME, format12Left);

					// 案件編號
					String APPROVENO = "";
					if (listMap.get(i).get("APPROVENO") != null) {
						APPROVENO = Util.trim(listMap.get(i).get("APPROVENO"));
					}
					LmsExcelUtil.addCell(dataRow, 4, APPROVENO, format12Center);

					// 保證案號
					String GRNTPAPER = "";
					if (listMap.get(i).get("GRNTPAPER") != null) {
						GRNTPAPER = Util.trim(listMap.get(i).get("GRNTPAPER"));

					}
					LmsExcelUtil.addCell(dataRow, 5, GRNTPAPER, format12Center);

					// "回覆日"
					String RECEIVEDAY = "";
					if (listMap.get(i).get("RECEIVEDAY") != null) {

						RECEIVEDAY = Util
								.trim(listMap.get(i).get("RECEIVEDAY"));
						RECEIVEDAY = Util.equals(RECEIVEDAY, "") ? "" : CapDate
								.formatDate(CapDate.parseDate(RECEIVEDAY),
										"yyyy-MM-dd");
					}
					LmsExcelUtil.addCell(dataRow, 6, RECEIVEDAY, format12Center);

					// "回覆結果"
					String DATASTATUS = "";
					if (listMap.get(i).get("DATASTATUS") != null) {
						DATASTATUS = Util
								.trim(listMap.get(i).get("DATASTATUS"));
						DATASTATUS = DATASTATUS + "."
								+ Util.trim(returnCodeMap.get(DATASTATUS));

						// if (Util.equals(DATASTATUS, "1")) {
						// DATASTATUS = "1.收檔成功";
						// } else if (Util.equals(DATASTATUS, "2")) {
						// DATASTATUS = "2.收檔失敗";
						// } else if (Util.equals(DATASTATUS, "3")) {
						// DATASTATUS = "3.保證書已產生";
						// } else if (Util.equals(DATASTATUS, "4")) {
						// DATASTATUS = "4.拒保或重覆申請等";
						// } else if (Util.equals(DATASTATUS, "5")) {
						// DATASTATUS = "5.送件中";
						// }
					}
					LmsExcelUtil.addCell(dataRow, 7, DATASTATUS, format12Left);

					// "處理說明"
					String DESCRIPTION = "";
					if (listMap.get(i).get("DESCRIPTION") != null) {
						DESCRIPTION = Util.trim(listMap.get(i).get(
								"DESCRIPTION"));
						DESCRIPTION = this.formatDescription(descripionMap,
								DESCRIPTION);

					}
					LmsExcelUtil.addCell(dataRow, 8, DESCRIPTION, format12Left);

					// "送件分行"
					String OWNBRID = "";
					if (listMap.get(i).get("OWNBRID") != null) {
						OWNBRID = Util.trim(listMap.get(i).get("OWNBRID"));

					}
					LmsExcelUtil.addCell(dataRow, 9, OWNBRID, format12Center);

					// "申請書鍵入者行編"
					String IMPORTERNO = "";
					if (listMap.get(i).get("IMPORTERNO") != null) {
						IMPORTERNO = Util
								.trim(listMap.get(i).get("IMPORTERNO"));

					}
					LmsExcelUtil.addCell(dataRow, 10, IMPORTERNO, format12Center);

					// "申請書鍵入者姓名"
					String IMPORTERNONAME = "";
					if (Util.notEquals(IMPORTERNO, "")) {
						IMPORTERNONAME = Util.nullToSpace(userInfoService
								.getUserName(IMPORTERNO));
					}
					LmsExcelUtil.addCell(dataRow, 11, IMPORTERNONAME, format12Left);

					// "分機"
					String IMPORTERTELEXT = "";
					if (listMap.get(i).get("IMPORTERTELEXT") != null) {
						IMPORTERTELEXT = Util.trim(listMap.get(i).get(
								"IMPORTERTELEXT"));

					}
					LmsExcelUtil.addCell(dataRow, 12, IMPORTERTELEXT, format12Center);
				}
			}
			book.write(baos);
			book.close();
			return baos;
		} catch (Exception ex) {
			LOGGER.error("[genExcel_1] Exception!!", ex.getMessage());
		} finally {

			if (returnCodeMap != null) {
				returnCodeMap.clear();
			}

			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[genExcel_1] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}

	/**
	 * 信保整批貸款回饋檔查詢-通知單
	 * 
	 * @param params
	 * @return
	 */
	private ByteArrayOutputStream genExcel_2(PageParameters params) {
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLS3502V01Page.class);
		ByteArrayOutputStream baos = null;
		List<Map<String, Object>> listMap = null;
		HSSFWorkbook book = null;
		HSSFFont font12 = null;
		HSSFCellStyle format12Center = null;
		HSSFCellStyle format12CenterNO = null;
		HSSFCellStyle format12Left = null;
		HSSFCellStyle format12Right = null;
		HSSFSheet sheet = null;
		
		String custId = null;
		String ownBrId = null;
		String applyTS_beg = null;
		String applyTS_end = null;

		Map<String, String> descripionMap = new LinkedHashMap<String, String>();
		descripionMap.put("BankNo", "銀行代碼");
		descripionMap.put("BankKeyNo", "案件編號");
		descripionMap.put("GrntPaper", "保證案號");
		descripionMap.put("IdNo", "借款人身分證字號");
		descripionMap.put("Borrower", "借款人姓名");
		descripionMap.put("ApproveNo", "貴單位核准本案授信之文件編號");
		descripionMap.put("ApproveDay", "批覆書額度核准日");
		descripionMap.put("LoanLimit", "授信單位依授權層級核准之額度");
		descripionMap.put("BegDay", "授信起日");
		descripionMap.put("EndDay", "授信迄日");
		descripionMap.put("Loan", "授信金額");
		descripionMap.put("RetMethod", "還款方式");
		descripionMap.put("FeeTerm", "手續費收取期別");
		descripionMap.put("GracePeriod", "寬限期月數");
		descripionMap.put("LoanRate", "年利率");
		descripionMap.put("FeeMethod", "手續費收取方式");
		descripionMap.put("Rate", "手續費率");
		descripionMap.put("TaxFlag", "票據繳納記號");
		descripionMap.put("AcntFeeFlg", "帳戶管理費用註記");
		descripionMap.put("AcntFeeFlg2", "帳戶管理費收取方式");
		descripionMap.put("AcntFee", "帳戶管理費用");
		descripionMap.put("OtherFeeFlag", "另收取保證費註記");
		descripionMap.put("OtherFeeFlag2", "保證費收取方式");
		descripionMap.put("OtherFee", "另收取保證費");
		descripionMap.put("LoanActNo", "放款帳號");
		descripionMap.put("BankUserName", "銀行經辦姓名");
		descripionMap.put("StaffTel1", "銀行經辦電話區碼");
		descripionMap.put("StaffTel2", "銀行經辦電話號碼");
		descripionMap.put("StaffTelExt", "銀行經辦電話分機");

		Map<String, String> returnCodeMap = null;
		Locale locale = null;

		try {
			baos = new ByteArrayOutputStream();
			book = new HSSFWorkbook();
			sheet = book.createSheet("查詢結果");
			/*
			 * 1.1方向 SheetSetting#setOrientation(PageOrientation po)； 參數：
			 * PageOrientation#LANDSCAPE 橫向打印 PageOrientation# PORTRAIT 縱向打印 (A)
			 * SheetSetting #setScaleFactor (int);百分比形式
			 */
			HSSFPrintSetup printSetUp = sheet.getPrintSetup();
			//設定橫向列印
			printSetUp.setLandscape(true);
			printSetUp.setPaperSize(PrintSetup.A4_PAPERSIZE);
			sheet.setFitToPage(true);
			// 縮放比例頁寬一頁
			printSetUp.setFitWidth((short)1);
			// 縮放比例頁高(5000頁)
			printSetUp.setFitHeight((short)5000);
			// 設定字型與格式、other.msg60=新細明體
			font12 = book.createFont();
			// 設定字體名稱
			font12.setFontName(pop2.getProperty("other.msg60")); 
			// 設定字體大小
			font12.setFontHeightInPoints((short) 12);
			// 設定非粗體
			font12.setBold(false);                               
			
			format12Center = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.CENTER);
			format12Left = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.LEFT);
			format12Right = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.RIGHT);
			format12CenterNO = LmsExcelUtil.setCellFormat(book, font12,
					HorizontalAlignment.CENTER, false, false);

			ownBrId = MegaSSOSecurityContext.getUnitNo();

			custId = StringEscapeUtils.escapeHtml(Util.trim(params.getString("custId")));

			applyTS_beg = Util.trim(params.getString("applyTS_beg"));
			applyTS_end = Util.trim(params.getString("applyTS_end"));

			// 總處單位可以看全部，分行單位只能看自己
			if (Util.notEquals(ownBrId, "")) {
				if (Util.equals(Util.getLeftStr(ownBrId, 1), "9")) {
					ownBrId = "";
				}
			}

			listMap = eloanDbBaseService.findC125m01aByCustIdBatchDate(ownBrId,
					custId, applyTS_beg, applyTS_end);

			locale = LMSUtil.getLocale();
			returnCodeMap = codetypeservice.findByCodeType(
					"cls3502_dataStatus", locale.toString());

			HSSFRow row0 = sheet.createRow(0);
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
			LmsExcelUtil.addCell(row0, 0, "信保基金整批回饋檔查詢-通知單", format12CenterNO);

			HSSFRow row1 = sheet.createRow(1);
			row1.setHeightInPoints(16.8f);
			CellRangeAddress region = null;
			region = new CellRangeAddress(1, 1, 0, 5);
			sheet.addMergedRegion(region);
			//設定合併區的外框線
			setMergeCellBorder(region,sheet);
			LmsExcelUtil.addCell(row1, 0, "身分證統編："
					+ (Util.equals(Util.trim(custId), "") ? "N.A." : custId), format12Left);
			
			region = new CellRangeAddress(1, 1, 6, 11);
			sheet.addMergedRegion(region);
			setMergeCellBorder(region,sheet);
			LmsExcelUtil.addCell(row1, 6, "通知單傳送信保日期："
					+ (Util.equals(Util.trim(applyTS_beg), "") ? "N.A."
							: CapDate.formatDate(Util.parseDate(applyTS_beg),
									"yyyy-MM-dd"))
					+ "－"
					+ (Util.equals(Util.trim(applyTS_end), "") ? "N.A."
							: CapDate.formatDate(Util.parseDate(applyTS_end),
									"yyyy-MM-dd")), format12Right);
			
			// 設定行寬
			int[] colWidths = {16, 16, 6, 40, 20, 20, 16, 20, 20, 16, 20, 40};
			for(int i = 0 ; i < colWidths.length ; i++) {
				sheet.setColumnWidth(i, colWidths[i] * 256); //width單位是 1/256 個字元寬度
			}
			
			String[] title = { "傳送日期", "身分證統編", "重複序號", "客戶名稱", "案件編號(額度序號)",
					"保證案號", "額度核准日", "核准額度", "通知單編號", "回覆日", "回覆結果", "處理說明" };
			HSSFRow row2 = sheet.createRow(2);
			for (int j = 0 ; j < title.length ; j++) {
				LmsExcelUtil.addCell(row2, j, title[j], format12Center);
			}
			
			if (!listMap.isEmpty()) {
				for (int i = 0, k = 3; i < listMap.size(); i++, k++) {
					HSSFRow dataRow = sheet.createRow(k);
					
					// 通知單申請日期
					String BATCHDATE = "";
					if (listMap.get(i).get("BATCHDATE") != null) {
						BATCHDATE = Util.trim(listMap.get(i).get("BATCHDATE"));
						BATCHDATE = Util.equals(BATCHDATE, "") ? "" : CapDate
								.formatDate(CapDate.parseDate(BATCHDATE),
										"yyyy-MM-dd");
					}
					LmsExcelUtil.addCell(dataRow, 0, BATCHDATE, format12Center);

					// 身分證統編
					String CUSTID = "";
					if (listMap.get(i).get("CUSTID") != null) {
						CUSTID = Util.trim(listMap.get(i).get("CUSTID"));

					}
					LmsExcelUtil.addCell(dataRow, 1, CUSTID, format12Center);

					// 重複序號
					String DUPNO = "";
					if (listMap.get(i).get("DUPNO") != null) {
						DUPNO = Util.trim(listMap.get(i).get("DUPNO"));
					}
					LmsExcelUtil.addCell(dataRow, 2, DUPNO, format12Center);

					// 客戶名稱
					String CUSTNAME = "";
					if (listMap.get(i).get("CUSTNAME") != null) {
						CUSTNAME = Util.trim(listMap.get(i).get("CUSTNAME"));

					}
					LmsExcelUtil.addCell(dataRow, 3, CUSTNAME, format12Left);

					// 案件編號(額度序號)
					String BANKKEYNO = "";
					if (listMap.get(i).get("BANKKEYNO") != null) {
						BANKKEYNO = Util.trim(listMap.get(i).get("BANKKEYNO"));
					}
					LmsExcelUtil.addCell(dataRow, 4, BANKKEYNO, format12Center);

					// 保證案號
					String GRNTPAPER = "";
					if (listMap.get(i).get("GRNTPAPER") != null) {
						GRNTPAPER = Util.trim(listMap.get(i).get("GRNTPAPER"));
					}
					LmsExcelUtil.addCell(dataRow, 5, GRNTPAPER, format12Center);

					// "額度核准日"
					String APPROVEDAY = "";
					if (listMap.get(i).get("APPROVEDAY") != null) {

						APPROVEDAY = Util
								.trim(listMap.get(i).get("APPROVEDAY"));
						APPROVEDAY = Util.equals(APPROVEDAY, "") ? "" : CapDate
								.formatDate(CapDate.parseDate(APPROVEDAY),
										"yyyy-MM-dd");
					}
					LmsExcelUtil.addCell(dataRow, 6, APPROVEDAY, format12Center);

					// "核准額度"
					BigDecimal LOANLIMIT = null;
					if (listMap.get(i).get("LOANLIMIT") != null) {

						LOANLIMIT = Util.equals(
								Util.trim(Util.trim(listMap.get(i).get(
										"LOANLIMIT"))), "") ? BigDecimal.ZERO
								: new BigDecimal(Util.trim(Util.trim(listMap
										.get(i).get("LOANLIMIT"))));
					}
					LmsExcelUtil.addCell(dataRow, 7, NumConverter.addComma(
							LOANLIMIT.toPlainString(), "#,###,###,###,##0"),
							format12Right);

					// "通知單編號"
					String SRLNO = "";
					if (listMap.get(i).get("SRLNO") != null) {
						SRLNO = Util.trim(listMap.get(i).get("SRLNO"));

					}
					LmsExcelUtil.addCell(dataRow, 8, SRLNO, format12Center);

					// "回覆日"
					String RECEIVEDAY = "";
					if (listMap.get(i).get("RECEIVEDAY") != null) {

						RECEIVEDAY = Util
								.trim(listMap.get(i).get("RECEIVEDAY"));
						RECEIVEDAY = Util.equals(RECEIVEDAY, "") ? "" : CapDate
								.formatDate(CapDate.parseDate(RECEIVEDAY),
										"yyyy-MM-dd");

					}
					LmsExcelUtil.addCell(dataRow, 9, RECEIVEDAY, format12Center);

					// "回覆結果"
					String DATASTATUS = "";
					if (listMap.get(i).get("DATASTATUS") != null) {
						DATASTATUS = Util
								.trim(listMap.get(i).get("DATASTATUS"));

						DATASTATUS = DATASTATUS + "."
								+ Util.trim(returnCodeMap.get(DATASTATUS));

						// if (Util.equals(DATASTATUS, "1")) {
						// DATASTATUS = "1.收檔成功";
						// } else if (Util.equals(DATASTATUS, "2")) {
						// DATASTATUS = "2.收檔失敗";
						// } else if (Util.equals(DATASTATUS, "3")) {
						// DATASTATUS = "3.入帳成功";
						// } else if (Util.equals(DATASTATUS, "4")) {
						// DATASTATUS = "4.退件";
						// } else if (Util.equals(DATASTATUS, "5")) {
						// DATASTATUS = "5.送件中";
						// }
					}
					LmsExcelUtil.addCell(dataRow, 10, DATASTATUS, format12Left);

					// "處理說明"
					String DESCRIPTION = "";
					if (listMap.get(i).get("DESCRIPTION") != null) {
						DESCRIPTION = Util.trim(listMap.get(i).get(
								"DESCRIPTION"));
						DESCRIPTION = this.formatDescription(descripionMap,
								DESCRIPTION);

					}
					LmsExcelUtil.addCell(dataRow, 11, DESCRIPTION, format12Left);

				}
			}
			book.write(baos);
			book.close();
			return baos;
		} catch (Exception ex) {
			LOGGER.error("[genExcel_2] Exception!!", ex.getMessage());
		} finally {

			if (returnCodeMap != null) {
				returnCodeMap.clear();
			}

			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[genExcel_2] Exception!!", ex.getMessage());
				}
			}
		}
		return null;
	}

	String formatDescription(Map<String, String> descripionMap,
			String DESCRIPTION) {
		String outputStr = Util.trim(DESCRIPTION);
		if (Util.equals(outputStr, "")) {
			return outputStr;
		}

		for (String colName : descripionMap.keySet()) {
			if (StringUtils.contains(outputStr, colName)) {
				outputStr = StringUtils.replace(outputStr, colName, colName
						+ "【" + descripionMap.get(colName) + "】");
			}
		}

		return outputStr;
	}
	
	/**
	 * poi升級，設定合併儲存格邊框顯示
	 * 
	 * @param region 
	 * 			合併區域
	 * @param sheet 

	 * @return
	 */
	private void setMergeCellBorder(CellRangeAddress region, HSSFSheet sheet) {
		RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet);
		RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
	}
	
}
