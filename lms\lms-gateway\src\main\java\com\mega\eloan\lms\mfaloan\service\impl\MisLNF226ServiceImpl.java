/* 
 *MisLNF226ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLNF226Service;

import tw.com.jcs.common.Util;

/**<pre>
 * 授信逾放比例檔
 * </pre>
 * @since  2012/2/6
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/2/6,REX,new
 *          </ul>
 */
@Service
public class MisLNF226ServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF226Service {
	public List<Map<String, Object>> findLNF226ForRate(String[] ownBrIds) {
		String ownBridParams = Util.genSqlParam(ownBrIds);
//		StringBuffer temp = new StringBuffer(0);
//		for (String ownBrId : ownBrIds) {
//			temp.append(temp.length() > 0 ? "," : "");
//			temp.append("'");
//			temp.append(ownBrId);
//			temp.append("'");
//		}
		List<Object> params = new ArrayList<Object>();
		params.addAll(Arrays.asList(ownBrIds));
		params.addAll(Arrays.asList(ownBrIds));

		return this.getJdbc().queryForListByCustParam("LNF226.selByBrno",
				new Object[] { ownBridParams, ownBridParams },
				params.toArray(new Object[0]));
	}
}
