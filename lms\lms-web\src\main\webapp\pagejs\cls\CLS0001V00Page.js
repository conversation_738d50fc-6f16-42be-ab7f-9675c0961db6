$(document).ready(function(){
    var grid = $("#gridview").iGrid({
        handler: 'cls0001gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        postData: {
            formAction: "query"
        },
        rowNum: 15,
        autowidth: false,
        colModel: [{
            colHeader: i18n.cls0001v00['v0005.caseName'],//文件名稱,
            name: 'caseType',
            width: 100,
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.cls0001v00['v0005.caseNo'],//案號,
            name: 'caseNo',
            width: 200,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.cls0001v00['v0005.mainCustId'],//主要借款人統編,
            name: 'custId',
            width: 100,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.cls0001v00['v0005.mainCust'],//主要借款人,
            name: 'custName',
            width: 120,
            sortable: true,
            align: "left"
        }, {
            colHeader: i18n.cls0001v00['v0005.sendTime'],//傳送時間,
            name: 'sendInfo',
            width: 100,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.cls0001v00['v0005.sendOwin'],//傳送單位,
            name: 'sendBrid',
            width: 100,
            sortable: true,
            align: "left"
        }, {
            name: 'srcOid',
            hidden: true
        }, {
            name: 'srcMainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }, {
            name: 'docType',
            hidden: true
        }, {
            name: 'docCode',
            hidden: true
        }, {
            name: 'docKind',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',//'../cls/cls0005v00/01'
            data: {
                oid: rowObject.srcOid,
                mainId: rowObject.srcMainId,
                mainOid: rowObject.srcOid,
                mainDocStatus: rowObject.docStatus,
                docType: rowObject.docType,
                docCode: rowObject.docCode,
                docKind: rowObject.docKind,
                docURL: rowObject.docURL
            },
            target: rowObject.srcOid
        });
    }
    
    
    $("#buttonPanel").find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
        
    });
});
