/* 
 * lms7120FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.form;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.ELRoleEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.BeanValidator;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.panels.LMSL140M01MPanel;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.fms.constants.fmsConstants;
import com.mega.eloan.lms.fms.service.LMS7120Service;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF511Service;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L712M01A;
import com.mega.eloan.lms.model.L918S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 停權解除維護FormHandler
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7120formhandler")
public class LMS7120FormHandler extends AbstractFormHandler {

	@Resource
	LMS7120Service service7120;

	@Resource
	BranchService branch;

	@Resource
	MisELF511Service misElf511Service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	LMSService lmsService;

	@Resource
	NumberService number;

	/**
	 * 設定所有分行
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult setAllBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> map = new TreeMap<String, String>();
		List<IBranch> list = branch.getAllBranch();
		for (IBranch thisBranch : list) {
			map.put(Util.trim(thisBranch.getBrNo()),
					Util.trim(thisBranch.getBrName()));
		}
		CapAjaxFormResult caseBrId = new CapAjaxFormResult(map);
		result.set("caseBrId", caseBrId);
		return result;
	}

	/**
	 * 查詢停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL712m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formStopDetail1 = new CapAjaxFormResult();
		CapAjaxFormResult lms140m01qform = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L712M01A meta = service7120.findL712m01aByMainId(mainId);
		if (meta == null) {
			meta = new L712M01A();
		}

		L140M01Q l140m01q = lmsService
				.findModelByMainId(L140M01Q.class, mainId);

		String cntrNoQ = "";
		boolean showBefore = false;
		boolean showDerv = false;

		String cntrNo = Util.trim(meta.getCntrNo());

		// 無資料時，要抓前案資料供使用者比對
		if (l140m01q == null && Util.notEquals(cntrNo, "")) {
			// Map<String, Object> elf506 =
			// misELF506Service.getByCntrNo(cntrNo);
			Map<String, Object> elf506 = lmsService
					.getELF506ByBranchAndCntrno(cntrNo);
			if (elf506 != null && !elf506.isEmpty()) {

				lms140m01qform = lmsService.setResultFromELF506(elf506, cntrNo,
						"b", lms140m01qform);

				// String elf506CntrNo = MapUtils.getString(elf506,
				// "ELF506_CNTRNO", "");
				// String bcnLoanFg = MapUtils.getString(elf506,
				// "ELF506_CN_LOAN_FG", "");
				// String bigolFlag = MapUtils.getString(elf506,
				// "ELF506_IGOL_FLAG", "");
				// String bdirectFg = MapUtils.getString(elf506,
				// "ELF506_DIRECT_FG", "");
				// // J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
				// String bcnBusKind = MapUtils.getString(elf506,
				// "ELF506_CN_BUS_KIND", "");
				// String bsTradeFg = MapUtils.getString(elf506,
				// "ELF506_S_TRADE_FG", "");
				//
				// String bcnTMUFg = MapUtils.getString(elf506,
				// "ELF506_CN_TMU_FG", "");
				//
				// BigDecimal bguar1Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_GUAR1_RATE", BigDecimal.ZERO);
				// BigDecimal bguar2Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_GUAR2_RATE", BigDecimal.ZERO);
				// BigDecimal bguar3Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_GUAR3_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll1Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL1_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll2Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL2_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll3Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL3_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll4Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL4_RATE", BigDecimal.ZERO);
				// BigDecimal bcoll5Rate = (BigDecimal)
				// MapUtils.getObject(elf506,
				// "ELF506_COLL5_RATE", BigDecimal.ZERO);
				//
				// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				// String bloanTarget = MapUtils.getString(elf506,
				// "ELF506_LOAN_TARGET", "");
				// String bisType = MapUtils.getString(elf506, "ELF506_IS_TYPE",
				// "");
				// String bgrntType = MapUtils.getString(elf506,
				// "ELF506_GRNT_TYPE", "");
				// String bgrntClass = MapUtils.getString(elf506,
				// "ELF506_GRNT_CLASS", "");
				// String bothCrdType = MapUtils.getString(elf506,
				// "ELF506_OTHCRD_TYPE", "");
				// String bunionArea3 = MapUtils.getString(elf506,
				// "ELF506_UNION_AREA3", "");
				// // END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				//
				// // J-105-0074-001 Web e-Loan
				// // 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
				// String bnCnSblcFg = MapUtils.getString(elf506,
				// "ELF506_NCN_SBLC_FG", "");
				//
				// // elf506.get("ELF506_MODIFYTIME");
				// // elf506.get("ELF506_CREATETIME");
				// // elf506.get("ELF506_CREATEUNIT");
				// // elf506.get("ELF506_MODIFYUNIT");
				// // elf506.get("ELF506_DOCUMENT_NO");
				// lms140m01qform.set("bcnLoanFg", bcnLoanFg);
				// lms140m01qform.set("biGolFlag", bigolFlag);
				// lms140m01qform.set("bdirectFg", bdirectFg);
				// // J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
				// lms140m01qform.set("bcnBusKind", bcnBusKind);
				// lms140m01qform.set("bsTradeFg", bsTradeFg);
				// lms140m01qform.set("bguar1Rate", bguar1Rate);
				// lms140m01qform.set("bguar2Rate", bguar2Rate);
				// lms140m01qform.set("bguar3Rate", bguar3Rate);
				// lms140m01qform.set("bcoll1Rate", bcoll1Rate);
				// lms140m01qform.set("bcoll2Rate", bcoll2Rate);
				// lms140m01qform.set("bcoll3Rate", bcoll3Rate);
				// lms140m01qform.set("bcoll4Rate", bcoll4Rate);
				// lms140m01qform.set("bcoll5Rate", bcoll5Rate);
				//
				// lms140m01qform.set("bcnTMUFg", bcnTMUFg);
				//
				// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				// lms140m01qform.set("bloanTarget", bloanTarget);
				// lms140m01qform.set("bisType", bisType);
				// lms140m01qform.set("bgrntType", bgrntType);
				// lms140m01qform.set("bgrntClass", bgrntClass);
				//
				// // result.set("bothCrdType", bothCrdType);
				// // ELF506_OTHCRD_TYPE="YYY                 "
				// bothCrdType = lmsService
				// .formatOthCrdTypeFromElf506(bothCrdType);
				// if (Util.isNotEmpty(bothCrdType)) {
				// String[] bothCrdTypeArray = bothCrdType.split("\\|");
				// JSONArray value = new JSONArray();
				// for (String txt : bothCrdTypeArray) {
				// if (Util.isNotEmpty(Util.trim(txt))) {
				// value.add(Util.trim(txt));
				// }
				// }
				// lms140m01qform.set("bothCrdType", value.toString());
				// }
				//
				// lms140m01qform.set("bunionArea3", bunionArea3);
				// // END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
				//
				// BigDecimal bTotal =
				// bguar1Rate.add(bguar2Rate).add(bguar3Rate)
				// .add(bcoll1Rate).add(bcoll2Rate).add(bcoll3Rate)
				// .add(bcoll4Rate).add(bcoll5Rate);
				// if (bTotal.intValue() == 100) {
				// lms140m01qform.set("brickTrFg", "Y");
				// } else {
				// lms140m01qform.set("brickTrFg", "Y");
				// }
				//
				// // J-105-0074-001 Web e-Loan
				// // 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
				// lms140m01qform.set("bnCnSblcFg", bnCnSblcFg);

				showBefore = true;
				cntrNoQ = cntrNo;

			}
		}

		if (l140m01q != null) {

			lms140m01qform = DataParse
					.toResult(l140m01q, DataParse.Delete, new String[] {
							EloanConstants.MAIN_ID, EloanConstants.OID });

			cntrNoQ = l140m01q.getCntrNoQ();
			result = DataParse
					.toResult(l140m01q, DataParse.Delete, new String[] {
							EloanConstants.MAIN_ID, EloanConstants.OID });

			String bcnLoanFg = CapString.trimNull(l140m01q.getBcnLoanFg());
			if (!"".equals(bcnLoanFg)) {
				showBefore = true;
			}

			String sTradeFg = l140m01q.getsTradeFg();
			if ("N".equals(sTradeFg)) {
				BigDecimal guar1Rate = l140m01q.getGuar1Rate();
				BigDecimal guar2Rate = l140m01q.getGuar2Rate();
				BigDecimal guar3Rate = l140m01q.getGuar3Rate();
				BigDecimal coll1Rate = l140m01q.getColl1Rate();
				BigDecimal coll2Rate = l140m01q.getColl2Rate();
				BigDecimal coll3Rate = l140m01q.getColl3Rate();
				BigDecimal coll4Rate = l140m01q.getColl4Rate();
				BigDecimal coll5Rate = l140m01q.getColl5Rate();

				BigDecimal total = guar1Rate.add(guar2Rate).add(guar3Rate)
						.add(coll1Rate).add(coll2Rate).add(coll3Rate)
						.add(coll4Rate).add(coll5Rate);
				if (total.intValue() == 100) {
					lms140m01qform.set("rickTrFg", "Y");
				} else {
					lms140m01qform.set("rickTrFg", "N");
				}
			}

			String bsTradeFg = l140m01q.getBsTradeFg();
			if ("N".equals(bsTradeFg)) {
				BigDecimal bguar1Rate = l140m01q.getBguar1Rate();
				BigDecimal bguar2Rate = l140m01q.getBguar2Rate();
				BigDecimal bguar3Rate = l140m01q.getBguar3Rate();
				BigDecimal bcoll1Rate = l140m01q.getBcoll1Rate();
				BigDecimal bcoll2Rate = l140m01q.getBcoll2Rate();
				BigDecimal bcoll3Rate = l140m01q.getBcoll3Rate();
				BigDecimal bcoll4Rate = l140m01q.getBcoll4Rate();
				BigDecimal bcoll5Rate = l140m01q.getBcoll5Rate();

				BigDecimal btotal = bguar1Rate.add(bguar2Rate).add(bguar3Rate)
						.add(bcoll1Rate).add(bcoll2Rate).add(bcoll3Rate)
						.add(bcoll4Rate).add(bcoll5Rate);
				if (btotal.intValue() == 100) {
					lms140m01qform.set("brickTrFg", "Y");
				} else {
					lms140m01qform.set("brickTrFg", "N");
				}
			}

			// J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			// 將db存的值轉為jsonArray
			if (Util.isNotEmpty(l140m01q.getOthCrdType())) {
				String othCrdType = l140m01q.getOthCrdType();
				String[] othCrdTypeArray = othCrdType.split("\\|");
				JSONArray value = new JSONArray();
				for (String txt : othCrdTypeArray) {
					if (Util.isNotEmpty(Util.trim(txt))) {
						value.add(Util.trim(txt));
					}
				}
				lms140m01qform.set("othCrdType", value.toString());
			}

			if (Util.isNotEmpty(l140m01q.getBothCrdType())) {
				String bothCrdType = l140m01q.getBothCrdType();
				String[] bothCrdTypeArray = bothCrdType.split("\\|");
				JSONArray value = new JSONArray();
				for (String txt : bothCrdTypeArray) {
					if (Util.isNotEmpty(Util.trim(txt))) {
						value.add(Util.trim(txt));
					}
				}
				lms140m01qform.set("bothCrdType", value.toString());
			}
			// J-104-00279-001 END

		}

		result.set("cntrNoQ", cntrNoQ);
		result.set("showBefore", showBefore);

		showDerv = true;

		result.set("showDerv", showDerv);

		// J-105-0074-001 Web e-Loan
		// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
		boolean isCnBrno = false;
		if (Util.notEquals(cntrNo, "")) {
			String cntrNoBranch = Util.trim(Util.getLeftStr(cntrNo, 3));

			if (Util.notEquals(cntrNoBranch, "")) {
				IBranch ibranch = branch.getBranch(cntrNoBranch);
				// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
				if (ibranch != null) {
					if (Util.equals(ibranch.getCountryType(), "CN")) {
						isCnBrno = true;
					}
				}

			}
		}

		if (isCnBrno) {
			result.set("showForCNCntrno", true);
		} else {
			result.set("showForCNCntrno", false);
		}

		// lms140m01qform

		// 針對欄位做額外處理後設值到前端
		formStopDetail1.set("caseBrId", Util.isEmpty(Util.trim(meta
				.getCaseBrId())) ? "N.A." : Util.trim(meta.getCaseBrId()) + " "
				+ branch.getBranchName(Util.nullToSpace(meta.getCaseBrId())));
		formStopDetail1.set(
				"custId",
				Util.isEmpty(Util.trim(meta.getCustId())) ? "N.A." : Util
						.trim(meta.getCustId()));
		formStopDetail1
				.set("dupNo",
						Util.isEmpty(Util.trim(meta.getDupNo())) ? UtilConstants.Mark.SPACE
								: Util.trim(meta.getDupNo()));
		formStopDetail1
				.set("custName",
						Util.isEmpty(Util.trim(meta.getCustName())) ? UtilConstants.Mark.SPACE
								: Util.trim(meta.getCustName()));
		formStopDetail1.set("caseNo", Util.trim(meta.getCaseNo()));

		formStopDetail1.set("stopUpdater",
				getPerName(Util.trim(meta.getStopUpdater())));
		formStopDetail1.set("stopApprover",
				getPerName(Util.trim(meta.getStopApprover())));
		formStopDetail1.set("caseDate", TWNDate.toFullAD(meta.getCaseDate()));
		formStopDetail1.set("stopApprTime",
				TWNDate.toFullAD(meta.getStopApprTime()));
		formStopDetail1.set("cntrNo", meta.getCntrNo());
		result.set("lms140m01qform", lms140m01qform);
		result.set("formStopDetail1", formStopDetail1);

		return result;
	}

	/**
	 * 查詢停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL918s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult formStopDetail2 = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L918S01A model = service7120.findL918s01aByOid(oid);
		if (model == null) {
			model = new L918S01A();
		}
		formStopDetail2 = DataParse.toResult(model, DataParse.Delete, "oid",
				"mainId");
		// TODO 案號要做特殊處理(顯示要用)
		formStopDetail2.set(
				"modlifyMons",
				(Util.isEmpty(Util.trim(model.getStatusFlag()))) ? Util
						.trim(model.getSuspendMons()) : Util.trim(model
						.getModlifyMons()));
		result.set("formStopDetail2", formStopDetail2);
		return result;
	}

	/**
	 * 依照使用者選擇刪除停權所有相關資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult startDel(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L712M01A l712m01a = service7120.findL712m01aByMainId(mainId);
		if (isAuthForDel(mainId)) {
			// 具有刪除權限
			// 進行刪除
			// deleteTable.properties 有設L712M01A deletedTime 刪除時一併刪除L140M01Q
			service7120.delete(l712m01a);

		} else {
			// 不具有刪除權限
			Map<String, String> param = new HashMap<String, String>();
			param.put("txCode", UtilConstants.Mark.HTMLSPACE);
			param.put("methodName", UtilConstants.Mark.HTMLSPACE);
			param.put("authType", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0004", param), getClass());
		}
		// 印出刪除成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}

	/**
	 * 判定是否具有刪除權限
	 * 
	 * @param oid
	 *            文件Oid
	 * @return true: 具有權限, false: 不具有權限
	 */
	private boolean isAuthForDel(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 主管
		if (user.getRoles().containsKey(ELRoleEnum.主管.getCode())) {
			return true;
		} else {
			// 非主管
			L712M01A l712m01a = service7120.findL712m01aByMainId(mainId);
			if (user.getUserId().equals(l712m01a.getUpdater())) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 查詢停權明細檔資料並建立停權主檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult startQuery(PageParameters params)
			throws CapException {
		// 取得當前使用者資料
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = IDGenerator.getUUID();
		String caseBrId = Util.trim(params.getString("caseBrId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String cntrNo = Util.trim(params.getString("cntrNo"));

		// 開始設定停權主檔
		L712M01A l712m01a = new L712M01A();
		l712m01a.setMainId(mainId);
		l712m01a.setCustId(custId);
		l712m01a.setDupNo(dupNo);
		l712m01a.setCustName(custName);
		l712m01a.setCaseBrId(caseBrId);
		l712m01a.setOwnBrId(user.getUnitNo());
		l712m01a.setDocStatus(CreditDocStatusEnum.授管處_停權編製中);
		l712m01a.setDocURL("/fms/lms7120m01");
		l712m01a.setStopUpdater(user.getUserId());
		l712m01a.setCaseDate(new Date());
		l712m01a.setCntrNo(cntrNo);

		result = (CapAjaxFormResult) reQueryElf506(params);

		// l712m01a.setAlnModifyTime(CapDate.parseDate(Util.trim(result.get("modifyTime"))));

		if (Util.notEquals(result.get("modifyTime"), "")) {
			try {

				SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
				Date modifyTime = sdf.parse(Util.trim(result.get("modifyTime"))
						.replaceAll("\\D", "")); // \d 數字 [0-9] \D 非數字 [^0-9]
				Timestamp ts = new Timestamp(modifyTime.getTime());
				l712m01a.setAlnModifyTime(ts);

			} catch (ParseException e) {
				// EFD0001=ERROR|$\{colName\}日期錯誤|
				// 告訴user，這個日期不是一個正確的日期"
				HashMap<String, String> colName = new HashMap<String, String>();
				colName.put("colName", "");
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0001", colName), getClass());
			}

		}
		l712m01a.setAlnModifyUnit((String) result.get("modifyUnit"));
		l712m01a.setAlnDocumentNo((String) result.get("documentNo"));

		// 存檔
		// service7120.save(l712m01a);

		String l140m01qForm = Util.trim(result.toString());
		L140M01Q l140m01q = lmsService
				.findModelByMainId(L140M01Q.class, mainId);
		if (l140m01q == null) {
			l140m01q = new L140M01Q();
			l140m01q.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01q.setCreator(user.getUserId());
			l140m01q.setMainId(mainId);
		}
		l140m01q.setUpdateTime(CapDate.getCurrentTimestamp());
		l140m01q.setUpdater(user.getUserId());

		DataParse.toBean(l140m01qForm, l140m01q);
		if (!BeanValidator.isValid(l140m01q)) {
			throw new CapMessageException(BeanValidator.getValidMsg(l140m01q,
					LMSL140M01MPanel.class), getClass());
		}
		// lmsService.save(l140m01q);

		service7120.save(l712m01a, l140m01q);

		// 開始設定要拋到前端的參數

		result.set(EloanConstants.MAIN_ID, mainId);
		result.set(EloanConstants.OID, Util.trim(l712m01a.getOid()));
		result.set("docURL", "/fms/lms7120m01");
		result.set("mainDocStatus", CreditDocStatusEnum.授管處_停權編製中.getCode());
		result.set("custId", custId);
		result.set("dupNo", dupNo);
		result.set("caseBrId", caseBrId);
		result.set("cntrNo", cntrNo);

		/*
		 * result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		 * .getMainMessage(this.getComponent(),
		 * UtilConstants.AJAX_RSP_MSG.執行成功));
		 */
		return result;
	}

	/**
	 * 儲存停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL712m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String lms140m01qform = params.getString("lms140m01qform");
		String cntrNo = Util.trim(params.getString("cntrNo"));

		L712M01A l712m01a = service7120.findL712m01aByMainId(mainId);
		if (l712m01a == null) {
			l712m01a = new L712M01A();
		}

		l712m01a.setStopUpdater(user.getUserId());
		l712m01a.setCaseDate(new Date());
		l712m01a.setCntrNo(cntrNo);

		L140M01Q l140m01q = lmsService
				.findModelByMainId(L140M01Q.class, mainId);

		if (l140m01q == null) {
			l140m01q = new L140M01Q();
			l140m01q.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01q.setCreator(user.getUserId());
			l140m01q.setMainId(mainId);
		}

		DataParse.toBean(lms140m01qform, l140m01q);

		l140m01q.setUpdateTime(CapDate.getCurrentTimestamp());
		l140m01q.setUpdater(user.getUserId());

		if (!BeanValidator.isValid(l140m01q)) {
			throw new CapMessageException(BeanValidator.getValidMsg(l140m01q,
					LMSL140M01MPanel.class), getClass());
		}

		// J-105-0074-001 Web e-Loan
		// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
		boolean isCnBrno = false;
		if (Util.notEquals(cntrNo, "")) {
			String cntrNoBranch = Util.trim(Util.getLeftStr(cntrNo, 3));

			if (Util.notEquals(cntrNoBranch, "")) {
				IBranch ibranch = branch.getBranch(cntrNoBranch);
				// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
				if (Util.equals(ibranch.getCountryType(), "CN")) {
					isCnBrno = true;
				}
			}
		}

		if (!isCnBrno) {
			l140m01q.setNCnSblcFg("");
		} else {
			if (Util.equals(Util.trim(l140m01q.getNCnSblcFg()), "")) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMSL140M01MPanel.class);
				// L140M01a.message168=額度明細表->大陸地區授信業務控管註記->是否由非大陸地區本行聯行開具擔保信用狀十足保證欄位不得為空白。
				throw new CapMessageException(
						prop.getProperty("L140M01a.message168"), getClass());

			}
		}

		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
		// 若無案號則進行給號
		if (Util.isEmpty(Util.trim(l712m01a.getCaseSeq()))
				&& Util.isEmpty(Util.trim(l712m01a.getCaseNo()))) {
			l712m01a.setCaseSeq(Integer.parseInt(number.getNumberWithMax(
					L712M01A.class, unit.getUnitNo(), null, 99999)));
			l712m01a.setCaseYear(Util.parseInt(TWNDate.toAD(new Date())
					.substring(0, 4)));
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branch.getBranch(unit.getUnitNo());
			caseNum.append(
					Util.toFullCharString(Util.trim(l712m01a.getCaseYear())))
					.append(Util.trim(ibranch.getNameABBR()))
					.append(UtilConstants.Field.兆)
					.append(UtilConstants.Field.授字第)
					.append(Util.toFullCharString(Util.addZeroWithValue(
							Util.trim(l712m01a.getCaseSeq()), 5)))
					.append(UtilConstants.Field.號);
			l712m01a.setCaseNo(caseNum.toString());
			l712m01a.setUpdater(unit.getUserId());
			l712m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}

		result.set("caseNo", l712m01a.getCaseNo());

		service7120.save(l712m01a, l140m01q);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		// if(true){
		// throw new CapMessageException("TEST", getClass());
		// }
		return result;
	}

	/**
	 * 刪除停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult delL918s01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String listOid = Util.trim(params.getString("list"));
		List<L918S01A> list = new ArrayList<L918S01A>();
		if (Util.isNotEmpty(listOid)) {
			String oids[] = listOid.split(",");
			for (String oid : oids) {
				L918S01A model = service7120.findL918s01aByOid(oid);
				if (model != null) {
					String modifyMons = Util.trim(model.getModlifyMons());
					Timestamp updateTime = CapDate.getCurrentTimestamp();
					model.setUpdater(user.getUserId());
					model.setUpdateTime(updateTime);
					if (fmsConstants.stopRelease.statusFlag.新增.equals(Util
							.trim(model.getStatusFlag()))) {
						// 若狀態Flag原為A
						// 將狀態Flag註記為R並將L900S01A停權月數(L900S01A.suspendMons)
						// 設為修改停權月數(L900S01A.modlifyMons)，
						// 最後將L900S01A修改停權月數(L900S01A.modlifyMons)更改為0
						model.setStatusFlag(fmsConstants.stopRelease.statusFlag.新增後刪除);
						model.setSuspendMons(Util.parseInt(modifyMons));
						model.setModlifyMons(0);
					} else {
						// 將狀態Flag註記為D並將L900S01A修改停權月數(L900S01A.modlifyMons)更改為0
						model.setStatusFlag(fmsConstants.stopRelease.statusFlag.刪除);
						model.setModlifyMons(0);
					}
					list.add(model);
				}
			}
		}
		service7120.saveList918s01a(list);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}

	/**
	 * 取消刪除停權明細畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult undoDelL918s01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String listOid = Util.trim(params.getString("list"));
		List<L918S01A> list = new ArrayList<L918S01A>();
		if (Util.isNotEmpty(listOid)) {
			String oids[] = listOid.split(",");
			for (String oid : oids) {
				L918S01A model = service7120.findL918s01aByOid(oid);
				if (model != null) {
					String suspendMons = Util.trim(model.getSuspendMons());
					Timestamp updateTime = CapDate.getCurrentTimestamp();
					model.setUpdater(user.getUserId());
					model.setUpdateTime(updateTime);
					if (fmsConstants.stopRelease.statusFlag.新增後刪除.equals(Util
							.trim(model.getStatusFlag()))) {
						// 狀態Flag原先為R
						// 將狀態Flag註記設為A並將L900S01A修改停權月數(L900S01A.modlifyMons)
						// 設為L900S01A停權月數(L900S01A.suspendMons)值，
						// 最後將L900S01A停權月數(L900S01A.suspendMons)清空
						model.setStatusFlag(fmsConstants.stopRelease.statusFlag.新增);
						model.setModlifyMons(Util.parseInt(suspendMons));
						model.setSuspendMons(null);
					} else if (fmsConstants.stopRelease.statusFlag.刪除
							.equals(Util.trim(model.getStatusFlag()))) {
						// 將狀態Flag清空並將L900S01A修改停權月數(L900S01A.modlifyMons)
						// 設為L900S01A停權月數(L900S01A.suspendMons)值
						model.setStatusFlag(UtilConstants.Mark.SPACE);
						model.setModlifyMons(Util.parseInt(suspendMons));
					}
					list.add(model);
				}
			}
		}
		service7120.saveList918s01a(list);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;
	}

	/**
	 * flow案件簽報書
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String list = Util.trim(params.getString("list"));
		if (Util.isNotEmpty(oid)) {
			// 若oid不為空，則走單一流程
			L712M01A l712m01a = (L712M01A) service7120.findL712m01aByOid(oid);
			if (l712m01a == null) {
				logger.info("\n l712m01a=====> null");
			}
			startFlow(params, l712m01a, oid);
		} else {
			// 若list不為空，代表同時有很多案件跑同一流程
			if (Util.isNotEmpty(list)) {
				String oids[] = list.split(",");
				for (String thisOid : oids) {
					L712M01A l712m01a = (L712M01A) service7120
							.findL712m01aByOid(thisOid);
					if (l712m01a == null) {
						logger.info("\n l712m01a=====> null");
					}
					startFlow(params, l712m01a, thisOid);
				}
			} else {
				// 例外情形，正常下不會發生
				logger.info("\n l712m01a=====> null");
			}
		}
		return new CapAjaxFormResult();
	}

	/**
	 * 開始進行流程
	 * 
	 * @param params
	 * @param parent
	 * @param l712m01a
	 * @param oid
	 * @throws CapMessageException
	 */
	private void startFlow(PageParameters params,
			L712M01A l712m01a, String oid) throws CapMessageException {
		if (!Util.isEmpty(l712m01a)) {
			try {
				service7120.flowAction(oid, l712m01a,
						params.containsKey("flowAction"),
						params.getString("flowAction", ""));
			} catch (FlowMessageException t1) {
				logger.error(
						"[flowAction] lms7120Service.flowAction FlowException!!",
						t1);
				if (t1.getExtraMessage() == null
						|| t1.getExtraMessage().isEmpty()) {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
				} else {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage(), t1.getExtraMessage()),
							getClass());
				}
			} catch (Throwable t1) {
				logger.error(
						"[flowAction] lms7120Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}
	}

	/**
	 * 取得完整Id(統編加重覆序號)
	 * 
	 * @param custid
	 * @param dupNo
	 * @return
	 */
	private String getAllCust(String custid, String dupNo) {
		StringBuilder strb = new StringBuilder();
		// if ("0".equals(dupNo)) {
		// dupNo = "";
		// }
		return strb.append(CapString.fillString(custid, 10, false, ' '))
				.append(dupNo).toString();
	}

	/**
	 * 依照完整客戶統編取得Map資料
	 * 
	 * @param allCust
	 *            完整客戶統編
	 * @return Map資料
	 */
	private Map<String, String> getCustMap(String allCust) {
		Map<String, String> map = new HashMap<String, String>();
		if (Util.isNotEmpty(allCust)) {
			map.put("custId", Util.trim((LMSUtil.checkSubStr(allCust, 0,
					allCust.length() - 1)) ? allCust.substring(0,
					allCust.length() - 1) : UtilConstants.Mark.SPACE));
			map.put("dupNo",
					Util.trim((LMSUtil.checkSubStr(allCust,
							allCust.length() - 1)) ? allCust.substring(allCust
							.length() - 1) : UtilConstants.Mark.SPACE));
		}
		return map;
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者id + " " + 使用者名稱
	 */
	private String getPerName(String id) {
		StringBuilder sb = new StringBuilder();
		if (!Util.isEmpty(userinfoservice.getUserName(id))) {
			sb.append(id).append(" ").append(userinfoservice.getUserName(id));
		} else {
			sb.append(id);
		}
		return sb.toString();
	}

	/**
	 * 查詢停權明細檔資料並建立停權主檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult reQueryElf506(PageParameters params)
			throws CapException {
		// 取得當前使用者資料
		CapAjaxFormResult result = new CapAjaxFormResult();

		String l140m01qForm = Util.trim(params.getString("lms140m01qform"));
		if (Util.notEquals(l140m01qForm, "")) {

			JSONObject js = JSONObject.fromObject(l140m01qForm);

			result.set("cnLoanFg", js.optString("cnLoanFg"));
			result.set("iGolFlag", js.optString("iGolFlag"));
			result.set("directFg", js.optString("directFg"));
			// J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			result.set("cnBusKind", js.optString("cnBusKind"));
			result.set("sTradeFg", js.optString("sTradeFg"));
			result.set("rickTrFg", js.optString("rickTrFg"));
			result.set("guar1Rate", js.optString("guar1Rate"));
			result.set("guar2Rate", js.optString("guar2Rate"));
			result.set("guar3Rate", js.optString("guar3Rate"));

			result.set("coll1Rate", js.optString("coll1Rate"));
			result.set("coll2Rate", js.optString("coll2Rate"));
			result.set("coll3Rate", js.optString("coll3Rate"));
			result.set("coll4Rate", js.optString("coll4Rate"));
			result.set("coll5Rate", js.optString("coll5Rate"));

			// J-103-0076 衍生性金融商品立約人身份別
			result.set("cnTMUFg", js.optString("cnTMUFg"));

			// BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			result.set("loanTarget", js.optString("loanTarget"));
			result.set("isType", js.optString("isType"));
			result.set("grntType", js.optString("grntType"));
			result.set("grntClass", js.optString("grntClass"));
			result.set("othCrdType", js.optString("othCrdType"));
			result.set("unionArea3", js.optString("unionArea3"));
			// END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別

			// J-105-0074-001 Web e-Loan
			// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
			result.set("nCnSblcFg", js.optString("nCnSblcFg"));

		}

		String cntrNo = Util.trim(params.getString("cntrNo"));
		if (Util.isEmpty(cntrNo)) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMSCommomPage.class);
			HashMap<String, String> msg = new HashMap<String, String>();
			msg.put("msg", Util.trim((String) prop.get("lmsL120M01A.error006")));
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		boolean showBefore = false;
		boolean showDerv = false;
		// Map<String, Object> elf506 = misELF506Service.getByCntrNo(cntrNo);
		Map<String, Object> elf506 = lmsService
				.getELF506ByBranchAndCntrno(cntrNo);
		if (elf506 != null && !elf506.isEmpty()) {

			result = lmsService
					.setResultFromELF506(elf506, cntrNo, "b", result);

			// String elf506CntrNo = MapUtils.getString(elf506, "ELF506_CNTRNO",
			// "");
			// String bcnLoanFg = MapUtils.getString(elf506,
			// "ELF506_CN_LOAN_FG",
			// "");
			// String bigolFlag = MapUtils.getString(elf506, "ELF506_IGOL_FLAG",
			// "");
			// String bdirectFg = MapUtils.getString(elf506, "ELF506_DIRECT_FG",
			// "");
			// // J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			// String bcnBusKind = MapUtils.getString(elf506,
			// "ELF506_CN_BUS_KIND", "");
			// String bsTradeFg = MapUtils.getString(elf506,
			// "ELF506_S_TRADE_FG",
			// "");
			// String bcnTMUFg = MapUtils
			// .getString(elf506, "ELF506_CN_TMU_FG", "");
			//
			// BigDecimal bguar1Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_GUAR1_RATE", BigDecimal.ZERO);
			// BigDecimal bguar2Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_GUAR2_RATE", BigDecimal.ZERO);
			// BigDecimal bguar3Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_GUAR3_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll1Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL1_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll2Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL2_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll3Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL3_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll4Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL4_RATE", BigDecimal.ZERO);
			// BigDecimal bcoll5Rate = (BigDecimal) MapUtils.getObject(elf506,
			// "ELF506_COLL5_RATE", BigDecimal.ZERO);
			//
			// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			//
			// if (Util.equals(bcnLoanFg, "N")) {
			// bigolFlag = "";
			// }
			//
			// String bloanTarget = MapUtils.getString(elf506,
			// "ELF506_LOAN_TARGET", "");
			// String bisType = MapUtils.getString(elf506, "ELF506_IS_TYPE",
			// "");
			// String bgrntType = MapUtils.getString(elf506, "ELF506_GRNT_TYPE",
			// "");
			// String bgrntClass = MapUtils.getString(elf506,
			// "ELF506_GRNT_CLASS",
			// "");
			// String bothCrdType = MapUtils.getString(elf506,
			// "ELF506_OTHCRD_TYPE", "");
			// String bunionArea3 = MapUtils.getString(elf506,
			// "ELF506_UNION_AREA3", "");
			// // END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			//
			// // J-105-0074-001 Web e-Loan
			// // 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
			// String bnCnSblcFg = MapUtils.getString(elf506,
			// "ELF506_NCN_SBLC_FG", "");
			//
			// // elf506.get("ELF506_MODIFYTIME");
			// // elf506.get("ELF506_CREATETIME");
			// // elf506.get("ELF506_CREATEUNIT");
			// // elf506.get("ELF506_MODIFYUNIT");
			// // elf506.get("ELF506_DOCUMENT_NO");
			// result.set("bcnLoanFg", bcnLoanFg);
			// result.set("biGolFlag", bigolFlag);
			// result.set("bdirectFg", bdirectFg);
			// // J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			// result.set("bcnBusKind", bcnBusKind);
			// result.set("bsTradeFg", bsTradeFg);
			// result.set("bguar1Rate", bguar1Rate);
			// result.set("bguar2Rate", bguar2Rate);
			// result.set("bguar3Rate", bguar3Rate);
			// result.set("bcoll1Rate", bcoll1Rate);
			// result.set("bcoll2Rate", bcoll2Rate);
			// result.set("bcoll3Rate", bcoll3Rate);
			// result.set("bcoll4Rate", bcoll4Rate);
			// result.set("bcoll5Rate", bcoll5Rate);
			// result.set("bcnTMUFg", bcnTMUFg);
			//
			// // BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			// result.set("bloanTarget", bloanTarget);
			// result.set("bisType", bisType);
			// result.set("bgrntType", bgrntType);
			// result.set("bgrntClass", bgrntClass);
			//
			// // result.set("bothCrdType", bothCrdType);
			// // ELF506_OTHCRD_TYPE="YYY                 "
			// bothCrdType = lmsService.formatOthCrdTypeFromElf506(bothCrdType);
			// if (Util.isNotEmpty(bothCrdType)) {
			// String[] bothCrdTypeArray = bothCrdType.split("\\|");
			// JSONArray value = new JSONArray();
			// for (String txt : bothCrdTypeArray) {
			// if (Util.isNotEmpty(Util.trim(txt))) {
			// value.add(Util.trim(txt));
			// }
			// }
			// result.set("bothCrdType", value.toString());
			// }
			//
			// result.set("bunionArea3", bunionArea3);
			// // END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			//
			// // J-105-0074-001 Web e-Loan
			// // 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
			// result.set("bnCnSblcFg", bnCnSblcFg);
			// BigDecimal bTotal = bguar1Rate.add(bguar2Rate).add(bguar3Rate)
			// .add(bcoll1Rate).add(bcoll2Rate).add(bcoll3Rate)
			// .add(bcoll4Rate).add(bcoll5Rate);
			// if (bTotal.intValue() == 100) {
			// result.set("brickTrFg", "Y");
			// } else {
			// result.set("brickTrFg", "N");
			// }

			result.set("cntrNo",
					MapUtils.getString(elf506, "ELF506_CNTRNO", ""));
			result.set("cntrNoQ",
					MapUtils.getString(elf506, "ELF506_CNTRNO", ""));
			// result.set("modifyTime",
			// MapUtils.getString(elf506, "ELF506_MODIFYTIME", ""));

			result.set("modifyTime",
					MapUtils.getString(elf506, "ELF506_MODIFYTIME", ""));
			result.set("createTime",
					MapUtils.getString(elf506, "ELF506_CREATETIME", ""));

			result.set("createUnit",
					MapUtils.getString(elf506, "ELF506_CREATEUNIT", ""));
			result.set("modifyUnit",
					MapUtils.getString(elf506, "ELF506_MODIFYUNIT", ""));
			result.set("documentNo",
					MapUtils.getString(elf506, "ELF506_DOCUMENT_NO", ""));

			result.set("elf506NoData", "N");
			showBefore = true;

		} else {
			result.set("elf506NoData", "Y");
		}

		// 開始設定要拋到前端的參數
		result.set("cntrNo", cntrNo);
		result.set("showBefore", showBefore);

		showDerv = true;
		result.set("showDerv", showDerv);

		// J-105-0074-001 Web e-Loan
		// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
		boolean isCnBrno = false;
		if (Util.notEquals(cntrNo, "")) {
			String cntrNoBranch = Util.trim(Util.getLeftStr(cntrNo, 3));

			if (Util.notEquals(cntrNoBranch, "")) {
				IBranch ibranch = branch.getBranch(cntrNoBranch);
				// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
				if (ibranch != null) {
					if (Util.equals(ibranch.getCountryType(), "CN")) {
						isCnBrno = true;
					}
				}

			}
		}

		if (isCnBrno) {
			result.set("showForCNCntrno", true);
		} else {
			result.set("showForCNCntrno", false);
		}

		/*
		 * result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		 * .getMainMessage(this.getComponent(),
		 * UtilConstants.AJAX_RSP_MSG.執行成功));
		 */
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL140M01Q(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString("mainId"));
		String l140m01qForm = Util.trim(params.getString("LMS140M01QForm"));
		L140M01Q l140m01q = lmsService
				.findModelByMainId(L140M01Q.class, mainId);
		if (l140m01q == null) {
			l140m01q = new L140M01Q();
			l140m01q.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01q.setCreator(user.getUserId());
			l140m01q.setMainId(mainId);
		}
		l140m01q.setUpdateTime(CapDate.getCurrentTimestamp());
		l140m01q.setUpdater(user.getUserId());

		DataParse.toBean(l140m01qForm, l140m01q);
		if (!BeanValidator.isValid(l140m01q)) {
			throw new CapMessageException(BeanValidator.getValidMsg(l140m01q,
					LMSL140M01MPanel.class), getClass());
		}

		// J-105-0074-001 Web e-Loan
		// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
		boolean isCnBrno = false;
		L712M01A meta = service7120.findL712m01aByMainId(mainId);
		if (meta == null) {
			meta = new L712M01A();
		}

		String cntrNo = Util.trim(meta.getCntrNo());
		if (Util.notEquals(cntrNo, "")) {
			String cntrNoBranch = Util.trim(Util.getLeftStr(cntrNo, 3));

			if (Util.notEquals(cntrNoBranch, "")) {
				IBranch ibranch = branch.getBranch(cntrNoBranch);
				// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
				if (ibranch != null) {
					if (Util.equals(ibranch.getCountryType(), "CN")) {
						isCnBrno = true;
					}
				}

			}
		}

		if (!isCnBrno) {
			l140m01q.setNCnSblcFg("");
		}

		lmsService.save(l140m01q);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));

		return result;
	}

	/**
	 * 查詢停權明細檔資料並建立停權主檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult chkElf506Exist(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		result = (CapAjaxFormResult) reQueryElf506(params);

		String elf506NoData = Util.trim(result.get("elf506NoData")).equals("") ? "N"
				: Util.trim(result.get("elf506NoData"));

		result.set("elf506NoData", elf506NoData);

		return result;
	}

}
