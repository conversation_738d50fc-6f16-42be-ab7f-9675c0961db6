package com.mega.eloan.lms.fms.handler.grid;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.fms.service.LMS7005Service;
import com.mega.eloan.lms.model.L700M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  案件分案對照表(GridHandler)
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/9/29,Miller Lin,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7005gridhandler")
public class LMS7005GridHandler extends AbstractGridHandler {

	@Resource
	LMS7005Service service;
	@Resource
	UserInfoService userSrv;	
	@Resource
	BranchService branch;

	//
	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryL700m01a(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 建立主要Search 條件
//		pageSetting.addOrderBy("branchId");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"ownBrId", user.getUnitNo());
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service.findPage(L700M01A.class,
				pageSetting);
		try {
			for (int e = 0; e < params.getInt("rowNum"); e++) {
				L700M01A model = (L700M01A) page.getContent().get(e);
				String getbraname = branch.getBranchName(model.getBranchId());
				model.setBranchId(model.getBranchId() + " " + getbraname);
				model.setUserNo(getPerName(model.getUserNo()));
				model.setReCheck(getPerName(model.getReCheck()));
				StringBuilder sbGrp = new StringBuilder();
				sbGrp.append(model.getGroupId()).append(" ").append(branch.getBranchName(model.getGroupId()));
				model.setGroupId(Util.trim(sbGrp.toString()));
			}
		} catch (java.lang.IndexOutOfBoundsException ex) {
			for (int e = 0; e < (page.getTotalRow() - params.getInt("rowNum")
					* (page.getPageSize() - 1)); e++) {
				L700M01A model2 = (L700M01A) page.getContent().get(e);
				String getbraname2 = branch.getBranchName(model2.getBranchId());
				model2.setBranchId(model2.getBranchId() + " " + getbraname2);
				model2.setUserNo(getPerName(model2.getUserNo()));
				model2.setReCheck(getPerName(model2.getReCheck()));
				StringBuilder sbGrp = new StringBuilder();
				sbGrp.append(model2.getGroupId()).append(" ").append(branch.getBranchName(model2.getGroupId()));
				model2.setGroupId(Util.trim(sbGrp.toString()));
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
	
	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * @param id 使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id){
		return (!Util.isEmpty(userSrv.getUserName(id))
		 ? userSrv.getUserName(id): id);
	}
}
