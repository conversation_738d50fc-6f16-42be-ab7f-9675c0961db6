$(document).ready(function(){
	
    var grid = $("#gridview").iGrid({
        handler: 'lms7800gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL140mm6a",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "approveTime|createTime",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{
	        colHeader: i18n.lms7800v01["L140MM6A.custId"], //借款戶統一編號
	        align: "left", width: 100, sortable: true, name: 'custId',
	        formatter: 'click', onclick: openDoc
	    }, {
	        colHeader: i18n.lms7800v01["L140MM6A.custName"], //借款戶名稱
	        align: "left", width: 100, sortable: true, name: 'custName'
	    },{
            colHeader: i18n.lms7800v01['L140MM6A.cntrNo'],//"額度序號",
            name: 'cntrNo',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms7800v01['L140MM6A.creator'],//"分行經辦",
            name: 'updater',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms7800v01['L140MM6A.approver'],//"覆核",
            name: 'approver',
            width: 80,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.lms7800v01["L140MM6A.approveTime"], // 核准日期
                align: "left",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'approveTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryL140mm6a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    $("#buttonPanel").find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            // L140M01M.error1=此功能不能多選
            CommonAPI.showMessage(i18n.lms7800v01["L140MM6A.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
		
    });
});
