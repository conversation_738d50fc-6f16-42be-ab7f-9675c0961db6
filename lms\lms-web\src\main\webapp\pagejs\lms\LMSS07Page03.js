var oldOid = "";
var _handler = "";
initDfd.done(function() {	
	A_1_8_1();
	A_1_8_2();
	gridviewCust();
	
	$("#formL120s04b").find("#grpGrrd").change(function(i){
		var $formL120s04b = $("#formL120s04b");
		var grpNo = $("#formL120s04b").find("#grpNo").html();
		var grpGrrd = $(this).val();
		
		//J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。			
		var grpYear =  $formL120s04b.find("#grpYear").html();
		 
		if(grpYear){
			//判斷2017以後為新版，之前為舊版
			if(parseInt(grpYear, 10) >= 2017){
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
				if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5" || grpGrrd == "6" || grpGrrd == "7"){
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				}else{
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}			
			}else{
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
				if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				}else{
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}			
			}
			
			
		}else{
			//如果沒有評等年度，以舊版執行
			// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
			if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
				// 顯示屬主要集團企業...
				$formL120s04b.find(".spectialHide").show();
			}else{
				// 隱藏屬主要集團企業...
				$formL120s04b.find(".spectialHide").hide();
			}			
		}
		
		
	});	
/*
	$("#custSearch").click(function(){
		if($("#formAdd").valid()){
			$("#gridviewCust").jqGrid("setGridParam", {//重新設定grid需要查到的資料
				postData : {
					formAction:"queryL120s01aById",
					custId : $("#formAdd").find("#searchId").val()
				},
				search: true
			}).trigger("reloadGrid");  
		}
	});
*/
	$("#buttonSearch03").click(function(){
		if($("#formAdd").valid()){
	        $.ajax({
	            type: "POST",
	            handler: responseJSON["handler"],
	            data: {
	                formAction: "getCustData",
	                custId: $("#formAdd").find("#searchId03").val()
	            },
	            success: function(responseData03){
	                // alert(JSON.stringify(responseData));
				      var selJson03 = {
					       		item : responseData03.selCus,
					       		format : "{value} - {key}",
					       		space: true
					       	};
				      $("#selCus03").setItems(selJson03);				  
				      $("#showSel03").show();
	            }
	        });						
		}
	});	
	
	//上傳檔案按鈕
	$("#uploadComFile").click(function(){
/*
		var count=$("#gridviewPare").jqGrid('getGridParam','records');
		if(count == 1){
			// other.msg173=最多只能產生(上傳)一筆借戶暨關係戶與本行授信往來比較表！
			return CommonAPI.showMessage(i18n.lmscommom["other.msg173"]);
		}
*/
		var limitFileSize=3145728;
		MegaApi.uploadDialog({
			fieldId:"LMSNoList",
            fieldIdHtml:"size='30'",
            fileDescId:"fileDesc",
            fileDescHtml:"size='30' maxlength='30'",
			subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
			limitSize:limitFileSize,
            width:320,
            height:190,			
			data:{
				mainId:$("#oidL120s04d").val()//responseJSON.mainId
			},
			success : function(obj) {
				$("#gridviewPare").trigger("reloadGrid");
			}
	   });
	});	
	//刪除檔案按鈕
	$("#deleteComFile").click(function(){
		var select  = $("#gridviewPare").getGridParam('selrow');		
		// confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){				
				var data = $("#gridviewPare").getRowData(select);
				if(data.oid == "" || data.oid == undefined || data.oid == null){		
					// TMMDeleteError=請先選擇需修改(刪除)之資料列
					CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
					return;
				}				
				$.ajax({
					handler : "lms1205formhandler",
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteUploadFile",
						fileOid:data.oid
					},
					success : function(obj) {
						$("#gridviewPare").trigger("reloadGrid");
					}
				});
			}else{
				return ;
			}
		});
	});		
	gridviewPare("gridviewPare", "LMSNoList");
});

// 設定附加檔案Grid
function gridviewPare(fileGridId, fieldId){
	//檔案上傳grid
	$("#gridviewPare").iGrid({
		handler : 'lms1205gridhandler', 
		height : 50,
		sortname : 'srcFileName',
		postData : {
			formAction : "queryfile",
			fieldId:fieldId,
			mainId:$("#oidL120s04d").val(),//responseJSON.mainId,
			needCngName : true
		},
		rowNum : 15,
		caption: "&nbsp;",
		hiddengrid : false,
		//expandOnLoad : true,	//只對subgrid有用
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lmss07['L1205S07.grid42'],//other.msg152=報表名稱
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false,
			formatter : 'click',
			onclick : openFile
		}, {
			colHeader : i18n.lmss07['L1205S07.index33'],//L1205S07.index33=上傳時間
			name : 'uploadTime',
			width : 140,
			sortable : false
		}, {
			name : 'oid',
			hidden : true
		}]
	});		
}

function openFile(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}

function gridviewCust(){
	var gridView = $("#gridviewCust").iGrid({
		handler: 'lms1205gridhandler',
		//height: 345, //for 15 筆
		height: "230px", //for 10 筆
		//autoHeight: true,
		width: "100%",
		sortname : 'custName',
		postData : {
			formAction : "queryL120s01aById",
			custId : $("#custSearch").find("#searchId").val(),
			rowNum:10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		// autofit: false,
		autowidth:true,
		colModel: [{
			  colHeader: i18n.lmss07["L1205S07.grida"],//"借款人名稱"
			  name: 'custName',
			  width: 100,
			  sortable: true
		},{
		  name: 'oid',
		  hidden: true
		}],
			ondblClickRow: function(rowid){
		}
	});
}

function A_1_8_1() {
	var gridA181 = $("#gridview_A-1-8-1")
			.iGrid({
				needPager: false,
				handler : 'lms1205gridhandler',
				// height: 345, //for 15 筆
				height : "300px", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : {
					formAction : "queryL120s04a",
					mainId : responseJSON.mainid
					//rowNum:30
				},
				//rowNum:30,
				caption: "&nbsp;",
				hiddengrid : false,
				sortname : 'custRelation|profit|custId',
				sortorder:'asc|desc|asc',
				//sortname : 'custRelation',
				multiselect: true,
				//rownumbers : true,
				hideMultiselect:false,
				// autofit: false,
				autowidth : true,
				colModel : [ {
					colHeader : i18n.lmss07["L1205S07.grid15"],//"需列印"
					name : 'prtFlag',
					align : "center",
					width : 40,
					sortable : false
				}, {
					colHeader : i18n.lmss07["L1205S07.grid16"],//"關係戶統編"
					name : 'custId',
					width : 80,
					sortable : false,
					formatter : 'click',
					onclick : openDoc3
				}, {
					colHeader : i18n.lmss07["L1205S07.grid17"],//"關係戶戶名"
					width : 160,
					name : 'custName',
					sortable : false
				}, {
					colHeader : i18n.lmss07["L1205S07.grid18"],//"與借戶關係"
					name : 'custRelationIndex',
					width : 80,
					sortable : false,
					align : "center"
				}, {
					colHeader : i18n.lmss07["L1205S07.grid19"],//"貢獻度"
					name : 'profit',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					colHeader : i18n.lmss07["L1205S07.grid20"],//"放款額度"
					name : 'loanQuota',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					colHeader : i18n.lmss07["L1205S07.grid21"],//"放款餘額"
					name : 'loanAvgBal',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
					colHeader : i18n.lmss07["L1205S07.grid22"],//"活期存款"
					name : 'depTime',
					width : 80,
					sortable : false,
					align : "right",
					formatter : function(data) {
						if(data == null){
							return "";
						}else{
							// 加入撇節符號
							return util.addComma(data);	
						}
					}					
				}, {
		        	 colHeader: "&nbsp",//"檢核欄位",
		             name: 'chkYN',
		             width: 20,
		             sortable: false,
					 align:"center"
		         },	{
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridA181.getRowData(rowid);
					openDoc3(null, null, data);
				}
			});
}

/**
 * 往來彙總實績表
 */
function A_1_8_2() {
	var gridA182 = $("#gridview_A-1-8-2")
			.iGrid({
				handler : 'lms1205gridhandler',
				// height: 345, //for 15 筆
				height : "50", // for 10 筆
				// autoHeight: true,
				width : "100%",
				postData : {
					formAction : "queryL120s04b",
					mainId : responseJSON.mainid,
					rowNum:30
				},
				rowNum:30,
				caption: "&nbsp;",
				hiddengrid : false,
				//sortname : 'custRelation',
				//multiselect: true,
				//rownumbers : true,
				//hideMultiselect:false,
				// autofit: false,
				autowidth : true,
				colModel : [ {
					colHeader : i18n.lmss07["L1205S07.grid42"],//"報表名稱"
					width : 200,
					name : 'rptName',
					sortable : false
//					formatter : function(data) {
//						// L1205S07.grid43=借戶暨關係戶與本行往來實績彙總表
//						return i18n.lmss07["L1205S07.grid43"];
//					}								
				}, {
					colHeader : i18n.lmss07["L1205S07.grid38"],//"建立日期"
					width : 200,
					name : 'createTime',
					sortable : false,
					formatter : 'click',
					onclick : tL120s04b	
				}, {
					name : 'docKind',
					hidden : true								
				}, {
					name : 'oid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridA182.getRowData(rowid);
					tL120s04b(null, null, data);
				}
			});
}

var L120s04dGrid = $("#l120s04dGrid").iGrid({
    handler: 'lms1205gridhandler',
    height: 350,
    postData: {
        formAction: "queryL120s04dList"
    },
    rownumbers: true,
//    rowNum: 10,
    needPager: false,
    // multiselect : true,
    colModel: [{
        colHeader: i18n.lmss07["L120S01Q.custId"],
        align: "left",
        width: 100, // 設定寬度
        sortable: true, // 是否允許排序
        formatter: 'click',
        onclick: openS04dDoc,
        name: 'keyCustId'
    }, {
        colHeader: i18n.lmss07["L120S01Q.dupNo"],
        align: "left",
        width: 10, // 設定寬度
        name: 'keyDupNo'
    }, {
        colHeader: i18n.lmss07["L120S01Q.custName"],
        align: "left",
        width: 100, // 設定寬度
        name: 'custName'
    }, {
        colHeader: "oid",
        name: 'oid',
        hidden: true
    }],
    ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        var data = $("#l120s04dGrid").getRowData(rowid);
        openS04dDoc(null, null, data);
    }
}).trigger("reloadGrid");

function openS04dDoc(cellvalue, options, data){
    $.ajax({
        handler: responseJSON["handler"],
        action: "queryL120s04",
        data: {
            mainId: responseJSON.mainId,
            keyCustId: data.keyCustId,
            keyDupNo: data.keyDupNo,
            custName: data.custName,
            dataOid: data.oid
        },
        success: function(obj){
            $("#LMS1205S07Form03").setData(obj.LMS1205S07Form03, false);
            $("#gridview_A-1-8-1").jqGrid("setGridParam", {
                postData : {
                    mainId: responseJSON.mainId,
                    keyCustId: data.keyCustId,
                    keyDupNo: data.keyDupNo,
                    needCustId: true
                },
                search: true
            }).trigger("reloadGrid");
            $("#gridview_A-1-8-2").jqGrid("setGridParam", {
                postData : {
                    mainId: responseJSON.mainId,
                    keyCustId: data.keyCustId,
                    keyDupNo: data.keyDupNo,
                    needCustId: true
                },
                search: true
            }).trigger("reloadGrid");
            $("#gridviewPare").jqGrid("setGridParam", {
                postData : {
                    fieldId:"LMSNoList",
                    mainId:data.oid
                },
                search: true
            }).trigger("reloadGrid");

            // 原寫在 LMSS07Page.js
            form03ShowHide();
        }
    }).done(function(){
        $("#form03detailDiv").thickbox({
            title: "",
            width: 900,
            height: 500,
            modal : true,
            buttons: {
                "close": function() {
                    $.thickbox.close();
                }
            }
        });
    });
}

function form03ShowHide(){
    if (responseJSON.typCd == "1" || responseJSON.typCd == "4") {
		$("#LMS1205S07Form03 #seaHide").show();
		//J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
		$("#LMS1205S07Form03 #seaHide2").show();
	} else {
		$("#LMS1205S07Form03 #seaHide").hide();
		//J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
		$("#LMS1205S07Form03 #seaHide2").hide();
	}
}

function pullinL120S04D(){
    // LMSS07Page.js inputSearch();
    var nowDate = new Date();
    var MM = nowDate.getMonth();
    var YY = nowDate.getFullYear();
    var SMM;
    var SYY;
    if (MM == 0) {
        MM = 12;
    }

    if (MM == 12) {
        SMM = MM - 5;
        YY = YY - 1;
        SYY = YY;
    } else if (MM > 5 && MM < 12) {
        SMM = MM - 5;
        SYY = YY;
    } else {
        SMM = MM + 12 - 5;
        SYY = YY - 1;
    }

    var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
    $tLMS1205S07Form03b.find("#queryDateS0").val(SYY);
    $tLMS1205S07Form03b.find("#queryDateS1").val(SMM);
    $tLMS1205S07Form03b.find("#queryDateE0").val(YY);
    $tLMS1205S07Form03b.find("#queryDateE1").val(MM);
    $("#inputSearch").thickbox({ // 使用選取的內容進行彈窗
        title : i18n.lmss07["L1205S07.thickbox8"],
        width : 450,
        height : 210,
        modal : true,
        align : 'center',
        valign : 'bottom',
        i18n : i18n.lmss07,
        buttons : {
            "L1205S07.thickbox1" : function() {
                var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
                var $LMS1205S07Form03 = $("#LMS1205S07Form03");
                if ($tLMS1205S07Form03b.valid()) {
                    if ($tLMS1205S07Form03b.find("#queryDateS1").val() < 1
                            || $tLMS1205S07Form03b.find("#queryDateS1").val() > 12
                            || $tLMS1205S07Form03b.find("#queryDateE1").val() < 1
                            || $tLMS1205S07Form03b.find("#queryDateE1").val() > 12) {
                        CommonAPI.showMessage(i18n.lmss07["l120v01.error3"]);
                        return;
                    } else if ($tLMS1205S07Form03b.find("#queryDateS0").val() <= 0
                            || $tLMS1205S07Form03b.find("#queryDateE0").val() <= 0) {
                        CommonAPI.showMessage(i18n.lmss07["l120v01.error8"]);
                        return;
                    } else if ($tLMS1205S07Form03b.find("#queryDateE0").val()
                            - $tLMS1205S07Form03b.find("#queryDateS0").val() < 0) {
                        CommonAPI.showMessage(i18n.lmss07["l120v01.error9"]);
                        return;
                    } else if (($tLMS1205S07Form03b.find("#queryDateE0").val()
                                - $tLMS1205S07Form03b.find("#queryDateS0").val() == 0)
                            && ($tLMS1205S07Form03b.find("#queryDateE1").val()
                                - $tLMS1205S07Form03b.find("#queryDateS1").val() < 0)) {
                        CommonAPI.showMessage(i18n.lmss07["l120v01.error9"]);
                        return;
                    } else {
                        $.thickbox.close();
                        // 先塞L120S04D
                        $.ajax({
                            handler: responseJSON["handler"],
                            action: "chkHasL120s04ds",
                            data: {
                                mainId: responseJSON.mainId,
                                LMS1205S07Form03 : JSON.stringify($LMS1205S07Form03.serializeData()),
                                queryDateS0 : $tLMS1205S07Form03b.find("#queryDateS0").val(),
                                queryDateS1 : $tLMS1205S07Form03b.find("#queryDateS1").val(),
                                queryDateE0 : $tLMS1205S07Form03b.find("#queryDateE0").val(),
                                queryDateE1 : $tLMS1205S07Form03b.find("#queryDateE1").val()
                            },
                            success: function(obj){
                                if(obj.checkDateMsg){
                                    return CommonAPI.showErrorMessage(obj.checkDateMsg);
                                }
                                if(obj.hasData == "Y"){
                                    //confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
                                    CommonAPI.confirmMessage(i18n.def["confirmBeforeDeleteAll"], function(b){
                                        if (b) {
                                            $.ajax({
                                                handler: responseJSON["handler"],
                                                type: "POST",
                                                action : "deleteL120s04ds",
                                                dataType: "json",
                                                data:{
                                                    mainId: responseJSON.mainId
                                                },
                                                success : function(json) {
                                                    L120s04dGrid.trigger("reloadGrid");
                                                    importL120S04D().done(function(oidList){
                                                        // step1. 引進各關係戶往來彙總
                                                        inputSearchByOids(oidList).done(function(resultData){
                                                            // step2. 計算集團/關係企業合計
                                                            setTotalByOids(resultData).done(function(resultData){
                                                                // step3. 產生借戶暨關係戶與本行往來實績彙總表
                                                                // 3-1 先刪除
                                                                createReportByOids_del(resultData).done(function(resultData){
                                                                    // 3-2 再產生
                                                                    createReportByOids_import(resultData).done(function(resultData){
                                                                        L120s04dGrid.trigger("reloadGrid");
                                                                        CommonAPI.showMessage(i18n.def["runSuccess"]);
                                                                        $.thickbox.close();
                                                                        $.thickbox.close();
                                                                    });
                                                                });
                                                            });
                                                        });
                                                    });
                                                    API.showPopMessage(i18n.lmss07['needWait']);
                                                }
                                            });
                                        }
                                    });
                                } else {
                                    importL120S04D().done(function(oidList){
                                        // step1. 引進各關係戶往來彙總
                                        inputSearchByOids(oidList).done(function(resultData){
                                            // step2. 計算集團/關係企業合計
                                            setTotalByOids(resultData).done(function(resultData){
                                                // step3. 產生借戶暨關係戶與本行往來實績彙總表
                                                // 3-1 先刪除
                                                createReportByOids_del(resultData).done(function(resultData){
                                                    // 3-2 再產生
                                                    createReportByOids_import(resultData).done(function(resultData){
                                                        L120s04dGrid.trigger("reloadGrid");
                                                        CommonAPI.showMessage(i18n.def["runSuccess"]);
                                                        $.thickbox.close();
                                                        $.thickbox.close();
                                                    });
                                                });
                                            });
                                        });
                                    });
                                    API.showPopMessage(i18n.lmss07['needWait']);
                                }
                            }
                        });
                    }
                }
            },
            "L1205S07.thickbox2" : function() {
                API.confirmMessage(i18n.def['flow.exit'], function(res) {
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function importL120S04D(){
    var my_dfd = $.Deferred();
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        action : "importL120s04d",
        dataType: "json",
        data:{
            mainId: responseJSON.mainId
        },
        success : function(json) {
//            L120s04dGrid.trigger("reloadGrid");
            my_dfd.resolve( json.oidL120s04dList );
        }
    });
    return my_dfd.promise();
}

function inputSearchByOids(oidList){
    var resultJSON = {'oidList':oidList};
    var my_dfd = $.Deferred();
    var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
    var $LMS1205S07Form03 = $("#LMS1205S07Form03");
    var oidArr = oidList.split("|");
    var successCnt = 0;
    var s04aArr = [];
    for (var i in oidArr) {
        var oidL120s04d = oidArr[i];
        $.ajax({
            handler : responseJSON["handler"],
            type : "POST",
            dataType : "json",
            data : {
                formAction : "saveL120s04a2",
                LMS1205S07Form03 : JSON.stringify($LMS1205S07Form03.serializeData()),
                mainId : responseJSON.mainid,
                queryDateS0 : $tLMS1205S07Form03b.find("#queryDateS0").val(),
                queryDateS1 : $tLMS1205S07Form03b.find("#queryDateS1").val(),
                queryDateE0 : $tLMS1205S07Form03b.find("#queryDateE0").val(),
                queryDateE1 : $tLMS1205S07Form03b.find("#queryDateE1").val(),
                oidL120s04d : oidL120s04d,
                alreadyCheckDate : true,
                showMsg : false
            },
            success : function(json) {
                var queryDateS = json.LMS1205S07Form03.queryDateS;
                var queryDateE = json.LMS1205S07Form03.queryDateE;
                var s04dOid = json.oidL120s04d;
                var s04aSize = json.s04aSize;
                s04aArr.push(s04dOid + "^" + s04aSize + "^" + queryDateS + "^" + queryDateE);
            }
        }).done(function(){
            successCnt++;
            if(oidArr.length == successCnt){
                my_dfd.resolve( $.extend(resultJSON, {'s04aArr':s04aArr.join("|")}) );
            }
        });
    }
    return my_dfd.promise();
}

function setTotalByOids(resultData){
    var my_dfd = $.Deferred();
    var oidArr = resultData.oidList.split("|");
    var s04aArr = resultData.s04aArr.split("|");
    var successCnt = 0;
    for (var i in oidArr) {
        var oidL120s04d = oidArr[i];
        for (var j in s04aArr) {
            var s04aStr = s04aArr[j];
            var s04aStrArr = s04aStr.split("^");
            var s04dOid = s04aStrArr[0];
            var s04aSize = s04aStrArr[1];
            var queryDateS = s04aStrArr[2];
            var queryDateE = s04aStrArr[3];
            if(s04dOid == oidL120s04d){
                if(s04aSize > 0){
                    $.ajax({
                        handler : responseJSON["handler"],
                        type : "POST",
                        dataType : "json",
                        action : "saveTotal",
                        data : {
                            mainId : responseJSON.mainid,
                            queryDateS : queryDateS,
                            queryDateE : queryDateE,
                            oidL120s04d: s04dOid,
                            showMsg : false
                        },
                        success : function(json) {

                        }
                    }).done(function(){
                        successCnt++;
                        if(oidArr.length == successCnt){
                            my_dfd.resolve( $.extend(resultData) );
                        }
                    });
                } else {    // 沒資料還是要+1  不然永遠不會等於總筆數
                    successCnt++;
                }
            }
        }
    }
    return my_dfd.promise();
}

function createReportByOids_del(resultData){
    var my_dfd = $.Deferred();
    var oidArr = resultData.oidList.split("|");
    var s04aArr = resultData.s04aArr.split("|");
    var successCnt = 0;
    for (var i in oidArr) {
        var oidL120s04d = oidArr[i];
        for (var j in s04aArr) {
            var s04aStr = s04aArr[j];
            var s04aStrArr = s04aStr.split("^");
            var s04dOid = s04aStrArr[0];
            var s04aSize = s04aStrArr[1];
            var queryDateS = s04aStrArr[2];
            var queryDateE = s04aStrArr[3];
            if(s04dOid == oidL120s04d){
                if(s04aSize > 0){
                    $.ajax({
                        handler : responseJSON["handler"],
                        type : "POST",
                        dataType : "json",
                        data : {
                            formAction : "deleteL120s04b",
                            mainId : responseJSON.mainid,
                            oidL120s04d: s04dOid,
                            showMsg : false
                        },
                        success : function(json) {

                        }
                    }).done(function(){
                        successCnt++;
                        if(oidArr.length == successCnt){
                            my_dfd.resolve( $.extend(resultData) );
                        }
                    });
                } else {    // 沒資料還是要+1  不然永遠不會等於總筆數
                    successCnt++;
                }
            }
        }
    }
    return my_dfd.promise();
}

function createReportByOids_import(resultData){
    var my_dfd = $.Deferred();
    var oidArr = resultData.oidList.split("|");
    var s04aArr = resultData.s04aArr.split("|");
    var successCnt = 0;
    for (var i in oidArr) {
        var oidL120s04d = oidArr[i];
        for (var j in s04aArr) {
            var s04aStr = s04aArr[j];
            var s04aStrArr = s04aStr.split("^");
            var s04dOid = s04aStrArr[0];
            var s04aSize = s04aStrArr[1];
            var queryDateS = s04aStrArr[2];
            var queryDateE = s04aStrArr[3];
            if(s04dOid == oidL120s04d){
                if(s04aSize > 0){
                    // 開始產生實績彙總表
                    // LMSS07Page.js importReport();
                    $.ajax({
                        handler : responseJSON["handler"],
                        type : "POST",
                        dataType : "json",
                        data : {
                            formAction : "importL120s04b",
                            mainId : responseJSON.mainid,
                            queryDateS : queryDateS,
                            queryDateE : queryDateE,
                            oidL120s04d: s04dOid,
                            showMsg : false
                        },
                        success : function() {
                        }
                    }).done(function(){
                        successCnt++;
                        if(oidArr.length == successCnt){
                            my_dfd.resolve( $.extend(resultData) );
                        }
                    });
                } else {    // 沒資料還是要+1  不然永遠不會等於總筆數
                    successCnt++;
                }
            }
        }
    }
    return my_dfd.promise();
}