package com.mega.eloan.lms.dao.impl;

import com.mega.eloan.lms.dao.C120S01WDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01W;
import java.util.List;

import org.springframework.stereotype.Repository;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

/** 收入明細表 **/
@Repository
public class C120S01WDaoImpl extends LMSJpaDao<C120S01W, String>
	implements C120S01WDao {

	@Override
	public C120S01W findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01W> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01W> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C120S01W> findByList(String mainId, String custId, String dupNo) {

		ISearch search = createSearchTemplete();
		if (mainId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}
		if (custId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		}
		if (dupNo != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01W> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<C120S01W> findByList(String mainId, String custId, String dupNo, String[] incomeItemArray) {

		ISearch search = createSearchTemplete();
		if (mainId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}
		if (custId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		}
		if (dupNo != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		
		search.addSearchModeParameters(SearchMode.IN, "incomeItem", incomeItemArray);
		search.setMaxResults(Integer.MAX_VALUE);
		List<C120S01W> list = createQuery(search).getResultList();
		return list;
	}

}