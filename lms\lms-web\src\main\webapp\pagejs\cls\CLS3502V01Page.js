$(function(){

    var grid = $("#gridview").iGrid({
        handler: 'cls3502gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "",
        localFirst: true,
        postData: {
            
        },
        rowNum: 15,
        sortname: "",
        sortorder: "",
        multiselect: false, 
    	colModel : [
    	{
			colHeader : "", 
			width : 110, //設定寬度
			sortable : true, //是否允許排序
			name : '' 							
		}]				
	});
   
    /**
     * 回傳 yyyy-MM-dd
     * @param {Object} n_month
     */
    function getBefore_N_MonthDate(n_month){
        var sysdate = CommonAPI.getToday().split("-");
        var tDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
        tDate.setMonth(tDate.getMonth() - n_month);
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
    }
    
    function openDoc(cellvalue, options, rowObject){
    	$.form.submit({
			url : '../lms/cls1220m03/01',
			data : {
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: rowObject.docStatus
            },
            target: rowObject.oid
		});
    };
	
    $("#buttonPanel").find('#btnView').click(function(){
		var selrow = grid.getGridParam('selrow');
        if (selrow) {
            openDoc('', '', grid.getRowData(selrow));
        }
        else {
            CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
        }	
    }).end().find("#btnProduceExcel").click(function(){    	
    	var _id = "_div_cls3502v01_b";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' width='100%' >");
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls3502v01['C125M01A.queryKind']+"</td>");
			dyna.push("	  <td> <select name='queryKind' id='queryKind'><option value='1'><wicket:message key='C125M01A.queryKind_1'>申請書</wicket:message></option><option value='2'><wicket:message key='C125M01A.queryKind_2'>通知單</wicket:message></option></select></td>");
			dyna.push("	</tr>");
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls3502v01['C125M01A.custId']+"</td>");
			dyna.push("	  <td><input type='text' id='custId' name='custId' maxlength='10' /></td>");
			dyna.push("	</tr>");
			dyna.push("	<tr>");			
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls3502v01['C125M01A.batchDate']+"</td>");
			dyna.push("	  <td>");
			dyna.push("	   <input type='text' id='applyTS_beg' name='applyTS_beg' maxlength='10' class='date' />");
			dyna.push("	 ~ <input type='text' id='applyTS_end' name='applyTS_end' maxlength='10' class='date' />");
			dyna.push("	  </td>");
			dyna.push("	</tr>");
			
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));		
		    
		    $("#"+_form).find(".date").filter(function(){
		        return !$(this).attr('readonly');
		    }).datepicker();
		    
		    if(true){
		    	//在 function pageInit(...) 中，會針對 欄位 custId addClass upText
		    	pageInit.call( $("#"+_id) );
		    }
		}
		//clear data
		$("#"+_form).reset();
		$("#"+_form).find("[name=applyTS_beg]").val(CommonAPI.getToday());
		$("#"+_form).find("[name=applyTS_end]").val(CommonAPI.getToday());
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: i18n.def.query,
	       width: 450,
           height: 210,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	   
            	   $.form.submit({
                       url: "../simple/FileProcessingService",
                       target: "_blank",
                       data: { 
                    	   queryKind:$("#"+_form).find("[name=queryKind]").val(),
                           custId:$("#"+_form).find("[name=custId]").val(),
                           applyTS_beg:$("#"+_form).find("[name=applyTS_beg]").val(),
                           applyTS_end:$("#"+_form).find("[name=applyTS_end]").val(),
                           fileDownloadName: "CLS3502R01.xls",
                           serviceName: "cls3052xlsservice"
                       }
                   });
            	   
            	   $.thickbox.close();
            	   
               },
               "cancel": function(){
            	   $.thickbox.close();            	  
               }
           }
		});	
	});
    
    $("#filter_historyBtn").click(function(){	
		
		var grid_id= "grid_applyKindB_History";
		$("#"+grid_id).jqGrid("setGridParam", {
			postData : get_param_grid_history('Y'),
			search : true
		}).trigger("reloadGrid");			
	
	});
    
    function get_param_grid_history(flag){
    	var _form = "div_applyKindB_History_form";
    	return {
			'formAction' : "query_applyKindB_History",
			'custId': $("#"+_form).find("[name=search_custId]").val(),
			'flag':flag
		};
    }
    
    var getLastDateOfTheMonth = function(){
        var tDate = new Date();
        tDate.setMonth(tDate.getMonth() + 1);
        tDate.setDate(1);
        tDate.setDate(tDate.getDate() - 1);
        return tDate.getFullYear() + "-" +
        (tDate.getMonth() < 9 ? "0" : "") +
        (tDate.getMonth() + 1) +
        "-" +
        (tDate.getDate() < 10 ? "0" : "") +
        tDate.getDate();
    }
});

