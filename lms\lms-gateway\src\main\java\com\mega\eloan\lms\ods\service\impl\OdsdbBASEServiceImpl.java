package com.mega.eloan.lms.ods.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.jdbc.EloanColumnMapRowMapper;
import com.mega.eloan.lms.ods.service.OdsdbBASEService;

@Service("odsdbBASEService")
public class OdsdbBASEServiceImpl extends AbstractODSJdbc implements
        OdsdbBASEService {

    public Map<String, Object> getODS_Status() {
        return this.getJdbc().queryForMap("getODS_Status", new String[] {});
    }

    public List<Map<String, Object>> findODS_0320_ById(String custId, String dupNo) {
        return this.getJdbc().queryForListWithMax("findODS_0320_ById",
                new Object[] { custId, dupNo, custId, dupNo, custId, dupNo,
                        custId, dupNo, custId, dupNo, custId, dupNo });
    }

    public Page<Map<String, Object>> findODS_0320_ById_forPage(ISearch search, String custId, String dupNo) {
        return this.getJdbc().queryForPage(search, "findODS_0320_ById",
                new Object[] { custId, dupNo, custId, dupNo, custId, dupNo,
                        custId, dupNo, custId, dupNo, custId, dupNo });
    }

    public List<Map<String, Object>> findODS_0060_TXN(String realActNo) {
        return this.getJdbc().queryForListWithMax("findODS_0060_TXN",
                new Object[] { realActNo });
    }

    public List<Map<String, Object>> findODS_0060_HIST(String realActNo, String currCode, String bgnDate, String endDate) {
        return this.getJdbc().queryForListWithMax("findODS_0060_HIST",
                new Object[] { realActNo, currCode, bgnDate, endDate });
    }

    public List<Map<String, Object>> findODS_8250(String brNo, String ioFlag, String func, String remitType, String bgnDate, String endDate,
            String custId, String dupNo, String begAmt, String endAmt, String bankId, String ractNo) {
        StringBuffer sqlSb = new StringBuffer();

        List<Object> params = new ArrayList<Object>();
        params.add(bgnDate);
        params.add(endDate);
        params.add(custId);
        params.add(dupNo);

        if (Util.isNotEmpty(brNo)){
            sqlSb.append(" AND LEFT(AP85_RMNO,3) = ? ");
            params.add(brNo);
        }

        if(Util.notEquals(remitType, "0")){
            sqlSb.append(" AND TYPE = ? ");
            params.add(remitType);
        }

		if (Util.isNotEmpty(begAmt) && Util.isNotEmpty(endAmt)) {
            sqlSb.append(" AND AP85_AMOUNT BETWEEN ? AND ? ");
            params.add(begAmt);
            params.add(endAmt);
		} else if (Util.isNotEmpty(begAmt)) {
            sqlSb.append(" AND AP85_AMOUNT >= ? ");
            params.add(begAmt);
        } else if (Util.isNotEmpty(endAmt)) {
            sqlSb.append(" AND AP85_AMOUNT <= ? ");
            params.add(endAmt);
        }

        if (Util.isNotEmpty(bankId)) {
            String bankId3 = "";
            String bankId4 = "";
            if(bankId.length() >= 7) {
                bankId3 = bankId.substring(0, 3);
                bankId4 = bankId.substring(3, 7);
            } else if(bankId.length() < 4) {
                bankId3 = bankId;
            } else {
                bankId3 = bankId.substring(0, 3);
                bankId4 = bankId.substring(3);
            }

            if(Util.equals(bankId4, "XXXX")){
//                sqlSb.append(" AND AP85_BANKID LIKE '").append(bankId3).append("%'");
                sqlSb.append(" AND LEFT(AP85_BANKID,3) = ? ");
                params.add(bankId3);
            } else {
                sqlSb.append(" AND AP85_BANKID = ? ");
                params.add(bankId);
            }
        }

        if (Util.isNotEmpty(ractNo)) {
            sqlSb.append(" AND AP85_ACTNO = ? ");
            params.add(ractNo);
        }

        String sql = "findODS_8250_I";
        if(Util.equals(ioFlag, "1")){   // 匯入
            sql = "findODS_8250_I";
        } else if(Util.equals(ioFlag, "2")){    // 匯出
            sql = "findODS_8250_O";
        }

		return this.getJdbc().queryForListByCustParam(sql, new Object[] { sqlSb.toString() },
                params.toArray(new Object[0]), 0,
				Integer.MAX_VALUE, new EloanColumnMapRowMapper());
    }

    public List<Map<String, Object>> findODS_8410_ByAccNo(String loanNo, String brNo) {
        return this.getJdbc().queryForListWithMax("findODS_8410_ByAccNo",
                new Object[] { loanNo, brNo });
    }

    public List<Map<String, Object>> findODS_CMSTKTBL() {
        return this.getJdbc().queryForListWithMax("findODS_CMSTKTBL",
                new Object[] {  });
    }

    public Map<String, Object> findODS_CMSTKTBL_SINGLE(String CMSTK_CODE) {
        return this.getJdbc().queryForMap("findODS_CMSTKTBL_ByCODE",
                new Object[] { CMSTK_CODE });
    }

    public List<Map<String, Object>> findODS_CMMEMTBN() {
        return this.getJdbc().queryForListWithMax("findODS_CMMEMTBN",
                new Object[] {  });
    }
    
    @Override
    public Map<String, Object> findODS_CMFWARNP(String idNo,String date) {
        return this.getJdbc().queryForMap("findODS_CMFWARNP_ById",
                new Object[] { date, idNo });
    }

    @Override
    public Map<String, Object> findODS_CMFAUDAC_ByAcc(String brNo,String acc) {
        return this.getJdbc().queryForMap("findODS_CMFAUDAC_ByAcc",
                new Object[] { brNo, acc });
    }
}