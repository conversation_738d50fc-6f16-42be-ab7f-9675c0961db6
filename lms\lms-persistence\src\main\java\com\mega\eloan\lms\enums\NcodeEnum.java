/* 
 * NcodeEnum.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * 金控法利害關係人查詢比對項目
 * </pre>
 * 
 * @since 2019/11/7
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2019/11/7,010173,new
 *          </ul>
 */
public enum NcodeEnum {
	// 比對項目:本國自然人比對(0)、外國自然人比對(1)、法人比對(2)
	本國自然人0("0"), 外國自然人1("1"), 法人2("2");

	private String code;

	NcodeEnum(String code) {
		this.code = code;
	}

	public String toString() {
		return this.code;
	}

	/**
	 * 判斷是否為比對項目
	 * 
	 * @return boolean
	 */
	public boolean isNcode() {
		return code.matches("0|1|2");
	}

	public String getCode() {
		return code;
	}

	public boolean isEquals(Object other) {
		if (other instanceof String) {
			return code.equals(other);
		} else {
			return super.equals(other);
		}
	}

	public static boolean isNcode(String nCode) {
		return nCode != null ? nCode.matches("0|1|2") : false;
	}

	public static NcodeEnum getEnum(String code) {
		for (NcodeEnum enums : NcodeEnum.values()) {
			if (enums.getCode().equals(code)) {
				return enums;
			}
		}
		return null;
	}

}
