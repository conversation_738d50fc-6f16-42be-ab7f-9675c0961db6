package tw.com.jcs.auth.model.impl;

import tw.com.jcs.auth.model.CodeItem;

/**
 * <pre>
 * 權限物件
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public class CodeItemImpl implements CodeItem {

    /** 權限代碼 **/
    private int code;

    /** 排列順序 **/
    private int seq;

    /** 權限階層 **/
    private int step;

    /** 權限上層代碼 **/
    private int parent;

    /** 代碼名稱 **/
    private String name;

    /** URL位置 **/
    private String path;

    /** DESC **/
    private String desc;

    /** 文件ID **/
    private String docid;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public int getSeq() {
        return seq;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }

    @Override
    public int getStep() {
        return step;
    }

    /**
     * @return CodeItem
     */
    @Override
    public String toString() {
        return "CodeItemImpl [code=" + code + ", seq=" + seq + ", step=" + step + ", parent=" + parent + ", name=" + name + ", path=" + path + "]";
    }

    public void setStep(int step) {
        this.step = step;
    }

    @Override
    public int getParent() {
        return parent;
    }

    public void setParent(int parent) {
        this.parent = parent;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + code;
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        result = prime * result + parent;
        result = prime * result + seq;
        result = prime * result + step;
        // result = prime * result + ((path == null) ? 0 : path.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CodeItemImpl other = (CodeItemImpl) obj;
        if (code != other.code)
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        } else if (!name.equals(other.name))
            return false;
        if (parent != other.parent)
            return false;
        if (seq != other.seq)
            return false;
        if (step != other.step)
            return false;

        if (path == null) {
            if (other.path != null)
                return false;
        } else if (!path.equals(other.path))
            return false;

        return true;
    }

    @Override
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDocid() {
        return docid;
    }

    public void setDocid(String docid) {
        this.docid = docid;
    }
}
