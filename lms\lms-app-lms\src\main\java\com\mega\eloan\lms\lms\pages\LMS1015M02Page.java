
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.common.OverSeaUtil;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2015/7/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/7/29,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1015m02")
public class LMS1015M02Page extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		//title、內容共用
		boolean v1_0 = false; 
		boolean v2_0 = false;
		String varVer = params.getString("varVer");
		if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_JP)){
			v2_0 = true;
		}else{
			v1_0 = true;
		}
		model.addAttribute("FACTOR_TITLE_V1_0", v1_0);
		model.addAttribute("FACTOR_TITLE_V2_0", v2_0);
		
		renderJsI18N(LMS1015M02Page.class);
	}

	public Class<? extends Meta> getDomainClass() {
		return null;
	}

}
