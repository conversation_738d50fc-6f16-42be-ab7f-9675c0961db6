var _oversea = true;
$(function() {
	$.ajax({
		type: "POST", handler: "lms2405m01formhandler",
		action: "overSeaProgram", async: false,//用「同步」的方式
	    data: {}
	    }).done(function(json){
	    	if(json.overSeaProgram){
	    		_oversea = true;
	    	}else{
	    		_oversea = false;
	    	}
	});
	//==================
	    $("input[name=filterRadio]").click(function(){
        if ($(this).val() == "new") {
        	//最新資料,不須輸入「日期區間」
            $("#chooseDateTag").hide();
        } else {
            $("#chooseDateTag").show();
        }
    });

		$("#chooseDateTag").hide();
		if(viewstatus == "030"){
			$.ajax({
				type : "POST",
				handler : "lms1815formhandler",
				data : {
					formAction : "queryBranch"
				}
				}).done(function(obj){
					var chooseItem = $("#selectBank");
					var _addSpace = false;
					if(chooseItem.attr("space")=="true"){
						if(_oversea){
							
						}else{
							_addSpace = true;	
						}						
					}
					
					$.each(obj.itemOrder, function(idx, brNo) {
	            		var currobj = {};
	            		var brName = obj.item[brNo];
	            		currobj[brNo] = brName;
	            		
	            		//select
						$("#selectBank").setItems({ item: currobj, format: "{value} {key}", clear:false, space: (_addSpace?(idx==0):false) });
					});
			});
			$("#openbox5").thickbox({
			    title : i18n.lms1815v01['search'],
			    width : 450,
			    height : 250,
			    modal : false,
				align:'center',
				valign:'bottom',
				i18n : i18n.def,
			    buttons: {
				    "sure": function() {
				    	var choose = $("input[name='filterRadio']:checked").val();
				    	var tDate = new Date();
				    	if(choose == "new"){
				    		grid({
				    			branch: $("#selectBank").val(), 
				    			custId: $("#custId").val(), 
				    			beforeDate : tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-01", 
				    			afterDate : tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + 
				    					"-" + (tDate.getDate() < 9 ? "0" : "") +(tDate.getDate()) + " 23:59:59"
				    		});
					    	$.thickbox.close();
				    	}else if(choose == "old"){
				    		if($("#filterForm").valid()){
				    			if($("#beforeDate").val()=="" || $("#afterDate").val()==""){
				    				return;
				    			}else{
				    				if($("#beforeDate").val() > $("#afterDate").val()){
				    					return CommonAPI.showErrorMessage(i18n.lms1815v01["err.dateError"]);
				    				}
				    			}
				    			grid({branch: $("#selectBank").val(),custId: $("#custId").val(), beforeDate : $("#beforeDate").val(), afterDate : $("#afterDate").val()});
						    	$.thickbox.close();
				    		}
				    	}else{
				    		return;
				    	}
				    },
				    "cancel": function() {
				    	grid({branch: "", custId: "", beforeDate : "0001-01-01", afterDate : "0001-01-01"});
				    	$.thickbox.close();
				    }
				}
			});
		}else{
			grid();
		}
	});
function grid(sendData) {
	var show = false;
	var not_dbu_br = true;
	if(! _oversea){
		not_dbu_br = false;
	}
	if(viewstatus == "020"){
		show = true;
	}
	
	var grid = $("#gridview").iGrid({
		handler : _oversea?'lms1815gridhandler':'lms1810gridhandler',
		height : 350,
		sortname : 'elfBranch',
		multiselect : show,
		hideMultiselect : !show,
		postData : $.extend({
			docStatus : viewstatus,
			formAction:"query"
		},(sendData || {})),
        loadComplete: function(){
            //J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		    //確認新增覆審名單種類
			$.ajax({
	            type: "POST",
	            handler: "lms1800formhandler",
	            data: {	 'formAction': 'getCtlTypeByBrNo'
	            }
	            }).done(function(responseData){
					 var ctlType =responseData.ctlType;
					 if(ctlType !="Z"){
					 	//自辦+主辦都有
					 	$("#gridview").hideCol('ctlType');
						
					 }
	        });  
		   
		   //***************** 
        },
		colModel : [ {
			colHeader : i18n.lms1815v01['L180M02A.branchId'],
			name : 'elfBranch',
			align : "center",
			width : 75,
			sortable : true
		}, {
			colHeader : i18n.lms1815v01['L180M02A.custId'],
			name : 'custId',
			width : 90,
			sortable : true,
			formatter : 'click',
			onclick : openDoc
		}, {
			colHeader : i18n.lms1815v01['L180M02A.custName'],
			name : 'custName',
			width : 100,
			sortable : true
		}, {
			colHeader :  i18n.lms1815v01['label.ctlType'],//"覆審種類",
			name : 'ctlType',
			width : 70,
			align : "center",
			sortable : false		
		}, {
			colHeader : i18n.lms1815v01['label.period'],
			name : 'elfRCkdLine',
			width : 50,
			sortable : false,
			align : "center",
			hidden : not_dbu_br
		}, {
			colHeader : i18n.lms1815v01['label.lrDate'],
			name : 'elfLRDate',
			width : 90,
			sortable : false,
			hidden : not_dbu_br
		}, {
			colHeader : i18n.lms1815v01['label.ndDate'],
			name : 'ndDate',
			width : 90,
			sortable : false,
			hidden : not_dbu_br
		}, {
			colHeader : i18n.lms1815v01['label.nckdMemo'],
			name : 'elfNCkdMemo',
			width : 100,
			sortable : false,
			hidden : not_dbu_br
		}, {
			colHeader : i18n.lms1815v01['L180M02A.updater'],
			name : 'updater',
			width : 100,
			sortable : true,
			align : "center"
		}, {
			name : 'oid',
			hidden : true
		}, {
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid){
			openDoc(null, null, grid.getRowData(rowid));
		}
	});

	function openDoc(cellvalue, options, rowObject) {
		ilog.debug(rowObject);
		if(_oversea){
			$.form.submit({
				url : '../lrs/lms1815m01/01',
				data : {
					formAction : "queryMain",
					oid : rowObject.oid,
					mainOid : rowObject.oid,
					mainId : rowObject.mainId,
					mainDocStatus : viewstatus
				},
				target : rowObject.oid
			});	
		}else{
			$.form.submit({
				url : '../lrs/lms1810m01/01',
				data : {
					oid : rowObject.oid,
					mainOid : rowObject.oid,
					mainId : rowObject.mainId,
					mainDocStatus : viewstatus
				},
				target : rowObject.oid
			});	
		}		
	};
	
	$("#buttonPanel").find("#btnDelete").click(function() {
		var row = $("#gridview").getGridParam('selrow');
		var list = "";
		if(row){
			var data = $("#gridview").getRowData(row);
			if(data){
				list = data.oid;	
			}				
		}		
		if (list == "") {
			CommonAPI.showMessage(i18n.lms1815v01["noInfo"]);
			return;
		}
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){
				$.ajax({
					handler : "lms1815formhandler",
					type : "POST",
					dataType : "json",
					data :  {
						formAction : "deleteMark",
						list : list
					}
					}).done(function(obj) {
						$("#gridview").trigger("reloadGrid");
				});
			}
		})
	}).end().find("#btnAdd").click(function() {
		if(_oversea){
			$.ajax({
	            type: "POST",
	            handler: "lms1815formhandler",
	            data: {
	                formAction: "newMain"
	            }
	            }).done(function(responseData){
	        		$.form.submit({
	        			url : '../lrs/lms1815m01/01',
	        			data: {
	        				oid : responseData.mainOid,
	        				mainOid : responseData.mainOid,
	        				mainId : responseData.mainId,
	        				mainDocStatus : responseData.mainDocStatus
	        			},
	        			target : responseData.mainOid
	        		});
	        });	
		}else{
			$.ajax({
	            type: "POST",
	            handler: "lms1810m01formhandler",
	            data: {
	                formAction: "newMain"
	            }
	            }).done(function(responseData){
	            	$("#gridview").trigger("reloadGrid");
	        		$.form.submit({
	        			url : '../lrs/lms1810m01/01',
	        			data: {
	        				oid : responseData.mainOid,
	        				mainOid : responseData.mainOid,
	        				mainId : responseData.mainId,
	        				mainDocStatus : responseData.mainDocStatus
	        			},
	        			target : responseData.mainOid
	        		});
	        });	
		}
		
	}).end().find("#btnFilter").click(function() {
		$("#chooseDateTag").hide();
		$("#filterForm").reset();
		$("#selectBank").val(userInfo.unitNo);
		thickOpenBox5();
	}).end().find("#btnAllSend").click(function() {
		$("input[name=send]").prop('checked',false);
		var rows = $("#gridview").getGridParam('selarrrow');
    	var list = new Array();
    	for (var i=0;i<rows.length;i++){
    		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0){
    			var data = $("#gridview").getRowData(rows[i]);
    			list[i] = data.oid;
    		}
    	}
    	if (list == "") {CommonAPI.showMessage(i18n.def["TMMDeleteError"]); return;}
		$("#sendBox").thickbox({
			title : i18n.lms1815v01["button.accept"],
			width : 150,
			height : 150,
			modal : false,
			align : "center",
			valign : "bottom",
			i18n:i18n.def,
			buttons : {
				"sure" : function() {
					if(_oversea){
						
						var flowAction;
						if($("input[name='send']:checked").val() == 1){
							flowAction = true;	
						}else if($("input[name='send']:checked").val() == 2){
							flowAction = false;
						}else{
							return;
						}
						
						$.thickbox.close();
						
						$.ajax({
							handler : "lms1815formhandler",
							type : "POST",
							dataType : "json",
							data :  {
								formAction : "flowCases",
								flowAction : flowAction,
								list : list
							}
							}).done(function(obj) {
								$("#gridview").trigger("reloadGrid");
								$.thickbox.close();
						});					
					}else{
						
						var decisionExpr = "";
						if($("input[name='send']:checked").val() == 1){
							decisionExpr = "核定";
						}else if($("input[name='send']:checked").val() == 2){
							decisionExpr = "退回";
						}else{
							return;
						}
						
						$.thickbox.close();
						
						$.each(list, function(idx, mainOid){
							$.ajax({
					            type: "POST",
					            handler: 'lms1810m01formhandler', action: "flowAction",
					            data:{
					            	'mainOid': mainOid, 
					            	'mainDocStatus': viewstatus,
					            	'decisionExpr': decisionExpr
					            }                
					            }).done(function(json){
					            	$.thickbox.close();
					            	$("#gridview").trigger("reloadGrid");
					        });
		            	});
					}
				},
				"cancel" : function() {
					$.thickbox.close();
				}
			}
		});
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        //BOM(null, null, result);
        openDoc(null, null, result);
    
    });
	
	function thickOpenBox5(openResult){
		$("#openbox5").thickbox({
		    title : i18n.lms1815v01['search'],
		    width : 450,
		    height : 250,
		    modal : false,
			align:'center',
			valign:'bottom',
			i18n : i18n.def,
		    buttons: {
			    "sure": function() {
			    	var choose = $("input[name='filterRadio']:checked").val();
			    	var tDate = new Date();
			    	var beforeDate = "";
			    	var afterDate = "";
			    	if(choose == "new"){

			    		beforeDate = tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-01";
			    		afterDate = tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + 
    														"-" + (tDate.getDate() < 9 ? "0" : "") +(tDate.getDate()) + " 23:59:59";
			    	}else if(choose == "old"){
			    		beforeDate = $("#beforeDate").val();
			    		afterDate = $("#afterDate").val();
			    		if(beforeDate=="" || afterDate==""){
		    				return;
		    			}else{
		    				if(beforeDate > afterDate){
		    					return CommonAPI.showErrorMessage(i18n.lms1815v01["err.dateError"]);
		    				}
		    			}
			    	}else{
			    		return;
			    	}
			    	$("#gridview").jqGrid("setGridParam", {
			    		postData : {
			    			docStatus : viewstatus,
			    			formAction : "query",
			    			branch: $("#selectBank").val(), 
			    			custId: $("#custId").val(), 
			    			beforeDate : beforeDate, 
			    			afterDate : afterDate
			    		},
						page : 1,
						//gridPage : 1,
			    		search: true
			    	}).trigger("reloadGrid");
			    	$.thickbox.close();
			    },
			    "cancel": function() {
			    	$.thickbox.close();
		        }
			}
		});
	}
	

}
