/* 
 *  LMS9015ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.lms.dao.L901M01ADao;
import com.mega.eloan.lms.fms.service.LMS9015Service;
import com.mega.eloan.lms.model.L901M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;

@Service
public class LMS9015ServiceImpl extends AbstractCapService implements
		LMS9015Service {

	

	@Resource
	DocLogService docLogService;

	@Resource
	L901M01ADao l901m01aDao;

	@Override
	public L901M01A findByOid(String oid) {
		return l901m01aDao.find(oid);
	}

	@Override
	public void save(L901M01A model) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		model.setUpdater(user.getUserId());
		model.setUpdateTime(CapDate.getCurrentTimestamp());
		l901m01aDao.save(model);
	}

	@Override
	public void delete(L901M01A model) {

		l901m01aDao.delete(model);

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L901M01A.class) {
			return l901m01aDao.findPage(search);
		}
		return null;
	}

	@Override
	public int findL9010m01fByItemSeqMax(String itemType,String branchId) {
		List<L901M01A> l901m01as = l901m01aDao.findByItemTypeAndbranchId(itemType, branchId);
		int max = 0;
		int seqval = 0;
		for(L901M01A l901m01a:l901m01as){
			seqval = l901m01a.getItemSeq();
			if (seqval > max) {
				max = seqval;
			}
		}
		return ++max;
	}

}
