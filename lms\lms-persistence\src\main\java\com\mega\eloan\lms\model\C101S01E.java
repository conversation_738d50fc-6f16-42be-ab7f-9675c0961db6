/* 
 * C101S01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.Lob;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.Check3;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 個金相關查詢資料檔 **/
@NamedEntityGraph(name = "C101S01E-entity-graph", attributeNodes = { @NamedAttributeNode("c101m01a") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01E", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C101S01E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 資料來源 **/
	@Size(max = 60)
	@Column(name = "DATASRC", length = 60, columnDefinition = "VARCHAR(60)")
	private String dataSrc;

	/**
	 * 有無票信退補記錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ECHKFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String eChkFlag;

	/** 票信資料截止日 **/
	@NotEmpty(message = "{required.message}", groups = { Check2.class })
	@NotNull(message = "{required.message}", groups = { Check2.class })
	@Temporal(TemporalType.DATE)
	@Column(name = "ECHKDDATE", columnDefinition = "DATE")
	private Date eChkDDate;

	/** 票信查詢日期 **/
	@NotEmpty(message = "{required.message}", groups = { Check2.class })
	@NotNull(message = "{required.message}", groups = { Check2.class })
	@Temporal(TemporalType.DATE)
	@Column(name = "ECHKQDATE", columnDefinition = "DATE")
	private Date eChkQDate;

	/**
	 * 有無聯徵逾催呆記錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "EJCICFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String eJcicFlag;

	/** 聯徵資料日期 **/
	@NotEmpty(message = "{required.message}", groups = { Check2.class })
	@NotNull(message = "{required.message}", groups = { Check2.class })
	@Temporal(TemporalType.DATE)
	@Column(name = "EJCICDDATE", columnDefinition = "DATE")
	private Date eJcicDDate;

	/** 聯徵查詢日期 **/
	@NotEmpty(message = "{required.message}", groups = { Check2.class })
	@NotNull(message = "{required.message}", groups = { Check2.class })
	@Temporal(TemporalType.DATE)
	@Column(name = "EJCICQDATE", columnDefinition = "DATE")
	private Date eJcicQDate;

	/**
	 * 引進原舊案資料
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@Size(max = 1)
	@Column(name = "ISFROMOLD", length = 1, columnDefinition = "CHAR(1)")
	private String isFromOld;

	/**
	 * 婉卻紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA1", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata1;

	/**
	 * 本行利害關係人
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA2", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata2;

	/**
	 * 金控利害關係人(44條)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA3", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata3;

	/**
	 * 金控利害關係人(45條)
	 * <p/>
	 * 100/12/08新增<br/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA16", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata16;

	/**
	 * 主從債務人(不含本次資料)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA6", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata6;

	/**
	 * 對同一自然人授信總餘額比率
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA5", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata5;

	/**
	 * 歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA4", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata4;

	/**
	 * 近一年內不含查詢當日非Z類被聯行查詢紀錄明細
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA14", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata14;

	/**
	 * 黑名單
	 * <p/>
	 * 100/12/08新增<br/>
	 * 1.有、2.無、3.N.A => c101s01j.ans1<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA7", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata7;

	/**
	 * 黑名單全型字英文名
	 * <p/>
	 * 100/12/08新增<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@Size(max = 120)
	@Column(name = "ENAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String eName;

	/**
	 * 證券暨期貨違約交割紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ISQDATA8", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata8;

	/**
	 * 退票紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA9", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata9;

	/**
	 * 拒絕往來紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA10", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata10;

	/**
	 * 主債務逾期、催收、呆帳紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA11", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata11;

	/**
	 * 信用卡強停紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA13", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata13;

	/**
	 * 身分證補、換發紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ISQDATA12", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata12;

	/**
	 * 成年監護制度查詢紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ISQDATA15", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata15;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 擔任負責人或董監事之企業是否於本行有授信額度達一億元以上
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ISQDATA17", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata17;
	
	/**
	 * 本案「銀行法第33條之2、銀行法第33條之4」之情形
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
//	企業戶在「消金徵信」中，會用 check3 來判斷是否執行過相關資料查詢 
//	@NotEmpty(message = "{required.message}", groups = { Check3.class })
//	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "MBRLT33", length = 1, columnDefinition = "CHAR(1)")
	private String mbRlt33;
	
	/** 說明 **/
	@Size(max = 1800)
	@Column(name = "MBRLTDSCR33", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String mbRltDscr33;
	
	/**
	 * 疑似偽造證件或財力證明
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA18", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata18;
	
	/** 異常通報紀錄{1:有, 2:無} **/
	@Size(max = 1)
	@Column(name = "ISQDATA29", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata29;
	
	/** 進件來源{L:經地政士進件, P:個人送件,O:其它} **/
	@Size(max = 1)
	@Column(name = "CASESRCFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String caseSrcFlag;
	
	/** 地政士姓名  => 移至 C101S01Y  */
	@Size(max = 120)
	@Column(name = "LAANAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String laaName;
	
	/** 地政士證書-年  => 移至 C101S01Y  */
	@Size(max = 3)
	@Column(name = "LAAYEAR", length = 3, columnDefinition = "VARCHAR(3)")
	private String laaYear;
	
	/** 地政士證書-字號  => 移至 C101S01Y  */
	@Size(max = 15)
	@Column(name = "LAAWORD", length = 15, columnDefinition = "VARCHAR(15)")
	private String laaWord;
	
	/** 地政士證書-流水號  => 移至 C101S01Y  */
	@Size(max = 6)
	@Column(name = "LAANO", length = 6, columnDefinition = "VARCHAR(6)")
	private String laaNo;
	
	/** 進件來源說明 **/
	@Size(max = 60)
	@Column(name = "CASESRCMEMO", length = 60, columnDefinition = "VARCHAR(60)")
	private String caseSrcMemo;
	
	/** 財管_查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "WM_QDATE", columnDefinition = "DATE")
	private Date wm_qDate;
	
	/** 財管_理財客戶註記(A:績優理財客戶) J-107-0304 **/
	@Size(max = 1)
	@Column(name = "WM_FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String wm_flag;
	
	/** 財管_等級代碼 **/
	@Size(max = 2)
	@Column(name = "WM_GRA_CD", length = 2, columnDefinition = "VARCHAR(2)")
	private String wm_gra_cd;
	
	/** 財管_等級名稱 **/
	@Size(max = 12)
	@Column(name = "WM_GRA_NAME", length = 12, columnDefinition = "VARCHAR(12)")
	private String wm_gra_name;
	
	/** 陪同/代辦人員統編 **/
	@Size(max = 10)
	@Column(name = "AGENTPID", length = 10, columnDefinition = "VARCHAR(10)")
	private String agentPId;
	
	/** 陪同/代辦人員姓名 **/
	@Size(max = 120)
	@Column(name = "AGENTPNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String agentPName;

	/** AML重大負面新聞 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Column(name = "AMLBADNEWS", length = 60, columnDefinition = "VARCHAR(60)")
	private String amlBadNews;

	/** 信用負面新聞(訊息) */
    @Lob
	@Column(name = "CREDITBADNEWS", columnDefinition = "CLOB")
	private String creditBadNews;
	
    /** 查詢組合        註：未直接使用 setProdId(String prodId)而是用 json 去塞值，可查找  (1)ClsConstants.C101S01E.查詢組合    (2)ClsScoreUtil.PRODID_KEY */
    @Column(name = "PRODID", length = 2, columnDefinition = "VARCHAR(2)")
	private String prodId;
    
    /** Z13受監護/輔助宣告回傳網頁 */
    @Lob
	@Column(name = "Z13_HTML", columnDefinition = "CLOB")
	private String z13_html;
    
    /** Z21身分證領補換資料查詢驗證回傳網頁 */
    @Lob
	@Column(name = "Z21_HTML", columnDefinition = "CLOB")
	private String z21_html;    
    
    /** Z13受監護/輔助宣告查詢時間 */
	@Column(name = "Z13_QTIME", columnDefinition = "TIMESTAMP")
	private Timestamp z13_qTime;
	
	/** Z21身分證領補換資料查詢驗證查詢時間 */
	@Column(name = "Z21_QTIME", columnDefinition = "TIMESTAMP")
	private Timestamp z21_qTime;
	
	/** 一鍵查詢時間 */
	@Column(name = "ONEBTNQ_QTIME", columnDefinition = "TIMESTAMP")
	private Timestamp oneBtnQ_qTime;
	
	/** 地政士事務所統編 **/
	@Column(name = "LAAOFFICEID", length = 10, columnDefinition = "VARCHAR(10)")
	private String laaOfficeId;
	
	/** 地政士事務所名稱 **/
	@Column(name = "LAAOFFICE", length = 150, columnDefinition = "VARCHAR(150)")
	private String laaOffice;
		
	/** 地政士黑名單警示名單敘述 */
	@Size(max=300)
	@Column(name="LAADESC", length=300, columnDefinition="VARCHAR(300)")
	private String laaDesc;

	/** wiseNews信用負面新聞(訊息) */
	@Lob
	@Column(name = "WISENEWS", columnDefinition = "CLOB")
	private String wiseNews;
	
	/**
	 * 聯徵T70證券暨期貨違約交割記錄資訊
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	@NotEmpty(message = "{required.message}", groups = { Check3.class })
	@NotNull(message = "{required.message}", groups = { Check3.class })
	@Size(max = 1)
	@Column(name = "ISQDATA30", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata30;
	
	/**
	 * 	
	 */
	@Size(max=20)
	@Column(name = "VERSION", length = 20, columnDefinition = "VARCHAR(20)")
	private String version;
	
	/**
	 * 聯徵B42從債務查詢－擔保品類別
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ISQDATA31", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata31;
	
	/**
	 * 聯徵B42共同債務查詢－擔保品類別
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ISQDATA32", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata32;
	
	/**
	 * 借款人三年內購置不動產結案資訊，是否有近一年有二戶以上授信借貸結案紀錄
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ISQDATA33", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata33;
	
	/**
	 * 借款人三年內購置不動產結案資訊，是否有近三年有二戶以上授信借貸結案紀錄
	 * 1.有、2.無、3.N.A
	 */
	@Size(max = 1)
	@Column(name = "ISQDATA34", length = 1, columnDefinition = "CHAR(1)")
	private String isQdata34;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得資料來源 **/
	public String getDataSrc() {
		return this.dataSrc;
	}

	/** 設定資料來源 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}

	/**
	 * 取得有無票信退補記錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getEChkFlag() {
		return this.eChkFlag;
	}

	/**
	 * 設定有無票信退補記錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setEChkFlag(String value) {
		this.eChkFlag = value;
	}

	/** 取得票信資料截止日 **/
	public Date getEChkDDate() {
		return this.eChkDDate;
	}

	/** 設定票信資料截止日 **/
	public void setEChkDDate(Date value) {
		this.eChkDDate = value;
	}

	/** 取得票信查詢日期 **/
	public Date getEChkQDate() {
		return this.eChkQDate;
	}

	/** 設定票信查詢日期 **/
	public void setEChkQDate(Date value) {
		this.eChkQDate = value;
	}

	/**
	 * 取得有無聯徵逾催呆記錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getEJcicFlag() {
		return this.eJcicFlag;
	}

	/**
	 * 設定有無聯徵逾催呆記錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setEJcicFlag(String value) {
		this.eJcicFlag = value;
	}

	/** 取得聯徵資料日期 **/
	public Date getEJcicDDate() {
		return this.eJcicDDate;
	}

	/** 設定聯徵資料日期 **/
	public void setEJcicDDate(Date value) {
		this.eJcicDDate = value;
	}

	/** 取得聯徵查詢日期 **/
	public Date getEJcicQDate() {
		return this.eJcicQDate;
	}

	/** 設定聯徵查詢日期 **/
	public void setEJcicQDate(Date value) {
		this.eJcicQDate = value;
	}

	/**
	 * 取得引進原舊案資料
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public String getIsFromOld() {
		return this.isFromOld;
	}

	/**
	 * 設定引進原舊案資料
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setIsFromOld(String value) {
		this.isFromOld = value;
	}

	/**
	 * 取得婉卻紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata1() {
		return this.isQdata1;
	}

	/**
	 * 設定婉卻紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata1(String value) {
		this.isQdata1 = value;
	}

	/**
	 * 取得本行利害關係人
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata2() {
		return this.isQdata2;
	}

	/**
	 * 設定本行利害關係人
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata2(String value) {
		this.isQdata2 = value;
	}

	/**
	 * 取得金控利害關係人(44條)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata3() {
		return this.isQdata3;
	}

	/**
	 * 設定金控利害關係人(44條)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata3(String value) {
		this.isQdata3 = value;
	}

	/**
	 * 取得金控利害關係人(45條)
	 * <p/>
	 * 100/12/08新增<br/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata16() {
		return this.isQdata16;
	}

	/**
	 * 設定金控利害關係人(45條)
	 * <p/>
	 * 100/12/08新增<br/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata16(String value) {
		this.isQdata16 = value;
	}

	/**
	 * 取得主從債務人(不含本次資料)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata6() {
		return this.isQdata6;
	}

	/**
	 * 設定主從債務人(不含本次資料)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata6(String value) {
		this.isQdata6 = value;
	}

	/**
	 * 取得對同一自然人授信總餘額比率
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata5() {
		return this.isQdata5;
	}

	/**
	 * 設定對同一自然人授信總餘額比率
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata5(String value) {
		this.isQdata5 = value;
	}

	/**
	 * 取得歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata4() {
		return this.isQdata4;
	}

	/**
	 * 設定歸戶(本行餘額為a-Loan資料、他行餘額為聯徵資料)
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata4(String value) {
		this.isQdata4 = value;
	}

	/**
	 * 取得近一年內不含查詢當日非Z類被聯行查詢紀錄明細
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata14() {
		return this.isQdata14;
	}

	/**
	 * 設定近一年內不含查詢當日非Z類被聯行查詢紀錄明細
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata14(String value) {
		this.isQdata14 = value;
	}

	/**
	 * 取得黑名單
	 * <p/>
	 * 100/12/08新增<br/>
	 * 1.有、2.無、3.N.A => c101s01j.ans1<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public String getIsQdata7() {
		return this.isQdata7;
	}

	/**
	 * 設定黑名單
	 * <p/>
	 * 100/12/08新增<br/>
	 * 1.有、2.無、3.N.A => c101s01j.ans1<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setIsQdata7(String value) {
		this.isQdata7 = value;
	}

	/**
	 * 取得黑名單全型字英文名
	 * <p/>
	 * 100/12/08新增<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public String getEName() {
		return this.eName;
	}

	/**
	 * 設定黑名單全型字英文名
	 * <p/>
	 * 100/12/08新增<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setEName(String value) {
		this.eName = value;
	}

	/**
	 * 取得證券暨期貨違約交割紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata8() {
		return this.isQdata8;
	}

	/**
	 * 設定證券暨期貨違約交割紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata8(String value) {
		this.isQdata8 = value;
	}

	/**
	 * 取得退票紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata9() {
		return this.isQdata9;
	}

	/**
	 * 設定退票紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata9(String value) {
		this.isQdata9 = value;
	}

	/**
	 * 取得拒絕往來紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata10() {
		return this.isQdata10;
	}

	/**
	 * 設定拒絕往來紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata10(String value) {
		this.isQdata10 = value;
	}

	/**
	 * 取得主債務逾期、催收、呆帳紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata11() {
		return this.isQdata11;
	}

	/**
	 * 設定主債務逾期、催收、呆帳紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata11(String value) {
		this.isQdata11 = value;
	}

	/**
	 * 取得信用卡強停紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata13() {
		return this.isQdata13;
	}

	/**
	 * 設定信用卡強停紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata13(String value) {
		this.isQdata13 = value;
	}

	/**
	 * 取得身分證補、換發紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata12() {
		return this.isQdata12;
	}

	/**
	 * 設定身分證補、換發紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata12(String value) {
		this.isQdata12 = value;
	}

	/**
	 * 取得成年監護制度查詢紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata15() {
		return this.isQdata15;
	}

	/**
	 * 設定成年監護制度查詢紀錄
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata15(String value) {
		this.isQdata15 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 擔任負責人或董監事之企業是否於本行有授信額度達一億元以上
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getIsQdata17() {
		return this.isQdata17;
	}

	/**
	 * 擔任負責人或董監事之企業是否於本行有授信額度達一億元以上
	 * <p/>
	 * 1.有、2.無、3.N.A
	 **/
	public void setIsQdata17(String value) {
		this.isQdata17 = value;
	}
	
	/**
	 * 取得本案「銀行法第33條之2、銀行法第33條之4」之情形
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public String getMbRlt33() {
		return mbRlt33;
	}

	/**
	 * 設定本案「銀行法第33條之2、銀行法第33條之4」之情形
	 * <p/>
	 * 1.有、2.無、3.N.A
	 */
	public void setMbRlt33(String mbRlt33) {
		this.mbRlt33 = mbRlt33;
	}
	
	/** 取得說明 **/
	public String getMbRltDscr33() {
		return mbRltDscr33;
	}
	
	/** 設定說明 **/
	public void setMbRltDscr33(String mbRltDscr33) {
		this.mbRltDscr33 = mbRltDscr33;
	}
	
	/** 取得疑似偽造證件或財力證明 **/
	public String getIsQdata18() {
		return isQdata18;
	}
	
	/** 設定疑似偽造證件或財力證明 **/
	public void setIsQdata18(String isQdata18) {
		this.isQdata18 = isQdata18;
	}

	/** 取得是否有異常通報紀錄{1:有, 2:無} **/
	public String getIsQdata29() {
		return isQdata29;
	}
	/** 設定是否有異常通報紀錄{1:有, 2:無} **/
	public void setIsQdata29(String isQdata29) {
		this.isQdata29 = isQdata29;
	}

	/** 取得進件來源{L:經地政士進件, P:個人送件,O:其它}
	 *  上述 L P O 代碼取消，新增A B C
	 *  取得進件來源{A:買賣件經地政士辦理 B:買賣件非經地政士辦理 C:非買賣件}
	**/
	public String getCaseSrcFlag() {
		return caseSrcFlag;
	}
	/** 設定進件來源{L:經地政士進件, P:個人送件,O:其它} **/
	public void setCaseSrcFlag(String value) {
		this.caseSrcFlag = value;
	}
	
	/** 取得地政士姓名 **/
	@Deprecated
	public String getLaaName() {
		return laaName;
	}
	/** 設定地政士姓名 **/
	@Deprecated
	public void setLaaName(String value) {
		this.laaName = value;
	}
	
	/** 取得地政士證書-年 **/
	@Deprecated
	public String getLaaYear() {
		return laaYear;
	}
	/** 設定地政士證書-年 **/
	@Deprecated
	public void setLaaYear(String value) {
		this.laaYear = value;
	}
	
	/** 取得地政士證書-字號 **/
	@Deprecated
	public String getLaaWord() {
		return laaWord;
	}
	/** 設定地政士證書-字號 **/
	@Deprecated
	public void setLaaWord(String value) {
		this.laaWord = value;
	}
	
	/** 取得地政士證書-流水號 **/
	@Deprecated
	public String getLaaNo() {
		return laaNo;
	}
	/** 設定地政士證書-流水號 **/
	@Deprecated
	public void setLaaNo(String value) {
		this.laaNo = value;
	}
	
	/** 取得進件來源說明 **/
	public String getCaseSrcMemo() {
		return caseSrcMemo;
	}
	/** 設定進件來源說明 **/
	public void setCaseSrcMemo(String value) {
		this.caseSrcMemo = value;
	}

	/** 取得財管_查詢日期 **/
	public Date getWm_qDate() {
		return wm_qDate;
	}
	/** 設定財管_查詢日期 **/
	public void setWm_qDate(Date wm_qDate) {
		this.wm_qDate = wm_qDate;
	}

	/** 取得財管_理財客戶註記(A:績優理財客戶) **/
	public String getWm_flag() {
		return wm_flag;
	}
	/** 設定財管_理財客戶註記(A:績優理財客戶) **/
	public void setWm_flag(String wm_flag) {
		this.wm_flag = wm_flag;
	}

	/** 取得財管_等級代碼 **/
	public String getWm_gra_cd() {
		return wm_gra_cd;
	}
	/** 設定財管_等級代碼 **/
	public void setWm_gra_cd(String wm_gra_cd) {
		this.wm_gra_cd = wm_gra_cd;
	}

	/** 取得財管_等級名稱 **/
	public String getWm_gra_name() {
		return wm_gra_name;
	}
	/** 設定財管_等級名稱 **/
	public void setWm_gra_name(String wm_gra_name) {
		this.wm_gra_name = wm_gra_name;
	}
	
	/** 取得陪同/代辦人員統編 **/
	public String getAgentPId() {
		return agentPId;
	}
	/** 設定陪同/代辦人員統編 **/
	public void setAgentPId(String agentPId) {
		this.agentPId = agentPId;
	}
	/** 取得陪同/代辦人員姓名 **/
	public String getAgentPName() {
		return agentPName;
	}
	/** 設定陪同/代辦人員姓名 **/
	public void setAgentPName(String agentPName) {
		this.agentPName = agentPName;
	}

    /**
     * AML重大負面新聞
     * @return
     */
    public String getAmlBadNews() {
        return amlBadNews;
    }

    /**
     * AML重大負面新聞
     * @param amlBadNews
     */
    public void setAmlBadNews(String amlBadNews) {
        this.amlBadNews = amlBadNews;
    }

    /**
     *  信用負面新聞(訊息)
     * @return
     */
    public String getCreditBadNews() {
        return creditBadNews;
    }

    /**
     *  信用負面新聞(訊息)
     * @param creditBadNews
     */
    public void setCreditBadNews(String creditBadNews) {
        this.creditBadNews = creditBadNews;
    }

    /** 取得查詢組合 **/
	public String getProdId() {
		return prodId;
	}
	/** 設定查詢組合 **/
	public void setProdId(String prodId) {
		this.prodId = prodId;
	}

	public String getZ13_html() {
		return z13_html;
	}

	public void setZ13_html(String z13_html) {
		this.z13_html = z13_html;
	}

	public String getZ21_html() {
		return z21_html;
	}

	public void setZ21_html(String z21_html) {
		this.z21_html = z21_html;
	}

	public Timestamp getZ13_qTime() {
		return z13_qTime;
	}

	public void setZ13_qTime(Timestamp z13_qTime) {
		this.z13_qTime = z13_qTime;
	}

	public Timestamp getZ21_qTime() {
		return z21_qTime;
	}

	public void setZ21_qTime(Timestamp z21_qTime) {
		this.z21_qTime = z21_qTime;
	}

	public Timestamp getOneBtnQ_qTime() {
		return oneBtnQ_qTime;
	}

	public void setOneBtnQ_qTime(Timestamp oneBtnQ_qTime) {
		this.oneBtnQ_qTime = oneBtnQ_qTime;
	}

	@Deprecated
	public String getLaaOfficeId() {
		return laaOfficeId;
	}
	@Deprecated
	public void setLaaOfficeId(String laaOfficeId) {
		this.laaOfficeId = laaOfficeId;
	}
	@Deprecated
	public String getLaaOffice() {
		return laaOffice;
	}
	@Deprecated
	public void setLaaOffice(String laaOffice) {
		this.laaOffice = laaOffice;
	}
	@Deprecated
	public String getLaaDesc() {
		return this.laaDesc;
	}
	@Deprecated
	public void setLaaDesc(String value) {
		this.laaDesc = value;
	}
	public String getWiseNews() {
		return wiseNews;
	}

	public void setWiseNews(String wiseNews) {
		this.wiseNews = wiseNews;
	}
	
	/**
	 * join
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = false, insertable = false, updatable = false),
			//@JoinColumn(name = "ownBrId", referencedColumnName = "ownBrId", nullable = false),
			@JoinColumn(name = "custId", referencedColumnName = "custId", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "dupNo", referencedColumnName = "dupNo", nullable = false, insertable = false, updatable = false) })
	private C101M01A c101m01a;

	public void setC101m01a(C101M01A c101m01a) {
		this.c101m01a = c101m01a;
	}

	public C101M01A getC101m01a() {
		return c101m01a;
	}

	public String getIsQdata30() {
		return isQdata30;
	}

	public void setIsQdata30(String isQdata30) {
		this.isQdata30 = isQdata30;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getIsQdata31() {
		return isQdata31;
	}

	public void setIsQdata31(String isQdata31) {
		this.isQdata31 = isQdata31;
	}

	public String getIsQdata32() {
		return isQdata32;
	}

	public void setIsQdata32(String isQdata32) {
		this.isQdata32 = isQdata32;
	}

	public String getIsQdata33() {
		return isQdata33;
	}

	public void setIsQdata33(String isQdata33) {
		this.isQdata33 = isQdata33;
	}

	public String getIsQdata34() {
		return isQdata34;
	}

	public void setIsQdata34(String isQdata34) {
		this.isQdata34 = isQdata34;
	}
}
