/* 
 * F101S01BDao.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.F101S01B;

/**
 * <pre>
 * F101S01B dao interface.
 * </pre>
 * 
 * @since 2011/8/18
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/18,Sunkist Wang,new
 *          </ul>
 */
public interface F101S01BDao extends IGenericDao<F101S01B> {
    /**
     * 刪除所有資料
     * 
     * @param meta
     *            F101M01A
     * @return int
     */
    int deleteByMeta(F101M01A meta);

    /**
     * 依mainId及比率代號，建立一筆財務比率資料，提供前期資料寫入用。
     * 
     * @param mainId
     *            mainId
     * @param ratioNo
     *            財務比率代號
     * @return F101S01B
     */
    F101S01B findByMainIdAndRatioNo(String mainId, String ratioNo);

    /**
     * 依mainId與fieldId取得前期財務比率
     * 
     * @param mainId
     *            mainId
     * @param ratioNos
     *            ratioNo array
     * @return List<F101S01B>
     */
    List<F101S01B> findByMainIdAndRatioNo(String mainId, String[] ratioNos);
}
