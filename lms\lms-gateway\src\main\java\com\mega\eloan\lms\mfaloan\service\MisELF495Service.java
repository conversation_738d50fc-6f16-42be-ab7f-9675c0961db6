package com.mega.eloan.lms.mfaloan.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF495;

/**
 * <pre>
 * 企金覆審額度檔
 * </pre>
 * 
 */
public interface MisELF495Service {

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	public Map<String, Object> selStatsDataByBranch_lrDate(
			String elf495_branch, String elf495_lrdate_s,
			String elf495_lrdate_e, String ctlType);

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param elf495_branch
	 * @param elf495_lrdate_s
	 * @param elf495_lrdate_e
	 * @param ctlType
	 * @return
	 */
	public Map<String, Object> selStatsDataByBranch_lrDate_By_ApprId(
			String elf495_branch, String elf495_lrdate_s,
			String elf495_lrdate_e, String ctlType, String elf495_apprId);

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	public List<Map<String, Object>> findWithELF411(String branch,
			String custId, String dupNo, String sELF411_DATAYM,
			String sELF411_DATAYM_CM, String ctlType);

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	public List<ELF495> findBy_FLMS180R11(String branch, String custId,
			String dupNo, String cntrNo1, String cntrNo2, Date elf495_lrdate,
			String ctlType);
}
