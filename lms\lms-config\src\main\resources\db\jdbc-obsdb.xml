<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
           http:/www.springframework.org/schema/jee http:/www.springframework.org/schema/jee/spring-jee-2.0.xsd
           ">

	<!-- OBS海外集中 -->
	<bean id="obsdb-COM" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${obsdb_com.jndiName}</value>
		</property>
	</bean>
	<bean id="obsdb-COMTxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="obsdb-COM" />
	</bean>
	<!-- TPE海外集中 -->
	<bean id="obsdb-TPE" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${obsdb_tpe.jndiName}</value>
		</property>
	</bean>
	<bean id="obsdb-TPETxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="obsdb-TPE" />
	</bean>

	<!-- 紐約 -->
	<bean id="obsdb-0A2" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${obsdb_0A2.jndiName}</value>
		</property>
	</bean>
	<bean id="obsdb-0A2TxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="obsdb-0A2" />
	</bean>

	<!-- 曼谷 ELBKK, ELTKY, ELPAR, ELHKG -->
	<bean id="obsdb-0E3" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${obsdb_0E3.jndiName}</value>
		</property>
	</bean>
	<bean id="obsdb-0E3TxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="obsdb-0E3" />
	</bean>

	<!-- 巴黎 ELBKK, ELTKY, ELPAR, ELHKG -->
	<bean id="obsdb-0B0" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${obsdb_0B0.jndiName}</value>
		</property>
	</bean>
	<bean id="obsdb-0B0TxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="obsdb-0B0" />
	</bean>

	<!-- 香港 ELBKK, ELTKY, ELPAR, ELHKG -->
	<bean id="obsdb-0C3" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${obsdb_0C3.jndiName}</value>
		</property>
	</bean>
	<bean id="obsdb-0C3TxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="obsdb-0C3" />
	</bean>
	
	
	<!-- 澳洲 -->
	<bean id="obsdb-0B9" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${obsdb_0B9.jndiName}</value>
		</property>
	</bean>
	
	<bean id="obsdb-0B9TxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="obsdb-0B9" />
	</bean>
</beans>