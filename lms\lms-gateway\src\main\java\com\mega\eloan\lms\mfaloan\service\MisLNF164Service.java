/* 
 *MisLNF164Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

/**
 * <pre>
 * 利率條件LN.LNF164(EJCIC.T)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
public interface MisLNF164Service {

	/**
	 * 新增
	 * 
	 * @param dataList
	 *            <pre>
	 *   object[] content
	 *  br_no 分行別
	 *  contract 額度序號
	 *  cust_id 客戶編號
	 *  kind 契約種類
	 *  swft 放款幣別
	 *  lnap_code 放款科目
	 *  intrt_type 收息方式
	 *  int_kind 利率條件是否字述
	 *  int_base 加碼基礎
	 *  int_spread 加減碼
	 *  int_type 利率方式
	 *  intchg_type 利率變動方式
	 *  intchg_cycl
	 *  int_memo 利率條件中文敘述
	 * </pre>
	 */
	public void insert(List<Object[]> dataList);
}
