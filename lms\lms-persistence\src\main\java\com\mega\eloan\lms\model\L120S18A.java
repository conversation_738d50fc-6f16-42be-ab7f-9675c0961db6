package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 同一經濟關係人額度彙總檔 **/
// 參考 J-108-0288 增加的 table LMS.L120S11A：本案借款人同時為其他授信戶應收帳款債務人之額度資料
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S18A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "custId2", "dupNo2" }))
public class L120S18A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 */
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Column(name = "CUSTNAME", length = 150, columnDefinition = "VARCHAR(150)")
	private String custName;

	/*** 列印順序  */
	@Digits(integer = 5, fraction = 0)
	@Column(name = "PRINTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer printSeq;

	/** 序號{合計:99999} */
	@Digits(integer = 5, fraction = 0)
	@Column(name = "ITEMSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer itemSeq;

	/** 統一編號{合計:9999999999} */
	@Size(max = 10)
	@Column(name = "CUSTID2", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId2;

	/** 重覆序號{合計:9} */
	@Size(max = 1)
	@Column(name = "DUPNO2", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo2;

	/** 名稱 **/
	@Column(name = "CUSTNAME2", length = 150, columnDefinition = "VARCHAR(150)")
	private String custName2;

	/** 
	 * 類別{1:二親等以內血親, 2:本人擔任負責人之企業, 3:配偶擔任負責人之企業}  <br/>
	 * 	 select * from com.bcodetype where codetype='C100M01A_GROUP' and locale='zh_TW'
	 */	
	@Column(name = "CTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cType;

	/**
	 * 稱謂{M:本人, 0:配偶, 1:祖(外祖)父母, 2:父母, 3:兄弟姊妹, 4:子女, 5:孫(外孫)子女} <br/>
	 * 	 select * from com.bcodetype where  locale='zh_TW' and codetype='ploan_relationTypeName'  -- 參考 codetype='C100M01A_RELATION'
	 */
	@Column(name = "APPT", length = 1, columnDefinition = "CHAR(1)")
	private String appt;

	/** 授信幣別 **/
	@Size(max = 3)
	@Column(name = "FACTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String factCurr;

	/** 授信額度金額 **/
	@Column(name = "FACTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal factAmt;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "Date")
	private Date dataDate;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getDupNo() {
		return dupNo;
	}

	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public Integer getPrintSeq() {
		return printSeq;
	}

	public void setPrintSeq(Integer printSeq) {
		this.printSeq = printSeq;
	}

	public Integer getItemSeq() {
		return itemSeq;
	}

	public void setItemSeq(Integer itemSeq) {
		this.itemSeq = itemSeq;
	}

	public String getCustId2() {
		return custId2;
	}

	public void setCustId2(String custId2) {
		this.custId2 = custId2;
	}

	public String getDupNo2() {
		return dupNo2;
	}

	public void setDupNo2(String dupNo2) {
		this.dupNo2 = dupNo2;
	}

	public String getCustName2() {
		return custName2;
	}

	public void setCustName2(String custName2) {
		this.custName2 = custName2;
	}

	public String getcType() {
		return cType;
	}

	public void setcType(String cType) {
		this.cType = cType;
	}

	public String getAppt() {
		return appt;
	}

	public void setAppt(String appt) {
		this.appt = appt;
	}

	public String getFactCurr() {
		return factCurr;
	}

	public void setFactCurr(String factCurr) {
		this.factCurr = factCurr;
	}

	public BigDecimal getFactAmt() {
		return factAmt;
	}

	public void setFactAmt(BigDecimal factAmt) {
		this.factAmt = factAmt;
	}

	public Date getDataDate() {
		return dataDate;
	}

	public void setDataDate(Date dataDate) {
		this.dataDate = dataDate;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}

}
