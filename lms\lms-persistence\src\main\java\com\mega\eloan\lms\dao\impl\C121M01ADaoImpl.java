package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C121M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C121M01A;

/** 海外消金評等模型主檔 **/
@Repository
public class C121M01ADaoImpl extends LMSJpaDao<C121M01A, String>
	implements C121M01ADao {

	@Override
	public C121M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public C121M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

//	@Override
//	public C121M01A findByMainIdRatingId(String mainId, String ratingId){
//		ISearch search = createSearchTemplete();
//		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
//		search.addSearchModeParameters(SearchMode.EQUALS, "ratingId", ratingId);
//		return findUniqueOrNone(search);		
//	}
	
	@Override
	public List<C121M01A> findCopyRatingDoc(String ratingId, String docStatus){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "ratingId", ratingId);
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", docStatus);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		return find(search);
	}
}