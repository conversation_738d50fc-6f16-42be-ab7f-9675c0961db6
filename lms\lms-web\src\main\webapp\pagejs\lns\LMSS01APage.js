var initS01aJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	},
	afterSave : function(responseData){
		responseJSON["areaChk"] = responseData.LMS1205S01Form.areaChk;
		var $LMS1205S01Form = $("#LMS1205S01Form");
		$LMS1205S01Form.setData(responseData.LMS1205S01Form);
		$LMS1205S01Form.find("[name=areaChk]").each(function(i){
			if($(this).val() == responseData.LMS1205S01Form.areaChk){
				$(this).prop("checked",true);
			}
		});
		var areaChk = responseData.LMS1205S01Form.areaChk;
		if(areaChk=="2"){
			if(!lmsM01Json.isSpectialAndSign()){
				$LMS1205S01Form.find("#divArea1").show();
				$LMS1205S01Form.find("#divArea2").hide();
				$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
					if($(this).val() == responseData.LMS1205S01Form.areaBrId){
						$(this).prop("selected",true);
					}
				});
			}else{
				$LMS1205S01Form.find("#divArea1").hide();
			}
		}else if(areaChk=="3"){
			$LMS1205S01Form.find("#divArea1").hide();
			if(responseData.LMS1205S01Form.areaBrId != undefined && 
			responseData.LMS1205S01Form.areaBrId != null && 
			responseData.LMS1205S01Form.areaBrId != ""){
				$LMS1205S01Form.find("#divArea2").show();
				$LMS1205S01Form.find("#areaBrId2 option").each(function(i){
					if($(this).val() == responseData.LMS1205S01Form.areaBrId){
						$(this).prop("selected",true);
					}
				});
			}else{
				$LMS1205S01Form.find("#divArea2").hide();
			}
		}else if(areaChk=="5"){
			//(108)第 3230 號
			if(!lmsM01Json.isSpectialAndSign()){
				$LMS1205S01Form.find("#divArea1").show();
				$LMS1205S01Form.find("#divArea2").hide();
				$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
					if($(this).val() == responseData.LMS1205S01Form.areaBrId){
						$(this).prop("selected",true);
					}
				});
			}else{
				$LMS1205S01Form.find("#divArea1").hide();
			}
		}else{
			$LMS1205S01Form.find("#divArea1").hide();
			$LMS1205S01Form.find("#divArea2").hide();
		}
		// 陳復述案(異常通報)「是否加送會審單位」設為送審查且唯讀
		if(responseJSON.docCode == "3" || responseJSON.docCode == "4"){
			if(responseJSON.docKind == "2"){
				$LMS1205S01Form.find("[name='areaChk']:eq(3)").prop("checked",true);
				$LMS1205S01Form.find("[name='areaChk']").prop("disabled",true);
				$LMS1205S01Form.find("#divArea2").show();
			}
		}	
		
		// J-108-0243 微型企業
		//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		var mainId = responseData.LMS1205S01Form.mainId;

		if(responseJSON.docType == "1" && responseJSON.docCode == "1" 
				&& responseJSON.docKind == "1"){
			 
			// 企金一般授權內
			$LMS1205S01Form.find("#mini").show();
			var miniFlag = responseData.LMS1205S01Form.miniFlag;
			if(miniFlag == "Y"){
				
				if(lmsM01Json.hidePanelbyCaseType_004()){
					$("#book23").hide();
				}else{
					$("#book23").show();
				}
				 
				$(".showCaseType").show();
				$(".showCaseTypeA").hide();
			} else if(miniFlag == "N"){
				$("#book23").hide();
				$(".showCaseType").hide();
				$(".showCaseTypeA").hide();
				cleanLMSS23APanel(mainId);
			} else {
				$("#book23").hide();
				$(".showCaseType").hide();
				$(".showCaseTypeA").hide();
				
			}
		} else if(responseJSON.docType == "1" && responseJSON.docCode == "4"  ){	
			//企金異常通報
			//J-GGG-XXXX
			//J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
			$LMS1205S01Form.find("#mini").show();
			var miniFlag = responseData.LMS1205S01Form.miniFlag;
			if(miniFlag == "Y"){
				$("#book23").hide();
				$(".showCaseType").show();
				
				//J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
				var caseType =  responseData.LMS1205S01Form.caseType;
				var caseTypeA =  responseData.LMS1205S01Form.caseTypeA;

				if(lmsM01Json.showCaseTypeA(responseJSON.docType,responseJSON.docCode,miniFlag,caseType)){
			    	$(".showCaseTypeA").show();
			    }else{
			    	$(".showCaseTypeA").hide();
			    }

			} else if(miniFlag == "N"){
				$("#book23").hide();
				$(".showCaseType").hide();
				$(".showCaseTypeA").hide();
				cleanLMSS23APanel(mainId);
			} else {
				$("#book23").hide();
				$(".showCaseType").hide();
				$(".showCaseTypeA").hide();
			}
			
//			$LMS1205S01Form.find("[name='miniFlag']").prop("disabled",false);
//			$LMS1205S01Form.find("#caseType").prop("disabled",false);
			if (lmsM01Json.hideAbnormalPanelbyCaseType()){
				 $("#page05").hide();
	       		 $("#page08").hide();
	       		 $("#book10").hide();
	       		 $("#book11").hide();
	       		 $("#book12").hide();
	       		 $("#book8").hide();
			}else{
				$("#page05").show();
	       		 $("#page08").show();
	       		 $("#book10").show();
	       		 $("#book11").show();
	       		 $("#book12").show();
	       		 $("#book8").show();
			}
			
		} else if(responseJSON.docType == "1" && responseJSON.docCode == "2"
                    && responseJSON.docKind == "1"){
            // J-110-0458 授權內其他
            $LMS1205S01Form.find("#mini").show();
            var miniFlag = responseData.LMS1205S01Form.miniFlag;
            if(miniFlag == "Y"){
                $(".showCaseType").show();
            } else if(miniFlag == "N"){
                $(".showCaseType").hide();
            } else {
                $(".showCaseType").hide();
            }
            $(".showCaseTypeA").hide();
        } else {
			 
			$LMS1205S01Form.find("#mini").hide();
			$LMS1205S01Form.find("[name='miniFlag']").prop("disabled",true);
			//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			$LMS1205S01Form.find("#caseType").prop("disabled",true);
		}
		
		
		//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		//J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		//J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式 
		if (  lmsM01Json.hidePanelbyCaseType_miniFlag_passAml() ) {
			$("#book20").hide();
		}else{
			$("#book20").show();
		}
		
	},
	// 設定基本資訊內容設定(企金授權外)
	setContent : function(jsonInit){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		var unitNo = userInfo.unitNo;
		var unitType = userInfo.unitType;
		
		$LMS1205S01Form.find("."+jsonInit.hideboss).show();								
		$LMS1205S01Form.find("[id='1105']").hide();
		$LMS1205S01Form.find("[id='1205']").show();
		//送會簽
		$LMS1205S01Form.find("#areaBrId1").setItems({
			item : jsonInit.LMS1205S01Form.type2,
			format : " {value} {key}",
			space: false
		});
		//送審查
		$LMS1205S01Form.find("#areaBrId2").setItems($.extend({
			item : jsonInit.LMS1205S01Form.type1,
			format : " {value} {key}"
		},(lmsM01Json.isSpectialBank() || lmsM01Json.isSpectialAndSend() )? {space: true} : {space: false}));		
		$LMS1205S01Form.setData(jsonInit.LMS1205S01Form, false);
		var areaChk = jsonInit.LMS1205S01Form.areaChk;
		if(areaChk=="2"){
			if (!lmsM01Json.isSpectialAndSign()) {
				$LMS1205S01Form.find("#divArea1").show();
				$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
					if($(this).val() == jsonInit.LMS1205S01Form.areaBrId){
						$(this).prop("selected",true);
					}
				});				
			}else{
				$LMS1205S01Form.find("#divArea1").hide();
			}
			$LMS1205S01Form.find("#divArea2").hide();
		}else if(areaChk=="3"){
			$LMS1205S01Form.find("#divArea1").hide();
			if(jsonInit.areaBrId != undefined && 
			jsonInit.areaBrId != null && 
			jsonInit.areaBrId != ""){
				$LMS1205S01Form.find("#divArea2").show();
				$LMS1205S01Form.find("#areaBrId2 option").each(function(i){
					if($(this).val() == jsonInit.areaBrId){
						$(this).prop("selected",true);
					}
				});
			}else{
				$LMS1205S01Form.find("#divArea2").hide();
			}
		}else if(areaChk=="5"){
			//(108)第 3230 號
			if (!lmsM01Json.isSpectialAndSign()) {
				$LMS1205S01Form.find("#divArea1").show();
				$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
					if($(this).val() == jsonInit.LMS1205S01Form.areaBrId){
						$(this).prop("selected",true);
					}
				});				
			}else{
				$LMS1205S01Form.find("#divArea1").hide();
			}
			$LMS1205S01Form.find("#divArea2").hide();
		}else{
			$LMS1205S01Form.find("#divArea1").hide();
			$LMS1205S01Form.find("#divArea2").hide();
		}
		$LMS1205S01Form.find("[name=areaChk]").each(function(i){
			if($(this).val() == jsonInit.LMS1205S01Form.areaChk){
				$(this).prop("checked",true);
			}
		});
		// 控制CodeType下拉式選單是否唯讀
		if(responseJSON.readOnly){
			 if (responseJSON.readOnly.toString() == "true") {
		            $("#LMS1205S01Form select").prop("disabled", true);
		            $("#LMS1205S01Form select").prop("disabled", true);
		        }
		}
       
		$LMS1205S01Form.find("#boss").html(jsonInit.LMS1205S01Form.boss);
		$LMS1205S01Form.find("#seaBoss").html(jsonInit.LMS1205S01Form.seaBoss);
		$LMS1205S01Form.find("#headReCheck").html(jsonInit.LMS1205S01Form.headReCheck);
		$LMS1205S01Form.find("#areaManager").html(jsonInit.LMS1205S01Form.areaManager);
		$LMS1205S01Form.find("#_appraiserCN").html(jsonInit.LMS1205S01Form._appraiserCN);
		$LMS1205S01Form.find("#_headReCheck").html(jsonInit.LMS1205S01Form._headReCheck);

		// 欄位隱藏顯示控制		
		initS01aJson.controlCol(jsonInit);
	},
	// 設定基本資訊內容設定(企金授權內)
	setContent2 : function(jsonInit){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		$LMS1205S01Form.find("."+jsonInit.hideboss).show();
		$LMS1205S01Form.find("[id='1205']").hide();
		$LMS1205S01Form.find("[id='1105']").show();
		
		//授權等級
		var obj2= CommonAPI.loadCombos("lms1201m01_authLvl");
		$LMS1205S01Form.find("#authLvl").setItems({
			item : obj2.lms1201m01_authLvl,
			space: false,
			format : "{key}"
		});
		// 營運中心授權內
		$LMS1205S01Form.find("#areaBrId3").setItems({
			item : jsonInit.LMS1205S01Form.type1,
			format : " {value} {key}",
			space: false
		});
		if(jsonInit.LMS1205S01Form.noParent){
			$LMS1205S01Form.find("#authLvl option:eq(1)").remove();
			//$LMS1205S01Form.find("#authLvl option:eq(2)").remove();
		}
		// 控制CodeType下拉式選單是否唯讀
		if(responseJSON.readOnly){
			if(responseJSON.readOnly.toString() == "true"){
				$("#LMS1205S01Form select").prop("disabled",true);
			}
		}
		$LMS1205S01Form.setData(jsonInit.LMS1205S01Form, false);
		$LMS1205S01Form.find("#authLvl option").each(function(i){
			var $this = $(this);
			if($this.val() == jsonInit.LMS1205S01Form.authLvl){
				$this.prop("selected",true);
				if($this.val() == "3"){
					$LMS1205S01Form.find("#divArea3").show();
				}else{
					$LMS1205S01Form.find("#divArea3").hide();
				}
			}
		});
		$LMS1205S01Form.find("#areaBrId3 option").each(function(i){
			if($(this).val() == jsonInit.LMS1205S01Form.areaBrId){
				$(this).prop("selected",true);
			}
		});
		
		var areaChk = jsonInit.LMS1205S01Form.areaChk;
		if(areaChk=="2"){
			if (!lmsM01Json.isSpectialAndSign()) {
				$LMS1205S01Form.find("#divArea1").show();
				$LMS1205S01Form.find("#divArea2").hide();
				$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
					var $this = $(this);
					if($this.val() == jsonInit.LMS1205S01Form.areaBrId){
						$this.prop("selected",true);
					}
				});			
			}else{
				$LMS1205S01Form.find("#divArea1").hide();
			}
		}else if(areaChk=="3"){
			$LMS1205S01Form.find("#divArea1").hide();
			if(jsonInit.areaBrId != undefined && 
			jsonInit.areaBrId != null && 
			jsonInit.areaBrId != ""){
				$LMS1205S01Form.find("#divArea2").show();
				$LMS1205S01Form.find("#areaBrId2 option").each(function(i){
					if($(this).val() == jsonInit.areaBrId){
						$(this).prop("selected",true);
					}
				});
			}else{
				$LMS1205S01Form.find("#divArea2").hide();
			}
		}else if(areaChk=="5"){
			//(108)第 3230 號
			if (!lmsM01Json.isSpectialAndSign()) {
				$LMS1205S01Form.find("#divArea1").show();
				$LMS1205S01Form.find("#divArea2").hide();
				$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
					var $this = $(this);
					if($this.val() == jsonInit.LMS1205S01Form.areaBrId){
						$this.prop("selected",true);
					}
				});			
			}else{
				$LMS1205S01Form.find("#divArea1").hide();
			}
		}else{
			$LMS1205S01Form.find("#divArea1").hide();
			$LMS1205S01Form.find("#divArea2").hide();
		}
		$LMS1205S01Form.find("[name=areaChk]").each(function(i){
			var $this = $(this);
			if($this.val() == jsonInit.LMS1205S01Form.areaChk){
				$this.prop("checked",true);
			}
		});
		// 控制CodeType下拉式選單是否唯讀
		if(responseJSON.readOnly){
	        if (responseJSON.readOnly.toString() == "true") {
	            $("#LMS1205S01Form select").prop("disabled", true);
	            $("#LMS1205S01Form select").prop("disabled", true);
	        }
		}
		$LMS1205S01Form.find("#boss").html(jsonInit.LMS1205S01Form.boss);
		$LMS1205S01Form.find("#seaBoss").html(jsonInit.LMS1205S01Form.seaBoss);
		$LMS1205S01Form.find("#headReCheck").html(jsonInit.LMS1205S01Form.headReCheck);
		$LMS1205S01Form.find("#areaManager").html(jsonInit.LMS1205S01Form.areaManager);
		$LMS1205S01Form.find("#_appraiserCN").html(jsonInit.LMS1205S01Form._appraiserCN);
		$LMS1205S01Form.find("#_headReCheck").html(jsonInit.LMS1205S01Form._headReCheck);

		// 欄位隱藏顯示控制
		initS01aJson.controlCol(jsonInit);
	},
	// 設定基本資訊內容設定(陳復述案、其他)
	setContent3 : function(jsonInit){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		var docKind = responseJSON.docKind;		
		var unitNo = userInfo.unitNo;
		var unitType = userInfo.unitType;		
		if(docKind == "1"){
			//授權內
			$LMS1205S01Form.find("."+jsonInit.hideboss).show();
			$LMS1205S01Form.find("[id='1205']").hide();
			$LMS1205S01Form.find("[id='1105']").show();
		}else{
			//授權外
			$LMS1205S01Form.find("."+jsonInit.hideboss).show();
			$LMS1205S01Form.find("td[id='1105']").hide();
			$LMS1205S01Form.find("[id='1205']").show();
		}
		//送會簽
		$LMS1205S01Form.find("#areaBrId1").setItems({
			item : jsonInit.LMS1205S01Form.type2,
			format : " {value} {key}",
			space: false
		});
		//送審查
		$LMS1205S01Form.find("#areaBrId2").setItems($.extend({
			item : jsonInit.LMS1205S01Form.type1,
			format : " {value} {key}"
		},(lmsM01Json.isSpectialBank() || lmsM01Json.isSpectialAndSend() )? {space: true} : {space: false}));
		// 營運中心授權內
		$LMS1205S01Form.find("#areaBrId3").setItems({
			item : jsonInit.LMS1205S01Form.type1,
			format : " {value} {key}",
			space: false
		});				
		//授權等級
		var obj2= CommonAPI.loadCombos("lms1201m01_authLvl");
		$LMS1205S01Form.find("#authLvl").setItems({
			item : obj2.lms1201m01_authLvl,
			format : "{key}",
			space: false
		});
		if(jsonInit.LMS1205S01Form.noParent){
			$LMS1205S01Form.find("#authLvl option:eq(1)").remove();
			//$LMS1205S01Form.find("#authLvl option:eq(2)").remove();
		}				
		$LMS1205S01Form.setData(jsonInit.LMS1205S01Form, false);
		
		$LMS1205S01Form.find("#authLvl option").each(function(i){
			var $this = $(this);
			if($this.val() == jsonInit.LMS1205S01Form.authLvl){
				$this.prop("selected",true);
				if($this.val() == "3"){
					$LMS1205S01Form.find("#divArea3").show();
				}else{
					$LMS1205S01Form.find("#divArea3").hide();
				}
			}
		});
		$LMS1205S01Form.find("#areaBrId3 option").each(function(i){
			if($(this).val() == jsonInit.LMS1205S01Form.areaBrId){
				$(this).prop("selected",true);
			}
		});

		
		var areaChk = jsonInit.LMS1205S01Form.areaChk;
		if(areaChk=="2"){
			if (!lmsM01Json.isSpectialAndSign()) {
				$LMS1205S01Form.find("#divArea1").show();
				$LMS1205S01Form.find("#divArea2").hide();
				$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
					if($(this).val() == jsonInit.LMS1205S01Form.areaBrId){
						$(this).prop("selected",true);
					}
				});			
			}else{
				$LMS1205S01Form.find("#divArea1").hide();
			}
			$LMS1205S01Form.find("#divArea2").hide();
		}else if(areaChk=="3"){
			$LMS1205S01Form.find("#divArea1").hide();
			if(jsonInit.areaBrId != undefined && 
			jsonInit.areaBrId != null && 
			jsonInit.areaBrId != ""){
				$LMS1205S01Form.find("#divArea2").show();
				$LMS1205S01Form.find("#areaBrId2 option").each(function(i){
					if($(this).val() == jsonInit.areaBrId){
						$(this).prop("selected",true);
					}
				});
			}else{
				$LMS1205S01Form.find("#divArea2").hide();
			}
		}else if(areaChk=="5"){
			//(108)第 3230 號
			if (!lmsM01Json.isSpectialAndSign()) {
				$LMS1205S01Form.find("#divArea1").show();
				$LMS1205S01Form.find("#divArea2").hide();
				$LMS1205S01Form.find("#areaBrId1 option").each(function(i){
					if($(this).val() == jsonInit.LMS1205S01Form.areaBrId){
						$(this).prop("selected",true);
					}
				});			
			}else{
				$LMS1205S01Form.find("#divArea1").hide();
			}
			$LMS1205S01Form.find("#divArea2").hide();	
		}else{
			$LMS1205S01Form.find("#divArea1").hide();
			$LMS1205S01Form.find("#divArea2").hide();
		}
		$LMS1205S01Form.find("[name=areaChk]").each(function(i){
			if($(this).val() == jsonInit.LMS1205S01Form.areaChk){
				$(this).prop("checked",true);
			}
		});				
		// 控制CodeType下拉式選單是否唯讀
		if(responseJSON.readOnly){
	        if (responseJSON.readOnly.toString() == "true") {
	            $("#LMS1205S01Form select").prop("disabled", true);
	            $("#LMS1205S01Form select").prop("disabled", true);
	        }
		}
		// 陳復述案(異常通報)「是否加送會審單位」設為送審查且唯讀
		if(responseJSON.docCode == "3" || responseJSON.docCode == "4"){
			if(responseJSON.docKind == "2"){
				$LMS1205S01Form.find("[name='areaChk']:eq(3)").prop("checked",true);
				$LMS1205S01Form.find("[name='areaChk']").prop("disabled",true);
				$LMS1205S01Form.find("#divArea2").show();
			}
		}
		
		$LMS1205S01Form.find("#boss").html(jsonInit.LMS1205S01Form.boss);
		$LMS1205S01Form.find("#seaBoss").html(jsonInit.LMS1205S01Form.seaBoss);
		$LMS1205S01Form.find("#headReCheck").html(jsonInit.LMS1205S01Form.headReCheck);
		$LMS1205S01Form.find("#areaManager").html(jsonInit.LMS1205S01Form.areaManager);
		$LMS1205S01Form.find("#_appraiserCN").html(jsonInit.LMS1205S01Form._appraiserCN);
		$LMS1205S01Form.find("#_headReCheck").html(jsonInit.LMS1205S01Form._headReCheck);

		// 欄位隱藏顯示控制
		initS01aJson.controlCol(jsonInit);
	},	
	// 欄位隱藏顯示控制
	controlCol : function(jsonInit){
		var $LMS1205S01Form = $("#LMS1205S01Form");
		//判定是否有總行資料，若有則顯示外框，無則隱藏
		if(!jsonInit.LMS1205S01Form.hasAdmin){
			$LMS1205S01Form.find(".hasAdmin1").css("border","0");
			$LMS1205S01Form.find(".hasAdmin2").hide();
		}		
		// 控制()顯示隱藏
		var createTime = jsonInit.LMS1205S01Form.createTime;	//文件建立者
		if(createTime != null && createTime != undefined && createTime != ""){
			$LMS1205S01Form.find(".noDataHideA").show();
		}else{
			$LMS1205S01Form.find(".noDataHideA").hide();
		}				
		var updateTime = jsonInit.LMS1205S01Form.updateTime;	//文件異動者
		if(updateTime != null && updateTime != undefined && updateTime != ""){
			$LMS1205S01Form.find(".noDataHideB").show();
		}else{
			$LMS1205S01Form.find(".noDataHideB").hide();
		}																								
		var sendFirstTime = jsonInit.LMS1205S01Form.sendFirstTime; //分行首次送件
		if(sendFirstTime != null && sendFirstTime != undefined && sendFirstTime != ""){
			$LMS1205S01Form.find(".noDataHide1").show();
		}else{
			$LMS1205S01Form.find(".noDataHide1").hide();
		}				
		var sendLastTime = jsonInit.LMS1205S01Form.sendLastTime; //分行最後送件
		if(sendLastTime != null && sendLastTime != undefined && sendLastTime != ""){
			$LMS1205S01Form.find(".noDataHide2").show();
		}else{
			$LMS1205S01Form.find(".noDataHide2").hide();
		}				
		if(jsonInit.hideReturnBH){
			$LMS1205S01Form.find("#returnBH").hide();
		}else{
			$LMS1205S01Form.find("#returnBH").show();
		}
		if(lmsM01Json.isSpectialAndSign()){
			$LMS1205S01Form.find("#divArea1").hide();
		}
	    // 控制總處分行會簽後修改送會簽與送會簽審查選項顯示隱藏
		// Miller added at 2013/05/02	
		//(108)第 3230 號 
		if(responseJSON.areaChk == "4" && responseJSON.docStatus != "07O"){
			$LMS1205S01Form.find("#areaChk4").show();
			$LMS1205S01Form.find("#areaChk2").hide();
			
		}else if(responseJSON.areaChk == "6" && responseJSON.docStatus != "07O"){
			$LMS1205S01Form.find("#areaChk6").show();
			$LMS1205S01Form.find("#areaChk5").hide();	
		}else{
			if (responseJSON.areaChk == "4" && responseJSON.docStatus == "07O") {
				// 當送會簽審查從授管處退回待補件時，將送會簽審查改成送會簽
				setTimeout(function(){
					$LMS1205S01Form.find("[name='areaChk']:radio:eq(1)").prop("checked", true);
				}, 500)
			}else if (responseJSON.areaChk == "6" && responseJSON.docStatus == "07O") {
				// 當送初審審查從授管處退回待補件時，將送初審審查改成送初審
				//(108)第 3230 號
				setTimeout(function(){
					$LMS1205S01Form.find("[name='areaChk']:radio:eq(4)").prop("checked", true);
				}, 500)
			}
			$LMS1205S01Form.find("#areaChk2").show();
			$LMS1205S01Form.find("#areaChk4").hide();			
			//(108)第 3230 號
			$LMS1205S01Form.find("#areaChk5").show();
			$LMS1205S01Form.find("#areaChk6").hide();			
		}
		
		if(userInfo.unitNo == "918" && responseJSON.mainDocStatus == "01K"){
			// 若為授管處提會會簽後修改則唯讀狀態
			$LMS1205S01Form.find("input[name='areaChk']").prop("disabled",true);
		}

		var rptTitleArea1 = $LMS1205S01Form.find("#rptTitleArea1").html();
		if(rptTitleArea1 != undefined && rptTitleArea1 != null && rptTitleArea1 != ""){
			// 控制營運中心授審會會期隱藏顯示
			$LMS1205S01Form.find(".areaTitle").show();
		}
		
		// J-108-0243 微型企業
		//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		var mainId = jsonInit.LMS1205S01Form.mainId;
		if(responseJSON.docType == "1" && responseJSON.docCode == "1" 
				&& responseJSON.docKind == "1"){
			// 企金一般授權內
			$LMS1205S01Form.find("#mini").show();
			var miniFlag = jsonInit.LMS1205S01Form.miniFlag;
			if(miniFlag == "Y"){
				 
				if(lmsM01Json.hidePanelbyCaseType_004()){
					$("#book23").hide();
				}else{
					$("#book23").show();
				}
				
				$(".showCaseType").show();
			} else if(miniFlag == "N"){
				$("#book23").hide();
				$(".showCaseType").hide();
			} else {
				$("#book23").hide();
				$(".showCaseType").hide();
			}
		} else if(responseJSON.docType == "1" && responseJSON.docCode == "4"  ){	
			 
			//J-GGG-XXXX
			$LMS1205S01Form.find("#mini").show();
			var miniFlag = jsonInit.LMS1205S01Form.miniFlag;
			if(miniFlag == "Y"){
				$("#book23").hide();
				$(".showCaseType").show();
			} else if(miniFlag == "N"){
				$("#book23").hide();
				$(".showCaseType").hide();
			} else {
				$("#book23").hide();
				$(".showCaseType").hide();
			}
			
//			$LMS1205S01Form.find("[name='miniFlag']").prop("disabled",false);
//			$LMS1205S01Form.find("#caseType").prop("disabled",false);	
			if (lmsM01Json.hideAbnormalPanelbyCaseType()){ 
				 $("#page05").hide();
	       		 $("#page08").hide();
	       		 $("#book10").hide();
	       		 $("#book11").hide();
	       		 $("#book12").hide();
	       		 $("#book8").hide();
			}else{	
				 $("#page05").show();
	       		 $("#page08").show();
	       		 $("#book10").show();
	       		 $("#book11").show();
	       		 $("#book12").show();
	       		 $("#book8").show();
			}
			 
		} else if(responseJSON.docType == "1" && responseJSON.docCode == "2"
                             && responseJSON.docKind == "1"){
            // J-110-0458 授權內其他
            $LMS1205S01Form.find("#mini").show();
            var miniFlag = jsonInit.LMS1205S01Form.miniFlag;
            if(miniFlag == "Y"){
                $(".showCaseType").show();
            } else if(miniFlag == "N"){
                $(".showCaseType").hide();
            } else {
                $(".showCaseType").hide();
            }
            $(".showCaseTypeA").hide();
        } else {
			$LMS1205S01Form.find("#mini").hide();
			$LMS1205S01Form.find("[name='miniFlag']").prop("disabled",true);
			$LMS1205S01Form.find("#caseType").prop("disabled",true);
		}	
		
		//J-109-0077_05097_B1021 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
		//J-109-0KKK_05097_B1001 簡化青年創業及啟動金貸款簽報書簽案流程
		//J-109-0459_05097_B1001 Web e-Loan簡化微型企業簽報書資僅為動用新台幣案件時得免執行制裁/管制名單掃描。
		//J-110-0CCC_05097_B1001 Web e-Loan新增國發基金協助新創事業紓困融資加碼方案微型企業簽報書格式 
		if ( lmsM01Json.hidePanelbyCaseType_miniFlag_passAml()   ) {
			$("#book20").hide();
		}else{
			$("#book20").show();
		}
		
		//J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
		var LMS1205S01Form = $("#LMS1205S01Form");
        var caseType =  LMS1205S01Form.find("#caseType").val();
        var miniFlag =  LMS1205S01Form.find("input[name='miniFlag']:radio:checked").val();
        
        //J-110-0336_05097_B1001 Web e-Loan授信異常通報增加通報類別與流程
        if(lmsM01Json.showCaseTypeA(responseJSON.docType ,responseJSON.docCode,miniFlag,caseType)){	
        	//異常通報
        	LMS1205S01Form.find(".showCaseTypeA").show();
        }else{
        	LMS1205S01Form.find(".showCaseTypeA").hide();
        	LMS1205S01Form.find("#caseTypeA").val('');
        }

		
	}
};

$(document).ready(function() {
	initS01aJson.setHandler();
	$.form.init({
        formHandler: initS01aJson.handlerName,
        formPostData:{
        	formAction : "queryLms1205m01",
        	oid : responseJSON.oid,
        	page : responseJSON.page,
        	mainId : responseJSON.mainId,
        	docType : responseJSON.docType,
        	docCode : responseJSON.docCode,
        	docKind : responseJSON.docKind,
			docStatus : responseJSON.mainDocStatus,
			areaDocstatus : responseJSON.areaDocstatus,
			txCode : responseJSON.txCode,
			itemDscr03 : "",
			itemDscr05 : "",
			ffbody : "",
			brnGroup : responseJSON.brnGroup			
        },
		loadSuccess:function(jsonInit){
			  
			if (initS01aJson.handlerName == "lms1201formhandler") {
				// 企金授權外設定
				initS01aJson.setContent(jsonInit);
			}else if (initS01aJson.handlerName == "lms1101formhandler") {
				// 企金授權內設定
				initS01aJson.setContent2(jsonInit);
			}else if (initS01aJson.handlerName == "lms1301formhandler"){
				// 陳復述案設定
				initS01aJson.setContent3(jsonInit);				
			}
			if(responseJSON.brnGroup != ""){
				var $LMS1205S01Form = $("#LMS1205S01Form");
				$LMS1205S01Form.find("#divArea1").hide();
				$LMS1205S01Form.find("#divArea2").show();
				$LMS1205S01Form.find("input[name='areaChk']:eq(3)").prop("checked",true);
				$LMS1205S01Form.find("input[name='areaChk']").prop("disabled",true);
				$LMS1205S01Form.find("#areaBrId2").val(responseJSON.brnGroup);
				$LMS1205S01Form.find("#areaBrId2").prop("disabled",true);
				// 營運中心制分行，營運中心授權內案件一樣不應該能選哪一間營運中心
				if(responseJSON.docKind == "1"){
					$LMS1205S01Form.find("#areaBrId3").val(responseJSON.brnGroup);
					$LMS1205S01Form.find("#areaBrId3").prop("disabled",true);
				}
			}
			
			for(o in jsonInit.hideBook){
				$(jsonInit.hideBook[o]).hide();
			}
			//UPGRADETODO TODO
			//$(".tabs-warp").scrollToTab();			
            // 所屬營運中心設定
			var $showBorrowData = $("#showBorrowData");
            responseJSON["brnGroup"] = jsonInit.brnGroup;
            if (jsonInit.areaTitle) {
                $showBorrowData.find("#title1301").html(jsonInit.areaTitle);
                if (lmsM01Json.docType == "1") {
                    $showBorrowData.find("#title0a").show();
                    $showBorrowData.find("#title0b").hide();
                } else if (lmsM01Json.docType == "2") {
                    $showBorrowData.find("#title0a").hide();
                    $showBorrowData.find("#title0b").show();
                }					
                if (lmsM01Json.docCode == "3") {
                    $showBorrowData.find("#title1y").show();
                } else if (lmsM01Json.docCode == "4") {
                    $showBorrowData.find("#title1x").show();
                }
            } else {
                if (lmsM01Json.docType == "1") {
                    $showBorrowData.find("#title0a").show();
                    $showBorrowData.find("#title0b").hide();
                } else if (lmsM01Json.docType == "2") {
                    $showBorrowData.find("#title0a").hide();
                    $showBorrowData.find("#title0b").show();
                }
                if (lmsM01Json.docKind == "2") {
                    if (lmsM01Json.docCode == "1") {
                        $showBorrowData.find("#title1").show();
                        $showBorrowData.find("#title1a").hide();
                        $showBorrowData.find("#title1b").hide();
                    } else if (lmsM01Json.docCode == "2") {
                        $showBorrowData.find("#title1a").show();
                        $showBorrowData.find("#title1").hide();
                        $showBorrowData.find("#title1b").hide();
                    } else if (lmsM01Json.docCode == "3") {
                        $showBorrowData.find("#title1b").show();
                        $showBorrowData.find("#title1").hide();
                        $showBorrowData.find("#title1a").hide();
                        $showBorrowData.find("#title1y").show();
                    } else if (lmsM01Json.docCode == "4") {
                        // other.msg114=授權外案件簽報書(異常通報案件)
                        $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg114"]);
                        $showBorrowData.find("#title1x").show();
                    }
                } else if (lmsM01Json.docKind == "1") {
                    $showBorrowData.find("#title1").hide();
                    $showBorrowData.find("#title1c").hide();
                    $showBorrowData.find("#title1d").hide();
                    $showBorrowData.find("#title1e").hide();
                    $showBorrowData.find("#title1f").hide();
                    $showBorrowData.find("#title1g").hide();
                    $showBorrowData.find("#title1h").hide();
                    $showBorrowData.find("#title1i").hide();
                    $showBorrowData.find("#title1j").hide();
                    $showBorrowData.find("#title1k").hide();						
                    if (responseJSON.authLvl == "2") {
                        // 總行授權內
                        if (lmsM01Json.docCode == "1") {
                            $showBorrowData.find("#title1f").show();
                            $showBorrowData.find("#title1g").hide();
                            $showBorrowData.find("#title1h").hide();
                        } else if (lmsM01Json.docCode == "2") {
                            $showBorrowData.find("#title1g").show();
                            $showBorrowData.find("#title1f").hide();
                            $showBorrowData.find("#title1h").hide();
                        } else if (lmsM01Json.docCode == "3") {
                            $showBorrowData.find("#title1h").show();
                            $showBorrowData.find("#title1f").hide();
                            $showBorrowData.find("#title1g").hide();
                            $showBorrowData.find("#title1y").show();
                        } else if (lmsM01Json.docCode == "4") {
                            // other.msg115=總行授權內案件簽報書(異常通報案件)
                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg115"]);
                            $showBorrowData.find("#title1x").show();
                        }
                    } else if (responseJSON.authLvl == "1") {
                        // 分行授權內
                        if (lmsM01Json.docCode == "1") {
                            $showBorrowData.find("#title1c").show();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1e").hide();
                        } else if (lmsM01Json.docCode == "2") {
                            $showBorrowData.find("#title1d").show();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1e").hide();
                        } else if (lmsM01Json.docCode == "3") {
                            $showBorrowData.find("#title1e").show();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1y").show();
                        } else if (lmsM01Json.docCode == "4") {
                            // other.msg116=分行授權內案件簽報書(異常通報案件)
                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
                            $showBorrowData.find("#title1x").show();
                        }
                    } else if (responseJSON.authLvl == "3") {
                        // 營運中心授權內
                        if (responseJSON.docCode == "1") {
                            $showBorrowData.find("#title1i").show();
                            $showBorrowData.find("#title1j").hide();
                            $showBorrowData.find("#title1k").hide();
                        } else if (responseJSON.docCode == "2") {
                            $showBorrowData.find("#title1j").show();
                            $showBorrowData.find("#title1i").hide();
                            $showBorrowData.find("#title1k").hide();
                        } else if (responseJSON.docCode == "3") {
                            $showBorrowData.find("#title1k").show();
                            $showBorrowData.find("#title1j").hide();
                            $showBorrowData.find("#title1i").hide();
                            $showBorrowData.find("#title1y").show();
                        } else if (lmsM01Json.docCode == "4") {
                            // other.msg117=營運中心授權內案件簽報書(異常通報案件)
                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg117"]);
                            $showBorrowData.find("#title1x").show();
                        }
                    } else {
                        // 預設顯示
                        if (lmsM01Json.docCode == "1") {
                            $showBorrowData.find("#title1c").show();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1e").hide();
                        } else if (lmsM01Json.docCode == "2") {
                            $showBorrowData.find("#title1d").show();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1e").hide();
                        } else if (lmsM01Json.docCode == "3") {
                            $showBorrowData.find("#title1e").show();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1y").show();
                        } else if (lmsM01Json.docCode == "4") {
                            // other.msg116=分行授權內案件簽報書(異常通報案件)
                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
                            $showBorrowData.find("#title1x").show();
                        }
                    }
                }
            }	
            // J-110-0493_11557_B1001
            // 檢查利害關係人授信額度合計是否達新台幣1億元以上
            if (jsonInit.rltOver100Million == 'Y') {
            	$("#LMS1205S01Form").find("#rltOver100MillionTr").show();
            }else{
            	$("#LMS1205S01Form").find("#rltOver100MillionTr").hide();
            }
		}
    });
});

// J-108-0243 微型企業
function cleanLMSS23APanel(mainId){
    $.ajax({
        handler: "microentformhandler",
        action: "deleteL120s10a",
        data: {
            mainId: mainId
        }
	}).done(function(obj) {
		ilog.debug('hihi');
    });
}