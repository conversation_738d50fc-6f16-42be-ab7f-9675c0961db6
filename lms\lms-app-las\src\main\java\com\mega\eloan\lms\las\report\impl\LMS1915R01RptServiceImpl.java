package com.mega.eloan.lms.las.report.impl;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.report.ReportGenerator;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.las.report.LMS1915R01RptService;
import com.mega.eloan.lms.las.service.LMS1915Service;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01E;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.sso.service.BranchService;

@Service("lms1915r01rptservice")
public class LMS1915R01RptServiceImpl extends AbstractReportService implements
		LMS1915R01RptService {

	@Resource
	LMS1915Service lms1915Service;

	@Resource
	BranchService branchService;

	@Override
	public void setReport001(ReportGenerator rptGenerator, String mainOid) {
		String balDate = "";

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		DecimalFormat df = new DecimalFormat("###,###,###,###,###,###,###,###");

		// String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = lms1915Service.getL192M01A(mainOid);

		Set<L192M01B> l192m01bs = meta.getL192m01bs();

		List<Map<String, String>> list = new ArrayList<Map<String, String>>();

		for (L192M01B l192m01b : l192m01bs) {
			if (!"1".equals(l192m01b.getCustType())) {
				continue;
			}

			String mainId = l192m01b.getMainId();
			String mainCustId = l192m01b.getMainCustId();
			String mainDupNo = l192m01b.getMainDupNo();

			// -----------------------------------第一區段資料開始，列印受檢單位，基準日，檢查日等基本資料------------------------------
			Map<String, String> values = reNewHashMapParams();
			values.put("ReportBean.column40", "section1");

			list.add(values);
			// -----------------------------------第一區段資料結束---------------------------------------------------------

			// -----------------------------------第二區段資料開始，列印借款人基本資料------------------------------
			values = reNewHashMapParams();

			values.put("ReportBean.column40", "section2");
			values.put("ReportBean.column01", l192m01b.getMainCustId());
			values.put("ReportBean.column02", l192m01b.getCustName());
			values.put("ReportBean.column03", l192m01b.getPosi());
			values.put(
					"ReportBean.column04",
					l192m01b.getIncomeAmt() == null ? "" : df.format(l192m01b
							.getIncomeAmt()));
			values.put("ReportBean.column05", l192m01b.gettTel());
			values.put("ReportBean.column06", l192m01b.gettAddr());
			values.put("ReportBean.column07", l192m01b.getCdQ1());
			values.put("ReportBean.column08", l192m01b.getCdQ2());
			list.add(values);
			// -----------------------------------第二區段資料結束---------------------------------------------------------

			// -----------------------------------第三區段資料開始，列印連保人基本資料------------------------------

			// 取得連保人資料
			List<L192M01B> l192m01bs2 = lms1915Service.getL192M01Bs(mainId,
					mainCustId, mainDupNo);

			values = reNewHashMapParams();
			values.put("ReportBean.column39", "first");

			for (L192M01B l192m01b2 : l192m01bs2) {
				values = values == null ? reNewHashMapParams() : values;
				values.put("ReportBean.column40", "part1");
				if ("2".equals(l192m01b2.getCustType())) {
					values.put("ReportBean.column09", l192m01b2.getCustId());
					values.put("ReportBean.column10", l192m01b2.getCustName());
					values.put(
							"ReportBean.column11",
							l192m01b2.getPosi() == null ? "" : l192m01b2
									.getPosi());

					values.put("ReportBean.column12",
							l192m01b2.getIncomeAmt() == null ? "" : l192m01b2
									.getIncomeAmt().toString());
				} else {
					continue;
				}
				list.add(values);
				values = null;
			}

			// -----------------------------------第三區段資料結束---------------------------------------------------------

			// -----------------------------------第四區段資料開始，列印申請內容資料------------------------------

			Set<L192S01A> l192s01as = l192m01b.getL192s01as();

			values = reNewHashMapParams();
			values.put("ReportBean.column39", "first");

			int count = 1;
			for (L192S01A l192s01a : l192s01as) {
				values = values == null ? reNewHashMapParams() : values;
				values.put("ReportBean.column40", "part2");
				values.put("ReportBean.column13", String.valueOf(count));
				values.put("ReportBean.column14", l192s01a.getAccNo());
				values.put(
						"ReportBean.column15",
						l192s01a.getQuotaAmt() == null ? "" : df
								.format(l192s01a.getQuotaAmt()));
				values.put(
						"ReportBean.column16",
						l192s01a.getBalAmt() == null ? "" : df.format(l192s01a
								.getBalAmt()));
				values.put(
						"ReportBean.column17",
						l192s01a.getAppDate() == null ? "" : sdf
								.format(l192s01a.getAppDate()));
				values.put(
						"ReportBean.column18",
						l192s01a.getSignDate() == null ? "" : sdf
								.format(l192s01a.getSignDate()));
				values.put(
						"ReportBean.column19",
						l192s01a.getUseDate() == null ? "" : sdf
								.format(l192s01a.getUseDate()));
				values.put(
						"ReportBean.column20",
						l192s01a.getFromDate() == null ? "" : sdf
								.format(l192s01a.getFromDate()));
				values.put(
						"ReportBean.column21",
						l192s01a.getEndDate() == null ? "" : sdf
								.format(l192s01a.getEndDate()));
				values.put("ReportBean.column22", l192s01a.getWay());
				values.put("ReportBean.column23", l192s01a.getAppr());

				list.add(values);
				values = null;
				count++;
			}

			// -----------------------------------第四區段資料結束---------------------------------------------------------

			// -----------------------------------第五區段資料開始，列印查核事項------------------------------
			values = reNewHashMapParams();
			L192M01E l192m01e = l192m01b.getL192m01e();
			if (l192m01e != null) {
				values.put("ReportBean.column40", "section3");
				values.put("ReportBean.column24", l192m01e.getUserItem1());
				values.put("ReportBean.column25", l192m01e.getUserItem2());
				values.put("ReportBean.column26", l192m01e.getUserItem3());
				values.put("ReportBean.column27", l192m01e.getCk1());
				values.put("ReportBean.column28", l192m01e.getCk2());
				values.put("ReportBean.column29", l192m01e.getCk3());
				values.put("ReportBean.column30", l192m01e.getCk4());
				values.put("ReportBean.column31", l192m01e.getCk5());
				values.put("ReportBean.column32", l192m01e.getCk6());
				values.put("ReportBean.column33", l192m01e.getCk7());
				values.put("ReportBean.column34", l192m01e.getUserCk1());
				values.put("ReportBean.column35", l192m01e.getUserCk2());
				values.put("ReportBean.column36", l192m01e.getUserCk3());

			}
			list.add(values);
			// -----------------------------------第五區段資料結束---------------------------------------------------------

			// -----------------------------------第六區段資料開始，列印查核事項------------------------------
			values = reNewHashMapParams();
			values.put("ReportBean.column40", "section4");
			list.add(values);
			// -----------------------------------第六區段資料結束---------------------------------------------------------

			// -----------------------------------第七區段資料開始，列印查核事項------------------------------
			values = reNewHashMapParams();
			values.put("ReportBean.column40", "section5");
			list.add(values);
			// -----------------------------------第七區段資料結束---------------------------------------------------------

		}
		rptGenerator.setRowsData(list); // set report Bean data

		Map<String, String> prompts = new HashMap<String, String>();
		prompts.put("ownBrId", meta.getOwnBrId());
		prompts.put("ownBrName", branchService.getBranchName(meta.getOwnBrId()));
		prompts.put(
				"checkBase",
				meta.getCheckBase() == null ? "" : sdf.format(meta
						.getCheckBase()));
		prompts.put(
				"checkDate",
				meta.getCheckDate() == null ? "" : sdf.format(meta
						.getCheckDate()));
		prompts.put("tNo", meta.getTNo());
		prompts.put("wpNo", meta.getWpNo());
		prompts.put("checkMan", meta.getCheckMan());
		prompts.put("leader", meta.getLeader());
		prompts.put("mtDoc", meta.getMtDoc());
		prompts.put("tAddr", meta.getTAddr());
		prompts.put("tTel", meta.getTTel());
		prompts.put("cdQ1", meta.getCdQ1());
		prompts.put("cdQ2", meta.getCdQ2());

		prompts.put("processComm", meta.getProcessComm());
		prompts.put("gist", meta.getGist());
		prompts.put("randomCode", meta.getRandomCode());

		prompts.put("balDate", balDate);
		prompts.put("custId", meta.getCustId());

		rptGenerator.setVariableData(prompts);
	}

	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/las/LMS1915R01_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		setReport001(rptGenerator, mainOid);
	}

	/**
	 * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
	 * 
	 * @return
	 */
	private Map<String, String> reNewHashMapParams() {
		Map<String, String> values = new HashMap<String, String>();
		for (int i = 1; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}

}
