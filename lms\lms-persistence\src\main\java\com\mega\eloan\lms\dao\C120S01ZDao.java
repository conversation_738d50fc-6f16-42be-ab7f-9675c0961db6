/* 
 * C120S01ZDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01Z;

/** 信貸集中徵信 **/
public interface C120S01ZDao extends IGenericDao<C120S01Z> {

	C120S01Z findByOid(String oid);
	
	List<C120S01Z> findByMainId(String mainId);
	
	List<C120S01Z> findByIndex01(String mainId, String custId);

	List<C120S01Z> findByMainIdCustIdDupNo(String mainId, String custId, String dupNo);
	C120S01Z findByUniqueKey(String mainId, String custId, String dupNo);
	
	int deleteByOid(String oid);
}