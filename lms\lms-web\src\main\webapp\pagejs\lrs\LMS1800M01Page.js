var initDfd = $.Deferred();
var _handler = "lms1800formhandler";
var grid_p = "grid_printL140M01A";
var grid_p_h = 340;
var print_url = "../../simple/FileProcessingService";
var print_map = {};
$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	
	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){
			
			// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button")) {
				var btnArr = [];
				btnArr.push("btn_addL180M01B");
				btnArr.push("btn_dcL180M01B");
				btnArr.push("btn_rsL180M01B");
				btnArr.push("btn_nrL180M01B");
				btnArr.push("btn_esL180M01B");
				btnArr.push("btn_random");
				btnArr.push("btnDcNReview");
				$.each(btnArr, function(idx, btnId){
					$("#"+btnId).addClass(" ui-state-disabled ").attr("disabled", "true");
				});
			}
			if(json.page=="01" || json.page=="03" ){
				//只在第 2 個 panel 才有grid 可供選擇
				$("#btn_printLatestL140M01A").addClass(" ui-state-disabled ").attr("disabled", "true");	
			}			
			
			tabForm.injectData(json);
			initDfd.resolve(json);	
	}});
	
	$("#"+grid_p).iGrid({
        handler: 'lms1800gridhandler',        
        height: grid_p_h,
        postData: {
        	parStrArr: '',
            formAction: "queryL140M01A"
        },
        rownumbers: true,
        needPager: false,        
        shrinkToFit: false,
        multiselect: true,        
        colModel: [
          {
            // "借款人統編",
            colHeader: i18n.lms1800m01['grid.custId'],name:'custId',align: "left",width: 85,sortable: false
          }, {
            // "借款人姓名",
            colHeader: i18n.lms1800m01['L180M01B.elfCName'],name: 'cName',align: "left",width: 130,sortable: false
          },{//"類別"
            colHeader: i18n.lms1800m01['grid.cntrNoType'], name:'rptNoDesc',align: "left",width: 85,sortable: false
          }, {//"額度序號",
            colHeader: i18n.lms1800m01['grid.cntrNo'], name:'cntrNo',align: "left",width: 85,sortable: false
          }, { name: 'rptNo', hidden: true}
          , { name: 'oid', hidden: true}  
          , { name: 'cntrCustid', hidden: true }
          , { name: 'cntrDupno', hidden: true }
        ] 
    });
	
	btnPanel.find("#btnSave").click(function(showMsg){
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				if(json.IncompleteMsg){
					API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
				}else{
					API.showMessage(i18n.def.saveSuccess);	
				}	
			}
        });
    }).end().find("#btnSend").click(function(){//呈主管
    	API.confirmMessage(i18n.def.confirmApply, function(result){
            if (result) {
            	saveAction().done(function(json){
        			genProjectNoAction().done(function(){
                    	flowAction({'decisionExpr':'呈主管'});
                    });
                });
        	}
    	});
    }).end().find("#btnAccept").click(function(){
    	flowAction({'decisionExpr':'核定'});
    }).end().find("#btnReturn").click(function(){
    	flowAction({'decisionExpr':'退回'});
    }).end().find("#btn_genBatchNo").click(function(){
    	genProjectNoAction().done(function(json){
    		tabForm.injectData(json);
    		
    		API.triggerOpener(); 
    		
    		if(json.page=="02"){
    			$("#gridview").trigger("reloadGrid");
    		}
    	});
    }).end().find("#btn_printLatestL140M01A").click(function(){
    	var oid_arr = gridGridSelectOidArr();
    	if(oid_arr.length==0){
    		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    		return;
    	}
    	printLatestL140M01A(oid_arr.join("|"));
    });    
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            ),                
            success: function(json){
            	API.triggerOpener(); window.close();
            }
        });
	}
	var genProjectNoAction = function(opts){
		return $.ajax({
			type: "POST",
	        handler: _handler,
	           data: { formAction: "genLrsProjectNo", mainOid: $("#mainOid").val() },
	           success: function(responseData){
	           }
		});
	}
});

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                ( opts||{} )
            ),                
            success: function(json){
            	tabForm.injectData(json);
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
        });
	}else{
		return $.Deferred();
	}
}

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});

function gridGridSelectOidArr(gridId){
    var oid_arr = [];
    if(gridId == "" || gridId == null){
        var rowId_arr = $("#gridview").getGridParam('selarrrow');
        for (var i = 0; i < rowId_arr.length; i++) {
            var data = $("#gridview").getRowData(rowId_arr[i]);
            oid_arr.push(data.oid);
        }
        var rowId_arrNR = $("#gridNReview").getGridParam('selarrrow');
        for (var i = 0; i < rowId_arrNR.length; i++) {
            var data = $("#gridNReview").getRowData(rowId_arr[i]);
            oid_arr.push(data.oid);
        }
    } else {
        var rowId_arr = $("#"+gridId).getGridParam('selarrrow');
        for (var i = 0; i < rowId_arr.length; i++) {
            var data = $("#"+gridId).getRowData(rowId_arr[i]);
            oid_arr.push(data.oid);
        }
    }
	return oid_arr;
}

function get_grid_p_items(){
	var rowId_arr = $("#"+grid_p).getGridParam('selarrrow');
	var oid_arr = [];
 	for (var i = 0; i < rowId_arr.length; i++) {
 		var data = $("#"+grid_p).getRowData(rowId_arr[i]);
 		oid_arr.push(data.rptNo+"^"+data.oid+"^"+data.cntrCustid+"^"+data.cntrDupno+"^"+data.cntrNo);    			
 	}
 	return oid_arr;
}
function printLatestL140M01A(oids){
	$.ajax({ type: "POST", handler: _handler, data:{ formAction: "getPrintL140M01AParam", 'oids': oids},
        success: function(json){
        	var my_dfd = $.Deferred();    	
        	if(json.notProc){
        		API.showPopMessage("", json.notProc,function(){
        			my_dfd.resolve();
  				});
        	}else{
        		my_dfd.resolve();
        	}

        	my_dfd.done(function(){
        		if(json.parStr){
            		$("#"+grid_p).jqGrid("setGridParam", {
                        postData: {
                        	parStrArr:json.parStr
                        },
                        search: true
                    }).trigger("reloadGrid");
            		
            		$("#div_printL140M01A").thickbox({ // 使用選取的內容進行彈窗
         		       title: "", width: 550, height: grid_p_h+140, align: "center", valign: "bottom", modal: false
         		       , i18n: $.extend({'my_print':'勾選分批列印', 'my_downloadZip':'整批下載'}, i18n.def),
         		       'readOnly':false,//打文件被 A 開啟中，若不指定 readOnly:false，則  my_downloadZip 不會出現
         		       'open': function(){
	                   		/*
	                   		 * 在 common.js 中，若 lockDoc 則會把 thickboxOptions.readOnly = true;
	                   		 */
	                   		if(thickboxOptions.readOnly){
	                   			tb_button(false);            			
	                   		}                   			   
         		       },
         	           buttons: {
         	               "my_print": function(){
         	            	  var oid_arr = get_grid_p_items();         	            	  
         	            	  if(oid_arr.length==0){
         	            		  API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
         	            		  return;
    	             	 	  }
         	            	  
         	            	  var target = [];
         	            	  var batchCnt = 10;
         	            	  if(true){
         	            		  var first_oid = oid_arr[0];
         	            		  var last_oid = oid_arr[oid_arr.length-1];
         	            		  var arr_len = oid_arr.length;
         	            		  var isBreak = false;
         	            		  for(var i = 0; isBreak==false; i++){
         	            			 var beg = i*batchCnt;
	         	            		 var end = (i+1)*batchCnt;
	         	            		 
	         	            		 if(end>=arr_len){
	         	            			 isBreak = true;
	         	            		 }
	         	            		 target.push( $( oid_arr ).slice( beg, end) );
         	            		  }
         	            		 if(true){
         	                    	var theQueue = $({});
         	                		var cnt = 0;
         	                		$.each(target,function(idx) {
         	                			theQueue.queue('myqueue', function(next) {
         	                				var current_arr = target[idx].get();
         	                				var target_window_id = $("#mainId").val()+"_blank_"+idx;
             	            					
         	                				nextBatchPrompt(arr_len, batchCnt, idx, current_arr[0], current_arr[current_arr.length-1], current_arr.length, first_oid, last_oid).done(function(){
         	                					run_rptservice(current_arr, target_window_id).done(function(){
         	                						delete print_map[target_window_id];
             	            						next();
             	            					});          	            					
             	            				});             	            				
         	                			});                                            			 
         	                		});
         	                		theQueue.dequeue('myqueue');
         	                    }
         	            	  }
         	            	    	                  	
         	               },
         	               "my_downloadZip": function(){
         	            	   var oid_arr = get_grid_p_items();         	            	  
         	            	   if(oid_arr.length==0){   	 		
         	            		   API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
         	            		   return;
    	             	 	   }
         	            	
         	            	  run_downloadZip(oid_arr);
        	               },
         	               "close": function(){
         	            	   $.thickbox.close();
         	               }
         	           }
            		});        		
            	}
        	});
        }
    });
}

function nextBatchPrompt(totalCnt, batchCnt, idx, next_first_oid, next_last_oid, next_cnt, first_oid, last_oid){
	var eq_first = next_first_oid==first_oid;
	var eq_last = next_last_oid==last_oid;
	
	var my_dfd = $.Deferred();
	if(eq_first){
		var desc = "全部"+totalCnt+"筆。將產生第1~"+(totalCnt<batchCnt?totalCnt:batchCnt)+"筆。";
		API.confirmMessage(desc, function(result){
            if (result) {
            	my_dfd.resolve();      	
        	}
    	});
	}else{
		var desc = "全部"+totalCnt+"筆。已產生第"+((idx-1)*batchCnt+1)+"~"+((idx)*batchCnt)+"筆。";
		if(eq_last){
			API.confirmMessage(desc+"是否要產生最後一批？", function(result){
	            if (result) {
	            	my_dfd.resolve();      	
	        	}
	    	});
		}else{
			API.confirmMessage(desc+"是否要產生下一批？", function(result){
	            if (result) {
	            	my_dfd.resolve();      	
	        	}
	    	});
		}
	}
	return my_dfd.promise();
}
function run_downloadZip(oid_arr){
	API.confirmMessage(i18n.lms1800m01['msg.downloadZip'], function(result){
        if (result) {
        	var my_timeout = 7200000;//ms
        	if(true){//先延長 timer，不然在處理過程中，會 timeout
        		timer_long_ajax_beg(my_timeout);	
        	}
        	tb_button(false);
        	$.ajax({
                handler: _handler, action: "callBatch", timeout: my_timeout, type: 'post',
                data: {'act':'downloadZip_cntrNoPdf'        	
                	, 'jq_timeout': (my_timeout/1000)                	
                	, 'rptOid': oid_arr.join("|")
                	, 'serviceName': "lms1201r01rptservice"
                	, 'mainOid': $("#mainOid").val() 
                	, 'mainDocStatus': $("#mainDocStatus").val()
                }, success: function(json_callBatch){
                	if(true){//恢復 timer
                		timer_long_ajax_end();
        			}
                	tb_button(true);
                	if(json_callBatch.r.response==="SUCCESS"){
                		//更新下載的連結
                		build_attch(json_callBatch);
                		
                		$.thickbox.close();
                		//---
                		API.showMessage(i18n.def.runSuccess);        		
                	}else{
                		API.showErrorMessage(json_callBatch.r.response);
                	}
        		}, error: function(xhr, status, e){
        			ilog.debug("downloadZip_cntrNoPdf:"+status);
        			
        			var postData = {
                		'mainOid': responseJSON.mainOid, 
                		'mainId': responseJSON.mainId,
                		'mainDocStatus': responseJSON.mainDocStatus
                	}            	
                	$.form.submit({ url: responseJSON.page, data:postData});
        		}
        		
            });    	
    	}
	});	
}

function run_rptservice(current_arr, target_window_id){
	var my_dfd = $.Deferred();    
	var cwin = window.open('', target_window_id); 
	print_map[target_window_id] = my_dfd;
	
	tb_button(false);;
	
	$.form.submit({
	     url: print_url,         	                     
	     target: target_window_id,
	     data: {
	     'targetWinId':target_window_id, 	 
		 'rptOid': current_arr.join("|"),
		 'fileDownloadName': "cntrNo.pdf",
		 'serviceName': "lms1201r01rptservice",
		 'isTwoPhase': 'Y'
	}});	
    return my_dfd.promise();
}

function childWin_complete(targetWinId){
	tb_button(true);
	if(targetWinId){
		print_map[targetWinId].resolve();	
	}
}
function tb_button(isOn){
	if(isOn){
		$(".TB_window").find("button[name!=close]:visible").removeClass(" ui-state-disabled ").removeAttr("disabled");		
	}else{
		$(".TB_window").find("button[name!=close]:visible").addClass(" ui-state-disabled ").attr("disabled", "true");
	}
}

function build_attch(passParam){
	$.each(passParam.attch, function(divId, arr){
		var dyna = [];
		{
			$.each(arr, function(idx, jsonItem) {
				var inProcess = false;
				if(jsonItem.flag!==null && Boolean(jsonItem.flag)){
					if(jsonItem.flag=="P"){
						inProcess = true;
					}
				}				
				dyna.push("<span class='"+(inProcess?"color-red":"linkDocFile")+"' oid='"+jsonItem.oid+"'>"
						+(inProcess?"產生中...請等候":jsonItem.srcFileName)+"</span>&nbsp;&nbsp;&nbsp;&nbsp;"
				+"("+jsonItem.uploadTime+")");					
			});	
		}					
		$("#"+divId).html(dyna.join("<br/>"));
		$("#"+divId).find('span.linkDocFile').click(function(){
			var oid = jQuery(this).attr("oid");
			
			$.capFileDownload({
		        handler:"simplefiledwnhandler",
		        data : {
		            fileOid:oid
		        }
			});				
		});
	});
}