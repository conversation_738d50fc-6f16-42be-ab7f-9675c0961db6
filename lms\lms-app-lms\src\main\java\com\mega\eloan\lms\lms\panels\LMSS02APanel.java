package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 借款人基本資料(海外消金)
 * </pre>
 * 
 * p.s.海外企金:LMSS02Panel
 */
public class LMSS02APanel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS02APanel(String id) {
		super(id);
	}

	public LMSS02APanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new LMSS02A_Panel("lmss02a_panel").processPanelData(model, params);
	}
}
