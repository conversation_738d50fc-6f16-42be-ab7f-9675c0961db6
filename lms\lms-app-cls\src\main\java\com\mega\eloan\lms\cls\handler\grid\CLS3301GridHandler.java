package com.mega.eloan.lms.cls.handler.grid;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.gwclient.IVRGwReqMessage;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.cls.service.CLS3301Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls3301gridhandler")
public class CLS3301GridHandler extends AbstractGridHandler {
	
	@Resource
	SysParameterService sysParameterService;
	
	@Resource
	CLS3301Service cls3301Service;
	
	public CapMapGridResult query(ISearch pageSetting, PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String custId = Util.trim(params.getString("custId"));
		String branCode = user.getUnitNo();
		String startDate = Util.trim(params.getString("fromDate")).replace("-", "");
		String endDate = Util.trim(params.getString("endDate")).replace("-", "");
		String lightId =user.getLightId();
		String userId =user.getUserId();

		//測試用
//		custId = "A123456789";
//		startDate = "20190601";
//		endDate = "20190723";
//		lightId = "ISEhfDAwNzYyNXxFTHxFODBGNjE5OUNDRjI0NzI3NEZDOENGRDgyRTMyRjFDQ3wxNDE4MjEyMzgwMzR3";
//		userId = "007625";
		
		IVRGwReqMessage req = new IVRGwReqMessage(custId,startDate, endDate, lightId, userId, branCode);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if(Util.notEquals(custId, "") && Util.notEquals(startDate, "") && Util.notEquals(endDate, ""))
		{
			list=cls3301Service.find(req);
		}
		
		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}
	
}
