/* 
 * L140MM5CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;
import tw.com.iisi.cap.dao.IGenericDao;
import com.mega.eloan.lms.model.L140MM5C;

/** eLoan電子文件維護作業資訊檔 **/
public interface L140MM5CDao extends IGenericDao<L140MM5C> {

	L140MM5C findByOid(String oid);
	
	List<L140MM5C> findByMainId(String mainId);

	List<L140MM5C> findByIndex01(String MAINID);
	
	List<L140MM5C> findByMainIdIsDelete(String mainId, String isDelete);
}