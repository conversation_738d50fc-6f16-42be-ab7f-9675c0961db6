/* 
 * C120S01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01C;


/** 個金償債能力檔 **/
public interface C120S01CDao extends IGenericDao<C120S01C> {

	C120S01C findByOid(String oid);

	List<C120S01C> findByMainId(String mainId);

	C120S01C findByUniqueKey(String mainId, String custId, String dupNo);

	List<C120S01C> findByIndex01(String mainId, String custId, String dupNo);
	
	List<C120S01C> findByCustIdDupId(String custId,String DupNo);
	
	int deleteByOid(String oid);
}