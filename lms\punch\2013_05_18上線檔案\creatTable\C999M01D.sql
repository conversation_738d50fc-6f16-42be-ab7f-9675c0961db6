---------------------------------------------------------
-- LMS.C999M01D 個金約據書項目描述檔
---------------------------------------------------------
--DROP TABLE LMS.C999M01D;
CREATE TABLE LMS.C999M01D (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	ITEMTYPE      CHAR(1)       not null,
	ITEMCONTENT   VARCHAR(3072),
	CREATOR       CHAR(6)      ,
	CREATE<PERSON><PERSON>    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C999M01D PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC999M01D01;
CREATE UNIQUE INDEX LMS.XC999M01D01 ON LMS.C999M01D   (MAINID, ITEMTYPE);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C999M01D IS '個金約據書項目描述檔';
COMMENT ON LMS.C999M01D (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	ITEMTYPE      IS '項目', 
	ITEMCONTENT   IS '說明', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
