var l120s24aGrid;
$(function() {
	lms1401s10PanelInit();
});

function lms1401s10PanelInit(){
	l120s24aGrid = $("#l120s24aGrid").iGrid({
		handler: 'lms1205gridhandler',
		height: 350,
		postData: {
			formAction: "queryL120s24aList"
		},
		rownumbers: true,
		multiselect : true,
		needPager: false,
		colModel: [{
			colHeader: i18n.lms1405s10["L120S24A.grid.cntrNo_s24a"],// 額度序號
			align: "center",
			width: 45, // 設定寬度
			name: 'cntrNo_s24a',
			formatter: 'click',
			onclick: loadS24ADoc
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.currentApplyCurr_s24a"],// 幣別
			align: "center",
			width: 20, // 設定寬度
			name: 'currentApplyCurr_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.currentApplyAmt_s24a"],// 現請額度(A)
			width: 25, // 設定寬度
			name: 'currentApplyAmt_s24a',
			sortable: false,
			align:"right"
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.ccf_s24a"],// CCF
			align: "left",
			width: 15, // 設定寬度
			name: 'ccf_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.specialFinRiskType_s24a"],// 特殊融資
			align: "center",
			width: 10, // 設定寬度
			name: 'specialFinRiskType_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.ccfAmt_s24a"],// 純表外之信用相當額(B)=(A)xCCF
			align: "right",
			width: 25, // 設定寬度
			name: 'ccfAmt_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.hasEstate_s24a"],// LTV法
			align: "center",
			width: 10, // 設定寬度
			name: 'hasEstate_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.isCBControl_s24a"],// 央行管制
			align: "center",
			width: 10, // 設定寬度
			name: 'isCBControl_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.beforeDeductRW_s24a"],// 抵減前風險權數
			align: "right",
			width: 15, // 設定寬度
			name: 'beforeDeductRW_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.totalDisCollAmt_s24a"],// 合格擔保品抵減金額(C)
			align: "right",
			width: 25, // 設定寬度
			name: 'totalDisCollAmt_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.calDisCollExposureAmt_s24a"],// 合格擔保品抵減後暴險額(D)=(A)-(C) 或 (B)-(C)
			align: "right",
			width: 28, // 設定寬度
			name: 'calDisCollExposureAmt_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.hasGutClass_s24a"],// 信保機構
			align: "center",
			width: 7, // 設定寬度
			name: 'hasGutClass_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.calHasGutDeptRWA_s24a"],// 信保部位風險性資產(E)
			align: "right",
			width: 25, // 設定寬度
			name: 'calHasGutDeptRWA_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.calNoGutRWA_s24a"],// 非信保部位風性資產(F)
			align: "right",
			width: 25, // 設定寬度
			name: 'calNoGutRWA_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.calDeductRWA_s24a"],// 抵減後風險性資產(G)=(E)+(F)
			align: "right",
			width: 25, // 設定寬度
			name: 'calDeductRWA_s24a'
		}, {
			colHeader: i18n.lms1405s10["L120S24A.grid.calDeductRW_s24a"],// 抵減後風險權數=(G)/(A)
			align: "right",
			width: 25, // 設定寬度
			name: 'calDeductRW_s24a'
		}, {
			colHeader: "oid",
			name: 'oid',
			hidden: true
		}, {
			colHeader: "versionDate_s24a",
			name: 'versionDate_s24a',
			hidden: true
		}, {
			// 當前最新的版本
			colHeader: "newestVersionDate",
			name: 'newestVersionDate',
			hidden: true
		}],
		ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
			var data = $("#l120s24aGrid").getRowData(rowid);
			loadS24ADoc(null, null, data);
		},
		loadComplete : function() {
			// 預設顯示新版本欄位
			var gridNowVersion = '20250101';
			var newestVersionDate = '20250101';
			// 從第一筆來判斷versionDate_s24a
			var row1Data = $("#l120s24aGrid").getRowData(1);
			if(row1Data != undefined && row1Data != null){
				// 第一筆有內容才判斷
				if(Object.keys(row1Data).length > 0){
					gridNowVersion = row1Data.versionDate_s24a;
					newestVersionDate = row1Data.newestVersionDate;
				}
			}
			// 這樣判斷是因為每次reload的時候一直show/hide會造成grid越長越胖
			if(gridNowVersion == '20250101'){
				$("#l120s24aGrid").showCol('specialFinRiskType_s24a');
				$("#L120S24AHint1Span").hide();
			}else if(gridNowVersion == '20220812'){
				// 如果版本是20220812，就不要顯示這個新欄位
				$("#l120s24aGrid").hideCol('specialFinRiskType_s24a');
				
				// 另外在畫面上出現紅字
				if(responseJSON.mainDocStatus =="01O" || responseJSON.mainDocStatus =="07O"  || 
						responseJSON.mainDocStatus =="01K" || responseJSON.mainDocStatus == "0EO" ){
					if(gridNowVersion != newestVersionDate){
						// 是在經辦可編輯的情況下，再顯示建議他換新版的字眼
						$("#L120S24AHint1Span").show();
					}else{
						$("#L120S24AHint1Span").hide();
					}
			    }
			}
			// 如果L120S24A沒資料，就什麼都不做
		}
		
	}).trigger("reloadGrid");
}


function loadS24ADoc(cellvalue, options, data){
	$.ajax({
		handler: 'lms1405s10formhandler',
		action: "queryL120s24a",
		data: {
			mainId: responseJSON.mainId,
			oid: data.oid }
		}).done(function(data){
			openS24ADoc(data);
	});
}

function openS24ADoc(data){
	$("#l120S24ADetailThickbox").empty();
	// 放在load之前才不會造成lms1401S10PageVXXXXXX讀不到
	// 將L120S24A.oid存進responseJSON讓後面存L120S24B.refOid_s24b時可以用
	responseJSON.openL120s24aOid = data.oid;
	$("#l120S24ADetailThickbox").load("../../lms/lms1405S10PageV"+data.versionDate_s24a,function(){
		if(data){
			L140S10Page.initL120s24aPage();// 先init初始化畫面
			$("#l120S24ADetailForm").setData(data);
			L140S10Page.triggerSelect();// select選項得自己觸發change
			L140S10Page.setHTMLData(data);// 等setData完才可以補一些畫面的值
			L140S10Page.readOnlyL120s24aPage();// 如果是整個畫面要readOnly，要等上面畫面都畫完在跑
		}
		
		// 這個超重要喔，沒有這個唯讀的時候會無法關閉thickbox
		thickboxOptions.customButton = [i18n.lms1405s10["LMS1401S10Form01.close"]];
		$("#l120S24ADetailThickbox").thickbox({
			title: i18n.lms1405s10["L120S24A.head1"] + ' ' + data.cntrNo_s24a + 
				' ' + i18n.lms1405s10["L120S24A.head2"],// 額度序號046111100082風險權數試算
			width: 900,
			height: 800,
			modal : true,
			buttons:  API.createJSON([{
				// 儲存
				key: i18n.lms1405s10["LMS1401S10Form01.saveData"],
				value: function(){
					if(!$("#l120S24ADetailForm").valid()){
						return false;
					}
					$.ajax({
						handler: 'lms1405s10formhandler',
						type: "POST",
						action : "saveL120s24a",
						dataType: "json",
						data:{
							mainId: responseJSON.mainId,
							oid: data.oid,
							l120S24ADetailForm: JSON.stringify($("#l120S24ADetailForm").serializeData()) }
						}).done(function(json) {
							$.thickbox.close();
							API.showMessage(i18n.def['saveSuccess'] + "<BR/>" + json.hint1);
							l120s24aGrid.trigger("reloadGrid");
					});
				}
			}, {
				// 離開
				key: i18n.lms1405s10["LMS1401S10Form01.close"],
				value: function(){
					$.thickbox.close();
					l120s24aGrid.trigger("reloadGrid");
				}
			}])
		});
	});
}

/**
 * 產生風險權數主檔
 */
function genL120S24A() {
	// 傳給後端建立主檔
	$.ajax({
		handler : 'lms1405s10formhandler',
		type : "POST",
		dataType : "json",
		action : "genL120s24a",
		data : {
			mainId : responseJSON.mainid }
		}).done(function(obj) {
			l120s24aGrid.trigger("reloadGrid");
	});	
}

/**
 * 寫回額度明細表
 */
function writeBackToL140m01a_s24a(){
	var rows = $("#l120s24aGrid").getGridParam('selarrrow');
	var data = [];
	
	if (rows == "") {// button.L120S24A.writeNonSelect=請先選擇需寫回之資料列
		return CommonAPI.showMessage(i18n.lms1405s10["button.L120S24A.writeNonSelect"]);
	}
	
	// button.L120S24A.writeConfirm=是否要寫回額度明細表風險權數欄位?
	CommonAPI.confirmMessage(i18n.lms1405s10["button.L120S24A.writeConfirm"], function(b){
		if (b) {
			for (var i in rows) {
				data.push($("#l120s24aGrid").getRowData(rows[i]).oid);
			}
			
			$.ajax({
				handler: 'lms1405s10formhandler',
				type: "POST",
				action : "writeBackToL140m01a",
				data: {
					mainId: responseJSON.mainId,
					oids: data }
				}).done(function(obj){
					$("#l120s24aGrid").trigger("reloadGrid");
			});
		}
	});
}

/**
 * 列印此頁
 */
function printPage_s24a(){
	$.form.submit({
		url: "../../simple/FileProcessingService",
		target: "_blank",
		data: {
			rptOid: "R47_simple" + "^^^^^",
			mainId: responseJSON.mainId,
			fileDownloadName: "lms1205r47_simple.pdf",
			serviceName: "lms1205r01rptservice"
		}
	});
}

/**
 * 刪除
 * @returns
 */
function delete_s24a(){
	var rows = $("#l120s24aGrid").getGridParam('selarrrow');
	var data = [];
	
	if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
		return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	}
	
	// confirmDelete=是否確定刪除?
	CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
		if (b) {
			for (var i in rows) {
				data.push($("#l120s24aGrid").getRowData(rows[i]).oid);
			}
			
			$.ajax({
				handler: 'lms1405s10formhandler',
				type: "POST",
				action : "deleteL120s24as",
				data: {
					oids: data }
				}).done(function(obj){
					$("#l120s24aGrid").trigger("reloadGrid");
			});
		}
	});
}

/**
 * 列印試算明細
 */
function printL120S24A_s24a(){
	$.form.submit({
		url: "../../simple/FileProcessingService",
		target: "_blank",
		data: {
			rptOid: "R47" + "^^^^^",
			mainId: responseJSON.mainId,
			fileDownloadName: "lms1205r47_simple.pdf",
			serviceName: "lms1205r01rptservice"
		}
	});
}


function checkReadonly_s24a(){
	// var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    // if (auth.readOnly || _openerLockDoc == "1") {
	// 用responseJSON.Auth.readOnly，在切換到海外的額度明細表頁籤的時候，會被壓成true...
	if (inits.toreadOnly || _openerLockDoc == "1") {
        return true;
    }
    return false;
}

initDfd.done(function(auth) {
	if (checkReadonly_s24a() || thickboxOptions.readOnly) {
        $("#LMS1401S10Form01").find("button:not(.forview)").hide();
    }
});