package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF495;
import com.mega.eloan.lms.mfaloan.service.MisELF495Service;

/**
 * <pre>
 * 企金覆審額度檔
 * </pre>
 * 
 */
@Service
public class MisELF495ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF495Service {

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	@Override
	public Map<String, Object> selStatsDataByBranch_lrDate(
			String elf495_branch, String elf495_lrdate_s,
			String elf495_lrdate_e, String ctlType) {
		return this.getJdbc().queryForMap(
				"ELF495.selStatsDataByBranch_lrDate",
				new String[] { elf495_branch, elf495_lrdate_s, elf495_lrdate_e,
						ctlType, elf495_branch, elf495_lrdate_s,
						elf495_lrdate_e, ctlType });
	}

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param elf495_branch
	 * @param elf495_lrdate_s
	 * @param elf495_lrdate_e
	 * @param ctlType
	 * @return
	 */
	@Override
	public Map<String, Object> selStatsDataByBranch_lrDate_By_ApprId(
			String elf495_branch, String elf495_lrdate_s,
			String elf495_lrdate_e, String ctlType, String elf495_apprId) {
		return this.getJdbc().queryForMap(
				"ELF495.selStatsDataByBranch_lrDate_By_ApprId",
				new String[] { elf495_apprId, elf495_branch, elf495_lrdate_s,
						elf495_lrdate_e, ctlType, elf495_apprId, elf495_branch,
						elf495_lrdate_s, elf495_lrdate_e, ctlType });
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	@Override
	public List<Map<String, Object>> findWithELF411(String branch,
			String custId, String dupNo, String sELF411_DATAYM,
			String sELF411_DATAYM_CM, String ctlType) {
		return this.getJdbc().queryForListWithMax(
				"ELF495.findWithELF411",
				new String[] { sELF411_DATAYM, branch, custId, dupNo, branch,
						custId, dupNo, sELF411_DATAYM_CM, ctlType });
	}

	private List<ELF495> toELF495(List<Map<String, Object>> rowData) {
		List<ELF495> list = new ArrayList<ELF495>();
		for (Map<String, Object> row : rowData) {
			ELF495 model = new ELF495();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	@Override
	public List<ELF495> findBy_FLMS180R11(String branch, String custId,
			String dupNo, String cntrNo1, String cntrNo2, Date elf495_lrdate,
			String ctlType) {

		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"ELF495.FLMS180R11",
				new String[] { branch, cntrNo1, cntrNo2, custId, dupNo,
						Util.trim(TWNDate.toAD(elf495_lrdate)), ctlType });
		return toELF495(rowData);
	}
}
