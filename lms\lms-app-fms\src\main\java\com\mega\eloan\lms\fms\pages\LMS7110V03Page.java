package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 停權解除維護(已覆核)
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/fms/lms7110v03")
public class LMS7110V03Page extends AbstractEloanInnerView {

	public LMS7110V03Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.授管處_停權已覆核);		
		// 加上Button
		ArrayList<EloanPageFragment> btns = new ArrayList<>();
		btns.add(LmsButtonEnum.View);
		addToButtonPanel(model, btns);
		renderJsI18N(LMS7110V03Page.class);
	}// ;

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS7110V03Page.js" };
	}
}
