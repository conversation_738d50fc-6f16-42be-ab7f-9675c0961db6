package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1220BarcodeR01RptService;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * rpt報表service程式
 * </pre>
 * 
 * @since 2014/05/30
 * <AUTHOR>
 * @version <ul>
 *          <li>
 *          </ul>
 */
@Service("cls1220barcoder01rptservice")
public class CLS1220BarcodeR01RptServiceImpl implements FileDownloadService, CLS1220BarcodeR01RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1220BarcodeR01RptServiceImpl.class);
	@Resource
	CLS1220Service service;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	BranchService branchService;

	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			if (baos == null) {
				return null;
			} else {
				return baos.toByteArray();
			}
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public OutputStream generateReport(PageParameters params) throws FileNotFoundException, IOException, Exception {

		OutputStream outputStream = null;
		List<InputStream> list = new LinkedList<InputStream>();

		try {
			// 依主借人、各個從債務人分別列印PDF後再合併
			// 1.主借人
			String mainId = params.getString(EloanConstants.MAIN_ID);
			C122M01A meta = service.getC122M01A_byMainId(mainId);
			OutputStream mainOutputStream = getOutputStream(meta, null);
			list.add(new ByteArrayInputStream(((ByteArrayOutputStream) mainOutputStream).toByteArray()));
			// 2.各個從債務人
			List<C122M01A> relateCaseC122M01As = service.findMetaApplyKind_relateCase(meta.getApplyKind(),
					meta.getPloanCaseId());
			for (C122M01A relateCaseC122M01A : relateCaseC122M01As) {
				OutputStream relOutputStream = getOutputStream(meta, relateCaseC122M01A);
				list.add(new ByteArrayInputStream(((ByteArrayOutputStream) relOutputStream).toByteArray()));
			}
			if (list != null && list.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(list, outputStream);
			} else {
				throw new CapMessageException("無PDF檔案資料，請改用一鍵列印HTML", this.getClass());
			}

		} finally {
		}

		return outputStream;
	}

	/**
	 * 取得PDF outputStream
	 * 
	 * @param meta
	 * @param relateCaseC122M01A
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	private OutputStream getOutputStream(C122M01A meta, C122M01A relateCaseC122M01A) throws FileNotFoundException,
			IOException, Exception {
		OutputStream outputStream = null;
		Locale locale = null;
		Map<String, String> rptVariableMap = null;
		try {
			locale = LMSUtil.getLocale();
			ReportGenerator generator = new ReportGenerator("report/cls/CLS1220BarcodeR01_" + locale.toString()
					+ ".rpt");
			rptVariableMap = new LinkedHashMap<String, String>();
			rptVariableMap.put("branch",
					"兆豐銀行 - " + meta.getOwnBrId() + " " + branchService.getBranchName(meta.getOwnBrId()));
			generator.setVariableData(rptVariableMap);
			List<Map<String, String>> rowsDatas = new ArrayList<Map<String, String>>();
			rowsDatas = getRowsDatas(meta, relateCaseC122M01A);
			generator.setRowsData(rowsDatas);
			LOGGER.info("into generateReport");
			outputStream = generator.generateReport();
			LOGGER.info("exit generateReport");
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	/**
	 * 設定CommonBean1
	 * 
	 * @param params
	 * @return
	 */
	private List<Map<String, String>> getRowsDatas(C122M01A meta, C122M01A relateCaseC122M01A) {
		List<Map<String, String>> rowsDatas = new ArrayList<Map<String, String>>();
		List<Map<String, String>> textBarcodeList = getTextAndBarcodeCode(meta, relateCaseC122M01A);
		for (Map<String, String> textBarcodeMap : textBarcodeList) {
			Map<String, String> map = new HashMap<String, String>();
			map.put("CommonBean1.field01", textBarcodeMap.get("text"));
			map.put("CommonBean1.field02", textBarcodeMap.get("barcode"));
			rowsDatas.add(map);
		}
		return rowsDatas;
	}

	/**
	 * 取得BARCODE資料LIST
	 * 
	 * @param meta
	 * @param relateCaseC122M01A
	 * @return
	 */
	private List<Map<String, String>> getTextAndBarcodeCode(C122M01A meta, C122M01A relateCaseC122M01A) {
		List<Map<String, String>> datas = new ArrayList<Map<String, String>>();
		Map<String, String> map = new HashMap<String, String>();
		// 共同借款人 = "C";
		// 連帶保證人 = "G";
		// 擔保品提供人 = "S";
		// ㄧ般保證人 = "N";
		Map<String, String> ploan_casePosMap = codeTypeService.findByCodeType("ploan_casePos");
		// 文件標題判斷relateCaseC122M01A是否為NULL產生不同的的文字
		// 兆豐銀行貸款文件(主借款人-A123456789)
		// 兆豐銀行貸款文件(保證人-A123456789)
		String pageTitleText = "";
		if (relateCaseC122M01A == null) {
			pageTitleText = "兆豐銀行貸款文件(主借款人-" + meta.getCustId() + ")";
		} else {
			String posDESC = ploan_casePosMap.get(relateCaseC122M01A.getPloanCasePos());
			pageTitleText = "兆豐銀行貸款文件(" + posDESC + "-" + relateCaseC122M01A.getCustId() + ")";
		}
		map = new HashMap<String, String>();
		map.put("text", pageTitleText);
		// 條碼
		map.put("barcode", "ITI01400");
		datas.add(map);

		// 案號：PA20220401038507
		map = new HashMap<String, String>();
		map.put("text", "案號：" + meta.getPloanCaseNo());
		// 案號條碼
		map.put("barcode", meta.getPloanCaseNo());
		datas.add(map);

		// 申請日期：20220401
		map = new HashMap<String, String>();
		map.put("text", "申請日期：" + CapDate.formatDate(meta.getApplyTS(), "yyyyMMdd"));
		// 申請日期條碼
		map.put("barcode", CapDate.formatDate(meta.getApplyTS(), "yyyyMMdd"));
		datas.add(map);

		// 主借款人A123456789
		map = new HashMap<String, String>();
		map.put("text", "主借款人：" + meta.getCustId());
		// 主借款人條碼0-A123456789
		map.put("barcode", "0-" + meta.getCustId());
		datas.add(map);

		if (relateCaseC122M01A == null) {
			// 主借款人部分額外再加一個主借款人條碼1-A123456789
			// 主借款人A123456789
			map = new HashMap<String, String>();
			map.put("text", "主借款人：" + meta.getCustId());
			// 主借款人條碼0-A123456789
			map.put("barcode", "1-" + meta.getCustId());
			datas.add(map);
		} else {
			// 關係人條碼
			String posDESC = ploan_casePosMap.get(relateCaseC122M01A.getPloanCasePos());
			// 文字
			map = new HashMap<String, String>();
			map.put("text", posDESC + "：" + relateCaseC122M01A.getCustId());
			// 條碼
			map.put("barcode", "1-" + relateCaseC122M01A.getCustId());
			datas.add(map);
		}
		return datas;
	}

}
