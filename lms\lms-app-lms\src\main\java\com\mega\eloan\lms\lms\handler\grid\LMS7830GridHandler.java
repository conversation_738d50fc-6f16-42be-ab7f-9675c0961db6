/* 
 *  LMS7820GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.grid;

import java.util.Map;

import javax.annotation.Resource;


import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lms.service.LMS7830Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 簽報案件查詢
 * </pre>
 * 
 * @since 2011/12/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/12,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7830gridhandler")
public class LMS7830GridHandler extends AbstractGridHandler {

	@Resource
	LMS7830Service lms7830Service;

	/**
	 * 查詢特殊登錄案件紀錄表外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryL783m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.trim(params.getString(UtilConstants.Field.CUSTID));
		String dupNo = Util.trim(params.getString(UtilConstants.Field.DUPNO));

		// 測試資料
		// Page<Map<String, Object>> page = lms7830Service.queryData("#0070012",
		// "1");

		Page<Map<String, Object>> page = lms7830Service
				.queryData(custId, dupNo,pageSetting);

		return new CapMapGridResult(page.getContent(), page.getTotalRow());

	}
}
