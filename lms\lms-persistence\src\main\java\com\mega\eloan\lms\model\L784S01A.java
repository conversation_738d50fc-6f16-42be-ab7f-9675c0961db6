/* 
 * L784S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 已敘做授信案件明細 **/
@Entity
@Table(name="L784S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L784S01A extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 統一編號<p/>
	 * L140M01A.custId
	 */
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * 分行<p/>
	 * L120M01A.CASEBRID
	 */
	@Column(name="BRID", length=3, columnDefinition="CHAR(3)")
	private String brId;

	/** 
	 * 統編重複碼<p/>
	 * L140M01A.dupNo
	 */
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 額度序號<p/>
	 * L140M01A.cntrNo
	 */
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 
	 * 戶名<p/>
	 * L140M01A.custName
	 */
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 
	 * 核定日<p/>
	 * L120M01A.endDate
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;

	/** 
	 * 授信科目<p/>
	 * L140M01A.lnSubject<br/>
	 *  (代碼) xx|xx| …<br/>
	 *  ※顯示時取第一筆顯示ＸＸＸＸ等99筆
	 */
	@Column(name="LNSUBJECT", length=1536, columnDefinition="VARCHAR(1536)")
	private String lnSubject;

	/** 
	 * 額度(幣別)<p/>
	 * L140M01A.currentApplyCurr
	 */
	@Column(name="CURRENTAPPLYCURR", length=3, columnDefinition="CHAR(3)")
	private String currentApplyCurr;

	/** 
	 * 額度(金額)<p/>
	 * L140M01A.currentApplyAmt
	 */
	@Column(name="CURRENTAPPLYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/** 
	 * 期間(代碼)<p/>
	 * L140M01A.useDeadline<br/>
	 *  (代碼)<br/>
	 *  0.不再動用<br/>
	 *  1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 *  2.自核准日起MM個月<br/>
	 *  3.自簽約日起MM個月<br/>
	 *  4.自首次動用日起MM個月<br/>
	 *  5.其他
	 */
	@Column(name="USEDEADLINE", length=1, columnDefinition="CHAR(1)")
	private String useDeadline;

	/** 
	 * 期間(文字)<p/>
	 * L140M01A.desp1
	 */
	@Column(name="DESP1", length=900, columnDefinition="VARCHAR(900)")
	private String desp1;

	/** 
	 * 備註_敘作續約或變更條件<p/>
	 * L140M01A.property<br/>
	 *  (代碼)<br/>
	 *  ※顯示時代碼須轉換為文字
	 */
	@Column(name="PROPERTY", length=30, columnDefinition="VARCHAR(30)")
	private String property;

	/** 
	 * 備註_核准人<p/>
	 * L120M01F.STAFFNO<br/>
	 *  (代碼)<br/>
	 *  ※顯示時代碼須轉換為文字
	 */
	@Column(name="STAFFNO", length=6, columnDefinition="CHAR(6)")
	private String staffNo;

	/** 
	 * 報表亂碼<p/>
	 * L140M01A.randomCode
	 */
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 備查日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="HQCHECKDATE", columnDefinition="DATE")
	private Date hqCheckDate;

	/** 
	 * 備註<p/>
	 * 100個全型字
	 */
	@Column(name="HQCHECKMEMO", length=300, columnDefinition="VARCHAR(300)")
	private String hqCheckMemo;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** L120M01A_MainId **/
	@Column(name="L120M01A_MainId", columnDefinition="CHAR(32)")
	private String L120M01A_MainId;
	
	/** L120M01A_MainId **/
	@Column(name="L140M01A_MainId", columnDefinition="CHAR(32)")
	private String L140M01A_MainId;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得統一編號<p/>
	 * L140M01A.custId
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定統一編號<p/>
	 *  L140M01A.custId
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得統編重複碼<p/>
	 * L140M01A.dupNo
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定統編重複碼<p/>
	 *  L140M01A.dupNo
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得額度序號<p/>
	 * L140M01A.cntrNo
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}
	/**
	 *  設定額度序號<p/>
	 *  L140M01A.cntrNo
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 
	 * 取得戶名<p/>
	 * L140M01A.custName
	 */
	public String getCustName() {
		return this.custName;
	}
	/**
	 *  設定戶名<p/>
	 *  L140M01A.custName
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得核定日<p/>
	 * L120M01A.endDate
	 */
	public Date getEndDate() {
		return this.endDate;
	}
	/**
	 *  設定核定日<p/>
	 *  L120M01A.endDate
	 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 
	 * 取得授信科目<p/>
	 * L140M01A.lnSubject<br/>
	 *  (代碼) xx|xx| …<br/>
	 *  ※顯示時取第一筆顯示ＸＸＸＸ等99筆
	 */
	public String getLnSubject() {
		return this.lnSubject;
	}
	/**
	 *  設定授信科目<p/>
	 *  L140M01A.lnSubject<br/>
	 *  (代碼) xx|xx| …<br/>
	 *  ※顯示時取第一筆顯示ＸＸＸＸ等99筆
	 **/
	public void setLnSubject(String value) {
		this.lnSubject = value;
	}

	/** 
	 * 取得額度(幣別)<p/>
	 * L140M01A.currentApplyCurr
	 */
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}
	/**
	 *  設定額度(幣別)<p/>
	 *  L140M01A.currentApplyCurr
	 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/** 
	 * 取得額度(金額)<p/>
	 * L140M01A.currentApplyAmt
	 */
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}
	/**
	 *  設定額度(金額)<p/>
	 *  L140M01A.currentApplyAmt
	 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/** 
	 * 取得期間(代碼)<p/>
	 * L140M01A.useDeadline<br/>
	 *  (代碼)<br/>
	 *  0.不再動用<br/>
	 *  1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 *  2.自核准日起MM個月<br/>
	 *  3.自簽約日起MM個月<br/>
	 *  4.自首次動用日起MM個月<br/>
	 *  5.其他
	 */
	public String getUseDeadline() {
		return this.useDeadline;
	}
	/**
	 *  設定期間(代碼)<p/>
	 *  L140M01A.useDeadline<br/>
	 *  (代碼)<br/>
	 *  0.不再動用<br/>
	 *  1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 *  2.自核准日起MM個月<br/>
	 *  3.自簽約日起MM個月<br/>
	 *  4.自首次動用日起MM個月<br/>
	 *  5.其他
	 **/
	public void setUseDeadline(String value) {
		this.useDeadline = value;
	}

	/** 
	 * 取得期間(文字)<p/>
	 * L140M01A.desp1
	 */
	public String getDesp1() {
		return this.desp1;
	}
	/**
	 *  設定期間(文字)<p/>
	 *  L140M01A.desp1
	 **/
	public void setDesp1(String value) {
		this.desp1 = value;
	}

	/** 
	 * 取得備註_敘作續約或變更條件<p/>
	 * L140M01A.property<br/>
	 *  (代碼)<br/>
	 *  ※顯示時代碼須轉換為文字
	 */
	public String getProperty() {
		return this.property;
	}
	/**
	 *  設定備註_敘作續約或變更條件<p/>
	 *  L140M01A.property<br/>
	 *  (代碼)<br/>
	 *  ※顯示時代碼須轉換為文字
	 **/
	public void setProperty(String value) {
		this.property = value;
	}

	/** 
	 * 取得備註_核准人<p/>
	 * L120M01F.STAFFNO<br/>
	 *  (代碼)<br/>
	 *  ※顯示時代碼須轉換為文字
	 */
	public String getStaffNo() {
		return this.staffNo;
	}
	/**
	 *  設定備註_核准人<p/>
	 *  L120M01F.STAFFNO<br/>
	 *  (代碼)<br/>
	 *  ※顯示時代碼須轉換為文字
	 **/
	public void setStaffNo(String value) {
		this.staffNo = value;
	}

	/** 
	 * 取得報表亂碼<p/>
	 * L140M01A.randomCode
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定報表亂碼<p/>
	 *  L140M01A.randomCode
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得備查日期 **/
	public Date getHqCheckDate() {
		return this.hqCheckDate;
	}
	/** 設定備查日期 **/
	public void setHqCheckDate(Date value) {
		this.hqCheckDate = value;
	}

	/** 
	 * 取得備註<p/>
	 * 100個全型字
	 */
	public String getHqCheckMemo() {
		return this.hqCheckMemo;
	}
	/**
	 *  設定備註<p/>
	 *  100個全型字
	 **/
	public void setHqCheckMemo(String value) {
		this.hqCheckMemo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	/**
	 * @param brId the brId to set
	 */
	public void setBrId(String brId) {
		this.brId = brId;
	}
	/**
	 * @return the brId
	 */
	public String getBrId() {
		return brId;
	}
	
	/**
	 * @param setL120M01A_MainId the setL120M01A_MainId to set
	 */
	public void setL120M01A_MainId(String L120M01A_MainId) {
		this.L120M01A_MainId = L120M01A_MainId;
	}
	/**
	 * @return the setL120M01A_MainId
	 */
	public String getL120M01A_MainId() {
		return L120M01A_MainId;
	}
	
	/**
	 * @param setL140M01A_MainId the setL140M01A_MainId to set
	 */
	public void setL140M01A_MainId(String L140M01A_MainId) {
		this.L140M01A_MainId = L140M01A_MainId;
	}
	/**
	 * @return the setL140M01A_MainId
	 */
	public String getL140M01A_MainId() {
		return L140M01A_MainId;
	}
}
