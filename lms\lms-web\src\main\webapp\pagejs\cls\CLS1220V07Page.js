$(function(){

    var grid = $("#gridview").iGrid({
        handler: 'cls1221gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryView_B",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "applyStatus|applyTS",
        sortorder: "asc|desc",
        multiselect: false, 
    	colModel : [
    	  {name : 'oid', hidden : true}
    	 ,{name : 'mainId',hidden : true}
    	 ,{name : 'docStatus',hidden : true}
    	 ,{name : 'ownBrId',hidden : true}
    	,{
			colHeader : i18n.cls1220m03['C122M01A.custId'], 
			width : 110, //設定寬度
			sortable : true, //是否允許排序
			name : 'custId', //身分證統編
			formatter: 'click',
			onclick : openDoc		
    	},{
			colHeader : i18n.cls1220m03['C122M01A.dupNo'],
			align : "left",
			width : 30, //設定寬度
			sortable : false, //是否允許排序
			name : 'dupNo' //col
		},{
			colHeader : i18n.cls1220m03['C122M01A.custName'],
			align : "left",
			width : 130, //設定寬度
			sortable : true, //是否允許排序
			name : 'custName' //col
		},{
			colHeader : i18n.cls1220m03['C122M01A.applyTS'],
			align : "left",
			width : 110, //設定寬度		
			name : 'applyTS'
		},{
			colHeader : i18n.cls1220m03['C122M01A.agreeQueryEJ'],
			align : "left",
			width : 100, 		
			name : 'agreeQueryEJ'
		},{
			colHeader : i18n.cls1220m03['C122M01A.statFlag'],
			align : "left",
			width : 100, 		
			name : 'statFlag'
		},{
			colHeader : i18n.cls1220m03['label.notifyMemo'],
			align : "left",
			width : 270, //設定寬度		
			name : 'notifyMemo'
		},{
			colHeader : i18n.cls1220m03['C122M01A.updater'],
			align : "left",
			width : 100, //設定寬度
			sortable : false, //是否允許排序
			name : 'updater' //col.id	
		},{
			colHeader : i18n.cls1220m03['C122M01A.updateTime'],
			align : "left",
			width : 110, //設定寬度
			sortable : false, //是否允許排序
			name : 'updateTime'//col.id
		}]				
	});
   
    /**
     * 回傳 yyyy-MM-dd
     * @param {Object} n_month
     */
    function getBefore_N_MonthDate(n_month){
        var sysdate = CommonAPI.getToday().split("-");
        var tDate = new Date(sysdate[0], sysdate[1] - 1, sysdate[2]);
        tDate.setMonth(tDate.getMonth() - n_month);
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate();
    }
    
    function openDoc(cellvalue, options, rowObject){
    	$.form.submit({
			url : '../lms/cls1220m03/01',
			data : {
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                mainDocStatus: rowObject.docStatus
            },
            target: rowObject.oid
		});
    };
	
    $("#buttonPanel").find('#btnView').click(function(){
		var selrow = grid.getGridParam('selrow');
        if (selrow) {
            openDoc('', '', grid.getRowData(selrow));
        }
        else {
            CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
        }	
    }).end().find("#btnQueryCustLoanRecord").click(function(){//查詢客戶申貸記錄
    	var _id = "div_applyKindB_History";
    	var _form = "div_applyKindB_History_form";
		var grid_id = "grid_applyKindB_History";
		
		//clear data
		$("#"+_form).reset();
		var my_post_data = get_param_grid_history('N');
		
		if($("#"+grid_id+".ui-jqgrid-btable").length >0){
			$("#"+grid_id).jqGrid("setGridParam", {
				postData : my_post_data,
				search : true
			}).trigger("reloadGrid");	        		
		}else{
			$("#"+grid_id).iGrid({
				handler : 'cls1221gridhandler',
				height : 160,
				divWidth: 0,
				postData : my_post_data,			
				colModel : [ {
		            colHeader: i18n.cls1220m03['C122M01A.custId'],
		            align: "left", width: 60, sortable:true, name: 'custId'
				}, {
		            colHeader: i18n.cls1220m03['C122M01A.custName'],
		            align: "left", width: 120, sortable: false, name: 'custName'
				}, {
		            colHeader: i18n.cls1220m03['C122M01A.applyTS'],
		            align: "left", width: 70, sortable: false, name: 'applyTS'
				}, {
		        	colHeader: i18n.cls1220m03['grid.ownBrId'],
		            align: "left", width: 100, sortable: false, name: 'ownBrId'
		       } ]
			});
		}    
		if(true){
			$("#"+_id+" #div_applyKindB_History_label_custId").val(i18n.cls1220m03['C122M01A.custId']);
		}		
		
		$("#"+_id).thickbox({
	        title: '',
	        width: 650, height: 400, align: 'center', valign: 'bottom', modal: false,
	        buttons: {
	            "close": function(){
	               $.thickbox.close();
	            }
	        }
	    });
    }).end().find("#btnCaseToChange").click(function(){
    	var grid_chose_data = grid.getSingleData();
		if (grid_chose_data){
			CommonAPI.showAllBranch({
		         btnAction: function(a, rtn){
		        	 MegaApi.confirmMessage("是否將"+grid_chose_data.custId+" "+grid_chose_data.custName 
		        			 +" 由 "+grid_chose_data.ownBrId+" 改分派至 "+rtn.brNo +" ？", function(action){
							if (action){
								$.thickbox.close();
				            	
				            	$.ajax({handler : 'cls1220m03formhandler',
										action : 'changeOwnBrId',
										data : {'mainOid':grid_chose_data.oid, 'newBrNo':rtn.brNo}
				            	}).done(function(json){
									grid.trigger("reloadGrid");
								}); 
							}
					}); 
		        	
	            }
	        });
        }else{
//        	CommonAPI.showMessage(i18n.def['grid.selrow']);
        }
    }).end().find("#btnCreateExl").click(function(){

   	 	var _id = "_div_CLS1220V07_btnCreateExl";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");		
			dyna.push("<form id='"+_form+"'>");
			
			/* var submenu = {'1':"線上進件2.0客戶在2020-05-22後線上同意查詢聯徵清單" 
					, '2':"產出「臨櫃進件已簽報」與「線上進件2.0之後」清單"
					, '3':"產出「已撥款未建擔保品」、「已建擔保品尚未撥款」與「已建額度尚未撥款」清單"
					 }; */
			var submenu = { '2b':"產出2021-06之後「臨櫃進件已簽報」與「線上進件」清單"
				, '3b':"產出2021-06之後「已撥款未建擔保品」、「已建擔保品尚未撥款」與「已建額度尚未撥款」清單"
			};
			
			build_submenu(dyna, 'decision_btnProduceExcel', submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: "", width: 750, height: 200, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	        buttons: {
	             "sure": function(){
	                 if (!$("#"+_form).valid()) {
	                     return;
	                 }
	                 var mode = "0";
	                 $.thickbox.close();
	                 mode = $("#"+_form).find("[name='decision_btnProduceExcel']:checked").val();
	             	
	                 if(true){
	                	 $.form.submit({
                         	url: __ajaxHandler,
                      		target : "_blank",
                      		data : {
                      			_pa : 'lmsdownloadformhandler',
                      			'mode': mode,
                                'fileDownloadName' : 'data_'+mode+'.xls',
                      			'serviceName' : "cls1220r07rptservcie"
                      		}
                      	 });
                      	 $.thickbox.close();
	                	 
	                 }	             	             
	             },
	             "cancel": function(){
	             	$.thickbox.close();
	             }
	         }
	    });
    }).end().find("#btnFilter").click(function(){    	
    	var _id = "_div_cls1220v07_b";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' width='100%' >");
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m03['C122M01A.custId']+"</td>");
			dyna.push("	  <td><input type='text' id='custId' name='custId' maxlength='10' /></td>");
			dyna.push("	</tr>");
			dyna.push("	<tr>");			
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m03['C122M01A.applyTS']+"</td>");
			dyna.push("	  <td>");
			dyna.push("	   <input type='text' id='applyTS_beg' name='applyTS_beg' maxlength='10' class='date' />");
			dyna.push("	 ~ <input type='text' id='applyTS_end' name='applyTS_end' maxlength='10' class='date' />");
			dyna.push("	  </td>");
			dyna.push("	</tr>");
			dyna.push("	<tr>");
			dyna.push("	  <td class='hd2' width='30%' nowrap>"+i18n.cls1220m03['C122M01A.statFlag']+"</td>");
			dyna.push("	  <td><select id='statFlag' name='statFlag'> ");
			dyna.push("	  <option value=''>"+i18n.def.comboSpace+"</option>");
			dyna.push("	  <option value='0'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.0']+"</option>");
			dyna.push("	  <option value='1'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.1']+"</option>");
			dyna.push("	  <option value='2'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.2']+"</option>");
			dyna.push("	  <option value='3'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.3']+"</option>");
			dyna.push("	  <option value='4'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.4']+"</option>");
			dyna.push("	  <option value='A'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.A']+"</option>");
			dyna.push("	  <option value='B'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.B']+"</option>");
			dyna.push("	  <option value='C'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.C']+"</option>");
			dyna.push("	  <option value='D'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.D']+"</option>");
			dyna.push("	  <option value='E'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.E']+"</option>");
            dyna.push("	  <option value='F'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.F']+"</option>");
            dyna.push("	  <option value='G'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.G']+"</option>");
            dyna.push("	  <option value='H'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.H']+"</option>");
            dyna.push("	  <option value='I'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.I']+"</option>");
            dyna.push("	  <option value='J'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.J']+"</option>");
            dyna.push("	  <option value='K'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.K']+"</option>");
            dyna.push("	  <option value='L'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.L']+"</option>");
            dyna.push("	  <option value='M'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.M']+"</option>");
            dyna.push("	  <option value='N'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.N']+"</option>");
            dyna.push("	  <option value='O'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.O']+"</option>");
            dyna.push("	  <option value='P'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.P']+"</option>");
            dyna.push("	  <option value='Q'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.Q']+"</option>");
            dyna.push("	  <option value='R'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.R']+"</option>");
            dyna.push("	  <option value='S'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.S']+"</option>");
			dyna.push("	  <option value='T'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.T']+"</option>");
			dyna.push("	  <option value='U'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.U']+"</option>");
            dyna.push("	  <option value='X'>"+i18n.cls1220m03['C122M01A.statFlag.applyKindB.X']+"</option>");
			dyna.push("	  </select>");
			dyna.push("	  </td>");
			dyna.push("	</tr>");
			
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));		
		    
		    $("#"+_form).find(".date").filter(function(){
		        return !$(this).prop('readonly');
		    }).datepicker();
		    
		    if(true){
		    	//在 function pageInit(...) 中，會針對 欄位 custId addClass upText
		    	pageInit.call( $("#"+_id) );
		    }
		}
		//clear data
		$("#"+_form).reset();
		$("#"+_form).find("[name=applyTS_beg]").val("2020-04-30");
		$("#"+_form).find("[name=applyTS_end]").val(CommonAPI.getToday());
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: i18n.def.query,
	       width: 450,
           height: 210,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	   $.thickbox.close();
            	   //~~~~
            	   grid.jqGrid("setGridParam", {
	           	    	postData : $.extend({ docStatus: viewstatus}, $("#"+_form).serializeData() ),
	           			search: true			
	           	   }).trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();            	  
               }
           }
		});
	});
    
    function build_submenu(dyna, rdoName, submenu){
		$.each(submenu, function(k, v) { 
			dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>"); 
        });		
	}
    
    $("#filter_historyBtn").click(function(){	
		
		var grid_id= "grid_applyKindB_History";
		if ($("#div_applyKindB_History_form").valid()) {
			$("#"+grid_id).jqGrid("setGridParam", {
				postData : get_param_grid_history('Y'),
				search : true
			}).trigger("reloadGrid");			
		}
	});
    
    function get_param_grid_history(flag){
    	var _form = "div_applyKindB_History_form";
    	return {
			'formAction' : "query_applyKindB_History",
			'custId': $("#"+_form).find("[name=search_custId]").val(),
			'flag':flag
		};
    }
    
    var getLastDateOfTheMonth = function(){
        var tDate = new Date();
        tDate.setMonth(tDate.getMonth() + 1);
        tDate.setDate(1);
        tDate.setDate(tDate.getDate() - 1);
        return tDate.getFullYear() + "-" +
        (tDate.getMonth() < 9 ? "0" : "") +
        (tDate.getMonth() + 1) +
        "-" +
        (tDate.getDate() < 10 ? "0" : "") +
        tDate.getDate();
    }
});

